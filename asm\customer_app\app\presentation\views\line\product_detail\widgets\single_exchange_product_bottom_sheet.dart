// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/single_exchange_product_bottom_sheet.dart

// class id: 1049572, size: 0x8
class :: {
}

// class id: 3213, size: 0x18, field offset: 0x14
class _SingleExchangeProductBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xc0cc84, size: 0x1e9c
    // 0xc0cc84: EnterFrame
    //     0xc0cc84: stp             fp, lr, [SP, #-0x10]!
    //     0xc0cc88: mov             fp, SP
    // 0xc0cc8c: AllocStack(0xb0)
    //     0xc0cc8c: sub             SP, SP, #0xb0
    // 0xc0cc90: SetupParameters(_SingleExchangeProductBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0xc0cc90: stur            x1, [fp, #-8]
    // 0xc0cc94: CheckStackOverflow
    //     0xc0cc94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0cc98: cmp             SP, x16
    //     0xc0cc9c: b.ls            #0xc0ead8
    // 0xc0cca0: r1 = 1
    //     0xc0cca0: movz            x1, #0x1
    // 0xc0cca4: r0 = AllocateContext()
    //     0xc0cca4: bl              #0x16f6108  ; AllocateContextStub
    // 0xc0cca8: mov             x1, x0
    // 0xc0ccac: ldur            x0, [fp, #-8]
    // 0xc0ccb0: stur            x1, [fp, #-0x10]
    // 0xc0ccb4: StoreField: r1->field_f = r0
    //     0xc0ccb4: stur            w0, [x1, #0xf]
    // 0xc0ccb8: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0xc0ccb8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc0ccbc: ldr             x0, [x0, #0x1ab0]
    //     0xc0ccc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc0ccc4: cmp             w0, w16
    //     0xc0ccc8: b.ne            #0xc0ccd8
    //     0xc0cccc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0xc0ccd0: ldr             x2, [x2, #0x60]
    //     0xc0ccd4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc0ccd8: stur            x0, [fp, #-0x28]
    // 0xc0ccdc: LoadField: r2 = r0->field_87
    //     0xc0ccdc: ldur            w2, [x0, #0x87]
    // 0xc0cce0: DecompressPointer r2
    //     0xc0cce0: add             x2, x2, HEAP, lsl #32
    // 0xc0cce4: stur            x2, [fp, #-0x20]
    // 0xc0cce8: LoadField: r3 = r2->field_7
    //     0xc0cce8: ldur            w3, [x2, #7]
    // 0xc0ccec: DecompressPointer r3
    //     0xc0ccec: add             x3, x3, HEAP, lsl #32
    // 0xc0ccf0: stur            x3, [fp, #-0x18]
    // 0xc0ccf4: r16 = Instance_Color
    //     0xc0ccf4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0ccf8: str             x16, [SP]
    // 0xc0ccfc: mov             x1, x3
    // 0xc0cd00: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xc0cd00: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xc0cd04: ldr             x4, [x4, #0xf40]
    // 0xc0cd08: r0 = copyWith()
    //     0xc0cd08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0cd0c: stur            x0, [fp, #-0x30]
    // 0xc0cd10: r0 = Text()
    //     0xc0cd10: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0cd14: mov             x3, x0
    // 0xc0cd18: r0 = "Ready for exchange \?"
    //     0xc0cd18: add             x0, PP, #0x53, lsl #12  ; [pp+0x539a0] "Ready for exchange \?"
    //     0xc0cd1c: ldr             x0, [x0, #0x9a0]
    // 0xc0cd20: stur            x3, [fp, #-0x38]
    // 0xc0cd24: StoreField: r3->field_b = r0
    //     0xc0cd24: stur            w0, [x3, #0xb]
    // 0xc0cd28: ldur            x0, [fp, #-0x30]
    // 0xc0cd2c: StoreField: r3->field_13 = r0
    //     0xc0cd2c: stur            w0, [x3, #0x13]
    // 0xc0cd30: r1 = Function '<anonymous closure>':.
    //     0xc0cd30: add             x1, PP, #0x53, lsl #12  ; [pp+0x539a8] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc0cd34: ldr             x1, [x1, #0x9a8]
    // 0xc0cd38: r2 = Null
    //     0xc0cd38: mov             x2, NULL
    // 0xc0cd3c: r0 = AllocateClosure()
    //     0xc0cd3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0cd40: stur            x0, [fp, #-0x30]
    // 0xc0cd44: r0 = IconButton()
    //     0xc0cd44: bl              #0x9881fc  ; AllocateIconButtonStub -> IconButton (size=0x70)
    // 0xc0cd48: mov             x3, x0
    // 0xc0cd4c: ldur            x0, [fp, #-0x30]
    // 0xc0cd50: stur            x3, [fp, #-0x40]
    // 0xc0cd54: StoreField: r3->field_3b = r0
    //     0xc0cd54: stur            w0, [x3, #0x3b]
    // 0xc0cd58: r0 = false
    //     0xc0cd58: add             x0, NULL, #0x30  ; false
    // 0xc0cd5c: StoreField: r3->field_4f = r0
    //     0xc0cd5c: stur            w0, [x3, #0x4f]
    // 0xc0cd60: r1 = Instance_Icon
    //     0xc0cd60: add             x1, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xc0cd64: ldr             x1, [x1, #0x2b8]
    // 0xc0cd68: StoreField: r3->field_1f = r1
    //     0xc0cd68: stur            w1, [x3, #0x1f]
    // 0xc0cd6c: r1 = Instance__IconButtonVariant
    //     0xc0cd6c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52900] Obj!_IconButtonVariant@d745e1
    //     0xc0cd70: ldr             x1, [x1, #0x900]
    // 0xc0cd74: StoreField: r3->field_6b = r1
    //     0xc0cd74: stur            w1, [x3, #0x6b]
    // 0xc0cd78: r1 = Null
    //     0xc0cd78: mov             x1, NULL
    // 0xc0cd7c: r2 = 4
    //     0xc0cd7c: movz            x2, #0x4
    // 0xc0cd80: r0 = AllocateArray()
    //     0xc0cd80: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0cd84: mov             x2, x0
    // 0xc0cd88: ldur            x0, [fp, #-0x38]
    // 0xc0cd8c: stur            x2, [fp, #-0x30]
    // 0xc0cd90: StoreField: r2->field_f = r0
    //     0xc0cd90: stur            w0, [x2, #0xf]
    // 0xc0cd94: ldur            x0, [fp, #-0x40]
    // 0xc0cd98: StoreField: r2->field_13 = r0
    //     0xc0cd98: stur            w0, [x2, #0x13]
    // 0xc0cd9c: r1 = <Widget>
    //     0xc0cd9c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0cda0: r0 = AllocateGrowableArray()
    //     0xc0cda0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0cda4: mov             x1, x0
    // 0xc0cda8: ldur            x0, [fp, #-0x30]
    // 0xc0cdac: stur            x1, [fp, #-0x38]
    // 0xc0cdb0: StoreField: r1->field_f = r0
    //     0xc0cdb0: stur            w0, [x1, #0xf]
    // 0xc0cdb4: r2 = 4
    //     0xc0cdb4: movz            x2, #0x4
    // 0xc0cdb8: StoreField: r1->field_b = r2
    //     0xc0cdb8: stur            w2, [x1, #0xb]
    // 0xc0cdbc: r0 = Row()
    //     0xc0cdbc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc0cdc0: mov             x2, x0
    // 0xc0cdc4: r0 = Instance_Axis
    //     0xc0cdc4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc0cdc8: stur            x2, [fp, #-0x30]
    // 0xc0cdcc: StoreField: r2->field_f = r0
    //     0xc0cdcc: stur            w0, [x2, #0xf]
    // 0xc0cdd0: r3 = Instance_MainAxisAlignment
    //     0xc0cdd0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xc0cdd4: ldr             x3, [x3, #0xa8]
    // 0xc0cdd8: StoreField: r2->field_13 = r3
    //     0xc0cdd8: stur            w3, [x2, #0x13]
    // 0xc0cddc: r4 = Instance_MainAxisSize
    //     0xc0cddc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0cde0: ldr             x4, [x4, #0xa10]
    // 0xc0cde4: ArrayStore: r2[0] = r4  ; List_4
    //     0xc0cde4: stur            w4, [x2, #0x17]
    // 0xc0cde8: r5 = Instance_CrossAxisAlignment
    //     0xc0cde8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc0cdec: ldr             x5, [x5, #0xa18]
    // 0xc0cdf0: StoreField: r2->field_1b = r5
    //     0xc0cdf0: stur            w5, [x2, #0x1b]
    // 0xc0cdf4: r6 = Instance_VerticalDirection
    //     0xc0cdf4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0cdf8: ldr             x6, [x6, #0xa20]
    // 0xc0cdfc: StoreField: r2->field_23 = r6
    //     0xc0cdfc: stur            w6, [x2, #0x23]
    // 0xc0ce00: r7 = Instance_Clip
    //     0xc0ce00: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0ce04: ldr             x7, [x7, #0x38]
    // 0xc0ce08: StoreField: r2->field_2b = r7
    //     0xc0ce08: stur            w7, [x2, #0x2b]
    // 0xc0ce0c: StoreField: r2->field_2f = rZR
    //     0xc0ce0c: stur            xzr, [x2, #0x2f]
    // 0xc0ce10: ldur            x1, [fp, #-0x38]
    // 0xc0ce14: StoreField: r2->field_b = r1
    //     0xc0ce14: stur            w1, [x2, #0xb]
    // 0xc0ce18: r1 = Instance_Color
    //     0xc0ce18: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0ce1c: d0 = 0.100000
    //     0xc0ce1c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc0ce20: r0 = withOpacity()
    //     0xc0ce20: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc0ce24: mov             x2, x0
    // 0xc0ce28: r1 = Null
    //     0xc0ce28: mov             x1, NULL
    // 0xc0ce2c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc0ce2c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc0ce30: r0 = Border.all()
    //     0xc0ce30: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xc0ce34: stur            x0, [fp, #-0x38]
    // 0xc0ce38: r0 = BoxDecoration()
    //     0xc0ce38: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc0ce3c: mov             x1, x0
    // 0xc0ce40: ldur            x0, [fp, #-0x38]
    // 0xc0ce44: stur            x1, [fp, #-0x40]
    // 0xc0ce48: StoreField: r1->field_f = r0
    //     0xc0ce48: stur            w0, [x1, #0xf]
    // 0xc0ce4c: r0 = Instance_BoxShape
    //     0xc0ce4c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0ce50: ldr             x0, [x0, #0x80]
    // 0xc0ce54: StoreField: r1->field_23 = r0
    //     0xc0ce54: stur            w0, [x1, #0x23]
    // 0xc0ce58: ldur            x2, [fp, #-8]
    // 0xc0ce5c: LoadField: r3 = r2->field_b
    //     0xc0ce5c: ldur            w3, [x2, #0xb]
    // 0xc0ce60: DecompressPointer r3
    //     0xc0ce60: add             x3, x3, HEAP, lsl #32
    // 0xc0ce64: cmp             w3, NULL
    // 0xc0ce68: b.eq            #0xc0eae0
    // 0xc0ce6c: LoadField: r4 = r3->field_2f
    //     0xc0ce6c: ldur            w4, [x3, #0x2f]
    // 0xc0ce70: DecompressPointer r4
    //     0xc0ce70: add             x4, x4, HEAP, lsl #32
    // 0xc0ce74: LoadField: r3 = r4->field_3f
    //     0xc0ce74: ldur            w3, [x4, #0x3f]
    // 0xc0ce78: DecompressPointer r3
    //     0xc0ce78: add             x3, x3, HEAP, lsl #32
    // 0xc0ce7c: cmp             w3, NULL
    // 0xc0ce80: b.ne            #0xc0ce8c
    // 0xc0ce84: r4 = Null
    //     0xc0ce84: mov             x4, NULL
    // 0xc0ce88: b               #0xc0ceb0
    // 0xc0ce8c: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xc0ce8c: ldur            w4, [x3, #0x17]
    // 0xc0ce90: DecompressPointer r4
    //     0xc0ce90: add             x4, x4, HEAP, lsl #32
    // 0xc0ce94: cmp             w4, NULL
    // 0xc0ce98: b.ne            #0xc0cea4
    // 0xc0ce9c: r4 = Null
    //     0xc0ce9c: mov             x4, NULL
    // 0xc0cea0: b               #0xc0ceb0
    // 0xc0cea4: LoadField: r5 = r4->field_7
    //     0xc0cea4: ldur            w5, [x4, #7]
    // 0xc0cea8: DecompressPointer r5
    //     0xc0cea8: add             x5, x5, HEAP, lsl #32
    // 0xc0ceac: mov             x4, x5
    // 0xc0ceb0: cmp             w4, NULL
    // 0xc0ceb4: b.ne            #0xc0cec0
    // 0xc0ceb8: r4 = 0
    //     0xc0ceb8: movz            x4, #0
    // 0xc0cebc: b               #0xc0ced0
    // 0xc0cec0: r5 = LoadInt32Instr(r4)
    //     0xc0cec0: sbfx            x5, x4, #1, #0x1f
    //     0xc0cec4: tbz             w4, #0, #0xc0cecc
    //     0xc0cec8: ldur            x5, [x4, #7]
    // 0xc0cecc: mov             x4, x5
    // 0xc0ced0: stur            x4, [fp, #-0x58]
    // 0xc0ced4: cmp             w3, NULL
    // 0xc0ced8: b.ne            #0xc0cee4
    // 0xc0cedc: r5 = Null
    //     0xc0cedc: mov             x5, NULL
    // 0xc0cee0: b               #0xc0cf08
    // 0xc0cee4: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xc0cee4: ldur            w5, [x3, #0x17]
    // 0xc0cee8: DecompressPointer r5
    //     0xc0cee8: add             x5, x5, HEAP, lsl #32
    // 0xc0ceec: cmp             w5, NULL
    // 0xc0cef0: b.ne            #0xc0cefc
    // 0xc0cef4: r5 = Null
    //     0xc0cef4: mov             x5, NULL
    // 0xc0cef8: b               #0xc0cf08
    // 0xc0cefc: LoadField: r6 = r5->field_b
    //     0xc0cefc: ldur            w6, [x5, #0xb]
    // 0xc0cf00: DecompressPointer r6
    //     0xc0cf00: add             x6, x6, HEAP, lsl #32
    // 0xc0cf04: mov             x5, x6
    // 0xc0cf08: cmp             w5, NULL
    // 0xc0cf0c: b.ne            #0xc0cf18
    // 0xc0cf10: r5 = 0
    //     0xc0cf10: movz            x5, #0
    // 0xc0cf14: b               #0xc0cf28
    // 0xc0cf18: r6 = LoadInt32Instr(r5)
    //     0xc0cf18: sbfx            x6, x5, #1, #0x1f
    //     0xc0cf1c: tbz             w5, #0, #0xc0cf24
    //     0xc0cf20: ldur            x6, [x5, #7]
    // 0xc0cf24: mov             x5, x6
    // 0xc0cf28: stur            x5, [fp, #-0x50]
    // 0xc0cf2c: cmp             w3, NULL
    // 0xc0cf30: b.ne            #0xc0cf3c
    // 0xc0cf34: r3 = Null
    //     0xc0cf34: mov             x3, NULL
    // 0xc0cf38: b               #0xc0cf5c
    // 0xc0cf3c: ArrayLoad: r6 = r3[0]  ; List_4
    //     0xc0cf3c: ldur            w6, [x3, #0x17]
    // 0xc0cf40: DecompressPointer r6
    //     0xc0cf40: add             x6, x6, HEAP, lsl #32
    // 0xc0cf44: cmp             w6, NULL
    // 0xc0cf48: b.ne            #0xc0cf54
    // 0xc0cf4c: r3 = Null
    //     0xc0cf4c: mov             x3, NULL
    // 0xc0cf50: b               #0xc0cf5c
    // 0xc0cf54: LoadField: r3 = r6->field_f
    //     0xc0cf54: ldur            w3, [x6, #0xf]
    // 0xc0cf58: DecompressPointer r3
    //     0xc0cf58: add             x3, x3, HEAP, lsl #32
    // 0xc0cf5c: cmp             w3, NULL
    // 0xc0cf60: b.ne            #0xc0cf6c
    // 0xc0cf64: r3 = 0
    //     0xc0cf64: movz            x3, #0
    // 0xc0cf68: b               #0xc0cf7c
    // 0xc0cf6c: r6 = LoadInt32Instr(r3)
    //     0xc0cf6c: sbfx            x6, x3, #1, #0x1f
    //     0xc0cf70: tbz             w3, #0, #0xc0cf78
    //     0xc0cf74: ldur            x6, [x3, #7]
    // 0xc0cf78: mov             x3, x6
    // 0xc0cf7c: stur            x3, [fp, #-0x48]
    // 0xc0cf80: r0 = Color()
    //     0xc0cf80: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xc0cf84: mov             x1, x0
    // 0xc0cf88: r0 = Instance_ColorSpace
    //     0xc0cf88: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xc0cf8c: stur            x1, [fp, #-0x38]
    // 0xc0cf90: StoreField: r1->field_27 = r0
    //     0xc0cf90: stur            w0, [x1, #0x27]
    // 0xc0cf94: d0 = 1.000000
    //     0xc0cf94: fmov            d0, #1.00000000
    // 0xc0cf98: StoreField: r1->field_7 = d0
    //     0xc0cf98: stur            d0, [x1, #7]
    // 0xc0cf9c: ldur            x2, [fp, #-0x58]
    // 0xc0cfa0: ubfx            x2, x2, #0, #0x20
    // 0xc0cfa4: and             w3, w2, #0xff
    // 0xc0cfa8: ubfx            x3, x3, #0, #0x20
    // 0xc0cfac: scvtf           d1, x3
    // 0xc0cfb0: d2 = 255.000000
    //     0xc0cfb0: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xc0cfb4: fdiv            d3, d1, d2
    // 0xc0cfb8: StoreField: r1->field_f = d3
    //     0xc0cfb8: stur            d3, [x1, #0xf]
    // 0xc0cfbc: ldur            x2, [fp, #-0x50]
    // 0xc0cfc0: ubfx            x2, x2, #0, #0x20
    // 0xc0cfc4: and             w3, w2, #0xff
    // 0xc0cfc8: ubfx            x3, x3, #0, #0x20
    // 0xc0cfcc: scvtf           d1, x3
    // 0xc0cfd0: fdiv            d3, d1, d2
    // 0xc0cfd4: ArrayStore: r1[0] = d3  ; List_8
    //     0xc0cfd4: stur            d3, [x1, #0x17]
    // 0xc0cfd8: ldur            x2, [fp, #-0x48]
    // 0xc0cfdc: ubfx            x2, x2, #0, #0x20
    // 0xc0cfe0: and             w3, w2, #0xff
    // 0xc0cfe4: ubfx            x3, x3, #0, #0x20
    // 0xc0cfe8: scvtf           d1, x3
    // 0xc0cfec: fdiv            d3, d1, d2
    // 0xc0cff0: StoreField: r1->field_1f = d3
    //     0xc0cff0: stur            d3, [x1, #0x1f]
    // 0xc0cff4: r0 = ImageHeaders.forImages()
    //     0xc0cff4: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xc0cff8: mov             x3, x0
    // 0xc0cffc: ldur            x0, [fp, #-8]
    // 0xc0d000: stur            x3, [fp, #-0x68]
    // 0xc0d004: LoadField: r1 = r0->field_b
    //     0xc0d004: ldur            w1, [x0, #0xb]
    // 0xc0d008: DecompressPointer r1
    //     0xc0d008: add             x1, x1, HEAP, lsl #32
    // 0xc0d00c: cmp             w1, NULL
    // 0xc0d010: b.eq            #0xc0eae4
    // 0xc0d014: LoadField: r2 = r1->field_b
    //     0xc0d014: ldur            w2, [x1, #0xb]
    // 0xc0d018: DecompressPointer r2
    //     0xc0d018: add             x2, x2, HEAP, lsl #32
    // 0xc0d01c: cmp             w2, NULL
    // 0xc0d020: b.ne            #0xc0d02c
    // 0xc0d024: r1 = Null
    //     0xc0d024: mov             x1, NULL
    // 0xc0d028: b               #0xc0d064
    // 0xc0d02c: LoadField: r1 = r2->field_b
    //     0xc0d02c: ldur            w1, [x2, #0xb]
    // 0xc0d030: DecompressPointer r1
    //     0xc0d030: add             x1, x1, HEAP, lsl #32
    // 0xc0d034: cmp             w1, NULL
    // 0xc0d038: b.ne            #0xc0d044
    // 0xc0d03c: r1 = Null
    //     0xc0d03c: mov             x1, NULL
    // 0xc0d040: b               #0xc0d064
    // 0xc0d044: LoadField: r2 = r1->field_7
    //     0xc0d044: ldur            w2, [x1, #7]
    // 0xc0d048: DecompressPointer r2
    //     0xc0d048: add             x2, x2, HEAP, lsl #32
    // 0xc0d04c: cmp             w2, NULL
    // 0xc0d050: b.ne            #0xc0d05c
    // 0xc0d054: r1 = Null
    //     0xc0d054: mov             x1, NULL
    // 0xc0d058: b               #0xc0d064
    // 0xc0d05c: LoadField: r1 = r2->field_7
    //     0xc0d05c: ldur            w1, [x2, #7]
    // 0xc0d060: DecompressPointer r1
    //     0xc0d060: add             x1, x1, HEAP, lsl #32
    // 0xc0d064: cmp             w1, NULL
    // 0xc0d068: b.ne            #0xc0d074
    // 0xc0d06c: r4 = ""
    //     0xc0d06c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0d070: b               #0xc0d078
    // 0xc0d074: mov             x4, x1
    // 0xc0d078: stur            x4, [fp, #-0x60]
    // 0xc0d07c: r1 = Function '<anonymous closure>':.
    //     0xc0d07c: add             x1, PP, #0x53, lsl #12  ; [pp+0x539b0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc0d080: ldr             x1, [x1, #0x9b0]
    // 0xc0d084: r2 = Null
    //     0xc0d084: mov             x2, NULL
    // 0xc0d088: r0 = AllocateClosure()
    //     0xc0d088: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0d08c: stur            x0, [fp, #-0x70]
    // 0xc0d090: r0 = CachedNetworkImage()
    //     0xc0d090: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xc0d094: stur            x0, [fp, #-0x78]
    // 0xc0d098: r16 = Instance_BoxFit
    //     0xc0d098: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xc0d09c: ldr             x16, [x16, #0x118]
    // 0xc0d0a0: ldur            lr, [fp, #-0x68]
    // 0xc0d0a4: stp             lr, x16, [SP, #0x18]
    // 0xc0d0a8: r16 = 84.000000
    //     0xc0d0a8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0xc0d0ac: ldr             x16, [x16, #0xf90]
    // 0xc0d0b0: r30 = 55.000000
    //     0xc0d0b0: add             lr, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xc0d0b4: ldr             lr, [lr, #0x9b8]
    // 0xc0d0b8: stp             lr, x16, [SP, #8]
    // 0xc0d0bc: ldur            x16, [fp, #-0x70]
    // 0xc0d0c0: str             x16, [SP]
    // 0xc0d0c4: mov             x1, x0
    // 0xc0d0c8: ldur            x2, [fp, #-0x60]
    // 0xc0d0cc: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x2, height, 0x4, httpHeaders, 0x3, width, 0x5, null]
    //     0xc0d0cc: add             x4, PP, #0x38, lsl #12  ; [pp+0x38d78] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x2, "height", 0x4, "httpHeaders", 0x3, "width", 0x5, Null]
    //     0xc0d0d0: ldr             x4, [x4, #0xd78]
    // 0xc0d0d4: r0 = CachedNetworkImage()
    //     0xc0d0d4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xc0d0d8: ldur            x0, [fp, #-8]
    // 0xc0d0dc: LoadField: r1 = r0->field_b
    //     0xc0d0dc: ldur            w1, [x0, #0xb]
    // 0xc0d0e0: DecompressPointer r1
    //     0xc0d0e0: add             x1, x1, HEAP, lsl #32
    // 0xc0d0e4: cmp             w1, NULL
    // 0xc0d0e8: b.eq            #0xc0eae8
    // 0xc0d0ec: LoadField: r2 = r1->field_b
    //     0xc0d0ec: ldur            w2, [x1, #0xb]
    // 0xc0d0f0: DecompressPointer r2
    //     0xc0d0f0: add             x2, x2, HEAP, lsl #32
    // 0xc0d0f4: cmp             w2, NULL
    // 0xc0d0f8: b.ne            #0xc0d104
    // 0xc0d0fc: r1 = Null
    //     0xc0d0fc: mov             x1, NULL
    // 0xc0d100: b               #0xc0d13c
    // 0xc0d104: LoadField: r1 = r2->field_b
    //     0xc0d104: ldur            w1, [x2, #0xb]
    // 0xc0d108: DecompressPointer r1
    //     0xc0d108: add             x1, x1, HEAP, lsl #32
    // 0xc0d10c: cmp             w1, NULL
    // 0xc0d110: b.ne            #0xc0d11c
    // 0xc0d114: r1 = Null
    //     0xc0d114: mov             x1, NULL
    // 0xc0d118: b               #0xc0d13c
    // 0xc0d11c: LoadField: r2 = r1->field_7
    //     0xc0d11c: ldur            w2, [x1, #7]
    // 0xc0d120: DecompressPointer r2
    //     0xc0d120: add             x2, x2, HEAP, lsl #32
    // 0xc0d124: cmp             w2, NULL
    // 0xc0d128: b.ne            #0xc0d134
    // 0xc0d12c: r1 = Null
    //     0xc0d12c: mov             x1, NULL
    // 0xc0d130: b               #0xc0d13c
    // 0xc0d134: LoadField: r1 = r2->field_b
    //     0xc0d134: ldur            w1, [x2, #0xb]
    // 0xc0d138: DecompressPointer r1
    //     0xc0d138: add             x1, x1, HEAP, lsl #32
    // 0xc0d13c: cmp             w1, NULL
    // 0xc0d140: b.ne            #0xc0d14c
    // 0xc0d144: r2 = ""
    //     0xc0d144: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0d148: b               #0xc0d150
    // 0xc0d14c: mov             x2, x1
    // 0xc0d150: stur            x2, [fp, #-0x60]
    // 0xc0d154: r16 = 12.000000
    //     0xc0d154: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0d158: ldr             x16, [x16, #0x9e8]
    // 0xc0d15c: r30 = Instance_Color
    //     0xc0d15c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc0d160: stp             lr, x16, [SP]
    // 0xc0d164: ldur            x1, [fp, #-0x18]
    // 0xc0d168: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc0d168: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc0d16c: ldr             x4, [x4, #0xaa0]
    // 0xc0d170: r0 = copyWith()
    //     0xc0d170: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0d174: stur            x0, [fp, #-0x68]
    // 0xc0d178: r0 = Text()
    //     0xc0d178: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0d17c: mov             x3, x0
    // 0xc0d180: ldur            x0, [fp, #-0x60]
    // 0xc0d184: stur            x3, [fp, #-0x70]
    // 0xc0d188: StoreField: r3->field_b = r0
    //     0xc0d188: stur            w0, [x3, #0xb]
    // 0xc0d18c: ldur            x0, [fp, #-0x68]
    // 0xc0d190: StoreField: r3->field_13 = r0
    //     0xc0d190: stur            w0, [x3, #0x13]
    // 0xc0d194: r0 = Instance_TextOverflow
    //     0xc0d194: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xc0d198: ldr             x0, [x0, #0xe10]
    // 0xc0d19c: StoreField: r3->field_2b = r0
    //     0xc0d19c: stur            w0, [x3, #0x2b]
    // 0xc0d1a0: r4 = 2
    //     0xc0d1a0: movz            x4, #0x2
    // 0xc0d1a4: StoreField: r3->field_37 = r4
    //     0xc0d1a4: stur            w4, [x3, #0x37]
    // 0xc0d1a8: r1 = Null
    //     0xc0d1a8: mov             x1, NULL
    // 0xc0d1ac: r2 = 8
    //     0xc0d1ac: movz            x2, #0x8
    // 0xc0d1b0: r0 = AllocateArray()
    //     0xc0d1b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0d1b4: r16 = "Size: "
    //     0xc0d1b4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xc0d1b8: ldr             x16, [x16, #0xf00]
    // 0xc0d1bc: StoreField: r0->field_f = r16
    //     0xc0d1bc: stur            w16, [x0, #0xf]
    // 0xc0d1c0: ldur            x1, [fp, #-8]
    // 0xc0d1c4: LoadField: r2 = r1->field_b
    //     0xc0d1c4: ldur            w2, [x1, #0xb]
    // 0xc0d1c8: DecompressPointer r2
    //     0xc0d1c8: add             x2, x2, HEAP, lsl #32
    // 0xc0d1cc: cmp             w2, NULL
    // 0xc0d1d0: b.eq            #0xc0eaec
    // 0xc0d1d4: LoadField: r3 = r2->field_b
    //     0xc0d1d4: ldur            w3, [x2, #0xb]
    // 0xc0d1d8: DecompressPointer r3
    //     0xc0d1d8: add             x3, x3, HEAP, lsl #32
    // 0xc0d1dc: cmp             w3, NULL
    // 0xc0d1e0: b.ne            #0xc0d1ec
    // 0xc0d1e4: r2 = Null
    //     0xc0d1e4: mov             x2, NULL
    // 0xc0d1e8: b               #0xc0d224
    // 0xc0d1ec: LoadField: r2 = r3->field_b
    //     0xc0d1ec: ldur            w2, [x3, #0xb]
    // 0xc0d1f0: DecompressPointer r2
    //     0xc0d1f0: add             x2, x2, HEAP, lsl #32
    // 0xc0d1f4: cmp             w2, NULL
    // 0xc0d1f8: b.ne            #0xc0d204
    // 0xc0d1fc: r2 = Null
    //     0xc0d1fc: mov             x2, NULL
    // 0xc0d200: b               #0xc0d224
    // 0xc0d204: LoadField: r4 = r2->field_7
    //     0xc0d204: ldur            w4, [x2, #7]
    // 0xc0d208: DecompressPointer r4
    //     0xc0d208: add             x4, x4, HEAP, lsl #32
    // 0xc0d20c: cmp             w4, NULL
    // 0xc0d210: b.ne            #0xc0d21c
    // 0xc0d214: r2 = Null
    //     0xc0d214: mov             x2, NULL
    // 0xc0d218: b               #0xc0d224
    // 0xc0d21c: LoadField: r2 = r4->field_f
    //     0xc0d21c: ldur            w2, [x4, #0xf]
    // 0xc0d220: DecompressPointer r2
    //     0xc0d220: add             x2, x2, HEAP, lsl #32
    // 0xc0d224: cmp             w2, NULL
    // 0xc0d228: b.ne            #0xc0d230
    // 0xc0d22c: r2 = ""
    //     0xc0d22c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0d230: StoreField: r0->field_13 = r2
    //     0xc0d230: stur            w2, [x0, #0x13]
    // 0xc0d234: r16 = " / Qty: "
    //     0xc0d234: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xc0d238: ldr             x16, [x16, #0x760]
    // 0xc0d23c: ArrayStore: r0[0] = r16  ; List_4
    //     0xc0d23c: stur            w16, [x0, #0x17]
    // 0xc0d240: cmp             w3, NULL
    // 0xc0d244: b.ne            #0xc0d250
    // 0xc0d248: r2 = Null
    //     0xc0d248: mov             x2, NULL
    // 0xc0d24c: b               #0xc0d288
    // 0xc0d250: LoadField: r2 = r3->field_b
    //     0xc0d250: ldur            w2, [x3, #0xb]
    // 0xc0d254: DecompressPointer r2
    //     0xc0d254: add             x2, x2, HEAP, lsl #32
    // 0xc0d258: cmp             w2, NULL
    // 0xc0d25c: b.ne            #0xc0d268
    // 0xc0d260: r2 = Null
    //     0xc0d260: mov             x2, NULL
    // 0xc0d264: b               #0xc0d288
    // 0xc0d268: LoadField: r3 = r2->field_7
    //     0xc0d268: ldur            w3, [x2, #7]
    // 0xc0d26c: DecompressPointer r3
    //     0xc0d26c: add             x3, x3, HEAP, lsl #32
    // 0xc0d270: cmp             w3, NULL
    // 0xc0d274: b.ne            #0xc0d280
    // 0xc0d278: r2 = Null
    //     0xc0d278: mov             x2, NULL
    // 0xc0d27c: b               #0xc0d288
    // 0xc0d280: LoadField: r2 = r3->field_13
    //     0xc0d280: ldur            w2, [x3, #0x13]
    // 0xc0d284: DecompressPointer r2
    //     0xc0d284: add             x2, x2, HEAP, lsl #32
    // 0xc0d288: cmp             w2, NULL
    // 0xc0d28c: b.ne            #0xc0d298
    // 0xc0d290: r3 = ""
    //     0xc0d290: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0d294: b               #0xc0d29c
    // 0xc0d298: mov             x3, x2
    // 0xc0d29c: ldur            x2, [fp, #-0x20]
    // 0xc0d2a0: StoreField: r0->field_1b = r3
    //     0xc0d2a0: stur            w3, [x0, #0x1b]
    // 0xc0d2a4: str             x0, [SP]
    // 0xc0d2a8: r0 = _interpolate()
    //     0xc0d2a8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xc0d2ac: mov             x2, x0
    // 0xc0d2b0: ldur            x0, [fp, #-0x20]
    // 0xc0d2b4: stur            x2, [fp, #-0x68]
    // 0xc0d2b8: LoadField: r3 = r0->field_2b
    //     0xc0d2b8: ldur            w3, [x0, #0x2b]
    // 0xc0d2bc: DecompressPointer r3
    //     0xc0d2bc: add             x3, x3, HEAP, lsl #32
    // 0xc0d2c0: stur            x3, [fp, #-0x60]
    // 0xc0d2c4: r16 = Instance_Color
    //     0xc0d2c4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc0d2c8: r30 = 12.000000
    //     0xc0d2c8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0d2cc: ldr             lr, [lr, #0x9e8]
    // 0xc0d2d0: stp             lr, x16, [SP]
    // 0xc0d2d4: mov             x1, x3
    // 0xc0d2d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc0d2d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc0d2dc: ldr             x4, [x4, #0x9b8]
    // 0xc0d2e0: r0 = copyWith()
    //     0xc0d2e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0d2e4: stur            x0, [fp, #-0x20]
    // 0xc0d2e8: r0 = Text()
    //     0xc0d2e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0d2ec: mov             x2, x0
    // 0xc0d2f0: ldur            x0, [fp, #-0x68]
    // 0xc0d2f4: stur            x2, [fp, #-0x80]
    // 0xc0d2f8: StoreField: r2->field_b = r0
    //     0xc0d2f8: stur            w0, [x2, #0xb]
    // 0xc0d2fc: ldur            x0, [fp, #-0x20]
    // 0xc0d300: StoreField: r2->field_13 = r0
    //     0xc0d300: stur            w0, [x2, #0x13]
    // 0xc0d304: ldur            x0, [fp, #-8]
    // 0xc0d308: LoadField: r1 = r0->field_b
    //     0xc0d308: ldur            w1, [x0, #0xb]
    // 0xc0d30c: DecompressPointer r1
    //     0xc0d30c: add             x1, x1, HEAP, lsl #32
    // 0xc0d310: cmp             w1, NULL
    // 0xc0d314: b.eq            #0xc0eaf0
    // 0xc0d318: LoadField: r3 = r1->field_b
    //     0xc0d318: ldur            w3, [x1, #0xb]
    // 0xc0d31c: DecompressPointer r3
    //     0xc0d31c: add             x3, x3, HEAP, lsl #32
    // 0xc0d320: cmp             w3, NULL
    // 0xc0d324: b.ne            #0xc0d330
    // 0xc0d328: r1 = Null
    //     0xc0d328: mov             x1, NULL
    // 0xc0d32c: b               #0xc0d384
    // 0xc0d330: LoadField: r1 = r3->field_b
    //     0xc0d330: ldur            w1, [x3, #0xb]
    // 0xc0d334: DecompressPointer r1
    //     0xc0d334: add             x1, x1, HEAP, lsl #32
    // 0xc0d338: cmp             w1, NULL
    // 0xc0d33c: b.ne            #0xc0d348
    // 0xc0d340: r1 = Null
    //     0xc0d340: mov             x1, NULL
    // 0xc0d344: b               #0xc0d384
    // 0xc0d348: LoadField: r3 = r1->field_7
    //     0xc0d348: ldur            w3, [x1, #7]
    // 0xc0d34c: DecompressPointer r3
    //     0xc0d34c: add             x3, x3, HEAP, lsl #32
    // 0xc0d350: cmp             w3, NULL
    // 0xc0d354: b.ne            #0xc0d360
    // 0xc0d358: r1 = Null
    //     0xc0d358: mov             x1, NULL
    // 0xc0d35c: b               #0xc0d384
    // 0xc0d360: LoadField: r1 = r3->field_1b
    //     0xc0d360: ldur            w1, [x3, #0x1b]
    // 0xc0d364: DecompressPointer r1
    //     0xc0d364: add             x1, x1, HEAP, lsl #32
    // 0xc0d368: cmp             w1, NULL
    // 0xc0d36c: b.ne            #0xc0d378
    // 0xc0d370: r1 = Null
    //     0xc0d370: mov             x1, NULL
    // 0xc0d374: b               #0xc0d384
    // 0xc0d378: LoadField: r3 = r1->field_7
    //     0xc0d378: ldur            w3, [x1, #7]
    // 0xc0d37c: DecompressPointer r3
    //     0xc0d37c: add             x3, x3, HEAP, lsl #32
    // 0xc0d380: mov             x1, x3
    // 0xc0d384: cmp             w1, NULL
    // 0xc0d388: b.ne            #0xc0d394
    // 0xc0d38c: r6 = ""
    //     0xc0d38c: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0d390: b               #0xc0d398
    // 0xc0d394: mov             x6, x1
    // 0xc0d398: ldur            x5, [fp, #-0x38]
    // 0xc0d39c: ldur            x4, [fp, #-0x78]
    // 0xc0d3a0: ldur            x3, [fp, #-0x70]
    // 0xc0d3a4: stur            x6, [fp, #-0x20]
    // 0xc0d3a8: r16 = 12.000000
    //     0xc0d3a8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0d3ac: ldr             x16, [x16, #0x9e8]
    // 0xc0d3b0: r30 = Instance_Color
    //     0xc0d3b0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc0d3b4: stp             lr, x16, [SP]
    // 0xc0d3b8: ldur            x1, [fp, #-0x18]
    // 0xc0d3bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc0d3bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc0d3c0: ldr             x4, [x4, #0xaa0]
    // 0xc0d3c4: r0 = copyWith()
    //     0xc0d3c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0d3c8: stur            x0, [fp, #-0x68]
    // 0xc0d3cc: r0 = Text()
    //     0xc0d3cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0d3d0: mov             x3, x0
    // 0xc0d3d4: ldur            x0, [fp, #-0x20]
    // 0xc0d3d8: stur            x3, [fp, #-0x88]
    // 0xc0d3dc: StoreField: r3->field_b = r0
    //     0xc0d3dc: stur            w0, [x3, #0xb]
    // 0xc0d3e0: ldur            x0, [fp, #-0x68]
    // 0xc0d3e4: StoreField: r3->field_13 = r0
    //     0xc0d3e4: stur            w0, [x3, #0x13]
    // 0xc0d3e8: r1 = Null
    //     0xc0d3e8: mov             x1, NULL
    // 0xc0d3ec: r2 = 6
    //     0xc0d3ec: movz            x2, #0x6
    // 0xc0d3f0: r0 = AllocateArray()
    //     0xc0d3f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0d3f4: mov             x2, x0
    // 0xc0d3f8: ldur            x0, [fp, #-0x70]
    // 0xc0d3fc: stur            x2, [fp, #-0x20]
    // 0xc0d400: StoreField: r2->field_f = r0
    //     0xc0d400: stur            w0, [x2, #0xf]
    // 0xc0d404: ldur            x0, [fp, #-0x80]
    // 0xc0d408: StoreField: r2->field_13 = r0
    //     0xc0d408: stur            w0, [x2, #0x13]
    // 0xc0d40c: ldur            x0, [fp, #-0x88]
    // 0xc0d410: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0d410: stur            w0, [x2, #0x17]
    // 0xc0d414: r1 = <Widget>
    //     0xc0d414: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0d418: r0 = AllocateGrowableArray()
    //     0xc0d418: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0d41c: mov             x1, x0
    // 0xc0d420: ldur            x0, [fp, #-0x20]
    // 0xc0d424: stur            x1, [fp, #-0x68]
    // 0xc0d428: StoreField: r1->field_f = r0
    //     0xc0d428: stur            w0, [x1, #0xf]
    // 0xc0d42c: r2 = 6
    //     0xc0d42c: movz            x2, #0x6
    // 0xc0d430: StoreField: r1->field_b = r2
    //     0xc0d430: stur            w2, [x1, #0xb]
    // 0xc0d434: r0 = Column()
    //     0xc0d434: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0d438: mov             x2, x0
    // 0xc0d43c: r0 = Instance_Axis
    //     0xc0d43c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0d440: stur            x2, [fp, #-0x20]
    // 0xc0d444: StoreField: r2->field_f = r0
    //     0xc0d444: stur            w0, [x2, #0xf]
    // 0xc0d448: r3 = Instance_MainAxisAlignment
    //     0xc0d448: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0d44c: ldr             x3, [x3, #0xa08]
    // 0xc0d450: StoreField: r2->field_13 = r3
    //     0xc0d450: stur            w3, [x2, #0x13]
    // 0xc0d454: r4 = Instance_MainAxisSize
    //     0xc0d454: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0d458: ldr             x4, [x4, #0xa10]
    // 0xc0d45c: ArrayStore: r2[0] = r4  ; List_4
    //     0xc0d45c: stur            w4, [x2, #0x17]
    // 0xc0d460: r5 = Instance_CrossAxisAlignment
    //     0xc0d460: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc0d464: ldr             x5, [x5, #0x890]
    // 0xc0d468: StoreField: r2->field_1b = r5
    //     0xc0d468: stur            w5, [x2, #0x1b]
    // 0xc0d46c: r6 = Instance_VerticalDirection
    //     0xc0d46c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0d470: ldr             x6, [x6, #0xa20]
    // 0xc0d474: StoreField: r2->field_23 = r6
    //     0xc0d474: stur            w6, [x2, #0x23]
    // 0xc0d478: r7 = Instance_Clip
    //     0xc0d478: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0d47c: ldr             x7, [x7, #0x38]
    // 0xc0d480: StoreField: r2->field_2b = r7
    //     0xc0d480: stur            w7, [x2, #0x2b]
    // 0xc0d484: StoreField: r2->field_2f = rZR
    //     0xc0d484: stur            xzr, [x2, #0x2f]
    // 0xc0d488: ldur            x1, [fp, #-0x68]
    // 0xc0d48c: StoreField: r2->field_b = r1
    //     0xc0d48c: stur            w1, [x2, #0xb]
    // 0xc0d490: r1 = <FlexParentData>
    //     0xc0d490: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xc0d494: ldr             x1, [x1, #0xe00]
    // 0xc0d498: r0 = Expanded()
    //     0xc0d498: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xc0d49c: mov             x3, x0
    // 0xc0d4a0: r0 = 1
    //     0xc0d4a0: movz            x0, #0x1
    // 0xc0d4a4: stur            x3, [fp, #-0x68]
    // 0xc0d4a8: StoreField: r3->field_13 = r0
    //     0xc0d4a8: stur            x0, [x3, #0x13]
    // 0xc0d4ac: r4 = Instance_FlexFit
    //     0xc0d4ac: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xc0d4b0: ldr             x4, [x4, #0xe08]
    // 0xc0d4b4: StoreField: r3->field_1b = r4
    //     0xc0d4b4: stur            w4, [x3, #0x1b]
    // 0xc0d4b8: ldur            x1, [fp, #-0x20]
    // 0xc0d4bc: StoreField: r3->field_b = r1
    //     0xc0d4bc: stur            w1, [x3, #0xb]
    // 0xc0d4c0: r1 = Null
    //     0xc0d4c0: mov             x1, NULL
    // 0xc0d4c4: r2 = 6
    //     0xc0d4c4: movz            x2, #0x6
    // 0xc0d4c8: r0 = AllocateArray()
    //     0xc0d4c8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0d4cc: mov             x2, x0
    // 0xc0d4d0: ldur            x0, [fp, #-0x78]
    // 0xc0d4d4: stur            x2, [fp, #-0x20]
    // 0xc0d4d8: StoreField: r2->field_f = r0
    //     0xc0d4d8: stur            w0, [x2, #0xf]
    // 0xc0d4dc: r16 = Instance_SizedBox
    //     0xc0d4dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xc0d4e0: ldr             x16, [x16, #0xb20]
    // 0xc0d4e4: StoreField: r2->field_13 = r16
    //     0xc0d4e4: stur            w16, [x2, #0x13]
    // 0xc0d4e8: ldur            x0, [fp, #-0x68]
    // 0xc0d4ec: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0d4ec: stur            w0, [x2, #0x17]
    // 0xc0d4f0: r1 = <Widget>
    //     0xc0d4f0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0d4f4: r0 = AllocateGrowableArray()
    //     0xc0d4f4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0d4f8: mov             x1, x0
    // 0xc0d4fc: ldur            x0, [fp, #-0x20]
    // 0xc0d500: stur            x1, [fp, #-0x68]
    // 0xc0d504: StoreField: r1->field_f = r0
    //     0xc0d504: stur            w0, [x1, #0xf]
    // 0xc0d508: r2 = 6
    //     0xc0d508: movz            x2, #0x6
    // 0xc0d50c: StoreField: r1->field_b = r2
    //     0xc0d50c: stur            w2, [x1, #0xb]
    // 0xc0d510: r0 = Row()
    //     0xc0d510: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc0d514: mov             x1, x0
    // 0xc0d518: r0 = Instance_Axis
    //     0xc0d518: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc0d51c: stur            x1, [fp, #-0x20]
    // 0xc0d520: StoreField: r1->field_f = r0
    //     0xc0d520: stur            w0, [x1, #0xf]
    // 0xc0d524: r2 = Instance_MainAxisAlignment
    //     0xc0d524: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0d528: ldr             x2, [x2, #0xa08]
    // 0xc0d52c: StoreField: r1->field_13 = r2
    //     0xc0d52c: stur            w2, [x1, #0x13]
    // 0xc0d530: r3 = Instance_MainAxisSize
    //     0xc0d530: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0d534: ldr             x3, [x3, #0xa10]
    // 0xc0d538: ArrayStore: r1[0] = r3  ; List_4
    //     0xc0d538: stur            w3, [x1, #0x17]
    // 0xc0d53c: r4 = Instance_CrossAxisAlignment
    //     0xc0d53c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc0d540: ldr             x4, [x4, #0xa18]
    // 0xc0d544: StoreField: r1->field_1b = r4
    //     0xc0d544: stur            w4, [x1, #0x1b]
    // 0xc0d548: r5 = Instance_VerticalDirection
    //     0xc0d548: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0d54c: ldr             x5, [x5, #0xa20]
    // 0xc0d550: StoreField: r1->field_23 = r5
    //     0xc0d550: stur            w5, [x1, #0x23]
    // 0xc0d554: r6 = Instance_Clip
    //     0xc0d554: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0d558: ldr             x6, [x6, #0x38]
    // 0xc0d55c: StoreField: r1->field_2b = r6
    //     0xc0d55c: stur            w6, [x1, #0x2b]
    // 0xc0d560: StoreField: r1->field_2f = rZR
    //     0xc0d560: stur            xzr, [x1, #0x2f]
    // 0xc0d564: ldur            x7, [fp, #-0x68]
    // 0xc0d568: StoreField: r1->field_b = r7
    //     0xc0d568: stur            w7, [x1, #0xb]
    // 0xc0d56c: r0 = Padding()
    //     0xc0d56c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0d570: mov             x1, x0
    // 0xc0d574: r0 = Instance_EdgeInsets
    //     0xc0d574: add             x0, PP, #0x34, lsl #12  ; [pp+0x34048] Obj!EdgeInsets@d59511
    //     0xc0d578: ldr             x0, [x0, #0x48]
    // 0xc0d57c: stur            x1, [fp, #-0x68]
    // 0xc0d580: StoreField: r1->field_f = r0
    //     0xc0d580: stur            w0, [x1, #0xf]
    // 0xc0d584: ldur            x2, [fp, #-0x20]
    // 0xc0d588: StoreField: r1->field_b = r2
    //     0xc0d588: stur            w2, [x1, #0xb]
    // 0xc0d58c: r0 = ColoredBox()
    //     0xc0d58c: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0xc0d590: mov             x3, x0
    // 0xc0d594: ldur            x0, [fp, #-0x38]
    // 0xc0d598: stur            x3, [fp, #-0x70]
    // 0xc0d59c: StoreField: r3->field_f = r0
    //     0xc0d59c: stur            w0, [x3, #0xf]
    // 0xc0d5a0: ldur            x0, [fp, #-0x68]
    // 0xc0d5a4: StoreField: r3->field_b = r0
    //     0xc0d5a4: stur            w0, [x3, #0xb]
    // 0xc0d5a8: ldur            x0, [fp, #-8]
    // 0xc0d5ac: LoadField: r1 = r0->field_b
    //     0xc0d5ac: ldur            w1, [x0, #0xb]
    // 0xc0d5b0: DecompressPointer r1
    //     0xc0d5b0: add             x1, x1, HEAP, lsl #32
    // 0xc0d5b4: cmp             w1, NULL
    // 0xc0d5b8: b.eq            #0xc0eaf4
    // 0xc0d5bc: LoadField: r2 = r1->field_13
    //     0xc0d5bc: ldur            w2, [x1, #0x13]
    // 0xc0d5c0: DecompressPointer r2
    //     0xc0d5c0: add             x2, x2, HEAP, lsl #32
    // 0xc0d5c4: cmp             w2, NULL
    // 0xc0d5c8: b.ne            #0xc0d5d4
    // 0xc0d5cc: r4 = ""
    //     0xc0d5cc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0d5d0: b               #0xc0d5d8
    // 0xc0d5d4: mov             x4, x2
    // 0xc0d5d8: stur            x4, [fp, #-0x20]
    // 0xc0d5dc: r1 = Function '<anonymous closure>':.
    //     0xc0d5dc: add             x1, PP, #0x53, lsl #12  ; [pp+0x539c0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc0d5e0: ldr             x1, [x1, #0x9c0]
    // 0xc0d5e4: r2 = Null
    //     0xc0d5e4: mov             x2, NULL
    // 0xc0d5e8: r0 = AllocateClosure()
    //     0xc0d5e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0d5ec: stur            x0, [fp, #-0x38]
    // 0xc0d5f0: r0 = CachedNetworkImage()
    //     0xc0d5f0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xc0d5f4: stur            x0, [fp, #-0x68]
    // 0xc0d5f8: r16 = Instance_BoxFit
    //     0xc0d5f8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xc0d5fc: ldr             x16, [x16, #0x118]
    // 0xc0d600: r30 = 84.000000
    //     0xc0d600: add             lr, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0xc0d604: ldr             lr, [lr, #0xf90]
    // 0xc0d608: stp             lr, x16, [SP, #0x10]
    // 0xc0d60c: r16 = 55.000000
    //     0xc0d60c: add             x16, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xc0d610: ldr             x16, [x16, #0x9b8]
    // 0xc0d614: ldur            lr, [fp, #-0x38]
    // 0xc0d618: stp             lr, x16, [SP]
    // 0xc0d61c: mov             x1, x0
    // 0xc0d620: ldur            x2, [fp, #-0x20]
    // 0xc0d624: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0xc0d624: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fbf8] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0xc0d628: ldr             x4, [x4, #0xbf8]
    // 0xc0d62c: r0 = CachedNetworkImage()
    //     0xc0d62c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xc0d630: ldur            x0, [fp, #-8]
    // 0xc0d634: LoadField: r1 = r0->field_b
    //     0xc0d634: ldur            w1, [x0, #0xb]
    // 0xc0d638: DecompressPointer r1
    //     0xc0d638: add             x1, x1, HEAP, lsl #32
    // 0xc0d63c: cmp             w1, NULL
    // 0xc0d640: b.eq            #0xc0eaf8
    // 0xc0d644: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xc0d644: ldur            w2, [x1, #0x17]
    // 0xc0d648: DecompressPointer r2
    //     0xc0d648: add             x2, x2, HEAP, lsl #32
    // 0xc0d64c: cmp             w2, NULL
    // 0xc0d650: b.ne            #0xc0d65c
    // 0xc0d654: r1 = Null
    //     0xc0d654: mov             x1, NULL
    // 0xc0d658: b               #0xc0d664
    // 0xc0d65c: LoadField: r1 = r2->field_f
    //     0xc0d65c: ldur            w1, [x2, #0xf]
    // 0xc0d660: DecompressPointer r1
    //     0xc0d660: add             x1, x1, HEAP, lsl #32
    // 0xc0d664: cmp             w1, NULL
    // 0xc0d668: b.ne            #0xc0d674
    // 0xc0d66c: r2 = ""
    //     0xc0d66c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0d670: b               #0xc0d678
    // 0xc0d674: mov             x2, x1
    // 0xc0d678: stur            x2, [fp, #-0x20]
    // 0xc0d67c: r16 = Instance_Color
    //     0xc0d67c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0d680: r30 = 12.000000
    //     0xc0d680: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0d684: ldr             lr, [lr, #0x9e8]
    // 0xc0d688: stp             lr, x16, [SP]
    // 0xc0d68c: ldur            x1, [fp, #-0x18]
    // 0xc0d690: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc0d690: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc0d694: ldr             x4, [x4, #0x9b8]
    // 0xc0d698: r0 = copyWith()
    //     0xc0d698: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0d69c: stur            x0, [fp, #-0x38]
    // 0xc0d6a0: r0 = Text()
    //     0xc0d6a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0d6a4: mov             x3, x0
    // 0xc0d6a8: ldur            x0, [fp, #-0x20]
    // 0xc0d6ac: stur            x3, [fp, #-0x78]
    // 0xc0d6b0: StoreField: r3->field_b = r0
    //     0xc0d6b0: stur            w0, [x3, #0xb]
    // 0xc0d6b4: ldur            x0, [fp, #-0x38]
    // 0xc0d6b8: StoreField: r3->field_13 = r0
    //     0xc0d6b8: stur            w0, [x3, #0x13]
    // 0xc0d6bc: r0 = Instance_TextOverflow
    //     0xc0d6bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xc0d6c0: ldr             x0, [x0, #0xe10]
    // 0xc0d6c4: StoreField: r3->field_2b = r0
    //     0xc0d6c4: stur            w0, [x3, #0x2b]
    // 0xc0d6c8: r0 = 2
    //     0xc0d6c8: movz            x0, #0x2
    // 0xc0d6cc: StoreField: r3->field_37 = r0
    //     0xc0d6cc: stur            w0, [x3, #0x37]
    // 0xc0d6d0: r1 = Null
    //     0xc0d6d0: mov             x1, NULL
    // 0xc0d6d4: r2 = 8
    //     0xc0d6d4: movz            x2, #0x8
    // 0xc0d6d8: r0 = AllocateArray()
    //     0xc0d6d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0d6dc: r16 = "Size: "
    //     0xc0d6dc: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xc0d6e0: ldr             x16, [x16, #0xf00]
    // 0xc0d6e4: StoreField: r0->field_f = r16
    //     0xc0d6e4: stur            w16, [x0, #0xf]
    // 0xc0d6e8: ldur            x1, [fp, #-8]
    // 0xc0d6ec: LoadField: r2 = r1->field_b
    //     0xc0d6ec: ldur            w2, [x1, #0xb]
    // 0xc0d6f0: DecompressPointer r2
    //     0xc0d6f0: add             x2, x2, HEAP, lsl #32
    // 0xc0d6f4: cmp             w2, NULL
    // 0xc0d6f8: b.eq            #0xc0eafc
    // 0xc0d6fc: LoadField: r3 = r2->field_f
    //     0xc0d6fc: ldur            w3, [x2, #0xf]
    // 0xc0d700: DecompressPointer r3
    //     0xc0d700: add             x3, x3, HEAP, lsl #32
    // 0xc0d704: cmp             w3, NULL
    // 0xc0d708: b.ne            #0xc0d714
    // 0xc0d70c: r3 = Null
    //     0xc0d70c: mov             x3, NULL
    // 0xc0d710: b               #0xc0d720
    // 0xc0d714: LoadField: r4 = r3->field_1f
    //     0xc0d714: ldur            w4, [x3, #0x1f]
    // 0xc0d718: DecompressPointer r4
    //     0xc0d718: add             x4, x4, HEAP, lsl #32
    // 0xc0d71c: mov             x3, x4
    // 0xc0d720: cmp             w3, NULL
    // 0xc0d724: b.ne            #0xc0d72c
    // 0xc0d728: r3 = ""
    //     0xc0d728: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0d72c: StoreField: r0->field_13 = r3
    //     0xc0d72c: stur            w3, [x0, #0x13]
    // 0xc0d730: r16 = " / Qty: "
    //     0xc0d730: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xc0d734: ldr             x16, [x16, #0x760]
    // 0xc0d738: ArrayStore: r0[0] = r16  ; List_4
    //     0xc0d738: stur            w16, [x0, #0x17]
    // 0xc0d73c: LoadField: r3 = r2->field_1b
    //     0xc0d73c: ldur            w3, [x2, #0x1b]
    // 0xc0d740: DecompressPointer r3
    //     0xc0d740: add             x3, x3, HEAP, lsl #32
    // 0xc0d744: StoreField: r0->field_1b = r3
    //     0xc0d744: stur            w3, [x0, #0x1b]
    // 0xc0d748: str             x0, [SP]
    // 0xc0d74c: r0 = _interpolate()
    //     0xc0d74c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xc0d750: stur            x0, [fp, #-0x20]
    // 0xc0d754: r16 = Instance_Color
    //     0xc0d754: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0d758: r30 = 12.000000
    //     0xc0d758: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0d75c: ldr             lr, [lr, #0x9e8]
    // 0xc0d760: stp             lr, x16, [SP]
    // 0xc0d764: ldur            x1, [fp, #-0x60]
    // 0xc0d768: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc0d768: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc0d76c: ldr             x4, [x4, #0x9b8]
    // 0xc0d770: r0 = copyWith()
    //     0xc0d770: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0d774: stur            x0, [fp, #-0x38]
    // 0xc0d778: r0 = Text()
    //     0xc0d778: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0d77c: mov             x2, x0
    // 0xc0d780: ldur            x0, [fp, #-0x20]
    // 0xc0d784: stur            x2, [fp, #-0x80]
    // 0xc0d788: StoreField: r2->field_b = r0
    //     0xc0d788: stur            w0, [x2, #0xb]
    // 0xc0d78c: ldur            x0, [fp, #-0x38]
    // 0xc0d790: StoreField: r2->field_13 = r0
    //     0xc0d790: stur            w0, [x2, #0x13]
    // 0xc0d794: ldur            x0, [fp, #-8]
    // 0xc0d798: LoadField: r1 = r0->field_b
    //     0xc0d798: ldur            w1, [x0, #0xb]
    // 0xc0d79c: DecompressPointer r1
    //     0xc0d79c: add             x1, x1, HEAP, lsl #32
    // 0xc0d7a0: cmp             w1, NULL
    // 0xc0d7a4: b.eq            #0xc0eb00
    // 0xc0d7a8: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xc0d7a8: ldur            w3, [x1, #0x17]
    // 0xc0d7ac: DecompressPointer r3
    //     0xc0d7ac: add             x3, x3, HEAP, lsl #32
    // 0xc0d7b0: cmp             w3, NULL
    // 0xc0d7b4: b.ne            #0xc0d7c0
    // 0xc0d7b8: r1 = Null
    //     0xc0d7b8: mov             x1, NULL
    // 0xc0d7bc: b               #0xc0d7c8
    // 0xc0d7c0: LoadField: r1 = r3->field_43
    //     0xc0d7c0: ldur            w1, [x3, #0x43]
    // 0xc0d7c4: DecompressPointer r1
    //     0xc0d7c4: add             x1, x1, HEAP, lsl #32
    // 0xc0d7c8: cmp             w1, NULL
    // 0xc0d7cc: b.ne            #0xc0d7d8
    // 0xc0d7d0: r6 = ""
    //     0xc0d7d0: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0d7d4: b               #0xc0d7dc
    // 0xc0d7d8: mov             x6, x1
    // 0xc0d7dc: ldur            x5, [fp, #-0x70]
    // 0xc0d7e0: ldur            x4, [fp, #-0x68]
    // 0xc0d7e4: ldur            x3, [fp, #-0x78]
    // 0xc0d7e8: stur            x6, [fp, #-0x20]
    // 0xc0d7ec: r16 = Instance_Color
    //     0xc0d7ec: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0d7f0: r30 = 12.000000
    //     0xc0d7f0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0d7f4: ldr             lr, [lr, #0x9e8]
    // 0xc0d7f8: stp             lr, x16, [SP]
    // 0xc0d7fc: ldur            x1, [fp, #-0x18]
    // 0xc0d800: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc0d800: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc0d804: ldr             x4, [x4, #0x9b8]
    // 0xc0d808: r0 = copyWith()
    //     0xc0d808: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0d80c: stur            x0, [fp, #-0x38]
    // 0xc0d810: r0 = Text()
    //     0xc0d810: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0d814: mov             x3, x0
    // 0xc0d818: ldur            x0, [fp, #-0x20]
    // 0xc0d81c: stur            x3, [fp, #-0x88]
    // 0xc0d820: StoreField: r3->field_b = r0
    //     0xc0d820: stur            w0, [x3, #0xb]
    // 0xc0d824: ldur            x0, [fp, #-0x38]
    // 0xc0d828: StoreField: r3->field_13 = r0
    //     0xc0d828: stur            w0, [x3, #0x13]
    // 0xc0d82c: r1 = Null
    //     0xc0d82c: mov             x1, NULL
    // 0xc0d830: r2 = 6
    //     0xc0d830: movz            x2, #0x6
    // 0xc0d834: r0 = AllocateArray()
    //     0xc0d834: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0d838: mov             x2, x0
    // 0xc0d83c: ldur            x0, [fp, #-0x78]
    // 0xc0d840: stur            x2, [fp, #-0x20]
    // 0xc0d844: StoreField: r2->field_f = r0
    //     0xc0d844: stur            w0, [x2, #0xf]
    // 0xc0d848: ldur            x0, [fp, #-0x80]
    // 0xc0d84c: StoreField: r2->field_13 = r0
    //     0xc0d84c: stur            w0, [x2, #0x13]
    // 0xc0d850: ldur            x0, [fp, #-0x88]
    // 0xc0d854: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0d854: stur            w0, [x2, #0x17]
    // 0xc0d858: r1 = <Widget>
    //     0xc0d858: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0d85c: r0 = AllocateGrowableArray()
    //     0xc0d85c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0d860: mov             x1, x0
    // 0xc0d864: ldur            x0, [fp, #-0x20]
    // 0xc0d868: stur            x1, [fp, #-0x38]
    // 0xc0d86c: StoreField: r1->field_f = r0
    //     0xc0d86c: stur            w0, [x1, #0xf]
    // 0xc0d870: r2 = 6
    //     0xc0d870: movz            x2, #0x6
    // 0xc0d874: StoreField: r1->field_b = r2
    //     0xc0d874: stur            w2, [x1, #0xb]
    // 0xc0d878: r0 = Column()
    //     0xc0d878: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0d87c: mov             x2, x0
    // 0xc0d880: r0 = Instance_Axis
    //     0xc0d880: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0d884: stur            x2, [fp, #-0x20]
    // 0xc0d888: StoreField: r2->field_f = r0
    //     0xc0d888: stur            w0, [x2, #0xf]
    // 0xc0d88c: r3 = Instance_MainAxisAlignment
    //     0xc0d88c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0d890: ldr             x3, [x3, #0xa08]
    // 0xc0d894: StoreField: r2->field_13 = r3
    //     0xc0d894: stur            w3, [x2, #0x13]
    // 0xc0d898: r4 = Instance_MainAxisSize
    //     0xc0d898: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0d89c: ldr             x4, [x4, #0xa10]
    // 0xc0d8a0: ArrayStore: r2[0] = r4  ; List_4
    //     0xc0d8a0: stur            w4, [x2, #0x17]
    // 0xc0d8a4: r5 = Instance_CrossAxisAlignment
    //     0xc0d8a4: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc0d8a8: ldr             x5, [x5, #0x890]
    // 0xc0d8ac: StoreField: r2->field_1b = r5
    //     0xc0d8ac: stur            w5, [x2, #0x1b]
    // 0xc0d8b0: r6 = Instance_VerticalDirection
    //     0xc0d8b0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0d8b4: ldr             x6, [x6, #0xa20]
    // 0xc0d8b8: StoreField: r2->field_23 = r6
    //     0xc0d8b8: stur            w6, [x2, #0x23]
    // 0xc0d8bc: r7 = Instance_Clip
    //     0xc0d8bc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0d8c0: ldr             x7, [x7, #0x38]
    // 0xc0d8c4: StoreField: r2->field_2b = r7
    //     0xc0d8c4: stur            w7, [x2, #0x2b]
    // 0xc0d8c8: StoreField: r2->field_2f = rZR
    //     0xc0d8c8: stur            xzr, [x2, #0x2f]
    // 0xc0d8cc: ldur            x1, [fp, #-0x38]
    // 0xc0d8d0: StoreField: r2->field_b = r1
    //     0xc0d8d0: stur            w1, [x2, #0xb]
    // 0xc0d8d4: r1 = <FlexParentData>
    //     0xc0d8d4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xc0d8d8: ldr             x1, [x1, #0xe00]
    // 0xc0d8dc: r0 = Expanded()
    //     0xc0d8dc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xc0d8e0: mov             x3, x0
    // 0xc0d8e4: r0 = 1
    //     0xc0d8e4: movz            x0, #0x1
    // 0xc0d8e8: stur            x3, [fp, #-0x38]
    // 0xc0d8ec: StoreField: r3->field_13 = r0
    //     0xc0d8ec: stur            x0, [x3, #0x13]
    // 0xc0d8f0: r4 = Instance_FlexFit
    //     0xc0d8f0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xc0d8f4: ldr             x4, [x4, #0xe08]
    // 0xc0d8f8: StoreField: r3->field_1b = r4
    //     0xc0d8f8: stur            w4, [x3, #0x1b]
    // 0xc0d8fc: ldur            x1, [fp, #-0x20]
    // 0xc0d900: StoreField: r3->field_b = r1
    //     0xc0d900: stur            w1, [x3, #0xb]
    // 0xc0d904: r1 = Null
    //     0xc0d904: mov             x1, NULL
    // 0xc0d908: r2 = 6
    //     0xc0d908: movz            x2, #0x6
    // 0xc0d90c: r0 = AllocateArray()
    //     0xc0d90c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0d910: mov             x2, x0
    // 0xc0d914: ldur            x0, [fp, #-0x68]
    // 0xc0d918: stur            x2, [fp, #-0x20]
    // 0xc0d91c: StoreField: r2->field_f = r0
    //     0xc0d91c: stur            w0, [x2, #0xf]
    // 0xc0d920: r16 = Instance_SizedBox
    //     0xc0d920: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xc0d924: ldr             x16, [x16, #0xb20]
    // 0xc0d928: StoreField: r2->field_13 = r16
    //     0xc0d928: stur            w16, [x2, #0x13]
    // 0xc0d92c: ldur            x0, [fp, #-0x38]
    // 0xc0d930: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0d930: stur            w0, [x2, #0x17]
    // 0xc0d934: r1 = <Widget>
    //     0xc0d934: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0d938: r0 = AllocateGrowableArray()
    //     0xc0d938: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0d93c: mov             x1, x0
    // 0xc0d940: ldur            x0, [fp, #-0x20]
    // 0xc0d944: stur            x1, [fp, #-0x38]
    // 0xc0d948: StoreField: r1->field_f = r0
    //     0xc0d948: stur            w0, [x1, #0xf]
    // 0xc0d94c: r2 = 6
    //     0xc0d94c: movz            x2, #0x6
    // 0xc0d950: StoreField: r1->field_b = r2
    //     0xc0d950: stur            w2, [x1, #0xb]
    // 0xc0d954: r0 = Row()
    //     0xc0d954: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc0d958: mov             x3, x0
    // 0xc0d95c: r0 = Instance_Axis
    //     0xc0d95c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc0d960: stur            x3, [fp, #-0x20]
    // 0xc0d964: StoreField: r3->field_f = r0
    //     0xc0d964: stur            w0, [x3, #0xf]
    // 0xc0d968: r4 = Instance_MainAxisAlignment
    //     0xc0d968: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0d96c: ldr             x4, [x4, #0xa08]
    // 0xc0d970: StoreField: r3->field_13 = r4
    //     0xc0d970: stur            w4, [x3, #0x13]
    // 0xc0d974: r5 = Instance_MainAxisSize
    //     0xc0d974: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0d978: ldr             x5, [x5, #0xa10]
    // 0xc0d97c: ArrayStore: r3[0] = r5  ; List_4
    //     0xc0d97c: stur            w5, [x3, #0x17]
    // 0xc0d980: r6 = Instance_CrossAxisAlignment
    //     0xc0d980: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc0d984: ldr             x6, [x6, #0xa18]
    // 0xc0d988: StoreField: r3->field_1b = r6
    //     0xc0d988: stur            w6, [x3, #0x1b]
    // 0xc0d98c: r7 = Instance_VerticalDirection
    //     0xc0d98c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0d990: ldr             x7, [x7, #0xa20]
    // 0xc0d994: StoreField: r3->field_23 = r7
    //     0xc0d994: stur            w7, [x3, #0x23]
    // 0xc0d998: r8 = Instance_Clip
    //     0xc0d998: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0d99c: ldr             x8, [x8, #0x38]
    // 0xc0d9a0: StoreField: r3->field_2b = r8
    //     0xc0d9a0: stur            w8, [x3, #0x2b]
    // 0xc0d9a4: StoreField: r3->field_2f = rZR
    //     0xc0d9a4: stur            xzr, [x3, #0x2f]
    // 0xc0d9a8: ldur            x1, [fp, #-0x38]
    // 0xc0d9ac: StoreField: r3->field_b = r1
    //     0xc0d9ac: stur            w1, [x3, #0xb]
    // 0xc0d9b0: r1 = Null
    //     0xc0d9b0: mov             x1, NULL
    // 0xc0d9b4: r2 = 2
    //     0xc0d9b4: movz            x2, #0x2
    // 0xc0d9b8: r0 = AllocateArray()
    //     0xc0d9b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0d9bc: mov             x2, x0
    // 0xc0d9c0: ldur            x0, [fp, #-0x20]
    // 0xc0d9c4: stur            x2, [fp, #-0x38]
    // 0xc0d9c8: StoreField: r2->field_f = r0
    //     0xc0d9c8: stur            w0, [x2, #0xf]
    // 0xc0d9cc: r1 = <Widget>
    //     0xc0d9cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0d9d0: r0 = AllocateGrowableArray()
    //     0xc0d9d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0d9d4: mov             x1, x0
    // 0xc0d9d8: ldur            x0, [fp, #-0x38]
    // 0xc0d9dc: stur            x1, [fp, #-0x20]
    // 0xc0d9e0: StoreField: r1->field_f = r0
    //     0xc0d9e0: stur            w0, [x1, #0xf]
    // 0xc0d9e4: r0 = 2
    //     0xc0d9e4: movz            x0, #0x2
    // 0xc0d9e8: StoreField: r1->field_b = r0
    //     0xc0d9e8: stur            w0, [x1, #0xb]
    // 0xc0d9ec: r0 = Column()
    //     0xc0d9ec: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0d9f0: mov             x1, x0
    // 0xc0d9f4: r0 = Instance_Axis
    //     0xc0d9f4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0d9f8: stur            x1, [fp, #-0x38]
    // 0xc0d9fc: StoreField: r1->field_f = r0
    //     0xc0d9fc: stur            w0, [x1, #0xf]
    // 0xc0da00: r2 = Instance_MainAxisAlignment
    //     0xc0da00: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0da04: ldr             x2, [x2, #0xa08]
    // 0xc0da08: StoreField: r1->field_13 = r2
    //     0xc0da08: stur            w2, [x1, #0x13]
    // 0xc0da0c: r3 = Instance_MainAxisSize
    //     0xc0da0c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0da10: ldr             x3, [x3, #0xa10]
    // 0xc0da14: ArrayStore: r1[0] = r3  ; List_4
    //     0xc0da14: stur            w3, [x1, #0x17]
    // 0xc0da18: r4 = Instance_CrossAxisAlignment
    //     0xc0da18: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc0da1c: ldr             x4, [x4, #0x890]
    // 0xc0da20: StoreField: r1->field_1b = r4
    //     0xc0da20: stur            w4, [x1, #0x1b]
    // 0xc0da24: r5 = Instance_VerticalDirection
    //     0xc0da24: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0da28: ldr             x5, [x5, #0xa20]
    // 0xc0da2c: StoreField: r1->field_23 = r5
    //     0xc0da2c: stur            w5, [x1, #0x23]
    // 0xc0da30: r6 = Instance_Clip
    //     0xc0da30: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0da34: ldr             x6, [x6, #0x38]
    // 0xc0da38: StoreField: r1->field_2b = r6
    //     0xc0da38: stur            w6, [x1, #0x2b]
    // 0xc0da3c: StoreField: r1->field_2f = rZR
    //     0xc0da3c: stur            xzr, [x1, #0x2f]
    // 0xc0da40: ldur            x7, [fp, #-0x20]
    // 0xc0da44: StoreField: r1->field_b = r7
    //     0xc0da44: stur            w7, [x1, #0xb]
    // 0xc0da48: r0 = Padding()
    //     0xc0da48: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0da4c: mov             x3, x0
    // 0xc0da50: r0 = Instance_EdgeInsets
    //     0xc0da50: add             x0, PP, #0x34, lsl #12  ; [pp+0x34048] Obj!EdgeInsets@d59511
    //     0xc0da54: ldr             x0, [x0, #0x48]
    // 0xc0da58: stur            x3, [fp, #-0x20]
    // 0xc0da5c: StoreField: r3->field_f = r0
    //     0xc0da5c: stur            w0, [x3, #0xf]
    // 0xc0da60: ldur            x0, [fp, #-0x38]
    // 0xc0da64: StoreField: r3->field_b = r0
    //     0xc0da64: stur            w0, [x3, #0xb]
    // 0xc0da68: r1 = Null
    //     0xc0da68: mov             x1, NULL
    // 0xc0da6c: r2 = 4
    //     0xc0da6c: movz            x2, #0x4
    // 0xc0da70: r0 = AllocateArray()
    //     0xc0da70: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0da74: mov             x2, x0
    // 0xc0da78: ldur            x0, [fp, #-0x70]
    // 0xc0da7c: stur            x2, [fp, #-0x38]
    // 0xc0da80: StoreField: r2->field_f = r0
    //     0xc0da80: stur            w0, [x2, #0xf]
    // 0xc0da84: ldur            x0, [fp, #-0x20]
    // 0xc0da88: StoreField: r2->field_13 = r0
    //     0xc0da88: stur            w0, [x2, #0x13]
    // 0xc0da8c: r1 = <Widget>
    //     0xc0da8c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0da90: r0 = AllocateGrowableArray()
    //     0xc0da90: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0da94: mov             x1, x0
    // 0xc0da98: ldur            x0, [fp, #-0x38]
    // 0xc0da9c: stur            x1, [fp, #-0x20]
    // 0xc0daa0: StoreField: r1->field_f = r0
    //     0xc0daa0: stur            w0, [x1, #0xf]
    // 0xc0daa4: r2 = 4
    //     0xc0daa4: movz            x2, #0x4
    // 0xc0daa8: StoreField: r1->field_b = r2
    //     0xc0daa8: stur            w2, [x1, #0xb]
    // 0xc0daac: r0 = Column()
    //     0xc0daac: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0dab0: mov             x1, x0
    // 0xc0dab4: r0 = Instance_Axis
    //     0xc0dab4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0dab8: stur            x1, [fp, #-0x38]
    // 0xc0dabc: StoreField: r1->field_f = r0
    //     0xc0dabc: stur            w0, [x1, #0xf]
    // 0xc0dac0: r2 = Instance_MainAxisAlignment
    //     0xc0dac0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0dac4: ldr             x2, [x2, #0xa08]
    // 0xc0dac8: StoreField: r1->field_13 = r2
    //     0xc0dac8: stur            w2, [x1, #0x13]
    // 0xc0dacc: r3 = Instance_MainAxisSize
    //     0xc0dacc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0dad0: ldr             x3, [x3, #0xa10]
    // 0xc0dad4: ArrayStore: r1[0] = r3  ; List_4
    //     0xc0dad4: stur            w3, [x1, #0x17]
    // 0xc0dad8: r4 = Instance_CrossAxisAlignment
    //     0xc0dad8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc0dadc: ldr             x4, [x4, #0xa18]
    // 0xc0dae0: StoreField: r1->field_1b = r4
    //     0xc0dae0: stur            w4, [x1, #0x1b]
    // 0xc0dae4: r5 = Instance_VerticalDirection
    //     0xc0dae4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0dae8: ldr             x5, [x5, #0xa20]
    // 0xc0daec: StoreField: r1->field_23 = r5
    //     0xc0daec: stur            w5, [x1, #0x23]
    // 0xc0daf0: r6 = Instance_Clip
    //     0xc0daf0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0daf4: ldr             x6, [x6, #0x38]
    // 0xc0daf8: StoreField: r1->field_2b = r6
    //     0xc0daf8: stur            w6, [x1, #0x2b]
    // 0xc0dafc: StoreField: r1->field_2f = rZR
    //     0xc0dafc: stur            xzr, [x1, #0x2f]
    // 0xc0db00: ldur            x7, [fp, #-0x20]
    // 0xc0db04: StoreField: r1->field_b = r7
    //     0xc0db04: stur            w7, [x1, #0xb]
    // 0xc0db08: r0 = SvgPicture()
    //     0xc0db08: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc0db0c: stur            x0, [fp, #-0x20]
    // 0xc0db10: r16 = Instance_BoxFit
    //     0xc0db10: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xc0db14: ldr             x16, [x16, #0xb18]
    // 0xc0db18: str             x16, [SP]
    // 0xc0db1c: mov             x1, x0
    // 0xc0db20: r2 = "assets/images/product_between_icon.svg"
    //     0xc0db20: add             x2, PP, #0x35, lsl #12  ; [pp+0x35f28] "assets/images/product_between_icon.svg"
    //     0xc0db24: ldr             x2, [x2, #0xf28]
    // 0xc0db28: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xc0db28: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xc0db2c: ldr             x4, [x4, #0xb0]
    // 0xc0db30: r0 = SvgPicture.asset()
    //     0xc0db30: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc0db34: r1 = <StackParentData>
    //     0xc0db34: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xc0db38: ldr             x1, [x1, #0x8e0]
    // 0xc0db3c: r0 = Positioned()
    //     0xc0db3c: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xc0db40: mov             x3, x0
    // 0xc0db44: r0 = 0.000000
    //     0xc0db44: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xc0db48: stur            x3, [fp, #-0x68]
    // 0xc0db4c: StoreField: r3->field_13 = r0
    //     0xc0db4c: stur            w0, [x3, #0x13]
    // 0xc0db50: ArrayStore: r3[0] = r0  ; List_4
    //     0xc0db50: stur            w0, [x3, #0x17]
    // 0xc0db54: StoreField: r3->field_1b = r0
    //     0xc0db54: stur            w0, [x3, #0x1b]
    // 0xc0db58: StoreField: r3->field_1f = r0
    //     0xc0db58: stur            w0, [x3, #0x1f]
    // 0xc0db5c: ldur            x0, [fp, #-0x20]
    // 0xc0db60: StoreField: r3->field_b = r0
    //     0xc0db60: stur            w0, [x3, #0xb]
    // 0xc0db64: r1 = Null
    //     0xc0db64: mov             x1, NULL
    // 0xc0db68: r2 = 4
    //     0xc0db68: movz            x2, #0x4
    // 0xc0db6c: r0 = AllocateArray()
    //     0xc0db6c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0db70: mov             x2, x0
    // 0xc0db74: ldur            x0, [fp, #-0x38]
    // 0xc0db78: stur            x2, [fp, #-0x20]
    // 0xc0db7c: StoreField: r2->field_f = r0
    //     0xc0db7c: stur            w0, [x2, #0xf]
    // 0xc0db80: ldur            x0, [fp, #-0x68]
    // 0xc0db84: StoreField: r2->field_13 = r0
    //     0xc0db84: stur            w0, [x2, #0x13]
    // 0xc0db88: r1 = <Widget>
    //     0xc0db88: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0db8c: r0 = AllocateGrowableArray()
    //     0xc0db8c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0db90: mov             x1, x0
    // 0xc0db94: ldur            x0, [fp, #-0x20]
    // 0xc0db98: stur            x1, [fp, #-0x38]
    // 0xc0db9c: StoreField: r1->field_f = r0
    //     0xc0db9c: stur            w0, [x1, #0xf]
    // 0xc0dba0: r2 = 4
    //     0xc0dba0: movz            x2, #0x4
    // 0xc0dba4: StoreField: r1->field_b = r2
    //     0xc0dba4: stur            w2, [x1, #0xb]
    // 0xc0dba8: r0 = Stack()
    //     0xc0dba8: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xc0dbac: mov             x2, x0
    // 0xc0dbb0: r0 = Instance_Alignment
    //     0xc0dbb0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc0dbb4: ldr             x0, [x0, #0xb10]
    // 0xc0dbb8: stur            x2, [fp, #-0x20]
    // 0xc0dbbc: StoreField: r2->field_f = r0
    //     0xc0dbbc: stur            w0, [x2, #0xf]
    // 0xc0dbc0: r0 = Instance_StackFit
    //     0xc0dbc0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xc0dbc4: ldr             x0, [x0, #0xfa8]
    // 0xc0dbc8: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0dbc8: stur            w0, [x2, #0x17]
    // 0xc0dbcc: r0 = Instance_Clip
    //     0xc0dbcc: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xc0dbd0: ldr             x0, [x0, #0x7e0]
    // 0xc0dbd4: StoreField: r2->field_1b = r0
    //     0xc0dbd4: stur            w0, [x2, #0x1b]
    // 0xc0dbd8: ldur            x0, [fp, #-0x38]
    // 0xc0dbdc: StoreField: r2->field_b = r0
    //     0xc0dbdc: stur            w0, [x2, #0xb]
    // 0xc0dbe0: ldur            x3, [fp, #-8]
    // 0xc0dbe4: LoadField: r0 = r3->field_b
    //     0xc0dbe4: ldur            w0, [x3, #0xb]
    // 0xc0dbe8: DecompressPointer r0
    //     0xc0dbe8: add             x0, x0, HEAP, lsl #32
    // 0xc0dbec: cmp             w0, NULL
    // 0xc0dbf0: b.eq            #0xc0eb04
    // 0xc0dbf4: LoadField: r1 = r0->field_2b
    //     0xc0dbf4: ldur            w1, [x0, #0x2b]
    // 0xc0dbf8: DecompressPointer r1
    //     0xc0dbf8: add             x1, x1, HEAP, lsl #32
    // 0xc0dbfc: cmp             w1, NULL
    // 0xc0dc00: b.ne            #0xc0dc0c
    // 0xc0dc04: r0 = Null
    //     0xc0dc04: mov             x0, NULL
    // 0xc0dc08: b               #0xc0dc24
    // 0xc0dc0c: r0 = LoadClassIdInstr(r1)
    //     0xc0dc0c: ldur            x0, [x1, #-1]
    //     0xc0dc10: ubfx            x0, x0, #0xc, #0x14
    // 0xc0dc14: r0 = GDT[cid_x0 + 0xe517]()
    //     0xc0dc14: movz            x17, #0xe517
    //     0xc0dc18: add             lr, x0, x17
    //     0xc0dc1c: ldr             lr, [x21, lr, lsl #3]
    //     0xc0dc20: blr             lr
    // 0xc0dc24: cmp             w0, NULL
    // 0xc0dc28: b.ne            #0xc0dc34
    // 0xc0dc2c: r1 = false
    //     0xc0dc2c: add             x1, NULL, #0x30  ; false
    // 0xc0dc30: b               #0xc0dc38
    // 0xc0dc34: mov             x1, x0
    // 0xc0dc38: ldur            x0, [fp, #-8]
    // 0xc0dc3c: stur            x1, [fp, #-0x70]
    // 0xc0dc40: LoadField: r2 = r0->field_b
    //     0xc0dc40: ldur            w2, [x0, #0xb]
    // 0xc0dc44: DecompressPointer r2
    //     0xc0dc44: add             x2, x2, HEAP, lsl #32
    // 0xc0dc48: cmp             w2, NULL
    // 0xc0dc4c: b.eq            #0xc0eb08
    // 0xc0dc50: LoadField: r3 = r2->field_2b
    //     0xc0dc50: ldur            w3, [x2, #0x2b]
    // 0xc0dc54: DecompressPointer r3
    //     0xc0dc54: add             x3, x3, HEAP, lsl #32
    // 0xc0dc58: stur            x3, [fp, #-0x68]
    // 0xc0dc5c: LoadField: r4 = r2->field_27
    //     0xc0dc5c: ldur            w4, [x2, #0x27]
    // 0xc0dc60: DecompressPointer r4
    //     0xc0dc60: add             x4, x4, HEAP, lsl #32
    // 0xc0dc64: stur            x4, [fp, #-0x38]
    // 0xc0dc68: r0 = CustomisedStrip()
    //     0xc0dc68: bl              #0x9d72b8  ; AllocateCustomisedStripStub -> CustomisedStrip (size=0x18)
    // 0xc0dc6c: mov             x1, x0
    // 0xc0dc70: ldur            x0, [fp, #-0x68]
    // 0xc0dc74: stur            x1, [fp, #-0x78]
    // 0xc0dc78: StoreField: r1->field_b = r0
    //     0xc0dc78: stur            w0, [x1, #0xb]
    // 0xc0dc7c: ldur            x0, [fp, #-0x38]
    // 0xc0dc80: StoreField: r1->field_13 = r0
    //     0xc0dc80: stur            w0, [x1, #0x13]
    // 0xc0dc84: r0 = Padding()
    //     0xc0dc84: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0dc88: mov             x1, x0
    // 0xc0dc8c: r0 = Instance_EdgeInsets
    //     0xc0dc8c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xc0dc90: ldr             x0, [x0, #0x668]
    // 0xc0dc94: stur            x1, [fp, #-0x38]
    // 0xc0dc98: StoreField: r1->field_f = r0
    //     0xc0dc98: stur            w0, [x1, #0xf]
    // 0xc0dc9c: ldur            x2, [fp, #-0x78]
    // 0xc0dca0: StoreField: r1->field_b = r2
    //     0xc0dca0: stur            w2, [x1, #0xb]
    // 0xc0dca4: r0 = Visibility()
    //     0xc0dca4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc0dca8: mov             x2, x0
    // 0xc0dcac: ldur            x0, [fp, #-0x38]
    // 0xc0dcb0: stur            x2, [fp, #-0x68]
    // 0xc0dcb4: StoreField: r2->field_b = r0
    //     0xc0dcb4: stur            w0, [x2, #0xb]
    // 0xc0dcb8: r0 = Instance_SizedBox
    //     0xc0dcb8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc0dcbc: StoreField: r2->field_f = r0
    //     0xc0dcbc: stur            w0, [x2, #0xf]
    // 0xc0dcc0: ldur            x0, [fp, #-0x70]
    // 0xc0dcc4: StoreField: r2->field_13 = r0
    //     0xc0dcc4: stur            w0, [x2, #0x13]
    // 0xc0dcc8: r0 = false
    //     0xc0dcc8: add             x0, NULL, #0x30  ; false
    // 0xc0dccc: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0dccc: stur            w0, [x2, #0x17]
    // 0xc0dcd0: StoreField: r2->field_1b = r0
    //     0xc0dcd0: stur            w0, [x2, #0x1b]
    // 0xc0dcd4: StoreField: r2->field_1f = r0
    //     0xc0dcd4: stur            w0, [x2, #0x1f]
    // 0xc0dcd8: StoreField: r2->field_23 = r0
    //     0xc0dcd8: stur            w0, [x2, #0x23]
    // 0xc0dcdc: StoreField: r2->field_27 = r0
    //     0xc0dcdc: stur            w0, [x2, #0x27]
    // 0xc0dce0: StoreField: r2->field_2b = r0
    //     0xc0dce0: stur            w0, [x2, #0x2b]
    // 0xc0dce4: r16 = Instance_Color
    //     0xc0dce4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0dce8: r30 = 12.000000
    //     0xc0dce8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0dcec: ldr             lr, [lr, #0x9e8]
    // 0xc0dcf0: stp             lr, x16, [SP]
    // 0xc0dcf4: ldur            x1, [fp, #-0x60]
    // 0xc0dcf8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc0dcf8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc0dcfc: ldr             x4, [x4, #0x9b8]
    // 0xc0dd00: r0 = copyWith()
    //     0xc0dd00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0dd04: stur            x0, [fp, #-0x38]
    // 0xc0dd08: r0 = TextSpan()
    //     0xc0dd08: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xc0dd0c: mov             x3, x0
    // 0xc0dd10: r0 = "• Exchange credit will be added "
    //     0xc0dd10: add             x0, PP, #0x53, lsl #12  ; [pp+0x539c8] "• Exchange credit will be added "
    //     0xc0dd14: ldr             x0, [x0, #0x9c8]
    // 0xc0dd18: stur            x3, [fp, #-0x70]
    // 0xc0dd1c: StoreField: r3->field_b = r0
    //     0xc0dd1c: stur            w0, [x3, #0xb]
    // 0xc0dd20: r0 = Instance__DeferringMouseCursor
    //     0xc0dd20: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xc0dd24: ArrayStore: r3[0] = r0  ; List_4
    //     0xc0dd24: stur            w0, [x3, #0x17]
    // 0xc0dd28: ldur            x1, [fp, #-0x38]
    // 0xc0dd2c: StoreField: r3->field_7 = r1
    //     0xc0dd2c: stur            w1, [x3, #7]
    // 0xc0dd30: r1 = Null
    //     0xc0dd30: mov             x1, NULL
    // 0xc0dd34: r2 = 4
    //     0xc0dd34: movz            x2, #0x4
    // 0xc0dd38: r0 = AllocateArray()
    //     0xc0dd38: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0dd3c: r16 = "worth "
    //     0xc0dd3c: add             x16, PP, #0x53, lsl #12  ; [pp+0x539d0] "worth "
    //     0xc0dd40: ldr             x16, [x16, #0x9d0]
    // 0xc0dd44: StoreField: r0->field_f = r16
    //     0xc0dd44: stur            w16, [x0, #0xf]
    // 0xc0dd48: ldur            x1, [fp, #-8]
    // 0xc0dd4c: LoadField: r2 = r1->field_b
    //     0xc0dd4c: ldur            w2, [x1, #0xb]
    // 0xc0dd50: DecompressPointer r2
    //     0xc0dd50: add             x2, x2, HEAP, lsl #32
    // 0xc0dd54: cmp             w2, NULL
    // 0xc0dd58: b.eq            #0xc0eb0c
    // 0xc0dd5c: LoadField: r3 = r2->field_b
    //     0xc0dd5c: ldur            w3, [x2, #0xb]
    // 0xc0dd60: DecompressPointer r3
    //     0xc0dd60: add             x3, x3, HEAP, lsl #32
    // 0xc0dd64: cmp             w3, NULL
    // 0xc0dd68: b.ne            #0xc0dd74
    // 0xc0dd6c: r2 = Null
    //     0xc0dd6c: mov             x2, NULL
    // 0xc0dd70: b               #0xc0ddac
    // 0xc0dd74: LoadField: r2 = r3->field_b
    //     0xc0dd74: ldur            w2, [x3, #0xb]
    // 0xc0dd78: DecompressPointer r2
    //     0xc0dd78: add             x2, x2, HEAP, lsl #32
    // 0xc0dd7c: cmp             w2, NULL
    // 0xc0dd80: b.ne            #0xc0dd8c
    // 0xc0dd84: r2 = Null
    //     0xc0dd84: mov             x2, NULL
    // 0xc0dd88: b               #0xc0ddac
    // 0xc0dd8c: LoadField: r3 = r2->field_b
    //     0xc0dd8c: ldur            w3, [x2, #0xb]
    // 0xc0dd90: DecompressPointer r3
    //     0xc0dd90: add             x3, x3, HEAP, lsl #32
    // 0xc0dd94: cmp             w3, NULL
    // 0xc0dd98: b.ne            #0xc0dda4
    // 0xc0dd9c: r2 = Null
    //     0xc0dd9c: mov             x2, NULL
    // 0xc0dda0: b               #0xc0ddac
    // 0xc0dda4: LoadField: r2 = r3->field_7
    //     0xc0dda4: ldur            w2, [x3, #7]
    // 0xc0dda8: DecompressPointer r2
    //     0xc0dda8: add             x2, x2, HEAP, lsl #32
    // 0xc0ddac: cmp             w2, NULL
    // 0xc0ddb0: b.ne            #0xc0ddbc
    // 0xc0ddb4: r3 = ""
    //     0xc0ddb4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0ddb8: b               #0xc0ddc0
    // 0xc0ddbc: mov             x3, x2
    // 0xc0ddc0: ldur            x2, [fp, #-0x70]
    // 0xc0ddc4: StoreField: r0->field_13 = r3
    //     0xc0ddc4: stur            w3, [x0, #0x13]
    // 0xc0ddc8: str             x0, [SP]
    // 0xc0ddcc: r0 = _interpolate()
    //     0xc0ddcc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xc0ddd0: stur            x0, [fp, #-0x38]
    // 0xc0ddd4: r16 = Instance_Color
    //     0xc0ddd4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0ddd8: str             x16, [SP]
    // 0xc0dddc: ldur            x1, [fp, #-0x18]
    // 0xc0dde0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xc0dde0: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xc0dde4: ldr             x4, [x4, #0xf40]
    // 0xc0dde8: r0 = copyWith()
    //     0xc0dde8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0ddec: stur            x0, [fp, #-0x78]
    // 0xc0ddf0: r0 = TextSpan()
    //     0xc0ddf0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xc0ddf4: mov             x3, x0
    // 0xc0ddf8: ldur            x0, [fp, #-0x38]
    // 0xc0ddfc: stur            x3, [fp, #-0x80]
    // 0xc0de00: StoreField: r3->field_b = r0
    //     0xc0de00: stur            w0, [x3, #0xb]
    // 0xc0de04: r0 = Instance__DeferringMouseCursor
    //     0xc0de04: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xc0de08: ArrayStore: r3[0] = r0  ; List_4
    //     0xc0de08: stur            w0, [x3, #0x17]
    // 0xc0de0c: ldur            x1, [fp, #-0x78]
    // 0xc0de10: StoreField: r3->field_7 = r1
    //     0xc0de10: stur            w1, [x3, #7]
    // 0xc0de14: r1 = Null
    //     0xc0de14: mov             x1, NULL
    // 0xc0de18: r2 = 4
    //     0xc0de18: movz            x2, #0x4
    // 0xc0de1c: r0 = AllocateArray()
    //     0xc0de1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0de20: mov             x2, x0
    // 0xc0de24: ldur            x0, [fp, #-0x70]
    // 0xc0de28: stur            x2, [fp, #-0x38]
    // 0xc0de2c: StoreField: r2->field_f = r0
    //     0xc0de2c: stur            w0, [x2, #0xf]
    // 0xc0de30: ldur            x0, [fp, #-0x80]
    // 0xc0de34: StoreField: r2->field_13 = r0
    //     0xc0de34: stur            w0, [x2, #0x13]
    // 0xc0de38: r1 = <InlineSpan>
    //     0xc0de38: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xc0de3c: ldr             x1, [x1, #0xe40]
    // 0xc0de40: r0 = AllocateGrowableArray()
    //     0xc0de40: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0de44: mov             x1, x0
    // 0xc0de48: ldur            x0, [fp, #-0x38]
    // 0xc0de4c: stur            x1, [fp, #-0x70]
    // 0xc0de50: StoreField: r1->field_f = r0
    //     0xc0de50: stur            w0, [x1, #0xf]
    // 0xc0de54: r2 = 4
    //     0xc0de54: movz            x2, #0x4
    // 0xc0de58: StoreField: r1->field_b = r2
    //     0xc0de58: stur            w2, [x1, #0xb]
    // 0xc0de5c: r0 = TextSpan()
    //     0xc0de5c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xc0de60: mov             x1, x0
    // 0xc0de64: ldur            x0, [fp, #-0x70]
    // 0xc0de68: stur            x1, [fp, #-0x38]
    // 0xc0de6c: StoreField: r1->field_f = r0
    //     0xc0de6c: stur            w0, [x1, #0xf]
    // 0xc0de70: r0 = Instance__DeferringMouseCursor
    //     0xc0de70: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xc0de74: ArrayStore: r1[0] = r0  ; List_4
    //     0xc0de74: stur            w0, [x1, #0x17]
    // 0xc0de78: r0 = RichText()
    //     0xc0de78: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xc0de7c: mov             x1, x0
    // 0xc0de80: ldur            x2, [fp, #-0x38]
    // 0xc0de84: stur            x0, [fp, #-0x38]
    // 0xc0de88: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc0de88: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc0de8c: r0 = RichText()
    //     0xc0de8c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xc0de90: r0 = Padding()
    //     0xc0de90: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0de94: mov             x2, x0
    // 0xc0de98: r0 = Instance_EdgeInsets
    //     0xc0de98: add             x0, PP, #0x53, lsl #12  ; [pp+0x539d8] Obj!EdgeInsets@d57c51
    //     0xc0de9c: ldr             x0, [x0, #0x9d8]
    // 0xc0dea0: stur            x2, [fp, #-0x70]
    // 0xc0dea4: StoreField: r2->field_f = r0
    //     0xc0dea4: stur            w0, [x2, #0xf]
    // 0xc0dea8: ldur            x0, [fp, #-0x38]
    // 0xc0deac: StoreField: r2->field_b = r0
    //     0xc0deac: stur            w0, [x2, #0xb]
    // 0xc0deb0: r16 = Instance_Color
    //     0xc0deb0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0deb4: r30 = 12.000000
    //     0xc0deb4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0deb8: ldr             lr, [lr, #0x9e8]
    // 0xc0debc: stp             lr, x16, [SP]
    // 0xc0dec0: ldur            x1, [fp, #-0x60]
    // 0xc0dec4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc0dec4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc0dec8: ldr             x4, [x4, #0x9b8]
    // 0xc0decc: r0 = copyWith()
    //     0xc0decc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0ded0: stur            x0, [fp, #-0x38]
    // 0xc0ded4: r0 = TextSpan()
    //     0xc0ded4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xc0ded8: mov             x2, x0
    // 0xc0dedc: r0 = "• Exchange any new item by "
    //     0xc0dedc: add             x0, PP, #0x53, lsl #12  ; [pp+0x539e0] "• Exchange any new item by "
    //     0xc0dee0: ldr             x0, [x0, #0x9e0]
    // 0xc0dee4: stur            x2, [fp, #-0x78]
    // 0xc0dee8: StoreField: r2->field_b = r0
    //     0xc0dee8: stur            w0, [x2, #0xb]
    // 0xc0deec: r0 = Instance__DeferringMouseCursor
    //     0xc0deec: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xc0def0: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0def0: stur            w0, [x2, #0x17]
    // 0xc0def4: ldur            x1, [fp, #-0x38]
    // 0xc0def8: StoreField: r2->field_7 = r1
    //     0xc0def8: stur            w1, [x2, #7]
    // 0xc0defc: ldur            x3, [fp, #-8]
    // 0xc0df00: LoadField: r1 = r3->field_b
    //     0xc0df00: ldur            w1, [x3, #0xb]
    // 0xc0df04: DecompressPointer r1
    //     0xc0df04: add             x1, x1, HEAP, lsl #32
    // 0xc0df08: cmp             w1, NULL
    // 0xc0df0c: b.eq            #0xc0eb10
    // 0xc0df10: LoadField: r4 = r1->field_b
    //     0xc0df10: ldur            w4, [x1, #0xb]
    // 0xc0df14: DecompressPointer r4
    //     0xc0df14: add             x4, x4, HEAP, lsl #32
    // 0xc0df18: cmp             w4, NULL
    // 0xc0df1c: b.ne            #0xc0df28
    // 0xc0df20: r1 = Null
    //     0xc0df20: mov             x1, NULL
    // 0xc0df24: b               #0xc0df4c
    // 0xc0df28: LoadField: r1 = r4->field_b
    //     0xc0df28: ldur            w1, [x4, #0xb]
    // 0xc0df2c: DecompressPointer r1
    //     0xc0df2c: add             x1, x1, HEAP, lsl #32
    // 0xc0df30: cmp             w1, NULL
    // 0xc0df34: b.ne            #0xc0df40
    // 0xc0df38: r1 = Null
    //     0xc0df38: mov             x1, NULL
    // 0xc0df3c: b               #0xc0df4c
    // 0xc0df40: LoadField: r4 = r1->field_13
    //     0xc0df40: ldur            w4, [x1, #0x13]
    // 0xc0df44: DecompressPointer r4
    //     0xc0df44: add             x4, x4, HEAP, lsl #32
    // 0xc0df48: mov             x1, x4
    // 0xc0df4c: cmp             w1, NULL
    // 0xc0df50: b.ne            #0xc0df5c
    // 0xc0df54: r7 = ""
    //     0xc0df54: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0df58: b               #0xc0df60
    // 0xc0df5c: mov             x7, x1
    // 0xc0df60: ldur            x6, [fp, #-0x20]
    // 0xc0df64: ldur            x5, [fp, #-0x68]
    // 0xc0df68: ldur            x4, [fp, #-0x70]
    // 0xc0df6c: stur            x7, [fp, #-0x38]
    // 0xc0df70: r16 = Instance_Color
    //     0xc0df70: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0df74: str             x16, [SP]
    // 0xc0df78: ldur            x1, [fp, #-0x18]
    // 0xc0df7c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xc0df7c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xc0df80: ldr             x4, [x4, #0xf40]
    // 0xc0df84: r0 = copyWith()
    //     0xc0df84: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0df88: stur            x0, [fp, #-0x80]
    // 0xc0df8c: r0 = TextSpan()
    //     0xc0df8c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xc0df90: mov             x3, x0
    // 0xc0df94: ldur            x0, [fp, #-0x38]
    // 0xc0df98: stur            x3, [fp, #-0x88]
    // 0xc0df9c: StoreField: r3->field_b = r0
    //     0xc0df9c: stur            w0, [x3, #0xb]
    // 0xc0dfa0: r0 = Instance__DeferringMouseCursor
    //     0xc0dfa0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xc0dfa4: ArrayStore: r3[0] = r0  ; List_4
    //     0xc0dfa4: stur            w0, [x3, #0x17]
    // 0xc0dfa8: ldur            x1, [fp, #-0x80]
    // 0xc0dfac: StoreField: r3->field_7 = r1
    //     0xc0dfac: stur            w1, [x3, #7]
    // 0xc0dfb0: r1 = Null
    //     0xc0dfb0: mov             x1, NULL
    // 0xc0dfb4: r2 = 4
    //     0xc0dfb4: movz            x2, #0x4
    // 0xc0dfb8: r0 = AllocateArray()
    //     0xc0dfb8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0dfbc: mov             x2, x0
    // 0xc0dfc0: ldur            x0, [fp, #-0x78]
    // 0xc0dfc4: stur            x2, [fp, #-0x38]
    // 0xc0dfc8: StoreField: r2->field_f = r0
    //     0xc0dfc8: stur            w0, [x2, #0xf]
    // 0xc0dfcc: ldur            x0, [fp, #-0x88]
    // 0xc0dfd0: StoreField: r2->field_13 = r0
    //     0xc0dfd0: stur            w0, [x2, #0x13]
    // 0xc0dfd4: r1 = <InlineSpan>
    //     0xc0dfd4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xc0dfd8: ldr             x1, [x1, #0xe40]
    // 0xc0dfdc: r0 = AllocateGrowableArray()
    //     0xc0dfdc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0dfe0: mov             x1, x0
    // 0xc0dfe4: ldur            x0, [fp, #-0x38]
    // 0xc0dfe8: stur            x1, [fp, #-0x78]
    // 0xc0dfec: StoreField: r1->field_f = r0
    //     0xc0dfec: stur            w0, [x1, #0xf]
    // 0xc0dff0: r0 = 4
    //     0xc0dff0: movz            x0, #0x4
    // 0xc0dff4: StoreField: r1->field_b = r0
    //     0xc0dff4: stur            w0, [x1, #0xb]
    // 0xc0dff8: r0 = TextSpan()
    //     0xc0dff8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xc0dffc: mov             x1, x0
    // 0xc0e000: ldur            x0, [fp, #-0x78]
    // 0xc0e004: stur            x1, [fp, #-0x38]
    // 0xc0e008: StoreField: r1->field_f = r0
    //     0xc0e008: stur            w0, [x1, #0xf]
    // 0xc0e00c: r0 = Instance__DeferringMouseCursor
    //     0xc0e00c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xc0e010: ArrayStore: r1[0] = r0  ; List_4
    //     0xc0e010: stur            w0, [x1, #0x17]
    // 0xc0e014: r0 = RichText()
    //     0xc0e014: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xc0e018: mov             x1, x0
    // 0xc0e01c: ldur            x2, [fp, #-0x38]
    // 0xc0e020: stur            x0, [fp, #-0x38]
    // 0xc0e024: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc0e024: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc0e028: r0 = RichText()
    //     0xc0e028: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xc0e02c: r0 = Padding()
    //     0xc0e02c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0e030: mov             x3, x0
    // 0xc0e034: r0 = Instance_EdgeInsets
    //     0xc0e034: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f30] Obj!EdgeInsets@d57b31
    //     0xc0e038: ldr             x0, [x0, #0xf30]
    // 0xc0e03c: stur            x3, [fp, #-0x78]
    // 0xc0e040: StoreField: r3->field_f = r0
    //     0xc0e040: stur            w0, [x3, #0xf]
    // 0xc0e044: ldur            x0, [fp, #-0x38]
    // 0xc0e048: StoreField: r3->field_b = r0
    //     0xc0e048: stur            w0, [x3, #0xb]
    // 0xc0e04c: r1 = Null
    //     0xc0e04c: mov             x1, NULL
    // 0xc0e050: r2 = 8
    //     0xc0e050: movz            x2, #0x8
    // 0xc0e054: r0 = AllocateArray()
    //     0xc0e054: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0e058: mov             x2, x0
    // 0xc0e05c: ldur            x0, [fp, #-0x20]
    // 0xc0e060: stur            x2, [fp, #-0x38]
    // 0xc0e064: StoreField: r2->field_f = r0
    //     0xc0e064: stur            w0, [x2, #0xf]
    // 0xc0e068: ldur            x0, [fp, #-0x68]
    // 0xc0e06c: StoreField: r2->field_13 = r0
    //     0xc0e06c: stur            w0, [x2, #0x13]
    // 0xc0e070: ldur            x0, [fp, #-0x70]
    // 0xc0e074: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0e074: stur            w0, [x2, #0x17]
    // 0xc0e078: ldur            x0, [fp, #-0x78]
    // 0xc0e07c: StoreField: r2->field_1b = r0
    //     0xc0e07c: stur            w0, [x2, #0x1b]
    // 0xc0e080: r1 = <Widget>
    //     0xc0e080: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0e084: r0 = AllocateGrowableArray()
    //     0xc0e084: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0e088: mov             x1, x0
    // 0xc0e08c: ldur            x0, [fp, #-0x38]
    // 0xc0e090: stur            x1, [fp, #-0x20]
    // 0xc0e094: StoreField: r1->field_f = r0
    //     0xc0e094: stur            w0, [x1, #0xf]
    // 0xc0e098: r0 = 8
    //     0xc0e098: movz            x0, #0x8
    // 0xc0e09c: StoreField: r1->field_b = r0
    //     0xc0e09c: stur            w0, [x1, #0xb]
    // 0xc0e0a0: r0 = Column()
    //     0xc0e0a0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0e0a4: mov             x1, x0
    // 0xc0e0a8: r0 = Instance_Axis
    //     0xc0e0a8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0e0ac: stur            x1, [fp, #-0x38]
    // 0xc0e0b0: StoreField: r1->field_f = r0
    //     0xc0e0b0: stur            w0, [x1, #0xf]
    // 0xc0e0b4: r2 = Instance_MainAxisAlignment
    //     0xc0e0b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0e0b8: ldr             x2, [x2, #0xa08]
    // 0xc0e0bc: StoreField: r1->field_13 = r2
    //     0xc0e0bc: stur            w2, [x1, #0x13]
    // 0xc0e0c0: r3 = Instance_MainAxisSize
    //     0xc0e0c0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0e0c4: ldr             x3, [x3, #0xa10]
    // 0xc0e0c8: ArrayStore: r1[0] = r3  ; List_4
    //     0xc0e0c8: stur            w3, [x1, #0x17]
    // 0xc0e0cc: r4 = Instance_CrossAxisAlignment
    //     0xc0e0cc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc0e0d0: ldr             x4, [x4, #0x890]
    // 0xc0e0d4: StoreField: r1->field_1b = r4
    //     0xc0e0d4: stur            w4, [x1, #0x1b]
    // 0xc0e0d8: r5 = Instance_VerticalDirection
    //     0xc0e0d8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0e0dc: ldr             x5, [x5, #0xa20]
    // 0xc0e0e0: StoreField: r1->field_23 = r5
    //     0xc0e0e0: stur            w5, [x1, #0x23]
    // 0xc0e0e4: r6 = Instance_Clip
    //     0xc0e0e4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0e0e8: ldr             x6, [x6, #0x38]
    // 0xc0e0ec: StoreField: r1->field_2b = r6
    //     0xc0e0ec: stur            w6, [x1, #0x2b]
    // 0xc0e0f0: StoreField: r1->field_2f = rZR
    //     0xc0e0f0: stur            xzr, [x1, #0x2f]
    // 0xc0e0f4: ldur            x7, [fp, #-0x20]
    // 0xc0e0f8: StoreField: r1->field_b = r7
    //     0xc0e0f8: stur            w7, [x1, #0xb]
    // 0xc0e0fc: r0 = Container()
    //     0xc0e0fc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc0e100: stur            x0, [fp, #-0x20]
    // 0xc0e104: ldur            x16, [fp, #-0x40]
    // 0xc0e108: ldur            lr, [fp, #-0x38]
    // 0xc0e10c: stp             lr, x16, [SP]
    // 0xc0e110: mov             x1, x0
    // 0xc0e114: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xc0e114: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xc0e118: ldr             x4, [x4, #0x88]
    // 0xc0e11c: r0 = Container()
    //     0xc0e11c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc0e120: r1 = Instance_Color
    //     0xc0e120: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0e124: d0 = 0.020000
    //     0xc0e124: add             x17, PP, #0x53, lsl #12  ; [pp+0x539e8] IMM: double(0.02) from 0x3f947ae147ae147b
    //     0xc0e128: ldr             d0, [x17, #0x9e8]
    // 0xc0e12c: r0 = withOpacity()
    //     0xc0e12c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc0e130: mov             x2, x0
    // 0xc0e134: r1 = Null
    //     0xc0e134: mov             x1, NULL
    // 0xc0e138: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc0e138: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc0e13c: r0 = Border.all()
    //     0xc0e13c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xc0e140: stur            x0, [fp, #-0x38]
    // 0xc0e144: r0 = BoxDecoration()
    //     0xc0e144: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc0e148: mov             x2, x0
    // 0xc0e14c: ldur            x0, [fp, #-0x38]
    // 0xc0e150: stur            x2, [fp, #-0x40]
    // 0xc0e154: StoreField: r2->field_f = r0
    //     0xc0e154: stur            w0, [x2, #0xf]
    // 0xc0e158: r0 = Instance_BoxShape
    //     0xc0e158: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0e15c: ldr             x0, [x0, #0x80]
    // 0xc0e160: StoreField: r2->field_23 = r0
    //     0xc0e160: stur            w0, [x2, #0x23]
    // 0xc0e164: ldur            x0, [fp, #-8]
    // 0xc0e168: LoadField: r1 = r0->field_b
    //     0xc0e168: ldur            w1, [x0, #0xb]
    // 0xc0e16c: DecompressPointer r1
    //     0xc0e16c: add             x1, x1, HEAP, lsl #32
    // 0xc0e170: cmp             w1, NULL
    // 0xc0e174: b.eq            #0xc0eb14
    // 0xc0e178: LoadField: r3 = r1->field_b
    //     0xc0e178: ldur            w3, [x1, #0xb]
    // 0xc0e17c: DecompressPointer r3
    //     0xc0e17c: add             x3, x3, HEAP, lsl #32
    // 0xc0e180: cmp             w3, NULL
    // 0xc0e184: b.ne            #0xc0e190
    // 0xc0e188: r1 = Null
    //     0xc0e188: mov             x1, NULL
    // 0xc0e18c: b               #0xc0e1e4
    // 0xc0e190: LoadField: r1 = r3->field_b
    //     0xc0e190: ldur            w1, [x3, #0xb]
    // 0xc0e194: DecompressPointer r1
    //     0xc0e194: add             x1, x1, HEAP, lsl #32
    // 0xc0e198: cmp             w1, NULL
    // 0xc0e19c: b.ne            #0xc0e1a8
    // 0xc0e1a0: r1 = Null
    //     0xc0e1a0: mov             x1, NULL
    // 0xc0e1a4: b               #0xc0e1e4
    // 0xc0e1a8: LoadField: r3 = r1->field_7
    //     0xc0e1a8: ldur            w3, [x1, #7]
    // 0xc0e1ac: DecompressPointer r3
    //     0xc0e1ac: add             x3, x3, HEAP, lsl #32
    // 0xc0e1b0: cmp             w3, NULL
    // 0xc0e1b4: b.ne            #0xc0e1c0
    // 0xc0e1b8: r1 = Null
    //     0xc0e1b8: mov             x1, NULL
    // 0xc0e1bc: b               #0xc0e1e4
    // 0xc0e1c0: LoadField: r1 = r3->field_1f
    //     0xc0e1c0: ldur            w1, [x3, #0x1f]
    // 0xc0e1c4: DecompressPointer r1
    //     0xc0e1c4: add             x1, x1, HEAP, lsl #32
    // 0xc0e1c8: cmp             w1, NULL
    // 0xc0e1cc: b.ne            #0xc0e1d8
    // 0xc0e1d0: r1 = Null
    //     0xc0e1d0: mov             x1, NULL
    // 0xc0e1d4: b               #0xc0e1e4
    // 0xc0e1d8: LoadField: r3 = r1->field_7
    //     0xc0e1d8: ldur            w3, [x1, #7]
    // 0xc0e1dc: DecompressPointer r3
    //     0xc0e1dc: add             x3, x3, HEAP, lsl #32
    // 0xc0e1e0: mov             x1, x3
    // 0xc0e1e4: cmp             w1, NULL
    // 0xc0e1e8: b.ne            #0xc0e1f4
    // 0xc0e1ec: r3 = ""
    //     0xc0e1ec: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0e1f0: b               #0xc0e1f8
    // 0xc0e1f4: mov             x3, x1
    // 0xc0e1f8: stur            x3, [fp, #-0x38]
    // 0xc0e1fc: r16 = Instance_Color
    //     0xc0e1fc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0e200: r30 = 12.000000
    //     0xc0e200: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0e204: ldr             lr, [lr, #0x9e8]
    // 0xc0e208: stp             lr, x16, [SP]
    // 0xc0e20c: ldur            x1, [fp, #-0x18]
    // 0xc0e210: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc0e210: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc0e214: ldr             x4, [x4, #0x9b8]
    // 0xc0e218: r0 = copyWith()
    //     0xc0e218: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0e21c: stur            x0, [fp, #-0x68]
    // 0xc0e220: r0 = Text()
    //     0xc0e220: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0e224: mov             x2, x0
    // 0xc0e228: ldur            x0, [fp, #-0x38]
    // 0xc0e22c: stur            x2, [fp, #-0x70]
    // 0xc0e230: StoreField: r2->field_b = r0
    //     0xc0e230: stur            w0, [x2, #0xb]
    // 0xc0e234: ldur            x0, [fp, #-0x68]
    // 0xc0e238: StoreField: r2->field_13 = r0
    //     0xc0e238: stur            w0, [x2, #0x13]
    // 0xc0e23c: ldur            x0, [fp, #-8]
    // 0xc0e240: LoadField: r1 = r0->field_b
    //     0xc0e240: ldur            w1, [x0, #0xb]
    // 0xc0e244: DecompressPointer r1
    //     0xc0e244: add             x1, x1, HEAP, lsl #32
    // 0xc0e248: cmp             w1, NULL
    // 0xc0e24c: b.eq            #0xc0eb18
    // 0xc0e250: LoadField: r3 = r1->field_b
    //     0xc0e250: ldur            w3, [x1, #0xb]
    // 0xc0e254: DecompressPointer r3
    //     0xc0e254: add             x3, x3, HEAP, lsl #32
    // 0xc0e258: cmp             w3, NULL
    // 0xc0e25c: b.ne            #0xc0e268
    // 0xc0e260: r1 = Null
    //     0xc0e260: mov             x1, NULL
    // 0xc0e264: b               #0xc0e2bc
    // 0xc0e268: LoadField: r1 = r3->field_b
    //     0xc0e268: ldur            w1, [x3, #0xb]
    // 0xc0e26c: DecompressPointer r1
    //     0xc0e26c: add             x1, x1, HEAP, lsl #32
    // 0xc0e270: cmp             w1, NULL
    // 0xc0e274: b.ne            #0xc0e280
    // 0xc0e278: r1 = Null
    //     0xc0e278: mov             x1, NULL
    // 0xc0e27c: b               #0xc0e2bc
    // 0xc0e280: LoadField: r3 = r1->field_7
    //     0xc0e280: ldur            w3, [x1, #7]
    // 0xc0e284: DecompressPointer r3
    //     0xc0e284: add             x3, x3, HEAP, lsl #32
    // 0xc0e288: cmp             w3, NULL
    // 0xc0e28c: b.ne            #0xc0e298
    // 0xc0e290: r1 = Null
    //     0xc0e290: mov             x1, NULL
    // 0xc0e294: b               #0xc0e2bc
    // 0xc0e298: LoadField: r1 = r3->field_1f
    //     0xc0e298: ldur            w1, [x3, #0x1f]
    // 0xc0e29c: DecompressPointer r1
    //     0xc0e29c: add             x1, x1, HEAP, lsl #32
    // 0xc0e2a0: cmp             w1, NULL
    // 0xc0e2a4: b.ne            #0xc0e2b0
    // 0xc0e2a8: r1 = Null
    //     0xc0e2a8: mov             x1, NULL
    // 0xc0e2ac: b               #0xc0e2bc
    // 0xc0e2b0: LoadField: r3 = r1->field_b
    //     0xc0e2b0: ldur            w3, [x1, #0xb]
    // 0xc0e2b4: DecompressPointer r3
    //     0xc0e2b4: add             x3, x3, HEAP, lsl #32
    // 0xc0e2b8: mov             x1, x3
    // 0xc0e2bc: cmp             w1, NULL
    // 0xc0e2c0: b.ne            #0xc0e2cc
    // 0xc0e2c4: r3 = ""
    //     0xc0e2c4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0e2c8: b               #0xc0e2d0
    // 0xc0e2cc: mov             x3, x1
    // 0xc0e2d0: stur            x3, [fp, #-0x38]
    // 0xc0e2d4: r1 = Instance_Color
    //     0xc0e2d4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0e2d8: d0 = 0.700000
    //     0xc0e2d8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc0e2dc: ldr             d0, [x17, #0xf48]
    // 0xc0e2e0: r0 = withOpacity()
    //     0xc0e2e0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc0e2e4: r16 = 12.000000
    //     0xc0e2e4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0e2e8: ldr             x16, [x16, #0x9e8]
    // 0xc0e2ec: stp             x16, x0, [SP]
    // 0xc0e2f0: ldur            x1, [fp, #-0x60]
    // 0xc0e2f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc0e2f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc0e2f8: ldr             x4, [x4, #0x9b8]
    // 0xc0e2fc: r0 = copyWith()
    //     0xc0e2fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0e300: stur            x0, [fp, #-0x60]
    // 0xc0e304: r0 = Text()
    //     0xc0e304: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0e308: mov             x3, x0
    // 0xc0e30c: ldur            x0, [fp, #-0x38]
    // 0xc0e310: stur            x3, [fp, #-0x68]
    // 0xc0e314: StoreField: r3->field_b = r0
    //     0xc0e314: stur            w0, [x3, #0xb]
    // 0xc0e318: ldur            x0, [fp, #-0x60]
    // 0xc0e31c: StoreField: r3->field_13 = r0
    //     0xc0e31c: stur            w0, [x3, #0x13]
    // 0xc0e320: r1 = Null
    //     0xc0e320: mov             x1, NULL
    // 0xc0e324: r2 = 6
    //     0xc0e324: movz            x2, #0x6
    // 0xc0e328: r0 = AllocateArray()
    //     0xc0e328: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0e32c: mov             x2, x0
    // 0xc0e330: ldur            x0, [fp, #-0x70]
    // 0xc0e334: stur            x2, [fp, #-0x38]
    // 0xc0e338: StoreField: r2->field_f = r0
    //     0xc0e338: stur            w0, [x2, #0xf]
    // 0xc0e33c: r16 = Instance_SizedBox
    //     0xc0e33c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xc0e340: ldr             x16, [x16, #0xc70]
    // 0xc0e344: StoreField: r2->field_13 = r16
    //     0xc0e344: stur            w16, [x2, #0x13]
    // 0xc0e348: ldur            x0, [fp, #-0x68]
    // 0xc0e34c: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0e34c: stur            w0, [x2, #0x17]
    // 0xc0e350: r1 = <Widget>
    //     0xc0e350: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0e354: r0 = AllocateGrowableArray()
    //     0xc0e354: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0e358: mov             x1, x0
    // 0xc0e35c: ldur            x0, [fp, #-0x38]
    // 0xc0e360: stur            x1, [fp, #-0x60]
    // 0xc0e364: StoreField: r1->field_f = r0
    //     0xc0e364: stur            w0, [x1, #0xf]
    // 0xc0e368: r2 = 6
    //     0xc0e368: movz            x2, #0x6
    // 0xc0e36c: StoreField: r1->field_b = r2
    //     0xc0e36c: stur            w2, [x1, #0xb]
    // 0xc0e370: r0 = Column()
    //     0xc0e370: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0e374: mov             x1, x0
    // 0xc0e378: r0 = Instance_Axis
    //     0xc0e378: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0e37c: stur            x1, [fp, #-0x38]
    // 0xc0e380: StoreField: r1->field_f = r0
    //     0xc0e380: stur            w0, [x1, #0xf]
    // 0xc0e384: r2 = Instance_MainAxisAlignment
    //     0xc0e384: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0e388: ldr             x2, [x2, #0xa08]
    // 0xc0e38c: StoreField: r1->field_13 = r2
    //     0xc0e38c: stur            w2, [x1, #0x13]
    // 0xc0e390: r3 = Instance_MainAxisSize
    //     0xc0e390: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0e394: ldr             x3, [x3, #0xa10]
    // 0xc0e398: ArrayStore: r1[0] = r3  ; List_4
    //     0xc0e398: stur            w3, [x1, #0x17]
    // 0xc0e39c: r4 = Instance_CrossAxisAlignment
    //     0xc0e39c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc0e3a0: ldr             x4, [x4, #0x890]
    // 0xc0e3a4: StoreField: r1->field_1b = r4
    //     0xc0e3a4: stur            w4, [x1, #0x1b]
    // 0xc0e3a8: r5 = Instance_VerticalDirection
    //     0xc0e3a8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0e3ac: ldr             x5, [x5, #0xa20]
    // 0xc0e3b0: StoreField: r1->field_23 = r5
    //     0xc0e3b0: stur            w5, [x1, #0x23]
    // 0xc0e3b4: r6 = Instance_Clip
    //     0xc0e3b4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0e3b8: ldr             x6, [x6, #0x38]
    // 0xc0e3bc: StoreField: r1->field_2b = r6
    //     0xc0e3bc: stur            w6, [x1, #0x2b]
    // 0xc0e3c0: StoreField: r1->field_2f = rZR
    //     0xc0e3c0: stur            xzr, [x1, #0x2f]
    // 0xc0e3c4: ldur            x7, [fp, #-0x60]
    // 0xc0e3c8: StoreField: r1->field_b = r7
    //     0xc0e3c8: stur            w7, [x1, #0xb]
    // 0xc0e3cc: r0 = Padding()
    //     0xc0e3cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0e3d0: mov             x1, x0
    // 0xc0e3d4: r0 = Instance_EdgeInsets
    //     0xc0e3d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xc0e3d8: ldr             x0, [x0, #0x1f0]
    // 0xc0e3dc: stur            x1, [fp, #-0x60]
    // 0xc0e3e0: StoreField: r1->field_f = r0
    //     0xc0e3e0: stur            w0, [x1, #0xf]
    // 0xc0e3e4: ldur            x0, [fp, #-0x38]
    // 0xc0e3e8: StoreField: r1->field_b = r0
    //     0xc0e3e8: stur            w0, [x1, #0xb]
    // 0xc0e3ec: r0 = Container()
    //     0xc0e3ec: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc0e3f0: stur            x0, [fp, #-0x38]
    // 0xc0e3f4: r16 = inf
    //     0xc0e3f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xc0e3f8: ldr             x16, [x16, #0x9f8]
    // 0xc0e3fc: ldur            lr, [fp, #-0x40]
    // 0xc0e400: stp             lr, x16, [SP, #8]
    // 0xc0e404: ldur            x16, [fp, #-0x60]
    // 0xc0e408: str             x16, [SP]
    // 0xc0e40c: mov             x1, x0
    // 0xc0e410: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, width, 0x1, null]
    //     0xc0e410: add             x4, PP, #0x33, lsl #12  ; [pp+0x33830] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "width", 0x1, Null]
    //     0xc0e414: ldr             x4, [x4, #0x830]
    // 0xc0e418: r0 = Container()
    //     0xc0e418: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc0e41c: r16 = <EdgeInsets>
    //     0xc0e41c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xc0e420: ldr             x16, [x16, #0xda0]
    // 0xc0e424: r30 = Instance_EdgeInsets
    //     0xc0e424: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xc0e428: ldr             lr, [lr, #0x1f0]
    // 0xc0e42c: stp             lr, x16, [SP]
    // 0xc0e430: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc0e430: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc0e434: r0 = all()
    //     0xc0e434: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc0e438: stur            x0, [fp, #-0x40]
    // 0xc0e43c: r16 = <Color>
    //     0xc0e43c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xc0e440: ldr             x16, [x16, #0xf80]
    // 0xc0e444: r30 = Instance_Color
    //     0xc0e444: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc0e448: stp             lr, x16, [SP]
    // 0xc0e44c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc0e44c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc0e450: r0 = all()
    //     0xc0e450: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc0e454: r1 = Instance_Color
    //     0xc0e454: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0e458: d0 = 0.080000
    //     0xc0e458: add             x17, PP, #0x27, lsl #12  ; [pp+0x27798] IMM: double(0.08) from 0x3fb47ae147ae147b
    //     0xc0e45c: ldr             d0, [x17, #0x798]
    // 0xc0e460: stur            x0, [fp, #-0x60]
    // 0xc0e464: r0 = withOpacity()
    //     0xc0e464: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc0e468: stur            x0, [fp, #-0x68]
    // 0xc0e46c: r0 = BorderSide()
    //     0xc0e46c: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xc0e470: mov             x1, x0
    // 0xc0e474: ldur            x0, [fp, #-0x68]
    // 0xc0e478: stur            x1, [fp, #-0x70]
    // 0xc0e47c: StoreField: r1->field_7 = r0
    //     0xc0e47c: stur            w0, [x1, #7]
    // 0xc0e480: d0 = 1.000000
    //     0xc0e480: fmov            d0, #1.00000000
    // 0xc0e484: StoreField: r1->field_b = d0
    //     0xc0e484: stur            d0, [x1, #0xb]
    // 0xc0e488: r0 = Instance_BorderStyle
    //     0xc0e488: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xc0e48c: ldr             x0, [x0, #0xf68]
    // 0xc0e490: StoreField: r1->field_13 = r0
    //     0xc0e490: stur            w0, [x1, #0x13]
    // 0xc0e494: d1 = -1.000000
    //     0xc0e494: fmov            d1, #-1.00000000
    // 0xc0e498: ArrayStore: r1[0] = d1  ; List_8
    //     0xc0e498: stur            d1, [x1, #0x17]
    // 0xc0e49c: r0 = RoundedRectangleBorder()
    //     0xc0e49c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xc0e4a0: mov             x1, x0
    // 0xc0e4a4: r0 = Instance_BorderRadius
    //     0xc0e4a4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xc0e4a8: ldr             x0, [x0, #0xf70]
    // 0xc0e4ac: StoreField: r1->field_b = r0
    //     0xc0e4ac: stur            w0, [x1, #0xb]
    // 0xc0e4b0: ldur            x2, [fp, #-0x70]
    // 0xc0e4b4: StoreField: r1->field_7 = r2
    //     0xc0e4b4: stur            w2, [x1, #7]
    // 0xc0e4b8: r16 = <RoundedRectangleBorder>
    //     0xc0e4b8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xc0e4bc: ldr             x16, [x16, #0xf78]
    // 0xc0e4c0: stp             x1, x16, [SP]
    // 0xc0e4c4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc0e4c4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc0e4c8: r0 = all()
    //     0xc0e4c8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc0e4cc: stur            x0, [fp, #-0x68]
    // 0xc0e4d0: r0 = ButtonStyle()
    //     0xc0e4d0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xc0e4d4: mov             x1, x0
    // 0xc0e4d8: ldur            x0, [fp, #-0x60]
    // 0xc0e4dc: stur            x1, [fp, #-0x70]
    // 0xc0e4e0: StoreField: r1->field_b = r0
    //     0xc0e4e0: stur            w0, [x1, #0xb]
    // 0xc0e4e4: ldur            x0, [fp, #-0x40]
    // 0xc0e4e8: StoreField: r1->field_23 = r0
    //     0xc0e4e8: stur            w0, [x1, #0x23]
    // 0xc0e4ec: ldur            x0, [fp, #-0x68]
    // 0xc0e4f0: StoreField: r1->field_43 = r0
    //     0xc0e4f0: stur            w0, [x1, #0x43]
    // 0xc0e4f4: r0 = TextButtonThemeData()
    //     0xc0e4f4: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xc0e4f8: mov             x2, x0
    // 0xc0e4fc: ldur            x0, [fp, #-0x70]
    // 0xc0e500: stur            x2, [fp, #-0x40]
    // 0xc0e504: StoreField: r2->field_7 = r0
    //     0xc0e504: stur            w0, [x2, #7]
    // 0xc0e508: r16 = 14.000000
    //     0xc0e508: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc0e50c: ldr             x16, [x16, #0x1d8]
    // 0xc0e510: r30 = Instance_Color
    //     0xc0e510: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0e514: stp             lr, x16, [SP]
    // 0xc0e518: ldur            x1, [fp, #-0x18]
    // 0xc0e51c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc0e51c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc0e520: ldr             x4, [x4, #0xaa0]
    // 0xc0e524: r0 = copyWith()
    //     0xc0e524: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0e528: stur            x0, [fp, #-0x60]
    // 0xc0e52c: r0 = Text()
    //     0xc0e52c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0e530: mov             x3, x0
    // 0xc0e534: r0 = "NO, BUY SEPARATELY"
    //     0xc0e534: add             x0, PP, #0x53, lsl #12  ; [pp+0x539f0] "NO, BUY SEPARATELY"
    //     0xc0e538: ldr             x0, [x0, #0x9f0]
    // 0xc0e53c: stur            x3, [fp, #-0x68]
    // 0xc0e540: StoreField: r3->field_b = r0
    //     0xc0e540: stur            w0, [x3, #0xb]
    // 0xc0e544: ldur            x0, [fp, #-0x60]
    // 0xc0e548: StoreField: r3->field_13 = r0
    //     0xc0e548: stur            w0, [x3, #0x13]
    // 0xc0e54c: ldur            x2, [fp, #-0x10]
    // 0xc0e550: r1 = Function '<anonymous closure>':.
    //     0xc0e550: add             x1, PP, #0x53, lsl #12  ; [pp+0x539f8] AnonymousClosure: (0xc0ebd4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/single_exchange_product_bottom_sheet.dart] _SingleExchangeProductBottomSheetState::build (0xc0cc84)
    //     0xc0e554: ldr             x1, [x1, #0x9f8]
    // 0xc0e558: r0 = AllocateClosure()
    //     0xc0e558: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0e55c: stur            x0, [fp, #-0x60]
    // 0xc0e560: r0 = TextButton()
    //     0xc0e560: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xc0e564: mov             x1, x0
    // 0xc0e568: ldur            x0, [fp, #-0x60]
    // 0xc0e56c: stur            x1, [fp, #-0x70]
    // 0xc0e570: StoreField: r1->field_b = r0
    //     0xc0e570: stur            w0, [x1, #0xb]
    // 0xc0e574: r0 = false
    //     0xc0e574: add             x0, NULL, #0x30  ; false
    // 0xc0e578: StoreField: r1->field_27 = r0
    //     0xc0e578: stur            w0, [x1, #0x27]
    // 0xc0e57c: r2 = true
    //     0xc0e57c: add             x2, NULL, #0x20  ; true
    // 0xc0e580: StoreField: r1->field_2f = r2
    //     0xc0e580: stur            w2, [x1, #0x2f]
    // 0xc0e584: ldur            x3, [fp, #-0x68]
    // 0xc0e588: StoreField: r1->field_37 = r3
    //     0xc0e588: stur            w3, [x1, #0x37]
    // 0xc0e58c: r0 = TextButtonTheme()
    //     0xc0e58c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xc0e590: mov             x2, x0
    // 0xc0e594: ldur            x0, [fp, #-0x40]
    // 0xc0e598: stur            x2, [fp, #-0x60]
    // 0xc0e59c: StoreField: r2->field_f = r0
    //     0xc0e59c: stur            w0, [x2, #0xf]
    // 0xc0e5a0: ldur            x0, [fp, #-0x70]
    // 0xc0e5a4: StoreField: r2->field_b = r0
    //     0xc0e5a4: stur            w0, [x2, #0xb]
    // 0xc0e5a8: r1 = <FlexParentData>
    //     0xc0e5a8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xc0e5ac: ldr             x1, [x1, #0xe00]
    // 0xc0e5b0: r0 = Expanded()
    //     0xc0e5b0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xc0e5b4: mov             x1, x0
    // 0xc0e5b8: r0 = 1
    //     0xc0e5b8: movz            x0, #0x1
    // 0xc0e5bc: stur            x1, [fp, #-0x40]
    // 0xc0e5c0: StoreField: r1->field_13 = r0
    //     0xc0e5c0: stur            x0, [x1, #0x13]
    // 0xc0e5c4: r2 = Instance_FlexFit
    //     0xc0e5c4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xc0e5c8: ldr             x2, [x2, #0xe08]
    // 0xc0e5cc: StoreField: r1->field_1b = r2
    //     0xc0e5cc: stur            w2, [x1, #0x1b]
    // 0xc0e5d0: ldur            x3, [fp, #-0x60]
    // 0xc0e5d4: StoreField: r1->field_b = r3
    //     0xc0e5d4: stur            w3, [x1, #0xb]
    // 0xc0e5d8: r16 = <EdgeInsets>
    //     0xc0e5d8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xc0e5dc: ldr             x16, [x16, #0xda0]
    // 0xc0e5e0: r30 = Instance_EdgeInsets
    //     0xc0e5e0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xc0e5e4: ldr             lr, [lr, #0x1f0]
    // 0xc0e5e8: stp             lr, x16, [SP]
    // 0xc0e5ec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc0e5ec: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc0e5f0: r0 = all()
    //     0xc0e5f0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc0e5f4: mov             x1, x0
    // 0xc0e5f8: ldur            x0, [fp, #-8]
    // 0xc0e5fc: stur            x1, [fp, #-0x60]
    // 0xc0e600: LoadField: r2 = r0->field_b
    //     0xc0e600: ldur            w2, [x0, #0xb]
    // 0xc0e604: DecompressPointer r2
    //     0xc0e604: add             x2, x2, HEAP, lsl #32
    // 0xc0e608: cmp             w2, NULL
    // 0xc0e60c: b.eq            #0xc0eb1c
    // 0xc0e610: LoadField: r0 = r2->field_2f
    //     0xc0e610: ldur            w0, [x2, #0x2f]
    // 0xc0e614: DecompressPointer r0
    //     0xc0e614: add             x0, x0, HEAP, lsl #32
    // 0xc0e618: LoadField: r2 = r0->field_3f
    //     0xc0e618: ldur            w2, [x0, #0x3f]
    // 0xc0e61c: DecompressPointer r2
    //     0xc0e61c: add             x2, x2, HEAP, lsl #32
    // 0xc0e620: cmp             w2, NULL
    // 0xc0e624: b.ne            #0xc0e630
    // 0xc0e628: r0 = Null
    //     0xc0e628: mov             x0, NULL
    // 0xc0e62c: b               #0xc0e654
    // 0xc0e630: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xc0e630: ldur            w0, [x2, #0x17]
    // 0xc0e634: DecompressPointer r0
    //     0xc0e634: add             x0, x0, HEAP, lsl #32
    // 0xc0e638: cmp             w0, NULL
    // 0xc0e63c: b.ne            #0xc0e648
    // 0xc0e640: r0 = Null
    //     0xc0e640: mov             x0, NULL
    // 0xc0e644: b               #0xc0e654
    // 0xc0e648: LoadField: r3 = r0->field_7
    //     0xc0e648: ldur            w3, [x0, #7]
    // 0xc0e64c: DecompressPointer r3
    //     0xc0e64c: add             x3, x3, HEAP, lsl #32
    // 0xc0e650: mov             x0, x3
    // 0xc0e654: cmp             w0, NULL
    // 0xc0e658: b.ne            #0xc0e664
    // 0xc0e65c: r0 = 0
    //     0xc0e65c: movz            x0, #0
    // 0xc0e660: b               #0xc0e674
    // 0xc0e664: r3 = LoadInt32Instr(r0)
    //     0xc0e664: sbfx            x3, x0, #1, #0x1f
    //     0xc0e668: tbz             w0, #0, #0xc0e670
    //     0xc0e66c: ldur            x3, [x0, #7]
    // 0xc0e670: mov             x0, x3
    // 0xc0e674: stur            x0, [fp, #-0x58]
    // 0xc0e678: cmp             w2, NULL
    // 0xc0e67c: b.ne            #0xc0e688
    // 0xc0e680: r3 = Null
    //     0xc0e680: mov             x3, NULL
    // 0xc0e684: b               #0xc0e6ac
    // 0xc0e688: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xc0e688: ldur            w3, [x2, #0x17]
    // 0xc0e68c: DecompressPointer r3
    //     0xc0e68c: add             x3, x3, HEAP, lsl #32
    // 0xc0e690: cmp             w3, NULL
    // 0xc0e694: b.ne            #0xc0e6a0
    // 0xc0e698: r3 = Null
    //     0xc0e698: mov             x3, NULL
    // 0xc0e69c: b               #0xc0e6ac
    // 0xc0e6a0: LoadField: r4 = r3->field_b
    //     0xc0e6a0: ldur            w4, [x3, #0xb]
    // 0xc0e6a4: DecompressPointer r4
    //     0xc0e6a4: add             x4, x4, HEAP, lsl #32
    // 0xc0e6a8: mov             x3, x4
    // 0xc0e6ac: cmp             w3, NULL
    // 0xc0e6b0: b.ne            #0xc0e6bc
    // 0xc0e6b4: r3 = 0
    //     0xc0e6b4: movz            x3, #0
    // 0xc0e6b8: b               #0xc0e6cc
    // 0xc0e6bc: r4 = LoadInt32Instr(r3)
    //     0xc0e6bc: sbfx            x4, x3, #1, #0x1f
    //     0xc0e6c0: tbz             w3, #0, #0xc0e6c8
    //     0xc0e6c4: ldur            x4, [x3, #7]
    // 0xc0e6c8: mov             x3, x4
    // 0xc0e6cc: stur            x3, [fp, #-0x50]
    // 0xc0e6d0: cmp             w2, NULL
    // 0xc0e6d4: b.ne            #0xc0e6e0
    // 0xc0e6d8: r2 = Null
    //     0xc0e6d8: mov             x2, NULL
    // 0xc0e6dc: b               #0xc0e700
    // 0xc0e6e0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xc0e6e0: ldur            w4, [x2, #0x17]
    // 0xc0e6e4: DecompressPointer r4
    //     0xc0e6e4: add             x4, x4, HEAP, lsl #32
    // 0xc0e6e8: cmp             w4, NULL
    // 0xc0e6ec: b.ne            #0xc0e6f8
    // 0xc0e6f0: r2 = Null
    //     0xc0e6f0: mov             x2, NULL
    // 0xc0e6f4: b               #0xc0e700
    // 0xc0e6f8: LoadField: r2 = r4->field_f
    //     0xc0e6f8: ldur            w2, [x4, #0xf]
    // 0xc0e6fc: DecompressPointer r2
    //     0xc0e6fc: add             x2, x2, HEAP, lsl #32
    // 0xc0e700: cmp             w2, NULL
    // 0xc0e704: b.ne            #0xc0e710
    // 0xc0e708: r8 = 0
    //     0xc0e708: movz            x8, #0
    // 0xc0e70c: b               #0xc0e720
    // 0xc0e710: r4 = LoadInt32Instr(r2)
    //     0xc0e710: sbfx            x4, x2, #1, #0x1f
    //     0xc0e714: tbz             w2, #0, #0xc0e71c
    //     0xc0e718: ldur            x4, [x2, #7]
    // 0xc0e71c: mov             x8, x4
    // 0xc0e720: ldur            x6, [fp, #-0x30]
    // 0xc0e724: ldur            x5, [fp, #-0x20]
    // 0xc0e728: ldur            x4, [fp, #-0x38]
    // 0xc0e72c: ldur            x2, [fp, #-0x40]
    // 0xc0e730: ldur            x7, [fp, #-0x28]
    // 0xc0e734: stur            x8, [fp, #-0x48]
    // 0xc0e738: r0 = Color()
    //     0xc0e738: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xc0e73c: mov             x1, x0
    // 0xc0e740: r0 = Instance_ColorSpace
    //     0xc0e740: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xc0e744: StoreField: r1->field_27 = r0
    //     0xc0e744: stur            w0, [x1, #0x27]
    // 0xc0e748: d0 = 1.000000
    //     0xc0e748: fmov            d0, #1.00000000
    // 0xc0e74c: StoreField: r1->field_7 = d0
    //     0xc0e74c: stur            d0, [x1, #7]
    // 0xc0e750: ldur            x0, [fp, #-0x58]
    // 0xc0e754: ubfx            x0, x0, #0, #0x20
    // 0xc0e758: and             w2, w0, #0xff
    // 0xc0e75c: ubfx            x2, x2, #0, #0x20
    // 0xc0e760: scvtf           d1, x2
    // 0xc0e764: d2 = 255.000000
    //     0xc0e764: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xc0e768: fdiv            d3, d1, d2
    // 0xc0e76c: StoreField: r1->field_f = d3
    //     0xc0e76c: stur            d3, [x1, #0xf]
    // 0xc0e770: ldur            x0, [fp, #-0x50]
    // 0xc0e774: ubfx            x0, x0, #0, #0x20
    // 0xc0e778: and             w2, w0, #0xff
    // 0xc0e77c: ubfx            x2, x2, #0, #0x20
    // 0xc0e780: scvtf           d1, x2
    // 0xc0e784: fdiv            d3, d1, d2
    // 0xc0e788: ArrayStore: r1[0] = d3  ; List_8
    //     0xc0e788: stur            d3, [x1, #0x17]
    // 0xc0e78c: ldur            x0, [fp, #-0x48]
    // 0xc0e790: ubfx            x0, x0, #0, #0x20
    // 0xc0e794: and             w2, w0, #0xff
    // 0xc0e798: ubfx            x2, x2, #0, #0x20
    // 0xc0e79c: scvtf           d1, x2
    // 0xc0e7a0: fdiv            d3, d1, d2
    // 0xc0e7a4: StoreField: r1->field_1f = d3
    //     0xc0e7a4: stur            d3, [x1, #0x1f]
    // 0xc0e7a8: r16 = <Color>
    //     0xc0e7a8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xc0e7ac: ldr             x16, [x16, #0xf80]
    // 0xc0e7b0: stp             x1, x16, [SP]
    // 0xc0e7b4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc0e7b4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc0e7b8: r0 = all()
    //     0xc0e7b8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc0e7bc: mov             x1, x0
    // 0xc0e7c0: ldur            x0, [fp, #-0x28]
    // 0xc0e7c4: stur            x1, [fp, #-0x68]
    // 0xc0e7c8: LoadField: r2 = r0->field_5b
    //     0xc0e7c8: ldur            w2, [x0, #0x5b]
    // 0xc0e7cc: DecompressPointer r2
    //     0xc0e7cc: add             x2, x2, HEAP, lsl #32
    // 0xc0e7d0: stur            x2, [fp, #-8]
    // 0xc0e7d4: r0 = BorderSide()
    //     0xc0e7d4: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xc0e7d8: mov             x1, x0
    // 0xc0e7dc: ldur            x0, [fp, #-8]
    // 0xc0e7e0: stur            x1, [fp, #-0x28]
    // 0xc0e7e4: StoreField: r1->field_7 = r0
    //     0xc0e7e4: stur            w0, [x1, #7]
    // 0xc0e7e8: d0 = 1.000000
    //     0xc0e7e8: fmov            d0, #1.00000000
    // 0xc0e7ec: StoreField: r1->field_b = d0
    //     0xc0e7ec: stur            d0, [x1, #0xb]
    // 0xc0e7f0: r0 = Instance_BorderStyle
    //     0xc0e7f0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xc0e7f4: ldr             x0, [x0, #0xf68]
    // 0xc0e7f8: StoreField: r1->field_13 = r0
    //     0xc0e7f8: stur            w0, [x1, #0x13]
    // 0xc0e7fc: d0 = -1.000000
    //     0xc0e7fc: fmov            d0, #-1.00000000
    // 0xc0e800: ArrayStore: r1[0] = d0  ; List_8
    //     0xc0e800: stur            d0, [x1, #0x17]
    // 0xc0e804: r0 = RoundedRectangleBorder()
    //     0xc0e804: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xc0e808: mov             x1, x0
    // 0xc0e80c: r0 = Instance_BorderRadius
    //     0xc0e80c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xc0e810: ldr             x0, [x0, #0xf70]
    // 0xc0e814: StoreField: r1->field_b = r0
    //     0xc0e814: stur            w0, [x1, #0xb]
    // 0xc0e818: ldur            x0, [fp, #-0x28]
    // 0xc0e81c: StoreField: r1->field_7 = r0
    //     0xc0e81c: stur            w0, [x1, #7]
    // 0xc0e820: r16 = <RoundedRectangleBorder>
    //     0xc0e820: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xc0e824: ldr             x16, [x16, #0xf78]
    // 0xc0e828: stp             x1, x16, [SP]
    // 0xc0e82c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc0e82c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc0e830: r0 = all()
    //     0xc0e830: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc0e834: stur            x0, [fp, #-8]
    // 0xc0e838: r0 = ButtonStyle()
    //     0xc0e838: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xc0e83c: mov             x1, x0
    // 0xc0e840: ldur            x0, [fp, #-0x68]
    // 0xc0e844: stur            x1, [fp, #-0x28]
    // 0xc0e848: StoreField: r1->field_b = r0
    //     0xc0e848: stur            w0, [x1, #0xb]
    // 0xc0e84c: ldur            x0, [fp, #-0x60]
    // 0xc0e850: StoreField: r1->field_23 = r0
    //     0xc0e850: stur            w0, [x1, #0x23]
    // 0xc0e854: ldur            x0, [fp, #-8]
    // 0xc0e858: StoreField: r1->field_43 = r0
    //     0xc0e858: stur            w0, [x1, #0x43]
    // 0xc0e85c: r0 = TextButtonThemeData()
    //     0xc0e85c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xc0e860: mov             x2, x0
    // 0xc0e864: ldur            x0, [fp, #-0x28]
    // 0xc0e868: stur            x2, [fp, #-8]
    // 0xc0e86c: StoreField: r2->field_7 = r0
    //     0xc0e86c: stur            w0, [x2, #7]
    // 0xc0e870: r16 = 14.000000
    //     0xc0e870: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc0e874: ldr             x16, [x16, #0x1d8]
    // 0xc0e878: r30 = Instance_Color
    //     0xc0e878: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc0e87c: stp             lr, x16, [SP]
    // 0xc0e880: ldur            x1, [fp, #-0x18]
    // 0xc0e884: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc0e884: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc0e888: ldr             x4, [x4, #0xaa0]
    // 0xc0e88c: r0 = copyWith()
    //     0xc0e88c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0e890: stur            x0, [fp, #-0x18]
    // 0xc0e894: r0 = Text()
    //     0xc0e894: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0e898: mov             x3, x0
    // 0xc0e89c: r0 = "YES, EXCHANGE"
    //     0xc0e89c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53a00] "YES, EXCHANGE"
    //     0xc0e8a0: ldr             x0, [x0, #0xa00]
    // 0xc0e8a4: stur            x3, [fp, #-0x28]
    // 0xc0e8a8: StoreField: r3->field_b = r0
    //     0xc0e8a8: stur            w0, [x3, #0xb]
    // 0xc0e8ac: ldur            x0, [fp, #-0x18]
    // 0xc0e8b0: StoreField: r3->field_13 = r0
    //     0xc0e8b0: stur            w0, [x3, #0x13]
    // 0xc0e8b4: ldur            x2, [fp, #-0x10]
    // 0xc0e8b8: r1 = Function '<anonymous closure>':.
    //     0xc0e8b8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53a08] AnonymousClosure: (0xc0eb20), in [package:customer_app/app/presentation/views/line/product_detail/widgets/single_exchange_product_bottom_sheet.dart] _SingleExchangeProductBottomSheetState::build (0xc0cc84)
    //     0xc0e8bc: ldr             x1, [x1, #0xa08]
    // 0xc0e8c0: r0 = AllocateClosure()
    //     0xc0e8c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0e8c4: stur            x0, [fp, #-0x10]
    // 0xc0e8c8: r0 = TextButton()
    //     0xc0e8c8: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xc0e8cc: mov             x1, x0
    // 0xc0e8d0: ldur            x0, [fp, #-0x10]
    // 0xc0e8d4: stur            x1, [fp, #-0x18]
    // 0xc0e8d8: StoreField: r1->field_b = r0
    //     0xc0e8d8: stur            w0, [x1, #0xb]
    // 0xc0e8dc: r0 = false
    //     0xc0e8dc: add             x0, NULL, #0x30  ; false
    // 0xc0e8e0: StoreField: r1->field_27 = r0
    //     0xc0e8e0: stur            w0, [x1, #0x27]
    // 0xc0e8e4: r0 = true
    //     0xc0e8e4: add             x0, NULL, #0x20  ; true
    // 0xc0e8e8: StoreField: r1->field_2f = r0
    //     0xc0e8e8: stur            w0, [x1, #0x2f]
    // 0xc0e8ec: ldur            x0, [fp, #-0x28]
    // 0xc0e8f0: StoreField: r1->field_37 = r0
    //     0xc0e8f0: stur            w0, [x1, #0x37]
    // 0xc0e8f4: r0 = TextButtonTheme()
    //     0xc0e8f4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xc0e8f8: mov             x2, x0
    // 0xc0e8fc: ldur            x0, [fp, #-8]
    // 0xc0e900: stur            x2, [fp, #-0x10]
    // 0xc0e904: StoreField: r2->field_f = r0
    //     0xc0e904: stur            w0, [x2, #0xf]
    // 0xc0e908: ldur            x0, [fp, #-0x18]
    // 0xc0e90c: StoreField: r2->field_b = r0
    //     0xc0e90c: stur            w0, [x2, #0xb]
    // 0xc0e910: r1 = <FlexParentData>
    //     0xc0e910: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xc0e914: ldr             x1, [x1, #0xe00]
    // 0xc0e918: r0 = Expanded()
    //     0xc0e918: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xc0e91c: mov             x3, x0
    // 0xc0e920: r0 = 1
    //     0xc0e920: movz            x0, #0x1
    // 0xc0e924: stur            x3, [fp, #-8]
    // 0xc0e928: StoreField: r3->field_13 = r0
    //     0xc0e928: stur            x0, [x3, #0x13]
    // 0xc0e92c: r0 = Instance_FlexFit
    //     0xc0e92c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xc0e930: ldr             x0, [x0, #0xe08]
    // 0xc0e934: StoreField: r3->field_1b = r0
    //     0xc0e934: stur            w0, [x3, #0x1b]
    // 0xc0e938: ldur            x0, [fp, #-0x10]
    // 0xc0e93c: StoreField: r3->field_b = r0
    //     0xc0e93c: stur            w0, [x3, #0xb]
    // 0xc0e940: r1 = Null
    //     0xc0e940: mov             x1, NULL
    // 0xc0e944: r2 = 6
    //     0xc0e944: movz            x2, #0x6
    // 0xc0e948: r0 = AllocateArray()
    //     0xc0e948: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0e94c: mov             x2, x0
    // 0xc0e950: ldur            x0, [fp, #-0x40]
    // 0xc0e954: stur            x2, [fp, #-0x10]
    // 0xc0e958: StoreField: r2->field_f = r0
    //     0xc0e958: stur            w0, [x2, #0xf]
    // 0xc0e95c: r16 = Instance_SizedBox
    //     0xc0e95c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xc0e960: ldr             x16, [x16, #0x998]
    // 0xc0e964: StoreField: r2->field_13 = r16
    //     0xc0e964: stur            w16, [x2, #0x13]
    // 0xc0e968: ldur            x0, [fp, #-8]
    // 0xc0e96c: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0e96c: stur            w0, [x2, #0x17]
    // 0xc0e970: r1 = <Widget>
    //     0xc0e970: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0e974: r0 = AllocateGrowableArray()
    //     0xc0e974: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0e978: mov             x1, x0
    // 0xc0e97c: ldur            x0, [fp, #-0x10]
    // 0xc0e980: stur            x1, [fp, #-8]
    // 0xc0e984: StoreField: r1->field_f = r0
    //     0xc0e984: stur            w0, [x1, #0xf]
    // 0xc0e988: r0 = 6
    //     0xc0e988: movz            x0, #0x6
    // 0xc0e98c: StoreField: r1->field_b = r0
    //     0xc0e98c: stur            w0, [x1, #0xb]
    // 0xc0e990: r0 = Row()
    //     0xc0e990: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc0e994: mov             x3, x0
    // 0xc0e998: r0 = Instance_Axis
    //     0xc0e998: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc0e99c: stur            x3, [fp, #-0x10]
    // 0xc0e9a0: StoreField: r3->field_f = r0
    //     0xc0e9a0: stur            w0, [x3, #0xf]
    // 0xc0e9a4: r0 = Instance_MainAxisAlignment
    //     0xc0e9a4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xc0e9a8: ldr             x0, [x0, #0xa8]
    // 0xc0e9ac: StoreField: r3->field_13 = r0
    //     0xc0e9ac: stur            w0, [x3, #0x13]
    // 0xc0e9b0: r0 = Instance_MainAxisSize
    //     0xc0e9b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0e9b4: ldr             x0, [x0, #0xa10]
    // 0xc0e9b8: ArrayStore: r3[0] = r0  ; List_4
    //     0xc0e9b8: stur            w0, [x3, #0x17]
    // 0xc0e9bc: r0 = Instance_CrossAxisAlignment
    //     0xc0e9bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc0e9c0: ldr             x0, [x0, #0xa18]
    // 0xc0e9c4: StoreField: r3->field_1b = r0
    //     0xc0e9c4: stur            w0, [x3, #0x1b]
    // 0xc0e9c8: r0 = Instance_VerticalDirection
    //     0xc0e9c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0e9cc: ldr             x0, [x0, #0xa20]
    // 0xc0e9d0: StoreField: r3->field_23 = r0
    //     0xc0e9d0: stur            w0, [x3, #0x23]
    // 0xc0e9d4: r4 = Instance_Clip
    //     0xc0e9d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0e9d8: ldr             x4, [x4, #0x38]
    // 0xc0e9dc: StoreField: r3->field_2b = r4
    //     0xc0e9dc: stur            w4, [x3, #0x2b]
    // 0xc0e9e0: StoreField: r3->field_2f = rZR
    //     0xc0e9e0: stur            xzr, [x3, #0x2f]
    // 0xc0e9e4: ldur            x1, [fp, #-8]
    // 0xc0e9e8: StoreField: r3->field_b = r1
    //     0xc0e9e8: stur            w1, [x3, #0xb]
    // 0xc0e9ec: r1 = Null
    //     0xc0e9ec: mov             x1, NULL
    // 0xc0e9f0: r2 = 12
    //     0xc0e9f0: movz            x2, #0xc
    // 0xc0e9f4: r0 = AllocateArray()
    //     0xc0e9f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0e9f8: mov             x2, x0
    // 0xc0e9fc: ldur            x0, [fp, #-0x30]
    // 0xc0ea00: stur            x2, [fp, #-8]
    // 0xc0ea04: StoreField: r2->field_f = r0
    //     0xc0ea04: stur            w0, [x2, #0xf]
    // 0xc0ea08: ldur            x0, [fp, #-0x20]
    // 0xc0ea0c: StoreField: r2->field_13 = r0
    //     0xc0ea0c: stur            w0, [x2, #0x13]
    // 0xc0ea10: r16 = Instance_SizedBox
    //     0xc0ea10: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xc0ea14: ldr             x16, [x16, #0x8f0]
    // 0xc0ea18: ArrayStore: r2[0] = r16  ; List_4
    //     0xc0ea18: stur            w16, [x2, #0x17]
    // 0xc0ea1c: ldur            x0, [fp, #-0x38]
    // 0xc0ea20: StoreField: r2->field_1b = r0
    //     0xc0ea20: stur            w0, [x2, #0x1b]
    // 0xc0ea24: r16 = Instance_SizedBox
    //     0xc0ea24: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xc0ea28: ldr             x16, [x16, #0x9f0]
    // 0xc0ea2c: StoreField: r2->field_1f = r16
    //     0xc0ea2c: stur            w16, [x2, #0x1f]
    // 0xc0ea30: ldur            x0, [fp, #-0x10]
    // 0xc0ea34: StoreField: r2->field_23 = r0
    //     0xc0ea34: stur            w0, [x2, #0x23]
    // 0xc0ea38: r1 = <Widget>
    //     0xc0ea38: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0ea3c: r0 = AllocateGrowableArray()
    //     0xc0ea3c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0ea40: mov             x1, x0
    // 0xc0ea44: ldur            x0, [fp, #-8]
    // 0xc0ea48: stur            x1, [fp, #-0x10]
    // 0xc0ea4c: StoreField: r1->field_f = r0
    //     0xc0ea4c: stur            w0, [x1, #0xf]
    // 0xc0ea50: r0 = 12
    //     0xc0ea50: movz            x0, #0xc
    // 0xc0ea54: StoreField: r1->field_b = r0
    //     0xc0ea54: stur            w0, [x1, #0xb]
    // 0xc0ea58: r0 = Column()
    //     0xc0ea58: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0ea5c: mov             x1, x0
    // 0xc0ea60: r0 = Instance_Axis
    //     0xc0ea60: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0ea64: stur            x1, [fp, #-8]
    // 0xc0ea68: StoreField: r1->field_f = r0
    //     0xc0ea68: stur            w0, [x1, #0xf]
    // 0xc0ea6c: r0 = Instance_MainAxisAlignment
    //     0xc0ea6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0ea70: ldr             x0, [x0, #0xa08]
    // 0xc0ea74: StoreField: r1->field_13 = r0
    //     0xc0ea74: stur            w0, [x1, #0x13]
    // 0xc0ea78: r0 = Instance_MainAxisSize
    //     0xc0ea78: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xc0ea7c: ldr             x0, [x0, #0xdd0]
    // 0xc0ea80: ArrayStore: r1[0] = r0  ; List_4
    //     0xc0ea80: stur            w0, [x1, #0x17]
    // 0xc0ea84: r0 = Instance_CrossAxisAlignment
    //     0xc0ea84: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc0ea88: ldr             x0, [x0, #0x890]
    // 0xc0ea8c: StoreField: r1->field_1b = r0
    //     0xc0ea8c: stur            w0, [x1, #0x1b]
    // 0xc0ea90: r0 = Instance_VerticalDirection
    //     0xc0ea90: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0ea94: ldr             x0, [x0, #0xa20]
    // 0xc0ea98: StoreField: r1->field_23 = r0
    //     0xc0ea98: stur            w0, [x1, #0x23]
    // 0xc0ea9c: r0 = Instance_Clip
    //     0xc0ea9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0eaa0: ldr             x0, [x0, #0x38]
    // 0xc0eaa4: StoreField: r1->field_2b = r0
    //     0xc0eaa4: stur            w0, [x1, #0x2b]
    // 0xc0eaa8: StoreField: r1->field_2f = rZR
    //     0xc0eaa8: stur            xzr, [x1, #0x2f]
    // 0xc0eaac: ldur            x0, [fp, #-0x10]
    // 0xc0eab0: StoreField: r1->field_b = r0
    //     0xc0eab0: stur            w0, [x1, #0xb]
    // 0xc0eab4: r0 = Padding()
    //     0xc0eab4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0eab8: r1 = Instance_EdgeInsets
    //     0xc0eab8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xc0eabc: ldr             x1, [x1, #0x668]
    // 0xc0eac0: StoreField: r0->field_f = r1
    //     0xc0eac0: stur            w1, [x0, #0xf]
    // 0xc0eac4: ldur            x1, [fp, #-8]
    // 0xc0eac8: StoreField: r0->field_b = r1
    //     0xc0eac8: stur            w1, [x0, #0xb]
    // 0xc0eacc: LeaveFrame
    //     0xc0eacc: mov             SP, fp
    //     0xc0ead0: ldp             fp, lr, [SP], #0x10
    // 0xc0ead4: ret
    //     0xc0ead4: ret             
    // 0xc0ead8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0ead8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0eadc: b               #0xc0cca0
    // 0xc0eae0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eae0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0eae4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eae4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0eae8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eae8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0eaec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eaec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0eaf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eaf0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0eaf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eaf4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0eaf8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eaf8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0eafc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eafc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0eb00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eb00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0eb04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eb04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0eb08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eb08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0eb0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eb0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0eb10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eb10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0eb14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eb14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0eb18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eb18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0eb1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0eb1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc0eb20, size: 0xb4
    // 0xc0eb20: EnterFrame
    //     0xc0eb20: stp             fp, lr, [SP, #-0x10]!
    //     0xc0eb24: mov             fp, SP
    // 0xc0eb28: AllocStack(0x10)
    //     0xc0eb28: sub             SP, SP, #0x10
    // 0xc0eb2c: SetupParameters()
    //     0xc0eb2c: ldr             x0, [fp, #0x10]
    //     0xc0eb30: ldur            w2, [x0, #0x17]
    //     0xc0eb34: add             x2, x2, HEAP, lsl #32
    //     0xc0eb38: stur            x2, [fp, #-8]
    // 0xc0eb3c: CheckStackOverflow
    //     0xc0eb3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0eb40: cmp             SP, x16
    //     0xc0eb44: b.ls            #0xc0ebc8
    // 0xc0eb48: LoadField: r0 = r2->field_f
    //     0xc0eb48: ldur            w0, [x2, #0xf]
    // 0xc0eb4c: DecompressPointer r0
    //     0xc0eb4c: add             x0, x0, HEAP, lsl #32
    // 0xc0eb50: LoadField: r1 = r0->field_13
    //     0xc0eb50: ldur            w1, [x0, #0x13]
    // 0xc0eb54: DecompressPointer r1
    //     0xc0eb54: add             x1, x1, HEAP, lsl #32
    // 0xc0eb58: r0 = exchangeBuyNowProceedPostEvent()
    //     0xc0eb58: bl              #0xa9ab50  ; [package:customer_app/app/presentation/controllers/product_detail/product_detail_controller.dart] ProductDetailController::exchangeBuyNowProceedPostEvent
    // 0xc0eb5c: ldur            x0, [fp, #-8]
    // 0xc0eb60: LoadField: r1 = r0->field_f
    //     0xc0eb60: ldur            w1, [x0, #0xf]
    // 0xc0eb64: DecompressPointer r1
    //     0xc0eb64: add             x1, x1, HEAP, lsl #32
    // 0xc0eb68: LoadField: r2 = r1->field_13
    //     0xc0eb68: ldur            w2, [x1, #0x13]
    // 0xc0eb6c: DecompressPointer r2
    //     0xc0eb6c: add             x2, x2, HEAP, lsl #32
    // 0xc0eb70: mov             x1, x2
    // 0xc0eb74: r0 = ctaExchangeCheckoutInitiatedPostEvent()
    //     0xc0eb74: bl              #0xa9a804  ; [package:customer_app/app/presentation/controllers/product_detail/product_detail_controller.dart] ProductDetailController::ctaExchangeCheckoutInitiatedPostEvent
    // 0xc0eb78: ldur            x0, [fp, #-8]
    // 0xc0eb7c: LoadField: r1 = r0->field_f
    //     0xc0eb7c: ldur            w1, [x0, #0xf]
    // 0xc0eb80: DecompressPointer r1
    //     0xc0eb80: add             x1, x1, HEAP, lsl #32
    // 0xc0eb84: LoadField: r0 = r1->field_b
    //     0xc0eb84: ldur            w0, [x1, #0xb]
    // 0xc0eb88: DecompressPointer r0
    //     0xc0eb88: add             x0, x0, HEAP, lsl #32
    // 0xc0eb8c: cmp             w0, NULL
    // 0xc0eb90: b.eq            #0xc0ebd0
    // 0xc0eb94: LoadField: r1 = r0->field_23
    //     0xc0eb94: ldur            w1, [x0, #0x23]
    // 0xc0eb98: DecompressPointer r1
    //     0xc0eb98: add             x1, x1, HEAP, lsl #32
    // 0xc0eb9c: str             x1, [SP]
    // 0xc0eba0: r4 = 0
    //     0xc0eba0: movz            x4, #0
    // 0xc0eba4: ldr             x0, [SP]
    // 0xc0eba8: r16 = UnlinkedCall_0x613b5c
    //     0xc0eba8: add             x16, PP, #0x53, lsl #12  ; [pp+0x53a10] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc0ebac: add             x16, x16, #0xa10
    // 0xc0ebb0: ldp             x5, lr, [x16]
    // 0xc0ebb4: blr             lr
    // 0xc0ebb8: r0 = Null
    //     0xc0ebb8: mov             x0, NULL
    // 0xc0ebbc: LeaveFrame
    //     0xc0ebbc: mov             SP, fp
    //     0xc0ebc0: ldp             fp, lr, [SP], #0x10
    // 0xc0ebc4: ret
    //     0xc0ebc4: ret             
    // 0xc0ebc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0ebc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0ebcc: b               #0xc0eb48
    // 0xc0ebd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0ebd0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc0ebd4, size: 0xbc
    // 0xc0ebd4: EnterFrame
    //     0xc0ebd4: stp             fp, lr, [SP, #-0x10]!
    //     0xc0ebd8: mov             fp, SP
    // 0xc0ebdc: AllocStack(0x10)
    //     0xc0ebdc: sub             SP, SP, #0x10
    // 0xc0ebe0: SetupParameters()
    //     0xc0ebe0: ldr             x0, [fp, #0x10]
    //     0xc0ebe4: ldur            w2, [x0, #0x17]
    //     0xc0ebe8: add             x2, x2, HEAP, lsl #32
    //     0xc0ebec: stur            x2, [fp, #-8]
    // 0xc0ebf0: CheckStackOverflow
    //     0xc0ebf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0ebf4: cmp             SP, x16
    //     0xc0ebf8: b.ls            #0xc0ec84
    // 0xc0ebfc: LoadField: r0 = r2->field_f
    //     0xc0ebfc: ldur            w0, [x2, #0xf]
    // 0xc0ec00: DecompressPointer r0
    //     0xc0ec00: add             x0, x0, HEAP, lsl #32
    // 0xc0ec04: LoadField: r1 = r0->field_13
    //     0xc0ec04: ldur            w1, [x0, #0x13]
    // 0xc0ec08: DecompressPointer r1
    //     0xc0ec08: add             x1, x1, HEAP, lsl #32
    // 0xc0ec0c: r0 = postBuyNowEvent()
    //     0xc0ec0c: bl              #0xa9af10  ; [package:customer_app/app/presentation/controllers/product_detail/product_detail_controller.dart] ProductDetailController::postBuyNowEvent
    // 0xc0ec10: ldur            x0, [fp, #-8]
    // 0xc0ec14: LoadField: r1 = r0->field_f
    //     0xc0ec14: ldur            w1, [x0, #0xf]
    // 0xc0ec18: DecompressPointer r1
    //     0xc0ec18: add             x1, x1, HEAP, lsl #32
    // 0xc0ec1c: LoadField: r2 = r1->field_13
    //     0xc0ec1c: ldur            w2, [x1, #0x13]
    // 0xc0ec20: DecompressPointer r2
    //     0xc0ec20: add             x2, x2, HEAP, lsl #32
    // 0xc0ec24: mov             x1, x2
    // 0xc0ec28: r2 = "buy_now"
    //     0xc0ec28: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe78] "buy_now"
    //     0xc0ec2c: ldr             x2, [x2, #0xe78]
    // 0xc0ec30: r0 = checkoutStartedPostEvent()
    //     0xc0ec30: bl              #0xa9ad70  ; [package:customer_app/app/presentation/controllers/product_detail/product_detail_controller.dart] ProductDetailController::checkoutStartedPostEvent
    // 0xc0ec34: ldur            x0, [fp, #-8]
    // 0xc0ec38: LoadField: r1 = r0->field_f
    //     0xc0ec38: ldur            w1, [x0, #0xf]
    // 0xc0ec3c: DecompressPointer r1
    //     0xc0ec3c: add             x1, x1, HEAP, lsl #32
    // 0xc0ec40: LoadField: r0 = r1->field_b
    //     0xc0ec40: ldur            w0, [x1, #0xb]
    // 0xc0ec44: DecompressPointer r0
    //     0xc0ec44: add             x0, x0, HEAP, lsl #32
    // 0xc0ec48: cmp             w0, NULL
    // 0xc0ec4c: b.eq            #0xc0ec8c
    // 0xc0ec50: LoadField: r1 = r0->field_1f
    //     0xc0ec50: ldur            w1, [x0, #0x1f]
    // 0xc0ec54: DecompressPointer r1
    //     0xc0ec54: add             x1, x1, HEAP, lsl #32
    // 0xc0ec58: str             x1, [SP]
    // 0xc0ec5c: r4 = 0
    //     0xc0ec5c: movz            x4, #0
    // 0xc0ec60: ldr             x0, [SP]
    // 0xc0ec64: r16 = UnlinkedCall_0x613b5c
    //     0xc0ec64: add             x16, PP, #0x53, lsl #12  ; [pp+0x53a48] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc0ec68: add             x16, x16, #0xa48
    // 0xc0ec6c: ldp             x5, lr, [x16]
    // 0xc0ec70: blr             lr
    // 0xc0ec74: r0 = Null
    //     0xc0ec74: mov             x0, NULL
    // 0xc0ec78: LeaveFrame
    //     0xc0ec78: mov             SP, fp
    //     0xc0ec7c: ldp             fp, lr, [SP], #0x10
    // 0xc0ec80: ret
    //     0xc0ec80: ret             
    // 0xc0ec84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0ec84: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0ec88: b               #0xc0ebfc
    // 0xc0ec8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0ec8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3961, size: 0x34, field offset: 0xc
//   const constructor, 
class SingleExchangeProductBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc812d8, size: 0x74
    // 0xc812d8: EnterFrame
    //     0xc812d8: stp             fp, lr, [SP, #-0x10]!
    //     0xc812dc: mov             fp, SP
    // 0xc812e0: AllocStack(0x10)
    //     0xc812e0: sub             SP, SP, #0x10
    // 0xc812e4: CheckStackOverflow
    //     0xc812e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc812e8: cmp             SP, x16
    //     0xc812ec: b.ls            #0xc81344
    // 0xc812f0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xc812f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc812f4: ldr             x0, [x0, #0x1c80]
    //     0xc812f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc812fc: cmp             w0, w16
    //     0xc81300: b.ne            #0xc8130c
    //     0xc81304: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xc81308: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc8130c: r16 = <ProductDetailController>
    //     0xc8130c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ede0] TypeArguments: <ProductDetailController>
    //     0xc81310: ldr             x16, [x16, #0xde0]
    // 0xc81314: str             x16, [SP]
    // 0xc81318: r4 = const [0x1, 0, 0, 0, null]
    //     0xc81318: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xc8131c: r0 = Inst.find()
    //     0xc8131c: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xc81320: r1 = <SingleExchangeProductBottomSheet>
    //     0xc81320: add             x1, PP, #0x48, lsl #12  ; [pp+0x48448] TypeArguments: <SingleExchangeProductBottomSheet>
    //     0xc81324: ldr             x1, [x1, #0x448]
    // 0xc81328: stur            x0, [fp, #-8]
    // 0xc8132c: r0 = _SingleExchangeProductBottomSheetState()
    //     0xc8132c: bl              #0xc8134c  ; Allocate_SingleExchangeProductBottomSheetStateStub -> _SingleExchangeProductBottomSheetState (size=0x18)
    // 0xc81330: ldur            x1, [fp, #-8]
    // 0xc81334: StoreField: r0->field_13 = r1
    //     0xc81334: stur            w1, [x0, #0x13]
    // 0xc81338: LeaveFrame
    //     0xc81338: mov             SP, fp
    //     0xc8133c: ldp             fp, lr, [SP], #0x10
    // 0xc81340: ret
    //     0xc81340: ret             
    // 0xc81344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc81344: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc81348: b               #0xc812f0
  }
}
