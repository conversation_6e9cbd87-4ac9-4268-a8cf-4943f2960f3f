// lib: , url: package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart

// class id: 1049529, size: 0x8
class :: {
}

// class id: 3242, size: 0x24, field offset: 0x14
class _TestimonialCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x949898, size: 0x1b8
    // 0x949898: EnterFrame
    //     0x949898: stp             fp, lr, [SP, #-0x10]!
    //     0x94989c: mov             fp, SP
    // 0x9498a0: AllocStack(0x28)
    //     0x9498a0: sub             SP, SP, #0x28
    // 0x9498a4: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */)
    //     0x9498a4: stur            x1, [fp, #-8]
    // 0x9498a8: CheckStackOverflow
    //     0x9498a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9498ac: cmp             SP, x16
    //     0x9498b0: b.ls            #0x949a38
    // 0x9498b4: r0 = PageController()
    //     0x9498b4: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x9498b8: stur            x0, [fp, #-0x10]
    // 0x9498bc: StoreField: r0->field_3f = rZR
    //     0x9498bc: stur            xzr, [x0, #0x3f]
    // 0x9498c0: r2 = true
    //     0x9498c0: add             x2, NULL, #0x20  ; true
    // 0x9498c4: StoreField: r0->field_47 = r2
    //     0x9498c4: stur            w2, [x0, #0x47]
    // 0x9498c8: d0 = 1.000000
    //     0x9498c8: fmov            d0, #1.00000000
    // 0x9498cc: StoreField: r0->field_4b = d0
    //     0x9498cc: stur            d0, [x0, #0x4b]
    // 0x9498d0: mov             x1, x0
    // 0x9498d4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9498d4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9498d8: r0 = ScrollController()
    //     0x9498d8: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x9498dc: ldur            x0, [fp, #-0x10]
    // 0x9498e0: ldur            x2, [fp, #-8]
    // 0x9498e4: StoreField: r2->field_13 = r0
    //     0x9498e4: stur            w0, [x2, #0x13]
    //     0x9498e8: ldurb           w16, [x2, #-1]
    //     0x9498ec: ldurb           w17, [x0, #-1]
    //     0x9498f0: and             x16, x17, x16, lsr #2
    //     0x9498f4: tst             x16, HEAP, lsr #32
    //     0x9498f8: b.eq            #0x949900
    //     0x9498fc: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x949900: LoadField: r0 = r2->field_b
    //     0x949900: ldur            w0, [x2, #0xb]
    // 0x949904: DecompressPointer r0
    //     0x949904: add             x0, x0, HEAP, lsl #32
    // 0x949908: cmp             w0, NULL
    // 0x94990c: b.eq            #0x949a40
    // 0x949910: LoadField: r1 = r0->field_b
    //     0x949910: ldur            w1, [x0, #0xb]
    // 0x949914: DecompressPointer r1
    //     0x949914: add             x1, x1, HEAP, lsl #32
    // 0x949918: cmp             w1, NULL
    // 0x94991c: b.eq            #0x949a28
    // 0x949920: LoadField: r0 = r2->field_1f
    //     0x949920: ldur            w0, [x2, #0x1f]
    // 0x949924: DecompressPointer r0
    //     0x949924: add             x0, x0, HEAP, lsl #32
    // 0x949928: stur            x0, [fp, #-0x10]
    // 0x94992c: r3 = 0
    //     0x94992c: movz            x3, #0
    // 0x949930: stur            x3, [fp, #-0x18]
    // 0x949934: CheckStackOverflow
    //     0x949934: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x949938: cmp             SP, x16
    //     0x94993c: b.ls            #0x949a44
    // 0x949940: LoadField: r1 = r2->field_b
    //     0x949940: ldur            w1, [x2, #0xb]
    // 0x949944: DecompressPointer r1
    //     0x949944: add             x1, x1, HEAP, lsl #32
    // 0x949948: cmp             w1, NULL
    // 0x94994c: b.eq            #0x949a4c
    // 0x949950: LoadField: r4 = r1->field_b
    //     0x949950: ldur            w4, [x1, #0xb]
    // 0x949954: DecompressPointer r4
    //     0x949954: add             x4, x4, HEAP, lsl #32
    // 0x949958: cmp             w4, NULL
    // 0x94995c: b.ne            #0x949968
    // 0x949960: r1 = Null
    //     0x949960: mov             x1, NULL
    // 0x949964: b               #0x94996c
    // 0x949968: LoadField: r1 = r4->field_b
    //     0x949968: ldur            w1, [x4, #0xb]
    // 0x94996c: cmp             w1, NULL
    // 0x949970: b.ne            #0x94997c
    // 0x949974: r1 = 0
    //     0x949974: movz            x1, #0
    // 0x949978: b               #0x949984
    // 0x94997c: r4 = LoadInt32Instr(r1)
    //     0x94997c: sbfx            x4, x1, #1, #0x1f
    // 0x949980: mov             x1, x4
    // 0x949984: cmp             x3, x1
    // 0x949988: b.ge            #0x949a28
    // 0x94998c: r1 = <bool>
    //     0x94998c: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x949990: r0 = RxBool()
    //     0x949990: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0x949994: mov             x2, x0
    // 0x949998: r0 = Sentinel
    //     0x949998: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x94999c: stur            x2, [fp, #-0x20]
    // 0x9499a0: StoreField: r2->field_13 = r0
    //     0x9499a0: stur            w0, [x2, #0x13]
    // 0x9499a4: r3 = true
    //     0x9499a4: add             x3, NULL, #0x20  ; true
    // 0x9499a8: ArrayStore: r2[0] = r3  ; List_4
    //     0x9499a8: stur            w3, [x2, #0x17]
    // 0x9499ac: mov             x1, x2
    // 0x9499b0: r0 = RxNotifier()
    //     0x9499b0: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0x9499b4: ldur            x3, [fp, #-0x20]
    // 0x9499b8: r4 = false
    //     0x9499b8: add             x4, NULL, #0x30  ; false
    // 0x9499bc: StoreField: r3->field_13 = r4
    //     0x9499bc: stur            w4, [x3, #0x13]
    // 0x9499c0: ldur            x5, [fp, #-0x18]
    // 0x9499c4: r0 = BoxInt64Instr(r5)
    //     0x9499c4: sbfiz           x0, x5, #1, #0x1f
    //     0x9499c8: cmp             x5, x0, asr #1
    //     0x9499cc: b.eq            #0x9499d8
    //     0x9499d0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9499d4: stur            x5, [x0, #7]
    // 0x9499d8: ldur            x1, [fp, #-0x10]
    // 0x9499dc: mov             x2, x0
    // 0x9499e0: stur            x0, [fp, #-0x28]
    // 0x9499e4: r0 = _hashCode()
    //     0x9499e4: bl              #0x16f1c0c  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x9499e8: mov             x2, x0
    // 0x9499ec: r0 = BoxInt64Instr(r2)
    //     0x9499ec: sbfiz           x0, x2, #1, #0x1f
    //     0x9499f0: cmp             x2, x0, asr #1
    //     0x9499f4: b.eq            #0x949a00
    //     0x9499f8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9499fc: stur            x2, [x0, #7]
    // 0x949a00: ldur            x1, [fp, #-0x10]
    // 0x949a04: ldur            x2, [fp, #-0x28]
    // 0x949a08: ldur            x3, [fp, #-0x20]
    // 0x949a0c: mov             x5, x0
    // 0x949a10: r0 = _set()
    //     0x949a10: bl              #0x16e32b4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x949a14: ldur            x1, [fp, #-0x18]
    // 0x949a18: add             x3, x1, #1
    // 0x949a1c: ldur            x2, [fp, #-8]
    // 0x949a20: ldur            x0, [fp, #-0x10]
    // 0x949a24: b               #0x949930
    // 0x949a28: r0 = Null
    //     0x949a28: mov             x0, NULL
    // 0x949a2c: LeaveFrame
    //     0x949a2c: mov             SP, fp
    //     0x949a30: ldp             fp, lr, [SP], #0x10
    // 0x949a34: ret
    //     0x949a34: ret             
    // 0x949a38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x949a38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x949a3c: b               #0x9498b4
    // 0x949a40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x949a40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x949a44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x949a44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x949a48: b               #0x949940
    // 0x949a4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x949a4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbf0494, size: 0x924
    // 0xbf0494: EnterFrame
    //     0xbf0494: stp             fp, lr, [SP, #-0x10]!
    //     0xbf0498: mov             fp, SP
    // 0xbf049c: AllocStack(0x78)
    //     0xbf049c: sub             SP, SP, #0x78
    // 0xbf04a0: SetupParameters(_TestimonialCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbf04a0: mov             x0, x1
    //     0xbf04a4: stur            x1, [fp, #-8]
    //     0xbf04a8: mov             x1, x2
    //     0xbf04ac: stur            x2, [fp, #-0x10]
    // 0xbf04b0: CheckStackOverflow
    //     0xbf04b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf04b4: cmp             SP, x16
    //     0xbf04b8: b.ls            #0xbf0d78
    // 0xbf04bc: r1 = 1
    //     0xbf04bc: movz            x1, #0x1
    // 0xbf04c0: r0 = AllocateContext()
    //     0xbf04c0: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf04c4: mov             x3, x0
    // 0xbf04c8: ldur            x0, [fp, #-8]
    // 0xbf04cc: stur            x3, [fp, #-0x20]
    // 0xbf04d0: StoreField: r3->field_f = r0
    //     0xbf04d0: stur            w0, [x3, #0xf]
    // 0xbf04d4: LoadField: r1 = r0->field_b
    //     0xbf04d4: ldur            w1, [x0, #0xb]
    // 0xbf04d8: DecompressPointer r1
    //     0xbf04d8: add             x1, x1, HEAP, lsl #32
    // 0xbf04dc: cmp             w1, NULL
    // 0xbf04e0: b.eq            #0xbf0d80
    // 0xbf04e4: LoadField: r2 = r1->field_13
    //     0xbf04e4: ldur            w2, [x1, #0x13]
    // 0xbf04e8: DecompressPointer r2
    //     0xbf04e8: add             x2, x2, HEAP, lsl #32
    // 0xbf04ec: cmp             w2, NULL
    // 0xbf04f0: b.ne            #0xbf04fc
    // 0xbf04f4: r1 = Null
    //     0xbf04f4: mov             x1, NULL
    // 0xbf04f8: b               #0xbf0504
    // 0xbf04fc: LoadField: r1 = r2->field_7
    //     0xbf04fc: ldur            w1, [x2, #7]
    // 0xbf0500: DecompressPointer r1
    //     0xbf0500: add             x1, x1, HEAP, lsl #32
    // 0xbf0504: cmp             w1, NULL
    // 0xbf0508: b.ne            #0xbf0514
    // 0xbf050c: r1 = Instance_TitleAlignment
    //     0xbf050c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbf0510: ldr             x1, [x1, #0x518]
    // 0xbf0514: r16 = Instance_TitleAlignment
    //     0xbf0514: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xbf0518: ldr             x16, [x16, #0x520]
    // 0xbf051c: cmp             w1, w16
    // 0xbf0520: b.ne            #0xbf0530
    // 0xbf0524: r4 = Instance_CrossAxisAlignment
    //     0xbf0524: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xbf0528: ldr             x4, [x4, #0xc68]
    // 0xbf052c: b               #0xbf0554
    // 0xbf0530: r16 = Instance_TitleAlignment
    //     0xbf0530: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbf0534: ldr             x16, [x16, #0x518]
    // 0xbf0538: cmp             w1, w16
    // 0xbf053c: b.ne            #0xbf054c
    // 0xbf0540: r4 = Instance_CrossAxisAlignment
    //     0xbf0540: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbf0544: ldr             x4, [x4, #0x890]
    // 0xbf0548: b               #0xbf0554
    // 0xbf054c: r4 = Instance_CrossAxisAlignment
    //     0xbf054c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbf0550: ldr             x4, [x4, #0xa18]
    // 0xbf0554: stur            x4, [fp, #-0x18]
    // 0xbf0558: r1 = <Widget>
    //     0xbf0558: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf055c: r2 = 0
    //     0xbf055c: movz            x2, #0
    // 0xbf0560: r0 = _GrowableList()
    //     0xbf0560: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xbf0564: mov             x2, x0
    // 0xbf0568: ldur            x1, [fp, #-8]
    // 0xbf056c: stur            x2, [fp, #-0x28]
    // 0xbf0570: LoadField: r0 = r1->field_b
    //     0xbf0570: ldur            w0, [x1, #0xb]
    // 0xbf0574: DecompressPointer r0
    //     0xbf0574: add             x0, x0, HEAP, lsl #32
    // 0xbf0578: cmp             w0, NULL
    // 0xbf057c: b.eq            #0xbf0d84
    // 0xbf0580: LoadField: r3 = r0->field_f
    //     0xbf0580: ldur            w3, [x0, #0xf]
    // 0xbf0584: DecompressPointer r3
    //     0xbf0584: add             x3, x3, HEAP, lsl #32
    // 0xbf0588: LoadField: r0 = r3->field_7
    //     0xbf0588: ldur            w0, [x3, #7]
    // 0xbf058c: cbz             w0, #0xbf07ac
    // 0xbf0590: r0 = LoadClassIdInstr(r3)
    //     0xbf0590: ldur            x0, [x3, #-1]
    //     0xbf0594: ubfx            x0, x0, #0xc, #0x14
    // 0xbf0598: str             x3, [SP]
    // 0xbf059c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbf059c: sub             lr, x0, #1, lsl #12
    //     0xbf05a0: ldr             lr, [x21, lr, lsl #3]
    //     0xbf05a4: blr             lr
    // 0xbf05a8: mov             x2, x0
    // 0xbf05ac: ldur            x0, [fp, #-8]
    // 0xbf05b0: stur            x2, [fp, #-0x38]
    // 0xbf05b4: LoadField: r1 = r0->field_b
    //     0xbf05b4: ldur            w1, [x0, #0xb]
    // 0xbf05b8: DecompressPointer r1
    //     0xbf05b8: add             x1, x1, HEAP, lsl #32
    // 0xbf05bc: cmp             w1, NULL
    // 0xbf05c0: b.eq            #0xbf0d88
    // 0xbf05c4: LoadField: r3 = r1->field_13
    //     0xbf05c4: ldur            w3, [x1, #0x13]
    // 0xbf05c8: DecompressPointer r3
    //     0xbf05c8: add             x3, x3, HEAP, lsl #32
    // 0xbf05cc: cmp             w3, NULL
    // 0xbf05d0: b.ne            #0xbf05dc
    // 0xbf05d4: r1 = Null
    //     0xbf05d4: mov             x1, NULL
    // 0xbf05d8: b               #0xbf05e4
    // 0xbf05dc: LoadField: r1 = r3->field_7
    //     0xbf05dc: ldur            w1, [x3, #7]
    // 0xbf05e0: DecompressPointer r1
    //     0xbf05e0: add             x1, x1, HEAP, lsl #32
    // 0xbf05e4: cmp             w1, NULL
    // 0xbf05e8: b.ne            #0xbf05f4
    // 0xbf05ec: r1 = Instance_TitleAlignment
    //     0xbf05ec: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbf05f0: ldr             x1, [x1, #0x518]
    // 0xbf05f4: r16 = Instance_TitleAlignment
    //     0xbf05f4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xbf05f8: ldr             x16, [x16, #0x520]
    // 0xbf05fc: cmp             w1, w16
    // 0xbf0600: b.ne            #0xbf060c
    // 0xbf0604: r4 = Instance_TextAlign
    //     0xbf0604: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xbf0608: b               #0xbf0628
    // 0xbf060c: r16 = Instance_TitleAlignment
    //     0xbf060c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbf0610: ldr             x16, [x16, #0x518]
    // 0xbf0614: cmp             w1, w16
    // 0xbf0618: b.ne            #0xbf0624
    // 0xbf061c: r4 = Instance_TextAlign
    //     0xbf061c: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xbf0620: b               #0xbf0628
    // 0xbf0624: r4 = Instance_TextAlign
    //     0xbf0624: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xbf0628: ldur            x3, [fp, #-0x28]
    // 0xbf062c: ldur            x1, [fp, #-0x10]
    // 0xbf0630: stur            x4, [fp, #-0x30]
    // 0xbf0634: r0 = of()
    //     0xbf0634: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf0638: LoadField: r1 = r0->field_87
    //     0xbf0638: ldur            w1, [x0, #0x87]
    // 0xbf063c: DecompressPointer r1
    //     0xbf063c: add             x1, x1, HEAP, lsl #32
    // 0xbf0640: LoadField: r0 = r1->field_27
    //     0xbf0640: ldur            w0, [x1, #0x27]
    // 0xbf0644: DecompressPointer r0
    //     0xbf0644: add             x0, x0, HEAP, lsl #32
    // 0xbf0648: r16 = 21.000000
    //     0xbf0648: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xbf064c: ldr             x16, [x16, #0x9b0]
    // 0xbf0650: r30 = Instance_Color
    //     0xbf0650: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf0654: stp             lr, x16, [SP]
    // 0xbf0658: mov             x1, x0
    // 0xbf065c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf065c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf0660: ldr             x4, [x4, #0xaa0]
    // 0xbf0664: r0 = copyWith()
    //     0xbf0664: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf0668: stur            x0, [fp, #-0x40]
    // 0xbf066c: r0 = Text()
    //     0xbf066c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf0670: mov             x3, x0
    // 0xbf0674: ldur            x0, [fp, #-0x38]
    // 0xbf0678: stur            x3, [fp, #-0x48]
    // 0xbf067c: StoreField: r3->field_b = r0
    //     0xbf067c: stur            w0, [x3, #0xb]
    // 0xbf0680: ldur            x0, [fp, #-0x40]
    // 0xbf0684: StoreField: r3->field_13 = r0
    //     0xbf0684: stur            w0, [x3, #0x13]
    // 0xbf0688: ldur            x0, [fp, #-0x30]
    // 0xbf068c: StoreField: r3->field_1b = r0
    //     0xbf068c: stur            w0, [x3, #0x1b]
    // 0xbf0690: r1 = Null
    //     0xbf0690: mov             x1, NULL
    // 0xbf0694: r2 = 4
    //     0xbf0694: movz            x2, #0x4
    // 0xbf0698: r0 = AllocateArray()
    //     0xbf0698: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf069c: mov             x2, x0
    // 0xbf06a0: ldur            x0, [fp, #-0x48]
    // 0xbf06a4: stur            x2, [fp, #-0x30]
    // 0xbf06a8: StoreField: r2->field_f = r0
    //     0xbf06a8: stur            w0, [x2, #0xf]
    // 0xbf06ac: r16 = Instance_SizedBox
    //     0xbf06ac: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xbf06b0: ldr             x16, [x16, #0xc70]
    // 0xbf06b4: StoreField: r2->field_13 = r16
    //     0xbf06b4: stur            w16, [x2, #0x13]
    // 0xbf06b8: r1 = <Widget>
    //     0xbf06b8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf06bc: r0 = AllocateGrowableArray()
    //     0xbf06bc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf06c0: mov             x1, x0
    // 0xbf06c4: ldur            x0, [fp, #-0x30]
    // 0xbf06c8: stur            x1, [fp, #-0x38]
    // 0xbf06cc: StoreField: r1->field_f = r0
    //     0xbf06cc: stur            w0, [x1, #0xf]
    // 0xbf06d0: r0 = 4
    //     0xbf06d0: movz            x0, #0x4
    // 0xbf06d4: StoreField: r1->field_b = r0
    //     0xbf06d4: stur            w0, [x1, #0xb]
    // 0xbf06d8: r0 = Column()
    //     0xbf06d8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf06dc: mov             x2, x0
    // 0xbf06e0: r0 = Instance_Axis
    //     0xbf06e0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf06e4: stur            x2, [fp, #-0x30]
    // 0xbf06e8: StoreField: r2->field_f = r0
    //     0xbf06e8: stur            w0, [x2, #0xf]
    // 0xbf06ec: r3 = Instance_MainAxisAlignment
    //     0xbf06ec: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf06f0: ldr             x3, [x3, #0xa08]
    // 0xbf06f4: StoreField: r2->field_13 = r3
    //     0xbf06f4: stur            w3, [x2, #0x13]
    // 0xbf06f8: r4 = Instance_MainAxisSize
    //     0xbf06f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf06fc: ldr             x4, [x4, #0xa10]
    // 0xbf0700: ArrayStore: r2[0] = r4  ; List_4
    //     0xbf0700: stur            w4, [x2, #0x17]
    // 0xbf0704: r1 = Instance_CrossAxisAlignment
    //     0xbf0704: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbf0708: ldr             x1, [x1, #0x890]
    // 0xbf070c: StoreField: r2->field_1b = r1
    //     0xbf070c: stur            w1, [x2, #0x1b]
    // 0xbf0710: r5 = Instance_VerticalDirection
    //     0xbf0710: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf0714: ldr             x5, [x5, #0xa20]
    // 0xbf0718: StoreField: r2->field_23 = r5
    //     0xbf0718: stur            w5, [x2, #0x23]
    // 0xbf071c: r6 = Instance_Clip
    //     0xbf071c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf0720: ldr             x6, [x6, #0x38]
    // 0xbf0724: StoreField: r2->field_2b = r6
    //     0xbf0724: stur            w6, [x2, #0x2b]
    // 0xbf0728: StoreField: r2->field_2f = rZR
    //     0xbf0728: stur            xzr, [x2, #0x2f]
    // 0xbf072c: ldur            x1, [fp, #-0x38]
    // 0xbf0730: StoreField: r2->field_b = r1
    //     0xbf0730: stur            w1, [x2, #0xb]
    // 0xbf0734: ldur            x7, [fp, #-0x28]
    // 0xbf0738: LoadField: r1 = r7->field_b
    //     0xbf0738: ldur            w1, [x7, #0xb]
    // 0xbf073c: LoadField: r8 = r7->field_f
    //     0xbf073c: ldur            w8, [x7, #0xf]
    // 0xbf0740: DecompressPointer r8
    //     0xbf0740: add             x8, x8, HEAP, lsl #32
    // 0xbf0744: LoadField: r9 = r8->field_b
    //     0xbf0744: ldur            w9, [x8, #0xb]
    // 0xbf0748: r8 = LoadInt32Instr(r1)
    //     0xbf0748: sbfx            x8, x1, #1, #0x1f
    // 0xbf074c: stur            x8, [fp, #-0x50]
    // 0xbf0750: r1 = LoadInt32Instr(r9)
    //     0xbf0750: sbfx            x1, x9, #1, #0x1f
    // 0xbf0754: cmp             x8, x1
    // 0xbf0758: b.ne            #0xbf0764
    // 0xbf075c: mov             x1, x7
    // 0xbf0760: r0 = _growToNextCapacity()
    //     0xbf0760: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbf0764: ldur            x2, [fp, #-0x28]
    // 0xbf0768: ldur            x3, [fp, #-0x50]
    // 0xbf076c: add             x0, x3, #1
    // 0xbf0770: lsl             x1, x0, #1
    // 0xbf0774: StoreField: r2->field_b = r1
    //     0xbf0774: stur            w1, [x2, #0xb]
    // 0xbf0778: LoadField: r1 = r2->field_f
    //     0xbf0778: ldur            w1, [x2, #0xf]
    // 0xbf077c: DecompressPointer r1
    //     0xbf077c: add             x1, x1, HEAP, lsl #32
    // 0xbf0780: ldur            x0, [fp, #-0x30]
    // 0xbf0784: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbf0784: add             x25, x1, x3, lsl #2
    //     0xbf0788: add             x25, x25, #0xf
    //     0xbf078c: str             w0, [x25]
    //     0xbf0790: tbz             w0, #0, #0xbf07ac
    //     0xbf0794: ldurb           w16, [x1, #-1]
    //     0xbf0798: ldurb           w17, [x0, #-1]
    //     0xbf079c: and             x16, x17, x16, lsr #2
    //     0xbf07a0: tst             x16, HEAP, lsr #32
    //     0xbf07a4: b.eq            #0xbf07ac
    //     0xbf07a8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf07ac: ldur            x1, [fp, #-8]
    // 0xbf07b0: LoadField: r0 = r1->field_b
    //     0xbf07b0: ldur            w0, [x1, #0xb]
    // 0xbf07b4: DecompressPointer r0
    //     0xbf07b4: add             x0, x0, HEAP, lsl #32
    // 0xbf07b8: cmp             w0, NULL
    // 0xbf07bc: b.eq            #0xbf0d8c
    // 0xbf07c0: LoadField: r3 = r0->field_2f
    //     0xbf07c0: ldur            w3, [x0, #0x2f]
    // 0xbf07c4: DecompressPointer r3
    //     0xbf07c4: add             x3, x3, HEAP, lsl #32
    // 0xbf07c8: cmp             w3, NULL
    // 0xbf07cc: b.ne            #0xbf07d8
    // 0xbf07d0: r0 = Null
    //     0xbf07d0: mov             x0, NULL
    // 0xbf07d4: b               #0xbf0804
    // 0xbf07d8: LoadField: r0 = r3->field_7
    //     0xbf07d8: ldur            w0, [x3, #7]
    // 0xbf07dc: DecompressPointer r0
    //     0xbf07dc: add             x0, x0, HEAP, lsl #32
    // 0xbf07e0: cmp             w0, NULL
    // 0xbf07e4: b.ne            #0xbf07f0
    // 0xbf07e8: r0 = Null
    //     0xbf07e8: mov             x0, NULL
    // 0xbf07ec: b               #0xbf0804
    // 0xbf07f0: LoadField: r4 = r0->field_7
    //     0xbf07f0: ldur            w4, [x0, #7]
    // 0xbf07f4: cbnz            w4, #0xbf0800
    // 0xbf07f8: r0 = false
    //     0xbf07f8: add             x0, NULL, #0x30  ; false
    // 0xbf07fc: b               #0xbf0804
    // 0xbf0800: r0 = true
    //     0xbf0800: add             x0, NULL, #0x20  ; true
    // 0xbf0804: cmp             w0, NULL
    // 0xbf0808: b.ne            #0xbf0814
    // 0xbf080c: r4 = false
    //     0xbf080c: add             x4, NULL, #0x30  ; false
    // 0xbf0810: b               #0xbf0818
    // 0xbf0814: mov             x4, x0
    // 0xbf0818: stur            x4, [fp, #-0x30]
    // 0xbf081c: cmp             w3, NULL
    // 0xbf0820: b.ne            #0xbf0834
    // 0xbf0824: mov             x1, x2
    // 0xbf0828: mov             x0, x4
    // 0xbf082c: r2 = Null
    //     0xbf082c: mov             x2, NULL
    // 0xbf0830: b               #0xbf0874
    // 0xbf0834: LoadField: r0 = r3->field_7
    //     0xbf0834: ldur            w0, [x3, #7]
    // 0xbf0838: DecompressPointer r0
    //     0xbf0838: add             x0, x0, HEAP, lsl #32
    // 0xbf083c: cmp             w0, NULL
    // 0xbf0840: b.ne            #0xbf084c
    // 0xbf0844: r0 = Null
    //     0xbf0844: mov             x0, NULL
    // 0xbf0848: b               #0xbf0868
    // 0xbf084c: r3 = LoadClassIdInstr(r0)
    //     0xbf084c: ldur            x3, [x0, #-1]
    //     0xbf0850: ubfx            x3, x3, #0xc, #0x14
    // 0xbf0854: str             x0, [SP]
    // 0xbf0858: mov             x0, x3
    // 0xbf085c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbf085c: sub             lr, x0, #1, lsl #12
    //     0xbf0860: ldr             lr, [x21, lr, lsl #3]
    //     0xbf0864: blr             lr
    // 0xbf0868: mov             x2, x0
    // 0xbf086c: ldur            x1, [fp, #-0x28]
    // 0xbf0870: ldur            x0, [fp, #-0x30]
    // 0xbf0874: str             x2, [SP]
    // 0xbf0878: r0 = _interpolateSingle()
    //     0xbf0878: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbf087c: ldur            x1, [fp, #-0x10]
    // 0xbf0880: stur            x0, [fp, #-0x38]
    // 0xbf0884: r0 = of()
    //     0xbf0884: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf0888: LoadField: r1 = r0->field_87
    //     0xbf0888: ldur            w1, [x0, #0x87]
    // 0xbf088c: DecompressPointer r1
    //     0xbf088c: add             x1, x1, HEAP, lsl #32
    // 0xbf0890: LoadField: r0 = r1->field_2b
    //     0xbf0890: ldur            w0, [x1, #0x2b]
    // 0xbf0894: DecompressPointer r0
    //     0xbf0894: add             x0, x0, HEAP, lsl #32
    // 0xbf0898: stur            x0, [fp, #-0x40]
    // 0xbf089c: r1 = Instance_Color
    //     0xbf089c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf08a0: d0 = 0.700000
    //     0xbf08a0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbf08a4: ldr             d0, [x17, #0xf48]
    // 0xbf08a8: r0 = withOpacity()
    //     0xbf08a8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbf08ac: r16 = 12.000000
    //     0xbf08ac: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf08b0: ldr             x16, [x16, #0x9e8]
    // 0xbf08b4: stp             x0, x16, [SP, #8]
    // 0xbf08b8: r16 = Instance_TextDecoration
    //     0xbf08b8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xbf08bc: ldr             x16, [x16, #0x10]
    // 0xbf08c0: str             x16, [SP]
    // 0xbf08c4: ldur            x1, [fp, #-0x40]
    // 0xbf08c8: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xbf08c8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xbf08cc: ldr             x4, [x4, #0xe38]
    // 0xbf08d0: r0 = copyWith()
    //     0xbf08d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf08d4: stur            x0, [fp, #-0x40]
    // 0xbf08d8: r0 = Text()
    //     0xbf08d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf08dc: mov             x1, x0
    // 0xbf08e0: ldur            x0, [fp, #-0x38]
    // 0xbf08e4: stur            x1, [fp, #-0x48]
    // 0xbf08e8: StoreField: r1->field_b = r0
    //     0xbf08e8: stur            w0, [x1, #0xb]
    // 0xbf08ec: ldur            x0, [fp, #-0x40]
    // 0xbf08f0: StoreField: r1->field_13 = r0
    //     0xbf08f0: stur            w0, [x1, #0x13]
    // 0xbf08f4: r0 = Visibility()
    //     0xbf08f4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbf08f8: mov             x1, x0
    // 0xbf08fc: ldur            x0, [fp, #-0x48]
    // 0xbf0900: stur            x1, [fp, #-0x38]
    // 0xbf0904: StoreField: r1->field_b = r0
    //     0xbf0904: stur            w0, [x1, #0xb]
    // 0xbf0908: r0 = Instance_SizedBox
    //     0xbf0908: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbf090c: StoreField: r1->field_f = r0
    //     0xbf090c: stur            w0, [x1, #0xf]
    // 0xbf0910: ldur            x0, [fp, #-0x30]
    // 0xbf0914: StoreField: r1->field_13 = r0
    //     0xbf0914: stur            w0, [x1, #0x13]
    // 0xbf0918: r0 = false
    //     0xbf0918: add             x0, NULL, #0x30  ; false
    // 0xbf091c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbf091c: stur            w0, [x1, #0x17]
    // 0xbf0920: StoreField: r1->field_1b = r0
    //     0xbf0920: stur            w0, [x1, #0x1b]
    // 0xbf0924: StoreField: r1->field_1f = r0
    //     0xbf0924: stur            w0, [x1, #0x1f]
    // 0xbf0928: StoreField: r1->field_23 = r0
    //     0xbf0928: stur            w0, [x1, #0x23]
    // 0xbf092c: StoreField: r1->field_27 = r0
    //     0xbf092c: stur            w0, [x1, #0x27]
    // 0xbf0930: StoreField: r1->field_2b = r0
    //     0xbf0930: stur            w0, [x1, #0x2b]
    // 0xbf0934: r0 = InkWell()
    //     0xbf0934: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbf0938: mov             x3, x0
    // 0xbf093c: ldur            x0, [fp, #-0x38]
    // 0xbf0940: stur            x3, [fp, #-0x30]
    // 0xbf0944: StoreField: r3->field_b = r0
    //     0xbf0944: stur            w0, [x3, #0xb]
    // 0xbf0948: ldur            x2, [fp, #-0x20]
    // 0xbf094c: r1 = Function '<anonymous closure>':.
    //     0xbf094c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53518] AnonymousClosure: (0xbf2d2c), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xbf0494)
    //     0xbf0950: ldr             x1, [x1, #0x518]
    // 0xbf0954: r0 = AllocateClosure()
    //     0xbf0954: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf0958: mov             x1, x0
    // 0xbf095c: ldur            x0, [fp, #-0x30]
    // 0xbf0960: StoreField: r0->field_f = r1
    //     0xbf0960: stur            w1, [x0, #0xf]
    // 0xbf0964: r1 = true
    //     0xbf0964: add             x1, NULL, #0x20  ; true
    // 0xbf0968: StoreField: r0->field_43 = r1
    //     0xbf0968: stur            w1, [x0, #0x43]
    // 0xbf096c: r2 = Instance_BoxShape
    //     0xbf096c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbf0970: ldr             x2, [x2, #0x80]
    // 0xbf0974: StoreField: r0->field_47 = r2
    //     0xbf0974: stur            w2, [x0, #0x47]
    // 0xbf0978: StoreField: r0->field_6f = r1
    //     0xbf0978: stur            w1, [x0, #0x6f]
    // 0xbf097c: r2 = false
    //     0xbf097c: add             x2, NULL, #0x30  ; false
    // 0xbf0980: StoreField: r0->field_73 = r2
    //     0xbf0980: stur            w2, [x0, #0x73]
    // 0xbf0984: StoreField: r0->field_83 = r1
    //     0xbf0984: stur            w1, [x0, #0x83]
    // 0xbf0988: StoreField: r0->field_7b = r2
    //     0xbf0988: stur            w2, [x0, #0x7b]
    // 0xbf098c: ldur            x2, [fp, #-0x28]
    // 0xbf0990: LoadField: r1 = r2->field_b
    //     0xbf0990: ldur            w1, [x2, #0xb]
    // 0xbf0994: LoadField: r3 = r2->field_f
    //     0xbf0994: ldur            w3, [x2, #0xf]
    // 0xbf0998: DecompressPointer r3
    //     0xbf0998: add             x3, x3, HEAP, lsl #32
    // 0xbf099c: LoadField: r4 = r3->field_b
    //     0xbf099c: ldur            w4, [x3, #0xb]
    // 0xbf09a0: r3 = LoadInt32Instr(r1)
    //     0xbf09a0: sbfx            x3, x1, #1, #0x1f
    // 0xbf09a4: stur            x3, [fp, #-0x50]
    // 0xbf09a8: r1 = LoadInt32Instr(r4)
    //     0xbf09a8: sbfx            x1, x4, #1, #0x1f
    // 0xbf09ac: cmp             x3, x1
    // 0xbf09b0: b.ne            #0xbf09bc
    // 0xbf09b4: mov             x1, x2
    // 0xbf09b8: r0 = _growToNextCapacity()
    //     0xbf09b8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbf09bc: ldur            x2, [fp, #-0x28]
    // 0xbf09c0: ldur            x3, [fp, #-0x50]
    // 0xbf09c4: add             x4, x3, #1
    // 0xbf09c8: stur            x4, [fp, #-0x58]
    // 0xbf09cc: lsl             x0, x4, #1
    // 0xbf09d0: StoreField: r2->field_b = r0
    //     0xbf09d0: stur            w0, [x2, #0xb]
    // 0xbf09d4: LoadField: r5 = r2->field_f
    //     0xbf09d4: ldur            w5, [x2, #0xf]
    // 0xbf09d8: DecompressPointer r5
    //     0xbf09d8: add             x5, x5, HEAP, lsl #32
    // 0xbf09dc: mov             x1, x5
    // 0xbf09e0: ldur            x0, [fp, #-0x30]
    // 0xbf09e4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbf09e4: add             x25, x1, x3, lsl #2
    //     0xbf09e8: add             x25, x25, #0xf
    //     0xbf09ec: str             w0, [x25]
    //     0xbf09f0: tbz             w0, #0, #0xbf0a0c
    //     0xbf09f4: ldurb           w16, [x1, #-1]
    //     0xbf09f8: ldurb           w17, [x0, #-1]
    //     0xbf09fc: and             x16, x17, x16, lsr #2
    //     0xbf0a00: tst             x16, HEAP, lsr #32
    //     0xbf0a04: b.eq            #0xbf0a0c
    //     0xbf0a08: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf0a0c: LoadField: r0 = r5->field_b
    //     0xbf0a0c: ldur            w0, [x5, #0xb]
    // 0xbf0a10: r1 = LoadInt32Instr(r0)
    //     0xbf0a10: sbfx            x1, x0, #1, #0x1f
    // 0xbf0a14: cmp             x4, x1
    // 0xbf0a18: b.ne            #0xbf0a24
    // 0xbf0a1c: mov             x1, x2
    // 0xbf0a20: r0 = _growToNextCapacity()
    //     0xbf0a20: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbf0a24: ldur            x3, [fp, #-8]
    // 0xbf0a28: ldur            x0, [fp, #-0x28]
    // 0xbf0a2c: ldur            x1, [fp, #-0x58]
    // 0xbf0a30: add             x2, x1, #1
    // 0xbf0a34: lsl             x4, x2, #1
    // 0xbf0a38: StoreField: r0->field_b = r4
    //     0xbf0a38: stur            w4, [x0, #0xb]
    // 0xbf0a3c: LoadField: r2 = r0->field_f
    //     0xbf0a3c: ldur            w2, [x0, #0xf]
    // 0xbf0a40: DecompressPointer r2
    //     0xbf0a40: add             x2, x2, HEAP, lsl #32
    // 0xbf0a44: add             x4, x2, x1, lsl #2
    // 0xbf0a48: r16 = Instance_SizedBox
    //     0xbf0a48: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xbf0a4c: ldr             x16, [x16, #0x9f0]
    // 0xbf0a50: StoreField: r4->field_f = r16
    //     0xbf0a50: stur            w16, [x4, #0xf]
    // 0xbf0a54: ArrayLoad: r2 = r3[0]  ; List_8
    //     0xbf0a54: ldur            x2, [x3, #0x17]
    // 0xbf0a58: mov             x1, x3
    // 0xbf0a5c: r0 = _calculateCardHeight()
    //     0xbf0a5c: bl              #0xbf0db8  ; [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_calculateCardHeight
    // 0xbf0a60: ldur            x0, [fp, #-8]
    // 0xbf0a64: stur            d0, [fp, #-0x60]
    // 0xbf0a68: LoadField: r1 = r0->field_b
    //     0xbf0a68: ldur            w1, [x0, #0xb]
    // 0xbf0a6c: DecompressPointer r1
    //     0xbf0a6c: add             x1, x1, HEAP, lsl #32
    // 0xbf0a70: cmp             w1, NULL
    // 0xbf0a74: b.eq            #0xbf0d90
    // 0xbf0a78: LoadField: r2 = r1->field_b
    //     0xbf0a78: ldur            w2, [x1, #0xb]
    // 0xbf0a7c: DecompressPointer r2
    //     0xbf0a7c: add             x2, x2, HEAP, lsl #32
    // 0xbf0a80: cmp             w2, NULL
    // 0xbf0a84: b.ne            #0xbf0a90
    // 0xbf0a88: r4 = Null
    //     0xbf0a88: mov             x4, NULL
    // 0xbf0a8c: b               #0xbf0a98
    // 0xbf0a90: LoadField: r1 = r2->field_b
    //     0xbf0a90: ldur            w1, [x2, #0xb]
    // 0xbf0a94: mov             x4, x1
    // 0xbf0a98: ldur            x3, [fp, #-0x28]
    // 0xbf0a9c: stur            x4, [fp, #-0x38]
    // 0xbf0aa0: LoadField: r5 = r0->field_13
    //     0xbf0aa0: ldur            w5, [x0, #0x13]
    // 0xbf0aa4: DecompressPointer r5
    //     0xbf0aa4: add             x5, x5, HEAP, lsl #32
    // 0xbf0aa8: r16 = Sentinel
    //     0xbf0aa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbf0aac: cmp             w5, w16
    // 0xbf0ab0: b.eq            #0xbf0d94
    // 0xbf0ab4: ldur            x2, [fp, #-0x20]
    // 0xbf0ab8: stur            x5, [fp, #-0x30]
    // 0xbf0abc: r1 = Function '<anonymous closure>':.
    //     0xbf0abc: add             x1, PP, #0x53, lsl #12  ; [pp+0x53520] AnonymousClosure: (0xbf2c1c), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xbf0494)
    //     0xbf0ac0: ldr             x1, [x1, #0x520]
    // 0xbf0ac4: r0 = AllocateClosure()
    //     0xbf0ac4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf0ac8: ldur            x2, [fp, #-0x20]
    // 0xbf0acc: r1 = Function '<anonymous closure>':.
    //     0xbf0acc: add             x1, PP, #0x53, lsl #12  ; [pp+0x53528] AnonymousClosure: (0xbf1098), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xbf0494)
    //     0xbf0ad0: ldr             x1, [x1, #0x528]
    // 0xbf0ad4: stur            x0, [fp, #-0x20]
    // 0xbf0ad8: r0 = AllocateClosure()
    //     0xbf0ad8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf0adc: stur            x0, [fp, #-0x40]
    // 0xbf0ae0: r0 = PageView()
    //     0xbf0ae0: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xbf0ae4: stur            x0, [fp, #-0x48]
    // 0xbf0ae8: r16 = Instance_BouncingScrollPhysics
    //     0xbf0ae8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xbf0aec: ldr             x16, [x16, #0x890]
    // 0xbf0af0: ldur            lr, [fp, #-0x30]
    // 0xbf0af4: stp             lr, x16, [SP]
    // 0xbf0af8: mov             x1, x0
    // 0xbf0afc: ldur            x2, [fp, #-0x40]
    // 0xbf0b00: ldur            x3, [fp, #-0x38]
    // 0xbf0b04: ldur            x5, [fp, #-0x20]
    // 0xbf0b08: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xbf0b08: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xbf0b0c: ldr             x4, [x4, #0xe40]
    // 0xbf0b10: r0 = PageView.builder()
    //     0xbf0b10: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xbf0b14: ldur            d0, [fp, #-0x60]
    // 0xbf0b18: r0 = inline_Allocate_Double()
    //     0xbf0b18: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbf0b1c: add             x0, x0, #0x10
    //     0xbf0b20: cmp             x1, x0
    //     0xbf0b24: b.ls            #0xbf0da0
    //     0xbf0b28: str             x0, [THR, #0x50]  ; THR::top
    //     0xbf0b2c: sub             x0, x0, #0xf
    //     0xbf0b30: movz            x1, #0xe15c
    //     0xbf0b34: movk            x1, #0x3, lsl #16
    //     0xbf0b38: stur            x1, [x0, #-1]
    // 0xbf0b3c: StoreField: r0->field_7 = d0
    //     0xbf0b3c: stur            d0, [x0, #7]
    // 0xbf0b40: stur            x0, [fp, #-0x20]
    // 0xbf0b44: r0 = SizedBox()
    //     0xbf0b44: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbf0b48: mov             x2, x0
    // 0xbf0b4c: ldur            x0, [fp, #-0x20]
    // 0xbf0b50: stur            x2, [fp, #-0x30]
    // 0xbf0b54: StoreField: r2->field_13 = r0
    //     0xbf0b54: stur            w0, [x2, #0x13]
    // 0xbf0b58: ldur            x0, [fp, #-0x48]
    // 0xbf0b5c: StoreField: r2->field_b = r0
    //     0xbf0b5c: stur            w0, [x2, #0xb]
    // 0xbf0b60: ldur            x0, [fp, #-0x28]
    // 0xbf0b64: LoadField: r1 = r0->field_b
    //     0xbf0b64: ldur            w1, [x0, #0xb]
    // 0xbf0b68: LoadField: r3 = r0->field_f
    //     0xbf0b68: ldur            w3, [x0, #0xf]
    // 0xbf0b6c: DecompressPointer r3
    //     0xbf0b6c: add             x3, x3, HEAP, lsl #32
    // 0xbf0b70: LoadField: r4 = r3->field_b
    //     0xbf0b70: ldur            w4, [x3, #0xb]
    // 0xbf0b74: r3 = LoadInt32Instr(r1)
    //     0xbf0b74: sbfx            x3, x1, #1, #0x1f
    // 0xbf0b78: stur            x3, [fp, #-0x50]
    // 0xbf0b7c: r1 = LoadInt32Instr(r4)
    //     0xbf0b7c: sbfx            x1, x4, #1, #0x1f
    // 0xbf0b80: cmp             x3, x1
    // 0xbf0b84: b.ne            #0xbf0b90
    // 0xbf0b88: mov             x1, x0
    // 0xbf0b8c: r0 = _growToNextCapacity()
    //     0xbf0b8c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbf0b90: ldur            x4, [fp, #-8]
    // 0xbf0b94: ldur            x2, [fp, #-0x28]
    // 0xbf0b98: ldur            x3, [fp, #-0x50]
    // 0xbf0b9c: add             x0, x3, #1
    // 0xbf0ba0: lsl             x1, x0, #1
    // 0xbf0ba4: StoreField: r2->field_b = r1
    //     0xbf0ba4: stur            w1, [x2, #0xb]
    // 0xbf0ba8: LoadField: r1 = r2->field_f
    //     0xbf0ba8: ldur            w1, [x2, #0xf]
    // 0xbf0bac: DecompressPointer r1
    //     0xbf0bac: add             x1, x1, HEAP, lsl #32
    // 0xbf0bb0: ldur            x0, [fp, #-0x30]
    // 0xbf0bb4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbf0bb4: add             x25, x1, x3, lsl #2
    //     0xbf0bb8: add             x25, x25, #0xf
    //     0xbf0bbc: str             w0, [x25]
    //     0xbf0bc0: tbz             w0, #0, #0xbf0bdc
    //     0xbf0bc4: ldurb           w16, [x1, #-1]
    //     0xbf0bc8: ldurb           w17, [x0, #-1]
    //     0xbf0bcc: and             x16, x17, x16, lsr #2
    //     0xbf0bd0: tst             x16, HEAP, lsr #32
    //     0xbf0bd4: b.eq            #0xbf0bdc
    //     0xbf0bd8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf0bdc: LoadField: r0 = r4->field_b
    //     0xbf0bdc: ldur            w0, [x4, #0xb]
    // 0xbf0be0: DecompressPointer r0
    //     0xbf0be0: add             x0, x0, HEAP, lsl #32
    // 0xbf0be4: cmp             w0, NULL
    // 0xbf0be8: b.eq            #0xbf0db0
    // 0xbf0bec: LoadField: r1 = r0->field_b
    //     0xbf0bec: ldur            w1, [x0, #0xb]
    // 0xbf0bf0: DecompressPointer r1
    //     0xbf0bf0: add             x1, x1, HEAP, lsl #32
    // 0xbf0bf4: cmp             w1, NULL
    // 0xbf0bf8: b.eq            #0xbf0db4
    // 0xbf0bfc: LoadField: r0 = r1->field_b
    //     0xbf0bfc: ldur            w0, [x1, #0xb]
    // 0xbf0c00: r1 = LoadInt32Instr(r0)
    //     0xbf0c00: sbfx            x1, x0, #1, #0x1f
    // 0xbf0c04: cmp             x1, #1
    // 0xbf0c08: b.le            #0xbf0cf8
    // 0xbf0c0c: r3 = LoadInt32Instr(r0)
    //     0xbf0c0c: sbfx            x3, x0, #1, #0x1f
    // 0xbf0c10: stur            x3, [fp, #-0x58]
    // 0xbf0c14: ArrayLoad: r0 = r4[0]  ; List_8
    //     0xbf0c14: ldur            x0, [x4, #0x17]
    // 0xbf0c18: ldur            x1, [fp, #-0x10]
    // 0xbf0c1c: stur            x0, [fp, #-0x50]
    // 0xbf0c20: r0 = of()
    //     0xbf0c20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf0c24: LoadField: r1 = r0->field_5b
    //     0xbf0c24: ldur            w1, [x0, #0x5b]
    // 0xbf0c28: DecompressPointer r1
    //     0xbf0c28: add             x1, x1, HEAP, lsl #32
    // 0xbf0c2c: stur            x1, [fp, #-8]
    // 0xbf0c30: r0 = CarouselIndicator()
    //     0xbf0c30: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xbf0c34: mov             x1, x0
    // 0xbf0c38: ldur            x0, [fp, #-0x58]
    // 0xbf0c3c: stur            x1, [fp, #-0x10]
    // 0xbf0c40: StoreField: r1->field_b = r0
    //     0xbf0c40: stur            x0, [x1, #0xb]
    // 0xbf0c44: ldur            x0, [fp, #-0x50]
    // 0xbf0c48: StoreField: r1->field_13 = r0
    //     0xbf0c48: stur            x0, [x1, #0x13]
    // 0xbf0c4c: ldur            x0, [fp, #-8]
    // 0xbf0c50: StoreField: r1->field_1b = r0
    //     0xbf0c50: stur            w0, [x1, #0x1b]
    // 0xbf0c54: r0 = Instance_Color
    //     0xbf0c54: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xbf0c58: ldr             x0, [x0, #0x90]
    // 0xbf0c5c: StoreField: r1->field_1f = r0
    //     0xbf0c5c: stur            w0, [x1, #0x1f]
    // 0xbf0c60: r0 = Padding()
    //     0xbf0c60: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf0c64: mov             x2, x0
    // 0xbf0c68: r0 = Instance_EdgeInsets
    //     0xbf0c68: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xbf0c6c: ldr             x0, [x0, #0xa00]
    // 0xbf0c70: stur            x2, [fp, #-8]
    // 0xbf0c74: StoreField: r2->field_f = r0
    //     0xbf0c74: stur            w0, [x2, #0xf]
    // 0xbf0c78: ldur            x0, [fp, #-0x10]
    // 0xbf0c7c: StoreField: r2->field_b = r0
    //     0xbf0c7c: stur            w0, [x2, #0xb]
    // 0xbf0c80: ldur            x0, [fp, #-0x28]
    // 0xbf0c84: LoadField: r1 = r0->field_b
    //     0xbf0c84: ldur            w1, [x0, #0xb]
    // 0xbf0c88: LoadField: r3 = r0->field_f
    //     0xbf0c88: ldur            w3, [x0, #0xf]
    // 0xbf0c8c: DecompressPointer r3
    //     0xbf0c8c: add             x3, x3, HEAP, lsl #32
    // 0xbf0c90: LoadField: r4 = r3->field_b
    //     0xbf0c90: ldur            w4, [x3, #0xb]
    // 0xbf0c94: r3 = LoadInt32Instr(r1)
    //     0xbf0c94: sbfx            x3, x1, #1, #0x1f
    // 0xbf0c98: stur            x3, [fp, #-0x50]
    // 0xbf0c9c: r1 = LoadInt32Instr(r4)
    //     0xbf0c9c: sbfx            x1, x4, #1, #0x1f
    // 0xbf0ca0: cmp             x3, x1
    // 0xbf0ca4: b.ne            #0xbf0cb0
    // 0xbf0ca8: mov             x1, x0
    // 0xbf0cac: r0 = _growToNextCapacity()
    //     0xbf0cac: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbf0cb0: ldur            x2, [fp, #-0x28]
    // 0xbf0cb4: ldur            x3, [fp, #-0x50]
    // 0xbf0cb8: add             x0, x3, #1
    // 0xbf0cbc: lsl             x1, x0, #1
    // 0xbf0cc0: StoreField: r2->field_b = r1
    //     0xbf0cc0: stur            w1, [x2, #0xb]
    // 0xbf0cc4: LoadField: r1 = r2->field_f
    //     0xbf0cc4: ldur            w1, [x2, #0xf]
    // 0xbf0cc8: DecompressPointer r1
    //     0xbf0cc8: add             x1, x1, HEAP, lsl #32
    // 0xbf0ccc: ldur            x0, [fp, #-8]
    // 0xbf0cd0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbf0cd0: add             x25, x1, x3, lsl #2
    //     0xbf0cd4: add             x25, x25, #0xf
    //     0xbf0cd8: str             w0, [x25]
    //     0xbf0cdc: tbz             w0, #0, #0xbf0cf8
    //     0xbf0ce0: ldurb           w16, [x1, #-1]
    //     0xbf0ce4: ldurb           w17, [x0, #-1]
    //     0xbf0ce8: and             x16, x17, x16, lsr #2
    //     0xbf0cec: tst             x16, HEAP, lsr #32
    //     0xbf0cf0: b.eq            #0xbf0cf8
    //     0xbf0cf4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf0cf8: ldur            x0, [fp, #-0x18]
    // 0xbf0cfc: r0 = Column()
    //     0xbf0cfc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf0d00: mov             x1, x0
    // 0xbf0d04: r0 = Instance_Axis
    //     0xbf0d04: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf0d08: stur            x1, [fp, #-8]
    // 0xbf0d0c: StoreField: r1->field_f = r0
    //     0xbf0d0c: stur            w0, [x1, #0xf]
    // 0xbf0d10: r0 = Instance_MainAxisAlignment
    //     0xbf0d10: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf0d14: ldr             x0, [x0, #0xa08]
    // 0xbf0d18: StoreField: r1->field_13 = r0
    //     0xbf0d18: stur            w0, [x1, #0x13]
    // 0xbf0d1c: r0 = Instance_MainAxisSize
    //     0xbf0d1c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf0d20: ldr             x0, [x0, #0xa10]
    // 0xbf0d24: ArrayStore: r1[0] = r0  ; List_4
    //     0xbf0d24: stur            w0, [x1, #0x17]
    // 0xbf0d28: ldur            x0, [fp, #-0x18]
    // 0xbf0d2c: StoreField: r1->field_1b = r0
    //     0xbf0d2c: stur            w0, [x1, #0x1b]
    // 0xbf0d30: r0 = Instance_VerticalDirection
    //     0xbf0d30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf0d34: ldr             x0, [x0, #0xa20]
    // 0xbf0d38: StoreField: r1->field_23 = r0
    //     0xbf0d38: stur            w0, [x1, #0x23]
    // 0xbf0d3c: r0 = Instance_Clip
    //     0xbf0d3c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf0d40: ldr             x0, [x0, #0x38]
    // 0xbf0d44: StoreField: r1->field_2b = r0
    //     0xbf0d44: stur            w0, [x1, #0x2b]
    // 0xbf0d48: StoreField: r1->field_2f = rZR
    //     0xbf0d48: stur            xzr, [x1, #0x2f]
    // 0xbf0d4c: ldur            x0, [fp, #-0x28]
    // 0xbf0d50: StoreField: r1->field_b = r0
    //     0xbf0d50: stur            w0, [x1, #0xb]
    // 0xbf0d54: r0 = Padding()
    //     0xbf0d54: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf0d58: r1 = Instance_EdgeInsets
    //     0xbf0d58: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xbf0d5c: ldr             x1, [x1, #0x110]
    // 0xbf0d60: StoreField: r0->field_f = r1
    //     0xbf0d60: stur            w1, [x0, #0xf]
    // 0xbf0d64: ldur            x1, [fp, #-8]
    // 0xbf0d68: StoreField: r0->field_b = r1
    //     0xbf0d68: stur            w1, [x0, #0xb]
    // 0xbf0d6c: LeaveFrame
    //     0xbf0d6c: mov             SP, fp
    //     0xbf0d70: ldp             fp, lr, [SP], #0x10
    // 0xbf0d74: ret
    //     0xbf0d74: ret             
    // 0xbf0d78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf0d78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf0d7c: b               #0xbf04bc
    // 0xbf0d80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf0d80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf0d84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf0d84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf0d88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf0d88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf0d8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf0d8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf0d90: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbf0d90: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbf0d94: r9 = _pageController
    //     0xbf0d94: add             x9, PP, #0x53, lsl #12  ; [pp+0x53530] Field <_TestimonialCarouselState@1708398649._pageController@1708398649>: late (offset: 0x14)
    //     0xbf0d98: ldr             x9, [x9, #0x530]
    // 0xbf0d9c: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xbf0d9c: bl              #0x16f7c00  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xbf0da0: SaveReg d0
    //     0xbf0da0: str             q0, [SP, #-0x10]!
    // 0xbf0da4: r0 = AllocateDouble()
    //     0xbf0da4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbf0da8: RestoreReg d0
    //     0xbf0da8: ldr             q0, [SP], #0x10
    // 0xbf0dac: b               #0xbf0b3c
    // 0xbf0db0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf0db0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf0db4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf0db4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _calculateCardHeight(/* No info */) {
    // ** addr: 0xbf0db8, size: 0x2e0
    // 0xbf0db8: EnterFrame
    //     0xbf0db8: stp             fp, lr, [SP, #-0x10]!
    //     0xbf0dbc: mov             fp, SP
    // 0xbf0dc0: AllocStack(0x20)
    //     0xbf0dc0: sub             SP, SP, #0x20
    // 0xbf0dc4: SetupParameters(_TestimonialCarouselState this /* r1 => r3 */)
    //     0xbf0dc4: mov             x3, x1
    // 0xbf0dc8: CheckStackOverflow
    //     0xbf0dc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf0dcc: cmp             SP, x16
    //     0xbf0dd0: b.ls            #0xbf1054
    // 0xbf0dd4: LoadField: r0 = r3->field_b
    //     0xbf0dd4: ldur            w0, [x3, #0xb]
    // 0xbf0dd8: DecompressPointer r0
    //     0xbf0dd8: add             x0, x0, HEAP, lsl #32
    // 0xbf0ddc: cmp             w0, NULL
    // 0xbf0de0: b.eq            #0xbf105c
    // 0xbf0de4: LoadField: r4 = r0->field_b
    //     0xbf0de4: ldur            w4, [x0, #0xb]
    // 0xbf0de8: DecompressPointer r4
    //     0xbf0de8: add             x4, x4, HEAP, lsl #32
    // 0xbf0dec: cmp             w4, NULL
    // 0xbf0df0: b.ne            #0xbf0dfc
    // 0xbf0df4: r0 = Null
    //     0xbf0df4: mov             x0, NULL
    // 0xbf0df8: b               #0xbf0e00
    // 0xbf0dfc: LoadField: r0 = r4->field_b
    //     0xbf0dfc: ldur            w0, [x4, #0xb]
    // 0xbf0e00: cmp             w0, NULL
    // 0xbf0e04: b.ne            #0xbf0e10
    // 0xbf0e08: r0 = 0
    //     0xbf0e08: movz            x0, #0
    // 0xbf0e0c: b               #0xbf0e18
    // 0xbf0e10: r1 = LoadInt32Instr(r0)
    //     0xbf0e10: sbfx            x1, x0, #1, #0x1f
    // 0xbf0e14: mov             x0, x1
    // 0xbf0e18: cmp             x2, x0
    // 0xbf0e1c: b.lt            #0xbf0e30
    // 0xbf0e20: d0 = 400.000000
    //     0xbf0e20: ldr             d0, [PP, #0x5a28]  ; [pp+0x5a28] IMM: double(400) from 0x4079000000000000
    // 0xbf0e24: LeaveFrame
    //     0xbf0e24: mov             SP, fp
    //     0xbf0e28: ldp             fp, lr, [SP], #0x10
    // 0xbf0e2c: ret
    //     0xbf0e2c: ret             
    // 0xbf0e30: cmp             w4, NULL
    // 0xbf0e34: b.eq            #0xbf1060
    // 0xbf0e38: LoadField: r0 = r4->field_b
    //     0xbf0e38: ldur            w0, [x4, #0xb]
    // 0xbf0e3c: r1 = LoadInt32Instr(r0)
    //     0xbf0e3c: sbfx            x1, x0, #1, #0x1f
    // 0xbf0e40: mov             x0, x1
    // 0xbf0e44: mov             x1, x2
    // 0xbf0e48: cmp             x1, x0
    // 0xbf0e4c: b.hs            #0xbf1064
    // 0xbf0e50: LoadField: r5 = r4->field_f
    //     0xbf0e50: ldur            w5, [x4, #0xf]
    // 0xbf0e54: DecompressPointer r5
    //     0xbf0e54: add             x5, x5, HEAP, lsl #32
    // 0xbf0e58: r0 = BoxInt64Instr(r2)
    //     0xbf0e58: sbfiz           x0, x2, #1, #0x1f
    //     0xbf0e5c: cmp             x2, x0, asr #1
    //     0xbf0e60: b.eq            #0xbf0e6c
    //     0xbf0e64: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf0e68: stur            x2, [x0, #7]
    // 0xbf0e6c: ArrayLoad: r4 = r5[r2]  ; Unknown_4
    //     0xbf0e6c: add             x16, x5, x2, lsl #2
    //     0xbf0e70: ldur            w4, [x16, #0xf]
    // 0xbf0e74: DecompressPointer r4
    //     0xbf0e74: add             x4, x4, HEAP, lsl #32
    // 0xbf0e78: stur            x4, [fp, #-0x18]
    // 0xbf0e7c: LoadField: r1 = r4->field_ab
    //     0xbf0e7c: ldur            w1, [x4, #0xab]
    // 0xbf0e80: DecompressPointer r1
    //     0xbf0e80: add             x1, x1, HEAP, lsl #32
    // 0xbf0e84: cmp             w1, NULL
    // 0xbf0e88: b.ne            #0xbf0e94
    // 0xbf0e8c: r1 = Null
    //     0xbf0e8c: mov             x1, NULL
    // 0xbf0e90: b               #0xbf0ea8
    // 0xbf0e94: LoadField: r2 = r1->field_b
    //     0xbf0e94: ldur            w2, [x1, #0xb]
    // 0xbf0e98: cbnz            w2, #0xbf0ea4
    // 0xbf0e9c: r1 = false
    //     0xbf0e9c: add             x1, NULL, #0x30  ; false
    // 0xbf0ea0: b               #0xbf0ea8
    // 0xbf0ea4: r1 = true
    //     0xbf0ea4: add             x1, NULL, #0x20  ; true
    // 0xbf0ea8: cmp             w1, NULL
    // 0xbf0eac: b.ne            #0xbf0eb8
    // 0xbf0eb0: r5 = false
    //     0xbf0eb0: add             x5, NULL, #0x30  ; false
    // 0xbf0eb4: b               #0xbf0ebc
    // 0xbf0eb8: mov             x5, x1
    // 0xbf0ebc: stur            x5, [fp, #-0x10]
    // 0xbf0ec0: LoadField: r6 = r3->field_1f
    //     0xbf0ec0: ldur            w6, [x3, #0x1f]
    // 0xbf0ec4: DecompressPointer r6
    //     0xbf0ec4: add             x6, x6, HEAP, lsl #32
    // 0xbf0ec8: mov             x1, x6
    // 0xbf0ecc: mov             x2, x0
    // 0xbf0ed0: stur            x6, [fp, #-8]
    // 0xbf0ed4: r0 = _getValueOrData()
    //     0xbf0ed4: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xbf0ed8: mov             x1, x0
    // 0xbf0edc: ldur            x0, [fp, #-8]
    // 0xbf0ee0: LoadField: r2 = r0->field_f
    //     0xbf0ee0: ldur            w2, [x0, #0xf]
    // 0xbf0ee4: DecompressPointer r2
    //     0xbf0ee4: add             x2, x2, HEAP, lsl #32
    // 0xbf0ee8: cmp             w2, w1
    // 0xbf0eec: b.ne            #0xbf0ef4
    // 0xbf0ef0: r1 = Null
    //     0xbf0ef0: mov             x1, NULL
    // 0xbf0ef4: cmp             w1, NULL
    // 0xbf0ef8: b.ne            #0xbf0f04
    // 0xbf0efc: r0 = Null
    //     0xbf0efc: mov             x0, NULL
    // 0xbf0f00: b               #0xbf0f08
    // 0xbf0f04: r0 = value()
    //     0xbf0f04: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf0f08: cmp             w0, NULL
    // 0xbf0f0c: b.ne            #0xbf0f18
    // 0xbf0f10: r2 = false
    //     0xbf0f10: add             x2, NULL, #0x30  ; false
    // 0xbf0f14: b               #0xbf0f1c
    // 0xbf0f18: mov             x2, x0
    // 0xbf0f1c: ldur            x0, [fp, #-0x18]
    // 0xbf0f20: stur            x2, [fp, #-8]
    // 0xbf0f24: LoadField: r1 = r0->field_9f
    //     0xbf0f24: ldur            w1, [x0, #0x9f]
    // 0xbf0f28: DecompressPointer r1
    //     0xbf0f28: add             x1, x1, HEAP, lsl #32
    // 0xbf0f2c: cmp             w1, NULL
    // 0xbf0f30: b.ne            #0xbf0f3c
    // 0xbf0f34: r0 = Null
    //     0xbf0f34: mov             x0, NULL
    // 0xbf0f38: b               #0xbf0f48
    // 0xbf0f3c: r0 = trim()
    //     0xbf0f3c: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xbf0f40: LoadField: r1 = r0->field_7
    //     0xbf0f40: ldur            w1, [x0, #7]
    // 0xbf0f44: mov             x0, x1
    // 0xbf0f48: cmp             w0, NULL
    // 0xbf0f4c: b.ne            #0xbf0f58
    // 0xbf0f50: r1 = 0
    //     0xbf0f50: movz            x1, #0
    // 0xbf0f54: b               #0xbf0f5c
    // 0xbf0f58: r1 = LoadInt32Instr(r0)
    //     0xbf0f58: sbfx            x1, x0, #1, #0x1f
    // 0xbf0f5c: ldur            x0, [fp, #-0x10]
    // 0xbf0f60: tbnz            w0, #4, #0xbf0f70
    // 0xbf0f64: d0 = 300.000000
    //     0xbf0f64: add             x17, PP, #0x4d, lsl #12  ; [pp+0x4d6d0] IMM: double(300) from 0x4072c00000000000
    //     0xbf0f68: ldr             d0, [x17, #0x6d0]
    // 0xbf0f6c: b               #0xbf0f74
    // 0xbf0f70: d0 = 100.000000
    //     0xbf0f70: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xbf0f74: ldur            x0, [fp, #-8]
    // 0xbf0f78: tbnz            w0, #4, #0xbf0fa0
    // 0xbf0f7c: d2 = 40.000000
    //     0xbf0f7c: ldr             d2, [PP, #0x5a38]  ; [pp+0x5a38] IMM: double(40) from 0x4044000000000000
    // 0xbf0f80: d1 = 35.000000
    //     0xbf0f80: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3eec8] IMM: double(35) from 0x4041800000000000
    //     0xbf0f84: ldr             d1, [x17, #0xec8]
    // 0xbf0f88: scvtf           d3, x1
    // 0xbf0f8c: fdiv            d4, d3, d2
    // 0xbf0f90: fmul            d2, d4, d1
    // 0xbf0f94: fadd            d1, d0, d2
    // 0xbf0f98: mov             v0.16b, v1.16b
    // 0xbf0f9c: b               #0xbf0fb0
    // 0xbf0fa0: d1 = 200.000000
    //     0xbf0fa0: add             x17, PP, #0x37, lsl #12  ; [pp+0x37360] IMM: double(200) from 0x4069000000000000
    //     0xbf0fa4: ldr             d1, [x17, #0x360]
    // 0xbf0fa8: fadd            d2, d0, d1
    // 0xbf0fac: mov             v0.16b, v2.16b
    // 0xbf0fb0: stur            d0, [fp, #-0x20]
    // 0xbf0fb4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbf0fb4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbf0fb8: ldr             x0, [x0, #0x1c80]
    //     0xbf0fbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbf0fc0: cmp             w0, w16
    //     0xbf0fc4: b.ne            #0xbf0fd0
    //     0xbf0fc8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbf0fcc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbf0fd0: r0 = GetNavigation.size()
    //     0xbf0fd0: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbf0fd4: LoadField: d0 = r0->field_f
    //     0xbf0fd4: ldur            d0, [x0, #0xf]
    // 0xbf0fd8: d1 = 0.950000
    //     0xbf0fd8: add             x17, PP, #0x51, lsl #12  ; [pp+0x51e58] IMM: double(0.95) from 0x3fee666666666666
    //     0xbf0fdc: ldr             d1, [x17, #0xe58]
    // 0xbf0fe0: fmul            d2, d0, d1
    // 0xbf0fe4: ldur            d0, [fp, #-0x20]
    // 0xbf0fe8: r1 = inline_Allocate_Double()
    //     0xbf0fe8: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xbf0fec: add             x1, x1, #0x10
    //     0xbf0ff0: cmp             x0, x1
    //     0xbf0ff4: b.ls            #0xbf1068
    //     0xbf0ff8: str             x1, [THR, #0x50]  ; THR::top
    //     0xbf0ffc: sub             x1, x1, #0xf
    //     0xbf1000: movz            x0, #0xe15c
    //     0xbf1004: movk            x0, #0x3, lsl #16
    //     0xbf1008: stur            x0, [x1, #-1]
    // 0xbf100c: StoreField: r1->field_7 = d0
    //     0xbf100c: stur            d0, [x1, #7]
    // 0xbf1010: r3 = inline_Allocate_Double()
    //     0xbf1010: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0xbf1014: add             x3, x3, #0x10
    //     0xbf1018: cmp             x0, x3
    //     0xbf101c: b.ls            #0xbf107c
    //     0xbf1020: str             x3, [THR, #0x50]  ; THR::top
    //     0xbf1024: sub             x3, x3, #0xf
    //     0xbf1028: movz            x0, #0xe15c
    //     0xbf102c: movk            x0, #0x3, lsl #16
    //     0xbf1030: stur            x0, [x3, #-1]
    // 0xbf1034: StoreField: r3->field_7 = d2
    //     0xbf1034: stur            d2, [x3, #7]
    // 0xbf1038: r2 = 200.000000
    //     0xbf1038: add             x2, PP, #0x48, lsl #12  ; [pp+0x48570] 200
    //     0xbf103c: ldr             x2, [x2, #0x570]
    // 0xbf1040: r0 = clamp()
    //     0xbf1040: bl              #0x748b58  ; [dart:core] _Double::clamp
    // 0xbf1044: LoadField: d0 = r0->field_7
    //     0xbf1044: ldur            d0, [x0, #7]
    // 0xbf1048: LeaveFrame
    //     0xbf1048: mov             SP, fp
    //     0xbf104c: ldp             fp, lr, [SP], #0x10
    // 0xbf1050: ret
    //     0xbf1050: ret             
    // 0xbf1054: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf1054: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf1058: b               #0xbf0dd4
    // 0xbf105c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf105c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf1060: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf1060: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf1064: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf1064: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf1068: stp             q0, q2, [SP, #-0x20]!
    // 0xbf106c: r0 = AllocateDouble()
    //     0xbf106c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbf1070: mov             x1, x0
    // 0xbf1074: ldp             q0, q2, [SP], #0x20
    // 0xbf1078: b               #0xbf100c
    // 0xbf107c: SaveReg d2
    //     0xbf107c: str             q2, [SP, #-0x10]!
    // 0xbf1080: SaveReg r1
    //     0xbf1080: str             x1, [SP, #-8]!
    // 0xbf1084: r0 = AllocateDouble()
    //     0xbf1084: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbf1088: mov             x3, x0
    // 0xbf108c: RestoreReg r1
    //     0xbf108c: ldr             x1, [SP], #8
    // 0xbf1090: RestoreReg d2
    //     0xbf1090: ldr             q2, [SP], #0x10
    // 0xbf1094: b               #0xbf1034
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbf1098, size: 0x94
    // 0xbf1098: EnterFrame
    //     0xbf1098: stp             fp, lr, [SP, #-0x10]!
    //     0xbf109c: mov             fp, SP
    // 0xbf10a0: AllocStack(0x8)
    //     0xbf10a0: sub             SP, SP, #8
    // 0xbf10a4: SetupParameters()
    //     0xbf10a4: ldr             x0, [fp, #0x20]
    //     0xbf10a8: ldur            w1, [x0, #0x17]
    //     0xbf10ac: add             x1, x1, HEAP, lsl #32
    // 0xbf10b0: CheckStackOverflow
    //     0xbf10b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf10b4: cmp             SP, x16
    //     0xbf10b8: b.ls            #0xbf1120
    // 0xbf10bc: LoadField: r0 = r1->field_f
    //     0xbf10bc: ldur            w0, [x1, #0xf]
    // 0xbf10c0: DecompressPointer r0
    //     0xbf10c0: add             x0, x0, HEAP, lsl #32
    // 0xbf10c4: stur            x0, [fp, #-8]
    // 0xbf10c8: LoadField: r1 = r0->field_b
    //     0xbf10c8: ldur            w1, [x0, #0xb]
    // 0xbf10cc: DecompressPointer r1
    //     0xbf10cc: add             x1, x1, HEAP, lsl #32
    // 0xbf10d0: cmp             w1, NULL
    // 0xbf10d4: b.eq            #0xbf1128
    // 0xbf10d8: LoadField: r2 = r1->field_b
    //     0xbf10d8: ldur            w2, [x1, #0xb]
    // 0xbf10dc: DecompressPointer r2
    //     0xbf10dc: add             x2, x2, HEAP, lsl #32
    // 0xbf10e0: cmp             w2, NULL
    // 0xbf10e4: b.ne            #0xbf10fc
    // 0xbf10e8: r1 = <Entity>
    //     0xbf10e8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23b68] TypeArguments: <Entity>
    //     0xbf10ec: ldr             x1, [x1, #0xb68]
    // 0xbf10f0: r2 = 0
    //     0xbf10f0: movz            x2, #0
    // 0xbf10f4: r0 = AllocateArray()
    //     0xbf10f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf10f8: mov             x2, x0
    // 0xbf10fc: ldr             x0, [fp, #0x10]
    // 0xbf1100: r3 = LoadInt32Instr(r0)
    //     0xbf1100: sbfx            x3, x0, #1, #0x1f
    //     0xbf1104: tbz             w0, #0, #0xbf110c
    //     0xbf1108: ldur            x3, [x0, #7]
    // 0xbf110c: ldur            x1, [fp, #-8]
    // 0xbf1110: r0 = _testimonialWidget()
    //     0xbf1110: bl              #0xbf112c  ; [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_testimonialWidget
    // 0xbf1114: LeaveFrame
    //     0xbf1114: mov             SP, fp
    //     0xbf1118: ldp             fp, lr, [SP], #0x10
    // 0xbf111c: ret
    //     0xbf111c: ret             
    // 0xbf1120: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf1120: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf1124: b               #0xbf10bc
    // 0xbf1128: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf1128: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _testimonialWidget(/* No info */) {
    // ** addr: 0xbf112c, size: 0xf1c
    // 0xbf112c: EnterFrame
    //     0xbf112c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf1130: mov             fp, SP
    // 0xbf1134: AllocStack(0xa0)
    //     0xbf1134: sub             SP, SP, #0xa0
    // 0xbf1138: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xbf1138: stur            x1, [fp, #-8]
    //     0xbf113c: stur            x2, [fp, #-0x10]
    //     0xbf1140: stur            x3, [fp, #-0x18]
    // 0xbf1144: CheckStackOverflow
    //     0xbf1144: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf1148: cmp             SP, x16
    //     0xbf114c: b.ls            #0xbf2010
    // 0xbf1150: r1 = 2
    //     0xbf1150: movz            x1, #0x2
    // 0xbf1154: r0 = AllocateContext()
    //     0xbf1154: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf1158: mov             x3, x0
    // 0xbf115c: ldur            x2, [fp, #-8]
    // 0xbf1160: stur            x3, [fp, #-0x28]
    // 0xbf1164: StoreField: r3->field_f = r2
    //     0xbf1164: stur            w2, [x3, #0xf]
    // 0xbf1168: ldur            x4, [fp, #-0x18]
    // 0xbf116c: r0 = BoxInt64Instr(r4)
    //     0xbf116c: sbfiz           x0, x4, #1, #0x1f
    //     0xbf1170: cmp             x4, x0, asr #1
    //     0xbf1174: b.eq            #0xbf1180
    //     0xbf1178: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf117c: stur            x4, [x0, #7]
    // 0xbf1180: mov             x1, x0
    // 0xbf1184: ldur            x0, [fp, #-0x10]
    // 0xbf1188: stur            x1, [fp, #-0x20]
    // 0xbf118c: r4 = LoadClassIdInstr(r0)
    //     0xbf118c: ldur            x4, [x0, #-1]
    //     0xbf1190: ubfx            x4, x4, #0xc, #0x14
    // 0xbf1194: stp             x1, x0, [SP]
    // 0xbf1198: mov             x0, x4
    // 0xbf119c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbf119c: sub             lr, x0, #0xb7
    //     0xbf11a0: ldr             lr, [x21, lr, lsl #3]
    //     0xbf11a4: blr             lr
    // 0xbf11a8: stur            x0, [fp, #-0x38]
    // 0xbf11ac: LoadField: r1 = r0->field_ab
    //     0xbf11ac: ldur            w1, [x0, #0xab]
    // 0xbf11b0: DecompressPointer r1
    //     0xbf11b0: add             x1, x1, HEAP, lsl #32
    // 0xbf11b4: cmp             w1, NULL
    // 0xbf11b8: b.ne            #0xbf11c4
    // 0xbf11bc: r1 = Null
    //     0xbf11bc: mov             x1, NULL
    // 0xbf11c0: b               #0xbf11d8
    // 0xbf11c4: LoadField: r2 = r1->field_b
    //     0xbf11c4: ldur            w2, [x1, #0xb]
    // 0xbf11c8: cbnz            w2, #0xbf11d4
    // 0xbf11cc: r1 = false
    //     0xbf11cc: add             x1, NULL, #0x30  ; false
    // 0xbf11d0: b               #0xbf11d8
    // 0xbf11d4: r1 = true
    //     0xbf11d4: add             x1, NULL, #0x20  ; true
    // 0xbf11d8: cmp             w1, NULL
    // 0xbf11dc: b.ne            #0xbf11e8
    // 0xbf11e0: r4 = false
    //     0xbf11e0: add             x4, NULL, #0x30  ; false
    // 0xbf11e4: b               #0xbf11ec
    // 0xbf11e8: mov             x4, x1
    // 0xbf11ec: ldur            x3, [fp, #-8]
    // 0xbf11f0: stur            x4, [fp, #-0x30]
    // 0xbf11f4: LoadField: r5 = r3->field_1f
    //     0xbf11f4: ldur            w5, [x3, #0x1f]
    // 0xbf11f8: DecompressPointer r5
    //     0xbf11f8: add             x5, x5, HEAP, lsl #32
    // 0xbf11fc: mov             x1, x5
    // 0xbf1200: ldur            x2, [fp, #-0x20]
    // 0xbf1204: stur            x5, [fp, #-0x10]
    // 0xbf1208: r0 = _getValueOrData()
    //     0xbf1208: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xbf120c: mov             x1, x0
    // 0xbf1210: ldur            x0, [fp, #-0x10]
    // 0xbf1214: LoadField: r2 = r0->field_f
    //     0xbf1214: ldur            w2, [x0, #0xf]
    // 0xbf1218: DecompressPointer r2
    //     0xbf1218: add             x2, x2, HEAP, lsl #32
    // 0xbf121c: cmp             w2, w1
    // 0xbf1220: b.ne            #0xbf122c
    // 0xbf1224: r0 = Null
    //     0xbf1224: mov             x0, NULL
    // 0xbf1228: b               #0xbf1230
    // 0xbf122c: mov             x0, x1
    // 0xbf1230: cmp             w0, NULL
    // 0xbf1234: b.ne            #0xbf1274
    // 0xbf1238: r1 = <bool>
    //     0xbf1238: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0xbf123c: r0 = RxBool()
    //     0xbf123c: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0xbf1240: mov             x2, x0
    // 0xbf1244: r0 = Sentinel
    //     0xbf1244: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbf1248: stur            x2, [fp, #-0x10]
    // 0xbf124c: StoreField: r2->field_13 = r0
    //     0xbf124c: stur            w0, [x2, #0x13]
    // 0xbf1250: r0 = true
    //     0xbf1250: add             x0, NULL, #0x20  ; true
    // 0xbf1254: ArrayStore: r2[0] = r0  ; List_4
    //     0xbf1254: stur            w0, [x2, #0x17]
    // 0xbf1258: mov             x1, x2
    // 0xbf125c: r0 = RxNotifier()
    //     0xbf125c: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0xbf1260: ldur            x0, [fp, #-0x10]
    // 0xbf1264: r2 = false
    //     0xbf1264: add             x2, NULL, #0x30  ; false
    // 0xbf1268: StoreField: r0->field_13 = r2
    //     0xbf1268: stur            w2, [x0, #0x13]
    // 0xbf126c: mov             x5, x0
    // 0xbf1270: b               #0xbf127c
    // 0xbf1274: r2 = false
    //     0xbf1274: add             x2, NULL, #0x30  ; false
    // 0xbf1278: mov             x5, x0
    // 0xbf127c: ldur            x4, [fp, #-0x28]
    // 0xbf1280: ldur            x3, [fp, #-0x38]
    // 0xbf1284: mov             x0, x5
    // 0xbf1288: stur            x5, [fp, #-0x10]
    // 0xbf128c: StoreField: r4->field_13 = r0
    //     0xbf128c: stur            w0, [x4, #0x13]
    //     0xbf1290: ldurb           w16, [x4, #-1]
    //     0xbf1294: ldurb           w17, [x0, #-1]
    //     0xbf1298: and             x16, x17, x16, lsr #2
    //     0xbf129c: tst             x16, HEAP, lsr #32
    //     0xbf12a0: b.eq            #0xbf12a8
    //     0xbf12a4: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0xbf12a8: r1 = Instance_Color
    //     0xbf12a8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf12ac: d0 = 0.100000
    //     0xbf12ac: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbf12b0: r0 = withOpacity()
    //     0xbf12b0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbf12b4: mov             x2, x0
    // 0xbf12b8: r1 = Null
    //     0xbf12b8: mov             x1, NULL
    // 0xbf12bc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf12bc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf12c0: r0 = Border.all()
    //     0xbf12c0: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbf12c4: stur            x0, [fp, #-0x20]
    // 0xbf12c8: r0 = BoxDecoration()
    //     0xbf12c8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbf12cc: mov             x2, x0
    // 0xbf12d0: ldur            x0, [fp, #-0x20]
    // 0xbf12d4: stur            x2, [fp, #-0x40]
    // 0xbf12d8: StoreField: r2->field_f = r0
    //     0xbf12d8: stur            w0, [x2, #0xf]
    // 0xbf12dc: r0 = Instance_BoxShape
    //     0xbf12dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbf12e0: ldr             x0, [x0, #0x80]
    // 0xbf12e4: StoreField: r2->field_23 = r0
    //     0xbf12e4: stur            w0, [x2, #0x23]
    // 0xbf12e8: ldur            x0, [fp, #-0x38]
    // 0xbf12ec: LoadField: r1 = r0->field_97
    //     0xbf12ec: ldur            w1, [x0, #0x97]
    // 0xbf12f0: DecompressPointer r1
    //     0xbf12f0: add             x1, x1, HEAP, lsl #32
    // 0xbf12f4: cmp             w1, NULL
    // 0xbf12f8: b.ne            #0xbf1300
    // 0xbf12fc: r1 = ""
    //     0xbf12fc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf1300: ldur            x3, [fp, #-8]
    // 0xbf1304: r0 = capitalizeFirstWord()
    //     0xbf1304: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xbf1308: mov             x2, x0
    // 0xbf130c: ldur            x0, [fp, #-8]
    // 0xbf1310: stur            x2, [fp, #-0x20]
    // 0xbf1314: LoadField: r1 = r0->field_f
    //     0xbf1314: ldur            w1, [x0, #0xf]
    // 0xbf1318: DecompressPointer r1
    //     0xbf1318: add             x1, x1, HEAP, lsl #32
    // 0xbf131c: cmp             w1, NULL
    // 0xbf1320: b.eq            #0xbf2018
    // 0xbf1324: r0 = of()
    //     0xbf1324: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf1328: LoadField: r1 = r0->field_87
    //     0xbf1328: ldur            w1, [x0, #0x87]
    // 0xbf132c: DecompressPointer r1
    //     0xbf132c: add             x1, x1, HEAP, lsl #32
    // 0xbf1330: LoadField: r0 = r1->field_7
    //     0xbf1330: ldur            w0, [x1, #7]
    // 0xbf1334: DecompressPointer r0
    //     0xbf1334: add             x0, x0, HEAP, lsl #32
    // 0xbf1338: stur            x0, [fp, #-0x48]
    // 0xbf133c: r1 = Instance_Color
    //     0xbf133c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf1340: d0 = 0.700000
    //     0xbf1340: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbf1344: ldr             d0, [x17, #0xf48]
    // 0xbf1348: r0 = withOpacity()
    //     0xbf1348: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbf134c: r16 = 16.000000
    //     0xbf134c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbf1350: ldr             x16, [x16, #0x188]
    // 0xbf1354: stp             x0, x16, [SP]
    // 0xbf1358: ldur            x1, [fp, #-0x48]
    // 0xbf135c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf135c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf1360: ldr             x4, [x4, #0xaa0]
    // 0xbf1364: r0 = copyWith()
    //     0xbf1364: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf1368: stur            x0, [fp, #-0x48]
    // 0xbf136c: r0 = Text()
    //     0xbf136c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf1370: mov             x3, x0
    // 0xbf1374: ldur            x0, [fp, #-0x20]
    // 0xbf1378: stur            x3, [fp, #-0x50]
    // 0xbf137c: StoreField: r3->field_b = r0
    //     0xbf137c: stur            w0, [x3, #0xb]
    // 0xbf1380: ldur            x0, [fp, #-0x48]
    // 0xbf1384: StoreField: r3->field_13 = r0
    //     0xbf1384: stur            w0, [x3, #0x13]
    // 0xbf1388: r1 = <Widget>
    //     0xbf1388: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf138c: r2 = 18
    //     0xbf138c: movz            x2, #0x12
    // 0xbf1390: r0 = AllocateArray()
    //     0xbf1390: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf1394: stur            x0, [fp, #-0x20]
    // 0xbf1398: r16 = Instance_Icon
    //     0xbf1398: add             x16, PP, #0x52, lsl #12  ; [pp+0x520d0] Obj!Icon@d66371
    //     0xbf139c: ldr             x16, [x16, #0xd0]
    // 0xbf13a0: StoreField: r0->field_f = r16
    //     0xbf13a0: stur            w16, [x0, #0xf]
    // 0xbf13a4: r16 = Instance_SizedBox
    //     0xbf13a4: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xbf13a8: ldr             x16, [x16, #0xe98]
    // 0xbf13ac: StoreField: r0->field_13 = r16
    //     0xbf13ac: stur            w16, [x0, #0x13]
    // 0xbf13b0: ldur            x2, [fp, #-8]
    // 0xbf13b4: LoadField: r1 = r2->field_f
    //     0xbf13b4: ldur            w1, [x2, #0xf]
    // 0xbf13b8: DecompressPointer r1
    //     0xbf13b8: add             x1, x1, HEAP, lsl #32
    // 0xbf13bc: cmp             w1, NULL
    // 0xbf13c0: b.eq            #0xbf201c
    // 0xbf13c4: r0 = of()
    //     0xbf13c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf13c8: LoadField: r1 = r0->field_87
    //     0xbf13c8: ldur            w1, [x0, #0x87]
    // 0xbf13cc: DecompressPointer r1
    //     0xbf13cc: add             x1, x1, HEAP, lsl #32
    // 0xbf13d0: LoadField: r0 = r1->field_2b
    //     0xbf13d0: ldur            w0, [x1, #0x2b]
    // 0xbf13d4: DecompressPointer r0
    //     0xbf13d4: add             x0, x0, HEAP, lsl #32
    // 0xbf13d8: stur            x0, [fp, #-0x48]
    // 0xbf13dc: r1 = Instance_Color
    //     0xbf13dc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf13e0: d0 = 0.700000
    //     0xbf13e0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbf13e4: ldr             d0, [x17, #0xf48]
    // 0xbf13e8: r0 = withOpacity()
    //     0xbf13e8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbf13ec: r16 = 12.000000
    //     0xbf13ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf13f0: ldr             x16, [x16, #0x9e8]
    // 0xbf13f4: stp             x0, x16, [SP]
    // 0xbf13f8: ldur            x1, [fp, #-0x48]
    // 0xbf13fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf13fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf1400: ldr             x4, [x4, #0xaa0]
    // 0xbf1404: r0 = copyWith()
    //     0xbf1404: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf1408: stur            x0, [fp, #-0x48]
    // 0xbf140c: r0 = Text()
    //     0xbf140c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf1410: mov             x1, x0
    // 0xbf1414: r0 = "Verified Buyer"
    //     0xbf1414: add             x0, PP, #0x52, lsl #12  ; [pp+0x520d8] "Verified Buyer"
    //     0xbf1418: ldr             x0, [x0, #0xd8]
    // 0xbf141c: StoreField: r1->field_b = r0
    //     0xbf141c: stur            w0, [x1, #0xb]
    // 0xbf1420: ldur            x0, [fp, #-0x48]
    // 0xbf1424: StoreField: r1->field_13 = r0
    //     0xbf1424: stur            w0, [x1, #0x13]
    // 0xbf1428: mov             x0, x1
    // 0xbf142c: ldur            x1, [fp, #-0x20]
    // 0xbf1430: ArrayStore: r1[2] = r0  ; List_4
    //     0xbf1430: add             x25, x1, #0x17
    //     0xbf1434: str             w0, [x25]
    //     0xbf1438: tbz             w0, #0, #0xbf1454
    //     0xbf143c: ldurb           w16, [x1, #-1]
    //     0xbf1440: ldurb           w17, [x0, #-1]
    //     0xbf1444: and             x16, x17, x16, lsr #2
    //     0xbf1448: tst             x16, HEAP, lsr #32
    //     0xbf144c: b.eq            #0xbf1454
    //     0xbf1450: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf1454: r0 = Container()
    //     0xbf1454: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf1458: stur            x0, [fp, #-0x48]
    // 0xbf145c: r16 = 5.000000
    //     0xbf145c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xbf1460: ldr             x16, [x16, #0xcf0]
    // 0xbf1464: str             x16, [SP]
    // 0xbf1468: mov             x1, x0
    // 0xbf146c: r4 = const [0, 0x2, 0x1, 0x1, width, 0x1, null]
    //     0xbf146c: add             x4, PP, #0x52, lsl #12  ; [pp+0x520e0] List(7) [0, 0x2, 0x1, 0x1, "width", 0x1, Null]
    //     0xbf1470: ldr             x4, [x4, #0xe0]
    // 0xbf1474: r0 = Container()
    //     0xbf1474: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf1478: ldur            x1, [fp, #-0x20]
    // 0xbf147c: ldur            x0, [fp, #-0x48]
    // 0xbf1480: ArrayStore: r1[3] = r0  ; List_4
    //     0xbf1480: add             x25, x1, #0x1b
    //     0xbf1484: str             w0, [x25]
    //     0xbf1488: tbz             w0, #0, #0xbf14a4
    //     0xbf148c: ldurb           w16, [x1, #-1]
    //     0xbf1490: ldurb           w17, [x0, #-1]
    //     0xbf1494: and             x16, x17, x16, lsr #2
    //     0xbf1498: tst             x16, HEAP, lsr #32
    //     0xbf149c: b.eq            #0xbf14a4
    //     0xbf14a0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf14a4: ldur            x2, [fp, #-0x38]
    // 0xbf14a8: LoadField: r0 = r2->field_a3
    //     0xbf14a8: ldur            w0, [x2, #0xa3]
    // 0xbf14ac: DecompressPointer r0
    //     0xbf14ac: add             x0, x0, HEAP, lsl #32
    // 0xbf14b0: cmp             w0, NULL
    // 0xbf14b4: b.eq            #0xbf14d4
    // 0xbf14b8: LoadField: r1 = r0->field_7
    //     0xbf14b8: ldur            w1, [x0, #7]
    // 0xbf14bc: cbnz            w1, #0xbf14c8
    // 0xbf14c0: r0 = false
    //     0xbf14c0: add             x0, NULL, #0x30  ; false
    // 0xbf14c4: b               #0xbf14cc
    // 0xbf14c8: r0 = true
    //     0xbf14c8: add             x0, NULL, #0x20  ; true
    // 0xbf14cc: mov             x3, x0
    // 0xbf14d0: b               #0xbf14d8
    // 0xbf14d4: r3 = false
    //     0xbf14d4: add             x3, NULL, #0x30  ; false
    // 0xbf14d8: ldur            x0, [fp, #-8]
    // 0xbf14dc: stur            x3, [fp, #-0x48]
    // 0xbf14e0: LoadField: r1 = r0->field_f
    //     0xbf14e0: ldur            w1, [x0, #0xf]
    // 0xbf14e4: DecompressPointer r1
    //     0xbf14e4: add             x1, x1, HEAP, lsl #32
    // 0xbf14e8: cmp             w1, NULL
    // 0xbf14ec: b.eq            #0xbf2020
    // 0xbf14f0: r0 = of()
    //     0xbf14f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf14f4: LoadField: r1 = r0->field_5b
    //     0xbf14f4: ldur            w1, [x0, #0x5b]
    // 0xbf14f8: DecompressPointer r1
    //     0xbf14f8: add             x1, x1, HEAP, lsl #32
    // 0xbf14fc: stur            x1, [fp, #-0x58]
    // 0xbf1500: r0 = BoxDecoration()
    //     0xbf1500: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbf1504: mov             x1, x0
    // 0xbf1508: ldur            x0, [fp, #-0x58]
    // 0xbf150c: stur            x1, [fp, #-0x60]
    // 0xbf1510: StoreField: r1->field_7 = r0
    //     0xbf1510: stur            w0, [x1, #7]
    // 0xbf1514: r0 = Instance_BoxShape
    //     0xbf1514: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xbf1518: ldr             x0, [x0, #0x970]
    // 0xbf151c: StoreField: r1->field_23 = r0
    //     0xbf151c: stur            w0, [x1, #0x23]
    // 0xbf1520: r0 = Container()
    //     0xbf1520: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf1524: stur            x0, [fp, #-0x58]
    // 0xbf1528: r16 = Instance_EdgeInsets
    //     0xbf1528: add             x16, PP, #0x52, lsl #12  ; [pp+0x52108] Obj!EdgeInsets@d579b1
    //     0xbf152c: ldr             x16, [x16, #0x108]
    // 0xbf1530: r30 = 5.000000
    //     0xbf1530: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xbf1534: ldr             lr, [lr, #0xcf0]
    // 0xbf1538: stp             lr, x16, [SP, #0x10]
    // 0xbf153c: r16 = 5.000000
    //     0xbf153c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xbf1540: ldr             x16, [x16, #0xcf0]
    // 0xbf1544: ldur            lr, [fp, #-0x60]
    // 0xbf1548: stp             lr, x16, [SP]
    // 0xbf154c: mov             x1, x0
    // 0xbf1550: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x3, margin, 0x1, width, 0x2, null]
    //     0xbf1550: add             x4, PP, #0x52, lsl #12  ; [pp+0x52118] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xbf1554: ldr             x4, [x4, #0x118]
    // 0xbf1558: r0 = Container()
    //     0xbf1558: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf155c: r0 = Visibility()
    //     0xbf155c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbf1560: mov             x1, x0
    // 0xbf1564: ldur            x0, [fp, #-0x58]
    // 0xbf1568: StoreField: r1->field_b = r0
    //     0xbf1568: stur            w0, [x1, #0xb]
    // 0xbf156c: r0 = Instance_SizedBox
    //     0xbf156c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbf1570: StoreField: r1->field_f = r0
    //     0xbf1570: stur            w0, [x1, #0xf]
    // 0xbf1574: ldur            x0, [fp, #-0x48]
    // 0xbf1578: StoreField: r1->field_13 = r0
    //     0xbf1578: stur            w0, [x1, #0x13]
    // 0xbf157c: r2 = false
    //     0xbf157c: add             x2, NULL, #0x30  ; false
    // 0xbf1580: ArrayStore: r1[0] = r2  ; List_4
    //     0xbf1580: stur            w2, [x1, #0x17]
    // 0xbf1584: StoreField: r1->field_1b = r2
    //     0xbf1584: stur            w2, [x1, #0x1b]
    // 0xbf1588: StoreField: r1->field_1f = r2
    //     0xbf1588: stur            w2, [x1, #0x1f]
    // 0xbf158c: StoreField: r1->field_23 = r2
    //     0xbf158c: stur            w2, [x1, #0x23]
    // 0xbf1590: StoreField: r1->field_27 = r2
    //     0xbf1590: stur            w2, [x1, #0x27]
    // 0xbf1594: StoreField: r1->field_2b = r2
    //     0xbf1594: stur            w2, [x1, #0x2b]
    // 0xbf1598: mov             x0, x1
    // 0xbf159c: ldur            x1, [fp, #-0x20]
    // 0xbf15a0: ArrayStore: r1[4] = r0  ; List_4
    //     0xbf15a0: add             x25, x1, #0x1f
    //     0xbf15a4: str             w0, [x25]
    //     0xbf15a8: tbz             w0, #0, #0xbf15c4
    //     0xbf15ac: ldurb           w16, [x1, #-1]
    //     0xbf15b0: ldurb           w17, [x0, #-1]
    //     0xbf15b4: and             x16, x17, x16, lsr #2
    //     0xbf15b8: tst             x16, HEAP, lsr #32
    //     0xbf15bc: b.eq            #0xbf15c4
    //     0xbf15c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf15c4: ldur            x0, [fp, #-0x38]
    // 0xbf15c8: LoadField: r1 = r0->field_a3
    //     0xbf15c8: ldur            w1, [x0, #0xa3]
    // 0xbf15cc: DecompressPointer r1
    //     0xbf15cc: add             x1, x1, HEAP, lsl #32
    // 0xbf15d0: cmp             w1, NULL
    // 0xbf15d4: b.ne            #0xbf15e0
    // 0xbf15d8: r4 = ""
    //     0xbf15d8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf15dc: b               #0xbf15e4
    // 0xbf15e0: mov             x4, x1
    // 0xbf15e4: ldur            x3, [fp, #-8]
    // 0xbf15e8: stur            x4, [fp, #-0x48]
    // 0xbf15ec: LoadField: r1 = r3->field_f
    //     0xbf15ec: ldur            w1, [x3, #0xf]
    // 0xbf15f0: DecompressPointer r1
    //     0xbf15f0: add             x1, x1, HEAP, lsl #32
    // 0xbf15f4: cmp             w1, NULL
    // 0xbf15f8: b.eq            #0xbf2024
    // 0xbf15fc: r0 = of()
    //     0xbf15fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf1600: LoadField: r1 = r0->field_87
    //     0xbf1600: ldur            w1, [x0, #0x87]
    // 0xbf1604: DecompressPointer r1
    //     0xbf1604: add             x1, x1, HEAP, lsl #32
    // 0xbf1608: LoadField: r0 = r1->field_2b
    //     0xbf1608: ldur            w0, [x1, #0x2b]
    // 0xbf160c: DecompressPointer r0
    //     0xbf160c: add             x0, x0, HEAP, lsl #32
    // 0xbf1610: stur            x0, [fp, #-0x58]
    // 0xbf1614: r1 = Instance_Color
    //     0xbf1614: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf1618: d0 = 0.700000
    //     0xbf1618: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbf161c: ldr             d0, [x17, #0xf48]
    // 0xbf1620: r0 = withOpacity()
    //     0xbf1620: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbf1624: r16 = 12.000000
    //     0xbf1624: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf1628: ldr             x16, [x16, #0x9e8]
    // 0xbf162c: stp             x0, x16, [SP]
    // 0xbf1630: ldur            x1, [fp, #-0x58]
    // 0xbf1634: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf1634: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf1638: ldr             x4, [x4, #0xaa0]
    // 0xbf163c: r0 = copyWith()
    //     0xbf163c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf1640: stur            x0, [fp, #-0x58]
    // 0xbf1644: r0 = Text()
    //     0xbf1644: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf1648: mov             x1, x0
    // 0xbf164c: ldur            x0, [fp, #-0x48]
    // 0xbf1650: StoreField: r1->field_b = r0
    //     0xbf1650: stur            w0, [x1, #0xb]
    // 0xbf1654: ldur            x0, [fp, #-0x58]
    // 0xbf1658: StoreField: r1->field_13 = r0
    //     0xbf1658: stur            w0, [x1, #0x13]
    // 0xbf165c: mov             x0, x1
    // 0xbf1660: ldur            x1, [fp, #-0x20]
    // 0xbf1664: ArrayStore: r1[5] = r0  ; List_4
    //     0xbf1664: add             x25, x1, #0x23
    //     0xbf1668: str             w0, [x25]
    //     0xbf166c: tbz             w0, #0, #0xbf1688
    //     0xbf1670: ldurb           w16, [x1, #-1]
    //     0xbf1674: ldurb           w17, [x0, #-1]
    //     0xbf1678: and             x16, x17, x16, lsr #2
    //     0xbf167c: tst             x16, HEAP, lsr #32
    //     0xbf1680: b.eq            #0xbf1688
    //     0xbf1684: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf1688: r0 = Container()
    //     0xbf1688: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf168c: stur            x0, [fp, #-0x48]
    // 0xbf1690: r16 = 5.000000
    //     0xbf1690: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xbf1694: ldr             x16, [x16, #0xcf0]
    // 0xbf1698: str             x16, [SP]
    // 0xbf169c: mov             x1, x0
    // 0xbf16a0: r4 = const [0, 0x2, 0x1, 0x1, width, 0x1, null]
    //     0xbf16a0: add             x4, PP, #0x52, lsl #12  ; [pp+0x520e0] List(7) [0, 0x2, 0x1, 0x1, "width", 0x1, Null]
    //     0xbf16a4: ldr             x4, [x4, #0xe0]
    // 0xbf16a8: r0 = Container()
    //     0xbf16a8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf16ac: ldur            x1, [fp, #-0x20]
    // 0xbf16b0: ldur            x0, [fp, #-0x48]
    // 0xbf16b4: ArrayStore: r1[6] = r0  ; List_4
    //     0xbf16b4: add             x25, x1, #0x27
    //     0xbf16b8: str             w0, [x25]
    //     0xbf16bc: tbz             w0, #0, #0xbf16d8
    //     0xbf16c0: ldurb           w16, [x1, #-1]
    //     0xbf16c4: ldurb           w17, [x0, #-1]
    //     0xbf16c8: and             x16, x17, x16, lsr #2
    //     0xbf16cc: tst             x16, HEAP, lsr #32
    //     0xbf16d0: b.eq            #0xbf16d8
    //     0xbf16d4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf16d8: ldur            x0, [fp, #-8]
    // 0xbf16dc: LoadField: r1 = r0->field_f
    //     0xbf16dc: ldur            w1, [x0, #0xf]
    // 0xbf16e0: DecompressPointer r1
    //     0xbf16e0: add             x1, x1, HEAP, lsl #32
    // 0xbf16e4: cmp             w1, NULL
    // 0xbf16e8: b.eq            #0xbf2028
    // 0xbf16ec: r0 = of()
    //     0xbf16ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf16f0: LoadField: r1 = r0->field_5b
    //     0xbf16f0: ldur            w1, [x0, #0x5b]
    // 0xbf16f4: DecompressPointer r1
    //     0xbf16f4: add             x1, x1, HEAP, lsl #32
    // 0xbf16f8: stur            x1, [fp, #-0x48]
    // 0xbf16fc: r0 = BoxDecoration()
    //     0xbf16fc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbf1700: mov             x1, x0
    // 0xbf1704: ldur            x0, [fp, #-0x48]
    // 0xbf1708: stur            x1, [fp, #-0x58]
    // 0xbf170c: StoreField: r1->field_7 = r0
    //     0xbf170c: stur            w0, [x1, #7]
    // 0xbf1710: r0 = Instance_BoxShape
    //     0xbf1710: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xbf1714: ldr             x0, [x0, #0x970]
    // 0xbf1718: StoreField: r1->field_23 = r0
    //     0xbf1718: stur            w0, [x1, #0x23]
    // 0xbf171c: r0 = Container()
    //     0xbf171c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf1720: stur            x0, [fp, #-0x48]
    // 0xbf1724: r16 = Instance_EdgeInsets
    //     0xbf1724: add             x16, PP, #0x52, lsl #12  ; [pp+0x52108] Obj!EdgeInsets@d579b1
    //     0xbf1728: ldr             x16, [x16, #0x108]
    // 0xbf172c: r30 = 5.000000
    //     0xbf172c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xbf1730: ldr             lr, [lr, #0xcf0]
    // 0xbf1734: stp             lr, x16, [SP, #0x10]
    // 0xbf1738: r16 = 5.000000
    //     0xbf1738: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xbf173c: ldr             x16, [x16, #0xcf0]
    // 0xbf1740: ldur            lr, [fp, #-0x58]
    // 0xbf1744: stp             lr, x16, [SP]
    // 0xbf1748: mov             x1, x0
    // 0xbf174c: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x3, margin, 0x1, width, 0x2, null]
    //     0xbf174c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52118] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xbf1750: ldr             x4, [x4, #0x118]
    // 0xbf1754: r0 = Container()
    //     0xbf1754: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf1758: ldur            x1, [fp, #-0x20]
    // 0xbf175c: ldur            x0, [fp, #-0x48]
    // 0xbf1760: ArrayStore: r1[7] = r0  ; List_4
    //     0xbf1760: add             x25, x1, #0x2b
    //     0xbf1764: str             w0, [x25]
    //     0xbf1768: tbz             w0, #0, #0xbf1784
    //     0xbf176c: ldurb           w16, [x1, #-1]
    //     0xbf1770: ldurb           w17, [x0, #-1]
    //     0xbf1774: and             x16, x17, x16, lsr #2
    //     0xbf1778: tst             x16, HEAP, lsr #32
    //     0xbf177c: b.eq            #0xbf1784
    //     0xbf1780: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf1784: ldur            x2, [fp, #-0x38]
    // 0xbf1788: LoadField: r0 = r2->field_a7
    //     0xbf1788: ldur            w0, [x2, #0xa7]
    // 0xbf178c: DecompressPointer r0
    //     0xbf178c: add             x0, x0, HEAP, lsl #32
    // 0xbf1790: cmp             w0, NULL
    // 0xbf1794: b.ne            #0xbf17a0
    // 0xbf1798: r4 = ""
    //     0xbf1798: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf179c: b               #0xbf17a4
    // 0xbf17a0: mov             x4, x0
    // 0xbf17a4: ldur            x0, [fp, #-8]
    // 0xbf17a8: ldur            x3, [fp, #-0x20]
    // 0xbf17ac: stur            x4, [fp, #-0x48]
    // 0xbf17b0: LoadField: r1 = r0->field_f
    //     0xbf17b0: ldur            w1, [x0, #0xf]
    // 0xbf17b4: DecompressPointer r1
    //     0xbf17b4: add             x1, x1, HEAP, lsl #32
    // 0xbf17b8: cmp             w1, NULL
    // 0xbf17bc: b.eq            #0xbf202c
    // 0xbf17c0: r0 = of()
    //     0xbf17c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf17c4: LoadField: r1 = r0->field_87
    //     0xbf17c4: ldur            w1, [x0, #0x87]
    // 0xbf17c8: DecompressPointer r1
    //     0xbf17c8: add             x1, x1, HEAP, lsl #32
    // 0xbf17cc: LoadField: r0 = r1->field_2b
    //     0xbf17cc: ldur            w0, [x1, #0x2b]
    // 0xbf17d0: DecompressPointer r0
    //     0xbf17d0: add             x0, x0, HEAP, lsl #32
    // 0xbf17d4: stur            x0, [fp, #-0x58]
    // 0xbf17d8: r1 = Instance_Color
    //     0xbf17d8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf17dc: d0 = 0.700000
    //     0xbf17dc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbf17e0: ldr             d0, [x17, #0xf48]
    // 0xbf17e4: r0 = withOpacity()
    //     0xbf17e4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbf17e8: r16 = 12.000000
    //     0xbf17e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf17ec: ldr             x16, [x16, #0x9e8]
    // 0xbf17f0: stp             x0, x16, [SP]
    // 0xbf17f4: ldur            x1, [fp, #-0x58]
    // 0xbf17f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf17f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf17fc: ldr             x4, [x4, #0xaa0]
    // 0xbf1800: r0 = copyWith()
    //     0xbf1800: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf1804: stur            x0, [fp, #-0x58]
    // 0xbf1808: r0 = Text()
    //     0xbf1808: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf180c: mov             x1, x0
    // 0xbf1810: ldur            x0, [fp, #-0x48]
    // 0xbf1814: StoreField: r1->field_b = r0
    //     0xbf1814: stur            w0, [x1, #0xb]
    // 0xbf1818: ldur            x0, [fp, #-0x58]
    // 0xbf181c: StoreField: r1->field_13 = r0
    //     0xbf181c: stur            w0, [x1, #0x13]
    // 0xbf1820: mov             x0, x1
    // 0xbf1824: ldur            x1, [fp, #-0x20]
    // 0xbf1828: ArrayStore: r1[8] = r0  ; List_4
    //     0xbf1828: add             x25, x1, #0x2f
    //     0xbf182c: str             w0, [x25]
    //     0xbf1830: tbz             w0, #0, #0xbf184c
    //     0xbf1834: ldurb           w16, [x1, #-1]
    //     0xbf1838: ldurb           w17, [x0, #-1]
    //     0xbf183c: and             x16, x17, x16, lsr #2
    //     0xbf1840: tst             x16, HEAP, lsr #32
    //     0xbf1844: b.eq            #0xbf184c
    //     0xbf1848: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf184c: r1 = <Widget>
    //     0xbf184c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf1850: r0 = AllocateGrowableArray()
    //     0xbf1850: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf1854: mov             x1, x0
    // 0xbf1858: ldur            x0, [fp, #-0x20]
    // 0xbf185c: stur            x1, [fp, #-0x48]
    // 0xbf1860: StoreField: r1->field_f = r0
    //     0xbf1860: stur            w0, [x1, #0xf]
    // 0xbf1864: r0 = 18
    //     0xbf1864: movz            x0, #0x12
    // 0xbf1868: StoreField: r1->field_b = r0
    //     0xbf1868: stur            w0, [x1, #0xb]
    // 0xbf186c: r0 = Row()
    //     0xbf186c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbf1870: mov             x2, x0
    // 0xbf1874: r0 = Instance_Axis
    //     0xbf1874: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbf1878: stur            x2, [fp, #-0x20]
    // 0xbf187c: StoreField: r2->field_f = r0
    //     0xbf187c: stur            w0, [x2, #0xf]
    // 0xbf1880: r3 = Instance_MainAxisAlignment
    //     0xbf1880: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf1884: ldr             x3, [x3, #0xa08]
    // 0xbf1888: StoreField: r2->field_13 = r3
    //     0xbf1888: stur            w3, [x2, #0x13]
    // 0xbf188c: r4 = Instance_MainAxisSize
    //     0xbf188c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf1890: ldr             x4, [x4, #0xa10]
    // 0xbf1894: ArrayStore: r2[0] = r4  ; List_4
    //     0xbf1894: stur            w4, [x2, #0x17]
    // 0xbf1898: r1 = Instance_CrossAxisAlignment
    //     0xbf1898: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbf189c: ldr             x1, [x1, #0xa18]
    // 0xbf18a0: StoreField: r2->field_1b = r1
    //     0xbf18a0: stur            w1, [x2, #0x1b]
    // 0xbf18a4: r5 = Instance_VerticalDirection
    //     0xbf18a4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf18a8: ldr             x5, [x5, #0xa20]
    // 0xbf18ac: StoreField: r2->field_23 = r5
    //     0xbf18ac: stur            w5, [x2, #0x23]
    // 0xbf18b0: r6 = Instance_Clip
    //     0xbf18b0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf18b4: ldr             x6, [x6, #0x38]
    // 0xbf18b8: StoreField: r2->field_2b = r6
    //     0xbf18b8: stur            w6, [x2, #0x2b]
    // 0xbf18bc: StoreField: r2->field_2f = rZR
    //     0xbf18bc: stur            xzr, [x2, #0x2f]
    // 0xbf18c0: ldur            x1, [fp, #-0x48]
    // 0xbf18c4: StoreField: r2->field_b = r1
    //     0xbf18c4: stur            w1, [x2, #0xb]
    // 0xbf18c8: ldur            x7, [fp, #-0x38]
    // 0xbf18cc: LoadField: r1 = r7->field_9b
    //     0xbf18cc: ldur            w1, [x7, #0x9b]
    // 0xbf18d0: DecompressPointer r1
    //     0xbf18d0: add             x1, x1, HEAP, lsl #32
    // 0xbf18d4: cmp             w1, NULL
    // 0xbf18d8: b.ne            #0xbf18e4
    // 0xbf18dc: r1 = "0.0"
    //     0xbf18dc: add             x1, PP, #0xe, lsl #12  ; [pp+0xe628] "0.0"
    //     0xbf18e0: ldr             x1, [x1, #0x628]
    // 0xbf18e4: r0 = parse()
    //     0xbf18e4: bl              #0x64333c  ; [dart:core] double::parse
    // 0xbf18e8: ldur            x2, [fp, #-0x38]
    // 0xbf18ec: stur            d0, [fp, #-0x80]
    // 0xbf18f0: LoadField: r0 = r2->field_9b
    //     0xbf18f0: ldur            w0, [x2, #0x9b]
    // 0xbf18f4: DecompressPointer r0
    //     0xbf18f4: add             x0, x0, HEAP, lsl #32
    // 0xbf18f8: cmp             w0, NULL
    // 0xbf18fc: b.ne            #0xbf1908
    // 0xbf1900: r1 = "0"
    //     0xbf1900: ldr             x1, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0xbf1904: b               #0xbf190c
    // 0xbf1908: mov             x1, x0
    // 0xbf190c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbf190c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbf1910: r0 = parse()
    //     0xbf1910: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xbf1914: stur            x0, [fp, #-0x18]
    // 0xbf1918: r0 = RatingWidget()
    //     0xbf1918: bl              #0x9b101c  ; AllocateRatingWidgetStub -> RatingWidget (size=0x14)
    // 0xbf191c: mov             x3, x0
    // 0xbf1920: r0 = Instance_Icon
    //     0xbf1920: add             x0, PP, #0x52, lsl #12  ; [pp+0x52190] Obj!Icon@d65fb1
    //     0xbf1924: ldr             x0, [x0, #0x190]
    // 0xbf1928: stur            x3, [fp, #-0x48]
    // 0xbf192c: StoreField: r3->field_7 = r0
    //     0xbf192c: stur            w0, [x3, #7]
    // 0xbf1930: r0 = Instance_Icon
    //     0xbf1930: add             x0, PP, #0x52, lsl #12  ; [pp+0x52198] Obj!Icon@d65f71
    //     0xbf1934: ldr             x0, [x0, #0x198]
    // 0xbf1938: StoreField: r3->field_b = r0
    //     0xbf1938: stur            w0, [x3, #0xb]
    // 0xbf193c: r0 = Instance_Icon
    //     0xbf193c: add             x0, PP, #0x52, lsl #12  ; [pp+0x521a0] Obj!Icon@d65f31
    //     0xbf1940: ldr             x0, [x0, #0x1a0]
    // 0xbf1944: StoreField: r3->field_f = r0
    //     0xbf1944: stur            w0, [x3, #0xf]
    // 0xbf1948: r1 = Function '<anonymous closure>':.
    //     0xbf1948: add             x1, PP, #0x53, lsl #12  ; [pp+0x53538] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xbf194c: ldr             x1, [x1, #0x538]
    // 0xbf1950: r2 = Null
    //     0xbf1950: mov             x2, NULL
    // 0xbf1954: r0 = AllocateClosure()
    //     0xbf1954: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf1958: stur            x0, [fp, #-0x58]
    // 0xbf195c: r0 = RatingBar()
    //     0xbf195c: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xbf1960: mov             x2, x0
    // 0xbf1964: ldur            x0, [fp, #-0x58]
    // 0xbf1968: stur            x2, [fp, #-0x60]
    // 0xbf196c: StoreField: r2->field_b = r0
    //     0xbf196c: stur            w0, [x2, #0xb]
    // 0xbf1970: r0 = true
    //     0xbf1970: add             x0, NULL, #0x20  ; true
    // 0xbf1974: StoreField: r2->field_1f = r0
    //     0xbf1974: stur            w0, [x2, #0x1f]
    // 0xbf1978: r1 = Instance_Axis
    //     0xbf1978: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbf197c: StoreField: r2->field_23 = r1
    //     0xbf197c: stur            w1, [x2, #0x23]
    // 0xbf1980: StoreField: r2->field_27 = r0
    //     0xbf1980: stur            w0, [x2, #0x27]
    // 0xbf1984: d0 = 2.000000
    //     0xbf1984: fmov            d0, #2.00000000
    // 0xbf1988: StoreField: r2->field_2b = d0
    //     0xbf1988: stur            d0, [x2, #0x2b]
    // 0xbf198c: StoreField: r2->field_33 = r0
    //     0xbf198c: stur            w0, [x2, #0x33]
    // 0xbf1990: ldur            d0, [fp, #-0x80]
    // 0xbf1994: StoreField: r2->field_37 = d0
    //     0xbf1994: stur            d0, [x2, #0x37]
    // 0xbf1998: ldur            x1, [fp, #-0x18]
    // 0xbf199c: StoreField: r2->field_3f = r1
    //     0xbf199c: stur            x1, [x2, #0x3f]
    // 0xbf19a0: r1 = Instance_EdgeInsets
    //     0xbf19a0: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xbf19a4: StoreField: r2->field_47 = r1
    //     0xbf19a4: stur            w1, [x2, #0x47]
    // 0xbf19a8: d0 = 20.000000
    //     0xbf19a8: fmov            d0, #20.00000000
    // 0xbf19ac: StoreField: r2->field_4b = d0
    //     0xbf19ac: stur            d0, [x2, #0x4b]
    // 0xbf19b0: StoreField: r2->field_53 = rZR
    //     0xbf19b0: stur            xzr, [x2, #0x53]
    // 0xbf19b4: r1 = false
    //     0xbf19b4: add             x1, NULL, #0x30  ; false
    // 0xbf19b8: StoreField: r2->field_5b = r1
    //     0xbf19b8: stur            w1, [x2, #0x5b]
    // 0xbf19bc: StoreField: r2->field_5f = r1
    //     0xbf19bc: stur            w1, [x2, #0x5f]
    // 0xbf19c0: ldur            x1, [fp, #-0x48]
    // 0xbf19c4: StoreField: r2->field_67 = r1
    //     0xbf19c4: stur            w1, [x2, #0x67]
    // 0xbf19c8: ldur            x3, [fp, #-0x38]
    // 0xbf19cc: LoadField: r1 = r3->field_9f
    //     0xbf19cc: ldur            w1, [x3, #0x9f]
    // 0xbf19d0: DecompressPointer r1
    //     0xbf19d0: add             x1, x1, HEAP, lsl #32
    // 0xbf19d4: cmp             w1, NULL
    // 0xbf19d8: b.ne            #0xbf19e4
    // 0xbf19dc: r0 = Null
    //     0xbf19dc: mov             x0, NULL
    // 0xbf19e0: b               #0xbf19e8
    // 0xbf19e4: r0 = trim()
    //     0xbf19e4: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xbf19e8: cmp             w0, NULL
    // 0xbf19ec: b.ne            #0xbf19f4
    // 0xbf19f0: r0 = ""
    //     0xbf19f0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf19f4: ldur            x1, [fp, #-0x10]
    // 0xbf19f8: stur            x0, [fp, #-0x48]
    // 0xbf19fc: r0 = value()
    //     0xbf19fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf1a00: tbnz            w0, #4, #0xbf1a0c
    // 0xbf1a04: r0 = Null
    //     0xbf1a04: mov             x0, NULL
    // 0xbf1a08: b               #0xbf1a10
    // 0xbf1a0c: r0 = 4
    //     0xbf1a0c: movz            x0, #0x4
    // 0xbf1a10: ldur            x1, [fp, #-0x10]
    // 0xbf1a14: stur            x0, [fp, #-0x58]
    // 0xbf1a18: r0 = value()
    //     0xbf1a18: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf1a1c: tbnz            w0, #4, #0xbf1a2c
    // 0xbf1a20: r5 = Instance_TextOverflow
    //     0xbf1a20: add             x5, PP, #0x4b, lsl #12  ; [pp+0x4b3a8] Obj!TextOverflow@d73761
    //     0xbf1a24: ldr             x5, [x5, #0x3a8]
    // 0xbf1a28: b               #0xbf1a34
    // 0xbf1a2c: r5 = Instance_TextOverflow
    //     0xbf1a2c: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbf1a30: ldr             x5, [x5, #0xe10]
    // 0xbf1a34: ldur            x4, [fp, #-8]
    // 0xbf1a38: ldur            x3, [fp, #-0x38]
    // 0xbf1a3c: ldur            x2, [fp, #-0x48]
    // 0xbf1a40: ldur            x0, [fp, #-0x58]
    // 0xbf1a44: stur            x5, [fp, #-0x68]
    // 0xbf1a48: LoadField: r1 = r4->field_f
    //     0xbf1a48: ldur            w1, [x4, #0xf]
    // 0xbf1a4c: DecompressPointer r1
    //     0xbf1a4c: add             x1, x1, HEAP, lsl #32
    // 0xbf1a50: cmp             w1, NULL
    // 0xbf1a54: b.eq            #0xbf2030
    // 0xbf1a58: r0 = of()
    //     0xbf1a58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf1a5c: LoadField: r1 = r0->field_87
    //     0xbf1a5c: ldur            w1, [x0, #0x87]
    // 0xbf1a60: DecompressPointer r1
    //     0xbf1a60: add             x1, x1, HEAP, lsl #32
    // 0xbf1a64: LoadField: r0 = r1->field_2b
    //     0xbf1a64: ldur            w0, [x1, #0x2b]
    // 0xbf1a68: DecompressPointer r0
    //     0xbf1a68: add             x0, x0, HEAP, lsl #32
    // 0xbf1a6c: LoadField: r1 = r0->field_13
    //     0xbf1a6c: ldur            w1, [x0, #0x13]
    // 0xbf1a70: DecompressPointer r1
    //     0xbf1a70: add             x1, x1, HEAP, lsl #32
    // 0xbf1a74: stur            x1, [fp, #-0x70]
    // 0xbf1a78: r0 = TextStyle()
    //     0xbf1a78: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xbf1a7c: mov             x1, x0
    // 0xbf1a80: r0 = true
    //     0xbf1a80: add             x0, NULL, #0x20  ; true
    // 0xbf1a84: stur            x1, [fp, #-0x78]
    // 0xbf1a88: StoreField: r1->field_7 = r0
    //     0xbf1a88: stur            w0, [x1, #7]
    // 0xbf1a8c: r0 = Instance_Color
    //     0xbf1a8c: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf1a90: StoreField: r1->field_b = r0
    //     0xbf1a90: stur            w0, [x1, #0xb]
    // 0xbf1a94: r0 = 14.000000
    //     0xbf1a94: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbf1a98: ldr             x0, [x0, #0x1d8]
    // 0xbf1a9c: StoreField: r1->field_1f = r0
    //     0xbf1a9c: stur            w0, [x1, #0x1f]
    // 0xbf1aa0: ldur            x0, [fp, #-0x70]
    // 0xbf1aa4: StoreField: r1->field_13 = r0
    //     0xbf1aa4: stur            w0, [x1, #0x13]
    // 0xbf1aa8: r0 = Text()
    //     0xbf1aa8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf1aac: mov             x3, x0
    // 0xbf1ab0: ldur            x0, [fp, #-0x48]
    // 0xbf1ab4: stur            x3, [fp, #-0x70]
    // 0xbf1ab8: StoreField: r3->field_b = r0
    //     0xbf1ab8: stur            w0, [x3, #0xb]
    // 0xbf1abc: ldur            x0, [fp, #-0x78]
    // 0xbf1ac0: StoreField: r3->field_13 = r0
    //     0xbf1ac0: stur            w0, [x3, #0x13]
    // 0xbf1ac4: r0 = Instance_TextAlign
    //     0xbf1ac4: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xbf1ac8: StoreField: r3->field_1b = r0
    //     0xbf1ac8: stur            w0, [x3, #0x1b]
    // 0xbf1acc: ldur            x0, [fp, #-0x68]
    // 0xbf1ad0: StoreField: r3->field_2b = r0
    //     0xbf1ad0: stur            w0, [x3, #0x2b]
    // 0xbf1ad4: ldur            x0, [fp, #-0x58]
    // 0xbf1ad8: StoreField: r3->field_37 = r0
    //     0xbf1ad8: stur            w0, [x3, #0x37]
    // 0xbf1adc: r1 = Null
    //     0xbf1adc: mov             x1, NULL
    // 0xbf1ae0: r2 = 2
    //     0xbf1ae0: movz            x2, #0x2
    // 0xbf1ae4: r0 = AllocateArray()
    //     0xbf1ae4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf1ae8: mov             x2, x0
    // 0xbf1aec: ldur            x0, [fp, #-0x70]
    // 0xbf1af0: stur            x2, [fp, #-0x48]
    // 0xbf1af4: StoreField: r2->field_f = r0
    //     0xbf1af4: stur            w0, [x2, #0xf]
    // 0xbf1af8: r1 = <Widget>
    //     0xbf1af8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf1afc: r0 = AllocateGrowableArray()
    //     0xbf1afc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf1b00: mov             x2, x0
    // 0xbf1b04: ldur            x0, [fp, #-0x48]
    // 0xbf1b08: stur            x2, [fp, #-0x58]
    // 0xbf1b0c: StoreField: r2->field_f = r0
    //     0xbf1b0c: stur            w0, [x2, #0xf]
    // 0xbf1b10: r0 = 2
    //     0xbf1b10: movz            x0, #0x2
    // 0xbf1b14: StoreField: r2->field_b = r0
    //     0xbf1b14: stur            w0, [x2, #0xb]
    // 0xbf1b18: ldur            x0, [fp, #-0x38]
    // 0xbf1b1c: LoadField: r1 = r0->field_9f
    //     0xbf1b1c: ldur            w1, [x0, #0x9f]
    // 0xbf1b20: DecompressPointer r1
    //     0xbf1b20: add             x1, x1, HEAP, lsl #32
    // 0xbf1b24: cmp             w1, NULL
    // 0xbf1b28: b.ne            #0xbf1b34
    // 0xbf1b2c: r0 = Null
    //     0xbf1b2c: mov             x0, NULL
    // 0xbf1b30: b               #0xbf1b38
    // 0xbf1b34: r0 = trim()
    //     0xbf1b34: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xbf1b38: cmp             w0, NULL
    // 0xbf1b3c: b.ne            #0xbf1b48
    // 0xbf1b40: r1 = ""
    //     0xbf1b40: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf1b44: b               #0xbf1b4c
    // 0xbf1b48: mov             x1, x0
    // 0xbf1b4c: ldur            x0, [fp, #-8]
    // 0xbf1b50: LoadField: r2 = r0->field_f
    //     0xbf1b50: ldur            w2, [x0, #0xf]
    // 0xbf1b54: DecompressPointer r2
    //     0xbf1b54: add             x2, x2, HEAP, lsl #32
    // 0xbf1b58: cmp             w2, NULL
    // 0xbf1b5c: b.eq            #0xbf2034
    // 0xbf1b60: r0 = TextExceeds.textExceedsLines()
    //     0xbf1b60: bl              #0xa5ca58  ; [package:customer_app/app/core/extension/extension_function.dart] ::TextExceeds.textExceedsLines
    // 0xbf1b64: tbnz            w0, #4, #0xbf1cf0
    // 0xbf1b68: ldur            x1, [fp, #-0x10]
    // 0xbf1b6c: r0 = value()
    //     0xbf1b6c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf1b70: tbnz            w0, #4, #0xbf1b80
    // 0xbf1b74: r3 = "Know Less"
    //     0xbf1b74: add             x3, PP, #0x52, lsl #12  ; [pp+0x521d0] "Know Less"
    //     0xbf1b78: ldr             x3, [x3, #0x1d0]
    // 0xbf1b7c: b               #0xbf1b88
    // 0xbf1b80: r3 = "Know more"
    //     0xbf1b80: add             x3, PP, #0x36, lsl #12  ; [pp+0x36020] "Know more"
    //     0xbf1b84: ldr             x3, [x3, #0x20]
    // 0xbf1b88: ldur            x0, [fp, #-8]
    // 0xbf1b8c: ldur            x2, [fp, #-0x58]
    // 0xbf1b90: stur            x3, [fp, #-0x10]
    // 0xbf1b94: LoadField: r1 = r0->field_f
    //     0xbf1b94: ldur            w1, [x0, #0xf]
    // 0xbf1b98: DecompressPointer r1
    //     0xbf1b98: add             x1, x1, HEAP, lsl #32
    // 0xbf1b9c: cmp             w1, NULL
    // 0xbf1ba0: b.eq            #0xbf2038
    // 0xbf1ba4: r0 = of()
    //     0xbf1ba4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf1ba8: LoadField: r1 = r0->field_87
    //     0xbf1ba8: ldur            w1, [x0, #0x87]
    // 0xbf1bac: DecompressPointer r1
    //     0xbf1bac: add             x1, x1, HEAP, lsl #32
    // 0xbf1bb0: LoadField: r0 = r1->field_7
    //     0xbf1bb0: ldur            w0, [x1, #7]
    // 0xbf1bb4: DecompressPointer r0
    //     0xbf1bb4: add             x0, x0, HEAP, lsl #32
    // 0xbf1bb8: ldur            x2, [fp, #-8]
    // 0xbf1bbc: stur            x0, [fp, #-0x48]
    // 0xbf1bc0: LoadField: r1 = r2->field_f
    //     0xbf1bc0: ldur            w1, [x2, #0xf]
    // 0xbf1bc4: DecompressPointer r1
    //     0xbf1bc4: add             x1, x1, HEAP, lsl #32
    // 0xbf1bc8: cmp             w1, NULL
    // 0xbf1bcc: b.eq            #0xbf203c
    // 0xbf1bd0: r0 = of()
    //     0xbf1bd0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf1bd4: LoadField: r1 = r0->field_5b
    //     0xbf1bd4: ldur            w1, [x0, #0x5b]
    // 0xbf1bd8: DecompressPointer r1
    //     0xbf1bd8: add             x1, x1, HEAP, lsl #32
    // 0xbf1bdc: r16 = 12.000000
    //     0xbf1bdc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf1be0: ldr             x16, [x16, #0x9e8]
    // 0xbf1be4: stp             x1, x16, [SP, #8]
    // 0xbf1be8: r16 = Instance_TextDecoration
    //     0xbf1be8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xbf1bec: ldr             x16, [x16, #0x10]
    // 0xbf1bf0: str             x16, [SP]
    // 0xbf1bf4: ldur            x1, [fp, #-0x48]
    // 0xbf1bf8: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xbf1bf8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xbf1bfc: ldr             x4, [x4, #0xe38]
    // 0xbf1c00: r0 = copyWith()
    //     0xbf1c00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf1c04: stur            x0, [fp, #-0x48]
    // 0xbf1c08: r0 = Text()
    //     0xbf1c08: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf1c0c: mov             x1, x0
    // 0xbf1c10: ldur            x0, [fp, #-0x10]
    // 0xbf1c14: stur            x1, [fp, #-0x68]
    // 0xbf1c18: StoreField: r1->field_b = r0
    //     0xbf1c18: stur            w0, [x1, #0xb]
    // 0xbf1c1c: ldur            x0, [fp, #-0x48]
    // 0xbf1c20: StoreField: r1->field_13 = r0
    //     0xbf1c20: stur            w0, [x1, #0x13]
    // 0xbf1c24: r0 = Padding()
    //     0xbf1c24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf1c28: mov             x1, x0
    // 0xbf1c2c: r0 = Instance_EdgeInsets
    //     0xbf1c2c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbf1c30: ldr             x0, [x0, #0x668]
    // 0xbf1c34: stur            x1, [fp, #-0x10]
    // 0xbf1c38: StoreField: r1->field_f = r0
    //     0xbf1c38: stur            w0, [x1, #0xf]
    // 0xbf1c3c: ldur            x0, [fp, #-0x68]
    // 0xbf1c40: StoreField: r1->field_b = r0
    //     0xbf1c40: stur            w0, [x1, #0xb]
    // 0xbf1c44: r0 = GestureDetector()
    //     0xbf1c44: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xbf1c48: ldur            x2, [fp, #-0x28]
    // 0xbf1c4c: r1 = Function '<anonymous closure>':.
    //     0xbf1c4c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53540] AnonymousClosure: (0xbf2b90), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_testimonialWidget (0xbf112c)
    //     0xbf1c50: ldr             x1, [x1, #0x540]
    // 0xbf1c54: stur            x0, [fp, #-0x28]
    // 0xbf1c58: r0 = AllocateClosure()
    //     0xbf1c58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf1c5c: ldur            x16, [fp, #-0x10]
    // 0xbf1c60: stp             x16, x0, [SP]
    // 0xbf1c64: ldur            x1, [fp, #-0x28]
    // 0xbf1c68: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xbf1c68: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xbf1c6c: ldr             x4, [x4, #0xaf0]
    // 0xbf1c70: r0 = GestureDetector()
    //     0xbf1c70: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xbf1c74: ldur            x0, [fp, #-0x58]
    // 0xbf1c78: LoadField: r1 = r0->field_b
    //     0xbf1c78: ldur            w1, [x0, #0xb]
    // 0xbf1c7c: LoadField: r2 = r0->field_f
    //     0xbf1c7c: ldur            w2, [x0, #0xf]
    // 0xbf1c80: DecompressPointer r2
    //     0xbf1c80: add             x2, x2, HEAP, lsl #32
    // 0xbf1c84: LoadField: r3 = r2->field_b
    //     0xbf1c84: ldur            w3, [x2, #0xb]
    // 0xbf1c88: r2 = LoadInt32Instr(r1)
    //     0xbf1c88: sbfx            x2, x1, #1, #0x1f
    // 0xbf1c8c: stur            x2, [fp, #-0x18]
    // 0xbf1c90: r1 = LoadInt32Instr(r3)
    //     0xbf1c90: sbfx            x1, x3, #1, #0x1f
    // 0xbf1c94: cmp             x2, x1
    // 0xbf1c98: b.ne            #0xbf1ca4
    // 0xbf1c9c: mov             x1, x0
    // 0xbf1ca0: r0 = _growToNextCapacity()
    //     0xbf1ca0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbf1ca4: ldur            x2, [fp, #-0x58]
    // 0xbf1ca8: ldur            x3, [fp, #-0x18]
    // 0xbf1cac: add             x0, x3, #1
    // 0xbf1cb0: lsl             x1, x0, #1
    // 0xbf1cb4: StoreField: r2->field_b = r1
    //     0xbf1cb4: stur            w1, [x2, #0xb]
    // 0xbf1cb8: LoadField: r1 = r2->field_f
    //     0xbf1cb8: ldur            w1, [x2, #0xf]
    // 0xbf1cbc: DecompressPointer r1
    //     0xbf1cbc: add             x1, x1, HEAP, lsl #32
    // 0xbf1cc0: ldur            x0, [fp, #-0x28]
    // 0xbf1cc4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbf1cc4: add             x25, x1, x3, lsl #2
    //     0xbf1cc8: add             x25, x25, #0xf
    //     0xbf1ccc: str             w0, [x25]
    //     0xbf1cd0: tbz             w0, #0, #0xbf1cec
    //     0xbf1cd4: ldurb           w16, [x1, #-1]
    //     0xbf1cd8: ldurb           w17, [x0, #-1]
    //     0xbf1cdc: and             x16, x17, x16, lsr #2
    //     0xbf1ce0: tst             x16, HEAP, lsr #32
    //     0xbf1ce4: b.eq            #0xbf1cec
    //     0xbf1ce8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf1cec: b               #0xbf1cf4
    // 0xbf1cf0: ldur            x2, [fp, #-0x58]
    // 0xbf1cf4: ldur            x4, [fp, #-0x30]
    // 0xbf1cf8: ldur            x3, [fp, #-0x50]
    // 0xbf1cfc: ldur            x1, [fp, #-0x20]
    // 0xbf1d00: ldur            x0, [fp, #-0x60]
    // 0xbf1d04: r0 = Column()
    //     0xbf1d04: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf1d08: mov             x3, x0
    // 0xbf1d0c: r0 = Instance_Axis
    //     0xbf1d0c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf1d10: stur            x3, [fp, #-0x10]
    // 0xbf1d14: StoreField: r3->field_f = r0
    //     0xbf1d14: stur            w0, [x3, #0xf]
    // 0xbf1d18: r4 = Instance_MainAxisAlignment
    //     0xbf1d18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf1d1c: ldr             x4, [x4, #0xa08]
    // 0xbf1d20: StoreField: r3->field_13 = r4
    //     0xbf1d20: stur            w4, [x3, #0x13]
    // 0xbf1d24: r1 = Instance_MainAxisSize
    //     0xbf1d24: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf1d28: ldr             x1, [x1, #0xa10]
    // 0xbf1d2c: ArrayStore: r3[0] = r1  ; List_4
    //     0xbf1d2c: stur            w1, [x3, #0x17]
    // 0xbf1d30: r5 = Instance_CrossAxisAlignment
    //     0xbf1d30: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbf1d34: ldr             x5, [x5, #0x890]
    // 0xbf1d38: StoreField: r3->field_1b = r5
    //     0xbf1d38: stur            w5, [x3, #0x1b]
    // 0xbf1d3c: r6 = Instance_VerticalDirection
    //     0xbf1d3c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf1d40: ldr             x6, [x6, #0xa20]
    // 0xbf1d44: StoreField: r3->field_23 = r6
    //     0xbf1d44: stur            w6, [x3, #0x23]
    // 0xbf1d48: r7 = Instance_Clip
    //     0xbf1d48: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf1d4c: ldr             x7, [x7, #0x38]
    // 0xbf1d50: StoreField: r3->field_2b = r7
    //     0xbf1d50: stur            w7, [x3, #0x2b]
    // 0xbf1d54: StoreField: r3->field_2f = rZR
    //     0xbf1d54: stur            xzr, [x3, #0x2f]
    // 0xbf1d58: ldur            x1, [fp, #-0x58]
    // 0xbf1d5c: StoreField: r3->field_b = r1
    //     0xbf1d5c: stur            w1, [x3, #0xb]
    // 0xbf1d60: r1 = Null
    //     0xbf1d60: mov             x1, NULL
    // 0xbf1d64: r2 = 16
    //     0xbf1d64: movz            x2, #0x10
    // 0xbf1d68: r0 = AllocateArray()
    //     0xbf1d68: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf1d6c: mov             x2, x0
    // 0xbf1d70: ldur            x0, [fp, #-0x50]
    // 0xbf1d74: stur            x2, [fp, #-0x28]
    // 0xbf1d78: StoreField: r2->field_f = r0
    //     0xbf1d78: stur            w0, [x2, #0xf]
    // 0xbf1d7c: r16 = Instance_SizedBox
    //     0xbf1d7c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xbf1d80: ldr             x16, [x16, #0x328]
    // 0xbf1d84: StoreField: r2->field_13 = r16
    //     0xbf1d84: stur            w16, [x2, #0x13]
    // 0xbf1d88: ldur            x0, [fp, #-0x20]
    // 0xbf1d8c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbf1d8c: stur            w0, [x2, #0x17]
    // 0xbf1d90: r16 = Instance_SizedBox
    //     0xbf1d90: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xbf1d94: ldr             x16, [x16, #0xc70]
    // 0xbf1d98: StoreField: r2->field_1b = r16
    //     0xbf1d98: stur            w16, [x2, #0x1b]
    // 0xbf1d9c: ldur            x0, [fp, #-0x60]
    // 0xbf1da0: StoreField: r2->field_1f = r0
    //     0xbf1da0: stur            w0, [x2, #0x1f]
    // 0xbf1da4: r16 = Instance_SizedBox
    //     0xbf1da4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52210] Obj!SizedBox@d680e1
    //     0xbf1da8: ldr             x16, [x16, #0x210]
    // 0xbf1dac: StoreField: r2->field_23 = r16
    //     0xbf1dac: stur            w16, [x2, #0x23]
    // 0xbf1db0: ldur            x0, [fp, #-0x10]
    // 0xbf1db4: StoreField: r2->field_27 = r0
    //     0xbf1db4: stur            w0, [x2, #0x27]
    // 0xbf1db8: r16 = Instance_SizedBox
    //     0xbf1db8: add             x16, PP, #0x52, lsl #12  ; [pp+0x52210] Obj!SizedBox@d680e1
    //     0xbf1dbc: ldr             x16, [x16, #0x210]
    // 0xbf1dc0: StoreField: r2->field_2b = r16
    //     0xbf1dc0: stur            w16, [x2, #0x2b]
    // 0xbf1dc4: r1 = <Widget>
    //     0xbf1dc4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf1dc8: r0 = AllocateGrowableArray()
    //     0xbf1dc8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf1dcc: mov             x3, x0
    // 0xbf1dd0: ldur            x0, [fp, #-0x28]
    // 0xbf1dd4: stur            x3, [fp, #-0x10]
    // 0xbf1dd8: StoreField: r3->field_f = r0
    //     0xbf1dd8: stur            w0, [x3, #0xf]
    // 0xbf1ddc: r0 = 16
    //     0xbf1ddc: movz            x0, #0x10
    // 0xbf1de0: StoreField: r3->field_b = r0
    //     0xbf1de0: stur            w0, [x3, #0xb]
    // 0xbf1de4: ldur            x0, [fp, #-0x30]
    // 0xbf1de8: tbnz            w0, #4, #0xbf1f28
    // 0xbf1dec: ldur            x2, [fp, #-0x38]
    // 0xbf1df0: LoadField: r0 = r2->field_ab
    //     0xbf1df0: ldur            w0, [x2, #0xab]
    // 0xbf1df4: DecompressPointer r0
    //     0xbf1df4: add             x0, x0, HEAP, lsl #32
    // 0xbf1df8: cmp             w0, NULL
    // 0xbf1dfc: b.ne            #0xbf1e08
    // 0xbf1e00: r0 = Null
    //     0xbf1e00: mov             x0, NULL
    // 0xbf1e04: b               #0xbf1e10
    // 0xbf1e08: LoadField: r1 = r0->field_b
    //     0xbf1e08: ldur            w1, [x0, #0xb]
    // 0xbf1e0c: mov             x0, x1
    // 0xbf1e10: cmp             w0, NULL
    // 0xbf1e14: b.ne            #0xbf1e20
    // 0xbf1e18: r0 = 0
    //     0xbf1e18: movz            x0, #0
    // 0xbf1e1c: b               #0xbf1e28
    // 0xbf1e20: r1 = LoadInt32Instr(r0)
    //     0xbf1e20: sbfx            x1, x0, #1, #0x1f
    // 0xbf1e24: mov             x0, x1
    // 0xbf1e28: cmp             x0, #3
    // 0xbf1e2c: b.gt            #0xbf1e4c
    // 0xbf1e30: ldur            x1, [fp, #-8]
    // 0xbf1e34: LoadField: r0 = r1->field_f
    //     0xbf1e34: ldur            w0, [x1, #0xf]
    // 0xbf1e38: DecompressPointer r0
    //     0xbf1e38: add             x0, x0, HEAP, lsl #32
    // 0xbf1e3c: cmp             w0, NULL
    // 0xbf1e40: b.eq            #0xbf2040
    // 0xbf1e44: r0 = _buildImagesRow()
    //     0xbf1e44: bl              #0xbf2868  ; [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRow
    // 0xbf1e48: b               #0xbf1e64
    // 0xbf1e4c: ldur            x1, [fp, #-8]
    // 0xbf1e50: LoadField: r3 = r1->field_f
    //     0xbf1e50: ldur            w3, [x1, #0xf]
    // 0xbf1e54: DecompressPointer r3
    //     0xbf1e54: add             x3, x3, HEAP, lsl #32
    // 0xbf1e58: cmp             w3, NULL
    // 0xbf1e5c: b.eq            #0xbf2044
    // 0xbf1e60: r0 = _buildImagesRowWithMore()
    //     0xbf1e60: bl              #0xbf2048  ; [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore
    // 0xbf1e64: ldur            x1, [fp, #-0x10]
    // 0xbf1e68: stur            x0, [fp, #-8]
    // 0xbf1e6c: r0 = SizedBox()
    //     0xbf1e6c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbf1e70: mov             x1, x0
    // 0xbf1e74: r0 = 120.000000
    //     0xbf1e74: add             x0, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0xbf1e78: ldr             x0, [x0, #0x3a0]
    // 0xbf1e7c: stur            x1, [fp, #-0x20]
    // 0xbf1e80: StoreField: r1->field_13 = r0
    //     0xbf1e80: stur            w0, [x1, #0x13]
    // 0xbf1e84: ldur            x0, [fp, #-8]
    // 0xbf1e88: StoreField: r1->field_b = r0
    //     0xbf1e88: stur            w0, [x1, #0xb]
    // 0xbf1e8c: r0 = Padding()
    //     0xbf1e8c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf1e90: mov             x2, x0
    // 0xbf1e94: r0 = Instance_EdgeInsets
    //     0xbf1e94: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xbf1e98: ldr             x0, [x0, #0x858]
    // 0xbf1e9c: stur            x2, [fp, #-8]
    // 0xbf1ea0: StoreField: r2->field_f = r0
    //     0xbf1ea0: stur            w0, [x2, #0xf]
    // 0xbf1ea4: ldur            x0, [fp, #-0x20]
    // 0xbf1ea8: StoreField: r2->field_b = r0
    //     0xbf1ea8: stur            w0, [x2, #0xb]
    // 0xbf1eac: ldur            x0, [fp, #-0x10]
    // 0xbf1eb0: LoadField: r1 = r0->field_b
    //     0xbf1eb0: ldur            w1, [x0, #0xb]
    // 0xbf1eb4: LoadField: r3 = r0->field_f
    //     0xbf1eb4: ldur            w3, [x0, #0xf]
    // 0xbf1eb8: DecompressPointer r3
    //     0xbf1eb8: add             x3, x3, HEAP, lsl #32
    // 0xbf1ebc: LoadField: r4 = r3->field_b
    //     0xbf1ebc: ldur            w4, [x3, #0xb]
    // 0xbf1ec0: r3 = LoadInt32Instr(r1)
    //     0xbf1ec0: sbfx            x3, x1, #1, #0x1f
    // 0xbf1ec4: stur            x3, [fp, #-0x18]
    // 0xbf1ec8: r1 = LoadInt32Instr(r4)
    //     0xbf1ec8: sbfx            x1, x4, #1, #0x1f
    // 0xbf1ecc: cmp             x3, x1
    // 0xbf1ed0: b.ne            #0xbf1edc
    // 0xbf1ed4: mov             x1, x0
    // 0xbf1ed8: r0 = _growToNextCapacity()
    //     0xbf1ed8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbf1edc: ldur            x2, [fp, #-0x10]
    // 0xbf1ee0: ldur            x3, [fp, #-0x18]
    // 0xbf1ee4: add             x0, x3, #1
    // 0xbf1ee8: lsl             x1, x0, #1
    // 0xbf1eec: StoreField: r2->field_b = r1
    //     0xbf1eec: stur            w1, [x2, #0xb]
    // 0xbf1ef0: LoadField: r1 = r2->field_f
    //     0xbf1ef0: ldur            w1, [x2, #0xf]
    // 0xbf1ef4: DecompressPointer r1
    //     0xbf1ef4: add             x1, x1, HEAP, lsl #32
    // 0xbf1ef8: ldur            x0, [fp, #-8]
    // 0xbf1efc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbf1efc: add             x25, x1, x3, lsl #2
    //     0xbf1f00: add             x25, x25, #0xf
    //     0xbf1f04: str             w0, [x25]
    //     0xbf1f08: tbz             w0, #0, #0xbf1f24
    //     0xbf1f0c: ldurb           w16, [x1, #-1]
    //     0xbf1f10: ldurb           w17, [x0, #-1]
    //     0xbf1f14: and             x16, x17, x16, lsr #2
    //     0xbf1f18: tst             x16, HEAP, lsr #32
    //     0xbf1f1c: b.eq            #0xbf1f24
    //     0xbf1f20: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf1f24: b               #0xbf1f2c
    // 0xbf1f28: mov             x2, x3
    // 0xbf1f2c: r0 = Column()
    //     0xbf1f2c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf1f30: mov             x1, x0
    // 0xbf1f34: r0 = Instance_Axis
    //     0xbf1f34: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf1f38: stur            x1, [fp, #-8]
    // 0xbf1f3c: StoreField: r1->field_f = r0
    //     0xbf1f3c: stur            w0, [x1, #0xf]
    // 0xbf1f40: r0 = Instance_MainAxisAlignment
    //     0xbf1f40: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf1f44: ldr             x0, [x0, #0xa08]
    // 0xbf1f48: StoreField: r1->field_13 = r0
    //     0xbf1f48: stur            w0, [x1, #0x13]
    // 0xbf1f4c: r0 = Instance_MainAxisSize
    //     0xbf1f4c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xbf1f50: ldr             x0, [x0, #0xdd0]
    // 0xbf1f54: ArrayStore: r1[0] = r0  ; List_4
    //     0xbf1f54: stur            w0, [x1, #0x17]
    // 0xbf1f58: r0 = Instance_CrossAxisAlignment
    //     0xbf1f58: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbf1f5c: ldr             x0, [x0, #0x890]
    // 0xbf1f60: StoreField: r1->field_1b = r0
    //     0xbf1f60: stur            w0, [x1, #0x1b]
    // 0xbf1f64: r0 = Instance_VerticalDirection
    //     0xbf1f64: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf1f68: ldr             x0, [x0, #0xa20]
    // 0xbf1f6c: StoreField: r1->field_23 = r0
    //     0xbf1f6c: stur            w0, [x1, #0x23]
    // 0xbf1f70: r0 = Instance_Clip
    //     0xbf1f70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf1f74: ldr             x0, [x0, #0x38]
    // 0xbf1f78: StoreField: r1->field_2b = r0
    //     0xbf1f78: stur            w0, [x1, #0x2b]
    // 0xbf1f7c: StoreField: r1->field_2f = rZR
    //     0xbf1f7c: stur            xzr, [x1, #0x2f]
    // 0xbf1f80: ldur            x0, [fp, #-0x10]
    // 0xbf1f84: StoreField: r1->field_b = r0
    //     0xbf1f84: stur            w0, [x1, #0xb]
    // 0xbf1f88: r0 = Padding()
    //     0xbf1f88: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf1f8c: mov             x1, x0
    // 0xbf1f90: r0 = Instance_EdgeInsets
    //     0xbf1f90: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbf1f94: ldr             x0, [x0, #0x1f0]
    // 0xbf1f98: stur            x1, [fp, #-0x10]
    // 0xbf1f9c: StoreField: r1->field_f = r0
    //     0xbf1f9c: stur            w0, [x1, #0xf]
    // 0xbf1fa0: ldur            x0, [fp, #-8]
    // 0xbf1fa4: StoreField: r1->field_b = r0
    //     0xbf1fa4: stur            w0, [x1, #0xb]
    // 0xbf1fa8: r0 = Container()
    //     0xbf1fa8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf1fac: stur            x0, [fp, #-8]
    // 0xbf1fb0: ldur            x16, [fp, #-0x40]
    // 0xbf1fb4: ldur            lr, [fp, #-0x10]
    // 0xbf1fb8: stp             lr, x16, [SP]
    // 0xbf1fbc: mov             x1, x0
    // 0xbf1fc0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbf1fc0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbf1fc4: ldr             x4, [x4, #0x88]
    // 0xbf1fc8: r0 = Container()
    //     0xbf1fc8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf1fcc: r0 = AnimatedContainer()
    //     0xbf1fcc: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xbf1fd0: stur            x0, [fp, #-0x10]
    // 0xbf1fd4: r16 = Instance_Cubic
    //     0xbf1fd4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xbf1fd8: ldr             x16, [x16, #0xaf8]
    // 0xbf1fdc: r30 = Instance_EdgeInsets
    //     0xbf1fdc: add             lr, PP, #0x52, lsl #12  ; [pp+0x52018] Obj!EdgeInsets@d586d1
    //     0xbf1fe0: ldr             lr, [lr, #0x18]
    // 0xbf1fe4: stp             lr, x16, [SP]
    // 0xbf1fe8: mov             x1, x0
    // 0xbf1fec: ldur            x2, [fp, #-8]
    // 0xbf1ff0: r3 = Instance_Duration
    //     0xbf1ff0: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xbf1ff4: r4 = const [0, 0x5, 0x2, 0x3, curve, 0x3, margin, 0x4, null]
    //     0xbf1ff4: add             x4, PP, #0x52, lsl #12  ; [pp+0x52218] List(9) [0, 0x5, 0x2, 0x3, "curve", 0x3, "margin", 0x4, Null]
    //     0xbf1ff8: ldr             x4, [x4, #0x218]
    // 0xbf1ffc: r0 = AnimatedContainer()
    //     0xbf1ffc: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xbf2000: ldur            x0, [fp, #-0x10]
    // 0xbf2004: LeaveFrame
    //     0xbf2004: mov             SP, fp
    //     0xbf2008: ldp             fp, lr, [SP], #0x10
    // 0xbf200c: ret
    //     0xbf200c: ret             
    // 0xbf2010: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf2010: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf2014: b               #0xbf1150
    // 0xbf2018: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf2018: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf201c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf201c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf2020: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf2020: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf2024: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf2024: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf2028: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf2028: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf202c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf202c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf2030: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf2030: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf2034: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf2034: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf2038: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf2038: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf203c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf203c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf2040: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf2040: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf2044: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf2044: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildImagesRowWithMore(/* No info */) {
    // ** addr: 0xbf2048, size: 0x490
    // 0xbf2048: EnterFrame
    //     0xbf2048: stp             fp, lr, [SP, #-0x10]!
    //     0xbf204c: mov             fp, SP
    // 0xbf2050: AllocStack(0x58)
    //     0xbf2050: sub             SP, SP, #0x58
    // 0xbf2054: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xbf2054: stur            x1, [fp, #-8]
    //     0xbf2058: stur            x2, [fp, #-0x10]
    //     0xbf205c: stur            x3, [fp, #-0x18]
    // 0xbf2060: CheckStackOverflow
    //     0xbf2060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf2064: cmp             SP, x16
    //     0xbf2068: b.ls            #0xbf24d0
    // 0xbf206c: r1 = 3
    //     0xbf206c: movz            x1, #0x3
    // 0xbf2070: r0 = AllocateContext()
    //     0xbf2070: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf2074: mov             x4, x0
    // 0xbf2078: ldur            x0, [fp, #-8]
    // 0xbf207c: stur            x4, [fp, #-0x20]
    // 0xbf2080: StoreField: r4->field_f = r0
    //     0xbf2080: stur            w0, [x4, #0xf]
    // 0xbf2084: ldur            x2, [fp, #-0x10]
    // 0xbf2088: StoreField: r4->field_13 = r2
    //     0xbf2088: stur            w2, [x4, #0x13]
    // 0xbf208c: ldur            x1, [fp, #-0x18]
    // 0xbf2090: ArrayStore: r4[0] = r1  ; List_4
    //     0xbf2090: stur            w1, [x4, #0x17]
    // 0xbf2094: mov             x1, x0
    // 0xbf2098: r3 = 0
    //     0xbf2098: movz            x3, #0
    // 0xbf209c: r0 = _buildImageThumbnail()
    //     0xbf209c: bl              #0xbf24d8  ; [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImageThumbnail
    // 0xbf20a0: stur            x0, [fp, #-0x10]
    // 0xbf20a4: r0 = Padding()
    //     0xbf20a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf20a8: mov             x1, x0
    // 0xbf20ac: r0 = Instance_EdgeInsets
    //     0xbf20ac: add             x0, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xbf20b0: ldr             x0, [x0, #0x550]
    // 0xbf20b4: stur            x1, [fp, #-0x18]
    // 0xbf20b8: StoreField: r1->field_f = r0
    //     0xbf20b8: stur            w0, [x1, #0xf]
    // 0xbf20bc: ldur            x2, [fp, #-0x10]
    // 0xbf20c0: StoreField: r1->field_b = r2
    //     0xbf20c0: stur            w2, [x1, #0xb]
    // 0xbf20c4: r0 = GestureDetector()
    //     0xbf20c4: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xbf20c8: ldur            x2, [fp, #-0x20]
    // 0xbf20cc: r1 = Function '<anonymous closure>':.
    //     0xbf20cc: add             x1, PP, #0x53, lsl #12  ; [pp+0x53558] AnonymousClosure: (0xbf280c), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore (0xbf2048)
    //     0xbf20d0: ldr             x1, [x1, #0x558]
    // 0xbf20d4: stur            x0, [fp, #-0x10]
    // 0xbf20d8: r0 = AllocateClosure()
    //     0xbf20d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf20dc: ldur            x16, [fp, #-0x18]
    // 0xbf20e0: stp             x16, x0, [SP]
    // 0xbf20e4: ldur            x1, [fp, #-0x10]
    // 0xbf20e8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xbf20e8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xbf20ec: ldr             x4, [x4, #0xaf0]
    // 0xbf20f0: r0 = GestureDetector()
    //     0xbf20f0: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xbf20f4: ldur            x0, [fp, #-0x20]
    // 0xbf20f8: LoadField: r2 = r0->field_13
    //     0xbf20f8: ldur            w2, [x0, #0x13]
    // 0xbf20fc: DecompressPointer r2
    //     0xbf20fc: add             x2, x2, HEAP, lsl #32
    // 0xbf2100: ldur            x1, [fp, #-8]
    // 0xbf2104: r3 = 1
    //     0xbf2104: movz            x3, #0x1
    // 0xbf2108: r0 = _buildImageThumbnail()
    //     0xbf2108: bl              #0xbf24d8  ; [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImageThumbnail
    // 0xbf210c: stur            x0, [fp, #-8]
    // 0xbf2110: r0 = Padding()
    //     0xbf2110: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf2114: mov             x1, x0
    // 0xbf2118: r0 = Instance_EdgeInsets
    //     0xbf2118: add             x0, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xbf211c: ldr             x0, [x0, #0x550]
    // 0xbf2120: stur            x1, [fp, #-0x18]
    // 0xbf2124: StoreField: r1->field_f = r0
    //     0xbf2124: stur            w0, [x1, #0xf]
    // 0xbf2128: ldur            x0, [fp, #-8]
    // 0xbf212c: StoreField: r1->field_b = r0
    //     0xbf212c: stur            w0, [x1, #0xb]
    // 0xbf2130: r0 = GestureDetector()
    //     0xbf2130: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xbf2134: ldur            x2, [fp, #-0x20]
    // 0xbf2138: r1 = Function '<anonymous closure>':.
    //     0xbf2138: add             x1, PP, #0x53, lsl #12  ; [pp+0x53560] AnonymousClosure: (0xbf27b0), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore (0xbf2048)
    //     0xbf213c: ldr             x1, [x1, #0x560]
    // 0xbf2140: stur            x0, [fp, #-8]
    // 0xbf2144: r0 = AllocateClosure()
    //     0xbf2144: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf2148: ldur            x16, [fp, #-0x18]
    // 0xbf214c: stp             x16, x0, [SP]
    // 0xbf2150: ldur            x1, [fp, #-8]
    // 0xbf2154: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xbf2154: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xbf2158: ldr             x4, [x4, #0xaf0]
    // 0xbf215c: r0 = GestureDetector()
    //     0xbf215c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xbf2160: r1 = Instance_Color
    //     0xbf2160: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf2164: d0 = 0.030000
    //     0xbf2164: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xbf2168: ldr             d0, [x17, #0x238]
    // 0xbf216c: r0 = withOpacity()
    //     0xbf216c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbf2170: stur            x0, [fp, #-0x18]
    // 0xbf2174: r0 = Radius()
    //     0xbf2174: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xbf2178: d0 = 10.000000
    //     0xbf2178: fmov            d0, #10.00000000
    // 0xbf217c: stur            x0, [fp, #-0x28]
    // 0xbf2180: StoreField: r0->field_7 = d0
    //     0xbf2180: stur            d0, [x0, #7]
    // 0xbf2184: StoreField: r0->field_f = d0
    //     0xbf2184: stur            d0, [x0, #0xf]
    // 0xbf2188: r0 = BorderRadius()
    //     0xbf2188: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xbf218c: mov             x1, x0
    // 0xbf2190: ldur            x0, [fp, #-0x28]
    // 0xbf2194: stur            x1, [fp, #-0x30]
    // 0xbf2198: StoreField: r1->field_7 = r0
    //     0xbf2198: stur            w0, [x1, #7]
    // 0xbf219c: StoreField: r1->field_b = r0
    //     0xbf219c: stur            w0, [x1, #0xb]
    // 0xbf21a0: StoreField: r1->field_f = r0
    //     0xbf21a0: stur            w0, [x1, #0xf]
    // 0xbf21a4: StoreField: r1->field_13 = r0
    //     0xbf21a4: stur            w0, [x1, #0x13]
    // 0xbf21a8: r0 = BoxDecoration()
    //     0xbf21a8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbf21ac: mov             x3, x0
    // 0xbf21b0: ldur            x0, [fp, #-0x18]
    // 0xbf21b4: stur            x3, [fp, #-0x28]
    // 0xbf21b8: StoreField: r3->field_7 = r0
    //     0xbf21b8: stur            w0, [x3, #7]
    // 0xbf21bc: ldur            x0, [fp, #-0x30]
    // 0xbf21c0: StoreField: r3->field_13 = r0
    //     0xbf21c0: stur            w0, [x3, #0x13]
    // 0xbf21c4: r0 = Instance_BoxShape
    //     0xbf21c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbf21c8: ldr             x0, [x0, #0x80]
    // 0xbf21cc: StoreField: r3->field_23 = r0
    //     0xbf21cc: stur            w0, [x3, #0x23]
    // 0xbf21d0: r1 = Null
    //     0xbf21d0: mov             x1, NULL
    // 0xbf21d4: r2 = 4
    //     0xbf21d4: movz            x2, #0x4
    // 0xbf21d8: r0 = AllocateArray()
    //     0xbf21d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf21dc: mov             x2, x0
    // 0xbf21e0: r16 = "+"
    //     0xbf21e0: ldr             x16, [PP, #0x2f50]  ; [pp+0x2f50] "+"
    // 0xbf21e4: StoreField: r2->field_f = r16
    //     0xbf21e4: stur            w16, [x2, #0xf]
    // 0xbf21e8: ldur            x3, [fp, #-0x20]
    // 0xbf21ec: LoadField: r0 = r3->field_13
    //     0xbf21ec: ldur            w0, [x3, #0x13]
    // 0xbf21f0: DecompressPointer r0
    //     0xbf21f0: add             x0, x0, HEAP, lsl #32
    // 0xbf21f4: LoadField: r1 = r0->field_ab
    //     0xbf21f4: ldur            w1, [x0, #0xab]
    // 0xbf21f8: DecompressPointer r1
    //     0xbf21f8: add             x1, x1, HEAP, lsl #32
    // 0xbf21fc: cmp             w1, NULL
    // 0xbf2200: b.ne            #0xbf220c
    // 0xbf2204: r0 = Null
    //     0xbf2204: mov             x0, NULL
    // 0xbf2208: b               #0xbf2210
    // 0xbf220c: LoadField: r0 = r1->field_b
    //     0xbf220c: ldur            w0, [x1, #0xb]
    // 0xbf2210: cmp             w0, NULL
    // 0xbf2214: b.ne            #0xbf2220
    // 0xbf2218: r0 = 0
    //     0xbf2218: movz            x0, #0
    // 0xbf221c: b               #0xbf2228
    // 0xbf2220: r1 = LoadInt32Instr(r0)
    //     0xbf2220: sbfx            x1, x0, #1, #0x1f
    // 0xbf2224: mov             x0, x1
    // 0xbf2228: ldur            x5, [fp, #-0x10]
    // 0xbf222c: ldur            x4, [fp, #-8]
    // 0xbf2230: sub             x6, x0, #2
    // 0xbf2234: r0 = BoxInt64Instr(r6)
    //     0xbf2234: sbfiz           x0, x6, #1, #0x1f
    //     0xbf2238: cmp             x6, x0, asr #1
    //     0xbf223c: b.eq            #0xbf2248
    //     0xbf2240: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf2244: stur            x6, [x0, #7]
    // 0xbf2248: StoreField: r2->field_13 = r0
    //     0xbf2248: stur            w0, [x2, #0x13]
    // 0xbf224c: str             x2, [SP]
    // 0xbf2250: r0 = _interpolate()
    //     0xbf2250: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbf2254: ldur            x2, [fp, #-0x20]
    // 0xbf2258: stur            x0, [fp, #-0x18]
    // 0xbf225c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xbf225c: ldur            w1, [x2, #0x17]
    // 0xbf2260: DecompressPointer r1
    //     0xbf2260: add             x1, x1, HEAP, lsl #32
    // 0xbf2264: r0 = of()
    //     0xbf2264: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf2268: LoadField: r1 = r0->field_87
    //     0xbf2268: ldur            w1, [x0, #0x87]
    // 0xbf226c: DecompressPointer r1
    //     0xbf226c: add             x1, x1, HEAP, lsl #32
    // 0xbf2270: LoadField: r0 = r1->field_2b
    //     0xbf2270: ldur            w0, [x1, #0x2b]
    // 0xbf2274: DecompressPointer r0
    //     0xbf2274: add             x0, x0, HEAP, lsl #32
    // 0xbf2278: r16 = 12.000000
    //     0xbf2278: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf227c: ldr             x16, [x16, #0x9e8]
    // 0xbf2280: r30 = Instance_Color
    //     0xbf2280: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf2284: stp             lr, x16, [SP]
    // 0xbf2288: mov             x1, x0
    // 0xbf228c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf228c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf2290: ldr             x4, [x4, #0xaa0]
    // 0xbf2294: r0 = copyWith()
    //     0xbf2294: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf2298: stur            x0, [fp, #-0x30]
    // 0xbf229c: r0 = Text()
    //     0xbf229c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf22a0: mov             x2, x0
    // 0xbf22a4: ldur            x0, [fp, #-0x18]
    // 0xbf22a8: stur            x2, [fp, #-0x38]
    // 0xbf22ac: StoreField: r2->field_b = r0
    //     0xbf22ac: stur            w0, [x2, #0xb]
    // 0xbf22b0: ldur            x0, [fp, #-0x30]
    // 0xbf22b4: StoreField: r2->field_13 = r0
    //     0xbf22b4: stur            w0, [x2, #0x13]
    // 0xbf22b8: ldur            x0, [fp, #-0x20]
    // 0xbf22bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf22bc: ldur            w1, [x0, #0x17]
    // 0xbf22c0: DecompressPointer r1
    //     0xbf22c0: add             x1, x1, HEAP, lsl #32
    // 0xbf22c4: r0 = of()
    //     0xbf22c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf22c8: LoadField: r1 = r0->field_87
    //     0xbf22c8: ldur            w1, [x0, #0x87]
    // 0xbf22cc: DecompressPointer r1
    //     0xbf22cc: add             x1, x1, HEAP, lsl #32
    // 0xbf22d0: LoadField: r0 = r1->field_2b
    //     0xbf22d0: ldur            w0, [x1, #0x2b]
    // 0xbf22d4: DecompressPointer r0
    //     0xbf22d4: add             x0, x0, HEAP, lsl #32
    // 0xbf22d8: r16 = 12.000000
    //     0xbf22d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf22dc: ldr             x16, [x16, #0x9e8]
    // 0xbf22e0: r30 = Instance_Color
    //     0xbf22e0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf22e4: stp             lr, x16, [SP]
    // 0xbf22e8: mov             x1, x0
    // 0xbf22ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf22ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf22f0: ldr             x4, [x4, #0xaa0]
    // 0xbf22f4: r0 = copyWith()
    //     0xbf22f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf22f8: stur            x0, [fp, #-0x18]
    // 0xbf22fc: r0 = Text()
    //     0xbf22fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf2300: mov             x3, x0
    // 0xbf2304: r0 = "Photos"
    //     0xbf2304: add             x0, PP, #0x52, lsl #12  ; [pp+0x52260] "Photos"
    //     0xbf2308: ldr             x0, [x0, #0x260]
    // 0xbf230c: stur            x3, [fp, #-0x30]
    // 0xbf2310: StoreField: r3->field_b = r0
    //     0xbf2310: stur            w0, [x3, #0xb]
    // 0xbf2314: ldur            x0, [fp, #-0x18]
    // 0xbf2318: StoreField: r3->field_13 = r0
    //     0xbf2318: stur            w0, [x3, #0x13]
    // 0xbf231c: r1 = Null
    //     0xbf231c: mov             x1, NULL
    // 0xbf2320: r2 = 4
    //     0xbf2320: movz            x2, #0x4
    // 0xbf2324: r0 = AllocateArray()
    //     0xbf2324: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf2328: mov             x2, x0
    // 0xbf232c: ldur            x0, [fp, #-0x38]
    // 0xbf2330: stur            x2, [fp, #-0x18]
    // 0xbf2334: StoreField: r2->field_f = r0
    //     0xbf2334: stur            w0, [x2, #0xf]
    // 0xbf2338: ldur            x0, [fp, #-0x30]
    // 0xbf233c: StoreField: r2->field_13 = r0
    //     0xbf233c: stur            w0, [x2, #0x13]
    // 0xbf2340: r1 = <Widget>
    //     0xbf2340: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf2344: r0 = AllocateGrowableArray()
    //     0xbf2344: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf2348: mov             x1, x0
    // 0xbf234c: ldur            x0, [fp, #-0x18]
    // 0xbf2350: stur            x1, [fp, #-0x30]
    // 0xbf2354: StoreField: r1->field_f = r0
    //     0xbf2354: stur            w0, [x1, #0xf]
    // 0xbf2358: r0 = 4
    //     0xbf2358: movz            x0, #0x4
    // 0xbf235c: StoreField: r1->field_b = r0
    //     0xbf235c: stur            w0, [x1, #0xb]
    // 0xbf2360: r0 = Column()
    //     0xbf2360: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf2364: mov             x1, x0
    // 0xbf2368: r0 = Instance_Axis
    //     0xbf2368: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf236c: stur            x1, [fp, #-0x18]
    // 0xbf2370: StoreField: r1->field_f = r0
    //     0xbf2370: stur            w0, [x1, #0xf]
    // 0xbf2374: r0 = Instance_MainAxisAlignment
    //     0xbf2374: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbf2378: ldr             x0, [x0, #0xab0]
    // 0xbf237c: StoreField: r1->field_13 = r0
    //     0xbf237c: stur            w0, [x1, #0x13]
    // 0xbf2380: r0 = Instance_MainAxisSize
    //     0xbf2380: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf2384: ldr             x0, [x0, #0xa10]
    // 0xbf2388: ArrayStore: r1[0] = r0  ; List_4
    //     0xbf2388: stur            w0, [x1, #0x17]
    // 0xbf238c: r2 = Instance_CrossAxisAlignment
    //     0xbf238c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbf2390: ldr             x2, [x2, #0xa18]
    // 0xbf2394: StoreField: r1->field_1b = r2
    //     0xbf2394: stur            w2, [x1, #0x1b]
    // 0xbf2398: r3 = Instance_VerticalDirection
    //     0xbf2398: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf239c: ldr             x3, [x3, #0xa20]
    // 0xbf23a0: StoreField: r1->field_23 = r3
    //     0xbf23a0: stur            w3, [x1, #0x23]
    // 0xbf23a4: r4 = Instance_Clip
    //     0xbf23a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf23a8: ldr             x4, [x4, #0x38]
    // 0xbf23ac: StoreField: r1->field_2b = r4
    //     0xbf23ac: stur            w4, [x1, #0x2b]
    // 0xbf23b0: StoreField: r1->field_2f = rZR
    //     0xbf23b0: stur            xzr, [x1, #0x2f]
    // 0xbf23b4: ldur            x5, [fp, #-0x30]
    // 0xbf23b8: StoreField: r1->field_b = r5
    //     0xbf23b8: stur            w5, [x1, #0xb]
    // 0xbf23bc: r0 = Container()
    //     0xbf23bc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf23c0: stur            x0, [fp, #-0x30]
    // 0xbf23c4: r16 = 64.000000
    //     0xbf23c4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xbf23c8: ldr             x16, [x16, #0x838]
    // 0xbf23cc: r30 = 64.000000
    //     0xbf23cc: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xbf23d0: ldr             lr, [lr, #0x838]
    // 0xbf23d4: stp             lr, x16, [SP, #0x10]
    // 0xbf23d8: ldur            x16, [fp, #-0x28]
    // 0xbf23dc: ldur            lr, [fp, #-0x18]
    // 0xbf23e0: stp             lr, x16, [SP]
    // 0xbf23e4: mov             x1, x0
    // 0xbf23e8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xbf23e8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xbf23ec: ldr             x4, [x4, #0x870]
    // 0xbf23f0: r0 = Container()
    //     0xbf23f0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf23f4: r0 = GestureDetector()
    //     0xbf23f4: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xbf23f8: ldur            x2, [fp, #-0x20]
    // 0xbf23fc: r1 = Function '<anonymous closure>':.
    //     0xbf23fc: add             x1, PP, #0x53, lsl #12  ; [pp+0x53568] AnonymousClosure: (0xbf2614), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore (0xbf2048)
    //     0xbf2400: ldr             x1, [x1, #0x568]
    // 0xbf2404: stur            x0, [fp, #-0x18]
    // 0xbf2408: r0 = AllocateClosure()
    //     0xbf2408: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf240c: ldur            x16, [fp, #-0x30]
    // 0xbf2410: stp             x16, x0, [SP]
    // 0xbf2414: ldur            x1, [fp, #-0x18]
    // 0xbf2418: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xbf2418: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xbf241c: ldr             x4, [x4, #0xaf0]
    // 0xbf2420: r0 = GestureDetector()
    //     0xbf2420: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xbf2424: r1 = Null
    //     0xbf2424: mov             x1, NULL
    // 0xbf2428: r2 = 6
    //     0xbf2428: movz            x2, #0x6
    // 0xbf242c: r0 = AllocateArray()
    //     0xbf242c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf2430: mov             x2, x0
    // 0xbf2434: ldur            x0, [fp, #-0x10]
    // 0xbf2438: stur            x2, [fp, #-0x20]
    // 0xbf243c: StoreField: r2->field_f = r0
    //     0xbf243c: stur            w0, [x2, #0xf]
    // 0xbf2440: ldur            x0, [fp, #-8]
    // 0xbf2444: StoreField: r2->field_13 = r0
    //     0xbf2444: stur            w0, [x2, #0x13]
    // 0xbf2448: ldur            x0, [fp, #-0x18]
    // 0xbf244c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbf244c: stur            w0, [x2, #0x17]
    // 0xbf2450: r1 = <Widget>
    //     0xbf2450: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf2454: r0 = AllocateGrowableArray()
    //     0xbf2454: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf2458: mov             x1, x0
    // 0xbf245c: ldur            x0, [fp, #-0x20]
    // 0xbf2460: stur            x1, [fp, #-8]
    // 0xbf2464: StoreField: r1->field_f = r0
    //     0xbf2464: stur            w0, [x1, #0xf]
    // 0xbf2468: r0 = 6
    //     0xbf2468: movz            x0, #0x6
    // 0xbf246c: StoreField: r1->field_b = r0
    //     0xbf246c: stur            w0, [x1, #0xb]
    // 0xbf2470: r0 = Row()
    //     0xbf2470: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbf2474: r1 = Instance_Axis
    //     0xbf2474: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbf2478: StoreField: r0->field_f = r1
    //     0xbf2478: stur            w1, [x0, #0xf]
    // 0xbf247c: r1 = Instance_MainAxisAlignment
    //     0xbf247c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf2480: ldr             x1, [x1, #0xa08]
    // 0xbf2484: StoreField: r0->field_13 = r1
    //     0xbf2484: stur            w1, [x0, #0x13]
    // 0xbf2488: r1 = Instance_MainAxisSize
    //     0xbf2488: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf248c: ldr             x1, [x1, #0xa10]
    // 0xbf2490: ArrayStore: r0[0] = r1  ; List_4
    //     0xbf2490: stur            w1, [x0, #0x17]
    // 0xbf2494: r1 = Instance_CrossAxisAlignment
    //     0xbf2494: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbf2498: ldr             x1, [x1, #0xa18]
    // 0xbf249c: StoreField: r0->field_1b = r1
    //     0xbf249c: stur            w1, [x0, #0x1b]
    // 0xbf24a0: r1 = Instance_VerticalDirection
    //     0xbf24a0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf24a4: ldr             x1, [x1, #0xa20]
    // 0xbf24a8: StoreField: r0->field_23 = r1
    //     0xbf24a8: stur            w1, [x0, #0x23]
    // 0xbf24ac: r1 = Instance_Clip
    //     0xbf24ac: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf24b0: ldr             x1, [x1, #0x38]
    // 0xbf24b4: StoreField: r0->field_2b = r1
    //     0xbf24b4: stur            w1, [x0, #0x2b]
    // 0xbf24b8: StoreField: r0->field_2f = rZR
    //     0xbf24b8: stur            xzr, [x0, #0x2f]
    // 0xbf24bc: ldur            x1, [fp, #-8]
    // 0xbf24c0: StoreField: r0->field_b = r1
    //     0xbf24c0: stur            w1, [x0, #0xb]
    // 0xbf24c4: LeaveFrame
    //     0xbf24c4: mov             SP, fp
    //     0xbf24c8: ldp             fp, lr, [SP], #0x10
    // 0xbf24cc: ret
    //     0xbf24cc: ret             
    // 0xbf24d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf24d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf24d4: b               #0xbf206c
  }
  _ _buildImageThumbnail(/* No info */) {
    // ** addr: 0xbf24d8, size: 0x13c
    // 0xbf24d8: EnterFrame
    //     0xbf24d8: stp             fp, lr, [SP, #-0x10]!
    //     0xbf24dc: mov             fp, SP
    // 0xbf24e0: AllocStack(0x58)
    //     0xbf24e0: sub             SP, SP, #0x58
    // 0xbf24e4: SetupParameters(_TestimonialCarouselState this /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r1, fp-0x10 */)
    //     0xbf24e4: mov             x0, x1
    //     0xbf24e8: mov             x1, x3
    //     0xbf24ec: stur            x2, [fp, #-8]
    //     0xbf24f0: stur            x3, [fp, #-0x10]
    // 0xbf24f4: CheckStackOverflow
    //     0xbf24f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf24f8: cmp             SP, x16
    //     0xbf24fc: b.ls            #0xbf2608
    // 0xbf2500: r0 = ImageHeaders.forImages()
    //     0xbf2500: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbf2504: mov             x3, x0
    // 0xbf2508: ldur            x0, [fp, #-8]
    // 0xbf250c: stur            x3, [fp, #-0x18]
    // 0xbf2510: LoadField: r2 = r0->field_ab
    //     0xbf2510: ldur            w2, [x0, #0xab]
    // 0xbf2514: DecompressPointer r2
    //     0xbf2514: add             x2, x2, HEAP, lsl #32
    // 0xbf2518: cmp             w2, NULL
    // 0xbf251c: b.ne            #0xbf2528
    // 0xbf2520: r0 = Null
    //     0xbf2520: mov             x0, NULL
    // 0xbf2524: b               #0xbf257c
    // 0xbf2528: ldur            x4, [fp, #-0x10]
    // 0xbf252c: LoadField: r0 = r2->field_b
    //     0xbf252c: ldur            w0, [x2, #0xb]
    // 0xbf2530: r1 = LoadInt32Instr(r0)
    //     0xbf2530: sbfx            x1, x0, #1, #0x1f
    // 0xbf2534: mov             x0, x1
    // 0xbf2538: mov             x1, x4
    // 0xbf253c: cmp             x1, x0
    // 0xbf2540: b.hs            #0xbf2610
    // 0xbf2544: LoadField: r0 = r2->field_f
    //     0xbf2544: ldur            w0, [x2, #0xf]
    // 0xbf2548: DecompressPointer r0
    //     0xbf2548: add             x0, x0, HEAP, lsl #32
    // 0xbf254c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbf254c: add             x16, x0, x4, lsl #2
    //     0xbf2550: ldur            w1, [x16, #0xf]
    // 0xbf2554: DecompressPointer r1
    //     0xbf2554: add             x1, x1, HEAP, lsl #32
    // 0xbf2558: LoadField: r0 = r1->field_7
    //     0xbf2558: ldur            w0, [x1, #7]
    // 0xbf255c: DecompressPointer r0
    //     0xbf255c: add             x0, x0, HEAP, lsl #32
    // 0xbf2560: cmp             w0, NULL
    // 0xbf2564: b.ne            #0xbf2570
    // 0xbf2568: r0 = Null
    //     0xbf2568: mov             x0, NULL
    // 0xbf256c: b               #0xbf257c
    // 0xbf2570: LoadField: r1 = r0->field_b
    //     0xbf2570: ldur            w1, [x0, #0xb]
    // 0xbf2574: DecompressPointer r1
    //     0xbf2574: add             x1, x1, HEAP, lsl #32
    // 0xbf2578: mov             x0, x1
    // 0xbf257c: cmp             w0, NULL
    // 0xbf2580: b.ne            #0xbf2588
    // 0xbf2584: r0 = ""
    //     0xbf2584: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf2588: stur            x0, [fp, #-8]
    // 0xbf258c: r1 = Function '<anonymous closure>':.
    //     0xbf258c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53578] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbf2590: ldr             x1, [x1, #0x578]
    // 0xbf2594: r2 = Null
    //     0xbf2594: mov             x2, NULL
    // 0xbf2598: r0 = AllocateClosure()
    //     0xbf2598: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf259c: r1 = Function '<anonymous closure>':.
    //     0xbf259c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53580] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbf25a0: ldr             x1, [x1, #0x580]
    // 0xbf25a4: r2 = Null
    //     0xbf25a4: mov             x2, NULL
    // 0xbf25a8: stur            x0, [fp, #-0x20]
    // 0xbf25ac: r0 = AllocateClosure()
    //     0xbf25ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf25b0: stur            x0, [fp, #-0x28]
    // 0xbf25b4: r0 = CachedNetworkImage()
    //     0xbf25b4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbf25b8: stur            x0, [fp, #-0x30]
    // 0xbf25bc: ldur            x16, [fp, #-0x18]
    // 0xbf25c0: r30 = 64.000000
    //     0xbf25c0: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xbf25c4: ldr             lr, [lr, #0x838]
    // 0xbf25c8: stp             lr, x16, [SP, #0x18]
    // 0xbf25cc: r16 = 64.000000
    //     0xbf25cc: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xbf25d0: ldr             x16, [x16, #0x838]
    // 0xbf25d4: ldur            lr, [fp, #-0x20]
    // 0xbf25d8: stp             lr, x16, [SP, #8]
    // 0xbf25dc: ldur            x16, [fp, #-0x28]
    // 0xbf25e0: str             x16, [SP]
    // 0xbf25e4: mov             x1, x0
    // 0xbf25e8: ldur            x2, [fp, #-8]
    // 0xbf25ec: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, height, 0x3, httpHeaders, 0x2, progressIndicatorBuilder, 0x5, width, 0x4, null]
    //     0xbf25ec: add             x4, PP, #0x53, lsl #12  ; [pp+0x53588] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "height", 0x3, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x4, Null]
    //     0xbf25f0: ldr             x4, [x4, #0x588]
    // 0xbf25f4: r0 = CachedNetworkImage()
    //     0xbf25f4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbf25f8: ldur            x0, [fp, #-0x30]
    // 0xbf25fc: LeaveFrame
    //     0xbf25fc: mov             SP, fp
    //     0xbf2600: ldp             fp, lr, [SP], #0x10
    // 0xbf2604: ret
    //     0xbf2604: ret             
    // 0xbf2608: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf2608: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf260c: b               #0xbf2500
    // 0xbf2610: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf2610: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbf2614, size: 0x5c
    // 0xbf2614: EnterFrame
    //     0xbf2614: stp             fp, lr, [SP, #-0x10]!
    //     0xbf2618: mov             fp, SP
    // 0xbf261c: ldr             x0, [fp, #0x10]
    // 0xbf2620: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf2620: ldur            w1, [x0, #0x17]
    // 0xbf2624: DecompressPointer r1
    //     0xbf2624: add             x1, x1, HEAP, lsl #32
    // 0xbf2628: CheckStackOverflow
    //     0xbf2628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf262c: cmp             SP, x16
    //     0xbf2630: b.ls            #0xbf2668
    // 0xbf2634: LoadField: r0 = r1->field_f
    //     0xbf2634: ldur            w0, [x1, #0xf]
    // 0xbf2638: DecompressPointer r0
    //     0xbf2638: add             x0, x0, HEAP, lsl #32
    // 0xbf263c: LoadField: r2 = r1->field_13
    //     0xbf263c: ldur            w2, [x1, #0x13]
    // 0xbf2640: DecompressPointer r2
    //     0xbf2640: add             x2, x2, HEAP, lsl #32
    // 0xbf2644: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xbf2644: ldur            w5, [x1, #0x17]
    // 0xbf2648: DecompressPointer r5
    //     0xbf2648: add             x5, x5, HEAP, lsl #32
    // 0xbf264c: mov             x1, x0
    // 0xbf2650: r3 = 2
    //     0xbf2650: movz            x3, #0x2
    // 0xbf2654: r0 = _openImageViewer()
    //     0xbf2654: bl              #0xbf2670  ; [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xbf2658: r0 = Null
    //     0xbf2658: mov             x0, NULL
    // 0xbf265c: LeaveFrame
    //     0xbf265c: mov             SP, fp
    //     0xbf2660: ldp             fp, lr, [SP], #0x10
    // 0xbf2664: ret
    //     0xbf2664: ret             
    // 0xbf2668: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf2668: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf266c: b               #0xbf2634
  }
  _ _openImageViewer(/* No info */) {
    // ** addr: 0xbf2670, size: 0xd0
    // 0xbf2670: EnterFrame
    //     0xbf2670: stp             fp, lr, [SP, #-0x10]!
    //     0xbf2674: mov             fp, SP
    // 0xbf2678: AllocStack(0x38)
    //     0xbf2678: sub             SP, SP, #0x38
    // 0xbf267c: SetupParameters(_TestimonialCarouselState this /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r1, fp-0x18 */)
    //     0xbf267c: mov             x0, x1
    //     0xbf2680: mov             x1, x5
    //     0xbf2684: stur            x2, [fp, #-8]
    //     0xbf2688: stur            x3, [fp, #-0x10]
    //     0xbf268c: stur            x5, [fp, #-0x18]
    // 0xbf2690: CheckStackOverflow
    //     0xbf2690: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf2694: cmp             SP, x16
    //     0xbf2698: b.ls            #0xbf2738
    // 0xbf269c: r1 = 2
    //     0xbf269c: movz            x1, #0x2
    // 0xbf26a0: r0 = AllocateContext()
    //     0xbf26a0: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf26a4: mov             x2, x0
    // 0xbf26a8: ldur            x0, [fp, #-8]
    // 0xbf26ac: stur            x2, [fp, #-0x20]
    // 0xbf26b0: StoreField: r2->field_f = r0
    //     0xbf26b0: stur            w0, [x2, #0xf]
    // 0xbf26b4: ldur            x3, [fp, #-0x10]
    // 0xbf26b8: r0 = BoxInt64Instr(r3)
    //     0xbf26b8: sbfiz           x0, x3, #1, #0x1f
    //     0xbf26bc: cmp             x3, x0, asr #1
    //     0xbf26c0: b.eq            #0xbf26cc
    //     0xbf26c4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf26c8: stur            x3, [x0, #7]
    // 0xbf26cc: StoreField: r2->field_13 = r0
    //     0xbf26cc: stur            w0, [x2, #0x13]
    // 0xbf26d0: ldur            x1, [fp, #-0x18]
    // 0xbf26d4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbf26d4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbf26d8: r0 = of()
    //     0xbf26d8: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xbf26dc: ldur            x2, [fp, #-0x20]
    // 0xbf26e0: r1 = Function '<anonymous closure>':.
    //     0xbf26e0: add             x1, PP, #0x53, lsl #12  ; [pp+0x53570] AnonymousClosure: (0xbf2740), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer (0xbf2670)
    //     0xbf26e4: ldr             x1, [x1, #0x570]
    // 0xbf26e8: stur            x0, [fp, #-8]
    // 0xbf26ec: r0 = AllocateClosure()
    //     0xbf26ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf26f0: r1 = Null
    //     0xbf26f0: mov             x1, NULL
    // 0xbf26f4: stur            x0, [fp, #-0x18]
    // 0xbf26f8: r0 = MaterialPageRoute()
    //     0xbf26f8: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xbf26fc: mov             x1, x0
    // 0xbf2700: ldur            x2, [fp, #-0x18]
    // 0xbf2704: stur            x0, [fp, #-0x18]
    // 0xbf2708: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf2708: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf270c: r0 = MaterialPageRoute()
    //     0xbf270c: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xbf2710: ldur            x16, [fp, #-8]
    // 0xbf2714: stp             x16, NULL, [SP, #8]
    // 0xbf2718: ldur            x16, [fp, #-0x18]
    // 0xbf271c: str             x16, [SP]
    // 0xbf2720: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbf2720: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbf2724: r0 = push()
    //     0xbf2724: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xbf2728: r0 = Null
    //     0xbf2728: mov             x0, NULL
    // 0xbf272c: LeaveFrame
    //     0xbf272c: mov             SP, fp
    //     0xbf2730: ldp             fp, lr, [SP], #0x10
    // 0xbf2734: ret
    //     0xbf2734: ret             
    // 0xbf2738: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf2738: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf273c: b               #0xbf269c
  }
  [closure] TestimonialMoreImagesWidget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xbf2740, size: 0x64
    // 0xbf2740: EnterFrame
    //     0xbf2740: stp             fp, lr, [SP, #-0x10]!
    //     0xbf2744: mov             fp, SP
    // 0xbf2748: AllocStack(0x10)
    //     0xbf2748: sub             SP, SP, #0x10
    // 0xbf274c: SetupParameters()
    //     0xbf274c: ldr             x0, [fp, #0x18]
    //     0xbf2750: ldur            w1, [x0, #0x17]
    //     0xbf2754: add             x1, x1, HEAP, lsl #32
    // 0xbf2758: LoadField: r0 = r1->field_13
    //     0xbf2758: ldur            w0, [x1, #0x13]
    // 0xbf275c: DecompressPointer r0
    //     0xbf275c: add             x0, x0, HEAP, lsl #32
    // 0xbf2760: stur            x0, [fp, #-0x10]
    // 0xbf2764: LoadField: r2 = r1->field_f
    //     0xbf2764: ldur            w2, [x1, #0xf]
    // 0xbf2768: DecompressPointer r2
    //     0xbf2768: add             x2, x2, HEAP, lsl #32
    // 0xbf276c: LoadField: r1 = r2->field_ab
    //     0xbf276c: ldur            w1, [x2, #0xab]
    // 0xbf2770: DecompressPointer r1
    //     0xbf2770: add             x1, x1, HEAP, lsl #32
    // 0xbf2774: stur            x1, [fp, #-8]
    // 0xbf2778: r0 = TestimonialMoreImagesWidget()
    //     0xbf2778: bl              #0xbf27a4  ; AllocateTestimonialMoreImagesWidgetStub -> TestimonialMoreImagesWidget (size=0x1c)
    // 0xbf277c: ldur            x1, [fp, #-8]
    // 0xbf2780: StoreField: r0->field_b = r1
    //     0xbf2780: stur            w1, [x0, #0xb]
    // 0xbf2784: ldur            x1, [fp, #-0x10]
    // 0xbf2788: r2 = LoadInt32Instr(r1)
    //     0xbf2788: sbfx            x2, x1, #1, #0x1f
    //     0xbf278c: tbz             w1, #0, #0xbf2794
    //     0xbf2790: ldur            x2, [x1, #7]
    // 0xbf2794: StoreField: r0->field_f = r2
    //     0xbf2794: stur            x2, [x0, #0xf]
    // 0xbf2798: LeaveFrame
    //     0xbf2798: mov             SP, fp
    //     0xbf279c: ldp             fp, lr, [SP], #0x10
    // 0xbf27a0: ret
    //     0xbf27a0: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbf27b0, size: 0x5c
    // 0xbf27b0: EnterFrame
    //     0xbf27b0: stp             fp, lr, [SP, #-0x10]!
    //     0xbf27b4: mov             fp, SP
    // 0xbf27b8: ldr             x0, [fp, #0x10]
    // 0xbf27bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf27bc: ldur            w1, [x0, #0x17]
    // 0xbf27c0: DecompressPointer r1
    //     0xbf27c0: add             x1, x1, HEAP, lsl #32
    // 0xbf27c4: CheckStackOverflow
    //     0xbf27c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf27c8: cmp             SP, x16
    //     0xbf27cc: b.ls            #0xbf2804
    // 0xbf27d0: LoadField: r0 = r1->field_f
    //     0xbf27d0: ldur            w0, [x1, #0xf]
    // 0xbf27d4: DecompressPointer r0
    //     0xbf27d4: add             x0, x0, HEAP, lsl #32
    // 0xbf27d8: LoadField: r2 = r1->field_13
    //     0xbf27d8: ldur            w2, [x1, #0x13]
    // 0xbf27dc: DecompressPointer r2
    //     0xbf27dc: add             x2, x2, HEAP, lsl #32
    // 0xbf27e0: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xbf27e0: ldur            w5, [x1, #0x17]
    // 0xbf27e4: DecompressPointer r5
    //     0xbf27e4: add             x5, x5, HEAP, lsl #32
    // 0xbf27e8: mov             x1, x0
    // 0xbf27ec: r3 = 1
    //     0xbf27ec: movz            x3, #0x1
    // 0xbf27f0: r0 = _openImageViewer()
    //     0xbf27f0: bl              #0xbf2670  ; [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xbf27f4: r0 = Null
    //     0xbf27f4: mov             x0, NULL
    // 0xbf27f8: LeaveFrame
    //     0xbf27f8: mov             SP, fp
    //     0xbf27fc: ldp             fp, lr, [SP], #0x10
    // 0xbf2800: ret
    //     0xbf2800: ret             
    // 0xbf2804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf2804: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf2808: b               #0xbf27d0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbf280c, size: 0x5c
    // 0xbf280c: EnterFrame
    //     0xbf280c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf2810: mov             fp, SP
    // 0xbf2814: ldr             x0, [fp, #0x10]
    // 0xbf2818: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf2818: ldur            w1, [x0, #0x17]
    // 0xbf281c: DecompressPointer r1
    //     0xbf281c: add             x1, x1, HEAP, lsl #32
    // 0xbf2820: CheckStackOverflow
    //     0xbf2820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf2824: cmp             SP, x16
    //     0xbf2828: b.ls            #0xbf2860
    // 0xbf282c: LoadField: r0 = r1->field_f
    //     0xbf282c: ldur            w0, [x1, #0xf]
    // 0xbf2830: DecompressPointer r0
    //     0xbf2830: add             x0, x0, HEAP, lsl #32
    // 0xbf2834: LoadField: r2 = r1->field_13
    //     0xbf2834: ldur            w2, [x1, #0x13]
    // 0xbf2838: DecompressPointer r2
    //     0xbf2838: add             x2, x2, HEAP, lsl #32
    // 0xbf283c: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xbf283c: ldur            w5, [x1, #0x17]
    // 0xbf2840: DecompressPointer r5
    //     0xbf2840: add             x5, x5, HEAP, lsl #32
    // 0xbf2844: mov             x1, x0
    // 0xbf2848: r3 = 0
    //     0xbf2848: movz            x3, #0
    // 0xbf284c: r0 = _openImageViewer()
    //     0xbf284c: bl              #0xbf2670  ; [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xbf2850: r0 = Null
    //     0xbf2850: mov             x0, NULL
    // 0xbf2854: LeaveFrame
    //     0xbf2854: mov             SP, fp
    //     0xbf2858: ldp             fp, lr, [SP], #0x10
    // 0xbf285c: ret
    //     0xbf285c: ret             
    // 0xbf2860: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf2860: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf2864: b               #0xbf282c
  }
  _ _buildImagesRow(/* No info */) {
    // ** addr: 0xbf2868, size: 0xdc
    // 0xbf2868: EnterFrame
    //     0xbf2868: stp             fp, lr, [SP, #-0x10]!
    //     0xbf286c: mov             fp, SP
    // 0xbf2870: AllocStack(0x30)
    //     0xbf2870: sub             SP, SP, #0x30
    // 0xbf2874: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbf2874: stur            x1, [fp, #-8]
    //     0xbf2878: stur            x2, [fp, #-0x10]
    // 0xbf287c: CheckStackOverflow
    //     0xbf287c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf2880: cmp             SP, x16
    //     0xbf2884: b.ls            #0xbf293c
    // 0xbf2888: r1 = 2
    //     0xbf2888: movz            x1, #0x2
    // 0xbf288c: r0 = AllocateContext()
    //     0xbf288c: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf2890: mov             x1, x0
    // 0xbf2894: ldur            x0, [fp, #-8]
    // 0xbf2898: StoreField: r1->field_f = r0
    //     0xbf2898: stur            w0, [x1, #0xf]
    // 0xbf289c: ldur            x0, [fp, #-0x10]
    // 0xbf28a0: StoreField: r1->field_13 = r0
    //     0xbf28a0: stur            w0, [x1, #0x13]
    // 0xbf28a4: LoadField: r2 = r0->field_ab
    //     0xbf28a4: ldur            w2, [x0, #0xab]
    // 0xbf28a8: DecompressPointer r2
    //     0xbf28a8: add             x2, x2, HEAP, lsl #32
    // 0xbf28ac: cmp             w2, NULL
    // 0xbf28b0: b.ne            #0xbf28bc
    // 0xbf28b4: r0 = Null
    //     0xbf28b4: mov             x0, NULL
    // 0xbf28b8: b               #0xbf28c0
    // 0xbf28bc: LoadField: r0 = r2->field_b
    //     0xbf28bc: ldur            w0, [x2, #0xb]
    // 0xbf28c0: cmp             w0, NULL
    // 0xbf28c4: b.ne            #0xbf28d0
    // 0xbf28c8: r0 = 0
    //     0xbf28c8: movz            x0, #0
    // 0xbf28cc: b               #0xbf28d8
    // 0xbf28d0: r2 = LoadInt32Instr(r0)
    //     0xbf28d0: sbfx            x2, x0, #1, #0x1f
    // 0xbf28d4: mov             x0, x2
    // 0xbf28d8: lsl             x3, x0, #1
    // 0xbf28dc: mov             x2, x1
    // 0xbf28e0: stur            x3, [fp, #-8]
    // 0xbf28e4: r1 = Function '<anonymous closure>':.
    //     0xbf28e4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53590] AnonymousClosure: (0xbf2944), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRow (0xbf2868)
    //     0xbf28e8: ldr             x1, [x1, #0x590]
    // 0xbf28ec: r0 = AllocateClosure()
    //     0xbf28ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf28f0: stur            x0, [fp, #-0x10]
    // 0xbf28f4: r0 = ListView()
    //     0xbf28f4: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbf28f8: stur            x0, [fp, #-0x18]
    // 0xbf28fc: r16 = true
    //     0xbf28fc: add             x16, NULL, #0x20  ; true
    // 0xbf2900: r30 = Instance_Axis
    //     0xbf2900: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbf2904: stp             lr, x16, [SP, #8]
    // 0xbf2908: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbf2908: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbf290c: ldr             x16, [x16, #0x1c8]
    // 0xbf2910: str             x16, [SP]
    // 0xbf2914: mov             x1, x0
    // 0xbf2918: ldur            x2, [fp, #-0x10]
    // 0xbf291c: ldur            x3, [fp, #-8]
    // 0xbf2920: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x5, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xbf2920: add             x4, PP, #0x52, lsl #12  ; [pp+0x52200] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x5, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xbf2924: ldr             x4, [x4, #0x200]
    // 0xbf2928: r0 = ListView.builder()
    //     0xbf2928: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbf292c: ldur            x0, [fp, #-0x18]
    // 0xbf2930: LeaveFrame
    //     0xbf2930: mov             SP, fp
    //     0xbf2934: ldp             fp, lr, [SP], #0x10
    // 0xbf2938: ret
    //     0xbf2938: ret             
    // 0xbf293c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf293c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf2940: b               #0xbf2888
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbf2944, size: 0x1c4
    // 0xbf2944: EnterFrame
    //     0xbf2944: stp             fp, lr, [SP, #-0x10]!
    //     0xbf2948: mov             fp, SP
    // 0xbf294c: AllocStack(0x58)
    //     0xbf294c: sub             SP, SP, #0x58
    // 0xbf2950: SetupParameters()
    //     0xbf2950: ldr             x0, [fp, #0x20]
    //     0xbf2954: ldur            w1, [x0, #0x17]
    //     0xbf2958: add             x1, x1, HEAP, lsl #32
    //     0xbf295c: stur            x1, [fp, #-8]
    // 0xbf2960: CheckStackOverflow
    //     0xbf2960: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf2964: cmp             SP, x16
    //     0xbf2968: b.ls            #0xbf2afc
    // 0xbf296c: r1 = 2
    //     0xbf296c: movz            x1, #0x2
    // 0xbf2970: r0 = AllocateContext()
    //     0xbf2970: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf2974: mov             x1, x0
    // 0xbf2978: ldur            x0, [fp, #-8]
    // 0xbf297c: stur            x1, [fp, #-0x10]
    // 0xbf2980: StoreField: r1->field_b = r0
    //     0xbf2980: stur            w0, [x1, #0xb]
    // 0xbf2984: ldr             x2, [fp, #0x18]
    // 0xbf2988: StoreField: r1->field_f = r2
    //     0xbf2988: stur            w2, [x1, #0xf]
    // 0xbf298c: ldr             x2, [fp, #0x10]
    // 0xbf2990: StoreField: r1->field_13 = r2
    //     0xbf2990: stur            w2, [x1, #0x13]
    // 0xbf2994: r0 = ImageHeaders.forImages()
    //     0xbf2994: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbf2998: mov             x3, x0
    // 0xbf299c: ldur            x0, [fp, #-8]
    // 0xbf29a0: stur            x3, [fp, #-0x18]
    // 0xbf29a4: LoadField: r1 = r0->field_13
    //     0xbf29a4: ldur            w1, [x0, #0x13]
    // 0xbf29a8: DecompressPointer r1
    //     0xbf29a8: add             x1, x1, HEAP, lsl #32
    // 0xbf29ac: LoadField: r2 = r1->field_ab
    //     0xbf29ac: ldur            w2, [x1, #0xab]
    // 0xbf29b0: DecompressPointer r2
    //     0xbf29b0: add             x2, x2, HEAP, lsl #32
    // 0xbf29b4: cmp             w2, NULL
    // 0xbf29b8: b.ne            #0xbf29c8
    // 0xbf29bc: ldur            x4, [fp, #-0x10]
    // 0xbf29c0: r0 = Null
    //     0xbf29c0: mov             x0, NULL
    // 0xbf29c4: b               #0xbf2a2c
    // 0xbf29c8: ldur            x4, [fp, #-0x10]
    // 0xbf29cc: LoadField: r0 = r4->field_13
    //     0xbf29cc: ldur            w0, [x4, #0x13]
    // 0xbf29d0: DecompressPointer r0
    //     0xbf29d0: add             x0, x0, HEAP, lsl #32
    // 0xbf29d4: LoadField: r1 = r2->field_b
    //     0xbf29d4: ldur            w1, [x2, #0xb]
    // 0xbf29d8: r5 = LoadInt32Instr(r0)
    //     0xbf29d8: sbfx            x5, x0, #1, #0x1f
    //     0xbf29dc: tbz             w0, #0, #0xbf29e4
    //     0xbf29e0: ldur            x5, [x0, #7]
    // 0xbf29e4: r0 = LoadInt32Instr(r1)
    //     0xbf29e4: sbfx            x0, x1, #1, #0x1f
    // 0xbf29e8: mov             x1, x5
    // 0xbf29ec: cmp             x1, x0
    // 0xbf29f0: b.hs            #0xbf2b04
    // 0xbf29f4: LoadField: r0 = r2->field_f
    //     0xbf29f4: ldur            w0, [x2, #0xf]
    // 0xbf29f8: DecompressPointer r0
    //     0xbf29f8: add             x0, x0, HEAP, lsl #32
    // 0xbf29fc: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbf29fc: add             x16, x0, x5, lsl #2
    //     0xbf2a00: ldur            w1, [x16, #0xf]
    // 0xbf2a04: DecompressPointer r1
    //     0xbf2a04: add             x1, x1, HEAP, lsl #32
    // 0xbf2a08: LoadField: r0 = r1->field_7
    //     0xbf2a08: ldur            w0, [x1, #7]
    // 0xbf2a0c: DecompressPointer r0
    //     0xbf2a0c: add             x0, x0, HEAP, lsl #32
    // 0xbf2a10: cmp             w0, NULL
    // 0xbf2a14: b.ne            #0xbf2a20
    // 0xbf2a18: r0 = Null
    //     0xbf2a18: mov             x0, NULL
    // 0xbf2a1c: b               #0xbf2a2c
    // 0xbf2a20: LoadField: r1 = r0->field_b
    //     0xbf2a20: ldur            w1, [x0, #0xb]
    // 0xbf2a24: DecompressPointer r1
    //     0xbf2a24: add             x1, x1, HEAP, lsl #32
    // 0xbf2a28: mov             x0, x1
    // 0xbf2a2c: cmp             w0, NULL
    // 0xbf2a30: b.ne            #0xbf2a38
    // 0xbf2a34: r0 = ""
    //     0xbf2a34: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf2a38: stur            x0, [fp, #-8]
    // 0xbf2a3c: r1 = Function '<anonymous closure>':.
    //     0xbf2a3c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53598] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbf2a40: ldr             x1, [x1, #0x598]
    // 0xbf2a44: r2 = Null
    //     0xbf2a44: mov             x2, NULL
    // 0xbf2a48: r0 = AllocateClosure()
    //     0xbf2a48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf2a4c: r1 = Function '<anonymous closure>':.
    //     0xbf2a4c: add             x1, PP, #0x53, lsl #12  ; [pp+0x535a0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbf2a50: ldr             x1, [x1, #0x5a0]
    // 0xbf2a54: r2 = Null
    //     0xbf2a54: mov             x2, NULL
    // 0xbf2a58: stur            x0, [fp, #-0x20]
    // 0xbf2a5c: r0 = AllocateClosure()
    //     0xbf2a5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf2a60: stur            x0, [fp, #-0x28]
    // 0xbf2a64: r0 = CachedNetworkImage()
    //     0xbf2a64: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbf2a68: stur            x0, [fp, #-0x30]
    // 0xbf2a6c: ldur            x16, [fp, #-0x18]
    // 0xbf2a70: r30 = 64.000000
    //     0xbf2a70: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xbf2a74: ldr             lr, [lr, #0x838]
    // 0xbf2a78: stp             lr, x16, [SP, #0x18]
    // 0xbf2a7c: r16 = 64.000000
    //     0xbf2a7c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xbf2a80: ldr             x16, [x16, #0x838]
    // 0xbf2a84: ldur            lr, [fp, #-0x20]
    // 0xbf2a88: stp             lr, x16, [SP, #8]
    // 0xbf2a8c: ldur            x16, [fp, #-0x28]
    // 0xbf2a90: str             x16, [SP]
    // 0xbf2a94: mov             x1, x0
    // 0xbf2a98: ldur            x2, [fp, #-8]
    // 0xbf2a9c: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, height, 0x3, httpHeaders, 0x2, progressIndicatorBuilder, 0x5, width, 0x4, null]
    //     0xbf2a9c: add             x4, PP, #0x53, lsl #12  ; [pp+0x53588] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "height", 0x3, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x4, Null]
    //     0xbf2aa0: ldr             x4, [x4, #0x588]
    // 0xbf2aa4: r0 = CachedNetworkImage()
    //     0xbf2aa4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbf2aa8: r0 = GestureDetector()
    //     0xbf2aa8: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xbf2aac: ldur            x2, [fp, #-0x10]
    // 0xbf2ab0: r1 = Function '<anonymous closure>':.
    //     0xbf2ab0: add             x1, PP, #0x53, lsl #12  ; [pp+0x535a8] AnonymousClosure: (0xbf2b08), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRow (0xbf2868)
    //     0xbf2ab4: ldr             x1, [x1, #0x5a8]
    // 0xbf2ab8: stur            x0, [fp, #-8]
    // 0xbf2abc: r0 = AllocateClosure()
    //     0xbf2abc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf2ac0: ldur            x16, [fp, #-0x30]
    // 0xbf2ac4: stp             x16, x0, [SP]
    // 0xbf2ac8: ldur            x1, [fp, #-8]
    // 0xbf2acc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xbf2acc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xbf2ad0: ldr             x4, [x4, #0xaf0]
    // 0xbf2ad4: r0 = GestureDetector()
    //     0xbf2ad4: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xbf2ad8: r0 = Padding()
    //     0xbf2ad8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf2adc: r1 = Instance_EdgeInsets
    //     0xbf2adc: add             x1, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xbf2ae0: ldr             x1, [x1, #0x550]
    // 0xbf2ae4: StoreField: r0->field_f = r1
    //     0xbf2ae4: stur            w1, [x0, #0xf]
    // 0xbf2ae8: ldur            x1, [fp, #-8]
    // 0xbf2aec: StoreField: r0->field_b = r1
    //     0xbf2aec: stur            w1, [x0, #0xb]
    // 0xbf2af0: LeaveFrame
    //     0xbf2af0: mov             SP, fp
    //     0xbf2af4: ldp             fp, lr, [SP], #0x10
    // 0xbf2af8: ret
    //     0xbf2af8: ret             
    // 0xbf2afc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf2afc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf2b00: b               #0xbf296c
    // 0xbf2b04: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf2b04: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbf2b08, size: 0x88
    // 0xbf2b08: EnterFrame
    //     0xbf2b08: stp             fp, lr, [SP, #-0x10]!
    //     0xbf2b0c: mov             fp, SP
    // 0xbf2b10: ldr             x0, [fp, #0x10]
    // 0xbf2b14: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf2b14: ldur            w1, [x0, #0x17]
    // 0xbf2b18: DecompressPointer r1
    //     0xbf2b18: add             x1, x1, HEAP, lsl #32
    // 0xbf2b1c: CheckStackOverflow
    //     0xbf2b1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf2b20: cmp             SP, x16
    //     0xbf2b24: b.ls            #0xbf2b88
    // 0xbf2b28: LoadField: r0 = r1->field_b
    //     0xbf2b28: ldur            w0, [x1, #0xb]
    // 0xbf2b2c: DecompressPointer r0
    //     0xbf2b2c: add             x0, x0, HEAP, lsl #32
    // 0xbf2b30: LoadField: r2 = r0->field_f
    //     0xbf2b30: ldur            w2, [x0, #0xf]
    // 0xbf2b34: DecompressPointer r2
    //     0xbf2b34: add             x2, x2, HEAP, lsl #32
    // 0xbf2b38: LoadField: r3 = r0->field_13
    //     0xbf2b38: ldur            w3, [x0, #0x13]
    // 0xbf2b3c: DecompressPointer r3
    //     0xbf2b3c: add             x3, x3, HEAP, lsl #32
    // 0xbf2b40: LoadField: r0 = r1->field_13
    //     0xbf2b40: ldur            w0, [x1, #0x13]
    // 0xbf2b44: DecompressPointer r0
    //     0xbf2b44: add             x0, x0, HEAP, lsl #32
    // 0xbf2b48: LoadField: r5 = r1->field_f
    //     0xbf2b48: ldur            w5, [x1, #0xf]
    // 0xbf2b4c: DecompressPointer r5
    //     0xbf2b4c: add             x5, x5, HEAP, lsl #32
    // 0xbf2b50: r1 = LoadInt32Instr(r0)
    //     0xbf2b50: sbfx            x1, x0, #1, #0x1f
    //     0xbf2b54: tbz             w0, #0, #0xbf2b5c
    //     0xbf2b58: ldur            x1, [x0, #7]
    // 0xbf2b5c: mov             x16, x3
    // 0xbf2b60: mov             x3, x2
    // 0xbf2b64: mov             x2, x16
    // 0xbf2b68: mov             x16, x1
    // 0xbf2b6c: mov             x1, x3
    // 0xbf2b70: mov             x3, x16
    // 0xbf2b74: r0 = _openImageViewer()
    //     0xbf2b74: bl              #0xbf2670  ; [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xbf2b78: r0 = Null
    //     0xbf2b78: mov             x0, NULL
    // 0xbf2b7c: LeaveFrame
    //     0xbf2b7c: mov             SP, fp
    //     0xbf2b80: ldp             fp, lr, [SP], #0x10
    // 0xbf2b84: ret
    //     0xbf2b84: ret             
    // 0xbf2b88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf2b88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf2b8c: b               #0xbf2b28
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbf2b90, size: 0x8c
    // 0xbf2b90: EnterFrame
    //     0xbf2b90: stp             fp, lr, [SP, #-0x10]!
    //     0xbf2b94: mov             fp, SP
    // 0xbf2b98: AllocStack(0x10)
    //     0xbf2b98: sub             SP, SP, #0x10
    // 0xbf2b9c: SetupParameters()
    //     0xbf2b9c: ldr             x0, [fp, #0x10]
    //     0xbf2ba0: ldur            w2, [x0, #0x17]
    //     0xbf2ba4: add             x2, x2, HEAP, lsl #32
    //     0xbf2ba8: stur            x2, [fp, #-0x10]
    // 0xbf2bac: CheckStackOverflow
    //     0xbf2bac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf2bb0: cmp             SP, x16
    //     0xbf2bb4: b.ls            #0xbf2c14
    // 0xbf2bb8: LoadField: r0 = r2->field_13
    //     0xbf2bb8: ldur            w0, [x2, #0x13]
    // 0xbf2bbc: DecompressPointer r0
    //     0xbf2bbc: add             x0, x0, HEAP, lsl #32
    // 0xbf2bc0: mov             x1, x0
    // 0xbf2bc4: stur            x0, [fp, #-8]
    // 0xbf2bc8: r0 = value()
    //     0xbf2bc8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf2bcc: eor             x2, x0, #0x10
    // 0xbf2bd0: ldur            x1, [fp, #-8]
    // 0xbf2bd4: r0 = value=()
    //     0xbf2bd4: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xbf2bd8: ldur            x0, [fp, #-0x10]
    // 0xbf2bdc: LoadField: r3 = r0->field_f
    //     0xbf2bdc: ldur            w3, [x0, #0xf]
    // 0xbf2be0: DecompressPointer r3
    //     0xbf2be0: add             x3, x3, HEAP, lsl #32
    // 0xbf2be4: stur            x3, [fp, #-8]
    // 0xbf2be8: r1 = Function '<anonymous closure>':.
    //     0xbf2be8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53548] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xbf2bec: ldr             x1, [x1, #0x548]
    // 0xbf2bf0: r2 = Null
    //     0xbf2bf0: mov             x2, NULL
    // 0xbf2bf4: r0 = AllocateClosure()
    //     0xbf2bf4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf2bf8: ldur            x1, [fp, #-8]
    // 0xbf2bfc: mov             x2, x0
    // 0xbf2c00: r0 = setState()
    //     0xbf2c00: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbf2c04: r0 = Null
    //     0xbf2c04: mov             x0, NULL
    // 0xbf2c08: LeaveFrame
    //     0xbf2c08: mov             SP, fp
    //     0xbf2c0c: ldp             fp, lr, [SP], #0x10
    // 0xbf2c10: ret
    //     0xbf2c10: ret             
    // 0xbf2c14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf2c14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf2c18: b               #0xbf2bb8
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xbf2c1c, size: 0x84
    // 0xbf2c1c: EnterFrame
    //     0xbf2c1c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf2c20: mov             fp, SP
    // 0xbf2c24: AllocStack(0x10)
    //     0xbf2c24: sub             SP, SP, #0x10
    // 0xbf2c28: SetupParameters()
    //     0xbf2c28: ldr             x0, [fp, #0x18]
    //     0xbf2c2c: ldur            w1, [x0, #0x17]
    //     0xbf2c30: add             x1, x1, HEAP, lsl #32
    //     0xbf2c34: stur            x1, [fp, #-8]
    // 0xbf2c38: CheckStackOverflow
    //     0xbf2c38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf2c3c: cmp             SP, x16
    //     0xbf2c40: b.ls            #0xbf2c98
    // 0xbf2c44: r1 = 1
    //     0xbf2c44: movz            x1, #0x1
    // 0xbf2c48: r0 = AllocateContext()
    //     0xbf2c48: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf2c4c: mov             x1, x0
    // 0xbf2c50: ldur            x0, [fp, #-8]
    // 0xbf2c54: StoreField: r1->field_b = r0
    //     0xbf2c54: stur            w0, [x1, #0xb]
    // 0xbf2c58: ldr             x2, [fp, #0x10]
    // 0xbf2c5c: StoreField: r1->field_f = r2
    //     0xbf2c5c: stur            w2, [x1, #0xf]
    // 0xbf2c60: LoadField: r3 = r0->field_f
    //     0xbf2c60: ldur            w3, [x0, #0xf]
    // 0xbf2c64: DecompressPointer r3
    //     0xbf2c64: add             x3, x3, HEAP, lsl #32
    // 0xbf2c68: mov             x2, x1
    // 0xbf2c6c: stur            x3, [fp, #-0x10]
    // 0xbf2c70: r1 = Function '<anonymous closure>':.
    //     0xbf2c70: add             x1, PP, #0x53, lsl #12  ; [pp+0x535b0] AnonymousClosure: (0xbf2ca0), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xbf0494)
    //     0xbf2c74: ldr             x1, [x1, #0x5b0]
    // 0xbf2c78: r0 = AllocateClosure()
    //     0xbf2c78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf2c7c: ldur            x1, [fp, #-0x10]
    // 0xbf2c80: mov             x2, x0
    // 0xbf2c84: r0 = setState()
    //     0xbf2c84: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbf2c88: r0 = Null
    //     0xbf2c88: mov             x0, NULL
    // 0xbf2c8c: LeaveFrame
    //     0xbf2c8c: mov             SP, fp
    //     0xbf2c90: ldp             fp, lr, [SP], #0x10
    // 0xbf2c94: ret
    //     0xbf2c94: ret             
    // 0xbf2c98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf2c98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf2c9c: b               #0xbf2c44
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbf2ca0, size: 0x8c
    // 0xbf2ca0: EnterFrame
    //     0xbf2ca0: stp             fp, lr, [SP, #-0x10]!
    //     0xbf2ca4: mov             fp, SP
    // 0xbf2ca8: AllocStack(0x8)
    //     0xbf2ca8: sub             SP, SP, #8
    // 0xbf2cac: SetupParameters()
    //     0xbf2cac: ldr             x0, [fp, #0x10]
    //     0xbf2cb0: ldur            w1, [x0, #0x17]
    //     0xbf2cb4: add             x1, x1, HEAP, lsl #32
    // 0xbf2cb8: CheckStackOverflow
    //     0xbf2cb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf2cbc: cmp             SP, x16
    //     0xbf2cc0: b.ls            #0xbf2d24
    // 0xbf2cc4: LoadField: r0 = r1->field_b
    //     0xbf2cc4: ldur            w0, [x1, #0xb]
    // 0xbf2cc8: DecompressPointer r0
    //     0xbf2cc8: add             x0, x0, HEAP, lsl #32
    // 0xbf2ccc: LoadField: r2 = r0->field_f
    //     0xbf2ccc: ldur            w2, [x0, #0xf]
    // 0xbf2cd0: DecompressPointer r2
    //     0xbf2cd0: add             x2, x2, HEAP, lsl #32
    // 0xbf2cd4: LoadField: r0 = r1->field_f
    //     0xbf2cd4: ldur            w0, [x1, #0xf]
    // 0xbf2cd8: DecompressPointer r0
    //     0xbf2cd8: add             x0, x0, HEAP, lsl #32
    // 0xbf2cdc: r1 = LoadInt32Instr(r0)
    //     0xbf2cdc: sbfx            x1, x0, #1, #0x1f
    //     0xbf2ce0: tbz             w0, #0, #0xbf2ce8
    //     0xbf2ce4: ldur            x1, [x0, #7]
    // 0xbf2ce8: ArrayStore: r2[0] = r1  ; List_8
    //     0xbf2ce8: stur            x1, [x2, #0x17]
    // 0xbf2cec: LoadField: r0 = r2->field_1f
    //     0xbf2cec: ldur            w0, [x2, #0x1f]
    // 0xbf2cf0: DecompressPointer r0
    //     0xbf2cf0: add             x0, x0, HEAP, lsl #32
    // 0xbf2cf4: stur            x0, [fp, #-8]
    // 0xbf2cf8: r1 = Function '<anonymous closure>':.
    //     0xbf2cf8: add             x1, PP, #0x53, lsl #12  ; [pp+0x535b8] AnonymousClosure: (0xa5cd5c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xc06824)
    //     0xbf2cfc: ldr             x1, [x1, #0x5b8]
    // 0xbf2d00: r2 = Null
    //     0xbf2d00: mov             x2, NULL
    // 0xbf2d04: r0 = AllocateClosure()
    //     0xbf2d04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf2d08: ldur            x1, [fp, #-8]
    // 0xbf2d0c: mov             x2, x0
    // 0xbf2d10: r0 = forEach()
    //     0xbf2d10: bl              #0x16878f8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xbf2d14: r0 = Null
    //     0xbf2d14: mov             x0, NULL
    // 0xbf2d18: LeaveFrame
    //     0xbf2d18: mov             SP, fp
    //     0xbf2d1c: ldp             fp, lr, [SP], #0x10
    // 0xbf2d20: ret
    //     0xbf2d20: ret             
    // 0xbf2d24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf2d24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf2d28: b               #0xbf2cc4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbf2d2c, size: 0xb8
    // 0xbf2d2c: EnterFrame
    //     0xbf2d2c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf2d30: mov             fp, SP
    // 0xbf2d34: AllocStack(0x38)
    //     0xbf2d34: sub             SP, SP, #0x38
    // 0xbf2d38: SetupParameters()
    //     0xbf2d38: ldr             x0, [fp, #0x10]
    //     0xbf2d3c: ldur            w1, [x0, #0x17]
    //     0xbf2d40: add             x1, x1, HEAP, lsl #32
    // 0xbf2d44: CheckStackOverflow
    //     0xbf2d44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf2d48: cmp             SP, x16
    //     0xbf2d4c: b.ls            #0xbf2dd8
    // 0xbf2d50: LoadField: r0 = r1->field_f
    //     0xbf2d50: ldur            w0, [x1, #0xf]
    // 0xbf2d54: DecompressPointer r0
    //     0xbf2d54: add             x0, x0, HEAP, lsl #32
    // 0xbf2d58: LoadField: r1 = r0->field_b
    //     0xbf2d58: ldur            w1, [x0, #0xb]
    // 0xbf2d5c: DecompressPointer r1
    //     0xbf2d5c: add             x1, x1, HEAP, lsl #32
    // 0xbf2d60: cmp             w1, NULL
    // 0xbf2d64: b.eq            #0xbf2de0
    // 0xbf2d68: LoadField: r0 = r1->field_1b
    //     0xbf2d68: ldur            w0, [x1, #0x1b]
    // 0xbf2d6c: DecompressPointer r0
    //     0xbf2d6c: add             x0, x0, HEAP, lsl #32
    // 0xbf2d70: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbf2d70: ldur            w2, [x1, #0x17]
    // 0xbf2d74: DecompressPointer r2
    //     0xbf2d74: add             x2, x2, HEAP, lsl #32
    // 0xbf2d78: LoadField: r3 = r1->field_f
    //     0xbf2d78: ldur            w3, [x1, #0xf]
    // 0xbf2d7c: DecompressPointer r3
    //     0xbf2d7c: add             x3, x3, HEAP, lsl #32
    // 0xbf2d80: LoadField: r4 = r1->field_23
    //     0xbf2d80: ldur            w4, [x1, #0x23]
    // 0xbf2d84: DecompressPointer r4
    //     0xbf2d84: add             x4, x4, HEAP, lsl #32
    // 0xbf2d88: LoadField: r5 = r1->field_1f
    //     0xbf2d88: ldur            w5, [x1, #0x1f]
    // 0xbf2d8c: DecompressPointer r5
    //     0xbf2d8c: add             x5, x5, HEAP, lsl #32
    // 0xbf2d90: LoadField: r6 = r1->field_2b
    //     0xbf2d90: ldur            w6, [x1, #0x2b]
    // 0xbf2d94: DecompressPointer r6
    //     0xbf2d94: add             x6, x6, HEAP, lsl #32
    // 0xbf2d98: LoadField: r7 = r1->field_27
    //     0xbf2d98: ldur            w7, [x1, #0x27]
    // 0xbf2d9c: DecompressPointer r7
    //     0xbf2d9c: add             x7, x7, HEAP, lsl #32
    // 0xbf2da0: stp             x0, x7, [SP, #0x28]
    // 0xbf2da4: stp             x3, x2, [SP, #0x18]
    // 0xbf2da8: stp             x5, x4, [SP, #8]
    // 0xbf2dac: str             x6, [SP]
    // 0xbf2db0: r4 = 0
    //     0xbf2db0: movz            x4, #0
    // 0xbf2db4: ldr             x0, [SP, #0x30]
    // 0xbf2db8: r16 = UnlinkedCall_0x613b5c
    //     0xbf2db8: add             x16, PP, #0x53, lsl #12  ; [pp+0x535c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbf2dbc: add             x16, x16, #0x5c0
    // 0xbf2dc0: ldp             x5, lr, [x16]
    // 0xbf2dc4: blr             lr
    // 0xbf2dc8: r0 = Null
    //     0xbf2dc8: mov             x0, NULL
    // 0xbf2dcc: LeaveFrame
    //     0xbf2dcc: mov             SP, fp
    //     0xbf2dd0: ldp             fp, lr, [SP], #0x10
    // 0xbf2dd4: ret
    //     0xbf2dd4: ret             
    // 0xbf2dd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf2dd8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf2ddc: b               #0xbf2d50
    // 0xbf2de0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf2de0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3988, size: 0x34, field offset: 0xc
//   const constructor, 
class TestimonialCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80bf4, size: 0x84
    // 0xc80bf4: EnterFrame
    //     0xc80bf4: stp             fp, lr, [SP, #-0x10]!
    //     0xc80bf8: mov             fp, SP
    // 0xc80bfc: AllocStack(0x18)
    //     0xc80bfc: sub             SP, SP, #0x18
    // 0xc80c00: CheckStackOverflow
    //     0xc80c00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc80c04: cmp             SP, x16
    //     0xc80c08: b.ls            #0xc80c70
    // 0xc80c0c: r1 = <TestimonialCarousel>
    //     0xc80c0c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48400] TypeArguments: <TestimonialCarousel>
    //     0xc80c10: ldr             x1, [x1, #0x400]
    // 0xc80c14: r0 = _TestimonialCarouselState()
    //     0xc80c14: bl              #0xc80c78  ; Allocate_TestimonialCarouselStateStub -> _TestimonialCarouselState (size=0x24)
    // 0xc80c18: mov             x1, x0
    // 0xc80c1c: r0 = Sentinel
    //     0xc80c1c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc80c20: stur            x1, [fp, #-8]
    // 0xc80c24: StoreField: r1->field_13 = r0
    //     0xc80c24: stur            w0, [x1, #0x13]
    // 0xc80c28: ArrayStore: r1[0] = rZR  ; List_8
    //     0xc80c28: stur            xzr, [x1, #0x17]
    // 0xc80c2c: r16 = <int, RxBool>
    //     0xc80c2c: add             x16, PP, #0x48, lsl #12  ; [pp+0x48298] TypeArguments: <int, RxBool>
    //     0xc80c30: ldr             x16, [x16, #0x298]
    // 0xc80c34: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc80c38: stp             lr, x16, [SP]
    // 0xc80c3c: r0 = Map._fromLiteral()
    //     0xc80c3c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xc80c40: ldur            x1, [fp, #-8]
    // 0xc80c44: StoreField: r1->field_1f = r0
    //     0xc80c44: stur            w0, [x1, #0x1f]
    //     0xc80c48: ldurb           w16, [x1, #-1]
    //     0xc80c4c: ldurb           w17, [x0, #-1]
    //     0xc80c50: and             x16, x17, x16, lsr #2
    //     0xc80c54: tst             x16, HEAP, lsr #32
    //     0xc80c58: b.eq            #0xc80c60
    //     0xc80c5c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc80c60: mov             x0, x1
    // 0xc80c64: LeaveFrame
    //     0xc80c64: mov             SP, fp
    //     0xc80c68: ldp             fp, lr, [SP], #0x10
    // 0xc80c6c: ret
    //     0xc80c6c: ret             
    // 0xc80c70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc80c70: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc80c74: b               #0xc80c0c
  }
}
