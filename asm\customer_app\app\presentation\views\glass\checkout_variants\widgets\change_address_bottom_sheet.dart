// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart

// class id: 1049362, size: 0x8
class :: {
}

// class id: 3370, size: 0x68, field offset: 0x14
class _ChangeAddressBottomSheetState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x93e238, size: 0x300
    // 0x93e238: EnterFrame
    //     0x93e238: stp             fp, lr, [SP, #-0x10]!
    //     0x93e23c: mov             fp, SP
    // 0x93e240: AllocStack(0x28)
    //     0x93e240: sub             SP, SP, #0x28
    // 0x93e244: SetupParameters(_ChangeAddressBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0x93e244: stur            x1, [fp, #-8]
    // 0x93e248: CheckStackOverflow
    //     0x93e248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93e24c: cmp             SP, x16
    //     0x93e250: b.ls            #0x93e520
    // 0x93e254: r1 = 1
    //     0x93e254: movz            x1, #0x1
    // 0x93e258: r0 = AllocateContext()
    //     0x93e258: bl              #0x16f6108  ; AllocateContextStub
    // 0x93e25c: mov             x4, x0
    // 0x93e260: ldur            x3, [fp, #-8]
    // 0x93e264: stur            x4, [fp, #-0x10]
    // 0x93e268: StoreField: r4->field_f = r3
    //     0x93e268: stur            w3, [x4, #0xf]
    // 0x93e26c: LoadField: r0 = r3->field_b
    //     0x93e26c: ldur            w0, [x3, #0xb]
    // 0x93e270: DecompressPointer r0
    //     0x93e270: add             x0, x0, HEAP, lsl #32
    // 0x93e274: cmp             w0, NULL
    // 0x93e278: b.eq            #0x93e528
    // 0x93e27c: LoadField: r1 = r0->field_1f
    //     0x93e27c: ldur            w1, [x0, #0x1f]
    // 0x93e280: DecompressPointer r1
    //     0x93e280: add             x1, x1, HEAP, lsl #32
    // 0x93e284: LoadField: r2 = r1->field_1b
    //     0x93e284: ldur            w2, [x1, #0x1b]
    // 0x93e288: DecompressPointer r2
    //     0x93e288: add             x2, x2, HEAP, lsl #32
    // 0x93e28c: cmp             w2, NULL
    // 0x93e290: b.ne            #0x93e29c
    // 0x93e294: r0 = Null
    //     0x93e294: mov             x0, NULL
    // 0x93e298: b               #0x93e2b4
    // 0x93e29c: LoadField: r0 = r2->field_b
    //     0x93e29c: ldur            w0, [x2, #0xb]
    // 0x93e2a0: cbnz            w0, #0x93e2ac
    // 0x93e2a4: r1 = false
    //     0x93e2a4: add             x1, NULL, #0x30  ; false
    // 0x93e2a8: b               #0x93e2b0
    // 0x93e2ac: r1 = true
    //     0x93e2ac: add             x1, NULL, #0x20  ; true
    // 0x93e2b0: mov             x0, x1
    // 0x93e2b4: cmp             w0, NULL
    // 0x93e2b8: b.eq            #0x93e40c
    // 0x93e2bc: tbnz            w0, #4, #0x93e40c
    // 0x93e2c0: cmp             w2, NULL
    // 0x93e2c4: b.ne            #0x93e2d0
    // 0x93e2c8: r2 = Null
    //     0x93e2c8: mov             x2, NULL
    // 0x93e2cc: b               #0x93e2fc
    // 0x93e2d0: LoadField: r0 = r2->field_b
    //     0x93e2d0: ldur            w0, [x2, #0xb]
    // 0x93e2d4: r1 = LoadInt32Instr(r0)
    //     0x93e2d4: sbfx            x1, x0, #1, #0x1f
    // 0x93e2d8: mov             x0, x1
    // 0x93e2dc: r1 = 0
    //     0x93e2dc: movz            x1, #0
    // 0x93e2e0: cmp             x1, x0
    // 0x93e2e4: b.hs            #0x93e52c
    // 0x93e2e8: LoadField: r0 = r2->field_f
    //     0x93e2e8: ldur            w0, [x2, #0xf]
    // 0x93e2ec: DecompressPointer r0
    //     0x93e2ec: add             x0, x0, HEAP, lsl #32
    // 0x93e2f0: LoadField: r1 = r0->field_f
    //     0x93e2f0: ldur            w1, [x0, #0xf]
    // 0x93e2f4: DecompressPointer r1
    //     0x93e2f4: add             x1, x1, HEAP, lsl #32
    // 0x93e2f8: mov             x2, x1
    // 0x93e2fc: mov             x1, x3
    // 0x93e300: r0 = setUpAddress()
    //     0x93e300: bl              #0x93e55c  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::setUpAddress
    // 0x93e304: r0 = LoadStaticField(0x878)
    //     0x93e304: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93e308: ldr             x0, [x0, #0x10f0]
    // 0x93e30c: cmp             w0, NULL
    // 0x93e310: b.eq            #0x93e530
    // 0x93e314: LoadField: r3 = r0->field_53
    //     0x93e314: ldur            w3, [x0, #0x53]
    // 0x93e318: DecompressPointer r3
    //     0x93e318: add             x3, x3, HEAP, lsl #32
    // 0x93e31c: stur            x3, [fp, #-0x20]
    // 0x93e320: LoadField: r0 = r3->field_7
    //     0x93e320: ldur            w0, [x3, #7]
    // 0x93e324: DecompressPointer r0
    //     0x93e324: add             x0, x0, HEAP, lsl #32
    // 0x93e328: ldur            x2, [fp, #-0x10]
    // 0x93e32c: stur            x0, [fp, #-0x18]
    // 0x93e330: r1 = Function '<anonymous closure>':.
    //     0x93e330: add             x1, PP, #0x56, lsl #12  ; [pp+0x56fe8] AnonymousClosure: (0x93f0b4), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::initState (0x93e238)
    //     0x93e334: ldr             x1, [x1, #0xfe8]
    // 0x93e338: r0 = AllocateClosure()
    //     0x93e338: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93e33c: ldur            x2, [fp, #-0x18]
    // 0x93e340: mov             x3, x0
    // 0x93e344: r1 = Null
    //     0x93e344: mov             x1, NULL
    // 0x93e348: stur            x3, [fp, #-0x18]
    // 0x93e34c: cmp             w2, NULL
    // 0x93e350: b.eq            #0x93e370
    // 0x93e354: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93e354: ldur            w4, [x2, #0x17]
    // 0x93e358: DecompressPointer r4
    //     0x93e358: add             x4, x4, HEAP, lsl #32
    // 0x93e35c: r8 = X0
    //     0x93e35c: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x93e360: LoadField: r9 = r4->field_7
    //     0x93e360: ldur            x9, [x4, #7]
    // 0x93e364: r3 = Null
    //     0x93e364: add             x3, PP, #0x56, lsl #12  ; [pp+0x56ff0] Null
    //     0x93e368: ldr             x3, [x3, #0xff0]
    // 0x93e36c: blr             x9
    // 0x93e370: ldur            x0, [fp, #-0x20]
    // 0x93e374: LoadField: r1 = r0->field_b
    //     0x93e374: ldur            w1, [x0, #0xb]
    // 0x93e378: LoadField: r2 = r0->field_f
    //     0x93e378: ldur            w2, [x0, #0xf]
    // 0x93e37c: DecompressPointer r2
    //     0x93e37c: add             x2, x2, HEAP, lsl #32
    // 0x93e380: LoadField: r3 = r2->field_b
    //     0x93e380: ldur            w3, [x2, #0xb]
    // 0x93e384: r2 = LoadInt32Instr(r1)
    //     0x93e384: sbfx            x2, x1, #1, #0x1f
    // 0x93e388: stur            x2, [fp, #-0x28]
    // 0x93e38c: r1 = LoadInt32Instr(r3)
    //     0x93e38c: sbfx            x1, x3, #1, #0x1f
    // 0x93e390: cmp             x2, x1
    // 0x93e394: b.ne            #0x93e3a0
    // 0x93e398: mov             x1, x0
    // 0x93e39c: r0 = _growToNextCapacity()
    //     0x93e39c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93e3a0: ldur            x3, [fp, #-8]
    // 0x93e3a4: ldur            x0, [fp, #-0x20]
    // 0x93e3a8: ldur            x2, [fp, #-0x28]
    // 0x93e3ac: r4 = true
    //     0x93e3ac: add             x4, NULL, #0x20  ; true
    // 0x93e3b0: add             x1, x2, #1
    // 0x93e3b4: lsl             x5, x1, #1
    // 0x93e3b8: StoreField: r0->field_b = r5
    //     0x93e3b8: stur            w5, [x0, #0xb]
    // 0x93e3bc: LoadField: r1 = r0->field_f
    //     0x93e3bc: ldur            w1, [x0, #0xf]
    // 0x93e3c0: DecompressPointer r1
    //     0x93e3c0: add             x1, x1, HEAP, lsl #32
    // 0x93e3c4: ldur            x0, [fp, #-0x18]
    // 0x93e3c8: ArrayStore: r1[r2] = r0  ; List_4
    //     0x93e3c8: add             x25, x1, x2, lsl #2
    //     0x93e3cc: add             x25, x25, #0xf
    //     0x93e3d0: str             w0, [x25]
    //     0x93e3d4: tbz             w0, #0, #0x93e3f0
    //     0x93e3d8: ldurb           w16, [x1, #-1]
    //     0x93e3dc: ldurb           w17, [x0, #-1]
    //     0x93e3e0: and             x16, x17, x16, lsr #2
    //     0x93e3e4: tst             x16, HEAP, lsr #32
    //     0x93e3e8: b.eq            #0x93e3f0
    //     0x93e3ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93e3f0: StoreField: r3->field_4f = r4
    //     0x93e3f0: stur            w4, [x3, #0x4f]
    // 0x93e3f4: StoreField: r3->field_53 = r4
    //     0x93e3f4: stur            w4, [x3, #0x53]
    // 0x93e3f8: StoreField: r3->field_57 = r4
    //     0x93e3f8: stur            w4, [x3, #0x57]
    // 0x93e3fc: StoreField: r3->field_5b = r4
    //     0x93e3fc: stur            w4, [x3, #0x5b]
    // 0x93e400: StoreField: r3->field_5f = r4
    //     0x93e400: stur            w4, [x3, #0x5f]
    // 0x93e404: StoreField: r3->field_63 = r4
    //     0x93e404: stur            w4, [x3, #0x63]
    // 0x93e408: b               #0x93e510
    // 0x93e40c: r0 = LoadStaticField(0x878)
    //     0x93e40c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93e410: ldr             x0, [x0, #0x10f0]
    // 0x93e414: cmp             w0, NULL
    // 0x93e418: b.eq            #0x93e534
    // 0x93e41c: LoadField: r4 = r0->field_53
    //     0x93e41c: ldur            w4, [x0, #0x53]
    // 0x93e420: DecompressPointer r4
    //     0x93e420: add             x4, x4, HEAP, lsl #32
    // 0x93e424: stur            x4, [fp, #-0x20]
    // 0x93e428: LoadField: r0 = r4->field_7
    //     0x93e428: ldur            w0, [x4, #7]
    // 0x93e42c: DecompressPointer r0
    //     0x93e42c: add             x0, x0, HEAP, lsl #32
    // 0x93e430: ldur            x2, [fp, #-0x10]
    // 0x93e434: stur            x0, [fp, #-0x18]
    // 0x93e438: r1 = Function '<anonymous closure>':.
    //     0x93e438: add             x1, PP, #0x57, lsl #12  ; [pp+0x57000] AnonymousClosure: (0x93f01c), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::initState (0x93e238)
    //     0x93e43c: ldr             x1, [x1]
    // 0x93e440: r0 = AllocateClosure()
    //     0x93e440: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93e444: ldur            x2, [fp, #-0x18]
    // 0x93e448: mov             x3, x0
    // 0x93e44c: r1 = Null
    //     0x93e44c: mov             x1, NULL
    // 0x93e450: stur            x3, [fp, #-0x10]
    // 0x93e454: cmp             w2, NULL
    // 0x93e458: b.eq            #0x93e478
    // 0x93e45c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93e45c: ldur            w4, [x2, #0x17]
    // 0x93e460: DecompressPointer r4
    //     0x93e460: add             x4, x4, HEAP, lsl #32
    // 0x93e464: r8 = X0
    //     0x93e464: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x93e468: LoadField: r9 = r4->field_7
    //     0x93e468: ldur            x9, [x4, #7]
    // 0x93e46c: r3 = Null
    //     0x93e46c: add             x3, PP, #0x57, lsl #12  ; [pp+0x57008] Null
    //     0x93e470: ldr             x3, [x3, #8]
    // 0x93e474: blr             x9
    // 0x93e478: ldur            x0, [fp, #-0x20]
    // 0x93e47c: LoadField: r1 = r0->field_b
    //     0x93e47c: ldur            w1, [x0, #0xb]
    // 0x93e480: LoadField: r2 = r0->field_f
    //     0x93e480: ldur            w2, [x0, #0xf]
    // 0x93e484: DecompressPointer r2
    //     0x93e484: add             x2, x2, HEAP, lsl #32
    // 0x93e488: LoadField: r3 = r2->field_b
    //     0x93e488: ldur            w3, [x2, #0xb]
    // 0x93e48c: r2 = LoadInt32Instr(r1)
    //     0x93e48c: sbfx            x2, x1, #1, #0x1f
    // 0x93e490: stur            x2, [fp, #-0x28]
    // 0x93e494: r1 = LoadInt32Instr(r3)
    //     0x93e494: sbfx            x1, x3, #1, #0x1f
    // 0x93e498: cmp             x2, x1
    // 0x93e49c: b.ne            #0x93e4a8
    // 0x93e4a0: mov             x1, x0
    // 0x93e4a4: r0 = _growToNextCapacity()
    //     0x93e4a4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93e4a8: ldur            x4, [fp, #-8]
    // 0x93e4ac: ldur            x2, [fp, #-0x20]
    // 0x93e4b0: ldur            x3, [fp, #-0x28]
    // 0x93e4b4: r5 = false
    //     0x93e4b4: add             x5, NULL, #0x30  ; false
    // 0x93e4b8: add             x6, x3, #1
    // 0x93e4bc: lsl             x7, x6, #1
    // 0x93e4c0: StoreField: r2->field_b = r7
    //     0x93e4c0: stur            w7, [x2, #0xb]
    // 0x93e4c4: LoadField: r1 = r2->field_f
    //     0x93e4c4: ldur            w1, [x2, #0xf]
    // 0x93e4c8: DecompressPointer r1
    //     0x93e4c8: add             x1, x1, HEAP, lsl #32
    // 0x93e4cc: ldur            x0, [fp, #-0x10]
    // 0x93e4d0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x93e4d0: add             x25, x1, x3, lsl #2
    //     0x93e4d4: add             x25, x25, #0xf
    //     0x93e4d8: str             w0, [x25]
    //     0x93e4dc: tbz             w0, #0, #0x93e4f8
    //     0x93e4e0: ldurb           w16, [x1, #-1]
    //     0x93e4e4: ldurb           w17, [x0, #-1]
    //     0x93e4e8: and             x16, x17, x16, lsr #2
    //     0x93e4ec: tst             x16, HEAP, lsr #32
    //     0x93e4f0: b.eq            #0x93e4f8
    //     0x93e4f4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93e4f8: StoreField: r4->field_4f = r5
    //     0x93e4f8: stur            w5, [x4, #0x4f]
    // 0x93e4fc: StoreField: r4->field_53 = r5
    //     0x93e4fc: stur            w5, [x4, #0x53]
    // 0x93e500: StoreField: r4->field_57 = r5
    //     0x93e500: stur            w5, [x4, #0x57]
    // 0x93e504: StoreField: r4->field_5b = r5
    //     0x93e504: stur            w5, [x4, #0x5b]
    // 0x93e508: StoreField: r4->field_5f = r5
    //     0x93e508: stur            w5, [x4, #0x5f]
    // 0x93e50c: StoreField: r4->field_63 = r5
    //     0x93e50c: stur            w5, [x4, #0x63]
    // 0x93e510: r0 = Null
    //     0x93e510: mov             x0, NULL
    // 0x93e514: LeaveFrame
    //     0x93e514: mov             SP, fp
    //     0x93e518: ldp             fp, lr, [SP], #0x10
    // 0x93e51c: ret
    //     0x93e51c: ret             
    // 0x93e520: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93e520: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93e524: b               #0x93e254
    // 0x93e528: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93e528: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93e52c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93e52c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93e530: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93e530: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93e534: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93e534: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ setUpAddress(/* No info */) {
    // ** addr: 0x93e55c, size: 0x784
    // 0x93e55c: EnterFrame
    //     0x93e55c: stp             fp, lr, [SP, #-0x10]!
    //     0x93e560: mov             fp, SP
    // 0x93e564: AllocStack(0x48)
    //     0x93e564: sub             SP, SP, #0x48
    // 0x93e568: SetupParameters(_ChangeAddressBottomSheetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x93e568: stur            x1, [fp, #-8]
    //     0x93e56c: stur            x2, [fp, #-0x10]
    // 0x93e570: CheckStackOverflow
    //     0x93e570: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93e574: cmp             SP, x16
    //     0x93e578: b.ls            #0x93ecc8
    // 0x93e57c: r1 = 1
    //     0x93e57c: movz            x1, #0x1
    // 0x93e580: r0 = AllocateContext()
    //     0x93e580: bl              #0x16f6108  ; AllocateContextStub
    // 0x93e584: mov             x1, x0
    // 0x93e588: ldur            x0, [fp, #-8]
    // 0x93e58c: stur            x1, [fp, #-0x30]
    // 0x93e590: StoreField: r1->field_f = r0
    //     0x93e590: stur            w0, [x1, #0xf]
    // 0x93e594: LoadField: r2 = r0->field_3f
    //     0x93e594: ldur            w2, [x0, #0x3f]
    // 0x93e598: DecompressPointer r2
    //     0x93e598: add             x2, x2, HEAP, lsl #32
    // 0x93e59c: ldur            x3, [fp, #-0x10]
    // 0x93e5a0: stur            x2, [fp, #-0x28]
    // 0x93e5a4: cmp             w3, NULL
    // 0x93e5a8: b.ne            #0x93e5b4
    // 0x93e5ac: r4 = Null
    //     0x93e5ac: mov             x4, NULL
    // 0x93e5b0: b               #0x93e5bc
    // 0x93e5b4: LoadField: r4 = r3->field_1b
    //     0x93e5b4: ldur            w4, [x3, #0x1b]
    // 0x93e5b8: DecompressPointer r4
    //     0x93e5b8: add             x4, x4, HEAP, lsl #32
    // 0x93e5bc: cmp             w4, NULL
    // 0x93e5c0: b.ne            #0x93e5c8
    // 0x93e5c4: r4 = ""
    //     0x93e5c4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93e5c8: stur            x4, [fp, #-0x20]
    // 0x93e5cc: cmp             w3, NULL
    // 0x93e5d0: b.ne            #0x93e5dc
    // 0x93e5d4: r5 = Null
    //     0x93e5d4: mov             x5, NULL
    // 0x93e5d8: b               #0x93e5fc
    // 0x93e5dc: LoadField: r5 = r3->field_1b
    //     0x93e5dc: ldur            w5, [x3, #0x1b]
    // 0x93e5e0: DecompressPointer r5
    //     0x93e5e0: add             x5, x5, HEAP, lsl #32
    // 0x93e5e4: cmp             w5, NULL
    // 0x93e5e8: b.ne            #0x93e5f4
    // 0x93e5ec: r5 = Null
    //     0x93e5ec: mov             x5, NULL
    // 0x93e5f0: b               #0x93e5fc
    // 0x93e5f4: LoadField: r6 = r5->field_7
    //     0x93e5f4: ldur            w6, [x5, #7]
    // 0x93e5f8: mov             x5, x6
    // 0x93e5fc: cmp             w5, NULL
    // 0x93e600: b.ne            #0x93e60c
    // 0x93e604: r5 = 0
    //     0x93e604: movz            x5, #0
    // 0x93e608: b               #0x93e614
    // 0x93e60c: r6 = LoadInt32Instr(r5)
    //     0x93e60c: sbfx            x6, x5, #1, #0x1f
    // 0x93e610: mov             x5, x6
    // 0x93e614: stur            x5, [fp, #-0x18]
    // 0x93e618: r0 = TextSelection()
    //     0x93e618: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x93e61c: mov             x1, x0
    // 0x93e620: ldur            x0, [fp, #-0x18]
    // 0x93e624: stur            x1, [fp, #-0x38]
    // 0x93e628: ArrayStore: r1[0] = r0  ; List_8
    //     0x93e628: stur            x0, [x1, #0x17]
    // 0x93e62c: StoreField: r1->field_1f = r0
    //     0x93e62c: stur            x0, [x1, #0x1f]
    // 0x93e630: r2 = Instance_TextAffinity
    //     0x93e630: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x93e634: StoreField: r1->field_27 = r2
    //     0x93e634: stur            w2, [x1, #0x27]
    // 0x93e638: r3 = false
    //     0x93e638: add             x3, NULL, #0x30  ; false
    // 0x93e63c: StoreField: r1->field_2b = r3
    //     0x93e63c: stur            w3, [x1, #0x2b]
    // 0x93e640: StoreField: r1->field_7 = r0
    //     0x93e640: stur            x0, [x1, #7]
    // 0x93e644: StoreField: r1->field_f = r0
    //     0x93e644: stur            x0, [x1, #0xf]
    // 0x93e648: r0 = TextEditingValue()
    //     0x93e648: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x93e64c: mov             x1, x0
    // 0x93e650: ldur            x0, [fp, #-0x20]
    // 0x93e654: StoreField: r1->field_7 = r0
    //     0x93e654: stur            w0, [x1, #7]
    // 0x93e658: ldur            x0, [fp, #-0x38]
    // 0x93e65c: StoreField: r1->field_b = r0
    //     0x93e65c: stur            w0, [x1, #0xb]
    // 0x93e660: r0 = Instance_TextRange
    //     0x93e660: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x93e664: StoreField: r1->field_f = r0
    //     0x93e664: stur            w0, [x1, #0xf]
    // 0x93e668: mov             x2, x1
    // 0x93e66c: ldur            x1, [fp, #-0x28]
    // 0x93e670: r0 = value=()
    //     0x93e670: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x93e674: ldur            x0, [fp, #-8]
    // 0x93e678: LoadField: r1 = r0->field_3f
    //     0x93e678: ldur            w1, [x0, #0x3f]
    // 0x93e67c: DecompressPointer r1
    //     0x93e67c: add             x1, x1, HEAP, lsl #32
    // 0x93e680: LoadField: r2 = r1->field_27
    //     0x93e680: ldur            w2, [x1, #0x27]
    // 0x93e684: DecompressPointer r2
    //     0x93e684: add             x2, x2, HEAP, lsl #32
    // 0x93e688: LoadField: r1 = r2->field_7
    //     0x93e688: ldur            w1, [x2, #7]
    // 0x93e68c: DecompressPointer r1
    //     0x93e68c: add             x1, x1, HEAP, lsl #32
    // 0x93e690: LoadField: r2 = r1->field_7
    //     0x93e690: ldur            w2, [x1, #7]
    // 0x93e694: cmp             w2, #0xc
    // 0x93e698: b.ne            #0x93e6cc
    // 0x93e69c: LoadField: r2 = r0->field_b
    //     0x93e69c: ldur            w2, [x0, #0xb]
    // 0x93e6a0: DecompressPointer r2
    //     0x93e6a0: add             x2, x2, HEAP, lsl #32
    // 0x93e6a4: cmp             w2, NULL
    // 0x93e6a8: b.eq            #0x93ecd0
    // 0x93e6ac: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x93e6ac: ldur            w3, [x2, #0x17]
    // 0x93e6b0: DecompressPointer r3
    //     0x93e6b0: add             x3, x3, HEAP, lsl #32
    // 0x93e6b4: stp             x1, x3, [SP]
    // 0x93e6b8: r4 = 0
    //     0x93e6b8: movz            x4, #0
    // 0x93e6bc: ldr             x0, [SP, #8]
    // 0x93e6c0: r5 = UnlinkedCall_0x613b5c
    //     0x93e6c0: add             x16, PP, #0x57, lsl #12  ; [pp+0x57030] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x93e6c4: ldp             x5, lr, [x16, #0x30]
    // 0x93e6c8: blr             lr
    // 0x93e6cc: ldur            x0, [fp, #-8]
    // 0x93e6d0: ldur            x1, [fp, #-0x10]
    // 0x93e6d4: LoadField: r2 = r0->field_3b
    //     0x93e6d4: ldur            w2, [x0, #0x3b]
    // 0x93e6d8: DecompressPointer r2
    //     0x93e6d8: add             x2, x2, HEAP, lsl #32
    // 0x93e6dc: stur            x2, [fp, #-0x28]
    // 0x93e6e0: cmp             w1, NULL
    // 0x93e6e4: b.ne            #0x93e6f0
    // 0x93e6e8: r3 = Null
    //     0x93e6e8: mov             x3, NULL
    // 0x93e6ec: b               #0x93e6f8
    // 0x93e6f0: LoadField: r3 = r1->field_2b
    //     0x93e6f0: ldur            w3, [x1, #0x2b]
    // 0x93e6f4: DecompressPointer r3
    //     0x93e6f4: add             x3, x3, HEAP, lsl #32
    // 0x93e6f8: cmp             w3, NULL
    // 0x93e6fc: b.ne            #0x93e704
    // 0x93e700: r3 = ""
    //     0x93e700: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93e704: stur            x3, [fp, #-0x20]
    // 0x93e708: cmp             w1, NULL
    // 0x93e70c: b.ne            #0x93e718
    // 0x93e710: r4 = Null
    //     0x93e710: mov             x4, NULL
    // 0x93e714: b               #0x93e738
    // 0x93e718: LoadField: r4 = r1->field_2b
    //     0x93e718: ldur            w4, [x1, #0x2b]
    // 0x93e71c: DecompressPointer r4
    //     0x93e71c: add             x4, x4, HEAP, lsl #32
    // 0x93e720: cmp             w4, NULL
    // 0x93e724: b.ne            #0x93e730
    // 0x93e728: r4 = Null
    //     0x93e728: mov             x4, NULL
    // 0x93e72c: b               #0x93e738
    // 0x93e730: LoadField: r5 = r4->field_7
    //     0x93e730: ldur            w5, [x4, #7]
    // 0x93e734: mov             x4, x5
    // 0x93e738: cmp             w4, NULL
    // 0x93e73c: b.ne            #0x93e748
    // 0x93e740: r4 = 0
    //     0x93e740: movz            x4, #0
    // 0x93e744: b               #0x93e750
    // 0x93e748: r5 = LoadInt32Instr(r4)
    //     0x93e748: sbfx            x5, x4, #1, #0x1f
    // 0x93e74c: mov             x4, x5
    // 0x93e750: stur            x4, [fp, #-0x18]
    // 0x93e754: r0 = TextSelection()
    //     0x93e754: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x93e758: mov             x1, x0
    // 0x93e75c: ldur            x0, [fp, #-0x18]
    // 0x93e760: stur            x1, [fp, #-0x38]
    // 0x93e764: ArrayStore: r1[0] = r0  ; List_8
    //     0x93e764: stur            x0, [x1, #0x17]
    // 0x93e768: StoreField: r1->field_1f = r0
    //     0x93e768: stur            x0, [x1, #0x1f]
    // 0x93e76c: r2 = Instance_TextAffinity
    //     0x93e76c: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x93e770: StoreField: r1->field_27 = r2
    //     0x93e770: stur            w2, [x1, #0x27]
    // 0x93e774: r3 = false
    //     0x93e774: add             x3, NULL, #0x30  ; false
    // 0x93e778: StoreField: r1->field_2b = r3
    //     0x93e778: stur            w3, [x1, #0x2b]
    // 0x93e77c: StoreField: r1->field_7 = r0
    //     0x93e77c: stur            x0, [x1, #7]
    // 0x93e780: StoreField: r1->field_f = r0
    //     0x93e780: stur            x0, [x1, #0xf]
    // 0x93e784: r0 = TextEditingValue()
    //     0x93e784: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x93e788: mov             x1, x0
    // 0x93e78c: ldur            x0, [fp, #-0x20]
    // 0x93e790: StoreField: r1->field_7 = r0
    //     0x93e790: stur            w0, [x1, #7]
    // 0x93e794: ldur            x0, [fp, #-0x38]
    // 0x93e798: StoreField: r1->field_b = r0
    //     0x93e798: stur            w0, [x1, #0xb]
    // 0x93e79c: r0 = Instance_TextRange
    //     0x93e79c: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x93e7a0: StoreField: r1->field_f = r0
    //     0x93e7a0: stur            w0, [x1, #0xf]
    // 0x93e7a4: mov             x2, x1
    // 0x93e7a8: ldur            x1, [fp, #-0x28]
    // 0x93e7ac: r0 = value=()
    //     0x93e7ac: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x93e7b0: ldur            x0, [fp, #-8]
    // 0x93e7b4: LoadField: r1 = r0->field_47
    //     0x93e7b4: ldur            w1, [x0, #0x47]
    // 0x93e7b8: DecompressPointer r1
    //     0x93e7b8: add             x1, x1, HEAP, lsl #32
    // 0x93e7bc: ldur            x2, [fp, #-0x10]
    // 0x93e7c0: stur            x1, [fp, #-0x28]
    // 0x93e7c4: cmp             w2, NULL
    // 0x93e7c8: b.ne            #0x93e7d4
    // 0x93e7cc: r3 = Null
    //     0x93e7cc: mov             x3, NULL
    // 0x93e7d0: b               #0x93e7dc
    // 0x93e7d4: LoadField: r3 = r2->field_13
    //     0x93e7d4: ldur            w3, [x2, #0x13]
    // 0x93e7d8: DecompressPointer r3
    //     0x93e7d8: add             x3, x3, HEAP, lsl #32
    // 0x93e7dc: cmp             w3, NULL
    // 0x93e7e0: b.ne            #0x93e7e8
    // 0x93e7e4: r3 = ""
    //     0x93e7e4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93e7e8: stur            x3, [fp, #-0x20]
    // 0x93e7ec: cmp             w2, NULL
    // 0x93e7f0: b.ne            #0x93e7fc
    // 0x93e7f4: r4 = Null
    //     0x93e7f4: mov             x4, NULL
    // 0x93e7f8: b               #0x93e81c
    // 0x93e7fc: LoadField: r4 = r2->field_13
    //     0x93e7fc: ldur            w4, [x2, #0x13]
    // 0x93e800: DecompressPointer r4
    //     0x93e800: add             x4, x4, HEAP, lsl #32
    // 0x93e804: cmp             w4, NULL
    // 0x93e808: b.ne            #0x93e814
    // 0x93e80c: r4 = Null
    //     0x93e80c: mov             x4, NULL
    // 0x93e810: b               #0x93e81c
    // 0x93e814: LoadField: r5 = r4->field_7
    //     0x93e814: ldur            w5, [x4, #7]
    // 0x93e818: mov             x4, x5
    // 0x93e81c: cmp             w4, NULL
    // 0x93e820: b.ne            #0x93e82c
    // 0x93e824: r4 = 0
    //     0x93e824: movz            x4, #0
    // 0x93e828: b               #0x93e834
    // 0x93e82c: r5 = LoadInt32Instr(r4)
    //     0x93e82c: sbfx            x5, x4, #1, #0x1f
    // 0x93e830: mov             x4, x5
    // 0x93e834: stur            x4, [fp, #-0x18]
    // 0x93e838: r0 = TextSelection()
    //     0x93e838: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x93e83c: mov             x1, x0
    // 0x93e840: ldur            x0, [fp, #-0x18]
    // 0x93e844: stur            x1, [fp, #-0x38]
    // 0x93e848: ArrayStore: r1[0] = r0  ; List_8
    //     0x93e848: stur            x0, [x1, #0x17]
    // 0x93e84c: StoreField: r1->field_1f = r0
    //     0x93e84c: stur            x0, [x1, #0x1f]
    // 0x93e850: r2 = Instance_TextAffinity
    //     0x93e850: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x93e854: StoreField: r1->field_27 = r2
    //     0x93e854: stur            w2, [x1, #0x27]
    // 0x93e858: r3 = false
    //     0x93e858: add             x3, NULL, #0x30  ; false
    // 0x93e85c: StoreField: r1->field_2b = r3
    //     0x93e85c: stur            w3, [x1, #0x2b]
    // 0x93e860: StoreField: r1->field_7 = r0
    //     0x93e860: stur            x0, [x1, #7]
    // 0x93e864: StoreField: r1->field_f = r0
    //     0x93e864: stur            x0, [x1, #0xf]
    // 0x93e868: r0 = TextEditingValue()
    //     0x93e868: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x93e86c: mov             x1, x0
    // 0x93e870: ldur            x0, [fp, #-0x20]
    // 0x93e874: StoreField: r1->field_7 = r0
    //     0x93e874: stur            w0, [x1, #7]
    // 0x93e878: ldur            x0, [fp, #-0x38]
    // 0x93e87c: StoreField: r1->field_b = r0
    //     0x93e87c: stur            w0, [x1, #0xb]
    // 0x93e880: r0 = Instance_TextRange
    //     0x93e880: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x93e884: StoreField: r1->field_f = r0
    //     0x93e884: stur            w0, [x1, #0xf]
    // 0x93e888: mov             x2, x1
    // 0x93e88c: ldur            x1, [fp, #-0x28]
    // 0x93e890: r0 = value=()
    //     0x93e890: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x93e894: ldur            x0, [fp, #-8]
    // 0x93e898: LoadField: r1 = r0->field_37
    //     0x93e898: ldur            w1, [x0, #0x37]
    // 0x93e89c: DecompressPointer r1
    //     0x93e89c: add             x1, x1, HEAP, lsl #32
    // 0x93e8a0: ldur            x2, [fp, #-0x10]
    // 0x93e8a4: stur            x1, [fp, #-0x28]
    // 0x93e8a8: cmp             w2, NULL
    // 0x93e8ac: b.ne            #0x93e8b8
    // 0x93e8b0: r3 = Null
    //     0x93e8b0: mov             x3, NULL
    // 0x93e8b4: b               #0x93e8c0
    // 0x93e8b8: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x93e8b8: ldur            w3, [x2, #0x17]
    // 0x93e8bc: DecompressPointer r3
    //     0x93e8bc: add             x3, x3, HEAP, lsl #32
    // 0x93e8c0: cmp             w3, NULL
    // 0x93e8c4: b.ne            #0x93e8cc
    // 0x93e8c8: r3 = ""
    //     0x93e8c8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93e8cc: stur            x3, [fp, #-0x20]
    // 0x93e8d0: cmp             w2, NULL
    // 0x93e8d4: b.ne            #0x93e8e0
    // 0x93e8d8: r4 = Null
    //     0x93e8d8: mov             x4, NULL
    // 0x93e8dc: b               #0x93e900
    // 0x93e8e0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93e8e0: ldur            w4, [x2, #0x17]
    // 0x93e8e4: DecompressPointer r4
    //     0x93e8e4: add             x4, x4, HEAP, lsl #32
    // 0x93e8e8: cmp             w4, NULL
    // 0x93e8ec: b.ne            #0x93e8f8
    // 0x93e8f0: r4 = Null
    //     0x93e8f0: mov             x4, NULL
    // 0x93e8f4: b               #0x93e900
    // 0x93e8f8: LoadField: r5 = r4->field_7
    //     0x93e8f8: ldur            w5, [x4, #7]
    // 0x93e8fc: mov             x4, x5
    // 0x93e900: cmp             w4, NULL
    // 0x93e904: b.ne            #0x93e910
    // 0x93e908: r4 = 0
    //     0x93e908: movz            x4, #0
    // 0x93e90c: b               #0x93e918
    // 0x93e910: r5 = LoadInt32Instr(r4)
    //     0x93e910: sbfx            x5, x4, #1, #0x1f
    // 0x93e914: mov             x4, x5
    // 0x93e918: stur            x4, [fp, #-0x18]
    // 0x93e91c: r0 = TextSelection()
    //     0x93e91c: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x93e920: mov             x1, x0
    // 0x93e924: ldur            x0, [fp, #-0x18]
    // 0x93e928: stur            x1, [fp, #-0x38]
    // 0x93e92c: ArrayStore: r1[0] = r0  ; List_8
    //     0x93e92c: stur            x0, [x1, #0x17]
    // 0x93e930: StoreField: r1->field_1f = r0
    //     0x93e930: stur            x0, [x1, #0x1f]
    // 0x93e934: r2 = Instance_TextAffinity
    //     0x93e934: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x93e938: StoreField: r1->field_27 = r2
    //     0x93e938: stur            w2, [x1, #0x27]
    // 0x93e93c: r3 = false
    //     0x93e93c: add             x3, NULL, #0x30  ; false
    // 0x93e940: StoreField: r1->field_2b = r3
    //     0x93e940: stur            w3, [x1, #0x2b]
    // 0x93e944: StoreField: r1->field_7 = r0
    //     0x93e944: stur            x0, [x1, #7]
    // 0x93e948: StoreField: r1->field_f = r0
    //     0x93e948: stur            x0, [x1, #0xf]
    // 0x93e94c: r0 = TextEditingValue()
    //     0x93e94c: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x93e950: mov             x1, x0
    // 0x93e954: ldur            x0, [fp, #-0x20]
    // 0x93e958: StoreField: r1->field_7 = r0
    //     0x93e958: stur            w0, [x1, #7]
    // 0x93e95c: ldur            x0, [fp, #-0x38]
    // 0x93e960: StoreField: r1->field_b = r0
    //     0x93e960: stur            w0, [x1, #0xb]
    // 0x93e964: r0 = Instance_TextRange
    //     0x93e964: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x93e968: StoreField: r1->field_f = r0
    //     0x93e968: stur            w0, [x1, #0xf]
    // 0x93e96c: mov             x2, x1
    // 0x93e970: ldur            x1, [fp, #-0x28]
    // 0x93e974: r0 = value=()
    //     0x93e974: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x93e978: ldur            x0, [fp, #-8]
    // 0x93e97c: LoadField: r1 = r0->field_43
    //     0x93e97c: ldur            w1, [x0, #0x43]
    // 0x93e980: DecompressPointer r1
    //     0x93e980: add             x1, x1, HEAP, lsl #32
    // 0x93e984: ldur            x2, [fp, #-0x10]
    // 0x93e988: stur            x1, [fp, #-0x28]
    // 0x93e98c: cmp             w2, NULL
    // 0x93e990: b.ne            #0x93e99c
    // 0x93e994: r3 = Null
    //     0x93e994: mov             x3, NULL
    // 0x93e998: b               #0x93e9a4
    // 0x93e99c: LoadField: r3 = r2->field_2f
    //     0x93e99c: ldur            w3, [x2, #0x2f]
    // 0x93e9a0: DecompressPointer r3
    //     0x93e9a0: add             x3, x3, HEAP, lsl #32
    // 0x93e9a4: cmp             w3, NULL
    // 0x93e9a8: b.ne            #0x93e9b0
    // 0x93e9ac: r3 = ""
    //     0x93e9ac: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93e9b0: stur            x3, [fp, #-0x20]
    // 0x93e9b4: cmp             w2, NULL
    // 0x93e9b8: b.ne            #0x93e9c4
    // 0x93e9bc: r2 = Null
    //     0x93e9bc: mov             x2, NULL
    // 0x93e9c0: b               #0x93e9e0
    // 0x93e9c4: LoadField: r4 = r2->field_2f
    //     0x93e9c4: ldur            w4, [x2, #0x2f]
    // 0x93e9c8: DecompressPointer r4
    //     0x93e9c8: add             x4, x4, HEAP, lsl #32
    // 0x93e9cc: cmp             w4, NULL
    // 0x93e9d0: b.ne            #0x93e9dc
    // 0x93e9d4: r2 = Null
    //     0x93e9d4: mov             x2, NULL
    // 0x93e9d8: b               #0x93e9e0
    // 0x93e9dc: LoadField: r2 = r4->field_7
    //     0x93e9dc: ldur            w2, [x4, #7]
    // 0x93e9e0: cmp             w2, NULL
    // 0x93e9e4: b.ne            #0x93e9f0
    // 0x93e9e8: r2 = 0
    //     0x93e9e8: movz            x2, #0
    // 0x93e9ec: b               #0x93e9f8
    // 0x93e9f0: r4 = LoadInt32Instr(r2)
    //     0x93e9f0: sbfx            x4, x2, #1, #0x1f
    // 0x93e9f4: mov             x2, x4
    // 0x93e9f8: stur            x2, [fp, #-0x18]
    // 0x93e9fc: r0 = TextSelection()
    //     0x93e9fc: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x93ea00: mov             x1, x0
    // 0x93ea04: ldur            x0, [fp, #-0x18]
    // 0x93ea08: stur            x1, [fp, #-0x10]
    // 0x93ea0c: ArrayStore: r1[0] = r0  ; List_8
    //     0x93ea0c: stur            x0, [x1, #0x17]
    // 0x93ea10: StoreField: r1->field_1f = r0
    //     0x93ea10: stur            x0, [x1, #0x1f]
    // 0x93ea14: r2 = Instance_TextAffinity
    //     0x93ea14: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x93ea18: StoreField: r1->field_27 = r2
    //     0x93ea18: stur            w2, [x1, #0x27]
    // 0x93ea1c: r3 = false
    //     0x93ea1c: add             x3, NULL, #0x30  ; false
    // 0x93ea20: StoreField: r1->field_2b = r3
    //     0x93ea20: stur            w3, [x1, #0x2b]
    // 0x93ea24: StoreField: r1->field_7 = r0
    //     0x93ea24: stur            x0, [x1, #7]
    // 0x93ea28: StoreField: r1->field_f = r0
    //     0x93ea28: stur            x0, [x1, #0xf]
    // 0x93ea2c: r0 = TextEditingValue()
    //     0x93ea2c: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x93ea30: mov             x1, x0
    // 0x93ea34: ldur            x0, [fp, #-0x20]
    // 0x93ea38: StoreField: r1->field_7 = r0
    //     0x93ea38: stur            w0, [x1, #7]
    // 0x93ea3c: ldur            x0, [fp, #-0x10]
    // 0x93ea40: StoreField: r1->field_b = r0
    //     0x93ea40: stur            w0, [x1, #0xb]
    // 0x93ea44: r0 = Instance_TextRange
    //     0x93ea44: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x93ea48: StoreField: r1->field_f = r0
    //     0x93ea48: stur            w0, [x1, #0xf]
    // 0x93ea4c: mov             x2, x1
    // 0x93ea50: ldur            x1, [fp, #-0x28]
    // 0x93ea54: r0 = value=()
    //     0x93ea54: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x93ea58: ldur            x0, [fp, #-8]
    // 0x93ea5c: LoadField: r3 = r0->field_33
    //     0x93ea5c: ldur            w3, [x0, #0x33]
    // 0x93ea60: DecompressPointer r3
    //     0x93ea60: add             x3, x3, HEAP, lsl #32
    // 0x93ea64: stur            x3, [fp, #-0x28]
    // 0x93ea68: LoadField: r1 = r0->field_b
    //     0x93ea68: ldur            w1, [x0, #0xb]
    // 0x93ea6c: DecompressPointer r1
    //     0x93ea6c: add             x1, x1, HEAP, lsl #32
    // 0x93ea70: cmp             w1, NULL
    // 0x93ea74: b.eq            #0x93ecd4
    // 0x93ea78: LoadField: r4 = r1->field_1f
    //     0x93ea78: ldur            w4, [x1, #0x1f]
    // 0x93ea7c: DecompressPointer r4
    //     0x93ea7c: add             x4, x4, HEAP, lsl #32
    // 0x93ea80: stur            x4, [fp, #-0x20]
    // 0x93ea84: LoadField: r1 = r4->field_b
    //     0x93ea84: ldur            w1, [x4, #0xb]
    // 0x93ea88: DecompressPointer r1
    //     0x93ea88: add             x1, x1, HEAP, lsl #32
    // 0x93ea8c: cmp             w1, NULL
    // 0x93ea90: b.ne            #0x93ea9c
    // 0x93ea94: r5 = ""
    //     0x93ea94: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93ea98: b               #0x93eaa0
    // 0x93ea9c: mov             x5, x1
    // 0x93eaa0: stur            x5, [fp, #-0x10]
    // 0x93eaa4: r1 = Null
    //     0x93eaa4: mov             x1, NULL
    // 0x93eaa8: r2 = 6
    //     0x93eaa8: movz            x2, #0x6
    // 0x93eaac: r0 = AllocateArray()
    //     0x93eaac: bl              #0x16f7198  ; AllocateArrayStub
    // 0x93eab0: mov             x1, x0
    // 0x93eab4: ldur            x0, [fp, #-0x10]
    // 0x93eab8: StoreField: r1->field_f = r0
    //     0x93eab8: stur            w0, [x1, #0xf]
    // 0x93eabc: r16 = " "
    //     0x93eabc: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x93eac0: StoreField: r1->field_13 = r16
    //     0x93eac0: stur            w16, [x1, #0x13]
    // 0x93eac4: ldur            x0, [fp, #-0x20]
    // 0x93eac8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x93eac8: ldur            w2, [x0, #0x17]
    // 0x93eacc: DecompressPointer r2
    //     0x93eacc: add             x2, x2, HEAP, lsl #32
    // 0x93ead0: cmp             w2, NULL
    // 0x93ead4: b.ne            #0x93eadc
    // 0x93ead8: r2 = ""
    //     0x93ead8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93eadc: ldur            x0, [fp, #-8]
    // 0x93eae0: ArrayStore: r1[0] = r2  ; List_4
    //     0x93eae0: stur            w2, [x1, #0x17]
    // 0x93eae4: str             x1, [SP]
    // 0x93eae8: r0 = _interpolate()
    //     0x93eae8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x93eaec: mov             x1, x0
    // 0x93eaf0: ldur            x0, [fp, #-8]
    // 0x93eaf4: stur            x1, [fp, #-0x10]
    // 0x93eaf8: LoadField: r2 = r0->field_b
    //     0x93eaf8: ldur            w2, [x0, #0xb]
    // 0x93eafc: DecompressPointer r2
    //     0x93eafc: add             x2, x2, HEAP, lsl #32
    // 0x93eb00: cmp             w2, NULL
    // 0x93eb04: b.eq            #0x93ecd8
    // 0x93eb08: LoadField: r0 = r2->field_1f
    //     0x93eb08: ldur            w0, [x2, #0x1f]
    // 0x93eb0c: DecompressPointer r0
    //     0x93eb0c: add             x0, x0, HEAP, lsl #32
    // 0x93eb10: LoadField: r2 = r0->field_b
    //     0x93eb10: ldur            w2, [x0, #0xb]
    // 0x93eb14: DecompressPointer r2
    //     0x93eb14: add             x2, x2, HEAP, lsl #32
    // 0x93eb18: cmp             w2, NULL
    // 0x93eb1c: b.ne            #0x93eb28
    // 0x93eb20: r2 = Null
    //     0x93eb20: mov             x2, NULL
    // 0x93eb24: b               #0x93eb30
    // 0x93eb28: LoadField: r3 = r2->field_7
    //     0x93eb28: ldur            w3, [x2, #7]
    // 0x93eb2c: mov             x2, x3
    // 0x93eb30: cmp             w2, NULL
    // 0x93eb34: b.ne            #0x93eb70
    // 0x93eb38: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x93eb38: ldur            w2, [x0, #0x17]
    // 0x93eb3c: DecompressPointer r2
    //     0x93eb3c: add             x2, x2, HEAP, lsl #32
    // 0x93eb40: cmp             w2, NULL
    // 0x93eb44: b.ne            #0x93eb50
    // 0x93eb48: r0 = Null
    //     0x93eb48: mov             x0, NULL
    // 0x93eb4c: b               #0x93eb54
    // 0x93eb50: LoadField: r0 = r2->field_7
    //     0x93eb50: ldur            w0, [x2, #7]
    // 0x93eb54: cmp             w0, NULL
    // 0x93eb58: b.ne            #0x93eb64
    // 0x93eb5c: r0 = 0
    //     0x93eb5c: movz            x0, #0
    // 0x93eb60: b               #0x93eb74
    // 0x93eb64: r2 = LoadInt32Instr(r0)
    //     0x93eb64: sbfx            x2, x0, #1, #0x1f
    // 0x93eb68: mov             x0, x2
    // 0x93eb6c: b               #0x93eb74
    // 0x93eb70: r0 = LoadInt32Instr(r2)
    //     0x93eb70: sbfx            x0, x2, #1, #0x1f
    // 0x93eb74: stur            x0, [fp, #-0x18]
    // 0x93eb78: r0 = TextSelection()
    //     0x93eb78: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x93eb7c: mov             x1, x0
    // 0x93eb80: ldur            x0, [fp, #-0x18]
    // 0x93eb84: stur            x1, [fp, #-8]
    // 0x93eb88: ArrayStore: r1[0] = r0  ; List_8
    //     0x93eb88: stur            x0, [x1, #0x17]
    // 0x93eb8c: StoreField: r1->field_1f = r0
    //     0x93eb8c: stur            x0, [x1, #0x1f]
    // 0x93eb90: r2 = Instance_TextAffinity
    //     0x93eb90: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x93eb94: StoreField: r1->field_27 = r2
    //     0x93eb94: stur            w2, [x1, #0x27]
    // 0x93eb98: r2 = false
    //     0x93eb98: add             x2, NULL, #0x30  ; false
    // 0x93eb9c: StoreField: r1->field_2b = r2
    //     0x93eb9c: stur            w2, [x1, #0x2b]
    // 0x93eba0: StoreField: r1->field_7 = r0
    //     0x93eba0: stur            x0, [x1, #7]
    // 0x93eba4: StoreField: r1->field_f = r0
    //     0x93eba4: stur            x0, [x1, #0xf]
    // 0x93eba8: r0 = TextEditingValue()
    //     0x93eba8: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x93ebac: mov             x1, x0
    // 0x93ebb0: ldur            x0, [fp, #-0x10]
    // 0x93ebb4: StoreField: r1->field_7 = r0
    //     0x93ebb4: stur            w0, [x1, #7]
    // 0x93ebb8: ldur            x0, [fp, #-8]
    // 0x93ebbc: StoreField: r1->field_b = r0
    //     0x93ebbc: stur            w0, [x1, #0xb]
    // 0x93ebc0: r0 = Instance_TextRange
    //     0x93ebc0: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x93ebc4: StoreField: r1->field_f = r0
    //     0x93ebc4: stur            w0, [x1, #0xf]
    // 0x93ebc8: mov             x2, x1
    // 0x93ebcc: ldur            x1, [fp, #-0x28]
    // 0x93ebd0: r0 = value=()
    //     0x93ebd0: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x93ebd4: r0 = LoadStaticField(0x878)
    //     0x93ebd4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93ebd8: ldr             x0, [x0, #0x10f0]
    // 0x93ebdc: cmp             w0, NULL
    // 0x93ebe0: b.eq            #0x93ecdc
    // 0x93ebe4: LoadField: r3 = r0->field_53
    //     0x93ebe4: ldur            w3, [x0, #0x53]
    // 0x93ebe8: DecompressPointer r3
    //     0x93ebe8: add             x3, x3, HEAP, lsl #32
    // 0x93ebec: stur            x3, [fp, #-0x10]
    // 0x93ebf0: LoadField: r0 = r3->field_7
    //     0x93ebf0: ldur            w0, [x3, #7]
    // 0x93ebf4: DecompressPointer r0
    //     0x93ebf4: add             x0, x0, HEAP, lsl #32
    // 0x93ebf8: ldur            x2, [fp, #-0x30]
    // 0x93ebfc: stur            x0, [fp, #-8]
    // 0x93ec00: r1 = Function '<anonymous closure>':.
    //     0x93ec00: add             x1, PP, #0x57, lsl #12  ; [pp+0x57040] AnonymousClosure: (0x93ece0), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::setUpAddress (0x93e55c)
    //     0x93ec04: ldr             x1, [x1, #0x40]
    // 0x93ec08: r0 = AllocateClosure()
    //     0x93ec08: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93ec0c: ldur            x2, [fp, #-8]
    // 0x93ec10: mov             x3, x0
    // 0x93ec14: r1 = Null
    //     0x93ec14: mov             x1, NULL
    // 0x93ec18: stur            x3, [fp, #-8]
    // 0x93ec1c: cmp             w2, NULL
    // 0x93ec20: b.eq            #0x93ec40
    // 0x93ec24: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93ec24: ldur            w4, [x2, #0x17]
    // 0x93ec28: DecompressPointer r4
    //     0x93ec28: add             x4, x4, HEAP, lsl #32
    // 0x93ec2c: r8 = X0
    //     0x93ec2c: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x93ec30: LoadField: r9 = r4->field_7
    //     0x93ec30: ldur            x9, [x4, #7]
    // 0x93ec34: r3 = Null
    //     0x93ec34: add             x3, PP, #0x57, lsl #12  ; [pp+0x57048] Null
    //     0x93ec38: ldr             x3, [x3, #0x48]
    // 0x93ec3c: blr             x9
    // 0x93ec40: ldur            x0, [fp, #-0x10]
    // 0x93ec44: LoadField: r1 = r0->field_b
    //     0x93ec44: ldur            w1, [x0, #0xb]
    // 0x93ec48: LoadField: r2 = r0->field_f
    //     0x93ec48: ldur            w2, [x0, #0xf]
    // 0x93ec4c: DecompressPointer r2
    //     0x93ec4c: add             x2, x2, HEAP, lsl #32
    // 0x93ec50: LoadField: r3 = r2->field_b
    //     0x93ec50: ldur            w3, [x2, #0xb]
    // 0x93ec54: r2 = LoadInt32Instr(r1)
    //     0x93ec54: sbfx            x2, x1, #1, #0x1f
    // 0x93ec58: stur            x2, [fp, #-0x18]
    // 0x93ec5c: r1 = LoadInt32Instr(r3)
    //     0x93ec5c: sbfx            x1, x3, #1, #0x1f
    // 0x93ec60: cmp             x2, x1
    // 0x93ec64: b.ne            #0x93ec70
    // 0x93ec68: mov             x1, x0
    // 0x93ec6c: r0 = _growToNextCapacity()
    //     0x93ec6c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93ec70: ldur            x2, [fp, #-0x10]
    // 0x93ec74: ldur            x3, [fp, #-0x18]
    // 0x93ec78: add             x4, x3, #1
    // 0x93ec7c: lsl             x5, x4, #1
    // 0x93ec80: StoreField: r2->field_b = r5
    //     0x93ec80: stur            w5, [x2, #0xb]
    // 0x93ec84: LoadField: r1 = r2->field_f
    //     0x93ec84: ldur            w1, [x2, #0xf]
    // 0x93ec88: DecompressPointer r1
    //     0x93ec88: add             x1, x1, HEAP, lsl #32
    // 0x93ec8c: ldur            x0, [fp, #-8]
    // 0x93ec90: ArrayStore: r1[r3] = r0  ; List_4
    //     0x93ec90: add             x25, x1, x3, lsl #2
    //     0x93ec94: add             x25, x25, #0xf
    //     0x93ec98: str             w0, [x25]
    //     0x93ec9c: tbz             w0, #0, #0x93ecb8
    //     0x93eca0: ldurb           w16, [x1, #-1]
    //     0x93eca4: ldurb           w17, [x0, #-1]
    //     0x93eca8: and             x16, x17, x16, lsr #2
    //     0x93ecac: tst             x16, HEAP, lsr #32
    //     0x93ecb0: b.eq            #0x93ecb8
    //     0x93ecb4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93ecb8: r0 = Null
    //     0x93ecb8: mov             x0, NULL
    // 0x93ecbc: LeaveFrame
    //     0x93ecbc: mov             SP, fp
    //     0x93ecc0: ldp             fp, lr, [SP], #0x10
    // 0x93ecc4: ret
    //     0x93ecc4: ret             
    // 0x93ecc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93ecc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93eccc: b               #0x93e57c
    // 0x93ecd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93ecd0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93ecd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93ecd4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93ecd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93ecd8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93ecdc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93ecdc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x93ece0, size: 0x33c
    // 0x93ece0: EnterFrame
    //     0x93ece0: stp             fp, lr, [SP, #-0x10]!
    //     0x93ece4: mov             fp, SP
    // 0x93ece8: AllocStack(0x58)
    //     0x93ece8: sub             SP, SP, #0x58
    // 0x93ecec: SetupParameters()
    //     0x93ecec: ldr             x0, [fp, #0x18]
    //     0x93ecf0: ldur            w3, [x0, #0x17]
    //     0x93ecf4: add             x3, x3, HEAP, lsl #32
    //     0x93ecf8: stur            x3, [fp, #-0x20]
    // 0x93ecfc: CheckStackOverflow
    //     0x93ecfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93ed00: cmp             SP, x16
    //     0x93ed04: b.ls            #0x93eff8
    // 0x93ed08: LoadField: r0 = r3->field_f
    //     0x93ed08: ldur            w0, [x3, #0xf]
    // 0x93ed0c: DecompressPointer r0
    //     0x93ed0c: add             x0, x0, HEAP, lsl #32
    // 0x93ed10: LoadField: r4 = r0->field_b
    //     0x93ed10: ldur            w4, [x0, #0xb]
    // 0x93ed14: DecompressPointer r4
    //     0x93ed14: add             x4, x4, HEAP, lsl #32
    // 0x93ed18: stur            x4, [fp, #-0x18]
    // 0x93ed1c: cmp             w4, NULL
    // 0x93ed20: b.eq            #0x93f000
    // 0x93ed24: LoadField: r0 = r4->field_1f
    //     0x93ed24: ldur            w0, [x4, #0x1f]
    // 0x93ed28: DecompressPointer r0
    //     0x93ed28: add             x0, x0, HEAP, lsl #32
    // 0x93ed2c: stur            x0, [fp, #-0x10]
    // 0x93ed30: LoadField: r1 = r0->field_b
    //     0x93ed30: ldur            w1, [x0, #0xb]
    // 0x93ed34: DecompressPointer r1
    //     0x93ed34: add             x1, x1, HEAP, lsl #32
    // 0x93ed38: cmp             w1, NULL
    // 0x93ed3c: b.ne            #0x93ed48
    // 0x93ed40: r5 = ""
    //     0x93ed40: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93ed44: b               #0x93ed4c
    // 0x93ed48: mov             x5, x1
    // 0x93ed4c: stur            x5, [fp, #-8]
    // 0x93ed50: r1 = Null
    //     0x93ed50: mov             x1, NULL
    // 0x93ed54: r2 = 6
    //     0x93ed54: movz            x2, #0x6
    // 0x93ed58: r0 = AllocateArray()
    //     0x93ed58: bl              #0x16f7198  ; AllocateArrayStub
    // 0x93ed5c: mov             x1, x0
    // 0x93ed60: ldur            x0, [fp, #-8]
    // 0x93ed64: StoreField: r1->field_f = r0
    //     0x93ed64: stur            w0, [x1, #0xf]
    // 0x93ed68: r16 = " "
    //     0x93ed68: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x93ed6c: StoreField: r1->field_13 = r16
    //     0x93ed6c: stur            w16, [x1, #0x13]
    // 0x93ed70: ldur            x0, [fp, #-0x10]
    // 0x93ed74: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x93ed74: ldur            w2, [x0, #0x17]
    // 0x93ed78: DecompressPointer r2
    //     0x93ed78: add             x2, x2, HEAP, lsl #32
    // 0x93ed7c: cmp             w2, NULL
    // 0x93ed80: b.ne            #0x93ed88
    // 0x93ed84: r2 = ""
    //     0x93ed84: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93ed88: ldur            x0, [fp, #-0x20]
    // 0x93ed8c: ArrayStore: r1[0] = r2  ; List_4
    //     0x93ed8c: stur            w2, [x1, #0x17]
    // 0x93ed90: str             x1, [SP]
    // 0x93ed94: r0 = _interpolate()
    //     0x93ed94: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x93ed98: mov             x2, x0
    // 0x93ed9c: ldur            x0, [fp, #-0x20]
    // 0x93eda0: LoadField: r1 = r0->field_f
    //     0x93eda0: ldur            w1, [x0, #0xf]
    // 0x93eda4: DecompressPointer r1
    //     0x93eda4: add             x1, x1, HEAP, lsl #32
    // 0x93eda8: LoadField: r0 = r1->field_b
    //     0x93eda8: ldur            w0, [x1, #0xb]
    // 0x93edac: DecompressPointer r0
    //     0x93edac: add             x0, x0, HEAP, lsl #32
    // 0x93edb0: cmp             w0, NULL
    // 0x93edb4: b.eq            #0x93f004
    // 0x93edb8: LoadField: r1 = r0->field_1f
    //     0x93edb8: ldur            w1, [x0, #0x1f]
    // 0x93edbc: DecompressPointer r1
    //     0x93edbc: add             x1, x1, HEAP, lsl #32
    // 0x93edc0: LoadField: r3 = r1->field_1b
    //     0x93edc0: ldur            w3, [x1, #0x1b]
    // 0x93edc4: DecompressPointer r3
    //     0x93edc4: add             x3, x3, HEAP, lsl #32
    // 0x93edc8: cmp             w3, NULL
    // 0x93edcc: b.ne            #0x93edd8
    // 0x93edd0: r0 = Null
    //     0x93edd0: mov             x0, NULL
    // 0x93edd4: b               #0x93ee18
    // 0x93edd8: LoadField: r0 = r3->field_b
    //     0x93edd8: ldur            w0, [x3, #0xb]
    // 0x93eddc: r1 = LoadInt32Instr(r0)
    //     0x93eddc: sbfx            x1, x0, #1, #0x1f
    // 0x93ede0: mov             x0, x1
    // 0x93ede4: r1 = 0
    //     0x93ede4: movz            x1, #0
    // 0x93ede8: cmp             x1, x0
    // 0x93edec: b.hs            #0x93f008
    // 0x93edf0: LoadField: r0 = r3->field_f
    //     0x93edf0: ldur            w0, [x3, #0xf]
    // 0x93edf4: DecompressPointer r0
    //     0x93edf4: add             x0, x0, HEAP, lsl #32
    // 0x93edf8: LoadField: r1 = r0->field_f
    //     0x93edf8: ldur            w1, [x0, #0xf]
    // 0x93edfc: DecompressPointer r1
    //     0x93edfc: add             x1, x1, HEAP, lsl #32
    // 0x93ee00: cmp             w1, NULL
    // 0x93ee04: b.ne            #0x93ee10
    // 0x93ee08: r0 = Null
    //     0x93ee08: mov             x0, NULL
    // 0x93ee0c: b               #0x93ee18
    // 0x93ee10: LoadField: r0 = r1->field_1b
    //     0x93ee10: ldur            w0, [x1, #0x1b]
    // 0x93ee14: DecompressPointer r0
    //     0x93ee14: add             x0, x0, HEAP, lsl #32
    // 0x93ee18: cmp             w0, NULL
    // 0x93ee1c: b.ne            #0x93ee28
    // 0x93ee20: r4 = ""
    //     0x93ee20: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93ee24: b               #0x93ee2c
    // 0x93ee28: mov             x4, x0
    // 0x93ee2c: cmp             w3, NULL
    // 0x93ee30: b.ne            #0x93ee3c
    // 0x93ee34: r0 = Null
    //     0x93ee34: mov             x0, NULL
    // 0x93ee38: b               #0x93ee7c
    // 0x93ee3c: LoadField: r0 = r3->field_b
    //     0x93ee3c: ldur            w0, [x3, #0xb]
    // 0x93ee40: r1 = LoadInt32Instr(r0)
    //     0x93ee40: sbfx            x1, x0, #1, #0x1f
    // 0x93ee44: mov             x0, x1
    // 0x93ee48: r1 = 0
    //     0x93ee48: movz            x1, #0
    // 0x93ee4c: cmp             x1, x0
    // 0x93ee50: b.hs            #0x93f00c
    // 0x93ee54: LoadField: r0 = r3->field_f
    //     0x93ee54: ldur            w0, [x3, #0xf]
    // 0x93ee58: DecompressPointer r0
    //     0x93ee58: add             x0, x0, HEAP, lsl #32
    // 0x93ee5c: LoadField: r1 = r0->field_f
    //     0x93ee5c: ldur            w1, [x0, #0xf]
    // 0x93ee60: DecompressPointer r1
    //     0x93ee60: add             x1, x1, HEAP, lsl #32
    // 0x93ee64: cmp             w1, NULL
    // 0x93ee68: b.ne            #0x93ee74
    // 0x93ee6c: r0 = Null
    //     0x93ee6c: mov             x0, NULL
    // 0x93ee70: b               #0x93ee7c
    // 0x93ee74: LoadField: r0 = r1->field_2b
    //     0x93ee74: ldur            w0, [x1, #0x2b]
    // 0x93ee78: DecompressPointer r0
    //     0x93ee78: add             x0, x0, HEAP, lsl #32
    // 0x93ee7c: cmp             w0, NULL
    // 0x93ee80: b.ne            #0x93ee8c
    // 0x93ee84: r5 = ""
    //     0x93ee84: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93ee88: b               #0x93ee90
    // 0x93ee8c: mov             x5, x0
    // 0x93ee90: cmp             w3, NULL
    // 0x93ee94: b.ne            #0x93eea0
    // 0x93ee98: r0 = Null
    //     0x93ee98: mov             x0, NULL
    // 0x93ee9c: b               #0x93eee0
    // 0x93eea0: LoadField: r0 = r3->field_b
    //     0x93eea0: ldur            w0, [x3, #0xb]
    // 0x93eea4: r1 = LoadInt32Instr(r0)
    //     0x93eea4: sbfx            x1, x0, #1, #0x1f
    // 0x93eea8: mov             x0, x1
    // 0x93eeac: r1 = 0
    //     0x93eeac: movz            x1, #0
    // 0x93eeb0: cmp             x1, x0
    // 0x93eeb4: b.hs            #0x93f010
    // 0x93eeb8: LoadField: r0 = r3->field_f
    //     0x93eeb8: ldur            w0, [x3, #0xf]
    // 0x93eebc: DecompressPointer r0
    //     0x93eebc: add             x0, x0, HEAP, lsl #32
    // 0x93eec0: LoadField: r1 = r0->field_f
    //     0x93eec0: ldur            w1, [x0, #0xf]
    // 0x93eec4: DecompressPointer r1
    //     0x93eec4: add             x1, x1, HEAP, lsl #32
    // 0x93eec8: cmp             w1, NULL
    // 0x93eecc: b.ne            #0x93eed8
    // 0x93eed0: r0 = Null
    //     0x93eed0: mov             x0, NULL
    // 0x93eed4: b               #0x93eee0
    // 0x93eed8: LoadField: r0 = r1->field_13
    //     0x93eed8: ldur            w0, [x1, #0x13]
    // 0x93eedc: DecompressPointer r0
    //     0x93eedc: add             x0, x0, HEAP, lsl #32
    // 0x93eee0: cmp             w0, NULL
    // 0x93eee4: b.ne            #0x93eef0
    // 0x93eee8: r6 = ""
    //     0x93eee8: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93eeec: b               #0x93eef4
    // 0x93eef0: mov             x6, x0
    // 0x93eef4: cmp             w3, NULL
    // 0x93eef8: b.ne            #0x93ef04
    // 0x93eefc: r0 = Null
    //     0x93eefc: mov             x0, NULL
    // 0x93ef00: b               #0x93ef44
    // 0x93ef04: LoadField: r0 = r3->field_b
    //     0x93ef04: ldur            w0, [x3, #0xb]
    // 0x93ef08: r1 = LoadInt32Instr(r0)
    //     0x93ef08: sbfx            x1, x0, #1, #0x1f
    // 0x93ef0c: mov             x0, x1
    // 0x93ef10: r1 = 0
    //     0x93ef10: movz            x1, #0
    // 0x93ef14: cmp             x1, x0
    // 0x93ef18: b.hs            #0x93f014
    // 0x93ef1c: LoadField: r0 = r3->field_f
    //     0x93ef1c: ldur            w0, [x3, #0xf]
    // 0x93ef20: DecompressPointer r0
    //     0x93ef20: add             x0, x0, HEAP, lsl #32
    // 0x93ef24: LoadField: r1 = r0->field_f
    //     0x93ef24: ldur            w1, [x0, #0xf]
    // 0x93ef28: DecompressPointer r1
    //     0x93ef28: add             x1, x1, HEAP, lsl #32
    // 0x93ef2c: cmp             w1, NULL
    // 0x93ef30: b.ne            #0x93ef3c
    // 0x93ef34: r0 = Null
    //     0x93ef34: mov             x0, NULL
    // 0x93ef38: b               #0x93ef44
    // 0x93ef3c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x93ef3c: ldur            w0, [x1, #0x17]
    // 0x93ef40: DecompressPointer r0
    //     0x93ef40: add             x0, x0, HEAP, lsl #32
    // 0x93ef44: cmp             w0, NULL
    // 0x93ef48: b.ne            #0x93ef54
    // 0x93ef4c: r7 = ""
    //     0x93ef4c: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93ef50: b               #0x93ef58
    // 0x93ef54: mov             x7, x0
    // 0x93ef58: cmp             w3, NULL
    // 0x93ef5c: b.ne            #0x93ef68
    // 0x93ef60: r0 = Null
    //     0x93ef60: mov             x0, NULL
    // 0x93ef64: b               #0x93efa8
    // 0x93ef68: LoadField: r0 = r3->field_b
    //     0x93ef68: ldur            w0, [x3, #0xb]
    // 0x93ef6c: r1 = LoadInt32Instr(r0)
    //     0x93ef6c: sbfx            x1, x0, #1, #0x1f
    // 0x93ef70: mov             x0, x1
    // 0x93ef74: r1 = 0
    //     0x93ef74: movz            x1, #0
    // 0x93ef78: cmp             x1, x0
    // 0x93ef7c: b.hs            #0x93f018
    // 0x93ef80: LoadField: r0 = r3->field_f
    //     0x93ef80: ldur            w0, [x3, #0xf]
    // 0x93ef84: DecompressPointer r0
    //     0x93ef84: add             x0, x0, HEAP, lsl #32
    // 0x93ef88: LoadField: r1 = r0->field_f
    //     0x93ef88: ldur            w1, [x0, #0xf]
    // 0x93ef8c: DecompressPointer r1
    //     0x93ef8c: add             x1, x1, HEAP, lsl #32
    // 0x93ef90: cmp             w1, NULL
    // 0x93ef94: b.ne            #0x93efa0
    // 0x93ef98: r0 = Null
    //     0x93ef98: mov             x0, NULL
    // 0x93ef9c: b               #0x93efa8
    // 0x93efa0: LoadField: r0 = r1->field_2f
    //     0x93efa0: ldur            w0, [x1, #0x2f]
    // 0x93efa4: DecompressPointer r0
    //     0x93efa4: add             x0, x0, HEAP, lsl #32
    // 0x93efa8: cmp             w0, NULL
    // 0x93efac: b.ne            #0x93efb8
    // 0x93efb0: r1 = ""
    //     0x93efb0: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93efb4: b               #0x93efbc
    // 0x93efb8: mov             x1, x0
    // 0x93efbc: ldur            x0, [fp, #-0x18]
    // 0x93efc0: LoadField: r3 = r0->field_f
    //     0x93efc0: ldur            w3, [x0, #0xf]
    // 0x93efc4: DecompressPointer r3
    //     0x93efc4: add             x3, x3, HEAP, lsl #32
    // 0x93efc8: stp             x2, x3, [SP, #0x28]
    // 0x93efcc: stp             x5, x4, [SP, #0x18]
    // 0x93efd0: stp             x7, x6, [SP, #8]
    // 0x93efd4: str             x1, [SP]
    // 0x93efd8: r4 = 0
    //     0x93efd8: movz            x4, #0
    // 0x93efdc: ldr             x0, [SP, #0x30]
    // 0x93efe0: r5 = UnlinkedCall_0x613b5c
    //     0x93efe0: add             x16, PP, #0x57, lsl #12  ; [pp+0x57058] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x93efe4: ldp             x5, lr, [x16, #0x58]
    // 0x93efe8: blr             lr
    // 0x93efec: LeaveFrame
    //     0x93efec: mov             SP, fp
    //     0x93eff0: ldp             fp, lr, [SP], #0x10
    // 0x93eff4: ret
    //     0x93eff4: ret             
    // 0x93eff8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93eff8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93effc: b               #0x93ed08
    // 0x93f000: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93f000: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93f004: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93f004: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93f008: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93f008: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93f00c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93f00c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93f010: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93f010: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93f014: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93f014: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93f018: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93f018: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x93f01c, size: 0x98
    // 0x93f01c: EnterFrame
    //     0x93f01c: stp             fp, lr, [SP, #-0x10]!
    //     0x93f020: mov             fp, SP
    // 0x93f024: AllocStack(0x38)
    //     0x93f024: sub             SP, SP, #0x38
    // 0x93f028: SetupParameters()
    //     0x93f028: ldr             x0, [fp, #0x18]
    //     0x93f02c: ldur            w1, [x0, #0x17]
    //     0x93f030: add             x1, x1, HEAP, lsl #32
    // 0x93f034: CheckStackOverflow
    //     0x93f034: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93f038: cmp             SP, x16
    //     0x93f03c: b.ls            #0x93f0a8
    // 0x93f040: LoadField: r0 = r1->field_f
    //     0x93f040: ldur            w0, [x1, #0xf]
    // 0x93f044: DecompressPointer r0
    //     0x93f044: add             x0, x0, HEAP, lsl #32
    // 0x93f048: LoadField: r1 = r0->field_b
    //     0x93f048: ldur            w1, [x0, #0xb]
    // 0x93f04c: DecompressPointer r1
    //     0x93f04c: add             x1, x1, HEAP, lsl #32
    // 0x93f050: cmp             w1, NULL
    // 0x93f054: b.eq            #0x93f0b0
    // 0x93f058: LoadField: r0 = r1->field_f
    //     0x93f058: ldur            w0, [x1, #0xf]
    // 0x93f05c: DecompressPointer r0
    //     0x93f05c: add             x0, x0, HEAP, lsl #32
    // 0x93f060: r16 = ""
    //     0x93f060: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93f064: stp             x16, x0, [SP, #0x28]
    // 0x93f068: r16 = ""
    //     0x93f068: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93f06c: r30 = ""
    //     0x93f06c: ldr             lr, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93f070: stp             lr, x16, [SP, #0x18]
    // 0x93f074: r16 = ""
    //     0x93f074: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93f078: r30 = ""
    //     0x93f078: ldr             lr, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93f07c: stp             lr, x16, [SP, #8]
    // 0x93f080: r16 = ""
    //     0x93f080: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93f084: str             x16, [SP]
    // 0x93f088: r4 = 0
    //     0x93f088: movz            x4, #0
    // 0x93f08c: ldr             x0, [SP, #0x30]
    // 0x93f090: r5 = UnlinkedCall_0x613b5c
    //     0x93f090: add             x16, PP, #0x57, lsl #12  ; [pp+0x57018] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x93f094: ldp             x5, lr, [x16, #0x18]
    // 0x93f098: blr             lr
    // 0x93f09c: LeaveFrame
    //     0x93f09c: mov             SP, fp
    //     0x93f0a0: ldp             fp, lr, [SP], #0x10
    // 0x93f0a4: ret
    //     0x93f0a4: ret             
    // 0x93f0a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93f0a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93f0ac: b               #0x93f040
    // 0x93f0b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93f0b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x93f0b4, size: 0x60
    // 0x93f0b4: EnterFrame
    //     0x93f0b4: stp             fp, lr, [SP, #-0x10]!
    //     0x93f0b8: mov             fp, SP
    // 0x93f0bc: AllocStack(0x8)
    //     0x93f0bc: sub             SP, SP, #8
    // 0x93f0c0: SetupParameters()
    //     0x93f0c0: ldr             x0, [fp, #0x18]
    //     0x93f0c4: ldur            w2, [x0, #0x17]
    //     0x93f0c8: add             x2, x2, HEAP, lsl #32
    // 0x93f0cc: CheckStackOverflow
    //     0x93f0cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93f0d0: cmp             SP, x16
    //     0x93f0d4: b.ls            #0x93f10c
    // 0x93f0d8: LoadField: r0 = r2->field_f
    //     0x93f0d8: ldur            w0, [x2, #0xf]
    // 0x93f0dc: DecompressPointer r0
    //     0x93f0dc: add             x0, x0, HEAP, lsl #32
    // 0x93f0e0: stur            x0, [fp, #-8]
    // 0x93f0e4: r1 = Function '<anonymous closure>':.
    //     0x93f0e4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57028] AnonymousClosure: (0x9040b4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::initState (0x945cec)
    //     0x93f0e8: ldr             x1, [x1, #0x28]
    // 0x93f0ec: r0 = AllocateClosure()
    //     0x93f0ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93f0f0: ldur            x1, [fp, #-8]
    // 0x93f0f4: mov             x2, x0
    // 0x93f0f8: r0 = setState()
    //     0x93f0f8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x93f0fc: r0 = Null
    //     0x93f0fc: mov             x0, NULL
    // 0x93f100: LeaveFrame
    //     0x93f100: mov             SP, fp
    //     0x93f104: ldp             fp, lr, [SP], #0x10
    // 0x93f108: ret
    //     0x93f108: ret             
    // 0x93f10c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93f10c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93f110: b               #0x93f0d8
  }
  _ build(/* No info */) {
    // ** addr: 0xb3ca88, size: 0x3328
    // 0xb3ca88: EnterFrame
    //     0xb3ca88: stp             fp, lr, [SP, #-0x10]!
    //     0xb3ca8c: mov             fp, SP
    // 0xb3ca90: AllocStack(0xf8)
    //     0xb3ca90: sub             SP, SP, #0xf8
    // 0xb3ca94: SetupParameters(_ChangeAddressBottomSheetState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb3ca94: stur            x1, [fp, #-8]
    //     0xb3ca98: mov             x16, x2
    //     0xb3ca9c: mov             x2, x1
    //     0xb3caa0: mov             x1, x16
    //     0xb3caa4: stur            x1, [fp, #-0x10]
    // 0xb3caa8: CheckStackOverflow
    //     0xb3caa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3caac: cmp             SP, x16
    //     0xb3cab0: b.ls            #0xb3fd7c
    // 0xb3cab4: r1 = 1
    //     0xb3cab4: movz            x1, #0x1
    // 0xb3cab8: r0 = AllocateContext()
    //     0xb3cab8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb3cabc: ldur            x2, [fp, #-8]
    // 0xb3cac0: stur            x0, [fp, #-0x18]
    // 0xb3cac4: StoreField: r0->field_f = r2
    //     0xb3cac4: stur            w2, [x0, #0xf]
    // 0xb3cac8: ldur            x1, [fp, #-0x10]
    // 0xb3cacc: r0 = of()
    //     0xb3cacc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3cad0: LoadField: r1 = r0->field_87
    //     0xb3cad0: ldur            w1, [x0, #0x87]
    // 0xb3cad4: DecompressPointer r1
    //     0xb3cad4: add             x1, x1, HEAP, lsl #32
    // 0xb3cad8: LoadField: r0 = r1->field_7
    //     0xb3cad8: ldur            w0, [x1, #7]
    // 0xb3cadc: DecompressPointer r0
    //     0xb3cadc: add             x0, x0, HEAP, lsl #32
    // 0xb3cae0: stur            x0, [fp, #-0x20]
    // 0xb3cae4: r1 = Instance_Color
    //     0xb3cae4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3cae8: d0 = 0.700000
    //     0xb3cae8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb3caec: ldr             d0, [x17, #0xf48]
    // 0xb3caf0: r0 = withOpacity()
    //     0xb3caf0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb3caf4: r16 = 14.000000
    //     0xb3caf4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3caf8: ldr             x16, [x16, #0x1d8]
    // 0xb3cafc: stp             x0, x16, [SP]
    // 0xb3cb00: ldur            x1, [fp, #-0x20]
    // 0xb3cb04: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3cb04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3cb08: ldr             x4, [x4, #0xaa0]
    // 0xb3cb0c: r0 = copyWith()
    //     0xb3cb0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3cb10: stur            x0, [fp, #-0x20]
    // 0xb3cb14: r0 = Text()
    //     0xb3cb14: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3cb18: mov             x2, x0
    // 0xb3cb1c: r0 = "Change Delivery Address"
    //     0xb3cb1c: add             x0, PP, #0x54, lsl #12  ; [pp+0x54870] "Change Delivery Address"
    //     0xb3cb20: ldr             x0, [x0, #0x870]
    // 0xb3cb24: stur            x2, [fp, #-0x28]
    // 0xb3cb28: StoreField: r2->field_b = r0
    //     0xb3cb28: stur            w0, [x2, #0xb]
    // 0xb3cb2c: ldur            x0, [fp, #-0x20]
    // 0xb3cb30: StoreField: r2->field_13 = r0
    //     0xb3cb30: stur            w0, [x2, #0x13]
    // 0xb3cb34: ldur            x1, [fp, #-0x10]
    // 0xb3cb38: r0 = of()
    //     0xb3cb38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3cb3c: LoadField: r1 = r0->field_5b
    //     0xb3cb3c: ldur            w1, [x0, #0x5b]
    // 0xb3cb40: DecompressPointer r1
    //     0xb3cb40: add             x1, x1, HEAP, lsl #32
    // 0xb3cb44: stur            x1, [fp, #-0x20]
    // 0xb3cb48: r0 = ColorFilter()
    //     0xb3cb48: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb3cb4c: mov             x1, x0
    // 0xb3cb50: ldur            x0, [fp, #-0x20]
    // 0xb3cb54: stur            x1, [fp, #-0x30]
    // 0xb3cb58: StoreField: r1->field_7 = r0
    //     0xb3cb58: stur            w0, [x1, #7]
    // 0xb3cb5c: r0 = Instance_BlendMode
    //     0xb3cb5c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb3cb60: ldr             x0, [x0, #0xb30]
    // 0xb3cb64: StoreField: r1->field_b = r0
    //     0xb3cb64: stur            w0, [x1, #0xb]
    // 0xb3cb68: r0 = 1
    //     0xb3cb68: movz            x0, #0x1
    // 0xb3cb6c: StoreField: r1->field_13 = r0
    //     0xb3cb6c: stur            x0, [x1, #0x13]
    // 0xb3cb70: r0 = SvgPicture()
    //     0xb3cb70: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb3cb74: stur            x0, [fp, #-0x20]
    // 0xb3cb78: ldur            x16, [fp, #-0x30]
    // 0xb3cb7c: str             x16, [SP]
    // 0xb3cb80: mov             x1, x0
    // 0xb3cb84: r2 = "assets/images/x.svg"
    //     0xb3cb84: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xb3cb88: ldr             x2, [x2, #0x5e8]
    // 0xb3cb8c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb3cb8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb3cb90: ldr             x4, [x4, #0xa38]
    // 0xb3cb94: r0 = SvgPicture.asset()
    //     0xb3cb94: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb3cb98: r0 = InkWell()
    //     0xb3cb98: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb3cb9c: mov             x3, x0
    // 0xb3cba0: ldur            x0, [fp, #-0x20]
    // 0xb3cba4: stur            x3, [fp, #-0x30]
    // 0xb3cba8: StoreField: r3->field_b = r0
    //     0xb3cba8: stur            w0, [x3, #0xb]
    // 0xb3cbac: r1 = Function '<anonymous closure>':.
    //     0xb3cbac: add             x1, PP, #0x56, lsl #12  ; [pp+0x56eb8] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb3cbb0: ldr             x1, [x1, #0xeb8]
    // 0xb3cbb4: r2 = Null
    //     0xb3cbb4: mov             x2, NULL
    // 0xb3cbb8: r0 = AllocateClosure()
    //     0xb3cbb8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3cbbc: mov             x1, x0
    // 0xb3cbc0: ldur            x0, [fp, #-0x30]
    // 0xb3cbc4: StoreField: r0->field_f = r1
    //     0xb3cbc4: stur            w1, [x0, #0xf]
    // 0xb3cbc8: r3 = true
    //     0xb3cbc8: add             x3, NULL, #0x20  ; true
    // 0xb3cbcc: StoreField: r0->field_43 = r3
    //     0xb3cbcc: stur            w3, [x0, #0x43]
    // 0xb3cbd0: r1 = Instance_BoxShape
    //     0xb3cbd0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb3cbd4: ldr             x1, [x1, #0x80]
    // 0xb3cbd8: StoreField: r0->field_47 = r1
    //     0xb3cbd8: stur            w1, [x0, #0x47]
    // 0xb3cbdc: StoreField: r0->field_6f = r3
    //     0xb3cbdc: stur            w3, [x0, #0x6f]
    // 0xb3cbe0: r4 = false
    //     0xb3cbe0: add             x4, NULL, #0x30  ; false
    // 0xb3cbe4: StoreField: r0->field_73 = r4
    //     0xb3cbe4: stur            w4, [x0, #0x73]
    // 0xb3cbe8: StoreField: r0->field_83 = r3
    //     0xb3cbe8: stur            w3, [x0, #0x83]
    // 0xb3cbec: StoreField: r0->field_7b = r4
    //     0xb3cbec: stur            w4, [x0, #0x7b]
    // 0xb3cbf0: r1 = Null
    //     0xb3cbf0: mov             x1, NULL
    // 0xb3cbf4: r2 = 4
    //     0xb3cbf4: movz            x2, #0x4
    // 0xb3cbf8: r0 = AllocateArray()
    //     0xb3cbf8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3cbfc: mov             x2, x0
    // 0xb3cc00: ldur            x0, [fp, #-0x28]
    // 0xb3cc04: stur            x2, [fp, #-0x20]
    // 0xb3cc08: StoreField: r2->field_f = r0
    //     0xb3cc08: stur            w0, [x2, #0xf]
    // 0xb3cc0c: ldur            x0, [fp, #-0x30]
    // 0xb3cc10: StoreField: r2->field_13 = r0
    //     0xb3cc10: stur            w0, [x2, #0x13]
    // 0xb3cc14: r1 = <Widget>
    //     0xb3cc14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3cc18: r0 = AllocateGrowableArray()
    //     0xb3cc18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3cc1c: mov             x1, x0
    // 0xb3cc20: ldur            x0, [fp, #-0x20]
    // 0xb3cc24: stur            x1, [fp, #-0x28]
    // 0xb3cc28: StoreField: r1->field_f = r0
    //     0xb3cc28: stur            w0, [x1, #0xf]
    // 0xb3cc2c: r2 = 4
    //     0xb3cc2c: movz            x2, #0x4
    // 0xb3cc30: StoreField: r1->field_b = r2
    //     0xb3cc30: stur            w2, [x1, #0xb]
    // 0xb3cc34: r0 = Row()
    //     0xb3cc34: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb3cc38: mov             x1, x0
    // 0xb3cc3c: r0 = Instance_Axis
    //     0xb3cc3c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb3cc40: stur            x1, [fp, #-0x20]
    // 0xb3cc44: StoreField: r1->field_f = r0
    //     0xb3cc44: stur            w0, [x1, #0xf]
    // 0xb3cc48: r2 = Instance_MainAxisAlignment
    //     0xb3cc48: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb3cc4c: ldr             x2, [x2, #0xa8]
    // 0xb3cc50: StoreField: r1->field_13 = r2
    //     0xb3cc50: stur            w2, [x1, #0x13]
    // 0xb3cc54: r2 = Instance_MainAxisSize
    //     0xb3cc54: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3cc58: ldr             x2, [x2, #0xa10]
    // 0xb3cc5c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb3cc5c: stur            w2, [x1, #0x17]
    // 0xb3cc60: r3 = Instance_CrossAxisAlignment
    //     0xb3cc60: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb3cc64: ldr             x3, [x3, #0xa18]
    // 0xb3cc68: StoreField: r1->field_1b = r3
    //     0xb3cc68: stur            w3, [x1, #0x1b]
    // 0xb3cc6c: r3 = Instance_VerticalDirection
    //     0xb3cc6c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3cc70: ldr             x3, [x3, #0xa20]
    // 0xb3cc74: StoreField: r1->field_23 = r3
    //     0xb3cc74: stur            w3, [x1, #0x23]
    // 0xb3cc78: r4 = Instance_Clip
    //     0xb3cc78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3cc7c: ldr             x4, [x4, #0x38]
    // 0xb3cc80: StoreField: r1->field_2b = r4
    //     0xb3cc80: stur            w4, [x1, #0x2b]
    // 0xb3cc84: StoreField: r1->field_2f = rZR
    //     0xb3cc84: stur            xzr, [x1, #0x2f]
    // 0xb3cc88: ldur            x5, [fp, #-0x28]
    // 0xb3cc8c: StoreField: r1->field_b = r5
    //     0xb3cc8c: stur            w5, [x1, #0xb]
    // 0xb3cc90: r0 = Padding()
    //     0xb3cc90: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3cc94: mov             x2, x0
    // 0xb3cc98: r0 = Instance_EdgeInsets
    //     0xb3cc98: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb3cc9c: ldr             x0, [x0, #0x668]
    // 0xb3cca0: stur            x2, [fp, #-0x28]
    // 0xb3cca4: StoreField: r2->field_f = r0
    //     0xb3cca4: stur            w0, [x2, #0xf]
    // 0xb3cca8: ldur            x1, [fp, #-0x20]
    // 0xb3ccac: StoreField: r2->field_b = r1
    //     0xb3ccac: stur            w1, [x2, #0xb]
    // 0xb3ccb0: ldur            x1, [fp, #-0x10]
    // 0xb3ccb4: r0 = of()
    //     0xb3ccb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3ccb8: LoadField: r1 = r0->field_87
    //     0xb3ccb8: ldur            w1, [x0, #0x87]
    // 0xb3ccbc: DecompressPointer r1
    //     0xb3ccbc: add             x1, x1, HEAP, lsl #32
    // 0xb3ccc0: LoadField: r0 = r1->field_2b
    //     0xb3ccc0: ldur            w0, [x1, #0x2b]
    // 0xb3ccc4: DecompressPointer r0
    //     0xb3ccc4: add             x0, x0, HEAP, lsl #32
    // 0xb3ccc8: r16 = 12.000000
    //     0xb3ccc8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3cccc: ldr             x16, [x16, #0x9e8]
    // 0xb3ccd0: r30 = Instance_Color
    //     0xb3ccd0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3ccd4: stp             lr, x16, [SP]
    // 0xb3ccd8: mov             x1, x0
    // 0xb3ccdc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3ccdc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3cce0: ldr             x4, [x4, #0xaa0]
    // 0xb3cce4: r0 = copyWith()
    //     0xb3cce4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3cce8: stur            x0, [fp, #-0x20]
    // 0xb3ccec: r0 = Text()
    //     0xb3ccec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3ccf0: mov             x1, x0
    // 0xb3ccf4: r0 = "Full Name*"
    //     0xb3ccf4: add             x0, PP, #0x54, lsl #12  ; [pp+0x54048] "Full Name*"
    //     0xb3ccf8: ldr             x0, [x0, #0x48]
    // 0xb3ccfc: stur            x1, [fp, #-0x30]
    // 0xb3cd00: StoreField: r1->field_b = r0
    //     0xb3cd00: stur            w0, [x1, #0xb]
    // 0xb3cd04: ldur            x0, [fp, #-0x20]
    // 0xb3cd08: StoreField: r1->field_13 = r0
    //     0xb3cd08: stur            w0, [x1, #0x13]
    // 0xb3cd0c: r0 = Padding()
    //     0xb3cd0c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3cd10: mov             x1, x0
    // 0xb3cd14: r0 = Instance_EdgeInsets
    //     0xb3cd14: add             x0, PP, #0x56, lsl #12  ; [pp+0x567e0] Obj!EdgeInsets@d58d31
    //     0xb3cd18: ldr             x0, [x0, #0x7e0]
    // 0xb3cd1c: stur            x1, [fp, #-0x38]
    // 0xb3cd20: StoreField: r1->field_f = r0
    //     0xb3cd20: stur            w0, [x1, #0xf]
    // 0xb3cd24: ldur            x0, [fp, #-0x30]
    // 0xb3cd28: StoreField: r1->field_b = r0
    //     0xb3cd28: stur            w0, [x1, #0xb]
    // 0xb3cd2c: ldur            x2, [fp, #-8]
    // 0xb3cd30: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb3cd30: ldur            w0, [x2, #0x17]
    // 0xb3cd34: DecompressPointer r0
    //     0xb3cd34: add             x0, x0, HEAP, lsl #32
    // 0xb3cd38: stur            x0, [fp, #-0x20]
    // 0xb3cd3c: r16 = "[a-zA-Z ]"
    //     0xb3cd3c: add             x16, PP, #0x54, lsl #12  ; [pp+0x54040] "[a-zA-Z ]"
    //     0xb3cd40: ldr             x16, [x16, #0x40]
    // 0xb3cd44: stp             x16, NULL, [SP, #0x20]
    // 0xb3cd48: r16 = false
    //     0xb3cd48: add             x16, NULL, #0x30  ; false
    // 0xb3cd4c: r30 = true
    //     0xb3cd4c: add             lr, NULL, #0x20  ; true
    // 0xb3cd50: stp             lr, x16, [SP, #0x10]
    // 0xb3cd54: r16 = false
    //     0xb3cd54: add             x16, NULL, #0x30  ; false
    // 0xb3cd58: r30 = false
    //     0xb3cd58: add             lr, NULL, #0x30  ; false
    // 0xb3cd5c: stp             lr, x16, [SP]
    // 0xb3cd60: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xb3cd60: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xb3cd64: r0 = _RegExp()
    //     0xb3cd64: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xb3cd68: stur            x0, [fp, #-0x30]
    // 0xb3cd6c: r0 = FilteringTextInputFormatter()
    //     0xb3cd6c: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xb3cd70: mov             x1, x0
    // 0xb3cd74: ldur            x0, [fp, #-0x30]
    // 0xb3cd78: stur            x1, [fp, #-0x40]
    // 0xb3cd7c: StoreField: r1->field_b = r0
    //     0xb3cd7c: stur            w0, [x1, #0xb]
    // 0xb3cd80: r0 = true
    //     0xb3cd80: add             x0, NULL, #0x20  ; true
    // 0xb3cd84: StoreField: r1->field_7 = r0
    //     0xb3cd84: stur            w0, [x1, #7]
    // 0xb3cd88: r2 = ""
    //     0xb3cd88: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3cd8c: StoreField: r1->field_f = r2
    //     0xb3cd8c: stur            w2, [x1, #0xf]
    // 0xb3cd90: r0 = LengthLimitingTextInputFormatter()
    //     0xb3cd90: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb3cd94: mov             x3, x0
    // 0xb3cd98: r0 = 180
    //     0xb3cd98: movz            x0, #0xb4
    // 0xb3cd9c: stur            x3, [fp, #-0x30]
    // 0xb3cda0: StoreField: r3->field_7 = r0
    //     0xb3cda0: stur            w0, [x3, #7]
    // 0xb3cda4: r1 = Null
    //     0xb3cda4: mov             x1, NULL
    // 0xb3cda8: r2 = 4
    //     0xb3cda8: movz            x2, #0x4
    // 0xb3cdac: r0 = AllocateArray()
    //     0xb3cdac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3cdb0: mov             x2, x0
    // 0xb3cdb4: ldur            x0, [fp, #-0x40]
    // 0xb3cdb8: stur            x2, [fp, #-0x48]
    // 0xb3cdbc: StoreField: r2->field_f = r0
    //     0xb3cdbc: stur            w0, [x2, #0xf]
    // 0xb3cdc0: ldur            x0, [fp, #-0x30]
    // 0xb3cdc4: StoreField: r2->field_13 = r0
    //     0xb3cdc4: stur            w0, [x2, #0x13]
    // 0xb3cdc8: r1 = <TextInputFormatter>
    //     0xb3cdc8: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb3cdcc: ldr             x1, [x1, #0x7b0]
    // 0xb3cdd0: r0 = AllocateGrowableArray()
    //     0xb3cdd0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3cdd4: mov             x2, x0
    // 0xb3cdd8: ldur            x0, [fp, #-0x48]
    // 0xb3cddc: stur            x2, [fp, #-0x40]
    // 0xb3cde0: StoreField: r2->field_f = r0
    //     0xb3cde0: stur            w0, [x2, #0xf]
    // 0xb3cde4: r0 = 4
    //     0xb3cde4: movz            x0, #0x4
    // 0xb3cde8: StoreField: r2->field_b = r0
    //     0xb3cde8: stur            w0, [x2, #0xb]
    // 0xb3cdec: ldur            x3, [fp, #-8]
    // 0xb3cdf0: LoadField: r4 = r3->field_33
    //     0xb3cdf0: ldur            w4, [x3, #0x33]
    // 0xb3cdf4: DecompressPointer r4
    //     0xb3cdf4: add             x4, x4, HEAP, lsl #32
    // 0xb3cdf8: ldur            x1, [fp, #-0x10]
    // 0xb3cdfc: stur            x4, [fp, #-0x30]
    // 0xb3ce00: r0 = of()
    //     0xb3ce00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3ce04: LoadField: r1 = r0->field_87
    //     0xb3ce04: ldur            w1, [x0, #0x87]
    // 0xb3ce08: DecompressPointer r1
    //     0xb3ce08: add             x1, x1, HEAP, lsl #32
    // 0xb3ce0c: LoadField: r0 = r1->field_2b
    //     0xb3ce0c: ldur            w0, [x1, #0x2b]
    // 0xb3ce10: DecompressPointer r0
    //     0xb3ce10: add             x0, x0, HEAP, lsl #32
    // 0xb3ce14: ldur            x1, [fp, #-0x10]
    // 0xb3ce18: stur            x0, [fp, #-0x48]
    // 0xb3ce1c: r0 = of()
    //     0xb3ce1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3ce20: LoadField: r1 = r0->field_5b
    //     0xb3ce20: ldur            w1, [x0, #0x5b]
    // 0xb3ce24: DecompressPointer r1
    //     0xb3ce24: add             x1, x1, HEAP, lsl #32
    // 0xb3ce28: r16 = 14.000000
    //     0xb3ce28: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3ce2c: ldr             x16, [x16, #0x1d8]
    // 0xb3ce30: stp             x16, x1, [SP]
    // 0xb3ce34: ldur            x1, [fp, #-0x48]
    // 0xb3ce38: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb3ce38: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb3ce3c: ldr             x4, [x4, #0x9b8]
    // 0xb3ce40: r0 = copyWith()
    //     0xb3ce40: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3ce44: ldur            x1, [fp, #-0x10]
    // 0xb3ce48: stur            x0, [fp, #-0x48]
    // 0xb3ce4c: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb3ce4c: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb3ce50: ldur            x1, [fp, #-0x10]
    // 0xb3ce54: stur            x0, [fp, #-0x50]
    // 0xb3ce58: r0 = of()
    //     0xb3ce58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3ce5c: LoadField: r1 = r0->field_5b
    //     0xb3ce5c: ldur            w1, [x0, #0x5b]
    // 0xb3ce60: DecompressPointer r1
    //     0xb3ce60: add             x1, x1, HEAP, lsl #32
    // 0xb3ce64: stur            x1, [fp, #-0x58]
    // 0xb3ce68: r0 = BorderSide()
    //     0xb3ce68: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb3ce6c: mov             x1, x0
    // 0xb3ce70: ldur            x0, [fp, #-0x58]
    // 0xb3ce74: stur            x1, [fp, #-0x60]
    // 0xb3ce78: StoreField: r1->field_7 = r0
    //     0xb3ce78: stur            w0, [x1, #7]
    // 0xb3ce7c: d0 = 1.000000
    //     0xb3ce7c: fmov            d0, #1.00000000
    // 0xb3ce80: StoreField: r1->field_b = d0
    //     0xb3ce80: stur            d0, [x1, #0xb]
    // 0xb3ce84: r0 = Instance_BorderStyle
    //     0xb3ce84: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb3ce88: ldr             x0, [x0, #0xf68]
    // 0xb3ce8c: StoreField: r1->field_13 = r0
    //     0xb3ce8c: stur            w0, [x1, #0x13]
    // 0xb3ce90: d1 = -1.000000
    //     0xb3ce90: fmov            d1, #-1.00000000
    // 0xb3ce94: ArrayStore: r1[0] = d1  ; List_8
    //     0xb3ce94: stur            d1, [x1, #0x17]
    // 0xb3ce98: r0 = Radius()
    //     0xb3ce98: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb3ce9c: d0 = 30.000000
    //     0xb3ce9c: fmov            d0, #30.00000000
    // 0xb3cea0: stur            x0, [fp, #-0x58]
    // 0xb3cea4: StoreField: r0->field_7 = d0
    //     0xb3cea4: stur            d0, [x0, #7]
    // 0xb3cea8: StoreField: r0->field_f = d0
    //     0xb3cea8: stur            d0, [x0, #0xf]
    // 0xb3ceac: r0 = BorderRadius()
    //     0xb3ceac: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb3ceb0: mov             x1, x0
    // 0xb3ceb4: ldur            x0, [fp, #-0x58]
    // 0xb3ceb8: stur            x1, [fp, #-0x68]
    // 0xb3cebc: StoreField: r1->field_7 = r0
    //     0xb3cebc: stur            w0, [x1, #7]
    // 0xb3cec0: StoreField: r1->field_b = r0
    //     0xb3cec0: stur            w0, [x1, #0xb]
    // 0xb3cec4: StoreField: r1->field_f = r0
    //     0xb3cec4: stur            w0, [x1, #0xf]
    // 0xb3cec8: StoreField: r1->field_13 = r0
    //     0xb3cec8: stur            w0, [x1, #0x13]
    // 0xb3cecc: r0 = OutlineInputBorder()
    //     0xb3cecc: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xb3ced0: mov             x2, x0
    // 0xb3ced4: ldur            x0, [fp, #-0x68]
    // 0xb3ced8: stur            x2, [fp, #-0x58]
    // 0xb3cedc: StoreField: r2->field_13 = r0
    //     0xb3cedc: stur            w0, [x2, #0x13]
    // 0xb3cee0: d0 = 4.000000
    //     0xb3cee0: fmov            d0, #4.00000000
    // 0xb3cee4: StoreField: r2->field_b = d0
    //     0xb3cee4: stur            d0, [x2, #0xb]
    // 0xb3cee8: ldur            x0, [fp, #-0x60]
    // 0xb3ceec: StoreField: r2->field_7 = r0
    //     0xb3ceec: stur            w0, [x2, #7]
    // 0xb3cef0: ldur            x1, [fp, #-0x10]
    // 0xb3cef4: r0 = of()
    //     0xb3cef4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3cef8: LoadField: r1 = r0->field_87
    //     0xb3cef8: ldur            w1, [x0, #0x87]
    // 0xb3cefc: DecompressPointer r1
    //     0xb3cefc: add             x1, x1, HEAP, lsl #32
    // 0xb3cf00: LoadField: r0 = r1->field_2b
    //     0xb3cf00: ldur            w0, [x1, #0x2b]
    // 0xb3cf04: DecompressPointer r0
    //     0xb3cf04: add             x0, x0, HEAP, lsl #32
    // 0xb3cf08: r16 = 12.000000
    //     0xb3cf08: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3cf0c: ldr             x16, [x16, #0x9e8]
    // 0xb3cf10: r30 = Instance_MaterialColor
    //     0xb3cf10: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb3cf14: ldr             lr, [lr, #0x180]
    // 0xb3cf18: stp             lr, x16, [SP]
    // 0xb3cf1c: mov             x1, x0
    // 0xb3cf20: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3cf20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3cf24: ldr             x4, [x4, #0xaa0]
    // 0xb3cf28: r0 = copyWith()
    //     0xb3cf28: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3cf2c: ldur            x2, [fp, #-8]
    // 0xb3cf30: stur            x0, [fp, #-0x70]
    // 0xb3cf34: LoadField: r1 = r2->field_4f
    //     0xb3cf34: ldur            w1, [x2, #0x4f]
    // 0xb3cf38: DecompressPointer r1
    //     0xb3cf38: add             x1, x1, HEAP, lsl #32
    // 0xb3cf3c: tbnz            w1, #4, #0xb3cfcc
    // 0xb3cf40: LoadField: r1 = r2->field_33
    //     0xb3cf40: ldur            w1, [x2, #0x33]
    // 0xb3cf44: DecompressPointer r1
    //     0xb3cf44: add             x1, x1, HEAP, lsl #32
    // 0xb3cf48: LoadField: r3 = r1->field_27
    //     0xb3cf48: ldur            w3, [x1, #0x27]
    // 0xb3cf4c: DecompressPointer r3
    //     0xb3cf4c: add             x3, x3, HEAP, lsl #32
    // 0xb3cf50: LoadField: r1 = r3->field_7
    //     0xb3cf50: ldur            w1, [x3, #7]
    // 0xb3cf54: DecompressPointer r1
    //     0xb3cf54: add             x1, x1, HEAP, lsl #32
    // 0xb3cf58: LoadField: r3 = r1->field_7
    //     0xb3cf58: ldur            w3, [x1, #7]
    // 0xb3cf5c: cbz             w3, #0xb3cf78
    // 0xb3cf60: r1 = LoadInt32Instr(r3)
    //     0xb3cf60: sbfx            x1, x3, #1, #0x1f
    // 0xb3cf64: cmp             x1, #1
    // 0xb3cf68: b.le            #0xb3cf78
    // 0xb3cf6c: r1 = Instance_IconData
    //     0xb3cf6c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb3cf70: ldr             x1, [x1, #0x130]
    // 0xb3cf74: b               #0xb3cf80
    // 0xb3cf78: r1 = Instance_IconData
    //     0xb3cf78: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb3cf7c: ldr             x1, [x1, #0x138]
    // 0xb3cf80: stur            x1, [fp, #-0x68]
    // 0xb3cf84: cbz             w3, #0xb3cfa0
    // 0xb3cf88: r4 = LoadInt32Instr(r3)
    //     0xb3cf88: sbfx            x4, x3, #1, #0x1f
    // 0xb3cf8c: cmp             x4, #1
    // 0xb3cf90: b.le            #0xb3cfa0
    // 0xb3cf94: r3 = Instance_Color
    //     0xb3cf94: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb3cf98: ldr             x3, [x3, #0x858]
    // 0xb3cf9c: b               #0xb3cfa8
    // 0xb3cfa0: r3 = Instance_Color
    //     0xb3cfa0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb3cfa4: ldr             x3, [x3, #0x50]
    // 0xb3cfa8: stur            x3, [fp, #-0x60]
    // 0xb3cfac: r0 = Icon()
    //     0xb3cfac: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb3cfb0: mov             x1, x0
    // 0xb3cfb4: ldur            x0, [fp, #-0x68]
    // 0xb3cfb8: StoreField: r1->field_b = r0
    //     0xb3cfb8: stur            w0, [x1, #0xb]
    // 0xb3cfbc: ldur            x0, [fp, #-0x60]
    // 0xb3cfc0: StoreField: r1->field_23 = r0
    //     0xb3cfc0: stur            w0, [x1, #0x23]
    // 0xb3cfc4: mov             x3, x1
    // 0xb3cfc8: b               #0xb3cfd0
    // 0xb3cfcc: r3 = Null
    //     0xb3cfcc: mov             x3, NULL
    // 0xb3cfd0: ldur            x2, [fp, #-8]
    // 0xb3cfd4: ldur            x0, [fp, #-0x20]
    // 0xb3cfd8: ldur            x1, [fp, #-0x10]
    // 0xb3cfdc: stur            x3, [fp, #-0x60]
    // 0xb3cfe0: r0 = of()
    //     0xb3cfe0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3cfe4: LoadField: r1 = r0->field_87
    //     0xb3cfe4: ldur            w1, [x0, #0x87]
    // 0xb3cfe8: DecompressPointer r1
    //     0xb3cfe8: add             x1, x1, HEAP, lsl #32
    // 0xb3cfec: LoadField: r0 = r1->field_2b
    //     0xb3cfec: ldur            w0, [x1, #0x2b]
    // 0xb3cff0: DecompressPointer r0
    //     0xb3cff0: add             x0, x0, HEAP, lsl #32
    // 0xb3cff4: r16 = 14.000000
    //     0xb3cff4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3cff8: ldr             x16, [x16, #0x1d8]
    // 0xb3cffc: r30 = Instance_Color
    //     0xb3cffc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3d000: stp             lr, x16, [SP]
    // 0xb3d004: mov             x1, x0
    // 0xb3d008: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3d008: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3d00c: ldr             x4, [x4, #0xaa0]
    // 0xb3d010: r0 = copyWith()
    //     0xb3d010: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3d014: ldur            x16, [fp, #-0x58]
    // 0xb3d018: ldur            lr, [fp, #-0x70]
    // 0xb3d01c: stp             lr, x16, [SP, #0x20]
    // 0xb3d020: r16 = Instance_EdgeInsets
    //     0xb3d020: add             x16, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb3d024: ldr             x16, [x16, #0xa78]
    // 0xb3d028: r30 = "Full Name"
    //     0xb3d028: add             lr, PP, #0x56, lsl #12  ; [pp+0x567e8] "Full Name"
    //     0xb3d02c: ldr             lr, [lr, #0x7e8]
    // 0xb3d030: stp             lr, x16, [SP, #0x10]
    // 0xb3d034: ldur            x16, [fp, #-0x60]
    // 0xb3d038: stp             x0, x16, [SP]
    // 0xb3d03c: ldur            x1, [fp, #-0x50]
    // 0xb3d040: r4 = const [0, 0x7, 0x6, 0x1, contentPadding, 0x3, errorStyle, 0x2, focusedBorder, 0x1, hintStyle, 0x6, hintText, 0x4, suffixIcon, 0x5, null]
    //     0xb3d040: add             x4, PP, #0x56, lsl #12  ; [pp+0x56ec0] List(17) [0, 0x7, 0x6, 0x1, "contentPadding", 0x3, "errorStyle", 0x2, "focusedBorder", 0x1, "hintStyle", 0x6, "hintText", 0x4, "suffixIcon", 0x5, Null]
    //     0xb3d044: ldr             x4, [x4, #0xec0]
    // 0xb3d048: r0 = copyWith()
    //     0xb3d048: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb3d04c: ldur            x2, [fp, #-8]
    // 0xb3d050: r1 = Function '_validateCustomerNameNumber@1537065735':.
    //     0xb3d050: add             x1, PP, #0x56, lsl #12  ; [pp+0x56ec8] AnonymousClosure: (0xb40aac), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateCustomerNameNumber (0xa02760)
    //     0xb3d054: ldr             x1, [x1, #0xec8]
    // 0xb3d058: stur            x0, [fp, #-0x50]
    // 0xb3d05c: r0 = AllocateClosure()
    //     0xb3d05c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3d060: ldur            x2, [fp, #-0x18]
    // 0xb3d064: r1 = Function '<anonymous closure>':.
    //     0xb3d064: add             x1, PP, #0x56, lsl #12  ; [pp+0x56ed0] AnonymousClosure: (0xb40a34), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xb3ca88)
    //     0xb3d068: ldr             x1, [x1, #0xed0]
    // 0xb3d06c: stur            x0, [fp, #-0x58]
    // 0xb3d070: r0 = AllocateClosure()
    //     0xb3d070: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3d074: r1 = <String>
    //     0xb3d074: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb3d078: stur            x0, [fp, #-0x60]
    // 0xb3d07c: r0 = TextFormField()
    //     0xb3d07c: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb3d080: stur            x0, [fp, #-0x68]
    // 0xb3d084: ldur            x16, [fp, #-0x58]
    // 0xb3d088: r30 = true
    //     0xb3d088: add             lr, NULL, #0x20  ; true
    // 0xb3d08c: stp             lr, x16, [SP, #0x40]
    // 0xb3d090: r16 = true
    //     0xb3d090: add             x16, NULL, #0x20  ; true
    // 0xb3d094: r30 = Instance_AutovalidateMode
    //     0xb3d094: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb3d098: ldr             lr, [lr, #0x7e8]
    // 0xb3d09c: stp             lr, x16, [SP, #0x30]
    // 0xb3d0a0: ldur            x16, [fp, #-0x40]
    // 0xb3d0a4: r30 = Instance_TextInputType
    //     0xb3d0a4: add             lr, PP, #0x54, lsl #12  ; [pp+0x54068] Obj!TextInputType@d55ba1
    //     0xb3d0a8: ldr             lr, [lr, #0x68]
    // 0xb3d0ac: stp             lr, x16, [SP, #0x20]
    // 0xb3d0b0: r16 = 2
    //     0xb3d0b0: movz            x16, #0x2
    // 0xb3d0b4: ldur            lr, [fp, #-0x30]
    // 0xb3d0b8: stp             lr, x16, [SP, #0x10]
    // 0xb3d0bc: ldur            x16, [fp, #-0x60]
    // 0xb3d0c0: ldur            lr, [fp, #-0x48]
    // 0xb3d0c4: stp             lr, x16, [SP]
    // 0xb3d0c8: mov             x1, x0
    // 0xb3d0cc: ldur            x2, [fp, #-0x50]
    // 0xb3d0d0: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x4, autovalidateMode, 0x5, controller, 0x9, enableSuggestions, 0x3, inputFormatters, 0x6, keyboardType, 0x7, maxLines, 0x8, onChanged, 0xa, style, 0xb, validator, 0x2, null]
    //     0xb3d0d0: add             x4, PP, #0x56, lsl #12  ; [pp+0x56ed8] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x4, "autovalidateMode", 0x5, "controller", 0x9, "enableSuggestions", 0x3, "inputFormatters", 0x6, "keyboardType", 0x7, "maxLines", 0x8, "onChanged", 0xa, "style", 0xb, "validator", 0x2, Null]
    //     0xb3d0d4: ldr             x4, [x4, #0xed8]
    // 0xb3d0d8: r0 = TextFormField()
    //     0xb3d0d8: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb3d0dc: r0 = Form()
    //     0xb3d0dc: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb3d0e0: mov             x2, x0
    // 0xb3d0e4: ldur            x0, [fp, #-0x68]
    // 0xb3d0e8: stur            x2, [fp, #-0x30]
    // 0xb3d0ec: StoreField: r2->field_b = r0
    //     0xb3d0ec: stur            w0, [x2, #0xb]
    // 0xb3d0f0: r0 = Instance_AutovalidateMode
    //     0xb3d0f0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb3d0f4: ldr             x0, [x0, #0x800]
    // 0xb3d0f8: StoreField: r2->field_23 = r0
    //     0xb3d0f8: stur            w0, [x2, #0x23]
    // 0xb3d0fc: ldur            x1, [fp, #-0x20]
    // 0xb3d100: StoreField: r2->field_7 = r1
    //     0xb3d100: stur            w1, [x2, #7]
    // 0xb3d104: ldur            x1, [fp, #-0x10]
    // 0xb3d108: r0 = of()
    //     0xb3d108: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3d10c: LoadField: r1 = r0->field_87
    //     0xb3d10c: ldur            w1, [x0, #0x87]
    // 0xb3d110: DecompressPointer r1
    //     0xb3d110: add             x1, x1, HEAP, lsl #32
    // 0xb3d114: LoadField: r0 = r1->field_2b
    //     0xb3d114: ldur            w0, [x1, #0x2b]
    // 0xb3d118: DecompressPointer r0
    //     0xb3d118: add             x0, x0, HEAP, lsl #32
    // 0xb3d11c: r16 = 12.000000
    //     0xb3d11c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3d120: ldr             x16, [x16, #0x9e8]
    // 0xb3d124: r30 = Instance_Color
    //     0xb3d124: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3d128: stp             lr, x16, [SP]
    // 0xb3d12c: mov             x1, x0
    // 0xb3d130: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3d130: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3d134: ldr             x4, [x4, #0xaa0]
    // 0xb3d138: r0 = copyWith()
    //     0xb3d138: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3d13c: stur            x0, [fp, #-0x20]
    // 0xb3d140: r0 = Text()
    //     0xb3d140: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3d144: mov             x1, x0
    // 0xb3d148: r0 = "House No./ Building Name*"
    //     0xb3d148: add             x0, PP, #0x54, lsl #12  ; [pp+0x54080] "House No./ Building Name*"
    //     0xb3d14c: ldr             x0, [x0, #0x80]
    // 0xb3d150: stur            x1, [fp, #-0x40]
    // 0xb3d154: StoreField: r1->field_b = r0
    //     0xb3d154: stur            w0, [x1, #0xb]
    // 0xb3d158: ldur            x0, [fp, #-0x20]
    // 0xb3d15c: StoreField: r1->field_13 = r0
    //     0xb3d15c: stur            w0, [x1, #0x13]
    // 0xb3d160: r0 = Padding()
    //     0xb3d160: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3d164: mov             x2, x0
    // 0xb3d168: r0 = Instance_EdgeInsets
    //     0xb3d168: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f4c0] Obj!EdgeInsets@d579e1
    //     0xb3d16c: ldr             x0, [x0, #0x4c0]
    // 0xb3d170: stur            x2, [fp, #-0x48]
    // 0xb3d174: StoreField: r2->field_f = r0
    //     0xb3d174: stur            w0, [x2, #0xf]
    // 0xb3d178: ldur            x1, [fp, #-0x40]
    // 0xb3d17c: StoreField: r2->field_b = r1
    //     0xb3d17c: stur            w1, [x2, #0xb]
    // 0xb3d180: ldur            x3, [fp, #-8]
    // 0xb3d184: LoadField: r4 = r3->field_13
    //     0xb3d184: ldur            w4, [x3, #0x13]
    // 0xb3d188: DecompressPointer r4
    //     0xb3d188: add             x4, x4, HEAP, lsl #32
    // 0xb3d18c: ldur            x1, [fp, #-0x10]
    // 0xb3d190: stur            x4, [fp, #-0x20]
    // 0xb3d194: r0 = of()
    //     0xb3d194: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3d198: LoadField: r1 = r0->field_87
    //     0xb3d198: ldur            w1, [x0, #0x87]
    // 0xb3d19c: DecompressPointer r1
    //     0xb3d19c: add             x1, x1, HEAP, lsl #32
    // 0xb3d1a0: LoadField: r0 = r1->field_2b
    //     0xb3d1a0: ldur            w0, [x1, #0x2b]
    // 0xb3d1a4: DecompressPointer r0
    //     0xb3d1a4: add             x0, x0, HEAP, lsl #32
    // 0xb3d1a8: ldur            x1, [fp, #-0x10]
    // 0xb3d1ac: stur            x0, [fp, #-0x40]
    // 0xb3d1b0: r0 = of()
    //     0xb3d1b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3d1b4: LoadField: r1 = r0->field_5b
    //     0xb3d1b4: ldur            w1, [x0, #0x5b]
    // 0xb3d1b8: DecompressPointer r1
    //     0xb3d1b8: add             x1, x1, HEAP, lsl #32
    // 0xb3d1bc: r16 = 14.000000
    //     0xb3d1bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3d1c0: ldr             x16, [x16, #0x1d8]
    // 0xb3d1c4: stp             x16, x1, [SP]
    // 0xb3d1c8: ldur            x1, [fp, #-0x40]
    // 0xb3d1cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb3d1cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb3d1d0: ldr             x4, [x4, #0x9b8]
    // 0xb3d1d4: r0 = copyWith()
    //     0xb3d1d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3d1d8: stur            x0, [fp, #-0x40]
    // 0xb3d1dc: r0 = InitLateStaticField(0xa94) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::singleLineFormatter
    //     0xb3d1dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb3d1e0: ldr             x0, [x0, #0x1528]
    //     0xb3d1e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb3d1e8: cmp             w0, w16
    //     0xb3d1ec: b.ne            #0xb3d1fc
    //     0xb3d1f0: add             x2, PP, #0x54, lsl #12  ; [pp+0x54078] Field <FilteringTextInputFormatter.singleLineFormatter>: static late final (offset: 0xa94)
    //     0xb3d1f4: ldr             x2, [x2, #0x78]
    //     0xb3d1f8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb3d1fc: stur            x0, [fp, #-0x50]
    // 0xb3d200: r0 = LengthLimitingTextInputFormatter()
    //     0xb3d200: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb3d204: mov             x3, x0
    // 0xb3d208: r0 = 240
    //     0xb3d208: movz            x0, #0xf0
    // 0xb3d20c: stur            x3, [fp, #-0x58]
    // 0xb3d210: StoreField: r3->field_7 = r0
    //     0xb3d210: stur            w0, [x3, #7]
    // 0xb3d214: r1 = Null
    //     0xb3d214: mov             x1, NULL
    // 0xb3d218: r2 = 4
    //     0xb3d218: movz            x2, #0x4
    // 0xb3d21c: r0 = AllocateArray()
    //     0xb3d21c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3d220: mov             x2, x0
    // 0xb3d224: ldur            x0, [fp, #-0x50]
    // 0xb3d228: stur            x2, [fp, #-0x60]
    // 0xb3d22c: StoreField: r2->field_f = r0
    //     0xb3d22c: stur            w0, [x2, #0xf]
    // 0xb3d230: ldur            x0, [fp, #-0x58]
    // 0xb3d234: StoreField: r2->field_13 = r0
    //     0xb3d234: stur            w0, [x2, #0x13]
    // 0xb3d238: r1 = <TextInputFormatter>
    //     0xb3d238: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb3d23c: ldr             x1, [x1, #0x7b0]
    // 0xb3d240: r0 = AllocateGrowableArray()
    //     0xb3d240: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3d244: mov             x2, x0
    // 0xb3d248: ldur            x0, [fp, #-0x60]
    // 0xb3d24c: stur            x2, [fp, #-0x58]
    // 0xb3d250: StoreField: r2->field_f = r0
    //     0xb3d250: stur            w0, [x2, #0xf]
    // 0xb3d254: r0 = 4
    //     0xb3d254: movz            x0, #0x4
    // 0xb3d258: StoreField: r2->field_b = r0
    //     0xb3d258: stur            w0, [x2, #0xb]
    // 0xb3d25c: ldur            x3, [fp, #-8]
    // 0xb3d260: LoadField: r4 = r3->field_3b
    //     0xb3d260: ldur            w4, [x3, #0x3b]
    // 0xb3d264: DecompressPointer r4
    //     0xb3d264: add             x4, x4, HEAP, lsl #32
    // 0xb3d268: ldur            x1, [fp, #-0x10]
    // 0xb3d26c: stur            x4, [fp, #-0x50]
    // 0xb3d270: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb3d270: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb3d274: ldur            x1, [fp, #-0x10]
    // 0xb3d278: stur            x0, [fp, #-0x60]
    // 0xb3d27c: r0 = of()
    //     0xb3d27c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3d280: LoadField: r1 = r0->field_5b
    //     0xb3d280: ldur            w1, [x0, #0x5b]
    // 0xb3d284: DecompressPointer r1
    //     0xb3d284: add             x1, x1, HEAP, lsl #32
    // 0xb3d288: stur            x1, [fp, #-0x68]
    // 0xb3d28c: r0 = BorderSide()
    //     0xb3d28c: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb3d290: mov             x1, x0
    // 0xb3d294: ldur            x0, [fp, #-0x68]
    // 0xb3d298: stur            x1, [fp, #-0x70]
    // 0xb3d29c: StoreField: r1->field_7 = r0
    //     0xb3d29c: stur            w0, [x1, #7]
    // 0xb3d2a0: d0 = 1.000000
    //     0xb3d2a0: fmov            d0, #1.00000000
    // 0xb3d2a4: StoreField: r1->field_b = d0
    //     0xb3d2a4: stur            d0, [x1, #0xb]
    // 0xb3d2a8: r0 = Instance_BorderStyle
    //     0xb3d2a8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb3d2ac: ldr             x0, [x0, #0xf68]
    // 0xb3d2b0: StoreField: r1->field_13 = r0
    //     0xb3d2b0: stur            w0, [x1, #0x13]
    // 0xb3d2b4: d1 = -1.000000
    //     0xb3d2b4: fmov            d1, #-1.00000000
    // 0xb3d2b8: ArrayStore: r1[0] = d1  ; List_8
    //     0xb3d2b8: stur            d1, [x1, #0x17]
    // 0xb3d2bc: r0 = Radius()
    //     0xb3d2bc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb3d2c0: d0 = 30.000000
    //     0xb3d2c0: fmov            d0, #30.00000000
    // 0xb3d2c4: stur            x0, [fp, #-0x68]
    // 0xb3d2c8: StoreField: r0->field_7 = d0
    //     0xb3d2c8: stur            d0, [x0, #7]
    // 0xb3d2cc: StoreField: r0->field_f = d0
    //     0xb3d2cc: stur            d0, [x0, #0xf]
    // 0xb3d2d0: r0 = BorderRadius()
    //     0xb3d2d0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb3d2d4: mov             x1, x0
    // 0xb3d2d8: ldur            x0, [fp, #-0x68]
    // 0xb3d2dc: stur            x1, [fp, #-0x78]
    // 0xb3d2e0: StoreField: r1->field_7 = r0
    //     0xb3d2e0: stur            w0, [x1, #7]
    // 0xb3d2e4: StoreField: r1->field_b = r0
    //     0xb3d2e4: stur            w0, [x1, #0xb]
    // 0xb3d2e8: StoreField: r1->field_f = r0
    //     0xb3d2e8: stur            w0, [x1, #0xf]
    // 0xb3d2ec: StoreField: r1->field_13 = r0
    //     0xb3d2ec: stur            w0, [x1, #0x13]
    // 0xb3d2f0: r0 = OutlineInputBorder()
    //     0xb3d2f0: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xb3d2f4: mov             x2, x0
    // 0xb3d2f8: ldur            x0, [fp, #-0x78]
    // 0xb3d2fc: stur            x2, [fp, #-0x68]
    // 0xb3d300: StoreField: r2->field_13 = r0
    //     0xb3d300: stur            w0, [x2, #0x13]
    // 0xb3d304: d0 = 4.000000
    //     0xb3d304: fmov            d0, #4.00000000
    // 0xb3d308: StoreField: r2->field_b = d0
    //     0xb3d308: stur            d0, [x2, #0xb]
    // 0xb3d30c: ldur            x0, [fp, #-0x70]
    // 0xb3d310: StoreField: r2->field_7 = r0
    //     0xb3d310: stur            w0, [x2, #7]
    // 0xb3d314: ldur            x1, [fp, #-0x10]
    // 0xb3d318: r0 = of()
    //     0xb3d318: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3d31c: LoadField: r1 = r0->field_87
    //     0xb3d31c: ldur            w1, [x0, #0x87]
    // 0xb3d320: DecompressPointer r1
    //     0xb3d320: add             x1, x1, HEAP, lsl #32
    // 0xb3d324: LoadField: r0 = r1->field_2b
    //     0xb3d324: ldur            w0, [x1, #0x2b]
    // 0xb3d328: DecompressPointer r0
    //     0xb3d328: add             x0, x0, HEAP, lsl #32
    // 0xb3d32c: stur            x0, [fp, #-0x70]
    // 0xb3d330: r1 = Instance_Color
    //     0xb3d330: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3d334: d0 = 0.400000
    //     0xb3d334: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb3d338: r0 = withOpacity()
    //     0xb3d338: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb3d33c: r16 = 14.000000
    //     0xb3d33c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3d340: ldr             x16, [x16, #0x1d8]
    // 0xb3d344: stp             x0, x16, [SP]
    // 0xb3d348: ldur            x1, [fp, #-0x70]
    // 0xb3d34c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3d34c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3d350: ldr             x4, [x4, #0xaa0]
    // 0xb3d354: r0 = copyWith()
    //     0xb3d354: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3d358: ldur            x1, [fp, #-0x10]
    // 0xb3d35c: stur            x0, [fp, #-0x70]
    // 0xb3d360: r0 = of()
    //     0xb3d360: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3d364: LoadField: r1 = r0->field_87
    //     0xb3d364: ldur            w1, [x0, #0x87]
    // 0xb3d368: DecompressPointer r1
    //     0xb3d368: add             x1, x1, HEAP, lsl #32
    // 0xb3d36c: LoadField: r0 = r1->field_2b
    //     0xb3d36c: ldur            w0, [x1, #0x2b]
    // 0xb3d370: DecompressPointer r0
    //     0xb3d370: add             x0, x0, HEAP, lsl #32
    // 0xb3d374: r16 = 12.000000
    //     0xb3d374: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3d378: ldr             x16, [x16, #0x9e8]
    // 0xb3d37c: r30 = Instance_MaterialColor
    //     0xb3d37c: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb3d380: ldr             lr, [lr, #0x180]
    // 0xb3d384: stp             lr, x16, [SP]
    // 0xb3d388: mov             x1, x0
    // 0xb3d38c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3d38c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3d390: ldr             x4, [x4, #0xaa0]
    // 0xb3d394: r0 = copyWith()
    //     0xb3d394: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3d398: ldur            x2, [fp, #-8]
    // 0xb3d39c: stur            x0, [fp, #-0x88]
    // 0xb3d3a0: LoadField: r1 = r2->field_53
    //     0xb3d3a0: ldur            w1, [x2, #0x53]
    // 0xb3d3a4: DecompressPointer r1
    //     0xb3d3a4: add             x1, x1, HEAP, lsl #32
    // 0xb3d3a8: tbnz            w1, #4, #0xb3d41c
    // 0xb3d3ac: LoadField: r1 = r2->field_3b
    //     0xb3d3ac: ldur            w1, [x2, #0x3b]
    // 0xb3d3b0: DecompressPointer r1
    //     0xb3d3b0: add             x1, x1, HEAP, lsl #32
    // 0xb3d3b4: LoadField: r3 = r1->field_27
    //     0xb3d3b4: ldur            w3, [x1, #0x27]
    // 0xb3d3b8: DecompressPointer r3
    //     0xb3d3b8: add             x3, x3, HEAP, lsl #32
    // 0xb3d3bc: LoadField: r1 = r3->field_7
    //     0xb3d3bc: ldur            w1, [x3, #7]
    // 0xb3d3c0: DecompressPointer r1
    //     0xb3d3c0: add             x1, x1, HEAP, lsl #32
    // 0xb3d3c4: LoadField: r3 = r1->field_7
    //     0xb3d3c4: ldur            w3, [x1, #7]
    // 0xb3d3c8: cbz             w3, #0xb3d3d8
    // 0xb3d3cc: r1 = Instance_IconData
    //     0xb3d3cc: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb3d3d0: ldr             x1, [x1, #0x130]
    // 0xb3d3d4: b               #0xb3d3e0
    // 0xb3d3d8: r1 = Instance_IconData
    //     0xb3d3d8: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb3d3dc: ldr             x1, [x1, #0x138]
    // 0xb3d3e0: stur            x1, [fp, #-0x80]
    // 0xb3d3e4: cbz             w3, #0xb3d3f4
    // 0xb3d3e8: r3 = Instance_Color
    //     0xb3d3e8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb3d3ec: ldr             x3, [x3, #0x858]
    // 0xb3d3f0: b               #0xb3d3fc
    // 0xb3d3f4: r3 = Instance_Color
    //     0xb3d3f4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb3d3f8: ldr             x3, [x3, #0x50]
    // 0xb3d3fc: stur            x3, [fp, #-0x78]
    // 0xb3d400: r0 = Icon()
    //     0xb3d400: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb3d404: mov             x1, x0
    // 0xb3d408: ldur            x0, [fp, #-0x80]
    // 0xb3d40c: StoreField: r1->field_b = r0
    //     0xb3d40c: stur            w0, [x1, #0xb]
    // 0xb3d410: ldur            x0, [fp, #-0x78]
    // 0xb3d414: StoreField: r1->field_23 = r0
    //     0xb3d414: stur            w0, [x1, #0x23]
    // 0xb3d418: b               #0xb3d420
    // 0xb3d41c: r1 = Null
    //     0xb3d41c: mov             x1, NULL
    // 0xb3d420: ldur            x2, [fp, #-8]
    // 0xb3d424: ldur            x0, [fp, #-0x48]
    // 0xb3d428: ldur            x3, [fp, #-0x20]
    // 0xb3d42c: ldur            x16, [fp, #-0x68]
    // 0xb3d430: r30 = Instance_EdgeInsets
    //     0xb3d430: add             lr, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb3d434: ldr             lr, [lr, #0xa78]
    // 0xb3d438: stp             lr, x16, [SP, #0x28]
    // 0xb3d43c: r16 = "House No./ Building Name*"
    //     0xb3d43c: add             x16, PP, #0x54, lsl #12  ; [pp+0x54080] "House No./ Building Name*"
    //     0xb3d440: ldr             x16, [x16, #0x80]
    // 0xb3d444: r30 = 4
    //     0xb3d444: movz            lr, #0x4
    // 0xb3d448: stp             lr, x16, [SP, #0x18]
    // 0xb3d44c: ldur            x16, [fp, #-0x70]
    // 0xb3d450: ldur            lr, [fp, #-0x88]
    // 0xb3d454: stp             lr, x16, [SP, #8]
    // 0xb3d458: str             x1, [SP]
    // 0xb3d45c: ldur            x1, [fp, #-0x60]
    // 0xb3d460: r4 = const [0, 0x8, 0x7, 0x1, contentPadding, 0x2, errorMaxLines, 0x4, errorStyle, 0x6, focusedBorder, 0x1, hintStyle, 0x5, hintText, 0x3, suffixIcon, 0x7, null]
    //     0xb3d460: add             x4, PP, #0x56, lsl #12  ; [pp+0x56ee0] List(19) [0, 0x8, 0x7, 0x1, "contentPadding", 0x2, "errorMaxLines", 0x4, "errorStyle", 0x6, "focusedBorder", 0x1, "hintStyle", 0x5, "hintText", 0x3, "suffixIcon", 0x7, Null]
    //     0xb3d464: ldr             x4, [x4, #0xee0]
    // 0xb3d468: r0 = copyWith()
    //     0xb3d468: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb3d46c: ldur            x2, [fp, #-8]
    // 0xb3d470: r1 = Function '_validateHouseNumber@1537065735':.
    //     0xb3d470: add             x1, PP, #0x56, lsl #12  ; [pp+0x56ee8] AnonymousClosure: (0xb409f8), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateHouseNumber (0xa025b4)
    //     0xb3d474: ldr             x1, [x1, #0xee8]
    // 0xb3d478: stur            x0, [fp, #-0x60]
    // 0xb3d47c: r0 = AllocateClosure()
    //     0xb3d47c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3d480: ldur            x2, [fp, #-0x18]
    // 0xb3d484: r1 = Function '<anonymous closure>':.
    //     0xb3d484: add             x1, PP, #0x56, lsl #12  ; [pp+0x56ef0] AnonymousClosure: (0xb40980), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xb3ca88)
    //     0xb3d488: ldr             x1, [x1, #0xef0]
    // 0xb3d48c: stur            x0, [fp, #-0x68]
    // 0xb3d490: r0 = AllocateClosure()
    //     0xb3d490: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3d494: r1 = <String>
    //     0xb3d494: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb3d498: stur            x0, [fp, #-0x70]
    // 0xb3d49c: r0 = TextFormField()
    //     0xb3d49c: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb3d4a0: stur            x0, [fp, #-0x78]
    // 0xb3d4a4: ldur            x16, [fp, #-0x68]
    // 0xb3d4a8: r30 = Instance_AutovalidateMode
    //     0xb3d4a8: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb3d4ac: ldr             lr, [lr, #0x7e8]
    // 0xb3d4b0: stp             lr, x16, [SP, #0x40]
    // 0xb3d4b4: r16 = true
    //     0xb3d4b4: add             x16, NULL, #0x20  ; true
    // 0xb3d4b8: r30 = true
    //     0xb3d4b8: add             lr, NULL, #0x20  ; true
    // 0xb3d4bc: stp             lr, x16, [SP, #0x30]
    // 0xb3d4c0: ldur            x16, [fp, #-0x40]
    // 0xb3d4c4: ldur            lr, [fp, #-0x58]
    // 0xb3d4c8: stp             lr, x16, [SP, #0x20]
    // 0xb3d4cc: r16 = Instance_TextInputType
    //     0xb3d4cc: add             x16, PP, #0x33, lsl #12  ; [pp+0x33bb8] Obj!TextInputType@d55b41
    //     0xb3d4d0: ldr             x16, [x16, #0xbb8]
    // 0xb3d4d4: ldur            lr, [fp, #-0x50]
    // 0xb3d4d8: stp             lr, x16, [SP, #0x10]
    // 0xb3d4dc: r16 = 2
    //     0xb3d4dc: movz            x16, #0x2
    // 0xb3d4e0: ldur            lr, [fp, #-0x70]
    // 0xb3d4e4: stp             lr, x16, [SP]
    // 0xb3d4e8: mov             x1, x0
    // 0xb3d4ec: ldur            x2, [fp, #-0x60]
    // 0xb3d4f0: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x5, autovalidateMode, 0x3, controller, 0x9, enableSuggestions, 0x4, inputFormatters, 0x7, keyboardType, 0x8, maxLines, 0xa, onChanged, 0xb, style, 0x6, validator, 0x2, null]
    //     0xb3d4f0: add             x4, PP, #0x56, lsl #12  ; [pp+0x56820] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x5, "autovalidateMode", 0x3, "controller", 0x9, "enableSuggestions", 0x4, "inputFormatters", 0x7, "keyboardType", 0x8, "maxLines", 0xa, "onChanged", 0xb, "style", 0x6, "validator", 0x2, Null]
    //     0xb3d4f4: ldr             x4, [x4, #0x820]
    // 0xb3d4f8: r0 = TextFormField()
    //     0xb3d4f8: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb3d4fc: r0 = Form()
    //     0xb3d4fc: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb3d500: mov             x3, x0
    // 0xb3d504: ldur            x0, [fp, #-0x78]
    // 0xb3d508: stur            x3, [fp, #-0x40]
    // 0xb3d50c: StoreField: r3->field_b = r0
    //     0xb3d50c: stur            w0, [x3, #0xb]
    // 0xb3d510: r0 = Instance_AutovalidateMode
    //     0xb3d510: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb3d514: ldr             x0, [x0, #0x800]
    // 0xb3d518: StoreField: r3->field_23 = r0
    //     0xb3d518: stur            w0, [x3, #0x23]
    // 0xb3d51c: ldur            x1, [fp, #-0x20]
    // 0xb3d520: StoreField: r3->field_7 = r1
    //     0xb3d520: stur            w1, [x3, #7]
    // 0xb3d524: r1 = Null
    //     0xb3d524: mov             x1, NULL
    // 0xb3d528: r2 = 4
    //     0xb3d528: movz            x2, #0x4
    // 0xb3d52c: r0 = AllocateArray()
    //     0xb3d52c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3d530: mov             x2, x0
    // 0xb3d534: ldur            x0, [fp, #-0x48]
    // 0xb3d538: stur            x2, [fp, #-0x20]
    // 0xb3d53c: StoreField: r2->field_f = r0
    //     0xb3d53c: stur            w0, [x2, #0xf]
    // 0xb3d540: ldur            x0, [fp, #-0x40]
    // 0xb3d544: StoreField: r2->field_13 = r0
    //     0xb3d544: stur            w0, [x2, #0x13]
    // 0xb3d548: r1 = <Widget>
    //     0xb3d548: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3d54c: r0 = AllocateGrowableArray()
    //     0xb3d54c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3d550: mov             x1, x0
    // 0xb3d554: ldur            x0, [fp, #-0x20]
    // 0xb3d558: stur            x1, [fp, #-0x40]
    // 0xb3d55c: StoreField: r1->field_f = r0
    //     0xb3d55c: stur            w0, [x1, #0xf]
    // 0xb3d560: r2 = 4
    //     0xb3d560: movz            x2, #0x4
    // 0xb3d564: StoreField: r1->field_b = r2
    //     0xb3d564: stur            w2, [x1, #0xb]
    // 0xb3d568: r0 = Column()
    //     0xb3d568: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3d56c: mov             x1, x0
    // 0xb3d570: r0 = Instance_Axis
    //     0xb3d570: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3d574: stur            x1, [fp, #-0x20]
    // 0xb3d578: StoreField: r1->field_f = r0
    //     0xb3d578: stur            w0, [x1, #0xf]
    // 0xb3d57c: r2 = Instance_MainAxisAlignment
    //     0xb3d57c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3d580: ldr             x2, [x2, #0xa08]
    // 0xb3d584: StoreField: r1->field_13 = r2
    //     0xb3d584: stur            w2, [x1, #0x13]
    // 0xb3d588: r3 = Instance_MainAxisSize
    //     0xb3d588: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3d58c: ldr             x3, [x3, #0xa10]
    // 0xb3d590: ArrayStore: r1[0] = r3  ; List_4
    //     0xb3d590: stur            w3, [x1, #0x17]
    // 0xb3d594: r4 = Instance_CrossAxisAlignment
    //     0xb3d594: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3d598: ldr             x4, [x4, #0x890]
    // 0xb3d59c: StoreField: r1->field_1b = r4
    //     0xb3d59c: stur            w4, [x1, #0x1b]
    // 0xb3d5a0: r5 = Instance_VerticalDirection
    //     0xb3d5a0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3d5a4: ldr             x5, [x5, #0xa20]
    // 0xb3d5a8: StoreField: r1->field_23 = r5
    //     0xb3d5a8: stur            w5, [x1, #0x23]
    // 0xb3d5ac: r6 = Instance_Clip
    //     0xb3d5ac: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3d5b0: ldr             x6, [x6, #0x38]
    // 0xb3d5b4: StoreField: r1->field_2b = r6
    //     0xb3d5b4: stur            w6, [x1, #0x2b]
    // 0xb3d5b8: StoreField: r1->field_2f = rZR
    //     0xb3d5b8: stur            xzr, [x1, #0x2f]
    // 0xb3d5bc: ldur            x7, [fp, #-0x40]
    // 0xb3d5c0: StoreField: r1->field_b = r7
    //     0xb3d5c0: stur            w7, [x1, #0xb]
    // 0xb3d5c4: r0 = Padding()
    //     0xb3d5c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3d5c8: mov             x2, x0
    // 0xb3d5cc: r0 = Instance_EdgeInsets
    //     0xb3d5cc: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xb3d5d0: ldr             x0, [x0, #0x858]
    // 0xb3d5d4: stur            x2, [fp, #-0x40]
    // 0xb3d5d8: StoreField: r2->field_f = r0
    //     0xb3d5d8: stur            w0, [x2, #0xf]
    // 0xb3d5dc: ldur            x1, [fp, #-0x20]
    // 0xb3d5e0: StoreField: r2->field_b = r1
    //     0xb3d5e0: stur            w1, [x2, #0xb]
    // 0xb3d5e4: ldur            x1, [fp, #-0x10]
    // 0xb3d5e8: r0 = of()
    //     0xb3d5e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3d5ec: LoadField: r1 = r0->field_87
    //     0xb3d5ec: ldur            w1, [x0, #0x87]
    // 0xb3d5f0: DecompressPointer r1
    //     0xb3d5f0: add             x1, x1, HEAP, lsl #32
    // 0xb3d5f4: LoadField: r0 = r1->field_2b
    //     0xb3d5f4: ldur            w0, [x1, #0x2b]
    // 0xb3d5f8: DecompressPointer r0
    //     0xb3d5f8: add             x0, x0, HEAP, lsl #32
    // 0xb3d5fc: r16 = 12.000000
    //     0xb3d5fc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3d600: ldr             x16, [x16, #0x9e8]
    // 0xb3d604: r30 = Instance_Color
    //     0xb3d604: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3d608: stp             lr, x16, [SP]
    // 0xb3d60c: mov             x1, x0
    // 0xb3d610: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3d610: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3d614: ldr             x4, [x4, #0xaa0]
    // 0xb3d618: r0 = copyWith()
    //     0xb3d618: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3d61c: stur            x0, [fp, #-0x20]
    // 0xb3d620: r0 = Text()
    //     0xb3d620: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3d624: mov             x1, x0
    // 0xb3d628: r0 = "Road Name / Area / Colony*"
    //     0xb3d628: add             x0, PP, #0x54, lsl #12  ; [pp+0x540b0] "Road Name / Area / Colony*"
    //     0xb3d62c: ldr             x0, [x0, #0xb0]
    // 0xb3d630: stur            x1, [fp, #-0x48]
    // 0xb3d634: StoreField: r1->field_b = r0
    //     0xb3d634: stur            w0, [x1, #0xb]
    // 0xb3d638: ldur            x0, [fp, #-0x20]
    // 0xb3d63c: StoreField: r1->field_13 = r0
    //     0xb3d63c: stur            w0, [x1, #0x13]
    // 0xb3d640: r0 = Padding()
    //     0xb3d640: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3d644: mov             x3, x0
    // 0xb3d648: r0 = Instance_EdgeInsets
    //     0xb3d648: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!EdgeInsets@d577d1
    //     0xb3d64c: ldr             x0, [x0, #0xb28]
    // 0xb3d650: stur            x3, [fp, #-0x50]
    // 0xb3d654: StoreField: r3->field_f = r0
    //     0xb3d654: stur            w0, [x3, #0xf]
    // 0xb3d658: ldur            x0, [fp, #-0x48]
    // 0xb3d65c: StoreField: r3->field_b = r0
    //     0xb3d65c: stur            w0, [x3, #0xb]
    // 0xb3d660: ldur            x4, [fp, #-8]
    // 0xb3d664: LoadField: r5 = r4->field_2f
    //     0xb3d664: ldur            w5, [x4, #0x2f]
    // 0xb3d668: DecompressPointer r5
    //     0xb3d668: add             x5, x5, HEAP, lsl #32
    // 0xb3d66c: stur            x5, [fp, #-0x20]
    // 0xb3d670: LoadField: r0 = r4->field_b
    //     0xb3d670: ldur            w0, [x4, #0xb]
    // 0xb3d674: DecompressPointer r0
    //     0xb3d674: add             x0, x0, HEAP, lsl #32
    // 0xb3d678: cmp             w0, NULL
    // 0xb3d67c: b.eq            #0xb3fd84
    // 0xb3d680: LoadField: r1 = r0->field_b
    //     0xb3d680: ldur            w1, [x0, #0xb]
    // 0xb3d684: DecompressPointer r1
    //     0xb3d684: add             x1, x1, HEAP, lsl #32
    // 0xb3d688: LoadField: r0 = r1->field_f
    //     0xb3d688: ldur            w0, [x1, #0xf]
    // 0xb3d68c: DecompressPointer r0
    //     0xb3d68c: add             x0, x0, HEAP, lsl #32
    // 0xb3d690: cmp             w0, NULL
    // 0xb3d694: b.ne            #0xb3d6a0
    // 0xb3d698: r0 = Null
    //     0xb3d698: mov             x0, NULL
    // 0xb3d69c: b               #0xb3d6cc
    // 0xb3d6a0: r1 = LoadClassIdInstr(r0)
    //     0xb3d6a0: ldur            x1, [x0, #-1]
    //     0xb3d6a4: ubfx            x1, x1, #0xc, #0x14
    // 0xb3d6a8: mov             x16, x0
    // 0xb3d6ac: mov             x0, x1
    // 0xb3d6b0: mov             x1, x16
    // 0xb3d6b4: r2 = "landmark"
    //     0xb3d6b4: add             x2, PP, #0x24, lsl #12  ; [pp+0x24930] "landmark"
    //     0xb3d6b8: ldr             x2, [x2, #0x930]
    // 0xb3d6bc: r0 = GDT[cid_x0 + 0xe437]()
    //     0xb3d6bc: movz            x17, #0xe437
    //     0xb3d6c0: add             lr, x0, x17
    //     0xb3d6c4: ldr             lr, [x21, lr, lsl #3]
    //     0xb3d6c8: blr             lr
    // 0xb3d6cc: cmp             w0, NULL
    // 0xb3d6d0: b.eq            #0xb3d6e0
    // 0xb3d6d4: tbnz            w0, #4, #0xb3d6e0
    // 0xb3d6d8: r0 = 250
    //     0xb3d6d8: movz            x0, #0xfa
    // 0xb3d6dc: b               #0xb3d6e4
    // 0xb3d6e0: r0 = 370
    //     0xb3d6e0: movz            x0, #0x172
    // 0xb3d6e4: ldur            x2, [fp, #-8]
    // 0xb3d6e8: lsl             x1, x0, #1
    // 0xb3d6ec: stur            x1, [fp, #-0x48]
    // 0xb3d6f0: r0 = LengthLimitingTextInputFormatter()
    //     0xb3d6f0: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb3d6f4: mov             x1, x0
    // 0xb3d6f8: ldur            x0, [fp, #-0x48]
    // 0xb3d6fc: stur            x1, [fp, #-0x58]
    // 0xb3d700: StoreField: r1->field_7 = r0
    //     0xb3d700: stur            w0, [x1, #7]
    // 0xb3d704: r16 = "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xb3d704: add             x16, PP, #0x54, lsl #12  ; [pp+0x540a8] "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xb3d708: ldr             x16, [x16, #0xa8]
    // 0xb3d70c: stp             x16, NULL, [SP, #0x20]
    // 0xb3d710: r16 = false
    //     0xb3d710: add             x16, NULL, #0x30  ; false
    // 0xb3d714: r30 = true
    //     0xb3d714: add             lr, NULL, #0x20  ; true
    // 0xb3d718: stp             lr, x16, [SP, #0x10]
    // 0xb3d71c: r16 = false
    //     0xb3d71c: add             x16, NULL, #0x30  ; false
    // 0xb3d720: r30 = false
    //     0xb3d720: add             lr, NULL, #0x30  ; false
    // 0xb3d724: stp             lr, x16, [SP]
    // 0xb3d728: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xb3d728: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xb3d72c: r0 = _RegExp()
    //     0xb3d72c: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xb3d730: stur            x0, [fp, #-0x48]
    // 0xb3d734: r0 = FilteringTextInputFormatter()
    //     0xb3d734: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xb3d738: mov             x3, x0
    // 0xb3d73c: ldur            x0, [fp, #-0x48]
    // 0xb3d740: stur            x3, [fp, #-0x60]
    // 0xb3d744: StoreField: r3->field_b = r0
    //     0xb3d744: stur            w0, [x3, #0xb]
    // 0xb3d748: r0 = true
    //     0xb3d748: add             x0, NULL, #0x20  ; true
    // 0xb3d74c: StoreField: r3->field_7 = r0
    //     0xb3d74c: stur            w0, [x3, #7]
    // 0xb3d750: r4 = ""
    //     0xb3d750: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3d754: StoreField: r3->field_f = r4
    //     0xb3d754: stur            w4, [x3, #0xf]
    // 0xb3d758: r1 = Null
    //     0xb3d758: mov             x1, NULL
    // 0xb3d75c: r2 = 4
    //     0xb3d75c: movz            x2, #0x4
    // 0xb3d760: r0 = AllocateArray()
    //     0xb3d760: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3d764: mov             x2, x0
    // 0xb3d768: ldur            x0, [fp, #-0x58]
    // 0xb3d76c: stur            x2, [fp, #-0x48]
    // 0xb3d770: StoreField: r2->field_f = r0
    //     0xb3d770: stur            w0, [x2, #0xf]
    // 0xb3d774: ldur            x0, [fp, #-0x60]
    // 0xb3d778: StoreField: r2->field_13 = r0
    //     0xb3d778: stur            w0, [x2, #0x13]
    // 0xb3d77c: r1 = <TextInputFormatter>
    //     0xb3d77c: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb3d780: ldr             x1, [x1, #0x7b0]
    // 0xb3d784: r0 = AllocateGrowableArray()
    //     0xb3d784: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3d788: mov             x2, x0
    // 0xb3d78c: ldur            x0, [fp, #-0x48]
    // 0xb3d790: stur            x2, [fp, #-0x58]
    // 0xb3d794: StoreField: r2->field_f = r0
    //     0xb3d794: stur            w0, [x2, #0xf]
    // 0xb3d798: r0 = 4
    //     0xb3d798: movz            x0, #0x4
    // 0xb3d79c: StoreField: r2->field_b = r0
    //     0xb3d79c: stur            w0, [x2, #0xb]
    // 0xb3d7a0: ldur            x1, [fp, #-0x10]
    // 0xb3d7a4: r0 = of()
    //     0xb3d7a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3d7a8: LoadField: r1 = r0->field_87
    //     0xb3d7a8: ldur            w1, [x0, #0x87]
    // 0xb3d7ac: DecompressPointer r1
    //     0xb3d7ac: add             x1, x1, HEAP, lsl #32
    // 0xb3d7b0: LoadField: r0 = r1->field_2b
    //     0xb3d7b0: ldur            w0, [x1, #0x2b]
    // 0xb3d7b4: DecompressPointer r0
    //     0xb3d7b4: add             x0, x0, HEAP, lsl #32
    // 0xb3d7b8: ldur            x1, [fp, #-0x10]
    // 0xb3d7bc: stur            x0, [fp, #-0x48]
    // 0xb3d7c0: r0 = of()
    //     0xb3d7c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3d7c4: LoadField: r1 = r0->field_5b
    //     0xb3d7c4: ldur            w1, [x0, #0x5b]
    // 0xb3d7c8: DecompressPointer r1
    //     0xb3d7c8: add             x1, x1, HEAP, lsl #32
    // 0xb3d7cc: r16 = 14.000000
    //     0xb3d7cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3d7d0: ldr             x16, [x16, #0x1d8]
    // 0xb3d7d4: stp             x16, x1, [SP]
    // 0xb3d7d8: ldur            x1, [fp, #-0x48]
    // 0xb3d7dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb3d7dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb3d7e0: ldr             x4, [x4, #0x9b8]
    // 0xb3d7e4: r0 = copyWith()
    //     0xb3d7e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3d7e8: ldur            x2, [fp, #-8]
    // 0xb3d7ec: stur            x0, [fp, #-0x60]
    // 0xb3d7f0: LoadField: r3 = r2->field_47
    //     0xb3d7f0: ldur            w3, [x2, #0x47]
    // 0xb3d7f4: DecompressPointer r3
    //     0xb3d7f4: add             x3, x3, HEAP, lsl #32
    // 0xb3d7f8: ldur            x1, [fp, #-0x10]
    // 0xb3d7fc: stur            x3, [fp, #-0x48]
    // 0xb3d800: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb3d800: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb3d804: ldur            x1, [fp, #-0x10]
    // 0xb3d808: stur            x0, [fp, #-0x68]
    // 0xb3d80c: r0 = of()
    //     0xb3d80c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3d810: LoadField: r1 = r0->field_5b
    //     0xb3d810: ldur            w1, [x0, #0x5b]
    // 0xb3d814: DecompressPointer r1
    //     0xb3d814: add             x1, x1, HEAP, lsl #32
    // 0xb3d818: stur            x1, [fp, #-0x70]
    // 0xb3d81c: r0 = BorderSide()
    //     0xb3d81c: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb3d820: mov             x1, x0
    // 0xb3d824: ldur            x0, [fp, #-0x70]
    // 0xb3d828: stur            x1, [fp, #-0x78]
    // 0xb3d82c: StoreField: r1->field_7 = r0
    //     0xb3d82c: stur            w0, [x1, #7]
    // 0xb3d830: d0 = 1.000000
    //     0xb3d830: fmov            d0, #1.00000000
    // 0xb3d834: StoreField: r1->field_b = d0
    //     0xb3d834: stur            d0, [x1, #0xb]
    // 0xb3d838: r0 = Instance_BorderStyle
    //     0xb3d838: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb3d83c: ldr             x0, [x0, #0xf68]
    // 0xb3d840: StoreField: r1->field_13 = r0
    //     0xb3d840: stur            w0, [x1, #0x13]
    // 0xb3d844: d1 = -1.000000
    //     0xb3d844: fmov            d1, #-1.00000000
    // 0xb3d848: ArrayStore: r1[0] = d1  ; List_8
    //     0xb3d848: stur            d1, [x1, #0x17]
    // 0xb3d84c: r0 = Radius()
    //     0xb3d84c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb3d850: d0 = 30.000000
    //     0xb3d850: fmov            d0, #30.00000000
    // 0xb3d854: stur            x0, [fp, #-0x70]
    // 0xb3d858: StoreField: r0->field_7 = d0
    //     0xb3d858: stur            d0, [x0, #7]
    // 0xb3d85c: StoreField: r0->field_f = d0
    //     0xb3d85c: stur            d0, [x0, #0xf]
    // 0xb3d860: r0 = BorderRadius()
    //     0xb3d860: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb3d864: mov             x1, x0
    // 0xb3d868: ldur            x0, [fp, #-0x70]
    // 0xb3d86c: stur            x1, [fp, #-0x80]
    // 0xb3d870: StoreField: r1->field_7 = r0
    //     0xb3d870: stur            w0, [x1, #7]
    // 0xb3d874: StoreField: r1->field_b = r0
    //     0xb3d874: stur            w0, [x1, #0xb]
    // 0xb3d878: StoreField: r1->field_f = r0
    //     0xb3d878: stur            w0, [x1, #0xf]
    // 0xb3d87c: StoreField: r1->field_13 = r0
    //     0xb3d87c: stur            w0, [x1, #0x13]
    // 0xb3d880: r0 = OutlineInputBorder()
    //     0xb3d880: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xb3d884: mov             x2, x0
    // 0xb3d888: ldur            x0, [fp, #-0x80]
    // 0xb3d88c: stur            x2, [fp, #-0x70]
    // 0xb3d890: StoreField: r2->field_13 = r0
    //     0xb3d890: stur            w0, [x2, #0x13]
    // 0xb3d894: d0 = 4.000000
    //     0xb3d894: fmov            d0, #4.00000000
    // 0xb3d898: StoreField: r2->field_b = d0
    //     0xb3d898: stur            d0, [x2, #0xb]
    // 0xb3d89c: ldur            x0, [fp, #-0x78]
    // 0xb3d8a0: StoreField: r2->field_7 = r0
    //     0xb3d8a0: stur            w0, [x2, #7]
    // 0xb3d8a4: ldur            x1, [fp, #-0x10]
    // 0xb3d8a8: r0 = of()
    //     0xb3d8a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3d8ac: LoadField: r1 = r0->field_87
    //     0xb3d8ac: ldur            w1, [x0, #0x87]
    // 0xb3d8b0: DecompressPointer r1
    //     0xb3d8b0: add             x1, x1, HEAP, lsl #32
    // 0xb3d8b4: LoadField: r0 = r1->field_2b
    //     0xb3d8b4: ldur            w0, [x1, #0x2b]
    // 0xb3d8b8: DecompressPointer r0
    //     0xb3d8b8: add             x0, x0, HEAP, lsl #32
    // 0xb3d8bc: stur            x0, [fp, #-0x78]
    // 0xb3d8c0: r1 = Instance_Color
    //     0xb3d8c0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3d8c4: d0 = 0.400000
    //     0xb3d8c4: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb3d8c8: r0 = withOpacity()
    //     0xb3d8c8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb3d8cc: r16 = 14.000000
    //     0xb3d8cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3d8d0: ldr             x16, [x16, #0x1d8]
    // 0xb3d8d4: stp             x0, x16, [SP]
    // 0xb3d8d8: ldur            x1, [fp, #-0x78]
    // 0xb3d8dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3d8dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3d8e0: ldr             x4, [x4, #0xaa0]
    // 0xb3d8e4: r0 = copyWith()
    //     0xb3d8e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3d8e8: ldur            x1, [fp, #-0x10]
    // 0xb3d8ec: stur            x0, [fp, #-0x78]
    // 0xb3d8f0: r0 = of()
    //     0xb3d8f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3d8f4: LoadField: r1 = r0->field_87
    //     0xb3d8f4: ldur            w1, [x0, #0x87]
    // 0xb3d8f8: DecompressPointer r1
    //     0xb3d8f8: add             x1, x1, HEAP, lsl #32
    // 0xb3d8fc: LoadField: r0 = r1->field_2b
    //     0xb3d8fc: ldur            w0, [x1, #0x2b]
    // 0xb3d900: DecompressPointer r0
    //     0xb3d900: add             x0, x0, HEAP, lsl #32
    // 0xb3d904: r16 = 12.000000
    //     0xb3d904: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3d908: ldr             x16, [x16, #0x9e8]
    // 0xb3d90c: r30 = Instance_MaterialColor
    //     0xb3d90c: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb3d910: ldr             lr, [lr, #0x180]
    // 0xb3d914: stp             lr, x16, [SP]
    // 0xb3d918: mov             x1, x0
    // 0xb3d91c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3d91c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3d920: ldr             x4, [x4, #0xaa0]
    // 0xb3d924: r0 = copyWith()
    //     0xb3d924: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3d928: mov             x1, x0
    // 0xb3d92c: ldur            x2, [fp, #-8]
    // 0xb3d930: stur            x1, [fp, #-0x80]
    // 0xb3d934: LoadField: r0 = r2->field_57
    //     0xb3d934: ldur            w0, [x2, #0x57]
    // 0xb3d938: DecompressPointer r0
    //     0xb3d938: add             x0, x0, HEAP, lsl #32
    // 0xb3d93c: tbnz            w0, #4, #0xb3da68
    // 0xb3d940: LoadField: r0 = r2->field_47
    //     0xb3d940: ldur            w0, [x2, #0x47]
    // 0xb3d944: DecompressPointer r0
    //     0xb3d944: add             x0, x0, HEAP, lsl #32
    // 0xb3d948: LoadField: r3 = r0->field_27
    //     0xb3d948: ldur            w3, [x0, #0x27]
    // 0xb3d94c: DecompressPointer r3
    //     0xb3d94c: add             x3, x3, HEAP, lsl #32
    // 0xb3d950: LoadField: r0 = r3->field_7
    //     0xb3d950: ldur            w0, [x3, #7]
    // 0xb3d954: DecompressPointer r0
    //     0xb3d954: add             x0, x0, HEAP, lsl #32
    // 0xb3d958: r3 = LoadClassIdInstr(r0)
    //     0xb3d958: ldur            x3, [x0, #-1]
    //     0xb3d95c: ubfx            x3, x3, #0xc, #0x14
    // 0xb3d960: r16 = ""
    //     0xb3d960: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3d964: stp             x16, x0, [SP]
    // 0xb3d968: mov             x0, x3
    // 0xb3d96c: mov             lr, x0
    // 0xb3d970: ldr             lr, [x21, lr, lsl #3]
    // 0xb3d974: blr             lr
    // 0xb3d978: tbz             w0, #4, #0xb3d9b4
    // 0xb3d97c: ldur            x2, [fp, #-8]
    // 0xb3d980: LoadField: r0 = r2->field_47
    //     0xb3d980: ldur            w0, [x2, #0x47]
    // 0xb3d984: DecompressPointer r0
    //     0xb3d984: add             x0, x0, HEAP, lsl #32
    // 0xb3d988: LoadField: r1 = r0->field_27
    //     0xb3d988: ldur            w1, [x0, #0x27]
    // 0xb3d98c: DecompressPointer r1
    //     0xb3d98c: add             x1, x1, HEAP, lsl #32
    // 0xb3d990: LoadField: r0 = r1->field_7
    //     0xb3d990: ldur            w0, [x1, #7]
    // 0xb3d994: DecompressPointer r0
    //     0xb3d994: add             x0, x0, HEAP, lsl #32
    // 0xb3d998: LoadField: r1 = r0->field_7
    //     0xb3d998: ldur            w1, [x0, #7]
    // 0xb3d99c: r0 = LoadInt32Instr(r1)
    //     0xb3d99c: sbfx            x0, x1, #1, #0x1f
    // 0xb3d9a0: cmp             x0, #0x14
    // 0xb3d9a4: b.lt            #0xb3d9b8
    // 0xb3d9a8: r1 = Instance_IconData
    //     0xb3d9a8: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb3d9ac: ldr             x1, [x1, #0x130]
    // 0xb3d9b0: b               #0xb3d9c0
    // 0xb3d9b4: ldur            x2, [fp, #-8]
    // 0xb3d9b8: r1 = Instance_IconData
    //     0xb3d9b8: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb3d9bc: ldr             x1, [x1, #0x138]
    // 0xb3d9c0: stur            x1, [fp, #-0x88]
    // 0xb3d9c4: LoadField: r0 = r2->field_47
    //     0xb3d9c4: ldur            w0, [x2, #0x47]
    // 0xb3d9c8: DecompressPointer r0
    //     0xb3d9c8: add             x0, x0, HEAP, lsl #32
    // 0xb3d9cc: LoadField: r3 = r0->field_27
    //     0xb3d9cc: ldur            w3, [x0, #0x27]
    // 0xb3d9d0: DecompressPointer r3
    //     0xb3d9d0: add             x3, x3, HEAP, lsl #32
    // 0xb3d9d4: LoadField: r0 = r3->field_7
    //     0xb3d9d4: ldur            w0, [x3, #7]
    // 0xb3d9d8: DecompressPointer r0
    //     0xb3d9d8: add             x0, x0, HEAP, lsl #32
    // 0xb3d9dc: r3 = LoadClassIdInstr(r0)
    //     0xb3d9dc: ldur            x3, [x0, #-1]
    //     0xb3d9e0: ubfx            x3, x3, #0xc, #0x14
    // 0xb3d9e4: r16 = ""
    //     0xb3d9e4: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3d9e8: stp             x16, x0, [SP]
    // 0xb3d9ec: mov             x0, x3
    // 0xb3d9f0: mov             lr, x0
    // 0xb3d9f4: ldr             lr, [x21, lr, lsl #3]
    // 0xb3d9f8: blr             lr
    // 0xb3d9fc: tbz             w0, #4, #0xb3da38
    // 0xb3da00: ldur            x2, [fp, #-8]
    // 0xb3da04: LoadField: r0 = r2->field_47
    //     0xb3da04: ldur            w0, [x2, #0x47]
    // 0xb3da08: DecompressPointer r0
    //     0xb3da08: add             x0, x0, HEAP, lsl #32
    // 0xb3da0c: LoadField: r1 = r0->field_27
    //     0xb3da0c: ldur            w1, [x0, #0x27]
    // 0xb3da10: DecompressPointer r1
    //     0xb3da10: add             x1, x1, HEAP, lsl #32
    // 0xb3da14: LoadField: r0 = r1->field_7
    //     0xb3da14: ldur            w0, [x1, #7]
    // 0xb3da18: DecompressPointer r0
    //     0xb3da18: add             x0, x0, HEAP, lsl #32
    // 0xb3da1c: LoadField: r1 = r0->field_7
    //     0xb3da1c: ldur            w1, [x0, #7]
    // 0xb3da20: r0 = LoadInt32Instr(r1)
    //     0xb3da20: sbfx            x0, x1, #1, #0x1f
    // 0xb3da24: cmp             x0, #0x14
    // 0xb3da28: b.lt            #0xb3da3c
    // 0xb3da2c: r1 = Instance_Color
    //     0xb3da2c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb3da30: ldr             x1, [x1, #0x858]
    // 0xb3da34: b               #0xb3da44
    // 0xb3da38: ldur            x2, [fp, #-8]
    // 0xb3da3c: r1 = Instance_Color
    //     0xb3da3c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb3da40: ldr             x1, [x1, #0x50]
    // 0xb3da44: ldur            x0, [fp, #-0x88]
    // 0xb3da48: stur            x1, [fp, #-0x90]
    // 0xb3da4c: r0 = Icon()
    //     0xb3da4c: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb3da50: mov             x1, x0
    // 0xb3da54: ldur            x0, [fp, #-0x88]
    // 0xb3da58: StoreField: r1->field_b = r0
    //     0xb3da58: stur            w0, [x1, #0xb]
    // 0xb3da5c: ldur            x0, [fp, #-0x90]
    // 0xb3da60: StoreField: r1->field_23 = r0
    //     0xb3da60: stur            w0, [x1, #0x23]
    // 0xb3da64: b               #0xb3da6c
    // 0xb3da68: r1 = Null
    //     0xb3da68: mov             x1, NULL
    // 0xb3da6c: ldur            x2, [fp, #-8]
    // 0xb3da70: ldur            x0, [fp, #-0x50]
    // 0xb3da74: ldur            x3, [fp, #-0x20]
    // 0xb3da78: ldur            x16, [fp, #-0x70]
    // 0xb3da7c: r30 = Instance_EdgeInsets
    //     0xb3da7c: add             lr, PP, #0x56, lsl #12  ; [pp+0x56828] Obj!EdgeInsets@d58d01
    //     0xb3da80: ldr             lr, [lr, #0x828]
    // 0xb3da84: stp             lr, x16, [SP, #0x20]
    // 0xb3da88: r16 = "Road Name / Area / Colony*"
    //     0xb3da88: add             x16, PP, #0x54, lsl #12  ; [pp+0x540b0] "Road Name / Area / Colony*"
    //     0xb3da8c: ldr             x16, [x16, #0xb0]
    // 0xb3da90: ldur            lr, [fp, #-0x78]
    // 0xb3da94: stp             lr, x16, [SP, #0x10]
    // 0xb3da98: ldur            x16, [fp, #-0x80]
    // 0xb3da9c: stp             x1, x16, [SP]
    // 0xb3daa0: ldur            x1, [fp, #-0x68]
    // 0xb3daa4: r4 = const [0, 0x7, 0x6, 0x1, contentPadding, 0x2, errorStyle, 0x5, focusedBorder, 0x1, hintStyle, 0x4, hintText, 0x3, suffixIcon, 0x6, null]
    //     0xb3daa4: add             x4, PP, #0x56, lsl #12  ; [pp+0x56ef8] List(17) [0, 0x7, 0x6, 0x1, "contentPadding", 0x2, "errorStyle", 0x5, "focusedBorder", 0x1, "hintStyle", 0x4, "hintText", 0x3, "suffixIcon", 0x6, Null]
    //     0xb3daa8: ldr             x4, [x4, #0xef8]
    // 0xb3daac: r0 = copyWith()
    //     0xb3daac: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb3dab0: ldur            x2, [fp, #-8]
    // 0xb3dab4: r1 = Function '_validateAddress@1537065735':.
    //     0xb3dab4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f00] AnonymousClosure: (0xb40944), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateAddress (0xa023b0)
    //     0xb3dab8: ldr             x1, [x1, #0xf00]
    // 0xb3dabc: stur            x0, [fp, #-0x68]
    // 0xb3dac0: r0 = AllocateClosure()
    //     0xb3dac0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3dac4: ldur            x2, [fp, #-0x18]
    // 0xb3dac8: r1 = Function '<anonymous closure>':.
    //     0xb3dac8: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f08] AnonymousClosure: (0xb408cc), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xb3ca88)
    //     0xb3dacc: ldr             x1, [x1, #0xf08]
    // 0xb3dad0: stur            x0, [fp, #-0x70]
    // 0xb3dad4: r0 = AllocateClosure()
    //     0xb3dad4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3dad8: r1 = <String>
    //     0xb3dad8: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb3dadc: stur            x0, [fp, #-0x78]
    // 0xb3dae0: r0 = TextFormField()
    //     0xb3dae0: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb3dae4: stur            x0, [fp, #-0x80]
    // 0xb3dae8: ldur            x16, [fp, #-0x70]
    // 0xb3daec: r30 = Instance_AutovalidateMode
    //     0xb3daec: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb3daf0: ldr             lr, [lr, #0x7e8]
    // 0xb3daf4: stp             lr, x16, [SP, #0x48]
    // 0xb3daf8: r16 = true
    //     0xb3daf8: add             x16, NULL, #0x20  ; true
    // 0xb3dafc: r30 = true
    //     0xb3dafc: add             lr, NULL, #0x20  ; true
    // 0xb3db00: stp             lr, x16, [SP, #0x38]
    // 0xb3db04: ldur            x16, [fp, #-0x58]
    // 0xb3db08: r30 = Instance_TextInputType
    //     0xb3db08: add             lr, PP, #0x33, lsl #12  ; [pp+0x337f0] Obj!TextInputType@d55b61
    //     0xb3db0c: ldr             lr, [lr, #0x7f0]
    // 0xb3db10: stp             lr, x16, [SP, #0x28]
    // 0xb3db14: r16 = 2
    //     0xb3db14: movz            x16, #0x2
    // 0xb3db18: r30 = 4
    //     0xb3db18: movz            lr, #0x4
    // 0xb3db1c: stp             lr, x16, [SP, #0x18]
    // 0xb3db20: ldur            x16, [fp, #-0x60]
    // 0xb3db24: ldur            lr, [fp, #-0x48]
    // 0xb3db28: stp             lr, x16, [SP, #8]
    // 0xb3db2c: ldur            x16, [fp, #-0x78]
    // 0xb3db30: str             x16, [SP]
    // 0xb3db34: mov             x1, x0
    // 0xb3db38: ldur            x2, [fp, #-0x68]
    // 0xb3db3c: r4 = const [0, 0xd, 0xb, 0x2, autofocus, 0x5, autovalidateMode, 0x3, controller, 0xb, enableSuggestions, 0x4, inputFormatters, 0x6, keyboardType, 0x7, maxLines, 0x9, minLines, 0x8, onChanged, 0xc, style, 0xa, validator, 0x2, null]
    //     0xb3db3c: add             x4, PP, #0x54, lsl #12  ; [pp+0x548b0] List(27) [0, 0xd, 0xb, 0x2, "autofocus", 0x5, "autovalidateMode", 0x3, "controller", 0xb, "enableSuggestions", 0x4, "inputFormatters", 0x6, "keyboardType", 0x7, "maxLines", 0x9, "minLines", 0x8, "onChanged", 0xc, "style", 0xa, "validator", 0x2, Null]
    //     0xb3db40: ldr             x4, [x4, #0x8b0]
    // 0xb3db44: r0 = TextFormField()
    //     0xb3db44: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb3db48: r0 = Form()
    //     0xb3db48: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb3db4c: mov             x3, x0
    // 0xb3db50: ldur            x0, [fp, #-0x80]
    // 0xb3db54: stur            x3, [fp, #-0x48]
    // 0xb3db58: StoreField: r3->field_b = r0
    //     0xb3db58: stur            w0, [x3, #0xb]
    // 0xb3db5c: r0 = Instance_AutovalidateMode
    //     0xb3db5c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb3db60: ldr             x0, [x0, #0x800]
    // 0xb3db64: StoreField: r3->field_23 = r0
    //     0xb3db64: stur            w0, [x3, #0x23]
    // 0xb3db68: ldur            x1, [fp, #-0x20]
    // 0xb3db6c: StoreField: r3->field_7 = r1
    //     0xb3db6c: stur            w1, [x3, #7]
    // 0xb3db70: r1 = Null
    //     0xb3db70: mov             x1, NULL
    // 0xb3db74: r2 = 4
    //     0xb3db74: movz            x2, #0x4
    // 0xb3db78: r0 = AllocateArray()
    //     0xb3db78: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3db7c: mov             x2, x0
    // 0xb3db80: ldur            x0, [fp, #-0x50]
    // 0xb3db84: stur            x2, [fp, #-0x20]
    // 0xb3db88: StoreField: r2->field_f = r0
    //     0xb3db88: stur            w0, [x2, #0xf]
    // 0xb3db8c: ldur            x0, [fp, #-0x48]
    // 0xb3db90: StoreField: r2->field_13 = r0
    //     0xb3db90: stur            w0, [x2, #0x13]
    // 0xb3db94: r1 = <Widget>
    //     0xb3db94: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3db98: r0 = AllocateGrowableArray()
    //     0xb3db98: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3db9c: mov             x1, x0
    // 0xb3dba0: ldur            x0, [fp, #-0x20]
    // 0xb3dba4: stur            x1, [fp, #-0x48]
    // 0xb3dba8: StoreField: r1->field_f = r0
    //     0xb3dba8: stur            w0, [x1, #0xf]
    // 0xb3dbac: r2 = 4
    //     0xb3dbac: movz            x2, #0x4
    // 0xb3dbb0: StoreField: r1->field_b = r2
    //     0xb3dbb0: stur            w2, [x1, #0xb]
    // 0xb3dbb4: r0 = Column()
    //     0xb3dbb4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3dbb8: mov             x1, x0
    // 0xb3dbbc: r0 = Instance_Axis
    //     0xb3dbbc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3dbc0: stur            x1, [fp, #-0x20]
    // 0xb3dbc4: StoreField: r1->field_f = r0
    //     0xb3dbc4: stur            w0, [x1, #0xf]
    // 0xb3dbc8: r2 = Instance_MainAxisAlignment
    //     0xb3dbc8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3dbcc: ldr             x2, [x2, #0xa08]
    // 0xb3dbd0: StoreField: r1->field_13 = r2
    //     0xb3dbd0: stur            w2, [x1, #0x13]
    // 0xb3dbd4: r3 = Instance_MainAxisSize
    //     0xb3dbd4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3dbd8: ldr             x3, [x3, #0xa10]
    // 0xb3dbdc: ArrayStore: r1[0] = r3  ; List_4
    //     0xb3dbdc: stur            w3, [x1, #0x17]
    // 0xb3dbe0: r4 = Instance_CrossAxisAlignment
    //     0xb3dbe0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3dbe4: ldr             x4, [x4, #0x890]
    // 0xb3dbe8: StoreField: r1->field_1b = r4
    //     0xb3dbe8: stur            w4, [x1, #0x1b]
    // 0xb3dbec: r5 = Instance_VerticalDirection
    //     0xb3dbec: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3dbf0: ldr             x5, [x5, #0xa20]
    // 0xb3dbf4: StoreField: r1->field_23 = r5
    //     0xb3dbf4: stur            w5, [x1, #0x23]
    // 0xb3dbf8: r6 = Instance_Clip
    //     0xb3dbf8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3dbfc: ldr             x6, [x6, #0x38]
    // 0xb3dc00: StoreField: r1->field_2b = r6
    //     0xb3dc00: stur            w6, [x1, #0x2b]
    // 0xb3dc04: StoreField: r1->field_2f = rZR
    //     0xb3dc04: stur            xzr, [x1, #0x2f]
    // 0xb3dc08: ldur            x7, [fp, #-0x48]
    // 0xb3dc0c: StoreField: r1->field_b = r7
    //     0xb3dc0c: stur            w7, [x1, #0xb]
    // 0xb3dc10: r0 = Padding()
    //     0xb3dc10: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3dc14: mov             x2, x0
    // 0xb3dc18: r0 = Instance_EdgeInsets
    //     0xb3dc18: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xb3dc1c: ldr             x0, [x0, #0x858]
    // 0xb3dc20: stur            x2, [fp, #-0x48]
    // 0xb3dc24: StoreField: r2->field_f = r0
    //     0xb3dc24: stur            w0, [x2, #0xf]
    // 0xb3dc28: ldur            x1, [fp, #-0x20]
    // 0xb3dc2c: StoreField: r2->field_b = r1
    //     0xb3dc2c: stur            w1, [x2, #0xb]
    // 0xb3dc30: ldur            x1, [fp, #-0x10]
    // 0xb3dc34: r0 = of()
    //     0xb3dc34: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3dc38: LoadField: r1 = r0->field_87
    //     0xb3dc38: ldur            w1, [x0, #0x87]
    // 0xb3dc3c: DecompressPointer r1
    //     0xb3dc3c: add             x1, x1, HEAP, lsl #32
    // 0xb3dc40: LoadField: r0 = r1->field_2b
    //     0xb3dc40: ldur            w0, [x1, #0x2b]
    // 0xb3dc44: DecompressPointer r0
    //     0xb3dc44: add             x0, x0, HEAP, lsl #32
    // 0xb3dc48: r16 = 12.000000
    //     0xb3dc48: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3dc4c: ldr             x16, [x16, #0x9e8]
    // 0xb3dc50: r30 = Instance_Color
    //     0xb3dc50: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3dc54: stp             lr, x16, [SP]
    // 0xb3dc58: mov             x1, x0
    // 0xb3dc5c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3dc5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3dc60: ldr             x4, [x4, #0xaa0]
    // 0xb3dc64: r0 = copyWith()
    //     0xb3dc64: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3dc68: stur            x0, [fp, #-0x20]
    // 0xb3dc6c: r0 = Text()
    //     0xb3dc6c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3dc70: mov             x1, x0
    // 0xb3dc74: r0 = "Pincode*"
    //     0xb3dc74: add             x0, PP, #0x54, lsl #12  ; [pp+0x540d8] "Pincode*"
    //     0xb3dc78: ldr             x0, [x0, #0xd8]
    // 0xb3dc7c: stur            x1, [fp, #-0x50]
    // 0xb3dc80: StoreField: r1->field_b = r0
    //     0xb3dc80: stur            w0, [x1, #0xb]
    // 0xb3dc84: ldur            x0, [fp, #-0x20]
    // 0xb3dc88: StoreField: r1->field_13 = r0
    //     0xb3dc88: stur            w0, [x1, #0x13]
    // 0xb3dc8c: r0 = Padding()
    //     0xb3dc8c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3dc90: mov             x2, x0
    // 0xb3dc94: r0 = Instance_EdgeInsets
    //     0xb3dc94: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f4c0] Obj!EdgeInsets@d579e1
    //     0xb3dc98: ldr             x0, [x0, #0x4c0]
    // 0xb3dc9c: stur            x2, [fp, #-0x58]
    // 0xb3dca0: StoreField: r2->field_f = r0
    //     0xb3dca0: stur            w0, [x2, #0xf]
    // 0xb3dca4: ldur            x1, [fp, #-0x50]
    // 0xb3dca8: StoreField: r2->field_b = r1
    //     0xb3dca8: stur            w1, [x2, #0xb]
    // 0xb3dcac: ldur            x3, [fp, #-8]
    // 0xb3dcb0: LoadField: r4 = r3->field_1f
    //     0xb3dcb0: ldur            w4, [x3, #0x1f]
    // 0xb3dcb4: DecompressPointer r4
    //     0xb3dcb4: add             x4, x4, HEAP, lsl #32
    // 0xb3dcb8: ldur            x1, [fp, #-0x10]
    // 0xb3dcbc: stur            x4, [fp, #-0x20]
    // 0xb3dcc0: r0 = of()
    //     0xb3dcc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3dcc4: LoadField: r1 = r0->field_87
    //     0xb3dcc4: ldur            w1, [x0, #0x87]
    // 0xb3dcc8: DecompressPointer r1
    //     0xb3dcc8: add             x1, x1, HEAP, lsl #32
    // 0xb3dccc: LoadField: r0 = r1->field_2b
    //     0xb3dccc: ldur            w0, [x1, #0x2b]
    // 0xb3dcd0: DecompressPointer r0
    //     0xb3dcd0: add             x0, x0, HEAP, lsl #32
    // 0xb3dcd4: ldur            x1, [fp, #-0x10]
    // 0xb3dcd8: stur            x0, [fp, #-0x50]
    // 0xb3dcdc: r0 = of()
    //     0xb3dcdc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3dce0: LoadField: r1 = r0->field_5b
    //     0xb3dce0: ldur            w1, [x0, #0x5b]
    // 0xb3dce4: DecompressPointer r1
    //     0xb3dce4: add             x1, x1, HEAP, lsl #32
    // 0xb3dce8: r16 = 14.000000
    //     0xb3dce8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3dcec: ldr             x16, [x16, #0x1d8]
    // 0xb3dcf0: stp             x16, x1, [SP]
    // 0xb3dcf4: ldur            x1, [fp, #-0x50]
    // 0xb3dcf8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb3dcf8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb3dcfc: ldr             x4, [x4, #0x9b8]
    // 0xb3dd00: r0 = copyWith()
    //     0xb3dd00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3dd04: stur            x0, [fp, #-0x50]
    // 0xb3dd08: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xb3dd08: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb3dd0c: ldr             x0, [x0, #0x1530]
    //     0xb3dd10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb3dd14: cmp             w0, w16
    //     0xb3dd18: b.ne            #0xb3dd28
    //     0xb3dd1c: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0xb3dd20: ldr             x2, [x2, #0x120]
    //     0xb3dd24: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb3dd28: stur            x0, [fp, #-0x60]
    // 0xb3dd2c: r16 = "[0-9]"
    //     0xb3dd2c: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0xb3dd30: ldr             x16, [x16, #0x128]
    // 0xb3dd34: stp             x16, NULL, [SP, #0x20]
    // 0xb3dd38: r16 = false
    //     0xb3dd38: add             x16, NULL, #0x30  ; false
    // 0xb3dd3c: r30 = true
    //     0xb3dd3c: add             lr, NULL, #0x20  ; true
    // 0xb3dd40: stp             lr, x16, [SP, #0x10]
    // 0xb3dd44: r16 = false
    //     0xb3dd44: add             x16, NULL, #0x30  ; false
    // 0xb3dd48: r30 = false
    //     0xb3dd48: add             lr, NULL, #0x30  ; false
    // 0xb3dd4c: stp             lr, x16, [SP]
    // 0xb3dd50: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xb3dd50: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xb3dd54: r0 = _RegExp()
    //     0xb3dd54: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xb3dd58: stur            x0, [fp, #-0x68]
    // 0xb3dd5c: r0 = FilteringTextInputFormatter()
    //     0xb3dd5c: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xb3dd60: mov             x1, x0
    // 0xb3dd64: ldur            x0, [fp, #-0x68]
    // 0xb3dd68: stur            x1, [fp, #-0x70]
    // 0xb3dd6c: StoreField: r1->field_b = r0
    //     0xb3dd6c: stur            w0, [x1, #0xb]
    // 0xb3dd70: r0 = true
    //     0xb3dd70: add             x0, NULL, #0x20  ; true
    // 0xb3dd74: StoreField: r1->field_7 = r0
    //     0xb3dd74: stur            w0, [x1, #7]
    // 0xb3dd78: r2 = ""
    //     0xb3dd78: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3dd7c: StoreField: r1->field_f = r2
    //     0xb3dd7c: stur            w2, [x1, #0xf]
    // 0xb3dd80: r0 = LengthLimitingTextInputFormatter()
    //     0xb3dd80: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb3dd84: mov             x3, x0
    // 0xb3dd88: r0 = 12
    //     0xb3dd88: movz            x0, #0xc
    // 0xb3dd8c: stur            x3, [fp, #-0x68]
    // 0xb3dd90: StoreField: r3->field_7 = r0
    //     0xb3dd90: stur            w0, [x3, #7]
    // 0xb3dd94: r1 = Null
    //     0xb3dd94: mov             x1, NULL
    // 0xb3dd98: r2 = 6
    //     0xb3dd98: movz            x2, #0x6
    // 0xb3dd9c: r0 = AllocateArray()
    //     0xb3dd9c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3dda0: mov             x2, x0
    // 0xb3dda4: ldur            x0, [fp, #-0x60]
    // 0xb3dda8: stur            x2, [fp, #-0x78]
    // 0xb3ddac: StoreField: r2->field_f = r0
    //     0xb3ddac: stur            w0, [x2, #0xf]
    // 0xb3ddb0: ldur            x1, [fp, #-0x70]
    // 0xb3ddb4: StoreField: r2->field_13 = r1
    //     0xb3ddb4: stur            w1, [x2, #0x13]
    // 0xb3ddb8: ldur            x1, [fp, #-0x68]
    // 0xb3ddbc: ArrayStore: r2[0] = r1  ; List_4
    //     0xb3ddbc: stur            w1, [x2, #0x17]
    // 0xb3ddc0: r1 = <TextInputFormatter>
    //     0xb3ddc0: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb3ddc4: ldr             x1, [x1, #0x7b0]
    // 0xb3ddc8: r0 = AllocateGrowableArray()
    //     0xb3ddc8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3ddcc: mov             x2, x0
    // 0xb3ddd0: ldur            x0, [fp, #-0x78]
    // 0xb3ddd4: stur            x2, [fp, #-0x70]
    // 0xb3ddd8: StoreField: r2->field_f = r0
    //     0xb3ddd8: stur            w0, [x2, #0xf]
    // 0xb3dddc: r0 = 6
    //     0xb3dddc: movz            x0, #0x6
    // 0xb3dde0: StoreField: r2->field_b = r0
    //     0xb3dde0: stur            w0, [x2, #0xb]
    // 0xb3dde4: ldur            x3, [fp, #-8]
    // 0xb3dde8: LoadField: r4 = r3->field_3f
    //     0xb3dde8: ldur            w4, [x3, #0x3f]
    // 0xb3ddec: DecompressPointer r4
    //     0xb3ddec: add             x4, x4, HEAP, lsl #32
    // 0xb3ddf0: ldur            x1, [fp, #-0x10]
    // 0xb3ddf4: stur            x4, [fp, #-0x68]
    // 0xb3ddf8: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb3ddf8: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb3ddfc: ldur            x1, [fp, #-0x10]
    // 0xb3de00: stur            x0, [fp, #-0x78]
    // 0xb3de04: r0 = of()
    //     0xb3de04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3de08: LoadField: r1 = r0->field_5b
    //     0xb3de08: ldur            w1, [x0, #0x5b]
    // 0xb3de0c: DecompressPointer r1
    //     0xb3de0c: add             x1, x1, HEAP, lsl #32
    // 0xb3de10: stur            x1, [fp, #-0x80]
    // 0xb3de14: r0 = BorderSide()
    //     0xb3de14: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb3de18: mov             x1, x0
    // 0xb3de1c: ldur            x0, [fp, #-0x80]
    // 0xb3de20: stur            x1, [fp, #-0x88]
    // 0xb3de24: StoreField: r1->field_7 = r0
    //     0xb3de24: stur            w0, [x1, #7]
    // 0xb3de28: d0 = 1.000000
    //     0xb3de28: fmov            d0, #1.00000000
    // 0xb3de2c: StoreField: r1->field_b = d0
    //     0xb3de2c: stur            d0, [x1, #0xb]
    // 0xb3de30: r0 = Instance_BorderStyle
    //     0xb3de30: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb3de34: ldr             x0, [x0, #0xf68]
    // 0xb3de38: StoreField: r1->field_13 = r0
    //     0xb3de38: stur            w0, [x1, #0x13]
    // 0xb3de3c: d1 = -1.000000
    //     0xb3de3c: fmov            d1, #-1.00000000
    // 0xb3de40: ArrayStore: r1[0] = d1  ; List_8
    //     0xb3de40: stur            d1, [x1, #0x17]
    // 0xb3de44: r0 = Radius()
    //     0xb3de44: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb3de48: d0 = 30.000000
    //     0xb3de48: fmov            d0, #30.00000000
    // 0xb3de4c: stur            x0, [fp, #-0x80]
    // 0xb3de50: StoreField: r0->field_7 = d0
    //     0xb3de50: stur            d0, [x0, #7]
    // 0xb3de54: StoreField: r0->field_f = d0
    //     0xb3de54: stur            d0, [x0, #0xf]
    // 0xb3de58: r0 = BorderRadius()
    //     0xb3de58: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb3de5c: mov             x1, x0
    // 0xb3de60: ldur            x0, [fp, #-0x80]
    // 0xb3de64: stur            x1, [fp, #-0x90]
    // 0xb3de68: StoreField: r1->field_7 = r0
    //     0xb3de68: stur            w0, [x1, #7]
    // 0xb3de6c: StoreField: r1->field_b = r0
    //     0xb3de6c: stur            w0, [x1, #0xb]
    // 0xb3de70: StoreField: r1->field_f = r0
    //     0xb3de70: stur            w0, [x1, #0xf]
    // 0xb3de74: StoreField: r1->field_13 = r0
    //     0xb3de74: stur            w0, [x1, #0x13]
    // 0xb3de78: r0 = OutlineInputBorder()
    //     0xb3de78: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xb3de7c: mov             x2, x0
    // 0xb3de80: ldur            x0, [fp, #-0x90]
    // 0xb3de84: stur            x2, [fp, #-0x80]
    // 0xb3de88: StoreField: r2->field_13 = r0
    //     0xb3de88: stur            w0, [x2, #0x13]
    // 0xb3de8c: d0 = 4.000000
    //     0xb3de8c: fmov            d0, #4.00000000
    // 0xb3de90: StoreField: r2->field_b = d0
    //     0xb3de90: stur            d0, [x2, #0xb]
    // 0xb3de94: ldur            x0, [fp, #-0x88]
    // 0xb3de98: StoreField: r2->field_7 = r0
    //     0xb3de98: stur            w0, [x2, #7]
    // 0xb3de9c: ldur            x1, [fp, #-0x10]
    // 0xb3dea0: r0 = of()
    //     0xb3dea0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3dea4: LoadField: r1 = r0->field_87
    //     0xb3dea4: ldur            w1, [x0, #0x87]
    // 0xb3dea8: DecompressPointer r1
    //     0xb3dea8: add             x1, x1, HEAP, lsl #32
    // 0xb3deac: LoadField: r0 = r1->field_2b
    //     0xb3deac: ldur            w0, [x1, #0x2b]
    // 0xb3deb0: DecompressPointer r0
    //     0xb3deb0: add             x0, x0, HEAP, lsl #32
    // 0xb3deb4: r16 = 12.000000
    //     0xb3deb4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3deb8: ldr             x16, [x16, #0x9e8]
    // 0xb3debc: r30 = Instance_MaterialColor
    //     0xb3debc: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb3dec0: ldr             lr, [lr, #0x180]
    // 0xb3dec4: stp             lr, x16, [SP]
    // 0xb3dec8: mov             x1, x0
    // 0xb3decc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3decc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3ded0: ldr             x4, [x4, #0xaa0]
    // 0xb3ded4: r0 = copyWith()
    //     0xb3ded4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3ded8: ldur            x1, [fp, #-0x10]
    // 0xb3dedc: stur            x0, [fp, #-0x88]
    // 0xb3dee0: r0 = of()
    //     0xb3dee0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3dee4: LoadField: r1 = r0->field_87
    //     0xb3dee4: ldur            w1, [x0, #0x87]
    // 0xb3dee8: DecompressPointer r1
    //     0xb3dee8: add             x1, x1, HEAP, lsl #32
    // 0xb3deec: LoadField: r0 = r1->field_2b
    //     0xb3deec: ldur            w0, [x1, #0x2b]
    // 0xb3def0: DecompressPointer r0
    //     0xb3def0: add             x0, x0, HEAP, lsl #32
    // 0xb3def4: stur            x0, [fp, #-0x90]
    // 0xb3def8: r1 = Instance_Color
    //     0xb3def8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3defc: d0 = 0.400000
    //     0xb3defc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb3df00: r0 = withOpacity()
    //     0xb3df00: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb3df04: r16 = 14.000000
    //     0xb3df04: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3df08: ldr             x16, [x16, #0x1d8]
    // 0xb3df0c: stp             x0, x16, [SP]
    // 0xb3df10: ldur            x1, [fp, #-0x90]
    // 0xb3df14: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3df14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3df18: ldr             x4, [x4, #0xaa0]
    // 0xb3df1c: r0 = copyWith()
    //     0xb3df1c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3df20: ldur            x2, [fp, #-8]
    // 0xb3df24: stur            x0, [fp, #-0xa0]
    // 0xb3df28: LoadField: r1 = r2->field_5b
    //     0xb3df28: ldur            w1, [x2, #0x5b]
    // 0xb3df2c: DecompressPointer r1
    //     0xb3df2c: add             x1, x1, HEAP, lsl #32
    // 0xb3df30: tbnz            w1, #4, #0xb3e084
    // 0xb3df34: LoadField: r1 = r2->field_3f
    //     0xb3df34: ldur            w1, [x2, #0x3f]
    // 0xb3df38: DecompressPointer r1
    //     0xb3df38: add             x1, x1, HEAP, lsl #32
    // 0xb3df3c: LoadField: r3 = r1->field_27
    //     0xb3df3c: ldur            w3, [x1, #0x27]
    // 0xb3df40: DecompressPointer r3
    //     0xb3df40: add             x3, x3, HEAP, lsl #32
    // 0xb3df44: LoadField: r1 = r3->field_7
    //     0xb3df44: ldur            w1, [x3, #7]
    // 0xb3df48: DecompressPointer r1
    //     0xb3df48: add             x1, x1, HEAP, lsl #32
    // 0xb3df4c: LoadField: r3 = r1->field_7
    //     0xb3df4c: ldur            w3, [x1, #7]
    // 0xb3df50: cmp             w3, #0xc
    // 0xb3df54: b.ne            #0xb3dfd0
    // 0xb3df58: LoadField: r1 = r2->field_b
    //     0xb3df58: ldur            w1, [x2, #0xb]
    // 0xb3df5c: DecompressPointer r1
    //     0xb3df5c: add             x1, x1, HEAP, lsl #32
    // 0xb3df60: cmp             w1, NULL
    // 0xb3df64: b.eq            #0xb3fd88
    // 0xb3df68: LoadField: r4 = r1->field_13
    //     0xb3df68: ldur            w4, [x1, #0x13]
    // 0xb3df6c: DecompressPointer r4
    //     0xb3df6c: add             x4, x4, HEAP, lsl #32
    // 0xb3df70: LoadField: r1 = r4->field_b
    //     0xb3df70: ldur            w1, [x4, #0xb]
    // 0xb3df74: DecompressPointer r1
    //     0xb3df74: add             x1, x1, HEAP, lsl #32
    // 0xb3df78: cmp             w1, NULL
    // 0xb3df7c: b.ne            #0xb3df88
    // 0xb3df80: r1 = Null
    //     0xb3df80: mov             x1, NULL
    // 0xb3df84: b               #0xb3dfb8
    // 0xb3df88: LoadField: r4 = r1->field_13
    //     0xb3df88: ldur            w4, [x1, #0x13]
    // 0xb3df8c: DecompressPointer r4
    //     0xb3df8c: add             x4, x4, HEAP, lsl #32
    // 0xb3df90: cmp             w4, NULL
    // 0xb3df94: b.ne            #0xb3dfa0
    // 0xb3df98: r1 = Null
    //     0xb3df98: mov             x1, NULL
    // 0xb3df9c: b               #0xb3dfb8
    // 0xb3dfa0: LoadField: r1 = r4->field_7
    //     0xb3dfa0: ldur            w1, [x4, #7]
    // 0xb3dfa4: cbnz            w1, #0xb3dfb0
    // 0xb3dfa8: r4 = false
    //     0xb3dfa8: add             x4, NULL, #0x30  ; false
    // 0xb3dfac: b               #0xb3dfb4
    // 0xb3dfb0: r4 = true
    //     0xb3dfb0: add             x4, NULL, #0x20  ; true
    // 0xb3dfb4: mov             x1, x4
    // 0xb3dfb8: cmp             w1, NULL
    // 0xb3dfbc: b.eq            #0xb3dfd0
    // 0xb3dfc0: tbnz            w1, #4, #0xb3dfd0
    // 0xb3dfc4: r1 = Instance_IconData
    //     0xb3dfc4: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb3dfc8: ldr             x1, [x1, #0x130]
    // 0xb3dfcc: b               #0xb3dfd8
    // 0xb3dfd0: r1 = Instance_IconData
    //     0xb3dfd0: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb3dfd4: ldr             x1, [x1, #0x138]
    // 0xb3dfd8: stur            x1, [fp, #-0x98]
    // 0xb3dfdc: cmp             w3, #0xc
    // 0xb3dfe0: b.ne            #0xb3e05c
    // 0xb3dfe4: LoadField: r3 = r2->field_b
    //     0xb3dfe4: ldur            w3, [x2, #0xb]
    // 0xb3dfe8: DecompressPointer r3
    //     0xb3dfe8: add             x3, x3, HEAP, lsl #32
    // 0xb3dfec: cmp             w3, NULL
    // 0xb3dff0: b.eq            #0xb3fd8c
    // 0xb3dff4: LoadField: r4 = r3->field_13
    //     0xb3dff4: ldur            w4, [x3, #0x13]
    // 0xb3dff8: DecompressPointer r4
    //     0xb3dff8: add             x4, x4, HEAP, lsl #32
    // 0xb3dffc: LoadField: r3 = r4->field_b
    //     0xb3dffc: ldur            w3, [x4, #0xb]
    // 0xb3e000: DecompressPointer r3
    //     0xb3e000: add             x3, x3, HEAP, lsl #32
    // 0xb3e004: cmp             w3, NULL
    // 0xb3e008: b.ne            #0xb3e014
    // 0xb3e00c: r3 = Null
    //     0xb3e00c: mov             x3, NULL
    // 0xb3e010: b               #0xb3e044
    // 0xb3e014: LoadField: r4 = r3->field_13
    //     0xb3e014: ldur            w4, [x3, #0x13]
    // 0xb3e018: DecompressPointer r4
    //     0xb3e018: add             x4, x4, HEAP, lsl #32
    // 0xb3e01c: cmp             w4, NULL
    // 0xb3e020: b.ne            #0xb3e02c
    // 0xb3e024: r3 = Null
    //     0xb3e024: mov             x3, NULL
    // 0xb3e028: b               #0xb3e044
    // 0xb3e02c: LoadField: r3 = r4->field_7
    //     0xb3e02c: ldur            w3, [x4, #7]
    // 0xb3e030: cbnz            w3, #0xb3e03c
    // 0xb3e034: r4 = false
    //     0xb3e034: add             x4, NULL, #0x30  ; false
    // 0xb3e038: b               #0xb3e040
    // 0xb3e03c: r4 = true
    //     0xb3e03c: add             x4, NULL, #0x20  ; true
    // 0xb3e040: mov             x3, x4
    // 0xb3e044: cmp             w3, NULL
    // 0xb3e048: b.eq            #0xb3e05c
    // 0xb3e04c: tbnz            w3, #4, #0xb3e05c
    // 0xb3e050: r3 = Instance_Color
    //     0xb3e050: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb3e054: ldr             x3, [x3, #0x858]
    // 0xb3e058: b               #0xb3e064
    // 0xb3e05c: r3 = Instance_Color
    //     0xb3e05c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb3e060: ldr             x3, [x3, #0x50]
    // 0xb3e064: stur            x3, [fp, #-0x90]
    // 0xb3e068: r0 = Icon()
    //     0xb3e068: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb3e06c: mov             x1, x0
    // 0xb3e070: ldur            x0, [fp, #-0x98]
    // 0xb3e074: StoreField: r1->field_b = r0
    //     0xb3e074: stur            w0, [x1, #0xb]
    // 0xb3e078: ldur            x0, [fp, #-0x90]
    // 0xb3e07c: StoreField: r1->field_23 = r0
    //     0xb3e07c: stur            w0, [x1, #0x23]
    // 0xb3e080: b               #0xb3e088
    // 0xb3e084: r1 = Null
    //     0xb3e084: mov             x1, NULL
    // 0xb3e088: ldur            x2, [fp, #-8]
    // 0xb3e08c: ldur            x0, [fp, #-0x58]
    // 0xb3e090: ldur            x3, [fp, #-0x20]
    // 0xb3e094: ldur            x16, [fp, #-0x80]
    // 0xb3e098: ldur            lr, [fp, #-0x88]
    // 0xb3e09c: stp             lr, x16, [SP, #0x28]
    // 0xb3e0a0: r16 = Instance_EdgeInsets
    //     0xb3e0a0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb3e0a4: ldr             x16, [x16, #0xa78]
    // 0xb3e0a8: r30 = "Pincode"
    //     0xb3e0a8: add             lr, PP, #0x56, lsl #12  ; [pp+0x56f10] "Pincode"
    //     0xb3e0ac: ldr             lr, [lr, #0xf10]
    // 0xb3e0b0: stp             lr, x16, [SP, #0x18]
    // 0xb3e0b4: ldur            x16, [fp, #-0xa0]
    // 0xb3e0b8: r30 = 4
    //     0xb3e0b8: movz            lr, #0x4
    // 0xb3e0bc: stp             lr, x16, [SP, #8]
    // 0xb3e0c0: str             x1, [SP]
    // 0xb3e0c4: ldur            x1, [fp, #-0x78]
    // 0xb3e0c8: r4 = const [0, 0x8, 0x7, 0x1, contentPadding, 0x3, errorMaxLines, 0x6, errorStyle, 0x2, focusedBorder, 0x1, hintStyle, 0x5, hintText, 0x4, suffixIcon, 0x7, null]
    //     0xb3e0c8: add             x4, PP, #0x56, lsl #12  ; [pp+0x56f18] List(19) [0, 0x8, 0x7, 0x1, "contentPadding", 0x3, "errorMaxLines", 0x6, "errorStyle", 0x2, "focusedBorder", 0x1, "hintStyle", 0x5, "hintText", 0x4, "suffixIcon", 0x7, Null]
    //     0xb3e0cc: ldr             x4, [x4, #0xf18]
    // 0xb3e0d0: r0 = copyWith()
    //     0xb3e0d0: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb3e0d4: ldur            x2, [fp, #-8]
    // 0xb3e0d8: r1 = Function '_validatePinCode@1537065735':.
    //     0xb3e0d8: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f20] AnonymousClosure: (0xb40734), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validatePinCode (0xb40770)
    //     0xb3e0dc: ldr             x1, [x1, #0xf20]
    // 0xb3e0e0: stur            x0, [fp, #-0x78]
    // 0xb3e0e4: r0 = AllocateClosure()
    //     0xb3e0e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3e0e8: ldur            x2, [fp, #-0x18]
    // 0xb3e0ec: r1 = Function '<anonymous closure>':.
    //     0xb3e0ec: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f28] AnonymousClosure: (0xb405d8), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xb3ca88)
    //     0xb3e0f0: ldr             x1, [x1, #0xf28]
    // 0xb3e0f4: stur            x0, [fp, #-0x80]
    // 0xb3e0f8: r0 = AllocateClosure()
    //     0xb3e0f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3e0fc: r1 = <String>
    //     0xb3e0fc: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb3e100: stur            x0, [fp, #-0x88]
    // 0xb3e104: r0 = TextFormField()
    //     0xb3e104: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb3e108: stur            x0, [fp, #-0x90]
    // 0xb3e10c: ldur            x16, [fp, #-0x80]
    // 0xb3e110: r30 = Instance_AutovalidateMode
    //     0xb3e110: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb3e114: ldr             lr, [lr, #0x7e8]
    // 0xb3e118: stp             lr, x16, [SP, #0x40]
    // 0xb3e11c: r16 = true
    //     0xb3e11c: add             x16, NULL, #0x20  ; true
    // 0xb3e120: r30 = true
    //     0xb3e120: add             lr, NULL, #0x20  ; true
    // 0xb3e124: stp             lr, x16, [SP, #0x30]
    // 0xb3e128: ldur            x16, [fp, #-0x50]
    // 0xb3e12c: ldur            lr, [fp, #-0x70]
    // 0xb3e130: stp             lr, x16, [SP, #0x20]
    // 0xb3e134: r16 = Instance_TextInputType
    //     0xb3e134: add             x16, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xb3e138: ldr             x16, [x16, #0x1a0]
    // 0xb3e13c: ldur            lr, [fp, #-0x68]
    // 0xb3e140: stp             lr, x16, [SP, #0x10]
    // 0xb3e144: r16 = 2
    //     0xb3e144: movz            x16, #0x2
    // 0xb3e148: ldur            lr, [fp, #-0x88]
    // 0xb3e14c: stp             lr, x16, [SP]
    // 0xb3e150: mov             x1, x0
    // 0xb3e154: ldur            x2, [fp, #-0x78]
    // 0xb3e158: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x5, autovalidateMode, 0x3, controller, 0x9, enableSuggestions, 0x4, inputFormatters, 0x7, keyboardType, 0x8, maxLines, 0xa, onChanged, 0xb, style, 0x6, validator, 0x2, null]
    //     0xb3e158: add             x4, PP, #0x56, lsl #12  ; [pp+0x56820] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x5, "autovalidateMode", 0x3, "controller", 0x9, "enableSuggestions", 0x4, "inputFormatters", 0x7, "keyboardType", 0x8, "maxLines", 0xa, "onChanged", 0xb, "style", 0x6, "validator", 0x2, Null]
    //     0xb3e15c: ldr             x4, [x4, #0x820]
    // 0xb3e160: r0 = TextFormField()
    //     0xb3e160: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb3e164: r0 = Form()
    //     0xb3e164: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb3e168: mov             x3, x0
    // 0xb3e16c: ldur            x0, [fp, #-0x90]
    // 0xb3e170: stur            x3, [fp, #-0x50]
    // 0xb3e174: StoreField: r3->field_b = r0
    //     0xb3e174: stur            w0, [x3, #0xb]
    // 0xb3e178: r0 = Instance_AutovalidateMode
    //     0xb3e178: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb3e17c: ldr             x0, [x0, #0x800]
    // 0xb3e180: StoreField: r3->field_23 = r0
    //     0xb3e180: stur            w0, [x3, #0x23]
    // 0xb3e184: ldur            x1, [fp, #-0x20]
    // 0xb3e188: StoreField: r3->field_7 = r1
    //     0xb3e188: stur            w1, [x3, #7]
    // 0xb3e18c: r1 = Null
    //     0xb3e18c: mov             x1, NULL
    // 0xb3e190: r2 = 4
    //     0xb3e190: movz            x2, #0x4
    // 0xb3e194: r0 = AllocateArray()
    //     0xb3e194: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3e198: mov             x2, x0
    // 0xb3e19c: ldur            x0, [fp, #-0x58]
    // 0xb3e1a0: stur            x2, [fp, #-0x20]
    // 0xb3e1a4: StoreField: r2->field_f = r0
    //     0xb3e1a4: stur            w0, [x2, #0xf]
    // 0xb3e1a8: ldur            x0, [fp, #-0x50]
    // 0xb3e1ac: StoreField: r2->field_13 = r0
    //     0xb3e1ac: stur            w0, [x2, #0x13]
    // 0xb3e1b0: r1 = <Widget>
    //     0xb3e1b0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3e1b4: r0 = AllocateGrowableArray()
    //     0xb3e1b4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3e1b8: mov             x1, x0
    // 0xb3e1bc: ldur            x0, [fp, #-0x20]
    // 0xb3e1c0: stur            x1, [fp, #-0x50]
    // 0xb3e1c4: StoreField: r1->field_f = r0
    //     0xb3e1c4: stur            w0, [x1, #0xf]
    // 0xb3e1c8: r2 = 4
    //     0xb3e1c8: movz            x2, #0x4
    // 0xb3e1cc: StoreField: r1->field_b = r2
    //     0xb3e1cc: stur            w2, [x1, #0xb]
    // 0xb3e1d0: r0 = Column()
    //     0xb3e1d0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3e1d4: mov             x1, x0
    // 0xb3e1d8: r0 = Instance_Axis
    //     0xb3e1d8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3e1dc: stur            x1, [fp, #-0x20]
    // 0xb3e1e0: StoreField: r1->field_f = r0
    //     0xb3e1e0: stur            w0, [x1, #0xf]
    // 0xb3e1e4: r2 = Instance_MainAxisAlignment
    //     0xb3e1e4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3e1e8: ldr             x2, [x2, #0xa08]
    // 0xb3e1ec: StoreField: r1->field_13 = r2
    //     0xb3e1ec: stur            w2, [x1, #0x13]
    // 0xb3e1f0: r3 = Instance_MainAxisSize
    //     0xb3e1f0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3e1f4: ldr             x3, [x3, #0xa10]
    // 0xb3e1f8: ArrayStore: r1[0] = r3  ; List_4
    //     0xb3e1f8: stur            w3, [x1, #0x17]
    // 0xb3e1fc: r4 = Instance_CrossAxisAlignment
    //     0xb3e1fc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3e200: ldr             x4, [x4, #0x890]
    // 0xb3e204: StoreField: r1->field_1b = r4
    //     0xb3e204: stur            w4, [x1, #0x1b]
    // 0xb3e208: r5 = Instance_VerticalDirection
    //     0xb3e208: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3e20c: ldr             x5, [x5, #0xa20]
    // 0xb3e210: StoreField: r1->field_23 = r5
    //     0xb3e210: stur            w5, [x1, #0x23]
    // 0xb3e214: r6 = Instance_Clip
    //     0xb3e214: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3e218: ldr             x6, [x6, #0x38]
    // 0xb3e21c: StoreField: r1->field_2b = r6
    //     0xb3e21c: stur            w6, [x1, #0x2b]
    // 0xb3e220: StoreField: r1->field_2f = rZR
    //     0xb3e220: stur            xzr, [x1, #0x2f]
    // 0xb3e224: ldur            x7, [fp, #-0x50]
    // 0xb3e228: StoreField: r1->field_b = r7
    //     0xb3e228: stur            w7, [x1, #0xb]
    // 0xb3e22c: r0 = Padding()
    //     0xb3e22c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3e230: mov             x2, x0
    // 0xb3e234: r0 = Instance_EdgeInsets
    //     0xb3e234: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xb3e238: ldr             x0, [x0, #0x858]
    // 0xb3e23c: stur            x2, [fp, #-0x50]
    // 0xb3e240: StoreField: r2->field_f = r0
    //     0xb3e240: stur            w0, [x2, #0xf]
    // 0xb3e244: ldur            x1, [fp, #-0x20]
    // 0xb3e248: StoreField: r2->field_b = r1
    //     0xb3e248: stur            w1, [x2, #0xb]
    // 0xb3e24c: ldur            x1, [fp, #-0x10]
    // 0xb3e250: r0 = of()
    //     0xb3e250: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3e254: LoadField: r1 = r0->field_87
    //     0xb3e254: ldur            w1, [x0, #0x87]
    // 0xb3e258: DecompressPointer r1
    //     0xb3e258: add             x1, x1, HEAP, lsl #32
    // 0xb3e25c: LoadField: r0 = r1->field_2b
    //     0xb3e25c: ldur            w0, [x1, #0x2b]
    // 0xb3e260: DecompressPointer r0
    //     0xb3e260: add             x0, x0, HEAP, lsl #32
    // 0xb3e264: r16 = 12.000000
    //     0xb3e264: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3e268: ldr             x16, [x16, #0x9e8]
    // 0xb3e26c: r30 = Instance_Color
    //     0xb3e26c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3e270: stp             lr, x16, [SP]
    // 0xb3e274: mov             x1, x0
    // 0xb3e278: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3e278: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3e27c: ldr             x4, [x4, #0xaa0]
    // 0xb3e280: r0 = copyWith()
    //     0xb3e280: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3e284: stur            x0, [fp, #-0x20]
    // 0xb3e288: r0 = Text()
    //     0xb3e288: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3e28c: mov             x2, x0
    // 0xb3e290: r0 = "City*"
    //     0xb3e290: add             x0, PP, #0x54, lsl #12  ; [pp+0x540f0] "City*"
    //     0xb3e294: ldr             x0, [x0, #0xf0]
    // 0xb3e298: stur            x2, [fp, #-0x68]
    // 0xb3e29c: StoreField: r2->field_b = r0
    //     0xb3e29c: stur            w0, [x2, #0xb]
    // 0xb3e2a0: ldur            x0, [fp, #-0x20]
    // 0xb3e2a4: StoreField: r2->field_13 = r0
    //     0xb3e2a4: stur            w0, [x2, #0x13]
    // 0xb3e2a8: ldur            x0, [fp, #-8]
    // 0xb3e2ac: LoadField: r3 = r0->field_23
    //     0xb3e2ac: ldur            w3, [x0, #0x23]
    // 0xb3e2b0: DecompressPointer r3
    //     0xb3e2b0: add             x3, x3, HEAP, lsl #32
    // 0xb3e2b4: stur            x3, [fp, #-0x58]
    // 0xb3e2b8: LoadField: r1 = r0->field_3f
    //     0xb3e2b8: ldur            w1, [x0, #0x3f]
    // 0xb3e2bc: DecompressPointer r1
    //     0xb3e2bc: add             x1, x1, HEAP, lsl #32
    // 0xb3e2c0: LoadField: r4 = r1->field_27
    //     0xb3e2c0: ldur            w4, [x1, #0x27]
    // 0xb3e2c4: DecompressPointer r4
    //     0xb3e2c4: add             x4, x4, HEAP, lsl #32
    // 0xb3e2c8: LoadField: r1 = r4->field_7
    //     0xb3e2c8: ldur            w1, [x4, #7]
    // 0xb3e2cc: DecompressPointer r1
    //     0xb3e2cc: add             x1, x1, HEAP, lsl #32
    // 0xb3e2d0: LoadField: r4 = r1->field_7
    //     0xb3e2d0: ldur            w4, [x1, #7]
    // 0xb3e2d4: cmp             w4, #0xc
    // 0xb3e2d8: b.ne            #0xb3e320
    // 0xb3e2dc: LoadField: r1 = r0->field_b
    //     0xb3e2dc: ldur            w1, [x0, #0xb]
    // 0xb3e2e0: DecompressPointer r1
    //     0xb3e2e0: add             x1, x1, HEAP, lsl #32
    // 0xb3e2e4: cmp             w1, NULL
    // 0xb3e2e8: b.eq            #0xb3fd90
    // 0xb3e2ec: LoadField: r4 = r1->field_13
    //     0xb3e2ec: ldur            w4, [x1, #0x13]
    // 0xb3e2f0: DecompressPointer r4
    //     0xb3e2f0: add             x4, x4, HEAP, lsl #32
    // 0xb3e2f4: LoadField: r1 = r4->field_b
    //     0xb3e2f4: ldur            w1, [x4, #0xb]
    // 0xb3e2f8: DecompressPointer r1
    //     0xb3e2f8: add             x1, x1, HEAP, lsl #32
    // 0xb3e2fc: cmp             w1, NULL
    // 0xb3e300: b.ne            #0xb3e30c
    // 0xb3e304: r1 = Null
    //     0xb3e304: mov             x1, NULL
    // 0xb3e308: b               #0xb3e318
    // 0xb3e30c: LoadField: r4 = r1->field_13
    //     0xb3e30c: ldur            w4, [x1, #0x13]
    // 0xb3e310: DecompressPointer r4
    //     0xb3e310: add             x4, x4, HEAP, lsl #32
    // 0xb3e314: mov             x1, x4
    // 0xb3e318: mov             x4, x1
    // 0xb3e31c: b               #0xb3e324
    // 0xb3e320: r4 = ""
    //     0xb3e320: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3e324: stur            x4, [fp, #-0x20]
    // 0xb3e328: r1 = <TextEditingValue>
    //     0xb3e328: ldr             x1, [PP, #0x6c80]  ; [pp+0x6c80] TypeArguments: <TextEditingValue>
    // 0xb3e32c: r0 = TextEditingController()
    //     0xb3e32c: bl              #0x905a14  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0xb3e330: stur            x0, [fp, #-0x70]
    // 0xb3e334: ldur            x16, [fp, #-0x20]
    // 0xb3e338: str             x16, [SP]
    // 0xb3e33c: mov             x1, x0
    // 0xb3e340: r4 = const [0, 0x2, 0x1, 0x1, text, 0x1, null]
    //     0xb3e340: add             x4, PP, #0x33, lsl #12  ; [pp+0x33c40] List(7) [0, 0x2, 0x1, 0x1, "text", 0x1, Null]
    //     0xb3e344: ldr             x4, [x4, #0xc40]
    // 0xb3e348: r0 = TextEditingController()
    //     0xb3e348: bl              #0x905904  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0xb3e34c: ldur            x1, [fp, #-0x10]
    // 0xb3e350: r0 = of()
    //     0xb3e350: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3e354: LoadField: r1 = r0->field_87
    //     0xb3e354: ldur            w1, [x0, #0x87]
    // 0xb3e358: DecompressPointer r1
    //     0xb3e358: add             x1, x1, HEAP, lsl #32
    // 0xb3e35c: LoadField: r0 = r1->field_2b
    //     0xb3e35c: ldur            w0, [x1, #0x2b]
    // 0xb3e360: DecompressPointer r0
    //     0xb3e360: add             x0, x0, HEAP, lsl #32
    // 0xb3e364: ldur            x1, [fp, #-0x10]
    // 0xb3e368: stur            x0, [fp, #-0x20]
    // 0xb3e36c: r0 = of()
    //     0xb3e36c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3e370: LoadField: r1 = r0->field_5b
    //     0xb3e370: ldur            w1, [x0, #0x5b]
    // 0xb3e374: DecompressPointer r1
    //     0xb3e374: add             x1, x1, HEAP, lsl #32
    // 0xb3e378: r16 = 14.000000
    //     0xb3e378: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3e37c: ldr             x16, [x16, #0x1d8]
    // 0xb3e380: stp             x1, x16, [SP]
    // 0xb3e384: ldur            x1, [fp, #-0x20]
    // 0xb3e388: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3e388: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3e38c: ldr             x4, [x4, #0xaa0]
    // 0xb3e390: r0 = copyWith()
    //     0xb3e390: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3e394: ldur            x1, [fp, #-0x10]
    // 0xb3e398: stur            x0, [fp, #-0x20]
    // 0xb3e39c: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb3e39c: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb3e3a0: r1 = Instance_Color
    //     0xb3e3a0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3e3a4: d0 = 0.100000
    //     0xb3e3a4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb3e3a8: stur            x0, [fp, #-0x78]
    // 0xb3e3ac: r0 = withOpacity()
    //     0xb3e3ac: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb3e3b0: ldur            x1, [fp, #-0x10]
    // 0xb3e3b4: stur            x0, [fp, #-0x80]
    // 0xb3e3b8: r0 = of()
    //     0xb3e3b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3e3bc: LoadField: r1 = r0->field_87
    //     0xb3e3bc: ldur            w1, [x0, #0x87]
    // 0xb3e3c0: DecompressPointer r1
    //     0xb3e3c0: add             x1, x1, HEAP, lsl #32
    // 0xb3e3c4: LoadField: r0 = r1->field_2b
    //     0xb3e3c4: ldur            w0, [x1, #0x2b]
    // 0xb3e3c8: DecompressPointer r0
    //     0xb3e3c8: add             x0, x0, HEAP, lsl #32
    // 0xb3e3cc: stur            x0, [fp, #-0x88]
    // 0xb3e3d0: r1 = Instance_Color
    //     0xb3e3d0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3e3d4: d0 = 0.400000
    //     0xb3e3d4: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb3e3d8: r0 = withOpacity()
    //     0xb3e3d8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb3e3dc: r16 = 14.000000
    //     0xb3e3dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3e3e0: ldr             x16, [x16, #0x1d8]
    // 0xb3e3e4: stp             x0, x16, [SP]
    // 0xb3e3e8: ldur            x1, [fp, #-0x88]
    // 0xb3e3ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3e3ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3e3f0: ldr             x4, [x4, #0xaa0]
    // 0xb3e3f4: r0 = copyWith()
    //     0xb3e3f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3e3f8: ldur            x2, [fp, #-8]
    // 0xb3e3fc: stur            x0, [fp, #-0x98]
    // 0xb3e400: LoadField: r1 = r2->field_5b
    //     0xb3e400: ldur            w1, [x2, #0x5b]
    // 0xb3e404: DecompressPointer r1
    //     0xb3e404: add             x1, x1, HEAP, lsl #32
    // 0xb3e408: tbnz            w1, #4, #0xb3e55c
    // 0xb3e40c: LoadField: r1 = r2->field_3f
    //     0xb3e40c: ldur            w1, [x2, #0x3f]
    // 0xb3e410: DecompressPointer r1
    //     0xb3e410: add             x1, x1, HEAP, lsl #32
    // 0xb3e414: LoadField: r3 = r1->field_27
    //     0xb3e414: ldur            w3, [x1, #0x27]
    // 0xb3e418: DecompressPointer r3
    //     0xb3e418: add             x3, x3, HEAP, lsl #32
    // 0xb3e41c: LoadField: r1 = r3->field_7
    //     0xb3e41c: ldur            w1, [x3, #7]
    // 0xb3e420: DecompressPointer r1
    //     0xb3e420: add             x1, x1, HEAP, lsl #32
    // 0xb3e424: LoadField: r3 = r1->field_7
    //     0xb3e424: ldur            w3, [x1, #7]
    // 0xb3e428: cmp             w3, #0xc
    // 0xb3e42c: b.ne            #0xb3e4a8
    // 0xb3e430: LoadField: r1 = r2->field_b
    //     0xb3e430: ldur            w1, [x2, #0xb]
    // 0xb3e434: DecompressPointer r1
    //     0xb3e434: add             x1, x1, HEAP, lsl #32
    // 0xb3e438: cmp             w1, NULL
    // 0xb3e43c: b.eq            #0xb3fd94
    // 0xb3e440: LoadField: r4 = r1->field_13
    //     0xb3e440: ldur            w4, [x1, #0x13]
    // 0xb3e444: DecompressPointer r4
    //     0xb3e444: add             x4, x4, HEAP, lsl #32
    // 0xb3e448: LoadField: r1 = r4->field_b
    //     0xb3e448: ldur            w1, [x4, #0xb]
    // 0xb3e44c: DecompressPointer r1
    //     0xb3e44c: add             x1, x1, HEAP, lsl #32
    // 0xb3e450: cmp             w1, NULL
    // 0xb3e454: b.ne            #0xb3e460
    // 0xb3e458: r1 = Null
    //     0xb3e458: mov             x1, NULL
    // 0xb3e45c: b               #0xb3e490
    // 0xb3e460: LoadField: r4 = r1->field_13
    //     0xb3e460: ldur            w4, [x1, #0x13]
    // 0xb3e464: DecompressPointer r4
    //     0xb3e464: add             x4, x4, HEAP, lsl #32
    // 0xb3e468: cmp             w4, NULL
    // 0xb3e46c: b.ne            #0xb3e478
    // 0xb3e470: r1 = Null
    //     0xb3e470: mov             x1, NULL
    // 0xb3e474: b               #0xb3e490
    // 0xb3e478: LoadField: r1 = r4->field_7
    //     0xb3e478: ldur            w1, [x4, #7]
    // 0xb3e47c: cbnz            w1, #0xb3e488
    // 0xb3e480: r4 = false
    //     0xb3e480: add             x4, NULL, #0x30  ; false
    // 0xb3e484: b               #0xb3e48c
    // 0xb3e488: r4 = true
    //     0xb3e488: add             x4, NULL, #0x20  ; true
    // 0xb3e48c: mov             x1, x4
    // 0xb3e490: cmp             w1, NULL
    // 0xb3e494: b.eq            #0xb3e4a8
    // 0xb3e498: tbnz            w1, #4, #0xb3e4a8
    // 0xb3e49c: r1 = Instance_IconData
    //     0xb3e49c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb3e4a0: ldr             x1, [x1, #0x130]
    // 0xb3e4a4: b               #0xb3e4b0
    // 0xb3e4a8: r1 = Instance_IconData
    //     0xb3e4a8: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb3e4ac: ldr             x1, [x1, #0x138]
    // 0xb3e4b0: stur            x1, [fp, #-0x90]
    // 0xb3e4b4: cmp             w3, #0xc
    // 0xb3e4b8: b.ne            #0xb3e534
    // 0xb3e4bc: LoadField: r3 = r2->field_b
    //     0xb3e4bc: ldur            w3, [x2, #0xb]
    // 0xb3e4c0: DecompressPointer r3
    //     0xb3e4c0: add             x3, x3, HEAP, lsl #32
    // 0xb3e4c4: cmp             w3, NULL
    // 0xb3e4c8: b.eq            #0xb3fd98
    // 0xb3e4cc: LoadField: r4 = r3->field_13
    //     0xb3e4cc: ldur            w4, [x3, #0x13]
    // 0xb3e4d0: DecompressPointer r4
    //     0xb3e4d0: add             x4, x4, HEAP, lsl #32
    // 0xb3e4d4: LoadField: r3 = r4->field_b
    //     0xb3e4d4: ldur            w3, [x4, #0xb]
    // 0xb3e4d8: DecompressPointer r3
    //     0xb3e4d8: add             x3, x3, HEAP, lsl #32
    // 0xb3e4dc: cmp             w3, NULL
    // 0xb3e4e0: b.ne            #0xb3e4ec
    // 0xb3e4e4: r3 = Null
    //     0xb3e4e4: mov             x3, NULL
    // 0xb3e4e8: b               #0xb3e51c
    // 0xb3e4ec: LoadField: r4 = r3->field_13
    //     0xb3e4ec: ldur            w4, [x3, #0x13]
    // 0xb3e4f0: DecompressPointer r4
    //     0xb3e4f0: add             x4, x4, HEAP, lsl #32
    // 0xb3e4f4: cmp             w4, NULL
    // 0xb3e4f8: b.ne            #0xb3e504
    // 0xb3e4fc: r3 = Null
    //     0xb3e4fc: mov             x3, NULL
    // 0xb3e500: b               #0xb3e51c
    // 0xb3e504: LoadField: r3 = r4->field_7
    //     0xb3e504: ldur            w3, [x4, #7]
    // 0xb3e508: cbnz            w3, #0xb3e514
    // 0xb3e50c: r4 = false
    //     0xb3e50c: add             x4, NULL, #0x30  ; false
    // 0xb3e510: b               #0xb3e518
    // 0xb3e514: r4 = true
    //     0xb3e514: add             x4, NULL, #0x20  ; true
    // 0xb3e518: mov             x3, x4
    // 0xb3e51c: cmp             w3, NULL
    // 0xb3e520: b.eq            #0xb3e534
    // 0xb3e524: tbnz            w3, #4, #0xb3e534
    // 0xb3e528: r3 = Instance_Color
    //     0xb3e528: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb3e52c: ldr             x3, [x3, #0x858]
    // 0xb3e530: b               #0xb3e53c
    // 0xb3e534: r3 = Instance_Color
    //     0xb3e534: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb3e538: ldr             x3, [x3, #0x50]
    // 0xb3e53c: stur            x3, [fp, #-0x88]
    // 0xb3e540: r0 = Icon()
    //     0xb3e540: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb3e544: mov             x1, x0
    // 0xb3e548: ldur            x0, [fp, #-0x90]
    // 0xb3e54c: StoreField: r1->field_b = r0
    //     0xb3e54c: stur            w0, [x1, #0xb]
    // 0xb3e550: ldur            x0, [fp, #-0x88]
    // 0xb3e554: StoreField: r1->field_23 = r0
    //     0xb3e554: stur            w0, [x1, #0x23]
    // 0xb3e558: b               #0xb3e560
    // 0xb3e55c: r1 = Null
    //     0xb3e55c: mov             x1, NULL
    // 0xb3e560: ldur            x2, [fp, #-8]
    // 0xb3e564: ldur            x0, [fp, #-0x68]
    // 0xb3e568: ldur            x3, [fp, #-0x58]
    // 0xb3e56c: ldur            x16, [fp, #-0x80]
    // 0xb3e570: r30 = "City*"
    //     0xb3e570: add             lr, PP, #0x54, lsl #12  ; [pp+0x540f0] "City*"
    //     0xb3e574: ldr             lr, [lr, #0xf0]
    // 0xb3e578: stp             lr, x16, [SP, #0x10]
    // 0xb3e57c: ldur            x16, [fp, #-0x98]
    // 0xb3e580: stp             x1, x16, [SP]
    // 0xb3e584: ldur            x1, [fp, #-0x78]
    // 0xb3e588: r4 = const [0, 0x5, 0x4, 0x1, fillColor, 0x1, hintStyle, 0x3, hintText, 0x2, suffixIcon, 0x4, null]
    //     0xb3e588: add             x4, PP, #0x56, lsl #12  ; [pp+0x56860] List(13) [0, 0x5, 0x4, 0x1, "fillColor", 0x1, "hintStyle", 0x3, "hintText", 0x2, "suffixIcon", 0x4, Null]
    //     0xb3e58c: ldr             x4, [x4, #0x860]
    // 0xb3e590: r0 = copyWith()
    //     0xb3e590: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb3e594: r1 = <String>
    //     0xb3e594: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb3e598: stur            x0, [fp, #-0x78]
    // 0xb3e59c: r0 = TextFormField()
    //     0xb3e59c: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb3e5a0: stur            x0, [fp, #-0x80]
    // 0xb3e5a4: r16 = true
    //     0xb3e5a4: add             x16, NULL, #0x20  ; true
    // 0xb3e5a8: r30 = true
    //     0xb3e5a8: add             lr, NULL, #0x20  ; true
    // 0xb3e5ac: stp             lr, x16, [SP, #0x10]
    // 0xb3e5b0: ldur            x16, [fp, #-0x70]
    // 0xb3e5b4: ldur            lr, [fp, #-0x20]
    // 0xb3e5b8: stp             lr, x16, [SP]
    // 0xb3e5bc: mov             x1, x0
    // 0xb3e5c0: ldur            x2, [fp, #-0x78]
    // 0xb3e5c4: r4 = const [0, 0x6, 0x4, 0x2, controller, 0x4, ignorePointers, 0x3, readOnly, 0x2, style, 0x5, null]
    //     0xb3e5c4: add             x4, PP, #0x54, lsl #12  ; [pp+0x54100] List(13) [0, 0x6, 0x4, 0x2, "controller", 0x4, "ignorePointers", 0x3, "readOnly", 0x2, "style", 0x5, Null]
    //     0xb3e5c8: ldr             x4, [x4, #0x100]
    // 0xb3e5cc: r0 = TextFormField()
    //     0xb3e5cc: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb3e5d0: r0 = Form()
    //     0xb3e5d0: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb3e5d4: mov             x3, x0
    // 0xb3e5d8: ldur            x0, [fp, #-0x80]
    // 0xb3e5dc: stur            x3, [fp, #-0x20]
    // 0xb3e5e0: StoreField: r3->field_b = r0
    //     0xb3e5e0: stur            w0, [x3, #0xb]
    // 0xb3e5e4: r0 = Instance_AutovalidateMode
    //     0xb3e5e4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb3e5e8: ldr             x0, [x0, #0x800]
    // 0xb3e5ec: StoreField: r3->field_23 = r0
    //     0xb3e5ec: stur            w0, [x3, #0x23]
    // 0xb3e5f0: ldur            x1, [fp, #-0x58]
    // 0xb3e5f4: StoreField: r3->field_7 = r1
    //     0xb3e5f4: stur            w1, [x3, #7]
    // 0xb3e5f8: r1 = Null
    //     0xb3e5f8: mov             x1, NULL
    // 0xb3e5fc: r2 = 4
    //     0xb3e5fc: movz            x2, #0x4
    // 0xb3e600: r0 = AllocateArray()
    //     0xb3e600: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3e604: mov             x2, x0
    // 0xb3e608: ldur            x0, [fp, #-0x68]
    // 0xb3e60c: stur            x2, [fp, #-0x58]
    // 0xb3e610: StoreField: r2->field_f = r0
    //     0xb3e610: stur            w0, [x2, #0xf]
    // 0xb3e614: ldur            x0, [fp, #-0x20]
    // 0xb3e618: StoreField: r2->field_13 = r0
    //     0xb3e618: stur            w0, [x2, #0x13]
    // 0xb3e61c: r1 = <Widget>
    //     0xb3e61c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3e620: r0 = AllocateGrowableArray()
    //     0xb3e620: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3e624: mov             x1, x0
    // 0xb3e628: ldur            x0, [fp, #-0x58]
    // 0xb3e62c: stur            x1, [fp, #-0x20]
    // 0xb3e630: StoreField: r1->field_f = r0
    //     0xb3e630: stur            w0, [x1, #0xf]
    // 0xb3e634: r2 = 4
    //     0xb3e634: movz            x2, #0x4
    // 0xb3e638: StoreField: r1->field_b = r2
    //     0xb3e638: stur            w2, [x1, #0xb]
    // 0xb3e63c: r0 = Column()
    //     0xb3e63c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3e640: mov             x2, x0
    // 0xb3e644: r0 = Instance_Axis
    //     0xb3e644: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3e648: stur            x2, [fp, #-0x58]
    // 0xb3e64c: StoreField: r2->field_f = r0
    //     0xb3e64c: stur            w0, [x2, #0xf]
    // 0xb3e650: r3 = Instance_MainAxisAlignment
    //     0xb3e650: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3e654: ldr             x3, [x3, #0xa08]
    // 0xb3e658: StoreField: r2->field_13 = r3
    //     0xb3e658: stur            w3, [x2, #0x13]
    // 0xb3e65c: r4 = Instance_MainAxisSize
    //     0xb3e65c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3e660: ldr             x4, [x4, #0xa10]
    // 0xb3e664: ArrayStore: r2[0] = r4  ; List_4
    //     0xb3e664: stur            w4, [x2, #0x17]
    // 0xb3e668: r5 = Instance_CrossAxisAlignment
    //     0xb3e668: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3e66c: ldr             x5, [x5, #0x890]
    // 0xb3e670: StoreField: r2->field_1b = r5
    //     0xb3e670: stur            w5, [x2, #0x1b]
    // 0xb3e674: r6 = Instance_VerticalDirection
    //     0xb3e674: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3e678: ldr             x6, [x6, #0xa20]
    // 0xb3e67c: StoreField: r2->field_23 = r6
    //     0xb3e67c: stur            w6, [x2, #0x23]
    // 0xb3e680: r7 = Instance_Clip
    //     0xb3e680: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3e684: ldr             x7, [x7, #0x38]
    // 0xb3e688: StoreField: r2->field_2b = r7
    //     0xb3e688: stur            w7, [x2, #0x2b]
    // 0xb3e68c: StoreField: r2->field_2f = rZR
    //     0xb3e68c: stur            xzr, [x2, #0x2f]
    // 0xb3e690: ldur            x1, [fp, #-0x20]
    // 0xb3e694: StoreField: r2->field_b = r1
    //     0xb3e694: stur            w1, [x2, #0xb]
    // 0xb3e698: r1 = <FlexParentData>
    //     0xb3e698: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb3e69c: ldr             x1, [x1, #0xe00]
    // 0xb3e6a0: r0 = Flexible()
    //     0xb3e6a0: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xb3e6a4: mov             x2, x0
    // 0xb3e6a8: r0 = 1
    //     0xb3e6a8: movz            x0, #0x1
    // 0xb3e6ac: stur            x2, [fp, #-0x20]
    // 0xb3e6b0: StoreField: r2->field_13 = r0
    //     0xb3e6b0: stur            x0, [x2, #0x13]
    // 0xb3e6b4: r3 = Instance_FlexFit
    //     0xb3e6b4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb3e6b8: ldr             x3, [x3, #0xe08]
    // 0xb3e6bc: StoreField: r2->field_1b = r3
    //     0xb3e6bc: stur            w3, [x2, #0x1b]
    // 0xb3e6c0: ldur            x1, [fp, #-0x58]
    // 0xb3e6c4: StoreField: r2->field_b = r1
    //     0xb3e6c4: stur            w1, [x2, #0xb]
    // 0xb3e6c8: ldur            x1, [fp, #-0x10]
    // 0xb3e6cc: r0 = of()
    //     0xb3e6cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3e6d0: LoadField: r1 = r0->field_87
    //     0xb3e6d0: ldur            w1, [x0, #0x87]
    // 0xb3e6d4: DecompressPointer r1
    //     0xb3e6d4: add             x1, x1, HEAP, lsl #32
    // 0xb3e6d8: LoadField: r0 = r1->field_2b
    //     0xb3e6d8: ldur            w0, [x1, #0x2b]
    // 0xb3e6dc: DecompressPointer r0
    //     0xb3e6dc: add             x0, x0, HEAP, lsl #32
    // 0xb3e6e0: r16 = 12.000000
    //     0xb3e6e0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3e6e4: ldr             x16, [x16, #0x9e8]
    // 0xb3e6e8: r30 = Instance_Color
    //     0xb3e6e8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3e6ec: stp             lr, x16, [SP]
    // 0xb3e6f0: mov             x1, x0
    // 0xb3e6f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3e6f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3e6f8: ldr             x4, [x4, #0xaa0]
    // 0xb3e6fc: r0 = copyWith()
    //     0xb3e6fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3e700: stur            x0, [fp, #-0x58]
    // 0xb3e704: r0 = Text()
    //     0xb3e704: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3e708: mov             x2, x0
    // 0xb3e70c: r0 = "State*"
    //     0xb3e70c: add             x0, PP, #0x54, lsl #12  ; [pp+0x54108] "State*"
    //     0xb3e710: ldr             x0, [x0, #0x108]
    // 0xb3e714: stur            x2, [fp, #-0x70]
    // 0xb3e718: StoreField: r2->field_b = r0
    //     0xb3e718: stur            w0, [x2, #0xb]
    // 0xb3e71c: ldur            x0, [fp, #-0x58]
    // 0xb3e720: StoreField: r2->field_13 = r0
    //     0xb3e720: stur            w0, [x2, #0x13]
    // 0xb3e724: ldur            x0, [fp, #-8]
    // 0xb3e728: LoadField: r3 = r0->field_27
    //     0xb3e728: ldur            w3, [x0, #0x27]
    // 0xb3e72c: DecompressPointer r3
    //     0xb3e72c: add             x3, x3, HEAP, lsl #32
    // 0xb3e730: stur            x3, [fp, #-0x68]
    // 0xb3e734: LoadField: r1 = r0->field_3f
    //     0xb3e734: ldur            w1, [x0, #0x3f]
    // 0xb3e738: DecompressPointer r1
    //     0xb3e738: add             x1, x1, HEAP, lsl #32
    // 0xb3e73c: LoadField: r4 = r1->field_27
    //     0xb3e73c: ldur            w4, [x1, #0x27]
    // 0xb3e740: DecompressPointer r4
    //     0xb3e740: add             x4, x4, HEAP, lsl #32
    // 0xb3e744: LoadField: r1 = r4->field_7
    //     0xb3e744: ldur            w1, [x4, #7]
    // 0xb3e748: DecompressPointer r1
    //     0xb3e748: add             x1, x1, HEAP, lsl #32
    // 0xb3e74c: LoadField: r4 = r1->field_7
    //     0xb3e74c: ldur            w4, [x1, #7]
    // 0xb3e750: cmp             w4, #0xc
    // 0xb3e754: b.ne            #0xb3e79c
    // 0xb3e758: LoadField: r1 = r0->field_b
    //     0xb3e758: ldur            w1, [x0, #0xb]
    // 0xb3e75c: DecompressPointer r1
    //     0xb3e75c: add             x1, x1, HEAP, lsl #32
    // 0xb3e760: cmp             w1, NULL
    // 0xb3e764: b.eq            #0xb3fd9c
    // 0xb3e768: LoadField: r4 = r1->field_13
    //     0xb3e768: ldur            w4, [x1, #0x13]
    // 0xb3e76c: DecompressPointer r4
    //     0xb3e76c: add             x4, x4, HEAP, lsl #32
    // 0xb3e770: LoadField: r1 = r4->field_b
    //     0xb3e770: ldur            w1, [x4, #0xb]
    // 0xb3e774: DecompressPointer r1
    //     0xb3e774: add             x1, x1, HEAP, lsl #32
    // 0xb3e778: cmp             w1, NULL
    // 0xb3e77c: b.ne            #0xb3e788
    // 0xb3e780: r1 = Null
    //     0xb3e780: mov             x1, NULL
    // 0xb3e784: b               #0xb3e794
    // 0xb3e788: LoadField: r4 = r1->field_f
    //     0xb3e788: ldur            w4, [x1, #0xf]
    // 0xb3e78c: DecompressPointer r4
    //     0xb3e78c: add             x4, x4, HEAP, lsl #32
    // 0xb3e790: mov             x1, x4
    // 0xb3e794: mov             x4, x1
    // 0xb3e798: b               #0xb3e7a0
    // 0xb3e79c: r4 = ""
    //     0xb3e79c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3e7a0: stur            x4, [fp, #-0x58]
    // 0xb3e7a4: r1 = <TextEditingValue>
    //     0xb3e7a4: ldr             x1, [PP, #0x6c80]  ; [pp+0x6c80] TypeArguments: <TextEditingValue>
    // 0xb3e7a8: r0 = TextEditingController()
    //     0xb3e7a8: bl              #0x905a14  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0xb3e7ac: stur            x0, [fp, #-0x78]
    // 0xb3e7b0: ldur            x16, [fp, #-0x58]
    // 0xb3e7b4: str             x16, [SP]
    // 0xb3e7b8: mov             x1, x0
    // 0xb3e7bc: r4 = const [0, 0x2, 0x1, 0x1, text, 0x1, null]
    //     0xb3e7bc: add             x4, PP, #0x33, lsl #12  ; [pp+0x33c40] List(7) [0, 0x2, 0x1, 0x1, "text", 0x1, Null]
    //     0xb3e7c0: ldr             x4, [x4, #0xc40]
    // 0xb3e7c4: r0 = TextEditingController()
    //     0xb3e7c4: bl              #0x905904  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0xb3e7c8: ldur            x1, [fp, #-0x10]
    // 0xb3e7cc: r0 = of()
    //     0xb3e7cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3e7d0: LoadField: r1 = r0->field_87
    //     0xb3e7d0: ldur            w1, [x0, #0x87]
    // 0xb3e7d4: DecompressPointer r1
    //     0xb3e7d4: add             x1, x1, HEAP, lsl #32
    // 0xb3e7d8: LoadField: r0 = r1->field_2b
    //     0xb3e7d8: ldur            w0, [x1, #0x2b]
    // 0xb3e7dc: DecompressPointer r0
    //     0xb3e7dc: add             x0, x0, HEAP, lsl #32
    // 0xb3e7e0: ldur            x1, [fp, #-0x10]
    // 0xb3e7e4: stur            x0, [fp, #-0x58]
    // 0xb3e7e8: r0 = of()
    //     0xb3e7e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3e7ec: LoadField: r1 = r0->field_5b
    //     0xb3e7ec: ldur            w1, [x0, #0x5b]
    // 0xb3e7f0: DecompressPointer r1
    //     0xb3e7f0: add             x1, x1, HEAP, lsl #32
    // 0xb3e7f4: r16 = 14.000000
    //     0xb3e7f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3e7f8: ldr             x16, [x16, #0x1d8]
    // 0xb3e7fc: stp             x1, x16, [SP]
    // 0xb3e800: ldur            x1, [fp, #-0x58]
    // 0xb3e804: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3e804: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3e808: ldr             x4, [x4, #0xaa0]
    // 0xb3e80c: r0 = copyWith()
    //     0xb3e80c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3e810: ldur            x1, [fp, #-0x10]
    // 0xb3e814: stur            x0, [fp, #-0x58]
    // 0xb3e818: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb3e818: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb3e81c: r1 = Instance_Color
    //     0xb3e81c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3e820: d0 = 0.100000
    //     0xb3e820: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb3e824: stur            x0, [fp, #-0x80]
    // 0xb3e828: r0 = withOpacity()
    //     0xb3e828: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb3e82c: ldur            x1, [fp, #-0x10]
    // 0xb3e830: stur            x0, [fp, #-0x88]
    // 0xb3e834: r0 = of()
    //     0xb3e834: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3e838: LoadField: r1 = r0->field_87
    //     0xb3e838: ldur            w1, [x0, #0x87]
    // 0xb3e83c: DecompressPointer r1
    //     0xb3e83c: add             x1, x1, HEAP, lsl #32
    // 0xb3e840: LoadField: r0 = r1->field_2b
    //     0xb3e840: ldur            w0, [x1, #0x2b]
    // 0xb3e844: DecompressPointer r0
    //     0xb3e844: add             x0, x0, HEAP, lsl #32
    // 0xb3e848: stur            x0, [fp, #-0x90]
    // 0xb3e84c: r1 = Instance_Color
    //     0xb3e84c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3e850: d0 = 0.400000
    //     0xb3e850: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb3e854: r0 = withOpacity()
    //     0xb3e854: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb3e858: r16 = 14.000000
    //     0xb3e858: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3e85c: ldr             x16, [x16, #0x1d8]
    // 0xb3e860: stp             x0, x16, [SP]
    // 0xb3e864: ldur            x1, [fp, #-0x90]
    // 0xb3e868: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3e868: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3e86c: ldr             x4, [x4, #0xaa0]
    // 0xb3e870: r0 = copyWith()
    //     0xb3e870: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3e874: ldur            x2, [fp, #-8]
    // 0xb3e878: stur            x0, [fp, #-0xa0]
    // 0xb3e87c: LoadField: r1 = r2->field_5b
    //     0xb3e87c: ldur            w1, [x2, #0x5b]
    // 0xb3e880: DecompressPointer r1
    //     0xb3e880: add             x1, x1, HEAP, lsl #32
    // 0xb3e884: tbnz            w1, #4, #0xb3e9d8
    // 0xb3e888: LoadField: r1 = r2->field_3f
    //     0xb3e888: ldur            w1, [x2, #0x3f]
    // 0xb3e88c: DecompressPointer r1
    //     0xb3e88c: add             x1, x1, HEAP, lsl #32
    // 0xb3e890: LoadField: r3 = r1->field_27
    //     0xb3e890: ldur            w3, [x1, #0x27]
    // 0xb3e894: DecompressPointer r3
    //     0xb3e894: add             x3, x3, HEAP, lsl #32
    // 0xb3e898: LoadField: r1 = r3->field_7
    //     0xb3e898: ldur            w1, [x3, #7]
    // 0xb3e89c: DecompressPointer r1
    //     0xb3e89c: add             x1, x1, HEAP, lsl #32
    // 0xb3e8a0: LoadField: r3 = r1->field_7
    //     0xb3e8a0: ldur            w3, [x1, #7]
    // 0xb3e8a4: cmp             w3, #0xc
    // 0xb3e8a8: b.ne            #0xb3e924
    // 0xb3e8ac: LoadField: r1 = r2->field_b
    //     0xb3e8ac: ldur            w1, [x2, #0xb]
    // 0xb3e8b0: DecompressPointer r1
    //     0xb3e8b0: add             x1, x1, HEAP, lsl #32
    // 0xb3e8b4: cmp             w1, NULL
    // 0xb3e8b8: b.eq            #0xb3fda0
    // 0xb3e8bc: LoadField: r4 = r1->field_13
    //     0xb3e8bc: ldur            w4, [x1, #0x13]
    // 0xb3e8c0: DecompressPointer r4
    //     0xb3e8c0: add             x4, x4, HEAP, lsl #32
    // 0xb3e8c4: LoadField: r1 = r4->field_b
    //     0xb3e8c4: ldur            w1, [x4, #0xb]
    // 0xb3e8c8: DecompressPointer r1
    //     0xb3e8c8: add             x1, x1, HEAP, lsl #32
    // 0xb3e8cc: cmp             w1, NULL
    // 0xb3e8d0: b.ne            #0xb3e8dc
    // 0xb3e8d4: r1 = Null
    //     0xb3e8d4: mov             x1, NULL
    // 0xb3e8d8: b               #0xb3e90c
    // 0xb3e8dc: LoadField: r4 = r1->field_13
    //     0xb3e8dc: ldur            w4, [x1, #0x13]
    // 0xb3e8e0: DecompressPointer r4
    //     0xb3e8e0: add             x4, x4, HEAP, lsl #32
    // 0xb3e8e4: cmp             w4, NULL
    // 0xb3e8e8: b.ne            #0xb3e8f4
    // 0xb3e8ec: r1 = Null
    //     0xb3e8ec: mov             x1, NULL
    // 0xb3e8f0: b               #0xb3e90c
    // 0xb3e8f4: LoadField: r1 = r4->field_7
    //     0xb3e8f4: ldur            w1, [x4, #7]
    // 0xb3e8f8: cbnz            w1, #0xb3e904
    // 0xb3e8fc: r4 = false
    //     0xb3e8fc: add             x4, NULL, #0x30  ; false
    // 0xb3e900: b               #0xb3e908
    // 0xb3e904: r4 = true
    //     0xb3e904: add             x4, NULL, #0x20  ; true
    // 0xb3e908: mov             x1, x4
    // 0xb3e90c: cmp             w1, NULL
    // 0xb3e910: b.eq            #0xb3e924
    // 0xb3e914: tbnz            w1, #4, #0xb3e924
    // 0xb3e918: r1 = Instance_IconData
    //     0xb3e918: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb3e91c: ldr             x1, [x1, #0x130]
    // 0xb3e920: b               #0xb3e92c
    // 0xb3e924: r1 = Instance_IconData
    //     0xb3e924: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb3e928: ldr             x1, [x1, #0x138]
    // 0xb3e92c: stur            x1, [fp, #-0x98]
    // 0xb3e930: cmp             w3, #0xc
    // 0xb3e934: b.ne            #0xb3e9b0
    // 0xb3e938: LoadField: r3 = r2->field_b
    //     0xb3e938: ldur            w3, [x2, #0xb]
    // 0xb3e93c: DecompressPointer r3
    //     0xb3e93c: add             x3, x3, HEAP, lsl #32
    // 0xb3e940: cmp             w3, NULL
    // 0xb3e944: b.eq            #0xb3fda4
    // 0xb3e948: LoadField: r4 = r3->field_13
    //     0xb3e948: ldur            w4, [x3, #0x13]
    // 0xb3e94c: DecompressPointer r4
    //     0xb3e94c: add             x4, x4, HEAP, lsl #32
    // 0xb3e950: LoadField: r3 = r4->field_b
    //     0xb3e950: ldur            w3, [x4, #0xb]
    // 0xb3e954: DecompressPointer r3
    //     0xb3e954: add             x3, x3, HEAP, lsl #32
    // 0xb3e958: cmp             w3, NULL
    // 0xb3e95c: b.ne            #0xb3e968
    // 0xb3e960: r3 = Null
    //     0xb3e960: mov             x3, NULL
    // 0xb3e964: b               #0xb3e998
    // 0xb3e968: LoadField: r4 = r3->field_f
    //     0xb3e968: ldur            w4, [x3, #0xf]
    // 0xb3e96c: DecompressPointer r4
    //     0xb3e96c: add             x4, x4, HEAP, lsl #32
    // 0xb3e970: cmp             w4, NULL
    // 0xb3e974: b.ne            #0xb3e980
    // 0xb3e978: r3 = Null
    //     0xb3e978: mov             x3, NULL
    // 0xb3e97c: b               #0xb3e998
    // 0xb3e980: LoadField: r3 = r4->field_7
    //     0xb3e980: ldur            w3, [x4, #7]
    // 0xb3e984: cbnz            w3, #0xb3e990
    // 0xb3e988: r4 = false
    //     0xb3e988: add             x4, NULL, #0x30  ; false
    // 0xb3e98c: b               #0xb3e994
    // 0xb3e990: r4 = true
    //     0xb3e990: add             x4, NULL, #0x20  ; true
    // 0xb3e994: mov             x3, x4
    // 0xb3e998: cmp             w3, NULL
    // 0xb3e99c: b.eq            #0xb3e9b0
    // 0xb3e9a0: tbnz            w3, #4, #0xb3e9b0
    // 0xb3e9a4: r3 = Instance_Color
    //     0xb3e9a4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb3e9a8: ldr             x3, [x3, #0x858]
    // 0xb3e9ac: b               #0xb3e9b8
    // 0xb3e9b0: r3 = Instance_Color
    //     0xb3e9b0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb3e9b4: ldr             x3, [x3, #0x50]
    // 0xb3e9b8: stur            x3, [fp, #-0x90]
    // 0xb3e9bc: r0 = Icon()
    //     0xb3e9bc: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb3e9c0: mov             x1, x0
    // 0xb3e9c4: ldur            x0, [fp, #-0x98]
    // 0xb3e9c8: StoreField: r1->field_b = r0
    //     0xb3e9c8: stur            w0, [x1, #0xb]
    // 0xb3e9cc: ldur            x0, [fp, #-0x90]
    // 0xb3e9d0: StoreField: r1->field_23 = r0
    //     0xb3e9d0: stur            w0, [x1, #0x23]
    // 0xb3e9d4: b               #0xb3e9dc
    // 0xb3e9d8: r1 = Null
    //     0xb3e9d8: mov             x1, NULL
    // 0xb3e9dc: ldur            x2, [fp, #-8]
    // 0xb3e9e0: ldur            x9, [fp, #-0x38]
    // 0xb3e9e4: ldur            x8, [fp, #-0x30]
    // 0xb3e9e8: ldur            x7, [fp, #-0x40]
    // 0xb3e9ec: ldur            x6, [fp, #-0x48]
    // 0xb3e9f0: ldur            x5, [fp, #-0x50]
    // 0xb3e9f4: ldur            x4, [fp, #-0x20]
    // 0xb3e9f8: ldur            x0, [fp, #-0x70]
    // 0xb3e9fc: ldur            x3, [fp, #-0x68]
    // 0xb3ea00: ldur            x16, [fp, #-0x88]
    // 0xb3ea04: r30 = "State*"
    //     0xb3ea04: add             lr, PP, #0x54, lsl #12  ; [pp+0x54108] "State*"
    //     0xb3ea08: ldr             lr, [lr, #0x108]
    // 0xb3ea0c: stp             lr, x16, [SP, #0x10]
    // 0xb3ea10: ldur            x16, [fp, #-0xa0]
    // 0xb3ea14: stp             x1, x16, [SP]
    // 0xb3ea18: ldur            x1, [fp, #-0x80]
    // 0xb3ea1c: r4 = const [0, 0x5, 0x4, 0x1, fillColor, 0x1, hintStyle, 0x3, hintText, 0x2, suffixIcon, 0x4, null]
    //     0xb3ea1c: add             x4, PP, #0x56, lsl #12  ; [pp+0x56860] List(13) [0, 0x5, 0x4, 0x1, "fillColor", 0x1, "hintStyle", 0x3, "hintText", 0x2, "suffixIcon", 0x4, Null]
    //     0xb3ea20: ldr             x4, [x4, #0x860]
    // 0xb3ea24: r0 = copyWith()
    //     0xb3ea24: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb3ea28: r1 = <String>
    //     0xb3ea28: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb3ea2c: stur            x0, [fp, #-0x80]
    // 0xb3ea30: r0 = TextFormField()
    //     0xb3ea30: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb3ea34: stur            x0, [fp, #-0x88]
    // 0xb3ea38: r16 = true
    //     0xb3ea38: add             x16, NULL, #0x20  ; true
    // 0xb3ea3c: r30 = true
    //     0xb3ea3c: add             lr, NULL, #0x20  ; true
    // 0xb3ea40: stp             lr, x16, [SP, #0x10]
    // 0xb3ea44: ldur            x16, [fp, #-0x78]
    // 0xb3ea48: ldur            lr, [fp, #-0x58]
    // 0xb3ea4c: stp             lr, x16, [SP]
    // 0xb3ea50: mov             x1, x0
    // 0xb3ea54: ldur            x2, [fp, #-0x80]
    // 0xb3ea58: r4 = const [0, 0x6, 0x4, 0x2, controller, 0x4, ignorePointers, 0x3, readOnly, 0x2, style, 0x5, null]
    //     0xb3ea58: add             x4, PP, #0x54, lsl #12  ; [pp+0x54100] List(13) [0, 0x6, 0x4, 0x2, "controller", 0x4, "ignorePointers", 0x3, "readOnly", 0x2, "style", 0x5, Null]
    //     0xb3ea5c: ldr             x4, [x4, #0x100]
    // 0xb3ea60: r0 = TextFormField()
    //     0xb3ea60: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb3ea64: r0 = Form()
    //     0xb3ea64: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb3ea68: mov             x3, x0
    // 0xb3ea6c: ldur            x0, [fp, #-0x88]
    // 0xb3ea70: stur            x3, [fp, #-0x58]
    // 0xb3ea74: StoreField: r3->field_b = r0
    //     0xb3ea74: stur            w0, [x3, #0xb]
    // 0xb3ea78: r0 = Instance_AutovalidateMode
    //     0xb3ea78: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb3ea7c: ldr             x0, [x0, #0x800]
    // 0xb3ea80: StoreField: r3->field_23 = r0
    //     0xb3ea80: stur            w0, [x3, #0x23]
    // 0xb3ea84: ldur            x1, [fp, #-0x68]
    // 0xb3ea88: StoreField: r3->field_7 = r1
    //     0xb3ea88: stur            w1, [x3, #7]
    // 0xb3ea8c: r1 = Null
    //     0xb3ea8c: mov             x1, NULL
    // 0xb3ea90: r2 = 4
    //     0xb3ea90: movz            x2, #0x4
    // 0xb3ea94: r0 = AllocateArray()
    //     0xb3ea94: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3ea98: mov             x2, x0
    // 0xb3ea9c: ldur            x0, [fp, #-0x70]
    // 0xb3eaa0: stur            x2, [fp, #-0x68]
    // 0xb3eaa4: StoreField: r2->field_f = r0
    //     0xb3eaa4: stur            w0, [x2, #0xf]
    // 0xb3eaa8: ldur            x0, [fp, #-0x58]
    // 0xb3eaac: StoreField: r2->field_13 = r0
    //     0xb3eaac: stur            w0, [x2, #0x13]
    // 0xb3eab0: r1 = <Widget>
    //     0xb3eab0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3eab4: r0 = AllocateGrowableArray()
    //     0xb3eab4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3eab8: mov             x1, x0
    // 0xb3eabc: ldur            x0, [fp, #-0x68]
    // 0xb3eac0: stur            x1, [fp, #-0x58]
    // 0xb3eac4: StoreField: r1->field_f = r0
    //     0xb3eac4: stur            w0, [x1, #0xf]
    // 0xb3eac8: r2 = 4
    //     0xb3eac8: movz            x2, #0x4
    // 0xb3eacc: StoreField: r1->field_b = r2
    //     0xb3eacc: stur            w2, [x1, #0xb]
    // 0xb3ead0: r0 = Column()
    //     0xb3ead0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3ead4: mov             x2, x0
    // 0xb3ead8: r0 = Instance_Axis
    //     0xb3ead8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3eadc: stur            x2, [fp, #-0x68]
    // 0xb3eae0: StoreField: r2->field_f = r0
    //     0xb3eae0: stur            w0, [x2, #0xf]
    // 0xb3eae4: r3 = Instance_MainAxisAlignment
    //     0xb3eae4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3eae8: ldr             x3, [x3, #0xa08]
    // 0xb3eaec: StoreField: r2->field_13 = r3
    //     0xb3eaec: stur            w3, [x2, #0x13]
    // 0xb3eaf0: r4 = Instance_MainAxisSize
    //     0xb3eaf0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3eaf4: ldr             x4, [x4, #0xa10]
    // 0xb3eaf8: ArrayStore: r2[0] = r4  ; List_4
    //     0xb3eaf8: stur            w4, [x2, #0x17]
    // 0xb3eafc: r5 = Instance_CrossAxisAlignment
    //     0xb3eafc: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3eb00: ldr             x5, [x5, #0x890]
    // 0xb3eb04: StoreField: r2->field_1b = r5
    //     0xb3eb04: stur            w5, [x2, #0x1b]
    // 0xb3eb08: r6 = Instance_VerticalDirection
    //     0xb3eb08: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3eb0c: ldr             x6, [x6, #0xa20]
    // 0xb3eb10: StoreField: r2->field_23 = r6
    //     0xb3eb10: stur            w6, [x2, #0x23]
    // 0xb3eb14: r7 = Instance_Clip
    //     0xb3eb14: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3eb18: ldr             x7, [x7, #0x38]
    // 0xb3eb1c: StoreField: r2->field_2b = r7
    //     0xb3eb1c: stur            w7, [x2, #0x2b]
    // 0xb3eb20: StoreField: r2->field_2f = rZR
    //     0xb3eb20: stur            xzr, [x2, #0x2f]
    // 0xb3eb24: ldur            x1, [fp, #-0x58]
    // 0xb3eb28: StoreField: r2->field_b = r1
    //     0xb3eb28: stur            w1, [x2, #0xb]
    // 0xb3eb2c: r1 = <FlexParentData>
    //     0xb3eb2c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb3eb30: ldr             x1, [x1, #0xe00]
    // 0xb3eb34: r0 = Flexible()
    //     0xb3eb34: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xb3eb38: mov             x3, x0
    // 0xb3eb3c: r0 = 1
    //     0xb3eb3c: movz            x0, #0x1
    // 0xb3eb40: stur            x3, [fp, #-0x58]
    // 0xb3eb44: StoreField: r3->field_13 = r0
    //     0xb3eb44: stur            x0, [x3, #0x13]
    // 0xb3eb48: r0 = Instance_FlexFit
    //     0xb3eb48: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb3eb4c: ldr             x0, [x0, #0xe08]
    // 0xb3eb50: StoreField: r3->field_1b = r0
    //     0xb3eb50: stur            w0, [x3, #0x1b]
    // 0xb3eb54: ldur            x0, [fp, #-0x68]
    // 0xb3eb58: StoreField: r3->field_b = r0
    //     0xb3eb58: stur            w0, [x3, #0xb]
    // 0xb3eb5c: r1 = Null
    //     0xb3eb5c: mov             x1, NULL
    // 0xb3eb60: r2 = 6
    //     0xb3eb60: movz            x2, #0x6
    // 0xb3eb64: r0 = AllocateArray()
    //     0xb3eb64: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3eb68: mov             x2, x0
    // 0xb3eb6c: ldur            x0, [fp, #-0x20]
    // 0xb3eb70: stur            x2, [fp, #-0x68]
    // 0xb3eb74: StoreField: r2->field_f = r0
    //     0xb3eb74: stur            w0, [x2, #0xf]
    // 0xb3eb78: r16 = Instance_SizedBox
    //     0xb3eb78: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb3eb7c: ldr             x16, [x16, #0x998]
    // 0xb3eb80: StoreField: r2->field_13 = r16
    //     0xb3eb80: stur            w16, [x2, #0x13]
    // 0xb3eb84: ldur            x0, [fp, #-0x58]
    // 0xb3eb88: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3eb88: stur            w0, [x2, #0x17]
    // 0xb3eb8c: r1 = <Widget>
    //     0xb3eb8c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3eb90: r0 = AllocateGrowableArray()
    //     0xb3eb90: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3eb94: mov             x1, x0
    // 0xb3eb98: ldur            x0, [fp, #-0x68]
    // 0xb3eb9c: stur            x1, [fp, #-0x20]
    // 0xb3eba0: StoreField: r1->field_f = r0
    //     0xb3eba0: stur            w0, [x1, #0xf]
    // 0xb3eba4: r2 = 6
    //     0xb3eba4: movz            x2, #0x6
    // 0xb3eba8: StoreField: r1->field_b = r2
    //     0xb3eba8: stur            w2, [x1, #0xb]
    // 0xb3ebac: r0 = Row()
    //     0xb3ebac: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb3ebb0: mov             x1, x0
    // 0xb3ebb4: r0 = Instance_Axis
    //     0xb3ebb4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb3ebb8: stur            x1, [fp, #-0x58]
    // 0xb3ebbc: StoreField: r1->field_f = r0
    //     0xb3ebbc: stur            w0, [x1, #0xf]
    // 0xb3ebc0: r2 = Instance_MainAxisAlignment
    //     0xb3ebc0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb3ebc4: ldr             x2, [x2, #0xd10]
    // 0xb3ebc8: StoreField: r1->field_13 = r2
    //     0xb3ebc8: stur            w2, [x1, #0x13]
    // 0xb3ebcc: r2 = Instance_MainAxisSize
    //     0xb3ebcc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3ebd0: ldr             x2, [x2, #0xa10]
    // 0xb3ebd4: ArrayStore: r1[0] = r2  ; List_4
    //     0xb3ebd4: stur            w2, [x1, #0x17]
    // 0xb3ebd8: r3 = Instance_CrossAxisAlignment
    //     0xb3ebd8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3ebdc: ldr             x3, [x3, #0x890]
    // 0xb3ebe0: StoreField: r1->field_1b = r3
    //     0xb3ebe0: stur            w3, [x1, #0x1b]
    // 0xb3ebe4: r4 = Instance_VerticalDirection
    //     0xb3ebe4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3ebe8: ldr             x4, [x4, #0xa20]
    // 0xb3ebec: StoreField: r1->field_23 = r4
    //     0xb3ebec: stur            w4, [x1, #0x23]
    // 0xb3ebf0: r5 = Instance_Clip
    //     0xb3ebf0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3ebf4: ldr             x5, [x5, #0x38]
    // 0xb3ebf8: StoreField: r1->field_2b = r5
    //     0xb3ebf8: stur            w5, [x1, #0x2b]
    // 0xb3ebfc: StoreField: r1->field_2f = rZR
    //     0xb3ebfc: stur            xzr, [x1, #0x2f]
    // 0xb3ec00: ldur            x6, [fp, #-0x20]
    // 0xb3ec04: StoreField: r1->field_b = r6
    //     0xb3ec04: stur            w6, [x1, #0xb]
    // 0xb3ec08: r0 = Padding()
    //     0xb3ec08: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3ec0c: mov             x3, x0
    // 0xb3ec10: r0 = Instance_EdgeInsets
    //     0xb3ec10: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xb3ec14: ldr             x0, [x0, #0x858]
    // 0xb3ec18: stur            x3, [fp, #-0x20]
    // 0xb3ec1c: StoreField: r3->field_f = r0
    //     0xb3ec1c: stur            w0, [x3, #0xf]
    // 0xb3ec20: ldur            x0, [fp, #-0x58]
    // 0xb3ec24: StoreField: r3->field_b = r0
    //     0xb3ec24: stur            w0, [x3, #0xb]
    // 0xb3ec28: r1 = Null
    //     0xb3ec28: mov             x1, NULL
    // 0xb3ec2c: r2 = 12
    //     0xb3ec2c: movz            x2, #0xc
    // 0xb3ec30: r0 = AllocateArray()
    //     0xb3ec30: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3ec34: mov             x2, x0
    // 0xb3ec38: ldur            x0, [fp, #-0x38]
    // 0xb3ec3c: stur            x2, [fp, #-0x58]
    // 0xb3ec40: StoreField: r2->field_f = r0
    //     0xb3ec40: stur            w0, [x2, #0xf]
    // 0xb3ec44: ldur            x0, [fp, #-0x30]
    // 0xb3ec48: StoreField: r2->field_13 = r0
    //     0xb3ec48: stur            w0, [x2, #0x13]
    // 0xb3ec4c: ldur            x0, [fp, #-0x40]
    // 0xb3ec50: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3ec50: stur            w0, [x2, #0x17]
    // 0xb3ec54: ldur            x0, [fp, #-0x48]
    // 0xb3ec58: StoreField: r2->field_1b = r0
    //     0xb3ec58: stur            w0, [x2, #0x1b]
    // 0xb3ec5c: ldur            x0, [fp, #-0x50]
    // 0xb3ec60: StoreField: r2->field_1f = r0
    //     0xb3ec60: stur            w0, [x2, #0x1f]
    // 0xb3ec64: ldur            x0, [fp, #-0x20]
    // 0xb3ec68: StoreField: r2->field_23 = r0
    //     0xb3ec68: stur            w0, [x2, #0x23]
    // 0xb3ec6c: r1 = <Widget>
    //     0xb3ec6c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3ec70: r0 = AllocateGrowableArray()
    //     0xb3ec70: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3ec74: mov             x1, x0
    // 0xb3ec78: ldur            x0, [fp, #-0x58]
    // 0xb3ec7c: stur            x1, [fp, #-0x20]
    // 0xb3ec80: StoreField: r1->field_f = r0
    //     0xb3ec80: stur            w0, [x1, #0xf]
    // 0xb3ec84: r2 = 12
    //     0xb3ec84: movz            x2, #0xc
    // 0xb3ec88: StoreField: r1->field_b = r2
    //     0xb3ec88: stur            w2, [x1, #0xb]
    // 0xb3ec8c: r0 = Column()
    //     0xb3ec8c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3ec90: mov             x1, x0
    // 0xb3ec94: r0 = Instance_Axis
    //     0xb3ec94: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3ec98: stur            x1, [fp, #-0x30]
    // 0xb3ec9c: StoreField: r1->field_f = r0
    //     0xb3ec9c: stur            w0, [x1, #0xf]
    // 0xb3eca0: r2 = Instance_MainAxisAlignment
    //     0xb3eca0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3eca4: ldr             x2, [x2, #0xa08]
    // 0xb3eca8: StoreField: r1->field_13 = r2
    //     0xb3eca8: stur            w2, [x1, #0x13]
    // 0xb3ecac: r3 = Instance_MainAxisSize
    //     0xb3ecac: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3ecb0: ldr             x3, [x3, #0xa10]
    // 0xb3ecb4: ArrayStore: r1[0] = r3  ; List_4
    //     0xb3ecb4: stur            w3, [x1, #0x17]
    // 0xb3ecb8: r4 = Instance_CrossAxisAlignment
    //     0xb3ecb8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3ecbc: ldr             x4, [x4, #0x890]
    // 0xb3ecc0: StoreField: r1->field_1b = r4
    //     0xb3ecc0: stur            w4, [x1, #0x1b]
    // 0xb3ecc4: r5 = Instance_VerticalDirection
    //     0xb3ecc4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3ecc8: ldr             x5, [x5, #0xa20]
    // 0xb3eccc: StoreField: r1->field_23 = r5
    //     0xb3eccc: stur            w5, [x1, #0x23]
    // 0xb3ecd0: r6 = Instance_Clip
    //     0xb3ecd0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3ecd4: ldr             x6, [x6, #0x38]
    // 0xb3ecd8: StoreField: r1->field_2b = r6
    //     0xb3ecd8: stur            w6, [x1, #0x2b]
    // 0xb3ecdc: StoreField: r1->field_2f = rZR
    //     0xb3ecdc: stur            xzr, [x1, #0x2f]
    // 0xb3ece0: ldur            x7, [fp, #-0x20]
    // 0xb3ece4: StoreField: r1->field_b = r7
    //     0xb3ece4: stur            w7, [x1, #0xb]
    // 0xb3ece8: r0 = Padding()
    //     0xb3ece8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3ecec: mov             x3, x0
    // 0xb3ecf0: r0 = Instance_EdgeInsets
    //     0xb3ecf0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb3ecf4: ldr             x0, [x0, #0xd0]
    // 0xb3ecf8: stur            x3, [fp, #-0x20]
    // 0xb3ecfc: StoreField: r3->field_f = r0
    //     0xb3ecfc: stur            w0, [x3, #0xf]
    // 0xb3ed00: ldur            x0, [fp, #-0x30]
    // 0xb3ed04: StoreField: r3->field_b = r0
    //     0xb3ed04: stur            w0, [x3, #0xb]
    // 0xb3ed08: ldur            x4, [fp, #-8]
    // 0xb3ed0c: LoadField: r0 = r4->field_b
    //     0xb3ed0c: ldur            w0, [x4, #0xb]
    // 0xb3ed10: DecompressPointer r0
    //     0xb3ed10: add             x0, x0, HEAP, lsl #32
    // 0xb3ed14: cmp             w0, NULL
    // 0xb3ed18: b.eq            #0xb3fda8
    // 0xb3ed1c: LoadField: r1 = r0->field_b
    //     0xb3ed1c: ldur            w1, [x0, #0xb]
    // 0xb3ed20: DecompressPointer r1
    //     0xb3ed20: add             x1, x1, HEAP, lsl #32
    // 0xb3ed24: LoadField: r0 = r1->field_f
    //     0xb3ed24: ldur            w0, [x1, #0xf]
    // 0xb3ed28: DecompressPointer r0
    //     0xb3ed28: add             x0, x0, HEAP, lsl #32
    // 0xb3ed2c: cmp             w0, NULL
    // 0xb3ed30: b.ne            #0xb3ed3c
    // 0xb3ed34: r0 = Null
    //     0xb3ed34: mov             x0, NULL
    // 0xb3ed38: b               #0xb3ed68
    // 0xb3ed3c: r1 = LoadClassIdInstr(r0)
    //     0xb3ed3c: ldur            x1, [x0, #-1]
    //     0xb3ed40: ubfx            x1, x1, #0xc, #0x14
    // 0xb3ed44: mov             x16, x0
    // 0xb3ed48: mov             x0, x1
    // 0xb3ed4c: mov             x1, x16
    // 0xb3ed50: r2 = "landmark"
    //     0xb3ed50: add             x2, PP, #0x24, lsl #12  ; [pp+0x24930] "landmark"
    //     0xb3ed54: ldr             x2, [x2, #0x930]
    // 0xb3ed58: r0 = GDT[cid_x0 + 0xe437]()
    //     0xb3ed58: movz            x17, #0xe437
    //     0xb3ed5c: add             lr, x0, x17
    //     0xb3ed60: ldr             lr, [x21, lr, lsl #3]
    //     0xb3ed64: blr             lr
    // 0xb3ed68: cmp             w0, NULL
    // 0xb3ed6c: b.ne            #0xb3ed74
    // 0xb3ed70: r0 = false
    //     0xb3ed70: add             x0, NULL, #0x30  ; false
    // 0xb3ed74: ldur            x2, [fp, #-8]
    // 0xb3ed78: stur            x0, [fp, #-0x38]
    // 0xb3ed7c: LoadField: r1 = r2->field_37
    //     0xb3ed7c: ldur            w1, [x2, #0x37]
    // 0xb3ed80: DecompressPointer r1
    //     0xb3ed80: add             x1, x1, HEAP, lsl #32
    // 0xb3ed84: LoadField: r3 = r1->field_27
    //     0xb3ed84: ldur            w3, [x1, #0x27]
    // 0xb3ed88: DecompressPointer r3
    //     0xb3ed88: add             x3, x3, HEAP, lsl #32
    // 0xb3ed8c: LoadField: r1 = r3->field_7
    //     0xb3ed8c: ldur            w1, [x3, #7]
    // 0xb3ed90: DecompressPointer r1
    //     0xb3ed90: add             x1, x1, HEAP, lsl #32
    // 0xb3ed94: LoadField: r3 = r1->field_7
    //     0xb3ed94: ldur            w3, [x1, #7]
    // 0xb3ed98: cbnz            w3, #0xb3eda8
    // 0xb3ed9c: r3 = "Landmark"
    //     0xb3ed9c: add             x3, PP, #0x54, lsl #12  ; [pp+0x54118] "Landmark"
    //     0xb3eda0: ldr             x3, [x3, #0x118]
    // 0xb3eda4: b               #0xb3edb0
    // 0xb3eda8: r3 = "Nearby Famous Place / Shop / School, etc. (Optional)"
    //     0xb3eda8: add             x3, PP, #0x54, lsl #12  ; [pp+0x54120] "Nearby Famous Place / Shop / School, etc. (Optional)"
    //     0xb3edac: ldr             x3, [x3, #0x120]
    // 0xb3edb0: ldur            x1, [fp, #-0x10]
    // 0xb3edb4: stur            x3, [fp, #-0x30]
    // 0xb3edb8: r0 = of()
    //     0xb3edb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3edbc: LoadField: r1 = r0->field_87
    //     0xb3edbc: ldur            w1, [x0, #0x87]
    // 0xb3edc0: DecompressPointer r1
    //     0xb3edc0: add             x1, x1, HEAP, lsl #32
    // 0xb3edc4: LoadField: r0 = r1->field_2b
    //     0xb3edc4: ldur            w0, [x1, #0x2b]
    // 0xb3edc8: DecompressPointer r0
    //     0xb3edc8: add             x0, x0, HEAP, lsl #32
    // 0xb3edcc: r16 = 12.000000
    //     0xb3edcc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3edd0: ldr             x16, [x16, #0x9e8]
    // 0xb3edd4: r30 = Instance_Color
    //     0xb3edd4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3edd8: stp             lr, x16, [SP]
    // 0xb3eddc: mov             x1, x0
    // 0xb3ede0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3ede0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3ede4: ldr             x4, [x4, #0xaa0]
    // 0xb3ede8: r0 = copyWith()
    //     0xb3ede8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3edec: stur            x0, [fp, #-0x40]
    // 0xb3edf0: r0 = Text()
    //     0xb3edf0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3edf4: mov             x1, x0
    // 0xb3edf8: ldur            x0, [fp, #-0x30]
    // 0xb3edfc: stur            x1, [fp, #-0x48]
    // 0xb3ee00: StoreField: r1->field_b = r0
    //     0xb3ee00: stur            w0, [x1, #0xb]
    // 0xb3ee04: ldur            x0, [fp, #-0x40]
    // 0xb3ee08: StoreField: r1->field_13 = r0
    //     0xb3ee08: stur            w0, [x1, #0x13]
    // 0xb3ee0c: r0 = Padding()
    //     0xb3ee0c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3ee10: mov             x1, x0
    // 0xb3ee14: r0 = Instance_EdgeInsets
    //     0xb3ee14: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f4c0] Obj!EdgeInsets@d579e1
    //     0xb3ee18: ldr             x0, [x0, #0x4c0]
    // 0xb3ee1c: stur            x1, [fp, #-0x40]
    // 0xb3ee20: StoreField: r1->field_f = r0
    //     0xb3ee20: stur            w0, [x1, #0xf]
    // 0xb3ee24: ldur            x2, [fp, #-0x48]
    // 0xb3ee28: StoreField: r1->field_b = r2
    //     0xb3ee28: stur            w2, [x1, #0xb]
    // 0xb3ee2c: ldur            x2, [fp, #-8]
    // 0xb3ee30: LoadField: r3 = r2->field_1b
    //     0xb3ee30: ldur            w3, [x2, #0x1b]
    // 0xb3ee34: DecompressPointer r3
    //     0xb3ee34: add             x3, x3, HEAP, lsl #32
    // 0xb3ee38: stur            x3, [fp, #-0x30]
    // 0xb3ee3c: r0 = LengthLimitingTextInputFormatter()
    //     0xb3ee3c: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb3ee40: mov             x1, x0
    // 0xb3ee44: r0 = 240
    //     0xb3ee44: movz            x0, #0xf0
    // 0xb3ee48: stur            x1, [fp, #-0x48]
    // 0xb3ee4c: StoreField: r1->field_7 = r0
    //     0xb3ee4c: stur            w0, [x1, #7]
    // 0xb3ee50: r16 = "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xb3ee50: add             x16, PP, #0x54, lsl #12  ; [pp+0x540a8] "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xb3ee54: ldr             x16, [x16, #0xa8]
    // 0xb3ee58: stp             x16, NULL, [SP, #0x20]
    // 0xb3ee5c: r16 = false
    //     0xb3ee5c: add             x16, NULL, #0x30  ; false
    // 0xb3ee60: r30 = true
    //     0xb3ee60: add             lr, NULL, #0x20  ; true
    // 0xb3ee64: stp             lr, x16, [SP, #0x10]
    // 0xb3ee68: r16 = false
    //     0xb3ee68: add             x16, NULL, #0x30  ; false
    // 0xb3ee6c: r30 = false
    //     0xb3ee6c: add             lr, NULL, #0x30  ; false
    // 0xb3ee70: stp             lr, x16, [SP]
    // 0xb3ee74: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xb3ee74: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xb3ee78: r0 = _RegExp()
    //     0xb3ee78: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xb3ee7c: stur            x0, [fp, #-0x50]
    // 0xb3ee80: r0 = FilteringTextInputFormatter()
    //     0xb3ee80: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xb3ee84: mov             x3, x0
    // 0xb3ee88: ldur            x0, [fp, #-0x50]
    // 0xb3ee8c: stur            x3, [fp, #-0x58]
    // 0xb3ee90: StoreField: r3->field_b = r0
    //     0xb3ee90: stur            w0, [x3, #0xb]
    // 0xb3ee94: r0 = true
    //     0xb3ee94: add             x0, NULL, #0x20  ; true
    // 0xb3ee98: StoreField: r3->field_7 = r0
    //     0xb3ee98: stur            w0, [x3, #7]
    // 0xb3ee9c: r4 = ""
    //     0xb3ee9c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3eea0: StoreField: r3->field_f = r4
    //     0xb3eea0: stur            w4, [x3, #0xf]
    // 0xb3eea4: r1 = Null
    //     0xb3eea4: mov             x1, NULL
    // 0xb3eea8: r2 = 4
    //     0xb3eea8: movz            x2, #0x4
    // 0xb3eeac: r0 = AllocateArray()
    //     0xb3eeac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3eeb0: mov             x2, x0
    // 0xb3eeb4: ldur            x0, [fp, #-0x48]
    // 0xb3eeb8: stur            x2, [fp, #-0x50]
    // 0xb3eebc: StoreField: r2->field_f = r0
    //     0xb3eebc: stur            w0, [x2, #0xf]
    // 0xb3eec0: ldur            x0, [fp, #-0x58]
    // 0xb3eec4: StoreField: r2->field_13 = r0
    //     0xb3eec4: stur            w0, [x2, #0x13]
    // 0xb3eec8: r1 = <TextInputFormatter>
    //     0xb3eec8: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb3eecc: ldr             x1, [x1, #0x7b0]
    // 0xb3eed0: r0 = AllocateGrowableArray()
    //     0xb3eed0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3eed4: mov             x2, x0
    // 0xb3eed8: ldur            x0, [fp, #-0x50]
    // 0xb3eedc: stur            x2, [fp, #-0x48]
    // 0xb3eee0: StoreField: r2->field_f = r0
    //     0xb3eee0: stur            w0, [x2, #0xf]
    // 0xb3eee4: r0 = 4
    //     0xb3eee4: movz            x0, #0x4
    // 0xb3eee8: StoreField: r2->field_b = r0
    //     0xb3eee8: stur            w0, [x2, #0xb]
    // 0xb3eeec: ldur            x1, [fp, #-0x10]
    // 0xb3eef0: r0 = of()
    //     0xb3eef0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3eef4: LoadField: r1 = r0->field_87
    //     0xb3eef4: ldur            w1, [x0, #0x87]
    // 0xb3eef8: DecompressPointer r1
    //     0xb3eef8: add             x1, x1, HEAP, lsl #32
    // 0xb3eefc: LoadField: r0 = r1->field_2b
    //     0xb3eefc: ldur            w0, [x1, #0x2b]
    // 0xb3ef00: DecompressPointer r0
    //     0xb3ef00: add             x0, x0, HEAP, lsl #32
    // 0xb3ef04: ldur            x1, [fp, #-0x10]
    // 0xb3ef08: stur            x0, [fp, #-0x50]
    // 0xb3ef0c: r0 = of()
    //     0xb3ef0c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3ef10: LoadField: r1 = r0->field_5b
    //     0xb3ef10: ldur            w1, [x0, #0x5b]
    // 0xb3ef14: DecompressPointer r1
    //     0xb3ef14: add             x1, x1, HEAP, lsl #32
    // 0xb3ef18: r16 = 14.000000
    //     0xb3ef18: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3ef1c: ldr             x16, [x16, #0x1d8]
    // 0xb3ef20: stp             x16, x1, [SP]
    // 0xb3ef24: ldur            x1, [fp, #-0x50]
    // 0xb3ef28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb3ef28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb3ef2c: ldr             x4, [x4, #0x9b8]
    // 0xb3ef30: r0 = copyWith()
    //     0xb3ef30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3ef34: ldur            x2, [fp, #-8]
    // 0xb3ef38: stur            x0, [fp, #-0x58]
    // 0xb3ef3c: LoadField: r3 = r2->field_37
    //     0xb3ef3c: ldur            w3, [x2, #0x37]
    // 0xb3ef40: DecompressPointer r3
    //     0xb3ef40: add             x3, x3, HEAP, lsl #32
    // 0xb3ef44: ldur            x1, [fp, #-0x10]
    // 0xb3ef48: stur            x3, [fp, #-0x50]
    // 0xb3ef4c: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb3ef4c: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb3ef50: ldur            x1, [fp, #-0x10]
    // 0xb3ef54: stur            x0, [fp, #-0x68]
    // 0xb3ef58: r0 = of()
    //     0xb3ef58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3ef5c: LoadField: r1 = r0->field_5b
    //     0xb3ef5c: ldur            w1, [x0, #0x5b]
    // 0xb3ef60: DecompressPointer r1
    //     0xb3ef60: add             x1, x1, HEAP, lsl #32
    // 0xb3ef64: stur            x1, [fp, #-0x70]
    // 0xb3ef68: r0 = BorderSide()
    //     0xb3ef68: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb3ef6c: mov             x1, x0
    // 0xb3ef70: ldur            x0, [fp, #-0x70]
    // 0xb3ef74: stur            x1, [fp, #-0x78]
    // 0xb3ef78: StoreField: r1->field_7 = r0
    //     0xb3ef78: stur            w0, [x1, #7]
    // 0xb3ef7c: d0 = 1.000000
    //     0xb3ef7c: fmov            d0, #1.00000000
    // 0xb3ef80: StoreField: r1->field_b = d0
    //     0xb3ef80: stur            d0, [x1, #0xb]
    // 0xb3ef84: r0 = Instance_BorderStyle
    //     0xb3ef84: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb3ef88: ldr             x0, [x0, #0xf68]
    // 0xb3ef8c: StoreField: r1->field_13 = r0
    //     0xb3ef8c: stur            w0, [x1, #0x13]
    // 0xb3ef90: d1 = -1.000000
    //     0xb3ef90: fmov            d1, #-1.00000000
    // 0xb3ef94: ArrayStore: r1[0] = d1  ; List_8
    //     0xb3ef94: stur            d1, [x1, #0x17]
    // 0xb3ef98: r0 = Radius()
    //     0xb3ef98: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb3ef9c: d0 = 30.000000
    //     0xb3ef9c: fmov            d0, #30.00000000
    // 0xb3efa0: stur            x0, [fp, #-0x70]
    // 0xb3efa4: StoreField: r0->field_7 = d0
    //     0xb3efa4: stur            d0, [x0, #7]
    // 0xb3efa8: StoreField: r0->field_f = d0
    //     0xb3efa8: stur            d0, [x0, #0xf]
    // 0xb3efac: r0 = BorderRadius()
    //     0xb3efac: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb3efb0: mov             x1, x0
    // 0xb3efb4: ldur            x0, [fp, #-0x70]
    // 0xb3efb8: stur            x1, [fp, #-0x80]
    // 0xb3efbc: StoreField: r1->field_7 = r0
    //     0xb3efbc: stur            w0, [x1, #7]
    // 0xb3efc0: StoreField: r1->field_b = r0
    //     0xb3efc0: stur            w0, [x1, #0xb]
    // 0xb3efc4: StoreField: r1->field_f = r0
    //     0xb3efc4: stur            w0, [x1, #0xf]
    // 0xb3efc8: StoreField: r1->field_13 = r0
    //     0xb3efc8: stur            w0, [x1, #0x13]
    // 0xb3efcc: r0 = OutlineInputBorder()
    //     0xb3efcc: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xb3efd0: mov             x2, x0
    // 0xb3efd4: ldur            x0, [fp, #-0x80]
    // 0xb3efd8: stur            x2, [fp, #-0x70]
    // 0xb3efdc: StoreField: r2->field_13 = r0
    //     0xb3efdc: stur            w0, [x2, #0x13]
    // 0xb3efe0: d0 = 4.000000
    //     0xb3efe0: fmov            d0, #4.00000000
    // 0xb3efe4: StoreField: r2->field_b = d0
    //     0xb3efe4: stur            d0, [x2, #0xb]
    // 0xb3efe8: ldur            x0, [fp, #-0x78]
    // 0xb3efec: StoreField: r2->field_7 = r0
    //     0xb3efec: stur            w0, [x2, #7]
    // 0xb3eff0: ldur            x1, [fp, #-0x10]
    // 0xb3eff4: r0 = of()
    //     0xb3eff4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3eff8: LoadField: r1 = r0->field_87
    //     0xb3eff8: ldur            w1, [x0, #0x87]
    // 0xb3effc: DecompressPointer r1
    //     0xb3effc: add             x1, x1, HEAP, lsl #32
    // 0xb3f000: LoadField: r0 = r1->field_2b
    //     0xb3f000: ldur            w0, [x1, #0x2b]
    // 0xb3f004: DecompressPointer r0
    //     0xb3f004: add             x0, x0, HEAP, lsl #32
    // 0xb3f008: stur            x0, [fp, #-0x78]
    // 0xb3f00c: r1 = Instance_Color
    //     0xb3f00c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3f010: d0 = 0.400000
    //     0xb3f010: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb3f014: r0 = withOpacity()
    //     0xb3f014: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb3f018: r16 = 14.000000
    //     0xb3f018: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3f01c: ldr             x16, [x16, #0x1d8]
    // 0xb3f020: stp             x0, x16, [SP]
    // 0xb3f024: ldur            x1, [fp, #-0x78]
    // 0xb3f028: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3f028: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3f02c: ldr             x4, [x4, #0xaa0]
    // 0xb3f030: r0 = copyWith()
    //     0xb3f030: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3f034: ldur            x1, [fp, #-0x10]
    // 0xb3f038: stur            x0, [fp, #-0x78]
    // 0xb3f03c: r0 = of()
    //     0xb3f03c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3f040: LoadField: r1 = r0->field_87
    //     0xb3f040: ldur            w1, [x0, #0x87]
    // 0xb3f044: DecompressPointer r1
    //     0xb3f044: add             x1, x1, HEAP, lsl #32
    // 0xb3f048: LoadField: r0 = r1->field_2b
    //     0xb3f048: ldur            w0, [x1, #0x2b]
    // 0xb3f04c: DecompressPointer r0
    //     0xb3f04c: add             x0, x0, HEAP, lsl #32
    // 0xb3f050: r16 = 12.000000
    //     0xb3f050: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3f054: ldr             x16, [x16, #0x9e8]
    // 0xb3f058: r30 = Instance_MaterialColor
    //     0xb3f058: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb3f05c: ldr             lr, [lr, #0x180]
    // 0xb3f060: stp             lr, x16, [SP]
    // 0xb3f064: mov             x1, x0
    // 0xb3f068: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3f068: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3f06c: ldr             x4, [x4, #0xaa0]
    // 0xb3f070: r0 = copyWith()
    //     0xb3f070: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3f074: ldur            x2, [fp, #-8]
    // 0xb3f078: stur            x0, [fp, #-0x90]
    // 0xb3f07c: LoadField: r1 = r2->field_5f
    //     0xb3f07c: ldur            w1, [x2, #0x5f]
    // 0xb3f080: DecompressPointer r1
    //     0xb3f080: add             x1, x1, HEAP, lsl #32
    // 0xb3f084: tbnz            w1, #4, #0xb3f11c
    // 0xb3f088: LoadField: r1 = r2->field_37
    //     0xb3f088: ldur            w1, [x2, #0x37]
    // 0xb3f08c: DecompressPointer r1
    //     0xb3f08c: add             x1, x1, HEAP, lsl #32
    // 0xb3f090: LoadField: r3 = r1->field_27
    //     0xb3f090: ldur            w3, [x1, #0x27]
    // 0xb3f094: DecompressPointer r3
    //     0xb3f094: add             x3, x3, HEAP, lsl #32
    // 0xb3f098: LoadField: r1 = r3->field_7
    //     0xb3f098: ldur            w1, [x3, #7]
    // 0xb3f09c: DecompressPointer r1
    //     0xb3f09c: add             x1, x1, HEAP, lsl #32
    // 0xb3f0a0: LoadField: r3 = r1->field_7
    //     0xb3f0a0: ldur            w3, [x1, #7]
    // 0xb3f0a4: cbz             w3, #0xb3f0c0
    // 0xb3f0a8: r1 = LoadInt32Instr(r3)
    //     0xb3f0a8: sbfx            x1, x3, #1, #0x1f
    // 0xb3f0ac: cmp             x1, #5
    // 0xb3f0b0: b.lt            #0xb3f0c0
    // 0xb3f0b4: r1 = Instance_IconData
    //     0xb3f0b4: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb3f0b8: ldr             x1, [x1, #0x130]
    // 0xb3f0bc: b               #0xb3f0d4
    // 0xb3f0c0: cbz             w3, #0xb3f0d0
    // 0xb3f0c4: r1 = Instance_IconData
    //     0xb3f0c4: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb3f0c8: ldr             x1, [x1, #0x138]
    // 0xb3f0cc: b               #0xb3f0d4
    // 0xb3f0d0: r1 = Null
    //     0xb3f0d0: mov             x1, NULL
    // 0xb3f0d4: stur            x1, [fp, #-0x88]
    // 0xb3f0d8: cbz             w3, #0xb3f0f4
    // 0xb3f0dc: r4 = LoadInt32Instr(r3)
    //     0xb3f0dc: sbfx            x4, x3, #1, #0x1f
    // 0xb3f0e0: cmp             x4, #5
    // 0xb3f0e4: b.lt            #0xb3f0f4
    // 0xb3f0e8: r3 = Instance_Color
    //     0xb3f0e8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb3f0ec: ldr             x3, [x3, #0x858]
    // 0xb3f0f0: b               #0xb3f0fc
    // 0xb3f0f4: r3 = Instance_Color
    //     0xb3f0f4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb3f0f8: ldr             x3, [x3, #0x50]
    // 0xb3f0fc: stur            x3, [fp, #-0x80]
    // 0xb3f100: r0 = Icon()
    //     0xb3f100: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb3f104: mov             x1, x0
    // 0xb3f108: ldur            x0, [fp, #-0x88]
    // 0xb3f10c: StoreField: r1->field_b = r0
    //     0xb3f10c: stur            w0, [x1, #0xb]
    // 0xb3f110: ldur            x0, [fp, #-0x80]
    // 0xb3f114: StoreField: r1->field_23 = r0
    //     0xb3f114: stur            w0, [x1, #0x23]
    // 0xb3f118: b               #0xb3f120
    // 0xb3f11c: r1 = Null
    //     0xb3f11c: mov             x1, NULL
    // 0xb3f120: ldur            x2, [fp, #-8]
    // 0xb3f124: ldur            x4, [fp, #-0x38]
    // 0xb3f128: ldur            x0, [fp, #-0x40]
    // 0xb3f12c: ldur            x3, [fp, #-0x30]
    // 0xb3f130: ldur            x16, [fp, #-0x70]
    // 0xb3f134: r30 = Instance_EdgeInsets
    //     0xb3f134: add             lr, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb3f138: ldr             lr, [lr, #0xa78]
    // 0xb3f13c: stp             lr, x16, [SP, #0x20]
    // 0xb3f140: r16 = "Landmark"
    //     0xb3f140: add             x16, PP, #0x54, lsl #12  ; [pp+0x54118] "Landmark"
    //     0xb3f144: ldr             x16, [x16, #0x118]
    // 0xb3f148: ldur            lr, [fp, #-0x78]
    // 0xb3f14c: stp             lr, x16, [SP, #0x10]
    // 0xb3f150: ldur            x16, [fp, #-0x90]
    // 0xb3f154: stp             x1, x16, [SP]
    // 0xb3f158: ldur            x1, [fp, #-0x68]
    // 0xb3f15c: r4 = const [0, 0x7, 0x6, 0x1, contentPadding, 0x2, errorStyle, 0x5, focusedBorder, 0x1, hintStyle, 0x4, hintText, 0x3, suffixIcon, 0x6, null]
    //     0xb3f15c: add             x4, PP, #0x56, lsl #12  ; [pp+0x56ef8] List(17) [0, 0x7, 0x6, 0x1, "contentPadding", 0x2, "errorStyle", 0x5, "focusedBorder", 0x1, "hintStyle", 0x4, "hintText", 0x3, "suffixIcon", 0x6, Null]
    //     0xb3f160: ldr             x4, [x4, #0xef8]
    // 0xb3f164: r0 = copyWith()
    //     0xb3f164: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb3f168: ldur            x2, [fp, #-8]
    // 0xb3f16c: r1 = Function '_validateLandmark@1537065735':.
    //     0xb3f16c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f30] AnonymousClosure: (0xb4059c), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateLandmark (0xa01e20)
    //     0xb3f170: ldr             x1, [x1, #0xf30]
    // 0xb3f174: stur            x0, [fp, #-0x68]
    // 0xb3f178: r0 = AllocateClosure()
    //     0xb3f178: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3f17c: ldur            x2, [fp, #-0x18]
    // 0xb3f180: r1 = Function '<anonymous closure>':.
    //     0xb3f180: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f38] AnonymousClosure: (0xb40524), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xb3ca88)
    //     0xb3f184: ldr             x1, [x1, #0xf38]
    // 0xb3f188: stur            x0, [fp, #-0x70]
    // 0xb3f18c: r0 = AllocateClosure()
    //     0xb3f18c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3f190: r1 = <String>
    //     0xb3f190: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb3f194: stur            x0, [fp, #-0x78]
    // 0xb3f198: r0 = TextFormField()
    //     0xb3f198: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb3f19c: stur            x0, [fp, #-0x80]
    // 0xb3f1a0: ldur            x16, [fp, #-0x70]
    // 0xb3f1a4: r30 = true
    //     0xb3f1a4: add             lr, NULL, #0x20  ; true
    // 0xb3f1a8: stp             lr, x16, [SP, #0x38]
    // 0xb3f1ac: r16 = Instance_AutovalidateMode
    //     0xb3f1ac: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb3f1b0: ldr             x16, [x16, #0x7e8]
    // 0xb3f1b4: ldur            lr, [fp, #-0x48]
    // 0xb3f1b8: stp             lr, x16, [SP, #0x28]
    // 0xb3f1bc: ldur            x16, [fp, #-0x58]
    // 0xb3f1c0: r30 = Instance_TextInputType
    //     0xb3f1c0: add             lr, PP, #0x54, lsl #12  ; [pp+0x54068] Obj!TextInputType@d55ba1
    //     0xb3f1c4: ldr             lr, [lr, #0x68]
    // 0xb3f1c8: stp             lr, x16, [SP, #0x18]
    // 0xb3f1cc: r16 = 2
    //     0xb3f1cc: movz            x16, #0x2
    // 0xb3f1d0: ldur            lr, [fp, #-0x50]
    // 0xb3f1d4: stp             lr, x16, [SP, #8]
    // 0xb3f1d8: ldur            x16, [fp, #-0x78]
    // 0xb3f1dc: str             x16, [SP]
    // 0xb3f1e0: mov             x1, x0
    // 0xb3f1e4: ldur            x2, [fp, #-0x68]
    // 0xb3f1e8: r4 = const [0, 0xb, 0x9, 0x2, autovalidateMode, 0x4, controller, 0x9, enableSuggestions, 0x3, inputFormatters, 0x5, keyboardType, 0x7, maxLines, 0x8, onChanged, 0xa, style, 0x6, validator, 0x2, null]
    //     0xb3f1e8: add             x4, PP, #0x56, lsl #12  ; [pp+0x56880] List(23) [0, 0xb, 0x9, 0x2, "autovalidateMode", 0x4, "controller", 0x9, "enableSuggestions", 0x3, "inputFormatters", 0x5, "keyboardType", 0x7, "maxLines", 0x8, "onChanged", 0xa, "style", 0x6, "validator", 0x2, Null]
    //     0xb3f1ec: ldr             x4, [x4, #0x880]
    // 0xb3f1f0: r0 = TextFormField()
    //     0xb3f1f0: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb3f1f4: r0 = Form()
    //     0xb3f1f4: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb3f1f8: mov             x3, x0
    // 0xb3f1fc: ldur            x0, [fp, #-0x80]
    // 0xb3f200: stur            x3, [fp, #-0x48]
    // 0xb3f204: StoreField: r3->field_b = r0
    //     0xb3f204: stur            w0, [x3, #0xb]
    // 0xb3f208: r0 = Instance_AutovalidateMode
    //     0xb3f208: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb3f20c: ldr             x0, [x0, #0x800]
    // 0xb3f210: StoreField: r3->field_23 = r0
    //     0xb3f210: stur            w0, [x3, #0x23]
    // 0xb3f214: ldur            x1, [fp, #-0x30]
    // 0xb3f218: StoreField: r3->field_7 = r1
    //     0xb3f218: stur            w1, [x3, #7]
    // 0xb3f21c: r1 = Null
    //     0xb3f21c: mov             x1, NULL
    // 0xb3f220: r2 = 4
    //     0xb3f220: movz            x2, #0x4
    // 0xb3f224: r0 = AllocateArray()
    //     0xb3f224: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3f228: mov             x2, x0
    // 0xb3f22c: ldur            x0, [fp, #-0x40]
    // 0xb3f230: stur            x2, [fp, #-0x30]
    // 0xb3f234: StoreField: r2->field_f = r0
    //     0xb3f234: stur            w0, [x2, #0xf]
    // 0xb3f238: ldur            x0, [fp, #-0x48]
    // 0xb3f23c: StoreField: r2->field_13 = r0
    //     0xb3f23c: stur            w0, [x2, #0x13]
    // 0xb3f240: r1 = <Widget>
    //     0xb3f240: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3f244: r0 = AllocateGrowableArray()
    //     0xb3f244: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3f248: mov             x1, x0
    // 0xb3f24c: ldur            x0, [fp, #-0x30]
    // 0xb3f250: stur            x1, [fp, #-0x40]
    // 0xb3f254: StoreField: r1->field_f = r0
    //     0xb3f254: stur            w0, [x1, #0xf]
    // 0xb3f258: r2 = 4
    //     0xb3f258: movz            x2, #0x4
    // 0xb3f25c: StoreField: r1->field_b = r2
    //     0xb3f25c: stur            w2, [x1, #0xb]
    // 0xb3f260: r0 = Column()
    //     0xb3f260: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3f264: mov             x1, x0
    // 0xb3f268: r0 = Instance_Axis
    //     0xb3f268: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3f26c: stur            x1, [fp, #-0x30]
    // 0xb3f270: StoreField: r1->field_f = r0
    //     0xb3f270: stur            w0, [x1, #0xf]
    // 0xb3f274: r2 = Instance_MainAxisAlignment
    //     0xb3f274: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3f278: ldr             x2, [x2, #0xa08]
    // 0xb3f27c: StoreField: r1->field_13 = r2
    //     0xb3f27c: stur            w2, [x1, #0x13]
    // 0xb3f280: r3 = Instance_MainAxisSize
    //     0xb3f280: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3f284: ldr             x3, [x3, #0xa10]
    // 0xb3f288: ArrayStore: r1[0] = r3  ; List_4
    //     0xb3f288: stur            w3, [x1, #0x17]
    // 0xb3f28c: r4 = Instance_CrossAxisAlignment
    //     0xb3f28c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3f290: ldr             x4, [x4, #0x890]
    // 0xb3f294: StoreField: r1->field_1b = r4
    //     0xb3f294: stur            w4, [x1, #0x1b]
    // 0xb3f298: r5 = Instance_VerticalDirection
    //     0xb3f298: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3f29c: ldr             x5, [x5, #0xa20]
    // 0xb3f2a0: StoreField: r1->field_23 = r5
    //     0xb3f2a0: stur            w5, [x1, #0x23]
    // 0xb3f2a4: r6 = Instance_Clip
    //     0xb3f2a4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3f2a8: ldr             x6, [x6, #0x38]
    // 0xb3f2ac: StoreField: r1->field_2b = r6
    //     0xb3f2ac: stur            w6, [x1, #0x2b]
    // 0xb3f2b0: StoreField: r1->field_2f = rZR
    //     0xb3f2b0: stur            xzr, [x1, #0x2f]
    // 0xb3f2b4: ldur            x7, [fp, #-0x40]
    // 0xb3f2b8: StoreField: r1->field_b = r7
    //     0xb3f2b8: stur            w7, [x1, #0xb]
    // 0xb3f2bc: r0 = Padding()
    //     0xb3f2bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3f2c0: mov             x1, x0
    // 0xb3f2c4: r0 = Instance_EdgeInsets
    //     0xb3f2c4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0xb3f2c8: ldr             x0, [x0, #0xa98]
    // 0xb3f2cc: stur            x1, [fp, #-0x40]
    // 0xb3f2d0: StoreField: r1->field_f = r0
    //     0xb3f2d0: stur            w0, [x1, #0xf]
    // 0xb3f2d4: ldur            x2, [fp, #-0x30]
    // 0xb3f2d8: StoreField: r1->field_b = r2
    //     0xb3f2d8: stur            w2, [x1, #0xb]
    // 0xb3f2dc: r0 = Visibility()
    //     0xb3f2dc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb3f2e0: mov             x3, x0
    // 0xb3f2e4: ldur            x0, [fp, #-0x40]
    // 0xb3f2e8: stur            x3, [fp, #-0x30]
    // 0xb3f2ec: StoreField: r3->field_b = r0
    //     0xb3f2ec: stur            w0, [x3, #0xb]
    // 0xb3f2f0: r4 = Instance_SizedBox
    //     0xb3f2f0: ldr             x4, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb3f2f4: StoreField: r3->field_f = r4
    //     0xb3f2f4: stur            w4, [x3, #0xf]
    // 0xb3f2f8: ldur            x0, [fp, #-0x38]
    // 0xb3f2fc: StoreField: r3->field_13 = r0
    //     0xb3f2fc: stur            w0, [x3, #0x13]
    // 0xb3f300: r5 = false
    //     0xb3f300: add             x5, NULL, #0x30  ; false
    // 0xb3f304: ArrayStore: r3[0] = r5  ; List_4
    //     0xb3f304: stur            w5, [x3, #0x17]
    // 0xb3f308: StoreField: r3->field_1b = r5
    //     0xb3f308: stur            w5, [x3, #0x1b]
    // 0xb3f30c: StoreField: r3->field_1f = r5
    //     0xb3f30c: stur            w5, [x3, #0x1f]
    // 0xb3f310: StoreField: r3->field_23 = r5
    //     0xb3f310: stur            w5, [x3, #0x23]
    // 0xb3f314: StoreField: r3->field_27 = r5
    //     0xb3f314: stur            w5, [x3, #0x27]
    // 0xb3f318: StoreField: r3->field_2b = r5
    //     0xb3f318: stur            w5, [x3, #0x2b]
    // 0xb3f31c: ldur            x6, [fp, #-8]
    // 0xb3f320: LoadField: r0 = r6->field_b
    //     0xb3f320: ldur            w0, [x6, #0xb]
    // 0xb3f324: DecompressPointer r0
    //     0xb3f324: add             x0, x0, HEAP, lsl #32
    // 0xb3f328: cmp             w0, NULL
    // 0xb3f32c: b.eq            #0xb3fdac
    // 0xb3f330: LoadField: r1 = r0->field_b
    //     0xb3f330: ldur            w1, [x0, #0xb]
    // 0xb3f334: DecompressPointer r1
    //     0xb3f334: add             x1, x1, HEAP, lsl #32
    // 0xb3f338: LoadField: r0 = r1->field_f
    //     0xb3f338: ldur            w0, [x1, #0xf]
    // 0xb3f33c: DecompressPointer r0
    //     0xb3f33c: add             x0, x0, HEAP, lsl #32
    // 0xb3f340: cmp             w0, NULL
    // 0xb3f344: b.ne            #0xb3f350
    // 0xb3f348: r0 = Null
    //     0xb3f348: mov             x0, NULL
    // 0xb3f34c: b               #0xb3f37c
    // 0xb3f350: r1 = LoadClassIdInstr(r0)
    //     0xb3f350: ldur            x1, [x0, #-1]
    //     0xb3f354: ubfx            x1, x1, #0xc, #0x14
    // 0xb3f358: mov             x16, x0
    // 0xb3f35c: mov             x0, x1
    // 0xb3f360: mov             x1, x16
    // 0xb3f364: r2 = "alternate_contact_number"
    //     0xb3f364: add             x2, PP, #0x54, lsl #12  ; [pp+0x54140] "alternate_contact_number"
    //     0xb3f368: ldr             x2, [x2, #0x140]
    // 0xb3f36c: r0 = GDT[cid_x0 + 0xe437]()
    //     0xb3f36c: movz            x17, #0xe437
    //     0xb3f370: add             lr, x0, x17
    //     0xb3f374: ldr             lr, [x21, lr, lsl #3]
    //     0xb3f378: blr             lr
    // 0xb3f37c: cmp             w0, NULL
    // 0xb3f380: b.ne            #0xb3f38c
    // 0xb3f384: r3 = false
    //     0xb3f384: add             x3, NULL, #0x30  ; false
    // 0xb3f388: b               #0xb3f390
    // 0xb3f38c: mov             x3, x0
    // 0xb3f390: ldur            x2, [fp, #-8]
    // 0xb3f394: ldur            x0, [fp, #-0x60]
    // 0xb3f398: ldur            x1, [fp, #-0x10]
    // 0xb3f39c: stur            x3, [fp, #-0x38]
    // 0xb3f3a0: r0 = of()
    //     0xb3f3a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3f3a4: LoadField: r1 = r0->field_87
    //     0xb3f3a4: ldur            w1, [x0, #0x87]
    // 0xb3f3a8: DecompressPointer r1
    //     0xb3f3a8: add             x1, x1, HEAP, lsl #32
    // 0xb3f3ac: LoadField: r0 = r1->field_2b
    //     0xb3f3ac: ldur            w0, [x1, #0x2b]
    // 0xb3f3b0: DecompressPointer r0
    //     0xb3f3b0: add             x0, x0, HEAP, lsl #32
    // 0xb3f3b4: r16 = 12.000000
    //     0xb3f3b4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3f3b8: ldr             x16, [x16, #0x9e8]
    // 0xb3f3bc: r30 = Instance_Color
    //     0xb3f3bc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3f3c0: stp             lr, x16, [SP]
    // 0xb3f3c4: mov             x1, x0
    // 0xb3f3c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3f3c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3f3cc: ldr             x4, [x4, #0xaa0]
    // 0xb3f3d0: r0 = copyWith()
    //     0xb3f3d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3f3d4: stur            x0, [fp, #-0x40]
    // 0xb3f3d8: r0 = Text()
    //     0xb3f3d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3f3dc: mov             x1, x0
    // 0xb3f3e0: r0 = "Alternate Ph No"
    //     0xb3f3e0: add             x0, PP, #0x54, lsl #12  ; [pp+0x54148] "Alternate Ph No"
    //     0xb3f3e4: ldr             x0, [x0, #0x148]
    // 0xb3f3e8: stur            x1, [fp, #-0x48]
    // 0xb3f3ec: StoreField: r1->field_b = r0
    //     0xb3f3ec: stur            w0, [x1, #0xb]
    // 0xb3f3f0: ldur            x0, [fp, #-0x40]
    // 0xb3f3f4: StoreField: r1->field_13 = r0
    //     0xb3f3f4: stur            w0, [x1, #0x13]
    // 0xb3f3f8: r0 = Padding()
    //     0xb3f3f8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3f3fc: mov             x1, x0
    // 0xb3f400: r0 = Instance_EdgeInsets
    //     0xb3f400: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f4c0] Obj!EdgeInsets@d579e1
    //     0xb3f404: ldr             x0, [x0, #0x4c0]
    // 0xb3f408: stur            x1, [fp, #-0x50]
    // 0xb3f40c: StoreField: r1->field_f = r0
    //     0xb3f40c: stur            w0, [x1, #0xf]
    // 0xb3f410: ldur            x0, [fp, #-0x48]
    // 0xb3f414: StoreField: r1->field_b = r0
    //     0xb3f414: stur            w0, [x1, #0xb]
    // 0xb3f418: ldur            x2, [fp, #-8]
    // 0xb3f41c: LoadField: r0 = r2->field_2b
    //     0xb3f41c: ldur            w0, [x2, #0x2b]
    // 0xb3f420: DecompressPointer r0
    //     0xb3f420: add             x0, x0, HEAP, lsl #32
    // 0xb3f424: stur            x0, [fp, #-0x40]
    // 0xb3f428: r16 = "[0-9]"
    //     0xb3f428: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0xb3f42c: ldr             x16, [x16, #0x128]
    // 0xb3f430: stp             x16, NULL, [SP, #0x20]
    // 0xb3f434: r16 = false
    //     0xb3f434: add             x16, NULL, #0x30  ; false
    // 0xb3f438: r30 = true
    //     0xb3f438: add             lr, NULL, #0x20  ; true
    // 0xb3f43c: stp             lr, x16, [SP, #0x10]
    // 0xb3f440: r16 = false
    //     0xb3f440: add             x16, NULL, #0x30  ; false
    // 0xb3f444: r30 = false
    //     0xb3f444: add             lr, NULL, #0x30  ; false
    // 0xb3f448: stp             lr, x16, [SP]
    // 0xb3f44c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xb3f44c: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xb3f450: r0 = _RegExp()
    //     0xb3f450: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xb3f454: stur            x0, [fp, #-0x48]
    // 0xb3f458: r0 = FilteringTextInputFormatter()
    //     0xb3f458: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xb3f45c: mov             x1, x0
    // 0xb3f460: ldur            x0, [fp, #-0x48]
    // 0xb3f464: stur            x1, [fp, #-0x58]
    // 0xb3f468: StoreField: r1->field_b = r0
    //     0xb3f468: stur            w0, [x1, #0xb]
    // 0xb3f46c: r0 = true
    //     0xb3f46c: add             x0, NULL, #0x20  ; true
    // 0xb3f470: StoreField: r1->field_7 = r0
    //     0xb3f470: stur            w0, [x1, #7]
    // 0xb3f474: r2 = ""
    //     0xb3f474: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3f478: StoreField: r1->field_f = r2
    //     0xb3f478: stur            w2, [x1, #0xf]
    // 0xb3f47c: r0 = LengthLimitingTextInputFormatter()
    //     0xb3f47c: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb3f480: mov             x3, x0
    // 0xb3f484: r0 = 20
    //     0xb3f484: movz            x0, #0x14
    // 0xb3f488: stur            x3, [fp, #-0x48]
    // 0xb3f48c: StoreField: r3->field_7 = r0
    //     0xb3f48c: stur            w0, [x3, #7]
    // 0xb3f490: r1 = Null
    //     0xb3f490: mov             x1, NULL
    // 0xb3f494: r2 = 6
    //     0xb3f494: movz            x2, #0x6
    // 0xb3f498: r0 = AllocateArray()
    //     0xb3f498: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3f49c: mov             x2, x0
    // 0xb3f4a0: ldur            x0, [fp, #-0x60]
    // 0xb3f4a4: stur            x2, [fp, #-0x68]
    // 0xb3f4a8: StoreField: r2->field_f = r0
    //     0xb3f4a8: stur            w0, [x2, #0xf]
    // 0xb3f4ac: ldur            x0, [fp, #-0x58]
    // 0xb3f4b0: StoreField: r2->field_13 = r0
    //     0xb3f4b0: stur            w0, [x2, #0x13]
    // 0xb3f4b4: ldur            x0, [fp, #-0x48]
    // 0xb3f4b8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3f4b8: stur            w0, [x2, #0x17]
    // 0xb3f4bc: r1 = <TextInputFormatter>
    //     0xb3f4bc: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb3f4c0: ldr             x1, [x1, #0x7b0]
    // 0xb3f4c4: r0 = AllocateGrowableArray()
    //     0xb3f4c4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3f4c8: mov             x2, x0
    // 0xb3f4cc: ldur            x0, [fp, #-0x68]
    // 0xb3f4d0: stur            x2, [fp, #-0x48]
    // 0xb3f4d4: StoreField: r2->field_f = r0
    //     0xb3f4d4: stur            w0, [x2, #0xf]
    // 0xb3f4d8: r0 = 6
    //     0xb3f4d8: movz            x0, #0x6
    // 0xb3f4dc: StoreField: r2->field_b = r0
    //     0xb3f4dc: stur            w0, [x2, #0xb]
    // 0xb3f4e0: ldur            x1, [fp, #-0x10]
    // 0xb3f4e4: r0 = of()
    //     0xb3f4e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3f4e8: LoadField: r1 = r0->field_87
    //     0xb3f4e8: ldur            w1, [x0, #0x87]
    // 0xb3f4ec: DecompressPointer r1
    //     0xb3f4ec: add             x1, x1, HEAP, lsl #32
    // 0xb3f4f0: LoadField: r0 = r1->field_2b
    //     0xb3f4f0: ldur            w0, [x1, #0x2b]
    // 0xb3f4f4: DecompressPointer r0
    //     0xb3f4f4: add             x0, x0, HEAP, lsl #32
    // 0xb3f4f8: ldur            x1, [fp, #-0x10]
    // 0xb3f4fc: stur            x0, [fp, #-0x58]
    // 0xb3f500: r0 = of()
    //     0xb3f500: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3f504: LoadField: r1 = r0->field_5b
    //     0xb3f504: ldur            w1, [x0, #0x5b]
    // 0xb3f508: DecompressPointer r1
    //     0xb3f508: add             x1, x1, HEAP, lsl #32
    // 0xb3f50c: r16 = 14.000000
    //     0xb3f50c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3f510: ldr             x16, [x16, #0x1d8]
    // 0xb3f514: stp             x16, x1, [SP]
    // 0xb3f518: ldur            x1, [fp, #-0x58]
    // 0xb3f51c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb3f51c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb3f520: ldr             x4, [x4, #0x9b8]
    // 0xb3f524: r0 = copyWith()
    //     0xb3f524: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3f528: ldur            x2, [fp, #-8]
    // 0xb3f52c: stur            x0, [fp, #-0x60]
    // 0xb3f530: LoadField: r3 = r2->field_43
    //     0xb3f530: ldur            w3, [x2, #0x43]
    // 0xb3f534: DecompressPointer r3
    //     0xb3f534: add             x3, x3, HEAP, lsl #32
    // 0xb3f538: ldur            x1, [fp, #-0x10]
    // 0xb3f53c: stur            x3, [fp, #-0x58]
    // 0xb3f540: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb3f540: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb3f544: ldur            x1, [fp, #-0x10]
    // 0xb3f548: stur            x0, [fp, #-0x68]
    // 0xb3f54c: r0 = of()
    //     0xb3f54c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3f550: LoadField: r1 = r0->field_5b
    //     0xb3f550: ldur            w1, [x0, #0x5b]
    // 0xb3f554: DecompressPointer r1
    //     0xb3f554: add             x1, x1, HEAP, lsl #32
    // 0xb3f558: stur            x1, [fp, #-0x70]
    // 0xb3f55c: r0 = BorderSide()
    //     0xb3f55c: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb3f560: mov             x1, x0
    // 0xb3f564: ldur            x0, [fp, #-0x70]
    // 0xb3f568: stur            x1, [fp, #-0x78]
    // 0xb3f56c: StoreField: r1->field_7 = r0
    //     0xb3f56c: stur            w0, [x1, #7]
    // 0xb3f570: d0 = 1.000000
    //     0xb3f570: fmov            d0, #1.00000000
    // 0xb3f574: StoreField: r1->field_b = d0
    //     0xb3f574: stur            d0, [x1, #0xb]
    // 0xb3f578: r0 = Instance_BorderStyle
    //     0xb3f578: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb3f57c: ldr             x0, [x0, #0xf68]
    // 0xb3f580: StoreField: r1->field_13 = r0
    //     0xb3f580: stur            w0, [x1, #0x13]
    // 0xb3f584: d0 = -1.000000
    //     0xb3f584: fmov            d0, #-1.00000000
    // 0xb3f588: ArrayStore: r1[0] = d0  ; List_8
    //     0xb3f588: stur            d0, [x1, #0x17]
    // 0xb3f58c: r0 = Radius()
    //     0xb3f58c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb3f590: d0 = 30.000000
    //     0xb3f590: fmov            d0, #30.00000000
    // 0xb3f594: stur            x0, [fp, #-0x70]
    // 0xb3f598: StoreField: r0->field_7 = d0
    //     0xb3f598: stur            d0, [x0, #7]
    // 0xb3f59c: StoreField: r0->field_f = d0
    //     0xb3f59c: stur            d0, [x0, #0xf]
    // 0xb3f5a0: r0 = BorderRadius()
    //     0xb3f5a0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb3f5a4: mov             x1, x0
    // 0xb3f5a8: ldur            x0, [fp, #-0x70]
    // 0xb3f5ac: stur            x1, [fp, #-0x80]
    // 0xb3f5b0: StoreField: r1->field_7 = r0
    //     0xb3f5b0: stur            w0, [x1, #7]
    // 0xb3f5b4: StoreField: r1->field_b = r0
    //     0xb3f5b4: stur            w0, [x1, #0xb]
    // 0xb3f5b8: StoreField: r1->field_f = r0
    //     0xb3f5b8: stur            w0, [x1, #0xf]
    // 0xb3f5bc: StoreField: r1->field_13 = r0
    //     0xb3f5bc: stur            w0, [x1, #0x13]
    // 0xb3f5c0: r0 = OutlineInputBorder()
    //     0xb3f5c0: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xb3f5c4: mov             x2, x0
    // 0xb3f5c8: ldur            x0, [fp, #-0x80]
    // 0xb3f5cc: stur            x2, [fp, #-0x70]
    // 0xb3f5d0: StoreField: r2->field_13 = r0
    //     0xb3f5d0: stur            w0, [x2, #0x13]
    // 0xb3f5d4: d0 = 4.000000
    //     0xb3f5d4: fmov            d0, #4.00000000
    // 0xb3f5d8: StoreField: r2->field_b = d0
    //     0xb3f5d8: stur            d0, [x2, #0xb]
    // 0xb3f5dc: ldur            x0, [fp, #-0x78]
    // 0xb3f5e0: StoreField: r2->field_7 = r0
    //     0xb3f5e0: stur            w0, [x2, #7]
    // 0xb3f5e4: ldur            x1, [fp, #-0x10]
    // 0xb3f5e8: r0 = of()
    //     0xb3f5e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3f5ec: LoadField: r1 = r0->field_87
    //     0xb3f5ec: ldur            w1, [x0, #0x87]
    // 0xb3f5f0: DecompressPointer r1
    //     0xb3f5f0: add             x1, x1, HEAP, lsl #32
    // 0xb3f5f4: LoadField: r0 = r1->field_2b
    //     0xb3f5f4: ldur            w0, [x1, #0x2b]
    // 0xb3f5f8: DecompressPointer r0
    //     0xb3f5f8: add             x0, x0, HEAP, lsl #32
    // 0xb3f5fc: stur            x0, [fp, #-0x78]
    // 0xb3f600: r1 = Instance_Color
    //     0xb3f600: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3f604: d0 = 0.400000
    //     0xb3f604: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb3f608: r0 = withOpacity()
    //     0xb3f608: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb3f60c: r16 = 14.000000
    //     0xb3f60c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3f610: ldr             x16, [x16, #0x1d8]
    // 0xb3f614: stp             x0, x16, [SP]
    // 0xb3f618: ldur            x1, [fp, #-0x78]
    // 0xb3f61c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3f61c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3f620: ldr             x4, [x4, #0xaa0]
    // 0xb3f624: r0 = copyWith()
    //     0xb3f624: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3f628: ldur            x1, [fp, #-0x10]
    // 0xb3f62c: stur            x0, [fp, #-0x78]
    // 0xb3f630: r0 = of()
    //     0xb3f630: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3f634: LoadField: r1 = r0->field_87
    //     0xb3f634: ldur            w1, [x0, #0x87]
    // 0xb3f638: DecompressPointer r1
    //     0xb3f638: add             x1, x1, HEAP, lsl #32
    // 0xb3f63c: LoadField: r0 = r1->field_2b
    //     0xb3f63c: ldur            w0, [x1, #0x2b]
    // 0xb3f640: DecompressPointer r0
    //     0xb3f640: add             x0, x0, HEAP, lsl #32
    // 0xb3f644: r16 = 12.000000
    //     0xb3f644: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3f648: ldr             x16, [x16, #0x9e8]
    // 0xb3f64c: r30 = Instance_MaterialColor
    //     0xb3f64c: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb3f650: ldr             lr, [lr, #0x180]
    // 0xb3f654: stp             lr, x16, [SP]
    // 0xb3f658: mov             x1, x0
    // 0xb3f65c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3f65c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3f660: ldr             x4, [x4, #0xaa0]
    // 0xb3f664: r0 = copyWith()
    //     0xb3f664: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3f668: mov             x3, x0
    // 0xb3f66c: ldur            x0, [fp, #-8]
    // 0xb3f670: stur            x3, [fp, #-0x80]
    // 0xb3f674: LoadField: r1 = r0->field_63
    //     0xb3f674: ldur            w1, [x0, #0x63]
    // 0xb3f678: DecompressPointer r1
    //     0xb3f678: add             x1, x1, HEAP, lsl #32
    // 0xb3f67c: tbnz            w1, #4, #0xb3f7a4
    // 0xb3f680: LoadField: r1 = r0->field_43
    //     0xb3f680: ldur            w1, [x0, #0x43]
    // 0xb3f684: DecompressPointer r1
    //     0xb3f684: add             x1, x1, HEAP, lsl #32
    // 0xb3f688: LoadField: r2 = r1->field_27
    //     0xb3f688: ldur            w2, [x1, #0x27]
    // 0xb3f68c: DecompressPointer r2
    //     0xb3f68c: add             x2, x2, HEAP, lsl #32
    // 0xb3f690: LoadField: r1 = r2->field_7
    //     0xb3f690: ldur            w1, [x2, #7]
    // 0xb3f694: DecompressPointer r1
    //     0xb3f694: add             x1, x1, HEAP, lsl #32
    // 0xb3f698: LoadField: r2 = r1->field_7
    //     0xb3f698: ldur            w2, [x1, #7]
    // 0xb3f69c: cbz             w2, #0xb3f6e0
    // 0xb3f6a0: cmp             w2, #0x14
    // 0xb3f6a4: b.ne            #0xb3f6e0
    // 0xb3f6a8: r16 = 2
    //     0xb3f6a8: movz            x16, #0x2
    // 0xb3f6ac: str             x16, [SP]
    // 0xb3f6b0: r2 = 0
    //     0xb3f6b0: movz            x2, #0
    // 0xb3f6b4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xb3f6b4: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xb3f6b8: r0 = substring()
    //     0xb3f6b8: bl              #0x61f5c0  ; [dart:core] _StringBase::substring
    // 0xb3f6bc: mov             x1, x0
    // 0xb3f6c0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb3f6c0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb3f6c4: r0 = parse()
    //     0xb3f6c4: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xb3f6c8: cmp             x0, #6
    // 0xb3f6cc: b.lt            #0xb3f6e0
    // 0xb3f6d0: ldur            x0, [fp, #-8]
    // 0xb3f6d4: r3 = Instance_IconData
    //     0xb3f6d4: add             x3, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb3f6d8: ldr             x3, [x3, #0x130]
    // 0xb3f6dc: b               #0xb3f718
    // 0xb3f6e0: ldur            x0, [fp, #-8]
    // 0xb3f6e4: LoadField: r1 = r0->field_43
    //     0xb3f6e4: ldur            w1, [x0, #0x43]
    // 0xb3f6e8: DecompressPointer r1
    //     0xb3f6e8: add             x1, x1, HEAP, lsl #32
    // 0xb3f6ec: LoadField: r2 = r1->field_27
    //     0xb3f6ec: ldur            w2, [x1, #0x27]
    // 0xb3f6f0: DecompressPointer r2
    //     0xb3f6f0: add             x2, x2, HEAP, lsl #32
    // 0xb3f6f4: LoadField: r1 = r2->field_7
    //     0xb3f6f4: ldur            w1, [x2, #7]
    // 0xb3f6f8: DecompressPointer r1
    //     0xb3f6f8: add             x1, x1, HEAP, lsl #32
    // 0xb3f6fc: LoadField: r2 = r1->field_7
    //     0xb3f6fc: ldur            w2, [x1, #7]
    // 0xb3f700: cbz             w2, #0xb3f710
    // 0xb3f704: r1 = Instance_IconData
    //     0xb3f704: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb3f708: ldr             x1, [x1, #0x138]
    // 0xb3f70c: b               #0xb3f714
    // 0xb3f710: r1 = Null
    //     0xb3f710: mov             x1, NULL
    // 0xb3f714: mov             x3, x1
    // 0xb3f718: stur            x3, [fp, #-0x88]
    // 0xb3f71c: LoadField: r1 = r0->field_43
    //     0xb3f71c: ldur            w1, [x0, #0x43]
    // 0xb3f720: DecompressPointer r1
    //     0xb3f720: add             x1, x1, HEAP, lsl #32
    // 0xb3f724: LoadField: r2 = r1->field_27
    //     0xb3f724: ldur            w2, [x1, #0x27]
    // 0xb3f728: DecompressPointer r2
    //     0xb3f728: add             x2, x2, HEAP, lsl #32
    // 0xb3f72c: LoadField: r1 = r2->field_7
    //     0xb3f72c: ldur            w1, [x2, #7]
    // 0xb3f730: DecompressPointer r1
    //     0xb3f730: add             x1, x1, HEAP, lsl #32
    // 0xb3f734: LoadField: r2 = r1->field_7
    //     0xb3f734: ldur            w2, [x1, #7]
    // 0xb3f738: cbz             w2, #0xb3f778
    // 0xb3f73c: cmp             w2, #0x14
    // 0xb3f740: b.ne            #0xb3f778
    // 0xb3f744: r16 = 2
    //     0xb3f744: movz            x16, #0x2
    // 0xb3f748: str             x16, [SP]
    // 0xb3f74c: r2 = 0
    //     0xb3f74c: movz            x2, #0
    // 0xb3f750: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xb3f750: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xb3f754: r0 = substring()
    //     0xb3f754: bl              #0x61f5c0  ; [dart:core] _StringBase::substring
    // 0xb3f758: mov             x1, x0
    // 0xb3f75c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb3f75c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb3f760: r0 = parse()
    //     0xb3f760: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xb3f764: cmp             x0, #6
    // 0xb3f768: b.lt            #0xb3f778
    // 0xb3f76c: r1 = Instance_Color
    //     0xb3f76c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb3f770: ldr             x1, [x1, #0x858]
    // 0xb3f774: b               #0xb3f780
    // 0xb3f778: r1 = Instance_Color
    //     0xb3f778: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb3f77c: ldr             x1, [x1, #0x50]
    // 0xb3f780: ldur            x0, [fp, #-0x88]
    // 0xb3f784: stur            x1, [fp, #-0x90]
    // 0xb3f788: r0 = Icon()
    //     0xb3f788: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb3f78c: mov             x1, x0
    // 0xb3f790: ldur            x0, [fp, #-0x88]
    // 0xb3f794: StoreField: r1->field_b = r0
    //     0xb3f794: stur            w0, [x1, #0xb]
    // 0xb3f798: ldur            x0, [fp, #-0x90]
    // 0xb3f79c: StoreField: r1->field_23 = r0
    //     0xb3f79c: stur            w0, [x1, #0x23]
    // 0xb3f7a0: b               #0xb3f7a8
    // 0xb3f7a4: r1 = Null
    //     0xb3f7a4: mov             x1, NULL
    // 0xb3f7a8: ldur            x2, [fp, #-8]
    // 0xb3f7ac: ldur            x4, [fp, #-0x38]
    // 0xb3f7b0: ldur            x0, [fp, #-0x50]
    // 0xb3f7b4: ldur            x3, [fp, #-0x40]
    // 0xb3f7b8: ldur            x16, [fp, #-0x70]
    // 0xb3f7bc: r30 = Instance_EdgeInsets
    //     0xb3f7bc: add             lr, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb3f7c0: ldr             lr, [lr, #0xa78]
    // 0xb3f7c4: stp             lr, x16, [SP, #0x20]
    // 0xb3f7c8: r16 = "Alternate Ph No"
    //     0xb3f7c8: add             x16, PP, #0x54, lsl #12  ; [pp+0x54148] "Alternate Ph No"
    //     0xb3f7cc: ldr             x16, [x16, #0x148]
    // 0xb3f7d0: ldur            lr, [fp, #-0x78]
    // 0xb3f7d4: stp             lr, x16, [SP, #0x10]
    // 0xb3f7d8: ldur            x16, [fp, #-0x80]
    // 0xb3f7dc: stp             x1, x16, [SP]
    // 0xb3f7e0: ldur            x1, [fp, #-0x68]
    // 0xb3f7e4: r4 = const [0, 0x7, 0x6, 0x1, contentPadding, 0x2, errorStyle, 0x5, focusedBorder, 0x1, hintStyle, 0x4, hintText, 0x3, suffixIcon, 0x6, null]
    //     0xb3f7e4: add             x4, PP, #0x56, lsl #12  ; [pp+0x56ef8] List(17) [0, 0x7, 0x6, 0x1, "contentPadding", 0x2, "errorStyle", 0x5, "focusedBorder", 0x1, "hintStyle", 0x4, "hintText", 0x3, "suffixIcon", 0x6, Null]
    //     0xb3f7e8: ldr             x4, [x4, #0xef8]
    // 0xb3f7ec: r0 = copyWith()
    //     0xb3f7ec: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb3f7f0: ldur            x2, [fp, #-8]
    // 0xb3f7f4: r1 = Function '_validateAlternateNo@1537065735':.
    //     0xb3f7f4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f40] AnonymousClosure: (0xb404e8), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateAlternateNo (0xa01af0)
    //     0xb3f7f8: ldr             x1, [x1, #0xf40]
    // 0xb3f7fc: stur            x0, [fp, #-0x68]
    // 0xb3f800: r0 = AllocateClosure()
    //     0xb3f800: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3f804: ldur            x2, [fp, #-0x18]
    // 0xb3f808: r1 = Function '<anonymous closure>':.
    //     0xb3f808: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f48] AnonymousClosure: (0xb402e0), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xb3ca88)
    //     0xb3f80c: ldr             x1, [x1, #0xf48]
    // 0xb3f810: stur            x0, [fp, #-0x70]
    // 0xb3f814: r0 = AllocateClosure()
    //     0xb3f814: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3f818: r1 = <String>
    //     0xb3f818: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb3f81c: stur            x0, [fp, #-0x78]
    // 0xb3f820: r0 = TextFormField()
    //     0xb3f820: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb3f824: stur            x0, [fp, #-0x80]
    // 0xb3f828: ldur            x16, [fp, #-0x70]
    // 0xb3f82c: r30 = true
    //     0xb3f82c: add             lr, NULL, #0x20  ; true
    // 0xb3f830: stp             lr, x16, [SP, #0x38]
    // 0xb3f834: r16 = Instance_AutovalidateMode
    //     0xb3f834: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb3f838: ldr             x16, [x16, #0x7e8]
    // 0xb3f83c: ldur            lr, [fp, #-0x48]
    // 0xb3f840: stp             lr, x16, [SP, #0x28]
    // 0xb3f844: ldur            x16, [fp, #-0x60]
    // 0xb3f848: r30 = Instance_TextInputType
    //     0xb3f848: add             lr, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xb3f84c: ldr             lr, [lr, #0x1a0]
    // 0xb3f850: stp             lr, x16, [SP, #0x18]
    // 0xb3f854: r16 = 2
    //     0xb3f854: movz            x16, #0x2
    // 0xb3f858: ldur            lr, [fp, #-0x58]
    // 0xb3f85c: stp             lr, x16, [SP, #8]
    // 0xb3f860: ldur            x16, [fp, #-0x78]
    // 0xb3f864: str             x16, [SP]
    // 0xb3f868: mov             x1, x0
    // 0xb3f86c: ldur            x2, [fp, #-0x68]
    // 0xb3f870: r4 = const [0, 0xb, 0x9, 0x2, autovalidateMode, 0x4, controller, 0x9, enableSuggestions, 0x3, inputFormatters, 0x5, keyboardType, 0x7, maxLines, 0x8, onChanged, 0xa, style, 0x6, validator, 0x2, null]
    //     0xb3f870: add             x4, PP, #0x56, lsl #12  ; [pp+0x56880] List(23) [0, 0xb, 0x9, 0x2, "autovalidateMode", 0x4, "controller", 0x9, "enableSuggestions", 0x3, "inputFormatters", 0x5, "keyboardType", 0x7, "maxLines", 0x8, "onChanged", 0xa, "style", 0x6, "validator", 0x2, Null]
    //     0xb3f874: ldr             x4, [x4, #0x880]
    // 0xb3f878: r0 = TextFormField()
    //     0xb3f878: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb3f87c: r0 = Form()
    //     0xb3f87c: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb3f880: mov             x3, x0
    // 0xb3f884: ldur            x0, [fp, #-0x80]
    // 0xb3f888: stur            x3, [fp, #-0x48]
    // 0xb3f88c: StoreField: r3->field_b = r0
    //     0xb3f88c: stur            w0, [x3, #0xb]
    // 0xb3f890: r0 = Instance_AutovalidateMode
    //     0xb3f890: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb3f894: ldr             x0, [x0, #0x800]
    // 0xb3f898: StoreField: r3->field_23 = r0
    //     0xb3f898: stur            w0, [x3, #0x23]
    // 0xb3f89c: ldur            x0, [fp, #-0x40]
    // 0xb3f8a0: StoreField: r3->field_7 = r0
    //     0xb3f8a0: stur            w0, [x3, #7]
    // 0xb3f8a4: r1 = Null
    //     0xb3f8a4: mov             x1, NULL
    // 0xb3f8a8: r2 = 4
    //     0xb3f8a8: movz            x2, #0x4
    // 0xb3f8ac: r0 = AllocateArray()
    //     0xb3f8ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3f8b0: mov             x2, x0
    // 0xb3f8b4: ldur            x0, [fp, #-0x50]
    // 0xb3f8b8: stur            x2, [fp, #-0x40]
    // 0xb3f8bc: StoreField: r2->field_f = r0
    //     0xb3f8bc: stur            w0, [x2, #0xf]
    // 0xb3f8c0: ldur            x0, [fp, #-0x48]
    // 0xb3f8c4: StoreField: r2->field_13 = r0
    //     0xb3f8c4: stur            w0, [x2, #0x13]
    // 0xb3f8c8: r1 = <Widget>
    //     0xb3f8c8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3f8cc: r0 = AllocateGrowableArray()
    //     0xb3f8cc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3f8d0: mov             x1, x0
    // 0xb3f8d4: ldur            x0, [fp, #-0x40]
    // 0xb3f8d8: stur            x1, [fp, #-0x48]
    // 0xb3f8dc: StoreField: r1->field_f = r0
    //     0xb3f8dc: stur            w0, [x1, #0xf]
    // 0xb3f8e0: r0 = 4
    //     0xb3f8e0: movz            x0, #0x4
    // 0xb3f8e4: StoreField: r1->field_b = r0
    //     0xb3f8e4: stur            w0, [x1, #0xb]
    // 0xb3f8e8: r0 = Column()
    //     0xb3f8e8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3f8ec: mov             x1, x0
    // 0xb3f8f0: r0 = Instance_Axis
    //     0xb3f8f0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3f8f4: stur            x1, [fp, #-0x40]
    // 0xb3f8f8: StoreField: r1->field_f = r0
    //     0xb3f8f8: stur            w0, [x1, #0xf]
    // 0xb3f8fc: r2 = Instance_MainAxisAlignment
    //     0xb3f8fc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3f900: ldr             x2, [x2, #0xa08]
    // 0xb3f904: StoreField: r1->field_13 = r2
    //     0xb3f904: stur            w2, [x1, #0x13]
    // 0xb3f908: r2 = Instance_MainAxisSize
    //     0xb3f908: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3f90c: ldr             x2, [x2, #0xa10]
    // 0xb3f910: ArrayStore: r1[0] = r2  ; List_4
    //     0xb3f910: stur            w2, [x1, #0x17]
    // 0xb3f914: r2 = Instance_CrossAxisAlignment
    //     0xb3f914: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3f918: ldr             x2, [x2, #0x890]
    // 0xb3f91c: StoreField: r1->field_1b = r2
    //     0xb3f91c: stur            w2, [x1, #0x1b]
    // 0xb3f920: r2 = Instance_VerticalDirection
    //     0xb3f920: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3f924: ldr             x2, [x2, #0xa20]
    // 0xb3f928: StoreField: r1->field_23 = r2
    //     0xb3f928: stur            w2, [x1, #0x23]
    // 0xb3f92c: r3 = Instance_Clip
    //     0xb3f92c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3f930: ldr             x3, [x3, #0x38]
    // 0xb3f934: StoreField: r1->field_2b = r3
    //     0xb3f934: stur            w3, [x1, #0x2b]
    // 0xb3f938: StoreField: r1->field_2f = rZR
    //     0xb3f938: stur            xzr, [x1, #0x2f]
    // 0xb3f93c: ldur            x4, [fp, #-0x48]
    // 0xb3f940: StoreField: r1->field_b = r4
    //     0xb3f940: stur            w4, [x1, #0xb]
    // 0xb3f944: r0 = Padding()
    //     0xb3f944: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3f948: mov             x1, x0
    // 0xb3f94c: r0 = Instance_EdgeInsets
    //     0xb3f94c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0xb3f950: ldr             x0, [x0, #0xa98]
    // 0xb3f954: stur            x1, [fp, #-0x48]
    // 0xb3f958: StoreField: r1->field_f = r0
    //     0xb3f958: stur            w0, [x1, #0xf]
    // 0xb3f95c: ldur            x0, [fp, #-0x40]
    // 0xb3f960: StoreField: r1->field_b = r0
    //     0xb3f960: stur            w0, [x1, #0xb]
    // 0xb3f964: r0 = Visibility()
    //     0xb3f964: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb3f968: mov             x2, x0
    // 0xb3f96c: ldur            x0, [fp, #-0x48]
    // 0xb3f970: stur            x2, [fp, #-0x40]
    // 0xb3f974: StoreField: r2->field_b = r0
    //     0xb3f974: stur            w0, [x2, #0xb]
    // 0xb3f978: r0 = Instance_SizedBox
    //     0xb3f978: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb3f97c: StoreField: r2->field_f = r0
    //     0xb3f97c: stur            w0, [x2, #0xf]
    // 0xb3f980: ldur            x0, [fp, #-0x38]
    // 0xb3f984: StoreField: r2->field_13 = r0
    //     0xb3f984: stur            w0, [x2, #0x13]
    // 0xb3f988: r0 = false
    //     0xb3f988: add             x0, NULL, #0x30  ; false
    // 0xb3f98c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3f98c: stur            w0, [x2, #0x17]
    // 0xb3f990: StoreField: r2->field_1b = r0
    //     0xb3f990: stur            w0, [x2, #0x1b]
    // 0xb3f994: StoreField: r2->field_1f = r0
    //     0xb3f994: stur            w0, [x2, #0x1f]
    // 0xb3f998: StoreField: r2->field_23 = r0
    //     0xb3f998: stur            w0, [x2, #0x23]
    // 0xb3f99c: StoreField: r2->field_27 = r0
    //     0xb3f99c: stur            w0, [x2, #0x27]
    // 0xb3f9a0: StoreField: r2->field_2b = r0
    //     0xb3f9a0: stur            w0, [x2, #0x2b]
    // 0xb3f9a4: ldur            x1, [fp, #-0x10]
    // 0xb3f9a8: r0 = of()
    //     0xb3f9a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3f9ac: LoadField: r1 = r0->field_5b
    //     0xb3f9ac: ldur            w1, [x0, #0x5b]
    // 0xb3f9b0: DecompressPointer r1
    //     0xb3f9b0: add             x1, x1, HEAP, lsl #32
    // 0xb3f9b4: r0 = LoadClassIdInstr(r1)
    //     0xb3f9b4: ldur            x0, [x1, #-1]
    //     0xb3f9b8: ubfx            x0, x0, #0xc, #0x14
    // 0xb3f9bc: d0 = 0.030000
    //     0xb3f9bc: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb3f9c0: ldr             d0, [x17, #0x238]
    // 0xb3f9c4: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb3f9c4: sub             lr, x0, #0xffa
    //     0xb3f9c8: ldr             lr, [x21, lr, lsl #3]
    //     0xb3f9cc: blr             lr
    // 0xb3f9d0: stur            x0, [fp, #-0x38]
    // 0xb3f9d4: r0 = Divider()
    //     0xb3f9d4: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xb3f9d8: mov             x3, x0
    // 0xb3f9dc: ldur            x0, [fp, #-0x38]
    // 0xb3f9e0: stur            x3, [fp, #-0x48]
    // 0xb3f9e4: StoreField: r3->field_1f = r0
    //     0xb3f9e4: stur            w0, [x3, #0x1f]
    // 0xb3f9e8: ldur            x0, [fp, #-8]
    // 0xb3f9ec: LoadField: r1 = r0->field_4b
    //     0xb3f9ec: ldur            w1, [x0, #0x4b]
    // 0xb3f9f0: DecompressPointer r1
    //     0xb3f9f0: add             x1, x1, HEAP, lsl #32
    // 0xb3f9f4: tbnz            w1, #4, #0xb3fa10
    // 0xb3f9f8: ldur            x2, [fp, #-0x18]
    // 0xb3f9fc: r1 = Function '<anonymous closure>':.
    //     0xb3f9fc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f50] AnonymousClosure: (0xb40158), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xb3ca88)
    //     0xb3fa00: ldr             x1, [x1, #0xf50]
    // 0xb3fa04: r0 = AllocateClosure()
    //     0xb3fa04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3fa08: mov             x1, x0
    // 0xb3fa0c: b               #0xb3fa14
    // 0xb3fa10: r1 = Null
    //     0xb3fa10: mov             x1, NULL
    // 0xb3fa14: ldur            x0, [fp, #-8]
    // 0xb3fa18: stur            x1, [fp, #-0x18]
    // 0xb3fa1c: r16 = <EdgeInsets>
    //     0xb3fa1c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb3fa20: ldr             x16, [x16, #0xda0]
    // 0xb3fa24: r30 = Instance_EdgeInsets
    //     0xb3fa24: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb3fa28: ldr             lr, [lr, #0x1f0]
    // 0xb3fa2c: stp             lr, x16, [SP]
    // 0xb3fa30: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb3fa30: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb3fa34: r0 = all()
    //     0xb3fa34: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb3fa38: ldur            x1, [fp, #-0x10]
    // 0xb3fa3c: stur            x0, [fp, #-0x38]
    // 0xb3fa40: r0 = of()
    //     0xb3fa40: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3fa44: LoadField: r1 = r0->field_5b
    //     0xb3fa44: ldur            w1, [x0, #0x5b]
    // 0xb3fa48: DecompressPointer r1
    //     0xb3fa48: add             x1, x1, HEAP, lsl #32
    // 0xb3fa4c: r16 = <Color>
    //     0xb3fa4c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb3fa50: ldr             x16, [x16, #0xf80]
    // 0xb3fa54: stp             x1, x16, [SP]
    // 0xb3fa58: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb3fa58: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb3fa5c: r0 = all()
    //     0xb3fa5c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb3fa60: mov             x2, x0
    // 0xb3fa64: ldur            x0, [fp, #-8]
    // 0xb3fa68: stur            x2, [fp, #-0x50]
    // 0xb3fa6c: LoadField: r1 = r0->field_4b
    //     0xb3fa6c: ldur            w1, [x0, #0x4b]
    // 0xb3fa70: DecompressPointer r1
    //     0xb3fa70: add             x1, x1, HEAP, lsl #32
    // 0xb3fa74: tbnz            w1, #4, #0xb3fa90
    // 0xb3fa78: ldur            x1, [fp, #-0x10]
    // 0xb3fa7c: r0 = of()
    //     0xb3fa7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3fa80: LoadField: r1 = r0->field_5b
    //     0xb3fa80: ldur            w1, [x0, #0x5b]
    // 0xb3fa84: DecompressPointer r1
    //     0xb3fa84: add             x1, x1, HEAP, lsl #32
    // 0xb3fa88: mov             x8, x1
    // 0xb3fa8c: b               #0xb3fabc
    // 0xb3fa90: ldur            x1, [fp, #-0x10]
    // 0xb3fa94: r0 = of()
    //     0xb3fa94: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3fa98: LoadField: r1 = r0->field_5b
    //     0xb3fa98: ldur            w1, [x0, #0x5b]
    // 0xb3fa9c: DecompressPointer r1
    //     0xb3fa9c: add             x1, x1, HEAP, lsl #32
    // 0xb3faa0: r0 = LoadClassIdInstr(r1)
    //     0xb3faa0: ldur            x0, [x1, #-1]
    //     0xb3faa4: ubfx            x0, x0, #0xc, #0x14
    // 0xb3faa8: d0 = 0.100000
    //     0xb3faa8: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb3faac: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb3faac: sub             lr, x0, #0xffa
    //     0xb3fab0: ldr             lr, [x21, lr, lsl #3]
    //     0xb3fab4: blr             lr
    // 0xb3fab8: mov             x8, x0
    // 0xb3fabc: ldur            x7, [fp, #-0x28]
    // 0xb3fac0: ldur            x6, [fp, #-0x20]
    // 0xb3fac4: ldur            x5, [fp, #-0x30]
    // 0xb3fac8: ldur            x4, [fp, #-0x40]
    // 0xb3facc: ldur            x3, [fp, #-0x48]
    // 0xb3fad0: ldur            x2, [fp, #-0x18]
    // 0xb3fad4: ldur            x1, [fp, #-0x38]
    // 0xb3fad8: ldur            x0, [fp, #-0x50]
    // 0xb3fadc: r16 = <Color>
    //     0xb3fadc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb3fae0: ldr             x16, [x16, #0xf80]
    // 0xb3fae4: stp             x8, x16, [SP]
    // 0xb3fae8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb3fae8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb3faec: r0 = all()
    //     0xb3faec: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb3faf0: stur            x0, [fp, #-8]
    // 0xb3faf4: r0 = Radius()
    //     0xb3faf4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb3faf8: d0 = 30.000000
    //     0xb3faf8: fmov            d0, #30.00000000
    // 0xb3fafc: stur            x0, [fp, #-0x58]
    // 0xb3fb00: StoreField: r0->field_7 = d0
    //     0xb3fb00: stur            d0, [x0, #7]
    // 0xb3fb04: StoreField: r0->field_f = d0
    //     0xb3fb04: stur            d0, [x0, #0xf]
    // 0xb3fb08: r0 = BorderRadius()
    //     0xb3fb08: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb3fb0c: mov             x1, x0
    // 0xb3fb10: ldur            x0, [fp, #-0x58]
    // 0xb3fb14: stur            x1, [fp, #-0x60]
    // 0xb3fb18: StoreField: r1->field_7 = r0
    //     0xb3fb18: stur            w0, [x1, #7]
    // 0xb3fb1c: StoreField: r1->field_b = r0
    //     0xb3fb1c: stur            w0, [x1, #0xb]
    // 0xb3fb20: StoreField: r1->field_f = r0
    //     0xb3fb20: stur            w0, [x1, #0xf]
    // 0xb3fb24: StoreField: r1->field_13 = r0
    //     0xb3fb24: stur            w0, [x1, #0x13]
    // 0xb3fb28: r0 = RoundedRectangleBorder()
    //     0xb3fb28: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb3fb2c: mov             x1, x0
    // 0xb3fb30: ldur            x0, [fp, #-0x60]
    // 0xb3fb34: StoreField: r1->field_b = r0
    //     0xb3fb34: stur            w0, [x1, #0xb]
    // 0xb3fb38: r0 = Instance_BorderSide
    //     0xb3fb38: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb3fb3c: ldr             x0, [x0, #0xe20]
    // 0xb3fb40: StoreField: r1->field_7 = r0
    //     0xb3fb40: stur            w0, [x1, #7]
    // 0xb3fb44: r16 = <RoundedRectangleBorder>
    //     0xb3fb44: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb3fb48: ldr             x16, [x16, #0xf78]
    // 0xb3fb4c: stp             x1, x16, [SP]
    // 0xb3fb50: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb3fb50: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb3fb54: r0 = all()
    //     0xb3fb54: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb3fb58: stur            x0, [fp, #-0x58]
    // 0xb3fb5c: r0 = ButtonStyle()
    //     0xb3fb5c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb3fb60: mov             x2, x0
    // 0xb3fb64: ldur            x0, [fp, #-8]
    // 0xb3fb68: stur            x2, [fp, #-0x60]
    // 0xb3fb6c: StoreField: r2->field_b = r0
    //     0xb3fb6c: stur            w0, [x2, #0xb]
    // 0xb3fb70: ldur            x0, [fp, #-0x50]
    // 0xb3fb74: StoreField: r2->field_f = r0
    //     0xb3fb74: stur            w0, [x2, #0xf]
    // 0xb3fb78: ldur            x0, [fp, #-0x38]
    // 0xb3fb7c: StoreField: r2->field_23 = r0
    //     0xb3fb7c: stur            w0, [x2, #0x23]
    // 0xb3fb80: ldur            x0, [fp, #-0x58]
    // 0xb3fb84: StoreField: r2->field_43 = r0
    //     0xb3fb84: stur            w0, [x2, #0x43]
    // 0xb3fb88: ldur            x1, [fp, #-0x10]
    // 0xb3fb8c: r0 = of()
    //     0xb3fb8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3fb90: LoadField: r1 = r0->field_87
    //     0xb3fb90: ldur            w1, [x0, #0x87]
    // 0xb3fb94: DecompressPointer r1
    //     0xb3fb94: add             x1, x1, HEAP, lsl #32
    // 0xb3fb98: LoadField: r0 = r1->field_7
    //     0xb3fb98: ldur            w0, [x1, #7]
    // 0xb3fb9c: DecompressPointer r0
    //     0xb3fb9c: add             x0, x0, HEAP, lsl #32
    // 0xb3fba0: r16 = 16.000000
    //     0xb3fba0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb3fba4: ldr             x16, [x16, #0x188]
    // 0xb3fba8: r30 = Instance_Color
    //     0xb3fba8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb3fbac: stp             lr, x16, [SP]
    // 0xb3fbb0: mov             x1, x0
    // 0xb3fbb4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3fbb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3fbb8: ldr             x4, [x4, #0xaa0]
    // 0xb3fbbc: r0 = copyWith()
    //     0xb3fbbc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3fbc0: stur            x0, [fp, #-8]
    // 0xb3fbc4: r0 = Text()
    //     0xb3fbc4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3fbc8: mov             x1, x0
    // 0xb3fbcc: r0 = "Confirm"
    //     0xb3fbcc: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3cec0] "Confirm"
    //     0xb3fbd0: ldr             x0, [x0, #0xec0]
    // 0xb3fbd4: stur            x1, [fp, #-0x10]
    // 0xb3fbd8: StoreField: r1->field_b = r0
    //     0xb3fbd8: stur            w0, [x1, #0xb]
    // 0xb3fbdc: ldur            x0, [fp, #-8]
    // 0xb3fbe0: StoreField: r1->field_13 = r0
    //     0xb3fbe0: stur            w0, [x1, #0x13]
    // 0xb3fbe4: r0 = TextButton()
    //     0xb3fbe4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb3fbe8: mov             x1, x0
    // 0xb3fbec: ldur            x0, [fp, #-0x18]
    // 0xb3fbf0: stur            x1, [fp, #-8]
    // 0xb3fbf4: StoreField: r1->field_b = r0
    //     0xb3fbf4: stur            w0, [x1, #0xb]
    // 0xb3fbf8: ldur            x0, [fp, #-0x60]
    // 0xb3fbfc: StoreField: r1->field_1b = r0
    //     0xb3fbfc: stur            w0, [x1, #0x1b]
    // 0xb3fc00: r0 = false
    //     0xb3fc00: add             x0, NULL, #0x30  ; false
    // 0xb3fc04: StoreField: r1->field_27 = r0
    //     0xb3fc04: stur            w0, [x1, #0x27]
    // 0xb3fc08: r2 = true
    //     0xb3fc08: add             x2, NULL, #0x20  ; true
    // 0xb3fc0c: StoreField: r1->field_2f = r2
    //     0xb3fc0c: stur            w2, [x1, #0x2f]
    // 0xb3fc10: ldur            x2, [fp, #-0x10]
    // 0xb3fc14: StoreField: r1->field_37 = r2
    //     0xb3fc14: stur            w2, [x1, #0x37]
    // 0xb3fc18: r0 = SizedBox()
    //     0xb3fc18: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb3fc1c: mov             x1, x0
    // 0xb3fc20: r0 = inf
    //     0xb3fc20: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb3fc24: ldr             x0, [x0, #0x9f8]
    // 0xb3fc28: stur            x1, [fp, #-0x10]
    // 0xb3fc2c: StoreField: r1->field_f = r0
    //     0xb3fc2c: stur            w0, [x1, #0xf]
    // 0xb3fc30: ldur            x0, [fp, #-8]
    // 0xb3fc34: StoreField: r1->field_b = r0
    //     0xb3fc34: stur            w0, [x1, #0xb]
    // 0xb3fc38: r0 = Padding()
    //     0xb3fc38: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3fc3c: mov             x3, x0
    // 0xb3fc40: r0 = Instance_EdgeInsets
    //     0xb3fc40: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb3fc44: ldr             x0, [x0, #0x668]
    // 0xb3fc48: stur            x3, [fp, #-8]
    // 0xb3fc4c: StoreField: r3->field_f = r0
    //     0xb3fc4c: stur            w0, [x3, #0xf]
    // 0xb3fc50: ldur            x0, [fp, #-0x10]
    // 0xb3fc54: StoreField: r3->field_b = r0
    //     0xb3fc54: stur            w0, [x3, #0xb]
    // 0xb3fc58: r1 = Null
    //     0xb3fc58: mov             x1, NULL
    // 0xb3fc5c: r2 = 12
    //     0xb3fc5c: movz            x2, #0xc
    // 0xb3fc60: r0 = AllocateArray()
    //     0xb3fc60: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3fc64: mov             x2, x0
    // 0xb3fc68: ldur            x0, [fp, #-0x28]
    // 0xb3fc6c: stur            x2, [fp, #-0x10]
    // 0xb3fc70: StoreField: r2->field_f = r0
    //     0xb3fc70: stur            w0, [x2, #0xf]
    // 0xb3fc74: ldur            x0, [fp, #-0x20]
    // 0xb3fc78: StoreField: r2->field_13 = r0
    //     0xb3fc78: stur            w0, [x2, #0x13]
    // 0xb3fc7c: ldur            x0, [fp, #-0x30]
    // 0xb3fc80: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3fc80: stur            w0, [x2, #0x17]
    // 0xb3fc84: ldur            x0, [fp, #-0x40]
    // 0xb3fc88: StoreField: r2->field_1b = r0
    //     0xb3fc88: stur            w0, [x2, #0x1b]
    // 0xb3fc8c: ldur            x0, [fp, #-0x48]
    // 0xb3fc90: StoreField: r2->field_1f = r0
    //     0xb3fc90: stur            w0, [x2, #0x1f]
    // 0xb3fc94: ldur            x0, [fp, #-8]
    // 0xb3fc98: StoreField: r2->field_23 = r0
    //     0xb3fc98: stur            w0, [x2, #0x23]
    // 0xb3fc9c: r1 = <Widget>
    //     0xb3fc9c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3fca0: r0 = AllocateGrowableArray()
    //     0xb3fca0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3fca4: mov             x1, x0
    // 0xb3fca8: ldur            x0, [fp, #-0x10]
    // 0xb3fcac: stur            x1, [fp, #-8]
    // 0xb3fcb0: StoreField: r1->field_f = r0
    //     0xb3fcb0: stur            w0, [x1, #0xf]
    // 0xb3fcb4: r0 = 12
    //     0xb3fcb4: movz            x0, #0xc
    // 0xb3fcb8: StoreField: r1->field_b = r0
    //     0xb3fcb8: stur            w0, [x1, #0xb]
    // 0xb3fcbc: r0 = Wrap()
    //     0xb3fcbc: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0xb3fcc0: mov             x1, x0
    // 0xb3fcc4: r0 = Instance_Axis
    //     0xb3fcc4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb3fcc8: stur            x1, [fp, #-0x10]
    // 0xb3fccc: StoreField: r1->field_f = r0
    //     0xb3fccc: stur            w0, [x1, #0xf]
    // 0xb3fcd0: r0 = Instance_WrapAlignment
    //     0xb3fcd0: add             x0, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0xb3fcd4: ldr             x0, [x0, #0x6e8]
    // 0xb3fcd8: StoreField: r1->field_13 = r0
    //     0xb3fcd8: stur            w0, [x1, #0x13]
    // 0xb3fcdc: ArrayStore: r1[0] = rZR  ; List_8
    //     0xb3fcdc: stur            xzr, [x1, #0x17]
    // 0xb3fce0: StoreField: r1->field_1f = r0
    //     0xb3fce0: stur            w0, [x1, #0x1f]
    // 0xb3fce4: StoreField: r1->field_23 = rZR
    //     0xb3fce4: stur            xzr, [x1, #0x23]
    // 0xb3fce8: r0 = Instance_WrapCrossAlignment
    //     0xb3fce8: add             x0, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0xb3fcec: ldr             x0, [x0, #0x6f0]
    // 0xb3fcf0: StoreField: r1->field_2b = r0
    //     0xb3fcf0: stur            w0, [x1, #0x2b]
    // 0xb3fcf4: r0 = Instance_VerticalDirection
    //     0xb3fcf4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3fcf8: ldr             x0, [x0, #0xa20]
    // 0xb3fcfc: StoreField: r1->field_33 = r0
    //     0xb3fcfc: stur            w0, [x1, #0x33]
    // 0xb3fd00: r0 = Instance_Clip
    //     0xb3fd00: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3fd04: ldr             x0, [x0, #0x38]
    // 0xb3fd08: StoreField: r1->field_37 = r0
    //     0xb3fd08: stur            w0, [x1, #0x37]
    // 0xb3fd0c: ldur            x0, [fp, #-8]
    // 0xb3fd10: StoreField: r1->field_b = r0
    //     0xb3fd10: stur            w0, [x1, #0xb]
    // 0xb3fd14: r0 = SingleChildScrollView()
    //     0xb3fd14: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xb3fd18: mov             x1, x0
    // 0xb3fd1c: r0 = Instance_Axis
    //     0xb3fd1c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3fd20: stur            x1, [fp, #-8]
    // 0xb3fd24: StoreField: r1->field_b = r0
    //     0xb3fd24: stur            w0, [x1, #0xb]
    // 0xb3fd28: r0 = false
    //     0xb3fd28: add             x0, NULL, #0x30  ; false
    // 0xb3fd2c: StoreField: r1->field_f = r0
    //     0xb3fd2c: stur            w0, [x1, #0xf]
    // 0xb3fd30: ldur            x0, [fp, #-0x10]
    // 0xb3fd34: StoreField: r1->field_23 = r0
    //     0xb3fd34: stur            w0, [x1, #0x23]
    // 0xb3fd38: r0 = Instance_DragStartBehavior
    //     0xb3fd38: ldr             x0, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xb3fd3c: StoreField: r1->field_27 = r0
    //     0xb3fd3c: stur            w0, [x1, #0x27]
    // 0xb3fd40: r0 = Instance_Clip
    //     0xb3fd40: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb3fd44: ldr             x0, [x0, #0x7e0]
    // 0xb3fd48: StoreField: r1->field_2b = r0
    //     0xb3fd48: stur            w0, [x1, #0x2b]
    // 0xb3fd4c: r0 = Instance_HitTestBehavior
    //     0xb3fd4c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xb3fd50: ldr             x0, [x0, #0x288]
    // 0xb3fd54: StoreField: r1->field_2f = r0
    //     0xb3fd54: stur            w0, [x1, #0x2f]
    // 0xb3fd58: r0 = Padding()
    //     0xb3fd58: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3fd5c: r1 = Instance_EdgeInsets
    //     0xb3fd5c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb3fd60: ldr             x1, [x1, #0xa00]
    // 0xb3fd64: StoreField: r0->field_f = r1
    //     0xb3fd64: stur            w1, [x0, #0xf]
    // 0xb3fd68: ldur            x1, [fp, #-8]
    // 0xb3fd6c: StoreField: r0->field_b = r1
    //     0xb3fd6c: stur            w1, [x0, #0xb]
    // 0xb3fd70: LeaveFrame
    //     0xb3fd70: mov             SP, fp
    //     0xb3fd74: ldp             fp, lr, [SP], #0x10
    // 0xb3fd78: ret
    //     0xb3fd78: ret             
    // 0xb3fd7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3fd7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3fd80: b               #0xb3cab4
    // 0xb3fd84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3fd84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3fd88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3fd88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3fd8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3fd8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3fd90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3fd90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3fd94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3fd94: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3fd98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3fd98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3fd9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3fd9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3fda0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3fda0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3fda4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3fda4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3fda8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3fda8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3fdac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3fdac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb40158, size: 0x188
    // 0xb40158: EnterFrame
    //     0xb40158: stp             fp, lr, [SP, #-0x10]!
    //     0xb4015c: mov             fp, SP
    // 0xb40160: AllocStack(0x40)
    //     0xb40160: sub             SP, SP, #0x40
    // 0xb40164: SetupParameters()
    //     0xb40164: ldr             x0, [fp, #0x10]
    //     0xb40168: ldur            w1, [x0, #0x17]
    //     0xb4016c: add             x1, x1, HEAP, lsl #32
    //     0xb40170: stur            x1, [fp, #-8]
    // 0xb40174: CheckStackOverflow
    //     0xb40174: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40178: cmp             SP, x16
    //     0xb4017c: b.ls            #0xb402d0
    // 0xb40180: LoadField: r0 = r1->field_f
    //     0xb40180: ldur            w0, [x1, #0xf]
    // 0xb40184: DecompressPointer r0
    //     0xb40184: add             x0, x0, HEAP, lsl #32
    // 0xb40188: LoadField: r2 = r0->field_b
    //     0xb40188: ldur            w2, [x0, #0xb]
    // 0xb4018c: DecompressPointer r2
    //     0xb4018c: add             x2, x2, HEAP, lsl #32
    // 0xb40190: cmp             w2, NULL
    // 0xb40194: b.eq            #0xb402d8
    // 0xb40198: LoadField: r3 = r0->field_33
    //     0xb40198: ldur            w3, [x0, #0x33]
    // 0xb4019c: DecompressPointer r3
    //     0xb4019c: add             x3, x3, HEAP, lsl #32
    // 0xb401a0: LoadField: r4 = r3->field_27
    //     0xb401a0: ldur            w4, [x3, #0x27]
    // 0xb401a4: DecompressPointer r4
    //     0xb401a4: add             x4, x4, HEAP, lsl #32
    // 0xb401a8: LoadField: r3 = r4->field_7
    //     0xb401a8: ldur            w3, [x4, #7]
    // 0xb401ac: DecompressPointer r3
    //     0xb401ac: add             x3, x3, HEAP, lsl #32
    // 0xb401b0: LoadField: r4 = r0->field_3f
    //     0xb401b0: ldur            w4, [x0, #0x3f]
    // 0xb401b4: DecompressPointer r4
    //     0xb401b4: add             x4, x4, HEAP, lsl #32
    // 0xb401b8: LoadField: r5 = r4->field_27
    //     0xb401b8: ldur            w5, [x4, #0x27]
    // 0xb401bc: DecompressPointer r5
    //     0xb401bc: add             x5, x5, HEAP, lsl #32
    // 0xb401c0: LoadField: r4 = r5->field_7
    //     0xb401c0: ldur            w4, [x5, #7]
    // 0xb401c4: DecompressPointer r4
    //     0xb401c4: add             x4, x4, HEAP, lsl #32
    // 0xb401c8: LoadField: r5 = r0->field_3b
    //     0xb401c8: ldur            w5, [x0, #0x3b]
    // 0xb401cc: DecompressPointer r5
    //     0xb401cc: add             x5, x5, HEAP, lsl #32
    // 0xb401d0: LoadField: r6 = r5->field_27
    //     0xb401d0: ldur            w6, [x5, #0x27]
    // 0xb401d4: DecompressPointer r6
    //     0xb401d4: add             x6, x6, HEAP, lsl #32
    // 0xb401d8: LoadField: r5 = r6->field_7
    //     0xb401d8: ldur            w5, [x6, #7]
    // 0xb401dc: DecompressPointer r5
    //     0xb401dc: add             x5, x5, HEAP, lsl #32
    // 0xb401e0: LoadField: r6 = r0->field_47
    //     0xb401e0: ldur            w6, [x0, #0x47]
    // 0xb401e4: DecompressPointer r6
    //     0xb401e4: add             x6, x6, HEAP, lsl #32
    // 0xb401e8: LoadField: r7 = r6->field_27
    //     0xb401e8: ldur            w7, [x6, #0x27]
    // 0xb401ec: DecompressPointer r7
    //     0xb401ec: add             x7, x7, HEAP, lsl #32
    // 0xb401f0: LoadField: r6 = r7->field_7
    //     0xb401f0: ldur            w6, [x7, #7]
    // 0xb401f4: DecompressPointer r6
    //     0xb401f4: add             x6, x6, HEAP, lsl #32
    // 0xb401f8: LoadField: r7 = r0->field_37
    //     0xb401f8: ldur            w7, [x0, #0x37]
    // 0xb401fc: DecompressPointer r7
    //     0xb401fc: add             x7, x7, HEAP, lsl #32
    // 0xb40200: LoadField: r8 = r7->field_27
    //     0xb40200: ldur            w8, [x7, #0x27]
    // 0xb40204: DecompressPointer r8
    //     0xb40204: add             x8, x8, HEAP, lsl #32
    // 0xb40208: LoadField: r7 = r8->field_7
    //     0xb40208: ldur            w7, [x8, #7]
    // 0xb4020c: DecompressPointer r7
    //     0xb4020c: add             x7, x7, HEAP, lsl #32
    // 0xb40210: LoadField: r8 = r0->field_43
    //     0xb40210: ldur            w8, [x0, #0x43]
    // 0xb40214: DecompressPointer r8
    //     0xb40214: add             x8, x8, HEAP, lsl #32
    // 0xb40218: LoadField: r0 = r8->field_27
    //     0xb40218: ldur            w0, [x8, #0x27]
    // 0xb4021c: DecompressPointer r0
    //     0xb4021c: add             x0, x0, HEAP, lsl #32
    // 0xb40220: LoadField: r8 = r0->field_7
    //     0xb40220: ldur            w8, [x0, #7]
    // 0xb40224: DecompressPointer r8
    //     0xb40224: add             x8, x8, HEAP, lsl #32
    // 0xb40228: LoadField: r0 = r2->field_f
    //     0xb40228: ldur            w0, [x2, #0xf]
    // 0xb4022c: DecompressPointer r0
    //     0xb4022c: add             x0, x0, HEAP, lsl #32
    // 0xb40230: stp             x3, x0, [SP, #0x28]
    // 0xb40234: stp             x5, x4, [SP, #0x18]
    // 0xb40238: stp             x7, x6, [SP, #8]
    // 0xb4023c: str             x8, [SP]
    // 0xb40240: r4 = 0
    //     0xb40240: movz            x4, #0
    // 0xb40244: ldr             x0, [SP, #0x30]
    // 0xb40248: r16 = UnlinkedCall_0x613b5c
    //     0xb40248: add             x16, PP, #0x56, lsl #12  ; [pp+0x56f58] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb4024c: add             x16, x16, #0xf58
    // 0xb40250: ldp             x5, lr, [x16]
    // 0xb40254: blr             lr
    // 0xb40258: ldur            x0, [fp, #-8]
    // 0xb4025c: LoadField: r1 = r0->field_f
    //     0xb4025c: ldur            w1, [x0, #0xf]
    // 0xb40260: DecompressPointer r1
    //     0xb40260: add             x1, x1, HEAP, lsl #32
    // 0xb40264: LoadField: r0 = r1->field_b
    //     0xb40264: ldur            w0, [x1, #0xb]
    // 0xb40268: DecompressPointer r0
    //     0xb40268: add             x0, x0, HEAP, lsl #32
    // 0xb4026c: cmp             w0, NULL
    // 0xb40270: b.eq            #0xb402dc
    // 0xb40274: LoadField: r1 = r0->field_23
    //     0xb40274: ldur            w1, [x0, #0x23]
    // 0xb40278: DecompressPointer r1
    //     0xb40278: add             x1, x1, HEAP, lsl #32
    // 0xb4027c: str             x1, [SP]
    // 0xb40280: r4 = 0
    //     0xb40280: movz            x4, #0
    // 0xb40284: ldr             x0, [SP]
    // 0xb40288: r16 = UnlinkedCall_0x613b5c
    //     0xb40288: add             x16, PP, #0x56, lsl #12  ; [pp+0x56f68] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb4028c: add             x16, x16, #0xf68
    // 0xb40290: ldp             x5, lr, [x16]
    // 0xb40294: blr             lr
    // 0xb40298: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb40298: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb4029c: ldr             x0, [x0, #0x1c80]
    //     0xb402a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb402a4: cmp             w0, w16
    //     0xb402a8: b.ne            #0xb402b4
    //     0xb402ac: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb402b0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb402b4: str             NULL, [SP]
    // 0xb402b8: r4 = const [0x1, 0, 0, 0, null]
    //     0xb402b8: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xb402bc: r0 = GetNavigation.back()
    //     0xb402bc: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xb402c0: r0 = Null
    //     0xb402c0: mov             x0, NULL
    // 0xb402c4: LeaveFrame
    //     0xb402c4: mov             SP, fp
    //     0xb402c8: ldp             fp, lr, [SP], #0x10
    // 0xb402cc: ret
    //     0xb402cc: ret             
    // 0xb402d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb402d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb402d4: b               #0xb40180
    // 0xb402d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb402d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb402dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb402dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb402e0, size: 0x78
    // 0xb402e0: EnterFrame
    //     0xb402e0: stp             fp, lr, [SP, #-0x10]!
    //     0xb402e4: mov             fp, SP
    // 0xb402e8: AllocStack(0x10)
    //     0xb402e8: sub             SP, SP, #0x10
    // 0xb402ec: SetupParameters()
    //     0xb402ec: ldr             x0, [fp, #0x18]
    //     0xb402f0: ldur            w3, [x0, #0x17]
    //     0xb402f4: add             x3, x3, HEAP, lsl #32
    //     0xb402f8: stur            x3, [fp, #-0x10]
    // 0xb402fc: CheckStackOverflow
    //     0xb402fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40300: cmp             SP, x16
    //     0xb40304: b.ls            #0xb40350
    // 0xb40308: LoadField: r0 = r3->field_f
    //     0xb40308: ldur            w0, [x3, #0xf]
    // 0xb4030c: DecompressPointer r0
    //     0xb4030c: add             x0, x0, HEAP, lsl #32
    // 0xb40310: mov             x2, x3
    // 0xb40314: stur            x0, [fp, #-8]
    // 0xb40318: r1 = Function '<anonymous closure>':.
    //     0xb40318: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f78] AnonymousClosure: (0xa01a18), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xb4031c: ldr             x1, [x1, #0xf78]
    // 0xb40320: r0 = AllocateClosure()
    //     0xb40320: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb40324: ldur            x1, [fp, #-8]
    // 0xb40328: mov             x2, x0
    // 0xb4032c: r0 = setState()
    //     0xb4032c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb40330: ldur            x0, [fp, #-0x10]
    // 0xb40334: LoadField: r1 = r0->field_f
    //     0xb40334: ldur            w1, [x0, #0xf]
    // 0xb40338: DecompressPointer r1
    //     0xb40338: add             x1, x1, HEAP, lsl #32
    // 0xb4033c: r0 = validateAddress()
    //     0xb4033c: bl              #0xb40358  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xb40340: r0 = Null
    //     0xb40340: mov             x0, NULL
    // 0xb40344: LeaveFrame
    //     0xb40344: mov             SP, fp
    //     0xb40348: ldp             fp, lr, [SP], #0x10
    // 0xb4034c: ret
    //     0xb4034c: ret             
    // 0xb40350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40350: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40354: b               #0xb40308
  }
  _ validateAddress(/* No info */) {
    // ** addr: 0xb40358, size: 0x130
    // 0xb40358: EnterFrame
    //     0xb40358: stp             fp, lr, [SP, #-0x10]!
    //     0xb4035c: mov             fp, SP
    // 0xb40360: AllocStack(0x18)
    //     0xb40360: sub             SP, SP, #0x18
    // 0xb40364: SetupParameters(_ChangeAddressBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0xb40364: stur            x1, [fp, #-8]
    // 0xb40368: CheckStackOverflow
    //     0xb40368: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4036c: cmp             SP, x16
    //     0xb40370: b.ls            #0xb4047c
    // 0xb40374: r1 = 1
    //     0xb40374: movz            x1, #0x1
    // 0xb40378: r0 = AllocateContext()
    //     0xb40378: bl              #0x16f6108  ; AllocateContextStub
    // 0xb4037c: mov             x1, x0
    // 0xb40380: ldur            x0, [fp, #-8]
    // 0xb40384: StoreField: r1->field_f = r0
    //     0xb40384: stur            w0, [x1, #0xf]
    // 0xb40388: r0 = LoadStaticField(0x878)
    //     0xb40388: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb4038c: ldr             x0, [x0, #0x10f0]
    // 0xb40390: cmp             w0, NULL
    // 0xb40394: b.eq            #0xb40484
    // 0xb40398: LoadField: r3 = r0->field_53
    //     0xb40398: ldur            w3, [x0, #0x53]
    // 0xb4039c: DecompressPointer r3
    //     0xb4039c: add             x3, x3, HEAP, lsl #32
    // 0xb403a0: stur            x3, [fp, #-0x10]
    // 0xb403a4: LoadField: r0 = r3->field_7
    //     0xb403a4: ldur            w0, [x3, #7]
    // 0xb403a8: DecompressPointer r0
    //     0xb403a8: add             x0, x0, HEAP, lsl #32
    // 0xb403ac: mov             x2, x1
    // 0xb403b0: stur            x0, [fp, #-8]
    // 0xb403b4: r1 = Function '<anonymous closure>':.
    //     0xb403b4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f80] AnonymousClosure: (0xb40488), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress (0xb40358)
    //     0xb403b8: ldr             x1, [x1, #0xf80]
    // 0xb403bc: r0 = AllocateClosure()
    //     0xb403bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb403c0: ldur            x2, [fp, #-8]
    // 0xb403c4: mov             x3, x0
    // 0xb403c8: r1 = Null
    //     0xb403c8: mov             x1, NULL
    // 0xb403cc: stur            x3, [fp, #-8]
    // 0xb403d0: cmp             w2, NULL
    // 0xb403d4: b.eq            #0xb403f4
    // 0xb403d8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb403d8: ldur            w4, [x2, #0x17]
    // 0xb403dc: DecompressPointer r4
    //     0xb403dc: add             x4, x4, HEAP, lsl #32
    // 0xb403e0: r8 = X0
    //     0xb403e0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0xb403e4: LoadField: r9 = r4->field_7
    //     0xb403e4: ldur            x9, [x4, #7]
    // 0xb403e8: r3 = Null
    //     0xb403e8: add             x3, PP, #0x56, lsl #12  ; [pp+0x56f88] Null
    //     0xb403ec: ldr             x3, [x3, #0xf88]
    // 0xb403f0: blr             x9
    // 0xb403f4: ldur            x0, [fp, #-0x10]
    // 0xb403f8: LoadField: r1 = r0->field_b
    //     0xb403f8: ldur            w1, [x0, #0xb]
    // 0xb403fc: LoadField: r2 = r0->field_f
    //     0xb403fc: ldur            w2, [x0, #0xf]
    // 0xb40400: DecompressPointer r2
    //     0xb40400: add             x2, x2, HEAP, lsl #32
    // 0xb40404: LoadField: r3 = r2->field_b
    //     0xb40404: ldur            w3, [x2, #0xb]
    // 0xb40408: r2 = LoadInt32Instr(r1)
    //     0xb40408: sbfx            x2, x1, #1, #0x1f
    // 0xb4040c: stur            x2, [fp, #-0x18]
    // 0xb40410: r1 = LoadInt32Instr(r3)
    //     0xb40410: sbfx            x1, x3, #1, #0x1f
    // 0xb40414: cmp             x2, x1
    // 0xb40418: b.ne            #0xb40424
    // 0xb4041c: mov             x1, x0
    // 0xb40420: r0 = _growToNextCapacity()
    //     0xb40420: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb40424: ldur            x2, [fp, #-0x10]
    // 0xb40428: ldur            x3, [fp, #-0x18]
    // 0xb4042c: add             x4, x3, #1
    // 0xb40430: lsl             x5, x4, #1
    // 0xb40434: StoreField: r2->field_b = r5
    //     0xb40434: stur            w5, [x2, #0xb]
    // 0xb40438: LoadField: r1 = r2->field_f
    //     0xb40438: ldur            w1, [x2, #0xf]
    // 0xb4043c: DecompressPointer r1
    //     0xb4043c: add             x1, x1, HEAP, lsl #32
    // 0xb40440: ldur            x0, [fp, #-8]
    // 0xb40444: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb40444: add             x25, x1, x3, lsl #2
    //     0xb40448: add             x25, x25, #0xf
    //     0xb4044c: str             w0, [x25]
    //     0xb40450: tbz             w0, #0, #0xb4046c
    //     0xb40454: ldurb           w16, [x1, #-1]
    //     0xb40458: ldurb           w17, [x0, #-1]
    //     0xb4045c: and             x16, x17, x16, lsr #2
    //     0xb40460: tst             x16, HEAP, lsr #32
    //     0xb40464: b.eq            #0xb4046c
    //     0xb40468: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb4046c: r0 = Null
    //     0xb4046c: mov             x0, NULL
    // 0xb40470: LeaveFrame
    //     0xb40470: mov             SP, fp
    //     0xb40474: ldp             fp, lr, [SP], #0x10
    // 0xb40478: ret
    //     0xb40478: ret             
    // 0xb4047c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4047c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40480: b               #0xb40374
    // 0xb40484: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb40484: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0xb40488, size: 0x60
    // 0xb40488: EnterFrame
    //     0xb40488: stp             fp, lr, [SP, #-0x10]!
    //     0xb4048c: mov             fp, SP
    // 0xb40490: AllocStack(0x8)
    //     0xb40490: sub             SP, SP, #8
    // 0xb40494: SetupParameters()
    //     0xb40494: ldr             x0, [fp, #0x18]
    //     0xb40498: ldur            w2, [x0, #0x17]
    //     0xb4049c: add             x2, x2, HEAP, lsl #32
    // 0xb404a0: CheckStackOverflow
    //     0xb404a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb404a4: cmp             SP, x16
    //     0xb404a8: b.ls            #0xb404e0
    // 0xb404ac: LoadField: r0 = r2->field_f
    //     0xb404ac: ldur            w0, [x2, #0xf]
    // 0xb404b0: DecompressPointer r0
    //     0xb404b0: add             x0, x0, HEAP, lsl #32
    // 0xb404b4: stur            x0, [fp, #-8]
    // 0xb404b8: r1 = Function '<anonymous closure>':.
    //     0xb404b8: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f98] AnonymousClosure: (0xa015e0), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress (0xa018e8)
    //     0xb404bc: ldr             x1, [x1, #0xf98]
    // 0xb404c0: r0 = AllocateClosure()
    //     0xb404c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb404c4: ldur            x1, [fp, #-8]
    // 0xb404c8: mov             x2, x0
    // 0xb404cc: r0 = setState()
    //     0xb404cc: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb404d0: r0 = Null
    //     0xb404d0: mov             x0, NULL
    // 0xb404d4: LeaveFrame
    //     0xb404d4: mov             SP, fp
    //     0xb404d8: ldp             fp, lr, [SP], #0x10
    // 0xb404dc: ret
    //     0xb404dc: ret             
    // 0xb404e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb404e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb404e4: b               #0xb404ac
  }
  [closure] String? _validateAlternateNo(dynamic, String?) {
    // ** addr: 0xb404e8, size: 0x3c
    // 0xb404e8: EnterFrame
    //     0xb404e8: stp             fp, lr, [SP, #-0x10]!
    //     0xb404ec: mov             fp, SP
    // 0xb404f0: ldr             x0, [fp, #0x18]
    // 0xb404f4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb404f4: ldur            w1, [x0, #0x17]
    // 0xb404f8: DecompressPointer r1
    //     0xb404f8: add             x1, x1, HEAP, lsl #32
    // 0xb404fc: CheckStackOverflow
    //     0xb404fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40500: cmp             SP, x16
    //     0xb40504: b.ls            #0xb4051c
    // 0xb40508: ldr             x2, [fp, #0x10]
    // 0xb4050c: r0 = _validateAlternateNo()
    //     0xb4050c: bl              #0xa01af0  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateAlternateNo
    // 0xb40510: LeaveFrame
    //     0xb40510: mov             SP, fp
    //     0xb40514: ldp             fp, lr, [SP], #0x10
    // 0xb40518: ret
    //     0xb40518: ret             
    // 0xb4051c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4051c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40520: b               #0xb40508
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb40524, size: 0x78
    // 0xb40524: EnterFrame
    //     0xb40524: stp             fp, lr, [SP, #-0x10]!
    //     0xb40528: mov             fp, SP
    // 0xb4052c: AllocStack(0x10)
    //     0xb4052c: sub             SP, SP, #0x10
    // 0xb40530: SetupParameters()
    //     0xb40530: ldr             x0, [fp, #0x18]
    //     0xb40534: ldur            w3, [x0, #0x17]
    //     0xb40538: add             x3, x3, HEAP, lsl #32
    //     0xb4053c: stur            x3, [fp, #-0x10]
    // 0xb40540: CheckStackOverflow
    //     0xb40540: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40544: cmp             SP, x16
    //     0xb40548: b.ls            #0xb40594
    // 0xb4054c: LoadField: r0 = r3->field_f
    //     0xb4054c: ldur            w0, [x3, #0xf]
    // 0xb40550: DecompressPointer r0
    //     0xb40550: add             x0, x0, HEAP, lsl #32
    // 0xb40554: mov             x2, x3
    // 0xb40558: stur            x0, [fp, #-8]
    // 0xb4055c: r1 = Function '<anonymous closure>':.
    //     0xb4055c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56fa0] AnonymousClosure: (0xa01cc4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xb40560: ldr             x1, [x1, #0xfa0]
    // 0xb40564: r0 = AllocateClosure()
    //     0xb40564: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb40568: ldur            x1, [fp, #-8]
    // 0xb4056c: mov             x2, x0
    // 0xb40570: r0 = setState()
    //     0xb40570: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb40574: ldur            x0, [fp, #-0x10]
    // 0xb40578: LoadField: r1 = r0->field_f
    //     0xb40578: ldur            w1, [x0, #0xf]
    // 0xb4057c: DecompressPointer r1
    //     0xb4057c: add             x1, x1, HEAP, lsl #32
    // 0xb40580: r0 = validateAddress()
    //     0xb40580: bl              #0xb40358  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xb40584: r0 = Null
    //     0xb40584: mov             x0, NULL
    // 0xb40588: LeaveFrame
    //     0xb40588: mov             SP, fp
    //     0xb4058c: ldp             fp, lr, [SP], #0x10
    // 0xb40590: ret
    //     0xb40590: ret             
    // 0xb40594: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40594: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40598: b               #0xb4054c
  }
  [closure] String? _validateLandmark(dynamic, String?) {
    // ** addr: 0xb4059c, size: 0x3c
    // 0xb4059c: EnterFrame
    //     0xb4059c: stp             fp, lr, [SP, #-0x10]!
    //     0xb405a0: mov             fp, SP
    // 0xb405a4: ldr             x0, [fp, #0x18]
    // 0xb405a8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb405a8: ldur            w1, [x0, #0x17]
    // 0xb405ac: DecompressPointer r1
    //     0xb405ac: add             x1, x1, HEAP, lsl #32
    // 0xb405b0: CheckStackOverflow
    //     0xb405b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb405b4: cmp             SP, x16
    //     0xb405b8: b.ls            #0xb405d0
    // 0xb405bc: ldr             x2, [fp, #0x10]
    // 0xb405c0: r0 = _validateLandmark()
    //     0xb405c0: bl              #0xa01e20  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateLandmark
    // 0xb405c4: LeaveFrame
    //     0xb405c4: mov             SP, fp
    //     0xb405c8: ldp             fp, lr, [SP], #0x10
    // 0xb405cc: ret
    //     0xb405cc: ret             
    // 0xb405d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb405d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb405d4: b               #0xb405bc
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb405d8, size: 0x15c
    // 0xb405d8: EnterFrame
    //     0xb405d8: stp             fp, lr, [SP, #-0x10]!
    //     0xb405dc: mov             fp, SP
    // 0xb405e0: AllocStack(0x20)
    //     0xb405e0: sub             SP, SP, #0x20
    // 0xb405e4: SetupParameters()
    //     0xb405e4: ldr             x0, [fp, #0x18]
    //     0xb405e8: ldur            w3, [x0, #0x17]
    //     0xb405ec: add             x3, x3, HEAP, lsl #32
    //     0xb405f0: stur            x3, [fp, #-0x10]
    // 0xb405f4: CheckStackOverflow
    //     0xb405f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb405f8: cmp             SP, x16
    //     0xb405fc: b.ls            #0xb40724
    // 0xb40600: LoadField: r0 = r3->field_f
    //     0xb40600: ldur            w0, [x3, #0xf]
    // 0xb40604: DecompressPointer r0
    //     0xb40604: add             x0, x0, HEAP, lsl #32
    // 0xb40608: mov             x2, x3
    // 0xb4060c: stur            x0, [fp, #-8]
    // 0xb40610: r1 = Function '<anonymous closure>':.
    //     0xb40610: add             x1, PP, #0x56, lsl #12  ; [pp+0x56fa8] AnonymousClosure: (0xa0202c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xb40614: ldr             x1, [x1, #0xfa8]
    // 0xb40618: r0 = AllocateClosure()
    //     0xb40618: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4061c: ldur            x1, [fp, #-8]
    // 0xb40620: mov             x2, x0
    // 0xb40624: r0 = setState()
    //     0xb40624: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb40628: ldr             x0, [fp, #0x10]
    // 0xb4062c: cmp             w0, NULL
    // 0xb40630: b.ne            #0xb4063c
    // 0xb40634: r1 = Null
    //     0xb40634: mov             x1, NULL
    // 0xb40638: b               #0xb40640
    // 0xb4063c: LoadField: r1 = r0->field_7
    //     0xb4063c: ldur            w1, [x0, #7]
    // 0xb40640: cmp             w1, NULL
    // 0xb40644: b.ne            #0xb40650
    // 0xb40648: r1 = 0
    //     0xb40648: movz            x1, #0
    // 0xb4064c: b               #0xb40658
    // 0xb40650: r2 = LoadInt32Instr(r1)
    //     0xb40650: sbfx            x2, x1, #1, #0x1f
    // 0xb40654: mov             x1, x2
    // 0xb40658: cmp             x1, #5
    // 0xb4065c: b.le            #0xb40704
    // 0xb40660: ldur            x1, [fp, #-0x10]
    // 0xb40664: LoadField: r2 = r1->field_f
    //     0xb40664: ldur            w2, [x1, #0xf]
    // 0xb40668: DecompressPointer r2
    //     0xb40668: add             x2, x2, HEAP, lsl #32
    // 0xb4066c: LoadField: r3 = r2->field_b
    //     0xb4066c: ldur            w3, [x2, #0xb]
    // 0xb40670: DecompressPointer r3
    //     0xb40670: add             x3, x3, HEAP, lsl #32
    // 0xb40674: cmp             w3, NULL
    // 0xb40678: b.eq            #0xb4072c
    // 0xb4067c: cmp             w0, NULL
    // 0xb40680: b.ne            #0xb4068c
    // 0xb40684: r2 = ""
    //     0xb40684: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb40688: b               #0xb40690
    // 0xb4068c: mov             x2, x0
    // 0xb40690: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xb40690: ldur            w4, [x3, #0x17]
    // 0xb40694: DecompressPointer r4
    //     0xb40694: add             x4, x4, HEAP, lsl #32
    // 0xb40698: stp             x2, x4, [SP]
    // 0xb4069c: r4 = 0
    //     0xb4069c: movz            x4, #0
    // 0xb406a0: ldr             x0, [SP, #8]
    // 0xb406a4: r16 = UnlinkedCall_0x613b5c
    //     0xb406a4: add             x16, PP, #0x56, lsl #12  ; [pp+0x56fb0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb406a8: add             x16, x16, #0xfb0
    // 0xb406ac: ldp             x5, lr, [x16]
    // 0xb406b0: blr             lr
    // 0xb406b4: ldur            x0, [fp, #-0x10]
    // 0xb406b8: LoadField: r1 = r0->field_f
    //     0xb406b8: ldur            w1, [x0, #0xf]
    // 0xb406bc: DecompressPointer r1
    //     0xb406bc: add             x1, x1, HEAP, lsl #32
    // 0xb406c0: LoadField: r2 = r1->field_b
    //     0xb406c0: ldur            w2, [x1, #0xb]
    // 0xb406c4: DecompressPointer r2
    //     0xb406c4: add             x2, x2, HEAP, lsl #32
    // 0xb406c8: cmp             w2, NULL
    // 0xb406cc: b.eq            #0xb40730
    // 0xb406d0: ldr             x1, [fp, #0x10]
    // 0xb406d4: cmp             w1, NULL
    // 0xb406d8: b.ne            #0xb406e0
    // 0xb406dc: r1 = ""
    //     0xb406dc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb406e0: LoadField: r3 = r2->field_1b
    //     0xb406e0: ldur            w3, [x2, #0x1b]
    // 0xb406e4: DecompressPointer r3
    //     0xb406e4: add             x3, x3, HEAP, lsl #32
    // 0xb406e8: stp             x1, x3, [SP]
    // 0xb406ec: r4 = 0
    //     0xb406ec: movz            x4, #0
    // 0xb406f0: ldr             x0, [SP, #8]
    // 0xb406f4: r16 = UnlinkedCall_0x613b5c
    //     0xb406f4: add             x16, PP, #0x56, lsl #12  ; [pp+0x56fc0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb406f8: add             x16, x16, #0xfc0
    // 0xb406fc: ldp             x5, lr, [x16]
    // 0xb40700: blr             lr
    // 0xb40704: ldur            x0, [fp, #-0x10]
    // 0xb40708: LoadField: r1 = r0->field_f
    //     0xb40708: ldur            w1, [x0, #0xf]
    // 0xb4070c: DecompressPointer r1
    //     0xb4070c: add             x1, x1, HEAP, lsl #32
    // 0xb40710: r0 = validateAddress()
    //     0xb40710: bl              #0xb40358  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xb40714: r0 = Null
    //     0xb40714: mov             x0, NULL
    // 0xb40718: LeaveFrame
    //     0xb40718: mov             SP, fp
    //     0xb4071c: ldp             fp, lr, [SP], #0x10
    // 0xb40720: ret
    //     0xb40720: ret             
    // 0xb40724: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40724: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40728: b               #0xb40600
    // 0xb4072c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4072c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb40730: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb40730: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] String? _validatePinCode(dynamic, String?) {
    // ** addr: 0xb40734, size: 0x3c
    // 0xb40734: EnterFrame
    //     0xb40734: stp             fp, lr, [SP, #-0x10]!
    //     0xb40738: mov             fp, SP
    // 0xb4073c: ldr             x0, [fp, #0x18]
    // 0xb40740: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb40740: ldur            w1, [x0, #0x17]
    // 0xb40744: DecompressPointer r1
    //     0xb40744: add             x1, x1, HEAP, lsl #32
    // 0xb40748: CheckStackOverflow
    //     0xb40748: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4074c: cmp             SP, x16
    //     0xb40750: b.ls            #0xb40768
    // 0xb40754: ldr             x2, [fp, #0x10]
    // 0xb40758: r0 = _validatePinCode()
    //     0xb40758: bl              #0xb40770  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validatePinCode
    // 0xb4075c: LeaveFrame
    //     0xb4075c: mov             SP, fp
    //     0xb40760: ldp             fp, lr, [SP], #0x10
    // 0xb40764: ret
    //     0xb40764: ret             
    // 0xb40768: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40768: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4076c: b               #0xb40754
  }
  _ _validatePinCode(/* No info */) {
    // ** addr: 0xb40770, size: 0x15c
    // 0xb40770: EnterFrame
    //     0xb40770: stp             fp, lr, [SP, #-0x10]!
    //     0xb40774: mov             fp, SP
    // 0xb40778: AllocStack(0x10)
    //     0xb40778: sub             SP, SP, #0x10
    // 0xb4077c: SetupParameters(_ChangeAddressBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb4077c: mov             x0, x1
    //     0xb40780: stur            x1, [fp, #-8]
    //     0xb40784: stur            x2, [fp, #-0x10]
    // 0xb40788: CheckStackOverflow
    //     0xb40788: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4078c: cmp             SP, x16
    //     0xb40790: b.ls            #0xb408c0
    // 0xb40794: cmp             w2, NULL
    // 0xb40798: b.ne            #0xb407a4
    // 0xb4079c: r1 = ""
    //     0xb4079c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb407a0: b               #0xb407a8
    // 0xb407a4: mov             x1, x2
    // 0xb407a8: r0 = checkEmpty()
    //     0xb407a8: bl              #0x8fc4b0  ; [package:customer_app/app/core/utils/utils.dart] Utils::checkEmpty
    // 0xb407ac: tbnz            w0, #4, #0xb407c4
    // 0xb407b0: r0 = "Pincode field is required"
    //     0xb407b0: add             x0, PP, #0x54, lsl #12  ; [pp+0x541e8] "Pincode field is required"
    //     0xb407b4: ldr             x0, [x0, #0x1e8]
    // 0xb407b8: LeaveFrame
    //     0xb407b8: mov             SP, fp
    //     0xb407bc: ldp             fp, lr, [SP], #0x10
    // 0xb407c0: ret
    //     0xb407c0: ret             
    // 0xb407c4: ldur            x0, [fp, #-0x10]
    // 0xb407c8: cmp             w0, NULL
    // 0xb407cc: b.ne            #0xb407d8
    // 0xb407d0: r0 = Null
    //     0xb407d0: mov             x0, NULL
    // 0xb407d4: b               #0xb407e0
    // 0xb407d8: LoadField: r1 = r0->field_7
    //     0xb407d8: ldur            w1, [x0, #7]
    // 0xb407dc: mov             x0, x1
    // 0xb407e0: cmp             w0, NULL
    // 0xb407e4: b.ne            #0xb407f0
    // 0xb407e8: r0 = 0
    //     0xb407e8: movz            x0, #0
    // 0xb407ec: b               #0xb407f8
    // 0xb407f0: r1 = LoadInt32Instr(r0)
    //     0xb407f0: sbfx            x1, x0, #1, #0x1f
    // 0xb407f4: mov             x0, x1
    // 0xb407f8: cmp             x0, #6
    // 0xb407fc: b.ge            #0xb40814
    // 0xb40800: r0 = "Please enter 6 digit pincode"
    //     0xb40800: add             x0, PP, #0x54, lsl #12  ; [pp+0x541f0] "Please enter 6 digit pincode"
    //     0xb40804: ldr             x0, [x0, #0x1f0]
    // 0xb40808: LeaveFrame
    //     0xb40808: mov             SP, fp
    //     0xb4080c: ldp             fp, lr, [SP], #0x10
    // 0xb40810: ret
    //     0xb40810: ret             
    // 0xb40814: ldur            x1, [fp, #-8]
    // 0xb40818: LoadField: r0 = r1->field_b
    //     0xb40818: ldur            w0, [x1, #0xb]
    // 0xb4081c: DecompressPointer r0
    //     0xb4081c: add             x0, x0, HEAP, lsl #32
    // 0xb40820: cmp             w0, NULL
    // 0xb40824: b.eq            #0xb408c8
    // 0xb40828: LoadField: r2 = r0->field_13
    //     0xb40828: ldur            w2, [x0, #0x13]
    // 0xb4082c: DecompressPointer r2
    //     0xb4082c: add             x2, x2, HEAP, lsl #32
    // 0xb40830: LoadField: r0 = r2->field_b
    //     0xb40830: ldur            w0, [x2, #0xb]
    // 0xb40834: DecompressPointer r0
    //     0xb40834: add             x0, x0, HEAP, lsl #32
    // 0xb40838: cmp             w0, NULL
    // 0xb4083c: b.eq            #0xb408b0
    // 0xb40840: LoadField: r2 = r0->field_13
    //     0xb40840: ldur            w2, [x0, #0x13]
    // 0xb40844: DecompressPointer r2
    //     0xb40844: add             x2, x2, HEAP, lsl #32
    // 0xb40848: cmp             w2, NULL
    // 0xb4084c: b.eq            #0xb40884
    // 0xb40850: cmp             w2, NULL
    // 0xb40854: b.ne            #0xb40860
    // 0xb40858: r0 = Null
    //     0xb40858: mov             x0, NULL
    // 0xb4085c: b               #0xb40878
    // 0xb40860: LoadField: r0 = r2->field_7
    //     0xb40860: ldur            w0, [x2, #7]
    // 0xb40864: cbz             w0, #0xb40870
    // 0xb40868: r2 = false
    //     0xb40868: add             x2, NULL, #0x30  ; false
    // 0xb4086c: b               #0xb40874
    // 0xb40870: r2 = true
    //     0xb40870: add             x2, NULL, #0x20  ; true
    // 0xb40874: mov             x0, x2
    // 0xb40878: cmp             w0, NULL
    // 0xb4087c: b.eq            #0xb4089c
    // 0xb40880: tbnz            w0, #4, #0xb4089c
    // 0xb40884: r0 = validateAddress()
    //     0xb40884: bl              #0xb40358  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xb40888: r0 = "Provided Pincode is not serviceable"
    //     0xb40888: add             x0, PP, #0x54, lsl #12  ; [pp+0x541f8] "Provided Pincode is not serviceable"
    //     0xb4088c: ldr             x0, [x0, #0x1f8]
    // 0xb40890: LeaveFrame
    //     0xb40890: mov             SP, fp
    //     0xb40894: ldp             fp, lr, [SP], #0x10
    // 0xb40898: ret
    //     0xb40898: ret             
    // 0xb4089c: r0 = validateAddress()
    //     0xb4089c: bl              #0xb40358  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xb408a0: r0 = Null
    //     0xb408a0: mov             x0, NULL
    // 0xb408a4: LeaveFrame
    //     0xb408a4: mov             SP, fp
    //     0xb408a8: ldp             fp, lr, [SP], #0x10
    // 0xb408ac: ret
    //     0xb408ac: ret             
    // 0xb408b0: r0 = Null
    //     0xb408b0: mov             x0, NULL
    // 0xb408b4: LeaveFrame
    //     0xb408b4: mov             SP, fp
    //     0xb408b8: ldp             fp, lr, [SP], #0x10
    // 0xb408bc: ret
    //     0xb408bc: ret             
    // 0xb408c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb408c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb408c4: b               #0xb40794
    // 0xb408c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb408c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb408cc, size: 0x78
    // 0xb408cc: EnterFrame
    //     0xb408cc: stp             fp, lr, [SP, #-0x10]!
    //     0xb408d0: mov             fp, SP
    // 0xb408d4: AllocStack(0x10)
    //     0xb408d4: sub             SP, SP, #0x10
    // 0xb408d8: SetupParameters()
    //     0xb408d8: ldr             x0, [fp, #0x18]
    //     0xb408dc: ldur            w3, [x0, #0x17]
    //     0xb408e0: add             x3, x3, HEAP, lsl #32
    //     0xb408e4: stur            x3, [fp, #-0x10]
    // 0xb408e8: CheckStackOverflow
    //     0xb408e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb408ec: cmp             SP, x16
    //     0xb408f0: b.ls            #0xb4093c
    // 0xb408f4: LoadField: r0 = r3->field_f
    //     0xb408f4: ldur            w0, [x3, #0xf]
    // 0xb408f8: DecompressPointer r0
    //     0xb408f8: add             x0, x0, HEAP, lsl #32
    // 0xb408fc: mov             x2, x3
    // 0xb40900: stur            x0, [fp, #-8]
    // 0xb40904: r1 = Function '<anonymous closure>':.
    //     0xb40904: add             x1, PP, #0x56, lsl #12  ; [pp+0x56fd0] AnonymousClosure: (0xa022d8), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xb40908: ldr             x1, [x1, #0xfd0]
    // 0xb4090c: r0 = AllocateClosure()
    //     0xb4090c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb40910: ldur            x1, [fp, #-8]
    // 0xb40914: mov             x2, x0
    // 0xb40918: r0 = setState()
    //     0xb40918: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb4091c: ldur            x0, [fp, #-0x10]
    // 0xb40920: LoadField: r1 = r0->field_f
    //     0xb40920: ldur            w1, [x0, #0xf]
    // 0xb40924: DecompressPointer r1
    //     0xb40924: add             x1, x1, HEAP, lsl #32
    // 0xb40928: r0 = validateAddress()
    //     0xb40928: bl              #0xb40358  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xb4092c: r0 = Null
    //     0xb4092c: mov             x0, NULL
    // 0xb40930: LeaveFrame
    //     0xb40930: mov             SP, fp
    //     0xb40934: ldp             fp, lr, [SP], #0x10
    // 0xb40938: ret
    //     0xb40938: ret             
    // 0xb4093c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4093c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40940: b               #0xb408f4
  }
  [closure] String? _validateAddress(dynamic, String?) {
    // ** addr: 0xb40944, size: 0x3c
    // 0xb40944: EnterFrame
    //     0xb40944: stp             fp, lr, [SP, #-0x10]!
    //     0xb40948: mov             fp, SP
    // 0xb4094c: ldr             x0, [fp, #0x18]
    // 0xb40950: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb40950: ldur            w1, [x0, #0x17]
    // 0xb40954: DecompressPointer r1
    //     0xb40954: add             x1, x1, HEAP, lsl #32
    // 0xb40958: CheckStackOverflow
    //     0xb40958: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4095c: cmp             SP, x16
    //     0xb40960: b.ls            #0xb40978
    // 0xb40964: ldr             x2, [fp, #0x10]
    // 0xb40968: r0 = _validateAddress()
    //     0xb40968: bl              #0xa023b0  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateAddress
    // 0xb4096c: LeaveFrame
    //     0xb4096c: mov             SP, fp
    //     0xb40970: ldp             fp, lr, [SP], #0x10
    // 0xb40974: ret
    //     0xb40974: ret             
    // 0xb40978: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40978: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4097c: b               #0xb40964
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb40980, size: 0x78
    // 0xb40980: EnterFrame
    //     0xb40980: stp             fp, lr, [SP, #-0x10]!
    //     0xb40984: mov             fp, SP
    // 0xb40988: AllocStack(0x10)
    //     0xb40988: sub             SP, SP, #0x10
    // 0xb4098c: SetupParameters()
    //     0xb4098c: ldr             x0, [fp, #0x18]
    //     0xb40990: ldur            w3, [x0, #0x17]
    //     0xb40994: add             x3, x3, HEAP, lsl #32
    //     0xb40998: stur            x3, [fp, #-0x10]
    // 0xb4099c: CheckStackOverflow
    //     0xb4099c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb409a0: cmp             SP, x16
    //     0xb409a4: b.ls            #0xb409f0
    // 0xb409a8: LoadField: r0 = r3->field_f
    //     0xb409a8: ldur            w0, [x3, #0xf]
    // 0xb409ac: DecompressPointer r0
    //     0xb409ac: add             x0, x0, HEAP, lsl #32
    // 0xb409b0: mov             x2, x3
    // 0xb409b4: stur            x0, [fp, #-8]
    // 0xb409b8: r1 = Function '<anonymous closure>':.
    //     0xb409b8: add             x1, PP, #0x56, lsl #12  ; [pp+0x56fd8] AnonymousClosure: (0xa024dc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xb409bc: ldr             x1, [x1, #0xfd8]
    // 0xb409c0: r0 = AllocateClosure()
    //     0xb409c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb409c4: ldur            x1, [fp, #-8]
    // 0xb409c8: mov             x2, x0
    // 0xb409cc: r0 = setState()
    //     0xb409cc: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb409d0: ldur            x0, [fp, #-0x10]
    // 0xb409d4: LoadField: r1 = r0->field_f
    //     0xb409d4: ldur            w1, [x0, #0xf]
    // 0xb409d8: DecompressPointer r1
    //     0xb409d8: add             x1, x1, HEAP, lsl #32
    // 0xb409dc: r0 = validateAddress()
    //     0xb409dc: bl              #0xb40358  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xb409e0: r0 = Null
    //     0xb409e0: mov             x0, NULL
    // 0xb409e4: LeaveFrame
    //     0xb409e4: mov             SP, fp
    //     0xb409e8: ldp             fp, lr, [SP], #0x10
    // 0xb409ec: ret
    //     0xb409ec: ret             
    // 0xb409f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb409f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb409f4: b               #0xb409a8
  }
  [closure] String? _validateHouseNumber(dynamic, String?) {
    // ** addr: 0xb409f8, size: 0x3c
    // 0xb409f8: EnterFrame
    //     0xb409f8: stp             fp, lr, [SP, #-0x10]!
    //     0xb409fc: mov             fp, SP
    // 0xb40a00: ldr             x0, [fp, #0x18]
    // 0xb40a04: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb40a04: ldur            w1, [x0, #0x17]
    // 0xb40a08: DecompressPointer r1
    //     0xb40a08: add             x1, x1, HEAP, lsl #32
    // 0xb40a0c: CheckStackOverflow
    //     0xb40a0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40a10: cmp             SP, x16
    //     0xb40a14: b.ls            #0xb40a2c
    // 0xb40a18: ldr             x2, [fp, #0x10]
    // 0xb40a1c: r0 = _validateHouseNumber()
    //     0xb40a1c: bl              #0xa025b4  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateHouseNumber
    // 0xb40a20: LeaveFrame
    //     0xb40a20: mov             SP, fp
    //     0xb40a24: ldp             fp, lr, [SP], #0x10
    // 0xb40a28: ret
    //     0xb40a28: ret             
    // 0xb40a2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40a2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40a30: b               #0xb40a18
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb40a34, size: 0x78
    // 0xb40a34: EnterFrame
    //     0xb40a34: stp             fp, lr, [SP, #-0x10]!
    //     0xb40a38: mov             fp, SP
    // 0xb40a3c: AllocStack(0x10)
    //     0xb40a3c: sub             SP, SP, #0x10
    // 0xb40a40: SetupParameters()
    //     0xb40a40: ldr             x0, [fp, #0x18]
    //     0xb40a44: ldur            w3, [x0, #0x17]
    //     0xb40a48: add             x3, x3, HEAP, lsl #32
    //     0xb40a4c: stur            x3, [fp, #-0x10]
    // 0xb40a50: CheckStackOverflow
    //     0xb40a50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40a54: cmp             SP, x16
    //     0xb40a58: b.ls            #0xb40aa4
    // 0xb40a5c: LoadField: r0 = r3->field_f
    //     0xb40a5c: ldur            w0, [x3, #0xf]
    // 0xb40a60: DecompressPointer r0
    //     0xb40a60: add             x0, x0, HEAP, lsl #32
    // 0xb40a64: mov             x2, x3
    // 0xb40a68: stur            x0, [fp, #-8]
    // 0xb40a6c: r1 = Function '<anonymous closure>':.
    //     0xb40a6c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56fe0] AnonymousClosure: (0xa02688), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xbaddd0)
    //     0xb40a70: ldr             x1, [x1, #0xfe0]
    // 0xb40a74: r0 = AllocateClosure()
    //     0xb40a74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb40a78: ldur            x1, [fp, #-8]
    // 0xb40a7c: mov             x2, x0
    // 0xb40a80: r0 = setState()
    //     0xb40a80: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb40a84: ldur            x0, [fp, #-0x10]
    // 0xb40a88: LoadField: r1 = r0->field_f
    //     0xb40a88: ldur            w1, [x0, #0xf]
    // 0xb40a8c: DecompressPointer r1
    //     0xb40a8c: add             x1, x1, HEAP, lsl #32
    // 0xb40a90: r0 = validateAddress()
    //     0xb40a90: bl              #0xb40358  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xb40a94: r0 = Null
    //     0xb40a94: mov             x0, NULL
    // 0xb40a98: LeaveFrame
    //     0xb40a98: mov             SP, fp
    //     0xb40a9c: ldp             fp, lr, [SP], #0x10
    // 0xb40aa0: ret
    //     0xb40aa0: ret             
    // 0xb40aa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40aa4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40aa8: b               #0xb40a5c
  }
  [closure] String? _validateCustomerNameNumber(dynamic, String?) {
    // ** addr: 0xb40aac, size: 0x3c
    // 0xb40aac: EnterFrame
    //     0xb40aac: stp             fp, lr, [SP, #-0x10]!
    //     0xb40ab0: mov             fp, SP
    // 0xb40ab4: ldr             x0, [fp, #0x18]
    // 0xb40ab8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb40ab8: ldur            w1, [x0, #0x17]
    // 0xb40abc: DecompressPointer r1
    //     0xb40abc: add             x1, x1, HEAP, lsl #32
    // 0xb40ac0: CheckStackOverflow
    //     0xb40ac0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40ac4: cmp             SP, x16
    //     0xb40ac8: b.ls            #0xb40ae0
    // 0xb40acc: ldr             x2, [fp, #0x10]
    // 0xb40ad0: r0 = _validateCustomerNameNumber()
    //     0xb40ad0: bl              #0xa02760  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateCustomerNameNumber
    // 0xb40ad4: LeaveFrame
    //     0xb40ad4: mov             SP, fp
    //     0xb40ad8: ldp             fp, lr, [SP], #0x10
    // 0xb40adc: ret
    //     0xb40adc: ret             
    // 0xb40ae0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40ae0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40ae4: b               #0xb40acc
  }
}

// class id: 4108, size: 0x28, field offset: 0xc
class ChangeAddressBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e8d0, size: 0x48
    // 0xc7e8d0: EnterFrame
    //     0xc7e8d0: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e8d4: mov             fp, SP
    // 0xc7e8d8: AllocStack(0x8)
    //     0xc7e8d8: sub             SP, SP, #8
    // 0xc7e8dc: CheckStackOverflow
    //     0xc7e8dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7e8e0: cmp             SP, x16
    //     0xc7e8e4: b.ls            #0xc7e910
    // 0xc7e8e8: r1 = <ChangeAddressBottomSheet>
    //     0xc7e8e8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a68] TypeArguments: <ChangeAddressBottomSheet>
    //     0xc7e8ec: ldr             x1, [x1, #0xa68]
    // 0xc7e8f0: r0 = _ChangeAddressBottomSheetState()
    //     0xc7e8f0: bl              #0xc7e918  ; Allocate_ChangeAddressBottomSheetStateStub -> _ChangeAddressBottomSheetState (size=0x68)
    // 0xc7e8f4: mov             x1, x0
    // 0xc7e8f8: stur            x0, [fp, #-8]
    // 0xc7e8fc: r0 = _ChangeAddressBottomSheetState()
    //     0xc7e8fc: bl              #0xc7a510  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_ChangeAddressBottomSheetState
    // 0xc7e900: ldur            x0, [fp, #-8]
    // 0xc7e904: LeaveFrame
    //     0xc7e904: mov             SP, fp
    //     0xc7e908: ldp             fp, lr, [SP], #0x10
    // 0xc7e90c: ret
    //     0xc7e90c: ret             
    // 0xc7e910: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e910: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e914: b               #0xc7e8e8
  }
}
