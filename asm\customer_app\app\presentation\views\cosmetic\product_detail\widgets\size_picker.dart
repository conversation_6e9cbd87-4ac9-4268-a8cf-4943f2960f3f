// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/size_picker.dart

// class id: 1049327, size: 0x8
class :: {
}

// class id: 3392, size: 0x1c, field offset: 0x14
class _SizePickerState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb1aea4, size: 0x6cc
    // 0xb1aea4: EnterFrame
    //     0xb1aea4: stp             fp, lr, [SP, #-0x10]!
    //     0xb1aea8: mov             fp, SP
    // 0xb1aeac: AllocStack(0x58)
    //     0xb1aeac: sub             SP, SP, #0x58
    // 0xb1aeb0: SetupParameters(_SizePickerState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb1aeb0: mov             x0, x1
    //     0xb1aeb4: stur            x1, [fp, #-8]
    //     0xb1aeb8: mov             x1, x2
    //     0xb1aebc: stur            x2, [fp, #-0x10]
    // 0xb1aec0: CheckStackOverflow
    //     0xb1aec0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1aec4: cmp             SP, x16
    //     0xb1aec8: b.ls            #0xb1b54c
    // 0xb1aecc: r1 = 2
    //     0xb1aecc: movz            x1, #0x2
    // 0xb1aed0: r0 = AllocateContext()
    //     0xb1aed0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb1aed4: mov             x1, x0
    // 0xb1aed8: ldur            x0, [fp, #-8]
    // 0xb1aedc: stur            x1, [fp, #-0x18]
    // 0xb1aee0: StoreField: r1->field_f = r0
    //     0xb1aee0: stur            w0, [x1, #0xf]
    // 0xb1aee4: LoadField: r2 = r0->field_b
    //     0xb1aee4: ldur            w2, [x0, #0xb]
    // 0xb1aee8: DecompressPointer r2
    //     0xb1aee8: add             x2, x2, HEAP, lsl #32
    // 0xb1aeec: cmp             w2, NULL
    // 0xb1aef0: b.eq            #0xb1b554
    // 0xb1aef4: LoadField: r3 = r2->field_b
    //     0xb1aef4: ldur            w3, [x2, #0xb]
    // 0xb1aef8: DecompressPointer r3
    //     0xb1aef8: add             x3, x3, HEAP, lsl #32
    // 0xb1aefc: LoadField: r2 = r3->field_1b
    //     0xb1aefc: ldur            w2, [x3, #0x1b]
    // 0xb1af00: DecompressPointer r2
    //     0xb1af00: add             x2, x2, HEAP, lsl #32
    // 0xb1af04: cmp             w2, NULL
    // 0xb1af08: b.ne            #0xb1af14
    // 0xb1af0c: r2 = Null
    //     0xb1af0c: mov             x2, NULL
    // 0xb1af10: b               #0xb1af6c
    // 0xb1af14: r16 = <WidgetEntity?>
    //     0xb1af14: add             x16, PP, #0x31, lsl #12  ; [pp+0x31e28] TypeArguments: <WidgetEntity?>
    //     0xb1af18: ldr             x16, [x16, #0xe28]
    // 0xb1af1c: stp             x2, x16, [SP]
    // 0xb1af20: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb1af20: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb1af24: r0 = cast()
    //     0xb1af24: bl              #0x7d9dc8  ; [dart:collection] ListBase::cast
    // 0xb1af28: ldur            x2, [fp, #-0x18]
    // 0xb1af2c: r1 = Function '<anonymous closure>':.
    //     0xb1af2c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57890] AnonymousClosure: (0xa9d774), in [package:customer_app/app/presentation/views/line/product_detail/widgets/size_picker.dart] _SizePickerState::build (0xc0ec90)
    //     0xb1af30: ldr             x1, [x1, #0x890]
    // 0xb1af34: stur            x0, [fp, #-0x20]
    // 0xb1af38: r0 = AllocateClosure()
    //     0xb1af38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1af3c: r1 = Function '<anonymous closure>':.
    //     0xb1af3c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57898] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb1af40: ldr             x1, [x1, #0x898]
    // 0xb1af44: r2 = Null
    //     0xb1af44: mov             x2, NULL
    // 0xb1af48: stur            x0, [fp, #-0x28]
    // 0xb1af4c: r0 = AllocateClosure()
    //     0xb1af4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1af50: str             x0, [SP]
    // 0xb1af54: ldur            x1, [fp, #-0x20]
    // 0xb1af58: ldur            x2, [fp, #-0x28]
    // 0xb1af5c: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xb1af5c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xb1af60: ldr             x4, [x4, #0xb48]
    // 0xb1af64: r0 = firstWhere()
    //     0xb1af64: bl              #0x908130  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::firstWhere
    // 0xb1af68: mov             x2, x0
    // 0xb1af6c: cmp             w2, NULL
    // 0xb1af70: b.eq            #0xb1b000
    // 0xb1af74: ldur            x0, [fp, #-8]
    // 0xb1af78: LoadField: r1 = r0->field_b
    //     0xb1af78: ldur            w1, [x0, #0xb]
    // 0xb1af7c: DecompressPointer r1
    //     0xb1af7c: add             x1, x1, HEAP, lsl #32
    // 0xb1af80: cmp             w1, NULL
    // 0xb1af84: b.eq            #0xb1b558
    // 0xb1af88: LoadField: r3 = r1->field_b
    //     0xb1af88: ldur            w3, [x1, #0xb]
    // 0xb1af8c: DecompressPointer r3
    //     0xb1af8c: add             x3, x3, HEAP, lsl #32
    // 0xb1af90: LoadField: r1 = r3->field_1b
    //     0xb1af90: ldur            w1, [x3, #0x1b]
    // 0xb1af94: DecompressPointer r1
    //     0xb1af94: add             x1, x1, HEAP, lsl #32
    // 0xb1af98: cmp             w1, NULL
    // 0xb1af9c: b.ne            #0xb1afac
    // 0xb1afa0: mov             x2, x0
    // 0xb1afa4: r1 = Null
    //     0xb1afa4: mov             x1, NULL
    // 0xb1afa8: b               #0xb1afd4
    // 0xb1afac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb1afac: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb1afb0: r0 = indexOf()
    //     0xb1afb0: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0xb1afb4: mov             x2, x0
    // 0xb1afb8: r0 = BoxInt64Instr(r2)
    //     0xb1afb8: sbfiz           x0, x2, #1, #0x1f
    //     0xb1afbc: cmp             x2, x0, asr #1
    //     0xb1afc0: b.eq            #0xb1afcc
    //     0xb1afc4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb1afc8: stur            x2, [x0, #7]
    // 0xb1afcc: mov             x1, x0
    // 0xb1afd0: ldur            x2, [fp, #-8]
    // 0xb1afd4: mov             x0, x1
    // 0xb1afd8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb1afd8: stur            w0, [x2, #0x17]
    //     0xb1afdc: tbz             w0, #0, #0xb1aff8
    //     0xb1afe0: ldurb           w16, [x2, #-1]
    //     0xb1afe4: ldurb           w17, [x0, #-1]
    //     0xb1afe8: and             x16, x17, x16, lsr #2
    //     0xb1afec: tst             x16, HEAP, lsr #32
    //     0xb1aff0: b.eq            #0xb1aff8
    //     0xb1aff4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb1aff8: mov             x0, x1
    // 0xb1affc: b               #0xb1b00c
    // 0xb1b000: ldur            x2, [fp, #-8]
    // 0xb1b004: ArrayStore: r2[0] = rZR  ; List_4
    //     0xb1b004: stur            wzr, [x2, #0x17]
    // 0xb1b008: r0 = 0
    //     0xb1b008: movz            x0, #0
    // 0xb1b00c: LoadField: r1 = r2->field_b
    //     0xb1b00c: ldur            w1, [x2, #0xb]
    // 0xb1b010: DecompressPointer r1
    //     0xb1b010: add             x1, x1, HEAP, lsl #32
    // 0xb1b014: cmp             w1, NULL
    // 0xb1b018: b.eq            #0xb1b55c
    // 0xb1b01c: LoadField: r3 = r1->field_b
    //     0xb1b01c: ldur            w3, [x1, #0xb]
    // 0xb1b020: DecompressPointer r3
    //     0xb1b020: add             x3, x3, HEAP, lsl #32
    // 0xb1b024: LoadField: r4 = r3->field_1b
    //     0xb1b024: ldur            w4, [x3, #0x1b]
    // 0xb1b028: DecompressPointer r4
    //     0xb1b028: add             x4, x4, HEAP, lsl #32
    // 0xb1b02c: cmp             w4, NULL
    // 0xb1b030: b.ne            #0xb1b03c
    // 0xb1b034: r0 = Null
    //     0xb1b034: mov             x0, NULL
    // 0xb1b038: b               #0xb1b08c
    // 0xb1b03c: cmp             w0, NULL
    // 0xb1b040: b.ne            #0xb1b04c
    // 0xb1b044: r3 = 0
    //     0xb1b044: movz            x3, #0
    // 0xb1b048: b               #0xb1b05c
    // 0xb1b04c: r1 = LoadInt32Instr(r0)
    //     0xb1b04c: sbfx            x1, x0, #1, #0x1f
    //     0xb1b050: tbz             w0, #0, #0xb1b058
    //     0xb1b054: ldur            x1, [x0, #7]
    // 0xb1b058: mov             x3, x1
    // 0xb1b05c: LoadField: r0 = r4->field_b
    //     0xb1b05c: ldur            w0, [x4, #0xb]
    // 0xb1b060: r1 = LoadInt32Instr(r0)
    //     0xb1b060: sbfx            x1, x0, #1, #0x1f
    // 0xb1b064: mov             x0, x1
    // 0xb1b068: mov             x1, x3
    // 0xb1b06c: cmp             x1, x0
    // 0xb1b070: b.hs            #0xb1b560
    // 0xb1b074: LoadField: r0 = r4->field_f
    //     0xb1b074: ldur            w0, [x4, #0xf]
    // 0xb1b078: DecompressPointer r0
    //     0xb1b078: add             x0, x0, HEAP, lsl #32
    // 0xb1b07c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb1b07c: add             x16, x0, x3, lsl #2
    //     0xb1b080: ldur            w1, [x16, #0xf]
    // 0xb1b084: DecompressPointer r1
    //     0xb1b084: add             x1, x1, HEAP, lsl #32
    // 0xb1b088: mov             x0, x1
    // 0xb1b08c: cmp             w0, NULL
    // 0xb1b090: b.ne            #0xb1b0a0
    // 0xb1b094: r0 = WidgetEntity()
    //     0xb1b094: bl              #0xa749ec  ; AllocateWidgetEntityStub -> WidgetEntity (size=0x140)
    // 0xb1b098: mov             x1, x0
    // 0xb1b09c: b               #0xb1b0a4
    // 0xb1b0a0: mov             x1, x0
    // 0xb1b0a4: ldur            x3, [fp, #-8]
    // 0xb1b0a8: mov             x0, x1
    // 0xb1b0ac: StoreField: r3->field_13 = r0
    //     0xb1b0ac: stur            w0, [x3, #0x13]
    //     0xb1b0b0: ldurb           w16, [x3, #-1]
    //     0xb1b0b4: ldurb           w17, [x0, #-1]
    //     0xb1b0b8: and             x16, x17, x16, lsr #2
    //     0xb1b0bc: tst             x16, HEAP, lsr #32
    //     0xb1b0c0: b.eq            #0xb1b0c8
    //     0xb1b0c4: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xb1b0c8: LoadField: r0 = r1->field_33
    //     0xb1b0c8: ldur            w0, [x1, #0x33]
    // 0xb1b0cc: DecompressPointer r0
    //     0xb1b0cc: add             x0, x0, HEAP, lsl #32
    // 0xb1b0d0: cmp             w0, NULL
    // 0xb1b0d4: b.ne            #0xb1b0dc
    // 0xb1b0d8: r0 = ""
    //     0xb1b0d8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb1b0dc: ldur            x4, [fp, #-0x18]
    // 0xb1b0e0: StoreField: r4->field_13 = r0
    //     0xb1b0e0: stur            w0, [x4, #0x13]
    //     0xb1b0e4: ldurb           w16, [x4, #-1]
    //     0xb1b0e8: ldurb           w17, [x0, #-1]
    //     0xb1b0ec: and             x16, x17, x16, lsr #2
    //     0xb1b0f0: tst             x16, HEAP, lsr #32
    //     0xb1b0f4: b.eq            #0xb1b0fc
    //     0xb1b0f8: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0xb1b0fc: r1 = <Widget>
    //     0xb1b0fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1b100: r2 = 0
    //     0xb1b100: movz            x2, #0
    // 0xb1b104: r0 = _GrowableList()
    //     0xb1b104: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb1b108: mov             x2, x0
    // 0xb1b10c: ldur            x1, [fp, #-8]
    // 0xb1b110: stur            x2, [fp, #-0x20]
    // 0xb1b114: LoadField: r0 = r1->field_b
    //     0xb1b114: ldur            w0, [x1, #0xb]
    // 0xb1b118: DecompressPointer r0
    //     0xb1b118: add             x0, x0, HEAP, lsl #32
    // 0xb1b11c: cmp             w0, NULL
    // 0xb1b120: b.eq            #0xb1b564
    // 0xb1b124: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb1b124: ldur            w3, [x0, #0x17]
    // 0xb1b128: DecompressPointer r3
    //     0xb1b128: add             x3, x3, HEAP, lsl #32
    // 0xb1b12c: r0 = LoadClassIdInstr(r3)
    //     0xb1b12c: ldur            x0, [x3, #-1]
    //     0xb1b130: ubfx            x0, x0, #0xc, #0x14
    // 0xb1b134: r16 = "size"
    //     0xb1b134: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xb1b138: ldr             x16, [x16, #0x9c0]
    // 0xb1b13c: stp             x16, x3, [SP]
    // 0xb1b140: mov             lr, x0
    // 0xb1b144: ldr             lr, [x21, lr, lsl #3]
    // 0xb1b148: blr             lr
    // 0xb1b14c: tbnz            w0, #4, #0xb1b238
    // 0xb1b150: ldur            x0, [fp, #-0x20]
    // 0xb1b154: ldur            x1, [fp, #-0x10]
    // 0xb1b158: r0 = of()
    //     0xb1b158: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1b15c: LoadField: r1 = r0->field_87
    //     0xb1b15c: ldur            w1, [x0, #0x87]
    // 0xb1b160: DecompressPointer r1
    //     0xb1b160: add             x1, x1, HEAP, lsl #32
    // 0xb1b164: LoadField: r0 = r1->field_7
    //     0xb1b164: ldur            w0, [x1, #7]
    // 0xb1b168: DecompressPointer r0
    //     0xb1b168: add             x0, x0, HEAP, lsl #32
    // 0xb1b16c: r16 = 16.000000
    //     0xb1b16c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb1b170: ldr             x16, [x16, #0x188]
    // 0xb1b174: r30 = Instance_Color
    //     0xb1b174: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1b178: stp             lr, x16, [SP]
    // 0xb1b17c: mov             x1, x0
    // 0xb1b180: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1b180: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1b184: ldr             x4, [x4, #0xaa0]
    // 0xb1b188: r0 = copyWith()
    //     0xb1b188: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1b18c: stur            x0, [fp, #-0x28]
    // 0xb1b190: r0 = Text()
    //     0xb1b190: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1b194: mov             x2, x0
    // 0xb1b198: r0 = "Select Size"
    //     0xb1b198: add             x0, PP, #0x52, lsl #12  ; [pp+0x52370] "Select Size"
    //     0xb1b19c: ldr             x0, [x0, #0x370]
    // 0xb1b1a0: stur            x2, [fp, #-0x38]
    // 0xb1b1a4: StoreField: r2->field_b = r0
    //     0xb1b1a4: stur            w0, [x2, #0xb]
    // 0xb1b1a8: ldur            x0, [fp, #-0x28]
    // 0xb1b1ac: StoreField: r2->field_13 = r0
    //     0xb1b1ac: stur            w0, [x2, #0x13]
    // 0xb1b1b0: ldur            x0, [fp, #-0x20]
    // 0xb1b1b4: LoadField: r1 = r0->field_b
    //     0xb1b1b4: ldur            w1, [x0, #0xb]
    // 0xb1b1b8: LoadField: r3 = r0->field_f
    //     0xb1b1b8: ldur            w3, [x0, #0xf]
    // 0xb1b1bc: DecompressPointer r3
    //     0xb1b1bc: add             x3, x3, HEAP, lsl #32
    // 0xb1b1c0: LoadField: r4 = r3->field_b
    //     0xb1b1c0: ldur            w4, [x3, #0xb]
    // 0xb1b1c4: r3 = LoadInt32Instr(r1)
    //     0xb1b1c4: sbfx            x3, x1, #1, #0x1f
    // 0xb1b1c8: stur            x3, [fp, #-0x30]
    // 0xb1b1cc: r1 = LoadInt32Instr(r4)
    //     0xb1b1cc: sbfx            x1, x4, #1, #0x1f
    // 0xb1b1d0: cmp             x3, x1
    // 0xb1b1d4: b.ne            #0xb1b1e0
    // 0xb1b1d8: mov             x1, x0
    // 0xb1b1dc: r0 = _growToNextCapacity()
    //     0xb1b1dc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1b1e0: ldur            x2, [fp, #-0x20]
    // 0xb1b1e4: ldur            x3, [fp, #-0x30]
    // 0xb1b1e8: add             x4, x3, #1
    // 0xb1b1ec: lsl             x0, x4, #1
    // 0xb1b1f0: StoreField: r2->field_b = r0
    //     0xb1b1f0: stur            w0, [x2, #0xb]
    // 0xb1b1f4: LoadField: r5 = r2->field_f
    //     0xb1b1f4: ldur            w5, [x2, #0xf]
    // 0xb1b1f8: DecompressPointer r5
    //     0xb1b1f8: add             x5, x5, HEAP, lsl #32
    // 0xb1b1fc: mov             x1, x5
    // 0xb1b200: ldur            x0, [fp, #-0x38]
    // 0xb1b204: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1b204: add             x25, x1, x3, lsl #2
    //     0xb1b208: add             x25, x25, #0xf
    //     0xb1b20c: str             w0, [x25]
    //     0xb1b210: tbz             w0, #0, #0xb1b22c
    //     0xb1b214: ldurb           w16, [x1, #-1]
    //     0xb1b218: ldurb           w17, [x0, #-1]
    //     0xb1b21c: and             x16, x17, x16, lsr #2
    //     0xb1b220: tst             x16, HEAP, lsr #32
    //     0xb1b224: b.eq            #0xb1b22c
    //     0xb1b228: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1b22c: mov             x3, x4
    // 0xb1b230: mov             x0, x5
    // 0xb1b234: b               #0xb1b31c
    // 0xb1b238: ldur            x2, [fp, #-0x20]
    // 0xb1b23c: ldur            x1, [fp, #-0x10]
    // 0xb1b240: r0 = of()
    //     0xb1b240: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1b244: LoadField: r1 = r0->field_87
    //     0xb1b244: ldur            w1, [x0, #0x87]
    // 0xb1b248: DecompressPointer r1
    //     0xb1b248: add             x1, x1, HEAP, lsl #32
    // 0xb1b24c: LoadField: r0 = r1->field_7
    //     0xb1b24c: ldur            w0, [x1, #7]
    // 0xb1b250: DecompressPointer r0
    //     0xb1b250: add             x0, x0, HEAP, lsl #32
    // 0xb1b254: r16 = 16.000000
    //     0xb1b254: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb1b258: ldr             x16, [x16, #0x188]
    // 0xb1b25c: r30 = Instance_Color
    //     0xb1b25c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1b260: stp             lr, x16, [SP]
    // 0xb1b264: mov             x1, x0
    // 0xb1b268: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1b268: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1b26c: ldr             x4, [x4, #0xaa0]
    // 0xb1b270: r0 = copyWith()
    //     0xb1b270: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1b274: stur            x0, [fp, #-0x10]
    // 0xb1b278: r0 = Text()
    //     0xb1b278: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1b27c: mov             x2, x0
    // 0xb1b280: r0 = "Select Variant"
    //     0xb1b280: add             x0, PP, #0x52, lsl #12  ; [pp+0x52378] "Select Variant"
    //     0xb1b284: ldr             x0, [x0, #0x378]
    // 0xb1b288: stur            x2, [fp, #-0x28]
    // 0xb1b28c: StoreField: r2->field_b = r0
    //     0xb1b28c: stur            w0, [x2, #0xb]
    // 0xb1b290: ldur            x0, [fp, #-0x10]
    // 0xb1b294: StoreField: r2->field_13 = r0
    //     0xb1b294: stur            w0, [x2, #0x13]
    // 0xb1b298: ldur            x0, [fp, #-0x20]
    // 0xb1b29c: LoadField: r1 = r0->field_b
    //     0xb1b29c: ldur            w1, [x0, #0xb]
    // 0xb1b2a0: LoadField: r3 = r0->field_f
    //     0xb1b2a0: ldur            w3, [x0, #0xf]
    // 0xb1b2a4: DecompressPointer r3
    //     0xb1b2a4: add             x3, x3, HEAP, lsl #32
    // 0xb1b2a8: LoadField: r4 = r3->field_b
    //     0xb1b2a8: ldur            w4, [x3, #0xb]
    // 0xb1b2ac: r3 = LoadInt32Instr(r1)
    //     0xb1b2ac: sbfx            x3, x1, #1, #0x1f
    // 0xb1b2b0: stur            x3, [fp, #-0x30]
    // 0xb1b2b4: r1 = LoadInt32Instr(r4)
    //     0xb1b2b4: sbfx            x1, x4, #1, #0x1f
    // 0xb1b2b8: cmp             x3, x1
    // 0xb1b2bc: b.ne            #0xb1b2c8
    // 0xb1b2c0: mov             x1, x0
    // 0xb1b2c4: r0 = _growToNextCapacity()
    //     0xb1b2c4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1b2c8: ldur            x2, [fp, #-0x20]
    // 0xb1b2cc: ldur            x3, [fp, #-0x30]
    // 0xb1b2d0: add             x4, x3, #1
    // 0xb1b2d4: lsl             x0, x4, #1
    // 0xb1b2d8: StoreField: r2->field_b = r0
    //     0xb1b2d8: stur            w0, [x2, #0xb]
    // 0xb1b2dc: LoadField: r5 = r2->field_f
    //     0xb1b2dc: ldur            w5, [x2, #0xf]
    // 0xb1b2e0: DecompressPointer r5
    //     0xb1b2e0: add             x5, x5, HEAP, lsl #32
    // 0xb1b2e4: mov             x1, x5
    // 0xb1b2e8: ldur            x0, [fp, #-0x28]
    // 0xb1b2ec: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1b2ec: add             x25, x1, x3, lsl #2
    //     0xb1b2f0: add             x25, x25, #0xf
    //     0xb1b2f4: str             w0, [x25]
    //     0xb1b2f8: tbz             w0, #0, #0xb1b314
    //     0xb1b2fc: ldurb           w16, [x1, #-1]
    //     0xb1b300: ldurb           w17, [x0, #-1]
    //     0xb1b304: and             x16, x17, x16, lsr #2
    //     0xb1b308: tst             x16, HEAP, lsr #32
    //     0xb1b30c: b.eq            #0xb1b314
    //     0xb1b310: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1b314: mov             x3, x4
    // 0xb1b318: mov             x0, x5
    // 0xb1b31c: stur            x3, [fp, #-0x30]
    // 0xb1b320: LoadField: r1 = r0->field_b
    //     0xb1b320: ldur            w1, [x0, #0xb]
    // 0xb1b324: r0 = LoadInt32Instr(r1)
    //     0xb1b324: sbfx            x0, x1, #1, #0x1f
    // 0xb1b328: cmp             x3, x0
    // 0xb1b32c: b.ne            #0xb1b338
    // 0xb1b330: mov             x1, x2
    // 0xb1b334: r0 = _growToNextCapacity()
    //     0xb1b334: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1b338: ldur            x4, [fp, #-8]
    // 0xb1b33c: ldur            x3, [fp, #-0x20]
    // 0xb1b340: ldur            x2, [fp, #-0x30]
    // 0xb1b344: add             x0, x2, #1
    // 0xb1b348: lsl             x1, x0, #1
    // 0xb1b34c: StoreField: r3->field_b = r1
    //     0xb1b34c: stur            w1, [x3, #0xb]
    // 0xb1b350: mov             x1, x2
    // 0xb1b354: cmp             x1, x0
    // 0xb1b358: b.hs            #0xb1b568
    // 0xb1b35c: LoadField: r0 = r3->field_f
    //     0xb1b35c: ldur            w0, [x3, #0xf]
    // 0xb1b360: DecompressPointer r0
    //     0xb1b360: add             x0, x0, HEAP, lsl #32
    // 0xb1b364: add             x1, x0, x2, lsl #2
    // 0xb1b368: r16 = Instance_SizedBox
    //     0xb1b368: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xb1b36c: ldr             x16, [x16, #0x328]
    // 0xb1b370: StoreField: r1->field_f = r16
    //     0xb1b370: stur            w16, [x1, #0xf]
    // 0xb1b374: LoadField: r0 = r4->field_b
    //     0xb1b374: ldur            w0, [x4, #0xb]
    // 0xb1b378: DecompressPointer r0
    //     0xb1b378: add             x0, x0, HEAP, lsl #32
    // 0xb1b37c: cmp             w0, NULL
    // 0xb1b380: b.eq            #0xb1b56c
    // 0xb1b384: LoadField: r1 = r0->field_b
    //     0xb1b384: ldur            w1, [x0, #0xb]
    // 0xb1b388: DecompressPointer r1
    //     0xb1b388: add             x1, x1, HEAP, lsl #32
    // 0xb1b38c: LoadField: r0 = r1->field_1b
    //     0xb1b38c: ldur            w0, [x1, #0x1b]
    // 0xb1b390: DecompressPointer r0
    //     0xb1b390: add             x0, x0, HEAP, lsl #32
    // 0xb1b394: cmp             w0, NULL
    // 0xb1b398: b.ne            #0xb1b3a4
    // 0xb1b39c: r0 = Null
    //     0xb1b39c: mov             x0, NULL
    // 0xb1b3a0: b               #0xb1b3ac
    // 0xb1b3a4: LoadField: r1 = r0->field_b
    //     0xb1b3a4: ldur            w1, [x0, #0xb]
    // 0xb1b3a8: mov             x0, x1
    // 0xb1b3ac: cmp             w0, NULL
    // 0xb1b3b0: b.ne            #0xb1b3bc
    // 0xb1b3b4: r0 = 0
    //     0xb1b3b4: movz            x0, #0
    // 0xb1b3b8: b               #0xb1b3c4
    // 0xb1b3bc: r1 = LoadInt32Instr(r0)
    //     0xb1b3bc: sbfx            x1, x0, #1, #0x1f
    // 0xb1b3c0: mov             x0, x1
    // 0xb1b3c4: lsl             x4, x0, #1
    // 0xb1b3c8: ldur            x2, [fp, #-0x18]
    // 0xb1b3cc: stur            x4, [fp, #-8]
    // 0xb1b3d0: r1 = Function '<anonymous closure>':.
    //     0xb1b3d0: add             x1, PP, #0x57, lsl #12  ; [pp+0x578a0] AnonymousClosure: (0xb1b594), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/size_picker.dart] _SizePickerState::build (0xb1aea4)
    //     0xb1b3d4: ldr             x1, [x1, #0x8a0]
    // 0xb1b3d8: r0 = AllocateClosure()
    //     0xb1b3d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1b3dc: stur            x0, [fp, #-0x10]
    // 0xb1b3e0: r0 = ListView()
    //     0xb1b3e0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb1b3e4: stur            x0, [fp, #-0x18]
    // 0xb1b3e8: r16 = true
    //     0xb1b3e8: add             x16, NULL, #0x20  ; true
    // 0xb1b3ec: r30 = Instance_Axis
    //     0xb1b3ec: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb1b3f0: stp             lr, x16, [SP]
    // 0xb1b3f4: mov             x1, x0
    // 0xb1b3f8: ldur            x2, [fp, #-0x10]
    // 0xb1b3fc: ldur            x3, [fp, #-8]
    // 0xb1b400: r4 = const [0, 0x5, 0x2, 0x3, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xb1b400: add             x4, PP, #0x37, lsl #12  ; [pp+0x372d0] List(9) [0, 0x5, 0x2, 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb1b404: ldr             x4, [x4, #0x2d0]
    // 0xb1b408: r0 = ListView.builder()
    //     0xb1b408: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb1b40c: r0 = SizedBox()
    //     0xb1b40c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb1b410: mov             x2, x0
    // 0xb1b414: r0 = 55.000000
    //     0xb1b414: add             x0, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xb1b418: ldr             x0, [x0, #0x9b8]
    // 0xb1b41c: stur            x2, [fp, #-8]
    // 0xb1b420: StoreField: r2->field_13 = r0
    //     0xb1b420: stur            w0, [x2, #0x13]
    // 0xb1b424: ldur            x0, [fp, #-0x18]
    // 0xb1b428: StoreField: r2->field_b = r0
    //     0xb1b428: stur            w0, [x2, #0xb]
    // 0xb1b42c: ldur            x0, [fp, #-0x20]
    // 0xb1b430: LoadField: r1 = r0->field_b
    //     0xb1b430: ldur            w1, [x0, #0xb]
    // 0xb1b434: LoadField: r3 = r0->field_f
    //     0xb1b434: ldur            w3, [x0, #0xf]
    // 0xb1b438: DecompressPointer r3
    //     0xb1b438: add             x3, x3, HEAP, lsl #32
    // 0xb1b43c: LoadField: r4 = r3->field_b
    //     0xb1b43c: ldur            w4, [x3, #0xb]
    // 0xb1b440: r3 = LoadInt32Instr(r1)
    //     0xb1b440: sbfx            x3, x1, #1, #0x1f
    // 0xb1b444: stur            x3, [fp, #-0x30]
    // 0xb1b448: r1 = LoadInt32Instr(r4)
    //     0xb1b448: sbfx            x1, x4, #1, #0x1f
    // 0xb1b44c: cmp             x3, x1
    // 0xb1b450: b.ne            #0xb1b45c
    // 0xb1b454: mov             x1, x0
    // 0xb1b458: r0 = _growToNextCapacity()
    //     0xb1b458: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1b45c: ldur            x2, [fp, #-0x20]
    // 0xb1b460: ldur            x3, [fp, #-0x30]
    // 0xb1b464: add             x0, x3, #1
    // 0xb1b468: lsl             x1, x0, #1
    // 0xb1b46c: StoreField: r2->field_b = r1
    //     0xb1b46c: stur            w1, [x2, #0xb]
    // 0xb1b470: LoadField: r1 = r2->field_f
    //     0xb1b470: ldur            w1, [x2, #0xf]
    // 0xb1b474: DecompressPointer r1
    //     0xb1b474: add             x1, x1, HEAP, lsl #32
    // 0xb1b478: ldur            x0, [fp, #-8]
    // 0xb1b47c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1b47c: add             x25, x1, x3, lsl #2
    //     0xb1b480: add             x25, x25, #0xf
    //     0xb1b484: str             w0, [x25]
    //     0xb1b488: tbz             w0, #0, #0xb1b4a4
    //     0xb1b48c: ldurb           w16, [x1, #-1]
    //     0xb1b490: ldurb           w17, [x0, #-1]
    //     0xb1b494: and             x16, x17, x16, lsr #2
    //     0xb1b498: tst             x16, HEAP, lsr #32
    //     0xb1b49c: b.eq            #0xb1b4a4
    //     0xb1b4a0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1b4a4: r0 = Column()
    //     0xb1b4a4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb1b4a8: mov             x1, x0
    // 0xb1b4ac: r0 = Instance_Axis
    //     0xb1b4ac: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb1b4b0: stur            x1, [fp, #-8]
    // 0xb1b4b4: StoreField: r1->field_f = r0
    //     0xb1b4b4: stur            w0, [x1, #0xf]
    // 0xb1b4b8: r0 = Instance_MainAxisAlignment
    //     0xb1b4b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb1b4bc: ldr             x0, [x0, #0xa08]
    // 0xb1b4c0: StoreField: r1->field_13 = r0
    //     0xb1b4c0: stur            w0, [x1, #0x13]
    // 0xb1b4c4: r0 = Instance_MainAxisSize
    //     0xb1b4c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb1b4c8: ldr             x0, [x0, #0xa10]
    // 0xb1b4cc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb1b4cc: stur            w0, [x1, #0x17]
    // 0xb1b4d0: r0 = Instance_CrossAxisAlignment
    //     0xb1b4d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb1b4d4: ldr             x0, [x0, #0x890]
    // 0xb1b4d8: StoreField: r1->field_1b = r0
    //     0xb1b4d8: stur            w0, [x1, #0x1b]
    // 0xb1b4dc: r0 = Instance_VerticalDirection
    //     0xb1b4dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1b4e0: ldr             x0, [x0, #0xa20]
    // 0xb1b4e4: StoreField: r1->field_23 = r0
    //     0xb1b4e4: stur            w0, [x1, #0x23]
    // 0xb1b4e8: r0 = Instance_Clip
    //     0xb1b4e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1b4ec: ldr             x0, [x0, #0x38]
    // 0xb1b4f0: StoreField: r1->field_2b = r0
    //     0xb1b4f0: stur            w0, [x1, #0x2b]
    // 0xb1b4f4: StoreField: r1->field_2f = rZR
    //     0xb1b4f4: stur            xzr, [x1, #0x2f]
    // 0xb1b4f8: ldur            x0, [fp, #-0x20]
    // 0xb1b4fc: StoreField: r1->field_b = r0
    //     0xb1b4fc: stur            w0, [x1, #0xb]
    // 0xb1b500: r0 = Container()
    //     0xb1b500: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1b504: stur            x0, [fp, #-0x10]
    // 0xb1b508: r16 = Instance_Color
    //     0xb1b508: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb1b50c: ldr             x16, [x16, #0x90]
    // 0xb1b510: r30 = inf
    //     0xb1b510: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb1b514: ldr             lr, [lr, #0x9f8]
    // 0xb1b518: stp             lr, x16, [SP, #0x10]
    // 0xb1b51c: r16 = Instance_EdgeInsets
    //     0xb1b51c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xb1b520: ldr             x16, [x16, #0x110]
    // 0xb1b524: ldur            lr, [fp, #-8]
    // 0xb1b528: stp             lr, x16, [SP]
    // 0xb1b52c: mov             x1, x0
    // 0xb1b530: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x1, padding, 0x3, width, 0x2, null]
    //     0xb1b530: add             x4, PP, #0x57, lsl #12  ; [pp+0x578a8] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xb1b534: ldr             x4, [x4, #0x8a8]
    // 0xb1b538: r0 = Container()
    //     0xb1b538: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1b53c: ldur            x0, [fp, #-0x10]
    // 0xb1b540: LeaveFrame
    //     0xb1b540: mov             SP, fp
    //     0xb1b544: ldp             fp, lr, [SP], #0x10
    // 0xb1b548: ret
    //     0xb1b548: ret             
    // 0xb1b54c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1b54c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1b550: b               #0xb1aecc
    // 0xb1b554: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1b554: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1b558: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1b558: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1b55c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1b55c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1b560: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb1b560: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb1b564: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1b564: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1b568: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb1b568: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb1b56c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1b56c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb1b594, size: 0x300
    // 0xb1b594: EnterFrame
    //     0xb1b594: stp             fp, lr, [SP, #-0x10]!
    //     0xb1b598: mov             fp, SP
    // 0xb1b59c: AllocStack(0x48)
    //     0xb1b59c: sub             SP, SP, #0x48
    // 0xb1b5a0: SetupParameters()
    //     0xb1b5a0: ldr             x0, [fp, #0x20]
    //     0xb1b5a4: ldur            w1, [x0, #0x17]
    //     0xb1b5a8: add             x1, x1, HEAP, lsl #32
    //     0xb1b5ac: stur            x1, [fp, #-8]
    // 0xb1b5b0: CheckStackOverflow
    //     0xb1b5b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1b5b4: cmp             SP, x16
    //     0xb1b5b8: b.ls            #0xb1b880
    // 0xb1b5bc: r1 = 2
    //     0xb1b5bc: movz            x1, #0x2
    // 0xb1b5c0: r0 = AllocateContext()
    //     0xb1b5c0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb1b5c4: mov             x3, x0
    // 0xb1b5c8: ldur            x2, [fp, #-8]
    // 0xb1b5cc: stur            x3, [fp, #-0x18]
    // 0xb1b5d0: StoreField: r3->field_b = r2
    //     0xb1b5d0: stur            w2, [x3, #0xb]
    // 0xb1b5d4: ldr             x0, [fp, #0x10]
    // 0xb1b5d8: StoreField: r3->field_f = r0
    //     0xb1b5d8: stur            w0, [x3, #0xf]
    // 0xb1b5dc: LoadField: r1 = r2->field_f
    //     0xb1b5dc: ldur            w1, [x2, #0xf]
    // 0xb1b5e0: DecompressPointer r1
    //     0xb1b5e0: add             x1, x1, HEAP, lsl #32
    // 0xb1b5e4: LoadField: r4 = r1->field_b
    //     0xb1b5e4: ldur            w4, [x1, #0xb]
    // 0xb1b5e8: DecompressPointer r4
    //     0xb1b5e8: add             x4, x4, HEAP, lsl #32
    // 0xb1b5ec: cmp             w4, NULL
    // 0xb1b5f0: b.eq            #0xb1b888
    // 0xb1b5f4: LoadField: r1 = r4->field_b
    //     0xb1b5f4: ldur            w1, [x4, #0xb]
    // 0xb1b5f8: DecompressPointer r1
    //     0xb1b5f8: add             x1, x1, HEAP, lsl #32
    // 0xb1b5fc: LoadField: r4 = r1->field_1b
    //     0xb1b5fc: ldur            w4, [x1, #0x1b]
    // 0xb1b600: DecompressPointer r4
    //     0xb1b600: add             x4, x4, HEAP, lsl #32
    // 0xb1b604: cmp             w4, NULL
    // 0xb1b608: b.eq            #0xb1b88c
    // 0xb1b60c: LoadField: r1 = r4->field_b
    //     0xb1b60c: ldur            w1, [x4, #0xb]
    // 0xb1b610: r5 = LoadInt32Instr(r0)
    //     0xb1b610: sbfx            x5, x0, #1, #0x1f
    //     0xb1b614: tbz             w0, #0, #0xb1b61c
    //     0xb1b618: ldur            x5, [x0, #7]
    // 0xb1b61c: r0 = LoadInt32Instr(r1)
    //     0xb1b61c: sbfx            x0, x1, #1, #0x1f
    // 0xb1b620: mov             x1, x5
    // 0xb1b624: cmp             x1, x0
    // 0xb1b628: b.hs            #0xb1b890
    // 0xb1b62c: LoadField: r0 = r4->field_f
    //     0xb1b62c: ldur            w0, [x4, #0xf]
    // 0xb1b630: DecompressPointer r0
    //     0xb1b630: add             x0, x0, HEAP, lsl #32
    // 0xb1b634: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb1b634: add             x16, x0, x5, lsl #2
    //     0xb1b638: ldur            w1, [x16, #0xf]
    // 0xb1b63c: DecompressPointer r1
    //     0xb1b63c: add             x1, x1, HEAP, lsl #32
    // 0xb1b640: stur            x1, [fp, #-0x10]
    // 0xb1b644: StoreField: r3->field_13 = r1
    //     0xb1b644: stur            w1, [x3, #0x13]
    // 0xb1b648: LoadField: r0 = r2->field_13
    //     0xb1b648: ldur            w0, [x2, #0x13]
    // 0xb1b64c: DecompressPointer r0
    //     0xb1b64c: add             x0, x0, HEAP, lsl #32
    // 0xb1b650: LoadField: r4 = r1->field_33
    //     0xb1b650: ldur            w4, [x1, #0x33]
    // 0xb1b654: DecompressPointer r4
    //     0xb1b654: add             x4, x4, HEAP, lsl #32
    // 0xb1b658: r5 = LoadClassIdInstr(r0)
    //     0xb1b658: ldur            x5, [x0, #-1]
    //     0xb1b65c: ubfx            x5, x5, #0xc, #0x14
    // 0xb1b660: stp             x4, x0, [SP]
    // 0xb1b664: mov             x0, x5
    // 0xb1b668: mov             lr, x0
    // 0xb1b66c: ldr             lr, [x21, lr, lsl #3]
    // 0xb1b670: blr             lr
    // 0xb1b674: tbnz            w0, #4, #0xb1b680
    // 0xb1b678: r1 = Instance_Color
    //     0xb1b678: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1b67c: b               #0xb1b684
    // 0xb1b680: r1 = Instance_Color
    //     0xb1b680: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb1b684: ldur            x0, [fp, #-0x10]
    // 0xb1b688: stur            x1, [fp, #-0x20]
    // 0xb1b68c: r0 = Radius()
    //     0xb1b68c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb1b690: d0 = 10.000000
    //     0xb1b690: fmov            d0, #10.00000000
    // 0xb1b694: stur            x0, [fp, #-0x28]
    // 0xb1b698: StoreField: r0->field_7 = d0
    //     0xb1b698: stur            d0, [x0, #7]
    // 0xb1b69c: StoreField: r0->field_f = d0
    //     0xb1b69c: stur            d0, [x0, #0xf]
    // 0xb1b6a0: r0 = BorderRadius()
    //     0xb1b6a0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb1b6a4: mov             x1, x0
    // 0xb1b6a8: ldur            x0, [fp, #-0x28]
    // 0xb1b6ac: stur            x1, [fp, #-0x30]
    // 0xb1b6b0: StoreField: r1->field_7 = r0
    //     0xb1b6b0: stur            w0, [x1, #7]
    // 0xb1b6b4: StoreField: r1->field_b = r0
    //     0xb1b6b4: stur            w0, [x1, #0xb]
    // 0xb1b6b8: StoreField: r1->field_f = r0
    //     0xb1b6b8: stur            w0, [x1, #0xf]
    // 0xb1b6bc: StoreField: r1->field_13 = r0
    //     0xb1b6bc: stur            w0, [x1, #0x13]
    // 0xb1b6c0: r0 = BoxDecoration()
    //     0xb1b6c0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb1b6c4: mov             x1, x0
    // 0xb1b6c8: ldur            x0, [fp, #-0x20]
    // 0xb1b6cc: stur            x1, [fp, #-0x28]
    // 0xb1b6d0: StoreField: r1->field_7 = r0
    //     0xb1b6d0: stur            w0, [x1, #7]
    // 0xb1b6d4: ldur            x0, [fp, #-0x30]
    // 0xb1b6d8: StoreField: r1->field_13 = r0
    //     0xb1b6d8: stur            w0, [x1, #0x13]
    // 0xb1b6dc: r2 = Instance_BoxShape
    //     0xb1b6dc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb1b6e0: ldr             x2, [x2, #0x80]
    // 0xb1b6e4: StoreField: r1->field_23 = r2
    //     0xb1b6e4: stur            w2, [x1, #0x23]
    // 0xb1b6e8: ldur            x0, [fp, #-0x10]
    // 0xb1b6ec: LoadField: r3 = r0->field_33
    //     0xb1b6ec: ldur            w3, [x0, #0x33]
    // 0xb1b6f0: DecompressPointer r3
    //     0xb1b6f0: add             x3, x3, HEAP, lsl #32
    // 0xb1b6f4: cmp             w3, NULL
    // 0xb1b6f8: b.ne            #0xb1b704
    // 0xb1b6fc: r4 = ""
    //     0xb1b6fc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb1b700: b               #0xb1b708
    // 0xb1b704: mov             x4, x3
    // 0xb1b708: ldur            x0, [fp, #-8]
    // 0xb1b70c: stur            x4, [fp, #-0x10]
    // 0xb1b710: LoadField: r5 = r0->field_13
    //     0xb1b710: ldur            w5, [x0, #0x13]
    // 0xb1b714: DecompressPointer r5
    //     0xb1b714: add             x5, x5, HEAP, lsl #32
    // 0xb1b718: r0 = LoadClassIdInstr(r5)
    //     0xb1b718: ldur            x0, [x5, #-1]
    //     0xb1b71c: ubfx            x0, x0, #0xc, #0x14
    // 0xb1b720: stp             x3, x5, [SP]
    // 0xb1b724: mov             lr, x0
    // 0xb1b728: ldr             lr, [x21, lr, lsl #3]
    // 0xb1b72c: blr             lr
    // 0xb1b730: tbnz            w0, #4, #0xb1b73c
    // 0xb1b734: r1 = Instance_Color
    //     0xb1b734: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb1b738: b               #0xb1b768
    // 0xb1b73c: ldr             x1, [fp, #0x18]
    // 0xb1b740: r0 = of()
    //     0xb1b740: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1b744: LoadField: r1 = r0->field_5b
    //     0xb1b744: ldur            w1, [x0, #0x5b]
    // 0xb1b748: DecompressPointer r1
    //     0xb1b748: add             x1, x1, HEAP, lsl #32
    // 0xb1b74c: r0 = LoadClassIdInstr(r1)
    //     0xb1b74c: ldur            x0, [x1, #-1]
    //     0xb1b750: ubfx            x0, x0, #0xc, #0x14
    // 0xb1b754: d0 = 0.400000
    //     0xb1b754: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb1b758: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb1b758: sub             lr, x0, #0xffa
    //     0xb1b75c: ldr             lr, [x21, lr, lsl #3]
    //     0xb1b760: blr             lr
    // 0xb1b764: mov             x1, x0
    // 0xb1b768: ldur            x0, [fp, #-0x10]
    // 0xb1b76c: stur            x1, [fp, #-8]
    // 0xb1b770: r0 = TextStyle()
    //     0xb1b770: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xb1b774: mov             x1, x0
    // 0xb1b778: r0 = true
    //     0xb1b778: add             x0, NULL, #0x20  ; true
    // 0xb1b77c: stur            x1, [fp, #-0x20]
    // 0xb1b780: StoreField: r1->field_7 = r0
    //     0xb1b780: stur            w0, [x1, #7]
    // 0xb1b784: ldur            x2, [fp, #-8]
    // 0xb1b788: StoreField: r1->field_b = r2
    //     0xb1b788: stur            w2, [x1, #0xb]
    // 0xb1b78c: r2 = 16.000000
    //     0xb1b78c: add             x2, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb1b790: ldr             x2, [x2, #0x188]
    // 0xb1b794: StoreField: r1->field_1f = r2
    //     0xb1b794: stur            w2, [x1, #0x1f]
    // 0xb1b798: r0 = Text()
    //     0xb1b798: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1b79c: mov             x1, x0
    // 0xb1b7a0: ldur            x0, [fp, #-0x10]
    // 0xb1b7a4: stur            x1, [fp, #-8]
    // 0xb1b7a8: StoreField: r1->field_b = r0
    //     0xb1b7a8: stur            w0, [x1, #0xb]
    // 0xb1b7ac: ldur            x0, [fp, #-0x20]
    // 0xb1b7b0: StoreField: r1->field_13 = r0
    //     0xb1b7b0: stur            w0, [x1, #0x13]
    // 0xb1b7b4: r0 = Center()
    //     0xb1b7b4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb1b7b8: mov             x1, x0
    // 0xb1b7bc: r0 = Instance_Alignment
    //     0xb1b7bc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb1b7c0: ldr             x0, [x0, #0xb10]
    // 0xb1b7c4: stur            x1, [fp, #-0x10]
    // 0xb1b7c8: StoreField: r1->field_f = r0
    //     0xb1b7c8: stur            w0, [x1, #0xf]
    // 0xb1b7cc: ldur            x0, [fp, #-8]
    // 0xb1b7d0: StoreField: r1->field_b = r0
    //     0xb1b7d0: stur            w0, [x1, #0xb]
    // 0xb1b7d4: r0 = Container()
    //     0xb1b7d4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1b7d8: stur            x0, [fp, #-8]
    // 0xb1b7dc: r16 = Instance_EdgeInsets
    //     0xb1b7dc: add             x16, PP, #0x57, lsl #12  ; [pp+0x578b0] Obj!EdgeInsets@d58a91
    //     0xb1b7e0: ldr             x16, [x16, #0x8b0]
    // 0xb1b7e4: ldur            lr, [fp, #-0x28]
    // 0xb1b7e8: stp             lr, x16, [SP, #8]
    // 0xb1b7ec: ldur            x16, [fp, #-0x10]
    // 0xb1b7f0: str             x16, [SP]
    // 0xb1b7f4: mov             x1, x0
    // 0xb1b7f8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xb1b7f8: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xb1b7fc: ldr             x4, [x4, #0x610]
    // 0xb1b800: r0 = Container()
    //     0xb1b800: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1b804: r0 = InkWell()
    //     0xb1b804: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb1b808: mov             x3, x0
    // 0xb1b80c: ldur            x0, [fp, #-8]
    // 0xb1b810: stur            x3, [fp, #-0x10]
    // 0xb1b814: StoreField: r3->field_b = r0
    //     0xb1b814: stur            w0, [x3, #0xb]
    // 0xb1b818: ldur            x2, [fp, #-0x18]
    // 0xb1b81c: r1 = Function '<anonymous closure>':.
    //     0xb1b81c: add             x1, PP, #0x57, lsl #12  ; [pp+0x578b8] AnonymousClosure: (0xb1b894), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/size_picker.dart] _SizePickerState::build (0xb1aea4)
    //     0xb1b820: ldr             x1, [x1, #0x8b8]
    // 0xb1b824: r0 = AllocateClosure()
    //     0xb1b824: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1b828: mov             x1, x0
    // 0xb1b82c: ldur            x0, [fp, #-0x10]
    // 0xb1b830: StoreField: r0->field_f = r1
    //     0xb1b830: stur            w1, [x0, #0xf]
    // 0xb1b834: r1 = true
    //     0xb1b834: add             x1, NULL, #0x20  ; true
    // 0xb1b838: StoreField: r0->field_43 = r1
    //     0xb1b838: stur            w1, [x0, #0x43]
    // 0xb1b83c: r2 = Instance_BoxShape
    //     0xb1b83c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb1b840: ldr             x2, [x2, #0x80]
    // 0xb1b844: StoreField: r0->field_47 = r2
    //     0xb1b844: stur            w2, [x0, #0x47]
    // 0xb1b848: StoreField: r0->field_6f = r1
    //     0xb1b848: stur            w1, [x0, #0x6f]
    // 0xb1b84c: r2 = false
    //     0xb1b84c: add             x2, NULL, #0x30  ; false
    // 0xb1b850: StoreField: r0->field_73 = r2
    //     0xb1b850: stur            w2, [x0, #0x73]
    // 0xb1b854: StoreField: r0->field_83 = r1
    //     0xb1b854: stur            w1, [x0, #0x83]
    // 0xb1b858: StoreField: r0->field_7b = r2
    //     0xb1b858: stur            w2, [x0, #0x7b]
    // 0xb1b85c: r0 = Padding()
    //     0xb1b85c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1b860: r1 = Instance_EdgeInsets
    //     0xb1b860: add             x1, PP, #0x57, lsl #12  ; [pp+0x578c0] Obj!EdgeInsets@d591b1
    //     0xb1b864: ldr             x1, [x1, #0x8c0]
    // 0xb1b868: StoreField: r0->field_f = r1
    //     0xb1b868: stur            w1, [x0, #0xf]
    // 0xb1b86c: ldur            x1, [fp, #-0x10]
    // 0xb1b870: StoreField: r0->field_b = r1
    //     0xb1b870: stur            w1, [x0, #0xb]
    // 0xb1b874: LeaveFrame
    //     0xb1b874: mov             SP, fp
    //     0xb1b878: ldp             fp, lr, [SP], #0x10
    // 0xb1b87c: ret
    //     0xb1b87c: ret             
    // 0xb1b880: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1b880: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1b884: b               #0xb1b5bc
    // 0xb1b888: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1b888: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1b88c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1b88c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1b890: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb1b890: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb1b894, size: 0x68
    // 0xb1b894: EnterFrame
    //     0xb1b894: stp             fp, lr, [SP, #-0x10]!
    //     0xb1b898: mov             fp, SP
    // 0xb1b89c: AllocStack(0x8)
    //     0xb1b89c: sub             SP, SP, #8
    // 0xb1b8a0: SetupParameters()
    //     0xb1b8a0: ldr             x0, [fp, #0x10]
    //     0xb1b8a4: ldur            w2, [x0, #0x17]
    //     0xb1b8a8: add             x2, x2, HEAP, lsl #32
    // 0xb1b8ac: CheckStackOverflow
    //     0xb1b8ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1b8b0: cmp             SP, x16
    //     0xb1b8b4: b.ls            #0xb1b8f4
    // 0xb1b8b8: LoadField: r0 = r2->field_b
    //     0xb1b8b8: ldur            w0, [x2, #0xb]
    // 0xb1b8bc: DecompressPointer r0
    //     0xb1b8bc: add             x0, x0, HEAP, lsl #32
    // 0xb1b8c0: LoadField: r3 = r0->field_f
    //     0xb1b8c0: ldur            w3, [x0, #0xf]
    // 0xb1b8c4: DecompressPointer r3
    //     0xb1b8c4: add             x3, x3, HEAP, lsl #32
    // 0xb1b8c8: stur            x3, [fp, #-8]
    // 0xb1b8cc: r1 = Function '<anonymous closure>':.
    //     0xb1b8cc: add             x1, PP, #0x57, lsl #12  ; [pp+0x578c8] AnonymousClosure: (0xb1b8fc), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/size_picker.dart] _SizePickerState::build (0xb1aea4)
    //     0xb1b8d0: ldr             x1, [x1, #0x8c8]
    // 0xb1b8d4: r0 = AllocateClosure()
    //     0xb1b8d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1b8d8: ldur            x1, [fp, #-8]
    // 0xb1b8dc: mov             x2, x0
    // 0xb1b8e0: r0 = setState()
    //     0xb1b8e0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb1b8e4: r0 = Null
    //     0xb1b8e4: mov             x0, NULL
    // 0xb1b8e8: LeaveFrame
    //     0xb1b8e8: mov             SP, fp
    //     0xb1b8ec: ldp             fp, lr, [SP], #0x10
    // 0xb1b8f0: ret
    //     0xb1b8f0: ret             
    // 0xb1b8f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1b8f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1b8f8: b               #0xb1b8b8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb1b8fc, size: 0x118
    // 0xb1b8fc: EnterFrame
    //     0xb1b8fc: stp             fp, lr, [SP, #-0x10]!
    //     0xb1b900: mov             fp, SP
    // 0xb1b904: AllocStack(0x10)
    //     0xb1b904: sub             SP, SP, #0x10
    // 0xb1b908: SetupParameters()
    //     0xb1b908: ldr             x0, [fp, #0x10]
    //     0xb1b90c: ldur            w1, [x0, #0x17]
    //     0xb1b910: add             x1, x1, HEAP, lsl #32
    // 0xb1b914: CheckStackOverflow
    //     0xb1b914: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1b918: cmp             SP, x16
    //     0xb1b91c: b.ls            #0xb1ba08
    // 0xb1b920: LoadField: r2 = r1->field_13
    //     0xb1b920: ldur            w2, [x1, #0x13]
    // 0xb1b924: DecompressPointer r2
    //     0xb1b924: add             x2, x2, HEAP, lsl #32
    // 0xb1b928: LoadField: r0 = r2->field_33
    //     0xb1b928: ldur            w0, [x2, #0x33]
    // 0xb1b92c: DecompressPointer r0
    //     0xb1b92c: add             x0, x0, HEAP, lsl #32
    // 0xb1b930: cmp             w0, NULL
    // 0xb1b934: b.ne            #0xb1b93c
    // 0xb1b938: r0 = ""
    //     0xb1b938: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb1b93c: LoadField: r3 = r1->field_b
    //     0xb1b93c: ldur            w3, [x1, #0xb]
    // 0xb1b940: DecompressPointer r3
    //     0xb1b940: add             x3, x3, HEAP, lsl #32
    // 0xb1b944: StoreField: r3->field_13 = r0
    //     0xb1b944: stur            w0, [x3, #0x13]
    //     0xb1b948: ldurb           w16, [x3, #-1]
    //     0xb1b94c: ldurb           w17, [x0, #-1]
    //     0xb1b950: and             x16, x17, x16, lsr #2
    //     0xb1b954: tst             x16, HEAP, lsr #32
    //     0xb1b958: b.eq            #0xb1b960
    //     0xb1b95c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xb1b960: LoadField: r4 = r3->field_f
    //     0xb1b960: ldur            w4, [x3, #0xf]
    // 0xb1b964: DecompressPointer r4
    //     0xb1b964: add             x4, x4, HEAP, lsl #32
    // 0xb1b968: mov             x0, x2
    // 0xb1b96c: StoreField: r4->field_13 = r0
    //     0xb1b96c: stur            w0, [x4, #0x13]
    //     0xb1b970: ldurb           w16, [x4, #-1]
    //     0xb1b974: ldurb           w17, [x0, #-1]
    //     0xb1b978: and             x16, x17, x16, lsr #2
    //     0xb1b97c: tst             x16, HEAP, lsr #32
    //     0xb1b980: b.eq            #0xb1b988
    //     0xb1b984: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0xb1b988: LoadField: r0 = r1->field_f
    //     0xb1b988: ldur            w0, [x1, #0xf]
    // 0xb1b98c: DecompressPointer r0
    //     0xb1b98c: add             x0, x0, HEAP, lsl #32
    // 0xb1b990: ArrayStore: r4[0] = r0  ; List_4
    //     0xb1b990: stur            w0, [x4, #0x17]
    //     0xb1b994: tbz             w0, #0, #0xb1b9b0
    //     0xb1b998: ldurb           w16, [x4, #-1]
    //     0xb1b99c: ldurb           w17, [x0, #-1]
    //     0xb1b9a0: and             x16, x17, x16, lsr #2
    //     0xb1b9a4: tst             x16, HEAP, lsr #32
    //     0xb1b9a8: b.eq            #0xb1b9b0
    //     0xb1b9ac: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0xb1b9b0: LoadField: r0 = r4->field_b
    //     0xb1b9b0: ldur            w0, [x4, #0xb]
    // 0xb1b9b4: DecompressPointer r0
    //     0xb1b9b4: add             x0, x0, HEAP, lsl #32
    // 0xb1b9b8: cmp             w0, NULL
    // 0xb1b9bc: b.eq            #0xb1ba10
    // 0xb1b9c0: LoadField: r1 = r2->field_37
    //     0xb1b9c0: ldur            w1, [x2, #0x37]
    // 0xb1b9c4: DecompressPointer r1
    //     0xb1b9c4: add             x1, x1, HEAP, lsl #32
    // 0xb1b9c8: cmp             w1, NULL
    // 0xb1b9cc: b.ne            #0xb1b9d4
    // 0xb1b9d0: r1 = ""
    //     0xb1b9d0: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb1b9d4: LoadField: r2 = r0->field_f
    //     0xb1b9d4: ldur            w2, [x0, #0xf]
    // 0xb1b9d8: DecompressPointer r2
    //     0xb1b9d8: add             x2, x2, HEAP, lsl #32
    // 0xb1b9dc: stp             x1, x2, [SP]
    // 0xb1b9e0: r4 = 0
    //     0xb1b9e0: movz            x4, #0
    // 0xb1b9e4: ldr             x0, [SP, #8]
    // 0xb1b9e8: r16 = UnlinkedCall_0x613b5c
    //     0xb1b9e8: add             x16, PP, #0x57, lsl #12  ; [pp+0x578d0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1b9ec: add             x16, x16, #0x8d0
    // 0xb1b9f0: ldp             x5, lr, [x16]
    // 0xb1b9f4: blr             lr
    // 0xb1b9f8: r0 = Null
    //     0xb1b9f8: mov             x0, NULL
    // 0xb1b9fc: LeaveFrame
    //     0xb1b9fc: mov             SP, fp
    //     0xb1ba00: ldp             fp, lr, [SP], #0x10
    // 0xb1ba04: ret
    //     0xb1ba04: ret             
    // 0xb1ba08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1ba08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1ba0c: b               #0xb1b920
    // 0xb1ba10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ba10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4130, size: 0x1c, field offset: 0xc
//   const constructor, 
class SizePicker extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e324, size: 0x28
    // 0xc7e324: EnterFrame
    //     0xc7e324: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e328: mov             fp, SP
    // 0xc7e32c: mov             x0, x1
    // 0xc7e330: r1 = <SizePicker>
    //     0xc7e330: add             x1, PP, #0x48, lsl #12  ; [pp+0x48ae0] TypeArguments: <SizePicker>
    //     0xc7e334: ldr             x1, [x1, #0xae0]
    // 0xc7e338: r0 = _SizePickerState()
    //     0xc7e338: bl              #0xc7e34c  ; Allocate_SizePickerStateStub -> _SizePickerState (size=0x1c)
    // 0xc7e33c: ArrayStore: r0[0] = rZR  ; List_4
    //     0xc7e33c: stur            wzr, [x0, #0x17]
    // 0xc7e340: LeaveFrame
    //     0xc7e340: mov             SP, fp
    //     0xc7e344: ldp             fp, lr, [SP], #0x10
    // 0xc7e348: ret
    //     0xc7e348: ret             
  }
}
