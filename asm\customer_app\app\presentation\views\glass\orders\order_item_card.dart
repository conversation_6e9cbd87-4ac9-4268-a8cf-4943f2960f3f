// lib: , url: package:customer_app/app/presentation/views/glass/orders/order_item_card.dart

// class id: 1049415, size: 0x8
class :: {
}

// class id: 3324, size: 0x14, field offset: 0x14
class _OrderItemCardState extends State<dynamic> {

  [closure] Divider <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x997c00, size: 0x48
    // 0x997c00: EnterFrame
    //     0x997c00: stp             fp, lr, [SP, #-0x10]!
    //     0x997c04: mov             fp, SP
    // 0x997c08: AllocStack(0x8)
    //     0x997c08: sub             SP, SP, #8
    // 0x997c0c: CheckStackOverflow
    //     0x997c0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x997c10: cmp             SP, x16
    //     0x997c14: b.ls            #0x997c40
    // 0x997c18: r1 = Instance_Color
    //     0x997c18: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x997c1c: d0 = 0.100000
    //     0x997c1c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x997c20: r0 = withOpacity()
    //     0x997c20: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x997c24: stur            x0, [fp, #-8]
    // 0x997c28: r0 = Divider()
    //     0x997c28: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x997c2c: ldur            x1, [fp, #-8]
    // 0x997c30: StoreField: r0->field_1f = r1
    //     0x997c30: stur            w1, [x0, #0x1f]
    // 0x997c34: LeaveFrame
    //     0x997c34: mov             SP, fp
    //     0x997c38: ldp             fp, lr, [SP], #0x10
    // 0x997c3c: ret
    //     0x997c3c: ret             
    // 0x997c40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x997c40: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x997c44: b               #0x997c18
  }
  _ build(/* No info */) {
    // ** addr: 0xb78668, size: 0x324
    // 0xb78668: EnterFrame
    //     0xb78668: stp             fp, lr, [SP, #-0x10]!
    //     0xb7866c: mov             fp, SP
    // 0xb78670: AllocStack(0x48)
    //     0xb78670: sub             SP, SP, #0x48
    // 0xb78674: SetupParameters(_OrderItemCardState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb78674: mov             x0, x1
    //     0xb78678: stur            x1, [fp, #-8]
    //     0xb7867c: mov             x1, x2
    //     0xb78680: stur            x2, [fp, #-0x10]
    // 0xb78684: CheckStackOverflow
    //     0xb78684: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb78688: cmp             SP, x16
    //     0xb7868c: b.ls            #0xb7897c
    // 0xb78690: r1 = 1
    //     0xb78690: movz            x1, #0x1
    // 0xb78694: r0 = AllocateContext()
    //     0xb78694: bl              #0x16f6108  ; AllocateContextStub
    // 0xb78698: mov             x3, x0
    // 0xb7869c: ldur            x0, [fp, #-8]
    // 0xb786a0: stur            x3, [fp, #-0x18]
    // 0xb786a4: StoreField: r3->field_f = r0
    //     0xb786a4: stur            w0, [x3, #0xf]
    // 0xb786a8: r1 = Null
    //     0xb786a8: mov             x1, NULL
    // 0xb786ac: r2 = 4
    //     0xb786ac: movz            x2, #0x4
    // 0xb786b0: r0 = AllocateArray()
    //     0xb786b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb786b4: r16 = "Ordered on "
    //     0xb786b4: add             x16, PP, #0x53, lsl #12  ; [pp+0x533e8] "Ordered on "
    //     0xb786b8: ldr             x16, [x16, #0x3e8]
    // 0xb786bc: StoreField: r0->field_f = r16
    //     0xb786bc: stur            w16, [x0, #0xf]
    // 0xb786c0: ldur            x1, [fp, #-8]
    // 0xb786c4: LoadField: r2 = r1->field_b
    //     0xb786c4: ldur            w2, [x1, #0xb]
    // 0xb786c8: DecompressPointer r2
    //     0xb786c8: add             x2, x2, HEAP, lsl #32
    // 0xb786cc: cmp             w2, NULL
    // 0xb786d0: b.eq            #0xb78984
    // 0xb786d4: LoadField: r3 = r2->field_b
    //     0xb786d4: ldur            w3, [x2, #0xb]
    // 0xb786d8: DecompressPointer r3
    //     0xb786d8: add             x3, x3, HEAP, lsl #32
    // 0xb786dc: LoadField: r2 = r3->field_7
    //     0xb786dc: ldur            w2, [x3, #7]
    // 0xb786e0: DecompressPointer r2
    //     0xb786e0: add             x2, x2, HEAP, lsl #32
    // 0xb786e4: StoreField: r0->field_13 = r2
    //     0xb786e4: stur            w2, [x0, #0x13]
    // 0xb786e8: str             x0, [SP]
    // 0xb786ec: r0 = _interpolate()
    //     0xb786ec: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb786f0: ldur            x1, [fp, #-0x10]
    // 0xb786f4: stur            x0, [fp, #-0x10]
    // 0xb786f8: r0 = of()
    //     0xb786f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb786fc: LoadField: r1 = r0->field_87
    //     0xb786fc: ldur            w1, [x0, #0x87]
    // 0xb78700: DecompressPointer r1
    //     0xb78700: add             x1, x1, HEAP, lsl #32
    // 0xb78704: LoadField: r0 = r1->field_2b
    //     0xb78704: ldur            w0, [x1, #0x2b]
    // 0xb78708: DecompressPointer r0
    //     0xb78708: add             x0, x0, HEAP, lsl #32
    // 0xb7870c: r16 = 12.000000
    //     0xb7870c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb78710: ldr             x16, [x16, #0x9e8]
    // 0xb78714: r30 = Instance_Color
    //     0xb78714: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb78718: stp             lr, x16, [SP]
    // 0xb7871c: mov             x1, x0
    // 0xb78720: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb78720: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb78724: ldr             x4, [x4, #0xaa0]
    // 0xb78728: r0 = copyWith()
    //     0xb78728: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7872c: stur            x0, [fp, #-0x20]
    // 0xb78730: r0 = Text()
    //     0xb78730: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb78734: mov             x1, x0
    // 0xb78738: ldur            x0, [fp, #-0x10]
    // 0xb7873c: stur            x1, [fp, #-0x28]
    // 0xb78740: StoreField: r1->field_b = r0
    //     0xb78740: stur            w0, [x1, #0xb]
    // 0xb78744: ldur            x0, [fp, #-0x20]
    // 0xb78748: StoreField: r1->field_13 = r0
    //     0xb78748: stur            w0, [x1, #0x13]
    // 0xb7874c: r0 = Align()
    //     0xb7874c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb78750: mov             x1, x0
    // 0xb78754: r0 = Instance_Alignment
    //     0xb78754: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb78758: ldr             x0, [x0, #0xfa0]
    // 0xb7875c: stur            x1, [fp, #-0x10]
    // 0xb78760: StoreField: r1->field_f = r0
    //     0xb78760: stur            w0, [x1, #0xf]
    // 0xb78764: ldur            x0, [fp, #-0x28]
    // 0xb78768: StoreField: r1->field_b = r0
    //     0xb78768: stur            w0, [x1, #0xb]
    // 0xb7876c: r0 = Padding()
    //     0xb7876c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb78770: mov             x3, x0
    // 0xb78774: r0 = Instance_EdgeInsets
    //     0xb78774: add             x0, PP, #0x48, lsl #12  ; [pp+0x48888] Obj!EdgeInsets@d59361
    //     0xb78778: ldr             x0, [x0, #0x888]
    // 0xb7877c: stur            x3, [fp, #-0x20]
    // 0xb78780: StoreField: r3->field_f = r0
    //     0xb78780: stur            w0, [x3, #0xf]
    // 0xb78784: ldur            x0, [fp, #-0x10]
    // 0xb78788: StoreField: r3->field_b = r0
    //     0xb78788: stur            w0, [x3, #0xb]
    // 0xb7878c: ldur            x0, [fp, #-8]
    // 0xb78790: LoadField: r1 = r0->field_b
    //     0xb78790: ldur            w1, [x0, #0xb]
    // 0xb78794: DecompressPointer r1
    //     0xb78794: add             x1, x1, HEAP, lsl #32
    // 0xb78798: cmp             w1, NULL
    // 0xb7879c: b.eq            #0xb78988
    // 0xb787a0: LoadField: r0 = r1->field_b
    //     0xb787a0: ldur            w0, [x1, #0xb]
    // 0xb787a4: DecompressPointer r0
    //     0xb787a4: add             x0, x0, HEAP, lsl #32
    // 0xb787a8: LoadField: r1 = r0->field_b
    //     0xb787a8: ldur            w1, [x0, #0xb]
    // 0xb787ac: DecompressPointer r1
    //     0xb787ac: add             x1, x1, HEAP, lsl #32
    // 0xb787b0: cmp             w1, NULL
    // 0xb787b4: b.ne            #0xb787c0
    // 0xb787b8: r0 = Null
    //     0xb787b8: mov             x0, NULL
    // 0xb787bc: b               #0xb787c4
    // 0xb787c0: LoadField: r0 = r1->field_b
    //     0xb787c0: ldur            w0, [x1, #0xb]
    // 0xb787c4: cmp             w0, NULL
    // 0xb787c8: b.ne            #0xb787d4
    // 0xb787cc: r0 = 0
    //     0xb787cc: movz            x0, #0
    // 0xb787d0: b               #0xb787dc
    // 0xb787d4: r1 = LoadInt32Instr(r0)
    //     0xb787d4: sbfx            x1, x0, #1, #0x1f
    // 0xb787d8: mov             x0, x1
    // 0xb787dc: ldur            x2, [fp, #-0x18]
    // 0xb787e0: stur            x0, [fp, #-0x30]
    // 0xb787e4: r1 = Function '<anonymous closure>':.
    //     0xb787e4: add             x1, PP, #0x55, lsl #12  ; [pp+0x55a60] AnonymousClosure: (0xb7898c), in [package:customer_app/app/presentation/views/glass/orders/order_item_card.dart] _OrderItemCardState::build (0xb78668)
    //     0xb787e8: ldr             x1, [x1, #0xa60]
    // 0xb787ec: r0 = AllocateClosure()
    //     0xb787ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb787f0: r1 = Function '<anonymous closure>':.
    //     0xb787f0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55a68] AnonymousClosure: (0x997c00), in [package:customer_app/app/presentation/views/glass/orders/order_item_card.dart] _OrderItemCardState::build (0xb78668)
    //     0xb787f4: ldr             x1, [x1, #0xa68]
    // 0xb787f8: r2 = Null
    //     0xb787f8: mov             x2, NULL
    // 0xb787fc: stur            x0, [fp, #-8]
    // 0xb78800: r0 = AllocateClosure()
    //     0xb78800: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb78804: stur            x0, [fp, #-0x10]
    // 0xb78808: r0 = ListView()
    //     0xb78808: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb7880c: stur            x0, [fp, #-0x18]
    // 0xb78810: r16 = Instance_Axis
    //     0xb78810: ldr             x16, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb78814: r30 = true
    //     0xb78814: add             lr, NULL, #0x20  ; true
    // 0xb78818: stp             lr, x16, [SP, #8]
    // 0xb7881c: r16 = false
    //     0xb7881c: add             x16, NULL, #0x30  ; false
    // 0xb78820: str             x16, [SP]
    // 0xb78824: mov             x1, x0
    // 0xb78828: ldur            x2, [fp, #-8]
    // 0xb7882c: ldur            x3, [fp, #-0x30]
    // 0xb78830: ldur            x5, [fp, #-0x10]
    // 0xb78834: r4 = const [0, 0x7, 0x3, 0x4, primary, 0x6, scrollDirection, 0x4, shrinkWrap, 0x5, null]
    //     0xb78834: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e528] List(11) [0, 0x7, 0x3, 0x4, "primary", 0x6, "scrollDirection", 0x4, "shrinkWrap", 0x5, Null]
    //     0xb78838: ldr             x4, [x4, #0x528]
    // 0xb7883c: r0 = ListView.separated()
    //     0xb7883c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb78840: r0 = Card()
    //     0xb78840: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb78844: mov             x2, x0
    // 0xb78848: r0 = 0.000000
    //     0xb78848: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb7884c: stur            x2, [fp, #-8]
    // 0xb78850: ArrayStore: r2[0] = r0  ; List_4
    //     0xb78850: stur            w0, [x2, #0x17]
    // 0xb78854: r0 = true
    //     0xb78854: add             x0, NULL, #0x20  ; true
    // 0xb78858: StoreField: r2->field_1f = r0
    //     0xb78858: stur            w0, [x2, #0x1f]
    // 0xb7885c: ldur            x1, [fp, #-0x18]
    // 0xb78860: StoreField: r2->field_2f = r1
    //     0xb78860: stur            w1, [x2, #0x2f]
    // 0xb78864: StoreField: r2->field_2b = r0
    //     0xb78864: stur            w0, [x2, #0x2b]
    // 0xb78868: r0 = Instance__CardVariant
    //     0xb78868: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb7886c: ldr             x0, [x0, #0xa68]
    // 0xb78870: StoreField: r2->field_33 = r0
    //     0xb78870: stur            w0, [x2, #0x33]
    // 0xb78874: r1 = <FlexParentData>
    //     0xb78874: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb78878: ldr             x1, [x1, #0xe00]
    // 0xb7887c: r0 = Flexible()
    //     0xb7887c: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xb78880: mov             x3, x0
    // 0xb78884: r0 = 1
    //     0xb78884: movz            x0, #0x1
    // 0xb78888: stur            x3, [fp, #-0x10]
    // 0xb7888c: StoreField: r3->field_13 = r0
    //     0xb7888c: stur            x0, [x3, #0x13]
    // 0xb78890: r0 = Instance_FlexFit
    //     0xb78890: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0xb78894: ldr             x0, [x0, #0xe20]
    // 0xb78898: StoreField: r3->field_1b = r0
    //     0xb78898: stur            w0, [x3, #0x1b]
    // 0xb7889c: ldur            x0, [fp, #-8]
    // 0xb788a0: StoreField: r3->field_b = r0
    //     0xb788a0: stur            w0, [x3, #0xb]
    // 0xb788a4: r1 = Null
    //     0xb788a4: mov             x1, NULL
    // 0xb788a8: r2 = 4
    //     0xb788a8: movz            x2, #0x4
    // 0xb788ac: r0 = AllocateArray()
    //     0xb788ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb788b0: mov             x2, x0
    // 0xb788b4: ldur            x0, [fp, #-0x20]
    // 0xb788b8: stur            x2, [fp, #-8]
    // 0xb788bc: StoreField: r2->field_f = r0
    //     0xb788bc: stur            w0, [x2, #0xf]
    // 0xb788c0: ldur            x0, [fp, #-0x10]
    // 0xb788c4: StoreField: r2->field_13 = r0
    //     0xb788c4: stur            w0, [x2, #0x13]
    // 0xb788c8: r1 = <Widget>
    //     0xb788c8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb788cc: r0 = AllocateGrowableArray()
    //     0xb788cc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb788d0: mov             x1, x0
    // 0xb788d4: ldur            x0, [fp, #-8]
    // 0xb788d8: stur            x1, [fp, #-0x10]
    // 0xb788dc: StoreField: r1->field_f = r0
    //     0xb788dc: stur            w0, [x1, #0xf]
    // 0xb788e0: r0 = 4
    //     0xb788e0: movz            x0, #0x4
    // 0xb788e4: StoreField: r1->field_b = r0
    //     0xb788e4: stur            w0, [x1, #0xb]
    // 0xb788e8: r0 = Column()
    //     0xb788e8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb788ec: mov             x1, x0
    // 0xb788f0: r0 = Instance_Axis
    //     0xb788f0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb788f4: stur            x1, [fp, #-8]
    // 0xb788f8: StoreField: r1->field_f = r0
    //     0xb788f8: stur            w0, [x1, #0xf]
    // 0xb788fc: r0 = Instance_MainAxisAlignment
    //     0xb788fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb78900: ldr             x0, [x0, #0xa08]
    // 0xb78904: StoreField: r1->field_13 = r0
    //     0xb78904: stur            w0, [x1, #0x13]
    // 0xb78908: r0 = Instance_MainAxisSize
    //     0xb78908: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb7890c: ldr             x0, [x0, #0xdd0]
    // 0xb78910: ArrayStore: r1[0] = r0  ; List_4
    //     0xb78910: stur            w0, [x1, #0x17]
    // 0xb78914: r0 = Instance_CrossAxisAlignment
    //     0xb78914: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb78918: ldr             x0, [x0, #0xa18]
    // 0xb7891c: StoreField: r1->field_1b = r0
    //     0xb7891c: stur            w0, [x1, #0x1b]
    // 0xb78920: r0 = Instance_VerticalDirection
    //     0xb78920: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb78924: ldr             x0, [x0, #0xa20]
    // 0xb78928: StoreField: r1->field_23 = r0
    //     0xb78928: stur            w0, [x1, #0x23]
    // 0xb7892c: r0 = Instance_Clip
    //     0xb7892c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb78930: ldr             x0, [x0, #0x38]
    // 0xb78934: StoreField: r1->field_2b = r0
    //     0xb78934: stur            w0, [x1, #0x2b]
    // 0xb78938: StoreField: r1->field_2f = rZR
    //     0xb78938: stur            xzr, [x1, #0x2f]
    // 0xb7893c: ldur            x0, [fp, #-0x10]
    // 0xb78940: StoreField: r1->field_b = r0
    //     0xb78940: stur            w0, [x1, #0xb]
    // 0xb78944: r0 = Container()
    //     0xb78944: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb78948: stur            x0, [fp, #-0x10]
    // 0xb7894c: r16 = Instance_EdgeInsets
    //     0xb7894c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0xb78950: ldr             x16, [x16, #0xb0]
    // 0xb78954: ldur            lr, [fp, #-8]
    // 0xb78958: stp             lr, x16, [SP]
    // 0xb7895c: mov             x1, x0
    // 0xb78960: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, margin, 0x1, null]
    //     0xb78960: add             x4, PP, #0x53, lsl #12  ; [pp+0x53400] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "margin", 0x1, Null]
    //     0xb78964: ldr             x4, [x4, #0x400]
    // 0xb78968: r0 = Container()
    //     0xb78968: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb7896c: ldur            x0, [fp, #-0x10]
    // 0xb78970: LeaveFrame
    //     0xb78970: mov             SP, fp
    //     0xb78974: ldp             fp, lr, [SP], #0x10
    // 0xb78978: ret
    //     0xb78978: ret             
    // 0xb7897c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7897c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb78980: b               #0xb78690
    // 0xb78984: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb78984: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb78988: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb78988: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Column <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb7898c, size: 0x13d8
    // 0xb7898c: EnterFrame
    //     0xb7898c: stp             fp, lr, [SP, #-0x10]!
    //     0xb78990: mov             fp, SP
    // 0xb78994: AllocStack(0x80)
    //     0xb78994: sub             SP, SP, #0x80
    // 0xb78998: SetupParameters()
    //     0xb78998: ldr             x0, [fp, #0x20]
    //     0xb7899c: ldur            w2, [x0, #0x17]
    //     0xb789a0: add             x2, x2, HEAP, lsl #32
    //     0xb789a4: stur            x2, [fp, #-0x48]
    // 0xb789a8: CheckStackOverflow
    //     0xb789a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb789ac: cmp             SP, x16
    //     0xb789b0: b.ls            #0xb79d18
    // 0xb789b4: LoadField: r0 = r2->field_f
    //     0xb789b4: ldur            w0, [x2, #0xf]
    // 0xb789b8: DecompressPointer r0
    //     0xb789b8: add             x0, x0, HEAP, lsl #32
    // 0xb789bc: LoadField: r3 = r0->field_b
    //     0xb789bc: ldur            w3, [x0, #0xb]
    // 0xb789c0: DecompressPointer r3
    //     0xb789c0: add             x3, x3, HEAP, lsl #32
    // 0xb789c4: cmp             w3, NULL
    // 0xb789c8: b.eq            #0xb79d20
    // 0xb789cc: LoadField: r4 = r3->field_1f
    //     0xb789cc: ldur            w4, [x3, #0x1f]
    // 0xb789d0: DecompressPointer r4
    //     0xb789d0: add             x4, x4, HEAP, lsl #32
    // 0xb789d4: stur            x4, [fp, #-0x40]
    // 0xb789d8: LoadField: r5 = r3->field_f
    //     0xb789d8: ldur            w5, [x3, #0xf]
    // 0xb789dc: DecompressPointer r5
    //     0xb789dc: add             x5, x5, HEAP, lsl #32
    // 0xb789e0: stur            x5, [fp, #-0x38]
    // 0xb789e4: LoadField: r6 = r3->field_13
    //     0xb789e4: ldur            w6, [x3, #0x13]
    // 0xb789e8: DecompressPointer r6
    //     0xb789e8: add             x6, x6, HEAP, lsl #32
    // 0xb789ec: stur            x6, [fp, #-0x30]
    // 0xb789f0: LoadField: r0 = r3->field_b
    //     0xb789f0: ldur            w0, [x3, #0xb]
    // 0xb789f4: DecompressPointer r0
    //     0xb789f4: add             x0, x0, HEAP, lsl #32
    // 0xb789f8: LoadField: r7 = r0->field_b
    //     0xb789f8: ldur            w7, [x0, #0xb]
    // 0xb789fc: DecompressPointer r7
    //     0xb789fc: add             x7, x7, HEAP, lsl #32
    // 0xb78a00: stur            x7, [fp, #-0x28]
    // 0xb78a04: cmp             w7, NULL
    // 0xb78a08: b.eq            #0xb79d24
    // 0xb78a0c: LoadField: r0 = r7->field_b
    //     0xb78a0c: ldur            w0, [x7, #0xb]
    // 0xb78a10: ldr             x1, [fp, #0x10]
    // 0xb78a14: r8 = LoadInt32Instr(r1)
    //     0xb78a14: sbfx            x8, x1, #1, #0x1f
    //     0xb78a18: tbz             w1, #0, #0xb78a20
    //     0xb78a1c: ldur            x8, [x1, #7]
    // 0xb78a20: stur            x8, [fp, #-0x20]
    // 0xb78a24: r1 = LoadInt32Instr(r0)
    //     0xb78a24: sbfx            x1, x0, #1, #0x1f
    // 0xb78a28: mov             x0, x1
    // 0xb78a2c: mov             x1, x8
    // 0xb78a30: cmp             x1, x0
    // 0xb78a34: b.hs            #0xb79d28
    // 0xb78a38: LoadField: r0 = r7->field_f
    //     0xb78a38: ldur            w0, [x7, #0xf]
    // 0xb78a3c: DecompressPointer r0
    //     0xb78a3c: add             x0, x0, HEAP, lsl #32
    // 0xb78a40: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xb78a40: add             x16, x0, x8, lsl #2
    //     0xb78a44: ldur            w1, [x16, #0xf]
    // 0xb78a48: DecompressPointer r1
    //     0xb78a48: add             x1, x1, HEAP, lsl #32
    // 0xb78a4c: stur            x1, [fp, #-0x18]
    // 0xb78a50: cmp             w1, NULL
    // 0xb78a54: b.eq            #0xb79d2c
    // 0xb78a58: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xb78a58: ldur            w0, [x3, #0x17]
    // 0xb78a5c: DecompressPointer r0
    //     0xb78a5c: add             x0, x0, HEAP, lsl #32
    // 0xb78a60: stur            x0, [fp, #-0x10]
    // 0xb78a64: LoadField: r9 = r3->field_1b
    //     0xb78a64: ldur            w9, [x3, #0x1b]
    // 0xb78a68: DecompressPointer r9
    //     0xb78a68: add             x9, x9, HEAP, lsl #32
    // 0xb78a6c: stur            x9, [fp, #-8]
    // 0xb78a70: r0 = OrderCard()
    //     0xb78a70: bl              #0xb79d64  ; AllocateOrderCardStub -> OrderCard (size=0x30)
    // 0xb78a74: mov             x3, x0
    // 0xb78a78: ldur            x0, [fp, #-0x18]
    // 0xb78a7c: stur            x3, [fp, #-0x50]
    // 0xb78a80: StoreField: r3->field_b = r0
    //     0xb78a80: stur            w0, [x3, #0xb]
    // 0xb78a84: r1 = "order_card"
    //     0xb78a84: add             x1, PP, #0x36, lsl #12  ; [pp+0x369a0] "order_card"
    //     0xb78a88: ldr             x1, [x1, #0x9a0]
    // 0xb78a8c: StoreField: r3->field_f = r1
    //     0xb78a8c: stur            w1, [x3, #0xf]
    // 0xb78a90: ldur            x1, [fp, #-0x38]
    // 0xb78a94: StoreField: r3->field_13 = r1
    //     0xb78a94: stur            w1, [x3, #0x13]
    // 0xb78a98: ldur            x1, [fp, #-0x30]
    // 0xb78a9c: ArrayStore: r3[0] = r1  ; List_4
    //     0xb78a9c: stur            w1, [x3, #0x17]
    // 0xb78aa0: ldur            x1, [fp, #-0x10]
    // 0xb78aa4: StoreField: r3->field_1b = r1
    //     0xb78aa4: stur            w1, [x3, #0x1b]
    // 0xb78aa8: ldur            x1, [fp, #-8]
    // 0xb78aac: StoreField: r3->field_1f = r1
    //     0xb78aac: stur            w1, [x3, #0x1f]
    // 0xb78ab0: ldur            x2, [fp, #-0x48]
    // 0xb78ab4: r1 = Function '<anonymous closure>':.
    //     0xb78ab4: add             x1, PP, #0x55, lsl #12  ; [pp+0x55a70] AnonymousClosure: (0xb79dec), in [package:customer_app/app/presentation/views/glass/orders/order_item_card.dart] _OrderItemCardState::build (0xb78668)
    //     0xb78ab8: ldr             x1, [x1, #0xa70]
    // 0xb78abc: r0 = AllocateClosure()
    //     0xb78abc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb78ac0: mov             x1, x0
    // 0xb78ac4: ldur            x0, [fp, #-0x50]
    // 0xb78ac8: StoreField: r0->field_2b = r1
    //     0xb78ac8: stur            w1, [x0, #0x2b]
    // 0xb78acc: ldur            x2, [fp, #-0x48]
    // 0xb78ad0: r1 = Function '<anonymous closure>':.
    //     0xb78ad0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55a78] AnonymousClosure: (0xb79d70), in [package:customer_app/app/presentation/views/glass/orders/order_item_card.dart] _OrderItemCardState::build (0xb78668)
    //     0xb78ad4: ldr             x1, [x1, #0xa78]
    // 0xb78ad8: r0 = AllocateClosure()
    //     0xb78ad8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb78adc: ldur            x2, [fp, #-0x50]
    // 0xb78ae0: StoreField: r2->field_23 = r0
    //     0xb78ae0: stur            w0, [x2, #0x23]
    // 0xb78ae4: ldur            x0, [fp, #-0x40]
    // 0xb78ae8: StoreField: r2->field_27 = r0
    //     0xb78ae8: stur            w0, [x2, #0x27]
    // 0xb78aec: ldur            x0, [fp, #-0x18]
    // 0xb78af0: cmp             w0, NULL
    // 0xb78af4: b.ne            #0xb78b00
    // 0xb78af8: r0 = Null
    //     0xb78af8: mov             x0, NULL
    // 0xb78afc: b               #0xb78b0c
    // 0xb78b00: LoadField: r1 = r0->field_7f
    //     0xb78b00: ldur            w1, [x0, #0x7f]
    // 0xb78b04: DecompressPointer r1
    //     0xb78b04: add             x1, x1, HEAP, lsl #32
    // 0xb78b08: mov             x0, x1
    // 0xb78b0c: ldur            x3, [fp, #-0x28]
    // 0xb78b10: cmp             w0, NULL
    // 0xb78b14: r16 = true
    //     0xb78b14: add             x16, NULL, #0x20  ; true
    // 0xb78b18: r17 = false
    //     0xb78b18: add             x17, NULL, #0x30  ; false
    // 0xb78b1c: csel            x4, x16, x17, ne
    // 0xb78b20: stur            x4, [fp, #-8]
    // 0xb78b24: cmp             w3, NULL
    // 0xb78b28: b.ne            #0xb78b38
    // 0xb78b2c: ldur            x5, [fp, #-0x20]
    // 0xb78b30: r0 = Null
    //     0xb78b30: mov             x0, NULL
    // 0xb78b34: b               #0xb78b9c
    // 0xb78b38: ldur            x5, [fp, #-0x20]
    // 0xb78b3c: LoadField: r0 = r3->field_b
    //     0xb78b3c: ldur            w0, [x3, #0xb]
    // 0xb78b40: r1 = LoadInt32Instr(r0)
    //     0xb78b40: sbfx            x1, x0, #1, #0x1f
    // 0xb78b44: mov             x0, x1
    // 0xb78b48: mov             x1, x5
    // 0xb78b4c: cmp             x1, x0
    // 0xb78b50: b.hs            #0xb79d30
    // 0xb78b54: LoadField: r0 = r3->field_f
    //     0xb78b54: ldur            w0, [x3, #0xf]
    // 0xb78b58: DecompressPointer r0
    //     0xb78b58: add             x0, x0, HEAP, lsl #32
    // 0xb78b5c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb78b5c: add             x16, x0, x5, lsl #2
    //     0xb78b60: ldur            w1, [x16, #0xf]
    // 0xb78b64: DecompressPointer r1
    //     0xb78b64: add             x1, x1, HEAP, lsl #32
    // 0xb78b68: cmp             w1, NULL
    // 0xb78b6c: b.ne            #0xb78b78
    // 0xb78b70: r0 = Null
    //     0xb78b70: mov             x0, NULL
    // 0xb78b74: b               #0xb78b9c
    // 0xb78b78: LoadField: r0 = r1->field_7f
    //     0xb78b78: ldur            w0, [x1, #0x7f]
    // 0xb78b7c: DecompressPointer r0
    //     0xb78b7c: add             x0, x0, HEAP, lsl #32
    // 0xb78b80: cmp             w0, NULL
    // 0xb78b84: b.ne            #0xb78b90
    // 0xb78b88: r0 = Null
    //     0xb78b88: mov             x0, NULL
    // 0xb78b8c: b               #0xb78b9c
    // 0xb78b90: LoadField: r1 = r0->field_2b
    //     0xb78b90: ldur            w1, [x0, #0x2b]
    // 0xb78b94: DecompressPointer r1
    //     0xb78b94: add             x1, x1, HEAP, lsl #32
    // 0xb78b98: mov             x0, x1
    // 0xb78b9c: cmp             w0, NULL
    // 0xb78ba0: b.ne            #0xb78c10
    // 0xb78ba4: ldur            x7, [fp, #-0x48]
    // 0xb78ba8: mov             x8, x5
    // 0xb78bac: r4 = 2
    //     0xb78bac: movz            x4, #0x2
    // 0xb78bb0: r6 = "Free"
    //     0xb78bb0: add             x6, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb78bb4: ldr             x6, [x6, #0x668]
    // 0xb78bb8: r5 = 150.000000
    //     0xb78bb8: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb78bbc: ldr             x5, [x5, #0x690]
    // 0xb78bc0: r3 = Instance_TextOverflow
    //     0xb78bc0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb78bc4: ldr             x3, [x3, #0xe10]
    // 0xb78bc8: r9 = Instance_CrossAxisAlignment
    //     0xb78bc8: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb78bcc: ldr             x9, [x9, #0x890]
    // 0xb78bd0: r10 = Instance_EdgeInsets
    //     0xb78bd0: add             x10, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb78bd4: ldr             x10, [x10, #0xa78]
    // 0xb78bd8: r19 = Instance_EdgeInsets
    //     0xb78bd8: add             x19, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb78bdc: ldr             x19, [x19, #0x770]
    // 0xb78be0: r13 = 6
    //     0xb78be0: movz            x13, #0x6
    // 0xb78be4: r14 = Instance_Axis
    //     0xb78be4: ldr             x14, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb78be8: r12 = Instance_FlexFit
    //     0xb78be8: add             x12, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb78bec: ldr             x12, [x12, #0xe08]
    // 0xb78bf0: r0 = Instance_BoxShape
    //     0xb78bf0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb78bf4: ldr             x0, [x0, #0x80]
    // 0xb78bf8: r1 = Instance_Alignment
    //     0xb78bf8: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb78bfc: ldr             x1, [x1, #0xb10]
    // 0xb78c00: r2 = -1
    //     0xb78c00: movn            x2, #0
    // 0xb78c04: d0 = 12.000000
    //     0xb78c04: fmov            d0, #12.00000000
    // 0xb78c08: r11 = 1
    //     0xb78c08: movz            x11, #0x1
    // 0xb78c0c: b               #0xb79418
    // 0xb78c10: tbnz            w0, #4, #0xb793b0
    // 0xb78c14: ldur            x0, [fp, #-0x48]
    // 0xb78c18: r0 = Radius()
    //     0xb78c18: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb78c1c: d0 = 12.000000
    //     0xb78c1c: fmov            d0, #12.00000000
    // 0xb78c20: stur            x0, [fp, #-0x10]
    // 0xb78c24: StoreField: r0->field_7 = d0
    //     0xb78c24: stur            d0, [x0, #7]
    // 0xb78c28: StoreField: r0->field_f = d0
    //     0xb78c28: stur            d0, [x0, #0xf]
    // 0xb78c2c: r0 = BorderRadius()
    //     0xb78c2c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb78c30: mov             x2, x0
    // 0xb78c34: ldur            x0, [fp, #-0x10]
    // 0xb78c38: stur            x2, [fp, #-0x18]
    // 0xb78c3c: StoreField: r2->field_7 = r0
    //     0xb78c3c: stur            w0, [x2, #7]
    // 0xb78c40: StoreField: r2->field_b = r0
    //     0xb78c40: stur            w0, [x2, #0xb]
    // 0xb78c44: StoreField: r2->field_f = r0
    //     0xb78c44: stur            w0, [x2, #0xf]
    // 0xb78c48: StoreField: r2->field_13 = r0
    //     0xb78c48: stur            w0, [x2, #0x13]
    // 0xb78c4c: r1 = Instance_Color
    //     0xb78c4c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb78c50: d0 = 0.070000
    //     0xb78c50: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xb78c54: ldr             d0, [x17, #0x5f8]
    // 0xb78c58: r0 = withOpacity()
    //     0xb78c58: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb78c5c: r16 = 1.000000
    //     0xb78c5c: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb78c60: str             x16, [SP]
    // 0xb78c64: mov             x2, x0
    // 0xb78c68: r1 = Null
    //     0xb78c68: mov             x1, NULL
    // 0xb78c6c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xb78c6c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xb78c70: ldr             x4, [x4, #0x108]
    // 0xb78c74: r0 = Border.all()
    //     0xb78c74: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb78c78: stur            x0, [fp, #-0x10]
    // 0xb78c7c: r0 = BoxDecoration()
    //     0xb78c7c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb78c80: mov             x2, x0
    // 0xb78c84: ldur            x0, [fp, #-0x10]
    // 0xb78c88: stur            x2, [fp, #-0x28]
    // 0xb78c8c: StoreField: r2->field_f = r0
    //     0xb78c8c: stur            w0, [x2, #0xf]
    // 0xb78c90: ldur            x0, [fp, #-0x18]
    // 0xb78c94: StoreField: r2->field_13 = r0
    //     0xb78c94: stur            w0, [x2, #0x13]
    // 0xb78c98: r0 = Instance_LinearGradient
    //     0xb78c98: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0xb78c9c: ldr             x0, [x0, #0x660]
    // 0xb78ca0: StoreField: r2->field_1b = r0
    //     0xb78ca0: stur            w0, [x2, #0x1b]
    // 0xb78ca4: r0 = Instance_BoxShape
    //     0xb78ca4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb78ca8: ldr             x0, [x0, #0x80]
    // 0xb78cac: StoreField: r2->field_23 = r0
    //     0xb78cac: stur            w0, [x2, #0x23]
    // 0xb78cb0: ldr             x1, [fp, #0x18]
    // 0xb78cb4: r0 = of()
    //     0xb78cb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb78cb8: LoadField: r1 = r0->field_87
    //     0xb78cb8: ldur            w1, [x0, #0x87]
    // 0xb78cbc: DecompressPointer r1
    //     0xb78cbc: add             x1, x1, HEAP, lsl #32
    // 0xb78cc0: LoadField: r0 = r1->field_7
    //     0xb78cc0: ldur            w0, [x1, #7]
    // 0xb78cc4: DecompressPointer r0
    //     0xb78cc4: add             x0, x0, HEAP, lsl #32
    // 0xb78cc8: r16 = 12.000000
    //     0xb78cc8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb78ccc: ldr             x16, [x16, #0x9e8]
    // 0xb78cd0: r30 = Instance_Color
    //     0xb78cd0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb78cd4: stp             lr, x16, [SP]
    // 0xb78cd8: mov             x1, x0
    // 0xb78cdc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb78cdc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb78ce0: ldr             x4, [x4, #0xaa0]
    // 0xb78ce4: r0 = copyWith()
    //     0xb78ce4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb78ce8: stur            x0, [fp, #-0x10]
    // 0xb78cec: r0 = Text()
    //     0xb78cec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb78cf0: mov             x1, x0
    // 0xb78cf4: r0 = "Free"
    //     0xb78cf4: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb78cf8: ldr             x0, [x0, #0x668]
    // 0xb78cfc: stur            x1, [fp, #-0x18]
    // 0xb78d00: StoreField: r1->field_b = r0
    //     0xb78d00: stur            w0, [x1, #0xb]
    // 0xb78d04: ldur            x2, [fp, #-0x10]
    // 0xb78d08: StoreField: r1->field_13 = r2
    //     0xb78d08: stur            w2, [x1, #0x13]
    // 0xb78d0c: r0 = Center()
    //     0xb78d0c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb78d10: r1 = Instance_Alignment
    //     0xb78d10: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb78d14: ldr             x1, [x1, #0xb10]
    // 0xb78d18: stur            x0, [fp, #-0x10]
    // 0xb78d1c: StoreField: r0->field_f = r1
    //     0xb78d1c: stur            w1, [x0, #0xf]
    // 0xb78d20: ldur            x1, [fp, #-0x18]
    // 0xb78d24: StoreField: r0->field_b = r1
    //     0xb78d24: stur            w1, [x0, #0xb]
    // 0xb78d28: r0 = RotatedBox()
    //     0xb78d28: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xb78d2c: r2 = -1
    //     0xb78d2c: movn            x2, #0
    // 0xb78d30: stur            x0, [fp, #-0x18]
    // 0xb78d34: StoreField: r0->field_f = r2
    //     0xb78d34: stur            x2, [x0, #0xf]
    // 0xb78d38: ldur            x1, [fp, #-0x10]
    // 0xb78d3c: StoreField: r0->field_b = r1
    //     0xb78d3c: stur            w1, [x0, #0xb]
    // 0xb78d40: r0 = Container()
    //     0xb78d40: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb78d44: stur            x0, [fp, #-0x10]
    // 0xb78d48: r16 = 24.000000
    //     0xb78d48: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xb78d4c: ldr             x16, [x16, #0xba8]
    // 0xb78d50: r30 = 56.000000
    //     0xb78d50: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb78d54: ldr             lr, [lr, #0xb78]
    // 0xb78d58: stp             lr, x16, [SP, #0x10]
    // 0xb78d5c: r16 = Instance_BoxDecoration
    //     0xb78d5c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40db0] Obj!BoxDecoration@d648c1
    //     0xb78d60: ldr             x16, [x16, #0xdb0]
    // 0xb78d64: ldur            lr, [fp, #-0x18]
    // 0xb78d68: stp             lr, x16, [SP]
    // 0xb78d6c: mov             x1, x0
    // 0xb78d70: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb78d70: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb78d74: ldr             x4, [x4, #0x870]
    // 0xb78d78: r0 = Container()
    //     0xb78d78: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb78d7c: ldur            x2, [fp, #-0x48]
    // 0xb78d80: LoadField: r0 = r2->field_f
    //     0xb78d80: ldur            w0, [x2, #0xf]
    // 0xb78d84: DecompressPointer r0
    //     0xb78d84: add             x0, x0, HEAP, lsl #32
    // 0xb78d88: LoadField: r1 = r0->field_b
    //     0xb78d88: ldur            w1, [x0, #0xb]
    // 0xb78d8c: DecompressPointer r1
    //     0xb78d8c: add             x1, x1, HEAP, lsl #32
    // 0xb78d90: cmp             w1, NULL
    // 0xb78d94: b.eq            #0xb79d34
    // 0xb78d98: LoadField: r0 = r1->field_b
    //     0xb78d98: ldur            w0, [x1, #0xb]
    // 0xb78d9c: DecompressPointer r0
    //     0xb78d9c: add             x0, x0, HEAP, lsl #32
    // 0xb78da0: LoadField: r3 = r0->field_b
    //     0xb78da0: ldur            w3, [x0, #0xb]
    // 0xb78da4: DecompressPointer r3
    //     0xb78da4: add             x3, x3, HEAP, lsl #32
    // 0xb78da8: cmp             w3, NULL
    // 0xb78dac: b.ne            #0xb78dbc
    // 0xb78db0: ldur            x4, [fp, #-0x20]
    // 0xb78db4: r0 = Null
    //     0xb78db4: mov             x0, NULL
    // 0xb78db8: b               #0xb78e20
    // 0xb78dbc: ldur            x4, [fp, #-0x20]
    // 0xb78dc0: LoadField: r0 = r3->field_b
    //     0xb78dc0: ldur            w0, [x3, #0xb]
    // 0xb78dc4: r1 = LoadInt32Instr(r0)
    //     0xb78dc4: sbfx            x1, x0, #1, #0x1f
    // 0xb78dc8: mov             x0, x1
    // 0xb78dcc: mov             x1, x4
    // 0xb78dd0: cmp             x1, x0
    // 0xb78dd4: b.hs            #0xb79d38
    // 0xb78dd8: LoadField: r0 = r3->field_f
    //     0xb78dd8: ldur            w0, [x3, #0xf]
    // 0xb78ddc: DecompressPointer r0
    //     0xb78ddc: add             x0, x0, HEAP, lsl #32
    // 0xb78de0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb78de0: add             x16, x0, x4, lsl #2
    //     0xb78de4: ldur            w1, [x16, #0xf]
    // 0xb78de8: DecompressPointer r1
    //     0xb78de8: add             x1, x1, HEAP, lsl #32
    // 0xb78dec: cmp             w1, NULL
    // 0xb78df0: b.ne            #0xb78dfc
    // 0xb78df4: r0 = Null
    //     0xb78df4: mov             x0, NULL
    // 0xb78df8: b               #0xb78e20
    // 0xb78dfc: LoadField: r0 = r1->field_7f
    //     0xb78dfc: ldur            w0, [x1, #0x7f]
    // 0xb78e00: DecompressPointer r0
    //     0xb78e00: add             x0, x0, HEAP, lsl #32
    // 0xb78e04: cmp             w0, NULL
    // 0xb78e08: b.ne            #0xb78e14
    // 0xb78e0c: r0 = Null
    //     0xb78e0c: mov             x0, NULL
    // 0xb78e10: b               #0xb78e20
    // 0xb78e14: LoadField: r1 = r0->field_7
    //     0xb78e14: ldur            w1, [x0, #7]
    // 0xb78e18: DecompressPointer r1
    //     0xb78e18: add             x1, x1, HEAP, lsl #32
    // 0xb78e1c: mov             x0, x1
    // 0xb78e20: cmp             w0, NULL
    // 0xb78e24: b.ne            #0xb78e2c
    // 0xb78e28: r0 = ""
    //     0xb78e28: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb78e2c: stur            x0, [fp, #-0x18]
    // 0xb78e30: r0 = CachedNetworkImage()
    //     0xb78e30: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb78e34: stur            x0, [fp, #-0x30]
    // 0xb78e38: r16 = 56.000000
    //     0xb78e38: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb78e3c: ldr             x16, [x16, #0xb78]
    // 0xb78e40: r30 = 56.000000
    //     0xb78e40: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb78e44: ldr             lr, [lr, #0xb78]
    // 0xb78e48: stp             lr, x16, [SP, #8]
    // 0xb78e4c: r16 = Instance_BoxFit
    //     0xb78e4c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb78e50: ldr             x16, [x16, #0x118]
    // 0xb78e54: str             x16, [SP]
    // 0xb78e58: mov             x1, x0
    // 0xb78e5c: ldur            x2, [fp, #-0x18]
    // 0xb78e60: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x3, width, 0x2, null]
    //     0xb78e60: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb40] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0xb78e64: ldr             x4, [x4, #0xb40]
    // 0xb78e68: r0 = CachedNetworkImage()
    //     0xb78e68: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb78e6c: ldur            x2, [fp, #-0x48]
    // 0xb78e70: LoadField: r0 = r2->field_f
    //     0xb78e70: ldur            w0, [x2, #0xf]
    // 0xb78e74: DecompressPointer r0
    //     0xb78e74: add             x0, x0, HEAP, lsl #32
    // 0xb78e78: LoadField: r1 = r0->field_b
    //     0xb78e78: ldur            w1, [x0, #0xb]
    // 0xb78e7c: DecompressPointer r1
    //     0xb78e7c: add             x1, x1, HEAP, lsl #32
    // 0xb78e80: cmp             w1, NULL
    // 0xb78e84: b.eq            #0xb79d3c
    // 0xb78e88: LoadField: r0 = r1->field_b
    //     0xb78e88: ldur            w0, [x1, #0xb]
    // 0xb78e8c: DecompressPointer r0
    //     0xb78e8c: add             x0, x0, HEAP, lsl #32
    // 0xb78e90: LoadField: r3 = r0->field_b
    //     0xb78e90: ldur            w3, [x0, #0xb]
    // 0xb78e94: DecompressPointer r3
    //     0xb78e94: add             x3, x3, HEAP, lsl #32
    // 0xb78e98: cmp             w3, NULL
    // 0xb78e9c: b.ne            #0xb78eac
    // 0xb78ea0: ldur            x4, [fp, #-0x20]
    // 0xb78ea4: r0 = Null
    //     0xb78ea4: mov             x0, NULL
    // 0xb78ea8: b               #0xb78f10
    // 0xb78eac: ldur            x4, [fp, #-0x20]
    // 0xb78eb0: LoadField: r0 = r3->field_b
    //     0xb78eb0: ldur            w0, [x3, #0xb]
    // 0xb78eb4: r1 = LoadInt32Instr(r0)
    //     0xb78eb4: sbfx            x1, x0, #1, #0x1f
    // 0xb78eb8: mov             x0, x1
    // 0xb78ebc: mov             x1, x4
    // 0xb78ec0: cmp             x1, x0
    // 0xb78ec4: b.hs            #0xb79d40
    // 0xb78ec8: LoadField: r0 = r3->field_f
    //     0xb78ec8: ldur            w0, [x3, #0xf]
    // 0xb78ecc: DecompressPointer r0
    //     0xb78ecc: add             x0, x0, HEAP, lsl #32
    // 0xb78ed0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb78ed0: add             x16, x0, x4, lsl #2
    //     0xb78ed4: ldur            w1, [x16, #0xf]
    // 0xb78ed8: DecompressPointer r1
    //     0xb78ed8: add             x1, x1, HEAP, lsl #32
    // 0xb78edc: cmp             w1, NULL
    // 0xb78ee0: b.ne            #0xb78eec
    // 0xb78ee4: r0 = Null
    //     0xb78ee4: mov             x0, NULL
    // 0xb78ee8: b               #0xb78f10
    // 0xb78eec: LoadField: r0 = r1->field_7f
    //     0xb78eec: ldur            w0, [x1, #0x7f]
    // 0xb78ef0: DecompressPointer r0
    //     0xb78ef0: add             x0, x0, HEAP, lsl #32
    // 0xb78ef4: cmp             w0, NULL
    // 0xb78ef8: b.ne            #0xb78f04
    // 0xb78efc: r0 = Null
    //     0xb78efc: mov             x0, NULL
    // 0xb78f00: b               #0xb78f10
    // 0xb78f04: LoadField: r1 = r0->field_b
    //     0xb78f04: ldur            w1, [x0, #0xb]
    // 0xb78f08: DecompressPointer r1
    //     0xb78f08: add             x1, x1, HEAP, lsl #32
    // 0xb78f0c: mov             x0, x1
    // 0xb78f10: cmp             w0, NULL
    // 0xb78f14: b.ne            #0xb78f1c
    // 0xb78f18: r0 = ""
    //     0xb78f18: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb78f1c: ldr             x1, [fp, #0x18]
    // 0xb78f20: stur            x0, [fp, #-0x18]
    // 0xb78f24: r0 = of()
    //     0xb78f24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb78f28: LoadField: r1 = r0->field_87
    //     0xb78f28: ldur            w1, [x0, #0x87]
    // 0xb78f2c: DecompressPointer r1
    //     0xb78f2c: add             x1, x1, HEAP, lsl #32
    // 0xb78f30: LoadField: r0 = r1->field_7
    //     0xb78f30: ldur            w0, [x1, #7]
    // 0xb78f34: DecompressPointer r0
    //     0xb78f34: add             x0, x0, HEAP, lsl #32
    // 0xb78f38: r16 = 12.000000
    //     0xb78f38: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb78f3c: ldr             x16, [x16, #0x9e8]
    // 0xb78f40: r30 = Instance_Color
    //     0xb78f40: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb78f44: stp             lr, x16, [SP]
    // 0xb78f48: mov             x1, x0
    // 0xb78f4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb78f4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb78f50: ldr             x4, [x4, #0xaa0]
    // 0xb78f54: r0 = copyWith()
    //     0xb78f54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb78f58: stur            x0, [fp, #-0x38]
    // 0xb78f5c: r0 = Text()
    //     0xb78f5c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb78f60: mov             x1, x0
    // 0xb78f64: ldur            x0, [fp, #-0x18]
    // 0xb78f68: stur            x1, [fp, #-0x40]
    // 0xb78f6c: StoreField: r1->field_b = r0
    //     0xb78f6c: stur            w0, [x1, #0xb]
    // 0xb78f70: ldur            x0, [fp, #-0x38]
    // 0xb78f74: StoreField: r1->field_13 = r0
    //     0xb78f74: stur            w0, [x1, #0x13]
    // 0xb78f78: r3 = Instance_TextOverflow
    //     0xb78f78: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb78f7c: ldr             x3, [x3, #0xe10]
    // 0xb78f80: StoreField: r1->field_2b = r3
    //     0xb78f80: stur            w3, [x1, #0x2b]
    // 0xb78f84: r4 = 2
    //     0xb78f84: movz            x4, #0x2
    // 0xb78f88: StoreField: r1->field_37 = r4
    //     0xb78f88: stur            w4, [x1, #0x37]
    // 0xb78f8c: r0 = SizedBox()
    //     0xb78f8c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb78f90: r5 = 150.000000
    //     0xb78f90: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb78f94: ldr             x5, [x5, #0x690]
    // 0xb78f98: stur            x0, [fp, #-0x18]
    // 0xb78f9c: StoreField: r0->field_f = r5
    //     0xb78f9c: stur            w5, [x0, #0xf]
    // 0xb78fa0: ldur            x1, [fp, #-0x40]
    // 0xb78fa4: StoreField: r0->field_b = r1
    //     0xb78fa4: stur            w1, [x0, #0xb]
    // 0xb78fa8: ldr             x1, [fp, #0x18]
    // 0xb78fac: r0 = of()
    //     0xb78fac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb78fb0: LoadField: r1 = r0->field_87
    //     0xb78fb0: ldur            w1, [x0, #0x87]
    // 0xb78fb4: DecompressPointer r1
    //     0xb78fb4: add             x1, x1, HEAP, lsl #32
    // 0xb78fb8: LoadField: r0 = r1->field_2b
    //     0xb78fb8: ldur            w0, [x1, #0x2b]
    // 0xb78fbc: DecompressPointer r0
    //     0xb78fbc: add             x0, x0, HEAP, lsl #32
    // 0xb78fc0: r16 = 12.000000
    //     0xb78fc0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb78fc4: ldr             x16, [x16, #0x9e8]
    // 0xb78fc8: r30 = Instance_Color
    //     0xb78fc8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb78fcc: ldr             lr, [lr, #0x858]
    // 0xb78fd0: stp             lr, x16, [SP]
    // 0xb78fd4: mov             x1, x0
    // 0xb78fd8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb78fd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb78fdc: ldr             x4, [x4, #0xaa0]
    // 0xb78fe0: r0 = copyWith()
    //     0xb78fe0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb78fe4: stur            x0, [fp, #-0x38]
    // 0xb78fe8: r0 = Text()
    //     0xb78fe8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb78fec: mov             x2, x0
    // 0xb78ff0: r6 = "Free"
    //     0xb78ff0: add             x6, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb78ff4: ldr             x6, [x6, #0x668]
    // 0xb78ff8: stur            x2, [fp, #-0x40]
    // 0xb78ffc: StoreField: r2->field_b = r6
    //     0xb78ffc: stur            w6, [x2, #0xb]
    // 0xb79000: ldur            x0, [fp, #-0x38]
    // 0xb79004: StoreField: r2->field_13 = r0
    //     0xb79004: stur            w0, [x2, #0x13]
    // 0xb79008: ldur            x7, [fp, #-0x48]
    // 0xb7900c: LoadField: r0 = r7->field_f
    //     0xb7900c: ldur            w0, [x7, #0xf]
    // 0xb79010: DecompressPointer r0
    //     0xb79010: add             x0, x0, HEAP, lsl #32
    // 0xb79014: LoadField: r1 = r0->field_b
    //     0xb79014: ldur            w1, [x0, #0xb]
    // 0xb79018: DecompressPointer r1
    //     0xb79018: add             x1, x1, HEAP, lsl #32
    // 0xb7901c: cmp             w1, NULL
    // 0xb79020: b.eq            #0xb79d44
    // 0xb79024: LoadField: r0 = r1->field_b
    //     0xb79024: ldur            w0, [x1, #0xb]
    // 0xb79028: DecompressPointer r0
    //     0xb79028: add             x0, x0, HEAP, lsl #32
    // 0xb7902c: LoadField: r3 = r0->field_b
    //     0xb7902c: ldur            w3, [x0, #0xb]
    // 0xb79030: DecompressPointer r3
    //     0xb79030: add             x3, x3, HEAP, lsl #32
    // 0xb79034: cmp             w3, NULL
    // 0xb79038: b.ne            #0xb79044
    // 0xb7903c: r0 = Null
    //     0xb7903c: mov             x0, NULL
    // 0xb79040: b               #0xb790a8
    // 0xb79044: ldur            x8, [fp, #-0x20]
    // 0xb79048: LoadField: r0 = r3->field_b
    //     0xb79048: ldur            w0, [x3, #0xb]
    // 0xb7904c: r1 = LoadInt32Instr(r0)
    //     0xb7904c: sbfx            x1, x0, #1, #0x1f
    // 0xb79050: mov             x0, x1
    // 0xb79054: mov             x1, x8
    // 0xb79058: cmp             x1, x0
    // 0xb7905c: b.hs            #0xb79d48
    // 0xb79060: LoadField: r0 = r3->field_f
    //     0xb79060: ldur            w0, [x3, #0xf]
    // 0xb79064: DecompressPointer r0
    //     0xb79064: add             x0, x0, HEAP, lsl #32
    // 0xb79068: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xb79068: add             x16, x0, x8, lsl #2
    //     0xb7906c: ldur            w1, [x16, #0xf]
    // 0xb79070: DecompressPointer r1
    //     0xb79070: add             x1, x1, HEAP, lsl #32
    // 0xb79074: cmp             w1, NULL
    // 0xb79078: b.ne            #0xb79084
    // 0xb7907c: r0 = Null
    //     0xb7907c: mov             x0, NULL
    // 0xb79080: b               #0xb790a8
    // 0xb79084: LoadField: r0 = r1->field_7f
    //     0xb79084: ldur            w0, [x1, #0x7f]
    // 0xb79088: DecompressPointer r0
    //     0xb79088: add             x0, x0, HEAP, lsl #32
    // 0xb7908c: cmp             w0, NULL
    // 0xb79090: b.ne            #0xb7909c
    // 0xb79094: r0 = Null
    //     0xb79094: mov             x0, NULL
    // 0xb79098: b               #0xb790a8
    // 0xb7909c: LoadField: r1 = r0->field_13
    //     0xb7909c: ldur            w1, [x0, #0x13]
    // 0xb790a0: DecompressPointer r1
    //     0xb790a0: add             x1, x1, HEAP, lsl #32
    // 0xb790a4: mov             x0, x1
    // 0xb790a8: cmp             w0, NULL
    // 0xb790ac: b.ne            #0xb790b8
    // 0xb790b0: r5 = ""
    //     0xb790b0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb790b4: b               #0xb790bc
    // 0xb790b8: mov             x5, x0
    // 0xb790bc: ldur            x4, [fp, #-0x10]
    // 0xb790c0: ldur            x3, [fp, #-0x30]
    // 0xb790c4: ldur            x0, [fp, #-0x18]
    // 0xb790c8: ldr             x1, [fp, #0x18]
    // 0xb790cc: stur            x5, [fp, #-0x38]
    // 0xb790d0: r0 = of()
    //     0xb790d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb790d4: LoadField: r1 = r0->field_87
    //     0xb790d4: ldur            w1, [x0, #0x87]
    // 0xb790d8: DecompressPointer r1
    //     0xb790d8: add             x1, x1, HEAP, lsl #32
    // 0xb790dc: LoadField: r0 = r1->field_2b
    //     0xb790dc: ldur            w0, [x1, #0x2b]
    // 0xb790e0: DecompressPointer r0
    //     0xb790e0: add             x0, x0, HEAP, lsl #32
    // 0xb790e4: r16 = 12.000000
    //     0xb790e4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb790e8: ldr             x16, [x16, #0x9e8]
    // 0xb790ec: r30 = Instance_TextDecoration
    //     0xb790ec: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb790f0: ldr             lr, [lr, #0xe30]
    // 0xb790f4: stp             lr, x16, [SP]
    // 0xb790f8: mov             x1, x0
    // 0xb790fc: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xb790fc: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xb79100: ldr             x4, [x4, #0x698]
    // 0xb79104: r0 = copyWith()
    //     0xb79104: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb79108: stur            x0, [fp, #-0x58]
    // 0xb7910c: r0 = Text()
    //     0xb7910c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb79110: mov             x3, x0
    // 0xb79114: ldur            x0, [fp, #-0x38]
    // 0xb79118: stur            x3, [fp, #-0x60]
    // 0xb7911c: StoreField: r3->field_b = r0
    //     0xb7911c: stur            w0, [x3, #0xb]
    // 0xb79120: ldur            x0, [fp, #-0x58]
    // 0xb79124: StoreField: r3->field_13 = r0
    //     0xb79124: stur            w0, [x3, #0x13]
    // 0xb79128: r1 = Null
    //     0xb79128: mov             x1, NULL
    // 0xb7912c: r2 = 6
    //     0xb7912c: movz            x2, #0x6
    // 0xb79130: r0 = AllocateArray()
    //     0xb79130: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb79134: mov             x2, x0
    // 0xb79138: ldur            x0, [fp, #-0x40]
    // 0xb7913c: stur            x2, [fp, #-0x38]
    // 0xb79140: StoreField: r2->field_f = r0
    //     0xb79140: stur            w0, [x2, #0xf]
    // 0xb79144: r16 = Instance_SizedBox
    //     0xb79144: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb79148: ldr             x16, [x16, #0xa50]
    // 0xb7914c: StoreField: r2->field_13 = r16
    //     0xb7914c: stur            w16, [x2, #0x13]
    // 0xb79150: ldur            x0, [fp, #-0x60]
    // 0xb79154: ArrayStore: r2[0] = r0  ; List_4
    //     0xb79154: stur            w0, [x2, #0x17]
    // 0xb79158: r1 = <Widget>
    //     0xb79158: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7915c: r0 = AllocateGrowableArray()
    //     0xb7915c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb79160: mov             x1, x0
    // 0xb79164: ldur            x0, [fp, #-0x38]
    // 0xb79168: stur            x1, [fp, #-0x40]
    // 0xb7916c: StoreField: r1->field_f = r0
    //     0xb7916c: stur            w0, [x1, #0xf]
    // 0xb79170: r2 = 6
    //     0xb79170: movz            x2, #0x6
    // 0xb79174: StoreField: r1->field_b = r2
    //     0xb79174: stur            w2, [x1, #0xb]
    // 0xb79178: r0 = Row()
    //     0xb79178: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb7917c: mov             x3, x0
    // 0xb79180: r0 = Instance_Axis
    //     0xb79180: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb79184: stur            x3, [fp, #-0x38]
    // 0xb79188: StoreField: r3->field_f = r0
    //     0xb79188: stur            w0, [x3, #0xf]
    // 0xb7918c: r4 = Instance_MainAxisAlignment
    //     0xb7918c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb79190: ldr             x4, [x4, #0xa08]
    // 0xb79194: StoreField: r3->field_13 = r4
    //     0xb79194: stur            w4, [x3, #0x13]
    // 0xb79198: r5 = Instance_MainAxisSize
    //     0xb79198: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb7919c: ldr             x5, [x5, #0xa10]
    // 0xb791a0: ArrayStore: r3[0] = r5  ; List_4
    //     0xb791a0: stur            w5, [x3, #0x17]
    // 0xb791a4: r6 = Instance_CrossAxisAlignment
    //     0xb791a4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb791a8: ldr             x6, [x6, #0xa18]
    // 0xb791ac: StoreField: r3->field_1b = r6
    //     0xb791ac: stur            w6, [x3, #0x1b]
    // 0xb791b0: r7 = Instance_VerticalDirection
    //     0xb791b0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb791b4: ldr             x7, [x7, #0xa20]
    // 0xb791b8: StoreField: r3->field_23 = r7
    //     0xb791b8: stur            w7, [x3, #0x23]
    // 0xb791bc: r8 = Instance_Clip
    //     0xb791bc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb791c0: ldr             x8, [x8, #0x38]
    // 0xb791c4: StoreField: r3->field_2b = r8
    //     0xb791c4: stur            w8, [x3, #0x2b]
    // 0xb791c8: StoreField: r3->field_2f = rZR
    //     0xb791c8: stur            xzr, [x3, #0x2f]
    // 0xb791cc: ldur            x1, [fp, #-0x40]
    // 0xb791d0: StoreField: r3->field_b = r1
    //     0xb791d0: stur            w1, [x3, #0xb]
    // 0xb791d4: r1 = Null
    //     0xb791d4: mov             x1, NULL
    // 0xb791d8: r2 = 6
    //     0xb791d8: movz            x2, #0x6
    // 0xb791dc: r0 = AllocateArray()
    //     0xb791dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb791e0: mov             x2, x0
    // 0xb791e4: ldur            x0, [fp, #-0x18]
    // 0xb791e8: stur            x2, [fp, #-0x40]
    // 0xb791ec: StoreField: r2->field_f = r0
    //     0xb791ec: stur            w0, [x2, #0xf]
    // 0xb791f0: r16 = Instance_SizedBox
    //     0xb791f0: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb791f4: ldr             x16, [x16, #0xc70]
    // 0xb791f8: StoreField: r2->field_13 = r16
    //     0xb791f8: stur            w16, [x2, #0x13]
    // 0xb791fc: ldur            x0, [fp, #-0x38]
    // 0xb79200: ArrayStore: r2[0] = r0  ; List_4
    //     0xb79200: stur            w0, [x2, #0x17]
    // 0xb79204: r1 = <Widget>
    //     0xb79204: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb79208: r0 = AllocateGrowableArray()
    //     0xb79208: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7920c: mov             x1, x0
    // 0xb79210: ldur            x0, [fp, #-0x40]
    // 0xb79214: stur            x1, [fp, #-0x18]
    // 0xb79218: StoreField: r1->field_f = r0
    //     0xb79218: stur            w0, [x1, #0xf]
    // 0xb7921c: r2 = 6
    //     0xb7921c: movz            x2, #0x6
    // 0xb79220: StoreField: r1->field_b = r2
    //     0xb79220: stur            w2, [x1, #0xb]
    // 0xb79224: r0 = Column()
    //     0xb79224: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb79228: mov             x1, x0
    // 0xb7922c: r0 = Instance_Axis
    //     0xb7922c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb79230: stur            x1, [fp, #-0x38]
    // 0xb79234: StoreField: r1->field_f = r0
    //     0xb79234: stur            w0, [x1, #0xf]
    // 0xb79238: r2 = Instance_MainAxisAlignment
    //     0xb79238: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb7923c: ldr             x2, [x2, #0xa08]
    // 0xb79240: StoreField: r1->field_13 = r2
    //     0xb79240: stur            w2, [x1, #0x13]
    // 0xb79244: r3 = Instance_MainAxisSize
    //     0xb79244: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb79248: ldr             x3, [x3, #0xa10]
    // 0xb7924c: ArrayStore: r1[0] = r3  ; List_4
    //     0xb7924c: stur            w3, [x1, #0x17]
    // 0xb79250: r9 = Instance_CrossAxisAlignment
    //     0xb79250: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb79254: ldr             x9, [x9, #0x890]
    // 0xb79258: StoreField: r1->field_1b = r9
    //     0xb79258: stur            w9, [x1, #0x1b]
    // 0xb7925c: r4 = Instance_VerticalDirection
    //     0xb7925c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb79260: ldr             x4, [x4, #0xa20]
    // 0xb79264: StoreField: r1->field_23 = r4
    //     0xb79264: stur            w4, [x1, #0x23]
    // 0xb79268: r5 = Instance_Clip
    //     0xb79268: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7926c: ldr             x5, [x5, #0x38]
    // 0xb79270: StoreField: r1->field_2b = r5
    //     0xb79270: stur            w5, [x1, #0x2b]
    // 0xb79274: StoreField: r1->field_2f = rZR
    //     0xb79274: stur            xzr, [x1, #0x2f]
    // 0xb79278: ldur            x6, [fp, #-0x18]
    // 0xb7927c: StoreField: r1->field_b = r6
    //     0xb7927c: stur            w6, [x1, #0xb]
    // 0xb79280: r0 = Padding()
    //     0xb79280: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb79284: r10 = Instance_EdgeInsets
    //     0xb79284: add             x10, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb79288: ldr             x10, [x10, #0xa78]
    // 0xb7928c: stur            x0, [fp, #-0x18]
    // 0xb79290: StoreField: r0->field_f = r10
    //     0xb79290: stur            w10, [x0, #0xf]
    // 0xb79294: ldur            x1, [fp, #-0x38]
    // 0xb79298: StoreField: r0->field_b = r1
    //     0xb79298: stur            w1, [x0, #0xb]
    // 0xb7929c: r1 = <FlexParentData>
    //     0xb7929c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb792a0: ldr             x1, [x1, #0xe00]
    // 0xb792a4: r0 = Expanded()
    //     0xb792a4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb792a8: r11 = 1
    //     0xb792a8: movz            x11, #0x1
    // 0xb792ac: stur            x0, [fp, #-0x38]
    // 0xb792b0: StoreField: r0->field_13 = r11
    //     0xb792b0: stur            x11, [x0, #0x13]
    // 0xb792b4: r12 = Instance_FlexFit
    //     0xb792b4: add             x12, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb792b8: ldr             x12, [x12, #0xe08]
    // 0xb792bc: StoreField: r0->field_1b = r12
    //     0xb792bc: stur            w12, [x0, #0x1b]
    // 0xb792c0: ldur            x1, [fp, #-0x18]
    // 0xb792c4: StoreField: r0->field_b = r1
    //     0xb792c4: stur            w1, [x0, #0xb]
    // 0xb792c8: r1 = Null
    //     0xb792c8: mov             x1, NULL
    // 0xb792cc: r2 = 6
    //     0xb792cc: movz            x2, #0x6
    // 0xb792d0: r0 = AllocateArray()
    //     0xb792d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb792d4: mov             x2, x0
    // 0xb792d8: ldur            x0, [fp, #-0x10]
    // 0xb792dc: stur            x2, [fp, #-0x18]
    // 0xb792e0: StoreField: r2->field_f = r0
    //     0xb792e0: stur            w0, [x2, #0xf]
    // 0xb792e4: ldur            x0, [fp, #-0x30]
    // 0xb792e8: StoreField: r2->field_13 = r0
    //     0xb792e8: stur            w0, [x2, #0x13]
    // 0xb792ec: ldur            x0, [fp, #-0x38]
    // 0xb792f0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb792f0: stur            w0, [x2, #0x17]
    // 0xb792f4: r1 = <Widget>
    //     0xb792f4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb792f8: r0 = AllocateGrowableArray()
    //     0xb792f8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb792fc: mov             x1, x0
    // 0xb79300: ldur            x0, [fp, #-0x18]
    // 0xb79304: stur            x1, [fp, #-0x10]
    // 0xb79308: StoreField: r1->field_f = r0
    //     0xb79308: stur            w0, [x1, #0xf]
    // 0xb7930c: r13 = 6
    //     0xb7930c: movz            x13, #0x6
    // 0xb79310: StoreField: r1->field_b = r13
    //     0xb79310: stur            w13, [x1, #0xb]
    // 0xb79314: r0 = Row()
    //     0xb79314: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb79318: r14 = Instance_Axis
    //     0xb79318: ldr             x14, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb7931c: stur            x0, [fp, #-0x18]
    // 0xb79320: StoreField: r0->field_f = r14
    //     0xb79320: stur            w14, [x0, #0xf]
    // 0xb79324: r1 = Instance_MainAxisAlignment
    //     0xb79324: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb79328: ldr             x1, [x1, #0xa08]
    // 0xb7932c: StoreField: r0->field_13 = r1
    //     0xb7932c: stur            w1, [x0, #0x13]
    // 0xb79330: r2 = Instance_MainAxisSize
    //     0xb79330: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb79334: ldr             x2, [x2, #0xa10]
    // 0xb79338: ArrayStore: r0[0] = r2  ; List_4
    //     0xb79338: stur            w2, [x0, #0x17]
    // 0xb7933c: r3 = Instance_CrossAxisAlignment
    //     0xb7933c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb79340: ldr             x3, [x3, #0xa18]
    // 0xb79344: StoreField: r0->field_1b = r3
    //     0xb79344: stur            w3, [x0, #0x1b]
    // 0xb79348: r4 = Instance_VerticalDirection
    //     0xb79348: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb7934c: ldr             x4, [x4, #0xa20]
    // 0xb79350: StoreField: r0->field_23 = r4
    //     0xb79350: stur            w4, [x0, #0x23]
    // 0xb79354: r5 = Instance_Clip
    //     0xb79354: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb79358: ldr             x5, [x5, #0x38]
    // 0xb7935c: StoreField: r0->field_2b = r5
    //     0xb7935c: stur            w5, [x0, #0x2b]
    // 0xb79360: StoreField: r0->field_2f = rZR
    //     0xb79360: stur            xzr, [x0, #0x2f]
    // 0xb79364: ldur            x6, [fp, #-0x10]
    // 0xb79368: StoreField: r0->field_b = r6
    //     0xb79368: stur            w6, [x0, #0xb]
    // 0xb7936c: r0 = Container()
    //     0xb7936c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb79370: stur            x0, [fp, #-0x10]
    // 0xb79374: ldur            x16, [fp, #-0x28]
    // 0xb79378: ldur            lr, [fp, #-0x18]
    // 0xb7937c: stp             lr, x16, [SP]
    // 0xb79380: mov             x1, x0
    // 0xb79384: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb79384: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb79388: ldr             x4, [x4, #0x88]
    // 0xb7938c: r0 = Container()
    //     0xb7938c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb79390: r0 = Padding()
    //     0xb79390: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb79394: r19 = Instance_EdgeInsets
    //     0xb79394: add             x19, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb79398: ldr             x19, [x19, #0x770]
    // 0xb7939c: StoreField: r0->field_f = r19
    //     0xb7939c: stur            w19, [x0, #0xf]
    // 0xb793a0: ldur            x1, [fp, #-0x10]
    // 0xb793a4: StoreField: r0->field_b = r1
    //     0xb793a4: stur            w1, [x0, #0xb]
    // 0xb793a8: mov             x2, x0
    // 0xb793ac: b               #0xb79c28
    // 0xb793b0: ldur            x7, [fp, #-0x48]
    // 0xb793b4: mov             x8, x5
    // 0xb793b8: r4 = 2
    //     0xb793b8: movz            x4, #0x2
    // 0xb793bc: r6 = "Free"
    //     0xb793bc: add             x6, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb793c0: ldr             x6, [x6, #0x668]
    // 0xb793c4: r5 = 150.000000
    //     0xb793c4: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb793c8: ldr             x5, [x5, #0x690]
    // 0xb793cc: r3 = Instance_TextOverflow
    //     0xb793cc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb793d0: ldr             x3, [x3, #0xe10]
    // 0xb793d4: r9 = Instance_CrossAxisAlignment
    //     0xb793d4: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb793d8: ldr             x9, [x9, #0x890]
    // 0xb793dc: r10 = Instance_EdgeInsets
    //     0xb793dc: add             x10, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb793e0: ldr             x10, [x10, #0xa78]
    // 0xb793e4: r19 = Instance_EdgeInsets
    //     0xb793e4: add             x19, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb793e8: ldr             x19, [x19, #0x770]
    // 0xb793ec: r13 = 6
    //     0xb793ec: movz            x13, #0x6
    // 0xb793f0: r14 = Instance_Axis
    //     0xb793f0: ldr             x14, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb793f4: r12 = Instance_FlexFit
    //     0xb793f4: add             x12, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb793f8: ldr             x12, [x12, #0xe08]
    // 0xb793fc: r0 = Instance_BoxShape
    //     0xb793fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb79400: ldr             x0, [x0, #0x80]
    // 0xb79404: r1 = Instance_Alignment
    //     0xb79404: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb79408: ldr             x1, [x1, #0xb10]
    // 0xb7940c: r2 = -1
    //     0xb7940c: movn            x2, #0
    // 0xb79410: d0 = 12.000000
    //     0xb79410: fmov            d0, #12.00000000
    // 0xb79414: r11 = 1
    //     0xb79414: movz            x11, #0x1
    // 0xb79418: r0 = Radius()
    //     0xb79418: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb7941c: d0 = 12.000000
    //     0xb7941c: fmov            d0, #12.00000000
    // 0xb79420: stur            x0, [fp, #-0x10]
    // 0xb79424: StoreField: r0->field_7 = d0
    //     0xb79424: stur            d0, [x0, #7]
    // 0xb79428: StoreField: r0->field_f = d0
    //     0xb79428: stur            d0, [x0, #0xf]
    // 0xb7942c: r0 = BorderRadius()
    //     0xb7942c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb79430: mov             x2, x0
    // 0xb79434: ldur            x0, [fp, #-0x10]
    // 0xb79438: stur            x2, [fp, #-0x18]
    // 0xb7943c: StoreField: r2->field_7 = r0
    //     0xb7943c: stur            w0, [x2, #7]
    // 0xb79440: StoreField: r2->field_b = r0
    //     0xb79440: stur            w0, [x2, #0xb]
    // 0xb79444: StoreField: r2->field_f = r0
    //     0xb79444: stur            w0, [x2, #0xf]
    // 0xb79448: StoreField: r2->field_13 = r0
    //     0xb79448: stur            w0, [x2, #0x13]
    // 0xb7944c: ldr             x1, [fp, #0x18]
    // 0xb79450: r0 = of()
    //     0xb79450: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb79454: LoadField: r1 = r0->field_5b
    //     0xb79454: ldur            w1, [x0, #0x5b]
    // 0xb79458: DecompressPointer r1
    //     0xb79458: add             x1, x1, HEAP, lsl #32
    // 0xb7945c: r0 = LoadClassIdInstr(r1)
    //     0xb7945c: ldur            x0, [x1, #-1]
    //     0xb79460: ubfx            x0, x0, #0xc, #0x14
    // 0xb79464: d0 = 0.070000
    //     0xb79464: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xb79468: ldr             d0, [x17, #0x5f8]
    // 0xb7946c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb7946c: sub             lr, x0, #0xffa
    //     0xb79470: ldr             lr, [x21, lr, lsl #3]
    //     0xb79474: blr             lr
    // 0xb79478: r16 = 1.000000
    //     0xb79478: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb7947c: str             x16, [SP]
    // 0xb79480: mov             x2, x0
    // 0xb79484: r1 = Null
    //     0xb79484: mov             x1, NULL
    // 0xb79488: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xb79488: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xb7948c: ldr             x4, [x4, #0x108]
    // 0xb79490: r0 = Border.all()
    //     0xb79490: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb79494: stur            x0, [fp, #-0x10]
    // 0xb79498: r0 = BoxDecoration()
    //     0xb79498: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb7949c: mov             x2, x0
    // 0xb794a0: ldur            x0, [fp, #-0x10]
    // 0xb794a4: stur            x2, [fp, #-0x28]
    // 0xb794a8: StoreField: r2->field_f = r0
    //     0xb794a8: stur            w0, [x2, #0xf]
    // 0xb794ac: ldur            x0, [fp, #-0x18]
    // 0xb794b0: StoreField: r2->field_13 = r0
    //     0xb794b0: stur            w0, [x2, #0x13]
    // 0xb794b4: r0 = Instance_BoxShape
    //     0xb794b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb794b8: ldr             x0, [x0, #0x80]
    // 0xb794bc: StoreField: r2->field_23 = r0
    //     0xb794bc: stur            w0, [x2, #0x23]
    // 0xb794c0: ldr             x1, [fp, #0x18]
    // 0xb794c4: r0 = of()
    //     0xb794c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb794c8: LoadField: r1 = r0->field_5b
    //     0xb794c8: ldur            w1, [x0, #0x5b]
    // 0xb794cc: DecompressPointer r1
    //     0xb794cc: add             x1, x1, HEAP, lsl #32
    // 0xb794d0: r0 = LoadClassIdInstr(r1)
    //     0xb794d0: ldur            x0, [x1, #-1]
    //     0xb794d4: ubfx            x0, x0, #0xc, #0x14
    // 0xb794d8: d0 = 0.400000
    //     0xb794d8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb794dc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb794dc: sub             lr, x0, #0xffa
    //     0xb794e0: ldr             lr, [x21, lr, lsl #3]
    //     0xb794e4: blr             lr
    // 0xb794e8: stur            x0, [fp, #-0x10]
    // 0xb794ec: r0 = BoxDecoration()
    //     0xb794ec: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb794f0: mov             x2, x0
    // 0xb794f4: ldur            x0, [fp, #-0x10]
    // 0xb794f8: stur            x2, [fp, #-0x18]
    // 0xb794fc: StoreField: r2->field_7 = r0
    //     0xb794fc: stur            w0, [x2, #7]
    // 0xb79500: r0 = Instance_BorderRadius
    //     0xb79500: add             x0, PP, #0x48, lsl #12  ; [pp+0x48990] Obj!BorderRadius@d5a281
    //     0xb79504: ldr             x0, [x0, #0x990]
    // 0xb79508: StoreField: r2->field_13 = r0
    //     0xb79508: stur            w0, [x2, #0x13]
    // 0xb7950c: r0 = Instance_BoxShape
    //     0xb7950c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb79510: ldr             x0, [x0, #0x80]
    // 0xb79514: StoreField: r2->field_23 = r0
    //     0xb79514: stur            w0, [x2, #0x23]
    // 0xb79518: ldr             x1, [fp, #0x18]
    // 0xb7951c: r0 = of()
    //     0xb7951c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb79520: LoadField: r1 = r0->field_87
    //     0xb79520: ldur            w1, [x0, #0x87]
    // 0xb79524: DecompressPointer r1
    //     0xb79524: add             x1, x1, HEAP, lsl #32
    // 0xb79528: LoadField: r0 = r1->field_7
    //     0xb79528: ldur            w0, [x1, #7]
    // 0xb7952c: DecompressPointer r0
    //     0xb7952c: add             x0, x0, HEAP, lsl #32
    // 0xb79530: r16 = 12.000000
    //     0xb79530: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb79534: ldr             x16, [x16, #0x9e8]
    // 0xb79538: r30 = Instance_Color
    //     0xb79538: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb7953c: stp             lr, x16, [SP]
    // 0xb79540: mov             x1, x0
    // 0xb79544: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb79544: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb79548: ldr             x4, [x4, #0xaa0]
    // 0xb7954c: r0 = copyWith()
    //     0xb7954c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb79550: stur            x0, [fp, #-0x10]
    // 0xb79554: r0 = Text()
    //     0xb79554: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb79558: mov             x1, x0
    // 0xb7955c: r0 = "Free"
    //     0xb7955c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb79560: ldr             x0, [x0, #0x668]
    // 0xb79564: stur            x1, [fp, #-0x30]
    // 0xb79568: StoreField: r1->field_b = r0
    //     0xb79568: stur            w0, [x1, #0xb]
    // 0xb7956c: ldur            x2, [fp, #-0x10]
    // 0xb79570: StoreField: r1->field_13 = r2
    //     0xb79570: stur            w2, [x1, #0x13]
    // 0xb79574: r0 = Center()
    //     0xb79574: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb79578: mov             x1, x0
    // 0xb7957c: r0 = Instance_Alignment
    //     0xb7957c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb79580: ldr             x0, [x0, #0xb10]
    // 0xb79584: stur            x1, [fp, #-0x10]
    // 0xb79588: StoreField: r1->field_f = r0
    //     0xb79588: stur            w0, [x1, #0xf]
    // 0xb7958c: ldur            x0, [fp, #-0x30]
    // 0xb79590: StoreField: r1->field_b = r0
    //     0xb79590: stur            w0, [x1, #0xb]
    // 0xb79594: r0 = RotatedBox()
    //     0xb79594: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xb79598: mov             x1, x0
    // 0xb7959c: r0 = -1
    //     0xb7959c: movn            x0, #0
    // 0xb795a0: stur            x1, [fp, #-0x30]
    // 0xb795a4: StoreField: r1->field_f = r0
    //     0xb795a4: stur            x0, [x1, #0xf]
    // 0xb795a8: ldur            x0, [fp, #-0x10]
    // 0xb795ac: StoreField: r1->field_b = r0
    //     0xb795ac: stur            w0, [x1, #0xb]
    // 0xb795b0: r0 = Container()
    //     0xb795b0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb795b4: stur            x0, [fp, #-0x10]
    // 0xb795b8: r16 = 24.000000
    //     0xb795b8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xb795bc: ldr             x16, [x16, #0xba8]
    // 0xb795c0: r30 = 56.000000
    //     0xb795c0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb795c4: ldr             lr, [lr, #0xb78]
    // 0xb795c8: stp             lr, x16, [SP, #0x10]
    // 0xb795cc: ldur            x16, [fp, #-0x18]
    // 0xb795d0: ldur            lr, [fp, #-0x30]
    // 0xb795d4: stp             lr, x16, [SP]
    // 0xb795d8: mov             x1, x0
    // 0xb795dc: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb795dc: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb795e0: ldr             x4, [x4, #0x870]
    // 0xb795e4: r0 = Container()
    //     0xb795e4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb795e8: ldur            x2, [fp, #-0x48]
    // 0xb795ec: LoadField: r0 = r2->field_f
    //     0xb795ec: ldur            w0, [x2, #0xf]
    // 0xb795f0: DecompressPointer r0
    //     0xb795f0: add             x0, x0, HEAP, lsl #32
    // 0xb795f4: LoadField: r1 = r0->field_b
    //     0xb795f4: ldur            w1, [x0, #0xb]
    // 0xb795f8: DecompressPointer r1
    //     0xb795f8: add             x1, x1, HEAP, lsl #32
    // 0xb795fc: cmp             w1, NULL
    // 0xb79600: b.eq            #0xb79d4c
    // 0xb79604: LoadField: r0 = r1->field_b
    //     0xb79604: ldur            w0, [x1, #0xb]
    // 0xb79608: DecompressPointer r0
    //     0xb79608: add             x0, x0, HEAP, lsl #32
    // 0xb7960c: LoadField: r3 = r0->field_b
    //     0xb7960c: ldur            w3, [x0, #0xb]
    // 0xb79610: DecompressPointer r3
    //     0xb79610: add             x3, x3, HEAP, lsl #32
    // 0xb79614: cmp             w3, NULL
    // 0xb79618: b.ne            #0xb79628
    // 0xb7961c: ldur            x4, [fp, #-0x20]
    // 0xb79620: r0 = Null
    //     0xb79620: mov             x0, NULL
    // 0xb79624: b               #0xb7968c
    // 0xb79628: ldur            x4, [fp, #-0x20]
    // 0xb7962c: LoadField: r0 = r3->field_b
    //     0xb7962c: ldur            w0, [x3, #0xb]
    // 0xb79630: r1 = LoadInt32Instr(r0)
    //     0xb79630: sbfx            x1, x0, #1, #0x1f
    // 0xb79634: mov             x0, x1
    // 0xb79638: mov             x1, x4
    // 0xb7963c: cmp             x1, x0
    // 0xb79640: b.hs            #0xb79d50
    // 0xb79644: LoadField: r0 = r3->field_f
    //     0xb79644: ldur            w0, [x3, #0xf]
    // 0xb79648: DecompressPointer r0
    //     0xb79648: add             x0, x0, HEAP, lsl #32
    // 0xb7964c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb7964c: add             x16, x0, x4, lsl #2
    //     0xb79650: ldur            w1, [x16, #0xf]
    // 0xb79654: DecompressPointer r1
    //     0xb79654: add             x1, x1, HEAP, lsl #32
    // 0xb79658: cmp             w1, NULL
    // 0xb7965c: b.ne            #0xb79668
    // 0xb79660: r0 = Null
    //     0xb79660: mov             x0, NULL
    // 0xb79664: b               #0xb7968c
    // 0xb79668: LoadField: r0 = r1->field_7f
    //     0xb79668: ldur            w0, [x1, #0x7f]
    // 0xb7966c: DecompressPointer r0
    //     0xb7966c: add             x0, x0, HEAP, lsl #32
    // 0xb79670: cmp             w0, NULL
    // 0xb79674: b.ne            #0xb79680
    // 0xb79678: r0 = Null
    //     0xb79678: mov             x0, NULL
    // 0xb7967c: b               #0xb7968c
    // 0xb79680: LoadField: r1 = r0->field_7
    //     0xb79680: ldur            w1, [x0, #7]
    // 0xb79684: DecompressPointer r1
    //     0xb79684: add             x1, x1, HEAP, lsl #32
    // 0xb79688: mov             x0, x1
    // 0xb7968c: cmp             w0, NULL
    // 0xb79690: b.ne            #0xb79698
    // 0xb79694: r0 = ""
    //     0xb79694: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb79698: stur            x0, [fp, #-0x18]
    // 0xb7969c: r0 = CachedNetworkImage()
    //     0xb7969c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb796a0: stur            x0, [fp, #-0x30]
    // 0xb796a4: r16 = 56.000000
    //     0xb796a4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb796a8: ldr             x16, [x16, #0xb78]
    // 0xb796ac: r30 = 56.000000
    //     0xb796ac: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb796b0: ldr             lr, [lr, #0xb78]
    // 0xb796b4: stp             lr, x16, [SP, #8]
    // 0xb796b8: r16 = Instance_BoxFit
    //     0xb796b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb796bc: ldr             x16, [x16, #0x118]
    // 0xb796c0: str             x16, [SP]
    // 0xb796c4: mov             x1, x0
    // 0xb796c8: ldur            x2, [fp, #-0x18]
    // 0xb796cc: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x3, width, 0x2, null]
    //     0xb796cc: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb40] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0xb796d0: ldr             x4, [x4, #0xb40]
    // 0xb796d4: r0 = CachedNetworkImage()
    //     0xb796d4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb796d8: ldur            x2, [fp, #-0x48]
    // 0xb796dc: LoadField: r0 = r2->field_f
    //     0xb796dc: ldur            w0, [x2, #0xf]
    // 0xb796e0: DecompressPointer r0
    //     0xb796e0: add             x0, x0, HEAP, lsl #32
    // 0xb796e4: LoadField: r1 = r0->field_b
    //     0xb796e4: ldur            w1, [x0, #0xb]
    // 0xb796e8: DecompressPointer r1
    //     0xb796e8: add             x1, x1, HEAP, lsl #32
    // 0xb796ec: cmp             w1, NULL
    // 0xb796f0: b.eq            #0xb79d54
    // 0xb796f4: LoadField: r0 = r1->field_b
    //     0xb796f4: ldur            w0, [x1, #0xb]
    // 0xb796f8: DecompressPointer r0
    //     0xb796f8: add             x0, x0, HEAP, lsl #32
    // 0xb796fc: LoadField: r3 = r0->field_b
    //     0xb796fc: ldur            w3, [x0, #0xb]
    // 0xb79700: DecompressPointer r3
    //     0xb79700: add             x3, x3, HEAP, lsl #32
    // 0xb79704: cmp             w3, NULL
    // 0xb79708: b.ne            #0xb79718
    // 0xb7970c: ldur            x4, [fp, #-0x20]
    // 0xb79710: r0 = Null
    //     0xb79710: mov             x0, NULL
    // 0xb79714: b               #0xb7977c
    // 0xb79718: ldur            x4, [fp, #-0x20]
    // 0xb7971c: LoadField: r0 = r3->field_b
    //     0xb7971c: ldur            w0, [x3, #0xb]
    // 0xb79720: r1 = LoadInt32Instr(r0)
    //     0xb79720: sbfx            x1, x0, #1, #0x1f
    // 0xb79724: mov             x0, x1
    // 0xb79728: mov             x1, x4
    // 0xb7972c: cmp             x1, x0
    // 0xb79730: b.hs            #0xb79d58
    // 0xb79734: LoadField: r0 = r3->field_f
    //     0xb79734: ldur            w0, [x3, #0xf]
    // 0xb79738: DecompressPointer r0
    //     0xb79738: add             x0, x0, HEAP, lsl #32
    // 0xb7973c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb7973c: add             x16, x0, x4, lsl #2
    //     0xb79740: ldur            w1, [x16, #0xf]
    // 0xb79744: DecompressPointer r1
    //     0xb79744: add             x1, x1, HEAP, lsl #32
    // 0xb79748: cmp             w1, NULL
    // 0xb7974c: b.ne            #0xb79758
    // 0xb79750: r0 = Null
    //     0xb79750: mov             x0, NULL
    // 0xb79754: b               #0xb7977c
    // 0xb79758: LoadField: r0 = r1->field_7f
    //     0xb79758: ldur            w0, [x1, #0x7f]
    // 0xb7975c: DecompressPointer r0
    //     0xb7975c: add             x0, x0, HEAP, lsl #32
    // 0xb79760: cmp             w0, NULL
    // 0xb79764: b.ne            #0xb79770
    // 0xb79768: r0 = Null
    //     0xb79768: mov             x0, NULL
    // 0xb7976c: b               #0xb7977c
    // 0xb79770: LoadField: r1 = r0->field_b
    //     0xb79770: ldur            w1, [x0, #0xb]
    // 0xb79774: DecompressPointer r1
    //     0xb79774: add             x1, x1, HEAP, lsl #32
    // 0xb79778: mov             x0, x1
    // 0xb7977c: cmp             w0, NULL
    // 0xb79780: b.ne            #0xb79788
    // 0xb79784: r0 = ""
    //     0xb79784: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb79788: ldr             x1, [fp, #0x18]
    // 0xb7978c: stur            x0, [fp, #-0x18]
    // 0xb79790: r0 = of()
    //     0xb79790: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb79794: LoadField: r1 = r0->field_87
    //     0xb79794: ldur            w1, [x0, #0x87]
    // 0xb79798: DecompressPointer r1
    //     0xb79798: add             x1, x1, HEAP, lsl #32
    // 0xb7979c: LoadField: r0 = r1->field_7
    //     0xb7979c: ldur            w0, [x1, #7]
    // 0xb797a0: DecompressPointer r0
    //     0xb797a0: add             x0, x0, HEAP, lsl #32
    // 0xb797a4: r16 = 12.000000
    //     0xb797a4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb797a8: ldr             x16, [x16, #0x9e8]
    // 0xb797ac: r30 = Instance_Color
    //     0xb797ac: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb797b0: stp             lr, x16, [SP]
    // 0xb797b4: mov             x1, x0
    // 0xb797b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb797b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb797bc: ldr             x4, [x4, #0xaa0]
    // 0xb797c0: r0 = copyWith()
    //     0xb797c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb797c4: stur            x0, [fp, #-0x38]
    // 0xb797c8: r0 = Text()
    //     0xb797c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb797cc: mov             x1, x0
    // 0xb797d0: ldur            x0, [fp, #-0x18]
    // 0xb797d4: stur            x1, [fp, #-0x40]
    // 0xb797d8: StoreField: r1->field_b = r0
    //     0xb797d8: stur            w0, [x1, #0xb]
    // 0xb797dc: ldur            x0, [fp, #-0x38]
    // 0xb797e0: StoreField: r1->field_13 = r0
    //     0xb797e0: stur            w0, [x1, #0x13]
    // 0xb797e4: r0 = Instance_TextOverflow
    //     0xb797e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb797e8: ldr             x0, [x0, #0xe10]
    // 0xb797ec: StoreField: r1->field_2b = r0
    //     0xb797ec: stur            w0, [x1, #0x2b]
    // 0xb797f0: r0 = 2
    //     0xb797f0: movz            x0, #0x2
    // 0xb797f4: StoreField: r1->field_37 = r0
    //     0xb797f4: stur            w0, [x1, #0x37]
    // 0xb797f8: r0 = SizedBox()
    //     0xb797f8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb797fc: mov             x2, x0
    // 0xb79800: r0 = 150.000000
    //     0xb79800: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb79804: ldr             x0, [x0, #0x690]
    // 0xb79808: stur            x2, [fp, #-0x18]
    // 0xb7980c: StoreField: r2->field_f = r0
    //     0xb7980c: stur            w0, [x2, #0xf]
    // 0xb79810: ldur            x0, [fp, #-0x40]
    // 0xb79814: StoreField: r2->field_b = r0
    //     0xb79814: stur            w0, [x2, #0xb]
    // 0xb79818: ldr             x1, [fp, #0x18]
    // 0xb7981c: r0 = of()
    //     0xb7981c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb79820: LoadField: r1 = r0->field_87
    //     0xb79820: ldur            w1, [x0, #0x87]
    // 0xb79824: DecompressPointer r1
    //     0xb79824: add             x1, x1, HEAP, lsl #32
    // 0xb79828: LoadField: r0 = r1->field_2b
    //     0xb79828: ldur            w0, [x1, #0x2b]
    // 0xb7982c: DecompressPointer r0
    //     0xb7982c: add             x0, x0, HEAP, lsl #32
    // 0xb79830: r16 = 12.000000
    //     0xb79830: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb79834: ldr             x16, [x16, #0x9e8]
    // 0xb79838: r30 = Instance_Color
    //     0xb79838: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb7983c: stp             lr, x16, [SP]
    // 0xb79840: mov             x1, x0
    // 0xb79844: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb79844: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb79848: ldr             x4, [x4, #0xaa0]
    // 0xb7984c: r0 = copyWith()
    //     0xb7984c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb79850: stur            x0, [fp, #-0x38]
    // 0xb79854: r0 = Text()
    //     0xb79854: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb79858: mov             x2, x0
    // 0xb7985c: r0 = "Free"
    //     0xb7985c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb79860: ldr             x0, [x0, #0x668]
    // 0xb79864: stur            x2, [fp, #-0x40]
    // 0xb79868: StoreField: r2->field_b = r0
    //     0xb79868: stur            w0, [x2, #0xb]
    // 0xb7986c: ldur            x0, [fp, #-0x38]
    // 0xb79870: StoreField: r2->field_13 = r0
    //     0xb79870: stur            w0, [x2, #0x13]
    // 0xb79874: ldur            x0, [fp, #-0x48]
    // 0xb79878: LoadField: r1 = r0->field_f
    //     0xb79878: ldur            w1, [x0, #0xf]
    // 0xb7987c: DecompressPointer r1
    //     0xb7987c: add             x1, x1, HEAP, lsl #32
    // 0xb79880: LoadField: r0 = r1->field_b
    //     0xb79880: ldur            w0, [x1, #0xb]
    // 0xb79884: DecompressPointer r0
    //     0xb79884: add             x0, x0, HEAP, lsl #32
    // 0xb79888: cmp             w0, NULL
    // 0xb7988c: b.eq            #0xb79d5c
    // 0xb79890: LoadField: r1 = r0->field_b
    //     0xb79890: ldur            w1, [x0, #0xb]
    // 0xb79894: DecompressPointer r1
    //     0xb79894: add             x1, x1, HEAP, lsl #32
    // 0xb79898: LoadField: r3 = r1->field_b
    //     0xb79898: ldur            w3, [x1, #0xb]
    // 0xb7989c: DecompressPointer r3
    //     0xb7989c: add             x3, x3, HEAP, lsl #32
    // 0xb798a0: cmp             w3, NULL
    // 0xb798a4: b.ne            #0xb798b0
    // 0xb798a8: r0 = Null
    //     0xb798a8: mov             x0, NULL
    // 0xb798ac: b               #0xb79914
    // 0xb798b0: ldur            x4, [fp, #-0x20]
    // 0xb798b4: LoadField: r0 = r3->field_b
    //     0xb798b4: ldur            w0, [x3, #0xb]
    // 0xb798b8: r1 = LoadInt32Instr(r0)
    //     0xb798b8: sbfx            x1, x0, #1, #0x1f
    // 0xb798bc: mov             x0, x1
    // 0xb798c0: mov             x1, x4
    // 0xb798c4: cmp             x1, x0
    // 0xb798c8: b.hs            #0xb79d60
    // 0xb798cc: LoadField: r0 = r3->field_f
    //     0xb798cc: ldur            w0, [x3, #0xf]
    // 0xb798d0: DecompressPointer r0
    //     0xb798d0: add             x0, x0, HEAP, lsl #32
    // 0xb798d4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb798d4: add             x16, x0, x4, lsl #2
    //     0xb798d8: ldur            w1, [x16, #0xf]
    // 0xb798dc: DecompressPointer r1
    //     0xb798dc: add             x1, x1, HEAP, lsl #32
    // 0xb798e0: cmp             w1, NULL
    // 0xb798e4: b.ne            #0xb798f0
    // 0xb798e8: r0 = Null
    //     0xb798e8: mov             x0, NULL
    // 0xb798ec: b               #0xb79914
    // 0xb798f0: LoadField: r0 = r1->field_7f
    //     0xb798f0: ldur            w0, [x1, #0x7f]
    // 0xb798f4: DecompressPointer r0
    //     0xb798f4: add             x0, x0, HEAP, lsl #32
    // 0xb798f8: cmp             w0, NULL
    // 0xb798fc: b.ne            #0xb79908
    // 0xb79900: r0 = Null
    //     0xb79900: mov             x0, NULL
    // 0xb79904: b               #0xb79914
    // 0xb79908: LoadField: r1 = r0->field_13
    //     0xb79908: ldur            w1, [x0, #0x13]
    // 0xb7990c: DecompressPointer r1
    //     0xb7990c: add             x1, x1, HEAP, lsl #32
    // 0xb79910: mov             x0, x1
    // 0xb79914: cmp             w0, NULL
    // 0xb79918: b.ne            #0xb79924
    // 0xb7991c: r5 = ""
    //     0xb7991c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb79920: b               #0xb79928
    // 0xb79924: mov             x5, x0
    // 0xb79928: ldur            x4, [fp, #-0x10]
    // 0xb7992c: ldur            x3, [fp, #-0x30]
    // 0xb79930: ldur            x0, [fp, #-0x18]
    // 0xb79934: ldr             x1, [fp, #0x18]
    // 0xb79938: stur            x5, [fp, #-0x38]
    // 0xb7993c: r0 = of()
    //     0xb7993c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb79940: LoadField: r1 = r0->field_87
    //     0xb79940: ldur            w1, [x0, #0x87]
    // 0xb79944: DecompressPointer r1
    //     0xb79944: add             x1, x1, HEAP, lsl #32
    // 0xb79948: LoadField: r0 = r1->field_2b
    //     0xb79948: ldur            w0, [x1, #0x2b]
    // 0xb7994c: DecompressPointer r0
    //     0xb7994c: add             x0, x0, HEAP, lsl #32
    // 0xb79950: r16 = 12.000000
    //     0xb79950: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb79954: ldr             x16, [x16, #0x9e8]
    // 0xb79958: r30 = Instance_TextDecoration
    //     0xb79958: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb7995c: ldr             lr, [lr, #0xe30]
    // 0xb79960: stp             lr, x16, [SP]
    // 0xb79964: mov             x1, x0
    // 0xb79968: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xb79968: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xb7996c: ldr             x4, [x4, #0x698]
    // 0xb79970: r0 = copyWith()
    //     0xb79970: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb79974: stur            x0, [fp, #-0x48]
    // 0xb79978: r0 = Text()
    //     0xb79978: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7997c: mov             x3, x0
    // 0xb79980: ldur            x0, [fp, #-0x38]
    // 0xb79984: stur            x3, [fp, #-0x58]
    // 0xb79988: StoreField: r3->field_b = r0
    //     0xb79988: stur            w0, [x3, #0xb]
    // 0xb7998c: ldur            x0, [fp, #-0x48]
    // 0xb79990: StoreField: r3->field_13 = r0
    //     0xb79990: stur            w0, [x3, #0x13]
    // 0xb79994: r1 = Null
    //     0xb79994: mov             x1, NULL
    // 0xb79998: r2 = 6
    //     0xb79998: movz            x2, #0x6
    // 0xb7999c: r0 = AllocateArray()
    //     0xb7999c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb799a0: mov             x2, x0
    // 0xb799a4: ldur            x0, [fp, #-0x40]
    // 0xb799a8: stur            x2, [fp, #-0x38]
    // 0xb799ac: StoreField: r2->field_f = r0
    //     0xb799ac: stur            w0, [x2, #0xf]
    // 0xb799b0: r16 = Instance_SizedBox
    //     0xb799b0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb799b4: ldr             x16, [x16, #0xa50]
    // 0xb799b8: StoreField: r2->field_13 = r16
    //     0xb799b8: stur            w16, [x2, #0x13]
    // 0xb799bc: ldur            x0, [fp, #-0x58]
    // 0xb799c0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb799c0: stur            w0, [x2, #0x17]
    // 0xb799c4: r1 = <Widget>
    //     0xb799c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb799c8: r0 = AllocateGrowableArray()
    //     0xb799c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb799cc: mov             x1, x0
    // 0xb799d0: ldur            x0, [fp, #-0x38]
    // 0xb799d4: stur            x1, [fp, #-0x40]
    // 0xb799d8: StoreField: r1->field_f = r0
    //     0xb799d8: stur            w0, [x1, #0xf]
    // 0xb799dc: r2 = 6
    //     0xb799dc: movz            x2, #0x6
    // 0xb799e0: StoreField: r1->field_b = r2
    //     0xb799e0: stur            w2, [x1, #0xb]
    // 0xb799e4: r0 = Row()
    //     0xb799e4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb799e8: mov             x3, x0
    // 0xb799ec: r0 = Instance_Axis
    //     0xb799ec: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb799f0: stur            x3, [fp, #-0x38]
    // 0xb799f4: StoreField: r3->field_f = r0
    //     0xb799f4: stur            w0, [x3, #0xf]
    // 0xb799f8: r4 = Instance_MainAxisAlignment
    //     0xb799f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb799fc: ldr             x4, [x4, #0xa08]
    // 0xb79a00: StoreField: r3->field_13 = r4
    //     0xb79a00: stur            w4, [x3, #0x13]
    // 0xb79a04: r5 = Instance_MainAxisSize
    //     0xb79a04: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb79a08: ldr             x5, [x5, #0xa10]
    // 0xb79a0c: ArrayStore: r3[0] = r5  ; List_4
    //     0xb79a0c: stur            w5, [x3, #0x17]
    // 0xb79a10: r6 = Instance_CrossAxisAlignment
    //     0xb79a10: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb79a14: ldr             x6, [x6, #0xa18]
    // 0xb79a18: StoreField: r3->field_1b = r6
    //     0xb79a18: stur            w6, [x3, #0x1b]
    // 0xb79a1c: r7 = Instance_VerticalDirection
    //     0xb79a1c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb79a20: ldr             x7, [x7, #0xa20]
    // 0xb79a24: StoreField: r3->field_23 = r7
    //     0xb79a24: stur            w7, [x3, #0x23]
    // 0xb79a28: r8 = Instance_Clip
    //     0xb79a28: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb79a2c: ldr             x8, [x8, #0x38]
    // 0xb79a30: StoreField: r3->field_2b = r8
    //     0xb79a30: stur            w8, [x3, #0x2b]
    // 0xb79a34: StoreField: r3->field_2f = rZR
    //     0xb79a34: stur            xzr, [x3, #0x2f]
    // 0xb79a38: ldur            x1, [fp, #-0x40]
    // 0xb79a3c: StoreField: r3->field_b = r1
    //     0xb79a3c: stur            w1, [x3, #0xb]
    // 0xb79a40: r1 = Null
    //     0xb79a40: mov             x1, NULL
    // 0xb79a44: r2 = 6
    //     0xb79a44: movz            x2, #0x6
    // 0xb79a48: r0 = AllocateArray()
    //     0xb79a48: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb79a4c: mov             x2, x0
    // 0xb79a50: ldur            x0, [fp, #-0x18]
    // 0xb79a54: stur            x2, [fp, #-0x40]
    // 0xb79a58: StoreField: r2->field_f = r0
    //     0xb79a58: stur            w0, [x2, #0xf]
    // 0xb79a5c: r16 = Instance_SizedBox
    //     0xb79a5c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb79a60: ldr             x16, [x16, #0xc70]
    // 0xb79a64: StoreField: r2->field_13 = r16
    //     0xb79a64: stur            w16, [x2, #0x13]
    // 0xb79a68: ldur            x0, [fp, #-0x38]
    // 0xb79a6c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb79a6c: stur            w0, [x2, #0x17]
    // 0xb79a70: r1 = <Widget>
    //     0xb79a70: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb79a74: r0 = AllocateGrowableArray()
    //     0xb79a74: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb79a78: mov             x1, x0
    // 0xb79a7c: ldur            x0, [fp, #-0x40]
    // 0xb79a80: stur            x1, [fp, #-0x18]
    // 0xb79a84: StoreField: r1->field_f = r0
    //     0xb79a84: stur            w0, [x1, #0xf]
    // 0xb79a88: r2 = 6
    //     0xb79a88: movz            x2, #0x6
    // 0xb79a8c: StoreField: r1->field_b = r2
    //     0xb79a8c: stur            w2, [x1, #0xb]
    // 0xb79a90: r0 = Column()
    //     0xb79a90: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb79a94: mov             x1, x0
    // 0xb79a98: r0 = Instance_Axis
    //     0xb79a98: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb79a9c: stur            x1, [fp, #-0x38]
    // 0xb79aa0: StoreField: r1->field_f = r0
    //     0xb79aa0: stur            w0, [x1, #0xf]
    // 0xb79aa4: r2 = Instance_MainAxisAlignment
    //     0xb79aa4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb79aa8: ldr             x2, [x2, #0xa08]
    // 0xb79aac: StoreField: r1->field_13 = r2
    //     0xb79aac: stur            w2, [x1, #0x13]
    // 0xb79ab0: r3 = Instance_MainAxisSize
    //     0xb79ab0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb79ab4: ldr             x3, [x3, #0xa10]
    // 0xb79ab8: ArrayStore: r1[0] = r3  ; List_4
    //     0xb79ab8: stur            w3, [x1, #0x17]
    // 0xb79abc: r4 = Instance_CrossAxisAlignment
    //     0xb79abc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb79ac0: ldr             x4, [x4, #0x890]
    // 0xb79ac4: StoreField: r1->field_1b = r4
    //     0xb79ac4: stur            w4, [x1, #0x1b]
    // 0xb79ac8: r4 = Instance_VerticalDirection
    //     0xb79ac8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb79acc: ldr             x4, [x4, #0xa20]
    // 0xb79ad0: StoreField: r1->field_23 = r4
    //     0xb79ad0: stur            w4, [x1, #0x23]
    // 0xb79ad4: r5 = Instance_Clip
    //     0xb79ad4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb79ad8: ldr             x5, [x5, #0x38]
    // 0xb79adc: StoreField: r1->field_2b = r5
    //     0xb79adc: stur            w5, [x1, #0x2b]
    // 0xb79ae0: StoreField: r1->field_2f = rZR
    //     0xb79ae0: stur            xzr, [x1, #0x2f]
    // 0xb79ae4: ldur            x6, [fp, #-0x18]
    // 0xb79ae8: StoreField: r1->field_b = r6
    //     0xb79ae8: stur            w6, [x1, #0xb]
    // 0xb79aec: r0 = Padding()
    //     0xb79aec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb79af0: mov             x2, x0
    // 0xb79af4: r0 = Instance_EdgeInsets
    //     0xb79af4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb79af8: ldr             x0, [x0, #0xa78]
    // 0xb79afc: stur            x2, [fp, #-0x18]
    // 0xb79b00: StoreField: r2->field_f = r0
    //     0xb79b00: stur            w0, [x2, #0xf]
    // 0xb79b04: ldur            x0, [fp, #-0x38]
    // 0xb79b08: StoreField: r2->field_b = r0
    //     0xb79b08: stur            w0, [x2, #0xb]
    // 0xb79b0c: r1 = <FlexParentData>
    //     0xb79b0c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb79b10: ldr             x1, [x1, #0xe00]
    // 0xb79b14: r0 = Expanded()
    //     0xb79b14: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb79b18: mov             x3, x0
    // 0xb79b1c: r0 = 1
    //     0xb79b1c: movz            x0, #0x1
    // 0xb79b20: stur            x3, [fp, #-0x38]
    // 0xb79b24: StoreField: r3->field_13 = r0
    //     0xb79b24: stur            x0, [x3, #0x13]
    // 0xb79b28: r0 = Instance_FlexFit
    //     0xb79b28: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb79b2c: ldr             x0, [x0, #0xe08]
    // 0xb79b30: StoreField: r3->field_1b = r0
    //     0xb79b30: stur            w0, [x3, #0x1b]
    // 0xb79b34: ldur            x0, [fp, #-0x18]
    // 0xb79b38: StoreField: r3->field_b = r0
    //     0xb79b38: stur            w0, [x3, #0xb]
    // 0xb79b3c: r1 = Null
    //     0xb79b3c: mov             x1, NULL
    // 0xb79b40: r2 = 6
    //     0xb79b40: movz            x2, #0x6
    // 0xb79b44: r0 = AllocateArray()
    //     0xb79b44: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb79b48: mov             x2, x0
    // 0xb79b4c: ldur            x0, [fp, #-0x10]
    // 0xb79b50: stur            x2, [fp, #-0x18]
    // 0xb79b54: StoreField: r2->field_f = r0
    //     0xb79b54: stur            w0, [x2, #0xf]
    // 0xb79b58: ldur            x0, [fp, #-0x30]
    // 0xb79b5c: StoreField: r2->field_13 = r0
    //     0xb79b5c: stur            w0, [x2, #0x13]
    // 0xb79b60: ldur            x0, [fp, #-0x38]
    // 0xb79b64: ArrayStore: r2[0] = r0  ; List_4
    //     0xb79b64: stur            w0, [x2, #0x17]
    // 0xb79b68: r1 = <Widget>
    //     0xb79b68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb79b6c: r0 = AllocateGrowableArray()
    //     0xb79b6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb79b70: mov             x1, x0
    // 0xb79b74: ldur            x0, [fp, #-0x18]
    // 0xb79b78: stur            x1, [fp, #-0x10]
    // 0xb79b7c: StoreField: r1->field_f = r0
    //     0xb79b7c: stur            w0, [x1, #0xf]
    // 0xb79b80: r0 = 6
    //     0xb79b80: movz            x0, #0x6
    // 0xb79b84: StoreField: r1->field_b = r0
    //     0xb79b84: stur            w0, [x1, #0xb]
    // 0xb79b88: r0 = Row()
    //     0xb79b88: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb79b8c: mov             x1, x0
    // 0xb79b90: r0 = Instance_Axis
    //     0xb79b90: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb79b94: stur            x1, [fp, #-0x18]
    // 0xb79b98: StoreField: r1->field_f = r0
    //     0xb79b98: stur            w0, [x1, #0xf]
    // 0xb79b9c: r0 = Instance_MainAxisAlignment
    //     0xb79b9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb79ba0: ldr             x0, [x0, #0xa08]
    // 0xb79ba4: StoreField: r1->field_13 = r0
    //     0xb79ba4: stur            w0, [x1, #0x13]
    // 0xb79ba8: r2 = Instance_MainAxisSize
    //     0xb79ba8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb79bac: ldr             x2, [x2, #0xa10]
    // 0xb79bb0: ArrayStore: r1[0] = r2  ; List_4
    //     0xb79bb0: stur            w2, [x1, #0x17]
    // 0xb79bb4: r3 = Instance_CrossAxisAlignment
    //     0xb79bb4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb79bb8: ldr             x3, [x3, #0xa18]
    // 0xb79bbc: StoreField: r1->field_1b = r3
    //     0xb79bbc: stur            w3, [x1, #0x1b]
    // 0xb79bc0: r4 = Instance_VerticalDirection
    //     0xb79bc0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb79bc4: ldr             x4, [x4, #0xa20]
    // 0xb79bc8: StoreField: r1->field_23 = r4
    //     0xb79bc8: stur            w4, [x1, #0x23]
    // 0xb79bcc: r5 = Instance_Clip
    //     0xb79bcc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb79bd0: ldr             x5, [x5, #0x38]
    // 0xb79bd4: StoreField: r1->field_2b = r5
    //     0xb79bd4: stur            w5, [x1, #0x2b]
    // 0xb79bd8: StoreField: r1->field_2f = rZR
    //     0xb79bd8: stur            xzr, [x1, #0x2f]
    // 0xb79bdc: ldur            x6, [fp, #-0x10]
    // 0xb79be0: StoreField: r1->field_b = r6
    //     0xb79be0: stur            w6, [x1, #0xb]
    // 0xb79be4: r0 = Container()
    //     0xb79be4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb79be8: stur            x0, [fp, #-0x10]
    // 0xb79bec: ldur            x16, [fp, #-0x28]
    // 0xb79bf0: ldur            lr, [fp, #-0x18]
    // 0xb79bf4: stp             lr, x16, [SP]
    // 0xb79bf8: mov             x1, x0
    // 0xb79bfc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb79bfc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb79c00: ldr             x4, [x4, #0x88]
    // 0xb79c04: r0 = Container()
    //     0xb79c04: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb79c08: r0 = Padding()
    //     0xb79c08: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb79c0c: mov             x1, x0
    // 0xb79c10: r0 = Instance_EdgeInsets
    //     0xb79c10: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb79c14: ldr             x0, [x0, #0x770]
    // 0xb79c18: StoreField: r1->field_f = r0
    //     0xb79c18: stur            w0, [x1, #0xf]
    // 0xb79c1c: ldur            x0, [fp, #-0x10]
    // 0xb79c20: StoreField: r1->field_b = r0
    //     0xb79c20: stur            w0, [x1, #0xb]
    // 0xb79c24: mov             x2, x1
    // 0xb79c28: ldur            x0, [fp, #-0x50]
    // 0xb79c2c: ldur            x1, [fp, #-8]
    // 0xb79c30: stur            x2, [fp, #-0x10]
    // 0xb79c34: r0 = Visibility()
    //     0xb79c34: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb79c38: mov             x3, x0
    // 0xb79c3c: ldur            x0, [fp, #-0x10]
    // 0xb79c40: stur            x3, [fp, #-0x18]
    // 0xb79c44: StoreField: r3->field_b = r0
    //     0xb79c44: stur            w0, [x3, #0xb]
    // 0xb79c48: r0 = Instance_SizedBox
    //     0xb79c48: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb79c4c: StoreField: r3->field_f = r0
    //     0xb79c4c: stur            w0, [x3, #0xf]
    // 0xb79c50: ldur            x0, [fp, #-8]
    // 0xb79c54: StoreField: r3->field_13 = r0
    //     0xb79c54: stur            w0, [x3, #0x13]
    // 0xb79c58: r0 = false
    //     0xb79c58: add             x0, NULL, #0x30  ; false
    // 0xb79c5c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb79c5c: stur            w0, [x3, #0x17]
    // 0xb79c60: StoreField: r3->field_1b = r0
    //     0xb79c60: stur            w0, [x3, #0x1b]
    // 0xb79c64: StoreField: r3->field_1f = r0
    //     0xb79c64: stur            w0, [x3, #0x1f]
    // 0xb79c68: StoreField: r3->field_23 = r0
    //     0xb79c68: stur            w0, [x3, #0x23]
    // 0xb79c6c: StoreField: r3->field_27 = r0
    //     0xb79c6c: stur            w0, [x3, #0x27]
    // 0xb79c70: StoreField: r3->field_2b = r0
    //     0xb79c70: stur            w0, [x3, #0x2b]
    // 0xb79c74: r1 = Null
    //     0xb79c74: mov             x1, NULL
    // 0xb79c78: r2 = 4
    //     0xb79c78: movz            x2, #0x4
    // 0xb79c7c: r0 = AllocateArray()
    //     0xb79c7c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb79c80: mov             x2, x0
    // 0xb79c84: ldur            x0, [fp, #-0x50]
    // 0xb79c88: stur            x2, [fp, #-8]
    // 0xb79c8c: StoreField: r2->field_f = r0
    //     0xb79c8c: stur            w0, [x2, #0xf]
    // 0xb79c90: ldur            x0, [fp, #-0x18]
    // 0xb79c94: StoreField: r2->field_13 = r0
    //     0xb79c94: stur            w0, [x2, #0x13]
    // 0xb79c98: r1 = <Widget>
    //     0xb79c98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb79c9c: r0 = AllocateGrowableArray()
    //     0xb79c9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb79ca0: mov             x1, x0
    // 0xb79ca4: ldur            x0, [fp, #-8]
    // 0xb79ca8: stur            x1, [fp, #-0x10]
    // 0xb79cac: StoreField: r1->field_f = r0
    //     0xb79cac: stur            w0, [x1, #0xf]
    // 0xb79cb0: r0 = 4
    //     0xb79cb0: movz            x0, #0x4
    // 0xb79cb4: StoreField: r1->field_b = r0
    //     0xb79cb4: stur            w0, [x1, #0xb]
    // 0xb79cb8: r0 = Column()
    //     0xb79cb8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb79cbc: r1 = Instance_Axis
    //     0xb79cbc: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb79cc0: StoreField: r0->field_f = r1
    //     0xb79cc0: stur            w1, [x0, #0xf]
    // 0xb79cc4: r1 = Instance_MainAxisAlignment
    //     0xb79cc4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb79cc8: ldr             x1, [x1, #0xa08]
    // 0xb79ccc: StoreField: r0->field_13 = r1
    //     0xb79ccc: stur            w1, [x0, #0x13]
    // 0xb79cd0: r1 = Instance_MainAxisSize
    //     0xb79cd0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb79cd4: ldr             x1, [x1, #0xa10]
    // 0xb79cd8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb79cd8: stur            w1, [x0, #0x17]
    // 0xb79cdc: r1 = Instance_CrossAxisAlignment
    //     0xb79cdc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb79ce0: ldr             x1, [x1, #0xa18]
    // 0xb79ce4: StoreField: r0->field_1b = r1
    //     0xb79ce4: stur            w1, [x0, #0x1b]
    // 0xb79ce8: r1 = Instance_VerticalDirection
    //     0xb79ce8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb79cec: ldr             x1, [x1, #0xa20]
    // 0xb79cf0: StoreField: r0->field_23 = r1
    //     0xb79cf0: stur            w1, [x0, #0x23]
    // 0xb79cf4: r1 = Instance_Clip
    //     0xb79cf4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb79cf8: ldr             x1, [x1, #0x38]
    // 0xb79cfc: StoreField: r0->field_2b = r1
    //     0xb79cfc: stur            w1, [x0, #0x2b]
    // 0xb79d00: StoreField: r0->field_2f = rZR
    //     0xb79d00: stur            xzr, [x0, #0x2f]
    // 0xb79d04: ldur            x1, [fp, #-0x10]
    // 0xb79d08: StoreField: r0->field_b = r1
    //     0xb79d08: stur            w1, [x0, #0xb]
    // 0xb79d0c: LeaveFrame
    //     0xb79d0c: mov             SP, fp
    //     0xb79d10: ldp             fp, lr, [SP], #0x10
    // 0xb79d14: ret
    //     0xb79d14: ret             
    // 0xb79d18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb79d18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb79d1c: b               #0xb789b4
    // 0xb79d20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb79d20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb79d24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb79d24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb79d28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb79d28: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb79d2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb79d2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb79d30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb79d30: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb79d34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb79d34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb79d38: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb79d38: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb79d3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb79d3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb79d40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb79d40: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb79d44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb79d44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb79d48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb79d48: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb79d4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb79d4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb79d50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb79d50: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb79d54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb79d54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb79d58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb79d58: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb79d5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb79d5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb79d60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb79d60: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xb79d70, size: 0x7c
    // 0xb79d70: EnterFrame
    //     0xb79d70: stp             fp, lr, [SP, #-0x10]!
    //     0xb79d74: mov             fp, SP
    // 0xb79d78: AllocStack(0x8)
    //     0xb79d78: sub             SP, SP, #8
    // 0xb79d7c: SetupParameters()
    //     0xb79d7c: ldr             x0, [fp, #0x10]
    //     0xb79d80: ldur            w1, [x0, #0x17]
    //     0xb79d84: add             x1, x1, HEAP, lsl #32
    // 0xb79d88: CheckStackOverflow
    //     0xb79d88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb79d8c: cmp             SP, x16
    //     0xb79d90: b.ls            #0xb79de0
    // 0xb79d94: LoadField: r0 = r1->field_f
    //     0xb79d94: ldur            w0, [x1, #0xf]
    // 0xb79d98: DecompressPointer r0
    //     0xb79d98: add             x0, x0, HEAP, lsl #32
    // 0xb79d9c: LoadField: r1 = r0->field_b
    //     0xb79d9c: ldur            w1, [x0, #0xb]
    // 0xb79da0: DecompressPointer r1
    //     0xb79da0: add             x1, x1, HEAP, lsl #32
    // 0xb79da4: cmp             w1, NULL
    // 0xb79da8: b.eq            #0xb79de8
    // 0xb79dac: LoadField: r0 = r1->field_23
    //     0xb79dac: ldur            w0, [x1, #0x23]
    // 0xb79db0: DecompressPointer r0
    //     0xb79db0: add             x0, x0, HEAP, lsl #32
    // 0xb79db4: str             x0, [SP]
    // 0xb79db8: r4 = 0
    //     0xb79db8: movz            x4, #0
    // 0xb79dbc: ldr             x0, [SP]
    // 0xb79dc0: r16 = UnlinkedCall_0x613b5c
    //     0xb79dc0: add             x16, PP, #0x55, lsl #12  ; [pp+0x55a80] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb79dc4: add             x16, x16, #0xa80
    // 0xb79dc8: ldp             x5, lr, [x16]
    // 0xb79dcc: blr             lr
    // 0xb79dd0: r0 = Null
    //     0xb79dd0: mov             x0, NULL
    // 0xb79dd4: LeaveFrame
    //     0xb79dd4: mov             SP, fp
    //     0xb79dd8: ldp             fp, lr, [SP], #0x10
    // 0xb79ddc: ret
    //     0xb79ddc: ret             
    // 0xb79de0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb79de0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb79de4: b               #0xb79d94
    // 0xb79de8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb79de8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, double, Items, String) {
    // ** addr: 0xb79dec, size: 0x8c
    // 0xb79dec: EnterFrame
    //     0xb79dec: stp             fp, lr, [SP, #-0x10]!
    //     0xb79df0: mov             fp, SP
    // 0xb79df4: AllocStack(0x20)
    //     0xb79df4: sub             SP, SP, #0x20
    // 0xb79df8: SetupParameters()
    //     0xb79df8: ldr             x0, [fp, #0x28]
    //     0xb79dfc: ldur            w1, [x0, #0x17]
    //     0xb79e00: add             x1, x1, HEAP, lsl #32
    // 0xb79e04: CheckStackOverflow
    //     0xb79e04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb79e08: cmp             SP, x16
    //     0xb79e0c: b.ls            #0xb79e6c
    // 0xb79e10: LoadField: r0 = r1->field_f
    //     0xb79e10: ldur            w0, [x1, #0xf]
    // 0xb79e14: DecompressPointer r0
    //     0xb79e14: add             x0, x0, HEAP, lsl #32
    // 0xb79e18: LoadField: r1 = r0->field_b
    //     0xb79e18: ldur            w1, [x0, #0xb]
    // 0xb79e1c: DecompressPointer r1
    //     0xb79e1c: add             x1, x1, HEAP, lsl #32
    // 0xb79e20: cmp             w1, NULL
    // 0xb79e24: b.eq            #0xb79e74
    // 0xb79e28: LoadField: r0 = r1->field_27
    //     0xb79e28: ldur            w0, [x1, #0x27]
    // 0xb79e2c: DecompressPointer r0
    //     0xb79e2c: add             x0, x0, HEAP, lsl #32
    // 0xb79e30: ldr             x16, [fp, #0x20]
    // 0xb79e34: stp             x16, x0, [SP, #0x10]
    // 0xb79e38: ldr             x16, [fp, #0x18]
    // 0xb79e3c: ldr             lr, [fp, #0x10]
    // 0xb79e40: stp             lr, x16, [SP]
    // 0xb79e44: r4 = 0
    //     0xb79e44: movz            x4, #0
    // 0xb79e48: ldr             x0, [SP, #0x18]
    // 0xb79e4c: r16 = UnlinkedCall_0x613b5c
    //     0xb79e4c: add             x16, PP, #0x55, lsl #12  ; [pp+0x55a90] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb79e50: add             x16, x16, #0xa90
    // 0xb79e54: ldp             x5, lr, [x16]
    // 0xb79e58: blr             lr
    // 0xb79e5c: r0 = Null
    //     0xb79e5c: mov             x0, NULL
    // 0xb79e60: LeaveFrame
    //     0xb79e60: mov             SP, fp
    //     0xb79e64: ldp             fp, lr, [SP], #0x10
    // 0xb79e68: ret
    //     0xb79e68: ret             
    // 0xb79e6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb79e6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb79e70: b               #0xb79e10
    // 0xb79e74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb79e74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4066, size: 0x2c, field offset: 0xc
//   const constructor, 
class OrderItemCard extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f644, size: 0x24
    // 0xc7f644: EnterFrame
    //     0xc7f644: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f648: mov             fp, SP
    // 0xc7f64c: mov             x0, x1
    // 0xc7f650: r1 = <OrderItemCard>
    //     0xc7f650: add             x1, PP, #0x48, lsl #12  ; [pp+0x487f8] TypeArguments: <OrderItemCard>
    //     0xc7f654: ldr             x1, [x1, #0x7f8]
    // 0xc7f658: r0 = _OrderItemCardState()
    //     0xc7f658: bl              #0xc7f668  ; Allocate_OrderItemCardStateStub -> _OrderItemCardState (size=0x14)
    // 0xc7f65c: LeaveFrame
    //     0xc7f65c: mov             SP, fp
    //     0xc7f660: ldp             fp, lr, [SP], #0x10
    // 0xc7f664: ret
    //     0xc7f664: ret             
  }
}
