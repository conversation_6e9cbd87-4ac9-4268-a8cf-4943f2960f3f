// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart

// class id: 1049450, size: 0x8
class :: {
}

// class id: 3302, size: 0x24, field offset: 0x14
class _TestimonialCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb1e10c, size: 0x70
    // 0xb1e10c: EnterFrame
    //     0xb1e10c: stp             fp, lr, [SP, #-0x10]!
    //     0xb1e110: mov             fp, SP
    // 0xb1e114: ldr             x0, [fp, #0x10]
    // 0xb1e118: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb1e118: ldur            w1, [x0, #0x17]
    // 0xb1e11c: DecompressPointer r1
    //     0xb1e11c: add             x1, x1, HEAP, lsl #32
    // 0xb1e120: CheckStackOverflow
    //     0xb1e120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1e124: cmp             SP, x16
    //     0xb1e128: b.ls            #0xb1e174
    // 0xb1e12c: LoadField: r0 = r1->field_b
    //     0xb1e12c: ldur            w0, [x1, #0xb]
    // 0xb1e130: DecompressPointer r0
    //     0xb1e130: add             x0, x0, HEAP, lsl #32
    // 0xb1e134: LoadField: r2 = r0->field_f
    //     0xb1e134: ldur            w2, [x0, #0xf]
    // 0xb1e138: DecompressPointer r2
    //     0xb1e138: add             x2, x2, HEAP, lsl #32
    // 0xb1e13c: LoadField: r0 = r1->field_f
    //     0xb1e13c: ldur            w0, [x1, #0xf]
    // 0xb1e140: DecompressPointer r0
    //     0xb1e140: add             x0, x0, HEAP, lsl #32
    // 0xb1e144: r1 = LoadInt32Instr(r0)
    //     0xb1e144: sbfx            x1, x0, #1, #0x1f
    //     0xb1e148: tbz             w0, #0, #0xb1e150
    //     0xb1e14c: ldur            x1, [x0, #7]
    // 0xb1e150: ArrayStore: r2[0] = r1  ; List_8
    //     0xb1e150: stur            x1, [x2, #0x17]
    // 0xb1e154: LoadField: r1 = r2->field_1f
    //     0xb1e154: ldur            w1, [x2, #0x1f]
    // 0xb1e158: DecompressPointer r1
    //     0xb1e158: add             x1, x1, HEAP, lsl #32
    // 0xb1e15c: r2 = false
    //     0xb1e15c: add             x2, NULL, #0x30  ; false
    // 0xb1e160: r0 = value=()
    //     0xb1e160: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xb1e164: r0 = Null
    //     0xb1e164: mov             x0, NULL
    // 0xb1e168: LeaveFrame
    //     0xb1e168: mov             SP, fp
    //     0xb1e16c: ldp             fp, lr, [SP], #0x10
    // 0xb1e170: ret
    //     0xb1e170: ret             
    // 0xb1e174: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1e174: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1e178: b               #0xb1e12c
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb1e17c, size: 0x84
    // 0xb1e17c: EnterFrame
    //     0xb1e17c: stp             fp, lr, [SP, #-0x10]!
    //     0xb1e180: mov             fp, SP
    // 0xb1e184: AllocStack(0x10)
    //     0xb1e184: sub             SP, SP, #0x10
    // 0xb1e188: SetupParameters()
    //     0xb1e188: ldr             x0, [fp, #0x18]
    //     0xb1e18c: ldur            w1, [x0, #0x17]
    //     0xb1e190: add             x1, x1, HEAP, lsl #32
    //     0xb1e194: stur            x1, [fp, #-8]
    // 0xb1e198: CheckStackOverflow
    //     0xb1e198: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1e19c: cmp             SP, x16
    //     0xb1e1a0: b.ls            #0xb1e1f8
    // 0xb1e1a4: r1 = 1
    //     0xb1e1a4: movz            x1, #0x1
    // 0xb1e1a8: r0 = AllocateContext()
    //     0xb1e1a8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb1e1ac: mov             x1, x0
    // 0xb1e1b0: ldur            x0, [fp, #-8]
    // 0xb1e1b4: StoreField: r1->field_b = r0
    //     0xb1e1b4: stur            w0, [x1, #0xb]
    // 0xb1e1b8: ldr             x2, [fp, #0x10]
    // 0xb1e1bc: StoreField: r1->field_f = r2
    //     0xb1e1bc: stur            w2, [x1, #0xf]
    // 0xb1e1c0: LoadField: r3 = r0->field_f
    //     0xb1e1c0: ldur            w3, [x0, #0xf]
    // 0xb1e1c4: DecompressPointer r3
    //     0xb1e1c4: add             x3, x3, HEAP, lsl #32
    // 0xb1e1c8: mov             x2, x1
    // 0xb1e1cc: stur            x3, [fp, #-0x10]
    // 0xb1e1d0: r1 = Function '<anonymous closure>':.
    //     0xb1e1d0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55290] AnonymousClosure: (0xb1e10c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xb943d8)
    //     0xb1e1d4: ldr             x1, [x1, #0x290]
    // 0xb1e1d8: r0 = AllocateClosure()
    //     0xb1e1d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1e1dc: ldur            x1, [fp, #-0x10]
    // 0xb1e1e0: mov             x2, x0
    // 0xb1e1e4: r0 = setState()
    //     0xb1e1e4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb1e1e8: r0 = Null
    //     0xb1e1e8: mov             x0, NULL
    // 0xb1e1ec: LeaveFrame
    //     0xb1e1ec: mov             SP, fp
    //     0xb1e1f0: ldp             fp, lr, [SP], #0x10
    // 0xb1e1f4: ret
    //     0xb1e1f4: ret             
    // 0xb1e1f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1e1f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1e1fc: b               #0xb1e1a4
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0xb1e200, size: 0x9ec
    // 0xb1e200: EnterFrame
    //     0xb1e200: stp             fp, lr, [SP, #-0x10]!
    //     0xb1e204: mov             fp, SP
    // 0xb1e208: AllocStack(0x88)
    //     0xb1e208: sub             SP, SP, #0x88
    // 0xb1e20c: SetupParameters()
    //     0xb1e20c: ldr             x0, [fp, #0x10]
    //     0xb1e210: ldur            w2, [x0, #0x17]
    //     0xb1e214: add             x2, x2, HEAP, lsl #32
    //     0xb1e218: stur            x2, [fp, #-0x10]
    // 0xb1e21c: CheckStackOverflow
    //     0xb1e21c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1e220: cmp             SP, x16
    //     0xb1e224: b.ls            #0xb1ebc0
    // 0xb1e228: LoadField: r0 = r2->field_f
    //     0xb1e228: ldur            w0, [x2, #0xf]
    // 0xb1e22c: DecompressPointer r0
    //     0xb1e22c: add             x0, x0, HEAP, lsl #32
    // 0xb1e230: LoadField: r1 = r0->field_b
    //     0xb1e230: ldur            w1, [x0, #0xb]
    // 0xb1e234: DecompressPointer r1
    //     0xb1e234: add             x1, x1, HEAP, lsl #32
    // 0xb1e238: cmp             w1, NULL
    // 0xb1e23c: b.eq            #0xb1ebc8
    // 0xb1e240: LoadField: r0 = r1->field_13
    //     0xb1e240: ldur            w0, [x1, #0x13]
    // 0xb1e244: DecompressPointer r0
    //     0xb1e244: add             x0, x0, HEAP, lsl #32
    // 0xb1e248: LoadField: r3 = r0->field_7
    //     0xb1e248: ldur            w3, [x0, #7]
    // 0xb1e24c: DecompressPointer r3
    //     0xb1e24c: add             x3, x3, HEAP, lsl #32
    // 0xb1e250: cmp             w3, NULL
    // 0xb1e254: b.ne            #0xb1e264
    // 0xb1e258: r0 = Instance_TitleAlignment
    //     0xb1e258: add             x0, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb1e25c: ldr             x0, [x0, #0x518]
    // 0xb1e260: b               #0xb1e268
    // 0xb1e264: mov             x0, x3
    // 0xb1e268: r16 = Instance_TitleAlignment
    //     0xb1e268: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb1e26c: ldr             x16, [x16, #0x520]
    // 0xb1e270: cmp             w0, w16
    // 0xb1e274: b.ne            #0xb1e284
    // 0xb1e278: r0 = Instance_CrossAxisAlignment
    //     0xb1e278: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb1e27c: ldr             x0, [x0, #0xc68]
    // 0xb1e280: b               #0xb1e2a8
    // 0xb1e284: r16 = Instance_TitleAlignment
    //     0xb1e284: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb1e288: ldr             x16, [x16, #0x518]
    // 0xb1e28c: cmp             w0, w16
    // 0xb1e290: b.ne            #0xb1e2a0
    // 0xb1e294: r0 = Instance_CrossAxisAlignment
    //     0xb1e294: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb1e298: ldr             x0, [x0, #0x890]
    // 0xb1e29c: b               #0xb1e2a8
    // 0xb1e2a0: r0 = Instance_CrossAxisAlignment
    //     0xb1e2a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb1e2a4: ldr             x0, [x0, #0xa18]
    // 0xb1e2a8: stur            x0, [fp, #-8]
    // 0xb1e2ac: LoadField: r3 = r1->field_f
    //     0xb1e2ac: ldur            w3, [x1, #0xf]
    // 0xb1e2b0: DecompressPointer r3
    //     0xb1e2b0: add             x3, x3, HEAP, lsl #32
    // 0xb1e2b4: mov             x1, x3
    // 0xb1e2b8: r0 = capitalizeFirstWord()
    //     0xb1e2b8: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb1e2bc: ldur            x2, [fp, #-0x10]
    // 0xb1e2c0: stur            x0, [fp, #-0x20]
    // 0xb1e2c4: LoadField: r1 = r2->field_f
    //     0xb1e2c4: ldur            w1, [x2, #0xf]
    // 0xb1e2c8: DecompressPointer r1
    //     0xb1e2c8: add             x1, x1, HEAP, lsl #32
    // 0xb1e2cc: LoadField: r3 = r1->field_b
    //     0xb1e2cc: ldur            w3, [x1, #0xb]
    // 0xb1e2d0: DecompressPointer r3
    //     0xb1e2d0: add             x3, x3, HEAP, lsl #32
    // 0xb1e2d4: cmp             w3, NULL
    // 0xb1e2d8: b.eq            #0xb1ebcc
    // 0xb1e2dc: LoadField: r1 = r3->field_13
    //     0xb1e2dc: ldur            w1, [x3, #0x13]
    // 0xb1e2e0: DecompressPointer r1
    //     0xb1e2e0: add             x1, x1, HEAP, lsl #32
    // 0xb1e2e4: LoadField: r3 = r1->field_7
    //     0xb1e2e4: ldur            w3, [x1, #7]
    // 0xb1e2e8: DecompressPointer r3
    //     0xb1e2e8: add             x3, x3, HEAP, lsl #32
    // 0xb1e2ec: cmp             w3, NULL
    // 0xb1e2f0: b.ne            #0xb1e300
    // 0xb1e2f4: r1 = Instance_TitleAlignment
    //     0xb1e2f4: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb1e2f8: ldr             x1, [x1, #0x518]
    // 0xb1e2fc: b               #0xb1e304
    // 0xb1e300: mov             x1, x3
    // 0xb1e304: r16 = Instance_TitleAlignment
    //     0xb1e304: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb1e308: ldr             x16, [x16, #0x520]
    // 0xb1e30c: cmp             w1, w16
    // 0xb1e310: b.ne            #0xb1e31c
    // 0xb1e314: r3 = Instance_TextAlign
    //     0xb1e314: ldr             x3, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xb1e318: b               #0xb1e338
    // 0xb1e31c: r16 = Instance_TitleAlignment
    //     0xb1e31c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb1e320: ldr             x16, [x16, #0x518]
    // 0xb1e324: cmp             w1, w16
    // 0xb1e328: b.ne            #0xb1e334
    // 0xb1e32c: r3 = Instance_TextAlign
    //     0xb1e32c: ldr             x3, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb1e330: b               #0xb1e338
    // 0xb1e334: r3 = Instance_TextAlign
    //     0xb1e334: ldr             x3, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb1e338: stur            x3, [fp, #-0x18]
    // 0xb1e33c: LoadField: r1 = r2->field_13
    //     0xb1e33c: ldur            w1, [x2, #0x13]
    // 0xb1e340: DecompressPointer r1
    //     0xb1e340: add             x1, x1, HEAP, lsl #32
    // 0xb1e344: r0 = of()
    //     0xb1e344: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1e348: LoadField: r1 = r0->field_87
    //     0xb1e348: ldur            w1, [x0, #0x87]
    // 0xb1e34c: DecompressPointer r1
    //     0xb1e34c: add             x1, x1, HEAP, lsl #32
    // 0xb1e350: LoadField: r0 = r1->field_7
    //     0xb1e350: ldur            w0, [x1, #7]
    // 0xb1e354: DecompressPointer r0
    //     0xb1e354: add             x0, x0, HEAP, lsl #32
    // 0xb1e358: r16 = Instance_Color
    //     0xb1e358: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1e35c: r30 = 32.000000
    //     0xb1e35c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb1e360: ldr             lr, [lr, #0x848]
    // 0xb1e364: stp             lr, x16, [SP]
    // 0xb1e368: mov             x1, x0
    // 0xb1e36c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb1e36c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb1e370: ldr             x4, [x4, #0x9b8]
    // 0xb1e374: r0 = copyWith()
    //     0xb1e374: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1e378: stur            x0, [fp, #-0x28]
    // 0xb1e37c: r0 = Text()
    //     0xb1e37c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1e380: mov             x1, x0
    // 0xb1e384: ldur            x0, [fp, #-0x20]
    // 0xb1e388: stur            x1, [fp, #-0x30]
    // 0xb1e38c: StoreField: r1->field_b = r0
    //     0xb1e38c: stur            w0, [x1, #0xb]
    // 0xb1e390: ldur            x0, [fp, #-0x28]
    // 0xb1e394: StoreField: r1->field_13 = r0
    //     0xb1e394: stur            w0, [x1, #0x13]
    // 0xb1e398: ldur            x0, [fp, #-0x18]
    // 0xb1e39c: StoreField: r1->field_1b = r0
    //     0xb1e39c: stur            w0, [x1, #0x1b]
    // 0xb1e3a0: r0 = Padding()
    //     0xb1e3a0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1e3a4: mov             x2, x0
    // 0xb1e3a8: r0 = Instance_EdgeInsets
    //     0xb1e3a8: add             x0, PP, #0x55, lsl #12  ; [pp+0x55078] Obj!EdgeInsets@d58791
    //     0xb1e3ac: ldr             x0, [x0, #0x78]
    // 0xb1e3b0: stur            x2, [fp, #-0x18]
    // 0xb1e3b4: StoreField: r2->field_f = r0
    //     0xb1e3b4: stur            w0, [x2, #0xf]
    // 0xb1e3b8: ldur            x0, [fp, #-0x30]
    // 0xb1e3bc: StoreField: r2->field_b = r0
    //     0xb1e3bc: stur            w0, [x2, #0xb]
    // 0xb1e3c0: ldur            x0, [fp, #-0x10]
    // 0xb1e3c4: LoadField: r1 = r0->field_f
    //     0xb1e3c4: ldur            w1, [x0, #0xf]
    // 0xb1e3c8: DecompressPointer r1
    //     0xb1e3c8: add             x1, x1, HEAP, lsl #32
    // 0xb1e3cc: LoadField: r3 = r1->field_1f
    //     0xb1e3cc: ldur            w3, [x1, #0x1f]
    // 0xb1e3d0: DecompressPointer r3
    //     0xb1e3d0: add             x3, x3, HEAP, lsl #32
    // 0xb1e3d4: mov             x1, x3
    // 0xb1e3d8: r0 = value()
    //     0xb1e3d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb1e3dc: tbnz            w0, #4, #0xb1e4fc
    // 0xb1e3e0: ldur            x2, [fp, #-0x10]
    // 0xb1e3e4: LoadField: r0 = r2->field_f
    //     0xb1e3e4: ldur            w0, [x2, #0xf]
    // 0xb1e3e8: DecompressPointer r0
    //     0xb1e3e8: add             x0, x0, HEAP, lsl #32
    // 0xb1e3ec: LoadField: r1 = r0->field_b
    //     0xb1e3ec: ldur            w1, [x0, #0xb]
    // 0xb1e3f0: DecompressPointer r1
    //     0xb1e3f0: add             x1, x1, HEAP, lsl #32
    // 0xb1e3f4: cmp             w1, NULL
    // 0xb1e3f8: b.eq            #0xb1ebd0
    // 0xb1e3fc: LoadField: r3 = r1->field_b
    //     0xb1e3fc: ldur            w3, [x1, #0xb]
    // 0xb1e400: DecompressPointer r3
    //     0xb1e400: add             x3, x3, HEAP, lsl #32
    // 0xb1e404: ArrayLoad: r4 = r0[0]  ; List_8
    //     0xb1e404: ldur            x4, [x0, #0x17]
    // 0xb1e408: r0 = BoxInt64Instr(r4)
    //     0xb1e408: sbfiz           x0, x4, #1, #0x1f
    //     0xb1e40c: cmp             x4, x0, asr #1
    //     0xb1e410: b.eq            #0xb1e41c
    //     0xb1e414: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb1e418: stur            x4, [x0, #7]
    // 0xb1e41c: r1 = LoadClassIdInstr(r3)
    //     0xb1e41c: ldur            x1, [x3, #-1]
    //     0xb1e420: ubfx            x1, x1, #0xc, #0x14
    // 0xb1e424: stp             x0, x3, [SP]
    // 0xb1e428: mov             x0, x1
    // 0xb1e42c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb1e42c: sub             lr, x0, #0xb7
    //     0xb1e430: ldr             lr, [x21, lr, lsl #3]
    //     0xb1e434: blr             lr
    // 0xb1e438: cmp             w0, NULL
    // 0xb1e43c: b.ne            #0xb1e448
    // 0xb1e440: r0 = Null
    //     0xb1e440: mov             x0, NULL
    // 0xb1e444: b               #0xb1e464
    // 0xb1e448: LoadField: r1 = r0->field_b7
    //     0xb1e448: ldur            w1, [x0, #0xb7]
    // 0xb1e44c: DecompressPointer r1
    //     0xb1e44c: add             x1, x1, HEAP, lsl #32
    // 0xb1e450: cmp             w1, NULL
    // 0xb1e454: b.ne            #0xb1e460
    // 0xb1e458: r0 = Null
    //     0xb1e458: mov             x0, NULL
    // 0xb1e45c: b               #0xb1e464
    // 0xb1e460: LoadField: r0 = r1->field_b
    //     0xb1e460: ldur            w0, [x1, #0xb]
    // 0xb1e464: cmp             w0, NULL
    // 0xb1e468: b.ne            #0xb1e474
    // 0xb1e46c: r0 = 0
    //     0xb1e46c: movz            x0, #0
    // 0xb1e470: b               #0xb1e47c
    // 0xb1e474: r1 = LoadInt32Instr(r0)
    //     0xb1e474: sbfx            x1, x0, #1, #0x1f
    // 0xb1e478: mov             x0, x1
    // 0xb1e47c: cmp             x0, #0
    // 0xb1e480: b.le            #0xb1e4f0
    // 0xb1e484: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb1e484: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1e488: ldr             x0, [x0, #0x1c80]
    //     0xb1e48c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1e490: cmp             w0, w16
    //     0xb1e494: b.ne            #0xb1e4a0
    //     0xb1e498: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb1e49c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb1e4a0: r0 = GetNavigation.size()
    //     0xb1e4a0: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb1e4a4: LoadField: d0 = r0->field_f
    //     0xb1e4a4: ldur            d0, [x0, #0xf]
    // 0xb1e4a8: d1 = 0.750000
    //     0xb1e4a8: fmov            d1, #0.75000000
    // 0xb1e4ac: fmul            d2, d0, d1
    // 0xb1e4b0: stur            d2, [fp, #-0x60]
    // 0xb1e4b4: r0 = GetNavigation.size()
    //     0xb1e4b4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb1e4b8: LoadField: d0 = r0->field_f
    //     0xb1e4b8: ldur            d0, [x0, #0xf]
    // 0xb1e4bc: d1 = 0.200000
    //     0xb1e4bc: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb1e4c0: fmul            d2, d0, d1
    // 0xb1e4c4: stur            d2, [fp, #-0x68]
    // 0xb1e4c8: r0 = BoxConstraints()
    //     0xb1e4c8: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb1e4cc: StoreField: r0->field_7 = rZR
    //     0xb1e4cc: stur            xzr, [x0, #7]
    // 0xb1e4d0: d0 = inf
    //     0xb1e4d0: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb1e4d4: StoreField: r0->field_f = d0
    //     0xb1e4d4: stur            d0, [x0, #0xf]
    // 0xb1e4d8: ldur            d0, [fp, #-0x68]
    // 0xb1e4dc: ArrayStore: r0[0] = d0  ; List_8
    //     0xb1e4dc: stur            d0, [x0, #0x17]
    // 0xb1e4e0: ldur            d0, [fp, #-0x60]
    // 0xb1e4e4: StoreField: r0->field_1f = d0
    //     0xb1e4e4: stur            d0, [x0, #0x1f]
    // 0xb1e4e8: mov             x3, x0
    // 0xb1e4ec: b               #0xb1e570
    // 0xb1e4f0: d1 = 0.200000
    //     0xb1e4f0: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb1e4f4: d0 = inf
    //     0xb1e4f4: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb1e4f8: b               #0xb1e504
    // 0xb1e4fc: d1 = 0.200000
    //     0xb1e4fc: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb1e500: d0 = inf
    //     0xb1e500: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb1e504: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb1e504: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1e508: ldr             x0, [x0, #0x1c80]
    //     0xb1e50c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1e510: cmp             w0, w16
    //     0xb1e514: b.ne            #0xb1e520
    //     0xb1e518: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb1e51c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb1e520: r0 = GetNavigation.size()
    //     0xb1e520: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb1e524: LoadField: d0 = r0->field_f
    //     0xb1e524: ldur            d0, [x0, #0xf]
    // 0xb1e528: d1 = 0.630000
    //     0xb1e528: add             x17, PP, #0x55, lsl #12  ; [pp+0x55080] IMM: double(0.63) from 0x3fe428f5c28f5c29
    //     0xb1e52c: ldr             d1, [x17, #0x80]
    // 0xb1e530: fmul            d2, d0, d1
    // 0xb1e534: stur            d2, [fp, #-0x60]
    // 0xb1e538: r0 = GetNavigation.size()
    //     0xb1e538: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb1e53c: LoadField: d0 = r0->field_f
    //     0xb1e53c: ldur            d0, [x0, #0xf]
    // 0xb1e540: d1 = 0.200000
    //     0xb1e540: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb1e544: fmul            d2, d0, d1
    // 0xb1e548: stur            d2, [fp, #-0x68]
    // 0xb1e54c: r0 = BoxConstraints()
    //     0xb1e54c: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb1e550: StoreField: r0->field_7 = rZR
    //     0xb1e550: stur            xzr, [x0, #7]
    // 0xb1e554: d0 = inf
    //     0xb1e554: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb1e558: StoreField: r0->field_f = d0
    //     0xb1e558: stur            d0, [x0, #0xf]
    // 0xb1e55c: ldur            d0, [fp, #-0x68]
    // 0xb1e560: ArrayStore: r0[0] = d0  ; List_8
    //     0xb1e560: stur            d0, [x0, #0x17]
    // 0xb1e564: ldur            d0, [fp, #-0x60]
    // 0xb1e568: StoreField: r0->field_1f = d0
    //     0xb1e568: stur            d0, [x0, #0x1f]
    // 0xb1e56c: mov             x3, x0
    // 0xb1e570: ldur            x2, [fp, #-0x10]
    // 0xb1e574: ldur            x1, [fp, #-0x18]
    // 0xb1e578: stur            x3, [fp, #-0x20]
    // 0xb1e57c: LoadField: r0 = r2->field_f
    //     0xb1e57c: ldur            w0, [x2, #0xf]
    // 0xb1e580: DecompressPointer r0
    //     0xb1e580: add             x0, x0, HEAP, lsl #32
    // 0xb1e584: LoadField: r4 = r0->field_b
    //     0xb1e584: ldur            w4, [x0, #0xb]
    // 0xb1e588: DecompressPointer r4
    //     0xb1e588: add             x4, x4, HEAP, lsl #32
    // 0xb1e58c: cmp             w4, NULL
    // 0xb1e590: b.eq            #0xb1ebd4
    // 0xb1e594: LoadField: r0 = r4->field_b
    //     0xb1e594: ldur            w0, [x4, #0xb]
    // 0xb1e598: DecompressPointer r0
    //     0xb1e598: add             x0, x0, HEAP, lsl #32
    // 0xb1e59c: r4 = LoadClassIdInstr(r0)
    //     0xb1e59c: ldur            x4, [x0, #-1]
    //     0xb1e5a0: ubfx            x4, x4, #0xc, #0x14
    // 0xb1e5a4: str             x0, [SP]
    // 0xb1e5a8: mov             x0, x4
    // 0xb1e5ac: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb1e5ac: movz            x17, #0xc898
    //     0xb1e5b0: add             lr, x0, x17
    //     0xb1e5b4: ldr             lr, [x21, lr, lsl #3]
    //     0xb1e5b8: blr             lr
    // 0xb1e5bc: mov             x3, x0
    // 0xb1e5c0: ldur            x0, [fp, #-0x10]
    // 0xb1e5c4: stur            x3, [fp, #-0x30]
    // 0xb1e5c8: LoadField: r1 = r0->field_f
    //     0xb1e5c8: ldur            w1, [x0, #0xf]
    // 0xb1e5cc: DecompressPointer r1
    //     0xb1e5cc: add             x1, x1, HEAP, lsl #32
    // 0xb1e5d0: LoadField: r4 = r1->field_13
    //     0xb1e5d0: ldur            w4, [x1, #0x13]
    // 0xb1e5d4: DecompressPointer r4
    //     0xb1e5d4: add             x4, x4, HEAP, lsl #32
    // 0xb1e5d8: r16 = Sentinel
    //     0xb1e5d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb1e5dc: cmp             w4, w16
    // 0xb1e5e0: b.eq            #0xb1ebd8
    // 0xb1e5e4: mov             x2, x0
    // 0xb1e5e8: stur            x4, [fp, #-0x28]
    // 0xb1e5ec: r1 = Function '<anonymous closure>':.
    //     0xb1e5ec: add             x1, PP, #0x55, lsl #12  ; [pp+0x55088] AnonymousClosure: (0xb1e17c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xb943d8)
    //     0xb1e5f0: ldr             x1, [x1, #0x88]
    // 0xb1e5f4: r0 = AllocateClosure()
    //     0xb1e5f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1e5f8: ldur            x2, [fp, #-0x10]
    // 0xb1e5fc: r1 = Function '<anonymous closure>':.
    //     0xb1e5fc: add             x1, PP, #0x55, lsl #12  ; [pp+0x55090] AnonymousClosure: (0xb1ed94), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xb943d8)
    //     0xb1e600: ldr             x1, [x1, #0x90]
    // 0xb1e604: stur            x0, [fp, #-0x38]
    // 0xb1e608: r0 = AllocateClosure()
    //     0xb1e608: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1e60c: stur            x0, [fp, #-0x40]
    // 0xb1e610: r0 = PageView()
    //     0xb1e610: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb1e614: stur            x0, [fp, #-0x48]
    // 0xb1e618: r16 = Instance_BouncingScrollPhysics
    //     0xb1e618: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb1e61c: ldr             x16, [x16, #0x890]
    // 0xb1e620: ldur            lr, [fp, #-0x28]
    // 0xb1e624: stp             lr, x16, [SP]
    // 0xb1e628: mov             x1, x0
    // 0xb1e62c: ldur            x2, [fp, #-0x40]
    // 0xb1e630: ldur            x3, [fp, #-0x30]
    // 0xb1e634: ldur            x5, [fp, #-0x38]
    // 0xb1e638: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xb1e638: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xb1e63c: ldr             x4, [x4, #0xe40]
    // 0xb1e640: r0 = PageView.builder()
    //     0xb1e640: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb1e644: r0 = ConstrainedBox()
    //     0xb1e644: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xb1e648: mov             x3, x0
    // 0xb1e64c: ldur            x0, [fp, #-0x20]
    // 0xb1e650: stur            x3, [fp, #-0x28]
    // 0xb1e654: StoreField: r3->field_f = r0
    //     0xb1e654: stur            w0, [x3, #0xf]
    // 0xb1e658: ldur            x0, [fp, #-0x48]
    // 0xb1e65c: StoreField: r3->field_b = r0
    //     0xb1e65c: stur            w0, [x3, #0xb]
    // 0xb1e660: r1 = Null
    //     0xb1e660: mov             x1, NULL
    // 0xb1e664: r2 = 4
    //     0xb1e664: movz            x2, #0x4
    // 0xb1e668: r0 = AllocateArray()
    //     0xb1e668: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1e66c: mov             x2, x0
    // 0xb1e670: ldur            x0, [fp, #-0x18]
    // 0xb1e674: stur            x2, [fp, #-0x20]
    // 0xb1e678: StoreField: r2->field_f = r0
    //     0xb1e678: stur            w0, [x2, #0xf]
    // 0xb1e67c: ldur            x0, [fp, #-0x28]
    // 0xb1e680: StoreField: r2->field_13 = r0
    //     0xb1e680: stur            w0, [x2, #0x13]
    // 0xb1e684: r1 = <Widget>
    //     0xb1e684: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1e688: r0 = AllocateGrowableArray()
    //     0xb1e688: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb1e68c: mov             x1, x0
    // 0xb1e690: ldur            x0, [fp, #-0x20]
    // 0xb1e694: stur            x1, [fp, #-0x18]
    // 0xb1e698: StoreField: r1->field_f = r0
    //     0xb1e698: stur            w0, [x1, #0xf]
    // 0xb1e69c: r0 = 4
    //     0xb1e69c: movz            x0, #0x4
    // 0xb1e6a0: StoreField: r1->field_b = r0
    //     0xb1e6a0: stur            w0, [x1, #0xb]
    // 0xb1e6a4: ldur            x2, [fp, #-0x10]
    // 0xb1e6a8: LoadField: r0 = r2->field_f
    //     0xb1e6a8: ldur            w0, [x2, #0xf]
    // 0xb1e6ac: DecompressPointer r0
    //     0xb1e6ac: add             x0, x0, HEAP, lsl #32
    // 0xb1e6b0: LoadField: r3 = r0->field_b
    //     0xb1e6b0: ldur            w3, [x0, #0xb]
    // 0xb1e6b4: DecompressPointer r3
    //     0xb1e6b4: add             x3, x3, HEAP, lsl #32
    // 0xb1e6b8: cmp             w3, NULL
    // 0xb1e6bc: b.eq            #0xb1ebe4
    // 0xb1e6c0: LoadField: r0 = r3->field_b
    //     0xb1e6c0: ldur            w0, [x3, #0xb]
    // 0xb1e6c4: DecompressPointer r0
    //     0xb1e6c4: add             x0, x0, HEAP, lsl #32
    // 0xb1e6c8: r3 = LoadClassIdInstr(r0)
    //     0xb1e6c8: ldur            x3, [x0, #-1]
    //     0xb1e6cc: ubfx            x3, x3, #0xc, #0x14
    // 0xb1e6d0: str             x0, [SP]
    // 0xb1e6d4: mov             x0, x3
    // 0xb1e6d8: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb1e6d8: movz            x17, #0xc898
    //     0xb1e6dc: add             lr, x0, x17
    //     0xb1e6e0: ldr             lr, [x21, lr, lsl #3]
    //     0xb1e6e4: blr             lr
    // 0xb1e6e8: r1 = LoadInt32Instr(r0)
    //     0xb1e6e8: sbfx            x1, x0, #1, #0x1f
    // 0xb1e6ec: cmp             x1, #1
    // 0xb1e6f0: b.le            #0xb1e8bc
    // 0xb1e6f4: ldur            x2, [fp, #-0x10]
    // 0xb1e6f8: ldur            x1, [fp, #-0x18]
    // 0xb1e6fc: LoadField: r0 = r2->field_f
    //     0xb1e6fc: ldur            w0, [x2, #0xf]
    // 0xb1e700: DecompressPointer r0
    //     0xb1e700: add             x0, x0, HEAP, lsl #32
    // 0xb1e704: LoadField: r3 = r0->field_b
    //     0xb1e704: ldur            w3, [x0, #0xb]
    // 0xb1e708: DecompressPointer r3
    //     0xb1e708: add             x3, x3, HEAP, lsl #32
    // 0xb1e70c: cmp             w3, NULL
    // 0xb1e710: b.eq            #0xb1ebe8
    // 0xb1e714: LoadField: r0 = r3->field_b
    //     0xb1e714: ldur            w0, [x3, #0xb]
    // 0xb1e718: DecompressPointer r0
    //     0xb1e718: add             x0, x0, HEAP, lsl #32
    // 0xb1e71c: r3 = LoadClassIdInstr(r0)
    //     0xb1e71c: ldur            x3, [x0, #-1]
    //     0xb1e720: ubfx            x3, x3, #0xc, #0x14
    // 0xb1e724: str             x0, [SP]
    // 0xb1e728: mov             x0, x3
    // 0xb1e72c: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb1e72c: movz            x17, #0xc898
    //     0xb1e730: add             lr, x0, x17
    //     0xb1e734: ldr             lr, [x21, lr, lsl #3]
    //     0xb1e738: blr             lr
    // 0xb1e73c: ldur            x2, [fp, #-0x10]
    // 0xb1e740: stur            x0, [fp, #-0x20]
    // 0xb1e744: LoadField: r1 = r2->field_f
    //     0xb1e744: ldur            w1, [x2, #0xf]
    // 0xb1e748: DecompressPointer r1
    //     0xb1e748: add             x1, x1, HEAP, lsl #32
    // 0xb1e74c: ArrayLoad: r3 = r1[0]  ; List_8
    //     0xb1e74c: ldur            x3, [x1, #0x17]
    // 0xb1e750: stur            x3, [fp, #-0x50]
    // 0xb1e754: LoadField: r1 = r2->field_13
    //     0xb1e754: ldur            w1, [x2, #0x13]
    // 0xb1e758: DecompressPointer r1
    //     0xb1e758: add             x1, x1, HEAP, lsl #32
    // 0xb1e75c: r0 = of()
    //     0xb1e75c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1e760: LoadField: r1 = r0->field_5b
    //     0xb1e760: ldur            w1, [x0, #0x5b]
    // 0xb1e764: DecompressPointer r1
    //     0xb1e764: add             x1, x1, HEAP, lsl #32
    // 0xb1e768: ldur            x0, [fp, #-0x20]
    // 0xb1e76c: stur            x1, [fp, #-0x28]
    // 0xb1e770: r2 = LoadInt32Instr(r0)
    //     0xb1e770: sbfx            x2, x0, #1, #0x1f
    // 0xb1e774: stur            x2, [fp, #-0x58]
    // 0xb1e778: r0 = CarouselIndicator()
    //     0xb1e778: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xb1e77c: mov             x3, x0
    // 0xb1e780: ldur            x0, [fp, #-0x58]
    // 0xb1e784: stur            x3, [fp, #-0x20]
    // 0xb1e788: StoreField: r3->field_b = r0
    //     0xb1e788: stur            x0, [x3, #0xb]
    // 0xb1e78c: ldur            x0, [fp, #-0x50]
    // 0xb1e790: StoreField: r3->field_13 = r0
    //     0xb1e790: stur            x0, [x3, #0x13]
    // 0xb1e794: ldur            x0, [fp, #-0x28]
    // 0xb1e798: StoreField: r3->field_1b = r0
    //     0xb1e798: stur            w0, [x3, #0x1b]
    // 0xb1e79c: r0 = Instance_Color
    //     0xb1e79c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb1e7a0: ldr             x0, [x0, #0x90]
    // 0xb1e7a4: StoreField: r3->field_1f = r0
    //     0xb1e7a4: stur            w0, [x3, #0x1f]
    // 0xb1e7a8: r1 = Null
    //     0xb1e7a8: mov             x1, NULL
    // 0xb1e7ac: r2 = 2
    //     0xb1e7ac: movz            x2, #0x2
    // 0xb1e7b0: r0 = AllocateArray()
    //     0xb1e7b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1e7b4: mov             x2, x0
    // 0xb1e7b8: ldur            x0, [fp, #-0x20]
    // 0xb1e7bc: stur            x2, [fp, #-0x28]
    // 0xb1e7c0: StoreField: r2->field_f = r0
    //     0xb1e7c0: stur            w0, [x2, #0xf]
    // 0xb1e7c4: r1 = <Widget>
    //     0xb1e7c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1e7c8: r0 = AllocateGrowableArray()
    //     0xb1e7c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb1e7cc: mov             x1, x0
    // 0xb1e7d0: ldur            x0, [fp, #-0x28]
    // 0xb1e7d4: stur            x1, [fp, #-0x20]
    // 0xb1e7d8: StoreField: r1->field_f = r0
    //     0xb1e7d8: stur            w0, [x1, #0xf]
    // 0xb1e7dc: r0 = 2
    //     0xb1e7dc: movz            x0, #0x2
    // 0xb1e7e0: StoreField: r1->field_b = r0
    //     0xb1e7e0: stur            w0, [x1, #0xb]
    // 0xb1e7e4: r0 = Row()
    //     0xb1e7e4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb1e7e8: mov             x2, x0
    // 0xb1e7ec: r0 = Instance_Axis
    //     0xb1e7ec: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb1e7f0: stur            x2, [fp, #-0x28]
    // 0xb1e7f4: StoreField: r2->field_f = r0
    //     0xb1e7f4: stur            w0, [x2, #0xf]
    // 0xb1e7f8: r0 = Instance_MainAxisAlignment
    //     0xb1e7f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb1e7fc: ldr             x0, [x0, #0xab0]
    // 0xb1e800: StoreField: r2->field_13 = r0
    //     0xb1e800: stur            w0, [x2, #0x13]
    // 0xb1e804: r0 = Instance_MainAxisSize
    //     0xb1e804: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb1e808: ldr             x0, [x0, #0xa10]
    // 0xb1e80c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb1e80c: stur            w0, [x2, #0x17]
    // 0xb1e810: r0 = Instance_CrossAxisAlignment
    //     0xb1e810: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb1e814: ldr             x0, [x0, #0xa18]
    // 0xb1e818: StoreField: r2->field_1b = r0
    //     0xb1e818: stur            w0, [x2, #0x1b]
    // 0xb1e81c: r0 = Instance_VerticalDirection
    //     0xb1e81c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1e820: ldr             x0, [x0, #0xa20]
    // 0xb1e824: StoreField: r2->field_23 = r0
    //     0xb1e824: stur            w0, [x2, #0x23]
    // 0xb1e828: r3 = Instance_Clip
    //     0xb1e828: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1e82c: ldr             x3, [x3, #0x38]
    // 0xb1e830: StoreField: r2->field_2b = r3
    //     0xb1e830: stur            w3, [x2, #0x2b]
    // 0xb1e834: StoreField: r2->field_2f = rZR
    //     0xb1e834: stur            xzr, [x2, #0x2f]
    // 0xb1e838: ldur            x1, [fp, #-0x20]
    // 0xb1e83c: StoreField: r2->field_b = r1
    //     0xb1e83c: stur            w1, [x2, #0xb]
    // 0xb1e840: ldur            x4, [fp, #-0x18]
    // 0xb1e844: LoadField: r1 = r4->field_b
    //     0xb1e844: ldur            w1, [x4, #0xb]
    // 0xb1e848: LoadField: r5 = r4->field_f
    //     0xb1e848: ldur            w5, [x4, #0xf]
    // 0xb1e84c: DecompressPointer r5
    //     0xb1e84c: add             x5, x5, HEAP, lsl #32
    // 0xb1e850: LoadField: r6 = r5->field_b
    //     0xb1e850: ldur            w6, [x5, #0xb]
    // 0xb1e854: r5 = LoadInt32Instr(r1)
    //     0xb1e854: sbfx            x5, x1, #1, #0x1f
    // 0xb1e858: stur            x5, [fp, #-0x50]
    // 0xb1e85c: r1 = LoadInt32Instr(r6)
    //     0xb1e85c: sbfx            x1, x6, #1, #0x1f
    // 0xb1e860: cmp             x5, x1
    // 0xb1e864: b.ne            #0xb1e870
    // 0xb1e868: mov             x1, x4
    // 0xb1e86c: r0 = _growToNextCapacity()
    //     0xb1e86c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1e870: ldur            x2, [fp, #-0x18]
    // 0xb1e874: ldur            x3, [fp, #-0x50]
    // 0xb1e878: add             x0, x3, #1
    // 0xb1e87c: lsl             x1, x0, #1
    // 0xb1e880: StoreField: r2->field_b = r1
    //     0xb1e880: stur            w1, [x2, #0xb]
    // 0xb1e884: LoadField: r1 = r2->field_f
    //     0xb1e884: ldur            w1, [x2, #0xf]
    // 0xb1e888: DecompressPointer r1
    //     0xb1e888: add             x1, x1, HEAP, lsl #32
    // 0xb1e88c: ldur            x0, [fp, #-0x28]
    // 0xb1e890: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1e890: add             x25, x1, x3, lsl #2
    //     0xb1e894: add             x25, x25, #0xf
    //     0xb1e898: str             w0, [x25]
    //     0xb1e89c: tbz             w0, #0, #0xb1e8b8
    //     0xb1e8a0: ldurb           w16, [x1, #-1]
    //     0xb1e8a4: ldurb           w17, [x0, #-1]
    //     0xb1e8a8: and             x16, x17, x16, lsr #2
    //     0xb1e8ac: tst             x16, HEAP, lsr #32
    //     0xb1e8b0: b.eq            #0xb1e8b8
    //     0xb1e8b4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1e8b8: b               #0xb1e94c
    // 0xb1e8bc: ldur            x2, [fp, #-0x18]
    // 0xb1e8c0: r0 = Container()
    //     0xb1e8c0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1e8c4: mov             x1, x0
    // 0xb1e8c8: stur            x0, [fp, #-0x20]
    // 0xb1e8cc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb1e8cc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb1e8d0: r0 = Container()
    //     0xb1e8d0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1e8d4: ldur            x0, [fp, #-0x18]
    // 0xb1e8d8: LoadField: r1 = r0->field_b
    //     0xb1e8d8: ldur            w1, [x0, #0xb]
    // 0xb1e8dc: LoadField: r2 = r0->field_f
    //     0xb1e8dc: ldur            w2, [x0, #0xf]
    // 0xb1e8e0: DecompressPointer r2
    //     0xb1e8e0: add             x2, x2, HEAP, lsl #32
    // 0xb1e8e4: LoadField: r3 = r2->field_b
    //     0xb1e8e4: ldur            w3, [x2, #0xb]
    // 0xb1e8e8: r2 = LoadInt32Instr(r1)
    //     0xb1e8e8: sbfx            x2, x1, #1, #0x1f
    // 0xb1e8ec: stur            x2, [fp, #-0x50]
    // 0xb1e8f0: r1 = LoadInt32Instr(r3)
    //     0xb1e8f0: sbfx            x1, x3, #1, #0x1f
    // 0xb1e8f4: cmp             x2, x1
    // 0xb1e8f8: b.ne            #0xb1e904
    // 0xb1e8fc: mov             x1, x0
    // 0xb1e900: r0 = _growToNextCapacity()
    //     0xb1e900: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1e904: ldur            x2, [fp, #-0x18]
    // 0xb1e908: ldur            x3, [fp, #-0x50]
    // 0xb1e90c: add             x0, x3, #1
    // 0xb1e910: lsl             x1, x0, #1
    // 0xb1e914: StoreField: r2->field_b = r1
    //     0xb1e914: stur            w1, [x2, #0xb]
    // 0xb1e918: LoadField: r1 = r2->field_f
    //     0xb1e918: ldur            w1, [x2, #0xf]
    // 0xb1e91c: DecompressPointer r1
    //     0xb1e91c: add             x1, x1, HEAP, lsl #32
    // 0xb1e920: ldur            x0, [fp, #-0x20]
    // 0xb1e924: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1e924: add             x25, x1, x3, lsl #2
    //     0xb1e928: add             x25, x25, #0xf
    //     0xb1e92c: str             w0, [x25]
    //     0xb1e930: tbz             w0, #0, #0xb1e94c
    //     0xb1e934: ldurb           w16, [x1, #-1]
    //     0xb1e938: ldurb           w17, [x0, #-1]
    //     0xb1e93c: and             x16, x17, x16, lsr #2
    //     0xb1e940: tst             x16, HEAP, lsr #32
    //     0xb1e944: b.eq            #0xb1e94c
    //     0xb1e948: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1e94c: ldur            x0, [fp, #-0x10]
    // 0xb1e950: LoadField: r1 = r0->field_13
    //     0xb1e950: ldur            w1, [x0, #0x13]
    // 0xb1e954: DecompressPointer r1
    //     0xb1e954: add             x1, x1, HEAP, lsl #32
    // 0xb1e958: r0 = of()
    //     0xb1e958: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1e95c: LoadField: r1 = r0->field_5b
    //     0xb1e95c: ldur            w1, [x0, #0x5b]
    // 0xb1e960: DecompressPointer r1
    //     0xb1e960: add             x1, x1, HEAP, lsl #32
    // 0xb1e964: stur            x1, [fp, #-0x20]
    // 0xb1e968: r0 = BoxDecoration()
    //     0xb1e968: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb1e96c: mov             x2, x0
    // 0xb1e970: ldur            x0, [fp, #-0x20]
    // 0xb1e974: stur            x2, [fp, #-0x28]
    // 0xb1e978: StoreField: r2->field_7 = r0
    //     0xb1e978: stur            w0, [x2, #7]
    // 0xb1e97c: r0 = Instance_BorderRadius
    //     0xb1e97c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb1e980: ldr             x0, [x0, #0x460]
    // 0xb1e984: StoreField: r2->field_13 = r0
    //     0xb1e984: stur            w0, [x2, #0x13]
    // 0xb1e988: r0 = Instance_BoxShape
    //     0xb1e988: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb1e98c: ldr             x0, [x0, #0x80]
    // 0xb1e990: StoreField: r2->field_23 = r0
    //     0xb1e990: stur            w0, [x2, #0x23]
    // 0xb1e994: ldur            x3, [fp, #-0x10]
    // 0xb1e998: LoadField: r1 = r3->field_13
    //     0xb1e998: ldur            w1, [x3, #0x13]
    // 0xb1e99c: DecompressPointer r1
    //     0xb1e99c: add             x1, x1, HEAP, lsl #32
    // 0xb1e9a0: r0 = of()
    //     0xb1e9a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1e9a4: LoadField: r1 = r0->field_87
    //     0xb1e9a4: ldur            w1, [x0, #0x87]
    // 0xb1e9a8: DecompressPointer r1
    //     0xb1e9a8: add             x1, x1, HEAP, lsl #32
    // 0xb1e9ac: LoadField: r0 = r1->field_2b
    //     0xb1e9ac: ldur            w0, [x1, #0x2b]
    // 0xb1e9b0: DecompressPointer r0
    //     0xb1e9b0: add             x0, x0, HEAP, lsl #32
    // 0xb1e9b4: r16 = 16.000000
    //     0xb1e9b4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb1e9b8: ldr             x16, [x16, #0x188]
    // 0xb1e9bc: r30 = Instance_Color
    //     0xb1e9bc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb1e9c0: stp             lr, x16, [SP]
    // 0xb1e9c4: mov             x1, x0
    // 0xb1e9c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1e9c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1e9cc: ldr             x4, [x4, #0xaa0]
    // 0xb1e9d0: r0 = copyWith()
    //     0xb1e9d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1e9d4: stur            x0, [fp, #-0x20]
    // 0xb1e9d8: r0 = Text()
    //     0xb1e9d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1e9dc: mov             x1, x0
    // 0xb1e9e0: r0 = "View All"
    //     0xb1e9e0: add             x0, PP, #0x55, lsl #12  ; [pp+0x55098] "View All"
    //     0xb1e9e4: ldr             x0, [x0, #0x98]
    // 0xb1e9e8: stur            x1, [fp, #-0x30]
    // 0xb1e9ec: StoreField: r1->field_b = r0
    //     0xb1e9ec: stur            w0, [x1, #0xb]
    // 0xb1e9f0: ldur            x0, [fp, #-0x20]
    // 0xb1e9f4: StoreField: r1->field_13 = r0
    //     0xb1e9f4: stur            w0, [x1, #0x13]
    // 0xb1e9f8: r0 = Center()
    //     0xb1e9f8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb1e9fc: mov             x1, x0
    // 0xb1ea00: r0 = Instance_Alignment
    //     0xb1ea00: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb1ea04: ldr             x0, [x0, #0xb10]
    // 0xb1ea08: stur            x1, [fp, #-0x20]
    // 0xb1ea0c: StoreField: r1->field_f = r0
    //     0xb1ea0c: stur            w0, [x1, #0xf]
    // 0xb1ea10: ldur            x0, [fp, #-0x30]
    // 0xb1ea14: StoreField: r1->field_b = r0
    //     0xb1ea14: stur            w0, [x1, #0xb]
    // 0xb1ea18: r0 = Container()
    //     0xb1ea18: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1ea1c: stur            x0, [fp, #-0x30]
    // 0xb1ea20: r16 = 40.000000
    //     0xb1ea20: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb1ea24: ldr             x16, [x16, #8]
    // 0xb1ea28: r30 = 110.000000
    //     0xb1ea28: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xb1ea2c: ldr             lr, [lr, #0x770]
    // 0xb1ea30: stp             lr, x16, [SP, #0x10]
    // 0xb1ea34: ldur            x16, [fp, #-0x28]
    // 0xb1ea38: ldur            lr, [fp, #-0x20]
    // 0xb1ea3c: stp             lr, x16, [SP]
    // 0xb1ea40: mov             x1, x0
    // 0xb1ea44: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb1ea44: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb1ea48: ldr             x4, [x4, #0x8c0]
    // 0xb1ea4c: r0 = Container()
    //     0xb1ea4c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1ea50: r0 = InkWell()
    //     0xb1ea50: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb1ea54: mov             x3, x0
    // 0xb1ea58: ldur            x0, [fp, #-0x30]
    // 0xb1ea5c: stur            x3, [fp, #-0x20]
    // 0xb1ea60: StoreField: r3->field_b = r0
    //     0xb1ea60: stur            w0, [x3, #0xb]
    // 0xb1ea64: ldur            x2, [fp, #-0x10]
    // 0xb1ea68: r1 = Function '<anonymous closure>':.
    //     0xb1ea68: add             x1, PP, #0x55, lsl #12  ; [pp+0x550a0] AnonymousClosure: (0xb1ebec), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xb943d8)
    //     0xb1ea6c: ldr             x1, [x1, #0xa0]
    // 0xb1ea70: r0 = AllocateClosure()
    //     0xb1ea70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1ea74: mov             x1, x0
    // 0xb1ea78: ldur            x0, [fp, #-0x20]
    // 0xb1ea7c: StoreField: r0->field_f = r1
    //     0xb1ea7c: stur            w1, [x0, #0xf]
    // 0xb1ea80: r1 = true
    //     0xb1ea80: add             x1, NULL, #0x20  ; true
    // 0xb1ea84: StoreField: r0->field_43 = r1
    //     0xb1ea84: stur            w1, [x0, #0x43]
    // 0xb1ea88: r2 = Instance_BoxShape
    //     0xb1ea88: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb1ea8c: ldr             x2, [x2, #0x80]
    // 0xb1ea90: StoreField: r0->field_47 = r2
    //     0xb1ea90: stur            w2, [x0, #0x47]
    // 0xb1ea94: StoreField: r0->field_6f = r1
    //     0xb1ea94: stur            w1, [x0, #0x6f]
    // 0xb1ea98: r2 = false
    //     0xb1ea98: add             x2, NULL, #0x30  ; false
    // 0xb1ea9c: StoreField: r0->field_73 = r2
    //     0xb1ea9c: stur            w2, [x0, #0x73]
    // 0xb1eaa0: StoreField: r0->field_83 = r1
    //     0xb1eaa0: stur            w1, [x0, #0x83]
    // 0xb1eaa4: StoreField: r0->field_7b = r2
    //     0xb1eaa4: stur            w2, [x0, #0x7b]
    // 0xb1eaa8: r0 = Padding()
    //     0xb1eaa8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1eaac: mov             x2, x0
    // 0xb1eab0: r0 = Instance_EdgeInsets
    //     0xb1eab0: add             x0, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xb1eab4: ldr             x0, [x0, #0x110]
    // 0xb1eab8: stur            x2, [fp, #-0x10]
    // 0xb1eabc: StoreField: r2->field_f = r0
    //     0xb1eabc: stur            w0, [x2, #0xf]
    // 0xb1eac0: ldur            x0, [fp, #-0x20]
    // 0xb1eac4: StoreField: r2->field_b = r0
    //     0xb1eac4: stur            w0, [x2, #0xb]
    // 0xb1eac8: ldur            x0, [fp, #-0x18]
    // 0xb1eacc: LoadField: r1 = r0->field_b
    //     0xb1eacc: ldur            w1, [x0, #0xb]
    // 0xb1ead0: LoadField: r3 = r0->field_f
    //     0xb1ead0: ldur            w3, [x0, #0xf]
    // 0xb1ead4: DecompressPointer r3
    //     0xb1ead4: add             x3, x3, HEAP, lsl #32
    // 0xb1ead8: LoadField: r4 = r3->field_b
    //     0xb1ead8: ldur            w4, [x3, #0xb]
    // 0xb1eadc: r3 = LoadInt32Instr(r1)
    //     0xb1eadc: sbfx            x3, x1, #1, #0x1f
    // 0xb1eae0: stur            x3, [fp, #-0x50]
    // 0xb1eae4: r1 = LoadInt32Instr(r4)
    //     0xb1eae4: sbfx            x1, x4, #1, #0x1f
    // 0xb1eae8: cmp             x3, x1
    // 0xb1eaec: b.ne            #0xb1eaf8
    // 0xb1eaf0: mov             x1, x0
    // 0xb1eaf4: r0 = _growToNextCapacity()
    //     0xb1eaf4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1eaf8: ldur            x4, [fp, #-8]
    // 0xb1eafc: ldur            x2, [fp, #-0x18]
    // 0xb1eb00: ldur            x3, [fp, #-0x50]
    // 0xb1eb04: add             x0, x3, #1
    // 0xb1eb08: lsl             x1, x0, #1
    // 0xb1eb0c: StoreField: r2->field_b = r1
    //     0xb1eb0c: stur            w1, [x2, #0xb]
    // 0xb1eb10: LoadField: r1 = r2->field_f
    //     0xb1eb10: ldur            w1, [x2, #0xf]
    // 0xb1eb14: DecompressPointer r1
    //     0xb1eb14: add             x1, x1, HEAP, lsl #32
    // 0xb1eb18: ldur            x0, [fp, #-0x10]
    // 0xb1eb1c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1eb1c: add             x25, x1, x3, lsl #2
    //     0xb1eb20: add             x25, x25, #0xf
    //     0xb1eb24: str             w0, [x25]
    //     0xb1eb28: tbz             w0, #0, #0xb1eb44
    //     0xb1eb2c: ldurb           w16, [x1, #-1]
    //     0xb1eb30: ldurb           w17, [x0, #-1]
    //     0xb1eb34: and             x16, x17, x16, lsr #2
    //     0xb1eb38: tst             x16, HEAP, lsr #32
    //     0xb1eb3c: b.eq            #0xb1eb44
    //     0xb1eb40: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1eb44: r0 = Column()
    //     0xb1eb44: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb1eb48: mov             x1, x0
    // 0xb1eb4c: r0 = Instance_Axis
    //     0xb1eb4c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb1eb50: stur            x1, [fp, #-0x10]
    // 0xb1eb54: StoreField: r1->field_f = r0
    //     0xb1eb54: stur            w0, [x1, #0xf]
    // 0xb1eb58: r0 = Instance_MainAxisAlignment
    //     0xb1eb58: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb1eb5c: ldr             x0, [x0, #0xa08]
    // 0xb1eb60: StoreField: r1->field_13 = r0
    //     0xb1eb60: stur            w0, [x1, #0x13]
    // 0xb1eb64: r0 = Instance_MainAxisSize
    //     0xb1eb64: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb1eb68: ldr             x0, [x0, #0xdd0]
    // 0xb1eb6c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb1eb6c: stur            w0, [x1, #0x17]
    // 0xb1eb70: ldur            x0, [fp, #-8]
    // 0xb1eb74: StoreField: r1->field_1b = r0
    //     0xb1eb74: stur            w0, [x1, #0x1b]
    // 0xb1eb78: r0 = Instance_VerticalDirection
    //     0xb1eb78: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1eb7c: ldr             x0, [x0, #0xa20]
    // 0xb1eb80: StoreField: r1->field_23 = r0
    //     0xb1eb80: stur            w0, [x1, #0x23]
    // 0xb1eb84: r0 = Instance_Clip
    //     0xb1eb84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1eb88: ldr             x0, [x0, #0x38]
    // 0xb1eb8c: StoreField: r1->field_2b = r0
    //     0xb1eb8c: stur            w0, [x1, #0x2b]
    // 0xb1eb90: StoreField: r1->field_2f = rZR
    //     0xb1eb90: stur            xzr, [x1, #0x2f]
    // 0xb1eb94: ldur            x0, [fp, #-0x18]
    // 0xb1eb98: StoreField: r1->field_b = r0
    //     0xb1eb98: stur            w0, [x1, #0xb]
    // 0xb1eb9c: r0 = Padding()
    //     0xb1eb9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1eba0: r1 = Instance_EdgeInsets
    //     0xb1eba0: add             x1, PP, #0x55, lsl #12  ; [pp+0x550a8] Obj!EdgeInsets@d58761
    //     0xb1eba4: ldr             x1, [x1, #0xa8]
    // 0xb1eba8: StoreField: r0->field_f = r1
    //     0xb1eba8: stur            w1, [x0, #0xf]
    // 0xb1ebac: ldur            x1, [fp, #-0x10]
    // 0xb1ebb0: StoreField: r0->field_b = r1
    //     0xb1ebb0: stur            w1, [x0, #0xb]
    // 0xb1ebb4: LeaveFrame
    //     0xb1ebb4: mov             SP, fp
    //     0xb1ebb8: ldp             fp, lr, [SP], #0x10
    // 0xb1ebbc: ret
    //     0xb1ebbc: ret             
    // 0xb1ebc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1ebc0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1ebc4: b               #0xb1e228
    // 0xb1ebc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ebc8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ebcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ebcc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ebd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ebd0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ebd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ebd4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ebd8: r9 = _pageController
    //     0xb1ebd8: add             x9, PP, #0x55, lsl #12  ; [pp+0x550b0] Field <_TestimonialCarouselState@1629062372._pageController@1629062372>: late (offset: 0x14)
    //     0xb1ebdc: ldr             x9, [x9, #0xb0]
    // 0xb1ebe0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb1ebe0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb1ebe4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ebe4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ebe8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ebe8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb1ebec, size: 0x1a8
    // 0xb1ebec: EnterFrame
    //     0xb1ebec: stp             fp, lr, [SP, #-0x10]!
    //     0xb1ebf0: mov             fp, SP
    // 0xb1ebf4: AllocStack(0x40)
    //     0xb1ebf4: sub             SP, SP, #0x40
    // 0xb1ebf8: SetupParameters()
    //     0xb1ebf8: ldr             x0, [fp, #0x10]
    //     0xb1ebfc: ldur            w1, [x0, #0x17]
    //     0xb1ec00: add             x1, x1, HEAP, lsl #32
    //     0xb1ec04: stur            x1, [fp, #-0x20]
    // 0xb1ec08: CheckStackOverflow
    //     0xb1ec08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1ec0c: cmp             SP, x16
    //     0xb1ec10: b.ls            #0xb1ed84
    // 0xb1ec14: LoadField: r0 = r1->field_f
    //     0xb1ec14: ldur            w0, [x1, #0xf]
    // 0xb1ec18: DecompressPointer r0
    //     0xb1ec18: add             x0, x0, HEAP, lsl #32
    // 0xb1ec1c: LoadField: r2 = r0->field_b
    //     0xb1ec1c: ldur            w2, [x0, #0xb]
    // 0xb1ec20: DecompressPointer r2
    //     0xb1ec20: add             x2, x2, HEAP, lsl #32
    // 0xb1ec24: stur            x2, [fp, #-0x18]
    // 0xb1ec28: cmp             w2, NULL
    // 0xb1ec2c: b.eq            #0xb1ed8c
    // 0xb1ec30: LoadField: r0 = r2->field_1b
    //     0xb1ec30: ldur            w0, [x2, #0x1b]
    // 0xb1ec34: DecompressPointer r0
    //     0xb1ec34: add             x0, x0, HEAP, lsl #32
    // 0xb1ec38: stur            x0, [fp, #-0x10]
    // 0xb1ec3c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb1ec3c: ldur            w3, [x2, #0x17]
    // 0xb1ec40: DecompressPointer r3
    //     0xb1ec40: add             x3, x3, HEAP, lsl #32
    // 0xb1ec44: stur            x3, [fp, #-8]
    // 0xb1ec48: r0 = EventData()
    //     0xb1ec48: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xb1ec4c: mov             x1, x0
    // 0xb1ec50: r0 = "product_page"
    //     0xb1ec50: add             x0, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xb1ec54: ldr             x0, [x0, #0x480]
    // 0xb1ec58: stur            x1, [fp, #-0x28]
    // 0xb1ec5c: StoreField: r1->field_13 = r0
    //     0xb1ec5c: stur            w0, [x1, #0x13]
    // 0xb1ec60: ldur            x0, [fp, #-8]
    // 0xb1ec64: StoreField: r1->field_53 = r0
    //     0xb1ec64: stur            w0, [x1, #0x53]
    // 0xb1ec68: ldur            x0, [fp, #-0x10]
    // 0xb1ec6c: StoreField: r1->field_57 = r0
    //     0xb1ec6c: stur            w0, [x1, #0x57]
    // 0xb1ec70: r0 = "view_all"
    //     0xb1ec70: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ba0] "view_all"
    //     0xb1ec74: ldr             x0, [x0, #0xba0]
    // 0xb1ec78: StoreField: r1->field_eb = r0
    //     0xb1ec78: stur            w0, [x1, #0xeb]
    // 0xb1ec7c: r0 = EventsRequest()
    //     0xb1ec7c: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xb1ec80: mov             x1, x0
    // 0xb1ec84: r0 = "cta_clicked"
    //     0xb1ec84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2edf8] "cta_clicked"
    //     0xb1ec88: ldr             x0, [x0, #0xdf8]
    // 0xb1ec8c: StoreField: r1->field_7 = r0
    //     0xb1ec8c: stur            w0, [x1, #7]
    // 0xb1ec90: ldur            x0, [fp, #-0x28]
    // 0xb1ec94: StoreField: r1->field_b = r0
    //     0xb1ec94: stur            w0, [x1, #0xb]
    // 0xb1ec98: ldur            x0, [fp, #-0x18]
    // 0xb1ec9c: LoadField: r2 = r0->field_27
    //     0xb1ec9c: ldur            w2, [x0, #0x27]
    // 0xb1eca0: DecompressPointer r2
    //     0xb1eca0: add             x2, x2, HEAP, lsl #32
    // 0xb1eca4: stp             x1, x2, [SP]
    // 0xb1eca8: r4 = 0
    //     0xb1eca8: movz            x4, #0
    // 0xb1ecac: ldr             x0, [SP, #8]
    // 0xb1ecb0: r5 = UnlinkedCall_0x613b5c
    //     0xb1ecb0: add             x16, PP, #0x55, lsl #12  ; [pp+0x550b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1ecb4: ldp             x5, lr, [x16, #0xb8]
    // 0xb1ecb8: blr             lr
    // 0xb1ecbc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb1ecbc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1ecc0: ldr             x0, [x0, #0x1c80]
    //     0xb1ecc4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1ecc8: cmp             w0, w16
    //     0xb1eccc: b.ne            #0xb1ecd8
    //     0xb1ecd0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb1ecd4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb1ecd8: r1 = Null
    //     0xb1ecd8: mov             x1, NULL
    // 0xb1ecdc: r2 = 12
    //     0xb1ecdc: movz            x2, #0xc
    // 0xb1ece0: r0 = AllocateArray()
    //     0xb1ece0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1ece4: r16 = "previousScreenSource"
    //     0xb1ece4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xb1ece8: ldr             x16, [x16, #0x448]
    // 0xb1ecec: StoreField: r0->field_f = r16
    //     0xb1ecec: stur            w16, [x0, #0xf]
    // 0xb1ecf0: ldur            x1, [fp, #-0x20]
    // 0xb1ecf4: LoadField: r2 = r1->field_f
    //     0xb1ecf4: ldur            w2, [x1, #0xf]
    // 0xb1ecf8: DecompressPointer r2
    //     0xb1ecf8: add             x2, x2, HEAP, lsl #32
    // 0xb1ecfc: LoadField: r1 = r2->field_b
    //     0xb1ecfc: ldur            w1, [x2, #0xb]
    // 0xb1ed00: DecompressPointer r1
    //     0xb1ed00: add             x1, x1, HEAP, lsl #32
    // 0xb1ed04: cmp             w1, NULL
    // 0xb1ed08: b.eq            #0xb1ed90
    // 0xb1ed0c: LoadField: r2 = r1->field_23
    //     0xb1ed0c: ldur            w2, [x1, #0x23]
    // 0xb1ed10: DecompressPointer r2
    //     0xb1ed10: add             x2, x2, HEAP, lsl #32
    // 0xb1ed14: StoreField: r0->field_13 = r2
    //     0xb1ed14: stur            w2, [x0, #0x13]
    // 0xb1ed18: r16 = "screenSource"
    //     0xb1ed18: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0xb1ed1c: ldr             x16, [x16, #0x450]
    // 0xb1ed20: ArrayStore: r0[0] = r16  ; List_4
    //     0xb1ed20: stur            w16, [x0, #0x17]
    // 0xb1ed24: LoadField: r2 = r1->field_1f
    //     0xb1ed24: ldur            w2, [x1, #0x1f]
    // 0xb1ed28: DecompressPointer r2
    //     0xb1ed28: add             x2, x2, HEAP, lsl #32
    // 0xb1ed2c: StoreField: r0->field_1b = r2
    //     0xb1ed2c: stur            w2, [x0, #0x1b]
    // 0xb1ed30: r16 = "widgetType"
    //     0xb1ed30: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f338] "widgetType"
    //     0xb1ed34: ldr             x16, [x16, #0x338]
    // 0xb1ed38: StoreField: r0->field_1f = r16
    //     0xb1ed38: stur            w16, [x0, #0x1f]
    // 0xb1ed3c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb1ed3c: ldur            w2, [x1, #0x17]
    // 0xb1ed40: DecompressPointer r2
    //     0xb1ed40: add             x2, x2, HEAP, lsl #32
    // 0xb1ed44: StoreField: r0->field_23 = r2
    //     0xb1ed44: stur            w2, [x0, #0x23]
    // 0xb1ed48: r16 = <String, String?>
    //     0xb1ed48: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0xb1ed4c: ldr             x16, [x16, #0x3c8]
    // 0xb1ed50: stp             x0, x16, [SP]
    // 0xb1ed54: r0 = Map._fromLiteral()
    //     0xb1ed54: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xb1ed58: r16 = "/testimonials"
    //     0xb1ed58: add             x16, PP, #0xd, lsl #12  ; [pp+0xd898] "/testimonials"
    //     0xb1ed5c: ldr             x16, [x16, #0x898]
    // 0xb1ed60: stp             x16, NULL, [SP, #8]
    // 0xb1ed64: str             x0, [SP]
    // 0xb1ed68: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb1ed68: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb1ed6c: ldr             x4, [x4, #0x438]
    // 0xb1ed70: r0 = GetNavigation.toNamed()
    //     0xb1ed70: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb1ed74: r0 = Null
    //     0xb1ed74: mov             x0, NULL
    // 0xb1ed78: LeaveFrame
    //     0xb1ed78: mov             SP, fp
    //     0xb1ed7c: ldp             fp, lr, [SP], #0x10
    // 0xb1ed80: ret
    //     0xb1ed80: ret             
    // 0xb1ed84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1ed84: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1ed88: b               #0xb1ec14
    // 0xb1ed8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ed8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ed90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ed90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] AnimatedContainer <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb1ed94, size: 0x70
    // 0xb1ed94: EnterFrame
    //     0xb1ed94: stp             fp, lr, [SP, #-0x10]!
    //     0xb1ed98: mov             fp, SP
    // 0xb1ed9c: ldr             x0, [fp, #0x20]
    // 0xb1eda0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb1eda0: ldur            w1, [x0, #0x17]
    // 0xb1eda4: DecompressPointer r1
    //     0xb1eda4: add             x1, x1, HEAP, lsl #32
    // 0xb1eda8: CheckStackOverflow
    //     0xb1eda8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1edac: cmp             SP, x16
    //     0xb1edb0: b.ls            #0xb1edf8
    // 0xb1edb4: LoadField: r0 = r1->field_f
    //     0xb1edb4: ldur            w0, [x1, #0xf]
    // 0xb1edb8: DecompressPointer r0
    //     0xb1edb8: add             x0, x0, HEAP, lsl #32
    // 0xb1edbc: LoadField: r1 = r0->field_b
    //     0xb1edbc: ldur            w1, [x0, #0xb]
    // 0xb1edc0: DecompressPointer r1
    //     0xb1edc0: add             x1, x1, HEAP, lsl #32
    // 0xb1edc4: cmp             w1, NULL
    // 0xb1edc8: b.eq            #0xb1ee00
    // 0xb1edcc: LoadField: r2 = r1->field_b
    //     0xb1edcc: ldur            w2, [x1, #0xb]
    // 0xb1edd0: DecompressPointer r2
    //     0xb1edd0: add             x2, x2, HEAP, lsl #32
    // 0xb1edd4: ldr             x1, [fp, #0x10]
    // 0xb1edd8: r3 = LoadInt32Instr(r1)
    //     0xb1edd8: sbfx            x3, x1, #1, #0x1f
    //     0xb1eddc: tbz             w1, #0, #0xb1ede4
    //     0xb1ede0: ldur            x3, [x1, #7]
    // 0xb1ede4: mov             x1, x0
    // 0xb1ede8: r0 = glassThemeSlider()
    //     0xb1ede8: bl              #0xb1ee04  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::glassThemeSlider
    // 0xb1edec: LeaveFrame
    //     0xb1edec: mov             SP, fp
    //     0xb1edf0: ldp             fp, lr, [SP], #0x10
    // 0xb1edf4: ret
    //     0xb1edf4: ret             
    // 0xb1edf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1edf8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1edfc: b               #0xb1edb4
    // 0xb1ee00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ee00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ glassThemeSlider(/* No info */) {
    // ** addr: 0xb1ee04, size: 0x1000
    // 0xb1ee04: EnterFrame
    //     0xb1ee04: stp             fp, lr, [SP, #-0x10]!
    //     0xb1ee08: mov             fp, SP
    // 0xb1ee0c: AllocStack(0x78)
    //     0xb1ee0c: sub             SP, SP, #0x78
    // 0xb1ee10: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb1ee10: stur            x1, [fp, #-8]
    //     0xb1ee14: stur            x2, [fp, #-0x10]
    //     0xb1ee18: stur            x3, [fp, #-0x18]
    // 0xb1ee1c: CheckStackOverflow
    //     0xb1ee1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1ee20: cmp             SP, x16
    //     0xb1ee24: b.ls            #0xb1fdac
    // 0xb1ee28: r1 = 2
    //     0xb1ee28: movz            x1, #0x2
    // 0xb1ee2c: r0 = AllocateContext()
    //     0xb1ee2c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb1ee30: mov             x2, x0
    // 0xb1ee34: ldur            x0, [fp, #-0x10]
    // 0xb1ee38: stur            x2, [fp, #-0x20]
    // 0xb1ee3c: StoreField: r2->field_f = r0
    //     0xb1ee3c: stur            w0, [x2, #0xf]
    // 0xb1ee40: ldur            x3, [fp, #-0x18]
    // 0xb1ee44: r0 = BoxInt64Instr(r3)
    //     0xb1ee44: sbfiz           x0, x3, #1, #0x1f
    //     0xb1ee48: cmp             x3, x0, asr #1
    //     0xb1ee4c: b.eq            #0xb1ee58
    //     0xb1ee50: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb1ee54: stur            x3, [x0, #7]
    // 0xb1ee58: StoreField: r2->field_13 = r0
    //     0xb1ee58: stur            w0, [x2, #0x13]
    // 0xb1ee5c: ldur            x0, [fp, #-8]
    // 0xb1ee60: LoadField: r1 = r0->field_f
    //     0xb1ee60: ldur            w1, [x0, #0xf]
    // 0xb1ee64: DecompressPointer r1
    //     0xb1ee64: add             x1, x1, HEAP, lsl #32
    // 0xb1ee68: cmp             w1, NULL
    // 0xb1ee6c: b.eq            #0xb1fdb4
    // 0xb1ee70: r0 = of()
    //     0xb1ee70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1ee74: LoadField: r1 = r0->field_5b
    //     0xb1ee74: ldur            w1, [x0, #0x5b]
    // 0xb1ee78: DecompressPointer r1
    //     0xb1ee78: add             x1, x1, HEAP, lsl #32
    // 0xb1ee7c: r0 = LoadClassIdInstr(r1)
    //     0xb1ee7c: ldur            x0, [x1, #-1]
    //     0xb1ee80: ubfx            x0, x0, #0xc, #0x14
    // 0xb1ee84: d0 = 0.030000
    //     0xb1ee84: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb1ee88: ldr             d0, [x17, #0x238]
    // 0xb1ee8c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb1ee8c: sub             lr, x0, #0xffa
    //     0xb1ee90: ldr             lr, [x21, lr, lsl #3]
    //     0xb1ee94: blr             lr
    // 0xb1ee98: stur            x0, [fp, #-0x10]
    // 0xb1ee9c: r0 = Radius()
    //     0xb1ee9c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb1eea0: d0 = 12.000000
    //     0xb1eea0: fmov            d0, #12.00000000
    // 0xb1eea4: stur            x0, [fp, #-0x28]
    // 0xb1eea8: StoreField: r0->field_7 = d0
    //     0xb1eea8: stur            d0, [x0, #7]
    // 0xb1eeac: StoreField: r0->field_f = d0
    //     0xb1eeac: stur            d0, [x0, #0xf]
    // 0xb1eeb0: r0 = BorderRadius()
    //     0xb1eeb0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb1eeb4: mov             x1, x0
    // 0xb1eeb8: ldur            x0, [fp, #-0x28]
    // 0xb1eebc: stur            x1, [fp, #-0x30]
    // 0xb1eec0: StoreField: r1->field_7 = r0
    //     0xb1eec0: stur            w0, [x1, #7]
    // 0xb1eec4: StoreField: r1->field_b = r0
    //     0xb1eec4: stur            w0, [x1, #0xb]
    // 0xb1eec8: StoreField: r1->field_f = r0
    //     0xb1eec8: stur            w0, [x1, #0xf]
    // 0xb1eecc: StoreField: r1->field_13 = r0
    //     0xb1eecc: stur            w0, [x1, #0x13]
    // 0xb1eed0: r0 = RoundedRectangleBorder()
    //     0xb1eed0: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb1eed4: mov             x1, x0
    // 0xb1eed8: ldur            x0, [fp, #-0x30]
    // 0xb1eedc: stur            x1, [fp, #-0x28]
    // 0xb1eee0: StoreField: r1->field_b = r0
    //     0xb1eee0: stur            w0, [x1, #0xb]
    // 0xb1eee4: r0 = Instance_BorderSide
    //     0xb1eee4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb1eee8: ldr             x0, [x0, #0xe20]
    // 0xb1eeec: StoreField: r1->field_7 = r0
    //     0xb1eeec: stur            w0, [x1, #7]
    // 0xb1eef0: ldur            x2, [fp, #-0x20]
    // 0xb1eef4: LoadField: r0 = r2->field_f
    //     0xb1eef4: ldur            w0, [x2, #0xf]
    // 0xb1eef8: DecompressPointer r0
    //     0xb1eef8: add             x0, x0, HEAP, lsl #32
    // 0xb1eefc: LoadField: r3 = r2->field_13
    //     0xb1eefc: ldur            w3, [x2, #0x13]
    // 0xb1ef00: DecompressPointer r3
    //     0xb1ef00: add             x3, x3, HEAP, lsl #32
    // 0xb1ef04: stp             x3, x0, [SP]
    // 0xb1ef08: r4 = 0
    //     0xb1ef08: movz            x4, #0
    // 0xb1ef0c: ldr             x0, [SP, #8]
    // 0xb1ef10: r5 = UnlinkedCall_0x613b5c
    //     0xb1ef10: add             x16, PP, #0x55, lsl #12  ; [pp+0x550c8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1ef14: ldp             x5, lr, [x16, #0xc8]
    // 0xb1ef18: blr             lr
    // 0xb1ef1c: cmp             w0, NULL
    // 0xb1ef20: b.eq            #0xb1fdb8
    // 0xb1ef24: LoadField: r1 = r0->field_cb
    //     0xb1ef24: ldur            w1, [x0, #0xcb]
    // 0xb1ef28: DecompressPointer r1
    //     0xb1ef28: add             x1, x1, HEAP, lsl #32
    // 0xb1ef2c: cmp             w1, NULL
    // 0xb1ef30: b.ne            #0xb1ef38
    // 0xb1ef34: r1 = ""
    //     0xb1ef34: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb1ef38: ldur            x0, [fp, #-8]
    // 0xb1ef3c: ldur            x2, [fp, #-0x20]
    // 0xb1ef40: r0 = capitalizeFirstWord()
    //     0xb1ef40: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb1ef44: mov             x2, x0
    // 0xb1ef48: ldur            x0, [fp, #-8]
    // 0xb1ef4c: stur            x2, [fp, #-0x30]
    // 0xb1ef50: LoadField: r1 = r0->field_f
    //     0xb1ef50: ldur            w1, [x0, #0xf]
    // 0xb1ef54: DecompressPointer r1
    //     0xb1ef54: add             x1, x1, HEAP, lsl #32
    // 0xb1ef58: cmp             w1, NULL
    // 0xb1ef5c: b.eq            #0xb1fdbc
    // 0xb1ef60: r0 = of()
    //     0xb1ef60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1ef64: LoadField: r1 = r0->field_87
    //     0xb1ef64: ldur            w1, [x0, #0x87]
    // 0xb1ef68: DecompressPointer r1
    //     0xb1ef68: add             x1, x1, HEAP, lsl #32
    // 0xb1ef6c: LoadField: r0 = r1->field_7
    //     0xb1ef6c: ldur            w0, [x1, #7]
    // 0xb1ef70: DecompressPointer r0
    //     0xb1ef70: add             x0, x0, HEAP, lsl #32
    // 0xb1ef74: r16 = 32.000000
    //     0xb1ef74: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb1ef78: ldr             x16, [x16, #0x848]
    // 0xb1ef7c: r30 = Instance_Color
    //     0xb1ef7c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1ef80: stp             lr, x16, [SP]
    // 0xb1ef84: mov             x1, x0
    // 0xb1ef88: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1ef88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1ef8c: ldr             x4, [x4, #0xaa0]
    // 0xb1ef90: r0 = copyWith()
    //     0xb1ef90: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1ef94: stur            x0, [fp, #-0x38]
    // 0xb1ef98: r0 = Text()
    //     0xb1ef98: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1ef9c: mov             x3, x0
    // 0xb1efa0: ldur            x0, [fp, #-0x30]
    // 0xb1efa4: stur            x3, [fp, #-0x40]
    // 0xb1efa8: StoreField: r3->field_b = r0
    //     0xb1efa8: stur            w0, [x3, #0xb]
    // 0xb1efac: ldur            x0, [fp, #-0x38]
    // 0xb1efb0: StoreField: r3->field_13 = r0
    //     0xb1efb0: stur            w0, [x3, #0x13]
    // 0xb1efb4: r1 = <Widget>
    //     0xb1efb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1efb8: r2 = 18
    //     0xb1efb8: movz            x2, #0x12
    // 0xb1efbc: r0 = AllocateArray()
    //     0xb1efbc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1efc0: mov             x3, x0
    // 0xb1efc4: ldur            x0, [fp, #-0x40]
    // 0xb1efc8: stur            x3, [fp, #-0x30]
    // 0xb1efcc: StoreField: r3->field_f = r0
    //     0xb1efcc: stur            w0, [x3, #0xf]
    // 0xb1efd0: r16 = Instance_SizedBox
    //     0xb1efd0: add             x16, PP, #0x55, lsl #12  ; [pp+0x550d8] Obj!SizedBox@d68001
    //     0xb1efd4: ldr             x16, [x16, #0xd8]
    // 0xb1efd8: StoreField: r3->field_13 = r16
    //     0xb1efd8: stur            w16, [x3, #0x13]
    // 0xb1efdc: r1 = <Widget>
    //     0xb1efdc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1efe0: r2 = 18
    //     0xb1efe0: movz            x2, #0x12
    // 0xb1efe4: r0 = AllocateArray()
    //     0xb1efe4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1efe8: stur            x0, [fp, #-0x38]
    // 0xb1efec: r16 = Instance_Icon
    //     0xb1efec: add             x16, PP, #0x52, lsl #12  ; [pp+0x520d0] Obj!Icon@d66371
    //     0xb1eff0: ldr             x16, [x16, #0xd0]
    // 0xb1eff4: StoreField: r0->field_f = r16
    //     0xb1eff4: stur            w16, [x0, #0xf]
    // 0xb1eff8: r16 = Instance_SizedBox
    //     0xb1eff8: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xb1effc: ldr             x16, [x16, #0xe98]
    // 0xb1f000: StoreField: r0->field_13 = r16
    //     0xb1f000: stur            w16, [x0, #0x13]
    // 0xb1f004: ldur            x2, [fp, #-8]
    // 0xb1f008: LoadField: r1 = r2->field_f
    //     0xb1f008: ldur            w1, [x2, #0xf]
    // 0xb1f00c: DecompressPointer r1
    //     0xb1f00c: add             x1, x1, HEAP, lsl #32
    // 0xb1f010: cmp             w1, NULL
    // 0xb1f014: b.eq            #0xb1fdc0
    // 0xb1f018: r0 = of()
    //     0xb1f018: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1f01c: LoadField: r1 = r0->field_87
    //     0xb1f01c: ldur            w1, [x0, #0x87]
    // 0xb1f020: DecompressPointer r1
    //     0xb1f020: add             x1, x1, HEAP, lsl #32
    // 0xb1f024: LoadField: r0 = r1->field_2b
    //     0xb1f024: ldur            w0, [x1, #0x2b]
    // 0xb1f028: DecompressPointer r0
    //     0xb1f028: add             x0, x0, HEAP, lsl #32
    // 0xb1f02c: stur            x0, [fp, #-0x40]
    // 0xb1f030: r1 = Instance_Color
    //     0xb1f030: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1f034: d0 = 0.700000
    //     0xb1f034: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb1f038: ldr             d0, [x17, #0xf48]
    // 0xb1f03c: r0 = withOpacity()
    //     0xb1f03c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb1f040: r16 = 14.000000
    //     0xb1f040: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb1f044: ldr             x16, [x16, #0x1d8]
    // 0xb1f048: stp             x0, x16, [SP]
    // 0xb1f04c: ldur            x1, [fp, #-0x40]
    // 0xb1f050: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1f050: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1f054: ldr             x4, [x4, #0xaa0]
    // 0xb1f058: r0 = copyWith()
    //     0xb1f058: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1f05c: stur            x0, [fp, #-0x40]
    // 0xb1f060: r0 = Text()
    //     0xb1f060: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1f064: mov             x1, x0
    // 0xb1f068: r0 = "Verified Buyer"
    //     0xb1f068: add             x0, PP, #0x52, lsl #12  ; [pp+0x520d8] "Verified Buyer"
    //     0xb1f06c: ldr             x0, [x0, #0xd8]
    // 0xb1f070: StoreField: r1->field_b = r0
    //     0xb1f070: stur            w0, [x1, #0xb]
    // 0xb1f074: ldur            x0, [fp, #-0x40]
    // 0xb1f078: StoreField: r1->field_13 = r0
    //     0xb1f078: stur            w0, [x1, #0x13]
    // 0xb1f07c: mov             x0, x1
    // 0xb1f080: ldur            x1, [fp, #-0x38]
    // 0xb1f084: ArrayStore: r1[2] = r0  ; List_4
    //     0xb1f084: add             x25, x1, #0x17
    //     0xb1f088: str             w0, [x25]
    //     0xb1f08c: tbz             w0, #0, #0xb1f0a8
    //     0xb1f090: ldurb           w16, [x1, #-1]
    //     0xb1f094: ldurb           w17, [x0, #-1]
    //     0xb1f098: and             x16, x17, x16, lsr #2
    //     0xb1f09c: tst             x16, HEAP, lsr #32
    //     0xb1f0a0: b.eq            #0xb1f0a8
    //     0xb1f0a4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1f0a8: r0 = Container()
    //     0xb1f0a8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1f0ac: stur            x0, [fp, #-0x40]
    // 0xb1f0b0: r16 = 5.000000
    //     0xb1f0b0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb1f0b4: ldr             x16, [x16, #0xcf0]
    // 0xb1f0b8: str             x16, [SP]
    // 0xb1f0bc: mov             x1, x0
    // 0xb1f0c0: r4 = const [0, 0x2, 0x1, 0x1, width, 0x1, null]
    //     0xb1f0c0: add             x4, PP, #0x52, lsl #12  ; [pp+0x520e0] List(7) [0, 0x2, 0x1, 0x1, "width", 0x1, Null]
    //     0xb1f0c4: ldr             x4, [x4, #0xe0]
    // 0xb1f0c8: r0 = Container()
    //     0xb1f0c8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1f0cc: ldur            x1, [fp, #-0x38]
    // 0xb1f0d0: ldur            x0, [fp, #-0x40]
    // 0xb1f0d4: ArrayStore: r1[3] = r0  ; List_4
    //     0xb1f0d4: add             x25, x1, #0x1b
    //     0xb1f0d8: str             w0, [x25]
    //     0xb1f0dc: tbz             w0, #0, #0xb1f0f8
    //     0xb1f0e0: ldurb           w16, [x1, #-1]
    //     0xb1f0e4: ldurb           w17, [x0, #-1]
    //     0xb1f0e8: and             x16, x17, x16, lsr #2
    //     0xb1f0ec: tst             x16, HEAP, lsr #32
    //     0xb1f0f0: b.eq            #0xb1f0f8
    //     0xb1f0f4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1f0f8: ldur            x2, [fp, #-0x20]
    // 0xb1f0fc: LoadField: r0 = r2->field_f
    //     0xb1f0fc: ldur            w0, [x2, #0xf]
    // 0xb1f100: DecompressPointer r0
    //     0xb1f100: add             x0, x0, HEAP, lsl #32
    // 0xb1f104: LoadField: r1 = r2->field_13
    //     0xb1f104: ldur            w1, [x2, #0x13]
    // 0xb1f108: DecompressPointer r1
    //     0xb1f108: add             x1, x1, HEAP, lsl #32
    // 0xb1f10c: stp             x1, x0, [SP]
    // 0xb1f110: r4 = 0
    //     0xb1f110: movz            x4, #0
    // 0xb1f114: ldr             x0, [SP, #8]
    // 0xb1f118: r5 = UnlinkedCall_0x613b5c
    //     0xb1f118: add             x16, PP, #0x55, lsl #12  ; [pp+0x550e0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1f11c: ldp             x5, lr, [x16, #0xe0]
    // 0xb1f120: blr             lr
    // 0xb1f124: cmp             w0, NULL
    // 0xb1f128: b.eq            #0xb1f1bc
    // 0xb1f12c: LoadField: r1 = r0->field_c7
    //     0xb1f12c: ldur            w1, [x0, #0xc7]
    // 0xb1f130: DecompressPointer r1
    //     0xb1f130: add             x1, x1, HEAP, lsl #32
    // 0xb1f134: cmp             w1, NULL
    // 0xb1f138: b.eq            #0xb1f1bc
    // 0xb1f13c: ldur            x2, [fp, #-0x20]
    // 0xb1f140: LoadField: r0 = r2->field_f
    //     0xb1f140: ldur            w0, [x2, #0xf]
    // 0xb1f144: DecompressPointer r0
    //     0xb1f144: add             x0, x0, HEAP, lsl #32
    // 0xb1f148: LoadField: r1 = r2->field_13
    //     0xb1f148: ldur            w1, [x2, #0x13]
    // 0xb1f14c: DecompressPointer r1
    //     0xb1f14c: add             x1, x1, HEAP, lsl #32
    // 0xb1f150: stp             x1, x0, [SP]
    // 0xb1f154: r4 = 0
    //     0xb1f154: movz            x4, #0
    // 0xb1f158: ldr             x0, [SP, #8]
    // 0xb1f15c: r5 = UnlinkedCall_0x613b5c
    //     0xb1f15c: add             x16, PP, #0x55, lsl #12  ; [pp+0x550f0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1f160: ldp             x5, lr, [x16, #0xf0]
    // 0xb1f164: blr             lr
    // 0xb1f168: cmp             w0, NULL
    // 0xb1f16c: b.ne            #0xb1f178
    // 0xb1f170: r0 = Null
    //     0xb1f170: mov             x0, NULL
    // 0xb1f174: b               #0xb1f1a8
    // 0xb1f178: LoadField: r1 = r0->field_c7
    //     0xb1f178: ldur            w1, [x0, #0xc7]
    // 0xb1f17c: DecompressPointer r1
    //     0xb1f17c: add             x1, x1, HEAP, lsl #32
    // 0xb1f180: cmp             w1, NULL
    // 0xb1f184: b.ne            #0xb1f190
    // 0xb1f188: r0 = Null
    //     0xb1f188: mov             x0, NULL
    // 0xb1f18c: b               #0xb1f1a8
    // 0xb1f190: LoadField: r0 = r1->field_7
    //     0xb1f190: ldur            w0, [x1, #7]
    // 0xb1f194: cbnz            w0, #0xb1f1a0
    // 0xb1f198: r1 = false
    //     0xb1f198: add             x1, NULL, #0x30  ; false
    // 0xb1f19c: b               #0xb1f1a4
    // 0xb1f1a0: r1 = true
    //     0xb1f1a0: add             x1, NULL, #0x20  ; true
    // 0xb1f1a4: mov             x0, x1
    // 0xb1f1a8: cmp             w0, NULL
    // 0xb1f1ac: b.ne            #0xb1f1b4
    // 0xb1f1b0: r0 = false
    //     0xb1f1b0: add             x0, NULL, #0x30  ; false
    // 0xb1f1b4: mov             x3, x0
    // 0xb1f1b8: b               #0xb1f1c0
    // 0xb1f1bc: r3 = false
    //     0xb1f1bc: add             x3, NULL, #0x30  ; false
    // 0xb1f1c0: ldur            x0, [fp, #-8]
    // 0xb1f1c4: ldur            x2, [fp, #-0x20]
    // 0xb1f1c8: stur            x3, [fp, #-0x40]
    // 0xb1f1cc: LoadField: r1 = r0->field_f
    //     0xb1f1cc: ldur            w1, [x0, #0xf]
    // 0xb1f1d0: DecompressPointer r1
    //     0xb1f1d0: add             x1, x1, HEAP, lsl #32
    // 0xb1f1d4: cmp             w1, NULL
    // 0xb1f1d8: b.eq            #0xb1fdc4
    // 0xb1f1dc: r0 = of()
    //     0xb1f1dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1f1e0: LoadField: r1 = r0->field_5b
    //     0xb1f1e0: ldur            w1, [x0, #0x5b]
    // 0xb1f1e4: DecompressPointer r1
    //     0xb1f1e4: add             x1, x1, HEAP, lsl #32
    // 0xb1f1e8: stur            x1, [fp, #-0x48]
    // 0xb1f1ec: r0 = BoxDecoration()
    //     0xb1f1ec: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb1f1f0: mov             x1, x0
    // 0xb1f1f4: ldur            x0, [fp, #-0x48]
    // 0xb1f1f8: stur            x1, [fp, #-0x50]
    // 0xb1f1fc: StoreField: r1->field_7 = r0
    //     0xb1f1fc: stur            w0, [x1, #7]
    // 0xb1f200: r0 = Instance_BoxShape
    //     0xb1f200: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb1f204: ldr             x0, [x0, #0x970]
    // 0xb1f208: StoreField: r1->field_23 = r0
    //     0xb1f208: stur            w0, [x1, #0x23]
    // 0xb1f20c: r0 = Container()
    //     0xb1f20c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1f210: stur            x0, [fp, #-0x48]
    // 0xb1f214: r16 = Instance_EdgeInsets
    //     0xb1f214: add             x16, PP, #0x52, lsl #12  ; [pp+0x52108] Obj!EdgeInsets@d579b1
    //     0xb1f218: ldr             x16, [x16, #0x108]
    // 0xb1f21c: r30 = 5.000000
    //     0xb1f21c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb1f220: ldr             lr, [lr, #0xcf0]
    // 0xb1f224: stp             lr, x16, [SP, #0x10]
    // 0xb1f228: r16 = 5.000000
    //     0xb1f228: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb1f22c: ldr             x16, [x16, #0xcf0]
    // 0xb1f230: ldur            lr, [fp, #-0x50]
    // 0xb1f234: stp             lr, x16, [SP]
    // 0xb1f238: mov             x1, x0
    // 0xb1f23c: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x3, margin, 0x1, width, 0x2, null]
    //     0xb1f23c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52118] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xb1f240: ldr             x4, [x4, #0x118]
    // 0xb1f244: r0 = Container()
    //     0xb1f244: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1f248: r0 = Visibility()
    //     0xb1f248: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb1f24c: mov             x1, x0
    // 0xb1f250: ldur            x0, [fp, #-0x48]
    // 0xb1f254: StoreField: r1->field_b = r0
    //     0xb1f254: stur            w0, [x1, #0xb]
    // 0xb1f258: r0 = Instance_SizedBox
    //     0xb1f258: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb1f25c: StoreField: r1->field_f = r0
    //     0xb1f25c: stur            w0, [x1, #0xf]
    // 0xb1f260: ldur            x0, [fp, #-0x40]
    // 0xb1f264: StoreField: r1->field_13 = r0
    //     0xb1f264: stur            w0, [x1, #0x13]
    // 0xb1f268: r2 = false
    //     0xb1f268: add             x2, NULL, #0x30  ; false
    // 0xb1f26c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb1f26c: stur            w2, [x1, #0x17]
    // 0xb1f270: StoreField: r1->field_1b = r2
    //     0xb1f270: stur            w2, [x1, #0x1b]
    // 0xb1f274: StoreField: r1->field_1f = r2
    //     0xb1f274: stur            w2, [x1, #0x1f]
    // 0xb1f278: StoreField: r1->field_23 = r2
    //     0xb1f278: stur            w2, [x1, #0x23]
    // 0xb1f27c: StoreField: r1->field_27 = r2
    //     0xb1f27c: stur            w2, [x1, #0x27]
    // 0xb1f280: StoreField: r1->field_2b = r2
    //     0xb1f280: stur            w2, [x1, #0x2b]
    // 0xb1f284: mov             x0, x1
    // 0xb1f288: ldur            x1, [fp, #-0x38]
    // 0xb1f28c: ArrayStore: r1[4] = r0  ; List_4
    //     0xb1f28c: add             x25, x1, #0x1f
    //     0xb1f290: str             w0, [x25]
    //     0xb1f294: tbz             w0, #0, #0xb1f2b0
    //     0xb1f298: ldurb           w16, [x1, #-1]
    //     0xb1f29c: ldurb           w17, [x0, #-1]
    //     0xb1f2a0: and             x16, x17, x16, lsr #2
    //     0xb1f2a4: tst             x16, HEAP, lsr #32
    //     0xb1f2a8: b.eq            #0xb1f2b0
    //     0xb1f2ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1f2b0: ldur            x0, [fp, #-0x20]
    // 0xb1f2b4: LoadField: r1 = r0->field_f
    //     0xb1f2b4: ldur            w1, [x0, #0xf]
    // 0xb1f2b8: DecompressPointer r1
    //     0xb1f2b8: add             x1, x1, HEAP, lsl #32
    // 0xb1f2bc: LoadField: r3 = r0->field_13
    //     0xb1f2bc: ldur            w3, [x0, #0x13]
    // 0xb1f2c0: DecompressPointer r3
    //     0xb1f2c0: add             x3, x3, HEAP, lsl #32
    // 0xb1f2c4: stp             x3, x1, [SP]
    // 0xb1f2c8: r4 = 0
    //     0xb1f2c8: movz            x4, #0
    // 0xb1f2cc: ldr             x0, [SP, #8]
    // 0xb1f2d0: r5 = UnlinkedCall_0x613b5c
    //     0xb1f2d0: add             x16, PP, #0x55, lsl #12  ; [pp+0x55100] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1f2d4: ldp             x5, lr, [x16, #0x100]
    // 0xb1f2d8: blr             lr
    // 0xb1f2dc: cmp             w0, NULL
    // 0xb1f2e0: b.ne            #0xb1f2ec
    // 0xb1f2e4: r0 = Null
    //     0xb1f2e4: mov             x0, NULL
    // 0xb1f2e8: b               #0xb1f2f8
    // 0xb1f2ec: LoadField: r1 = r0->field_c7
    //     0xb1f2ec: ldur            w1, [x0, #0xc7]
    // 0xb1f2f0: DecompressPointer r1
    //     0xb1f2f0: add             x1, x1, HEAP, lsl #32
    // 0xb1f2f4: mov             x0, x1
    // 0xb1f2f8: cmp             w0, NULL
    // 0xb1f2fc: b.ne            #0xb1f308
    // 0xb1f300: r3 = ""
    //     0xb1f300: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb1f304: b               #0xb1f30c
    // 0xb1f308: mov             x3, x0
    // 0xb1f30c: ldur            x0, [fp, #-8]
    // 0xb1f310: ldur            x2, [fp, #-0x20]
    // 0xb1f314: stur            x3, [fp, #-0x40]
    // 0xb1f318: LoadField: r1 = r0->field_f
    //     0xb1f318: ldur            w1, [x0, #0xf]
    // 0xb1f31c: DecompressPointer r1
    //     0xb1f31c: add             x1, x1, HEAP, lsl #32
    // 0xb1f320: cmp             w1, NULL
    // 0xb1f324: b.eq            #0xb1fdc8
    // 0xb1f328: r0 = of()
    //     0xb1f328: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1f32c: LoadField: r1 = r0->field_87
    //     0xb1f32c: ldur            w1, [x0, #0x87]
    // 0xb1f330: DecompressPointer r1
    //     0xb1f330: add             x1, x1, HEAP, lsl #32
    // 0xb1f334: LoadField: r0 = r1->field_2b
    //     0xb1f334: ldur            w0, [x1, #0x2b]
    // 0xb1f338: DecompressPointer r0
    //     0xb1f338: add             x0, x0, HEAP, lsl #32
    // 0xb1f33c: stur            x0, [fp, #-0x48]
    // 0xb1f340: r1 = Instance_Color
    //     0xb1f340: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1f344: d0 = 0.700000
    //     0xb1f344: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb1f348: ldr             d0, [x17, #0xf48]
    // 0xb1f34c: r0 = withOpacity()
    //     0xb1f34c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb1f350: r16 = 14.000000
    //     0xb1f350: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb1f354: ldr             x16, [x16, #0x1d8]
    // 0xb1f358: stp             x0, x16, [SP]
    // 0xb1f35c: ldur            x1, [fp, #-0x48]
    // 0xb1f360: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1f360: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1f364: ldr             x4, [x4, #0xaa0]
    // 0xb1f368: r0 = copyWith()
    //     0xb1f368: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1f36c: stur            x0, [fp, #-0x48]
    // 0xb1f370: r0 = Text()
    //     0xb1f370: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1f374: mov             x1, x0
    // 0xb1f378: ldur            x0, [fp, #-0x40]
    // 0xb1f37c: StoreField: r1->field_b = r0
    //     0xb1f37c: stur            w0, [x1, #0xb]
    // 0xb1f380: ldur            x0, [fp, #-0x48]
    // 0xb1f384: StoreField: r1->field_13 = r0
    //     0xb1f384: stur            w0, [x1, #0x13]
    // 0xb1f388: mov             x0, x1
    // 0xb1f38c: ldur            x1, [fp, #-0x38]
    // 0xb1f390: ArrayStore: r1[5] = r0  ; List_4
    //     0xb1f390: add             x25, x1, #0x23
    //     0xb1f394: str             w0, [x25]
    //     0xb1f398: tbz             w0, #0, #0xb1f3b4
    //     0xb1f39c: ldurb           w16, [x1, #-1]
    //     0xb1f3a0: ldurb           w17, [x0, #-1]
    //     0xb1f3a4: and             x16, x17, x16, lsr #2
    //     0xb1f3a8: tst             x16, HEAP, lsr #32
    //     0xb1f3ac: b.eq            #0xb1f3b4
    //     0xb1f3b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1f3b4: r0 = Container()
    //     0xb1f3b4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1f3b8: stur            x0, [fp, #-0x40]
    // 0xb1f3bc: r16 = 5.000000
    //     0xb1f3bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb1f3c0: ldr             x16, [x16, #0xcf0]
    // 0xb1f3c4: str             x16, [SP]
    // 0xb1f3c8: mov             x1, x0
    // 0xb1f3cc: r4 = const [0, 0x2, 0x1, 0x1, width, 0x1, null]
    //     0xb1f3cc: add             x4, PP, #0x52, lsl #12  ; [pp+0x520e0] List(7) [0, 0x2, 0x1, 0x1, "width", 0x1, Null]
    //     0xb1f3d0: ldr             x4, [x4, #0xe0]
    // 0xb1f3d4: r0 = Container()
    //     0xb1f3d4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1f3d8: ldur            x1, [fp, #-0x38]
    // 0xb1f3dc: ldur            x0, [fp, #-0x40]
    // 0xb1f3e0: ArrayStore: r1[6] = r0  ; List_4
    //     0xb1f3e0: add             x25, x1, #0x27
    //     0xb1f3e4: str             w0, [x25]
    //     0xb1f3e8: tbz             w0, #0, #0xb1f404
    //     0xb1f3ec: ldurb           w16, [x1, #-1]
    //     0xb1f3f0: ldurb           w17, [x0, #-1]
    //     0xb1f3f4: and             x16, x17, x16, lsr #2
    //     0xb1f3f8: tst             x16, HEAP, lsr #32
    //     0xb1f3fc: b.eq            #0xb1f404
    //     0xb1f400: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1f404: ldur            x0, [fp, #-8]
    // 0xb1f408: LoadField: r1 = r0->field_f
    //     0xb1f408: ldur            w1, [x0, #0xf]
    // 0xb1f40c: DecompressPointer r1
    //     0xb1f40c: add             x1, x1, HEAP, lsl #32
    // 0xb1f410: cmp             w1, NULL
    // 0xb1f414: b.eq            #0xb1fdcc
    // 0xb1f418: r0 = of()
    //     0xb1f418: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1f41c: LoadField: r1 = r0->field_5b
    //     0xb1f41c: ldur            w1, [x0, #0x5b]
    // 0xb1f420: DecompressPointer r1
    //     0xb1f420: add             x1, x1, HEAP, lsl #32
    // 0xb1f424: stur            x1, [fp, #-0x40]
    // 0xb1f428: r0 = BoxDecoration()
    //     0xb1f428: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb1f42c: mov             x1, x0
    // 0xb1f430: ldur            x0, [fp, #-0x40]
    // 0xb1f434: stur            x1, [fp, #-0x48]
    // 0xb1f438: StoreField: r1->field_7 = r0
    //     0xb1f438: stur            w0, [x1, #7]
    // 0xb1f43c: r0 = Instance_BoxShape
    //     0xb1f43c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb1f440: ldr             x0, [x0, #0x970]
    // 0xb1f444: StoreField: r1->field_23 = r0
    //     0xb1f444: stur            w0, [x1, #0x23]
    // 0xb1f448: r0 = Container()
    //     0xb1f448: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1f44c: stur            x0, [fp, #-0x40]
    // 0xb1f450: r16 = Instance_EdgeInsets
    //     0xb1f450: add             x16, PP, #0x52, lsl #12  ; [pp+0x52108] Obj!EdgeInsets@d579b1
    //     0xb1f454: ldr             x16, [x16, #0x108]
    // 0xb1f458: r30 = 5.000000
    //     0xb1f458: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb1f45c: ldr             lr, [lr, #0xcf0]
    // 0xb1f460: stp             lr, x16, [SP, #0x10]
    // 0xb1f464: r16 = 5.000000
    //     0xb1f464: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb1f468: ldr             x16, [x16, #0xcf0]
    // 0xb1f46c: ldur            lr, [fp, #-0x48]
    // 0xb1f470: stp             lr, x16, [SP]
    // 0xb1f474: mov             x1, x0
    // 0xb1f478: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x3, margin, 0x1, width, 0x2, null]
    //     0xb1f478: add             x4, PP, #0x52, lsl #12  ; [pp+0x52118] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xb1f47c: ldr             x4, [x4, #0x118]
    // 0xb1f480: r0 = Container()
    //     0xb1f480: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1f484: ldur            x1, [fp, #-0x38]
    // 0xb1f488: ldur            x0, [fp, #-0x40]
    // 0xb1f48c: ArrayStore: r1[7] = r0  ; List_4
    //     0xb1f48c: add             x25, x1, #0x2b
    //     0xb1f490: str             w0, [x25]
    //     0xb1f494: tbz             w0, #0, #0xb1f4b0
    //     0xb1f498: ldurb           w16, [x1, #-1]
    //     0xb1f49c: ldurb           w17, [x0, #-1]
    //     0xb1f4a0: and             x16, x17, x16, lsr #2
    //     0xb1f4a4: tst             x16, HEAP, lsr #32
    //     0xb1f4a8: b.eq            #0xb1f4b0
    //     0xb1f4ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1f4b0: ldur            x2, [fp, #-0x20]
    // 0xb1f4b4: LoadField: r0 = r2->field_f
    //     0xb1f4b4: ldur            w0, [x2, #0xf]
    // 0xb1f4b8: DecompressPointer r0
    //     0xb1f4b8: add             x0, x0, HEAP, lsl #32
    // 0xb1f4bc: LoadField: r1 = r2->field_13
    //     0xb1f4bc: ldur            w1, [x2, #0x13]
    // 0xb1f4c0: DecompressPointer r1
    //     0xb1f4c0: add             x1, x1, HEAP, lsl #32
    // 0xb1f4c4: stp             x1, x0, [SP]
    // 0xb1f4c8: r4 = 0
    //     0xb1f4c8: movz            x4, #0
    // 0xb1f4cc: ldr             x0, [SP, #8]
    // 0xb1f4d0: r5 = UnlinkedCall_0x613b5c
    //     0xb1f4d0: add             x16, PP, #0x55, lsl #12  ; [pp+0x55110] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1f4d4: ldp             x5, lr, [x16, #0x110]
    // 0xb1f4d8: blr             lr
    // 0xb1f4dc: cmp             w0, NULL
    // 0xb1f4e0: b.eq            #0xb1fdd0
    // 0xb1f4e4: LoadField: r1 = r0->field_c3
    //     0xb1f4e4: ldur            w1, [x0, #0xc3]
    // 0xb1f4e8: DecompressPointer r1
    //     0xb1f4e8: add             x1, x1, HEAP, lsl #32
    // 0xb1f4ec: cmp             w1, NULL
    // 0xb1f4f0: b.ne            #0xb1f4fc
    // 0xb1f4f4: r5 = ""
    //     0xb1f4f4: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb1f4f8: b               #0xb1f500
    // 0xb1f4fc: mov             x5, x1
    // 0xb1f500: ldur            x0, [fp, #-8]
    // 0xb1f504: ldur            x2, [fp, #-0x20]
    // 0xb1f508: ldur            x4, [fp, #-0x30]
    // 0xb1f50c: ldur            x3, [fp, #-0x38]
    // 0xb1f510: stur            x5, [fp, #-0x40]
    // 0xb1f514: LoadField: r1 = r0->field_f
    //     0xb1f514: ldur            w1, [x0, #0xf]
    // 0xb1f518: DecompressPointer r1
    //     0xb1f518: add             x1, x1, HEAP, lsl #32
    // 0xb1f51c: cmp             w1, NULL
    // 0xb1f520: b.eq            #0xb1fdd4
    // 0xb1f524: r0 = of()
    //     0xb1f524: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1f528: LoadField: r1 = r0->field_87
    //     0xb1f528: ldur            w1, [x0, #0x87]
    // 0xb1f52c: DecompressPointer r1
    //     0xb1f52c: add             x1, x1, HEAP, lsl #32
    // 0xb1f530: LoadField: r0 = r1->field_2b
    //     0xb1f530: ldur            w0, [x1, #0x2b]
    // 0xb1f534: DecompressPointer r0
    //     0xb1f534: add             x0, x0, HEAP, lsl #32
    // 0xb1f538: stur            x0, [fp, #-0x48]
    // 0xb1f53c: r1 = Instance_Color
    //     0xb1f53c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1f540: d0 = 0.700000
    //     0xb1f540: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb1f544: ldr             d0, [x17, #0xf48]
    // 0xb1f548: r0 = withOpacity()
    //     0xb1f548: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb1f54c: r16 = 14.000000
    //     0xb1f54c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb1f550: ldr             x16, [x16, #0x1d8]
    // 0xb1f554: stp             x0, x16, [SP]
    // 0xb1f558: ldur            x1, [fp, #-0x48]
    // 0xb1f55c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1f55c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1f560: ldr             x4, [x4, #0xaa0]
    // 0xb1f564: r0 = copyWith()
    //     0xb1f564: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1f568: stur            x0, [fp, #-0x48]
    // 0xb1f56c: r0 = Text()
    //     0xb1f56c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1f570: mov             x1, x0
    // 0xb1f574: ldur            x0, [fp, #-0x40]
    // 0xb1f578: StoreField: r1->field_b = r0
    //     0xb1f578: stur            w0, [x1, #0xb]
    // 0xb1f57c: ldur            x0, [fp, #-0x48]
    // 0xb1f580: StoreField: r1->field_13 = r0
    //     0xb1f580: stur            w0, [x1, #0x13]
    // 0xb1f584: mov             x0, x1
    // 0xb1f588: ldur            x1, [fp, #-0x38]
    // 0xb1f58c: ArrayStore: r1[8] = r0  ; List_4
    //     0xb1f58c: add             x25, x1, #0x2f
    //     0xb1f590: str             w0, [x25]
    //     0xb1f594: tbz             w0, #0, #0xb1f5b0
    //     0xb1f598: ldurb           w16, [x1, #-1]
    //     0xb1f59c: ldurb           w17, [x0, #-1]
    //     0xb1f5a0: and             x16, x17, x16, lsr #2
    //     0xb1f5a4: tst             x16, HEAP, lsr #32
    //     0xb1f5a8: b.eq            #0xb1f5b0
    //     0xb1f5ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1f5b0: r1 = <Widget>
    //     0xb1f5b0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1f5b4: r0 = AllocateGrowableArray()
    //     0xb1f5b4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb1f5b8: mov             x1, x0
    // 0xb1f5bc: ldur            x0, [fp, #-0x38]
    // 0xb1f5c0: stur            x1, [fp, #-0x40]
    // 0xb1f5c4: StoreField: r1->field_f = r0
    //     0xb1f5c4: stur            w0, [x1, #0xf]
    // 0xb1f5c8: r0 = 18
    //     0xb1f5c8: movz            x0, #0x12
    // 0xb1f5cc: StoreField: r1->field_b = r0
    //     0xb1f5cc: stur            w0, [x1, #0xb]
    // 0xb1f5d0: r0 = Row()
    //     0xb1f5d0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb1f5d4: r2 = Instance_Axis
    //     0xb1f5d4: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb1f5d8: StoreField: r0->field_f = r2
    //     0xb1f5d8: stur            w2, [x0, #0xf]
    // 0xb1f5dc: r3 = Instance_MainAxisAlignment
    //     0xb1f5dc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb1f5e0: ldr             x3, [x3, #0xa08]
    // 0xb1f5e4: StoreField: r0->field_13 = r3
    //     0xb1f5e4: stur            w3, [x0, #0x13]
    // 0xb1f5e8: r1 = Instance_MainAxisSize
    //     0xb1f5e8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb1f5ec: ldr             x1, [x1, #0xa10]
    // 0xb1f5f0: ArrayStore: r0[0] = r1  ; List_4
    //     0xb1f5f0: stur            w1, [x0, #0x17]
    // 0xb1f5f4: r1 = Instance_CrossAxisAlignment
    //     0xb1f5f4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb1f5f8: ldr             x1, [x1, #0xa18]
    // 0xb1f5fc: StoreField: r0->field_1b = r1
    //     0xb1f5fc: stur            w1, [x0, #0x1b]
    // 0xb1f600: r4 = Instance_VerticalDirection
    //     0xb1f600: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1f604: ldr             x4, [x4, #0xa20]
    // 0xb1f608: StoreField: r0->field_23 = r4
    //     0xb1f608: stur            w4, [x0, #0x23]
    // 0xb1f60c: r5 = Instance_Clip
    //     0xb1f60c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1f610: ldr             x5, [x5, #0x38]
    // 0xb1f614: StoreField: r0->field_2b = r5
    //     0xb1f614: stur            w5, [x0, #0x2b]
    // 0xb1f618: StoreField: r0->field_2f = rZR
    //     0xb1f618: stur            xzr, [x0, #0x2f]
    // 0xb1f61c: ldur            x1, [fp, #-0x40]
    // 0xb1f620: StoreField: r0->field_b = r1
    //     0xb1f620: stur            w1, [x0, #0xb]
    // 0xb1f624: ldur            x1, [fp, #-0x30]
    // 0xb1f628: ArrayStore: r1[2] = r0  ; List_4
    //     0xb1f628: add             x25, x1, #0x17
    //     0xb1f62c: str             w0, [x25]
    //     0xb1f630: tbz             w0, #0, #0xb1f64c
    //     0xb1f634: ldurb           w16, [x1, #-1]
    //     0xb1f638: ldurb           w17, [x0, #-1]
    //     0xb1f63c: and             x16, x17, x16, lsr #2
    //     0xb1f640: tst             x16, HEAP, lsr #32
    //     0xb1f644: b.eq            #0xb1f64c
    //     0xb1f648: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1f64c: ldur            x1, [fp, #-0x30]
    // 0xb1f650: r16 = Instance_SizedBox
    //     0xb1f650: add             x16, PP, #0x55, lsl #12  ; [pp+0x550d8] Obj!SizedBox@d68001
    //     0xb1f654: ldr             x16, [x16, #0xd8]
    // 0xb1f658: StoreField: r1->field_1b = r16
    //     0xb1f658: stur            w16, [x1, #0x1b]
    // 0xb1f65c: ldur            x0, [fp, #-0x20]
    // 0xb1f660: LoadField: r6 = r0->field_f
    //     0xb1f660: ldur            w6, [x0, #0xf]
    // 0xb1f664: DecompressPointer r6
    //     0xb1f664: add             x6, x6, HEAP, lsl #32
    // 0xb1f668: LoadField: r7 = r0->field_13
    //     0xb1f668: ldur            w7, [x0, #0x13]
    // 0xb1f66c: DecompressPointer r7
    //     0xb1f66c: add             x7, x7, HEAP, lsl #32
    // 0xb1f670: stp             x7, x6, [SP]
    // 0xb1f674: r4 = 0
    //     0xb1f674: movz            x4, #0
    // 0xb1f678: ldr             x0, [SP, #8]
    // 0xb1f67c: r5 = UnlinkedCall_0x613b5c
    //     0xb1f67c: add             x16, PP, #0x55, lsl #12  ; [pp+0x55120] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1f680: ldp             x5, lr, [x16, #0x120]
    // 0xb1f684: blr             lr
    // 0xb1f688: cmp             w0, NULL
    // 0xb1f68c: b.eq            #0xb1fdd8
    // 0xb1f690: LoadField: r3 = r0->field_bb
    //     0xb1f690: ldur            w3, [x0, #0xbb]
    // 0xb1f694: DecompressPointer r3
    //     0xb1f694: add             x3, x3, HEAP, lsl #32
    // 0xb1f698: mov             x0, x3
    // 0xb1f69c: stur            x3, [fp, #-0x38]
    // 0xb1f6a0: r2 = Null
    //     0xb1f6a0: mov             x2, NULL
    // 0xb1f6a4: r1 = Null
    //     0xb1f6a4: mov             x1, NULL
    // 0xb1f6a8: r4 = LoadClassIdInstr(r0)
    //     0xb1f6a8: ldur            x4, [x0, #-1]
    //     0xb1f6ac: ubfx            x4, x4, #0xc, #0x14
    // 0xb1f6b0: sub             x4, x4, #0x5e
    // 0xb1f6b4: cmp             x4, #1
    // 0xb1f6b8: b.ls            #0xb1f6cc
    // 0xb1f6bc: r8 = String
    //     0xb1f6bc: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb1f6c0: r3 = Null
    //     0xb1f6c0: add             x3, PP, #0x55, lsl #12  ; [pp+0x55130] Null
    //     0xb1f6c4: ldr             x3, [x3, #0x130]
    // 0xb1f6c8: r0 = String()
    //     0xb1f6c8: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb1f6cc: ldur            x1, [fp, #-0x38]
    // 0xb1f6d0: r0 = parse()
    //     0xb1f6d0: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb1f6d4: ldur            x2, [fp, #-0x20]
    // 0xb1f6d8: stur            d0, [fp, #-0x58]
    // 0xb1f6dc: LoadField: r0 = r2->field_f
    //     0xb1f6dc: ldur            w0, [x2, #0xf]
    // 0xb1f6e0: DecompressPointer r0
    //     0xb1f6e0: add             x0, x0, HEAP, lsl #32
    // 0xb1f6e4: LoadField: r1 = r2->field_13
    //     0xb1f6e4: ldur            w1, [x2, #0x13]
    // 0xb1f6e8: DecompressPointer r1
    //     0xb1f6e8: add             x1, x1, HEAP, lsl #32
    // 0xb1f6ec: stp             x1, x0, [SP]
    // 0xb1f6f0: r4 = 0
    //     0xb1f6f0: movz            x4, #0
    // 0xb1f6f4: ldr             x0, [SP, #8]
    // 0xb1f6f8: r5 = UnlinkedCall_0x613b5c
    //     0xb1f6f8: add             x16, PP, #0x55, lsl #12  ; [pp+0x55140] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1f6fc: ldp             x5, lr, [x16, #0x140]
    // 0xb1f700: blr             lr
    // 0xb1f704: cmp             w0, NULL
    // 0xb1f708: b.eq            #0xb1fddc
    // 0xb1f70c: LoadField: r3 = r0->field_bb
    //     0xb1f70c: ldur            w3, [x0, #0xbb]
    // 0xb1f710: DecompressPointer r3
    //     0xb1f710: add             x3, x3, HEAP, lsl #32
    // 0xb1f714: mov             x0, x3
    // 0xb1f718: stur            x3, [fp, #-0x38]
    // 0xb1f71c: r2 = Null
    //     0xb1f71c: mov             x2, NULL
    // 0xb1f720: r1 = Null
    //     0xb1f720: mov             x1, NULL
    // 0xb1f724: r4 = LoadClassIdInstr(r0)
    //     0xb1f724: ldur            x4, [x0, #-1]
    //     0xb1f728: ubfx            x4, x4, #0xc, #0x14
    // 0xb1f72c: sub             x4, x4, #0x5e
    // 0xb1f730: cmp             x4, #1
    // 0xb1f734: b.ls            #0xb1f748
    // 0xb1f738: r8 = String
    //     0xb1f738: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb1f73c: r3 = Null
    //     0xb1f73c: add             x3, PP, #0x55, lsl #12  ; [pp+0x55150] Null
    //     0xb1f740: ldr             x3, [x3, #0x150]
    // 0xb1f744: r0 = String()
    //     0xb1f744: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb1f748: ldur            x1, [fp, #-0x38]
    // 0xb1f74c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb1f74c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb1f750: r0 = parse()
    //     0xb1f750: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xb1f754: stur            x0, [fp, #-0x18]
    // 0xb1f758: r0 = RatingWidget()
    //     0xb1f758: bl              #0x9b101c  ; AllocateRatingWidgetStub -> RatingWidget (size=0x14)
    // 0xb1f75c: mov             x3, x0
    // 0xb1f760: r0 = Instance_Icon
    //     0xb1f760: add             x0, PP, #0x52, lsl #12  ; [pp+0x52190] Obj!Icon@d65fb1
    //     0xb1f764: ldr             x0, [x0, #0x190]
    // 0xb1f768: stur            x3, [fp, #-0x38]
    // 0xb1f76c: StoreField: r3->field_7 = r0
    //     0xb1f76c: stur            w0, [x3, #7]
    // 0xb1f770: r0 = Instance_Icon
    //     0xb1f770: add             x0, PP, #0x52, lsl #12  ; [pp+0x52198] Obj!Icon@d65f71
    //     0xb1f774: ldr             x0, [x0, #0x198]
    // 0xb1f778: StoreField: r3->field_b = r0
    //     0xb1f778: stur            w0, [x3, #0xb]
    // 0xb1f77c: r0 = Instance_Icon
    //     0xb1f77c: add             x0, PP, #0x52, lsl #12  ; [pp+0x521a0] Obj!Icon@d65f31
    //     0xb1f780: ldr             x0, [x0, #0x1a0]
    // 0xb1f784: StoreField: r3->field_f = r0
    //     0xb1f784: stur            w0, [x3, #0xf]
    // 0xb1f788: r1 = Function '<anonymous closure>':.
    //     0xb1f788: add             x1, PP, #0x55, lsl #12  ; [pp+0x55160] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb1f78c: ldr             x1, [x1, #0x160]
    // 0xb1f790: r2 = Null
    //     0xb1f790: mov             x2, NULL
    // 0xb1f794: r0 = AllocateClosure()
    //     0xb1f794: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1f798: stur            x0, [fp, #-0x40]
    // 0xb1f79c: r0 = RatingBar()
    //     0xb1f79c: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xb1f7a0: mov             x1, x0
    // 0xb1f7a4: ldur            x0, [fp, #-0x40]
    // 0xb1f7a8: StoreField: r1->field_b = r0
    //     0xb1f7a8: stur            w0, [x1, #0xb]
    // 0xb1f7ac: r2 = true
    //     0xb1f7ac: add             x2, NULL, #0x20  ; true
    // 0xb1f7b0: StoreField: r1->field_1f = r2
    //     0xb1f7b0: stur            w2, [x1, #0x1f]
    // 0xb1f7b4: r0 = Instance_Axis
    //     0xb1f7b4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb1f7b8: StoreField: r1->field_23 = r0
    //     0xb1f7b8: stur            w0, [x1, #0x23]
    // 0xb1f7bc: StoreField: r1->field_27 = r2
    //     0xb1f7bc: stur            w2, [x1, #0x27]
    // 0xb1f7c0: d0 = 2.000000
    //     0xb1f7c0: fmov            d0, #2.00000000
    // 0xb1f7c4: StoreField: r1->field_2b = d0
    //     0xb1f7c4: stur            d0, [x1, #0x2b]
    // 0xb1f7c8: StoreField: r1->field_33 = r2
    //     0xb1f7c8: stur            w2, [x1, #0x33]
    // 0xb1f7cc: ldur            d0, [fp, #-0x58]
    // 0xb1f7d0: StoreField: r1->field_37 = d0
    //     0xb1f7d0: stur            d0, [x1, #0x37]
    // 0xb1f7d4: ldur            x0, [fp, #-0x18]
    // 0xb1f7d8: StoreField: r1->field_3f = r0
    //     0xb1f7d8: stur            x0, [x1, #0x3f]
    // 0xb1f7dc: r0 = Instance_EdgeInsets
    //     0xb1f7dc: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb1f7e0: StoreField: r1->field_47 = r0
    //     0xb1f7e0: stur            w0, [x1, #0x47]
    // 0xb1f7e4: d0 = 18.000000
    //     0xb1f7e4: fmov            d0, #18.00000000
    // 0xb1f7e8: StoreField: r1->field_4b = d0
    //     0xb1f7e8: stur            d0, [x1, #0x4b]
    // 0xb1f7ec: StoreField: r1->field_53 = rZR
    //     0xb1f7ec: stur            xzr, [x1, #0x53]
    // 0xb1f7f0: r0 = false
    //     0xb1f7f0: add             x0, NULL, #0x30  ; false
    // 0xb1f7f4: StoreField: r1->field_5b = r0
    //     0xb1f7f4: stur            w0, [x1, #0x5b]
    // 0xb1f7f8: StoreField: r1->field_5f = r0
    //     0xb1f7f8: stur            w0, [x1, #0x5f]
    // 0xb1f7fc: ldur            x0, [fp, #-0x38]
    // 0xb1f800: StoreField: r1->field_67 = r0
    //     0xb1f800: stur            w0, [x1, #0x67]
    // 0xb1f804: mov             x0, x1
    // 0xb1f808: ldur            x1, [fp, #-0x30]
    // 0xb1f80c: ArrayStore: r1[4] = r0  ; List_4
    //     0xb1f80c: add             x25, x1, #0x1f
    //     0xb1f810: str             w0, [x25]
    //     0xb1f814: tbz             w0, #0, #0xb1f830
    //     0xb1f818: ldurb           w16, [x1, #-1]
    //     0xb1f81c: ldurb           w17, [x0, #-1]
    //     0xb1f820: and             x16, x17, x16, lsr #2
    //     0xb1f824: tst             x16, HEAP, lsr #32
    //     0xb1f828: b.eq            #0xb1f830
    //     0xb1f82c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1f830: ldur            x1, [fp, #-0x30]
    // 0xb1f834: r16 = Instance_SizedBox
    //     0xb1f834: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xb1f838: ldr             x16, [x16, #0x9f0]
    // 0xb1f83c: StoreField: r1->field_23 = r16
    //     0xb1f83c: stur            w16, [x1, #0x23]
    // 0xb1f840: ldur            x0, [fp, #-0x20]
    // 0xb1f844: LoadField: r3 = r0->field_f
    //     0xb1f844: ldur            w3, [x0, #0xf]
    // 0xb1f848: DecompressPointer r3
    //     0xb1f848: add             x3, x3, HEAP, lsl #32
    // 0xb1f84c: LoadField: r4 = r0->field_13
    //     0xb1f84c: ldur            w4, [x0, #0x13]
    // 0xb1f850: DecompressPointer r4
    //     0xb1f850: add             x4, x4, HEAP, lsl #32
    // 0xb1f854: stp             x4, x3, [SP]
    // 0xb1f858: r4 = 0
    //     0xb1f858: movz            x4, #0
    // 0xb1f85c: ldr             x0, [SP, #8]
    // 0xb1f860: r5 = UnlinkedCall_0x613b5c
    //     0xb1f860: add             x16, PP, #0x55, lsl #12  ; [pp+0x55168] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1f864: ldp             x5, lr, [x16, #0x168]
    // 0xb1f868: blr             lr
    // 0xb1f86c: cmp             w0, NULL
    // 0xb1f870: b.eq            #0xb1fde0
    // 0xb1f874: LoadField: r1 = r0->field_bf
    //     0xb1f874: ldur            w1, [x0, #0xbf]
    // 0xb1f878: DecompressPointer r1
    //     0xb1f878: add             x1, x1, HEAP, lsl #32
    // 0xb1f87c: cmp             w1, NULL
    // 0xb1f880: b.eq            #0xb1fde4
    // 0xb1f884: r0 = trim()
    //     0xb1f884: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb1f888: mov             x2, x0
    // 0xb1f88c: ldur            x0, [fp, #-8]
    // 0xb1f890: stur            x2, [fp, #-0x38]
    // 0xb1f894: LoadField: r1 = r0->field_f
    //     0xb1f894: ldur            w1, [x0, #0xf]
    // 0xb1f898: DecompressPointer r1
    //     0xb1f898: add             x1, x1, HEAP, lsl #32
    // 0xb1f89c: cmp             w1, NULL
    // 0xb1f8a0: b.eq            #0xb1fde8
    // 0xb1f8a4: r0 = of()
    //     0xb1f8a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1f8a8: LoadField: r1 = r0->field_87
    //     0xb1f8a8: ldur            w1, [x0, #0x87]
    // 0xb1f8ac: DecompressPointer r1
    //     0xb1f8ac: add             x1, x1, HEAP, lsl #32
    // 0xb1f8b0: LoadField: r0 = r1->field_2b
    //     0xb1f8b0: ldur            w0, [x1, #0x2b]
    // 0xb1f8b4: DecompressPointer r0
    //     0xb1f8b4: add             x0, x0, HEAP, lsl #32
    // 0xb1f8b8: LoadField: r1 = r0->field_13
    //     0xb1f8b8: ldur            w1, [x0, #0x13]
    // 0xb1f8bc: DecompressPointer r1
    //     0xb1f8bc: add             x1, x1, HEAP, lsl #32
    // 0xb1f8c0: r16 = Instance_Color
    //     0xb1f8c0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1f8c4: stp             x16, x1, [SP]
    // 0xb1f8c8: r1 = Instance_TextStyle
    //     0xb1f8c8: add             x1, PP, #0x52, lsl #12  ; [pp+0x521c8] Obj!TextStyle@d62951
    //     0xb1f8cc: ldr             x1, [x1, #0x1c8]
    // 0xb1f8d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0xb1f8d0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0xb1f8d4: ldr             x4, [x4, #0x9b8]
    // 0xb1f8d8: r0 = copyWith()
    //     0xb1f8d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1f8dc: mov             x2, x0
    // 0xb1f8e0: ldur            x0, [fp, #-8]
    // 0xb1f8e4: stur            x2, [fp, #-0x40]
    // 0xb1f8e8: LoadField: r1 = r0->field_f
    //     0xb1f8e8: ldur            w1, [x0, #0xf]
    // 0xb1f8ec: DecompressPointer r1
    //     0xb1f8ec: add             x1, x1, HEAP, lsl #32
    // 0xb1f8f0: cmp             w1, NULL
    // 0xb1f8f4: b.eq            #0xb1fdec
    // 0xb1f8f8: r0 = of()
    //     0xb1f8f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1f8fc: LoadField: r1 = r0->field_87
    //     0xb1f8fc: ldur            w1, [x0, #0x87]
    // 0xb1f900: DecompressPointer r1
    //     0xb1f900: add             x1, x1, HEAP, lsl #32
    // 0xb1f904: LoadField: r0 = r1->field_7
    //     0xb1f904: ldur            w0, [x1, #7]
    // 0xb1f908: DecompressPointer r0
    //     0xb1f908: add             x0, x0, HEAP, lsl #32
    // 0xb1f90c: ldur            x2, [fp, #-8]
    // 0xb1f910: stur            x0, [fp, #-0x48]
    // 0xb1f914: LoadField: r1 = r2->field_f
    //     0xb1f914: ldur            w1, [x2, #0xf]
    // 0xb1f918: DecompressPointer r1
    //     0xb1f918: add             x1, x1, HEAP, lsl #32
    // 0xb1f91c: cmp             w1, NULL
    // 0xb1f920: b.eq            #0xb1fdf0
    // 0xb1f924: r0 = of()
    //     0xb1f924: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1f928: LoadField: r1 = r0->field_5b
    //     0xb1f928: ldur            w1, [x0, #0x5b]
    // 0xb1f92c: DecompressPointer r1
    //     0xb1f92c: add             x1, x1, HEAP, lsl #32
    // 0xb1f930: r16 = 12.000000
    //     0xb1f930: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb1f934: ldr             x16, [x16, #0x9e8]
    // 0xb1f938: stp             x1, x16, [SP, #8]
    // 0xb1f93c: r16 = Instance_TextDecoration
    //     0xb1f93c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb1f940: ldr             x16, [x16, #0x10]
    // 0xb1f944: str             x16, [SP]
    // 0xb1f948: ldur            x1, [fp, #-0x48]
    // 0xb1f94c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xb1f94c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xb1f950: ldr             x4, [x4, #0xe38]
    // 0xb1f954: r0 = copyWith()
    //     0xb1f954: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1f958: mov             x2, x0
    // 0xb1f95c: ldur            x0, [fp, #-8]
    // 0xb1f960: stur            x2, [fp, #-0x48]
    // 0xb1f964: LoadField: r1 = r0->field_f
    //     0xb1f964: ldur            w1, [x0, #0xf]
    // 0xb1f968: DecompressPointer r1
    //     0xb1f968: add             x1, x1, HEAP, lsl #32
    // 0xb1f96c: cmp             w1, NULL
    // 0xb1f970: b.eq            #0xb1fdf4
    // 0xb1f974: r0 = of()
    //     0xb1f974: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1f978: LoadField: r1 = r0->field_87
    //     0xb1f978: ldur            w1, [x0, #0x87]
    // 0xb1f97c: DecompressPointer r1
    //     0xb1f97c: add             x1, x1, HEAP, lsl #32
    // 0xb1f980: LoadField: r0 = r1->field_7
    //     0xb1f980: ldur            w0, [x1, #7]
    // 0xb1f984: DecompressPointer r0
    //     0xb1f984: add             x0, x0, HEAP, lsl #32
    // 0xb1f988: ldur            x1, [fp, #-8]
    // 0xb1f98c: stur            x0, [fp, #-0x50]
    // 0xb1f990: LoadField: r2 = r1->field_f
    //     0xb1f990: ldur            w2, [x1, #0xf]
    // 0xb1f994: DecompressPointer r2
    //     0xb1f994: add             x2, x2, HEAP, lsl #32
    // 0xb1f998: cmp             w2, NULL
    // 0xb1f99c: b.eq            #0xb1fdf8
    // 0xb1f9a0: mov             x1, x2
    // 0xb1f9a4: r0 = of()
    //     0xb1f9a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1f9a8: LoadField: r1 = r0->field_5b
    //     0xb1f9a8: ldur            w1, [x0, #0x5b]
    // 0xb1f9ac: DecompressPointer r1
    //     0xb1f9ac: add             x1, x1, HEAP, lsl #32
    // 0xb1f9b0: r16 = 12.000000
    //     0xb1f9b0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb1f9b4: ldr             x16, [x16, #0x9e8]
    // 0xb1f9b8: stp             x1, x16, [SP, #8]
    // 0xb1f9bc: r16 = Instance_TextDecoration
    //     0xb1f9bc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb1f9c0: ldr             x16, [x16, #0x10]
    // 0xb1f9c4: str             x16, [SP]
    // 0xb1f9c8: ldur            x1, [fp, #-0x50]
    // 0xb1f9cc: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xb1f9cc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xb1f9d0: ldr             x4, [x4, #0xe38]
    // 0xb1f9d4: r0 = copyWith()
    //     0xb1f9d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1f9d8: stur            x0, [fp, #-8]
    // 0xb1f9dc: r0 = ReadMoreText()
    //     0xb1f9dc: bl              #0x99f824  ; AllocateReadMoreTextStub -> ReadMoreText (size=0x6c)
    // 0xb1f9e0: mov             x1, x0
    // 0xb1f9e4: ldur            x0, [fp, #-0x38]
    // 0xb1f9e8: StoreField: r1->field_3f = r0
    //     0xb1f9e8: stur            w0, [x1, #0x3f]
    // 0xb1f9ec: r0 = "\n\nKnow Less"
    //     0xb1f9ec: add             x0, PP, #0x55, lsl #12  ; [pp+0x55178] "\n\nKnow Less"
    //     0xb1f9f0: ldr             x0, [x0, #0x178]
    // 0xb1f9f4: StoreField: r1->field_43 = r0
    //     0xb1f9f4: stur            w0, [x1, #0x43]
    // 0xb1f9f8: r0 = "\n\nKnow more"
    //     0xb1f9f8: add             x0, PP, #0x55, lsl #12  ; [pp+0x55180] "\n\nKnow more"
    //     0xb1f9fc: ldr             x0, [x0, #0x180]
    // 0xb1fa00: StoreField: r1->field_47 = r0
    //     0xb1fa00: stur            w0, [x1, #0x47]
    // 0xb1fa04: r0 = 240
    //     0xb1fa04: movz            x0, #0xf0
    // 0xb1fa08: StoreField: r1->field_f = r0
    //     0xb1fa08: stur            x0, [x1, #0xf]
    // 0xb1fa0c: r0 = 2
    //     0xb1fa0c: movz            x0, #0x2
    // 0xb1fa10: ArrayStore: r1[0] = r0  ; List_8
    //     0xb1fa10: stur            x0, [x1, #0x17]
    // 0xb1fa14: r0 = Instance_TrimMode
    //     0xb1fa14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] Obj!TrimMode@d70201
    //     0xb1fa18: ldr             x0, [x0, #0x9d0]
    // 0xb1fa1c: StoreField: r1->field_1f = r0
    //     0xb1fa1c: stur            w0, [x1, #0x1f]
    // 0xb1fa20: ldur            x0, [fp, #-0x40]
    // 0xb1fa24: StoreField: r1->field_4f = r0
    //     0xb1fa24: stur            w0, [x1, #0x4f]
    // 0xb1fa28: r0 = Instance_TextAlign
    //     0xb1fa28: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb1fa2c: StoreField: r1->field_53 = r0
    //     0xb1fa2c: stur            w0, [x1, #0x53]
    // 0xb1fa30: ldur            x0, [fp, #-0x48]
    // 0xb1fa34: StoreField: r1->field_23 = r0
    //     0xb1fa34: stur            w0, [x1, #0x23]
    // 0xb1fa38: ldur            x0, [fp, #-8]
    // 0xb1fa3c: StoreField: r1->field_27 = r0
    //     0xb1fa3c: stur            w0, [x1, #0x27]
    // 0xb1fa40: r0 = "… "
    //     0xb1fa40: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "… "
    //     0xb1fa44: ldr             x0, [x0, #0x9d8]
    // 0xb1fa48: StoreField: r1->field_3b = r0
    //     0xb1fa48: stur            w0, [x1, #0x3b]
    // 0xb1fa4c: r2 = true
    //     0xb1fa4c: add             x2, NULL, #0x20  ; true
    // 0xb1fa50: StoreField: r1->field_37 = r2
    //     0xb1fa50: stur            w2, [x1, #0x37]
    // 0xb1fa54: mov             x0, x1
    // 0xb1fa58: ldur            x1, [fp, #-0x30]
    // 0xb1fa5c: ArrayStore: r1[6] = r0  ; List_4
    //     0xb1fa5c: add             x25, x1, #0x27
    //     0xb1fa60: str             w0, [x25]
    //     0xb1fa64: tbz             w0, #0, #0xb1fa80
    //     0xb1fa68: ldurb           w16, [x1, #-1]
    //     0xb1fa6c: ldurb           w17, [x0, #-1]
    //     0xb1fa70: and             x16, x17, x16, lsr #2
    //     0xb1fa74: tst             x16, HEAP, lsr #32
    //     0xb1fa78: b.eq            #0xb1fa80
    //     0xb1fa7c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1fa80: ldur            x1, [fp, #-0x30]
    // 0xb1fa84: r16 = Instance_SizedBox
    //     0xb1fa84: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xb1fa88: ldr             x16, [x16, #0x9f0]
    // 0xb1fa8c: StoreField: r1->field_2b = r16
    //     0xb1fa8c: stur            w16, [x1, #0x2b]
    // 0xb1fa90: ldur            x0, [fp, #-0x20]
    // 0xb1fa94: LoadField: r3 = r0->field_f
    //     0xb1fa94: ldur            w3, [x0, #0xf]
    // 0xb1fa98: DecompressPointer r3
    //     0xb1fa98: add             x3, x3, HEAP, lsl #32
    // 0xb1fa9c: LoadField: r4 = r0->field_13
    //     0xb1fa9c: ldur            w4, [x0, #0x13]
    // 0xb1faa0: DecompressPointer r4
    //     0xb1faa0: add             x4, x4, HEAP, lsl #32
    // 0xb1faa4: stp             x4, x3, [SP]
    // 0xb1faa8: r4 = 0
    //     0xb1faa8: movz            x4, #0
    // 0xb1faac: ldr             x0, [SP, #8]
    // 0xb1fab0: r5 = UnlinkedCall_0x613b5c
    //     0xb1fab0: add             x16, PP, #0x55, lsl #12  ; [pp+0x55188] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1fab4: ldp             x5, lr, [x16, #0x188]
    // 0xb1fab8: blr             lr
    // 0xb1fabc: cmp             w0, NULL
    // 0xb1fac0: b.eq            #0xb1fdfc
    // 0xb1fac4: LoadField: r1 = r0->field_b7
    //     0xb1fac4: ldur            w1, [x0, #0xb7]
    // 0xb1fac8: DecompressPointer r1
    //     0xb1fac8: add             x1, x1, HEAP, lsl #32
    // 0xb1facc: cmp             w1, NULL
    // 0xb1fad0: b.eq            #0xb1fe00
    // 0xb1fad4: LoadField: r0 = r1->field_b
    //     0xb1fad4: ldur            w0, [x1, #0xb]
    // 0xb1fad8: r1 = LoadInt32Instr(r0)
    //     0xb1fad8: sbfx            x1, x0, #1, #0x1f
    // 0xb1fadc: cmp             x1, #3
    // 0xb1fae0: b.gt            #0xb1fbb8
    // 0xb1fae4: ldur            x2, [fp, #-0x20]
    // 0xb1fae8: LoadField: r0 = r2->field_f
    //     0xb1fae8: ldur            w0, [x2, #0xf]
    // 0xb1faec: DecompressPointer r0
    //     0xb1faec: add             x0, x0, HEAP, lsl #32
    // 0xb1faf0: LoadField: r1 = r2->field_13
    //     0xb1faf0: ldur            w1, [x2, #0x13]
    // 0xb1faf4: DecompressPointer r1
    //     0xb1faf4: add             x1, x1, HEAP, lsl #32
    // 0xb1faf8: stp             x1, x0, [SP]
    // 0xb1fafc: r4 = 0
    //     0xb1fafc: movz            x4, #0
    // 0xb1fb00: ldr             x0, [SP, #8]
    // 0xb1fb04: r5 = UnlinkedCall_0x613b5c
    //     0xb1fb04: add             x16, PP, #0x55, lsl #12  ; [pp+0x55198] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1fb08: ldp             x5, lr, [x16, #0x198]
    // 0xb1fb0c: blr             lr
    // 0xb1fb10: cmp             w0, NULL
    // 0xb1fb14: b.ne            #0xb1fb20
    // 0xb1fb18: r0 = Null
    //     0xb1fb18: mov             x0, NULL
    // 0xb1fb1c: b               #0xb1fb3c
    // 0xb1fb20: LoadField: r1 = r0->field_b7
    //     0xb1fb20: ldur            w1, [x0, #0xb7]
    // 0xb1fb24: DecompressPointer r1
    //     0xb1fb24: add             x1, x1, HEAP, lsl #32
    // 0xb1fb28: cmp             w1, NULL
    // 0xb1fb2c: b.ne            #0xb1fb38
    // 0xb1fb30: r0 = Null
    //     0xb1fb30: mov             x0, NULL
    // 0xb1fb34: b               #0xb1fb3c
    // 0xb1fb38: LoadField: r0 = r1->field_b
    //     0xb1fb38: ldur            w0, [x1, #0xb]
    // 0xb1fb3c: cmp             w0, NULL
    // 0xb1fb40: b.ne            #0xb1fb4c
    // 0xb1fb44: r0 = 0
    //     0xb1fb44: movz            x0, #0
    // 0xb1fb48: b               #0xb1fb54
    // 0xb1fb4c: r1 = LoadInt32Instr(r0)
    //     0xb1fb4c: sbfx            x1, x0, #1, #0x1f
    // 0xb1fb50: mov             x0, x1
    // 0xb1fb54: lsl             x3, x0, #1
    // 0xb1fb58: ldur            x2, [fp, #-0x20]
    // 0xb1fb5c: stur            x3, [fp, #-8]
    // 0xb1fb60: r1 = Function '<anonymous closure>':.
    //     0xb1fb60: add             x1, PP, #0x55, lsl #12  ; [pp+0x551a8] AnonymousClosure: (0xb205ac), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::glassThemeSlider (0xb1ee04)
    //     0xb1fb64: ldr             x1, [x1, #0x1a8]
    // 0xb1fb68: r0 = AllocateClosure()
    //     0xb1fb68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1fb6c: stur            x0, [fp, #-0x38]
    // 0xb1fb70: r0 = ListView()
    //     0xb1fb70: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb1fb74: stur            x0, [fp, #-0x40]
    // 0xb1fb78: r16 = true
    //     0xb1fb78: add             x16, NULL, #0x20  ; true
    // 0xb1fb7c: r30 = Instance_Axis
    //     0xb1fb7c: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb1fb80: stp             lr, x16, [SP, #0x10]
    // 0xb1fb84: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb1fb84: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb1fb88: ldr             x16, [x16, #0x1c8]
    // 0xb1fb8c: r30 = Instance_EdgeInsets
    //     0xb1fb8c: add             lr, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xb1fb90: ldr             lr, [lr, #0x550]
    // 0xb1fb94: stp             lr, x16, [SP]
    // 0xb1fb98: mov             x1, x0
    // 0xb1fb9c: ldur            x2, [fp, #-0x38]
    // 0xb1fba0: ldur            x3, [fp, #-8]
    // 0xb1fba4: r4 = const [0, 0x7, 0x4, 0x3, padding, 0x6, physics, 0x5, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xb1fba4: add             x4, PP, #0x55, lsl #12  ; [pp+0x551b0] List(13) [0, 0x7, 0x4, 0x3, "padding", 0x6, "physics", 0x5, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb1fba8: ldr             x4, [x4, #0x1b0]
    // 0xb1fbac: r0 = ListView.builder()
    //     0xb1fbac: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb1fbb0: ldur            x3, [fp, #-0x40]
    // 0xb1fbb4: b               #0xb1fc10
    // 0xb1fbb8: ldur            x2, [fp, #-0x20]
    // 0xb1fbbc: r1 = Function '<anonymous closure>':.
    //     0xb1fbbc: add             x1, PP, #0x55, lsl #12  ; [pp+0x551b8] AnonymousClosure: (0xb1fe04), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::glassThemeSlider (0xb1ee04)
    //     0xb1fbc0: ldr             x1, [x1, #0x1b8]
    // 0xb1fbc4: r0 = AllocateClosure()
    //     0xb1fbc4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1fbc8: stur            x0, [fp, #-8]
    // 0xb1fbcc: r0 = ListView()
    //     0xb1fbcc: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb1fbd0: stur            x0, [fp, #-0x20]
    // 0xb1fbd4: r16 = true
    //     0xb1fbd4: add             x16, NULL, #0x20  ; true
    // 0xb1fbd8: r30 = Instance_Axis
    //     0xb1fbd8: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb1fbdc: stp             lr, x16, [SP, #0x10]
    // 0xb1fbe0: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb1fbe0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb1fbe4: ldr             x16, [x16, #0x1c8]
    // 0xb1fbe8: r30 = Instance_EdgeInsets
    //     0xb1fbe8: add             lr, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xb1fbec: ldr             lr, [lr, #0x550]
    // 0xb1fbf0: stp             lr, x16, [SP]
    // 0xb1fbf4: mov             x1, x0
    // 0xb1fbf8: ldur            x2, [fp, #-8]
    // 0xb1fbfc: r3 = 6
    //     0xb1fbfc: movz            x3, #0x6
    // 0xb1fc00: r4 = const [0, 0x7, 0x4, 0x3, padding, 0x6, physics, 0x5, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xb1fc00: add             x4, PP, #0x55, lsl #12  ; [pp+0x551b0] List(13) [0, 0x7, 0x4, 0x3, "padding", 0x6, "physics", 0x5, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb1fc04: ldr             x4, [x4, #0x1b0]
    // 0xb1fc08: r0 = ListView.builder()
    //     0xb1fc08: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb1fc0c: ldur            x3, [fp, #-0x20]
    // 0xb1fc10: ldur            x2, [fp, #-0x10]
    // 0xb1fc14: ldur            x0, [fp, #-0x28]
    // 0xb1fc18: ldur            x1, [fp, #-0x30]
    // 0xb1fc1c: stur            x3, [fp, #-8]
    // 0xb1fc20: r0 = SizedBox()
    //     0xb1fc20: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb1fc24: mov             x1, x0
    // 0xb1fc28: r0 = 120.000000
    //     0xb1fc28: add             x0, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0xb1fc2c: ldr             x0, [x0, #0x3a0]
    // 0xb1fc30: StoreField: r1->field_13 = r0
    //     0xb1fc30: stur            w0, [x1, #0x13]
    // 0xb1fc34: ldur            x0, [fp, #-8]
    // 0xb1fc38: StoreField: r1->field_b = r0
    //     0xb1fc38: stur            w0, [x1, #0xb]
    // 0xb1fc3c: mov             x0, x1
    // 0xb1fc40: ldur            x1, [fp, #-0x30]
    // 0xb1fc44: ArrayStore: r1[8] = r0  ; List_4
    //     0xb1fc44: add             x25, x1, #0x2f
    //     0xb1fc48: str             w0, [x25]
    //     0xb1fc4c: tbz             w0, #0, #0xb1fc68
    //     0xb1fc50: ldurb           w16, [x1, #-1]
    //     0xb1fc54: ldurb           w17, [x0, #-1]
    //     0xb1fc58: and             x16, x17, x16, lsr #2
    //     0xb1fc5c: tst             x16, HEAP, lsr #32
    //     0xb1fc60: b.eq            #0xb1fc68
    //     0xb1fc64: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1fc68: r1 = <Widget>
    //     0xb1fc68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1fc6c: r0 = AllocateGrowableArray()
    //     0xb1fc6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb1fc70: mov             x1, x0
    // 0xb1fc74: ldur            x0, [fp, #-0x30]
    // 0xb1fc78: stur            x1, [fp, #-8]
    // 0xb1fc7c: StoreField: r1->field_f = r0
    //     0xb1fc7c: stur            w0, [x1, #0xf]
    // 0xb1fc80: r0 = 18
    //     0xb1fc80: movz            x0, #0x12
    // 0xb1fc84: StoreField: r1->field_b = r0
    //     0xb1fc84: stur            w0, [x1, #0xb]
    // 0xb1fc88: r0 = Column()
    //     0xb1fc88: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb1fc8c: mov             x1, x0
    // 0xb1fc90: r0 = Instance_Axis
    //     0xb1fc90: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb1fc94: stur            x1, [fp, #-0x20]
    // 0xb1fc98: StoreField: r1->field_f = r0
    //     0xb1fc98: stur            w0, [x1, #0xf]
    // 0xb1fc9c: r0 = Instance_MainAxisAlignment
    //     0xb1fc9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb1fca0: ldr             x0, [x0, #0xa08]
    // 0xb1fca4: StoreField: r1->field_13 = r0
    //     0xb1fca4: stur            w0, [x1, #0x13]
    // 0xb1fca8: r0 = Instance_MainAxisSize
    //     0xb1fca8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb1fcac: ldr             x0, [x0, #0xdd0]
    // 0xb1fcb0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb1fcb0: stur            w0, [x1, #0x17]
    // 0xb1fcb4: r0 = Instance_CrossAxisAlignment
    //     0xb1fcb4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb1fcb8: ldr             x0, [x0, #0x890]
    // 0xb1fcbc: StoreField: r1->field_1b = r0
    //     0xb1fcbc: stur            w0, [x1, #0x1b]
    // 0xb1fcc0: r0 = Instance_VerticalDirection
    //     0xb1fcc0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1fcc4: ldr             x0, [x0, #0xa20]
    // 0xb1fcc8: StoreField: r1->field_23 = r0
    //     0xb1fcc8: stur            w0, [x1, #0x23]
    // 0xb1fccc: r0 = Instance_Clip
    //     0xb1fccc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1fcd0: ldr             x0, [x0, #0x38]
    // 0xb1fcd4: StoreField: r1->field_2b = r0
    //     0xb1fcd4: stur            w0, [x1, #0x2b]
    // 0xb1fcd8: StoreField: r1->field_2f = rZR
    //     0xb1fcd8: stur            xzr, [x1, #0x2f]
    // 0xb1fcdc: ldur            x0, [fp, #-8]
    // 0xb1fce0: StoreField: r1->field_b = r0
    //     0xb1fce0: stur            w0, [x1, #0xb]
    // 0xb1fce4: r0 = Padding()
    //     0xb1fce4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1fce8: mov             x1, x0
    // 0xb1fcec: r0 = Instance_EdgeInsets
    //     0xb1fcec: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e560] Obj!EdgeInsets@d582e1
    //     0xb1fcf0: ldr             x0, [x0, #0x560]
    // 0xb1fcf4: stur            x1, [fp, #-8]
    // 0xb1fcf8: StoreField: r1->field_f = r0
    //     0xb1fcf8: stur            w0, [x1, #0xf]
    // 0xb1fcfc: ldur            x0, [fp, #-0x20]
    // 0xb1fd00: StoreField: r1->field_b = r0
    //     0xb1fd00: stur            w0, [x1, #0xb]
    // 0xb1fd04: r0 = Card()
    //     0xb1fd04: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb1fd08: mov             x1, x0
    // 0xb1fd0c: ldur            x0, [fp, #-0x10]
    // 0xb1fd10: stur            x1, [fp, #-0x20]
    // 0xb1fd14: StoreField: r1->field_b = r0
    //     0xb1fd14: stur            w0, [x1, #0xb]
    // 0xb1fd18: r0 = 0.000000
    //     0xb1fd18: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb1fd1c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb1fd1c: stur            w0, [x1, #0x17]
    // 0xb1fd20: ldur            x0, [fp, #-0x28]
    // 0xb1fd24: StoreField: r1->field_1b = r0
    //     0xb1fd24: stur            w0, [x1, #0x1b]
    // 0xb1fd28: r0 = true
    //     0xb1fd28: add             x0, NULL, #0x20  ; true
    // 0xb1fd2c: StoreField: r1->field_1f = r0
    //     0xb1fd2c: stur            w0, [x1, #0x1f]
    // 0xb1fd30: ldur            x2, [fp, #-8]
    // 0xb1fd34: StoreField: r1->field_2f = r2
    //     0xb1fd34: stur            w2, [x1, #0x2f]
    // 0xb1fd38: StoreField: r1->field_2b = r0
    //     0xb1fd38: stur            w0, [x1, #0x2b]
    // 0xb1fd3c: r0 = Instance__CardVariant
    //     0xb1fd3c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb1fd40: ldr             x0, [x0, #0xa68]
    // 0xb1fd44: StoreField: r1->field_33 = r0
    //     0xb1fd44: stur            w0, [x1, #0x33]
    // 0xb1fd48: r0 = Container()
    //     0xb1fd48: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1fd4c: stur            x0, [fp, #-8]
    // 0xb1fd50: r16 = Instance_EdgeInsets
    //     0xb1fd50: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb1fd54: ldr             x16, [x16, #0x980]
    // 0xb1fd58: ldur            lr, [fp, #-0x20]
    // 0xb1fd5c: stp             lr, x16, [SP]
    // 0xb1fd60: mov             x1, x0
    // 0xb1fd64: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xb1fd64: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xb1fd68: ldr             x4, [x4, #0x30]
    // 0xb1fd6c: r0 = Container()
    //     0xb1fd6c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1fd70: r0 = AnimatedContainer()
    //     0xb1fd70: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xb1fd74: stur            x0, [fp, #-0x10]
    // 0xb1fd78: r16 = Instance_Cubic
    //     0xb1fd78: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xb1fd7c: ldr             x16, [x16, #0xaf8]
    // 0xb1fd80: str             x16, [SP]
    // 0xb1fd84: mov             x1, x0
    // 0xb1fd88: ldur            x2, [fp, #-8]
    // 0xb1fd8c: r3 = Instance_Duration
    //     0xb1fd8c: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb1fd90: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xb1fd90: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xb1fd94: ldr             x4, [x4, #0xbc8]
    // 0xb1fd98: r0 = AnimatedContainer()
    //     0xb1fd98: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xb1fd9c: ldur            x0, [fp, #-0x10]
    // 0xb1fda0: LeaveFrame
    //     0xb1fda0: mov             SP, fp
    //     0xb1fda4: ldp             fp, lr, [SP], #0x10
    // 0xb1fda8: ret
    //     0xb1fda8: ret             
    // 0xb1fdac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1fdac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1fdb0: b               #0xb1ee28
    // 0xb1fdb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1fdb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1fdb8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1fdb8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1fdbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1fdbc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1fdc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1fdc0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1fdc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1fdc4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1fdc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1fdc8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1fdcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1fdcc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1fdd0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1fdd0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1fdd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1fdd4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1fdd8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1fdd8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1fddc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1fddc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1fde0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1fde0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1fde4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1fde4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1fde8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1fde8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1fdec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1fdec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1fdf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1fdf0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1fdf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1fdf4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1fdf8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1fdf8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1fdfc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1fdfc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1fe00: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1fe00: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb1fe04, size: 0x4e4
    // 0xb1fe04: EnterFrame
    //     0xb1fe04: stp             fp, lr, [SP, #-0x10]!
    //     0xb1fe08: mov             fp, SP
    // 0xb1fe0c: AllocStack(0x50)
    //     0xb1fe0c: sub             SP, SP, #0x50
    // 0xb1fe10: SetupParameters()
    //     0xb1fe10: ldr             x0, [fp, #0x20]
    //     0xb1fe14: ldur            w1, [x0, #0x17]
    //     0xb1fe18: add             x1, x1, HEAP, lsl #32
    //     0xb1fe1c: stur            x1, [fp, #-8]
    // 0xb1fe20: CheckStackOverflow
    //     0xb1fe20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1fe24: cmp             SP, x16
    //     0xb1fe28: b.ls            #0xb202cc
    // 0xb1fe2c: r1 = 2
    //     0xb1fe2c: movz            x1, #0x2
    // 0xb1fe30: r0 = AllocateContext()
    //     0xb1fe30: bl              #0x16f6108  ; AllocateContextStub
    // 0xb1fe34: mov             x2, x0
    // 0xb1fe38: ldur            x0, [fp, #-8]
    // 0xb1fe3c: stur            x2, [fp, #-0x10]
    // 0xb1fe40: StoreField: r2->field_b = r0
    //     0xb1fe40: stur            w0, [x2, #0xb]
    // 0xb1fe44: ldr             x1, [fp, #0x18]
    // 0xb1fe48: StoreField: r2->field_f = r1
    //     0xb1fe48: stur            w1, [x2, #0xf]
    // 0xb1fe4c: ldr             x1, [fp, #0x10]
    // 0xb1fe50: StoreField: r2->field_13 = r1
    //     0xb1fe50: stur            w1, [x2, #0x13]
    // 0xb1fe54: cmp             w1, #4
    // 0xb1fe58: b.ne            #0xb20120
    // 0xb1fe5c: r1 = Instance_Color
    //     0xb1fe5c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1fe60: d0 = 0.030000
    //     0xb1fe60: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb1fe64: ldr             d0, [x17, #0x238]
    // 0xb1fe68: r0 = withOpacity()
    //     0xb1fe68: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb1fe6c: stur            x0, [fp, #-0x18]
    // 0xb1fe70: r0 = Radius()
    //     0xb1fe70: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb1fe74: d0 = 10.000000
    //     0xb1fe74: fmov            d0, #10.00000000
    // 0xb1fe78: stur            x0, [fp, #-0x20]
    // 0xb1fe7c: StoreField: r0->field_7 = d0
    //     0xb1fe7c: stur            d0, [x0, #7]
    // 0xb1fe80: StoreField: r0->field_f = d0
    //     0xb1fe80: stur            d0, [x0, #0xf]
    // 0xb1fe84: r0 = BorderRadius()
    //     0xb1fe84: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb1fe88: mov             x1, x0
    // 0xb1fe8c: ldur            x0, [fp, #-0x20]
    // 0xb1fe90: stur            x1, [fp, #-0x28]
    // 0xb1fe94: StoreField: r1->field_7 = r0
    //     0xb1fe94: stur            w0, [x1, #7]
    // 0xb1fe98: StoreField: r1->field_b = r0
    //     0xb1fe98: stur            w0, [x1, #0xb]
    // 0xb1fe9c: StoreField: r1->field_f = r0
    //     0xb1fe9c: stur            w0, [x1, #0xf]
    // 0xb1fea0: StoreField: r1->field_13 = r0
    //     0xb1fea0: stur            w0, [x1, #0x13]
    // 0xb1fea4: r0 = BoxDecoration()
    //     0xb1fea4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb1fea8: mov             x3, x0
    // 0xb1feac: ldur            x0, [fp, #-0x18]
    // 0xb1feb0: stur            x3, [fp, #-0x20]
    // 0xb1feb4: StoreField: r3->field_7 = r0
    //     0xb1feb4: stur            w0, [x3, #7]
    // 0xb1feb8: ldur            x0, [fp, #-0x28]
    // 0xb1febc: StoreField: r3->field_13 = r0
    //     0xb1febc: stur            w0, [x3, #0x13]
    // 0xb1fec0: r0 = Instance_BoxShape
    //     0xb1fec0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb1fec4: ldr             x0, [x0, #0x80]
    // 0xb1fec8: StoreField: r3->field_23 = r0
    //     0xb1fec8: stur            w0, [x3, #0x23]
    // 0xb1fecc: r1 = Null
    //     0xb1fecc: mov             x1, NULL
    // 0xb1fed0: r2 = 4
    //     0xb1fed0: movz            x2, #0x4
    // 0xb1fed4: r0 = AllocateArray()
    //     0xb1fed4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1fed8: stur            x0, [fp, #-0x18]
    // 0xb1fedc: r16 = "+"
    //     0xb1fedc: ldr             x16, [PP, #0x2f50]  ; [pp+0x2f50] "+"
    // 0xb1fee0: StoreField: r0->field_f = r16
    //     0xb1fee0: stur            w16, [x0, #0xf]
    // 0xb1fee4: ldur            x1, [fp, #-8]
    // 0xb1fee8: LoadField: r2 = r1->field_f
    //     0xb1fee8: ldur            w2, [x1, #0xf]
    // 0xb1feec: DecompressPointer r2
    //     0xb1feec: add             x2, x2, HEAP, lsl #32
    // 0xb1fef0: LoadField: r3 = r1->field_13
    //     0xb1fef0: ldur            w3, [x1, #0x13]
    // 0xb1fef4: DecompressPointer r3
    //     0xb1fef4: add             x3, x3, HEAP, lsl #32
    // 0xb1fef8: stp             x3, x2, [SP]
    // 0xb1fefc: r4 = 0
    //     0xb1fefc: movz            x4, #0
    // 0xb1ff00: ldr             x0, [SP, #8]
    // 0xb1ff04: r5 = UnlinkedCall_0x613b5c
    //     0xb1ff04: add             x16, PP, #0x55, lsl #12  ; [pp+0x551c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1ff08: ldp             x5, lr, [x16, #0x1c0]
    // 0xb1ff0c: blr             lr
    // 0xb1ff10: cmp             w0, NULL
    // 0xb1ff14: b.eq            #0xb202d4
    // 0xb1ff18: LoadField: r1 = r0->field_b7
    //     0xb1ff18: ldur            w1, [x0, #0xb7]
    // 0xb1ff1c: DecompressPointer r1
    //     0xb1ff1c: add             x1, x1, HEAP, lsl #32
    // 0xb1ff20: cmp             w1, NULL
    // 0xb1ff24: b.eq            #0xb202d8
    // 0xb1ff28: LoadField: r0 = r1->field_b
    //     0xb1ff28: ldur            w0, [x1, #0xb]
    // 0xb1ff2c: r1 = LoadInt32Instr(r0)
    //     0xb1ff2c: sbfx            x1, x0, #1, #0x1f
    // 0xb1ff30: sub             x0, x1, #2
    // 0xb1ff34: lsl             x1, x0, #1
    // 0xb1ff38: ldur            x0, [fp, #-0x18]
    // 0xb1ff3c: StoreField: r0->field_13 = r1
    //     0xb1ff3c: stur            w1, [x0, #0x13]
    // 0xb1ff40: str             x0, [SP]
    // 0xb1ff44: r0 = _interpolate()
    //     0xb1ff44: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb1ff48: ldur            x2, [fp, #-0x10]
    // 0xb1ff4c: stur            x0, [fp, #-0x18]
    // 0xb1ff50: LoadField: r1 = r2->field_f
    //     0xb1ff50: ldur            w1, [x2, #0xf]
    // 0xb1ff54: DecompressPointer r1
    //     0xb1ff54: add             x1, x1, HEAP, lsl #32
    // 0xb1ff58: r0 = of()
    //     0xb1ff58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1ff5c: LoadField: r1 = r0->field_87
    //     0xb1ff5c: ldur            w1, [x0, #0x87]
    // 0xb1ff60: DecompressPointer r1
    //     0xb1ff60: add             x1, x1, HEAP, lsl #32
    // 0xb1ff64: LoadField: r0 = r1->field_2b
    //     0xb1ff64: ldur            w0, [x1, #0x2b]
    // 0xb1ff68: DecompressPointer r0
    //     0xb1ff68: add             x0, x0, HEAP, lsl #32
    // 0xb1ff6c: r16 = 12.000000
    //     0xb1ff6c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb1ff70: ldr             x16, [x16, #0x9e8]
    // 0xb1ff74: r30 = Instance_Color
    //     0xb1ff74: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1ff78: stp             lr, x16, [SP]
    // 0xb1ff7c: mov             x1, x0
    // 0xb1ff80: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1ff80: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1ff84: ldr             x4, [x4, #0xaa0]
    // 0xb1ff88: r0 = copyWith()
    //     0xb1ff88: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1ff8c: stur            x0, [fp, #-0x28]
    // 0xb1ff90: r0 = Text()
    //     0xb1ff90: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1ff94: mov             x2, x0
    // 0xb1ff98: ldur            x0, [fp, #-0x18]
    // 0xb1ff9c: stur            x2, [fp, #-0x30]
    // 0xb1ffa0: StoreField: r2->field_b = r0
    //     0xb1ffa0: stur            w0, [x2, #0xb]
    // 0xb1ffa4: ldur            x0, [fp, #-0x28]
    // 0xb1ffa8: StoreField: r2->field_13 = r0
    //     0xb1ffa8: stur            w0, [x2, #0x13]
    // 0xb1ffac: ldur            x0, [fp, #-0x10]
    // 0xb1ffb0: LoadField: r1 = r0->field_f
    //     0xb1ffb0: ldur            w1, [x0, #0xf]
    // 0xb1ffb4: DecompressPointer r1
    //     0xb1ffb4: add             x1, x1, HEAP, lsl #32
    // 0xb1ffb8: r0 = of()
    //     0xb1ffb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1ffbc: LoadField: r1 = r0->field_87
    //     0xb1ffbc: ldur            w1, [x0, #0x87]
    // 0xb1ffc0: DecompressPointer r1
    //     0xb1ffc0: add             x1, x1, HEAP, lsl #32
    // 0xb1ffc4: LoadField: r0 = r1->field_2b
    //     0xb1ffc4: ldur            w0, [x1, #0x2b]
    // 0xb1ffc8: DecompressPointer r0
    //     0xb1ffc8: add             x0, x0, HEAP, lsl #32
    // 0xb1ffcc: r16 = 12.000000
    //     0xb1ffcc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb1ffd0: ldr             x16, [x16, #0x9e8]
    // 0xb1ffd4: r30 = Instance_Color
    //     0xb1ffd4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1ffd8: stp             lr, x16, [SP]
    // 0xb1ffdc: mov             x1, x0
    // 0xb1ffe0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1ffe0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1ffe4: ldr             x4, [x4, #0xaa0]
    // 0xb1ffe8: r0 = copyWith()
    //     0xb1ffe8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1ffec: stur            x0, [fp, #-0x18]
    // 0xb1fff0: r0 = Text()
    //     0xb1fff0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1fff4: mov             x3, x0
    // 0xb1fff8: r0 = "Photos"
    //     0xb1fff8: add             x0, PP, #0x52, lsl #12  ; [pp+0x52260] "Photos"
    //     0xb1fffc: ldr             x0, [x0, #0x260]
    // 0xb20000: stur            x3, [fp, #-0x28]
    // 0xb20004: StoreField: r3->field_b = r0
    //     0xb20004: stur            w0, [x3, #0xb]
    // 0xb20008: ldur            x0, [fp, #-0x18]
    // 0xb2000c: StoreField: r3->field_13 = r0
    //     0xb2000c: stur            w0, [x3, #0x13]
    // 0xb20010: r1 = Null
    //     0xb20010: mov             x1, NULL
    // 0xb20014: r2 = 4
    //     0xb20014: movz            x2, #0x4
    // 0xb20018: r0 = AllocateArray()
    //     0xb20018: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2001c: mov             x2, x0
    // 0xb20020: ldur            x0, [fp, #-0x30]
    // 0xb20024: stur            x2, [fp, #-0x18]
    // 0xb20028: StoreField: r2->field_f = r0
    //     0xb20028: stur            w0, [x2, #0xf]
    // 0xb2002c: ldur            x0, [fp, #-0x28]
    // 0xb20030: StoreField: r2->field_13 = r0
    //     0xb20030: stur            w0, [x2, #0x13]
    // 0xb20034: r1 = <Widget>
    //     0xb20034: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb20038: r0 = AllocateGrowableArray()
    //     0xb20038: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2003c: mov             x1, x0
    // 0xb20040: ldur            x0, [fp, #-0x18]
    // 0xb20044: stur            x1, [fp, #-0x28]
    // 0xb20048: StoreField: r1->field_f = r0
    //     0xb20048: stur            w0, [x1, #0xf]
    // 0xb2004c: r0 = 4
    //     0xb2004c: movz            x0, #0x4
    // 0xb20050: StoreField: r1->field_b = r0
    //     0xb20050: stur            w0, [x1, #0xb]
    // 0xb20054: r0 = Column()
    //     0xb20054: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb20058: mov             x1, x0
    // 0xb2005c: r0 = Instance_Axis
    //     0xb2005c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb20060: stur            x1, [fp, #-0x18]
    // 0xb20064: StoreField: r1->field_f = r0
    //     0xb20064: stur            w0, [x1, #0xf]
    // 0xb20068: r0 = Instance_MainAxisAlignment
    //     0xb20068: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb2006c: ldr             x0, [x0, #0xab0]
    // 0xb20070: StoreField: r1->field_13 = r0
    //     0xb20070: stur            w0, [x1, #0x13]
    // 0xb20074: r0 = Instance_MainAxisSize
    //     0xb20074: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb20078: ldr             x0, [x0, #0xa10]
    // 0xb2007c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2007c: stur            w0, [x1, #0x17]
    // 0xb20080: r0 = Instance_CrossAxisAlignment
    //     0xb20080: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb20084: ldr             x0, [x0, #0xa18]
    // 0xb20088: StoreField: r1->field_1b = r0
    //     0xb20088: stur            w0, [x1, #0x1b]
    // 0xb2008c: r0 = Instance_VerticalDirection
    //     0xb2008c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb20090: ldr             x0, [x0, #0xa20]
    // 0xb20094: StoreField: r1->field_23 = r0
    //     0xb20094: stur            w0, [x1, #0x23]
    // 0xb20098: r0 = Instance_Clip
    //     0xb20098: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2009c: ldr             x0, [x0, #0x38]
    // 0xb200a0: StoreField: r1->field_2b = r0
    //     0xb200a0: stur            w0, [x1, #0x2b]
    // 0xb200a4: StoreField: r1->field_2f = rZR
    //     0xb200a4: stur            xzr, [x1, #0x2f]
    // 0xb200a8: ldur            x0, [fp, #-0x28]
    // 0xb200ac: StoreField: r1->field_b = r0
    //     0xb200ac: stur            w0, [x1, #0xb]
    // 0xb200b0: r0 = Container()
    //     0xb200b0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb200b4: stur            x0, [fp, #-0x28]
    // 0xb200b8: r16 = 64.000000
    //     0xb200b8: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb200bc: ldr             x16, [x16, #0x838]
    // 0xb200c0: ldur            lr, [fp, #-0x20]
    // 0xb200c4: stp             lr, x16, [SP, #8]
    // 0xb200c8: ldur            x16, [fp, #-0x18]
    // 0xb200cc: str             x16, [SP]
    // 0xb200d0: mov             x1, x0
    // 0xb200d4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, width, 0x1, null]
    //     0xb200d4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33830] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "width", 0x1, Null]
    //     0xb200d8: ldr             x4, [x4, #0x830]
    // 0xb200dc: r0 = Container()
    //     0xb200dc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb200e0: r0 = GestureDetector()
    //     0xb200e0: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb200e4: ldur            x2, [fp, #-0x10]
    // 0xb200e8: r1 = Function '<anonymous closure>':.
    //     0xb200e8: add             x1, PP, #0x55, lsl #12  ; [pp+0x551d0] AnonymousClosure: (0xb20460), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::glassThemeSlider (0xb1ee04)
    //     0xb200ec: ldr             x1, [x1, #0x1d0]
    // 0xb200f0: stur            x0, [fp, #-0x18]
    // 0xb200f4: r0 = AllocateClosure()
    //     0xb200f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb200f8: ldur            x16, [fp, #-0x28]
    // 0xb200fc: stp             x16, x0, [SP]
    // 0xb20100: ldur            x1, [fp, #-0x18]
    // 0xb20104: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb20104: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb20108: ldr             x4, [x4, #0xaf0]
    // 0xb2010c: r0 = GestureDetector()
    //     0xb2010c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb20110: ldur            x0, [fp, #-0x18]
    // 0xb20114: LeaveFrame
    //     0xb20114: mov             SP, fp
    //     0xb20118: ldp             fp, lr, [SP], #0x10
    // 0xb2011c: ret
    //     0xb2011c: ret             
    // 0xb20120: mov             x1, x0
    // 0xb20124: LoadField: r0 = r1->field_f
    //     0xb20124: ldur            w0, [x1, #0xf]
    // 0xb20128: DecompressPointer r0
    //     0xb20128: add             x0, x0, HEAP, lsl #32
    // 0xb2012c: LoadField: r2 = r1->field_13
    //     0xb2012c: ldur            w2, [x1, #0x13]
    // 0xb20130: DecompressPointer r2
    //     0xb20130: add             x2, x2, HEAP, lsl #32
    // 0xb20134: stp             x2, x0, [SP]
    // 0xb20138: r4 = 0
    //     0xb20138: movz            x4, #0
    // 0xb2013c: ldr             x0, [SP, #8]
    // 0xb20140: r5 = UnlinkedCall_0x613b5c
    //     0xb20140: add             x16, PP, #0x55, lsl #12  ; [pp+0x551d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb20144: ldp             x5, lr, [x16, #0x1d8]
    // 0xb20148: blr             lr
    // 0xb2014c: cmp             w0, NULL
    // 0xb20150: b.ne            #0xb20160
    // 0xb20154: ldur            x3, [fp, #-0x10]
    // 0xb20158: r4 = Null
    //     0xb20158: mov             x4, NULL
    // 0xb2015c: b               #0xb201cc
    // 0xb20160: ldur            x3, [fp, #-0x10]
    // 0xb20164: LoadField: r2 = r0->field_b7
    //     0xb20164: ldur            w2, [x0, #0xb7]
    // 0xb20168: DecompressPointer r2
    //     0xb20168: add             x2, x2, HEAP, lsl #32
    // 0xb2016c: LoadField: r0 = r3->field_13
    //     0xb2016c: ldur            w0, [x3, #0x13]
    // 0xb20170: DecompressPointer r0
    //     0xb20170: add             x0, x0, HEAP, lsl #32
    // 0xb20174: cmp             w2, NULL
    // 0xb20178: b.eq            #0xb202dc
    // 0xb2017c: LoadField: r1 = r2->field_b
    //     0xb2017c: ldur            w1, [x2, #0xb]
    // 0xb20180: r4 = LoadInt32Instr(r0)
    //     0xb20180: sbfx            x4, x0, #1, #0x1f
    //     0xb20184: tbz             w0, #0, #0xb2018c
    //     0xb20188: ldur            x4, [x0, #7]
    // 0xb2018c: r0 = LoadInt32Instr(r1)
    //     0xb2018c: sbfx            x0, x1, #1, #0x1f
    // 0xb20190: mov             x1, x4
    // 0xb20194: cmp             x1, x0
    // 0xb20198: b.hs            #0xb202e0
    // 0xb2019c: LoadField: r0 = r2->field_f
    //     0xb2019c: ldur            w0, [x2, #0xf]
    // 0xb201a0: DecompressPointer r0
    //     0xb201a0: add             x0, x0, HEAP, lsl #32
    // 0xb201a4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb201a4: add             x16, x0, x4, lsl #2
    //     0xb201a8: ldur            w1, [x16, #0xf]
    // 0xb201ac: DecompressPointer r1
    //     0xb201ac: add             x1, x1, HEAP, lsl #32
    // 0xb201b0: LoadField: r0 = r1->field_7
    //     0xb201b0: ldur            w0, [x1, #7]
    // 0xb201b4: DecompressPointer r0
    //     0xb201b4: add             x0, x0, HEAP, lsl #32
    // 0xb201b8: cmp             w0, NULL
    // 0xb201bc: b.eq            #0xb202e4
    // 0xb201c0: LoadField: r1 = r0->field_b
    //     0xb201c0: ldur            w1, [x0, #0xb]
    // 0xb201c4: DecompressPointer r1
    //     0xb201c4: add             x1, x1, HEAP, lsl #32
    // 0xb201c8: mov             x4, x1
    // 0xb201cc: mov             x0, x4
    // 0xb201d0: stur            x4, [fp, #-8]
    // 0xb201d4: r2 = Null
    //     0xb201d4: mov             x2, NULL
    // 0xb201d8: r1 = Null
    //     0xb201d8: mov             x1, NULL
    // 0xb201dc: r4 = LoadClassIdInstr(r0)
    //     0xb201dc: ldur            x4, [x0, #-1]
    //     0xb201e0: ubfx            x4, x4, #0xc, #0x14
    // 0xb201e4: sub             x4, x4, #0x5e
    // 0xb201e8: cmp             x4, #1
    // 0xb201ec: b.ls            #0xb20200
    // 0xb201f0: r8 = String
    //     0xb201f0: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb201f4: r3 = Null
    //     0xb201f4: add             x3, PP, #0x55, lsl #12  ; [pp+0x551e8] Null
    //     0xb201f8: ldr             x3, [x3, #0x1e8]
    // 0xb201fc: r0 = String()
    //     0xb201fc: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb20200: r1 = Function '<anonymous closure>':.
    //     0xb20200: add             x1, PP, #0x55, lsl #12  ; [pp+0x551f8] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb20204: ldr             x1, [x1, #0x1f8]
    // 0xb20208: r2 = Null
    //     0xb20208: mov             x2, NULL
    // 0xb2020c: r0 = AllocateClosure()
    //     0xb2020c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb20210: r1 = Function '<anonymous closure>':.
    //     0xb20210: add             x1, PP, #0x55, lsl #12  ; [pp+0x55200] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb20214: ldr             x1, [x1, #0x200]
    // 0xb20218: r2 = Null
    //     0xb20218: mov             x2, NULL
    // 0xb2021c: stur            x0, [fp, #-0x18]
    // 0xb20220: r0 = AllocateClosure()
    //     0xb20220: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb20224: stur            x0, [fp, #-0x20]
    // 0xb20228: r0 = CachedNetworkImage()
    //     0xb20228: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb2022c: stur            x0, [fp, #-0x28]
    // 0xb20230: r16 = 64.000000
    //     0xb20230: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb20234: ldr             x16, [x16, #0x838]
    // 0xb20238: r30 = 64.000000
    //     0xb20238: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb2023c: ldr             lr, [lr, #0x838]
    // 0xb20240: stp             lr, x16, [SP, #0x10]
    // 0xb20244: ldur            x16, [fp, #-0x18]
    // 0xb20248: ldur            lr, [fp, #-0x20]
    // 0xb2024c: stp             lr, x16, [SP]
    // 0xb20250: mov             x1, x0
    // 0xb20254: ldur            x2, [fp, #-8]
    // 0xb20258: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xb20258: add             x4, PP, #0x40, lsl #12  ; [pp+0x40388] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xb2025c: ldr             x4, [x4, #0x388]
    // 0xb20260: r0 = CachedNetworkImage()
    //     0xb20260: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb20264: r0 = Container()
    //     0xb20264: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb20268: stur            x0, [fp, #-8]
    // 0xb2026c: r16 = Instance_EdgeInsets
    //     0xb2026c: add             x16, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xb20270: ldr             x16, [x16, #0x550]
    // 0xb20274: ldur            lr, [fp, #-0x28]
    // 0xb20278: stp             lr, x16, [SP]
    // 0xb2027c: mov             x1, x0
    // 0xb20280: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xb20280: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xb20284: ldr             x4, [x4, #0x30]
    // 0xb20288: r0 = Container()
    //     0xb20288: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb2028c: r0 = GestureDetector()
    //     0xb2028c: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb20290: ldur            x2, [fp, #-0x10]
    // 0xb20294: r1 = Function '<anonymous closure>':.
    //     0xb20294: add             x1, PP, #0x55, lsl #12  ; [pp+0x55208] AnonymousClosure: (0xb202e8), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::glassThemeSlider (0xb1ee04)
    //     0xb20298: ldr             x1, [x1, #0x208]
    // 0xb2029c: stur            x0, [fp, #-0x10]
    // 0xb202a0: r0 = AllocateClosure()
    //     0xb202a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb202a4: ldur            x16, [fp, #-8]
    // 0xb202a8: stp             x16, x0, [SP]
    // 0xb202ac: ldur            x1, [fp, #-0x10]
    // 0xb202b0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb202b0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb202b4: ldr             x4, [x4, #0xaf0]
    // 0xb202b8: r0 = GestureDetector()
    //     0xb202b8: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb202bc: ldur            x0, [fp, #-0x10]
    // 0xb202c0: LeaveFrame
    //     0xb202c0: mov             SP, fp
    //     0xb202c4: ldp             fp, lr, [SP], #0x10
    // 0xb202c8: ret
    //     0xb202c8: ret             
    // 0xb202cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb202cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb202d0: b               #0xb1fe2c
    // 0xb202d4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb202d4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb202d8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb202d8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb202dc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb202dc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb202e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb202e0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb202e4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb202e4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb202e8, size: 0x9c
    // 0xb202e8: EnterFrame
    //     0xb202e8: stp             fp, lr, [SP, #-0x10]!
    //     0xb202ec: mov             fp, SP
    // 0xb202f0: AllocStack(0x28)
    //     0xb202f0: sub             SP, SP, #0x28
    // 0xb202f4: SetupParameters()
    //     0xb202f4: ldr             x0, [fp, #0x10]
    //     0xb202f8: ldur            w2, [x0, #0x17]
    //     0xb202fc: add             x2, x2, HEAP, lsl #32
    //     0xb20300: stur            x2, [fp, #-8]
    // 0xb20304: CheckStackOverflow
    //     0xb20304: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb20308: cmp             SP, x16
    //     0xb2030c: b.ls            #0xb2037c
    // 0xb20310: LoadField: r1 = r2->field_f
    //     0xb20310: ldur            w1, [x2, #0xf]
    // 0xb20314: DecompressPointer r1
    //     0xb20314: add             x1, x1, HEAP, lsl #32
    // 0xb20318: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb20318: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb2031c: r0 = of()
    //     0xb2031c: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xb20320: ldur            x2, [fp, #-8]
    // 0xb20324: r1 = Function '<anonymous closure>':.
    //     0xb20324: add             x1, PP, #0x55, lsl #12  ; [pp+0x55210] AnonymousClosure: (0xb20384), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::glassThemeSlider (0xb1ee04)
    //     0xb20328: ldr             x1, [x1, #0x210]
    // 0xb2032c: stur            x0, [fp, #-8]
    // 0xb20330: r0 = AllocateClosure()
    //     0xb20330: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb20334: r1 = Null
    //     0xb20334: mov             x1, NULL
    // 0xb20338: stur            x0, [fp, #-0x10]
    // 0xb2033c: r0 = MaterialPageRoute()
    //     0xb2033c: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xb20340: mov             x1, x0
    // 0xb20344: ldur            x2, [fp, #-0x10]
    // 0xb20348: stur            x0, [fp, #-0x10]
    // 0xb2034c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb2034c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb20350: r0 = MaterialPageRoute()
    //     0xb20350: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xb20354: ldur            x16, [fp, #-8]
    // 0xb20358: stp             x16, NULL, [SP, #8]
    // 0xb2035c: ldur            x16, [fp, #-0x10]
    // 0xb20360: str             x16, [SP]
    // 0xb20364: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb20364: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb20368: r0 = push()
    //     0xb20368: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xb2036c: r0 = Null
    //     0xb2036c: mov             x0, NULL
    // 0xb20370: LeaveFrame
    //     0xb20370: mov             SP, fp
    //     0xb20374: ldp             fp, lr, [SP], #0x10
    // 0xb20378: ret
    //     0xb20378: ret             
    // 0xb2037c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2037c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb20380: b               #0xb20310
  }
  [closure] TestimonialMoreImagesWidget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb20384, size: 0xb0
    // 0xb20384: EnterFrame
    //     0xb20384: stp             fp, lr, [SP, #-0x10]!
    //     0xb20388: mov             fp, SP
    // 0xb2038c: AllocStack(0x20)
    //     0xb2038c: sub             SP, SP, #0x20
    // 0xb20390: SetupParameters()
    //     0xb20390: ldr             x0, [fp, #0x18]
    //     0xb20394: ldur            w1, [x0, #0x17]
    //     0xb20398: add             x1, x1, HEAP, lsl #32
    // 0xb2039c: CheckStackOverflow
    //     0xb2039c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb203a0: cmp             SP, x16
    //     0xb203a4: b.ls            #0xb20428
    // 0xb203a8: LoadField: r0 = r1->field_13
    //     0xb203a8: ldur            w0, [x1, #0x13]
    // 0xb203ac: DecompressPointer r0
    //     0xb203ac: add             x0, x0, HEAP, lsl #32
    // 0xb203b0: stur            x0, [fp, #-8]
    // 0xb203b4: LoadField: r2 = r1->field_b
    //     0xb203b4: ldur            w2, [x1, #0xb]
    // 0xb203b8: DecompressPointer r2
    //     0xb203b8: add             x2, x2, HEAP, lsl #32
    // 0xb203bc: LoadField: r1 = r2->field_f
    //     0xb203bc: ldur            w1, [x2, #0xf]
    // 0xb203c0: DecompressPointer r1
    //     0xb203c0: add             x1, x1, HEAP, lsl #32
    // 0xb203c4: LoadField: r3 = r2->field_13
    //     0xb203c4: ldur            w3, [x2, #0x13]
    // 0xb203c8: DecompressPointer r3
    //     0xb203c8: add             x3, x3, HEAP, lsl #32
    // 0xb203cc: stp             x3, x1, [SP]
    // 0xb203d0: r4 = 0
    //     0xb203d0: movz            x4, #0
    // 0xb203d4: ldr             x0, [SP, #8]
    // 0xb203d8: r16 = UnlinkedCall_0x613b5c
    //     0xb203d8: add             x16, PP, #0x55, lsl #12  ; [pp+0x55218] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb203dc: add             x16, x16, #0x218
    // 0xb203e0: ldp             x5, lr, [x16]
    // 0xb203e4: blr             lr
    // 0xb203e8: cmp             w0, NULL
    // 0xb203ec: b.eq            #0xb20430
    // 0xb203f0: LoadField: r1 = r0->field_b7
    //     0xb203f0: ldur            w1, [x0, #0xb7]
    // 0xb203f4: DecompressPointer r1
    //     0xb203f4: add             x1, x1, HEAP, lsl #32
    // 0xb203f8: stur            x1, [fp, #-0x10]
    // 0xb203fc: r0 = TestimonialMoreImagesWidget()
    //     0xb203fc: bl              #0xb20434  ; AllocateTestimonialMoreImagesWidgetStub -> TestimonialMoreImagesWidget (size=0x1c)
    // 0xb20400: ldur            x1, [fp, #-0x10]
    // 0xb20404: StoreField: r0->field_b = r1
    //     0xb20404: stur            w1, [x0, #0xb]
    // 0xb20408: ldur            x1, [fp, #-8]
    // 0xb2040c: r2 = LoadInt32Instr(r1)
    //     0xb2040c: sbfx            x2, x1, #1, #0x1f
    //     0xb20410: tbz             w1, #0, #0xb20418
    //     0xb20414: ldur            x2, [x1, #7]
    // 0xb20418: StoreField: r0->field_f = r2
    //     0xb20418: stur            x2, [x0, #0xf]
    // 0xb2041c: LeaveFrame
    //     0xb2041c: mov             SP, fp
    //     0xb20420: ldp             fp, lr, [SP], #0x10
    // 0xb20424: ret
    //     0xb20424: ret             
    // 0xb20428: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb20428: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2042c: b               #0xb203a8
    // 0xb20430: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb20430: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb20460, size: 0x9c
    // 0xb20460: EnterFrame
    //     0xb20460: stp             fp, lr, [SP, #-0x10]!
    //     0xb20464: mov             fp, SP
    // 0xb20468: AllocStack(0x28)
    //     0xb20468: sub             SP, SP, #0x28
    // 0xb2046c: SetupParameters()
    //     0xb2046c: ldr             x0, [fp, #0x10]
    //     0xb20470: ldur            w2, [x0, #0x17]
    //     0xb20474: add             x2, x2, HEAP, lsl #32
    //     0xb20478: stur            x2, [fp, #-8]
    // 0xb2047c: CheckStackOverflow
    //     0xb2047c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb20480: cmp             SP, x16
    //     0xb20484: b.ls            #0xb204f4
    // 0xb20488: LoadField: r1 = r2->field_f
    //     0xb20488: ldur            w1, [x2, #0xf]
    // 0xb2048c: DecompressPointer r1
    //     0xb2048c: add             x1, x1, HEAP, lsl #32
    // 0xb20490: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb20490: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb20494: r0 = of()
    //     0xb20494: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xb20498: ldur            x2, [fp, #-8]
    // 0xb2049c: r1 = Function '<anonymous closure>':.
    //     0xb2049c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55228] AnonymousClosure: (0xb204fc), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::glassThemeSlider (0xb1ee04)
    //     0xb204a0: ldr             x1, [x1, #0x228]
    // 0xb204a4: stur            x0, [fp, #-8]
    // 0xb204a8: r0 = AllocateClosure()
    //     0xb204a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb204ac: r1 = Null
    //     0xb204ac: mov             x1, NULL
    // 0xb204b0: stur            x0, [fp, #-0x10]
    // 0xb204b4: r0 = MaterialPageRoute()
    //     0xb204b4: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xb204b8: mov             x1, x0
    // 0xb204bc: ldur            x2, [fp, #-0x10]
    // 0xb204c0: stur            x0, [fp, #-0x10]
    // 0xb204c4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb204c4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb204c8: r0 = MaterialPageRoute()
    //     0xb204c8: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xb204cc: ldur            x16, [fp, #-8]
    // 0xb204d0: stp             x16, NULL, [SP, #8]
    // 0xb204d4: ldur            x16, [fp, #-0x10]
    // 0xb204d8: str             x16, [SP]
    // 0xb204dc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb204dc: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb204e0: r0 = push()
    //     0xb204e0: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xb204e4: r0 = Null
    //     0xb204e4: mov             x0, NULL
    // 0xb204e8: LeaveFrame
    //     0xb204e8: mov             SP, fp
    //     0xb204ec: ldp             fp, lr, [SP], #0x10
    // 0xb204f0: ret
    //     0xb204f0: ret             
    // 0xb204f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb204f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb204f8: b               #0xb20488
  }
  [closure] TestimonialMoreImagesWidget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb204fc, size: 0xb0
    // 0xb204fc: EnterFrame
    //     0xb204fc: stp             fp, lr, [SP, #-0x10]!
    //     0xb20500: mov             fp, SP
    // 0xb20504: AllocStack(0x20)
    //     0xb20504: sub             SP, SP, #0x20
    // 0xb20508: SetupParameters()
    //     0xb20508: ldr             x0, [fp, #0x18]
    //     0xb2050c: ldur            w1, [x0, #0x17]
    //     0xb20510: add             x1, x1, HEAP, lsl #32
    // 0xb20514: CheckStackOverflow
    //     0xb20514: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb20518: cmp             SP, x16
    //     0xb2051c: b.ls            #0xb205a0
    // 0xb20520: LoadField: r0 = r1->field_13
    //     0xb20520: ldur            w0, [x1, #0x13]
    // 0xb20524: DecompressPointer r0
    //     0xb20524: add             x0, x0, HEAP, lsl #32
    // 0xb20528: stur            x0, [fp, #-8]
    // 0xb2052c: LoadField: r2 = r1->field_b
    //     0xb2052c: ldur            w2, [x1, #0xb]
    // 0xb20530: DecompressPointer r2
    //     0xb20530: add             x2, x2, HEAP, lsl #32
    // 0xb20534: LoadField: r1 = r2->field_f
    //     0xb20534: ldur            w1, [x2, #0xf]
    // 0xb20538: DecompressPointer r1
    //     0xb20538: add             x1, x1, HEAP, lsl #32
    // 0xb2053c: LoadField: r3 = r2->field_13
    //     0xb2053c: ldur            w3, [x2, #0x13]
    // 0xb20540: DecompressPointer r3
    //     0xb20540: add             x3, x3, HEAP, lsl #32
    // 0xb20544: stp             x3, x1, [SP]
    // 0xb20548: r4 = 0
    //     0xb20548: movz            x4, #0
    // 0xb2054c: ldr             x0, [SP, #8]
    // 0xb20550: r16 = UnlinkedCall_0x613b5c
    //     0xb20550: add             x16, PP, #0x55, lsl #12  ; [pp+0x55230] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb20554: add             x16, x16, #0x230
    // 0xb20558: ldp             x5, lr, [x16]
    // 0xb2055c: blr             lr
    // 0xb20560: cmp             w0, NULL
    // 0xb20564: b.eq            #0xb205a8
    // 0xb20568: LoadField: r1 = r0->field_b7
    //     0xb20568: ldur            w1, [x0, #0xb7]
    // 0xb2056c: DecompressPointer r1
    //     0xb2056c: add             x1, x1, HEAP, lsl #32
    // 0xb20570: stur            x1, [fp, #-0x10]
    // 0xb20574: r0 = TestimonialMoreImagesWidget()
    //     0xb20574: bl              #0xb20434  ; AllocateTestimonialMoreImagesWidgetStub -> TestimonialMoreImagesWidget (size=0x1c)
    // 0xb20578: ldur            x1, [fp, #-0x10]
    // 0xb2057c: StoreField: r0->field_b = r1
    //     0xb2057c: stur            w1, [x0, #0xb]
    // 0xb20580: ldur            x1, [fp, #-8]
    // 0xb20584: r2 = LoadInt32Instr(r1)
    //     0xb20584: sbfx            x2, x1, #1, #0x1f
    //     0xb20588: tbz             w1, #0, #0xb20590
    //     0xb2058c: ldur            x2, [x1, #7]
    // 0xb20590: StoreField: r0->field_f = r2
    //     0xb20590: stur            x2, [x0, #0xf]
    // 0xb20594: LeaveFrame
    //     0xb20594: mov             SP, fp
    //     0xb20598: ldp             fp, lr, [SP], #0x10
    // 0xb2059c: ret
    //     0xb2059c: ret             
    // 0xb205a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb205a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb205a4: b               #0xb20520
    // 0xb205a8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb205a8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb205ac, size: 0x1e8
    // 0xb205ac: EnterFrame
    //     0xb205ac: stp             fp, lr, [SP, #-0x10]!
    //     0xb205b0: mov             fp, SP
    // 0xb205b4: AllocStack(0x48)
    //     0xb205b4: sub             SP, SP, #0x48
    // 0xb205b8: SetupParameters()
    //     0xb205b8: ldr             x0, [fp, #0x20]
    //     0xb205bc: ldur            w1, [x0, #0x17]
    //     0xb205c0: add             x1, x1, HEAP, lsl #32
    //     0xb205c4: stur            x1, [fp, #-8]
    // 0xb205c8: CheckStackOverflow
    //     0xb205c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb205cc: cmp             SP, x16
    //     0xb205d0: b.ls            #0xb20780
    // 0xb205d4: r1 = 2
    //     0xb205d4: movz            x1, #0x2
    // 0xb205d8: r0 = AllocateContext()
    //     0xb205d8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb205dc: mov             x1, x0
    // 0xb205e0: ldur            x0, [fp, #-8]
    // 0xb205e4: stur            x1, [fp, #-0x10]
    // 0xb205e8: StoreField: r1->field_b = r0
    //     0xb205e8: stur            w0, [x1, #0xb]
    // 0xb205ec: ldr             x2, [fp, #0x18]
    // 0xb205f0: StoreField: r1->field_f = r2
    //     0xb205f0: stur            w2, [x1, #0xf]
    // 0xb205f4: ldr             x2, [fp, #0x10]
    // 0xb205f8: StoreField: r1->field_13 = r2
    //     0xb205f8: stur            w2, [x1, #0x13]
    // 0xb205fc: LoadField: r2 = r0->field_f
    //     0xb205fc: ldur            w2, [x0, #0xf]
    // 0xb20600: DecompressPointer r2
    //     0xb20600: add             x2, x2, HEAP, lsl #32
    // 0xb20604: LoadField: r3 = r0->field_13
    //     0xb20604: ldur            w3, [x0, #0x13]
    // 0xb20608: DecompressPointer r3
    //     0xb20608: add             x3, x3, HEAP, lsl #32
    // 0xb2060c: stp             x3, x2, [SP]
    // 0xb20610: r4 = 0
    //     0xb20610: movz            x4, #0
    // 0xb20614: ldr             x0, [SP, #8]
    // 0xb20618: r16 = UnlinkedCall_0x613b5c
    //     0xb20618: add             x16, PP, #0x55, lsl #12  ; [pp+0x55240] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb2061c: add             x16, x16, #0x240
    // 0xb20620: ldp             x5, lr, [x16]
    // 0xb20624: blr             lr
    // 0xb20628: cmp             w0, NULL
    // 0xb2062c: b.ne            #0xb2063c
    // 0xb20630: ldur            x3, [fp, #-0x10]
    // 0xb20634: r4 = Null
    //     0xb20634: mov             x4, NULL
    // 0xb20638: b               #0xb206a8
    // 0xb2063c: ldur            x3, [fp, #-0x10]
    // 0xb20640: LoadField: r2 = r0->field_b7
    //     0xb20640: ldur            w2, [x0, #0xb7]
    // 0xb20644: DecompressPointer r2
    //     0xb20644: add             x2, x2, HEAP, lsl #32
    // 0xb20648: LoadField: r0 = r3->field_13
    //     0xb20648: ldur            w0, [x3, #0x13]
    // 0xb2064c: DecompressPointer r0
    //     0xb2064c: add             x0, x0, HEAP, lsl #32
    // 0xb20650: cmp             w2, NULL
    // 0xb20654: b.eq            #0xb20788
    // 0xb20658: LoadField: r1 = r2->field_b
    //     0xb20658: ldur            w1, [x2, #0xb]
    // 0xb2065c: r4 = LoadInt32Instr(r0)
    //     0xb2065c: sbfx            x4, x0, #1, #0x1f
    //     0xb20660: tbz             w0, #0, #0xb20668
    //     0xb20664: ldur            x4, [x0, #7]
    // 0xb20668: r0 = LoadInt32Instr(r1)
    //     0xb20668: sbfx            x0, x1, #1, #0x1f
    // 0xb2066c: mov             x1, x4
    // 0xb20670: cmp             x1, x0
    // 0xb20674: b.hs            #0xb2078c
    // 0xb20678: LoadField: r0 = r2->field_f
    //     0xb20678: ldur            w0, [x2, #0xf]
    // 0xb2067c: DecompressPointer r0
    //     0xb2067c: add             x0, x0, HEAP, lsl #32
    // 0xb20680: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb20680: add             x16, x0, x4, lsl #2
    //     0xb20684: ldur            w1, [x16, #0xf]
    // 0xb20688: DecompressPointer r1
    //     0xb20688: add             x1, x1, HEAP, lsl #32
    // 0xb2068c: LoadField: r0 = r1->field_7
    //     0xb2068c: ldur            w0, [x1, #7]
    // 0xb20690: DecompressPointer r0
    //     0xb20690: add             x0, x0, HEAP, lsl #32
    // 0xb20694: cmp             w0, NULL
    // 0xb20698: b.eq            #0xb20790
    // 0xb2069c: LoadField: r1 = r0->field_b
    //     0xb2069c: ldur            w1, [x0, #0xb]
    // 0xb206a0: DecompressPointer r1
    //     0xb206a0: add             x1, x1, HEAP, lsl #32
    // 0xb206a4: mov             x4, x1
    // 0xb206a8: mov             x0, x4
    // 0xb206ac: stur            x4, [fp, #-8]
    // 0xb206b0: r2 = Null
    //     0xb206b0: mov             x2, NULL
    // 0xb206b4: r1 = Null
    //     0xb206b4: mov             x1, NULL
    // 0xb206b8: r4 = LoadClassIdInstr(r0)
    //     0xb206b8: ldur            x4, [x0, #-1]
    //     0xb206bc: ubfx            x4, x4, #0xc, #0x14
    // 0xb206c0: sub             x4, x4, #0x5e
    // 0xb206c4: cmp             x4, #1
    // 0xb206c8: b.ls            #0xb206dc
    // 0xb206cc: r8 = String
    //     0xb206cc: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb206d0: r3 = Null
    //     0xb206d0: add             x3, PP, #0x55, lsl #12  ; [pp+0x55250] Null
    //     0xb206d4: ldr             x3, [x3, #0x250]
    // 0xb206d8: r0 = String()
    //     0xb206d8: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb206dc: r1 = Function '<anonymous closure>':.
    //     0xb206dc: add             x1, PP, #0x55, lsl #12  ; [pp+0x55260] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb206e0: ldr             x1, [x1, #0x260]
    // 0xb206e4: r2 = Null
    //     0xb206e4: mov             x2, NULL
    // 0xb206e8: r0 = AllocateClosure()
    //     0xb206e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb206ec: r1 = Function '<anonymous closure>':.
    //     0xb206ec: add             x1, PP, #0x55, lsl #12  ; [pp+0x55268] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb206f0: ldr             x1, [x1, #0x268]
    // 0xb206f4: r2 = Null
    //     0xb206f4: mov             x2, NULL
    // 0xb206f8: stur            x0, [fp, #-0x18]
    // 0xb206fc: r0 = AllocateClosure()
    //     0xb206fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb20700: stur            x0, [fp, #-0x20]
    // 0xb20704: r0 = CachedNetworkImage()
    //     0xb20704: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb20708: stur            x0, [fp, #-0x28]
    // 0xb2070c: r16 = 64.000000
    //     0xb2070c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb20710: ldr             x16, [x16, #0x838]
    // 0xb20714: r30 = 64.000000
    //     0xb20714: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb20718: ldr             lr, [lr, #0x838]
    // 0xb2071c: stp             lr, x16, [SP, #0x10]
    // 0xb20720: ldur            x16, [fp, #-0x18]
    // 0xb20724: ldur            lr, [fp, #-0x20]
    // 0xb20728: stp             lr, x16, [SP]
    // 0xb2072c: mov             x1, x0
    // 0xb20730: ldur            x2, [fp, #-8]
    // 0xb20734: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xb20734: add             x4, PP, #0x40, lsl #12  ; [pp+0x40388] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xb20738: ldr             x4, [x4, #0x388]
    // 0xb2073c: r0 = CachedNetworkImage()
    //     0xb2073c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb20740: r0 = GestureDetector()
    //     0xb20740: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb20744: ldur            x2, [fp, #-0x10]
    // 0xb20748: r1 = Function '<anonymous closure>':.
    //     0xb20748: add             x1, PP, #0x55, lsl #12  ; [pp+0x55270] AnonymousClosure: (0xb20794), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::glassThemeSlider (0xb1ee04)
    //     0xb2074c: ldr             x1, [x1, #0x270]
    // 0xb20750: stur            x0, [fp, #-8]
    // 0xb20754: r0 = AllocateClosure()
    //     0xb20754: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb20758: ldur            x16, [fp, #-0x28]
    // 0xb2075c: stp             x16, x0, [SP]
    // 0xb20760: ldur            x1, [fp, #-8]
    // 0xb20764: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb20764: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb20768: ldr             x4, [x4, #0xaf0]
    // 0xb2076c: r0 = GestureDetector()
    //     0xb2076c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb20770: ldur            x0, [fp, #-8]
    // 0xb20774: LeaveFrame
    //     0xb20774: mov             SP, fp
    //     0xb20778: ldp             fp, lr, [SP], #0x10
    // 0xb2077c: ret
    //     0xb2077c: ret             
    // 0xb20780: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb20780: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb20784: b               #0xb205d4
    // 0xb20788: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb20788: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb2078c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb2078c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb20790: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb20790: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb20794, size: 0x9c
    // 0xb20794: EnterFrame
    //     0xb20794: stp             fp, lr, [SP, #-0x10]!
    //     0xb20798: mov             fp, SP
    // 0xb2079c: AllocStack(0x28)
    //     0xb2079c: sub             SP, SP, #0x28
    // 0xb207a0: SetupParameters()
    //     0xb207a0: ldr             x0, [fp, #0x10]
    //     0xb207a4: ldur            w2, [x0, #0x17]
    //     0xb207a8: add             x2, x2, HEAP, lsl #32
    //     0xb207ac: stur            x2, [fp, #-8]
    // 0xb207b0: CheckStackOverflow
    //     0xb207b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb207b4: cmp             SP, x16
    //     0xb207b8: b.ls            #0xb20828
    // 0xb207bc: LoadField: r1 = r2->field_f
    //     0xb207bc: ldur            w1, [x2, #0xf]
    // 0xb207c0: DecompressPointer r1
    //     0xb207c0: add             x1, x1, HEAP, lsl #32
    // 0xb207c4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb207c4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb207c8: r0 = of()
    //     0xb207c8: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xb207cc: ldur            x2, [fp, #-8]
    // 0xb207d0: r1 = Function '<anonymous closure>':.
    //     0xb207d0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55278] AnonymousClosure: (0xb20830), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::glassThemeSlider (0xb1ee04)
    //     0xb207d4: ldr             x1, [x1, #0x278]
    // 0xb207d8: stur            x0, [fp, #-8]
    // 0xb207dc: r0 = AllocateClosure()
    //     0xb207dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb207e0: r1 = Null
    //     0xb207e0: mov             x1, NULL
    // 0xb207e4: stur            x0, [fp, #-0x10]
    // 0xb207e8: r0 = MaterialPageRoute()
    //     0xb207e8: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xb207ec: mov             x1, x0
    // 0xb207f0: ldur            x2, [fp, #-0x10]
    // 0xb207f4: stur            x0, [fp, #-0x10]
    // 0xb207f8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb207f8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb207fc: r0 = MaterialPageRoute()
    //     0xb207fc: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xb20800: ldur            x16, [fp, #-8]
    // 0xb20804: stp             x16, NULL, [SP, #8]
    // 0xb20808: ldur            x16, [fp, #-0x10]
    // 0xb2080c: str             x16, [SP]
    // 0xb20810: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb20810: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb20814: r0 = push()
    //     0xb20814: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xb20818: r0 = Null
    //     0xb20818: mov             x0, NULL
    // 0xb2081c: LeaveFrame
    //     0xb2081c: mov             SP, fp
    //     0xb20820: ldp             fp, lr, [SP], #0x10
    // 0xb20824: ret
    //     0xb20824: ret             
    // 0xb20828: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb20828: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2082c: b               #0xb207bc
  }
  [closure] TestimonialMoreImagesWidget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb20830, size: 0xb0
    // 0xb20830: EnterFrame
    //     0xb20830: stp             fp, lr, [SP, #-0x10]!
    //     0xb20834: mov             fp, SP
    // 0xb20838: AllocStack(0x20)
    //     0xb20838: sub             SP, SP, #0x20
    // 0xb2083c: SetupParameters()
    //     0xb2083c: ldr             x0, [fp, #0x18]
    //     0xb20840: ldur            w1, [x0, #0x17]
    //     0xb20844: add             x1, x1, HEAP, lsl #32
    // 0xb20848: CheckStackOverflow
    //     0xb20848: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2084c: cmp             SP, x16
    //     0xb20850: b.ls            #0xb208d4
    // 0xb20854: LoadField: r0 = r1->field_13
    //     0xb20854: ldur            w0, [x1, #0x13]
    // 0xb20858: DecompressPointer r0
    //     0xb20858: add             x0, x0, HEAP, lsl #32
    // 0xb2085c: stur            x0, [fp, #-8]
    // 0xb20860: LoadField: r2 = r1->field_b
    //     0xb20860: ldur            w2, [x1, #0xb]
    // 0xb20864: DecompressPointer r2
    //     0xb20864: add             x2, x2, HEAP, lsl #32
    // 0xb20868: LoadField: r1 = r2->field_f
    //     0xb20868: ldur            w1, [x2, #0xf]
    // 0xb2086c: DecompressPointer r1
    //     0xb2086c: add             x1, x1, HEAP, lsl #32
    // 0xb20870: LoadField: r3 = r2->field_13
    //     0xb20870: ldur            w3, [x2, #0x13]
    // 0xb20874: DecompressPointer r3
    //     0xb20874: add             x3, x3, HEAP, lsl #32
    // 0xb20878: stp             x3, x1, [SP]
    // 0xb2087c: r4 = 0
    //     0xb2087c: movz            x4, #0
    // 0xb20880: ldr             x0, [SP, #8]
    // 0xb20884: r16 = UnlinkedCall_0x613b5c
    //     0xb20884: add             x16, PP, #0x55, lsl #12  ; [pp+0x55280] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb20888: add             x16, x16, #0x280
    // 0xb2088c: ldp             x5, lr, [x16]
    // 0xb20890: blr             lr
    // 0xb20894: cmp             w0, NULL
    // 0xb20898: b.eq            #0xb208dc
    // 0xb2089c: LoadField: r1 = r0->field_b7
    //     0xb2089c: ldur            w1, [x0, #0xb7]
    // 0xb208a0: DecompressPointer r1
    //     0xb208a0: add             x1, x1, HEAP, lsl #32
    // 0xb208a4: stur            x1, [fp, #-0x10]
    // 0xb208a8: r0 = TestimonialMoreImagesWidget()
    //     0xb208a8: bl              #0xb20434  ; AllocateTestimonialMoreImagesWidgetStub -> TestimonialMoreImagesWidget (size=0x1c)
    // 0xb208ac: ldur            x1, [fp, #-0x10]
    // 0xb208b0: StoreField: r0->field_b = r1
    //     0xb208b0: stur            w1, [x0, #0xb]
    // 0xb208b4: ldur            x1, [fp, #-8]
    // 0xb208b8: r2 = LoadInt32Instr(r1)
    //     0xb208b8: sbfx            x2, x1, #1, #0x1f
    //     0xb208bc: tbz             w1, #0, #0xb208c4
    //     0xb208c0: ldur            x2, [x1, #7]
    // 0xb208c4: StoreField: r0->field_f = r2
    //     0xb208c4: stur            x2, [x0, #0xf]
    // 0xb208c8: LeaveFrame
    //     0xb208c8: mov             SP, fp
    //     0xb208cc: ldp             fp, lr, [SP], #0x10
    // 0xb208d0: ret
    //     0xb208d0: ret             
    // 0xb208d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb208d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb208d8: b               #0xb20854
    // 0xb208dc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb208dc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb943d8, size: 0x64
    // 0xb943d8: EnterFrame
    //     0xb943d8: stp             fp, lr, [SP, #-0x10]!
    //     0xb943dc: mov             fp, SP
    // 0xb943e0: AllocStack(0x18)
    //     0xb943e0: sub             SP, SP, #0x18
    // 0xb943e4: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb943e4: stur            x1, [fp, #-8]
    //     0xb943e8: stur            x2, [fp, #-0x10]
    // 0xb943ec: r1 = 2
    //     0xb943ec: movz            x1, #0x2
    // 0xb943f0: r0 = AllocateContext()
    //     0xb943f0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb943f4: mov             x1, x0
    // 0xb943f8: ldur            x0, [fp, #-8]
    // 0xb943fc: stur            x1, [fp, #-0x18]
    // 0xb94400: StoreField: r1->field_f = r0
    //     0xb94400: stur            w0, [x1, #0xf]
    // 0xb94404: ldur            x0, [fp, #-0x10]
    // 0xb94408: StoreField: r1->field_13 = r0
    //     0xb94408: stur            w0, [x1, #0x13]
    // 0xb9440c: r0 = Obx()
    //     0xb9440c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb94410: ldur            x2, [fp, #-0x18]
    // 0xb94414: r1 = Function '<anonymous closure>':.
    //     0xb94414: add             x1, PP, #0x55, lsl #12  ; [pp+0x55070] AnonymousClosure: (0xb1e200), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xb943d8)
    //     0xb94418: ldr             x1, [x1, #0x70]
    // 0xb9441c: stur            x0, [fp, #-8]
    // 0xb94420: r0 = AllocateClosure()
    //     0xb94420: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb94424: mov             x1, x0
    // 0xb94428: ldur            x0, [fp, #-8]
    // 0xb9442c: StoreField: r0->field_b = r1
    //     0xb9442c: stur            w1, [x0, #0xb]
    // 0xb94430: LeaveFrame
    //     0xb94430: mov             SP, fp
    //     0xb94434: ldp             fp, lr, [SP], #0x10
    // 0xb94438: ret
    //     0xb94438: ret             
  }
}

// class id: 4045, size: 0x2c, field offset: 0xc
//   const constructor, 
class TestimonialCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7fbc8, size: 0x48
    // 0xc7fbc8: EnterFrame
    //     0xc7fbc8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7fbcc: mov             fp, SP
    // 0xc7fbd0: AllocStack(0x8)
    //     0xc7fbd0: sub             SP, SP, #8
    // 0xc7fbd4: CheckStackOverflow
    //     0xc7fbd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7fbd8: cmp             SP, x16
    //     0xc7fbdc: b.ls            #0xc7fc08
    // 0xc7fbe0: r1 = <TestimonialCarousel>
    //     0xc7fbe0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48720] TypeArguments: <TestimonialCarousel>
    //     0xc7fbe4: ldr             x1, [x1, #0x720]
    // 0xc7fbe8: r0 = _TestimonialCarouselState()
    //     0xc7fbe8: bl              #0xc7fc10  ; Allocate_TestimonialCarouselStateStub -> _TestimonialCarouselState (size=0x24)
    // 0xc7fbec: mov             x1, x0
    // 0xc7fbf0: stur            x0, [fp, #-8]
    // 0xc7fbf4: r0 = _TestimonialCarouselState()
    //     0xc7fbf4: bl              #0xc7c770  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::_TestimonialCarouselState
    // 0xc7fbf8: ldur            x0, [fp, #-8]
    // 0xc7fbfc: LeaveFrame
    //     0xc7fbfc: mov             SP, fp
    //     0xc7fc00: ldp             fp, lr, [SP], #0x10
    // 0xc7fc04: ret
    //     0xc7fc04: ret             
    // 0xc7fc08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7fc08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7fc0c: b               #0xc7fbe0
  }
}
