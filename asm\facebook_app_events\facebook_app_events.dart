// lib: , url: package:facebook_app_events/facebook_app_events.dart

// class id: 1049635, size: 0x8
class :: {
}

// class id: 4954, size: 0x8, field offset: 0x8
class FacebookAppEvents extends Object {

  Future<void> logAddToCart(FacebookAppEvents, {Map<String, dynamic>? content, required String id, required String type, required String currency, required double price}) {
    // ** addr: 0x8a2ce0, size: 0x21c
    // 0x8a2ce0: EnterFrame
    //     0x8a2ce0: stp             fp, lr, [SP, #-0x10]!
    //     0x8a2ce4: mov             fp, SP
    // 0x8a2ce8: AllocStack(0x40)
    //     0x8a2ce8: sub             SP, SP, #0x40
    // 0x8a2cec: SetupParameters(FacebookAppEvents this /* r1 => r0, fp-0x28 */, {dynamic content = Null /* r3, fp-0x20 */, dynamic required /* r5, fp-0x18 */, dynamic required /* r8, fp-0x10 */, dynamic required /* r4, fp-0x8 */})
    //     0x8a2cec: mov             x0, x1
    //     0x8a2cf0: stur            x1, [fp, #-0x28]
    //     0x8a2cf4: ldur            w1, [x4, #0x13]
    //     0x8a2cf8: ldur            w2, [x4, #0x1f]
    //     0x8a2cfc: add             x2, x2, HEAP, lsl #32
    //     0x8a2d00: add             x16, PP, #0x23, lsl #12  ; [pp+0x23d28] "content"
    //     0x8a2d04: ldr             x16, [x16, #0xd28]
    //     0x8a2d08: cmp             w2, w16
    //     0x8a2d0c: b.ne            #0x8a2d30
    //     0x8a2d10: ldur            w2, [x4, #0x23]
    //     0x8a2d14: add             x2, x2, HEAP, lsl #32
    //     0x8a2d18: sub             w3, w1, w2
    //     0x8a2d1c: add             x2, fp, w3, sxtw #2
    //     0x8a2d20: ldr             x2, [x2, #8]
    //     0x8a2d24: mov             x3, x2
    //     0x8a2d28: movz            x2, #0x1
    //     0x8a2d2c: b               #0x8a2d38
    //     0x8a2d30: mov             x3, NULL
    //     0x8a2d34: movz            x2, #0
    //     0x8a2d38: stur            x3, [fp, #-0x20]
    //     0x8a2d3c: lsl             x5, x2, #1
    //     0x8a2d40: add             w2, w5, #2
    //     0x8a2d44: lsl             w5, w2, #1
    //     0x8a2d48: add             w6, w5, #0xa
    //     0x8a2d4c: add             x16, x4, w6, sxtw #1
    //     0x8a2d50: ldur            w5, [x16, #0xf]
    //     0x8a2d54: add             x5, x5, HEAP, lsl #32
    //     0x8a2d58: sub             w6, w1, w5
    //     0x8a2d5c: add             x5, fp, w6, sxtw #2
    //     0x8a2d60: ldr             x5, [x5, #8]
    //     0x8a2d64: stur            x5, [fp, #-0x18]
    //     0x8a2d68: add             w6, w2, #2
    //     0x8a2d6c: lsl             w2, w6, #1
    //     0x8a2d70: add             w7, w2, #0xa
    //     0x8a2d74: add             x16, x4, w7, sxtw #1
    //     0x8a2d78: ldur            w2, [x16, #0xf]
    //     0x8a2d7c: add             x2, x2, HEAP, lsl #32
    //     0x8a2d80: sub             w7, w1, w2
    //     0x8a2d84: add             x8, fp, w7, sxtw #2
    //     0x8a2d88: ldr             x8, [x8, #8]
    //     0x8a2d8c: stur            x8, [fp, #-0x10]
    //     0x8a2d90: add             w2, w6, #2
    //     0x8a2d94: lsl             w6, w2, #1
    //     0x8a2d98: add             w2, w6, #0xa
    //     0x8a2d9c: add             x16, x4, w2, sxtw #1
    //     0x8a2da0: ldur            w6, [x16, #0xf]
    //     0x8a2da4: add             x6, x6, HEAP, lsl #32
    //     0x8a2da8: sub             w2, w1, w6
    //     0x8a2dac: add             x4, fp, w2, sxtw #2
    //     0x8a2db0: ldr             x4, [x4, #8]
    //     0x8a2db4: stur            x4, [fp, #-8]
    // 0x8a2db8: CheckStackOverflow
    //     0x8a2db8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a2dbc: cmp             SP, x16
    //     0x8a2dc0: b.ls            #0x8a2ef4
    // 0x8a2dc4: r1 = Null
    //     0x8a2dc4: mov             x1, NULL
    // 0x8a2dc8: r2 = 16
    //     0x8a2dc8: movz            x2, #0x10
    // 0x8a2dcc: r0 = AllocateArray()
    //     0x8a2dcc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8a2dd0: stur            x0, [fp, #-0x30]
    // 0x8a2dd4: r16 = "fb_content"
    //     0x8a2dd4: add             x16, PP, #0x30, lsl #12  ; [pp+0x304a0] "fb_content"
    //     0x8a2dd8: ldr             x16, [x16, #0x4a0]
    // 0x8a2ddc: StoreField: r0->field_f = r16
    //     0x8a2ddc: stur            w16, [x0, #0xf]
    // 0x8a2de0: ldur            x2, [fp, #-0x20]
    // 0x8a2de4: cmp             w2, NULL
    // 0x8a2de8: b.eq            #0x8a2e00
    // 0x8a2dec: r1 = Instance_JsonCodec
    //     0x8a2dec: add             x1, PP, #8, lsl #12  ; [pp+0x8ea0] Obj!JsonCodec@d6ed71
    //     0x8a2df0: ldr             x1, [x1, #0xea0]
    // 0x8a2df4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8a2df4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8a2df8: r0 = encode()
    //     0x8a2df8: bl              #0x16375b0  ; [dart:convert] JsonCodec::encode
    // 0x8a2dfc: b               #0x8a2e04
    // 0x8a2e00: r0 = Null
    //     0x8a2e00: mov             x0, NULL
    // 0x8a2e04: ldur            x3, [fp, #-0x10]
    // 0x8a2e08: ldur            x2, [fp, #-0x30]
    // 0x8a2e0c: mov             x1, x2
    // 0x8a2e10: ArrayStore: r1[1] = r0  ; List_4
    //     0x8a2e10: add             x25, x1, #0x13
    //     0x8a2e14: str             w0, [x25]
    //     0x8a2e18: tbz             w0, #0, #0x8a2e34
    //     0x8a2e1c: ldurb           w16, [x1, #-1]
    //     0x8a2e20: ldurb           w17, [x0, #-1]
    //     0x8a2e24: and             x16, x17, x16, lsr #2
    //     0x8a2e28: tst             x16, HEAP, lsr #32
    //     0x8a2e2c: b.eq            #0x8a2e34
    //     0x8a2e30: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x8a2e34: r16 = "fb_content_id"
    //     0x8a2e34: add             x16, PP, #0x30, lsl #12  ; [pp+0x304a8] "fb_content_id"
    //     0x8a2e38: ldr             x16, [x16, #0x4a8]
    // 0x8a2e3c: ArrayStore: r2[0] = r16  ; List_4
    //     0x8a2e3c: stur            w16, [x2, #0x17]
    // 0x8a2e40: mov             x1, x2
    // 0x8a2e44: ldur            x0, [fp, #-0x18]
    // 0x8a2e48: ArrayStore: r1[3] = r0  ; List_4
    //     0x8a2e48: add             x25, x1, #0x1b
    //     0x8a2e4c: str             w0, [x25]
    //     0x8a2e50: tbz             w0, #0, #0x8a2e6c
    //     0x8a2e54: ldurb           w16, [x1, #-1]
    //     0x8a2e58: ldurb           w17, [x0, #-1]
    //     0x8a2e5c: and             x16, x17, x16, lsr #2
    //     0x8a2e60: tst             x16, HEAP, lsr #32
    //     0x8a2e64: b.eq            #0x8a2e6c
    //     0x8a2e68: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x8a2e6c: r16 = "fb_content_type"
    //     0x8a2e6c: add             x16, PP, #0x30, lsl #12  ; [pp+0x304b0] "fb_content_type"
    //     0x8a2e70: ldr             x16, [x16, #0x4b0]
    // 0x8a2e74: StoreField: r2->field_1f = r16
    //     0x8a2e74: stur            w16, [x2, #0x1f]
    // 0x8a2e78: mov             x1, x2
    // 0x8a2e7c: ldur            x0, [fp, #-8]
    // 0x8a2e80: ArrayStore: r1[5] = r0  ; List_4
    //     0x8a2e80: add             x25, x1, #0x23
    //     0x8a2e84: str             w0, [x25]
    //     0x8a2e88: tbz             w0, #0, #0x8a2ea4
    //     0x8a2e8c: ldurb           w16, [x1, #-1]
    //     0x8a2e90: ldurb           w17, [x0, #-1]
    //     0x8a2e94: and             x16, x17, x16, lsr #2
    //     0x8a2e98: tst             x16, HEAP, lsr #32
    //     0x8a2e9c: b.eq            #0x8a2ea4
    //     0x8a2ea0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x8a2ea4: r16 = "fb_currency"
    //     0x8a2ea4: add             x16, PP, #0x30, lsl #12  ; [pp+0x304b8] "fb_currency"
    //     0x8a2ea8: ldr             x16, [x16, #0x4b8]
    // 0x8a2eac: StoreField: r2->field_27 = r16
    //     0x8a2eac: stur            w16, [x2, #0x27]
    // 0x8a2eb0: r16 = "INR"
    //     0x8a2eb0: add             x16, PP, #0x30, lsl #12  ; [pp+0x304c0] "INR"
    //     0x8a2eb4: ldr             x16, [x16, #0x4c0]
    // 0x8a2eb8: StoreField: r2->field_2b = r16
    //     0x8a2eb8: stur            w16, [x2, #0x2b]
    // 0x8a2ebc: r16 = <String, dynamic>
    //     0x8a2ebc: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x8a2ec0: stp             x2, x16, [SP]
    // 0x8a2ec4: r0 = Map._fromLiteral()
    //     0x8a2ec4: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x8a2ec8: mov             x1, x0
    // 0x8a2ecc: ldur            x0, [fp, #-0x10]
    // 0x8a2ed0: LoadField: d0 = r0->field_7
    //     0x8a2ed0: ldur            d0, [x0, #7]
    // 0x8a2ed4: mov             x3, x1
    // 0x8a2ed8: ldur            x1, [fp, #-0x28]
    // 0x8a2edc: r2 = "fb_mobile_add_to_cart"
    //     0x8a2edc: add             x2, PP, #0x32, lsl #12  ; [pp+0x32320] "fb_mobile_add_to_cart"
    //     0x8a2ee0: ldr             x2, [x2, #0x320]
    // 0x8a2ee4: r0 = logEvent()
    //     0x8a2ee4: bl              #0x8a2efc  ; [package:facebook_app_events/facebook_app_events.dart] FacebookAppEvents::logEvent
    // 0x8a2ee8: LeaveFrame
    //     0x8a2ee8: mov             SP, fp
    //     0x8a2eec: ldp             fp, lr, [SP], #0x10
    // 0x8a2ef0: ret
    //     0x8a2ef0: ret             
    // 0x8a2ef4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a2ef4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a2ef8: b               #0x8a2dc4
  }
  _ logEvent(/* No info */) {
    // ** addr: 0x8a2efc, size: 0x11c
    // 0x8a2efc: EnterFrame
    //     0x8a2efc: stp             fp, lr, [SP, #-0x10]!
    //     0x8a2f00: mov             fp, SP
    // 0x8a2f04: AllocStack(0x40)
    //     0x8a2f04: sub             SP, SP, #0x40
    // 0x8a2f08: SetupParameters(FacebookAppEvents this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */, dynamic _ /* d0 => d0, fp-0x20 */)
    //     0x8a2f08: mov             x4, x1
    //     0x8a2f0c: mov             x0, x3
    //     0x8a2f10: stur            x3, [fp, #-0x18]
    //     0x8a2f14: mov             x3, x2
    //     0x8a2f18: stur            x1, [fp, #-8]
    //     0x8a2f1c: stur            x2, [fp, #-0x10]
    //     0x8a2f20: stur            d0, [fp, #-0x20]
    // 0x8a2f24: CheckStackOverflow
    //     0x8a2f24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a2f28: cmp             SP, x16
    //     0x8a2f2c: b.ls            #0x8a2ff4
    // 0x8a2f30: r1 = Null
    //     0x8a2f30: mov             x1, NULL
    // 0x8a2f34: r2 = 8
    //     0x8a2f34: movz            x2, #0x8
    // 0x8a2f38: r0 = AllocateArray()
    //     0x8a2f38: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8a2f3c: r16 = "name"
    //     0x8a2f3c: ldr             x16, [PP, #0x77a8]  ; [pp+0x77a8] "name"
    // 0x8a2f40: StoreField: r0->field_f = r16
    //     0x8a2f40: stur            w16, [x0, #0xf]
    // 0x8a2f44: ldur            x1, [fp, #-0x10]
    // 0x8a2f48: StoreField: r0->field_13 = r1
    //     0x8a2f48: stur            w1, [x0, #0x13]
    // 0x8a2f4c: r16 = "_valueToSum"
    //     0x8a2f4c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30580] "_valueToSum"
    //     0x8a2f50: ldr             x16, [x16, #0x580]
    // 0x8a2f54: ArrayStore: r0[0] = r16  ; List_4
    //     0x8a2f54: stur            w16, [x0, #0x17]
    // 0x8a2f58: ldur            d0, [fp, #-0x20]
    // 0x8a2f5c: r1 = inline_Allocate_Double()
    //     0x8a2f5c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x8a2f60: add             x1, x1, #0x10
    //     0x8a2f64: cmp             x2, x1
    //     0x8a2f68: b.ls            #0x8a2ffc
    //     0x8a2f6c: str             x1, [THR, #0x50]  ; THR::top
    //     0x8a2f70: sub             x1, x1, #0xf
    //     0x8a2f74: movz            x2, #0xe15c
    //     0x8a2f78: movk            x2, #0x3, lsl #16
    //     0x8a2f7c: stur            x2, [x1, #-1]
    // 0x8a2f80: StoreField: r1->field_7 = d0
    //     0x8a2f80: stur            d0, [x1, #7]
    // 0x8a2f84: StoreField: r0->field_1b = r1
    //     0x8a2f84: stur            w1, [x0, #0x1b]
    // 0x8a2f88: r16 = <String, dynamic>
    //     0x8a2f88: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x8a2f8c: stp             x0, x16, [SP]
    // 0x8a2f90: r0 = Map._fromLiteral()
    //     0x8a2f90: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x8a2f94: ldur            x1, [fp, #-8]
    // 0x8a2f98: ldur            x2, [fp, #-0x18]
    // 0x8a2f9c: stur            x0, [fp, #-0x10]
    // 0x8a2fa0: r0 = _filterOutNulls()
    //     0x8a2fa0: bl              #0x8a3018  ; [package:facebook_app_events/facebook_app_events.dart] FacebookAppEvents::_filterOutNulls
    // 0x8a2fa4: ldur            x1, [fp, #-0x10]
    // 0x8a2fa8: mov             x3, x0
    // 0x8a2fac: r2 = "parameters"
    //     0x8a2fac: add             x2, PP, #0xb, lsl #12  ; [pp+0xba68] "parameters"
    //     0x8a2fb0: ldr             x2, [x2, #0xa68]
    // 0x8a2fb4: r0 = []=()
    //     0x8a2fb4: bl              #0x1699264  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x8a2fb8: ldur            x1, [fp, #-8]
    // 0x8a2fbc: ldur            x2, [fp, #-0x10]
    // 0x8a2fc0: r0 = _filterOutNulls()
    //     0x8a2fc0: bl              #0x8a3018  ; [package:facebook_app_events/facebook_app_events.dart] FacebookAppEvents::_filterOutNulls
    // 0x8a2fc4: r16 = <void?>
    //     0x8a2fc4: ldr             x16, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0x8a2fc8: r30 = Instance_MethodChannel
    //     0x8a2fc8: add             lr, PP, #0x30, lsl #12  ; [pp+0x30588] Obj!MethodChannel@d55f21
    //     0x8a2fcc: ldr             lr, [lr, #0x588]
    // 0x8a2fd0: stp             lr, x16, [SP, #0x10]
    // 0x8a2fd4: r16 = "logEvent"
    //     0x8a2fd4: add             x16, PP, #0x30, lsl #12  ; [pp+0x30590] "logEvent"
    //     0x8a2fd8: ldr             x16, [x16, #0x590]
    // 0x8a2fdc: stp             x0, x16, [SP]
    // 0x8a2fe0: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8a2fe0: ldr             x4, [PP, #0x770]  ; [pp+0x770] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8a2fe4: r0 = invokeMethod()
    //     0x8a2fe4: bl              #0x16c98f8  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x8a2fe8: LeaveFrame
    //     0x8a2fe8: mov             SP, fp
    //     0x8a2fec: ldp             fp, lr, [SP], #0x10
    // 0x8a2ff0: ret
    //     0x8a2ff0: ret             
    // 0x8a2ff4: r0 = StackOverflowSharedWithFPURegs()
    //     0x8a2ff4: bl              #0x16f7320  ; StackOverflowSharedWithFPURegsStub
    // 0x8a2ff8: b               #0x8a2f30
    // 0x8a2ffc: SaveReg d0
    //     0x8a2ffc: str             q0, [SP, #-0x10]!
    // 0x8a3000: SaveReg r0
    //     0x8a3000: str             x0, [SP, #-8]!
    // 0x8a3004: r0 = AllocateDouble()
    //     0x8a3004: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x8a3008: mov             x1, x0
    // 0x8a300c: RestoreReg r0
    //     0x8a300c: ldr             x0, [SP], #8
    // 0x8a3010: RestoreReg d0
    //     0x8a3010: ldr             q0, [SP], #0x10
    // 0x8a3014: b               #0x8a2f80
  }
  _ _filterOutNulls(/* No info */) {
    // ** addr: 0x8a3018, size: 0x80
    // 0x8a3018: EnterFrame
    //     0x8a3018: stp             fp, lr, [SP, #-0x10]!
    //     0x8a301c: mov             fp, SP
    // 0x8a3020: AllocStack(0x20)
    //     0x8a3020: sub             SP, SP, #0x20
    // 0x8a3024: SetupParameters(FacebookAppEvents this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0x8a3024: mov             x0, x1
    //     0x8a3028: mov             x1, x2
    //     0x8a302c: stur            x2, [fp, #-8]
    // 0x8a3030: CheckStackOverflow
    //     0x8a3030: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a3034: cmp             SP, x16
    //     0x8a3038: b.ls            #0x8a3090
    // 0x8a303c: r16 = <String, dynamic>
    //     0x8a303c: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x8a3040: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8a3044: stp             lr, x16, [SP]
    // 0x8a3048: r0 = Map._fromLiteral()
    //     0x8a3048: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x8a304c: stur            x0, [fp, #-0x10]
    // 0x8a3050: r1 = 1
    //     0x8a3050: movz            x1, #0x1
    // 0x8a3054: r0 = AllocateContext()
    //     0x8a3054: bl              #0x16f6108  ; AllocateContextStub
    // 0x8a3058: mov             x1, x0
    // 0x8a305c: ldur            x0, [fp, #-0x10]
    // 0x8a3060: StoreField: r1->field_f = r0
    //     0x8a3060: stur            w0, [x1, #0xf]
    // 0x8a3064: mov             x2, x1
    // 0x8a3068: r1 = Function '<anonymous closure>':.
    //     0x8a3068: add             x1, PP, #0x30, lsl #12  ; [pp+0x30598] AnonymousClosure: (0x8a3098), in [package:facebook_app_events/facebook_app_events.dart] FacebookAppEvents::_filterOutNulls (0x8a3018)
    //     0x8a306c: ldr             x1, [x1, #0x598]
    // 0x8a3070: r0 = AllocateClosure()
    //     0x8a3070: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8a3074: ldur            x1, [fp, #-8]
    // 0x8a3078: mov             x2, x0
    // 0x8a307c: r0 = forEach()
    //     0x8a307c: bl              #0x16878f8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0x8a3080: ldur            x0, [fp, #-0x10]
    // 0x8a3084: LeaveFrame
    //     0x8a3084: mov             SP, fp
    //     0x8a3088: ldp             fp, lr, [SP], #0x10
    // 0x8a308c: ret
    //     0x8a308c: ret             
    // 0x8a3090: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a3090: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a3094: b               #0x8a303c
  }
  [closure] void <anonymous closure>(dynamic, String, dynamic) {
    // ** addr: 0x8a3098, size: 0x58
    // 0x8a3098: EnterFrame
    //     0x8a3098: stp             fp, lr, [SP, #-0x10]!
    //     0x8a309c: mov             fp, SP
    // 0x8a30a0: ldr             x0, [fp, #0x20]
    // 0x8a30a4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8a30a4: ldur            w1, [x0, #0x17]
    // 0x8a30a8: DecompressPointer r1
    //     0x8a30a8: add             x1, x1, HEAP, lsl #32
    // 0x8a30ac: CheckStackOverflow
    //     0x8a30ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a30b0: cmp             SP, x16
    //     0x8a30b4: b.ls            #0x8a30e8
    // 0x8a30b8: ldr             x3, [fp, #0x10]
    // 0x8a30bc: cmp             w3, NULL
    // 0x8a30c0: b.eq            #0x8a30d8
    // 0x8a30c4: LoadField: r0 = r1->field_f
    //     0x8a30c4: ldur            w0, [x1, #0xf]
    // 0x8a30c8: DecompressPointer r0
    //     0x8a30c8: add             x0, x0, HEAP, lsl #32
    // 0x8a30cc: mov             x1, x0
    // 0x8a30d0: ldr             x2, [fp, #0x18]
    // 0x8a30d4: r0 = []=()
    //     0x8a30d4: bl              #0x1699264  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x8a30d8: r0 = Null
    //     0x8a30d8: mov             x0, NULL
    // 0x8a30dc: LeaveFrame
    //     0x8a30dc: mov             SP, fp
    //     0x8a30e0: ldp             fp, lr, [SP], #0x10
    // 0x8a30e4: ret
    //     0x8a30e4: ret             
    // 0x8a30e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a30e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a30ec: b               #0x8a30b8
  }
  _ logPurchase(/* No info */) {
    // ** addr: 0x15bb98c, size: 0x10c
    // 0x15bb98c: EnterFrame
    //     0x15bb98c: stp             fp, lr, [SP, #-0x10]!
    //     0x15bb990: mov             fp, SP
    // 0x15bb994: AllocStack(0x38)
    //     0x15bb994: sub             SP, SP, #0x38
    // 0x15bb998: SetupParameters(FacebookAppEvents this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x18 */)
    //     0x15bb998: mov             x3, x1
    //     0x15bb99c: mov             x0, x2
    //     0x15bb9a0: stur            x1, [fp, #-8]
    //     0x15bb9a4: stur            x2, [fp, #-0x10]
    //     0x15bb9a8: stur            d0, [fp, #-0x18]
    // 0x15bb9ac: CheckStackOverflow
    //     0x15bb9ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15bb9b0: cmp             SP, x16
    //     0x15bb9b4: b.ls            #0x15bba74
    // 0x15bb9b8: r1 = Null
    //     0x15bb9b8: mov             x1, NULL
    // 0x15bb9bc: r2 = 12
    //     0x15bb9bc: movz            x2, #0xc
    // 0x15bb9c0: r0 = AllocateArray()
    //     0x15bb9c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15bb9c4: r16 = "amount"
    //     0x15bb9c4: add             x16, PP, #0xe, lsl #12  ; [pp+0xea28] "amount"
    //     0x15bb9c8: ldr             x16, [x16, #0xa28]
    // 0x15bb9cc: StoreField: r0->field_f = r16
    //     0x15bb9cc: stur            w16, [x0, #0xf]
    // 0x15bb9d0: ldur            d0, [fp, #-0x18]
    // 0x15bb9d4: r1 = inline_Allocate_Double()
    //     0x15bb9d4: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x15bb9d8: add             x1, x1, #0x10
    //     0x15bb9dc: cmp             x2, x1
    //     0x15bb9e0: b.ls            #0x15bba7c
    //     0x15bb9e4: str             x1, [THR, #0x50]  ; THR::top
    //     0x15bb9e8: sub             x1, x1, #0xf
    //     0x15bb9ec: movz            x2, #0xe15c
    //     0x15bb9f0: movk            x2, #0x3, lsl #16
    //     0x15bb9f4: stur            x2, [x1, #-1]
    // 0x15bb9f8: StoreField: r1->field_7 = d0
    //     0x15bb9f8: stur            d0, [x1, #7]
    // 0x15bb9fc: StoreField: r0->field_13 = r1
    //     0x15bb9fc: stur            w1, [x0, #0x13]
    // 0x15bba00: r16 = "currency"
    //     0x15bba00: add             x16, PP, #0x39, lsl #12  ; [pp+0x398b8] "currency"
    //     0x15bba04: ldr             x16, [x16, #0x8b8]
    // 0x15bba08: ArrayStore: r0[0] = r16  ; List_4
    //     0x15bba08: stur            w16, [x0, #0x17]
    // 0x15bba0c: r16 = "INR"
    //     0x15bba0c: add             x16, PP, #0x30, lsl #12  ; [pp+0x304c0] "INR"
    //     0x15bba10: ldr             x16, [x16, #0x4c0]
    // 0x15bba14: StoreField: r0->field_1b = r16
    //     0x15bba14: stur            w16, [x0, #0x1b]
    // 0x15bba18: r16 = "parameters"
    //     0x15bba18: add             x16, PP, #0xb, lsl #12  ; [pp+0xba68] "parameters"
    //     0x15bba1c: ldr             x16, [x16, #0xa68]
    // 0x15bba20: StoreField: r0->field_1f = r16
    //     0x15bba20: stur            w16, [x0, #0x1f]
    // 0x15bba24: ldur            x1, [fp, #-0x10]
    // 0x15bba28: StoreField: r0->field_23 = r1
    //     0x15bba28: stur            w1, [x0, #0x23]
    // 0x15bba2c: r16 = <String, dynamic>
    //     0x15bba2c: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x15bba30: stp             x0, x16, [SP]
    // 0x15bba34: r0 = Map._fromLiteral()
    //     0x15bba34: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x15bba38: ldur            x1, [fp, #-8]
    // 0x15bba3c: mov             x2, x0
    // 0x15bba40: r0 = _filterOutNulls()
    //     0x15bba40: bl              #0x8a3018  ; [package:facebook_app_events/facebook_app_events.dart] FacebookAppEvents::_filterOutNulls
    // 0x15bba44: r16 = <void?>
    //     0x15bba44: ldr             x16, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0x15bba48: r30 = Instance_MethodChannel
    //     0x15bba48: add             lr, PP, #0x30, lsl #12  ; [pp+0x30588] Obj!MethodChannel@d55f21
    //     0x15bba4c: ldr             lr, [lr, #0x588]
    // 0x15bba50: stp             lr, x16, [SP, #0x10]
    // 0x15bba54: r16 = "logPurchase"
    //     0x15bba54: add             x16, PP, #0x49, lsl #12  ; [pp+0x498b0] "logPurchase"
    //     0x15bba58: ldr             x16, [x16, #0x8b0]
    // 0x15bba5c: stp             x0, x16, [SP]
    // 0x15bba60: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x15bba60: ldr             x4, [PP, #0x770]  ; [pp+0x770] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x15bba64: r0 = invokeMethod()
    //     0x15bba64: bl              #0x16c98f8  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x15bba68: LeaveFrame
    //     0x15bba68: mov             SP, fp
    //     0x15bba6c: ldp             fp, lr, [SP], #0x10
    // 0x15bba70: ret
    //     0x15bba70: ret             
    // 0x15bba74: r0 = StackOverflowSharedWithFPURegs()
    //     0x15bba74: bl              #0x16f7320  ; StackOverflowSharedWithFPURegsStub
    // 0x15bba78: b               #0x15bb9b8
    // 0x15bba7c: SaveReg d0
    //     0x15bba7c: str             q0, [SP, #-0x10]!
    // 0x15bba80: SaveReg r0
    //     0x15bba80: str             x0, [SP, #-8]!
    // 0x15bba84: r0 = AllocateDouble()
    //     0x15bba84: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x15bba88: mov             x1, x0
    // 0x15bba8c: RestoreReg r0
    //     0x15bba8c: ldr             x0, [SP], #8
    // 0x15bba90: RestoreReg d0
    //     0x15bba90: ldr             q0, [SP], #0x10
    // 0x15bba94: b               #0x15bb9f8
  }
}
