// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart

// class id: 1049567, size: 0x8
class :: {
}

// class id: 3216, size: 0x1c, field offset: 0x14
class _ReviewWidgetState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x94b1c8, size: 0x50
    // 0x94b1c8: LoadField: r2 = r1->field_b
    //     0x94b1c8: ldur            w2, [x1, #0xb]
    // 0x94b1cc: DecompressPointer r2
    //     0x94b1cc: add             x2, x2, HEAP, lsl #32
    // 0x94b1d0: cmp             w2, NULL
    // 0x94b1d4: b.eq            #0x94b20c
    // 0x94b1d8: LoadField: r0 = r2->field_13
    //     0x94b1d8: ldur            w0, [x2, #0x13]
    // 0x94b1dc: DecompressPointer r0
    //     0x94b1dc: add             x0, x0, HEAP, lsl #32
    // 0x94b1e0: ArrayStore: r1[0] = r0  ; List_4
    //     0x94b1e0: stur            w0, [x1, #0x17]
    //     0x94b1e4: ldurb           w16, [x1, #-1]
    //     0x94b1e8: ldurb           w17, [x0, #-1]
    //     0x94b1ec: and             x16, x17, x16, lsr #2
    //     0x94b1f0: tst             x16, HEAP, lsr #32
    //     0x94b1f4: b.eq            #0x94b204
    //     0x94b1f8: str             lr, [SP, #-8]!
    //     0x94b1fc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    //     0x94b200: ldr             lr, [SP], #8
    // 0x94b204: r0 = Null
    //     0x94b204: mov             x0, NULL
    // 0x94b208: ret
    //     0x94b208: ret             
    // 0x94b20c: EnterFrame
    //     0x94b20c: stp             fp, lr, [SP, #-0x10]!
    //     0x94b210: mov             fp, SP
    // 0x94b214: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94b214: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, TapDownDetails) {
    // ** addr: 0xa90680, size: 0x54
    // 0xa90680: ldr             x1, [SP, #8]
    // 0xa90684: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa90684: ldur            w2, [x1, #0x17]
    // 0xa90688: DecompressPointer r2
    //     0xa90688: add             x2, x2, HEAP, lsl #32
    // 0xa9068c: LoadField: r1 = r2->field_b
    //     0xa9068c: ldur            w1, [x2, #0xb]
    // 0xa90690: DecompressPointer r1
    //     0xa90690: add             x1, x1, HEAP, lsl #32
    // 0xa90694: LoadField: r2 = r1->field_f
    //     0xa90694: ldur            w2, [x1, #0xf]
    // 0xa90698: DecompressPointer r2
    //     0xa90698: add             x2, x2, HEAP, lsl #32
    // 0xa9069c: ldr             x1, [SP]
    // 0xa906a0: LoadField: r0 = r1->field_7
    //     0xa906a0: ldur            w0, [x1, #7]
    // 0xa906a4: DecompressPointer r0
    //     0xa906a4: add             x0, x0, HEAP, lsl #32
    // 0xa906a8: StoreField: r2->field_13 = r0
    //     0xa906a8: stur            w0, [x2, #0x13]
    //     0xa906ac: ldurb           w16, [x2, #-1]
    //     0xa906b0: ldurb           w17, [x0, #-1]
    //     0xa906b4: and             x16, x17, x16, lsr #2
    //     0xa906b8: tst             x16, HEAP, lsr #32
    //     0xa906bc: b.eq            #0xa906cc
    //     0xa906c0: str             lr, [SP, #-8]!
    //     0xa906c4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    //     0xa906c8: ldr             lr, [SP], #8
    // 0xa906cc: r0 = Null
    //     0xa906cc: mov             x0, NULL
    // 0xa906d0: ret
    //     0xa906d0: ret             
  }
  [closure] Column <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa906d4, size: 0xe18
    // 0xa906d4: EnterFrame
    //     0xa906d4: stp             fp, lr, [SP, #-0x10]!
    //     0xa906d8: mov             fp, SP
    // 0xa906dc: AllocStack(0x70)
    //     0xa906dc: sub             SP, SP, #0x70
    // 0xa906e0: SetupParameters()
    //     0xa906e0: ldr             x0, [fp, #0x20]
    //     0xa906e4: ldur            w1, [x0, #0x17]
    //     0xa906e8: add             x1, x1, HEAP, lsl #32
    //     0xa906ec: stur            x1, [fp, #-8]
    // 0xa906f0: CheckStackOverflow
    //     0xa906f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa906f4: cmp             SP, x16
    //     0xa906f8: b.ls            #0xa914d8
    // 0xa906fc: r1 = 2
    //     0xa906fc: movz            x1, #0x2
    // 0xa90700: r0 = AllocateContext()
    //     0xa90700: bl              #0x16f6108  ; AllocateContextStub
    // 0xa90704: mov             x2, x0
    // 0xa90708: ldur            x0, [fp, #-8]
    // 0xa9070c: stur            x2, [fp, #-0x10]
    // 0xa90710: StoreField: r2->field_b = r0
    //     0xa90710: stur            w0, [x2, #0xb]
    // 0xa90714: ldr             x1, [fp, #0x18]
    // 0xa90718: StoreField: r2->field_f = r1
    //     0xa90718: stur            w1, [x2, #0xf]
    // 0xa9071c: LoadField: r1 = r0->field_f
    //     0xa9071c: ldur            w1, [x0, #0xf]
    // 0xa90720: DecompressPointer r1
    //     0xa90720: add             x1, x1, HEAP, lsl #32
    // 0xa90724: LoadField: r0 = r1->field_b
    //     0xa90724: ldur            w0, [x1, #0xb]
    // 0xa90728: DecompressPointer r0
    //     0xa90728: add             x0, x0, HEAP, lsl #32
    // 0xa9072c: cmp             w0, NULL
    // 0xa90730: b.eq            #0xa914e0
    // 0xa90734: LoadField: r1 = r0->field_b
    //     0xa90734: ldur            w1, [x0, #0xb]
    // 0xa90738: DecompressPointer r1
    //     0xa90738: add             x1, x1, HEAP, lsl #32
    // 0xa9073c: cmp             w1, NULL
    // 0xa90740: b.ne            #0xa9074c
    // 0xa90744: r0 = Null
    //     0xa90744: mov             x0, NULL
    // 0xa90748: b               #0xa90790
    // 0xa9074c: ldr             x0, [fp, #0x10]
    // 0xa90750: LoadField: r3 = r1->field_13
    //     0xa90750: ldur            w3, [x1, #0x13]
    // 0xa90754: DecompressPointer r3
    //     0xa90754: add             x3, x3, HEAP, lsl #32
    // 0xa90758: LoadField: r1 = r3->field_b
    //     0xa90758: ldur            w1, [x3, #0xb]
    // 0xa9075c: r4 = LoadInt32Instr(r0)
    //     0xa9075c: sbfx            x4, x0, #1, #0x1f
    //     0xa90760: tbz             w0, #0, #0xa90768
    //     0xa90764: ldur            x4, [x0, #7]
    // 0xa90768: r0 = LoadInt32Instr(r1)
    //     0xa90768: sbfx            x0, x1, #1, #0x1f
    // 0xa9076c: mov             x1, x4
    // 0xa90770: cmp             x1, x0
    // 0xa90774: b.hs            #0xa914e4
    // 0xa90778: LoadField: r0 = r3->field_f
    //     0xa90778: ldur            w0, [x3, #0xf]
    // 0xa9077c: DecompressPointer r0
    //     0xa9077c: add             x0, x0, HEAP, lsl #32
    // 0xa90780: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa90780: add             x16, x0, x4, lsl #2
    //     0xa90784: ldur            w1, [x16, #0xf]
    // 0xa90788: DecompressPointer r1
    //     0xa90788: add             x1, x1, HEAP, lsl #32
    // 0xa9078c: mov             x0, x1
    // 0xa90790: stur            x0, [fp, #-8]
    // 0xa90794: StoreField: r2->field_13 = r0
    //     0xa90794: stur            w0, [x2, #0x13]
    // 0xa90798: r1 = Instance_Color
    //     0xa90798: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa9079c: d0 = 0.050000
    //     0xa9079c: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0xa907a0: r0 = withOpacity()
    //     0xa907a0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa907a4: stur            x0, [fp, #-0x18]
    // 0xa907a8: r0 = BoxDecoration()
    //     0xa907a8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa907ac: mov             x1, x0
    // 0xa907b0: ldur            x0, [fp, #-0x18]
    // 0xa907b4: stur            x1, [fp, #-0x20]
    // 0xa907b8: StoreField: r1->field_7 = r0
    //     0xa907b8: stur            w0, [x1, #7]
    // 0xa907bc: r0 = Instance_BoxShape
    //     0xa907bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xa907c0: ldr             x0, [x0, #0x970]
    // 0xa907c4: StoreField: r1->field_23 = r0
    //     0xa907c4: stur            w0, [x1, #0x23]
    // 0xa907c8: ldur            x0, [fp, #-8]
    // 0xa907cc: cmp             w0, NULL
    // 0xa907d0: b.ne            #0xa907dc
    // 0xa907d4: r0 = Null
    //     0xa907d4: mov             x0, NULL
    // 0xa907d8: b               #0xa90818
    // 0xa907dc: LoadField: r2 = r0->field_7
    //     0xa907dc: ldur            w2, [x0, #7]
    // 0xa907e0: DecompressPointer r2
    //     0xa907e0: add             x2, x2, HEAP, lsl #32
    // 0xa907e4: cmp             w2, NULL
    // 0xa907e8: b.ne            #0xa907f4
    // 0xa907ec: r0 = Null
    //     0xa907ec: mov             x0, NULL
    // 0xa907f0: b               #0xa90818
    // 0xa907f4: stp             xzr, x2, [SP]
    // 0xa907f8: r0 = []()
    //     0xa907f8: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xa907fc: r1 = LoadClassIdInstr(r0)
    //     0xa907fc: ldur            x1, [x0, #-1]
    //     0xa90800: ubfx            x1, x1, #0xc, #0x14
    // 0xa90804: str             x0, [SP]
    // 0xa90808: mov             x0, x1
    // 0xa9080c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xa9080c: sub             lr, x0, #1, lsl #12
    //     0xa90810: ldr             lr, [x21, lr, lsl #3]
    //     0xa90814: blr             lr
    // 0xa90818: cmp             w0, NULL
    // 0xa9081c: b.ne            #0xa90828
    // 0xa90820: r3 = ""
    //     0xa90820: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa90824: b               #0xa9082c
    // 0xa90828: mov             x3, x0
    // 0xa9082c: ldur            x2, [fp, #-0x10]
    // 0xa90830: ldur            x0, [fp, #-8]
    // 0xa90834: stur            x3, [fp, #-0x18]
    // 0xa90838: LoadField: r1 = r2->field_f
    //     0xa90838: ldur            w1, [x2, #0xf]
    // 0xa9083c: DecompressPointer r1
    //     0xa9083c: add             x1, x1, HEAP, lsl #32
    // 0xa90840: r0 = of()
    //     0xa90840: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa90844: LoadField: r1 = r0->field_87
    //     0xa90844: ldur            w1, [x0, #0x87]
    // 0xa90848: DecompressPointer r1
    //     0xa90848: add             x1, x1, HEAP, lsl #32
    // 0xa9084c: LoadField: r0 = r1->field_7
    //     0xa9084c: ldur            w0, [x1, #7]
    // 0xa90850: DecompressPointer r0
    //     0xa90850: add             x0, x0, HEAP, lsl #32
    // 0xa90854: stur            x0, [fp, #-0x28]
    // 0xa90858: r1 = Instance_Color
    //     0xa90858: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa9085c: d0 = 0.500000
    //     0xa9085c: fmov            d0, #0.50000000
    // 0xa90860: r0 = withOpacity()
    //     0xa90860: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa90864: r16 = 16.000000
    //     0xa90864: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa90868: ldr             x16, [x16, #0x188]
    // 0xa9086c: stp             x0, x16, [SP]
    // 0xa90870: ldur            x1, [fp, #-0x28]
    // 0xa90874: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa90874: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa90878: ldr             x4, [x4, #0xaa0]
    // 0xa9087c: r0 = copyWith()
    //     0xa9087c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa90880: stur            x0, [fp, #-0x28]
    // 0xa90884: r0 = Text()
    //     0xa90884: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa90888: mov             x1, x0
    // 0xa9088c: ldur            x0, [fp, #-0x18]
    // 0xa90890: stur            x1, [fp, #-0x30]
    // 0xa90894: StoreField: r1->field_b = r0
    //     0xa90894: stur            w0, [x1, #0xb]
    // 0xa90898: ldur            x0, [fp, #-0x28]
    // 0xa9089c: StoreField: r1->field_13 = r0
    //     0xa9089c: stur            w0, [x1, #0x13]
    // 0xa908a0: r0 = Center()
    //     0xa908a0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa908a4: mov             x1, x0
    // 0xa908a8: r0 = Instance_Alignment
    //     0xa908a8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa908ac: ldr             x0, [x0, #0xb10]
    // 0xa908b0: stur            x1, [fp, #-0x18]
    // 0xa908b4: StoreField: r1->field_f = r0
    //     0xa908b4: stur            w0, [x1, #0xf]
    // 0xa908b8: ldur            x0, [fp, #-0x30]
    // 0xa908bc: StoreField: r1->field_b = r0
    //     0xa908bc: stur            w0, [x1, #0xb]
    // 0xa908c0: r0 = Container()
    //     0xa908c0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa908c4: stur            x0, [fp, #-0x28]
    // 0xa908c8: r16 = 34.000000
    //     0xa908c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xa908cc: ldr             x16, [x16, #0x978]
    // 0xa908d0: r30 = 34.000000
    //     0xa908d0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xa908d4: ldr             lr, [lr, #0x978]
    // 0xa908d8: stp             lr, x16, [SP, #0x18]
    // 0xa908dc: r16 = Instance_EdgeInsets
    //     0xa908dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xa908e0: ldr             x16, [x16, #0x980]
    // 0xa908e4: ldur            lr, [fp, #-0x20]
    // 0xa908e8: stp             lr, x16, [SP, #8]
    // 0xa908ec: ldur            x16, [fp, #-0x18]
    // 0xa908f0: str             x16, [SP]
    // 0xa908f4: mov             x1, x0
    // 0xa908f8: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xa908f8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xa908fc: ldr             x4, [x4, #0x988]
    // 0xa90900: r0 = Container()
    //     0xa90900: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa90904: ldur            x0, [fp, #-8]
    // 0xa90908: cmp             w0, NULL
    // 0xa9090c: b.ne            #0xa90918
    // 0xa90910: r1 = Null
    //     0xa90910: mov             x1, NULL
    // 0xa90914: b               #0xa90920
    // 0xa90918: LoadField: r1 = r0->field_7
    //     0xa90918: ldur            w1, [x0, #7]
    // 0xa9091c: DecompressPointer r1
    //     0xa9091c: add             x1, x1, HEAP, lsl #32
    // 0xa90920: cmp             w1, NULL
    // 0xa90924: b.ne            #0xa90930
    // 0xa90928: r3 = ""
    //     0xa90928: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa9092c: b               #0xa90934
    // 0xa90930: mov             x3, x1
    // 0xa90934: ldur            x2, [fp, #-0x10]
    // 0xa90938: stur            x3, [fp, #-0x18]
    // 0xa9093c: LoadField: r1 = r2->field_f
    //     0xa9093c: ldur            w1, [x2, #0xf]
    // 0xa90940: DecompressPointer r1
    //     0xa90940: add             x1, x1, HEAP, lsl #32
    // 0xa90944: r0 = of()
    //     0xa90944: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa90948: LoadField: r1 = r0->field_87
    //     0xa90948: ldur            w1, [x0, #0x87]
    // 0xa9094c: DecompressPointer r1
    //     0xa9094c: add             x1, x1, HEAP, lsl #32
    // 0xa90950: LoadField: r0 = r1->field_7
    //     0xa90950: ldur            w0, [x1, #7]
    // 0xa90954: DecompressPointer r0
    //     0xa90954: add             x0, x0, HEAP, lsl #32
    // 0xa90958: r16 = 14.000000
    //     0xa90958: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa9095c: ldr             x16, [x16, #0x1d8]
    // 0xa90960: r30 = Instance_Color
    //     0xa90960: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa90964: stp             lr, x16, [SP]
    // 0xa90968: mov             x1, x0
    // 0xa9096c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa9096c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa90970: ldr             x4, [x4, #0xaa0]
    // 0xa90974: r0 = copyWith()
    //     0xa90974: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa90978: stur            x0, [fp, #-0x20]
    // 0xa9097c: r0 = Text()
    //     0xa9097c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa90980: mov             x2, x0
    // 0xa90984: ldur            x0, [fp, #-0x18]
    // 0xa90988: stur            x2, [fp, #-0x30]
    // 0xa9098c: StoreField: r2->field_b = r0
    //     0xa9098c: stur            w0, [x2, #0xb]
    // 0xa90990: ldur            x0, [fp, #-0x20]
    // 0xa90994: StoreField: r2->field_13 = r0
    //     0xa90994: stur            w0, [x2, #0x13]
    // 0xa90998: ldur            x0, [fp, #-8]
    // 0xa9099c: cmp             w0, NULL
    // 0xa909a0: b.ne            #0xa909ac
    // 0xa909a4: r1 = Null
    //     0xa909a4: mov             x1, NULL
    // 0xa909a8: b               #0xa909b4
    // 0xa909ac: LoadField: r1 = r0->field_1f
    //     0xa909ac: ldur            w1, [x0, #0x1f]
    // 0xa909b0: DecompressPointer r1
    //     0xa909b0: add             x1, x1, HEAP, lsl #32
    // 0xa909b4: cmp             w1, NULL
    // 0xa909b8: b.ne            #0xa909c4
    // 0xa909bc: r4 = ""
    //     0xa909bc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa909c0: b               #0xa909c8
    // 0xa909c4: mov             x4, x1
    // 0xa909c8: ldur            x3, [fp, #-0x10]
    // 0xa909cc: stur            x4, [fp, #-0x18]
    // 0xa909d0: LoadField: r1 = r3->field_f
    //     0xa909d0: ldur            w1, [x3, #0xf]
    // 0xa909d4: DecompressPointer r1
    //     0xa909d4: add             x1, x1, HEAP, lsl #32
    // 0xa909d8: r0 = of()
    //     0xa909d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa909dc: LoadField: r1 = r0->field_87
    //     0xa909dc: ldur            w1, [x0, #0x87]
    // 0xa909e0: DecompressPointer r1
    //     0xa909e0: add             x1, x1, HEAP, lsl #32
    // 0xa909e4: LoadField: r0 = r1->field_33
    //     0xa909e4: ldur            w0, [x1, #0x33]
    // 0xa909e8: DecompressPointer r0
    //     0xa909e8: add             x0, x0, HEAP, lsl #32
    // 0xa909ec: stur            x0, [fp, #-0x20]
    // 0xa909f0: cmp             w0, NULL
    // 0xa909f4: b.ne            #0xa90a00
    // 0xa909f8: r4 = Null
    //     0xa909f8: mov             x4, NULL
    // 0xa909fc: b               #0xa90a28
    // 0xa90a00: r1 = Instance_Color
    //     0xa90a00: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa90a04: d0 = 0.500000
    //     0xa90a04: fmov            d0, #0.50000000
    // 0xa90a08: r0 = withOpacity()
    //     0xa90a08: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa90a0c: r16 = 10.000000
    //     0xa90a0c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xa90a10: stp             x0, x16, [SP]
    // 0xa90a14: ldur            x1, [fp, #-0x20]
    // 0xa90a18: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa90a18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa90a1c: ldr             x4, [x4, #0xaa0]
    // 0xa90a20: r0 = copyWith()
    //     0xa90a20: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa90a24: mov             x4, x0
    // 0xa90a28: ldur            x1, [fp, #-8]
    // 0xa90a2c: ldur            x3, [fp, #-0x28]
    // 0xa90a30: ldur            x0, [fp, #-0x30]
    // 0xa90a34: ldur            x2, [fp, #-0x18]
    // 0xa90a38: stur            x4, [fp, #-0x20]
    // 0xa90a3c: r0 = Text()
    //     0xa90a3c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa90a40: mov             x1, x0
    // 0xa90a44: ldur            x0, [fp, #-0x18]
    // 0xa90a48: stur            x1, [fp, #-0x38]
    // 0xa90a4c: StoreField: r1->field_b = r0
    //     0xa90a4c: stur            w0, [x1, #0xb]
    // 0xa90a50: ldur            x0, [fp, #-0x20]
    // 0xa90a54: StoreField: r1->field_13 = r0
    //     0xa90a54: stur            w0, [x1, #0x13]
    // 0xa90a58: r0 = Padding()
    //     0xa90a58: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa90a5c: mov             x3, x0
    // 0xa90a60: r0 = Instance_EdgeInsets
    //     0xa90a60: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xa90a64: ldr             x0, [x0, #0x990]
    // 0xa90a68: stur            x3, [fp, #-0x18]
    // 0xa90a6c: StoreField: r3->field_f = r0
    //     0xa90a6c: stur            w0, [x3, #0xf]
    // 0xa90a70: ldur            x0, [fp, #-0x38]
    // 0xa90a74: StoreField: r3->field_b = r0
    //     0xa90a74: stur            w0, [x3, #0xb]
    // 0xa90a78: r1 = Null
    //     0xa90a78: mov             x1, NULL
    // 0xa90a7c: r2 = 4
    //     0xa90a7c: movz            x2, #0x4
    // 0xa90a80: r0 = AllocateArray()
    //     0xa90a80: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa90a84: mov             x2, x0
    // 0xa90a88: ldur            x0, [fp, #-0x30]
    // 0xa90a8c: stur            x2, [fp, #-0x20]
    // 0xa90a90: StoreField: r2->field_f = r0
    //     0xa90a90: stur            w0, [x2, #0xf]
    // 0xa90a94: ldur            x0, [fp, #-0x18]
    // 0xa90a98: StoreField: r2->field_13 = r0
    //     0xa90a98: stur            w0, [x2, #0x13]
    // 0xa90a9c: r1 = <Widget>
    //     0xa90a9c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa90aa0: r0 = AllocateGrowableArray()
    //     0xa90aa0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa90aa4: mov             x1, x0
    // 0xa90aa8: ldur            x0, [fp, #-0x20]
    // 0xa90aac: stur            x1, [fp, #-0x18]
    // 0xa90ab0: StoreField: r1->field_f = r0
    //     0xa90ab0: stur            w0, [x1, #0xf]
    // 0xa90ab4: r2 = 4
    //     0xa90ab4: movz            x2, #0x4
    // 0xa90ab8: StoreField: r1->field_b = r2
    //     0xa90ab8: stur            w2, [x1, #0xb]
    // 0xa90abc: r0 = Column()
    //     0xa90abc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa90ac0: mov             x3, x0
    // 0xa90ac4: r0 = Instance_Axis
    //     0xa90ac4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa90ac8: stur            x3, [fp, #-0x20]
    // 0xa90acc: StoreField: r3->field_f = r0
    //     0xa90acc: stur            w0, [x3, #0xf]
    // 0xa90ad0: r4 = Instance_MainAxisAlignment
    //     0xa90ad0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa90ad4: ldr             x4, [x4, #0xa08]
    // 0xa90ad8: StoreField: r3->field_13 = r4
    //     0xa90ad8: stur            w4, [x3, #0x13]
    // 0xa90adc: r5 = Instance_MainAxisSize
    //     0xa90adc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa90ae0: ldr             x5, [x5, #0xa10]
    // 0xa90ae4: ArrayStore: r3[0] = r5  ; List_4
    //     0xa90ae4: stur            w5, [x3, #0x17]
    // 0xa90ae8: r6 = Instance_CrossAxisAlignment
    //     0xa90ae8: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa90aec: ldr             x6, [x6, #0x890]
    // 0xa90af0: StoreField: r3->field_1b = r6
    //     0xa90af0: stur            w6, [x3, #0x1b]
    // 0xa90af4: r7 = Instance_VerticalDirection
    //     0xa90af4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa90af8: ldr             x7, [x7, #0xa20]
    // 0xa90afc: StoreField: r3->field_23 = r7
    //     0xa90afc: stur            w7, [x3, #0x23]
    // 0xa90b00: r8 = Instance_Clip
    //     0xa90b00: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa90b04: ldr             x8, [x8, #0x38]
    // 0xa90b08: StoreField: r3->field_2b = r8
    //     0xa90b08: stur            w8, [x3, #0x2b]
    // 0xa90b0c: StoreField: r3->field_2f = rZR
    //     0xa90b0c: stur            xzr, [x3, #0x2f]
    // 0xa90b10: ldur            x1, [fp, #-0x18]
    // 0xa90b14: StoreField: r3->field_b = r1
    //     0xa90b14: stur            w1, [x3, #0xb]
    // 0xa90b18: r1 = Null
    //     0xa90b18: mov             x1, NULL
    // 0xa90b1c: r2 = 6
    //     0xa90b1c: movz            x2, #0x6
    // 0xa90b20: r0 = AllocateArray()
    //     0xa90b20: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa90b24: mov             x2, x0
    // 0xa90b28: ldur            x0, [fp, #-0x28]
    // 0xa90b2c: stur            x2, [fp, #-0x18]
    // 0xa90b30: StoreField: r2->field_f = r0
    //     0xa90b30: stur            w0, [x2, #0xf]
    // 0xa90b34: r16 = Instance_SizedBox
    //     0xa90b34: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xa90b38: ldr             x16, [x16, #0x998]
    // 0xa90b3c: StoreField: r2->field_13 = r16
    //     0xa90b3c: stur            w16, [x2, #0x13]
    // 0xa90b40: ldur            x0, [fp, #-0x20]
    // 0xa90b44: ArrayStore: r2[0] = r0  ; List_4
    //     0xa90b44: stur            w0, [x2, #0x17]
    // 0xa90b48: r1 = <Widget>
    //     0xa90b48: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa90b4c: r0 = AllocateGrowableArray()
    //     0xa90b4c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa90b50: mov             x1, x0
    // 0xa90b54: ldur            x0, [fp, #-0x18]
    // 0xa90b58: stur            x1, [fp, #-0x20]
    // 0xa90b5c: StoreField: r1->field_f = r0
    //     0xa90b5c: stur            w0, [x1, #0xf]
    // 0xa90b60: r2 = 6
    //     0xa90b60: movz            x2, #0x6
    // 0xa90b64: StoreField: r1->field_b = r2
    //     0xa90b64: stur            w2, [x1, #0xb]
    // 0xa90b68: r0 = Row()
    //     0xa90b68: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa90b6c: mov             x2, x0
    // 0xa90b70: r0 = Instance_Axis
    //     0xa90b70: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa90b74: stur            x2, [fp, #-0x18]
    // 0xa90b78: StoreField: r2->field_f = r0
    //     0xa90b78: stur            w0, [x2, #0xf]
    // 0xa90b7c: r3 = Instance_MainAxisAlignment
    //     0xa90b7c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa90b80: ldr             x3, [x3, #0xa08]
    // 0xa90b84: StoreField: r2->field_13 = r3
    //     0xa90b84: stur            w3, [x2, #0x13]
    // 0xa90b88: r4 = Instance_MainAxisSize
    //     0xa90b88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa90b8c: ldr             x4, [x4, #0xa10]
    // 0xa90b90: ArrayStore: r2[0] = r4  ; List_4
    //     0xa90b90: stur            w4, [x2, #0x17]
    // 0xa90b94: r5 = Instance_CrossAxisAlignment
    //     0xa90b94: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa90b98: ldr             x5, [x5, #0xa18]
    // 0xa90b9c: StoreField: r2->field_1b = r5
    //     0xa90b9c: stur            w5, [x2, #0x1b]
    // 0xa90ba0: r6 = Instance_VerticalDirection
    //     0xa90ba0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa90ba4: ldr             x6, [x6, #0xa20]
    // 0xa90ba8: StoreField: r2->field_23 = r6
    //     0xa90ba8: stur            w6, [x2, #0x23]
    // 0xa90bac: r7 = Instance_Clip
    //     0xa90bac: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa90bb0: ldr             x7, [x7, #0x38]
    // 0xa90bb4: StoreField: r2->field_2b = r7
    //     0xa90bb4: stur            w7, [x2, #0x2b]
    // 0xa90bb8: StoreField: r2->field_2f = rZR
    //     0xa90bb8: stur            xzr, [x2, #0x2f]
    // 0xa90bbc: ldur            x1, [fp, #-0x20]
    // 0xa90bc0: StoreField: r2->field_b = r1
    //     0xa90bc0: stur            w1, [x2, #0xb]
    // 0xa90bc4: ldur            x8, [fp, #-8]
    // 0xa90bc8: cmp             w8, NULL
    // 0xa90bcc: b.eq            #0xa90be0
    // 0xa90bd0: LoadField: r1 = r8->field_f
    //     0xa90bd0: ldur            w1, [x8, #0xf]
    // 0xa90bd4: DecompressPointer r1
    //     0xa90bd4: add             x1, x1, HEAP, lsl #32
    // 0xa90bd8: cmp             w1, #0xa
    // 0xa90bdc: b.eq            #0xa90bf8
    // 0xa90be0: cmp             w8, NULL
    // 0xa90be4: b.eq            #0xa90c08
    // 0xa90be8: LoadField: r1 = r8->field_f
    //     0xa90be8: ldur            w1, [x8, #0xf]
    // 0xa90bec: DecompressPointer r1
    //     0xa90bec: add             x1, x1, HEAP, lsl #32
    // 0xa90bf0: cmp             w1, #8
    // 0xa90bf4: b.ne            #0xa90c08
    // 0xa90bf8: mov             x0, x8
    // 0xa90bfc: r1 = Instance_Color
    //     0xa90bfc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa90c00: ldr             x1, [x1, #0x858]
    // 0xa90c04: b               #0xa90c78
    // 0xa90c08: cmp             w8, NULL
    // 0xa90c0c: b.ne            #0xa90c18
    // 0xa90c10: mov             x0, x8
    // 0xa90c14: b               #0xa90c4c
    // 0xa90c18: LoadField: r1 = r8->field_f
    //     0xa90c18: ldur            w1, [x8, #0xf]
    // 0xa90c1c: DecompressPointer r1
    //     0xa90c1c: add             x1, x1, HEAP, lsl #32
    // 0xa90c20: cmp             w1, #6
    // 0xa90c24: b.ne            #0xa90c48
    // 0xa90c28: r1 = Instance_Color
    //     0xa90c28: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa90c2c: ldr             x1, [x1, #0x858]
    // 0xa90c30: d0 = 0.700000
    //     0xa90c30: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa90c34: ldr             d0, [x17, #0xf48]
    // 0xa90c38: r0 = withOpacity()
    //     0xa90c38: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa90c3c: mov             x1, x0
    // 0xa90c40: ldur            x0, [fp, #-8]
    // 0xa90c44: b               #0xa90c78
    // 0xa90c48: ldur            x0, [fp, #-8]
    // 0xa90c4c: cmp             w0, NULL
    // 0xa90c50: b.eq            #0xa90c70
    // 0xa90c54: LoadField: r1 = r0->field_f
    //     0xa90c54: ldur            w1, [x0, #0xf]
    // 0xa90c58: DecompressPointer r1
    //     0xa90c58: add             x1, x1, HEAP, lsl #32
    // 0xa90c5c: cmp             w1, #4
    // 0xa90c60: b.ne            #0xa90c70
    // 0xa90c64: r1 = Instance_Color
    //     0xa90c64: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xa90c68: ldr             x1, [x1, #0x860]
    // 0xa90c6c: b               #0xa90c78
    // 0xa90c70: r1 = Instance_Color
    //     0xa90c70: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xa90c74: ldr             x1, [x1, #0x50]
    // 0xa90c78: stur            x1, [fp, #-0x20]
    // 0xa90c7c: r0 = ColorFilter()
    //     0xa90c7c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xa90c80: mov             x1, x0
    // 0xa90c84: ldur            x0, [fp, #-0x20]
    // 0xa90c88: stur            x1, [fp, #-0x28]
    // 0xa90c8c: StoreField: r1->field_7 = r0
    //     0xa90c8c: stur            w0, [x1, #7]
    // 0xa90c90: r0 = Instance_BlendMode
    //     0xa90c90: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa90c94: ldr             x0, [x0, #0xb30]
    // 0xa90c98: StoreField: r1->field_b = r0
    //     0xa90c98: stur            w0, [x1, #0xb]
    // 0xa90c9c: r0 = 1
    //     0xa90c9c: movz            x0, #0x1
    // 0xa90ca0: StoreField: r1->field_13 = r0
    //     0xa90ca0: stur            x0, [x1, #0x13]
    // 0xa90ca4: r0 = SvgPicture()
    //     0xa90ca4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa90ca8: stur            x0, [fp, #-0x20]
    // 0xa90cac: ldur            x16, [fp, #-0x28]
    // 0xa90cb0: str             x16, [SP]
    // 0xa90cb4: mov             x1, x0
    // 0xa90cb8: r2 = "assets/images/green_star.svg"
    //     0xa90cb8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xa90cbc: ldr             x2, [x2, #0x9a0]
    // 0xa90cc0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xa90cc0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xa90cc4: ldr             x4, [x4, #0xa38]
    // 0xa90cc8: r0 = SvgPicture.asset()
    //     0xa90cc8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa90ccc: ldur            x1, [fp, #-8]
    // 0xa90cd0: cmp             w1, NULL
    // 0xa90cd4: b.ne            #0xa90ce0
    // 0xa90cd8: r0 = Null
    //     0xa90cd8: mov             x0, NULL
    // 0xa90cdc: b               #0xa90d14
    // 0xa90ce0: LoadField: r0 = r1->field_f
    //     0xa90ce0: ldur            w0, [x1, #0xf]
    // 0xa90ce4: DecompressPointer r0
    //     0xa90ce4: add             x0, x0, HEAP, lsl #32
    // 0xa90ce8: r2 = 60
    //     0xa90ce8: movz            x2, #0x3c
    // 0xa90cec: branchIfSmi(r0, 0xa90cf8)
    //     0xa90cec: tbz             w0, #0, #0xa90cf8
    // 0xa90cf0: r2 = LoadClassIdInstr(r0)
    //     0xa90cf0: ldur            x2, [x0, #-1]
    //     0xa90cf4: ubfx            x2, x2, #0xc, #0x14
    // 0xa90cf8: str             x0, [SP]
    // 0xa90cfc: mov             x0, x2
    // 0xa90d00: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xa90d00: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xa90d04: r0 = GDT[cid_x0 + 0x2700]()
    //     0xa90d04: movz            x17, #0x2700
    //     0xa90d08: add             lr, x0, x17
    //     0xa90d0c: ldr             lr, [x21, lr, lsl #3]
    //     0xa90d10: blr             lr
    // 0xa90d14: cmp             w0, NULL
    // 0xa90d18: b.ne            #0xa90d24
    // 0xa90d1c: r5 = ""
    //     0xa90d1c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa90d20: b               #0xa90d28
    // 0xa90d24: mov             x5, x0
    // 0xa90d28: ldur            x4, [fp, #-0x10]
    // 0xa90d2c: ldur            x0, [fp, #-8]
    // 0xa90d30: ldur            x3, [fp, #-0x18]
    // 0xa90d34: ldur            x2, [fp, #-0x20]
    // 0xa90d38: stur            x5, [fp, #-0x28]
    // 0xa90d3c: LoadField: r1 = r4->field_f
    //     0xa90d3c: ldur            w1, [x4, #0xf]
    // 0xa90d40: DecompressPointer r1
    //     0xa90d40: add             x1, x1, HEAP, lsl #32
    // 0xa90d44: r0 = of()
    //     0xa90d44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa90d48: LoadField: r1 = r0->field_87
    //     0xa90d48: ldur            w1, [x0, #0x87]
    // 0xa90d4c: DecompressPointer r1
    //     0xa90d4c: add             x1, x1, HEAP, lsl #32
    // 0xa90d50: LoadField: r0 = r1->field_7
    //     0xa90d50: ldur            w0, [x1, #7]
    // 0xa90d54: DecompressPointer r0
    //     0xa90d54: add             x0, x0, HEAP, lsl #32
    // 0xa90d58: r16 = 12.000000
    //     0xa90d58: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa90d5c: ldr             x16, [x16, #0x9e8]
    // 0xa90d60: r30 = Instance_Color
    //     0xa90d60: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa90d64: stp             lr, x16, [SP]
    // 0xa90d68: mov             x1, x0
    // 0xa90d6c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa90d6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa90d70: ldr             x4, [x4, #0xaa0]
    // 0xa90d74: r0 = copyWith()
    //     0xa90d74: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa90d78: stur            x0, [fp, #-0x30]
    // 0xa90d7c: r0 = Text()
    //     0xa90d7c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa90d80: mov             x3, x0
    // 0xa90d84: ldur            x0, [fp, #-0x28]
    // 0xa90d88: stur            x3, [fp, #-0x38]
    // 0xa90d8c: StoreField: r3->field_b = r0
    //     0xa90d8c: stur            w0, [x3, #0xb]
    // 0xa90d90: ldur            x0, [fp, #-0x30]
    // 0xa90d94: StoreField: r3->field_13 = r0
    //     0xa90d94: stur            w0, [x3, #0x13]
    // 0xa90d98: r1 = Null
    //     0xa90d98: mov             x1, NULL
    // 0xa90d9c: r2 = 6
    //     0xa90d9c: movz            x2, #0x6
    // 0xa90da0: r0 = AllocateArray()
    //     0xa90da0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa90da4: mov             x2, x0
    // 0xa90da8: ldur            x0, [fp, #-0x20]
    // 0xa90dac: stur            x2, [fp, #-0x28]
    // 0xa90db0: StoreField: r2->field_f = r0
    //     0xa90db0: stur            w0, [x2, #0xf]
    // 0xa90db4: r16 = Instance_SizedBox
    //     0xa90db4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9a8] Obj!SizedBox@d67ea1
    //     0xa90db8: ldr             x16, [x16, #0x9a8]
    // 0xa90dbc: StoreField: r2->field_13 = r16
    //     0xa90dbc: stur            w16, [x2, #0x13]
    // 0xa90dc0: ldur            x0, [fp, #-0x38]
    // 0xa90dc4: ArrayStore: r2[0] = r0  ; List_4
    //     0xa90dc4: stur            w0, [x2, #0x17]
    // 0xa90dc8: r1 = <Widget>
    //     0xa90dc8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa90dcc: r0 = AllocateGrowableArray()
    //     0xa90dcc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa90dd0: mov             x1, x0
    // 0xa90dd4: ldur            x0, [fp, #-0x28]
    // 0xa90dd8: stur            x1, [fp, #-0x20]
    // 0xa90ddc: StoreField: r1->field_f = r0
    //     0xa90ddc: stur            w0, [x1, #0xf]
    // 0xa90de0: r0 = 6
    //     0xa90de0: movz            x0, #0x6
    // 0xa90de4: StoreField: r1->field_b = r0
    //     0xa90de4: stur            w0, [x1, #0xb]
    // 0xa90de8: r0 = Row()
    //     0xa90de8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa90dec: mov             x3, x0
    // 0xa90df0: r0 = Instance_Axis
    //     0xa90df0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa90df4: stur            x3, [fp, #-0x28]
    // 0xa90df8: StoreField: r3->field_f = r0
    //     0xa90df8: stur            w0, [x3, #0xf]
    // 0xa90dfc: r4 = Instance_MainAxisAlignment
    //     0xa90dfc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa90e00: ldr             x4, [x4, #0xa08]
    // 0xa90e04: StoreField: r3->field_13 = r4
    //     0xa90e04: stur            w4, [x3, #0x13]
    // 0xa90e08: r5 = Instance_MainAxisSize
    //     0xa90e08: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa90e0c: ldr             x5, [x5, #0xa10]
    // 0xa90e10: ArrayStore: r3[0] = r5  ; List_4
    //     0xa90e10: stur            w5, [x3, #0x17]
    // 0xa90e14: r6 = Instance_CrossAxisAlignment
    //     0xa90e14: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa90e18: ldr             x6, [x6, #0xa18]
    // 0xa90e1c: StoreField: r3->field_1b = r6
    //     0xa90e1c: stur            w6, [x3, #0x1b]
    // 0xa90e20: r7 = Instance_VerticalDirection
    //     0xa90e20: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa90e24: ldr             x7, [x7, #0xa20]
    // 0xa90e28: StoreField: r3->field_23 = r7
    //     0xa90e28: stur            w7, [x3, #0x23]
    // 0xa90e2c: r8 = Instance_Clip
    //     0xa90e2c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa90e30: ldr             x8, [x8, #0x38]
    // 0xa90e34: StoreField: r3->field_2b = r8
    //     0xa90e34: stur            w8, [x3, #0x2b]
    // 0xa90e38: StoreField: r3->field_2f = rZR
    //     0xa90e38: stur            xzr, [x3, #0x2f]
    // 0xa90e3c: ldur            x1, [fp, #-0x20]
    // 0xa90e40: StoreField: r3->field_b = r1
    //     0xa90e40: stur            w1, [x3, #0xb]
    // 0xa90e44: r1 = Null
    //     0xa90e44: mov             x1, NULL
    // 0xa90e48: r2 = 4
    //     0xa90e48: movz            x2, #0x4
    // 0xa90e4c: r0 = AllocateArray()
    //     0xa90e4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa90e50: mov             x2, x0
    // 0xa90e54: ldur            x0, [fp, #-0x18]
    // 0xa90e58: stur            x2, [fp, #-0x20]
    // 0xa90e5c: StoreField: r2->field_f = r0
    //     0xa90e5c: stur            w0, [x2, #0xf]
    // 0xa90e60: ldur            x0, [fp, #-0x28]
    // 0xa90e64: StoreField: r2->field_13 = r0
    //     0xa90e64: stur            w0, [x2, #0x13]
    // 0xa90e68: r1 = <Widget>
    //     0xa90e68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa90e6c: r0 = AllocateGrowableArray()
    //     0xa90e6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa90e70: mov             x1, x0
    // 0xa90e74: ldur            x0, [fp, #-0x20]
    // 0xa90e78: stur            x1, [fp, #-0x18]
    // 0xa90e7c: StoreField: r1->field_f = r0
    //     0xa90e7c: stur            w0, [x1, #0xf]
    // 0xa90e80: r2 = 4
    //     0xa90e80: movz            x2, #0x4
    // 0xa90e84: StoreField: r1->field_b = r2
    //     0xa90e84: stur            w2, [x1, #0xb]
    // 0xa90e88: r0 = Row()
    //     0xa90e88: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa90e8c: mov             x3, x0
    // 0xa90e90: r0 = Instance_Axis
    //     0xa90e90: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa90e94: stur            x3, [fp, #-0x20]
    // 0xa90e98: StoreField: r3->field_f = r0
    //     0xa90e98: stur            w0, [x3, #0xf]
    // 0xa90e9c: r0 = Instance_MainAxisAlignment
    //     0xa90e9c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xa90ea0: ldr             x0, [x0, #0xa8]
    // 0xa90ea4: StoreField: r3->field_13 = r0
    //     0xa90ea4: stur            w0, [x3, #0x13]
    // 0xa90ea8: r0 = Instance_MainAxisSize
    //     0xa90ea8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa90eac: ldr             x0, [x0, #0xa10]
    // 0xa90eb0: ArrayStore: r3[0] = r0  ; List_4
    //     0xa90eb0: stur            w0, [x3, #0x17]
    // 0xa90eb4: r1 = Instance_CrossAxisAlignment
    //     0xa90eb4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa90eb8: ldr             x1, [x1, #0xa18]
    // 0xa90ebc: StoreField: r3->field_1b = r1
    //     0xa90ebc: stur            w1, [x3, #0x1b]
    // 0xa90ec0: r4 = Instance_VerticalDirection
    //     0xa90ec0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa90ec4: ldr             x4, [x4, #0xa20]
    // 0xa90ec8: StoreField: r3->field_23 = r4
    //     0xa90ec8: stur            w4, [x3, #0x23]
    // 0xa90ecc: r5 = Instance_Clip
    //     0xa90ecc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa90ed0: ldr             x5, [x5, #0x38]
    // 0xa90ed4: StoreField: r3->field_2b = r5
    //     0xa90ed4: stur            w5, [x3, #0x2b]
    // 0xa90ed8: StoreField: r3->field_2f = rZR
    //     0xa90ed8: stur            xzr, [x3, #0x2f]
    // 0xa90edc: ldur            x1, [fp, #-0x18]
    // 0xa90ee0: StoreField: r3->field_b = r1
    //     0xa90ee0: stur            w1, [x3, #0xb]
    // 0xa90ee4: r1 = Null
    //     0xa90ee4: mov             x1, NULL
    // 0xa90ee8: r2 = 2
    //     0xa90ee8: movz            x2, #0x2
    // 0xa90eec: r0 = AllocateArray()
    //     0xa90eec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa90ef0: mov             x2, x0
    // 0xa90ef4: ldur            x0, [fp, #-0x20]
    // 0xa90ef8: stur            x2, [fp, #-0x18]
    // 0xa90efc: StoreField: r2->field_f = r0
    //     0xa90efc: stur            w0, [x2, #0xf]
    // 0xa90f00: r1 = <Widget>
    //     0xa90f00: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa90f04: r0 = AllocateGrowableArray()
    //     0xa90f04: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa90f08: mov             x2, x0
    // 0xa90f0c: ldur            x0, [fp, #-0x18]
    // 0xa90f10: stur            x2, [fp, #-0x20]
    // 0xa90f14: StoreField: r2->field_f = r0
    //     0xa90f14: stur            w0, [x2, #0xf]
    // 0xa90f18: r0 = 2
    //     0xa90f18: movz            x0, #0x2
    // 0xa90f1c: StoreField: r2->field_b = r0
    //     0xa90f1c: stur            w0, [x2, #0xb]
    // 0xa90f20: ldur            x0, [fp, #-8]
    // 0xa90f24: cmp             w0, NULL
    // 0xa90f28: b.ne            #0xa90f34
    // 0xa90f2c: r1 = Null
    //     0xa90f2c: mov             x1, NULL
    // 0xa90f30: b               #0xa90f60
    // 0xa90f34: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa90f34: ldur            w1, [x0, #0x17]
    // 0xa90f38: DecompressPointer r1
    //     0xa90f38: add             x1, x1, HEAP, lsl #32
    // 0xa90f3c: cmp             w1, NULL
    // 0xa90f40: b.ne            #0xa90f4c
    // 0xa90f44: r1 = Null
    //     0xa90f44: mov             x1, NULL
    // 0xa90f48: b               #0xa90f60
    // 0xa90f4c: LoadField: r3 = r1->field_7
    //     0xa90f4c: ldur            w3, [x1, #7]
    // 0xa90f50: cbnz            w3, #0xa90f5c
    // 0xa90f54: r1 = false
    //     0xa90f54: add             x1, NULL, #0x30  ; false
    // 0xa90f58: b               #0xa90f60
    // 0xa90f5c: r1 = true
    //     0xa90f5c: add             x1, NULL, #0x20  ; true
    // 0xa90f60: cmp             w1, NULL
    // 0xa90f64: b.ne            #0xa90f70
    // 0xa90f68: mov             x3, x2
    // 0xa90f6c: b               #0xa911a8
    // 0xa90f70: tbnz            w1, #4, #0xa911a4
    // 0xa90f74: cmp             w0, NULL
    // 0xa90f78: b.ne            #0xa90f84
    // 0xa90f7c: r0 = Null
    //     0xa90f7c: mov             x0, NULL
    // 0xa90f80: b               #0xa90fa0
    // 0xa90f84: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa90f84: ldur            w1, [x0, #0x17]
    // 0xa90f88: DecompressPointer r1
    //     0xa90f88: add             x1, x1, HEAP, lsl #32
    // 0xa90f8c: cmp             w1, NULL
    // 0xa90f90: b.ne            #0xa90f9c
    // 0xa90f94: r0 = Null
    //     0xa90f94: mov             x0, NULL
    // 0xa90f98: b               #0xa90fa0
    // 0xa90f9c: r0 = trim()
    //     0xa90f9c: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xa90fa0: cmp             w0, NULL
    // 0xa90fa4: b.ne            #0xa90fb0
    // 0xa90fa8: r3 = ""
    //     0xa90fa8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa90fac: b               #0xa90fb4
    // 0xa90fb0: mov             x3, x0
    // 0xa90fb4: ldur            x2, [fp, #-0x10]
    // 0xa90fb8: ldur            x0, [fp, #-0x20]
    // 0xa90fbc: stur            x3, [fp, #-0x18]
    // 0xa90fc0: LoadField: r1 = r2->field_f
    //     0xa90fc0: ldur            w1, [x2, #0xf]
    // 0xa90fc4: DecompressPointer r1
    //     0xa90fc4: add             x1, x1, HEAP, lsl #32
    // 0xa90fc8: r0 = of()
    //     0xa90fc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa90fcc: LoadField: r1 = r0->field_87
    //     0xa90fcc: ldur            w1, [x0, #0x87]
    // 0xa90fd0: DecompressPointer r1
    //     0xa90fd0: add             x1, x1, HEAP, lsl #32
    // 0xa90fd4: LoadField: r0 = r1->field_2b
    //     0xa90fd4: ldur            w0, [x1, #0x2b]
    // 0xa90fd8: DecompressPointer r0
    //     0xa90fd8: add             x0, x0, HEAP, lsl #32
    // 0xa90fdc: LoadField: r1 = r0->field_13
    //     0xa90fdc: ldur            w1, [x0, #0x13]
    // 0xa90fe0: DecompressPointer r1
    //     0xa90fe0: add             x1, x1, HEAP, lsl #32
    // 0xa90fe4: r16 = Instance_Color
    //     0xa90fe4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa90fe8: stp             x16, x1, [SP]
    // 0xa90fec: r1 = Instance_TextStyle
    //     0xa90fec: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9b0] Obj!TextStyle@d62871
    //     0xa90ff0: ldr             x1, [x1, #0x9b0]
    // 0xa90ff4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0xa90ff4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0xa90ff8: ldr             x4, [x4, #0x9b8]
    // 0xa90ffc: r0 = copyWith()
    //     0xa90ffc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa91000: ldur            x2, [fp, #-0x10]
    // 0xa91004: stur            x0, [fp, #-0x28]
    // 0xa91008: LoadField: r1 = r2->field_f
    //     0xa91008: ldur            w1, [x2, #0xf]
    // 0xa9100c: DecompressPointer r1
    //     0xa9100c: add             x1, x1, HEAP, lsl #32
    // 0xa91010: r0 = of()
    //     0xa91010: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa91014: LoadField: r1 = r0->field_87
    //     0xa91014: ldur            w1, [x0, #0x87]
    // 0xa91018: DecompressPointer r1
    //     0xa91018: add             x1, x1, HEAP, lsl #32
    // 0xa9101c: LoadField: r0 = r1->field_7
    //     0xa9101c: ldur            w0, [x1, #7]
    // 0xa91020: DecompressPointer r0
    //     0xa91020: add             x0, x0, HEAP, lsl #32
    // 0xa91024: r16 = Instance_Color
    //     0xa91024: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa91028: r30 = 12.000000
    //     0xa91028: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa9102c: ldr             lr, [lr, #0x9e8]
    // 0xa91030: stp             lr, x16, [SP]
    // 0xa91034: mov             x1, x0
    // 0xa91038: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa91038: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa9103c: ldr             x4, [x4, #0x9b8]
    // 0xa91040: r0 = copyWith()
    //     0xa91040: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa91044: ldur            x2, [fp, #-0x10]
    // 0xa91048: stur            x0, [fp, #-0x30]
    // 0xa9104c: LoadField: r1 = r2->field_f
    //     0xa9104c: ldur            w1, [x2, #0xf]
    // 0xa91050: DecompressPointer r1
    //     0xa91050: add             x1, x1, HEAP, lsl #32
    // 0xa91054: r0 = of()
    //     0xa91054: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa91058: LoadField: r1 = r0->field_87
    //     0xa91058: ldur            w1, [x0, #0x87]
    // 0xa9105c: DecompressPointer r1
    //     0xa9105c: add             x1, x1, HEAP, lsl #32
    // 0xa91060: LoadField: r0 = r1->field_7
    //     0xa91060: ldur            w0, [x1, #7]
    // 0xa91064: DecompressPointer r0
    //     0xa91064: add             x0, x0, HEAP, lsl #32
    // 0xa91068: r16 = Instance_Color
    //     0xa91068: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa9106c: r30 = 12.000000
    //     0xa9106c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa91070: ldr             lr, [lr, #0x9e8]
    // 0xa91074: stp             lr, x16, [SP]
    // 0xa91078: mov             x1, x0
    // 0xa9107c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa9107c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa91080: ldr             x4, [x4, #0x9b8]
    // 0xa91084: r0 = copyWith()
    //     0xa91084: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa91088: stur            x0, [fp, #-0x38]
    // 0xa9108c: r0 = ReadMoreText()
    //     0xa9108c: bl              #0x99f824  ; AllocateReadMoreTextStub -> ReadMoreText (size=0x6c)
    // 0xa91090: mov             x1, x0
    // 0xa91094: ldur            x0, [fp, #-0x18]
    // 0xa91098: stur            x1, [fp, #-0x40]
    // 0xa9109c: StoreField: r1->field_3f = r0
    //     0xa9109c: stur            w0, [x1, #0x3f]
    // 0xa910a0: r0 = " Read Less"
    //     0xa910a0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c0] " Read Less"
    //     0xa910a4: ldr             x0, [x0, #0x9c0]
    // 0xa910a8: StoreField: r1->field_43 = r0
    //     0xa910a8: stur            w0, [x1, #0x43]
    // 0xa910ac: r0 = "Read More"
    //     0xa910ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c8] "Read More"
    //     0xa910b0: ldr             x0, [x0, #0x9c8]
    // 0xa910b4: StoreField: r1->field_47 = r0
    //     0xa910b4: stur            w0, [x1, #0x47]
    // 0xa910b8: r0 = 240
    //     0xa910b8: movz            x0, #0xf0
    // 0xa910bc: StoreField: r1->field_f = r0
    //     0xa910bc: stur            x0, [x1, #0xf]
    // 0xa910c0: r0 = 2
    //     0xa910c0: movz            x0, #0x2
    // 0xa910c4: ArrayStore: r1[0] = r0  ; List_8
    //     0xa910c4: stur            x0, [x1, #0x17]
    // 0xa910c8: r0 = Instance_TrimMode
    //     0xa910c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] Obj!TrimMode@d70201
    //     0xa910cc: ldr             x0, [x0, #0x9d0]
    // 0xa910d0: StoreField: r1->field_1f = r0
    //     0xa910d0: stur            w0, [x1, #0x1f]
    // 0xa910d4: ldur            x0, [fp, #-0x28]
    // 0xa910d8: StoreField: r1->field_4f = r0
    //     0xa910d8: stur            w0, [x1, #0x4f]
    // 0xa910dc: r0 = Instance_TextAlign
    //     0xa910dc: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xa910e0: StoreField: r1->field_53 = r0
    //     0xa910e0: stur            w0, [x1, #0x53]
    // 0xa910e4: ldur            x0, [fp, #-0x30]
    // 0xa910e8: StoreField: r1->field_23 = r0
    //     0xa910e8: stur            w0, [x1, #0x23]
    // 0xa910ec: ldur            x0, [fp, #-0x38]
    // 0xa910f0: StoreField: r1->field_27 = r0
    //     0xa910f0: stur            w0, [x1, #0x27]
    // 0xa910f4: r0 = "… "
    //     0xa910f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "… "
    //     0xa910f8: ldr             x0, [x0, #0x9d8]
    // 0xa910fc: StoreField: r1->field_3b = r0
    //     0xa910fc: stur            w0, [x1, #0x3b]
    // 0xa91100: r0 = true
    //     0xa91100: add             x0, NULL, #0x20  ; true
    // 0xa91104: StoreField: r1->field_37 = r0
    //     0xa91104: stur            w0, [x1, #0x37]
    // 0xa91108: r0 = Padding()
    //     0xa91108: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa9110c: mov             x2, x0
    // 0xa91110: r0 = Instance_EdgeInsets
    //     0xa91110: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9e0] Obj!EdgeInsets@d577a1
    //     0xa91114: ldr             x0, [x0, #0x9e0]
    // 0xa91118: stur            x2, [fp, #-0x18]
    // 0xa9111c: StoreField: r2->field_f = r0
    //     0xa9111c: stur            w0, [x2, #0xf]
    // 0xa91120: ldur            x0, [fp, #-0x40]
    // 0xa91124: StoreField: r2->field_b = r0
    //     0xa91124: stur            w0, [x2, #0xb]
    // 0xa91128: ldur            x0, [fp, #-0x20]
    // 0xa9112c: LoadField: r1 = r0->field_b
    //     0xa9112c: ldur            w1, [x0, #0xb]
    // 0xa91130: LoadField: r3 = r0->field_f
    //     0xa91130: ldur            w3, [x0, #0xf]
    // 0xa91134: DecompressPointer r3
    //     0xa91134: add             x3, x3, HEAP, lsl #32
    // 0xa91138: LoadField: r4 = r3->field_b
    //     0xa91138: ldur            w4, [x3, #0xb]
    // 0xa9113c: r3 = LoadInt32Instr(r1)
    //     0xa9113c: sbfx            x3, x1, #1, #0x1f
    // 0xa91140: stur            x3, [fp, #-0x48]
    // 0xa91144: r1 = LoadInt32Instr(r4)
    //     0xa91144: sbfx            x1, x4, #1, #0x1f
    // 0xa91148: cmp             x3, x1
    // 0xa9114c: b.ne            #0xa91158
    // 0xa91150: mov             x1, x0
    // 0xa91154: r0 = _growToNextCapacity()
    //     0xa91154: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa91158: ldur            x3, [fp, #-0x20]
    // 0xa9115c: ldur            x2, [fp, #-0x48]
    // 0xa91160: add             x0, x2, #1
    // 0xa91164: lsl             x1, x0, #1
    // 0xa91168: StoreField: r3->field_b = r1
    //     0xa91168: stur            w1, [x3, #0xb]
    // 0xa9116c: LoadField: r1 = r3->field_f
    //     0xa9116c: ldur            w1, [x3, #0xf]
    // 0xa91170: DecompressPointer r1
    //     0xa91170: add             x1, x1, HEAP, lsl #32
    // 0xa91174: ldur            x0, [fp, #-0x18]
    // 0xa91178: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa91178: add             x25, x1, x2, lsl #2
    //     0xa9117c: add             x25, x25, #0xf
    //     0xa91180: str             w0, [x25]
    //     0xa91184: tbz             w0, #0, #0xa911a0
    //     0xa91188: ldurb           w16, [x1, #-1]
    //     0xa9118c: ldurb           w17, [x0, #-1]
    //     0xa91190: and             x16, x17, x16, lsr #2
    //     0xa91194: tst             x16, HEAP, lsr #32
    //     0xa91198: b.eq            #0xa911a0
    //     0xa9119c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa911a0: b               #0xa911a8
    // 0xa911a4: mov             x3, x2
    // 0xa911a8: ldur            x0, [fp, #-8]
    // 0xa911ac: cmp             w0, NULL
    // 0xa911b0: b.ne            #0xa911bc
    // 0xa911b4: r1 = Null
    //     0xa911b4: mov             x1, NULL
    // 0xa911b8: b               #0xa911cc
    // 0xa911bc: LoadField: r1 = r0->field_1b
    //     0xa911bc: ldur            w1, [x0, #0x1b]
    // 0xa911c0: DecompressPointer r1
    //     0xa911c0: add             x1, x1, HEAP, lsl #32
    // 0xa911c4: LoadField: r2 = r1->field_b
    //     0xa911c4: ldur            w2, [x1, #0xb]
    // 0xa911c8: mov             x1, x2
    // 0xa911cc: cmp             w1, NULL
    // 0xa911d0: b.eq            #0xa914e8
    // 0xa911d4: r2 = LoadInt32Instr(r1)
    //     0xa911d4: sbfx            x2, x1, #1, #0x1f
    // 0xa911d8: cmp             x2, #1
    // 0xa911dc: r16 = true
    //     0xa911dc: add             x16, NULL, #0x20  ; true
    // 0xa911e0: r17 = false
    //     0xa911e0: add             x17, NULL, #0x30  ; false
    // 0xa911e4: csel            x4, x16, x17, ge
    // 0xa911e8: stur            x4, [fp, #-0x18]
    // 0xa911ec: cmp             w0, NULL
    // 0xa911f0: b.ne            #0xa911fc
    // 0xa911f4: r0 = Null
    //     0xa911f4: mov             x0, NULL
    // 0xa911f8: b               #0xa91208
    // 0xa911fc: LoadField: r1 = r0->field_1b
    //     0xa911fc: ldur            w1, [x0, #0x1b]
    // 0xa91200: DecompressPointer r1
    //     0xa91200: add             x1, x1, HEAP, lsl #32
    // 0xa91204: LoadField: r0 = r1->field_b
    //     0xa91204: ldur            w0, [x1, #0xb]
    // 0xa91208: cmp             w0, NULL
    // 0xa9120c: b.ne            #0xa91218
    // 0xa91210: r0 = 0
    //     0xa91210: movz            x0, #0
    // 0xa91214: b               #0xa91220
    // 0xa91218: r1 = LoadInt32Instr(r0)
    //     0xa91218: sbfx            x1, x0, #1, #0x1f
    // 0xa9121c: mov             x0, x1
    // 0xa91220: stur            x0, [fp, #-0x48]
    // 0xa91224: r1 = Function '<anonymous closure>':.
    //     0xa91224: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a0e8] AnonymousClosure: (0x9b3480), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa91228: ldr             x1, [x1, #0xe8]
    // 0xa9122c: r2 = Null
    //     0xa9122c: mov             x2, NULL
    // 0xa91230: r0 = AllocateClosure()
    //     0xa91230: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa91234: ldur            x2, [fp, #-0x10]
    // 0xa91238: r1 = Function '<anonymous closure>':.
    //     0xa91238: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a0f0] AnonymousClosure: (0xa91f90), in [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xc0c210)
    //     0xa9123c: ldr             x1, [x1, #0xf0]
    // 0xa91240: stur            x0, [fp, #-8]
    // 0xa91244: r0 = AllocateClosure()
    //     0xa91244: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa91248: stur            x0, [fp, #-0x28]
    // 0xa9124c: r0 = ListView()
    //     0xa9124c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xa91250: stur            x0, [fp, #-0x30]
    // 0xa91254: r16 = true
    //     0xa91254: add             x16, NULL, #0x20  ; true
    // 0xa91258: r30 = Instance_Axis
    //     0xa91258: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa9125c: stp             lr, x16, [SP, #8]
    // 0xa91260: r16 = Instance_NeverScrollableScrollPhysics
    //     0xa91260: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xa91264: ldr             x16, [x16, #0x1c8]
    // 0xa91268: str             x16, [SP]
    // 0xa9126c: mov             x1, x0
    // 0xa91270: ldur            x2, [fp, #-0x28]
    // 0xa91274: ldur            x3, [fp, #-0x48]
    // 0xa91278: ldur            x5, [fp, #-8]
    // 0xa9127c: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x6, scrollDirection, 0x5, shrinkWrap, 0x4, null]
    //     0xa9127c: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a0f8] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x6, "scrollDirection", 0x5, "shrinkWrap", 0x4, Null]
    //     0xa91280: ldr             x4, [x4, #0xf8]
    // 0xa91284: r0 = ListView.separated()
    //     0xa91284: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xa91288: r0 = SizedBox()
    //     0xa91288: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa9128c: mov             x1, x0
    // 0xa91290: r0 = inf
    //     0xa91290: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xa91294: ldr             x0, [x0, #0x9f8]
    // 0xa91298: stur            x1, [fp, #-8]
    // 0xa9129c: StoreField: r1->field_f = r0
    //     0xa9129c: stur            w0, [x1, #0xf]
    // 0xa912a0: r0 = 48.000000
    //     0xa912a0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xa912a4: ldr             x0, [x0, #0xad8]
    // 0xa912a8: StoreField: r1->field_13 = r0
    //     0xa912a8: stur            w0, [x1, #0x13]
    // 0xa912ac: ldur            x0, [fp, #-0x30]
    // 0xa912b0: StoreField: r1->field_b = r0
    //     0xa912b0: stur            w0, [x1, #0xb]
    // 0xa912b4: r0 = Padding()
    //     0xa912b4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa912b8: mov             x1, x0
    // 0xa912bc: r0 = Instance_EdgeInsets
    //     0xa912bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xa912c0: ldr             x0, [x0, #0xa00]
    // 0xa912c4: stur            x1, [fp, #-0x28]
    // 0xa912c8: StoreField: r1->field_f = r0
    //     0xa912c8: stur            w0, [x1, #0xf]
    // 0xa912cc: ldur            x0, [fp, #-8]
    // 0xa912d0: StoreField: r1->field_b = r0
    //     0xa912d0: stur            w0, [x1, #0xb]
    // 0xa912d4: r0 = Visibility()
    //     0xa912d4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa912d8: mov             x1, x0
    // 0xa912dc: ldur            x0, [fp, #-0x28]
    // 0xa912e0: stur            x1, [fp, #-8]
    // 0xa912e4: StoreField: r1->field_b = r0
    //     0xa912e4: stur            w0, [x1, #0xb]
    // 0xa912e8: r0 = Instance_SizedBox
    //     0xa912e8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa912ec: StoreField: r1->field_f = r0
    //     0xa912ec: stur            w0, [x1, #0xf]
    // 0xa912f0: ldur            x0, [fp, #-0x18]
    // 0xa912f4: StoreField: r1->field_13 = r0
    //     0xa912f4: stur            w0, [x1, #0x13]
    // 0xa912f8: r0 = false
    //     0xa912f8: add             x0, NULL, #0x30  ; false
    // 0xa912fc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa912fc: stur            w0, [x1, #0x17]
    // 0xa91300: StoreField: r1->field_1b = r0
    //     0xa91300: stur            w0, [x1, #0x1b]
    // 0xa91304: StoreField: r1->field_1f = r0
    //     0xa91304: stur            w0, [x1, #0x1f]
    // 0xa91308: StoreField: r1->field_23 = r0
    //     0xa91308: stur            w0, [x1, #0x23]
    // 0xa9130c: StoreField: r1->field_27 = r0
    //     0xa9130c: stur            w0, [x1, #0x27]
    // 0xa91310: StoreField: r1->field_2b = r0
    //     0xa91310: stur            w0, [x1, #0x2b]
    // 0xa91314: r0 = GestureDetector()
    //     0xa91314: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa91318: ldur            x2, [fp, #-0x10]
    // 0xa9131c: r1 = Function '<anonymous closure>':.
    //     0xa9131c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a100] AnonymousClosure: (0xa90680), in [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xc0c210)
    //     0xa91320: ldr             x1, [x1, #0x100]
    // 0xa91324: stur            x0, [fp, #-0x18]
    // 0xa91328: r0 = AllocateClosure()
    //     0xa91328: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa9132c: ldur            x2, [fp, #-0x10]
    // 0xa91330: r1 = Function '<anonymous closure>':.
    //     0xa91330: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a108] AnonymousClosure: (0xa914ec), in [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xc0c210)
    //     0xa91334: ldr             x1, [x1, #0x108]
    // 0xa91338: stur            x0, [fp, #-0x10]
    // 0xa9133c: r0 = AllocateClosure()
    //     0xa9133c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa91340: ldur            x16, [fp, #-0x10]
    // 0xa91344: stp             x0, x16, [SP, #8]
    // 0xa91348: r16 = Instance_Icon
    //     0xa91348: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa18] Obj!Icon@d65e71
    //     0xa9134c: ldr             x16, [x16, #0xa18]
    // 0xa91350: str             x16, [SP]
    // 0xa91354: ldur            x1, [fp, #-0x18]
    // 0xa91358: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, onTap, 0x2, onTapDown, 0x1, null]
    //     0xa91358: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa20] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "onTap", 0x2, "onTapDown", 0x1, Null]
    //     0xa9135c: ldr             x4, [x4, #0xa20]
    // 0xa91360: r0 = GestureDetector()
    //     0xa91360: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa91364: r0 = Align()
    //     0xa91364: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa91368: mov             x3, x0
    // 0xa9136c: r0 = Instance_Alignment
    //     0xa9136c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0xa91370: ldr             x0, [x0, #0xa28]
    // 0xa91374: stur            x3, [fp, #-0x10]
    // 0xa91378: StoreField: r3->field_f = r0
    //     0xa91378: stur            w0, [x3, #0xf]
    // 0xa9137c: ldur            x1, [fp, #-0x18]
    // 0xa91380: StoreField: r3->field_b = r1
    //     0xa91380: stur            w1, [x3, #0xb]
    // 0xa91384: r1 = Null
    //     0xa91384: mov             x1, NULL
    // 0xa91388: r2 = 4
    //     0xa91388: movz            x2, #0x4
    // 0xa9138c: r0 = AllocateArray()
    //     0xa9138c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa91390: mov             x2, x0
    // 0xa91394: ldur            x0, [fp, #-8]
    // 0xa91398: stur            x2, [fp, #-0x18]
    // 0xa9139c: StoreField: r2->field_f = r0
    //     0xa9139c: stur            w0, [x2, #0xf]
    // 0xa913a0: ldur            x0, [fp, #-0x10]
    // 0xa913a4: StoreField: r2->field_13 = r0
    //     0xa913a4: stur            w0, [x2, #0x13]
    // 0xa913a8: r1 = <Widget>
    //     0xa913a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa913ac: r0 = AllocateGrowableArray()
    //     0xa913ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa913b0: mov             x1, x0
    // 0xa913b4: ldur            x0, [fp, #-0x18]
    // 0xa913b8: stur            x1, [fp, #-8]
    // 0xa913bc: StoreField: r1->field_f = r0
    //     0xa913bc: stur            w0, [x1, #0xf]
    // 0xa913c0: r0 = 4
    //     0xa913c0: movz            x0, #0x4
    // 0xa913c4: StoreField: r1->field_b = r0
    //     0xa913c4: stur            w0, [x1, #0xb]
    // 0xa913c8: r0 = Stack()
    //     0xa913c8: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa913cc: mov             x2, x0
    // 0xa913d0: r0 = Instance_Alignment
    //     0xa913d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0xa913d4: ldr             x0, [x0, #0xa28]
    // 0xa913d8: stur            x2, [fp, #-0x10]
    // 0xa913dc: StoreField: r2->field_f = r0
    //     0xa913dc: stur            w0, [x2, #0xf]
    // 0xa913e0: r0 = Instance_StackFit
    //     0xa913e0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa913e4: ldr             x0, [x0, #0xfa8]
    // 0xa913e8: ArrayStore: r2[0] = r0  ; List_4
    //     0xa913e8: stur            w0, [x2, #0x17]
    // 0xa913ec: r0 = Instance_Clip
    //     0xa913ec: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa913f0: ldr             x0, [x0, #0x7e0]
    // 0xa913f4: StoreField: r2->field_1b = r0
    //     0xa913f4: stur            w0, [x2, #0x1b]
    // 0xa913f8: ldur            x0, [fp, #-8]
    // 0xa913fc: StoreField: r2->field_b = r0
    //     0xa913fc: stur            w0, [x2, #0xb]
    // 0xa91400: ldur            x0, [fp, #-0x20]
    // 0xa91404: LoadField: r1 = r0->field_b
    //     0xa91404: ldur            w1, [x0, #0xb]
    // 0xa91408: LoadField: r3 = r0->field_f
    //     0xa91408: ldur            w3, [x0, #0xf]
    // 0xa9140c: DecompressPointer r3
    //     0xa9140c: add             x3, x3, HEAP, lsl #32
    // 0xa91410: LoadField: r4 = r3->field_b
    //     0xa91410: ldur            w4, [x3, #0xb]
    // 0xa91414: r3 = LoadInt32Instr(r1)
    //     0xa91414: sbfx            x3, x1, #1, #0x1f
    // 0xa91418: stur            x3, [fp, #-0x48]
    // 0xa9141c: r1 = LoadInt32Instr(r4)
    //     0xa9141c: sbfx            x1, x4, #1, #0x1f
    // 0xa91420: cmp             x3, x1
    // 0xa91424: b.ne            #0xa91430
    // 0xa91428: mov             x1, x0
    // 0xa9142c: r0 = _growToNextCapacity()
    //     0xa9142c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa91430: ldur            x2, [fp, #-0x20]
    // 0xa91434: ldur            x3, [fp, #-0x48]
    // 0xa91438: add             x0, x3, #1
    // 0xa9143c: lsl             x1, x0, #1
    // 0xa91440: StoreField: r2->field_b = r1
    //     0xa91440: stur            w1, [x2, #0xb]
    // 0xa91444: LoadField: r1 = r2->field_f
    //     0xa91444: ldur            w1, [x2, #0xf]
    // 0xa91448: DecompressPointer r1
    //     0xa91448: add             x1, x1, HEAP, lsl #32
    // 0xa9144c: ldur            x0, [fp, #-0x10]
    // 0xa91450: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa91450: add             x25, x1, x3, lsl #2
    //     0xa91454: add             x25, x25, #0xf
    //     0xa91458: str             w0, [x25]
    //     0xa9145c: tbz             w0, #0, #0xa91478
    //     0xa91460: ldurb           w16, [x1, #-1]
    //     0xa91464: ldurb           w17, [x0, #-1]
    //     0xa91468: and             x16, x17, x16, lsr #2
    //     0xa9146c: tst             x16, HEAP, lsr #32
    //     0xa91470: b.eq            #0xa91478
    //     0xa91474: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa91478: r0 = Column()
    //     0xa91478: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa9147c: r1 = Instance_Axis
    //     0xa9147c: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa91480: StoreField: r0->field_f = r1
    //     0xa91480: stur            w1, [x0, #0xf]
    // 0xa91484: r1 = Instance_MainAxisAlignment
    //     0xa91484: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa91488: ldr             x1, [x1, #0xa08]
    // 0xa9148c: StoreField: r0->field_13 = r1
    //     0xa9148c: stur            w1, [x0, #0x13]
    // 0xa91490: r1 = Instance_MainAxisSize
    //     0xa91490: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa91494: ldr             x1, [x1, #0xa10]
    // 0xa91498: ArrayStore: r0[0] = r1  ; List_4
    //     0xa91498: stur            w1, [x0, #0x17]
    // 0xa9149c: r1 = Instance_CrossAxisAlignment
    //     0xa9149c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa914a0: ldr             x1, [x1, #0x890]
    // 0xa914a4: StoreField: r0->field_1b = r1
    //     0xa914a4: stur            w1, [x0, #0x1b]
    // 0xa914a8: r1 = Instance_VerticalDirection
    //     0xa914a8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa914ac: ldr             x1, [x1, #0xa20]
    // 0xa914b0: StoreField: r0->field_23 = r1
    //     0xa914b0: stur            w1, [x0, #0x23]
    // 0xa914b4: r1 = Instance_Clip
    //     0xa914b4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa914b8: ldr             x1, [x1, #0x38]
    // 0xa914bc: StoreField: r0->field_2b = r1
    //     0xa914bc: stur            w1, [x0, #0x2b]
    // 0xa914c0: StoreField: r0->field_2f = rZR
    //     0xa914c0: stur            xzr, [x0, #0x2f]
    // 0xa914c4: ldur            x1, [fp, #-0x20]
    // 0xa914c8: StoreField: r0->field_b = r1
    //     0xa914c8: stur            w1, [x0, #0xb]
    // 0xa914cc: LeaveFrame
    //     0xa914cc: mov             SP, fp
    //     0xa914d0: ldp             fp, lr, [SP], #0x10
    // 0xa914d4: ret
    //     0xa914d4: ret             
    // 0xa914d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa914d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa914dc: b               #0xa906fc
    // 0xa914e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa914e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa914e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa914e4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa914e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa914e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa914ec, size: 0x100
    // 0xa914ec: EnterFrame
    //     0xa914ec: stp             fp, lr, [SP, #-0x10]!
    //     0xa914f0: mov             fp, SP
    // 0xa914f4: AllocStack(0x28)
    //     0xa914f4: sub             SP, SP, #0x28
    // 0xa914f8: SetupParameters()
    //     0xa914f8: ldr             x0, [fp, #0x10]
    //     0xa914fc: ldur            w1, [x0, #0x17]
    //     0xa91500: add             x1, x1, HEAP, lsl #32
    //     0xa91504: stur            x1, [fp, #-0x10]
    // 0xa91508: CheckStackOverflow
    //     0xa91508: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9150c: cmp             SP, x16
    //     0xa91510: b.ls            #0xa915e0
    // 0xa91514: LoadField: r0 = r1->field_b
    //     0xa91514: ldur            w0, [x1, #0xb]
    // 0xa91518: DecompressPointer r0
    //     0xa91518: add             x0, x0, HEAP, lsl #32
    // 0xa9151c: stur            x0, [fp, #-8]
    // 0xa91520: LoadField: r2 = r0->field_f
    //     0xa91520: ldur            w2, [x0, #0xf]
    // 0xa91524: DecompressPointer r2
    //     0xa91524: add             x2, x2, HEAP, lsl #32
    // 0xa91528: LoadField: r3 = r2->field_13
    //     0xa91528: ldur            w3, [x2, #0x13]
    // 0xa9152c: DecompressPointer r3
    //     0xa9152c: add             x3, x3, HEAP, lsl #32
    // 0xa91530: cmp             w3, NULL
    // 0xa91534: b.eq            #0xa915d0
    // 0xa91538: LoadField: r3 = r2->field_b
    //     0xa91538: ldur            w3, [x2, #0xb]
    // 0xa9153c: DecompressPointer r3
    //     0xa9153c: add             x3, x3, HEAP, lsl #32
    // 0xa91540: cmp             w3, NULL
    // 0xa91544: b.eq            #0xa915e8
    // 0xa91548: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xa91548: ldur            w2, [x3, #0x17]
    // 0xa9154c: DecompressPointer r2
    //     0xa9154c: add             x2, x2, HEAP, lsl #32
    // 0xa91550: r16 = "flag_dots"
    //     0xa91550: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa30] "flag_dots"
    //     0xa91554: ldr             x16, [x16, #0xa30]
    // 0xa91558: stp             x16, x2, [SP, #8]
    // 0xa9155c: r16 = ""
    //     0xa9155c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa91560: str             x16, [SP]
    // 0xa91564: r4 = 0
    //     0xa91564: movz            x4, #0
    // 0xa91568: ldr             x0, [SP, #0x10]
    // 0xa9156c: r5 = UnlinkedCall_0x613b5c
    //     0xa9156c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a110] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa91570: ldp             x5, lr, [x16, #0x110]
    // 0xa91574: blr             lr
    // 0xa91578: ldur            x0, [fp, #-8]
    // 0xa9157c: LoadField: r1 = r0->field_f
    //     0xa9157c: ldur            w1, [x0, #0xf]
    // 0xa91580: DecompressPointer r1
    //     0xa91580: add             x1, x1, HEAP, lsl #32
    // 0xa91584: ldur            x0, [fp, #-0x10]
    // 0xa91588: LoadField: r2 = r0->field_f
    //     0xa91588: ldur            w2, [x0, #0xf]
    // 0xa9158c: DecompressPointer r2
    //     0xa9158c: add             x2, x2, HEAP, lsl #32
    // 0xa91590: LoadField: r3 = r1->field_13
    //     0xa91590: ldur            w3, [x1, #0x13]
    // 0xa91594: DecompressPointer r3
    //     0xa91594: add             x3, x3, HEAP, lsl #32
    // 0xa91598: LoadField: r4 = r0->field_13
    //     0xa91598: ldur            w4, [x0, #0x13]
    // 0xa9159c: DecompressPointer r4
    //     0xa9159c: add             x4, x4, HEAP, lsl #32
    // 0xa915a0: cmp             w4, NULL
    // 0xa915a4: b.ne            #0xa915b0
    // 0xa915a8: r0 = Null
    //     0xa915a8: mov             x0, NULL
    // 0xa915ac: b               #0xa915b8
    // 0xa915b0: LoadField: r0 = r4->field_b
    //     0xa915b0: ldur            w0, [x4, #0xb]
    // 0xa915b4: DecompressPointer r0
    //     0xa915b4: add             x0, x0, HEAP, lsl #32
    // 0xa915b8: cmp             w0, NULL
    // 0xa915bc: b.ne            #0xa915c8
    // 0xa915c0: r5 = ""
    //     0xa915c0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa915c4: b               #0xa915cc
    // 0xa915c8: mov             x5, x0
    // 0xa915cc: r0 = showMenuItem()
    //     0xa915cc: bl              #0xa915ec  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::showMenuItem
    // 0xa915d0: r0 = Null
    //     0xa915d0: mov             x0, NULL
    // 0xa915d4: LeaveFrame
    //     0xa915d4: mov             SP, fp
    //     0xa915d8: ldp             fp, lr, [SP], #0x10
    // 0xa915dc: ret
    //     0xa915dc: ret             
    // 0xa915e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa915e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa915e4: b               #0xa91514
    // 0xa915e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa915e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ showMenuItem(/* No info */) {
    // ** addr: 0xa915ec, size: 0x6b0
    // 0xa915ec: EnterFrame
    //     0xa915ec: stp             fp, lr, [SP, #-0x10]!
    //     0xa915f0: mov             fp, SP
    // 0xa915f4: AllocStack(0x98)
    //     0xa915f4: sub             SP, SP, #0x98
    // 0xa915f8: SetupParameters(_ReviewWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */)
    //     0xa915f8: mov             x0, x1
    //     0xa915fc: stur            x1, [fp, #-8]
    //     0xa91600: mov             x1, x2
    //     0xa91604: stur            x2, [fp, #-0x10]
    //     0xa91608: mov             x2, x5
    //     0xa9160c: stur            x3, [fp, #-0x18]
    //     0xa91610: stur            x5, [fp, #-0x20]
    // 0xa91614: CheckStackOverflow
    //     0xa91614: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa91618: cmp             SP, x16
    //     0xa9161c: b.ls            #0xa91c14
    // 0xa91620: r1 = 2
    //     0xa91620: movz            x1, #0x2
    // 0xa91624: r0 = AllocateContext()
    //     0xa91624: bl              #0x16f6108  ; AllocateContextStub
    // 0xa91628: mov             x4, x0
    // 0xa9162c: ldur            x3, [fp, #-8]
    // 0xa91630: stur            x4, [fp, #-0x28]
    // 0xa91634: StoreField: r4->field_f = r3
    //     0xa91634: stur            w3, [x4, #0xf]
    // 0xa91638: ldur            x2, [fp, #-0x20]
    // 0xa9163c: StoreField: r4->field_13 = r2
    //     0xa9163c: stur            w2, [x4, #0x13]
    // 0xa91640: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xa91640: ldur            w1, [x3, #0x17]
    // 0xa91644: DecompressPointer r1
    //     0xa91644: add             x1, x1, HEAP, lsl #32
    // 0xa91648: r0 = LoadClassIdInstr(r1)
    //     0xa91648: ldur            x0, [x1, #-1]
    //     0xa9164c: ubfx            x0, x0, #0xc, #0x14
    // 0xa91650: r0 = GDT[cid_x0 + -0xfe]()
    //     0xa91650: sub             lr, x0, #0xfe
    //     0xa91654: ldr             lr, [x21, lr, lsl #3]
    //     0xa91658: blr             lr
    // 0xa9165c: r1 = 60
    //     0xa9165c: movz            x1, #0x3c
    // 0xa91660: branchIfSmi(r0, 0xa9166c)
    //     0xa91660: tbz             w0, #0, #0xa9166c
    // 0xa91664: r1 = LoadClassIdInstr(r0)
    //     0xa91664: ldur            x1, [x0, #-1]
    //     0xa91668: ubfx            x1, x1, #0xc, #0x14
    // 0xa9166c: r16 = true
    //     0xa9166c: add             x16, NULL, #0x20  ; true
    // 0xa91670: stp             x16, x0, [SP]
    // 0xa91674: mov             x0, x1
    // 0xa91678: mov             lr, x0
    // 0xa9167c: ldr             lr, [x21, lr, lsl #3]
    // 0xa91680: blr             lr
    // 0xa91684: tbz             w0, #4, #0xa91694
    // 0xa91688: d0 = 120.000000
    //     0xa91688: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2fa38] IMM: double(120) from 0x405e000000000000
    //     0xa9168c: ldr             d0, [x17, #0xa38]
    // 0xa91690: b               #0xa91698
    // 0xa91694: d0 = 100.000000
    //     0xa91694: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xa91698: ldur            x0, [fp, #-0x18]
    // 0xa9169c: stur            d0, [fp, #-0x50]
    // 0xa916a0: r0 = BoxConstraints()
    //     0xa916a0: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xa916a4: stur            x0, [fp, #-0x20]
    // 0xa916a8: StoreField: r0->field_7 = rZR
    //     0xa916a8: stur            xzr, [x0, #7]
    // 0xa916ac: ldur            d0, [fp, #-0x50]
    // 0xa916b0: StoreField: r0->field_f = d0
    //     0xa916b0: stur            d0, [x0, #0xf]
    // 0xa916b4: ArrayStore: r0[0] = rZR  ; List_8
    //     0xa916b4: stur            xzr, [x0, #0x17]
    // 0xa916b8: d0 = inf
    //     0xa916b8: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xa916bc: StoreField: r0->field_1f = d0
    //     0xa916bc: stur            d0, [x0, #0x1f]
    // 0xa916c0: ldur            x1, [fp, #-0x18]
    // 0xa916c4: cmp             w1, NULL
    // 0xa916c8: b.ne            #0xa916d4
    // 0xa916cc: r2 = Null
    //     0xa916cc: mov             x2, NULL
    // 0xa916d0: b               #0xa91700
    // 0xa916d4: LoadField: d0 = r1->field_7
    //     0xa916d4: ldur            d0, [x1, #7]
    // 0xa916d8: r2 = inline_Allocate_Double()
    //     0xa916d8: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xa916dc: add             x2, x2, #0x10
    //     0xa916e0: cmp             x3, x2
    //     0xa916e4: b.ls            #0xa91c1c
    //     0xa916e8: str             x2, [THR, #0x50]  ; THR::top
    //     0xa916ec: sub             x2, x2, #0xf
    //     0xa916f0: movz            x3, #0xe15c
    //     0xa916f4: movk            x3, #0x3, lsl #16
    //     0xa916f8: stur            x3, [x2, #-1]
    // 0xa916fc: StoreField: r2->field_7 = d0
    //     0xa916fc: stur            d0, [x2, #7]
    // 0xa91700: cmp             w2, NULL
    // 0xa91704: b.ne            #0xa91710
    // 0xa91708: d0 = 0.000000
    //     0xa91708: eor             v0.16b, v0.16b, v0.16b
    // 0xa9170c: b               #0xa91714
    // 0xa91710: LoadField: d0 = r2->field_7
    //     0xa91710: ldur            d0, [x2, #7]
    // 0xa91714: stur            d0, [fp, #-0x68]
    // 0xa91718: cmp             w1, NULL
    // 0xa9171c: b.ne            #0xa91728
    // 0xa91720: r2 = Null
    //     0xa91720: mov             x2, NULL
    // 0xa91724: b               #0xa91754
    // 0xa91728: LoadField: d1 = r1->field_f
    //     0xa91728: ldur            d1, [x1, #0xf]
    // 0xa9172c: r2 = inline_Allocate_Double()
    //     0xa9172c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xa91730: add             x2, x2, #0x10
    //     0xa91734: cmp             x3, x2
    //     0xa91738: b.ls            #0xa91c38
    //     0xa9173c: str             x2, [THR, #0x50]  ; THR::top
    //     0xa91740: sub             x2, x2, #0xf
    //     0xa91744: movz            x3, #0xe15c
    //     0xa91748: movk            x3, #0x3, lsl #16
    //     0xa9174c: stur            x3, [x2, #-1]
    // 0xa91750: StoreField: r2->field_7 = d1
    //     0xa91750: stur            d1, [x2, #7]
    // 0xa91754: cmp             w2, NULL
    // 0xa91758: b.ne            #0xa91764
    // 0xa9175c: d2 = 0.000000
    //     0xa9175c: eor             v2.16b, v2.16b, v2.16b
    // 0xa91760: b               #0xa9176c
    // 0xa91764: LoadField: d1 = r2->field_7
    //     0xa91764: ldur            d1, [x2, #7]
    // 0xa91768: mov             v2.16b, v1.16b
    // 0xa9176c: d1 = 50.000000
    //     0xa9176c: ldr             d1, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0xa91770: fsub            d3, d2, d1
    // 0xa91774: stur            d3, [fp, #-0x60]
    // 0xa91778: cmp             w1, NULL
    // 0xa9177c: b.ne            #0xa91788
    // 0xa91780: r2 = Null
    //     0xa91780: mov             x2, NULL
    // 0xa91784: b               #0xa917b4
    // 0xa91788: LoadField: d2 = r1->field_7
    //     0xa91788: ldur            d2, [x1, #7]
    // 0xa9178c: r2 = inline_Allocate_Double()
    //     0xa9178c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xa91790: add             x2, x2, #0x10
    //     0xa91794: cmp             x3, x2
    //     0xa91798: b.ls            #0xa91c54
    //     0xa9179c: str             x2, [THR, #0x50]  ; THR::top
    //     0xa917a0: sub             x2, x2, #0xf
    //     0xa917a4: movz            x3, #0xe15c
    //     0xa917a8: movk            x3, #0x3, lsl #16
    //     0xa917ac: stur            x3, [x2, #-1]
    // 0xa917b0: StoreField: r2->field_7 = d2
    //     0xa917b0: stur            d2, [x2, #7]
    // 0xa917b4: cmp             w2, NULL
    // 0xa917b8: b.ne            #0xa917c4
    // 0xa917bc: d2 = 0.000000
    //     0xa917bc: eor             v2.16b, v2.16b, v2.16b
    // 0xa917c0: b               #0xa917c8
    // 0xa917c4: LoadField: d2 = r2->field_7
    //     0xa917c4: ldur            d2, [x2, #7]
    // 0xa917c8: fadd            d4, d2, d1
    // 0xa917cc: stur            d4, [fp, #-0x58]
    // 0xa917d0: cmp             w1, NULL
    // 0xa917d4: b.ne            #0xa917e0
    // 0xa917d8: r1 = Null
    //     0xa917d8: mov             x1, NULL
    // 0xa917dc: b               #0xa9180c
    // 0xa917e0: LoadField: d1 = r1->field_f
    //     0xa917e0: ldur            d1, [x1, #0xf]
    // 0xa917e4: r1 = inline_Allocate_Double()
    //     0xa917e4: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xa917e8: add             x1, x1, #0x10
    //     0xa917ec: cmp             x2, x1
    //     0xa917f0: b.ls            #0xa91c78
    //     0xa917f4: str             x1, [THR, #0x50]  ; THR::top
    //     0xa917f8: sub             x1, x1, #0xf
    //     0xa917fc: movz            x2, #0xe15c
    //     0xa91800: movk            x2, #0x3, lsl #16
    //     0xa91804: stur            x2, [x1, #-1]
    // 0xa91808: StoreField: r1->field_7 = d1
    //     0xa91808: stur            d1, [x1, #7]
    // 0xa9180c: cmp             w1, NULL
    // 0xa91810: b.ne            #0xa9181c
    // 0xa91814: d1 = 0.000000
    //     0xa91814: eor             v1.16b, v1.16b, v1.16b
    // 0xa91818: b               #0xa91820
    // 0xa9181c: LoadField: d1 = r1->field_7
    //     0xa9181c: ldur            d1, [x1, #7]
    // 0xa91820: ldur            x1, [fp, #-8]
    // 0xa91824: ldur            x2, [fp, #-0x28]
    // 0xa91828: stur            d1, [fp, #-0x50]
    // 0xa9182c: r0 = RelativeRect()
    //     0xa9182c: bl              #0x9abaf4  ; AllocateRelativeRectStub -> RelativeRect (size=0x28)
    // 0xa91830: ldur            d0, [fp, #-0x68]
    // 0xa91834: stur            x0, [fp, #-0x18]
    // 0xa91838: StoreField: r0->field_7 = d0
    //     0xa91838: stur            d0, [x0, #7]
    // 0xa9183c: ldur            d0, [fp, #-0x60]
    // 0xa91840: StoreField: r0->field_f = d0
    //     0xa91840: stur            d0, [x0, #0xf]
    // 0xa91844: ldur            d0, [fp, #-0x58]
    // 0xa91848: ArrayStore: r0[0] = d0  ; List_8
    //     0xa91848: stur            d0, [x0, #0x17]
    // 0xa9184c: ldur            d0, [fp, #-0x50]
    // 0xa91850: StoreField: r0->field_1f = d0
    //     0xa91850: stur            d0, [x0, #0x1f]
    // 0xa91854: r1 = <Widget>
    //     0xa91854: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa91858: r2 = 0
    //     0xa91858: movz            x2, #0
    // 0xa9185c: r0 = _GrowableList()
    //     0xa9185c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xa91860: mov             x4, x0
    // 0xa91864: ldur            x3, [fp, #-8]
    // 0xa91868: stur            x4, [fp, #-0x30]
    // 0xa9186c: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xa9186c: ldur            w1, [x3, #0x17]
    // 0xa91870: DecompressPointer r1
    //     0xa91870: add             x1, x1, HEAP, lsl #32
    // 0xa91874: ldur            x5, [fp, #-0x28]
    // 0xa91878: LoadField: r2 = r5->field_13
    //     0xa91878: ldur            w2, [x5, #0x13]
    // 0xa9187c: DecompressPointer r2
    //     0xa9187c: add             x2, x2, HEAP, lsl #32
    // 0xa91880: r0 = LoadClassIdInstr(r1)
    //     0xa91880: ldur            x0, [x1, #-1]
    //     0xa91884: ubfx            x0, x0, #0xc, #0x14
    // 0xa91888: r0 = GDT[cid_x0 + -0xfe]()
    //     0xa91888: sub             lr, x0, #0xfe
    //     0xa9188c: ldr             lr, [x21, lr, lsl #3]
    //     0xa91890: blr             lr
    // 0xa91894: r16 = true
    //     0xa91894: add             x16, NULL, #0x20  ; true
    // 0xa91898: cmp             w0, w16
    // 0xa9189c: b.ne            #0xa91900
    // 0xa918a0: ldur            x0, [fp, #-0x30]
    // 0xa918a4: LoadField: r1 = r0->field_b
    //     0xa918a4: ldur            w1, [x0, #0xb]
    // 0xa918a8: LoadField: r2 = r0->field_f
    //     0xa918a8: ldur            w2, [x0, #0xf]
    // 0xa918ac: DecompressPointer r2
    //     0xa918ac: add             x2, x2, HEAP, lsl #32
    // 0xa918b0: LoadField: r3 = r2->field_b
    //     0xa918b0: ldur            w3, [x2, #0xb]
    // 0xa918b4: r2 = LoadInt32Instr(r1)
    //     0xa918b4: sbfx            x2, x1, #1, #0x1f
    // 0xa918b8: stur            x2, [fp, #-0x38]
    // 0xa918bc: r1 = LoadInt32Instr(r3)
    //     0xa918bc: sbfx            x1, x3, #1, #0x1f
    // 0xa918c0: cmp             x2, x1
    // 0xa918c4: b.ne            #0xa918d0
    // 0xa918c8: mov             x1, x0
    // 0xa918cc: r0 = _growToNextCapacity()
    //     0xa918cc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa918d0: ldur            x0, [fp, #-0x30]
    // 0xa918d4: ldur            x1, [fp, #-0x38]
    // 0xa918d8: add             x2, x1, #1
    // 0xa918dc: lsl             x3, x2, #1
    // 0xa918e0: StoreField: r0->field_b = r3
    //     0xa918e0: stur            w3, [x0, #0xb]
    // 0xa918e4: LoadField: r2 = r0->field_f
    //     0xa918e4: ldur            w2, [x0, #0xf]
    // 0xa918e8: DecompressPointer r2
    //     0xa918e8: add             x2, x2, HEAP, lsl #32
    // 0xa918ec: add             x3, x2, x1, lsl #2
    // 0xa918f0: r16 = Instance_Icon
    //     0xa918f0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa48] Obj!Icon@d65eb1
    //     0xa918f4: ldr             x16, [x16, #0xa48]
    // 0xa918f8: StoreField: r3->field_f = r16
    //     0xa918f8: stur            w16, [x3, #0xf]
    // 0xa918fc: b               #0xa91904
    // 0xa91900: ldur            x0, [fp, #-0x30]
    // 0xa91904: LoadField: r1 = r0->field_b
    //     0xa91904: ldur            w1, [x0, #0xb]
    // 0xa91908: LoadField: r2 = r0->field_f
    //     0xa91908: ldur            w2, [x0, #0xf]
    // 0xa9190c: DecompressPointer r2
    //     0xa9190c: add             x2, x2, HEAP, lsl #32
    // 0xa91910: LoadField: r3 = r2->field_b
    //     0xa91910: ldur            w3, [x2, #0xb]
    // 0xa91914: r2 = LoadInt32Instr(r1)
    //     0xa91914: sbfx            x2, x1, #1, #0x1f
    // 0xa91918: stur            x2, [fp, #-0x38]
    // 0xa9191c: r1 = LoadInt32Instr(r3)
    //     0xa9191c: sbfx            x1, x3, #1, #0x1f
    // 0xa91920: cmp             x2, x1
    // 0xa91924: b.ne            #0xa91930
    // 0xa91928: mov             x1, x0
    // 0xa9192c: r0 = _growToNextCapacity()
    //     0xa9192c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa91930: ldur            x1, [fp, #-8]
    // 0xa91934: ldur            x4, [fp, #-0x28]
    // 0xa91938: ldur            x3, [fp, #-0x30]
    // 0xa9193c: ldur            x0, [fp, #-0x38]
    // 0xa91940: add             x2, x0, #1
    // 0xa91944: lsl             x5, x2, #1
    // 0xa91948: StoreField: r3->field_b = r5
    //     0xa91948: stur            w5, [x3, #0xb]
    // 0xa9194c: LoadField: r2 = r3->field_f
    //     0xa9194c: ldur            w2, [x3, #0xf]
    // 0xa91950: DecompressPointer r2
    //     0xa91950: add             x2, x2, HEAP, lsl #32
    // 0xa91954: add             x5, x2, x0, lsl #2
    // 0xa91958: r16 = Instance_SizedBox
    //     0xa91958: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xa9195c: ldr             x16, [x16, #0xa50]
    // 0xa91960: StoreField: r5->field_f = r16
    //     0xa91960: stur            w16, [x5, #0xf]
    // 0xa91964: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa91964: ldur            w0, [x1, #0x17]
    // 0xa91968: DecompressPointer r0
    //     0xa91968: add             x0, x0, HEAP, lsl #32
    // 0xa9196c: LoadField: r2 = r4->field_13
    //     0xa9196c: ldur            w2, [x4, #0x13]
    // 0xa91970: DecompressPointer r2
    //     0xa91970: add             x2, x2, HEAP, lsl #32
    // 0xa91974: r1 = LoadClassIdInstr(r0)
    //     0xa91974: ldur            x1, [x0, #-1]
    //     0xa91978: ubfx            x1, x1, #0xc, #0x14
    // 0xa9197c: mov             x16, x0
    // 0xa91980: mov             x0, x1
    // 0xa91984: mov             x1, x16
    // 0xa91988: r0 = GDT[cid_x0 + -0xfe]()
    //     0xa91988: sub             lr, x0, #0xfe
    //     0xa9198c: ldr             lr, [x21, lr, lsl #3]
    //     0xa91990: blr             lr
    // 0xa91994: r1 = 60
    //     0xa91994: movz            x1, #0x3c
    // 0xa91998: branchIfSmi(r0, 0xa919a4)
    //     0xa91998: tbz             w0, #0, #0xa919a4
    // 0xa9199c: r1 = LoadClassIdInstr(r0)
    //     0xa9199c: ldur            x1, [x0, #-1]
    //     0xa919a0: ubfx            x1, x1, #0xc, #0x14
    // 0xa919a4: r16 = true
    //     0xa919a4: add             x16, NULL, #0x20  ; true
    // 0xa919a8: stp             x16, x0, [SP]
    // 0xa919ac: mov             x0, x1
    // 0xa919b0: mov             lr, x0
    // 0xa919b4: ldr             lr, [x21, lr, lsl #3]
    // 0xa919b8: blr             lr
    // 0xa919bc: tbz             w0, #4, #0xa919cc
    // 0xa919c0: r0 = "Flag as abusive"
    //     0xa919c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa60] "Flag as abusive"
    //     0xa919c4: ldr             x0, [x0, #0xa60]
    // 0xa919c8: b               #0xa919d4
    // 0xa919cc: r0 = "Flagged"
    //     0xa919cc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa58] "Flagged"
    //     0xa919d0: ldr             x0, [x0, #0xa58]
    // 0xa919d4: ldur            x1, [fp, #-0x10]
    // 0xa919d8: stur            x0, [fp, #-8]
    // 0xa919dc: r0 = of()
    //     0xa919dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa919e0: LoadField: r1 = r0->field_87
    //     0xa919e0: ldur            w1, [x0, #0x87]
    // 0xa919e4: DecompressPointer r1
    //     0xa919e4: add             x1, x1, HEAP, lsl #32
    // 0xa919e8: LoadField: r0 = r1->field_33
    //     0xa919e8: ldur            w0, [x1, #0x33]
    // 0xa919ec: DecompressPointer r0
    //     0xa919ec: add             x0, x0, HEAP, lsl #32
    // 0xa919f0: cmp             w0, NULL
    // 0xa919f4: b.ne            #0xa91a00
    // 0xa919f8: r2 = Null
    //     0xa919f8: mov             x2, NULL
    // 0xa919fc: b               #0xa91a24
    // 0xa91a00: r16 = 12.000000
    //     0xa91a00: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa91a04: ldr             x16, [x16, #0x9e8]
    // 0xa91a08: r30 = Instance_Color
    //     0xa91a08: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa91a0c: stp             lr, x16, [SP]
    // 0xa91a10: mov             x1, x0
    // 0xa91a14: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa91a14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa91a18: ldr             x4, [x4, #0xaa0]
    // 0xa91a1c: r0 = copyWith()
    //     0xa91a1c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa91a20: mov             x2, x0
    // 0xa91a24: ldur            x1, [fp, #-0x30]
    // 0xa91a28: ldur            x0, [fp, #-8]
    // 0xa91a2c: stur            x2, [fp, #-0x40]
    // 0xa91a30: r0 = Text()
    //     0xa91a30: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa91a34: mov             x2, x0
    // 0xa91a38: ldur            x0, [fp, #-8]
    // 0xa91a3c: stur            x2, [fp, #-0x48]
    // 0xa91a40: StoreField: r2->field_b = r0
    //     0xa91a40: stur            w0, [x2, #0xb]
    // 0xa91a44: ldur            x0, [fp, #-0x40]
    // 0xa91a48: StoreField: r2->field_13 = r0
    //     0xa91a48: stur            w0, [x2, #0x13]
    // 0xa91a4c: ldur            x0, [fp, #-0x30]
    // 0xa91a50: LoadField: r1 = r0->field_b
    //     0xa91a50: ldur            w1, [x0, #0xb]
    // 0xa91a54: LoadField: r3 = r0->field_f
    //     0xa91a54: ldur            w3, [x0, #0xf]
    // 0xa91a58: DecompressPointer r3
    //     0xa91a58: add             x3, x3, HEAP, lsl #32
    // 0xa91a5c: LoadField: r4 = r3->field_b
    //     0xa91a5c: ldur            w4, [x3, #0xb]
    // 0xa91a60: r3 = LoadInt32Instr(r1)
    //     0xa91a60: sbfx            x3, x1, #1, #0x1f
    // 0xa91a64: stur            x3, [fp, #-0x38]
    // 0xa91a68: r1 = LoadInt32Instr(r4)
    //     0xa91a68: sbfx            x1, x4, #1, #0x1f
    // 0xa91a6c: cmp             x3, x1
    // 0xa91a70: b.ne            #0xa91a7c
    // 0xa91a74: mov             x1, x0
    // 0xa91a78: r0 = _growToNextCapacity()
    //     0xa91a78: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa91a7c: ldur            x2, [fp, #-0x30]
    // 0xa91a80: ldur            x3, [fp, #-0x38]
    // 0xa91a84: add             x0, x3, #1
    // 0xa91a88: lsl             x1, x0, #1
    // 0xa91a8c: StoreField: r2->field_b = r1
    //     0xa91a8c: stur            w1, [x2, #0xb]
    // 0xa91a90: LoadField: r1 = r2->field_f
    //     0xa91a90: ldur            w1, [x2, #0xf]
    // 0xa91a94: DecompressPointer r1
    //     0xa91a94: add             x1, x1, HEAP, lsl #32
    // 0xa91a98: ldur            x0, [fp, #-0x48]
    // 0xa91a9c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa91a9c: add             x25, x1, x3, lsl #2
    //     0xa91aa0: add             x25, x25, #0xf
    //     0xa91aa4: str             w0, [x25]
    //     0xa91aa8: tbz             w0, #0, #0xa91ac4
    //     0xa91aac: ldurb           w16, [x1, #-1]
    //     0xa91ab0: ldurb           w17, [x0, #-1]
    //     0xa91ab4: and             x16, x17, x16, lsr #2
    //     0xa91ab8: tst             x16, HEAP, lsr #32
    //     0xa91abc: b.eq            #0xa91ac4
    //     0xa91ac0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa91ac4: r0 = Row()
    //     0xa91ac4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa91ac8: mov             x2, x0
    // 0xa91acc: r0 = Instance_Axis
    //     0xa91acc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa91ad0: stur            x2, [fp, #-8]
    // 0xa91ad4: StoreField: r2->field_f = r0
    //     0xa91ad4: stur            w0, [x2, #0xf]
    // 0xa91ad8: r0 = Instance_MainAxisAlignment
    //     0xa91ad8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa91adc: ldr             x0, [x0, #0xa08]
    // 0xa91ae0: StoreField: r2->field_13 = r0
    //     0xa91ae0: stur            w0, [x2, #0x13]
    // 0xa91ae4: r0 = Instance_MainAxisSize
    //     0xa91ae4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa91ae8: ldr             x0, [x0, #0xa10]
    // 0xa91aec: ArrayStore: r2[0] = r0  ; List_4
    //     0xa91aec: stur            w0, [x2, #0x17]
    // 0xa91af0: r0 = Instance_CrossAxisAlignment
    //     0xa91af0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa91af4: ldr             x0, [x0, #0xa18]
    // 0xa91af8: StoreField: r2->field_1b = r0
    //     0xa91af8: stur            w0, [x2, #0x1b]
    // 0xa91afc: r0 = Instance_VerticalDirection
    //     0xa91afc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa91b00: ldr             x0, [x0, #0xa20]
    // 0xa91b04: StoreField: r2->field_23 = r0
    //     0xa91b04: stur            w0, [x2, #0x23]
    // 0xa91b08: r0 = Instance_Clip
    //     0xa91b08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa91b0c: ldr             x0, [x0, #0x38]
    // 0xa91b10: StoreField: r2->field_2b = r0
    //     0xa91b10: stur            w0, [x2, #0x2b]
    // 0xa91b14: StoreField: r2->field_2f = rZR
    //     0xa91b14: stur            xzr, [x2, #0x2f]
    // 0xa91b18: ldur            x0, [fp, #-0x30]
    // 0xa91b1c: StoreField: r2->field_b = r0
    //     0xa91b1c: stur            w0, [x2, #0xb]
    // 0xa91b20: r1 = <String>
    //     0xa91b20: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xa91b24: r0 = PopupMenuItem()
    //     0xa91b24: bl              #0x9abca4  ; AllocatePopupMenuItemStub -> PopupMenuItem<X0> (size=0x38)
    // 0xa91b28: mov             x3, x0
    // 0xa91b2c: r0 = "flag"
    //     0xa91b2c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xa91b30: ldr             x0, [x0, #0xa68]
    // 0xa91b34: stur            x3, [fp, #-0x30]
    // 0xa91b38: StoreField: r3->field_f = r0
    //     0xa91b38: stur            w0, [x3, #0xf]
    // 0xa91b3c: ldur            x2, [fp, #-0x28]
    // 0xa91b40: r1 = Function '<anonymous closure>':.
    //     0xa91b40: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a120] AnonymousClosure: (0xa91e30), in [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::showMenuItem (0xa915ec)
    //     0xa91b44: ldr             x1, [x1, #0x120]
    // 0xa91b48: r0 = AllocateClosure()
    //     0xa91b48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa91b4c: mov             x1, x0
    // 0xa91b50: ldur            x0, [fp, #-0x30]
    // 0xa91b54: StoreField: r0->field_13 = r1
    //     0xa91b54: stur            w1, [x0, #0x13]
    // 0xa91b58: r1 = true
    //     0xa91b58: add             x1, NULL, #0x20  ; true
    // 0xa91b5c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa91b5c: stur            w1, [x0, #0x17]
    // 0xa91b60: d0 = 25.000000
    //     0xa91b60: fmov            d0, #25.00000000
    // 0xa91b64: StoreField: r0->field_1b = d0
    //     0xa91b64: stur            d0, [x0, #0x1b]
    // 0xa91b68: ldur            x1, [fp, #-8]
    // 0xa91b6c: StoreField: r0->field_33 = r1
    //     0xa91b6c: stur            w1, [x0, #0x33]
    // 0xa91b70: r1 = Null
    //     0xa91b70: mov             x1, NULL
    // 0xa91b74: r2 = 2
    //     0xa91b74: movz            x2, #0x2
    // 0xa91b78: r0 = AllocateArray()
    //     0xa91b78: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa91b7c: mov             x2, x0
    // 0xa91b80: ldur            x0, [fp, #-0x30]
    // 0xa91b84: stur            x2, [fp, #-8]
    // 0xa91b88: StoreField: r2->field_f = r0
    //     0xa91b88: stur            w0, [x2, #0xf]
    // 0xa91b8c: r1 = <PopupMenuEntry<String>>
    //     0xa91b8c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa70] TypeArguments: <PopupMenuEntry<String>>
    //     0xa91b90: ldr             x1, [x1, #0xa70]
    // 0xa91b94: r0 = AllocateGrowableArray()
    //     0xa91b94: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa91b98: mov             x1, x0
    // 0xa91b9c: ldur            x0, [fp, #-8]
    // 0xa91ba0: StoreField: r1->field_f = r0
    //     0xa91ba0: stur            w0, [x1, #0xf]
    // 0xa91ba4: r0 = 2
    //     0xa91ba4: movz            x0, #0x2
    // 0xa91ba8: StoreField: r1->field_b = r0
    //     0xa91ba8: stur            w0, [x1, #0xb]
    // 0xa91bac: r16 = <String>
    //     0xa91bac: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xa91bb0: ldur            lr, [fp, #-0x10]
    // 0xa91bb4: stp             lr, x16, [SP, #0x20]
    // 0xa91bb8: ldur            x16, [fp, #-0x18]
    // 0xa91bbc: stp             x16, x1, [SP, #0x10]
    // 0xa91bc0: r16 = Instance_Color
    //     0xa91bc0: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa91bc4: ldur            lr, [fp, #-0x20]
    // 0xa91bc8: stp             lr, x16, [SP]
    // 0xa91bcc: r4 = const [0x1, 0x5, 0x5, 0x3, color, 0x3, constraints, 0x4, null]
    //     0xa91bcc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa78] List(9) [0x1, 0x5, 0x5, 0x3, "color", 0x3, "constraints", 0x4, Null]
    //     0xa91bd0: ldr             x4, [x4, #0xa78]
    // 0xa91bd4: r0 = showMenu()
    //     0xa91bd4: bl              #0x9ab6c4  ; [package:flutter/src/material/popup_menu.dart] ::showMenu
    // 0xa91bd8: ldur            x2, [fp, #-0x28]
    // 0xa91bdc: r1 = Function '<anonymous closure>':.
    //     0xa91bdc: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a128] AnonymousClosure: (0xa91c9c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::showMenuItem (0xa915ec)
    //     0xa91be0: ldr             x1, [x1, #0x128]
    // 0xa91be4: stur            x0, [fp, #-8]
    // 0xa91be8: r0 = AllocateClosure()
    //     0xa91be8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa91bec: r16 = <Null?>
    //     0xa91bec: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xa91bf0: ldur            lr, [fp, #-8]
    // 0xa91bf4: stp             lr, x16, [SP, #8]
    // 0xa91bf8: str             x0, [SP]
    // 0xa91bfc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa91bfc: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa91c00: r0 = then()
    //     0xa91c00: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xa91c04: r0 = Null
    //     0xa91c04: mov             x0, NULL
    // 0xa91c08: LeaveFrame
    //     0xa91c08: mov             SP, fp
    //     0xa91c0c: ldp             fp, lr, [SP], #0x10
    // 0xa91c10: ret
    //     0xa91c10: ret             
    // 0xa91c14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa91c14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa91c18: b               #0xa91620
    // 0xa91c1c: SaveReg d0
    //     0xa91c1c: str             q0, [SP, #-0x10]!
    // 0xa91c20: stp             x0, x1, [SP, #-0x10]!
    // 0xa91c24: r0 = AllocateDouble()
    //     0xa91c24: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa91c28: mov             x2, x0
    // 0xa91c2c: ldp             x0, x1, [SP], #0x10
    // 0xa91c30: RestoreReg d0
    //     0xa91c30: ldr             q0, [SP], #0x10
    // 0xa91c34: b               #0xa916fc
    // 0xa91c38: stp             q0, q1, [SP, #-0x20]!
    // 0xa91c3c: stp             x0, x1, [SP, #-0x10]!
    // 0xa91c40: r0 = AllocateDouble()
    //     0xa91c40: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa91c44: mov             x2, x0
    // 0xa91c48: ldp             x0, x1, [SP], #0x10
    // 0xa91c4c: ldp             q0, q1, [SP], #0x20
    // 0xa91c50: b               #0xa91750
    // 0xa91c54: stp             q2, q3, [SP, #-0x20]!
    // 0xa91c58: stp             q0, q1, [SP, #-0x20]!
    // 0xa91c5c: stp             x0, x1, [SP, #-0x10]!
    // 0xa91c60: r0 = AllocateDouble()
    //     0xa91c60: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa91c64: mov             x2, x0
    // 0xa91c68: ldp             x0, x1, [SP], #0x10
    // 0xa91c6c: ldp             q0, q1, [SP], #0x20
    // 0xa91c70: ldp             q2, q3, [SP], #0x20
    // 0xa91c74: b               #0xa917b0
    // 0xa91c78: stp             q3, q4, [SP, #-0x20]!
    // 0xa91c7c: stp             q0, q1, [SP, #-0x20]!
    // 0xa91c80: SaveReg r0
    //     0xa91c80: str             x0, [SP, #-8]!
    // 0xa91c84: r0 = AllocateDouble()
    //     0xa91c84: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa91c88: mov             x1, x0
    // 0xa91c8c: RestoreReg r0
    //     0xa91c8c: ldr             x0, [SP], #8
    // 0xa91c90: ldp             q0, q1, [SP], #0x20
    // 0xa91c94: ldp             q3, q4, [SP], #0x20
    // 0xa91c98: b               #0xa91808
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0xa91c9c, size: 0x94
    // 0xa91c9c: EnterFrame
    //     0xa91c9c: stp             fp, lr, [SP, #-0x10]!
    //     0xa91ca0: mov             fp, SP
    // 0xa91ca4: AllocStack(0x20)
    //     0xa91ca4: sub             SP, SP, #0x20
    // 0xa91ca8: SetupParameters()
    //     0xa91ca8: ldr             x0, [fp, #0x18]
    //     0xa91cac: ldur            w2, [x0, #0x17]
    //     0xa91cb0: add             x2, x2, HEAP, lsl #32
    //     0xa91cb4: stur            x2, [fp, #-8]
    // 0xa91cb8: CheckStackOverflow
    //     0xa91cb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa91cbc: cmp             SP, x16
    //     0xa91cc0: b.ls            #0xa91d28
    // 0xa91cc4: ldr             x0, [fp, #0x10]
    // 0xa91cc8: r1 = LoadClassIdInstr(r0)
    //     0xa91cc8: ldur            x1, [x0, #-1]
    //     0xa91ccc: ubfx            x1, x1, #0xc, #0x14
    // 0xa91cd0: r16 = "flag"
    //     0xa91cd0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xa91cd4: ldr             x16, [x16, #0xa68]
    // 0xa91cd8: stp             x16, x0, [SP]
    // 0xa91cdc: mov             x0, x1
    // 0xa91ce0: mov             lr, x0
    // 0xa91ce4: ldr             lr, [x21, lr, lsl #3]
    // 0xa91ce8: blr             lr
    // 0xa91cec: tbnz            w0, #4, #0xa91d18
    // 0xa91cf0: ldur            x2, [fp, #-8]
    // 0xa91cf4: LoadField: r0 = r2->field_f
    //     0xa91cf4: ldur            w0, [x2, #0xf]
    // 0xa91cf8: DecompressPointer r0
    //     0xa91cf8: add             x0, x0, HEAP, lsl #32
    // 0xa91cfc: stur            x0, [fp, #-0x10]
    // 0xa91d00: r1 = Function '<anonymous closure>':.
    //     0xa91d00: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a130] AnonymousClosure: (0xa91d30), in [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::showMenuItem (0xa915ec)
    //     0xa91d04: ldr             x1, [x1, #0x130]
    // 0xa91d08: r0 = AllocateClosure()
    //     0xa91d08: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa91d0c: ldur            x1, [fp, #-0x10]
    // 0xa91d10: mov             x2, x0
    // 0xa91d14: r0 = setState()
    //     0xa91d14: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa91d18: r0 = Null
    //     0xa91d18: mov             x0, NULL
    // 0xa91d1c: LeaveFrame
    //     0xa91d1c: mov             SP, fp
    //     0xa91d20: ldp             fp, lr, [SP], #0x10
    // 0xa91d24: ret
    //     0xa91d24: ret             
    // 0xa91d28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa91d28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa91d2c: b               #0xa91cc4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa91d30, size: 0x100
    // 0xa91d30: EnterFrame
    //     0xa91d30: stp             fp, lr, [SP, #-0x10]!
    //     0xa91d34: mov             fp, SP
    // 0xa91d38: AllocStack(0x20)
    //     0xa91d38: sub             SP, SP, #0x20
    // 0xa91d3c: SetupParameters()
    //     0xa91d3c: ldr             x0, [fp, #0x10]
    //     0xa91d40: ldur            w4, [x0, #0x17]
    //     0xa91d44: add             x4, x4, HEAP, lsl #32
    //     0xa91d48: stur            x4, [fp, #-8]
    // 0xa91d4c: CheckStackOverflow
    //     0xa91d4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa91d50: cmp             SP, x16
    //     0xa91d54: b.ls            #0xa91e20
    // 0xa91d58: LoadField: r0 = r4->field_f
    //     0xa91d58: ldur            w0, [x4, #0xf]
    // 0xa91d5c: DecompressPointer r0
    //     0xa91d5c: add             x0, x0, HEAP, lsl #32
    // 0xa91d60: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa91d60: ldur            w1, [x0, #0x17]
    // 0xa91d64: DecompressPointer r1
    //     0xa91d64: add             x1, x1, HEAP, lsl #32
    // 0xa91d68: LoadField: r2 = r4->field_13
    //     0xa91d68: ldur            w2, [x4, #0x13]
    // 0xa91d6c: DecompressPointer r2
    //     0xa91d6c: add             x2, x2, HEAP, lsl #32
    // 0xa91d70: r0 = LoadClassIdInstr(r1)
    //     0xa91d70: ldur            x0, [x1, #-1]
    //     0xa91d74: ubfx            x0, x0, #0xc, #0x14
    // 0xa91d78: r3 = true
    //     0xa91d78: add             x3, NULL, #0x20  ; true
    // 0xa91d7c: r0 = GDT[cid_x0 + 0x35a]()
    //     0xa91d7c: add             lr, x0, #0x35a
    //     0xa91d80: ldr             lr, [x21, lr, lsl #3]
    //     0xa91d84: blr             lr
    // 0xa91d88: ldur            x1, [fp, #-8]
    // 0xa91d8c: LoadField: r0 = r1->field_f
    //     0xa91d8c: ldur            w0, [x1, #0xf]
    // 0xa91d90: DecompressPointer r0
    //     0xa91d90: add             x0, x0, HEAP, lsl #32
    // 0xa91d94: LoadField: r2 = r0->field_b
    //     0xa91d94: ldur            w2, [x0, #0xb]
    // 0xa91d98: DecompressPointer r2
    //     0xa91d98: add             x2, x2, HEAP, lsl #32
    // 0xa91d9c: cmp             w2, NULL
    // 0xa91da0: b.eq            #0xa91e28
    // 0xa91da4: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa91da4: ldur            w3, [x0, #0x17]
    // 0xa91da8: DecompressPointer r3
    //     0xa91da8: add             x3, x3, HEAP, lsl #32
    // 0xa91dac: LoadField: r0 = r2->field_f
    //     0xa91dac: ldur            w0, [x2, #0xf]
    // 0xa91db0: DecompressPointer r0
    //     0xa91db0: add             x0, x0, HEAP, lsl #32
    // 0xa91db4: stp             x3, x0, [SP]
    // 0xa91db8: ClosureCall
    //     0xa91db8: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xa91dbc: ldur            x2, [x0, #0x1f]
    //     0xa91dc0: blr             x2
    // 0xa91dc4: ldur            x0, [fp, #-8]
    // 0xa91dc8: LoadField: r1 = r0->field_f
    //     0xa91dc8: ldur            w1, [x0, #0xf]
    // 0xa91dcc: DecompressPointer r1
    //     0xa91dcc: add             x1, x1, HEAP, lsl #32
    // 0xa91dd0: LoadField: r0 = r1->field_b
    //     0xa91dd0: ldur            w0, [x1, #0xb]
    // 0xa91dd4: DecompressPointer r0
    //     0xa91dd4: add             x0, x0, HEAP, lsl #32
    // 0xa91dd8: cmp             w0, NULL
    // 0xa91ddc: b.eq            #0xa91e2c
    // 0xa91de0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa91de0: ldur            w1, [x0, #0x17]
    // 0xa91de4: DecompressPointer r1
    //     0xa91de4: add             x1, x1, HEAP, lsl #32
    // 0xa91de8: r16 = "flag_abusive"
    //     0xa91de8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa88] "flag_abusive"
    //     0xa91dec: ldr             x16, [x16, #0xa88]
    // 0xa91df0: stp             x16, x1, [SP, #8]
    // 0xa91df4: r16 = ""
    //     0xa91df4: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa91df8: str             x16, [SP]
    // 0xa91dfc: r4 = 0
    //     0xa91dfc: movz            x4, #0
    // 0xa91e00: ldr             x0, [SP, #0x10]
    // 0xa91e04: r5 = UnlinkedCall_0x613b5c
    //     0xa91e04: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a138] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa91e08: ldp             x5, lr, [x16, #0x138]
    // 0xa91e0c: blr             lr
    // 0xa91e10: r0 = Null
    //     0xa91e10: mov             x0, NULL
    // 0xa91e14: LeaveFrame
    //     0xa91e14: mov             SP, fp
    //     0xa91e18: ldp             fp, lr, [SP], #0x10
    // 0xa91e1c: ret
    //     0xa91e1c: ret             
    // 0xa91e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa91e20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa91e24: b               #0xa91d58
    // 0xa91e28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa91e28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa91e2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa91e2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa91e30, size: 0x60
    // 0xa91e30: EnterFrame
    //     0xa91e30: stp             fp, lr, [SP, #-0x10]!
    //     0xa91e34: mov             fp, SP
    // 0xa91e38: AllocStack(0x8)
    //     0xa91e38: sub             SP, SP, #8
    // 0xa91e3c: SetupParameters()
    //     0xa91e3c: ldr             x0, [fp, #0x10]
    //     0xa91e40: ldur            w2, [x0, #0x17]
    //     0xa91e44: add             x2, x2, HEAP, lsl #32
    // 0xa91e48: CheckStackOverflow
    //     0xa91e48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa91e4c: cmp             SP, x16
    //     0xa91e50: b.ls            #0xa91e88
    // 0xa91e54: LoadField: r0 = r2->field_f
    //     0xa91e54: ldur            w0, [x2, #0xf]
    // 0xa91e58: DecompressPointer r0
    //     0xa91e58: add             x0, x0, HEAP, lsl #32
    // 0xa91e5c: stur            x0, [fp, #-8]
    // 0xa91e60: r1 = Function '<anonymous closure>':.
    //     0xa91e60: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a148] AnonymousClosure: (0xa91e90), in [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::showMenuItem (0xa915ec)
    //     0xa91e64: ldr             x1, [x1, #0x148]
    // 0xa91e68: r0 = AllocateClosure()
    //     0xa91e68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa91e6c: ldur            x1, [fp, #-8]
    // 0xa91e70: mov             x2, x0
    // 0xa91e74: r0 = setState()
    //     0xa91e74: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa91e78: r0 = Null
    //     0xa91e78: mov             x0, NULL
    // 0xa91e7c: LeaveFrame
    //     0xa91e7c: mov             SP, fp
    //     0xa91e80: ldp             fp, lr, [SP], #0x10
    // 0xa91e84: ret
    //     0xa91e84: ret             
    // 0xa91e88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa91e88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa91e8c: b               #0xa91e54
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa91e90, size: 0x100
    // 0xa91e90: EnterFrame
    //     0xa91e90: stp             fp, lr, [SP, #-0x10]!
    //     0xa91e94: mov             fp, SP
    // 0xa91e98: AllocStack(0x20)
    //     0xa91e98: sub             SP, SP, #0x20
    // 0xa91e9c: SetupParameters()
    //     0xa91e9c: ldr             x0, [fp, #0x10]
    //     0xa91ea0: ldur            w4, [x0, #0x17]
    //     0xa91ea4: add             x4, x4, HEAP, lsl #32
    //     0xa91ea8: stur            x4, [fp, #-8]
    // 0xa91eac: CheckStackOverflow
    //     0xa91eac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa91eb0: cmp             SP, x16
    //     0xa91eb4: b.ls            #0xa91f80
    // 0xa91eb8: LoadField: r0 = r4->field_f
    //     0xa91eb8: ldur            w0, [x4, #0xf]
    // 0xa91ebc: DecompressPointer r0
    //     0xa91ebc: add             x0, x0, HEAP, lsl #32
    // 0xa91ec0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa91ec0: ldur            w1, [x0, #0x17]
    // 0xa91ec4: DecompressPointer r1
    //     0xa91ec4: add             x1, x1, HEAP, lsl #32
    // 0xa91ec8: LoadField: r2 = r4->field_13
    //     0xa91ec8: ldur            w2, [x4, #0x13]
    // 0xa91ecc: DecompressPointer r2
    //     0xa91ecc: add             x2, x2, HEAP, lsl #32
    // 0xa91ed0: r0 = LoadClassIdInstr(r1)
    //     0xa91ed0: ldur            x0, [x1, #-1]
    //     0xa91ed4: ubfx            x0, x0, #0xc, #0x14
    // 0xa91ed8: r3 = true
    //     0xa91ed8: add             x3, NULL, #0x20  ; true
    // 0xa91edc: r0 = GDT[cid_x0 + 0x35a]()
    //     0xa91edc: add             lr, x0, #0x35a
    //     0xa91ee0: ldr             lr, [x21, lr, lsl #3]
    //     0xa91ee4: blr             lr
    // 0xa91ee8: ldur            x1, [fp, #-8]
    // 0xa91eec: LoadField: r0 = r1->field_f
    //     0xa91eec: ldur            w0, [x1, #0xf]
    // 0xa91ef0: DecompressPointer r0
    //     0xa91ef0: add             x0, x0, HEAP, lsl #32
    // 0xa91ef4: LoadField: r2 = r0->field_b
    //     0xa91ef4: ldur            w2, [x0, #0xb]
    // 0xa91ef8: DecompressPointer r2
    //     0xa91ef8: add             x2, x2, HEAP, lsl #32
    // 0xa91efc: cmp             w2, NULL
    // 0xa91f00: b.eq            #0xa91f88
    // 0xa91f04: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa91f04: ldur            w3, [x0, #0x17]
    // 0xa91f08: DecompressPointer r3
    //     0xa91f08: add             x3, x3, HEAP, lsl #32
    // 0xa91f0c: LoadField: r0 = r2->field_f
    //     0xa91f0c: ldur            w0, [x2, #0xf]
    // 0xa91f10: DecompressPointer r0
    //     0xa91f10: add             x0, x0, HEAP, lsl #32
    // 0xa91f14: stp             x3, x0, [SP]
    // 0xa91f18: ClosureCall
    //     0xa91f18: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xa91f1c: ldur            x2, [x0, #0x1f]
    //     0xa91f20: blr             x2
    // 0xa91f24: ldur            x0, [fp, #-8]
    // 0xa91f28: LoadField: r1 = r0->field_f
    //     0xa91f28: ldur            w1, [x0, #0xf]
    // 0xa91f2c: DecompressPointer r1
    //     0xa91f2c: add             x1, x1, HEAP, lsl #32
    // 0xa91f30: LoadField: r0 = r1->field_b
    //     0xa91f30: ldur            w0, [x1, #0xb]
    // 0xa91f34: DecompressPointer r0
    //     0xa91f34: add             x0, x0, HEAP, lsl #32
    // 0xa91f38: cmp             w0, NULL
    // 0xa91f3c: b.eq            #0xa91f8c
    // 0xa91f40: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa91f40: ldur            w1, [x0, #0x17]
    // 0xa91f44: DecompressPointer r1
    //     0xa91f44: add             x1, x1, HEAP, lsl #32
    // 0xa91f48: r16 = "flag_abusive"
    //     0xa91f48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa88] "flag_abusive"
    //     0xa91f4c: ldr             x16, [x16, #0xa88]
    // 0xa91f50: stp             x16, x1, [SP, #8]
    // 0xa91f54: r16 = ""
    //     0xa91f54: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa91f58: str             x16, [SP]
    // 0xa91f5c: r4 = 0
    //     0xa91f5c: movz            x4, #0
    // 0xa91f60: ldr             x0, [SP, #0x10]
    // 0xa91f64: r5 = UnlinkedCall_0x613b5c
    //     0xa91f64: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a150] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa91f68: ldp             x5, lr, [x16, #0x150]
    // 0xa91f6c: blr             lr
    // 0xa91f70: r0 = Null
    //     0xa91f70: mov             x0, NULL
    // 0xa91f74: LeaveFrame
    //     0xa91f74: mov             SP, fp
    //     0xa91f78: ldp             fp, lr, [SP], #0x10
    // 0xa91f7c: ret
    //     0xa91f7c: ret             
    // 0xa91f80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa91f80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa91f84: b               #0xa91eb8
    // 0xa91f88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa91f88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa91f8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa91f8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa91f90, size: 0x278
    // 0xa91f90: EnterFrame
    //     0xa91f90: stp             fp, lr, [SP, #-0x10]!
    //     0xa91f94: mov             fp, SP
    // 0xa91f98: AllocStack(0x68)
    //     0xa91f98: sub             SP, SP, #0x68
    // 0xa91f9c: SetupParameters()
    //     0xa91f9c: ldr             x0, [fp, #0x20]
    //     0xa91fa0: ldur            w1, [x0, #0x17]
    //     0xa91fa4: add             x1, x1, HEAP, lsl #32
    //     0xa91fa8: stur            x1, [fp, #-8]
    // 0xa91fac: CheckStackOverflow
    //     0xa91fac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa91fb0: cmp             SP, x16
    //     0xa91fb4: b.ls            #0xa921f8
    // 0xa91fb8: r1 = 3
    //     0xa91fb8: movz            x1, #0x3
    // 0xa91fbc: r0 = AllocateContext()
    //     0xa91fbc: bl              #0x16f6108  ; AllocateContextStub
    // 0xa91fc0: mov             x2, x0
    // 0xa91fc4: ldur            x0, [fp, #-8]
    // 0xa91fc8: stur            x2, [fp, #-0x10]
    // 0xa91fcc: StoreField: r2->field_b = r0
    //     0xa91fcc: stur            w0, [x2, #0xb]
    // 0xa91fd0: ldr             x1, [fp, #0x18]
    // 0xa91fd4: StoreField: r2->field_f = r1
    //     0xa91fd4: stur            w1, [x2, #0xf]
    // 0xa91fd8: ldr             x3, [fp, #0x10]
    // 0xa91fdc: StoreField: r2->field_13 = r3
    //     0xa91fdc: stur            w3, [x2, #0x13]
    // 0xa91fe0: LoadField: r4 = r0->field_13
    //     0xa91fe0: ldur            w4, [x0, #0x13]
    // 0xa91fe4: DecompressPointer r4
    //     0xa91fe4: add             x4, x4, HEAP, lsl #32
    // 0xa91fe8: cmp             w4, NULL
    // 0xa91fec: b.ne            #0xa91ff8
    // 0xa91ff0: r5 = Null
    //     0xa91ff0: mov             x5, NULL
    // 0xa91ff4: b               #0xa92044
    // 0xa91ff8: LoadField: r5 = r4->field_1b
    //     0xa91ff8: ldur            w5, [x4, #0x1b]
    // 0xa91ffc: DecompressPointer r5
    //     0xa91ffc: add             x5, x5, HEAP, lsl #32
    // 0xa92000: LoadField: r0 = r5->field_b
    //     0xa92000: ldur            w0, [x5, #0xb]
    // 0xa92004: r6 = LoadInt32Instr(r3)
    //     0xa92004: sbfx            x6, x3, #1, #0x1f
    //     0xa92008: tbz             w3, #0, #0xa92010
    //     0xa9200c: ldur            x6, [x3, #7]
    // 0xa92010: r1 = LoadInt32Instr(r0)
    //     0xa92010: sbfx            x1, x0, #1, #0x1f
    // 0xa92014: mov             x0, x1
    // 0xa92018: mov             x1, x6
    // 0xa9201c: cmp             x1, x0
    // 0xa92020: b.hs            #0xa92200
    // 0xa92024: LoadField: r0 = r5->field_f
    //     0xa92024: ldur            w0, [x5, #0xf]
    // 0xa92028: DecompressPointer r0
    //     0xa92028: add             x0, x0, HEAP, lsl #32
    // 0xa9202c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa9202c: add             x16, x0, x6, lsl #2
    //     0xa92030: ldur            w1, [x16, #0xf]
    // 0xa92034: DecompressPointer r1
    //     0xa92034: add             x1, x1, HEAP, lsl #32
    // 0xa92038: LoadField: r0 = r1->field_13
    //     0xa92038: ldur            w0, [x1, #0x13]
    // 0xa9203c: DecompressPointer r0
    //     0xa9203c: add             x0, x0, HEAP, lsl #32
    // 0xa92040: mov             x5, x0
    // 0xa92044: stur            x5, [fp, #-8]
    // 0xa92048: ArrayStore: r2[0] = r5  ; List_4
    //     0xa92048: stur            w5, [x2, #0x17]
    // 0xa9204c: cmp             w4, NULL
    // 0xa92050: b.ne            #0xa9205c
    // 0xa92054: r0 = Null
    //     0xa92054: mov             x0, NULL
    // 0xa92058: b               #0xa920a4
    // 0xa9205c: LoadField: r6 = r4->field_1b
    //     0xa9205c: ldur            w6, [x4, #0x1b]
    // 0xa92060: DecompressPointer r6
    //     0xa92060: add             x6, x6, HEAP, lsl #32
    // 0xa92064: LoadField: r0 = r6->field_b
    //     0xa92064: ldur            w0, [x6, #0xb]
    // 0xa92068: r4 = LoadInt32Instr(r3)
    //     0xa92068: sbfx            x4, x3, #1, #0x1f
    //     0xa9206c: tbz             w3, #0, #0xa92074
    //     0xa92070: ldur            x4, [x3, #7]
    // 0xa92074: r1 = LoadInt32Instr(r0)
    //     0xa92074: sbfx            x1, x0, #1, #0x1f
    // 0xa92078: mov             x0, x1
    // 0xa9207c: mov             x1, x4
    // 0xa92080: cmp             x1, x0
    // 0xa92084: b.hs            #0xa92204
    // 0xa92088: LoadField: r0 = r6->field_f
    //     0xa92088: ldur            w0, [x6, #0xf]
    // 0xa9208c: DecompressPointer r0
    //     0xa9208c: add             x0, x0, HEAP, lsl #32
    // 0xa92090: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa92090: add             x16, x0, x4, lsl #2
    //     0xa92094: ldur            w1, [x16, #0xf]
    // 0xa92098: DecompressPointer r1
    //     0xa92098: add             x1, x1, HEAP, lsl #32
    // 0xa9209c: LoadField: r0 = r1->field_f
    //     0xa9209c: ldur            w0, [x1, #0xf]
    // 0xa920a0: DecompressPointer r0
    //     0xa920a0: add             x0, x0, HEAP, lsl #32
    // 0xa920a4: r1 = LoadClassIdInstr(r0)
    //     0xa920a4: ldur            x1, [x0, #-1]
    //     0xa920a8: ubfx            x1, x1, #0xc, #0x14
    // 0xa920ac: r16 = "image"
    //     0xa920ac: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0xa920b0: stp             x16, x0, [SP]
    // 0xa920b4: mov             x0, x1
    // 0xa920b8: mov             lr, x0
    // 0xa920bc: ldr             lr, [x21, lr, lsl #3]
    // 0xa920c0: blr             lr
    // 0xa920c4: tbnz            w0, #4, #0xa92168
    // 0xa920c8: ldur            x0, [fp, #-8]
    // 0xa920cc: r0 = ImageHeaders.forImages()
    //     0xa920cc: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xa920d0: mov             x3, x0
    // 0xa920d4: ldur            x0, [fp, #-8]
    // 0xa920d8: stur            x3, [fp, #-0x20]
    // 0xa920dc: cmp             w0, NULL
    // 0xa920e0: b.ne            #0xa920e8
    // 0xa920e4: r0 = ""
    //     0xa920e4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa920e8: stur            x0, [fp, #-0x18]
    // 0xa920ec: r1 = Function '<anonymous closure>':.
    //     0xa920ec: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a160] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa920f0: ldr             x1, [x1, #0x160]
    // 0xa920f4: r2 = Null
    //     0xa920f4: mov             x2, NULL
    // 0xa920f8: r0 = AllocateClosure()
    //     0xa920f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa920fc: r1 = Function '<anonymous closure>':.
    //     0xa920fc: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a168] AnonymousClosure: (0x9b17ac), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa92100: ldr             x1, [x1, #0x168]
    // 0xa92104: r2 = Null
    //     0xa92104: mov             x2, NULL
    // 0xa92108: stur            x0, [fp, #-0x28]
    // 0xa9210c: r0 = AllocateClosure()
    //     0xa9210c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa92110: stur            x0, [fp, #-0x30]
    // 0xa92114: r0 = CachedNetworkImage()
    //     0xa92114: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa92118: stur            x0, [fp, #-0x38]
    // 0xa9211c: r16 = Instance_BoxFit
    //     0xa9211c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xa92120: ldr             x16, [x16, #0x118]
    // 0xa92124: r30 = 48.000000
    //     0xa92124: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xa92128: ldr             lr, [lr, #0xad8]
    // 0xa9212c: stp             lr, x16, [SP, #0x20]
    // 0xa92130: r16 = 48.000000
    //     0xa92130: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xa92134: ldr             x16, [x16, #0xad8]
    // 0xa92138: ldur            lr, [fp, #-0x20]
    // 0xa9213c: stp             lr, x16, [SP, #0x10]
    // 0xa92140: ldur            x16, [fp, #-0x28]
    // 0xa92144: ldur            lr, [fp, #-0x30]
    // 0xa92148: stp             lr, x16, [SP]
    // 0xa9214c: mov             x1, x0
    // 0xa92150: ldur            x2, [fp, #-0x18]
    // 0xa92154: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x2, height, 0x4, httpHeaders, 0x5, progressIndicatorBuilder, 0x6, width, 0x3, null]
    //     0xa92154: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a170] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x2, "height", 0x4, "httpHeaders", 0x5, "progressIndicatorBuilder", 0x6, "width", 0x3, Null]
    //     0xa92158: ldr             x4, [x4, #0x170]
    // 0xa9215c: r0 = CachedNetworkImage()
    //     0xa9215c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa92160: ldur            x0, [fp, #-0x38]
    // 0xa92164: b               #0xa921b4
    // 0xa92168: ldur            x0, [fp, #-8]
    // 0xa9216c: cmp             w0, NULL
    // 0xa92170: b.ne            #0xa92178
    // 0xa92174: r0 = ""
    //     0xa92174: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa92178: stur            x0, [fp, #-8]
    // 0xa9217c: r0 = VideoPlayerWidget()
    //     0xa9217c: bl              #0x8fed7c  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0xa92180: mov             x1, x0
    // 0xa92184: ldur            x0, [fp, #-8]
    // 0xa92188: stur            x1, [fp, #-0x18]
    // 0xa9218c: StoreField: r1->field_b = r0
    //     0xa9218c: stur            w0, [x1, #0xb]
    // 0xa92190: r0 = SizedBox()
    //     0xa92190: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa92194: mov             x1, x0
    // 0xa92198: r0 = 48.000000
    //     0xa92198: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xa9219c: ldr             x0, [x0, #0xad8]
    // 0xa921a0: StoreField: r1->field_f = r0
    //     0xa921a0: stur            w0, [x1, #0xf]
    // 0xa921a4: StoreField: r1->field_13 = r0
    //     0xa921a4: stur            w0, [x1, #0x13]
    // 0xa921a8: ldur            x0, [fp, #-0x18]
    // 0xa921ac: StoreField: r1->field_b = r0
    //     0xa921ac: stur            w0, [x1, #0xb]
    // 0xa921b0: mov             x0, x1
    // 0xa921b4: stur            x0, [fp, #-8]
    // 0xa921b8: r0 = GestureDetector()
    //     0xa921b8: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa921bc: ldur            x2, [fp, #-0x10]
    // 0xa921c0: r1 = Function '<anonymous closure>':.
    //     0xa921c0: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a178] AnonymousClosure: (0xa92208), in [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xc0c210)
    //     0xa921c4: ldr             x1, [x1, #0x178]
    // 0xa921c8: stur            x0, [fp, #-0x10]
    // 0xa921cc: r0 = AllocateClosure()
    //     0xa921cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa921d0: ldur            x16, [fp, #-8]
    // 0xa921d4: stp             x16, x0, [SP]
    // 0xa921d8: ldur            x1, [fp, #-0x10]
    // 0xa921dc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xa921dc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xa921e0: ldr             x4, [x4, #0xaf0]
    // 0xa921e4: r0 = GestureDetector()
    //     0xa921e4: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa921e8: ldur            x0, [fp, #-0x10]
    // 0xa921ec: LeaveFrame
    //     0xa921ec: mov             SP, fp
    //     0xa921f0: ldp             fp, lr, [SP], #0x10
    // 0xa921f4: ret
    //     0xa921f4: ret             
    // 0xa921f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa921f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa921fc: b               #0xa91fb8
    // 0xa92200: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa92200: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa92204: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa92204: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa92208, size: 0x100
    // 0xa92208: EnterFrame
    //     0xa92208: stp             fp, lr, [SP, #-0x10]!
    //     0xa9220c: mov             fp, SP
    // 0xa92210: AllocStack(0x28)
    //     0xa92210: sub             SP, SP, #0x28
    // 0xa92214: SetupParameters()
    //     0xa92214: ldr             x0, [fp, #0x10]
    //     0xa92218: ldur            w2, [x0, #0x17]
    //     0xa9221c: add             x2, x2, HEAP, lsl #32
    //     0xa92220: stur            x2, [fp, #-8]
    // 0xa92224: CheckStackOverflow
    //     0xa92224: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa92228: cmp             SP, x16
    //     0xa9222c: b.ls            #0xa922fc
    // 0xa92230: LoadField: r0 = r2->field_b
    //     0xa92230: ldur            w0, [x2, #0xb]
    // 0xa92234: DecompressPointer r0
    //     0xa92234: add             x0, x0, HEAP, lsl #32
    // 0xa92238: LoadField: r1 = r0->field_b
    //     0xa92238: ldur            w1, [x0, #0xb]
    // 0xa9223c: DecompressPointer r1
    //     0xa9223c: add             x1, x1, HEAP, lsl #32
    // 0xa92240: LoadField: r0 = r1->field_f
    //     0xa92240: ldur            w0, [x1, #0xf]
    // 0xa92244: DecompressPointer r0
    //     0xa92244: add             x0, x0, HEAP, lsl #32
    // 0xa92248: LoadField: r1 = r0->field_b
    //     0xa92248: ldur            w1, [x0, #0xb]
    // 0xa9224c: DecompressPointer r1
    //     0xa9224c: add             x1, x1, HEAP, lsl #32
    // 0xa92250: cmp             w1, NULL
    // 0xa92254: b.eq            #0xa92304
    // 0xa92258: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xa92258: ldur            w0, [x2, #0x17]
    // 0xa9225c: DecompressPointer r0
    //     0xa9225c: add             x0, x0, HEAP, lsl #32
    // 0xa92260: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xa92260: ldur            w3, [x1, #0x17]
    // 0xa92264: DecompressPointer r3
    //     0xa92264: add             x3, x3, HEAP, lsl #32
    // 0xa92268: r16 = "single_media"
    //     0xa92268: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fab0] "single_media"
    //     0xa9226c: ldr             x16, [x16, #0xab0]
    // 0xa92270: stp             x16, x3, [SP, #8]
    // 0xa92274: str             x0, [SP]
    // 0xa92278: r4 = 0
    //     0xa92278: movz            x4, #0
    // 0xa9227c: ldr             x0, [SP, #0x10]
    // 0xa92280: r5 = UnlinkedCall_0x613b5c
    //     0xa92280: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a180] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa92284: ldp             x5, lr, [x16, #0x180]
    // 0xa92288: blr             lr
    // 0xa9228c: ldur            x2, [fp, #-8]
    // 0xa92290: LoadField: r1 = r2->field_f
    //     0xa92290: ldur            w1, [x2, #0xf]
    // 0xa92294: DecompressPointer r1
    //     0xa92294: add             x1, x1, HEAP, lsl #32
    // 0xa92298: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa92298: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa9229c: r0 = of()
    //     0xa9229c: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xa922a0: ldur            x2, [fp, #-8]
    // 0xa922a4: r1 = Function '<anonymous closure>':.
    //     0xa922a4: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a190] AnonymousClosure: (0xa92308), in [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xc0c210)
    //     0xa922a8: ldr             x1, [x1, #0x190]
    // 0xa922ac: stur            x0, [fp, #-8]
    // 0xa922b0: r0 = AllocateClosure()
    //     0xa922b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa922b4: r1 = Null
    //     0xa922b4: mov             x1, NULL
    // 0xa922b8: stur            x0, [fp, #-0x10]
    // 0xa922bc: r0 = MaterialPageRoute()
    //     0xa922bc: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xa922c0: mov             x1, x0
    // 0xa922c4: ldur            x2, [fp, #-0x10]
    // 0xa922c8: stur            x0, [fp, #-0x10]
    // 0xa922cc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa922cc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa922d0: r0 = MaterialPageRoute()
    //     0xa922d0: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xa922d4: ldur            x16, [fp, #-8]
    // 0xa922d8: stp             x16, NULL, [SP, #8]
    // 0xa922dc: ldur            x16, [fp, #-0x10]
    // 0xa922e0: str             x16, [SP]
    // 0xa922e4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa922e4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa922e8: r0 = push()
    //     0xa922e8: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xa922ec: r0 = Null
    //     0xa922ec: mov             x0, NULL
    // 0xa922f0: LeaveFrame
    //     0xa922f0: mov             SP, fp
    //     0xa922f4: ldp             fp, lr, [SP], #0x10
    // 0xa922f8: ret
    //     0xa922f8: ret             
    // 0xa922fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa922fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa92300: b               #0xa92230
    // 0xa92304: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa92304: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RatingReviewOnTapImage <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xa92308, size: 0xc4
    // 0xa92308: EnterFrame
    //     0xa92308: stp             fp, lr, [SP, #-0x10]!
    //     0xa9230c: mov             fp, SP
    // 0xa92310: AllocStack(0x28)
    //     0xa92310: sub             SP, SP, #0x28
    // 0xa92314: SetupParameters()
    //     0xa92314: ldr             x0, [fp, #0x18]
    //     0xa92318: ldur            w2, [x0, #0x17]
    //     0xa9231c: add             x2, x2, HEAP, lsl #32
    //     0xa92320: stur            x2, [fp, #-0x20]
    // 0xa92324: LoadField: r0 = r2->field_13
    //     0xa92324: ldur            w0, [x2, #0x13]
    // 0xa92328: DecompressPointer r0
    //     0xa92328: add             x0, x0, HEAP, lsl #32
    // 0xa9232c: stur            x0, [fp, #-0x18]
    // 0xa92330: LoadField: r1 = r2->field_b
    //     0xa92330: ldur            w1, [x2, #0xb]
    // 0xa92334: DecompressPointer r1
    //     0xa92334: add             x1, x1, HEAP, lsl #32
    // 0xa92338: LoadField: r3 = r1->field_b
    //     0xa92338: ldur            w3, [x1, #0xb]
    // 0xa9233c: DecompressPointer r3
    //     0xa9233c: add             x3, x3, HEAP, lsl #32
    // 0xa92340: LoadField: r4 = r3->field_f
    //     0xa92340: ldur            w4, [x3, #0xf]
    // 0xa92344: DecompressPointer r4
    //     0xa92344: add             x4, x4, HEAP, lsl #32
    // 0xa92348: LoadField: r3 = r4->field_b
    //     0xa92348: ldur            w3, [x4, #0xb]
    // 0xa9234c: DecompressPointer r3
    //     0xa9234c: add             x3, x3, HEAP, lsl #32
    // 0xa92350: cmp             w3, NULL
    // 0xa92354: b.eq            #0xa923c8
    // 0xa92358: LoadField: r4 = r3->field_13
    //     0xa92358: ldur            w4, [x3, #0x13]
    // 0xa9235c: DecompressPointer r4
    //     0xa9235c: add             x4, x4, HEAP, lsl #32
    // 0xa92360: stur            x4, [fp, #-0x10]
    // 0xa92364: LoadField: r3 = r1->field_13
    //     0xa92364: ldur            w3, [x1, #0x13]
    // 0xa92368: DecompressPointer r3
    //     0xa92368: add             x3, x3, HEAP, lsl #32
    // 0xa9236c: stur            x3, [fp, #-8]
    // 0xa92370: r0 = RatingReviewOnTapImage()
    //     0xa92370: bl              #0x9b1700  ; AllocateRatingReviewOnTapImageStub -> RatingReviewOnTapImage (size=0x20)
    // 0xa92374: mov             x3, x0
    // 0xa92378: ldur            x0, [fp, #-8]
    // 0xa9237c: stur            x3, [fp, #-0x28]
    // 0xa92380: StoreField: r3->field_b = r0
    //     0xa92380: stur            w0, [x3, #0xb]
    // 0xa92384: ldur            x0, [fp, #-0x18]
    // 0xa92388: r1 = LoadInt32Instr(r0)
    //     0xa92388: sbfx            x1, x0, #1, #0x1f
    //     0xa9238c: tbz             w0, #0, #0xa92394
    //     0xa92390: ldur            x1, [x0, #7]
    // 0xa92394: StoreField: r3->field_f = r1
    //     0xa92394: stur            x1, [x3, #0xf]
    // 0xa92398: ldur            x2, [fp, #-0x20]
    // 0xa9239c: r1 = Function '<anonymous closure>':.
    //     0xa9239c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a198] AnonymousClosure: (0xa923cc), in [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xc0c210)
    //     0xa923a0: ldr             x1, [x1, #0x198]
    // 0xa923a4: r0 = AllocateClosure()
    //     0xa923a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa923a8: mov             x1, x0
    // 0xa923ac: ldur            x0, [fp, #-0x28]
    // 0xa923b0: ArrayStore: r0[0] = r1  ; List_4
    //     0xa923b0: stur            w1, [x0, #0x17]
    // 0xa923b4: ldur            x1, [fp, #-0x10]
    // 0xa923b8: StoreField: r0->field_1b = r1
    //     0xa923b8: stur            w1, [x0, #0x1b]
    // 0xa923bc: LeaveFrame
    //     0xa923bc: mov             SP, fp
    //     0xa923c0: ldp             fp, lr, [SP], #0x10
    // 0xa923c4: ret
    //     0xa923c4: ret             
    // 0xa923c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa923c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, Map<String, dynamic>) {
    // ** addr: 0xa923cc, size: 0x84
    // 0xa923cc: EnterFrame
    //     0xa923cc: stp             fp, lr, [SP, #-0x10]!
    //     0xa923d0: mov             fp, SP
    // 0xa923d4: AllocStack(0x10)
    //     0xa923d4: sub             SP, SP, #0x10
    // 0xa923d8: SetupParameters()
    //     0xa923d8: ldr             x0, [fp, #0x18]
    //     0xa923dc: ldur            w1, [x0, #0x17]
    //     0xa923e0: add             x1, x1, HEAP, lsl #32
    // 0xa923e4: CheckStackOverflow
    //     0xa923e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa923e8: cmp             SP, x16
    //     0xa923ec: b.ls            #0xa92444
    // 0xa923f0: LoadField: r0 = r1->field_b
    //     0xa923f0: ldur            w0, [x1, #0xb]
    // 0xa923f4: DecompressPointer r0
    //     0xa923f4: add             x0, x0, HEAP, lsl #32
    // 0xa923f8: LoadField: r1 = r0->field_b
    //     0xa923f8: ldur            w1, [x0, #0xb]
    // 0xa923fc: DecompressPointer r1
    //     0xa923fc: add             x1, x1, HEAP, lsl #32
    // 0xa92400: LoadField: r0 = r1->field_f
    //     0xa92400: ldur            w0, [x1, #0xf]
    // 0xa92404: DecompressPointer r0
    //     0xa92404: add             x0, x0, HEAP, lsl #32
    // 0xa92408: LoadField: r1 = r0->field_b
    //     0xa92408: ldur            w1, [x0, #0xb]
    // 0xa9240c: DecompressPointer r1
    //     0xa9240c: add             x1, x1, HEAP, lsl #32
    // 0xa92410: cmp             w1, NULL
    // 0xa92414: b.eq            #0xa9244c
    // 0xa92418: LoadField: r0 = r1->field_f
    //     0xa92418: ldur            w0, [x1, #0xf]
    // 0xa9241c: DecompressPointer r0
    //     0xa9241c: add             x0, x0, HEAP, lsl #32
    // 0xa92420: ldr             x16, [fp, #0x10]
    // 0xa92424: stp             x16, x0, [SP]
    // 0xa92428: ClosureCall
    //     0xa92428: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xa9242c: ldur            x2, [x0, #0x1f]
    //     0xa92430: blr             x2
    // 0xa92434: r0 = Null
    //     0xa92434: mov             x0, NULL
    // 0xa92438: LeaveFrame
    //     0xa92438: mov             SP, fp
    //     0xa9243c: ldp             fp, lr, [SP], #0x10
    // 0xa92440: ret
    //     0xa92440: ret             
    // 0xa92444: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa92444: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa92448: b               #0xa923f0
    // 0xa9244c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9244c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xc0c210, size: 0x110
    // 0xc0c210: EnterFrame
    //     0xc0c210: stp             fp, lr, [SP, #-0x10]!
    //     0xc0c214: mov             fp, SP
    // 0xc0c218: AllocStack(0x30)
    //     0xc0c218: sub             SP, SP, #0x30
    // 0xc0c21c: SetupParameters(_ReviewWidgetState this /* r1 => r1, fp-0x8 */)
    //     0xc0c21c: stur            x1, [fp, #-8]
    // 0xc0c220: CheckStackOverflow
    //     0xc0c220: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0c224: cmp             SP, x16
    //     0xc0c228: b.ls            #0xc0c314
    // 0xc0c22c: r1 = 1
    //     0xc0c22c: movz            x1, #0x1
    // 0xc0c230: r0 = AllocateContext()
    //     0xc0c230: bl              #0x16f6108  ; AllocateContextStub
    // 0xc0c234: mov             x3, x0
    // 0xc0c238: ldur            x0, [fp, #-8]
    // 0xc0c23c: stur            x3, [fp, #-0x18]
    // 0xc0c240: StoreField: r3->field_f = r0
    //     0xc0c240: stur            w0, [x3, #0xf]
    // 0xc0c244: LoadField: r1 = r0->field_b
    //     0xc0c244: ldur            w1, [x0, #0xb]
    // 0xc0c248: DecompressPointer r1
    //     0xc0c248: add             x1, x1, HEAP, lsl #32
    // 0xc0c24c: cmp             w1, NULL
    // 0xc0c250: b.eq            #0xc0c31c
    // 0xc0c254: LoadField: r0 = r1->field_b
    //     0xc0c254: ldur            w0, [x1, #0xb]
    // 0xc0c258: DecompressPointer r0
    //     0xc0c258: add             x0, x0, HEAP, lsl #32
    // 0xc0c25c: cmp             w0, NULL
    // 0xc0c260: b.ne            #0xc0c26c
    // 0xc0c264: r0 = Null
    //     0xc0c264: mov             x0, NULL
    // 0xc0c268: b               #0xc0c278
    // 0xc0c26c: LoadField: r1 = r0->field_13
    //     0xc0c26c: ldur            w1, [x0, #0x13]
    // 0xc0c270: DecompressPointer r1
    //     0xc0c270: add             x1, x1, HEAP, lsl #32
    // 0xc0c274: LoadField: r0 = r1->field_b
    //     0xc0c274: ldur            w0, [x1, #0xb]
    // 0xc0c278: cmp             w0, NULL
    // 0xc0c27c: b.ne            #0xc0c288
    // 0xc0c280: r0 = 0
    //     0xc0c280: movz            x0, #0
    // 0xc0c284: b               #0xc0c290
    // 0xc0c288: r1 = LoadInt32Instr(r0)
    //     0xc0c288: sbfx            x1, x0, #1, #0x1f
    // 0xc0c28c: mov             x0, x1
    // 0xc0c290: stur            x0, [fp, #-0x10]
    // 0xc0c294: r1 = Function '<anonymous closure>':.
    //     0xc0c294: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a0d8] AnonymousClosure: (0x9b6638), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xc0c298: ldr             x1, [x1, #0xd8]
    // 0xc0c29c: r2 = Null
    //     0xc0c29c: mov             x2, NULL
    // 0xc0c2a0: r0 = AllocateClosure()
    //     0xc0c2a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0c2a4: ldur            x2, [fp, #-0x18]
    // 0xc0c2a8: r1 = Function '<anonymous closure>':.
    //     0xc0c2a8: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a0e0] AnonymousClosure: (0xa906d4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xc0c210)
    //     0xc0c2ac: ldr             x1, [x1, #0xe0]
    // 0xc0c2b0: stur            x0, [fp, #-8]
    // 0xc0c2b4: r0 = AllocateClosure()
    //     0xc0c2b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0c2b8: stur            x0, [fp, #-0x18]
    // 0xc0c2bc: r0 = ListView()
    //     0xc0c2bc: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xc0c2c0: stur            x0, [fp, #-0x20]
    // 0xc0c2c4: r16 = Instance_NeverScrollableScrollPhysics
    //     0xc0c2c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xc0c2c8: ldr             x16, [x16, #0x1c8]
    // 0xc0c2cc: r30 = true
    //     0xc0c2cc: add             lr, NULL, #0x20  ; true
    // 0xc0c2d0: stp             lr, x16, [SP]
    // 0xc0c2d4: mov             x1, x0
    // 0xc0c2d8: ldur            x2, [fp, #-0x18]
    // 0xc0c2dc: ldur            x3, [fp, #-0x10]
    // 0xc0c2e0: ldur            x5, [fp, #-8]
    // 0xc0c2e4: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x4, shrinkWrap, 0x5, null]
    //     0xc0c2e4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f968] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x4, "shrinkWrap", 0x5, Null]
    //     0xc0c2e8: ldr             x4, [x4, #0x968]
    // 0xc0c2ec: r0 = ListView.separated()
    //     0xc0c2ec: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xc0c2f0: r0 = Padding()
    //     0xc0c2f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0c2f4: r1 = Instance_EdgeInsets
    //     0xc0c2f4: add             x1, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xc0c2f8: ldr             x1, [x1, #0x778]
    // 0xc0c2fc: StoreField: r0->field_f = r1
    //     0xc0c2fc: stur            w1, [x0, #0xf]
    // 0xc0c300: ldur            x1, [fp, #-0x20]
    // 0xc0c304: StoreField: r0->field_b = r1
    //     0xc0c304: stur            w1, [x0, #0xb]
    // 0xc0c308: LeaveFrame
    //     0xc0c308: mov             SP, fp
    //     0xc0c30c: ldp             fp, lr, [SP], #0x10
    // 0xc0c310: ret
    //     0xc0c310: ret             
    // 0xc0c314: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0c314: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0c318: b               #0xc0c22c
    // 0xc0c31c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0c31c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3964, size: 0x1c, field offset: 0xc
//   const constructor, 
class ReviewWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc811ec, size: 0x54
    // 0xc811ec: EnterFrame
    //     0xc811ec: stp             fp, lr, [SP, #-0x10]!
    //     0xc811f0: mov             fp, SP
    // 0xc811f4: AllocStack(0x18)
    //     0xc811f4: sub             SP, SP, #0x18
    // 0xc811f8: CheckStackOverflow
    //     0xc811f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc811fc: cmp             SP, x16
    //     0xc81200: b.ls            #0xc81238
    // 0xc81204: r16 = <String, dynamic>
    //     0xc81204: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xc81208: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc8120c: stp             lr, x16, [SP]
    // 0xc81210: r0 = Map._fromLiteral()
    //     0xc81210: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xc81214: r1 = <ReviewWidget>
    //     0xc81214: add             x1, PP, #0x61, lsl #12  ; [pp+0x61b58] TypeArguments: <ReviewWidget>
    //     0xc81218: ldr             x1, [x1, #0xb58]
    // 0xc8121c: stur            x0, [fp, #-8]
    // 0xc81220: r0 = _ReviewWidgetState()
    //     0xc81220: bl              #0xc81240  ; Allocate_ReviewWidgetStateStub -> _ReviewWidgetState (size=0x1c)
    // 0xc81224: ldur            x1, [fp, #-8]
    // 0xc81228: ArrayStore: r0[0] = r1  ; List_4
    //     0xc81228: stur            w1, [x0, #0x17]
    // 0xc8122c: LeaveFrame
    //     0xc8122c: mov             SP, fp
    //     0xc81230: ldp             fp, lr, [SP], #0x10
    // 0xc81234: ret
    //     0xc81234: ret             
    // 0xc81238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc81238: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc8123c: b               #0xc81204
  }
}
