// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_breadcrumb.dart

// class id: 1049365, size: 0x8
class :: {
}

// class id: 3367, size: 0x24, field offset: 0x14
class _CheckoutBreadCrumbState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x940398, size: 0x140
    // 0x940398: EnterFrame
    //     0x940398: stp             fp, lr, [SP, #-0x10]!
    //     0x94039c: mov             fp, SP
    // 0x9403a0: AllocStack(0x28)
    //     0x9403a0: sub             SP, SP, #0x28
    // 0x9403a4: SetupParameters(_CheckoutBreadCrumbState this /* r1 => r0, fp-0x10 */)
    //     0x9403a4: mov             x0, x1
    //     0x9403a8: stur            x1, [fp, #-0x10]
    // 0x9403ac: CheckStackOverflow
    //     0x9403ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9403b0: cmp             SP, x16
    //     0x9403b4: b.ls            #0x9404cc
    // 0x9403b8: LoadField: r1 = r0->field_b
    //     0x9403b8: ldur            w1, [x0, #0xb]
    // 0x9403bc: DecompressPointer r1
    //     0x9403bc: add             x1, x1, HEAP, lsl #32
    // 0x9403c0: cmp             w1, NULL
    // 0x9403c4: b.eq            #0x9404d4
    // 0x9403c8: LoadField: r2 = r1->field_b
    //     0x9403c8: ldur            w2, [x1, #0xb]
    // 0x9403cc: DecompressPointer r2
    //     0x9403cc: add             x2, x2, HEAP, lsl #32
    // 0x9403d0: LoadField: r3 = r2->field_7
    //     0x9403d0: ldur            w3, [x2, #7]
    // 0x9403d4: DecompressPointer r3
    //     0x9403d4: add             x3, x3, HEAP, lsl #32
    // 0x9403d8: stur            x3, [fp, #-8]
    // 0x9403dc: cmp             w3, NULL
    // 0x9403e0: b.ne            #0x9403ec
    // 0x9403e4: r0 = Null
    //     0x9403e4: mov             x0, NULL
    // 0x9403e8: b               #0x940420
    // 0x9403ec: r1 = Function '<anonymous closure>':.
    //     0x9403ec: add             x1, PP, #0x56, lsl #12  ; [pp+0x56eb0] Function: [dart:ui] Paint::_objects (0x1557e38)
    //     0x9403f0: ldr             x1, [x1, #0xeb0]
    // 0x9403f4: r2 = Null
    //     0x9403f4: mov             x2, NULL
    // 0x9403f8: r0 = AllocateClosure()
    //     0x9403f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9403fc: r16 = <String?>
    //     0x9403fc: ldr             x16, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0x940400: ldur            lr, [fp, #-8]
    // 0x940404: stp             lr, x16, [SP, #8]
    // 0x940408: str             x0, [SP]
    // 0x94040c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x94040c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x940410: r0 = map()
    //     0x940410: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0x940414: mov             x1, x0
    // 0x940418: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x940418: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x94041c: r0 = toList()
    //     0x94041c: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0x940420: cmp             w0, NULL
    // 0x940424: b.ne            #0x940464
    // 0x940428: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x940428: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x94042c: ldr             x0, [x0]
    //     0x940430: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x940434: cmp             w0, w16
    //     0x940438: b.ne            #0x940444
    //     0x94043c: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x940440: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x940444: r1 = <String?>
    //     0x940444: ldr             x1, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0x940448: stur            x0, [fp, #-8]
    // 0x94044c: r0 = AllocateGrowableArray()
    //     0x94044c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x940450: mov             x1, x0
    // 0x940454: ldur            x0, [fp, #-8]
    // 0x940458: StoreField: r1->field_f = r0
    //     0x940458: stur            w0, [x1, #0xf]
    // 0x94045c: StoreField: r1->field_b = rZR
    //     0x94045c: stur            wzr, [x1, #0xb]
    // 0x940460: b               #0x940468
    // 0x940464: mov             x1, x0
    // 0x940468: ldur            x2, [fp, #-0x10]
    // 0x94046c: mov             x0, x1
    // 0x940470: StoreField: r2->field_1b = r0
    //     0x940470: stur            w0, [x2, #0x1b]
    //     0x940474: ldurb           w16, [x2, #-1]
    //     0x940478: ldurb           w17, [x0, #-1]
    //     0x94047c: and             x16, x17, x16, lsr #2
    //     0x940480: tst             x16, HEAP, lsr #32
    //     0x940484: b.eq            #0x94048c
    //     0x940488: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x94048c: r0 = toSet()
    //     0x94048c: bl              #0x7d698c  ; [dart:core] _GrowableList::toSet
    // 0x940490: mov             x1, x0
    // 0x940494: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x940494: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x940498: r0 = toList()
    //     0x940498: bl              #0x7dd5cc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::toList
    // 0x94049c: ldur            x1, [fp, #-0x10]
    // 0x9404a0: StoreField: r1->field_1f = r0
    //     0x9404a0: stur            w0, [x1, #0x1f]
    //     0x9404a4: ldurb           w16, [x1, #-1]
    //     0x9404a8: ldurb           w17, [x0, #-1]
    //     0x9404ac: and             x16, x17, x16, lsr #2
    //     0x9404b0: tst             x16, HEAP, lsr #32
    //     0x9404b4: b.eq            #0x9404bc
    //     0x9404b8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x9404bc: r0 = Null
    //     0x9404bc: mov             x0, NULL
    // 0x9404c0: LeaveFrame
    //     0x9404c0: mov             SP, fp
    //     0x9404c4: ldp             fp, lr, [SP], #0x10
    // 0x9404c8: ret
    //     0x9404c8: ret             
    // 0x9404cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9404cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9404d0: b               #0x9403b8
    // 0x9404d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9404d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb45350, size: 0x254
    // 0xb45350: EnterFrame
    //     0xb45350: stp             fp, lr, [SP, #-0x10]!
    //     0xb45354: mov             fp, SP
    // 0xb45358: AllocStack(0x48)
    //     0xb45358: sub             SP, SP, #0x48
    // 0xb4535c: SetupParameters(_CheckoutBreadCrumbState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb4535c: mov             x0, x1
    //     0xb45360: stur            x1, [fp, #-8]
    //     0xb45364: mov             x1, x2
    //     0xb45368: stur            x2, [fp, #-0x10]
    // 0xb4536c: CheckStackOverflow
    //     0xb4536c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb45370: cmp             SP, x16
    //     0xb45374: b.ls            #0xb45594
    // 0xb45378: r1 = 2
    //     0xb45378: movz            x1, #0x2
    // 0xb4537c: r0 = AllocateContext()
    //     0xb4537c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb45380: mov             x2, x0
    // 0xb45384: ldur            x0, [fp, #-8]
    // 0xb45388: stur            x2, [fp, #-0x18]
    // 0xb4538c: StoreField: r2->field_f = r0
    //     0xb4538c: stur            w0, [x2, #0xf]
    // 0xb45390: ldur            x1, [fp, #-0x10]
    // 0xb45394: StoreField: r2->field_13 = r1
    //     0xb45394: stur            w1, [x2, #0x13]
    // 0xb45398: r0 = of()
    //     0xb45398: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4539c: ldur            x2, [fp, #-0x18]
    // 0xb453a0: stur            x0, [fp, #-0x10]
    // 0xb453a4: LoadField: r1 = r2->field_13
    //     0xb453a4: ldur            w1, [x2, #0x13]
    // 0xb453a8: DecompressPointer r1
    //     0xb453a8: add             x1, x1, HEAP, lsl #32
    // 0xb453ac: r0 = of()
    //     0xb453ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb453b0: LoadField: r1 = r0->field_5b
    //     0xb453b0: ldur            w1, [x0, #0x5b]
    // 0xb453b4: DecompressPointer r1
    //     0xb453b4: add             x1, x1, HEAP, lsl #32
    // 0xb453b8: r0 = LoadClassIdInstr(r1)
    //     0xb453b8: ldur            x0, [x1, #-1]
    //     0xb453bc: ubfx            x0, x0, #0xc, #0x14
    // 0xb453c0: d0 = 0.030000
    //     0xb453c0: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb453c4: ldr             d0, [x17, #0x238]
    // 0xb453c8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb453c8: sub             lr, x0, #0xffa
    //     0xb453cc: ldr             lr, [x21, lr, lsl #3]
    //     0xb453d0: blr             lr
    // 0xb453d4: r16 = Instance_ColorScheme
    //     0xb453d4: add             x16, PP, #0x56, lsl #12  ; [pp+0x56e50] Obj!ColorScheme@d643f1
    //     0xb453d8: ldr             x16, [x16, #0xe50]
    // 0xb453dc: stp             x16, x0, [SP]
    // 0xb453e0: ldur            x1, [fp, #-0x10]
    // 0xb453e4: r4 = const [0, 0x3, 0x2, 0x1, canvasColor, 0x1, colorScheme, 0x2, null]
    //     0xb453e4: add             x4, PP, #0x56, lsl #12  ; [pp+0x56e58] List(9) [0, 0x3, 0x2, 0x1, "canvasColor", 0x1, "colorScheme", 0x2, Null]
    //     0xb453e8: ldr             x4, [x4, #0xe58]
    // 0xb453ec: r0 = copyWith()
    //     0xb453ec: bl              #0x6adb58  ; [package:flutter/src/material/theme_data.dart] ThemeData::copyWith
    // 0xb453f0: mov             x3, x0
    // 0xb453f4: ldur            x0, [fp, #-8]
    // 0xb453f8: stur            x3, [fp, #-0x10]
    // 0xb453fc: LoadField: r1 = r0->field_1f
    //     0xb453fc: ldur            w1, [x0, #0x1f]
    // 0xb45400: DecompressPointer r1
    //     0xb45400: add             x1, x1, HEAP, lsl #32
    // 0xb45404: LoadField: r0 = r1->field_b
    //     0xb45404: ldur            w0, [x1, #0xb]
    // 0xb45408: ldur            x2, [fp, #-0x18]
    // 0xb4540c: stur            x0, [fp, #-8]
    // 0xb45410: r1 = Function '<anonymous closure>':.
    //     0xb45410: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e60] AnonymousClosure: (0xb455d0), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_breadcrumb.dart] _CheckoutBreadCrumbState::build (0xb45350)
    //     0xb45414: ldr             x1, [x1, #0xe60]
    // 0xb45418: r0 = AllocateClosure()
    //     0xb45418: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4541c: mov             x3, x0
    // 0xb45420: ldur            x0, [fp, #-8]
    // 0xb45424: stur            x3, [fp, #-0x18]
    // 0xb45428: r2 = LoadInt32Instr(r0)
    //     0xb45428: sbfx            x2, x0, #1, #0x1f
    // 0xb4542c: r1 = <Step>
    //     0xb4542c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e68] TypeArguments: <Step>
    //     0xb45430: ldr             x1, [x1, #0xe68]
    // 0xb45434: r0 = _GrowableList()
    //     0xb45434: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb45438: mov             x1, x0
    // 0xb4543c: stur            x1, [fp, #-0x30]
    // 0xb45440: LoadField: r0 = r1->field_b
    //     0xb45440: ldur            w0, [x1, #0xb]
    // 0xb45444: r2 = LoadInt32Instr(r0)
    //     0xb45444: sbfx            x2, x0, #1, #0x1f
    // 0xb45448: stur            x2, [fp, #-0x28]
    // 0xb4544c: LoadField: r3 = r1->field_f
    //     0xb4544c: ldur            w3, [x1, #0xf]
    // 0xb45450: DecompressPointer r3
    //     0xb45450: add             x3, x3, HEAP, lsl #32
    // 0xb45454: stur            x3, [fp, #-8]
    // 0xb45458: r4 = 0
    //     0xb45458: movz            x4, #0
    // 0xb4545c: stur            x4, [fp, #-0x20]
    // 0xb45460: CheckStackOverflow
    //     0xb45460: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb45464: cmp             SP, x16
    //     0xb45468: b.ls            #0xb4559c
    // 0xb4546c: cmp             x4, x2
    // 0xb45470: b.ge            #0xb45514
    // 0xb45474: lsl             x0, x4, #1
    // 0xb45478: ldur            x16, [fp, #-0x18]
    // 0xb4547c: stp             x0, x16, [SP]
    // 0xb45480: ldur            x0, [fp, #-0x18]
    // 0xb45484: ClosureCall
    //     0xb45484: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb45488: ldur            x2, [x0, #0x1f]
    //     0xb4548c: blr             x2
    // 0xb45490: mov             x3, x0
    // 0xb45494: r2 = Null
    //     0xb45494: mov             x2, NULL
    // 0xb45498: r1 = Null
    //     0xb45498: mov             x1, NULL
    // 0xb4549c: stur            x3, [fp, #-0x38]
    // 0xb454a0: r4 = 60
    //     0xb454a0: movz            x4, #0x3c
    // 0xb454a4: branchIfSmi(r0, 0xb454b0)
    //     0xb454a4: tbz             w0, #0, #0xb454b0
    // 0xb454a8: r4 = LoadClassIdInstr(r0)
    //     0xb454a8: ldur            x4, [x0, #-1]
    //     0xb454ac: ubfx            x4, x4, #0xc, #0x14
    // 0xb454b0: cmp             x4, #0x8b7
    // 0xb454b4: b.eq            #0xb454cc
    // 0xb454b8: r8 = Step
    //     0xb454b8: add             x8, PP, #0x56, lsl #12  ; [pp+0x56e70] Type: Step
    //     0xb454bc: ldr             x8, [x8, #0xe70]
    // 0xb454c0: r3 = Null
    //     0xb454c0: add             x3, PP, #0x56, lsl #12  ; [pp+0x56e78] Null
    //     0xb454c4: ldr             x3, [x3, #0xe78]
    // 0xb454c8: r0 = Step()
    //     0xb454c8: bl              #0xb455b0  ; IsType_Step_Stub
    // 0xb454cc: ldur            x1, [fp, #-8]
    // 0xb454d0: ldur            x0, [fp, #-0x38]
    // 0xb454d4: ldur            x2, [fp, #-0x20]
    // 0xb454d8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb454d8: add             x25, x1, x2, lsl #2
    //     0xb454dc: add             x25, x25, #0xf
    //     0xb454e0: str             w0, [x25]
    //     0xb454e4: tbz             w0, #0, #0xb45500
    //     0xb454e8: ldurb           w16, [x1, #-1]
    //     0xb454ec: ldurb           w17, [x0, #-1]
    //     0xb454f0: and             x16, x17, x16, lsr #2
    //     0xb454f4: tst             x16, HEAP, lsr #32
    //     0xb454f8: b.eq            #0xb45500
    //     0xb454fc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb45500: add             x4, x2, #1
    // 0xb45504: ldur            x1, [fp, #-0x30]
    // 0xb45508: ldur            x3, [fp, #-8]
    // 0xb4550c: ldur            x2, [fp, #-0x28]
    // 0xb45510: b               #0xb4545c
    // 0xb45514: mov             x0, x1
    // 0xb45518: ldur            x1, [fp, #-0x10]
    // 0xb4551c: r0 = Stepper()
    //     0xb4551c: bl              #0xb455a4  ; AllocateStepperStub -> Stepper (size=0x5c)
    // 0xb45520: mov             x1, x0
    // 0xb45524: ldur            x0, [fp, #-0x30]
    // 0xb45528: stur            x1, [fp, #-8]
    // 0xb4552c: StoreField: r1->field_b = r0
    //     0xb4552c: stur            w0, [x1, #0xb]
    // 0xb45530: r0 = Instance_StepperType
    //     0xb45530: add             x0, PP, #0x56, lsl #12  ; [pp+0x56e88] Obj!StepperType@d73be1
    //     0xb45534: ldr             x0, [x0, #0xe88]
    // 0xb45538: ArrayStore: r1[0] = r0  ; List_4
    //     0xb45538: stur            w0, [x1, #0x17]
    // 0xb4553c: StoreField: r1->field_1b = rZR
    //     0xb4553c: stur            xzr, [x1, #0x1b]
    // 0xb45540: StoreField: r1->field_33 = rZR
    //     0xb45540: stur            xzr, [x1, #0x33]
    // 0xb45544: StoreField: r1->field_3f = rZR
    //     0xb45544: stur            xzr, [x1, #0x3f]
    // 0xb45548: r0 = Instance_Clip
    //     0xb45548: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4554c: ldr             x0, [x0, #0x38]
    // 0xb45550: StoreField: r1->field_57 = r0
    //     0xb45550: stur            w0, [x1, #0x57]
    // 0xb45554: r0 = SizedBox()
    //     0xb45554: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb45558: mov             x1, x0
    // 0xb4555c: r0 = 73.000000
    //     0xb4555c: add             x0, PP, #0x56, lsl #12  ; [pp+0x56e90] 73
    //     0xb45560: ldr             x0, [x0, #0xe90]
    // 0xb45564: stur            x1, [fp, #-0x18]
    // 0xb45568: StoreField: r1->field_13 = r0
    //     0xb45568: stur            w0, [x1, #0x13]
    // 0xb4556c: ldur            x0, [fp, #-8]
    // 0xb45570: StoreField: r1->field_b = r0
    //     0xb45570: stur            w0, [x1, #0xb]
    // 0xb45574: r0 = Theme()
    //     0xb45574: bl              #0x796f30  ; AllocateThemeStub -> Theme (size=0x14)
    // 0xb45578: ldur            x1, [fp, #-0x10]
    // 0xb4557c: StoreField: r0->field_b = r1
    //     0xb4557c: stur            w1, [x0, #0xb]
    // 0xb45580: ldur            x1, [fp, #-0x18]
    // 0xb45584: StoreField: r0->field_f = r1
    //     0xb45584: stur            w1, [x0, #0xf]
    // 0xb45588: LeaveFrame
    //     0xb45588: mov             SP, fp
    //     0xb4558c: ldp             fp, lr, [SP], #0x10
    // 0xb45590: ret
    //     0xb45590: ret             
    // 0xb45594: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb45594: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb45598: b               #0xb45378
    // 0xb4559c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4559c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb455a0: b               #0xb4546c
  }
  [closure] Step <anonymous closure>(dynamic, int) {
    // ** addr: 0xb455d0, size: 0x2bc
    // 0xb455d0: EnterFrame
    //     0xb455d0: stp             fp, lr, [SP, #-0x10]!
    //     0xb455d4: mov             fp, SP
    // 0xb455d8: AllocStack(0x48)
    //     0xb455d8: sub             SP, SP, #0x48
    // 0xb455dc: SetupParameters()
    //     0xb455dc: ldr             x0, [fp, #0x18]
    //     0xb455e0: ldur            w2, [x0, #0x17]
    //     0xb455e4: add             x2, x2, HEAP, lsl #32
    //     0xb455e8: stur            x2, [fp, #-0x28]
    // 0xb455ec: CheckStackOverflow
    //     0xb455ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb455f0: cmp             SP, x16
    //     0xb455f4: b.ls            #0xb45874
    // 0xb455f8: LoadField: r3 = r2->field_f
    //     0xb455f8: ldur            w3, [x2, #0xf]
    // 0xb455fc: DecompressPointer r3
    //     0xb455fc: add             x3, x3, HEAP, lsl #32
    // 0xb45600: LoadField: r0 = r3->field_b
    //     0xb45600: ldur            w0, [x3, #0xb]
    // 0xb45604: DecompressPointer r0
    //     0xb45604: add             x0, x0, HEAP, lsl #32
    // 0xb45608: cmp             w0, NULL
    // 0xb4560c: b.eq            #0xb4587c
    // 0xb45610: LoadField: r1 = r0->field_b
    //     0xb45610: ldur            w1, [x0, #0xb]
    // 0xb45614: DecompressPointer r1
    //     0xb45614: add             x1, x1, HEAP, lsl #32
    // 0xb45618: LoadField: r4 = r1->field_7
    //     0xb45618: ldur            w4, [x1, #7]
    // 0xb4561c: DecompressPointer r4
    //     0xb4561c: add             x4, x4, HEAP, lsl #32
    // 0xb45620: cmp             w4, NULL
    // 0xb45624: b.eq            #0xb45880
    // 0xb45628: LoadField: r0 = r4->field_b
    //     0xb45628: ldur            w0, [x4, #0xb]
    // 0xb4562c: ldr             x1, [fp, #0x10]
    // 0xb45630: r5 = LoadInt32Instr(r1)
    //     0xb45630: sbfx            x5, x1, #1, #0x1f
    //     0xb45634: tbz             w1, #0, #0xb4563c
    //     0xb45638: ldur            x5, [x1, #7]
    // 0xb4563c: r1 = LoadInt32Instr(r0)
    //     0xb4563c: sbfx            x1, x0, #1, #0x1f
    // 0xb45640: mov             x0, x1
    // 0xb45644: mov             x1, x5
    // 0xb45648: cmp             x1, x0
    // 0xb4564c: b.hs            #0xb45884
    // 0xb45650: LoadField: r0 = r4->field_f
    //     0xb45650: ldur            w0, [x4, #0xf]
    // 0xb45654: DecompressPointer r0
    //     0xb45654: add             x0, x0, HEAP, lsl #32
    // 0xb45658: ArrayLoad: r4 = r0[r5]  ; Unknown_4
    //     0xb45658: add             x16, x0, x5, lsl #2
    //     0xb4565c: ldur            w4, [x16, #0xf]
    // 0xb45660: DecompressPointer r4
    //     0xb45660: add             x4, x4, HEAP, lsl #32
    // 0xb45664: stur            x4, [fp, #-0x20]
    // 0xb45668: LoadField: r0 = r4->field_f
    //     0xb45668: ldur            w0, [x4, #0xf]
    // 0xb4566c: DecompressPointer r0
    //     0xb4566c: add             x0, x0, HEAP, lsl #32
    // 0xb45670: cmp             w0, NULL
    // 0xb45674: b.ne            #0xb45680
    // 0xb45678: r6 = false
    //     0xb45678: add             x6, NULL, #0x30  ; false
    // 0xb4567c: b               #0xb45684
    // 0xb45680: mov             x6, x0
    // 0xb45684: stur            x6, [fp, #-0x18]
    // 0xb45688: cmp             w0, NULL
    // 0xb4568c: b.eq            #0xb456a0
    // 0xb45690: tbnz            w0, #4, #0xb456a0
    // 0xb45694: r7 = Instance_StepState
    //     0xb45694: add             x7, PP, #0x56, lsl #12  ; [pp+0x56e98] Obj!StepState@d73c21
    //     0xb45698: ldr             x7, [x7, #0xe98]
    // 0xb4569c: b               #0xb456a8
    // 0xb456a0: r7 = Instance_StepState
    //     0xb456a0: add             x7, PP, #0x56, lsl #12  ; [pp+0x56ea0] Obj!StepState@d73c01
    //     0xb456a4: ldr             x7, [x7, #0xea0]
    // 0xb456a8: stur            x7, [fp, #-0x10]
    // 0xb456ac: LoadField: r8 = r3->field_1f
    //     0xb456ac: ldur            w8, [x3, #0x1f]
    // 0xb456b0: DecompressPointer r8
    //     0xb456b0: add             x8, x8, HEAP, lsl #32
    // 0xb456b4: LoadField: r0 = r8->field_b
    //     0xb456b4: ldur            w0, [x8, #0xb]
    // 0xb456b8: r1 = LoadInt32Instr(r0)
    //     0xb456b8: sbfx            x1, x0, #1, #0x1f
    // 0xb456bc: mov             x0, x1
    // 0xb456c0: mov             x1, x5
    // 0xb456c4: cmp             x1, x0
    // 0xb456c8: b.hs            #0xb45888
    // 0xb456cc: LoadField: r0 = r8->field_f
    //     0xb456cc: ldur            w0, [x8, #0xf]
    // 0xb456d0: DecompressPointer r0
    //     0xb456d0: add             x0, x0, HEAP, lsl #32
    // 0xb456d4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb456d4: add             x16, x0, x5, lsl #2
    //     0xb456d8: ldur            w1, [x16, #0xf]
    // 0xb456dc: DecompressPointer r1
    //     0xb456dc: add             x1, x1, HEAP, lsl #32
    // 0xb456e0: cmp             w1, NULL
    // 0xb456e4: b.ne            #0xb456f0
    // 0xb456e8: r0 = ""
    //     0xb456e8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb456ec: b               #0xb456f4
    // 0xb456f0: mov             x0, x1
    // 0xb456f4: stur            x0, [fp, #-8]
    // 0xb456f8: LoadField: r1 = r2->field_13
    //     0xb456f8: ldur            w1, [x2, #0x13]
    // 0xb456fc: DecompressPointer r1
    //     0xb456fc: add             x1, x1, HEAP, lsl #32
    // 0xb45700: r0 = of()
    //     0xb45700: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb45704: LoadField: r1 = r0->field_87
    //     0xb45704: ldur            w1, [x0, #0x87]
    // 0xb45708: DecompressPointer r1
    //     0xb45708: add             x1, x1, HEAP, lsl #32
    // 0xb4570c: LoadField: r0 = r1->field_7
    //     0xb4570c: ldur            w0, [x1, #7]
    // 0xb45710: DecompressPointer r0
    //     0xb45710: add             x0, x0, HEAP, lsl #32
    // 0xb45714: ldur            x2, [fp, #-0x20]
    // 0xb45718: stur            x0, [fp, #-0x30]
    // 0xb4571c: LoadField: r1 = r2->field_f
    //     0xb4571c: ldur            w1, [x2, #0xf]
    // 0xb45720: DecompressPointer r1
    //     0xb45720: add             x1, x1, HEAP, lsl #32
    // 0xb45724: cmp             w1, NULL
    // 0xb45728: b.eq            #0xb4573c
    // 0xb4572c: tbnz            w1, #4, #0xb4573c
    // 0xb45730: mov             x0, x2
    // 0xb45734: r1 = Instance_Color
    //     0xb45734: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb45738: b               #0xb45754
    // 0xb4573c: r1 = Instance_Color
    //     0xb4573c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb45740: d0 = 0.300000
    //     0xb45740: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb45744: ldr             d0, [x17, #0x658]
    // 0xb45748: r0 = withOpacity()
    //     0xb45748: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb4574c: mov             x1, x0
    // 0xb45750: ldur            x0, [fp, #-0x20]
    // 0xb45754: ldur            x2, [fp, #-8]
    // 0xb45758: r16 = 12.000000
    //     0xb45758: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4575c: ldr             x16, [x16, #0x9e8]
    // 0xb45760: stp             x16, x1, [SP]
    // 0xb45764: ldur            x1, [fp, #-0x30]
    // 0xb45768: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb45768: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb4576c: ldr             x4, [x4, #0x9b8]
    // 0xb45770: r0 = copyWith()
    //     0xb45770: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb45774: stur            x0, [fp, #-0x30]
    // 0xb45778: r0 = Text()
    //     0xb45778: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4577c: mov             x2, x0
    // 0xb45780: ldur            x0, [fp, #-8]
    // 0xb45784: stur            x2, [fp, #-0x38]
    // 0xb45788: StoreField: r2->field_b = r0
    //     0xb45788: stur            w0, [x2, #0xb]
    // 0xb4578c: ldur            x0, [fp, #-0x30]
    // 0xb45790: StoreField: r2->field_13 = r0
    //     0xb45790: stur            w0, [x2, #0x13]
    // 0xb45794: r0 = Instance_TextAlign
    //     0xb45794: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb45798: StoreField: r2->field_1b = r0
    //     0xb45798: stur            w0, [x2, #0x1b]
    // 0xb4579c: ldur            x0, [fp, #-0x20]
    // 0xb457a0: LoadField: r1 = r0->field_f
    //     0xb457a0: ldur            w1, [x0, #0xf]
    // 0xb457a4: DecompressPointer r1
    //     0xb457a4: add             x1, x1, HEAP, lsl #32
    // 0xb457a8: cmp             w1, NULL
    // 0xb457ac: b.ne            #0xb457b8
    // 0xb457b0: ldur            x0, [fp, #-0x28]
    // 0xb457b4: b               #0xb457e0
    // 0xb457b8: tbnz            w1, #4, #0xb457dc
    // 0xb457bc: ldur            x0, [fp, #-0x28]
    // 0xb457c0: LoadField: r1 = r0->field_13
    //     0xb457c0: ldur            w1, [x0, #0x13]
    // 0xb457c4: DecompressPointer r1
    //     0xb457c4: add             x1, x1, HEAP, lsl #32
    // 0xb457c8: r0 = of()
    //     0xb457c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb457cc: LoadField: r1 = r0->field_5b
    //     0xb457cc: ldur            w1, [x0, #0x5b]
    // 0xb457d0: DecompressPointer r1
    //     0xb457d0: add             x1, x1, HEAP, lsl #32
    // 0xb457d4: mov             x3, x1
    // 0xb457d8: b               #0xb45814
    // 0xb457dc: ldur            x0, [fp, #-0x28]
    // 0xb457e0: LoadField: r1 = r0->field_13
    //     0xb457e0: ldur            w1, [x0, #0x13]
    // 0xb457e4: DecompressPointer r1
    //     0xb457e4: add             x1, x1, HEAP, lsl #32
    // 0xb457e8: r0 = of()
    //     0xb457e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb457ec: LoadField: r1 = r0->field_5b
    //     0xb457ec: ldur            w1, [x0, #0x5b]
    // 0xb457f0: DecompressPointer r1
    //     0xb457f0: add             x1, x1, HEAP, lsl #32
    // 0xb457f4: r0 = LoadClassIdInstr(r1)
    //     0xb457f4: ldur            x0, [x1, #-1]
    //     0xb457f8: ubfx            x0, x0, #0xc, #0x14
    // 0xb457fc: d0 = 0.300000
    //     0xb457fc: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb45800: ldr             d0, [x17, #0x658]
    // 0xb45804: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb45804: sub             lr, x0, #0xffa
    //     0xb45808: ldr             lr, [x21, lr, lsl #3]
    //     0xb4580c: blr             lr
    // 0xb45810: mov             x3, x0
    // 0xb45814: ldur            x1, [fp, #-0x18]
    // 0xb45818: ldur            x2, [fp, #-0x10]
    // 0xb4581c: ldur            x0, [fp, #-0x38]
    // 0xb45820: stur            x3, [fp, #-8]
    // 0xb45824: r0 = StepStyle()
    //     0xb45824: bl              #0xb45898  ; AllocateStepStyleStub -> StepStyle (size=0x28)
    // 0xb45828: mov             x1, x0
    // 0xb4582c: ldur            x0, [fp, #-8]
    // 0xb45830: stur            x1, [fp, #-0x20]
    // 0xb45834: StoreField: r1->field_7 = r0
    //     0xb45834: stur            w0, [x1, #7]
    // 0xb45838: r0 = Step()
    //     0xb45838: bl              #0xb4588c  ; AllocateStepStub -> Step (size=0x24)
    // 0xb4583c: ldur            x1, [fp, #-0x38]
    // 0xb45840: StoreField: r0->field_7 = r1
    //     0xb45840: stur            w1, [x0, #7]
    // 0xb45844: r1 = Instance_Text
    //     0xb45844: add             x1, PP, #0x56, lsl #12  ; [pp+0x56ea8] Obj!Text@d659d1
    //     0xb45848: ldr             x1, [x1, #0xea8]
    // 0xb4584c: StoreField: r0->field_f = r1
    //     0xb4584c: stur            w1, [x0, #0xf]
    // 0xb45850: ldur            x1, [fp, #-0x10]
    // 0xb45854: StoreField: r0->field_13 = r1
    //     0xb45854: stur            w1, [x0, #0x13]
    // 0xb45858: ldur            x1, [fp, #-0x18]
    // 0xb4585c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb4585c: stur            w1, [x0, #0x17]
    // 0xb45860: ldur            x1, [fp, #-0x20]
    // 0xb45864: StoreField: r0->field_1f = r1
    //     0xb45864: stur            w1, [x0, #0x1f]
    // 0xb45868: LeaveFrame
    //     0xb45868: mov             SP, fp
    //     0xb4586c: ldp             fp, lr, [SP], #0x10
    // 0xb45870: ret
    //     0xb45870: ret             
    // 0xb45874: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb45874: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb45878: b               #0xb455f8
    // 0xb4587c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4587c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb45880: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb45880: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb45884: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb45884: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb45888: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb45888: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _CheckoutBreadCrumbState(/* No info */) {
    // ** addr: 0xc7e9f0, size: 0xc0
    // 0xc7e9f0: EnterFrame
    //     0xc7e9f0: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e9f4: mov             fp, SP
    // 0xc7e9f8: AllocStack(0x10)
    //     0xc7e9f8: sub             SP, SP, #0x10
    // 0xc7e9fc: SetupParameters(_CheckoutBreadCrumbState this /* r1 => r1, fp-0x8 */)
    //     0xc7e9fc: stur            x1, [fp, #-8]
    // 0xc7ea00: CheckStackOverflow
    //     0xc7ea00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7ea04: cmp             SP, x16
    //     0xc7ea08: b.ls            #0xc7eaa8
    // 0xc7ea0c: StoreField: r1->field_13 = rZR
    //     0xc7ea0c: stur            xzr, [x1, #0x13]
    // 0xc7ea10: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xc7ea10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc7ea14: ldr             x0, [x0]
    //     0xc7ea18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc7ea1c: cmp             w0, w16
    //     0xc7ea20: b.ne            #0xc7ea2c
    //     0xc7ea24: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xc7ea28: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc7ea2c: r1 = <String?>
    //     0xc7ea2c: ldr             x1, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0xc7ea30: stur            x0, [fp, #-0x10]
    // 0xc7ea34: r0 = AllocateGrowableArray()
    //     0xc7ea34: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc7ea38: ldur            x2, [fp, #-0x10]
    // 0xc7ea3c: StoreField: r0->field_f = r2
    //     0xc7ea3c: stur            w2, [x0, #0xf]
    // 0xc7ea40: StoreField: r0->field_b = rZR
    //     0xc7ea40: stur            wzr, [x0, #0xb]
    // 0xc7ea44: ldur            x3, [fp, #-8]
    // 0xc7ea48: StoreField: r3->field_1b = r0
    //     0xc7ea48: stur            w0, [x3, #0x1b]
    //     0xc7ea4c: ldurb           w16, [x3, #-1]
    //     0xc7ea50: ldurb           w17, [x0, #-1]
    //     0xc7ea54: and             x16, x17, x16, lsr #2
    //     0xc7ea58: tst             x16, HEAP, lsr #32
    //     0xc7ea5c: b.eq            #0xc7ea64
    //     0xc7ea60: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xc7ea64: r1 = <String?>
    //     0xc7ea64: ldr             x1, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0xc7ea68: r0 = AllocateGrowableArray()
    //     0xc7ea68: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc7ea6c: ldur            x1, [fp, #-0x10]
    // 0xc7ea70: StoreField: r0->field_f = r1
    //     0xc7ea70: stur            w1, [x0, #0xf]
    // 0xc7ea74: StoreField: r0->field_b = rZR
    //     0xc7ea74: stur            wzr, [x0, #0xb]
    // 0xc7ea78: ldur            x1, [fp, #-8]
    // 0xc7ea7c: StoreField: r1->field_1f = r0
    //     0xc7ea7c: stur            w0, [x1, #0x1f]
    //     0xc7ea80: ldurb           w16, [x1, #-1]
    //     0xc7ea84: ldurb           w17, [x0, #-1]
    //     0xc7ea88: and             x16, x17, x16, lsr #2
    //     0xc7ea8c: tst             x16, HEAP, lsr #32
    //     0xc7ea90: b.eq            #0xc7ea98
    //     0xc7ea94: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc7ea98: r0 = Null
    //     0xc7ea98: mov             x0, NULL
    // 0xc7ea9c: LeaveFrame
    //     0xc7ea9c: mov             SP, fp
    //     0xc7eaa0: ldp             fp, lr, [SP], #0x10
    // 0xc7eaa4: ret
    //     0xc7eaa4: ret             
    // 0xc7eaa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7eaa8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7eaac: b               #0xc7ea0c
  }
}

// class id: 4105, size: 0x10, field offset: 0xc
//   const constructor, 
class CheckoutBreadCrumb extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e9a8, size: 0x48
    // 0xc7e9a8: EnterFrame
    //     0xc7e9a8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e9ac: mov             fp, SP
    // 0xc7e9b0: AllocStack(0x8)
    //     0xc7e9b0: sub             SP, SP, #8
    // 0xc7e9b4: CheckStackOverflow
    //     0xc7e9b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7e9b8: cmp             SP, x16
    //     0xc7e9bc: b.ls            #0xc7e9e8
    // 0xc7e9c0: r1 = <CheckoutBreadCrumb>
    //     0xc7e9c0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a60] TypeArguments: <CheckoutBreadCrumb>
    //     0xc7e9c4: ldr             x1, [x1, #0xa60]
    // 0xc7e9c8: r0 = _CheckoutBreadCrumbState()
    //     0xc7e9c8: bl              #0xc7eab0  ; Allocate_CheckoutBreadCrumbStateStub -> _CheckoutBreadCrumbState (size=0x24)
    // 0xc7e9cc: mov             x1, x0
    // 0xc7e9d0: stur            x0, [fp, #-8]
    // 0xc7e9d4: r0 = _CheckoutBreadCrumbState()
    //     0xc7e9d4: bl              #0xc7e9f0  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_breadcrumb.dart] _CheckoutBreadCrumbState::_CheckoutBreadCrumbState
    // 0xc7e9d8: ldur            x0, [fp, #-8]
    // 0xc7e9dc: LeaveFrame
    //     0xc7e9dc: mov             SP, fp
    //     0xc7e9e0: ldp             fp, lr, [SP], #0x10
    // 0xc7e9e4: ret
    //     0xc7e9e4: ret             
    // 0xc7e9e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e9e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e9ec: b               #0xc7e9c0
  }
}
