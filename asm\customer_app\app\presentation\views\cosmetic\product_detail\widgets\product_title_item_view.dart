// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_title_item_view.dart

// class id: 1049318, size: 0x8
class :: {
}

// class id: 3399, size: 0x14, field offset: 0x14
class _ProductTitleItemViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb11aa4, size: 0x8f4
    // 0xb11aa4: EnterFrame
    //     0xb11aa4: stp             fp, lr, [SP, #-0x10]!
    //     0xb11aa8: mov             fp, SP
    // 0xb11aac: AllocStack(0x60)
    //     0xb11aac: sub             SP, SP, #0x60
    // 0xb11ab0: SetupParameters(_ProductTitleItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb11ab0: mov             x0, x1
    //     0xb11ab4: stur            x1, [fp, #-8]
    //     0xb11ab8: mov             x1, x2
    //     0xb11abc: stur            x2, [fp, #-0x10]
    // 0xb11ac0: CheckStackOverflow
    //     0xb11ac0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb11ac4: cmp             SP, x16
    //     0xb11ac8: b.ls            #0xb12388
    // 0xb11acc: r1 = 2
    //     0xb11acc: movz            x1, #0x2
    // 0xb11ad0: r0 = AllocateContext()
    //     0xb11ad0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb11ad4: mov             x1, x0
    // 0xb11ad8: ldur            x0, [fp, #-8]
    // 0xb11adc: stur            x1, [fp, #-0x30]
    // 0xb11ae0: StoreField: r1->field_f = r0
    //     0xb11ae0: stur            w0, [x1, #0xf]
    // 0xb11ae4: ldur            x2, [fp, #-0x10]
    // 0xb11ae8: StoreField: r1->field_13 = r2
    //     0xb11ae8: stur            w2, [x1, #0x13]
    // 0xb11aec: LoadField: r3 = r0->field_b
    //     0xb11aec: ldur            w3, [x0, #0xb]
    // 0xb11af0: DecompressPointer r3
    //     0xb11af0: add             x3, x3, HEAP, lsl #32
    // 0xb11af4: cmp             w3, NULL
    // 0xb11af8: b.eq            #0xb12390
    // 0xb11afc: LoadField: r4 = r3->field_b
    //     0xb11afc: ldur            w4, [x3, #0xb]
    // 0xb11b00: DecompressPointer r4
    //     0xb11b00: add             x4, x4, HEAP, lsl #32
    // 0xb11b04: stur            x4, [fp, #-0x28]
    // 0xb11b08: LoadField: r5 = r3->field_f
    //     0xb11b08: ldur            w5, [x3, #0xf]
    // 0xb11b0c: DecompressPointer r5
    //     0xb11b0c: add             x5, x5, HEAP, lsl #32
    // 0xb11b10: stur            x5, [fp, #-0x20]
    // 0xb11b14: LoadField: r6 = r3->field_13
    //     0xb11b14: ldur            w6, [x3, #0x13]
    // 0xb11b18: DecompressPointer r6
    //     0xb11b18: add             x6, x6, HEAP, lsl #32
    // 0xb11b1c: stur            x6, [fp, #-0x18]
    // 0xb11b20: r0 = Radius()
    //     0xb11b20: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb11b24: d0 = 10.000000
    //     0xb11b24: fmov            d0, #10.00000000
    // 0xb11b28: stur            x0, [fp, #-0x38]
    // 0xb11b2c: StoreField: r0->field_7 = d0
    //     0xb11b2c: stur            d0, [x0, #7]
    // 0xb11b30: StoreField: r0->field_f = d0
    //     0xb11b30: stur            d0, [x0, #0xf]
    // 0xb11b34: r0 = BorderRadius()
    //     0xb11b34: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb11b38: mov             x1, x0
    // 0xb11b3c: ldur            x0, [fp, #-0x38]
    // 0xb11b40: stur            x1, [fp, #-0x40]
    // 0xb11b44: StoreField: r1->field_7 = r0
    //     0xb11b44: stur            w0, [x1, #7]
    // 0xb11b48: StoreField: r1->field_b = r0
    //     0xb11b48: stur            w0, [x1, #0xb]
    // 0xb11b4c: StoreField: r1->field_f = r0
    //     0xb11b4c: stur            w0, [x1, #0xf]
    // 0xb11b50: StoreField: r1->field_13 = r0
    //     0xb11b50: stur            w0, [x1, #0x13]
    // 0xb11b54: r0 = ProductTitleWidget()
    //     0xb11b54: bl              #0xa898b0  ; AllocateProductTitleWidgetStub -> ProductTitleWidget (size=0x20)
    // 0xb11b58: mov             x3, x0
    // 0xb11b5c: ldur            x0, [fp, #-0x28]
    // 0xb11b60: stur            x3, [fp, #-0x38]
    // 0xb11b64: StoreField: r3->field_f = r0
    //     0xb11b64: stur            w0, [x3, #0xf]
    // 0xb11b68: ldur            x1, [fp, #-0x18]
    // 0xb11b6c: StoreField: r3->field_b = r1
    //     0xb11b6c: stur            w1, [x3, #0xb]
    // 0xb11b70: ldur            x1, [fp, #-0x20]
    // 0xb11b74: StoreField: r3->field_13 = r1
    //     0xb11b74: stur            w1, [x3, #0x13]
    // 0xb11b78: ldur            x2, [fp, #-0x30]
    // 0xb11b7c: r1 = Function '<anonymous closure>':.
    //     0xb11b7c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57ae8] AnonymousClosure: (0xb1244c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_title_item_view.dart] _ProductTitleItemViewState::build (0xb11aa4)
    //     0xb11b80: ldr             x1, [x1, #0xae8]
    // 0xb11b84: r0 = AllocateClosure()
    //     0xb11b84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb11b88: mov             x1, x0
    // 0xb11b8c: ldur            x0, [fp, #-0x38]
    // 0xb11b90: StoreField: r0->field_1b = r1
    //     0xb11b90: stur            w1, [x0, #0x1b]
    // 0xb11b94: ldur            x1, [fp, #-0x40]
    // 0xb11b98: ArrayStore: r0[0] = r1  ; List_4
    //     0xb11b98: stur            w1, [x0, #0x17]
    // 0xb11b9c: ldur            x1, [fp, #-0x28]
    // 0xb11ba0: LoadField: r2 = r1->field_4b
    //     0xb11ba0: ldur            w2, [x1, #0x4b]
    // 0xb11ba4: DecompressPointer r2
    //     0xb11ba4: add             x2, x2, HEAP, lsl #32
    // 0xb11ba8: stur            x2, [fp, #-0x20]
    // 0xb11bac: cmp             w2, NULL
    // 0xb11bb0: b.ne            #0xb11bbc
    // 0xb11bb4: r1 = Null
    //     0xb11bb4: mov             x1, NULL
    // 0xb11bb8: b               #0xb11bd4
    // 0xb11bbc: LoadField: r1 = r2->field_7
    //     0xb11bbc: ldur            w1, [x2, #7]
    // 0xb11bc0: cbnz            w1, #0xb11bcc
    // 0xb11bc4: r3 = false
    //     0xb11bc4: add             x3, NULL, #0x30  ; false
    // 0xb11bc8: b               #0xb11bd0
    // 0xb11bcc: r3 = true
    //     0xb11bcc: add             x3, NULL, #0x20  ; true
    // 0xb11bd0: mov             x1, x3
    // 0xb11bd4: cmp             w1, NULL
    // 0xb11bd8: b.ne            #0xb11be0
    // 0xb11bdc: r1 = false
    //     0xb11bdc: add             x1, NULL, #0x30  ; false
    // 0xb11be0: stur            x1, [fp, #-0x18]
    // 0xb11be4: r0 = Radius()
    //     0xb11be4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb11be8: d0 = 50.000000
    //     0xb11be8: ldr             d0, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0xb11bec: stur            x0, [fp, #-0x28]
    // 0xb11bf0: StoreField: r0->field_7 = d0
    //     0xb11bf0: stur            d0, [x0, #7]
    // 0xb11bf4: StoreField: r0->field_f = d0
    //     0xb11bf4: stur            d0, [x0, #0xf]
    // 0xb11bf8: r0 = BorderRadius()
    //     0xb11bf8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb11bfc: mov             x1, x0
    // 0xb11c00: ldur            x0, [fp, #-0x28]
    // 0xb11c04: stur            x1, [fp, #-0x40]
    // 0xb11c08: StoreField: r1->field_7 = r0
    //     0xb11c08: stur            w0, [x1, #7]
    // 0xb11c0c: StoreField: r1->field_b = r0
    //     0xb11c0c: stur            w0, [x1, #0xb]
    // 0xb11c10: StoreField: r1->field_f = r0
    //     0xb11c10: stur            w0, [x1, #0xf]
    // 0xb11c14: StoreField: r1->field_13 = r0
    //     0xb11c14: stur            w0, [x1, #0x13]
    // 0xb11c18: r0 = BoxDecoration()
    //     0xb11c18: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb11c1c: mov             x2, x0
    // 0xb11c20: r0 = Instance_Color
    //     0xb11c20: add             x0, PP, #0x40, lsl #12  ; [pp+0x40860] Obj!Color@d6b0a1
    //     0xb11c24: ldr             x0, [x0, #0x860]
    // 0xb11c28: stur            x2, [fp, #-0x48]
    // 0xb11c2c: StoreField: r2->field_7 = r0
    //     0xb11c2c: stur            w0, [x2, #7]
    // 0xb11c30: ldur            x1, [fp, #-0x40]
    // 0xb11c34: StoreField: r2->field_13 = r1
    //     0xb11c34: stur            w1, [x2, #0x13]
    // 0xb11c38: r3 = Instance_BoxShape
    //     0xb11c38: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb11c3c: ldr             x3, [x3, #0x80]
    // 0xb11c40: StoreField: r2->field_23 = r3
    //     0xb11c40: stur            w3, [x2, #0x23]
    // 0xb11c44: ldur            x1, [fp, #-0x20]
    // 0xb11c48: cmp             w1, NULL
    // 0xb11c4c: b.ne            #0xb11c58
    // 0xb11c50: r4 = Null
    //     0xb11c50: mov             x4, NULL
    // 0xb11c54: b               #0xb11c70
    // 0xb11c58: LoadField: r4 = r1->field_7
    //     0xb11c58: ldur            w4, [x1, #7]
    // 0xb11c5c: cbnz            w4, #0xb11c68
    // 0xb11c60: r5 = false
    //     0xb11c60: add             x5, NULL, #0x30  ; false
    // 0xb11c64: b               #0xb11c6c
    // 0xb11c68: r5 = true
    //     0xb11c68: add             x5, NULL, #0x20  ; true
    // 0xb11c6c: mov             x4, x5
    // 0xb11c70: cmp             w4, NULL
    // 0xb11c74: b.ne            #0xb11c7c
    // 0xb11c78: r4 = false
    //     0xb11c78: add             x4, NULL, #0x30  ; false
    // 0xb11c7c: stur            x4, [fp, #-0x28]
    // 0xb11c80: cmp             w1, NULL
    // 0xb11c84: b.ne            #0xb11c90
    // 0xb11c88: r7 = ""
    //     0xb11c88: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb11c8c: b               #0xb11c94
    // 0xb11c90: mov             x7, x1
    // 0xb11c94: ldur            x6, [fp, #-8]
    // 0xb11c98: ldur            x5, [fp, #-0x18]
    // 0xb11c9c: ldur            x1, [fp, #-0x10]
    // 0xb11ca0: stur            x7, [fp, #-0x20]
    // 0xb11ca4: r0 = of()
    //     0xb11ca4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb11ca8: LoadField: r1 = r0->field_87
    //     0xb11ca8: ldur            w1, [x0, #0x87]
    // 0xb11cac: DecompressPointer r1
    //     0xb11cac: add             x1, x1, HEAP, lsl #32
    // 0xb11cb0: LoadField: r0 = r1->field_7
    //     0xb11cb0: ldur            w0, [x1, #7]
    // 0xb11cb4: DecompressPointer r0
    //     0xb11cb4: add             x0, x0, HEAP, lsl #32
    // 0xb11cb8: r16 = 12.000000
    //     0xb11cb8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb11cbc: ldr             x16, [x16, #0x9e8]
    // 0xb11cc0: r30 = Instance_Color
    //     0xb11cc0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb11cc4: stp             lr, x16, [SP]
    // 0xb11cc8: mov             x1, x0
    // 0xb11ccc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb11ccc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb11cd0: ldr             x4, [x4, #0xaa0]
    // 0xb11cd4: r0 = copyWith()
    //     0xb11cd4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb11cd8: stur            x0, [fp, #-0x10]
    // 0xb11cdc: r0 = Text()
    //     0xb11cdc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb11ce0: mov             x1, x0
    // 0xb11ce4: ldur            x0, [fp, #-0x20]
    // 0xb11ce8: stur            x1, [fp, #-0x40]
    // 0xb11cec: StoreField: r1->field_b = r0
    //     0xb11cec: stur            w0, [x1, #0xb]
    // 0xb11cf0: ldur            x0, [fp, #-0x10]
    // 0xb11cf4: StoreField: r1->field_13 = r0
    //     0xb11cf4: stur            w0, [x1, #0x13]
    // 0xb11cf8: r0 = Instance_TextAlign
    //     0xb11cf8: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb11cfc: StoreField: r1->field_1b = r0
    //     0xb11cfc: stur            w0, [x1, #0x1b]
    // 0xb11d00: r0 = Padding()
    //     0xb11d00: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb11d04: mov             x3, x0
    // 0xb11d08: r0 = Instance_EdgeInsets
    //     0xb11d08: add             x0, PP, #0x57, lsl #12  ; [pp+0x57af0] Obj!EdgeInsets@d590f1
    //     0xb11d0c: ldr             x0, [x0, #0xaf0]
    // 0xb11d10: stur            x3, [fp, #-0x10]
    // 0xb11d14: StoreField: r3->field_f = r0
    //     0xb11d14: stur            w0, [x3, #0xf]
    // 0xb11d18: ldur            x0, [fp, #-0x40]
    // 0xb11d1c: StoreField: r3->field_b = r0
    //     0xb11d1c: stur            w0, [x3, #0xb]
    // 0xb11d20: r1 = Null
    //     0xb11d20: mov             x1, NULL
    // 0xb11d24: r2 = 2
    //     0xb11d24: movz            x2, #0x2
    // 0xb11d28: r0 = AllocateArray()
    //     0xb11d28: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb11d2c: mov             x2, x0
    // 0xb11d30: ldur            x0, [fp, #-0x10]
    // 0xb11d34: stur            x2, [fp, #-0x20]
    // 0xb11d38: StoreField: r2->field_f = r0
    //     0xb11d38: stur            w0, [x2, #0xf]
    // 0xb11d3c: r1 = <Widget>
    //     0xb11d3c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb11d40: r0 = AllocateGrowableArray()
    //     0xb11d40: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb11d44: mov             x1, x0
    // 0xb11d48: ldur            x0, [fp, #-0x20]
    // 0xb11d4c: stur            x1, [fp, #-0x10]
    // 0xb11d50: StoreField: r1->field_f = r0
    //     0xb11d50: stur            w0, [x1, #0xf]
    // 0xb11d54: r0 = 2
    //     0xb11d54: movz            x0, #0x2
    // 0xb11d58: StoreField: r1->field_b = r0
    //     0xb11d58: stur            w0, [x1, #0xb]
    // 0xb11d5c: r0 = Row()
    //     0xb11d5c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb11d60: mov             x1, x0
    // 0xb11d64: r0 = Instance_Axis
    //     0xb11d64: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb11d68: stur            x1, [fp, #-0x20]
    // 0xb11d6c: StoreField: r1->field_f = r0
    //     0xb11d6c: stur            w0, [x1, #0xf]
    // 0xb11d70: r2 = Instance_MainAxisAlignment
    //     0xb11d70: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb11d74: ldr             x2, [x2, #0xab0]
    // 0xb11d78: StoreField: r1->field_13 = r2
    //     0xb11d78: stur            w2, [x1, #0x13]
    // 0xb11d7c: r3 = Instance_MainAxisSize
    //     0xb11d7c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb11d80: ldr             x3, [x3, #0xa10]
    // 0xb11d84: ArrayStore: r1[0] = r3  ; List_4
    //     0xb11d84: stur            w3, [x1, #0x17]
    // 0xb11d88: r4 = Instance_CrossAxisAlignment
    //     0xb11d88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb11d8c: ldr             x4, [x4, #0xa18]
    // 0xb11d90: StoreField: r1->field_1b = r4
    //     0xb11d90: stur            w4, [x1, #0x1b]
    // 0xb11d94: r5 = Instance_VerticalDirection
    //     0xb11d94: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb11d98: ldr             x5, [x5, #0xa20]
    // 0xb11d9c: StoreField: r1->field_23 = r5
    //     0xb11d9c: stur            w5, [x1, #0x23]
    // 0xb11da0: r6 = Instance_Clip
    //     0xb11da0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb11da4: ldr             x6, [x6, #0x38]
    // 0xb11da8: StoreField: r1->field_2b = r6
    //     0xb11da8: stur            w6, [x1, #0x2b]
    // 0xb11dac: StoreField: r1->field_2f = rZR
    //     0xb11dac: stur            xzr, [x1, #0x2f]
    // 0xb11db0: ldur            x7, [fp, #-0x10]
    // 0xb11db4: StoreField: r1->field_b = r7
    //     0xb11db4: stur            w7, [x1, #0xb]
    // 0xb11db8: r0 = Center()
    //     0xb11db8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb11dbc: mov             x1, x0
    // 0xb11dc0: r0 = Instance_Alignment
    //     0xb11dc0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb11dc4: ldr             x0, [x0, #0xb10]
    // 0xb11dc8: stur            x1, [fp, #-0x10]
    // 0xb11dcc: StoreField: r1->field_f = r0
    //     0xb11dcc: stur            w0, [x1, #0xf]
    // 0xb11dd0: ldur            x2, [fp, #-0x20]
    // 0xb11dd4: StoreField: r1->field_b = r2
    //     0xb11dd4: stur            w2, [x1, #0xb]
    // 0xb11dd8: r0 = Visibility()
    //     0xb11dd8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb11ddc: mov             x1, x0
    // 0xb11de0: ldur            x0, [fp, #-0x10]
    // 0xb11de4: stur            x1, [fp, #-0x20]
    // 0xb11de8: StoreField: r1->field_b = r0
    //     0xb11de8: stur            w0, [x1, #0xb]
    // 0xb11dec: r0 = Instance_SizedBox
    //     0xb11dec: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb11df0: StoreField: r1->field_f = r0
    //     0xb11df0: stur            w0, [x1, #0xf]
    // 0xb11df4: ldur            x2, [fp, #-0x28]
    // 0xb11df8: StoreField: r1->field_13 = r2
    //     0xb11df8: stur            w2, [x1, #0x13]
    // 0xb11dfc: r2 = false
    //     0xb11dfc: add             x2, NULL, #0x30  ; false
    // 0xb11e00: ArrayStore: r1[0] = r2  ; List_4
    //     0xb11e00: stur            w2, [x1, #0x17]
    // 0xb11e04: StoreField: r1->field_1b = r2
    //     0xb11e04: stur            w2, [x1, #0x1b]
    // 0xb11e08: StoreField: r1->field_1f = r2
    //     0xb11e08: stur            w2, [x1, #0x1f]
    // 0xb11e0c: StoreField: r1->field_23 = r2
    //     0xb11e0c: stur            w2, [x1, #0x23]
    // 0xb11e10: StoreField: r1->field_27 = r2
    //     0xb11e10: stur            w2, [x1, #0x27]
    // 0xb11e14: StoreField: r1->field_2b = r2
    //     0xb11e14: stur            w2, [x1, #0x2b]
    // 0xb11e18: r0 = Container()
    //     0xb11e18: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb11e1c: stur            x0, [fp, #-0x10]
    // 0xb11e20: ldur            x16, [fp, #-0x48]
    // 0xb11e24: r30 = Instance_EdgeInsets
    //     0xb11e24: add             lr, PP, #0x36, lsl #12  ; [pp+0x36c10] Obj!EdgeInsets@d57c81
    //     0xb11e28: ldr             lr, [lr, #0xc10]
    // 0xb11e2c: stp             lr, x16, [SP, #8]
    // 0xb11e30: ldur            x16, [fp, #-0x20]
    // 0xb11e34: str             x16, [SP]
    // 0xb11e38: mov             x1, x0
    // 0xb11e3c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xb11e3c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xb11e40: ldr             x4, [x4, #0xb40]
    // 0xb11e44: r0 = Container()
    //     0xb11e44: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb11e48: r0 = IntrinsicWidth()
    //     0xb11e48: bl              #0xa898a4  ; AllocateIntrinsicWidthStub -> IntrinsicWidth (size=0x18)
    // 0xb11e4c: mov             x1, x0
    // 0xb11e50: ldur            x0, [fp, #-0x10]
    // 0xb11e54: stur            x1, [fp, #-0x20]
    // 0xb11e58: StoreField: r1->field_b = r0
    //     0xb11e58: stur            w0, [x1, #0xb]
    // 0xb11e5c: r0 = Padding()
    //     0xb11e5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb11e60: mov             x1, x0
    // 0xb11e64: r0 = Instance_EdgeInsets
    //     0xb11e64: add             x0, PP, #0x57, lsl #12  ; [pp+0x57af8] Obj!EdgeInsets@d590c1
    //     0xb11e68: ldr             x0, [x0, #0xaf8]
    // 0xb11e6c: stur            x1, [fp, #-0x10]
    // 0xb11e70: StoreField: r1->field_f = r0
    //     0xb11e70: stur            w0, [x1, #0xf]
    // 0xb11e74: ldur            x2, [fp, #-0x20]
    // 0xb11e78: StoreField: r1->field_b = r2
    //     0xb11e78: stur            w2, [x1, #0xb]
    // 0xb11e7c: r0 = Visibility()
    //     0xb11e7c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb11e80: mov             x1, x0
    // 0xb11e84: ldur            x0, [fp, #-0x10]
    // 0xb11e88: stur            x1, [fp, #-0x20]
    // 0xb11e8c: StoreField: r1->field_b = r0
    //     0xb11e8c: stur            w0, [x1, #0xb]
    // 0xb11e90: r0 = Instance_SizedBox
    //     0xb11e90: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb11e94: StoreField: r1->field_f = r0
    //     0xb11e94: stur            w0, [x1, #0xf]
    // 0xb11e98: ldur            x0, [fp, #-0x18]
    // 0xb11e9c: StoreField: r1->field_13 = r0
    //     0xb11e9c: stur            w0, [x1, #0x13]
    // 0xb11ea0: r0 = false
    //     0xb11ea0: add             x0, NULL, #0x30  ; false
    // 0xb11ea4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb11ea4: stur            w0, [x1, #0x17]
    // 0xb11ea8: StoreField: r1->field_1b = r0
    //     0xb11ea8: stur            w0, [x1, #0x1b]
    // 0xb11eac: StoreField: r1->field_1f = r0
    //     0xb11eac: stur            w0, [x1, #0x1f]
    // 0xb11eb0: StoreField: r1->field_23 = r0
    //     0xb11eb0: stur            w0, [x1, #0x23]
    // 0xb11eb4: StoreField: r1->field_27 = r0
    //     0xb11eb4: stur            w0, [x1, #0x27]
    // 0xb11eb8: StoreField: r1->field_2b = r0
    //     0xb11eb8: stur            w0, [x1, #0x2b]
    // 0xb11ebc: r0 = Radius()
    //     0xb11ebc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb11ec0: d0 = 50.000000
    //     0xb11ec0: ldr             d0, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0xb11ec4: stur            x0, [fp, #-0x10]
    // 0xb11ec8: StoreField: r0->field_7 = d0
    //     0xb11ec8: stur            d0, [x0, #7]
    // 0xb11ecc: StoreField: r0->field_f = d0
    //     0xb11ecc: stur            d0, [x0, #0xf]
    // 0xb11ed0: r0 = BorderRadius()
    //     0xb11ed0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb11ed4: mov             x1, x0
    // 0xb11ed8: ldur            x0, [fp, #-0x10]
    // 0xb11edc: stur            x1, [fp, #-0x18]
    // 0xb11ee0: StoreField: r1->field_7 = r0
    //     0xb11ee0: stur            w0, [x1, #7]
    // 0xb11ee4: StoreField: r1->field_b = r0
    //     0xb11ee4: stur            w0, [x1, #0xb]
    // 0xb11ee8: StoreField: r1->field_f = r0
    //     0xb11ee8: stur            w0, [x1, #0xf]
    // 0xb11eec: StoreField: r1->field_13 = r0
    //     0xb11eec: stur            w0, [x1, #0x13]
    // 0xb11ef0: r0 = BoxDecoration()
    //     0xb11ef0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb11ef4: mov             x3, x0
    // 0xb11ef8: r0 = Instance_Color
    //     0xb11ef8: add             x0, PP, #0x40, lsl #12  ; [pp+0x40860] Obj!Color@d6b0a1
    //     0xb11efc: ldr             x0, [x0, #0x860]
    // 0xb11f00: stur            x3, [fp, #-0x10]
    // 0xb11f04: StoreField: r3->field_7 = r0
    //     0xb11f04: stur            w0, [x3, #7]
    // 0xb11f08: ldur            x0, [fp, #-0x18]
    // 0xb11f0c: StoreField: r3->field_13 = r0
    //     0xb11f0c: stur            w0, [x3, #0x13]
    // 0xb11f10: r0 = Instance_BoxShape
    //     0xb11f10: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb11f14: ldr             x0, [x0, #0x80]
    // 0xb11f18: StoreField: r3->field_23 = r0
    //     0xb11f18: stur            w0, [x3, #0x23]
    // 0xb11f1c: ldur            x1, [fp, #-8]
    // 0xb11f20: LoadField: r2 = r1->field_b
    //     0xb11f20: ldur            w2, [x1, #0xb]
    // 0xb11f24: DecompressPointer r2
    //     0xb11f24: add             x2, x2, HEAP, lsl #32
    // 0xb11f28: cmp             w2, NULL
    // 0xb11f2c: b.eq            #0xb12394
    // 0xb11f30: LoadField: r4 = r2->field_27
    //     0xb11f30: ldur            w4, [x2, #0x27]
    // 0xb11f34: DecompressPointer r4
    //     0xb11f34: add             x4, x4, HEAP, lsl #32
    // 0xb11f38: stur            x4, [fp, #-8]
    // 0xb11f3c: cmp             w4, NULL
    // 0xb11f40: b.ne            #0xb11f4c
    // 0xb11f44: r1 = Null
    //     0xb11f44: mov             x1, NULL
    // 0xb11f48: b               #0xb11f54
    // 0xb11f4c: LoadField: r1 = r4->field_b
    //     0xb11f4c: ldur            w1, [x4, #0xb]
    // 0xb11f50: DecompressPointer r1
    //     0xb11f50: add             x1, x1, HEAP, lsl #32
    // 0xb11f54: cmp             w1, NULL
    // 0xb11f58: b.eq            #0xb11fbc
    // 0xb11f5c: r2 = LoadInt32Instr(r1)
    //     0xb11f5c: sbfx            x2, x1, #1, #0x1f
    //     0xb11f60: tbz             w1, #0, #0xb11f68
    //     0xb11f64: ldur            x2, [x1, #7]
    // 0xb11f68: cmp             x2, #0
    // 0xb11f6c: b.le            #0xb11fbc
    // 0xb11f70: r1 = Null
    //     0xb11f70: mov             x1, NULL
    // 0xb11f74: r2 = 4
    //     0xb11f74: movz            x2, #0x4
    // 0xb11f78: r0 = AllocateArray()
    //     0xb11f78: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb11f7c: r16 = "Get this as low as "
    //     0xb11f7c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52608] "Get this as low as "
    //     0xb11f80: ldr             x16, [x16, #0x608]
    // 0xb11f84: StoreField: r0->field_f = r16
    //     0xb11f84: stur            w16, [x0, #0xf]
    // 0xb11f88: ldur            x1, [fp, #-8]
    // 0xb11f8c: cmp             w1, NULL
    // 0xb11f90: b.ne            #0xb11f9c
    // 0xb11f94: r1 = Null
    //     0xb11f94: mov             x1, NULL
    // 0xb11f98: b               #0xb11fa8
    // 0xb11f9c: LoadField: r2 = r1->field_13
    //     0xb11f9c: ldur            w2, [x1, #0x13]
    // 0xb11fa0: DecompressPointer r2
    //     0xb11fa0: add             x2, x2, HEAP, lsl #32
    // 0xb11fa4: mov             x1, x2
    // 0xb11fa8: StoreField: r0->field_13 = r1
    //     0xb11fa8: stur            w1, [x0, #0x13]
    // 0xb11fac: str             x0, [SP]
    // 0xb11fb0: r0 = _interpolate()
    //     0xb11fb0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb11fb4: mov             x4, x0
    // 0xb11fb8: b               #0xb11fc4
    // 0xb11fbc: r4 = "View available offers"
    //     0xb11fbc: add             x4, PP, #0x52, lsl #12  ; [pp+0x52610] "View available offers"
    //     0xb11fc0: ldr             x4, [x4, #0x610]
    // 0xb11fc4: ldur            x3, [fp, #-0x30]
    // 0xb11fc8: ldur            x2, [fp, #-0x38]
    // 0xb11fcc: ldur            x0, [fp, #-0x20]
    // 0xb11fd0: stur            x4, [fp, #-8]
    // 0xb11fd4: LoadField: r1 = r3->field_13
    //     0xb11fd4: ldur            w1, [x3, #0x13]
    // 0xb11fd8: DecompressPointer r1
    //     0xb11fd8: add             x1, x1, HEAP, lsl #32
    // 0xb11fdc: r0 = of()
    //     0xb11fdc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb11fe0: LoadField: r1 = r0->field_87
    //     0xb11fe0: ldur            w1, [x0, #0x87]
    // 0xb11fe4: DecompressPointer r1
    //     0xb11fe4: add             x1, x1, HEAP, lsl #32
    // 0xb11fe8: LoadField: r0 = r1->field_7
    //     0xb11fe8: ldur            w0, [x1, #7]
    // 0xb11fec: DecompressPointer r0
    //     0xb11fec: add             x0, x0, HEAP, lsl #32
    // 0xb11ff0: r16 = 12.000000
    //     0xb11ff0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb11ff4: ldr             x16, [x16, #0x9e8]
    // 0xb11ff8: r30 = Instance_Color
    //     0xb11ff8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb11ffc: stp             lr, x16, [SP]
    // 0xb12000: mov             x1, x0
    // 0xb12004: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb12004: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb12008: ldr             x4, [x4, #0xaa0]
    // 0xb1200c: r0 = copyWith()
    //     0xb1200c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb12010: stur            x0, [fp, #-0x18]
    // 0xb12014: r0 = Text()
    //     0xb12014: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb12018: mov             x1, x0
    // 0xb1201c: ldur            x0, [fp, #-8]
    // 0xb12020: stur            x1, [fp, #-0x28]
    // 0xb12024: StoreField: r1->field_b = r0
    //     0xb12024: stur            w0, [x1, #0xb]
    // 0xb12028: ldur            x0, [fp, #-0x18]
    // 0xb1202c: StoreField: r1->field_13 = r0
    //     0xb1202c: stur            w0, [x1, #0x13]
    // 0xb12030: r0 = Instance_TextAlign
    //     0xb12030: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb12034: StoreField: r1->field_1b = r0
    //     0xb12034: stur            w0, [x1, #0x1b]
    // 0xb12038: r0 = Padding()
    //     0xb12038: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1203c: mov             x3, x0
    // 0xb12040: r0 = Instance_EdgeInsets
    //     0xb12040: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xb12044: ldr             x0, [x0, #0xe60]
    // 0xb12048: stur            x3, [fp, #-8]
    // 0xb1204c: StoreField: r3->field_f = r0
    //     0xb1204c: stur            w0, [x3, #0xf]
    // 0xb12050: ldur            x0, [fp, #-0x28]
    // 0xb12054: StoreField: r3->field_b = r0
    //     0xb12054: stur            w0, [x3, #0xb]
    // 0xb12058: r1 = Null
    //     0xb12058: mov             x1, NULL
    // 0xb1205c: r2 = 6
    //     0xb1205c: movz            x2, #0x6
    // 0xb12060: r0 = AllocateArray()
    //     0xb12060: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb12064: mov             x2, x0
    // 0xb12068: ldur            x0, [fp, #-8]
    // 0xb1206c: stur            x2, [fp, #-0x18]
    // 0xb12070: StoreField: r2->field_f = r0
    //     0xb12070: stur            w0, [x2, #0xf]
    // 0xb12074: r16 = Instance_Spacer
    //     0xb12074: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb12078: ldr             x16, [x16, #0xf0]
    // 0xb1207c: StoreField: r2->field_13 = r16
    //     0xb1207c: stur            w16, [x2, #0x13]
    // 0xb12080: r16 = Instance_Padding
    //     0xb12080: add             x16, PP, #0x57, lsl #12  ; [pp+0x57b00] Obj!Padding@d684e1
    //     0xb12084: ldr             x16, [x16, #0xb00]
    // 0xb12088: ArrayStore: r2[0] = r16  ; List_4
    //     0xb12088: stur            w16, [x2, #0x17]
    // 0xb1208c: r1 = <Widget>
    //     0xb1208c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb12090: r0 = AllocateGrowableArray()
    //     0xb12090: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb12094: mov             x1, x0
    // 0xb12098: ldur            x0, [fp, #-0x18]
    // 0xb1209c: stur            x1, [fp, #-8]
    // 0xb120a0: StoreField: r1->field_f = r0
    //     0xb120a0: stur            w0, [x1, #0xf]
    // 0xb120a4: r0 = 6
    //     0xb120a4: movz            x0, #0x6
    // 0xb120a8: StoreField: r1->field_b = r0
    //     0xb120a8: stur            w0, [x1, #0xb]
    // 0xb120ac: r0 = Row()
    //     0xb120ac: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb120b0: mov             x1, x0
    // 0xb120b4: r0 = Instance_Axis
    //     0xb120b4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb120b8: stur            x1, [fp, #-0x18]
    // 0xb120bc: StoreField: r1->field_f = r0
    //     0xb120bc: stur            w0, [x1, #0xf]
    // 0xb120c0: r2 = Instance_MainAxisAlignment
    //     0xb120c0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb120c4: ldr             x2, [x2, #0xab0]
    // 0xb120c8: StoreField: r1->field_13 = r2
    //     0xb120c8: stur            w2, [x1, #0x13]
    // 0xb120cc: r2 = Instance_MainAxisSize
    //     0xb120cc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb120d0: ldr             x2, [x2, #0xa10]
    // 0xb120d4: ArrayStore: r1[0] = r2  ; List_4
    //     0xb120d4: stur            w2, [x1, #0x17]
    // 0xb120d8: r3 = Instance_CrossAxisAlignment
    //     0xb120d8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb120dc: ldr             x3, [x3, #0xa18]
    // 0xb120e0: StoreField: r1->field_1b = r3
    //     0xb120e0: stur            w3, [x1, #0x1b]
    // 0xb120e4: r4 = Instance_VerticalDirection
    //     0xb120e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb120e8: ldr             x4, [x4, #0xa20]
    // 0xb120ec: StoreField: r1->field_23 = r4
    //     0xb120ec: stur            w4, [x1, #0x23]
    // 0xb120f0: r5 = Instance_Clip
    //     0xb120f0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb120f4: ldr             x5, [x5, #0x38]
    // 0xb120f8: StoreField: r1->field_2b = r5
    //     0xb120f8: stur            w5, [x1, #0x2b]
    // 0xb120fc: StoreField: r1->field_2f = rZR
    //     0xb120fc: stur            xzr, [x1, #0x2f]
    // 0xb12100: ldur            x6, [fp, #-8]
    // 0xb12104: StoreField: r1->field_b = r6
    //     0xb12104: stur            w6, [x1, #0xb]
    // 0xb12108: r0 = Center()
    //     0xb12108: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb1210c: mov             x1, x0
    // 0xb12110: r0 = Instance_Alignment
    //     0xb12110: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb12114: ldr             x0, [x0, #0xb10]
    // 0xb12118: stur            x1, [fp, #-8]
    // 0xb1211c: StoreField: r1->field_f = r0
    //     0xb1211c: stur            w0, [x1, #0xf]
    // 0xb12120: ldur            x0, [fp, #-0x18]
    // 0xb12124: StoreField: r1->field_b = r0
    //     0xb12124: stur            w0, [x1, #0xb]
    // 0xb12128: r0 = Container()
    //     0xb12128: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1212c: stur            x0, [fp, #-0x18]
    // 0xb12130: ldur            x16, [fp, #-0x10]
    // 0xb12134: r30 = Instance_EdgeInsets
    //     0xb12134: add             lr, PP, #0x36, lsl #12  ; [pp+0x36c10] Obj!EdgeInsets@d57c81
    //     0xb12138: ldr             lr, [lr, #0xc10]
    // 0xb1213c: stp             lr, x16, [SP, #8]
    // 0xb12140: ldur            x16, [fp, #-8]
    // 0xb12144: str             x16, [SP]
    // 0xb12148: mov             x1, x0
    // 0xb1214c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xb1214c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xb12150: ldr             x4, [x4, #0xb40]
    // 0xb12154: r0 = Container()
    //     0xb12154: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb12158: r0 = IntrinsicWidth()
    //     0xb12158: bl              #0xa898a4  ; AllocateIntrinsicWidthStub -> IntrinsicWidth (size=0x18)
    // 0xb1215c: mov             x1, x0
    // 0xb12160: ldur            x0, [fp, #-0x18]
    // 0xb12164: stur            x1, [fp, #-8]
    // 0xb12168: StoreField: r1->field_b = r0
    //     0xb12168: stur            w0, [x1, #0xb]
    // 0xb1216c: r0 = InkWell()
    //     0xb1216c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb12170: mov             x3, x0
    // 0xb12174: ldur            x0, [fp, #-8]
    // 0xb12178: stur            x3, [fp, #-0x10]
    // 0xb1217c: StoreField: r3->field_b = r0
    //     0xb1217c: stur            w0, [x3, #0xb]
    // 0xb12180: ldur            x2, [fp, #-0x30]
    // 0xb12184: r1 = Function '<anonymous closure>':.
    //     0xb12184: add             x1, PP, #0x57, lsl #12  ; [pp+0x57b08] AnonymousClosure: (0xb123bc), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_title_item_view.dart] _ProductTitleItemViewState::build (0xb11aa4)
    //     0xb12188: ldr             x1, [x1, #0xb08]
    // 0xb1218c: r0 = AllocateClosure()
    //     0xb1218c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb12190: mov             x1, x0
    // 0xb12194: ldur            x0, [fp, #-0x10]
    // 0xb12198: StoreField: r0->field_f = r1
    //     0xb12198: stur            w1, [x0, #0xf]
    // 0xb1219c: r1 = true
    //     0xb1219c: add             x1, NULL, #0x20  ; true
    // 0xb121a0: StoreField: r0->field_43 = r1
    //     0xb121a0: stur            w1, [x0, #0x43]
    // 0xb121a4: r2 = Instance_BoxShape
    //     0xb121a4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb121a8: ldr             x2, [x2, #0x80]
    // 0xb121ac: StoreField: r0->field_47 = r2
    //     0xb121ac: stur            w2, [x0, #0x47]
    // 0xb121b0: StoreField: r0->field_6f = r1
    //     0xb121b0: stur            w1, [x0, #0x6f]
    // 0xb121b4: r2 = false
    //     0xb121b4: add             x2, NULL, #0x30  ; false
    // 0xb121b8: StoreField: r0->field_73 = r2
    //     0xb121b8: stur            w2, [x0, #0x73]
    // 0xb121bc: StoreField: r0->field_83 = r1
    //     0xb121bc: stur            w1, [x0, #0x83]
    // 0xb121c0: StoreField: r0->field_7b = r2
    //     0xb121c0: stur            w2, [x0, #0x7b]
    // 0xb121c4: r0 = Padding()
    //     0xb121c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb121c8: mov             x3, x0
    // 0xb121cc: r0 = Instance_EdgeInsets
    //     0xb121cc: add             x0, PP, #0x57, lsl #12  ; [pp+0x57af8] Obj!EdgeInsets@d590c1
    //     0xb121d0: ldr             x0, [x0, #0xaf8]
    // 0xb121d4: stur            x3, [fp, #-8]
    // 0xb121d8: StoreField: r3->field_f = r0
    //     0xb121d8: stur            w0, [x3, #0xf]
    // 0xb121dc: ldur            x0, [fp, #-0x10]
    // 0xb121e0: StoreField: r3->field_b = r0
    //     0xb121e0: stur            w0, [x3, #0xb]
    // 0xb121e4: r1 = Null
    //     0xb121e4: mov             x1, NULL
    // 0xb121e8: r2 = 4
    //     0xb121e8: movz            x2, #0x4
    // 0xb121ec: r0 = AllocateArray()
    //     0xb121ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb121f0: mov             x2, x0
    // 0xb121f4: ldur            x0, [fp, #-0x20]
    // 0xb121f8: stur            x2, [fp, #-0x10]
    // 0xb121fc: StoreField: r2->field_f = r0
    //     0xb121fc: stur            w0, [x2, #0xf]
    // 0xb12200: ldur            x0, [fp, #-8]
    // 0xb12204: StoreField: r2->field_13 = r0
    //     0xb12204: stur            w0, [x2, #0x13]
    // 0xb12208: r1 = <Widget>
    //     0xb12208: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1220c: r0 = AllocateGrowableArray()
    //     0xb1220c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb12210: mov             x1, x0
    // 0xb12214: ldur            x0, [fp, #-0x10]
    // 0xb12218: stur            x1, [fp, #-8]
    // 0xb1221c: StoreField: r1->field_f = r0
    //     0xb1221c: stur            w0, [x1, #0xf]
    // 0xb12220: r2 = 4
    //     0xb12220: movz            x2, #0x4
    // 0xb12224: StoreField: r1->field_b = r2
    //     0xb12224: stur            w2, [x1, #0xb]
    // 0xb12228: r0 = Row()
    //     0xb12228: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb1222c: mov             x1, x0
    // 0xb12230: r0 = Instance_Axis
    //     0xb12230: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb12234: stur            x1, [fp, #-0x10]
    // 0xb12238: StoreField: r1->field_f = r0
    //     0xb12238: stur            w0, [x1, #0xf]
    // 0xb1223c: r0 = Instance_MainAxisAlignment
    //     0xb1223c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb12240: ldr             x0, [x0, #0xa08]
    // 0xb12244: StoreField: r1->field_13 = r0
    //     0xb12244: stur            w0, [x1, #0x13]
    // 0xb12248: r2 = Instance_MainAxisSize
    //     0xb12248: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb1224c: ldr             x2, [x2, #0xa10]
    // 0xb12250: ArrayStore: r1[0] = r2  ; List_4
    //     0xb12250: stur            w2, [x1, #0x17]
    // 0xb12254: r2 = Instance_CrossAxisAlignment
    //     0xb12254: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb12258: ldr             x2, [x2, #0xa18]
    // 0xb1225c: StoreField: r1->field_1b = r2
    //     0xb1225c: stur            w2, [x1, #0x1b]
    // 0xb12260: r2 = Instance_VerticalDirection
    //     0xb12260: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb12264: ldr             x2, [x2, #0xa20]
    // 0xb12268: StoreField: r1->field_23 = r2
    //     0xb12268: stur            w2, [x1, #0x23]
    // 0xb1226c: r3 = Instance_Clip
    //     0xb1226c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb12270: ldr             x3, [x3, #0x38]
    // 0xb12274: StoreField: r1->field_2b = r3
    //     0xb12274: stur            w3, [x1, #0x2b]
    // 0xb12278: StoreField: r1->field_2f = rZR
    //     0xb12278: stur            xzr, [x1, #0x2f]
    // 0xb1227c: ldur            x4, [fp, #-8]
    // 0xb12280: StoreField: r1->field_b = r4
    //     0xb12280: stur            w4, [x1, #0xb]
    // 0xb12284: r0 = Padding()
    //     0xb12284: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb12288: mov             x3, x0
    // 0xb1228c: r0 = Instance_EdgeInsets
    //     0xb1228c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b30] Obj!EdgeInsets@d588e1
    //     0xb12290: ldr             x0, [x0, #0xb30]
    // 0xb12294: stur            x3, [fp, #-8]
    // 0xb12298: StoreField: r3->field_f = r0
    //     0xb12298: stur            w0, [x3, #0xf]
    // 0xb1229c: ldur            x0, [fp, #-0x10]
    // 0xb122a0: StoreField: r3->field_b = r0
    //     0xb122a0: stur            w0, [x3, #0xb]
    // 0xb122a4: r1 = Null
    //     0xb122a4: mov             x1, NULL
    // 0xb122a8: r2 = 4
    //     0xb122a8: movz            x2, #0x4
    // 0xb122ac: r0 = AllocateArray()
    //     0xb122ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb122b0: mov             x2, x0
    // 0xb122b4: ldur            x0, [fp, #-0x38]
    // 0xb122b8: stur            x2, [fp, #-0x10]
    // 0xb122bc: StoreField: r2->field_f = r0
    //     0xb122bc: stur            w0, [x2, #0xf]
    // 0xb122c0: ldur            x0, [fp, #-8]
    // 0xb122c4: StoreField: r2->field_13 = r0
    //     0xb122c4: stur            w0, [x2, #0x13]
    // 0xb122c8: r1 = <Widget>
    //     0xb122c8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb122cc: r0 = AllocateGrowableArray()
    //     0xb122cc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb122d0: mov             x1, x0
    // 0xb122d4: ldur            x0, [fp, #-0x10]
    // 0xb122d8: stur            x1, [fp, #-8]
    // 0xb122dc: StoreField: r1->field_f = r0
    //     0xb122dc: stur            w0, [x1, #0xf]
    // 0xb122e0: r0 = 4
    //     0xb122e0: movz            x0, #0x4
    // 0xb122e4: StoreField: r1->field_b = r0
    //     0xb122e4: stur            w0, [x1, #0xb]
    // 0xb122e8: r0 = Column()
    //     0xb122e8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb122ec: mov             x1, x0
    // 0xb122f0: r0 = Instance_Axis
    //     0xb122f0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb122f4: stur            x1, [fp, #-0x10]
    // 0xb122f8: StoreField: r1->field_f = r0
    //     0xb122f8: stur            w0, [x1, #0xf]
    // 0xb122fc: r0 = Instance_MainAxisAlignment
    //     0xb122fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb12300: ldr             x0, [x0, #0xa08]
    // 0xb12304: StoreField: r1->field_13 = r0
    //     0xb12304: stur            w0, [x1, #0x13]
    // 0xb12308: r0 = Instance_MainAxisSize
    //     0xb12308: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb1230c: ldr             x0, [x0, #0xdd0]
    // 0xb12310: ArrayStore: r1[0] = r0  ; List_4
    //     0xb12310: stur            w0, [x1, #0x17]
    // 0xb12314: r0 = Instance_CrossAxisAlignment
    //     0xb12314: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb12318: ldr             x0, [x0, #0x890]
    // 0xb1231c: StoreField: r1->field_1b = r0
    //     0xb1231c: stur            w0, [x1, #0x1b]
    // 0xb12320: r0 = Instance_VerticalDirection
    //     0xb12320: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb12324: ldr             x0, [x0, #0xa20]
    // 0xb12328: StoreField: r1->field_23 = r0
    //     0xb12328: stur            w0, [x1, #0x23]
    // 0xb1232c: r0 = Instance_Clip
    //     0xb1232c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb12330: ldr             x0, [x0, #0x38]
    // 0xb12334: StoreField: r1->field_2b = r0
    //     0xb12334: stur            w0, [x1, #0x2b]
    // 0xb12338: StoreField: r1->field_2f = rZR
    //     0xb12338: stur            xzr, [x1, #0x2f]
    // 0xb1233c: ldur            x0, [fp, #-8]
    // 0xb12340: StoreField: r1->field_b = r0
    //     0xb12340: stur            w0, [x1, #0xb]
    // 0xb12344: r0 = Container()
    //     0xb12344: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb12348: stur            x0, [fp, #-8]
    // 0xb1234c: r16 = Instance_Color
    //     0xb1234c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb12350: ldr             x16, [x16, #0x90]
    // 0xb12354: r30 = Instance_EdgeInsets
    //     0xb12354: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb12358: ldr             lr, [lr, #0x668]
    // 0xb1235c: stp             lr, x16, [SP, #8]
    // 0xb12360: ldur            x16, [fp, #-0x10]
    // 0xb12364: str             x16, [SP]
    // 0xb12368: mov             x1, x0
    // 0xb1236c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x1, padding, 0x2, null]
    //     0xb1236c: add             x4, PP, #0x45, lsl #12  ; [pp+0x45c40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x1, "padding", 0x2, Null]
    //     0xb12370: ldr             x4, [x4, #0xc40]
    // 0xb12374: r0 = Container()
    //     0xb12374: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb12378: ldur            x0, [fp, #-8]
    // 0xb1237c: LeaveFrame
    //     0xb1237c: mov             SP, fp
    //     0xb12380: ldp             fp, lr, [SP], #0x10
    // 0xb12384: ret
    //     0xb12384: ret             
    // 0xb12388: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb12388: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1238c: b               #0xb11acc
    // 0xb12390: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb12390: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb12394: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb12394: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb123bc, size: 0x90
    // 0xb123bc: EnterFrame
    //     0xb123bc: stp             fp, lr, [SP, #-0x10]!
    //     0xb123c0: mov             fp, SP
    // 0xb123c4: AllocStack(0x18)
    //     0xb123c4: sub             SP, SP, #0x18
    // 0xb123c8: SetupParameters()
    //     0xb123c8: ldr             x0, [fp, #0x10]
    //     0xb123cc: ldur            w1, [x0, #0x17]
    //     0xb123d0: add             x1, x1, HEAP, lsl #32
    // 0xb123d4: CheckStackOverflow
    //     0xb123d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb123d8: cmp             SP, x16
    //     0xb123dc: b.ls            #0xb12440
    // 0xb123e0: LoadField: r0 = r1->field_f
    //     0xb123e0: ldur            w0, [x1, #0xf]
    // 0xb123e4: DecompressPointer r0
    //     0xb123e4: add             x0, x0, HEAP, lsl #32
    // 0xb123e8: LoadField: r2 = r0->field_b
    //     0xb123e8: ldur            w2, [x0, #0xb]
    // 0xb123ec: DecompressPointer r2
    //     0xb123ec: add             x2, x2, HEAP, lsl #32
    // 0xb123f0: cmp             w2, NULL
    // 0xb123f4: b.eq            #0xb12448
    // 0xb123f8: LoadField: r0 = r1->field_13
    //     0xb123f8: ldur            w0, [x1, #0x13]
    // 0xb123fc: DecompressPointer r0
    //     0xb123fc: add             x0, x0, HEAP, lsl #32
    // 0xb12400: LoadField: r1 = r2->field_1f
    //     0xb12400: ldur            w1, [x2, #0x1f]
    // 0xb12404: DecompressPointer r1
    //     0xb12404: add             x1, x1, HEAP, lsl #32
    // 0xb12408: r16 = "cosmetics"
    //     0xb12408: add             x16, PP, #0xd, lsl #12  ; [pp+0xd7e8] "cosmetics"
    //     0xb1240c: ldr             x16, [x16, #0x7e8]
    // 0xb12410: stp             x16, x1, [SP, #8]
    // 0xb12414: str             x0, [SP]
    // 0xb12418: r4 = 0
    //     0xb12418: movz            x4, #0
    // 0xb1241c: ldr             x0, [SP, #0x10]
    // 0xb12420: r16 = UnlinkedCall_0x613b5c
    //     0xb12420: add             x16, PP, #0x57, lsl #12  ; [pp+0x57b10] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb12424: add             x16, x16, #0xb10
    // 0xb12428: ldp             x5, lr, [x16]
    // 0xb1242c: blr             lr
    // 0xb12430: r0 = Null
    //     0xb12430: mov             x0, NULL
    // 0xb12434: LeaveFrame
    //     0xb12434: mov             SP, fp
    //     0xb12438: ldp             fp, lr, [SP], #0x10
    // 0xb1243c: ret
    //     0xb1243c: ret             
    // 0xb12440: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb12440: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb12444: b               #0xb123e0
    // 0xb12448: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb12448: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, WidgetRatings?) {
    // ** addr: 0xb1244c, size: 0x88
    // 0xb1244c: EnterFrame
    //     0xb1244c: stp             fp, lr, [SP, #-0x10]!
    //     0xb12450: mov             fp, SP
    // 0xb12454: AllocStack(0x18)
    //     0xb12454: sub             SP, SP, #0x18
    // 0xb12458: SetupParameters()
    //     0xb12458: ldr             x0, [fp, #0x20]
    //     0xb1245c: ldur            w1, [x0, #0x17]
    //     0xb12460: add             x1, x1, HEAP, lsl #32
    // 0xb12464: CheckStackOverflow
    //     0xb12464: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb12468: cmp             SP, x16
    //     0xb1246c: b.ls            #0xb124c8
    // 0xb12470: LoadField: r0 = r1->field_f
    //     0xb12470: ldur            w0, [x1, #0xf]
    // 0xb12474: DecompressPointer r0
    //     0xb12474: add             x0, x0, HEAP, lsl #32
    // 0xb12478: LoadField: r1 = r0->field_b
    //     0xb12478: ldur            w1, [x0, #0xb]
    // 0xb1247c: DecompressPointer r1
    //     0xb1247c: add             x1, x1, HEAP, lsl #32
    // 0xb12480: cmp             w1, NULL
    // 0xb12484: b.eq            #0xb124d0
    // 0xb12488: LoadField: r0 = r1->field_1b
    //     0xb12488: ldur            w0, [x1, #0x1b]
    // 0xb1248c: DecompressPointer r0
    //     0xb1248c: add             x0, x0, HEAP, lsl #32
    // 0xb12490: ldr             x16, [fp, #0x18]
    // 0xb12494: stp             x16, x0, [SP, #8]
    // 0xb12498: ldr             x16, [fp, #0x10]
    // 0xb1249c: str             x16, [SP]
    // 0xb124a0: r4 = 0
    //     0xb124a0: movz            x4, #0
    // 0xb124a4: ldr             x0, [SP, #0x10]
    // 0xb124a8: r16 = UnlinkedCall_0x613b5c
    //     0xb124a8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57b20] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb124ac: add             x16, x16, #0xb20
    // 0xb124b0: ldp             x5, lr, [x16]
    // 0xb124b4: blr             lr
    // 0xb124b8: r0 = Null
    //     0xb124b8: mov             x0, NULL
    // 0xb124bc: LeaveFrame
    //     0xb124bc: mov             SP, fp
    //     0xb124c0: ldp             fp, lr, [SP], #0x10
    // 0xb124c4: ret
    //     0xb124c4: ret             
    // 0xb124c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb124c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb124cc: b               #0xb12470
    // 0xb124d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb124d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4137, size: 0x2c, field offset: 0xc
//   const constructor, 
class ProductTitleItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e118, size: 0x24
    // 0xc7e118: EnterFrame
    //     0xc7e118: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e11c: mov             fp, SP
    // 0xc7e120: mov             x0, x1
    // 0xc7e124: r1 = <ProductTitleItemView>
    //     0xc7e124: add             x1, PP, #0x48, lsl #12  ; [pp+0x48af8] TypeArguments: <ProductTitleItemView>
    //     0xc7e128: ldr             x1, [x1, #0xaf8]
    // 0xc7e12c: r0 = _ProductTitleItemViewState()
    //     0xc7e12c: bl              #0xc7e13c  ; Allocate_ProductTitleItemViewStateStub -> _ProductTitleItemViewState (size=0x14)
    // 0xc7e130: LeaveFrame
    //     0xc7e130: mov             SP, fp
    //     0xc7e134: ldp             fp, lr, [SP], #0x10
    // 0xc7e138: ret
    //     0xc7e138: ret             
  }
}
