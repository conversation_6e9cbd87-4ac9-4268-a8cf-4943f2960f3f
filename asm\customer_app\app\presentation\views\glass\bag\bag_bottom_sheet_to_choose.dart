// lib: , url: package:customer_app/app/presentation/views/glass/bag/bag_bottom_sheet_to_choose.dart

// class id: 1049339, size: 0x8
class :: {
}

// class id: 3385, size: 0x18, field offset: 0x14
class _BagBottomSheetToChooseState extends State<dynamic> {

  late BagImage? image; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x93d680, size: 0xe8
    // 0x93d680: EnterFrame
    //     0x93d680: stp             fp, lr, [SP, #-0x10]!
    //     0x93d684: mov             fp, SP
    // 0x93d688: AllocStack(0x20)
    //     0x93d688: sub             SP, SP, #0x20
    // 0x93d68c: SetupParameters(_BagBottomSheetToChooseState this /* r1 => r0, fp-0x10 */)
    //     0x93d68c: mov             x0, x1
    //     0x93d690: stur            x1, [fp, #-0x10]
    // 0x93d694: CheckStackOverflow
    //     0x93d694: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93d698: cmp             SP, x16
    //     0x93d69c: b.ls            #0x93d75c
    // 0x93d6a0: LoadField: r1 = r0->field_b
    //     0x93d6a0: ldur            w1, [x0, #0xb]
    // 0x93d6a4: DecompressPointer r1
    //     0x93d6a4: add             x1, x1, HEAP, lsl #32
    // 0x93d6a8: cmp             w1, NULL
    // 0x93d6ac: b.eq            #0x93d764
    // 0x93d6b0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x93d6b0: ldur            w2, [x1, #0x17]
    // 0x93d6b4: DecompressPointer r2
    //     0x93d6b4: add             x2, x2, HEAP, lsl #32
    // 0x93d6b8: cmp             w2, NULL
    // 0x93d6bc: b.ne            #0x93d6cc
    // 0x93d6c0: mov             x1, x0
    // 0x93d6c4: r0 = Null
    //     0x93d6c4: mov             x0, NULL
    // 0x93d6c8: b               #0x93d730
    // 0x93d6cc: LoadField: r3 = r2->field_1b
    //     0x93d6cc: ldur            w3, [x2, #0x1b]
    // 0x93d6d0: DecompressPointer r3
    //     0x93d6d0: add             x3, x3, HEAP, lsl #32
    // 0x93d6d4: stur            x3, [fp, #-8]
    // 0x93d6d8: cmp             w3, NULL
    // 0x93d6dc: b.ne            #0x93d6e8
    // 0x93d6e0: r1 = Null
    //     0x93d6e0: mov             x1, NULL
    // 0x93d6e4: b               #0x93d728
    // 0x93d6e8: r1 = Function '<anonymous closure>':.
    //     0x93d6e8: add             x1, PP, #0x57, lsl #12  ; [pp+0x573c0] AnonymousClosure: (0x901144), in [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemsList (0x901fb4)
    //     0x93d6ec: ldr             x1, [x1, #0x3c0]
    // 0x93d6f0: r2 = Null
    //     0x93d6f0: mov             x2, NULL
    // 0x93d6f4: r0 = AllocateClosure()
    //     0x93d6f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93d6f8: r1 = Function '<anonymous closure>':.
    //     0x93d6f8: add             x1, PP, #0x57, lsl #12  ; [pp+0x573c8] AnonymousClosure: (0x8fb1b8), in [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildProductItem (0x8fb1d0)
    //     0x93d6fc: ldr             x1, [x1, #0x3c8]
    // 0x93d700: r2 = Null
    //     0x93d700: mov             x2, NULL
    // 0x93d704: stur            x0, [fp, #-0x18]
    // 0x93d708: r0 = AllocateClosure()
    //     0x93d708: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93d70c: str             x0, [SP]
    // 0x93d710: ldur            x1, [fp, #-8]
    // 0x93d714: ldur            x2, [fp, #-0x18]
    // 0x93d718: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x93d718: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x93d71c: ldr             x4, [x4, #0xb48]
    // 0x93d720: r0 = firstWhere()
    //     0x93d720: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0x93d724: mov             x1, x0
    // 0x93d728: mov             x0, x1
    // 0x93d72c: ldur            x1, [fp, #-0x10]
    // 0x93d730: StoreField: r1->field_13 = r0
    //     0x93d730: stur            w0, [x1, #0x13]
    //     0x93d734: ldurb           w16, [x1, #-1]
    //     0x93d738: ldurb           w17, [x0, #-1]
    //     0x93d73c: and             x16, x17, x16, lsr #2
    //     0x93d740: tst             x16, HEAP, lsr #32
    //     0x93d744: b.eq            #0x93d74c
    //     0x93d748: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x93d74c: r0 = Null
    //     0x93d74c: mov             x0, NULL
    // 0x93d750: LeaveFrame
    //     0x93d750: mov             SP, fp
    //     0x93d754: ldp             fp, lr, [SP], #0x10
    // 0x93d758: ret
    //     0x93d758: ret             
    // 0x93d75c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93d75c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93d760: b               #0x93d6a0
    // 0x93d764: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93d764: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb295c0, size: 0xde8
    // 0xb295c0: EnterFrame
    //     0xb295c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb295c4: mov             fp, SP
    // 0xb295c8: AllocStack(0x70)
    //     0xb295c8: sub             SP, SP, #0x70
    // 0xb295cc: SetupParameters(_BagBottomSheetToChooseState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb295cc: mov             x0, x1
    //     0xb295d0: stur            x1, [fp, #-8]
    //     0xb295d4: mov             x1, x2
    //     0xb295d8: stur            x2, [fp, #-0x10]
    // 0xb295dc: CheckStackOverflow
    //     0xb295dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb295e0: cmp             SP, x16
    //     0xb295e4: b.ls            #0xb2a380
    // 0xb295e8: r1 = 1
    //     0xb295e8: movz            x1, #0x1
    // 0xb295ec: r0 = AllocateContext()
    //     0xb295ec: bl              #0x16f6108  ; AllocateContextStub
    // 0xb295f0: mov             x2, x0
    // 0xb295f4: ldur            x0, [fp, #-8]
    // 0xb295f8: stur            x2, [fp, #-0x18]
    // 0xb295fc: StoreField: r2->field_f = r0
    //     0xb295fc: stur            w0, [x2, #0xf]
    // 0xb29600: ldur            x1, [fp, #-0x10]
    // 0xb29604: r0 = of()
    //     0xb29604: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb29608: LoadField: r1 = r0->field_87
    //     0xb29608: ldur            w1, [x0, #0x87]
    // 0xb2960c: DecompressPointer r1
    //     0xb2960c: add             x1, x1, HEAP, lsl #32
    // 0xb29610: LoadField: r0 = r1->field_7
    //     0xb29610: ldur            w0, [x1, #7]
    // 0xb29614: DecompressPointer r0
    //     0xb29614: add             x0, x0, HEAP, lsl #32
    // 0xb29618: r16 = 15.000000
    //     0xb29618: add             x16, PP, #0x41, lsl #12  ; [pp+0x41480] 15
    //     0xb2961c: ldr             x16, [x16, #0x480]
    // 0xb29620: r30 = Instance_Color
    //     0xb29620: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb29624: stp             lr, x16, [SP]
    // 0xb29628: mov             x1, x0
    // 0xb2962c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2962c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb29630: ldr             x4, [x4, #0xaa0]
    // 0xb29634: r0 = copyWith()
    //     0xb29634: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb29638: stur            x0, [fp, #-0x20]
    // 0xb2963c: r0 = Text()
    //     0xb2963c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb29640: mov             x2, x0
    // 0xb29644: r0 = "Would you like to personalise your product\?"
    //     0xb29644: add             x0, PP, #0x54, lsl #12  ; [pp+0x54d18] "Would you like to personalise your product\?"
    //     0xb29648: ldr             x0, [x0, #0xd18]
    // 0xb2964c: stur            x2, [fp, #-0x28]
    // 0xb29650: StoreField: r2->field_b = r0
    //     0xb29650: stur            w0, [x2, #0xb]
    // 0xb29654: ldur            x0, [fp, #-0x20]
    // 0xb29658: StoreField: r2->field_13 = r0
    //     0xb29658: stur            w0, [x2, #0x13]
    // 0xb2965c: r1 = <FlexParentData>
    //     0xb2965c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb29660: ldr             x1, [x1, #0xe00]
    // 0xb29664: r0 = Expanded()
    //     0xb29664: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb29668: mov             x1, x0
    // 0xb2966c: r0 = 1
    //     0xb2966c: movz            x0, #0x1
    // 0xb29670: stur            x1, [fp, #-0x20]
    // 0xb29674: StoreField: r1->field_13 = r0
    //     0xb29674: stur            x0, [x1, #0x13]
    // 0xb29678: r2 = Instance_FlexFit
    //     0xb29678: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb2967c: ldr             x2, [x2, #0xe08]
    // 0xb29680: StoreField: r1->field_1b = r2
    //     0xb29680: stur            w2, [x1, #0x1b]
    // 0xb29684: ldur            x3, [fp, #-0x28]
    // 0xb29688: StoreField: r1->field_b = r3
    //     0xb29688: stur            w3, [x1, #0xb]
    // 0xb2968c: r0 = InkWell()
    //     0xb2968c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb29690: mov             x3, x0
    // 0xb29694: r0 = Instance_Icon
    //     0xb29694: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xb29698: ldr             x0, [x0, #0x2b8]
    // 0xb2969c: stur            x3, [fp, #-0x28]
    // 0xb296a0: StoreField: r3->field_b = r0
    //     0xb296a0: stur            w0, [x3, #0xb]
    // 0xb296a4: r1 = Function '<anonymous closure>':.
    //     0xb296a4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57360] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb296a8: ldr             x1, [x1, #0x360]
    // 0xb296ac: r2 = Null
    //     0xb296ac: mov             x2, NULL
    // 0xb296b0: r0 = AllocateClosure()
    //     0xb296b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb296b4: mov             x1, x0
    // 0xb296b8: ldur            x0, [fp, #-0x28]
    // 0xb296bc: StoreField: r0->field_f = r1
    //     0xb296bc: stur            w1, [x0, #0xf]
    // 0xb296c0: r3 = true
    //     0xb296c0: add             x3, NULL, #0x20  ; true
    // 0xb296c4: StoreField: r0->field_43 = r3
    //     0xb296c4: stur            w3, [x0, #0x43]
    // 0xb296c8: r1 = Instance_BoxShape
    //     0xb296c8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb296cc: ldr             x1, [x1, #0x80]
    // 0xb296d0: StoreField: r0->field_47 = r1
    //     0xb296d0: stur            w1, [x0, #0x47]
    // 0xb296d4: StoreField: r0->field_6f = r3
    //     0xb296d4: stur            w3, [x0, #0x6f]
    // 0xb296d8: r4 = false
    //     0xb296d8: add             x4, NULL, #0x30  ; false
    // 0xb296dc: StoreField: r0->field_73 = r4
    //     0xb296dc: stur            w4, [x0, #0x73]
    // 0xb296e0: StoreField: r0->field_83 = r3
    //     0xb296e0: stur            w3, [x0, #0x83]
    // 0xb296e4: StoreField: r0->field_7b = r4
    //     0xb296e4: stur            w4, [x0, #0x7b]
    // 0xb296e8: r1 = Null
    //     0xb296e8: mov             x1, NULL
    // 0xb296ec: r2 = 6
    //     0xb296ec: movz            x2, #0x6
    // 0xb296f0: r0 = AllocateArray()
    //     0xb296f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb296f4: mov             x2, x0
    // 0xb296f8: ldur            x0, [fp, #-0x20]
    // 0xb296fc: stur            x2, [fp, #-0x30]
    // 0xb29700: StoreField: r2->field_f = r0
    //     0xb29700: stur            w0, [x2, #0xf]
    // 0xb29704: r16 = Instance_SizedBox
    //     0xb29704: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb29708: ldr             x16, [x16, #0xb20]
    // 0xb2970c: StoreField: r2->field_13 = r16
    //     0xb2970c: stur            w16, [x2, #0x13]
    // 0xb29710: ldur            x0, [fp, #-0x28]
    // 0xb29714: ArrayStore: r2[0] = r0  ; List_4
    //     0xb29714: stur            w0, [x2, #0x17]
    // 0xb29718: r1 = <Widget>
    //     0xb29718: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2971c: r0 = AllocateGrowableArray()
    //     0xb2971c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb29720: mov             x1, x0
    // 0xb29724: ldur            x0, [fp, #-0x30]
    // 0xb29728: stur            x1, [fp, #-0x20]
    // 0xb2972c: StoreField: r1->field_f = r0
    //     0xb2972c: stur            w0, [x1, #0xf]
    // 0xb29730: r2 = 6
    //     0xb29730: movz            x2, #0x6
    // 0xb29734: StoreField: r1->field_b = r2
    //     0xb29734: stur            w2, [x1, #0xb]
    // 0xb29738: r0 = Row()
    //     0xb29738: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2973c: mov             x1, x0
    // 0xb29740: r0 = Instance_Axis
    //     0xb29740: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb29744: stur            x1, [fp, #-0x28]
    // 0xb29748: StoreField: r1->field_f = r0
    //     0xb29748: stur            w0, [x1, #0xf]
    // 0xb2974c: r2 = Instance_MainAxisAlignment
    //     0xb2974c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb29750: ldr             x2, [x2, #0xa08]
    // 0xb29754: StoreField: r1->field_13 = r2
    //     0xb29754: stur            w2, [x1, #0x13]
    // 0xb29758: r3 = Instance_MainAxisSize
    //     0xb29758: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2975c: ldr             x3, [x3, #0xa10]
    // 0xb29760: ArrayStore: r1[0] = r3  ; List_4
    //     0xb29760: stur            w3, [x1, #0x17]
    // 0xb29764: r4 = Instance_CrossAxisAlignment
    //     0xb29764: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb29768: ldr             x4, [x4, #0xa18]
    // 0xb2976c: StoreField: r1->field_1b = r4
    //     0xb2976c: stur            w4, [x1, #0x1b]
    // 0xb29770: r5 = Instance_VerticalDirection
    //     0xb29770: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb29774: ldr             x5, [x5, #0xa20]
    // 0xb29778: StoreField: r1->field_23 = r5
    //     0xb29778: stur            w5, [x1, #0x23]
    // 0xb2977c: r6 = Instance_Clip
    //     0xb2977c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb29780: ldr             x6, [x6, #0x38]
    // 0xb29784: StoreField: r1->field_2b = r6
    //     0xb29784: stur            w6, [x1, #0x2b]
    // 0xb29788: StoreField: r1->field_2f = rZR
    //     0xb29788: stur            xzr, [x1, #0x2f]
    // 0xb2978c: ldur            x7, [fp, #-0x20]
    // 0xb29790: StoreField: r1->field_b = r7
    //     0xb29790: stur            w7, [x1, #0xb]
    // 0xb29794: r0 = Radius()
    //     0xb29794: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb29798: d0 = 15.000000
    //     0xb29798: fmov            d0, #15.00000000
    // 0xb2979c: stur            x0, [fp, #-0x20]
    // 0xb297a0: StoreField: r0->field_7 = d0
    //     0xb297a0: stur            d0, [x0, #7]
    // 0xb297a4: StoreField: r0->field_f = d0
    //     0xb297a4: stur            d0, [x0, #0xf]
    // 0xb297a8: r0 = BorderRadius()
    //     0xb297a8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb297ac: mov             x3, x0
    // 0xb297b0: ldur            x0, [fp, #-0x20]
    // 0xb297b4: stur            x3, [fp, #-0x30]
    // 0xb297b8: StoreField: r3->field_7 = r0
    //     0xb297b8: stur            w0, [x3, #7]
    // 0xb297bc: StoreField: r3->field_b = r0
    //     0xb297bc: stur            w0, [x3, #0xb]
    // 0xb297c0: StoreField: r3->field_f = r0
    //     0xb297c0: stur            w0, [x3, #0xf]
    // 0xb297c4: StoreField: r3->field_13 = r0
    //     0xb297c4: stur            w0, [x3, #0x13]
    // 0xb297c8: ldur            x0, [fp, #-8]
    // 0xb297cc: LoadField: r1 = r0->field_13
    //     0xb297cc: ldur            w1, [x0, #0x13]
    // 0xb297d0: DecompressPointer r1
    //     0xb297d0: add             x1, x1, HEAP, lsl #32
    // 0xb297d4: r16 = Sentinel
    //     0xb297d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb297d8: cmp             w1, w16
    // 0xb297dc: b.eq            #0xb2a388
    // 0xb297e0: cmp             w1, NULL
    // 0xb297e4: b.ne            #0xb297f0
    // 0xb297e8: r1 = Null
    //     0xb297e8: mov             x1, NULL
    // 0xb297ec: b               #0xb297fc
    // 0xb297f0: LoadField: r2 = r1->field_b
    //     0xb297f0: ldur            w2, [x1, #0xb]
    // 0xb297f4: DecompressPointer r2
    //     0xb297f4: add             x2, x2, HEAP, lsl #32
    // 0xb297f8: mov             x1, x2
    // 0xb297fc: cmp             w1, NULL
    // 0xb29800: b.ne            #0xb2980c
    // 0xb29804: r4 = ""
    //     0xb29804: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb29808: b               #0xb29810
    // 0xb2980c: mov             x4, x1
    // 0xb29810: stur            x4, [fp, #-0x20]
    // 0xb29814: r1 = Function '<anonymous closure>':.
    //     0xb29814: add             x1, PP, #0x57, lsl #12  ; [pp+0x57368] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb29818: ldr             x1, [x1, #0x368]
    // 0xb2981c: r2 = Null
    //     0xb2981c: mov             x2, NULL
    // 0xb29820: r0 = AllocateClosure()
    //     0xb29820: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb29824: r1 = Function '<anonymous closure>':.
    //     0xb29824: add             x1, PP, #0x57, lsl #12  ; [pp+0x57370] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb29828: ldr             x1, [x1, #0x370]
    // 0xb2982c: r2 = Null
    //     0xb2982c: mov             x2, NULL
    // 0xb29830: stur            x0, [fp, #-0x38]
    // 0xb29834: r0 = AllocateClosure()
    //     0xb29834: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb29838: stur            x0, [fp, #-0x40]
    // 0xb2983c: r0 = CachedNetworkImage()
    //     0xb2983c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb29840: stur            x0, [fp, #-0x48]
    // 0xb29844: r16 = 56.000000
    //     0xb29844: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb29848: ldr             x16, [x16, #0xb78]
    // 0xb2984c: r30 = 56.000000
    //     0xb2984c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb29850: ldr             lr, [lr, #0xb78]
    // 0xb29854: stp             lr, x16, [SP, #0x18]
    // 0xb29858: r16 = Instance_BoxFit
    //     0xb29858: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb2985c: ldr             x16, [x16, #0x118]
    // 0xb29860: ldur            lr, [fp, #-0x38]
    // 0xb29864: stp             lr, x16, [SP, #8]
    // 0xb29868: ldur            x16, [fp, #-0x40]
    // 0xb2986c: str             x16, [SP]
    // 0xb29870: mov             x1, x0
    // 0xb29874: ldur            x2, [fp, #-0x20]
    // 0xb29878: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xb29878: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xb2987c: ldr             x4, [x4, #0xc28]
    // 0xb29880: r0 = CachedNetworkImage()
    //     0xb29880: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb29884: r0 = ClipRRect()
    //     0xb29884: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb29888: mov             x2, x0
    // 0xb2988c: ldur            x0, [fp, #-0x30]
    // 0xb29890: stur            x2, [fp, #-0x38]
    // 0xb29894: StoreField: r2->field_f = r0
    //     0xb29894: stur            w0, [x2, #0xf]
    // 0xb29898: r0 = Instance_Clip
    //     0xb29898: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb2989c: ldr             x0, [x0, #0x138]
    // 0xb298a0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb298a0: stur            w0, [x2, #0x17]
    // 0xb298a4: ldur            x0, [fp, #-0x48]
    // 0xb298a8: StoreField: r2->field_b = r0
    //     0xb298a8: stur            w0, [x2, #0xb]
    // 0xb298ac: ldur            x0, [fp, #-8]
    // 0xb298b0: LoadField: r1 = r0->field_b
    //     0xb298b0: ldur            w1, [x0, #0xb]
    // 0xb298b4: DecompressPointer r1
    //     0xb298b4: add             x1, x1, HEAP, lsl #32
    // 0xb298b8: cmp             w1, NULL
    // 0xb298bc: b.eq            #0xb2a394
    // 0xb298c0: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb298c0: ldur            w3, [x1, #0x17]
    // 0xb298c4: DecompressPointer r3
    //     0xb298c4: add             x3, x3, HEAP, lsl #32
    // 0xb298c8: cmp             w3, NULL
    // 0xb298cc: b.ne            #0xb298d8
    // 0xb298d0: r1 = Null
    //     0xb298d0: mov             x1, NULL
    // 0xb298d4: b               #0xb298fc
    // 0xb298d8: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb298d8: ldur            w1, [x3, #0x17]
    // 0xb298dc: DecompressPointer r1
    //     0xb298dc: add             x1, x1, HEAP, lsl #32
    // 0xb298e0: cmp             w1, NULL
    // 0xb298e4: b.ne            #0xb298f0
    // 0xb298e8: r1 = Null
    //     0xb298e8: mov             x1, NULL
    // 0xb298ec: b               #0xb298fc
    // 0xb298f0: LoadField: r3 = r1->field_7
    //     0xb298f0: ldur            w3, [x1, #7]
    // 0xb298f4: DecompressPointer r3
    //     0xb298f4: add             x3, x3, HEAP, lsl #32
    // 0xb298f8: mov             x1, x3
    // 0xb298fc: cmp             w1, NULL
    // 0xb29900: b.ne            #0xb2990c
    // 0xb29904: r3 = ""
    //     0xb29904: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb29908: b               #0xb29910
    // 0xb2990c: mov             x3, x1
    // 0xb29910: ldur            x1, [fp, #-0x10]
    // 0xb29914: stur            x3, [fp, #-0x20]
    // 0xb29918: r0 = of()
    //     0xb29918: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2991c: LoadField: r1 = r0->field_87
    //     0xb2991c: ldur            w1, [x0, #0x87]
    // 0xb29920: DecompressPointer r1
    //     0xb29920: add             x1, x1, HEAP, lsl #32
    // 0xb29924: LoadField: r0 = r1->field_2b
    //     0xb29924: ldur            w0, [x1, #0x2b]
    // 0xb29928: DecompressPointer r0
    //     0xb29928: add             x0, x0, HEAP, lsl #32
    // 0xb2992c: stur            x0, [fp, #-0x30]
    // 0xb29930: r1 = Instance_Color
    //     0xb29930: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb29934: d0 = 0.700000
    //     0xb29934: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb29938: ldr             d0, [x17, #0xf48]
    // 0xb2993c: r0 = withOpacity()
    //     0xb2993c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb29940: r16 = 12.000000
    //     0xb29940: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb29944: ldr             x16, [x16, #0x9e8]
    // 0xb29948: stp             x0, x16, [SP]
    // 0xb2994c: ldur            x1, [fp, #-0x30]
    // 0xb29950: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb29950: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb29954: ldr             x4, [x4, #0xaa0]
    // 0xb29958: r0 = copyWith()
    //     0xb29958: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2995c: stur            x0, [fp, #-0x30]
    // 0xb29960: r0 = Text()
    //     0xb29960: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb29964: mov             x1, x0
    // 0xb29968: ldur            x0, [fp, #-0x20]
    // 0xb2996c: stur            x1, [fp, #-0x40]
    // 0xb29970: StoreField: r1->field_b = r0
    //     0xb29970: stur            w0, [x1, #0xb]
    // 0xb29974: ldur            x0, [fp, #-0x30]
    // 0xb29978: StoreField: r1->field_13 = r0
    //     0xb29978: stur            w0, [x1, #0x13]
    // 0xb2997c: r2 = 2
    //     0xb2997c: movz            x2, #0x2
    // 0xb29980: StoreField: r1->field_37 = r2
    //     0xb29980: stur            w2, [x1, #0x37]
    // 0xb29984: r0 = SizedBox()
    //     0xb29984: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb29988: mov             x1, x0
    // 0xb2998c: r0 = 200.000000
    //     0xb2998c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48570] 200
    //     0xb29990: ldr             x0, [x0, #0x570]
    // 0xb29994: stur            x1, [fp, #-0x20]
    // 0xb29998: StoreField: r1->field_f = r0
    //     0xb29998: stur            w0, [x1, #0xf]
    // 0xb2999c: ldur            x0, [fp, #-0x40]
    // 0xb299a0: StoreField: r1->field_b = r0
    //     0xb299a0: stur            w0, [x1, #0xb]
    // 0xb299a4: r0 = Padding()
    //     0xb299a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb299a8: mov             x2, x0
    // 0xb299ac: r1 = Instance_EdgeInsets
    //     0xb299ac: add             x1, PP, #0x54, lsl #12  ; [pp+0x54d38] Obj!EdgeInsets@d57a11
    //     0xb299b0: ldr             x1, [x1, #0xd38]
    // 0xb299b4: stur            x2, [fp, #-0x30]
    // 0xb299b8: StoreField: r2->field_f = r1
    //     0xb299b8: stur            w1, [x2, #0xf]
    // 0xb299bc: ldur            x0, [fp, #-0x20]
    // 0xb299c0: StoreField: r2->field_b = r0
    //     0xb299c0: stur            w0, [x2, #0xb]
    // 0xb299c4: ldur            x3, [fp, #-8]
    // 0xb299c8: LoadField: r0 = r3->field_b
    //     0xb299c8: ldur            w0, [x3, #0xb]
    // 0xb299cc: DecompressPointer r0
    //     0xb299cc: add             x0, x0, HEAP, lsl #32
    // 0xb299d0: cmp             w0, NULL
    // 0xb299d4: b.eq            #0xb2a398
    // 0xb299d8: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xb299d8: ldur            w4, [x0, #0x17]
    // 0xb299dc: DecompressPointer r4
    //     0xb299dc: add             x4, x4, HEAP, lsl #32
    // 0xb299e0: cmp             w4, NULL
    // 0xb299e4: b.ne            #0xb299f0
    // 0xb299e8: r0 = Null
    //     0xb299e8: mov             x0, NULL
    // 0xb299ec: b               #0xb299f8
    // 0xb299f0: LoadField: r0 = r4->field_87
    //     0xb299f0: ldur            w0, [x4, #0x87]
    // 0xb299f4: DecompressPointer r0
    //     0xb299f4: add             x0, x0, HEAP, lsl #32
    // 0xb299f8: r4 = LoadClassIdInstr(r0)
    //     0xb299f8: ldur            x4, [x0, #-1]
    //     0xb299fc: ubfx            x4, x4, #0xc, #0x14
    // 0xb29a00: r16 = "size"
    //     0xb29a00: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xb29a04: ldr             x16, [x16, #0x9c0]
    // 0xb29a08: stp             x16, x0, [SP]
    // 0xb29a0c: mov             x0, x4
    // 0xb29a10: mov             lr, x0
    // 0xb29a14: ldr             lr, [x21, lr, lsl #3]
    // 0xb29a18: blr             lr
    // 0xb29a1c: tbnz            w0, #4, #0xb29a84
    // 0xb29a20: ldur            x0, [fp, #-8]
    // 0xb29a24: r1 = Null
    //     0xb29a24: mov             x1, NULL
    // 0xb29a28: r2 = 4
    //     0xb29a28: movz            x2, #0x4
    // 0xb29a2c: r0 = AllocateArray()
    //     0xb29a2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb29a30: r16 = "Size: "
    //     0xb29a30: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xb29a34: ldr             x16, [x16, #0xf00]
    // 0xb29a38: StoreField: r0->field_f = r16
    //     0xb29a38: stur            w16, [x0, #0xf]
    // 0xb29a3c: ldur            x1, [fp, #-8]
    // 0xb29a40: LoadField: r2 = r1->field_b
    //     0xb29a40: ldur            w2, [x1, #0xb]
    // 0xb29a44: DecompressPointer r2
    //     0xb29a44: add             x2, x2, HEAP, lsl #32
    // 0xb29a48: cmp             w2, NULL
    // 0xb29a4c: b.eq            #0xb2a39c
    // 0xb29a50: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb29a50: ldur            w3, [x2, #0x17]
    // 0xb29a54: DecompressPointer r3
    //     0xb29a54: add             x3, x3, HEAP, lsl #32
    // 0xb29a58: cmp             w3, NULL
    // 0xb29a5c: b.ne            #0xb29a68
    // 0xb29a60: r2 = Null
    //     0xb29a60: mov             x2, NULL
    // 0xb29a64: b               #0xb29a70
    // 0xb29a68: LoadField: r2 = r3->field_53
    //     0xb29a68: ldur            w2, [x3, #0x53]
    // 0xb29a6c: DecompressPointer r2
    //     0xb29a6c: add             x2, x2, HEAP, lsl #32
    // 0xb29a70: StoreField: r0->field_13 = r2
    //     0xb29a70: stur            w2, [x0, #0x13]
    // 0xb29a74: str             x0, [SP]
    // 0xb29a78: r0 = _interpolate()
    //     0xb29a78: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb29a7c: mov             x2, x0
    // 0xb29a80: b               #0xb29ae4
    // 0xb29a84: ldur            x0, [fp, #-8]
    // 0xb29a88: r1 = Null
    //     0xb29a88: mov             x1, NULL
    // 0xb29a8c: r2 = 4
    //     0xb29a8c: movz            x2, #0x4
    // 0xb29a90: r0 = AllocateArray()
    //     0xb29a90: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb29a94: r16 = "Variant: "
    //     0xb29a94: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0xb29a98: ldr             x16, [x16, #0xf08]
    // 0xb29a9c: StoreField: r0->field_f = r16
    //     0xb29a9c: stur            w16, [x0, #0xf]
    // 0xb29aa0: ldur            x1, [fp, #-8]
    // 0xb29aa4: LoadField: r2 = r1->field_b
    //     0xb29aa4: ldur            w2, [x1, #0xb]
    // 0xb29aa8: DecompressPointer r2
    //     0xb29aa8: add             x2, x2, HEAP, lsl #32
    // 0xb29aac: cmp             w2, NULL
    // 0xb29ab0: b.eq            #0xb2a3a0
    // 0xb29ab4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb29ab4: ldur            w3, [x2, #0x17]
    // 0xb29ab8: DecompressPointer r3
    //     0xb29ab8: add             x3, x3, HEAP, lsl #32
    // 0xb29abc: cmp             w3, NULL
    // 0xb29ac0: b.ne            #0xb29acc
    // 0xb29ac4: r2 = Null
    //     0xb29ac4: mov             x2, NULL
    // 0xb29ac8: b               #0xb29ad4
    // 0xb29acc: LoadField: r2 = r3->field_53
    //     0xb29acc: ldur            w2, [x3, #0x53]
    // 0xb29ad0: DecompressPointer r2
    //     0xb29ad0: add             x2, x2, HEAP, lsl #32
    // 0xb29ad4: StoreField: r0->field_13 = r2
    //     0xb29ad4: stur            w2, [x0, #0x13]
    // 0xb29ad8: str             x0, [SP]
    // 0xb29adc: r0 = _interpolate()
    //     0xb29adc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb29ae0: mov             x2, x0
    // 0xb29ae4: ldur            x0, [fp, #-8]
    // 0xb29ae8: ldur            x1, [fp, #-0x10]
    // 0xb29aec: stur            x2, [fp, #-0x20]
    // 0xb29af0: r0 = of()
    //     0xb29af0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb29af4: LoadField: r1 = r0->field_87
    //     0xb29af4: ldur            w1, [x0, #0x87]
    // 0xb29af8: DecompressPointer r1
    //     0xb29af8: add             x1, x1, HEAP, lsl #32
    // 0xb29afc: LoadField: r0 = r1->field_7
    //     0xb29afc: ldur            w0, [x1, #7]
    // 0xb29b00: DecompressPointer r0
    //     0xb29b00: add             x0, x0, HEAP, lsl #32
    // 0xb29b04: stur            x0, [fp, #-0x40]
    // 0xb29b08: r1 = Instance_Color
    //     0xb29b08: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb29b0c: d0 = 0.700000
    //     0xb29b0c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb29b10: ldr             d0, [x17, #0xf48]
    // 0xb29b14: r0 = withOpacity()
    //     0xb29b14: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb29b18: r16 = 12.000000
    //     0xb29b18: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb29b1c: ldr             x16, [x16, #0x9e8]
    // 0xb29b20: stp             x0, x16, [SP]
    // 0xb29b24: ldur            x1, [fp, #-0x40]
    // 0xb29b28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb29b28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb29b2c: ldr             x4, [x4, #0xaa0]
    // 0xb29b30: r0 = copyWith()
    //     0xb29b30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb29b34: stur            x0, [fp, #-0x40]
    // 0xb29b38: r0 = Text()
    //     0xb29b38: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb29b3c: mov             x1, x0
    // 0xb29b40: ldur            x0, [fp, #-0x20]
    // 0xb29b44: stur            x1, [fp, #-0x48]
    // 0xb29b48: StoreField: r1->field_b = r0
    //     0xb29b48: stur            w0, [x1, #0xb]
    // 0xb29b4c: ldur            x0, [fp, #-0x40]
    // 0xb29b50: StoreField: r1->field_13 = r0
    //     0xb29b50: stur            w0, [x1, #0x13]
    // 0xb29b54: r2 = 4
    //     0xb29b54: movz            x2, #0x4
    // 0xb29b58: StoreField: r1->field_37 = r2
    //     0xb29b58: stur            w2, [x1, #0x37]
    // 0xb29b5c: r0 = Padding()
    //     0xb29b5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb29b60: mov             x1, x0
    // 0xb29b64: r0 = Instance_EdgeInsets
    //     0xb29b64: add             x0, PP, #0x54, lsl #12  ; [pp+0x54d38] Obj!EdgeInsets@d57a11
    //     0xb29b68: ldr             x0, [x0, #0xd38]
    // 0xb29b6c: stur            x1, [fp, #-0x20]
    // 0xb29b70: StoreField: r1->field_f = r0
    //     0xb29b70: stur            w0, [x1, #0xf]
    // 0xb29b74: ldur            x2, [fp, #-0x48]
    // 0xb29b78: StoreField: r1->field_b = r2
    //     0xb29b78: stur            w2, [x1, #0xb]
    // 0xb29b7c: ldur            x2, [fp, #-8]
    // 0xb29b80: LoadField: r3 = r2->field_b
    //     0xb29b80: ldur            w3, [x2, #0xb]
    // 0xb29b84: DecompressPointer r3
    //     0xb29b84: add             x3, x3, HEAP, lsl #32
    // 0xb29b88: cmp             w3, NULL
    // 0xb29b8c: b.eq            #0xb2a3a4
    // 0xb29b90: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xb29b90: ldur            w2, [x3, #0x17]
    // 0xb29b94: DecompressPointer r2
    //     0xb29b94: add             x2, x2, HEAP, lsl #32
    // 0xb29b98: cmp             w2, NULL
    // 0xb29b9c: b.ne            #0xb29ba8
    // 0xb29ba0: r5 = Null
    //     0xb29ba0: mov             x5, NULL
    // 0xb29ba4: b               #0xb29bb4
    // 0xb29ba8: LoadField: r3 = r2->field_3f
    //     0xb29ba8: ldur            w3, [x2, #0x3f]
    // 0xb29bac: DecompressPointer r3
    //     0xb29bac: add             x3, x3, HEAP, lsl #32
    // 0xb29bb0: mov             x5, x3
    // 0xb29bb4: ldur            x4, [fp, #-0x28]
    // 0xb29bb8: ldur            x3, [fp, #-0x38]
    // 0xb29bbc: ldur            x2, [fp, #-0x30]
    // 0xb29bc0: str             x5, [SP]
    // 0xb29bc4: r0 = _interpolateSingle()
    //     0xb29bc4: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb29bc8: ldur            x1, [fp, #-0x10]
    // 0xb29bcc: stur            x0, [fp, #-8]
    // 0xb29bd0: r0 = of()
    //     0xb29bd0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb29bd4: LoadField: r1 = r0->field_87
    //     0xb29bd4: ldur            w1, [x0, #0x87]
    // 0xb29bd8: DecompressPointer r1
    //     0xb29bd8: add             x1, x1, HEAP, lsl #32
    // 0xb29bdc: LoadField: r0 = r1->field_2b
    //     0xb29bdc: ldur            w0, [x1, #0x2b]
    // 0xb29be0: DecompressPointer r0
    //     0xb29be0: add             x0, x0, HEAP, lsl #32
    // 0xb29be4: r16 = 12.000000
    //     0xb29be4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb29be8: ldr             x16, [x16, #0x9e8]
    // 0xb29bec: r30 = Instance_Color
    //     0xb29bec: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb29bf0: stp             lr, x16, [SP]
    // 0xb29bf4: mov             x1, x0
    // 0xb29bf8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb29bf8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb29bfc: ldr             x4, [x4, #0xaa0]
    // 0xb29c00: r0 = copyWith()
    //     0xb29c00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb29c04: stur            x0, [fp, #-0x40]
    // 0xb29c08: r0 = TextSpan()
    //     0xb29c08: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb29c0c: mov             x3, x0
    // 0xb29c10: ldur            x0, [fp, #-8]
    // 0xb29c14: stur            x3, [fp, #-0x48]
    // 0xb29c18: StoreField: r3->field_b = r0
    //     0xb29c18: stur            w0, [x3, #0xb]
    // 0xb29c1c: r0 = Instance__DeferringMouseCursor
    //     0xb29c1c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb29c20: ArrayStore: r3[0] = r0  ; List_4
    //     0xb29c20: stur            w0, [x3, #0x17]
    // 0xb29c24: ldur            x1, [fp, #-0x40]
    // 0xb29c28: StoreField: r3->field_7 = r1
    //     0xb29c28: stur            w1, [x3, #7]
    // 0xb29c2c: r1 = Null
    //     0xb29c2c: mov             x1, NULL
    // 0xb29c30: r2 = 2
    //     0xb29c30: movz            x2, #0x2
    // 0xb29c34: r0 = AllocateArray()
    //     0xb29c34: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb29c38: mov             x2, x0
    // 0xb29c3c: ldur            x0, [fp, #-0x48]
    // 0xb29c40: stur            x2, [fp, #-8]
    // 0xb29c44: StoreField: r2->field_f = r0
    //     0xb29c44: stur            w0, [x2, #0xf]
    // 0xb29c48: r1 = <TextSpan>
    //     0xb29c48: add             x1, PP, #0x48, lsl #12  ; [pp+0x48940] TypeArguments: <TextSpan>
    //     0xb29c4c: ldr             x1, [x1, #0x940]
    // 0xb29c50: r0 = AllocateGrowableArray()
    //     0xb29c50: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb29c54: mov             x1, x0
    // 0xb29c58: ldur            x0, [fp, #-8]
    // 0xb29c5c: stur            x1, [fp, #-0x40]
    // 0xb29c60: StoreField: r1->field_f = r0
    //     0xb29c60: stur            w0, [x1, #0xf]
    // 0xb29c64: r0 = 2
    //     0xb29c64: movz            x0, #0x2
    // 0xb29c68: StoreField: r1->field_b = r0
    //     0xb29c68: stur            w0, [x1, #0xb]
    // 0xb29c6c: r0 = TextSpan()
    //     0xb29c6c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb29c70: mov             x1, x0
    // 0xb29c74: ldur            x0, [fp, #-0x40]
    // 0xb29c78: stur            x1, [fp, #-8]
    // 0xb29c7c: StoreField: r1->field_f = r0
    //     0xb29c7c: stur            w0, [x1, #0xf]
    // 0xb29c80: r0 = Instance__DeferringMouseCursor
    //     0xb29c80: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb29c84: ArrayStore: r1[0] = r0  ; List_4
    //     0xb29c84: stur            w0, [x1, #0x17]
    // 0xb29c88: r0 = RichText()
    //     0xb29c88: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb29c8c: mov             x1, x0
    // 0xb29c90: ldur            x2, [fp, #-8]
    // 0xb29c94: stur            x0, [fp, #-8]
    // 0xb29c98: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb29c98: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb29c9c: r0 = RichText()
    //     0xb29c9c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb29ca0: r0 = Padding()
    //     0xb29ca0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb29ca4: mov             x3, x0
    // 0xb29ca8: r0 = Instance_EdgeInsets
    //     0xb29ca8: add             x0, PP, #0x54, lsl #12  ; [pp+0x54d38] Obj!EdgeInsets@d57a11
    //     0xb29cac: ldr             x0, [x0, #0xd38]
    // 0xb29cb0: stur            x3, [fp, #-0x40]
    // 0xb29cb4: StoreField: r3->field_f = r0
    //     0xb29cb4: stur            w0, [x3, #0xf]
    // 0xb29cb8: ldur            x0, [fp, #-8]
    // 0xb29cbc: StoreField: r3->field_b = r0
    //     0xb29cbc: stur            w0, [x3, #0xb]
    // 0xb29cc0: r1 = Null
    //     0xb29cc0: mov             x1, NULL
    // 0xb29cc4: r2 = 6
    //     0xb29cc4: movz            x2, #0x6
    // 0xb29cc8: r0 = AllocateArray()
    //     0xb29cc8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb29ccc: mov             x2, x0
    // 0xb29cd0: ldur            x0, [fp, #-0x30]
    // 0xb29cd4: stur            x2, [fp, #-8]
    // 0xb29cd8: StoreField: r2->field_f = r0
    //     0xb29cd8: stur            w0, [x2, #0xf]
    // 0xb29cdc: ldur            x0, [fp, #-0x20]
    // 0xb29ce0: StoreField: r2->field_13 = r0
    //     0xb29ce0: stur            w0, [x2, #0x13]
    // 0xb29ce4: ldur            x0, [fp, #-0x40]
    // 0xb29ce8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb29ce8: stur            w0, [x2, #0x17]
    // 0xb29cec: r1 = <Widget>
    //     0xb29cec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb29cf0: r0 = AllocateGrowableArray()
    //     0xb29cf0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb29cf4: mov             x1, x0
    // 0xb29cf8: ldur            x0, [fp, #-8]
    // 0xb29cfc: stur            x1, [fp, #-0x20]
    // 0xb29d00: StoreField: r1->field_f = r0
    //     0xb29d00: stur            w0, [x1, #0xf]
    // 0xb29d04: r2 = 6
    //     0xb29d04: movz            x2, #0x6
    // 0xb29d08: StoreField: r1->field_b = r2
    //     0xb29d08: stur            w2, [x1, #0xb]
    // 0xb29d0c: r0 = Column()
    //     0xb29d0c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb29d10: mov             x3, x0
    // 0xb29d14: r0 = Instance_Axis
    //     0xb29d14: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb29d18: stur            x3, [fp, #-8]
    // 0xb29d1c: StoreField: r3->field_f = r0
    //     0xb29d1c: stur            w0, [x3, #0xf]
    // 0xb29d20: r4 = Instance_MainAxisAlignment
    //     0xb29d20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb29d24: ldr             x4, [x4, #0xa08]
    // 0xb29d28: StoreField: r3->field_13 = r4
    //     0xb29d28: stur            w4, [x3, #0x13]
    // 0xb29d2c: r5 = Instance_MainAxisSize
    //     0xb29d2c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb29d30: ldr             x5, [x5, #0xa10]
    // 0xb29d34: ArrayStore: r3[0] = r5  ; List_4
    //     0xb29d34: stur            w5, [x3, #0x17]
    // 0xb29d38: r1 = Instance_CrossAxisAlignment
    //     0xb29d38: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb29d3c: ldr             x1, [x1, #0x890]
    // 0xb29d40: StoreField: r3->field_1b = r1
    //     0xb29d40: stur            w1, [x3, #0x1b]
    // 0xb29d44: r6 = Instance_VerticalDirection
    //     0xb29d44: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb29d48: ldr             x6, [x6, #0xa20]
    // 0xb29d4c: StoreField: r3->field_23 = r6
    //     0xb29d4c: stur            w6, [x3, #0x23]
    // 0xb29d50: r7 = Instance_Clip
    //     0xb29d50: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb29d54: ldr             x7, [x7, #0x38]
    // 0xb29d58: StoreField: r3->field_2b = r7
    //     0xb29d58: stur            w7, [x3, #0x2b]
    // 0xb29d5c: StoreField: r3->field_2f = rZR
    //     0xb29d5c: stur            xzr, [x3, #0x2f]
    // 0xb29d60: ldur            x1, [fp, #-0x20]
    // 0xb29d64: StoreField: r3->field_b = r1
    //     0xb29d64: stur            w1, [x3, #0xb]
    // 0xb29d68: r1 = Null
    //     0xb29d68: mov             x1, NULL
    // 0xb29d6c: r2 = 4
    //     0xb29d6c: movz            x2, #0x4
    // 0xb29d70: r0 = AllocateArray()
    //     0xb29d70: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb29d74: mov             x2, x0
    // 0xb29d78: ldur            x0, [fp, #-0x38]
    // 0xb29d7c: stur            x2, [fp, #-0x20]
    // 0xb29d80: StoreField: r2->field_f = r0
    //     0xb29d80: stur            w0, [x2, #0xf]
    // 0xb29d84: ldur            x0, [fp, #-8]
    // 0xb29d88: StoreField: r2->field_13 = r0
    //     0xb29d88: stur            w0, [x2, #0x13]
    // 0xb29d8c: r1 = <Widget>
    //     0xb29d8c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb29d90: r0 = AllocateGrowableArray()
    //     0xb29d90: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb29d94: mov             x1, x0
    // 0xb29d98: ldur            x0, [fp, #-0x20]
    // 0xb29d9c: stur            x1, [fp, #-8]
    // 0xb29da0: StoreField: r1->field_f = r0
    //     0xb29da0: stur            w0, [x1, #0xf]
    // 0xb29da4: r0 = 4
    //     0xb29da4: movz            x0, #0x4
    // 0xb29da8: StoreField: r1->field_b = r0
    //     0xb29da8: stur            w0, [x1, #0xb]
    // 0xb29dac: r0 = Row()
    //     0xb29dac: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb29db0: mov             x1, x0
    // 0xb29db4: r0 = Instance_Axis
    //     0xb29db4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb29db8: stur            x1, [fp, #-0x20]
    // 0xb29dbc: StoreField: r1->field_f = r0
    //     0xb29dbc: stur            w0, [x1, #0xf]
    // 0xb29dc0: r2 = Instance_MainAxisAlignment
    //     0xb29dc0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb29dc4: ldr             x2, [x2, #0xa08]
    // 0xb29dc8: StoreField: r1->field_13 = r2
    //     0xb29dc8: stur            w2, [x1, #0x13]
    // 0xb29dcc: r3 = Instance_MainAxisSize
    //     0xb29dcc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb29dd0: ldr             x3, [x3, #0xa10]
    // 0xb29dd4: ArrayStore: r1[0] = r3  ; List_4
    //     0xb29dd4: stur            w3, [x1, #0x17]
    // 0xb29dd8: r4 = Instance_CrossAxisAlignment
    //     0xb29dd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb29ddc: ldr             x4, [x4, #0xa18]
    // 0xb29de0: StoreField: r1->field_1b = r4
    //     0xb29de0: stur            w4, [x1, #0x1b]
    // 0xb29de4: r5 = Instance_VerticalDirection
    //     0xb29de4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb29de8: ldr             x5, [x5, #0xa20]
    // 0xb29dec: StoreField: r1->field_23 = r5
    //     0xb29dec: stur            w5, [x1, #0x23]
    // 0xb29df0: r6 = Instance_Clip
    //     0xb29df0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb29df4: ldr             x6, [x6, #0x38]
    // 0xb29df8: StoreField: r1->field_2b = r6
    //     0xb29df8: stur            w6, [x1, #0x2b]
    // 0xb29dfc: StoreField: r1->field_2f = rZR
    //     0xb29dfc: stur            xzr, [x1, #0x2f]
    // 0xb29e00: ldur            x7, [fp, #-8]
    // 0xb29e04: StoreField: r1->field_b = r7
    //     0xb29e04: stur            w7, [x1, #0xb]
    // 0xb29e08: r0 = Padding()
    //     0xb29e08: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb29e0c: mov             x1, x0
    // 0xb29e10: r0 = Instance_EdgeInsets
    //     0xb29e10: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0xb29e14: ldr             x0, [x0, #0x868]
    // 0xb29e18: stur            x1, [fp, #-8]
    // 0xb29e1c: StoreField: r1->field_f = r0
    //     0xb29e1c: stur            w0, [x1, #0xf]
    // 0xb29e20: ldur            x0, [fp, #-0x20]
    // 0xb29e24: StoreField: r1->field_b = r0
    //     0xb29e24: stur            w0, [x1, #0xb]
    // 0xb29e28: r16 = <EdgeInsets>
    //     0xb29e28: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb29e2c: ldr             x16, [x16, #0xda0]
    // 0xb29e30: r30 = Instance_EdgeInsets
    //     0xb29e30: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb29e34: ldr             lr, [lr, #0x1f0]
    // 0xb29e38: stp             lr, x16, [SP]
    // 0xb29e3c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb29e3c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb29e40: r0 = all()
    //     0xb29e40: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb29e44: ldur            x1, [fp, #-0x10]
    // 0xb29e48: stur            x0, [fp, #-0x20]
    // 0xb29e4c: r0 = of()
    //     0xb29e4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb29e50: LoadField: r1 = r0->field_5b
    //     0xb29e50: ldur            w1, [x0, #0x5b]
    // 0xb29e54: DecompressPointer r1
    //     0xb29e54: add             x1, x1, HEAP, lsl #32
    // 0xb29e58: r0 = LoadClassIdInstr(r1)
    //     0xb29e58: ldur            x0, [x1, #-1]
    //     0xb29e5c: ubfx            x0, x0, #0xc, #0x14
    // 0xb29e60: d0 = 0.100000
    //     0xb29e60: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb29e64: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb29e64: sub             lr, x0, #0xffa
    //     0xb29e68: ldr             lr, [x21, lr, lsl #3]
    //     0xb29e6c: blr             lr
    // 0xb29e70: r16 = <Color>
    //     0xb29e70: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb29e74: ldr             x16, [x16, #0xf80]
    // 0xb29e78: stp             x0, x16, [SP]
    // 0xb29e7c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb29e7c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb29e80: r0 = all()
    //     0xb29e80: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb29e84: stur            x0, [fp, #-0x30]
    // 0xb29e88: r0 = Radius()
    //     0xb29e88: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb29e8c: d0 = 30.000000
    //     0xb29e8c: fmov            d0, #30.00000000
    // 0xb29e90: stur            x0, [fp, #-0x38]
    // 0xb29e94: StoreField: r0->field_7 = d0
    //     0xb29e94: stur            d0, [x0, #7]
    // 0xb29e98: StoreField: r0->field_f = d0
    //     0xb29e98: stur            d0, [x0, #0xf]
    // 0xb29e9c: r0 = BorderRadius()
    //     0xb29e9c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb29ea0: mov             x1, x0
    // 0xb29ea4: ldur            x0, [fp, #-0x38]
    // 0xb29ea8: stur            x1, [fp, #-0x40]
    // 0xb29eac: StoreField: r1->field_7 = r0
    //     0xb29eac: stur            w0, [x1, #7]
    // 0xb29eb0: StoreField: r1->field_b = r0
    //     0xb29eb0: stur            w0, [x1, #0xb]
    // 0xb29eb4: StoreField: r1->field_f = r0
    //     0xb29eb4: stur            w0, [x1, #0xf]
    // 0xb29eb8: StoreField: r1->field_13 = r0
    //     0xb29eb8: stur            w0, [x1, #0x13]
    // 0xb29ebc: r0 = RoundedRectangleBorder()
    //     0xb29ebc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb29ec0: mov             x1, x0
    // 0xb29ec4: ldur            x0, [fp, #-0x40]
    // 0xb29ec8: StoreField: r1->field_b = r0
    //     0xb29ec8: stur            w0, [x1, #0xb]
    // 0xb29ecc: r0 = Instance_BorderSide
    //     0xb29ecc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb29ed0: ldr             x0, [x0, #0xe20]
    // 0xb29ed4: StoreField: r1->field_7 = r0
    //     0xb29ed4: stur            w0, [x1, #7]
    // 0xb29ed8: r16 = <RoundedRectangleBorder>
    //     0xb29ed8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb29edc: ldr             x16, [x16, #0xf78]
    // 0xb29ee0: stp             x1, x16, [SP]
    // 0xb29ee4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb29ee4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb29ee8: r0 = all()
    //     0xb29ee8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb29eec: stur            x0, [fp, #-0x38]
    // 0xb29ef0: r0 = ButtonStyle()
    //     0xb29ef0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb29ef4: mov             x1, x0
    // 0xb29ef8: ldur            x0, [fp, #-0x30]
    // 0xb29efc: stur            x1, [fp, #-0x40]
    // 0xb29f00: StoreField: r1->field_b = r0
    //     0xb29f00: stur            w0, [x1, #0xb]
    // 0xb29f04: ldur            x0, [fp, #-0x20]
    // 0xb29f08: StoreField: r1->field_23 = r0
    //     0xb29f08: stur            w0, [x1, #0x23]
    // 0xb29f0c: ldur            x0, [fp, #-0x38]
    // 0xb29f10: StoreField: r1->field_43 = r0
    //     0xb29f10: stur            w0, [x1, #0x43]
    // 0xb29f14: r0 = TextButtonThemeData()
    //     0xb29f14: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb29f18: mov             x2, x0
    // 0xb29f1c: ldur            x0, [fp, #-0x40]
    // 0xb29f20: stur            x2, [fp, #-0x20]
    // 0xb29f24: StoreField: r2->field_7 = r0
    //     0xb29f24: stur            w0, [x2, #7]
    // 0xb29f28: r1 = "i\'ll choose"
    //     0xb29f28: add             x1, PP, #0x57, lsl #12  ; [pp+0x57378] "i\'ll choose"
    //     0xb29f2c: ldr             x1, [x1, #0x378]
    // 0xb29f30: r0 = capitalizeFirstWord()
    //     0xb29f30: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb29f34: ldur            x1, [fp, #-0x10]
    // 0xb29f38: stur            x0, [fp, #-0x30]
    // 0xb29f3c: r0 = of()
    //     0xb29f3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb29f40: LoadField: r1 = r0->field_87
    //     0xb29f40: ldur            w1, [x0, #0x87]
    // 0xb29f44: DecompressPointer r1
    //     0xb29f44: add             x1, x1, HEAP, lsl #32
    // 0xb29f48: LoadField: r0 = r1->field_7
    //     0xb29f48: ldur            w0, [x1, #7]
    // 0xb29f4c: DecompressPointer r0
    //     0xb29f4c: add             x0, x0, HEAP, lsl #32
    // 0xb29f50: ldur            x1, [fp, #-0x10]
    // 0xb29f54: stur            x0, [fp, #-0x38]
    // 0xb29f58: r0 = of()
    //     0xb29f58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb29f5c: LoadField: r1 = r0->field_5b
    //     0xb29f5c: ldur            w1, [x0, #0x5b]
    // 0xb29f60: DecompressPointer r1
    //     0xb29f60: add             x1, x1, HEAP, lsl #32
    // 0xb29f64: r16 = 14.000000
    //     0xb29f64: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb29f68: ldr             x16, [x16, #0x1d8]
    // 0xb29f6c: stp             x1, x16, [SP]
    // 0xb29f70: ldur            x1, [fp, #-0x38]
    // 0xb29f74: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb29f74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb29f78: ldr             x4, [x4, #0xaa0]
    // 0xb29f7c: r0 = copyWith()
    //     0xb29f7c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb29f80: stur            x0, [fp, #-0x38]
    // 0xb29f84: r0 = Text()
    //     0xb29f84: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb29f88: mov             x3, x0
    // 0xb29f8c: ldur            x0, [fp, #-0x30]
    // 0xb29f90: stur            x3, [fp, #-0x40]
    // 0xb29f94: StoreField: r3->field_b = r0
    //     0xb29f94: stur            w0, [x3, #0xb]
    // 0xb29f98: ldur            x0, [fp, #-0x38]
    // 0xb29f9c: StoreField: r3->field_13 = r0
    //     0xb29f9c: stur            w0, [x3, #0x13]
    // 0xb29fa0: ldur            x2, [fp, #-0x18]
    // 0xb29fa4: r1 = Function '<anonymous closure>':.
    //     0xb29fa4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57380] AnonymousClosure: (0xb2a52c), in [package:customer_app/app/presentation/views/glass/bag/bag_bottom_sheet_to_choose.dart] _BagBottomSheetToChooseState::build (0xb295c0)
    //     0xb29fa8: ldr             x1, [x1, #0x380]
    // 0xb29fac: r0 = AllocateClosure()
    //     0xb29fac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb29fb0: stur            x0, [fp, #-0x30]
    // 0xb29fb4: r0 = TextButton()
    //     0xb29fb4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb29fb8: mov             x1, x0
    // 0xb29fbc: ldur            x0, [fp, #-0x30]
    // 0xb29fc0: stur            x1, [fp, #-0x38]
    // 0xb29fc4: StoreField: r1->field_b = r0
    //     0xb29fc4: stur            w0, [x1, #0xb]
    // 0xb29fc8: r0 = false
    //     0xb29fc8: add             x0, NULL, #0x30  ; false
    // 0xb29fcc: StoreField: r1->field_27 = r0
    //     0xb29fcc: stur            w0, [x1, #0x27]
    // 0xb29fd0: r2 = true
    //     0xb29fd0: add             x2, NULL, #0x20  ; true
    // 0xb29fd4: StoreField: r1->field_2f = r2
    //     0xb29fd4: stur            w2, [x1, #0x2f]
    // 0xb29fd8: ldur            x3, [fp, #-0x40]
    // 0xb29fdc: StoreField: r1->field_37 = r3
    //     0xb29fdc: stur            w3, [x1, #0x37]
    // 0xb29fe0: r0 = TextButtonTheme()
    //     0xb29fe0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb29fe4: mov             x2, x0
    // 0xb29fe8: ldur            x0, [fp, #-0x20]
    // 0xb29fec: stur            x2, [fp, #-0x30]
    // 0xb29ff0: StoreField: r2->field_f = r0
    //     0xb29ff0: stur            w0, [x2, #0xf]
    // 0xb29ff4: ldur            x0, [fp, #-0x38]
    // 0xb29ff8: StoreField: r2->field_b = r0
    //     0xb29ff8: stur            w0, [x2, #0xb]
    // 0xb29ffc: r1 = <FlexParentData>
    //     0xb29ffc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb2a000: ldr             x1, [x1, #0xe00]
    // 0xb2a004: r0 = Flexible()
    //     0xb2a004: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xb2a008: mov             x1, x0
    // 0xb2a00c: r0 = 1
    //     0xb2a00c: movz            x0, #0x1
    // 0xb2a010: stur            x1, [fp, #-0x20]
    // 0xb2a014: StoreField: r1->field_13 = r0
    //     0xb2a014: stur            x0, [x1, #0x13]
    // 0xb2a018: r2 = Instance_FlexFit
    //     0xb2a018: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb2a01c: ldr             x2, [x2, #0xe08]
    // 0xb2a020: StoreField: r1->field_1b = r2
    //     0xb2a020: stur            w2, [x1, #0x1b]
    // 0xb2a024: ldur            x3, [fp, #-0x30]
    // 0xb2a028: StoreField: r1->field_b = r3
    //     0xb2a028: stur            w3, [x1, #0xb]
    // 0xb2a02c: r16 = <EdgeInsets>
    //     0xb2a02c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb2a030: ldr             x16, [x16, #0xda0]
    // 0xb2a034: r30 = Instance_EdgeInsets
    //     0xb2a034: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb2a038: ldr             lr, [lr, #0x1f0]
    // 0xb2a03c: stp             lr, x16, [SP]
    // 0xb2a040: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb2a040: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb2a044: r0 = all()
    //     0xb2a044: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb2a048: ldur            x1, [fp, #-0x10]
    // 0xb2a04c: stur            x0, [fp, #-0x30]
    // 0xb2a050: r0 = of()
    //     0xb2a050: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2a054: LoadField: r1 = r0->field_5b
    //     0xb2a054: ldur            w1, [x0, #0x5b]
    // 0xb2a058: DecompressPointer r1
    //     0xb2a058: add             x1, x1, HEAP, lsl #32
    // 0xb2a05c: r16 = <Color>
    //     0xb2a05c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb2a060: ldr             x16, [x16, #0xf80]
    // 0xb2a064: stp             x1, x16, [SP]
    // 0xb2a068: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb2a068: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb2a06c: r0 = all()
    //     0xb2a06c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb2a070: stur            x0, [fp, #-0x38]
    // 0xb2a074: r0 = Radius()
    //     0xb2a074: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb2a078: d0 = 30.000000
    //     0xb2a078: fmov            d0, #30.00000000
    // 0xb2a07c: stur            x0, [fp, #-0x40]
    // 0xb2a080: StoreField: r0->field_7 = d0
    //     0xb2a080: stur            d0, [x0, #7]
    // 0xb2a084: StoreField: r0->field_f = d0
    //     0xb2a084: stur            d0, [x0, #0xf]
    // 0xb2a088: r0 = BorderRadius()
    //     0xb2a088: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb2a08c: mov             x1, x0
    // 0xb2a090: ldur            x0, [fp, #-0x40]
    // 0xb2a094: stur            x1, [fp, #-0x48]
    // 0xb2a098: StoreField: r1->field_7 = r0
    //     0xb2a098: stur            w0, [x1, #7]
    // 0xb2a09c: StoreField: r1->field_b = r0
    //     0xb2a09c: stur            w0, [x1, #0xb]
    // 0xb2a0a0: StoreField: r1->field_f = r0
    //     0xb2a0a0: stur            w0, [x1, #0xf]
    // 0xb2a0a4: StoreField: r1->field_13 = r0
    //     0xb2a0a4: stur            w0, [x1, #0x13]
    // 0xb2a0a8: r0 = RoundedRectangleBorder()
    //     0xb2a0a8: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb2a0ac: mov             x1, x0
    // 0xb2a0b0: ldur            x0, [fp, #-0x48]
    // 0xb2a0b4: StoreField: r1->field_b = r0
    //     0xb2a0b4: stur            w0, [x1, #0xb]
    // 0xb2a0b8: r0 = Instance_BorderSide
    //     0xb2a0b8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb2a0bc: ldr             x0, [x0, #0xe20]
    // 0xb2a0c0: StoreField: r1->field_7 = r0
    //     0xb2a0c0: stur            w0, [x1, #7]
    // 0xb2a0c4: r16 = <RoundedRectangleBorder>
    //     0xb2a0c4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb2a0c8: ldr             x16, [x16, #0xf78]
    // 0xb2a0cc: stp             x1, x16, [SP]
    // 0xb2a0d0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb2a0d0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb2a0d4: r0 = all()
    //     0xb2a0d4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb2a0d8: stur            x0, [fp, #-0x40]
    // 0xb2a0dc: r0 = ButtonStyle()
    //     0xb2a0dc: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb2a0e0: mov             x1, x0
    // 0xb2a0e4: ldur            x0, [fp, #-0x38]
    // 0xb2a0e8: stur            x1, [fp, #-0x48]
    // 0xb2a0ec: StoreField: r1->field_b = r0
    //     0xb2a0ec: stur            w0, [x1, #0xb]
    // 0xb2a0f0: ldur            x0, [fp, #-0x30]
    // 0xb2a0f4: StoreField: r1->field_23 = r0
    //     0xb2a0f4: stur            w0, [x1, #0x23]
    // 0xb2a0f8: ldur            x0, [fp, #-0x40]
    // 0xb2a0fc: StoreField: r1->field_43 = r0
    //     0xb2a0fc: stur            w0, [x1, #0x43]
    // 0xb2a100: r0 = TextButtonThemeData()
    //     0xb2a100: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb2a104: mov             x2, x0
    // 0xb2a108: ldur            x0, [fp, #-0x48]
    // 0xb2a10c: stur            x2, [fp, #-0x30]
    // 0xb2a110: StoreField: r2->field_7 = r0
    //     0xb2a110: stur            w0, [x2, #7]
    // 0xb2a114: r1 = "repeat last"
    //     0xb2a114: add             x1, PP, #0x57, lsl #12  ; [pp+0x57388] "repeat last"
    //     0xb2a118: ldr             x1, [x1, #0x388]
    // 0xb2a11c: r0 = capitalizeFirstWord()
    //     0xb2a11c: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb2a120: ldur            x1, [fp, #-0x10]
    // 0xb2a124: stur            x0, [fp, #-0x10]
    // 0xb2a128: r0 = of()
    //     0xb2a128: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2a12c: LoadField: r1 = r0->field_87
    //     0xb2a12c: ldur            w1, [x0, #0x87]
    // 0xb2a130: DecompressPointer r1
    //     0xb2a130: add             x1, x1, HEAP, lsl #32
    // 0xb2a134: LoadField: r0 = r1->field_7
    //     0xb2a134: ldur            w0, [x1, #7]
    // 0xb2a138: DecompressPointer r0
    //     0xb2a138: add             x0, x0, HEAP, lsl #32
    // 0xb2a13c: r16 = 14.000000
    //     0xb2a13c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2a140: ldr             x16, [x16, #0x1d8]
    // 0xb2a144: r30 = Instance_Color
    //     0xb2a144: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb2a148: stp             lr, x16, [SP]
    // 0xb2a14c: mov             x1, x0
    // 0xb2a150: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2a150: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2a154: ldr             x4, [x4, #0xaa0]
    // 0xb2a158: r0 = copyWith()
    //     0xb2a158: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2a15c: stur            x0, [fp, #-0x38]
    // 0xb2a160: r0 = Text()
    //     0xb2a160: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2a164: mov             x3, x0
    // 0xb2a168: ldur            x0, [fp, #-0x10]
    // 0xb2a16c: stur            x3, [fp, #-0x40]
    // 0xb2a170: StoreField: r3->field_b = r0
    //     0xb2a170: stur            w0, [x3, #0xb]
    // 0xb2a174: ldur            x0, [fp, #-0x38]
    // 0xb2a178: StoreField: r3->field_13 = r0
    //     0xb2a178: stur            w0, [x3, #0x13]
    // 0xb2a17c: ldur            x2, [fp, #-0x18]
    // 0xb2a180: r1 = Function '<anonymous closure>':.
    //     0xb2a180: add             x1, PP, #0x57, lsl #12  ; [pp+0x57390] AnonymousClosure: (0xb2a3a8), in [package:customer_app/app/presentation/views/glass/bag/bag_bottom_sheet_to_choose.dart] _BagBottomSheetToChooseState::build (0xb295c0)
    //     0xb2a184: ldr             x1, [x1, #0x390]
    // 0xb2a188: r0 = AllocateClosure()
    //     0xb2a188: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2a18c: stur            x0, [fp, #-0x10]
    // 0xb2a190: r0 = TextButton()
    //     0xb2a190: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb2a194: mov             x1, x0
    // 0xb2a198: ldur            x0, [fp, #-0x10]
    // 0xb2a19c: stur            x1, [fp, #-0x18]
    // 0xb2a1a0: StoreField: r1->field_b = r0
    //     0xb2a1a0: stur            w0, [x1, #0xb]
    // 0xb2a1a4: r0 = false
    //     0xb2a1a4: add             x0, NULL, #0x30  ; false
    // 0xb2a1a8: StoreField: r1->field_27 = r0
    //     0xb2a1a8: stur            w0, [x1, #0x27]
    // 0xb2a1ac: r0 = true
    //     0xb2a1ac: add             x0, NULL, #0x20  ; true
    // 0xb2a1b0: StoreField: r1->field_2f = r0
    //     0xb2a1b0: stur            w0, [x1, #0x2f]
    // 0xb2a1b4: ldur            x0, [fp, #-0x40]
    // 0xb2a1b8: StoreField: r1->field_37 = r0
    //     0xb2a1b8: stur            w0, [x1, #0x37]
    // 0xb2a1bc: r0 = TextButtonTheme()
    //     0xb2a1bc: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb2a1c0: mov             x2, x0
    // 0xb2a1c4: ldur            x0, [fp, #-0x30]
    // 0xb2a1c8: stur            x2, [fp, #-0x10]
    // 0xb2a1cc: StoreField: r2->field_f = r0
    //     0xb2a1cc: stur            w0, [x2, #0xf]
    // 0xb2a1d0: ldur            x0, [fp, #-0x18]
    // 0xb2a1d4: StoreField: r2->field_b = r0
    //     0xb2a1d4: stur            w0, [x2, #0xb]
    // 0xb2a1d8: r1 = <FlexParentData>
    //     0xb2a1d8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb2a1dc: ldr             x1, [x1, #0xe00]
    // 0xb2a1e0: r0 = Flexible()
    //     0xb2a1e0: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xb2a1e4: mov             x3, x0
    // 0xb2a1e8: r0 = 1
    //     0xb2a1e8: movz            x0, #0x1
    // 0xb2a1ec: stur            x3, [fp, #-0x18]
    // 0xb2a1f0: StoreField: r3->field_13 = r0
    //     0xb2a1f0: stur            x0, [x3, #0x13]
    // 0xb2a1f4: r0 = Instance_FlexFit
    //     0xb2a1f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb2a1f8: ldr             x0, [x0, #0xe08]
    // 0xb2a1fc: StoreField: r3->field_1b = r0
    //     0xb2a1fc: stur            w0, [x3, #0x1b]
    // 0xb2a200: ldur            x0, [fp, #-0x10]
    // 0xb2a204: StoreField: r3->field_b = r0
    //     0xb2a204: stur            w0, [x3, #0xb]
    // 0xb2a208: r1 = Null
    //     0xb2a208: mov             x1, NULL
    // 0xb2a20c: r2 = 6
    //     0xb2a20c: movz            x2, #0x6
    // 0xb2a210: r0 = AllocateArray()
    //     0xb2a210: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2a214: mov             x2, x0
    // 0xb2a218: ldur            x0, [fp, #-0x20]
    // 0xb2a21c: stur            x2, [fp, #-0x10]
    // 0xb2a220: StoreField: r2->field_f = r0
    //     0xb2a220: stur            w0, [x2, #0xf]
    // 0xb2a224: r16 = Instance_SizedBox
    //     0xb2a224: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb2a228: ldr             x16, [x16, #0xb20]
    // 0xb2a22c: StoreField: r2->field_13 = r16
    //     0xb2a22c: stur            w16, [x2, #0x13]
    // 0xb2a230: ldur            x0, [fp, #-0x18]
    // 0xb2a234: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2a234: stur            w0, [x2, #0x17]
    // 0xb2a238: r1 = <Widget>
    //     0xb2a238: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2a23c: r0 = AllocateGrowableArray()
    //     0xb2a23c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2a240: mov             x1, x0
    // 0xb2a244: ldur            x0, [fp, #-0x10]
    // 0xb2a248: stur            x1, [fp, #-0x18]
    // 0xb2a24c: StoreField: r1->field_f = r0
    //     0xb2a24c: stur            w0, [x1, #0xf]
    // 0xb2a250: r2 = 6
    //     0xb2a250: movz            x2, #0x6
    // 0xb2a254: StoreField: r1->field_b = r2
    //     0xb2a254: stur            w2, [x1, #0xb]
    // 0xb2a258: r0 = Row()
    //     0xb2a258: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2a25c: mov             x3, x0
    // 0xb2a260: r0 = Instance_Axis
    //     0xb2a260: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2a264: stur            x3, [fp, #-0x10]
    // 0xb2a268: StoreField: r3->field_f = r0
    //     0xb2a268: stur            w0, [x3, #0xf]
    // 0xb2a26c: r0 = Instance_MainAxisAlignment
    //     0xb2a26c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb2a270: ldr             x0, [x0, #0xd10]
    // 0xb2a274: StoreField: r3->field_13 = r0
    //     0xb2a274: stur            w0, [x3, #0x13]
    // 0xb2a278: r0 = Instance_MainAxisSize
    //     0xb2a278: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2a27c: ldr             x0, [x0, #0xa10]
    // 0xb2a280: ArrayStore: r3[0] = r0  ; List_4
    //     0xb2a280: stur            w0, [x3, #0x17]
    // 0xb2a284: r0 = Instance_CrossAxisAlignment
    //     0xb2a284: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2a288: ldr             x0, [x0, #0xa18]
    // 0xb2a28c: StoreField: r3->field_1b = r0
    //     0xb2a28c: stur            w0, [x3, #0x1b]
    // 0xb2a290: r4 = Instance_VerticalDirection
    //     0xb2a290: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2a294: ldr             x4, [x4, #0xa20]
    // 0xb2a298: StoreField: r3->field_23 = r4
    //     0xb2a298: stur            w4, [x3, #0x23]
    // 0xb2a29c: r5 = Instance_Clip
    //     0xb2a29c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2a2a0: ldr             x5, [x5, #0x38]
    // 0xb2a2a4: StoreField: r3->field_2b = r5
    //     0xb2a2a4: stur            w5, [x3, #0x2b]
    // 0xb2a2a8: StoreField: r3->field_2f = rZR
    //     0xb2a2a8: stur            xzr, [x3, #0x2f]
    // 0xb2a2ac: ldur            x1, [fp, #-0x18]
    // 0xb2a2b0: StoreField: r3->field_b = r1
    //     0xb2a2b0: stur            w1, [x3, #0xb]
    // 0xb2a2b4: r1 = Null
    //     0xb2a2b4: mov             x1, NULL
    // 0xb2a2b8: r2 = 6
    //     0xb2a2b8: movz            x2, #0x6
    // 0xb2a2bc: r0 = AllocateArray()
    //     0xb2a2bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2a2c0: mov             x2, x0
    // 0xb2a2c4: ldur            x0, [fp, #-0x28]
    // 0xb2a2c8: stur            x2, [fp, #-0x18]
    // 0xb2a2cc: StoreField: r2->field_f = r0
    //     0xb2a2cc: stur            w0, [x2, #0xf]
    // 0xb2a2d0: ldur            x0, [fp, #-8]
    // 0xb2a2d4: StoreField: r2->field_13 = r0
    //     0xb2a2d4: stur            w0, [x2, #0x13]
    // 0xb2a2d8: ldur            x0, [fp, #-0x10]
    // 0xb2a2dc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2a2dc: stur            w0, [x2, #0x17]
    // 0xb2a2e0: r1 = <Widget>
    //     0xb2a2e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2a2e4: r0 = AllocateGrowableArray()
    //     0xb2a2e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2a2e8: mov             x1, x0
    // 0xb2a2ec: ldur            x0, [fp, #-0x18]
    // 0xb2a2f0: stur            x1, [fp, #-8]
    // 0xb2a2f4: StoreField: r1->field_f = r0
    //     0xb2a2f4: stur            w0, [x1, #0xf]
    // 0xb2a2f8: r0 = 6
    //     0xb2a2f8: movz            x0, #0x6
    // 0xb2a2fc: StoreField: r1->field_b = r0
    //     0xb2a2fc: stur            w0, [x1, #0xb]
    // 0xb2a300: r0 = Column()
    //     0xb2a300: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb2a304: mov             x1, x0
    // 0xb2a308: r0 = Instance_Axis
    //     0xb2a308: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2a30c: stur            x1, [fp, #-0x10]
    // 0xb2a310: StoreField: r1->field_f = r0
    //     0xb2a310: stur            w0, [x1, #0xf]
    // 0xb2a314: r0 = Instance_MainAxisAlignment
    //     0xb2a314: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2a318: ldr             x0, [x0, #0xa08]
    // 0xb2a31c: StoreField: r1->field_13 = r0
    //     0xb2a31c: stur            w0, [x1, #0x13]
    // 0xb2a320: r0 = Instance_MainAxisSize
    //     0xb2a320: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb2a324: ldr             x0, [x0, #0xdd0]
    // 0xb2a328: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2a328: stur            w0, [x1, #0x17]
    // 0xb2a32c: r0 = Instance_CrossAxisAlignment
    //     0xb2a32c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2a330: ldr             x0, [x0, #0xa18]
    // 0xb2a334: StoreField: r1->field_1b = r0
    //     0xb2a334: stur            w0, [x1, #0x1b]
    // 0xb2a338: r0 = Instance_VerticalDirection
    //     0xb2a338: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2a33c: ldr             x0, [x0, #0xa20]
    // 0xb2a340: StoreField: r1->field_23 = r0
    //     0xb2a340: stur            w0, [x1, #0x23]
    // 0xb2a344: r0 = Instance_Clip
    //     0xb2a344: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2a348: ldr             x0, [x0, #0x38]
    // 0xb2a34c: StoreField: r1->field_2b = r0
    //     0xb2a34c: stur            w0, [x1, #0x2b]
    // 0xb2a350: StoreField: r1->field_2f = rZR
    //     0xb2a350: stur            xzr, [x1, #0x2f]
    // 0xb2a354: ldur            x0, [fp, #-8]
    // 0xb2a358: StoreField: r1->field_b = r0
    //     0xb2a358: stur            w0, [x1, #0xb]
    // 0xb2a35c: r0 = Padding()
    //     0xb2a35c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2a360: r1 = Instance_EdgeInsets
    //     0xb2a360: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb2a364: ldr             x1, [x1, #0xd0]
    // 0xb2a368: StoreField: r0->field_f = r1
    //     0xb2a368: stur            w1, [x0, #0xf]
    // 0xb2a36c: ldur            x1, [fp, #-0x10]
    // 0xb2a370: StoreField: r0->field_b = r1
    //     0xb2a370: stur            w1, [x0, #0xb]
    // 0xb2a374: LeaveFrame
    //     0xb2a374: mov             SP, fp
    //     0xb2a378: ldp             fp, lr, [SP], #0x10
    // 0xb2a37c: ret
    //     0xb2a37c: ret             
    // 0xb2a380: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2a380: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2a384: b               #0xb295e8
    // 0xb2a388: r9 = image
    //     0xb2a388: add             x9, PP, #0x57, lsl #12  ; [pp+0x57398] Field <<EMAIL>>: late (offset: 0x14)
    //     0xb2a38c: ldr             x9, [x9, #0x398]
    // 0xb2a390: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb2a390: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb2a394: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2a394: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2a398: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2a398: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2a39c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2a39c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2a3a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2a3a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2a3a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2a3a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb2a3a8, size: 0x184
    // 0xb2a3a8: EnterFrame
    //     0xb2a3a8: stp             fp, lr, [SP, #-0x10]!
    //     0xb2a3ac: mov             fp, SP
    // 0xb2a3b0: AllocStack(0x38)
    //     0xb2a3b0: sub             SP, SP, #0x38
    // 0xb2a3b4: SetupParameters()
    //     0xb2a3b4: ldr             x0, [fp, #0x10]
    //     0xb2a3b8: ldur            w1, [x0, #0x17]
    //     0xb2a3bc: add             x1, x1, HEAP, lsl #32
    //     0xb2a3c0: stur            x1, [fp, #-8]
    // 0xb2a3c4: CheckStackOverflow
    //     0xb2a3c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2a3c8: cmp             SP, x16
    //     0xb2a3cc: b.ls            #0xb2a51c
    // 0xb2a3d0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb2a3d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb2a3d4: ldr             x0, [x0, #0x1c80]
    //     0xb2a3d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb2a3dc: cmp             w0, w16
    //     0xb2a3e0: b.ne            #0xb2a3ec
    //     0xb2a3e4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb2a3e8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb2a3ec: str             NULL, [SP]
    // 0xb2a3f0: r4 = const [0x1, 0, 0, 0, null]
    //     0xb2a3f0: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xb2a3f4: r0 = GetNavigation.back()
    //     0xb2a3f4: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xb2a3f8: ldur            x0, [fp, #-8]
    // 0xb2a3fc: LoadField: r1 = r0->field_f
    //     0xb2a3fc: ldur            w1, [x0, #0xf]
    // 0xb2a400: DecompressPointer r1
    //     0xb2a400: add             x1, x1, HEAP, lsl #32
    // 0xb2a404: LoadField: r0 = r1->field_b
    //     0xb2a404: ldur            w0, [x1, #0xb]
    // 0xb2a408: DecompressPointer r0
    //     0xb2a408: add             x0, x0, HEAP, lsl #32
    // 0xb2a40c: stur            x0, [fp, #-0x28]
    // 0xb2a410: cmp             w0, NULL
    // 0xb2a414: b.eq            #0xb2a524
    // 0xb2a418: LoadField: r1 = r0->field_b
    //     0xb2a418: ldur            x1, [x0, #0xb]
    // 0xb2a41c: LoadField: r2 = r0->field_13
    //     0xb2a41c: ldur            w2, [x0, #0x13]
    // 0xb2a420: DecompressPointer r2
    //     0xb2a420: add             x2, x2, HEAP, lsl #32
    // 0xb2a424: cmp             w2, NULL
    // 0xb2a428: b.eq            #0xb2a528
    // 0xb2a42c: r3 = LoadInt32Instr(r2)
    //     0xb2a42c: sbfx            x3, x2, #1, #0x1f
    //     0xb2a430: tbz             w2, #0, #0xb2a438
    //     0xb2a434: ldur            x3, [x2, #7]
    // 0xb2a438: sub             x2, x1, x3
    // 0xb2a43c: stur            x2, [fp, #-0x20]
    // 0xb2a440: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb2a440: ldur            w1, [x0, #0x17]
    // 0xb2a444: DecompressPointer r1
    //     0xb2a444: add             x1, x1, HEAP, lsl #32
    // 0xb2a448: cmp             w1, NULL
    // 0xb2a44c: b.ne            #0xb2a458
    // 0xb2a450: r3 = Null
    //     0xb2a450: mov             x3, NULL
    // 0xb2a454: b               #0xb2a460
    // 0xb2a458: LoadField: r3 = r1->field_b
    //     0xb2a458: ldur            w3, [x1, #0xb]
    // 0xb2a45c: DecompressPointer r3
    //     0xb2a45c: add             x3, x3, HEAP, lsl #32
    // 0xb2a460: stur            x3, [fp, #-0x18]
    // 0xb2a464: cmp             w1, NULL
    // 0xb2a468: b.ne            #0xb2a474
    // 0xb2a46c: r4 = Null
    //     0xb2a46c: mov             x4, NULL
    // 0xb2a470: b               #0xb2a498
    // 0xb2a474: LoadField: r4 = r1->field_1f
    //     0xb2a474: ldur            w4, [x1, #0x1f]
    // 0xb2a478: DecompressPointer r4
    //     0xb2a478: add             x4, x4, HEAP, lsl #32
    // 0xb2a47c: cmp             w4, NULL
    // 0xb2a480: b.ne            #0xb2a48c
    // 0xb2a484: r4 = Null
    //     0xb2a484: mov             x4, NULL
    // 0xb2a488: b               #0xb2a498
    // 0xb2a48c: LoadField: r5 = r4->field_b
    //     0xb2a48c: ldur            w5, [x4, #0xb]
    // 0xb2a490: DecompressPointer r5
    //     0xb2a490: add             x5, x5, HEAP, lsl #32
    // 0xb2a494: mov             x4, x5
    // 0xb2a498: stur            x4, [fp, #-0x10]
    // 0xb2a49c: cmp             w1, NULL
    // 0xb2a4a0: b.ne            #0xb2a4ac
    // 0xb2a4a4: r1 = Null
    //     0xb2a4a4: mov             x1, NULL
    // 0xb2a4a8: b               #0xb2a4b8
    // 0xb2a4ac: LoadField: r5 = r1->field_83
    //     0xb2a4ac: ldur            w5, [x1, #0x83]
    // 0xb2a4b0: DecompressPointer r5
    //     0xb2a4b0: add             x5, x5, HEAP, lsl #32
    // 0xb2a4b4: mov             x1, x5
    // 0xb2a4b8: stur            x1, [fp, #-8]
    // 0xb2a4bc: r0 = AddToBagRequest()
    //     0xb2a4bc: bl              #0x9c67ec  ; AllocateAddToBagRequestStub -> AddToBagRequest (size=0x1c)
    // 0xb2a4c0: mov             x1, x0
    // 0xb2a4c4: ldur            x0, [fp, #-0x18]
    // 0xb2a4c8: StoreField: r1->field_7 = r0
    //     0xb2a4c8: stur            w0, [x1, #7]
    // 0xb2a4cc: ldur            x0, [fp, #-0x10]
    // 0xb2a4d0: StoreField: r1->field_b = r0
    //     0xb2a4d0: stur            w0, [x1, #0xb]
    // 0xb2a4d4: ldur            x0, [fp, #-0x20]
    // 0xb2a4d8: StoreField: r1->field_f = r0
    //     0xb2a4d8: stur            x0, [x1, #0xf]
    // 0xb2a4dc: ldur            x0, [fp, #-8]
    // 0xb2a4e0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2a4e0: stur            w0, [x1, #0x17]
    // 0xb2a4e4: ldur            x0, [fp, #-0x28]
    // 0xb2a4e8: LoadField: r2 = r0->field_1f
    //     0xb2a4e8: ldur            w2, [x0, #0x1f]
    // 0xb2a4ec: DecompressPointer r2
    //     0xb2a4ec: add             x2, x2, HEAP, lsl #32
    // 0xb2a4f0: stp             x1, x2, [SP]
    // 0xb2a4f4: r4 = 0
    //     0xb2a4f4: movz            x4, #0
    // 0xb2a4f8: ldr             x0, [SP, #8]
    // 0xb2a4fc: r16 = UnlinkedCall_0x613b5c
    //     0xb2a4fc: add             x16, PP, #0x57, lsl #12  ; [pp+0x573a0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb2a500: add             x16, x16, #0x3a0
    // 0xb2a504: ldp             x5, lr, [x16]
    // 0xb2a508: blr             lr
    // 0xb2a50c: r0 = Null
    //     0xb2a50c: mov             x0, NULL
    // 0xb2a510: LeaveFrame
    //     0xb2a510: mov             SP, fp
    //     0xb2a514: ldp             fp, lr, [SP], #0x10
    // 0xb2a518: ret
    //     0xb2a518: ret             
    // 0xb2a51c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2a51c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2a520: b               #0xb2a3d0
    // 0xb2a524: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2a524: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2a528: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2a528: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xb2a52c, size: 0x144
    // 0xb2a52c: EnterFrame
    //     0xb2a52c: stp             fp, lr, [SP, #-0x10]!
    //     0xb2a530: mov             fp, SP
    // 0xb2a534: AllocStack(0x40)
    //     0xb2a534: sub             SP, SP, #0x40
    // 0xb2a538: SetupParameters(_BagBottomSheetToChooseState this /* r1 */)
    //     0xb2a538: stur            NULL, [fp, #-8]
    //     0xb2a53c: movz            x0, #0
    //     0xb2a540: add             x1, fp, w0, sxtw #2
    //     0xb2a544: ldr             x1, [x1, #0x10]
    //     0xb2a548: ldur            w2, [x1, #0x17]
    //     0xb2a54c: add             x2, x2, HEAP, lsl #32
    //     0xb2a550: stur            x2, [fp, #-0x10]
    // 0xb2a554: CheckStackOverflow
    //     0xb2a554: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2a558: cmp             SP, x16
    //     0xb2a55c: b.ls            #0xb2a664
    // 0xb2a560: InitAsync() -> Future<void?>
    //     0xb2a560: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0xb2a564: bl              #0x6326e0  ; InitAsyncStub
    // 0xb2a568: ldur            x0, [fp, #-0x10]
    // 0xb2a56c: LoadField: r1 = r0->field_f
    //     0xb2a56c: ldur            w1, [x0, #0xf]
    // 0xb2a570: DecompressPointer r1
    //     0xb2a570: add             x1, x1, HEAP, lsl #32
    // 0xb2a574: LoadField: r0 = r1->field_b
    //     0xb2a574: ldur            w0, [x1, #0xb]
    // 0xb2a578: DecompressPointer r0
    //     0xb2a578: add             x0, x0, HEAP, lsl #32
    // 0xb2a57c: cmp             w0, NULL
    // 0xb2a580: b.eq            #0xb2a66c
    // 0xb2a584: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb2a584: ldur            w1, [x0, #0x17]
    // 0xb2a588: DecompressPointer r1
    //     0xb2a588: add             x1, x1, HEAP, lsl #32
    // 0xb2a58c: cmp             w1, NULL
    // 0xb2a590: b.ne            #0xb2a59c
    // 0xb2a594: r2 = Null
    //     0xb2a594: mov             x2, NULL
    // 0xb2a598: b               #0xb2a5a4
    // 0xb2a59c: LoadField: r2 = r1->field_7f
    //     0xb2a59c: ldur            w2, [x1, #0x7f]
    // 0xb2a5a0: DecompressPointer r2
    //     0xb2a5a0: add             x2, x2, HEAP, lsl #32
    // 0xb2a5a4: cmp             w2, NULL
    // 0xb2a5a8: b.ne            #0xb2a5b0
    // 0xb2a5ac: r2 = ""
    //     0xb2a5ac: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2a5b0: cmp             w1, NULL
    // 0xb2a5b4: b.ne            #0xb2a5c0
    // 0xb2a5b8: r3 = Null
    //     0xb2a5b8: mov             x3, NULL
    // 0xb2a5bc: b               #0xb2a5c8
    // 0xb2a5c0: LoadField: r3 = r1->field_b
    //     0xb2a5c0: ldur            w3, [x1, #0xb]
    // 0xb2a5c4: DecompressPointer r3
    //     0xb2a5c4: add             x3, x3, HEAP, lsl #32
    // 0xb2a5c8: cmp             w1, NULL
    // 0xb2a5cc: b.ne            #0xb2a5d8
    // 0xb2a5d0: r4 = Null
    //     0xb2a5d0: mov             x4, NULL
    // 0xb2a5d4: b               #0xb2a5fc
    // 0xb2a5d8: LoadField: r4 = r1->field_1f
    //     0xb2a5d8: ldur            w4, [x1, #0x1f]
    // 0xb2a5dc: DecompressPointer r4
    //     0xb2a5dc: add             x4, x4, HEAP, lsl #32
    // 0xb2a5e0: cmp             w4, NULL
    // 0xb2a5e4: b.ne            #0xb2a5f0
    // 0xb2a5e8: r4 = Null
    //     0xb2a5e8: mov             x4, NULL
    // 0xb2a5ec: b               #0xb2a5fc
    // 0xb2a5f0: LoadField: r5 = r4->field_b
    //     0xb2a5f0: ldur            w5, [x4, #0xb]
    // 0xb2a5f4: DecompressPointer r5
    //     0xb2a5f4: add             x5, x5, HEAP, lsl #32
    // 0xb2a5f8: mov             x4, x5
    // 0xb2a5fc: cmp             w1, NULL
    // 0xb2a600: b.ne            #0xb2a60c
    // 0xb2a604: r5 = Null
    //     0xb2a604: mov             x5, NULL
    // 0xb2a608: b               #0xb2a614
    // 0xb2a60c: LoadField: r5 = r1->field_83
    //     0xb2a60c: ldur            w5, [x1, #0x83]
    // 0xb2a610: DecompressPointer r5
    //     0xb2a610: add             x5, x5, HEAP, lsl #32
    // 0xb2a614: cmp             w1, NULL
    // 0xb2a618: b.ne            #0xb2a624
    // 0xb2a61c: r1 = Null
    //     0xb2a61c: mov             x1, NULL
    // 0xb2a620: b               #0xb2a630
    // 0xb2a624: LoadField: r6 = r1->field_43
    //     0xb2a624: ldur            w6, [x1, #0x43]
    // 0xb2a628: DecompressPointer r6
    //     0xb2a628: add             x6, x6, HEAP, lsl #32
    // 0xb2a62c: mov             x1, x6
    // 0xb2a630: LoadField: r6 = r0->field_1b
    //     0xb2a630: ldur            w6, [x0, #0x1b]
    // 0xb2a634: DecompressPointer r6
    //     0xb2a634: add             x6, x6, HEAP, lsl #32
    // 0xb2a638: stp             x2, x6, [SP, #0x20]
    // 0xb2a63c: stp             x4, x3, [SP, #0x10]
    // 0xb2a640: stp             x1, x5, [SP]
    // 0xb2a644: r4 = 0
    //     0xb2a644: movz            x4, #0
    // 0xb2a648: ldr             x0, [SP, #0x28]
    // 0xb2a64c: r16 = UnlinkedCall_0x613b5c
    //     0xb2a64c: add             x16, PP, #0x57, lsl #12  ; [pp+0x573b0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb2a650: add             x16, x16, #0x3b0
    // 0xb2a654: ldp             x5, lr, [x16]
    // 0xb2a658: blr             lr
    // 0xb2a65c: r0 = Null
    //     0xb2a65c: mov             x0, NULL
    // 0xb2a660: r0 = ReturnAsyncNotFuture()
    //     0xb2a660: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xb2a664: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2a664: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2a668: b               #0xb2a560
    // 0xb2a66c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2a66c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4123, size: 0x24, field offset: 0xc
//   const constructor, 
class BagBottomSheetToChoose extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e508, size: 0x2c
    // 0xc7e508: EnterFrame
    //     0xc7e508: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e50c: mov             fp, SP
    // 0xc7e510: mov             x0, x1
    // 0xc7e514: r1 = <BagBottomSheetToChoose>
    //     0xc7e514: add             x1, PP, #0x48, lsl #12  ; [pp+0x48ab0] TypeArguments: <BagBottomSheetToChoose>
    //     0xc7e518: ldr             x1, [x1, #0xab0]
    // 0xc7e51c: r0 = _BagBottomSheetToChooseState()
    //     0xc7e51c: bl              #0xc7e534  ; Allocate_BagBottomSheetToChooseStateStub -> _BagBottomSheetToChooseState (size=0x18)
    // 0xc7e520: r1 = Sentinel
    //     0xc7e520: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7e524: StoreField: r0->field_13 = r1
    //     0xc7e524: stur            w1, [x0, #0x13]
    // 0xc7e528: LeaveFrame
    //     0xc7e528: mov             SP, fp
    //     0xc7e52c: ldp             fp, lr, [SP], #0x10
    // 0xc7e530: ret
    //     0xc7e530: ret             
  }
}
