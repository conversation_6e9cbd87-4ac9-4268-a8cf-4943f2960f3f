// lib: , url: package:customer_app/app/presentation/views/glass/search/search_page.dart

// class id: 1049458, size: 0x8
class :: {
}

// class id: 4550, size: 0x14, field offset: 0x14
//   const constructor, 
class SearchPage extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14fce78, size: 0xe4
    // 0x14fce78: EnterFrame
    //     0x14fce78: stp             fp, lr, [SP, #-0x10]!
    //     0x14fce7c: mov             fp, SP
    // 0x14fce80: AllocStack(0x20)
    //     0x14fce80: sub             SP, SP, #0x20
    // 0x14fce84: SetupParameters(SearchPage this /* r1 => r0, fp-0x8 */)
    //     0x14fce84: mov             x0, x1
    //     0x14fce88: stur            x1, [fp, #-8]
    // 0x14fce8c: CheckStackOverflow
    //     0x14fce8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fce90: cmp             SP, x16
    //     0x14fce94: b.ls            #0x14fcf54
    // 0x14fce98: r1 = 1
    //     0x14fce98: movz            x1, #0x1
    // 0x14fce9c: r0 = AllocateContext()
    //     0x14fce9c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14fcea0: ldur            x2, [fp, #-8]
    // 0x14fcea4: stur            x0, [fp, #-0x10]
    // 0x14fcea8: StoreField: r0->field_f = r2
    //     0x14fcea8: stur            w2, [x0, #0xf]
    // 0x14fceac: r0 = Obx()
    //     0x14fceac: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14fceb0: ldur            x2, [fp, #-0x10]
    // 0x14fceb4: r1 = Function '<anonymous closure>':.
    //     0x14fceb4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e890] AnonymousClosure: (0x14fcf94), in [package:customer_app/app/presentation/views/glass/search/search_page.dart] SearchPage::body (0x14fce78)
    //     0x14fceb8: ldr             x1, [x1, #0x890]
    // 0x14fcebc: stur            x0, [fp, #-0x18]
    // 0x14fcec0: r0 = AllocateClosure()
    //     0x14fcec0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fcec4: mov             x1, x0
    // 0x14fcec8: ldur            x0, [fp, #-0x18]
    // 0x14fcecc: StoreField: r0->field_b = r1
    //     0x14fcecc: stur            w1, [x0, #0xb]
    // 0x14fced0: r0 = WillPopScope()
    //     0x14fced0: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14fced4: mov             x3, x0
    // 0x14fced8: ldur            x0, [fp, #-0x18]
    // 0x14fcedc: stur            x3, [fp, #-0x20]
    // 0x14fcee0: StoreField: r3->field_b = r0
    //     0x14fcee0: stur            w0, [x3, #0xb]
    // 0x14fcee4: ldur            x2, [fp, #-8]
    // 0x14fcee8: r1 = Function 'onBackPress':.
    //     0x14fcee8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e898] AnonymousClosure: (0x14fcf5c), in [package:customer_app/app/presentation/views/basic/search/search_page.dart] SearchPage::onBackPress (0x1479784)
    //     0x14fceec: ldr             x1, [x1, #0x898]
    // 0x14fcef0: r0 = AllocateClosure()
    //     0x14fcef0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fcef4: mov             x1, x0
    // 0x14fcef8: ldur            x0, [fp, #-0x20]
    // 0x14fcefc: StoreField: r0->field_f = r1
    //     0x14fcefc: stur            w1, [x0, #0xf]
    // 0x14fcf00: ldur            x2, [fp, #-0x10]
    // 0x14fcf04: r1 = Function '<anonymous closure>':.
    //     0x14fcf04: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8a0] AnonymousClosure: (0x14796e4), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14fcf08: ldr             x1, [x1, #0x8a0]
    // 0x14fcf0c: r0 = AllocateClosure()
    //     0x14fcf0c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fcf10: ldur            x2, [fp, #-0x10]
    // 0x14fcf14: r1 = Function '<anonymous closure>':.
    //     0x14fcf14: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8a8] AnonymousClosure: (0x1479614), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14fcf18: ldr             x1, [x1, #0x8a8]
    // 0x14fcf1c: stur            x0, [fp, #-8]
    // 0x14fcf20: r0 = AllocateClosure()
    //     0x14fcf20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fcf24: stur            x0, [fp, #-0x10]
    // 0x14fcf28: r0 = PagingView()
    //     0x14fcf28: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x14fcf2c: mov             x1, x0
    // 0x14fcf30: ldur            x2, [fp, #-0x20]
    // 0x14fcf34: ldur            x3, [fp, #-0x10]
    // 0x14fcf38: ldur            x5, [fp, #-8]
    // 0x14fcf3c: stur            x0, [fp, #-8]
    // 0x14fcf40: r0 = PagingView()
    //     0x14fcf40: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x14fcf44: ldur            x0, [fp, #-8]
    // 0x14fcf48: LeaveFrame
    //     0x14fcf48: mov             SP, fp
    //     0x14fcf4c: ldp             fp, lr, [SP], #0x10
    // 0x14fcf50: ret
    //     0x14fcf50: ret             
    // 0x14fcf54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fcf54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fcf58: b               #0x14fce98
  }
  [closure] Future<bool> onBackPress(dynamic) {
    // ** addr: 0x14fcf5c, size: 0x38
    // 0x14fcf5c: EnterFrame
    //     0x14fcf5c: stp             fp, lr, [SP, #-0x10]!
    //     0x14fcf60: mov             fp, SP
    // 0x14fcf64: ldr             x0, [fp, #0x10]
    // 0x14fcf68: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14fcf68: ldur            w1, [x0, #0x17]
    // 0x14fcf6c: DecompressPointer r1
    //     0x14fcf6c: add             x1, x1, HEAP, lsl #32
    // 0x14fcf70: CheckStackOverflow
    //     0x14fcf70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fcf74: cmp             SP, x16
    //     0x14fcf78: b.ls            #0x14fcf8c
    // 0x14fcf7c: r0 = onBackPress()
    //     0x14fcf7c: bl              #0x1479784  ; [package:customer_app/app/presentation/views/basic/search/search_page.dart] SearchPage::onBackPress
    // 0x14fcf80: LeaveFrame
    //     0x14fcf80: mov             SP, fp
    //     0x14fcf84: ldp             fp, lr, [SP], #0x10
    // 0x14fcf88: ret
    //     0x14fcf88: ret             
    // 0x14fcf8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fcf8c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fcf90: b               #0x14fcf7c
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0x14fcf94, size: 0x2d4
    // 0x14fcf94: EnterFrame
    //     0x14fcf94: stp             fp, lr, [SP, #-0x10]!
    //     0x14fcf98: mov             fp, SP
    // 0x14fcf9c: AllocStack(0xd0)
    //     0x14fcf9c: sub             SP, SP, #0xd0
    // 0x14fcfa0: SetupParameters()
    //     0x14fcfa0: ldr             x0, [fp, #0x10]
    //     0x14fcfa4: ldur            w2, [x0, #0x17]
    //     0x14fcfa8: add             x2, x2, HEAP, lsl #32
    //     0x14fcfac: stur            x2, [fp, #-8]
    // 0x14fcfb0: CheckStackOverflow
    //     0x14fcfb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fcfb4: cmp             SP, x16
    //     0x14fcfb8: b.ls            #0x14fd260
    // 0x14fcfbc: LoadField: r1 = r2->field_f
    //     0x14fcfbc: ldur            w1, [x2, #0xf]
    // 0x14fcfc0: DecompressPointer r1
    //     0x14fcfc0: add             x1, x1, HEAP, lsl #32
    // 0x14fcfc4: r0 = controller()
    //     0x14fcfc4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fcfc8: LoadField: r1 = r0->field_57
    //     0x14fcfc8: ldur            w1, [x0, #0x57]
    // 0x14fcfcc: DecompressPointer r1
    //     0x14fcfcc: add             x1, x1, HEAP, lsl #32
    // 0x14fcfd0: r0 = value()
    //     0x14fcfd0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14fcfd4: ldur            x2, [fp, #-8]
    // 0x14fcfd8: stur            x0, [fp, #-0x10]
    // 0x14fcfdc: LoadField: r1 = r2->field_f
    //     0x14fcfdc: ldur            w1, [x2, #0xf]
    // 0x14fcfe0: DecompressPointer r1
    //     0x14fcfe0: add             x1, x1, HEAP, lsl #32
    // 0x14fcfe4: r0 = controller()
    //     0x14fcfe4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fcfe8: LoadField: r1 = r0->field_57
    //     0x14fcfe8: ldur            w1, [x0, #0x57]
    // 0x14fcfec: DecompressPointer r1
    //     0x14fcfec: add             x1, x1, HEAP, lsl #32
    // 0x14fcff0: r0 = value()
    //     0x14fcff0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14fcff4: LoadField: r1 = r0->field_b
    //     0x14fcff4: ldur            w1, [x0, #0xb]
    // 0x14fcff8: DecompressPointer r1
    //     0x14fcff8: add             x1, x1, HEAP, lsl #32
    // 0x14fcffc: cmp             w1, NULL
    // 0x14fd000: b.ne            #0x14fd00c
    // 0x14fd004: r0 = Null
    //     0x14fd004: mov             x0, NULL
    // 0x14fd008: b               #0x14fd020
    // 0x14fd00c: LoadField: r0 = r1->field_f
    //     0x14fd00c: ldur            w0, [x1, #0xf]
    // 0x14fd010: DecompressPointer r0
    //     0x14fd010: add             x0, x0, HEAP, lsl #32
    // 0x14fd014: LoadField: r1 = r0->field_b
    //     0x14fd014: ldur            w1, [x0, #0xb]
    // 0x14fd018: DecompressPointer r1
    //     0x14fd018: add             x1, x1, HEAP, lsl #32
    // 0x14fd01c: mov             x0, x1
    // 0x14fd020: cmp             w0, NULL
    // 0x14fd024: b.ne            #0x14fd030
    // 0x14fd028: r3 = ""
    //     0x14fd028: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14fd02c: b               #0x14fd034
    // 0x14fd030: mov             x3, x0
    // 0x14fd034: ldur            x2, [fp, #-8]
    // 0x14fd038: stur            x3, [fp, #-0x18]
    // 0x14fd03c: LoadField: r1 = r2->field_f
    //     0x14fd03c: ldur            w1, [x2, #0xf]
    // 0x14fd040: DecompressPointer r1
    //     0x14fd040: add             x1, x1, HEAP, lsl #32
    // 0x14fd044: r0 = controller()
    //     0x14fd044: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fd048: LoadField: r1 = r0->field_67
    //     0x14fd048: ldur            w1, [x0, #0x67]
    // 0x14fd04c: DecompressPointer r1
    //     0x14fd04c: add             x1, x1, HEAP, lsl #32
    // 0x14fd050: r0 = value()
    //     0x14fd050: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14fd054: ldur            x2, [fp, #-8]
    // 0x14fd058: stur            x0, [fp, #-0x20]
    // 0x14fd05c: LoadField: r1 = r2->field_f
    //     0x14fd05c: ldur            w1, [x2, #0xf]
    // 0x14fd060: DecompressPointer r1
    //     0x14fd060: add             x1, x1, HEAP, lsl #32
    // 0x14fd064: r0 = controller()
    //     0x14fd064: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fd068: LoadField: r1 = r0->field_4f
    //     0x14fd068: ldur            w1, [x0, #0x4f]
    // 0x14fd06c: DecompressPointer r1
    //     0x14fd06c: add             x1, x1, HEAP, lsl #32
    // 0x14fd070: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14fd070: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14fd074: r0 = toList()
    //     0x14fd074: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14fd078: ldur            x2, [fp, #-8]
    // 0x14fd07c: stur            x0, [fp, #-0x28]
    // 0x14fd080: LoadField: r1 = r2->field_f
    //     0x14fd080: ldur            w1, [x2, #0xf]
    // 0x14fd084: DecompressPointer r1
    //     0x14fd084: add             x1, x1, HEAP, lsl #32
    // 0x14fd088: r0 = controller()
    //     0x14fd088: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fd08c: LoadField: r1 = r0->field_5b
    //     0x14fd08c: ldur            w1, [x0, #0x5b]
    // 0x14fd090: DecompressPointer r1
    //     0x14fd090: add             x1, x1, HEAP, lsl #32
    // 0x14fd094: r0 = value()
    //     0x14fd094: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14fd098: ldur            x2, [fp, #-8]
    // 0x14fd09c: r1 = Function '<anonymous closure>':.
    //     0x14fd09c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8b0] AnonymousClosure: (0x13b3d00), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14fd0a0: ldr             x1, [x1, #0x8b0]
    // 0x14fd0a4: stur            x0, [fp, #-0x30]
    // 0x14fd0a8: r0 = AllocateClosure()
    //     0x14fd0a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fd0ac: ldur            x2, [fp, #-8]
    // 0x14fd0b0: r1 = Function '<anonymous closure>':.
    //     0x14fd0b0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8b8] AnonymousClosure: (0x13b3290), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14fd0b4: ldr             x1, [x1, #0x8b8]
    // 0x14fd0b8: stur            x0, [fp, #-0x38]
    // 0x14fd0bc: r0 = AllocateClosure()
    //     0x14fd0bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fd0c0: ldur            x2, [fp, #-8]
    // 0x14fd0c4: r1 = Function '<anonymous closure>':.
    //     0x14fd0c4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8c0] AnonymousClosure: (0x13b3208), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14fd0c8: ldr             x1, [x1, #0x8c0]
    // 0x14fd0cc: stur            x0, [fp, #-0x40]
    // 0x14fd0d0: r0 = AllocateClosure()
    //     0x14fd0d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fd0d4: ldur            x2, [fp, #-8]
    // 0x14fd0d8: r1 = Function '<anonymous closure>':.
    //     0x14fd0d8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8c8] AnonymousClosure: (0x13b3058), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14fd0dc: ldr             x1, [x1, #0x8c8]
    // 0x14fd0e0: stur            x0, [fp, #-0x48]
    // 0x14fd0e4: r0 = AllocateClosure()
    //     0x14fd0e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fd0e8: ldur            x2, [fp, #-8]
    // 0x14fd0ec: r1 = Function '<anonymous closure>':.
    //     0x14fd0ec: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8d0] AnonymousClosure: (0x13b2fc4), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14fd0f0: ldr             x1, [x1, #0x8d0]
    // 0x14fd0f4: stur            x0, [fp, #-0x50]
    // 0x14fd0f8: r0 = AllocateClosure()
    //     0x14fd0f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fd0fc: ldur            x2, [fp, #-8]
    // 0x14fd100: r1 = Function '<anonymous closure>':.
    //     0x14fd100: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8d8] AnonymousClosure: (0x13b2cdc), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14fd104: ldr             x1, [x1, #0x8d8]
    // 0x14fd108: stur            x0, [fp, #-0x58]
    // 0x14fd10c: r0 = AllocateClosure()
    //     0x14fd10c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fd110: ldur            x2, [fp, #-8]
    // 0x14fd114: r1 = Function '<anonymous closure>':.
    //     0x14fd114: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8e0] AnonymousClosure: (0x14fd274), in [package:customer_app/app/presentation/views/glass/search/search_page.dart] SearchPage::body (0x14fce78)
    //     0x14fd118: ldr             x1, [x1, #0x8e0]
    // 0x14fd11c: stur            x0, [fp, #-0x60]
    // 0x14fd120: r0 = AllocateClosure()
    //     0x14fd120: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fd124: ldur            x2, [fp, #-8]
    // 0x14fd128: r1 = Function '<anonymous closure>':.
    //     0x14fd128: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8e8] AnonymousClosure: (0x13b276c), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14fd12c: ldr             x1, [x1, #0x8e8]
    // 0x14fd130: stur            x0, [fp, #-0x68]
    // 0x14fd134: r0 = AllocateClosure()
    //     0x14fd134: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fd138: ldur            x2, [fp, #-8]
    // 0x14fd13c: r1 = Function '<anonymous closure>':.
    //     0x14fd13c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8f0] AnonymousClosure: (0x13b0b1c), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14fd140: ldr             x1, [x1, #0x8f0]
    // 0x14fd144: stur            x0, [fp, #-0x70]
    // 0x14fd148: r0 = AllocateClosure()
    //     0x14fd148: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fd14c: ldur            x2, [fp, #-8]
    // 0x14fd150: r1 = Function '<anonymous closure>':.
    //     0x14fd150: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8f8] AnonymousClosure: (0x13b1124), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14fd154: ldr             x1, [x1, #0x8f8]
    // 0x14fd158: stur            x0, [fp, #-8]
    // 0x14fd15c: r0 = AllocateClosure()
    //     0x14fd15c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fd160: stur            x0, [fp, #-0x78]
    // 0x14fd164: r0 = SearchView()
    //     0x14fd164: bl              #0x14fd268  ; AllocateSearchViewStub -> SearchView (size=0x48)
    // 0x14fd168: stur            x0, [fp, #-0x80]
    // 0x14fd16c: ldur            x16, [fp, #-0x28]
    // 0x14fd170: ldur            lr, [fp, #-0x78]
    // 0x14fd174: stp             lr, x16, [SP, #0x40]
    // 0x14fd178: ldur            x16, [fp, #-0x48]
    // 0x14fd17c: ldur            lr, [fp, #-0x40]
    // 0x14fd180: stp             lr, x16, [SP, #0x30]
    // 0x14fd184: ldur            x16, [fp, #-0x38]
    // 0x14fd188: ldur            lr, [fp, #-8]
    // 0x14fd18c: stp             lr, x16, [SP, #0x20]
    // 0x14fd190: ldur            x16, [fp, #-0x50]
    // 0x14fd194: ldur            lr, [fp, #-0x68]
    // 0x14fd198: stp             lr, x16, [SP, #0x10]
    // 0x14fd19c: ldur            x16, [fp, #-0x30]
    // 0x14fd1a0: ldur            lr, [fp, #-0x70]
    // 0x14fd1a4: stp             lr, x16, [SP]
    // 0x14fd1a8: mov             x1, x0
    // 0x14fd1ac: ldur            x2, [fp, #-0x10]
    // 0x14fd1b0: ldur            x3, [fp, #-0x18]
    // 0x14fd1b4: ldur            x5, [fp, #-0x60]
    // 0x14fd1b8: ldur            x6, [fp, #-0x20]
    // 0x14fd1bc: ldur            x7, [fp, #-0x58]
    // 0x14fd1c0: r0 = SearchView()
    //     0x14fd1c0: bl              #0x1479b30  ; [package:customer_app/app/presentation/views/basic/search/widgets/search_view.dart] SearchView::SearchView
    // 0x14fd1c4: r1 = Null
    //     0x14fd1c4: mov             x1, NULL
    // 0x14fd1c8: r2 = 2
    //     0x14fd1c8: movz            x2, #0x2
    // 0x14fd1cc: r0 = AllocateArray()
    //     0x14fd1cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14fd1d0: mov             x2, x0
    // 0x14fd1d4: ldur            x0, [fp, #-0x80]
    // 0x14fd1d8: stur            x2, [fp, #-8]
    // 0x14fd1dc: StoreField: r2->field_f = r0
    //     0x14fd1dc: stur            w0, [x2, #0xf]
    // 0x14fd1e0: r1 = <Widget>
    //     0x14fd1e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14fd1e4: r0 = AllocateGrowableArray()
    //     0x14fd1e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14fd1e8: mov             x1, x0
    // 0x14fd1ec: ldur            x0, [fp, #-8]
    // 0x14fd1f0: stur            x1, [fp, #-0x10]
    // 0x14fd1f4: StoreField: r1->field_f = r0
    //     0x14fd1f4: stur            w0, [x1, #0xf]
    // 0x14fd1f8: r0 = 2
    //     0x14fd1f8: movz            x0, #0x2
    // 0x14fd1fc: StoreField: r1->field_b = r0
    //     0x14fd1fc: stur            w0, [x1, #0xb]
    // 0x14fd200: r0 = Column()
    //     0x14fd200: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14fd204: r1 = Instance_Axis
    //     0x14fd204: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14fd208: StoreField: r0->field_f = r1
    //     0x14fd208: stur            w1, [x0, #0xf]
    // 0x14fd20c: r1 = Instance_MainAxisAlignment
    //     0x14fd20c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14fd210: ldr             x1, [x1, #0xa08]
    // 0x14fd214: StoreField: r0->field_13 = r1
    //     0x14fd214: stur            w1, [x0, #0x13]
    // 0x14fd218: r1 = Instance_MainAxisSize
    //     0x14fd218: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14fd21c: ldr             x1, [x1, #0xa10]
    // 0x14fd220: ArrayStore: r0[0] = r1  ; List_4
    //     0x14fd220: stur            w1, [x0, #0x17]
    // 0x14fd224: r1 = Instance_CrossAxisAlignment
    //     0x14fd224: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14fd228: ldr             x1, [x1, #0xa18]
    // 0x14fd22c: StoreField: r0->field_1b = r1
    //     0x14fd22c: stur            w1, [x0, #0x1b]
    // 0x14fd230: r1 = Instance_VerticalDirection
    //     0x14fd230: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14fd234: ldr             x1, [x1, #0xa20]
    // 0x14fd238: StoreField: r0->field_23 = r1
    //     0x14fd238: stur            w1, [x0, #0x23]
    // 0x14fd23c: r1 = Instance_Clip
    //     0x14fd23c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14fd240: ldr             x1, [x1, #0x38]
    // 0x14fd244: StoreField: r0->field_2b = r1
    //     0x14fd244: stur            w1, [x0, #0x2b]
    // 0x14fd248: StoreField: r0->field_2f = rZR
    //     0x14fd248: stur            xzr, [x0, #0x2f]
    // 0x14fd24c: ldur            x1, [fp, #-0x10]
    // 0x14fd250: StoreField: r0->field_b = r1
    //     0x14fd250: stur            w1, [x0, #0xb]
    // 0x14fd254: LeaveFrame
    //     0x14fd254: mov             SP, fp
    //     0x14fd258: ldp             fp, lr, [SP], #0x10
    // 0x14fd25c: ret
    //     0x14fd25c: ret             
    // 0x14fd260: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fd260: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fd264: b               #0x14fcfbc
  }
  [closure] Null <anonymous closure>(dynamic, dynamic, dynamic, dynamic, dynamic, dynamic) {
    // ** addr: 0x14fd274, size: 0x180
    // 0x14fd274: EnterFrame
    //     0x14fd274: stp             fp, lr, [SP, #-0x10]!
    //     0x14fd278: mov             fp, SP
    // 0x14fd27c: AllocStack(0x10)
    //     0x14fd27c: sub             SP, SP, #0x10
    // 0x14fd280: SetupParameters()
    //     0x14fd280: ldr             x0, [fp, #0x38]
    //     0x14fd284: ldur            w1, [x0, #0x17]
    //     0x14fd288: add             x1, x1, HEAP, lsl #32
    // 0x14fd28c: CheckStackOverflow
    //     0x14fd28c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fd290: cmp             SP, x16
    //     0x14fd294: b.ls            #0x14fd3ec
    // 0x14fd298: LoadField: r0 = r1->field_f
    //     0x14fd298: ldur            w0, [x1, #0xf]
    // 0x14fd29c: DecompressPointer r0
    //     0x14fd29c: add             x0, x0, HEAP, lsl #32
    // 0x14fd2a0: mov             x1, x0
    // 0x14fd2a4: r0 = controller()
    //     0x14fd2a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fd2a8: mov             x3, x0
    // 0x14fd2ac: ldr             x0, [fp, #0x28]
    // 0x14fd2b0: r2 = Null
    //     0x14fd2b0: mov             x2, NULL
    // 0x14fd2b4: r1 = Null
    //     0x14fd2b4: mov             x1, NULL
    // 0x14fd2b8: stur            x3, [fp, #-8]
    // 0x14fd2bc: r4 = 60
    //     0x14fd2bc: movz            x4, #0x3c
    // 0x14fd2c0: branchIfSmi(r0, 0x14fd2cc)
    //     0x14fd2c0: tbz             w0, #0, #0x14fd2cc
    // 0x14fd2c4: r4 = LoadClassIdInstr(r0)
    //     0x14fd2c4: ldur            x4, [x0, #-1]
    //     0x14fd2c8: ubfx            x4, x4, #0xc, #0x14
    // 0x14fd2cc: sub             x4, x4, #0x5e
    // 0x14fd2d0: cmp             x4, #1
    // 0x14fd2d4: b.ls            #0x14fd2e8
    // 0x14fd2d8: r8 = String
    //     0x14fd2d8: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x14fd2dc: r3 = Null
    //     0x14fd2dc: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e900] Null
    //     0x14fd2e0: ldr             x3, [x3, #0x900]
    // 0x14fd2e4: r0 = String()
    //     0x14fd2e4: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x14fd2e8: ldr             x0, [fp, #0x20]
    // 0x14fd2ec: r2 = Null
    //     0x14fd2ec: mov             x2, NULL
    // 0x14fd2f0: r1 = Null
    //     0x14fd2f0: mov             x1, NULL
    // 0x14fd2f4: r4 = 60
    //     0x14fd2f4: movz            x4, #0x3c
    // 0x14fd2f8: branchIfSmi(r0, 0x14fd304)
    //     0x14fd2f8: tbz             w0, #0, #0x14fd304
    // 0x14fd2fc: r4 = LoadClassIdInstr(r0)
    //     0x14fd2fc: ldur            x4, [x0, #-1]
    //     0x14fd300: ubfx            x4, x4, #0xc, #0x14
    // 0x14fd304: sub             x4, x4, #0x5e
    // 0x14fd308: cmp             x4, #1
    // 0x14fd30c: b.ls            #0x14fd320
    // 0x14fd310: r8 = String
    //     0x14fd310: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x14fd314: r3 = Null
    //     0x14fd314: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e910] Null
    //     0x14fd318: ldr             x3, [x3, #0x910]
    // 0x14fd31c: r0 = String()
    //     0x14fd31c: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x14fd320: ldr             x0, [fp, #0x18]
    // 0x14fd324: r2 = Null
    //     0x14fd324: mov             x2, NULL
    // 0x14fd328: r1 = Null
    //     0x14fd328: mov             x1, NULL
    // 0x14fd32c: branchIfSmi(r0, 0x14fd354)
    //     0x14fd32c: tbz             w0, #0, #0x14fd354
    // 0x14fd330: r4 = LoadClassIdInstr(r0)
    //     0x14fd330: ldur            x4, [x0, #-1]
    //     0x14fd334: ubfx            x4, x4, #0xc, #0x14
    // 0x14fd338: sub             x4, x4, #0x3c
    // 0x14fd33c: cmp             x4, #1
    // 0x14fd340: b.ls            #0x14fd354
    // 0x14fd344: r8 = int
    //     0x14fd344: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x14fd348: r3 = Null
    //     0x14fd348: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e920] Null
    //     0x14fd34c: ldr             x3, [x3, #0x920]
    // 0x14fd350: r0 = int()
    //     0x14fd350: bl              #0x16fc548  ; IsType_int_Stub
    // 0x14fd354: ldr             x0, [fp, #0x10]
    // 0x14fd358: cmp             w0, NULL
    // 0x14fd35c: b.ne            #0x14fd36c
    // 0x14fd360: r0 = ProductRating()
    //     0x14fd360: bl              #0x911a74  ; AllocateProductRatingStub -> ProductRating (size=0x18)
    // 0x14fd364: mov             x4, x0
    // 0x14fd368: b               #0x14fd370
    // 0x14fd36c: mov             x4, x0
    // 0x14fd370: ldr             x3, [fp, #0x18]
    // 0x14fd374: mov             x0, x4
    // 0x14fd378: stur            x4, [fp, #-0x10]
    // 0x14fd37c: r2 = Null
    //     0x14fd37c: mov             x2, NULL
    // 0x14fd380: r1 = Null
    //     0x14fd380: mov             x1, NULL
    // 0x14fd384: r4 = 60
    //     0x14fd384: movz            x4, #0x3c
    // 0x14fd388: branchIfSmi(r0, 0x14fd394)
    //     0x14fd388: tbz             w0, #0, #0x14fd394
    // 0x14fd38c: r4 = LoadClassIdInstr(r0)
    //     0x14fd38c: ldur            x4, [x0, #-1]
    //     0x14fd390: ubfx            x4, x4, #0xc, #0x14
    // 0x14fd394: r17 = 5178
    //     0x14fd394: movz            x17, #0x143a
    // 0x14fd398: cmp             x4, x17
    // 0x14fd39c: b.eq            #0x14fd3b4
    // 0x14fd3a0: r8 = ProductRating
    //     0x14fd3a0: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ee30] Type: ProductRating
    //     0x14fd3a4: ldr             x8, [x8, #0xe30]
    // 0x14fd3a8: r3 = Null
    //     0x14fd3a8: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e930] Null
    //     0x14fd3ac: ldr             x3, [x3, #0x930]
    // 0x14fd3b0: r0 = DefaultTypeTest()
    //     0x14fd3b0: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x14fd3b4: ldr             x0, [fp, #0x18]
    // 0x14fd3b8: r6 = LoadInt32Instr(r0)
    //     0x14fd3b8: sbfx            x6, x0, #1, #0x1f
    //     0x14fd3bc: tbz             w0, #0, #0x14fd3c4
    //     0x14fd3c0: ldur            x6, [x0, #7]
    // 0x14fd3c4: ldur            x1, [fp, #-8]
    // 0x14fd3c8: ldr             x2, [fp, #0x30]
    // 0x14fd3cc: ldr             x3, [fp, #0x28]
    // 0x14fd3d0: ldr             x5, [fp, #0x20]
    // 0x14fd3d4: ldur            x7, [fp, #-0x10]
    // 0x14fd3d8: r0 = productViewedEvent()
    //     0x14fd3d8: bl              #0x13b2a20  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::productViewedEvent
    // 0x14fd3dc: r0 = Null
    //     0x14fd3dc: mov             x0, NULL
    // 0x14fd3e0: LeaveFrame
    //     0x14fd3e0: mov             SP, fp
    //     0x14fd3e4: ldp             fp, lr, [SP], #0x10
    // 0x14fd3e8: ret
    //     0x14fd3e8: ret             
    // 0x14fd3ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fd3ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fd3f0: b               #0x14fd298
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0x15d7564, size: 0x45c
    // 0x15d7564: EnterFrame
    //     0x15d7564: stp             fp, lr, [SP, #-0x10]!
    //     0x15d7568: mov             fp, SP
    // 0x15d756c: AllocStack(0x48)
    //     0x15d756c: sub             SP, SP, #0x48
    // 0x15d7570: SetupParameters()
    //     0x15d7570: ldr             x0, [fp, #0x10]
    //     0x15d7574: ldur            w2, [x0, #0x17]
    //     0x15d7578: add             x2, x2, HEAP, lsl #32
    //     0x15d757c: stur            x2, [fp, #-8]
    // 0x15d7580: CheckStackOverflow
    //     0x15d7580: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d7584: cmp             SP, x16
    //     0x15d7588: b.ls            #0x15d79b8
    // 0x15d758c: LoadField: r1 = r2->field_f
    //     0x15d758c: ldur            w1, [x2, #0xf]
    // 0x15d7590: DecompressPointer r1
    //     0x15d7590: add             x1, x1, HEAP, lsl #32
    // 0x15d7594: r0 = controller()
    //     0x15d7594: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d7598: LoadField: r1 = r0->field_67
    //     0x15d7598: ldur            w1, [x0, #0x67]
    // 0x15d759c: DecompressPointer r1
    //     0x15d759c: add             x1, x1, HEAP, lsl #32
    // 0x15d75a0: r0 = value()
    //     0x15d75a0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d75a4: LoadField: r1 = r0->field_3f
    //     0x15d75a4: ldur            w1, [x0, #0x3f]
    // 0x15d75a8: DecompressPointer r1
    //     0x15d75a8: add             x1, x1, HEAP, lsl #32
    // 0x15d75ac: cmp             w1, NULL
    // 0x15d75b0: b.ne            #0x15d75bc
    // 0x15d75b4: r0 = Null
    //     0x15d75b4: mov             x0, NULL
    // 0x15d75b8: b               #0x15d75c4
    // 0x15d75bc: LoadField: r0 = r1->field_f
    //     0x15d75bc: ldur            w0, [x1, #0xf]
    // 0x15d75c0: DecompressPointer r0
    //     0x15d75c0: add             x0, x0, HEAP, lsl #32
    // 0x15d75c4: r1 = LoadClassIdInstr(r0)
    //     0x15d75c4: ldur            x1, [x0, #-1]
    //     0x15d75c8: ubfx            x1, x1, #0xc, #0x14
    // 0x15d75cc: r16 = "image_text"
    //     0x15d75cc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15d75d0: ldr             x16, [x16, #0xa88]
    // 0x15d75d4: stp             x16, x0, [SP]
    // 0x15d75d8: mov             x0, x1
    // 0x15d75dc: mov             lr, x0
    // 0x15d75e0: ldr             lr, [x21, lr, lsl #3]
    // 0x15d75e4: blr             lr
    // 0x15d75e8: tbnz            w0, #4, #0x15d75f4
    // 0x15d75ec: r2 = true
    //     0x15d75ec: add             x2, NULL, #0x20  ; true
    // 0x15d75f0: b               #0x15d7654
    // 0x15d75f4: ldur            x0, [fp, #-8]
    // 0x15d75f8: LoadField: r1 = r0->field_f
    //     0x15d75f8: ldur            w1, [x0, #0xf]
    // 0x15d75fc: DecompressPointer r1
    //     0x15d75fc: add             x1, x1, HEAP, lsl #32
    // 0x15d7600: r0 = controller()
    //     0x15d7600: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d7604: LoadField: r1 = r0->field_67
    //     0x15d7604: ldur            w1, [x0, #0x67]
    // 0x15d7608: DecompressPointer r1
    //     0x15d7608: add             x1, x1, HEAP, lsl #32
    // 0x15d760c: r0 = value()
    //     0x15d760c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d7610: LoadField: r1 = r0->field_3f
    //     0x15d7610: ldur            w1, [x0, #0x3f]
    // 0x15d7614: DecompressPointer r1
    //     0x15d7614: add             x1, x1, HEAP, lsl #32
    // 0x15d7618: cmp             w1, NULL
    // 0x15d761c: b.ne            #0x15d7628
    // 0x15d7620: r0 = Null
    //     0x15d7620: mov             x0, NULL
    // 0x15d7624: b               #0x15d7630
    // 0x15d7628: LoadField: r0 = r1->field_f
    //     0x15d7628: ldur            w0, [x1, #0xf]
    // 0x15d762c: DecompressPointer r0
    //     0x15d762c: add             x0, x0, HEAP, lsl #32
    // 0x15d7630: r1 = LoadClassIdInstr(r0)
    //     0x15d7630: ldur            x1, [x0, #-1]
    //     0x15d7634: ubfx            x1, x1, #0xc, #0x14
    // 0x15d7638: r16 = "image"
    //     0x15d7638: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x15d763c: stp             x16, x0, [SP]
    // 0x15d7640: mov             x0, x1
    // 0x15d7644: mov             lr, x0
    // 0x15d7648: ldr             lr, [x21, lr, lsl #3]
    // 0x15d764c: blr             lr
    // 0x15d7650: mov             x2, x0
    // 0x15d7654: ldur            x0, [fp, #-8]
    // 0x15d7658: stur            x2, [fp, #-0x10]
    // 0x15d765c: LoadField: r1 = r0->field_f
    //     0x15d765c: ldur            w1, [x0, #0xf]
    // 0x15d7660: DecompressPointer r1
    //     0x15d7660: add             x1, x1, HEAP, lsl #32
    // 0x15d7664: r0 = controller()
    //     0x15d7664: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d7668: LoadField: r1 = r0->field_67
    //     0x15d7668: ldur            w1, [x0, #0x67]
    // 0x15d766c: DecompressPointer r1
    //     0x15d766c: add             x1, x1, HEAP, lsl #32
    // 0x15d7670: r0 = value()
    //     0x15d7670: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d7674: LoadField: r1 = r0->field_27
    //     0x15d7674: ldur            w1, [x0, #0x27]
    // 0x15d7678: DecompressPointer r1
    //     0x15d7678: add             x1, x1, HEAP, lsl #32
    // 0x15d767c: cmp             w1, NULL
    // 0x15d7680: b.ne            #0x15d768c
    // 0x15d7684: r2 = ""
    //     0x15d7684: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15d7688: b               #0x15d7690
    // 0x15d768c: mov             x2, x1
    // 0x15d7690: ldur            x0, [fp, #-8]
    // 0x15d7694: ldur            x1, [fp, #-0x10]
    // 0x15d7698: stur            x2, [fp, #-0x18]
    // 0x15d769c: r0 = ImageHeaders.forImages()
    //     0x15d769c: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x15d76a0: stur            x0, [fp, #-0x20]
    // 0x15d76a4: r0 = CachedNetworkImage()
    //     0x15d76a4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15d76a8: stur            x0, [fp, #-0x28]
    // 0x15d76ac: ldur            x16, [fp, #-0x20]
    // 0x15d76b0: r30 = Instance_BoxFit
    //     0x15d76b0: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15d76b4: ldr             lr, [lr, #0xb18]
    // 0x15d76b8: stp             lr, x16, [SP, #0x10]
    // 0x15d76bc: r16 = 50.000000
    //     0x15d76bc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15d76c0: ldr             x16, [x16, #0xa90]
    // 0x15d76c4: r30 = 50.000000
    //     0x15d76c4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15d76c8: ldr             lr, [lr, #0xa90]
    // 0x15d76cc: stp             lr, x16, [SP]
    // 0x15d76d0: mov             x1, x0
    // 0x15d76d4: ldur            x2, [fp, #-0x18]
    // 0x15d76d8: r4 = const [0, 0x6, 0x4, 0x2, fit, 0x3, height, 0x4, httpHeaders, 0x2, width, 0x5, null]
    //     0x15d76d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea98] List(13) [0, 0x6, 0x4, 0x2, "fit", 0x3, "height", 0x4, "httpHeaders", 0x2, "width", 0x5, Null]
    //     0x15d76dc: ldr             x4, [x4, #0xa98]
    // 0x15d76e0: r0 = CachedNetworkImage()
    //     0x15d76e0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15d76e4: r0 = Visibility()
    //     0x15d76e4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15d76e8: mov             x2, x0
    // 0x15d76ec: ldur            x0, [fp, #-0x28]
    // 0x15d76f0: stur            x2, [fp, #-0x18]
    // 0x15d76f4: StoreField: r2->field_b = r0
    //     0x15d76f4: stur            w0, [x2, #0xb]
    // 0x15d76f8: r0 = Instance_SizedBox
    //     0x15d76f8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15d76fc: StoreField: r2->field_f = r0
    //     0x15d76fc: stur            w0, [x2, #0xf]
    // 0x15d7700: ldur            x1, [fp, #-0x10]
    // 0x15d7704: StoreField: r2->field_13 = r1
    //     0x15d7704: stur            w1, [x2, #0x13]
    // 0x15d7708: r3 = false
    //     0x15d7708: add             x3, NULL, #0x30  ; false
    // 0x15d770c: ArrayStore: r2[0] = r3  ; List_4
    //     0x15d770c: stur            w3, [x2, #0x17]
    // 0x15d7710: StoreField: r2->field_1b = r3
    //     0x15d7710: stur            w3, [x2, #0x1b]
    // 0x15d7714: StoreField: r2->field_1f = r3
    //     0x15d7714: stur            w3, [x2, #0x1f]
    // 0x15d7718: StoreField: r2->field_23 = r3
    //     0x15d7718: stur            w3, [x2, #0x23]
    // 0x15d771c: StoreField: r2->field_27 = r3
    //     0x15d771c: stur            w3, [x2, #0x27]
    // 0x15d7720: StoreField: r2->field_2b = r3
    //     0x15d7720: stur            w3, [x2, #0x2b]
    // 0x15d7724: ldur            x4, [fp, #-8]
    // 0x15d7728: LoadField: r1 = r4->field_f
    //     0x15d7728: ldur            w1, [x4, #0xf]
    // 0x15d772c: DecompressPointer r1
    //     0x15d772c: add             x1, x1, HEAP, lsl #32
    // 0x15d7730: r0 = controller()
    //     0x15d7730: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d7734: LoadField: r1 = r0->field_67
    //     0x15d7734: ldur            w1, [x0, #0x67]
    // 0x15d7738: DecompressPointer r1
    //     0x15d7738: add             x1, x1, HEAP, lsl #32
    // 0x15d773c: r0 = value()
    //     0x15d773c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d7740: LoadField: r1 = r0->field_3f
    //     0x15d7740: ldur            w1, [x0, #0x3f]
    // 0x15d7744: DecompressPointer r1
    //     0x15d7744: add             x1, x1, HEAP, lsl #32
    // 0x15d7748: cmp             w1, NULL
    // 0x15d774c: b.ne            #0x15d7758
    // 0x15d7750: r0 = Null
    //     0x15d7750: mov             x0, NULL
    // 0x15d7754: b               #0x15d7760
    // 0x15d7758: LoadField: r0 = r1->field_f
    //     0x15d7758: ldur            w0, [x1, #0xf]
    // 0x15d775c: DecompressPointer r0
    //     0x15d775c: add             x0, x0, HEAP, lsl #32
    // 0x15d7760: r1 = LoadClassIdInstr(r0)
    //     0x15d7760: ldur            x1, [x0, #-1]
    //     0x15d7764: ubfx            x1, x1, #0xc, #0x14
    // 0x15d7768: r16 = "image_text"
    //     0x15d7768: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15d776c: ldr             x16, [x16, #0xa88]
    // 0x15d7770: stp             x16, x0, [SP]
    // 0x15d7774: mov             x0, x1
    // 0x15d7778: mov             lr, x0
    // 0x15d777c: ldr             lr, [x21, lr, lsl #3]
    // 0x15d7780: blr             lr
    // 0x15d7784: tbnz            w0, #4, #0x15d7790
    // 0x15d7788: r2 = true
    //     0x15d7788: add             x2, NULL, #0x20  ; true
    // 0x15d778c: b               #0x15d77f0
    // 0x15d7790: ldur            x0, [fp, #-8]
    // 0x15d7794: LoadField: r1 = r0->field_f
    //     0x15d7794: ldur            w1, [x0, #0xf]
    // 0x15d7798: DecompressPointer r1
    //     0x15d7798: add             x1, x1, HEAP, lsl #32
    // 0x15d779c: r0 = controller()
    //     0x15d779c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d77a0: LoadField: r1 = r0->field_67
    //     0x15d77a0: ldur            w1, [x0, #0x67]
    // 0x15d77a4: DecompressPointer r1
    //     0x15d77a4: add             x1, x1, HEAP, lsl #32
    // 0x15d77a8: r0 = value()
    //     0x15d77a8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d77ac: LoadField: r1 = r0->field_3f
    //     0x15d77ac: ldur            w1, [x0, #0x3f]
    // 0x15d77b0: DecompressPointer r1
    //     0x15d77b0: add             x1, x1, HEAP, lsl #32
    // 0x15d77b4: cmp             w1, NULL
    // 0x15d77b8: b.ne            #0x15d77c4
    // 0x15d77bc: r0 = Null
    //     0x15d77bc: mov             x0, NULL
    // 0x15d77c0: b               #0x15d77cc
    // 0x15d77c4: LoadField: r0 = r1->field_f
    //     0x15d77c4: ldur            w0, [x1, #0xf]
    // 0x15d77c8: DecompressPointer r0
    //     0x15d77c8: add             x0, x0, HEAP, lsl #32
    // 0x15d77cc: r1 = LoadClassIdInstr(r0)
    //     0x15d77cc: ldur            x1, [x0, #-1]
    //     0x15d77d0: ubfx            x1, x1, #0xc, #0x14
    // 0x15d77d4: r16 = "text"
    //     0x15d77d4: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15d77d8: stp             x16, x0, [SP]
    // 0x15d77dc: mov             x0, x1
    // 0x15d77e0: mov             lr, x0
    // 0x15d77e4: ldr             lr, [x21, lr, lsl #3]
    // 0x15d77e8: blr             lr
    // 0x15d77ec: mov             x2, x0
    // 0x15d77f0: ldur            x0, [fp, #-8]
    // 0x15d77f4: stur            x2, [fp, #-0x10]
    // 0x15d77f8: LoadField: r1 = r0->field_f
    //     0x15d77f8: ldur            w1, [x0, #0xf]
    // 0x15d77fc: DecompressPointer r1
    //     0x15d77fc: add             x1, x1, HEAP, lsl #32
    // 0x15d7800: r0 = controller()
    //     0x15d7800: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d7804: LoadField: r1 = r0->field_67
    //     0x15d7804: ldur            w1, [x0, #0x67]
    // 0x15d7808: DecompressPointer r1
    //     0x15d7808: add             x1, x1, HEAP, lsl #32
    // 0x15d780c: r0 = value()
    //     0x15d780c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d7810: LoadField: r1 = r0->field_2b
    //     0x15d7810: ldur            w1, [x0, #0x2b]
    // 0x15d7814: DecompressPointer r1
    //     0x15d7814: add             x1, x1, HEAP, lsl #32
    // 0x15d7818: cmp             w1, NULL
    // 0x15d781c: b.ne            #0x15d7828
    // 0x15d7820: r4 = ""
    //     0x15d7820: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15d7824: b               #0x15d782c
    // 0x15d7828: mov             x4, x1
    // 0x15d782c: ldur            x0, [fp, #-8]
    // 0x15d7830: ldur            x3, [fp, #-0x18]
    // 0x15d7834: ldur            x2, [fp, #-0x10]
    // 0x15d7838: stur            x4, [fp, #-0x20]
    // 0x15d783c: LoadField: r1 = r0->field_13
    //     0x15d783c: ldur            w1, [x0, #0x13]
    // 0x15d7840: DecompressPointer r1
    //     0x15d7840: add             x1, x1, HEAP, lsl #32
    // 0x15d7844: r0 = of()
    //     0x15d7844: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d7848: LoadField: r1 = r0->field_87
    //     0x15d7848: ldur            w1, [x0, #0x87]
    // 0x15d784c: DecompressPointer r1
    //     0x15d784c: add             x1, x1, HEAP, lsl #32
    // 0x15d7850: LoadField: r0 = r1->field_2b
    //     0x15d7850: ldur            w0, [x1, #0x2b]
    // 0x15d7854: DecompressPointer r0
    //     0x15d7854: add             x0, x0, HEAP, lsl #32
    // 0x15d7858: r16 = 16.000000
    //     0x15d7858: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15d785c: ldr             x16, [x16, #0x188]
    // 0x15d7860: r30 = Instance_Color
    //     0x15d7860: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15d7864: stp             lr, x16, [SP]
    // 0x15d7868: mov             x1, x0
    // 0x15d786c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15d786c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15d7870: ldr             x4, [x4, #0xaa0]
    // 0x15d7874: r0 = copyWith()
    //     0x15d7874: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15d7878: stur            x0, [fp, #-8]
    // 0x15d787c: r0 = Text()
    //     0x15d787c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15d7880: mov             x2, x0
    // 0x15d7884: ldur            x0, [fp, #-0x20]
    // 0x15d7888: stur            x2, [fp, #-0x28]
    // 0x15d788c: StoreField: r2->field_b = r0
    //     0x15d788c: stur            w0, [x2, #0xb]
    // 0x15d7890: ldur            x0, [fp, #-8]
    // 0x15d7894: StoreField: r2->field_13 = r0
    //     0x15d7894: stur            w0, [x2, #0x13]
    // 0x15d7898: r1 = <FlexParentData>
    //     0x15d7898: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x15d789c: ldr             x1, [x1, #0xe00]
    // 0x15d78a0: r0 = Expanded()
    //     0x15d78a0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x15d78a4: mov             x1, x0
    // 0x15d78a8: r0 = 1
    //     0x15d78a8: movz            x0, #0x1
    // 0x15d78ac: stur            x1, [fp, #-8]
    // 0x15d78b0: StoreField: r1->field_13 = r0
    //     0x15d78b0: stur            x0, [x1, #0x13]
    // 0x15d78b4: r0 = Instance_FlexFit
    //     0x15d78b4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x15d78b8: ldr             x0, [x0, #0xe08]
    // 0x15d78bc: StoreField: r1->field_1b = r0
    //     0x15d78bc: stur            w0, [x1, #0x1b]
    // 0x15d78c0: ldur            x0, [fp, #-0x28]
    // 0x15d78c4: StoreField: r1->field_b = r0
    //     0x15d78c4: stur            w0, [x1, #0xb]
    // 0x15d78c8: r0 = Visibility()
    //     0x15d78c8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15d78cc: mov             x3, x0
    // 0x15d78d0: ldur            x0, [fp, #-8]
    // 0x15d78d4: stur            x3, [fp, #-0x20]
    // 0x15d78d8: StoreField: r3->field_b = r0
    //     0x15d78d8: stur            w0, [x3, #0xb]
    // 0x15d78dc: r0 = Instance_SizedBox
    //     0x15d78dc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15d78e0: StoreField: r3->field_f = r0
    //     0x15d78e0: stur            w0, [x3, #0xf]
    // 0x15d78e4: ldur            x0, [fp, #-0x10]
    // 0x15d78e8: StoreField: r3->field_13 = r0
    //     0x15d78e8: stur            w0, [x3, #0x13]
    // 0x15d78ec: r0 = false
    //     0x15d78ec: add             x0, NULL, #0x30  ; false
    // 0x15d78f0: ArrayStore: r3[0] = r0  ; List_4
    //     0x15d78f0: stur            w0, [x3, #0x17]
    // 0x15d78f4: StoreField: r3->field_1b = r0
    //     0x15d78f4: stur            w0, [x3, #0x1b]
    // 0x15d78f8: StoreField: r3->field_1f = r0
    //     0x15d78f8: stur            w0, [x3, #0x1f]
    // 0x15d78fc: StoreField: r3->field_23 = r0
    //     0x15d78fc: stur            w0, [x3, #0x23]
    // 0x15d7900: StoreField: r3->field_27 = r0
    //     0x15d7900: stur            w0, [x3, #0x27]
    // 0x15d7904: StoreField: r3->field_2b = r0
    //     0x15d7904: stur            w0, [x3, #0x2b]
    // 0x15d7908: r1 = Null
    //     0x15d7908: mov             x1, NULL
    // 0x15d790c: r2 = 6
    //     0x15d790c: movz            x2, #0x6
    // 0x15d7910: r0 = AllocateArray()
    //     0x15d7910: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15d7914: mov             x2, x0
    // 0x15d7918: ldur            x0, [fp, #-0x18]
    // 0x15d791c: stur            x2, [fp, #-8]
    // 0x15d7920: StoreField: r2->field_f = r0
    //     0x15d7920: stur            w0, [x2, #0xf]
    // 0x15d7924: r16 = Instance_SizedBox
    //     0x15d7924: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x15d7928: ldr             x16, [x16, #0xaa8]
    // 0x15d792c: StoreField: r2->field_13 = r16
    //     0x15d792c: stur            w16, [x2, #0x13]
    // 0x15d7930: ldur            x0, [fp, #-0x20]
    // 0x15d7934: ArrayStore: r2[0] = r0  ; List_4
    //     0x15d7934: stur            w0, [x2, #0x17]
    // 0x15d7938: r1 = <Widget>
    //     0x15d7938: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15d793c: r0 = AllocateGrowableArray()
    //     0x15d793c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15d7940: mov             x1, x0
    // 0x15d7944: ldur            x0, [fp, #-8]
    // 0x15d7948: stur            x1, [fp, #-0x10]
    // 0x15d794c: StoreField: r1->field_f = r0
    //     0x15d794c: stur            w0, [x1, #0xf]
    // 0x15d7950: r0 = 6
    //     0x15d7950: movz            x0, #0x6
    // 0x15d7954: StoreField: r1->field_b = r0
    //     0x15d7954: stur            w0, [x1, #0xb]
    // 0x15d7958: r0 = Row()
    //     0x15d7958: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15d795c: r1 = Instance_Axis
    //     0x15d795c: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15d7960: StoreField: r0->field_f = r1
    //     0x15d7960: stur            w1, [x0, #0xf]
    // 0x15d7964: r1 = Instance_MainAxisAlignment
    //     0x15d7964: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x15d7968: ldr             x1, [x1, #0xab0]
    // 0x15d796c: StoreField: r0->field_13 = r1
    //     0x15d796c: stur            w1, [x0, #0x13]
    // 0x15d7970: r1 = Instance_MainAxisSize
    //     0x15d7970: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15d7974: ldr             x1, [x1, #0xa10]
    // 0x15d7978: ArrayStore: r0[0] = r1  ; List_4
    //     0x15d7978: stur            w1, [x0, #0x17]
    // 0x15d797c: r1 = Instance_CrossAxisAlignment
    //     0x15d797c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15d7980: ldr             x1, [x1, #0xa18]
    // 0x15d7984: StoreField: r0->field_1b = r1
    //     0x15d7984: stur            w1, [x0, #0x1b]
    // 0x15d7988: r1 = Instance_VerticalDirection
    //     0x15d7988: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15d798c: ldr             x1, [x1, #0xa20]
    // 0x15d7990: StoreField: r0->field_23 = r1
    //     0x15d7990: stur            w1, [x0, #0x23]
    // 0x15d7994: r1 = Instance_Clip
    //     0x15d7994: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15d7998: ldr             x1, [x1, #0x38]
    // 0x15d799c: StoreField: r0->field_2b = r1
    //     0x15d799c: stur            w1, [x0, #0x2b]
    // 0x15d79a0: StoreField: r0->field_2f = rZR
    //     0x15d79a0: stur            xzr, [x0, #0x2f]
    // 0x15d79a4: ldur            x1, [fp, #-0x10]
    // 0x15d79a8: StoreField: r0->field_b = r1
    //     0x15d79a8: stur            w1, [x0, #0xb]
    // 0x15d79ac: LeaveFrame
    //     0x15d79ac: mov             SP, fp
    //     0x15d79b0: ldp             fp, lr, [SP], #0x10
    // 0x15d79b4: ret
    //     0x15d79b4: ret             
    // 0x15d79b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d79b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d79bc: b               #0x15d758c
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e5d14, size: 0x18c
    // 0x15e5d14: EnterFrame
    //     0x15e5d14: stp             fp, lr, [SP, #-0x10]!
    //     0x15e5d18: mov             fp, SP
    // 0x15e5d1c: AllocStack(0x28)
    //     0x15e5d1c: sub             SP, SP, #0x28
    // 0x15e5d20: SetupParameters(SearchPage this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15e5d20: mov             x0, x1
    //     0x15e5d24: stur            x1, [fp, #-8]
    //     0x15e5d28: mov             x1, x2
    //     0x15e5d2c: stur            x2, [fp, #-0x10]
    // 0x15e5d30: CheckStackOverflow
    //     0x15e5d30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e5d34: cmp             SP, x16
    //     0x15e5d38: b.ls            #0x15e5e98
    // 0x15e5d3c: r1 = 2
    //     0x15e5d3c: movz            x1, #0x2
    // 0x15e5d40: r0 = AllocateContext()
    //     0x15e5d40: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e5d44: mov             x1, x0
    // 0x15e5d48: ldur            x0, [fp, #-8]
    // 0x15e5d4c: stur            x1, [fp, #-0x18]
    // 0x15e5d50: StoreField: r1->field_f = r0
    //     0x15e5d50: stur            w0, [x1, #0xf]
    // 0x15e5d54: ldur            x0, [fp, #-0x10]
    // 0x15e5d58: StoreField: r1->field_13 = r0
    //     0x15e5d58: stur            w0, [x1, #0x13]
    // 0x15e5d5c: r0 = Obx()
    //     0x15e5d5c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e5d60: ldur            x2, [fp, #-0x18]
    // 0x15e5d64: r1 = Function '<anonymous closure>':.
    //     0x15e5d64: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e940] AnonymousClosure: (0x15d7564), in [package:customer_app/app/presentation/views/glass/search/search_page.dart] SearchPage::appBar (0x15e5d14)
    //     0x15e5d68: ldr             x1, [x1, #0x940]
    // 0x15e5d6c: stur            x0, [fp, #-8]
    // 0x15e5d70: r0 = AllocateClosure()
    //     0x15e5d70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e5d74: mov             x1, x0
    // 0x15e5d78: ldur            x0, [fp, #-8]
    // 0x15e5d7c: StoreField: r0->field_b = r1
    //     0x15e5d7c: stur            w1, [x0, #0xb]
    // 0x15e5d80: ldur            x1, [fp, #-0x10]
    // 0x15e5d84: r0 = of()
    //     0x15e5d84: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e5d88: LoadField: r1 = r0->field_5b
    //     0x15e5d88: ldur            w1, [x0, #0x5b]
    // 0x15e5d8c: DecompressPointer r1
    //     0x15e5d8c: add             x1, x1, HEAP, lsl #32
    // 0x15e5d90: stur            x1, [fp, #-0x10]
    // 0x15e5d94: r0 = ColorFilter()
    //     0x15e5d94: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e5d98: mov             x1, x0
    // 0x15e5d9c: ldur            x0, [fp, #-0x10]
    // 0x15e5da0: stur            x1, [fp, #-0x20]
    // 0x15e5da4: StoreField: r1->field_7 = r0
    //     0x15e5da4: stur            w0, [x1, #7]
    // 0x15e5da8: r0 = Instance_BlendMode
    //     0x15e5da8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e5dac: ldr             x0, [x0, #0xb30]
    // 0x15e5db0: StoreField: r1->field_b = r0
    //     0x15e5db0: stur            w0, [x1, #0xb]
    // 0x15e5db4: r0 = 1
    //     0x15e5db4: movz            x0, #0x1
    // 0x15e5db8: StoreField: r1->field_13 = r0
    //     0x15e5db8: stur            x0, [x1, #0x13]
    // 0x15e5dbc: r0 = SvgPicture()
    //     0x15e5dbc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e5dc0: stur            x0, [fp, #-0x10]
    // 0x15e5dc4: ldur            x16, [fp, #-0x20]
    // 0x15e5dc8: str             x16, [SP]
    // 0x15e5dcc: mov             x1, x0
    // 0x15e5dd0: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e5dd0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e5dd4: ldr             x2, [x2, #0xa40]
    // 0x15e5dd8: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e5dd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e5ddc: ldr             x4, [x4, #0xa38]
    // 0x15e5de0: r0 = SvgPicture.asset()
    //     0x15e5de0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e5de4: r0 = Align()
    //     0x15e5de4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e5de8: mov             x1, x0
    // 0x15e5dec: r0 = Instance_Alignment
    //     0x15e5dec: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e5df0: ldr             x0, [x0, #0xb10]
    // 0x15e5df4: stur            x1, [fp, #-0x20]
    // 0x15e5df8: StoreField: r1->field_f = r0
    //     0x15e5df8: stur            w0, [x1, #0xf]
    // 0x15e5dfc: r0 = 1.000000
    //     0x15e5dfc: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e5e00: StoreField: r1->field_13 = r0
    //     0x15e5e00: stur            w0, [x1, #0x13]
    // 0x15e5e04: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e5e04: stur            w0, [x1, #0x17]
    // 0x15e5e08: ldur            x0, [fp, #-0x10]
    // 0x15e5e0c: StoreField: r1->field_b = r0
    //     0x15e5e0c: stur            w0, [x1, #0xb]
    // 0x15e5e10: r0 = InkWell()
    //     0x15e5e10: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e5e14: mov             x3, x0
    // 0x15e5e18: ldur            x0, [fp, #-0x20]
    // 0x15e5e1c: stur            x3, [fp, #-0x10]
    // 0x15e5e20: StoreField: r3->field_b = r0
    //     0x15e5e20: stur            w0, [x3, #0xb]
    // 0x15e5e24: ldur            x2, [fp, #-0x18]
    // 0x15e5e28: r1 = Function '<anonymous closure>':.
    //     0x15e5e28: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e948] AnonymousClosure: (0x15e5ea0), in [package:customer_app/app/presentation/views/glass/search/search_page.dart] SearchPage::appBar (0x15e5d14)
    //     0x15e5e2c: ldr             x1, [x1, #0x948]
    // 0x15e5e30: r0 = AllocateClosure()
    //     0x15e5e30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e5e34: ldur            x2, [fp, #-0x10]
    // 0x15e5e38: StoreField: r2->field_f = r0
    //     0x15e5e38: stur            w0, [x2, #0xf]
    // 0x15e5e3c: r0 = true
    //     0x15e5e3c: add             x0, NULL, #0x20  ; true
    // 0x15e5e40: StoreField: r2->field_43 = r0
    //     0x15e5e40: stur            w0, [x2, #0x43]
    // 0x15e5e44: r1 = Instance_BoxShape
    //     0x15e5e44: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e5e48: ldr             x1, [x1, #0x80]
    // 0x15e5e4c: StoreField: r2->field_47 = r1
    //     0x15e5e4c: stur            w1, [x2, #0x47]
    // 0x15e5e50: StoreField: r2->field_6f = r0
    //     0x15e5e50: stur            w0, [x2, #0x6f]
    // 0x15e5e54: r1 = false
    //     0x15e5e54: add             x1, NULL, #0x30  ; false
    // 0x15e5e58: StoreField: r2->field_73 = r1
    //     0x15e5e58: stur            w1, [x2, #0x73]
    // 0x15e5e5c: StoreField: r2->field_83 = r0
    //     0x15e5e5c: stur            w0, [x2, #0x83]
    // 0x15e5e60: StoreField: r2->field_7b = r1
    //     0x15e5e60: stur            w1, [x2, #0x7b]
    // 0x15e5e64: r0 = AppBar()
    //     0x15e5e64: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e5e68: stur            x0, [fp, #-0x18]
    // 0x15e5e6c: ldur            x16, [fp, #-8]
    // 0x15e5e70: str             x16, [SP]
    // 0x15e5e74: mov             x1, x0
    // 0x15e5e78: ldur            x2, [fp, #-0x10]
    // 0x15e5e7c: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e5e7c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e5e80: ldr             x4, [x4, #0xf00]
    // 0x15e5e84: r0 = AppBar()
    //     0x15e5e84: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e5e88: ldur            x0, [fp, #-0x18]
    // 0x15e5e8c: LeaveFrame
    //     0x15e5e8c: mov             SP, fp
    //     0x15e5e90: ldp             fp, lr, [SP], #0x10
    // 0x15e5e94: ret
    //     0x15e5e94: ret             
    // 0x15e5e98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e5e98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e5e9c: b               #0x15e5d3c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15e5ea0, size: 0x48
    // 0x15e5ea0: EnterFrame
    //     0x15e5ea0: stp             fp, lr, [SP, #-0x10]!
    //     0x15e5ea4: mov             fp, SP
    // 0x15e5ea8: ldr             x0, [fp, #0x10]
    // 0x15e5eac: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x15e5eac: ldur            w1, [x0, #0x17]
    // 0x15e5eb0: DecompressPointer r1
    //     0x15e5eb0: add             x1, x1, HEAP, lsl #32
    // 0x15e5eb4: CheckStackOverflow
    //     0x15e5eb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e5eb8: cmp             SP, x16
    //     0x15e5ebc: b.ls            #0x15e5ee0
    // 0x15e5ec0: LoadField: r0 = r1->field_f
    //     0x15e5ec0: ldur            w0, [x1, #0xf]
    // 0x15e5ec4: DecompressPointer r0
    //     0x15e5ec4: add             x0, x0, HEAP, lsl #32
    // 0x15e5ec8: mov             x1, x0
    // 0x15e5ecc: r0 = onBackPress()
    //     0x15e5ecc: bl              #0x1479784  ; [package:customer_app/app/presentation/views/basic/search/search_page.dart] SearchPage::onBackPress
    // 0x15e5ed0: r0 = Null
    //     0x15e5ed0: mov             x0, NULL
    // 0x15e5ed4: LeaveFrame
    //     0x15e5ed4: mov             SP, fp
    //     0x15e5ed8: ldp             fp, lr, [SP], #0x10
    // 0x15e5edc: ret
    //     0x15e5edc: ret             
    // 0x15e5ee0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e5ee0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e5ee4: b               #0x15e5ec0
  }
}
