// lib: , url: package:customer_app/app/presentation/views/line/login/login_view.dart

// class id: 1049532, size: 0x8
class :: {
}

// class id: 4531, size: 0x20, field offset: 0x14
class LoginView extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1369340, size: 0x64
    // 0x1369340: EnterFrame
    //     0x1369340: stp             fp, lr, [SP, #-0x10]!
    //     0x1369344: mov             fp, SP
    // 0x1369348: AllocStack(0x18)
    //     0x1369348: sub             SP, SP, #0x18
    // 0x136934c: SetupParameters(LoginView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x136934c: stur            x1, [fp, #-8]
    //     0x1369350: stur            x2, [fp, #-0x10]
    // 0x1369354: r1 = 2
    //     0x1369354: movz            x1, #0x2
    // 0x1369358: r0 = AllocateContext()
    //     0x1369358: bl              #0x16f6108  ; AllocateContextStub
    // 0x136935c: mov             x1, x0
    // 0x1369360: ldur            x0, [fp, #-8]
    // 0x1369364: stur            x1, [fp, #-0x18]
    // 0x1369368: StoreField: r1->field_f = r0
    //     0x1369368: stur            w0, [x1, #0xf]
    // 0x136936c: ldur            x0, [fp, #-0x10]
    // 0x1369370: StoreField: r1->field_13 = r0
    //     0x1369370: stur            w0, [x1, #0x13]
    // 0x1369374: r0 = Obx()
    //     0x1369374: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1369378: ldur            x2, [fp, #-0x18]
    // 0x136937c: r1 = Function '<anonymous closure>':.
    //     0x136937c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36f20] AnonymousClosure: (0x13693a4), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::bottomNavigationBar (0x1369340)
    //     0x1369380: ldr             x1, [x1, #0xf20]
    // 0x1369384: stur            x0, [fp, #-8]
    // 0x1369388: r0 = AllocateClosure()
    //     0x1369388: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x136938c: mov             x1, x0
    // 0x1369390: ldur            x0, [fp, #-8]
    // 0x1369394: StoreField: r0->field_b = r1
    //     0x1369394: stur            w1, [x0, #0xb]
    // 0x1369398: LeaveFrame
    //     0x1369398: mov             SP, fp
    //     0x136939c: ldp             fp, lr, [SP], #0x10
    // 0x13693a0: ret
    //     0x13693a0: ret             
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x13693a4, size: 0x294
    // 0x13693a4: EnterFrame
    //     0x13693a4: stp             fp, lr, [SP, #-0x10]!
    //     0x13693a8: mov             fp, SP
    // 0x13693ac: AllocStack(0x50)
    //     0x13693ac: sub             SP, SP, #0x50
    // 0x13693b0: SetupParameters()
    //     0x13693b0: ldr             x0, [fp, #0x10]
    //     0x13693b4: ldur            w2, [x0, #0x17]
    //     0x13693b8: add             x2, x2, HEAP, lsl #32
    //     0x13693bc: stur            x2, [fp, #-8]
    // 0x13693c0: CheckStackOverflow
    //     0x13693c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13693c4: cmp             SP, x16
    //     0x13693c8: b.ls            #0x1369630
    // 0x13693cc: LoadField: r1 = r2->field_13
    //     0x13693cc: ldur            w1, [x2, #0x13]
    // 0x13693d0: DecompressPointer r1
    //     0x13693d0: add             x1, x1, HEAP, lsl #32
    // 0x13693d4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13693d4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13693d8: r0 = _of()
    //     0x13693d8: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x13693dc: LoadField: r1 = r0->field_23
    //     0x13693dc: ldur            w1, [x0, #0x23]
    // 0x13693e0: DecompressPointer r1
    //     0x13693e0: add             x1, x1, HEAP, lsl #32
    // 0x13693e4: LoadField: d0 = r1->field_1f
    //     0x13693e4: ldur            d0, [x1, #0x1f]
    // 0x13693e8: stur            d0, [fp, #-0x38]
    // 0x13693ec: r0 = EdgeInsets()
    //     0x13693ec: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x13693f0: stur            x0, [fp, #-0x10]
    // 0x13693f4: StoreField: r0->field_7 = rZR
    //     0x13693f4: stur            xzr, [x0, #7]
    // 0x13693f8: StoreField: r0->field_f = rZR
    //     0x13693f8: stur            xzr, [x0, #0xf]
    // 0x13693fc: ArrayStore: r0[0] = rZR  ; List_8
    //     0x13693fc: stur            xzr, [x0, #0x17]
    // 0x1369400: ldur            d0, [fp, #-0x38]
    // 0x1369404: StoreField: r0->field_1f = d0
    //     0x1369404: stur            d0, [x0, #0x1f]
    // 0x1369408: ldur            x2, [fp, #-8]
    // 0x136940c: LoadField: r1 = r2->field_f
    //     0x136940c: ldur            w1, [x2, #0xf]
    // 0x1369410: DecompressPointer r1
    //     0x1369410: add             x1, x1, HEAP, lsl #32
    // 0x1369414: r0 = controller()
    //     0x1369414: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1369418: LoadField: r1 = r0->field_8b
    //     0x1369418: ldur            w1, [x0, #0x8b]
    // 0x136941c: DecompressPointer r1
    //     0x136941c: add             x1, x1, HEAP, lsl #32
    // 0x1369420: r0 = value()
    //     0x1369420: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1369424: cmp             w0, NULL
    // 0x1369428: b.ne            #0x1369434
    // 0x136942c: r1 = true
    //     0x136942c: add             x1, NULL, #0x20  ; true
    // 0x1369430: b               #0x1369438
    // 0x1369434: mov             x1, x0
    // 0x1369438: ldur            x2, [fp, #-8]
    // 0x136943c: ldur            x0, [fp, #-0x10]
    // 0x1369440: eor             x3, x1, #0x10
    // 0x1369444: stur            x3, [fp, #-0x18]
    // 0x1369448: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1369448: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x136944c: ldr             x0, [x0, #0x1c80]
    //     0x1369450: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1369454: cmp             w0, w16
    //     0x1369458: b.ne            #0x1369464
    //     0x136945c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1369460: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1369464: r0 = GetNavigation.width()
    //     0x1369464: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x1369468: stur            d0, [fp, #-0x38]
    // 0x136946c: r0 = GetNavigation.size()
    //     0x136946c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1369470: LoadField: d0 = r0->field_f
    //     0x1369470: ldur            d0, [x0, #0xf]
    // 0x1369474: d1 = 0.060000
    //     0x1369474: add             x17, PP, #0x36, lsl #12  ; [pp+0x36f28] IMM: double(0.06) from 0x3faeb851eb851eb8
    //     0x1369478: ldr             d1, [x17, #0xf28]
    // 0x136947c: fmul            d2, d0, d1
    // 0x1369480: ldur            x2, [fp, #-8]
    // 0x1369484: stur            d2, [fp, #-0x40]
    // 0x1369488: LoadField: r1 = r2->field_13
    //     0x1369488: ldur            w1, [x2, #0x13]
    // 0x136948c: DecompressPointer r1
    //     0x136948c: add             x1, x1, HEAP, lsl #32
    // 0x1369490: r0 = of()
    //     0x1369490: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1369494: LoadField: r2 = r0->field_5b
    //     0x1369494: ldur            w2, [x0, #0x5b]
    // 0x1369498: DecompressPointer r2
    //     0x1369498: add             x2, x2, HEAP, lsl #32
    // 0x136949c: ldur            x0, [fp, #-8]
    // 0x13694a0: stur            x2, [fp, #-0x20]
    // 0x13694a4: LoadField: r1 = r0->field_13
    //     0x13694a4: ldur            w1, [x0, #0x13]
    // 0x13694a8: DecompressPointer r1
    //     0x13694a8: add             x1, x1, HEAP, lsl #32
    // 0x13694ac: r0 = of()
    //     0x13694ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13694b0: LoadField: r1 = r0->field_87
    //     0x13694b0: ldur            w1, [x0, #0x87]
    // 0x13694b4: DecompressPointer r1
    //     0x13694b4: add             x1, x1, HEAP, lsl #32
    // 0x13694b8: LoadField: r0 = r1->field_7
    //     0x13694b8: ldur            w0, [x1, #7]
    // 0x13694bc: DecompressPointer r0
    //     0x13694bc: add             x0, x0, HEAP, lsl #32
    // 0x13694c0: r16 = 14.000000
    //     0x13694c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13694c4: ldr             x16, [x16, #0x1d8]
    // 0x13694c8: r30 = Instance_Color
    //     0x13694c8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13694cc: stp             lr, x16, [SP]
    // 0x13694d0: mov             x1, x0
    // 0x13694d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13694d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13694d8: ldr             x4, [x4, #0xaa0]
    // 0x13694dc: r0 = copyWith()
    //     0x13694dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13694e0: stur            x0, [fp, #-0x28]
    // 0x13694e4: r0 = Text()
    //     0x13694e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13694e8: mov             x3, x0
    // 0x13694ec: r0 = "CONFIRM NUMBER"
    //     0x13694ec: add             x0, PP, #0x36, lsl #12  ; [pp+0x36f30] "CONFIRM NUMBER"
    //     0x13694f0: ldr             x0, [x0, #0xf30]
    // 0x13694f4: stur            x3, [fp, #-0x30]
    // 0x13694f8: StoreField: r3->field_b = r0
    //     0x13694f8: stur            w0, [x3, #0xb]
    // 0x13694fc: ldur            x0, [fp, #-0x28]
    // 0x1369500: StoreField: r3->field_13 = r0
    //     0x1369500: stur            w0, [x3, #0x13]
    // 0x1369504: ldur            x2, [fp, #-8]
    // 0x1369508: r1 = Function '<anonymous closure>':.
    //     0x1369508: add             x1, PP, #0x36, lsl #12  ; [pp+0x36f38] AnonymousClosure: (0x1369638), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::bottomNavigationBar (0x1369340)
    //     0x136950c: ldr             x1, [x1, #0xf38]
    // 0x1369510: r0 = AllocateClosure()
    //     0x1369510: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1369514: stur            x0, [fp, #-8]
    // 0x1369518: r0 = MaterialButton()
    //     0x1369518: bl              #0x131db78  ; AllocateMaterialButtonStub -> MaterialButton (size=0x88)
    // 0x136951c: mov             x1, x0
    // 0x1369520: ldur            x0, [fp, #-8]
    // 0x1369524: stur            x1, [fp, #-0x28]
    // 0x1369528: StoreField: r1->field_b = r0
    //     0x1369528: stur            w0, [x1, #0xb]
    // 0x136952c: r0 = Instance_Color
    //     0x136952c: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1369530: StoreField: r1->field_1f = r0
    //     0x1369530: stur            w0, [x1, #0x1f]
    // 0x1369534: ldur            x0, [fp, #-0x20]
    // 0x1369538: StoreField: r1->field_23 = r0
    //     0x1369538: stur            w0, [x1, #0x23]
    // 0x136953c: r0 = Instance_RoundedRectangleBorder
    //     0x136953c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x1369540: ldr             x0, [x0, #0xd68]
    // 0x1369544: StoreField: r1->field_5b = r0
    //     0x1369544: stur            w0, [x1, #0x5b]
    // 0x1369548: r0 = Instance_Clip
    //     0x1369548: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x136954c: ldr             x0, [x0, #0x38]
    // 0x1369550: StoreField: r1->field_5f = r0
    //     0x1369550: stur            w0, [x1, #0x5f]
    // 0x1369554: r0 = false
    //     0x1369554: add             x0, NULL, #0x30  ; false
    // 0x1369558: StoreField: r1->field_67 = r0
    //     0x1369558: stur            w0, [x1, #0x67]
    // 0x136955c: ldur            d0, [fp, #-0x38]
    // 0x1369560: StoreField: r1->field_73 = d0
    //     0x1369560: stur            d0, [x1, #0x73]
    // 0x1369564: ldur            d0, [fp, #-0x40]
    // 0x1369568: StoreField: r1->field_7b = d0
    //     0x1369568: stur            d0, [x1, #0x7b]
    // 0x136956c: r2 = true
    //     0x136956c: add             x2, NULL, #0x20  ; true
    // 0x1369570: StoreField: r1->field_83 = r2
    //     0x1369570: stur            w2, [x1, #0x83]
    // 0x1369574: ldur            x2, [fp, #-0x30]
    // 0x1369578: StoreField: r1->field_4f = r2
    //     0x1369578: stur            w2, [x1, #0x4f]
    // 0x136957c: r2 = Instance_ValueKey
    //     0x136957c: add             x2, PP, #0x36, lsl #12  ; [pp+0x36f40] Obj!ValueKey<String>@d5b381
    //     0x1369580: ldr             x2, [x2, #0xf40]
    // 0x1369584: StoreField: r1->field_7 = r2
    //     0x1369584: stur            w2, [x1, #7]
    // 0x1369588: r0 = Padding()
    //     0x1369588: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x136958c: mov             x1, x0
    // 0x1369590: r0 = Instance_EdgeInsets
    //     0x1369590: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1369594: ldr             x0, [x0, #0x1f0]
    // 0x1369598: stur            x1, [fp, #-8]
    // 0x136959c: StoreField: r1->field_f = r0
    //     0x136959c: stur            w0, [x1, #0xf]
    // 0x13695a0: ldur            x0, [fp, #-0x28]
    // 0x13695a4: StoreField: r1->field_b = r0
    //     0x13695a4: stur            w0, [x1, #0xb]
    // 0x13695a8: r0 = Visibility()
    //     0x13695a8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x13695ac: mov             x1, x0
    // 0x13695b0: ldur            x0, [fp, #-8]
    // 0x13695b4: stur            x1, [fp, #-0x20]
    // 0x13695b8: StoreField: r1->field_b = r0
    //     0x13695b8: stur            w0, [x1, #0xb]
    // 0x13695bc: r0 = Instance_SizedBox
    //     0x13695bc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x13695c0: StoreField: r1->field_f = r0
    //     0x13695c0: stur            w0, [x1, #0xf]
    // 0x13695c4: ldur            x0, [fp, #-0x18]
    // 0x13695c8: StoreField: r1->field_13 = r0
    //     0x13695c8: stur            w0, [x1, #0x13]
    // 0x13695cc: r0 = false
    //     0x13695cc: add             x0, NULL, #0x30  ; false
    // 0x13695d0: ArrayStore: r1[0] = r0  ; List_4
    //     0x13695d0: stur            w0, [x1, #0x17]
    // 0x13695d4: StoreField: r1->field_1b = r0
    //     0x13695d4: stur            w0, [x1, #0x1b]
    // 0x13695d8: StoreField: r1->field_1f = r0
    //     0x13695d8: stur            w0, [x1, #0x1f]
    // 0x13695dc: StoreField: r1->field_23 = r0
    //     0x13695dc: stur            w0, [x1, #0x23]
    // 0x13695e0: StoreField: r1->field_27 = r0
    //     0x13695e0: stur            w0, [x1, #0x27]
    // 0x13695e4: StoreField: r1->field_2b = r0
    //     0x13695e4: stur            w0, [x1, #0x2b]
    // 0x13695e8: r0 = Container()
    //     0x13695e8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13695ec: stur            x0, [fp, #-8]
    // 0x13695f0: r16 = Instance_BoxDecoration
    //     0x13695f0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f48] Obj!BoxDecoration@d64cb1
    //     0x13695f4: ldr             x16, [x16, #0xf48]
    // 0x13695f8: ldur            lr, [fp, #-0x20]
    // 0x13695fc: stp             lr, x16, [SP]
    // 0x1369600: mov             x1, x0
    // 0x1369604: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x1369604: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x1369608: ldr             x4, [x4, #0x88]
    // 0x136960c: r0 = Container()
    //     0x136960c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1369610: r0 = Padding()
    //     0x1369610: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1369614: ldur            x1, [fp, #-0x10]
    // 0x1369618: StoreField: r0->field_f = r1
    //     0x1369618: stur            w1, [x0, #0xf]
    // 0x136961c: ldur            x1, [fp, #-8]
    // 0x1369620: StoreField: r0->field_b = r1
    //     0x1369620: stur            w1, [x0, #0xb]
    // 0x1369624: LeaveFrame
    //     0x1369624: mov             SP, fp
    //     0x1369628: ldp             fp, lr, [SP], #0x10
    // 0x136962c: ret
    //     0x136962c: ret             
    // 0x1369630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1369630: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1369634: b               #0x13693cc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1369638, size: 0x48
    // 0x1369638: EnterFrame
    //     0x1369638: stp             fp, lr, [SP, #-0x10]!
    //     0x136963c: mov             fp, SP
    // 0x1369640: ldr             x0, [fp, #0x10]
    // 0x1369644: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1369644: ldur            w1, [x0, #0x17]
    // 0x1369648: DecompressPointer r1
    //     0x1369648: add             x1, x1, HEAP, lsl #32
    // 0x136964c: CheckStackOverflow
    //     0x136964c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1369650: cmp             SP, x16
    //     0x1369654: b.ls            #0x1369678
    // 0x1369658: LoadField: r0 = r1->field_f
    //     0x1369658: ldur            w0, [x1, #0xf]
    // 0x136965c: DecompressPointer r0
    //     0x136965c: add             x0, x0, HEAP, lsl #32
    // 0x1369660: mov             x1, x0
    // 0x1369664: r0 = onPhoneSubmitted()
    //     0x1369664: bl              #0x131dbcc  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::onPhoneSubmitted
    // 0x1369668: r0 = Null
    //     0x1369668: mov             x0, NULL
    // 0x136966c: LeaveFrame
    //     0x136966c: mov             SP, fp
    //     0x1369670: ldp             fp, lr, [SP], #0x10
    // 0x1369674: ret
    //     0x1369674: ret             
    // 0x1369678: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1369678: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x136967c: b               #0x1369658
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x140313c, size: 0xe4
    // 0x140313c: EnterFrame
    //     0x140313c: stp             fp, lr, [SP, #-0x10]!
    //     0x1403140: mov             fp, SP
    // 0x1403144: AllocStack(0x10)
    //     0x1403144: sub             SP, SP, #0x10
    // 0x1403148: SetupParameters()
    //     0x1403148: ldr             x0, [fp, #0x10]
    //     0x140314c: ldur            w2, [x0, #0x17]
    //     0x1403150: add             x2, x2, HEAP, lsl #32
    //     0x1403154: stur            x2, [fp, #-8]
    // 0x1403158: CheckStackOverflow
    //     0x1403158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140315c: cmp             SP, x16
    //     0x1403160: b.ls            #0x1403218
    // 0x1403164: LoadField: r1 = r2->field_f
    //     0x1403164: ldur            w1, [x2, #0xf]
    // 0x1403168: DecompressPointer r1
    //     0x1403168: add             x1, x1, HEAP, lsl #32
    // 0x140316c: r0 = controller()
    //     0x140316c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1403170: LoadField: r1 = r0->field_97
    //     0x1403170: ldur            w1, [x0, #0x97]
    // 0x1403174: DecompressPointer r1
    //     0x1403174: add             x1, x1, HEAP, lsl #32
    // 0x1403178: r0 = value()
    //     0x1403178: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140317c: cmp             w0, NULL
    // 0x1403180: b.eq            #0x1403208
    // 0x1403184: tbnz            w0, #4, #0x1403208
    // 0x1403188: ldur            x0, [fp, #-8]
    // 0x140318c: LoadField: r1 = r0->field_f
    //     0x140318c: ldur            w1, [x0, #0xf]
    // 0x1403190: DecompressPointer r1
    //     0x1403190: add             x1, x1, HEAP, lsl #32
    // 0x1403194: r0 = controller()
    //     0x1403194: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1403198: LoadField: r1 = r0->field_97
    //     0x1403198: ldur            w1, [x0, #0x97]
    // 0x140319c: DecompressPointer r1
    //     0x140319c: add             x1, x1, HEAP, lsl #32
    // 0x14031a0: r2 = false
    //     0x14031a0: add             x2, NULL, #0x30  ; false
    // 0x14031a4: r0 = value=()
    //     0x14031a4: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14031a8: ldur            x0, [fp, #-8]
    // 0x14031ac: LoadField: r1 = r0->field_f
    //     0x14031ac: ldur            w1, [x0, #0xf]
    // 0x14031b0: DecompressPointer r1
    //     0x14031b0: add             x1, x1, HEAP, lsl #32
    // 0x14031b4: r0 = controller()
    //     0x14031b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14031b8: mov             x1, x0
    // 0x14031bc: r0 = startTimer()
    //     0x14031bc: bl              #0x1402244  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::startTimer
    // 0x14031c0: ldur            x0, [fp, #-8]
    // 0x14031c4: LoadField: r1 = r0->field_f
    //     0x14031c4: ldur            w1, [x0, #0xf]
    // 0x14031c8: DecompressPointer r1
    //     0x14031c8: add             x1, x1, HEAP, lsl #32
    // 0x14031cc: r0 = controller()
    //     0x14031cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14031d0: mov             x2, x0
    // 0x14031d4: ldur            x0, [fp, #-8]
    // 0x14031d8: stur            x2, [fp, #-0x10]
    // 0x14031dc: LoadField: r1 = r0->field_f
    //     0x14031dc: ldur            w1, [x0, #0xf]
    // 0x14031e0: DecompressPointer r1
    //     0x14031e0: add             x1, x1, HEAP, lsl #32
    // 0x14031e4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14031e4: ldur            w0, [x1, #0x17]
    // 0x14031e8: DecompressPointer r0
    //     0x14031e8: add             x0, x0, HEAP, lsl #32
    // 0x14031ec: stur            x0, [fp, #-8]
    // 0x14031f0: r0 = controller()
    //     0x14031f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14031f4: LoadField: r3 = r0->field_9b
    //     0x14031f4: ldur            w3, [x0, #0x9b]
    // 0x14031f8: DecompressPointer r3
    //     0x14031f8: add             x3, x3, HEAP, lsl #32
    // 0x14031fc: ldur            x1, [fp, #-0x10]
    // 0x1403200: ldur            x2, [fp, #-8]
    // 0x1403204: r0 = resendOtp()
    //     0x1403204: bl              #0x1404f54  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::resendOtp
    // 0x1403208: r0 = Null
    //     0x1403208: mov             x0, NULL
    // 0x140320c: LeaveFrame
    //     0x140320c: mov             SP, fp
    //     0x1403210: ldp             fp, lr, [SP], #0x10
    // 0x1403214: ret
    //     0x1403214: ret             
    // 0x1403218: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1403218: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140321c: b               #0x1403164
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0x1403220, size: 0xec0
    // 0x1403220: EnterFrame
    //     0x1403220: stp             fp, lr, [SP, #-0x10]!
    //     0x1403224: mov             fp, SP
    // 0x1403228: AllocStack(0xd0)
    //     0x1403228: sub             SP, SP, #0xd0
    // 0x140322c: SetupParameters()
    //     0x140322c: ldr             x0, [fp, #0x10]
    //     0x1403230: ldur            w2, [x0, #0x17]
    //     0x1403234: add             x2, x2, HEAP, lsl #32
    //     0x1403238: stur            x2, [fp, #-8]
    // 0x140323c: CheckStackOverflow
    //     0x140323c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1403240: cmp             SP, x16
    //     0x1403244: b.ls            #0x14040bc
    // 0x1403248: LoadField: r1 = r2->field_f
    //     0x1403248: ldur            w1, [x2, #0xf]
    // 0x140324c: DecompressPointer r1
    //     0x140324c: add             x1, x1, HEAP, lsl #32
    // 0x1403250: r0 = controller()
    //     0x1403250: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1403254: LoadField: r1 = r0->field_8b
    //     0x1403254: ldur            w1, [x0, #0x8b]
    // 0x1403258: DecompressPointer r1
    //     0x1403258: add             x1, x1, HEAP, lsl #32
    // 0x140325c: r0 = value()
    //     0x140325c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1403260: cmp             w0, NULL
    // 0x1403264: b.eq            #0x1403278
    // 0x1403268: tbz             w0, #4, #0x1403278
    // 0x140326c: r0 = "ENTER MOBILE NUMBER"
    //     0x140326c: add             x0, PP, #0x37, lsl #12  ; [pp+0x370f8] "ENTER MOBILE NUMBER"
    //     0x1403270: ldr             x0, [x0, #0xf8]
    // 0x1403274: b               #0x1403280
    // 0x1403278: r0 = "ENTER OTP"
    //     0x1403278: add             x0, PP, #0x37, lsl #12  ; [pp+0x37100] "ENTER OTP"
    //     0x140327c: ldr             x0, [x0, #0x100]
    // 0x1403280: ldur            x2, [fp, #-8]
    // 0x1403284: stur            x0, [fp, #-0x10]
    // 0x1403288: LoadField: r1 = r2->field_13
    //     0x1403288: ldur            w1, [x2, #0x13]
    // 0x140328c: DecompressPointer r1
    //     0x140328c: add             x1, x1, HEAP, lsl #32
    // 0x1403290: r0 = of()
    //     0x1403290: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1403294: LoadField: r1 = r0->field_87
    //     0x1403294: ldur            w1, [x0, #0x87]
    // 0x1403298: DecompressPointer r1
    //     0x1403298: add             x1, x1, HEAP, lsl #32
    // 0x140329c: LoadField: r0 = r1->field_7
    //     0x140329c: ldur            w0, [x1, #7]
    // 0x14032a0: DecompressPointer r0
    //     0x14032a0: add             x0, x0, HEAP, lsl #32
    // 0x14032a4: stur            x0, [fp, #-0x18]
    // 0x14032a8: r1 = Instance_Color
    //     0x14032a8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14032ac: d0 = 0.700000
    //     0x14032ac: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14032b0: ldr             d0, [x17, #0xf48]
    // 0x14032b4: r0 = withOpacity()
    //     0x14032b4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14032b8: r16 = 14.000000
    //     0x14032b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14032bc: ldr             x16, [x16, #0x1d8]
    // 0x14032c0: stp             x0, x16, [SP]
    // 0x14032c4: ldur            x1, [fp, #-0x18]
    // 0x14032c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14032c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14032cc: ldr             x4, [x4, #0xaa0]
    // 0x14032d0: r0 = copyWith()
    //     0x14032d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14032d4: stur            x0, [fp, #-0x18]
    // 0x14032d8: r0 = Text()
    //     0x14032d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14032dc: mov             x1, x0
    // 0x14032e0: ldur            x0, [fp, #-0x10]
    // 0x14032e4: stur            x1, [fp, #-0x20]
    // 0x14032e8: StoreField: r1->field_b = r0
    //     0x14032e8: stur            w0, [x1, #0xb]
    // 0x14032ec: ldur            x0, [fp, #-0x18]
    // 0x14032f0: StoreField: r1->field_13 = r0
    //     0x14032f0: stur            w0, [x1, #0x13]
    // 0x14032f4: r0 = Padding()
    //     0x14032f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14032f8: mov             x2, x0
    // 0x14032fc: r0 = Instance_EdgeInsets
    //     0x14032fc: add             x0, PP, #0x37, lsl #12  ; [pp+0x37108] Obj!EdgeInsets@d59db1
    //     0x1403300: ldr             x0, [x0, #0x108]
    // 0x1403304: stur            x2, [fp, #-0x10]
    // 0x1403308: StoreField: r2->field_f = r0
    //     0x1403308: stur            w0, [x2, #0xf]
    // 0x140330c: ldur            x0, [fp, #-0x20]
    // 0x1403310: StoreField: r2->field_b = r0
    //     0x1403310: stur            w0, [x2, #0xb]
    // 0x1403314: ldur            x0, [fp, #-8]
    // 0x1403318: LoadField: r1 = r0->field_f
    //     0x1403318: ldur            w1, [x0, #0xf]
    // 0x140331c: DecompressPointer r1
    //     0x140331c: add             x1, x1, HEAP, lsl #32
    // 0x1403320: r0 = controller()
    //     0x1403320: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1403324: LoadField: r1 = r0->field_8b
    //     0x1403324: ldur            w1, [x0, #0x8b]
    // 0x1403328: DecompressPointer r1
    //     0x1403328: add             x1, x1, HEAP, lsl #32
    // 0x140332c: r0 = value()
    //     0x140332c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1403330: cmp             w0, NULL
    // 0x1403334: b.ne            #0x1403340
    // 0x1403338: r3 = true
    //     0x1403338: add             x3, NULL, #0x20  ; true
    // 0x140333c: b               #0x1403344
    // 0x1403340: mov             x3, x0
    // 0x1403344: ldur            x0, [fp, #-8]
    // 0x1403348: stur            x3, [fp, #-0x18]
    // 0x140334c: r1 = Null
    //     0x140334c: mov             x1, NULL
    // 0x1403350: r2 = 4
    //     0x1403350: movz            x2, #0x4
    // 0x1403354: r0 = AllocateArray()
    //     0x1403354: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1403358: r16 = "A 4 digit code has been sent on "
    //     0x1403358: add             x16, PP, #0x37, lsl #12  ; [pp+0x37110] "A 4 digit code has been sent on "
    //     0x140335c: ldr             x16, [x16, #0x110]
    // 0x1403360: StoreField: r0->field_f = r16
    //     0x1403360: stur            w16, [x0, #0xf]
    // 0x1403364: ldur            x2, [fp, #-8]
    // 0x1403368: LoadField: r1 = r2->field_f
    //     0x1403368: ldur            w1, [x2, #0xf]
    // 0x140336c: DecompressPointer r1
    //     0x140336c: add             x1, x1, HEAP, lsl #32
    // 0x1403370: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x1403370: ldur            w3, [x1, #0x17]
    // 0x1403374: DecompressPointer r3
    //     0x1403374: add             x3, x3, HEAP, lsl #32
    // 0x1403378: StoreField: r0->field_13 = r3
    //     0x1403378: stur            w3, [x0, #0x13]
    // 0x140337c: str             x0, [SP]
    // 0x1403380: r0 = _interpolate()
    //     0x1403380: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1403384: ldur            x2, [fp, #-8]
    // 0x1403388: stur            x0, [fp, #-0x20]
    // 0x140338c: LoadField: r1 = r2->field_13
    //     0x140338c: ldur            w1, [x2, #0x13]
    // 0x1403390: DecompressPointer r1
    //     0x1403390: add             x1, x1, HEAP, lsl #32
    // 0x1403394: r0 = of()
    //     0x1403394: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1403398: LoadField: r1 = r0->field_87
    //     0x1403398: ldur            w1, [x0, #0x87]
    // 0x140339c: DecompressPointer r1
    //     0x140339c: add             x1, x1, HEAP, lsl #32
    // 0x14033a0: LoadField: r0 = r1->field_2b
    //     0x14033a0: ldur            w0, [x1, #0x2b]
    // 0x14033a4: DecompressPointer r0
    //     0x14033a4: add             x0, x0, HEAP, lsl #32
    // 0x14033a8: stur            x0, [fp, #-0x28]
    // 0x14033ac: r1 = Instance_Color
    //     0x14033ac: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14033b0: d0 = 0.700000
    //     0x14033b0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14033b4: ldr             d0, [x17, #0xf48]
    // 0x14033b8: r0 = withOpacity()
    //     0x14033b8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14033bc: r16 = 14.000000
    //     0x14033bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14033c0: ldr             x16, [x16, #0x1d8]
    // 0x14033c4: stp             x0, x16, [SP]
    // 0x14033c8: ldur            x1, [fp, #-0x28]
    // 0x14033cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14033cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14033d0: ldr             x4, [x4, #0xaa0]
    // 0x14033d4: r0 = copyWith()
    //     0x14033d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14033d8: stur            x0, [fp, #-0x28]
    // 0x14033dc: r0 = Text()
    //     0x14033dc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14033e0: mov             x1, x0
    // 0x14033e4: ldur            x0, [fp, #-0x20]
    // 0x14033e8: stur            x1, [fp, #-0x30]
    // 0x14033ec: StoreField: r1->field_b = r0
    //     0x14033ec: stur            w0, [x1, #0xb]
    // 0x14033f0: ldur            x0, [fp, #-0x28]
    // 0x14033f4: StoreField: r1->field_13 = r0
    //     0x14033f4: stur            w0, [x1, #0x13]
    // 0x14033f8: r0 = Padding()
    //     0x14033f8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14033fc: mov             x1, x0
    // 0x1403400: r0 = Instance_EdgeInsets
    //     0x1403400: add             x0, PP, #0x37, lsl #12  ; [pp+0x37118] Obj!EdgeInsets@d59d81
    //     0x1403404: ldr             x0, [x0, #0x118]
    // 0x1403408: stur            x1, [fp, #-0x20]
    // 0x140340c: StoreField: r1->field_f = r0
    //     0x140340c: stur            w0, [x1, #0xf]
    // 0x1403410: ldur            x0, [fp, #-0x30]
    // 0x1403414: StoreField: r1->field_b = r0
    //     0x1403414: stur            w0, [x1, #0xb]
    // 0x1403418: r0 = Visibility()
    //     0x1403418: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x140341c: mov             x2, x0
    // 0x1403420: ldur            x0, [fp, #-0x20]
    // 0x1403424: stur            x2, [fp, #-0x28]
    // 0x1403428: StoreField: r2->field_b = r0
    //     0x1403428: stur            w0, [x2, #0xb]
    // 0x140342c: r0 = Instance_SizedBox
    //     0x140342c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1403430: StoreField: r2->field_f = r0
    //     0x1403430: stur            w0, [x2, #0xf]
    // 0x1403434: ldur            x1, [fp, #-0x18]
    // 0x1403438: StoreField: r2->field_13 = r1
    //     0x1403438: stur            w1, [x2, #0x13]
    // 0x140343c: r3 = false
    //     0x140343c: add             x3, NULL, #0x30  ; false
    // 0x1403440: ArrayStore: r2[0] = r3  ; List_4
    //     0x1403440: stur            w3, [x2, #0x17]
    // 0x1403444: StoreField: r2->field_1b = r3
    //     0x1403444: stur            w3, [x2, #0x1b]
    // 0x1403448: StoreField: r2->field_1f = r3
    //     0x1403448: stur            w3, [x2, #0x1f]
    // 0x140344c: StoreField: r2->field_23 = r3
    //     0x140344c: stur            w3, [x2, #0x23]
    // 0x1403450: StoreField: r2->field_27 = r3
    //     0x1403450: stur            w3, [x2, #0x27]
    // 0x1403454: StoreField: r2->field_2b = r3
    //     0x1403454: stur            w3, [x2, #0x2b]
    // 0x1403458: ldur            x4, [fp, #-8]
    // 0x140345c: LoadField: r1 = r4->field_f
    //     0x140345c: ldur            w1, [x4, #0xf]
    // 0x1403460: DecompressPointer r1
    //     0x1403460: add             x1, x1, HEAP, lsl #32
    // 0x1403464: r0 = controller()
    //     0x1403464: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1403468: LoadField: r1 = r0->field_8b
    //     0x1403468: ldur            w1, [x0, #0x8b]
    // 0x140346c: DecompressPointer r1
    //     0x140346c: add             x1, x1, HEAP, lsl #32
    // 0x1403470: r0 = value()
    //     0x1403470: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1403474: cmp             w0, NULL
    // 0x1403478: b.ne            #0x1403480
    // 0x140347c: r0 = true
    //     0x140347c: add             x0, NULL, #0x20  ; true
    // 0x1403480: ldur            x2, [fp, #-8]
    // 0x1403484: eor             x1, x0, #0x10
    // 0x1403488: stur            x1, [fp, #-0x30]
    // 0x140348c: LoadField: r0 = r2->field_f
    //     0x140348c: ldur            w0, [x2, #0xf]
    // 0x1403490: DecompressPointer r0
    //     0x1403490: add             x0, x0, HEAP, lsl #32
    // 0x1403494: stur            x0, [fp, #-0x20]
    // 0x1403498: LoadField: r3 = r0->field_13
    //     0x1403498: ldur            w3, [x0, #0x13]
    // 0x140349c: DecompressPointer r3
    //     0x140349c: add             x3, x3, HEAP, lsl #32
    // 0x14034a0: stur            x3, [fp, #-0x18]
    // 0x14034a4: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0x14034a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14034a8: ldr             x0, [x0, #0x1530]
    //     0x14034ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14034b0: cmp             w0, w16
    //     0x14034b4: b.ne            #0x14034c4
    //     0x14034b8: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0x14034bc: ldr             x2, [x2, #0x120]
    //     0x14034c0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14034c4: stur            x0, [fp, #-0x38]
    // 0x14034c8: r16 = "[0-9]"
    //     0x14034c8: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0x14034cc: ldr             x16, [x16, #0x128]
    // 0x14034d0: stp             x16, NULL, [SP, #0x20]
    // 0x14034d4: r16 = false
    //     0x14034d4: add             x16, NULL, #0x30  ; false
    // 0x14034d8: r30 = true
    //     0x14034d8: add             lr, NULL, #0x20  ; true
    // 0x14034dc: stp             lr, x16, [SP, #0x10]
    // 0x14034e0: r16 = false
    //     0x14034e0: add             x16, NULL, #0x30  ; false
    // 0x14034e4: r30 = false
    //     0x14034e4: add             lr, NULL, #0x30  ; false
    // 0x14034e8: stp             lr, x16, [SP]
    // 0x14034ec: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x14034ec: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x14034f0: r0 = _RegExp()
    //     0x14034f0: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0x14034f4: stur            x0, [fp, #-0x40]
    // 0x14034f8: r0 = FilteringTextInputFormatter()
    //     0x14034f8: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0x14034fc: mov             x1, x0
    // 0x1403500: ldur            x0, [fp, #-0x40]
    // 0x1403504: stur            x1, [fp, #-0x48]
    // 0x1403508: StoreField: r1->field_b = r0
    //     0x1403508: stur            w0, [x1, #0xb]
    // 0x140350c: r0 = true
    //     0x140350c: add             x0, NULL, #0x20  ; true
    // 0x1403510: StoreField: r1->field_7 = r0
    //     0x1403510: stur            w0, [x1, #7]
    // 0x1403514: r2 = ""
    //     0x1403514: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1403518: StoreField: r1->field_f = r2
    //     0x1403518: stur            w2, [x1, #0xf]
    // 0x140351c: r0 = LengthLimitingTextInputFormatter()
    //     0x140351c: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0x1403520: mov             x3, x0
    // 0x1403524: r0 = 20
    //     0x1403524: movz            x0, #0x14
    // 0x1403528: stur            x3, [fp, #-0x40]
    // 0x140352c: StoreField: r3->field_7 = r0
    //     0x140352c: stur            w0, [x3, #7]
    // 0x1403530: r1 = Null
    //     0x1403530: mov             x1, NULL
    // 0x1403534: r2 = 6
    //     0x1403534: movz            x2, #0x6
    // 0x1403538: r0 = AllocateArray()
    //     0x1403538: bl              #0x16f7198  ; AllocateArrayStub
    // 0x140353c: mov             x2, x0
    // 0x1403540: ldur            x0, [fp, #-0x38]
    // 0x1403544: stur            x2, [fp, #-0x50]
    // 0x1403548: StoreField: r2->field_f = r0
    //     0x1403548: stur            w0, [x2, #0xf]
    // 0x140354c: ldur            x1, [fp, #-0x48]
    // 0x1403550: StoreField: r2->field_13 = r1
    //     0x1403550: stur            w1, [x2, #0x13]
    // 0x1403554: ldur            x1, [fp, #-0x40]
    // 0x1403558: ArrayStore: r2[0] = r1  ; List_4
    //     0x1403558: stur            w1, [x2, #0x17]
    // 0x140355c: r1 = <TextInputFormatter>
    //     0x140355c: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0x1403560: ldr             x1, [x1, #0x7b0]
    // 0x1403564: r0 = AllocateGrowableArray()
    //     0x1403564: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1403568: mov             x2, x0
    // 0x140356c: ldur            x0, [fp, #-0x50]
    // 0x1403570: stur            x2, [fp, #-0x40]
    // 0x1403574: StoreField: r2->field_f = r0
    //     0x1403574: stur            w0, [x2, #0xf]
    // 0x1403578: r0 = 6
    //     0x1403578: movz            x0, #0x6
    // 0x140357c: StoreField: r2->field_b = r0
    //     0x140357c: stur            w0, [x2, #0xb]
    // 0x1403580: ldur            x0, [fp, #-8]
    // 0x1403584: LoadField: r1 = r0->field_13
    //     0x1403584: ldur            w1, [x0, #0x13]
    // 0x1403588: DecompressPointer r1
    //     0x1403588: add             x1, x1, HEAP, lsl #32
    // 0x140358c: r0 = of()
    //     0x140358c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1403590: LoadField: r1 = r0->field_87
    //     0x1403590: ldur            w1, [x0, #0x87]
    // 0x1403594: DecompressPointer r1
    //     0x1403594: add             x1, x1, HEAP, lsl #32
    // 0x1403598: LoadField: r0 = r1->field_2b
    //     0x1403598: ldur            w0, [x1, #0x2b]
    // 0x140359c: DecompressPointer r0
    //     0x140359c: add             x0, x0, HEAP, lsl #32
    // 0x14035a0: ldur            x2, [fp, #-8]
    // 0x14035a4: stur            x0, [fp, #-0x48]
    // 0x14035a8: LoadField: r1 = r2->field_13
    //     0x14035a8: ldur            w1, [x2, #0x13]
    // 0x14035ac: DecompressPointer r1
    //     0x14035ac: add             x1, x1, HEAP, lsl #32
    // 0x14035b0: r0 = of()
    //     0x14035b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14035b4: LoadField: r1 = r0->field_5b
    //     0x14035b4: ldur            w1, [x0, #0x5b]
    // 0x14035b8: DecompressPointer r1
    //     0x14035b8: add             x1, x1, HEAP, lsl #32
    // 0x14035bc: r16 = 14.000000
    //     0x14035bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14035c0: ldr             x16, [x16, #0x1d8]
    // 0x14035c4: stp             x16, x1, [SP]
    // 0x14035c8: ldur            x1, [fp, #-0x48]
    // 0x14035cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14035cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14035d0: ldr             x4, [x4, #0x9b8]
    // 0x14035d4: r0 = copyWith()
    //     0x14035d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14035d8: ldur            x2, [fp, #-8]
    // 0x14035dc: stur            x0, [fp, #-0x48]
    // 0x14035e0: LoadField: r1 = r2->field_13
    //     0x14035e0: ldur            w1, [x2, #0x13]
    // 0x14035e4: DecompressPointer r1
    //     0x14035e4: add             x1, x1, HEAP, lsl #32
    // 0x14035e8: r0 = getTextFormFieldInputDecoration()
    //     0x14035e8: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0x14035ec: ldur            x2, [fp, #-8]
    // 0x14035f0: stur            x0, [fp, #-0x50]
    // 0x14035f4: LoadField: r1 = r2->field_13
    //     0x14035f4: ldur            w1, [x2, #0x13]
    // 0x14035f8: DecompressPointer r1
    //     0x14035f8: add             x1, x1, HEAP, lsl #32
    // 0x14035fc: r0 = of()
    //     0x14035fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1403600: LoadField: r1 = r0->field_87
    //     0x1403600: ldur            w1, [x0, #0x87]
    // 0x1403604: DecompressPointer r1
    //     0x1403604: add             x1, x1, HEAP, lsl #32
    // 0x1403608: LoadField: r0 = r1->field_2b
    //     0x1403608: ldur            w0, [x1, #0x2b]
    // 0x140360c: DecompressPointer r0
    //     0x140360c: add             x0, x0, HEAP, lsl #32
    // 0x1403610: ldur            x2, [fp, #-8]
    // 0x1403614: stur            x0, [fp, #-0x58]
    // 0x1403618: LoadField: r1 = r2->field_13
    //     0x1403618: ldur            w1, [x2, #0x13]
    // 0x140361c: DecompressPointer r1
    //     0x140361c: add             x1, x1, HEAP, lsl #32
    // 0x1403620: r0 = of()
    //     0x1403620: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1403624: LoadField: r1 = r0->field_5b
    //     0x1403624: ldur            w1, [x0, #0x5b]
    // 0x1403628: DecompressPointer r1
    //     0x1403628: add             x1, x1, HEAP, lsl #32
    // 0x140362c: r0 = LoadClassIdInstr(r1)
    //     0x140362c: ldur            x0, [x1, #-1]
    //     0x1403630: ubfx            x0, x0, #0xc, #0x14
    // 0x1403634: d0 = 0.400000
    //     0x1403634: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1403638: r0 = GDT[cid_x0 + -0xffa]()
    //     0x1403638: sub             lr, x0, #0xffa
    //     0x140363c: ldr             lr, [x21, lr, lsl #3]
    //     0x1403640: blr             lr
    // 0x1403644: r16 = 14.000000
    //     0x1403644: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1403648: ldr             x16, [x16, #0x1d8]
    // 0x140364c: stp             x16, x0, [SP]
    // 0x1403650: ldur            x1, [fp, #-0x58]
    // 0x1403654: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1403654: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1403658: ldr             x4, [x4, #0x9b8]
    // 0x140365c: r0 = copyWith()
    //     0x140365c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1403660: ldur            x2, [fp, #-8]
    // 0x1403664: stur            x0, [fp, #-0x58]
    // 0x1403668: LoadField: r1 = r2->field_f
    //     0x1403668: ldur            w1, [x2, #0xf]
    // 0x140366c: DecompressPointer r1
    //     0x140366c: add             x1, x1, HEAP, lsl #32
    // 0x1403670: r0 = controller()
    //     0x1403670: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1403674: mov             x1, x0
    // 0x1403678: r0 = paytmPaymentInitResponse()
    //     0x1403678: bl              #0x12d93bc  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::paytmPaymentInitResponse
    // 0x140367c: tbnz            w0, #4, #0x1403710
    // 0x1403680: ldur            x2, [fp, #-8]
    // 0x1403684: LoadField: r1 = r2->field_f
    //     0x1403684: ldur            w1, [x2, #0xf]
    // 0x1403688: DecompressPointer r1
    //     0x1403688: add             x1, x1, HEAP, lsl #32
    // 0x140368c: r0 = controller()
    //     0x140368c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1403690: LoadField: r1 = r0->field_b7
    //     0x1403690: ldur            w1, [x0, #0xb7]
    // 0x1403694: DecompressPointer r1
    //     0x1403694: add             x1, x1, HEAP, lsl #32
    // 0x1403698: r0 = value()
    //     0x1403698: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140369c: tbnz            w0, #4, #0x14036ac
    // 0x14036a0: r0 = Instance_IconData
    //     0x14036a0: add             x0, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0x14036a4: ldr             x0, [x0, #0x130]
    // 0x14036a8: b               #0x14036b4
    // 0x14036ac: r0 = Instance_IconData
    //     0x14036ac: add             x0, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0x14036b0: ldr             x0, [x0, #0x138]
    // 0x14036b4: ldur            x2, [fp, #-8]
    // 0x14036b8: stur            x0, [fp, #-0x60]
    // 0x14036bc: LoadField: r1 = r2->field_f
    //     0x14036bc: ldur            w1, [x2, #0xf]
    // 0x14036c0: DecompressPointer r1
    //     0x14036c0: add             x1, x1, HEAP, lsl #32
    // 0x14036c4: r0 = controller()
    //     0x14036c4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14036c8: mov             x1, x0
    // 0x14036cc: r0 = isShowBag()
    //     0x14036cc: bl              #0x12d8848  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::isShowBag
    // 0x14036d0: tbnz            w0, #4, #0x14036e0
    // 0x14036d4: r1 = Instance_Color
    //     0x14036d4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x14036d8: ldr             x1, [x1, #0x858]
    // 0x14036dc: b               #0x14036e8
    // 0x14036e0: r1 = Instance_Color
    //     0x14036e0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0x14036e4: ldr             x1, [x1, #0x50]
    // 0x14036e8: ldur            x0, [fp, #-0x60]
    // 0x14036ec: stur            x1, [fp, #-0x68]
    // 0x14036f0: r0 = Icon()
    //     0x14036f0: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x14036f4: mov             x1, x0
    // 0x14036f8: ldur            x0, [fp, #-0x60]
    // 0x14036fc: StoreField: r1->field_b = r0
    //     0x14036fc: stur            w0, [x1, #0xb]
    // 0x1403700: ldur            x0, [fp, #-0x68]
    // 0x1403704: StoreField: r1->field_23 = r0
    //     0x1403704: stur            w0, [x1, #0x23]
    // 0x1403708: mov             x3, x1
    // 0x140370c: b               #0x1403714
    // 0x1403710: r3 = Null
    //     0x1403710: mov             x3, NULL
    // 0x1403714: ldur            x2, [fp, #-8]
    // 0x1403718: ldur            x0, [fp, #-0x30]
    // 0x140371c: ldur            x1, [fp, #-0x18]
    // 0x1403720: stur            x3, [fp, #-0x60]
    // 0x1403724: r0 = SvgPicture()
    //     0x1403724: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x1403728: mov             x1, x0
    // 0x140372c: r2 = "assets/images/whatsapp_icon_seeklogo.svg"
    //     0x140372c: add             x2, PP, #0x37, lsl #12  ; [pp+0x37140] "assets/images/whatsapp_icon_seeklogo.svg"
    //     0x1403730: ldr             x2, [x2, #0x140]
    // 0x1403734: stur            x0, [fp, #-0x68]
    // 0x1403738: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x1403738: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x140373c: r0 = SvgPicture.asset()
    //     0x140373c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x1403740: ldur            x2, [fp, #-8]
    // 0x1403744: LoadField: r1 = r2->field_13
    //     0x1403744: ldur            w1, [x2, #0x13]
    // 0x1403748: DecompressPointer r1
    //     0x1403748: add             x1, x1, HEAP, lsl #32
    // 0x140374c: r0 = of()
    //     0x140374c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1403750: LoadField: r1 = r0->field_87
    //     0x1403750: ldur            w1, [x0, #0x87]
    // 0x1403754: DecompressPointer r1
    //     0x1403754: add             x1, x1, HEAP, lsl #32
    // 0x1403758: LoadField: r0 = r1->field_2b
    //     0x1403758: ldur            w0, [x1, #0x2b]
    // 0x140375c: DecompressPointer r0
    //     0x140375c: add             x0, x0, HEAP, lsl #32
    // 0x1403760: ldur            x2, [fp, #-8]
    // 0x1403764: stur            x0, [fp, #-0x70]
    // 0x1403768: LoadField: r1 = r2->field_13
    //     0x1403768: ldur            w1, [x2, #0x13]
    // 0x140376c: DecompressPointer r1
    //     0x140376c: add             x1, x1, HEAP, lsl #32
    // 0x1403770: r0 = of()
    //     0x1403770: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1403774: LoadField: r1 = r0->field_5b
    //     0x1403774: ldur            w1, [x0, #0x5b]
    // 0x1403778: DecompressPointer r1
    //     0x1403778: add             x1, x1, HEAP, lsl #32
    // 0x140377c: r16 = 14.000000
    //     0x140377c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1403780: ldr             x16, [x16, #0x1d8]
    // 0x1403784: stp             x1, x16, [SP]
    // 0x1403788: ldur            x1, [fp, #-0x70]
    // 0x140378c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x140378c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1403790: ldr             x4, [x4, #0xaa0]
    // 0x1403794: r0 = copyWith()
    //     0x1403794: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1403798: stur            x0, [fp, #-0x70]
    // 0x140379c: r0 = Text()
    //     0x140379c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14037a0: mov             x3, x0
    // 0x14037a4: r0 = "+91 "
    //     0x14037a4: add             x0, PP, #0x37, lsl #12  ; [pp+0x37148] "+91 "
    //     0x14037a8: ldr             x0, [x0, #0x148]
    // 0x14037ac: stur            x3, [fp, #-0x78]
    // 0x14037b0: StoreField: r3->field_b = r0
    //     0x14037b0: stur            w0, [x3, #0xb]
    // 0x14037b4: ldur            x0, [fp, #-0x70]
    // 0x14037b8: StoreField: r3->field_13 = r0
    //     0x14037b8: stur            w0, [x3, #0x13]
    // 0x14037bc: r1 = Null
    //     0x14037bc: mov             x1, NULL
    // 0x14037c0: r2 = 4
    //     0x14037c0: movz            x2, #0x4
    // 0x14037c4: r0 = AllocateArray()
    //     0x14037c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14037c8: mov             x2, x0
    // 0x14037cc: ldur            x0, [fp, #-0x68]
    // 0x14037d0: stur            x2, [fp, #-0x70]
    // 0x14037d4: StoreField: r2->field_f = r0
    //     0x14037d4: stur            w0, [x2, #0xf]
    // 0x14037d8: ldur            x0, [fp, #-0x78]
    // 0x14037dc: StoreField: r2->field_13 = r0
    //     0x14037dc: stur            w0, [x2, #0x13]
    // 0x14037e0: r1 = <Widget>
    //     0x14037e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14037e4: r0 = AllocateGrowableArray()
    //     0x14037e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14037e8: mov             x1, x0
    // 0x14037ec: ldur            x0, [fp, #-0x70]
    // 0x14037f0: stur            x1, [fp, #-0x68]
    // 0x14037f4: StoreField: r1->field_f = r0
    //     0x14037f4: stur            w0, [x1, #0xf]
    // 0x14037f8: r2 = 4
    //     0x14037f8: movz            x2, #0x4
    // 0x14037fc: StoreField: r1->field_b = r2
    //     0x14037fc: stur            w2, [x1, #0xb]
    // 0x1403800: r0 = Row()
    //     0x1403800: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1403804: mov             x1, x0
    // 0x1403808: r0 = Instance_Axis
    //     0x1403808: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x140380c: stur            x1, [fp, #-0x70]
    // 0x1403810: StoreField: r1->field_f = r0
    //     0x1403810: stur            w0, [x1, #0xf]
    // 0x1403814: r0 = Instance_MainAxisAlignment
    //     0x1403814: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1403818: ldr             x0, [x0, #0xa08]
    // 0x140381c: StoreField: r1->field_13 = r0
    //     0x140381c: stur            w0, [x1, #0x13]
    // 0x1403820: r2 = Instance_MainAxisSize
    //     0x1403820: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x1403824: ldr             x2, [x2, #0xdd0]
    // 0x1403828: ArrayStore: r1[0] = r2  ; List_4
    //     0x1403828: stur            w2, [x1, #0x17]
    // 0x140382c: r2 = Instance_CrossAxisAlignment
    //     0x140382c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1403830: ldr             x2, [x2, #0xa18]
    // 0x1403834: StoreField: r1->field_1b = r2
    //     0x1403834: stur            w2, [x1, #0x1b]
    // 0x1403838: r2 = Instance_VerticalDirection
    //     0x1403838: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x140383c: ldr             x2, [x2, #0xa20]
    // 0x1403840: StoreField: r1->field_23 = r2
    //     0x1403840: stur            w2, [x1, #0x23]
    // 0x1403844: r3 = Instance_Clip
    //     0x1403844: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1403848: ldr             x3, [x3, #0x38]
    // 0x140384c: StoreField: r1->field_2b = r3
    //     0x140384c: stur            w3, [x1, #0x2b]
    // 0x1403850: StoreField: r1->field_2f = rZR
    //     0x1403850: stur            xzr, [x1, #0x2f]
    // 0x1403854: ldur            x4, [fp, #-0x68]
    // 0x1403858: StoreField: r1->field_b = r4
    //     0x1403858: stur            w4, [x1, #0xb]
    // 0x140385c: r0 = Padding()
    //     0x140385c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1403860: mov             x1, x0
    // 0x1403864: r0 = Instance_EdgeInsets
    //     0x1403864: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0x1403868: ldr             x0, [x0, #0xc40]
    // 0x140386c: stur            x1, [fp, #-0x68]
    // 0x1403870: StoreField: r1->field_f = r0
    //     0x1403870: stur            w0, [x1, #0xf]
    // 0x1403874: ldur            x0, [fp, #-0x70]
    // 0x1403878: StoreField: r1->field_b = r0
    //     0x1403878: stur            w0, [x1, #0xb]
    // 0x140387c: r0 = Align()
    //     0x140387c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x1403880: mov             x1, x0
    // 0x1403884: r0 = Instance_Alignment
    //     0x1403884: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1403888: ldr             x0, [x0, #0xb10]
    // 0x140388c: stur            x1, [fp, #-0x70]
    // 0x1403890: StoreField: r1->field_f = r0
    //     0x1403890: stur            w0, [x1, #0xf]
    // 0x1403894: r0 = 1.000000
    //     0x1403894: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x1403898: StoreField: r1->field_13 = r0
    //     0x1403898: stur            w0, [x1, #0x13]
    // 0x140389c: ArrayStore: r1[0] = r0  ; List_4
    //     0x140389c: stur            w0, [x1, #0x17]
    // 0x14038a0: ldur            x0, [fp, #-0x68]
    // 0x14038a4: StoreField: r1->field_b = r0
    //     0x14038a4: stur            w0, [x1, #0xb]
    // 0x14038a8: r0 = Padding()
    //     0x14038a8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14038ac: mov             x2, x0
    // 0x14038b0: r0 = Instance_EdgeInsets
    //     0x14038b0: add             x0, PP, #0x34, lsl #12  ; [pp+0x340e0] Obj!EdgeInsets@d57051
    //     0x14038b4: ldr             x0, [x0, #0xe0]
    // 0x14038b8: stur            x2, [fp, #-0x68]
    // 0x14038bc: StoreField: r2->field_f = r0
    //     0x14038bc: stur            w0, [x2, #0xf]
    // 0x14038c0: ldur            x0, [fp, #-0x70]
    // 0x14038c4: StoreField: r2->field_b = r0
    //     0x14038c4: stur            w0, [x2, #0xb]
    // 0x14038c8: ldur            x0, [fp, #-8]
    // 0x14038cc: LoadField: r1 = r0->field_13
    //     0x14038cc: ldur            w1, [x0, #0x13]
    // 0x14038d0: DecompressPointer r1
    //     0x14038d0: add             x1, x1, HEAP, lsl #32
    // 0x14038d4: r0 = of()
    //     0x14038d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14038d8: LoadField: r1 = r0->field_87
    //     0x14038d8: ldur            w1, [x0, #0x87]
    // 0x14038dc: DecompressPointer r1
    //     0x14038dc: add             x1, x1, HEAP, lsl #32
    // 0x14038e0: LoadField: r0 = r1->field_2b
    //     0x14038e0: ldur            w0, [x1, #0x2b]
    // 0x14038e4: DecompressPointer r0
    //     0x14038e4: add             x0, x0, HEAP, lsl #32
    // 0x14038e8: r16 = 12.000000
    //     0x14038e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14038ec: ldr             x16, [x16, #0x9e8]
    // 0x14038f0: r30 = Instance_MaterialColor
    //     0x14038f0: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0x14038f4: ldr             lr, [lr, #0x180]
    // 0x14038f8: stp             lr, x16, [SP]
    // 0x14038fc: mov             x1, x0
    // 0x1403900: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1403900: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1403904: ldr             x4, [x4, #0xaa0]
    // 0x1403908: r0 = copyWith()
    //     0x1403908: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x140390c: r16 = Instance_EdgeInsets
    //     0x140390c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0x1403910: ldr             x16, [x16, #0xc40]
    // 0x1403914: r30 = "Enter Whatsapp no."
    //     0x1403914: add             lr, PP, #0x37, lsl #12  ; [pp+0x37150] "Enter Whatsapp no."
    //     0x1403918: ldr             lr, [lr, #0x150]
    // 0x140391c: stp             lr, x16, [SP, #0x20]
    // 0x1403920: ldur            x16, [fp, #-0x58]
    // 0x1403924: ldur            lr, [fp, #-0x60]
    // 0x1403928: stp             lr, x16, [SP, #0x10]
    // 0x140392c: ldur            x16, [fp, #-0x68]
    // 0x1403930: stp             x0, x16, [SP]
    // 0x1403934: ldur            x1, [fp, #-0x50]
    // 0x1403938: r4 = const [0, 0x7, 0x6, 0x1, contentPadding, 0x1, errorStyle, 0x6, labelStyle, 0x3, labelText, 0x2, prefixIcon, 0x5, suffixIcon, 0x4, null]
    //     0x1403938: add             x4, PP, #0x37, lsl #12  ; [pp+0x37158] List(17) [0, 0x7, 0x6, 0x1, "contentPadding", 0x1, "errorStyle", 0x6, "labelStyle", 0x3, "labelText", 0x2, "prefixIcon", 0x5, "suffixIcon", 0x4, Null]
    //     0x140393c: ldr             x4, [x4, #0x158]
    // 0x1403940: r0 = copyWith()
    //     0x1403940: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0x1403944: ldur            x2, [fp, #-0x20]
    // 0x1403948: r1 = Function '_validatePhoneNumber@1711185970':.
    //     0x1403948: add             x1, PP, #0x37, lsl #12  ; [pp+0x37160] AnonymousClosure: (0x1404d04), in [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::_validatePhoneNumber (0x1404d40)
    //     0x140394c: ldr             x1, [x1, #0x160]
    // 0x1403950: stur            x0, [fp, #-0x20]
    // 0x1403954: r0 = AllocateClosure()
    //     0x1403954: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1403958: ldur            x2, [fp, #-8]
    // 0x140395c: r1 = Function '<anonymous closure>':.
    //     0x140395c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37168] AnonymousClosure: (0x1404c8c), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::_getLineThemeLoginForm (0x1404eac)
    //     0x1403960: ldr             x1, [x1, #0x168]
    // 0x1403964: stur            x0, [fp, #-0x50]
    // 0x1403968: r0 = AllocateClosure()
    //     0x1403968: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140396c: ldur            x2, [fp, #-8]
    // 0x1403970: r1 = Function '<anonymous closure>':.
    //     0x1403970: add             x1, PP, #0x37, lsl #12  ; [pp+0x37170] AnonymousClosure: (0x1404a84), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::_getLineThemeLoginForm (0x1404eac)
    //     0x1403974: ldr             x1, [x1, #0x170]
    // 0x1403978: stur            x0, [fp, #-0x58]
    // 0x140397c: r0 = AllocateClosure()
    //     0x140397c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1403980: r1 = <String>
    //     0x1403980: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x1403984: stur            x0, [fp, #-0x60]
    // 0x1403988: r0 = TextFormField()
    //     0x1403988: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0x140398c: stur            x0, [fp, #-0x68]
    // 0x1403990: r16 = true
    //     0x1403990: add             x16, NULL, #0x20  ; true
    // 0x1403994: ldur            lr, [fp, #-0x50]
    // 0x1403998: stp             lr, x16, [SP, #0x40]
    // 0x140399c: r16 = Instance_AutovalidateMode
    //     0x140399c: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0x14039a0: ldr             x16, [x16, #0x7e8]
    // 0x14039a4: r30 = false
    //     0x14039a4: add             lr, NULL, #0x30  ; false
    // 0x14039a8: stp             lr, x16, [SP, #0x30]
    // 0x14039ac: ldur            x16, [fp, #-0x58]
    // 0x14039b0: ldur            lr, [fp, #-0x40]
    // 0x14039b4: stp             lr, x16, [SP, #0x20]
    // 0x14039b8: r16 = Instance_TextInputType
    //     0x14039b8: add             x16, PP, #0x37, lsl #12  ; [pp+0x37178] Obj!TextInputType@d55be1
    //     0x14039bc: ldr             x16, [x16, #0x178]
    // 0x14039c0: r30 = 2
    //     0x14039c0: movz            lr, #0x2
    // 0x14039c4: stp             lr, x16, [SP, #0x10]
    // 0x14039c8: ldur            x16, [fp, #-0x60]
    // 0x14039cc: ldur            lr, [fp, #-0x48]
    // 0x14039d0: stp             lr, x16, [SP]
    // 0x14039d4: mov             x1, x0
    // 0x14039d8: ldur            x2, [fp, #-0x20]
    // 0x14039dc: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x2, autovalidateMode, 0x4, enableSuggestions, 0x5, inputFormatters, 0x7, keyboardType, 0x8, maxLines, 0x9, onChanged, 0xa, onFieldSubmitted, 0x6, style, 0xb, validator, 0x3, null]
    //     0x14039dc: add             x4, PP, #0x37, lsl #12  ; [pp+0x37180] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x2, "autovalidateMode", 0x4, "enableSuggestions", 0x5, "inputFormatters", 0x7, "keyboardType", 0x8, "maxLines", 0x9, "onChanged", 0xa, "onFieldSubmitted", 0x6, "style", 0xb, "validator", 0x3, Null]
    //     0x14039e0: ldr             x4, [x4, #0x180]
    // 0x14039e4: r0 = TextFormField()
    //     0x14039e4: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0x14039e8: r0 = Form()
    //     0x14039e8: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0x14039ec: mov             x1, x0
    // 0x14039f0: ldur            x0, [fp, #-0x68]
    // 0x14039f4: stur            x1, [fp, #-0x20]
    // 0x14039f8: StoreField: r1->field_b = r0
    //     0x14039f8: stur            w0, [x1, #0xb]
    // 0x14039fc: r0 = Instance_AutovalidateMode
    //     0x14039fc: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0x1403a00: ldr             x0, [x0, #0x800]
    // 0x1403a04: StoreField: r1->field_23 = r0
    //     0x1403a04: stur            w0, [x1, #0x23]
    // 0x1403a08: ldur            x0, [fp, #-0x18]
    // 0x1403a0c: StoreField: r1->field_7 = r0
    //     0x1403a0c: stur            w0, [x1, #7]
    // 0x1403a10: r0 = Padding()
    //     0x1403a10: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1403a14: mov             x1, x0
    // 0x1403a18: r0 = Instance_EdgeInsets
    //     0x1403a18: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1403a1c: ldr             x0, [x0, #0x1f0]
    // 0x1403a20: stur            x1, [fp, #-0x18]
    // 0x1403a24: StoreField: r1->field_f = r0
    //     0x1403a24: stur            w0, [x1, #0xf]
    // 0x1403a28: ldur            x0, [fp, #-0x20]
    // 0x1403a2c: StoreField: r1->field_b = r0
    //     0x1403a2c: stur            w0, [x1, #0xb]
    // 0x1403a30: r0 = Visibility()
    //     0x1403a30: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1403a34: mov             x2, x0
    // 0x1403a38: ldur            x0, [fp, #-0x18]
    // 0x1403a3c: stur            x2, [fp, #-0x20]
    // 0x1403a40: StoreField: r2->field_b = r0
    //     0x1403a40: stur            w0, [x2, #0xb]
    // 0x1403a44: r0 = Instance_SizedBox
    //     0x1403a44: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1403a48: StoreField: r2->field_f = r0
    //     0x1403a48: stur            w0, [x2, #0xf]
    // 0x1403a4c: ldur            x1, [fp, #-0x30]
    // 0x1403a50: StoreField: r2->field_13 = r1
    //     0x1403a50: stur            w1, [x2, #0x13]
    // 0x1403a54: r3 = false
    //     0x1403a54: add             x3, NULL, #0x30  ; false
    // 0x1403a58: ArrayStore: r2[0] = r3  ; List_4
    //     0x1403a58: stur            w3, [x2, #0x17]
    // 0x1403a5c: StoreField: r2->field_1b = r3
    //     0x1403a5c: stur            w3, [x2, #0x1b]
    // 0x1403a60: StoreField: r2->field_1f = r3
    //     0x1403a60: stur            w3, [x2, #0x1f]
    // 0x1403a64: StoreField: r2->field_23 = r3
    //     0x1403a64: stur            w3, [x2, #0x23]
    // 0x1403a68: StoreField: r2->field_27 = r3
    //     0x1403a68: stur            w3, [x2, #0x27]
    // 0x1403a6c: StoreField: r2->field_2b = r3
    //     0x1403a6c: stur            w3, [x2, #0x2b]
    // 0x1403a70: ldur            x4, [fp, #-8]
    // 0x1403a74: LoadField: r1 = r4->field_f
    //     0x1403a74: ldur            w1, [x4, #0xf]
    // 0x1403a78: DecompressPointer r1
    //     0x1403a78: add             x1, x1, HEAP, lsl #32
    // 0x1403a7c: r0 = controller()
    //     0x1403a7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1403a80: LoadField: r1 = r0->field_8b
    //     0x1403a80: ldur            w1, [x0, #0x8b]
    // 0x1403a84: DecompressPointer r1
    //     0x1403a84: add             x1, x1, HEAP, lsl #32
    // 0x1403a88: r0 = value()
    //     0x1403a88: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1403a8c: cmp             w0, NULL
    // 0x1403a90: b.ne            #0x1403a9c
    // 0x1403a94: r1 = true
    //     0x1403a94: add             x1, NULL, #0x20  ; true
    // 0x1403a98: b               #0x1403aa0
    // 0x1403a9c: mov             x1, x0
    // 0x1403aa0: ldur            x2, [fp, #-8]
    // 0x1403aa4: ldur            x0, [fp, #-0x38]
    // 0x1403aa8: stur            x1, [fp, #-0x18]
    // 0x1403aac: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1403aac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1403ab0: ldr             x0, [x0, #0x1c80]
    //     0x1403ab4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1403ab8: cmp             w0, w16
    //     0x1403abc: b.ne            #0x1403ac8
    //     0x1403ac0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1403ac4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1403ac8: r0 = GetNavigation.width()
    //     0x1403ac8: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x1403acc: stur            d0, [fp, #-0x80]
    // 0x1403ad0: r16 = "[0-9]"
    //     0x1403ad0: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0x1403ad4: ldr             x16, [x16, #0x128]
    // 0x1403ad8: stp             x16, NULL, [SP, #0x20]
    // 0x1403adc: r16 = false
    //     0x1403adc: add             x16, NULL, #0x30  ; false
    // 0x1403ae0: r30 = true
    //     0x1403ae0: add             lr, NULL, #0x20  ; true
    // 0x1403ae4: stp             lr, x16, [SP, #0x10]
    // 0x1403ae8: r16 = false
    //     0x1403ae8: add             x16, NULL, #0x30  ; false
    // 0x1403aec: r30 = false
    //     0x1403aec: add             lr, NULL, #0x30  ; false
    // 0x1403af0: stp             lr, x16, [SP]
    // 0x1403af4: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x1403af4: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x1403af8: r0 = _RegExp()
    //     0x1403af8: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0x1403afc: stur            x0, [fp, #-0x30]
    // 0x1403b00: r0 = FilteringTextInputFormatter()
    //     0x1403b00: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0x1403b04: mov             x3, x0
    // 0x1403b08: ldur            x0, [fp, #-0x30]
    // 0x1403b0c: stur            x3, [fp, #-0x40]
    // 0x1403b10: StoreField: r3->field_b = r0
    //     0x1403b10: stur            w0, [x3, #0xb]
    // 0x1403b14: r0 = true
    //     0x1403b14: add             x0, NULL, #0x20  ; true
    // 0x1403b18: StoreField: r3->field_7 = r0
    //     0x1403b18: stur            w0, [x3, #7]
    // 0x1403b1c: r1 = ""
    //     0x1403b1c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1403b20: StoreField: r3->field_f = r1
    //     0x1403b20: stur            w1, [x3, #0xf]
    // 0x1403b24: r1 = Null
    //     0x1403b24: mov             x1, NULL
    // 0x1403b28: r2 = 4
    //     0x1403b28: movz            x2, #0x4
    // 0x1403b2c: r0 = AllocateArray()
    //     0x1403b2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1403b30: mov             x2, x0
    // 0x1403b34: ldur            x0, [fp, #-0x38]
    // 0x1403b38: stur            x2, [fp, #-0x30]
    // 0x1403b3c: StoreField: r2->field_f = r0
    //     0x1403b3c: stur            w0, [x2, #0xf]
    // 0x1403b40: ldur            x0, [fp, #-0x40]
    // 0x1403b44: StoreField: r2->field_13 = r0
    //     0x1403b44: stur            w0, [x2, #0x13]
    // 0x1403b48: r1 = <TextInputFormatter>
    //     0x1403b48: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0x1403b4c: ldr             x1, [x1, #0x7b0]
    // 0x1403b50: r0 = AllocateGrowableArray()
    //     0x1403b50: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1403b54: mov             x2, x0
    // 0x1403b58: ldur            x0, [fp, #-0x30]
    // 0x1403b5c: stur            x2, [fp, #-0x38]
    // 0x1403b60: StoreField: r2->field_f = r0
    //     0x1403b60: stur            w0, [x2, #0xf]
    // 0x1403b64: r0 = 4
    //     0x1403b64: movz            x0, #0x4
    // 0x1403b68: StoreField: r2->field_b = r0
    //     0x1403b68: stur            w0, [x2, #0xb]
    // 0x1403b6c: ldur            x0, [fp, #-8]
    // 0x1403b70: LoadField: r1 = r0->field_13
    //     0x1403b70: ldur            w1, [x0, #0x13]
    // 0x1403b74: DecompressPointer r1
    //     0x1403b74: add             x1, x1, HEAP, lsl #32
    // 0x1403b78: r0 = of()
    //     0x1403b78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1403b7c: LoadField: r1 = r0->field_87
    //     0x1403b7c: ldur            w1, [x0, #0x87]
    // 0x1403b80: DecompressPointer r1
    //     0x1403b80: add             x1, x1, HEAP, lsl #32
    // 0x1403b84: LoadField: r0 = r1->field_7
    //     0x1403b84: ldur            w0, [x1, #7]
    // 0x1403b88: DecompressPointer r0
    //     0x1403b88: add             x0, x0, HEAP, lsl #32
    // 0x1403b8c: ldur            x2, [fp, #-8]
    // 0x1403b90: stur            x0, [fp, #-0x30]
    // 0x1403b94: LoadField: r1 = r2->field_13
    //     0x1403b94: ldur            w1, [x2, #0x13]
    // 0x1403b98: DecompressPointer r1
    //     0x1403b98: add             x1, x1, HEAP, lsl #32
    // 0x1403b9c: r0 = of()
    //     0x1403b9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1403ba0: LoadField: r1 = r0->field_5b
    //     0x1403ba0: ldur            w1, [x0, #0x5b]
    // 0x1403ba4: DecompressPointer r1
    //     0x1403ba4: add             x1, x1, HEAP, lsl #32
    // 0x1403ba8: r16 = 14.000000
    //     0x1403ba8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1403bac: ldr             x16, [x16, #0x1d8]
    // 0x1403bb0: stp             x16, x1, [SP]
    // 0x1403bb4: ldur            x1, [fp, #-0x30]
    // 0x1403bb8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1403bb8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1403bbc: ldr             x4, [x4, #0x9b8]
    // 0x1403bc0: r0 = copyWith()
    //     0x1403bc0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1403bc4: ldur            x2, [fp, #-8]
    // 0x1403bc8: stur            x0, [fp, #-0x30]
    // 0x1403bcc: LoadField: r1 = r2->field_13
    //     0x1403bcc: ldur            w1, [x2, #0x13]
    // 0x1403bd0: DecompressPointer r1
    //     0x1403bd0: add             x1, x1, HEAP, lsl #32
    // 0x1403bd4: r0 = of()
    //     0x1403bd4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1403bd8: LoadField: r1 = r0->field_5b
    //     0x1403bd8: ldur            w1, [x0, #0x5b]
    // 0x1403bdc: DecompressPointer r1
    //     0x1403bdc: add             x1, x1, HEAP, lsl #32
    // 0x1403be0: r0 = LoadClassIdInstr(r1)
    //     0x1403be0: ldur            x0, [x1, #-1]
    //     0x1403be4: ubfx            x0, x0, #0xc, #0x14
    // 0x1403be8: d0 = 0.100000
    //     0x1403be8: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x1403bec: r0 = GDT[cid_x0 + -0xffa]()
    //     0x1403bec: sub             lr, x0, #0xffa
    //     0x1403bf0: ldr             lr, [x21, lr, lsl #3]
    //     0x1403bf4: blr             lr
    // 0x1403bf8: r1 = <Color>
    //     0x1403bf8: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1403bfc: ldr             x1, [x1, #0xf80]
    // 0x1403c00: stur            x0, [fp, #-0x40]
    // 0x1403c04: r0 = FixedColorBuilder()
    //     0x1403c04: bl              #0xa0985c  ; AllocateFixedColorBuilderStub -> FixedColorBuilder (size=0x10)
    // 0x1403c08: mov             x1, x0
    // 0x1403c0c: ldur            x0, [fp, #-0x40]
    // 0x1403c10: stur            x1, [fp, #-0x48]
    // 0x1403c14: StoreField: r1->field_b = r0
    //     0x1403c14: stur            w0, [x1, #0xb]
    // 0x1403c18: r0 = BoxLooseDecoration()
    //     0x1403c18: bl              #0xb4d24c  ; AllocateBoxLooseDecorationStub -> BoxLooseDecoration (size=0x48)
    // 0x1403c1c: stur            x0, [fp, #-0x40]
    // 0x1403c20: r16 = 1.000000
    //     0x1403c20: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x1403c24: str             x16, [SP]
    // 0x1403c28: mov             x1, x0
    // 0x1403c2c: ldur            x2, [fp, #-0x48]
    // 0x1403c30: ldur            x3, [fp, #-0x30]
    // 0x1403c34: r4 = const [0, 0x4, 0x1, 0x3, strokeWidth, 0x3, null]
    //     0x1403c34: add             x4, PP, #0x37, lsl #12  ; [pp+0x37188] List(7) [0, 0x4, 0x1, 0x3, "strokeWidth", 0x3, Null]
    //     0x1403c38: ldr             x4, [x4, #0x188]
    // 0x1403c3c: r0 = BoxLooseDecoration()
    //     0x1403c3c: bl              #0xb4cebc  ; [package:pin_input_text_field/src/decoration/pin_decoration.dart] BoxLooseDecoration::BoxLooseDecoration
    // 0x1403c40: ldur            x2, [fp, #-8]
    // 0x1403c44: LoadField: r1 = r2->field_13
    //     0x1403c44: ldur            w1, [x2, #0x13]
    // 0x1403c48: DecompressPointer r1
    //     0x1403c48: add             x1, x1, HEAP, lsl #32
    // 0x1403c4c: r0 = of()
    //     0x1403c4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1403c50: LoadField: r1 = r0->field_5b
    //     0x1403c50: ldur            w1, [x0, #0x5b]
    // 0x1403c54: DecompressPointer r1
    //     0x1403c54: add             x1, x1, HEAP, lsl #32
    // 0x1403c58: stur            x1, [fp, #-0x30]
    // 0x1403c5c: r0 = Cursor()
    //     0x1403c5c: bl              #0xa09868  ; AllocateCursorStub -> Cursor (size=0x3c)
    // 0x1403c60: d0 = 1.000000
    //     0x1403c60: fmov            d0, #1.00000000
    // 0x1403c64: stur            x0, [fp, #-0x48]
    // 0x1403c68: StoreField: r0->field_7 = d0
    //     0x1403c68: stur            d0, [x0, #7]
    // 0x1403c6c: d0 = 20.000000
    //     0x1403c6c: fmov            d0, #20.00000000
    // 0x1403c70: StoreField: r0->field_f = d0
    //     0x1403c70: stur            d0, [x0, #0xf]
    // 0x1403c74: r1 = Instance_Radius
    //     0x1403c74: add             x1, PP, #0x37, lsl #12  ; [pp+0x37190] Obj!Radius@d6bf21
    //     0x1403c78: ldr             x1, [x1, #0x190]
    // 0x1403c7c: ArrayStore: r0[0] = r1  ; List_4
    //     0x1403c7c: stur            w1, [x0, #0x17]
    // 0x1403c80: ldur            x1, [fp, #-0x30]
    // 0x1403c84: StoreField: r0->field_1b = r1
    //     0x1403c84: stur            w1, [x0, #0x1b]
    // 0x1403c88: r1 = Instance_Duration
    //     0x1403c88: add             x1, PP, #0xa, lsl #12  ; [pp+0xa058] Obj!Duration@d777a1
    //     0x1403c8c: ldr             x1, [x1, #0x58]
    // 0x1403c90: StoreField: r0->field_1f = r1
    //     0x1403c90: stur            w1, [x0, #0x1f]
    // 0x1403c94: r1 = Instance_Duration
    //     0x1403c94: ldr             x1, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0x1403c98: StoreField: r0->field_23 = r1
    //     0x1403c98: stur            w1, [x0, #0x23]
    // 0x1403c9c: r1 = Instance_Duration
    //     0x1403c9c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e4b8] Obj!Duration@d77851
    //     0x1403ca0: ldr             x1, [x1, #0x4b8]
    // 0x1403ca4: StoreField: r0->field_27 = r1
    //     0x1403ca4: stur            w1, [x0, #0x27]
    // 0x1403ca8: r1 = Instance_Orientation
    //     0x1403ca8: add             x1, PP, #0x37, lsl #12  ; [pp+0x37198] Obj!Orientation@d70241
    //     0x1403cac: ldr             x1, [x1, #0x198]
    // 0x1403cb0: StoreField: r0->field_2f = r1
    //     0x1403cb0: stur            w1, [x0, #0x2f]
    // 0x1403cb4: StoreField: r0->field_33 = rZR
    //     0x1403cb4: stur            xzr, [x0, #0x33]
    // 0x1403cb8: r2 = true
    //     0x1403cb8: add             x2, NULL, #0x20  ; true
    // 0x1403cbc: StoreField: r0->field_2b = r2
    //     0x1403cbc: stur            w2, [x0, #0x2b]
    // 0x1403cc0: ldur            x3, [fp, #-8]
    // 0x1403cc4: LoadField: r1 = r3->field_f
    //     0x1403cc4: ldur            w1, [x3, #0xf]
    // 0x1403cc8: DecompressPointer r1
    //     0x1403cc8: add             x1, x1, HEAP, lsl #32
    // 0x1403ccc: r0 = controller()
    //     0x1403ccc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1403cd0: LoadField: r1 = r0->field_7f
    //     0x1403cd0: ldur            w1, [x0, #0x7f]
    // 0x1403cd4: DecompressPointer r1
    //     0x1403cd4: add             x1, x1, HEAP, lsl #32
    // 0x1403cd8: r0 = value()
    //     0x1403cd8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1403cdc: stur            x0, [fp, #-0x30]
    // 0x1403ce0: r0 = PinFieldAutoFill()
    //     0x1403ce0: bl              #0xa09844  ; AllocatePinFieldAutoFillStub -> PinFieldAutoFill (size=0x4c)
    // 0x1403ce4: mov             x3, x0
    // 0x1403ce8: r0 = Instance_TextInputType
    //     0x1403ce8: add             x0, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0x1403cec: ldr             x0, [x0, #0x1a0]
    // 0x1403cf0: stur            x3, [fp, #-0x50]
    // 0x1403cf4: StoreField: r3->field_33 = r0
    //     0x1403cf4: stur            w0, [x3, #0x33]
    // 0x1403cf8: r0 = Instance_TextInputAction
    //     0x1403cf8: ldr             x0, [PP, #0x70c0]  ; [pp+0x70c0] Obj!TextInputAction@d728e1
    // 0x1403cfc: StoreField: r3->field_37 = r0
    //     0x1403cfc: stur            w0, [x3, #0x37]
    // 0x1403d00: ldur            x0, [fp, #-0x48]
    // 0x1403d04: StoreField: r3->field_2f = r0
    //     0x1403d04: stur            w0, [x3, #0x2f]
    // 0x1403d08: ldur            x0, [fp, #-0x38]
    // 0x1403d0c: StoreField: r3->field_47 = r0
    //     0x1403d0c: stur            w0, [x3, #0x47]
    // 0x1403d10: r0 = true
    //     0x1403d10: add             x0, NULL, #0x20  ; true
    // 0x1403d14: StoreField: r3->field_3b = r0
    //     0x1403d14: stur            w0, [x3, #0x3b]
    // 0x1403d18: StoreField: r3->field_3f = r0
    //     0x1403d18: stur            w0, [x3, #0x3f]
    // 0x1403d1c: ldur            x1, [fp, #-0x40]
    // 0x1403d20: StoreField: r3->field_27 = r1
    //     0x1403d20: stur            w1, [x3, #0x27]
    // 0x1403d24: ldur            x2, [fp, #-8]
    // 0x1403d28: r1 = Function '<anonymous closure>':.
    //     0x1403d28: add             x1, PP, #0x37, lsl #12  ; [pp+0x371a8] AnonymousClosure: (0x1404280), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::_getLineThemeLoginForm (0x1404eac)
    //     0x1403d2c: ldr             x1, [x1, #0x1a8]
    // 0x1403d30: r0 = AllocateClosure()
    //     0x1403d30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1403d34: mov             x1, x0
    // 0x1403d38: ldur            x0, [fp, #-0x50]
    // 0x1403d3c: StoreField: r0->field_1f = r1
    //     0x1403d3c: stur            w1, [x0, #0x1f]
    // 0x1403d40: ldur            x2, [fp, #-8]
    // 0x1403d44: r1 = Function '<anonymous closure>':.
    //     0x1403d44: add             x1, PP, #0x37, lsl #12  ; [pp+0x371b0] AnonymousClosure: (0x14040e0), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::_getLineThemeLoginForm (0x1404eac)
    //     0x1403d48: ldr             x1, [x1, #0x1b0]
    // 0x1403d4c: r0 = AllocateClosure()
    //     0x1403d4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1403d50: mov             x1, x0
    // 0x1403d54: ldur            x0, [fp, #-0x50]
    // 0x1403d58: StoreField: r0->field_23 = r1
    //     0x1403d58: stur            w1, [x0, #0x23]
    // 0x1403d5c: ldur            x1, [fp, #-0x30]
    // 0x1403d60: StoreField: r0->field_1b = r1
    //     0x1403d60: stur            w1, [x0, #0x1b]
    // 0x1403d64: r1 = false
    //     0x1403d64: add             x1, NULL, #0x30  ; false
    // 0x1403d68: StoreField: r0->field_13 = r1
    //     0x1403d68: stur            w1, [x0, #0x13]
    // 0x1403d6c: r2 = 4
    //     0x1403d6c: movz            x2, #0x4
    // 0x1403d70: StoreField: r0->field_b = r2
    //     0x1403d70: stur            x2, [x0, #0xb]
    // 0x1403d74: ldur            d0, [fp, #-0x80]
    // 0x1403d78: r2 = inline_Allocate_Double()
    //     0x1403d78: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x1403d7c: add             x2, x2, #0x10
    //     0x1403d80: cmp             x3, x2
    //     0x1403d84: b.ls            #0x14040c4
    //     0x1403d88: str             x2, [THR, #0x50]  ; THR::top
    //     0x1403d8c: sub             x2, x2, #0xf
    //     0x1403d90: movz            x3, #0xe15c
    //     0x1403d94: movk            x3, #0x3, lsl #16
    //     0x1403d98: stur            x3, [x2, #-1]
    // 0x1403d9c: StoreField: r2->field_7 = d0
    //     0x1403d9c: stur            d0, [x2, #7]
    // 0x1403da0: stur            x2, [fp, #-0x30]
    // 0x1403da4: r0 = Container()
    //     0x1403da4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1403da8: stur            x0, [fp, #-0x38]
    // 0x1403dac: r16 = Instance_EdgeInsets
    //     0x1403dac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0x1403db0: ldr             x16, [x16, #0xd0]
    // 0x1403db4: ldur            lr, [fp, #-0x30]
    // 0x1403db8: stp             lr, x16, [SP, #8]
    // 0x1403dbc: ldur            x16, [fp, #-0x50]
    // 0x1403dc0: str             x16, [SP]
    // 0x1403dc4: mov             x1, x0
    // 0x1403dc8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x1, width, 0x2, null]
    //     0x1403dc8: add             x4, PP, #0x37, lsl #12  ; [pp+0x371b8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0x1403dcc: ldr             x4, [x4, #0x1b8]
    // 0x1403dd0: r0 = Container()
    //     0x1403dd0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1403dd4: r0 = Visibility()
    //     0x1403dd4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1403dd8: mov             x2, x0
    // 0x1403ddc: ldur            x0, [fp, #-0x38]
    // 0x1403de0: stur            x2, [fp, #-0x30]
    // 0x1403de4: StoreField: r2->field_b = r0
    //     0x1403de4: stur            w0, [x2, #0xb]
    // 0x1403de8: r0 = Instance_SizedBox
    //     0x1403de8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1403dec: StoreField: r2->field_f = r0
    //     0x1403dec: stur            w0, [x2, #0xf]
    // 0x1403df0: ldur            x1, [fp, #-0x18]
    // 0x1403df4: StoreField: r2->field_13 = r1
    //     0x1403df4: stur            w1, [x2, #0x13]
    // 0x1403df8: r3 = false
    //     0x1403df8: add             x3, NULL, #0x30  ; false
    // 0x1403dfc: ArrayStore: r2[0] = r3  ; List_4
    //     0x1403dfc: stur            w3, [x2, #0x17]
    // 0x1403e00: StoreField: r2->field_1b = r3
    //     0x1403e00: stur            w3, [x2, #0x1b]
    // 0x1403e04: StoreField: r2->field_1f = r3
    //     0x1403e04: stur            w3, [x2, #0x1f]
    // 0x1403e08: StoreField: r2->field_23 = r3
    //     0x1403e08: stur            w3, [x2, #0x23]
    // 0x1403e0c: StoreField: r2->field_27 = r3
    //     0x1403e0c: stur            w3, [x2, #0x27]
    // 0x1403e10: StoreField: r2->field_2b = r3
    //     0x1403e10: stur            w3, [x2, #0x2b]
    // 0x1403e14: ldur            x4, [fp, #-8]
    // 0x1403e18: LoadField: r1 = r4->field_f
    //     0x1403e18: ldur            w1, [x4, #0xf]
    // 0x1403e1c: DecompressPointer r1
    //     0x1403e1c: add             x1, x1, HEAP, lsl #32
    // 0x1403e20: r0 = controller()
    //     0x1403e20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1403e24: LoadField: r1 = r0->field_8b
    //     0x1403e24: ldur            w1, [x0, #0x8b]
    // 0x1403e28: DecompressPointer r1
    //     0x1403e28: add             x1, x1, HEAP, lsl #32
    // 0x1403e2c: r0 = value()
    //     0x1403e2c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1403e30: cmp             w0, NULL
    // 0x1403e34: b.ne            #0x1403e3c
    // 0x1403e38: r0 = true
    //     0x1403e38: add             x0, NULL, #0x20  ; true
    // 0x1403e3c: ldur            x2, [fp, #-8]
    // 0x1403e40: stur            x0, [fp, #-0x18]
    // 0x1403e44: LoadField: r1 = r2->field_13
    //     0x1403e44: ldur            w1, [x2, #0x13]
    // 0x1403e48: DecompressPointer r1
    //     0x1403e48: add             x1, x1, HEAP, lsl #32
    // 0x1403e4c: r0 = of()
    //     0x1403e4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1403e50: LoadField: r1 = r0->field_87
    //     0x1403e50: ldur            w1, [x0, #0x87]
    // 0x1403e54: DecompressPointer r1
    //     0x1403e54: add             x1, x1, HEAP, lsl #32
    // 0x1403e58: LoadField: r0 = r1->field_7
    //     0x1403e58: ldur            w0, [x1, #7]
    // 0x1403e5c: DecompressPointer r0
    //     0x1403e5c: add             x0, x0, HEAP, lsl #32
    // 0x1403e60: ldur            x2, [fp, #-8]
    // 0x1403e64: stur            x0, [fp, #-0x38]
    // 0x1403e68: LoadField: r1 = r2->field_f
    //     0x1403e68: ldur            w1, [x2, #0xf]
    // 0x1403e6c: DecompressPointer r1
    //     0x1403e6c: add             x1, x1, HEAP, lsl #32
    // 0x1403e70: r0 = controller()
    //     0x1403e70: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1403e74: LoadField: r1 = r0->field_97
    //     0x1403e74: ldur            w1, [x0, #0x97]
    // 0x1403e78: DecompressPointer r1
    //     0x1403e78: add             x1, x1, HEAP, lsl #32
    // 0x1403e7c: r0 = value()
    //     0x1403e7c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1403e80: cmp             w0, NULL
    // 0x1403e84: b.eq            #0x1403ea8
    // 0x1403e88: tbnz            w0, #4, #0x1403ea8
    // 0x1403e8c: ldur            x2, [fp, #-8]
    // 0x1403e90: LoadField: r1 = r2->field_13
    //     0x1403e90: ldur            w1, [x2, #0x13]
    // 0x1403e94: DecompressPointer r1
    //     0x1403e94: add             x1, x1, HEAP, lsl #32
    // 0x1403e98: r0 = of()
    //     0x1403e98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1403e9c: LoadField: r1 = r0->field_5b
    //     0x1403e9c: ldur            w1, [x0, #0x5b]
    // 0x1403ea0: DecompressPointer r1
    //     0x1403ea0: add             x1, x1, HEAP, lsl #32
    // 0x1403ea4: b               #0x1403edc
    // 0x1403ea8: ldur            x2, [fp, #-8]
    // 0x1403eac: LoadField: r1 = r2->field_13
    //     0x1403eac: ldur            w1, [x2, #0x13]
    // 0x1403eb0: DecompressPointer r1
    //     0x1403eb0: add             x1, x1, HEAP, lsl #32
    // 0x1403eb4: r0 = of()
    //     0x1403eb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1403eb8: LoadField: r1 = r0->field_5b
    //     0x1403eb8: ldur            w1, [x0, #0x5b]
    // 0x1403ebc: DecompressPointer r1
    //     0x1403ebc: add             x1, x1, HEAP, lsl #32
    // 0x1403ec0: r0 = LoadClassIdInstr(r1)
    //     0x1403ec0: ldur            x0, [x1, #-1]
    //     0x1403ec4: ubfx            x0, x0, #0xc, #0x14
    // 0x1403ec8: d0 = 0.400000
    //     0x1403ec8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1403ecc: r0 = GDT[cid_x0 + -0xffa]()
    //     0x1403ecc: sub             lr, x0, #0xffa
    //     0x1403ed0: ldr             lr, [x21, lr, lsl #3]
    //     0x1403ed4: blr             lr
    // 0x1403ed8: mov             x1, x0
    // 0x1403edc: ldur            x5, [fp, #-0x10]
    // 0x1403ee0: ldur            x4, [fp, #-0x28]
    // 0x1403ee4: ldur            x3, [fp, #-0x20]
    // 0x1403ee8: ldur            x2, [fp, #-0x30]
    // 0x1403eec: ldur            x0, [fp, #-0x18]
    // 0x1403ef0: r16 = 12.000000
    //     0x1403ef0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1403ef4: ldr             x16, [x16, #0x9e8]
    // 0x1403ef8: stp             x16, x1, [SP]
    // 0x1403efc: ldur            x1, [fp, #-0x38]
    // 0x1403f00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1403f00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1403f04: ldr             x4, [x4, #0x9b8]
    // 0x1403f08: r0 = copyWith()
    //     0x1403f08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1403f0c: stur            x0, [fp, #-0x38]
    // 0x1403f10: r0 = Text()
    //     0x1403f10: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1403f14: mov             x1, x0
    // 0x1403f18: r0 = "RESEND OTP"
    //     0x1403f18: add             x0, PP, #0x37, lsl #12  ; [pp+0x371c0] "RESEND OTP"
    //     0x1403f1c: ldr             x0, [x0, #0x1c0]
    // 0x1403f20: stur            x1, [fp, #-0x40]
    // 0x1403f24: StoreField: r1->field_b = r0
    //     0x1403f24: stur            w0, [x1, #0xb]
    // 0x1403f28: ldur            x0, [fp, #-0x38]
    // 0x1403f2c: StoreField: r1->field_13 = r0
    //     0x1403f2c: stur            w0, [x1, #0x13]
    // 0x1403f30: r0 = InkWell()
    //     0x1403f30: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1403f34: mov             x3, x0
    // 0x1403f38: ldur            x0, [fp, #-0x40]
    // 0x1403f3c: stur            x3, [fp, #-0x38]
    // 0x1403f40: StoreField: r3->field_b = r0
    //     0x1403f40: stur            w0, [x3, #0xb]
    // 0x1403f44: ldur            x2, [fp, #-8]
    // 0x1403f48: r1 = Function '<anonymous closure>':.
    //     0x1403f48: add             x1, PP, #0x37, lsl #12  ; [pp+0x371c8] AnonymousClosure: (0x140313c), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::_getLineThemeLoginForm (0x1404eac)
    //     0x1403f4c: ldr             x1, [x1, #0x1c8]
    // 0x1403f50: r0 = AllocateClosure()
    //     0x1403f50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1403f54: mov             x1, x0
    // 0x1403f58: ldur            x0, [fp, #-0x38]
    // 0x1403f5c: StoreField: r0->field_f = r1
    //     0x1403f5c: stur            w1, [x0, #0xf]
    // 0x1403f60: r1 = true
    //     0x1403f60: add             x1, NULL, #0x20  ; true
    // 0x1403f64: StoreField: r0->field_43 = r1
    //     0x1403f64: stur            w1, [x0, #0x43]
    // 0x1403f68: r2 = Instance_BoxShape
    //     0x1403f68: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1403f6c: ldr             x2, [x2, #0x80]
    // 0x1403f70: StoreField: r0->field_47 = r2
    //     0x1403f70: stur            w2, [x0, #0x47]
    // 0x1403f74: StoreField: r0->field_6f = r1
    //     0x1403f74: stur            w1, [x0, #0x6f]
    // 0x1403f78: r2 = false
    //     0x1403f78: add             x2, NULL, #0x30  ; false
    // 0x1403f7c: StoreField: r0->field_73 = r2
    //     0x1403f7c: stur            w2, [x0, #0x73]
    // 0x1403f80: StoreField: r0->field_83 = r1
    //     0x1403f80: stur            w1, [x0, #0x83]
    // 0x1403f84: StoreField: r0->field_7b = r2
    //     0x1403f84: stur            w2, [x0, #0x7b]
    // 0x1403f88: r0 = Visibility()
    //     0x1403f88: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1403f8c: mov             x1, x0
    // 0x1403f90: ldur            x0, [fp, #-0x38]
    // 0x1403f94: stur            x1, [fp, #-8]
    // 0x1403f98: StoreField: r1->field_b = r0
    //     0x1403f98: stur            w0, [x1, #0xb]
    // 0x1403f9c: r0 = Instance_SizedBox
    //     0x1403f9c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1403fa0: StoreField: r1->field_f = r0
    //     0x1403fa0: stur            w0, [x1, #0xf]
    // 0x1403fa4: ldur            x0, [fp, #-0x18]
    // 0x1403fa8: StoreField: r1->field_13 = r0
    //     0x1403fa8: stur            w0, [x1, #0x13]
    // 0x1403fac: r0 = false
    //     0x1403fac: add             x0, NULL, #0x30  ; false
    // 0x1403fb0: ArrayStore: r1[0] = r0  ; List_4
    //     0x1403fb0: stur            w0, [x1, #0x17]
    // 0x1403fb4: StoreField: r1->field_1b = r0
    //     0x1403fb4: stur            w0, [x1, #0x1b]
    // 0x1403fb8: StoreField: r1->field_1f = r0
    //     0x1403fb8: stur            w0, [x1, #0x1f]
    // 0x1403fbc: StoreField: r1->field_23 = r0
    //     0x1403fbc: stur            w0, [x1, #0x23]
    // 0x1403fc0: StoreField: r1->field_27 = r0
    //     0x1403fc0: stur            w0, [x1, #0x27]
    // 0x1403fc4: StoreField: r1->field_2b = r0
    //     0x1403fc4: stur            w0, [x1, #0x2b]
    // 0x1403fc8: r0 = Padding()
    //     0x1403fc8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1403fcc: mov             x3, x0
    // 0x1403fd0: r0 = Instance_EdgeInsets
    //     0x1403fd0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x1403fd4: ldr             x0, [x0, #0xa78]
    // 0x1403fd8: stur            x3, [fp, #-0x18]
    // 0x1403fdc: StoreField: r3->field_f = r0
    //     0x1403fdc: stur            w0, [x3, #0xf]
    // 0x1403fe0: ldur            x0, [fp, #-8]
    // 0x1403fe4: StoreField: r3->field_b = r0
    //     0x1403fe4: stur            w0, [x3, #0xb]
    // 0x1403fe8: r1 = Null
    //     0x1403fe8: mov             x1, NULL
    // 0x1403fec: r2 = 14
    //     0x1403fec: movz            x2, #0xe
    // 0x1403ff0: r0 = AllocateArray()
    //     0x1403ff0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1403ff4: mov             x2, x0
    // 0x1403ff8: ldur            x0, [fp, #-0x10]
    // 0x1403ffc: stur            x2, [fp, #-8]
    // 0x1404000: StoreField: r2->field_f = r0
    //     0x1404000: stur            w0, [x2, #0xf]
    // 0x1404004: ldur            x0, [fp, #-0x28]
    // 0x1404008: StoreField: r2->field_13 = r0
    //     0x1404008: stur            w0, [x2, #0x13]
    // 0x140400c: ldur            x0, [fp, #-0x20]
    // 0x1404010: ArrayStore: r2[0] = r0  ; List_4
    //     0x1404010: stur            w0, [x2, #0x17]
    // 0x1404014: ldur            x0, [fp, #-0x30]
    // 0x1404018: StoreField: r2->field_1b = r0
    //     0x1404018: stur            w0, [x2, #0x1b]
    // 0x140401c: r16 = Instance_SizedBox
    //     0x140401c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0x1404020: ldr             x16, [x16, #0x578]
    // 0x1404024: StoreField: r2->field_1f = r16
    //     0x1404024: stur            w16, [x2, #0x1f]
    // 0x1404028: ldur            x0, [fp, #-0x18]
    // 0x140402c: StoreField: r2->field_23 = r0
    //     0x140402c: stur            w0, [x2, #0x23]
    // 0x1404030: r16 = Instance_SizedBox
    //     0x1404030: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x1404034: ldr             x16, [x16, #0x9f0]
    // 0x1404038: StoreField: r2->field_27 = r16
    //     0x1404038: stur            w16, [x2, #0x27]
    // 0x140403c: r1 = <Widget>
    //     0x140403c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1404040: r0 = AllocateGrowableArray()
    //     0x1404040: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1404044: mov             x1, x0
    // 0x1404048: ldur            x0, [fp, #-8]
    // 0x140404c: stur            x1, [fp, #-0x10]
    // 0x1404050: StoreField: r1->field_f = r0
    //     0x1404050: stur            w0, [x1, #0xf]
    // 0x1404054: r0 = 14
    //     0x1404054: movz            x0, #0xe
    // 0x1404058: StoreField: r1->field_b = r0
    //     0x1404058: stur            w0, [x1, #0xb]
    // 0x140405c: r0 = Column()
    //     0x140405c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1404060: r1 = Instance_Axis
    //     0x1404060: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1404064: StoreField: r0->field_f = r1
    //     0x1404064: stur            w1, [x0, #0xf]
    // 0x1404068: r1 = Instance_MainAxisAlignment
    //     0x1404068: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x140406c: ldr             x1, [x1, #0xa08]
    // 0x1404070: StoreField: r0->field_13 = r1
    //     0x1404070: stur            w1, [x0, #0x13]
    // 0x1404074: r1 = Instance_MainAxisSize
    //     0x1404074: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1404078: ldr             x1, [x1, #0xa10]
    // 0x140407c: ArrayStore: r0[0] = r1  ; List_4
    //     0x140407c: stur            w1, [x0, #0x17]
    // 0x1404080: r1 = Instance_CrossAxisAlignment
    //     0x1404080: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1404084: ldr             x1, [x1, #0x890]
    // 0x1404088: StoreField: r0->field_1b = r1
    //     0x1404088: stur            w1, [x0, #0x1b]
    // 0x140408c: r1 = Instance_VerticalDirection
    //     0x140408c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1404090: ldr             x1, [x1, #0xa20]
    // 0x1404094: StoreField: r0->field_23 = r1
    //     0x1404094: stur            w1, [x0, #0x23]
    // 0x1404098: r1 = Instance_Clip
    //     0x1404098: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x140409c: ldr             x1, [x1, #0x38]
    // 0x14040a0: StoreField: r0->field_2b = r1
    //     0x14040a0: stur            w1, [x0, #0x2b]
    // 0x14040a4: StoreField: r0->field_2f = rZR
    //     0x14040a4: stur            xzr, [x0, #0x2f]
    // 0x14040a8: ldur            x1, [fp, #-0x10]
    // 0x14040ac: StoreField: r0->field_b = r1
    //     0x14040ac: stur            w1, [x0, #0xb]
    // 0x14040b0: LeaveFrame
    //     0x14040b0: mov             SP, fp
    //     0x14040b4: ldp             fp, lr, [SP], #0x10
    // 0x14040b8: ret
    //     0x14040b8: ret             
    // 0x14040bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14040bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14040c0: b               #0x1403248
    // 0x14040c4: SaveReg d0
    //     0x14040c4: str             q0, [SP, #-0x10]!
    // 0x14040c8: stp             x0, x1, [SP, #-0x10]!
    // 0x14040cc: r0 = AllocateDouble()
    //     0x14040cc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x14040d0: mov             x2, x0
    // 0x14040d4: ldp             x0, x1, [SP], #0x10
    // 0x14040d8: RestoreReg d0
    //     0x14040d8: ldr             q0, [SP], #0x10
    // 0x14040dc: b               #0x1403d9c
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0x14040e0, size: 0x150
    // 0x14040e0: EnterFrame
    //     0x14040e0: stp             fp, lr, [SP, #-0x10]!
    //     0x14040e4: mov             fp, SP
    // 0x14040e8: AllocStack(0x20)
    //     0x14040e8: sub             SP, SP, #0x20
    // 0x14040ec: SetupParameters()
    //     0x14040ec: ldr             x0, [fp, #0x18]
    //     0x14040f0: ldur            w2, [x0, #0x17]
    //     0x14040f4: add             x2, x2, HEAP, lsl #32
    //     0x14040f8: stur            x2, [fp, #-8]
    // 0x14040fc: CheckStackOverflow
    //     0x14040fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1404100: cmp             SP, x16
    //     0x1404104: b.ls            #0x1404224
    // 0x1404108: ldr             x0, [fp, #0x10]
    // 0x140410c: cmp             w0, NULL
    // 0x1404110: b.eq            #0x140422c
    // 0x1404114: LoadField: r1 = r0->field_7
    //     0x1404114: ldur            w1, [x0, #7]
    // 0x1404118: cmp             w1, #8
    // 0x140411c: b.ne            #0x14041b8
    // 0x1404120: LoadField: r1 = r2->field_f
    //     0x1404120: ldur            w1, [x2, #0xf]
    // 0x1404124: DecompressPointer r1
    //     0x1404124: add             x1, x1, HEAP, lsl #32
    // 0x1404128: r0 = controller()
    //     0x1404128: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140412c: LoadField: r1 = r0->field_8f
    //     0x140412c: ldur            w1, [x0, #0x8f]
    // 0x1404130: DecompressPointer r1
    //     0x1404130: add             x1, x1, HEAP, lsl #32
    // 0x1404134: r2 = true
    //     0x1404134: add             x2, NULL, #0x20  ; true
    // 0x1404138: r0 = value=()
    //     0x1404138: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140413c: ldur            x2, [fp, #-8]
    // 0x1404140: LoadField: r1 = r2->field_f
    //     0x1404140: ldur            w1, [x2, #0xf]
    // 0x1404144: DecompressPointer r1
    //     0x1404144: add             x1, x1, HEAP, lsl #32
    // 0x1404148: ldr             x0, [fp, #0x10]
    // 0x140414c: StoreField: r1->field_1b = r0
    //     0x140414c: stur            w0, [x1, #0x1b]
    //     0x1404150: ldurb           w16, [x1, #-1]
    //     0x1404154: ldurb           w17, [x0, #-1]
    //     0x1404158: and             x16, x17, x16, lsr #2
    //     0x140415c: tst             x16, HEAP, lsr #32
    //     0x1404160: b.eq            #0x1404168
    //     0x1404164: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1404168: r0 = controller()
    //     0x1404168: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140416c: mov             x1, x0
    // 0x1404170: ldr             x2, [fp, #0x10]
    // 0x1404174: r0 = onCodeChanged()
    //     0x1404174: bl              #0x1404230  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::onCodeChanged
    // 0x1404178: ldur            x0, [fp, #-8]
    // 0x140417c: LoadField: r1 = r0->field_13
    //     0x140417c: ldur            w1, [x0, #0x13]
    // 0x1404180: DecompressPointer r1
    //     0x1404180: add             x1, x1, HEAP, lsl #32
    // 0x1404184: r0 = of()
    //     0x1404184: bl              #0x81ef68  ; [package:flutter/src/widgets/focus_scope.dart] FocusScope::of
    // 0x1404188: stur            x0, [fp, #-0x10]
    // 0x140418c: r0 = FocusNode()
    //     0x140418c: bl              #0x8182fc  ; AllocateFocusNodeStub -> FocusNode (size=0x68)
    // 0x1404190: mov             x1, x0
    // 0x1404194: stur            x0, [fp, #-0x18]
    // 0x1404198: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1404198: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x140419c: r0 = FocusNode()
    //     0x140419c: bl              #0x695c10  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::FocusNode
    // 0x14041a0: ldur            x16, [fp, #-0x18]
    // 0x14041a4: str             x16, [SP]
    // 0x14041a8: ldur            x1, [fp, #-0x10]
    // 0x14041ac: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x14041ac: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x14041b0: r0 = requestFocus()
    //     0x14041b0: bl              #0x6595f4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::requestFocus
    // 0x14041b4: b               #0x1404214
    // 0x14041b8: mov             x0, x2
    // 0x14041bc: LoadField: r1 = r0->field_f
    //     0x14041bc: ldur            w1, [x0, #0xf]
    // 0x14041c0: DecompressPointer r1
    //     0x14041c0: add             x1, x1, HEAP, lsl #32
    // 0x14041c4: r0 = controller()
    //     0x14041c4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14041c8: LoadField: r1 = r0->field_8f
    //     0x14041c8: ldur            w1, [x0, #0x8f]
    // 0x14041cc: DecompressPointer r1
    //     0x14041cc: add             x1, x1, HEAP, lsl #32
    // 0x14041d0: r2 = false
    //     0x14041d0: add             x2, NULL, #0x30  ; false
    // 0x14041d4: r0 = value=()
    //     0x14041d4: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14041d8: ldur            x0, [fp, #-8]
    // 0x14041dc: LoadField: r1 = r0->field_f
    //     0x14041dc: ldur            w1, [x0, #0xf]
    // 0x14041e0: DecompressPointer r1
    //     0x14041e0: add             x1, x1, HEAP, lsl #32
    // 0x14041e4: ldr             x0, [fp, #0x10]
    // 0x14041e8: StoreField: r1->field_1b = r0
    //     0x14041e8: stur            w0, [x1, #0x1b]
    //     0x14041ec: ldurb           w16, [x1, #-1]
    //     0x14041f0: ldurb           w17, [x0, #-1]
    //     0x14041f4: and             x16, x17, x16, lsr #2
    //     0x14041f8: tst             x16, HEAP, lsr #32
    //     0x14041fc: b.eq            #0x1404204
    //     0x1404200: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1404204: r0 = controller()
    //     0x1404204: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1404208: mov             x1, x0
    // 0x140420c: ldr             x2, [fp, #0x10]
    // 0x1404210: r0 = onCodeChanged()
    //     0x1404210: bl              #0x1404230  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::onCodeChanged
    // 0x1404214: r0 = Null
    //     0x1404214: mov             x0, NULL
    // 0x1404218: LeaveFrame
    //     0x1404218: mov             SP, fp
    //     0x140421c: ldp             fp, lr, [SP], #0x10
    // 0x1404220: ret
    //     0x1404220: ret             
    // 0x1404224: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1404224: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1404228: b               #0x1404108
    // 0x140422c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x140422c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String) {
    // ** addr: 0x1404280, size: 0xc4
    // 0x1404280: EnterFrame
    //     0x1404280: stp             fp, lr, [SP, #-0x10]!
    //     0x1404284: mov             fp, SP
    // 0x1404288: AllocStack(0x8)
    //     0x1404288: sub             SP, SP, #8
    // 0x140428c: SetupParameters()
    //     0x140428c: ldr             x0, [fp, #0x18]
    //     0x1404290: ldur            w2, [x0, #0x17]
    //     0x1404294: add             x2, x2, HEAP, lsl #32
    //     0x1404298: stur            x2, [fp, #-8]
    // 0x140429c: CheckStackOverflow
    //     0x140429c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14042a0: cmp             SP, x16
    //     0x14042a4: b.ls            #0x140433c
    // 0x14042a8: LoadField: r1 = r2->field_f
    //     0x14042a8: ldur            w1, [x2, #0xf]
    // 0x14042ac: DecompressPointer r1
    //     0x14042ac: add             x1, x1, HEAP, lsl #32
    // 0x14042b0: r0 = controller()
    //     0x14042b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14042b4: LoadField: r1 = r0->field_8f
    //     0x14042b4: ldur            w1, [x0, #0x8f]
    // 0x14042b8: DecompressPointer r1
    //     0x14042b8: add             x1, x1, HEAP, lsl #32
    // 0x14042bc: r2 = true
    //     0x14042bc: add             x2, NULL, #0x20  ; true
    // 0x14042c0: r0 = value=()
    //     0x14042c0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14042c4: ldur            x0, [fp, #-8]
    // 0x14042c8: LoadField: r1 = r0->field_f
    //     0x14042c8: ldur            w1, [x0, #0xf]
    // 0x14042cc: DecompressPointer r1
    //     0x14042cc: add             x1, x1, HEAP, lsl #32
    // 0x14042d0: r0 = controller()
    //     0x14042d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14042d4: mov             x1, x0
    // 0x14042d8: ldr             x2, [fp, #0x10]
    // 0x14042dc: r0 = onCodeSubmitted()
    //     0x14042dc: bl              #0x1404344  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::onCodeSubmitted
    // 0x14042e0: ldur            x0, [fp, #-8]
    // 0x14042e4: LoadField: r1 = r0->field_f
    //     0x14042e4: ldur            w1, [x0, #0xf]
    // 0x14042e8: DecompressPointer r1
    //     0x14042e8: add             x1, x1, HEAP, lsl #32
    // 0x14042ec: r0 = controller()
    //     0x14042ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14042f0: LoadField: r1 = r0->field_7f
    //     0x14042f0: ldur            w1, [x0, #0x7f]
    // 0x14042f4: DecompressPointer r1
    //     0x14042f4: add             x1, x1, HEAP, lsl #32
    // 0x14042f8: ldr             x2, [fp, #0x10]
    // 0x14042fc: r0 = value=()
    //     0x14042fc: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1404300: ldur            x1, [fp, #-8]
    // 0x1404304: LoadField: r2 = r1->field_f
    //     0x1404304: ldur            w2, [x1, #0xf]
    // 0x1404308: DecompressPointer r2
    //     0x1404308: add             x2, x2, HEAP, lsl #32
    // 0x140430c: ldr             x0, [fp, #0x10]
    // 0x1404310: StoreField: r2->field_1b = r0
    //     0x1404310: stur            w0, [x2, #0x1b]
    //     0x1404314: ldurb           w16, [x2, #-1]
    //     0x1404318: ldurb           w17, [x0, #-1]
    //     0x140431c: and             x16, x17, x16, lsr #2
    //     0x1404320: tst             x16, HEAP, lsr #32
    //     0x1404324: b.eq            #0x140432c
    //     0x1404328: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x140432c: r0 = Null
    //     0x140432c: mov             x0, NULL
    // 0x1404330: LeaveFrame
    //     0x1404330: mov             SP, fp
    //     0x1404334: ldp             fp, lr, [SP], #0x10
    // 0x1404338: ret
    //     0x1404338: ret             
    // 0x140433c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140433c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1404340: b               #0x14042a8
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0x1404a84, size: 0x9c
    // 0x1404a84: EnterFrame
    //     0x1404a84: stp             fp, lr, [SP, #-0x10]!
    //     0x1404a88: mov             fp, SP
    // 0x1404a8c: AllocStack(0x8)
    //     0x1404a8c: sub             SP, SP, #8
    // 0x1404a90: SetupParameters()
    //     0x1404a90: ldr             x0, [fp, #0x18]
    //     0x1404a94: ldur            w2, [x0, #0x17]
    //     0x1404a98: add             x2, x2, HEAP, lsl #32
    //     0x1404a9c: stur            x2, [fp, #-8]
    // 0x1404aa0: CheckStackOverflow
    //     0x1404aa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1404aa4: cmp             SP, x16
    //     0x1404aa8: b.ls            #0x1404b18
    // 0x1404aac: LoadField: r1 = r2->field_f
    //     0x1404aac: ldur            w1, [x2, #0xf]
    // 0x1404ab0: DecompressPointer r1
    //     0x1404ab0: add             x1, x1, HEAP, lsl #32
    // 0x1404ab4: ldr             x0, [fp, #0x10]
    // 0x1404ab8: cmp             w0, NULL
    // 0x1404abc: b.ne            #0x1404ac4
    // 0x1404ac0: r0 = "0"
    //     0x1404ac0: ldr             x0, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0x1404ac4: ArrayStore: r1[0] = r0  ; List_4
    //     0x1404ac4: stur            w0, [x1, #0x17]
    //     0x1404ac8: ldurb           w16, [x1, #-1]
    //     0x1404acc: ldurb           w17, [x0, #-1]
    //     0x1404ad0: and             x16, x17, x16, lsr #2
    //     0x1404ad4: tst             x16, HEAP, lsr #32
    //     0x1404ad8: b.eq            #0x1404ae0
    //     0x1404adc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1404ae0: r0 = controller()
    //     0x1404ae0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1404ae4: mov             x1, x0
    // 0x1404ae8: r2 = true
    //     0x1404ae8: add             x2, NULL, #0x20  ; true
    // 0x1404aec: r0 = onUserInteraction=()
    //     0x1404aec: bl              #0x1404c4c  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::onUserInteraction=
    // 0x1404af0: ldur            x0, [fp, #-8]
    // 0x1404af4: LoadField: r1 = r0->field_f
    //     0x1404af4: ldur            w1, [x0, #0xf]
    // 0x1404af8: DecompressPointer r1
    //     0x1404af8: add             x1, x1, HEAP, lsl #32
    // 0x1404afc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x1404afc: ldur            w2, [x1, #0x17]
    // 0x1404b00: DecompressPointer r2
    //     0x1404b00: add             x2, x2, HEAP, lsl #32
    // 0x1404b04: r0 = _validateInput()
    //     0x1404b04: bl              #0x1404b20  ; [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::_validateInput
    // 0x1404b08: r0 = Null
    //     0x1404b08: mov             x0, NULL
    // 0x1404b0c: LeaveFrame
    //     0x1404b0c: mov             SP, fp
    //     0x1404b10: ldp             fp, lr, [SP], #0x10
    // 0x1404b14: ret
    //     0x1404b14: ret             
    // 0x1404b18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1404b18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1404b1c: b               #0x1404aac
  }
  _ _validateInput(/* No info */) {
    // ** addr: 0x1404b20, size: 0xe0
    // 0x1404b20: EnterFrame
    //     0x1404b20: stp             fp, lr, [SP, #-0x10]!
    //     0x1404b24: mov             fp, SP
    // 0x1404b28: AllocStack(0x18)
    //     0x1404b28: sub             SP, SP, #0x18
    // 0x1404b2c: SetupParameters(LoginView this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r1 */)
    //     0x1404b2c: mov             x0, x1
    //     0x1404b30: stur            x1, [fp, #-0x10]
    //     0x1404b34: mov             x1, x2
    // 0x1404b38: CheckStackOverflow
    //     0x1404b38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1404b3c: cmp             SP, x16
    //     0x1404b40: b.ls            #0x1404bf8
    // 0x1404b44: LoadField: r3 = r1->field_7
    //     0x1404b44: ldur            w3, [x1, #7]
    // 0x1404b48: stur            x3, [fp, #-8]
    // 0x1404b4c: cbz             w3, #0x1404b6c
    // 0x1404b50: r16 = 2
    //     0x1404b50: movz            x16, #0x2
    // 0x1404b54: str             x16, [SP]
    // 0x1404b58: r2 = 0
    //     0x1404b58: movz            x2, #0
    // 0x1404b5c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x1404b5c: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x1404b60: r0 = substring()
    //     0x1404b60: bl              #0x61f5c0  ; [dart:core] _StringBase::substring
    // 0x1404b64: mov             x1, x0
    // 0x1404b68: b               #0x1404b70
    // 0x1404b6c: r1 = Null
    //     0x1404b6c: mov             x1, NULL
    // 0x1404b70: ldur            x0, [fp, #-8]
    // 0x1404b74: r2 = LoadInt32Instr(r0)
    //     0x1404b74: sbfx            x2, x0, #1, #0x1f
    // 0x1404b78: cmp             x2, #0xa
    // 0x1404b7c: b.ge            #0x1404ba0
    // 0x1404b80: cmp             x2, #0xa
    // 0x1404b84: b.eq            #0x1404ba0
    // 0x1404b88: ldur            x1, [fp, #-0x10]
    // 0x1404b8c: r0 = controller()
    //     0x1404b8c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1404b90: mov             x1, x0
    // 0x1404b94: r2 = false
    //     0x1404b94: add             x2, NULL, #0x30  ; false
    // 0x1404b98: r0 = isValid=()
    //     0x1404b98: bl              #0x1404c00  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::isValid=
    // 0x1404b9c: b               #0x1404be8
    // 0x1404ba0: cmp             w1, NULL
    // 0x1404ba4: b.ne            #0x1404bac
    // 0x1404ba8: r1 = "0"
    //     0x1404ba8: ldr             x1, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0x1404bac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1404bac: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1404bb0: r0 = parse()
    //     0x1404bb0: bl              #0x6255f0  ; [dart:core] int::parse
    // 0x1404bb4: cmp             x0, #6
    // 0x1404bb8: b.ge            #0x1404bd4
    // 0x1404bbc: ldur            x1, [fp, #-0x10]
    // 0x1404bc0: r0 = controller()
    //     0x1404bc0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1404bc4: mov             x1, x0
    // 0x1404bc8: r2 = false
    //     0x1404bc8: add             x2, NULL, #0x30  ; false
    // 0x1404bcc: r0 = isValid=()
    //     0x1404bcc: bl              #0x1404c00  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::isValid=
    // 0x1404bd0: b               #0x1404be8
    // 0x1404bd4: ldur            x1, [fp, #-0x10]
    // 0x1404bd8: r0 = controller()
    //     0x1404bd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1404bdc: mov             x1, x0
    // 0x1404be0: r2 = true
    //     0x1404be0: add             x2, NULL, #0x20  ; true
    // 0x1404be4: r0 = isValid=()
    //     0x1404be4: bl              #0x1404c00  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::isValid=
    // 0x1404be8: r0 = Null
    //     0x1404be8: mov             x0, NULL
    // 0x1404bec: LeaveFrame
    //     0x1404bec: mov             SP, fp
    //     0x1404bf0: ldp             fp, lr, [SP], #0x10
    // 0x1404bf4: ret
    //     0x1404bf4: ret             
    // 0x1404bf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1404bf8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1404bfc: b               #0x1404b44
  }
  [closure] Future<void> <anonymous closure>(dynamic, String) async {
    // ** addr: 0x1404c8c, size: 0x78
    // 0x1404c8c: EnterFrame
    //     0x1404c8c: stp             fp, lr, [SP, #-0x10]!
    //     0x1404c90: mov             fp, SP
    // 0x1404c94: AllocStack(0x18)
    //     0x1404c94: sub             SP, SP, #0x18
    // 0x1404c98: SetupParameters(LoginView this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x1404c98: stur            NULL, [fp, #-8]
    //     0x1404c9c: movz            x0, #0
    //     0x1404ca0: add             x1, fp, w0, sxtw #2
    //     0x1404ca4: ldr             x1, [x1, #0x18]
    //     0x1404ca8: add             x2, fp, w0, sxtw #2
    //     0x1404cac: ldr             x2, [x2, #0x10]
    //     0x1404cb0: stur            x2, [fp, #-0x18]
    //     0x1404cb4: ldur            w3, [x1, #0x17]
    //     0x1404cb8: add             x3, x3, HEAP, lsl #32
    //     0x1404cbc: stur            x3, [fp, #-0x10]
    // 0x1404cc0: CheckStackOverflow
    //     0x1404cc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1404cc4: cmp             SP, x16
    //     0x1404cc8: b.ls            #0x1404cfc
    // 0x1404ccc: InitAsync() -> Future<void?>
    //     0x1404ccc: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x1404cd0: bl              #0x6326e0  ; InitAsyncStub
    // 0x1404cd4: ldur            x0, [fp, #-0x18]
    // 0x1404cd8: LoadField: r1 = r0->field_7
    //     0x1404cd8: ldur            w1, [x0, #7]
    // 0x1404cdc: cmp             w1, #0x14
    // 0x1404ce0: b.ne            #0x1404cf4
    // 0x1404ce4: ldur            x0, [fp, #-0x10]
    // 0x1404ce8: LoadField: r1 = r0->field_f
    //     0x1404ce8: ldur            w1, [x0, #0xf]
    // 0x1404cec: DecompressPointer r1
    //     0x1404cec: add             x1, x1, HEAP, lsl #32
    // 0x1404cf0: r0 = onPhoneSubmitted()
    //     0x1404cf0: bl              #0x131dbcc  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::onPhoneSubmitted
    // 0x1404cf4: r0 = Null
    //     0x1404cf4: mov             x0, NULL
    // 0x1404cf8: r0 = ReturnAsyncNotFuture()
    //     0x1404cf8: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x1404cfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1404cfc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1404d00: b               #0x1404ccc
  }
  [closure] String? _validatePhoneNumber(dynamic, String?) {
    // ** addr: 0x1404d04, size: 0x3c
    // 0x1404d04: EnterFrame
    //     0x1404d04: stp             fp, lr, [SP, #-0x10]!
    //     0x1404d08: mov             fp, SP
    // 0x1404d0c: ldr             x0, [fp, #0x18]
    // 0x1404d10: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1404d10: ldur            w1, [x0, #0x17]
    // 0x1404d14: DecompressPointer r1
    //     0x1404d14: add             x1, x1, HEAP, lsl #32
    // 0x1404d18: CheckStackOverflow
    //     0x1404d18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1404d1c: cmp             SP, x16
    //     0x1404d20: b.ls            #0x1404d38
    // 0x1404d24: ldr             x2, [fp, #0x10]
    // 0x1404d28: r0 = _validatePhoneNumber()
    //     0x1404d28: bl              #0x1404d40  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::_validatePhoneNumber
    // 0x1404d2c: LeaveFrame
    //     0x1404d2c: mov             SP, fp
    //     0x1404d30: ldp             fp, lr, [SP], #0x10
    // 0x1404d34: ret
    //     0x1404d34: ret             
    // 0x1404d38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1404d38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1404d3c: b               #0x1404d24
  }
  _ _getLineThemeLoginForm(/* No info */) {
    // ** addr: 0x1404eac, size: 0xa8
    // 0x1404eac: EnterFrame
    //     0x1404eac: stp             fp, lr, [SP, #-0x10]!
    //     0x1404eb0: mov             fp, SP
    // 0x1404eb4: AllocStack(0x18)
    //     0x1404eb4: sub             SP, SP, #0x18
    // 0x1404eb8: SetupParameters(LoginView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1404eb8: stur            x1, [fp, #-8]
    //     0x1404ebc: stur            x2, [fp, #-0x10]
    // 0x1404ec0: CheckStackOverflow
    //     0x1404ec0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1404ec4: cmp             SP, x16
    //     0x1404ec8: b.ls            #0x1404f4c
    // 0x1404ecc: r1 = 2
    //     0x1404ecc: movz            x1, #0x2
    // 0x1404ed0: r0 = AllocateContext()
    //     0x1404ed0: bl              #0x16f6108  ; AllocateContextStub
    // 0x1404ed4: mov             x2, x0
    // 0x1404ed8: ldur            x0, [fp, #-8]
    // 0x1404edc: stur            x2, [fp, #-0x18]
    // 0x1404ee0: StoreField: r2->field_f = r0
    //     0x1404ee0: stur            w0, [x2, #0xf]
    // 0x1404ee4: ldur            x1, [fp, #-0x10]
    // 0x1404ee8: StoreField: r2->field_13 = r1
    //     0x1404ee8: stur            w1, [x2, #0x13]
    // 0x1404eec: mov             x1, x0
    // 0x1404ef0: r0 = controller()
    //     0x1404ef0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1404ef4: LoadField: r1 = r0->field_8b
    //     0x1404ef4: ldur            w1, [x0, #0x8b]
    // 0x1404ef8: DecompressPointer r1
    //     0x1404ef8: add             x1, x1, HEAP, lsl #32
    // 0x1404efc: r0 = value()
    //     0x1404efc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1404f00: cmp             w0, NULL
    // 0x1404f04: b.eq            #0x1404f0c
    // 0x1404f08: tbnz            w0, #4, #0x1404f1c
    // 0x1404f0c: ldur            x1, [fp, #-8]
    // 0x1404f10: r0 = controller()
    //     0x1404f10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1404f14: mov             x1, x0
    // 0x1404f18: r0 = startTimer()
    //     0x1404f18: bl              #0x1402244  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::startTimer
    // 0x1404f1c: r0 = Obx()
    //     0x1404f1c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1404f20: ldur            x2, [fp, #-0x18]
    // 0x1404f24: r1 = Function '<anonymous closure>':.
    //     0x1404f24: add             x1, PP, #0x37, lsl #12  ; [pp+0x370f0] AnonymousClosure: (0x1403220), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::_getLineThemeLoginForm (0x1404eac)
    //     0x1404f28: ldr             x1, [x1, #0xf0]
    // 0x1404f2c: stur            x0, [fp, #-8]
    // 0x1404f30: r0 = AllocateClosure()
    //     0x1404f30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1404f34: mov             x1, x0
    // 0x1404f38: ldur            x0, [fp, #-8]
    // 0x1404f3c: StoreField: r0->field_b = r1
    //     0x1404f3c: stur            w1, [x0, #0xb]
    // 0x1404f40: LeaveFrame
    //     0x1404f40: mov             SP, fp
    //     0x1404f44: ldp             fp, lr, [SP], #0x10
    // 0x1404f48: ret
    //     0x1404f48: ret             
    // 0x1404f4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1404f4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1404f50: b               #0x1404ecc
  }
  _ body(/* No info */) {
    // ** addr: 0x15063a8, size: 0xc4
    // 0x15063a8: EnterFrame
    //     0x15063a8: stp             fp, lr, [SP, #-0x10]!
    //     0x15063ac: mov             fp, SP
    // 0x15063b0: AllocStack(0x18)
    //     0x15063b0: sub             SP, SP, #0x18
    // 0x15063b4: SetupParameters(LoginView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15063b4: mov             x0, x1
    //     0x15063b8: stur            x1, [fp, #-8]
    //     0x15063bc: stur            x2, [fp, #-0x10]
    // 0x15063c0: r1 = 2
    //     0x15063c0: movz            x1, #0x2
    // 0x15063c4: r0 = AllocateContext()
    //     0x15063c4: bl              #0x16f6108  ; AllocateContextStub
    // 0x15063c8: ldur            x2, [fp, #-8]
    // 0x15063cc: stur            x0, [fp, #-0x18]
    // 0x15063d0: StoreField: r0->field_f = r2
    //     0x15063d0: stur            w2, [x0, #0xf]
    // 0x15063d4: ldur            x1, [fp, #-0x10]
    // 0x15063d8: StoreField: r0->field_13 = r1
    //     0x15063d8: stur            w1, [x0, #0x13]
    // 0x15063dc: r0 = Obx()
    //     0x15063dc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15063e0: ldur            x2, [fp, #-0x18]
    // 0x15063e4: r1 = Function '<anonymous closure>':.
    //     0x15063e4: add             x1, PP, #0x37, lsl #12  ; [pp+0x37078] AnonymousClosure: (0x1506628), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::body (0x15063a8)
    //     0x15063e8: ldr             x1, [x1, #0x78]
    // 0x15063ec: stur            x0, [fp, #-0x10]
    // 0x15063f0: r0 = AllocateClosure()
    //     0x15063f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15063f4: mov             x1, x0
    // 0x15063f8: ldur            x0, [fp, #-0x10]
    // 0x15063fc: StoreField: r0->field_b = r1
    //     0x15063fc: stur            w1, [x0, #0xb]
    // 0x1506400: r0 = WillPopScope()
    //     0x1506400: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x1506404: mov             x3, x0
    // 0x1506408: ldur            x0, [fp, #-0x10]
    // 0x150640c: stur            x3, [fp, #-0x18]
    // 0x1506410: StoreField: r3->field_b = r0
    //     0x1506410: stur            w0, [x3, #0xb]
    // 0x1506414: ldur            x2, [fp, #-8]
    // 0x1506418: r1 = Function 'onBackPress':.
    //     0x1506418: add             x1, PP, #0x37, lsl #12  ; [pp+0x37080] AnonymousClosure: (0x150646c), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::onBackPress (0x15064a4)
    //     0x150641c: ldr             x1, [x1, #0x80]
    // 0x1506420: r0 = AllocateClosure()
    //     0x1506420: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1506424: mov             x1, x0
    // 0x1506428: ldur            x0, [fp, #-0x18]
    // 0x150642c: StoreField: r0->field_f = r1
    //     0x150642c: stur            w1, [x0, #0xf]
    // 0x1506430: r0 = SafeArea()
    //     0x1506430: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x1506434: r1 = true
    //     0x1506434: add             x1, NULL, #0x20  ; true
    // 0x1506438: StoreField: r0->field_b = r1
    //     0x1506438: stur            w1, [x0, #0xb]
    // 0x150643c: StoreField: r0->field_f = r1
    //     0x150643c: stur            w1, [x0, #0xf]
    // 0x1506440: StoreField: r0->field_13 = r1
    //     0x1506440: stur            w1, [x0, #0x13]
    // 0x1506444: ArrayStore: r0[0] = r1  ; List_4
    //     0x1506444: stur            w1, [x0, #0x17]
    // 0x1506448: r1 = Instance_EdgeInsets
    //     0x1506448: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x150644c: StoreField: r0->field_1b = r1
    //     0x150644c: stur            w1, [x0, #0x1b]
    // 0x1506450: r1 = false
    //     0x1506450: add             x1, NULL, #0x30  ; false
    // 0x1506454: StoreField: r0->field_1f = r1
    //     0x1506454: stur            w1, [x0, #0x1f]
    // 0x1506458: ldur            x1, [fp, #-0x18]
    // 0x150645c: StoreField: r0->field_23 = r1
    //     0x150645c: stur            w1, [x0, #0x23]
    // 0x1506460: LeaveFrame
    //     0x1506460: mov             SP, fp
    //     0x1506464: ldp             fp, lr, [SP], #0x10
    // 0x1506468: ret
    //     0x1506468: ret             
  }
  [closure] Future<bool> onBackPress(dynamic) {
    // ** addr: 0x150646c, size: 0x38
    // 0x150646c: EnterFrame
    //     0x150646c: stp             fp, lr, [SP, #-0x10]!
    //     0x1506470: mov             fp, SP
    // 0x1506474: ldr             x0, [fp, #0x10]
    // 0x1506478: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1506478: ldur            w1, [x0, #0x17]
    // 0x150647c: DecompressPointer r1
    //     0x150647c: add             x1, x1, HEAP, lsl #32
    // 0x1506480: CheckStackOverflow
    //     0x1506480: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1506484: cmp             SP, x16
    //     0x1506488: b.ls            #0x150649c
    // 0x150648c: r0 = onBackPress()
    //     0x150648c: bl              #0x15064a4  ; [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::onBackPress
    // 0x1506490: LeaveFrame
    //     0x1506490: mov             SP, fp
    //     0x1506494: ldp             fp, lr, [SP], #0x10
    // 0x1506498: ret
    //     0x1506498: ret             
    // 0x150649c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x150649c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15064a0: b               #0x150648c
  }
  _ onBackPress(/* No info */) {
    // ** addr: 0x15064a4, size: 0x184
    // 0x15064a4: EnterFrame
    //     0x15064a4: stp             fp, lr, [SP, #-0x10]!
    //     0x15064a8: mov             fp, SP
    // 0x15064ac: AllocStack(0x20)
    //     0x15064ac: sub             SP, SP, #0x20
    // 0x15064b0: SetupParameters(LoginView this /* r1 => r0, fp-0x8 */)
    //     0x15064b0: mov             x0, x1
    //     0x15064b4: stur            x1, [fp, #-8]
    // 0x15064b8: CheckStackOverflow
    //     0x15064b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15064bc: cmp             SP, x16
    //     0x15064c0: b.ls            #0x1506620
    // 0x15064c4: mov             x1, x0
    // 0x15064c8: r2 = false
    //     0x15064c8: add             x2, NULL, #0x30  ; false
    // 0x15064cc: r0 = showLoading()
    //     0x15064cc: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15064d0: ldur            x1, [fp, #-8]
    // 0x15064d4: r0 = controller()
    //     0x15064d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15064d8: LoadField: r1 = r0->field_8b
    //     0x15064d8: ldur            w1, [x0, #0x8b]
    // 0x15064dc: DecompressPointer r1
    //     0x15064dc: add             x1, x1, HEAP, lsl #32
    // 0x15064e0: r0 = value()
    //     0x15064e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15064e4: cmp             w0, NULL
    // 0x15064e8: b.eq            #0x1506574
    // 0x15064ec: tbnz            w0, #4, #0x1506574
    // 0x15064f0: ldur            x1, [fp, #-8]
    // 0x15064f4: r0 = controller()
    //     0x15064f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15064f8: LoadField: r1 = r0->field_8b
    //     0x15064f8: ldur            w1, [x0, #0x8b]
    // 0x15064fc: DecompressPointer r1
    //     0x15064fc: add             x1, x1, HEAP, lsl #32
    // 0x1506500: r2 = false
    //     0x1506500: add             x2, NULL, #0x30  ; false
    // 0x1506504: r0 = value=()
    //     0x1506504: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1506508: ldur            x1, [fp, #-8]
    // 0x150650c: r0 = controller()
    //     0x150650c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1506510: LoadField: r1 = r0->field_97
    //     0x1506510: ldur            w1, [x0, #0x97]
    // 0x1506514: DecompressPointer r1
    //     0x1506514: add             x1, x1, HEAP, lsl #32
    // 0x1506518: r2 = false
    //     0x1506518: add             x2, NULL, #0x30  ; false
    // 0x150651c: r0 = value=()
    //     0x150651c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1506520: r1 = <bool>
    //     0x1506520: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x1506524: r0 = _Future()
    //     0x1506524: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x1506528: stur            x0, [fp, #-0x10]
    // 0x150652c: StoreField: r0->field_b = rZR
    //     0x150652c: stur            xzr, [x0, #0xb]
    // 0x1506530: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x1506530: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1506534: ldr             x0, [x0, #0x778]
    //     0x1506538: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x150653c: cmp             w0, w16
    //     0x1506540: b.ne            #0x150654c
    //     0x1506544: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x1506548: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x150654c: mov             x1, x0
    // 0x1506550: ldur            x0, [fp, #-0x10]
    // 0x1506554: StoreField: r0->field_13 = r1
    //     0x1506554: stur            w1, [x0, #0x13]
    // 0x1506558: mov             x1, x0
    // 0x150655c: r2 = false
    //     0x150655c: add             x2, NULL, #0x30  ; false
    // 0x1506560: r0 = _asyncComplete()
    //     0x1506560: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x1506564: ldur            x0, [fp, #-0x10]
    // 0x1506568: LeaveFrame
    //     0x1506568: mov             SP, fp
    //     0x150656c: ldp             fp, lr, [SP], #0x10
    // 0x1506570: ret
    //     0x1506570: ret             
    // 0x1506574: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1506574: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1506578: ldr             x0, [x0, #0x1c80]
    //     0x150657c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1506580: cmp             w0, w16
    //     0x1506584: b.ne            #0x1506590
    //     0x1506588: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x150658c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1506590: ldur            x1, [fp, #-8]
    // 0x1506594: r0 = controller()
    //     0x1506594: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1506598: mov             x1, x0
    // 0x150659c: r0 = appliedCoupon()
    //     0x150659c: bl              #0x913a0c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_payment_method_controller.dart] CheckoutPaymentMethodController::appliedCoupon
    // 0x15065a0: tbnz            w0, #4, #0x15065b0
    // 0x15065a4: r0 = "success"
    //     0x15065a4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d918] "success"
    //     0x15065a8: ldr             x0, [x0, #0x918]
    // 0x15065ac: b               #0x15065b8
    // 0x15065b0: r0 = "Failed"
    //     0x15065b0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d738] "Failed"
    //     0x15065b4: ldr             x0, [x0, #0x738]
    // 0x15065b8: r16 = <String>
    //     0x15065b8: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x15065bc: stp             x0, x16, [SP]
    // 0x15065c0: r4 = const [0x1, 0x1, 0x1, 0, result, 0, null]
    //     0x15065c0: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2d920] List(7) [0x1, 0x1, 0x1, 0, "result", 0, Null]
    //     0x15065c4: ldr             x4, [x4, #0x920]
    // 0x15065c8: r0 = GetNavigation.back()
    //     0x15065c8: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x15065cc: r1 = <bool>
    //     0x15065cc: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x15065d0: r0 = _Future()
    //     0x15065d0: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x15065d4: stur            x0, [fp, #-8]
    // 0x15065d8: StoreField: r0->field_b = rZR
    //     0x15065d8: stur            xzr, [x0, #0xb]
    // 0x15065dc: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x15065dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15065e0: ldr             x0, [x0, #0x778]
    //     0x15065e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15065e8: cmp             w0, w16
    //     0x15065ec: b.ne            #0x15065f8
    //     0x15065f0: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x15065f4: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x15065f8: mov             x1, x0
    // 0x15065fc: ldur            x0, [fp, #-8]
    // 0x1506600: StoreField: r0->field_13 = r1
    //     0x1506600: stur            w1, [x0, #0x13]
    // 0x1506604: mov             x1, x0
    // 0x1506608: r2 = true
    //     0x1506608: add             x2, NULL, #0x20  ; true
    // 0x150660c: r0 = _asyncComplete()
    //     0x150660c: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x1506610: ldur            x0, [fp, #-8]
    // 0x1506614: LeaveFrame
    //     0x1506614: mov             SP, fp
    //     0x1506618: ldp             fp, lr, [SP], #0x10
    // 0x150661c: ret
    //     0x150661c: ret             
    // 0x1506620: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1506620: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1506624: b               #0x15064c4
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x1506628, size: 0x35c
    // 0x1506628: EnterFrame
    //     0x1506628: stp             fp, lr, [SP, #-0x10]!
    //     0x150662c: mov             fp, SP
    // 0x1506630: AllocStack(0x50)
    //     0x1506630: sub             SP, SP, #0x50
    // 0x1506634: SetupParameters()
    //     0x1506634: ldr             x0, [fp, #0x10]
    //     0x1506638: ldur            w2, [x0, #0x17]
    //     0x150663c: add             x2, x2, HEAP, lsl #32
    //     0x1506640: stur            x2, [fp, #-8]
    // 0x1506644: CheckStackOverflow
    //     0x1506644: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1506648: cmp             SP, x16
    //     0x150664c: b.ls            #0x150697c
    // 0x1506650: LoadField: r1 = r2->field_f
    //     0x1506650: ldur            w1, [x2, #0xf]
    // 0x1506654: DecompressPointer r1
    //     0x1506654: add             x1, x1, HEAP, lsl #32
    // 0x1506658: r0 = controller()
    //     0x1506658: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x150665c: LoadField: r1 = r0->field_6b
    //     0x150665c: ldur            w1, [x0, #0x6b]
    // 0x1506660: DecompressPointer r1
    //     0x1506660: add             x1, x1, HEAP, lsl #32
    // 0x1506664: r0 = value()
    //     0x1506664: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1506668: tbnz            w0, #4, #0x1506688
    // 0x150666c: r0 = Container()
    //     0x150666c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1506670: mov             x1, x0
    // 0x1506674: stur            x0, [fp, #-0x10]
    // 0x1506678: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1506678: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x150667c: r0 = Container()
    //     0x150667c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1506680: ldur            x0, [fp, #-0x10]
    // 0x1506684: b               #0x1506970
    // 0x1506688: ldur            x0, [fp, #-8]
    // 0x150668c: LoadField: r1 = r0->field_f
    //     0x150668c: ldur            w1, [x0, #0xf]
    // 0x1506690: DecompressPointer r1
    //     0x1506690: add             x1, x1, HEAP, lsl #32
    // 0x1506694: LoadField: r2 = r0->field_13
    //     0x1506694: ldur            w2, [x0, #0x13]
    // 0x1506698: DecompressPointer r2
    //     0x1506698: add             x2, x2, HEAP, lsl #32
    // 0x150669c: r0 = _getLineThemeLoginForm()
    //     0x150669c: bl              #0x1404eac  ; [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::_getLineThemeLoginForm
    // 0x15066a0: ldur            x2, [fp, #-8]
    // 0x15066a4: stur            x0, [fp, #-0x10]
    // 0x15066a8: LoadField: r1 = r2->field_f
    //     0x15066a8: ldur            w1, [x2, #0xf]
    // 0x15066ac: DecompressPointer r1
    //     0x15066ac: add             x1, x1, HEAP, lsl #32
    // 0x15066b0: r0 = controller()
    //     0x15066b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15066b4: LoadField: r1 = r0->field_8b
    //     0x15066b4: ldur            w1, [x0, #0x8b]
    // 0x15066b8: DecompressPointer r1
    //     0x15066b8: add             x1, x1, HEAP, lsl #32
    // 0x15066bc: r0 = value()
    //     0x15066bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15066c0: cmp             w0, NULL
    // 0x15066c4: b.ne            #0x15066cc
    // 0x15066c8: r0 = true
    //     0x15066c8: add             x0, NULL, #0x20  ; true
    // 0x15066cc: ldur            x2, [fp, #-8]
    // 0x15066d0: stur            x0, [fp, #-0x18]
    // 0x15066d4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15066d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15066d8: ldr             x0, [x0, #0x1c80]
    //     0x15066dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15066e0: cmp             w0, w16
    //     0x15066e4: b.ne            #0x15066f0
    //     0x15066e8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15066ec: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15066f0: r0 = GetNavigation.width()
    //     0x15066f0: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x15066f4: stur            d0, [fp, #-0x38]
    // 0x15066f8: r0 = GetNavigation.size()
    //     0x15066f8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x15066fc: LoadField: d0 = r0->field_f
    //     0x15066fc: ldur            d0, [x0, #0xf]
    // 0x1506700: d1 = 0.060000
    //     0x1506700: add             x17, PP, #0x36, lsl #12  ; [pp+0x36f28] IMM: double(0.06) from 0x3faeb851eb851eb8
    //     0x1506704: ldr             d1, [x17, #0xf28]
    // 0x1506708: fmul            d2, d0, d1
    // 0x150670c: ldur            x2, [fp, #-8]
    // 0x1506710: stur            d2, [fp, #-0x40]
    // 0x1506714: LoadField: r1 = r2->field_13
    //     0x1506714: ldur            w1, [x2, #0x13]
    // 0x1506718: DecompressPointer r1
    //     0x1506718: add             x1, x1, HEAP, lsl #32
    // 0x150671c: r0 = of()
    //     0x150671c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1506720: LoadField: r2 = r0->field_5b
    //     0x1506720: ldur            w2, [x0, #0x5b]
    // 0x1506724: DecompressPointer r2
    //     0x1506724: add             x2, x2, HEAP, lsl #32
    // 0x1506728: ldur            x0, [fp, #-8]
    // 0x150672c: stur            x2, [fp, #-0x20]
    // 0x1506730: LoadField: r1 = r0->field_f
    //     0x1506730: ldur            w1, [x0, #0xf]
    // 0x1506734: DecompressPointer r1
    //     0x1506734: add             x1, x1, HEAP, lsl #32
    // 0x1506738: r0 = controller()
    //     0x1506738: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x150673c: LoadField: r1 = r0->field_8f
    //     0x150673c: ldur            w1, [x0, #0x8f]
    // 0x1506740: DecompressPointer r1
    //     0x1506740: add             x1, x1, HEAP, lsl #32
    // 0x1506744: r0 = value()
    //     0x1506744: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1506748: cmp             w0, NULL
    // 0x150674c: b.eq            #0x150676c
    // 0x1506750: tbnz            w0, #4, #0x150676c
    // 0x1506754: ldur            x2, [fp, #-8]
    // 0x1506758: r1 = Function '<anonymous closure>':.
    //     0x1506758: add             x1, PP, #0x37, lsl #12  ; [pp+0x37088] AnonymousClosure: (0x1506984), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::body (0x15063a8)
    //     0x150675c: ldr             x1, [x1, #0x88]
    // 0x1506760: r0 = AllocateClosure()
    //     0x1506760: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1506764: mov             x4, x0
    // 0x1506768: b               #0x1506770
    // 0x150676c: r4 = Null
    //     0x150676c: mov             x4, NULL
    // 0x1506770: ldur            x1, [fp, #-8]
    // 0x1506774: ldur            x3, [fp, #-0x10]
    // 0x1506778: ldur            x2, [fp, #-0x18]
    // 0x150677c: ldur            d1, [fp, #-0x38]
    // 0x1506780: ldur            d0, [fp, #-0x40]
    // 0x1506784: ldur            x0, [fp, #-0x20]
    // 0x1506788: stur            x4, [fp, #-0x28]
    // 0x150678c: LoadField: r5 = r1->field_13
    //     0x150678c: ldur            w5, [x1, #0x13]
    // 0x1506790: DecompressPointer r5
    //     0x1506790: add             x5, x5, HEAP, lsl #32
    // 0x1506794: mov             x1, x5
    // 0x1506798: r0 = of()
    //     0x1506798: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x150679c: LoadField: r1 = r0->field_87
    //     0x150679c: ldur            w1, [x0, #0x87]
    // 0x15067a0: DecompressPointer r1
    //     0x15067a0: add             x1, x1, HEAP, lsl #32
    // 0x15067a4: LoadField: r0 = r1->field_7
    //     0x15067a4: ldur            w0, [x1, #7]
    // 0x15067a8: DecompressPointer r0
    //     0x15067a8: add             x0, x0, HEAP, lsl #32
    // 0x15067ac: r16 = 14.000000
    //     0x15067ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x15067b0: ldr             x16, [x16, #0x1d8]
    // 0x15067b4: r30 = Instance_Color
    //     0x15067b4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15067b8: stp             lr, x16, [SP]
    // 0x15067bc: mov             x1, x0
    // 0x15067c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15067c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15067c4: ldr             x4, [x4, #0xaa0]
    // 0x15067c8: r0 = copyWith()
    //     0x15067c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15067cc: stur            x0, [fp, #-8]
    // 0x15067d0: r0 = Text()
    //     0x15067d0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15067d4: mov             x1, x0
    // 0x15067d8: r0 = "VERIFY"
    //     0x15067d8: add             x0, PP, #0x37, lsl #12  ; [pp+0x37090] "VERIFY"
    //     0x15067dc: ldr             x0, [x0, #0x90]
    // 0x15067e0: stur            x1, [fp, #-0x30]
    // 0x15067e4: StoreField: r1->field_b = r0
    //     0x15067e4: stur            w0, [x1, #0xb]
    // 0x15067e8: ldur            x0, [fp, #-8]
    // 0x15067ec: StoreField: r1->field_13 = r0
    //     0x15067ec: stur            w0, [x1, #0x13]
    // 0x15067f0: r0 = MaterialButton()
    //     0x15067f0: bl              #0x131db78  ; AllocateMaterialButtonStub -> MaterialButton (size=0x88)
    // 0x15067f4: mov             x1, x0
    // 0x15067f8: ldur            x0, [fp, #-0x28]
    // 0x15067fc: stur            x1, [fp, #-8]
    // 0x1506800: StoreField: r1->field_b = r0
    //     0x1506800: stur            w0, [x1, #0xb]
    // 0x1506804: r0 = Instance_Color
    //     0x1506804: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1506808: StoreField: r1->field_1f = r0
    //     0x1506808: stur            w0, [x1, #0x1f]
    // 0x150680c: ldur            x0, [fp, #-0x20]
    // 0x1506810: StoreField: r1->field_23 = r0
    //     0x1506810: stur            w0, [x1, #0x23]
    // 0x1506814: r0 = Instance_Color
    //     0x1506814: add             x0, PP, #0x33, lsl #12  ; [pp+0x337b8] Obj!Color@d6ad11
    //     0x1506818: ldr             x0, [x0, #0x7b8]
    // 0x150681c: StoreField: r1->field_27 = r0
    //     0x150681c: stur            w0, [x1, #0x27]
    // 0x1506820: r0 = Instance_RoundedRectangleBorder
    //     0x1506820: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x1506824: ldr             x0, [x0, #0xd68]
    // 0x1506828: StoreField: r1->field_5b = r0
    //     0x1506828: stur            w0, [x1, #0x5b]
    // 0x150682c: r0 = Instance_Clip
    //     0x150682c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1506830: ldr             x0, [x0, #0x38]
    // 0x1506834: StoreField: r1->field_5f = r0
    //     0x1506834: stur            w0, [x1, #0x5f]
    // 0x1506838: r0 = false
    //     0x1506838: add             x0, NULL, #0x30  ; false
    // 0x150683c: StoreField: r1->field_67 = r0
    //     0x150683c: stur            w0, [x1, #0x67]
    // 0x1506840: ldur            d0, [fp, #-0x38]
    // 0x1506844: StoreField: r1->field_73 = d0
    //     0x1506844: stur            d0, [x1, #0x73]
    // 0x1506848: ldur            d0, [fp, #-0x40]
    // 0x150684c: StoreField: r1->field_7b = d0
    //     0x150684c: stur            d0, [x1, #0x7b]
    // 0x1506850: r2 = true
    //     0x1506850: add             x2, NULL, #0x20  ; true
    // 0x1506854: StoreField: r1->field_83 = r2
    //     0x1506854: stur            w2, [x1, #0x83]
    // 0x1506858: ldur            x2, [fp, #-0x30]
    // 0x150685c: StoreField: r1->field_4f = r2
    //     0x150685c: stur            w2, [x1, #0x4f]
    // 0x1506860: r2 = Instance_ValueKey
    //     0x1506860: add             x2, PP, #0x37, lsl #12  ; [pp+0x37098] Obj!ValueKey<String>@d5b3c1
    //     0x1506864: ldr             x2, [x2, #0x98]
    // 0x1506868: StoreField: r1->field_7 = r2
    //     0x1506868: stur            w2, [x1, #7]
    // 0x150686c: r0 = Padding()
    //     0x150686c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1506870: mov             x1, x0
    // 0x1506874: r0 = Instance_EdgeInsets
    //     0x1506874: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1506878: ldr             x0, [x0, #0x1f0]
    // 0x150687c: stur            x1, [fp, #-0x20]
    // 0x1506880: StoreField: r1->field_f = r0
    //     0x1506880: stur            w0, [x1, #0xf]
    // 0x1506884: ldur            x0, [fp, #-8]
    // 0x1506888: StoreField: r1->field_b = r0
    //     0x1506888: stur            w0, [x1, #0xb]
    // 0x150688c: r0 = Visibility()
    //     0x150688c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1506890: mov             x2, x0
    // 0x1506894: ldur            x0, [fp, #-0x20]
    // 0x1506898: stur            x2, [fp, #-8]
    // 0x150689c: StoreField: r2->field_b = r0
    //     0x150689c: stur            w0, [x2, #0xb]
    // 0x15068a0: r0 = Instance_SizedBox
    //     0x15068a0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15068a4: StoreField: r2->field_f = r0
    //     0x15068a4: stur            w0, [x2, #0xf]
    // 0x15068a8: ldur            x0, [fp, #-0x18]
    // 0x15068ac: StoreField: r2->field_13 = r0
    //     0x15068ac: stur            w0, [x2, #0x13]
    // 0x15068b0: r0 = false
    //     0x15068b0: add             x0, NULL, #0x30  ; false
    // 0x15068b4: ArrayStore: r2[0] = r0  ; List_4
    //     0x15068b4: stur            w0, [x2, #0x17]
    // 0x15068b8: StoreField: r2->field_1b = r0
    //     0x15068b8: stur            w0, [x2, #0x1b]
    // 0x15068bc: StoreField: r2->field_1f = r0
    //     0x15068bc: stur            w0, [x2, #0x1f]
    // 0x15068c0: StoreField: r2->field_23 = r0
    //     0x15068c0: stur            w0, [x2, #0x23]
    // 0x15068c4: StoreField: r2->field_27 = r0
    //     0x15068c4: stur            w0, [x2, #0x27]
    // 0x15068c8: StoreField: r2->field_2b = r0
    //     0x15068c8: stur            w0, [x2, #0x2b]
    // 0x15068cc: r1 = <StackParentData>
    //     0x15068cc: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x15068d0: ldr             x1, [x1, #0x8e0]
    // 0x15068d4: r0 = Positioned()
    //     0x15068d4: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x15068d8: mov             x3, x0
    // 0x15068dc: r0 = 0.000000
    //     0x15068dc: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x15068e0: stur            x3, [fp, #-0x18]
    // 0x15068e4: StoreField: r3->field_13 = r0
    //     0x15068e4: stur            w0, [x3, #0x13]
    // 0x15068e8: StoreField: r3->field_1b = r0
    //     0x15068e8: stur            w0, [x3, #0x1b]
    // 0x15068ec: r0 = 10.000000
    //     0x15068ec: ldr             x0, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x15068f0: StoreField: r3->field_1f = r0
    //     0x15068f0: stur            w0, [x3, #0x1f]
    // 0x15068f4: ldur            x0, [fp, #-8]
    // 0x15068f8: StoreField: r3->field_b = r0
    //     0x15068f8: stur            w0, [x3, #0xb]
    // 0x15068fc: r1 = Null
    //     0x15068fc: mov             x1, NULL
    // 0x1506900: r2 = 4
    //     0x1506900: movz            x2, #0x4
    // 0x1506904: r0 = AllocateArray()
    //     0x1506904: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1506908: mov             x2, x0
    // 0x150690c: ldur            x0, [fp, #-0x10]
    // 0x1506910: stur            x2, [fp, #-8]
    // 0x1506914: StoreField: r2->field_f = r0
    //     0x1506914: stur            w0, [x2, #0xf]
    // 0x1506918: ldur            x0, [fp, #-0x18]
    // 0x150691c: StoreField: r2->field_13 = r0
    //     0x150691c: stur            w0, [x2, #0x13]
    // 0x1506920: r1 = <Widget>
    //     0x1506920: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1506924: r0 = AllocateGrowableArray()
    //     0x1506924: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1506928: mov             x1, x0
    // 0x150692c: ldur            x0, [fp, #-8]
    // 0x1506930: stur            x1, [fp, #-0x10]
    // 0x1506934: StoreField: r1->field_f = r0
    //     0x1506934: stur            w0, [x1, #0xf]
    // 0x1506938: r0 = 4
    //     0x1506938: movz            x0, #0x4
    // 0x150693c: StoreField: r1->field_b = r0
    //     0x150693c: stur            w0, [x1, #0xb]
    // 0x1506940: r0 = Stack()
    //     0x1506940: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x1506944: r1 = Instance_AlignmentDirectional
    //     0x1506944: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0x1506948: ldr             x1, [x1, #0xd08]
    // 0x150694c: StoreField: r0->field_f = r1
    //     0x150694c: stur            w1, [x0, #0xf]
    // 0x1506950: r1 = Instance_StackFit
    //     0x1506950: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x1506954: ldr             x1, [x1, #0xfa8]
    // 0x1506958: ArrayStore: r0[0] = r1  ; List_4
    //     0x1506958: stur            w1, [x0, #0x17]
    // 0x150695c: r1 = Instance_Clip
    //     0x150695c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x1506960: ldr             x1, [x1, #0x7e0]
    // 0x1506964: StoreField: r0->field_1b = r1
    //     0x1506964: stur            w1, [x0, #0x1b]
    // 0x1506968: ldur            x1, [fp, #-0x10]
    // 0x150696c: StoreField: r0->field_b = r1
    //     0x150696c: stur            w1, [x0, #0xb]
    // 0x1506970: LeaveFrame
    //     0x1506970: mov             SP, fp
    //     0x1506974: ldp             fp, lr, [SP], #0x10
    // 0x1506978: ret
    //     0x1506978: ret             
    // 0x150697c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x150697c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1506980: b               #0x1506650
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1506984, size: 0x48
    // 0x1506984: EnterFrame
    //     0x1506984: stp             fp, lr, [SP, #-0x10]!
    //     0x1506988: mov             fp, SP
    // 0x150698c: ldr             x0, [fp, #0x10]
    // 0x1506990: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1506990: ldur            w1, [x0, #0x17]
    // 0x1506994: DecompressPointer r1
    //     0x1506994: add             x1, x1, HEAP, lsl #32
    // 0x1506998: CheckStackOverflow
    //     0x1506998: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x150699c: cmp             SP, x16
    //     0x15069a0: b.ls            #0x15069c4
    // 0x15069a4: LoadField: r0 = r1->field_f
    //     0x15069a4: ldur            w0, [x1, #0xf]
    // 0x15069a8: DecompressPointer r0
    //     0x15069a8: add             x0, x0, HEAP, lsl #32
    // 0x15069ac: mov             x1, x0
    // 0x15069b0: r0 = onOtpSubmit()
    //     0x15069b0: bl              #0x14056a0  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::onOtpSubmit
    // 0x15069b4: r0 = Null
    //     0x15069b4: mov             x0, NULL
    // 0x15069b8: LeaveFrame
    //     0x15069b8: mov             SP, fp
    //     0x15069bc: ldp             fp, lr, [SP], #0x10
    // 0x15069c0: ret
    //     0x15069c0: ret             
    // 0x15069c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15069c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15069c8: b               #0x15069a4
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0x15cf880, size: 0x42c
    // 0x15cf880: EnterFrame
    //     0x15cf880: stp             fp, lr, [SP, #-0x10]!
    //     0x15cf884: mov             fp, SP
    // 0x15cf888: AllocStack(0x48)
    //     0x15cf888: sub             SP, SP, #0x48
    // 0x15cf88c: SetupParameters()
    //     0x15cf88c: ldr             x0, [fp, #0x10]
    //     0x15cf890: ldur            w2, [x0, #0x17]
    //     0x15cf894: add             x2, x2, HEAP, lsl #32
    //     0x15cf898: stur            x2, [fp, #-8]
    // 0x15cf89c: CheckStackOverflow
    //     0x15cf89c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15cf8a0: cmp             SP, x16
    //     0x15cf8a4: b.ls            #0x15cfca4
    // 0x15cf8a8: LoadField: r1 = r2->field_f
    //     0x15cf8a8: ldur            w1, [x2, #0xf]
    // 0x15cf8ac: DecompressPointer r1
    //     0x15cf8ac: add             x1, x1, HEAP, lsl #32
    // 0x15cf8b0: r0 = controller()
    //     0x15cf8b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cf8b4: LoadField: r1 = r0->field_7b
    //     0x15cf8b4: ldur            w1, [x0, #0x7b]
    // 0x15cf8b8: DecompressPointer r1
    //     0x15cf8b8: add             x1, x1, HEAP, lsl #32
    // 0x15cf8bc: r0 = value()
    //     0x15cf8bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cf8c0: LoadField: r1 = r0->field_3f
    //     0x15cf8c0: ldur            w1, [x0, #0x3f]
    // 0x15cf8c4: DecompressPointer r1
    //     0x15cf8c4: add             x1, x1, HEAP, lsl #32
    // 0x15cf8c8: cmp             w1, NULL
    // 0x15cf8cc: b.ne            #0x15cf8d8
    // 0x15cf8d0: r0 = Null
    //     0x15cf8d0: mov             x0, NULL
    // 0x15cf8d4: b               #0x15cf8e0
    // 0x15cf8d8: LoadField: r0 = r1->field_f
    //     0x15cf8d8: ldur            w0, [x1, #0xf]
    // 0x15cf8dc: DecompressPointer r0
    //     0x15cf8dc: add             x0, x0, HEAP, lsl #32
    // 0x15cf8e0: r1 = LoadClassIdInstr(r0)
    //     0x15cf8e0: ldur            x1, [x0, #-1]
    //     0x15cf8e4: ubfx            x1, x1, #0xc, #0x14
    // 0x15cf8e8: r16 = "image_text"
    //     0x15cf8e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15cf8ec: ldr             x16, [x16, #0xa88]
    // 0x15cf8f0: stp             x16, x0, [SP]
    // 0x15cf8f4: mov             x0, x1
    // 0x15cf8f8: mov             lr, x0
    // 0x15cf8fc: ldr             lr, [x21, lr, lsl #3]
    // 0x15cf900: blr             lr
    // 0x15cf904: tbnz            w0, #4, #0x15cf910
    // 0x15cf908: r2 = true
    //     0x15cf908: add             x2, NULL, #0x20  ; true
    // 0x15cf90c: b               #0x15cf970
    // 0x15cf910: ldur            x0, [fp, #-8]
    // 0x15cf914: LoadField: r1 = r0->field_f
    //     0x15cf914: ldur            w1, [x0, #0xf]
    // 0x15cf918: DecompressPointer r1
    //     0x15cf918: add             x1, x1, HEAP, lsl #32
    // 0x15cf91c: r0 = controller()
    //     0x15cf91c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cf920: LoadField: r1 = r0->field_7b
    //     0x15cf920: ldur            w1, [x0, #0x7b]
    // 0x15cf924: DecompressPointer r1
    //     0x15cf924: add             x1, x1, HEAP, lsl #32
    // 0x15cf928: r0 = value()
    //     0x15cf928: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cf92c: LoadField: r1 = r0->field_3f
    //     0x15cf92c: ldur            w1, [x0, #0x3f]
    // 0x15cf930: DecompressPointer r1
    //     0x15cf930: add             x1, x1, HEAP, lsl #32
    // 0x15cf934: cmp             w1, NULL
    // 0x15cf938: b.ne            #0x15cf944
    // 0x15cf93c: r0 = Null
    //     0x15cf93c: mov             x0, NULL
    // 0x15cf940: b               #0x15cf94c
    // 0x15cf944: LoadField: r0 = r1->field_f
    //     0x15cf944: ldur            w0, [x1, #0xf]
    // 0x15cf948: DecompressPointer r0
    //     0x15cf948: add             x0, x0, HEAP, lsl #32
    // 0x15cf94c: r1 = LoadClassIdInstr(r0)
    //     0x15cf94c: ldur            x1, [x0, #-1]
    //     0x15cf950: ubfx            x1, x1, #0xc, #0x14
    // 0x15cf954: r16 = "image"
    //     0x15cf954: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x15cf958: stp             x16, x0, [SP]
    // 0x15cf95c: mov             x0, x1
    // 0x15cf960: mov             lr, x0
    // 0x15cf964: ldr             lr, [x21, lr, lsl #3]
    // 0x15cf968: blr             lr
    // 0x15cf96c: mov             x2, x0
    // 0x15cf970: ldur            x0, [fp, #-8]
    // 0x15cf974: stur            x2, [fp, #-0x10]
    // 0x15cf978: LoadField: r1 = r0->field_f
    //     0x15cf978: ldur            w1, [x0, #0xf]
    // 0x15cf97c: DecompressPointer r1
    //     0x15cf97c: add             x1, x1, HEAP, lsl #32
    // 0x15cf980: r0 = controller()
    //     0x15cf980: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cf984: LoadField: r1 = r0->field_7b
    //     0x15cf984: ldur            w1, [x0, #0x7b]
    // 0x15cf988: DecompressPointer r1
    //     0x15cf988: add             x1, x1, HEAP, lsl #32
    // 0x15cf98c: r0 = value()
    //     0x15cf98c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cf990: LoadField: r1 = r0->field_27
    //     0x15cf990: ldur            w1, [x0, #0x27]
    // 0x15cf994: DecompressPointer r1
    //     0x15cf994: add             x1, x1, HEAP, lsl #32
    // 0x15cf998: cmp             w1, NULL
    // 0x15cf99c: b.ne            #0x15cf9a8
    // 0x15cf9a0: r2 = ""
    //     0x15cf9a0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15cf9a4: b               #0x15cf9ac
    // 0x15cf9a8: mov             x2, x1
    // 0x15cf9ac: ldur            x0, [fp, #-8]
    // 0x15cf9b0: ldur            x1, [fp, #-0x10]
    // 0x15cf9b4: stur            x2, [fp, #-0x18]
    // 0x15cf9b8: r0 = ImageHeaders.forImages()
    //     0x15cf9b8: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x15cf9bc: stur            x0, [fp, #-0x20]
    // 0x15cf9c0: r0 = CachedNetworkImage()
    //     0x15cf9c0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15cf9c4: stur            x0, [fp, #-0x28]
    // 0x15cf9c8: ldur            x16, [fp, #-0x20]
    // 0x15cf9cc: r30 = Instance_BoxFit
    //     0x15cf9cc: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15cf9d0: ldr             lr, [lr, #0xb18]
    // 0x15cf9d4: stp             lr, x16, [SP, #0x10]
    // 0x15cf9d8: r16 = 50.000000
    //     0x15cf9d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15cf9dc: ldr             x16, [x16, #0xa90]
    // 0x15cf9e0: r30 = 50.000000
    //     0x15cf9e0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15cf9e4: ldr             lr, [lr, #0xa90]
    // 0x15cf9e8: stp             lr, x16, [SP]
    // 0x15cf9ec: mov             x1, x0
    // 0x15cf9f0: ldur            x2, [fp, #-0x18]
    // 0x15cf9f4: r4 = const [0, 0x6, 0x4, 0x2, fit, 0x3, height, 0x4, httpHeaders, 0x2, width, 0x5, null]
    //     0x15cf9f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea98] List(13) [0, 0x6, 0x4, 0x2, "fit", 0x3, "height", 0x4, "httpHeaders", 0x2, "width", 0x5, Null]
    //     0x15cf9f8: ldr             x4, [x4, #0xa98]
    // 0x15cf9fc: r0 = CachedNetworkImage()
    //     0x15cf9fc: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15cfa00: r0 = Visibility()
    //     0x15cfa00: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15cfa04: mov             x2, x0
    // 0x15cfa08: ldur            x0, [fp, #-0x28]
    // 0x15cfa0c: stur            x2, [fp, #-0x18]
    // 0x15cfa10: StoreField: r2->field_b = r0
    //     0x15cfa10: stur            w0, [x2, #0xb]
    // 0x15cfa14: r0 = Instance_SizedBox
    //     0x15cfa14: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15cfa18: StoreField: r2->field_f = r0
    //     0x15cfa18: stur            w0, [x2, #0xf]
    // 0x15cfa1c: ldur            x1, [fp, #-0x10]
    // 0x15cfa20: StoreField: r2->field_13 = r1
    //     0x15cfa20: stur            w1, [x2, #0x13]
    // 0x15cfa24: r3 = false
    //     0x15cfa24: add             x3, NULL, #0x30  ; false
    // 0x15cfa28: ArrayStore: r2[0] = r3  ; List_4
    //     0x15cfa28: stur            w3, [x2, #0x17]
    // 0x15cfa2c: StoreField: r2->field_1b = r3
    //     0x15cfa2c: stur            w3, [x2, #0x1b]
    // 0x15cfa30: StoreField: r2->field_1f = r3
    //     0x15cfa30: stur            w3, [x2, #0x1f]
    // 0x15cfa34: StoreField: r2->field_23 = r3
    //     0x15cfa34: stur            w3, [x2, #0x23]
    // 0x15cfa38: StoreField: r2->field_27 = r3
    //     0x15cfa38: stur            w3, [x2, #0x27]
    // 0x15cfa3c: StoreField: r2->field_2b = r3
    //     0x15cfa3c: stur            w3, [x2, #0x2b]
    // 0x15cfa40: ldur            x4, [fp, #-8]
    // 0x15cfa44: LoadField: r1 = r4->field_f
    //     0x15cfa44: ldur            w1, [x4, #0xf]
    // 0x15cfa48: DecompressPointer r1
    //     0x15cfa48: add             x1, x1, HEAP, lsl #32
    // 0x15cfa4c: r0 = controller()
    //     0x15cfa4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cfa50: LoadField: r1 = r0->field_7b
    //     0x15cfa50: ldur            w1, [x0, #0x7b]
    // 0x15cfa54: DecompressPointer r1
    //     0x15cfa54: add             x1, x1, HEAP, lsl #32
    // 0x15cfa58: r0 = value()
    //     0x15cfa58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cfa5c: LoadField: r1 = r0->field_3f
    //     0x15cfa5c: ldur            w1, [x0, #0x3f]
    // 0x15cfa60: DecompressPointer r1
    //     0x15cfa60: add             x1, x1, HEAP, lsl #32
    // 0x15cfa64: cmp             w1, NULL
    // 0x15cfa68: b.ne            #0x15cfa74
    // 0x15cfa6c: r0 = Null
    //     0x15cfa6c: mov             x0, NULL
    // 0x15cfa70: b               #0x15cfa7c
    // 0x15cfa74: LoadField: r0 = r1->field_f
    //     0x15cfa74: ldur            w0, [x1, #0xf]
    // 0x15cfa78: DecompressPointer r0
    //     0x15cfa78: add             x0, x0, HEAP, lsl #32
    // 0x15cfa7c: r1 = LoadClassIdInstr(r0)
    //     0x15cfa7c: ldur            x1, [x0, #-1]
    //     0x15cfa80: ubfx            x1, x1, #0xc, #0x14
    // 0x15cfa84: r16 = "image_text"
    //     0x15cfa84: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15cfa88: ldr             x16, [x16, #0xa88]
    // 0x15cfa8c: stp             x16, x0, [SP]
    // 0x15cfa90: mov             x0, x1
    // 0x15cfa94: mov             lr, x0
    // 0x15cfa98: ldr             lr, [x21, lr, lsl #3]
    // 0x15cfa9c: blr             lr
    // 0x15cfaa0: tbnz            w0, #4, #0x15cfaac
    // 0x15cfaa4: r2 = true
    //     0x15cfaa4: add             x2, NULL, #0x20  ; true
    // 0x15cfaa8: b               #0x15cfb0c
    // 0x15cfaac: ldur            x0, [fp, #-8]
    // 0x15cfab0: LoadField: r1 = r0->field_f
    //     0x15cfab0: ldur            w1, [x0, #0xf]
    // 0x15cfab4: DecompressPointer r1
    //     0x15cfab4: add             x1, x1, HEAP, lsl #32
    // 0x15cfab8: r0 = controller()
    //     0x15cfab8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cfabc: LoadField: r1 = r0->field_7b
    //     0x15cfabc: ldur            w1, [x0, #0x7b]
    // 0x15cfac0: DecompressPointer r1
    //     0x15cfac0: add             x1, x1, HEAP, lsl #32
    // 0x15cfac4: r0 = value()
    //     0x15cfac4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cfac8: LoadField: r1 = r0->field_3f
    //     0x15cfac8: ldur            w1, [x0, #0x3f]
    // 0x15cfacc: DecompressPointer r1
    //     0x15cfacc: add             x1, x1, HEAP, lsl #32
    // 0x15cfad0: cmp             w1, NULL
    // 0x15cfad4: b.ne            #0x15cfae0
    // 0x15cfad8: r0 = Null
    //     0x15cfad8: mov             x0, NULL
    // 0x15cfadc: b               #0x15cfae8
    // 0x15cfae0: LoadField: r0 = r1->field_f
    //     0x15cfae0: ldur            w0, [x1, #0xf]
    // 0x15cfae4: DecompressPointer r0
    //     0x15cfae4: add             x0, x0, HEAP, lsl #32
    // 0x15cfae8: r1 = LoadClassIdInstr(r0)
    //     0x15cfae8: ldur            x1, [x0, #-1]
    //     0x15cfaec: ubfx            x1, x1, #0xc, #0x14
    // 0x15cfaf0: r16 = "text"
    //     0x15cfaf0: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15cfaf4: stp             x16, x0, [SP]
    // 0x15cfaf8: mov             x0, x1
    // 0x15cfafc: mov             lr, x0
    // 0x15cfb00: ldr             lr, [x21, lr, lsl #3]
    // 0x15cfb04: blr             lr
    // 0x15cfb08: mov             x2, x0
    // 0x15cfb0c: ldur            x0, [fp, #-8]
    // 0x15cfb10: stur            x2, [fp, #-0x10]
    // 0x15cfb14: LoadField: r1 = r0->field_f
    //     0x15cfb14: ldur            w1, [x0, #0xf]
    // 0x15cfb18: DecompressPointer r1
    //     0x15cfb18: add             x1, x1, HEAP, lsl #32
    // 0x15cfb1c: r0 = controller()
    //     0x15cfb1c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cfb20: LoadField: r1 = r0->field_7b
    //     0x15cfb20: ldur            w1, [x0, #0x7b]
    // 0x15cfb24: DecompressPointer r1
    //     0x15cfb24: add             x1, x1, HEAP, lsl #32
    // 0x15cfb28: r0 = value()
    //     0x15cfb28: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cfb2c: LoadField: r1 = r0->field_2b
    //     0x15cfb2c: ldur            w1, [x0, #0x2b]
    // 0x15cfb30: DecompressPointer r1
    //     0x15cfb30: add             x1, x1, HEAP, lsl #32
    // 0x15cfb34: cmp             w1, NULL
    // 0x15cfb38: b.ne            #0x15cfb44
    // 0x15cfb3c: r4 = ""
    //     0x15cfb3c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15cfb40: b               #0x15cfb48
    // 0x15cfb44: mov             x4, x1
    // 0x15cfb48: ldur            x0, [fp, #-8]
    // 0x15cfb4c: ldur            x3, [fp, #-0x18]
    // 0x15cfb50: ldur            x2, [fp, #-0x10]
    // 0x15cfb54: stur            x4, [fp, #-0x20]
    // 0x15cfb58: LoadField: r1 = r0->field_13
    //     0x15cfb58: ldur            w1, [x0, #0x13]
    // 0x15cfb5c: DecompressPointer r1
    //     0x15cfb5c: add             x1, x1, HEAP, lsl #32
    // 0x15cfb60: r0 = of()
    //     0x15cfb60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15cfb64: LoadField: r1 = r0->field_87
    //     0x15cfb64: ldur            w1, [x0, #0x87]
    // 0x15cfb68: DecompressPointer r1
    //     0x15cfb68: add             x1, x1, HEAP, lsl #32
    // 0x15cfb6c: LoadField: r0 = r1->field_2b
    //     0x15cfb6c: ldur            w0, [x1, #0x2b]
    // 0x15cfb70: DecompressPointer r0
    //     0x15cfb70: add             x0, x0, HEAP, lsl #32
    // 0x15cfb74: r16 = 16.000000
    //     0x15cfb74: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15cfb78: ldr             x16, [x16, #0x188]
    // 0x15cfb7c: r30 = Instance_Color
    //     0x15cfb7c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15cfb80: stp             lr, x16, [SP]
    // 0x15cfb84: mov             x1, x0
    // 0x15cfb88: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15cfb88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15cfb8c: ldr             x4, [x4, #0xaa0]
    // 0x15cfb90: r0 = copyWith()
    //     0x15cfb90: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15cfb94: stur            x0, [fp, #-8]
    // 0x15cfb98: r0 = Text()
    //     0x15cfb98: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15cfb9c: mov             x1, x0
    // 0x15cfba0: ldur            x0, [fp, #-0x20]
    // 0x15cfba4: stur            x1, [fp, #-0x28]
    // 0x15cfba8: StoreField: r1->field_b = r0
    //     0x15cfba8: stur            w0, [x1, #0xb]
    // 0x15cfbac: ldur            x0, [fp, #-8]
    // 0x15cfbb0: StoreField: r1->field_13 = r0
    //     0x15cfbb0: stur            w0, [x1, #0x13]
    // 0x15cfbb4: r0 = Visibility()
    //     0x15cfbb4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15cfbb8: mov             x3, x0
    // 0x15cfbbc: ldur            x0, [fp, #-0x28]
    // 0x15cfbc0: stur            x3, [fp, #-8]
    // 0x15cfbc4: StoreField: r3->field_b = r0
    //     0x15cfbc4: stur            w0, [x3, #0xb]
    // 0x15cfbc8: r0 = Instance_SizedBox
    //     0x15cfbc8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15cfbcc: StoreField: r3->field_f = r0
    //     0x15cfbcc: stur            w0, [x3, #0xf]
    // 0x15cfbd0: ldur            x0, [fp, #-0x10]
    // 0x15cfbd4: StoreField: r3->field_13 = r0
    //     0x15cfbd4: stur            w0, [x3, #0x13]
    // 0x15cfbd8: r0 = false
    //     0x15cfbd8: add             x0, NULL, #0x30  ; false
    // 0x15cfbdc: ArrayStore: r3[0] = r0  ; List_4
    //     0x15cfbdc: stur            w0, [x3, #0x17]
    // 0x15cfbe0: StoreField: r3->field_1b = r0
    //     0x15cfbe0: stur            w0, [x3, #0x1b]
    // 0x15cfbe4: StoreField: r3->field_1f = r0
    //     0x15cfbe4: stur            w0, [x3, #0x1f]
    // 0x15cfbe8: StoreField: r3->field_23 = r0
    //     0x15cfbe8: stur            w0, [x3, #0x23]
    // 0x15cfbec: StoreField: r3->field_27 = r0
    //     0x15cfbec: stur            w0, [x3, #0x27]
    // 0x15cfbf0: StoreField: r3->field_2b = r0
    //     0x15cfbf0: stur            w0, [x3, #0x2b]
    // 0x15cfbf4: r1 = Null
    //     0x15cfbf4: mov             x1, NULL
    // 0x15cfbf8: r2 = 6
    //     0x15cfbf8: movz            x2, #0x6
    // 0x15cfbfc: r0 = AllocateArray()
    //     0x15cfbfc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15cfc00: mov             x2, x0
    // 0x15cfc04: ldur            x0, [fp, #-0x18]
    // 0x15cfc08: stur            x2, [fp, #-0x10]
    // 0x15cfc0c: StoreField: r2->field_f = r0
    //     0x15cfc0c: stur            w0, [x2, #0xf]
    // 0x15cfc10: r16 = Instance_SizedBox
    //     0x15cfc10: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x15cfc14: ldr             x16, [x16, #0xaa8]
    // 0x15cfc18: StoreField: r2->field_13 = r16
    //     0x15cfc18: stur            w16, [x2, #0x13]
    // 0x15cfc1c: ldur            x0, [fp, #-8]
    // 0x15cfc20: ArrayStore: r2[0] = r0  ; List_4
    //     0x15cfc20: stur            w0, [x2, #0x17]
    // 0x15cfc24: r1 = <Widget>
    //     0x15cfc24: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15cfc28: r0 = AllocateGrowableArray()
    //     0x15cfc28: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15cfc2c: mov             x1, x0
    // 0x15cfc30: ldur            x0, [fp, #-0x10]
    // 0x15cfc34: stur            x1, [fp, #-8]
    // 0x15cfc38: StoreField: r1->field_f = r0
    //     0x15cfc38: stur            w0, [x1, #0xf]
    // 0x15cfc3c: r0 = 6
    //     0x15cfc3c: movz            x0, #0x6
    // 0x15cfc40: StoreField: r1->field_b = r0
    //     0x15cfc40: stur            w0, [x1, #0xb]
    // 0x15cfc44: r0 = Row()
    //     0x15cfc44: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15cfc48: r1 = Instance_Axis
    //     0x15cfc48: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15cfc4c: StoreField: r0->field_f = r1
    //     0x15cfc4c: stur            w1, [x0, #0xf]
    // 0x15cfc50: r1 = Instance_MainAxisAlignment
    //     0x15cfc50: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x15cfc54: ldr             x1, [x1, #0xab0]
    // 0x15cfc58: StoreField: r0->field_13 = r1
    //     0x15cfc58: stur            w1, [x0, #0x13]
    // 0x15cfc5c: r1 = Instance_MainAxisSize
    //     0x15cfc5c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15cfc60: ldr             x1, [x1, #0xa10]
    // 0x15cfc64: ArrayStore: r0[0] = r1  ; List_4
    //     0x15cfc64: stur            w1, [x0, #0x17]
    // 0x15cfc68: r1 = Instance_CrossAxisAlignment
    //     0x15cfc68: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15cfc6c: ldr             x1, [x1, #0xa18]
    // 0x15cfc70: StoreField: r0->field_1b = r1
    //     0x15cfc70: stur            w1, [x0, #0x1b]
    // 0x15cfc74: r1 = Instance_VerticalDirection
    //     0x15cfc74: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15cfc78: ldr             x1, [x1, #0xa20]
    // 0x15cfc7c: StoreField: r0->field_23 = r1
    //     0x15cfc7c: stur            w1, [x0, #0x23]
    // 0x15cfc80: r1 = Instance_Clip
    //     0x15cfc80: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15cfc84: ldr             x1, [x1, #0x38]
    // 0x15cfc88: StoreField: r0->field_2b = r1
    //     0x15cfc88: stur            w1, [x0, #0x2b]
    // 0x15cfc8c: StoreField: r0->field_2f = rZR
    //     0x15cfc8c: stur            xzr, [x0, #0x2f]
    // 0x15cfc90: ldur            x1, [fp, #-8]
    // 0x15cfc94: StoreField: r0->field_b = r1
    //     0x15cfc94: stur            w1, [x0, #0xb]
    // 0x15cfc98: LeaveFrame
    //     0x15cfc98: mov             SP, fp
    //     0x15cfc9c: ldp             fp, lr, [SP], #0x10
    // 0x15cfca0: ret
    //     0x15cfca0: ret             
    // 0x15cfca4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15cfca4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15cfca8: b               #0x15cf8a8
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x15d1de4, size: 0x50
    // 0x15d1de4: EnterFrame
    //     0x15d1de4: stp             fp, lr, [SP, #-0x10]!
    //     0x15d1de8: mov             fp, SP
    // 0x15d1dec: ldr             x0, [fp, #0x18]
    // 0x15d1df0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x15d1df0: ldur            w1, [x0, #0x17]
    // 0x15d1df4: DecompressPointer r1
    //     0x15d1df4: add             x1, x1, HEAP, lsl #32
    // 0x15d1df8: CheckStackOverflow
    //     0x15d1df8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d1dfc: cmp             SP, x16
    //     0x15d1e00: b.ls            #0x15d1e2c
    // 0x15d1e04: LoadField: r0 = r1->field_f
    //     0x15d1e04: ldur            w0, [x1, #0xf]
    // 0x15d1e08: DecompressPointer r0
    //     0x15d1e08: add             x0, x0, HEAP, lsl #32
    // 0x15d1e0c: mov             x1, x0
    // 0x15d1e10: r0 = controller()
    //     0x15d1e10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d1e14: mov             x1, x0
    // 0x15d1e18: r0 = getBagCount()
    //     0x15d1e18: bl              #0x15a1c00  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::getBagCount
    // 0x15d1e1c: r0 = Null
    //     0x15d1e1c: mov             x0, NULL
    // 0x15d1e20: LeaveFrame
    //     0x15d1e20: mov             SP, fp
    //     0x15d1e24: ldp             fp, lr, [SP], #0x10
    // 0x15d1e28: ret
    //     0x15d1e28: ret             
    // 0x15d1e2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d1e2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d1e30: b               #0x15d1e04
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15d1e34, size: 0xdc
    // 0x15d1e34: EnterFrame
    //     0x15d1e34: stp             fp, lr, [SP, #-0x10]!
    //     0x15d1e38: mov             fp, SP
    // 0x15d1e3c: AllocStack(0x28)
    //     0x15d1e3c: sub             SP, SP, #0x28
    // 0x15d1e40: SetupParameters()
    //     0x15d1e40: ldr             x0, [fp, #0x10]
    //     0x15d1e44: ldur            w2, [x0, #0x17]
    //     0x15d1e48: add             x2, x2, HEAP, lsl #32
    //     0x15d1e4c: stur            x2, [fp, #-8]
    // 0x15d1e50: CheckStackOverflow
    //     0x15d1e50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d1e54: cmp             SP, x16
    //     0x15d1e58: b.ls            #0x15d1f08
    // 0x15d1e5c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15d1e5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15d1e60: ldr             x0, [x0, #0x1c80]
    //     0x15d1e64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15d1e68: cmp             w0, w16
    //     0x15d1e6c: b.ne            #0x15d1e78
    //     0x15d1e70: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15d1e74: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15d1e78: r1 = Null
    //     0x15d1e78: mov             x1, NULL
    // 0x15d1e7c: r2 = 4
    //     0x15d1e7c: movz            x2, #0x4
    // 0x15d1e80: r0 = AllocateArray()
    //     0x15d1e80: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15d1e84: r16 = "previousScreenSource"
    //     0x15d1e84: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x15d1e88: ldr             x16, [x16, #0x448]
    // 0x15d1e8c: StoreField: r0->field_f = r16
    //     0x15d1e8c: stur            w16, [x0, #0xf]
    // 0x15d1e90: r16 = "login_page"
    //     0x15d1e90: add             x16, PP, #0x37, lsl #12  ; [pp+0x37298] "login_page"
    //     0x15d1e94: ldr             x16, [x16, #0x298]
    // 0x15d1e98: StoreField: r0->field_13 = r16
    //     0x15d1e98: stur            w16, [x0, #0x13]
    // 0x15d1e9c: r16 = <String, String>
    //     0x15d1e9c: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x15d1ea0: ldr             x16, [x16, #0x788]
    // 0x15d1ea4: stp             x0, x16, [SP]
    // 0x15d1ea8: r0 = Map._fromLiteral()
    //     0x15d1ea8: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x15d1eac: r16 = "/bag"
    //     0x15d1eac: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x15d1eb0: ldr             x16, [x16, #0x468]
    // 0x15d1eb4: stp             x16, NULL, [SP, #8]
    // 0x15d1eb8: str             x0, [SP]
    // 0x15d1ebc: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x15d1ebc: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x15d1ec0: ldr             x4, [x4, #0x438]
    // 0x15d1ec4: r0 = GetNavigation.toNamed()
    //     0x15d1ec4: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15d1ec8: stur            x0, [fp, #-0x10]
    // 0x15d1ecc: cmp             w0, NULL
    // 0x15d1ed0: b.eq            #0x15d1ef8
    // 0x15d1ed4: ldur            x2, [fp, #-8]
    // 0x15d1ed8: r1 = Function '<anonymous closure>':.
    //     0x15d1ed8: add             x1, PP, #0x37, lsl #12  ; [pp+0x372a0] AnonymousClosure: (0x15d1de4), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::appBar (0x15ea93c)
    //     0x15d1edc: ldr             x1, [x1, #0x2a0]
    // 0x15d1ee0: r0 = AllocateClosure()
    //     0x15d1ee0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d1ee4: ldur            x16, [fp, #-0x10]
    // 0x15d1ee8: stp             x16, NULL, [SP, #8]
    // 0x15d1eec: str             x0, [SP]
    // 0x15d1ef0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15d1ef0: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15d1ef4: r0 = then()
    //     0x15d1ef4: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15d1ef8: r0 = Null
    //     0x15d1ef8: mov             x0, NULL
    // 0x15d1efc: LeaveFrame
    //     0x15d1efc: mov             SP, fp
    //     0x15d1f00: ldp             fp, lr, [SP], #0x10
    // 0x15d1f04: ret
    //     0x15d1f04: ret             
    // 0x15d1f08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d1f08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d1f0c: b               #0x15d1e5c
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15d1f10, size: 0x2f8
    // 0x15d1f10: EnterFrame
    //     0x15d1f10: stp             fp, lr, [SP, #-0x10]!
    //     0x15d1f14: mov             fp, SP
    // 0x15d1f18: AllocStack(0x58)
    //     0x15d1f18: sub             SP, SP, #0x58
    // 0x15d1f1c: SetupParameters()
    //     0x15d1f1c: ldr             x0, [fp, #0x10]
    //     0x15d1f20: ldur            w2, [x0, #0x17]
    //     0x15d1f24: add             x2, x2, HEAP, lsl #32
    //     0x15d1f28: stur            x2, [fp, #-8]
    // 0x15d1f2c: CheckStackOverflow
    //     0x15d1f2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d1f30: cmp             SP, x16
    //     0x15d1f34: b.ls            #0x15d2200
    // 0x15d1f38: LoadField: r1 = r2->field_f
    //     0x15d1f38: ldur            w1, [x2, #0xf]
    // 0x15d1f3c: DecompressPointer r1
    //     0x15d1f3c: add             x1, x1, HEAP, lsl #32
    // 0x15d1f40: r0 = controller()
    //     0x15d1f40: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d1f44: LoadField: r1 = r0->field_7b
    //     0x15d1f44: ldur            w1, [x0, #0x7b]
    // 0x15d1f48: DecompressPointer r1
    //     0x15d1f48: add             x1, x1, HEAP, lsl #32
    // 0x15d1f4c: r0 = value()
    //     0x15d1f4c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d1f50: LoadField: r1 = r0->field_1f
    //     0x15d1f50: ldur            w1, [x0, #0x1f]
    // 0x15d1f54: DecompressPointer r1
    //     0x15d1f54: add             x1, x1, HEAP, lsl #32
    // 0x15d1f58: cmp             w1, NULL
    // 0x15d1f5c: b.ne            #0x15d1f68
    // 0x15d1f60: r0 = Null
    //     0x15d1f60: mov             x0, NULL
    // 0x15d1f64: b               #0x15d1f70
    // 0x15d1f68: LoadField: r0 = r1->field_7
    //     0x15d1f68: ldur            w0, [x1, #7]
    // 0x15d1f6c: DecompressPointer r0
    //     0x15d1f6c: add             x0, x0, HEAP, lsl #32
    // 0x15d1f70: cmp             w0, NULL
    // 0x15d1f74: b.ne            #0x15d1f80
    // 0x15d1f78: r0 = false
    //     0x15d1f78: add             x0, NULL, #0x30  ; false
    // 0x15d1f7c: b               #0x15d2168
    // 0x15d1f80: tbnz            w0, #4, #0x15d2164
    // 0x15d1f84: ldur            x2, [fp, #-8]
    // 0x15d1f88: LoadField: r1 = r2->field_f
    //     0x15d1f88: ldur            w1, [x2, #0xf]
    // 0x15d1f8c: DecompressPointer r1
    //     0x15d1f8c: add             x1, x1, HEAP, lsl #32
    // 0x15d1f90: r0 = controller()
    //     0x15d1f90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d1f94: mov             x1, x0
    // 0x15d1f98: r0 = offerParams()
    //     0x15d1f98: bl              #0xa0fbe8  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::offerParams
    // 0x15d1f9c: ldur            x2, [fp, #-8]
    // 0x15d1fa0: stur            x0, [fp, #-0x10]
    // 0x15d1fa4: LoadField: r1 = r2->field_13
    //     0x15d1fa4: ldur            w1, [x2, #0x13]
    // 0x15d1fa8: DecompressPointer r1
    //     0x15d1fa8: add             x1, x1, HEAP, lsl #32
    // 0x15d1fac: r0 = of()
    //     0x15d1fac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d1fb0: LoadField: r2 = r0->field_5b
    //     0x15d1fb0: ldur            w2, [x0, #0x5b]
    // 0x15d1fb4: DecompressPointer r2
    //     0x15d1fb4: add             x2, x2, HEAP, lsl #32
    // 0x15d1fb8: ldur            x0, [fp, #-8]
    // 0x15d1fbc: stur            x2, [fp, #-0x18]
    // 0x15d1fc0: LoadField: r1 = r0->field_f
    //     0x15d1fc0: ldur            w1, [x0, #0xf]
    // 0x15d1fc4: DecompressPointer r1
    //     0x15d1fc4: add             x1, x1, HEAP, lsl #32
    // 0x15d1fc8: r0 = controller()
    //     0x15d1fc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d1fcc: LoadField: r1 = r0->field_c7
    //     0x15d1fcc: ldur            w1, [x0, #0xc7]
    // 0x15d1fd0: DecompressPointer r1
    //     0x15d1fd0: add             x1, x1, HEAP, lsl #32
    // 0x15d1fd4: r0 = value()
    //     0x15d1fd4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d1fd8: cmp             w0, NULL
    // 0x15d1fdc: r16 = true
    //     0x15d1fdc: add             x16, NULL, #0x20  ; true
    // 0x15d1fe0: r17 = false
    //     0x15d1fe0: add             x17, NULL, #0x30  ; false
    // 0x15d1fe4: csel            x2, x16, x17, ne
    // 0x15d1fe8: ldur            x0, [fp, #-8]
    // 0x15d1fec: stur            x2, [fp, #-0x20]
    // 0x15d1ff0: LoadField: r1 = r0->field_f
    //     0x15d1ff0: ldur            w1, [x0, #0xf]
    // 0x15d1ff4: DecompressPointer r1
    //     0x15d1ff4: add             x1, x1, HEAP, lsl #32
    // 0x15d1ff8: r0 = controller()
    //     0x15d1ff8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d1ffc: LoadField: r1 = r0->field_c7
    //     0x15d1ffc: ldur            w1, [x0, #0xc7]
    // 0x15d2000: DecompressPointer r1
    //     0x15d2000: add             x1, x1, HEAP, lsl #32
    // 0x15d2004: r0 = value()
    //     0x15d2004: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d2008: str             x0, [SP]
    // 0x15d200c: r0 = _interpolateSingle()
    //     0x15d200c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15d2010: ldur            x2, [fp, #-8]
    // 0x15d2014: stur            x0, [fp, #-0x28]
    // 0x15d2018: LoadField: r1 = r2->field_13
    //     0x15d2018: ldur            w1, [x2, #0x13]
    // 0x15d201c: DecompressPointer r1
    //     0x15d201c: add             x1, x1, HEAP, lsl #32
    // 0x15d2020: r0 = of()
    //     0x15d2020: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d2024: LoadField: r1 = r0->field_87
    //     0x15d2024: ldur            w1, [x0, #0x87]
    // 0x15d2028: DecompressPointer r1
    //     0x15d2028: add             x1, x1, HEAP, lsl #32
    // 0x15d202c: LoadField: r0 = r1->field_27
    //     0x15d202c: ldur            w0, [x1, #0x27]
    // 0x15d2030: DecompressPointer r0
    //     0x15d2030: add             x0, x0, HEAP, lsl #32
    // 0x15d2034: r16 = Instance_Color
    //     0x15d2034: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15d2038: str             x16, [SP]
    // 0x15d203c: mov             x1, x0
    // 0x15d2040: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15d2040: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15d2044: ldr             x4, [x4, #0xf40]
    // 0x15d2048: r0 = copyWith()
    //     0x15d2048: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15d204c: stur            x0, [fp, #-0x30]
    // 0x15d2050: r0 = Text()
    //     0x15d2050: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15d2054: mov             x2, x0
    // 0x15d2058: ldur            x0, [fp, #-0x28]
    // 0x15d205c: stur            x2, [fp, #-0x38]
    // 0x15d2060: StoreField: r2->field_b = r0
    //     0x15d2060: stur            w0, [x2, #0xb]
    // 0x15d2064: ldur            x0, [fp, #-0x30]
    // 0x15d2068: StoreField: r2->field_13 = r0
    //     0x15d2068: stur            w0, [x2, #0x13]
    // 0x15d206c: ldur            x0, [fp, #-8]
    // 0x15d2070: LoadField: r1 = r0->field_13
    //     0x15d2070: ldur            w1, [x0, #0x13]
    // 0x15d2074: DecompressPointer r1
    //     0x15d2074: add             x1, x1, HEAP, lsl #32
    // 0x15d2078: r0 = of()
    //     0x15d2078: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d207c: LoadField: r1 = r0->field_5b
    //     0x15d207c: ldur            w1, [x0, #0x5b]
    // 0x15d2080: DecompressPointer r1
    //     0x15d2080: add             x1, x1, HEAP, lsl #32
    // 0x15d2084: stur            x1, [fp, #-0x28]
    // 0x15d2088: r0 = ColorFilter()
    //     0x15d2088: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d208c: mov             x1, x0
    // 0x15d2090: ldur            x0, [fp, #-0x28]
    // 0x15d2094: stur            x1, [fp, #-0x30]
    // 0x15d2098: StoreField: r1->field_7 = r0
    //     0x15d2098: stur            w0, [x1, #7]
    // 0x15d209c: r0 = Instance_BlendMode
    //     0x15d209c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d20a0: ldr             x0, [x0, #0xb30]
    // 0x15d20a4: StoreField: r1->field_b = r0
    //     0x15d20a4: stur            w0, [x1, #0xb]
    // 0x15d20a8: r0 = 1
    //     0x15d20a8: movz            x0, #0x1
    // 0x15d20ac: StoreField: r1->field_13 = r0
    //     0x15d20ac: stur            x0, [x1, #0x13]
    // 0x15d20b0: r0 = SvgPicture()
    //     0x15d20b0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d20b4: stur            x0, [fp, #-0x28]
    // 0x15d20b8: r16 = Instance_BoxFit
    //     0x15d20b8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15d20bc: ldr             x16, [x16, #0xb18]
    // 0x15d20c0: r30 = 24.000000
    //     0x15d20c0: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15d20c4: ldr             lr, [lr, #0xba8]
    // 0x15d20c8: stp             lr, x16, [SP, #0x10]
    // 0x15d20cc: r16 = 24.000000
    //     0x15d20cc: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15d20d0: ldr             x16, [x16, #0xba8]
    // 0x15d20d4: ldur            lr, [fp, #-0x30]
    // 0x15d20d8: stp             lr, x16, [SP]
    // 0x15d20dc: mov             x1, x0
    // 0x15d20e0: r2 = "assets/images/shopping_bag.svg"
    //     0x15d20e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15d20e4: ldr             x2, [x2, #0xa60]
    // 0x15d20e8: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15d20e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15d20ec: ldr             x4, [x4, #0xa68]
    // 0x15d20f0: r0 = SvgPicture.asset()
    //     0x15d20f0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d20f4: r0 = Badge()
    //     0x15d20f4: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15d20f8: mov             x1, x0
    // 0x15d20fc: ldur            x0, [fp, #-0x18]
    // 0x15d2100: stur            x1, [fp, #-0x30]
    // 0x15d2104: StoreField: r1->field_b = r0
    //     0x15d2104: stur            w0, [x1, #0xb]
    // 0x15d2108: ldur            x0, [fp, #-0x38]
    // 0x15d210c: StoreField: r1->field_27 = r0
    //     0x15d210c: stur            w0, [x1, #0x27]
    // 0x15d2110: ldur            x0, [fp, #-0x20]
    // 0x15d2114: StoreField: r1->field_2b = r0
    //     0x15d2114: stur            w0, [x1, #0x2b]
    // 0x15d2118: ldur            x0, [fp, #-0x28]
    // 0x15d211c: StoreField: r1->field_2f = r0
    //     0x15d211c: stur            w0, [x1, #0x2f]
    // 0x15d2120: r0 = Visibility()
    //     0x15d2120: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15d2124: mov             x1, x0
    // 0x15d2128: ldur            x0, [fp, #-0x30]
    // 0x15d212c: StoreField: r1->field_b = r0
    //     0x15d212c: stur            w0, [x1, #0xb]
    // 0x15d2130: r0 = Instance_SizedBox
    //     0x15d2130: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15d2134: StoreField: r1->field_f = r0
    //     0x15d2134: stur            w0, [x1, #0xf]
    // 0x15d2138: ldur            x0, [fp, #-0x10]
    // 0x15d213c: StoreField: r1->field_13 = r0
    //     0x15d213c: stur            w0, [x1, #0x13]
    // 0x15d2140: r0 = false
    //     0x15d2140: add             x0, NULL, #0x30  ; false
    // 0x15d2144: ArrayStore: r1[0] = r0  ; List_4
    //     0x15d2144: stur            w0, [x1, #0x17]
    // 0x15d2148: StoreField: r1->field_1b = r0
    //     0x15d2148: stur            w0, [x1, #0x1b]
    // 0x15d214c: StoreField: r1->field_1f = r0
    //     0x15d214c: stur            w0, [x1, #0x1f]
    // 0x15d2150: StoreField: r1->field_23 = r0
    //     0x15d2150: stur            w0, [x1, #0x23]
    // 0x15d2154: StoreField: r1->field_27 = r0
    //     0x15d2154: stur            w0, [x1, #0x27]
    // 0x15d2158: StoreField: r1->field_2b = r0
    //     0x15d2158: stur            w0, [x1, #0x2b]
    // 0x15d215c: mov             x0, x1
    // 0x15d2160: b               #0x15d2180
    // 0x15d2164: r0 = false
    //     0x15d2164: add             x0, NULL, #0x30  ; false
    // 0x15d2168: r0 = Container()
    //     0x15d2168: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15d216c: mov             x1, x0
    // 0x15d2170: stur            x0, [fp, #-0x10]
    // 0x15d2174: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15d2174: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15d2178: r0 = Container()
    //     0x15d2178: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15d217c: ldur            x0, [fp, #-0x10]
    // 0x15d2180: stur            x0, [fp, #-0x10]
    // 0x15d2184: r0 = InkWell()
    //     0x15d2184: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15d2188: mov             x3, x0
    // 0x15d218c: ldur            x0, [fp, #-0x10]
    // 0x15d2190: stur            x3, [fp, #-0x18]
    // 0x15d2194: StoreField: r3->field_b = r0
    //     0x15d2194: stur            w0, [x3, #0xb]
    // 0x15d2198: ldur            x2, [fp, #-8]
    // 0x15d219c: r1 = Function '<anonymous closure>':.
    //     0x15d219c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37288] AnonymousClosure: (0x15d1e34), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::appBar (0x15ea93c)
    //     0x15d21a0: ldr             x1, [x1, #0x288]
    // 0x15d21a4: r0 = AllocateClosure()
    //     0x15d21a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d21a8: mov             x1, x0
    // 0x15d21ac: ldur            x0, [fp, #-0x18]
    // 0x15d21b0: StoreField: r0->field_f = r1
    //     0x15d21b0: stur            w1, [x0, #0xf]
    // 0x15d21b4: r1 = true
    //     0x15d21b4: add             x1, NULL, #0x20  ; true
    // 0x15d21b8: StoreField: r0->field_43 = r1
    //     0x15d21b8: stur            w1, [x0, #0x43]
    // 0x15d21bc: r2 = Instance_BoxShape
    //     0x15d21bc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15d21c0: ldr             x2, [x2, #0x80]
    // 0x15d21c4: StoreField: r0->field_47 = r2
    //     0x15d21c4: stur            w2, [x0, #0x47]
    // 0x15d21c8: StoreField: r0->field_6f = r1
    //     0x15d21c8: stur            w1, [x0, #0x6f]
    // 0x15d21cc: r2 = false
    //     0x15d21cc: add             x2, NULL, #0x30  ; false
    // 0x15d21d0: StoreField: r0->field_73 = r2
    //     0x15d21d0: stur            w2, [x0, #0x73]
    // 0x15d21d4: StoreField: r0->field_83 = r1
    //     0x15d21d4: stur            w1, [x0, #0x83]
    // 0x15d21d8: StoreField: r0->field_7b = r2
    //     0x15d21d8: stur            w2, [x0, #0x7b]
    // 0x15d21dc: r0 = Padding()
    //     0x15d21dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15d21e0: r1 = Instance_EdgeInsets
    //     0x15d21e0: add             x1, PP, #0x37, lsl #12  ; [pp+0x37290] Obj!EdgeInsets@d5a111
    //     0x15d21e4: ldr             x1, [x1, #0x290]
    // 0x15d21e8: StoreField: r0->field_f = r1
    //     0x15d21e8: stur            w1, [x0, #0xf]
    // 0x15d21ec: ldur            x1, [fp, #-0x18]
    // 0x15d21f0: StoreField: r0->field_b = r1
    //     0x15d21f0: stur            w1, [x0, #0xb]
    // 0x15d21f4: LeaveFrame
    //     0x15d21f4: mov             SP, fp
    //     0x15d21f8: ldp             fp, lr, [SP], #0x10
    // 0x15d21fc: ret
    //     0x15d21fc: ret             
    // 0x15d2200: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d2200: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d2204: b               #0x15d1f38
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15ea93c, size: 0x2b0
    // 0x15ea93c: EnterFrame
    //     0x15ea93c: stp             fp, lr, [SP, #-0x10]!
    //     0x15ea940: mov             fp, SP
    // 0x15ea944: AllocStack(0x30)
    //     0x15ea944: sub             SP, SP, #0x30
    // 0x15ea948: SetupParameters(LoginView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15ea948: stur            x1, [fp, #-8]
    //     0x15ea94c: stur            x2, [fp, #-0x10]
    // 0x15ea950: CheckStackOverflow
    //     0x15ea950: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15ea954: cmp             SP, x16
    //     0x15ea958: b.ls            #0x15eabe4
    // 0x15ea95c: r1 = 2
    //     0x15ea95c: movz            x1, #0x2
    // 0x15ea960: r0 = AllocateContext()
    //     0x15ea960: bl              #0x16f6108  ; AllocateContextStub
    // 0x15ea964: ldur            x1, [fp, #-8]
    // 0x15ea968: stur            x0, [fp, #-0x18]
    // 0x15ea96c: StoreField: r0->field_f = r1
    //     0x15ea96c: stur            w1, [x0, #0xf]
    // 0x15ea970: ldur            x2, [fp, #-0x10]
    // 0x15ea974: StoreField: r0->field_13 = r2
    //     0x15ea974: stur            w2, [x0, #0x13]
    // 0x15ea978: r0 = Obx()
    //     0x15ea978: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15ea97c: ldur            x2, [fp, #-0x18]
    // 0x15ea980: r1 = Function '<anonymous closure>':.
    //     0x15ea980: add             x1, PP, #0x37, lsl #12  ; [pp+0x37270] AnonymousClosure: (0x15cf880), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::appBar (0x15ea93c)
    //     0x15ea984: ldr             x1, [x1, #0x270]
    // 0x15ea988: stur            x0, [fp, #-0x10]
    // 0x15ea98c: r0 = AllocateClosure()
    //     0x15ea98c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15ea990: mov             x1, x0
    // 0x15ea994: ldur            x0, [fp, #-0x10]
    // 0x15ea998: StoreField: r0->field_b = r1
    //     0x15ea998: stur            w1, [x0, #0xb]
    // 0x15ea99c: ldur            x1, [fp, #-8]
    // 0x15ea9a0: r0 = controller()
    //     0x15ea9a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ea9a4: mov             x1, x0
    // 0x15ea9a8: r0 = verifyPaymentResponse()
    //     0x15ea9a8: bl              #0x91e698  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_variants_controller.dart] CheckoutVariantsController::verifyPaymentResponse
    // 0x15ea9ac: tbnz            w0, #4, #0x15eaa44
    // 0x15ea9b0: ldur            x2, [fp, #-0x18]
    // 0x15ea9b4: LoadField: r1 = r2->field_13
    //     0x15ea9b4: ldur            w1, [x2, #0x13]
    // 0x15ea9b8: DecompressPointer r1
    //     0x15ea9b8: add             x1, x1, HEAP, lsl #32
    // 0x15ea9bc: r0 = of()
    //     0x15ea9bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15ea9c0: LoadField: r1 = r0->field_5b
    //     0x15ea9c0: ldur            w1, [x0, #0x5b]
    // 0x15ea9c4: DecompressPointer r1
    //     0x15ea9c4: add             x1, x1, HEAP, lsl #32
    // 0x15ea9c8: stur            x1, [fp, #-8]
    // 0x15ea9cc: r0 = ColorFilter()
    //     0x15ea9cc: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15ea9d0: mov             x1, x0
    // 0x15ea9d4: ldur            x0, [fp, #-8]
    // 0x15ea9d8: stur            x1, [fp, #-0x20]
    // 0x15ea9dc: StoreField: r1->field_7 = r0
    //     0x15ea9dc: stur            w0, [x1, #7]
    // 0x15ea9e0: r0 = Instance_BlendMode
    //     0x15ea9e0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15ea9e4: ldr             x0, [x0, #0xb30]
    // 0x15ea9e8: StoreField: r1->field_b = r0
    //     0x15ea9e8: stur            w0, [x1, #0xb]
    // 0x15ea9ec: r2 = 1
    //     0x15ea9ec: movz            x2, #0x1
    // 0x15ea9f0: StoreField: r1->field_13 = r2
    //     0x15ea9f0: stur            x2, [x1, #0x13]
    // 0x15ea9f4: r0 = SvgPicture()
    //     0x15ea9f4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15ea9f8: stur            x0, [fp, #-8]
    // 0x15ea9fc: ldur            x16, [fp, #-0x20]
    // 0x15eaa00: str             x16, [SP]
    // 0x15eaa04: mov             x1, x0
    // 0x15eaa08: r2 = "assets/images/search.svg"
    //     0x15eaa08: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15eaa0c: ldr             x2, [x2, #0xa30]
    // 0x15eaa10: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15eaa10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15eaa14: ldr             x4, [x4, #0xa38]
    // 0x15eaa18: r0 = SvgPicture.asset()
    //     0x15eaa18: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15eaa1c: r0 = Align()
    //     0x15eaa1c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15eaa20: r3 = Instance_Alignment
    //     0x15eaa20: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15eaa24: ldr             x3, [x3, #0xb10]
    // 0x15eaa28: StoreField: r0->field_f = r3
    //     0x15eaa28: stur            w3, [x0, #0xf]
    // 0x15eaa2c: r4 = 1.000000
    //     0x15eaa2c: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15eaa30: StoreField: r0->field_13 = r4
    //     0x15eaa30: stur            w4, [x0, #0x13]
    // 0x15eaa34: ArrayStore: r0[0] = r4  ; List_4
    //     0x15eaa34: stur            w4, [x0, #0x17]
    // 0x15eaa38: ldur            x1, [fp, #-8]
    // 0x15eaa3c: StoreField: r0->field_b = r1
    //     0x15eaa3c: stur            w1, [x0, #0xb]
    // 0x15eaa40: b               #0x15eaaf4
    // 0x15eaa44: ldur            x5, [fp, #-0x18]
    // 0x15eaa48: r4 = 1.000000
    //     0x15eaa48: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15eaa4c: r0 = Instance_BlendMode
    //     0x15eaa4c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15eaa50: ldr             x0, [x0, #0xb30]
    // 0x15eaa54: r3 = Instance_Alignment
    //     0x15eaa54: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15eaa58: ldr             x3, [x3, #0xb10]
    // 0x15eaa5c: r2 = 1
    //     0x15eaa5c: movz            x2, #0x1
    // 0x15eaa60: LoadField: r1 = r5->field_13
    //     0x15eaa60: ldur            w1, [x5, #0x13]
    // 0x15eaa64: DecompressPointer r1
    //     0x15eaa64: add             x1, x1, HEAP, lsl #32
    // 0x15eaa68: r0 = of()
    //     0x15eaa68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15eaa6c: LoadField: r1 = r0->field_5b
    //     0x15eaa6c: ldur            w1, [x0, #0x5b]
    // 0x15eaa70: DecompressPointer r1
    //     0x15eaa70: add             x1, x1, HEAP, lsl #32
    // 0x15eaa74: stur            x1, [fp, #-8]
    // 0x15eaa78: r0 = ColorFilter()
    //     0x15eaa78: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15eaa7c: mov             x1, x0
    // 0x15eaa80: ldur            x0, [fp, #-8]
    // 0x15eaa84: stur            x1, [fp, #-0x20]
    // 0x15eaa88: StoreField: r1->field_7 = r0
    //     0x15eaa88: stur            w0, [x1, #7]
    // 0x15eaa8c: r0 = Instance_BlendMode
    //     0x15eaa8c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15eaa90: ldr             x0, [x0, #0xb30]
    // 0x15eaa94: StoreField: r1->field_b = r0
    //     0x15eaa94: stur            w0, [x1, #0xb]
    // 0x15eaa98: r0 = 1
    //     0x15eaa98: movz            x0, #0x1
    // 0x15eaa9c: StoreField: r1->field_13 = r0
    //     0x15eaa9c: stur            x0, [x1, #0x13]
    // 0x15eaaa0: r0 = SvgPicture()
    //     0x15eaaa0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15eaaa4: stur            x0, [fp, #-8]
    // 0x15eaaa8: ldur            x16, [fp, #-0x20]
    // 0x15eaaac: str             x16, [SP]
    // 0x15eaab0: mov             x1, x0
    // 0x15eaab4: r2 = "assets/images/appbar_arrow.svg"
    //     0x15eaab4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15eaab8: ldr             x2, [x2, #0xa40]
    // 0x15eaabc: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15eaabc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15eaac0: ldr             x4, [x4, #0xa38]
    // 0x15eaac4: r0 = SvgPicture.asset()
    //     0x15eaac4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15eaac8: r0 = Align()
    //     0x15eaac8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15eaacc: mov             x1, x0
    // 0x15eaad0: r0 = Instance_Alignment
    //     0x15eaad0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15eaad4: ldr             x0, [x0, #0xb10]
    // 0x15eaad8: StoreField: r1->field_f = r0
    //     0x15eaad8: stur            w0, [x1, #0xf]
    // 0x15eaadc: r0 = 1.000000
    //     0x15eaadc: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15eaae0: StoreField: r1->field_13 = r0
    //     0x15eaae0: stur            w0, [x1, #0x13]
    // 0x15eaae4: ArrayStore: r1[0] = r0  ; List_4
    //     0x15eaae4: stur            w0, [x1, #0x17]
    // 0x15eaae8: ldur            x0, [fp, #-8]
    // 0x15eaaec: StoreField: r1->field_b = r0
    //     0x15eaaec: stur            w0, [x1, #0xb]
    // 0x15eaaf0: mov             x0, x1
    // 0x15eaaf4: stur            x0, [fp, #-8]
    // 0x15eaaf8: r0 = InkWell()
    //     0x15eaaf8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15eaafc: mov             x3, x0
    // 0x15eab00: ldur            x0, [fp, #-8]
    // 0x15eab04: stur            x3, [fp, #-0x20]
    // 0x15eab08: StoreField: r3->field_b = r0
    //     0x15eab08: stur            w0, [x3, #0xb]
    // 0x15eab0c: ldur            x2, [fp, #-0x18]
    // 0x15eab10: r1 = Function '<anonymous closure>':.
    //     0x15eab10: add             x1, PP, #0x37, lsl #12  ; [pp+0x37278] AnonymousClosure: (0x15eabec), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::appBar (0x15ea93c)
    //     0x15eab14: ldr             x1, [x1, #0x278]
    // 0x15eab18: r0 = AllocateClosure()
    //     0x15eab18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15eab1c: ldur            x2, [fp, #-0x20]
    // 0x15eab20: StoreField: r2->field_f = r0
    //     0x15eab20: stur            w0, [x2, #0xf]
    // 0x15eab24: r0 = true
    //     0x15eab24: add             x0, NULL, #0x20  ; true
    // 0x15eab28: StoreField: r2->field_43 = r0
    //     0x15eab28: stur            w0, [x2, #0x43]
    // 0x15eab2c: r1 = Instance_BoxShape
    //     0x15eab2c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15eab30: ldr             x1, [x1, #0x80]
    // 0x15eab34: StoreField: r2->field_47 = r1
    //     0x15eab34: stur            w1, [x2, #0x47]
    // 0x15eab38: StoreField: r2->field_6f = r0
    //     0x15eab38: stur            w0, [x2, #0x6f]
    // 0x15eab3c: r1 = false
    //     0x15eab3c: add             x1, NULL, #0x30  ; false
    // 0x15eab40: StoreField: r2->field_73 = r1
    //     0x15eab40: stur            w1, [x2, #0x73]
    // 0x15eab44: StoreField: r2->field_83 = r0
    //     0x15eab44: stur            w0, [x2, #0x83]
    // 0x15eab48: StoreField: r2->field_7b = r1
    //     0x15eab48: stur            w1, [x2, #0x7b]
    // 0x15eab4c: r0 = Obx()
    //     0x15eab4c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15eab50: ldur            x2, [fp, #-0x18]
    // 0x15eab54: r1 = Function '<anonymous closure>':.
    //     0x15eab54: add             x1, PP, #0x37, lsl #12  ; [pp+0x37280] AnonymousClosure: (0x15d1f10), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::appBar (0x15ea93c)
    //     0x15eab58: ldr             x1, [x1, #0x280]
    // 0x15eab5c: stur            x0, [fp, #-8]
    // 0x15eab60: r0 = AllocateClosure()
    //     0x15eab60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15eab64: mov             x1, x0
    // 0x15eab68: ldur            x0, [fp, #-8]
    // 0x15eab6c: StoreField: r0->field_b = r1
    //     0x15eab6c: stur            w1, [x0, #0xb]
    // 0x15eab70: r1 = Null
    //     0x15eab70: mov             x1, NULL
    // 0x15eab74: r2 = 2
    //     0x15eab74: movz            x2, #0x2
    // 0x15eab78: r0 = AllocateArray()
    //     0x15eab78: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15eab7c: mov             x2, x0
    // 0x15eab80: ldur            x0, [fp, #-8]
    // 0x15eab84: stur            x2, [fp, #-0x18]
    // 0x15eab88: StoreField: r2->field_f = r0
    //     0x15eab88: stur            w0, [x2, #0xf]
    // 0x15eab8c: r1 = <Widget>
    //     0x15eab8c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15eab90: r0 = AllocateGrowableArray()
    //     0x15eab90: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15eab94: mov             x1, x0
    // 0x15eab98: ldur            x0, [fp, #-0x18]
    // 0x15eab9c: stur            x1, [fp, #-8]
    // 0x15eaba0: StoreField: r1->field_f = r0
    //     0x15eaba0: stur            w0, [x1, #0xf]
    // 0x15eaba4: r0 = 2
    //     0x15eaba4: movz            x0, #0x2
    // 0x15eaba8: StoreField: r1->field_b = r0
    //     0x15eaba8: stur            w0, [x1, #0xb]
    // 0x15eabac: r0 = AppBar()
    //     0x15eabac: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15eabb0: stur            x0, [fp, #-0x18]
    // 0x15eabb4: ldur            x16, [fp, #-0x10]
    // 0x15eabb8: ldur            lr, [fp, #-8]
    // 0x15eabbc: stp             lr, x16, [SP]
    // 0x15eabc0: mov             x1, x0
    // 0x15eabc4: ldur            x2, [fp, #-0x20]
    // 0x15eabc8: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15eabc8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15eabcc: ldr             x4, [x4, #0xa58]
    // 0x15eabd0: r0 = AppBar()
    //     0x15eabd0: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15eabd4: ldur            x0, [fp, #-0x18]
    // 0x15eabd8: LeaveFrame
    //     0x15eabd8: mov             SP, fp
    //     0x15eabdc: ldp             fp, lr, [SP], #0x10
    // 0x15eabe0: ret
    //     0x15eabe0: ret             
    // 0x15eabe4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15eabe4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15eabe8: b               #0x15ea95c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15eabec, size: 0xb4
    // 0x15eabec: EnterFrame
    //     0x15eabec: stp             fp, lr, [SP, #-0x10]!
    //     0x15eabf0: mov             fp, SP
    // 0x15eabf4: AllocStack(0x18)
    //     0x15eabf4: sub             SP, SP, #0x18
    // 0x15eabf8: SetupParameters()
    //     0x15eabf8: ldr             x0, [fp, #0x10]
    //     0x15eabfc: ldur            w3, [x0, #0x17]
    //     0x15eac00: add             x3, x3, HEAP, lsl #32
    //     0x15eac04: stur            x3, [fp, #-8]
    // 0x15eac08: CheckStackOverflow
    //     0x15eac08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15eac0c: cmp             SP, x16
    //     0x15eac10: b.ls            #0x15eac98
    // 0x15eac14: LoadField: r1 = r3->field_f
    //     0x15eac14: ldur            w1, [x3, #0xf]
    // 0x15eac18: DecompressPointer r1
    //     0x15eac18: add             x1, x1, HEAP, lsl #32
    // 0x15eac1c: r2 = false
    //     0x15eac1c: add             x2, NULL, #0x30  ; false
    // 0x15eac20: r0 = showLoading()
    //     0x15eac20: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15eac24: ldur            x0, [fp, #-8]
    // 0x15eac28: LoadField: r1 = r0->field_f
    //     0x15eac28: ldur            w1, [x0, #0xf]
    // 0x15eac2c: DecompressPointer r1
    //     0x15eac2c: add             x1, x1, HEAP, lsl #32
    // 0x15eac30: r0 = controller()
    //     0x15eac30: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15eac34: LoadField: r1 = r0->field_bf
    //     0x15eac34: ldur            w1, [x0, #0xbf]
    // 0x15eac38: DecompressPointer r1
    //     0x15eac38: add             x1, x1, HEAP, lsl #32
    // 0x15eac3c: r0 = value()
    //     0x15eac3c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15eac40: tbnz            w0, #4, #0x15eac78
    // 0x15eac44: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15eac44: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15eac48: ldr             x0, [x0, #0x1c80]
    //     0x15eac4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15eac50: cmp             w0, w16
    //     0x15eac54: b.ne            #0x15eac60
    //     0x15eac58: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15eac5c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15eac60: r16 = "/search"
    //     0x15eac60: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15eac64: ldr             x16, [x16, #0x838]
    // 0x15eac68: stp             x16, NULL, [SP]
    // 0x15eac6c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15eac6c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15eac70: r0 = GetNavigation.toNamed()
    //     0x15eac70: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15eac74: b               #0x15eac88
    // 0x15eac78: ldur            x0, [fp, #-8]
    // 0x15eac7c: LoadField: r1 = r0->field_f
    //     0x15eac7c: ldur            w1, [x0, #0xf]
    // 0x15eac80: DecompressPointer r1
    //     0x15eac80: add             x1, x1, HEAP, lsl #32
    // 0x15eac84: r0 = onBackPress()
    //     0x15eac84: bl              #0x15064a4  ; [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::onBackPress
    // 0x15eac88: r0 = Null
    //     0x15eac88: mov             x0, NULL
    // 0x15eac8c: LeaveFrame
    //     0x15eac8c: mov             SP, fp
    //     0x15eac90: ldp             fp, lr, [SP], #0x10
    // 0x15eac94: ret
    //     0x15eac94: ret             
    // 0x15eac98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15eac98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15eac9c: b               #0x15eac14
  }
}
