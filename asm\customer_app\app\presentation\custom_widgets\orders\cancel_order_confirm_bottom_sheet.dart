// lib: , url: package:customer_app/app/presentation/custom_widgets/orders/cancel_order_confirm_bottom_sheet.dart

// class id: 1049071, size: 0x8
class :: {

  [closure] static String? _validateRemarks(dynamic, String?) {
    // ** addr: 0x9978c4, size: 0x30
    // 0x9978c4: EnterFrame
    //     0x9978c4: stp             fp, lr, [SP, #-0x10]!
    //     0x9978c8: mov             fp, SP
    // 0x9978cc: CheckStackOverflow
    //     0x9978cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9978d0: cmp             SP, x16
    //     0x9978d4: b.ls            #0x9978ec
    // 0x9978d8: ldr             x1, [fp, #0x10]
    // 0x9978dc: r0 = _validateRemarks()
    //     0x9978dc: bl              #0x9978f4  ; [package:customer_app/app/presentation/custom_widgets/orders/cancel_order_confirm_bottom_sheet.dart] ::_validateRemarks
    // 0x9978e0: LeaveFrame
    //     0x9978e0: mov             SP, fp
    //     0x9978e4: ldp             fp, lr, [SP], #0x10
    // 0x9978e8: ret
    //     0x9978e8: ret             
    // 0x9978ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9978ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9978f0: b               #0x9978d8
  }
  static _ _validateRemarks(/* No info */) {
    // ** addr: 0x9978f4, size: 0x54
    // 0x9978f4: EnterFrame
    //     0x9978f4: stp             fp, lr, [SP, #-0x10]!
    //     0x9978f8: mov             fp, SP
    // 0x9978fc: CheckStackOverflow
    //     0x9978fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x997900: cmp             SP, x16
    //     0x997904: b.ls            #0x997940
    // 0x997908: cmp             w1, NULL
    // 0x99790c: b.ne            #0x997914
    // 0x997910: r1 = ""
    //     0x997910: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x997914: r0 = checkEmpty()
    //     0x997914: bl              #0x8fc4b0  ; [package:customer_app/app/core/utils/utils.dart] Utils::checkEmpty
    // 0x997918: tbnz            w0, #4, #0x997930
    // 0x99791c: r0 = "Reason is required"
    //     0x99791c: add             x0, PP, #0x5c, lsl #12  ; [pp+0x5c018] "Reason is required"
    //     0x997920: ldr             x0, [x0, #0x18]
    // 0x997924: LeaveFrame
    //     0x997924: mov             SP, fp
    //     0x997928: ldp             fp, lr, [SP], #0x10
    // 0x99792c: ret
    //     0x99792c: ret             
    // 0x997930: r0 = Null
    //     0x997930: mov             x0, NULL
    // 0x997934: LeaveFrame
    //     0x997934: mov             SP, fp
    //     0x997938: ldp             fp, lr, [SP], #0x10
    // 0x99793c: ret
    //     0x99793c: ret             
    // 0x997940: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x997940: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x997944: b               #0x997908
  }
}

// class id: 3587, size: 0x24, field offset: 0x14
class _CancelOrderConfirmBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0x991580, size: 0x2218
    // 0x991580: EnterFrame
    //     0x991580: stp             fp, lr, [SP, #-0x10]!
    //     0x991584: mov             fp, SP
    // 0x991588: AllocStack(0xc0)
    //     0x991588: sub             SP, SP, #0xc0
    // 0x99158c: SetupParameters(_CancelOrderConfirmBottomSheetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x99158c: stur            x1, [fp, #-8]
    //     0x991590: stur            x2, [fp, #-0x10]
    // 0x991594: CheckStackOverflow
    //     0x991594: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x991598: cmp             SP, x16
    //     0x99159c: b.ls            #0x9936fc
    // 0x9915a0: r1 = 2
    //     0x9915a0: movz            x1, #0x2
    // 0x9915a4: r0 = AllocateContext()
    //     0x9915a4: bl              #0x16f6108  ; AllocateContextStub
    // 0x9915a8: mov             x3, x0
    // 0x9915ac: ldur            x0, [fp, #-8]
    // 0x9915b0: stur            x3, [fp, #-0x18]
    // 0x9915b4: StoreField: r3->field_f = r0
    //     0x9915b4: stur            w0, [x3, #0xf]
    // 0x9915b8: ldur            x1, [fp, #-0x10]
    // 0x9915bc: StoreField: r3->field_13 = r1
    //     0x9915bc: stur            w1, [x3, #0x13]
    // 0x9915c0: LoadField: r1 = r0->field_1f
    //     0x9915c0: ldur            w1, [x0, #0x1f]
    // 0x9915c4: DecompressPointer r1
    //     0x9915c4: add             x1, x1, HEAP, lsl #32
    // 0x9915c8: LoadField: r2 = r0->field_b
    //     0x9915c8: ldur            w2, [x0, #0xb]
    // 0x9915cc: DecompressPointer r2
    //     0x9915cc: add             x2, x2, HEAP, lsl #32
    // 0x9915d0: cmp             w2, NULL
    // 0x9915d4: b.eq            #0x993704
    // 0x9915d8: LoadField: r4 = r2->field_b
    //     0x9915d8: ldur            w4, [x2, #0xb]
    // 0x9915dc: DecompressPointer r4
    //     0x9915dc: add             x4, x4, HEAP, lsl #32
    // 0x9915e0: cmp             w4, NULL
    // 0x9915e4: b.ne            #0x9915f0
    // 0x9915e8: r2 = Null
    //     0x9915e8: mov             x2, NULL
    // 0x9915ec: b               #0x9915f8
    // 0x9915f0: LoadField: r2 = r4->field_13
    //     0x9915f0: ldur            w2, [x4, #0x13]
    // 0x9915f4: DecompressPointer r2
    //     0x9915f4: add             x2, x2, HEAP, lsl #32
    // 0x9915f8: cmp             w2, NULL
    // 0x9915fc: b.ne            #0x991604
    // 0x991600: r2 = true
    //     0x991600: add             x2, NULL, #0x20  ; true
    // 0x991604: r0 = value=()
    //     0x991604: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x991608: ldur            x0, [fp, #-8]
    // 0x99160c: LoadField: r1 = r0->field_b
    //     0x99160c: ldur            w1, [x0, #0xb]
    // 0x991610: DecompressPointer r1
    //     0x991610: add             x1, x1, HEAP, lsl #32
    // 0x991614: cmp             w1, NULL
    // 0x991618: b.eq            #0x993708
    // 0x99161c: LoadField: r2 = r1->field_13
    //     0x99161c: ldur            w2, [x1, #0x13]
    // 0x991620: DecompressPointer r2
    //     0x991620: add             x2, x2, HEAP, lsl #32
    // 0x991624: ldur            x3, [fp, #-0x18]
    // 0x991628: stur            x2, [fp, #-0x10]
    // 0x99162c: LoadField: r1 = r3->field_13
    //     0x99162c: ldur            w1, [x3, #0x13]
    // 0x991630: DecompressPointer r1
    //     0x991630: add             x1, x1, HEAP, lsl #32
    // 0x991634: r0 = of()
    //     0x991634: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x991638: LoadField: r1 = r0->field_87
    //     0x991638: ldur            w1, [x0, #0x87]
    // 0x99163c: DecompressPointer r1
    //     0x99163c: add             x1, x1, HEAP, lsl #32
    // 0x991640: LoadField: r0 = r1->field_7
    //     0x991640: ldur            w0, [x1, #7]
    // 0x991644: DecompressPointer r0
    //     0x991644: add             x0, x0, HEAP, lsl #32
    // 0x991648: stur            x0, [fp, #-0x20]
    // 0x99164c: r1 = Instance_Color
    //     0x99164c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x991650: d0 = 0.700000
    //     0x991650: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x991654: ldr             d0, [x17, #0xf48]
    // 0x991658: r0 = withOpacity()
    //     0x991658: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x99165c: r16 = 16.000000
    //     0x99165c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x991660: ldr             x16, [x16, #0x188]
    // 0x991664: stp             x16, x0, [SP]
    // 0x991668: ldur            x1, [fp, #-0x20]
    // 0x99166c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x99166c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x991670: ldr             x4, [x4, #0x9b8]
    // 0x991674: r0 = copyWith()
    //     0x991674: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x991678: stur            x0, [fp, #-0x20]
    // 0x99167c: r0 = Text()
    //     0x99167c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x991680: mov             x1, x0
    // 0x991684: ldur            x0, [fp, #-0x10]
    // 0x991688: stur            x1, [fp, #-0x28]
    // 0x99168c: StoreField: r1->field_b = r0
    //     0x99168c: stur            w0, [x1, #0xb]
    // 0x991690: ldur            x0, [fp, #-0x20]
    // 0x991694: StoreField: r1->field_13 = r0
    //     0x991694: stur            w0, [x1, #0x13]
    // 0x991698: r0 = SvgPicture()
    //     0x991698: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x99169c: mov             x1, x0
    // 0x9916a0: r2 = "assets/images/x.svg"
    //     0x9916a0: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0x9916a4: ldr             x2, [x2, #0x5e8]
    // 0x9916a8: stur            x0, [fp, #-0x10]
    // 0x9916ac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9916ac: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9916b0: r0 = SvgPicture.asset()
    //     0x9916b0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x9916b4: r0 = InkWell()
    //     0x9916b4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x9916b8: mov             x3, x0
    // 0x9916bc: ldur            x0, [fp, #-0x10]
    // 0x9916c0: stur            x3, [fp, #-0x20]
    // 0x9916c4: StoreField: r3->field_b = r0
    //     0x9916c4: stur            w0, [x3, #0xb]
    // 0x9916c8: ldur            x2, [fp, #-0x18]
    // 0x9916cc: r1 = Function '<anonymous closure>':.
    //     0x9916cc: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bf40] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0x9916d0: ldr             x1, [x1, #0xf40]
    // 0x9916d4: r0 = AllocateClosure()
    //     0x9916d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9916d8: mov             x1, x0
    // 0x9916dc: ldur            x0, [fp, #-0x20]
    // 0x9916e0: StoreField: r0->field_f = r1
    //     0x9916e0: stur            w1, [x0, #0xf]
    // 0x9916e4: r3 = true
    //     0x9916e4: add             x3, NULL, #0x20  ; true
    // 0x9916e8: StoreField: r0->field_43 = r3
    //     0x9916e8: stur            w3, [x0, #0x43]
    // 0x9916ec: r4 = Instance_BoxShape
    //     0x9916ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x9916f0: ldr             x4, [x4, #0x80]
    // 0x9916f4: StoreField: r0->field_47 = r4
    //     0x9916f4: stur            w4, [x0, #0x47]
    // 0x9916f8: StoreField: r0->field_6f = r3
    //     0x9916f8: stur            w3, [x0, #0x6f]
    // 0x9916fc: r5 = false
    //     0x9916fc: add             x5, NULL, #0x30  ; false
    // 0x991700: StoreField: r0->field_73 = r5
    //     0x991700: stur            w5, [x0, #0x73]
    // 0x991704: StoreField: r0->field_83 = r3
    //     0x991704: stur            w3, [x0, #0x83]
    // 0x991708: StoreField: r0->field_7b = r5
    //     0x991708: stur            w5, [x0, #0x7b]
    // 0x99170c: r1 = Null
    //     0x99170c: mov             x1, NULL
    // 0x991710: r2 = 4
    //     0x991710: movz            x2, #0x4
    // 0x991714: r0 = AllocateArray()
    //     0x991714: bl              #0x16f7198  ; AllocateArrayStub
    // 0x991718: mov             x2, x0
    // 0x99171c: ldur            x0, [fp, #-0x28]
    // 0x991720: stur            x2, [fp, #-0x10]
    // 0x991724: StoreField: r2->field_f = r0
    //     0x991724: stur            w0, [x2, #0xf]
    // 0x991728: ldur            x0, [fp, #-0x20]
    // 0x99172c: StoreField: r2->field_13 = r0
    //     0x99172c: stur            w0, [x2, #0x13]
    // 0x991730: r1 = <Widget>
    //     0x991730: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x991734: r0 = AllocateGrowableArray()
    //     0x991734: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x991738: mov             x1, x0
    // 0x99173c: ldur            x0, [fp, #-0x10]
    // 0x991740: stur            x1, [fp, #-0x20]
    // 0x991744: StoreField: r1->field_f = r0
    //     0x991744: stur            w0, [x1, #0xf]
    // 0x991748: r2 = 4
    //     0x991748: movz            x2, #0x4
    // 0x99174c: StoreField: r1->field_b = r2
    //     0x99174c: stur            w2, [x1, #0xb]
    // 0x991750: r0 = Row()
    //     0x991750: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x991754: mov             x1, x0
    // 0x991758: r0 = Instance_Axis
    //     0x991758: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x99175c: stur            x1, [fp, #-0x10]
    // 0x991760: StoreField: r1->field_f = r0
    //     0x991760: stur            w0, [x1, #0xf]
    // 0x991764: r2 = Instance_MainAxisAlignment
    //     0x991764: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x991768: ldr             x2, [x2, #0xa8]
    // 0x99176c: StoreField: r1->field_13 = r2
    //     0x99176c: stur            w2, [x1, #0x13]
    // 0x991770: r3 = Instance_MainAxisSize
    //     0x991770: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x991774: ldr             x3, [x3, #0xa10]
    // 0x991778: ArrayStore: r1[0] = r3  ; List_4
    //     0x991778: stur            w3, [x1, #0x17]
    // 0x99177c: r4 = Instance_CrossAxisAlignment
    //     0x99177c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x991780: ldr             x4, [x4, #0xa18]
    // 0x991784: StoreField: r1->field_1b = r4
    //     0x991784: stur            w4, [x1, #0x1b]
    // 0x991788: r5 = Instance_VerticalDirection
    //     0x991788: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x99178c: ldr             x5, [x5, #0xa20]
    // 0x991790: StoreField: r1->field_23 = r5
    //     0x991790: stur            w5, [x1, #0x23]
    // 0x991794: r6 = Instance_Clip
    //     0x991794: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x991798: ldr             x6, [x6, #0x38]
    // 0x99179c: StoreField: r1->field_2b = r6
    //     0x99179c: stur            w6, [x1, #0x2b]
    // 0x9917a0: StoreField: r1->field_2f = rZR
    //     0x9917a0: stur            xzr, [x1, #0x2f]
    // 0x9917a4: ldur            x7, [fp, #-0x20]
    // 0x9917a8: StoreField: r1->field_b = r7
    //     0x9917a8: stur            w7, [x1, #0xb]
    // 0x9917ac: ldur            x7, [fp, #-8]
    // 0x9917b0: LoadField: r8 = r7->field_b
    //     0x9917b0: ldur            w8, [x7, #0xb]
    // 0x9917b4: DecompressPointer r8
    //     0x9917b4: add             x8, x8, HEAP, lsl #32
    // 0x9917b8: cmp             w8, NULL
    // 0x9917bc: b.eq            #0x99370c
    // 0x9917c0: LoadField: r9 = r8->field_f
    //     0x9917c0: ldur            w9, [x8, #0xf]
    // 0x9917c4: DecompressPointer r9
    //     0x9917c4: add             x9, x9, HEAP, lsl #32
    // 0x9917c8: str             x9, [SP]
    // 0x9917cc: r0 = _interpolateSingle()
    //     0x9917cc: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x9917d0: ldur            x2, [fp, #-0x18]
    // 0x9917d4: stur            x0, [fp, #-0x20]
    // 0x9917d8: LoadField: r1 = r2->field_13
    //     0x9917d8: ldur            w1, [x2, #0x13]
    // 0x9917dc: DecompressPointer r1
    //     0x9917dc: add             x1, x1, HEAP, lsl #32
    // 0x9917e0: r0 = of()
    //     0x9917e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9917e4: LoadField: r1 = r0->field_87
    //     0x9917e4: ldur            w1, [x0, #0x87]
    // 0x9917e8: DecompressPointer r1
    //     0x9917e8: add             x1, x1, HEAP, lsl #32
    // 0x9917ec: LoadField: r0 = r1->field_2b
    //     0x9917ec: ldur            w0, [x1, #0x2b]
    // 0x9917f0: DecompressPointer r0
    //     0x9917f0: add             x0, x0, HEAP, lsl #32
    // 0x9917f4: stur            x0, [fp, #-0x28]
    // 0x9917f8: r1 = Instance_Color
    //     0x9917f8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9917fc: d0 = 0.400000
    //     0x9917fc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x991800: r0 = withOpacity()
    //     0x991800: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x991804: r16 = 14.000000
    //     0x991804: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x991808: ldr             x16, [x16, #0x1d8]
    // 0x99180c: stp             x16, x0, [SP]
    // 0x991810: ldur            x1, [fp, #-0x28]
    // 0x991814: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x991814: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x991818: ldr             x4, [x4, #0x9b8]
    // 0x99181c: r0 = copyWith()
    //     0x99181c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x991820: stur            x0, [fp, #-0x28]
    // 0x991824: r0 = Text()
    //     0x991824: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x991828: mov             x3, x0
    // 0x99182c: ldur            x0, [fp, #-0x20]
    // 0x991830: stur            x3, [fp, #-0x30]
    // 0x991834: StoreField: r3->field_b = r0
    //     0x991834: stur            w0, [x3, #0xb]
    // 0x991838: ldur            x0, [fp, #-0x28]
    // 0x99183c: StoreField: r3->field_13 = r0
    //     0x99183c: stur            w0, [x3, #0x13]
    // 0x991840: r1 = Null
    //     0x991840: mov             x1, NULL
    // 0x991844: r2 = 8
    //     0x991844: movz            x2, #0x8
    // 0x991848: r0 = AllocateArray()
    //     0x991848: bl              #0x16f7198  ; AllocateArrayStub
    // 0x99184c: mov             x2, x0
    // 0x991850: ldur            x0, [fp, #-0x10]
    // 0x991854: stur            x2, [fp, #-0x20]
    // 0x991858: StoreField: r2->field_f = r0
    //     0x991858: stur            w0, [x2, #0xf]
    // 0x99185c: r16 = Instance_SizedBox
    //     0x99185c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0x991860: ldr             x16, [x16, #0x578]
    // 0x991864: StoreField: r2->field_13 = r16
    //     0x991864: stur            w16, [x2, #0x13]
    // 0x991868: ldur            x0, [fp, #-0x30]
    // 0x99186c: ArrayStore: r2[0] = r0  ; List_4
    //     0x99186c: stur            w0, [x2, #0x17]
    // 0x991870: r16 = Instance_SizedBox
    //     0x991870: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d68] Obj!SizedBox@d67d41
    //     0x991874: ldr             x16, [x16, #0xd68]
    // 0x991878: StoreField: r2->field_1b = r16
    //     0x991878: stur            w16, [x2, #0x1b]
    // 0x99187c: r1 = <Widget>
    //     0x99187c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x991880: r0 = AllocateGrowableArray()
    //     0x991880: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x991884: mov             x1, x0
    // 0x991888: ldur            x0, [fp, #-0x20]
    // 0x99188c: stur            x1, [fp, #-0x10]
    // 0x991890: StoreField: r1->field_f = r0
    //     0x991890: stur            w0, [x1, #0xf]
    // 0x991894: r0 = 8
    //     0x991894: movz            x0, #0x8
    // 0x991898: StoreField: r1->field_b = r0
    //     0x991898: stur            w0, [x1, #0xb]
    // 0x99189c: ldur            x2, [fp, #-8]
    // 0x9918a0: LoadField: r0 = r2->field_b
    //     0x9918a0: ldur            w0, [x2, #0xb]
    // 0x9918a4: DecompressPointer r0
    //     0x9918a4: add             x0, x0, HEAP, lsl #32
    // 0x9918a8: cmp             w0, NULL
    // 0x9918ac: b.eq            #0x993710
    // 0x9918b0: LoadField: r3 = r0->field_b
    //     0x9918b0: ldur            w3, [x0, #0xb]
    // 0x9918b4: DecompressPointer r3
    //     0x9918b4: add             x3, x3, HEAP, lsl #32
    // 0x9918b8: cmp             w3, NULL
    // 0x9918bc: b.eq            #0x9920ac
    // 0x9918c0: LoadField: r4 = r3->field_7
    //     0x9918c0: ldur            w4, [x3, #7]
    // 0x9918c4: DecompressPointer r4
    //     0x9918c4: add             x4, x4, HEAP, lsl #32
    // 0x9918c8: cmp             w4, NULL
    // 0x9918cc: b.eq            #0x9920a4
    // 0x9918d0: LoadField: r3 = r0->field_2f
    //     0x9918d0: ldur            w3, [x0, #0x2f]
    // 0x9918d4: DecompressPointer r3
    //     0x9918d4: add             x3, x3, HEAP, lsl #32
    // 0x9918d8: r0 = LoadClassIdInstr(r3)
    //     0x9918d8: ldur            x0, [x3, #-1]
    //     0x9918dc: ubfx            x0, x0, #0xc, #0x14
    // 0x9918e0: r16 = "cod"
    //     0x9918e0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a28] "cod"
    //     0x9918e4: ldr             x16, [x16, #0xa28]
    // 0x9918e8: stp             x16, x3, [SP]
    // 0x9918ec: mov             lr, x0
    // 0x9918f0: ldr             lr, [x21, lr, lsl #3]
    // 0x9918f4: blr             lr
    // 0x9918f8: tbz             w0, #4, #0x99209c
    // 0x9918fc: ldur            x0, [fp, #-8]
    // 0x991900: ldur            x2, [fp, #-0x18]
    // 0x991904: LoadField: r1 = r2->field_13
    //     0x991904: ldur            w1, [x2, #0x13]
    // 0x991908: DecompressPointer r1
    //     0x991908: add             x1, x1, HEAP, lsl #32
    // 0x99190c: r0 = of()
    //     0x99190c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x991910: LoadField: r1 = r0->field_87
    //     0x991910: ldur            w1, [x0, #0x87]
    // 0x991914: DecompressPointer r1
    //     0x991914: add             x1, x1, HEAP, lsl #32
    // 0x991918: LoadField: r0 = r1->field_2b
    //     0x991918: ldur            w0, [x1, #0x2b]
    // 0x99191c: DecompressPointer r0
    //     0x99191c: add             x0, x0, HEAP, lsl #32
    // 0x991920: stur            x0, [fp, #-0x20]
    // 0x991924: r1 = Instance_Color
    //     0x991924: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x991928: d0 = 0.700000
    //     0x991928: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x99192c: ldr             d0, [x17, #0xf48]
    // 0x991930: r0 = withOpacity()
    //     0x991930: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x991934: r16 = 14.000000
    //     0x991934: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x991938: ldr             x16, [x16, #0x1d8]
    // 0x99193c: stp             x0, x16, [SP]
    // 0x991940: ldur            x1, [fp, #-0x20]
    // 0x991944: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x991944: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x991948: ldr             x4, [x4, #0xaa0]
    // 0x99194c: r0 = copyWith()
    //     0x99194c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x991950: stur            x0, [fp, #-0x20]
    // 0x991954: r0 = Text()
    //     0x991954: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x991958: mov             x2, x0
    // 0x99195c: r0 = "Initial Amount"
    //     0x99195c: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5bf48] "Initial Amount"
    //     0x991960: ldr             x0, [x0, #0xf48]
    // 0x991964: stur            x2, [fp, #-0x28]
    // 0x991968: StoreField: r2->field_b = r0
    //     0x991968: stur            w0, [x2, #0xb]
    // 0x99196c: ldur            x0, [fp, #-0x20]
    // 0x991970: StoreField: r2->field_13 = r0
    //     0x991970: stur            w0, [x2, #0x13]
    // 0x991974: ldur            x0, [fp, #-8]
    // 0x991978: LoadField: r1 = r0->field_b
    //     0x991978: ldur            w1, [x0, #0xb]
    // 0x99197c: DecompressPointer r1
    //     0x99197c: add             x1, x1, HEAP, lsl #32
    // 0x991980: cmp             w1, NULL
    // 0x991984: b.eq            #0x993714
    // 0x991988: LoadField: r3 = r1->field_b
    //     0x991988: ldur            w3, [x1, #0xb]
    // 0x99198c: DecompressPointer r3
    //     0x99198c: add             x3, x3, HEAP, lsl #32
    // 0x991990: cmp             w3, NULL
    // 0x991994: b.ne            #0x9919a0
    // 0x991998: r1 = Null
    //     0x991998: mov             x1, NULL
    // 0x99199c: b               #0x9919a8
    // 0x9919a0: LoadField: r1 = r3->field_1b
    //     0x9919a0: ldur            w1, [x3, #0x1b]
    // 0x9919a4: DecompressPointer r1
    //     0x9919a4: add             x1, x1, HEAP, lsl #32
    // 0x9919a8: cmp             w1, NULL
    // 0x9919ac: b.ne            #0x9919b8
    // 0x9919b0: r4 = ""
    //     0x9919b0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9919b4: b               #0x9919bc
    // 0x9919b8: mov             x4, x1
    // 0x9919bc: ldur            x3, [fp, #-0x18]
    // 0x9919c0: stur            x4, [fp, #-0x20]
    // 0x9919c4: LoadField: r1 = r3->field_13
    //     0x9919c4: ldur            w1, [x3, #0x13]
    // 0x9919c8: DecompressPointer r1
    //     0x9919c8: add             x1, x1, HEAP, lsl #32
    // 0x9919cc: r0 = of()
    //     0x9919cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9919d0: LoadField: r1 = r0->field_87
    //     0x9919d0: ldur            w1, [x0, #0x87]
    // 0x9919d4: DecompressPointer r1
    //     0x9919d4: add             x1, x1, HEAP, lsl #32
    // 0x9919d8: LoadField: r0 = r1->field_2b
    //     0x9919d8: ldur            w0, [x1, #0x2b]
    // 0x9919dc: DecompressPointer r0
    //     0x9919dc: add             x0, x0, HEAP, lsl #32
    // 0x9919e0: stur            x0, [fp, #-0x30]
    // 0x9919e4: r1 = Instance_Color
    //     0x9919e4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9919e8: d0 = 0.700000
    //     0x9919e8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x9919ec: ldr             d0, [x17, #0xf48]
    // 0x9919f0: r0 = withOpacity()
    //     0x9919f0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9919f4: r16 = 14.000000
    //     0x9919f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x9919f8: ldr             x16, [x16, #0x1d8]
    // 0x9919fc: stp             x0, x16, [SP]
    // 0x991a00: ldur            x1, [fp, #-0x30]
    // 0x991a04: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x991a04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x991a08: ldr             x4, [x4, #0xaa0]
    // 0x991a0c: r0 = copyWith()
    //     0x991a0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x991a10: stur            x0, [fp, #-0x30]
    // 0x991a14: r0 = Text()
    //     0x991a14: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x991a18: mov             x3, x0
    // 0x991a1c: ldur            x0, [fp, #-0x20]
    // 0x991a20: stur            x3, [fp, #-0x38]
    // 0x991a24: StoreField: r3->field_b = r0
    //     0x991a24: stur            w0, [x3, #0xb]
    // 0x991a28: ldur            x0, [fp, #-0x30]
    // 0x991a2c: StoreField: r3->field_13 = r0
    //     0x991a2c: stur            w0, [x3, #0x13]
    // 0x991a30: r1 = Null
    //     0x991a30: mov             x1, NULL
    // 0x991a34: r2 = 6
    //     0x991a34: movz            x2, #0x6
    // 0x991a38: r0 = AllocateArray()
    //     0x991a38: bl              #0x16f7198  ; AllocateArrayStub
    // 0x991a3c: mov             x2, x0
    // 0x991a40: ldur            x0, [fp, #-0x28]
    // 0x991a44: stur            x2, [fp, #-0x20]
    // 0x991a48: StoreField: r2->field_f = r0
    //     0x991a48: stur            w0, [x2, #0xf]
    // 0x991a4c: r16 = Instance_Spacer
    //     0x991a4c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x991a50: ldr             x16, [x16, #0xf0]
    // 0x991a54: StoreField: r2->field_13 = r16
    //     0x991a54: stur            w16, [x2, #0x13]
    // 0x991a58: ldur            x0, [fp, #-0x38]
    // 0x991a5c: ArrayStore: r2[0] = r0  ; List_4
    //     0x991a5c: stur            w0, [x2, #0x17]
    // 0x991a60: r1 = <Widget>
    //     0x991a60: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x991a64: r0 = AllocateGrowableArray()
    //     0x991a64: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x991a68: mov             x1, x0
    // 0x991a6c: ldur            x0, [fp, #-0x20]
    // 0x991a70: stur            x1, [fp, #-0x28]
    // 0x991a74: StoreField: r1->field_f = r0
    //     0x991a74: stur            w0, [x1, #0xf]
    // 0x991a78: r2 = 6
    //     0x991a78: movz            x2, #0x6
    // 0x991a7c: StoreField: r1->field_b = r2
    //     0x991a7c: stur            w2, [x1, #0xb]
    // 0x991a80: r0 = Row()
    //     0x991a80: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x991a84: mov             x2, x0
    // 0x991a88: r0 = Instance_Axis
    //     0x991a88: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x991a8c: stur            x2, [fp, #-0x20]
    // 0x991a90: StoreField: r2->field_f = r0
    //     0x991a90: stur            w0, [x2, #0xf]
    // 0x991a94: r3 = Instance_MainAxisAlignment
    //     0x991a94: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0x991a98: ldr             x3, [x3, #0xd10]
    // 0x991a9c: StoreField: r2->field_13 = r3
    //     0x991a9c: stur            w3, [x2, #0x13]
    // 0x991aa0: r4 = Instance_MainAxisSize
    //     0x991aa0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x991aa4: ldr             x4, [x4, #0xa10]
    // 0x991aa8: ArrayStore: r2[0] = r4  ; List_4
    //     0x991aa8: stur            w4, [x2, #0x17]
    // 0x991aac: r5 = Instance_CrossAxisAlignment
    //     0x991aac: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x991ab0: ldr             x5, [x5, #0x890]
    // 0x991ab4: StoreField: r2->field_1b = r5
    //     0x991ab4: stur            w5, [x2, #0x1b]
    // 0x991ab8: r6 = Instance_VerticalDirection
    //     0x991ab8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x991abc: ldr             x6, [x6, #0xa20]
    // 0x991ac0: StoreField: r2->field_23 = r6
    //     0x991ac0: stur            w6, [x2, #0x23]
    // 0x991ac4: r7 = Instance_Clip
    //     0x991ac4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x991ac8: ldr             x7, [x7, #0x38]
    // 0x991acc: StoreField: r2->field_2b = r7
    //     0x991acc: stur            w7, [x2, #0x2b]
    // 0x991ad0: StoreField: r2->field_2f = rZR
    //     0x991ad0: stur            xzr, [x2, #0x2f]
    // 0x991ad4: ldur            x1, [fp, #-0x28]
    // 0x991ad8: StoreField: r2->field_b = r1
    //     0x991ad8: stur            w1, [x2, #0xb]
    // 0x991adc: ldur            x8, [fp, #-0x18]
    // 0x991ae0: LoadField: r1 = r8->field_13
    //     0x991ae0: ldur            w1, [x8, #0x13]
    // 0x991ae4: DecompressPointer r1
    //     0x991ae4: add             x1, x1, HEAP, lsl #32
    // 0x991ae8: r0 = of()
    //     0x991ae8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x991aec: LoadField: r1 = r0->field_87
    //     0x991aec: ldur            w1, [x0, #0x87]
    // 0x991af0: DecompressPointer r1
    //     0x991af0: add             x1, x1, HEAP, lsl #32
    // 0x991af4: LoadField: r0 = r1->field_2b
    //     0x991af4: ldur            w0, [x1, #0x2b]
    // 0x991af8: DecompressPointer r0
    //     0x991af8: add             x0, x0, HEAP, lsl #32
    // 0x991afc: r16 = 14.000000
    //     0x991afc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x991b00: ldr             x16, [x16, #0x1d8]
    // 0x991b04: r30 = Instance_Color
    //     0x991b04: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x991b08: stp             lr, x16, [SP]
    // 0x991b0c: mov             x1, x0
    // 0x991b10: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x991b10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x991b14: ldr             x4, [x4, #0xaa0]
    // 0x991b18: r0 = copyWith()
    //     0x991b18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x991b1c: stur            x0, [fp, #-0x28]
    // 0x991b20: r0 = Text()
    //     0x991b20: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x991b24: mov             x3, x0
    // 0x991b28: r0 = "Cancellation Charges "
    //     0x991b28: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5bf50] "Cancellation Charges "
    //     0x991b2c: ldr             x0, [x0, #0xf50]
    // 0x991b30: stur            x3, [fp, #-0x30]
    // 0x991b34: StoreField: r3->field_b = r0
    //     0x991b34: stur            w0, [x3, #0xb]
    // 0x991b38: ldur            x0, [fp, #-0x28]
    // 0x991b3c: StoreField: r3->field_13 = r0
    //     0x991b3c: stur            w0, [x3, #0x13]
    // 0x991b40: r1 = Null
    //     0x991b40: mov             x1, NULL
    // 0x991b44: r2 = 4
    //     0x991b44: movz            x2, #0x4
    // 0x991b48: r0 = AllocateArray()
    //     0x991b48: bl              #0x16f7198  ; AllocateArrayStub
    // 0x991b4c: r16 = "-"
    //     0x991b4c: ldr             x16, [PP, #0x31a8]  ; [pp+0x31a8] "-"
    // 0x991b50: StoreField: r0->field_f = r16
    //     0x991b50: stur            w16, [x0, #0xf]
    // 0x991b54: ldur            x1, [fp, #-8]
    // 0x991b58: LoadField: r2 = r1->field_b
    //     0x991b58: ldur            w2, [x1, #0xb]
    // 0x991b5c: DecompressPointer r2
    //     0x991b5c: add             x2, x2, HEAP, lsl #32
    // 0x991b60: cmp             w2, NULL
    // 0x991b64: b.eq            #0x993718
    // 0x991b68: LoadField: r3 = r2->field_b
    //     0x991b68: ldur            w3, [x2, #0xb]
    // 0x991b6c: DecompressPointer r3
    //     0x991b6c: add             x3, x3, HEAP, lsl #32
    // 0x991b70: cmp             w3, NULL
    // 0x991b74: b.ne            #0x991b80
    // 0x991b78: r2 = Null
    //     0x991b78: mov             x2, NULL
    // 0x991b7c: b               #0x991b88
    // 0x991b80: LoadField: r2 = r3->field_23
    //     0x991b80: ldur            w2, [x3, #0x23]
    // 0x991b84: DecompressPointer r2
    //     0x991b84: add             x2, x2, HEAP, lsl #32
    // 0x991b88: cmp             w2, NULL
    // 0x991b8c: b.ne            #0x991b98
    // 0x991b90: r4 = ""
    //     0x991b90: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x991b94: b               #0x991b9c
    // 0x991b98: mov             x4, x2
    // 0x991b9c: ldur            x3, [fp, #-0x18]
    // 0x991ba0: ldur            x2, [fp, #-0x30]
    // 0x991ba4: StoreField: r0->field_13 = r4
    //     0x991ba4: stur            w4, [x0, #0x13]
    // 0x991ba8: str             x0, [SP]
    // 0x991bac: r0 = _interpolate()
    //     0x991bac: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x991bb0: ldur            x2, [fp, #-0x18]
    // 0x991bb4: stur            x0, [fp, #-0x28]
    // 0x991bb8: LoadField: r1 = r2->field_13
    //     0x991bb8: ldur            w1, [x2, #0x13]
    // 0x991bbc: DecompressPointer r1
    //     0x991bbc: add             x1, x1, HEAP, lsl #32
    // 0x991bc0: r0 = of()
    //     0x991bc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x991bc4: LoadField: r1 = r0->field_87
    //     0x991bc4: ldur            w1, [x0, #0x87]
    // 0x991bc8: DecompressPointer r1
    //     0x991bc8: add             x1, x1, HEAP, lsl #32
    // 0x991bcc: LoadField: r0 = r1->field_2b
    //     0x991bcc: ldur            w0, [x1, #0x2b]
    // 0x991bd0: DecompressPointer r0
    //     0x991bd0: add             x0, x0, HEAP, lsl #32
    // 0x991bd4: stur            x0, [fp, #-0x38]
    // 0x991bd8: r1 = Instance_Color
    //     0x991bd8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x991bdc: d0 = 0.700000
    //     0x991bdc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x991be0: ldr             d0, [x17, #0xf48]
    // 0x991be4: r0 = withOpacity()
    //     0x991be4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x991be8: r16 = 14.000000
    //     0x991be8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x991bec: ldr             x16, [x16, #0x1d8]
    // 0x991bf0: stp             x0, x16, [SP]
    // 0x991bf4: ldur            x1, [fp, #-0x38]
    // 0x991bf8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x991bf8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x991bfc: ldr             x4, [x4, #0xaa0]
    // 0x991c00: r0 = copyWith()
    //     0x991c00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x991c04: stur            x0, [fp, #-0x38]
    // 0x991c08: r0 = Text()
    //     0x991c08: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x991c0c: mov             x3, x0
    // 0x991c10: ldur            x0, [fp, #-0x28]
    // 0x991c14: stur            x3, [fp, #-0x40]
    // 0x991c18: StoreField: r3->field_b = r0
    //     0x991c18: stur            w0, [x3, #0xb]
    // 0x991c1c: ldur            x0, [fp, #-0x38]
    // 0x991c20: StoreField: r3->field_13 = r0
    //     0x991c20: stur            w0, [x3, #0x13]
    // 0x991c24: r1 = Null
    //     0x991c24: mov             x1, NULL
    // 0x991c28: r2 = 6
    //     0x991c28: movz            x2, #0x6
    // 0x991c2c: r0 = AllocateArray()
    //     0x991c2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x991c30: mov             x2, x0
    // 0x991c34: ldur            x0, [fp, #-0x30]
    // 0x991c38: stur            x2, [fp, #-0x28]
    // 0x991c3c: StoreField: r2->field_f = r0
    //     0x991c3c: stur            w0, [x2, #0xf]
    // 0x991c40: r16 = Instance_Spacer
    //     0x991c40: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x991c44: ldr             x16, [x16, #0xf0]
    // 0x991c48: StoreField: r2->field_13 = r16
    //     0x991c48: stur            w16, [x2, #0x13]
    // 0x991c4c: ldur            x0, [fp, #-0x40]
    // 0x991c50: ArrayStore: r2[0] = r0  ; List_4
    //     0x991c50: stur            w0, [x2, #0x17]
    // 0x991c54: r1 = <Widget>
    //     0x991c54: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x991c58: r0 = AllocateGrowableArray()
    //     0x991c58: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x991c5c: mov             x1, x0
    // 0x991c60: ldur            x0, [fp, #-0x28]
    // 0x991c64: stur            x1, [fp, #-0x30]
    // 0x991c68: StoreField: r1->field_f = r0
    //     0x991c68: stur            w0, [x1, #0xf]
    // 0x991c6c: r2 = 6
    //     0x991c6c: movz            x2, #0x6
    // 0x991c70: StoreField: r1->field_b = r2
    //     0x991c70: stur            w2, [x1, #0xb]
    // 0x991c74: r0 = Row()
    //     0x991c74: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x991c78: mov             x2, x0
    // 0x991c7c: r0 = Instance_Axis
    //     0x991c7c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x991c80: stur            x2, [fp, #-0x28]
    // 0x991c84: StoreField: r2->field_f = r0
    //     0x991c84: stur            w0, [x2, #0xf]
    // 0x991c88: r3 = Instance_MainAxisAlignment
    //     0x991c88: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0x991c8c: ldr             x3, [x3, #0xd10]
    // 0x991c90: StoreField: r2->field_13 = r3
    //     0x991c90: stur            w3, [x2, #0x13]
    // 0x991c94: r4 = Instance_MainAxisSize
    //     0x991c94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x991c98: ldr             x4, [x4, #0xa10]
    // 0x991c9c: ArrayStore: r2[0] = r4  ; List_4
    //     0x991c9c: stur            w4, [x2, #0x17]
    // 0x991ca0: r5 = Instance_CrossAxisAlignment
    //     0x991ca0: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x991ca4: ldr             x5, [x5, #0x890]
    // 0x991ca8: StoreField: r2->field_1b = r5
    //     0x991ca8: stur            w5, [x2, #0x1b]
    // 0x991cac: r6 = Instance_VerticalDirection
    //     0x991cac: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x991cb0: ldr             x6, [x6, #0xa20]
    // 0x991cb4: StoreField: r2->field_23 = r6
    //     0x991cb4: stur            w6, [x2, #0x23]
    // 0x991cb8: r7 = Instance_Clip
    //     0x991cb8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x991cbc: ldr             x7, [x7, #0x38]
    // 0x991cc0: StoreField: r2->field_2b = r7
    //     0x991cc0: stur            w7, [x2, #0x2b]
    // 0x991cc4: StoreField: r2->field_2f = rZR
    //     0x991cc4: stur            xzr, [x2, #0x2f]
    // 0x991cc8: ldur            x1, [fp, #-0x30]
    // 0x991ccc: StoreField: r2->field_b = r1
    //     0x991ccc: stur            w1, [x2, #0xb]
    // 0x991cd0: r1 = Instance_Color
    //     0x991cd0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x991cd4: d0 = 0.100000
    //     0x991cd4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x991cd8: r0 = withOpacity()
    //     0x991cd8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x991cdc: stur            x0, [fp, #-0x30]
    // 0x991ce0: r0 = Divider()
    //     0x991ce0: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x991ce4: mov             x2, x0
    // 0x991ce8: ldur            x0, [fp, #-0x30]
    // 0x991cec: stur            x2, [fp, #-0x38]
    // 0x991cf0: StoreField: r2->field_1f = r0
    //     0x991cf0: stur            w0, [x2, #0x1f]
    // 0x991cf4: ldur            x0, [fp, #-0x18]
    // 0x991cf8: LoadField: r1 = r0->field_13
    //     0x991cf8: ldur            w1, [x0, #0x13]
    // 0x991cfc: DecompressPointer r1
    //     0x991cfc: add             x1, x1, HEAP, lsl #32
    // 0x991d00: r0 = of()
    //     0x991d00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x991d04: LoadField: r1 = r0->field_87
    //     0x991d04: ldur            w1, [x0, #0x87]
    // 0x991d08: DecompressPointer r1
    //     0x991d08: add             x1, x1, HEAP, lsl #32
    // 0x991d0c: LoadField: r0 = r1->field_7
    //     0x991d0c: ldur            w0, [x1, #7]
    // 0x991d10: DecompressPointer r0
    //     0x991d10: add             x0, x0, HEAP, lsl #32
    // 0x991d14: r16 = Instance_Color
    //     0x991d14: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x991d18: r30 = 14.000000
    //     0x991d18: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x991d1c: ldr             lr, [lr, #0x1d8]
    // 0x991d20: stp             lr, x16, [SP]
    // 0x991d24: mov             x1, x0
    // 0x991d28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x991d28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x991d2c: ldr             x4, [x4, #0x9b8]
    // 0x991d30: r0 = copyWith()
    //     0x991d30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x991d34: stur            x0, [fp, #-0x30]
    // 0x991d38: r0 = Text()
    //     0x991d38: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x991d3c: mov             x2, x0
    // 0x991d40: r0 = "Refund Amount"
    //     0x991d40: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5bf58] "Refund Amount"
    //     0x991d44: ldr             x0, [x0, #0xf58]
    // 0x991d48: stur            x2, [fp, #-0x40]
    // 0x991d4c: StoreField: r2->field_b = r0
    //     0x991d4c: stur            w0, [x2, #0xb]
    // 0x991d50: ldur            x0, [fp, #-0x30]
    // 0x991d54: StoreField: r2->field_13 = r0
    //     0x991d54: stur            w0, [x2, #0x13]
    // 0x991d58: ldur            x0, [fp, #-8]
    // 0x991d5c: LoadField: r1 = r0->field_b
    //     0x991d5c: ldur            w1, [x0, #0xb]
    // 0x991d60: DecompressPointer r1
    //     0x991d60: add             x1, x1, HEAP, lsl #32
    // 0x991d64: cmp             w1, NULL
    // 0x991d68: b.eq            #0x99371c
    // 0x991d6c: LoadField: r3 = r1->field_b
    //     0x991d6c: ldur            w3, [x1, #0xb]
    // 0x991d70: DecompressPointer r3
    //     0x991d70: add             x3, x3, HEAP, lsl #32
    // 0x991d74: cmp             w3, NULL
    // 0x991d78: b.ne            #0x991d84
    // 0x991d7c: r1 = Null
    //     0x991d7c: mov             x1, NULL
    // 0x991d80: b               #0x991d8c
    // 0x991d84: LoadField: r1 = r3->field_1f
    //     0x991d84: ldur            w1, [x3, #0x1f]
    // 0x991d88: DecompressPointer r1
    //     0x991d88: add             x1, x1, HEAP, lsl #32
    // 0x991d8c: cmp             w1, NULL
    // 0x991d90: b.ne            #0x991d9c
    // 0x991d94: r8 = ""
    //     0x991d94: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x991d98: b               #0x991da0
    // 0x991d9c: mov             x8, x1
    // 0x991da0: ldur            x4, [fp, #-0x18]
    // 0x991da4: ldur            x6, [fp, #-0x20]
    // 0x991da8: ldur            x5, [fp, #-0x28]
    // 0x991dac: ldur            x3, [fp, #-0x38]
    // 0x991db0: ldur            x7, [fp, #-0x10]
    // 0x991db4: stur            x8, [fp, #-0x30]
    // 0x991db8: LoadField: r1 = r4->field_13
    //     0x991db8: ldur            w1, [x4, #0x13]
    // 0x991dbc: DecompressPointer r1
    //     0x991dbc: add             x1, x1, HEAP, lsl #32
    // 0x991dc0: r0 = of()
    //     0x991dc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x991dc4: LoadField: r1 = r0->field_87
    //     0x991dc4: ldur            w1, [x0, #0x87]
    // 0x991dc8: DecompressPointer r1
    //     0x991dc8: add             x1, x1, HEAP, lsl #32
    // 0x991dcc: LoadField: r0 = r1->field_7
    //     0x991dcc: ldur            w0, [x1, #7]
    // 0x991dd0: DecompressPointer r0
    //     0x991dd0: add             x0, x0, HEAP, lsl #32
    // 0x991dd4: r16 = Instance_Color
    //     0x991dd4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x991dd8: r30 = 14.000000
    //     0x991dd8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x991ddc: ldr             lr, [lr, #0x1d8]
    // 0x991de0: stp             lr, x16, [SP]
    // 0x991de4: mov             x1, x0
    // 0x991de8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x991de8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x991dec: ldr             x4, [x4, #0x9b8]
    // 0x991df0: r0 = copyWith()
    //     0x991df0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x991df4: stur            x0, [fp, #-0x48]
    // 0x991df8: r0 = Text()
    //     0x991df8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x991dfc: mov             x3, x0
    // 0x991e00: ldur            x0, [fp, #-0x30]
    // 0x991e04: stur            x3, [fp, #-0x50]
    // 0x991e08: StoreField: r3->field_b = r0
    //     0x991e08: stur            w0, [x3, #0xb]
    // 0x991e0c: ldur            x0, [fp, #-0x48]
    // 0x991e10: StoreField: r3->field_13 = r0
    //     0x991e10: stur            w0, [x3, #0x13]
    // 0x991e14: r1 = Null
    //     0x991e14: mov             x1, NULL
    // 0x991e18: r2 = 6
    //     0x991e18: movz            x2, #0x6
    // 0x991e1c: r0 = AllocateArray()
    //     0x991e1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x991e20: mov             x2, x0
    // 0x991e24: ldur            x0, [fp, #-0x40]
    // 0x991e28: stur            x2, [fp, #-0x30]
    // 0x991e2c: StoreField: r2->field_f = r0
    //     0x991e2c: stur            w0, [x2, #0xf]
    // 0x991e30: r16 = Instance_Spacer
    //     0x991e30: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x991e34: ldr             x16, [x16, #0xf0]
    // 0x991e38: StoreField: r2->field_13 = r16
    //     0x991e38: stur            w16, [x2, #0x13]
    // 0x991e3c: ldur            x0, [fp, #-0x50]
    // 0x991e40: ArrayStore: r2[0] = r0  ; List_4
    //     0x991e40: stur            w0, [x2, #0x17]
    // 0x991e44: r1 = <Widget>
    //     0x991e44: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x991e48: r0 = AllocateGrowableArray()
    //     0x991e48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x991e4c: mov             x1, x0
    // 0x991e50: ldur            x0, [fp, #-0x30]
    // 0x991e54: stur            x1, [fp, #-0x40]
    // 0x991e58: StoreField: r1->field_f = r0
    //     0x991e58: stur            w0, [x1, #0xf]
    // 0x991e5c: r0 = 6
    //     0x991e5c: movz            x0, #0x6
    // 0x991e60: StoreField: r1->field_b = r0
    //     0x991e60: stur            w0, [x1, #0xb]
    // 0x991e64: r0 = Row()
    //     0x991e64: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x991e68: mov             x2, x0
    // 0x991e6c: r0 = Instance_Axis
    //     0x991e6c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x991e70: stur            x2, [fp, #-0x30]
    // 0x991e74: StoreField: r2->field_f = r0
    //     0x991e74: stur            w0, [x2, #0xf]
    // 0x991e78: r1 = Instance_MainAxisAlignment
    //     0x991e78: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0x991e7c: ldr             x1, [x1, #0xd10]
    // 0x991e80: StoreField: r2->field_13 = r1
    //     0x991e80: stur            w1, [x2, #0x13]
    // 0x991e84: r3 = Instance_MainAxisSize
    //     0x991e84: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x991e88: ldr             x3, [x3, #0xa10]
    // 0x991e8c: ArrayStore: r2[0] = r3  ; List_4
    //     0x991e8c: stur            w3, [x2, #0x17]
    // 0x991e90: r4 = Instance_CrossAxisAlignment
    //     0x991e90: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x991e94: ldr             x4, [x4, #0x890]
    // 0x991e98: StoreField: r2->field_1b = r4
    //     0x991e98: stur            w4, [x2, #0x1b]
    // 0x991e9c: r5 = Instance_VerticalDirection
    //     0x991e9c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x991ea0: ldr             x5, [x5, #0xa20]
    // 0x991ea4: StoreField: r2->field_23 = r5
    //     0x991ea4: stur            w5, [x2, #0x23]
    // 0x991ea8: r6 = Instance_Clip
    //     0x991ea8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x991eac: ldr             x6, [x6, #0x38]
    // 0x991eb0: StoreField: r2->field_2b = r6
    //     0x991eb0: stur            w6, [x2, #0x2b]
    // 0x991eb4: StoreField: r2->field_2f = rZR
    //     0x991eb4: stur            xzr, [x2, #0x2f]
    // 0x991eb8: ldur            x1, [fp, #-0x40]
    // 0x991ebc: StoreField: r2->field_b = r1
    //     0x991ebc: stur            w1, [x2, #0xb]
    // 0x991ec0: r1 = Instance_Color
    //     0x991ec0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x991ec4: d0 = 0.100000
    //     0x991ec4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x991ec8: r0 = withOpacity()
    //     0x991ec8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x991ecc: stur            x0, [fp, #-0x40]
    // 0x991ed0: r0 = Divider()
    //     0x991ed0: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x991ed4: mov             x2, x0
    // 0x991ed8: ldur            x0, [fp, #-0x40]
    // 0x991edc: stur            x2, [fp, #-0x48]
    // 0x991ee0: StoreField: r2->field_1f = r0
    //     0x991ee0: stur            w0, [x2, #0x1f]
    // 0x991ee4: ldur            x0, [fp, #-0x18]
    // 0x991ee8: LoadField: r1 = r0->field_13
    //     0x991ee8: ldur            w1, [x0, #0x13]
    // 0x991eec: DecompressPointer r1
    //     0x991eec: add             x1, x1, HEAP, lsl #32
    // 0x991ef0: r0 = of()
    //     0x991ef0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x991ef4: LoadField: r1 = r0->field_87
    //     0x991ef4: ldur            w1, [x0, #0x87]
    // 0x991ef8: DecompressPointer r1
    //     0x991ef8: add             x1, x1, HEAP, lsl #32
    // 0x991efc: LoadField: r0 = r1->field_2b
    //     0x991efc: ldur            w0, [x1, #0x2b]
    // 0x991f00: DecompressPointer r0
    //     0x991f00: add             x0, x0, HEAP, lsl #32
    // 0x991f04: stur            x0, [fp, #-0x40]
    // 0x991f08: r1 = Instance_Color
    //     0x991f08: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x991f0c: d0 = 0.400000
    //     0x991f0c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x991f10: r0 = withOpacity()
    //     0x991f10: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x991f14: r16 = 14.000000
    //     0x991f14: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x991f18: ldr             x16, [x16, #0x1d8]
    // 0x991f1c: stp             x16, x0, [SP]
    // 0x991f20: ldur            x1, [fp, #-0x40]
    // 0x991f24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x991f24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x991f28: ldr             x4, [x4, #0x9b8]
    // 0x991f2c: r0 = copyWith()
    //     0x991f2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x991f30: stur            x0, [fp, #-0x40]
    // 0x991f34: r0 = Text()
    //     0x991f34: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x991f38: mov             x3, x0
    // 0x991f3c: r0 = "Amount will be auto-reversed to the same account within 5-6 days."
    //     0x991f3c: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5bf60] "Amount will be auto-reversed to the same account within 5-6 days."
    //     0x991f40: ldr             x0, [x0, #0xf60]
    // 0x991f44: stur            x3, [fp, #-0x50]
    // 0x991f48: StoreField: r3->field_b = r0
    //     0x991f48: stur            w0, [x3, #0xb]
    // 0x991f4c: ldur            x0, [fp, #-0x40]
    // 0x991f50: StoreField: r3->field_13 = r0
    //     0x991f50: stur            w0, [x3, #0x13]
    // 0x991f54: r1 = Null
    //     0x991f54: mov             x1, NULL
    // 0x991f58: r2 = 14
    //     0x991f58: movz            x2, #0xe
    // 0x991f5c: r0 = AllocateArray()
    //     0x991f5c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x991f60: mov             x2, x0
    // 0x991f64: ldur            x0, [fp, #-0x20]
    // 0x991f68: stur            x2, [fp, #-0x40]
    // 0x991f6c: StoreField: r2->field_f = r0
    //     0x991f6c: stur            w0, [x2, #0xf]
    // 0x991f70: ldur            x0, [fp, #-0x28]
    // 0x991f74: StoreField: r2->field_13 = r0
    //     0x991f74: stur            w0, [x2, #0x13]
    // 0x991f78: ldur            x0, [fp, #-0x38]
    // 0x991f7c: ArrayStore: r2[0] = r0  ; List_4
    //     0x991f7c: stur            w0, [x2, #0x17]
    // 0x991f80: ldur            x0, [fp, #-0x30]
    // 0x991f84: StoreField: r2->field_1b = r0
    //     0x991f84: stur            w0, [x2, #0x1b]
    // 0x991f88: ldur            x0, [fp, #-0x48]
    // 0x991f8c: StoreField: r2->field_1f = r0
    //     0x991f8c: stur            w0, [x2, #0x1f]
    // 0x991f90: ldur            x0, [fp, #-0x50]
    // 0x991f94: StoreField: r2->field_23 = r0
    //     0x991f94: stur            w0, [x2, #0x23]
    // 0x991f98: r16 = Instance_SizedBox
    //     0x991f98: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x991f9c: ldr             x16, [x16, #0x8f0]
    // 0x991fa0: StoreField: r2->field_27 = r16
    //     0x991fa0: stur            w16, [x2, #0x27]
    // 0x991fa4: r1 = <Widget>
    //     0x991fa4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x991fa8: r0 = AllocateGrowableArray()
    //     0x991fa8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x991fac: mov             x1, x0
    // 0x991fb0: ldur            x0, [fp, #-0x40]
    // 0x991fb4: stur            x1, [fp, #-0x20]
    // 0x991fb8: StoreField: r1->field_f = r0
    //     0x991fb8: stur            w0, [x1, #0xf]
    // 0x991fbc: r0 = 14
    //     0x991fbc: movz            x0, #0xe
    // 0x991fc0: StoreField: r1->field_b = r0
    //     0x991fc0: stur            w0, [x1, #0xb]
    // 0x991fc4: r0 = Column()
    //     0x991fc4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x991fc8: mov             x2, x0
    // 0x991fcc: r0 = Instance_Axis
    //     0x991fcc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x991fd0: stur            x2, [fp, #-0x28]
    // 0x991fd4: StoreField: r2->field_f = r0
    //     0x991fd4: stur            w0, [x2, #0xf]
    // 0x991fd8: r3 = Instance_MainAxisAlignment
    //     0x991fd8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x991fdc: ldr             x3, [x3, #0xa08]
    // 0x991fe0: StoreField: r2->field_13 = r3
    //     0x991fe0: stur            w3, [x2, #0x13]
    // 0x991fe4: r4 = Instance_MainAxisSize
    //     0x991fe4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x991fe8: ldr             x4, [x4, #0xa10]
    // 0x991fec: ArrayStore: r2[0] = r4  ; List_4
    //     0x991fec: stur            w4, [x2, #0x17]
    // 0x991ff0: r5 = Instance_CrossAxisAlignment
    //     0x991ff0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x991ff4: ldr             x5, [x5, #0xa18]
    // 0x991ff8: StoreField: r2->field_1b = r5
    //     0x991ff8: stur            w5, [x2, #0x1b]
    // 0x991ffc: r6 = Instance_VerticalDirection
    //     0x991ffc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x992000: ldr             x6, [x6, #0xa20]
    // 0x992004: StoreField: r2->field_23 = r6
    //     0x992004: stur            w6, [x2, #0x23]
    // 0x992008: r7 = Instance_Clip
    //     0x992008: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x99200c: ldr             x7, [x7, #0x38]
    // 0x992010: StoreField: r2->field_2b = r7
    //     0x992010: stur            w7, [x2, #0x2b]
    // 0x992014: StoreField: r2->field_2f = rZR
    //     0x992014: stur            xzr, [x2, #0x2f]
    // 0x992018: ldur            x1, [fp, #-0x20]
    // 0x99201c: StoreField: r2->field_b = r1
    //     0x99201c: stur            w1, [x2, #0xb]
    // 0x992020: ldur            x8, [fp, #-0x10]
    // 0x992024: LoadField: r1 = r8->field_b
    //     0x992024: ldur            w1, [x8, #0xb]
    // 0x992028: LoadField: r9 = r8->field_f
    //     0x992028: ldur            w9, [x8, #0xf]
    // 0x99202c: DecompressPointer r9
    //     0x99202c: add             x9, x9, HEAP, lsl #32
    // 0x992030: LoadField: r10 = r9->field_b
    //     0x992030: ldur            w10, [x9, #0xb]
    // 0x992034: r9 = LoadInt32Instr(r1)
    //     0x992034: sbfx            x9, x1, #1, #0x1f
    // 0x992038: stur            x9, [fp, #-0x58]
    // 0x99203c: r1 = LoadInt32Instr(r10)
    //     0x99203c: sbfx            x1, x10, #1, #0x1f
    // 0x992040: cmp             x9, x1
    // 0x992044: b.ne            #0x992050
    // 0x992048: mov             x1, x8
    // 0x99204c: r0 = _growToNextCapacity()
    //     0x99204c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x992050: ldur            x2, [fp, #-0x10]
    // 0x992054: ldur            x3, [fp, #-0x58]
    // 0x992058: add             x0, x3, #1
    // 0x99205c: lsl             x1, x0, #1
    // 0x992060: StoreField: r2->field_b = r1
    //     0x992060: stur            w1, [x2, #0xb]
    // 0x992064: LoadField: r1 = r2->field_f
    //     0x992064: ldur            w1, [x2, #0xf]
    // 0x992068: DecompressPointer r1
    //     0x992068: add             x1, x1, HEAP, lsl #32
    // 0x99206c: ldur            x0, [fp, #-0x28]
    // 0x992070: ArrayStore: r1[r3] = r0  ; List_4
    //     0x992070: add             x25, x1, x3, lsl #2
    //     0x992074: add             x25, x25, #0xf
    //     0x992078: str             w0, [x25]
    //     0x99207c: tbz             w0, #0, #0x992098
    //     0x992080: ldurb           w16, [x1, #-1]
    //     0x992084: ldurb           w17, [x0, #-1]
    //     0x992088: and             x16, x17, x16, lsr #2
    //     0x99208c: tst             x16, HEAP, lsr #32
    //     0x992090: b.eq            #0x992098
    //     0x992094: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x992098: b               #0x99213c
    // 0x99209c: ldur            x2, [fp, #-0x10]
    // 0x9920a0: b               #0x9920b0
    // 0x9920a4: mov             x2, x1
    // 0x9920a8: b               #0x9920b0
    // 0x9920ac: mov             x2, x1
    // 0x9920b0: r0 = Container()
    //     0x9920b0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9920b4: mov             x1, x0
    // 0x9920b8: stur            x0, [fp, #-0x20]
    // 0x9920bc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9920bc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9920c0: r0 = Container()
    //     0x9920c0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9920c4: ldur            x0, [fp, #-0x10]
    // 0x9920c8: LoadField: r1 = r0->field_b
    //     0x9920c8: ldur            w1, [x0, #0xb]
    // 0x9920cc: LoadField: r2 = r0->field_f
    //     0x9920cc: ldur            w2, [x0, #0xf]
    // 0x9920d0: DecompressPointer r2
    //     0x9920d0: add             x2, x2, HEAP, lsl #32
    // 0x9920d4: LoadField: r3 = r2->field_b
    //     0x9920d4: ldur            w3, [x2, #0xb]
    // 0x9920d8: r2 = LoadInt32Instr(r1)
    //     0x9920d8: sbfx            x2, x1, #1, #0x1f
    // 0x9920dc: stur            x2, [fp, #-0x58]
    // 0x9920e0: r1 = LoadInt32Instr(r3)
    //     0x9920e0: sbfx            x1, x3, #1, #0x1f
    // 0x9920e4: cmp             x2, x1
    // 0x9920e8: b.ne            #0x9920f4
    // 0x9920ec: mov             x1, x0
    // 0x9920f0: r0 = _growToNextCapacity()
    //     0x9920f0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9920f4: ldur            x2, [fp, #-0x10]
    // 0x9920f8: ldur            x3, [fp, #-0x58]
    // 0x9920fc: add             x0, x3, #1
    // 0x992100: lsl             x1, x0, #1
    // 0x992104: StoreField: r2->field_b = r1
    //     0x992104: stur            w1, [x2, #0xb]
    // 0x992108: LoadField: r1 = r2->field_f
    //     0x992108: ldur            w1, [x2, #0xf]
    // 0x99210c: DecompressPointer r1
    //     0x99210c: add             x1, x1, HEAP, lsl #32
    // 0x992110: ldur            x0, [fp, #-0x20]
    // 0x992114: ArrayStore: r1[r3] = r0  ; List_4
    //     0x992114: add             x25, x1, x3, lsl #2
    //     0x992118: add             x25, x25, #0xf
    //     0x99211c: str             w0, [x25]
    //     0x992120: tbz             w0, #0, #0x99213c
    //     0x992124: ldurb           w16, [x1, #-1]
    //     0x992128: ldurb           w17, [x0, #-1]
    //     0x99212c: and             x16, x17, x16, lsr #2
    //     0x992130: tst             x16, HEAP, lsr #32
    //     0x992134: b.eq            #0x99213c
    //     0x992138: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x99213c: ldur            x0, [fp, #-8]
    // 0x992140: LoadField: r1 = r0->field_b
    //     0x992140: ldur            w1, [x0, #0xb]
    // 0x992144: DecompressPointer r1
    //     0x992144: add             x1, x1, HEAP, lsl #32
    // 0x992148: cmp             w1, NULL
    // 0x99214c: b.eq            #0x993720
    // 0x992150: LoadField: r3 = r1->field_b
    //     0x992150: ldur            w3, [x1, #0xb]
    // 0x992154: DecompressPointer r3
    //     0x992154: add             x3, x3, HEAP, lsl #32
    // 0x992158: cmp             w3, NULL
    // 0x99215c: b.eq            #0x9929f4
    // 0x992160: LoadField: r1 = r3->field_13
    //     0x992160: ldur            w1, [x3, #0x13]
    // 0x992164: DecompressPointer r1
    //     0x992164: add             x1, x1, HEAP, lsl #32
    // 0x992168: cmp             w1, NULL
    // 0x99216c: b.eq            #0x992174
    // 0x992170: tbnz            w1, #4, #0x9929f4
    // 0x992174: ldur            x3, [fp, #-0x18]
    // 0x992178: LoadField: r1 = r3->field_13
    //     0x992178: ldur            w1, [x3, #0x13]
    // 0x99217c: DecompressPointer r1
    //     0x99217c: add             x1, x1, HEAP, lsl #32
    // 0x992180: r0 = of()
    //     0x992180: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x992184: LoadField: r1 = r0->field_87
    //     0x992184: ldur            w1, [x0, #0x87]
    // 0x992188: DecompressPointer r1
    //     0x992188: add             x1, x1, HEAP, lsl #32
    // 0x99218c: LoadField: r0 = r1->field_7
    //     0x99218c: ldur            w0, [x1, #7]
    // 0x992190: DecompressPointer r0
    //     0x992190: add             x0, x0, HEAP, lsl #32
    // 0x992194: r16 = Instance_Color
    //     0x992194: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x992198: r30 = 16.000000
    //     0x992198: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x99219c: ldr             lr, [lr, #0x188]
    // 0x9921a0: stp             lr, x16, [SP]
    // 0x9921a4: mov             x1, x0
    // 0x9921a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9921a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9921ac: ldr             x4, [x4, #0x9b8]
    // 0x9921b0: r0 = copyWith()
    //     0x9921b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9921b4: stur            x0, [fp, #-0x20]
    // 0x9921b8: r0 = Text()
    //     0x9921b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9921bc: mov             x2, x0
    // 0x9921c0: r0 = "Reason for cancellation"
    //     0x9921c0: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5bf68] "Reason for cancellation"
    //     0x9921c4: ldr             x0, [x0, #0xf68]
    // 0x9921c8: stur            x2, [fp, #-0x28]
    // 0x9921cc: StoreField: r2->field_b = r0
    //     0x9921cc: stur            w0, [x2, #0xb]
    // 0x9921d0: ldur            x0, [fp, #-0x20]
    // 0x9921d4: StoreField: r2->field_13 = r0
    //     0x9921d4: stur            w0, [x2, #0x13]
    // 0x9921d8: ldur            x0, [fp, #-0x18]
    // 0x9921dc: LoadField: r1 = r0->field_13
    //     0x9921dc: ldur            w1, [x0, #0x13]
    // 0x9921e0: DecompressPointer r1
    //     0x9921e0: add             x1, x1, HEAP, lsl #32
    // 0x9921e4: r0 = of()
    //     0x9921e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9921e8: LoadField: r1 = r0->field_87
    //     0x9921e8: ldur            w1, [x0, #0x87]
    // 0x9921ec: DecompressPointer r1
    //     0x9921ec: add             x1, x1, HEAP, lsl #32
    // 0x9921f0: LoadField: r0 = r1->field_2b
    //     0x9921f0: ldur            w0, [x1, #0x2b]
    // 0x9921f4: DecompressPointer r0
    //     0x9921f4: add             x0, x0, HEAP, lsl #32
    // 0x9921f8: stur            x0, [fp, #-0x20]
    // 0x9921fc: r1 = Instance_Color
    //     0x9921fc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x992200: d0 = 0.400000
    //     0x992200: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x992204: r0 = withOpacity()
    //     0x992204: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x992208: r16 = 14.000000
    //     0x992208: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x99220c: ldr             x16, [x16, #0x1d8]
    // 0x992210: stp             x16, x0, [SP]
    // 0x992214: ldur            x1, [fp, #-0x20]
    // 0x992218: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x992218: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x99221c: ldr             x4, [x4, #0x9b8]
    // 0x992220: r0 = copyWith()
    //     0x992220: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x992224: stur            x0, [fp, #-0x20]
    // 0x992228: r0 = Text()
    //     0x992228: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x99222c: mov             x2, x0
    // 0x992230: r0 = "Please select the most suitable reason to cancel your order"
    //     0x992230: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5bf70] "Please select the most suitable reason to cancel your order"
    //     0x992234: ldr             x0, [x0, #0xf70]
    // 0x992238: stur            x2, [fp, #-0x30]
    // 0x99223c: StoreField: r2->field_b = r0
    //     0x99223c: stur            w0, [x2, #0xb]
    // 0x992240: ldur            x0, [fp, #-0x20]
    // 0x992244: StoreField: r2->field_13 = r0
    //     0x992244: stur            w0, [x2, #0x13]
    // 0x992248: ldur            x3, [fp, #-8]
    // 0x99224c: LoadField: r0 = r3->field_b
    //     0x99224c: ldur            w0, [x3, #0xb]
    // 0x992250: DecompressPointer r0
    //     0x992250: add             x0, x0, HEAP, lsl #32
    // 0x992254: cmp             w0, NULL
    // 0x992258: b.eq            #0x993724
    // 0x99225c: LoadField: r1 = r0->field_b
    //     0x99225c: ldur            w1, [x0, #0xb]
    // 0x992260: DecompressPointer r1
    //     0x992260: add             x1, x1, HEAP, lsl #32
    // 0x992264: cmp             w1, NULL
    // 0x992268: b.ne            #0x992274
    // 0x99226c: r0 = Null
    //     0x99226c: mov             x0, NULL
    // 0x992270: b               #0x9922e8
    // 0x992274: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x992274: ldur            w4, [x1, #0x17]
    // 0x992278: DecompressPointer r4
    //     0x992278: add             x4, x4, HEAP, lsl #32
    // 0x99227c: cmp             w4, NULL
    // 0x992280: b.ne            #0x99228c
    // 0x992284: r0 = Null
    //     0x992284: mov             x0, NULL
    // 0x992288: b               #0x9922e8
    // 0x99228c: LoadField: r0 = r3->field_1b
    //     0x99228c: ldur            w0, [x3, #0x1b]
    // 0x992290: DecompressPointer r0
    //     0x992290: add             x0, x0, HEAP, lsl #32
    // 0x992294: cmp             w0, NULL
    // 0x992298: b.ne            #0x9922a4
    // 0x99229c: r5 = 0
    //     0x99229c: movz            x5, #0
    // 0x9922a0: b               #0x9922b4
    // 0x9922a4: r1 = LoadInt32Instr(r0)
    //     0x9922a4: sbfx            x1, x0, #1, #0x1f
    //     0x9922a8: tbz             w0, #0, #0x9922b0
    //     0x9922ac: ldur            x1, [x0, #7]
    // 0x9922b0: mov             x5, x1
    // 0x9922b4: LoadField: r0 = r4->field_b
    //     0x9922b4: ldur            w0, [x4, #0xb]
    // 0x9922b8: r1 = LoadInt32Instr(r0)
    //     0x9922b8: sbfx            x1, x0, #1, #0x1f
    // 0x9922bc: mov             x0, x1
    // 0x9922c0: mov             x1, x5
    // 0x9922c4: cmp             x1, x0
    // 0x9922c8: b.hs            #0x993728
    // 0x9922cc: LoadField: r0 = r4->field_f
    //     0x9922cc: ldur            w0, [x4, #0xf]
    // 0x9922d0: DecompressPointer r0
    //     0x9922d0: add             x0, x0, HEAP, lsl #32
    // 0x9922d4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x9922d4: add             x16, x0, x5, lsl #2
    //     0x9922d8: ldur            w1, [x16, #0xf]
    // 0x9922dc: DecompressPointer r1
    //     0x9922dc: add             x1, x1, HEAP, lsl #32
    // 0x9922e0: LoadField: r0 = r1->field_b
    //     0x9922e0: ldur            w0, [x1, #0xb]
    // 0x9922e4: DecompressPointer r0
    //     0x9922e4: add             x0, x0, HEAP, lsl #32
    // 0x9922e8: r1 = LoadClassIdInstr(r0)
    //     0x9922e8: ldur            x1, [x0, #-1]
    //     0x9922ec: ubfx            x1, x1, #0xc, #0x14
    // 0x9922f0: r16 = "other"
    //     0x9922f0: add             x16, PP, #0x11, lsl #12  ; [pp+0x11f20] "other"
    //     0x9922f4: ldr             x16, [x16, #0xf20]
    // 0x9922f8: stp             x16, x0, [SP]
    // 0x9922fc: mov             x0, x1
    // 0x992300: mov             lr, x0
    // 0x992304: ldr             lr, [x21, lr, lsl #3]
    // 0x992308: blr             lr
    // 0x99230c: tbnz            w0, #4, #0x99231c
    // 0x992310: d0 = 340.000000
    //     0x992310: add             x17, PP, #0x5b, lsl #12  ; [pp+0x5bf78] IMM: double(340) from 0x4075400000000000
    //     0x992314: ldr             d0, [x17, #0xf78]
    // 0x992318: b               #0x992324
    // 0x99231c: d0 = 210.000000
    //     0x99231c: add             x17, PP, #0x5b, lsl #12  ; [pp+0x5bf80] IMM: double(210) from 0x406a400000000000
    //     0x992320: ldr             d0, [x17, #0xf80]
    // 0x992324: ldur            x0, [fp, #-8]
    // 0x992328: stur            d0, [fp, #-0x78]
    // 0x99232c: r1 = Null
    //     0x99232c: mov             x1, NULL
    // 0x992330: r2 = Instance_MaterialColor
    //     0x992330: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x992334: ldr             x2, [x2, #0xdc0]
    // 0x992338: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x992338: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x99233c: r0 = Border.all()
    //     0x99233c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x992340: mov             x1, x0
    // 0x992344: ldur            x0, [fp, #-8]
    // 0x992348: stur            x1, [fp, #-0x40]
    // 0x99234c: LoadField: r2 = r0->field_b
    //     0x99234c: ldur            w2, [x0, #0xb]
    // 0x992350: DecompressPointer r2
    //     0x992350: add             x2, x2, HEAP, lsl #32
    // 0x992354: stur            x2, [fp, #-0x38]
    // 0x992358: cmp             w2, NULL
    // 0x99235c: b.eq            #0x99372c
    // 0x992360: LoadField: r3 = r2->field_1f
    //     0x992360: ldur            w3, [x2, #0x1f]
    // 0x992364: DecompressPointer r3
    //     0x992364: add             x3, x3, HEAP, lsl #32
    // 0x992368: stur            x3, [fp, #-0x20]
    // 0x99236c: r0 = BoxDecoration()
    //     0x99236c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x992370: mov             x3, x0
    // 0x992374: ldur            x0, [fp, #-0x40]
    // 0x992378: stur            x3, [fp, #-0x48]
    // 0x99237c: StoreField: r3->field_f = r0
    //     0x99237c: stur            w0, [x3, #0xf]
    // 0x992380: ldur            x0, [fp, #-0x20]
    // 0x992384: StoreField: r3->field_13 = r0
    //     0x992384: stur            w0, [x3, #0x13]
    // 0x992388: r0 = Instance_BoxShape
    //     0x992388: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x99238c: ldr             x0, [x0, #0x80]
    // 0x992390: StoreField: r3->field_23 = r0
    //     0x992390: stur            w0, [x3, #0x23]
    // 0x992394: ldur            x0, [fp, #-0x38]
    // 0x992398: LoadField: r1 = r0->field_b
    //     0x992398: ldur            w1, [x0, #0xb]
    // 0x99239c: DecompressPointer r1
    //     0x99239c: add             x1, x1, HEAP, lsl #32
    // 0x9923a0: cmp             w1, NULL
    // 0x9923a4: b.ne            #0x9923b0
    // 0x9923a8: r0 = Null
    //     0x9923a8: mov             x0, NULL
    // 0x9923ac: b               #0x9923d0
    // 0x9923b0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9923b0: ldur            w0, [x1, #0x17]
    // 0x9923b4: DecompressPointer r0
    //     0x9923b4: add             x0, x0, HEAP, lsl #32
    // 0x9923b8: cmp             w0, NULL
    // 0x9923bc: b.ne            #0x9923c8
    // 0x9923c0: r0 = Null
    //     0x9923c0: mov             x0, NULL
    // 0x9923c4: b               #0x9923d0
    // 0x9923c8: LoadField: r1 = r0->field_b
    //     0x9923c8: ldur            w1, [x0, #0xb]
    // 0x9923cc: mov             x0, x1
    // 0x9923d0: cmp             w0, NULL
    // 0x9923d4: b.ne            #0x9923e0
    // 0x9923d8: r4 = 0
    //     0x9923d8: movz            x4, #0
    // 0x9923dc: b               #0x9923e8
    // 0x9923e0: r1 = LoadInt32Instr(r0)
    //     0x9923e0: sbfx            x1, x0, #1, #0x1f
    // 0x9923e4: mov             x4, x1
    // 0x9923e8: ldur            x0, [fp, #-8]
    // 0x9923ec: stur            x4, [fp, #-0x58]
    // 0x9923f0: r1 = Function '<anonymous closure>':.
    //     0x9923f0: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bf88] AnonymousClosure: (0x997c00), in [package:customer_app/app/presentation/views/glass/orders/order_item_card.dart] _OrderItemCardState::build (0xb78668)
    //     0x9923f4: ldr             x1, [x1, #0xf88]
    // 0x9923f8: r2 = Null
    //     0x9923f8: mov             x2, NULL
    // 0x9923fc: r0 = AllocateClosure()
    //     0x9923fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x992400: ldur            x2, [fp, #-0x18]
    // 0x992404: r1 = Function '<anonymous closure>':.
    //     0x992404: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bf90] AnonymousClosure: (0x997948), in [package:customer_app/app/presentation/custom_widgets/orders/cancel_order_confirm_bottom_sheet.dart] _CancelOrderConfirmBottomSheetState::build (0x991580)
    //     0x992408: ldr             x1, [x1, #0xf90]
    // 0x99240c: stur            x0, [fp, #-0x20]
    // 0x992410: r0 = AllocateClosure()
    //     0x992410: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x992414: stur            x0, [fp, #-0x38]
    // 0x992418: r0 = ListView()
    //     0x992418: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x99241c: stur            x0, [fp, #-0x40]
    // 0x992420: r16 = Instance_EdgeInsets
    //     0x992420: ldr             x16, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x992424: str             x16, [SP]
    // 0x992428: mov             x1, x0
    // 0x99242c: ldur            x2, [fp, #-0x38]
    // 0x992430: ldur            x3, [fp, #-0x58]
    // 0x992434: ldur            x5, [fp, #-0x20]
    // 0x992438: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0x992438: add             x4, PP, #0x5b, lsl #12  ; [pp+0x5bf98] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0x99243c: ldr             x4, [x4, #0xf98]
    // 0x992440: r0 = ListView.separated()
    //     0x992440: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x992444: r0 = Scrollbar()
    //     0x992444: bl              #0x997690  ; AllocateScrollbarStub -> Scrollbar (size=0x30)
    // 0x992448: mov             x1, x0
    // 0x99244c: ldur            x0, [fp, #-0x40]
    // 0x992450: stur            x1, [fp, #-0x20]
    // 0x992454: StoreField: r1->field_b = r0
    //     0x992454: stur            w0, [x1, #0xb]
    // 0x992458: r0 = true
    //     0x992458: add             x0, NULL, #0x20  ; true
    // 0x99245c: ArrayStore: r1[0] = r0  ; List_4
    //     0x99245c: stur            w0, [x1, #0x17]
    // 0x992460: r0 = ConstrainedBox()
    //     0x992460: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x992464: mov             x3, x0
    // 0x992468: r0 = Instance_BoxConstraints
    //     0x992468: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5bfa0] Obj!BoxConstraints@d565a1
    //     0x99246c: ldr             x0, [x0, #0xfa0]
    // 0x992470: stur            x3, [fp, #-0x38]
    // 0x992474: StoreField: r3->field_f = r0
    //     0x992474: stur            w0, [x3, #0xf]
    // 0x992478: ldur            x0, [fp, #-0x20]
    // 0x99247c: StoreField: r3->field_b = r0
    //     0x99247c: stur            w0, [x3, #0xb]
    // 0x992480: r1 = Null
    //     0x992480: mov             x1, NULL
    // 0x992484: r2 = 2
    //     0x992484: movz            x2, #0x2
    // 0x992488: r0 = AllocateArray()
    //     0x992488: bl              #0x16f7198  ; AllocateArrayStub
    // 0x99248c: mov             x2, x0
    // 0x992490: ldur            x0, [fp, #-0x38]
    // 0x992494: stur            x2, [fp, #-0x20]
    // 0x992498: StoreField: r2->field_f = r0
    //     0x992498: stur            w0, [x2, #0xf]
    // 0x99249c: r1 = <Widget>
    //     0x99249c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9924a0: r0 = AllocateGrowableArray()
    //     0x9924a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9924a4: mov             x2, x0
    // 0x9924a8: ldur            x0, [fp, #-0x20]
    // 0x9924ac: stur            x2, [fp, #-0x38]
    // 0x9924b0: StoreField: r2->field_f = r0
    //     0x9924b0: stur            w0, [x2, #0xf]
    // 0x9924b4: r3 = 2
    //     0x9924b4: movz            x3, #0x2
    // 0x9924b8: StoreField: r2->field_b = r3
    //     0x9924b8: stur            w3, [x2, #0xb]
    // 0x9924bc: ldur            x4, [fp, #-8]
    // 0x9924c0: LoadField: r0 = r4->field_b
    //     0x9924c0: ldur            w0, [x4, #0xb]
    // 0x9924c4: DecompressPointer r0
    //     0x9924c4: add             x0, x0, HEAP, lsl #32
    // 0x9924c8: cmp             w0, NULL
    // 0x9924cc: b.eq            #0x993730
    // 0x9924d0: LoadField: r1 = r0->field_b
    //     0x9924d0: ldur            w1, [x0, #0xb]
    // 0x9924d4: DecompressPointer r1
    //     0x9924d4: add             x1, x1, HEAP, lsl #32
    // 0x9924d8: cmp             w1, NULL
    // 0x9924dc: b.ne            #0x9924e8
    // 0x9924e0: r0 = Null
    //     0x9924e0: mov             x0, NULL
    // 0x9924e4: b               #0x99255c
    // 0x9924e8: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x9924e8: ldur            w5, [x1, #0x17]
    // 0x9924ec: DecompressPointer r5
    //     0x9924ec: add             x5, x5, HEAP, lsl #32
    // 0x9924f0: cmp             w5, NULL
    // 0x9924f4: b.ne            #0x992500
    // 0x9924f8: r0 = Null
    //     0x9924f8: mov             x0, NULL
    // 0x9924fc: b               #0x99255c
    // 0x992500: LoadField: r0 = r4->field_1b
    //     0x992500: ldur            w0, [x4, #0x1b]
    // 0x992504: DecompressPointer r0
    //     0x992504: add             x0, x0, HEAP, lsl #32
    // 0x992508: cmp             w0, NULL
    // 0x99250c: b.ne            #0x992518
    // 0x992510: r6 = 0
    //     0x992510: movz            x6, #0
    // 0x992514: b               #0x992528
    // 0x992518: r1 = LoadInt32Instr(r0)
    //     0x992518: sbfx            x1, x0, #1, #0x1f
    //     0x99251c: tbz             w0, #0, #0x992524
    //     0x992520: ldur            x1, [x0, #7]
    // 0x992524: mov             x6, x1
    // 0x992528: LoadField: r0 = r5->field_b
    //     0x992528: ldur            w0, [x5, #0xb]
    // 0x99252c: r1 = LoadInt32Instr(r0)
    //     0x99252c: sbfx            x1, x0, #1, #0x1f
    // 0x992530: mov             x0, x1
    // 0x992534: mov             x1, x6
    // 0x992538: cmp             x1, x0
    // 0x99253c: b.hs            #0x993734
    // 0x992540: LoadField: r0 = r5->field_f
    //     0x992540: ldur            w0, [x5, #0xf]
    // 0x992544: DecompressPointer r0
    //     0x992544: add             x0, x0, HEAP, lsl #32
    // 0x992548: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0x992548: add             x16, x0, x6, lsl #2
    //     0x99254c: ldur            w1, [x16, #0xf]
    // 0x992550: DecompressPointer r1
    //     0x992550: add             x1, x1, HEAP, lsl #32
    // 0x992554: LoadField: r0 = r1->field_b
    //     0x992554: ldur            w0, [x1, #0xb]
    // 0x992558: DecompressPointer r0
    //     0x992558: add             x0, x0, HEAP, lsl #32
    // 0x99255c: r1 = LoadClassIdInstr(r0)
    //     0x99255c: ldur            x1, [x0, #-1]
    //     0x992560: ubfx            x1, x1, #0xc, #0x14
    // 0x992564: r16 = "other"
    //     0x992564: add             x16, PP, #0x11, lsl #12  ; [pp+0x11f20] "other"
    //     0x992568: ldr             x16, [x16, #0xf20]
    // 0x99256c: stp             x16, x0, [SP]
    // 0x992570: mov             x0, x1
    // 0x992574: mov             lr, x0
    // 0x992578: ldr             lr, [x21, lr, lsl #3]
    // 0x99257c: blr             lr
    // 0x992580: tbnz            w0, #4, #0x992754
    // 0x992584: ldur            x0, [fp, #-8]
    // 0x992588: ldur            x1, [fp, #-0x38]
    // 0x99258c: LoadField: r2 = r0->field_13
    //     0x99258c: ldur            w2, [x0, #0x13]
    // 0x992590: DecompressPointer r2
    //     0x992590: add             x2, x2, HEAP, lsl #32
    // 0x992594: stur            x2, [fp, #-0x20]
    // 0x992598: r0 = LengthLimitingTextInputFormatter()
    //     0x992598: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0x99259c: mov             x3, x0
    // 0x9925a0: r0 = 120
    //     0x9925a0: movz            x0, #0x78
    // 0x9925a4: stur            x3, [fp, #-0x40]
    // 0x9925a8: StoreField: r3->field_7 = r0
    //     0x9925a8: stur            w0, [x3, #7]
    // 0x9925ac: r1 = Null
    //     0x9925ac: mov             x1, NULL
    // 0x9925b0: r2 = 2
    //     0x9925b0: movz            x2, #0x2
    // 0x9925b4: r0 = AllocateArray()
    //     0x9925b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9925b8: mov             x2, x0
    // 0x9925bc: ldur            x0, [fp, #-0x40]
    // 0x9925c0: stur            x2, [fp, #-0x50]
    // 0x9925c4: StoreField: r2->field_f = r0
    //     0x9925c4: stur            w0, [x2, #0xf]
    // 0x9925c8: r1 = <TextInputFormatter>
    //     0x9925c8: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0x9925cc: ldr             x1, [x1, #0x7b0]
    // 0x9925d0: r0 = AllocateGrowableArray()
    //     0x9925d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9925d4: mov             x3, x0
    // 0x9925d8: ldur            x0, [fp, #-0x50]
    // 0x9925dc: stur            x3, [fp, #-0x60]
    // 0x9925e0: StoreField: r3->field_f = r0
    //     0x9925e0: stur            w0, [x3, #0xf]
    // 0x9925e4: r0 = 2
    //     0x9925e4: movz            x0, #0x2
    // 0x9925e8: StoreField: r3->field_b = r0
    //     0x9925e8: stur            w0, [x3, #0xb]
    // 0x9925ec: ldur            x0, [fp, #-8]
    // 0x9925f0: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x9925f0: ldur            w4, [x0, #0x17]
    // 0x9925f4: DecompressPointer r4
    //     0x9925f4: add             x4, x4, HEAP, lsl #32
    // 0x9925f8: stur            x4, [fp, #-0x50]
    // 0x9925fc: LoadField: r1 = r0->field_b
    //     0x9925fc: ldur            w1, [x0, #0xb]
    // 0x992600: DecompressPointer r1
    //     0x992600: add             x1, x1, HEAP, lsl #32
    // 0x992604: cmp             w1, NULL
    // 0x992608: b.eq            #0x993738
    // 0x99260c: LoadField: r5 = r1->field_23
    //     0x99260c: ldur            w5, [x1, #0x23]
    // 0x992610: DecompressPointer r5
    //     0x992610: add             x5, x5, HEAP, lsl #32
    // 0x992614: stur            x5, [fp, #-0x40]
    // 0x992618: r1 = Function '<anonymous closure>':.
    //     0x992618: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bfa8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x99261c: ldr             x1, [x1, #0xfa8]
    // 0x992620: r2 = Null
    //     0x992620: mov             x2, NULL
    // 0x992624: r0 = AllocateClosure()
    //     0x992624: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x992628: r1 = <String>
    //     0x992628: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x99262c: stur            x0, [fp, #-0x68]
    // 0x992630: r0 = TextFormField()
    //     0x992630: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0x992634: stur            x0, [fp, #-0x70]
    // 0x992638: r16 = Closure: (String?) => String? from Function '_validateRemarks@1232453197': static.
    //     0x992638: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5bfb0] Closure: (String?) => String? from Function '_validateRemarks@1232453197': static. (0x7fa7379978c4)
    //     0x99263c: ldr             x16, [x16, #0xfb0]
    // 0x992640: r30 = Instance_AutovalidateMode
    //     0x992640: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0x992644: ldr             lr, [lr, #0x7e8]
    // 0x992648: stp             lr, x16, [SP, #0x38]
    // 0x99264c: r16 = false
    //     0x99264c: add             x16, NULL, #0x30  ; false
    // 0x992650: ldur            lr, [fp, #-0x60]
    // 0x992654: stp             lr, x16, [SP, #0x28]
    // 0x992658: r16 = Instance_TextInputType
    //     0x992658: add             x16, PP, #0x33, lsl #12  ; [pp+0x337f0] Obj!TextInputType@d55b61
    //     0x99265c: ldr             x16, [x16, #0x7f0]
    // 0x992660: r30 = 2
    //     0x992660: movz            lr, #0x2
    // 0x992664: stp             lr, x16, [SP, #0x18]
    // 0x992668: r16 = 6
    //     0x992668: movz            x16, #0x6
    // 0x99266c: ldur            lr, [fp, #-0x50]
    // 0x992670: stp             lr, x16, [SP, #8]
    // 0x992674: ldur            x16, [fp, #-0x68]
    // 0x992678: str             x16, [SP]
    // 0x99267c: mov             x1, x0
    // 0x992680: ldur            x2, [fp, #-0x40]
    // 0x992684: r4 = const [0, 0xb, 0x9, 0x2, autovalidateMode, 0x3, controller, 0x9, enableSuggestions, 0x4, inputFormatters, 0x5, keyboardType, 0x6, maxLines, 0x8, minLines, 0x7, onChanged, 0xa, validator, 0x2, null]
    //     0x992684: add             x4, PP, #0x5b, lsl #12  ; [pp+0x5bfb8] List(23) [0, 0xb, 0x9, 0x2, "autovalidateMode", 0x3, "controller", 0x9, "enableSuggestions", 0x4, "inputFormatters", 0x5, "keyboardType", 0x6, "maxLines", 0x8, "minLines", 0x7, "onChanged", 0xa, "validator", 0x2, Null]
    //     0x992688: ldr             x4, [x4, #0xfb8]
    // 0x99268c: r0 = TextFormField()
    //     0x99268c: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0x992690: r0 = Form()
    //     0x992690: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0x992694: mov             x1, x0
    // 0x992698: ldur            x0, [fp, #-0x70]
    // 0x99269c: stur            x1, [fp, #-0x40]
    // 0x9926a0: StoreField: r1->field_b = r0
    //     0x9926a0: stur            w0, [x1, #0xb]
    // 0x9926a4: r0 = Instance_AutovalidateMode
    //     0x9926a4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0x9926a8: ldr             x0, [x0, #0x800]
    // 0x9926ac: StoreField: r1->field_23 = r0
    //     0x9926ac: stur            w0, [x1, #0x23]
    // 0x9926b0: ldur            x0, [fp, #-0x20]
    // 0x9926b4: StoreField: r1->field_7 = r0
    //     0x9926b4: stur            w0, [x1, #7]
    // 0x9926b8: r0 = Padding()
    //     0x9926b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9926bc: mov             x2, x0
    // 0x9926c0: r0 = Instance_EdgeInsets
    //     0x9926c0: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5bfc0] Obj!EdgeInsets@d57441
    //     0x9926c4: ldr             x0, [x0, #0xfc0]
    // 0x9926c8: stur            x2, [fp, #-0x20]
    // 0x9926cc: StoreField: r2->field_f = r0
    //     0x9926cc: stur            w0, [x2, #0xf]
    // 0x9926d0: ldur            x0, [fp, #-0x40]
    // 0x9926d4: StoreField: r2->field_b = r0
    //     0x9926d4: stur            w0, [x2, #0xb]
    // 0x9926d8: ldur            x0, [fp, #-0x38]
    // 0x9926dc: LoadField: r1 = r0->field_b
    //     0x9926dc: ldur            w1, [x0, #0xb]
    // 0x9926e0: LoadField: r3 = r0->field_f
    //     0x9926e0: ldur            w3, [x0, #0xf]
    // 0x9926e4: DecompressPointer r3
    //     0x9926e4: add             x3, x3, HEAP, lsl #32
    // 0x9926e8: LoadField: r4 = r3->field_b
    //     0x9926e8: ldur            w4, [x3, #0xb]
    // 0x9926ec: r3 = LoadInt32Instr(r1)
    //     0x9926ec: sbfx            x3, x1, #1, #0x1f
    // 0x9926f0: stur            x3, [fp, #-0x58]
    // 0x9926f4: r1 = LoadInt32Instr(r4)
    //     0x9926f4: sbfx            x1, x4, #1, #0x1f
    // 0x9926f8: cmp             x3, x1
    // 0x9926fc: b.ne            #0x992708
    // 0x992700: mov             x1, x0
    // 0x992704: r0 = _growToNextCapacity()
    //     0x992704: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x992708: ldur            x2, [fp, #-0x38]
    // 0x99270c: ldur            x3, [fp, #-0x58]
    // 0x992710: add             x0, x3, #1
    // 0x992714: lsl             x1, x0, #1
    // 0x992718: StoreField: r2->field_b = r1
    //     0x992718: stur            w1, [x2, #0xb]
    // 0x99271c: LoadField: r1 = r2->field_f
    //     0x99271c: ldur            w1, [x2, #0xf]
    // 0x992720: DecompressPointer r1
    //     0x992720: add             x1, x1, HEAP, lsl #32
    // 0x992724: ldur            x0, [fp, #-0x20]
    // 0x992728: ArrayStore: r1[r3] = r0  ; List_4
    //     0x992728: add             x25, x1, x3, lsl #2
    //     0x99272c: add             x25, x25, #0xf
    //     0x992730: str             w0, [x25]
    //     0x992734: tbz             w0, #0, #0x992750
    //     0x992738: ldurb           w16, [x1, #-1]
    //     0x99273c: ldurb           w17, [x0, #-1]
    //     0x992740: and             x16, x17, x16, lsr #2
    //     0x992744: tst             x16, HEAP, lsr #32
    //     0x992748: b.eq            #0x992750
    //     0x99274c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x992750: b               #0x9927e4
    // 0x992754: ldur            x2, [fp, #-0x38]
    // 0x992758: r0 = Container()
    //     0x992758: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x99275c: mov             x1, x0
    // 0x992760: stur            x0, [fp, #-0x20]
    // 0x992764: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x992764: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x992768: r0 = Container()
    //     0x992768: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x99276c: ldur            x0, [fp, #-0x38]
    // 0x992770: LoadField: r1 = r0->field_b
    //     0x992770: ldur            w1, [x0, #0xb]
    // 0x992774: LoadField: r2 = r0->field_f
    //     0x992774: ldur            w2, [x0, #0xf]
    // 0x992778: DecompressPointer r2
    //     0x992778: add             x2, x2, HEAP, lsl #32
    // 0x99277c: LoadField: r3 = r2->field_b
    //     0x99277c: ldur            w3, [x2, #0xb]
    // 0x992780: r2 = LoadInt32Instr(r1)
    //     0x992780: sbfx            x2, x1, #1, #0x1f
    // 0x992784: stur            x2, [fp, #-0x58]
    // 0x992788: r1 = LoadInt32Instr(r3)
    //     0x992788: sbfx            x1, x3, #1, #0x1f
    // 0x99278c: cmp             x2, x1
    // 0x992790: b.ne            #0x99279c
    // 0x992794: mov             x1, x0
    // 0x992798: r0 = _growToNextCapacity()
    //     0x992798: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x99279c: ldur            x2, [fp, #-0x38]
    // 0x9927a0: ldur            x3, [fp, #-0x58]
    // 0x9927a4: add             x0, x3, #1
    // 0x9927a8: lsl             x1, x0, #1
    // 0x9927ac: StoreField: r2->field_b = r1
    //     0x9927ac: stur            w1, [x2, #0xb]
    // 0x9927b0: LoadField: r1 = r2->field_f
    //     0x9927b0: ldur            w1, [x2, #0xf]
    // 0x9927b4: DecompressPointer r1
    //     0x9927b4: add             x1, x1, HEAP, lsl #32
    // 0x9927b8: ldur            x0, [fp, #-0x20]
    // 0x9927bc: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9927bc: add             x25, x1, x3, lsl #2
    //     0x9927c0: add             x25, x25, #0xf
    //     0x9927c4: str             w0, [x25]
    //     0x9927c8: tbz             w0, #0, #0x9927e4
    //     0x9927cc: ldurb           w16, [x1, #-1]
    //     0x9927d0: ldurb           w17, [x0, #-1]
    //     0x9927d4: and             x16, x17, x16, lsr #2
    //     0x9927d8: tst             x16, HEAP, lsr #32
    //     0x9927dc: b.eq            #0x9927e4
    //     0x9927e0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9927e4: ldur            x1, [fp, #-0x28]
    // 0x9927e8: ldur            x0, [fp, #-0x30]
    // 0x9927ec: ldur            d0, [fp, #-0x78]
    // 0x9927f0: ldur            x3, [fp, #-0x10]
    // 0x9927f4: r0 = Column()
    //     0x9927f4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9927f8: mov             x1, x0
    // 0x9927fc: r0 = Instance_Axis
    //     0x9927fc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x992800: stur            x1, [fp, #-0x40]
    // 0x992804: StoreField: r1->field_f = r0
    //     0x992804: stur            w0, [x1, #0xf]
    // 0x992808: r2 = Instance_MainAxisAlignment
    //     0x992808: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x99280c: ldr             x2, [x2, #0xa08]
    // 0x992810: StoreField: r1->field_13 = r2
    //     0x992810: stur            w2, [x1, #0x13]
    // 0x992814: r3 = Instance_MainAxisSize
    //     0x992814: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x992818: ldr             x3, [x3, #0xa10]
    // 0x99281c: ArrayStore: r1[0] = r3  ; List_4
    //     0x99281c: stur            w3, [x1, #0x17]
    // 0x992820: r4 = Instance_CrossAxisAlignment
    //     0x992820: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x992824: ldr             x4, [x4, #0xa18]
    // 0x992828: StoreField: r1->field_1b = r4
    //     0x992828: stur            w4, [x1, #0x1b]
    // 0x99282c: r4 = Instance_VerticalDirection
    //     0x99282c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x992830: ldr             x4, [x4, #0xa20]
    // 0x992834: StoreField: r1->field_23 = r4
    //     0x992834: stur            w4, [x1, #0x23]
    // 0x992838: r5 = Instance_Clip
    //     0x992838: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x99283c: ldr             x5, [x5, #0x38]
    // 0x992840: StoreField: r1->field_2b = r5
    //     0x992840: stur            w5, [x1, #0x2b]
    // 0x992844: StoreField: r1->field_2f = rZR
    //     0x992844: stur            xzr, [x1, #0x2f]
    // 0x992848: ldur            x6, [fp, #-0x38]
    // 0x99284c: StoreField: r1->field_b = r6
    //     0x99284c: stur            w6, [x1, #0xb]
    // 0x992850: ldur            d0, [fp, #-0x78]
    // 0x992854: r6 = inline_Allocate_Double()
    //     0x992854: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0x992858: add             x6, x6, #0x10
    //     0x99285c: cmp             x7, x6
    //     0x992860: b.ls            #0x99373c
    //     0x992864: str             x6, [THR, #0x50]  ; THR::top
    //     0x992868: sub             x6, x6, #0xf
    //     0x99286c: movz            x7, #0xe15c
    //     0x992870: movk            x7, #0x3, lsl #16
    //     0x992874: stur            x7, [x6, #-1]
    // 0x992878: StoreField: r6->field_7 = d0
    //     0x992878: stur            d0, [x6, #7]
    // 0x99287c: stur            x6, [fp, #-0x20]
    // 0x992880: r0 = Container()
    //     0x992880: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x992884: stur            x0, [fp, #-0x38]
    // 0x992888: ldur            x16, [fp, #-0x20]
    // 0x99288c: ldur            lr, [fp, #-0x48]
    // 0x992890: stp             lr, x16, [SP, #8]
    // 0x992894: ldur            x16, [fp, #-0x40]
    // 0x992898: str             x16, [SP]
    // 0x99289c: mov             x1, x0
    // 0x9928a0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0x9928a0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0x9928a4: ldr             x4, [x4, #0xc78]
    // 0x9928a8: r0 = Container()
    //     0x9928a8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9928ac: r1 = Null
    //     0x9928ac: mov             x1, NULL
    // 0x9928b0: r2 = 12
    //     0x9928b0: movz            x2, #0xc
    // 0x9928b4: r0 = AllocateArray()
    //     0x9928b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9928b8: mov             x2, x0
    // 0x9928bc: ldur            x0, [fp, #-0x28]
    // 0x9928c0: stur            x2, [fp, #-0x20]
    // 0x9928c4: StoreField: r2->field_f = r0
    //     0x9928c4: stur            w0, [x2, #0xf]
    // 0x9928c8: r16 = Instance_SizedBox
    //     0x9928c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x9928cc: ldr             x16, [x16, #0x8f0]
    // 0x9928d0: StoreField: r2->field_13 = r16
    //     0x9928d0: stur            w16, [x2, #0x13]
    // 0x9928d4: ldur            x0, [fp, #-0x30]
    // 0x9928d8: ArrayStore: r2[0] = r0  ; List_4
    //     0x9928d8: stur            w0, [x2, #0x17]
    // 0x9928dc: r16 = Instance_SizedBox
    //     0x9928dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x9928e0: ldr             x16, [x16, #0x8f0]
    // 0x9928e4: StoreField: r2->field_1b = r16
    //     0x9928e4: stur            w16, [x2, #0x1b]
    // 0x9928e8: ldur            x0, [fp, #-0x38]
    // 0x9928ec: StoreField: r2->field_1f = r0
    //     0x9928ec: stur            w0, [x2, #0x1f]
    // 0x9928f0: r16 = Instance_SizedBox
    //     0x9928f0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0x9928f4: ldr             x16, [x16, #0x578]
    // 0x9928f8: StoreField: r2->field_23 = r16
    //     0x9928f8: stur            w16, [x2, #0x23]
    // 0x9928fc: r1 = <Widget>
    //     0x9928fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x992900: r0 = AllocateGrowableArray()
    //     0x992900: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x992904: mov             x1, x0
    // 0x992908: ldur            x0, [fp, #-0x20]
    // 0x99290c: stur            x1, [fp, #-0x28]
    // 0x992910: StoreField: r1->field_f = r0
    //     0x992910: stur            w0, [x1, #0xf]
    // 0x992914: r0 = 12
    //     0x992914: movz            x0, #0xc
    // 0x992918: StoreField: r1->field_b = r0
    //     0x992918: stur            w0, [x1, #0xb]
    // 0x99291c: r0 = Column()
    //     0x99291c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x992920: mov             x2, x0
    // 0x992924: r0 = Instance_Axis
    //     0x992924: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x992928: stur            x2, [fp, #-0x20]
    // 0x99292c: StoreField: r2->field_f = r0
    //     0x99292c: stur            w0, [x2, #0xf]
    // 0x992930: r3 = Instance_MainAxisAlignment
    //     0x992930: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x992934: ldr             x3, [x3, #0xa08]
    // 0x992938: StoreField: r2->field_13 = r3
    //     0x992938: stur            w3, [x2, #0x13]
    // 0x99293c: r4 = Instance_MainAxisSize
    //     0x99293c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x992940: ldr             x4, [x4, #0xa10]
    // 0x992944: ArrayStore: r2[0] = r4  ; List_4
    //     0x992944: stur            w4, [x2, #0x17]
    // 0x992948: r5 = Instance_CrossAxisAlignment
    //     0x992948: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x99294c: ldr             x5, [x5, #0x890]
    // 0x992950: StoreField: r2->field_1b = r5
    //     0x992950: stur            w5, [x2, #0x1b]
    // 0x992954: r6 = Instance_VerticalDirection
    //     0x992954: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x992958: ldr             x6, [x6, #0xa20]
    // 0x99295c: StoreField: r2->field_23 = r6
    //     0x99295c: stur            w6, [x2, #0x23]
    // 0x992960: r7 = Instance_Clip
    //     0x992960: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x992964: ldr             x7, [x7, #0x38]
    // 0x992968: StoreField: r2->field_2b = r7
    //     0x992968: stur            w7, [x2, #0x2b]
    // 0x99296c: StoreField: r2->field_2f = rZR
    //     0x99296c: stur            xzr, [x2, #0x2f]
    // 0x992970: ldur            x1, [fp, #-0x28]
    // 0x992974: StoreField: r2->field_b = r1
    //     0x992974: stur            w1, [x2, #0xb]
    // 0x992978: ldur            x8, [fp, #-0x10]
    // 0x99297c: LoadField: r1 = r8->field_b
    //     0x99297c: ldur            w1, [x8, #0xb]
    // 0x992980: LoadField: r9 = r8->field_f
    //     0x992980: ldur            w9, [x8, #0xf]
    // 0x992984: DecompressPointer r9
    //     0x992984: add             x9, x9, HEAP, lsl #32
    // 0x992988: LoadField: r10 = r9->field_b
    //     0x992988: ldur            w10, [x9, #0xb]
    // 0x99298c: r9 = LoadInt32Instr(r1)
    //     0x99298c: sbfx            x9, x1, #1, #0x1f
    // 0x992990: stur            x9, [fp, #-0x58]
    // 0x992994: r1 = LoadInt32Instr(r10)
    //     0x992994: sbfx            x1, x10, #1, #0x1f
    // 0x992998: cmp             x9, x1
    // 0x99299c: b.ne            #0x9929a8
    // 0x9929a0: mov             x1, x8
    // 0x9929a4: r0 = _growToNextCapacity()
    //     0x9929a4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9929a8: ldur            x2, [fp, #-0x10]
    // 0x9929ac: ldur            x3, [fp, #-0x58]
    // 0x9929b0: add             x0, x3, #1
    // 0x9929b4: lsl             x1, x0, #1
    // 0x9929b8: StoreField: r2->field_b = r1
    //     0x9929b8: stur            w1, [x2, #0xb]
    // 0x9929bc: LoadField: r1 = r2->field_f
    //     0x9929bc: ldur            w1, [x2, #0xf]
    // 0x9929c0: DecompressPointer r1
    //     0x9929c0: add             x1, x1, HEAP, lsl #32
    // 0x9929c4: ldur            x0, [fp, #-0x20]
    // 0x9929c8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9929c8: add             x25, x1, x3, lsl #2
    //     0x9929cc: add             x25, x25, #0xf
    //     0x9929d0: str             w0, [x25]
    //     0x9929d4: tbz             w0, #0, #0x9929f0
    //     0x9929d8: ldurb           w16, [x1, #-1]
    //     0x9929dc: ldurb           w17, [x0, #-1]
    //     0x9929e0: and             x16, x17, x16, lsr #2
    //     0x9929e4: tst             x16, HEAP, lsr #32
    //     0x9929e8: b.eq            #0x9929f0
    //     0x9929ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9929f0: b               #0x992a80
    // 0x9929f4: r0 = Container()
    //     0x9929f4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9929f8: mov             x1, x0
    // 0x9929fc: stur            x0, [fp, #-0x20]
    // 0x992a00: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x992a00: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x992a04: r0 = Container()
    //     0x992a04: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x992a08: ldur            x0, [fp, #-0x10]
    // 0x992a0c: LoadField: r1 = r0->field_b
    //     0x992a0c: ldur            w1, [x0, #0xb]
    // 0x992a10: LoadField: r2 = r0->field_f
    //     0x992a10: ldur            w2, [x0, #0xf]
    // 0x992a14: DecompressPointer r2
    //     0x992a14: add             x2, x2, HEAP, lsl #32
    // 0x992a18: LoadField: r3 = r2->field_b
    //     0x992a18: ldur            w3, [x2, #0xb]
    // 0x992a1c: r2 = LoadInt32Instr(r1)
    //     0x992a1c: sbfx            x2, x1, #1, #0x1f
    // 0x992a20: stur            x2, [fp, #-0x58]
    // 0x992a24: r1 = LoadInt32Instr(r3)
    //     0x992a24: sbfx            x1, x3, #1, #0x1f
    // 0x992a28: cmp             x2, x1
    // 0x992a2c: b.ne            #0x992a38
    // 0x992a30: mov             x1, x0
    // 0x992a34: r0 = _growToNextCapacity()
    //     0x992a34: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x992a38: ldur            x2, [fp, #-0x10]
    // 0x992a3c: ldur            x3, [fp, #-0x58]
    // 0x992a40: add             x0, x3, #1
    // 0x992a44: lsl             x1, x0, #1
    // 0x992a48: StoreField: r2->field_b = r1
    //     0x992a48: stur            w1, [x2, #0xb]
    // 0x992a4c: LoadField: r1 = r2->field_f
    //     0x992a4c: ldur            w1, [x2, #0xf]
    // 0x992a50: DecompressPointer r1
    //     0x992a50: add             x1, x1, HEAP, lsl #32
    // 0x992a54: ldur            x0, [fp, #-0x20]
    // 0x992a58: ArrayStore: r1[r3] = r0  ; List_4
    //     0x992a58: add             x25, x1, x3, lsl #2
    //     0x992a5c: add             x25, x25, #0xf
    //     0x992a60: str             w0, [x25]
    //     0x992a64: tbz             w0, #0, #0x992a80
    //     0x992a68: ldurb           w16, [x1, #-1]
    //     0x992a6c: ldurb           w17, [x0, #-1]
    //     0x992a70: and             x16, x17, x16, lsr #2
    //     0x992a74: tst             x16, HEAP, lsr #32
    //     0x992a78: b.eq            #0x992a80
    //     0x992a7c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x992a80: ldur            x0, [fp, #-8]
    // 0x992a84: r16 = <EdgeInsets>
    //     0x992a84: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x992a88: ldr             x16, [x16, #0xda0]
    // 0x992a8c: r30 = Instance_EdgeInsets
    //     0x992a8c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x992a90: ldr             lr, [lr, #0x1f0]
    // 0x992a94: stp             lr, x16, [SP]
    // 0x992a98: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x992a98: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x992a9c: r0 = all()
    //     0x992a9c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x992aa0: mov             x2, x0
    // 0x992aa4: ldur            x0, [fp, #-8]
    // 0x992aa8: stur            x2, [fp, #-0x20]
    // 0x992aac: LoadField: r1 = r0->field_b
    //     0x992aac: ldur            w1, [x0, #0xb]
    // 0x992ab0: DecompressPointer r1
    //     0x992ab0: add             x1, x1, HEAP, lsl #32
    // 0x992ab4: cmp             w1, NULL
    // 0x992ab8: b.eq            #0x993768
    // 0x992abc: LoadField: r3 = r1->field_27
    //     0x992abc: ldur            w3, [x1, #0x27]
    // 0x992ac0: DecompressPointer r3
    //     0x992ac0: add             x3, x3, HEAP, lsl #32
    // 0x992ac4: cmp             w3, NULL
    // 0x992ac8: b.ne            #0x992b30
    // 0x992acc: ldur            x3, [fp, #-0x18]
    // 0x992ad0: LoadField: r1 = r3->field_13
    //     0x992ad0: ldur            w1, [x3, #0x13]
    // 0x992ad4: DecompressPointer r1
    //     0x992ad4: add             x1, x1, HEAP, lsl #32
    // 0x992ad8: r0 = of()
    //     0x992ad8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x992adc: LoadField: r1 = r0->field_5b
    //     0x992adc: ldur            w1, [x0, #0x5b]
    // 0x992ae0: DecompressPointer r1
    //     0x992ae0: add             x1, x1, HEAP, lsl #32
    // 0x992ae4: r0 = LoadClassIdInstr(r1)
    //     0x992ae4: ldur            x0, [x1, #-1]
    //     0x992ae8: ubfx            x0, x0, #0xc, #0x14
    // 0x992aec: d0 = 0.100000
    //     0x992aec: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x992af0: r0 = GDT[cid_x0 + -0xffa]()
    //     0x992af0: sub             lr, x0, #0xffa
    //     0x992af4: ldr             lr, [x21, lr, lsl #3]
    //     0x992af8: blr             lr
    // 0x992afc: stur            x0, [fp, #-0x28]
    // 0x992b00: r0 = BorderSide()
    //     0x992b00: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x992b04: mov             x1, x0
    // 0x992b08: ldur            x0, [fp, #-0x28]
    // 0x992b0c: StoreField: r1->field_7 = r0
    //     0x992b0c: stur            w0, [x1, #7]
    // 0x992b10: d0 = 1.000000
    //     0x992b10: fmov            d0, #1.00000000
    // 0x992b14: StoreField: r1->field_b = d0
    //     0x992b14: stur            d0, [x1, #0xb]
    // 0x992b18: r0 = Instance_BorderStyle
    //     0x992b18: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x992b1c: ldr             x0, [x0, #0xf68]
    // 0x992b20: StoreField: r1->field_13 = r0
    //     0x992b20: stur            w0, [x1, #0x13]
    // 0x992b24: d0 = -1.000000
    //     0x992b24: fmov            d0, #-1.00000000
    // 0x992b28: ArrayStore: r1[0] = d0  ; List_8
    //     0x992b28: stur            d0, [x1, #0x17]
    // 0x992b2c: mov             x3, x1
    // 0x992b30: ldur            x0, [fp, #-8]
    // 0x992b34: ldur            x2, [fp, #-0x18]
    // 0x992b38: ldur            x1, [fp, #-0x20]
    // 0x992b3c: stur            x3, [fp, #-0x30]
    // 0x992b40: LoadField: r4 = r0->field_b
    //     0x992b40: ldur            w4, [x0, #0xb]
    // 0x992b44: DecompressPointer r4
    //     0x992b44: add             x4, x4, HEAP, lsl #32
    // 0x992b48: cmp             w4, NULL
    // 0x992b4c: b.eq            #0x99376c
    // 0x992b50: LoadField: r5 = r4->field_1f
    //     0x992b50: ldur            w5, [x4, #0x1f]
    // 0x992b54: DecompressPointer r5
    //     0x992b54: add             x5, x5, HEAP, lsl #32
    // 0x992b58: stur            x5, [fp, #-0x28]
    // 0x992b5c: r0 = RoundedRectangleBorder()
    //     0x992b5c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x992b60: mov             x1, x0
    // 0x992b64: ldur            x0, [fp, #-0x28]
    // 0x992b68: StoreField: r1->field_b = r0
    //     0x992b68: stur            w0, [x1, #0xb]
    // 0x992b6c: ldur            x0, [fp, #-0x30]
    // 0x992b70: StoreField: r1->field_7 = r0
    //     0x992b70: stur            w0, [x1, #7]
    // 0x992b74: r16 = <RoundedRectangleBorder>
    //     0x992b74: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x992b78: ldr             x16, [x16, #0xf78]
    // 0x992b7c: stp             x1, x16, [SP]
    // 0x992b80: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x992b80: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x992b84: r0 = all()
    //     0x992b84: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x992b88: stur            x0, [fp, #-0x28]
    // 0x992b8c: r0 = ButtonStyle()
    //     0x992b8c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x992b90: mov             x1, x0
    // 0x992b94: ldur            x0, [fp, #-0x20]
    // 0x992b98: stur            x1, [fp, #-0x30]
    // 0x992b9c: StoreField: r1->field_23 = r0
    //     0x992b9c: stur            w0, [x1, #0x23]
    // 0x992ba0: ldur            x0, [fp, #-0x28]
    // 0x992ba4: StoreField: r1->field_43 = r0
    //     0x992ba4: stur            w0, [x1, #0x43]
    // 0x992ba8: r0 = TextButtonThemeData()
    //     0x992ba8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x992bac: mov             x2, x0
    // 0x992bb0: ldur            x0, [fp, #-0x30]
    // 0x992bb4: stur            x2, [fp, #-0x28]
    // 0x992bb8: StoreField: r2->field_7 = r0
    //     0x992bb8: stur            w0, [x2, #7]
    // 0x992bbc: ldur            x0, [fp, #-8]
    // 0x992bc0: LoadField: r1 = r0->field_b
    //     0x992bc0: ldur            w1, [x0, #0xb]
    // 0x992bc4: DecompressPointer r1
    //     0x992bc4: add             x1, x1, HEAP, lsl #32
    // 0x992bc8: cmp             w1, NULL
    // 0x992bcc: b.eq            #0x993770
    // 0x992bd0: LoadField: r3 = r1->field_2b
    //     0x992bd0: ldur            w3, [x1, #0x2b]
    // 0x992bd4: DecompressPointer r3
    //     0x992bd4: add             x3, x3, HEAP, lsl #32
    // 0x992bd8: ldur            x4, [fp, #-0x18]
    // 0x992bdc: stur            x3, [fp, #-0x20]
    // 0x992be0: LoadField: r1 = r4->field_13
    //     0x992be0: ldur            w1, [x4, #0x13]
    // 0x992be4: DecompressPointer r1
    //     0x992be4: add             x1, x1, HEAP, lsl #32
    // 0x992be8: r0 = of()
    //     0x992be8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x992bec: LoadField: r1 = r0->field_87
    //     0x992bec: ldur            w1, [x0, #0x87]
    // 0x992bf0: DecompressPointer r1
    //     0x992bf0: add             x1, x1, HEAP, lsl #32
    // 0x992bf4: LoadField: r0 = r1->field_7
    //     0x992bf4: ldur            w0, [x1, #7]
    // 0x992bf8: DecompressPointer r0
    //     0x992bf8: add             x0, x0, HEAP, lsl #32
    // 0x992bfc: ldur            x2, [fp, #-0x18]
    // 0x992c00: stur            x0, [fp, #-0x30]
    // 0x992c04: LoadField: r1 = r2->field_13
    //     0x992c04: ldur            w1, [x2, #0x13]
    // 0x992c08: DecompressPointer r1
    //     0x992c08: add             x1, x1, HEAP, lsl #32
    // 0x992c0c: r0 = of()
    //     0x992c0c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x992c10: LoadField: r1 = r0->field_5b
    //     0x992c10: ldur            w1, [x0, #0x5b]
    // 0x992c14: DecompressPointer r1
    //     0x992c14: add             x1, x1, HEAP, lsl #32
    // 0x992c18: r16 = 14.000000
    //     0x992c18: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x992c1c: ldr             x16, [x16, #0x1d8]
    // 0x992c20: stp             x1, x16, [SP]
    // 0x992c24: ldur            x1, [fp, #-0x30]
    // 0x992c28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x992c28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x992c2c: ldr             x4, [x4, #0xaa0]
    // 0x992c30: r0 = copyWith()
    //     0x992c30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x992c34: stur            x0, [fp, #-0x30]
    // 0x992c38: r0 = Text()
    //     0x992c38: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x992c3c: mov             x3, x0
    // 0x992c40: ldur            x0, [fp, #-0x20]
    // 0x992c44: stur            x3, [fp, #-0x38]
    // 0x992c48: StoreField: r3->field_b = r0
    //     0x992c48: stur            w0, [x3, #0xb]
    // 0x992c4c: ldur            x0, [fp, #-0x30]
    // 0x992c50: StoreField: r3->field_13 = r0
    //     0x992c50: stur            w0, [x3, #0x13]
    // 0x992c54: r1 = Function '<anonymous closure>':.
    //     0x992c54: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bfc8] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x992c58: ldr             x1, [x1, #0xfc8]
    // 0x992c5c: r2 = Null
    //     0x992c5c: mov             x2, NULL
    // 0x992c60: r0 = AllocateClosure()
    //     0x992c60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x992c64: stur            x0, [fp, #-0x20]
    // 0x992c68: r0 = TextButton()
    //     0x992c68: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x992c6c: mov             x1, x0
    // 0x992c70: ldur            x0, [fp, #-0x20]
    // 0x992c74: stur            x1, [fp, #-0x30]
    // 0x992c78: StoreField: r1->field_b = r0
    //     0x992c78: stur            w0, [x1, #0xb]
    // 0x992c7c: r0 = false
    //     0x992c7c: add             x0, NULL, #0x30  ; false
    // 0x992c80: StoreField: r1->field_27 = r0
    //     0x992c80: stur            w0, [x1, #0x27]
    // 0x992c84: r2 = true
    //     0x992c84: add             x2, NULL, #0x20  ; true
    // 0x992c88: StoreField: r1->field_2f = r2
    //     0x992c88: stur            w2, [x1, #0x2f]
    // 0x992c8c: ldur            x3, [fp, #-0x38]
    // 0x992c90: StoreField: r1->field_37 = r3
    //     0x992c90: stur            w3, [x1, #0x37]
    // 0x992c94: r0 = TextButtonTheme()
    //     0x992c94: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x992c98: mov             x2, x0
    // 0x992c9c: ldur            x0, [fp, #-0x28]
    // 0x992ca0: stur            x2, [fp, #-0x20]
    // 0x992ca4: StoreField: r2->field_f = r0
    //     0x992ca4: stur            w0, [x2, #0xf]
    // 0x992ca8: ldur            x0, [fp, #-0x30]
    // 0x992cac: StoreField: r2->field_b = r0
    //     0x992cac: stur            w0, [x2, #0xb]
    // 0x992cb0: r1 = <FlexParentData>
    //     0x992cb0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x992cb4: ldr             x1, [x1, #0xe00]
    // 0x992cb8: r0 = Expanded()
    //     0x992cb8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x992cbc: mov             x1, x0
    // 0x992cc0: r0 = 1
    //     0x992cc0: movz            x0, #0x1
    // 0x992cc4: stur            x1, [fp, #-0x28]
    // 0x992cc8: StoreField: r1->field_13 = r0
    //     0x992cc8: stur            x0, [x1, #0x13]
    // 0x992ccc: r2 = Instance_FlexFit
    //     0x992ccc: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x992cd0: ldr             x2, [x2, #0xe08]
    // 0x992cd4: StoreField: r1->field_1b = r2
    //     0x992cd4: stur            w2, [x1, #0x1b]
    // 0x992cd8: ldur            x3, [fp, #-0x20]
    // 0x992cdc: StoreField: r1->field_b = r3
    //     0x992cdc: stur            w3, [x1, #0xb]
    // 0x992ce0: ldur            x3, [fp, #-8]
    // 0x992ce4: LoadField: r4 = r3->field_b
    //     0x992ce4: ldur            w4, [x3, #0xb]
    // 0x992ce8: DecompressPointer r4
    //     0x992ce8: add             x4, x4, HEAP, lsl #32
    // 0x992cec: cmp             w4, NULL
    // 0x992cf0: b.eq            #0x993774
    // 0x992cf4: LoadField: r5 = r4->field_b
    //     0x992cf4: ldur            w5, [x4, #0xb]
    // 0x992cf8: DecompressPointer r5
    //     0x992cf8: add             x5, x5, HEAP, lsl #32
    // 0x992cfc: cmp             w5, NULL
    // 0x992d00: b.ne            #0x992d0c
    // 0x992d04: r5 = true
    //     0x992d04: add             x5, NULL, #0x20  ; true
    // 0x992d08: b               #0x992d40
    // 0x992d0c: LoadField: r4 = r5->field_13
    //     0x992d0c: ldur            w4, [x5, #0x13]
    // 0x992d10: DecompressPointer r4
    //     0x992d10: add             x4, x4, HEAP, lsl #32
    // 0x992d14: cmp             w4, NULL
    // 0x992d18: b.eq            #0x992d3c
    // 0x992d1c: tbnz            w4, #4, #0x992d3c
    // 0x992d20: LoadField: r4 = r3->field_1b
    //     0x992d20: ldur            w4, [x3, #0x1b]
    // 0x992d24: DecompressPointer r4
    //     0x992d24: add             x4, x4, HEAP, lsl #32
    // 0x992d28: cmp             w4, NULL
    // 0x992d2c: r16 = true
    //     0x992d2c: add             x16, NULL, #0x20  ; true
    // 0x992d30: r17 = false
    //     0x992d30: add             x17, NULL, #0x30  ; false
    // 0x992d34: csel            x5, x16, x17, ne
    // 0x992d38: b               #0x992d40
    // 0x992d3c: r5 = false
    //     0x992d3c: add             x5, NULL, #0x30  ; false
    // 0x992d40: ldur            x4, [fp, #-0x18]
    // 0x992d44: stur            x5, [fp, #-0x20]
    // 0x992d48: r16 = <EdgeInsets>
    //     0x992d48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x992d4c: ldr             x16, [x16, #0xda0]
    // 0x992d50: r30 = Instance_EdgeInsets
    //     0x992d50: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x992d54: ldr             lr, [lr, #0x1f0]
    // 0x992d58: stp             lr, x16, [SP]
    // 0x992d5c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x992d5c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x992d60: r0 = all()
    //     0x992d60: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x992d64: ldur            x2, [fp, #-0x18]
    // 0x992d68: stur            x0, [fp, #-0x30]
    // 0x992d6c: LoadField: r1 = r2->field_13
    //     0x992d6c: ldur            w1, [x2, #0x13]
    // 0x992d70: DecompressPointer r1
    //     0x992d70: add             x1, x1, HEAP, lsl #32
    // 0x992d74: r0 = of()
    //     0x992d74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x992d78: LoadField: r1 = r0->field_5b
    //     0x992d78: ldur            w1, [x0, #0x5b]
    // 0x992d7c: DecompressPointer r1
    //     0x992d7c: add             x1, x1, HEAP, lsl #32
    // 0x992d80: r16 = <Color>
    //     0x992d80: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x992d84: ldr             x16, [x16, #0xf80]
    // 0x992d88: stp             x1, x16, [SP]
    // 0x992d8c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x992d8c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x992d90: r0 = all()
    //     0x992d90: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x992d94: mov             x1, x0
    // 0x992d98: ldur            x0, [fp, #-8]
    // 0x992d9c: stur            x1, [fp, #-0x40]
    // 0x992da0: LoadField: r2 = r0->field_b
    //     0x992da0: ldur            w2, [x0, #0xb]
    // 0x992da4: DecompressPointer r2
    //     0x992da4: add             x2, x2, HEAP, lsl #32
    // 0x992da8: cmp             w2, NULL
    // 0x992dac: b.eq            #0x993778
    // 0x992db0: LoadField: r3 = r2->field_1f
    //     0x992db0: ldur            w3, [x2, #0x1f]
    // 0x992db4: DecompressPointer r3
    //     0x992db4: add             x3, x3, HEAP, lsl #32
    // 0x992db8: stur            x3, [fp, #-0x38]
    // 0x992dbc: r0 = RoundedRectangleBorder()
    //     0x992dbc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x992dc0: mov             x1, x0
    // 0x992dc4: ldur            x0, [fp, #-0x38]
    // 0x992dc8: StoreField: r1->field_b = r0
    //     0x992dc8: stur            w0, [x1, #0xb]
    // 0x992dcc: r0 = Instance_BorderSide
    //     0x992dcc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x992dd0: ldr             x0, [x0, #0xe20]
    // 0x992dd4: StoreField: r1->field_7 = r0
    //     0x992dd4: stur            w0, [x1, #7]
    // 0x992dd8: r16 = <RoundedRectangleBorder>
    //     0x992dd8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x992ddc: ldr             x16, [x16, #0xf78]
    // 0x992de0: stp             x1, x16, [SP]
    // 0x992de4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x992de4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x992de8: r0 = all()
    //     0x992de8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x992dec: stur            x0, [fp, #-0x38]
    // 0x992df0: r0 = ButtonStyle()
    //     0x992df0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x992df4: mov             x1, x0
    // 0x992df8: ldur            x0, [fp, #-0x40]
    // 0x992dfc: stur            x1, [fp, #-0x48]
    // 0x992e00: StoreField: r1->field_b = r0
    //     0x992e00: stur            w0, [x1, #0xb]
    // 0x992e04: ldur            x0, [fp, #-0x30]
    // 0x992e08: StoreField: r1->field_23 = r0
    //     0x992e08: stur            w0, [x1, #0x23]
    // 0x992e0c: ldur            x0, [fp, #-0x38]
    // 0x992e10: StoreField: r1->field_43 = r0
    //     0x992e10: stur            w0, [x1, #0x43]
    // 0x992e14: r0 = TextButtonThemeData()
    //     0x992e14: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x992e18: mov             x2, x0
    // 0x992e1c: ldur            x0, [fp, #-0x48]
    // 0x992e20: stur            x2, [fp, #-0x38]
    // 0x992e24: StoreField: r2->field_7 = r0
    //     0x992e24: stur            w0, [x2, #7]
    // 0x992e28: ldur            x0, [fp, #-8]
    // 0x992e2c: LoadField: r1 = r0->field_b
    //     0x992e2c: ldur            w1, [x0, #0xb]
    // 0x992e30: DecompressPointer r1
    //     0x992e30: add             x1, x1, HEAP, lsl #32
    // 0x992e34: cmp             w1, NULL
    // 0x992e38: b.eq            #0x99377c
    // 0x992e3c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x992e3c: ldur            w3, [x1, #0x17]
    // 0x992e40: DecompressPointer r3
    //     0x992e40: add             x3, x3, HEAP, lsl #32
    // 0x992e44: ldur            x4, [fp, #-0x18]
    // 0x992e48: stur            x3, [fp, #-0x30]
    // 0x992e4c: LoadField: r1 = r4->field_13
    //     0x992e4c: ldur            w1, [x4, #0x13]
    // 0x992e50: DecompressPointer r1
    //     0x992e50: add             x1, x1, HEAP, lsl #32
    // 0x992e54: r0 = of()
    //     0x992e54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x992e58: LoadField: r1 = r0->field_87
    //     0x992e58: ldur            w1, [x0, #0x87]
    // 0x992e5c: DecompressPointer r1
    //     0x992e5c: add             x1, x1, HEAP, lsl #32
    // 0x992e60: LoadField: r0 = r1->field_7
    //     0x992e60: ldur            w0, [x1, #7]
    // 0x992e64: DecompressPointer r0
    //     0x992e64: add             x0, x0, HEAP, lsl #32
    // 0x992e68: r16 = 14.000000
    //     0x992e68: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x992e6c: ldr             x16, [x16, #0x1d8]
    // 0x992e70: r30 = Instance_Color
    //     0x992e70: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x992e74: stp             lr, x16, [SP]
    // 0x992e78: mov             x1, x0
    // 0x992e7c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x992e7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x992e80: ldr             x4, [x4, #0xaa0]
    // 0x992e84: r0 = copyWith()
    //     0x992e84: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x992e88: stur            x0, [fp, #-0x40]
    // 0x992e8c: r0 = Text()
    //     0x992e8c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x992e90: mov             x3, x0
    // 0x992e94: ldur            x0, [fp, #-0x30]
    // 0x992e98: stur            x3, [fp, #-0x48]
    // 0x992e9c: StoreField: r3->field_b = r0
    //     0x992e9c: stur            w0, [x3, #0xb]
    // 0x992ea0: ldur            x0, [fp, #-0x40]
    // 0x992ea4: StoreField: r3->field_13 = r0
    //     0x992ea4: stur            w0, [x3, #0x13]
    // 0x992ea8: r0 = Instance_TextAlign
    //     0x992ea8: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0x992eac: StoreField: r3->field_1b = r0
    //     0x992eac: stur            w0, [x3, #0x1b]
    // 0x992eb0: ldur            x2, [fp, #-0x18]
    // 0x992eb4: r1 = Function '<anonymous closure>':.
    //     0x992eb4: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bfd0] AnonymousClosure: (0x997744), in [package:customer_app/app/presentation/custom_widgets/orders/cancel_order_confirm_bottom_sheet.dart] _CancelOrderConfirmBottomSheetState::build (0x991580)
    //     0x992eb8: ldr             x1, [x1, #0xfd0]
    // 0x992ebc: r0 = AllocateClosure()
    //     0x992ebc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x992ec0: stur            x0, [fp, #-0x30]
    // 0x992ec4: r0 = TextButton()
    //     0x992ec4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x992ec8: mov             x1, x0
    // 0x992ecc: ldur            x0, [fp, #-0x30]
    // 0x992ed0: stur            x1, [fp, #-0x40]
    // 0x992ed4: StoreField: r1->field_b = r0
    //     0x992ed4: stur            w0, [x1, #0xb]
    // 0x992ed8: r0 = false
    //     0x992ed8: add             x0, NULL, #0x30  ; false
    // 0x992edc: StoreField: r1->field_27 = r0
    //     0x992edc: stur            w0, [x1, #0x27]
    // 0x992ee0: r2 = true
    //     0x992ee0: add             x2, NULL, #0x20  ; true
    // 0x992ee4: StoreField: r1->field_2f = r2
    //     0x992ee4: stur            w2, [x1, #0x2f]
    // 0x992ee8: ldur            x3, [fp, #-0x48]
    // 0x992eec: StoreField: r1->field_37 = r3
    //     0x992eec: stur            w3, [x1, #0x37]
    // 0x992ef0: r0 = TextButtonTheme()
    //     0x992ef0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x992ef4: mov             x2, x0
    // 0x992ef8: ldur            x0, [fp, #-0x38]
    // 0x992efc: stur            x2, [fp, #-0x30]
    // 0x992f00: StoreField: r2->field_f = r0
    //     0x992f00: stur            w0, [x2, #0xf]
    // 0x992f04: ldur            x0, [fp, #-0x40]
    // 0x992f08: StoreField: r2->field_b = r0
    //     0x992f08: stur            w0, [x2, #0xb]
    // 0x992f0c: r1 = <FlexParentData>
    //     0x992f0c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x992f10: ldr             x1, [x1, #0xe00]
    // 0x992f14: r0 = Expanded()
    //     0x992f14: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x992f18: mov             x1, x0
    // 0x992f1c: r0 = 1
    //     0x992f1c: movz            x0, #0x1
    // 0x992f20: stur            x1, [fp, #-0x38]
    // 0x992f24: StoreField: r1->field_13 = r0
    //     0x992f24: stur            x0, [x1, #0x13]
    // 0x992f28: r2 = Instance_FlexFit
    //     0x992f28: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x992f2c: ldr             x2, [x2, #0xe08]
    // 0x992f30: StoreField: r1->field_1b = r2
    //     0x992f30: stur            w2, [x1, #0x1b]
    // 0x992f34: ldur            x3, [fp, #-0x30]
    // 0x992f38: StoreField: r1->field_b = r3
    //     0x992f38: stur            w3, [x1, #0xb]
    // 0x992f3c: r0 = Visibility()
    //     0x992f3c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x992f40: mov             x1, x0
    // 0x992f44: ldur            x0, [fp, #-0x38]
    // 0x992f48: stur            x1, [fp, #-0x30]
    // 0x992f4c: StoreField: r1->field_b = r0
    //     0x992f4c: stur            w0, [x1, #0xb]
    // 0x992f50: r0 = Instance_SizedBox
    //     0x992f50: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x992f54: StoreField: r1->field_f = r0
    //     0x992f54: stur            w0, [x1, #0xf]
    // 0x992f58: ldur            x2, [fp, #-0x20]
    // 0x992f5c: StoreField: r1->field_13 = r2
    //     0x992f5c: stur            w2, [x1, #0x13]
    // 0x992f60: r2 = false
    //     0x992f60: add             x2, NULL, #0x30  ; false
    // 0x992f64: ArrayStore: r1[0] = r2  ; List_4
    //     0x992f64: stur            w2, [x1, #0x17]
    // 0x992f68: StoreField: r1->field_1b = r2
    //     0x992f68: stur            w2, [x1, #0x1b]
    // 0x992f6c: StoreField: r1->field_1f = r2
    //     0x992f6c: stur            w2, [x1, #0x1f]
    // 0x992f70: StoreField: r1->field_23 = r2
    //     0x992f70: stur            w2, [x1, #0x23]
    // 0x992f74: StoreField: r1->field_27 = r2
    //     0x992f74: stur            w2, [x1, #0x27]
    // 0x992f78: StoreField: r1->field_2b = r2
    //     0x992f78: stur            w2, [x1, #0x2b]
    // 0x992f7c: ldur            x3, [fp, #-8]
    // 0x992f80: LoadField: r4 = r3->field_b
    //     0x992f80: ldur            w4, [x3, #0xb]
    // 0x992f84: DecompressPointer r4
    //     0x992f84: add             x4, x4, HEAP, lsl #32
    // 0x992f88: cmp             w4, NULL
    // 0x992f8c: b.eq            #0x993780
    // 0x992f90: LoadField: r5 = r4->field_b
    //     0x992f90: ldur            w5, [x4, #0xb]
    // 0x992f94: DecompressPointer r5
    //     0x992f94: add             x5, x5, HEAP, lsl #32
    // 0x992f98: cmp             w5, NULL
    // 0x992f9c: b.ne            #0x992fa8
    // 0x992fa0: r4 = Null
    //     0x992fa0: mov             x4, NULL
    // 0x992fa4: b               #0x992fb0
    // 0x992fa8: LoadField: r4 = r5->field_13
    //     0x992fa8: ldur            w4, [x5, #0x13]
    // 0x992fac: DecompressPointer r4
    //     0x992fac: add             x4, x4, HEAP, lsl #32
    // 0x992fb0: cmp             w4, NULL
    // 0x992fb4: b.eq            #0x992fd8
    // 0x992fb8: tbnz            w4, #4, #0x992fd8
    // 0x992fbc: LoadField: r4 = r3->field_1b
    //     0x992fbc: ldur            w4, [x3, #0x1b]
    // 0x992fc0: DecompressPointer r4
    //     0x992fc0: add             x4, x4, HEAP, lsl #32
    // 0x992fc4: cmp             w4, NULL
    // 0x992fc8: r16 = true
    //     0x992fc8: add             x16, NULL, #0x20  ; true
    // 0x992fcc: r17 = false
    //     0x992fcc: add             x17, NULL, #0x30  ; false
    // 0x992fd0: csel            x5, x16, x17, eq
    // 0x992fd4: b               #0x992fdc
    // 0x992fd8: r5 = false
    //     0x992fd8: add             x5, NULL, #0x30  ; false
    // 0x992fdc: ldur            x4, [fp, #-0x18]
    // 0x992fe0: stur            x5, [fp, #-0x20]
    // 0x992fe4: r16 = <EdgeInsets>
    //     0x992fe4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x992fe8: ldr             x16, [x16, #0xda0]
    // 0x992fec: r30 = Instance_EdgeInsets
    //     0x992fec: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x992ff0: ldr             lr, [lr, #0x1f0]
    // 0x992ff4: stp             lr, x16, [SP]
    // 0x992ff8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x992ff8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x992ffc: r0 = all()
    //     0x992ffc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x993000: ldur            x2, [fp, #-0x18]
    // 0x993004: stur            x0, [fp, #-0x38]
    // 0x993008: LoadField: r1 = r2->field_13
    //     0x993008: ldur            w1, [x2, #0x13]
    // 0x99300c: DecompressPointer r1
    //     0x99300c: add             x1, x1, HEAP, lsl #32
    // 0x993010: r0 = of()
    //     0x993010: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x993014: LoadField: r1 = r0->field_5b
    //     0x993014: ldur            w1, [x0, #0x5b]
    // 0x993018: DecompressPointer r1
    //     0x993018: add             x1, x1, HEAP, lsl #32
    // 0x99301c: r0 = LoadClassIdInstr(r1)
    //     0x99301c: ldur            x0, [x1, #-1]
    //     0x993020: ubfx            x0, x0, #0xc, #0x14
    // 0x993024: d0 = 0.100000
    //     0x993024: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x993028: r0 = GDT[cid_x0 + -0xffa]()
    //     0x993028: sub             lr, x0, #0xffa
    //     0x99302c: ldr             lr, [x21, lr, lsl #3]
    //     0x993030: blr             lr
    // 0x993034: r16 = <Color>
    //     0x993034: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x993038: ldr             x16, [x16, #0xf80]
    // 0x99303c: stp             x0, x16, [SP]
    // 0x993040: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x993040: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x993044: r0 = all()
    //     0x993044: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x993048: mov             x1, x0
    // 0x99304c: ldur            x0, [fp, #-8]
    // 0x993050: stur            x1, [fp, #-0x48]
    // 0x993054: LoadField: r2 = r0->field_b
    //     0x993054: ldur            w2, [x0, #0xb]
    // 0x993058: DecompressPointer r2
    //     0x993058: add             x2, x2, HEAP, lsl #32
    // 0x99305c: cmp             w2, NULL
    // 0x993060: b.eq            #0x993784
    // 0x993064: LoadField: r3 = r2->field_1f
    //     0x993064: ldur            w3, [x2, #0x1f]
    // 0x993068: DecompressPointer r3
    //     0x993068: add             x3, x3, HEAP, lsl #32
    // 0x99306c: stur            x3, [fp, #-0x40]
    // 0x993070: r0 = RoundedRectangleBorder()
    //     0x993070: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x993074: mov             x1, x0
    // 0x993078: ldur            x0, [fp, #-0x40]
    // 0x99307c: StoreField: r1->field_b = r0
    //     0x99307c: stur            w0, [x1, #0xb]
    // 0x993080: r0 = Instance_BorderSide
    //     0x993080: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x993084: ldr             x0, [x0, #0xe20]
    // 0x993088: StoreField: r1->field_7 = r0
    //     0x993088: stur            w0, [x1, #7]
    // 0x99308c: r16 = <RoundedRectangleBorder>
    //     0x99308c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x993090: ldr             x16, [x16, #0xf78]
    // 0x993094: stp             x1, x16, [SP]
    // 0x993098: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x993098: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x99309c: r0 = all()
    //     0x99309c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x9930a0: stur            x0, [fp, #-0x40]
    // 0x9930a4: r0 = ButtonStyle()
    //     0x9930a4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x9930a8: mov             x1, x0
    // 0x9930ac: ldur            x0, [fp, #-0x48]
    // 0x9930b0: stur            x1, [fp, #-0x50]
    // 0x9930b4: StoreField: r1->field_b = r0
    //     0x9930b4: stur            w0, [x1, #0xb]
    // 0x9930b8: ldur            x0, [fp, #-0x38]
    // 0x9930bc: StoreField: r1->field_23 = r0
    //     0x9930bc: stur            w0, [x1, #0x23]
    // 0x9930c0: ldur            x0, [fp, #-0x40]
    // 0x9930c4: StoreField: r1->field_43 = r0
    //     0x9930c4: stur            w0, [x1, #0x43]
    // 0x9930c8: r0 = TextButtonThemeData()
    //     0x9930c8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x9930cc: mov             x2, x0
    // 0x9930d0: ldur            x0, [fp, #-0x50]
    // 0x9930d4: stur            x2, [fp, #-0x40]
    // 0x9930d8: StoreField: r2->field_7 = r0
    //     0x9930d8: stur            w0, [x2, #7]
    // 0x9930dc: ldur            x0, [fp, #-8]
    // 0x9930e0: LoadField: r1 = r0->field_b
    //     0x9930e0: ldur            w1, [x0, #0xb]
    // 0x9930e4: DecompressPointer r1
    //     0x9930e4: add             x1, x1, HEAP, lsl #32
    // 0x9930e8: cmp             w1, NULL
    // 0x9930ec: b.eq            #0x993788
    // 0x9930f0: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x9930f0: ldur            w3, [x1, #0x17]
    // 0x9930f4: DecompressPointer r3
    //     0x9930f4: add             x3, x3, HEAP, lsl #32
    // 0x9930f8: ldur            x4, [fp, #-0x18]
    // 0x9930fc: stur            x3, [fp, #-0x38]
    // 0x993100: LoadField: r1 = r4->field_13
    //     0x993100: ldur            w1, [x4, #0x13]
    // 0x993104: DecompressPointer r1
    //     0x993104: add             x1, x1, HEAP, lsl #32
    // 0x993108: r0 = of()
    //     0x993108: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x99310c: LoadField: r1 = r0->field_87
    //     0x99310c: ldur            w1, [x0, #0x87]
    // 0x993110: DecompressPointer r1
    //     0x993110: add             x1, x1, HEAP, lsl #32
    // 0x993114: LoadField: r0 = r1->field_7
    //     0x993114: ldur            w0, [x1, #7]
    // 0x993118: DecompressPointer r0
    //     0x993118: add             x0, x0, HEAP, lsl #32
    // 0x99311c: ldur            x2, [fp, #-0x18]
    // 0x993120: stur            x0, [fp, #-0x48]
    // 0x993124: LoadField: r1 = r2->field_13
    //     0x993124: ldur            w1, [x2, #0x13]
    // 0x993128: DecompressPointer r1
    //     0x993128: add             x1, x1, HEAP, lsl #32
    // 0x99312c: r0 = of()
    //     0x99312c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x993130: LoadField: r1 = r0->field_5b
    //     0x993130: ldur            w1, [x0, #0x5b]
    // 0x993134: DecompressPointer r1
    //     0x993134: add             x1, x1, HEAP, lsl #32
    // 0x993138: r0 = LoadClassIdInstr(r1)
    //     0x993138: ldur            x0, [x1, #-1]
    //     0x99313c: ubfx            x0, x0, #0xc, #0x14
    // 0x993140: d0 = 0.100000
    //     0x993140: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x993144: r0 = GDT[cid_x0 + -0xffa]()
    //     0x993144: sub             lr, x0, #0xffa
    //     0x993148: ldr             lr, [x21, lr, lsl #3]
    //     0x99314c: blr             lr
    // 0x993150: r16 = 14.000000
    //     0x993150: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x993154: ldr             x16, [x16, #0x1d8]
    // 0x993158: stp             x0, x16, [SP]
    // 0x99315c: ldur            x1, [fp, #-0x48]
    // 0x993160: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x993160: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x993164: ldr             x4, [x4, #0xaa0]
    // 0x993168: r0 = copyWith()
    //     0x993168: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x99316c: stur            x0, [fp, #-0x48]
    // 0x993170: r0 = Text()
    //     0x993170: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x993174: mov             x3, x0
    // 0x993178: ldur            x0, [fp, #-0x38]
    // 0x99317c: stur            x3, [fp, #-0x50]
    // 0x993180: StoreField: r3->field_b = r0
    //     0x993180: stur            w0, [x3, #0xb]
    // 0x993184: ldur            x0, [fp, #-0x48]
    // 0x993188: StoreField: r3->field_13 = r0
    //     0x993188: stur            w0, [x3, #0x13]
    // 0x99318c: r1 = Function '<anonymous closure>':.
    //     0x99318c: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bfd8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x993190: ldr             x1, [x1, #0xfd8]
    // 0x993194: r2 = Null
    //     0x993194: mov             x2, NULL
    // 0x993198: r0 = AllocateClosure()
    //     0x993198: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x99319c: stur            x0, [fp, #-0x38]
    // 0x9931a0: r0 = TextButton()
    //     0x9931a0: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x9931a4: mov             x1, x0
    // 0x9931a8: ldur            x0, [fp, #-0x38]
    // 0x9931ac: stur            x1, [fp, #-0x48]
    // 0x9931b0: StoreField: r1->field_b = r0
    //     0x9931b0: stur            w0, [x1, #0xb]
    // 0x9931b4: r0 = false
    //     0x9931b4: add             x0, NULL, #0x30  ; false
    // 0x9931b8: StoreField: r1->field_27 = r0
    //     0x9931b8: stur            w0, [x1, #0x27]
    // 0x9931bc: r2 = true
    //     0x9931bc: add             x2, NULL, #0x20  ; true
    // 0x9931c0: StoreField: r1->field_2f = r2
    //     0x9931c0: stur            w2, [x1, #0x2f]
    // 0x9931c4: ldur            x3, [fp, #-0x50]
    // 0x9931c8: StoreField: r1->field_37 = r3
    //     0x9931c8: stur            w3, [x1, #0x37]
    // 0x9931cc: r0 = TextButtonTheme()
    //     0x9931cc: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x9931d0: mov             x2, x0
    // 0x9931d4: ldur            x0, [fp, #-0x40]
    // 0x9931d8: stur            x2, [fp, #-0x38]
    // 0x9931dc: StoreField: r2->field_f = r0
    //     0x9931dc: stur            w0, [x2, #0xf]
    // 0x9931e0: ldur            x0, [fp, #-0x48]
    // 0x9931e4: StoreField: r2->field_b = r0
    //     0x9931e4: stur            w0, [x2, #0xb]
    // 0x9931e8: r1 = <FlexParentData>
    //     0x9931e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x9931ec: ldr             x1, [x1, #0xe00]
    // 0x9931f0: r0 = Expanded()
    //     0x9931f0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x9931f4: mov             x1, x0
    // 0x9931f8: r0 = 1
    //     0x9931f8: movz            x0, #0x1
    // 0x9931fc: stur            x1, [fp, #-0x40]
    // 0x993200: StoreField: r1->field_13 = r0
    //     0x993200: stur            x0, [x1, #0x13]
    // 0x993204: r2 = Instance_FlexFit
    //     0x993204: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x993208: ldr             x2, [x2, #0xe08]
    // 0x99320c: StoreField: r1->field_1b = r2
    //     0x99320c: stur            w2, [x1, #0x1b]
    // 0x993210: ldur            x3, [fp, #-0x38]
    // 0x993214: StoreField: r1->field_b = r3
    //     0x993214: stur            w3, [x1, #0xb]
    // 0x993218: r0 = Visibility()
    //     0x993218: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x99321c: mov             x1, x0
    // 0x993220: ldur            x0, [fp, #-0x40]
    // 0x993224: stur            x1, [fp, #-0x38]
    // 0x993228: StoreField: r1->field_b = r0
    //     0x993228: stur            w0, [x1, #0xb]
    // 0x99322c: r0 = Instance_SizedBox
    //     0x99322c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x993230: StoreField: r1->field_f = r0
    //     0x993230: stur            w0, [x1, #0xf]
    // 0x993234: ldur            x2, [fp, #-0x20]
    // 0x993238: StoreField: r1->field_13 = r2
    //     0x993238: stur            w2, [x1, #0x13]
    // 0x99323c: r2 = false
    //     0x99323c: add             x2, NULL, #0x30  ; false
    // 0x993240: ArrayStore: r1[0] = r2  ; List_4
    //     0x993240: stur            w2, [x1, #0x17]
    // 0x993244: StoreField: r1->field_1b = r2
    //     0x993244: stur            w2, [x1, #0x1b]
    // 0x993248: StoreField: r1->field_1f = r2
    //     0x993248: stur            w2, [x1, #0x1f]
    // 0x99324c: StoreField: r1->field_23 = r2
    //     0x99324c: stur            w2, [x1, #0x23]
    // 0x993250: StoreField: r1->field_27 = r2
    //     0x993250: stur            w2, [x1, #0x27]
    // 0x993254: StoreField: r1->field_2b = r2
    //     0x993254: stur            w2, [x1, #0x2b]
    // 0x993258: ldur            x3, [fp, #-8]
    // 0x99325c: LoadField: r4 = r3->field_b
    //     0x99325c: ldur            w4, [x3, #0xb]
    // 0x993260: DecompressPointer r4
    //     0x993260: add             x4, x4, HEAP, lsl #32
    // 0x993264: cmp             w4, NULL
    // 0x993268: b.eq            #0x99378c
    // 0x99326c: LoadField: r5 = r4->field_b
    //     0x99326c: ldur            w5, [x4, #0xb]
    // 0x993270: DecompressPointer r5
    //     0x993270: add             x5, x5, HEAP, lsl #32
    // 0x993274: cmp             w5, NULL
    // 0x993278: b.ne            #0x993284
    // 0x99327c: r4 = Null
    //     0x99327c: mov             x4, NULL
    // 0x993280: b               #0x9932b0
    // 0x993284: ArrayLoad: r4 = r5[0]  ; List_4
    //     0x993284: ldur            w4, [x5, #0x17]
    // 0x993288: DecompressPointer r4
    //     0x993288: add             x4, x4, HEAP, lsl #32
    // 0x99328c: cmp             w4, NULL
    // 0x993290: b.ne            #0x99329c
    // 0x993294: r4 = Null
    //     0x993294: mov             x4, NULL
    // 0x993298: b               #0x9932b0
    // 0x99329c: LoadField: r5 = r4->field_b
    //     0x99329c: ldur            w5, [x4, #0xb]
    // 0x9932a0: cbz             w5, #0x9932ac
    // 0x9932a4: r4 = false
    //     0x9932a4: add             x4, NULL, #0x30  ; false
    // 0x9932a8: b               #0x9932b0
    // 0x9932ac: r4 = true
    //     0x9932ac: add             x4, NULL, #0x20  ; true
    // 0x9932b0: cmp             w4, NULL
    // 0x9932b4: b.ne            #0x9932c0
    // 0x9932b8: r8 = false
    //     0x9932b8: add             x8, NULL, #0x30  ; false
    // 0x9932bc: b               #0x9932c4
    // 0x9932c0: mov             x8, x4
    // 0x9932c4: ldur            x4, [fp, #-0x18]
    // 0x9932c8: ldur            x6, [fp, #-0x28]
    // 0x9932cc: ldur            x5, [fp, #-0x30]
    // 0x9932d0: ldur            x7, [fp, #-0x10]
    // 0x9932d4: stur            x8, [fp, #-0x20]
    // 0x9932d8: r16 = <EdgeInsets>
    //     0x9932d8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x9932dc: ldr             x16, [x16, #0xda0]
    // 0x9932e0: r30 = Instance_EdgeInsets
    //     0x9932e0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x9932e4: ldr             lr, [lr, #0x1f0]
    // 0x9932e8: stp             lr, x16, [SP]
    // 0x9932ec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9932ec: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9932f0: r0 = all()
    //     0x9932f0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x9932f4: ldur            x2, [fp, #-0x18]
    // 0x9932f8: stur            x0, [fp, #-0x40]
    // 0x9932fc: LoadField: r1 = r2->field_13
    //     0x9932fc: ldur            w1, [x2, #0x13]
    // 0x993300: DecompressPointer r1
    //     0x993300: add             x1, x1, HEAP, lsl #32
    // 0x993304: r0 = of()
    //     0x993304: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x993308: LoadField: r1 = r0->field_5b
    //     0x993308: ldur            w1, [x0, #0x5b]
    // 0x99330c: DecompressPointer r1
    //     0x99330c: add             x1, x1, HEAP, lsl #32
    // 0x993310: r16 = <Color>
    //     0x993310: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x993314: ldr             x16, [x16, #0xf80]
    // 0x993318: stp             x1, x16, [SP]
    // 0x99331c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x99331c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x993320: r0 = all()
    //     0x993320: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x993324: mov             x1, x0
    // 0x993328: ldur            x0, [fp, #-8]
    // 0x99332c: stur            x1, [fp, #-0x50]
    // 0x993330: LoadField: r2 = r0->field_b
    //     0x993330: ldur            w2, [x0, #0xb]
    // 0x993334: DecompressPointer r2
    //     0x993334: add             x2, x2, HEAP, lsl #32
    // 0x993338: cmp             w2, NULL
    // 0x99333c: b.eq            #0x993790
    // 0x993340: LoadField: r3 = r2->field_1f
    //     0x993340: ldur            w3, [x2, #0x1f]
    // 0x993344: DecompressPointer r3
    //     0x993344: add             x3, x3, HEAP, lsl #32
    // 0x993348: stur            x3, [fp, #-0x48]
    // 0x99334c: r0 = RoundedRectangleBorder()
    //     0x99334c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x993350: mov             x1, x0
    // 0x993354: ldur            x0, [fp, #-0x48]
    // 0x993358: StoreField: r1->field_b = r0
    //     0x993358: stur            w0, [x1, #0xb]
    // 0x99335c: r0 = Instance_BorderSide
    //     0x99335c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x993360: ldr             x0, [x0, #0xe20]
    // 0x993364: StoreField: r1->field_7 = r0
    //     0x993364: stur            w0, [x1, #7]
    // 0x993368: r16 = <RoundedRectangleBorder>
    //     0x993368: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x99336c: ldr             x16, [x16, #0xf78]
    // 0x993370: stp             x1, x16, [SP]
    // 0x993374: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x993374: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x993378: r0 = all()
    //     0x993378: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x99337c: stur            x0, [fp, #-0x48]
    // 0x993380: r0 = ButtonStyle()
    //     0x993380: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x993384: mov             x1, x0
    // 0x993388: ldur            x0, [fp, #-0x50]
    // 0x99338c: stur            x1, [fp, #-0x60]
    // 0x993390: StoreField: r1->field_b = r0
    //     0x993390: stur            w0, [x1, #0xb]
    // 0x993394: ldur            x0, [fp, #-0x40]
    // 0x993398: StoreField: r1->field_23 = r0
    //     0x993398: stur            w0, [x1, #0x23]
    // 0x99339c: ldur            x0, [fp, #-0x48]
    // 0x9933a0: StoreField: r1->field_43 = r0
    //     0x9933a0: stur            w0, [x1, #0x43]
    // 0x9933a4: r0 = TextButtonThemeData()
    //     0x9933a4: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x9933a8: mov             x2, x0
    // 0x9933ac: ldur            x0, [fp, #-0x60]
    // 0x9933b0: stur            x2, [fp, #-0x40]
    // 0x9933b4: StoreField: r2->field_7 = r0
    //     0x9933b4: stur            w0, [x2, #7]
    // 0x9933b8: ldur            x0, [fp, #-8]
    // 0x9933bc: LoadField: r1 = r0->field_b
    //     0x9933bc: ldur            w1, [x0, #0xb]
    // 0x9933c0: DecompressPointer r1
    //     0x9933c0: add             x1, x1, HEAP, lsl #32
    // 0x9933c4: cmp             w1, NULL
    // 0x9933c8: b.eq            #0x993794
    // 0x9933cc: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9933cc: ldur            w0, [x1, #0x17]
    // 0x9933d0: DecompressPointer r0
    //     0x9933d0: add             x0, x0, HEAP, lsl #32
    // 0x9933d4: ldur            x3, [fp, #-0x18]
    // 0x9933d8: stur            x0, [fp, #-8]
    // 0x9933dc: LoadField: r1 = r3->field_13
    //     0x9933dc: ldur            w1, [x3, #0x13]
    // 0x9933e0: DecompressPointer r1
    //     0x9933e0: add             x1, x1, HEAP, lsl #32
    // 0x9933e4: r0 = of()
    //     0x9933e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9933e8: LoadField: r1 = r0->field_87
    //     0x9933e8: ldur            w1, [x0, #0x87]
    // 0x9933ec: DecompressPointer r1
    //     0x9933ec: add             x1, x1, HEAP, lsl #32
    // 0x9933f0: LoadField: r0 = r1->field_7
    //     0x9933f0: ldur            w0, [x1, #7]
    // 0x9933f4: DecompressPointer r0
    //     0x9933f4: add             x0, x0, HEAP, lsl #32
    // 0x9933f8: r16 = 14.000000
    //     0x9933f8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x9933fc: ldr             x16, [x16, #0x1d8]
    // 0x993400: r30 = Instance_Color
    //     0x993400: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x993404: stp             lr, x16, [SP]
    // 0x993408: mov             x1, x0
    // 0x99340c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x99340c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x993410: ldr             x4, [x4, #0xaa0]
    // 0x993414: r0 = copyWith()
    //     0x993414: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x993418: stur            x0, [fp, #-0x48]
    // 0x99341c: r0 = Text()
    //     0x99341c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x993420: mov             x3, x0
    // 0x993424: ldur            x0, [fp, #-8]
    // 0x993428: stur            x3, [fp, #-0x50]
    // 0x99342c: StoreField: r3->field_b = r0
    //     0x99342c: stur            w0, [x3, #0xb]
    // 0x993430: ldur            x0, [fp, #-0x48]
    // 0x993434: StoreField: r3->field_13 = r0
    //     0x993434: stur            w0, [x3, #0x13]
    // 0x993438: ldur            x2, [fp, #-0x18]
    // 0x99343c: r1 = Function '<anonymous closure>':.
    //     0x99343c: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bfe0] AnonymousClosure: (0x99769c), in [package:customer_app/app/presentation/custom_widgets/orders/cancel_order_confirm_bottom_sheet.dart] _CancelOrderConfirmBottomSheetState::build (0x991580)
    //     0x993440: ldr             x1, [x1, #0xfe0]
    // 0x993444: r0 = AllocateClosure()
    //     0x993444: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x993448: stur            x0, [fp, #-8]
    // 0x99344c: r0 = TextButton()
    //     0x99344c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x993450: mov             x1, x0
    // 0x993454: ldur            x0, [fp, #-8]
    // 0x993458: stur            x1, [fp, #-0x18]
    // 0x99345c: StoreField: r1->field_b = r0
    //     0x99345c: stur            w0, [x1, #0xb]
    // 0x993460: r0 = false
    //     0x993460: add             x0, NULL, #0x30  ; false
    // 0x993464: StoreField: r1->field_27 = r0
    //     0x993464: stur            w0, [x1, #0x27]
    // 0x993468: r2 = true
    //     0x993468: add             x2, NULL, #0x20  ; true
    // 0x99346c: StoreField: r1->field_2f = r2
    //     0x99346c: stur            w2, [x1, #0x2f]
    // 0x993470: ldur            x2, [fp, #-0x50]
    // 0x993474: StoreField: r1->field_37 = r2
    //     0x993474: stur            w2, [x1, #0x37]
    // 0x993478: r0 = TextButtonTheme()
    //     0x993478: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x99347c: mov             x2, x0
    // 0x993480: ldur            x0, [fp, #-0x40]
    // 0x993484: stur            x2, [fp, #-8]
    // 0x993488: StoreField: r2->field_f = r0
    //     0x993488: stur            w0, [x2, #0xf]
    // 0x99348c: ldur            x0, [fp, #-0x18]
    // 0x993490: StoreField: r2->field_b = r0
    //     0x993490: stur            w0, [x2, #0xb]
    // 0x993494: r1 = <FlexParentData>
    //     0x993494: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x993498: ldr             x1, [x1, #0xe00]
    // 0x99349c: r0 = Expanded()
    //     0x99349c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x9934a0: mov             x1, x0
    // 0x9934a4: r0 = 1
    //     0x9934a4: movz            x0, #0x1
    // 0x9934a8: stur            x1, [fp, #-0x18]
    // 0x9934ac: StoreField: r1->field_13 = r0
    //     0x9934ac: stur            x0, [x1, #0x13]
    // 0x9934b0: r0 = Instance_FlexFit
    //     0x9934b0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x9934b4: ldr             x0, [x0, #0xe08]
    // 0x9934b8: StoreField: r1->field_1b = r0
    //     0x9934b8: stur            w0, [x1, #0x1b]
    // 0x9934bc: ldur            x0, [fp, #-8]
    // 0x9934c0: StoreField: r1->field_b = r0
    //     0x9934c0: stur            w0, [x1, #0xb]
    // 0x9934c4: r0 = Visibility()
    //     0x9934c4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x9934c8: mov             x3, x0
    // 0x9934cc: ldur            x0, [fp, #-0x18]
    // 0x9934d0: stur            x3, [fp, #-8]
    // 0x9934d4: StoreField: r3->field_b = r0
    //     0x9934d4: stur            w0, [x3, #0xb]
    // 0x9934d8: r0 = Instance_SizedBox
    //     0x9934d8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x9934dc: StoreField: r3->field_f = r0
    //     0x9934dc: stur            w0, [x3, #0xf]
    // 0x9934e0: ldur            x0, [fp, #-0x20]
    // 0x9934e4: StoreField: r3->field_13 = r0
    //     0x9934e4: stur            w0, [x3, #0x13]
    // 0x9934e8: r0 = false
    //     0x9934e8: add             x0, NULL, #0x30  ; false
    // 0x9934ec: ArrayStore: r3[0] = r0  ; List_4
    //     0x9934ec: stur            w0, [x3, #0x17]
    // 0x9934f0: StoreField: r3->field_1b = r0
    //     0x9934f0: stur            w0, [x3, #0x1b]
    // 0x9934f4: StoreField: r3->field_1f = r0
    //     0x9934f4: stur            w0, [x3, #0x1f]
    // 0x9934f8: StoreField: r3->field_23 = r0
    //     0x9934f8: stur            w0, [x3, #0x23]
    // 0x9934fc: StoreField: r3->field_27 = r0
    //     0x9934fc: stur            w0, [x3, #0x27]
    // 0x993500: StoreField: r3->field_2b = r0
    //     0x993500: stur            w0, [x3, #0x2b]
    // 0x993504: r1 = Null
    //     0x993504: mov             x1, NULL
    // 0x993508: r2 = 10
    //     0x993508: movz            x2, #0xa
    // 0x99350c: r0 = AllocateArray()
    //     0x99350c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x993510: mov             x2, x0
    // 0x993514: ldur            x0, [fp, #-0x28]
    // 0x993518: stur            x2, [fp, #-0x18]
    // 0x99351c: StoreField: r2->field_f = r0
    //     0x99351c: stur            w0, [x2, #0xf]
    // 0x993520: r16 = Instance_SizedBox
    //     0x993520: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x993524: ldr             x16, [x16, #0xb20]
    // 0x993528: StoreField: r2->field_13 = r16
    //     0x993528: stur            w16, [x2, #0x13]
    // 0x99352c: ldur            x0, [fp, #-0x30]
    // 0x993530: ArrayStore: r2[0] = r0  ; List_4
    //     0x993530: stur            w0, [x2, #0x17]
    // 0x993534: ldur            x0, [fp, #-0x38]
    // 0x993538: StoreField: r2->field_1b = r0
    //     0x993538: stur            w0, [x2, #0x1b]
    // 0x99353c: ldur            x0, [fp, #-8]
    // 0x993540: StoreField: r2->field_1f = r0
    //     0x993540: stur            w0, [x2, #0x1f]
    // 0x993544: r1 = <Widget>
    //     0x993544: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x993548: r0 = AllocateGrowableArray()
    //     0x993548: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x99354c: mov             x1, x0
    // 0x993550: ldur            x0, [fp, #-0x18]
    // 0x993554: stur            x1, [fp, #-8]
    // 0x993558: StoreField: r1->field_f = r0
    //     0x993558: stur            w0, [x1, #0xf]
    // 0x99355c: r0 = 10
    //     0x99355c: movz            x0, #0xa
    // 0x993560: StoreField: r1->field_b = r0
    //     0x993560: stur            w0, [x1, #0xb]
    // 0x993564: r0 = Row()
    //     0x993564: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x993568: mov             x2, x0
    // 0x99356c: r0 = Instance_Axis
    //     0x99356c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x993570: stur            x2, [fp, #-0x18]
    // 0x993574: StoreField: r2->field_f = r0
    //     0x993574: stur            w0, [x2, #0xf]
    // 0x993578: r0 = Instance_MainAxisAlignment
    //     0x993578: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x99357c: ldr             x0, [x0, #0xa8]
    // 0x993580: StoreField: r2->field_13 = r0
    //     0x993580: stur            w0, [x2, #0x13]
    // 0x993584: r0 = Instance_MainAxisSize
    //     0x993584: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x993588: ldr             x0, [x0, #0xa10]
    // 0x99358c: ArrayStore: r2[0] = r0  ; List_4
    //     0x99358c: stur            w0, [x2, #0x17]
    // 0x993590: r3 = Instance_CrossAxisAlignment
    //     0x993590: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x993594: ldr             x3, [x3, #0x890]
    // 0x993598: StoreField: r2->field_1b = r3
    //     0x993598: stur            w3, [x2, #0x1b]
    // 0x99359c: r4 = Instance_VerticalDirection
    //     0x99359c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9935a0: ldr             x4, [x4, #0xa20]
    // 0x9935a4: StoreField: r2->field_23 = r4
    //     0x9935a4: stur            w4, [x2, #0x23]
    // 0x9935a8: r5 = Instance_Clip
    //     0x9935a8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9935ac: ldr             x5, [x5, #0x38]
    // 0x9935b0: StoreField: r2->field_2b = r5
    //     0x9935b0: stur            w5, [x2, #0x2b]
    // 0x9935b4: StoreField: r2->field_2f = rZR
    //     0x9935b4: stur            xzr, [x2, #0x2f]
    // 0x9935b8: ldur            x1, [fp, #-8]
    // 0x9935bc: StoreField: r2->field_b = r1
    //     0x9935bc: stur            w1, [x2, #0xb]
    // 0x9935c0: ldur            x6, [fp, #-0x10]
    // 0x9935c4: LoadField: r1 = r6->field_b
    //     0x9935c4: ldur            w1, [x6, #0xb]
    // 0x9935c8: LoadField: r7 = r6->field_f
    //     0x9935c8: ldur            w7, [x6, #0xf]
    // 0x9935cc: DecompressPointer r7
    //     0x9935cc: add             x7, x7, HEAP, lsl #32
    // 0x9935d0: LoadField: r8 = r7->field_b
    //     0x9935d0: ldur            w8, [x7, #0xb]
    // 0x9935d4: r7 = LoadInt32Instr(r1)
    //     0x9935d4: sbfx            x7, x1, #1, #0x1f
    // 0x9935d8: stur            x7, [fp, #-0x58]
    // 0x9935dc: r1 = LoadInt32Instr(r8)
    //     0x9935dc: sbfx            x1, x8, #1, #0x1f
    // 0x9935e0: cmp             x7, x1
    // 0x9935e4: b.ne            #0x9935f0
    // 0x9935e8: mov             x1, x6
    // 0x9935ec: r0 = _growToNextCapacity()
    //     0x9935ec: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9935f0: ldur            x2, [fp, #-0x10]
    // 0x9935f4: ldur            x3, [fp, #-0x58]
    // 0x9935f8: add             x0, x3, #1
    // 0x9935fc: lsl             x1, x0, #1
    // 0x993600: StoreField: r2->field_b = r1
    //     0x993600: stur            w1, [x2, #0xb]
    // 0x993604: LoadField: r1 = r2->field_f
    //     0x993604: ldur            w1, [x2, #0xf]
    // 0x993608: DecompressPointer r1
    //     0x993608: add             x1, x1, HEAP, lsl #32
    // 0x99360c: ldur            x0, [fp, #-0x18]
    // 0x993610: ArrayStore: r1[r3] = r0  ; List_4
    //     0x993610: add             x25, x1, x3, lsl #2
    //     0x993614: add             x25, x25, #0xf
    //     0x993618: str             w0, [x25]
    //     0x99361c: tbz             w0, #0, #0x993638
    //     0x993620: ldurb           w16, [x1, #-1]
    //     0x993624: ldurb           w17, [x0, #-1]
    //     0x993628: and             x16, x17, x16, lsr #2
    //     0x99362c: tst             x16, HEAP, lsr #32
    //     0x993630: b.eq            #0x993638
    //     0x993634: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x993638: r0 = Column()
    //     0x993638: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x99363c: mov             x1, x0
    // 0x993640: r0 = Instance_Axis
    //     0x993640: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x993644: stur            x1, [fp, #-8]
    // 0x993648: StoreField: r1->field_f = r0
    //     0x993648: stur            w0, [x1, #0xf]
    // 0x99364c: r2 = Instance_MainAxisAlignment
    //     0x99364c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x993650: ldr             x2, [x2, #0xa08]
    // 0x993654: StoreField: r1->field_13 = r2
    //     0x993654: stur            w2, [x1, #0x13]
    // 0x993658: r2 = Instance_MainAxisSize
    //     0x993658: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x99365c: ldr             x2, [x2, #0xa10]
    // 0x993660: ArrayStore: r1[0] = r2  ; List_4
    //     0x993660: stur            w2, [x1, #0x17]
    // 0x993664: r2 = Instance_CrossAxisAlignment
    //     0x993664: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x993668: ldr             x2, [x2, #0x890]
    // 0x99366c: StoreField: r1->field_1b = r2
    //     0x99366c: stur            w2, [x1, #0x1b]
    // 0x993670: r2 = Instance_VerticalDirection
    //     0x993670: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x993674: ldr             x2, [x2, #0xa20]
    // 0x993678: StoreField: r1->field_23 = r2
    //     0x993678: stur            w2, [x1, #0x23]
    // 0x99367c: r2 = Instance_Clip
    //     0x99367c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x993680: ldr             x2, [x2, #0x38]
    // 0x993684: StoreField: r1->field_2b = r2
    //     0x993684: stur            w2, [x1, #0x2b]
    // 0x993688: StoreField: r1->field_2f = rZR
    //     0x993688: stur            xzr, [x1, #0x2f]
    // 0x99368c: ldur            x2, [fp, #-0x10]
    // 0x993690: StoreField: r1->field_b = r2
    //     0x993690: stur            w2, [x1, #0xb]
    // 0x993694: r0 = Padding()
    //     0x993694: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x993698: mov             x1, x0
    // 0x99369c: r0 = Instance_EdgeInsets
    //     0x99369c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x9936a0: ldr             x0, [x0, #0x1f0]
    // 0x9936a4: stur            x1, [fp, #-0x10]
    // 0x9936a8: StoreField: r1->field_f = r0
    //     0x9936a8: stur            w0, [x1, #0xf]
    // 0x9936ac: ldur            x0, [fp, #-8]
    // 0x9936b0: StoreField: r1->field_b = r0
    //     0x9936b0: stur            w0, [x1, #0xb]
    // 0x9936b4: r0 = SingleChildScrollView()
    //     0x9936b4: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x9936b8: r1 = Instance_Axis
    //     0x9936b8: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9936bc: StoreField: r0->field_b = r1
    //     0x9936bc: stur            w1, [x0, #0xb]
    // 0x9936c0: r1 = false
    //     0x9936c0: add             x1, NULL, #0x30  ; false
    // 0x9936c4: StoreField: r0->field_f = r1
    //     0x9936c4: stur            w1, [x0, #0xf]
    // 0x9936c8: ldur            x1, [fp, #-0x10]
    // 0x9936cc: StoreField: r0->field_23 = r1
    //     0x9936cc: stur            w1, [x0, #0x23]
    // 0x9936d0: r1 = Instance_DragStartBehavior
    //     0x9936d0: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x9936d4: StoreField: r0->field_27 = r1
    //     0x9936d4: stur            w1, [x0, #0x27]
    // 0x9936d8: r1 = Instance_Clip
    //     0x9936d8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x9936dc: ldr             x1, [x1, #0x7e0]
    // 0x9936e0: StoreField: r0->field_2b = r1
    //     0x9936e0: stur            w1, [x0, #0x2b]
    // 0x9936e4: r1 = Instance_HitTestBehavior
    //     0x9936e4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x9936e8: ldr             x1, [x1, #0x288]
    // 0x9936ec: StoreField: r0->field_2f = r1
    //     0x9936ec: stur            w1, [x0, #0x2f]
    // 0x9936f0: LeaveFrame
    //     0x9936f0: mov             SP, fp
    //     0x9936f4: ldp             fp, lr, [SP], #0x10
    // 0x9936f8: ret
    //     0x9936f8: ret             
    // 0x9936fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9936fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x993700: b               #0x9915a0
    // 0x993704: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993704: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993708: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993708: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99370c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99370c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993710: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993710: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993714: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993714: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993718: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993718: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99371c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99371c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993720: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993720: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993724: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993724: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993728: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x993728: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x99372c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99372c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993730: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993730: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993734: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x993734: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x993738: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993738: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99373c: SaveReg d0
    //     0x99373c: str             q0, [SP, #-0x10]!
    // 0x993740: stp             x4, x5, [SP, #-0x10]!
    // 0x993744: stp             x2, x3, [SP, #-0x10]!
    // 0x993748: stp             x0, x1, [SP, #-0x10]!
    // 0x99374c: r0 = AllocateDouble()
    //     0x99374c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x993750: mov             x6, x0
    // 0x993754: ldp             x0, x1, [SP], #0x10
    // 0x993758: ldp             x2, x3, [SP], #0x10
    // 0x99375c: ldp             x4, x5, [SP], #0x10
    // 0x993760: RestoreReg d0
    //     0x993760: ldr             q0, [SP], #0x10
    // 0x993764: b               #0x992878
    // 0x993768: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993768: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99376c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99376c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993770: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993770: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993774: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993774: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993778: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993778: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99377c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99377c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993780: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993780: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993784: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993784: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993788: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993788: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99378c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99378c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993790: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993790: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x993794: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x993794: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x99769c, size: 0xa8
    // 0x99769c: EnterFrame
    //     0x99769c: stp             fp, lr, [SP, #-0x10]!
    //     0x9976a0: mov             fp, SP
    // 0x9976a4: AllocStack(0x18)
    //     0x9976a4: sub             SP, SP, #0x18
    // 0x9976a8: SetupParameters()
    //     0x9976a8: ldr             x0, [fp, #0x10]
    //     0x9976ac: ldur            w1, [x0, #0x17]
    //     0x9976b0: add             x1, x1, HEAP, lsl #32
    // 0x9976b4: CheckStackOverflow
    //     0x9976b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9976b8: cmp             SP, x16
    //     0x9976bc: b.ls            #0x997738
    // 0x9976c0: LoadField: r0 = r1->field_f
    //     0x9976c0: ldur            w0, [x1, #0xf]
    // 0x9976c4: DecompressPointer r0
    //     0x9976c4: add             x0, x0, HEAP, lsl #32
    // 0x9976c8: LoadField: r1 = r0->field_b
    //     0x9976c8: ldur            w1, [x0, #0xb]
    // 0x9976cc: DecompressPointer r1
    //     0x9976cc: add             x1, x1, HEAP, lsl #32
    // 0x9976d0: cmp             w1, NULL
    // 0x9976d4: b.eq            #0x997740
    // 0x9976d8: LoadField: r0 = r1->field_1b
    //     0x9976d8: ldur            w0, [x1, #0x1b]
    // 0x9976dc: DecompressPointer r0
    //     0x9976dc: add             x0, x0, HEAP, lsl #32
    // 0x9976e0: stp             NULL, x0, [SP, #8]
    // 0x9976e4: str             NULL, [SP]
    // 0x9976e8: r4 = 0
    //     0x9976e8: movz            x4, #0
    // 0x9976ec: ldr             x0, [SP, #0x10]
    // 0x9976f0: r16 = UnlinkedCall_0x613b5c
    //     0x9976f0: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5bfe8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9976f4: add             x16, x16, #0xfe8
    // 0x9976f8: ldp             x5, lr, [x16]
    // 0x9976fc: blr             lr
    // 0x997700: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x997700: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x997704: ldr             x0, [x0, #0x1c80]
    //     0x997708: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x99770c: cmp             w0, w16
    //     0x997710: b.ne            #0x99771c
    //     0x997714: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x997718: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x99771c: str             NULL, [SP]
    // 0x997720: r4 = const [0x1, 0, 0, 0, null]
    //     0x997720: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x997724: r0 = GetNavigation.back()
    //     0x997724: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x997728: r0 = Null
    //     0x997728: mov             x0, NULL
    // 0x99772c: LeaveFrame
    //     0x99772c: mov             SP, fp
    //     0x997730: ldp             fp, lr, [SP], #0x10
    // 0x997734: ret
    //     0x997734: ret             
    // 0x997738: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x997738: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99773c: b               #0x9976c0
    // 0x997740: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997740: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x997744, size: 0x180
    // 0x997744: EnterFrame
    //     0x997744: stp             fp, lr, [SP, #-0x10]!
    //     0x997748: mov             fp, SP
    // 0x99774c: AllocStack(0x18)
    //     0x99774c: sub             SP, SP, #0x18
    // 0x997750: SetupParameters()
    //     0x997750: ldr             x0, [fp, #0x10]
    //     0x997754: ldur            w1, [x0, #0x17]
    //     0x997758: add             x1, x1, HEAP, lsl #32
    // 0x99775c: CheckStackOverflow
    //     0x99775c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x997760: cmp             SP, x16
    //     0x997764: b.ls            #0x9978b0
    // 0x997768: LoadField: r2 = r1->field_f
    //     0x997768: ldur            w2, [x1, #0xf]
    // 0x99776c: DecompressPointer r2
    //     0x99776c: add             x2, x2, HEAP, lsl #32
    // 0x997770: LoadField: r0 = r2->field_1b
    //     0x997770: ldur            w0, [x2, #0x1b]
    // 0x997774: DecompressPointer r0
    //     0x997774: add             x0, x0, HEAP, lsl #32
    // 0x997778: cmp             w0, NULL
    // 0x99777c: b.eq            #0x997844
    // 0x997780: LoadField: r3 = r2->field_b
    //     0x997780: ldur            w3, [x2, #0xb]
    // 0x997784: DecompressPointer r3
    //     0x997784: add             x3, x3, HEAP, lsl #32
    // 0x997788: cmp             w3, NULL
    // 0x99778c: b.eq            #0x9978b8
    // 0x997790: LoadField: r1 = r3->field_b
    //     0x997790: ldur            w1, [x3, #0xb]
    // 0x997794: DecompressPointer r1
    //     0x997794: add             x1, x1, HEAP, lsl #32
    // 0x997798: cmp             w1, NULL
    // 0x99779c: b.ne            #0x9977a8
    // 0x9977a0: r0 = Null
    //     0x9977a0: mov             x0, NULL
    // 0x9977a4: b               #0x997800
    // 0x9977a8: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x9977a8: ldur            w4, [x1, #0x17]
    // 0x9977ac: DecompressPointer r4
    //     0x9977ac: add             x4, x4, HEAP, lsl #32
    // 0x9977b0: cmp             w4, NULL
    // 0x9977b4: b.ne            #0x9977c0
    // 0x9977b8: r0 = Null
    //     0x9977b8: mov             x0, NULL
    // 0x9977bc: b               #0x997800
    // 0x9977c0: r5 = LoadInt32Instr(r0)
    //     0x9977c0: sbfx            x5, x0, #1, #0x1f
    //     0x9977c4: tbz             w0, #0, #0x9977cc
    //     0x9977c8: ldur            x5, [x0, #7]
    // 0x9977cc: LoadField: r0 = r4->field_b
    //     0x9977cc: ldur            w0, [x4, #0xb]
    // 0x9977d0: r1 = LoadInt32Instr(r0)
    //     0x9977d0: sbfx            x1, x0, #1, #0x1f
    // 0x9977d4: mov             x0, x1
    // 0x9977d8: mov             x1, x5
    // 0x9977dc: cmp             x1, x0
    // 0x9977e0: b.hs            #0x9978bc
    // 0x9977e4: LoadField: r0 = r4->field_f
    //     0x9977e4: ldur            w0, [x4, #0xf]
    // 0x9977e8: DecompressPointer r0
    //     0x9977e8: add             x0, x0, HEAP, lsl #32
    // 0x9977ec: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x9977ec: add             x16, x0, x5, lsl #2
    //     0x9977f0: ldur            w1, [x16, #0xf]
    // 0x9977f4: DecompressPointer r1
    //     0x9977f4: add             x1, x1, HEAP, lsl #32
    // 0x9977f8: LoadField: r0 = r1->field_b
    //     0x9977f8: ldur            w0, [x1, #0xb]
    // 0x9977fc: DecompressPointer r0
    //     0x9977fc: add             x0, x0, HEAP, lsl #32
    // 0x997800: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x997800: ldur            w1, [x2, #0x17]
    // 0x997804: DecompressPointer r1
    //     0x997804: add             x1, x1, HEAP, lsl #32
    // 0x997808: LoadField: r2 = r1->field_27
    //     0x997808: ldur            w2, [x1, #0x27]
    // 0x99780c: DecompressPointer r2
    //     0x99780c: add             x2, x2, HEAP, lsl #32
    // 0x997810: LoadField: r1 = r2->field_7
    //     0x997810: ldur            w1, [x2, #7]
    // 0x997814: DecompressPointer r1
    //     0x997814: add             x1, x1, HEAP, lsl #32
    // 0x997818: LoadField: r2 = r3->field_1b
    //     0x997818: ldur            w2, [x3, #0x1b]
    // 0x99781c: DecompressPointer r2
    //     0x99781c: add             x2, x2, HEAP, lsl #32
    // 0x997820: stp             x0, x2, [SP, #8]
    // 0x997824: str             x1, [SP]
    // 0x997828: r4 = 0
    //     0x997828: movz            x4, #0
    // 0x99782c: ldr             x0, [SP, #0x10]
    // 0x997830: r16 = UnlinkedCall_0x613b5c
    //     0x997830: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5bff8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x997834: add             x16, x16, #0xff8
    // 0x997838: ldp             x5, lr, [x16]
    // 0x99783c: blr             lr
    // 0x997840: b               #0x997878
    // 0x997844: LoadField: r0 = r2->field_b
    //     0x997844: ldur            w0, [x2, #0xb]
    // 0x997848: DecompressPointer r0
    //     0x997848: add             x0, x0, HEAP, lsl #32
    // 0x99784c: cmp             w0, NULL
    // 0x997850: b.eq            #0x9978c0
    // 0x997854: LoadField: r1 = r0->field_1b
    //     0x997854: ldur            w1, [x0, #0x1b]
    // 0x997858: DecompressPointer r1
    //     0x997858: add             x1, x1, HEAP, lsl #32
    // 0x99785c: stp             NULL, x1, [SP, #8]
    // 0x997860: str             NULL, [SP]
    // 0x997864: r4 = 0
    //     0x997864: movz            x4, #0
    // 0x997868: ldr             x0, [SP, #0x10]
    // 0x99786c: r5 = UnlinkedCall_0x613b5c
    //     0x99786c: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c008] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x997870: ldp             x5, lr, [x16, #8]
    // 0x997874: blr             lr
    // 0x997878: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x997878: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x99787c: ldr             x0, [x0, #0x1c80]
    //     0x997880: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x997884: cmp             w0, w16
    //     0x997888: b.ne            #0x997894
    //     0x99788c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x997890: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x997894: str             NULL, [SP]
    // 0x997898: r4 = const [0x1, 0, 0, 0, null]
    //     0x997898: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x99789c: r0 = GetNavigation.back()
    //     0x99789c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x9978a0: r0 = Null
    //     0x9978a0: mov             x0, NULL
    // 0x9978a4: LeaveFrame
    //     0x9978a4: mov             SP, fp
    //     0x9978a8: ldp             fp, lr, [SP], #0x10
    // 0x9978ac: ret
    //     0x9978ac: ret             
    // 0x9978b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9978b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9978b4: b               #0x997768
    // 0x9978b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9978b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9978bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9978bc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9978c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9978c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RadioListTile<int> <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x997948, size: 0x1d4
    // 0x997948: EnterFrame
    //     0x997948: stp             fp, lr, [SP, #-0x10]!
    //     0x99794c: mov             fp, SP
    // 0x997950: AllocStack(0x30)
    //     0x997950: sub             SP, SP, #0x30
    // 0x997954: SetupParameters()
    //     0x997954: ldr             x0, [fp, #0x20]
    //     0x997958: ldur            w2, [x0, #0x17]
    //     0x99795c: add             x2, x2, HEAP, lsl #32
    //     0x997960: stur            x2, [fp, #-0x10]
    // 0x997964: CheckStackOverflow
    //     0x997964: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x997968: cmp             SP, x16
    //     0x99796c: b.ls            #0x997b0c
    // 0x997970: LoadField: r0 = r2->field_f
    //     0x997970: ldur            w0, [x2, #0xf]
    // 0x997974: DecompressPointer r0
    //     0x997974: add             x0, x0, HEAP, lsl #32
    // 0x997978: LoadField: r1 = r0->field_b
    //     0x997978: ldur            w1, [x0, #0xb]
    // 0x99797c: DecompressPointer r1
    //     0x99797c: add             x1, x1, HEAP, lsl #32
    // 0x997980: cmp             w1, NULL
    // 0x997984: b.eq            #0x997b14
    // 0x997988: LoadField: r0 = r1->field_b
    //     0x997988: ldur            w0, [x1, #0xb]
    // 0x99798c: DecompressPointer r0
    //     0x99798c: add             x0, x0, HEAP, lsl #32
    // 0x997990: cmp             w0, NULL
    // 0x997994: b.ne            #0x9979a4
    // 0x997998: ldr             x4, [fp, #0x10]
    // 0x99799c: r0 = Null
    //     0x99799c: mov             x0, NULL
    // 0x9979a0: b               #0x997a04
    // 0x9979a4: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x9979a4: ldur            w3, [x0, #0x17]
    // 0x9979a8: DecompressPointer r3
    //     0x9979a8: add             x3, x3, HEAP, lsl #32
    // 0x9979ac: cmp             w3, NULL
    // 0x9979b0: b.ne            #0x9979c0
    // 0x9979b4: ldr             x4, [fp, #0x10]
    // 0x9979b8: r0 = Null
    //     0x9979b8: mov             x0, NULL
    // 0x9979bc: b               #0x997a04
    // 0x9979c0: ldr             x4, [fp, #0x10]
    // 0x9979c4: LoadField: r0 = r3->field_b
    //     0x9979c4: ldur            w0, [x3, #0xb]
    // 0x9979c8: r5 = LoadInt32Instr(r4)
    //     0x9979c8: sbfx            x5, x4, #1, #0x1f
    //     0x9979cc: tbz             w4, #0, #0x9979d4
    //     0x9979d0: ldur            x5, [x4, #7]
    // 0x9979d4: r1 = LoadInt32Instr(r0)
    //     0x9979d4: sbfx            x1, x0, #1, #0x1f
    // 0x9979d8: mov             x0, x1
    // 0x9979dc: mov             x1, x5
    // 0x9979e0: cmp             x1, x0
    // 0x9979e4: b.hs            #0x997b18
    // 0x9979e8: LoadField: r0 = r3->field_f
    //     0x9979e8: ldur            w0, [x3, #0xf]
    // 0x9979ec: DecompressPointer r0
    //     0x9979ec: add             x0, x0, HEAP, lsl #32
    // 0x9979f0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x9979f0: add             x16, x0, x5, lsl #2
    //     0x9979f4: ldur            w1, [x16, #0xf]
    // 0x9979f8: DecompressPointer r1
    //     0x9979f8: add             x1, x1, HEAP, lsl #32
    // 0x9979fc: LoadField: r0 = r1->field_7
    //     0x9979fc: ldur            w0, [x1, #7]
    // 0x997a00: DecompressPointer r0
    //     0x997a00: add             x0, x0, HEAP, lsl #32
    // 0x997a04: cmp             w0, NULL
    // 0x997a08: b.ne            #0x997a10
    // 0x997a0c: r0 = ""
    //     0x997a0c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x997a10: ldr             x1, [fp, #0x18]
    // 0x997a14: stur            x0, [fp, #-8]
    // 0x997a18: r0 = of()
    //     0x997a18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x997a1c: LoadField: r1 = r0->field_87
    //     0x997a1c: ldur            w1, [x0, #0x87]
    // 0x997a20: DecompressPointer r1
    //     0x997a20: add             x1, x1, HEAP, lsl #32
    // 0x997a24: LoadField: r0 = r1->field_2b
    //     0x997a24: ldur            w0, [x1, #0x2b]
    // 0x997a28: DecompressPointer r0
    //     0x997a28: add             x0, x0, HEAP, lsl #32
    // 0x997a2c: r16 = 14.000000
    //     0x997a2c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x997a30: ldr             x16, [x16, #0x1d8]
    // 0x997a34: r30 = Instance_Color
    //     0x997a34: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x997a38: stp             lr, x16, [SP]
    // 0x997a3c: mov             x1, x0
    // 0x997a40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x997a40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x997a44: ldr             x4, [x4, #0xaa0]
    // 0x997a48: r0 = copyWith()
    //     0x997a48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x997a4c: stur            x0, [fp, #-0x18]
    // 0x997a50: r0 = Text()
    //     0x997a50: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x997a54: mov             x2, x0
    // 0x997a58: ldur            x0, [fp, #-8]
    // 0x997a5c: stur            x2, [fp, #-0x20]
    // 0x997a60: StoreField: r2->field_b = r0
    //     0x997a60: stur            w0, [x2, #0xb]
    // 0x997a64: ldur            x0, [fp, #-0x18]
    // 0x997a68: StoreField: r2->field_13 = r0
    //     0x997a68: stur            w0, [x2, #0x13]
    // 0x997a6c: ldur            x0, [fp, #-0x10]
    // 0x997a70: LoadField: r1 = r0->field_f
    //     0x997a70: ldur            w1, [x0, #0xf]
    // 0x997a74: DecompressPointer r1
    //     0x997a74: add             x1, x1, HEAP, lsl #32
    // 0x997a78: LoadField: r3 = r1->field_1b
    //     0x997a78: ldur            w3, [x1, #0x1b]
    // 0x997a7c: DecompressPointer r3
    //     0x997a7c: add             x3, x3, HEAP, lsl #32
    // 0x997a80: stur            x3, [fp, #-8]
    // 0x997a84: r1 = <int>
    //     0x997a84: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0x997a88: r0 = RadioListTile()
    //     0x997a88: bl              #0x997b1c  ; AllocateRadioListTileStub -> RadioListTile<X0> (size=0xa0)
    // 0x997a8c: mov             x3, x0
    // 0x997a90: ldr             x0, [fp, #0x10]
    // 0x997a94: stur            x3, [fp, #-0x18]
    // 0x997a98: StoreField: r3->field_f = r0
    //     0x997a98: stur            w0, [x3, #0xf]
    // 0x997a9c: ldur            x0, [fp, #-8]
    // 0x997aa0: StoreField: r3->field_13 = r0
    //     0x997aa0: stur            w0, [x3, #0x13]
    // 0x997aa4: ldur            x2, [fp, #-0x10]
    // 0x997aa8: r1 = Function '<anonymous closure>':.
    //     0x997aa8: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c020] AnonymousClosure: (0x997b28), in [package:customer_app/app/presentation/custom_widgets/orders/cancel_order_confirm_bottom_sheet.dart] _CancelOrderConfirmBottomSheetState::build (0x991580)
    //     0x997aac: ldr             x1, [x1, #0x20]
    // 0x997ab0: r0 = AllocateClosure()
    //     0x997ab0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x997ab4: mov             x1, x0
    // 0x997ab8: ldur            x0, [fp, #-0x18]
    // 0x997abc: ArrayStore: r0[0] = r1  ; List_4
    //     0x997abc: stur            w1, [x0, #0x17]
    // 0x997ac0: r1 = true
    //     0x997ac0: add             x1, NULL, #0x20  ; true
    // 0x997ac4: StoreField: r0->field_1f = r1
    //     0x997ac4: stur            w1, [x0, #0x1f]
    // 0x997ac8: ldur            x1, [fp, #-0x20]
    // 0x997acc: StoreField: r0->field_3b = r1
    //     0x997acc: stur            w1, [x0, #0x3b]
    // 0x997ad0: r1 = false
    //     0x997ad0: add             x1, NULL, #0x30  ; false
    // 0x997ad4: StoreField: r0->field_4f = r1
    //     0x997ad4: stur            w1, [x0, #0x4f]
    // 0x997ad8: StoreField: r0->field_57 = r1
    //     0x997ad8: stur            w1, [x0, #0x57]
    // 0x997adc: r2 = Instance_EdgeInsets
    //     0x997adc: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x997ae0: StoreField: r0->field_5b = r2
    //     0x997ae0: stur            w2, [x0, #0x5b]
    // 0x997ae4: d0 = 1.000000
    //     0x997ae4: fmov            d0, #1.00000000
    // 0x997ae8: StoreField: r0->field_8b = d0
    //     0x997ae8: stur            d0, [x0, #0x8b]
    // 0x997aec: StoreField: r0->field_83 = r1
    //     0x997aec: stur            w1, [x0, #0x83]
    // 0x997af0: r2 = Instance__RadioType
    //     0x997af0: add             x2, PP, #0x38, lsl #12  ; [pp+0x38050] Obj!_RadioType@d74141
    //     0x997af4: ldr             x2, [x2, #0x50]
    // 0x997af8: StoreField: r0->field_7b = r2
    //     0x997af8: stur            w2, [x0, #0x7b]
    // 0x997afc: StoreField: r0->field_87 = r1
    //     0x997afc: stur            w1, [x0, #0x87]
    // 0x997b00: LeaveFrame
    //     0x997b00: mov             SP, fp
    //     0x997b04: ldp             fp, lr, [SP], #0x10
    // 0x997b08: ret
    //     0x997b08: ret             
    // 0x997b0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x997b0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x997b10: b               #0x997970
    // 0x997b14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997b14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x997b18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x997b18: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int?) {
    // ** addr: 0x997b28, size: 0x84
    // 0x997b28: EnterFrame
    //     0x997b28: stp             fp, lr, [SP, #-0x10]!
    //     0x997b2c: mov             fp, SP
    // 0x997b30: AllocStack(0x10)
    //     0x997b30: sub             SP, SP, #0x10
    // 0x997b34: SetupParameters()
    //     0x997b34: ldr             x0, [fp, #0x18]
    //     0x997b38: ldur            w1, [x0, #0x17]
    //     0x997b3c: add             x1, x1, HEAP, lsl #32
    //     0x997b40: stur            x1, [fp, #-8]
    // 0x997b44: CheckStackOverflow
    //     0x997b44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x997b48: cmp             SP, x16
    //     0x997b4c: b.ls            #0x997ba4
    // 0x997b50: r1 = 1
    //     0x997b50: movz            x1, #0x1
    // 0x997b54: r0 = AllocateContext()
    //     0x997b54: bl              #0x16f6108  ; AllocateContextStub
    // 0x997b58: mov             x1, x0
    // 0x997b5c: ldur            x0, [fp, #-8]
    // 0x997b60: StoreField: r1->field_b = r0
    //     0x997b60: stur            w0, [x1, #0xb]
    // 0x997b64: ldr             x2, [fp, #0x10]
    // 0x997b68: StoreField: r1->field_f = r2
    //     0x997b68: stur            w2, [x1, #0xf]
    // 0x997b6c: LoadField: r3 = r0->field_f
    //     0x997b6c: ldur            w3, [x0, #0xf]
    // 0x997b70: DecompressPointer r3
    //     0x997b70: add             x3, x3, HEAP, lsl #32
    // 0x997b74: mov             x2, x1
    // 0x997b78: stur            x3, [fp, #-0x10]
    // 0x997b7c: r1 = Function '<anonymous closure>':.
    //     0x997b7c: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c028] AnonymousClosure: (0x997bac), in [package:customer_app/app/presentation/custom_widgets/orders/cancel_order_confirm_bottom_sheet.dart] _CancelOrderConfirmBottomSheetState::build (0x991580)
    //     0x997b80: ldr             x1, [x1, #0x28]
    // 0x997b84: r0 = AllocateClosure()
    //     0x997b84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x997b88: ldur            x1, [fp, #-0x10]
    // 0x997b8c: mov             x2, x0
    // 0x997b90: r0 = setState()
    //     0x997b90: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x997b94: r0 = Null
    //     0x997b94: mov             x0, NULL
    // 0x997b98: LeaveFrame
    //     0x997b98: mov             SP, fp
    //     0x997b9c: ldp             fp, lr, [SP], #0x10
    // 0x997ba0: ret
    //     0x997ba0: ret             
    // 0x997ba4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x997ba4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x997ba8: b               #0x997b50
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x997bac, size: 0x54
    // 0x997bac: ldr             x1, [SP]
    // 0x997bb0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x997bb0: ldur            w2, [x1, #0x17]
    // 0x997bb4: DecompressPointer r2
    //     0x997bb4: add             x2, x2, HEAP, lsl #32
    // 0x997bb8: LoadField: r1 = r2->field_b
    //     0x997bb8: ldur            w1, [x2, #0xb]
    // 0x997bbc: DecompressPointer r1
    //     0x997bbc: add             x1, x1, HEAP, lsl #32
    // 0x997bc0: LoadField: r3 = r1->field_f
    //     0x997bc0: ldur            w3, [x1, #0xf]
    // 0x997bc4: DecompressPointer r3
    //     0x997bc4: add             x3, x3, HEAP, lsl #32
    // 0x997bc8: LoadField: r0 = r2->field_f
    //     0x997bc8: ldur            w0, [x2, #0xf]
    // 0x997bcc: DecompressPointer r0
    //     0x997bcc: add             x0, x0, HEAP, lsl #32
    // 0x997bd0: StoreField: r3->field_1b = r0
    //     0x997bd0: stur            w0, [x3, #0x1b]
    //     0x997bd4: tbz             w0, #0, #0x997bf8
    //     0x997bd8: ldurb           w16, [x3, #-1]
    //     0x997bdc: ldurb           w17, [x0, #-1]
    //     0x997be0: and             x16, x17, x16, lsr #2
    //     0x997be4: tst             x16, HEAP, lsr #32
    //     0x997be8: b.eq            #0x997bf8
    //     0x997bec: str             lr, [SP, #-8]!
    //     0x997bf0: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    //     0x997bf4: ldr             lr, [SP], #8
    // 0x997bf8: r0 = Null
    //     0x997bf8: mov             x0, NULL
    // 0x997bfc: ret
    //     0x997bfc: ret             
  }
  _ _CancelOrderConfirmBottomSheetState(/* No info */) {
    // ** addr: 0xc79a90, size: 0xf4
    // 0xc79a90: EnterFrame
    //     0xc79a90: stp             fp, lr, [SP, #-0x10]!
    //     0xc79a94: mov             fp, SP
    // 0xc79a98: AllocStack(0x10)
    //     0xc79a98: sub             SP, SP, #0x10
    // 0xc79a9c: SetupParameters(_CancelOrderConfirmBottomSheetState this /* r1 => r0, fp-0x8 */)
    //     0xc79a9c: mov             x0, x1
    //     0xc79aa0: stur            x1, [fp, #-8]
    // 0xc79aa4: CheckStackOverflow
    //     0xc79aa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc79aa8: cmp             SP, x16
    //     0xc79aac: b.ls            #0xc79b7c
    // 0xc79ab0: r1 = <FormState>
    //     0xc79ab0: add             x1, PP, #0xd, lsl #12  ; [pp+0xdad8] TypeArguments: <FormState>
    //     0xc79ab4: ldr             x1, [x1, #0xad8]
    // 0xc79ab8: r0 = LabeledGlobalKey()
    //     0xc79ab8: bl              #0x689b40  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xc79abc: ldur            x2, [fp, #-8]
    // 0xc79ac0: StoreField: r2->field_13 = r0
    //     0xc79ac0: stur            w0, [x2, #0x13]
    //     0xc79ac4: ldurb           w16, [x2, #-1]
    //     0xc79ac8: ldurb           w17, [x0, #-1]
    //     0xc79acc: and             x16, x17, x16, lsr #2
    //     0xc79ad0: tst             x16, HEAP, lsr #32
    //     0xc79ad4: b.eq            #0xc79adc
    //     0xc79ad8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xc79adc: r1 = <TextEditingValue>
    //     0xc79adc: ldr             x1, [PP, #0x6c80]  ; [pp+0x6c80] TypeArguments: <TextEditingValue>
    // 0xc79ae0: r0 = TextEditingController()
    //     0xc79ae0: bl              #0x905a14  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0xc79ae4: mov             x1, x0
    // 0xc79ae8: stur            x0, [fp, #-0x10]
    // 0xc79aec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc79aec: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc79af0: r0 = TextEditingController()
    //     0xc79af0: bl              #0x905904  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0xc79af4: ldur            x0, [fp, #-0x10]
    // 0xc79af8: ldur            x2, [fp, #-8]
    // 0xc79afc: ArrayStore: r2[0] = r0  ; List_4
    //     0xc79afc: stur            w0, [x2, #0x17]
    //     0xc79b00: ldurb           w16, [x2, #-1]
    //     0xc79b04: ldurb           w17, [x0, #-1]
    //     0xc79b08: and             x16, x17, x16, lsr #2
    //     0xc79b0c: tst             x16, HEAP, lsr #32
    //     0xc79b10: b.eq            #0xc79b18
    //     0xc79b14: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xc79b18: r1 = <bool>
    //     0xc79b18: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0xc79b1c: r0 = RxBool()
    //     0xc79b1c: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0xc79b20: mov             x2, x0
    // 0xc79b24: r0 = Sentinel
    //     0xc79b24: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc79b28: stur            x2, [fp, #-0x10]
    // 0xc79b2c: StoreField: r2->field_13 = r0
    //     0xc79b2c: stur            w0, [x2, #0x13]
    // 0xc79b30: r0 = true
    //     0xc79b30: add             x0, NULL, #0x20  ; true
    // 0xc79b34: ArrayStore: r2[0] = r0  ; List_4
    //     0xc79b34: stur            w0, [x2, #0x17]
    // 0xc79b38: mov             x1, x2
    // 0xc79b3c: r0 = RxNotifier()
    //     0xc79b3c: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0xc79b40: ldur            x0, [fp, #-0x10]
    // 0xc79b44: r1 = true
    //     0xc79b44: add             x1, NULL, #0x20  ; true
    // 0xc79b48: StoreField: r0->field_13 = r1
    //     0xc79b48: stur            w1, [x0, #0x13]
    // 0xc79b4c: ldur            x1, [fp, #-8]
    // 0xc79b50: StoreField: r1->field_1f = r0
    //     0xc79b50: stur            w0, [x1, #0x1f]
    //     0xc79b54: ldurb           w16, [x1, #-1]
    //     0xc79b58: ldurb           w17, [x0, #-1]
    //     0xc79b5c: and             x16, x17, x16, lsr #2
    //     0xc79b60: tst             x16, HEAP, lsr #32
    //     0xc79b64: b.eq            #0xc79b6c
    //     0xc79b68: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc79b6c: r0 = Null
    //     0xc79b6c: mov             x0, NULL
    // 0xc79b70: LeaveFrame
    //     0xc79b70: mov             SP, fp
    //     0xc79b74: ldp             fp, lr, [SP], #0x10
    // 0xc79b78: ret
    //     0xc79b78: ret             
    // 0xc79b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc79b7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc79b80: b               #0xc79ab0
  }
}

// class id: 4314, size: 0x34, field offset: 0xc
//   const constructor, 
class CancelOrderConfirmBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc79a48, size: 0x48
    // 0xc79a48: EnterFrame
    //     0xc79a48: stp             fp, lr, [SP, #-0x10]!
    //     0xc79a4c: mov             fp, SP
    // 0xc79a50: AllocStack(0x8)
    //     0xc79a50: sub             SP, SP, #8
    // 0xc79a54: CheckStackOverflow
    //     0xc79a54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc79a58: cmp             SP, x16
    //     0xc79a5c: b.ls            #0xc79a88
    // 0xc79a60: r1 = <CancelOrderConfirmBottomSheet>
    //     0xc79a60: add             x1, PP, #0x49, lsl #12  ; [pp+0x49368] TypeArguments: <CancelOrderConfirmBottomSheet>
    //     0xc79a64: ldr             x1, [x1, #0x368]
    // 0xc79a68: r0 = _CancelOrderConfirmBottomSheetState()
    //     0xc79a68: bl              #0xc79b84  ; Allocate_CancelOrderConfirmBottomSheetStateStub -> _CancelOrderConfirmBottomSheetState (size=0x24)
    // 0xc79a6c: mov             x1, x0
    // 0xc79a70: stur            x0, [fp, #-8]
    // 0xc79a74: r0 = _CancelOrderConfirmBottomSheetState()
    //     0xc79a74: bl              #0xc79a90  ; [package:customer_app/app/presentation/custom_widgets/orders/cancel_order_confirm_bottom_sheet.dart] _CancelOrderConfirmBottomSheetState::_CancelOrderConfirmBottomSheetState
    // 0xc79a78: ldur            x0, [fp, #-8]
    // 0xc79a7c: LeaveFrame
    //     0xc79a7c: mov             SP, fp
    //     0xc79a80: ldp             fp, lr, [SP], #0x10
    // 0xc79a84: ret
    //     0xc79a84: ret             
    // 0xc79a88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc79a88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc79a8c: b               #0xc79a60
  }
}
