// lib: , url: package:customer_app/app/presentation/custom_widgets/home/<USER>

// class id: 1049067, size: 0x8
class :: {
}

// class id: 3591, size: 0x20, field offset: 0x14
class _VideoCarouselItemViewState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ build(/* No info */) {
    // ** addr: 0x98e440, size: 0x3f4
    // 0x98e440: EnterFrame
    //     0x98e440: stp             fp, lr, [SP, #-0x10]!
    //     0x98e444: mov             fp, SP
    // 0x98e448: AllocStack(0x60)
    //     0x98e448: sub             SP, SP, #0x60
    // 0x98e44c: SetupParameters(_VideoCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x98e44c: mov             x0, x1
    //     0x98e450: stur            x1, [fp, #-8]
    //     0x98e454: mov             x1, x2
    //     0x98e458: stur            x2, [fp, #-0x10]
    // 0x98e45c: CheckStackOverflow
    //     0x98e45c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98e460: cmp             SP, x16
    //     0x98e464: b.ls            #0x98e818
    // 0x98e468: r1 = 1
    //     0x98e468: movz            x1, #0x1
    // 0x98e46c: r0 = AllocateContext()
    //     0x98e46c: bl              #0x16f6108  ; AllocateContextStub
    // 0x98e470: mov             x3, x0
    // 0x98e474: ldur            x0, [fp, #-8]
    // 0x98e478: stur            x3, [fp, #-0x30]
    // 0x98e47c: StoreField: r3->field_f = r0
    //     0x98e47c: stur            w0, [x3, #0xf]
    // 0x98e480: LoadField: r1 = r0->field_b
    //     0x98e480: ldur            w1, [x0, #0xb]
    // 0x98e484: DecompressPointer r1
    //     0x98e484: add             x1, x1, HEAP, lsl #32
    // 0x98e488: cmp             w1, NULL
    // 0x98e48c: b.eq            #0x98e820
    // 0x98e490: LoadField: r4 = r1->field_13
    //     0x98e490: ldur            w4, [x1, #0x13]
    // 0x98e494: DecompressPointer r4
    //     0x98e494: add             x4, x4, HEAP, lsl #32
    // 0x98e498: stur            x4, [fp, #-0x28]
    // 0x98e49c: LoadField: r2 = r1->field_b
    //     0x98e49c: ldur            w2, [x1, #0xb]
    // 0x98e4a0: DecompressPointer r2
    //     0x98e4a0: add             x2, x2, HEAP, lsl #32
    // 0x98e4a4: cmp             w2, NULL
    // 0x98e4a8: b.ne            #0x98e4b4
    // 0x98e4ac: r5 = Null
    //     0x98e4ac: mov             x5, NULL
    // 0x98e4b0: b               #0x98e4bc
    // 0x98e4b4: LoadField: r1 = r2->field_b
    //     0x98e4b4: ldur            w1, [x2, #0xb]
    // 0x98e4b8: mov             x5, x1
    // 0x98e4bc: stur            x5, [fp, #-0x20]
    // 0x98e4c0: LoadField: r6 = r0->field_13
    //     0x98e4c0: ldur            w6, [x0, #0x13]
    // 0x98e4c4: DecompressPointer r6
    //     0x98e4c4: add             x6, x6, HEAP, lsl #32
    // 0x98e4c8: r16 = Sentinel
    //     0x98e4c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x98e4cc: cmp             w6, w16
    // 0x98e4d0: b.eq            #0x98e824
    // 0x98e4d4: mov             x2, x3
    // 0x98e4d8: stur            x6, [fp, #-0x18]
    // 0x98e4dc: r1 = Function '<anonymous closure>':.
    //     0x98e4dc: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c088] AnonymousClosure: (0x98e954), in [package:customer_app/app/presentation/custom_widgets/home/<USER>
    //     0x98e4e0: ldr             x1, [x1, #0x88]
    // 0x98e4e4: r0 = AllocateClosure()
    //     0x98e4e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x98e4e8: ldur            x2, [fp, #-0x30]
    // 0x98e4ec: r1 = Function '<anonymous closure>':.
    //     0x98e4ec: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c090] AnonymousClosure: (0x98e864), in [package:customer_app/app/presentation/custom_widgets/product_detail/product_video_carousel_item_view.dart] _ProductVideoCarouselItemViewState::build (0x9abe2c)
    //     0x98e4f0: ldr             x1, [x1, #0x90]
    // 0x98e4f4: stur            x0, [fp, #-0x30]
    // 0x98e4f8: r0 = AllocateClosure()
    //     0x98e4f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x98e4fc: stur            x0, [fp, #-0x38]
    // 0x98e500: r0 = PageView()
    //     0x98e500: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0x98e504: stur            x0, [fp, #-0x40]
    // 0x98e508: r16 = Instance_BouncingScrollPhysics
    //     0x98e508: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0x98e50c: ldr             x16, [x16, #0x890]
    // 0x98e510: ldur            lr, [fp, #-0x18]
    // 0x98e514: stp             lr, x16, [SP]
    // 0x98e518: mov             x1, x0
    // 0x98e51c: ldur            x2, [fp, #-0x38]
    // 0x98e520: ldur            x3, [fp, #-0x20]
    // 0x98e524: ldur            x5, [fp, #-0x30]
    // 0x98e528: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0x98e528: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0x98e52c: ldr             x4, [x4, #0xe40]
    // 0x98e530: r0 = PageView.builder()
    //     0x98e530: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0x98e534: r0 = AspectRatio()
    //     0x98e534: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0x98e538: d0 = 1.777778
    //     0x98e538: add             x17, PP, #0x4e, lsl #12  ; [pp+0x4e8f0] IMM: double(1.7777777777777777) from 0x3ffc71c71c71c71c
    //     0x98e53c: ldr             d0, [x17, #0x8f0]
    // 0x98e540: stur            x0, [fp, #-0x18]
    // 0x98e544: StoreField: r0->field_f = d0
    //     0x98e544: stur            d0, [x0, #0xf]
    // 0x98e548: ldur            x1, [fp, #-0x40]
    // 0x98e54c: StoreField: r0->field_b = r1
    //     0x98e54c: stur            w1, [x0, #0xb]
    // 0x98e550: r1 = Null
    //     0x98e550: mov             x1, NULL
    // 0x98e554: r2 = 2
    //     0x98e554: movz            x2, #0x2
    // 0x98e558: r0 = AllocateArray()
    //     0x98e558: bl              #0x16f7198  ; AllocateArrayStub
    // 0x98e55c: mov             x2, x0
    // 0x98e560: ldur            x0, [fp, #-0x18]
    // 0x98e564: stur            x2, [fp, #-0x20]
    // 0x98e568: StoreField: r2->field_f = r0
    //     0x98e568: stur            w0, [x2, #0xf]
    // 0x98e56c: r1 = <Widget>
    //     0x98e56c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x98e570: r0 = AllocateGrowableArray()
    //     0x98e570: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x98e574: mov             x2, x0
    // 0x98e578: ldur            x0, [fp, #-0x20]
    // 0x98e57c: stur            x2, [fp, #-0x18]
    // 0x98e580: StoreField: r2->field_f = r0
    //     0x98e580: stur            w0, [x2, #0xf]
    // 0x98e584: r0 = 2
    //     0x98e584: movz            x0, #0x2
    // 0x98e588: StoreField: r2->field_b = r0
    //     0x98e588: stur            w0, [x2, #0xb]
    // 0x98e58c: ldur            x1, [fp, #-8]
    // 0x98e590: LoadField: r3 = r1->field_b
    //     0x98e590: ldur            w3, [x1, #0xb]
    // 0x98e594: DecompressPointer r3
    //     0x98e594: add             x3, x3, HEAP, lsl #32
    // 0x98e598: cmp             w3, NULL
    // 0x98e59c: b.eq            #0x98e830
    // 0x98e5a0: LoadField: r4 = r3->field_b
    //     0x98e5a0: ldur            w4, [x3, #0xb]
    // 0x98e5a4: DecompressPointer r4
    //     0x98e5a4: add             x4, x4, HEAP, lsl #32
    // 0x98e5a8: cmp             w4, NULL
    // 0x98e5ac: b.ne            #0x98e5b8
    // 0x98e5b0: r3 = Null
    //     0x98e5b0: mov             x3, NULL
    // 0x98e5b4: b               #0x98e5bc
    // 0x98e5b8: LoadField: r3 = r4->field_b
    //     0x98e5b8: ldur            w3, [x4, #0xb]
    // 0x98e5bc: cmp             w3, NULL
    // 0x98e5c0: b.ne            #0x98e5cc
    // 0x98e5c4: r3 = 0
    //     0x98e5c4: movz            x3, #0
    // 0x98e5c8: b               #0x98e5d4
    // 0x98e5cc: r5 = LoadInt32Instr(r3)
    //     0x98e5cc: sbfx            x5, x3, #1, #0x1f
    // 0x98e5d0: mov             x3, x5
    // 0x98e5d4: cmp             x3, #1
    // 0x98e5d8: b.le            #0x98e788
    // 0x98e5dc: cmp             w4, NULL
    // 0x98e5e0: b.ne            #0x98e5ec
    // 0x98e5e4: r3 = Null
    //     0x98e5e4: mov             x3, NULL
    // 0x98e5e8: b               #0x98e5f0
    // 0x98e5ec: LoadField: r3 = r4->field_b
    //     0x98e5ec: ldur            w3, [x4, #0xb]
    // 0x98e5f0: cmp             w3, NULL
    // 0x98e5f4: b.ne            #0x98e600
    // 0x98e5f8: r3 = 0
    //     0x98e5f8: movz            x3, #0
    // 0x98e5fc: b               #0x98e608
    // 0x98e600: r4 = LoadInt32Instr(r3)
    //     0x98e600: sbfx            x4, x3, #1, #0x1f
    // 0x98e604: mov             x3, x4
    // 0x98e608: stur            x3, [fp, #-0x50]
    // 0x98e60c: ArrayLoad: r4 = r1[0]  ; List_8
    //     0x98e60c: ldur            x4, [x1, #0x17]
    // 0x98e610: ldur            x1, [fp, #-0x10]
    // 0x98e614: stur            x4, [fp, #-0x48]
    // 0x98e618: r0 = of()
    //     0x98e618: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x98e61c: LoadField: r1 = r0->field_5b
    //     0x98e61c: ldur            w1, [x0, #0x5b]
    // 0x98e620: DecompressPointer r1
    //     0x98e620: add             x1, x1, HEAP, lsl #32
    // 0x98e624: stur            x1, [fp, #-8]
    // 0x98e628: r0 = CarouselIndicator()
    //     0x98e628: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0x98e62c: mov             x3, x0
    // 0x98e630: ldur            x0, [fp, #-0x50]
    // 0x98e634: stur            x3, [fp, #-0x10]
    // 0x98e638: StoreField: r3->field_b = r0
    //     0x98e638: stur            x0, [x3, #0xb]
    // 0x98e63c: ldur            x0, [fp, #-0x48]
    // 0x98e640: StoreField: r3->field_13 = r0
    //     0x98e640: stur            x0, [x3, #0x13]
    // 0x98e644: ldur            x0, [fp, #-8]
    // 0x98e648: StoreField: r3->field_1b = r0
    //     0x98e648: stur            w0, [x3, #0x1b]
    // 0x98e64c: r0 = Instance_Color
    //     0x98e64c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x98e650: ldr             x0, [x0, #0x90]
    // 0x98e654: StoreField: r3->field_1f = r0
    //     0x98e654: stur            w0, [x3, #0x1f]
    // 0x98e658: r1 = Null
    //     0x98e658: mov             x1, NULL
    // 0x98e65c: r2 = 2
    //     0x98e65c: movz            x2, #0x2
    // 0x98e660: r0 = AllocateArray()
    //     0x98e660: bl              #0x16f7198  ; AllocateArrayStub
    // 0x98e664: mov             x2, x0
    // 0x98e668: ldur            x0, [fp, #-0x10]
    // 0x98e66c: stur            x2, [fp, #-8]
    // 0x98e670: StoreField: r2->field_f = r0
    //     0x98e670: stur            w0, [x2, #0xf]
    // 0x98e674: r1 = <Widget>
    //     0x98e674: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x98e678: r0 = AllocateGrowableArray()
    //     0x98e678: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x98e67c: mov             x1, x0
    // 0x98e680: ldur            x0, [fp, #-8]
    // 0x98e684: stur            x1, [fp, #-0x10]
    // 0x98e688: StoreField: r1->field_f = r0
    //     0x98e688: stur            w0, [x1, #0xf]
    // 0x98e68c: r0 = 2
    //     0x98e68c: movz            x0, #0x2
    // 0x98e690: StoreField: r1->field_b = r0
    //     0x98e690: stur            w0, [x1, #0xb]
    // 0x98e694: r0 = Row()
    //     0x98e694: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x98e698: mov             x1, x0
    // 0x98e69c: r0 = Instance_Axis
    //     0x98e69c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x98e6a0: stur            x1, [fp, #-8]
    // 0x98e6a4: StoreField: r1->field_f = r0
    //     0x98e6a4: stur            w0, [x1, #0xf]
    // 0x98e6a8: r0 = Instance_MainAxisAlignment
    //     0x98e6a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x98e6ac: ldr             x0, [x0, #0xab0]
    // 0x98e6b0: StoreField: r1->field_13 = r0
    //     0x98e6b0: stur            w0, [x1, #0x13]
    // 0x98e6b4: r0 = Instance_MainAxisSize
    //     0x98e6b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x98e6b8: ldr             x0, [x0, #0xa10]
    // 0x98e6bc: ArrayStore: r1[0] = r0  ; List_4
    //     0x98e6bc: stur            w0, [x1, #0x17]
    // 0x98e6c0: r2 = Instance_CrossAxisAlignment
    //     0x98e6c0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x98e6c4: ldr             x2, [x2, #0xa18]
    // 0x98e6c8: StoreField: r1->field_1b = r2
    //     0x98e6c8: stur            w2, [x1, #0x1b]
    // 0x98e6cc: r3 = Instance_VerticalDirection
    //     0x98e6cc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x98e6d0: ldr             x3, [x3, #0xa20]
    // 0x98e6d4: StoreField: r1->field_23 = r3
    //     0x98e6d4: stur            w3, [x1, #0x23]
    // 0x98e6d8: r4 = Instance_Clip
    //     0x98e6d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x98e6dc: ldr             x4, [x4, #0x38]
    // 0x98e6e0: StoreField: r1->field_2b = r4
    //     0x98e6e0: stur            w4, [x1, #0x2b]
    // 0x98e6e4: StoreField: r1->field_2f = rZR
    //     0x98e6e4: stur            xzr, [x1, #0x2f]
    // 0x98e6e8: ldur            x5, [fp, #-0x10]
    // 0x98e6ec: StoreField: r1->field_b = r5
    //     0x98e6ec: stur            w5, [x1, #0xb]
    // 0x98e6f0: r0 = Padding()
    //     0x98e6f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x98e6f4: mov             x2, x0
    // 0x98e6f8: r0 = Instance_EdgeInsets
    //     0x98e6f8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x98e6fc: ldr             x0, [x0, #0x770]
    // 0x98e700: stur            x2, [fp, #-0x10]
    // 0x98e704: StoreField: r2->field_f = r0
    //     0x98e704: stur            w0, [x2, #0xf]
    // 0x98e708: ldur            x0, [fp, #-8]
    // 0x98e70c: StoreField: r2->field_b = r0
    //     0x98e70c: stur            w0, [x2, #0xb]
    // 0x98e710: ldur            x0, [fp, #-0x18]
    // 0x98e714: LoadField: r1 = r0->field_b
    //     0x98e714: ldur            w1, [x0, #0xb]
    // 0x98e718: LoadField: r3 = r0->field_f
    //     0x98e718: ldur            w3, [x0, #0xf]
    // 0x98e71c: DecompressPointer r3
    //     0x98e71c: add             x3, x3, HEAP, lsl #32
    // 0x98e720: LoadField: r4 = r3->field_b
    //     0x98e720: ldur            w4, [x3, #0xb]
    // 0x98e724: r3 = LoadInt32Instr(r1)
    //     0x98e724: sbfx            x3, x1, #1, #0x1f
    // 0x98e728: stur            x3, [fp, #-0x48]
    // 0x98e72c: r1 = LoadInt32Instr(r4)
    //     0x98e72c: sbfx            x1, x4, #1, #0x1f
    // 0x98e730: cmp             x3, x1
    // 0x98e734: b.ne            #0x98e740
    // 0x98e738: mov             x1, x0
    // 0x98e73c: r0 = _growToNextCapacity()
    //     0x98e73c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x98e740: ldur            x2, [fp, #-0x18]
    // 0x98e744: ldur            x3, [fp, #-0x48]
    // 0x98e748: add             x0, x3, #1
    // 0x98e74c: lsl             x1, x0, #1
    // 0x98e750: StoreField: r2->field_b = r1
    //     0x98e750: stur            w1, [x2, #0xb]
    // 0x98e754: LoadField: r1 = r2->field_f
    //     0x98e754: ldur            w1, [x2, #0xf]
    // 0x98e758: DecompressPointer r1
    //     0x98e758: add             x1, x1, HEAP, lsl #32
    // 0x98e75c: ldur            x0, [fp, #-0x10]
    // 0x98e760: ArrayStore: r1[r3] = r0  ; List_4
    //     0x98e760: add             x25, x1, x3, lsl #2
    //     0x98e764: add             x25, x25, #0xf
    //     0x98e768: str             w0, [x25]
    //     0x98e76c: tbz             w0, #0, #0x98e788
    //     0x98e770: ldurb           w16, [x1, #-1]
    //     0x98e774: ldurb           w17, [x0, #-1]
    //     0x98e778: and             x16, x17, x16, lsr #2
    //     0x98e77c: tst             x16, HEAP, lsr #32
    //     0x98e780: b.eq            #0x98e788
    //     0x98e784: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x98e788: r0 = Column()
    //     0x98e788: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x98e78c: mov             x1, x0
    // 0x98e790: r0 = Instance_Axis
    //     0x98e790: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x98e794: stur            x1, [fp, #-8]
    // 0x98e798: StoreField: r1->field_f = r0
    //     0x98e798: stur            w0, [x1, #0xf]
    // 0x98e79c: r0 = Instance_MainAxisAlignment
    //     0x98e79c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x98e7a0: ldr             x0, [x0, #0xa08]
    // 0x98e7a4: StoreField: r1->field_13 = r0
    //     0x98e7a4: stur            w0, [x1, #0x13]
    // 0x98e7a8: r0 = Instance_MainAxisSize
    //     0x98e7a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x98e7ac: ldr             x0, [x0, #0xa10]
    // 0x98e7b0: ArrayStore: r1[0] = r0  ; List_4
    //     0x98e7b0: stur            w0, [x1, #0x17]
    // 0x98e7b4: r0 = Instance_CrossAxisAlignment
    //     0x98e7b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x98e7b8: ldr             x0, [x0, #0xa18]
    // 0x98e7bc: StoreField: r1->field_1b = r0
    //     0x98e7bc: stur            w0, [x1, #0x1b]
    // 0x98e7c0: r0 = Instance_VerticalDirection
    //     0x98e7c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x98e7c4: ldr             x0, [x0, #0xa20]
    // 0x98e7c8: StoreField: r1->field_23 = r0
    //     0x98e7c8: stur            w0, [x1, #0x23]
    // 0x98e7cc: r0 = Instance_Clip
    //     0x98e7cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x98e7d0: ldr             x0, [x0, #0x38]
    // 0x98e7d4: StoreField: r1->field_2b = r0
    //     0x98e7d4: stur            w0, [x1, #0x2b]
    // 0x98e7d8: StoreField: r1->field_2f = rZR
    //     0x98e7d8: stur            xzr, [x1, #0x2f]
    // 0x98e7dc: ldur            x0, [fp, #-0x18]
    // 0x98e7e0: StoreField: r1->field_b = r0
    //     0x98e7e0: stur            w0, [x1, #0xb]
    // 0x98e7e4: r0 = Container()
    //     0x98e7e4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x98e7e8: stur            x0, [fp, #-0x10]
    // 0x98e7ec: ldur            x16, [fp, #-0x28]
    // 0x98e7f0: ldur            lr, [fp, #-8]
    // 0x98e7f4: stp             lr, x16, [SP]
    // 0x98e7f8: mov             x1, x0
    // 0x98e7fc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, margin, 0x1, null]
    //     0x98e7fc: add             x4, PP, #0x53, lsl #12  ; [pp+0x53400] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "margin", 0x1, Null]
    //     0x98e800: ldr             x4, [x4, #0x400]
    // 0x98e804: r0 = Container()
    //     0x98e804: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x98e808: ldur            x0, [fp, #-0x10]
    // 0x98e80c: LeaveFrame
    //     0x98e80c: mov             SP, fp
    //     0x98e810: ldp             fp, lr, [SP], #0x10
    // 0x98e814: ret
    //     0x98e814: ret             
    // 0x98e818: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98e818: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98e81c: b               #0x98e468
    // 0x98e820: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98e820: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98e824: r9 = _pageController
    //     0x98e824: add             x9, PP, #0x5c, lsl #12  ; [pp+0x5c080] Field <_VideoCarouselItemViewState@1227267451._pageController@1227267451>: late (offset: 0x14)
    //     0x98e828: ldr             x9, [x9, #0x80]
    // 0x98e82c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x98e82c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x98e830: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98e830: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0x98e954, size: 0x84
    // 0x98e954: EnterFrame
    //     0x98e954: stp             fp, lr, [SP, #-0x10]!
    //     0x98e958: mov             fp, SP
    // 0x98e95c: AllocStack(0x10)
    //     0x98e95c: sub             SP, SP, #0x10
    // 0x98e960: SetupParameters()
    //     0x98e960: ldr             x0, [fp, #0x18]
    //     0x98e964: ldur            w1, [x0, #0x17]
    //     0x98e968: add             x1, x1, HEAP, lsl #32
    //     0x98e96c: stur            x1, [fp, #-8]
    // 0x98e970: CheckStackOverflow
    //     0x98e970: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98e974: cmp             SP, x16
    //     0x98e978: b.ls            #0x98e9d0
    // 0x98e97c: r1 = 1
    //     0x98e97c: movz            x1, #0x1
    // 0x98e980: r0 = AllocateContext()
    //     0x98e980: bl              #0x16f6108  ; AllocateContextStub
    // 0x98e984: mov             x1, x0
    // 0x98e988: ldur            x0, [fp, #-8]
    // 0x98e98c: StoreField: r1->field_b = r0
    //     0x98e98c: stur            w0, [x1, #0xb]
    // 0x98e990: ldr             x2, [fp, #0x10]
    // 0x98e994: StoreField: r1->field_f = r2
    //     0x98e994: stur            w2, [x1, #0xf]
    // 0x98e998: LoadField: r3 = r0->field_f
    //     0x98e998: ldur            w3, [x0, #0xf]
    // 0x98e99c: DecompressPointer r3
    //     0x98e99c: add             x3, x3, HEAP, lsl #32
    // 0x98e9a0: mov             x2, x1
    // 0x98e9a4: stur            x3, [fp, #-0x10]
    // 0x98e9a8: r1 = Function '<anonymous closure>':.
    //     0x98e9a8: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c098] AnonymousClosure: (0x98e9d8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0x98e9ac: ldr             x1, [x1, #0x98]
    // 0x98e9b0: r0 = AllocateClosure()
    //     0x98e9b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x98e9b4: ldur            x1, [fp, #-0x10]
    // 0x98e9b8: mov             x2, x0
    // 0x98e9bc: r0 = setState()
    //     0x98e9bc: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x98e9c0: r0 = Null
    //     0x98e9c0: mov             x0, NULL
    // 0x98e9c4: LeaveFrame
    //     0x98e9c4: mov             SP, fp
    //     0x98e9c8: ldp             fp, lr, [SP], #0x10
    // 0x98e9cc: ret
    //     0x98e9cc: ret             
    // 0x98e9d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98e9d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98e9d4: b               #0x98e97c
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc869d8, size: 0x54
    // 0xc869d8: EnterFrame
    //     0xc869d8: stp             fp, lr, [SP, #-0x10]!
    //     0xc869dc: mov             fp, SP
    // 0xc869e0: CheckStackOverflow
    //     0xc869e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc869e4: cmp             SP, x16
    //     0xc869e8: b.ls            #0xc86a18
    // 0xc869ec: LoadField: r0 = r1->field_13
    //     0xc869ec: ldur            w0, [x1, #0x13]
    // 0xc869f0: DecompressPointer r0
    //     0xc869f0: add             x0, x0, HEAP, lsl #32
    // 0xc869f4: r16 = Sentinel
    //     0xc869f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc869f8: cmp             w0, w16
    // 0xc869fc: b.eq            #0xc86a20
    // 0xc86a00: mov             x1, x0
    // 0xc86a04: r0 = dispose()
    //     0xc86a04: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc86a08: r0 = Null
    //     0xc86a08: mov             x0, NULL
    // 0xc86a0c: LeaveFrame
    //     0xc86a0c: mov             SP, fp
    //     0xc86a10: ldp             fp, lr, [SP], #0x10
    // 0xc86a14: ret
    //     0xc86a14: ret             
    // 0xc86a18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc86a18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc86a1c: b               #0xc869ec
    // 0xc86a20: r9 = _pageController
    //     0xc86a20: add             x9, PP, #0x5c, lsl #12  ; [pp+0x5c080] Field <_VideoCarouselItemViewState@1227267451._pageController@1227267451>: late (offset: 0x14)
    //     0xc86a24: ldr             x9, [x9, #0x80]
    // 0xc86a28: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc86a28: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4318, size: 0x18, field offset: 0xc
//   const constructor, 
class VideoCarouselItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7997c, size: 0x30
    // 0xc7997c: EnterFrame
    //     0xc7997c: stp             fp, lr, [SP, #-0x10]!
    //     0xc79980: mov             fp, SP
    // 0xc79984: mov             x0, x1
    // 0xc79988: r1 = <VideoCarouselItemView>
    //     0xc79988: add             x1, PP, #0x49, lsl #12  ; [pp+0x49380] TypeArguments: <VideoCarouselItemView>
    //     0xc7998c: ldr             x1, [x1, #0x380]
    // 0xc79990: r0 = _VideoCarouselItemViewState()
    //     0xc79990: bl              #0xc799ac  ; Allocate_VideoCarouselItemViewStateStub -> _VideoCarouselItemViewState (size=0x20)
    // 0xc79994: r1 = Sentinel
    //     0xc79994: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc79998: StoreField: r0->field_13 = r1
    //     0xc79998: stur            w1, [x0, #0x13]
    // 0xc7999c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc7999c: stur            xzr, [x0, #0x17]
    // 0xc799a0: LeaveFrame
    //     0xc799a0: mov             SP, fp
    //     0xc799a4: ldp             fp, lr, [SP], #0x10
    // 0xc799a8: ret
    //     0xc799a8: ret             
  }
}
