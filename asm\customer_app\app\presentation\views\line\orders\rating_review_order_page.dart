// lib: , url: package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart

// class id: 1049539, size: 0x8
class :: {
}

// class id: 4529, size: 0x14, field offset: 0x14
//   const constructor, 
class RatingReviewOrderPage extends BaseView<dynamic> {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x131efa4, size: 0x98
    // 0x131efa4: EnterFrame
    //     0x131efa4: stp             fp, lr, [SP, #-0x10]!
    //     0x131efa8: mov             fp, SP
    // 0x131efac: AllocStack(0x8)
    //     0x131efac: sub             SP, SP, #8
    // 0x131efb0: SetupParameters()
    //     0x131efb0: ldr             x0, [fp, #0x10]
    //     0x131efb4: ldur            w2, [x0, #0x17]
    //     0x131efb8: add             x2, x2, HEAP, lsl #32
    //     0x131efbc: stur            x2, [fp, #-8]
    // 0x131efc0: CheckStackOverflow
    //     0x131efc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131efc4: cmp             SP, x16
    //     0x131efc8: b.ls            #0x131f034
    // 0x131efcc: LoadField: r1 = r2->field_f
    //     0x131efcc: ldur            w1, [x2, #0xf]
    // 0x131efd0: DecompressPointer r1
    //     0x131efd0: add             x1, x1, HEAP, lsl #32
    // 0x131efd4: r0 = controller()
    //     0x131efd4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131efd8: LoadField: r1 = r0->field_c3
    //     0x131efd8: ldur            w1, [x0, #0xc3]
    // 0x131efdc: DecompressPointer r1
    //     0x131efdc: add             x1, x1, HEAP, lsl #32
    // 0x131efe0: r0 = value()
    //     0x131efe0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131efe4: LoadField: d0 = r0->field_7
    //     0x131efe4: ldur            d0, [x0, #7]
    // 0x131efe8: d1 = 0.000000
    //     0x131efe8: eor             v1.16b, v1.16b, v1.16b
    // 0x131efec: fcmp            d0, d1
    // 0x131eff0: b.eq            #0x131f024
    // 0x131eff4: ldur            x0, [fp, #-8]
    // 0x131eff8: LoadField: r1 = r0->field_f
    //     0x131eff8: ldur            w1, [x0, #0xf]
    // 0x131effc: DecompressPointer r1
    //     0x131effc: add             x1, x1, HEAP, lsl #32
    // 0x131f000: r0 = controller()
    //     0x131f000: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131f004: mov             x1, x0
    // 0x131f008: r0 = onSubmitEvent()
    //     0x131f008: bl              #0x1321e28  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::onSubmitEvent
    // 0x131f00c: ldur            x0, [fp, #-8]
    // 0x131f010: LoadField: r1 = r0->field_f
    //     0x131f010: ldur            w1, [x0, #0xf]
    // 0x131f014: DecompressPointer r1
    //     0x131f014: add             x1, x1, HEAP, lsl #32
    // 0x131f018: r0 = controller()
    //     0x131f018: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131f01c: mov             x1, x0
    // 0x131f020: r0 = createReview()
    //     0x131f020: bl              #0x131f714  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::createReview
    // 0x131f024: r0 = Null
    //     0x131f024: mov             x0, NULL
    // 0x131f028: LeaveFrame
    //     0x131f028: mov             SP, fp
    //     0x131f02c: ldp             fp, lr, [SP], #0x10
    // 0x131f030: ret
    //     0x131f030: ret             
    // 0x131f034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131f034: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131f038: b               #0x131efcc
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x131f03c, size: 0x6d8
    // 0x131f03c: EnterFrame
    //     0x131f03c: stp             fp, lr, [SP, #-0x10]!
    //     0x131f040: mov             fp, SP
    // 0x131f044: AllocStack(0x60)
    //     0x131f044: sub             SP, SP, #0x60
    // 0x131f048: SetupParameters()
    //     0x131f048: ldr             x0, [fp, #0x10]
    //     0x131f04c: ldur            w2, [x0, #0x17]
    //     0x131f050: add             x2, x2, HEAP, lsl #32
    //     0x131f054: stur            x2, [fp, #-8]
    // 0x131f058: CheckStackOverflow
    //     0x131f058: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131f05c: cmp             SP, x16
    //     0x131f060: b.ls            #0x131f70c
    // 0x131f064: LoadField: r1 = r2->field_13
    //     0x131f064: ldur            w1, [x2, #0x13]
    // 0x131f068: DecompressPointer r1
    //     0x131f068: add             x1, x1, HEAP, lsl #32
    // 0x131f06c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x131f06c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x131f070: r0 = _of()
    //     0x131f070: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x131f074: LoadField: r1 = r0->field_23
    //     0x131f074: ldur            w1, [x0, #0x23]
    // 0x131f078: DecompressPointer r1
    //     0x131f078: add             x1, x1, HEAP, lsl #32
    // 0x131f07c: LoadField: d0 = r1->field_1f
    //     0x131f07c: ldur            d0, [x1, #0x1f]
    // 0x131f080: stur            d0, [fp, #-0x50]
    // 0x131f084: r0 = EdgeInsets()
    //     0x131f084: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x131f088: stur            x0, [fp, #-0x10]
    // 0x131f08c: StoreField: r0->field_7 = rZR
    //     0x131f08c: stur            xzr, [x0, #7]
    // 0x131f090: StoreField: r0->field_f = rZR
    //     0x131f090: stur            xzr, [x0, #0xf]
    // 0x131f094: ArrayStore: r0[0] = rZR  ; List_8
    //     0x131f094: stur            xzr, [x0, #0x17]
    // 0x131f098: ldur            d0, [fp, #-0x50]
    // 0x131f09c: StoreField: r0->field_1f = d0
    //     0x131f09c: stur            d0, [x0, #0x1f]
    // 0x131f0a0: r16 = <EdgeInsets>
    //     0x131f0a0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x131f0a4: ldr             x16, [x16, #0xda0]
    // 0x131f0a8: r30 = Instance_EdgeInsets
    //     0x131f0a8: add             lr, PP, #0x34, lsl #12  ; [pp+0x34670] Obj!EdgeInsets@d572c1
    //     0x131f0ac: ldr             lr, [lr, #0x670]
    // 0x131f0b0: stp             lr, x16, [SP]
    // 0x131f0b4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x131f0b4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x131f0b8: r0 = all()
    //     0x131f0b8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x131f0bc: ldur            x2, [fp, #-8]
    // 0x131f0c0: stur            x0, [fp, #-0x18]
    // 0x131f0c4: LoadField: r1 = r2->field_f
    //     0x131f0c4: ldur            w1, [x2, #0xf]
    // 0x131f0c8: DecompressPointer r1
    //     0x131f0c8: add             x1, x1, HEAP, lsl #32
    // 0x131f0cc: r0 = controller()
    //     0x131f0cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131f0d0: LoadField: r1 = r0->field_c3
    //     0x131f0d0: ldur            w1, [x0, #0xc3]
    // 0x131f0d4: DecompressPointer r1
    //     0x131f0d4: add             x1, x1, HEAP, lsl #32
    // 0x131f0d8: r0 = value()
    //     0x131f0d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131f0dc: LoadField: d0 = r0->field_7
    //     0x131f0dc: ldur            d0, [x0, #7]
    // 0x131f0e0: d1 = 0.000000
    //     0x131f0e0: eor             v1.16b, v1.16b, v1.16b
    // 0x131f0e4: fcmp            d0, d1
    // 0x131f0e8: b.eq            #0x131f2d8
    // 0x131f0ec: ldur            x2, [fp, #-8]
    // 0x131f0f0: LoadField: r1 = r2->field_f
    //     0x131f0f0: ldur            w1, [x2, #0xf]
    // 0x131f0f4: DecompressPointer r1
    //     0x131f0f4: add             x1, x1, HEAP, lsl #32
    // 0x131f0f8: r0 = controller()
    //     0x131f0f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131f0fc: LoadField: r1 = r0->field_53
    //     0x131f0fc: ldur            w1, [x0, #0x53]
    // 0x131f100: DecompressPointer r1
    //     0x131f100: add             x1, x1, HEAP, lsl #32
    // 0x131f104: r0 = value()
    //     0x131f104: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131f108: LoadField: r1 = r0->field_3f
    //     0x131f108: ldur            w1, [x0, #0x3f]
    // 0x131f10c: DecompressPointer r1
    //     0x131f10c: add             x1, x1, HEAP, lsl #32
    // 0x131f110: cmp             w1, NULL
    // 0x131f114: b.ne            #0x131f120
    // 0x131f118: r0 = Null
    //     0x131f118: mov             x0, NULL
    // 0x131f11c: b               #0x131f144
    // 0x131f120: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x131f120: ldur            w0, [x1, #0x17]
    // 0x131f124: DecompressPointer r0
    //     0x131f124: add             x0, x0, HEAP, lsl #32
    // 0x131f128: cmp             w0, NULL
    // 0x131f12c: b.ne            #0x131f138
    // 0x131f130: r0 = Null
    //     0x131f130: mov             x0, NULL
    // 0x131f134: b               #0x131f144
    // 0x131f138: LoadField: r1 = r0->field_7
    //     0x131f138: ldur            w1, [x0, #7]
    // 0x131f13c: DecompressPointer r1
    //     0x131f13c: add             x1, x1, HEAP, lsl #32
    // 0x131f140: mov             x0, x1
    // 0x131f144: cmp             w0, NULL
    // 0x131f148: b.ne            #0x131f154
    // 0x131f14c: r0 = 0
    //     0x131f14c: movz            x0, #0
    // 0x131f150: b               #0x131f164
    // 0x131f154: r1 = LoadInt32Instr(r0)
    //     0x131f154: sbfx            x1, x0, #1, #0x1f
    //     0x131f158: tbz             w0, #0, #0x131f160
    //     0x131f15c: ldur            x1, [x0, #7]
    // 0x131f160: mov             x0, x1
    // 0x131f164: ldur            x2, [fp, #-8]
    // 0x131f168: stur            x0, [fp, #-0x20]
    // 0x131f16c: LoadField: r1 = r2->field_f
    //     0x131f16c: ldur            w1, [x2, #0xf]
    // 0x131f170: DecompressPointer r1
    //     0x131f170: add             x1, x1, HEAP, lsl #32
    // 0x131f174: r0 = controller()
    //     0x131f174: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131f178: LoadField: r1 = r0->field_53
    //     0x131f178: ldur            w1, [x0, #0x53]
    // 0x131f17c: DecompressPointer r1
    //     0x131f17c: add             x1, x1, HEAP, lsl #32
    // 0x131f180: r0 = value()
    //     0x131f180: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131f184: LoadField: r1 = r0->field_3f
    //     0x131f184: ldur            w1, [x0, #0x3f]
    // 0x131f188: DecompressPointer r1
    //     0x131f188: add             x1, x1, HEAP, lsl #32
    // 0x131f18c: cmp             w1, NULL
    // 0x131f190: b.ne            #0x131f19c
    // 0x131f194: r0 = Null
    //     0x131f194: mov             x0, NULL
    // 0x131f198: b               #0x131f1c0
    // 0x131f19c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x131f19c: ldur            w0, [x1, #0x17]
    // 0x131f1a0: DecompressPointer r0
    //     0x131f1a0: add             x0, x0, HEAP, lsl #32
    // 0x131f1a4: cmp             w0, NULL
    // 0x131f1a8: b.ne            #0x131f1b4
    // 0x131f1ac: r0 = Null
    //     0x131f1ac: mov             x0, NULL
    // 0x131f1b0: b               #0x131f1c0
    // 0x131f1b4: LoadField: r1 = r0->field_b
    //     0x131f1b4: ldur            w1, [x0, #0xb]
    // 0x131f1b8: DecompressPointer r1
    //     0x131f1b8: add             x1, x1, HEAP, lsl #32
    // 0x131f1bc: mov             x0, x1
    // 0x131f1c0: cmp             w0, NULL
    // 0x131f1c4: b.ne            #0x131f1d0
    // 0x131f1c8: r0 = 0
    //     0x131f1c8: movz            x0, #0
    // 0x131f1cc: b               #0x131f1e0
    // 0x131f1d0: r1 = LoadInt32Instr(r0)
    //     0x131f1d0: sbfx            x1, x0, #1, #0x1f
    //     0x131f1d4: tbz             w0, #0, #0x131f1dc
    //     0x131f1d8: ldur            x1, [x0, #7]
    // 0x131f1dc: mov             x0, x1
    // 0x131f1e0: ldur            x2, [fp, #-8]
    // 0x131f1e4: stur            x0, [fp, #-0x28]
    // 0x131f1e8: LoadField: r1 = r2->field_f
    //     0x131f1e8: ldur            w1, [x2, #0xf]
    // 0x131f1ec: DecompressPointer r1
    //     0x131f1ec: add             x1, x1, HEAP, lsl #32
    // 0x131f1f0: r0 = controller()
    //     0x131f1f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131f1f4: LoadField: r1 = r0->field_53
    //     0x131f1f4: ldur            w1, [x0, #0x53]
    // 0x131f1f8: DecompressPointer r1
    //     0x131f1f8: add             x1, x1, HEAP, lsl #32
    // 0x131f1fc: r0 = value()
    //     0x131f1fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131f200: LoadField: r1 = r0->field_3f
    //     0x131f200: ldur            w1, [x0, #0x3f]
    // 0x131f204: DecompressPointer r1
    //     0x131f204: add             x1, x1, HEAP, lsl #32
    // 0x131f208: cmp             w1, NULL
    // 0x131f20c: b.ne            #0x131f218
    // 0x131f210: r0 = Null
    //     0x131f210: mov             x0, NULL
    // 0x131f214: b               #0x131f23c
    // 0x131f218: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x131f218: ldur            w0, [x1, #0x17]
    // 0x131f21c: DecompressPointer r0
    //     0x131f21c: add             x0, x0, HEAP, lsl #32
    // 0x131f220: cmp             w0, NULL
    // 0x131f224: b.ne            #0x131f230
    // 0x131f228: r0 = Null
    //     0x131f228: mov             x0, NULL
    // 0x131f22c: b               #0x131f23c
    // 0x131f230: LoadField: r1 = r0->field_f
    //     0x131f230: ldur            w1, [x0, #0xf]
    // 0x131f234: DecompressPointer r1
    //     0x131f234: add             x1, x1, HEAP, lsl #32
    // 0x131f238: mov             x0, x1
    // 0x131f23c: cmp             w0, NULL
    // 0x131f240: b.ne            #0x131f24c
    // 0x131f244: r0 = 0
    //     0x131f244: movz            x0, #0
    // 0x131f248: b               #0x131f25c
    // 0x131f24c: r1 = LoadInt32Instr(r0)
    //     0x131f24c: sbfx            x1, x0, #1, #0x1f
    //     0x131f250: tbz             w0, #0, #0x131f258
    //     0x131f254: ldur            x1, [x0, #7]
    // 0x131f258: mov             x0, x1
    // 0x131f25c: stur            x0, [fp, #-0x30]
    // 0x131f260: r0 = Color()
    //     0x131f260: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x131f264: mov             x1, x0
    // 0x131f268: r0 = Instance_ColorSpace
    //     0x131f268: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x131f26c: StoreField: r1->field_27 = r0
    //     0x131f26c: stur            w0, [x1, #0x27]
    // 0x131f270: d0 = 1.000000
    //     0x131f270: fmov            d0, #1.00000000
    // 0x131f274: StoreField: r1->field_7 = d0
    //     0x131f274: stur            d0, [x1, #7]
    // 0x131f278: ldur            x0, [fp, #-0x20]
    // 0x131f27c: ubfx            x0, x0, #0, #0x20
    // 0x131f280: and             w2, w0, #0xff
    // 0x131f284: ubfx            x2, x2, #0, #0x20
    // 0x131f288: scvtf           d0, x2
    // 0x131f28c: d1 = 255.000000
    //     0x131f28c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x131f290: fdiv            d2, d0, d1
    // 0x131f294: StoreField: r1->field_f = d2
    //     0x131f294: stur            d2, [x1, #0xf]
    // 0x131f298: ldur            x0, [fp, #-0x28]
    // 0x131f29c: ubfx            x0, x0, #0, #0x20
    // 0x131f2a0: and             w2, w0, #0xff
    // 0x131f2a4: ubfx            x2, x2, #0, #0x20
    // 0x131f2a8: scvtf           d0, x2
    // 0x131f2ac: fdiv            d2, d0, d1
    // 0x131f2b0: ArrayStore: r1[0] = d2  ; List_8
    //     0x131f2b0: stur            d2, [x1, #0x17]
    // 0x131f2b4: ldur            x0, [fp, #-0x30]
    // 0x131f2b8: ubfx            x0, x0, #0, #0x20
    // 0x131f2bc: and             w2, w0, #0xff
    // 0x131f2c0: ubfx            x2, x2, #0, #0x20
    // 0x131f2c4: scvtf           d0, x2
    // 0x131f2c8: fdiv            d2, d0, d1
    // 0x131f2cc: StoreField: r1->field_1f = d2
    //     0x131f2cc: stur            d2, [x1, #0x1f]
    // 0x131f2d0: mov             x3, x1
    // 0x131f2d4: b               #0x131f4c8
    // 0x131f2d8: ldur            x2, [fp, #-8]
    // 0x131f2dc: r0 = Instance_ColorSpace
    //     0x131f2dc: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x131f2e0: d1 = 255.000000
    //     0x131f2e0: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x131f2e4: LoadField: r1 = r2->field_f
    //     0x131f2e4: ldur            w1, [x2, #0xf]
    // 0x131f2e8: DecompressPointer r1
    //     0x131f2e8: add             x1, x1, HEAP, lsl #32
    // 0x131f2ec: r0 = controller()
    //     0x131f2ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131f2f0: LoadField: r1 = r0->field_53
    //     0x131f2f0: ldur            w1, [x0, #0x53]
    // 0x131f2f4: DecompressPointer r1
    //     0x131f2f4: add             x1, x1, HEAP, lsl #32
    // 0x131f2f8: r0 = value()
    //     0x131f2f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131f2fc: LoadField: r1 = r0->field_3f
    //     0x131f2fc: ldur            w1, [x0, #0x3f]
    // 0x131f300: DecompressPointer r1
    //     0x131f300: add             x1, x1, HEAP, lsl #32
    // 0x131f304: cmp             w1, NULL
    // 0x131f308: b.ne            #0x131f314
    // 0x131f30c: r0 = Null
    //     0x131f30c: mov             x0, NULL
    // 0x131f310: b               #0x131f338
    // 0x131f314: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x131f314: ldur            w0, [x1, #0x17]
    // 0x131f318: DecompressPointer r0
    //     0x131f318: add             x0, x0, HEAP, lsl #32
    // 0x131f31c: cmp             w0, NULL
    // 0x131f320: b.ne            #0x131f32c
    // 0x131f324: r0 = Null
    //     0x131f324: mov             x0, NULL
    // 0x131f328: b               #0x131f338
    // 0x131f32c: LoadField: r1 = r0->field_7
    //     0x131f32c: ldur            w1, [x0, #7]
    // 0x131f330: DecompressPointer r1
    //     0x131f330: add             x1, x1, HEAP, lsl #32
    // 0x131f334: mov             x0, x1
    // 0x131f338: cmp             w0, NULL
    // 0x131f33c: b.ne            #0x131f348
    // 0x131f340: r0 = 0
    //     0x131f340: movz            x0, #0
    // 0x131f344: b               #0x131f358
    // 0x131f348: r1 = LoadInt32Instr(r0)
    //     0x131f348: sbfx            x1, x0, #1, #0x1f
    //     0x131f34c: tbz             w0, #0, #0x131f354
    //     0x131f350: ldur            x1, [x0, #7]
    // 0x131f354: mov             x0, x1
    // 0x131f358: ldur            x2, [fp, #-8]
    // 0x131f35c: stur            x0, [fp, #-0x20]
    // 0x131f360: LoadField: r1 = r2->field_f
    //     0x131f360: ldur            w1, [x2, #0xf]
    // 0x131f364: DecompressPointer r1
    //     0x131f364: add             x1, x1, HEAP, lsl #32
    // 0x131f368: r0 = controller()
    //     0x131f368: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131f36c: LoadField: r1 = r0->field_53
    //     0x131f36c: ldur            w1, [x0, #0x53]
    // 0x131f370: DecompressPointer r1
    //     0x131f370: add             x1, x1, HEAP, lsl #32
    // 0x131f374: r0 = value()
    //     0x131f374: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131f378: LoadField: r1 = r0->field_3f
    //     0x131f378: ldur            w1, [x0, #0x3f]
    // 0x131f37c: DecompressPointer r1
    //     0x131f37c: add             x1, x1, HEAP, lsl #32
    // 0x131f380: cmp             w1, NULL
    // 0x131f384: b.ne            #0x131f390
    // 0x131f388: r0 = Null
    //     0x131f388: mov             x0, NULL
    // 0x131f38c: b               #0x131f3b4
    // 0x131f390: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x131f390: ldur            w0, [x1, #0x17]
    // 0x131f394: DecompressPointer r0
    //     0x131f394: add             x0, x0, HEAP, lsl #32
    // 0x131f398: cmp             w0, NULL
    // 0x131f39c: b.ne            #0x131f3a8
    // 0x131f3a0: r0 = Null
    //     0x131f3a0: mov             x0, NULL
    // 0x131f3a4: b               #0x131f3b4
    // 0x131f3a8: LoadField: r1 = r0->field_b
    //     0x131f3a8: ldur            w1, [x0, #0xb]
    // 0x131f3ac: DecompressPointer r1
    //     0x131f3ac: add             x1, x1, HEAP, lsl #32
    // 0x131f3b0: mov             x0, x1
    // 0x131f3b4: cmp             w0, NULL
    // 0x131f3b8: b.ne            #0x131f3c4
    // 0x131f3bc: r0 = 0
    //     0x131f3bc: movz            x0, #0
    // 0x131f3c0: b               #0x131f3d4
    // 0x131f3c4: r1 = LoadInt32Instr(r0)
    //     0x131f3c4: sbfx            x1, x0, #1, #0x1f
    //     0x131f3c8: tbz             w0, #0, #0x131f3d0
    //     0x131f3cc: ldur            x1, [x0, #7]
    // 0x131f3d0: mov             x0, x1
    // 0x131f3d4: ldur            x2, [fp, #-8]
    // 0x131f3d8: stur            x0, [fp, #-0x28]
    // 0x131f3dc: LoadField: r1 = r2->field_f
    //     0x131f3dc: ldur            w1, [x2, #0xf]
    // 0x131f3e0: DecompressPointer r1
    //     0x131f3e0: add             x1, x1, HEAP, lsl #32
    // 0x131f3e4: r0 = controller()
    //     0x131f3e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131f3e8: mov             x1, x0
    // 0x131f3ec: r0 = headerConfigData()
    //     0x131f3ec: bl              #0x8a3724  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_address_controller.dart] CheckoutRequestAddressController::headerConfigData
    // 0x131f3f0: LoadField: r1 = r0->field_3f
    //     0x131f3f0: ldur            w1, [x0, #0x3f]
    // 0x131f3f4: DecompressPointer r1
    //     0x131f3f4: add             x1, x1, HEAP, lsl #32
    // 0x131f3f8: cmp             w1, NULL
    // 0x131f3fc: b.ne            #0x131f408
    // 0x131f400: r0 = Null
    //     0x131f400: mov             x0, NULL
    // 0x131f404: b               #0x131f42c
    // 0x131f408: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x131f408: ldur            w0, [x1, #0x17]
    // 0x131f40c: DecompressPointer r0
    //     0x131f40c: add             x0, x0, HEAP, lsl #32
    // 0x131f410: cmp             w0, NULL
    // 0x131f414: b.ne            #0x131f420
    // 0x131f418: r0 = Null
    //     0x131f418: mov             x0, NULL
    // 0x131f41c: b               #0x131f42c
    // 0x131f420: LoadField: r1 = r0->field_f
    //     0x131f420: ldur            w1, [x0, #0xf]
    // 0x131f424: DecompressPointer r1
    //     0x131f424: add             x1, x1, HEAP, lsl #32
    // 0x131f428: mov             x0, x1
    // 0x131f42c: cmp             w0, NULL
    // 0x131f430: b.ne            #0x131f43c
    // 0x131f434: r0 = 0
    //     0x131f434: movz            x0, #0
    // 0x131f438: b               #0x131f44c
    // 0x131f43c: r1 = LoadInt32Instr(r0)
    //     0x131f43c: sbfx            x1, x0, #1, #0x1f
    //     0x131f440: tbz             w0, #0, #0x131f448
    //     0x131f444: ldur            x1, [x0, #7]
    // 0x131f448: mov             x0, x1
    // 0x131f44c: stur            x0, [fp, #-0x30]
    // 0x131f450: r0 = Color()
    //     0x131f450: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x131f454: mov             x1, x0
    // 0x131f458: r0 = Instance_ColorSpace
    //     0x131f458: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x131f45c: StoreField: r1->field_27 = r0
    //     0x131f45c: stur            w0, [x1, #0x27]
    // 0x131f460: d0 = 0.300000
    //     0x131f460: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x131f464: ldr             d0, [x17, #0x658]
    // 0x131f468: StoreField: r1->field_7 = d0
    //     0x131f468: stur            d0, [x1, #7]
    // 0x131f46c: ldur            x0, [fp, #-0x20]
    // 0x131f470: ubfx            x0, x0, #0, #0x20
    // 0x131f474: and             w2, w0, #0xff
    // 0x131f478: ubfx            x2, x2, #0, #0x20
    // 0x131f47c: scvtf           d0, x2
    // 0x131f480: d1 = 255.000000
    //     0x131f480: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x131f484: fdiv            d2, d0, d1
    // 0x131f488: StoreField: r1->field_f = d2
    //     0x131f488: stur            d2, [x1, #0xf]
    // 0x131f48c: ldur            x0, [fp, #-0x28]
    // 0x131f490: ubfx            x0, x0, #0, #0x20
    // 0x131f494: and             w2, w0, #0xff
    // 0x131f498: ubfx            x2, x2, #0, #0x20
    // 0x131f49c: scvtf           d0, x2
    // 0x131f4a0: fdiv            d2, d0, d1
    // 0x131f4a4: ArrayStore: r1[0] = d2  ; List_8
    //     0x131f4a4: stur            d2, [x1, #0x17]
    // 0x131f4a8: ldur            x0, [fp, #-0x30]
    // 0x131f4ac: ubfx            x0, x0, #0, #0x20
    // 0x131f4b0: and             w2, w0, #0xff
    // 0x131f4b4: ubfx            x2, x2, #0, #0x20
    // 0x131f4b8: scvtf           d0, x2
    // 0x131f4bc: fdiv            d2, d0, d1
    // 0x131f4c0: StoreField: r1->field_1f = d2
    //     0x131f4c0: stur            d2, [x1, #0x1f]
    // 0x131f4c4: mov             x3, x1
    // 0x131f4c8: ldur            x2, [fp, #-8]
    // 0x131f4cc: ldur            x1, [fp, #-0x10]
    // 0x131f4d0: ldur            x0, [fp, #-0x18]
    // 0x131f4d4: r16 = <Color>
    //     0x131f4d4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x131f4d8: ldr             x16, [x16, #0xf80]
    // 0x131f4dc: stp             x3, x16, [SP]
    // 0x131f4e0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x131f4e0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x131f4e4: r0 = all()
    //     0x131f4e4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x131f4e8: stur            x0, [fp, #-0x38]
    // 0x131f4ec: r16 = <RoundedRectangleBorder>
    //     0x131f4ec: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x131f4f0: ldr             x16, [x16, #0xf78]
    // 0x131f4f4: r30 = Instance_RoundedRectangleBorder
    //     0x131f4f4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x131f4f8: ldr             lr, [lr, #0xd68]
    // 0x131f4fc: stp             lr, x16, [SP]
    // 0x131f500: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x131f500: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x131f504: r0 = all()
    //     0x131f504: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x131f508: stur            x0, [fp, #-0x40]
    // 0x131f50c: r0 = ButtonStyle()
    //     0x131f50c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x131f510: mov             x1, x0
    // 0x131f514: ldur            x0, [fp, #-0x38]
    // 0x131f518: stur            x1, [fp, #-0x48]
    // 0x131f51c: StoreField: r1->field_b = r0
    //     0x131f51c: stur            w0, [x1, #0xb]
    // 0x131f520: ldur            x0, [fp, #-0x18]
    // 0x131f524: StoreField: r1->field_23 = r0
    //     0x131f524: stur            w0, [x1, #0x23]
    // 0x131f528: ldur            x0, [fp, #-0x40]
    // 0x131f52c: StoreField: r1->field_43 = r0
    //     0x131f52c: stur            w0, [x1, #0x43]
    // 0x131f530: r0 = TextButtonThemeData()
    //     0x131f530: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x131f534: mov             x2, x0
    // 0x131f538: ldur            x0, [fp, #-0x48]
    // 0x131f53c: stur            x2, [fp, #-0x18]
    // 0x131f540: StoreField: r2->field_7 = r0
    //     0x131f540: stur            w0, [x2, #7]
    // 0x131f544: ldur            x0, [fp, #-8]
    // 0x131f548: LoadField: r1 = r0->field_13
    //     0x131f548: ldur            w1, [x0, #0x13]
    // 0x131f54c: DecompressPointer r1
    //     0x131f54c: add             x1, x1, HEAP, lsl #32
    // 0x131f550: r0 = of()
    //     0x131f550: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x131f554: LoadField: r1 = r0->field_87
    //     0x131f554: ldur            w1, [x0, #0x87]
    // 0x131f558: DecompressPointer r1
    //     0x131f558: add             x1, x1, HEAP, lsl #32
    // 0x131f55c: LoadField: r0 = r1->field_7
    //     0x131f55c: ldur            w0, [x1, #7]
    // 0x131f560: DecompressPointer r0
    //     0x131f560: add             x0, x0, HEAP, lsl #32
    // 0x131f564: r16 = 16.000000
    //     0x131f564: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x131f568: ldr             x16, [x16, #0x188]
    // 0x131f56c: r30 = Instance_Color
    //     0x131f56c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x131f570: stp             lr, x16, [SP]
    // 0x131f574: mov             x1, x0
    // 0x131f578: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x131f578: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x131f57c: ldr             x4, [x4, #0xaa0]
    // 0x131f580: r0 = copyWith()
    //     0x131f580: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x131f584: stur            x0, [fp, #-0x38]
    // 0x131f588: r0 = Text()
    //     0x131f588: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x131f58c: mov             x3, x0
    // 0x131f590: r0 = "SUBMIT"
    //     0x131f590: add             x0, PP, #0x36, lsl #12  ; [pp+0x366c0] "SUBMIT"
    //     0x131f594: ldr             x0, [x0, #0x6c0]
    // 0x131f598: stur            x3, [fp, #-0x40]
    // 0x131f59c: StoreField: r3->field_b = r0
    //     0x131f59c: stur            w0, [x3, #0xb]
    // 0x131f5a0: ldur            x0, [fp, #-0x38]
    // 0x131f5a4: StoreField: r3->field_13 = r0
    //     0x131f5a4: stur            w0, [x3, #0x13]
    // 0x131f5a8: ldur            x2, [fp, #-8]
    // 0x131f5ac: r1 = Function '<anonymous closure>':.
    //     0x131f5ac: add             x1, PP, #0x36, lsl #12  ; [pp+0x366c8] AnonymousClosure: (0x131efa4), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::bottomNavigationBar (0x1369680)
    //     0x131f5b0: ldr             x1, [x1, #0x6c8]
    // 0x131f5b4: r0 = AllocateClosure()
    //     0x131f5b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x131f5b8: stur            x0, [fp, #-8]
    // 0x131f5bc: r0 = TextButton()
    //     0x131f5bc: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x131f5c0: mov             x1, x0
    // 0x131f5c4: ldur            x0, [fp, #-8]
    // 0x131f5c8: stur            x1, [fp, #-0x38]
    // 0x131f5cc: StoreField: r1->field_b = r0
    //     0x131f5cc: stur            w0, [x1, #0xb]
    // 0x131f5d0: r0 = false
    //     0x131f5d0: add             x0, NULL, #0x30  ; false
    // 0x131f5d4: StoreField: r1->field_27 = r0
    //     0x131f5d4: stur            w0, [x1, #0x27]
    // 0x131f5d8: r0 = true
    //     0x131f5d8: add             x0, NULL, #0x20  ; true
    // 0x131f5dc: StoreField: r1->field_2f = r0
    //     0x131f5dc: stur            w0, [x1, #0x2f]
    // 0x131f5e0: ldur            x0, [fp, #-0x40]
    // 0x131f5e4: StoreField: r1->field_37 = r0
    //     0x131f5e4: stur            w0, [x1, #0x37]
    // 0x131f5e8: r0 = Instance_ValueKey
    //     0x131f5e8: add             x0, PP, #0x36, lsl #12  ; [pp+0x366d0] Obj!ValueKey<String>@d5b331
    //     0x131f5ec: ldr             x0, [x0, #0x6d0]
    // 0x131f5f0: StoreField: r1->field_7 = r0
    //     0x131f5f0: stur            w0, [x1, #7]
    // 0x131f5f4: r0 = TextButtonTheme()
    //     0x131f5f4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x131f5f8: mov             x1, x0
    // 0x131f5fc: ldur            x0, [fp, #-0x18]
    // 0x131f600: stur            x1, [fp, #-8]
    // 0x131f604: StoreField: r1->field_f = r0
    //     0x131f604: stur            w0, [x1, #0xf]
    // 0x131f608: ldur            x0, [fp, #-0x38]
    // 0x131f60c: StoreField: r1->field_b = r0
    //     0x131f60c: stur            w0, [x1, #0xb]
    // 0x131f610: r0 = SizedBox()
    //     0x131f610: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x131f614: mov             x1, x0
    // 0x131f618: r0 = inf
    //     0x131f618: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x131f61c: ldr             x0, [x0, #0x9f8]
    // 0x131f620: stur            x1, [fp, #-0x18]
    // 0x131f624: StoreField: r1->field_f = r0
    //     0x131f624: stur            w0, [x1, #0xf]
    // 0x131f628: ldur            x0, [fp, #-8]
    // 0x131f62c: StoreField: r1->field_b = r0
    //     0x131f62c: stur            w0, [x1, #0xb]
    // 0x131f630: r0 = Padding()
    //     0x131f630: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x131f634: mov             x3, x0
    // 0x131f638: r0 = Instance_EdgeInsets
    //     0x131f638: add             x0, PP, #0x36, lsl #12  ; [pp+0x366d8] Obj!EdgeInsets@d59a81
    //     0x131f63c: ldr             x0, [x0, #0x6d8]
    // 0x131f640: stur            x3, [fp, #-8]
    // 0x131f644: StoreField: r3->field_f = r0
    //     0x131f644: stur            w0, [x3, #0xf]
    // 0x131f648: ldur            x0, [fp, #-0x18]
    // 0x131f64c: StoreField: r3->field_b = r0
    //     0x131f64c: stur            w0, [x3, #0xb]
    // 0x131f650: r1 = Null
    //     0x131f650: mov             x1, NULL
    // 0x131f654: r2 = 4
    //     0x131f654: movz            x2, #0x4
    // 0x131f658: r0 = AllocateArray()
    //     0x131f658: bl              #0x16f7198  ; AllocateArrayStub
    // 0x131f65c: stur            x0, [fp, #-0x18]
    // 0x131f660: r16 = Instance_Padding
    //     0x131f660: add             x16, PP, #0x36, lsl #12  ; [pp+0x366e0] Obj!Padding@d68561
    //     0x131f664: ldr             x16, [x16, #0x6e0]
    // 0x131f668: StoreField: r0->field_f = r16
    //     0x131f668: stur            w16, [x0, #0xf]
    // 0x131f66c: ldur            x1, [fp, #-8]
    // 0x131f670: StoreField: r0->field_13 = r1
    //     0x131f670: stur            w1, [x0, #0x13]
    // 0x131f674: r1 = <Widget>
    //     0x131f674: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x131f678: r0 = AllocateGrowableArray()
    //     0x131f678: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x131f67c: mov             x1, x0
    // 0x131f680: ldur            x0, [fp, #-0x18]
    // 0x131f684: stur            x1, [fp, #-8]
    // 0x131f688: StoreField: r1->field_f = r0
    //     0x131f688: stur            w0, [x1, #0xf]
    // 0x131f68c: r0 = 4
    //     0x131f68c: movz            x0, #0x4
    // 0x131f690: StoreField: r1->field_b = r0
    //     0x131f690: stur            w0, [x1, #0xb]
    // 0x131f694: r0 = Wrap()
    //     0x131f694: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0x131f698: mov             x1, x0
    // 0x131f69c: r0 = Instance_Axis
    //     0x131f69c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x131f6a0: stur            x1, [fp, #-0x18]
    // 0x131f6a4: StoreField: r1->field_f = r0
    //     0x131f6a4: stur            w0, [x1, #0xf]
    // 0x131f6a8: r0 = Instance_WrapAlignment
    //     0x131f6a8: add             x0, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0x131f6ac: ldr             x0, [x0, #0x6e8]
    // 0x131f6b0: StoreField: r1->field_13 = r0
    //     0x131f6b0: stur            w0, [x1, #0x13]
    // 0x131f6b4: ArrayStore: r1[0] = rZR  ; List_8
    //     0x131f6b4: stur            xzr, [x1, #0x17]
    // 0x131f6b8: StoreField: r1->field_1f = r0
    //     0x131f6b8: stur            w0, [x1, #0x1f]
    // 0x131f6bc: StoreField: r1->field_23 = rZR
    //     0x131f6bc: stur            xzr, [x1, #0x23]
    // 0x131f6c0: r0 = Instance_WrapCrossAlignment
    //     0x131f6c0: add             x0, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0x131f6c4: ldr             x0, [x0, #0x6f0]
    // 0x131f6c8: StoreField: r1->field_2b = r0
    //     0x131f6c8: stur            w0, [x1, #0x2b]
    // 0x131f6cc: r0 = Instance_VerticalDirection
    //     0x131f6cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x131f6d0: ldr             x0, [x0, #0xa20]
    // 0x131f6d4: StoreField: r1->field_33 = r0
    //     0x131f6d4: stur            w0, [x1, #0x33]
    // 0x131f6d8: r0 = Instance_Clip
    //     0x131f6d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x131f6dc: ldr             x0, [x0, #0x38]
    // 0x131f6e0: StoreField: r1->field_37 = r0
    //     0x131f6e0: stur            w0, [x1, #0x37]
    // 0x131f6e4: ldur            x0, [fp, #-8]
    // 0x131f6e8: StoreField: r1->field_b = r0
    //     0x131f6e8: stur            w0, [x1, #0xb]
    // 0x131f6ec: r0 = Padding()
    //     0x131f6ec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x131f6f0: ldur            x1, [fp, #-0x10]
    // 0x131f6f4: StoreField: r0->field_f = r1
    //     0x131f6f4: stur            w1, [x0, #0xf]
    // 0x131f6f8: ldur            x1, [fp, #-0x18]
    // 0x131f6fc: StoreField: r0->field_b = r1
    //     0x131f6fc: stur            w1, [x0, #0xb]
    // 0x131f700: LeaveFrame
    //     0x131f700: mov             SP, fp
    //     0x131f704: ldp             fp, lr, [SP], #0x10
    // 0x131f708: ret
    //     0x131f708: ret             
    // 0x131f70c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131f70c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131f710: b               #0x131f064
  }
  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1369680, size: 0x64
    // 0x1369680: EnterFrame
    //     0x1369680: stp             fp, lr, [SP, #-0x10]!
    //     0x1369684: mov             fp, SP
    // 0x1369688: AllocStack(0x18)
    //     0x1369688: sub             SP, SP, #0x18
    // 0x136968c: SetupParameters(RatingReviewOrderPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x136968c: stur            x1, [fp, #-8]
    //     0x1369690: stur            x2, [fp, #-0x10]
    // 0x1369694: r1 = 2
    //     0x1369694: movz            x1, #0x2
    // 0x1369698: r0 = AllocateContext()
    //     0x1369698: bl              #0x16f6108  ; AllocateContextStub
    // 0x136969c: mov             x1, x0
    // 0x13696a0: ldur            x0, [fp, #-8]
    // 0x13696a4: stur            x1, [fp, #-0x18]
    // 0x13696a8: StoreField: r1->field_f = r0
    //     0x13696a8: stur            w0, [x1, #0xf]
    // 0x13696ac: ldur            x0, [fp, #-0x10]
    // 0x13696b0: StoreField: r1->field_13 = r0
    //     0x13696b0: stur            w0, [x1, #0x13]
    // 0x13696b4: r0 = Obx()
    //     0x13696b4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13696b8: ldur            x2, [fp, #-0x18]
    // 0x13696bc: r1 = Function '<anonymous closure>':.
    //     0x13696bc: add             x1, PP, #0x36, lsl #12  ; [pp+0x366b8] AnonymousClosure: (0x131f03c), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::bottomNavigationBar (0x1369680)
    //     0x13696c0: ldr             x1, [x1, #0x6b8]
    // 0x13696c4: stur            x0, [fp, #-8]
    // 0x13696c8: r0 = AllocateClosure()
    //     0x13696c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13696cc: mov             x1, x0
    // 0x13696d0: ldur            x0, [fp, #-8]
    // 0x13696d4: StoreField: r0->field_b = r1
    //     0x13696d4: stur            w1, [x0, #0xb]
    // 0x13696d8: LeaveFrame
    //     0x13696d8: mov             SP, fp
    //     0x13696dc: ldp             fp, lr, [SP], #0x10
    // 0x13696e0: ret
    //     0x13696e0: ret             
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x140a92c, size: 0x7b8
    // 0x140a92c: EnterFrame
    //     0x140a92c: stp             fp, lr, [SP, #-0x10]!
    //     0x140a930: mov             fp, SP
    // 0x140a934: AllocStack(0x38)
    //     0x140a934: sub             SP, SP, #0x38
    // 0x140a938: SetupParameters(RatingReviewOrderPage this /* r1 */)
    //     0x140a938: stur            NULL, [fp, #-8]
    //     0x140a93c: movz            x0, #0
    //     0x140a940: add             x1, fp, w0, sxtw #2
    //     0x140a944: ldr             x1, [x1, #0x10]
    //     0x140a948: ldur            w2, [x1, #0x17]
    //     0x140a94c: add             x2, x2, HEAP, lsl #32
    //     0x140a950: stur            x2, [fp, #-0x10]
    // 0x140a954: CheckStackOverflow
    //     0x140a954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140a958: cmp             SP, x16
    //     0x140a95c: b.ls            #0x140b0d8
    // 0x140a960: InitAsync() -> Future<void?>
    //     0x140a960: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x140a964: bl              #0x6326e0  ; InitAsyncStub
    // 0x140a968: ldur            x0, [fp, #-0x10]
    // 0x140a96c: LoadField: r2 = r0->field_b
    //     0x140a96c: ldur            w2, [x0, #0xb]
    // 0x140a970: DecompressPointer r2
    //     0x140a970: add             x2, x2, HEAP, lsl #32
    // 0x140a974: stur            x2, [fp, #-0x18]
    // 0x140a978: LoadField: r1 = r2->field_f
    //     0x140a978: ldur            w1, [x2, #0xf]
    // 0x140a97c: DecompressPointer r1
    //     0x140a97c: add             x1, x1, HEAP, lsl #32
    // 0x140a980: r0 = controller()
    //     0x140a980: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140a984: LoadField: r1 = r0->field_97
    //     0x140a984: ldur            w1, [x0, #0x97]
    // 0x140a988: DecompressPointer r1
    //     0x140a988: add             x1, x1, HEAP, lsl #32
    // 0x140a98c: ldur            x0, [fp, #-0x10]
    // 0x140a990: LoadField: r2 = r0->field_f
    //     0x140a990: ldur            w2, [x0, #0xf]
    // 0x140a994: DecompressPointer r2
    //     0x140a994: add             x2, x2, HEAP, lsl #32
    // 0x140a998: stur            x2, [fp, #-0x20]
    // 0x140a99c: r0 = value()
    //     0x140a99c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140a9a0: r1 = LoadClassIdInstr(r0)
    //     0x140a9a0: ldur            x1, [x0, #-1]
    //     0x140a9a4: ubfx            x1, x1, #0xc, #0x14
    // 0x140a9a8: ldur            x16, [fp, #-0x20]
    // 0x140a9ac: stp             x16, x0, [SP]
    // 0x140a9b0: mov             x0, x1
    // 0x140a9b4: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140a9b4: sub             lr, x0, #0xb7
    //     0x140a9b8: ldr             lr, [x21, lr, lsl #3]
    //     0x140a9bc: blr             lr
    // 0x140a9c0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x140a9c0: ldur            w1, [x0, #0x17]
    // 0x140a9c4: DecompressPointer r1
    //     0x140a9c4: add             x1, x1, HEAP, lsl #32
    // 0x140a9c8: cmp             w1, NULL
    // 0x140a9cc: b.ne            #0x140a9d8
    // 0x140a9d0: r0 = Null
    //     0x140a9d0: mov             x0, NULL
    // 0x140a9d4: b               #0x140aa00
    // 0x140a9d8: r0 = LoadClassIdInstr(r1)
    //     0x140a9d8: ldur            x0, [x1, #-1]
    //     0x140a9dc: ubfx            x0, x0, #0xc, #0x14
    // 0x140a9e0: r0 = GDT[cid_x0 + -0xe96]()
    //     0x140a9e0: sub             lr, x0, #0xe96
    //     0x140a9e4: ldr             lr, [x21, lr, lsl #3]
    //     0x140a9e8: blr             lr
    // 0x140a9ec: LoadField: r1 = r0->field_7
    //     0x140a9ec: ldur            w1, [x0, #7]
    // 0x140a9f0: cbnz            w1, #0x140a9fc
    // 0x140a9f4: r0 = false
    //     0x140a9f4: add             x0, NULL, #0x30  ; false
    // 0x140a9f8: b               #0x140aa00
    // 0x140a9fc: r0 = true
    //     0x140a9fc: add             x0, NULL, #0x20  ; true
    // 0x140aa00: cmp             w0, NULL
    // 0x140aa04: b.eq            #0x140ad5c
    // 0x140aa08: tbnz            w0, #4, #0x140ad5c
    // 0x140aa0c: ldur            x0, [fp, #-0x10]
    // 0x140aa10: ldur            x2, [fp, #-0x18]
    // 0x140aa14: LoadField: r1 = r2->field_f
    //     0x140aa14: ldur            w1, [x2, #0xf]
    // 0x140aa18: DecompressPointer r1
    //     0x140aa18: add             x1, x1, HEAP, lsl #32
    // 0x140aa1c: r0 = controller()
    //     0x140aa1c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140aa20: LoadField: r1 = r0->field_97
    //     0x140aa20: ldur            w1, [x0, #0x97]
    // 0x140aa24: DecompressPointer r1
    //     0x140aa24: add             x1, x1, HEAP, lsl #32
    // 0x140aa28: ldur            x0, [fp, #-0x10]
    // 0x140aa2c: LoadField: r2 = r0->field_f
    //     0x140aa2c: ldur            w2, [x0, #0xf]
    // 0x140aa30: DecompressPointer r2
    //     0x140aa30: add             x2, x2, HEAP, lsl #32
    // 0x140aa34: stur            x2, [fp, #-0x20]
    // 0x140aa38: r0 = value()
    //     0x140aa38: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140aa3c: r1 = LoadClassIdInstr(r0)
    //     0x140aa3c: ldur            x1, [x0, #-1]
    //     0x140aa40: ubfx            x1, x1, #0xc, #0x14
    // 0x140aa44: ldur            x16, [fp, #-0x20]
    // 0x140aa48: stp             x16, x0, [SP]
    // 0x140aa4c: mov             x0, x1
    // 0x140aa50: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140aa50: sub             lr, x0, #0xb7
    //     0x140aa54: ldr             lr, [x21, lr, lsl #3]
    //     0x140aa58: blr             lr
    // 0x140aa5c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x140aa5c: ldur            w1, [x0, #0x17]
    // 0x140aa60: DecompressPointer r1
    //     0x140aa60: add             x1, x1, HEAP, lsl #32
    // 0x140aa64: cmp             w1, NULL
    // 0x140aa68: b.ne            #0x140aa74
    // 0x140aa6c: r0 = Null
    //     0x140aa6c: mov             x0, NULL
    // 0x140aa70: b               #0x140aa88
    // 0x140aa74: r0 = LoadClassIdInstr(r1)
    //     0x140aa74: ldur            x0, [x1, #-1]
    //     0x140aa78: ubfx            x0, x0, #0xc, #0x14
    // 0x140aa7c: r0 = GDT[cid_x0 + -0xe96]()
    //     0x140aa7c: sub             lr, x0, #0xe96
    //     0x140aa80: ldr             lr, [x21, lr, lsl #3]
    //     0x140aa84: blr             lr
    // 0x140aa88: cmp             w0, NULL
    // 0x140aa8c: b.ne            #0x140aa98
    // 0x140aa90: r1 = ""
    //     0x140aa90: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x140aa94: b               #0x140aa9c
    // 0x140aa98: mov             x1, x0
    // 0x140aa9c: r0 = lookupMimeType()
    //     0x140aa9c: bl              #0x8ab24c  ; [package:mime/src/mime_type.dart] ::lookupMimeType
    // 0x140aaa0: cmp             w0, NULL
    // 0x140aaa4: b.ne            #0x140aab0
    // 0x140aaa8: r3 = Null
    //     0x140aaa8: mov             x3, NULL
    // 0x140aaac: b               #0x140aad8
    // 0x140aab0: r1 = LoadClassIdInstr(r0)
    //     0x140aab0: ldur            x1, [x0, #-1]
    //     0x140aab4: ubfx            x1, x1, #0xc, #0x14
    // 0x140aab8: mov             x16, x0
    // 0x140aabc: mov             x0, x1
    // 0x140aac0: mov             x1, x16
    // 0x140aac4: r2 = "/"
    //     0x140aac4: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x140aac8: r0 = GDT[cid_x0 + -0xffc]()
    //     0x140aac8: sub             lr, x0, #0xffc
    //     0x140aacc: ldr             lr, [x21, lr, lsl #3]
    //     0x140aad0: blr             lr
    // 0x140aad4: mov             x3, x0
    // 0x140aad8: ldur            x0, [fp, #-0x10]
    // 0x140aadc: ldur            x2, [fp, #-0x18]
    // 0x140aae0: stur            x3, [fp, #-0x20]
    // 0x140aae4: LoadField: r1 = r2->field_f
    //     0x140aae4: ldur            w1, [x2, #0xf]
    // 0x140aae8: DecompressPointer r1
    //     0x140aae8: add             x1, x1, HEAP, lsl #32
    // 0x140aaec: r0 = controller()
    //     0x140aaec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140aaf0: LoadField: r1 = r0->field_97
    //     0x140aaf0: ldur            w1, [x0, #0x97]
    // 0x140aaf4: DecompressPointer r1
    //     0x140aaf4: add             x1, x1, HEAP, lsl #32
    // 0x140aaf8: ldur            x0, [fp, #-0x10]
    // 0x140aafc: LoadField: r2 = r0->field_f
    //     0x140aafc: ldur            w2, [x0, #0xf]
    // 0x140ab00: DecompressPointer r2
    //     0x140ab00: add             x2, x2, HEAP, lsl #32
    // 0x140ab04: stur            x2, [fp, #-0x28]
    // 0x140ab08: r0 = value()
    //     0x140ab08: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140ab0c: r1 = LoadClassIdInstr(r0)
    //     0x140ab0c: ldur            x1, [x0, #-1]
    //     0x140ab10: ubfx            x1, x1, #0xc, #0x14
    // 0x140ab14: ldur            x16, [fp, #-0x28]
    // 0x140ab18: stp             x16, x0, [SP]
    // 0x140ab1c: mov             x0, x1
    // 0x140ab20: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140ab20: sub             lr, x0, #0xb7
    //     0x140ab24: ldr             lr, [x21, lr, lsl #3]
    //     0x140ab28: blr             lr
    // 0x140ab2c: LoadField: r1 = r0->field_f
    //     0x140ab2c: ldur            w1, [x0, #0xf]
    // 0x140ab30: DecompressPointer r1
    //     0x140ab30: add             x1, x1, HEAP, lsl #32
    // 0x140ab34: cmp             w1, NULL
    // 0x140ab38: b.ne            #0x140ac78
    // 0x140ab3c: ldur            x2, [fp, #-0x20]
    // 0x140ab40: cmp             w2, NULL
    // 0x140ab44: b.ne            #0x140ab50
    // 0x140ab48: r0 = Null
    //     0x140ab48: mov             x0, NULL
    // 0x140ab4c: b               #0x140ab7c
    // 0x140ab50: LoadField: r0 = r2->field_b
    //     0x140ab50: ldur            w0, [x2, #0xb]
    // 0x140ab54: r1 = LoadInt32Instr(r0)
    //     0x140ab54: sbfx            x1, x0, #1, #0x1f
    // 0x140ab58: mov             x0, x1
    // 0x140ab5c: r1 = 0
    //     0x140ab5c: movz            x1, #0
    // 0x140ab60: cmp             x1, x0
    // 0x140ab64: b.hs            #0x140b0e0
    // 0x140ab68: LoadField: r0 = r2->field_f
    //     0x140ab68: ldur            w0, [x2, #0xf]
    // 0x140ab6c: DecompressPointer r0
    //     0x140ab6c: add             x0, x0, HEAP, lsl #32
    // 0x140ab70: LoadField: r1 = r0->field_f
    //     0x140ab70: ldur            w1, [x0, #0xf]
    // 0x140ab74: DecompressPointer r1
    //     0x140ab74: add             x1, x1, HEAP, lsl #32
    // 0x140ab78: mov             x0, x1
    // 0x140ab7c: r1 = LoadClassIdInstr(r0)
    //     0x140ab7c: ldur            x1, [x0, #-1]
    //     0x140ab80: ubfx            x1, x1, #0xc, #0x14
    // 0x140ab84: r16 = "video"
    //     0x140ab84: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x140ab88: ldr             x16, [x16, #0xb50]
    // 0x140ab8c: stp             x16, x0, [SP]
    // 0x140ab90: mov             x0, x1
    // 0x140ab94: mov             lr, x0
    // 0x140ab98: ldr             lr, [x21, lr, lsl #3]
    // 0x140ab9c: blr             lr
    // 0x140aba0: tbnz            w0, #4, #0x140ac10
    // 0x140aba4: ldur            x0, [fp, #-0x18]
    // 0x140aba8: LoadField: r1 = r0->field_f
    //     0x140aba8: ldur            w1, [x0, #0xf]
    // 0x140abac: DecompressPointer r1
    //     0x140abac: add             x1, x1, HEAP, lsl #32
    // 0x140abb0: r0 = controller()
    //     0x140abb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140abb4: LoadField: r2 = r0->field_9f
    //     0x140abb4: ldur            w2, [x0, #0x9f]
    // 0x140abb8: DecompressPointer r2
    //     0x140abb8: add             x2, x2, HEAP, lsl #32
    // 0x140abbc: ldur            x0, [fp, #-0x18]
    // 0x140abc0: stur            x2, [fp, #-0x20]
    // 0x140abc4: LoadField: r1 = r0->field_f
    //     0x140abc4: ldur            w1, [x0, #0xf]
    // 0x140abc8: DecompressPointer r1
    //     0x140abc8: add             x1, x1, HEAP, lsl #32
    // 0x140abcc: r0 = controller()
    //     0x140abcc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140abd0: LoadField: r1 = r0->field_9f
    //     0x140abd0: ldur            w1, [x0, #0x9f]
    // 0x140abd4: DecompressPointer r1
    //     0x140abd4: add             x1, x1, HEAP, lsl #32
    // 0x140abd8: r0 = value()
    //     0x140abd8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140abdc: r1 = LoadInt32Instr(r0)
    //     0x140abdc: sbfx            x1, x0, #1, #0x1f
    //     0x140abe0: tbz             w0, #0, #0x140abe8
    //     0x140abe4: ldur            x1, [x0, #7]
    // 0x140abe8: sub             x2, x1, #1
    // 0x140abec: r0 = BoxInt64Instr(r2)
    //     0x140abec: sbfiz           x0, x2, #1, #0x1f
    //     0x140abf0: cmp             x2, x0, asr #1
    //     0x140abf4: b.eq            #0x140ac00
    //     0x140abf8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x140abfc: stur            x2, [x0, #7]
    // 0x140ac00: ldur            x1, [fp, #-0x20]
    // 0x140ac04: mov             x2, x0
    // 0x140ac08: r0 = value=()
    //     0x140ac08: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140ac0c: b               #0x140ac78
    // 0x140ac10: ldur            x0, [fp, #-0x18]
    // 0x140ac14: LoadField: r1 = r0->field_f
    //     0x140ac14: ldur            w1, [x0, #0xf]
    // 0x140ac18: DecompressPointer r1
    //     0x140ac18: add             x1, x1, HEAP, lsl #32
    // 0x140ac1c: r0 = controller()
    //     0x140ac1c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140ac20: LoadField: r2 = r0->field_9b
    //     0x140ac20: ldur            w2, [x0, #0x9b]
    // 0x140ac24: DecompressPointer r2
    //     0x140ac24: add             x2, x2, HEAP, lsl #32
    // 0x140ac28: ldur            x0, [fp, #-0x18]
    // 0x140ac2c: stur            x2, [fp, #-0x20]
    // 0x140ac30: LoadField: r1 = r0->field_f
    //     0x140ac30: ldur            w1, [x0, #0xf]
    // 0x140ac34: DecompressPointer r1
    //     0x140ac34: add             x1, x1, HEAP, lsl #32
    // 0x140ac38: r0 = controller()
    //     0x140ac38: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140ac3c: LoadField: r1 = r0->field_9b
    //     0x140ac3c: ldur            w1, [x0, #0x9b]
    // 0x140ac40: DecompressPointer r1
    //     0x140ac40: add             x1, x1, HEAP, lsl #32
    // 0x140ac44: r0 = value()
    //     0x140ac44: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140ac48: r1 = LoadInt32Instr(r0)
    //     0x140ac48: sbfx            x1, x0, #1, #0x1f
    //     0x140ac4c: tbz             w0, #0, #0x140ac54
    //     0x140ac50: ldur            x1, [x0, #7]
    // 0x140ac54: sub             x2, x1, #1
    // 0x140ac58: r0 = BoxInt64Instr(r2)
    //     0x140ac58: sbfiz           x0, x2, #1, #0x1f
    //     0x140ac5c: cmp             x2, x0, asr #1
    //     0x140ac60: b.eq            #0x140ac6c
    //     0x140ac64: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x140ac68: stur            x2, [x0, #7]
    // 0x140ac6c: ldur            x1, [fp, #-0x20]
    // 0x140ac70: mov             x2, x0
    // 0x140ac74: r0 = value=()
    //     0x140ac74: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140ac78: ldur            x2, [fp, #-0x10]
    // 0x140ac7c: ldur            x0, [fp, #-0x18]
    // 0x140ac80: LoadField: r1 = r0->field_f
    //     0x140ac80: ldur            w1, [x0, #0xf]
    // 0x140ac84: DecompressPointer r1
    //     0x140ac84: add             x1, x1, HEAP, lsl #32
    // 0x140ac88: r0 = controller()
    //     0x140ac88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140ac8c: LoadField: r1 = r0->field_97
    //     0x140ac8c: ldur            w1, [x0, #0x97]
    // 0x140ac90: DecompressPointer r1
    //     0x140ac90: add             x1, x1, HEAP, lsl #32
    // 0x140ac94: ldur            x0, [fp, #-0x10]
    // 0x140ac98: LoadField: r2 = r0->field_f
    //     0x140ac98: ldur            w2, [x0, #0xf]
    // 0x140ac9c: DecompressPointer r2
    //     0x140ac9c: add             x2, x2, HEAP, lsl #32
    // 0x140aca0: stur            x2, [fp, #-0x20]
    // 0x140aca4: r0 = value()
    //     0x140aca4: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140aca8: r1 = LoadClassIdInstr(r0)
    //     0x140aca8: ldur            x1, [x0, #-1]
    //     0x140acac: ubfx            x1, x1, #0xc, #0x14
    // 0x140acb0: ldur            x16, [fp, #-0x20]
    // 0x140acb4: stp             x16, x0, [SP]
    // 0x140acb8: mov             x0, x1
    // 0x140acbc: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140acbc: sub             lr, x0, #0xb7
    //     0x140acc0: ldr             lr, [x21, lr, lsl #3]
    //     0x140acc4: blr             lr
    // 0x140acc8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x140acc8: ldur            w1, [x0, #0x17]
    // 0x140accc: DecompressPointer r1
    //     0x140accc: add             x1, x1, HEAP, lsl #32
    // 0x140acd0: cmp             w1, NULL
    // 0x140acd4: b.ne            #0x140ace0
    // 0x140acd8: r2 = Null
    //     0x140acd8: mov             x2, NULL
    // 0x140acdc: b               #0x140acf8
    // 0x140ace0: r0 = LoadClassIdInstr(r1)
    //     0x140ace0: ldur            x0, [x1, #-1]
    //     0x140ace4: ubfx            x0, x0, #0xc, #0x14
    // 0x140ace8: r0 = GDT[cid_x0 + -0xfff]()
    //     0x140ace8: sub             lr, x0, #0xfff
    //     0x140acec: ldr             lr, [x21, lr, lsl #3]
    //     0x140acf0: blr             lr
    // 0x140acf4: mov             x2, x0
    // 0x140acf8: mov             x0, x2
    // 0x140acfc: stur            x2, [fp, #-0x20]
    // 0x140ad00: r1 = <int?>
    //     0x140ad00: ldr             x1, [PP, #0x1780]  ; [pp+0x1780] TypeArguments: <int?>
    // 0x140ad04: r0 = AwaitWithTypeCheck()
    //     0x140ad04: bl              #0x6870b0  ; AwaitWithTypeCheckStub
    // 0x140ad08: cmp             w0, NULL
    // 0x140ad0c: b.ne            #0x140ad18
    // 0x140ad10: r2 = 0
    //     0x140ad10: movz            x2, #0
    // 0x140ad14: b               #0x140ad1c
    // 0x140ad18: mov             x2, x0
    // 0x140ad1c: ldur            x0, [fp, #-0x18]
    // 0x140ad20: stur            x2, [fp, #-0x20]
    // 0x140ad24: LoadField: r1 = r0->field_f
    //     0x140ad24: ldur            w1, [x0, #0xf]
    // 0x140ad28: DecompressPointer r1
    //     0x140ad28: add             x1, x1, HEAP, lsl #32
    // 0x140ad2c: r0 = controller()
    //     0x140ad2c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140ad30: LoadField: r2 = r0->field_a3
    //     0x140ad30: ldur            w2, [x0, #0xa3]
    // 0x140ad34: DecompressPointer r2
    //     0x140ad34: add             x2, x2, HEAP, lsl #32
    // 0x140ad38: mov             x1, x2
    // 0x140ad3c: stur            x2, [fp, #-0x28]
    // 0x140ad40: r0 = value()
    //     0x140ad40: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140ad44: ldur            x16, [fp, #-0x20]
    // 0x140ad48: stp             x16, x0, [SP]
    // 0x140ad4c: r0 = -()
    //     0x140ad4c: bl              #0x16f28bc  ; [dart:core] _Double::-
    // 0x140ad50: ldur            x1, [fp, #-0x28]
    // 0x140ad54: mov             x2, x0
    // 0x140ad58: r0 = value=()
    //     0x140ad58: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140ad5c: ldur            x2, [fp, #-0x10]
    // 0x140ad60: ldur            x0, [fp, #-0x18]
    // 0x140ad64: LoadField: r1 = r0->field_f
    //     0x140ad64: ldur            w1, [x0, #0xf]
    // 0x140ad68: DecompressPointer r1
    //     0x140ad68: add             x1, x1, HEAP, lsl #32
    // 0x140ad6c: r0 = controller()
    //     0x140ad6c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140ad70: LoadField: r1 = r0->field_97
    //     0x140ad70: ldur            w1, [x0, #0x97]
    // 0x140ad74: DecompressPointer r1
    //     0x140ad74: add             x1, x1, HEAP, lsl #32
    // 0x140ad78: ldur            x0, [fp, #-0x10]
    // 0x140ad7c: LoadField: r2 = r0->field_f
    //     0x140ad7c: ldur            w2, [x0, #0xf]
    // 0x140ad80: DecompressPointer r2
    //     0x140ad80: add             x2, x2, HEAP, lsl #32
    // 0x140ad84: stur            x2, [fp, #-0x20]
    // 0x140ad88: r0 = value()
    //     0x140ad88: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140ad8c: r1 = LoadClassIdInstr(r0)
    //     0x140ad8c: ldur            x1, [x0, #-1]
    //     0x140ad90: ubfx            x1, x1, #0xc, #0x14
    // 0x140ad94: ldur            x16, [fp, #-0x20]
    // 0x140ad98: stp             x16, x0, [SP]
    // 0x140ad9c: mov             x0, x1
    // 0x140ada0: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140ada0: sub             lr, x0, #0xb7
    //     0x140ada4: ldr             lr, [x21, lr, lsl #3]
    //     0x140ada8: blr             lr
    // 0x140adac: LoadField: r1 = r0->field_f
    //     0x140adac: ldur            w1, [x0, #0xf]
    // 0x140adb0: DecompressPointer r1
    //     0x140adb0: add             x1, x1, HEAP, lsl #32
    // 0x140adb4: cmp             w1, NULL
    // 0x140adb8: b.ne            #0x140ae4c
    // 0x140adbc: ldur            x0, [fp, #-0x10]
    // 0x140adc0: ldur            x2, [fp, #-0x18]
    // 0x140adc4: LoadField: r1 = r2->field_f
    //     0x140adc4: ldur            w1, [x2, #0xf]
    // 0x140adc8: DecompressPointer r1
    //     0x140adc8: add             x1, x1, HEAP, lsl #32
    // 0x140adcc: r0 = controller()
    //     0x140adcc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140add0: LoadField: r1 = r0->field_97
    //     0x140add0: ldur            w1, [x0, #0x97]
    // 0x140add4: DecompressPointer r1
    //     0x140add4: add             x1, x1, HEAP, lsl #32
    // 0x140add8: ldur            x0, [fp, #-0x10]
    // 0x140addc: LoadField: r2 = r0->field_f
    //     0x140addc: ldur            w2, [x0, #0xf]
    // 0x140ade0: DecompressPointer r2
    //     0x140ade0: add             x2, x2, HEAP, lsl #32
    // 0x140ade4: stur            x2, [fp, #-0x20]
    // 0x140ade8: r0 = value()
    //     0x140ade8: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140adec: r1 = LoadClassIdInstr(r0)
    //     0x140adec: ldur            x1, [x0, #-1]
    //     0x140adf0: ubfx            x1, x1, #0xc, #0x14
    // 0x140adf4: ldur            x16, [fp, #-0x20]
    // 0x140adf8: stp             x16, x0, [SP]
    // 0x140adfc: mov             x0, x1
    // 0x140ae00: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140ae00: sub             lr, x0, #0xb7
    //     0x140ae04: ldr             lr, [x21, lr, lsl #3]
    //     0x140ae08: blr             lr
    // 0x140ae0c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x140ae0c: ldur            w1, [x0, #0x17]
    // 0x140ae10: DecompressPointer r1
    //     0x140ae10: add             x1, x1, HEAP, lsl #32
    // 0x140ae14: cmp             w1, NULL
    // 0x140ae18: b.ne            #0x140ae24
    // 0x140ae1c: r0 = Null
    //     0x140ae1c: mov             x0, NULL
    // 0x140ae20: b               #0x140ae38
    // 0x140ae24: r0 = LoadClassIdInstr(r1)
    //     0x140ae24: ldur            x0, [x1, #-1]
    //     0x140ae28: ubfx            x0, x0, #0xc, #0x14
    // 0x140ae2c: r0 = GDT[cid_x0 + -0xe96]()
    //     0x140ae2c: sub             lr, x0, #0xe96
    //     0x140ae30: ldr             lr, [x21, lr, lsl #3]
    //     0x140ae34: blr             lr
    // 0x140ae38: cmp             w0, NULL
    // 0x140ae3c: b.ne            #0x140ae44
    // 0x140ae40: r0 = ""
    //     0x140ae40: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x140ae44: mov             x3, x0
    // 0x140ae48: b               #0x140aebc
    // 0x140ae4c: ldur            x0, [fp, #-0x10]
    // 0x140ae50: ldur            x2, [fp, #-0x18]
    // 0x140ae54: LoadField: r1 = r2->field_f
    //     0x140ae54: ldur            w1, [x2, #0xf]
    // 0x140ae58: DecompressPointer r1
    //     0x140ae58: add             x1, x1, HEAP, lsl #32
    // 0x140ae5c: r0 = controller()
    //     0x140ae5c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140ae60: LoadField: r1 = r0->field_97
    //     0x140ae60: ldur            w1, [x0, #0x97]
    // 0x140ae64: DecompressPointer r1
    //     0x140ae64: add             x1, x1, HEAP, lsl #32
    // 0x140ae68: ldur            x0, [fp, #-0x10]
    // 0x140ae6c: LoadField: r2 = r0->field_f
    //     0x140ae6c: ldur            w2, [x0, #0xf]
    // 0x140ae70: DecompressPointer r2
    //     0x140ae70: add             x2, x2, HEAP, lsl #32
    // 0x140ae74: stur            x2, [fp, #-0x20]
    // 0x140ae78: r0 = value()
    //     0x140ae78: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140ae7c: r1 = LoadClassIdInstr(r0)
    //     0x140ae7c: ldur            x1, [x0, #-1]
    //     0x140ae80: ubfx            x1, x1, #0xc, #0x14
    // 0x140ae84: ldur            x16, [fp, #-0x20]
    // 0x140ae88: stp             x16, x0, [SP]
    // 0x140ae8c: mov             x0, x1
    // 0x140ae90: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140ae90: sub             lr, x0, #0xb7
    //     0x140ae94: ldr             lr, [x21, lr, lsl #3]
    //     0x140ae98: blr             lr
    // 0x140ae9c: LoadField: r1 = r0->field_f
    //     0x140ae9c: ldur            w1, [x0, #0xf]
    // 0x140aea0: DecompressPointer r1
    //     0x140aea0: add             x1, x1, HEAP, lsl #32
    // 0x140aea4: cmp             w1, NULL
    // 0x140aea8: b.ne            #0x140aeb4
    // 0x140aeac: r0 = ""
    //     0x140aeac: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x140aeb0: b               #0x140aeb8
    // 0x140aeb4: mov             x0, x1
    // 0x140aeb8: mov             x3, x0
    // 0x140aebc: ldur            x0, [fp, #-0x10]
    // 0x140aec0: ldur            x2, [fp, #-0x18]
    // 0x140aec4: stur            x3, [fp, #-0x20]
    // 0x140aec8: LoadField: r1 = r2->field_f
    //     0x140aec8: ldur            w1, [x2, #0xf]
    // 0x140aecc: DecompressPointer r1
    //     0x140aecc: add             x1, x1, HEAP, lsl #32
    // 0x140aed0: r0 = controller()
    //     0x140aed0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140aed4: LoadField: r1 = r0->field_97
    //     0x140aed4: ldur            w1, [x0, #0x97]
    // 0x140aed8: DecompressPointer r1
    //     0x140aed8: add             x1, x1, HEAP, lsl #32
    // 0x140aedc: ldur            x0, [fp, #-0x10]
    // 0x140aee0: LoadField: r2 = r0->field_f
    //     0x140aee0: ldur            w2, [x0, #0xf]
    // 0x140aee4: DecompressPointer r2
    //     0x140aee4: add             x2, x2, HEAP, lsl #32
    // 0x140aee8: stur            x2, [fp, #-0x28]
    // 0x140aeec: r0 = value()
    //     0x140aeec: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140aef0: r1 = LoadClassIdInstr(r0)
    //     0x140aef0: ldur            x1, [x0, #-1]
    //     0x140aef4: ubfx            x1, x1, #0xc, #0x14
    // 0x140aef8: ldur            x16, [fp, #-0x28]
    // 0x140aefc: stp             x16, x0, [SP]
    // 0x140af00: mov             x0, x1
    // 0x140af04: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140af04: sub             lr, x0, #0xb7
    //     0x140af08: ldr             lr, [x21, lr, lsl #3]
    //     0x140af0c: blr             lr
    // 0x140af10: LoadField: r1 = r0->field_f
    //     0x140af10: ldur            w1, [x0, #0xf]
    // 0x140af14: DecompressPointer r1
    //     0x140af14: add             x1, x1, HEAP, lsl #32
    // 0x140af18: cmp             w1, NULL
    // 0x140af1c: b.eq            #0x140b070
    // 0x140af20: ldur            x0, [fp, #-0x10]
    // 0x140af24: ldur            x2, [fp, #-0x18]
    // 0x140af28: LoadField: r1 = r2->field_f
    //     0x140af28: ldur            w1, [x2, #0xf]
    // 0x140af2c: DecompressPointer r1
    //     0x140af2c: add             x1, x1, HEAP, lsl #32
    // 0x140af30: r0 = controller()
    //     0x140af30: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140af34: LoadField: r1 = r0->field_97
    //     0x140af34: ldur            w1, [x0, #0x97]
    // 0x140af38: DecompressPointer r1
    //     0x140af38: add             x1, x1, HEAP, lsl #32
    // 0x140af3c: ldur            x0, [fp, #-0x10]
    // 0x140af40: LoadField: r2 = r0->field_f
    //     0x140af40: ldur            w2, [x0, #0xf]
    // 0x140af44: DecompressPointer r2
    //     0x140af44: add             x2, x2, HEAP, lsl #32
    // 0x140af48: stur            x2, [fp, #-0x28]
    // 0x140af4c: r0 = value()
    //     0x140af4c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140af50: r1 = LoadClassIdInstr(r0)
    //     0x140af50: ldur            x1, [x0, #-1]
    //     0x140af54: ubfx            x1, x1, #0xc, #0x14
    // 0x140af58: ldur            x16, [fp, #-0x28]
    // 0x140af5c: stp             x16, x0, [SP]
    // 0x140af60: mov             x0, x1
    // 0x140af64: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140af64: sub             lr, x0, #0xb7
    //     0x140af68: ldr             lr, [x21, lr, lsl #3]
    //     0x140af6c: blr             lr
    // 0x140af70: LoadField: r1 = r0->field_b
    //     0x140af70: ldur            w1, [x0, #0xb]
    // 0x140af74: DecompressPointer r1
    //     0x140af74: add             x1, x1, HEAP, lsl #32
    // 0x140af78: r0 = LoadClassIdInstr(r1)
    //     0x140af78: ldur            x0, [x1, #-1]
    //     0x140af7c: ubfx            x0, x0, #0xc, #0x14
    // 0x140af80: r16 = "video"
    //     0x140af80: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x140af84: ldr             x16, [x16, #0xb50]
    // 0x140af88: stp             x16, x1, [SP]
    // 0x140af8c: mov             lr, x0
    // 0x140af90: ldr             lr, [x21, lr, lsl #3]
    // 0x140af94: blr             lr
    // 0x140af98: tbnz            w0, #4, #0x140b008
    // 0x140af9c: ldur            x0, [fp, #-0x18]
    // 0x140afa0: LoadField: r1 = r0->field_f
    //     0x140afa0: ldur            w1, [x0, #0xf]
    // 0x140afa4: DecompressPointer r1
    //     0x140afa4: add             x1, x1, HEAP, lsl #32
    // 0x140afa8: r0 = controller()
    //     0x140afa8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140afac: LoadField: r2 = r0->field_9f
    //     0x140afac: ldur            w2, [x0, #0x9f]
    // 0x140afb0: DecompressPointer r2
    //     0x140afb0: add             x2, x2, HEAP, lsl #32
    // 0x140afb4: ldur            x0, [fp, #-0x18]
    // 0x140afb8: stur            x2, [fp, #-0x28]
    // 0x140afbc: LoadField: r1 = r0->field_f
    //     0x140afbc: ldur            w1, [x0, #0xf]
    // 0x140afc0: DecompressPointer r1
    //     0x140afc0: add             x1, x1, HEAP, lsl #32
    // 0x140afc4: r0 = controller()
    //     0x140afc4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140afc8: LoadField: r1 = r0->field_9f
    //     0x140afc8: ldur            w1, [x0, #0x9f]
    // 0x140afcc: DecompressPointer r1
    //     0x140afcc: add             x1, x1, HEAP, lsl #32
    // 0x140afd0: r0 = value()
    //     0x140afd0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140afd4: r1 = LoadInt32Instr(r0)
    //     0x140afd4: sbfx            x1, x0, #1, #0x1f
    //     0x140afd8: tbz             w0, #0, #0x140afe0
    //     0x140afdc: ldur            x1, [x0, #7]
    // 0x140afe0: sub             x2, x1, #1
    // 0x140afe4: r0 = BoxInt64Instr(r2)
    //     0x140afe4: sbfiz           x0, x2, #1, #0x1f
    //     0x140afe8: cmp             x2, x0, asr #1
    //     0x140afec: b.eq            #0x140aff8
    //     0x140aff0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x140aff4: stur            x2, [x0, #7]
    // 0x140aff8: ldur            x1, [fp, #-0x28]
    // 0x140affc: mov             x2, x0
    // 0x140b000: r0 = value=()
    //     0x140b000: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140b004: b               #0x140b070
    // 0x140b008: ldur            x0, [fp, #-0x18]
    // 0x140b00c: LoadField: r1 = r0->field_f
    //     0x140b00c: ldur            w1, [x0, #0xf]
    // 0x140b010: DecompressPointer r1
    //     0x140b010: add             x1, x1, HEAP, lsl #32
    // 0x140b014: r0 = controller()
    //     0x140b014: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140b018: LoadField: r2 = r0->field_9b
    //     0x140b018: ldur            w2, [x0, #0x9b]
    // 0x140b01c: DecompressPointer r2
    //     0x140b01c: add             x2, x2, HEAP, lsl #32
    // 0x140b020: ldur            x0, [fp, #-0x18]
    // 0x140b024: stur            x2, [fp, #-0x28]
    // 0x140b028: LoadField: r1 = r0->field_f
    //     0x140b028: ldur            w1, [x0, #0xf]
    // 0x140b02c: DecompressPointer r1
    //     0x140b02c: add             x1, x1, HEAP, lsl #32
    // 0x140b030: r0 = controller()
    //     0x140b030: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140b034: LoadField: r1 = r0->field_9b
    //     0x140b034: ldur            w1, [x0, #0x9b]
    // 0x140b038: DecompressPointer r1
    //     0x140b038: add             x1, x1, HEAP, lsl #32
    // 0x140b03c: r0 = value()
    //     0x140b03c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140b040: r1 = LoadInt32Instr(r0)
    //     0x140b040: sbfx            x1, x0, #1, #0x1f
    //     0x140b044: tbz             w0, #0, #0x140b04c
    //     0x140b048: ldur            x1, [x0, #7]
    // 0x140b04c: sub             x2, x1, #1
    // 0x140b050: r0 = BoxInt64Instr(r2)
    //     0x140b050: sbfiz           x0, x2, #1, #0x1f
    //     0x140b054: cmp             x2, x0, asr #1
    //     0x140b058: b.eq            #0x140b064
    //     0x140b05c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x140b060: stur            x2, [x0, #7]
    // 0x140b064: ldur            x1, [fp, #-0x28]
    // 0x140b068: mov             x2, x0
    // 0x140b06c: r0 = value=()
    //     0x140b06c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140b070: ldur            x2, [fp, #-0x10]
    // 0x140b074: ldur            x0, [fp, #-0x18]
    // 0x140b078: LoadField: r1 = r0->field_f
    //     0x140b078: ldur            w1, [x0, #0xf]
    // 0x140b07c: DecompressPointer r1
    //     0x140b07c: add             x1, x1, HEAP, lsl #32
    // 0x140b080: r0 = controller()
    //     0x140b080: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140b084: LoadField: r1 = r0->field_73
    //     0x140b084: ldur            w1, [x0, #0x73]
    // 0x140b088: DecompressPointer r1
    //     0x140b088: add             x1, x1, HEAP, lsl #32
    // 0x140b08c: ldur            x16, [fp, #-0x20]
    // 0x140b090: stp             x16, x1, [SP]
    // 0x140b094: r0 = add()
    //     0x140b094: bl              #0x1654150  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::add
    // 0x140b098: ldur            x0, [fp, #-0x18]
    // 0x140b09c: LoadField: r1 = r0->field_f
    //     0x140b09c: ldur            w1, [x0, #0xf]
    // 0x140b0a0: DecompressPointer r1
    //     0x140b0a0: add             x1, x1, HEAP, lsl #32
    // 0x140b0a4: r0 = controller()
    //     0x140b0a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140b0a8: LoadField: r1 = r0->field_97
    //     0x140b0a8: ldur            w1, [x0, #0x97]
    // 0x140b0ac: DecompressPointer r1
    //     0x140b0ac: add             x1, x1, HEAP, lsl #32
    // 0x140b0b0: ldur            x0, [fp, #-0x10]
    // 0x140b0b4: LoadField: r2 = r0->field_f
    //     0x140b0b4: ldur            w2, [x0, #0xf]
    // 0x140b0b8: DecompressPointer r2
    //     0x140b0b8: add             x2, x2, HEAP, lsl #32
    // 0x140b0bc: r0 = LoadInt32Instr(r2)
    //     0x140b0bc: sbfx            x0, x2, #1, #0x1f
    //     0x140b0c0: tbz             w2, #0, #0x140b0c8
    //     0x140b0c4: ldur            x0, [x2, #7]
    // 0x140b0c8: mov             x2, x0
    // 0x140b0cc: r0 = removeAt()
    //     0x140b0cc: bl              #0x64b64c  ; [dart:collection] ListBase::removeAt
    // 0x140b0d0: r0 = Null
    //     0x140b0d0: mov             x0, NULL
    // 0x140b0d4: r0 = ReturnAsyncNotFuture()
    //     0x140b0d4: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x140b0d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140b0d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140b0dc: b               #0x140a960
    // 0x140b0e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x140b0e0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x140b0e4, size: 0xbe0
    // 0x140b0e4: EnterFrame
    //     0x140b0e4: stp             fp, lr, [SP, #-0x10]!
    //     0x140b0e8: mov             fp, SP
    // 0x140b0ec: AllocStack(0x58)
    //     0x140b0ec: sub             SP, SP, #0x58
    // 0x140b0f0: SetupParameters()
    //     0x140b0f0: ldr             x0, [fp, #0x20]
    //     0x140b0f4: ldur            w1, [x0, #0x17]
    //     0x140b0f8: add             x1, x1, HEAP, lsl #32
    //     0x140b0fc: stur            x1, [fp, #-8]
    // 0x140b100: CheckStackOverflow
    //     0x140b100: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140b104: cmp             SP, x16
    //     0x140b108: b.ls            #0x140bcbc
    // 0x140b10c: r1 = 1
    //     0x140b10c: movz            x1, #0x1
    // 0x140b110: r0 = AllocateContext()
    //     0x140b110: bl              #0x16f6108  ; AllocateContextStub
    // 0x140b114: mov             x2, x0
    // 0x140b118: ldur            x0, [fp, #-8]
    // 0x140b11c: stur            x2, [fp, #-0x10]
    // 0x140b120: StoreField: r2->field_b = r0
    //     0x140b120: stur            w0, [x2, #0xb]
    // 0x140b124: ldr             x3, [fp, #0x10]
    // 0x140b128: StoreField: r2->field_f = r3
    //     0x140b128: stur            w3, [x2, #0xf]
    // 0x140b12c: LoadField: r1 = r0->field_f
    //     0x140b12c: ldur            w1, [x0, #0xf]
    // 0x140b130: DecompressPointer r1
    //     0x140b130: add             x1, x1, HEAP, lsl #32
    // 0x140b134: r0 = controller()
    //     0x140b134: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140b138: LoadField: r1 = r0->field_97
    //     0x140b138: ldur            w1, [x0, #0x97]
    // 0x140b13c: DecompressPointer r1
    //     0x140b13c: add             x1, x1, HEAP, lsl #32
    // 0x140b140: r0 = value()
    //     0x140b140: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140b144: r1 = LoadClassIdInstr(r0)
    //     0x140b144: ldur            x1, [x0, #-1]
    //     0x140b148: ubfx            x1, x1, #0xc, #0x14
    // 0x140b14c: str             x0, [SP]
    // 0x140b150: mov             x0, x1
    // 0x140b154: r0 = GDT[cid_x0 + 0xc898]()
    //     0x140b154: movz            x17, #0xc898
    //     0x140b158: add             lr, x0, x17
    //     0x140b15c: ldr             lr, [x21, lr, lsl #3]
    //     0x140b160: blr             lr
    // 0x140b164: mov             x1, x0
    // 0x140b168: ldr             x0, [fp, #0x10]
    // 0x140b16c: r2 = LoadInt32Instr(r0)
    //     0x140b16c: sbfx            x2, x0, #1, #0x1f
    //     0x140b170: tbz             w0, #0, #0x140b178
    //     0x140b174: ldur            x2, [x0, #7]
    // 0x140b178: r0 = LoadInt32Instr(r1)
    //     0x140b178: sbfx            x0, x1, #1, #0x1f
    //     0x140b17c: tbz             w1, #0, #0x140b184
    //     0x140b180: ldur            x0, [x1, #7]
    // 0x140b184: cmp             x2, x0
    // 0x140b188: b.ne            #0x140b374
    // 0x140b18c: ldur            x0, [fp, #-8]
    // 0x140b190: LoadField: r1 = r0->field_f
    //     0x140b190: ldur            w1, [x0, #0xf]
    // 0x140b194: DecompressPointer r1
    //     0x140b194: add             x1, x1, HEAP, lsl #32
    // 0x140b198: r0 = controller()
    //     0x140b198: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140b19c: LoadField: r1 = r0->field_97
    //     0x140b19c: ldur            w1, [x0, #0x97]
    // 0x140b1a0: DecompressPointer r1
    //     0x140b1a0: add             x1, x1, HEAP, lsl #32
    // 0x140b1a4: r0 = value()
    //     0x140b1a4: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140b1a8: r1 = LoadClassIdInstr(r0)
    //     0x140b1a8: ldur            x1, [x0, #-1]
    //     0x140b1ac: ubfx            x1, x1, #0xc, #0x14
    // 0x140b1b0: str             x0, [SP]
    // 0x140b1b4: mov             x0, x1
    // 0x140b1b8: r0 = GDT[cid_x0 + 0xc898]()
    //     0x140b1b8: movz            x17, #0xc898
    //     0x140b1bc: add             lr, x0, x17
    //     0x140b1c0: ldr             lr, [x21, lr, lsl #3]
    //     0x140b1c4: blr             lr
    // 0x140b1c8: r1 = LoadInt32Instr(r0)
    //     0x140b1c8: sbfx            x1, x0, #1, #0x1f
    //     0x140b1cc: tbz             w0, #0, #0x140b1d4
    //     0x140b1d0: ldur            x1, [x0, #7]
    // 0x140b1d4: cmp             x1, #4
    // 0x140b1d8: b.ge            #0x140b360
    // 0x140b1dc: r1 = Instance_Color
    //     0x140b1dc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140b1e0: d0 = 0.050000
    //     0x140b1e0: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0x140b1e4: r0 = withOpacity()
    //     0x140b1e4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140b1e8: stur            x0, [fp, #-0x18]
    // 0x140b1ec: r0 = BoxDecoration()
    //     0x140b1ec: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x140b1f0: mov             x2, x0
    // 0x140b1f4: ldur            x0, [fp, #-0x18]
    // 0x140b1f8: stur            x2, [fp, #-0x20]
    // 0x140b1fc: StoreField: r2->field_7 = r0
    //     0x140b1fc: stur            w0, [x2, #7]
    // 0x140b200: r0 = Instance_BoxShape
    //     0x140b200: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x140b204: ldr             x0, [x0, #0x80]
    // 0x140b208: StoreField: r2->field_23 = r0
    //     0x140b208: stur            w0, [x2, #0x23]
    // 0x140b20c: r1 = Instance_MaterialColor
    //     0x140b20c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x140b210: ldr             x1, [x1, #0xdc0]
    // 0x140b214: d0 = 0.300000
    //     0x140b214: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x140b218: ldr             d0, [x17, #0x658]
    // 0x140b21c: r0 = withOpacity()
    //     0x140b21c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140b220: r1 = Instance_Color
    //     0x140b220: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140b224: d0 = 0.500000
    //     0x140b224: fmov            d0, #0.50000000
    // 0x140b228: stur            x0, [fp, #-0x18]
    // 0x140b22c: r0 = withOpacity()
    //     0x140b22c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140b230: stur            x0, [fp, #-0x28]
    // 0x140b234: r0 = Icon()
    //     0x140b234: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x140b238: mov             x1, x0
    // 0x140b23c: r0 = Instance_IconData
    //     0x140b23c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36b10] Obj!IconData@d55801
    //     0x140b240: ldr             x0, [x0, #0xb10]
    // 0x140b244: stur            x1, [fp, #-0x30]
    // 0x140b248: StoreField: r1->field_b = r0
    //     0x140b248: stur            w0, [x1, #0xb]
    // 0x140b24c: r0 = 25.000000
    //     0x140b24c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0x140b250: ldr             x0, [x0, #0x98]
    // 0x140b254: StoreField: r1->field_f = r0
    //     0x140b254: stur            w0, [x1, #0xf]
    // 0x140b258: ldur            x0, [fp, #-0x28]
    // 0x140b25c: StoreField: r1->field_23 = r0
    //     0x140b25c: stur            w0, [x1, #0x23]
    // 0x140b260: r0 = Center()
    //     0x140b260: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x140b264: mov             x1, x0
    // 0x140b268: r0 = Instance_Alignment
    //     0x140b268: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x140b26c: ldr             x0, [x0, #0xb10]
    // 0x140b270: stur            x1, [fp, #-0x28]
    // 0x140b274: StoreField: r1->field_f = r0
    //     0x140b274: stur            w0, [x1, #0xf]
    // 0x140b278: ldur            x0, [fp, #-0x30]
    // 0x140b27c: StoreField: r1->field_b = r0
    //     0x140b27c: stur            w0, [x1, #0xb]
    // 0x140b280: r0 = DottedBorder()
    //     0x140b280: bl              #0x9f8894  ; AllocateDottedBorderStub -> DottedBorder (size=0x3c)
    // 0x140b284: mov             x1, x0
    // 0x140b288: ldur            x2, [fp, #-0x28]
    // 0x140b28c: ldur            x3, [fp, #-0x18]
    // 0x140b290: r5 = const [5.0, 5.0]
    //     0x140b290: add             x5, PP, #0x36, lsl #12  ; [pp+0x36ab0] List<double>(2)
    //     0x140b294: ldr             x5, [x5, #0xab0]
    // 0x140b298: d0 = 1.000000
    //     0x140b298: fmov            d0, #1.00000000
    // 0x140b29c: stur            x0, [fp, #-0x18]
    // 0x140b2a0: r4 = const [0, 0x5, 0, 0x5, null]
    //     0x140b2a0: ldr             x4, [PP, #0x1330]  ; [pp+0x1330] List(5) [0, 0x5, 0, 0x5, Null]
    // 0x140b2a4: r0 = DottedBorder()
    //     0x140b2a4: bl              #0x9f8704  ; [package:dotted_border/dotted_border.dart] DottedBorder::DottedBorder
    // 0x140b2a8: r0 = Container()
    //     0x140b2a8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x140b2ac: stur            x0, [fp, #-0x28]
    // 0x140b2b0: r16 = 57.000000
    //     0x140b2b0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36b18] 57
    //     0x140b2b4: ldr             x16, [x16, #0xb18]
    // 0x140b2b8: r30 = 57.000000
    //     0x140b2b8: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b18] 57
    //     0x140b2bc: ldr             lr, [lr, #0xb18]
    // 0x140b2c0: stp             lr, x16, [SP, #0x10]
    // 0x140b2c4: ldur            x16, [fp, #-0x20]
    // 0x140b2c8: ldur            lr, [fp, #-0x18]
    // 0x140b2cc: stp             lr, x16, [SP]
    // 0x140b2d0: mov             x1, x0
    // 0x140b2d4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0x140b2d4: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x140b2d8: ldr             x4, [x4, #0x8c0]
    // 0x140b2dc: r0 = Container()
    //     0x140b2dc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x140b2e0: r0 = Padding()
    //     0x140b2e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x140b2e4: r3 = Instance_EdgeInsets
    //     0x140b2e4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!EdgeInsets@d599c1
    //     0x140b2e8: ldr             x3, [x3, #0xdf8]
    // 0x140b2ec: stur            x0, [fp, #-0x18]
    // 0x140b2f0: StoreField: r0->field_f = r3
    //     0x140b2f0: stur            w3, [x0, #0xf]
    // 0x140b2f4: ldur            x1, [fp, #-0x28]
    // 0x140b2f8: StoreField: r0->field_b = r1
    //     0x140b2f8: stur            w1, [x0, #0xb]
    // 0x140b2fc: r0 = InkWell()
    //     0x140b2fc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x140b300: mov             x3, x0
    // 0x140b304: ldur            x0, [fp, #-0x18]
    // 0x140b308: stur            x3, [fp, #-0x20]
    // 0x140b30c: StoreField: r3->field_b = r0
    //     0x140b30c: stur            w0, [x3, #0xb]
    // 0x140b310: ldur            x2, [fp, #-0x10]
    // 0x140b314: r1 = Function '<anonymous closure>':.
    //     0x140b314: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b20] AnonymousClosure: (0x140bcc4), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x140b318: ldr             x1, [x1, #0xb20]
    // 0x140b31c: r0 = AllocateClosure()
    //     0x140b31c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140b320: mov             x1, x0
    // 0x140b324: ldur            x0, [fp, #-0x20]
    // 0x140b328: StoreField: r0->field_f = r1
    //     0x140b328: stur            w1, [x0, #0xf]
    // 0x140b32c: r1 = true
    //     0x140b32c: add             x1, NULL, #0x20  ; true
    // 0x140b330: StoreField: r0->field_43 = r1
    //     0x140b330: stur            w1, [x0, #0x43]
    // 0x140b334: r2 = Instance_BoxShape
    //     0x140b334: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x140b338: ldr             x2, [x2, #0x80]
    // 0x140b33c: StoreField: r0->field_47 = r2
    //     0x140b33c: stur            w2, [x0, #0x47]
    // 0x140b340: StoreField: r0->field_6f = r1
    //     0x140b340: stur            w1, [x0, #0x6f]
    // 0x140b344: r2 = false
    //     0x140b344: add             x2, NULL, #0x30  ; false
    // 0x140b348: StoreField: r0->field_73 = r2
    //     0x140b348: stur            w2, [x0, #0x73]
    // 0x140b34c: StoreField: r0->field_83 = r1
    //     0x140b34c: stur            w1, [x0, #0x83]
    // 0x140b350: StoreField: r0->field_7b = r2
    //     0x140b350: stur            w2, [x0, #0x7b]
    // 0x140b354: LeaveFrame
    //     0x140b354: mov             SP, fp
    //     0x140b358: ldp             fp, lr, [SP], #0x10
    // 0x140b35c: ret
    //     0x140b35c: ret             
    // 0x140b360: r3 = Instance_EdgeInsets
    //     0x140b360: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!EdgeInsets@d599c1
    //     0x140b364: ldr             x3, [x3, #0xdf8]
    // 0x140b368: r0 = Instance_Alignment
    //     0x140b368: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x140b36c: ldr             x0, [x0, #0xb10]
    // 0x140b370: b               #0x140b384
    // 0x140b374: r3 = Instance_EdgeInsets
    //     0x140b374: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!EdgeInsets@d599c1
    //     0x140b378: ldr             x3, [x3, #0xdf8]
    // 0x140b37c: r0 = Instance_Alignment
    //     0x140b37c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x140b380: ldr             x0, [x0, #0xb10]
    // 0x140b384: ldur            x4, [fp, #-8]
    // 0x140b388: ldur            x5, [fp, #-0x10]
    // 0x140b38c: r1 = <Widget>
    //     0x140b38c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x140b390: r2 = 0
    //     0x140b390: movz            x2, #0
    // 0x140b394: r0 = _GrowableList()
    //     0x140b394: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x140b398: mov             x2, x0
    // 0x140b39c: ldur            x0, [fp, #-8]
    // 0x140b3a0: stur            x2, [fp, #-0x18]
    // 0x140b3a4: LoadField: r1 = r0->field_f
    //     0x140b3a4: ldur            w1, [x0, #0xf]
    // 0x140b3a8: DecompressPointer r1
    //     0x140b3a8: add             x1, x1, HEAP, lsl #32
    // 0x140b3ac: r0 = controller()
    //     0x140b3ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140b3b0: LoadField: r1 = r0->field_97
    //     0x140b3b0: ldur            w1, [x0, #0x97]
    // 0x140b3b4: DecompressPointer r1
    //     0x140b3b4: add             x1, x1, HEAP, lsl #32
    // 0x140b3b8: ldur            x2, [fp, #-0x10]
    // 0x140b3bc: LoadField: r0 = r2->field_f
    //     0x140b3bc: ldur            w0, [x2, #0xf]
    // 0x140b3c0: DecompressPointer r0
    //     0x140b3c0: add             x0, x0, HEAP, lsl #32
    // 0x140b3c4: stur            x0, [fp, #-0x20]
    // 0x140b3c8: r0 = value()
    //     0x140b3c8: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140b3cc: r1 = LoadClassIdInstr(r0)
    //     0x140b3cc: ldur            x1, [x0, #-1]
    //     0x140b3d0: ubfx            x1, x1, #0xc, #0x14
    // 0x140b3d4: ldur            x16, [fp, #-0x20]
    // 0x140b3d8: stp             x16, x0, [SP]
    // 0x140b3dc: mov             x0, x1
    // 0x140b3e0: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140b3e0: sub             lr, x0, #0xb7
    //     0x140b3e4: ldr             lr, [x21, lr, lsl #3]
    //     0x140b3e8: blr             lr
    // 0x140b3ec: LoadField: r1 = r0->field_f
    //     0x140b3ec: ldur            w1, [x0, #0xf]
    // 0x140b3f0: DecompressPointer r1
    //     0x140b3f0: add             x1, x1, HEAP, lsl #32
    // 0x140b3f4: cmp             w1, NULL
    // 0x140b3f8: b.ne            #0x140b70c
    // 0x140b3fc: ldur            x0, [fp, #-8]
    // 0x140b400: ldur            x2, [fp, #-0x10]
    // 0x140b404: LoadField: r1 = r0->field_f
    //     0x140b404: ldur            w1, [x0, #0xf]
    // 0x140b408: DecompressPointer r1
    //     0x140b408: add             x1, x1, HEAP, lsl #32
    // 0x140b40c: r0 = controller()
    //     0x140b40c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140b410: LoadField: r1 = r0->field_97
    //     0x140b410: ldur            w1, [x0, #0x97]
    // 0x140b414: DecompressPointer r1
    //     0x140b414: add             x1, x1, HEAP, lsl #32
    // 0x140b418: ldur            x2, [fp, #-0x10]
    // 0x140b41c: LoadField: r0 = r2->field_f
    //     0x140b41c: ldur            w0, [x2, #0xf]
    // 0x140b420: DecompressPointer r0
    //     0x140b420: add             x0, x0, HEAP, lsl #32
    // 0x140b424: stur            x0, [fp, #-0x20]
    // 0x140b428: r0 = value()
    //     0x140b428: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140b42c: r1 = LoadClassIdInstr(r0)
    //     0x140b42c: ldur            x1, [x0, #-1]
    //     0x140b430: ubfx            x1, x1, #0xc, #0x14
    // 0x140b434: ldur            x16, [fp, #-0x20]
    // 0x140b438: stp             x16, x0, [SP]
    // 0x140b43c: mov             x0, x1
    // 0x140b440: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140b440: sub             lr, x0, #0xb7
    //     0x140b444: ldr             lr, [x21, lr, lsl #3]
    //     0x140b448: blr             lr
    // 0x140b44c: LoadField: r1 = r0->field_13
    //     0x140b44c: ldur            w1, [x0, #0x13]
    // 0x140b450: DecompressPointer r1
    //     0x140b450: add             x1, x1, HEAP, lsl #32
    // 0x140b454: cmp             w1, NULL
    // 0x140b458: b.ne            #0x140b468
    // 0x140b45c: r4 = 0
    //     0x140b45c: movz            x4, #0
    // 0x140b460: r0 = AllocateUint8Array()
    //     0x140b460: bl              #0x16f6e7c  ; AllocateUint8ArrayStub
    // 0x140b464: mov             x1, x0
    // 0x140b468: ldur            x0, [fp, #-8]
    // 0x140b46c: ldur            x2, [fp, #-0x10]
    // 0x140b470: stur            x1, [fp, #-0x20]
    // 0x140b474: r0 = Image()
    //     0x140b474: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0x140b478: mov             x1, x0
    // 0x140b47c: ldur            x2, [fp, #-0x20]
    // 0x140b480: d0 = 57.000000
    //     0x140b480: add             x17, PP, #0x36, lsl #12  ; [pp+0x36b28] IMM: double(57) from 0x404c800000000000
    //     0x140b484: ldr             d0, [x17, #0xb28]
    // 0x140b488: d1 = 57.000000
    //     0x140b488: add             x17, PP, #0x36, lsl #12  ; [pp+0x36b28] IMM: double(57) from 0x404c800000000000
    //     0x140b48c: ldr             d1, [x17, #0xb28]
    // 0x140b490: stur            x0, [fp, #-0x20]
    // 0x140b494: r0 = Image.memory()
    //     0x140b494: bl              #0x9a52d0  ; [package:flutter/src/widgets/image.dart] Image::Image.memory
    // 0x140b498: r1 = Null
    //     0x140b498: mov             x1, NULL
    // 0x140b49c: r2 = 2
    //     0x140b49c: movz            x2, #0x2
    // 0x140b4a0: r0 = AllocateArray()
    //     0x140b4a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x140b4a4: mov             x2, x0
    // 0x140b4a8: ldur            x0, [fp, #-0x20]
    // 0x140b4ac: stur            x2, [fp, #-0x28]
    // 0x140b4b0: StoreField: r2->field_f = r0
    //     0x140b4b0: stur            w0, [x2, #0xf]
    // 0x140b4b4: r1 = <Widget>
    //     0x140b4b4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x140b4b8: r0 = AllocateGrowableArray()
    //     0x140b4b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x140b4bc: mov             x2, x0
    // 0x140b4c0: ldur            x0, [fp, #-0x28]
    // 0x140b4c4: stur            x2, [fp, #-0x20]
    // 0x140b4c8: StoreField: r2->field_f = r0
    //     0x140b4c8: stur            w0, [x2, #0xf]
    // 0x140b4cc: r0 = 2
    //     0x140b4cc: movz            x0, #0x2
    // 0x140b4d0: StoreField: r2->field_b = r0
    //     0x140b4d0: stur            w0, [x2, #0xb]
    // 0x140b4d4: ldur            x0, [fp, #-8]
    // 0x140b4d8: LoadField: r1 = r0->field_f
    //     0x140b4d8: ldur            w1, [x0, #0xf]
    // 0x140b4dc: DecompressPointer r1
    //     0x140b4dc: add             x1, x1, HEAP, lsl #32
    // 0x140b4e0: r0 = controller()
    //     0x140b4e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140b4e4: LoadField: r1 = r0->field_97
    //     0x140b4e4: ldur            w1, [x0, #0x97]
    // 0x140b4e8: DecompressPointer r1
    //     0x140b4e8: add             x1, x1, HEAP, lsl #32
    // 0x140b4ec: ldur            x2, [fp, #-0x10]
    // 0x140b4f0: LoadField: r0 = r2->field_f
    //     0x140b4f0: ldur            w0, [x2, #0xf]
    // 0x140b4f4: DecompressPointer r0
    //     0x140b4f4: add             x0, x0, HEAP, lsl #32
    // 0x140b4f8: stur            x0, [fp, #-0x28]
    // 0x140b4fc: r0 = value()
    //     0x140b4fc: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140b500: r1 = LoadClassIdInstr(r0)
    //     0x140b500: ldur            x1, [x0, #-1]
    //     0x140b504: ubfx            x1, x1, #0xc, #0x14
    // 0x140b508: ldur            x16, [fp, #-0x28]
    // 0x140b50c: stp             x16, x0, [SP]
    // 0x140b510: mov             x0, x1
    // 0x140b514: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140b514: sub             lr, x0, #0xb7
    //     0x140b518: ldr             lr, [x21, lr, lsl #3]
    //     0x140b51c: blr             lr
    // 0x140b520: LoadField: r1 = r0->field_b
    //     0x140b520: ldur            w1, [x0, #0xb]
    // 0x140b524: DecompressPointer r1
    //     0x140b524: add             x1, x1, HEAP, lsl #32
    // 0x140b528: r0 = LoadClassIdInstr(r1)
    //     0x140b528: ldur            x0, [x1, #-1]
    //     0x140b52c: ubfx            x0, x0, #0xc, #0x14
    // 0x140b530: r16 = "video_local"
    //     0x140b530: add             x16, PP, #0x36, lsl #12  ; [pp+0x369c0] "video_local"
    //     0x140b534: ldr             x16, [x16, #0x9c0]
    // 0x140b538: stp             x16, x1, [SP]
    // 0x140b53c: mov             lr, x0
    // 0x140b540: ldr             lr, [x21, lr, lsl #3]
    // 0x140b544: blr             lr
    // 0x140b548: tbnz            w0, #4, #0x140b650
    // 0x140b54c: ldur            x0, [fp, #-0x20]
    // 0x140b550: r1 = Instance_Color
    //     0x140b550: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140b554: d0 = 0.300000
    //     0x140b554: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x140b558: ldr             d0, [x17, #0x658]
    // 0x140b55c: r0 = withOpacity()
    //     0x140b55c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140b560: stur            x0, [fp, #-0x28]
    // 0x140b564: r0 = BoxDecoration()
    //     0x140b564: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x140b568: mov             x1, x0
    // 0x140b56c: ldur            x0, [fp, #-0x28]
    // 0x140b570: stur            x1, [fp, #-0x30]
    // 0x140b574: StoreField: r1->field_7 = r0
    //     0x140b574: stur            w0, [x1, #7]
    // 0x140b578: r0 = Instance_BoxShape
    //     0x140b578: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x140b57c: ldr             x0, [x0, #0x970]
    // 0x140b580: StoreField: r1->field_23 = r0
    //     0x140b580: stur            w0, [x1, #0x23]
    // 0x140b584: r0 = Container()
    //     0x140b584: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x140b588: stur            x0, [fp, #-0x28]
    // 0x140b58c: ldur            x16, [fp, #-0x30]
    // 0x140b590: r30 = Instance_EdgeInsets
    //     0x140b590: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b30] Obj!EdgeInsets@d57711
    //     0x140b594: ldr             lr, [lr, #0xb30]
    // 0x140b598: stp             lr, x16, [SP, #8]
    // 0x140b59c: r16 = Instance_Icon
    //     0x140b59c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36b38] Obj!Icon@d66431
    //     0x140b5a0: ldr             x16, [x16, #0xb38]
    // 0x140b5a4: str             x16, [SP]
    // 0x140b5a8: mov             x1, x0
    // 0x140b5ac: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0x140b5ac: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0x140b5b0: ldr             x4, [x4, #0xb40]
    // 0x140b5b4: r0 = Container()
    //     0x140b5b4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x140b5b8: r1 = <StackParentData>
    //     0x140b5b8: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x140b5bc: ldr             x1, [x1, #0x8e0]
    // 0x140b5c0: r0 = Positioned()
    //     0x140b5c0: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x140b5c4: mov             x2, x0
    // 0x140b5c8: ldur            x0, [fp, #-0x28]
    // 0x140b5cc: stur            x2, [fp, #-0x30]
    // 0x140b5d0: StoreField: r2->field_b = r0
    //     0x140b5d0: stur            w0, [x2, #0xb]
    // 0x140b5d4: ldur            x0, [fp, #-0x20]
    // 0x140b5d8: LoadField: r1 = r0->field_b
    //     0x140b5d8: ldur            w1, [x0, #0xb]
    // 0x140b5dc: LoadField: r3 = r0->field_f
    //     0x140b5dc: ldur            w3, [x0, #0xf]
    // 0x140b5e0: DecompressPointer r3
    //     0x140b5e0: add             x3, x3, HEAP, lsl #32
    // 0x140b5e4: LoadField: r4 = r3->field_b
    //     0x140b5e4: ldur            w4, [x3, #0xb]
    // 0x140b5e8: r3 = LoadInt32Instr(r1)
    //     0x140b5e8: sbfx            x3, x1, #1, #0x1f
    // 0x140b5ec: stur            x3, [fp, #-0x38]
    // 0x140b5f0: r1 = LoadInt32Instr(r4)
    //     0x140b5f0: sbfx            x1, x4, #1, #0x1f
    // 0x140b5f4: cmp             x3, x1
    // 0x140b5f8: b.ne            #0x140b604
    // 0x140b5fc: mov             x1, x0
    // 0x140b600: r0 = _growToNextCapacity()
    //     0x140b600: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x140b604: ldur            x2, [fp, #-0x20]
    // 0x140b608: ldur            x3, [fp, #-0x38]
    // 0x140b60c: add             x0, x3, #1
    // 0x140b610: lsl             x1, x0, #1
    // 0x140b614: StoreField: r2->field_b = r1
    //     0x140b614: stur            w1, [x2, #0xb]
    // 0x140b618: LoadField: r1 = r2->field_f
    //     0x140b618: ldur            w1, [x2, #0xf]
    // 0x140b61c: DecompressPointer r1
    //     0x140b61c: add             x1, x1, HEAP, lsl #32
    // 0x140b620: ldur            x0, [fp, #-0x30]
    // 0x140b624: ArrayStore: r1[r3] = r0  ; List_4
    //     0x140b624: add             x25, x1, x3, lsl #2
    //     0x140b628: add             x25, x25, #0xf
    //     0x140b62c: str             w0, [x25]
    //     0x140b630: tbz             w0, #0, #0x140b64c
    //     0x140b634: ldurb           w16, [x1, #-1]
    //     0x140b638: ldurb           w17, [x0, #-1]
    //     0x140b63c: and             x16, x17, x16, lsr #2
    //     0x140b640: tst             x16, HEAP, lsr #32
    //     0x140b644: b.eq            #0x140b64c
    //     0x140b648: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x140b64c: b               #0x140b654
    // 0x140b650: ldur            x2, [fp, #-0x20]
    // 0x140b654: ldur            x1, [fp, #-0x18]
    // 0x140b658: r0 = Stack()
    //     0x140b658: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x140b65c: mov             x2, x0
    // 0x140b660: r0 = Instance_Alignment
    //     0x140b660: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x140b664: ldr             x0, [x0, #0xb10]
    // 0x140b668: stur            x2, [fp, #-0x28]
    // 0x140b66c: StoreField: r2->field_f = r0
    //     0x140b66c: stur            w0, [x2, #0xf]
    // 0x140b670: r0 = Instance_StackFit
    //     0x140b670: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x140b674: ldr             x0, [x0, #0xfa8]
    // 0x140b678: ArrayStore: r2[0] = r0  ; List_4
    //     0x140b678: stur            w0, [x2, #0x17]
    // 0x140b67c: r3 = Instance_Clip
    //     0x140b67c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x140b680: ldr             x3, [x3, #0x7e0]
    // 0x140b684: StoreField: r2->field_1b = r3
    //     0x140b684: stur            w3, [x2, #0x1b]
    // 0x140b688: ldur            x1, [fp, #-0x20]
    // 0x140b68c: StoreField: r2->field_b = r1
    //     0x140b68c: stur            w1, [x2, #0xb]
    // 0x140b690: ldur            x4, [fp, #-0x18]
    // 0x140b694: LoadField: r1 = r4->field_b
    //     0x140b694: ldur            w1, [x4, #0xb]
    // 0x140b698: LoadField: r5 = r4->field_f
    //     0x140b698: ldur            w5, [x4, #0xf]
    // 0x140b69c: DecompressPointer r5
    //     0x140b69c: add             x5, x5, HEAP, lsl #32
    // 0x140b6a0: LoadField: r6 = r5->field_b
    //     0x140b6a0: ldur            w6, [x5, #0xb]
    // 0x140b6a4: r5 = LoadInt32Instr(r1)
    //     0x140b6a4: sbfx            x5, x1, #1, #0x1f
    // 0x140b6a8: stur            x5, [fp, #-0x38]
    // 0x140b6ac: r1 = LoadInt32Instr(r6)
    //     0x140b6ac: sbfx            x1, x6, #1, #0x1f
    // 0x140b6b0: cmp             x5, x1
    // 0x140b6b4: b.ne            #0x140b6c0
    // 0x140b6b8: mov             x1, x4
    // 0x140b6bc: r0 = _growToNextCapacity()
    //     0x140b6bc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x140b6c0: ldur            x2, [fp, #-0x18]
    // 0x140b6c4: ldur            x3, [fp, #-0x38]
    // 0x140b6c8: add             x0, x3, #1
    // 0x140b6cc: lsl             x1, x0, #1
    // 0x140b6d0: StoreField: r2->field_b = r1
    //     0x140b6d0: stur            w1, [x2, #0xb]
    // 0x140b6d4: LoadField: r1 = r2->field_f
    //     0x140b6d4: ldur            w1, [x2, #0xf]
    // 0x140b6d8: DecompressPointer r1
    //     0x140b6d8: add             x1, x1, HEAP, lsl #32
    // 0x140b6dc: ldur            x0, [fp, #-0x28]
    // 0x140b6e0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x140b6e0: add             x25, x1, x3, lsl #2
    //     0x140b6e4: add             x25, x25, #0xf
    //     0x140b6e8: str             w0, [x25]
    //     0x140b6ec: tbz             w0, #0, #0x140b708
    //     0x140b6f0: ldurb           w16, [x1, #-1]
    //     0x140b6f4: ldurb           w17, [x0, #-1]
    //     0x140b6f8: and             x16, x17, x16, lsr #2
    //     0x140b6fc: tst             x16, HEAP, lsr #32
    //     0x140b700: b.eq            #0x140b708
    //     0x140b704: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x140b708: b               #0x140b710
    // 0x140b70c: ldur            x2, [fp, #-0x18]
    // 0x140b710: ldur            x3, [fp, #-8]
    // 0x140b714: ldur            x0, [fp, #-0x10]
    // 0x140b718: LoadField: r1 = r3->field_f
    //     0x140b718: ldur            w1, [x3, #0xf]
    // 0x140b71c: DecompressPointer r1
    //     0x140b71c: add             x1, x1, HEAP, lsl #32
    // 0x140b720: r0 = controller()
    //     0x140b720: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140b724: LoadField: r1 = r0->field_97
    //     0x140b724: ldur            w1, [x0, #0x97]
    // 0x140b728: DecompressPointer r1
    //     0x140b728: add             x1, x1, HEAP, lsl #32
    // 0x140b72c: ldur            x2, [fp, #-0x10]
    // 0x140b730: LoadField: r0 = r2->field_f
    //     0x140b730: ldur            w0, [x2, #0xf]
    // 0x140b734: DecompressPointer r0
    //     0x140b734: add             x0, x0, HEAP, lsl #32
    // 0x140b738: stur            x0, [fp, #-0x20]
    // 0x140b73c: r0 = value()
    //     0x140b73c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140b740: r1 = LoadClassIdInstr(r0)
    //     0x140b740: ldur            x1, [x0, #-1]
    //     0x140b744: ubfx            x1, x1, #0xc, #0x14
    // 0x140b748: ldur            x16, [fp, #-0x20]
    // 0x140b74c: stp             x16, x0, [SP]
    // 0x140b750: mov             x0, x1
    // 0x140b754: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140b754: sub             lr, x0, #0xb7
    //     0x140b758: ldr             lr, [x21, lr, lsl #3]
    //     0x140b75c: blr             lr
    // 0x140b760: LoadField: r1 = r0->field_f
    //     0x140b760: ldur            w1, [x0, #0xf]
    // 0x140b764: DecompressPointer r1
    //     0x140b764: add             x1, x1, HEAP, lsl #32
    // 0x140b768: cmp             w1, NULL
    // 0x140b76c: b.eq            #0x140b8f8
    // 0x140b770: ldur            x0, [fp, #-8]
    // 0x140b774: ldur            x2, [fp, #-0x10]
    // 0x140b778: LoadField: r1 = r0->field_f
    //     0x140b778: ldur            w1, [x0, #0xf]
    // 0x140b77c: DecompressPointer r1
    //     0x140b77c: add             x1, x1, HEAP, lsl #32
    // 0x140b780: r0 = controller()
    //     0x140b780: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140b784: LoadField: r1 = r0->field_97
    //     0x140b784: ldur            w1, [x0, #0x97]
    // 0x140b788: DecompressPointer r1
    //     0x140b788: add             x1, x1, HEAP, lsl #32
    // 0x140b78c: ldur            x2, [fp, #-0x10]
    // 0x140b790: LoadField: r0 = r2->field_f
    //     0x140b790: ldur            w0, [x2, #0xf]
    // 0x140b794: DecompressPointer r0
    //     0x140b794: add             x0, x0, HEAP, lsl #32
    // 0x140b798: stur            x0, [fp, #-0x20]
    // 0x140b79c: r0 = value()
    //     0x140b79c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140b7a0: r1 = LoadClassIdInstr(r0)
    //     0x140b7a0: ldur            x1, [x0, #-1]
    //     0x140b7a4: ubfx            x1, x1, #0xc, #0x14
    // 0x140b7a8: ldur            x16, [fp, #-0x20]
    // 0x140b7ac: stp             x16, x0, [SP]
    // 0x140b7b0: mov             x0, x1
    // 0x140b7b4: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140b7b4: sub             lr, x0, #0xb7
    //     0x140b7b8: ldr             lr, [x21, lr, lsl #3]
    //     0x140b7bc: blr             lr
    // 0x140b7c0: LoadField: r1 = r0->field_b
    //     0x140b7c0: ldur            w1, [x0, #0xb]
    // 0x140b7c4: DecompressPointer r1
    //     0x140b7c4: add             x1, x1, HEAP, lsl #32
    // 0x140b7c8: r0 = LoadClassIdInstr(r1)
    //     0x140b7c8: ldur            x0, [x1, #-1]
    //     0x140b7cc: ubfx            x0, x0, #0xc, #0x14
    // 0x140b7d0: r16 = "video"
    //     0x140b7d0: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x140b7d4: ldr             x16, [x16, #0xb50]
    // 0x140b7d8: stp             x16, x1, [SP]
    // 0x140b7dc: mov             lr, x0
    // 0x140b7e0: ldr             lr, [x21, lr, lsl #3]
    // 0x140b7e4: blr             lr
    // 0x140b7e8: tbnz            w0, #4, #0x140b8f0
    // 0x140b7ec: ldur            x0, [fp, #-8]
    // 0x140b7f0: ldur            x2, [fp, #-0x10]
    // 0x140b7f4: LoadField: r1 = r0->field_f
    //     0x140b7f4: ldur            w1, [x0, #0xf]
    // 0x140b7f8: DecompressPointer r1
    //     0x140b7f8: add             x1, x1, HEAP, lsl #32
    // 0x140b7fc: r0 = controller()
    //     0x140b7fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140b800: LoadField: r1 = r0->field_97
    //     0x140b800: ldur            w1, [x0, #0x97]
    // 0x140b804: DecompressPointer r1
    //     0x140b804: add             x1, x1, HEAP, lsl #32
    // 0x140b808: ldur            x2, [fp, #-0x10]
    // 0x140b80c: LoadField: r0 = r2->field_f
    //     0x140b80c: ldur            w0, [x2, #0xf]
    // 0x140b810: DecompressPointer r0
    //     0x140b810: add             x0, x0, HEAP, lsl #32
    // 0x140b814: stur            x0, [fp, #-0x20]
    // 0x140b818: r0 = value()
    //     0x140b818: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140b81c: r1 = LoadClassIdInstr(r0)
    //     0x140b81c: ldur            x1, [x0, #-1]
    //     0x140b820: ubfx            x1, x1, #0xc, #0x14
    // 0x140b824: ldur            x16, [fp, #-0x20]
    // 0x140b828: stp             x16, x0, [SP]
    // 0x140b82c: mov             x0, x1
    // 0x140b830: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140b830: sub             lr, x0, #0xb7
    //     0x140b834: ldr             lr, [x21, lr, lsl #3]
    //     0x140b838: blr             lr
    // 0x140b83c: LoadField: r1 = r0->field_f
    //     0x140b83c: ldur            w1, [x0, #0xf]
    // 0x140b840: DecompressPointer r1
    //     0x140b840: add             x1, x1, HEAP, lsl #32
    // 0x140b844: cmp             w1, NULL
    // 0x140b848: b.ne            #0x140b854
    // 0x140b84c: r0 = ""
    //     0x140b84c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x140b850: b               #0x140b858
    // 0x140b854: mov             x0, x1
    // 0x140b858: ldur            x1, [fp, #-0x18]
    // 0x140b85c: stur            x0, [fp, #-0x20]
    // 0x140b860: r0 = VideoPlayerWidget()
    //     0x140b860: bl              #0x8fed7c  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0x140b864: mov             x2, x0
    // 0x140b868: ldur            x0, [fp, #-0x20]
    // 0x140b86c: stur            x2, [fp, #-0x28]
    // 0x140b870: StoreField: r2->field_b = r0
    //     0x140b870: stur            w0, [x2, #0xb]
    // 0x140b874: ldur            x0, [fp, #-0x18]
    // 0x140b878: LoadField: r1 = r0->field_b
    //     0x140b878: ldur            w1, [x0, #0xb]
    // 0x140b87c: LoadField: r3 = r0->field_f
    //     0x140b87c: ldur            w3, [x0, #0xf]
    // 0x140b880: DecompressPointer r3
    //     0x140b880: add             x3, x3, HEAP, lsl #32
    // 0x140b884: LoadField: r4 = r3->field_b
    //     0x140b884: ldur            w4, [x3, #0xb]
    // 0x140b888: r3 = LoadInt32Instr(r1)
    //     0x140b888: sbfx            x3, x1, #1, #0x1f
    // 0x140b88c: stur            x3, [fp, #-0x38]
    // 0x140b890: r1 = LoadInt32Instr(r4)
    //     0x140b890: sbfx            x1, x4, #1, #0x1f
    // 0x140b894: cmp             x3, x1
    // 0x140b898: b.ne            #0x140b8a4
    // 0x140b89c: mov             x1, x0
    // 0x140b8a0: r0 = _growToNextCapacity()
    //     0x140b8a0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x140b8a4: ldur            x2, [fp, #-0x18]
    // 0x140b8a8: ldur            x3, [fp, #-0x38]
    // 0x140b8ac: add             x0, x3, #1
    // 0x140b8b0: lsl             x1, x0, #1
    // 0x140b8b4: StoreField: r2->field_b = r1
    //     0x140b8b4: stur            w1, [x2, #0xb]
    // 0x140b8b8: LoadField: r1 = r2->field_f
    //     0x140b8b8: ldur            w1, [x2, #0xf]
    // 0x140b8bc: DecompressPointer r1
    //     0x140b8bc: add             x1, x1, HEAP, lsl #32
    // 0x140b8c0: ldur            x0, [fp, #-0x28]
    // 0x140b8c4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x140b8c4: add             x25, x1, x3, lsl #2
    //     0x140b8c8: add             x25, x25, #0xf
    //     0x140b8cc: str             w0, [x25]
    //     0x140b8d0: tbz             w0, #0, #0x140b8ec
    //     0x140b8d4: ldurb           w16, [x1, #-1]
    //     0x140b8d8: ldurb           w17, [x0, #-1]
    //     0x140b8dc: and             x16, x17, x16, lsr #2
    //     0x140b8e0: tst             x16, HEAP, lsr #32
    //     0x140b8e4: b.eq            #0x140b8ec
    //     0x140b8e8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x140b8ec: b               #0x140b8fc
    // 0x140b8f0: ldur            x2, [fp, #-0x18]
    // 0x140b8f4: b               #0x140b8fc
    // 0x140b8f8: ldur            x2, [fp, #-0x18]
    // 0x140b8fc: ldur            x3, [fp, #-8]
    // 0x140b900: ldur            x0, [fp, #-0x10]
    // 0x140b904: LoadField: r1 = r3->field_f
    //     0x140b904: ldur            w1, [x3, #0xf]
    // 0x140b908: DecompressPointer r1
    //     0x140b908: add             x1, x1, HEAP, lsl #32
    // 0x140b90c: r0 = controller()
    //     0x140b90c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140b910: LoadField: r1 = r0->field_97
    //     0x140b910: ldur            w1, [x0, #0x97]
    // 0x140b914: DecompressPointer r1
    //     0x140b914: add             x1, x1, HEAP, lsl #32
    // 0x140b918: ldur            x2, [fp, #-0x10]
    // 0x140b91c: LoadField: r0 = r2->field_f
    //     0x140b91c: ldur            w0, [x2, #0xf]
    // 0x140b920: DecompressPointer r0
    //     0x140b920: add             x0, x0, HEAP, lsl #32
    // 0x140b924: stur            x0, [fp, #-0x20]
    // 0x140b928: r0 = value()
    //     0x140b928: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140b92c: r1 = LoadClassIdInstr(r0)
    //     0x140b92c: ldur            x1, [x0, #-1]
    //     0x140b930: ubfx            x1, x1, #0xc, #0x14
    // 0x140b934: ldur            x16, [fp, #-0x20]
    // 0x140b938: stp             x16, x0, [SP]
    // 0x140b93c: mov             x0, x1
    // 0x140b940: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140b940: sub             lr, x0, #0xb7
    //     0x140b944: ldr             lr, [x21, lr, lsl #3]
    //     0x140b948: blr             lr
    // 0x140b94c: LoadField: r1 = r0->field_f
    //     0x140b94c: ldur            w1, [x0, #0xf]
    // 0x140b950: DecompressPointer r1
    //     0x140b950: add             x1, x1, HEAP, lsl #32
    // 0x140b954: cmp             w1, NULL
    // 0x140b958: b.eq            #0x140bb08
    // 0x140b95c: ldur            x0, [fp, #-8]
    // 0x140b960: ldur            x2, [fp, #-0x10]
    // 0x140b964: LoadField: r1 = r0->field_f
    //     0x140b964: ldur            w1, [x0, #0xf]
    // 0x140b968: DecompressPointer r1
    //     0x140b968: add             x1, x1, HEAP, lsl #32
    // 0x140b96c: r0 = controller()
    //     0x140b96c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140b970: LoadField: r1 = r0->field_97
    //     0x140b970: ldur            w1, [x0, #0x97]
    // 0x140b974: DecompressPointer r1
    //     0x140b974: add             x1, x1, HEAP, lsl #32
    // 0x140b978: ldur            x2, [fp, #-0x10]
    // 0x140b97c: LoadField: r0 = r2->field_f
    //     0x140b97c: ldur            w0, [x2, #0xf]
    // 0x140b980: DecompressPointer r0
    //     0x140b980: add             x0, x0, HEAP, lsl #32
    // 0x140b984: stur            x0, [fp, #-0x20]
    // 0x140b988: r0 = value()
    //     0x140b988: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140b98c: r1 = LoadClassIdInstr(r0)
    //     0x140b98c: ldur            x1, [x0, #-1]
    //     0x140b990: ubfx            x1, x1, #0xc, #0x14
    // 0x140b994: ldur            x16, [fp, #-0x20]
    // 0x140b998: stp             x16, x0, [SP]
    // 0x140b99c: mov             x0, x1
    // 0x140b9a0: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140b9a0: sub             lr, x0, #0xb7
    //     0x140b9a4: ldr             lr, [x21, lr, lsl #3]
    //     0x140b9a8: blr             lr
    // 0x140b9ac: LoadField: r1 = r0->field_b
    //     0x140b9ac: ldur            w1, [x0, #0xb]
    // 0x140b9b0: DecompressPointer r1
    //     0x140b9b0: add             x1, x1, HEAP, lsl #32
    // 0x140b9b4: r0 = LoadClassIdInstr(r1)
    //     0x140b9b4: ldur            x0, [x1, #-1]
    //     0x140b9b8: ubfx            x0, x0, #0xc, #0x14
    // 0x140b9bc: r16 = "image"
    //     0x140b9bc: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x140b9c0: stp             x16, x1, [SP]
    // 0x140b9c4: mov             lr, x0
    // 0x140b9c8: ldr             lr, [x21, lr, lsl #3]
    // 0x140b9cc: blr             lr
    // 0x140b9d0: tbnz            w0, #4, #0x140bb00
    // 0x140b9d4: ldur            x0, [fp, #-8]
    // 0x140b9d8: ldur            x2, [fp, #-0x10]
    // 0x140b9dc: LoadField: r1 = r0->field_f
    //     0x140b9dc: ldur            w1, [x0, #0xf]
    // 0x140b9e0: DecompressPointer r1
    //     0x140b9e0: add             x1, x1, HEAP, lsl #32
    // 0x140b9e4: r0 = controller()
    //     0x140b9e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140b9e8: LoadField: r1 = r0->field_97
    //     0x140b9e8: ldur            w1, [x0, #0x97]
    // 0x140b9ec: DecompressPointer r1
    //     0x140b9ec: add             x1, x1, HEAP, lsl #32
    // 0x140b9f0: ldur            x2, [fp, #-0x10]
    // 0x140b9f4: LoadField: r0 = r2->field_f
    //     0x140b9f4: ldur            w0, [x2, #0xf]
    // 0x140b9f8: DecompressPointer r0
    //     0x140b9f8: add             x0, x0, HEAP, lsl #32
    // 0x140b9fc: stur            x0, [fp, #-8]
    // 0x140ba00: r0 = value()
    //     0x140ba00: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140ba04: r1 = LoadClassIdInstr(r0)
    //     0x140ba04: ldur            x1, [x0, #-1]
    //     0x140ba08: ubfx            x1, x1, #0xc, #0x14
    // 0x140ba0c: ldur            x16, [fp, #-8]
    // 0x140ba10: stp             x16, x0, [SP]
    // 0x140ba14: mov             x0, x1
    // 0x140ba18: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140ba18: sub             lr, x0, #0xb7
    //     0x140ba1c: ldr             lr, [x21, lr, lsl #3]
    //     0x140ba20: blr             lr
    // 0x140ba24: LoadField: r1 = r0->field_f
    //     0x140ba24: ldur            w1, [x0, #0xf]
    // 0x140ba28: DecompressPointer r1
    //     0x140ba28: add             x1, x1, HEAP, lsl #32
    // 0x140ba2c: cmp             w1, NULL
    // 0x140ba30: b.ne            #0x140ba3c
    // 0x140ba34: r2 = ""
    //     0x140ba34: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x140ba38: b               #0x140ba40
    // 0x140ba3c: mov             x2, x1
    // 0x140ba40: ldur            x1, [fp, #-0x18]
    // 0x140ba44: stur            x2, [fp, #-8]
    // 0x140ba48: r0 = Image()
    //     0x140ba48: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0x140ba4c: stur            x0, [fp, #-0x20]
    // 0x140ba50: r16 = 57.000000
    //     0x140ba50: add             x16, PP, #0x36, lsl #12  ; [pp+0x36b18] 57
    //     0x140ba54: ldr             x16, [x16, #0xb18]
    // 0x140ba58: r30 = 57.000000
    //     0x140ba58: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b18] 57
    //     0x140ba5c: ldr             lr, [lr, #0xb18]
    // 0x140ba60: stp             lr, x16, [SP, #8]
    // 0x140ba64: r16 = Instance_BoxFit
    //     0x140ba64: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x140ba68: ldr             x16, [x16, #0x118]
    // 0x140ba6c: str             x16, [SP]
    // 0x140ba70: mov             x1, x0
    // 0x140ba74: ldur            x2, [fp, #-8]
    // 0x140ba78: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x2, width, 0x3, null]
    //     0x140ba78: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b48] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0x140ba7c: ldr             x4, [x4, #0xb48]
    // 0x140ba80: r0 = Image.network()
    //     0x140ba80: bl              #0x802090  ; [package:flutter/src/widgets/image.dart] Image::Image.network
    // 0x140ba84: ldur            x0, [fp, #-0x18]
    // 0x140ba88: LoadField: r1 = r0->field_b
    //     0x140ba88: ldur            w1, [x0, #0xb]
    // 0x140ba8c: LoadField: r2 = r0->field_f
    //     0x140ba8c: ldur            w2, [x0, #0xf]
    // 0x140ba90: DecompressPointer r2
    //     0x140ba90: add             x2, x2, HEAP, lsl #32
    // 0x140ba94: LoadField: r3 = r2->field_b
    //     0x140ba94: ldur            w3, [x2, #0xb]
    // 0x140ba98: r2 = LoadInt32Instr(r1)
    //     0x140ba98: sbfx            x2, x1, #1, #0x1f
    // 0x140ba9c: stur            x2, [fp, #-0x38]
    // 0x140baa0: r1 = LoadInt32Instr(r3)
    //     0x140baa0: sbfx            x1, x3, #1, #0x1f
    // 0x140baa4: cmp             x2, x1
    // 0x140baa8: b.ne            #0x140bab4
    // 0x140baac: mov             x1, x0
    // 0x140bab0: r0 = _growToNextCapacity()
    //     0x140bab0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x140bab4: ldur            x2, [fp, #-0x18]
    // 0x140bab8: ldur            x3, [fp, #-0x38]
    // 0x140babc: add             x0, x3, #1
    // 0x140bac0: lsl             x1, x0, #1
    // 0x140bac4: StoreField: r2->field_b = r1
    //     0x140bac4: stur            w1, [x2, #0xb]
    // 0x140bac8: LoadField: r1 = r2->field_f
    //     0x140bac8: ldur            w1, [x2, #0xf]
    // 0x140bacc: DecompressPointer r1
    //     0x140bacc: add             x1, x1, HEAP, lsl #32
    // 0x140bad0: ldur            x0, [fp, #-0x20]
    // 0x140bad4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x140bad4: add             x25, x1, x3, lsl #2
    //     0x140bad8: add             x25, x25, #0xf
    //     0x140badc: str             w0, [x25]
    //     0x140bae0: tbz             w0, #0, #0x140bafc
    //     0x140bae4: ldurb           w16, [x1, #-1]
    //     0x140bae8: ldurb           w17, [x0, #-1]
    //     0x140baec: and             x16, x17, x16, lsr #2
    //     0x140baf0: tst             x16, HEAP, lsr #32
    //     0x140baf4: b.eq            #0x140bafc
    //     0x140baf8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x140bafc: b               #0x140bb0c
    // 0x140bb00: ldur            x2, [fp, #-0x18]
    // 0x140bb04: b               #0x140bb0c
    // 0x140bb08: ldur            x2, [fp, #-0x18]
    // 0x140bb0c: ldr             x1, [fp, #0x18]
    // 0x140bb10: r0 = of()
    //     0x140bb10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x140bb14: LoadField: r2 = r0->field_5b
    //     0x140bb14: ldur            w2, [x0, #0x5b]
    // 0x140bb18: DecompressPointer r2
    //     0x140bb18: add             x2, x2, HEAP, lsl #32
    // 0x140bb1c: r16 = 1.000000
    //     0x140bb1c: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x140bb20: str             x16, [SP]
    // 0x140bb24: r1 = Null
    //     0x140bb24: mov             x1, NULL
    // 0x140bb28: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x140bb28: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x140bb2c: ldr             x4, [x4, #0x108]
    // 0x140bb30: r0 = Border.all()
    //     0x140bb30: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x140bb34: ldr             x1, [fp, #0x18]
    // 0x140bb38: stur            x0, [fp, #-8]
    // 0x140bb3c: r0 = of()
    //     0x140bb3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x140bb40: LoadField: r1 = r0->field_5b
    //     0x140bb40: ldur            w1, [x0, #0x5b]
    // 0x140bb44: DecompressPointer r1
    //     0x140bb44: add             x1, x1, HEAP, lsl #32
    // 0x140bb48: stur            x1, [fp, #-0x20]
    // 0x140bb4c: r0 = BoxDecoration()
    //     0x140bb4c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x140bb50: mov             x1, x0
    // 0x140bb54: ldur            x0, [fp, #-0x20]
    // 0x140bb58: stur            x1, [fp, #-0x28]
    // 0x140bb5c: StoreField: r1->field_7 = r0
    //     0x140bb5c: stur            w0, [x1, #7]
    // 0x140bb60: ldur            x0, [fp, #-8]
    // 0x140bb64: StoreField: r1->field_f = r0
    //     0x140bb64: stur            w0, [x1, #0xf]
    // 0x140bb68: r0 = Instance_BoxShape
    //     0x140bb68: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x140bb6c: ldr             x0, [x0, #0x970]
    // 0x140bb70: StoreField: r1->field_23 = r0
    //     0x140bb70: stur            w0, [x1, #0x23]
    // 0x140bb74: r0 = Container()
    //     0x140bb74: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x140bb78: stur            x0, [fp, #-8]
    // 0x140bb7c: ldur            x16, [fp, #-0x28]
    // 0x140bb80: r30 = Instance_Icon
    //     0x140bb80: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b50] Obj!Icon@d669b1
    //     0x140bb84: ldr             lr, [lr, #0xb50]
    // 0x140bb88: stp             lr, x16, [SP]
    // 0x140bb8c: mov             x1, x0
    // 0x140bb90: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x140bb90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x140bb94: ldr             x4, [x4, #0x88]
    // 0x140bb98: r0 = Container()
    //     0x140bb98: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x140bb9c: r0 = GestureDetector()
    //     0x140bb9c: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0x140bba0: ldur            x2, [fp, #-0x10]
    // 0x140bba4: r1 = Function '<anonymous closure>':.
    //     0x140bba4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b58] AnonymousClosure: (0x140a92c), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x140bba8: ldr             x1, [x1, #0xb58]
    // 0x140bbac: stur            x0, [fp, #-0x10]
    // 0x140bbb0: r0 = AllocateClosure()
    //     0x140bbb0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140bbb4: ldur            x16, [fp, #-8]
    // 0x140bbb8: stp             x16, x0, [SP]
    // 0x140bbbc: ldur            x1, [fp, #-0x10]
    // 0x140bbc0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0x140bbc0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0x140bbc4: ldr             x4, [x4, #0xaf0]
    // 0x140bbc8: r0 = GestureDetector()
    //     0x140bbc8: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0x140bbcc: r1 = <StackParentData>
    //     0x140bbcc: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x140bbd0: ldr             x1, [x1, #0x8e0]
    // 0x140bbd4: r0 = Positioned()
    //     0x140bbd4: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x140bbd8: mov             x2, x0
    // 0x140bbdc: ldur            x0, [fp, #-0x10]
    // 0x140bbe0: stur            x2, [fp, #-8]
    // 0x140bbe4: StoreField: r2->field_b = r0
    //     0x140bbe4: stur            w0, [x2, #0xb]
    // 0x140bbe8: ldur            x0, [fp, #-0x18]
    // 0x140bbec: LoadField: r1 = r0->field_b
    //     0x140bbec: ldur            w1, [x0, #0xb]
    // 0x140bbf0: LoadField: r3 = r0->field_f
    //     0x140bbf0: ldur            w3, [x0, #0xf]
    // 0x140bbf4: DecompressPointer r3
    //     0x140bbf4: add             x3, x3, HEAP, lsl #32
    // 0x140bbf8: LoadField: r4 = r3->field_b
    //     0x140bbf8: ldur            w4, [x3, #0xb]
    // 0x140bbfc: r3 = LoadInt32Instr(r1)
    //     0x140bbfc: sbfx            x3, x1, #1, #0x1f
    // 0x140bc00: stur            x3, [fp, #-0x38]
    // 0x140bc04: r1 = LoadInt32Instr(r4)
    //     0x140bc04: sbfx            x1, x4, #1, #0x1f
    // 0x140bc08: cmp             x3, x1
    // 0x140bc0c: b.ne            #0x140bc18
    // 0x140bc10: mov             x1, x0
    // 0x140bc14: r0 = _growToNextCapacity()
    //     0x140bc14: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x140bc18: ldur            x2, [fp, #-0x18]
    // 0x140bc1c: ldur            x3, [fp, #-0x38]
    // 0x140bc20: add             x0, x3, #1
    // 0x140bc24: lsl             x1, x0, #1
    // 0x140bc28: StoreField: r2->field_b = r1
    //     0x140bc28: stur            w1, [x2, #0xb]
    // 0x140bc2c: LoadField: r1 = r2->field_f
    //     0x140bc2c: ldur            w1, [x2, #0xf]
    // 0x140bc30: DecompressPointer r1
    //     0x140bc30: add             x1, x1, HEAP, lsl #32
    // 0x140bc34: ldur            x0, [fp, #-8]
    // 0x140bc38: ArrayStore: r1[r3] = r0  ; List_4
    //     0x140bc38: add             x25, x1, x3, lsl #2
    //     0x140bc3c: add             x25, x25, #0xf
    //     0x140bc40: str             w0, [x25]
    //     0x140bc44: tbz             w0, #0, #0x140bc60
    //     0x140bc48: ldurb           w16, [x1, #-1]
    //     0x140bc4c: ldurb           w17, [x0, #-1]
    //     0x140bc50: and             x16, x17, x16, lsr #2
    //     0x140bc54: tst             x16, HEAP, lsr #32
    //     0x140bc58: b.eq            #0x140bc60
    //     0x140bc5c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x140bc60: r0 = Stack()
    //     0x140bc60: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x140bc64: mov             x1, x0
    // 0x140bc68: r0 = Instance_Alignment
    //     0x140bc68: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0x140bc6c: ldr             x0, [x0, #0x950]
    // 0x140bc70: stur            x1, [fp, #-8]
    // 0x140bc74: StoreField: r1->field_f = r0
    //     0x140bc74: stur            w0, [x1, #0xf]
    // 0x140bc78: r0 = Instance_StackFit
    //     0x140bc78: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x140bc7c: ldr             x0, [x0, #0xfa8]
    // 0x140bc80: ArrayStore: r1[0] = r0  ; List_4
    //     0x140bc80: stur            w0, [x1, #0x17]
    // 0x140bc84: r0 = Instance_Clip
    //     0x140bc84: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x140bc88: ldr             x0, [x0, #0x7e0]
    // 0x140bc8c: StoreField: r1->field_1b = r0
    //     0x140bc8c: stur            w0, [x1, #0x1b]
    // 0x140bc90: ldur            x0, [fp, #-0x18]
    // 0x140bc94: StoreField: r1->field_b = r0
    //     0x140bc94: stur            w0, [x1, #0xb]
    // 0x140bc98: r0 = Padding()
    //     0x140bc98: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x140bc9c: r1 = Instance_EdgeInsets
    //     0x140bc9c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!EdgeInsets@d599c1
    //     0x140bca0: ldr             x1, [x1, #0xdf8]
    // 0x140bca4: StoreField: r0->field_f = r1
    //     0x140bca4: stur            w1, [x0, #0xf]
    // 0x140bca8: ldur            x1, [fp, #-8]
    // 0x140bcac: StoreField: r0->field_b = r1
    //     0x140bcac: stur            w1, [x0, #0xb]
    // 0x140bcb0: LeaveFrame
    //     0x140bcb0: mov             SP, fp
    //     0x140bcb4: ldp             fp, lr, [SP], #0x10
    // 0x140bcb8: ret
    //     0x140bcb8: ret             
    // 0x140bcbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140bcbc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140bcc0: b               #0x140b10c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x140bcc4, size: 0x118
    // 0x140bcc4: EnterFrame
    //     0x140bcc4: stp             fp, lr, [SP, #-0x10]!
    //     0x140bcc8: mov             fp, SP
    // 0x140bccc: AllocStack(0x30)
    //     0x140bccc: sub             SP, SP, #0x30
    // 0x140bcd0: SetupParameters()
    //     0x140bcd0: ldr             x0, [fp, #0x10]
    //     0x140bcd4: ldur            w1, [x0, #0x17]
    //     0x140bcd8: add             x1, x1, HEAP, lsl #32
    // 0x140bcdc: CheckStackOverflow
    //     0x140bcdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140bce0: cmp             SP, x16
    //     0x140bce4: b.ls            #0x140bdd4
    // 0x140bce8: LoadField: r0 = r1->field_b
    //     0x140bce8: ldur            w0, [x1, #0xb]
    // 0x140bcec: DecompressPointer r0
    //     0x140bcec: add             x0, x0, HEAP, lsl #32
    // 0x140bcf0: stur            x0, [fp, #-8]
    // 0x140bcf4: LoadField: r1 = r0->field_f
    //     0x140bcf4: ldur            w1, [x0, #0xf]
    // 0x140bcf8: DecompressPointer r1
    //     0x140bcf8: add             x1, x1, HEAP, lsl #32
    // 0x140bcfc: r0 = controller()
    //     0x140bcfc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140bd00: LoadField: r2 = r0->field_a7
    //     0x140bd00: ldur            x2, [x0, #0xa7]
    // 0x140bd04: ldur            x0, [fp, #-8]
    // 0x140bd08: stur            x2, [fp, #-0x10]
    // 0x140bd0c: LoadField: r1 = r0->field_f
    //     0x140bd0c: ldur            w1, [x0, #0xf]
    // 0x140bd10: DecompressPointer r1
    //     0x140bd10: add             x1, x1, HEAP, lsl #32
    // 0x140bd14: r0 = controller()
    //     0x140bd14: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140bd18: LoadField: r1 = r0->field_a3
    //     0x140bd18: ldur            w1, [x0, #0xa3]
    // 0x140bd1c: DecompressPointer r1
    //     0x140bd1c: add             x1, x1, HEAP, lsl #32
    // 0x140bd20: r16 = <double>
    //     0x140bd20: ldr             x16, [PP, #0x3fd8]  ; [pp+0x3fd8] TypeArguments: <double>
    // 0x140bd24: stp             x1, x16, [SP, #8]
    // 0x140bd28: ldur            x0, [fp, #-0x10]
    // 0x140bd2c: str             x0, [SP]
    // 0x140bd30: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x140bd30: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x140bd34: r0 = RxNumExt.<()
    //     0x140bd34: bl              #0x140c790  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxNumExt.<
    // 0x140bd38: tbnz            w0, #4, #0x140bd58
    // 0x140bd3c: ldur            x0, [fp, #-8]
    // 0x140bd40: LoadField: r1 = r0->field_f
    //     0x140bd40: ldur            w1, [x0, #0xf]
    // 0x140bd44: DecompressPointer r1
    //     0x140bd44: add             x1, x1, HEAP, lsl #32
    // 0x140bd48: r0 = controller()
    //     0x140bd48: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140bd4c: mov             x1, x0
    // 0x140bd50: r0 = getImageVideoFromGallery()
    //     0x140bd50: bl              #0x140bddc  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::getImageVideoFromGallery
    // 0x140bd54: b               #0x140bdc4
    // 0x140bd58: ldur            x0, [fp, #-8]
    // 0x140bd5c: ldur            x3, [fp, #-0x10]
    // 0x140bd60: LoadField: r4 = r0->field_f
    //     0x140bd60: ldur            w4, [x0, #0xf]
    // 0x140bd64: DecompressPointer r4
    //     0x140bd64: add             x4, x4, HEAP, lsl #32
    // 0x140bd68: stur            x4, [fp, #-0x18]
    // 0x140bd6c: r1 = Null
    //     0x140bd6c: mov             x1, NULL
    // 0x140bd70: r2 = 6
    //     0x140bd70: movz            x2, #0x6
    // 0x140bd74: r0 = AllocateArray()
    //     0x140bd74: bl              #0x16f7198  ; AllocateArrayStub
    // 0x140bd78: mov             x2, x0
    // 0x140bd7c: r16 = "Maximum size allowed is "
    //     0x140bd7c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33930] "Maximum size allowed is "
    //     0x140bd80: ldr             x16, [x16, #0x930]
    // 0x140bd84: StoreField: r2->field_f = r16
    //     0x140bd84: stur            w16, [x2, #0xf]
    // 0x140bd88: ldur            x3, [fp, #-0x10]
    // 0x140bd8c: r0 = BoxInt64Instr(r3)
    //     0x140bd8c: sbfiz           x0, x3, #1, #0x1f
    //     0x140bd90: cmp             x3, x0, asr #1
    //     0x140bd94: b.eq            #0x140bda0
    //     0x140bd98: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x140bd9c: stur            x3, [x0, #7]
    // 0x140bda0: StoreField: r2->field_13 = r0
    //     0x140bda0: stur            w0, [x2, #0x13]
    // 0x140bda4: r16 = " MB."
    //     0x140bda4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33938] " MB."
    //     0x140bda8: ldr             x16, [x16, #0x938]
    // 0x140bdac: ArrayStore: r2[0] = r16  ; List_4
    //     0x140bdac: stur            w16, [x2, #0x17]
    // 0x140bdb0: str             x2, [SP]
    // 0x140bdb4: r0 = _interpolate()
    //     0x140bdb4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x140bdb8: ldur            x1, [fp, #-0x18]
    // 0x140bdbc: mov             x2, x0
    // 0x140bdc0: r0 = showErrorSnackBar()
    //     0x140bdc0: bl              #0x9a5fc0  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showErrorSnackBar
    // 0x140bdc4: r0 = Null
    //     0x140bdc4: mov             x0, NULL
    // 0x140bdc8: LeaveFrame
    //     0x140bdc8: mov             SP, fp
    //     0x140bdcc: ldp             fp, lr, [SP], #0x10
    // 0x140bdd0: ret
    //     0x140bdd0: ret             
    // 0x140bdd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140bdd4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140bdd8: b               #0x140bce8
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x140c804, size: 0x1338
    // 0x140c804: EnterFrame
    //     0x140c804: stp             fp, lr, [SP, #-0x10]!
    //     0x140c808: mov             fp, SP
    // 0x140c80c: AllocStack(0xd0)
    //     0x140c80c: sub             SP, SP, #0xd0
    // 0x140c810: SetupParameters()
    //     0x140c810: ldr             x0, [fp, #0x10]
    //     0x140c814: ldur            w2, [x0, #0x17]
    //     0x140c818: add             x2, x2, HEAP, lsl #32
    //     0x140c81c: stur            x2, [fp, #-8]
    // 0x140c820: CheckStackOverflow
    //     0x140c820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140c824: cmp             SP, x16
    //     0x140c828: b.ls            #0x140db24
    // 0x140c82c: r0 = Obx()
    //     0x140c82c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x140c830: ldur            x2, [fp, #-8]
    // 0x140c834: r1 = Function '<anonymous closure>':.
    //     0x140c834: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a40] AnonymousClosure: (0x140dee0), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x140c838: ldr             x1, [x1, #0xa40]
    // 0x140c83c: stur            x0, [fp, #-0x10]
    // 0x140c840: r0 = AllocateClosure()
    //     0x140c840: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140c844: mov             x1, x0
    // 0x140c848: ldur            x0, [fp, #-0x10]
    // 0x140c84c: StoreField: r0->field_b = r1
    //     0x140c84c: stur            w1, [x0, #0xb]
    // 0x140c850: r0 = ImageHeaders.forImages()
    //     0x140c850: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x140c854: ldur            x2, [fp, #-8]
    // 0x140c858: stur            x0, [fp, #-0x18]
    // 0x140c85c: LoadField: r1 = r2->field_f
    //     0x140c85c: ldur            w1, [x2, #0xf]
    // 0x140c860: DecompressPointer r1
    //     0x140c860: add             x1, x1, HEAP, lsl #32
    // 0x140c864: r0 = controller()
    //     0x140c864: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140c868: LoadField: r1 = r0->field_4b
    //     0x140c868: ldur            w1, [x0, #0x4b]
    // 0x140c86c: DecompressPointer r1
    //     0x140c86c: add             x1, x1, HEAP, lsl #32
    // 0x140c870: r0 = value()
    //     0x140c870: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140c874: LoadField: r1 = r0->field_b
    //     0x140c874: ldur            w1, [x0, #0xb]
    // 0x140c878: DecompressPointer r1
    //     0x140c878: add             x1, x1, HEAP, lsl #32
    // 0x140c87c: cmp             w1, NULL
    // 0x140c880: b.ne            #0x140c88c
    // 0x140c884: r0 = Null
    //     0x140c884: mov             x0, NULL
    // 0x140c888: b               #0x140c894
    // 0x140c88c: LoadField: r0 = r1->field_f
    //     0x140c88c: ldur            w0, [x1, #0xf]
    // 0x140c890: DecompressPointer r0
    //     0x140c890: add             x0, x0, HEAP, lsl #32
    // 0x140c894: cmp             w0, NULL
    // 0x140c898: b.ne            #0x140c8a4
    // 0x140c89c: r3 = ""
    //     0x140c89c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x140c8a0: b               #0x140c8a8
    // 0x140c8a4: mov             x3, x0
    // 0x140c8a8: ldur            x0, [fp, #-8]
    // 0x140c8ac: stur            x3, [fp, #-0x20]
    // 0x140c8b0: r1 = Function '<anonymous closure>':.
    //     0x140c8b0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a48] AnonymousClosure: (0x9d9cb4), in [package:customer_app/app/presentation/views/line/post_order/order_detail/order_detail_view.dart] OrderDetailView::body (0x1506b64)
    //     0x140c8b4: ldr             x1, [x1, #0xa48]
    // 0x140c8b8: r2 = Null
    //     0x140c8b8: mov             x2, NULL
    // 0x140c8bc: r0 = AllocateClosure()
    //     0x140c8bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140c8c0: stur            x0, [fp, #-0x28]
    // 0x140c8c4: r0 = CachedNetworkImage()
    //     0x140c8c4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x140c8c8: stur            x0, [fp, #-0x30]
    // 0x140c8cc: ldur            x16, [fp, #-0x18]
    // 0x140c8d0: r30 = 96.000000
    //     0x140c8d0: add             lr, PP, #0x36, lsl #12  ; [pp+0x36a50] 96
    //     0x140c8d4: ldr             lr, [lr, #0xa50]
    // 0x140c8d8: stp             lr, x16, [SP, #0x18]
    // 0x140c8dc: r16 = 56.000000
    //     0x140c8dc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x140c8e0: ldr             x16, [x16, #0xb78]
    // 0x140c8e4: ldur            lr, [fp, #-0x28]
    // 0x140c8e8: stp             lr, x16, [SP, #8]
    // 0x140c8ec: r16 = Instance_BoxFit
    //     0x140c8ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x140c8f0: ldr             x16, [x16, #0x118]
    // 0x140c8f4: str             x16, [SP]
    // 0x140c8f8: mov             x1, x0
    // 0x140c8fc: ldur            x2, [fp, #-0x20]
    // 0x140c900: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x5, fit, 0x6, height, 0x3, httpHeaders, 0x2, width, 0x4, null]
    //     0x140c900: add             x4, PP, #0x36, lsl #12  ; [pp+0x36a58] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x5, "fit", 0x6, "height", 0x3, "httpHeaders", 0x2, "width", 0x4, Null]
    //     0x140c904: ldr             x4, [x4, #0xa58]
    // 0x140c908: r0 = CachedNetworkImage()
    //     0x140c908: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x140c90c: ldur            x2, [fp, #-8]
    // 0x140c910: LoadField: r1 = r2->field_f
    //     0x140c910: ldur            w1, [x2, #0xf]
    // 0x140c914: DecompressPointer r1
    //     0x140c914: add             x1, x1, HEAP, lsl #32
    // 0x140c918: r0 = controller()
    //     0x140c918: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140c91c: LoadField: r1 = r0->field_4b
    //     0x140c91c: ldur            w1, [x0, #0x4b]
    // 0x140c920: DecompressPointer r1
    //     0x140c920: add             x1, x1, HEAP, lsl #32
    // 0x140c924: r0 = value()
    //     0x140c924: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140c928: LoadField: r1 = r0->field_b
    //     0x140c928: ldur            w1, [x0, #0xb]
    // 0x140c92c: DecompressPointer r1
    //     0x140c92c: add             x1, x1, HEAP, lsl #32
    // 0x140c930: cmp             w1, NULL
    // 0x140c934: b.ne            #0x140c940
    // 0x140c938: r0 = Null
    //     0x140c938: mov             x0, NULL
    // 0x140c93c: b               #0x140c948
    // 0x140c940: LoadField: r0 = r1->field_13
    //     0x140c940: ldur            w0, [x1, #0x13]
    // 0x140c944: DecompressPointer r0
    //     0x140c944: add             x0, x0, HEAP, lsl #32
    // 0x140c948: cmp             w0, NULL
    // 0x140c94c: b.ne            #0x140c958
    // 0x140c950: r3 = ""
    //     0x140c950: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x140c954: b               #0x140c95c
    // 0x140c958: mov             x3, x0
    // 0x140c95c: ldur            x2, [fp, #-8]
    // 0x140c960: ldur            x0, [fp, #-0x30]
    // 0x140c964: stur            x3, [fp, #-0x18]
    // 0x140c968: LoadField: r1 = r2->field_13
    //     0x140c968: ldur            w1, [x2, #0x13]
    // 0x140c96c: DecompressPointer r1
    //     0x140c96c: add             x1, x1, HEAP, lsl #32
    // 0x140c970: r0 = of()
    //     0x140c970: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x140c974: LoadField: r1 = r0->field_87
    //     0x140c974: ldur            w1, [x0, #0x87]
    // 0x140c978: DecompressPointer r1
    //     0x140c978: add             x1, x1, HEAP, lsl #32
    // 0x140c97c: LoadField: r0 = r1->field_7
    //     0x140c97c: ldur            w0, [x1, #7]
    // 0x140c980: DecompressPointer r0
    //     0x140c980: add             x0, x0, HEAP, lsl #32
    // 0x140c984: r16 = 16.000000
    //     0x140c984: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x140c988: ldr             x16, [x16, #0x188]
    // 0x140c98c: r30 = Instance_Color
    //     0x140c98c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140c990: stp             lr, x16, [SP]
    // 0x140c994: mov             x1, x0
    // 0x140c998: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x140c998: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x140c99c: ldr             x4, [x4, #0xaa0]
    // 0x140c9a0: r0 = copyWith()
    //     0x140c9a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x140c9a4: stur            x0, [fp, #-0x20]
    // 0x140c9a8: r0 = Text()
    //     0x140c9a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x140c9ac: mov             x1, x0
    // 0x140c9b0: ldur            x0, [fp, #-0x18]
    // 0x140c9b4: stur            x1, [fp, #-0x28]
    // 0x140c9b8: StoreField: r1->field_b = r0
    //     0x140c9b8: stur            w0, [x1, #0xb]
    // 0x140c9bc: ldur            x0, [fp, #-0x20]
    // 0x140c9c0: StoreField: r1->field_13 = r0
    //     0x140c9c0: stur            w0, [x1, #0x13]
    // 0x140c9c4: r0 = Instance_TextOverflow
    //     0x140c9c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x140c9c8: ldr             x0, [x0, #0xe10]
    // 0x140c9cc: StoreField: r1->field_2b = r0
    //     0x140c9cc: stur            w0, [x1, #0x2b]
    // 0x140c9d0: r2 = 4
    //     0x140c9d0: movz            x2, #0x4
    // 0x140c9d4: StoreField: r1->field_37 = r2
    //     0x140c9d4: stur            w2, [x1, #0x37]
    // 0x140c9d8: r0 = Padding()
    //     0x140c9d8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x140c9dc: mov             x2, x0
    // 0x140c9e0: r0 = Instance_EdgeInsets
    //     0x140c9e0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0x140c9e4: ldr             x0, [x0, #0x778]
    // 0x140c9e8: stur            x2, [fp, #-0x18]
    // 0x140c9ec: StoreField: r2->field_f = r0
    //     0x140c9ec: stur            w0, [x2, #0xf]
    // 0x140c9f0: ldur            x0, [fp, #-0x28]
    // 0x140c9f4: StoreField: r2->field_b = r0
    //     0x140c9f4: stur            w0, [x2, #0xb]
    // 0x140c9f8: ldur            x0, [fp, #-8]
    // 0x140c9fc: LoadField: r1 = r0->field_f
    //     0x140c9fc: ldur            w1, [x0, #0xf]
    // 0x140ca00: DecompressPointer r1
    //     0x140ca00: add             x1, x1, HEAP, lsl #32
    // 0x140ca04: r0 = controller()
    //     0x140ca04: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140ca08: LoadField: r1 = r0->field_c3
    //     0x140ca08: ldur            w1, [x0, #0xc3]
    // 0x140ca0c: DecompressPointer r1
    //     0x140ca0c: add             x1, x1, HEAP, lsl #32
    // 0x140ca10: r0 = value()
    //     0x140ca10: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140ca14: ldur            x2, [fp, #-8]
    // 0x140ca18: r1 = Function '<anonymous closure>':.
    //     0x140ca18: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a60] AnonymousClosure: (0x140de64), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x140ca1c: ldr             x1, [x1, #0xa60]
    // 0x140ca20: stur            x0, [fp, #-0x20]
    // 0x140ca24: r0 = AllocateClosure()
    //     0x140ca24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140ca28: stur            x0, [fp, #-0x28]
    // 0x140ca2c: r0 = RatingBar()
    //     0x140ca2c: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0x140ca30: mov             x3, x0
    // 0x140ca34: ldur            x0, [fp, #-0x28]
    // 0x140ca38: stur            x3, [fp, #-0x38]
    // 0x140ca3c: StoreField: r3->field_b = r0
    //     0x140ca3c: stur            w0, [x3, #0xb]
    // 0x140ca40: r0 = false
    //     0x140ca40: add             x0, NULL, #0x30  ; false
    // 0x140ca44: StoreField: r3->field_1f = r0
    //     0x140ca44: stur            w0, [x3, #0x1f]
    // 0x140ca48: r4 = Instance_Axis
    //     0x140ca48: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x140ca4c: StoreField: r3->field_23 = r4
    //     0x140ca4c: stur            w4, [x3, #0x23]
    // 0x140ca50: r5 = true
    //     0x140ca50: add             x5, NULL, #0x20  ; true
    // 0x140ca54: StoreField: r3->field_27 = r5
    //     0x140ca54: stur            w5, [x3, #0x27]
    // 0x140ca58: d0 = 2.000000
    //     0x140ca58: fmov            d0, #2.00000000
    // 0x140ca5c: StoreField: r3->field_2b = d0
    //     0x140ca5c: stur            d0, [x3, #0x2b]
    // 0x140ca60: StoreField: r3->field_33 = r0
    //     0x140ca60: stur            w0, [x3, #0x33]
    // 0x140ca64: ldur            x1, [fp, #-0x20]
    // 0x140ca68: LoadField: d0 = r1->field_7
    //     0x140ca68: ldur            d0, [x1, #7]
    // 0x140ca6c: StoreField: r3->field_37 = d0
    //     0x140ca6c: stur            d0, [x3, #0x37]
    // 0x140ca70: r1 = 5
    //     0x140ca70: movz            x1, #0x5
    // 0x140ca74: StoreField: r3->field_3f = r1
    //     0x140ca74: stur            x1, [x3, #0x3f]
    // 0x140ca78: r1 = Instance_EdgeInsets
    //     0x140ca78: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a68] Obj!EdgeInsets@d572f1
    //     0x140ca7c: ldr             x1, [x1, #0xa68]
    // 0x140ca80: StoreField: r3->field_47 = r1
    //     0x140ca80: stur            w1, [x3, #0x47]
    // 0x140ca84: d0 = 30.000000
    //     0x140ca84: fmov            d0, #30.00000000
    // 0x140ca88: StoreField: r3->field_4b = d0
    //     0x140ca88: stur            d0, [x3, #0x4b]
    // 0x140ca8c: d0 = 1.000000
    //     0x140ca8c: fmov            d0, #1.00000000
    // 0x140ca90: StoreField: r3->field_53 = d0
    //     0x140ca90: stur            d0, [x3, #0x53]
    // 0x140ca94: StoreField: r3->field_5b = r0
    //     0x140ca94: stur            w0, [x3, #0x5b]
    // 0x140ca98: StoreField: r3->field_5f = r0
    //     0x140ca98: stur            w0, [x3, #0x5f]
    // 0x140ca9c: ldur            x2, [fp, #-8]
    // 0x140caa0: r1 = Function '<anonymous closure>':.
    //     0x140caa0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a70] AnonymousClosure: (0x140dcc0), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x140caa4: ldr             x1, [x1, #0xa70]
    // 0x140caa8: r0 = AllocateClosure()
    //     0x140caa8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140caac: mov             x1, x0
    // 0x140cab0: ldur            x0, [fp, #-0x38]
    // 0x140cab4: StoreField: r0->field_63 = r1
    //     0x140cab4: stur            w1, [x0, #0x63]
    // 0x140cab8: r1 = Null
    //     0x140cab8: mov             x1, NULL
    // 0x140cabc: r2 = 4
    //     0x140cabc: movz            x2, #0x4
    // 0x140cac0: r0 = AllocateArray()
    //     0x140cac0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x140cac4: mov             x2, x0
    // 0x140cac8: ldur            x0, [fp, #-0x18]
    // 0x140cacc: stur            x2, [fp, #-0x20]
    // 0x140cad0: StoreField: r2->field_f = r0
    //     0x140cad0: stur            w0, [x2, #0xf]
    // 0x140cad4: ldur            x0, [fp, #-0x38]
    // 0x140cad8: StoreField: r2->field_13 = r0
    //     0x140cad8: stur            w0, [x2, #0x13]
    // 0x140cadc: r1 = <Widget>
    //     0x140cadc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x140cae0: r0 = AllocateGrowableArray()
    //     0x140cae0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x140cae4: mov             x1, x0
    // 0x140cae8: ldur            x0, [fp, #-0x20]
    // 0x140caec: stur            x1, [fp, #-0x18]
    // 0x140caf0: StoreField: r1->field_f = r0
    //     0x140caf0: stur            w0, [x1, #0xf]
    // 0x140caf4: r2 = 4
    //     0x140caf4: movz            x2, #0x4
    // 0x140caf8: StoreField: r1->field_b = r2
    //     0x140caf8: stur            w2, [x1, #0xb]
    // 0x140cafc: r0 = Column()
    //     0x140cafc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x140cb00: mov             x1, x0
    // 0x140cb04: r0 = Instance_Axis
    //     0x140cb04: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x140cb08: stur            x1, [fp, #-0x20]
    // 0x140cb0c: StoreField: r1->field_f = r0
    //     0x140cb0c: stur            w0, [x1, #0xf]
    // 0x140cb10: r2 = Instance_MainAxisAlignment
    //     0x140cb10: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x140cb14: ldr             x2, [x2, #0xa08]
    // 0x140cb18: StoreField: r1->field_13 = r2
    //     0x140cb18: stur            w2, [x1, #0x13]
    // 0x140cb1c: r3 = Instance_MainAxisSize
    //     0x140cb1c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x140cb20: ldr             x3, [x3, #0xa10]
    // 0x140cb24: ArrayStore: r1[0] = r3  ; List_4
    //     0x140cb24: stur            w3, [x1, #0x17]
    // 0x140cb28: r4 = Instance_CrossAxisAlignment
    //     0x140cb28: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x140cb2c: ldr             x4, [x4, #0x890]
    // 0x140cb30: StoreField: r1->field_1b = r4
    //     0x140cb30: stur            w4, [x1, #0x1b]
    // 0x140cb34: r5 = Instance_VerticalDirection
    //     0x140cb34: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x140cb38: ldr             x5, [x5, #0xa20]
    // 0x140cb3c: StoreField: r1->field_23 = r5
    //     0x140cb3c: stur            w5, [x1, #0x23]
    // 0x140cb40: r6 = Instance_Clip
    //     0x140cb40: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x140cb44: ldr             x6, [x6, #0x38]
    // 0x140cb48: StoreField: r1->field_2b = r6
    //     0x140cb48: stur            w6, [x1, #0x2b]
    // 0x140cb4c: StoreField: r1->field_2f = rZR
    //     0x140cb4c: stur            xzr, [x1, #0x2f]
    // 0x140cb50: ldur            x7, [fp, #-0x18]
    // 0x140cb54: StoreField: r1->field_b = r7
    //     0x140cb54: stur            w7, [x1, #0xb]
    // 0x140cb58: r0 = Padding()
    //     0x140cb58: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x140cb5c: mov             x2, x0
    // 0x140cb60: r0 = Instance_EdgeInsets
    //     0x140cb60: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x140cb64: ldr             x0, [x0, #0xa78]
    // 0x140cb68: stur            x2, [fp, #-0x18]
    // 0x140cb6c: StoreField: r2->field_f = r0
    //     0x140cb6c: stur            w0, [x2, #0xf]
    // 0x140cb70: ldur            x0, [fp, #-0x20]
    // 0x140cb74: StoreField: r2->field_b = r0
    //     0x140cb74: stur            w0, [x2, #0xb]
    // 0x140cb78: r1 = <FlexParentData>
    //     0x140cb78: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x140cb7c: ldr             x1, [x1, #0xe00]
    // 0x140cb80: r0 = Expanded()
    //     0x140cb80: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x140cb84: mov             x3, x0
    // 0x140cb88: r0 = 1
    //     0x140cb88: movz            x0, #0x1
    // 0x140cb8c: stur            x3, [fp, #-0x20]
    // 0x140cb90: StoreField: r3->field_13 = r0
    //     0x140cb90: stur            x0, [x3, #0x13]
    // 0x140cb94: r4 = Instance_FlexFit
    //     0x140cb94: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x140cb98: ldr             x4, [x4, #0xe08]
    // 0x140cb9c: StoreField: r3->field_1b = r4
    //     0x140cb9c: stur            w4, [x3, #0x1b]
    // 0x140cba0: ldur            x1, [fp, #-0x18]
    // 0x140cba4: StoreField: r3->field_b = r1
    //     0x140cba4: stur            w1, [x3, #0xb]
    // 0x140cba8: r1 = Null
    //     0x140cba8: mov             x1, NULL
    // 0x140cbac: r2 = 4
    //     0x140cbac: movz            x2, #0x4
    // 0x140cbb0: r0 = AllocateArray()
    //     0x140cbb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x140cbb4: mov             x2, x0
    // 0x140cbb8: ldur            x0, [fp, #-0x30]
    // 0x140cbbc: stur            x2, [fp, #-0x18]
    // 0x140cbc0: StoreField: r2->field_f = r0
    //     0x140cbc0: stur            w0, [x2, #0xf]
    // 0x140cbc4: ldur            x0, [fp, #-0x20]
    // 0x140cbc8: StoreField: r2->field_13 = r0
    //     0x140cbc8: stur            w0, [x2, #0x13]
    // 0x140cbcc: r1 = <Widget>
    //     0x140cbcc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x140cbd0: r0 = AllocateGrowableArray()
    //     0x140cbd0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x140cbd4: mov             x1, x0
    // 0x140cbd8: ldur            x0, [fp, #-0x18]
    // 0x140cbdc: stur            x1, [fp, #-0x20]
    // 0x140cbe0: StoreField: r1->field_f = r0
    //     0x140cbe0: stur            w0, [x1, #0xf]
    // 0x140cbe4: r2 = 4
    //     0x140cbe4: movz            x2, #0x4
    // 0x140cbe8: StoreField: r1->field_b = r2
    //     0x140cbe8: stur            w2, [x1, #0xb]
    // 0x140cbec: r0 = Row()
    //     0x140cbec: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x140cbf0: mov             x1, x0
    // 0x140cbf4: r0 = Instance_Axis
    //     0x140cbf4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x140cbf8: stur            x1, [fp, #-0x18]
    // 0x140cbfc: StoreField: r1->field_f = r0
    //     0x140cbfc: stur            w0, [x1, #0xf]
    // 0x140cc00: r2 = Instance_MainAxisAlignment
    //     0x140cc00: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x140cc04: ldr             x2, [x2, #0xa08]
    // 0x140cc08: StoreField: r1->field_13 = r2
    //     0x140cc08: stur            w2, [x1, #0x13]
    // 0x140cc0c: r3 = Instance_MainAxisSize
    //     0x140cc0c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x140cc10: ldr             x3, [x3, #0xa10]
    // 0x140cc14: ArrayStore: r1[0] = r3  ; List_4
    //     0x140cc14: stur            w3, [x1, #0x17]
    // 0x140cc18: r4 = Instance_CrossAxisAlignment
    //     0x140cc18: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x140cc1c: ldr             x4, [x4, #0x890]
    // 0x140cc20: StoreField: r1->field_1b = r4
    //     0x140cc20: stur            w4, [x1, #0x1b]
    // 0x140cc24: r5 = Instance_VerticalDirection
    //     0x140cc24: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x140cc28: ldr             x5, [x5, #0xa20]
    // 0x140cc2c: StoreField: r1->field_23 = r5
    //     0x140cc2c: stur            w5, [x1, #0x23]
    // 0x140cc30: r6 = Instance_Clip
    //     0x140cc30: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x140cc34: ldr             x6, [x6, #0x38]
    // 0x140cc38: StoreField: r1->field_2b = r6
    //     0x140cc38: stur            w6, [x1, #0x2b]
    // 0x140cc3c: StoreField: r1->field_2f = rZR
    //     0x140cc3c: stur            xzr, [x1, #0x2f]
    // 0x140cc40: ldur            x7, [fp, #-0x20]
    // 0x140cc44: StoreField: r1->field_b = r7
    //     0x140cc44: stur            w7, [x1, #0xb]
    // 0x140cc48: r0 = Padding()
    //     0x140cc48: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x140cc4c: mov             x3, x0
    // 0x140cc50: r0 = Instance_EdgeInsets
    //     0x140cc50: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f30] Obj!EdgeInsets@d57b31
    //     0x140cc54: ldr             x0, [x0, #0xf30]
    // 0x140cc58: stur            x3, [fp, #-0x20]
    // 0x140cc5c: StoreField: r3->field_f = r0
    //     0x140cc5c: stur            w0, [x3, #0xf]
    // 0x140cc60: ldur            x0, [fp, #-0x18]
    // 0x140cc64: StoreField: r3->field_b = r0
    //     0x140cc64: stur            w0, [x3, #0xb]
    // 0x140cc68: r1 = Null
    //     0x140cc68: mov             x1, NULL
    // 0x140cc6c: r2 = 4
    //     0x140cc6c: movz            x2, #0x4
    // 0x140cc70: r0 = AllocateArray()
    //     0x140cc70: bl              #0x16f7198  ; AllocateArrayStub
    // 0x140cc74: stur            x0, [fp, #-0x18]
    // 0x140cc78: r16 = "Purchased on: "
    //     0x140cc78: add             x16, PP, #0x36, lsl #12  ; [pp+0x36a80] "Purchased on: "
    //     0x140cc7c: ldr             x16, [x16, #0xa80]
    // 0x140cc80: StoreField: r0->field_f = r16
    //     0x140cc80: stur            w16, [x0, #0xf]
    // 0x140cc84: ldur            x2, [fp, #-8]
    // 0x140cc88: LoadField: r1 = r2->field_f
    //     0x140cc88: ldur            w1, [x2, #0xf]
    // 0x140cc8c: DecompressPointer r1
    //     0x140cc8c: add             x1, x1, HEAP, lsl #32
    // 0x140cc90: r0 = controller()
    //     0x140cc90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140cc94: LoadField: r1 = r0->field_4b
    //     0x140cc94: ldur            w1, [x0, #0x4b]
    // 0x140cc98: DecompressPointer r1
    //     0x140cc98: add             x1, x1, HEAP, lsl #32
    // 0x140cc9c: r0 = value()
    //     0x140cc9c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140cca0: LoadField: r1 = r0->field_b
    //     0x140cca0: ldur            w1, [x0, #0xb]
    // 0x140cca4: DecompressPointer r1
    //     0x140cca4: add             x1, x1, HEAP, lsl #32
    // 0x140cca8: cmp             w1, NULL
    // 0x140ccac: b.ne            #0x140ccb8
    // 0x140ccb0: r0 = Null
    //     0x140ccb0: mov             x0, NULL
    // 0x140ccb4: b               #0x140ccc0
    // 0x140ccb8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x140ccb8: ldur            w0, [x1, #0x17]
    // 0x140ccbc: DecompressPointer r0
    //     0x140ccbc: add             x0, x0, HEAP, lsl #32
    // 0x140ccc0: cmp             w0, NULL
    // 0x140ccc4: b.ne            #0x140cccc
    // 0x140ccc8: r0 = ""
    //     0x140ccc8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x140cccc: ldur            x2, [fp, #-8]
    // 0x140ccd0: ldur            x1, [fp, #-0x18]
    // 0x140ccd4: ArrayStore: r1[1] = r0  ; List_4
    //     0x140ccd4: add             x25, x1, #0x13
    //     0x140ccd8: str             w0, [x25]
    //     0x140ccdc: tbz             w0, #0, #0x140ccf8
    //     0x140cce0: ldurb           w16, [x1, #-1]
    //     0x140cce4: ldurb           w17, [x0, #-1]
    //     0x140cce8: and             x16, x17, x16, lsr #2
    //     0x140ccec: tst             x16, HEAP, lsr #32
    //     0x140ccf0: b.eq            #0x140ccf8
    //     0x140ccf4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x140ccf8: ldur            x16, [fp, #-0x18]
    // 0x140ccfc: str             x16, [SP]
    // 0x140cd00: r0 = _interpolate()
    //     0x140cd00: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x140cd04: ldur            x2, [fp, #-8]
    // 0x140cd08: stur            x0, [fp, #-0x18]
    // 0x140cd0c: LoadField: r1 = r2->field_13
    //     0x140cd0c: ldur            w1, [x2, #0x13]
    // 0x140cd10: DecompressPointer r1
    //     0x140cd10: add             x1, x1, HEAP, lsl #32
    // 0x140cd14: r0 = of()
    //     0x140cd14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x140cd18: LoadField: r1 = r0->field_87
    //     0x140cd18: ldur            w1, [x0, #0x87]
    // 0x140cd1c: DecompressPointer r1
    //     0x140cd1c: add             x1, x1, HEAP, lsl #32
    // 0x140cd20: LoadField: r0 = r1->field_2b
    //     0x140cd20: ldur            w0, [x1, #0x2b]
    // 0x140cd24: DecompressPointer r0
    //     0x140cd24: add             x0, x0, HEAP, lsl #32
    // 0x140cd28: stur            x0, [fp, #-0x28]
    // 0x140cd2c: r1 = Instance_Color
    //     0x140cd2c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140cd30: d0 = 0.400000
    //     0x140cd30: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x140cd34: r0 = withOpacity()
    //     0x140cd34: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140cd38: r16 = 12.000000
    //     0x140cd38: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x140cd3c: ldr             x16, [x16, #0x9e8]
    // 0x140cd40: stp             x0, x16, [SP]
    // 0x140cd44: ldur            x1, [fp, #-0x28]
    // 0x140cd48: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x140cd48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x140cd4c: ldr             x4, [x4, #0xaa0]
    // 0x140cd50: r0 = copyWith()
    //     0x140cd50: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x140cd54: stur            x0, [fp, #-0x28]
    // 0x140cd58: r0 = Text()
    //     0x140cd58: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x140cd5c: mov             x1, x0
    // 0x140cd60: ldur            x0, [fp, #-0x18]
    // 0x140cd64: stur            x1, [fp, #-0x30]
    // 0x140cd68: StoreField: r1->field_b = r0
    //     0x140cd68: stur            w0, [x1, #0xb]
    // 0x140cd6c: ldur            x0, [fp, #-0x28]
    // 0x140cd70: StoreField: r1->field_13 = r0
    //     0x140cd70: stur            w0, [x1, #0x13]
    // 0x140cd74: r0 = Padding()
    //     0x140cd74: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x140cd78: mov             x2, x0
    // 0x140cd7c: r0 = Instance_EdgeInsets
    //     0x140cd7c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x140cd80: ldr             x0, [x0, #0x668]
    // 0x140cd84: stur            x2, [fp, #-0x18]
    // 0x140cd88: StoreField: r2->field_f = r0
    //     0x140cd88: stur            w0, [x2, #0xf]
    // 0x140cd8c: ldur            x1, [fp, #-0x30]
    // 0x140cd90: StoreField: r2->field_b = r1
    //     0x140cd90: stur            w1, [x2, #0xb]
    // 0x140cd94: ldur            x3, [fp, #-8]
    // 0x140cd98: LoadField: r1 = r3->field_13
    //     0x140cd98: ldur            w1, [x3, #0x13]
    // 0x140cd9c: DecompressPointer r1
    //     0x140cd9c: add             x1, x1, HEAP, lsl #32
    // 0x140cda0: r0 = of()
    //     0x140cda0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x140cda4: LoadField: r1 = r0->field_87
    //     0x140cda4: ldur            w1, [x0, #0x87]
    // 0x140cda8: DecompressPointer r1
    //     0x140cda8: add             x1, x1, HEAP, lsl #32
    // 0x140cdac: LoadField: r0 = r1->field_7
    //     0x140cdac: ldur            w0, [x1, #7]
    // 0x140cdb0: DecompressPointer r0
    //     0x140cdb0: add             x0, x0, HEAP, lsl #32
    // 0x140cdb4: r16 = 16.000000
    //     0x140cdb4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x140cdb8: ldr             x16, [x16, #0x188]
    // 0x140cdbc: r30 = Instance_Color
    //     0x140cdbc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140cdc0: stp             lr, x16, [SP]
    // 0x140cdc4: mov             x1, x0
    // 0x140cdc8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x140cdc8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x140cdcc: ldr             x4, [x4, #0xaa0]
    // 0x140cdd0: r0 = copyWith()
    //     0x140cdd0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x140cdd4: stur            x0, [fp, #-0x28]
    // 0x140cdd8: r0 = Text()
    //     0x140cdd8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x140cddc: mov             x2, x0
    // 0x140cde0: r0 = "Share photos & video :"
    //     0x140cde0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a88] "Share photos & video :"
    //     0x140cde4: ldr             x0, [x0, #0xa88]
    // 0x140cde8: stur            x2, [fp, #-0x30]
    // 0x140cdec: StoreField: r2->field_b = r0
    //     0x140cdec: stur            w0, [x2, #0xb]
    // 0x140cdf0: ldur            x0, [fp, #-0x28]
    // 0x140cdf4: StoreField: r2->field_13 = r0
    //     0x140cdf4: stur            w0, [x2, #0x13]
    // 0x140cdf8: ldur            x0, [fp, #-8]
    // 0x140cdfc: LoadField: r1 = r0->field_13
    //     0x140cdfc: ldur            w1, [x0, #0x13]
    // 0x140ce00: DecompressPointer r1
    //     0x140ce00: add             x1, x1, HEAP, lsl #32
    // 0x140ce04: r0 = of()
    //     0x140ce04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x140ce08: LoadField: r1 = r0->field_87
    //     0x140ce08: ldur            w1, [x0, #0x87]
    // 0x140ce0c: DecompressPointer r1
    //     0x140ce0c: add             x1, x1, HEAP, lsl #32
    // 0x140ce10: LoadField: r0 = r1->field_2b
    //     0x140ce10: ldur            w0, [x1, #0x2b]
    // 0x140ce14: DecompressPointer r0
    //     0x140ce14: add             x0, x0, HEAP, lsl #32
    // 0x140ce18: stur            x0, [fp, #-0x28]
    // 0x140ce1c: r1 = Instance_Color
    //     0x140ce1c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140ce20: d0 = 0.500000
    //     0x140ce20: fmov            d0, #0.50000000
    // 0x140ce24: r0 = withOpacity()
    //     0x140ce24: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140ce28: r16 = 12.000000
    //     0x140ce28: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x140ce2c: ldr             x16, [x16, #0x9e8]
    // 0x140ce30: stp             x0, x16, [SP]
    // 0x140ce34: ldur            x1, [fp, #-0x28]
    // 0x140ce38: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x140ce38: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x140ce3c: ldr             x4, [x4, #0xaa0]
    // 0x140ce40: r0 = copyWith()
    //     0x140ce40: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x140ce44: stur            x0, [fp, #-0x28]
    // 0x140ce48: r0 = Text()
    //     0x140ce48: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x140ce4c: mov             x1, x0
    // 0x140ce50: r0 = "Capture and add your product experience like Unboxing, Product Usage, etc. (max 100 MB)"
    //     0x140ce50: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a90] "Capture and add your product experience like Unboxing, Product Usage, etc. (max 100 MB)"
    //     0x140ce54: ldr             x0, [x0, #0xa90]
    // 0x140ce58: stur            x1, [fp, #-0x38]
    // 0x140ce5c: StoreField: r1->field_b = r0
    //     0x140ce5c: stur            w0, [x1, #0xb]
    // 0x140ce60: ldur            x0, [fp, #-0x28]
    // 0x140ce64: StoreField: r1->field_13 = r0
    //     0x140ce64: stur            w0, [x1, #0x13]
    // 0x140ce68: r0 = Padding()
    //     0x140ce68: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x140ce6c: mov             x3, x0
    // 0x140ce70: r0 = Instance_EdgeInsets
    //     0x140ce70: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x140ce74: ldr             x0, [x0, #0x770]
    // 0x140ce78: stur            x3, [fp, #-0x28]
    // 0x140ce7c: StoreField: r3->field_f = r0
    //     0x140ce7c: stur            w0, [x3, #0xf]
    // 0x140ce80: ldur            x0, [fp, #-0x38]
    // 0x140ce84: StoreField: r3->field_b = r0
    //     0x140ce84: stur            w0, [x3, #0xb]
    // 0x140ce88: r1 = Null
    //     0x140ce88: mov             x1, NULL
    // 0x140ce8c: r2 = 4
    //     0x140ce8c: movz            x2, #0x4
    // 0x140ce90: r0 = AllocateArray()
    //     0x140ce90: bl              #0x16f7198  ; AllocateArrayStub
    // 0x140ce94: mov             x2, x0
    // 0x140ce98: ldur            x0, [fp, #-0x30]
    // 0x140ce9c: stur            x2, [fp, #-0x38]
    // 0x140cea0: StoreField: r2->field_f = r0
    //     0x140cea0: stur            w0, [x2, #0xf]
    // 0x140cea4: ldur            x0, [fp, #-0x28]
    // 0x140cea8: StoreField: r2->field_13 = r0
    //     0x140cea8: stur            w0, [x2, #0x13]
    // 0x140ceac: r1 = <Widget>
    //     0x140ceac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x140ceb0: r0 = AllocateGrowableArray()
    //     0x140ceb0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x140ceb4: mov             x1, x0
    // 0x140ceb8: ldur            x0, [fp, #-0x38]
    // 0x140cebc: stur            x1, [fp, #-0x28]
    // 0x140cec0: StoreField: r1->field_f = r0
    //     0x140cec0: stur            w0, [x1, #0xf]
    // 0x140cec4: r2 = 4
    //     0x140cec4: movz            x2, #0x4
    // 0x140cec8: StoreField: r1->field_b = r2
    //     0x140cec8: stur            w2, [x1, #0xb]
    // 0x140cecc: r0 = Column()
    //     0x140cecc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x140ced0: mov             x1, x0
    // 0x140ced4: r0 = Instance_Axis
    //     0x140ced4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x140ced8: stur            x1, [fp, #-0x30]
    // 0x140cedc: StoreField: r1->field_f = r0
    //     0x140cedc: stur            w0, [x1, #0xf]
    // 0x140cee0: r2 = Instance_MainAxisAlignment
    //     0x140cee0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x140cee4: ldr             x2, [x2, #0xa08]
    // 0x140cee8: StoreField: r1->field_13 = r2
    //     0x140cee8: stur            w2, [x1, #0x13]
    // 0x140ceec: r3 = Instance_MainAxisSize
    //     0x140ceec: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x140cef0: ldr             x3, [x3, #0xa10]
    // 0x140cef4: ArrayStore: r1[0] = r3  ; List_4
    //     0x140cef4: stur            w3, [x1, #0x17]
    // 0x140cef8: r4 = Instance_CrossAxisAlignment
    //     0x140cef8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x140cefc: ldr             x4, [x4, #0x890]
    // 0x140cf00: StoreField: r1->field_1b = r4
    //     0x140cf00: stur            w4, [x1, #0x1b]
    // 0x140cf04: r5 = Instance_VerticalDirection
    //     0x140cf04: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x140cf08: ldr             x5, [x5, #0xa20]
    // 0x140cf0c: StoreField: r1->field_23 = r5
    //     0x140cf0c: stur            w5, [x1, #0x23]
    // 0x140cf10: r6 = Instance_Clip
    //     0x140cf10: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x140cf14: ldr             x6, [x6, #0x38]
    // 0x140cf18: StoreField: r1->field_2b = r6
    //     0x140cf18: stur            w6, [x1, #0x2b]
    // 0x140cf1c: StoreField: r1->field_2f = rZR
    //     0x140cf1c: stur            xzr, [x1, #0x2f]
    // 0x140cf20: ldur            x7, [fp, #-0x28]
    // 0x140cf24: StoreField: r1->field_b = r7
    //     0x140cf24: stur            w7, [x1, #0xb]
    // 0x140cf28: r0 = Padding()
    //     0x140cf28: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x140cf2c: mov             x2, x0
    // 0x140cf30: r0 = Instance_EdgeInsets
    //     0x140cf30: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0x140cf34: ldr             x0, [x0, #0xa98]
    // 0x140cf38: stur            x2, [fp, #-0x28]
    // 0x140cf3c: StoreField: r2->field_f = r0
    //     0x140cf3c: stur            w0, [x2, #0xf]
    // 0x140cf40: ldur            x1, [fp, #-0x30]
    // 0x140cf44: StoreField: r2->field_b = r1
    //     0x140cf44: stur            w1, [x2, #0xb]
    // 0x140cf48: ldur            x3, [fp, #-8]
    // 0x140cf4c: LoadField: r1 = r3->field_f
    //     0x140cf4c: ldur            w1, [x3, #0xf]
    // 0x140cf50: DecompressPointer r1
    //     0x140cf50: add             x1, x1, HEAP, lsl #32
    // 0x140cf54: r0 = controller()
    //     0x140cf54: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140cf58: LoadField: r1 = r0->field_97
    //     0x140cf58: ldur            w1, [x0, #0x97]
    // 0x140cf5c: DecompressPointer r1
    //     0x140cf5c: add             x1, x1, HEAP, lsl #32
    // 0x140cf60: r0 = value()
    //     0x140cf60: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140cf64: r1 = LoadClassIdInstr(r0)
    //     0x140cf64: ldur            x1, [x0, #-1]
    //     0x140cf68: ubfx            x1, x1, #0xc, #0x14
    // 0x140cf6c: str             x0, [SP]
    // 0x140cf70: mov             x0, x1
    // 0x140cf74: r0 = GDT[cid_x0 + 0xc898]()
    //     0x140cf74: movz            x17, #0xc898
    //     0x140cf78: add             lr, x0, x17
    //     0x140cf7c: ldr             lr, [x21, lr, lsl #3]
    //     0x140cf80: blr             lr
    // 0x140cf84: cbnz            w0, #0x140d1dc
    // 0x140cf88: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x140cf88: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x140cf8c: ldr             x0, [x0, #0x1c80]
    //     0x140cf90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x140cf94: cmp             w0, w16
    //     0x140cf98: b.ne            #0x140cfa4
    //     0x140cf9c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x140cfa0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x140cfa4: r0 = GetNavigation.size()
    //     0x140cfa4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x140cfa8: LoadField: d1 = r0->field_7
    //     0x140cfa8: ldur            d1, [x0, #7]
    // 0x140cfac: stur            d1, [fp, #-0x80]
    // 0x140cfb0: r1 = Instance_Color
    //     0x140cfb0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140cfb4: d0 = 0.050000
    //     0x140cfb4: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0x140cfb8: r0 = withOpacity()
    //     0x140cfb8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140cfbc: stur            x0, [fp, #-0x30]
    // 0x140cfc0: r0 = BoxDecoration()
    //     0x140cfc0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x140cfc4: mov             x2, x0
    // 0x140cfc8: ldur            x0, [fp, #-0x30]
    // 0x140cfcc: stur            x2, [fp, #-0x38]
    // 0x140cfd0: StoreField: r2->field_7 = r0
    //     0x140cfd0: stur            w0, [x2, #7]
    // 0x140cfd4: r0 = Instance_BoxShape
    //     0x140cfd4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x140cfd8: ldr             x0, [x0, #0x80]
    // 0x140cfdc: StoreField: r2->field_23 = r0
    //     0x140cfdc: stur            w0, [x2, #0x23]
    // 0x140cfe0: r1 = Instance_MaterialColor
    //     0x140cfe0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x140cfe4: ldr             x1, [x1, #0xdc0]
    // 0x140cfe8: d0 = 0.300000
    //     0x140cfe8: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x140cfec: ldr             d0, [x17, #0x658]
    // 0x140cff0: r0 = withOpacity()
    //     0x140cff0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140cff4: r1 = Instance_Color
    //     0x140cff4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140cff8: d0 = 0.200000
    //     0x140cff8: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0x140cffc: stur            x0, [fp, #-0x30]
    // 0x140d000: r0 = withOpacity()
    //     0x140d000: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140d004: stur            x0, [fp, #-0x40]
    // 0x140d008: r0 = Icon()
    //     0x140d008: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x140d00c: mov             x3, x0
    // 0x140d010: r0 = Instance_IconData
    //     0x140d010: add             x0, PP, #0x36, lsl #12  ; [pp+0x36aa0] Obj!IconData@d555c1
    //     0x140d014: ldr             x0, [x0, #0xaa0]
    // 0x140d018: stur            x3, [fp, #-0x48]
    // 0x140d01c: StoreField: r3->field_b = r0
    //     0x140d01c: stur            w0, [x3, #0xb]
    // 0x140d020: r0 = 25.000000
    //     0x140d020: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0x140d024: ldr             x0, [x0, #0x98]
    // 0x140d028: StoreField: r3->field_f = r0
    //     0x140d028: stur            w0, [x3, #0xf]
    // 0x140d02c: ldur            x0, [fp, #-0x40]
    // 0x140d030: StoreField: r3->field_23 = r0
    //     0x140d030: stur            w0, [x3, #0x23]
    // 0x140d034: r1 = Null
    //     0x140d034: mov             x1, NULL
    // 0x140d038: r2 = 4
    //     0x140d038: movz            x2, #0x4
    // 0x140d03c: r0 = AllocateArray()
    //     0x140d03c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x140d040: mov             x2, x0
    // 0x140d044: ldur            x0, [fp, #-0x48]
    // 0x140d048: stur            x2, [fp, #-0x40]
    // 0x140d04c: StoreField: r2->field_f = r0
    //     0x140d04c: stur            w0, [x2, #0xf]
    // 0x140d050: r16 = Instance_Icon
    //     0x140d050: add             x16, PP, #0x36, lsl #12  ; [pp+0x36aa8] Obj!Icon@d66331
    //     0x140d054: ldr             x16, [x16, #0xaa8]
    // 0x140d058: StoreField: r2->field_13 = r16
    //     0x140d058: stur            w16, [x2, #0x13]
    // 0x140d05c: r1 = <Widget>
    //     0x140d05c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x140d060: r0 = AllocateGrowableArray()
    //     0x140d060: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x140d064: mov             x1, x0
    // 0x140d068: ldur            x0, [fp, #-0x40]
    // 0x140d06c: stur            x1, [fp, #-0x48]
    // 0x140d070: StoreField: r1->field_f = r0
    //     0x140d070: stur            w0, [x1, #0xf]
    // 0x140d074: r0 = 4
    //     0x140d074: movz            x0, #0x4
    // 0x140d078: StoreField: r1->field_b = r0
    //     0x140d078: stur            w0, [x1, #0xb]
    // 0x140d07c: r0 = Stack()
    //     0x140d07c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x140d080: mov             x1, x0
    // 0x140d084: r0 = Instance_Alignment
    //     0x140d084: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0x140d088: ldr             x0, [x0, #0xa28]
    // 0x140d08c: stur            x1, [fp, #-0x40]
    // 0x140d090: StoreField: r1->field_f = r0
    //     0x140d090: stur            w0, [x1, #0xf]
    // 0x140d094: r0 = Instance_StackFit
    //     0x140d094: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x140d098: ldr             x0, [x0, #0xfa8]
    // 0x140d09c: ArrayStore: r1[0] = r0  ; List_4
    //     0x140d09c: stur            w0, [x1, #0x17]
    // 0x140d0a0: r0 = Instance_Clip
    //     0x140d0a0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x140d0a4: ldr             x0, [x0, #0x7e0]
    // 0x140d0a8: StoreField: r1->field_1b = r0
    //     0x140d0a8: stur            w0, [x1, #0x1b]
    // 0x140d0ac: ldur            x2, [fp, #-0x48]
    // 0x140d0b0: StoreField: r1->field_b = r2
    //     0x140d0b0: stur            w2, [x1, #0xb]
    // 0x140d0b4: r0 = Center()
    //     0x140d0b4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x140d0b8: mov             x1, x0
    // 0x140d0bc: r0 = Instance_Alignment
    //     0x140d0bc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x140d0c0: ldr             x0, [x0, #0xb10]
    // 0x140d0c4: stur            x1, [fp, #-0x48]
    // 0x140d0c8: StoreField: r1->field_f = r0
    //     0x140d0c8: stur            w0, [x1, #0xf]
    // 0x140d0cc: ldur            x0, [fp, #-0x40]
    // 0x140d0d0: StoreField: r1->field_b = r0
    //     0x140d0d0: stur            w0, [x1, #0xb]
    // 0x140d0d4: r0 = DottedBorder()
    //     0x140d0d4: bl              #0x9f8894  ; AllocateDottedBorderStub -> DottedBorder (size=0x3c)
    // 0x140d0d8: mov             x1, x0
    // 0x140d0dc: ldur            x2, [fp, #-0x48]
    // 0x140d0e0: ldur            x3, [fp, #-0x30]
    // 0x140d0e4: r5 = const [5.0, 5.0]
    //     0x140d0e4: add             x5, PP, #0x36, lsl #12  ; [pp+0x36ab0] List<double>(2)
    //     0x140d0e8: ldr             x5, [x5, #0xab0]
    // 0x140d0ec: d0 = 1.000000
    //     0x140d0ec: fmov            d0, #1.00000000
    // 0x140d0f0: stur            x0, [fp, #-0x30]
    // 0x140d0f4: r4 = const [0, 0x5, 0, 0x5, null]
    //     0x140d0f4: ldr             x4, [PP, #0x1330]  ; [pp+0x1330] List(5) [0, 0x5, 0, 0x5, Null]
    // 0x140d0f8: r0 = DottedBorder()
    //     0x140d0f8: bl              #0x9f8704  ; [package:dotted_border/dotted_border.dart] DottedBorder::DottedBorder
    // 0x140d0fc: ldur            d0, [fp, #-0x80]
    // 0x140d100: r0 = inline_Allocate_Double()
    //     0x140d100: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x140d104: add             x0, x0, #0x10
    //     0x140d108: cmp             x1, x0
    //     0x140d10c: b.ls            #0x140db2c
    //     0x140d110: str             x0, [THR, #0x50]  ; THR::top
    //     0x140d114: sub             x0, x0, #0xf
    //     0x140d118: movz            x1, #0xe15c
    //     0x140d11c: movk            x1, #0x3, lsl #16
    //     0x140d120: stur            x1, [x0, #-1]
    // 0x140d124: StoreField: r0->field_7 = d0
    //     0x140d124: stur            d0, [x0, #7]
    // 0x140d128: stur            x0, [fp, #-0x40]
    // 0x140d12c: r0 = Container()
    //     0x140d12c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x140d130: stur            x0, [fp, #-0x48]
    // 0x140d134: ldur            x16, [fp, #-0x40]
    // 0x140d138: r30 = 60.000000
    //     0x140d138: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x140d13c: ldr             lr, [lr, #0x110]
    // 0x140d140: stp             lr, x16, [SP, #0x10]
    // 0x140d144: ldur            x16, [fp, #-0x38]
    // 0x140d148: ldur            lr, [fp, #-0x30]
    // 0x140d14c: stp             lr, x16, [SP]
    // 0x140d150: mov             x1, x0
    // 0x140d154: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x140d154: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x140d158: ldr             x4, [x4, #0x870]
    // 0x140d15c: r0 = Container()
    //     0x140d15c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x140d160: r0 = Padding()
    //     0x140d160: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x140d164: mov             x1, x0
    // 0x140d168: r0 = Instance_EdgeInsets
    //     0x140d168: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!EdgeInsets@d599c1
    //     0x140d16c: ldr             x0, [x0, #0xdf8]
    // 0x140d170: stur            x1, [fp, #-0x30]
    // 0x140d174: StoreField: r1->field_f = r0
    //     0x140d174: stur            w0, [x1, #0xf]
    // 0x140d178: ldur            x0, [fp, #-0x48]
    // 0x140d17c: StoreField: r1->field_b = r0
    //     0x140d17c: stur            w0, [x1, #0xb]
    // 0x140d180: r0 = InkWell()
    //     0x140d180: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x140d184: mov             x3, x0
    // 0x140d188: ldur            x0, [fp, #-0x30]
    // 0x140d18c: stur            x3, [fp, #-0x38]
    // 0x140d190: StoreField: r3->field_b = r0
    //     0x140d190: stur            w0, [x3, #0xb]
    // 0x140d194: ldur            x2, [fp, #-8]
    // 0x140d198: r1 = Function '<anonymous closure>':.
    //     0x140d198: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ab8] AnonymousClosure: (0x140dbb0), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x140d19c: ldr             x1, [x1, #0xab8]
    // 0x140d1a0: r0 = AllocateClosure()
    //     0x140d1a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140d1a4: mov             x1, x0
    // 0x140d1a8: ldur            x0, [fp, #-0x38]
    // 0x140d1ac: StoreField: r0->field_f = r1
    //     0x140d1ac: stur            w1, [x0, #0xf]
    // 0x140d1b0: r1 = true
    //     0x140d1b0: add             x1, NULL, #0x20  ; true
    // 0x140d1b4: StoreField: r0->field_43 = r1
    //     0x140d1b4: stur            w1, [x0, #0x43]
    // 0x140d1b8: r2 = Instance_BoxShape
    //     0x140d1b8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x140d1bc: ldr             x2, [x2, #0x80]
    // 0x140d1c0: StoreField: r0->field_47 = r2
    //     0x140d1c0: stur            w2, [x0, #0x47]
    // 0x140d1c4: StoreField: r0->field_6f = r1
    //     0x140d1c4: stur            w1, [x0, #0x6f]
    // 0x140d1c8: r3 = false
    //     0x140d1c8: add             x3, NULL, #0x30  ; false
    // 0x140d1cc: StoreField: r0->field_73 = r3
    //     0x140d1cc: stur            w3, [x0, #0x73]
    // 0x140d1d0: StoreField: r0->field_83 = r1
    //     0x140d1d0: stur            w1, [x0, #0x83]
    // 0x140d1d4: StoreField: r0->field_7b = r3
    //     0x140d1d4: stur            w3, [x0, #0x7b]
    // 0x140d1d8: b               #0x140d348
    // 0x140d1dc: ldur            x0, [fp, #-8]
    // 0x140d1e0: r3 = false
    //     0x140d1e0: add             x3, NULL, #0x30  ; false
    // 0x140d1e4: r2 = Instance_BoxShape
    //     0x140d1e4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x140d1e8: ldr             x2, [x2, #0x80]
    // 0x140d1ec: LoadField: r1 = r0->field_f
    //     0x140d1ec: ldur            w1, [x0, #0xf]
    // 0x140d1f0: DecompressPointer r1
    //     0x140d1f0: add             x1, x1, HEAP, lsl #32
    // 0x140d1f4: r0 = controller()
    //     0x140d1f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140d1f8: LoadField: r1 = r0->field_97
    //     0x140d1f8: ldur            w1, [x0, #0x97]
    // 0x140d1fc: DecompressPointer r1
    //     0x140d1fc: add             x1, x1, HEAP, lsl #32
    // 0x140d200: r0 = value()
    //     0x140d200: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140d204: r1 = LoadClassIdInstr(r0)
    //     0x140d204: ldur            x1, [x0, #-1]
    //     0x140d208: ubfx            x1, x1, #0xc, #0x14
    // 0x140d20c: str             x0, [SP]
    // 0x140d210: mov             x0, x1
    // 0x140d214: r0 = GDT[cid_x0 + 0xc898]()
    //     0x140d214: movz            x17, #0xc898
    //     0x140d218: add             lr, x0, x17
    //     0x140d21c: ldr             lr, [x21, lr, lsl #3]
    //     0x140d220: blr             lr
    // 0x140d224: r1 = LoadInt32Instr(r0)
    //     0x140d224: sbfx            x1, x0, #1, #0x1f
    //     0x140d228: tbz             w0, #0, #0x140d230
    //     0x140d22c: ldur            x1, [x0, #7]
    // 0x140d230: cmp             x1, #4
    // 0x140d234: b.ge            #0x140d28c
    // 0x140d238: ldur            x2, [fp, #-8]
    // 0x140d23c: LoadField: r1 = r2->field_f
    //     0x140d23c: ldur            w1, [x2, #0xf]
    // 0x140d240: DecompressPointer r1
    //     0x140d240: add             x1, x1, HEAP, lsl #32
    // 0x140d244: r0 = controller()
    //     0x140d244: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140d248: LoadField: r1 = r0->field_97
    //     0x140d248: ldur            w1, [x0, #0x97]
    // 0x140d24c: DecompressPointer r1
    //     0x140d24c: add             x1, x1, HEAP, lsl #32
    // 0x140d250: r0 = value()
    //     0x140d250: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140d254: r1 = LoadClassIdInstr(r0)
    //     0x140d254: ldur            x1, [x0, #-1]
    //     0x140d258: ubfx            x1, x1, #0xc, #0x14
    // 0x140d25c: str             x0, [SP]
    // 0x140d260: mov             x0, x1
    // 0x140d264: r0 = GDT[cid_x0 + 0xc898]()
    //     0x140d264: movz            x17, #0xc898
    //     0x140d268: add             lr, x0, x17
    //     0x140d26c: ldr             lr, [x21, lr, lsl #3]
    //     0x140d270: blr             lr
    // 0x140d274: r1 = LoadInt32Instr(r0)
    //     0x140d274: sbfx            x1, x0, #1, #0x1f
    //     0x140d278: tbz             w0, #0, #0x140d280
    //     0x140d27c: ldur            x1, [x0, #7]
    // 0x140d280: add             x0, x1, #1
    // 0x140d284: mov             x3, x0
    // 0x140d288: b               #0x140d2d8
    // 0x140d28c: ldur            x2, [fp, #-8]
    // 0x140d290: LoadField: r1 = r2->field_f
    //     0x140d290: ldur            w1, [x2, #0xf]
    // 0x140d294: DecompressPointer r1
    //     0x140d294: add             x1, x1, HEAP, lsl #32
    // 0x140d298: r0 = controller()
    //     0x140d298: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140d29c: LoadField: r1 = r0->field_97
    //     0x140d29c: ldur            w1, [x0, #0x97]
    // 0x140d2a0: DecompressPointer r1
    //     0x140d2a0: add             x1, x1, HEAP, lsl #32
    // 0x140d2a4: r0 = value()
    //     0x140d2a4: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140d2a8: r1 = LoadClassIdInstr(r0)
    //     0x140d2a8: ldur            x1, [x0, #-1]
    //     0x140d2ac: ubfx            x1, x1, #0xc, #0x14
    // 0x140d2b0: str             x0, [SP]
    // 0x140d2b4: mov             x0, x1
    // 0x140d2b8: r0 = GDT[cid_x0 + 0xc898]()
    //     0x140d2b8: movz            x17, #0xc898
    //     0x140d2bc: add             lr, x0, x17
    //     0x140d2c0: ldr             lr, [x21, lr, lsl #3]
    //     0x140d2c4: blr             lr
    // 0x140d2c8: r1 = LoadInt32Instr(r0)
    //     0x140d2c8: sbfx            x1, x0, #1, #0x1f
    //     0x140d2cc: tbz             w0, #0, #0x140d2d4
    //     0x140d2d0: ldur            x1, [x0, #7]
    // 0x140d2d4: mov             x3, x1
    // 0x140d2d8: stur            x3, [fp, #-0x50]
    // 0x140d2dc: r1 = Function '<anonymous closure>':.
    //     0x140d2dc: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ac0] AnonymousClosure: (0x9a411c), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0x140d2e0: ldr             x1, [x1, #0xac0]
    // 0x140d2e4: r2 = Null
    //     0x140d2e4: mov             x2, NULL
    // 0x140d2e8: r0 = AllocateClosure()
    //     0x140d2e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140d2ec: ldur            x2, [fp, #-8]
    // 0x140d2f0: r1 = Function '<anonymous closure>':.
    //     0x140d2f0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ac8] AnonymousClosure: (0x140b0e4), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x140d2f4: ldr             x1, [x1, #0xac8]
    // 0x140d2f8: stur            x0, [fp, #-0x30]
    // 0x140d2fc: r0 = AllocateClosure()
    //     0x140d2fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140d300: stur            x0, [fp, #-0x38]
    // 0x140d304: r0 = ListView()
    //     0x140d304: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x140d308: stur            x0, [fp, #-0x40]
    // 0x140d30c: r16 = Instance_BouncingScrollPhysics
    //     0x140d30c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0x140d310: ldr             x16, [x16, #0x890]
    // 0x140d314: r30 = true
    //     0x140d314: add             lr, NULL, #0x20  ; true
    // 0x140d318: stp             lr, x16, [SP, #0x10]
    // 0x140d31c: r16 = true
    //     0x140d31c: add             x16, NULL, #0x20  ; true
    // 0x140d320: r30 = Instance_Axis
    //     0x140d320: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x140d324: stp             lr, x16, [SP]
    // 0x140d328: mov             x1, x0
    // 0x140d32c: ldur            x2, [fp, #-0x38]
    // 0x140d330: ldur            x3, [fp, #-0x50]
    // 0x140d334: ldur            x5, [fp, #-0x30]
    // 0x140d338: r4 = const [0, 0x8, 0x4, 0x4, physics, 0x4, primary, 0x6, scrollDirection, 0x7, shrinkWrap, 0x5, null]
    //     0x140d338: add             x4, PP, #0x36, lsl #12  ; [pp+0x36ad0] List(13) [0, 0x8, 0x4, 0x4, "physics", 0x4, "primary", 0x6, "scrollDirection", 0x7, "shrinkWrap", 0x5, Null]
    //     0x140d33c: ldr             x4, [x4, #0xad0]
    // 0x140d340: r0 = ListView.separated()
    //     0x140d340: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x140d344: ldur            x0, [fp, #-0x40]
    // 0x140d348: ldur            x2, [fp, #-8]
    // 0x140d34c: stur            x0, [fp, #-0x30]
    // 0x140d350: r0 = SizedBox()
    //     0x140d350: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x140d354: mov             x1, x0
    // 0x140d358: r0 = 60.000000
    //     0x140d358: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x140d35c: ldr             x0, [x0, #0x110]
    // 0x140d360: stur            x1, [fp, #-0x38]
    // 0x140d364: StoreField: r1->field_13 = r0
    //     0x140d364: stur            w0, [x1, #0x13]
    // 0x140d368: ldur            x0, [fp, #-0x30]
    // 0x140d36c: StoreField: r1->field_b = r0
    //     0x140d36c: stur            w0, [x1, #0xb]
    // 0x140d370: r0 = Padding()
    //     0x140d370: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x140d374: mov             x2, x0
    // 0x140d378: r0 = Instance_EdgeInsets
    //     0x140d378: add             x0, PP, #0x34, lsl #12  ; [pp+0x340b8] Obj!EdgeInsets@d57981
    //     0x140d37c: ldr             x0, [x0, #0xb8]
    // 0x140d380: stur            x2, [fp, #-0x30]
    // 0x140d384: StoreField: r2->field_f = r0
    //     0x140d384: stur            w0, [x2, #0xf]
    // 0x140d388: ldur            x0, [fp, #-0x38]
    // 0x140d38c: StoreField: r2->field_b = r0
    //     0x140d38c: stur            w0, [x2, #0xb]
    // 0x140d390: ldur            x0, [fp, #-8]
    // 0x140d394: LoadField: r1 = r0->field_13
    //     0x140d394: ldur            w1, [x0, #0x13]
    // 0x140d398: DecompressPointer r1
    //     0x140d398: add             x1, x1, HEAP, lsl #32
    // 0x140d39c: r0 = of()
    //     0x140d39c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x140d3a0: LoadField: r1 = r0->field_87
    //     0x140d3a0: ldur            w1, [x0, #0x87]
    // 0x140d3a4: DecompressPointer r1
    //     0x140d3a4: add             x1, x1, HEAP, lsl #32
    // 0x140d3a8: LoadField: r0 = r1->field_7
    //     0x140d3a8: ldur            w0, [x1, #7]
    // 0x140d3ac: DecompressPointer r0
    //     0x140d3ac: add             x0, x0, HEAP, lsl #32
    // 0x140d3b0: r16 = 16.000000
    //     0x140d3b0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x140d3b4: ldr             x16, [x16, #0x188]
    // 0x140d3b8: r30 = Instance_Color
    //     0x140d3b8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140d3bc: stp             lr, x16, [SP]
    // 0x140d3c0: mov             x1, x0
    // 0x140d3c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x140d3c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x140d3c8: ldr             x4, [x4, #0xaa0]
    // 0x140d3cc: r0 = copyWith()
    //     0x140d3cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x140d3d0: stur            x0, [fp, #-0x38]
    // 0x140d3d4: r0 = Text()
    //     0x140d3d4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x140d3d8: mov             x1, x0
    // 0x140d3dc: r0 = "Write a review :"
    //     0x140d3dc: add             x0, PP, #0x36, lsl #12  ; [pp+0x36ad8] "Write a review :"
    //     0x140d3e0: ldr             x0, [x0, #0xad8]
    // 0x140d3e4: stur            x1, [fp, #-0x40]
    // 0x140d3e8: StoreField: r1->field_b = r0
    //     0x140d3e8: stur            w0, [x1, #0xb]
    // 0x140d3ec: ldur            x0, [fp, #-0x38]
    // 0x140d3f0: StoreField: r1->field_13 = r0
    //     0x140d3f0: stur            w0, [x1, #0x13]
    // 0x140d3f4: r0 = Padding()
    //     0x140d3f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x140d3f8: mov             x2, x0
    // 0x140d3fc: r0 = Instance_EdgeInsets
    //     0x140d3fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x140d400: ldr             x0, [x0, #0x668]
    // 0x140d404: stur            x2, [fp, #-0x38]
    // 0x140d408: StoreField: r2->field_f = r0
    //     0x140d408: stur            w0, [x2, #0xf]
    // 0x140d40c: ldur            x0, [fp, #-0x40]
    // 0x140d410: StoreField: r2->field_b = r0
    //     0x140d410: stur            w0, [x2, #0xb]
    // 0x140d414: ldur            x0, [fp, #-8]
    // 0x140d418: LoadField: r1 = r0->field_f
    //     0x140d418: ldur            w1, [x0, #0xf]
    // 0x140d41c: DecompressPointer r1
    //     0x140d41c: add             x1, x1, HEAP, lsl #32
    // 0x140d420: r0 = controller()
    //     0x140d420: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140d424: LoadField: r1 = r0->field_8b
    //     0x140d424: ldur            w1, [x0, #0x8b]
    // 0x140d428: DecompressPointer r1
    //     0x140d428: add             x1, x1, HEAP, lsl #32
    // 0x140d42c: stur            x1, [fp, #-0x40]
    // 0x140d430: r0 = LengthLimitingTextInputFormatter()
    //     0x140d430: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0x140d434: mov             x3, x0
    // 0x140d438: r0 = 4000
    //     0x140d438: movz            x0, #0xfa0
    // 0x140d43c: stur            x3, [fp, #-0x48]
    // 0x140d440: StoreField: r3->field_7 = r0
    //     0x140d440: stur            w0, [x3, #7]
    // 0x140d444: r1 = Null
    //     0x140d444: mov             x1, NULL
    // 0x140d448: r2 = 2
    //     0x140d448: movz            x2, #0x2
    // 0x140d44c: r0 = AllocateArray()
    //     0x140d44c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x140d450: mov             x2, x0
    // 0x140d454: ldur            x0, [fp, #-0x48]
    // 0x140d458: stur            x2, [fp, #-0x58]
    // 0x140d45c: StoreField: r2->field_f = r0
    //     0x140d45c: stur            w0, [x2, #0xf]
    // 0x140d460: r1 = <TextInputFormatter>
    //     0x140d460: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0x140d464: ldr             x1, [x1, #0x7b0]
    // 0x140d468: r0 = AllocateGrowableArray()
    //     0x140d468: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x140d46c: mov             x2, x0
    // 0x140d470: ldur            x0, [fp, #-0x58]
    // 0x140d474: stur            x2, [fp, #-0x48]
    // 0x140d478: StoreField: r2->field_f = r0
    //     0x140d478: stur            w0, [x2, #0xf]
    // 0x140d47c: r0 = 2
    //     0x140d47c: movz            x0, #0x2
    // 0x140d480: StoreField: r2->field_b = r0
    //     0x140d480: stur            w0, [x2, #0xb]
    // 0x140d484: ldur            x0, [fp, #-8]
    // 0x140d488: LoadField: r1 = r0->field_13
    //     0x140d488: ldur            w1, [x0, #0x13]
    // 0x140d48c: DecompressPointer r1
    //     0x140d48c: add             x1, x1, HEAP, lsl #32
    // 0x140d490: r0 = of()
    //     0x140d490: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x140d494: LoadField: r1 = r0->field_87
    //     0x140d494: ldur            w1, [x0, #0x87]
    // 0x140d498: DecompressPointer r1
    //     0x140d498: add             x1, x1, HEAP, lsl #32
    // 0x140d49c: LoadField: r0 = r1->field_2b
    //     0x140d49c: ldur            w0, [x1, #0x2b]
    // 0x140d4a0: DecompressPointer r0
    //     0x140d4a0: add             x0, x0, HEAP, lsl #32
    // 0x140d4a4: ldur            x2, [fp, #-8]
    // 0x140d4a8: stur            x0, [fp, #-0x58]
    // 0x140d4ac: LoadField: r1 = r2->field_13
    //     0x140d4ac: ldur            w1, [x2, #0x13]
    // 0x140d4b0: DecompressPointer r1
    //     0x140d4b0: add             x1, x1, HEAP, lsl #32
    // 0x140d4b4: r0 = of()
    //     0x140d4b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x140d4b8: LoadField: r1 = r0->field_5b
    //     0x140d4b8: ldur            w1, [x0, #0x5b]
    // 0x140d4bc: DecompressPointer r1
    //     0x140d4bc: add             x1, x1, HEAP, lsl #32
    // 0x140d4c0: str             x1, [SP]
    // 0x140d4c4: ldur            x1, [fp, #-0x58]
    // 0x140d4c8: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x140d4c8: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x140d4cc: ldr             x4, [x4, #0xf40]
    // 0x140d4d0: r0 = copyWith()
    //     0x140d4d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x140d4d4: ldur            x2, [fp, #-8]
    // 0x140d4d8: stur            x0, [fp, #-0x58]
    // 0x140d4dc: LoadField: r1 = r2->field_f
    //     0x140d4dc: ldur            w1, [x2, #0xf]
    // 0x140d4e0: DecompressPointer r1
    //     0x140d4e0: add             x1, x1, HEAP, lsl #32
    // 0x140d4e4: r0 = controller()
    //     0x140d4e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140d4e8: LoadField: r2 = r0->field_8f
    //     0x140d4e8: ldur            w2, [x0, #0x8f]
    // 0x140d4ec: DecompressPointer r2
    //     0x140d4ec: add             x2, x2, HEAP, lsl #32
    // 0x140d4f0: ldur            x0, [fp, #-8]
    // 0x140d4f4: stur            x2, [fp, #-0x60]
    // 0x140d4f8: LoadField: r1 = r0->field_13
    //     0x140d4f8: ldur            w1, [x0, #0x13]
    // 0x140d4fc: DecompressPointer r1
    //     0x140d4fc: add             x1, x1, HEAP, lsl #32
    // 0x140d500: r0 = getTextFormFieldInputDecoration()
    //     0x140d500: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0x140d504: ldur            x2, [fp, #-8]
    // 0x140d508: stur            x0, [fp, #-0x68]
    // 0x140d50c: LoadField: r1 = r2->field_13
    //     0x140d50c: ldur            w1, [x2, #0x13]
    // 0x140d510: DecompressPointer r1
    //     0x140d510: add             x1, x1, HEAP, lsl #32
    // 0x140d514: r0 = of()
    //     0x140d514: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x140d518: LoadField: r1 = r0->field_87
    //     0x140d518: ldur            w1, [x0, #0x87]
    // 0x140d51c: DecompressPointer r1
    //     0x140d51c: add             x1, x1, HEAP, lsl #32
    // 0x140d520: LoadField: r0 = r1->field_2b
    //     0x140d520: ldur            w0, [x1, #0x2b]
    // 0x140d524: DecompressPointer r0
    //     0x140d524: add             x0, x0, HEAP, lsl #32
    // 0x140d528: stur            x0, [fp, #-0x70]
    // 0x140d52c: r1 = Instance_Color
    //     0x140d52c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140d530: d0 = 0.400000
    //     0x140d530: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x140d534: r0 = withOpacity()
    //     0x140d534: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140d538: r16 = 12.000000
    //     0x140d538: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x140d53c: ldr             x16, [x16, #0x9e8]
    // 0x140d540: stp             x16, x0, [SP]
    // 0x140d544: ldur            x1, [fp, #-0x70]
    // 0x140d548: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x140d548: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x140d54c: ldr             x4, [x4, #0x9b8]
    // 0x140d550: r0 = copyWith()
    //     0x140d550: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x140d554: ldur            x2, [fp, #-8]
    // 0x140d558: stur            x0, [fp, #-0x70]
    // 0x140d55c: LoadField: r1 = r2->field_13
    //     0x140d55c: ldur            w1, [x2, #0x13]
    // 0x140d560: DecompressPointer r1
    //     0x140d560: add             x1, x1, HEAP, lsl #32
    // 0x140d564: r0 = of()
    //     0x140d564: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x140d568: LoadField: r1 = r0->field_87
    //     0x140d568: ldur            w1, [x0, #0x87]
    // 0x140d56c: DecompressPointer r1
    //     0x140d56c: add             x1, x1, HEAP, lsl #32
    // 0x140d570: LoadField: r0 = r1->field_2b
    //     0x140d570: ldur            w0, [x1, #0x2b]
    // 0x140d574: DecompressPointer r0
    //     0x140d574: add             x0, x0, HEAP, lsl #32
    // 0x140d578: r16 = Instance_Color
    //     0x140d578: add             x16, PP, #0x33, lsl #12  ; [pp+0x337c0] Obj!Color@d6b0d1
    //     0x140d57c: ldr             x16, [x16, #0x7c0]
    // 0x140d580: r30 = 12.000000
    //     0x140d580: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x140d584: ldr             lr, [lr, #0x9e8]
    // 0x140d588: stp             lr, x16, [SP]
    // 0x140d58c: mov             x1, x0
    // 0x140d590: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x140d590: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x140d594: ldr             x4, [x4, #0x9b8]
    // 0x140d598: r0 = copyWith()
    //     0x140d598: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x140d59c: r16 = "How you use the product. Things that are great about it. Things that aren’t great about it."
    //     0x140d59c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36ae0] "How you use the product. Things that are great about it. Things that aren’t great about it."
    //     0x140d5a0: ldr             x16, [x16, #0xae0]
    // 0x140d5a4: ldur            lr, [fp, #-0x70]
    // 0x140d5a8: stp             lr, x16, [SP, #8]
    // 0x140d5ac: str             x0, [SP]
    // 0x140d5b0: ldur            x1, [fp, #-0x68]
    // 0x140d5b4: r4 = const [0, 0x4, 0x3, 0x1, errorStyle, 0x3, hintStyle, 0x2, hintText, 0x1, null]
    //     0x140d5b4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33fe8] List(11) [0, 0x4, 0x3, 0x1, "errorStyle", 0x3, "hintStyle", 0x2, "hintText", 0x1, Null]
    //     0x140d5b8: ldr             x4, [x4, #0xfe8]
    // 0x140d5bc: r0 = copyWith()
    //     0x140d5bc: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0x140d5c0: ldur            x2, [fp, #-8]
    // 0x140d5c4: r1 = Function '<anonymous closure>':.
    //     0x140d5c4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ae8] AnonymousClosure: (0x140db3c), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x140d5c8: ldr             x1, [x1, #0xae8]
    // 0x140d5cc: stur            x0, [fp, #-0x68]
    // 0x140d5d0: r0 = AllocateClosure()
    //     0x140d5d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140d5d4: r1 = <String>
    //     0x140d5d4: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x140d5d8: stur            x0, [fp, #-0x70]
    // 0x140d5dc: r0 = TextFormField()
    //     0x140d5dc: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0x140d5e0: stur            x0, [fp, #-0x78]
    // 0x140d5e4: r16 = false
    //     0x140d5e4: add             x16, NULL, #0x30  ; false
    // 0x140d5e8: r30 = Instance_AutovalidateMode
    //     0x140d5e8: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0x140d5ec: ldr             lr, [lr, #0x7e8]
    // 0x140d5f0: stp             lr, x16, [SP, #0x40]
    // 0x140d5f4: ldur            x16, [fp, #-0x48]
    // 0x140d5f8: r30 = Instance_TextInputType
    //     0x140d5f8: add             lr, PP, #0x33, lsl #12  ; [pp+0x337f0] Obj!TextInputType@d55b61
    //     0x140d5fc: ldr             lr, [lr, #0x7f0]
    // 0x140d600: stp             lr, x16, [SP, #0x30]
    // 0x140d604: r16 = Instance_TextInputAction
    //     0x140d604: ldr             x16, [PP, #0x70c0]  ; [pp+0x70c0] Obj!TextInputAction@d728e1
    // 0x140d608: r30 = 6
    //     0x140d608: movz            lr, #0x6
    // 0x140d60c: stp             lr, x16, [SP, #0x20]
    // 0x140d610: r16 = 10
    //     0x140d610: movz            x16, #0xa
    // 0x140d614: ldur            lr, [fp, #-0x58]
    // 0x140d618: stp             lr, x16, [SP, #0x10]
    // 0x140d61c: ldur            x16, [fp, #-0x60]
    // 0x140d620: ldur            lr, [fp, #-0x70]
    // 0x140d624: stp             lr, x16, [SP]
    // 0x140d628: mov             x1, x0
    // 0x140d62c: ldur            x2, [fp, #-0x68]
    // 0x140d630: r4 = const [0, 0xc, 0xa, 0x2, autovalidateMode, 0x3, controller, 0xa, enableSuggestions, 0x2, inputFormatters, 0x4, keyboardType, 0x5, maxLines, 0x8, minLines, 0x7, onChanged, 0xb, style, 0x9, textInputAction, 0x6, null]
    //     0x140d630: add             x4, PP, #0x36, lsl #12  ; [pp+0x36af0] List(25) [0, 0xc, 0xa, 0x2, "autovalidateMode", 0x3, "controller", 0xa, "enableSuggestions", 0x2, "inputFormatters", 0x4, "keyboardType", 0x5, "maxLines", 0x8, "minLines", 0x7, "onChanged", 0xb, "style", 0x9, "textInputAction", 0x6, Null]
    //     0x140d634: ldr             x4, [x4, #0xaf0]
    // 0x140d638: r0 = TextFormField()
    //     0x140d638: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0x140d63c: r0 = Form()
    //     0x140d63c: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0x140d640: mov             x1, x0
    // 0x140d644: ldur            x0, [fp, #-0x78]
    // 0x140d648: stur            x1, [fp, #-0x48]
    // 0x140d64c: StoreField: r1->field_b = r0
    //     0x140d64c: stur            w0, [x1, #0xb]
    // 0x140d650: r0 = Instance_AutovalidateMode
    //     0x140d650: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0x140d654: ldr             x0, [x0, #0x800]
    // 0x140d658: StoreField: r1->field_23 = r0
    //     0x140d658: stur            w0, [x1, #0x23]
    // 0x140d65c: ldur            x0, [fp, #-0x40]
    // 0x140d660: StoreField: r1->field_7 = r0
    //     0x140d660: stur            w0, [x1, #7]
    // 0x140d664: r0 = Padding()
    //     0x140d664: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x140d668: mov             x2, x0
    // 0x140d66c: r0 = Instance_EdgeInsets
    //     0x140d66c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe18] Obj!EdgeInsets@d57ef1
    //     0x140d670: ldr             x0, [x0, #0xe18]
    // 0x140d674: stur            x2, [fp, #-0x40]
    // 0x140d678: StoreField: r2->field_f = r0
    //     0x140d678: stur            w0, [x2, #0xf]
    // 0x140d67c: ldur            x0, [fp, #-0x48]
    // 0x140d680: StoreField: r2->field_b = r0
    //     0x140d680: stur            w0, [x2, #0xb]
    // 0x140d684: ldur            x0, [fp, #-8]
    // 0x140d688: LoadField: r1 = r0->field_f
    //     0x140d688: ldur            w1, [x0, #0xf]
    // 0x140d68c: DecompressPointer r1
    //     0x140d68c: add             x1, x1, HEAP, lsl #32
    // 0x140d690: r0 = controller()
    //     0x140d690: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140d694: LoadField: r1 = r0->field_4b
    //     0x140d694: ldur            w1, [x0, #0x4b]
    // 0x140d698: DecompressPointer r1
    //     0x140d698: add             x1, x1, HEAP, lsl #32
    // 0x140d69c: r0 = value()
    //     0x140d69c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140d6a0: LoadField: r1 = r0->field_b
    //     0x140d6a0: ldur            w1, [x0, #0xb]
    // 0x140d6a4: DecompressPointer r1
    //     0x140d6a4: add             x1, x1, HEAP, lsl #32
    // 0x140d6a8: cmp             w1, NULL
    // 0x140d6ac: b.ne            #0x140d6b8
    // 0x140d6b0: r0 = Null
    //     0x140d6b0: mov             x0, NULL
    // 0x140d6b4: b               #0x140d700
    // 0x140d6b8: LoadField: r0 = r1->field_1f
    //     0x140d6b8: ldur            w0, [x1, #0x1f]
    // 0x140d6bc: DecompressPointer r0
    //     0x140d6bc: add             x0, x0, HEAP, lsl #32
    // 0x140d6c0: cmp             w0, NULL
    // 0x140d6c4: b.ne            #0x140d6d0
    // 0x140d6c8: r0 = Null
    //     0x140d6c8: mov             x0, NULL
    // 0x140d6cc: b               #0x140d700
    // 0x140d6d0: LoadField: r1 = r0->field_2f
    //     0x140d6d0: ldur            w1, [x0, #0x2f]
    // 0x140d6d4: DecompressPointer r1
    //     0x140d6d4: add             x1, x1, HEAP, lsl #32
    // 0x140d6d8: cmp             w1, NULL
    // 0x140d6dc: b.ne            #0x140d6e8
    // 0x140d6e0: r0 = Null
    //     0x140d6e0: mov             x0, NULL
    // 0x140d6e4: b               #0x140d700
    // 0x140d6e8: LoadField: r0 = r1->field_7
    //     0x140d6e8: ldur            w0, [x1, #7]
    // 0x140d6ec: cbnz            w0, #0x140d6f8
    // 0x140d6f0: r1 = false
    //     0x140d6f0: add             x1, NULL, #0x30  ; false
    // 0x140d6f4: b               #0x140d6fc
    // 0x140d6f8: r1 = true
    //     0x140d6f8: add             x1, NULL, #0x20  ; true
    // 0x140d6fc: mov             x0, x1
    // 0x140d700: cmp             w0, NULL
    // 0x140d704: b.ne            #0x140d710
    // 0x140d708: r2 = false
    //     0x140d708: add             x2, NULL, #0x30  ; false
    // 0x140d70c: b               #0x140d714
    // 0x140d710: mov             x2, x0
    // 0x140d714: ldur            x0, [fp, #-8]
    // 0x140d718: stur            x2, [fp, #-0x48]
    // 0x140d71c: r1 = Instance_Color
    //     0x140d71c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140d720: d0 = 0.050000
    //     0x140d720: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0x140d724: r0 = withOpacity()
    //     0x140d724: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140d728: stur            x0, [fp, #-0x58]
    // 0x140d72c: r0 = BoxDecoration()
    //     0x140d72c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x140d730: mov             x2, x0
    // 0x140d734: ldur            x0, [fp, #-0x58]
    // 0x140d738: stur            x2, [fp, #-0x60]
    // 0x140d73c: StoreField: r2->field_7 = r0
    //     0x140d73c: stur            w0, [x2, #7]
    // 0x140d740: r0 = Instance_BoxShape
    //     0x140d740: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x140d744: ldr             x0, [x0, #0x80]
    // 0x140d748: StoreField: r2->field_23 = r0
    //     0x140d748: stur            w0, [x2, #0x23]
    // 0x140d74c: r1 = Instance_Color
    //     0x140d74c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140d750: d0 = 0.500000
    //     0x140d750: fmov            d0, #0.50000000
    // 0x140d754: r0 = withOpacity()
    //     0x140d754: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140d758: stur            x0, [fp, #-0x58]
    // 0x140d75c: r0 = Icon()
    //     0x140d75c: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x140d760: mov             x2, x0
    // 0x140d764: r0 = Instance_IconData
    //     0x140d764: add             x0, PP, #0x36, lsl #12  ; [pp+0x36af8] Obj!IconData@d55421
    //     0x140d768: ldr             x0, [x0, #0xaf8]
    // 0x140d76c: stur            x2, [fp, #-0x68]
    // 0x140d770: StoreField: r2->field_b = r0
    //     0x140d770: stur            w0, [x2, #0xb]
    // 0x140d774: ldur            x0, [fp, #-0x58]
    // 0x140d778: StoreField: r2->field_23 = r0
    //     0x140d778: stur            w0, [x2, #0x23]
    // 0x140d77c: ldur            x0, [fp, #-8]
    // 0x140d780: LoadField: r1 = r0->field_f
    //     0x140d780: ldur            w1, [x0, #0xf]
    // 0x140d784: DecompressPointer r1
    //     0x140d784: add             x1, x1, HEAP, lsl #32
    // 0x140d788: r0 = controller()
    //     0x140d788: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140d78c: LoadField: r1 = r0->field_4b
    //     0x140d78c: ldur            w1, [x0, #0x4b]
    // 0x140d790: DecompressPointer r1
    //     0x140d790: add             x1, x1, HEAP, lsl #32
    // 0x140d794: r0 = value()
    //     0x140d794: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140d798: LoadField: r1 = r0->field_b
    //     0x140d798: ldur            w1, [x0, #0xb]
    // 0x140d79c: DecompressPointer r1
    //     0x140d79c: add             x1, x1, HEAP, lsl #32
    // 0x140d7a0: cmp             w1, NULL
    // 0x140d7a4: b.ne            #0x140d7b0
    // 0x140d7a8: r0 = Null
    //     0x140d7a8: mov             x0, NULL
    // 0x140d7ac: b               #0x140d7d4
    // 0x140d7b0: LoadField: r0 = r1->field_1f
    //     0x140d7b0: ldur            w0, [x1, #0x1f]
    // 0x140d7b4: DecompressPointer r0
    //     0x140d7b4: add             x0, x0, HEAP, lsl #32
    // 0x140d7b8: cmp             w0, NULL
    // 0x140d7bc: b.ne            #0x140d7c8
    // 0x140d7c0: r0 = Null
    //     0x140d7c0: mov             x0, NULL
    // 0x140d7c4: b               #0x140d7d4
    // 0x140d7c8: LoadField: r1 = r0->field_2f
    //     0x140d7c8: ldur            w1, [x0, #0x2f]
    // 0x140d7cc: DecompressPointer r1
    //     0x140d7cc: add             x1, x1, HEAP, lsl #32
    // 0x140d7d0: mov             x0, x1
    // 0x140d7d4: cmp             w0, NULL
    // 0x140d7d8: b.ne            #0x140d7e4
    // 0x140d7dc: r10 = ""
    //     0x140d7dc: ldr             x10, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x140d7e0: b               #0x140d7e8
    // 0x140d7e4: mov             x10, x0
    // 0x140d7e8: ldur            x1, [fp, #-8]
    // 0x140d7ec: ldur            x9, [fp, #-0x10]
    // 0x140d7f0: ldur            x8, [fp, #-0x20]
    // 0x140d7f4: ldur            x7, [fp, #-0x18]
    // 0x140d7f8: ldur            x6, [fp, #-0x28]
    // 0x140d7fc: ldur            x5, [fp, #-0x30]
    // 0x140d800: ldur            x4, [fp, #-0x38]
    // 0x140d804: ldur            x3, [fp, #-0x40]
    // 0x140d808: ldur            x2, [fp, #-0x48]
    // 0x140d80c: ldur            x0, [fp, #-0x68]
    // 0x140d810: stur            x10, [fp, #-0x58]
    // 0x140d814: LoadField: r11 = r1->field_13
    //     0x140d814: ldur            w11, [x1, #0x13]
    // 0x140d818: DecompressPointer r11
    //     0x140d818: add             x11, x11, HEAP, lsl #32
    // 0x140d81c: mov             x1, x11
    // 0x140d820: r0 = of()
    //     0x140d820: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x140d824: LoadField: r1 = r0->field_87
    //     0x140d824: ldur            w1, [x0, #0x87]
    // 0x140d828: DecompressPointer r1
    //     0x140d828: add             x1, x1, HEAP, lsl #32
    // 0x140d82c: LoadField: r0 = r1->field_2b
    //     0x140d82c: ldur            w0, [x1, #0x2b]
    // 0x140d830: DecompressPointer r0
    //     0x140d830: add             x0, x0, HEAP, lsl #32
    // 0x140d834: stur            x0, [fp, #-8]
    // 0x140d838: r1 = Instance_Color
    //     0x140d838: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140d83c: d0 = 0.500000
    //     0x140d83c: fmov            d0, #0.50000000
    // 0x140d840: r0 = withOpacity()
    //     0x140d840: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140d844: r16 = 12.000000
    //     0x140d844: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x140d848: ldr             x16, [x16, #0x9e8]
    // 0x140d84c: stp             x0, x16, [SP]
    // 0x140d850: ldur            x1, [fp, #-8]
    // 0x140d854: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x140d854: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x140d858: ldr             x4, [x4, #0xaa0]
    // 0x140d85c: r0 = copyWith()
    //     0x140d85c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x140d860: stur            x0, [fp, #-8]
    // 0x140d864: r0 = Text()
    //     0x140d864: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x140d868: mov             x2, x0
    // 0x140d86c: ldur            x0, [fp, #-0x58]
    // 0x140d870: stur            x2, [fp, #-0x70]
    // 0x140d874: StoreField: r2->field_b = r0
    //     0x140d874: stur            w0, [x2, #0xb]
    // 0x140d878: ldur            x0, [fp, #-8]
    // 0x140d87c: StoreField: r2->field_13 = r0
    //     0x140d87c: stur            w0, [x2, #0x13]
    // 0x140d880: r1 = <FlexParentData>
    //     0x140d880: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x140d884: ldr             x1, [x1, #0xe00]
    // 0x140d888: r0 = Expanded()
    //     0x140d888: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x140d88c: mov             x3, x0
    // 0x140d890: r0 = 1
    //     0x140d890: movz            x0, #0x1
    // 0x140d894: stur            x3, [fp, #-8]
    // 0x140d898: StoreField: r3->field_13 = r0
    //     0x140d898: stur            x0, [x3, #0x13]
    // 0x140d89c: r0 = Instance_FlexFit
    //     0x140d89c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x140d8a0: ldr             x0, [x0, #0xe08]
    // 0x140d8a4: StoreField: r3->field_1b = r0
    //     0x140d8a4: stur            w0, [x3, #0x1b]
    // 0x140d8a8: ldur            x0, [fp, #-0x70]
    // 0x140d8ac: StoreField: r3->field_b = r0
    //     0x140d8ac: stur            w0, [x3, #0xb]
    // 0x140d8b0: r1 = Null
    //     0x140d8b0: mov             x1, NULL
    // 0x140d8b4: r2 = 6
    //     0x140d8b4: movz            x2, #0x6
    // 0x140d8b8: r0 = AllocateArray()
    //     0x140d8b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x140d8bc: mov             x2, x0
    // 0x140d8c0: ldur            x0, [fp, #-0x68]
    // 0x140d8c4: stur            x2, [fp, #-0x58]
    // 0x140d8c8: StoreField: r2->field_f = r0
    //     0x140d8c8: stur            w0, [x2, #0xf]
    // 0x140d8cc: r16 = Instance_SizedBox
    //     0x140d8cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x140d8d0: ldr             x16, [x16, #0xb20]
    // 0x140d8d4: StoreField: r2->field_13 = r16
    //     0x140d8d4: stur            w16, [x2, #0x13]
    // 0x140d8d8: ldur            x0, [fp, #-8]
    // 0x140d8dc: ArrayStore: r2[0] = r0  ; List_4
    //     0x140d8dc: stur            w0, [x2, #0x17]
    // 0x140d8e0: r1 = <Widget>
    //     0x140d8e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x140d8e4: r0 = AllocateGrowableArray()
    //     0x140d8e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x140d8e8: mov             x1, x0
    // 0x140d8ec: ldur            x0, [fp, #-0x58]
    // 0x140d8f0: stur            x1, [fp, #-8]
    // 0x140d8f4: StoreField: r1->field_f = r0
    //     0x140d8f4: stur            w0, [x1, #0xf]
    // 0x140d8f8: r0 = 6
    //     0x140d8f8: movz            x0, #0x6
    // 0x140d8fc: StoreField: r1->field_b = r0
    //     0x140d8fc: stur            w0, [x1, #0xb]
    // 0x140d900: r0 = Row()
    //     0x140d900: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x140d904: mov             x1, x0
    // 0x140d908: r0 = Instance_Axis
    //     0x140d908: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x140d90c: stur            x1, [fp, #-0x58]
    // 0x140d910: StoreField: r1->field_f = r0
    //     0x140d910: stur            w0, [x1, #0xf]
    // 0x140d914: r0 = Instance_MainAxisAlignment
    //     0x140d914: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x140d918: ldr             x0, [x0, #0xa08]
    // 0x140d91c: StoreField: r1->field_13 = r0
    //     0x140d91c: stur            w0, [x1, #0x13]
    // 0x140d920: r2 = Instance_MainAxisSize
    //     0x140d920: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x140d924: ldr             x2, [x2, #0xa10]
    // 0x140d928: ArrayStore: r1[0] = r2  ; List_4
    //     0x140d928: stur            w2, [x1, #0x17]
    // 0x140d92c: r3 = Instance_CrossAxisAlignment
    //     0x140d92c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x140d930: ldr             x3, [x3, #0xa18]
    // 0x140d934: StoreField: r1->field_1b = r3
    //     0x140d934: stur            w3, [x1, #0x1b]
    // 0x140d938: r3 = Instance_VerticalDirection
    //     0x140d938: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x140d93c: ldr             x3, [x3, #0xa20]
    // 0x140d940: StoreField: r1->field_23 = r3
    //     0x140d940: stur            w3, [x1, #0x23]
    // 0x140d944: r4 = Instance_Clip
    //     0x140d944: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x140d948: ldr             x4, [x4, #0x38]
    // 0x140d94c: StoreField: r1->field_2b = r4
    //     0x140d94c: stur            w4, [x1, #0x2b]
    // 0x140d950: StoreField: r1->field_2f = rZR
    //     0x140d950: stur            xzr, [x1, #0x2f]
    // 0x140d954: ldur            x5, [fp, #-8]
    // 0x140d958: StoreField: r1->field_b = r5
    //     0x140d958: stur            w5, [x1, #0xb]
    // 0x140d95c: r0 = Container()
    //     0x140d95c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x140d960: stur            x0, [fp, #-8]
    // 0x140d964: r16 = Instance_EdgeInsets
    //     0x140d964: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x140d968: ldr             x16, [x16, #0x980]
    // 0x140d96c: ldur            lr, [fp, #-0x60]
    // 0x140d970: stp             lr, x16, [SP, #8]
    // 0x140d974: ldur            x16, [fp, #-0x58]
    // 0x140d978: str             x16, [SP]
    // 0x140d97c: mov             x1, x0
    // 0x140d980: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0x140d980: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0x140d984: ldr             x4, [x4, #0x610]
    // 0x140d988: r0 = Container()
    //     0x140d988: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x140d98c: r0 = Padding()
    //     0x140d98c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x140d990: mov             x1, x0
    // 0x140d994: r0 = Instance_EdgeInsets
    //     0x140d994: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0x140d998: ldr             x0, [x0, #0xa98]
    // 0x140d99c: stur            x1, [fp, #-0x58]
    // 0x140d9a0: StoreField: r1->field_f = r0
    //     0x140d9a0: stur            w0, [x1, #0xf]
    // 0x140d9a4: ldur            x0, [fp, #-8]
    // 0x140d9a8: StoreField: r1->field_b = r0
    //     0x140d9a8: stur            w0, [x1, #0xb]
    // 0x140d9ac: r0 = Visibility()
    //     0x140d9ac: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x140d9b0: mov             x3, x0
    // 0x140d9b4: ldur            x0, [fp, #-0x58]
    // 0x140d9b8: stur            x3, [fp, #-8]
    // 0x140d9bc: StoreField: r3->field_b = r0
    //     0x140d9bc: stur            w0, [x3, #0xb]
    // 0x140d9c0: r0 = Instance_SizedBox
    //     0x140d9c0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x140d9c4: StoreField: r3->field_f = r0
    //     0x140d9c4: stur            w0, [x3, #0xf]
    // 0x140d9c8: ldur            x0, [fp, #-0x48]
    // 0x140d9cc: StoreField: r3->field_13 = r0
    //     0x140d9cc: stur            w0, [x3, #0x13]
    // 0x140d9d0: r0 = false
    //     0x140d9d0: add             x0, NULL, #0x30  ; false
    // 0x140d9d4: ArrayStore: r3[0] = r0  ; List_4
    //     0x140d9d4: stur            w0, [x3, #0x17]
    // 0x140d9d8: StoreField: r3->field_1b = r0
    //     0x140d9d8: stur            w0, [x3, #0x1b]
    // 0x140d9dc: StoreField: r3->field_1f = r0
    //     0x140d9dc: stur            w0, [x3, #0x1f]
    // 0x140d9e0: StoreField: r3->field_23 = r0
    //     0x140d9e0: stur            w0, [x3, #0x23]
    // 0x140d9e4: StoreField: r3->field_27 = r0
    //     0x140d9e4: stur            w0, [x3, #0x27]
    // 0x140d9e8: StoreField: r3->field_2b = r0
    //     0x140d9e8: stur            w0, [x3, #0x2b]
    // 0x140d9ec: r1 = Null
    //     0x140d9ec: mov             x1, NULL
    // 0x140d9f0: r2 = 16
    //     0x140d9f0: movz            x2, #0x10
    // 0x140d9f4: r0 = AllocateArray()
    //     0x140d9f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x140d9f8: mov             x2, x0
    // 0x140d9fc: ldur            x0, [fp, #-0x10]
    // 0x140da00: stur            x2, [fp, #-0x48]
    // 0x140da04: StoreField: r2->field_f = r0
    //     0x140da04: stur            w0, [x2, #0xf]
    // 0x140da08: ldur            x0, [fp, #-0x20]
    // 0x140da0c: StoreField: r2->field_13 = r0
    //     0x140da0c: stur            w0, [x2, #0x13]
    // 0x140da10: ldur            x0, [fp, #-0x18]
    // 0x140da14: ArrayStore: r2[0] = r0  ; List_4
    //     0x140da14: stur            w0, [x2, #0x17]
    // 0x140da18: ldur            x0, [fp, #-0x28]
    // 0x140da1c: StoreField: r2->field_1b = r0
    //     0x140da1c: stur            w0, [x2, #0x1b]
    // 0x140da20: ldur            x0, [fp, #-0x30]
    // 0x140da24: StoreField: r2->field_1f = r0
    //     0x140da24: stur            w0, [x2, #0x1f]
    // 0x140da28: ldur            x0, [fp, #-0x38]
    // 0x140da2c: StoreField: r2->field_23 = r0
    //     0x140da2c: stur            w0, [x2, #0x23]
    // 0x140da30: ldur            x0, [fp, #-0x40]
    // 0x140da34: StoreField: r2->field_27 = r0
    //     0x140da34: stur            w0, [x2, #0x27]
    // 0x140da38: ldur            x0, [fp, #-8]
    // 0x140da3c: StoreField: r2->field_2b = r0
    //     0x140da3c: stur            w0, [x2, #0x2b]
    // 0x140da40: r1 = <Widget>
    //     0x140da40: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x140da44: r0 = AllocateGrowableArray()
    //     0x140da44: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x140da48: mov             x1, x0
    // 0x140da4c: ldur            x0, [fp, #-0x48]
    // 0x140da50: stur            x1, [fp, #-8]
    // 0x140da54: StoreField: r1->field_f = r0
    //     0x140da54: stur            w0, [x1, #0xf]
    // 0x140da58: r0 = 16
    //     0x140da58: movz            x0, #0x10
    // 0x140da5c: StoreField: r1->field_b = r0
    //     0x140da5c: stur            w0, [x1, #0xb]
    // 0x140da60: r0 = Column()
    //     0x140da60: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x140da64: mov             x1, x0
    // 0x140da68: r0 = Instance_Axis
    //     0x140da68: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x140da6c: stur            x1, [fp, #-0x10]
    // 0x140da70: StoreField: r1->field_f = r0
    //     0x140da70: stur            w0, [x1, #0xf]
    // 0x140da74: r2 = Instance_MainAxisAlignment
    //     0x140da74: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x140da78: ldr             x2, [x2, #0xa08]
    // 0x140da7c: StoreField: r1->field_13 = r2
    //     0x140da7c: stur            w2, [x1, #0x13]
    // 0x140da80: r2 = Instance_MainAxisSize
    //     0x140da80: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x140da84: ldr             x2, [x2, #0xa10]
    // 0x140da88: ArrayStore: r1[0] = r2  ; List_4
    //     0x140da88: stur            w2, [x1, #0x17]
    // 0x140da8c: r2 = Instance_CrossAxisAlignment
    //     0x140da8c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x140da90: ldr             x2, [x2, #0x890]
    // 0x140da94: StoreField: r1->field_1b = r2
    //     0x140da94: stur            w2, [x1, #0x1b]
    // 0x140da98: r2 = Instance_VerticalDirection
    //     0x140da98: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x140da9c: ldr             x2, [x2, #0xa20]
    // 0x140daa0: StoreField: r1->field_23 = r2
    //     0x140daa0: stur            w2, [x1, #0x23]
    // 0x140daa4: r2 = Instance_Clip
    //     0x140daa4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x140daa8: ldr             x2, [x2, #0x38]
    // 0x140daac: StoreField: r1->field_2b = r2
    //     0x140daac: stur            w2, [x1, #0x2b]
    // 0x140dab0: StoreField: r1->field_2f = rZR
    //     0x140dab0: stur            xzr, [x1, #0x2f]
    // 0x140dab4: ldur            x2, [fp, #-8]
    // 0x140dab8: StoreField: r1->field_b = r2
    //     0x140dab8: stur            w2, [x1, #0xb]
    // 0x140dabc: r0 = SingleChildScrollView()
    //     0x140dabc: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x140dac0: mov             x1, x0
    // 0x140dac4: r0 = Instance_Axis
    //     0x140dac4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x140dac8: stur            x1, [fp, #-8]
    // 0x140dacc: StoreField: r1->field_b = r0
    //     0x140dacc: stur            w0, [x1, #0xb]
    // 0x140dad0: r0 = false
    //     0x140dad0: add             x0, NULL, #0x30  ; false
    // 0x140dad4: StoreField: r1->field_f = r0
    //     0x140dad4: stur            w0, [x1, #0xf]
    // 0x140dad8: ldur            x0, [fp, #-0x10]
    // 0x140dadc: StoreField: r1->field_23 = r0
    //     0x140dadc: stur            w0, [x1, #0x23]
    // 0x140dae0: r0 = Instance_DragStartBehavior
    //     0x140dae0: ldr             x0, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x140dae4: StoreField: r1->field_27 = r0
    //     0x140dae4: stur            w0, [x1, #0x27]
    // 0x140dae8: r0 = Instance_Clip
    //     0x140dae8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x140daec: ldr             x0, [x0, #0x7e0]
    // 0x140daf0: StoreField: r1->field_2b = r0
    //     0x140daf0: stur            w0, [x1, #0x2b]
    // 0x140daf4: r0 = Instance_HitTestBehavior
    //     0x140daf4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x140daf8: ldr             x0, [x0, #0x288]
    // 0x140dafc: StoreField: r1->field_2f = r0
    //     0x140dafc: stur            w0, [x1, #0x2f]
    // 0x140db00: r0 = Padding()
    //     0x140db00: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x140db04: r1 = Instance_EdgeInsets
    //     0x140db04: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b00] Obj!EdgeInsets@d57cb1
    //     0x140db08: ldr             x1, [x1, #0xb00]
    // 0x140db0c: StoreField: r0->field_f = r1
    //     0x140db0c: stur            w1, [x0, #0xf]
    // 0x140db10: ldur            x1, [fp, #-8]
    // 0x140db14: StoreField: r0->field_b = r1
    //     0x140db14: stur            w1, [x0, #0xb]
    // 0x140db18: LeaveFrame
    //     0x140db18: mov             SP, fp
    //     0x140db1c: ldp             fp, lr, [SP], #0x10
    // 0x140db20: ret
    //     0x140db20: ret             
    // 0x140db24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140db24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140db28: b               #0x140c82c
    // 0x140db2c: SaveReg d0
    //     0x140db2c: str             q0, [SP, #-0x10]!
    // 0x140db30: r0 = AllocateDouble()
    //     0x140db30: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x140db34: RestoreReg d0
    //     0x140db34: ldr             q0, [SP], #0x10
    // 0x140db38: b               #0x140d124
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0x140db3c, size: 0x74
    // 0x140db3c: EnterFrame
    //     0x140db3c: stp             fp, lr, [SP, #-0x10]!
    //     0x140db40: mov             fp, SP
    // 0x140db44: AllocStack(0x8)
    //     0x140db44: sub             SP, SP, #8
    // 0x140db48: SetupParameters()
    //     0x140db48: ldr             x0, [fp, #0x18]
    //     0x140db4c: ldur            w1, [x0, #0x17]
    //     0x140db50: add             x1, x1, HEAP, lsl #32
    // 0x140db54: CheckStackOverflow
    //     0x140db54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140db58: cmp             SP, x16
    //     0x140db5c: b.ls            #0x140dba8
    // 0x140db60: LoadField: r0 = r1->field_f
    //     0x140db60: ldur            w0, [x1, #0xf]
    // 0x140db64: DecompressPointer r0
    //     0x140db64: add             x0, x0, HEAP, lsl #32
    // 0x140db68: mov             x1, x0
    // 0x140db6c: r0 = controller()
    //     0x140db6c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140db70: ldr             x1, [fp, #0x10]
    // 0x140db74: stur            x0, [fp, #-8]
    // 0x140db78: cmp             w1, NULL
    // 0x140db7c: b.ne            #0x140db88
    // 0x140db80: r2 = Null
    //     0x140db80: mov             x2, NULL
    // 0x140db84: b               #0x140db90
    // 0x140db88: r0 = trim()
    //     0x140db88: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0x140db8c: mov             x2, x0
    // 0x140db90: ldur            x1, [fp, #-8]
    // 0x140db94: r0 = validateRemark()
    //     0x140db94: bl              #0x140e6d8  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::validateRemark
    // 0x140db98: r0 = Null
    //     0x140db98: mov             x0, NULL
    // 0x140db9c: LeaveFrame
    //     0x140db9c: mov             SP, fp
    //     0x140dba0: ldp             fp, lr, [SP], #0x10
    // 0x140dba4: ret
    //     0x140dba4: ret             
    // 0x140dba8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140dba8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140dbac: b               #0x140db60
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x140dbb0, size: 0x110
    // 0x140dbb0: EnterFrame
    //     0x140dbb0: stp             fp, lr, [SP, #-0x10]!
    //     0x140dbb4: mov             fp, SP
    // 0x140dbb8: AllocStack(0x30)
    //     0x140dbb8: sub             SP, SP, #0x30
    // 0x140dbbc: SetupParameters()
    //     0x140dbbc: ldr             x0, [fp, #0x10]
    //     0x140dbc0: ldur            w2, [x0, #0x17]
    //     0x140dbc4: add             x2, x2, HEAP, lsl #32
    //     0x140dbc8: stur            x2, [fp, #-8]
    // 0x140dbcc: CheckStackOverflow
    //     0x140dbcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140dbd0: cmp             SP, x16
    //     0x140dbd4: b.ls            #0x140dcb8
    // 0x140dbd8: LoadField: r1 = r2->field_f
    //     0x140dbd8: ldur            w1, [x2, #0xf]
    // 0x140dbdc: DecompressPointer r1
    //     0x140dbdc: add             x1, x1, HEAP, lsl #32
    // 0x140dbe0: r0 = controller()
    //     0x140dbe0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140dbe4: LoadField: r2 = r0->field_a7
    //     0x140dbe4: ldur            x2, [x0, #0xa7]
    // 0x140dbe8: ldur            x0, [fp, #-8]
    // 0x140dbec: stur            x2, [fp, #-0x10]
    // 0x140dbf0: LoadField: r1 = r0->field_f
    //     0x140dbf0: ldur            w1, [x0, #0xf]
    // 0x140dbf4: DecompressPointer r1
    //     0x140dbf4: add             x1, x1, HEAP, lsl #32
    // 0x140dbf8: r0 = controller()
    //     0x140dbf8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140dbfc: LoadField: r1 = r0->field_a3
    //     0x140dbfc: ldur            w1, [x0, #0xa3]
    // 0x140dc00: DecompressPointer r1
    //     0x140dc00: add             x1, x1, HEAP, lsl #32
    // 0x140dc04: r16 = <double>
    //     0x140dc04: ldr             x16, [PP, #0x3fd8]  ; [pp+0x3fd8] TypeArguments: <double>
    // 0x140dc08: stp             x1, x16, [SP, #8]
    // 0x140dc0c: ldur            x0, [fp, #-0x10]
    // 0x140dc10: str             x0, [SP]
    // 0x140dc14: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x140dc14: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x140dc18: r0 = RxNumExt.<()
    //     0x140dc18: bl              #0x140c790  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxNumExt.<
    // 0x140dc1c: tbnz            w0, #4, #0x140dc3c
    // 0x140dc20: ldur            x0, [fp, #-8]
    // 0x140dc24: LoadField: r1 = r0->field_f
    //     0x140dc24: ldur            w1, [x0, #0xf]
    // 0x140dc28: DecompressPointer r1
    //     0x140dc28: add             x1, x1, HEAP, lsl #32
    // 0x140dc2c: r0 = controller()
    //     0x140dc2c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140dc30: mov             x1, x0
    // 0x140dc34: r0 = getImageVideoFromGallery()
    //     0x140dc34: bl              #0x140bddc  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::getImageVideoFromGallery
    // 0x140dc38: b               #0x140dca8
    // 0x140dc3c: ldur            x0, [fp, #-8]
    // 0x140dc40: ldur            x3, [fp, #-0x10]
    // 0x140dc44: LoadField: r4 = r0->field_f
    //     0x140dc44: ldur            w4, [x0, #0xf]
    // 0x140dc48: DecompressPointer r4
    //     0x140dc48: add             x4, x4, HEAP, lsl #32
    // 0x140dc4c: stur            x4, [fp, #-0x18]
    // 0x140dc50: r1 = Null
    //     0x140dc50: mov             x1, NULL
    // 0x140dc54: r2 = 6
    //     0x140dc54: movz            x2, #0x6
    // 0x140dc58: r0 = AllocateArray()
    //     0x140dc58: bl              #0x16f7198  ; AllocateArrayStub
    // 0x140dc5c: mov             x2, x0
    // 0x140dc60: r16 = "Maximum size allowed is "
    //     0x140dc60: add             x16, PP, #0x33, lsl #12  ; [pp+0x33930] "Maximum size allowed is "
    //     0x140dc64: ldr             x16, [x16, #0x930]
    // 0x140dc68: StoreField: r2->field_f = r16
    //     0x140dc68: stur            w16, [x2, #0xf]
    // 0x140dc6c: ldur            x3, [fp, #-0x10]
    // 0x140dc70: r0 = BoxInt64Instr(r3)
    //     0x140dc70: sbfiz           x0, x3, #1, #0x1f
    //     0x140dc74: cmp             x3, x0, asr #1
    //     0x140dc78: b.eq            #0x140dc84
    //     0x140dc7c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x140dc80: stur            x3, [x0, #7]
    // 0x140dc84: StoreField: r2->field_13 = r0
    //     0x140dc84: stur            w0, [x2, #0x13]
    // 0x140dc88: r16 = " MB."
    //     0x140dc88: add             x16, PP, #0x33, lsl #12  ; [pp+0x33938] " MB."
    //     0x140dc8c: ldr             x16, [x16, #0x938]
    // 0x140dc90: ArrayStore: r2[0] = r16  ; List_4
    //     0x140dc90: stur            w16, [x2, #0x17]
    // 0x140dc94: str             x2, [SP]
    // 0x140dc98: r0 = _interpolate()
    //     0x140dc98: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x140dc9c: ldur            x1, [fp, #-0x18]
    // 0x140dca0: mov             x2, x0
    // 0x140dca4: r0 = showErrorSnackBar()
    //     0x140dca4: bl              #0x9a5fc0  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showErrorSnackBar
    // 0x140dca8: r0 = Null
    //     0x140dca8: mov             x0, NULL
    // 0x140dcac: LeaveFrame
    //     0x140dcac: mov             SP, fp
    //     0x140dcb0: ldp             fp, lr, [SP], #0x10
    // 0x140dcb4: ret
    //     0x140dcb4: ret             
    // 0x140dcb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140dcb8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140dcbc: b               #0x140dbd8
  }
  [closure] SvgPicture <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x140dcc0, size: 0x1a4
    // 0x140dcc0: EnterFrame
    //     0x140dcc0: stp             fp, lr, [SP, #-0x10]!
    //     0x140dcc4: mov             fp, SP
    // 0x140dcc8: AllocStack(0x18)
    //     0x140dcc8: sub             SP, SP, #0x18
    // 0x140dccc: SetupParameters()
    //     0x140dccc: ldr             x0, [fp, #0x20]
    //     0x140dcd0: ldur            w2, [x0, #0x17]
    //     0x140dcd4: add             x2, x2, HEAP, lsl #32
    //     0x140dcd8: stur            x2, [fp, #-8]
    // 0x140dcdc: CheckStackOverflow
    //     0x140dcdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140dce0: cmp             SP, x16
    //     0x140dce4: b.ls            #0x140de24
    // 0x140dce8: LoadField: r1 = r2->field_f
    //     0x140dce8: ldur            w1, [x2, #0xf]
    // 0x140dcec: DecompressPointer r1
    //     0x140dcec: add             x1, x1, HEAP, lsl #32
    // 0x140dcf0: r0 = controller()
    //     0x140dcf0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140dcf4: LoadField: r1 = r0->field_c3
    //     0x140dcf4: ldur            w1, [x0, #0xc3]
    // 0x140dcf8: DecompressPointer r1
    //     0x140dcf8: add             x1, x1, HEAP, lsl #32
    // 0x140dcfc: r0 = value()
    //     0x140dcfc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140dd00: LoadField: d0 = r0->field_7
    //     0x140dd00: ldur            d0, [x0, #7]
    // 0x140dd04: fcmp            d0, d0
    // 0x140dd08: b.vs            #0x140de2c
    // 0x140dd0c: fcvtzs          x0, d0
    // 0x140dd10: asr             x16, x0, #0x1e
    // 0x140dd14: cmp             x16, x0, asr #63
    // 0x140dd18: b.ne            #0x140de2c
    // 0x140dd1c: lsl             x0, x0, #1
    // 0x140dd20: r1 = LoadInt32Instr(r0)
    //     0x140dd20: sbfx            x1, x0, #1, #0x1f
    //     0x140dd24: tbz             w0, #0, #0x140dd2c
    //     0x140dd28: ldur            x1, [x0, #7]
    // 0x140dd2c: sub             x0, x1, #1
    // 0x140dd30: ldr             x1, [fp, #0x10]
    // 0x140dd34: r2 = LoadInt32Instr(r1)
    //     0x140dd34: sbfx            x2, x1, #1, #0x1f
    //     0x140dd38: tbz             w1, #0, #0x140dd40
    //     0x140dd3c: ldur            x2, [x1, #7]
    // 0x140dd40: cmp             x2, x0
    // 0x140dd44: b.ne            #0x140ddf0
    // 0x140dd48: ldur            x0, [fp, #-8]
    // 0x140dd4c: r1 = Null
    //     0x140dd4c: mov             x1, NULL
    // 0x140dd50: r2 = 6
    //     0x140dd50: movz            x2, #0x6
    // 0x140dd54: r0 = AllocateArray()
    //     0x140dd54: bl              #0x16f7198  ; AllocateArrayStub
    // 0x140dd58: stur            x0, [fp, #-0x10]
    // 0x140dd5c: r16 = "assets/images/star"
    //     0x140dd5c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36bb0] "assets/images/star"
    //     0x140dd60: ldr             x16, [x16, #0xbb0]
    // 0x140dd64: StoreField: r0->field_f = r16
    //     0x140dd64: stur            w16, [x0, #0xf]
    // 0x140dd68: ldur            x1, [fp, #-8]
    // 0x140dd6c: LoadField: r2 = r1->field_f
    //     0x140dd6c: ldur            w2, [x1, #0xf]
    // 0x140dd70: DecompressPointer r2
    //     0x140dd70: add             x2, x2, HEAP, lsl #32
    // 0x140dd74: mov             x1, x2
    // 0x140dd78: r0 = controller()
    //     0x140dd78: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140dd7c: LoadField: r1 = r0->field_c3
    //     0x140dd7c: ldur            w1, [x0, #0xc3]
    // 0x140dd80: DecompressPointer r1
    //     0x140dd80: add             x1, x1, HEAP, lsl #32
    // 0x140dd84: r0 = value()
    //     0x140dd84: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140dd88: LoadField: d0 = r0->field_7
    //     0x140dd88: ldur            d0, [x0, #7]
    // 0x140dd8c: fcmp            d0, d0
    // 0x140dd90: b.vs            #0x140de48
    // 0x140dd94: fcvtzs          x0, d0
    // 0x140dd98: asr             x16, x0, #0x1e
    // 0x140dd9c: cmp             x16, x0, asr #63
    // 0x140dda0: b.ne            #0x140de48
    // 0x140dda4: lsl             x0, x0, #1
    // 0x140dda8: ldur            x1, [fp, #-0x10]
    // 0x140ddac: ArrayStore: r1[1] = r0  ; List_4
    //     0x140ddac: add             x25, x1, #0x13
    //     0x140ddb0: str             w0, [x25]
    //     0x140ddb4: tbz             w0, #0, #0x140ddd0
    //     0x140ddb8: ldurb           w16, [x1, #-1]
    //     0x140ddbc: ldurb           w17, [x0, #-1]
    //     0x140ddc0: and             x16, x17, x16, lsr #2
    //     0x140ddc4: tst             x16, HEAP, lsr #32
    //     0x140ddc8: b.eq            #0x140ddd0
    //     0x140ddcc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x140ddd0: ldur            x0, [fp, #-0x10]
    // 0x140ddd4: r16 = ".svg"
    //     0x140ddd4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36bb8] ".svg"
    //     0x140ddd8: ldr             x16, [x16, #0xbb8]
    // 0x140dddc: ArrayStore: r0[0] = r16  ; List_4
    //     0x140dddc: stur            w16, [x0, #0x17]
    // 0x140dde0: str             x0, [SP]
    // 0x140dde4: r0 = _interpolate()
    //     0x140dde4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x140dde8: mov             x2, x0
    // 0x140ddec: b               #0x140ddf8
    // 0x140ddf0: r2 = "assets/images/ratedStar.svg"
    //     0x140ddf0: add             x2, PP, #0x36, lsl #12  ; [pp+0x36bc0] "assets/images/ratedStar.svg"
    //     0x140ddf4: ldr             x2, [x2, #0xbc0]
    // 0x140ddf8: stur            x2, [fp, #-8]
    // 0x140ddfc: r0 = SvgPicture()
    //     0x140ddfc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x140de00: mov             x1, x0
    // 0x140de04: ldur            x2, [fp, #-8]
    // 0x140de08: stur            x0, [fp, #-8]
    // 0x140de0c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x140de0c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x140de10: r0 = SvgPicture.asset()
    //     0x140de10: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x140de14: ldur            x0, [fp, #-8]
    // 0x140de18: LeaveFrame
    //     0x140de18: mov             SP, fp
    //     0x140de1c: ldp             fp, lr, [SP], #0x10
    // 0x140de20: ret
    //     0x140de20: ret             
    // 0x140de24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140de24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140de28: b               #0x140dce8
    // 0x140de2c: SaveReg d0
    //     0x140de2c: str             q0, [SP, #-0x10]!
    // 0x140de30: r0 = 74
    //     0x140de30: movz            x0, #0x4a
    // 0x140de34: r30 = DoubleToIntegerStub
    //     0x140de34: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0x140de38: LoadField: r30 = r30->field_7
    //     0x140de38: ldur            lr, [lr, #7]
    // 0x140de3c: blr             lr
    // 0x140de40: RestoreReg d0
    //     0x140de40: ldr             q0, [SP], #0x10
    // 0x140de44: b               #0x140dd20
    // 0x140de48: SaveReg d0
    //     0x140de48: str             q0, [SP, #-0x10]!
    // 0x140de4c: r0 = 74
    //     0x140de4c: movz            x0, #0x4a
    // 0x140de50: r30 = DoubleToIntegerStub
    //     0x140de50: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0x140de54: LoadField: r30 = r30->field_7
    //     0x140de54: ldur            lr, [lr, #7]
    // 0x140de58: blr             lr
    // 0x140de5c: RestoreReg d0
    //     0x140de5c: ldr             q0, [SP], #0x10
    // 0x140de60: b               #0x140dda8
  }
  [closure] void <anonymous closure>(dynamic, double) {
    // ** addr: 0x140de64, size: 0x7c
    // 0x140de64: EnterFrame
    //     0x140de64: stp             fp, lr, [SP, #-0x10]!
    //     0x140de68: mov             fp, SP
    // 0x140de6c: AllocStack(0x8)
    //     0x140de6c: sub             SP, SP, #8
    // 0x140de70: SetupParameters()
    //     0x140de70: ldr             x0, [fp, #0x18]
    //     0x140de74: ldur            w2, [x0, #0x17]
    //     0x140de78: add             x2, x2, HEAP, lsl #32
    //     0x140de7c: stur            x2, [fp, #-8]
    // 0x140de80: CheckStackOverflow
    //     0x140de80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140de84: cmp             SP, x16
    //     0x140de88: b.ls            #0x140ded8
    // 0x140de8c: LoadField: r1 = r2->field_f
    //     0x140de8c: ldur            w1, [x2, #0xf]
    // 0x140de90: DecompressPointer r1
    //     0x140de90: add             x1, x1, HEAP, lsl #32
    // 0x140de94: r0 = controller()
    //     0x140de94: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140de98: LoadField: r1 = r0->field_af
    //     0x140de98: ldur            w1, [x0, #0xaf]
    // 0x140de9c: DecompressPointer r1
    //     0x140de9c: add             x1, x1, HEAP, lsl #32
    // 0x140dea0: r2 = true
    //     0x140dea0: add             x2, NULL, #0x20  ; true
    // 0x140dea4: r0 = value=()
    //     0x140dea4: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140dea8: ldur            x0, [fp, #-8]
    // 0x140deac: LoadField: r1 = r0->field_f
    //     0x140deac: ldur            w1, [x0, #0xf]
    // 0x140deb0: DecompressPointer r1
    //     0x140deb0: add             x1, x1, HEAP, lsl #32
    // 0x140deb4: r0 = controller()
    //     0x140deb4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140deb8: LoadField: r1 = r0->field_c3
    //     0x140deb8: ldur            w1, [x0, #0xc3]
    // 0x140debc: DecompressPointer r1
    //     0x140debc: add             x1, x1, HEAP, lsl #32
    // 0x140dec0: ldr             x2, [fp, #0x10]
    // 0x140dec4: r0 = value=()
    //     0x140dec4: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140dec8: r0 = Null
    //     0x140dec8: mov             x0, NULL
    // 0x140decc: LeaveFrame
    //     0x140decc: mov             SP, fp
    //     0x140ded0: ldp             fp, lr, [SP], #0x10
    // 0x140ded4: ret
    //     0x140ded4: ret             
    // 0x140ded8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140ded8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140dedc: b               #0x140de8c
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x140dee0, size: 0x164
    // 0x140dee0: EnterFrame
    //     0x140dee0: stp             fp, lr, [SP, #-0x10]!
    //     0x140dee4: mov             fp, SP
    // 0x140dee8: AllocStack(0x20)
    //     0x140dee8: sub             SP, SP, #0x20
    // 0x140deec: SetupParameters()
    //     0x140deec: ldr             x0, [fp, #0x10]
    //     0x140def0: ldur            w2, [x0, #0x17]
    //     0x140def4: add             x2, x2, HEAP, lsl #32
    //     0x140def8: stur            x2, [fp, #-8]
    // 0x140defc: CheckStackOverflow
    //     0x140defc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140df00: cmp             SP, x16
    //     0x140df04: b.ls            #0x140e038
    // 0x140df08: LoadField: r1 = r2->field_f
    //     0x140df08: ldur            w1, [x2, #0xf]
    // 0x140df0c: DecompressPointer r1
    //     0x140df0c: add             x1, x1, HEAP, lsl #32
    // 0x140df10: r0 = controller()
    //     0x140df10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140df14: LoadField: r1 = r0->field_db
    //     0x140df14: ldur            w1, [x0, #0xdb]
    // 0x140df18: DecompressPointer r1
    //     0x140df18: add             x1, x1, HEAP, lsl #32
    // 0x140df1c: r0 = value()
    //     0x140df1c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140df20: tbnz            w0, #4, #0x140e028
    // 0x140df24: ldur            x2, [fp, #-8]
    // 0x140df28: LoadField: r1 = r2->field_f
    //     0x140df28: ldur            w1, [x2, #0xf]
    // 0x140df2c: DecompressPointer r1
    //     0x140df2c: add             x1, x1, HEAP, lsl #32
    // 0x140df30: r0 = controller()
    //     0x140df30: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140df34: LoadField: r1 = r0->field_db
    //     0x140df34: ldur            w1, [x0, #0xdb]
    // 0x140df38: DecompressPointer r1
    //     0x140df38: add             x1, x1, HEAP, lsl #32
    // 0x140df3c: r2 = false
    //     0x140df3c: add             x2, NULL, #0x30  ; false
    // 0x140df40: r0 = value=()
    //     0x140df40: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140df44: r0 = LoadStaticField(0x878)
    //     0x140df44: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x140df48: ldr             x0, [x0, #0x10f0]
    // 0x140df4c: cmp             w0, NULL
    // 0x140df50: b.eq            #0x140e040
    // 0x140df54: LoadField: r3 = r0->field_53
    //     0x140df54: ldur            w3, [x0, #0x53]
    // 0x140df58: DecompressPointer r3
    //     0x140df58: add             x3, x3, HEAP, lsl #32
    // 0x140df5c: stur            x3, [fp, #-0x18]
    // 0x140df60: LoadField: r0 = r3->field_7
    //     0x140df60: ldur            w0, [x3, #7]
    // 0x140df64: DecompressPointer r0
    //     0x140df64: add             x0, x0, HEAP, lsl #32
    // 0x140df68: ldur            x2, [fp, #-8]
    // 0x140df6c: stur            x0, [fp, #-0x10]
    // 0x140df70: r1 = Function '<anonymous closure>':.
    //     0x140df70: add             x1, PP, #0x36, lsl #12  ; [pp+0x36bc8] AnonymousClosure: (0x140e044), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x140df74: ldr             x1, [x1, #0xbc8]
    // 0x140df78: r0 = AllocateClosure()
    //     0x140df78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140df7c: ldur            x2, [fp, #-0x10]
    // 0x140df80: mov             x3, x0
    // 0x140df84: r1 = Null
    //     0x140df84: mov             x1, NULL
    // 0x140df88: stur            x3, [fp, #-8]
    // 0x140df8c: cmp             w2, NULL
    // 0x140df90: b.eq            #0x140dfb0
    // 0x140df94: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x140df94: ldur            w4, [x2, #0x17]
    // 0x140df98: DecompressPointer r4
    //     0x140df98: add             x4, x4, HEAP, lsl #32
    // 0x140df9c: r8 = X0
    //     0x140df9c: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x140dfa0: LoadField: r9 = r4->field_7
    //     0x140dfa0: ldur            x9, [x4, #7]
    // 0x140dfa4: r3 = Null
    //     0x140dfa4: add             x3, PP, #0x36, lsl #12  ; [pp+0x36bd0] Null
    //     0x140dfa8: ldr             x3, [x3, #0xbd0]
    // 0x140dfac: blr             x9
    // 0x140dfb0: ldur            x0, [fp, #-0x18]
    // 0x140dfb4: LoadField: r1 = r0->field_b
    //     0x140dfb4: ldur            w1, [x0, #0xb]
    // 0x140dfb8: LoadField: r2 = r0->field_f
    //     0x140dfb8: ldur            w2, [x0, #0xf]
    // 0x140dfbc: DecompressPointer r2
    //     0x140dfbc: add             x2, x2, HEAP, lsl #32
    // 0x140dfc0: LoadField: r3 = r2->field_b
    //     0x140dfc0: ldur            w3, [x2, #0xb]
    // 0x140dfc4: r2 = LoadInt32Instr(r1)
    //     0x140dfc4: sbfx            x2, x1, #1, #0x1f
    // 0x140dfc8: stur            x2, [fp, #-0x20]
    // 0x140dfcc: r1 = LoadInt32Instr(r3)
    //     0x140dfcc: sbfx            x1, x3, #1, #0x1f
    // 0x140dfd0: cmp             x2, x1
    // 0x140dfd4: b.ne            #0x140dfe0
    // 0x140dfd8: mov             x1, x0
    // 0x140dfdc: r0 = _growToNextCapacity()
    //     0x140dfdc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x140dfe0: ldur            x2, [fp, #-0x18]
    // 0x140dfe4: ldur            x3, [fp, #-0x20]
    // 0x140dfe8: add             x4, x3, #1
    // 0x140dfec: lsl             x5, x4, #1
    // 0x140dff0: StoreField: r2->field_b = r5
    //     0x140dff0: stur            w5, [x2, #0xb]
    // 0x140dff4: LoadField: r1 = r2->field_f
    //     0x140dff4: ldur            w1, [x2, #0xf]
    // 0x140dff8: DecompressPointer r1
    //     0x140dff8: add             x1, x1, HEAP, lsl #32
    // 0x140dffc: ldur            x0, [fp, #-8]
    // 0x140e000: ArrayStore: r1[r3] = r0  ; List_4
    //     0x140e000: add             x25, x1, x3, lsl #2
    //     0x140e004: add             x25, x25, #0xf
    //     0x140e008: str             w0, [x25]
    //     0x140e00c: tbz             w0, #0, #0x140e028
    //     0x140e010: ldurb           w16, [x1, #-1]
    //     0x140e014: ldurb           w17, [x0, #-1]
    //     0x140e018: and             x16, x17, x16, lsr #2
    //     0x140e01c: tst             x16, HEAP, lsr #32
    //     0x140e020: b.eq            #0x140e028
    //     0x140e024: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x140e028: r0 = Instance_SizedBox
    //     0x140e028: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x140e02c: LeaveFrame
    //     0x140e02c: mov             SP, fp
    //     0x140e030: ldp             fp, lr, [SP], #0x10
    // 0x140e034: ret
    //     0x140e034: ret             
    // 0x140e038: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140e038: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140e03c: b               #0x140df08
    // 0x140e040: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x140e040: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x140e044, size: 0x48
    // 0x140e044: EnterFrame
    //     0x140e044: stp             fp, lr, [SP, #-0x10]!
    //     0x140e048: mov             fp, SP
    // 0x140e04c: ldr             x0, [fp, #0x18]
    // 0x140e050: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x140e050: ldur            w1, [x0, #0x17]
    // 0x140e054: DecompressPointer r1
    //     0x140e054: add             x1, x1, HEAP, lsl #32
    // 0x140e058: CheckStackOverflow
    //     0x140e058: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140e05c: cmp             SP, x16
    //     0x140e060: b.ls            #0x140e084
    // 0x140e064: LoadField: r0 = r1->field_f
    //     0x140e064: ldur            w0, [x1, #0xf]
    // 0x140e068: DecompressPointer r0
    //     0x140e068: add             x0, x0, HEAP, lsl #32
    // 0x140e06c: mov             x1, x0
    // 0x140e070: r0 = _showRatingSuccessBottomSheet()
    //     0x140e070: bl              #0x140e08c  ; [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::_showRatingSuccessBottomSheet
    // 0x140e074: r0 = Null
    //     0x140e074: mov             x0, NULL
    // 0x140e078: LeaveFrame
    //     0x140e078: mov             SP, fp
    //     0x140e07c: ldp             fp, lr, [SP], #0x10
    // 0x140e080: ret
    //     0x140e080: ret             
    // 0x140e084: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140e084: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140e088: b               #0x140e064
  }
  _ _showRatingSuccessBottomSheet(/* No info */) {
    // ** addr: 0x140e08c, size: 0xc0
    // 0x140e08c: EnterFrame
    //     0x140e08c: stp             fp, lr, [SP, #-0x10]!
    //     0x140e090: mov             fp, SP
    // 0x140e094: AllocStack(0x38)
    //     0x140e094: sub             SP, SP, #0x38
    // 0x140e098: SetupParameters(RatingReviewOrderPage this /* r1 => r1, fp-0x8 */)
    //     0x140e098: stur            x1, [fp, #-8]
    // 0x140e09c: CheckStackOverflow
    //     0x140e09c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140e0a0: cmp             SP, x16
    //     0x140e0a4: b.ls            #0x140e140
    // 0x140e0a8: r1 = 1
    //     0x140e0a8: movz            x1, #0x1
    // 0x140e0ac: r0 = AllocateContext()
    //     0x140e0ac: bl              #0x16f6108  ; AllocateContextStub
    // 0x140e0b0: ldur            x1, [fp, #-8]
    // 0x140e0b4: stur            x0, [fp, #-0x10]
    // 0x140e0b8: StoreField: r0->field_f = r1
    //     0x140e0b8: stur            w1, [x0, #0xf]
    // 0x140e0bc: r0 = controller()
    //     0x140e0bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140e0c0: mov             x1, x0
    // 0x140e0c4: r0 = false
    //     0x140e0c4: add             x0, NULL, #0x30  ; false
    // 0x140e0c8: StoreField: r1->field_c7 = r0
    //     0x140e0c8: stur            w0, [x1, #0xc7]
    // 0x140e0cc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x140e0cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x140e0d0: ldr             x0, [x0, #0x1c80]
    //     0x140e0d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x140e0d8: cmp             w0, w16
    //     0x140e0dc: b.ne            #0x140e0e8
    //     0x140e0e0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x140e0e4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x140e0e8: r0 = GetNavigation.context()
    //     0x140e0e8: bl              #0x8a54d0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0x140e0ec: stur            x0, [fp, #-8]
    // 0x140e0f0: cmp             w0, NULL
    // 0x140e0f4: b.eq            #0x140e148
    // 0x140e0f8: ldur            x2, [fp, #-0x10]
    // 0x140e0fc: r1 = Function '<anonymous closure>':.
    //     0x140e0fc: add             x1, PP, #0x36, lsl #12  ; [pp+0x36be0] AnonymousClosure: (0x140e14c), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::_showRatingSuccessBottomSheet (0x140e08c)
    //     0x140e100: ldr             x1, [x1, #0xbe0]
    // 0x140e104: r0 = AllocateClosure()
    //     0x140e104: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140e108: stp             x0, NULL, [SP, #0x18]
    // 0x140e10c: ldur            x16, [fp, #-8]
    // 0x140e110: r30 = true
    //     0x140e110: add             lr, NULL, #0x20  ; true
    // 0x140e114: stp             lr, x16, [SP, #8]
    // 0x140e118: r16 = Instance_Color
    //     0x140e118: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x140e11c: ldr             x16, [x16, #0xf88]
    // 0x140e120: str             x16, [SP]
    // 0x140e124: r4 = const [0x1, 0x4, 0x4, 0x2, backgroundColor, 0x3, isScrollControlled, 0x2, null]
    //     0x140e124: add             x4, PP, #0x36, lsl #12  ; [pp+0x36be8] List(9) [0x1, 0x4, 0x4, 0x2, "backgroundColor", 0x3, "isScrollControlled", 0x2, Null]
    //     0x140e128: ldr             x4, [x4, #0xbe8]
    // 0x140e12c: r0 = showModalBottomSheet()
    //     0x140e12c: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x140e130: r0 = Null
    //     0x140e130: mov             x0, NULL
    // 0x140e134: LeaveFrame
    //     0x140e134: mov             SP, fp
    //     0x140e138: ldp             fp, lr, [SP], #0x10
    // 0x140e13c: ret
    //     0x140e13c: ret             
    // 0x140e140: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140e140: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140e144: b               #0x140e0a8
    // 0x140e148: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x140e148: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RatingReviewSuccessBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x140e14c, size: 0x110
    // 0x140e14c: EnterFrame
    //     0x140e14c: stp             fp, lr, [SP, #-0x10]!
    //     0x140e150: mov             fp, SP
    // 0x140e154: AllocStack(0x30)
    //     0x140e154: sub             SP, SP, #0x30
    // 0x140e158: SetupParameters()
    //     0x140e158: ldr             x0, [fp, #0x18]
    //     0x140e15c: ldur            w2, [x0, #0x17]
    //     0x140e160: add             x2, x2, HEAP, lsl #32
    //     0x140e164: stur            x2, [fp, #-8]
    // 0x140e168: CheckStackOverflow
    //     0x140e168: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140e16c: cmp             SP, x16
    //     0x140e170: b.ls            #0x140e254
    // 0x140e174: LoadField: r1 = r2->field_f
    //     0x140e174: ldur            w1, [x2, #0xf]
    // 0x140e178: DecompressPointer r1
    //     0x140e178: add             x1, x1, HEAP, lsl #32
    // 0x140e17c: r0 = controller()
    //     0x140e17c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140e180: LoadField: r1 = r0->field_4b
    //     0x140e180: ldur            w1, [x0, #0x4b]
    // 0x140e184: DecompressPointer r1
    //     0x140e184: add             x1, x1, HEAP, lsl #32
    // 0x140e188: r0 = value()
    //     0x140e188: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140e18c: LoadField: r1 = r0->field_b
    //     0x140e18c: ldur            w1, [x0, #0xb]
    // 0x140e190: DecompressPointer r1
    //     0x140e190: add             x1, x1, HEAP, lsl #32
    // 0x140e194: cmp             w1, NULL
    // 0x140e198: b.ne            #0x140e1a4
    // 0x140e19c: r0 = Null
    //     0x140e19c: mov             x0, NULL
    // 0x140e1a0: b               #0x140e1dc
    // 0x140e1a4: LoadField: r0 = r1->field_1f
    //     0x140e1a4: ldur            w0, [x1, #0x1f]
    // 0x140e1a8: DecompressPointer r0
    //     0x140e1a8: add             x0, x0, HEAP, lsl #32
    // 0x140e1ac: cmp             w0, NULL
    // 0x140e1b0: b.ne            #0x140e1bc
    // 0x140e1b4: r0 = Null
    //     0x140e1b4: mov             x0, NULL
    // 0x140e1b8: b               #0x140e1dc
    // 0x140e1bc: LoadField: r1 = r0->field_13
    //     0x140e1bc: ldur            w1, [x0, #0x13]
    // 0x140e1c0: DecompressPointer r1
    //     0x140e1c0: add             x1, x1, HEAP, lsl #32
    // 0x140e1c4: cmp             w1, NULL
    // 0x140e1c8: b.ne            #0x140e1d4
    // 0x140e1cc: r0 = Null
    //     0x140e1cc: mov             x0, NULL
    // 0x140e1d0: b               #0x140e1dc
    // 0x140e1d4: stp             x1, NULL, [SP]
    // 0x140e1d8: r0 = _Double.fromInteger()
    //     0x140e1d8: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x140e1dc: cmp             w0, NULL
    // 0x140e1e0: b.ne            #0x140e1ec
    // 0x140e1e4: d0 = 0.000000
    //     0x140e1e4: eor             v0.16b, v0.16b, v0.16b
    // 0x140e1e8: b               #0x140e1f0
    // 0x140e1ec: LoadField: d0 = r0->field_7
    //     0x140e1ec: ldur            d0, [x0, #7]
    // 0x140e1f0: ldur            x2, [fp, #-8]
    // 0x140e1f4: stur            d0, [fp, #-0x20]
    // 0x140e1f8: LoadField: r1 = r2->field_f
    //     0x140e1f8: ldur            w1, [x2, #0xf]
    // 0x140e1fc: DecompressPointer r1
    //     0x140e1fc: add             x1, x1, HEAP, lsl #32
    // 0x140e200: r0 = controller()
    //     0x140e200: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140e204: LoadField: r1 = r0->field_4b
    //     0x140e204: ldur            w1, [x0, #0x4b]
    // 0x140e208: DecompressPointer r1
    //     0x140e208: add             x1, x1, HEAP, lsl #32
    // 0x140e20c: r0 = value()
    //     0x140e20c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140e210: stur            x0, [fp, #-0x10]
    // 0x140e214: r0 = RatingReviewSuccessBottomSheet()
    //     0x140e214: bl              #0x140e25c  ; AllocateRatingReviewSuccessBottomSheetStub -> RatingReviewSuccessBottomSheet (size=0x1c)
    // 0x140e218: ldur            d0, [fp, #-0x20]
    // 0x140e21c: stur            x0, [fp, #-0x18]
    // 0x140e220: StoreField: r0->field_b = d0
    //     0x140e220: stur            d0, [x0, #0xb]
    // 0x140e224: ldur            x1, [fp, #-0x10]
    // 0x140e228: StoreField: r0->field_13 = r1
    //     0x140e228: stur            w1, [x0, #0x13]
    // 0x140e22c: ldur            x2, [fp, #-8]
    // 0x140e230: r1 = Function '<anonymous closure>':.
    //     0x140e230: add             x1, PP, #0x36, lsl #12  ; [pp+0x36bf0] AnonymousClosure: (0x140e268), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::_showRatingSuccessBottomSheet (0x140e08c)
    //     0x140e234: ldr             x1, [x1, #0xbf0]
    // 0x140e238: r0 = AllocateClosure()
    //     0x140e238: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140e23c: mov             x1, x0
    // 0x140e240: ldur            x0, [fp, #-0x18]
    // 0x140e244: ArrayStore: r0[0] = r1  ; List_4
    //     0x140e244: stur            w1, [x0, #0x17]
    // 0x140e248: LeaveFrame
    //     0x140e248: mov             SP, fp
    //     0x140e24c: ldp             fp, lr, [SP], #0x10
    // 0x140e250: ret
    //     0x140e250: ret             
    // 0x140e254: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140e254: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140e258: b               #0x140e174
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x140e268, size: 0x7c
    // 0x140e268: EnterFrame
    //     0x140e268: stp             fp, lr, [SP, #-0x10]!
    //     0x140e26c: mov             fp, SP
    // 0x140e270: AllocStack(0x8)
    //     0x140e270: sub             SP, SP, #8
    // 0x140e274: SetupParameters()
    //     0x140e274: ldr             x0, [fp, #0x10]
    //     0x140e278: ldur            w1, [x0, #0x17]
    //     0x140e27c: add             x1, x1, HEAP, lsl #32
    // 0x140e280: CheckStackOverflow
    //     0x140e280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140e284: cmp             SP, x16
    //     0x140e288: b.ls            #0x140e2dc
    // 0x140e28c: LoadField: r0 = r1->field_f
    //     0x140e28c: ldur            w0, [x1, #0xf]
    // 0x140e290: DecompressPointer r0
    //     0x140e290: add             x0, x0, HEAP, lsl #32
    // 0x140e294: mov             x1, x0
    // 0x140e298: r0 = controller()
    //     0x140e298: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140e29c: mov             x1, x0
    // 0x140e2a0: r0 = resetPageState()
    //     0x140e2a0: bl              #0x89d510  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::resetPageState
    // 0x140e2a4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x140e2a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x140e2a8: ldr             x0, [x0, #0x1c80]
    //     0x140e2ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x140e2b0: cmp             w0, w16
    //     0x140e2b4: b.ne            #0x140e2c0
    //     0x140e2b8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x140e2bc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x140e2c0: str             NULL, [SP]
    // 0x140e2c4: r4 = const [0x1, 0, 0, 0, null]
    //     0x140e2c4: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x140e2c8: r0 = GetNavigation.back()
    //     0x140e2c8: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x140e2cc: r0 = Null
    //     0x140e2cc: mov             x0, NULL
    // 0x140e2d0: LeaveFrame
    //     0x140e2d0: mov             SP, fp
    //     0x140e2d4: ldp             fp, lr, [SP], #0x10
    // 0x140e2d8: ret
    //     0x140e2d8: ret             
    // 0x140e2dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140e2dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140e2e0: b               #0x140e28c
  }
  _ body(/* No info */) {
    // ** addr: 0x1506a98, size: 0x94
    // 0x1506a98: EnterFrame
    //     0x1506a98: stp             fp, lr, [SP, #-0x10]!
    //     0x1506a9c: mov             fp, SP
    // 0x1506aa0: AllocStack(0x18)
    //     0x1506aa0: sub             SP, SP, #0x18
    // 0x1506aa4: SetupParameters(RatingReviewOrderPage this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1506aa4: mov             x0, x1
    //     0x1506aa8: stur            x1, [fp, #-8]
    //     0x1506aac: stur            x2, [fp, #-0x10]
    // 0x1506ab0: r1 = 2
    //     0x1506ab0: movz            x1, #0x2
    // 0x1506ab4: r0 = AllocateContext()
    //     0x1506ab4: bl              #0x16f6108  ; AllocateContextStub
    // 0x1506ab8: ldur            x2, [fp, #-8]
    // 0x1506abc: stur            x0, [fp, #-0x18]
    // 0x1506ac0: StoreField: r0->field_f = r2
    //     0x1506ac0: stur            w2, [x0, #0xf]
    // 0x1506ac4: ldur            x1, [fp, #-0x10]
    // 0x1506ac8: StoreField: r0->field_13 = r1
    //     0x1506ac8: stur            w1, [x0, #0x13]
    // 0x1506acc: r0 = Obx()
    //     0x1506acc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1506ad0: ldur            x2, [fp, #-0x18]
    // 0x1506ad4: r1 = Function '<anonymous closure>':.
    //     0x1506ad4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a30] AnonymousClosure: (0x140c804), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x1506ad8: ldr             x1, [x1, #0xa30]
    // 0x1506adc: stur            x0, [fp, #-0x10]
    // 0x1506ae0: r0 = AllocateClosure()
    //     0x1506ae0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1506ae4: mov             x1, x0
    // 0x1506ae8: ldur            x0, [fp, #-0x10]
    // 0x1506aec: StoreField: r0->field_b = r1
    //     0x1506aec: stur            w1, [x0, #0xb]
    // 0x1506af0: r0 = WillPopScope()
    //     0x1506af0: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x1506af4: mov             x3, x0
    // 0x1506af8: ldur            x0, [fp, #-0x10]
    // 0x1506afc: stur            x3, [fp, #-0x18]
    // 0x1506b00: StoreField: r3->field_b = r0
    //     0x1506b00: stur            w0, [x3, #0xb]
    // 0x1506b04: ldur            x2, [fp, #-8]
    // 0x1506b08: r1 = Function '_onBackPress@1719437308':.
    //     0x1506b08: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a38] AnonymousClosure: (0x1506b2c), in [package:customer_app/app/presentation/views/basic/orders/rating_review_order_page.dart] RatingReviewOrderPage::_onBackPress (0x14071f8)
    //     0x1506b0c: ldr             x1, [x1, #0xa38]
    // 0x1506b10: r0 = AllocateClosure()
    //     0x1506b10: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1506b14: mov             x1, x0
    // 0x1506b18: ldur            x0, [fp, #-0x18]
    // 0x1506b1c: StoreField: r0->field_f = r1
    //     0x1506b1c: stur            w1, [x0, #0xf]
    // 0x1506b20: LeaveFrame
    //     0x1506b20: mov             SP, fp
    //     0x1506b24: ldp             fp, lr, [SP], #0x10
    // 0x1506b28: ret
    //     0x1506b28: ret             
  }
  [closure] Future<bool> _onBackPress(dynamic) {
    // ** addr: 0x1506b2c, size: 0x38
    // 0x1506b2c: EnterFrame
    //     0x1506b2c: stp             fp, lr, [SP, #-0x10]!
    //     0x1506b30: mov             fp, SP
    // 0x1506b34: ldr             x0, [fp, #0x10]
    // 0x1506b38: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1506b38: ldur            w1, [x0, #0x17]
    // 0x1506b3c: DecompressPointer r1
    //     0x1506b3c: add             x1, x1, HEAP, lsl #32
    // 0x1506b40: CheckStackOverflow
    //     0x1506b40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1506b44: cmp             SP, x16
    //     0x1506b48: b.ls            #0x1506b5c
    // 0x1506b4c: r0 = _onBackPress()
    //     0x1506b4c: bl              #0x14071f8  ; [package:customer_app/app/presentation/views/basic/orders/rating_review_order_page.dart] RatingReviewOrderPage::_onBackPress
    // 0x1506b50: LeaveFrame
    //     0x1506b50: mov             SP, fp
    //     0x1506b54: ldp             fp, lr, [SP], #0x10
    // 0x1506b58: ret
    //     0x1506b58: ret             
    // 0x1506b5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1506b5c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1506b60: b               #0x1506b4c
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15eb384, size: 0x1bc
    // 0x15eb384: EnterFrame
    //     0x15eb384: stp             fp, lr, [SP, #-0x10]!
    //     0x15eb388: mov             fp, SP
    // 0x15eb38c: AllocStack(0x30)
    //     0x15eb38c: sub             SP, SP, #0x30
    // 0x15eb390: SetupParameters(RatingReviewOrderPage this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15eb390: mov             x0, x1
    //     0x15eb394: stur            x1, [fp, #-8]
    //     0x15eb398: mov             x1, x2
    //     0x15eb39c: stur            x2, [fp, #-0x10]
    // 0x15eb3a0: CheckStackOverflow
    //     0x15eb3a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15eb3a4: cmp             SP, x16
    //     0x15eb3a8: b.ls            #0x15eb538
    // 0x15eb3ac: r1 = 1
    //     0x15eb3ac: movz            x1, #0x1
    // 0x15eb3b0: r0 = AllocateContext()
    //     0x15eb3b0: bl              #0x16f6108  ; AllocateContextStub
    // 0x15eb3b4: mov             x2, x0
    // 0x15eb3b8: ldur            x0, [fp, #-8]
    // 0x15eb3bc: stur            x2, [fp, #-0x18]
    // 0x15eb3c0: StoreField: r2->field_f = r0
    //     0x15eb3c0: stur            w0, [x2, #0xf]
    // 0x15eb3c4: ldur            x1, [fp, #-0x10]
    // 0x15eb3c8: r0 = of()
    //     0x15eb3c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15eb3cc: LoadField: r1 = r0->field_87
    //     0x15eb3cc: ldur            w1, [x0, #0x87]
    // 0x15eb3d0: DecompressPointer r1
    //     0x15eb3d0: add             x1, x1, HEAP, lsl #32
    // 0x15eb3d4: LoadField: r0 = r1->field_7
    //     0x15eb3d4: ldur            w0, [x1, #7]
    // 0x15eb3d8: DecompressPointer r0
    //     0x15eb3d8: add             x0, x0, HEAP, lsl #32
    // 0x15eb3dc: r16 = Instance_Color
    //     0x15eb3dc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15eb3e0: r30 = 16.000000
    //     0x15eb3e0: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15eb3e4: ldr             lr, [lr, #0x188]
    // 0x15eb3e8: stp             lr, x16, [SP]
    // 0x15eb3ec: mov             x1, x0
    // 0x15eb3f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x15eb3f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x15eb3f4: ldr             x4, [x4, #0x9b8]
    // 0x15eb3f8: r0 = copyWith()
    //     0x15eb3f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15eb3fc: stur            x0, [fp, #-8]
    // 0x15eb400: r0 = Text()
    //     0x15eb400: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15eb404: mov             x2, x0
    // 0x15eb408: r0 = "Review Product"
    //     0x15eb408: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c20] "Review Product"
    //     0x15eb40c: ldr             x0, [x0, #0xc20]
    // 0x15eb410: stur            x2, [fp, #-0x20]
    // 0x15eb414: StoreField: r2->field_b = r0
    //     0x15eb414: stur            w0, [x2, #0xb]
    // 0x15eb418: ldur            x0, [fp, #-8]
    // 0x15eb41c: StoreField: r2->field_13 = r0
    //     0x15eb41c: stur            w0, [x2, #0x13]
    // 0x15eb420: ldur            x1, [fp, #-0x10]
    // 0x15eb424: r0 = of()
    //     0x15eb424: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15eb428: LoadField: r1 = r0->field_5b
    //     0x15eb428: ldur            w1, [x0, #0x5b]
    // 0x15eb42c: DecompressPointer r1
    //     0x15eb42c: add             x1, x1, HEAP, lsl #32
    // 0x15eb430: stur            x1, [fp, #-8]
    // 0x15eb434: r0 = ColorFilter()
    //     0x15eb434: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15eb438: mov             x1, x0
    // 0x15eb43c: ldur            x0, [fp, #-8]
    // 0x15eb440: stur            x1, [fp, #-0x10]
    // 0x15eb444: StoreField: r1->field_7 = r0
    //     0x15eb444: stur            w0, [x1, #7]
    // 0x15eb448: r0 = Instance_BlendMode
    //     0x15eb448: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15eb44c: ldr             x0, [x0, #0xb30]
    // 0x15eb450: StoreField: r1->field_b = r0
    //     0x15eb450: stur            w0, [x1, #0xb]
    // 0x15eb454: r0 = 1
    //     0x15eb454: movz            x0, #0x1
    // 0x15eb458: StoreField: r1->field_13 = r0
    //     0x15eb458: stur            x0, [x1, #0x13]
    // 0x15eb45c: r0 = SvgPicture()
    //     0x15eb45c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15eb460: stur            x0, [fp, #-8]
    // 0x15eb464: ldur            x16, [fp, #-0x10]
    // 0x15eb468: str             x16, [SP]
    // 0x15eb46c: mov             x1, x0
    // 0x15eb470: r2 = "assets/images/appbar_arrow.svg"
    //     0x15eb470: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15eb474: ldr             x2, [x2, #0xa40]
    // 0x15eb478: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15eb478: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15eb47c: ldr             x4, [x4, #0xa38]
    // 0x15eb480: r0 = SvgPicture.asset()
    //     0x15eb480: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15eb484: r0 = Align()
    //     0x15eb484: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15eb488: mov             x1, x0
    // 0x15eb48c: r0 = Instance_Alignment
    //     0x15eb48c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15eb490: ldr             x0, [x0, #0xb10]
    // 0x15eb494: stur            x1, [fp, #-0x10]
    // 0x15eb498: StoreField: r1->field_f = r0
    //     0x15eb498: stur            w0, [x1, #0xf]
    // 0x15eb49c: r0 = 1.000000
    //     0x15eb49c: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15eb4a0: StoreField: r1->field_13 = r0
    //     0x15eb4a0: stur            w0, [x1, #0x13]
    // 0x15eb4a4: ArrayStore: r1[0] = r0  ; List_4
    //     0x15eb4a4: stur            w0, [x1, #0x17]
    // 0x15eb4a8: ldur            x0, [fp, #-8]
    // 0x15eb4ac: StoreField: r1->field_b = r0
    //     0x15eb4ac: stur            w0, [x1, #0xb]
    // 0x15eb4b0: r0 = InkWell()
    //     0x15eb4b0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15eb4b4: mov             x3, x0
    // 0x15eb4b8: ldur            x0, [fp, #-0x10]
    // 0x15eb4bc: stur            x3, [fp, #-8]
    // 0x15eb4c0: StoreField: r3->field_b = r0
    //     0x15eb4c0: stur            w0, [x3, #0xb]
    // 0x15eb4c4: ldur            x2, [fp, #-0x18]
    // 0x15eb4c8: r1 = Function '<anonymous closure>':.
    //     0x15eb4c8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36c28] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15eb4cc: ldr             x1, [x1, #0xc28]
    // 0x15eb4d0: r0 = AllocateClosure()
    //     0x15eb4d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15eb4d4: ldur            x2, [fp, #-8]
    // 0x15eb4d8: StoreField: r2->field_f = r0
    //     0x15eb4d8: stur            w0, [x2, #0xf]
    // 0x15eb4dc: r0 = true
    //     0x15eb4dc: add             x0, NULL, #0x20  ; true
    // 0x15eb4e0: StoreField: r2->field_43 = r0
    //     0x15eb4e0: stur            w0, [x2, #0x43]
    // 0x15eb4e4: r1 = Instance_BoxShape
    //     0x15eb4e4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15eb4e8: ldr             x1, [x1, #0x80]
    // 0x15eb4ec: StoreField: r2->field_47 = r1
    //     0x15eb4ec: stur            w1, [x2, #0x47]
    // 0x15eb4f0: StoreField: r2->field_6f = r0
    //     0x15eb4f0: stur            w0, [x2, #0x6f]
    // 0x15eb4f4: r1 = false
    //     0x15eb4f4: add             x1, NULL, #0x30  ; false
    // 0x15eb4f8: StoreField: r2->field_73 = r1
    //     0x15eb4f8: stur            w1, [x2, #0x73]
    // 0x15eb4fc: StoreField: r2->field_83 = r0
    //     0x15eb4fc: stur            w0, [x2, #0x83]
    // 0x15eb500: StoreField: r2->field_7b = r1
    //     0x15eb500: stur            w1, [x2, #0x7b]
    // 0x15eb504: r0 = AppBar()
    //     0x15eb504: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15eb508: stur            x0, [fp, #-0x10]
    // 0x15eb50c: ldur            x16, [fp, #-0x20]
    // 0x15eb510: str             x16, [SP]
    // 0x15eb514: mov             x1, x0
    // 0x15eb518: ldur            x2, [fp, #-8]
    // 0x15eb51c: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15eb51c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15eb520: ldr             x4, [x4, #0xf00]
    // 0x15eb524: r0 = AppBar()
    //     0x15eb524: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15eb528: ldur            x0, [fp, #-0x10]
    // 0x15eb52c: LeaveFrame
    //     0x15eb52c: mov             SP, fp
    //     0x15eb530: ldp             fp, lr, [SP], #0x10
    // 0x15eb534: ret
    //     0x15eb534: ret             
    // 0x15eb538: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15eb538: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15eb53c: b               #0x15eb3ac
  }
}
