// lib: , url: package:customer_app/app/presentation/views/cosmetic/image/enlarge_image_view.dart

// class id: 1049287, size: 0x8
class :: {
}

// class id: 4598, size: 0x14, field offset: 0x14
//   const constructor, 
class EnlargeImageView extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14a6a30, size: 0x58
    // 0x14a6a30: EnterFrame
    //     0x14a6a30: stp             fp, lr, [SP, #-0x10]!
    //     0x14a6a34: mov             fp, SP
    // 0x14a6a38: AllocStack(0x10)
    //     0x14a6a38: sub             SP, SP, #0x10
    // 0x14a6a3c: SetupParameters(EnlargeImageView this /* r1 => r1, fp-0x8 */)
    //     0x14a6a3c: stur            x1, [fp, #-8]
    // 0x14a6a40: r1 = 1
    //     0x14a6a40: movz            x1, #0x1
    // 0x14a6a44: r0 = AllocateContext()
    //     0x14a6a44: bl              #0x16f6108  ; AllocateContextStub
    // 0x14a6a48: mov             x1, x0
    // 0x14a6a4c: ldur            x0, [fp, #-8]
    // 0x14a6a50: stur            x1, [fp, #-0x10]
    // 0x14a6a54: StoreField: r1->field_f = r0
    //     0x14a6a54: stur            w0, [x1, #0xf]
    // 0x14a6a58: r0 = Obx()
    //     0x14a6a58: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14a6a5c: ldur            x2, [fp, #-0x10]
    // 0x14a6a60: r1 = Function '<anonymous closure>':.
    //     0x14a6a60: add             x1, PP, #0x43, lsl #12  ; [pp+0x432a0] AnonymousClosure: (0x14a6a88), in [package:customer_app/app/presentation/views/cosmetic/image/enlarge_image_view.dart] EnlargeImageView::body (0x14a6a30)
    //     0x14a6a64: ldr             x1, [x1, #0x2a0]
    // 0x14a6a68: stur            x0, [fp, #-8]
    // 0x14a6a6c: r0 = AllocateClosure()
    //     0x14a6a6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a6a70: mov             x1, x0
    // 0x14a6a74: ldur            x0, [fp, #-8]
    // 0x14a6a78: StoreField: r0->field_b = r1
    //     0x14a6a78: stur            w1, [x0, #0xb]
    // 0x14a6a7c: LeaveFrame
    //     0x14a6a7c: mov             SP, fp
    //     0x14a6a80: ldp             fp, lr, [SP], #0x10
    // 0x14a6a84: ret
    //     0x14a6a84: ret             
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0x14a6a88, size: 0x354
    // 0x14a6a88: EnterFrame
    //     0x14a6a88: stp             fp, lr, [SP, #-0x10]!
    //     0x14a6a8c: mov             fp, SP
    // 0x14a6a90: AllocStack(0x48)
    //     0x14a6a90: sub             SP, SP, #0x48
    // 0x14a6a94: SetupParameters()
    //     0x14a6a94: ldr             x0, [fp, #0x10]
    //     0x14a6a98: ldur            w3, [x0, #0x17]
    //     0x14a6a9c: add             x3, x3, HEAP, lsl #32
    //     0x14a6aa0: stur            x3, [fp, #-8]
    // 0x14a6aa4: CheckStackOverflow
    //     0x14a6aa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a6aa8: cmp             SP, x16
    //     0x14a6aac: b.ls            #0x14a6dd4
    // 0x14a6ab0: r16 = 0.500000
    //     0x14a6ab0: ldr             x16, [PP, #0x47c0]  ; [pp+0x47c0] 0.5
    // 0x14a6ab4: str             x16, [SP]
    // 0x14a6ab8: r1 = Null
    //     0x14a6ab8: mov             x1, NULL
    // 0x14a6abc: r2 = Instance_Color
    //     0x14a6abc: ldr             x2, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a6ac0: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x14a6ac0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x14a6ac4: ldr             x4, [x4, #0x108]
    // 0x14a6ac8: r0 = Border.all()
    //     0x14a6ac8: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14a6acc: stur            x0, [fp, #-0x10]
    // 0x14a6ad0: r0 = BoxDecoration()
    //     0x14a6ad0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14a6ad4: mov             x1, x0
    // 0x14a6ad8: r0 = Instance_Color
    //     0x14a6ad8: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14a6adc: stur            x1, [fp, #-0x18]
    // 0x14a6ae0: StoreField: r1->field_7 = r0
    //     0x14a6ae0: stur            w0, [x1, #7]
    // 0x14a6ae4: ldur            x0, [fp, #-0x10]
    // 0x14a6ae8: StoreField: r1->field_f = r0
    //     0x14a6ae8: stur            w0, [x1, #0xf]
    // 0x14a6aec: r0 = Instance_BoxShape
    //     0x14a6aec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x14a6af0: ldr             x0, [x0, #0x970]
    // 0x14a6af4: StoreField: r1->field_23 = r0
    //     0x14a6af4: stur            w0, [x1, #0x23]
    // 0x14a6af8: r0 = Container()
    //     0x14a6af8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14a6afc: stur            x0, [fp, #-0x10]
    // 0x14a6b00: r16 = 35.000000
    //     0x14a6b00: add             x16, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0x14a6b04: ldr             x16, [x16, #0x2b0]
    // 0x14a6b08: r30 = 35.000000
    //     0x14a6b08: add             lr, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0x14a6b0c: ldr             lr, [lr, #0x2b0]
    // 0x14a6b10: stp             lr, x16, [SP, #0x10]
    // 0x14a6b14: ldur            x16, [fp, #-0x18]
    // 0x14a6b18: r30 = Instance_Icon
    //     0x14a6b18: add             lr, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0x14a6b1c: ldr             lr, [lr, #0x2b8]
    // 0x14a6b20: stp             lr, x16, [SP]
    // 0x14a6b24: mov             x1, x0
    // 0x14a6b28: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x14a6b28: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x14a6b2c: ldr             x4, [x4, #0x870]
    // 0x14a6b30: r0 = Container()
    //     0x14a6b30: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14a6b34: r0 = InkWell()
    //     0x14a6b34: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14a6b38: mov             x3, x0
    // 0x14a6b3c: ldur            x0, [fp, #-0x10]
    // 0x14a6b40: stur            x3, [fp, #-0x18]
    // 0x14a6b44: StoreField: r3->field_b = r0
    //     0x14a6b44: stur            w0, [x3, #0xb]
    // 0x14a6b48: r1 = Function '<anonymous closure>':.
    //     0x14a6b48: add             x1, PP, #0x43, lsl #12  ; [pp+0x432a8] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14a6b4c: ldr             x1, [x1, #0x2a8]
    // 0x14a6b50: r2 = Null
    //     0x14a6b50: mov             x2, NULL
    // 0x14a6b54: r0 = AllocateClosure()
    //     0x14a6b54: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a6b58: mov             x1, x0
    // 0x14a6b5c: ldur            x0, [fp, #-0x18]
    // 0x14a6b60: StoreField: r0->field_f = r1
    //     0x14a6b60: stur            w1, [x0, #0xf]
    // 0x14a6b64: r1 = true
    //     0x14a6b64: add             x1, NULL, #0x20  ; true
    // 0x14a6b68: StoreField: r0->field_43 = r1
    //     0x14a6b68: stur            w1, [x0, #0x43]
    // 0x14a6b6c: r2 = Instance_BoxShape
    //     0x14a6b6c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14a6b70: ldr             x2, [x2, #0x80]
    // 0x14a6b74: StoreField: r0->field_47 = r2
    //     0x14a6b74: stur            w2, [x0, #0x47]
    // 0x14a6b78: StoreField: r0->field_6f = r1
    //     0x14a6b78: stur            w1, [x0, #0x6f]
    // 0x14a6b7c: r2 = false
    //     0x14a6b7c: add             x2, NULL, #0x30  ; false
    // 0x14a6b80: StoreField: r0->field_73 = r2
    //     0x14a6b80: stur            w2, [x0, #0x73]
    // 0x14a6b84: StoreField: r0->field_83 = r1
    //     0x14a6b84: stur            w1, [x0, #0x83]
    // 0x14a6b88: StoreField: r0->field_7b = r2
    //     0x14a6b88: stur            w2, [x0, #0x7b]
    // 0x14a6b8c: r0 = Align()
    //     0x14a6b8c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14a6b90: mov             x1, x0
    // 0x14a6b94: r0 = Instance_Alignment
    //     0x14a6b94: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0x14a6b98: ldr             x0, [x0, #0x950]
    // 0x14a6b9c: stur            x1, [fp, #-0x10]
    // 0x14a6ba0: StoreField: r1->field_f = r0
    //     0x14a6ba0: stur            w0, [x1, #0xf]
    // 0x14a6ba4: ldur            x0, [fp, #-0x18]
    // 0x14a6ba8: StoreField: r1->field_b = r0
    //     0x14a6ba8: stur            w0, [x1, #0xb]
    // 0x14a6bac: r0 = Padding()
    //     0x14a6bac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a6bb0: mov             x2, x0
    // 0x14a6bb4: r0 = Instance_EdgeInsets
    //     0x14a6bb4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x14a6bb8: ldr             x0, [x0, #0x980]
    // 0x14a6bbc: stur            x2, [fp, #-0x18]
    // 0x14a6bc0: StoreField: r2->field_f = r0
    //     0x14a6bc0: stur            w0, [x2, #0xf]
    // 0x14a6bc4: ldur            x0, [fp, #-0x10]
    // 0x14a6bc8: StoreField: r2->field_b = r0
    //     0x14a6bc8: stur            w0, [x2, #0xb]
    // 0x14a6bcc: ldur            x0, [fp, #-8]
    // 0x14a6bd0: LoadField: r1 = r0->field_f
    //     0x14a6bd0: ldur            w1, [x0, #0xf]
    // 0x14a6bd4: DecompressPointer r1
    //     0x14a6bd4: add             x1, x1, HEAP, lsl #32
    // 0x14a6bd8: r0 = controller()
    //     0x14a6bd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a6bdc: LoadField: r1 = r0->field_4b
    //     0x14a6bdc: ldur            w1, [x0, #0x4b]
    // 0x14a6be0: DecompressPointer r1
    //     0x14a6be0: add             x1, x1, HEAP, lsl #32
    // 0x14a6be4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14a6be4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14a6be8: r0 = toList()
    //     0x14a6be8: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14a6bec: LoadField: r3 = r0->field_b
    //     0x14a6bec: ldur            w3, [x0, #0xb]
    // 0x14a6bf0: ldur            x2, [fp, #-8]
    // 0x14a6bf4: stur            x3, [fp, #-0x10]
    // 0x14a6bf8: r1 = Function '<anonymous closure>':.
    //     0x14a6bf8: add             x1, PP, #0x43, lsl #12  ; [pp+0x432b0] AnonymousClosure: (0x14a6f88), in [package:customer_app/app/presentation/views/cosmetic/image/enlarge_image_view.dart] EnlargeImageView::body (0x14a6a30)
    //     0x14a6bfc: ldr             x1, [x1, #0x2b0]
    // 0x14a6c00: r0 = AllocateClosure()
    //     0x14a6c00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a6c04: stur            x0, [fp, #-0x20]
    // 0x14a6c08: r0 = ListView()
    //     0x14a6c08: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14a6c0c: stur            x0, [fp, #-0x28]
    // 0x14a6c10: r16 = true
    //     0x14a6c10: add             x16, NULL, #0x20  ; true
    // 0x14a6c14: r30 = Instance_Axis
    //     0x14a6c14: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14a6c18: stp             lr, x16, [SP]
    // 0x14a6c1c: mov             x1, x0
    // 0x14a6c20: ldur            x2, [fp, #-0x20]
    // 0x14a6c24: ldur            x3, [fp, #-0x10]
    // 0x14a6c28: r4 = const [0, 0x5, 0x2, 0x3, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0x14a6c28: add             x4, PP, #0x37, lsl #12  ; [pp+0x372d0] List(9) [0, 0x5, 0x2, 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0x14a6c2c: ldr             x4, [x4, #0x2d0]
    // 0x14a6c30: r0 = ListView.builder()
    //     0x14a6c30: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x14a6c34: r0 = Center()
    //     0x14a6c34: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14a6c38: mov             x2, x0
    // 0x14a6c3c: r0 = Instance_Alignment
    //     0x14a6c3c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14a6c40: ldr             x0, [x0, #0xb10]
    // 0x14a6c44: stur            x2, [fp, #-0x10]
    // 0x14a6c48: StoreField: r2->field_f = r0
    //     0x14a6c48: stur            w0, [x2, #0xf]
    // 0x14a6c4c: ldur            x0, [fp, #-0x28]
    // 0x14a6c50: StoreField: r2->field_b = r0
    //     0x14a6c50: stur            w0, [x2, #0xb]
    // 0x14a6c54: r1 = <FlexParentData>
    //     0x14a6c54: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14a6c58: ldr             x1, [x1, #0xe00]
    // 0x14a6c5c: r0 = Expanded()
    //     0x14a6c5c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14a6c60: mov             x2, x0
    // 0x14a6c64: r0 = 5
    //     0x14a6c64: movz            x0, #0x5
    // 0x14a6c68: stur            x2, [fp, #-0x20]
    // 0x14a6c6c: StoreField: r2->field_13 = r0
    //     0x14a6c6c: stur            x0, [x2, #0x13]
    // 0x14a6c70: r0 = Instance_FlexFit
    //     0x14a6c70: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14a6c74: ldr             x0, [x0, #0xe08]
    // 0x14a6c78: StoreField: r2->field_1b = r0
    //     0x14a6c78: stur            w0, [x2, #0x1b]
    // 0x14a6c7c: ldur            x0, [fp, #-0x10]
    // 0x14a6c80: StoreField: r2->field_b = r0
    //     0x14a6c80: stur            w0, [x2, #0xb]
    // 0x14a6c84: ldur            x0, [fp, #-8]
    // 0x14a6c88: LoadField: r1 = r0->field_f
    //     0x14a6c88: ldur            w1, [x0, #0xf]
    // 0x14a6c8c: DecompressPointer r1
    //     0x14a6c8c: add             x1, x1, HEAP, lsl #32
    // 0x14a6c90: r0 = controller()
    //     0x14a6c90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a6c94: LoadField: r1 = r0->field_4b
    //     0x14a6c94: ldur            w1, [x0, #0x4b]
    // 0x14a6c98: DecompressPointer r1
    //     0x14a6c98: add             x1, x1, HEAP, lsl #32
    // 0x14a6c9c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14a6c9c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14a6ca0: r0 = toList()
    //     0x14a6ca0: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14a6ca4: LoadField: r3 = r0->field_b
    //     0x14a6ca4: ldur            w3, [x0, #0xb]
    // 0x14a6ca8: ldur            x2, [fp, #-8]
    // 0x14a6cac: stur            x3, [fp, #-0x10]
    // 0x14a6cb0: r1 = Function '<anonymous closure>':.
    //     0x14a6cb0: add             x1, PP, #0x43, lsl #12  ; [pp+0x432b8] AnonymousClosure: (0x14a6ddc), in [package:customer_app/app/presentation/views/cosmetic/image/enlarge_image_view.dart] EnlargeImageView::body (0x14a6a30)
    //     0x14a6cb4: ldr             x1, [x1, #0x2b8]
    // 0x14a6cb8: r0 = AllocateClosure()
    //     0x14a6cb8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a6cbc: stur            x0, [fp, #-8]
    // 0x14a6cc0: r0 = ListView()
    //     0x14a6cc0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14a6cc4: stur            x0, [fp, #-0x28]
    // 0x14a6cc8: r16 = true
    //     0x14a6cc8: add             x16, NULL, #0x20  ; true
    // 0x14a6ccc: r30 = Instance_Axis
    //     0x14a6ccc: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14a6cd0: stp             lr, x16, [SP]
    // 0x14a6cd4: mov             x1, x0
    // 0x14a6cd8: ldur            x2, [fp, #-8]
    // 0x14a6cdc: ldur            x3, [fp, #-0x10]
    // 0x14a6ce0: r4 = const [0, 0x5, 0x2, 0x3, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0x14a6ce0: add             x4, PP, #0x37, lsl #12  ; [pp+0x372d0] List(9) [0, 0x5, 0x2, 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0x14a6ce4: ldr             x4, [x4, #0x2d0]
    // 0x14a6ce8: r0 = ListView.builder()
    //     0x14a6ce8: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x14a6cec: r1 = <FlexParentData>
    //     0x14a6cec: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14a6cf0: ldr             x1, [x1, #0xe00]
    // 0x14a6cf4: r0 = Flexible()
    //     0x14a6cf4: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x14a6cf8: mov             x3, x0
    // 0x14a6cfc: r0 = 1
    //     0x14a6cfc: movz            x0, #0x1
    // 0x14a6d00: stur            x3, [fp, #-8]
    // 0x14a6d04: StoreField: r3->field_13 = r0
    //     0x14a6d04: stur            x0, [x3, #0x13]
    // 0x14a6d08: r0 = Instance_FlexFit
    //     0x14a6d08: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x14a6d0c: ldr             x0, [x0, #0xe20]
    // 0x14a6d10: StoreField: r3->field_1b = r0
    //     0x14a6d10: stur            w0, [x3, #0x1b]
    // 0x14a6d14: ldur            x0, [fp, #-0x28]
    // 0x14a6d18: StoreField: r3->field_b = r0
    //     0x14a6d18: stur            w0, [x3, #0xb]
    // 0x14a6d1c: r1 = Null
    //     0x14a6d1c: mov             x1, NULL
    // 0x14a6d20: r2 = 8
    //     0x14a6d20: movz            x2, #0x8
    // 0x14a6d24: r0 = AllocateArray()
    //     0x14a6d24: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a6d28: mov             x2, x0
    // 0x14a6d2c: ldur            x0, [fp, #-0x18]
    // 0x14a6d30: stur            x2, [fp, #-0x10]
    // 0x14a6d34: StoreField: r2->field_f = r0
    //     0x14a6d34: stur            w0, [x2, #0xf]
    // 0x14a6d38: ldur            x0, [fp, #-0x20]
    // 0x14a6d3c: StoreField: r2->field_13 = r0
    //     0x14a6d3c: stur            w0, [x2, #0x13]
    // 0x14a6d40: r16 = Instance_Divider
    //     0x14a6d40: add             x16, PP, #0x37, lsl #12  ; [pp+0x372e0] Obj!Divider@d66be1
    //     0x14a6d44: ldr             x16, [x16, #0x2e0]
    // 0x14a6d48: ArrayStore: r2[0] = r16  ; List_4
    //     0x14a6d48: stur            w16, [x2, #0x17]
    // 0x14a6d4c: ldur            x0, [fp, #-8]
    // 0x14a6d50: StoreField: r2->field_1b = r0
    //     0x14a6d50: stur            w0, [x2, #0x1b]
    // 0x14a6d54: r1 = <Widget>
    //     0x14a6d54: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14a6d58: r0 = AllocateGrowableArray()
    //     0x14a6d58: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14a6d5c: mov             x1, x0
    // 0x14a6d60: ldur            x0, [fp, #-0x10]
    // 0x14a6d64: stur            x1, [fp, #-8]
    // 0x14a6d68: StoreField: r1->field_f = r0
    //     0x14a6d68: stur            w0, [x1, #0xf]
    // 0x14a6d6c: r0 = 8
    //     0x14a6d6c: movz            x0, #0x8
    // 0x14a6d70: StoreField: r1->field_b = r0
    //     0x14a6d70: stur            w0, [x1, #0xb]
    // 0x14a6d74: r0 = Column()
    //     0x14a6d74: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14a6d78: r1 = Instance_Axis
    //     0x14a6d78: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14a6d7c: StoreField: r0->field_f = r1
    //     0x14a6d7c: stur            w1, [x0, #0xf]
    // 0x14a6d80: r1 = Instance_MainAxisAlignment
    //     0x14a6d80: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14a6d84: ldr             x1, [x1, #0xa08]
    // 0x14a6d88: StoreField: r0->field_13 = r1
    //     0x14a6d88: stur            w1, [x0, #0x13]
    // 0x14a6d8c: r1 = Instance_MainAxisSize
    //     0x14a6d8c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14a6d90: ldr             x1, [x1, #0xa10]
    // 0x14a6d94: ArrayStore: r0[0] = r1  ; List_4
    //     0x14a6d94: stur            w1, [x0, #0x17]
    // 0x14a6d98: r1 = Instance_CrossAxisAlignment
    //     0x14a6d98: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14a6d9c: ldr             x1, [x1, #0x890]
    // 0x14a6da0: StoreField: r0->field_1b = r1
    //     0x14a6da0: stur            w1, [x0, #0x1b]
    // 0x14a6da4: r1 = Instance_VerticalDirection
    //     0x14a6da4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14a6da8: ldr             x1, [x1, #0xa20]
    // 0x14a6dac: StoreField: r0->field_23 = r1
    //     0x14a6dac: stur            w1, [x0, #0x23]
    // 0x14a6db0: r1 = Instance_Clip
    //     0x14a6db0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14a6db4: ldr             x1, [x1, #0x38]
    // 0x14a6db8: StoreField: r0->field_2b = r1
    //     0x14a6db8: stur            w1, [x0, #0x2b]
    // 0x14a6dbc: StoreField: r0->field_2f = rZR
    //     0x14a6dbc: stur            xzr, [x0, #0x2f]
    // 0x14a6dc0: ldur            x1, [fp, #-8]
    // 0x14a6dc4: StoreField: r0->field_b = r1
    //     0x14a6dc4: stur            w1, [x0, #0xb]
    // 0x14a6dc8: LeaveFrame
    //     0x14a6dc8: mov             SP, fp
    //     0x14a6dcc: ldp             fp, lr, [SP], #0x10
    // 0x14a6dd0: ret
    //     0x14a6dd0: ret             
    // 0x14a6dd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14a6dd4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14a6dd8: b               #0x14a6ab0
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14a6ddc, size: 0x1ac
    // 0x14a6ddc: EnterFrame
    //     0x14a6ddc: stp             fp, lr, [SP, #-0x10]!
    //     0x14a6de0: mov             fp, SP
    // 0x14a6de4: AllocStack(0x40)
    //     0x14a6de4: sub             SP, SP, #0x40
    // 0x14a6de8: SetupParameters()
    //     0x14a6de8: ldr             x0, [fp, #0x20]
    //     0x14a6dec: ldur            w1, [x0, #0x17]
    //     0x14a6df0: add             x1, x1, HEAP, lsl #32
    // 0x14a6df4: CheckStackOverflow
    //     0x14a6df4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a6df8: cmp             SP, x16
    //     0x14a6dfc: b.ls            #0x14a6f78
    // 0x14a6e00: LoadField: r0 = r1->field_f
    //     0x14a6e00: ldur            w0, [x1, #0xf]
    // 0x14a6e04: DecompressPointer r0
    //     0x14a6e04: add             x0, x0, HEAP, lsl #32
    // 0x14a6e08: mov             x1, x0
    // 0x14a6e0c: r0 = controller()
    //     0x14a6e0c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a6e10: LoadField: r1 = r0->field_4b
    //     0x14a6e10: ldur            w1, [x0, #0x4b]
    // 0x14a6e14: DecompressPointer r1
    //     0x14a6e14: add             x1, x1, HEAP, lsl #32
    // 0x14a6e18: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14a6e18: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14a6e1c: r0 = toList()
    //     0x14a6e1c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14a6e20: mov             x2, x0
    // 0x14a6e24: LoadField: r0 = r2->field_b
    //     0x14a6e24: ldur            w0, [x2, #0xb]
    // 0x14a6e28: ldr             x1, [fp, #0x10]
    // 0x14a6e2c: r3 = LoadInt32Instr(r1)
    //     0x14a6e2c: sbfx            x3, x1, #1, #0x1f
    //     0x14a6e30: tbz             w1, #0, #0x14a6e38
    //     0x14a6e34: ldur            x3, [x1, #7]
    // 0x14a6e38: r1 = LoadInt32Instr(r0)
    //     0x14a6e38: sbfx            x1, x0, #1, #0x1f
    // 0x14a6e3c: mov             x0, x1
    // 0x14a6e40: mov             x1, x3
    // 0x14a6e44: cmp             x1, x0
    // 0x14a6e48: b.hs            #0x14a6f80
    // 0x14a6e4c: LoadField: r0 = r2->field_f
    //     0x14a6e4c: ldur            w0, [x2, #0xf]
    // 0x14a6e50: DecompressPointer r0
    //     0x14a6e50: add             x0, x0, HEAP, lsl #32
    // 0x14a6e54: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14a6e54: add             x16, x0, x3, lsl #2
    //     0x14a6e58: ldur            w1, [x16, #0xf]
    // 0x14a6e5c: DecompressPointer r1
    //     0x14a6e5c: add             x1, x1, HEAP, lsl #32
    // 0x14a6e60: cmp             w1, NULL
    // 0x14a6e64: b.eq            #0x14a6f84
    // 0x14a6e68: LoadField: r0 = r1->field_2b
    //     0x14a6e68: ldur            w0, [x1, #0x2b]
    // 0x14a6e6c: DecompressPointer r0
    //     0x14a6e6c: add             x0, x0, HEAP, lsl #32
    // 0x14a6e70: cmp             w0, NULL
    // 0x14a6e74: b.ne            #0x14a6e80
    // 0x14a6e78: r0 = Null
    //     0x14a6e78: mov             x0, NULL
    // 0x14a6e7c: b               #0x14a6e8c
    // 0x14a6e80: LoadField: r1 = r0->field_b
    //     0x14a6e80: ldur            w1, [x0, #0xb]
    // 0x14a6e84: DecompressPointer r1
    //     0x14a6e84: add             x1, x1, HEAP, lsl #32
    // 0x14a6e88: mov             x0, x1
    // 0x14a6e8c: cmp             w0, NULL
    // 0x14a6e90: b.ne            #0x14a6e98
    // 0x14a6e94: r0 = ""
    //     0x14a6e94: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14a6e98: stur            x0, [fp, #-8]
    // 0x14a6e9c: r1 = Function '<anonymous closure>':.
    //     0x14a6e9c: add             x1, PP, #0x43, lsl #12  ; [pp+0x432c0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14a6ea0: ldr             x1, [x1, #0x2c0]
    // 0x14a6ea4: r2 = Null
    //     0x14a6ea4: mov             x2, NULL
    // 0x14a6ea8: r0 = AllocateClosure()
    //     0x14a6ea8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a6eac: r1 = Function '<anonymous closure>':.
    //     0x14a6eac: add             x1, PP, #0x43, lsl #12  ; [pp+0x432c8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14a6eb0: ldr             x1, [x1, #0x2c8]
    // 0x14a6eb4: r2 = Null
    //     0x14a6eb4: mov             x2, NULL
    // 0x14a6eb8: stur            x0, [fp, #-0x10]
    // 0x14a6ebc: r0 = AllocateClosure()
    //     0x14a6ebc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a6ec0: stur            x0, [fp, #-0x18]
    // 0x14a6ec4: r0 = CachedNetworkImage()
    //     0x14a6ec4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14a6ec8: stur            x0, [fp, #-0x20]
    // 0x14a6ecc: r16 = 100.000000
    //     0x14a6ecc: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0x14a6ed0: r30 = 80.000000
    //     0x14a6ed0: add             lr, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0x14a6ed4: ldr             lr, [lr, #0x2f8]
    // 0x14a6ed8: stp             lr, x16, [SP, #0x10]
    // 0x14a6edc: ldur            x16, [fp, #-0x10]
    // 0x14a6ee0: ldur            lr, [fp, #-0x18]
    // 0x14a6ee4: stp             lr, x16, [SP]
    // 0x14a6ee8: mov             x1, x0
    // 0x14a6eec: ldur            x2, [fp, #-8]
    // 0x14a6ef0: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0x14a6ef0: add             x4, PP, #0x40, lsl #12  ; [pp+0x40388] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0x14a6ef4: ldr             x4, [x4, #0x388]
    // 0x14a6ef8: r0 = CachedNetworkImage()
    //     0x14a6ef8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14a6efc: r0 = InkWell()
    //     0x14a6efc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14a6f00: mov             x3, x0
    // 0x14a6f04: ldur            x0, [fp, #-0x20]
    // 0x14a6f08: stur            x3, [fp, #-8]
    // 0x14a6f0c: StoreField: r3->field_b = r0
    //     0x14a6f0c: stur            w0, [x3, #0xb]
    // 0x14a6f10: r1 = Function '<anonymous closure>':.
    //     0x14a6f10: add             x1, PP, #0x43, lsl #12  ; [pp+0x432d0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x14a6f14: ldr             x1, [x1, #0x2d0]
    // 0x14a6f18: r2 = Null
    //     0x14a6f18: mov             x2, NULL
    // 0x14a6f1c: r0 = AllocateClosure()
    //     0x14a6f1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a6f20: mov             x1, x0
    // 0x14a6f24: ldur            x0, [fp, #-8]
    // 0x14a6f28: StoreField: r0->field_f = r1
    //     0x14a6f28: stur            w1, [x0, #0xf]
    // 0x14a6f2c: r1 = true
    //     0x14a6f2c: add             x1, NULL, #0x20  ; true
    // 0x14a6f30: StoreField: r0->field_43 = r1
    //     0x14a6f30: stur            w1, [x0, #0x43]
    // 0x14a6f34: r2 = Instance_BoxShape
    //     0x14a6f34: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14a6f38: ldr             x2, [x2, #0x80]
    // 0x14a6f3c: StoreField: r0->field_47 = r2
    //     0x14a6f3c: stur            w2, [x0, #0x47]
    // 0x14a6f40: StoreField: r0->field_6f = r1
    //     0x14a6f40: stur            w1, [x0, #0x6f]
    // 0x14a6f44: r2 = false
    //     0x14a6f44: add             x2, NULL, #0x30  ; false
    // 0x14a6f48: StoreField: r0->field_73 = r2
    //     0x14a6f48: stur            w2, [x0, #0x73]
    // 0x14a6f4c: StoreField: r0->field_83 = r1
    //     0x14a6f4c: stur            w1, [x0, #0x83]
    // 0x14a6f50: StoreField: r0->field_7b = r2
    //     0x14a6f50: stur            w2, [x0, #0x7b]
    // 0x14a6f54: r0 = Padding()
    //     0x14a6f54: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a6f58: r1 = Instance_EdgeInsets
    //     0x14a6f58: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x14a6f5c: ldr             x1, [x1, #0x980]
    // 0x14a6f60: StoreField: r0->field_f = r1
    //     0x14a6f60: stur            w1, [x0, #0xf]
    // 0x14a6f64: ldur            x1, [fp, #-8]
    // 0x14a6f68: StoreField: r0->field_b = r1
    //     0x14a6f68: stur            w1, [x0, #0xb]
    // 0x14a6f6c: LeaveFrame
    //     0x14a6f6c: mov             SP, fp
    //     0x14a6f70: ldp             fp, lr, [SP], #0x10
    // 0x14a6f74: ret
    //     0x14a6f74: ret             
    // 0x14a6f78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14a6f78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14a6f7c: b               #0x14a6e00
    // 0x14a6f80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14a6f80: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14a6f84: r0 = NullErrorSharedWithoutFPURegs()
    //     0x14a6f84: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] InteractiveViewer <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14a6f88, size: 0x238
    // 0x14a6f88: EnterFrame
    //     0x14a6f88: stp             fp, lr, [SP, #-0x10]!
    //     0x14a6f8c: mov             fp, SP
    // 0x14a6f90: AllocStack(0x60)
    //     0x14a6f90: sub             SP, SP, #0x60
    // 0x14a6f94: SetupParameters()
    //     0x14a6f94: ldr             x0, [fp, #0x20]
    //     0x14a6f98: ldur            w1, [x0, #0x17]
    //     0x14a6f9c: add             x1, x1, HEAP, lsl #32
    //     0x14a6fa0: stur            x1, [fp, #-8]
    // 0x14a6fa4: CheckStackOverflow
    //     0x14a6fa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a6fa8: cmp             SP, x16
    //     0x14a6fac: b.ls            #0x14a7178
    // 0x14a6fb0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14a6fb0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14a6fb4: ldr             x0, [x0, #0x1c80]
    //     0x14a6fb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14a6fbc: cmp             w0, w16
    //     0x14a6fc0: b.ne            #0x14a6fcc
    //     0x14a6fc4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14a6fc8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14a6fcc: r0 = GetNavigation.size()
    //     0x14a6fcc: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x14a6fd0: LoadField: d0 = r0->field_f
    //     0x14a6fd0: ldur            d0, [x0, #0xf]
    // 0x14a6fd4: stur            d0, [fp, #-0x38]
    // 0x14a6fd8: r0 = GetNavigation.size()
    //     0x14a6fd8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x14a6fdc: LoadField: d0 = r0->field_7
    //     0x14a6fdc: ldur            d0, [x0, #7]
    // 0x14a6fe0: ldur            x0, [fp, #-8]
    // 0x14a6fe4: stur            d0, [fp, #-0x40]
    // 0x14a6fe8: LoadField: r1 = r0->field_f
    //     0x14a6fe8: ldur            w1, [x0, #0xf]
    // 0x14a6fec: DecompressPointer r1
    //     0x14a6fec: add             x1, x1, HEAP, lsl #32
    // 0x14a6ff0: r0 = controller()
    //     0x14a6ff0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a6ff4: LoadField: r1 = r0->field_4b
    //     0x14a6ff4: ldur            w1, [x0, #0x4b]
    // 0x14a6ff8: DecompressPointer r1
    //     0x14a6ff8: add             x1, x1, HEAP, lsl #32
    // 0x14a6ffc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14a6ffc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14a7000: r0 = toList()
    //     0x14a7000: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14a7004: mov             x2, x0
    // 0x14a7008: LoadField: r0 = r2->field_b
    //     0x14a7008: ldur            w0, [x2, #0xb]
    // 0x14a700c: ldr             x1, [fp, #0x10]
    // 0x14a7010: r3 = LoadInt32Instr(r1)
    //     0x14a7010: sbfx            x3, x1, #1, #0x1f
    //     0x14a7014: tbz             w1, #0, #0x14a701c
    //     0x14a7018: ldur            x3, [x1, #7]
    // 0x14a701c: r1 = LoadInt32Instr(r0)
    //     0x14a701c: sbfx            x1, x0, #1, #0x1f
    // 0x14a7020: mov             x0, x1
    // 0x14a7024: mov             x1, x3
    // 0x14a7028: cmp             x1, x0
    // 0x14a702c: b.hs            #0x14a7180
    // 0x14a7030: LoadField: r0 = r2->field_f
    //     0x14a7030: ldur            w0, [x2, #0xf]
    // 0x14a7034: DecompressPointer r0
    //     0x14a7034: add             x0, x0, HEAP, lsl #32
    // 0x14a7038: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14a7038: add             x16, x0, x3, lsl #2
    //     0x14a703c: ldur            w1, [x16, #0xf]
    // 0x14a7040: DecompressPointer r1
    //     0x14a7040: add             x1, x1, HEAP, lsl #32
    // 0x14a7044: cmp             w1, NULL
    // 0x14a7048: b.eq            #0x14a7184
    // 0x14a704c: LoadField: r0 = r1->field_2b
    //     0x14a704c: ldur            w0, [x1, #0x2b]
    // 0x14a7050: DecompressPointer r0
    //     0x14a7050: add             x0, x0, HEAP, lsl #32
    // 0x14a7054: cmp             w0, NULL
    // 0x14a7058: b.ne            #0x14a7064
    // 0x14a705c: r0 = Null
    //     0x14a705c: mov             x0, NULL
    // 0x14a7060: b               #0x14a7070
    // 0x14a7064: LoadField: r1 = r0->field_b
    //     0x14a7064: ldur            w1, [x0, #0xb]
    // 0x14a7068: DecompressPointer r1
    //     0x14a7068: add             x1, x1, HEAP, lsl #32
    // 0x14a706c: mov             x0, x1
    // 0x14a7070: cmp             w0, NULL
    // 0x14a7074: b.ne            #0x14a707c
    // 0x14a7078: r0 = ""
    //     0x14a7078: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14a707c: ldur            d1, [fp, #-0x38]
    // 0x14a7080: ldur            d0, [fp, #-0x40]
    // 0x14a7084: stur            x0, [fp, #-0x18]
    // 0x14a7088: r3 = inline_Allocate_Double()
    //     0x14a7088: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0x14a708c: add             x3, x3, #0x10
    //     0x14a7090: cmp             x1, x3
    //     0x14a7094: b.ls            #0x14a7188
    //     0x14a7098: str             x3, [THR, #0x50]  ; THR::top
    //     0x14a709c: sub             x3, x3, #0xf
    //     0x14a70a0: movz            x1, #0xe15c
    //     0x14a70a4: movk            x1, #0x3, lsl #16
    //     0x14a70a8: stur            x1, [x3, #-1]
    // 0x14a70ac: StoreField: r3->field_7 = d1
    //     0x14a70ac: stur            d1, [x3, #7]
    // 0x14a70b0: stur            x3, [fp, #-0x10]
    // 0x14a70b4: r4 = inline_Allocate_Double()
    //     0x14a70b4: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0x14a70b8: add             x4, x4, #0x10
    //     0x14a70bc: cmp             x1, x4
    //     0x14a70c0: b.ls            #0x14a71a4
    //     0x14a70c4: str             x4, [THR, #0x50]  ; THR::top
    //     0x14a70c8: sub             x4, x4, #0xf
    //     0x14a70cc: movz            x1, #0xe15c
    //     0x14a70d0: movk            x1, #0x3, lsl #16
    //     0x14a70d4: stur            x1, [x4, #-1]
    // 0x14a70d8: StoreField: r4->field_7 = d0
    //     0x14a70d8: stur            d0, [x4, #7]
    // 0x14a70dc: stur            x4, [fp, #-8]
    // 0x14a70e0: r1 = Function '<anonymous closure>':.
    //     0x14a70e0: add             x1, PP, #0x43, lsl #12  ; [pp+0x432d8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14a70e4: ldr             x1, [x1, #0x2d8]
    // 0x14a70e8: r2 = Null
    //     0x14a70e8: mov             x2, NULL
    // 0x14a70ec: r0 = AllocateClosure()
    //     0x14a70ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a70f0: r1 = Function '<anonymous closure>':.
    //     0x14a70f0: add             x1, PP, #0x43, lsl #12  ; [pp+0x432e0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14a70f4: ldr             x1, [x1, #0x2e0]
    // 0x14a70f8: r2 = Null
    //     0x14a70f8: mov             x2, NULL
    // 0x14a70fc: stur            x0, [fp, #-0x20]
    // 0x14a7100: r0 = AllocateClosure()
    //     0x14a7100: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a7104: stur            x0, [fp, #-0x28]
    // 0x14a7108: r0 = CachedNetworkImage()
    //     0x14a7108: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14a710c: stur            x0, [fp, #-0x30]
    // 0x14a7110: ldur            x16, [fp, #-0x10]
    // 0x14a7114: ldur            lr, [fp, #-8]
    // 0x14a7118: stp             lr, x16, [SP, #0x10]
    // 0x14a711c: ldur            x16, [fp, #-0x20]
    // 0x14a7120: ldur            lr, [fp, #-0x28]
    // 0x14a7124: stp             lr, x16, [SP]
    // 0x14a7128: mov             x1, x0
    // 0x14a712c: ldur            x2, [fp, #-0x18]
    // 0x14a7130: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0x14a7130: add             x4, PP, #0x40, lsl #12  ; [pp+0x40388] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0x14a7134: ldr             x4, [x4, #0x388]
    // 0x14a7138: r0 = CachedNetworkImage()
    //     0x14a7138: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14a713c: r0 = InteractiveViewer()
    //     0x14a713c: bl              #0xc92d1c  ; AllocateInteractiveViewerStub -> InteractiveViewer (size=0x64)
    // 0x14a7140: stur            x0, [fp, #-8]
    // 0x14a7144: r16 = Instance_EdgeInsets
    //     0x14a7144: ldr             x16, [PP, #0x6d10]  ; [pp+0x6d10] Obj!EdgeInsets@d56cf1
    // 0x14a7148: r30 = 0.500000
    //     0x14a7148: ldr             lr, [PP, #0x47c0]  ; [pp+0x47c0] 0.5
    // 0x14a714c: stp             lr, x16, [SP]
    // 0x14a7150: mov             x1, x0
    // 0x14a7154: ldur            x2, [fp, #-0x30]
    // 0x14a7158: d0 = 2.000000
    //     0x14a7158: fmov            d0, #2.00000000
    // 0x14a715c: r4 = const [0, 0x5, 0x2, 0x3, boundaryMargin, 0x3, minScale, 0x4, null]
    //     0x14a715c: add             x4, PP, #0x37, lsl #12  ; [pp+0x37328] List(9) [0, 0x5, 0x2, 0x3, "boundaryMargin", 0x3, "minScale", 0x4, Null]
    //     0x14a7160: ldr             x4, [x4, #0x328]
    // 0x14a7164: r0 = InteractiveViewer()
    //     0x14a7164: bl              #0xc92b1c  ; [package:flutter/src/widgets/interactive_viewer.dart] InteractiveViewer::InteractiveViewer
    // 0x14a7168: ldur            x0, [fp, #-8]
    // 0x14a716c: LeaveFrame
    //     0x14a716c: mov             SP, fp
    //     0x14a7170: ldp             fp, lr, [SP], #0x10
    // 0x14a7174: ret
    //     0x14a7174: ret             
    // 0x14a7178: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14a7178: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14a717c: b               #0x14a6fb0
    // 0x14a7180: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14a7180: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14a7184: r0 = NullErrorSharedWithoutFPURegs()
    //     0x14a7184: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0x14a7188: stp             q0, q1, [SP, #-0x20]!
    // 0x14a718c: SaveReg r0
    //     0x14a718c: str             x0, [SP, #-8]!
    // 0x14a7190: r0 = AllocateDouble()
    //     0x14a7190: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x14a7194: mov             x3, x0
    // 0x14a7198: RestoreReg r0
    //     0x14a7198: ldr             x0, [SP], #8
    // 0x14a719c: ldp             q0, q1, [SP], #0x20
    // 0x14a71a0: b               #0x14a70ac
    // 0x14a71a4: SaveReg d0
    //     0x14a71a4: str             q0, [SP, #-0x10]!
    // 0x14a71a8: stp             x0, x3, [SP, #-0x10]!
    // 0x14a71ac: r0 = AllocateDouble()
    //     0x14a71ac: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x14a71b0: mov             x4, x0
    // 0x14a71b4: ldp             x0, x3, [SP], #0x10
    // 0x14a71b8: RestoreReg d0
    //     0x14a71b8: ldr             q0, [SP], #0x10
    // 0x14a71bc: b               #0x14a70d8
  }
}
