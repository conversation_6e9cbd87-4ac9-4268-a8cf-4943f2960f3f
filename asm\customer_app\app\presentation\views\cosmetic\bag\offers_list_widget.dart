// lib: , url: package:customer_app/app/presentation/views/cosmetic/bag/offers_list_widget.dart

// class id: 1049228, size: 0x8
class :: {
}

// class id: 3464, size: 0x14, field offset: 0x14
class _OffersListWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xabbd10, size: 0x4e8
    // 0xabbd10: EnterFrame
    //     0xabbd10: stp             fp, lr, [SP, #-0x10]!
    //     0xabbd14: mov             fp, SP
    // 0xabbd18: AllocStack(0x50)
    //     0xabbd18: sub             SP, SP, #0x50
    // 0xabbd1c: SetupParameters(_OffersListWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xabbd1c: mov             x0, x1
    //     0xabbd20: stur            x1, [fp, #-8]
    //     0xabbd24: mov             x1, x2
    //     0xabbd28: stur            x2, [fp, #-0x10]
    // 0xabbd2c: CheckStackOverflow
    //     0xabbd2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xabbd30: cmp             SP, x16
    //     0xabbd34: b.ls            #0xabc1e8
    // 0xabbd38: r1 = 1
    //     0xabbd38: movz            x1, #0x1
    // 0xabbd3c: r0 = AllocateContext()
    //     0xabbd3c: bl              #0x16f6108  ; AllocateContextStub
    // 0xabbd40: mov             x1, x0
    // 0xabbd44: ldur            x0, [fp, #-8]
    // 0xabbd48: stur            x1, [fp, #-0x30]
    // 0xabbd4c: StoreField: r1->field_f = r0
    //     0xabbd4c: stur            w0, [x1, #0xf]
    // 0xabbd50: LoadField: r2 = r0->field_b
    //     0xabbd50: ldur            w2, [x0, #0xb]
    // 0xabbd54: DecompressPointer r2
    //     0xabbd54: add             x2, x2, HEAP, lsl #32
    // 0xabbd58: stur            x2, [fp, #-0x28]
    // 0xabbd5c: cmp             w2, NULL
    // 0xabbd60: b.eq            #0xabc1f0
    // 0xabbd64: LoadField: r3 = r2->field_23
    //     0xabbd64: ldur            w3, [x2, #0x23]
    // 0xabbd68: DecompressPointer r3
    //     0xabbd68: add             x3, x3, HEAP, lsl #32
    // 0xabbd6c: stur            x3, [fp, #-0x20]
    // 0xabbd70: LoadField: r4 = r2->field_b
    //     0xabbd70: ldur            w4, [x2, #0xb]
    // 0xabbd74: DecompressPointer r4
    //     0xabbd74: add             x4, x4, HEAP, lsl #32
    // 0xabbd78: cmp             w4, NULL
    // 0xabbd7c: b.ne            #0xabbd88
    // 0xabbd80: r4 = Null
    //     0xabbd80: mov             x4, NULL
    // 0xabbd84: b               #0xabbd94
    // 0xabbd88: LoadField: r5 = r4->field_f
    //     0xabbd88: ldur            w5, [x4, #0xf]
    // 0xabbd8c: DecompressPointer r5
    //     0xabbd8c: add             x5, x5, HEAP, lsl #32
    // 0xabbd90: LoadField: r4 = r5->field_b
    //     0xabbd90: ldur            w4, [x5, #0xb]
    // 0xabbd94: stur            x4, [fp, #-0x18]
    // 0xabbd98: LoadField: r5 = r2->field_27
    //     0xabbd98: ldur            w5, [x2, #0x27]
    // 0xabbd9c: DecompressPointer r5
    //     0xabbd9c: add             x5, x5, HEAP, lsl #32
    // 0xabbda0: r16 = "false"
    //     0xabbda0: add             x16, PP, #8, lsl #12  ; [pp+0x8ed8] "false"
    //     0xabbda4: ldr             x16, [x16, #0xed8]
    // 0xabbda8: stp             x16, x5, [SP]
    // 0xabbdac: r0 = ==()
    //     0xabbdac: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xabbdb0: tbnz            w0, #4, #0xabbdbc
    // 0xabbdb4: r4 = false
    //     0xabbdb4: add             x4, NULL, #0x30  ; false
    // 0xabbdb8: b               #0xabbdc0
    // 0xabbdbc: r4 = true
    //     0xabbdbc: add             x4, NULL, #0x20  ; true
    // 0xabbdc0: ldur            x0, [fp, #-8]
    // 0xabbdc4: ldur            x2, [fp, #-0x20]
    // 0xabbdc8: ldur            x3, [fp, #-0x18]
    // 0xabbdcc: ldur            x1, [fp, #-0x28]
    // 0xabbdd0: stur            x4, [fp, #-0x38]
    // 0xabbdd4: r0 = EventData()
    //     0xabbdd4: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xabbdd8: mov             x1, x0
    // 0xabbddc: ldur            x0, [fp, #-0x20]
    // 0xabbde0: stur            x1, [fp, #-0x40]
    // 0xabbde4: StoreField: r1->field_87 = r0
    //     0xabbde4: stur            w0, [x1, #0x87]
    // 0xabbde8: ldur            x0, [fp, #-0x18]
    // 0xabbdec: StoreField: r1->field_9b = r0
    //     0xabbdec: stur            w0, [x1, #0x9b]
    // 0xabbdf0: ldur            x0, [fp, #-0x38]
    // 0xabbdf4: StoreField: r1->field_9f = r0
    //     0xabbdf4: stur            w0, [x1, #0x9f]
    // 0xabbdf8: r0 = EventsRequest()
    //     0xabbdf8: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xabbdfc: mov             x1, x0
    // 0xabbe00: r0 = "offer_clicked"
    //     0xabbe00: add             x0, PP, #0x54, lsl #12  ; [pp+0x54b58] "offer_clicked"
    //     0xabbe04: ldr             x0, [x0, #0xb58]
    // 0xabbe08: StoreField: r1->field_7 = r0
    //     0xabbe08: stur            w0, [x1, #7]
    // 0xabbe0c: ldur            x0, [fp, #-0x40]
    // 0xabbe10: StoreField: r1->field_b = r0
    //     0xabbe10: stur            w0, [x1, #0xb]
    // 0xabbe14: ldur            x0, [fp, #-0x28]
    // 0xabbe18: LoadField: r2 = r0->field_1f
    //     0xabbe18: ldur            w2, [x0, #0x1f]
    // 0xabbe1c: DecompressPointer r2
    //     0xabbe1c: add             x2, x2, HEAP, lsl #32
    // 0xabbe20: stp             x1, x2, [SP]
    // 0xabbe24: r4 = 0
    //     0xabbe24: movz            x4, #0
    // 0xabbe28: ldr             x0, [SP, #8]
    // 0xabbe2c: r16 = UnlinkedCall_0x613b5c
    //     0xabbe2c: add             x16, PP, #0x59, lsl #12  ; [pp+0x593d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xabbe30: add             x16, x16, #0x3d8
    // 0xabbe34: ldp             x5, lr, [x16]
    // 0xabbe38: blr             lr
    // 0xabbe3c: ldur            x1, [fp, #-0x10]
    // 0xabbe40: r0 = of()
    //     0xabbe40: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabbe44: LoadField: r1 = r0->field_87
    //     0xabbe44: ldur            w1, [x0, #0x87]
    // 0xabbe48: DecompressPointer r1
    //     0xabbe48: add             x1, x1, HEAP, lsl #32
    // 0xabbe4c: LoadField: r0 = r1->field_23
    //     0xabbe4c: ldur            w0, [x1, #0x23]
    // 0xabbe50: DecompressPointer r0
    //     0xabbe50: add             x0, x0, HEAP, lsl #32
    // 0xabbe54: r16 = Instance_Color
    //     0xabbe54: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xabbe58: r30 = 21.000000
    //     0xabbe58: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xabbe5c: ldr             lr, [lr, #0x9b0]
    // 0xabbe60: stp             lr, x16, [SP]
    // 0xabbe64: mov             x1, x0
    // 0xabbe68: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xabbe68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xabbe6c: ldr             x4, [x4, #0x9b8]
    // 0xabbe70: r0 = copyWith()
    //     0xabbe70: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabbe74: stur            x0, [fp, #-0x10]
    // 0xabbe78: r0 = Text()
    //     0xabbe78: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabbe7c: mov             x1, x0
    // 0xabbe80: r0 = "All Offers & Coupons"
    //     0xabbe80: add             x0, PP, #0x57, lsl #12  ; [pp+0x571a8] "All Offers & Coupons"
    //     0xabbe84: ldr             x0, [x0, #0x1a8]
    // 0xabbe88: stur            x1, [fp, #-0x18]
    // 0xabbe8c: StoreField: r1->field_b = r0
    //     0xabbe8c: stur            w0, [x1, #0xb]
    // 0xabbe90: ldur            x0, [fp, #-0x10]
    // 0xabbe94: StoreField: r1->field_13 = r0
    //     0xabbe94: stur            w0, [x1, #0x13]
    // 0xabbe98: r0 = Padding()
    //     0xabbe98: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xabbe9c: mov             x2, x0
    // 0xabbea0: r0 = Instance_EdgeInsets
    //     0xabbea0: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xabbea4: ldr             x0, [x0, #0xf70]
    // 0xabbea8: stur            x2, [fp, #-0x10]
    // 0xabbeac: StoreField: r2->field_f = r0
    //     0xabbeac: stur            w0, [x2, #0xf]
    // 0xabbeb0: ldur            x0, [fp, #-0x18]
    // 0xabbeb4: StoreField: r2->field_b = r0
    //     0xabbeb4: stur            w0, [x2, #0xb]
    // 0xabbeb8: r1 = <FlexParentData>
    //     0xabbeb8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xabbebc: ldr             x1, [x1, #0xe00]
    // 0xabbec0: r0 = Expanded()
    //     0xabbec0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xabbec4: stur            x0, [fp, #-0x18]
    // 0xabbec8: StoreField: r0->field_13 = rZR
    //     0xabbec8: stur            xzr, [x0, #0x13]
    // 0xabbecc: r1 = Instance_FlexFit
    //     0xabbecc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xabbed0: ldr             x1, [x1, #0xe08]
    // 0xabbed4: StoreField: r0->field_1b = r1
    //     0xabbed4: stur            w1, [x0, #0x1b]
    // 0xabbed8: ldur            x2, [fp, #-0x10]
    // 0xabbedc: StoreField: r0->field_b = r2
    //     0xabbedc: stur            w2, [x0, #0xb]
    // 0xabbee0: r0 = InkWell()
    //     0xabbee0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xabbee4: mov             x3, x0
    // 0xabbee8: r0 = Instance_Icon
    //     0xabbee8: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xabbeec: ldr             x0, [x0, #0x2b8]
    // 0xabbef0: stur            x3, [fp, #-0x10]
    // 0xabbef4: StoreField: r3->field_b = r0
    //     0xabbef4: stur            w0, [x3, #0xb]
    // 0xabbef8: r1 = Function '<anonymous closure>':.
    //     0xabbef8: add             x1, PP, #0x59, lsl #12  ; [pp+0x593e8] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xabbefc: ldr             x1, [x1, #0x3e8]
    // 0xabbf00: r2 = Null
    //     0xabbf00: mov             x2, NULL
    // 0xabbf04: r0 = AllocateClosure()
    //     0xabbf04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabbf08: mov             x1, x0
    // 0xabbf0c: ldur            x0, [fp, #-0x10]
    // 0xabbf10: StoreField: r0->field_f = r1
    //     0xabbf10: stur            w1, [x0, #0xf]
    // 0xabbf14: r1 = true
    //     0xabbf14: add             x1, NULL, #0x20  ; true
    // 0xabbf18: StoreField: r0->field_43 = r1
    //     0xabbf18: stur            w1, [x0, #0x43]
    // 0xabbf1c: r2 = Instance_BoxShape
    //     0xabbf1c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xabbf20: ldr             x2, [x2, #0x80]
    // 0xabbf24: StoreField: r0->field_47 = r2
    //     0xabbf24: stur            w2, [x0, #0x47]
    // 0xabbf28: StoreField: r0->field_6f = r1
    //     0xabbf28: stur            w1, [x0, #0x6f]
    // 0xabbf2c: r2 = false
    //     0xabbf2c: add             x2, NULL, #0x30  ; false
    // 0xabbf30: StoreField: r0->field_73 = r2
    //     0xabbf30: stur            w2, [x0, #0x73]
    // 0xabbf34: StoreField: r0->field_83 = r1
    //     0xabbf34: stur            w1, [x0, #0x83]
    // 0xabbf38: StoreField: r0->field_7b = r2
    //     0xabbf38: stur            w2, [x0, #0x7b]
    // 0xabbf3c: r0 = Padding()
    //     0xabbf3c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xabbf40: mov             x2, x0
    // 0xabbf44: r0 = Instance_EdgeInsets
    //     0xabbf44: add             x0, PP, #0x52, lsl #12  ; [pp+0x52268] Obj!EdgeInsets@d57111
    //     0xabbf48: ldr             x0, [x0, #0x268]
    // 0xabbf4c: stur            x2, [fp, #-0x20]
    // 0xabbf50: StoreField: r2->field_f = r0
    //     0xabbf50: stur            w0, [x2, #0xf]
    // 0xabbf54: ldur            x0, [fp, #-0x10]
    // 0xabbf58: StoreField: r2->field_b = r0
    //     0xabbf58: stur            w0, [x2, #0xb]
    // 0xabbf5c: r1 = <FlexParentData>
    //     0xabbf5c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xabbf60: ldr             x1, [x1, #0xe00]
    // 0xabbf64: r0 = Expanded()
    //     0xabbf64: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xabbf68: stur            x0, [fp, #-0x10]
    // 0xabbf6c: StoreField: r0->field_13 = rZR
    //     0xabbf6c: stur            xzr, [x0, #0x13]
    // 0xabbf70: r1 = Instance_FlexFit
    //     0xabbf70: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xabbf74: ldr             x1, [x1, #0xe08]
    // 0xabbf78: StoreField: r0->field_1b = r1
    //     0xabbf78: stur            w1, [x0, #0x1b]
    // 0xabbf7c: ldur            x1, [fp, #-0x20]
    // 0xabbf80: StoreField: r0->field_b = r1
    //     0xabbf80: stur            w1, [x0, #0xb]
    // 0xabbf84: r1 = Null
    //     0xabbf84: mov             x1, NULL
    // 0xabbf88: r2 = 6
    //     0xabbf88: movz            x2, #0x6
    // 0xabbf8c: r0 = AllocateArray()
    //     0xabbf8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabbf90: mov             x2, x0
    // 0xabbf94: ldur            x0, [fp, #-0x18]
    // 0xabbf98: stur            x2, [fp, #-0x20]
    // 0xabbf9c: StoreField: r2->field_f = r0
    //     0xabbf9c: stur            w0, [x2, #0xf]
    // 0xabbfa0: r16 = Instance_Spacer
    //     0xabbfa0: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xabbfa4: ldr             x16, [x16, #0xf0]
    // 0xabbfa8: StoreField: r2->field_13 = r16
    //     0xabbfa8: stur            w16, [x2, #0x13]
    // 0xabbfac: ldur            x0, [fp, #-0x10]
    // 0xabbfb0: ArrayStore: r2[0] = r0  ; List_4
    //     0xabbfb0: stur            w0, [x2, #0x17]
    // 0xabbfb4: r1 = <Widget>
    //     0xabbfb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabbfb8: r0 = AllocateGrowableArray()
    //     0xabbfb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabbfbc: mov             x1, x0
    // 0xabbfc0: ldur            x0, [fp, #-0x20]
    // 0xabbfc4: stur            x1, [fp, #-0x10]
    // 0xabbfc8: StoreField: r1->field_f = r0
    //     0xabbfc8: stur            w0, [x1, #0xf]
    // 0xabbfcc: r0 = 6
    //     0xabbfcc: movz            x0, #0x6
    // 0xabbfd0: StoreField: r1->field_b = r0
    //     0xabbfd0: stur            w0, [x1, #0xb]
    // 0xabbfd4: r0 = Row()
    //     0xabbfd4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xabbfd8: mov             x3, x0
    // 0xabbfdc: r0 = Instance_Axis
    //     0xabbfdc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xabbfe0: stur            x3, [fp, #-0x18]
    // 0xabbfe4: StoreField: r3->field_f = r0
    //     0xabbfe4: stur            w0, [x3, #0xf]
    // 0xabbfe8: r0 = Instance_MainAxisAlignment
    //     0xabbfe8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xabbfec: ldr             x0, [x0, #0xa08]
    // 0xabbff0: StoreField: r3->field_13 = r0
    //     0xabbff0: stur            w0, [x3, #0x13]
    // 0xabbff4: r1 = Instance_MainAxisSize
    //     0xabbff4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xabbff8: ldr             x1, [x1, #0xa10]
    // 0xabbffc: ArrayStore: r3[0] = r1  ; List_4
    //     0xabbffc: stur            w1, [x3, #0x17]
    // 0xabc000: r4 = Instance_CrossAxisAlignment
    //     0xabc000: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xabc004: ldr             x4, [x4, #0xa18]
    // 0xabc008: StoreField: r3->field_1b = r4
    //     0xabc008: stur            w4, [x3, #0x1b]
    // 0xabc00c: r5 = Instance_VerticalDirection
    //     0xabc00c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabc010: ldr             x5, [x5, #0xa20]
    // 0xabc014: StoreField: r3->field_23 = r5
    //     0xabc014: stur            w5, [x3, #0x23]
    // 0xabc018: r6 = Instance_Clip
    //     0xabc018: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabc01c: ldr             x6, [x6, #0x38]
    // 0xabc020: StoreField: r3->field_2b = r6
    //     0xabc020: stur            w6, [x3, #0x2b]
    // 0xabc024: StoreField: r3->field_2f = rZR
    //     0xabc024: stur            xzr, [x3, #0x2f]
    // 0xabc028: ldur            x1, [fp, #-0x10]
    // 0xabc02c: StoreField: r3->field_b = r1
    //     0xabc02c: stur            w1, [x3, #0xb]
    // 0xabc030: ldur            x1, [fp, #-8]
    // 0xabc034: LoadField: r2 = r1->field_b
    //     0xabc034: ldur            w2, [x1, #0xb]
    // 0xabc038: DecompressPointer r2
    //     0xabc038: add             x2, x2, HEAP, lsl #32
    // 0xabc03c: cmp             w2, NULL
    // 0xabc040: b.eq            #0xabc1f4
    // 0xabc044: LoadField: r1 = r2->field_b
    //     0xabc044: ldur            w1, [x2, #0xb]
    // 0xabc048: DecompressPointer r1
    //     0xabc048: add             x1, x1, HEAP, lsl #32
    // 0xabc04c: cmp             w1, NULL
    // 0xabc050: b.ne            #0xabc05c
    // 0xabc054: r7 = Null
    //     0xabc054: mov             x7, NULL
    // 0xabc058: b               #0xabc06c
    // 0xabc05c: LoadField: r2 = r1->field_f
    //     0xabc05c: ldur            w2, [x1, #0xf]
    // 0xabc060: DecompressPointer r2
    //     0xabc060: add             x2, x2, HEAP, lsl #32
    // 0xabc064: LoadField: r1 = r2->field_b
    //     0xabc064: ldur            w1, [x2, #0xb]
    // 0xabc068: mov             x7, x1
    // 0xabc06c: ldur            x2, [fp, #-0x30]
    // 0xabc070: stur            x7, [fp, #-8]
    // 0xabc074: r1 = Function '<anonymous closure>':.
    //     0xabc074: add             x1, PP, #0x59, lsl #12  ; [pp+0x593f0] AnonymousClosure: (0xabc21c), in [package:customer_app/app/presentation/views/cosmetic/bag/offers_list_widget.dart] _OffersListWidgetState::build (0xabbd10)
    //     0xabc078: ldr             x1, [x1, #0x3f0]
    // 0xabc07c: r0 = AllocateClosure()
    //     0xabc07c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabc080: stur            x0, [fp, #-0x10]
    // 0xabc084: r0 = ListView()
    //     0xabc084: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xabc088: stur            x0, [fp, #-0x20]
    // 0xabc08c: r16 = true
    //     0xabc08c: add             x16, NULL, #0x20  ; true
    // 0xabc090: r30 = Instance_NeverScrollableScrollPhysics
    //     0xabc090: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xabc094: ldr             lr, [lr, #0x1c8]
    // 0xabc098: stp             lr, x16, [SP]
    // 0xabc09c: mov             x1, x0
    // 0xabc0a0: ldur            x2, [fp, #-0x10]
    // 0xabc0a4: ldur            x3, [fp, #-8]
    // 0xabc0a8: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xabc0a8: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xabc0ac: ldr             x4, [x4, #8]
    // 0xabc0b0: r0 = ListView.builder()
    //     0xabc0b0: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xabc0b4: r1 = Null
    //     0xabc0b4: mov             x1, NULL
    // 0xabc0b8: r2 = 8
    //     0xabc0b8: movz            x2, #0x8
    // 0xabc0bc: r0 = AllocateArray()
    //     0xabc0bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabc0c0: stur            x0, [fp, #-8]
    // 0xabc0c4: r16 = Instance_SizedBox
    //     0xabc0c4: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xabc0c8: ldr             x16, [x16, #0x578]
    // 0xabc0cc: StoreField: r0->field_f = r16
    //     0xabc0cc: stur            w16, [x0, #0xf]
    // 0xabc0d0: ldur            x1, [fp, #-0x18]
    // 0xabc0d4: StoreField: r0->field_13 = r1
    //     0xabc0d4: stur            w1, [x0, #0x13]
    // 0xabc0d8: r16 = Instance_SizedBox
    //     0xabc0d8: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xabc0dc: ldr             x16, [x16, #0x578]
    // 0xabc0e0: ArrayStore: r0[0] = r16  ; List_4
    //     0xabc0e0: stur            w16, [x0, #0x17]
    // 0xabc0e4: ldur            x1, [fp, #-0x20]
    // 0xabc0e8: StoreField: r0->field_1b = r1
    //     0xabc0e8: stur            w1, [x0, #0x1b]
    // 0xabc0ec: r1 = <Widget>
    //     0xabc0ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabc0f0: r0 = AllocateGrowableArray()
    //     0xabc0f0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabc0f4: mov             x1, x0
    // 0xabc0f8: ldur            x0, [fp, #-8]
    // 0xabc0fc: stur            x1, [fp, #-0x10]
    // 0xabc100: StoreField: r1->field_f = r0
    //     0xabc100: stur            w0, [x1, #0xf]
    // 0xabc104: r0 = 8
    //     0xabc104: movz            x0, #0x8
    // 0xabc108: StoreField: r1->field_b = r0
    //     0xabc108: stur            w0, [x1, #0xb]
    // 0xabc10c: r0 = Column()
    //     0xabc10c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xabc110: mov             x1, x0
    // 0xabc114: r0 = Instance_Axis
    //     0xabc114: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xabc118: stur            x1, [fp, #-8]
    // 0xabc11c: StoreField: r1->field_f = r0
    //     0xabc11c: stur            w0, [x1, #0xf]
    // 0xabc120: r2 = Instance_MainAxisAlignment
    //     0xabc120: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xabc124: ldr             x2, [x2, #0xa08]
    // 0xabc128: StoreField: r1->field_13 = r2
    //     0xabc128: stur            w2, [x1, #0x13]
    // 0xabc12c: r2 = Instance_MainAxisSize
    //     0xabc12c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xabc130: ldr             x2, [x2, #0xdd0]
    // 0xabc134: ArrayStore: r1[0] = r2  ; List_4
    //     0xabc134: stur            w2, [x1, #0x17]
    // 0xabc138: r2 = Instance_CrossAxisAlignment
    //     0xabc138: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xabc13c: ldr             x2, [x2, #0xa18]
    // 0xabc140: StoreField: r1->field_1b = r2
    //     0xabc140: stur            w2, [x1, #0x1b]
    // 0xabc144: r2 = Instance_VerticalDirection
    //     0xabc144: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabc148: ldr             x2, [x2, #0xa20]
    // 0xabc14c: StoreField: r1->field_23 = r2
    //     0xabc14c: stur            w2, [x1, #0x23]
    // 0xabc150: r2 = Instance_Clip
    //     0xabc150: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabc154: ldr             x2, [x2, #0x38]
    // 0xabc158: StoreField: r1->field_2b = r2
    //     0xabc158: stur            w2, [x1, #0x2b]
    // 0xabc15c: StoreField: r1->field_2f = rZR
    //     0xabc15c: stur            xzr, [x1, #0x2f]
    // 0xabc160: ldur            x2, [fp, #-0x10]
    // 0xabc164: StoreField: r1->field_b = r2
    //     0xabc164: stur            w2, [x1, #0xb]
    // 0xabc168: r0 = SingleChildScrollView()
    //     0xabc168: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xabc16c: mov             x1, x0
    // 0xabc170: r0 = Instance_Axis
    //     0xabc170: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xabc174: stur            x1, [fp, #-0x10]
    // 0xabc178: StoreField: r1->field_b = r0
    //     0xabc178: stur            w0, [x1, #0xb]
    // 0xabc17c: r0 = false
    //     0xabc17c: add             x0, NULL, #0x30  ; false
    // 0xabc180: StoreField: r1->field_f = r0
    //     0xabc180: stur            w0, [x1, #0xf]
    // 0xabc184: ldur            x2, [fp, #-8]
    // 0xabc188: StoreField: r1->field_23 = r2
    //     0xabc188: stur            w2, [x1, #0x23]
    // 0xabc18c: r2 = Instance_DragStartBehavior
    //     0xabc18c: ldr             x2, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xabc190: StoreField: r1->field_27 = r2
    //     0xabc190: stur            w2, [x1, #0x27]
    // 0xabc194: r2 = Instance_Clip
    //     0xabc194: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xabc198: ldr             x2, [x2, #0x7e0]
    // 0xabc19c: StoreField: r1->field_2b = r2
    //     0xabc19c: stur            w2, [x1, #0x2b]
    // 0xabc1a0: r2 = Instance_HitTestBehavior
    //     0xabc1a0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xabc1a4: ldr             x2, [x2, #0x288]
    // 0xabc1a8: StoreField: r1->field_2f = r2
    //     0xabc1a8: stur            w2, [x1, #0x2f]
    // 0xabc1ac: r0 = SafeArea()
    //     0xabc1ac: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xabc1b0: r1 = true
    //     0xabc1b0: add             x1, NULL, #0x20  ; true
    // 0xabc1b4: StoreField: r0->field_b = r1
    //     0xabc1b4: stur            w1, [x0, #0xb]
    // 0xabc1b8: StoreField: r0->field_f = r1
    //     0xabc1b8: stur            w1, [x0, #0xf]
    // 0xabc1bc: StoreField: r0->field_13 = r1
    //     0xabc1bc: stur            w1, [x0, #0x13]
    // 0xabc1c0: ArrayStore: r0[0] = r1  ; List_4
    //     0xabc1c0: stur            w1, [x0, #0x17]
    // 0xabc1c4: r1 = Instance_EdgeInsets
    //     0xabc1c4: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xabc1c8: StoreField: r0->field_1b = r1
    //     0xabc1c8: stur            w1, [x0, #0x1b]
    // 0xabc1cc: r1 = false
    //     0xabc1cc: add             x1, NULL, #0x30  ; false
    // 0xabc1d0: StoreField: r0->field_1f = r1
    //     0xabc1d0: stur            w1, [x0, #0x1f]
    // 0xabc1d4: ldur            x1, [fp, #-0x10]
    // 0xabc1d8: StoreField: r0->field_23 = r1
    //     0xabc1d8: stur            w1, [x0, #0x23]
    // 0xabc1dc: LeaveFrame
    //     0xabc1dc: mov             SP, fp
    //     0xabc1e0: ldp             fp, lr, [SP], #0x10
    // 0xabc1e4: ret
    //     0xabc1e4: ret             
    // 0xabc1e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabc1e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabc1ec: b               #0xabbd38
    // 0xabc1f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabc1f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabc1f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabc1f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xabc21c, size: 0x954
    // 0xabc21c: EnterFrame
    //     0xabc21c: stp             fp, lr, [SP, #-0x10]!
    //     0xabc220: mov             fp, SP
    // 0xabc224: AllocStack(0x60)
    //     0xabc224: sub             SP, SP, #0x60
    // 0xabc228: SetupParameters()
    //     0xabc228: ldr             x0, [fp, #0x20]
    //     0xabc22c: ldur            w1, [x0, #0x17]
    //     0xabc230: add             x1, x1, HEAP, lsl #32
    //     0xabc234: stur            x1, [fp, #-8]
    // 0xabc238: CheckStackOverflow
    //     0xabc238: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xabc23c: cmp             SP, x16
    //     0xabc240: b.ls            #0xabcb18
    // 0xabc244: r0 = Container()
    //     0xabc244: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xabc248: mov             x1, x0
    // 0xabc24c: stur            x0, [fp, #-0x10]
    // 0xabc250: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xabc250: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xabc254: r0 = Container()
    //     0xabc254: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xabc258: ldur            x2, [fp, #-8]
    // 0xabc25c: LoadField: r0 = r2->field_f
    //     0xabc25c: ldur            w0, [x2, #0xf]
    // 0xabc260: DecompressPointer r0
    //     0xabc260: add             x0, x0, HEAP, lsl #32
    // 0xabc264: LoadField: r1 = r0->field_b
    //     0xabc264: ldur            w1, [x0, #0xb]
    // 0xabc268: DecompressPointer r1
    //     0xabc268: add             x1, x1, HEAP, lsl #32
    // 0xabc26c: cmp             w1, NULL
    // 0xabc270: b.eq            #0xabcb20
    // 0xabc274: LoadField: r0 = r1->field_b
    //     0xabc274: ldur            w0, [x1, #0xb]
    // 0xabc278: DecompressPointer r0
    //     0xabc278: add             x0, x0, HEAP, lsl #32
    // 0xabc27c: cmp             w0, NULL
    // 0xabc280: b.ne            #0xabc290
    // 0xabc284: ldr             x3, [fp, #0x10]
    // 0xabc288: r0 = Null
    //     0xabc288: mov             x0, NULL
    // 0xabc28c: b               #0xabc2dc
    // 0xabc290: ldr             x3, [fp, #0x10]
    // 0xabc294: LoadField: r4 = r0->field_f
    //     0xabc294: ldur            w4, [x0, #0xf]
    // 0xabc298: DecompressPointer r4
    //     0xabc298: add             x4, x4, HEAP, lsl #32
    // 0xabc29c: LoadField: r0 = r4->field_b
    //     0xabc29c: ldur            w0, [x4, #0xb]
    // 0xabc2a0: r5 = LoadInt32Instr(r3)
    //     0xabc2a0: sbfx            x5, x3, #1, #0x1f
    //     0xabc2a4: tbz             w3, #0, #0xabc2ac
    //     0xabc2a8: ldur            x5, [x3, #7]
    // 0xabc2ac: r1 = LoadInt32Instr(r0)
    //     0xabc2ac: sbfx            x1, x0, #1, #0x1f
    // 0xabc2b0: mov             x0, x1
    // 0xabc2b4: mov             x1, x5
    // 0xabc2b8: cmp             x1, x0
    // 0xabc2bc: b.hs            #0xabcb24
    // 0xabc2c0: LoadField: r0 = r4->field_f
    //     0xabc2c0: ldur            w0, [x4, #0xf]
    // 0xabc2c4: DecompressPointer r0
    //     0xabc2c4: add             x0, x0, HEAP, lsl #32
    // 0xabc2c8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xabc2c8: add             x16, x0, x5, lsl #2
    //     0xabc2cc: ldur            w1, [x16, #0xf]
    // 0xabc2d0: DecompressPointer r1
    //     0xabc2d0: add             x1, x1, HEAP, lsl #32
    // 0xabc2d4: LoadField: r0 = r1->field_7
    //     0xabc2d4: ldur            w0, [x1, #7]
    // 0xabc2d8: DecompressPointer r0
    //     0xabc2d8: add             x0, x0, HEAP, lsl #32
    // 0xabc2dc: r1 = LoadClassIdInstr(r0)
    //     0xabc2dc: ldur            x1, [x0, #-1]
    //     0xabc2e0: ubfx            x1, x1, #0xc, #0x14
    // 0xabc2e4: r16 = "special_offers"
    //     0xabc2e4: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c0] "special_offers"
    //     0xabc2e8: ldr             x16, [x16, #0x1c0]
    // 0xabc2ec: stp             x16, x0, [SP]
    // 0xabc2f0: mov             x0, x1
    // 0xabc2f4: mov             lr, x0
    // 0xabc2f8: ldr             lr, [x21, lr, lsl #3]
    // 0xabc2fc: blr             lr
    // 0xabc300: tbnz            w0, #4, #0xabc480
    // 0xabc304: ldur            x2, [fp, #-8]
    // 0xabc308: LoadField: r0 = r2->field_f
    //     0xabc308: ldur            w0, [x2, #0xf]
    // 0xabc30c: DecompressPointer r0
    //     0xabc30c: add             x0, x0, HEAP, lsl #32
    // 0xabc310: LoadField: r3 = r0->field_b
    //     0xabc310: ldur            w3, [x0, #0xb]
    // 0xabc314: DecompressPointer r3
    //     0xabc314: add             x3, x3, HEAP, lsl #32
    // 0xabc318: cmp             w3, NULL
    // 0xabc31c: b.eq            #0xabcb28
    // 0xabc320: LoadField: r4 = r3->field_b
    //     0xabc320: ldur            w4, [x3, #0xb]
    // 0xabc324: DecompressPointer r4
    //     0xabc324: add             x4, x4, HEAP, lsl #32
    // 0xabc328: cmp             w4, NULL
    // 0xabc32c: b.ne            #0xabc33c
    // 0xabc330: ldr             x5, [fp, #0x10]
    // 0xabc334: r6 = Null
    //     0xabc334: mov             x6, NULL
    // 0xabc338: b               #0xabc38c
    // 0xabc33c: ldr             x5, [fp, #0x10]
    // 0xabc340: LoadField: r6 = r4->field_f
    //     0xabc340: ldur            w6, [x4, #0xf]
    // 0xabc344: DecompressPointer r6
    //     0xabc344: add             x6, x6, HEAP, lsl #32
    // 0xabc348: LoadField: r0 = r6->field_b
    //     0xabc348: ldur            w0, [x6, #0xb]
    // 0xabc34c: r7 = LoadInt32Instr(r5)
    //     0xabc34c: sbfx            x7, x5, #1, #0x1f
    //     0xabc350: tbz             w5, #0, #0xabc358
    //     0xabc354: ldur            x7, [x5, #7]
    // 0xabc358: r1 = LoadInt32Instr(r0)
    //     0xabc358: sbfx            x1, x0, #1, #0x1f
    // 0xabc35c: mov             x0, x1
    // 0xabc360: mov             x1, x7
    // 0xabc364: cmp             x1, x0
    // 0xabc368: b.hs            #0xabcb2c
    // 0xabc36c: LoadField: r0 = r6->field_f
    //     0xabc36c: ldur            w0, [x6, #0xf]
    // 0xabc370: DecompressPointer r0
    //     0xabc370: add             x0, x0, HEAP, lsl #32
    // 0xabc374: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xabc374: add             x16, x0, x7, lsl #2
    //     0xabc378: ldur            w1, [x16, #0xf]
    // 0xabc37c: DecompressPointer r1
    //     0xabc37c: add             x1, x1, HEAP, lsl #32
    // 0xabc380: LoadField: r0 = r1->field_7
    //     0xabc380: ldur            w0, [x1, #7]
    // 0xabc384: DecompressPointer r0
    //     0xabc384: add             x0, x0, HEAP, lsl #32
    // 0xabc388: mov             x6, x0
    // 0xabc38c: stur            x6, [fp, #-0x50]
    // 0xabc390: cmp             w4, NULL
    // 0xabc394: b.ne            #0xabc3a0
    // 0xabc398: r0 = Null
    //     0xabc398: mov             x0, NULL
    // 0xabc39c: b               #0xabc3e4
    // 0xabc3a0: LoadField: r7 = r4->field_f
    //     0xabc3a0: ldur            w7, [x4, #0xf]
    // 0xabc3a4: DecompressPointer r7
    //     0xabc3a4: add             x7, x7, HEAP, lsl #32
    // 0xabc3a8: LoadField: r0 = r7->field_b
    //     0xabc3a8: ldur            w0, [x7, #0xb]
    // 0xabc3ac: r4 = LoadInt32Instr(r5)
    //     0xabc3ac: sbfx            x4, x5, #1, #0x1f
    //     0xabc3b0: tbz             w5, #0, #0xabc3b8
    //     0xabc3b4: ldur            x4, [x5, #7]
    // 0xabc3b8: r1 = LoadInt32Instr(r0)
    //     0xabc3b8: sbfx            x1, x0, #1, #0x1f
    // 0xabc3bc: mov             x0, x1
    // 0xabc3c0: mov             x1, x4
    // 0xabc3c4: cmp             x1, x0
    // 0xabc3c8: b.hs            #0xabcb30
    // 0xabc3cc: LoadField: r0 = r7->field_f
    //     0xabc3cc: ldur            w0, [x7, #0xf]
    // 0xabc3d0: DecompressPointer r0
    //     0xabc3d0: add             x0, x0, HEAP, lsl #32
    // 0xabc3d4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xabc3d4: add             x16, x0, x4, lsl #2
    //     0xabc3d8: ldur            w1, [x16, #0xf]
    // 0xabc3dc: DecompressPointer r1
    //     0xabc3dc: add             x1, x1, HEAP, lsl #32
    // 0xabc3e0: mov             x0, x1
    // 0xabc3e4: stur            x0, [fp, #-0x48]
    // 0xabc3e8: LoadField: r1 = r3->field_f
    //     0xabc3e8: ldur            w1, [x3, #0xf]
    // 0xabc3ec: DecompressPointer r1
    //     0xabc3ec: add             x1, x1, HEAP, lsl #32
    // 0xabc3f0: stur            x1, [fp, #-0x40]
    // 0xabc3f4: LoadField: r4 = r3->field_13
    //     0xabc3f4: ldur            w4, [x3, #0x13]
    // 0xabc3f8: DecompressPointer r4
    //     0xabc3f8: add             x4, x4, HEAP, lsl #32
    // 0xabc3fc: stur            x4, [fp, #-0x38]
    // 0xabc400: ArrayLoad: r7 = r3[0]  ; List_4
    //     0xabc400: ldur            w7, [x3, #0x17]
    // 0xabc404: DecompressPointer r7
    //     0xabc404: add             x7, x7, HEAP, lsl #32
    // 0xabc408: stur            x7, [fp, #-0x30]
    // 0xabc40c: LoadField: r8 = r3->field_1b
    //     0xabc40c: ldur            w8, [x3, #0x1b]
    // 0xabc410: DecompressPointer r8
    //     0xabc410: add             x8, x8, HEAP, lsl #32
    // 0xabc414: stur            x8, [fp, #-0x28]
    // 0xabc418: LoadField: r9 = r3->field_2b
    //     0xabc418: ldur            w9, [x3, #0x2b]
    // 0xabc41c: DecompressPointer r9
    //     0xabc41c: add             x9, x9, HEAP, lsl #32
    // 0xabc420: stur            x9, [fp, #-0x20]
    // 0xabc424: LoadField: r10 = r3->field_2f
    //     0xabc424: ldur            w10, [x3, #0x2f]
    // 0xabc428: DecompressPointer r10
    //     0xabc428: add             x10, x10, HEAP, lsl #32
    // 0xabc42c: stur            x10, [fp, #-0x18]
    // 0xabc430: r0 = OffersContentItemView()
    //     0xabc430: bl              #0xabcb70  ; AllocateOffersContentItemViewStub -> OffersContentItemView (size=0x2c)
    // 0xabc434: mov             x1, x0
    // 0xabc438: ldur            x0, [fp, #-0x50]
    // 0xabc43c: StoreField: r1->field_b = r0
    //     0xabc43c: stur            w0, [x1, #0xb]
    // 0xabc440: ldur            x0, [fp, #-0x48]
    // 0xabc444: StoreField: r1->field_f = r0
    //     0xabc444: stur            w0, [x1, #0xf]
    // 0xabc448: ldur            x0, [fp, #-0x40]
    // 0xabc44c: StoreField: r1->field_13 = r0
    //     0xabc44c: stur            w0, [x1, #0x13]
    // 0xabc450: ldur            x0, [fp, #-0x38]
    // 0xabc454: ArrayStore: r1[0] = r0  ; List_4
    //     0xabc454: stur            w0, [x1, #0x17]
    // 0xabc458: ldur            x0, [fp, #-0x30]
    // 0xabc45c: StoreField: r1->field_1b = r0
    //     0xabc45c: stur            w0, [x1, #0x1b]
    // 0xabc460: ldur            x0, [fp, #-0x28]
    // 0xabc464: StoreField: r1->field_1f = r0
    //     0xabc464: stur            w0, [x1, #0x1f]
    // 0xabc468: ldur            x0, [fp, #-0x20]
    // 0xabc46c: StoreField: r1->field_23 = r0
    //     0xabc46c: stur            w0, [x1, #0x23]
    // 0xabc470: ldur            x0, [fp, #-0x18]
    // 0xabc474: StoreField: r1->field_27 = r0
    //     0xabc474: stur            w0, [x1, #0x27]
    // 0xabc478: mov             x3, x1
    // 0xabc47c: b               #0xabc484
    // 0xabc480: ldur            x3, [fp, #-0x10]
    // 0xabc484: ldur            x2, [fp, #-8]
    // 0xabc488: stur            x3, [fp, #-0x10]
    // 0xabc48c: LoadField: r0 = r2->field_f
    //     0xabc48c: ldur            w0, [x2, #0xf]
    // 0xabc490: DecompressPointer r0
    //     0xabc490: add             x0, x0, HEAP, lsl #32
    // 0xabc494: LoadField: r1 = r0->field_b
    //     0xabc494: ldur            w1, [x0, #0xb]
    // 0xabc498: DecompressPointer r1
    //     0xabc498: add             x1, x1, HEAP, lsl #32
    // 0xabc49c: cmp             w1, NULL
    // 0xabc4a0: b.eq            #0xabcb34
    // 0xabc4a4: LoadField: r0 = r1->field_b
    //     0xabc4a4: ldur            w0, [x1, #0xb]
    // 0xabc4a8: DecompressPointer r0
    //     0xabc4a8: add             x0, x0, HEAP, lsl #32
    // 0xabc4ac: cmp             w0, NULL
    // 0xabc4b0: b.ne            #0xabc4c0
    // 0xabc4b4: ldr             x4, [fp, #0x10]
    // 0xabc4b8: r0 = Null
    //     0xabc4b8: mov             x0, NULL
    // 0xabc4bc: b               #0xabc50c
    // 0xabc4c0: ldr             x4, [fp, #0x10]
    // 0xabc4c4: LoadField: r5 = r0->field_f
    //     0xabc4c4: ldur            w5, [x0, #0xf]
    // 0xabc4c8: DecompressPointer r5
    //     0xabc4c8: add             x5, x5, HEAP, lsl #32
    // 0xabc4cc: LoadField: r0 = r5->field_b
    //     0xabc4cc: ldur            w0, [x5, #0xb]
    // 0xabc4d0: r6 = LoadInt32Instr(r4)
    //     0xabc4d0: sbfx            x6, x4, #1, #0x1f
    //     0xabc4d4: tbz             w4, #0, #0xabc4dc
    //     0xabc4d8: ldur            x6, [x4, #7]
    // 0xabc4dc: r1 = LoadInt32Instr(r0)
    //     0xabc4dc: sbfx            x1, x0, #1, #0x1f
    // 0xabc4e0: mov             x0, x1
    // 0xabc4e4: mov             x1, x6
    // 0xabc4e8: cmp             x1, x0
    // 0xabc4ec: b.hs            #0xabcb38
    // 0xabc4f0: LoadField: r0 = r5->field_f
    //     0xabc4f0: ldur            w0, [x5, #0xf]
    // 0xabc4f4: DecompressPointer r0
    //     0xabc4f4: add             x0, x0, HEAP, lsl #32
    // 0xabc4f8: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xabc4f8: add             x16, x0, x6, lsl #2
    //     0xabc4fc: ldur            w1, [x16, #0xf]
    // 0xabc500: DecompressPointer r1
    //     0xabc500: add             x1, x1, HEAP, lsl #32
    // 0xabc504: LoadField: r0 = r1->field_7
    //     0xabc504: ldur            w0, [x1, #7]
    // 0xabc508: DecompressPointer r0
    //     0xabc508: add             x0, x0, HEAP, lsl #32
    // 0xabc50c: r1 = LoadClassIdInstr(r0)
    //     0xabc50c: ldur            x1, [x0, #-1]
    //     0xabc510: ubfx            x1, x1, #0xc, #0x14
    // 0xabc514: r16 = "coupons"
    //     0xabc514: add             x16, PP, #0x25, lsl #12  ; [pp+0x25128] "coupons"
    //     0xabc518: ldr             x16, [x16, #0x128]
    // 0xabc51c: stp             x16, x0, [SP]
    // 0xabc520: mov             x0, x1
    // 0xabc524: mov             lr, x0
    // 0xabc528: ldr             lr, [x21, lr, lsl #3]
    // 0xabc52c: blr             lr
    // 0xabc530: tbnz            w0, #4, #0xabc6b0
    // 0xabc534: ldur            x2, [fp, #-8]
    // 0xabc538: LoadField: r0 = r2->field_f
    //     0xabc538: ldur            w0, [x2, #0xf]
    // 0xabc53c: DecompressPointer r0
    //     0xabc53c: add             x0, x0, HEAP, lsl #32
    // 0xabc540: LoadField: r3 = r0->field_b
    //     0xabc540: ldur            w3, [x0, #0xb]
    // 0xabc544: DecompressPointer r3
    //     0xabc544: add             x3, x3, HEAP, lsl #32
    // 0xabc548: cmp             w3, NULL
    // 0xabc54c: b.eq            #0xabcb3c
    // 0xabc550: LoadField: r4 = r3->field_b
    //     0xabc550: ldur            w4, [x3, #0xb]
    // 0xabc554: DecompressPointer r4
    //     0xabc554: add             x4, x4, HEAP, lsl #32
    // 0xabc558: cmp             w4, NULL
    // 0xabc55c: b.ne            #0xabc56c
    // 0xabc560: ldr             x5, [fp, #0x10]
    // 0xabc564: r6 = Null
    //     0xabc564: mov             x6, NULL
    // 0xabc568: b               #0xabc5bc
    // 0xabc56c: ldr             x5, [fp, #0x10]
    // 0xabc570: LoadField: r6 = r4->field_f
    //     0xabc570: ldur            w6, [x4, #0xf]
    // 0xabc574: DecompressPointer r6
    //     0xabc574: add             x6, x6, HEAP, lsl #32
    // 0xabc578: LoadField: r0 = r6->field_b
    //     0xabc578: ldur            w0, [x6, #0xb]
    // 0xabc57c: r7 = LoadInt32Instr(r5)
    //     0xabc57c: sbfx            x7, x5, #1, #0x1f
    //     0xabc580: tbz             w5, #0, #0xabc588
    //     0xabc584: ldur            x7, [x5, #7]
    // 0xabc588: r1 = LoadInt32Instr(r0)
    //     0xabc588: sbfx            x1, x0, #1, #0x1f
    // 0xabc58c: mov             x0, x1
    // 0xabc590: mov             x1, x7
    // 0xabc594: cmp             x1, x0
    // 0xabc598: b.hs            #0xabcb40
    // 0xabc59c: LoadField: r0 = r6->field_f
    //     0xabc59c: ldur            w0, [x6, #0xf]
    // 0xabc5a0: DecompressPointer r0
    //     0xabc5a0: add             x0, x0, HEAP, lsl #32
    // 0xabc5a4: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xabc5a4: add             x16, x0, x7, lsl #2
    //     0xabc5a8: ldur            w1, [x16, #0xf]
    // 0xabc5ac: DecompressPointer r1
    //     0xabc5ac: add             x1, x1, HEAP, lsl #32
    // 0xabc5b0: LoadField: r0 = r1->field_7
    //     0xabc5b0: ldur            w0, [x1, #7]
    // 0xabc5b4: DecompressPointer r0
    //     0xabc5b4: add             x0, x0, HEAP, lsl #32
    // 0xabc5b8: mov             x6, x0
    // 0xabc5bc: stur            x6, [fp, #-0x50]
    // 0xabc5c0: cmp             w4, NULL
    // 0xabc5c4: b.ne            #0xabc5d0
    // 0xabc5c8: r0 = Null
    //     0xabc5c8: mov             x0, NULL
    // 0xabc5cc: b               #0xabc614
    // 0xabc5d0: LoadField: r7 = r4->field_f
    //     0xabc5d0: ldur            w7, [x4, #0xf]
    // 0xabc5d4: DecompressPointer r7
    //     0xabc5d4: add             x7, x7, HEAP, lsl #32
    // 0xabc5d8: LoadField: r0 = r7->field_b
    //     0xabc5d8: ldur            w0, [x7, #0xb]
    // 0xabc5dc: r4 = LoadInt32Instr(r5)
    //     0xabc5dc: sbfx            x4, x5, #1, #0x1f
    //     0xabc5e0: tbz             w5, #0, #0xabc5e8
    //     0xabc5e4: ldur            x4, [x5, #7]
    // 0xabc5e8: r1 = LoadInt32Instr(r0)
    //     0xabc5e8: sbfx            x1, x0, #1, #0x1f
    // 0xabc5ec: mov             x0, x1
    // 0xabc5f0: mov             x1, x4
    // 0xabc5f4: cmp             x1, x0
    // 0xabc5f8: b.hs            #0xabcb44
    // 0xabc5fc: LoadField: r0 = r7->field_f
    //     0xabc5fc: ldur            w0, [x7, #0xf]
    // 0xabc600: DecompressPointer r0
    //     0xabc600: add             x0, x0, HEAP, lsl #32
    // 0xabc604: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xabc604: add             x16, x0, x4, lsl #2
    //     0xabc608: ldur            w1, [x16, #0xf]
    // 0xabc60c: DecompressPointer r1
    //     0xabc60c: add             x1, x1, HEAP, lsl #32
    // 0xabc610: mov             x0, x1
    // 0xabc614: stur            x0, [fp, #-0x48]
    // 0xabc618: LoadField: r1 = r3->field_f
    //     0xabc618: ldur            w1, [x3, #0xf]
    // 0xabc61c: DecompressPointer r1
    //     0xabc61c: add             x1, x1, HEAP, lsl #32
    // 0xabc620: stur            x1, [fp, #-0x40]
    // 0xabc624: LoadField: r4 = r3->field_13
    //     0xabc624: ldur            w4, [x3, #0x13]
    // 0xabc628: DecompressPointer r4
    //     0xabc628: add             x4, x4, HEAP, lsl #32
    // 0xabc62c: stur            x4, [fp, #-0x38]
    // 0xabc630: ArrayLoad: r7 = r3[0]  ; List_4
    //     0xabc630: ldur            w7, [x3, #0x17]
    // 0xabc634: DecompressPointer r7
    //     0xabc634: add             x7, x7, HEAP, lsl #32
    // 0xabc638: stur            x7, [fp, #-0x30]
    // 0xabc63c: LoadField: r8 = r3->field_1b
    //     0xabc63c: ldur            w8, [x3, #0x1b]
    // 0xabc640: DecompressPointer r8
    //     0xabc640: add             x8, x8, HEAP, lsl #32
    // 0xabc644: stur            x8, [fp, #-0x28]
    // 0xabc648: LoadField: r9 = r3->field_2b
    //     0xabc648: ldur            w9, [x3, #0x2b]
    // 0xabc64c: DecompressPointer r9
    //     0xabc64c: add             x9, x9, HEAP, lsl #32
    // 0xabc650: stur            x9, [fp, #-0x20]
    // 0xabc654: LoadField: r10 = r3->field_2f
    //     0xabc654: ldur            w10, [x3, #0x2f]
    // 0xabc658: DecompressPointer r10
    //     0xabc658: add             x10, x10, HEAP, lsl #32
    // 0xabc65c: stur            x10, [fp, #-0x18]
    // 0xabc660: r0 = OffersContentItemView()
    //     0xabc660: bl              #0xabcb70  ; AllocateOffersContentItemViewStub -> OffersContentItemView (size=0x2c)
    // 0xabc664: mov             x1, x0
    // 0xabc668: ldur            x0, [fp, #-0x50]
    // 0xabc66c: StoreField: r1->field_b = r0
    //     0xabc66c: stur            w0, [x1, #0xb]
    // 0xabc670: ldur            x0, [fp, #-0x48]
    // 0xabc674: StoreField: r1->field_f = r0
    //     0xabc674: stur            w0, [x1, #0xf]
    // 0xabc678: ldur            x0, [fp, #-0x40]
    // 0xabc67c: StoreField: r1->field_13 = r0
    //     0xabc67c: stur            w0, [x1, #0x13]
    // 0xabc680: ldur            x0, [fp, #-0x38]
    // 0xabc684: ArrayStore: r1[0] = r0  ; List_4
    //     0xabc684: stur            w0, [x1, #0x17]
    // 0xabc688: ldur            x0, [fp, #-0x30]
    // 0xabc68c: StoreField: r1->field_1b = r0
    //     0xabc68c: stur            w0, [x1, #0x1b]
    // 0xabc690: ldur            x0, [fp, #-0x28]
    // 0xabc694: StoreField: r1->field_1f = r0
    //     0xabc694: stur            w0, [x1, #0x1f]
    // 0xabc698: ldur            x0, [fp, #-0x20]
    // 0xabc69c: StoreField: r1->field_23 = r0
    //     0xabc69c: stur            w0, [x1, #0x23]
    // 0xabc6a0: ldur            x0, [fp, #-0x18]
    // 0xabc6a4: StoreField: r1->field_27 = r0
    //     0xabc6a4: stur            w0, [x1, #0x27]
    // 0xabc6a8: mov             x3, x1
    // 0xabc6ac: b               #0xabc6b4
    // 0xabc6b0: ldur            x3, [fp, #-0x10]
    // 0xabc6b4: ldur            x2, [fp, #-8]
    // 0xabc6b8: stur            x3, [fp, #-0x10]
    // 0xabc6bc: LoadField: r0 = r2->field_f
    //     0xabc6bc: ldur            w0, [x2, #0xf]
    // 0xabc6c0: DecompressPointer r0
    //     0xabc6c0: add             x0, x0, HEAP, lsl #32
    // 0xabc6c4: LoadField: r1 = r0->field_b
    //     0xabc6c4: ldur            w1, [x0, #0xb]
    // 0xabc6c8: DecompressPointer r1
    //     0xabc6c8: add             x1, x1, HEAP, lsl #32
    // 0xabc6cc: cmp             w1, NULL
    // 0xabc6d0: b.eq            #0xabcb48
    // 0xabc6d4: LoadField: r0 = r1->field_b
    //     0xabc6d4: ldur            w0, [x1, #0xb]
    // 0xabc6d8: DecompressPointer r0
    //     0xabc6d8: add             x0, x0, HEAP, lsl #32
    // 0xabc6dc: cmp             w0, NULL
    // 0xabc6e0: b.ne            #0xabc6f0
    // 0xabc6e4: ldr             x4, [fp, #0x10]
    // 0xabc6e8: r0 = Null
    //     0xabc6e8: mov             x0, NULL
    // 0xabc6ec: b               #0xabc73c
    // 0xabc6f0: ldr             x4, [fp, #0x10]
    // 0xabc6f4: LoadField: r5 = r0->field_f
    //     0xabc6f4: ldur            w5, [x0, #0xf]
    // 0xabc6f8: DecompressPointer r5
    //     0xabc6f8: add             x5, x5, HEAP, lsl #32
    // 0xabc6fc: LoadField: r0 = r5->field_b
    //     0xabc6fc: ldur            w0, [x5, #0xb]
    // 0xabc700: r6 = LoadInt32Instr(r4)
    //     0xabc700: sbfx            x6, x4, #1, #0x1f
    //     0xabc704: tbz             w4, #0, #0xabc70c
    //     0xabc708: ldur            x6, [x4, #7]
    // 0xabc70c: r1 = LoadInt32Instr(r0)
    //     0xabc70c: sbfx            x1, x0, #1, #0x1f
    // 0xabc710: mov             x0, x1
    // 0xabc714: mov             x1, x6
    // 0xabc718: cmp             x1, x0
    // 0xabc71c: b.hs            #0xabcb4c
    // 0xabc720: LoadField: r0 = r5->field_f
    //     0xabc720: ldur            w0, [x5, #0xf]
    // 0xabc724: DecompressPointer r0
    //     0xabc724: add             x0, x0, HEAP, lsl #32
    // 0xabc728: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xabc728: add             x16, x0, x6, lsl #2
    //     0xabc72c: ldur            w1, [x16, #0xf]
    // 0xabc730: DecompressPointer r1
    //     0xabc730: add             x1, x1, HEAP, lsl #32
    // 0xabc734: LoadField: r0 = r1->field_7
    //     0xabc734: ldur            w0, [x1, #7]
    // 0xabc738: DecompressPointer r0
    //     0xabc738: add             x0, x0, HEAP, lsl #32
    // 0xabc73c: r1 = LoadClassIdInstr(r0)
    //     0xabc73c: ldur            x1, [x0, #-1]
    //     0xabc740: ubfx            x1, x1, #0xc, #0x14
    // 0xabc744: r16 = "checkout_offers"
    //     0xabc744: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c8] "checkout_offers"
    //     0xabc748: ldr             x16, [x16, #0x1c8]
    // 0xabc74c: stp             x16, x0, [SP]
    // 0xabc750: mov             x0, x1
    // 0xabc754: mov             lr, x0
    // 0xabc758: ldr             lr, [x21, lr, lsl #3]
    // 0xabc75c: blr             lr
    // 0xabc760: tbnz            w0, #4, #0xabc8e0
    // 0xabc764: ldur            x2, [fp, #-8]
    // 0xabc768: LoadField: r0 = r2->field_f
    //     0xabc768: ldur            w0, [x2, #0xf]
    // 0xabc76c: DecompressPointer r0
    //     0xabc76c: add             x0, x0, HEAP, lsl #32
    // 0xabc770: LoadField: r3 = r0->field_b
    //     0xabc770: ldur            w3, [x0, #0xb]
    // 0xabc774: DecompressPointer r3
    //     0xabc774: add             x3, x3, HEAP, lsl #32
    // 0xabc778: cmp             w3, NULL
    // 0xabc77c: b.eq            #0xabcb50
    // 0xabc780: LoadField: r4 = r3->field_b
    //     0xabc780: ldur            w4, [x3, #0xb]
    // 0xabc784: DecompressPointer r4
    //     0xabc784: add             x4, x4, HEAP, lsl #32
    // 0xabc788: cmp             w4, NULL
    // 0xabc78c: b.ne            #0xabc79c
    // 0xabc790: ldr             x5, [fp, #0x10]
    // 0xabc794: r6 = Null
    //     0xabc794: mov             x6, NULL
    // 0xabc798: b               #0xabc7ec
    // 0xabc79c: ldr             x5, [fp, #0x10]
    // 0xabc7a0: LoadField: r6 = r4->field_f
    //     0xabc7a0: ldur            w6, [x4, #0xf]
    // 0xabc7a4: DecompressPointer r6
    //     0xabc7a4: add             x6, x6, HEAP, lsl #32
    // 0xabc7a8: LoadField: r0 = r6->field_b
    //     0xabc7a8: ldur            w0, [x6, #0xb]
    // 0xabc7ac: r7 = LoadInt32Instr(r5)
    //     0xabc7ac: sbfx            x7, x5, #1, #0x1f
    //     0xabc7b0: tbz             w5, #0, #0xabc7b8
    //     0xabc7b4: ldur            x7, [x5, #7]
    // 0xabc7b8: r1 = LoadInt32Instr(r0)
    //     0xabc7b8: sbfx            x1, x0, #1, #0x1f
    // 0xabc7bc: mov             x0, x1
    // 0xabc7c0: mov             x1, x7
    // 0xabc7c4: cmp             x1, x0
    // 0xabc7c8: b.hs            #0xabcb54
    // 0xabc7cc: LoadField: r0 = r6->field_f
    //     0xabc7cc: ldur            w0, [x6, #0xf]
    // 0xabc7d0: DecompressPointer r0
    //     0xabc7d0: add             x0, x0, HEAP, lsl #32
    // 0xabc7d4: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xabc7d4: add             x16, x0, x7, lsl #2
    //     0xabc7d8: ldur            w1, [x16, #0xf]
    // 0xabc7dc: DecompressPointer r1
    //     0xabc7dc: add             x1, x1, HEAP, lsl #32
    // 0xabc7e0: LoadField: r0 = r1->field_7
    //     0xabc7e0: ldur            w0, [x1, #7]
    // 0xabc7e4: DecompressPointer r0
    //     0xabc7e4: add             x0, x0, HEAP, lsl #32
    // 0xabc7e8: mov             x6, x0
    // 0xabc7ec: stur            x6, [fp, #-0x50]
    // 0xabc7f0: cmp             w4, NULL
    // 0xabc7f4: b.ne            #0xabc800
    // 0xabc7f8: r0 = Null
    //     0xabc7f8: mov             x0, NULL
    // 0xabc7fc: b               #0xabc844
    // 0xabc800: LoadField: r7 = r4->field_f
    //     0xabc800: ldur            w7, [x4, #0xf]
    // 0xabc804: DecompressPointer r7
    //     0xabc804: add             x7, x7, HEAP, lsl #32
    // 0xabc808: LoadField: r0 = r7->field_b
    //     0xabc808: ldur            w0, [x7, #0xb]
    // 0xabc80c: r4 = LoadInt32Instr(r5)
    //     0xabc80c: sbfx            x4, x5, #1, #0x1f
    //     0xabc810: tbz             w5, #0, #0xabc818
    //     0xabc814: ldur            x4, [x5, #7]
    // 0xabc818: r1 = LoadInt32Instr(r0)
    //     0xabc818: sbfx            x1, x0, #1, #0x1f
    // 0xabc81c: mov             x0, x1
    // 0xabc820: mov             x1, x4
    // 0xabc824: cmp             x1, x0
    // 0xabc828: b.hs            #0xabcb58
    // 0xabc82c: LoadField: r0 = r7->field_f
    //     0xabc82c: ldur            w0, [x7, #0xf]
    // 0xabc830: DecompressPointer r0
    //     0xabc830: add             x0, x0, HEAP, lsl #32
    // 0xabc834: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xabc834: add             x16, x0, x4, lsl #2
    //     0xabc838: ldur            w1, [x16, #0xf]
    // 0xabc83c: DecompressPointer r1
    //     0xabc83c: add             x1, x1, HEAP, lsl #32
    // 0xabc840: mov             x0, x1
    // 0xabc844: stur            x0, [fp, #-0x48]
    // 0xabc848: LoadField: r1 = r3->field_f
    //     0xabc848: ldur            w1, [x3, #0xf]
    // 0xabc84c: DecompressPointer r1
    //     0xabc84c: add             x1, x1, HEAP, lsl #32
    // 0xabc850: stur            x1, [fp, #-0x40]
    // 0xabc854: LoadField: r4 = r3->field_13
    //     0xabc854: ldur            w4, [x3, #0x13]
    // 0xabc858: DecompressPointer r4
    //     0xabc858: add             x4, x4, HEAP, lsl #32
    // 0xabc85c: stur            x4, [fp, #-0x38]
    // 0xabc860: ArrayLoad: r7 = r3[0]  ; List_4
    //     0xabc860: ldur            w7, [x3, #0x17]
    // 0xabc864: DecompressPointer r7
    //     0xabc864: add             x7, x7, HEAP, lsl #32
    // 0xabc868: stur            x7, [fp, #-0x30]
    // 0xabc86c: LoadField: r8 = r3->field_1b
    //     0xabc86c: ldur            w8, [x3, #0x1b]
    // 0xabc870: DecompressPointer r8
    //     0xabc870: add             x8, x8, HEAP, lsl #32
    // 0xabc874: stur            x8, [fp, #-0x28]
    // 0xabc878: LoadField: r9 = r3->field_2b
    //     0xabc878: ldur            w9, [x3, #0x2b]
    // 0xabc87c: DecompressPointer r9
    //     0xabc87c: add             x9, x9, HEAP, lsl #32
    // 0xabc880: stur            x9, [fp, #-0x20]
    // 0xabc884: LoadField: r10 = r3->field_2f
    //     0xabc884: ldur            w10, [x3, #0x2f]
    // 0xabc888: DecompressPointer r10
    //     0xabc888: add             x10, x10, HEAP, lsl #32
    // 0xabc88c: stur            x10, [fp, #-0x18]
    // 0xabc890: r0 = OffersContentItemView()
    //     0xabc890: bl              #0xabcb70  ; AllocateOffersContentItemViewStub -> OffersContentItemView (size=0x2c)
    // 0xabc894: mov             x1, x0
    // 0xabc898: ldur            x0, [fp, #-0x50]
    // 0xabc89c: StoreField: r1->field_b = r0
    //     0xabc89c: stur            w0, [x1, #0xb]
    // 0xabc8a0: ldur            x0, [fp, #-0x48]
    // 0xabc8a4: StoreField: r1->field_f = r0
    //     0xabc8a4: stur            w0, [x1, #0xf]
    // 0xabc8a8: ldur            x0, [fp, #-0x40]
    // 0xabc8ac: StoreField: r1->field_13 = r0
    //     0xabc8ac: stur            w0, [x1, #0x13]
    // 0xabc8b0: ldur            x0, [fp, #-0x38]
    // 0xabc8b4: ArrayStore: r1[0] = r0  ; List_4
    //     0xabc8b4: stur            w0, [x1, #0x17]
    // 0xabc8b8: ldur            x0, [fp, #-0x30]
    // 0xabc8bc: StoreField: r1->field_1b = r0
    //     0xabc8bc: stur            w0, [x1, #0x1b]
    // 0xabc8c0: ldur            x0, [fp, #-0x28]
    // 0xabc8c4: StoreField: r1->field_1f = r0
    //     0xabc8c4: stur            w0, [x1, #0x1f]
    // 0xabc8c8: ldur            x0, [fp, #-0x20]
    // 0xabc8cc: StoreField: r1->field_23 = r0
    //     0xabc8cc: stur            w0, [x1, #0x23]
    // 0xabc8d0: ldur            x0, [fp, #-0x18]
    // 0xabc8d4: StoreField: r1->field_27 = r0
    //     0xabc8d4: stur            w0, [x1, #0x27]
    // 0xabc8d8: mov             x3, x1
    // 0xabc8dc: b               #0xabc8e4
    // 0xabc8e0: ldur            x3, [fp, #-0x10]
    // 0xabc8e4: ldur            x2, [fp, #-8]
    // 0xabc8e8: stur            x3, [fp, #-0x10]
    // 0xabc8ec: LoadField: r0 = r2->field_f
    //     0xabc8ec: ldur            w0, [x2, #0xf]
    // 0xabc8f0: DecompressPointer r0
    //     0xabc8f0: add             x0, x0, HEAP, lsl #32
    // 0xabc8f4: LoadField: r1 = r0->field_b
    //     0xabc8f4: ldur            w1, [x0, #0xb]
    // 0xabc8f8: DecompressPointer r1
    //     0xabc8f8: add             x1, x1, HEAP, lsl #32
    // 0xabc8fc: cmp             w1, NULL
    // 0xabc900: b.eq            #0xabcb5c
    // 0xabc904: LoadField: r0 = r1->field_b
    //     0xabc904: ldur            w0, [x1, #0xb]
    // 0xabc908: DecompressPointer r0
    //     0xabc908: add             x0, x0, HEAP, lsl #32
    // 0xabc90c: cmp             w0, NULL
    // 0xabc910: b.ne            #0xabc920
    // 0xabc914: ldr             x4, [fp, #0x10]
    // 0xabc918: r0 = Null
    //     0xabc918: mov             x0, NULL
    // 0xabc91c: b               #0xabc96c
    // 0xabc920: ldr             x4, [fp, #0x10]
    // 0xabc924: LoadField: r5 = r0->field_f
    //     0xabc924: ldur            w5, [x0, #0xf]
    // 0xabc928: DecompressPointer r5
    //     0xabc928: add             x5, x5, HEAP, lsl #32
    // 0xabc92c: LoadField: r0 = r5->field_b
    //     0xabc92c: ldur            w0, [x5, #0xb]
    // 0xabc930: r6 = LoadInt32Instr(r4)
    //     0xabc930: sbfx            x6, x4, #1, #0x1f
    //     0xabc934: tbz             w4, #0, #0xabc93c
    //     0xabc938: ldur            x6, [x4, #7]
    // 0xabc93c: r1 = LoadInt32Instr(r0)
    //     0xabc93c: sbfx            x1, x0, #1, #0x1f
    // 0xabc940: mov             x0, x1
    // 0xabc944: mov             x1, x6
    // 0xabc948: cmp             x1, x0
    // 0xabc94c: b.hs            #0xabcb60
    // 0xabc950: LoadField: r0 = r5->field_f
    //     0xabc950: ldur            w0, [x5, #0xf]
    // 0xabc954: DecompressPointer r0
    //     0xabc954: add             x0, x0, HEAP, lsl #32
    // 0xabc958: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xabc958: add             x16, x0, x6, lsl #2
    //     0xabc95c: ldur            w1, [x16, #0xf]
    // 0xabc960: DecompressPointer r1
    //     0xabc960: add             x1, x1, HEAP, lsl #32
    // 0xabc964: LoadField: r0 = r1->field_7
    //     0xabc964: ldur            w0, [x1, #7]
    // 0xabc968: DecompressPointer r0
    //     0xabc968: add             x0, x0, HEAP, lsl #32
    // 0xabc96c: r1 = LoadClassIdInstr(r0)
    //     0xabc96c: ldur            x1, [x0, #-1]
    //     0xabc970: ubfx            x1, x1, #0xc, #0x14
    // 0xabc974: r16 = "other_offers"
    //     0xabc974: add             x16, PP, #0x57, lsl #12  ; [pp+0x571d0] "other_offers"
    //     0xabc978: ldr             x16, [x16, #0x1d0]
    // 0xabc97c: stp             x16, x0, [SP]
    // 0xabc980: mov             x0, x1
    // 0xabc984: mov             lr, x0
    // 0xabc988: ldr             lr, [x21, lr, lsl #3]
    // 0xabc98c: blr             lr
    // 0xabc990: tbnz            w0, #4, #0xabcb08
    // 0xabc994: ldur            x0, [fp, #-8]
    // 0xabc998: LoadField: r1 = r0->field_f
    //     0xabc998: ldur            w1, [x0, #0xf]
    // 0xabc99c: DecompressPointer r1
    //     0xabc99c: add             x1, x1, HEAP, lsl #32
    // 0xabc9a0: LoadField: r2 = r1->field_b
    //     0xabc9a0: ldur            w2, [x1, #0xb]
    // 0xabc9a4: DecompressPointer r2
    //     0xabc9a4: add             x2, x2, HEAP, lsl #32
    // 0xabc9a8: cmp             w2, NULL
    // 0xabc9ac: b.eq            #0xabcb64
    // 0xabc9b0: LoadField: r3 = r2->field_b
    //     0xabc9b0: ldur            w3, [x2, #0xb]
    // 0xabc9b4: DecompressPointer r3
    //     0xabc9b4: add             x3, x3, HEAP, lsl #32
    // 0xabc9b8: cmp             w3, NULL
    // 0xabc9bc: b.ne            #0xabc9cc
    // 0xabc9c0: ldr             x4, [fp, #0x10]
    // 0xabc9c4: r5 = Null
    //     0xabc9c4: mov             x5, NULL
    // 0xabc9c8: b               #0xabca1c
    // 0xabc9cc: ldr             x4, [fp, #0x10]
    // 0xabc9d0: LoadField: r5 = r3->field_f
    //     0xabc9d0: ldur            w5, [x3, #0xf]
    // 0xabc9d4: DecompressPointer r5
    //     0xabc9d4: add             x5, x5, HEAP, lsl #32
    // 0xabc9d8: LoadField: r0 = r5->field_b
    //     0xabc9d8: ldur            w0, [x5, #0xb]
    // 0xabc9dc: r6 = LoadInt32Instr(r4)
    //     0xabc9dc: sbfx            x6, x4, #1, #0x1f
    //     0xabc9e0: tbz             w4, #0, #0xabc9e8
    //     0xabc9e4: ldur            x6, [x4, #7]
    // 0xabc9e8: r1 = LoadInt32Instr(r0)
    //     0xabc9e8: sbfx            x1, x0, #1, #0x1f
    // 0xabc9ec: mov             x0, x1
    // 0xabc9f0: mov             x1, x6
    // 0xabc9f4: cmp             x1, x0
    // 0xabc9f8: b.hs            #0xabcb68
    // 0xabc9fc: LoadField: r0 = r5->field_f
    //     0xabc9fc: ldur            w0, [x5, #0xf]
    // 0xabca00: DecompressPointer r0
    //     0xabca00: add             x0, x0, HEAP, lsl #32
    // 0xabca04: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xabca04: add             x16, x0, x6, lsl #2
    //     0xabca08: ldur            w1, [x16, #0xf]
    // 0xabca0c: DecompressPointer r1
    //     0xabca0c: add             x1, x1, HEAP, lsl #32
    // 0xabca10: LoadField: r0 = r1->field_7
    //     0xabca10: ldur            w0, [x1, #7]
    // 0xabca14: DecompressPointer r0
    //     0xabca14: add             x0, x0, HEAP, lsl #32
    // 0xabca18: mov             x5, x0
    // 0xabca1c: stur            x5, [fp, #-0x48]
    // 0xabca20: cmp             w3, NULL
    // 0xabca24: b.ne            #0xabca30
    // 0xabca28: r0 = Null
    //     0xabca28: mov             x0, NULL
    // 0xabca2c: b               #0xabca74
    // 0xabca30: LoadField: r6 = r3->field_f
    //     0xabca30: ldur            w6, [x3, #0xf]
    // 0xabca34: DecompressPointer r6
    //     0xabca34: add             x6, x6, HEAP, lsl #32
    // 0xabca38: LoadField: r0 = r6->field_b
    //     0xabca38: ldur            w0, [x6, #0xb]
    // 0xabca3c: r3 = LoadInt32Instr(r4)
    //     0xabca3c: sbfx            x3, x4, #1, #0x1f
    //     0xabca40: tbz             w4, #0, #0xabca48
    //     0xabca44: ldur            x3, [x4, #7]
    // 0xabca48: r1 = LoadInt32Instr(r0)
    //     0xabca48: sbfx            x1, x0, #1, #0x1f
    // 0xabca4c: mov             x0, x1
    // 0xabca50: mov             x1, x3
    // 0xabca54: cmp             x1, x0
    // 0xabca58: b.hs            #0xabcb6c
    // 0xabca5c: LoadField: r0 = r6->field_f
    //     0xabca5c: ldur            w0, [x6, #0xf]
    // 0xabca60: DecompressPointer r0
    //     0xabca60: add             x0, x0, HEAP, lsl #32
    // 0xabca64: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xabca64: add             x16, x0, x3, lsl #2
    //     0xabca68: ldur            w1, [x16, #0xf]
    // 0xabca6c: DecompressPointer r1
    //     0xabca6c: add             x1, x1, HEAP, lsl #32
    // 0xabca70: mov             x0, x1
    // 0xabca74: stur            x0, [fp, #-0x40]
    // 0xabca78: LoadField: r1 = r2->field_f
    //     0xabca78: ldur            w1, [x2, #0xf]
    // 0xabca7c: DecompressPointer r1
    //     0xabca7c: add             x1, x1, HEAP, lsl #32
    // 0xabca80: stur            x1, [fp, #-0x38]
    // 0xabca84: LoadField: r3 = r2->field_13
    //     0xabca84: ldur            w3, [x2, #0x13]
    // 0xabca88: DecompressPointer r3
    //     0xabca88: add             x3, x3, HEAP, lsl #32
    // 0xabca8c: stur            x3, [fp, #-0x30]
    // 0xabca90: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xabca90: ldur            w4, [x2, #0x17]
    // 0xabca94: DecompressPointer r4
    //     0xabca94: add             x4, x4, HEAP, lsl #32
    // 0xabca98: stur            x4, [fp, #-0x28]
    // 0xabca9c: LoadField: r6 = r2->field_1b
    //     0xabca9c: ldur            w6, [x2, #0x1b]
    // 0xabcaa0: DecompressPointer r6
    //     0xabcaa0: add             x6, x6, HEAP, lsl #32
    // 0xabcaa4: stur            x6, [fp, #-0x20]
    // 0xabcaa8: LoadField: r7 = r2->field_2b
    //     0xabcaa8: ldur            w7, [x2, #0x2b]
    // 0xabcaac: DecompressPointer r7
    //     0xabcaac: add             x7, x7, HEAP, lsl #32
    // 0xabcab0: stur            x7, [fp, #-0x18]
    // 0xabcab4: LoadField: r8 = r2->field_2f
    //     0xabcab4: ldur            w8, [x2, #0x2f]
    // 0xabcab8: DecompressPointer r8
    //     0xabcab8: add             x8, x8, HEAP, lsl #32
    // 0xabcabc: stur            x8, [fp, #-8]
    // 0xabcac0: r0 = OffersContentItemView()
    //     0xabcac0: bl              #0xabcb70  ; AllocateOffersContentItemViewStub -> OffersContentItemView (size=0x2c)
    // 0xabcac4: ldur            x1, [fp, #-0x48]
    // 0xabcac8: StoreField: r0->field_b = r1
    //     0xabcac8: stur            w1, [x0, #0xb]
    // 0xabcacc: ldur            x1, [fp, #-0x40]
    // 0xabcad0: StoreField: r0->field_f = r1
    //     0xabcad0: stur            w1, [x0, #0xf]
    // 0xabcad4: ldur            x1, [fp, #-0x38]
    // 0xabcad8: StoreField: r0->field_13 = r1
    //     0xabcad8: stur            w1, [x0, #0x13]
    // 0xabcadc: ldur            x1, [fp, #-0x30]
    // 0xabcae0: ArrayStore: r0[0] = r1  ; List_4
    //     0xabcae0: stur            w1, [x0, #0x17]
    // 0xabcae4: ldur            x1, [fp, #-0x28]
    // 0xabcae8: StoreField: r0->field_1b = r1
    //     0xabcae8: stur            w1, [x0, #0x1b]
    // 0xabcaec: ldur            x1, [fp, #-0x20]
    // 0xabcaf0: StoreField: r0->field_1f = r1
    //     0xabcaf0: stur            w1, [x0, #0x1f]
    // 0xabcaf4: ldur            x1, [fp, #-0x18]
    // 0xabcaf8: StoreField: r0->field_23 = r1
    //     0xabcaf8: stur            w1, [x0, #0x23]
    // 0xabcafc: ldur            x1, [fp, #-8]
    // 0xabcb00: StoreField: r0->field_27 = r1
    //     0xabcb00: stur            w1, [x0, #0x27]
    // 0xabcb04: b               #0xabcb0c
    // 0xabcb08: ldur            x0, [fp, #-0x10]
    // 0xabcb0c: LeaveFrame
    //     0xabcb0c: mov             SP, fp
    //     0xabcb10: ldp             fp, lr, [SP], #0x10
    // 0xabcb14: ret
    //     0xabcb14: ret             
    // 0xabcb18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabcb18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabcb1c: b               #0xabc244
    // 0xabcb20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabcb20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabcb24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabcb24: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabcb28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabcb28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabcb2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabcb2c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabcb30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabcb30: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabcb34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabcb34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabcb38: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabcb38: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabcb3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabcb3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabcb40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabcb40: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabcb44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabcb44: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabcb48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabcb48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabcb4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabcb4c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabcb50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabcb50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabcb54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabcb54: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabcb58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabcb58: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabcb5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabcb5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabcb60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabcb60: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabcb64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabcb64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabcb68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabcb68: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabcb6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabcb6c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4197, size: 0x34, field offset: 0xc
//   const constructor, 
class OffersListWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7d028, size: 0x24
    // 0xc7d028: EnterFrame
    //     0xc7d028: stp             fp, lr, [SP, #-0x10]!
    //     0xc7d02c: mov             fp, SP
    // 0xc7d030: mov             x0, x1
    // 0xc7d034: r1 = <OffersListWidget>
    //     0xc7d034: add             x1, PP, #0x48, lsl #12  ; [pp+0x48de0] TypeArguments: <OffersListWidget>
    //     0xc7d038: ldr             x1, [x1, #0xde0]
    // 0xc7d03c: r0 = _OffersListWidgetState()
    //     0xc7d03c: bl              #0xc7d04c  ; Allocate_OffersListWidgetStateStub -> _OffersListWidgetState (size=0x14)
    // 0xc7d040: LeaveFrame
    //     0xc7d040: mov             SP, fp
    //     0xc7d044: ldp             fp, lr, [SP], #0x10
    // 0xc7d048: ret
    //     0xc7d048: ret             
  }
}
