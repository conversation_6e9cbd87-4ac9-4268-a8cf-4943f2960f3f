// lib: , url: package:customer_app/app/presentation/views/glass/search/widgets/search_view.dart

// class id: 1049459, size: 0x8
class :: {
}

// class id: 3297, size: 0x24, field offset: 0x14
class _SearchViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb9ad68, size: 0x11bc
    // 0xb9ad68: EnterFrame
    //     0xb9ad68: stp             fp, lr, [SP, #-0x10]!
    //     0xb9ad6c: mov             fp, SP
    // 0xb9ad70: AllocStack(0x90)
    //     0xb9ad70: sub             SP, SP, #0x90
    // 0xb9ad74: SetupParameters(_SearchViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb9ad74: mov             x0, x1
    //     0xb9ad78: stur            x1, [fp, #-8]
    //     0xb9ad7c: mov             x1, x2
    //     0xb9ad80: stur            x2, [fp, #-0x10]
    // 0xb9ad84: CheckStackOverflow
    //     0xb9ad84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9ad88: cmp             SP, x16
    //     0xb9ad8c: b.ls            #0xb9bf00
    // 0xb9ad90: r1 = 2
    //     0xb9ad90: movz            x1, #0x2
    // 0xb9ad94: r0 = AllocateContext()
    //     0xb9ad94: bl              #0x16f6108  ; AllocateContextStub
    // 0xb9ad98: mov             x2, x0
    // 0xb9ad9c: ldur            x0, [fp, #-8]
    // 0xb9ada0: stur            x2, [fp, #-0x18]
    // 0xb9ada4: StoreField: r2->field_f = r0
    //     0xb9ada4: stur            w0, [x2, #0xf]
    // 0xb9ada8: ldur            x1, [fp, #-0x10]
    // 0xb9adac: StoreField: r2->field_13 = r1
    //     0xb9adac: stur            w1, [x2, #0x13]
    // 0xb9adb0: r0 = of()
    //     0xb9adb0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9adb4: LoadField: r1 = r0->field_87
    //     0xb9adb4: ldur            w1, [x0, #0x87]
    // 0xb9adb8: DecompressPointer r1
    //     0xb9adb8: add             x1, x1, HEAP, lsl #32
    // 0xb9adbc: LoadField: r0 = r1->field_2b
    //     0xb9adbc: ldur            w0, [x1, #0x2b]
    // 0xb9adc0: DecompressPointer r0
    //     0xb9adc0: add             x0, x0, HEAP, lsl #32
    // 0xb9adc4: stur            x0, [fp, #-0x10]
    // 0xb9adc8: r1 = Instance_Color
    //     0xb9adc8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9adcc: d0 = 0.700000
    //     0xb9adcc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb9add0: ldr             d0, [x17, #0xf48]
    // 0xb9add4: r0 = withOpacity()
    //     0xb9add4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb9add8: r16 = 16.000000
    //     0xb9add8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb9addc: ldr             x16, [x16, #0x188]
    // 0xb9ade0: stp             x0, x16, [SP]
    // 0xb9ade4: ldur            x1, [fp, #-0x10]
    // 0xb9ade8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb9ade8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9adec: ldr             x4, [x4, #0xaa0]
    // 0xb9adf0: r0 = copyWith()
    //     0xb9adf0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9adf4: mov             x2, x0
    // 0xb9adf8: ldur            x0, [fp, #-8]
    // 0xb9adfc: stur            x2, [fp, #-0x20]
    // 0xb9ae00: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb9ae00: ldur            w3, [x0, #0x17]
    // 0xb9ae04: DecompressPointer r3
    //     0xb9ae04: add             x3, x3, HEAP, lsl #32
    // 0xb9ae08: ldur            x4, [fp, #-0x18]
    // 0xb9ae0c: stur            x3, [fp, #-0x10]
    // 0xb9ae10: LoadField: r1 = r4->field_13
    //     0xb9ae10: ldur            w1, [x4, #0x13]
    // 0xb9ae14: DecompressPointer r1
    //     0xb9ae14: add             x1, x1, HEAP, lsl #32
    // 0xb9ae18: r0 = of()
    //     0xb9ae18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9ae1c: LoadField: r1 = r0->field_87
    //     0xb9ae1c: ldur            w1, [x0, #0x87]
    // 0xb9ae20: DecompressPointer r1
    //     0xb9ae20: add             x1, x1, HEAP, lsl #32
    // 0xb9ae24: LoadField: r0 = r1->field_2b
    //     0xb9ae24: ldur            w0, [x1, #0x2b]
    // 0xb9ae28: DecompressPointer r0
    //     0xb9ae28: add             x0, x0, HEAP, lsl #32
    // 0xb9ae2c: stur            x0, [fp, #-0x28]
    // 0xb9ae30: r1 = Instance_Color
    //     0xb9ae30: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9ae34: d0 = 0.400000
    //     0xb9ae34: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb9ae38: r0 = withOpacity()
    //     0xb9ae38: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb9ae3c: r16 = 16.000000
    //     0xb9ae3c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb9ae40: ldr             x16, [x16, #0x188]
    // 0xb9ae44: stp             x16, x0, [SP]
    // 0xb9ae48: ldur            x1, [fp, #-0x28]
    // 0xb9ae4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb9ae4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb9ae50: ldr             x4, [x4, #0x9b8]
    // 0xb9ae54: r0 = copyWith()
    //     0xb9ae54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9ae58: ldur            x2, [fp, #-0x18]
    // 0xb9ae5c: stur            x0, [fp, #-0x28]
    // 0xb9ae60: LoadField: r1 = r2->field_13
    //     0xb9ae60: ldur            w1, [x2, #0x13]
    // 0xb9ae64: DecompressPointer r1
    //     0xb9ae64: add             x1, x1, HEAP, lsl #32
    // 0xb9ae68: r0 = of()
    //     0xb9ae68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9ae6c: LoadField: r1 = r0->field_5b
    //     0xb9ae6c: ldur            w1, [x0, #0x5b]
    // 0xb9ae70: DecompressPointer r1
    //     0xb9ae70: add             x1, x1, HEAP, lsl #32
    // 0xb9ae74: stur            x1, [fp, #-0x30]
    // 0xb9ae78: r0 = ColorFilter()
    //     0xb9ae78: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb9ae7c: mov             x1, x0
    // 0xb9ae80: ldur            x0, [fp, #-0x30]
    // 0xb9ae84: stur            x1, [fp, #-0x38]
    // 0xb9ae88: StoreField: r1->field_7 = r0
    //     0xb9ae88: stur            w0, [x1, #7]
    // 0xb9ae8c: r0 = Instance_BlendMode
    //     0xb9ae8c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb9ae90: ldr             x0, [x0, #0xb30]
    // 0xb9ae94: StoreField: r1->field_b = r0
    //     0xb9ae94: stur            w0, [x1, #0xb]
    // 0xb9ae98: r2 = 1
    //     0xb9ae98: movz            x2, #0x1
    // 0xb9ae9c: StoreField: r1->field_13 = r2
    //     0xb9ae9c: stur            x2, [x1, #0x13]
    // 0xb9aea0: r0 = SvgPicture()
    //     0xb9aea0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb9aea4: stur            x0, [fp, #-0x30]
    // 0xb9aea8: ldur            x16, [fp, #-0x38]
    // 0xb9aeac: str             x16, [SP]
    // 0xb9aeb0: mov             x1, x0
    // 0xb9aeb4: r2 = "assets/images/search.svg"
    //     0xb9aeb4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0xb9aeb8: ldr             x2, [x2, #0xa30]
    // 0xb9aebc: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb9aebc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb9aec0: ldr             x4, [x4, #0xa38]
    // 0xb9aec4: r0 = SvgPicture.asset()
    //     0xb9aec4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb9aec8: r0 = Align()
    //     0xb9aec8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb9aecc: mov             x1, x0
    // 0xb9aed0: r0 = Instance_Alignment
    //     0xb9aed0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb9aed4: ldr             x0, [x0, #0xb10]
    // 0xb9aed8: stur            x1, [fp, #-0x38]
    // 0xb9aedc: StoreField: r1->field_f = r0
    //     0xb9aedc: stur            w0, [x1, #0xf]
    // 0xb9aee0: r2 = 1.000000
    //     0xb9aee0: ldr             x2, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb9aee4: StoreField: r1->field_13 = r2
    //     0xb9aee4: stur            w2, [x1, #0x13]
    // 0xb9aee8: ArrayStore: r1[0] = r2  ; List_4
    //     0xb9aee8: stur            w2, [x1, #0x17]
    // 0xb9aeec: ldur            x3, [fp, #-0x30]
    // 0xb9aef0: StoreField: r1->field_b = r3
    //     0xb9aef0: stur            w3, [x1, #0xb]
    // 0xb9aef4: r0 = Align()
    //     0xb9aef4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb9aef8: mov             x1, x0
    // 0xb9aefc: r0 = Instance_Alignment
    //     0xb9aefc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb9af00: ldr             x0, [x0, #0xb10]
    // 0xb9af04: stur            x1, [fp, #-0x30]
    // 0xb9af08: StoreField: r1->field_f = r0
    //     0xb9af08: stur            w0, [x1, #0xf]
    // 0xb9af0c: r0 = 1.000000
    //     0xb9af0c: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb9af10: StoreField: r1->field_13 = r0
    //     0xb9af10: stur            w0, [x1, #0x13]
    // 0xb9af14: ArrayStore: r1[0] = r0  ; List_4
    //     0xb9af14: stur            w0, [x1, #0x17]
    // 0xb9af18: ldur            x2, [fp, #-0x38]
    // 0xb9af1c: StoreField: r1->field_b = r2
    //     0xb9af1c: stur            w2, [x1, #0xb]
    // 0xb9af20: r0 = InputDecoration()
    //     0xb9af20: bl              #0x81349c  ; AllocateInputDecorationStub -> InputDecoration (size=0xec)
    // 0xb9af24: mov             x3, x0
    // 0xb9af28: r0 = "Search"
    //     0xb9af28: add             x0, PP, #0x51, lsl #12  ; [pp+0x51c08] "Search"
    //     0xb9af2c: ldr             x0, [x0, #0xc08]
    // 0xb9af30: stur            x3, [fp, #-0x38]
    // 0xb9af34: StoreField: r3->field_2f = r0
    //     0xb9af34: stur            w0, [x3, #0x2f]
    // 0xb9af38: ldur            x0, [fp, #-0x28]
    // 0xb9af3c: StoreField: r3->field_37 = r0
    //     0xb9af3c: stur            w0, [x3, #0x37]
    // 0xb9af40: r0 = true
    //     0xb9af40: add             x0, NULL, #0x20  ; true
    // 0xb9af44: StoreField: r3->field_47 = r0
    //     0xb9af44: stur            w0, [x3, #0x47]
    // 0xb9af48: StoreField: r3->field_4b = r0
    //     0xb9af48: stur            w0, [x3, #0x4b]
    // 0xb9af4c: ldur            x1, [fp, #-0x30]
    // 0xb9af50: StoreField: r3->field_73 = r1
    //     0xb9af50: stur            w1, [x3, #0x73]
    // 0xb9af54: r1 = Instance__NoInputBorder
    //     0xb9af54: add             x1, PP, #0x33, lsl #12  ; [pp+0x33ee8] Obj!_NoInputBorder@d5ad71
    //     0xb9af58: ldr             x1, [x1, #0xee8]
    // 0xb9af5c: StoreField: r3->field_d3 = r1
    //     0xb9af5c: stur            w1, [x3, #0xd3]
    // 0xb9af60: StoreField: r3->field_d7 = r0
    //     0xb9af60: stur            w0, [x3, #0xd7]
    // 0xb9af64: ldur            x2, [fp, #-0x18]
    // 0xb9af68: r1 = Function '<anonymous closure>':.
    //     0xb9af68: add             x1, PP, #0x54, lsl #12  ; [pp+0x54d98] AnonymousClosure: (0xb9cc80), in [package:customer_app/app/presentation/views/glass/search/widgets/search_view.dart] _SearchViewState::build (0xb9ad68)
    //     0xb9af6c: ldr             x1, [x1, #0xd98]
    // 0xb9af70: r0 = AllocateClosure()
    //     0xb9af70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9af74: ldur            x2, [fp, #-0x18]
    // 0xb9af78: r1 = Function '<anonymous closure>':.
    //     0xb9af78: add             x1, PP, #0x54, lsl #12  ; [pp+0x54da0] AnonymousClosure: (0xb9ca60), in [package:customer_app/app/presentation/views/glass/search/widgets/search_view.dart] _SearchViewState::build (0xb9ad68)
    //     0xb9af7c: ldr             x1, [x1, #0xda0]
    // 0xb9af80: stur            x0, [fp, #-0x28]
    // 0xb9af84: r0 = AllocateClosure()
    //     0xb9af84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9af88: r1 = <String>
    //     0xb9af88: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb9af8c: stur            x0, [fp, #-0x30]
    // 0xb9af90: r0 = TextFormField()
    //     0xb9af90: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb9af94: stur            x0, [fp, #-0x40]
    // 0xb9af98: r16 = true
    //     0xb9af98: add             x16, NULL, #0x20  ; true
    // 0xb9af9c: ldur            lr, [fp, #-0x20]
    // 0xb9afa0: stp             lr, x16, [SP, #0x20]
    // 0xb9afa4: ldur            x16, [fp, #-0x10]
    // 0xb9afa8: r30 = Instance_TextInputAction
    //     0xb9afa8: ldr             lr, [PP, #0x7460]  ; [pp+0x7460] Obj!TextInputAction@d72a01
    // 0xb9afac: stp             lr, x16, [SP, #0x10]
    // 0xb9afb0: ldur            x16, [fp, #-0x28]
    // 0xb9afb4: ldur            lr, [fp, #-0x30]
    // 0xb9afb8: stp             lr, x16, [SP]
    // 0xb9afbc: mov             x1, x0
    // 0xb9afc0: ldur            x2, [fp, #-0x38]
    // 0xb9afc4: r4 = const [0, 0x8, 0x6, 0x2, autofocus, 0x2, controller, 0x4, onChanged, 0x7, onFieldSubmitted, 0x6, style, 0x3, textInputAction, 0x5, null]
    //     0xb9afc4: add             x4, PP, #0x51, lsl #12  ; [pp+0x51c20] List(17) [0, 0x8, 0x6, 0x2, "autofocus", 0x2, "controller", 0x4, "onChanged", 0x7, "onFieldSubmitted", 0x6, "style", 0x3, "textInputAction", 0x5, Null]
    //     0xb9afc8: ldr             x4, [x4, #0xc20]
    // 0xb9afcc: r0 = TextFormField()
    //     0xb9afcc: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb9afd0: r0 = Padding()
    //     0xb9afd0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9afd4: mov             x2, x0
    // 0xb9afd8: r0 = Instance_EdgeInsets
    //     0xb9afd8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb9afdc: ldr             x0, [x0, #0xa78]
    // 0xb9afe0: stur            x2, [fp, #-0x20]
    // 0xb9afe4: StoreField: r2->field_f = r0
    //     0xb9afe4: stur            w0, [x2, #0xf]
    // 0xb9afe8: ldur            x0, [fp, #-0x40]
    // 0xb9afec: StoreField: r2->field_b = r0
    //     0xb9afec: stur            w0, [x2, #0xb]
    // 0xb9aff0: r1 = <FlexParentData>
    //     0xb9aff0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb9aff4: ldr             x1, [x1, #0xe00]
    // 0xb9aff8: r0 = Expanded()
    //     0xb9aff8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb9affc: mov             x2, x0
    // 0xb9b000: r0 = 1
    //     0xb9b000: movz            x0, #0x1
    // 0xb9b004: stur            x2, [fp, #-0x28]
    // 0xb9b008: StoreField: r2->field_13 = r0
    //     0xb9b008: stur            x0, [x2, #0x13]
    // 0xb9b00c: r3 = Instance_FlexFit
    //     0xb9b00c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb9b010: ldr             x3, [x3, #0xe08]
    // 0xb9b014: StoreField: r2->field_1b = r3
    //     0xb9b014: stur            w3, [x2, #0x1b]
    // 0xb9b018: ldur            x1, [fp, #-0x20]
    // 0xb9b01c: StoreField: r2->field_b = r1
    //     0xb9b01c: stur            w1, [x2, #0xb]
    // 0xb9b020: ldur            x4, [fp, #-8]
    // 0xb9b024: LoadField: r5 = r4->field_13
    //     0xb9b024: ldur            w5, [x4, #0x13]
    // 0xb9b028: DecompressPointer r5
    //     0xb9b028: add             x5, x5, HEAP, lsl #32
    // 0xb9b02c: ldur            x6, [fp, #-0x18]
    // 0xb9b030: stur            x5, [fp, #-0x20]
    // 0xb9b034: LoadField: r1 = r6->field_13
    //     0xb9b034: ldur            w1, [x6, #0x13]
    // 0xb9b038: DecompressPointer r1
    //     0xb9b038: add             x1, x1, HEAP, lsl #32
    // 0xb9b03c: r0 = of()
    //     0xb9b03c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9b040: LoadField: r1 = r0->field_5b
    //     0xb9b040: ldur            w1, [x0, #0x5b]
    // 0xb9b044: DecompressPointer r1
    //     0xb9b044: add             x1, x1, HEAP, lsl #32
    // 0xb9b048: stur            x1, [fp, #-0x30]
    // 0xb9b04c: r0 = ColorFilter()
    //     0xb9b04c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb9b050: mov             x1, x0
    // 0xb9b054: ldur            x0, [fp, #-0x30]
    // 0xb9b058: stur            x1, [fp, #-0x38]
    // 0xb9b05c: StoreField: r1->field_7 = r0
    //     0xb9b05c: stur            w0, [x1, #7]
    // 0xb9b060: r0 = Instance_BlendMode
    //     0xb9b060: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb9b064: ldr             x0, [x0, #0xb30]
    // 0xb9b068: StoreField: r1->field_b = r0
    //     0xb9b068: stur            w0, [x1, #0xb]
    // 0xb9b06c: r2 = 1
    //     0xb9b06c: movz            x2, #0x1
    // 0xb9b070: StoreField: r1->field_13 = r2
    //     0xb9b070: stur            x2, [x1, #0x13]
    // 0xb9b074: r0 = SvgPicture()
    //     0xb9b074: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb9b078: stur            x0, [fp, #-0x30]
    // 0xb9b07c: ldur            x16, [fp, #-0x38]
    // 0xb9b080: str             x16, [SP]
    // 0xb9b084: mov             x1, x0
    // 0xb9b088: r2 = "assets/images/x.svg"
    //     0xb9b088: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xb9b08c: ldr             x2, [x2, #0x5e8]
    // 0xb9b090: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb9b090: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb9b094: ldr             x4, [x4, #0xa38]
    // 0xb9b098: r0 = SvgPicture.asset()
    //     0xb9b098: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb9b09c: r0 = InkWell()
    //     0xb9b09c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb9b0a0: mov             x3, x0
    // 0xb9b0a4: ldur            x0, [fp, #-0x30]
    // 0xb9b0a8: stur            x3, [fp, #-0x38]
    // 0xb9b0ac: StoreField: r3->field_b = r0
    //     0xb9b0ac: stur            w0, [x3, #0xb]
    // 0xb9b0b0: ldur            x2, [fp, #-0x18]
    // 0xb9b0b4: r1 = Function '<anonymous closure>':.
    //     0xb9b0b4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54da8] AnonymousClosure: (0xb9c9b8), in [package:customer_app/app/presentation/views/glass/search/widgets/search_view.dart] _SearchViewState::build (0xb9ad68)
    //     0xb9b0b8: ldr             x1, [x1, #0xda8]
    // 0xb9b0bc: r0 = AllocateClosure()
    //     0xb9b0bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9b0c0: mov             x1, x0
    // 0xb9b0c4: ldur            x0, [fp, #-0x38]
    // 0xb9b0c8: StoreField: r0->field_f = r1
    //     0xb9b0c8: stur            w1, [x0, #0xf]
    // 0xb9b0cc: r1 = true
    //     0xb9b0cc: add             x1, NULL, #0x20  ; true
    // 0xb9b0d0: StoreField: r0->field_43 = r1
    //     0xb9b0d0: stur            w1, [x0, #0x43]
    // 0xb9b0d4: r2 = Instance_BoxShape
    //     0xb9b0d4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb9b0d8: ldr             x2, [x2, #0x80]
    // 0xb9b0dc: StoreField: r0->field_47 = r2
    //     0xb9b0dc: stur            w2, [x0, #0x47]
    // 0xb9b0e0: StoreField: r0->field_6f = r1
    //     0xb9b0e0: stur            w1, [x0, #0x6f]
    // 0xb9b0e4: r2 = false
    //     0xb9b0e4: add             x2, NULL, #0x30  ; false
    // 0xb9b0e8: StoreField: r0->field_73 = r2
    //     0xb9b0e8: stur            w2, [x0, #0x73]
    // 0xb9b0ec: StoreField: r0->field_83 = r1
    //     0xb9b0ec: stur            w1, [x0, #0x83]
    // 0xb9b0f0: StoreField: r0->field_7b = r2
    //     0xb9b0f0: stur            w2, [x0, #0x7b]
    // 0xb9b0f4: r0 = Visibility()
    //     0xb9b0f4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb9b0f8: mov             x1, x0
    // 0xb9b0fc: ldur            x0, [fp, #-0x38]
    // 0xb9b100: stur            x1, [fp, #-0x30]
    // 0xb9b104: StoreField: r1->field_b = r0
    //     0xb9b104: stur            w0, [x1, #0xb]
    // 0xb9b108: r0 = Instance_SizedBox
    //     0xb9b108: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb9b10c: StoreField: r1->field_f = r0
    //     0xb9b10c: stur            w0, [x1, #0xf]
    // 0xb9b110: ldur            x2, [fp, #-0x20]
    // 0xb9b114: StoreField: r1->field_13 = r2
    //     0xb9b114: stur            w2, [x1, #0x13]
    // 0xb9b118: r2 = false
    //     0xb9b118: add             x2, NULL, #0x30  ; false
    // 0xb9b11c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb9b11c: stur            w2, [x1, #0x17]
    // 0xb9b120: StoreField: r1->field_1b = r2
    //     0xb9b120: stur            w2, [x1, #0x1b]
    // 0xb9b124: StoreField: r1->field_1f = r2
    //     0xb9b124: stur            w2, [x1, #0x1f]
    // 0xb9b128: StoreField: r1->field_23 = r2
    //     0xb9b128: stur            w2, [x1, #0x23]
    // 0xb9b12c: StoreField: r1->field_27 = r2
    //     0xb9b12c: stur            w2, [x1, #0x27]
    // 0xb9b130: StoreField: r1->field_2b = r2
    //     0xb9b130: stur            w2, [x1, #0x2b]
    // 0xb9b134: r0 = Padding()
    //     0xb9b134: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9b138: mov             x3, x0
    // 0xb9b13c: r0 = Instance_EdgeInsets
    //     0xb9b13c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xb9b140: ldr             x0, [x0, #0xd48]
    // 0xb9b144: stur            x3, [fp, #-0x20]
    // 0xb9b148: StoreField: r3->field_f = r0
    //     0xb9b148: stur            w0, [x3, #0xf]
    // 0xb9b14c: ldur            x1, [fp, #-0x30]
    // 0xb9b150: StoreField: r3->field_b = r1
    //     0xb9b150: stur            w1, [x3, #0xb]
    // 0xb9b154: r1 = Null
    //     0xb9b154: mov             x1, NULL
    // 0xb9b158: r2 = 4
    //     0xb9b158: movz            x2, #0x4
    // 0xb9b15c: r0 = AllocateArray()
    //     0xb9b15c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9b160: mov             x2, x0
    // 0xb9b164: ldur            x0, [fp, #-0x28]
    // 0xb9b168: stur            x2, [fp, #-0x30]
    // 0xb9b16c: StoreField: r2->field_f = r0
    //     0xb9b16c: stur            w0, [x2, #0xf]
    // 0xb9b170: ldur            x0, [fp, #-0x20]
    // 0xb9b174: StoreField: r2->field_13 = r0
    //     0xb9b174: stur            w0, [x2, #0x13]
    // 0xb9b178: r1 = <Widget>
    //     0xb9b178: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9b17c: r0 = AllocateGrowableArray()
    //     0xb9b17c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9b180: mov             x1, x0
    // 0xb9b184: ldur            x0, [fp, #-0x30]
    // 0xb9b188: stur            x1, [fp, #-0x20]
    // 0xb9b18c: StoreField: r1->field_f = r0
    //     0xb9b18c: stur            w0, [x1, #0xf]
    // 0xb9b190: r2 = 4
    //     0xb9b190: movz            x2, #0x4
    // 0xb9b194: StoreField: r1->field_b = r2
    //     0xb9b194: stur            w2, [x1, #0xb]
    // 0xb9b198: r0 = Row()
    //     0xb9b198: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb9b19c: mov             x3, x0
    // 0xb9b1a0: r0 = Instance_Axis
    //     0xb9b1a0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb9b1a4: stur            x3, [fp, #-0x28]
    // 0xb9b1a8: StoreField: r3->field_f = r0
    //     0xb9b1a8: stur            w0, [x3, #0xf]
    // 0xb9b1ac: r4 = Instance_MainAxisAlignment
    //     0xb9b1ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9b1b0: ldr             x4, [x4, #0xa08]
    // 0xb9b1b4: StoreField: r3->field_13 = r4
    //     0xb9b1b4: stur            w4, [x3, #0x13]
    // 0xb9b1b8: r5 = Instance_MainAxisSize
    //     0xb9b1b8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9b1bc: ldr             x5, [x5, #0xa10]
    // 0xb9b1c0: ArrayStore: r3[0] = r5  ; List_4
    //     0xb9b1c0: stur            w5, [x3, #0x17]
    // 0xb9b1c4: r1 = Instance_CrossAxisAlignment
    //     0xb9b1c4: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb9b1c8: ldr             x1, [x1, #0xc68]
    // 0xb9b1cc: StoreField: r3->field_1b = r1
    //     0xb9b1cc: stur            w1, [x3, #0x1b]
    // 0xb9b1d0: r6 = Instance_VerticalDirection
    //     0xb9b1d0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9b1d4: ldr             x6, [x6, #0xa20]
    // 0xb9b1d8: StoreField: r3->field_23 = r6
    //     0xb9b1d8: stur            w6, [x3, #0x23]
    // 0xb9b1dc: r7 = Instance_Clip
    //     0xb9b1dc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9b1e0: ldr             x7, [x7, #0x38]
    // 0xb9b1e4: StoreField: r3->field_2b = r7
    //     0xb9b1e4: stur            w7, [x3, #0x2b]
    // 0xb9b1e8: StoreField: r3->field_2f = rZR
    //     0xb9b1e8: stur            xzr, [x3, #0x2f]
    // 0xb9b1ec: ldur            x1, [fp, #-0x20]
    // 0xb9b1f0: StoreField: r3->field_b = r1
    //     0xb9b1f0: stur            w1, [x3, #0xb]
    // 0xb9b1f4: ldur            x8, [fp, #-8]
    // 0xb9b1f8: LoadField: r1 = r8->field_b
    //     0xb9b1f8: ldur            w1, [x8, #0xb]
    // 0xb9b1fc: DecompressPointer r1
    //     0xb9b1fc: add             x1, x1, HEAP, lsl #32
    // 0xb9b200: cmp             w1, NULL
    // 0xb9b204: b.eq            #0xb9bf08
    // 0xb9b208: LoadField: r2 = r1->field_b
    //     0xb9b208: ldur            w2, [x1, #0xb]
    // 0xb9b20c: DecompressPointer r2
    //     0xb9b20c: add             x2, x2, HEAP, lsl #32
    // 0xb9b210: LoadField: r9 = r2->field_f
    //     0xb9b210: ldur            w9, [x2, #0xf]
    // 0xb9b214: DecompressPointer r9
    //     0xb9b214: add             x9, x9, HEAP, lsl #32
    // 0xb9b218: cmp             w9, NULL
    // 0xb9b21c: b.eq            #0xb9b244
    // 0xb9b220: LoadField: r2 = r1->field_3f
    //     0xb9b220: ldur            w2, [x1, #0x3f]
    // 0xb9b224: DecompressPointer r2
    //     0xb9b224: add             x2, x2, HEAP, lsl #32
    // 0xb9b228: LoadField: r9 = r2->field_b
    //     0xb9b228: ldur            w9, [x2, #0xb]
    // 0xb9b22c: cbz             w9, #0xb9b238
    // 0xb9b230: r2 = false
    //     0xb9b230: add             x2, NULL, #0x30  ; false
    // 0xb9b234: b               #0xb9b23c
    // 0xb9b238: r2 = true
    //     0xb9b238: add             x2, NULL, #0x20  ; true
    // 0xb9b23c: mov             x9, x2
    // 0xb9b240: b               #0xb9b248
    // 0xb9b244: r9 = false
    //     0xb9b244: add             x9, NULL, #0x30  ; false
    // 0xb9b248: stur            x9, [fp, #-0x20]
    // 0xb9b24c: LoadField: r2 = r1->field_3f
    //     0xb9b24c: ldur            w2, [x1, #0x3f]
    // 0xb9b250: DecompressPointer r2
    //     0xb9b250: add             x2, x2, HEAP, lsl #32
    // 0xb9b254: LoadField: r1 = r2->field_b
    //     0xb9b254: ldur            w1, [x2, #0xb]
    // 0xb9b258: cbnz            w1, #0xb9b33c
    // 0xb9b25c: ldur            x10, [fp, #-0x18]
    // 0xb9b260: ldur            x11, [fp, #-0x10]
    // 0xb9b264: r1 = Null
    //     0xb9b264: mov             x1, NULL
    // 0xb9b268: r2 = 6
    //     0xb9b268: movz            x2, #0x6
    // 0xb9b26c: r0 = AllocateArray()
    //     0xb9b26c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9b270: r16 = "No results found for \""
    //     0xb9b270: add             x16, PP, #0x51, lsl #12  ; [pp+0x51c30] "No results found for \""
    //     0xb9b274: ldr             x16, [x16, #0xc30]
    // 0xb9b278: StoreField: r0->field_f = r16
    //     0xb9b278: stur            w16, [x0, #0xf]
    // 0xb9b27c: ldur            x1, [fp, #-0x10]
    // 0xb9b280: LoadField: r2 = r1->field_27
    //     0xb9b280: ldur            w2, [x1, #0x27]
    // 0xb9b284: DecompressPointer r2
    //     0xb9b284: add             x2, x2, HEAP, lsl #32
    // 0xb9b288: LoadField: r1 = r2->field_7
    //     0xb9b288: ldur            w1, [x2, #7]
    // 0xb9b28c: DecompressPointer r1
    //     0xb9b28c: add             x1, x1, HEAP, lsl #32
    // 0xb9b290: StoreField: r0->field_13 = r1
    //     0xb9b290: stur            w1, [x0, #0x13]
    // 0xb9b294: r16 = "\""
    //     0xb9b294: add             x16, PP, #8, lsl #12  ; [pp+0x8550] "\""
    //     0xb9b298: ldr             x16, [x16, #0x550]
    // 0xb9b29c: ArrayStore: r0[0] = r16  ; List_4
    //     0xb9b29c: stur            w16, [x0, #0x17]
    // 0xb9b2a0: str             x0, [SP]
    // 0xb9b2a4: r0 = _interpolate()
    //     0xb9b2a4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb9b2a8: ldur            x2, [fp, #-0x18]
    // 0xb9b2ac: stur            x0, [fp, #-0x10]
    // 0xb9b2b0: LoadField: r1 = r2->field_13
    //     0xb9b2b0: ldur            w1, [x2, #0x13]
    // 0xb9b2b4: DecompressPointer r1
    //     0xb9b2b4: add             x1, x1, HEAP, lsl #32
    // 0xb9b2b8: r0 = of()
    //     0xb9b2b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9b2bc: LoadField: r1 = r0->field_87
    //     0xb9b2bc: ldur            w1, [x0, #0x87]
    // 0xb9b2c0: DecompressPointer r1
    //     0xb9b2c0: add             x1, x1, HEAP, lsl #32
    // 0xb9b2c4: LoadField: r0 = r1->field_7
    //     0xb9b2c4: ldur            w0, [x1, #7]
    // 0xb9b2c8: DecompressPointer r0
    //     0xb9b2c8: add             x0, x0, HEAP, lsl #32
    // 0xb9b2cc: stur            x0, [fp, #-0x30]
    // 0xb9b2d0: r1 = Instance_Color
    //     0xb9b2d0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9b2d4: d0 = 0.400000
    //     0xb9b2d4: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb9b2d8: r0 = withOpacity()
    //     0xb9b2d8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb9b2dc: r16 = 16.000000
    //     0xb9b2dc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb9b2e0: ldr             x16, [x16, #0x188]
    // 0xb9b2e4: stp             x16, x0, [SP]
    // 0xb9b2e8: ldur            x1, [fp, #-0x30]
    // 0xb9b2ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb9b2ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb9b2f0: ldr             x4, [x4, #0x9b8]
    // 0xb9b2f4: r0 = copyWith()
    //     0xb9b2f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9b2f8: stur            x0, [fp, #-0x30]
    // 0xb9b2fc: r0 = Text()
    //     0xb9b2fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9b300: mov             x1, x0
    // 0xb9b304: ldur            x0, [fp, #-0x10]
    // 0xb9b308: stur            x1, [fp, #-0x38]
    // 0xb9b30c: StoreField: r1->field_b = r0
    //     0xb9b30c: stur            w0, [x1, #0xb]
    // 0xb9b310: ldur            x0, [fp, #-0x30]
    // 0xb9b314: StoreField: r1->field_13 = r0
    //     0xb9b314: stur            w0, [x1, #0x13]
    // 0xb9b318: r0 = Padding()
    //     0xb9b318: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9b31c: mov             x1, x0
    // 0xb9b320: r0 = Instance_EdgeInsets
    //     0xb9b320: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb9b324: ldr             x0, [x0, #0x980]
    // 0xb9b328: StoreField: r1->field_f = r0
    //     0xb9b328: stur            w0, [x1, #0xf]
    // 0xb9b32c: ldur            x0, [fp, #-0x38]
    // 0xb9b330: StoreField: r1->field_b = r0
    //     0xb9b330: stur            w0, [x1, #0xb]
    // 0xb9b334: mov             x3, x1
    // 0xb9b338: b               #0xb9b354
    // 0xb9b33c: r0 = Container()
    //     0xb9b33c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb9b340: mov             x1, x0
    // 0xb9b344: stur            x0, [fp, #-0x10]
    // 0xb9b348: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb9b348: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb9b34c: r0 = Container()
    //     0xb9b34c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb9b350: ldur            x3, [fp, #-0x10]
    // 0xb9b354: ldur            x0, [fp, #-8]
    // 0xb9b358: ldur            x2, [fp, #-0x18]
    // 0xb9b35c: ldur            x1, [fp, #-0x20]
    // 0xb9b360: stur            x3, [fp, #-0x10]
    // 0xb9b364: r0 = Visibility()
    //     0xb9b364: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb9b368: mov             x2, x0
    // 0xb9b36c: ldur            x0, [fp, #-0x10]
    // 0xb9b370: stur            x2, [fp, #-0x30]
    // 0xb9b374: StoreField: r2->field_b = r0
    //     0xb9b374: stur            w0, [x2, #0xb]
    // 0xb9b378: r0 = Instance_SizedBox
    //     0xb9b378: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb9b37c: StoreField: r2->field_f = r0
    //     0xb9b37c: stur            w0, [x2, #0xf]
    // 0xb9b380: ldur            x1, [fp, #-0x20]
    // 0xb9b384: StoreField: r2->field_13 = r1
    //     0xb9b384: stur            w1, [x2, #0x13]
    // 0xb9b388: r3 = false
    //     0xb9b388: add             x3, NULL, #0x30  ; false
    // 0xb9b38c: ArrayStore: r2[0] = r3  ; List_4
    //     0xb9b38c: stur            w3, [x2, #0x17]
    // 0xb9b390: StoreField: r2->field_1b = r3
    //     0xb9b390: stur            w3, [x2, #0x1b]
    // 0xb9b394: StoreField: r2->field_1f = r3
    //     0xb9b394: stur            w3, [x2, #0x1f]
    // 0xb9b398: StoreField: r2->field_23 = r3
    //     0xb9b398: stur            w3, [x2, #0x23]
    // 0xb9b39c: StoreField: r2->field_27 = r3
    //     0xb9b39c: stur            w3, [x2, #0x27]
    // 0xb9b3a0: StoreField: r2->field_2b = r3
    //     0xb9b3a0: stur            w3, [x2, #0x2b]
    // 0xb9b3a4: ldur            x4, [fp, #-8]
    // 0xb9b3a8: LoadField: r1 = r4->field_b
    //     0xb9b3a8: ldur            w1, [x4, #0xb]
    // 0xb9b3ac: DecompressPointer r1
    //     0xb9b3ac: add             x1, x1, HEAP, lsl #32
    // 0xb9b3b0: cmp             w1, NULL
    // 0xb9b3b4: b.eq            #0xb9bf0c
    // 0xb9b3b8: LoadField: r5 = r1->field_3f
    //     0xb9b3b8: ldur            w5, [x1, #0x3f]
    // 0xb9b3bc: DecompressPointer r5
    //     0xb9b3bc: add             x5, x5, HEAP, lsl #32
    // 0xb9b3c0: LoadField: r1 = r5->field_b
    //     0xb9b3c0: ldur            w1, [x5, #0xb]
    // 0xb9b3c4: cbnz            w1, #0xb9b3d0
    // 0xb9b3c8: r5 = false
    //     0xb9b3c8: add             x5, NULL, #0x30  ; false
    // 0xb9b3cc: b               #0xb9b3d4
    // 0xb9b3d0: r5 = true
    //     0xb9b3d0: add             x5, NULL, #0x20  ; true
    // 0xb9b3d4: ldur            x6, [fp, #-0x18]
    // 0xb9b3d8: stur            x5, [fp, #-0x10]
    // 0xb9b3dc: LoadField: r1 = r6->field_13
    //     0xb9b3dc: ldur            w1, [x6, #0x13]
    // 0xb9b3e0: DecompressPointer r1
    //     0xb9b3e0: add             x1, x1, HEAP, lsl #32
    // 0xb9b3e4: r0 = of()
    //     0xb9b3e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9b3e8: LoadField: r1 = r0->field_5b
    //     0xb9b3e8: ldur            w1, [x0, #0x5b]
    // 0xb9b3ec: DecompressPointer r1
    //     0xb9b3ec: add             x1, x1, HEAP, lsl #32
    // 0xb9b3f0: stur            x1, [fp, #-0x20]
    // 0xb9b3f4: r0 = ColorFilter()
    //     0xb9b3f4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb9b3f8: mov             x1, x0
    // 0xb9b3fc: ldur            x0, [fp, #-0x20]
    // 0xb9b400: stur            x1, [fp, #-0x38]
    // 0xb9b404: StoreField: r1->field_7 = r0
    //     0xb9b404: stur            w0, [x1, #7]
    // 0xb9b408: r0 = Instance_BlendMode
    //     0xb9b408: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb9b40c: ldr             x0, [x0, #0xb30]
    // 0xb9b410: StoreField: r1->field_b = r0
    //     0xb9b410: stur            w0, [x1, #0xb]
    // 0xb9b414: r0 = 1
    //     0xb9b414: movz            x0, #0x1
    // 0xb9b418: StoreField: r1->field_13 = r0
    //     0xb9b418: stur            x0, [x1, #0x13]
    // 0xb9b41c: r0 = SvgPicture()
    //     0xb9b41c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb9b420: stur            x0, [fp, #-0x20]
    // 0xb9b424: ldur            x16, [fp, #-0x38]
    // 0xb9b428: str             x16, [SP]
    // 0xb9b42c: mov             x1, x0
    // 0xb9b430: r2 = "assets/images/filter.svg"
    //     0xb9b430: add             x2, PP, #0x51, lsl #12  ; [pp+0x51c38] "assets/images/filter.svg"
    //     0xb9b434: ldr             x2, [x2, #0xc38]
    // 0xb9b438: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb9b438: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb9b43c: ldr             x4, [x4, #0xa38]
    // 0xb9b440: r0 = SvgPicture.asset()
    //     0xb9b440: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb9b444: ldur            x0, [fp, #-8]
    // 0xb9b448: LoadField: r1 = r0->field_b
    //     0xb9b448: ldur            w1, [x0, #0xb]
    // 0xb9b44c: DecompressPointer r1
    //     0xb9b44c: add             x1, x1, HEAP, lsl #32
    // 0xb9b450: cmp             w1, NULL
    // 0xb9b454: b.eq            #0xb9bf10
    // 0xb9b458: LoadField: r2 = r1->field_f
    //     0xb9b458: ldur            w2, [x1, #0xf]
    // 0xb9b45c: DecompressPointer r2
    //     0xb9b45c: add             x2, x2, HEAP, lsl #32
    // 0xb9b460: LoadField: r3 = r2->field_7
    //     0xb9b460: ldur            w3, [x2, #7]
    // 0xb9b464: cbz             w3, #0xb9b470
    // 0xb9b468: mov             x5, x2
    // 0xb9b46c: b               #0xb9b474
    // 0xb9b470: r5 = Null
    //     0xb9b470: mov             x5, NULL
    // 0xb9b474: stur            x5, [fp, #-0x40]
    // 0xb9b478: LoadField: r2 = r1->field_b
    //     0xb9b478: ldur            w2, [x1, #0xb]
    // 0xb9b47c: DecompressPointer r2
    //     0xb9b47c: add             x2, x2, HEAP, lsl #32
    // 0xb9b480: LoadField: r1 = r2->field_b
    //     0xb9b480: ldur            w1, [x2, #0xb]
    // 0xb9b484: DecompressPointer r1
    //     0xb9b484: add             x1, x1, HEAP, lsl #32
    // 0xb9b488: cmp             w1, NULL
    // 0xb9b48c: b.ne            #0xb9b498
    // 0xb9b490: r3 = Null
    //     0xb9b490: mov             x3, NULL
    // 0xb9b494: b               #0xb9b4e4
    // 0xb9b498: LoadField: r3 = r1->field_b
    //     0xb9b498: ldur            w3, [x1, #0xb]
    // 0xb9b49c: DecompressPointer r3
    //     0xb9b49c: add             x3, x3, HEAP, lsl #32
    // 0xb9b4a0: ldur            x2, [fp, #-0x18]
    // 0xb9b4a4: stur            x3, [fp, #-0x38]
    // 0xb9b4a8: r1 = Function '<anonymous closure>':.
    //     0xb9b4a8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54db0] AnonymousClosure: (0xaacbec), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xb9b4ac: ldr             x1, [x1, #0xdb0]
    // 0xb9b4b0: r0 = AllocateClosure()
    //     0xb9b4b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9b4b4: r16 = <DropdownMenuItem<String>>
    //     0xb9b4b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f918] TypeArguments: <DropdownMenuItem<String>>
    //     0xb9b4b8: ldr             x16, [x16, #0x918]
    // 0xb9b4bc: ldur            lr, [fp, #-0x38]
    // 0xb9b4c0: stp             lr, x16, [SP, #8]
    // 0xb9b4c4: str             x0, [SP]
    // 0xb9b4c8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb9b4c8: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb9b4cc: r0 = map()
    //     0xb9b4cc: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0xb9b4d0: mov             x1, x0
    // 0xb9b4d4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb9b4d4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb9b4d8: r0 = toList()
    //     0xb9b4d8: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xb9b4dc: mov             x3, x0
    // 0xb9b4e0: ldur            x0, [fp, #-8]
    // 0xb9b4e4: ldur            x2, [fp, #-0x18]
    // 0xb9b4e8: stur            x3, [fp, #-0x38]
    // 0xb9b4ec: r1 = Function '<anonymous closure>':.
    //     0xb9b4ec: add             x1, PP, #0x54, lsl #12  ; [pp+0x54db8] AnonymousClosure: (0xb9c758), in [package:customer_app/app/presentation/views/glass/search/widgets/search_view.dart] _SearchViewState::build (0xb9ad68)
    //     0xb9b4f0: ldr             x1, [x1, #0xdb8]
    // 0xb9b4f4: r0 = AllocateClosure()
    //     0xb9b4f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9b4f8: r1 = <String>
    //     0xb9b4f8: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb9b4fc: stur            x0, [fp, #-0x48]
    // 0xb9b500: r0 = DropdownButton()
    //     0xb9b500: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0xb9b504: stur            x0, [fp, #-0x50]
    // 0xb9b508: r16 = 0.000000
    //     0xb9b508: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb9b50c: str             x16, [SP]
    // 0xb9b510: mov             x1, x0
    // 0xb9b514: ldur            x2, [fp, #-0x38]
    // 0xb9b518: ldur            x3, [fp, #-0x48]
    // 0xb9b51c: ldur            x5, [fp, #-0x40]
    // 0xb9b520: r4 = const [0, 0x5, 0x1, 0x4, iconSize, 0x4, null]
    //     0xb9b520: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c0d8] List(7) [0, 0x5, 0x1, 0x4, "iconSize", 0x4, Null]
    //     0xb9b524: ldr             x4, [x4, #0xd8]
    // 0xb9b528: r0 = DropdownButton()
    //     0xb9b528: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0xb9b52c: r0 = DropdownButtonHideUnderline()
    //     0xb9b52c: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0xb9b530: mov             x1, x0
    // 0xb9b534: ldur            x0, [fp, #-0x50]
    // 0xb9b538: stur            x1, [fp, #-0x38]
    // 0xb9b53c: StoreField: r1->field_b = r0
    //     0xb9b53c: stur            w0, [x1, #0xb]
    // 0xb9b540: r0 = Padding()
    //     0xb9b540: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9b544: mov             x2, x0
    // 0xb9b548: r0 = Instance_EdgeInsets
    //     0xb9b548: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xb9b54c: ldr             x0, [x0, #0xc40]
    // 0xb9b550: stur            x2, [fp, #-0x40]
    // 0xb9b554: StoreField: r2->field_f = r0
    //     0xb9b554: stur            w0, [x2, #0xf]
    // 0xb9b558: ldur            x0, [fp, #-0x38]
    // 0xb9b55c: StoreField: r2->field_b = r0
    //     0xb9b55c: stur            w0, [x2, #0xb]
    // 0xb9b560: r1 = <FlexParentData>
    //     0xb9b560: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb9b564: ldr             x1, [x1, #0xe00]
    // 0xb9b568: r0 = Expanded()
    //     0xb9b568: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb9b56c: stur            x0, [fp, #-0x48]
    // 0xb9b570: StoreField: r0->field_13 = rZR
    //     0xb9b570: stur            xzr, [x0, #0x13]
    // 0xb9b574: r1 = Instance_FlexFit
    //     0xb9b574: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb9b578: ldr             x1, [x1, #0xe08]
    // 0xb9b57c: StoreField: r0->field_1b = r1
    //     0xb9b57c: stur            w1, [x0, #0x1b]
    // 0xb9b580: ldur            x1, [fp, #-0x40]
    // 0xb9b584: StoreField: r0->field_b = r1
    //     0xb9b584: stur            w1, [x0, #0xb]
    // 0xb9b588: ldur            x3, [fp, #-8]
    // 0xb9b58c: LoadField: r1 = r3->field_b
    //     0xb9b58c: ldur            w1, [x3, #0xb]
    // 0xb9b590: DecompressPointer r1
    //     0xb9b590: add             x1, x1, HEAP, lsl #32
    // 0xb9b594: cmp             w1, NULL
    // 0xb9b598: b.eq            #0xb9bf14
    // 0xb9b59c: LoadField: r2 = r1->field_3f
    //     0xb9b59c: ldur            w2, [x1, #0x3f]
    // 0xb9b5a0: DecompressPointer r2
    //     0xb9b5a0: add             x2, x2, HEAP, lsl #32
    // 0xb9b5a4: LoadField: r4 = r2->field_b
    //     0xb9b5a4: ldur            w4, [x2, #0xb]
    // 0xb9b5a8: cbnz            w4, #0xb9b5b4
    // 0xb9b5ac: r5 = false
    //     0xb9b5ac: add             x5, NULL, #0x30  ; false
    // 0xb9b5b0: b               #0xb9b5b8
    // 0xb9b5b4: r5 = true
    //     0xb9b5b4: add             x5, NULL, #0x20  ; true
    // 0xb9b5b8: stur            x5, [fp, #-0x40]
    // 0xb9b5bc: LoadField: r2 = r1->field_b
    //     0xb9b5bc: ldur            w2, [x1, #0xb]
    // 0xb9b5c0: DecompressPointer r2
    //     0xb9b5c0: add             x2, x2, HEAP, lsl #32
    // 0xb9b5c4: LoadField: r1 = r2->field_f
    //     0xb9b5c4: ldur            w1, [x2, #0xf]
    // 0xb9b5c8: DecompressPointer r1
    //     0xb9b5c8: add             x1, x1, HEAP, lsl #32
    // 0xb9b5cc: cmp             w1, NULL
    // 0xb9b5d0: b.ne            #0xb9b5dc
    // 0xb9b5d4: r10 = Null
    //     0xb9b5d4: mov             x10, NULL
    // 0xb9b5d8: b               #0xb9b5e8
    // 0xb9b5dc: LoadField: r2 = r1->field_13
    //     0xb9b5dc: ldur            w2, [x1, #0x13]
    // 0xb9b5e0: DecompressPointer r2
    //     0xb9b5e0: add             x2, x2, HEAP, lsl #32
    // 0xb9b5e4: mov             x10, x2
    // 0xb9b5e8: ldur            x8, [fp, #-0x18]
    // 0xb9b5ec: ldur            x9, [fp, #-0x28]
    // 0xb9b5f0: ldur            x6, [fp, #-0x30]
    // 0xb9b5f4: ldur            x4, [fp, #-0x20]
    // 0xb9b5f8: ldur            x7, [fp, #-0x10]
    // 0xb9b5fc: stur            x10, [fp, #-0x38]
    // 0xb9b600: r1 = Null
    //     0xb9b600: mov             x1, NULL
    // 0xb9b604: r2 = 4
    //     0xb9b604: movz            x2, #0x4
    // 0xb9b608: r0 = AllocateArray()
    //     0xb9b608: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9b60c: mov             x1, x0
    // 0xb9b610: ldur            x0, [fp, #-0x38]
    // 0xb9b614: StoreField: r1->field_f = r0
    //     0xb9b614: stur            w0, [x1, #0xf]
    // 0xb9b618: r16 = " items"
    //     0xb9b618: add             x16, PP, #0x51, lsl #12  ; [pp+0x51c50] " items"
    //     0xb9b61c: ldr             x16, [x16, #0xc50]
    // 0xb9b620: StoreField: r1->field_13 = r16
    //     0xb9b620: stur            w16, [x1, #0x13]
    // 0xb9b624: str             x1, [SP]
    // 0xb9b628: r0 = _interpolate()
    //     0xb9b628: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb9b62c: ldur            x2, [fp, #-0x18]
    // 0xb9b630: stur            x0, [fp, #-0x38]
    // 0xb9b634: LoadField: r1 = r2->field_13
    //     0xb9b634: ldur            w1, [x2, #0x13]
    // 0xb9b638: DecompressPointer r1
    //     0xb9b638: add             x1, x1, HEAP, lsl #32
    // 0xb9b63c: r0 = of()
    //     0xb9b63c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9b640: LoadField: r1 = r0->field_87
    //     0xb9b640: ldur            w1, [x0, #0x87]
    // 0xb9b644: DecompressPointer r1
    //     0xb9b644: add             x1, x1, HEAP, lsl #32
    // 0xb9b648: LoadField: r0 = r1->field_2b
    //     0xb9b648: ldur            w0, [x1, #0x2b]
    // 0xb9b64c: DecompressPointer r0
    //     0xb9b64c: add             x0, x0, HEAP, lsl #32
    // 0xb9b650: r16 = 14.000000
    //     0xb9b650: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb9b654: ldr             x16, [x16, #0x1d8]
    // 0xb9b658: r30 = Instance_Color
    //     0xb9b658: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9b65c: stp             lr, x16, [SP]
    // 0xb9b660: mov             x1, x0
    // 0xb9b664: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb9b664: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9b668: ldr             x4, [x4, #0xaa0]
    // 0xb9b66c: r0 = copyWith()
    //     0xb9b66c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9b670: stur            x0, [fp, #-0x50]
    // 0xb9b674: r0 = Text()
    //     0xb9b674: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9b678: mov             x1, x0
    // 0xb9b67c: ldur            x0, [fp, #-0x38]
    // 0xb9b680: stur            x1, [fp, #-0x58]
    // 0xb9b684: StoreField: r1->field_b = r0
    //     0xb9b684: stur            w0, [x1, #0xb]
    // 0xb9b688: ldur            x0, [fp, #-0x50]
    // 0xb9b68c: StoreField: r1->field_13 = r0
    //     0xb9b68c: stur            w0, [x1, #0x13]
    // 0xb9b690: r0 = Visibility()
    //     0xb9b690: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb9b694: mov             x1, x0
    // 0xb9b698: ldur            x0, [fp, #-0x58]
    // 0xb9b69c: stur            x1, [fp, #-0x38]
    // 0xb9b6a0: StoreField: r1->field_b = r0
    //     0xb9b6a0: stur            w0, [x1, #0xb]
    // 0xb9b6a4: r0 = Instance_SizedBox
    //     0xb9b6a4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb9b6a8: StoreField: r1->field_f = r0
    //     0xb9b6a8: stur            w0, [x1, #0xf]
    // 0xb9b6ac: ldur            x2, [fp, #-0x40]
    // 0xb9b6b0: StoreField: r1->field_13 = r2
    //     0xb9b6b0: stur            w2, [x1, #0x13]
    // 0xb9b6b4: r2 = false
    //     0xb9b6b4: add             x2, NULL, #0x30  ; false
    // 0xb9b6b8: ArrayStore: r1[0] = r2  ; List_4
    //     0xb9b6b8: stur            w2, [x1, #0x17]
    // 0xb9b6bc: StoreField: r1->field_1b = r2
    //     0xb9b6bc: stur            w2, [x1, #0x1b]
    // 0xb9b6c0: StoreField: r1->field_1f = r2
    //     0xb9b6c0: stur            w2, [x1, #0x1f]
    // 0xb9b6c4: StoreField: r1->field_23 = r2
    //     0xb9b6c4: stur            w2, [x1, #0x23]
    // 0xb9b6c8: StoreField: r1->field_27 = r2
    //     0xb9b6c8: stur            w2, [x1, #0x27]
    // 0xb9b6cc: StoreField: r1->field_2b = r2
    //     0xb9b6cc: stur            w2, [x1, #0x2b]
    // 0xb9b6d0: r0 = Padding()
    //     0xb9b6d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9b6d4: mov             x3, x0
    // 0xb9b6d8: r0 = Instance_EdgeInsets
    //     0xb9b6d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xb9b6dc: ldr             x0, [x0, #0xd48]
    // 0xb9b6e0: stur            x3, [fp, #-0x40]
    // 0xb9b6e4: StoreField: r3->field_f = r0
    //     0xb9b6e4: stur            w0, [x3, #0xf]
    // 0xb9b6e8: ldur            x0, [fp, #-0x38]
    // 0xb9b6ec: StoreField: r3->field_b = r0
    //     0xb9b6ec: stur            w0, [x3, #0xb]
    // 0xb9b6f0: r1 = Null
    //     0xb9b6f0: mov             x1, NULL
    // 0xb9b6f4: r2 = 8
    //     0xb9b6f4: movz            x2, #0x8
    // 0xb9b6f8: r0 = AllocateArray()
    //     0xb9b6f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9b6fc: mov             x2, x0
    // 0xb9b700: ldur            x0, [fp, #-0x20]
    // 0xb9b704: stur            x2, [fp, #-0x38]
    // 0xb9b708: StoreField: r2->field_f = r0
    //     0xb9b708: stur            w0, [x2, #0xf]
    // 0xb9b70c: ldur            x0, [fp, #-0x48]
    // 0xb9b710: StoreField: r2->field_13 = r0
    //     0xb9b710: stur            w0, [x2, #0x13]
    // 0xb9b714: r16 = Instance_Spacer
    //     0xb9b714: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb9b718: ldr             x16, [x16, #0xf0]
    // 0xb9b71c: ArrayStore: r2[0] = r16  ; List_4
    //     0xb9b71c: stur            w16, [x2, #0x17]
    // 0xb9b720: ldur            x0, [fp, #-0x40]
    // 0xb9b724: StoreField: r2->field_1b = r0
    //     0xb9b724: stur            w0, [x2, #0x1b]
    // 0xb9b728: r1 = <Widget>
    //     0xb9b728: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9b72c: r0 = AllocateGrowableArray()
    //     0xb9b72c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9b730: mov             x1, x0
    // 0xb9b734: ldur            x0, [fp, #-0x38]
    // 0xb9b738: stur            x1, [fp, #-0x20]
    // 0xb9b73c: StoreField: r1->field_f = r0
    //     0xb9b73c: stur            w0, [x1, #0xf]
    // 0xb9b740: r0 = 8
    //     0xb9b740: movz            x0, #0x8
    // 0xb9b744: StoreField: r1->field_b = r0
    //     0xb9b744: stur            w0, [x1, #0xb]
    // 0xb9b748: r0 = Row()
    //     0xb9b748: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb9b74c: mov             x1, x0
    // 0xb9b750: r0 = Instance_Axis
    //     0xb9b750: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb9b754: stur            x1, [fp, #-0x38]
    // 0xb9b758: StoreField: r1->field_f = r0
    //     0xb9b758: stur            w0, [x1, #0xf]
    // 0xb9b75c: r0 = Instance_MainAxisAlignment
    //     0xb9b75c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9b760: ldr             x0, [x0, #0xa08]
    // 0xb9b764: StoreField: r1->field_13 = r0
    //     0xb9b764: stur            w0, [x1, #0x13]
    // 0xb9b768: r2 = Instance_MainAxisSize
    //     0xb9b768: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9b76c: ldr             x2, [x2, #0xa10]
    // 0xb9b770: ArrayStore: r1[0] = r2  ; List_4
    //     0xb9b770: stur            w2, [x1, #0x17]
    // 0xb9b774: r3 = Instance_CrossAxisAlignment
    //     0xb9b774: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9b778: ldr             x3, [x3, #0xa18]
    // 0xb9b77c: StoreField: r1->field_1b = r3
    //     0xb9b77c: stur            w3, [x1, #0x1b]
    // 0xb9b780: r4 = Instance_VerticalDirection
    //     0xb9b780: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9b784: ldr             x4, [x4, #0xa20]
    // 0xb9b788: StoreField: r1->field_23 = r4
    //     0xb9b788: stur            w4, [x1, #0x23]
    // 0xb9b78c: r5 = Instance_Clip
    //     0xb9b78c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9b790: ldr             x5, [x5, #0x38]
    // 0xb9b794: StoreField: r1->field_2b = r5
    //     0xb9b794: stur            w5, [x1, #0x2b]
    // 0xb9b798: StoreField: r1->field_2f = rZR
    //     0xb9b798: stur            xzr, [x1, #0x2f]
    // 0xb9b79c: ldur            x6, [fp, #-0x20]
    // 0xb9b7a0: StoreField: r1->field_b = r6
    //     0xb9b7a0: stur            w6, [x1, #0xb]
    // 0xb9b7a4: r0 = Padding()
    //     0xb9b7a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9b7a8: mov             x1, x0
    // 0xb9b7ac: r0 = Instance_EdgeInsets
    //     0xb9b7ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb9b7b0: ldr             x0, [x0, #0x668]
    // 0xb9b7b4: stur            x1, [fp, #-0x20]
    // 0xb9b7b8: StoreField: r1->field_f = r0
    //     0xb9b7b8: stur            w0, [x1, #0xf]
    // 0xb9b7bc: ldur            x0, [fp, #-0x38]
    // 0xb9b7c0: StoreField: r1->field_b = r0
    //     0xb9b7c0: stur            w0, [x1, #0xb]
    // 0xb9b7c4: r0 = Visibility()
    //     0xb9b7c4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb9b7c8: mov             x2, x0
    // 0xb9b7cc: ldur            x0, [fp, #-0x20]
    // 0xb9b7d0: stur            x2, [fp, #-0x38]
    // 0xb9b7d4: StoreField: r2->field_b = r0
    //     0xb9b7d4: stur            w0, [x2, #0xb]
    // 0xb9b7d8: r0 = Instance_SizedBox
    //     0xb9b7d8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb9b7dc: StoreField: r2->field_f = r0
    //     0xb9b7dc: stur            w0, [x2, #0xf]
    // 0xb9b7e0: ldur            x1, [fp, #-0x10]
    // 0xb9b7e4: StoreField: r2->field_13 = r1
    //     0xb9b7e4: stur            w1, [x2, #0x13]
    // 0xb9b7e8: r3 = false
    //     0xb9b7e8: add             x3, NULL, #0x30  ; false
    // 0xb9b7ec: ArrayStore: r2[0] = r3  ; List_4
    //     0xb9b7ec: stur            w3, [x2, #0x17]
    // 0xb9b7f0: StoreField: r2->field_1b = r3
    //     0xb9b7f0: stur            w3, [x2, #0x1b]
    // 0xb9b7f4: StoreField: r2->field_1f = r3
    //     0xb9b7f4: stur            w3, [x2, #0x1f]
    // 0xb9b7f8: StoreField: r2->field_23 = r3
    //     0xb9b7f8: stur            w3, [x2, #0x23]
    // 0xb9b7fc: StoreField: r2->field_27 = r3
    //     0xb9b7fc: stur            w3, [x2, #0x27]
    // 0xb9b800: StoreField: r2->field_2b = r3
    //     0xb9b800: stur            w3, [x2, #0x2b]
    // 0xb9b804: ldur            x4, [fp, #-8]
    // 0xb9b808: LoadField: r1 = r4->field_b
    //     0xb9b808: ldur            w1, [x4, #0xb]
    // 0xb9b80c: DecompressPointer r1
    //     0xb9b80c: add             x1, x1, HEAP, lsl #32
    // 0xb9b810: cmp             w1, NULL
    // 0xb9b814: b.eq            #0xb9bf18
    // 0xb9b818: LoadField: r5 = r1->field_3f
    //     0xb9b818: ldur            w5, [x1, #0x3f]
    // 0xb9b81c: DecompressPointer r5
    //     0xb9b81c: add             x5, x5, HEAP, lsl #32
    // 0xb9b820: LoadField: r1 = r5->field_b
    //     0xb9b820: ldur            w1, [x5, #0xb]
    // 0xb9b824: cbnz            w1, #0xb9b830
    // 0xb9b828: r5 = false
    //     0xb9b828: add             x5, NULL, #0x30  ; false
    // 0xb9b82c: b               #0xb9b834
    // 0xb9b830: r5 = true
    //     0xb9b830: add             x5, NULL, #0x20  ; true
    // 0xb9b834: stur            x5, [fp, #-0x10]
    // 0xb9b838: r1 = Instance_Color
    //     0xb9b838: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9b83c: d0 = 0.100000
    //     0xb9b83c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb9b840: r0 = withOpacity()
    //     0xb9b840: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb9b844: stur            x0, [fp, #-0x20]
    // 0xb9b848: r0 = Divider()
    //     0xb9b848: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xb9b84c: mov             x1, x0
    // 0xb9b850: r0 = 1.000000
    //     0xb9b850: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb9b854: stur            x1, [fp, #-0x40]
    // 0xb9b858: StoreField: r1->field_f = r0
    //     0xb9b858: stur            w0, [x1, #0xf]
    // 0xb9b85c: ldur            x0, [fp, #-0x20]
    // 0xb9b860: StoreField: r1->field_1f = r0
    //     0xb9b860: stur            w0, [x1, #0x1f]
    // 0xb9b864: r0 = Visibility()
    //     0xb9b864: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb9b868: mov             x1, x0
    // 0xb9b86c: ldur            x0, [fp, #-0x40]
    // 0xb9b870: stur            x1, [fp, #-0x48]
    // 0xb9b874: StoreField: r1->field_b = r0
    //     0xb9b874: stur            w0, [x1, #0xb]
    // 0xb9b878: r0 = Instance_SizedBox
    //     0xb9b878: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb9b87c: StoreField: r1->field_f = r0
    //     0xb9b87c: stur            w0, [x1, #0xf]
    // 0xb9b880: ldur            x2, [fp, #-0x10]
    // 0xb9b884: StoreField: r1->field_13 = r2
    //     0xb9b884: stur            w2, [x1, #0x13]
    // 0xb9b888: r2 = false
    //     0xb9b888: add             x2, NULL, #0x30  ; false
    // 0xb9b88c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb9b88c: stur            w2, [x1, #0x17]
    // 0xb9b890: StoreField: r1->field_1b = r2
    //     0xb9b890: stur            w2, [x1, #0x1b]
    // 0xb9b894: StoreField: r1->field_1f = r2
    //     0xb9b894: stur            w2, [x1, #0x1f]
    // 0xb9b898: StoreField: r1->field_23 = r2
    //     0xb9b898: stur            w2, [x1, #0x23]
    // 0xb9b89c: StoreField: r1->field_27 = r2
    //     0xb9b89c: stur            w2, [x1, #0x27]
    // 0xb9b8a0: StoreField: r1->field_2b = r2
    //     0xb9b8a0: stur            w2, [x1, #0x2b]
    // 0xb9b8a4: ldur            x3, [fp, #-8]
    // 0xb9b8a8: LoadField: r4 = r3->field_b
    //     0xb9b8a8: ldur            w4, [x3, #0xb]
    // 0xb9b8ac: DecompressPointer r4
    //     0xb9b8ac: add             x4, x4, HEAP, lsl #32
    // 0xb9b8b0: cmp             w4, NULL
    // 0xb9b8b4: b.eq            #0xb9bf1c
    // 0xb9b8b8: LoadField: r5 = r4->field_3f
    //     0xb9b8b8: ldur            w5, [x4, #0x3f]
    // 0xb9b8bc: DecompressPointer r5
    //     0xb9b8bc: add             x5, x5, HEAP, lsl #32
    // 0xb9b8c0: stur            x5, [fp, #-0x40]
    // 0xb9b8c4: LoadField: r6 = r5->field_b
    //     0xb9b8c4: ldur            w6, [x5, #0xb]
    // 0xb9b8c8: cbnz            w6, #0xb9b8d4
    // 0xb9b8cc: r7 = false
    //     0xb9b8cc: add             x7, NULL, #0x30  ; false
    // 0xb9b8d0: b               #0xb9b8d8
    // 0xb9b8d4: r7 = true
    //     0xb9b8d4: add             x7, NULL, #0x20  ; true
    // 0xb9b8d8: stur            x7, [fp, #-0x20]
    // 0xb9b8dc: LoadField: r6 = r4->field_13
    //     0xb9b8dc: ldur            w6, [x4, #0x13]
    // 0xb9b8e0: DecompressPointer r6
    //     0xb9b8e0: add             x6, x6, HEAP, lsl #32
    // 0xb9b8e4: stur            x6, [fp, #-0x10]
    // 0xb9b8e8: r0 = CustomStyle()
    //     0xb9b8e8: bl              #0x910168  ; AllocateCustomStyleStub -> CustomStyle (size=0x10)
    // 0xb9b8ec: mov             x1, x0
    // 0xb9b8f0: r0 = Instance_TitleAlignment
    //     0xb9b8f0: add             x0, PP, #0x24, lsl #12  ; [pp+0x24510] Obj!TitleAlignment@d75601
    //     0xb9b8f4: ldr             x0, [x0, #0x510]
    // 0xb9b8f8: stur            x1, [fp, #-0x50]
    // 0xb9b8fc: StoreField: r1->field_7 = r0
    //     0xb9b8fc: stur            w0, [x1, #7]
    // 0xb9b900: r0 = ProductGridItemView()
    //     0xb9b900: bl              #0xb9bf44  ; AllocateProductGridItemViewStub -> ProductGridItemView (size=0x5c)
    // 0xb9b904: mov             x1, x0
    // 0xb9b908: ldur            x0, [fp, #-0x40]
    // 0xb9b90c: stur            x1, [fp, #-0x58]
    // 0xb9b910: StoreField: r1->field_b = r0
    //     0xb9b910: stur            w0, [x1, #0xb]
    // 0xb9b914: r0 = ""
    //     0xb9b914: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb9b918: StoreField: r1->field_f = r0
    //     0xb9b918: stur            w0, [x1, #0xf]
    // 0xb9b91c: r0 = false
    //     0xb9b91c: add             x0, NULL, #0x30  ; false
    // 0xb9b920: StoreField: r1->field_13 = r0
    //     0xb9b920: stur            w0, [x1, #0x13]
    // 0xb9b924: r0 = ViewAll()
    //     0xb9b924: bl              #0x90ff98  ; AllocateViewAllStub -> ViewAll (size=0x10)
    // 0xb9b928: mov             x1, x0
    // 0xb9b92c: ldur            x0, [fp, #-0x58]
    // 0xb9b930: ArrayStore: r0[0] = r1  ; List_4
    //     0xb9b930: stur            w1, [x0, #0x17]
    // 0xb9b934: r3 = false
    //     0xb9b934: add             x3, NULL, #0x30  ; false
    // 0xb9b938: StoreField: r0->field_1b = r3
    //     0xb9b938: stur            w3, [x0, #0x1b]
    // 0xb9b93c: ldur            x1, [fp, #-0x10]
    // 0xb9b940: StoreField: r0->field_1f = r1
    //     0xb9b940: stur            w1, [x0, #0x1f]
    // 0xb9b944: ldur            x1, [fp, #-0x50]
    // 0xb9b948: StoreField: r0->field_23 = r1
    //     0xb9b948: stur            w1, [x0, #0x23]
    // 0xb9b94c: r4 = "search_page"
    //     0xb9b94c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ee58] "search_page"
    //     0xb9b950: ldr             x4, [x4, #0xe58]
    // 0xb9b954: StoreField: r0->field_37 = r4
    //     0xb9b954: stur            w4, [x0, #0x37]
    // 0xb9b958: StoreField: r0->field_3b = r4
    //     0xb9b958: stur            w4, [x0, #0x3b]
    // 0xb9b95c: ldur            x2, [fp, #-0x18]
    // 0xb9b960: r1 = Function '<anonymous closure>':.
    //     0xb9b960: add             x1, PP, #0x54, lsl #12  ; [pp+0x54dc0] AnonymousClosure: (0xb9c6cc), in [package:customer_app/app/presentation/views/glass/search/widgets/search_view.dart] _SearchViewState::build (0xb9ad68)
    //     0xb9b964: ldr             x1, [x1, #0xdc0]
    // 0xb9b968: r0 = AllocateClosure()
    //     0xb9b968: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9b96c: mov             x1, x0
    // 0xb9b970: ldur            x0, [fp, #-0x58]
    // 0xb9b974: StoreField: r0->field_47 = r1
    //     0xb9b974: stur            w1, [x0, #0x47]
    // 0xb9b978: ldur            x2, [fp, #-0x18]
    // 0xb9b97c: r1 = Function '<anonymous closure>':.
    //     0xb9b97c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54dc8] AnonymousClosure: (0xb9c634), in [package:customer_app/app/presentation/views/glass/search/widgets/search_view.dart] _SearchViewState::build (0xb9ad68)
    //     0xb9b980: ldr             x1, [x1, #0xdc8]
    // 0xb9b984: r0 = AllocateClosure()
    //     0xb9b984: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9b988: mov             x1, x0
    // 0xb9b98c: ldur            x0, [fp, #-0x58]
    // 0xb9b990: StoreField: r0->field_4f = r1
    //     0xb9b990: stur            w1, [x0, #0x4f]
    // 0xb9b994: ldur            x2, [fp, #-0x18]
    // 0xb9b998: r1 = Function '<anonymous closure>':.
    //     0xb9b998: add             x1, PP, #0x54, lsl #12  ; [pp+0x54dd0] AnonymousClosure: (0xb9c588), in [package:customer_app/app/presentation/views/glass/search/widgets/search_view.dart] _SearchViewState::build (0xb9ad68)
    //     0xb9b99c: ldr             x1, [x1, #0xdd0]
    // 0xb9b9a0: r0 = AllocateClosure()
    //     0xb9b9a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9b9a4: mov             x1, x0
    // 0xb9b9a8: ldur            x0, [fp, #-0x58]
    // 0xb9b9ac: StoreField: r0->field_53 = r1
    //     0xb9b9ac: stur            w1, [x0, #0x53]
    // 0xb9b9b0: r1 = "search_page"
    //     0xb9b9b0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ee58] "search_page"
    //     0xb9b9b4: ldr             x1, [x1, #0xe58]
    // 0xb9b9b8: StoreField: r0->field_3f = r1
    //     0xb9b9b8: stur            w1, [x0, #0x3f]
    // 0xb9b9bc: ldur            x2, [fp, #-0x18]
    // 0xb9b9c0: r1 = Function '<anonymous closure>':.
    //     0xb9b9c0: add             x1, PP, #0x54, lsl #12  ; [pp+0x54dd8] AnonymousClosure: (0xb9c4c8), in [package:customer_app/app/presentation/views/glass/search/widgets/search_view.dart] _SearchViewState::build (0xb9ad68)
    //     0xb9b9c4: ldr             x1, [x1, #0xdd8]
    // 0xb9b9c8: r0 = AllocateClosure()
    //     0xb9b9c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9b9cc: mov             x1, x0
    // 0xb9b9d0: ldur            x0, [fp, #-0x58]
    // 0xb9b9d4: StoreField: r0->field_4b = r1
    //     0xb9b9d4: stur            w1, [x0, #0x4b]
    // 0xb9b9d8: r1 = Function '<anonymous closure>':.
    //     0xb9b9d8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54de0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb9b9dc: ldr             x1, [x1, #0xde0]
    // 0xb9b9e0: r2 = Null
    //     0xb9b9e0: mov             x2, NULL
    // 0xb9b9e4: r0 = AllocateClosure()
    //     0xb9b9e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9b9e8: mov             x1, x0
    // 0xb9b9ec: ldur            x0, [fp, #-0x58]
    // 0xb9b9f0: StoreField: r0->field_57 = r1
    //     0xb9b9f0: stur            w1, [x0, #0x57]
    // 0xb9b9f4: r0 = Visibility()
    //     0xb9b9f4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb9b9f8: mov             x3, x0
    // 0xb9b9fc: ldur            x0, [fp, #-0x58]
    // 0xb9ba00: stur            x3, [fp, #-0x10]
    // 0xb9ba04: StoreField: r3->field_b = r0
    //     0xb9ba04: stur            w0, [x3, #0xb]
    // 0xb9ba08: r0 = Instance_SizedBox
    //     0xb9ba08: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb9ba0c: StoreField: r3->field_f = r0
    //     0xb9ba0c: stur            w0, [x3, #0xf]
    // 0xb9ba10: ldur            x0, [fp, #-0x20]
    // 0xb9ba14: StoreField: r3->field_13 = r0
    //     0xb9ba14: stur            w0, [x3, #0x13]
    // 0xb9ba18: r0 = false
    //     0xb9ba18: add             x0, NULL, #0x30  ; false
    // 0xb9ba1c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb9ba1c: stur            w0, [x3, #0x17]
    // 0xb9ba20: StoreField: r3->field_1b = r0
    //     0xb9ba20: stur            w0, [x3, #0x1b]
    // 0xb9ba24: StoreField: r3->field_1f = r0
    //     0xb9ba24: stur            w0, [x3, #0x1f]
    // 0xb9ba28: StoreField: r3->field_23 = r0
    //     0xb9ba28: stur            w0, [x3, #0x23]
    // 0xb9ba2c: StoreField: r3->field_27 = r0
    //     0xb9ba2c: stur            w0, [x3, #0x27]
    // 0xb9ba30: StoreField: r3->field_2b = r0
    //     0xb9ba30: stur            w0, [x3, #0x2b]
    // 0xb9ba34: r1 = Null
    //     0xb9ba34: mov             x1, NULL
    // 0xb9ba38: r2 = 12
    //     0xb9ba38: movz            x2, #0xc
    // 0xb9ba3c: r0 = AllocateArray()
    //     0xb9ba3c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9ba40: mov             x2, x0
    // 0xb9ba44: ldur            x0, [fp, #-0x28]
    // 0xb9ba48: stur            x2, [fp, #-0x20]
    // 0xb9ba4c: StoreField: r2->field_f = r0
    //     0xb9ba4c: stur            w0, [x2, #0xf]
    // 0xb9ba50: r16 = Instance_Divider
    //     0xb9ba50: add             x16, PP, #0x48, lsl #12  ; [pp+0x484d0] Obj!Divider@d66ca1
    //     0xb9ba54: ldr             x16, [x16, #0x4d0]
    // 0xb9ba58: StoreField: r2->field_13 = r16
    //     0xb9ba58: stur            w16, [x2, #0x13]
    // 0xb9ba5c: ldur            x0, [fp, #-0x30]
    // 0xb9ba60: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9ba60: stur            w0, [x2, #0x17]
    // 0xb9ba64: ldur            x0, [fp, #-0x38]
    // 0xb9ba68: StoreField: r2->field_1b = r0
    //     0xb9ba68: stur            w0, [x2, #0x1b]
    // 0xb9ba6c: ldur            x0, [fp, #-0x48]
    // 0xb9ba70: StoreField: r2->field_1f = r0
    //     0xb9ba70: stur            w0, [x2, #0x1f]
    // 0xb9ba74: ldur            x0, [fp, #-0x10]
    // 0xb9ba78: StoreField: r2->field_23 = r0
    //     0xb9ba78: stur            w0, [x2, #0x23]
    // 0xb9ba7c: r1 = <Widget>
    //     0xb9ba7c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9ba80: r0 = AllocateGrowableArray()
    //     0xb9ba80: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9ba84: mov             x1, x0
    // 0xb9ba88: ldur            x0, [fp, #-0x20]
    // 0xb9ba8c: stur            x1, [fp, #-0x10]
    // 0xb9ba90: StoreField: r1->field_f = r0
    //     0xb9ba90: stur            w0, [x1, #0xf]
    // 0xb9ba94: r0 = 12
    //     0xb9ba94: movz            x0, #0xc
    // 0xb9ba98: StoreField: r1->field_b = r0
    //     0xb9ba98: stur            w0, [x1, #0xb]
    // 0xb9ba9c: r0 = Column()
    //     0xb9ba9c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb9baa0: mov             x3, x0
    // 0xb9baa4: r0 = Instance_Axis
    //     0xb9baa4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb9baa8: stur            x3, [fp, #-0x20]
    // 0xb9baac: StoreField: r3->field_f = r0
    //     0xb9baac: stur            w0, [x3, #0xf]
    // 0xb9bab0: r4 = Instance_MainAxisAlignment
    //     0xb9bab0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9bab4: ldr             x4, [x4, #0xa08]
    // 0xb9bab8: StoreField: r3->field_13 = r4
    //     0xb9bab8: stur            w4, [x3, #0x13]
    // 0xb9babc: r5 = Instance_MainAxisSize
    //     0xb9babc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9bac0: ldr             x5, [x5, #0xa10]
    // 0xb9bac4: ArrayStore: r3[0] = r5  ; List_4
    //     0xb9bac4: stur            w5, [x3, #0x17]
    // 0xb9bac8: r1 = Instance_CrossAxisAlignment
    //     0xb9bac8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9bacc: ldr             x1, [x1, #0xa18]
    // 0xb9bad0: StoreField: r3->field_1b = r1
    //     0xb9bad0: stur            w1, [x3, #0x1b]
    // 0xb9bad4: r6 = Instance_VerticalDirection
    //     0xb9bad4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9bad8: ldr             x6, [x6, #0xa20]
    // 0xb9badc: StoreField: r3->field_23 = r6
    //     0xb9badc: stur            w6, [x3, #0x23]
    // 0xb9bae0: r7 = Instance_Clip
    //     0xb9bae0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9bae4: ldr             x7, [x7, #0x38]
    // 0xb9bae8: StoreField: r3->field_2b = r7
    //     0xb9bae8: stur            w7, [x3, #0x2b]
    // 0xb9baec: StoreField: r3->field_2f = rZR
    //     0xb9baec: stur            xzr, [x3, #0x2f]
    // 0xb9baf0: ldur            x1, [fp, #-0x10]
    // 0xb9baf4: StoreField: r3->field_b = r1
    //     0xb9baf4: stur            w1, [x3, #0xb]
    // 0xb9baf8: r1 = Null
    //     0xb9baf8: mov             x1, NULL
    // 0xb9bafc: r2 = 2
    //     0xb9bafc: movz            x2, #0x2
    // 0xb9bb00: r0 = AllocateArray()
    //     0xb9bb00: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9bb04: mov             x2, x0
    // 0xb9bb08: ldur            x0, [fp, #-0x20]
    // 0xb9bb0c: stur            x2, [fp, #-0x10]
    // 0xb9bb10: StoreField: r2->field_f = r0
    //     0xb9bb10: stur            w0, [x2, #0xf]
    // 0xb9bb14: r1 = <Widget>
    //     0xb9bb14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9bb18: r0 = AllocateGrowableArray()
    //     0xb9bb18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9bb1c: mov             x2, x0
    // 0xb9bb20: ldur            x0, [fp, #-0x10]
    // 0xb9bb24: stur            x2, [fp, #-0x20]
    // 0xb9bb28: StoreField: r2->field_f = r0
    //     0xb9bb28: stur            w0, [x2, #0xf]
    // 0xb9bb2c: r0 = 2
    //     0xb9bb2c: movz            x0, #0x2
    // 0xb9bb30: StoreField: r2->field_b = r0
    //     0xb9bb30: stur            w0, [x2, #0xb]
    // 0xb9bb34: ldur            x0, [fp, #-8]
    // 0xb9bb38: LoadField: r1 = r0->field_1f
    //     0xb9bb38: ldur            w1, [x0, #0x1f]
    // 0xb9bb3c: DecompressPointer r1
    //     0xb9bb3c: add             x1, x1, HEAP, lsl #32
    // 0xb9bb40: tbnz            w1, #4, #0xb9be80
    // 0xb9bb44: ldur            x3, [fp, #-0x18]
    // 0xb9bb48: LoadField: r1 = r3->field_13
    //     0xb9bb48: ldur            w1, [x3, #0x13]
    // 0xb9bb4c: DecompressPointer r1
    //     0xb9bb4c: add             x1, x1, HEAP, lsl #32
    // 0xb9bb50: r0 = of()
    //     0xb9bb50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9bb54: LoadField: r1 = r0->field_87
    //     0xb9bb54: ldur            w1, [x0, #0x87]
    // 0xb9bb58: DecompressPointer r1
    //     0xb9bb58: add             x1, x1, HEAP, lsl #32
    // 0xb9bb5c: LoadField: r0 = r1->field_2b
    //     0xb9bb5c: ldur            w0, [x1, #0x2b]
    // 0xb9bb60: DecompressPointer r0
    //     0xb9bb60: add             x0, x0, HEAP, lsl #32
    // 0xb9bb64: r16 = Instance_Color
    //     0xb9bb64: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9bb68: r30 = 12.000000
    //     0xb9bb68: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb9bb6c: ldr             lr, [lr, #0x9e8]
    // 0xb9bb70: stp             lr, x16, [SP]
    // 0xb9bb74: mov             x1, x0
    // 0xb9bb78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb9bb78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb9bb7c: ldr             x4, [x4, #0x9b8]
    // 0xb9bb80: r0 = copyWith()
    //     0xb9bb80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9bb84: stur            x0, [fp, #-0x10]
    // 0xb9bb88: r0 = Text()
    //     0xb9bb88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9bb8c: mov             x1, x0
    // 0xb9bb90: r0 = "Suggestions"
    //     0xb9bb90: add             x0, PP, #0x51, lsl #12  ; [pp+0x51c80] "Suggestions"
    //     0xb9bb94: ldr             x0, [x0, #0xc80]
    // 0xb9bb98: stur            x1, [fp, #-0x28]
    // 0xb9bb9c: StoreField: r1->field_b = r0
    //     0xb9bb9c: stur            w0, [x1, #0xb]
    // 0xb9bba0: ldur            x0, [fp, #-0x10]
    // 0xb9bba4: StoreField: r1->field_13 = r0
    //     0xb9bba4: stur            w0, [x1, #0x13]
    // 0xb9bba8: r0 = Padding()
    //     0xb9bba8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9bbac: mov             x3, x0
    // 0xb9bbb0: r0 = Instance_EdgeInsets
    //     0xb9bbb0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb9bbb4: ldr             x0, [x0, #0xd0]
    // 0xb9bbb8: stur            x3, [fp, #-0x30]
    // 0xb9bbbc: StoreField: r3->field_f = r0
    //     0xb9bbbc: stur            w0, [x3, #0xf]
    // 0xb9bbc0: ldur            x0, [fp, #-0x28]
    // 0xb9bbc4: StoreField: r3->field_b = r0
    //     0xb9bbc4: stur            w0, [x3, #0xb]
    // 0xb9bbc8: ldur            x0, [fp, #-8]
    // 0xb9bbcc: LoadField: r1 = r0->field_b
    //     0xb9bbcc: ldur            w1, [x0, #0xb]
    // 0xb9bbd0: DecompressPointer r1
    //     0xb9bbd0: add             x1, x1, HEAP, lsl #32
    // 0xb9bbd4: cmp             w1, NULL
    // 0xb9bbd8: b.eq            #0xb9bf20
    // 0xb9bbdc: LoadField: r0 = r1->field_43
    //     0xb9bbdc: ldur            w0, [x1, #0x43]
    // 0xb9bbe0: DecompressPointer r0
    //     0xb9bbe0: add             x0, x0, HEAP, lsl #32
    // 0xb9bbe4: LoadField: r1 = r0->field_b
    //     0xb9bbe4: ldur            w1, [x0, #0xb]
    // 0xb9bbe8: DecompressPointer r1
    //     0xb9bbe8: add             x1, x1, HEAP, lsl #32
    // 0xb9bbec: cmp             w1, NULL
    // 0xb9bbf0: b.ne            #0xb9bbfc
    // 0xb9bbf4: r0 = Null
    //     0xb9bbf4: mov             x0, NULL
    // 0xb9bbf8: b               #0xb9bc00
    // 0xb9bbfc: LoadField: r0 = r1->field_b
    //     0xb9bbfc: ldur            w0, [x1, #0xb]
    // 0xb9bc00: cmp             w0, NULL
    // 0xb9bc04: b.ne            #0xb9bc10
    // 0xb9bc08: r0 = 0
    //     0xb9bc08: movz            x0, #0
    // 0xb9bc0c: b               #0xb9bc18
    // 0xb9bc10: r2 = LoadInt32Instr(r0)
    //     0xb9bc10: sbfx            x2, x0, #1, #0x1f
    // 0xb9bc14: mov             x0, x2
    // 0xb9bc18: cmp             x0, #3
    // 0xb9bc1c: b.le            #0xb9bc2c
    // 0xb9bc20: r0 = 180.000000
    //     0xb9bc20: add             x0, PP, #0x51, lsl #12  ; [pp+0x51c88] 180
    //     0xb9bc24: ldr             x0, [x0, #0xc88]
    // 0xb9bc28: b               #0xb9bc30
    // 0xb9bc2c: r0 = Null
    //     0xb9bc2c: mov             x0, NULL
    // 0xb9bc30: stur            x0, [fp, #-0x10]
    // 0xb9bc34: cmp             w1, NULL
    // 0xb9bc38: b.ne            #0xb9bc44
    // 0xb9bc3c: r1 = Null
    //     0xb9bc3c: mov             x1, NULL
    // 0xb9bc40: b               #0xb9bc4c
    // 0xb9bc44: LoadField: r2 = r1->field_b
    //     0xb9bc44: ldur            w2, [x1, #0xb]
    // 0xb9bc48: mov             x1, x2
    // 0xb9bc4c: cmp             w1, NULL
    // 0xb9bc50: b.ne            #0xb9bc5c
    // 0xb9bc54: r1 = 0
    //     0xb9bc54: movz            x1, #0
    // 0xb9bc58: b               #0xb9bc64
    // 0xb9bc5c: r2 = LoadInt32Instr(r1)
    //     0xb9bc5c: sbfx            x2, x1, #1, #0x1f
    // 0xb9bc60: mov             x1, x2
    // 0xb9bc64: ldur            x4, [fp, #-0x20]
    // 0xb9bc68: lsl             x5, x1, #1
    // 0xb9bc6c: ldur            x2, [fp, #-0x18]
    // 0xb9bc70: stur            x5, [fp, #-8]
    // 0xb9bc74: r1 = Function '<anonymous closure>':.
    //     0xb9bc74: add             x1, PP, #0x54, lsl #12  ; [pp+0x54de8] AnonymousClosure: (0xb9bf50), in [package:customer_app/app/presentation/views/glass/search/widgets/search_view.dart] _SearchViewState::build (0xb9ad68)
    //     0xb9bc78: ldr             x1, [x1, #0xde8]
    // 0xb9bc7c: r0 = AllocateClosure()
    //     0xb9bc7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9bc80: stur            x0, [fp, #-0x18]
    // 0xb9bc84: r0 = ListView()
    //     0xb9bc84: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb9bc88: stur            x0, [fp, #-0x28]
    // 0xb9bc8c: r16 = Instance_EdgeInsets
    //     0xb9bc8c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb9bc90: ldr             x16, [x16, #0x980]
    // 0xb9bc94: r30 = true
    //     0xb9bc94: add             lr, NULL, #0x20  ; true
    // 0xb9bc98: stp             lr, x16, [SP]
    // 0xb9bc9c: mov             x1, x0
    // 0xb9bca0: ldur            x2, [fp, #-0x18]
    // 0xb9bca4: ldur            x3, [fp, #-8]
    // 0xb9bca8: r4 = const [0, 0x5, 0x2, 0x3, padding, 0x3, shrinkWrap, 0x4, null]
    //     0xb9bca8: add             x4, PP, #0x51, lsl #12  ; [pp+0x51c98] List(9) [0, 0x5, 0x2, 0x3, "padding", 0x3, "shrinkWrap", 0x4, Null]
    //     0xb9bcac: ldr             x4, [x4, #0xc98]
    // 0xb9bcb0: r0 = ListView.builder()
    //     0xb9bcb0: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb9bcb4: r0 = Scrollbar()
    //     0xb9bcb4: bl              #0x997690  ; AllocateScrollbarStub -> Scrollbar (size=0x30)
    // 0xb9bcb8: mov             x1, x0
    // 0xb9bcbc: ldur            x0, [fp, #-0x28]
    // 0xb9bcc0: stur            x1, [fp, #-8]
    // 0xb9bcc4: StoreField: r1->field_b = r0
    //     0xb9bcc4: stur            w0, [x1, #0xb]
    // 0xb9bcc8: r0 = SizedBox()
    //     0xb9bcc8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb9bccc: mov             x3, x0
    // 0xb9bcd0: r0 = inf
    //     0xb9bcd0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb9bcd4: ldr             x0, [x0, #0x9f8]
    // 0xb9bcd8: stur            x3, [fp, #-0x18]
    // 0xb9bcdc: StoreField: r3->field_f = r0
    //     0xb9bcdc: stur            w0, [x3, #0xf]
    // 0xb9bce0: ldur            x0, [fp, #-0x10]
    // 0xb9bce4: StoreField: r3->field_13 = r0
    //     0xb9bce4: stur            w0, [x3, #0x13]
    // 0xb9bce8: ldur            x0, [fp, #-8]
    // 0xb9bcec: StoreField: r3->field_b = r0
    //     0xb9bcec: stur            w0, [x3, #0xb]
    // 0xb9bcf0: r1 = Null
    //     0xb9bcf0: mov             x1, NULL
    // 0xb9bcf4: r2 = 4
    //     0xb9bcf4: movz            x2, #0x4
    // 0xb9bcf8: r0 = AllocateArray()
    //     0xb9bcf8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9bcfc: mov             x2, x0
    // 0xb9bd00: ldur            x0, [fp, #-0x30]
    // 0xb9bd04: stur            x2, [fp, #-8]
    // 0xb9bd08: StoreField: r2->field_f = r0
    //     0xb9bd08: stur            w0, [x2, #0xf]
    // 0xb9bd0c: ldur            x0, [fp, #-0x18]
    // 0xb9bd10: StoreField: r2->field_13 = r0
    //     0xb9bd10: stur            w0, [x2, #0x13]
    // 0xb9bd14: r1 = <Widget>
    //     0xb9bd14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9bd18: r0 = AllocateGrowableArray()
    //     0xb9bd18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9bd1c: mov             x1, x0
    // 0xb9bd20: ldur            x0, [fp, #-8]
    // 0xb9bd24: stur            x1, [fp, #-0x10]
    // 0xb9bd28: StoreField: r1->field_f = r0
    //     0xb9bd28: stur            w0, [x1, #0xf]
    // 0xb9bd2c: r0 = 4
    //     0xb9bd2c: movz            x0, #0x4
    // 0xb9bd30: StoreField: r1->field_b = r0
    //     0xb9bd30: stur            w0, [x1, #0xb]
    // 0xb9bd34: r0 = Column()
    //     0xb9bd34: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb9bd38: mov             x1, x0
    // 0xb9bd3c: r0 = Instance_Axis
    //     0xb9bd3c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb9bd40: stur            x1, [fp, #-8]
    // 0xb9bd44: StoreField: r1->field_f = r0
    //     0xb9bd44: stur            w0, [x1, #0xf]
    // 0xb9bd48: r2 = Instance_MainAxisAlignment
    //     0xb9bd48: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9bd4c: ldr             x2, [x2, #0xa08]
    // 0xb9bd50: StoreField: r1->field_13 = r2
    //     0xb9bd50: stur            w2, [x1, #0x13]
    // 0xb9bd54: r2 = Instance_MainAxisSize
    //     0xb9bd54: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9bd58: ldr             x2, [x2, #0xa10]
    // 0xb9bd5c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb9bd5c: stur            w2, [x1, #0x17]
    // 0xb9bd60: r2 = Instance_CrossAxisAlignment
    //     0xb9bd60: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb9bd64: ldr             x2, [x2, #0x890]
    // 0xb9bd68: StoreField: r1->field_1b = r2
    //     0xb9bd68: stur            w2, [x1, #0x1b]
    // 0xb9bd6c: r2 = Instance_VerticalDirection
    //     0xb9bd6c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9bd70: ldr             x2, [x2, #0xa20]
    // 0xb9bd74: StoreField: r1->field_23 = r2
    //     0xb9bd74: stur            w2, [x1, #0x23]
    // 0xb9bd78: r2 = Instance_Clip
    //     0xb9bd78: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9bd7c: ldr             x2, [x2, #0x38]
    // 0xb9bd80: StoreField: r1->field_2b = r2
    //     0xb9bd80: stur            w2, [x1, #0x2b]
    // 0xb9bd84: StoreField: r1->field_2f = rZR
    //     0xb9bd84: stur            xzr, [x1, #0x2f]
    // 0xb9bd88: ldur            x3, [fp, #-0x10]
    // 0xb9bd8c: StoreField: r1->field_b = r3
    //     0xb9bd8c: stur            w3, [x1, #0xb]
    // 0xb9bd90: r0 = Material()
    //     0xb9bd90: bl              #0xaabb1c  ; AllocateMaterialStub -> Material (size=0x44)
    // 0xb9bd94: mov             x1, x0
    // 0xb9bd98: r0 = Instance_MaterialType
    //     0xb9bd98: add             x0, PP, #0x4b, lsl #12  ; [pp+0x4bd20] Obj!MaterialType@d741e1
    //     0xb9bd9c: ldr             x0, [x0, #0xd20]
    // 0xb9bda0: stur            x1, [fp, #-0x10]
    // 0xb9bda4: StoreField: r1->field_f = r0
    //     0xb9bda4: stur            w0, [x1, #0xf]
    // 0xb9bda8: d0 = 2.000000
    //     0xb9bda8: fmov            d0, #2.00000000
    // 0xb9bdac: ArrayStore: r1[0] = d0  ; List_8
    //     0xb9bdac: stur            d0, [x1, #0x17]
    // 0xb9bdb0: r0 = Instance_Color
    //     0xb9bdb0: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb9bdb4: StoreField: r1->field_1f = r0
    //     0xb9bdb4: stur            w0, [x1, #0x1f]
    // 0xb9bdb8: r0 = true
    //     0xb9bdb8: add             x0, NULL, #0x20  ; true
    // 0xb9bdbc: StoreField: r1->field_33 = r0
    //     0xb9bdbc: stur            w0, [x1, #0x33]
    // 0xb9bdc0: r0 = Instance_Clip
    //     0xb9bdc0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9bdc4: ldr             x0, [x0, #0x38]
    // 0xb9bdc8: StoreField: r1->field_37 = r0
    //     0xb9bdc8: stur            w0, [x1, #0x37]
    // 0xb9bdcc: r0 = Instance_Duration
    //     0xb9bdcc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e150] Obj!Duration@d77761
    //     0xb9bdd0: ldr             x0, [x0, #0x150]
    // 0xb9bdd4: StoreField: r1->field_3b = r0
    //     0xb9bdd4: stur            w0, [x1, #0x3b]
    // 0xb9bdd8: ldur            x0, [fp, #-8]
    // 0xb9bddc: StoreField: r1->field_b = r0
    //     0xb9bddc: stur            w0, [x1, #0xb]
    // 0xb9bde0: r0 = false
    //     0xb9bde0: add             x0, NULL, #0x30  ; false
    // 0xb9bde4: StoreField: r1->field_13 = r0
    //     0xb9bde4: stur            w0, [x1, #0x13]
    // 0xb9bde8: r0 = Padding()
    //     0xb9bde8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9bdec: mov             x2, x0
    // 0xb9bdf0: r0 = Instance_EdgeInsets
    //     0xb9bdf0: add             x0, PP, #0x51, lsl #12  ; [pp+0x51ca0] Obj!EdgeInsets@d58b81
    //     0xb9bdf4: ldr             x0, [x0, #0xca0]
    // 0xb9bdf8: stur            x2, [fp, #-8]
    // 0xb9bdfc: StoreField: r2->field_f = r0
    //     0xb9bdfc: stur            w0, [x2, #0xf]
    // 0xb9be00: ldur            x0, [fp, #-0x10]
    // 0xb9be04: StoreField: r2->field_b = r0
    //     0xb9be04: stur            w0, [x2, #0xb]
    // 0xb9be08: ldur            x0, [fp, #-0x20]
    // 0xb9be0c: LoadField: r1 = r0->field_b
    //     0xb9be0c: ldur            w1, [x0, #0xb]
    // 0xb9be10: LoadField: r3 = r0->field_f
    //     0xb9be10: ldur            w3, [x0, #0xf]
    // 0xb9be14: DecompressPointer r3
    //     0xb9be14: add             x3, x3, HEAP, lsl #32
    // 0xb9be18: LoadField: r4 = r3->field_b
    //     0xb9be18: ldur            w4, [x3, #0xb]
    // 0xb9be1c: r3 = LoadInt32Instr(r1)
    //     0xb9be1c: sbfx            x3, x1, #1, #0x1f
    // 0xb9be20: stur            x3, [fp, #-0x60]
    // 0xb9be24: r1 = LoadInt32Instr(r4)
    //     0xb9be24: sbfx            x1, x4, #1, #0x1f
    // 0xb9be28: cmp             x3, x1
    // 0xb9be2c: b.ne            #0xb9be38
    // 0xb9be30: mov             x1, x0
    // 0xb9be34: r0 = _growToNextCapacity()
    //     0xb9be34: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb9be38: ldur            x2, [fp, #-0x20]
    // 0xb9be3c: ldur            x3, [fp, #-0x60]
    // 0xb9be40: add             x0, x3, #1
    // 0xb9be44: lsl             x1, x0, #1
    // 0xb9be48: StoreField: r2->field_b = r1
    //     0xb9be48: stur            w1, [x2, #0xb]
    // 0xb9be4c: LoadField: r1 = r2->field_f
    //     0xb9be4c: ldur            w1, [x2, #0xf]
    // 0xb9be50: DecompressPointer r1
    //     0xb9be50: add             x1, x1, HEAP, lsl #32
    // 0xb9be54: ldur            x0, [fp, #-8]
    // 0xb9be58: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb9be58: add             x25, x1, x3, lsl #2
    //     0xb9be5c: add             x25, x25, #0xf
    //     0xb9be60: str             w0, [x25]
    //     0xb9be64: tbz             w0, #0, #0xb9be80
    //     0xb9be68: ldurb           w16, [x1, #-1]
    //     0xb9be6c: ldurb           w17, [x0, #-1]
    //     0xb9be70: and             x16, x17, x16, lsr #2
    //     0xb9be74: tst             x16, HEAP, lsr #32
    //     0xb9be78: b.eq            #0xb9be80
    //     0xb9be7c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb9be80: r0 = Stack()
    //     0xb9be80: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb9be84: mov             x1, x0
    // 0xb9be88: r0 = Instance_Alignment
    //     0xb9be88: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb9be8c: ldr             x0, [x0, #0xfa0]
    // 0xb9be90: stur            x1, [fp, #-8]
    // 0xb9be94: StoreField: r1->field_f = r0
    //     0xb9be94: stur            w0, [x1, #0xf]
    // 0xb9be98: r0 = Instance_StackFit
    //     0xb9be98: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb9be9c: ldr             x0, [x0, #0xfa8]
    // 0xb9bea0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb9bea0: stur            w0, [x1, #0x17]
    // 0xb9bea4: r0 = Instance_Clip
    //     0xb9bea4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb9bea8: ldr             x0, [x0, #0x7e0]
    // 0xb9beac: StoreField: r1->field_1b = r0
    //     0xb9beac: stur            w0, [x1, #0x1b]
    // 0xb9beb0: ldur            x2, [fp, #-0x20]
    // 0xb9beb4: StoreField: r1->field_b = r2
    //     0xb9beb4: stur            w2, [x1, #0xb]
    // 0xb9beb8: r0 = SingleChildScrollView()
    //     0xb9beb8: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xb9bebc: r1 = Instance_Axis
    //     0xb9bebc: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb9bec0: StoreField: r0->field_b = r1
    //     0xb9bec0: stur            w1, [x0, #0xb]
    // 0xb9bec4: r1 = false
    //     0xb9bec4: add             x1, NULL, #0x30  ; false
    // 0xb9bec8: StoreField: r0->field_f = r1
    //     0xb9bec8: stur            w1, [x0, #0xf]
    // 0xb9becc: ldur            x1, [fp, #-8]
    // 0xb9bed0: StoreField: r0->field_23 = r1
    //     0xb9bed0: stur            w1, [x0, #0x23]
    // 0xb9bed4: r1 = Instance_DragStartBehavior
    //     0xb9bed4: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xb9bed8: StoreField: r0->field_27 = r1
    //     0xb9bed8: stur            w1, [x0, #0x27]
    // 0xb9bedc: r1 = Instance_Clip
    //     0xb9bedc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb9bee0: ldr             x1, [x1, #0x7e0]
    // 0xb9bee4: StoreField: r0->field_2b = r1
    //     0xb9bee4: stur            w1, [x0, #0x2b]
    // 0xb9bee8: r1 = Instance_HitTestBehavior
    //     0xb9bee8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xb9beec: ldr             x1, [x1, #0x288]
    // 0xb9bef0: StoreField: r0->field_2f = r1
    //     0xb9bef0: stur            w1, [x0, #0x2f]
    // 0xb9bef4: LeaveFrame
    //     0xb9bef4: mov             SP, fp
    //     0xb9bef8: ldp             fp, lr, [SP], #0x10
    // 0xb9befc: ret
    //     0xb9befc: ret             
    // 0xb9bf00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9bf00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9bf04: b               #0xb9ad90
    // 0xb9bf08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9bf08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9bf0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9bf0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9bf10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9bf10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9bf14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9bf14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9bf18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9bf18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9bf1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9bf1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9bf20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9bf20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb9bf50, size: 0x474
    // 0xb9bf50: EnterFrame
    //     0xb9bf50: stp             fp, lr, [SP, #-0x10]!
    //     0xb9bf54: mov             fp, SP
    // 0xb9bf58: AllocStack(0x50)
    //     0xb9bf58: sub             SP, SP, #0x50
    // 0xb9bf5c: SetupParameters()
    //     0xb9bf5c: ldr             x0, [fp, #0x20]
    //     0xb9bf60: ldur            w1, [x0, #0x17]
    //     0xb9bf64: add             x1, x1, HEAP, lsl #32
    //     0xb9bf68: stur            x1, [fp, #-8]
    // 0xb9bf6c: CheckStackOverflow
    //     0xb9bf6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9bf70: cmp             SP, x16
    //     0xb9bf74: b.ls            #0xb9c3b4
    // 0xb9bf78: r1 = 1
    //     0xb9bf78: movz            x1, #0x1
    // 0xb9bf7c: r0 = AllocateContext()
    //     0xb9bf7c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb9bf80: mov             x3, x0
    // 0xb9bf84: ldur            x0, [fp, #-8]
    // 0xb9bf88: stur            x3, [fp, #-0x18]
    // 0xb9bf8c: StoreField: r3->field_b = r0
    //     0xb9bf8c: stur            w0, [x3, #0xb]
    // 0xb9bf90: LoadField: r1 = r0->field_f
    //     0xb9bf90: ldur            w1, [x0, #0xf]
    // 0xb9bf94: DecompressPointer r1
    //     0xb9bf94: add             x1, x1, HEAP, lsl #32
    // 0xb9bf98: LoadField: r0 = r1->field_b
    //     0xb9bf98: ldur            w0, [x1, #0xb]
    // 0xb9bf9c: DecompressPointer r0
    //     0xb9bf9c: add             x0, x0, HEAP, lsl #32
    // 0xb9bfa0: cmp             w0, NULL
    // 0xb9bfa4: b.eq            #0xb9c3bc
    // 0xb9bfa8: LoadField: r1 = r0->field_43
    //     0xb9bfa8: ldur            w1, [x0, #0x43]
    // 0xb9bfac: DecompressPointer r1
    //     0xb9bfac: add             x1, x1, HEAP, lsl #32
    // 0xb9bfb0: LoadField: r2 = r1->field_b
    //     0xb9bfb0: ldur            w2, [x1, #0xb]
    // 0xb9bfb4: DecompressPointer r2
    //     0xb9bfb4: add             x2, x2, HEAP, lsl #32
    // 0xb9bfb8: cmp             w2, NULL
    // 0xb9bfbc: b.ne            #0xb9bfc8
    // 0xb9bfc0: r0 = Null
    //     0xb9bfc0: mov             x0, NULL
    // 0xb9bfc4: b               #0xb9c004
    // 0xb9bfc8: ldr             x0, [fp, #0x10]
    // 0xb9bfcc: LoadField: r1 = r2->field_b
    //     0xb9bfcc: ldur            w1, [x2, #0xb]
    // 0xb9bfd0: r4 = LoadInt32Instr(r0)
    //     0xb9bfd0: sbfx            x4, x0, #1, #0x1f
    //     0xb9bfd4: tbz             w0, #0, #0xb9bfdc
    //     0xb9bfd8: ldur            x4, [x0, #7]
    // 0xb9bfdc: r0 = LoadInt32Instr(r1)
    //     0xb9bfdc: sbfx            x0, x1, #1, #0x1f
    // 0xb9bfe0: mov             x1, x4
    // 0xb9bfe4: cmp             x1, x0
    // 0xb9bfe8: b.hs            #0xb9c3c0
    // 0xb9bfec: LoadField: r0 = r2->field_f
    //     0xb9bfec: ldur            w0, [x2, #0xf]
    // 0xb9bff0: DecompressPointer r0
    //     0xb9bff0: add             x0, x0, HEAP, lsl #32
    // 0xb9bff4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb9bff4: add             x16, x0, x4, lsl #2
    //     0xb9bff8: ldur            w1, [x16, #0xf]
    // 0xb9bffc: DecompressPointer r1
    //     0xb9bffc: add             x1, x1, HEAP, lsl #32
    // 0xb9c000: mov             x0, x1
    // 0xb9c004: stur            x0, [fp, #-0x10]
    // 0xb9c008: StoreField: r3->field_f = r0
    //     0xb9c008: stur            w0, [x3, #0xf]
    // 0xb9c00c: cmp             w0, NULL
    // 0xb9c010: b.ne            #0xb9c01c
    // 0xb9c014: r1 = Null
    //     0xb9c014: mov             x1, NULL
    // 0xb9c018: b               #0xb9c024
    // 0xb9c01c: LoadField: r1 = r0->field_7
    //     0xb9c01c: ldur            w1, [x0, #7]
    // 0xb9c020: DecompressPointer r1
    //     0xb9c020: add             x1, x1, HEAP, lsl #32
    // 0xb9c024: cmp             w1, NULL
    // 0xb9c028: b.ne            #0xb9c034
    // 0xb9c02c: r4 = ""
    //     0xb9c02c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb9c030: b               #0xb9c038
    // 0xb9c034: mov             x4, x1
    // 0xb9c038: stur            x4, [fp, #-8]
    // 0xb9c03c: r1 = Function '<anonymous closure>':.
    //     0xb9c03c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54df0] AnonymousClosure: (0xaac6d0), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xb9c040: ldr             x1, [x1, #0xdf0]
    // 0xb9c044: r2 = Null
    //     0xb9c044: mov             x2, NULL
    // 0xb9c048: r0 = AllocateClosure()
    //     0xb9c048: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9c04c: stur            x0, [fp, #-0x20]
    // 0xb9c050: r0 = CachedNetworkImage()
    //     0xb9c050: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb9c054: stur            x0, [fp, #-0x28]
    // 0xb9c058: r16 = 35.000000
    //     0xb9c058: add             x16, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0xb9c05c: ldr             x16, [x16, #0x2b0]
    // 0xb9c060: r30 = 35.000000
    //     0xb9c060: add             lr, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0xb9c064: ldr             lr, [lr, #0x2b0]
    // 0xb9c068: stp             lr, x16, [SP, #0x10]
    // 0xb9c06c: r16 = Instance_BoxFit
    //     0xb9c06c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb9c070: ldr             x16, [x16, #0x118]
    // 0xb9c074: ldur            lr, [fp, #-0x20]
    // 0xb9c078: stp             lr, x16, [SP]
    // 0xb9c07c: mov             x1, x0
    // 0xb9c080: ldur            x2, [fp, #-8]
    // 0xb9c084: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x4, height, 0x3, width, 0x2, null]
    //     0xb9c084: add             x4, PP, #0x54, lsl #12  ; [pp+0x54df8] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0xb9c088: ldr             x4, [x4, #0xdf8]
    // 0xb9c08c: r0 = CachedNetworkImage()
    //     0xb9c08c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb9c090: ldur            x0, [fp, #-0x10]
    // 0xb9c094: cmp             w0, NULL
    // 0xb9c098: b.ne            #0xb9c0a4
    // 0xb9c09c: r1 = Null
    //     0xb9c09c: mov             x1, NULL
    // 0xb9c0a0: b               #0xb9c0ac
    // 0xb9c0a4: LoadField: r1 = r0->field_b
    //     0xb9c0a4: ldur            w1, [x0, #0xb]
    // 0xb9c0a8: DecompressPointer r1
    //     0xb9c0a8: add             x1, x1, HEAP, lsl #32
    // 0xb9c0ac: cmp             w1, NULL
    // 0xb9c0b0: b.ne            #0xb9c0bc
    // 0xb9c0b4: r2 = ""
    //     0xb9c0b4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb9c0b8: b               #0xb9c0c0
    // 0xb9c0bc: mov             x2, x1
    // 0xb9c0c0: ldr             x1, [fp, #0x18]
    // 0xb9c0c4: stur            x2, [fp, #-8]
    // 0xb9c0c8: r0 = of()
    //     0xb9c0c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9c0cc: LoadField: r1 = r0->field_87
    //     0xb9c0cc: ldur            w1, [x0, #0x87]
    // 0xb9c0d0: DecompressPointer r1
    //     0xb9c0d0: add             x1, x1, HEAP, lsl #32
    // 0xb9c0d4: LoadField: r0 = r1->field_2b
    //     0xb9c0d4: ldur            w0, [x1, #0x2b]
    // 0xb9c0d8: DecompressPointer r0
    //     0xb9c0d8: add             x0, x0, HEAP, lsl #32
    // 0xb9c0dc: r16 = Instance_Color
    //     0xb9c0dc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9c0e0: r30 = 12.000000
    //     0xb9c0e0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb9c0e4: ldr             lr, [lr, #0x9e8]
    // 0xb9c0e8: stp             lr, x16, [SP]
    // 0xb9c0ec: mov             x1, x0
    // 0xb9c0f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb9c0f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb9c0f4: ldr             x4, [x4, #0x9b8]
    // 0xb9c0f8: r0 = copyWith()
    //     0xb9c0f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9c0fc: stur            x0, [fp, #-0x20]
    // 0xb9c100: r0 = Text()
    //     0xb9c100: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9c104: mov             x2, x0
    // 0xb9c108: ldur            x0, [fp, #-8]
    // 0xb9c10c: stur            x2, [fp, #-0x30]
    // 0xb9c110: StoreField: r2->field_b = r0
    //     0xb9c110: stur            w0, [x2, #0xb]
    // 0xb9c114: ldur            x0, [fp, #-0x20]
    // 0xb9c118: StoreField: r2->field_13 = r0
    //     0xb9c118: stur            w0, [x2, #0x13]
    // 0xb9c11c: ldur            x0, [fp, #-0x10]
    // 0xb9c120: cmp             w0, NULL
    // 0xb9c124: b.ne            #0xb9c130
    // 0xb9c128: r0 = Null
    //     0xb9c128: mov             x0, NULL
    // 0xb9c12c: b               #0xb9c13c
    // 0xb9c130: LoadField: r1 = r0->field_f
    //     0xb9c130: ldur            w1, [x0, #0xf]
    // 0xb9c134: DecompressPointer r1
    //     0xb9c134: add             x1, x1, HEAP, lsl #32
    // 0xb9c138: mov             x0, x1
    // 0xb9c13c: cmp             w0, NULL
    // 0xb9c140: b.ne            #0xb9c14c
    // 0xb9c144: r3 = ""
    //     0xb9c144: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb9c148: b               #0xb9c150
    // 0xb9c14c: mov             x3, x0
    // 0xb9c150: ldur            x0, [fp, #-0x28]
    // 0xb9c154: ldr             x1, [fp, #0x18]
    // 0xb9c158: stur            x3, [fp, #-8]
    // 0xb9c15c: r0 = of()
    //     0xb9c15c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9c160: LoadField: r1 = r0->field_87
    //     0xb9c160: ldur            w1, [x0, #0x87]
    // 0xb9c164: DecompressPointer r1
    //     0xb9c164: add             x1, x1, HEAP, lsl #32
    // 0xb9c168: LoadField: r0 = r1->field_2b
    //     0xb9c168: ldur            w0, [x1, #0x2b]
    // 0xb9c16c: DecompressPointer r0
    //     0xb9c16c: add             x0, x0, HEAP, lsl #32
    // 0xb9c170: r16 = Instance_Color
    //     0xb9c170: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9c174: r30 = 12.000000
    //     0xb9c174: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb9c178: ldr             lr, [lr, #0x9e8]
    // 0xb9c17c: stp             lr, x16, [SP]
    // 0xb9c180: mov             x1, x0
    // 0xb9c184: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb9c184: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb9c188: ldr             x4, [x4, #0x9b8]
    // 0xb9c18c: r0 = copyWith()
    //     0xb9c18c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9c190: stur            x0, [fp, #-0x10]
    // 0xb9c194: r0 = Text()
    //     0xb9c194: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9c198: mov             x3, x0
    // 0xb9c19c: ldur            x0, [fp, #-8]
    // 0xb9c1a0: stur            x3, [fp, #-0x20]
    // 0xb9c1a4: StoreField: r3->field_b = r0
    //     0xb9c1a4: stur            w0, [x3, #0xb]
    // 0xb9c1a8: ldur            x0, [fp, #-0x10]
    // 0xb9c1ac: StoreField: r3->field_13 = r0
    //     0xb9c1ac: stur            w0, [x3, #0x13]
    // 0xb9c1b0: r0 = Instance_TextOverflow
    //     0xb9c1b0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb9c1b4: ldr             x0, [x0, #0xe10]
    // 0xb9c1b8: StoreField: r3->field_2b = r0
    //     0xb9c1b8: stur            w0, [x3, #0x2b]
    // 0xb9c1bc: r0 = 2
    //     0xb9c1bc: movz            x0, #0x2
    // 0xb9c1c0: StoreField: r3->field_37 = r0
    //     0xb9c1c0: stur            w0, [x3, #0x37]
    // 0xb9c1c4: r1 = Null
    //     0xb9c1c4: mov             x1, NULL
    // 0xb9c1c8: r2 = 6
    //     0xb9c1c8: movz            x2, #0x6
    // 0xb9c1cc: r0 = AllocateArray()
    //     0xb9c1cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9c1d0: mov             x2, x0
    // 0xb9c1d4: ldur            x0, [fp, #-0x30]
    // 0xb9c1d8: stur            x2, [fp, #-8]
    // 0xb9c1dc: StoreField: r2->field_f = r0
    //     0xb9c1dc: stur            w0, [x2, #0xf]
    // 0xb9c1e0: r16 = Instance_SizedBox
    //     0xb9c1e0: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb9c1e4: ldr             x16, [x16, #0xc70]
    // 0xb9c1e8: StoreField: r2->field_13 = r16
    //     0xb9c1e8: stur            w16, [x2, #0x13]
    // 0xb9c1ec: ldur            x0, [fp, #-0x20]
    // 0xb9c1f0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9c1f0: stur            w0, [x2, #0x17]
    // 0xb9c1f4: r1 = <Widget>
    //     0xb9c1f4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9c1f8: r0 = AllocateGrowableArray()
    //     0xb9c1f8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9c1fc: mov             x1, x0
    // 0xb9c200: ldur            x0, [fp, #-8]
    // 0xb9c204: stur            x1, [fp, #-0x10]
    // 0xb9c208: StoreField: r1->field_f = r0
    //     0xb9c208: stur            w0, [x1, #0xf]
    // 0xb9c20c: r2 = 6
    //     0xb9c20c: movz            x2, #0x6
    // 0xb9c210: StoreField: r1->field_b = r2
    //     0xb9c210: stur            w2, [x1, #0xb]
    // 0xb9c214: r0 = Column()
    //     0xb9c214: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb9c218: mov             x2, x0
    // 0xb9c21c: r0 = Instance_Axis
    //     0xb9c21c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb9c220: stur            x2, [fp, #-8]
    // 0xb9c224: StoreField: r2->field_f = r0
    //     0xb9c224: stur            w0, [x2, #0xf]
    // 0xb9c228: r0 = Instance_MainAxisAlignment
    //     0xb9c228: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9c22c: ldr             x0, [x0, #0xa08]
    // 0xb9c230: StoreField: r2->field_13 = r0
    //     0xb9c230: stur            w0, [x2, #0x13]
    // 0xb9c234: r3 = Instance_MainAxisSize
    //     0xb9c234: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9c238: ldr             x3, [x3, #0xa10]
    // 0xb9c23c: ArrayStore: r2[0] = r3  ; List_4
    //     0xb9c23c: stur            w3, [x2, #0x17]
    // 0xb9c240: r1 = Instance_CrossAxisAlignment
    //     0xb9c240: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb9c244: ldr             x1, [x1, #0x890]
    // 0xb9c248: StoreField: r2->field_1b = r1
    //     0xb9c248: stur            w1, [x2, #0x1b]
    // 0xb9c24c: r4 = Instance_VerticalDirection
    //     0xb9c24c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9c250: ldr             x4, [x4, #0xa20]
    // 0xb9c254: StoreField: r2->field_23 = r4
    //     0xb9c254: stur            w4, [x2, #0x23]
    // 0xb9c258: r5 = Instance_Clip
    //     0xb9c258: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9c25c: ldr             x5, [x5, #0x38]
    // 0xb9c260: StoreField: r2->field_2b = r5
    //     0xb9c260: stur            w5, [x2, #0x2b]
    // 0xb9c264: StoreField: r2->field_2f = rZR
    //     0xb9c264: stur            xzr, [x2, #0x2f]
    // 0xb9c268: ldur            x1, [fp, #-0x10]
    // 0xb9c26c: StoreField: r2->field_b = r1
    //     0xb9c26c: stur            w1, [x2, #0xb]
    // 0xb9c270: r1 = <FlexParentData>
    //     0xb9c270: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb9c274: ldr             x1, [x1, #0xe00]
    // 0xb9c278: r0 = Expanded()
    //     0xb9c278: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb9c27c: mov             x3, x0
    // 0xb9c280: r0 = 1
    //     0xb9c280: movz            x0, #0x1
    // 0xb9c284: stur            x3, [fp, #-0x10]
    // 0xb9c288: StoreField: r3->field_13 = r0
    //     0xb9c288: stur            x0, [x3, #0x13]
    // 0xb9c28c: r0 = Instance_FlexFit
    //     0xb9c28c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb9c290: ldr             x0, [x0, #0xe08]
    // 0xb9c294: StoreField: r3->field_1b = r0
    //     0xb9c294: stur            w0, [x3, #0x1b]
    // 0xb9c298: ldur            x0, [fp, #-8]
    // 0xb9c29c: StoreField: r3->field_b = r0
    //     0xb9c29c: stur            w0, [x3, #0xb]
    // 0xb9c2a0: r1 = Null
    //     0xb9c2a0: mov             x1, NULL
    // 0xb9c2a4: r2 = 6
    //     0xb9c2a4: movz            x2, #0x6
    // 0xb9c2a8: r0 = AllocateArray()
    //     0xb9c2a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9c2ac: mov             x2, x0
    // 0xb9c2b0: ldur            x0, [fp, #-0x28]
    // 0xb9c2b4: stur            x2, [fp, #-8]
    // 0xb9c2b8: StoreField: r2->field_f = r0
    //     0xb9c2b8: stur            w0, [x2, #0xf]
    // 0xb9c2bc: r16 = Instance_SizedBox
    //     0xb9c2bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb9c2c0: ldr             x16, [x16, #0xb20]
    // 0xb9c2c4: StoreField: r2->field_13 = r16
    //     0xb9c2c4: stur            w16, [x2, #0x13]
    // 0xb9c2c8: ldur            x0, [fp, #-0x10]
    // 0xb9c2cc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9c2cc: stur            w0, [x2, #0x17]
    // 0xb9c2d0: r1 = <Widget>
    //     0xb9c2d0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9c2d4: r0 = AllocateGrowableArray()
    //     0xb9c2d4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9c2d8: mov             x1, x0
    // 0xb9c2dc: ldur            x0, [fp, #-8]
    // 0xb9c2e0: stur            x1, [fp, #-0x10]
    // 0xb9c2e4: StoreField: r1->field_f = r0
    //     0xb9c2e4: stur            w0, [x1, #0xf]
    // 0xb9c2e8: r0 = 6
    //     0xb9c2e8: movz            x0, #0x6
    // 0xb9c2ec: StoreField: r1->field_b = r0
    //     0xb9c2ec: stur            w0, [x1, #0xb]
    // 0xb9c2f0: r0 = Row()
    //     0xb9c2f0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb9c2f4: mov             x1, x0
    // 0xb9c2f8: r0 = Instance_Axis
    //     0xb9c2f8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb9c2fc: stur            x1, [fp, #-8]
    // 0xb9c300: StoreField: r1->field_f = r0
    //     0xb9c300: stur            w0, [x1, #0xf]
    // 0xb9c304: r0 = Instance_MainAxisAlignment
    //     0xb9c304: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9c308: ldr             x0, [x0, #0xa08]
    // 0xb9c30c: StoreField: r1->field_13 = r0
    //     0xb9c30c: stur            w0, [x1, #0x13]
    // 0xb9c310: r0 = Instance_MainAxisSize
    //     0xb9c310: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9c314: ldr             x0, [x0, #0xa10]
    // 0xb9c318: ArrayStore: r1[0] = r0  ; List_4
    //     0xb9c318: stur            w0, [x1, #0x17]
    // 0xb9c31c: r0 = Instance_CrossAxisAlignment
    //     0xb9c31c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9c320: ldr             x0, [x0, #0xa18]
    // 0xb9c324: StoreField: r1->field_1b = r0
    //     0xb9c324: stur            w0, [x1, #0x1b]
    // 0xb9c328: r0 = Instance_VerticalDirection
    //     0xb9c328: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9c32c: ldr             x0, [x0, #0xa20]
    // 0xb9c330: StoreField: r1->field_23 = r0
    //     0xb9c330: stur            w0, [x1, #0x23]
    // 0xb9c334: r0 = Instance_Clip
    //     0xb9c334: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9c338: ldr             x0, [x0, #0x38]
    // 0xb9c33c: StoreField: r1->field_2b = r0
    //     0xb9c33c: stur            w0, [x1, #0x2b]
    // 0xb9c340: StoreField: r1->field_2f = rZR
    //     0xb9c340: stur            xzr, [x1, #0x2f]
    // 0xb9c344: ldur            x0, [fp, #-0x10]
    // 0xb9c348: StoreField: r1->field_b = r0
    //     0xb9c348: stur            w0, [x1, #0xb]
    // 0xb9c34c: r0 = Container()
    //     0xb9c34c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb9c350: stur            x0, [fp, #-0x10]
    // 0xb9c354: r16 = Instance_EdgeInsets
    //     0xb9c354: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb9c358: ldr             x16, [x16, #0x980]
    // 0xb9c35c: ldur            lr, [fp, #-8]
    // 0xb9c360: stp             lr, x16, [SP]
    // 0xb9c364: mov             x1, x0
    // 0xb9c368: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xb9c368: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xb9c36c: ldr             x4, [x4, #0x30]
    // 0xb9c370: r0 = Container()
    //     0xb9c370: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb9c374: r0 = GestureDetector()
    //     0xb9c374: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb9c378: ldur            x2, [fp, #-0x18]
    // 0xb9c37c: r1 = Function '<anonymous closure>':.
    //     0xb9c37c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54e00] AnonymousClosure: (0xb9c3c4), in [package:customer_app/app/presentation/views/glass/search/widgets/search_view.dart] _SearchViewState::build (0xb9ad68)
    //     0xb9c380: ldr             x1, [x1, #0xe00]
    // 0xb9c384: stur            x0, [fp, #-8]
    // 0xb9c388: r0 = AllocateClosure()
    //     0xb9c388: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9c38c: ldur            x16, [fp, #-0x10]
    // 0xb9c390: stp             x16, x0, [SP]
    // 0xb9c394: ldur            x1, [fp, #-8]
    // 0xb9c398: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb9c398: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb9c39c: ldr             x4, [x4, #0xaf0]
    // 0xb9c3a0: r0 = GestureDetector()
    //     0xb9c3a0: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb9c3a4: ldur            x0, [fp, #-8]
    // 0xb9c3a8: LeaveFrame
    //     0xb9c3a8: mov             SP, fp
    //     0xb9c3ac: ldp             fp, lr, [SP], #0x10
    // 0xb9c3b0: ret
    //     0xb9c3b0: ret             
    // 0xb9c3b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9c3b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9c3b8: b               #0xb9bf78
    // 0xb9c3bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9c3bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9c3c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb9c3c0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb9c3c4, size: 0x104
    // 0xb9c3c4: EnterFrame
    //     0xb9c3c4: stp             fp, lr, [SP, #-0x10]!
    //     0xb9c3c8: mov             fp, SP
    // 0xb9c3cc: AllocStack(0x30)
    //     0xb9c3cc: sub             SP, SP, #0x30
    // 0xb9c3d0: SetupParameters()
    //     0xb9c3d0: ldr             x0, [fp, #0x10]
    //     0xb9c3d4: ldur            w2, [x0, #0x17]
    //     0xb9c3d8: add             x2, x2, HEAP, lsl #32
    //     0xb9c3dc: stur            x2, [fp, #-0x10]
    // 0xb9c3e0: CheckStackOverflow
    //     0xb9c3e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9c3e4: cmp             SP, x16
    //     0xb9c3e8: b.ls            #0xb9c4bc
    // 0xb9c3ec: LoadField: r0 = r2->field_b
    //     0xb9c3ec: ldur            w0, [x2, #0xb]
    // 0xb9c3f0: DecompressPointer r0
    //     0xb9c3f0: add             x0, x0, HEAP, lsl #32
    // 0xb9c3f4: stur            x0, [fp, #-8]
    // 0xb9c3f8: LoadField: r1 = r0->field_f
    //     0xb9c3f8: ldur            w1, [x0, #0xf]
    // 0xb9c3fc: DecompressPointer r1
    //     0xb9c3fc: add             x1, x1, HEAP, lsl #32
    // 0xb9c400: LoadField: r3 = r1->field_b
    //     0xb9c400: ldur            w3, [x1, #0xb]
    // 0xb9c404: DecompressPointer r3
    //     0xb9c404: add             x3, x3, HEAP, lsl #32
    // 0xb9c408: cmp             w3, NULL
    // 0xb9c40c: b.eq            #0xb9c4c4
    // 0xb9c410: LoadField: r4 = r2->field_f
    //     0xb9c410: ldur            w4, [x2, #0xf]
    // 0xb9c414: DecompressPointer r4
    //     0xb9c414: add             x4, x4, HEAP, lsl #32
    // 0xb9c418: cmp             w4, NULL
    // 0xb9c41c: b.ne            #0xb9c428
    // 0xb9c420: r4 = Null
    //     0xb9c420: mov             x4, NULL
    // 0xb9c424: b               #0xb9c434
    // 0xb9c428: LoadField: r5 = r4->field_b
    //     0xb9c428: ldur            w5, [x4, #0xb]
    // 0xb9c42c: DecompressPointer r5
    //     0xb9c42c: add             x5, x5, HEAP, lsl #32
    // 0xb9c430: mov             x4, x5
    // 0xb9c434: cmp             w4, NULL
    // 0xb9c438: b.ne            #0xb9c440
    // 0xb9c43c: r4 = " "
    //     0xb9c43c: ldr             x4, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb9c440: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xb9c440: ldur            w5, [x1, #0x17]
    // 0xb9c444: DecompressPointer r5
    //     0xb9c444: add             x5, x5, HEAP, lsl #32
    // 0xb9c448: LoadField: r1 = r5->field_27
    //     0xb9c448: ldur            w1, [x5, #0x27]
    // 0xb9c44c: DecompressPointer r1
    //     0xb9c44c: add             x1, x1, HEAP, lsl #32
    // 0xb9c450: LoadField: r5 = r1->field_7
    //     0xb9c450: ldur            w5, [x1, #7]
    // 0xb9c454: DecompressPointer r5
    //     0xb9c454: add             x5, x5, HEAP, lsl #32
    // 0xb9c458: LoadField: r1 = r3->field_3b
    //     0xb9c458: ldur            w1, [x3, #0x3b]
    // 0xb9c45c: DecompressPointer r1
    //     0xb9c45c: add             x1, x1, HEAP, lsl #32
    // 0xb9c460: stp             x4, x1, [SP, #8]
    // 0xb9c464: str             x5, [SP]
    // 0xb9c468: r4 = 0
    //     0xb9c468: movz            x4, #0
    // 0xb9c46c: ldr             x0, [SP, #0x10]
    // 0xb9c470: r16 = UnlinkedCall_0x613b5c
    //     0xb9c470: add             x16, PP, #0x54, lsl #12  ; [pp+0x54e08] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb9c474: add             x16, x16, #0xe08
    // 0xb9c478: ldp             x5, lr, [x16]
    // 0xb9c47c: blr             lr
    // 0xb9c480: ldur            x0, [fp, #-8]
    // 0xb9c484: LoadField: r3 = r0->field_f
    //     0xb9c484: ldur            w3, [x0, #0xf]
    // 0xb9c488: DecompressPointer r3
    //     0xb9c488: add             x3, x3, HEAP, lsl #32
    // 0xb9c48c: ldur            x2, [fp, #-0x10]
    // 0xb9c490: stur            x3, [fp, #-0x18]
    // 0xb9c494: r1 = Function '<anonymous closure>':.
    //     0xb9c494: add             x1, PP, #0x54, lsl #12  ; [pp+0x54e18] AnonymousClosure: (0xaac0ac), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xb9c498: ldr             x1, [x1, #0xe18]
    // 0xb9c49c: r0 = AllocateClosure()
    //     0xb9c49c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9c4a0: ldur            x1, [fp, #-0x18]
    // 0xb9c4a4: mov             x2, x0
    // 0xb9c4a8: r0 = setState()
    //     0xb9c4a8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb9c4ac: r0 = Null
    //     0xb9c4ac: mov             x0, NULL
    // 0xb9c4b0: LeaveFrame
    //     0xb9c4b0: mov             SP, fp
    //     0xb9c4b4: ldp             fp, lr, [SP], #0x10
    // 0xb9c4b8: ret
    //     0xb9c4b8: ret             
    // 0xb9c4bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9c4bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9c4c0: b               #0xb9c3ec
    // 0xb9c4c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9c4c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, dynamic, dynamic, dynamic, dynamic, dynamic) {
    // ** addr: 0xb9c4c8, size: 0xc0
    // 0xb9c4c8: EnterFrame
    //     0xb9c4c8: stp             fp, lr, [SP, #-0x10]!
    //     0xb9c4cc: mov             fp, SP
    // 0xb9c4d0: AllocStack(0x40)
    //     0xb9c4d0: sub             SP, SP, #0x40
    // 0xb9c4d4: SetupParameters()
    //     0xb9c4d4: ldr             x0, [fp, #0x38]
    //     0xb9c4d8: ldur            w1, [x0, #0x17]
    //     0xb9c4dc: add             x1, x1, HEAP, lsl #32
    // 0xb9c4e0: CheckStackOverflow
    //     0xb9c4e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9c4e4: cmp             SP, x16
    //     0xb9c4e8: b.ls            #0xb9c57c
    // 0xb9c4ec: LoadField: r0 = r1->field_f
    //     0xb9c4ec: ldur            w0, [x1, #0xf]
    // 0xb9c4f0: DecompressPointer r0
    //     0xb9c4f0: add             x0, x0, HEAP, lsl #32
    // 0xb9c4f4: LoadField: r1 = r0->field_b
    //     0xb9c4f4: ldur            w1, [x0, #0xb]
    // 0xb9c4f8: DecompressPointer r1
    //     0xb9c4f8: add             x1, x1, HEAP, lsl #32
    // 0xb9c4fc: stur            x1, [fp, #-8]
    // 0xb9c500: cmp             w1, NULL
    // 0xb9c504: b.eq            #0xb9c584
    // 0xb9c508: ldr             x0, [fp, #0x10]
    // 0xb9c50c: cmp             w0, NULL
    // 0xb9c510: b.ne            #0xb9c520
    // 0xb9c514: r0 = ProductRating()
    //     0xb9c514: bl              #0x911a74  ; AllocateProductRatingStub -> ProductRating (size=0x18)
    // 0xb9c518: mov             x1, x0
    // 0xb9c51c: b               #0xb9c524
    // 0xb9c520: mov             x1, x0
    // 0xb9c524: ldur            x0, [fp, #-8]
    // 0xb9c528: LoadField: r2 = r0->field_2f
    //     0xb9c528: ldur            w2, [x0, #0x2f]
    // 0xb9c52c: DecompressPointer r2
    //     0xb9c52c: add             x2, x2, HEAP, lsl #32
    // 0xb9c530: ldr             x16, [fp, #0x30]
    // 0xb9c534: stp             x16, x2, [SP, #0x28]
    // 0xb9c538: ldr             x16, [fp, #0x28]
    // 0xb9c53c: ldr             lr, [fp, #0x20]
    // 0xb9c540: stp             lr, x16, [SP, #0x18]
    // 0xb9c544: ldr             x16, [fp, #0x18]
    // 0xb9c548: r30 = ""
    //     0xb9c548: ldr             lr, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb9c54c: stp             lr, x16, [SP, #8]
    // 0xb9c550: str             x1, [SP]
    // 0xb9c554: r4 = 0
    //     0xb9c554: movz            x4, #0
    // 0xb9c558: ldr             x0, [SP, #0x30]
    // 0xb9c55c: r16 = UnlinkedCall_0x613b5c
    //     0xb9c55c: add             x16, PP, #0x54, lsl #12  ; [pp+0x54e20] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb9c560: add             x16, x16, #0xe20
    // 0xb9c564: ldp             x5, lr, [x16]
    // 0xb9c568: blr             lr
    // 0xb9c56c: r0 = Null
    //     0xb9c56c: mov             x0, NULL
    // 0xb9c570: LeaveFrame
    //     0xb9c570: mov             SP, fp
    //     0xb9c574: ldp             fp, lr, [SP], #0x10
    // 0xb9c578: ret
    //     0xb9c578: ret             
    // 0xb9c57c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9c57c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9c580: b               #0xb9c4ec
    // 0xb9c584: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9c584: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String, String, String, String, String) {
    // ** addr: 0xb9c588, size: 0xac
    // 0xb9c588: EnterFrame
    //     0xb9c588: stp             fp, lr, [SP, #-0x10]!
    //     0xb9c58c: mov             fp, SP
    // 0xb9c590: AllocStack(0x48)
    //     0xb9c590: sub             SP, SP, #0x48
    // 0xb9c594: SetupParameters()
    //     0xb9c594: ldr             x0, [fp, #0x50]
    //     0xb9c598: ldur            w1, [x0, #0x17]
    //     0xb9c59c: add             x1, x1, HEAP, lsl #32
    // 0xb9c5a0: CheckStackOverflow
    //     0xb9c5a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9c5a4: cmp             SP, x16
    //     0xb9c5a8: b.ls            #0xb9c628
    // 0xb9c5ac: LoadField: r0 = r1->field_f
    //     0xb9c5ac: ldur            w0, [x1, #0xf]
    // 0xb9c5b0: DecompressPointer r0
    //     0xb9c5b0: add             x0, x0, HEAP, lsl #32
    // 0xb9c5b4: LoadField: r1 = r0->field_b
    //     0xb9c5b4: ldur            w1, [x0, #0xb]
    // 0xb9c5b8: DecompressPointer r1
    //     0xb9c5b8: add             x1, x1, HEAP, lsl #32
    // 0xb9c5bc: cmp             w1, NULL
    // 0xb9c5c0: b.eq            #0xb9c630
    // 0xb9c5c4: LoadField: r0 = r1->field_37
    //     0xb9c5c4: ldur            w0, [x1, #0x37]
    // 0xb9c5c8: DecompressPointer r0
    //     0xb9c5c8: add             x0, x0, HEAP, lsl #32
    // 0xb9c5cc: ldr             x16, [fp, #0x48]
    // 0xb9c5d0: stp             x16, x0, [SP, #0x38]
    // 0xb9c5d4: ldr             x16, [fp, #0x40]
    // 0xb9c5d8: ldr             lr, [fp, #0x38]
    // 0xb9c5dc: stp             lr, x16, [SP, #0x28]
    // 0xb9c5e0: ldr             x16, [fp, #0x30]
    // 0xb9c5e4: ldr             lr, [fp, #0x28]
    // 0xb9c5e8: stp             lr, x16, [SP, #0x18]
    // 0xb9c5ec: ldr             x16, [fp, #0x20]
    // 0xb9c5f0: ldr             lr, [fp, #0x18]
    // 0xb9c5f4: stp             lr, x16, [SP, #8]
    // 0xb9c5f8: ldr             x16, [fp, #0x10]
    // 0xb9c5fc: str             x16, [SP]
    // 0xb9c600: r4 = 0
    //     0xb9c600: movz            x4, #0
    // 0xb9c604: ldr             x0, [SP, #0x40]
    // 0xb9c608: r16 = UnlinkedCall_0x613b5c
    //     0xb9c608: add             x16, PP, #0x54, lsl #12  ; [pp+0x54e30] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb9c60c: add             x16, x16, #0xe30
    // 0xb9c610: ldp             x5, lr, [x16]
    // 0xb9c614: blr             lr
    // 0xb9c618: r0 = Null
    //     0xb9c618: mov             x0, NULL
    // 0xb9c61c: LeaveFrame
    //     0xb9c61c: mov             SP, fp
    //     0xb9c620: ldp             fp, lr, [SP], #0x10
    // 0xb9c624: ret
    //     0xb9c624: ret             
    // 0xb9c628: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9c628: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9c62c: b               #0xb9c5ac
    // 0xb9c630: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9c630: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String, String) {
    // ** addr: 0xb9c634, size: 0x98
    // 0xb9c634: EnterFrame
    //     0xb9c634: stp             fp, lr, [SP, #-0x10]!
    //     0xb9c638: mov             fp, SP
    // 0xb9c63c: AllocStack(0x30)
    //     0xb9c63c: sub             SP, SP, #0x30
    // 0xb9c640: SetupParameters()
    //     0xb9c640: ldr             x0, [fp, #0x38]
    //     0xb9c644: ldur            w1, [x0, #0x17]
    //     0xb9c648: add             x1, x1, HEAP, lsl #32
    // 0xb9c64c: CheckStackOverflow
    //     0xb9c64c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9c650: cmp             SP, x16
    //     0xb9c654: b.ls            #0xb9c6c0
    // 0xb9c658: LoadField: r0 = r1->field_f
    //     0xb9c658: ldur            w0, [x1, #0xf]
    // 0xb9c65c: DecompressPointer r0
    //     0xb9c65c: add             x0, x0, HEAP, lsl #32
    // 0xb9c660: LoadField: r1 = r0->field_b
    //     0xb9c660: ldur            w1, [x0, #0xb]
    // 0xb9c664: DecompressPointer r1
    //     0xb9c664: add             x1, x1, HEAP, lsl #32
    // 0xb9c668: cmp             w1, NULL
    // 0xb9c66c: b.eq            #0xb9c6c8
    // 0xb9c670: LoadField: r0 = r1->field_33
    //     0xb9c670: ldur            w0, [x1, #0x33]
    // 0xb9c674: DecompressPointer r0
    //     0xb9c674: add             x0, x0, HEAP, lsl #32
    // 0xb9c678: ldr             x16, [fp, #0x30]
    // 0xb9c67c: stp             x16, x0, [SP, #0x20]
    // 0xb9c680: ldr             x16, [fp, #0x28]
    // 0xb9c684: ldr             lr, [fp, #0x20]
    // 0xb9c688: stp             lr, x16, [SP, #0x10]
    // 0xb9c68c: ldr             x16, [fp, #0x18]
    // 0xb9c690: ldr             lr, [fp, #0x10]
    // 0xb9c694: stp             lr, x16, [SP]
    // 0xb9c698: r4 = 0
    //     0xb9c698: movz            x4, #0
    // 0xb9c69c: ldr             x0, [SP, #0x28]
    // 0xb9c6a0: r16 = UnlinkedCall_0x613b5c
    //     0xb9c6a0: add             x16, PP, #0x54, lsl #12  ; [pp+0x54e40] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb9c6a4: add             x16, x16, #0xe40
    // 0xb9c6a8: ldp             x5, lr, [x16]
    // 0xb9c6ac: blr             lr
    // 0xb9c6b0: r0 = Null
    //     0xb9c6b0: mov             x0, NULL
    // 0xb9c6b4: LeaveFrame
    //     0xb9c6b4: mov             SP, fp
    //     0xb9c6b8: ldp             fp, lr, [SP], #0x10
    // 0xb9c6bc: ret
    //     0xb9c6bc: ret             
    // 0xb9c6c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9c6c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9c6c4: b               #0xb9c658
    // 0xb9c6c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9c6c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, int, String) {
    // ** addr: 0xb9c6cc, size: 0x8c
    // 0xb9c6cc: EnterFrame
    //     0xb9c6cc: stp             fp, lr, [SP, #-0x10]!
    //     0xb9c6d0: mov             fp, SP
    // 0xb9c6d4: AllocStack(0x20)
    //     0xb9c6d4: sub             SP, SP, #0x20
    // 0xb9c6d8: SetupParameters()
    //     0xb9c6d8: ldr             x0, [fp, #0x28]
    //     0xb9c6dc: ldur            w1, [x0, #0x17]
    //     0xb9c6e0: add             x1, x1, HEAP, lsl #32
    // 0xb9c6e4: CheckStackOverflow
    //     0xb9c6e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9c6e8: cmp             SP, x16
    //     0xb9c6ec: b.ls            #0xb9c74c
    // 0xb9c6f0: LoadField: r0 = r1->field_f
    //     0xb9c6f0: ldur            w0, [x1, #0xf]
    // 0xb9c6f4: DecompressPointer r0
    //     0xb9c6f4: add             x0, x0, HEAP, lsl #32
    // 0xb9c6f8: LoadField: r1 = r0->field_b
    //     0xb9c6f8: ldur            w1, [x0, #0xb]
    // 0xb9c6fc: DecompressPointer r1
    //     0xb9c6fc: add             x1, x1, HEAP, lsl #32
    // 0xb9c700: cmp             w1, NULL
    // 0xb9c704: b.eq            #0xb9c754
    // 0xb9c708: LoadField: r0 = r1->field_2b
    //     0xb9c708: ldur            w0, [x1, #0x2b]
    // 0xb9c70c: DecompressPointer r0
    //     0xb9c70c: add             x0, x0, HEAP, lsl #32
    // 0xb9c710: ldr             x16, [fp, #0x20]
    // 0xb9c714: stp             x16, x0, [SP, #0x10]
    // 0xb9c718: ldr             x16, [fp, #0x18]
    // 0xb9c71c: ldr             lr, [fp, #0x10]
    // 0xb9c720: stp             lr, x16, [SP]
    // 0xb9c724: r4 = 0
    //     0xb9c724: movz            x4, #0
    // 0xb9c728: ldr             x0, [SP, #0x18]
    // 0xb9c72c: r16 = UnlinkedCall_0x613b5c
    //     0xb9c72c: add             x16, PP, #0x54, lsl #12  ; [pp+0x54e50] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb9c730: add             x16, x16, #0xe50
    // 0xb9c734: ldp             x5, lr, [x16]
    // 0xb9c738: blr             lr
    // 0xb9c73c: r0 = Null
    //     0xb9c73c: mov             x0, NULL
    // 0xb9c740: LeaveFrame
    //     0xb9c740: mov             SP, fp
    //     0xb9c744: ldp             fp, lr, [SP], #0x10
    // 0xb9c748: ret
    //     0xb9c748: ret             
    // 0xb9c74c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9c74c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9c750: b               #0xb9c6f0
    // 0xb9c754: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9c754: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xb9c758, size: 0x84
    // 0xb9c758: EnterFrame
    //     0xb9c758: stp             fp, lr, [SP, #-0x10]!
    //     0xb9c75c: mov             fp, SP
    // 0xb9c760: AllocStack(0x10)
    //     0xb9c760: sub             SP, SP, #0x10
    // 0xb9c764: SetupParameters()
    //     0xb9c764: ldr             x0, [fp, #0x18]
    //     0xb9c768: ldur            w1, [x0, #0x17]
    //     0xb9c76c: add             x1, x1, HEAP, lsl #32
    //     0xb9c770: stur            x1, [fp, #-8]
    // 0xb9c774: CheckStackOverflow
    //     0xb9c774: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9c778: cmp             SP, x16
    //     0xb9c77c: b.ls            #0xb9c7d4
    // 0xb9c780: r1 = 1
    //     0xb9c780: movz            x1, #0x1
    // 0xb9c784: r0 = AllocateContext()
    //     0xb9c784: bl              #0x16f6108  ; AllocateContextStub
    // 0xb9c788: mov             x1, x0
    // 0xb9c78c: ldur            x0, [fp, #-8]
    // 0xb9c790: StoreField: r1->field_b = r0
    //     0xb9c790: stur            w0, [x1, #0xb]
    // 0xb9c794: ldr             x2, [fp, #0x10]
    // 0xb9c798: StoreField: r1->field_f = r2
    //     0xb9c798: stur            w2, [x1, #0xf]
    // 0xb9c79c: LoadField: r3 = r0->field_f
    //     0xb9c79c: ldur            w3, [x0, #0xf]
    // 0xb9c7a0: DecompressPointer r3
    //     0xb9c7a0: add             x3, x3, HEAP, lsl #32
    // 0xb9c7a4: mov             x2, x1
    // 0xb9c7a8: stur            x3, [fp, #-0x10]
    // 0xb9c7ac: r1 = Function '<anonymous closure>':.
    //     0xb9c7ac: add             x1, PP, #0x54, lsl #12  ; [pp+0x54e60] AnonymousClosure: (0xb9c7dc), in [package:customer_app/app/presentation/views/glass/search/widgets/search_view.dart] _SearchViewState::build (0xb9ad68)
    //     0xb9c7b0: ldr             x1, [x1, #0xe60]
    // 0xb9c7b4: r0 = AllocateClosure()
    //     0xb9c7b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9c7b8: ldur            x1, [fp, #-0x10]
    // 0xb9c7bc: mov             x2, x0
    // 0xb9c7c0: r0 = setState()
    //     0xb9c7c0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb9c7c4: r0 = Null
    //     0xb9c7c4: mov             x0, NULL
    // 0xb9c7c8: LeaveFrame
    //     0xb9c7c8: mov             SP, fp
    //     0xb9c7cc: ldp             fp, lr, [SP], #0x10
    // 0xb9c7d0: ret
    //     0xb9c7d0: ret             
    // 0xb9c7d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9c7d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9c7d8: b               #0xb9c780
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb9c7dc, size: 0x1dc
    // 0xb9c7dc: EnterFrame
    //     0xb9c7dc: stp             fp, lr, [SP, #-0x10]!
    //     0xb9c7e0: mov             fp, SP
    // 0xb9c7e4: AllocStack(0x38)
    //     0xb9c7e4: sub             SP, SP, #0x38
    // 0xb9c7e8: SetupParameters()
    //     0xb9c7e8: ldr             x0, [fp, #0x10]
    //     0xb9c7ec: ldur            w3, [x0, #0x17]
    //     0xb9c7f0: add             x3, x3, HEAP, lsl #32
    //     0xb9c7f4: stur            x3, [fp, #-0x18]
    // 0xb9c7f8: CheckStackOverflow
    //     0xb9c7f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9c7fc: cmp             SP, x16
    //     0xb9c800: b.ls            #0xb9c9a4
    // 0xb9c804: LoadField: r0 = r3->field_b
    //     0xb9c804: ldur            w0, [x3, #0xb]
    // 0xb9c808: DecompressPointer r0
    //     0xb9c808: add             x0, x0, HEAP, lsl #32
    // 0xb9c80c: stur            x0, [fp, #-0x10]
    // 0xb9c810: LoadField: r4 = r0->field_f
    //     0xb9c810: ldur            w4, [x0, #0xf]
    // 0xb9c814: DecompressPointer r4
    //     0xb9c814: add             x4, x4, HEAP, lsl #32
    // 0xb9c818: stur            x4, [fp, #-8]
    // 0xb9c81c: LoadField: r2 = r3->field_f
    //     0xb9c81c: ldur            w2, [x3, #0xf]
    // 0xb9c820: DecompressPointer r2
    //     0xb9c820: add             x2, x2, HEAP, lsl #32
    // 0xb9c824: r1 = _ConstMap len:5
    //     0xb9c824: add             x1, PP, #0x51, lsl #12  ; [pp+0x51da8] Map<String, String>(5)
    //     0xb9c828: ldr             x1, [x1, #0xda8]
    // 0xb9c82c: r0 = []()
    //     0xb9c82c: bl              #0x16a3e7c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb9c830: cmp             w0, NULL
    // 0xb9c834: b.ne            #0xb9c854
    // 0xb9c838: ldur            x1, [fp, #-0x10]
    // 0xb9c83c: LoadField: r0 = r1->field_f
    //     0xb9c83c: ldur            w0, [x1, #0xf]
    // 0xb9c840: DecompressPointer r0
    //     0xb9c840: add             x0, x0, HEAP, lsl #32
    // 0xb9c844: LoadField: r2 = r0->field_1b
    //     0xb9c844: ldur            w2, [x0, #0x1b]
    // 0xb9c848: DecompressPointer r2
    //     0xb9c848: add             x2, x2, HEAP, lsl #32
    // 0xb9c84c: mov             x0, x2
    // 0xb9c850: b               #0xb9c858
    // 0xb9c854: ldur            x1, [fp, #-0x10]
    // 0xb9c858: ldur            x2, [fp, #-8]
    // 0xb9c85c: StoreField: r2->field_1b = r0
    //     0xb9c85c: stur            w0, [x2, #0x1b]
    //     0xb9c860: ldurb           w16, [x2, #-1]
    //     0xb9c864: ldurb           w17, [x0, #-1]
    //     0xb9c868: and             x16, x17, x16, lsr #2
    //     0xb9c86c: tst             x16, HEAP, lsr #32
    //     0xb9c870: b.eq            #0xb9c878
    //     0xb9c874: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb9c878: LoadField: r0 = r1->field_f
    //     0xb9c878: ldur            w0, [x1, #0xf]
    // 0xb9c87c: DecompressPointer r0
    //     0xb9c87c: add             x0, x0, HEAP, lsl #32
    // 0xb9c880: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb9c880: ldur            w2, [x0, #0x17]
    // 0xb9c884: DecompressPointer r2
    //     0xb9c884: add             x2, x2, HEAP, lsl #32
    // 0xb9c888: LoadField: r3 = r2->field_27
    //     0xb9c888: ldur            w3, [x2, #0x27]
    // 0xb9c88c: DecompressPointer r3
    //     0xb9c88c: add             x3, x3, HEAP, lsl #32
    // 0xb9c890: LoadField: r2 = r3->field_7
    //     0xb9c890: ldur            w2, [x3, #7]
    // 0xb9c894: DecompressPointer r2
    //     0xb9c894: add             x2, x2, HEAP, lsl #32
    // 0xb9c898: LoadField: r4 = r2->field_7
    //     0xb9c898: ldur            w4, [x2, #7]
    // 0xb9c89c: cbz             w4, #0xb9c8b0
    // 0xb9c8a0: LoadField: r2 = r3->field_7
    //     0xb9c8a0: ldur            w2, [x3, #7]
    // 0xb9c8a4: DecompressPointer r2
    //     0xb9c8a4: add             x2, x2, HEAP, lsl #32
    // 0xb9c8a8: mov             x4, x2
    // 0xb9c8ac: b               #0xb9c8b4
    // 0xb9c8b0: r4 = " "
    //     0xb9c8b0: ldr             x4, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb9c8b4: ldur            x2, [fp, #-0x18]
    // 0xb9c8b8: LoadField: r5 = r0->field_b
    //     0xb9c8b8: ldur            w5, [x0, #0xb]
    // 0xb9c8bc: DecompressPointer r5
    //     0xb9c8bc: add             x5, x5, HEAP, lsl #32
    // 0xb9c8c0: cmp             w5, NULL
    // 0xb9c8c4: b.eq            #0xb9c9ac
    // 0xb9c8c8: LoadField: r6 = r0->field_1b
    //     0xb9c8c8: ldur            w6, [x0, #0x1b]
    // 0xb9c8cc: DecompressPointer r6
    //     0xb9c8cc: add             x6, x6, HEAP, lsl #32
    // 0xb9c8d0: LoadField: r0 = r3->field_7
    //     0xb9c8d0: ldur            w0, [x3, #7]
    // 0xb9c8d4: DecompressPointer r0
    //     0xb9c8d4: add             x0, x0, HEAP, lsl #32
    // 0xb9c8d8: LoadField: r3 = r5->field_27
    //     0xb9c8d8: ldur            w3, [x5, #0x27]
    // 0xb9c8dc: DecompressPointer r3
    //     0xb9c8dc: add             x3, x3, HEAP, lsl #32
    // 0xb9c8e0: stp             x4, x3, [SP, #0x10]
    // 0xb9c8e4: stp             x0, x6, [SP]
    // 0xb9c8e8: r4 = 0
    //     0xb9c8e8: movz            x4, #0
    // 0xb9c8ec: ldr             x0, [SP, #0x18]
    // 0xb9c8f0: r16 = UnlinkedCall_0x613b5c
    //     0xb9c8f0: add             x16, PP, #0x54, lsl #12  ; [pp+0x54e68] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb9c8f4: add             x16, x16, #0xe68
    // 0xb9c8f8: ldp             x5, lr, [x16]
    // 0xb9c8fc: blr             lr
    // 0xb9c900: ldur            x0, [fp, #-0x10]
    // 0xb9c904: LoadField: r1 = r0->field_f
    //     0xb9c904: ldur            w1, [x0, #0xf]
    // 0xb9c908: DecompressPointer r1
    //     0xb9c908: add             x1, x1, HEAP, lsl #32
    // 0xb9c90c: LoadField: r3 = r1->field_b
    //     0xb9c90c: ldur            w3, [x1, #0xb]
    // 0xb9c910: DecompressPointer r3
    //     0xb9c910: add             x3, x3, HEAP, lsl #32
    // 0xb9c914: stur            x3, [fp, #-0x10]
    // 0xb9c918: cmp             w3, NULL
    // 0xb9c91c: b.eq            #0xb9c9b0
    // 0xb9c920: ldur            x0, [fp, #-0x18]
    // 0xb9c924: LoadField: r4 = r0->field_f
    //     0xb9c924: ldur            w4, [x0, #0xf]
    // 0xb9c928: DecompressPointer r4
    //     0xb9c928: add             x4, x4, HEAP, lsl #32
    // 0xb9c92c: stur            x4, [fp, #-8]
    // 0xb9c930: cmp             w4, NULL
    // 0xb9c934: b.eq            #0xb9c9b4
    // 0xb9c938: mov             x0, x4
    // 0xb9c93c: r2 = Null
    //     0xb9c93c: mov             x2, NULL
    // 0xb9c940: r1 = Null
    //     0xb9c940: mov             x1, NULL
    // 0xb9c944: r4 = 60
    //     0xb9c944: movz            x4, #0x3c
    // 0xb9c948: branchIfSmi(r0, 0xb9c954)
    //     0xb9c948: tbz             w0, #0, #0xb9c954
    // 0xb9c94c: r4 = LoadClassIdInstr(r0)
    //     0xb9c94c: ldur            x4, [x0, #-1]
    //     0xb9c950: ubfx            x4, x4, #0xc, #0x14
    // 0xb9c954: sub             x4, x4, #0x5e
    // 0xb9c958: cmp             x4, #1
    // 0xb9c95c: b.ls            #0xb9c970
    // 0xb9c960: r8 = String
    //     0xb9c960: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb9c964: r3 = Null
    //     0xb9c964: add             x3, PP, #0x54, lsl #12  ; [pp+0x54e78] Null
    //     0xb9c968: ldr             x3, [x3, #0xe78]
    // 0xb9c96c: r0 = String()
    //     0xb9c96c: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb9c970: ldur            x0, [fp, #-8]
    // 0xb9c974: ldur            x1, [fp, #-0x10]
    // 0xb9c978: StoreField: r1->field_f = r0
    //     0xb9c978: stur            w0, [x1, #0xf]
    //     0xb9c97c: ldurb           w16, [x1, #-1]
    //     0xb9c980: ldurb           w17, [x0, #-1]
    //     0xb9c984: and             x16, x17, x16, lsr #2
    //     0xb9c988: tst             x16, HEAP, lsr #32
    //     0xb9c98c: b.eq            #0xb9c994
    //     0xb9c990: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xb9c994: r0 = Null
    //     0xb9c994: mov             x0, NULL
    // 0xb9c998: LeaveFrame
    //     0xb9c998: mov             SP, fp
    //     0xb9c99c: ldp             fp, lr, [SP], #0x10
    // 0xb9c9a0: ret
    //     0xb9c9a0: ret             
    // 0xb9c9a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9c9a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9c9a8: b               #0xb9c804
    // 0xb9c9ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9c9ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9c9b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9c9b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9c9b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9c9b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb9c9b8, size: 0xa8
    // 0xb9c9b8: EnterFrame
    //     0xb9c9b8: stp             fp, lr, [SP, #-0x10]!
    //     0xb9c9bc: mov             fp, SP
    // 0xb9c9c0: AllocStack(0x18)
    //     0xb9c9c0: sub             SP, SP, #0x18
    // 0xb9c9c4: SetupParameters()
    //     0xb9c9c4: ldr             x0, [fp, #0x10]
    //     0xb9c9c8: ldur            w2, [x0, #0x17]
    //     0xb9c9cc: add             x2, x2, HEAP, lsl #32
    //     0xb9c9d0: stur            x2, [fp, #-8]
    // 0xb9c9d4: CheckStackOverflow
    //     0xb9c9d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9c9d8: cmp             SP, x16
    //     0xb9c9dc: b.ls            #0xb9ca54
    // 0xb9c9e0: LoadField: r0 = r2->field_f
    //     0xb9c9e0: ldur            w0, [x2, #0xf]
    // 0xb9c9e4: DecompressPointer r0
    //     0xb9c9e4: add             x0, x0, HEAP, lsl #32
    // 0xb9c9e8: LoadField: r1 = r0->field_b
    //     0xb9c9e8: ldur            w1, [x0, #0xb]
    // 0xb9c9ec: DecompressPointer r1
    //     0xb9c9ec: add             x1, x1, HEAP, lsl #32
    // 0xb9c9f0: cmp             w1, NULL
    // 0xb9c9f4: b.eq            #0xb9ca5c
    // 0xb9c9f8: LoadField: r0 = r1->field_23
    //     0xb9c9f8: ldur            w0, [x1, #0x23]
    // 0xb9c9fc: DecompressPointer r0
    //     0xb9c9fc: add             x0, x0, HEAP, lsl #32
    // 0xb9ca00: str             x0, [SP]
    // 0xb9ca04: r4 = 0
    //     0xb9ca04: movz            x4, #0
    // 0xb9ca08: ldr             x0, [SP]
    // 0xb9ca0c: r16 = UnlinkedCall_0x613b5c
    //     0xb9ca0c: add             x16, PP, #0x54, lsl #12  ; [pp+0x54e88] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb9ca10: add             x16, x16, #0xe88
    // 0xb9ca14: ldp             x5, lr, [x16]
    // 0xb9ca18: blr             lr
    // 0xb9ca1c: ldur            x2, [fp, #-8]
    // 0xb9ca20: LoadField: r0 = r2->field_f
    //     0xb9ca20: ldur            w0, [x2, #0xf]
    // 0xb9ca24: DecompressPointer r0
    //     0xb9ca24: add             x0, x0, HEAP, lsl #32
    // 0xb9ca28: stur            x0, [fp, #-0x10]
    // 0xb9ca2c: r1 = Function '<anonymous closure>':.
    //     0xb9ca2c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54e98] AnonymousClosure: (0xaacdec), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xb9ca30: ldr             x1, [x1, #0xe98]
    // 0xb9ca34: r0 = AllocateClosure()
    //     0xb9ca34: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9ca38: ldur            x1, [fp, #-0x10]
    // 0xb9ca3c: mov             x2, x0
    // 0xb9ca40: r0 = setState()
    //     0xb9ca40: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb9ca44: r0 = Null
    //     0xb9ca44: mov             x0, NULL
    // 0xb9ca48: LeaveFrame
    //     0xb9ca48: mov             SP, fp
    //     0xb9ca4c: ldp             fp, lr, [SP], #0x10
    // 0xb9ca50: ret
    //     0xb9ca50: ret             
    // 0xb9ca54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9ca54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9ca58: b               #0xb9c9e0
    // 0xb9ca5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9ca5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic, String) async {
    // ** addr: 0xb9ca60, size: 0x188
    // 0xb9ca60: EnterFrame
    //     0xb9ca60: stp             fp, lr, [SP, #-0x10]!
    //     0xb9ca64: mov             fp, SP
    // 0xb9ca68: AllocStack(0x38)
    //     0xb9ca68: sub             SP, SP, #0x38
    // 0xb9ca6c: SetupParameters(_SearchViewState this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xb9ca6c: stur            NULL, [fp, #-8]
    //     0xb9ca70: movz            x0, #0
    //     0xb9ca74: add             x1, fp, w0, sxtw #2
    //     0xb9ca78: ldr             x1, [x1, #0x18]
    //     0xb9ca7c: add             x2, fp, w0, sxtw #2
    //     0xb9ca80: ldr             x2, [x2, #0x10]
    //     0xb9ca84: stur            x2, [fp, #-0x18]
    //     0xb9ca88: ldur            w3, [x1, #0x17]
    //     0xb9ca8c: add             x3, x3, HEAP, lsl #32
    //     0xb9ca90: stur            x3, [fp, #-0x10]
    // 0xb9ca94: CheckStackOverflow
    //     0xb9ca94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9ca98: cmp             SP, x16
    //     0xb9ca9c: b.ls            #0xb9cbdc
    // 0xb9caa0: InitAsync() -> Future<void?>
    //     0xb9caa0: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0xb9caa4: bl              #0x6326e0  ; InitAsyncStub
    // 0xb9caa8: r1 = 1
    //     0xb9caa8: movz            x1, #0x1
    // 0xb9caac: r0 = AllocateContext()
    //     0xb9caac: bl              #0x16f6108  ; AllocateContextStub
    // 0xb9cab0: mov             x2, x0
    // 0xb9cab4: ldur            x0, [fp, #-0x10]
    // 0xb9cab8: stur            x2, [fp, #-0x20]
    // 0xb9cabc: StoreField: r2->field_b = r0
    //     0xb9cabc: stur            w0, [x2, #0xb]
    // 0xb9cac0: ldur            x1, [fp, #-0x18]
    // 0xb9cac4: r0 = trim()
    //     0xb9cac4: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb9cac8: mov             x1, x0
    // 0xb9cacc: ldur            x2, [fp, #-0x20]
    // 0xb9cad0: StoreField: r2->field_f = r0
    //     0xb9cad0: stur            w0, [x2, #0xf]
    //     0xb9cad4: ldurb           w16, [x2, #-1]
    //     0xb9cad8: ldurb           w17, [x0, #-1]
    //     0xb9cadc: and             x16, x17, x16, lsr #2
    //     0xb9cae0: tst             x16, HEAP, lsr #32
    //     0xb9cae4: b.eq            #0xb9caec
    //     0xb9cae8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb9caec: LoadField: r0 = r1->field_7
    //     0xb9caec: ldur            w0, [x1, #7]
    // 0xb9caf0: cbz             w0, #0xb9cba8
    // 0xb9caf4: ldur            x3, [fp, #-0x10]
    // 0xb9caf8: r4 = true
    //     0xb9caf8: add             x4, NULL, #0x20  ; true
    // 0xb9cafc: LoadField: r5 = r3->field_f
    //     0xb9cafc: ldur            w5, [x3, #0xf]
    // 0xb9cb00: DecompressPointer r5
    //     0xb9cb00: add             x5, x5, HEAP, lsl #32
    // 0xb9cb04: stur            x5, [fp, #-0x28]
    // 0xb9cb08: StoreField: r5->field_13 = r4
    //     0xb9cb08: stur            w4, [x5, #0x13]
    // 0xb9cb0c: r4 = LoadInt32Instr(r0)
    //     0xb9cb0c: sbfx            x4, x0, #1, #0x1f
    // 0xb9cb10: cmp             x4, #3
    // 0xb9cb14: b.lt            #0xb9cb88
    // 0xb9cb18: LoadField: r0 = r5->field_b
    //     0xb9cb18: ldur            w0, [x5, #0xb]
    // 0xb9cb1c: DecompressPointer r0
    //     0xb9cb1c: add             x0, x0, HEAP, lsl #32
    // 0xb9cb20: cmp             w0, NULL
    // 0xb9cb24: b.eq            #0xb9cbe4
    // 0xb9cb28: LoadField: r4 = r0->field_1b
    //     0xb9cb28: ldur            w4, [x0, #0x1b]
    // 0xb9cb2c: DecompressPointer r4
    //     0xb9cb2c: add             x4, x4, HEAP, lsl #32
    // 0xb9cb30: stp             x1, x4, [SP]
    // 0xb9cb34: r4 = 0
    //     0xb9cb34: movz            x4, #0
    // 0xb9cb38: ldr             x0, [SP, #8]
    // 0xb9cb3c: r16 = UnlinkedCall_0x613b5c
    //     0xb9cb3c: add             x16, PP, #0x54, lsl #12  ; [pp+0x54ea0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb9cb40: add             x16, x16, #0xea0
    // 0xb9cb44: ldp             x5, lr, [x16]
    // 0xb9cb48: blr             lr
    // 0xb9cb4c: mov             x1, x0
    // 0xb9cb50: stur            x1, [fp, #-0x18]
    // 0xb9cb54: r0 = Await()
    //     0xb9cb54: bl              #0x63248c  ; AwaitStub
    // 0xb9cb58: ldur            x0, [fp, #-0x10]
    // 0xb9cb5c: LoadField: r3 = r0->field_f
    //     0xb9cb5c: ldur            w3, [x0, #0xf]
    // 0xb9cb60: DecompressPointer r3
    //     0xb9cb60: add             x3, x3, HEAP, lsl #32
    // 0xb9cb64: ldur            x2, [fp, #-0x20]
    // 0xb9cb68: stur            x3, [fp, #-0x18]
    // 0xb9cb6c: r1 = Function '<anonymous closure>':.
    //     0xb9cb6c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54eb0] AnonymousClosure: (0xaad370), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xb9cb70: ldr             x1, [x1, #0xeb0]
    // 0xb9cb74: r0 = AllocateClosure()
    //     0xb9cb74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9cb78: ldur            x1, [fp, #-0x18]
    // 0xb9cb7c: mov             x2, x0
    // 0xb9cb80: r0 = setState()
    //     0xb9cb80: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb9cb84: b               #0xb9cbd4
    // 0xb9cb88: ldur            x2, [fp, #-0x20]
    // 0xb9cb8c: r1 = Function '<anonymous closure>':.
    //     0xb9cb8c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54eb8] AnonymousClosure: (0xaad124), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xb9cb90: ldr             x1, [x1, #0xeb8]
    // 0xb9cb94: r0 = AllocateClosure()
    //     0xb9cb94: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9cb98: ldur            x1, [fp, #-0x28]
    // 0xb9cb9c: mov             x2, x0
    // 0xb9cba0: r0 = setState()
    //     0xb9cba0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb9cba4: b               #0xb9cbd4
    // 0xb9cba8: ldur            x0, [fp, #-0x10]
    // 0xb9cbac: LoadField: r3 = r0->field_f
    //     0xb9cbac: ldur            w3, [x0, #0xf]
    // 0xb9cbb0: DecompressPointer r3
    //     0xb9cbb0: add             x3, x3, HEAP, lsl #32
    // 0xb9cbb4: ldur            x2, [fp, #-0x20]
    // 0xb9cbb8: stur            x3, [fp, #-0x18]
    // 0xb9cbbc: r1 = Function '<anonymous closure>':.
    //     0xb9cbbc: add             x1, PP, #0x54, lsl #12  ; [pp+0x54ec0] AnonymousClosure: (0xb9cbe8), in [package:customer_app/app/presentation/views/glass/search/widgets/search_view.dart] _SearchViewState::build (0xb9ad68)
    //     0xb9cbc0: ldr             x1, [x1, #0xec0]
    // 0xb9cbc4: r0 = AllocateClosure()
    //     0xb9cbc4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9cbc8: ldur            x1, [fp, #-0x18]
    // 0xb9cbcc: mov             x2, x0
    // 0xb9cbd0: r0 = setState()
    //     0xb9cbd0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb9cbd4: r0 = Null
    //     0xb9cbd4: mov             x0, NULL
    // 0xb9cbd8: r0 = ReturnAsyncNotFuture()
    //     0xb9cbd8: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xb9cbdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9cbdc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9cbe0: b               #0xb9caa0
    // 0xb9cbe4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9cbe4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb9cbe8, size: 0x98
    // 0xb9cbe8: EnterFrame
    //     0xb9cbe8: stp             fp, lr, [SP, #-0x10]!
    //     0xb9cbec: mov             fp, SP
    // 0xb9cbf0: AllocStack(0x10)
    //     0xb9cbf0: sub             SP, SP, #0x10
    // 0xb9cbf4: SetupParameters()
    //     0xb9cbf4: add             x0, NULL, #0x30  ; false
    //     0xb9cbf8: ldr             x1, [fp, #0x10]
    //     0xb9cbfc: ldur            w2, [x1, #0x17]
    //     0xb9cc00: add             x2, x2, HEAP, lsl #32
    // 0xb9cbf4: r0 = false
    // 0xb9cc04: CheckStackOverflow
    //     0xb9cc04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9cc08: cmp             SP, x16
    //     0xb9cc0c: b.ls            #0xb9cc74
    // 0xb9cc10: LoadField: r1 = r2->field_b
    //     0xb9cc10: ldur            w1, [x2, #0xb]
    // 0xb9cc14: DecompressPointer r1
    //     0xb9cc14: add             x1, x1, HEAP, lsl #32
    // 0xb9cc18: LoadField: r3 = r1->field_f
    //     0xb9cc18: ldur            w3, [x1, #0xf]
    // 0xb9cc1c: DecompressPointer r3
    //     0xb9cc1c: add             x3, x3, HEAP, lsl #32
    // 0xb9cc20: StoreField: r3->field_13 = r0
    //     0xb9cc20: stur            w0, [x3, #0x13]
    // 0xb9cc24: StoreField: r3->field_1f = r0
    //     0xb9cc24: stur            w0, [x3, #0x1f]
    // 0xb9cc28: LoadField: r0 = r3->field_b
    //     0xb9cc28: ldur            w0, [x3, #0xb]
    // 0xb9cc2c: DecompressPointer r0
    //     0xb9cc2c: add             x0, x0, HEAP, lsl #32
    // 0xb9cc30: cmp             w0, NULL
    // 0xb9cc34: b.eq            #0xb9cc7c
    // 0xb9cc38: LoadField: r1 = r2->field_f
    //     0xb9cc38: ldur            w1, [x2, #0xf]
    // 0xb9cc3c: DecompressPointer r1
    //     0xb9cc3c: add             x1, x1, HEAP, lsl #32
    // 0xb9cc40: LoadField: r2 = r0->field_1f
    //     0xb9cc40: ldur            w2, [x0, #0x1f]
    // 0xb9cc44: DecompressPointer r2
    //     0xb9cc44: add             x2, x2, HEAP, lsl #32
    // 0xb9cc48: stp             x1, x2, [SP]
    // 0xb9cc4c: r4 = 0
    //     0xb9cc4c: movz            x4, #0
    // 0xb9cc50: ldr             x0, [SP, #8]
    // 0xb9cc54: r16 = UnlinkedCall_0x613b5c
    //     0xb9cc54: add             x16, PP, #0x54, lsl #12  ; [pp+0x54ec8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb9cc58: add             x16, x16, #0xec8
    // 0xb9cc5c: ldp             x5, lr, [x16]
    // 0xb9cc60: blr             lr
    // 0xb9cc64: r0 = Null
    //     0xb9cc64: mov             x0, NULL
    // 0xb9cc68: LeaveFrame
    //     0xb9cc68: mov             SP, fp
    //     0xb9cc6c: ldp             fp, lr, [SP], #0x10
    // 0xb9cc70: ret
    //     0xb9cc70: ret             
    // 0xb9cc74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9cc74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9cc78: b               #0xb9cc10
    // 0xb9cc7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9cc7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0xb9cc80, size: 0xb0
    // 0xb9cc80: EnterFrame
    //     0xb9cc80: stp             fp, lr, [SP, #-0x10]!
    //     0xb9cc84: mov             fp, SP
    // 0xb9cc88: AllocStack(0x20)
    //     0xb9cc88: sub             SP, SP, #0x20
    // 0xb9cc8c: SetupParameters()
    //     0xb9cc8c: ldr             x0, [fp, #0x18]
    //     0xb9cc90: ldur            w2, [x0, #0x17]
    //     0xb9cc94: add             x2, x2, HEAP, lsl #32
    //     0xb9cc98: stur            x2, [fp, #-8]
    // 0xb9cc9c: CheckStackOverflow
    //     0xb9cc9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9cca0: cmp             SP, x16
    //     0xb9cca4: b.ls            #0xb9cd24
    // 0xb9cca8: ldr             x1, [fp, #0x10]
    // 0xb9ccac: r0 = trim()
    //     0xb9ccac: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb9ccb0: mov             x1, x0
    // 0xb9ccb4: ldur            x0, [fp, #-8]
    // 0xb9ccb8: LoadField: r2 = r0->field_f
    //     0xb9ccb8: ldur            w2, [x0, #0xf]
    // 0xb9ccbc: DecompressPointer r2
    //     0xb9ccbc: add             x2, x2, HEAP, lsl #32
    // 0xb9ccc0: LoadField: r3 = r2->field_b
    //     0xb9ccc0: ldur            w3, [x2, #0xb]
    // 0xb9ccc4: DecompressPointer r3
    //     0xb9ccc4: add             x3, x3, HEAP, lsl #32
    // 0xb9ccc8: cmp             w3, NULL
    // 0xb9cccc: b.eq            #0xb9cd2c
    // 0xb9ccd0: LoadField: r4 = r2->field_1b
    //     0xb9ccd0: ldur            w4, [x2, #0x1b]
    // 0xb9ccd4: DecompressPointer r4
    //     0xb9ccd4: add             x4, x4, HEAP, lsl #32
    // 0xb9ccd8: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xb9ccd8: ldur            w2, [x3, #0x17]
    // 0xb9ccdc: DecompressPointer r2
    //     0xb9ccdc: add             x2, x2, HEAP, lsl #32
    // 0xb9cce0: stp             x1, x2, [SP, #8]
    // 0xb9cce4: str             x4, [SP]
    // 0xb9cce8: r4 = 0
    //     0xb9cce8: movz            x4, #0
    // 0xb9ccec: ldr             x0, [SP, #0x10]
    // 0xb9ccf0: r16 = UnlinkedCall_0x613b5c
    //     0xb9ccf0: add             x16, PP, #0x54, lsl #12  ; [pp+0x54ed8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb9ccf4: add             x16, x16, #0xed8
    // 0xb9ccf8: ldp             x5, lr, [x16]
    // 0xb9ccfc: blr             lr
    // 0xb9cd00: ldur            x1, [fp, #-8]
    // 0xb9cd04: LoadField: r2 = r1->field_f
    //     0xb9cd04: ldur            w2, [x1, #0xf]
    // 0xb9cd08: DecompressPointer r2
    //     0xb9cd08: add             x2, x2, HEAP, lsl #32
    // 0xb9cd0c: r1 = false
    //     0xb9cd0c: add             x1, NULL, #0x30  ; false
    // 0xb9cd10: StoreField: r2->field_1f = r1
    //     0xb9cd10: stur            w1, [x2, #0x1f]
    // 0xb9cd14: r0 = Null
    //     0xb9cd14: mov             x0, NULL
    // 0xb9cd18: LeaveFrame
    //     0xb9cd18: mov             SP, fp
    //     0xb9cd1c: ldp             fp, lr, [SP], #0x10
    // 0xb9cd20: ret
    //     0xb9cd20: ret             
    // 0xb9cd24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9cd24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9cd28: b               #0xb9cca8
    // 0xb9cd2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9cd2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4040, size: 0x48, field offset: 0xc
class SearchView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7fd24, size: 0x48
    // 0xc7fd24: EnterFrame
    //     0xc7fd24: stp             fp, lr, [SP, #-0x10]!
    //     0xc7fd28: mov             fp, SP
    // 0xc7fd2c: AllocStack(0x8)
    //     0xc7fd2c: sub             SP, SP, #8
    // 0xc7fd30: CheckStackOverflow
    //     0xc7fd30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7fd34: cmp             SP, x16
    //     0xc7fd38: b.ls            #0xc7fd64
    // 0xc7fd3c: r1 = <SearchView>
    //     0xc7fd3c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48700] TypeArguments: <SearchView>
    //     0xc7fd40: ldr             x1, [x1, #0x700]
    // 0xc7fd44: r0 = _SearchViewState()
    //     0xc7fd44: bl              #0xc7fd6c  ; Allocate_SearchViewStateStub -> _SearchViewState (size=0x24)
    // 0xc7fd48: mov             x1, x0
    // 0xc7fd4c: stur            x0, [fp, #-8]
    // 0xc7fd50: r0 = _SearchViewState()
    //     0xc7fd50: bl              #0xc7ccc4  ; [package:customer_app/app/presentation/views/basic/search/widgets/search_view.dart] _SearchViewState::_SearchViewState
    // 0xc7fd54: ldur            x0, [fp, #-8]
    // 0xc7fd58: LeaveFrame
    //     0xc7fd58: mov             SP, fp
    //     0xc7fd5c: ldp             fp, lr, [SP], #0x10
    // 0xc7fd60: ret
    //     0xc7fd60: ret             
    // 0xc7fd64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7fd64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7fd68: b               #0xc7fd3c
  }
}
