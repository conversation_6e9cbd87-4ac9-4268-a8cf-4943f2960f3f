// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart

// class id: 1049489, size: 0x8
class :: {
}

// class id: 3275, size: 0x14, field offset: 0x14
//   transformed mixin,
abstract class __CheckoutOtpWidgetState&State&CodeAutoFill extends State<dynamic>
     with CodeAutoFill {
}

// class id: 3276, size: 0x20, field offset: 0x14
class _CheckoutOtpWidgetState extends __CheckoutOtpWidgetState&State&CodeAutoFill {

  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x905e34, size: 0x6c
    // 0x905e34: EnterFrame
    //     0x905e34: stp             fp, lr, [SP, #-0x10]!
    //     0x905e38: mov             fp, SP
    // 0x905e3c: AllocStack(0x8)
    //     0x905e3c: sub             SP, SP, #8
    // 0x905e40: SetupParameters()
    //     0x905e40: ldr             x0, [fp, #0x18]
    //     0x905e44: ldur            w1, [x0, #0x17]
    //     0x905e48: add             x1, x1, HEAP, lsl #32
    // 0x905e4c: CheckStackOverflow
    //     0x905e4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x905e50: cmp             SP, x16
    //     0x905e54: b.ls            #0x905e94
    // 0x905e58: LoadField: r0 = r1->field_f
    //     0x905e58: ldur            w0, [x1, #0xf]
    // 0x905e5c: DecompressPointer r0
    //     0x905e5c: add             x0, x0, HEAP, lsl #32
    // 0x905e60: LoadField: r1 = r0->field_b
    //     0x905e60: ldur            w1, [x0, #0xb]
    // 0x905e64: DecompressPointer r1
    //     0x905e64: add             x1, x1, HEAP, lsl #32
    // 0x905e68: cmp             w1, NULL
    // 0x905e6c: b.eq            #0x905e9c
    // 0x905e70: LoadField: r0 = r1->field_13
    //     0x905e70: ldur            w0, [x1, #0x13]
    // 0x905e74: DecompressPointer r0
    //     0x905e74: add             x0, x0, HEAP, lsl #32
    // 0x905e78: str             x0, [SP]
    // 0x905e7c: ClosureCall
    //     0x905e7c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x905e80: ldur            x2, [x0, #0x1f]
    //     0x905e84: blr             x2
    // 0x905e88: LeaveFrame
    //     0x905e88: mov             SP, fp
    //     0x905e8c: ldp             fp, lr, [SP], #0x10
    // 0x905e90: ret
    //     0x905e90: ret             
    // 0x905e94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x905e94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x905e98: b               #0x905e58
    // 0x905e9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x905e9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x947e54, size: 0x138
    // 0x947e54: EnterFrame
    //     0x947e54: stp             fp, lr, [SP, #-0x10]!
    //     0x947e58: mov             fp, SP
    // 0x947e5c: AllocStack(0x20)
    //     0x947e5c: sub             SP, SP, #0x20
    // 0x947e60: SetupParameters(_CheckoutOtpWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x947e60: stur            x1, [fp, #-8]
    // 0x947e64: CheckStackOverflow
    //     0x947e64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x947e68: cmp             SP, x16
    //     0x947e6c: b.ls            #0x947f80
    // 0x947e70: r1 = 1
    //     0x947e70: movz            x1, #0x1
    // 0x947e74: r0 = AllocateContext()
    //     0x947e74: bl              #0x16f6108  ; AllocateContextStub
    // 0x947e78: mov             x1, x0
    // 0x947e7c: ldur            x0, [fp, #-8]
    // 0x947e80: StoreField: r1->field_f = r0
    //     0x947e80: stur            w0, [x1, #0xf]
    // 0x947e84: r2 = LoadStaticField(0x878)
    //     0x947e84: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x947e88: ldr             x2, [x2, #0x10f0]
    // 0x947e8c: cmp             w2, NULL
    // 0x947e90: b.eq            #0x947f88
    // 0x947e94: LoadField: r3 = r2->field_53
    //     0x947e94: ldur            w3, [x2, #0x53]
    // 0x947e98: DecompressPointer r3
    //     0x947e98: add             x3, x3, HEAP, lsl #32
    // 0x947e9c: stur            x3, [fp, #-0x18]
    // 0x947ea0: LoadField: r4 = r3->field_7
    //     0x947ea0: ldur            w4, [x3, #7]
    // 0x947ea4: DecompressPointer r4
    //     0x947ea4: add             x4, x4, HEAP, lsl #32
    // 0x947ea8: mov             x2, x1
    // 0x947eac: stur            x4, [fp, #-0x10]
    // 0x947eb0: r1 = Function '<anonymous closure>':.
    //     0x947eb0: add             x1, PP, #0x53, lsl #12  ; [pp+0x53fa0] AnonymousClosure: (0x905e34), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::initState (0x947e54)
    //     0x947eb4: ldr             x1, [x1, #0xfa0]
    // 0x947eb8: r0 = AllocateClosure()
    //     0x947eb8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x947ebc: ldur            x2, [fp, #-0x10]
    // 0x947ec0: mov             x3, x0
    // 0x947ec4: r1 = Null
    //     0x947ec4: mov             x1, NULL
    // 0x947ec8: stur            x3, [fp, #-0x10]
    // 0x947ecc: cmp             w2, NULL
    // 0x947ed0: b.eq            #0x947ef0
    // 0x947ed4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x947ed4: ldur            w4, [x2, #0x17]
    // 0x947ed8: DecompressPointer r4
    //     0x947ed8: add             x4, x4, HEAP, lsl #32
    // 0x947edc: r8 = X0
    //     0x947edc: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x947ee0: LoadField: r9 = r4->field_7
    //     0x947ee0: ldur            x9, [x4, #7]
    // 0x947ee4: r3 = Null
    //     0x947ee4: add             x3, PP, #0x53, lsl #12  ; [pp+0x53fa8] Null
    //     0x947ee8: ldr             x3, [x3, #0xfa8]
    // 0x947eec: blr             x9
    // 0x947ef0: ldur            x0, [fp, #-0x18]
    // 0x947ef4: LoadField: r1 = r0->field_b
    //     0x947ef4: ldur            w1, [x0, #0xb]
    // 0x947ef8: LoadField: r2 = r0->field_f
    //     0x947ef8: ldur            w2, [x0, #0xf]
    // 0x947efc: DecompressPointer r2
    //     0x947efc: add             x2, x2, HEAP, lsl #32
    // 0x947f00: LoadField: r3 = r2->field_b
    //     0x947f00: ldur            w3, [x2, #0xb]
    // 0x947f04: r2 = LoadInt32Instr(r1)
    //     0x947f04: sbfx            x2, x1, #1, #0x1f
    // 0x947f08: stur            x2, [fp, #-0x20]
    // 0x947f0c: r1 = LoadInt32Instr(r3)
    //     0x947f0c: sbfx            x1, x3, #1, #0x1f
    // 0x947f10: cmp             x2, x1
    // 0x947f14: b.ne            #0x947f20
    // 0x947f18: mov             x1, x0
    // 0x947f1c: r0 = _growToNextCapacity()
    //     0x947f1c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x947f20: ldur            x0, [fp, #-0x18]
    // 0x947f24: ldur            x2, [fp, #-0x20]
    // 0x947f28: add             x1, x2, #1
    // 0x947f2c: lsl             x3, x1, #1
    // 0x947f30: StoreField: r0->field_b = r3
    //     0x947f30: stur            w3, [x0, #0xb]
    // 0x947f34: LoadField: r1 = r0->field_f
    //     0x947f34: ldur            w1, [x0, #0xf]
    // 0x947f38: DecompressPointer r1
    //     0x947f38: add             x1, x1, HEAP, lsl #32
    // 0x947f3c: ldur            x0, [fp, #-0x10]
    // 0x947f40: ArrayStore: r1[r2] = r0  ; List_4
    //     0x947f40: add             x25, x1, x2, lsl #2
    //     0x947f44: add             x25, x25, #0xf
    //     0x947f48: str             w0, [x25]
    //     0x947f4c: tbz             w0, #0, #0x947f68
    //     0x947f50: ldurb           w16, [x1, #-1]
    //     0x947f54: ldurb           w17, [x0, #-1]
    //     0x947f58: and             x16, x17, x16, lsr #2
    //     0x947f5c: tst             x16, HEAP, lsr #32
    //     0x947f60: b.eq            #0x947f68
    //     0x947f64: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x947f68: ldur            x1, [fp, #-8]
    // 0x947f6c: r0 = registerOtpListenListener()
    //     0x947f6c: bl              #0x905b7c  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::registerOtpListenListener
    // 0x947f70: r0 = Null
    //     0x947f70: mov             x0, NULL
    // 0x947f74: LeaveFrame
    //     0x947f74: mov             SP, fp
    //     0x947f78: ldp             fp, lr, [SP], #0x10
    // 0x947f7c: ret
    //     0x947f7c: ret             
    // 0x947f80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x947f80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x947f84: b               #0x947e70
    // 0x947f88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x947f88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] int <anonymous closure>(dynamic, int) {
    // ** addr: 0xa0a694, size: 0x44
    // 0xa0a694: r2 = 30
    //     0xa0a694: movz            x2, #0x1e
    // 0xa0a698: ldr             x3, [SP]
    // 0xa0a69c: r4 = LoadInt32Instr(r3)
    //     0xa0a69c: sbfx            x4, x3, #1, #0x1f
    //     0xa0a6a0: tbz             w3, #0, #0xa0a6a8
    //     0xa0a6a4: ldur            x4, [x3, #7]
    // 0xa0a6a8: sub             x3, x2, x4
    // 0xa0a6ac: sub             x2, x3, #1
    // 0xa0a6b0: r0 = BoxInt64Instr(r2)
    //     0xa0a6b0: sbfiz           x0, x2, #1, #0x1f
    //     0xa0a6b4: cmp             x2, x0, asr #1
    //     0xa0a6b8: b.eq            #0xa0a6d4
    //     0xa0a6bc: stp             fp, lr, [SP, #-0x10]!
    //     0xa0a6c0: mov             fp, SP
    //     0xa0a6c4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa0a6c8: mov             SP, fp
    //     0xa0a6cc: ldp             fp, lr, [SP], #0x10
    //     0xa0a6d0: stur            x2, [x0, #7]
    // 0xa0a6d4: ret
    //     0xa0a6d4: ret             
  }
  _ _CheckoutOtpWidgetState(/* No info */) {
    // ** addr: 0xa0a6d8, size: 0x98
    // 0xa0a6d8: EnterFrame
    //     0xa0a6d8: stp             fp, lr, [SP, #-0x10]!
    //     0xa0a6dc: mov             fp, SP
    // 0xa0a6e0: AllocStack(0x8)
    //     0xa0a6e0: sub             SP, SP, #8
    // 0xa0a6e4: r2 = false
    //     0xa0a6e4: add             x2, NULL, #0x30  ; false
    // 0xa0a6e8: r0 = ""
    //     0xa0a6e8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa0a6ec: mov             x3, x1
    // 0xa0a6f0: stur            x1, [fp, #-8]
    // 0xa0a6f4: CheckStackOverflow
    //     0xa0a6f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0a6f8: cmp             SP, x16
    //     0xa0a6fc: b.ls            #0xa0a768
    // 0xa0a700: StoreField: r3->field_13 = r2
    //     0xa0a700: stur            w2, [x3, #0x13]
    // 0xa0a704: ArrayStore: r3[0] = r0  ; List_4
    //     0xa0a704: stur            w0, [x3, #0x17]
    // 0xa0a708: r1 = Function '<anonymous closure>':.
    //     0xa0a708: add             x1, PP, #0x48, lsl #12  ; [pp+0x48620] AnonymousClosure: (0xa0a694), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::_CheckoutOtpWidgetState (0xa0a6d8)
    //     0xa0a70c: ldr             x1, [x1, #0x620]
    // 0xa0a710: r2 = Null
    //     0xa0a710: mov             x2, NULL
    // 0xa0a714: r0 = AllocateClosure()
    //     0xa0a714: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa0a718: mov             x2, x0
    // 0xa0a71c: r1 = <int>
    //     0xa0a71c: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xa0a720: r0 = Stream.periodic()
    //     0xa0a720: bl              #0xa0a0f8  ; [dart:async] Stream::Stream.periodic
    // 0xa0a724: mov             x1, x0
    // 0xa0a728: r2 = 30
    //     0xa0a728: movz            x2, #0x1e
    // 0xa0a72c: r0 = take()
    //     0xa0a72c: bl              #0xa0a080  ; [dart:async] Stream::take
    // 0xa0a730: ldur            x1, [fp, #-8]
    // 0xa0a734: StoreField: r1->field_1b = r0
    //     0xa0a734: stur            w0, [x1, #0x1b]
    //     0xa0a738: ldurb           w16, [x1, #-1]
    //     0xa0a73c: ldurb           w17, [x0, #-1]
    //     0xa0a740: and             x16, x17, x16, lsr #2
    //     0xa0a744: tst             x16, HEAP, lsr #32
    //     0xa0a748: b.eq            #0xa0a750
    //     0xa0a74c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xa0a750: r1 = Null
    //     0xa0a750: mov             x1, NULL
    // 0xa0a754: r0 = SmsAutoFill()
    //     0xa0a754: bl              #0x905c88  ; [package:sms_autofill/sms_autofill.dart] SmsAutoFill::SmsAutoFill
    // 0xa0a758: r0 = Null
    //     0xa0a758: mov             x0, NULL
    // 0xa0a75c: LeaveFrame
    //     0xa0a75c: mov             SP, fp
    //     0xa0a760: ldp             fp, lr, [SP], #0x10
    // 0xa0a764: ret
    //     0xa0a764: ret             
    // 0xa0a768: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0a768: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0a76c: b               #0xa0a700
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa0a9a8, size: 0x70
    // 0xa0a9a8: EnterFrame
    //     0xa0a9a8: stp             fp, lr, [SP, #-0x10]!
    //     0xa0a9ac: mov             fp, SP
    // 0xa0a9b0: AllocStack(0x8)
    //     0xa0a9b0: sub             SP, SP, #8
    // 0xa0a9b4: SetupParameters()
    //     0xa0a9b4: ldr             x0, [fp, #0x10]
    //     0xa0a9b8: ldur            w1, [x0, #0x17]
    //     0xa0a9bc: add             x1, x1, HEAP, lsl #32
    // 0xa0a9c0: CheckStackOverflow
    //     0xa0a9c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0a9c4: cmp             SP, x16
    //     0xa0a9c8: b.ls            #0xa0aa0c
    // 0xa0a9cc: LoadField: r0 = r1->field_f
    //     0xa0a9cc: ldur            w0, [x1, #0xf]
    // 0xa0a9d0: DecompressPointer r0
    //     0xa0a9d0: add             x0, x0, HEAP, lsl #32
    // 0xa0a9d4: LoadField: r1 = r0->field_b
    //     0xa0a9d4: ldur            w1, [x0, #0xb]
    // 0xa0a9d8: DecompressPointer r1
    //     0xa0a9d8: add             x1, x1, HEAP, lsl #32
    // 0xa0a9dc: cmp             w1, NULL
    // 0xa0a9e0: b.eq            #0xa0aa14
    // 0xa0a9e4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa0a9e4: ldur            w0, [x1, #0x17]
    // 0xa0a9e8: DecompressPointer r0
    //     0xa0a9e8: add             x0, x0, HEAP, lsl #32
    // 0xa0a9ec: str             x0, [SP]
    // 0xa0a9f0: ClosureCall
    //     0xa0a9f0: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xa0a9f4: ldur            x2, [x0, #0x1f]
    //     0xa0a9f8: blr             x2
    // 0xa0a9fc: r0 = Null
    //     0xa0a9fc: mov             x0, NULL
    // 0xa0aa00: LeaveFrame
    //     0xa0aa00: mov             SP, fp
    //     0xa0aa04: ldp             fp, lr, [SP], #0x10
    // 0xa0aa08: ret
    //     0xa0aa08: ret             
    // 0xa0aa0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0aa0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0aa10: b               #0xa0a9cc
    // 0xa0aa14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0aa14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbb5e4c, size: 0x840
    // 0xbb5e4c: EnterFrame
    //     0xbb5e4c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb5e50: mov             fp, SP
    // 0xbb5e54: AllocStack(0x88)
    //     0xbb5e54: sub             SP, SP, #0x88
    // 0xbb5e58: SetupParameters(_CheckoutOtpWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbb5e58: mov             x0, x1
    //     0xbb5e5c: stur            x1, [fp, #-8]
    //     0xbb5e60: mov             x1, x2
    //     0xbb5e64: stur            x2, [fp, #-0x10]
    // 0xbb5e68: CheckStackOverflow
    //     0xbb5e68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb5e6c: cmp             SP, x16
    //     0xbb5e70: b.ls            #0xbb6664
    // 0xbb5e74: r1 = 2
    //     0xbb5e74: movz            x1, #0x2
    // 0xbb5e78: r0 = AllocateContext()
    //     0xbb5e78: bl              #0x16f6108  ; AllocateContextStub
    // 0xbb5e7c: mov             x2, x0
    // 0xbb5e80: ldur            x0, [fp, #-8]
    // 0xbb5e84: stur            x2, [fp, #-0x18]
    // 0xbb5e88: StoreField: r2->field_f = r0
    //     0xbb5e88: stur            w0, [x2, #0xf]
    // 0xbb5e8c: ldur            x1, [fp, #-0x10]
    // 0xbb5e90: StoreField: r2->field_13 = r1
    //     0xbb5e90: stur            w1, [x2, #0x13]
    // 0xbb5e94: r0 = of()
    //     0xbb5e94: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb5e98: LoadField: r1 = r0->field_5b
    //     0xbb5e98: ldur            w1, [x0, #0x5b]
    // 0xbb5e9c: DecompressPointer r1
    //     0xbb5e9c: add             x1, x1, HEAP, lsl #32
    // 0xbb5ea0: r0 = LoadClassIdInstr(r1)
    //     0xbb5ea0: ldur            x0, [x1, #-1]
    //     0xbb5ea4: ubfx            x0, x0, #0xc, #0x14
    // 0xbb5ea8: d0 = 0.100000
    //     0xbb5ea8: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbb5eac: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbb5eac: sub             lr, x0, #0xffa
    //     0xbb5eb0: ldr             lr, [x21, lr, lsl #3]
    //     0xbb5eb4: blr             lr
    // 0xbb5eb8: stur            x0, [fp, #-0x10]
    // 0xbb5ebc: r0 = Divider()
    //     0xbb5ebc: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xbb5ec0: mov             x2, x0
    // 0xbb5ec4: ldur            x0, [fp, #-0x10]
    // 0xbb5ec8: stur            x2, [fp, #-0x20]
    // 0xbb5ecc: StoreField: r2->field_1f = r0
    //     0xbb5ecc: stur            w0, [x2, #0x1f]
    // 0xbb5ed0: ldur            x0, [fp, #-0x18]
    // 0xbb5ed4: LoadField: r1 = r0->field_13
    //     0xbb5ed4: ldur            w1, [x0, #0x13]
    // 0xbb5ed8: DecompressPointer r1
    //     0xbb5ed8: add             x1, x1, HEAP, lsl #32
    // 0xbb5edc: r0 = of()
    //     0xbb5edc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb5ee0: LoadField: r1 = r0->field_87
    //     0xbb5ee0: ldur            w1, [x0, #0x87]
    // 0xbb5ee4: DecompressPointer r1
    //     0xbb5ee4: add             x1, x1, HEAP, lsl #32
    // 0xbb5ee8: LoadField: r0 = r1->field_7
    //     0xbb5ee8: ldur            w0, [x1, #7]
    // 0xbb5eec: DecompressPointer r0
    //     0xbb5eec: add             x0, x0, HEAP, lsl #32
    // 0xbb5ef0: r16 = Instance_Color
    //     0xbb5ef0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb5ef4: r30 = 14.000000
    //     0xbb5ef4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb5ef8: ldr             lr, [lr, #0x1d8]
    // 0xbb5efc: stp             lr, x16, [SP]
    // 0xbb5f00: mov             x1, x0
    // 0xbb5f04: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb5f04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb5f08: ldr             x4, [x4, #0x9b8]
    // 0xbb5f0c: r0 = copyWith()
    //     0xbb5f0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb5f10: stur            x0, [fp, #-0x10]
    // 0xbb5f14: r0 = Text()
    //     0xbb5f14: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb5f18: mov             x1, x0
    // 0xbb5f1c: r0 = "Enter OTP to confirm"
    //     0xbb5f1c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53ee8] "Enter OTP to confirm"
    //     0xbb5f20: ldr             x0, [x0, #0xee8]
    // 0xbb5f24: stur            x1, [fp, #-0x28]
    // 0xbb5f28: StoreField: r1->field_b = r0
    //     0xbb5f28: stur            w0, [x1, #0xb]
    // 0xbb5f2c: ldur            x0, [fp, #-0x10]
    // 0xbb5f30: StoreField: r1->field_13 = r0
    //     0xbb5f30: stur            w0, [x1, #0x13]
    // 0xbb5f34: r0 = Padding()
    //     0xbb5f34: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb5f38: mov             x3, x0
    // 0xbb5f3c: r0 = Instance_EdgeInsets
    //     0xbb5f3c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbb5f40: ldr             x0, [x0, #0x668]
    // 0xbb5f44: stur            x3, [fp, #-0x10]
    // 0xbb5f48: StoreField: r3->field_f = r0
    //     0xbb5f48: stur            w0, [x3, #0xf]
    // 0xbb5f4c: ldur            x1, [fp, #-0x28]
    // 0xbb5f50: StoreField: r3->field_b = r1
    //     0xbb5f50: stur            w1, [x3, #0xb]
    // 0xbb5f54: r1 = Null
    //     0xbb5f54: mov             x1, NULL
    // 0xbb5f58: r2 = 4
    //     0xbb5f58: movz            x2, #0x4
    // 0xbb5f5c: r0 = AllocateArray()
    //     0xbb5f5c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb5f60: r16 = "Otp sent to "
    //     0xbb5f60: add             x16, PP, #0x53, lsl #12  ; [pp+0x53ef0] "Otp sent to "
    //     0xbb5f64: ldr             x16, [x16, #0xef0]
    // 0xbb5f68: StoreField: r0->field_f = r16
    //     0xbb5f68: stur            w16, [x0, #0xf]
    // 0xbb5f6c: ldur            x1, [fp, #-8]
    // 0xbb5f70: LoadField: r2 = r1->field_b
    //     0xbb5f70: ldur            w2, [x1, #0xb]
    // 0xbb5f74: DecompressPointer r2
    //     0xbb5f74: add             x2, x2, HEAP, lsl #32
    // 0xbb5f78: cmp             w2, NULL
    // 0xbb5f7c: b.eq            #0xbb666c
    // 0xbb5f80: LoadField: r3 = r2->field_1b
    //     0xbb5f80: ldur            w3, [x2, #0x1b]
    // 0xbb5f84: DecompressPointer r3
    //     0xbb5f84: add             x3, x3, HEAP, lsl #32
    // 0xbb5f88: StoreField: r0->field_13 = r3
    //     0xbb5f88: stur            w3, [x0, #0x13]
    // 0xbb5f8c: str             x0, [SP]
    // 0xbb5f90: r0 = _interpolate()
    //     0xbb5f90: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbb5f94: ldur            x2, [fp, #-0x18]
    // 0xbb5f98: stur            x0, [fp, #-0x28]
    // 0xbb5f9c: LoadField: r1 = r2->field_13
    //     0xbb5f9c: ldur            w1, [x2, #0x13]
    // 0xbb5fa0: DecompressPointer r1
    //     0xbb5fa0: add             x1, x1, HEAP, lsl #32
    // 0xbb5fa4: r0 = of()
    //     0xbb5fa4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb5fa8: LoadField: r1 = r0->field_87
    //     0xbb5fa8: ldur            w1, [x0, #0x87]
    // 0xbb5fac: DecompressPointer r1
    //     0xbb5fac: add             x1, x1, HEAP, lsl #32
    // 0xbb5fb0: LoadField: r0 = r1->field_7
    //     0xbb5fb0: ldur            w0, [x1, #7]
    // 0xbb5fb4: DecompressPointer r0
    //     0xbb5fb4: add             x0, x0, HEAP, lsl #32
    // 0xbb5fb8: stur            x0, [fp, #-0x30]
    // 0xbb5fbc: r1 = Instance_Color
    //     0xbb5fbc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb5fc0: r2 = 70
    //     0xbb5fc0: movz            x2, #0x46
    // 0xbb5fc4: r0 = withAlpha()
    //     0xbb5fc4: bl              #0x1685b4c  ; [dart:ui] Color::withAlpha
    // 0xbb5fc8: r16 = 12.000000
    //     0xbb5fc8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb5fcc: ldr             x16, [x16, #0x9e8]
    // 0xbb5fd0: stp             x16, x0, [SP]
    // 0xbb5fd4: ldur            x1, [fp, #-0x30]
    // 0xbb5fd8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb5fd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb5fdc: ldr             x4, [x4, #0x9b8]
    // 0xbb5fe0: r0 = copyWith()
    //     0xbb5fe0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb5fe4: stur            x0, [fp, #-0x30]
    // 0xbb5fe8: r0 = Text()
    //     0xbb5fe8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb5fec: mov             x2, x0
    // 0xbb5ff0: ldur            x0, [fp, #-0x28]
    // 0xbb5ff4: stur            x2, [fp, #-0x38]
    // 0xbb5ff8: StoreField: r2->field_b = r0
    //     0xbb5ff8: stur            w0, [x2, #0xb]
    // 0xbb5ffc: ldur            x0, [fp, #-0x30]
    // 0xbb6000: StoreField: r2->field_13 = r0
    //     0xbb6000: stur            w0, [x2, #0x13]
    // 0xbb6004: ldur            x0, [fp, #-0x18]
    // 0xbb6008: LoadField: r1 = r0->field_13
    //     0xbb6008: ldur            w1, [x0, #0x13]
    // 0xbb600c: DecompressPointer r1
    //     0xbb600c: add             x1, x1, HEAP, lsl #32
    // 0xbb6010: r0 = of()
    //     0xbb6010: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb6014: LoadField: r1 = r0->field_87
    //     0xbb6014: ldur            w1, [x0, #0x87]
    // 0xbb6018: DecompressPointer r1
    //     0xbb6018: add             x1, x1, HEAP, lsl #32
    // 0xbb601c: LoadField: r0 = r1->field_7
    //     0xbb601c: ldur            w0, [x1, #7]
    // 0xbb6020: DecompressPointer r0
    //     0xbb6020: add             x0, x0, HEAP, lsl #32
    // 0xbb6024: stur            x0, [fp, #-0x28]
    // 0xbb6028: r1 = Instance_Color
    //     0xbb6028: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb602c: r2 = 80
    //     0xbb602c: movz            x2, #0x50
    // 0xbb6030: r0 = withAlpha()
    //     0xbb6030: bl              #0x1685b4c  ; [dart:ui] Color::withAlpha
    // 0xbb6034: r16 = 14.000000
    //     0xbb6034: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb6038: ldr             x16, [x16, #0x1d8]
    // 0xbb603c: stp             x16, x0, [SP, #8]
    // 0xbb6040: r16 = Instance_TextDecoration
    //     0xbb6040: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xbb6044: ldr             x16, [x16, #0x10]
    // 0xbb6048: str             x16, [SP]
    // 0xbb604c: ldur            x1, [fp, #-0x28]
    // 0xbb6050: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, decoration, 0x3, fontSize, 0x2, null]
    //     0xbb6050: add             x4, PP, #0x40, lsl #12  ; [pp+0x407c8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "decoration", 0x3, "fontSize", 0x2, Null]
    //     0xbb6054: ldr             x4, [x4, #0x7c8]
    // 0xbb6058: r0 = copyWith()
    //     0xbb6058: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb605c: stur            x0, [fp, #-0x28]
    // 0xbb6060: r0 = Text()
    //     0xbb6060: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb6064: mov             x1, x0
    // 0xbb6068: r0 = "EDIT"
    //     0xbb6068: add             x0, PP, #0x53, lsl #12  ; [pp+0x53ef8] "EDIT"
    //     0xbb606c: ldr             x0, [x0, #0xef8]
    // 0xbb6070: stur            x1, [fp, #-0x30]
    // 0xbb6074: StoreField: r1->field_b = r0
    //     0xbb6074: stur            w0, [x1, #0xb]
    // 0xbb6078: ldur            x0, [fp, #-0x28]
    // 0xbb607c: StoreField: r1->field_13 = r0
    //     0xbb607c: stur            w0, [x1, #0x13]
    // 0xbb6080: r0 = Padding()
    //     0xbb6080: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb6084: mov             x1, x0
    // 0xbb6088: r0 = Instance_EdgeInsets
    //     0xbb6088: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xbb608c: ldr             x0, [x0, #0xe60]
    // 0xbb6090: stur            x1, [fp, #-0x28]
    // 0xbb6094: StoreField: r1->field_f = r0
    //     0xbb6094: stur            w0, [x1, #0xf]
    // 0xbb6098: ldur            x0, [fp, #-0x30]
    // 0xbb609c: StoreField: r1->field_b = r0
    //     0xbb609c: stur            w0, [x1, #0xb]
    // 0xbb60a0: r0 = InkWell()
    //     0xbb60a0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbb60a4: mov             x3, x0
    // 0xbb60a8: ldur            x0, [fp, #-0x28]
    // 0xbb60ac: stur            x3, [fp, #-0x30]
    // 0xbb60b0: StoreField: r3->field_b = r0
    //     0xbb60b0: stur            w0, [x3, #0xb]
    // 0xbb60b4: ldur            x2, [fp, #-0x18]
    // 0xbb60b8: r1 = Function '<anonymous closure>':.
    //     0xbb60b8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53f00] AnonymousClosure: (0xa0a9a8), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::build (0xbb5e4c)
    //     0xbb60bc: ldr             x1, [x1, #0xf00]
    // 0xbb60c0: r0 = AllocateClosure()
    //     0xbb60c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb60c4: mov             x1, x0
    // 0xbb60c8: ldur            x0, [fp, #-0x30]
    // 0xbb60cc: StoreField: r0->field_f = r1
    //     0xbb60cc: stur            w1, [x0, #0xf]
    // 0xbb60d0: r3 = true
    //     0xbb60d0: add             x3, NULL, #0x20  ; true
    // 0xbb60d4: StoreField: r0->field_43 = r3
    //     0xbb60d4: stur            w3, [x0, #0x43]
    // 0xbb60d8: r1 = Instance_BoxShape
    //     0xbb60d8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbb60dc: ldr             x1, [x1, #0x80]
    // 0xbb60e0: StoreField: r0->field_47 = r1
    //     0xbb60e0: stur            w1, [x0, #0x47]
    // 0xbb60e4: StoreField: r0->field_6f = r3
    //     0xbb60e4: stur            w3, [x0, #0x6f]
    // 0xbb60e8: r4 = false
    //     0xbb60e8: add             x4, NULL, #0x30  ; false
    // 0xbb60ec: StoreField: r0->field_73 = r4
    //     0xbb60ec: stur            w4, [x0, #0x73]
    // 0xbb60f0: StoreField: r0->field_83 = r3
    //     0xbb60f0: stur            w3, [x0, #0x83]
    // 0xbb60f4: StoreField: r0->field_7b = r4
    //     0xbb60f4: stur            w4, [x0, #0x7b]
    // 0xbb60f8: r1 = Null
    //     0xbb60f8: mov             x1, NULL
    // 0xbb60fc: r2 = 4
    //     0xbb60fc: movz            x2, #0x4
    // 0xbb6100: r0 = AllocateArray()
    //     0xbb6100: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb6104: mov             x2, x0
    // 0xbb6108: ldur            x0, [fp, #-0x38]
    // 0xbb610c: stur            x2, [fp, #-0x28]
    // 0xbb6110: StoreField: r2->field_f = r0
    //     0xbb6110: stur            w0, [x2, #0xf]
    // 0xbb6114: ldur            x0, [fp, #-0x30]
    // 0xbb6118: StoreField: r2->field_13 = r0
    //     0xbb6118: stur            w0, [x2, #0x13]
    // 0xbb611c: r1 = <Widget>
    //     0xbb611c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb6120: r0 = AllocateGrowableArray()
    //     0xbb6120: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb6124: mov             x1, x0
    // 0xbb6128: ldur            x0, [fp, #-0x28]
    // 0xbb612c: stur            x1, [fp, #-0x30]
    // 0xbb6130: StoreField: r1->field_f = r0
    //     0xbb6130: stur            w0, [x1, #0xf]
    // 0xbb6134: r2 = 4
    //     0xbb6134: movz            x2, #0x4
    // 0xbb6138: StoreField: r1->field_b = r2
    //     0xbb6138: stur            w2, [x1, #0xb]
    // 0xbb613c: r0 = Row()
    //     0xbb613c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbb6140: mov             x1, x0
    // 0xbb6144: r0 = Instance_Axis
    //     0xbb6144: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb6148: stur            x1, [fp, #-0x28]
    // 0xbb614c: StoreField: r1->field_f = r0
    //     0xbb614c: stur            w0, [x1, #0xf]
    // 0xbb6150: r0 = Instance_MainAxisAlignment
    //     0xbb6150: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb6154: ldr             x0, [x0, #0xa08]
    // 0xbb6158: StoreField: r1->field_13 = r0
    //     0xbb6158: stur            w0, [x1, #0x13]
    // 0xbb615c: r2 = Instance_MainAxisSize
    //     0xbb615c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb6160: ldr             x2, [x2, #0xa10]
    // 0xbb6164: ArrayStore: r1[0] = r2  ; List_4
    //     0xbb6164: stur            w2, [x1, #0x17]
    // 0xbb6168: r3 = Instance_CrossAxisAlignment
    //     0xbb6168: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb616c: ldr             x3, [x3, #0xa18]
    // 0xbb6170: StoreField: r1->field_1b = r3
    //     0xbb6170: stur            w3, [x1, #0x1b]
    // 0xbb6174: r3 = Instance_VerticalDirection
    //     0xbb6174: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb6178: ldr             x3, [x3, #0xa20]
    // 0xbb617c: StoreField: r1->field_23 = r3
    //     0xbb617c: stur            w3, [x1, #0x23]
    // 0xbb6180: r4 = Instance_Clip
    //     0xbb6180: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb6184: ldr             x4, [x4, #0x38]
    // 0xbb6188: StoreField: r1->field_2b = r4
    //     0xbb6188: stur            w4, [x1, #0x2b]
    // 0xbb618c: StoreField: r1->field_2f = rZR
    //     0xbb618c: stur            xzr, [x1, #0x2f]
    // 0xbb6190: ldur            x5, [fp, #-0x30]
    // 0xbb6194: StoreField: r1->field_b = r5
    //     0xbb6194: stur            w5, [x1, #0xb]
    // 0xbb6198: r0 = Padding()
    //     0xbb6198: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb619c: mov             x1, x0
    // 0xbb61a0: r0 = Instance_EdgeInsets
    //     0xbb61a0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe18] Obj!EdgeInsets@d57ef1
    //     0xbb61a4: ldr             x0, [x0, #0xe18]
    // 0xbb61a8: stur            x1, [fp, #-0x30]
    // 0xbb61ac: StoreField: r1->field_f = r0
    //     0xbb61ac: stur            w0, [x1, #0xf]
    // 0xbb61b0: ldur            x0, [fp, #-0x28]
    // 0xbb61b4: StoreField: r1->field_b = r0
    //     0xbb61b4: stur            w0, [x1, #0xb]
    // 0xbb61b8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbb61b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbb61bc: ldr             x0, [x0, #0x1c80]
    //     0xbb61c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbb61c4: cmp             w0, w16
    //     0xbb61c8: b.ne            #0xbb61d4
    //     0xbb61cc: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbb61d0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbb61d4: r0 = GetNavigation.width()
    //     0xbb61d4: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0xbb61d8: stur            d0, [fp, #-0x58]
    // 0xbb61dc: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xbb61dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbb61e0: ldr             x0, [x0, #0x1530]
    //     0xbb61e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbb61e8: cmp             w0, w16
    //     0xbb61ec: b.ne            #0xbb61fc
    //     0xbb61f0: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0xbb61f4: ldr             x2, [x2, #0x120]
    //     0xbb61f8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbb61fc: stur            x0, [fp, #-0x28]
    // 0xbb6200: r16 = "[0-9]"
    //     0xbb6200: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0xbb6204: ldr             x16, [x16, #0x128]
    // 0xbb6208: stp             x16, NULL, [SP, #0x20]
    // 0xbb620c: r16 = false
    //     0xbb620c: add             x16, NULL, #0x30  ; false
    // 0xbb6210: r30 = true
    //     0xbb6210: add             lr, NULL, #0x20  ; true
    // 0xbb6214: stp             lr, x16, [SP, #0x10]
    // 0xbb6218: r16 = false
    //     0xbb6218: add             x16, NULL, #0x30  ; false
    // 0xbb621c: r30 = false
    //     0xbb621c: add             lr, NULL, #0x30  ; false
    // 0xbb6220: stp             lr, x16, [SP]
    // 0xbb6224: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xbb6224: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xbb6228: r0 = _RegExp()
    //     0xbb6228: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xbb622c: stur            x0, [fp, #-0x38]
    // 0xbb6230: r0 = FilteringTextInputFormatter()
    //     0xbb6230: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xbb6234: mov             x3, x0
    // 0xbb6238: ldur            x0, [fp, #-0x38]
    // 0xbb623c: stur            x3, [fp, #-0x40]
    // 0xbb6240: StoreField: r3->field_b = r0
    //     0xbb6240: stur            w0, [x3, #0xb]
    // 0xbb6244: r0 = true
    //     0xbb6244: add             x0, NULL, #0x20  ; true
    // 0xbb6248: StoreField: r3->field_7 = r0
    //     0xbb6248: stur            w0, [x3, #7]
    // 0xbb624c: r1 = ""
    //     0xbb624c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb6250: StoreField: r3->field_f = r1
    //     0xbb6250: stur            w1, [x3, #0xf]
    // 0xbb6254: r1 = Null
    //     0xbb6254: mov             x1, NULL
    // 0xbb6258: r2 = 4
    //     0xbb6258: movz            x2, #0x4
    // 0xbb625c: r0 = AllocateArray()
    //     0xbb625c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb6260: mov             x2, x0
    // 0xbb6264: ldur            x0, [fp, #-0x28]
    // 0xbb6268: stur            x2, [fp, #-0x38]
    // 0xbb626c: StoreField: r2->field_f = r0
    //     0xbb626c: stur            w0, [x2, #0xf]
    // 0xbb6270: ldur            x0, [fp, #-0x40]
    // 0xbb6274: StoreField: r2->field_13 = r0
    //     0xbb6274: stur            w0, [x2, #0x13]
    // 0xbb6278: r1 = <TextInputFormatter>
    //     0xbb6278: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbb627c: ldr             x1, [x1, #0x7b0]
    // 0xbb6280: r0 = AllocateGrowableArray()
    //     0xbb6280: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb6284: mov             x2, x0
    // 0xbb6288: ldur            x0, [fp, #-0x38]
    // 0xbb628c: stur            x2, [fp, #-0x28]
    // 0xbb6290: StoreField: r2->field_f = r0
    //     0xbb6290: stur            w0, [x2, #0xf]
    // 0xbb6294: r0 = 4
    //     0xbb6294: movz            x0, #0x4
    // 0xbb6298: StoreField: r2->field_b = r0
    //     0xbb6298: stur            w0, [x2, #0xb]
    // 0xbb629c: ldur            x0, [fp, #-0x18]
    // 0xbb62a0: LoadField: r1 = r0->field_13
    //     0xbb62a0: ldur            w1, [x0, #0x13]
    // 0xbb62a4: DecompressPointer r1
    //     0xbb62a4: add             x1, x1, HEAP, lsl #32
    // 0xbb62a8: r0 = of()
    //     0xbb62a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb62ac: LoadField: r1 = r0->field_5b
    //     0xbb62ac: ldur            w1, [x0, #0x5b]
    // 0xbb62b0: DecompressPointer r1
    //     0xbb62b0: add             x1, x1, HEAP, lsl #32
    // 0xbb62b4: stur            x1, [fp, #-0x38]
    // 0xbb62b8: r0 = Cursor()
    //     0xbb62b8: bl              #0xa09868  ; AllocateCursorStub -> Cursor (size=0x3c)
    // 0xbb62bc: d0 = 1.000000
    //     0xbb62bc: fmov            d0, #1.00000000
    // 0xbb62c0: stur            x0, [fp, #-0x40]
    // 0xbb62c4: StoreField: r0->field_7 = d0
    //     0xbb62c4: stur            d0, [x0, #7]
    // 0xbb62c8: d1 = 27.000000
    //     0xbb62c8: fmov            d1, #27.00000000
    // 0xbb62cc: StoreField: r0->field_f = d1
    //     0xbb62cc: stur            d1, [x0, #0xf]
    // 0xbb62d0: r1 = Instance_Radius
    //     0xbb62d0: add             x1, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0xbb62d4: ldr             x1, [x1, #0xb48]
    // 0xbb62d8: ArrayStore: r0[0] = r1  ; List_4
    //     0xbb62d8: stur            w1, [x0, #0x17]
    // 0xbb62dc: ldur            x1, [fp, #-0x38]
    // 0xbb62e0: StoreField: r0->field_1b = r1
    //     0xbb62e0: stur            w1, [x0, #0x1b]
    // 0xbb62e4: r1 = Instance_Duration
    //     0xbb62e4: add             x1, PP, #0xa, lsl #12  ; [pp+0xa058] Obj!Duration@d777a1
    //     0xbb62e8: ldr             x1, [x1, #0x58]
    // 0xbb62ec: StoreField: r0->field_1f = r1
    //     0xbb62ec: stur            w1, [x0, #0x1f]
    // 0xbb62f0: r1 = Instance_Duration
    //     0xbb62f0: ldr             x1, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xbb62f4: StoreField: r0->field_23 = r1
    //     0xbb62f4: stur            w1, [x0, #0x23]
    // 0xbb62f8: r1 = Instance_Duration
    //     0xbb62f8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e4b8] Obj!Duration@d77851
    //     0xbb62fc: ldr             x1, [x1, #0x4b8]
    // 0xbb6300: StoreField: r0->field_27 = r1
    //     0xbb6300: stur            w1, [x0, #0x27]
    // 0xbb6304: r1 = Instance_Orientation
    //     0xbb6304: add             x1, PP, #0x37, lsl #12  ; [pp+0x37198] Obj!Orientation@d70241
    //     0xbb6308: ldr             x1, [x1, #0x198]
    // 0xbb630c: StoreField: r0->field_2f = r1
    //     0xbb630c: stur            w1, [x0, #0x2f]
    // 0xbb6310: StoreField: r0->field_33 = rZR
    //     0xbb6310: stur            xzr, [x0, #0x33]
    // 0xbb6314: r2 = true
    //     0xbb6314: add             x2, NULL, #0x20  ; true
    // 0xbb6318: StoreField: r0->field_2b = r2
    //     0xbb6318: stur            w2, [x0, #0x2b]
    // 0xbb631c: ldur            x3, [fp, #-0x18]
    // 0xbb6320: LoadField: r1 = r3->field_13
    //     0xbb6320: ldur            w1, [x3, #0x13]
    // 0xbb6324: DecompressPointer r1
    //     0xbb6324: add             x1, x1, HEAP, lsl #32
    // 0xbb6328: r0 = of()
    //     0xbb6328: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb632c: LoadField: r1 = r0->field_87
    //     0xbb632c: ldur            w1, [x0, #0x87]
    // 0xbb6330: DecompressPointer r1
    //     0xbb6330: add             x1, x1, HEAP, lsl #32
    // 0xbb6334: LoadField: r0 = r1->field_2b
    //     0xbb6334: ldur            w0, [x1, #0x2b]
    // 0xbb6338: DecompressPointer r0
    //     0xbb6338: add             x0, x0, HEAP, lsl #32
    // 0xbb633c: ldur            x2, [fp, #-0x18]
    // 0xbb6340: stur            x0, [fp, #-0x38]
    // 0xbb6344: LoadField: r1 = r2->field_13
    //     0xbb6344: ldur            w1, [x2, #0x13]
    // 0xbb6348: DecompressPointer r1
    //     0xbb6348: add             x1, x1, HEAP, lsl #32
    // 0xbb634c: r0 = of()
    //     0xbb634c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb6350: LoadField: r1 = r0->field_5b
    //     0xbb6350: ldur            w1, [x0, #0x5b]
    // 0xbb6354: DecompressPointer r1
    //     0xbb6354: add             x1, x1, HEAP, lsl #32
    // 0xbb6358: r16 = 16.000000
    //     0xbb6358: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbb635c: ldr             x16, [x16, #0x188]
    // 0xbb6360: stp             x1, x16, [SP]
    // 0xbb6364: ldur            x1, [fp, #-0x38]
    // 0xbb6368: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb6368: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb636c: ldr             x4, [x4, #0xaa0]
    // 0xbb6370: r0 = copyWith()
    //     0xbb6370: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb6374: ldur            x2, [fp, #-0x18]
    // 0xbb6378: stur            x0, [fp, #-0x38]
    // 0xbb637c: LoadField: r1 = r2->field_13
    //     0xbb637c: ldur            w1, [x2, #0x13]
    // 0xbb6380: DecompressPointer r1
    //     0xbb6380: add             x1, x1, HEAP, lsl #32
    // 0xbb6384: r0 = of()
    //     0xbb6384: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb6388: LoadField: r1 = r0->field_5b
    //     0xbb6388: ldur            w1, [x0, #0x5b]
    // 0xbb638c: DecompressPointer r1
    //     0xbb638c: add             x1, x1, HEAP, lsl #32
    // 0xbb6390: r0 = LoadClassIdInstr(r1)
    //     0xbb6390: ldur            x0, [x1, #-1]
    //     0xbb6394: ubfx            x0, x0, #0xc, #0x14
    // 0xbb6398: d0 = 0.300000
    //     0xbb6398: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xbb639c: ldr             d0, [x17, #0x658]
    // 0xbb63a0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbb63a0: sub             lr, x0, #0xffa
    //     0xbb63a4: ldr             lr, [x21, lr, lsl #3]
    //     0xbb63a8: blr             lr
    // 0xbb63ac: r1 = <Color>
    //     0xbb63ac: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbb63b0: ldr             x1, [x1, #0xf80]
    // 0xbb63b4: stur            x0, [fp, #-0x48]
    // 0xbb63b8: r0 = FixedColorBuilder()
    //     0xbb63b8: bl              #0xa0985c  ; AllocateFixedColorBuilderStub -> FixedColorBuilder (size=0x10)
    // 0xbb63bc: mov             x1, x0
    // 0xbb63c0: ldur            x0, [fp, #-0x48]
    // 0xbb63c4: stur            x1, [fp, #-0x50]
    // 0xbb63c8: StoreField: r1->field_b = r0
    //     0xbb63c8: stur            w0, [x1, #0xb]
    // 0xbb63cc: r0 = UnderlineDecoration()
    //     0xbb63cc: bl              #0xa09850  ; AllocateUnderlineDecorationStub -> UnderlineDecoration (size=0x48)
    // 0xbb63d0: d0 = 12.000000
    //     0xbb63d0: fmov            d0, #12.00000000
    // 0xbb63d4: stur            x0, [fp, #-0x48]
    // 0xbb63d8: StoreField: r0->field_27 = d0
    //     0xbb63d8: stur            d0, [x0, #0x27]
    // 0xbb63dc: ldur            x1, [fp, #-0x50]
    // 0xbb63e0: StoreField: r0->field_33 = r1
    //     0xbb63e0: stur            w1, [x0, #0x33]
    // 0xbb63e4: d0 = 1.000000
    //     0xbb63e4: fmov            d0, #1.00000000
    // 0xbb63e8: StoreField: r0->field_37 = d0
    //     0xbb63e8: stur            d0, [x0, #0x37]
    // 0xbb63ec: r1 = Sentinel
    //     0xbb63ec: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb63f0: StoreField: r0->field_23 = r1
    //     0xbb63f0: stur            w1, [x0, #0x23]
    // 0xbb63f4: ldur            x1, [fp, #-0x38]
    // 0xbb63f8: StoreField: r0->field_7 = r1
    //     0xbb63f8: stur            w1, [x0, #7]
    // 0xbb63fc: ldur            x1, [fp, #-8]
    // 0xbb6400: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbb6400: ldur            w2, [x1, #0x17]
    // 0xbb6404: DecompressPointer r2
    //     0xbb6404: add             x2, x2, HEAP, lsl #32
    // 0xbb6408: stur            x2, [fp, #-0x38]
    // 0xbb640c: r0 = PinFieldAutoFill()
    //     0xbb640c: bl              #0xa09844  ; AllocatePinFieldAutoFillStub -> PinFieldAutoFill (size=0x4c)
    // 0xbb6410: mov             x3, x0
    // 0xbb6414: r0 = Instance_TextInputType
    //     0xbb6414: add             x0, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xbb6418: ldr             x0, [x0, #0x1a0]
    // 0xbb641c: stur            x3, [fp, #-0x50]
    // 0xbb6420: StoreField: r3->field_33 = r0
    //     0xbb6420: stur            w0, [x3, #0x33]
    // 0xbb6424: r0 = Instance_TextInputAction
    //     0xbb6424: ldr             x0, [PP, #0x70c0]  ; [pp+0x70c0] Obj!TextInputAction@d728e1
    // 0xbb6428: StoreField: r3->field_37 = r0
    //     0xbb6428: stur            w0, [x3, #0x37]
    // 0xbb642c: ldur            x0, [fp, #-0x40]
    // 0xbb6430: StoreField: r3->field_2f = r0
    //     0xbb6430: stur            w0, [x3, #0x2f]
    // 0xbb6434: ldur            x0, [fp, #-0x28]
    // 0xbb6438: StoreField: r3->field_47 = r0
    //     0xbb6438: stur            w0, [x3, #0x47]
    // 0xbb643c: r0 = true
    //     0xbb643c: add             x0, NULL, #0x20  ; true
    // 0xbb6440: StoreField: r3->field_3b = r0
    //     0xbb6440: stur            w0, [x3, #0x3b]
    // 0xbb6444: StoreField: r3->field_3f = r0
    //     0xbb6444: stur            w0, [x3, #0x3f]
    // 0xbb6448: ldur            x0, [fp, #-0x48]
    // 0xbb644c: StoreField: r3->field_27 = r0
    //     0xbb644c: stur            w0, [x3, #0x27]
    // 0xbb6450: ldur            x2, [fp, #-0x18]
    // 0xbb6454: r1 = Function '<anonymous closure>':.
    //     0xbb6454: add             x1, PP, #0x53, lsl #12  ; [pp+0x53f08] AnonymousClosure: (0xbb6f74), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::build (0xbb5e4c)
    //     0xbb6458: ldr             x1, [x1, #0xf08]
    // 0xbb645c: r0 = AllocateClosure()
    //     0xbb645c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb6460: mov             x1, x0
    // 0xbb6464: ldur            x0, [fp, #-0x50]
    // 0xbb6468: StoreField: r0->field_1f = r1
    //     0xbb6468: stur            w1, [x0, #0x1f]
    // 0xbb646c: ldur            x2, [fp, #-0x18]
    // 0xbb6470: r1 = Function '<anonymous closure>':.
    //     0xbb6470: add             x1, PP, #0x53, lsl #12  ; [pp+0x53f10] AnonymousClosure: (0xbb6e68), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::build (0xbb5e4c)
    //     0xbb6474: ldr             x1, [x1, #0xf10]
    // 0xbb6478: r0 = AllocateClosure()
    //     0xbb6478: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb647c: mov             x1, x0
    // 0xbb6480: ldur            x0, [fp, #-0x50]
    // 0xbb6484: StoreField: r0->field_23 = r1
    //     0xbb6484: stur            w1, [x0, #0x23]
    // 0xbb6488: ldur            x1, [fp, #-0x38]
    // 0xbb648c: StoreField: r0->field_1b = r1
    //     0xbb648c: stur            w1, [x0, #0x1b]
    // 0xbb6490: r1 = false
    //     0xbb6490: add             x1, NULL, #0x30  ; false
    // 0xbb6494: StoreField: r0->field_13 = r1
    //     0xbb6494: stur            w1, [x0, #0x13]
    // 0xbb6498: r1 = 4
    //     0xbb6498: movz            x1, #0x4
    // 0xbb649c: StoreField: r0->field_b = r1
    //     0xbb649c: stur            x1, [x0, #0xb]
    // 0xbb64a0: ldur            d0, [fp, #-0x58]
    // 0xbb64a4: r1 = inline_Allocate_Double()
    //     0xbb64a4: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xbb64a8: add             x1, x1, #0x10
    //     0xbb64ac: cmp             x2, x1
    //     0xbb64b0: b.ls            #0xbb6670
    //     0xbb64b4: str             x1, [THR, #0x50]  ; THR::top
    //     0xbb64b8: sub             x1, x1, #0xf
    //     0xbb64bc: movz            x2, #0xe15c
    //     0xbb64c0: movk            x2, #0x3, lsl #16
    //     0xbb64c4: stur            x2, [x1, #-1]
    // 0xbb64c8: StoreField: r1->field_7 = d0
    //     0xbb64c8: stur            d0, [x1, #7]
    // 0xbb64cc: stur            x1, [fp, #-0x28]
    // 0xbb64d0: r0 = Container()
    //     0xbb64d0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbb64d4: stur            x0, [fp, #-0x38]
    // 0xbb64d8: r16 = Instance_EdgeInsets
    //     0xbb64d8: add             x16, PP, #0x53, lsl #12  ; [pp+0x53f18] Obj!EdgeInsets@d57ec1
    //     0xbb64dc: ldr             x16, [x16, #0xf18]
    // 0xbb64e0: ldur            lr, [fp, #-0x28]
    // 0xbb64e4: stp             lr, x16, [SP, #8]
    // 0xbb64e8: ldur            x16, [fp, #-0x50]
    // 0xbb64ec: str             x16, [SP]
    // 0xbb64f0: mov             x1, x0
    // 0xbb64f4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x1, width, 0x2, null]
    //     0xbb64f4: add             x4, PP, #0x37, lsl #12  ; [pp+0x371b8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xbb64f8: ldr             x4, [x4, #0x1b8]
    // 0xbb64fc: r0 = Container()
    //     0xbb64fc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbb6500: r0 = Padding()
    //     0xbb6500: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb6504: mov             x3, x0
    // 0xbb6508: r0 = Instance_EdgeInsets
    //     0xbb6508: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f20] Obj!EdgeInsets@d57e91
    //     0xbb650c: ldr             x0, [x0, #0xf20]
    // 0xbb6510: stur            x3, [fp, #-0x40]
    // 0xbb6514: StoreField: r3->field_f = r0
    //     0xbb6514: stur            w0, [x3, #0xf]
    // 0xbb6518: ldur            x0, [fp, #-0x38]
    // 0xbb651c: StoreField: r3->field_b = r0
    //     0xbb651c: stur            w0, [x3, #0xb]
    // 0xbb6520: ldur            x0, [fp, #-8]
    // 0xbb6524: LoadField: r4 = r0->field_1b
    //     0xbb6524: ldur            w4, [x0, #0x1b]
    // 0xbb6528: DecompressPointer r4
    //     0xbb6528: add             x4, x4, HEAP, lsl #32
    // 0xbb652c: ldur            x2, [fp, #-0x18]
    // 0xbb6530: stur            x4, [fp, #-0x28]
    // 0xbb6534: r1 = Function '<anonymous closure>':.
    //     0xbb6534: add             x1, PP, #0x53, lsl #12  ; [pp+0x53f28] AnonymousClosure: (0xbb668c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::build (0xbb5e4c)
    //     0xbb6538: ldr             x1, [x1, #0xf28]
    // 0xbb653c: r0 = AllocateClosure()
    //     0xbb653c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb6540: r1 = <int, AsyncSnapshot<int>, int>
    //     0xbb6540: add             x1, PP, #0x53, lsl #12  ; [pp+0x53f30] TypeArguments: <int, AsyncSnapshot<int>, int>
    //     0xbb6544: ldr             x1, [x1, #0xf30]
    // 0xbb6548: stur            x0, [fp, #-8]
    // 0xbb654c: r0 = StreamBuilder()
    //     0xbb654c: bl              #0xa09838  ; AllocateStreamBuilderStub -> StreamBuilder<C2X0> (size=0x1c)
    // 0xbb6550: mov             x1, x0
    // 0xbb6554: ldur            x0, [fp, #-8]
    // 0xbb6558: stur            x1, [fp, #-0x18]
    // 0xbb655c: StoreField: r1->field_13 = r0
    //     0xbb655c: stur            w0, [x1, #0x13]
    // 0xbb6560: ldur            x0, [fp, #-0x28]
    // 0xbb6564: StoreField: r1->field_f = r0
    //     0xbb6564: stur            w0, [x1, #0xf]
    // 0xbb6568: r0 = Padding()
    //     0xbb6568: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb656c: mov             x3, x0
    // 0xbb6570: r0 = Instance_EdgeInsets
    //     0xbb6570: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbb6574: ldr             x0, [x0, #0x668]
    // 0xbb6578: stur            x3, [fp, #-8]
    // 0xbb657c: StoreField: r3->field_f = r0
    //     0xbb657c: stur            w0, [x3, #0xf]
    // 0xbb6580: ldur            x0, [fp, #-0x18]
    // 0xbb6584: StoreField: r3->field_b = r0
    //     0xbb6584: stur            w0, [x3, #0xb]
    // 0xbb6588: r1 = Null
    //     0xbb6588: mov             x1, NULL
    // 0xbb658c: r2 = 10
    //     0xbb658c: movz            x2, #0xa
    // 0xbb6590: r0 = AllocateArray()
    //     0xbb6590: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb6594: mov             x2, x0
    // 0xbb6598: ldur            x0, [fp, #-0x20]
    // 0xbb659c: stur            x2, [fp, #-0x18]
    // 0xbb65a0: StoreField: r2->field_f = r0
    //     0xbb65a0: stur            w0, [x2, #0xf]
    // 0xbb65a4: ldur            x0, [fp, #-0x10]
    // 0xbb65a8: StoreField: r2->field_13 = r0
    //     0xbb65a8: stur            w0, [x2, #0x13]
    // 0xbb65ac: ldur            x0, [fp, #-0x30]
    // 0xbb65b0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb65b0: stur            w0, [x2, #0x17]
    // 0xbb65b4: ldur            x0, [fp, #-0x40]
    // 0xbb65b8: StoreField: r2->field_1b = r0
    //     0xbb65b8: stur            w0, [x2, #0x1b]
    // 0xbb65bc: ldur            x0, [fp, #-8]
    // 0xbb65c0: StoreField: r2->field_1f = r0
    //     0xbb65c0: stur            w0, [x2, #0x1f]
    // 0xbb65c4: r1 = <Widget>
    //     0xbb65c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb65c8: r0 = AllocateGrowableArray()
    //     0xbb65c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb65cc: mov             x1, x0
    // 0xbb65d0: ldur            x0, [fp, #-0x18]
    // 0xbb65d4: stur            x1, [fp, #-8]
    // 0xbb65d8: StoreField: r1->field_f = r0
    //     0xbb65d8: stur            w0, [x1, #0xf]
    // 0xbb65dc: r0 = 10
    //     0xbb65dc: movz            x0, #0xa
    // 0xbb65e0: StoreField: r1->field_b = r0
    //     0xbb65e0: stur            w0, [x1, #0xb]
    // 0xbb65e4: r0 = Column()
    //     0xbb65e4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb65e8: mov             x1, x0
    // 0xbb65ec: r0 = Instance_Axis
    //     0xbb65ec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbb65f0: stur            x1, [fp, #-0x10]
    // 0xbb65f4: StoreField: r1->field_f = r0
    //     0xbb65f4: stur            w0, [x1, #0xf]
    // 0xbb65f8: r0 = Instance_MainAxisAlignment
    //     0xbb65f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb65fc: ldr             x0, [x0, #0xa08]
    // 0xbb6600: StoreField: r1->field_13 = r0
    //     0xbb6600: stur            w0, [x1, #0x13]
    // 0xbb6604: r0 = Instance_MainAxisSize
    //     0xbb6604: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb6608: ldr             x0, [x0, #0xa10]
    // 0xbb660c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbb660c: stur            w0, [x1, #0x17]
    // 0xbb6610: r0 = Instance_CrossAxisAlignment
    //     0xbb6610: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbb6614: ldr             x0, [x0, #0x890]
    // 0xbb6618: StoreField: r1->field_1b = r0
    //     0xbb6618: stur            w0, [x1, #0x1b]
    // 0xbb661c: r0 = Instance_VerticalDirection
    //     0xbb661c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb6620: ldr             x0, [x0, #0xa20]
    // 0xbb6624: StoreField: r1->field_23 = r0
    //     0xbb6624: stur            w0, [x1, #0x23]
    // 0xbb6628: r0 = Instance_Clip
    //     0xbb6628: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb662c: ldr             x0, [x0, #0x38]
    // 0xbb6630: StoreField: r1->field_2b = r0
    //     0xbb6630: stur            w0, [x1, #0x2b]
    // 0xbb6634: StoreField: r1->field_2f = rZR
    //     0xbb6634: stur            xzr, [x1, #0x2f]
    // 0xbb6638: ldur            x0, [fp, #-8]
    // 0xbb663c: StoreField: r1->field_b = r0
    //     0xbb663c: stur            w0, [x1, #0xb]
    // 0xbb6640: r0 = Padding()
    //     0xbb6640: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb6644: r1 = Instance_EdgeInsets
    //     0xbb6644: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0xbb6648: ldr             x1, [x1, #0xb0]
    // 0xbb664c: StoreField: r0->field_f = r1
    //     0xbb664c: stur            w1, [x0, #0xf]
    // 0xbb6650: ldur            x1, [fp, #-0x10]
    // 0xbb6654: StoreField: r0->field_b = r1
    //     0xbb6654: stur            w1, [x0, #0xb]
    // 0xbb6658: LeaveFrame
    //     0xbb6658: mov             SP, fp
    //     0xbb665c: ldp             fp, lr, [SP], #0x10
    // 0xbb6660: ret
    //     0xbb6660: ret             
    // 0xbb6664: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb6664: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb6668: b               #0xbb5e74
    // 0xbb666c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb666c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb6670: SaveReg d0
    //     0xbb6670: str             q0, [SP, #-0x10]!
    // 0xbb6674: SaveReg r0
    //     0xbb6674: str             x0, [SP, #-8]!
    // 0xbb6678: r0 = AllocateDouble()
    //     0xbb6678: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbb667c: mov             x1, x0
    // 0xbb6680: RestoreReg r0
    //     0xbb6680: ldr             x0, [SP], #8
    // 0xbb6684: RestoreReg d0
    //     0xbb6684: ldr             q0, [SP], #0x10
    // 0xbb6688: b               #0xbb64c8
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, AsyncSnapshot<int>) {
    // ** addr: 0xbb668c, size: 0x650
    // 0xbb668c: EnterFrame
    //     0xbb668c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb6690: mov             fp, SP
    // 0xbb6694: AllocStack(0x48)
    //     0xbb6694: sub             SP, SP, #0x48
    // 0xbb6698: SetupParameters()
    //     0xbb6698: ldr             x0, [fp, #0x20]
    //     0xbb669c: ldur            w2, [x0, #0x17]
    //     0xbb66a0: add             x2, x2, HEAP, lsl #32
    //     0xbb66a4: stur            x2, [fp, #-0x38]
    // 0xbb66a8: CheckStackOverflow
    //     0xbb66a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb66ac: cmp             SP, x16
    //     0xbb66b0: b.ls            #0xbb6cd4
    // 0xbb66b4: ldr             x0, [fp, #0x10]
    // 0xbb66b8: LoadField: r1 = r0->field_f
    //     0xbb66b8: ldur            w1, [x0, #0xf]
    // 0xbb66bc: DecompressPointer r1
    //     0xbb66bc: add             x1, x1, HEAP, lsl #32
    // 0xbb66c0: stur            x1, [fp, #-0x30]
    // 0xbb66c4: cmp             w1, NULL
    // 0xbb66c8: b.ne            #0xbb6910
    // 0xbb66cc: ldr             x1, [fp, #0x18]
    // 0xbb66d0: r0 = of()
    //     0xbb66d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb66d4: LoadField: r1 = r0->field_87
    //     0xbb66d4: ldur            w1, [x0, #0x87]
    // 0xbb66d8: DecompressPointer r1
    //     0xbb66d8: add             x1, x1, HEAP, lsl #32
    // 0xbb66dc: LoadField: r0 = r1->field_2b
    //     0xbb66dc: ldur            w0, [x1, #0x2b]
    // 0xbb66e0: DecompressPointer r0
    //     0xbb66e0: add             x0, x0, HEAP, lsl #32
    // 0xbb66e4: stur            x0, [fp, #-8]
    // 0xbb66e8: r1 = Instance_Color
    //     0xbb66e8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb66ec: d0 = 0.700000
    //     0xbb66ec: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbb66f0: ldr             d0, [x17, #0xf48]
    // 0xbb66f4: r0 = withOpacity()
    //     0xbb66f4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb66f8: r16 = 12.000000
    //     0xbb66f8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb66fc: ldr             x16, [x16, #0x9e8]
    // 0xbb6700: stp             x16, x0, [SP]
    // 0xbb6704: ldur            x1, [fp, #-8]
    // 0xbb6708: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb6708: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb670c: ldr             x4, [x4, #0x9b8]
    // 0xbb6710: r0 = copyWith()
    //     0xbb6710: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb6714: stur            x0, [fp, #-8]
    // 0xbb6718: r0 = Text()
    //     0xbb6718: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb671c: r3 = "Didn\'t receive OTP\? "
    //     0xbb671c: add             x3, PP, #0x53, lsl #12  ; [pp+0x53f38] "Didn\'t receive OTP\? "
    //     0xbb6720: ldr             x3, [x3, #0xf38]
    // 0xbb6724: stur            x0, [fp, #-0x10]
    // 0xbb6728: StoreField: r0->field_b = r3
    //     0xbb6728: stur            w3, [x0, #0xb]
    // 0xbb672c: ldur            x1, [fp, #-8]
    // 0xbb6730: StoreField: r0->field_13 = r1
    //     0xbb6730: stur            w1, [x0, #0x13]
    // 0xbb6734: ldr             x1, [fp, #0x18]
    // 0xbb6738: r0 = of()
    //     0xbb6738: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb673c: LoadField: r1 = r0->field_87
    //     0xbb673c: ldur            w1, [x0, #0x87]
    // 0xbb6740: DecompressPointer r1
    //     0xbb6740: add             x1, x1, HEAP, lsl #32
    // 0xbb6744: LoadField: r0 = r1->field_2b
    //     0xbb6744: ldur            w0, [x1, #0x2b]
    // 0xbb6748: DecompressPointer r0
    //     0xbb6748: add             x0, x0, HEAP, lsl #32
    // 0xbb674c: r16 = Instance_Color
    //     0xbb674c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb6750: ldr             x16, [x16, #0x858]
    // 0xbb6754: r30 = 12.000000
    //     0xbb6754: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb6758: ldr             lr, [lr, #0x9e8]
    // 0xbb675c: stp             lr, x16, [SP]
    // 0xbb6760: mov             x1, x0
    // 0xbb6764: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb6764: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb6768: ldr             x4, [x4, #0x9b8]
    // 0xbb676c: r0 = copyWith()
    //     0xbb676c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb6770: stur            x0, [fp, #-8]
    // 0xbb6774: r0 = Text()
    //     0xbb6774: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb6778: r4 = "Resend OTP "
    //     0xbb6778: add             x4, PP, #0x53, lsl #12  ; [pp+0x53f40] "Resend OTP "
    //     0xbb677c: ldr             x4, [x4, #0xf40]
    // 0xbb6780: stur            x0, [fp, #-0x18]
    // 0xbb6784: StoreField: r0->field_b = r4
    //     0xbb6784: stur            w4, [x0, #0xb]
    // 0xbb6788: ldur            x1, [fp, #-8]
    // 0xbb678c: StoreField: r0->field_13 = r1
    //     0xbb678c: stur            w1, [x0, #0x13]
    // 0xbb6790: ldr             x1, [fp, #0x18]
    // 0xbb6794: r0 = of()
    //     0xbb6794: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb6798: LoadField: r1 = r0->field_87
    //     0xbb6798: ldur            w1, [x0, #0x87]
    // 0xbb679c: DecompressPointer r1
    //     0xbb679c: add             x1, x1, HEAP, lsl #32
    // 0xbb67a0: LoadField: r0 = r1->field_2b
    //     0xbb67a0: ldur            w0, [x1, #0x2b]
    // 0xbb67a4: DecompressPointer r0
    //     0xbb67a4: add             x0, x0, HEAP, lsl #32
    // 0xbb67a8: stur            x0, [fp, #-8]
    // 0xbb67ac: r1 = Instance_Color
    //     0xbb67ac: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb67b0: d0 = 0.700000
    //     0xbb67b0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbb67b4: ldr             d0, [x17, #0xf48]
    // 0xbb67b8: r0 = withOpacity()
    //     0xbb67b8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb67bc: r16 = 12.000000
    //     0xbb67bc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb67c0: ldr             x16, [x16, #0x9e8]
    // 0xbb67c4: stp             x16, x0, [SP]
    // 0xbb67c8: ldur            x1, [fp, #-8]
    // 0xbb67cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb67cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb67d0: ldr             x4, [x4, #0x9b8]
    // 0xbb67d4: r0 = copyWith()
    //     0xbb67d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb67d8: stur            x0, [fp, #-8]
    // 0xbb67dc: r0 = Text()
    //     0xbb67dc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb67e0: mov             x2, x0
    // 0xbb67e4: r0 = "in "
    //     0xbb67e4: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f48] "in "
    //     0xbb67e8: ldr             x0, [x0, #0xf48]
    // 0xbb67ec: stur            x2, [fp, #-0x20]
    // 0xbb67f0: StoreField: r2->field_b = r0
    //     0xbb67f0: stur            w0, [x2, #0xb]
    // 0xbb67f4: ldur            x0, [fp, #-8]
    // 0xbb67f8: StoreField: r2->field_13 = r0
    //     0xbb67f8: stur            w0, [x2, #0x13]
    // 0xbb67fc: ldr             x1, [fp, #0x18]
    // 0xbb6800: r0 = of()
    //     0xbb6800: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb6804: LoadField: r1 = r0->field_87
    //     0xbb6804: ldur            w1, [x0, #0x87]
    // 0xbb6808: DecompressPointer r1
    //     0xbb6808: add             x1, x1, HEAP, lsl #32
    // 0xbb680c: LoadField: r0 = r1->field_2b
    //     0xbb680c: ldur            w0, [x1, #0x2b]
    // 0xbb6810: DecompressPointer r0
    //     0xbb6810: add             x0, x0, HEAP, lsl #32
    // 0xbb6814: r16 = Instance_Color
    //     0xbb6814: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbb6818: ldr             x16, [x16, #0x50]
    // 0xbb681c: r30 = 12.000000
    //     0xbb681c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb6820: ldr             lr, [lr, #0x9e8]
    // 0xbb6824: stp             lr, x16, [SP]
    // 0xbb6828: mov             x1, x0
    // 0xbb682c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb682c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb6830: ldr             x4, [x4, #0x9b8]
    // 0xbb6834: r0 = copyWith()
    //     0xbb6834: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb6838: stur            x0, [fp, #-8]
    // 0xbb683c: r0 = Text()
    //     0xbb683c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb6840: mov             x3, x0
    // 0xbb6844: r0 = "30s"
    //     0xbb6844: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f50] "30s"
    //     0xbb6848: ldr             x0, [x0, #0xf50]
    // 0xbb684c: stur            x3, [fp, #-0x28]
    // 0xbb6850: StoreField: r3->field_b = r0
    //     0xbb6850: stur            w0, [x3, #0xb]
    // 0xbb6854: ldur            x0, [fp, #-8]
    // 0xbb6858: StoreField: r3->field_13 = r0
    //     0xbb6858: stur            w0, [x3, #0x13]
    // 0xbb685c: r1 = Null
    //     0xbb685c: mov             x1, NULL
    // 0xbb6860: r2 = 8
    //     0xbb6860: movz            x2, #0x8
    // 0xbb6864: r0 = AllocateArray()
    //     0xbb6864: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb6868: mov             x2, x0
    // 0xbb686c: ldur            x0, [fp, #-0x10]
    // 0xbb6870: stur            x2, [fp, #-8]
    // 0xbb6874: StoreField: r2->field_f = r0
    //     0xbb6874: stur            w0, [x2, #0xf]
    // 0xbb6878: ldur            x0, [fp, #-0x18]
    // 0xbb687c: StoreField: r2->field_13 = r0
    //     0xbb687c: stur            w0, [x2, #0x13]
    // 0xbb6880: ldur            x0, [fp, #-0x20]
    // 0xbb6884: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb6884: stur            w0, [x2, #0x17]
    // 0xbb6888: ldur            x0, [fp, #-0x28]
    // 0xbb688c: StoreField: r2->field_1b = r0
    //     0xbb688c: stur            w0, [x2, #0x1b]
    // 0xbb6890: r1 = <Widget>
    //     0xbb6890: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb6894: r0 = AllocateGrowableArray()
    //     0xbb6894: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb6898: mov             x1, x0
    // 0xbb689c: ldur            x0, [fp, #-8]
    // 0xbb68a0: stur            x1, [fp, #-0x10]
    // 0xbb68a4: StoreField: r1->field_f = r0
    //     0xbb68a4: stur            w0, [x1, #0xf]
    // 0xbb68a8: r5 = 8
    //     0xbb68a8: movz            x5, #0x8
    // 0xbb68ac: StoreField: r1->field_b = r5
    //     0xbb68ac: stur            w5, [x1, #0xb]
    // 0xbb68b0: r0 = Row()
    //     0xbb68b0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbb68b4: r6 = Instance_Axis
    //     0xbb68b4: ldr             x6, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb68b8: StoreField: r0->field_f = r6
    //     0xbb68b8: stur            w6, [x0, #0xf]
    // 0xbb68bc: r7 = Instance_MainAxisAlignment
    //     0xbb68bc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb68c0: ldr             x7, [x7, #0xa08]
    // 0xbb68c4: StoreField: r0->field_13 = r7
    //     0xbb68c4: stur            w7, [x0, #0x13]
    // 0xbb68c8: r8 = Instance_MainAxisSize
    //     0xbb68c8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb68cc: ldr             x8, [x8, #0xa10]
    // 0xbb68d0: ArrayStore: r0[0] = r8  ; List_4
    //     0xbb68d0: stur            w8, [x0, #0x17]
    // 0xbb68d4: r9 = Instance_CrossAxisAlignment
    //     0xbb68d4: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb68d8: ldr             x9, [x9, #0xa18]
    // 0xbb68dc: StoreField: r0->field_1b = r9
    //     0xbb68dc: stur            w9, [x0, #0x1b]
    // 0xbb68e0: r10 = Instance_VerticalDirection
    //     0xbb68e0: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb68e4: ldr             x10, [x10, #0xa20]
    // 0xbb68e8: StoreField: r0->field_23 = r10
    //     0xbb68e8: stur            w10, [x0, #0x23]
    // 0xbb68ec: r11 = Instance_Clip
    //     0xbb68ec: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb68f0: ldr             x11, [x11, #0x38]
    // 0xbb68f4: StoreField: r0->field_2b = r11
    //     0xbb68f4: stur            w11, [x0, #0x2b]
    // 0xbb68f8: StoreField: r0->field_2f = rZR
    //     0xbb68f8: stur            xzr, [x0, #0x2f]
    // 0xbb68fc: ldur            x1, [fp, #-0x10]
    // 0xbb6900: StoreField: r0->field_b = r1
    //     0xbb6900: stur            w1, [x0, #0xb]
    // 0xbb6904: LeaveFrame
    //     0xbb6904: mov             SP, fp
    //     0xbb6908: ldp             fp, lr, [SP], #0x10
    // 0xbb690c: ret
    //     0xbb690c: ret             
    // 0xbb6910: r3 = "Didn\'t receive OTP\? "
    //     0xbb6910: add             x3, PP, #0x53, lsl #12  ; [pp+0x53f38] "Didn\'t receive OTP\? "
    //     0xbb6914: ldr             x3, [x3, #0xf38]
    // 0xbb6918: r4 = "Resend OTP "
    //     0xbb6918: add             x4, PP, #0x53, lsl #12  ; [pp+0x53f40] "Resend OTP "
    //     0xbb691c: ldr             x4, [x4, #0xf40]
    // 0xbb6920: r5 = 8
    //     0xbb6920: movz            x5, #0x8
    // 0xbb6924: r9 = Instance_CrossAxisAlignment
    //     0xbb6924: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb6928: ldr             x9, [x9, #0xa18]
    // 0xbb692c: r7 = Instance_MainAxisAlignment
    //     0xbb692c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb6930: ldr             x7, [x7, #0xa08]
    // 0xbb6934: r8 = Instance_MainAxisSize
    //     0xbb6934: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb6938: ldr             x8, [x8, #0xa10]
    // 0xbb693c: r6 = Instance_Axis
    //     0xbb693c: ldr             x6, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb6940: r10 = Instance_VerticalDirection
    //     0xbb6940: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb6944: ldr             x10, [x10, #0xa20]
    // 0xbb6948: r11 = Instance_Clip
    //     0xbb6948: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb694c: ldr             x11, [x11, #0x38]
    // 0xbb6950: r0 = 60
    //     0xbb6950: movz            x0, #0x3c
    // 0xbb6954: branchIfSmi(r1, 0xbb6960)
    //     0xbb6954: tbz             w1, #0, #0xbb6960
    // 0xbb6958: r0 = LoadClassIdInstr(r1)
    //     0xbb6958: ldur            x0, [x1, #-1]
    //     0xbb695c: ubfx            x0, x0, #0xc, #0x14
    // 0xbb6960: stp             xzr, x1, [SP]
    // 0xbb6964: r0 = GDT[cid_x0 + -0xff5]()
    //     0xbb6964: sub             lr, x0, #0xff5
    //     0xbb6968: ldr             lr, [x21, lr, lsl #3]
    //     0xbb696c: blr             lr
    // 0xbb6970: stur            x0, [fp, #-8]
    // 0xbb6974: tbnz            w0, #4, #0xbb6990
    // 0xbb6978: ldur            x2, [fp, #-0x38]
    // 0xbb697c: r3 = true
    //     0xbb697c: add             x3, NULL, #0x20  ; true
    // 0xbb6980: LoadField: r1 = r2->field_f
    //     0xbb6980: ldur            w1, [x2, #0xf]
    // 0xbb6984: DecompressPointer r1
    //     0xbb6984: add             x1, x1, HEAP, lsl #32
    // 0xbb6988: StoreField: r1->field_13 = r3
    //     0xbb6988: stur            w3, [x1, #0x13]
    // 0xbb698c: b               #0xbb6998
    // 0xbb6990: ldur            x2, [fp, #-0x38]
    // 0xbb6994: r3 = true
    //     0xbb6994: add             x3, NULL, #0x20  ; true
    // 0xbb6998: ldr             x1, [fp, #0x18]
    // 0xbb699c: r0 = of()
    //     0xbb699c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb69a0: LoadField: r1 = r0->field_87
    //     0xbb69a0: ldur            w1, [x0, #0x87]
    // 0xbb69a4: DecompressPointer r1
    //     0xbb69a4: add             x1, x1, HEAP, lsl #32
    // 0xbb69a8: LoadField: r0 = r1->field_2b
    //     0xbb69a8: ldur            w0, [x1, #0x2b]
    // 0xbb69ac: DecompressPointer r0
    //     0xbb69ac: add             x0, x0, HEAP, lsl #32
    // 0xbb69b0: stur            x0, [fp, #-0x10]
    // 0xbb69b4: r1 = Instance_Color
    //     0xbb69b4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb69b8: d0 = 0.700000
    //     0xbb69b8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbb69bc: ldr             d0, [x17, #0xf48]
    // 0xbb69c0: r0 = withOpacity()
    //     0xbb69c0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb69c4: r16 = 12.000000
    //     0xbb69c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb69c8: ldr             x16, [x16, #0x9e8]
    // 0xbb69cc: stp             x16, x0, [SP]
    // 0xbb69d0: ldur            x1, [fp, #-0x10]
    // 0xbb69d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb69d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb69d8: ldr             x4, [x4, #0x9b8]
    // 0xbb69dc: r0 = copyWith()
    //     0xbb69dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb69e0: stur            x0, [fp, #-0x10]
    // 0xbb69e4: r0 = Text()
    //     0xbb69e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb69e8: mov             x3, x0
    // 0xbb69ec: r0 = "Didn\'t receive OTP\? "
    //     0xbb69ec: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f38] "Didn\'t receive OTP\? "
    //     0xbb69f0: ldr             x0, [x0, #0xf38]
    // 0xbb69f4: stur            x3, [fp, #-0x18]
    // 0xbb69f8: StoreField: r3->field_b = r0
    //     0xbb69f8: stur            w0, [x3, #0xb]
    // 0xbb69fc: ldur            x0, [fp, #-0x10]
    // 0xbb6a00: StoreField: r3->field_13 = r0
    //     0xbb6a00: stur            w0, [x3, #0x13]
    // 0xbb6a04: ldur            x2, [fp, #-0x38]
    // 0xbb6a08: LoadField: r0 = r2->field_f
    //     0xbb6a08: ldur            w0, [x2, #0xf]
    // 0xbb6a0c: DecompressPointer r0
    //     0xbb6a0c: add             x0, x0, HEAP, lsl #32
    // 0xbb6a10: LoadField: r1 = r0->field_13
    //     0xbb6a10: ldur            w1, [x0, #0x13]
    // 0xbb6a14: DecompressPointer r1
    //     0xbb6a14: add             x1, x1, HEAP, lsl #32
    // 0xbb6a18: tbnz            w1, #4, #0xbb6a38
    // 0xbb6a1c: ldur            x0, [fp, #-8]
    // 0xbb6a20: tbnz            w0, #4, #0xbb6a38
    // 0xbb6a24: r1 = Function '<anonymous closure>':.
    //     0xbb6a24: add             x1, PP, #0x53, lsl #12  ; [pp+0x53f58] AnonymousClosure: (0xbb6cdc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::build (0xbb5e4c)
    //     0xbb6a28: ldr             x1, [x1, #0xf58]
    // 0xbb6a2c: r0 = AllocateClosure()
    //     0xbb6a2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb6a30: mov             x2, x0
    // 0xbb6a34: b               #0xbb6a3c
    // 0xbb6a38: r2 = Null
    //     0xbb6a38: mov             x2, NULL
    // 0xbb6a3c: ldur            x0, [fp, #-8]
    // 0xbb6a40: ldr             x1, [fp, #0x18]
    // 0xbb6a44: stur            x2, [fp, #-0x10]
    // 0xbb6a48: r0 = of()
    //     0xbb6a48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb6a4c: LoadField: r1 = r0->field_87
    //     0xbb6a4c: ldur            w1, [x0, #0x87]
    // 0xbb6a50: DecompressPointer r1
    //     0xbb6a50: add             x1, x1, HEAP, lsl #32
    // 0xbb6a54: LoadField: r0 = r1->field_2b
    //     0xbb6a54: ldur            w0, [x1, #0x2b]
    // 0xbb6a58: DecompressPointer r0
    //     0xbb6a58: add             x0, x0, HEAP, lsl #32
    // 0xbb6a5c: r16 = Instance_Color
    //     0xbb6a5c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb6a60: ldr             x16, [x16, #0x858]
    // 0xbb6a64: r30 = 12.000000
    //     0xbb6a64: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb6a68: ldr             lr, [lr, #0x9e8]
    // 0xbb6a6c: stp             lr, x16, [SP]
    // 0xbb6a70: mov             x1, x0
    // 0xbb6a74: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb6a74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb6a78: ldr             x4, [x4, #0x9b8]
    // 0xbb6a7c: r0 = copyWith()
    //     0xbb6a7c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb6a80: stur            x0, [fp, #-0x20]
    // 0xbb6a84: r0 = Text()
    //     0xbb6a84: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb6a88: mov             x1, x0
    // 0xbb6a8c: r0 = "Resend OTP "
    //     0xbb6a8c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f40] "Resend OTP "
    //     0xbb6a90: ldr             x0, [x0, #0xf40]
    // 0xbb6a94: stur            x1, [fp, #-0x28]
    // 0xbb6a98: StoreField: r1->field_b = r0
    //     0xbb6a98: stur            w0, [x1, #0xb]
    // 0xbb6a9c: ldur            x0, [fp, #-0x20]
    // 0xbb6aa0: StoreField: r1->field_13 = r0
    //     0xbb6aa0: stur            w0, [x1, #0x13]
    // 0xbb6aa4: r0 = InkWell()
    //     0xbb6aa4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbb6aa8: mov             x2, x0
    // 0xbb6aac: ldur            x0, [fp, #-0x28]
    // 0xbb6ab0: stur            x2, [fp, #-0x20]
    // 0xbb6ab4: StoreField: r2->field_b = r0
    //     0xbb6ab4: stur            w0, [x2, #0xb]
    // 0xbb6ab8: ldur            x0, [fp, #-0x10]
    // 0xbb6abc: StoreField: r2->field_f = r0
    //     0xbb6abc: stur            w0, [x2, #0xf]
    // 0xbb6ac0: r0 = true
    //     0xbb6ac0: add             x0, NULL, #0x20  ; true
    // 0xbb6ac4: StoreField: r2->field_43 = r0
    //     0xbb6ac4: stur            w0, [x2, #0x43]
    // 0xbb6ac8: r1 = Instance_BoxShape
    //     0xbb6ac8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbb6acc: ldr             x1, [x1, #0x80]
    // 0xbb6ad0: StoreField: r2->field_47 = r1
    //     0xbb6ad0: stur            w1, [x2, #0x47]
    // 0xbb6ad4: StoreField: r2->field_6f = r0
    //     0xbb6ad4: stur            w0, [x2, #0x6f]
    // 0xbb6ad8: r1 = false
    //     0xbb6ad8: add             x1, NULL, #0x30  ; false
    // 0xbb6adc: StoreField: r2->field_73 = r1
    //     0xbb6adc: stur            w1, [x2, #0x73]
    // 0xbb6ae0: StoreField: r2->field_83 = r0
    //     0xbb6ae0: stur            w0, [x2, #0x83]
    // 0xbb6ae4: StoreField: r2->field_7b = r1
    //     0xbb6ae4: stur            w1, [x2, #0x7b]
    // 0xbb6ae8: ldur            x0, [fp, #-8]
    // 0xbb6aec: tbnz            w0, #4, #0xbb6af8
    // 0xbb6af0: r3 = ""
    //     0xbb6af0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb6af4: b               #0xbb6b00
    // 0xbb6af8: r3 = "in "
    //     0xbb6af8: add             x3, PP, #0x53, lsl #12  ; [pp+0x53f48] "in "
    //     0xbb6afc: ldr             x3, [x3, #0xf48]
    // 0xbb6b00: ldr             x1, [fp, #0x18]
    // 0xbb6b04: stur            x3, [fp, #-0x10]
    // 0xbb6b08: r0 = of()
    //     0xbb6b08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb6b0c: LoadField: r1 = r0->field_87
    //     0xbb6b0c: ldur            w1, [x0, #0x87]
    // 0xbb6b10: DecompressPointer r1
    //     0xbb6b10: add             x1, x1, HEAP, lsl #32
    // 0xbb6b14: LoadField: r0 = r1->field_2b
    //     0xbb6b14: ldur            w0, [x1, #0x2b]
    // 0xbb6b18: DecompressPointer r0
    //     0xbb6b18: add             x0, x0, HEAP, lsl #32
    // 0xbb6b1c: stur            x0, [fp, #-0x28]
    // 0xbb6b20: r1 = Instance_Color
    //     0xbb6b20: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb6b24: d0 = 0.700000
    //     0xbb6b24: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbb6b28: ldr             d0, [x17, #0xf48]
    // 0xbb6b2c: r0 = withOpacity()
    //     0xbb6b2c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb6b30: r16 = 12.000000
    //     0xbb6b30: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb6b34: ldr             x16, [x16, #0x9e8]
    // 0xbb6b38: stp             x16, x0, [SP]
    // 0xbb6b3c: ldur            x1, [fp, #-0x28]
    // 0xbb6b40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb6b40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb6b44: ldr             x4, [x4, #0x9b8]
    // 0xbb6b48: r0 = copyWith()
    //     0xbb6b48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb6b4c: stur            x0, [fp, #-0x28]
    // 0xbb6b50: r0 = Text()
    //     0xbb6b50: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb6b54: mov             x3, x0
    // 0xbb6b58: ldur            x0, [fp, #-0x10]
    // 0xbb6b5c: stur            x3, [fp, #-0x38]
    // 0xbb6b60: StoreField: r3->field_b = r0
    //     0xbb6b60: stur            w0, [x3, #0xb]
    // 0xbb6b64: ldur            x0, [fp, #-0x28]
    // 0xbb6b68: StoreField: r3->field_13 = r0
    //     0xbb6b68: stur            w0, [x3, #0x13]
    // 0xbb6b6c: ldur            x0, [fp, #-8]
    // 0xbb6b70: tbnz            w0, #4, #0xbb6b80
    // 0xbb6b74: mov             x0, x3
    // 0xbb6b78: r4 = ""
    //     0xbb6b78: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb6b7c: b               #0xbb6bb8
    // 0xbb6b80: ldur            x0, [fp, #-0x30]
    // 0xbb6b84: r1 = Null
    //     0xbb6b84: mov             x1, NULL
    // 0xbb6b88: r2 = 4
    //     0xbb6b88: movz            x2, #0x4
    // 0xbb6b8c: r0 = AllocateArray()
    //     0xbb6b8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb6b90: mov             x1, x0
    // 0xbb6b94: ldur            x0, [fp, #-0x30]
    // 0xbb6b98: StoreField: r1->field_f = r0
    //     0xbb6b98: stur            w0, [x1, #0xf]
    // 0xbb6b9c: r16 = "s"
    //     0xbb6b9c: add             x16, PP, #0xc, lsl #12  ; [pp+0xc728] "s"
    //     0xbb6ba0: ldr             x16, [x16, #0x728]
    // 0xbb6ba4: StoreField: r1->field_13 = r16
    //     0xbb6ba4: stur            w16, [x1, #0x13]
    // 0xbb6ba8: str             x1, [SP]
    // 0xbb6bac: r0 = _interpolate()
    //     0xbb6bac: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbb6bb0: mov             x4, x0
    // 0xbb6bb4: ldur            x0, [fp, #-0x38]
    // 0xbb6bb8: ldur            x3, [fp, #-0x18]
    // 0xbb6bbc: ldur            x2, [fp, #-0x20]
    // 0xbb6bc0: ldr             x1, [fp, #0x18]
    // 0xbb6bc4: stur            x4, [fp, #-8]
    // 0xbb6bc8: r0 = of()
    //     0xbb6bc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb6bcc: LoadField: r1 = r0->field_87
    //     0xbb6bcc: ldur            w1, [x0, #0x87]
    // 0xbb6bd0: DecompressPointer r1
    //     0xbb6bd0: add             x1, x1, HEAP, lsl #32
    // 0xbb6bd4: LoadField: r0 = r1->field_2b
    //     0xbb6bd4: ldur            w0, [x1, #0x2b]
    // 0xbb6bd8: DecompressPointer r0
    //     0xbb6bd8: add             x0, x0, HEAP, lsl #32
    // 0xbb6bdc: r16 = Instance_Color
    //     0xbb6bdc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbb6be0: ldr             x16, [x16, #0x50]
    // 0xbb6be4: r30 = 12.000000
    //     0xbb6be4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb6be8: ldr             lr, [lr, #0x9e8]
    // 0xbb6bec: stp             lr, x16, [SP]
    // 0xbb6bf0: mov             x1, x0
    // 0xbb6bf4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb6bf4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb6bf8: ldr             x4, [x4, #0x9b8]
    // 0xbb6bfc: r0 = copyWith()
    //     0xbb6bfc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb6c00: stur            x0, [fp, #-0x10]
    // 0xbb6c04: r0 = Text()
    //     0xbb6c04: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb6c08: mov             x3, x0
    // 0xbb6c0c: ldur            x0, [fp, #-8]
    // 0xbb6c10: stur            x3, [fp, #-0x28]
    // 0xbb6c14: StoreField: r3->field_b = r0
    //     0xbb6c14: stur            w0, [x3, #0xb]
    // 0xbb6c18: ldur            x0, [fp, #-0x10]
    // 0xbb6c1c: StoreField: r3->field_13 = r0
    //     0xbb6c1c: stur            w0, [x3, #0x13]
    // 0xbb6c20: r1 = Null
    //     0xbb6c20: mov             x1, NULL
    // 0xbb6c24: r2 = 8
    //     0xbb6c24: movz            x2, #0x8
    // 0xbb6c28: r0 = AllocateArray()
    //     0xbb6c28: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb6c2c: mov             x2, x0
    // 0xbb6c30: ldur            x0, [fp, #-0x18]
    // 0xbb6c34: stur            x2, [fp, #-8]
    // 0xbb6c38: StoreField: r2->field_f = r0
    //     0xbb6c38: stur            w0, [x2, #0xf]
    // 0xbb6c3c: ldur            x0, [fp, #-0x20]
    // 0xbb6c40: StoreField: r2->field_13 = r0
    //     0xbb6c40: stur            w0, [x2, #0x13]
    // 0xbb6c44: ldur            x0, [fp, #-0x38]
    // 0xbb6c48: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb6c48: stur            w0, [x2, #0x17]
    // 0xbb6c4c: ldur            x0, [fp, #-0x28]
    // 0xbb6c50: StoreField: r2->field_1b = r0
    //     0xbb6c50: stur            w0, [x2, #0x1b]
    // 0xbb6c54: r1 = <Widget>
    //     0xbb6c54: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb6c58: r0 = AllocateGrowableArray()
    //     0xbb6c58: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb6c5c: mov             x1, x0
    // 0xbb6c60: ldur            x0, [fp, #-8]
    // 0xbb6c64: stur            x1, [fp, #-0x10]
    // 0xbb6c68: StoreField: r1->field_f = r0
    //     0xbb6c68: stur            w0, [x1, #0xf]
    // 0xbb6c6c: r0 = 8
    //     0xbb6c6c: movz            x0, #0x8
    // 0xbb6c70: StoreField: r1->field_b = r0
    //     0xbb6c70: stur            w0, [x1, #0xb]
    // 0xbb6c74: r0 = Row()
    //     0xbb6c74: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbb6c78: r1 = Instance_Axis
    //     0xbb6c78: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb6c7c: StoreField: r0->field_f = r1
    //     0xbb6c7c: stur            w1, [x0, #0xf]
    // 0xbb6c80: r1 = Instance_MainAxisAlignment
    //     0xbb6c80: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb6c84: ldr             x1, [x1, #0xa08]
    // 0xbb6c88: StoreField: r0->field_13 = r1
    //     0xbb6c88: stur            w1, [x0, #0x13]
    // 0xbb6c8c: r1 = Instance_MainAxisSize
    //     0xbb6c8c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb6c90: ldr             x1, [x1, #0xa10]
    // 0xbb6c94: ArrayStore: r0[0] = r1  ; List_4
    //     0xbb6c94: stur            w1, [x0, #0x17]
    // 0xbb6c98: r1 = Instance_CrossAxisAlignment
    //     0xbb6c98: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb6c9c: ldr             x1, [x1, #0xa18]
    // 0xbb6ca0: StoreField: r0->field_1b = r1
    //     0xbb6ca0: stur            w1, [x0, #0x1b]
    // 0xbb6ca4: r1 = Instance_VerticalDirection
    //     0xbb6ca4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb6ca8: ldr             x1, [x1, #0xa20]
    // 0xbb6cac: StoreField: r0->field_23 = r1
    //     0xbb6cac: stur            w1, [x0, #0x23]
    // 0xbb6cb0: r1 = Instance_Clip
    //     0xbb6cb0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb6cb4: ldr             x1, [x1, #0x38]
    // 0xbb6cb8: StoreField: r0->field_2b = r1
    //     0xbb6cb8: stur            w1, [x0, #0x2b]
    // 0xbb6cbc: StoreField: r0->field_2f = rZR
    //     0xbb6cbc: stur            xzr, [x0, #0x2f]
    // 0xbb6cc0: ldur            x1, [fp, #-0x10]
    // 0xbb6cc4: StoreField: r0->field_b = r1
    //     0xbb6cc4: stur            w1, [x0, #0xb]
    // 0xbb6cc8: LeaveFrame
    //     0xbb6cc8: mov             SP, fp
    //     0xbb6ccc: ldp             fp, lr, [SP], #0x10
    // 0xbb6cd0: ret
    //     0xbb6cd0: ret             
    // 0xbb6cd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb6cd4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb6cd8: b               #0xbb66b4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbb6cdc, size: 0x48
    // 0xbb6cdc: EnterFrame
    //     0xbb6cdc: stp             fp, lr, [SP, #-0x10]!
    //     0xbb6ce0: mov             fp, SP
    // 0xbb6ce4: ldr             x0, [fp, #0x10]
    // 0xbb6ce8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb6ce8: ldur            w1, [x0, #0x17]
    // 0xbb6cec: DecompressPointer r1
    //     0xbb6cec: add             x1, x1, HEAP, lsl #32
    // 0xbb6cf0: CheckStackOverflow
    //     0xbb6cf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb6cf4: cmp             SP, x16
    //     0xbb6cf8: b.ls            #0xbb6d1c
    // 0xbb6cfc: LoadField: r0 = r1->field_f
    //     0xbb6cfc: ldur            w0, [x1, #0xf]
    // 0xbb6d00: DecompressPointer r0
    //     0xbb6d00: add             x0, x0, HEAP, lsl #32
    // 0xbb6d04: mov             x1, x0
    // 0xbb6d08: r0 = _resendCode()
    //     0xbb6d08: bl              #0xbb6d24  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::_resendCode
    // 0xbb6d0c: r0 = Null
    //     0xbb6d0c: mov             x0, NULL
    // 0xbb6d10: LeaveFrame
    //     0xbb6d10: mov             SP, fp
    //     0xbb6d14: ldp             fp, lr, [SP], #0x10
    // 0xbb6d18: ret
    //     0xbb6d18: ret             
    // 0xbb6d1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb6d1c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb6d20: b               #0xbb6cfc
  }
  _ _resendCode(/* No info */) {
    // ** addr: 0xbb6d24, size: 0x64
    // 0xbb6d24: EnterFrame
    //     0xbb6d24: stp             fp, lr, [SP, #-0x10]!
    //     0xbb6d28: mov             fp, SP
    // 0xbb6d2c: AllocStack(0x8)
    //     0xbb6d2c: sub             SP, SP, #8
    // 0xbb6d30: SetupParameters(_CheckoutOtpWidgetState this /* r1 => r1, fp-0x8 */)
    //     0xbb6d30: stur            x1, [fp, #-8]
    // 0xbb6d34: CheckStackOverflow
    //     0xbb6d34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb6d38: cmp             SP, x16
    //     0xbb6d3c: b.ls            #0xbb6d80
    // 0xbb6d40: r1 = 1
    //     0xbb6d40: movz            x1, #0x1
    // 0xbb6d44: r0 = AllocateContext()
    //     0xbb6d44: bl              #0x16f6108  ; AllocateContextStub
    // 0xbb6d48: mov             x1, x0
    // 0xbb6d4c: ldur            x0, [fp, #-8]
    // 0xbb6d50: StoreField: r1->field_f = r0
    //     0xbb6d50: stur            w0, [x1, #0xf]
    // 0xbb6d54: mov             x2, x1
    // 0xbb6d58: r1 = Function '<anonymous closure>':.
    //     0xbb6d58: add             x1, PP, #0x53, lsl #12  ; [pp+0x53f60] AnonymousClosure: (0xbb6d88), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::_resendCode (0xbb6d24)
    //     0xbb6d5c: ldr             x1, [x1, #0xf60]
    // 0xbb6d60: r0 = AllocateClosure()
    //     0xbb6d60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb6d64: ldur            x1, [fp, #-8]
    // 0xbb6d68: mov             x2, x0
    // 0xbb6d6c: r0 = setState()
    //     0xbb6d6c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbb6d70: r0 = Null
    //     0xbb6d70: mov             x0, NULL
    // 0xbb6d74: LeaveFrame
    //     0xbb6d74: mov             SP, fp
    //     0xbb6d78: ldp             fp, lr, [SP], #0x10
    // 0xbb6d7c: ret
    //     0xbb6d7c: ret             
    // 0xbb6d80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb6d80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb6d84: b               #0xbb6d40
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbb6d88, size: 0xe0
    // 0xbb6d88: EnterFrame
    //     0xbb6d88: stp             fp, lr, [SP, #-0x10]!
    //     0xbb6d8c: mov             fp, SP
    // 0xbb6d90: AllocStack(0x18)
    //     0xbb6d90: sub             SP, SP, #0x18
    // 0xbb6d94: SetupParameters()
    //     0xbb6d94: add             x0, NULL, #0x30  ; false
    //     0xbb6d98: ldr             x1, [fp, #0x10]
    //     0xbb6d9c: ldur            w2, [x1, #0x17]
    //     0xbb6da0: add             x2, x2, HEAP, lsl #32
    //     0xbb6da4: stur            x2, [fp, #-8]
    // 0xbb6d94: r0 = false
    // 0xbb6da8: CheckStackOverflow
    //     0xbb6da8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb6dac: cmp             SP, x16
    //     0xbb6db0: b.ls            #0xbb6e5c
    // 0xbb6db4: LoadField: r1 = r2->field_f
    //     0xbb6db4: ldur            w1, [x2, #0xf]
    // 0xbb6db8: DecompressPointer r1
    //     0xbb6db8: add             x1, x1, HEAP, lsl #32
    // 0xbb6dbc: StoreField: r1->field_13 = r0
    //     0xbb6dbc: stur            w0, [x1, #0x13]
    // 0xbb6dc0: LoadField: r0 = r1->field_b
    //     0xbb6dc0: ldur            w0, [x1, #0xb]
    // 0xbb6dc4: DecompressPointer r0
    //     0xbb6dc4: add             x0, x0, HEAP, lsl #32
    // 0xbb6dc8: cmp             w0, NULL
    // 0xbb6dcc: b.eq            #0xbb6e64
    // 0xbb6dd0: LoadField: r1 = r0->field_f
    //     0xbb6dd0: ldur            w1, [x0, #0xf]
    // 0xbb6dd4: DecompressPointer r1
    //     0xbb6dd4: add             x1, x1, HEAP, lsl #32
    // 0xbb6dd8: str             x1, [SP]
    // 0xbb6ddc: r4 = 0
    //     0xbb6ddc: movz            x4, #0
    // 0xbb6de0: ldr             x0, [SP]
    // 0xbb6de4: r16 = UnlinkedCall_0x613b5c
    //     0xbb6de4: add             x16, PP, #0x53, lsl #12  ; [pp+0x53f68] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbb6de8: add             x16, x16, #0xf68
    // 0xbb6dec: ldp             x5, lr, [x16]
    // 0xbb6df0: blr             lr
    // 0xbb6df4: ldur            x0, [fp, #-8]
    // 0xbb6df8: LoadField: r3 = r0->field_f
    //     0xbb6df8: ldur            w3, [x0, #0xf]
    // 0xbb6dfc: DecompressPointer r3
    //     0xbb6dfc: add             x3, x3, HEAP, lsl #32
    // 0xbb6e00: stur            x3, [fp, #-0x10]
    // 0xbb6e04: r1 = Function '<anonymous closure>':.
    //     0xbb6e04: add             x1, PP, #0x53, lsl #12  ; [pp+0x53f78] AnonymousClosure: (0xa0a694), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::_CheckoutOtpWidgetState (0xa0a6d8)
    //     0xbb6e08: ldr             x1, [x1, #0xf78]
    // 0xbb6e0c: r2 = Null
    //     0xbb6e0c: mov             x2, NULL
    // 0xbb6e10: r0 = AllocateClosure()
    //     0xbb6e10: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb6e14: mov             x2, x0
    // 0xbb6e18: r1 = <int>
    //     0xbb6e18: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xbb6e1c: r0 = Stream.periodic()
    //     0xbb6e1c: bl              #0xa0a0f8  ; [dart:async] Stream::Stream.periodic
    // 0xbb6e20: mov             x1, x0
    // 0xbb6e24: r2 = 30
    //     0xbb6e24: movz            x2, #0x1e
    // 0xbb6e28: r0 = take()
    //     0xbb6e28: bl              #0xa0a080  ; [dart:async] Stream::take
    // 0xbb6e2c: ldur            x1, [fp, #-0x10]
    // 0xbb6e30: StoreField: r1->field_1b = r0
    //     0xbb6e30: stur            w0, [x1, #0x1b]
    //     0xbb6e34: ldurb           w16, [x1, #-1]
    //     0xbb6e38: ldurb           w17, [x0, #-1]
    //     0xbb6e3c: and             x16, x17, x16, lsr #2
    //     0xbb6e40: tst             x16, HEAP, lsr #32
    //     0xbb6e44: b.eq            #0xbb6e4c
    //     0xbb6e48: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xbb6e4c: r0 = Null
    //     0xbb6e4c: mov             x0, NULL
    // 0xbb6e50: LeaveFrame
    //     0xbb6e50: mov             SP, fp
    //     0xbb6e54: ldp             fp, lr, [SP], #0x10
    // 0xbb6e58: ret
    //     0xbb6e58: ret             
    // 0xbb6e5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb6e5c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb6e60: b               #0xbb6db4
    // 0xbb6e64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb6e64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0xbb6e68, size: 0x10c
    // 0xbb6e68: EnterFrame
    //     0xbb6e68: stp             fp, lr, [SP, #-0x10]!
    //     0xbb6e6c: mov             fp, SP
    // 0xbb6e70: AllocStack(0x28)
    //     0xbb6e70: sub             SP, SP, #0x28
    // 0xbb6e74: SetupParameters()
    //     0xbb6e74: ldr             x0, [fp, #0x18]
    //     0xbb6e78: ldur            w1, [x0, #0x17]
    //     0xbb6e7c: add             x1, x1, HEAP, lsl #32
    //     0xbb6e80: stur            x1, [fp, #-8]
    // 0xbb6e84: CheckStackOverflow
    //     0xbb6e84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb6e88: cmp             SP, x16
    //     0xbb6e8c: b.ls            #0xbb6f68
    // 0xbb6e90: LoadField: r2 = r1->field_f
    //     0xbb6e90: ldur            w2, [x1, #0xf]
    // 0xbb6e94: DecompressPointer r2
    //     0xbb6e94: add             x2, x2, HEAP, lsl #32
    // 0xbb6e98: ldr             x3, [fp, #0x10]
    // 0xbb6e9c: cmp             w3, NULL
    // 0xbb6ea0: b.ne            #0xbb6eac
    // 0xbb6ea4: r0 = ""
    //     0xbb6ea4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb6ea8: b               #0xbb6eb0
    // 0xbb6eac: mov             x0, x3
    // 0xbb6eb0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb6eb0: stur            w0, [x2, #0x17]
    //     0xbb6eb4: ldurb           w16, [x2, #-1]
    //     0xbb6eb8: ldurb           w17, [x0, #-1]
    //     0xbb6ebc: and             x16, x17, x16, lsr #2
    //     0xbb6ec0: tst             x16, HEAP, lsr #32
    //     0xbb6ec4: b.eq            #0xbb6ecc
    //     0xbb6ec8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbb6ecc: cmp             w3, NULL
    // 0xbb6ed0: b.eq            #0xbb6f58
    // 0xbb6ed4: LoadField: r0 = r3->field_7
    //     0xbb6ed4: ldur            w0, [x3, #7]
    // 0xbb6ed8: cmp             w0, #8
    // 0xbb6edc: b.ne            #0xbb6f58
    // 0xbb6ee0: LoadField: r0 = r2->field_b
    //     0xbb6ee0: ldur            w0, [x2, #0xb]
    // 0xbb6ee4: DecompressPointer r0
    //     0xbb6ee4: add             x0, x0, HEAP, lsl #32
    // 0xbb6ee8: cmp             w0, NULL
    // 0xbb6eec: b.eq            #0xbb6f70
    // 0xbb6ef0: LoadField: r2 = r0->field_b
    //     0xbb6ef0: ldur            w2, [x0, #0xb]
    // 0xbb6ef4: DecompressPointer r2
    //     0xbb6ef4: add             x2, x2, HEAP, lsl #32
    // 0xbb6ef8: stp             x3, x2, [SP, #8]
    // 0xbb6efc: r16 = true
    //     0xbb6efc: add             x16, NULL, #0x20  ; true
    // 0xbb6f00: str             x16, [SP]
    // 0xbb6f04: r4 = 0
    //     0xbb6f04: movz            x4, #0
    // 0xbb6f08: ldr             x0, [SP, #0x10]
    // 0xbb6f0c: r16 = UnlinkedCall_0x613b5c
    //     0xbb6f0c: add             x16, PP, #0x53, lsl #12  ; [pp+0x53f80] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbb6f10: add             x16, x16, #0xf80
    // 0xbb6f14: ldp             x5, lr, [x16]
    // 0xbb6f18: blr             lr
    // 0xbb6f1c: ldur            x0, [fp, #-8]
    // 0xbb6f20: LoadField: r1 = r0->field_13
    //     0xbb6f20: ldur            w1, [x0, #0x13]
    // 0xbb6f24: DecompressPointer r1
    //     0xbb6f24: add             x1, x1, HEAP, lsl #32
    // 0xbb6f28: r0 = of()
    //     0xbb6f28: bl              #0x81ef68  ; [package:flutter/src/widgets/focus_scope.dart] FocusScope::of
    // 0xbb6f2c: stur            x0, [fp, #-8]
    // 0xbb6f30: r0 = FocusNode()
    //     0xbb6f30: bl              #0x8182fc  ; AllocateFocusNodeStub -> FocusNode (size=0x68)
    // 0xbb6f34: mov             x1, x0
    // 0xbb6f38: stur            x0, [fp, #-0x10]
    // 0xbb6f3c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbb6f3c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbb6f40: r0 = FocusNode()
    //     0xbb6f40: bl              #0x695c10  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::FocusNode
    // 0xbb6f44: ldur            x16, [fp, #-0x10]
    // 0xbb6f48: str             x16, [SP]
    // 0xbb6f4c: ldur            x1, [fp, #-8]
    // 0xbb6f50: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xbb6f50: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xbb6f54: r0 = requestFocus()
    //     0xbb6f54: bl              #0x6595f4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::requestFocus
    // 0xbb6f58: r0 = Null
    //     0xbb6f58: mov             x0, NULL
    // 0xbb6f5c: LeaveFrame
    //     0xbb6f5c: mov             SP, fp
    //     0xbb6f60: ldp             fp, lr, [SP], #0x10
    // 0xbb6f64: ret
    //     0xbb6f64: ret             
    // 0xbb6f68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb6f68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb6f6c: b               #0xbb6e90
    // 0xbb6f70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb6f70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String) {
    // ** addr: 0xbb6f74, size: 0xa8
    // 0xbb6f74: EnterFrame
    //     0xbb6f74: stp             fp, lr, [SP, #-0x10]!
    //     0xbb6f78: mov             fp, SP
    // 0xbb6f7c: AllocStack(0x18)
    //     0xbb6f7c: sub             SP, SP, #0x18
    // 0xbb6f80: SetupParameters()
    //     0xbb6f80: ldr             x0, [fp, #0x18]
    //     0xbb6f84: ldur            w1, [x0, #0x17]
    //     0xbb6f88: add             x1, x1, HEAP, lsl #32
    // 0xbb6f8c: CheckStackOverflow
    //     0xbb6f8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb6f90: cmp             SP, x16
    //     0xbb6f94: b.ls            #0xbb7010
    // 0xbb6f98: LoadField: r2 = r1->field_f
    //     0xbb6f98: ldur            w2, [x1, #0xf]
    // 0xbb6f9c: DecompressPointer r2
    //     0xbb6f9c: add             x2, x2, HEAP, lsl #32
    // 0xbb6fa0: ldr             x0, [fp, #0x10]
    // 0xbb6fa4: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb6fa4: stur            w0, [x2, #0x17]
    //     0xbb6fa8: ldurb           w16, [x2, #-1]
    //     0xbb6fac: ldurb           w17, [x0, #-1]
    //     0xbb6fb0: and             x16, x17, x16, lsr #2
    //     0xbb6fb4: tst             x16, HEAP, lsr #32
    //     0xbb6fb8: b.eq            #0xbb6fc0
    //     0xbb6fbc: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbb6fc0: LoadField: r0 = r2->field_b
    //     0xbb6fc0: ldur            w0, [x2, #0xb]
    // 0xbb6fc4: DecompressPointer r0
    //     0xbb6fc4: add             x0, x0, HEAP, lsl #32
    // 0xbb6fc8: cmp             w0, NULL
    // 0xbb6fcc: b.eq            #0xbb7018
    // 0xbb6fd0: LoadField: r1 = r0->field_b
    //     0xbb6fd0: ldur            w1, [x0, #0xb]
    // 0xbb6fd4: DecompressPointer r1
    //     0xbb6fd4: add             x1, x1, HEAP, lsl #32
    // 0xbb6fd8: ldr             x16, [fp, #0x10]
    // 0xbb6fdc: stp             x16, x1, [SP, #8]
    // 0xbb6fe0: r16 = true
    //     0xbb6fe0: add             x16, NULL, #0x20  ; true
    // 0xbb6fe4: str             x16, [SP]
    // 0xbb6fe8: r4 = 0
    //     0xbb6fe8: movz            x4, #0
    // 0xbb6fec: ldr             x0, [SP, #0x10]
    // 0xbb6ff0: r16 = UnlinkedCall_0x613b5c
    //     0xbb6ff0: add             x16, PP, #0x53, lsl #12  ; [pp+0x53f90] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbb6ff4: add             x16, x16, #0xf90
    // 0xbb6ff8: ldp             x5, lr, [x16]
    // 0xbb6ffc: blr             lr
    // 0xbb7000: r0 = Null
    //     0xbb7000: mov             x0, NULL
    // 0xbb7004: LeaveFrame
    //     0xbb7004: mov             SP, fp
    //     0xbb7008: ldp             fp, lr, [SP], #0x10
    // 0xbb700c: ret
    //     0xbb700c: ret             
    // 0xbb7010: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb7010: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb7014: b               #0xbb6f98
    // 0xbb7018: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7018: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4019, size: 0x20, field offset: 0xc
//   const constructor, 
class CheckoutOtpWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc802d8, size: 0x48
    // 0xc802d8: EnterFrame
    //     0xc802d8: stp             fp, lr, [SP, #-0x10]!
    //     0xc802dc: mov             fp, SP
    // 0xc802e0: AllocStack(0x8)
    //     0xc802e0: sub             SP, SP, #8
    // 0xc802e4: CheckStackOverflow
    //     0xc802e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc802e8: cmp             SP, x16
    //     0xc802ec: b.ls            #0xc80318
    // 0xc802f0: r1 = <CheckoutOtpWidget>
    //     0xc802f0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48618] TypeArguments: <CheckoutOtpWidget>
    //     0xc802f4: ldr             x1, [x1, #0x618]
    // 0xc802f8: r0 = _CheckoutOtpWidgetState()
    //     0xc802f8: bl              #0xc80320  ; Allocate_CheckoutOtpWidgetStateStub -> _CheckoutOtpWidgetState (size=0x20)
    // 0xc802fc: mov             x1, x0
    // 0xc80300: stur            x0, [fp, #-8]
    // 0xc80304: r0 = _CheckoutOtpWidgetState()
    //     0xc80304: bl              #0xa0a6d8  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::_CheckoutOtpWidgetState
    // 0xc80308: ldur            x0, [fp, #-8]
    // 0xc8030c: LeaveFrame
    //     0xc8030c: mov             SP, fp
    //     0xc80310: ldp             fp, lr, [SP], #0x10
    // 0xc80314: ret
    //     0xc80314: ret             
    // 0xc80318: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc80318: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc8031c: b               #0xc802f0
  }
}
