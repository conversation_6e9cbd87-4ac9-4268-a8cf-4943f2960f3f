// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/offer_section_widget.dart

// class id: 1049374, size: 0x8
class :: {
}

// class id: 3357, size: 0x14, field offset: 0x14
class _OfferSectionWidgetState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x94123c, size: 0x130
    // 0x94123c: EnterFrame
    //     0x94123c: stp             fp, lr, [SP, #-0x10]!
    //     0x941240: mov             fp, SP
    // 0x941244: AllocStack(0x18)
    //     0x941244: sub             SP, SP, #0x18
    // 0x941248: SetupParameters(_OfferSectionWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x941248: stur            x1, [fp, #-8]
    // 0x94124c: CheckStackOverflow
    //     0x94124c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x941250: cmp             SP, x16
    //     0x941254: b.ls            #0x941360
    // 0x941258: r1 = 1
    //     0x941258: movz            x1, #0x1
    // 0x94125c: r0 = AllocateContext()
    //     0x94125c: bl              #0x16f6108  ; AllocateContextStub
    // 0x941260: mov             x1, x0
    // 0x941264: ldur            x0, [fp, #-8]
    // 0x941268: StoreField: r1->field_f = r0
    //     0x941268: stur            w0, [x1, #0xf]
    // 0x94126c: r0 = LoadStaticField(0x878)
    //     0x94126c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x941270: ldr             x0, [x0, #0x10f0]
    // 0x941274: cmp             w0, NULL
    // 0x941278: b.eq            #0x941368
    // 0x94127c: LoadField: r3 = r0->field_53
    //     0x94127c: ldur            w3, [x0, #0x53]
    // 0x941280: DecompressPointer r3
    //     0x941280: add             x3, x3, HEAP, lsl #32
    // 0x941284: stur            x3, [fp, #-0x10]
    // 0x941288: LoadField: r0 = r3->field_7
    //     0x941288: ldur            w0, [x3, #7]
    // 0x94128c: DecompressPointer r0
    //     0x94128c: add             x0, x0, HEAP, lsl #32
    // 0x941290: mov             x2, x1
    // 0x941294: stur            x0, [fp, #-8]
    // 0x941298: r1 = Function '<anonymous closure>':.
    //     0x941298: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b40] AnonymousClosure: (0x906d90), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::initState (0x948564)
    //     0x94129c: ldr             x1, [x1, #0xb40]
    // 0x9412a0: r0 = AllocateClosure()
    //     0x9412a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9412a4: ldur            x2, [fp, #-8]
    // 0x9412a8: mov             x3, x0
    // 0x9412ac: r1 = Null
    //     0x9412ac: mov             x1, NULL
    // 0x9412b0: stur            x3, [fp, #-8]
    // 0x9412b4: cmp             w2, NULL
    // 0x9412b8: b.eq            #0x9412d8
    // 0x9412bc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9412bc: ldur            w4, [x2, #0x17]
    // 0x9412c0: DecompressPointer r4
    //     0x9412c0: add             x4, x4, HEAP, lsl #32
    // 0x9412c4: r8 = X0
    //     0x9412c4: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x9412c8: LoadField: r9 = r4->field_7
    //     0x9412c8: ldur            x9, [x4, #7]
    // 0x9412cc: r3 = Null
    //     0x9412cc: add             x3, PP, #0x56, lsl #12  ; [pp+0x56b48] Null
    //     0x9412d0: ldr             x3, [x3, #0xb48]
    // 0x9412d4: blr             x9
    // 0x9412d8: ldur            x0, [fp, #-0x10]
    // 0x9412dc: LoadField: r1 = r0->field_b
    //     0x9412dc: ldur            w1, [x0, #0xb]
    // 0x9412e0: LoadField: r2 = r0->field_f
    //     0x9412e0: ldur            w2, [x0, #0xf]
    // 0x9412e4: DecompressPointer r2
    //     0x9412e4: add             x2, x2, HEAP, lsl #32
    // 0x9412e8: LoadField: r3 = r2->field_b
    //     0x9412e8: ldur            w3, [x2, #0xb]
    // 0x9412ec: r2 = LoadInt32Instr(r1)
    //     0x9412ec: sbfx            x2, x1, #1, #0x1f
    // 0x9412f0: stur            x2, [fp, #-0x18]
    // 0x9412f4: r1 = LoadInt32Instr(r3)
    //     0x9412f4: sbfx            x1, x3, #1, #0x1f
    // 0x9412f8: cmp             x2, x1
    // 0x9412fc: b.ne            #0x941308
    // 0x941300: mov             x1, x0
    // 0x941304: r0 = _growToNextCapacity()
    //     0x941304: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x941308: ldur            x2, [fp, #-0x10]
    // 0x94130c: ldur            x3, [fp, #-0x18]
    // 0x941310: add             x4, x3, #1
    // 0x941314: lsl             x5, x4, #1
    // 0x941318: StoreField: r2->field_b = r5
    //     0x941318: stur            w5, [x2, #0xb]
    // 0x94131c: LoadField: r1 = r2->field_f
    //     0x94131c: ldur            w1, [x2, #0xf]
    // 0x941320: DecompressPointer r1
    //     0x941320: add             x1, x1, HEAP, lsl #32
    // 0x941324: ldur            x0, [fp, #-8]
    // 0x941328: ArrayStore: r1[r3] = r0  ; List_4
    //     0x941328: add             x25, x1, x3, lsl #2
    //     0x94132c: add             x25, x25, #0xf
    //     0x941330: str             w0, [x25]
    //     0x941334: tbz             w0, #0, #0x941350
    //     0x941338: ldurb           w16, [x1, #-1]
    //     0x94133c: ldurb           w17, [x0, #-1]
    //     0x941340: and             x16, x17, x16, lsr #2
    //     0x941344: tst             x16, HEAP, lsr #32
    //     0x941348: b.eq            #0x941350
    //     0x94134c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x941350: r0 = Null
    //     0x941350: mov             x0, NULL
    // 0x941354: LeaveFrame
    //     0x941354: mov             SP, fp
    //     0x941358: ldp             fp, lr, [SP], #0x10
    // 0x94135c: ret
    //     0x94135c: ret             
    // 0x941360: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x941360: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x941364: b               #0x941258
    // 0x941368: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941368: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb4a86c, size: 0x15e0
    // 0xb4a86c: EnterFrame
    //     0xb4a86c: stp             fp, lr, [SP, #-0x10]!
    //     0xb4a870: mov             fp, SP
    // 0xb4a874: AllocStack(0x78)
    //     0xb4a874: sub             SP, SP, #0x78
    // 0xb4a878: SetupParameters(_OfferSectionWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb4a878: stur            x1, [fp, #-8]
    //     0xb4a87c: stur            x2, [fp, #-0x10]
    // 0xb4a880: CheckStackOverflow
    //     0xb4a880: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4a884: cmp             SP, x16
    //     0xb4a888: b.ls            #0xb4be24
    // 0xb4a88c: r1 = 2
    //     0xb4a88c: movz            x1, #0x2
    // 0xb4a890: r0 = AllocateContext()
    //     0xb4a890: bl              #0x16f6108  ; AllocateContextStub
    // 0xb4a894: mov             x2, x0
    // 0xb4a898: ldur            x1, [fp, #-8]
    // 0xb4a89c: stur            x2, [fp, #-0x18]
    // 0xb4a8a0: StoreField: r2->field_f = r1
    //     0xb4a8a0: stur            w1, [x2, #0xf]
    // 0xb4a8a4: ldur            x0, [fp, #-0x10]
    // 0xb4a8a8: StoreField: r2->field_13 = r0
    //     0xb4a8a8: stur            w0, [x2, #0x13]
    // 0xb4a8ac: LoadField: r0 = r1->field_b
    //     0xb4a8ac: ldur            w0, [x1, #0xb]
    // 0xb4a8b0: DecompressPointer r0
    //     0xb4a8b0: add             x0, x0, HEAP, lsl #32
    // 0xb4a8b4: cmp             w0, NULL
    // 0xb4a8b8: b.eq            #0xb4be2c
    // 0xb4a8bc: LoadField: r3 = r0->field_f
    //     0xb4a8bc: ldur            w3, [x0, #0xf]
    // 0xb4a8c0: DecompressPointer r3
    //     0xb4a8c0: add             x3, x3, HEAP, lsl #32
    // 0xb4a8c4: LoadField: r4 = r3->field_b
    //     0xb4a8c4: ldur            w4, [x3, #0xb]
    // 0xb4a8c8: DecompressPointer r4
    //     0xb4a8c8: add             x4, x4, HEAP, lsl #32
    // 0xb4a8cc: cmp             w4, NULL
    // 0xb4a8d0: b.ne            #0xb4a8dc
    // 0xb4a8d4: r3 = Null
    //     0xb4a8d4: mov             x3, NULL
    // 0xb4a8d8: b               #0xb4a8f0
    // 0xb4a8dc: LoadField: r3 = r4->field_13
    //     0xb4a8dc: ldur            w3, [x4, #0x13]
    // 0xb4a8e0: DecompressPointer r3
    //     0xb4a8e0: add             x3, x3, HEAP, lsl #32
    // 0xb4a8e4: LoadField: r4 = r3->field_7
    //     0xb4a8e4: ldur            w4, [x3, #7]
    // 0xb4a8e8: DecompressPointer r4
    //     0xb4a8e8: add             x4, x4, HEAP, lsl #32
    // 0xb4a8ec: mov             x3, x4
    // 0xb4a8f0: cmp             w3, NULL
    // 0xb4a8f4: b.ne            #0xb4a900
    // 0xb4a8f8: r3 = 0
    //     0xb4a8f8: movz            x3, #0
    // 0xb4a8fc: b               #0xb4a910
    // 0xb4a900: r4 = LoadInt32Instr(r3)
    //     0xb4a900: sbfx            x4, x3, #1, #0x1f
    //     0xb4a904: tbz             w3, #0, #0xb4a90c
    //     0xb4a908: ldur            x4, [x3, #7]
    // 0xb4a90c: mov             x3, x4
    // 0xb4a910: cmp             x3, #0
    // 0xb4a914: r16 = true
    //     0xb4a914: add             x16, NULL, #0x20  ; true
    // 0xb4a918: r17 = false
    //     0xb4a918: add             x17, NULL, #0x30  ; false
    // 0xb4a91c: csel            x4, x16, x17, gt
    // 0xb4a920: stur            x4, [fp, #-0x10]
    // 0xb4a924: LoadField: r3 = r0->field_13
    //     0xb4a924: ldur            w3, [x0, #0x13]
    // 0xb4a928: DecompressPointer r3
    //     0xb4a928: add             x3, x3, HEAP, lsl #32
    // 0xb4a92c: r0 = LoadClassIdInstr(r3)
    //     0xb4a92c: ldur            x0, [x3, #-1]
    //     0xb4a930: ubfx            x0, x0, #0xc, #0x14
    // 0xb4a934: r16 = ""
    //     0xb4a934: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4a938: stp             x16, x3, [SP]
    // 0xb4a93c: mov             lr, x0
    // 0xb4a940: ldr             lr, [x21, lr, lsl #3]
    // 0xb4a944: blr             lr
    // 0xb4a948: tbnz            w0, #4, #0xb4aee4
    // 0xb4a94c: ldur            x0, [fp, #-8]
    // 0xb4a950: r0 = Radius()
    //     0xb4a950: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb4a954: d0 = 20.000000
    //     0xb4a954: fmov            d0, #20.00000000
    // 0xb4a958: stur            x0, [fp, #-0x20]
    // 0xb4a95c: StoreField: r0->field_7 = d0
    //     0xb4a95c: stur            d0, [x0, #7]
    // 0xb4a960: StoreField: r0->field_f = d0
    //     0xb4a960: stur            d0, [x0, #0xf]
    // 0xb4a964: r0 = BorderRadius()
    //     0xb4a964: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb4a968: mov             x1, x0
    // 0xb4a96c: ldur            x0, [fp, #-0x20]
    // 0xb4a970: stur            x1, [fp, #-0x28]
    // 0xb4a974: StoreField: r1->field_7 = r0
    //     0xb4a974: stur            w0, [x1, #7]
    // 0xb4a978: StoreField: r1->field_b = r0
    //     0xb4a978: stur            w0, [x1, #0xb]
    // 0xb4a97c: StoreField: r1->field_f = r0
    //     0xb4a97c: stur            w0, [x1, #0xf]
    // 0xb4a980: StoreField: r1->field_13 = r0
    //     0xb4a980: stur            w0, [x1, #0x13]
    // 0xb4a984: r0 = BoxDecoration()
    //     0xb4a984: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb4a988: r1 = Instance_Color
    //     0xb4a988: add             x1, PP, #0x40, lsl #12  ; [pp+0x40860] Obj!Color@d6b0a1
    //     0xb4a98c: ldr             x1, [x1, #0x860]
    // 0xb4a990: stur            x0, [fp, #-0x20]
    // 0xb4a994: StoreField: r0->field_7 = r1
    //     0xb4a994: stur            w1, [x0, #7]
    // 0xb4a998: ldur            x1, [fp, #-0x28]
    // 0xb4a99c: StoreField: r0->field_13 = r1
    //     0xb4a99c: stur            w1, [x0, #0x13]
    // 0xb4a9a0: r1 = Instance_BoxShape
    //     0xb4a9a0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb4a9a4: ldr             x1, [x1, #0x80]
    // 0xb4a9a8: StoreField: r0->field_23 = r1
    //     0xb4a9a8: stur            w1, [x0, #0x23]
    // 0xb4a9ac: r0 = SvgPicture()
    //     0xb4a9ac: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb4a9b0: stur            x0, [fp, #-0x28]
    // 0xb4a9b4: r16 = Instance_BoxFit
    //     0xb4a9b4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb4a9b8: ldr             x16, [x16, #0xb18]
    // 0xb4a9bc: r30 = 40.000000
    //     0xb4a9bc: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb4a9c0: ldr             lr, [lr, #8]
    // 0xb4a9c4: stp             lr, x16, [SP, #8]
    // 0xb4a9c8: r16 = 40.000000
    //     0xb4a9c8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb4a9cc: ldr             x16, [x16, #8]
    // 0xb4a9d0: str             x16, [SP]
    // 0xb4a9d4: mov             x1, x0
    // 0xb4a9d8: r2 = "assets/images/offer_icon.svg"
    //     0xb4a9d8: add             x2, PP, #0x43, lsl #12  ; [pp+0x43758] "assets/images/offer_icon.svg"
    //     0xb4a9dc: ldr             x2, [x2, #0x758]
    // 0xb4a9e0: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0xb4a9e0: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d8e0] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0xb4a9e4: ldr             x4, [x4, #0x8e0]
    // 0xb4a9e8: r0 = SvgPicture.asset()
    //     0xb4a9e8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb4a9ec: r0 = Padding()
    //     0xb4a9ec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4a9f0: mov             x3, x0
    // 0xb4a9f4: r0 = Instance_EdgeInsets
    //     0xb4a9f4: add             x0, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb4a9f8: ldr             x0, [x0, #0x878]
    // 0xb4a9fc: stur            x3, [fp, #-0x30]
    // 0xb4aa00: StoreField: r3->field_f = r0
    //     0xb4aa00: stur            w0, [x3, #0xf]
    // 0xb4aa04: ldur            x1, [fp, #-0x28]
    // 0xb4aa08: StoreField: r3->field_b = r1
    //     0xb4aa08: stur            w1, [x3, #0xb]
    // 0xb4aa0c: r1 = Null
    //     0xb4aa0c: mov             x1, NULL
    // 0xb4aa10: r2 = 4
    //     0xb4aa10: movz            x2, #0x4
    // 0xb4aa14: r0 = AllocateArray()
    //     0xb4aa14: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4aa18: r16 = "Save upto "
    //     0xb4aa18: add             x16, PP, #0x38, lsl #12  ; [pp+0x38ca0] "Save upto "
    //     0xb4aa1c: ldr             x16, [x16, #0xca0]
    // 0xb4aa20: StoreField: r0->field_f = r16
    //     0xb4aa20: stur            w16, [x0, #0xf]
    // 0xb4aa24: ldur            x1, [fp, #-8]
    // 0xb4aa28: LoadField: r2 = r1->field_b
    //     0xb4aa28: ldur            w2, [x1, #0xb]
    // 0xb4aa2c: DecompressPointer r2
    //     0xb4aa2c: add             x2, x2, HEAP, lsl #32
    // 0xb4aa30: cmp             w2, NULL
    // 0xb4aa34: b.eq            #0xb4be30
    // 0xb4aa38: LoadField: r3 = r2->field_f
    //     0xb4aa38: ldur            w3, [x2, #0xf]
    // 0xb4aa3c: DecompressPointer r3
    //     0xb4aa3c: add             x3, x3, HEAP, lsl #32
    // 0xb4aa40: LoadField: r2 = r3->field_b
    //     0xb4aa40: ldur            w2, [x3, #0xb]
    // 0xb4aa44: DecompressPointer r2
    //     0xb4aa44: add             x2, x2, HEAP, lsl #32
    // 0xb4aa48: cmp             w2, NULL
    // 0xb4aa4c: b.ne            #0xb4aa58
    // 0xb4aa50: r3 = Null
    //     0xb4aa50: mov             x3, NULL
    // 0xb4aa54: b               #0xb4aa60
    // 0xb4aa58: LoadField: r3 = r2->field_b
    //     0xb4aa58: ldur            w3, [x2, #0xb]
    // 0xb4aa5c: DecompressPointer r3
    //     0xb4aa5c: add             x3, x3, HEAP, lsl #32
    // 0xb4aa60: ldur            x2, [fp, #-0x18]
    // 0xb4aa64: StoreField: r0->field_13 = r3
    //     0xb4aa64: stur            w3, [x0, #0x13]
    // 0xb4aa68: str             x0, [SP]
    // 0xb4aa6c: r0 = _interpolate()
    //     0xb4aa6c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb4aa70: ldur            x2, [fp, #-0x18]
    // 0xb4aa74: stur            x0, [fp, #-0x28]
    // 0xb4aa78: LoadField: r1 = r2->field_13
    //     0xb4aa78: ldur            w1, [x2, #0x13]
    // 0xb4aa7c: DecompressPointer r1
    //     0xb4aa7c: add             x1, x1, HEAP, lsl #32
    // 0xb4aa80: r0 = of()
    //     0xb4aa80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4aa84: LoadField: r1 = r0->field_87
    //     0xb4aa84: ldur            w1, [x0, #0x87]
    // 0xb4aa88: DecompressPointer r1
    //     0xb4aa88: add             x1, x1, HEAP, lsl #32
    // 0xb4aa8c: LoadField: r0 = r1->field_7
    //     0xb4aa8c: ldur            w0, [x1, #7]
    // 0xb4aa90: DecompressPointer r0
    //     0xb4aa90: add             x0, x0, HEAP, lsl #32
    // 0xb4aa94: r16 = Instance_Color
    //     0xb4aa94: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4aa98: r30 = 12.000000
    //     0xb4aa98: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4aa9c: ldr             lr, [lr, #0x9e8]
    // 0xb4aaa0: stp             lr, x16, [SP]
    // 0xb4aaa4: mov             x1, x0
    // 0xb4aaa8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb4aaa8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb4aaac: ldr             x4, [x4, #0x9b8]
    // 0xb4aab0: r0 = copyWith()
    //     0xb4aab0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4aab4: stur            x0, [fp, #-0x38]
    // 0xb4aab8: r0 = Text()
    //     0xb4aab8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4aabc: mov             x3, x0
    // 0xb4aac0: ldur            x0, [fp, #-0x28]
    // 0xb4aac4: stur            x3, [fp, #-0x40]
    // 0xb4aac8: StoreField: r3->field_b = r0
    //     0xb4aac8: stur            w0, [x3, #0xb]
    // 0xb4aacc: ldur            x0, [fp, #-0x38]
    // 0xb4aad0: StoreField: r3->field_13 = r0
    //     0xb4aad0: stur            w0, [x3, #0x13]
    // 0xb4aad4: ldur            x2, [fp, #-8]
    // 0xb4aad8: LoadField: r0 = r2->field_b
    //     0xb4aad8: ldur            w0, [x2, #0xb]
    // 0xb4aadc: DecompressPointer r0
    //     0xb4aadc: add             x0, x0, HEAP, lsl #32
    // 0xb4aae0: cmp             w0, NULL
    // 0xb4aae4: b.eq            #0xb4be34
    // 0xb4aae8: LoadField: r1 = r0->field_f
    //     0xb4aae8: ldur            w1, [x0, #0xf]
    // 0xb4aaec: DecompressPointer r1
    //     0xb4aaec: add             x1, x1, HEAP, lsl #32
    // 0xb4aaf0: LoadField: r0 = r1->field_b
    //     0xb4aaf0: ldur            w0, [x1, #0xb]
    // 0xb4aaf4: DecompressPointer r0
    //     0xb4aaf4: add             x0, x0, HEAP, lsl #32
    // 0xb4aaf8: cmp             w0, NULL
    // 0xb4aafc: b.ne            #0xb4ab08
    // 0xb4ab00: r0 = Null
    //     0xb4ab00: mov             x0, NULL
    // 0xb4ab04: b               #0xb4ab18
    // 0xb4ab08: LoadField: r1 = r0->field_13
    //     0xb4ab08: ldur            w1, [x0, #0x13]
    // 0xb4ab0c: DecompressPointer r1
    //     0xb4ab0c: add             x1, x1, HEAP, lsl #32
    // 0xb4ab10: LoadField: r0 = r1->field_7
    //     0xb4ab10: ldur            w0, [x1, #7]
    // 0xb4ab14: DecompressPointer r0
    //     0xb4ab14: add             x0, x0, HEAP, lsl #32
    // 0xb4ab18: cmp             w0, NULL
    // 0xb4ab1c: b.ne            #0xb4ab28
    // 0xb4ab20: r6 = ""
    //     0xb4ab20: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4ab24: b               #0xb4ab2c
    // 0xb4ab28: mov             x6, x0
    // 0xb4ab2c: ldur            x0, [fp, #-0x18]
    // 0xb4ab30: ldur            x4, [fp, #-0x30]
    // 0xb4ab34: r5 = 4
    //     0xb4ab34: movz            x5, #0x4
    // 0xb4ab38: mov             x2, x5
    // 0xb4ab3c: stur            x6, [fp, #-0x28]
    // 0xb4ab40: r1 = Null
    //     0xb4ab40: mov             x1, NULL
    // 0xb4ab44: r0 = AllocateArray()
    //     0xb4ab44: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4ab48: mov             x1, x0
    // 0xb4ab4c: ldur            x0, [fp, #-0x28]
    // 0xb4ab50: StoreField: r1->field_f = r0
    //     0xb4ab50: stur            w0, [x1, #0xf]
    // 0xb4ab54: r16 = " offers available"
    //     0xb4ab54: add             x16, PP, #0x38, lsl #12  ; [pp+0x38ca8] " offers available"
    //     0xb4ab58: ldr             x16, [x16, #0xca8]
    // 0xb4ab5c: StoreField: r1->field_13 = r16
    //     0xb4ab5c: stur            w16, [x1, #0x13]
    // 0xb4ab60: str             x1, [SP]
    // 0xb4ab64: r0 = _interpolate()
    //     0xb4ab64: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb4ab68: ldur            x2, [fp, #-0x18]
    // 0xb4ab6c: stur            x0, [fp, #-0x28]
    // 0xb4ab70: LoadField: r1 = r2->field_13
    //     0xb4ab70: ldur            w1, [x2, #0x13]
    // 0xb4ab74: DecompressPointer r1
    //     0xb4ab74: add             x1, x1, HEAP, lsl #32
    // 0xb4ab78: r0 = of()
    //     0xb4ab78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4ab7c: LoadField: r1 = r0->field_87
    //     0xb4ab7c: ldur            w1, [x0, #0x87]
    // 0xb4ab80: DecompressPointer r1
    //     0xb4ab80: add             x1, x1, HEAP, lsl #32
    // 0xb4ab84: LoadField: r0 = r1->field_2b
    //     0xb4ab84: ldur            w0, [x1, #0x2b]
    // 0xb4ab88: DecompressPointer r0
    //     0xb4ab88: add             x0, x0, HEAP, lsl #32
    // 0xb4ab8c: r16 = Instance_Color
    //     0xb4ab8c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb4ab90: ldr             x16, [x16, #0x858]
    // 0xb4ab94: r30 = 12.000000
    //     0xb4ab94: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4ab98: ldr             lr, [lr, #0x9e8]
    // 0xb4ab9c: stp             lr, x16, [SP]
    // 0xb4aba0: mov             x1, x0
    // 0xb4aba4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb4aba4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb4aba8: ldr             x4, [x4, #0x9b8]
    // 0xb4abac: r0 = copyWith()
    //     0xb4abac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4abb0: stur            x0, [fp, #-0x38]
    // 0xb4abb4: r0 = Text()
    //     0xb4abb4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4abb8: mov             x3, x0
    // 0xb4abbc: ldur            x0, [fp, #-0x28]
    // 0xb4abc0: stur            x3, [fp, #-0x48]
    // 0xb4abc4: StoreField: r3->field_b = r0
    //     0xb4abc4: stur            w0, [x3, #0xb]
    // 0xb4abc8: ldur            x0, [fp, #-0x38]
    // 0xb4abcc: StoreField: r3->field_13 = r0
    //     0xb4abcc: stur            w0, [x3, #0x13]
    // 0xb4abd0: r1 = Null
    //     0xb4abd0: mov             x1, NULL
    // 0xb4abd4: r2 = 4
    //     0xb4abd4: movz            x2, #0x4
    // 0xb4abd8: r0 = AllocateArray()
    //     0xb4abd8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4abdc: mov             x2, x0
    // 0xb4abe0: ldur            x0, [fp, #-0x40]
    // 0xb4abe4: stur            x2, [fp, #-0x28]
    // 0xb4abe8: StoreField: r2->field_f = r0
    //     0xb4abe8: stur            w0, [x2, #0xf]
    // 0xb4abec: ldur            x0, [fp, #-0x48]
    // 0xb4abf0: StoreField: r2->field_13 = r0
    //     0xb4abf0: stur            w0, [x2, #0x13]
    // 0xb4abf4: r1 = <Widget>
    //     0xb4abf4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4abf8: r0 = AllocateGrowableArray()
    //     0xb4abf8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4abfc: mov             x1, x0
    // 0xb4ac00: ldur            x0, [fp, #-0x28]
    // 0xb4ac04: stur            x1, [fp, #-0x38]
    // 0xb4ac08: StoreField: r1->field_f = r0
    //     0xb4ac08: stur            w0, [x1, #0xf]
    // 0xb4ac0c: r3 = 4
    //     0xb4ac0c: movz            x3, #0x4
    // 0xb4ac10: StoreField: r1->field_b = r3
    //     0xb4ac10: stur            w3, [x1, #0xb]
    // 0xb4ac14: r0 = Column()
    //     0xb4ac14: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb4ac18: r4 = Instance_Axis
    //     0xb4ac18: ldr             x4, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb4ac1c: stur            x0, [fp, #-0x28]
    // 0xb4ac20: StoreField: r0->field_f = r4
    //     0xb4ac20: stur            w4, [x0, #0xf]
    // 0xb4ac24: r5 = Instance_MainAxisAlignment
    //     0xb4ac24: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb4ac28: ldr             x5, [x5, #0xab0]
    // 0xb4ac2c: StoreField: r0->field_13 = r5
    //     0xb4ac2c: stur            w5, [x0, #0x13]
    // 0xb4ac30: r2 = Instance_MainAxisSize
    //     0xb4ac30: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4ac34: ldr             x2, [x2, #0xa10]
    // 0xb4ac38: ArrayStore: r0[0] = r2  ; List_4
    //     0xb4ac38: stur            w2, [x0, #0x17]
    // 0xb4ac3c: r6 = Instance_CrossAxisAlignment
    //     0xb4ac3c: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb4ac40: ldr             x6, [x6, #0x890]
    // 0xb4ac44: StoreField: r0->field_1b = r6
    //     0xb4ac44: stur            w6, [x0, #0x1b]
    // 0xb4ac48: r3 = Instance_VerticalDirection
    //     0xb4ac48: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4ac4c: ldr             x3, [x3, #0xa20]
    // 0xb4ac50: StoreField: r0->field_23 = r3
    //     0xb4ac50: stur            w3, [x0, #0x23]
    // 0xb4ac54: r4 = Instance_Clip
    //     0xb4ac54: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4ac58: ldr             x4, [x4, #0x38]
    // 0xb4ac5c: StoreField: r0->field_2b = r4
    //     0xb4ac5c: stur            w4, [x0, #0x2b]
    // 0xb4ac60: StoreField: r0->field_2f = rZR
    //     0xb4ac60: stur            xzr, [x0, #0x2f]
    // 0xb4ac64: ldur            x1, [fp, #-0x38]
    // 0xb4ac68: StoreField: r0->field_b = r1
    //     0xb4ac68: stur            w1, [x0, #0xb]
    // 0xb4ac6c: r1 = <FlexParentData>
    //     0xb4ac6c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb4ac70: ldr             x1, [x1, #0xe00]
    // 0xb4ac74: r0 = Expanded()
    //     0xb4ac74: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb4ac78: stur            x0, [fp, #-0x38]
    // 0xb4ac7c: StoreField: r0->field_13 = rZR
    //     0xb4ac7c: stur            xzr, [x0, #0x13]
    // 0xb4ac80: r7 = Instance_FlexFit
    //     0xb4ac80: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb4ac84: ldr             x7, [x7, #0xe08]
    // 0xb4ac88: StoreField: r0->field_1b = r7
    //     0xb4ac88: stur            w7, [x0, #0x1b]
    // 0xb4ac8c: ldur            x1, [fp, #-0x28]
    // 0xb4ac90: StoreField: r0->field_b = r1
    //     0xb4ac90: stur            w1, [x0, #0xb]
    // 0xb4ac94: ldur            x2, [fp, #-0x18]
    // 0xb4ac98: LoadField: r1 = r2->field_13
    //     0xb4ac98: ldur            w1, [x2, #0x13]
    // 0xb4ac9c: DecompressPointer r1
    //     0xb4ac9c: add             x1, x1, HEAP, lsl #32
    // 0xb4aca0: r0 = of()
    //     0xb4aca0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4aca4: LoadField: r1 = r0->field_87
    //     0xb4aca4: ldur            w1, [x0, #0x87]
    // 0xb4aca8: DecompressPointer r1
    //     0xb4aca8: add             x1, x1, HEAP, lsl #32
    // 0xb4acac: LoadField: r0 = r1->field_7
    //     0xb4acac: ldur            w0, [x1, #7]
    // 0xb4acb0: DecompressPointer r0
    //     0xb4acb0: add             x0, x0, HEAP, lsl #32
    // 0xb4acb4: r16 = 14.000000
    //     0xb4acb4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb4acb8: ldr             x16, [x16, #0x1d8]
    // 0xb4acbc: r30 = Instance_Color
    //     0xb4acbc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb4acc0: ldr             lr, [lr, #0x858]
    // 0xb4acc4: stp             lr, x16, [SP]
    // 0xb4acc8: mov             x1, x0
    // 0xb4accc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4accc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4acd0: ldr             x4, [x4, #0xaa0]
    // 0xb4acd4: r0 = copyWith()
    //     0xb4acd4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4acd8: stur            x0, [fp, #-0x28]
    // 0xb4acdc: r0 = Text()
    //     0xb4acdc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4ace0: mov             x1, x0
    // 0xb4ace4: r0 = "OFFERS"
    //     0xb4ace4: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cb8] "OFFERS"
    //     0xb4ace8: ldr             x0, [x0, #0xcb8]
    // 0xb4acec: stur            x1, [fp, #-0x40]
    // 0xb4acf0: StoreField: r1->field_b = r0
    //     0xb4acf0: stur            w0, [x1, #0xb]
    // 0xb4acf4: ldur            x0, [fp, #-0x28]
    // 0xb4acf8: StoreField: r1->field_13 = r0
    //     0xb4acf8: stur            w0, [x1, #0x13]
    // 0xb4acfc: r0 = InkWell()
    //     0xb4acfc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb4ad00: mov             x3, x0
    // 0xb4ad04: ldur            x0, [fp, #-0x40]
    // 0xb4ad08: stur            x3, [fp, #-0x28]
    // 0xb4ad0c: StoreField: r3->field_b = r0
    //     0xb4ad0c: stur            w0, [x3, #0xb]
    // 0xb4ad10: ldur            x2, [fp, #-0x18]
    // 0xb4ad14: r1 = Function '<anonymous closure>':.
    //     0xb4ad14: add             x1, PP, #0x56, lsl #12  ; [pp+0x56af0] AnonymousClosure: (0xb4c418), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xb4a86c)
    //     0xb4ad18: ldr             x1, [x1, #0xaf0]
    // 0xb4ad1c: r0 = AllocateClosure()
    //     0xb4ad1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4ad20: mov             x1, x0
    // 0xb4ad24: ldur            x0, [fp, #-0x28]
    // 0xb4ad28: StoreField: r0->field_f = r1
    //     0xb4ad28: stur            w1, [x0, #0xf]
    // 0xb4ad2c: r1 = true
    //     0xb4ad2c: add             x1, NULL, #0x20  ; true
    // 0xb4ad30: StoreField: r0->field_43 = r1
    //     0xb4ad30: stur            w1, [x0, #0x43]
    // 0xb4ad34: r2 = Instance_BoxShape
    //     0xb4ad34: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb4ad38: ldr             x2, [x2, #0x80]
    // 0xb4ad3c: StoreField: r0->field_47 = r2
    //     0xb4ad3c: stur            w2, [x0, #0x47]
    // 0xb4ad40: StoreField: r0->field_6f = r1
    //     0xb4ad40: stur            w1, [x0, #0x6f]
    // 0xb4ad44: r3 = false
    //     0xb4ad44: add             x3, NULL, #0x30  ; false
    // 0xb4ad48: StoreField: r0->field_73 = r3
    //     0xb4ad48: stur            w3, [x0, #0x73]
    // 0xb4ad4c: StoreField: r0->field_83 = r1
    //     0xb4ad4c: stur            w1, [x0, #0x83]
    // 0xb4ad50: StoreField: r0->field_7b = r3
    //     0xb4ad50: stur            w3, [x0, #0x7b]
    // 0xb4ad54: r0 = Padding()
    //     0xb4ad54: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4ad58: mov             x3, x0
    // 0xb4ad5c: r0 = Instance_EdgeInsets
    //     0xb4ad5c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb4ad60: ldr             x0, [x0, #0x1f0]
    // 0xb4ad64: stur            x3, [fp, #-0x40]
    // 0xb4ad68: StoreField: r3->field_f = r0
    //     0xb4ad68: stur            w0, [x3, #0xf]
    // 0xb4ad6c: ldur            x0, [fp, #-0x28]
    // 0xb4ad70: StoreField: r3->field_b = r0
    //     0xb4ad70: stur            w0, [x3, #0xb]
    // 0xb4ad74: r1 = Null
    //     0xb4ad74: mov             x1, NULL
    // 0xb4ad78: r2 = 8
    //     0xb4ad78: movz            x2, #0x8
    // 0xb4ad7c: r0 = AllocateArray()
    //     0xb4ad7c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4ad80: mov             x2, x0
    // 0xb4ad84: ldur            x0, [fp, #-0x30]
    // 0xb4ad88: stur            x2, [fp, #-0x28]
    // 0xb4ad8c: StoreField: r2->field_f = r0
    //     0xb4ad8c: stur            w0, [x2, #0xf]
    // 0xb4ad90: ldur            x0, [fp, #-0x38]
    // 0xb4ad94: StoreField: r2->field_13 = r0
    //     0xb4ad94: stur            w0, [x2, #0x13]
    // 0xb4ad98: r16 = Instance_Spacer
    //     0xb4ad98: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb4ad9c: ldr             x16, [x16, #0xf0]
    // 0xb4ada0: ArrayStore: r2[0] = r16  ; List_4
    //     0xb4ada0: stur            w16, [x2, #0x17]
    // 0xb4ada4: ldur            x0, [fp, #-0x40]
    // 0xb4ada8: StoreField: r2->field_1b = r0
    //     0xb4ada8: stur            w0, [x2, #0x1b]
    // 0xb4adac: r1 = <Widget>
    //     0xb4adac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4adb0: r0 = AllocateGrowableArray()
    //     0xb4adb0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4adb4: mov             x1, x0
    // 0xb4adb8: ldur            x0, [fp, #-0x28]
    // 0xb4adbc: stur            x1, [fp, #-0x30]
    // 0xb4adc0: StoreField: r1->field_f = r0
    //     0xb4adc0: stur            w0, [x1, #0xf]
    // 0xb4adc4: r0 = 8
    //     0xb4adc4: movz            x0, #0x8
    // 0xb4adc8: StoreField: r1->field_b = r0
    //     0xb4adc8: stur            w0, [x1, #0xb]
    // 0xb4adcc: r0 = Row()
    //     0xb4adcc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb4add0: r8 = Instance_Axis
    //     0xb4add0: ldr             x8, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4add4: stur            x0, [fp, #-0x28]
    // 0xb4add8: StoreField: r0->field_f = r8
    //     0xb4add8: stur            w8, [x0, #0xf]
    // 0xb4addc: r9 = Instance_MainAxisAlignment
    //     0xb4addc: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb4ade0: ldr             x9, [x9, #0xd10]
    // 0xb4ade4: StoreField: r0->field_13 = r9
    //     0xb4ade4: stur            w9, [x0, #0x13]
    // 0xb4ade8: r10 = Instance_MainAxisSize
    //     0xb4ade8: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4adec: ldr             x10, [x10, #0xa10]
    // 0xb4adf0: ArrayStore: r0[0] = r10  ; List_4
    //     0xb4adf0: stur            w10, [x0, #0x17]
    // 0xb4adf4: r11 = Instance_CrossAxisAlignment
    //     0xb4adf4: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4adf8: ldr             x11, [x11, #0xa18]
    // 0xb4adfc: StoreField: r0->field_1b = r11
    //     0xb4adfc: stur            w11, [x0, #0x1b]
    // 0xb4ae00: r12 = Instance_VerticalDirection
    //     0xb4ae00: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4ae04: ldr             x12, [x12, #0xa20]
    // 0xb4ae08: StoreField: r0->field_23 = r12
    //     0xb4ae08: stur            w12, [x0, #0x23]
    // 0xb4ae0c: r13 = Instance_Clip
    //     0xb4ae0c: add             x13, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4ae10: ldr             x13, [x13, #0x38]
    // 0xb4ae14: StoreField: r0->field_2b = r13
    //     0xb4ae14: stur            w13, [x0, #0x2b]
    // 0xb4ae18: StoreField: r0->field_2f = rZR
    //     0xb4ae18: stur            xzr, [x0, #0x2f]
    // 0xb4ae1c: ldur            x1, [fp, #-0x30]
    // 0xb4ae20: StoreField: r0->field_b = r1
    //     0xb4ae20: stur            w1, [x0, #0xb]
    // 0xb4ae24: r0 = Container()
    //     0xb4ae24: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb4ae28: stur            x0, [fp, #-0x30]
    // 0xb4ae2c: ldur            x16, [fp, #-0x20]
    // 0xb4ae30: ldur            lr, [fp, #-0x28]
    // 0xb4ae34: stp             lr, x16, [SP]
    // 0xb4ae38: mov             x1, x0
    // 0xb4ae3c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb4ae3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb4ae40: ldr             x4, [x4, #0x88]
    // 0xb4ae44: r0 = Container()
    //     0xb4ae44: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb4ae48: r0 = Padding()
    //     0xb4ae48: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4ae4c: r14 = Instance_EdgeInsets
    //     0xb4ae4c: add             x14, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb4ae50: ldr             x14, [x14, #0x878]
    // 0xb4ae54: stur            x0, [fp, #-0x20]
    // 0xb4ae58: StoreField: r0->field_f = r14
    //     0xb4ae58: stur            w14, [x0, #0xf]
    // 0xb4ae5c: ldur            x1, [fp, #-0x30]
    // 0xb4ae60: StoreField: r0->field_b = r1
    //     0xb4ae60: stur            w1, [x0, #0xb]
    // 0xb4ae64: r0 = SizedBox()
    //     0xb4ae64: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb4ae68: mov             x1, x0
    // 0xb4ae6c: r0 = 100.000000
    //     0xb4ae6c: ldr             x0, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xb4ae70: stur            x1, [fp, #-0x28]
    // 0xb4ae74: StoreField: r1->field_13 = r0
    //     0xb4ae74: stur            w0, [x1, #0x13]
    // 0xb4ae78: ldur            x0, [fp, #-0x20]
    // 0xb4ae7c: StoreField: r1->field_b = r0
    //     0xb4ae7c: stur            w0, [x1, #0xb]
    // 0xb4ae80: r0 = InkWell()
    //     0xb4ae80: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb4ae84: mov             x3, x0
    // 0xb4ae88: ldur            x0, [fp, #-0x28]
    // 0xb4ae8c: stur            x3, [fp, #-0x20]
    // 0xb4ae90: StoreField: r3->field_b = r0
    //     0xb4ae90: stur            w0, [x3, #0xb]
    // 0xb4ae94: ldur            x2, [fp, #-0x18]
    // 0xb4ae98: r1 = Function '<anonymous closure>':.
    //     0xb4ae98: add             x1, PP, #0x56, lsl #12  ; [pp+0x56af8] AnonymousClosure: (0xb4c39c), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xb4a86c)
    //     0xb4ae9c: ldr             x1, [x1, #0xaf8]
    // 0xb4aea0: r0 = AllocateClosure()
    //     0xb4aea0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4aea4: mov             x1, x0
    // 0xb4aea8: ldur            x0, [fp, #-0x20]
    // 0xb4aeac: StoreField: r0->field_f = r1
    //     0xb4aeac: stur            w1, [x0, #0xf]
    // 0xb4aeb0: r19 = true
    //     0xb4aeb0: add             x19, NULL, #0x20  ; true
    // 0xb4aeb4: StoreField: r0->field_43 = r19
    //     0xb4aeb4: stur            w19, [x0, #0x43]
    // 0xb4aeb8: r20 = Instance_BoxShape
    //     0xb4aeb8: add             x20, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb4aebc: ldr             x20, [x20, #0x80]
    // 0xb4aec0: StoreField: r0->field_47 = r20
    //     0xb4aec0: stur            w20, [x0, #0x47]
    // 0xb4aec4: StoreField: r0->field_6f = r19
    //     0xb4aec4: stur            w19, [x0, #0x6f]
    // 0xb4aec8: r23 = false
    //     0xb4aec8: add             x23, NULL, #0x30  ; false
    // 0xb4aecc: StoreField: r0->field_73 = r23
    //     0xb4aecc: stur            w23, [x0, #0x73]
    // 0xb4aed0: StoreField: r0->field_83 = r19
    //     0xb4aed0: stur            w19, [x0, #0x83]
    // 0xb4aed4: StoreField: r0->field_7b = r23
    //     0xb4aed4: stur            w23, [x0, #0x7b]
    // 0xb4aed8: mov             x1, x0
    // 0xb4aedc: mov             x2, x23
    // 0xb4aee0: b               #0xb4bdd8
    // 0xb4aee4: ldur            x2, [fp, #-8]
    // 0xb4aee8: r19 = true
    //     0xb4aee8: add             x19, NULL, #0x20  ; true
    // 0xb4aeec: r1 = Instance_Color
    //     0xb4aeec: add             x1, PP, #0x40, lsl #12  ; [pp+0x40860] Obj!Color@d6b0a1
    //     0xb4aef0: ldr             x1, [x1, #0x860]
    // 0xb4aef4: r14 = Instance_EdgeInsets
    //     0xb4aef4: add             x14, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb4aef8: ldr             x14, [x14, #0x878]
    // 0xb4aefc: r3 = 4
    //     0xb4aefc: movz            x3, #0x4
    // 0xb4af00: r5 = Instance_MainAxisAlignment
    //     0xb4af00: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb4af04: ldr             x5, [x5, #0xab0]
    // 0xb4af08: r6 = Instance_CrossAxisAlignment
    //     0xb4af08: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb4af0c: ldr             x6, [x6, #0x890]
    // 0xb4af10: r9 = Instance_MainAxisAlignment
    //     0xb4af10: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb4af14: ldr             x9, [x9, #0xd10]
    // 0xb4af18: r23 = false
    //     0xb4af18: add             x23, NULL, #0x30  ; false
    // 0xb4af1c: r20 = Instance_BoxShape
    //     0xb4af1c: add             x20, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb4af20: ldr             x20, [x20, #0x80]
    // 0xb4af24: r10 = Instance_MainAxisSize
    //     0xb4af24: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4af28: ldr             x10, [x10, #0xa10]
    // 0xb4af2c: r12 = Instance_VerticalDirection
    //     0xb4af2c: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4af30: ldr             x12, [x12, #0xa20]
    // 0xb4af34: r4 = Instance_Axis
    //     0xb4af34: ldr             x4, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb4af38: r7 = Instance_FlexFit
    //     0xb4af38: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb4af3c: ldr             x7, [x7, #0xe08]
    // 0xb4af40: r11 = Instance_CrossAxisAlignment
    //     0xb4af40: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4af44: ldr             x11, [x11, #0xa18]
    // 0xb4af48: r8 = Instance_Axis
    //     0xb4af48: ldr             x8, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4af4c: r13 = Instance_Clip
    //     0xb4af4c: add             x13, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4af50: ldr             x13, [x13, #0x38]
    // 0xb4af54: LoadField: r0 = r2->field_b
    //     0xb4af54: ldur            w0, [x2, #0xb]
    // 0xb4af58: DecompressPointer r0
    //     0xb4af58: add             x0, x0, HEAP, lsl #32
    // 0xb4af5c: cmp             w0, NULL
    // 0xb4af60: b.eq            #0xb4be38
    // 0xb4af64: LoadField: r24 = r0->field_27
    //     0xb4af64: ldur            w24, [x0, #0x27]
    // 0xb4af68: DecompressPointer r24
    //     0xb4af68: add             x24, x24, HEAP, lsl #32
    // 0xb4af6c: r0 = LoadClassIdInstr(r24)
    //     0xb4af6c: ldur            x0, [x24, #-1]
    //     0xb4af70: ubfx            x0, x0, #0xc, #0x14
    // 0xb4af74: r16 = "bumper_coupon"
    //     0xb4af74: add             x16, PP, #0x12, lsl #12  ; [pp+0x128d8] "bumper_coupon"
    //     0xb4af78: ldr             x16, [x16, #0x8d8]
    // 0xb4af7c: stp             x16, x24, [SP]
    // 0xb4af80: mov             lr, x0
    // 0xb4af84: ldr             lr, [x21, lr, lsl #3]
    // 0xb4af88: blr             lr
    // 0xb4af8c: tbnz            w0, #4, #0xb4b768
    // 0xb4af90: ldur            x0, [fp, #-8]
    // 0xb4af94: LoadField: r1 = r0->field_b
    //     0xb4af94: ldur            w1, [x0, #0xb]
    // 0xb4af98: DecompressPointer r1
    //     0xb4af98: add             x1, x1, HEAP, lsl #32
    // 0xb4af9c: cmp             w1, NULL
    // 0xb4afa0: b.eq            #0xb4be3c
    // 0xb4afa4: LoadField: r2 = r1->field_b
    //     0xb4afa4: ldur            w2, [x1, #0xb]
    // 0xb4afa8: DecompressPointer r2
    //     0xb4afa8: add             x2, x2, HEAP, lsl #32
    // 0xb4afac: LoadField: r1 = r2->field_13
    //     0xb4afac: ldur            w1, [x2, #0x13]
    // 0xb4afb0: DecompressPointer r1
    //     0xb4afb0: add             x1, x1, HEAP, lsl #32
    // 0xb4afb4: stur            x1, [fp, #-0x20]
    // 0xb4afb8: cmp             w1, NULL
    // 0xb4afbc: b.ne            #0xb4afc8
    // 0xb4afc0: r2 = Null
    //     0xb4afc0: mov             x2, NULL
    // 0xb4afc4: b               #0xb4afd0
    // 0xb4afc8: LoadField: r2 = r1->field_7
    //     0xb4afc8: ldur            w2, [x1, #7]
    // 0xb4afcc: DecompressPointer r2
    //     0xb4afcc: add             x2, x2, HEAP, lsl #32
    // 0xb4afd0: cmp             w2, NULL
    // 0xb4afd4: b.ne            #0xb4afe0
    // 0xb4afd8: r2 = 0
    //     0xb4afd8: movz            x2, #0
    // 0xb4afdc: b               #0xb4aff0
    // 0xb4afe0: r3 = LoadInt32Instr(r2)
    //     0xb4afe0: sbfx            x3, x2, #1, #0x1f
    //     0xb4afe4: tbz             w2, #0, #0xb4afec
    //     0xb4afe8: ldur            x3, [x2, #7]
    // 0xb4afec: mov             x2, x3
    // 0xb4aff0: stur            x2, [fp, #-0x60]
    // 0xb4aff4: cmp             w1, NULL
    // 0xb4aff8: b.ne            #0xb4b004
    // 0xb4affc: r3 = Null
    //     0xb4affc: mov             x3, NULL
    // 0xb4b000: b               #0xb4b00c
    // 0xb4b004: LoadField: r3 = r1->field_b
    //     0xb4b004: ldur            w3, [x1, #0xb]
    // 0xb4b008: DecompressPointer r3
    //     0xb4b008: add             x3, x3, HEAP, lsl #32
    // 0xb4b00c: cmp             w3, NULL
    // 0xb4b010: b.ne            #0xb4b01c
    // 0xb4b014: r3 = 0
    //     0xb4b014: movz            x3, #0
    // 0xb4b018: b               #0xb4b02c
    // 0xb4b01c: r4 = LoadInt32Instr(r3)
    //     0xb4b01c: sbfx            x4, x3, #1, #0x1f
    //     0xb4b020: tbz             w3, #0, #0xb4b028
    //     0xb4b024: ldur            x4, [x3, #7]
    // 0xb4b028: mov             x3, x4
    // 0xb4b02c: stur            x3, [fp, #-0x58]
    // 0xb4b030: cmp             w1, NULL
    // 0xb4b034: b.ne            #0xb4b040
    // 0xb4b038: r4 = Null
    //     0xb4b038: mov             x4, NULL
    // 0xb4b03c: b               #0xb4b048
    // 0xb4b040: LoadField: r4 = r1->field_f
    //     0xb4b040: ldur            w4, [x1, #0xf]
    // 0xb4b044: DecompressPointer r4
    //     0xb4b044: add             x4, x4, HEAP, lsl #32
    // 0xb4b048: cmp             w4, NULL
    // 0xb4b04c: b.ne            #0xb4b058
    // 0xb4b050: r4 = 0
    //     0xb4b050: movz            x4, #0
    // 0xb4b054: b               #0xb4b068
    // 0xb4b058: r5 = LoadInt32Instr(r4)
    //     0xb4b058: sbfx            x5, x4, #1, #0x1f
    //     0xb4b05c: tbz             w4, #0, #0xb4b064
    //     0xb4b060: ldur            x5, [x4, #7]
    // 0xb4b064: mov             x4, x5
    // 0xb4b068: stur            x4, [fp, #-0x50]
    // 0xb4b06c: r0 = Color()
    //     0xb4b06c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb4b070: mov             x1, x0
    // 0xb4b074: r0 = Instance_ColorSpace
    //     0xb4b074: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb4b078: stur            x1, [fp, #-0x28]
    // 0xb4b07c: StoreField: r1->field_27 = r0
    //     0xb4b07c: stur            w0, [x1, #0x27]
    // 0xb4b080: d0 = 1.000000
    //     0xb4b080: fmov            d0, #1.00000000
    // 0xb4b084: StoreField: r1->field_7 = d0
    //     0xb4b084: stur            d0, [x1, #7]
    // 0xb4b088: ldur            x2, [fp, #-0x60]
    // 0xb4b08c: ubfx            x2, x2, #0, #0x20
    // 0xb4b090: and             w3, w2, #0xff
    // 0xb4b094: ubfx            x3, x3, #0, #0x20
    // 0xb4b098: scvtf           d0, x3
    // 0xb4b09c: d1 = 255.000000
    //     0xb4b09c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb4b0a0: fdiv            d2, d0, d1
    // 0xb4b0a4: StoreField: r1->field_f = d2
    //     0xb4b0a4: stur            d2, [x1, #0xf]
    // 0xb4b0a8: ldur            x2, [fp, #-0x58]
    // 0xb4b0ac: ubfx            x2, x2, #0, #0x20
    // 0xb4b0b0: and             w3, w2, #0xff
    // 0xb4b0b4: ubfx            x3, x3, #0, #0x20
    // 0xb4b0b8: scvtf           d0, x3
    // 0xb4b0bc: fdiv            d2, d0, d1
    // 0xb4b0c0: ArrayStore: r1[0] = d2  ; List_8
    //     0xb4b0c0: stur            d2, [x1, #0x17]
    // 0xb4b0c4: ldur            x2, [fp, #-0x50]
    // 0xb4b0c8: ubfx            x2, x2, #0, #0x20
    // 0xb4b0cc: and             w3, w2, #0xff
    // 0xb4b0d0: ubfx            x3, x3, #0, #0x20
    // 0xb4b0d4: scvtf           d0, x3
    // 0xb4b0d8: fdiv            d2, d0, d1
    // 0xb4b0dc: StoreField: r1->field_1f = d2
    //     0xb4b0dc: stur            d2, [x1, #0x1f]
    // 0xb4b0e0: ldur            x2, [fp, #-0x20]
    // 0xb4b0e4: cmp             w2, NULL
    // 0xb4b0e8: b.ne            #0xb4b0f4
    // 0xb4b0ec: r3 = Null
    //     0xb4b0ec: mov             x3, NULL
    // 0xb4b0f0: b               #0xb4b0fc
    // 0xb4b0f4: LoadField: r3 = r2->field_7
    //     0xb4b0f4: ldur            w3, [x2, #7]
    // 0xb4b0f8: DecompressPointer r3
    //     0xb4b0f8: add             x3, x3, HEAP, lsl #32
    // 0xb4b0fc: cmp             w3, NULL
    // 0xb4b100: b.ne            #0xb4b10c
    // 0xb4b104: r3 = 0
    //     0xb4b104: movz            x3, #0
    // 0xb4b108: b               #0xb4b11c
    // 0xb4b10c: r4 = LoadInt32Instr(r3)
    //     0xb4b10c: sbfx            x4, x3, #1, #0x1f
    //     0xb4b110: tbz             w3, #0, #0xb4b118
    //     0xb4b114: ldur            x4, [x3, #7]
    // 0xb4b118: mov             x3, x4
    // 0xb4b11c: stur            x3, [fp, #-0x60]
    // 0xb4b120: cmp             w2, NULL
    // 0xb4b124: b.ne            #0xb4b130
    // 0xb4b128: r4 = Null
    //     0xb4b128: mov             x4, NULL
    // 0xb4b12c: b               #0xb4b138
    // 0xb4b130: LoadField: r4 = r2->field_b
    //     0xb4b130: ldur            w4, [x2, #0xb]
    // 0xb4b134: DecompressPointer r4
    //     0xb4b134: add             x4, x4, HEAP, lsl #32
    // 0xb4b138: cmp             w4, NULL
    // 0xb4b13c: b.ne            #0xb4b148
    // 0xb4b140: r4 = 0
    //     0xb4b140: movz            x4, #0
    // 0xb4b144: b               #0xb4b158
    // 0xb4b148: r5 = LoadInt32Instr(r4)
    //     0xb4b148: sbfx            x5, x4, #1, #0x1f
    //     0xb4b14c: tbz             w4, #0, #0xb4b154
    //     0xb4b150: ldur            x5, [x4, #7]
    // 0xb4b154: mov             x4, x5
    // 0xb4b158: stur            x4, [fp, #-0x58]
    // 0xb4b15c: cmp             w2, NULL
    // 0xb4b160: b.ne            #0xb4b16c
    // 0xb4b164: r2 = Null
    //     0xb4b164: mov             x2, NULL
    // 0xb4b168: b               #0xb4b178
    // 0xb4b16c: LoadField: r5 = r2->field_f
    //     0xb4b16c: ldur            w5, [x2, #0xf]
    // 0xb4b170: DecompressPointer r5
    //     0xb4b170: add             x5, x5, HEAP, lsl #32
    // 0xb4b174: mov             x2, x5
    // 0xb4b178: cmp             w2, NULL
    // 0xb4b17c: b.ne            #0xb4b188
    // 0xb4b180: r5 = 0
    //     0xb4b180: movz            x5, #0
    // 0xb4b184: b               #0xb4b194
    // 0xb4b188: r5 = LoadInt32Instr(r2)
    //     0xb4b188: sbfx            x5, x2, #1, #0x1f
    //     0xb4b18c: tbz             w2, #0, #0xb4b194
    //     0xb4b190: ldur            x5, [x2, #7]
    // 0xb4b194: ldur            x2, [fp, #-8]
    // 0xb4b198: stur            x5, [fp, #-0x50]
    // 0xb4b19c: r0 = Color()
    //     0xb4b19c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb4b1a0: mov             x3, x0
    // 0xb4b1a4: r0 = Instance_ColorSpace
    //     0xb4b1a4: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb4b1a8: stur            x3, [fp, #-0x20]
    // 0xb4b1ac: StoreField: r3->field_27 = r0
    //     0xb4b1ac: stur            w0, [x3, #0x27]
    // 0xb4b1b0: d0 = 0.700000
    //     0xb4b1b0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb4b1b4: ldr             d0, [x17, #0xf48]
    // 0xb4b1b8: StoreField: r3->field_7 = d0
    //     0xb4b1b8: stur            d0, [x3, #7]
    // 0xb4b1bc: ldur            x0, [fp, #-0x60]
    // 0xb4b1c0: ubfx            x0, x0, #0, #0x20
    // 0xb4b1c4: and             w1, w0, #0xff
    // 0xb4b1c8: ubfx            x1, x1, #0, #0x20
    // 0xb4b1cc: scvtf           d0, x1
    // 0xb4b1d0: d1 = 255.000000
    //     0xb4b1d0: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb4b1d4: fdiv            d2, d0, d1
    // 0xb4b1d8: StoreField: r3->field_f = d2
    //     0xb4b1d8: stur            d2, [x3, #0xf]
    // 0xb4b1dc: ldur            x0, [fp, #-0x58]
    // 0xb4b1e0: ubfx            x0, x0, #0, #0x20
    // 0xb4b1e4: and             w1, w0, #0xff
    // 0xb4b1e8: ubfx            x1, x1, #0, #0x20
    // 0xb4b1ec: scvtf           d0, x1
    // 0xb4b1f0: fdiv            d2, d0, d1
    // 0xb4b1f4: ArrayStore: r3[0] = d2  ; List_8
    //     0xb4b1f4: stur            d2, [x3, #0x17]
    // 0xb4b1f8: ldur            x0, [fp, #-0x50]
    // 0xb4b1fc: ubfx            x0, x0, #0, #0x20
    // 0xb4b200: and             w1, w0, #0xff
    // 0xb4b204: ubfx            x1, x1, #0, #0x20
    // 0xb4b208: scvtf           d0, x1
    // 0xb4b20c: fdiv            d2, d0, d1
    // 0xb4b210: StoreField: r3->field_1f = d2
    //     0xb4b210: stur            d2, [x3, #0x1f]
    // 0xb4b214: r1 = Null
    //     0xb4b214: mov             x1, NULL
    // 0xb4b218: r2 = 4
    //     0xb4b218: movz            x2, #0x4
    // 0xb4b21c: r0 = AllocateArray()
    //     0xb4b21c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4b220: mov             x2, x0
    // 0xb4b224: ldur            x0, [fp, #-0x28]
    // 0xb4b228: stur            x2, [fp, #-0x30]
    // 0xb4b22c: StoreField: r2->field_f = r0
    //     0xb4b22c: stur            w0, [x2, #0xf]
    // 0xb4b230: ldur            x0, [fp, #-0x20]
    // 0xb4b234: StoreField: r2->field_13 = r0
    //     0xb4b234: stur            w0, [x2, #0x13]
    // 0xb4b238: r1 = <Color>
    //     0xb4b238: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb4b23c: ldr             x1, [x1, #0xf80]
    // 0xb4b240: r0 = AllocateGrowableArray()
    //     0xb4b240: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4b244: mov             x1, x0
    // 0xb4b248: ldur            x0, [fp, #-0x30]
    // 0xb4b24c: stur            x1, [fp, #-0x20]
    // 0xb4b250: StoreField: r1->field_f = r0
    //     0xb4b250: stur            w0, [x1, #0xf]
    // 0xb4b254: r2 = 4
    //     0xb4b254: movz            x2, #0x4
    // 0xb4b258: StoreField: r1->field_b = r2
    //     0xb4b258: stur            w2, [x1, #0xb]
    // 0xb4b25c: r0 = LinearGradient()
    //     0xb4b25c: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xb4b260: mov             x1, x0
    // 0xb4b264: r0 = Instance_Alignment
    //     0xb4b264: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb4b268: ldr             x0, [x0, #0xce0]
    // 0xb4b26c: stur            x1, [fp, #-0x28]
    // 0xb4b270: StoreField: r1->field_13 = r0
    //     0xb4b270: stur            w0, [x1, #0x13]
    // 0xb4b274: r0 = Instance_Alignment
    //     0xb4b274: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xb4b278: ldr             x0, [x0, #0xce8]
    // 0xb4b27c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb4b27c: stur            w0, [x1, #0x17]
    // 0xb4b280: r0 = Instance_TileMode
    //     0xb4b280: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xb4b284: ldr             x0, [x0, #0xcf0]
    // 0xb4b288: StoreField: r1->field_1b = r0
    //     0xb4b288: stur            w0, [x1, #0x1b]
    // 0xb4b28c: ldur            x0, [fp, #-0x20]
    // 0xb4b290: StoreField: r1->field_7 = r0
    //     0xb4b290: stur            w0, [x1, #7]
    // 0xb4b294: r0 = BoxDecoration()
    //     0xb4b294: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb4b298: mov             x1, x0
    // 0xb4b29c: r0 = Instance_BorderRadius
    //     0xb4b29c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e10] Obj!BorderRadius@d5a1c1
    //     0xb4b2a0: ldr             x0, [x0, #0xe10]
    // 0xb4b2a4: stur            x1, [fp, #-0x20]
    // 0xb4b2a8: StoreField: r1->field_13 = r0
    //     0xb4b2a8: stur            w0, [x1, #0x13]
    // 0xb4b2ac: ldur            x0, [fp, #-0x28]
    // 0xb4b2b0: StoreField: r1->field_1b = r0
    //     0xb4b2b0: stur            w0, [x1, #0x1b]
    // 0xb4b2b4: r0 = Instance_BoxShape
    //     0xb4b2b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb4b2b8: ldr             x0, [x0, #0x80]
    // 0xb4b2bc: StoreField: r1->field_23 = r0
    //     0xb4b2bc: stur            w0, [x1, #0x23]
    // 0xb4b2c0: r0 = SvgPicture()
    //     0xb4b2c0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb4b2c4: stur            x0, [fp, #-0x28]
    // 0xb4b2c8: r16 = Instance_BoxFit
    //     0xb4b2c8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb4b2cc: ldr             x16, [x16, #0xb18]
    // 0xb4b2d0: r30 = 40.000000
    //     0xb4b2d0: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb4b2d4: ldr             lr, [lr, #8]
    // 0xb4b2d8: stp             lr, x16, [SP, #8]
    // 0xb4b2dc: r16 = 40.000000
    //     0xb4b2dc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb4b2e0: ldr             x16, [x16, #8]
    // 0xb4b2e4: str             x16, [SP]
    // 0xb4b2e8: mov             x1, x0
    // 0xb4b2ec: r2 = "assets/images/offer_icon.svg"
    //     0xb4b2ec: add             x2, PP, #0x43, lsl #12  ; [pp+0x43758] "assets/images/offer_icon.svg"
    //     0xb4b2f0: ldr             x2, [x2, #0x758]
    // 0xb4b2f4: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0xb4b2f4: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d8e0] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0xb4b2f8: ldr             x4, [x4, #0x8e0]
    // 0xb4b2fc: r0 = SvgPicture.asset()
    //     0xb4b2fc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb4b300: r0 = Padding()
    //     0xb4b300: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4b304: mov             x3, x0
    // 0xb4b308: r0 = Instance_EdgeInsets
    //     0xb4b308: add             x0, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb4b30c: ldr             x0, [x0, #0x878]
    // 0xb4b310: stur            x3, [fp, #-0x30]
    // 0xb4b314: StoreField: r3->field_f = r0
    //     0xb4b314: stur            w0, [x3, #0xf]
    // 0xb4b318: ldur            x1, [fp, #-0x28]
    // 0xb4b31c: StoreField: r3->field_b = r1
    //     0xb4b31c: stur            w1, [x3, #0xb]
    // 0xb4b320: r1 = Null
    //     0xb4b320: mov             x1, NULL
    // 0xb4b324: r2 = 6
    //     0xb4b324: movz            x2, #0x6
    // 0xb4b328: r0 = AllocateArray()
    //     0xb4b328: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4b32c: r16 = "You\'ve saved "
    //     0xb4b32c: add             x16, PP, #0x38, lsl #12  ; [pp+0x38cf8] "You\'ve saved "
    //     0xb4b330: ldr             x16, [x16, #0xcf8]
    // 0xb4b334: StoreField: r0->field_f = r16
    //     0xb4b334: stur            w16, [x0, #0xf]
    // 0xb4b338: ldur            x1, [fp, #-8]
    // 0xb4b33c: LoadField: r2 = r1->field_b
    //     0xb4b33c: ldur            w2, [x1, #0xb]
    // 0xb4b340: DecompressPointer r2
    //     0xb4b340: add             x2, x2, HEAP, lsl #32
    // 0xb4b344: cmp             w2, NULL
    // 0xb4b348: b.eq            #0xb4be40
    // 0xb4b34c: LoadField: r1 = r2->field_23
    //     0xb4b34c: ldur            w1, [x2, #0x23]
    // 0xb4b350: DecompressPointer r1
    //     0xb4b350: add             x1, x1, HEAP, lsl #32
    // 0xb4b354: LoadField: r2 = r1->field_b
    //     0xb4b354: ldur            w2, [x1, #0xb]
    // 0xb4b358: DecompressPointer r2
    //     0xb4b358: add             x2, x2, HEAP, lsl #32
    // 0xb4b35c: cmp             w2, NULL
    // 0xb4b360: b.ne            #0xb4b36c
    // 0xb4b364: r1 = Null
    //     0xb4b364: mov             x1, NULL
    // 0xb4b368: b               #0xb4b390
    // 0xb4b36c: LoadField: r1 = r2->field_33
    //     0xb4b36c: ldur            w1, [x2, #0x33]
    // 0xb4b370: DecompressPointer r1
    //     0xb4b370: add             x1, x1, HEAP, lsl #32
    // 0xb4b374: cmp             w1, NULL
    // 0xb4b378: b.ne            #0xb4b384
    // 0xb4b37c: r1 = Null
    //     0xb4b37c: mov             x1, NULL
    // 0xb4b380: b               #0xb4b390
    // 0xb4b384: LoadField: r2 = r1->field_f
    //     0xb4b384: ldur            w2, [x1, #0xf]
    // 0xb4b388: DecompressPointer r2
    //     0xb4b388: add             x2, x2, HEAP, lsl #32
    // 0xb4b38c: mov             x1, x2
    // 0xb4b390: cmp             w1, NULL
    // 0xb4b394: b.ne            #0xb4b3a0
    // 0xb4b398: r3 = ""
    //     0xb4b398: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4b39c: b               #0xb4b3a4
    // 0xb4b3a0: mov             x3, x1
    // 0xb4b3a4: ldur            x2, [fp, #-0x18]
    // 0xb4b3a8: ldur            x1, [fp, #-0x30]
    // 0xb4b3ac: StoreField: r0->field_13 = r3
    //     0xb4b3ac: stur            w3, [x0, #0x13]
    // 0xb4b3b0: r16 = "!"
    //     0xb4b3b0: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d00] "!"
    //     0xb4b3b4: ldr             x16, [x16, #0xd00]
    // 0xb4b3b8: ArrayStore: r0[0] = r16  ; List_4
    //     0xb4b3b8: stur            w16, [x0, #0x17]
    // 0xb4b3bc: str             x0, [SP]
    // 0xb4b3c0: r0 = _interpolate()
    //     0xb4b3c0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb4b3c4: ldur            x2, [fp, #-0x18]
    // 0xb4b3c8: stur            x0, [fp, #-0x28]
    // 0xb4b3cc: LoadField: r1 = r2->field_13
    //     0xb4b3cc: ldur            w1, [x2, #0x13]
    // 0xb4b3d0: DecompressPointer r1
    //     0xb4b3d0: add             x1, x1, HEAP, lsl #32
    // 0xb4b3d4: r0 = of()
    //     0xb4b3d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4b3d8: LoadField: r1 = r0->field_87
    //     0xb4b3d8: ldur            w1, [x0, #0x87]
    // 0xb4b3dc: DecompressPointer r1
    //     0xb4b3dc: add             x1, x1, HEAP, lsl #32
    // 0xb4b3e0: LoadField: r0 = r1->field_7
    //     0xb4b3e0: ldur            w0, [x1, #7]
    // 0xb4b3e4: DecompressPointer r0
    //     0xb4b3e4: add             x0, x0, HEAP, lsl #32
    // 0xb4b3e8: r16 = 16.000000
    //     0xb4b3e8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb4b3ec: ldr             x16, [x16, #0x188]
    // 0xb4b3f0: r30 = Instance_Color
    //     0xb4b3f0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb4b3f4: stp             lr, x16, [SP]
    // 0xb4b3f8: mov             x1, x0
    // 0xb4b3fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4b3fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4b400: ldr             x4, [x4, #0xaa0]
    // 0xb4b404: r0 = copyWith()
    //     0xb4b404: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4b408: stur            x0, [fp, #-0x38]
    // 0xb4b40c: r0 = Text()
    //     0xb4b40c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4b410: mov             x2, x0
    // 0xb4b414: ldur            x0, [fp, #-0x28]
    // 0xb4b418: stur            x2, [fp, #-0x40]
    // 0xb4b41c: StoreField: r2->field_b = r0
    //     0xb4b41c: stur            w0, [x2, #0xb]
    // 0xb4b420: ldur            x0, [fp, #-0x38]
    // 0xb4b424: StoreField: r2->field_13 = r0
    //     0xb4b424: stur            w0, [x2, #0x13]
    // 0xb4b428: ldur            x0, [fp, #-0x18]
    // 0xb4b42c: LoadField: r1 = r0->field_13
    //     0xb4b42c: ldur            w1, [x0, #0x13]
    // 0xb4b430: DecompressPointer r1
    //     0xb4b430: add             x1, x1, HEAP, lsl #32
    // 0xb4b434: r0 = of()
    //     0xb4b434: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4b438: LoadField: r1 = r0->field_87
    //     0xb4b438: ldur            w1, [x0, #0x87]
    // 0xb4b43c: DecompressPointer r1
    //     0xb4b43c: add             x1, x1, HEAP, lsl #32
    // 0xb4b440: LoadField: r0 = r1->field_2b
    //     0xb4b440: ldur            w0, [x1, #0x2b]
    // 0xb4b444: DecompressPointer r0
    //     0xb4b444: add             x0, x0, HEAP, lsl #32
    // 0xb4b448: r16 = Instance_Color
    //     0xb4b448: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb4b44c: r30 = 12.000000
    //     0xb4b44c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4b450: ldr             lr, [lr, #0x9e8]
    // 0xb4b454: stp             lr, x16, [SP]
    // 0xb4b458: mov             x1, x0
    // 0xb4b45c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb4b45c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb4b460: ldr             x4, [x4, #0x9b8]
    // 0xb4b464: r0 = copyWith()
    //     0xb4b464: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4b468: stur            x0, [fp, #-0x28]
    // 0xb4b46c: r0 = Text()
    //     0xb4b46c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4b470: mov             x3, x0
    // 0xb4b474: r0 = "Bumper Offer Applied!"
    //     0xb4b474: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d08] "Bumper Offer Applied!"
    //     0xb4b478: ldr             x0, [x0, #0xd08]
    // 0xb4b47c: stur            x3, [fp, #-0x38]
    // 0xb4b480: StoreField: r3->field_b = r0
    //     0xb4b480: stur            w0, [x3, #0xb]
    // 0xb4b484: ldur            x0, [fp, #-0x28]
    // 0xb4b488: StoreField: r3->field_13 = r0
    //     0xb4b488: stur            w0, [x3, #0x13]
    // 0xb4b48c: r1 = Null
    //     0xb4b48c: mov             x1, NULL
    // 0xb4b490: r2 = 4
    //     0xb4b490: movz            x2, #0x4
    // 0xb4b494: r0 = AllocateArray()
    //     0xb4b494: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4b498: mov             x2, x0
    // 0xb4b49c: ldur            x0, [fp, #-0x40]
    // 0xb4b4a0: stur            x2, [fp, #-0x28]
    // 0xb4b4a4: StoreField: r2->field_f = r0
    //     0xb4b4a4: stur            w0, [x2, #0xf]
    // 0xb4b4a8: ldur            x0, [fp, #-0x38]
    // 0xb4b4ac: StoreField: r2->field_13 = r0
    //     0xb4b4ac: stur            w0, [x2, #0x13]
    // 0xb4b4b0: r1 = <Widget>
    //     0xb4b4b0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4b4b4: r0 = AllocateGrowableArray()
    //     0xb4b4b4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4b4b8: mov             x1, x0
    // 0xb4b4bc: ldur            x0, [fp, #-0x28]
    // 0xb4b4c0: stur            x1, [fp, #-0x38]
    // 0xb4b4c4: StoreField: r1->field_f = r0
    //     0xb4b4c4: stur            w0, [x1, #0xf]
    // 0xb4b4c8: r2 = 4
    //     0xb4b4c8: movz            x2, #0x4
    // 0xb4b4cc: StoreField: r1->field_b = r2
    //     0xb4b4cc: stur            w2, [x1, #0xb]
    // 0xb4b4d0: r0 = Column()
    //     0xb4b4d0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb4b4d4: mov             x2, x0
    // 0xb4b4d8: r0 = Instance_Axis
    //     0xb4b4d8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb4b4dc: stur            x2, [fp, #-0x28]
    // 0xb4b4e0: StoreField: r2->field_f = r0
    //     0xb4b4e0: stur            w0, [x2, #0xf]
    // 0xb4b4e4: r3 = Instance_MainAxisAlignment
    //     0xb4b4e4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb4b4e8: ldr             x3, [x3, #0xab0]
    // 0xb4b4ec: StoreField: r2->field_13 = r3
    //     0xb4b4ec: stur            w3, [x2, #0x13]
    // 0xb4b4f0: r0 = Instance_MainAxisSize
    //     0xb4b4f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4b4f4: ldr             x0, [x0, #0xa10]
    // 0xb4b4f8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4b4f8: stur            w0, [x2, #0x17]
    // 0xb4b4fc: r4 = Instance_CrossAxisAlignment
    //     0xb4b4fc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb4b500: ldr             x4, [x4, #0x890]
    // 0xb4b504: StoreField: r2->field_1b = r4
    //     0xb4b504: stur            w4, [x2, #0x1b]
    // 0xb4b508: r3 = Instance_VerticalDirection
    //     0xb4b508: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4b50c: ldr             x3, [x3, #0xa20]
    // 0xb4b510: StoreField: r2->field_23 = r3
    //     0xb4b510: stur            w3, [x2, #0x23]
    // 0xb4b514: r4 = Instance_Clip
    //     0xb4b514: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4b518: ldr             x4, [x4, #0x38]
    // 0xb4b51c: StoreField: r2->field_2b = r4
    //     0xb4b51c: stur            w4, [x2, #0x2b]
    // 0xb4b520: StoreField: r2->field_2f = rZR
    //     0xb4b520: stur            xzr, [x2, #0x2f]
    // 0xb4b524: ldur            x1, [fp, #-0x38]
    // 0xb4b528: StoreField: r2->field_b = r1
    //     0xb4b528: stur            w1, [x2, #0xb]
    // 0xb4b52c: r1 = <FlexParentData>
    //     0xb4b52c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb4b530: ldr             x1, [x1, #0xe00]
    // 0xb4b534: r0 = Expanded()
    //     0xb4b534: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb4b538: stur            x0, [fp, #-0x38]
    // 0xb4b53c: StoreField: r0->field_13 = rZR
    //     0xb4b53c: stur            xzr, [x0, #0x13]
    // 0xb4b540: r5 = Instance_FlexFit
    //     0xb4b540: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb4b544: ldr             x5, [x5, #0xe08]
    // 0xb4b548: StoreField: r0->field_1b = r5
    //     0xb4b548: stur            w5, [x0, #0x1b]
    // 0xb4b54c: ldur            x1, [fp, #-0x28]
    // 0xb4b550: StoreField: r0->field_b = r1
    //     0xb4b550: stur            w1, [x0, #0xb]
    // 0xb4b554: ldur            x2, [fp, #-0x18]
    // 0xb4b558: LoadField: r1 = r2->field_13
    //     0xb4b558: ldur            w1, [x2, #0x13]
    // 0xb4b55c: DecompressPointer r1
    //     0xb4b55c: add             x1, x1, HEAP, lsl #32
    // 0xb4b560: r0 = of()
    //     0xb4b560: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4b564: LoadField: r1 = r0->field_87
    //     0xb4b564: ldur            w1, [x0, #0x87]
    // 0xb4b568: DecompressPointer r1
    //     0xb4b568: add             x1, x1, HEAP, lsl #32
    // 0xb4b56c: LoadField: r0 = r1->field_7
    //     0xb4b56c: ldur            w0, [x1, #7]
    // 0xb4b570: DecompressPointer r0
    //     0xb4b570: add             x0, x0, HEAP, lsl #32
    // 0xb4b574: r16 = 14.000000
    //     0xb4b574: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb4b578: ldr             x16, [x16, #0x1d8]
    // 0xb4b57c: r30 = Instance_Color
    //     0xb4b57c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb4b580: stp             lr, x16, [SP]
    // 0xb4b584: mov             x1, x0
    // 0xb4b588: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4b588: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4b58c: ldr             x4, [x4, #0xaa0]
    // 0xb4b590: r0 = copyWith()
    //     0xb4b590: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4b594: stur            x0, [fp, #-0x28]
    // 0xb4b598: r0 = Text()
    //     0xb4b598: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4b59c: r6 = "CHANGE"
    //     0xb4b59c: add             x6, PP, #0x38, lsl #12  ; [pp+0x38d10] "CHANGE"
    //     0xb4b5a0: ldr             x6, [x6, #0xd10]
    // 0xb4b5a4: stur            x0, [fp, #-0x40]
    // 0xb4b5a8: StoreField: r0->field_b = r6
    //     0xb4b5a8: stur            w6, [x0, #0xb]
    // 0xb4b5ac: ldur            x1, [fp, #-0x28]
    // 0xb4b5b0: StoreField: r0->field_13 = r1
    //     0xb4b5b0: stur            w1, [x0, #0x13]
    // 0xb4b5b4: r0 = InkWell()
    //     0xb4b5b4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb4b5b8: mov             x3, x0
    // 0xb4b5bc: ldur            x0, [fp, #-0x40]
    // 0xb4b5c0: stur            x3, [fp, #-0x28]
    // 0xb4b5c4: StoreField: r3->field_b = r0
    //     0xb4b5c4: stur            w0, [x3, #0xb]
    // 0xb4b5c8: ldur            x2, [fp, #-0x18]
    // 0xb4b5cc: r1 = Function '<anonymous closure>':.
    //     0xb4b5cc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b00] AnonymousClosure: (0xb4c320), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xb4a86c)
    //     0xb4b5d0: ldr             x1, [x1, #0xb00]
    // 0xb4b5d4: r0 = AllocateClosure()
    //     0xb4b5d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4b5d8: mov             x1, x0
    // 0xb4b5dc: ldur            x0, [fp, #-0x28]
    // 0xb4b5e0: StoreField: r0->field_f = r1
    //     0xb4b5e0: stur            w1, [x0, #0xf]
    // 0xb4b5e4: r1 = true
    //     0xb4b5e4: add             x1, NULL, #0x20  ; true
    // 0xb4b5e8: StoreField: r0->field_43 = r1
    //     0xb4b5e8: stur            w1, [x0, #0x43]
    // 0xb4b5ec: r2 = Instance_BoxShape
    //     0xb4b5ec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb4b5f0: ldr             x2, [x2, #0x80]
    // 0xb4b5f4: StoreField: r0->field_47 = r2
    //     0xb4b5f4: stur            w2, [x0, #0x47]
    // 0xb4b5f8: StoreField: r0->field_6f = r1
    //     0xb4b5f8: stur            w1, [x0, #0x6f]
    // 0xb4b5fc: r3 = false
    //     0xb4b5fc: add             x3, NULL, #0x30  ; false
    // 0xb4b600: StoreField: r0->field_73 = r3
    //     0xb4b600: stur            w3, [x0, #0x73]
    // 0xb4b604: StoreField: r0->field_83 = r1
    //     0xb4b604: stur            w1, [x0, #0x83]
    // 0xb4b608: StoreField: r0->field_7b = r3
    //     0xb4b608: stur            w3, [x0, #0x7b]
    // 0xb4b60c: r0 = Padding()
    //     0xb4b60c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4b610: mov             x3, x0
    // 0xb4b614: r0 = Instance_EdgeInsets
    //     0xb4b614: add             x0, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb4b618: ldr             x0, [x0, #0x878]
    // 0xb4b61c: stur            x3, [fp, #-0x40]
    // 0xb4b620: StoreField: r3->field_f = r0
    //     0xb4b620: stur            w0, [x3, #0xf]
    // 0xb4b624: ldur            x1, [fp, #-0x28]
    // 0xb4b628: StoreField: r3->field_b = r1
    //     0xb4b628: stur            w1, [x3, #0xb]
    // 0xb4b62c: r1 = Null
    //     0xb4b62c: mov             x1, NULL
    // 0xb4b630: r2 = 10
    //     0xb4b630: movz            x2, #0xa
    // 0xb4b634: r0 = AllocateArray()
    //     0xb4b634: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4b638: mov             x2, x0
    // 0xb4b63c: ldur            x0, [fp, #-0x30]
    // 0xb4b640: stur            x2, [fp, #-0x28]
    // 0xb4b644: StoreField: r2->field_f = r0
    //     0xb4b644: stur            w0, [x2, #0xf]
    // 0xb4b648: r16 = Instance_SizedBox
    //     0xb4b648: add             x16, PP, #0x40, lsl #12  ; [pp+0x40890] Obj!SizedBox@d67f81
    //     0xb4b64c: ldr             x16, [x16, #0x890]
    // 0xb4b650: StoreField: r2->field_13 = r16
    //     0xb4b650: stur            w16, [x2, #0x13]
    // 0xb4b654: ldur            x0, [fp, #-0x38]
    // 0xb4b658: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4b658: stur            w0, [x2, #0x17]
    // 0xb4b65c: r16 = Instance_Spacer
    //     0xb4b65c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb4b660: ldr             x16, [x16, #0xf0]
    // 0xb4b664: StoreField: r2->field_1b = r16
    //     0xb4b664: stur            w16, [x2, #0x1b]
    // 0xb4b668: ldur            x0, [fp, #-0x40]
    // 0xb4b66c: StoreField: r2->field_1f = r0
    //     0xb4b66c: stur            w0, [x2, #0x1f]
    // 0xb4b670: r1 = <Widget>
    //     0xb4b670: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4b674: r0 = AllocateGrowableArray()
    //     0xb4b674: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4b678: mov             x1, x0
    // 0xb4b67c: ldur            x0, [fp, #-0x28]
    // 0xb4b680: stur            x1, [fp, #-0x30]
    // 0xb4b684: StoreField: r1->field_f = r0
    //     0xb4b684: stur            w0, [x1, #0xf]
    // 0xb4b688: r7 = 10
    //     0xb4b688: movz            x7, #0xa
    // 0xb4b68c: StoreField: r1->field_b = r7
    //     0xb4b68c: stur            w7, [x1, #0xb]
    // 0xb4b690: r0 = Row()
    //     0xb4b690: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb4b694: r8 = Instance_Axis
    //     0xb4b694: ldr             x8, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4b698: stur            x0, [fp, #-0x28]
    // 0xb4b69c: StoreField: r0->field_f = r8
    //     0xb4b69c: stur            w8, [x0, #0xf]
    // 0xb4b6a0: r9 = Instance_MainAxisAlignment
    //     0xb4b6a0: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb4b6a4: ldr             x9, [x9, #0xd10]
    // 0xb4b6a8: StoreField: r0->field_13 = r9
    //     0xb4b6a8: stur            w9, [x0, #0x13]
    // 0xb4b6ac: r10 = Instance_MainAxisSize
    //     0xb4b6ac: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4b6b0: ldr             x10, [x10, #0xa10]
    // 0xb4b6b4: ArrayStore: r0[0] = r10  ; List_4
    //     0xb4b6b4: stur            w10, [x0, #0x17]
    // 0xb4b6b8: r11 = Instance_CrossAxisAlignment
    //     0xb4b6b8: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4b6bc: ldr             x11, [x11, #0xa18]
    // 0xb4b6c0: StoreField: r0->field_1b = r11
    //     0xb4b6c0: stur            w11, [x0, #0x1b]
    // 0xb4b6c4: r12 = Instance_VerticalDirection
    //     0xb4b6c4: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4b6c8: ldr             x12, [x12, #0xa20]
    // 0xb4b6cc: StoreField: r0->field_23 = r12
    //     0xb4b6cc: stur            w12, [x0, #0x23]
    // 0xb4b6d0: r13 = Instance_Clip
    //     0xb4b6d0: add             x13, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4b6d4: ldr             x13, [x13, #0x38]
    // 0xb4b6d8: StoreField: r0->field_2b = r13
    //     0xb4b6d8: stur            w13, [x0, #0x2b]
    // 0xb4b6dc: StoreField: r0->field_2f = rZR
    //     0xb4b6dc: stur            xzr, [x0, #0x2f]
    // 0xb4b6e0: ldur            x1, [fp, #-0x30]
    // 0xb4b6e4: StoreField: r0->field_b = r1
    //     0xb4b6e4: stur            w1, [x0, #0xb]
    // 0xb4b6e8: r0 = Container()
    //     0xb4b6e8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb4b6ec: stur            x0, [fp, #-0x30]
    // 0xb4b6f0: r16 = 100.000000
    //     0xb4b6f0: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xb4b6f4: ldur            lr, [fp, #-0x20]
    // 0xb4b6f8: stp             lr, x16, [SP, #8]
    // 0xb4b6fc: ldur            x16, [fp, #-0x28]
    // 0xb4b700: str             x16, [SP]
    // 0xb4b704: mov             x1, x0
    // 0xb4b708: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xb4b708: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xb4b70c: ldr             x4, [x4, #0xc78]
    // 0xb4b710: r0 = Container()
    //     0xb4b710: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb4b714: r1 = <Path>
    //     0xb4b714: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xb4b718: ldr             x1, [x1, #0xd30]
    // 0xb4b71c: r0 = MovieTicketClipper()
    //     0xb4b71c: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0xb4b720: stur            x0, [fp, #-0x20]
    // 0xb4b724: r0 = ClipPath()
    //     0xb4b724: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xb4b728: mov             x1, x0
    // 0xb4b72c: ldur            x0, [fp, #-0x20]
    // 0xb4b730: stur            x1, [fp, #-0x28]
    // 0xb4b734: StoreField: r1->field_f = r0
    //     0xb4b734: stur            w0, [x1, #0xf]
    // 0xb4b738: r0 = Instance_Clip
    //     0xb4b738: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb4b73c: ldr             x0, [x0, #0x138]
    // 0xb4b740: StoreField: r1->field_13 = r0
    //     0xb4b740: stur            w0, [x1, #0x13]
    // 0xb4b744: ldur            x0, [fp, #-0x30]
    // 0xb4b748: StoreField: r1->field_b = r0
    //     0xb4b748: stur            w0, [x1, #0xb]
    // 0xb4b74c: r0 = Padding()
    //     0xb4b74c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4b750: r14 = Instance_EdgeInsets
    //     0xb4b750: add             x14, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb4b754: ldr             x14, [x14, #0x878]
    // 0xb4b758: StoreField: r0->field_f = r14
    //     0xb4b758: stur            w14, [x0, #0xf]
    // 0xb4b75c: ldur            x1, [fp, #-0x28]
    // 0xb4b760: StoreField: r0->field_b = r1
    //     0xb4b760: stur            w1, [x0, #0xb]
    // 0xb4b764: b               #0xb4bd78
    // 0xb4b768: ldur            x1, [fp, #-8]
    // 0xb4b76c: r14 = Instance_EdgeInsets
    //     0xb4b76c: add             x14, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb4b770: ldr             x14, [x14, #0x878]
    // 0xb4b774: r2 = 4
    //     0xb4b774: movz            x2, #0x4
    // 0xb4b778: r3 = Instance_MainAxisAlignment
    //     0xb4b778: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb4b77c: ldr             x3, [x3, #0xab0]
    // 0xb4b780: r4 = Instance_CrossAxisAlignment
    //     0xb4b780: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb4b784: ldr             x4, [x4, #0x890]
    // 0xb4b788: r6 = "CHANGE"
    //     0xb4b788: add             x6, PP, #0x38, lsl #12  ; [pp+0x38d10] "CHANGE"
    //     0xb4b78c: ldr             x6, [x6, #0xd10]
    // 0xb4b790: r9 = Instance_MainAxisAlignment
    //     0xb4b790: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb4b794: ldr             x9, [x9, #0xd10]
    // 0xb4b798: r10 = Instance_MainAxisSize
    //     0xb4b798: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4b79c: ldr             x10, [x10, #0xa10]
    // 0xb4b7a0: r12 = Instance_VerticalDirection
    //     0xb4b7a0: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4b7a4: ldr             x12, [x12, #0xa20]
    // 0xb4b7a8: r0 = Instance_Axis
    //     0xb4b7a8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb4b7ac: r5 = Instance_FlexFit
    //     0xb4b7ac: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb4b7b0: ldr             x5, [x5, #0xe08]
    // 0xb4b7b4: r7 = 10
    //     0xb4b7b4: movz            x7, #0xa
    // 0xb4b7b8: r11 = Instance_CrossAxisAlignment
    //     0xb4b7b8: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4b7bc: ldr             x11, [x11, #0xa18]
    // 0xb4b7c0: r8 = Instance_Axis
    //     0xb4b7c0: ldr             x8, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4b7c4: r13 = Instance_Clip
    //     0xb4b7c4: add             x13, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4b7c8: ldr             x13, [x13, #0x38]
    // 0xb4b7cc: r0 = Radius()
    //     0xb4b7cc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb4b7d0: d0 = 10.000000
    //     0xb4b7d0: fmov            d0, #10.00000000
    // 0xb4b7d4: stur            x0, [fp, #-0x20]
    // 0xb4b7d8: StoreField: r0->field_7 = d0
    //     0xb4b7d8: stur            d0, [x0, #7]
    // 0xb4b7dc: StoreField: r0->field_f = d0
    //     0xb4b7dc: stur            d0, [x0, #0xf]
    // 0xb4b7e0: r0 = BorderRadius()
    //     0xb4b7e0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb4b7e4: mov             x1, x0
    // 0xb4b7e8: ldur            x0, [fp, #-0x20]
    // 0xb4b7ec: stur            x1, [fp, #-0x28]
    // 0xb4b7f0: StoreField: r1->field_7 = r0
    //     0xb4b7f0: stur            w0, [x1, #7]
    // 0xb4b7f4: StoreField: r1->field_b = r0
    //     0xb4b7f4: stur            w0, [x1, #0xb]
    // 0xb4b7f8: StoreField: r1->field_f = r0
    //     0xb4b7f8: stur            w0, [x1, #0xf]
    // 0xb4b7fc: StoreField: r1->field_13 = r0
    //     0xb4b7fc: stur            w0, [x1, #0x13]
    // 0xb4b800: r0 = BoxDecoration()
    //     0xb4b800: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb4b804: mov             x1, x0
    // 0xb4b808: r0 = Instance_Color
    //     0xb4b808: add             x0, PP, #0x40, lsl #12  ; [pp+0x40860] Obj!Color@d6b0a1
    //     0xb4b80c: ldr             x0, [x0, #0x860]
    // 0xb4b810: stur            x1, [fp, #-0x20]
    // 0xb4b814: StoreField: r1->field_7 = r0
    //     0xb4b814: stur            w0, [x1, #7]
    // 0xb4b818: ldur            x0, [fp, #-0x28]
    // 0xb4b81c: StoreField: r1->field_13 = r0
    //     0xb4b81c: stur            w0, [x1, #0x13]
    // 0xb4b820: r0 = Instance_BoxShape
    //     0xb4b820: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb4b824: ldr             x0, [x0, #0x80]
    // 0xb4b828: StoreField: r1->field_23 = r0
    //     0xb4b828: stur            w0, [x1, #0x23]
    // 0xb4b82c: ldur            x2, [fp, #-8]
    // 0xb4b830: LoadField: r3 = r2->field_b
    //     0xb4b830: ldur            w3, [x2, #0xb]
    // 0xb4b834: DecompressPointer r3
    //     0xb4b834: add             x3, x3, HEAP, lsl #32
    // 0xb4b838: cmp             w3, NULL
    // 0xb4b83c: b.eq            #0xb4be44
    // 0xb4b840: LoadField: r4 = r3->field_23
    //     0xb4b840: ldur            w4, [x3, #0x23]
    // 0xb4b844: DecompressPointer r4
    //     0xb4b844: add             x4, x4, HEAP, lsl #32
    // 0xb4b848: LoadField: r3 = r4->field_b
    //     0xb4b848: ldur            w3, [x4, #0xb]
    // 0xb4b84c: DecompressPointer r3
    //     0xb4b84c: add             x3, x3, HEAP, lsl #32
    // 0xb4b850: cmp             w3, NULL
    // 0xb4b854: b.ne            #0xb4b860
    // 0xb4b858: r3 = Null
    //     0xb4b858: mov             x3, NULL
    // 0xb4b85c: b               #0xb4b880
    // 0xb4b860: LoadField: r4 = r3->field_33
    //     0xb4b860: ldur            w4, [x3, #0x33]
    // 0xb4b864: DecompressPointer r4
    //     0xb4b864: add             x4, x4, HEAP, lsl #32
    // 0xb4b868: cmp             w4, NULL
    // 0xb4b86c: b.ne            #0xb4b878
    // 0xb4b870: r3 = Null
    //     0xb4b870: mov             x3, NULL
    // 0xb4b874: b               #0xb4b880
    // 0xb4b878: ArrayLoad: r3 = r4[0]  ; List_4
    //     0xb4b878: ldur            w3, [x4, #0x17]
    // 0xb4b87c: DecompressPointer r3
    //     0xb4b87c: add             x3, x3, HEAP, lsl #32
    // 0xb4b880: cmp             w3, NULL
    // 0xb4b884: b.eq            #0xb4b8c0
    // 0xb4b888: tbnz            w3, #4, #0xb4b8c0
    // 0xb4b88c: r0 = SvgPicture()
    //     0xb4b88c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb4b890: stur            x0, [fp, #-0x28]
    // 0xb4b894: r16 = 30.000000
    //     0xb4b894: add             x16, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xb4b898: ldr             x16, [x16, #0x768]
    // 0xb4b89c: str             x16, [SP]
    // 0xb4b8a0: mov             x1, x0
    // 0xb4b8a4: r2 = "assets/images/gift-icon-popup.svg"
    //     0xb4b8a4: add             x2, PP, #0x52, lsl #12  ; [pp+0x528e8] "assets/images/gift-icon-popup.svg"
    //     0xb4b8a8: ldr             x2, [x2, #0x8e8]
    // 0xb4b8ac: r4 = const [0, 0x3, 0x1, 0x2, height, 0x2, null]
    //     0xb4b8ac: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c760] List(7) [0, 0x3, 0x1, 0x2, "height", 0x2, Null]
    //     0xb4b8b0: ldr             x4, [x4, #0x760]
    // 0xb4b8b4: r0 = SvgPicture.asset()
    //     0xb4b8b4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb4b8b8: ldur            x1, [fp, #-0x28]
    // 0xb4b8bc: b               #0xb4b8c8
    // 0xb4b8c0: r1 = Instance_Icon
    //     0xb4b8c0: add             x1, PP, #0x46, lsl #12  ; [pp+0x46568] Obj!Icon@d662f1
    //     0xb4b8c4: ldr             x1, [x1, #0x568]
    // 0xb4b8c8: ldur            x0, [fp, #-8]
    // 0xb4b8cc: stur            x1, [fp, #-0x28]
    // 0xb4b8d0: r0 = Padding()
    //     0xb4b8d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4b8d4: mov             x3, x0
    // 0xb4b8d8: r0 = Instance_EdgeInsets
    //     0xb4b8d8: add             x0, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb4b8dc: ldr             x0, [x0, #0x878]
    // 0xb4b8e0: stur            x3, [fp, #-0x30]
    // 0xb4b8e4: StoreField: r3->field_f = r0
    //     0xb4b8e4: stur            w0, [x3, #0xf]
    // 0xb4b8e8: ldur            x1, [fp, #-0x28]
    // 0xb4b8ec: StoreField: r3->field_b = r1
    //     0xb4b8ec: stur            w1, [x3, #0xb]
    // 0xb4b8f0: ldur            x1, [fp, #-8]
    // 0xb4b8f4: LoadField: r2 = r1->field_b
    //     0xb4b8f4: ldur            w2, [x1, #0xb]
    // 0xb4b8f8: DecompressPointer r2
    //     0xb4b8f8: add             x2, x2, HEAP, lsl #32
    // 0xb4b8fc: cmp             w2, NULL
    // 0xb4b900: b.eq            #0xb4be48
    // 0xb4b904: LoadField: r1 = r2->field_23
    //     0xb4b904: ldur            w1, [x2, #0x23]
    // 0xb4b908: DecompressPointer r1
    //     0xb4b908: add             x1, x1, HEAP, lsl #32
    // 0xb4b90c: LoadField: r4 = r1->field_b
    //     0xb4b90c: ldur            w4, [x1, #0xb]
    // 0xb4b910: DecompressPointer r4
    //     0xb4b910: add             x4, x4, HEAP, lsl #32
    // 0xb4b914: stur            x4, [fp, #-8]
    // 0xb4b918: cmp             w4, NULL
    // 0xb4b91c: b.ne            #0xb4b928
    // 0xb4b920: r1 = Null
    //     0xb4b920: mov             x1, NULL
    // 0xb4b924: b               #0xb4b94c
    // 0xb4b928: LoadField: r1 = r4->field_33
    //     0xb4b928: ldur            w1, [x4, #0x33]
    // 0xb4b92c: DecompressPointer r1
    //     0xb4b92c: add             x1, x1, HEAP, lsl #32
    // 0xb4b930: cmp             w1, NULL
    // 0xb4b934: b.ne            #0xb4b940
    // 0xb4b938: r1 = Null
    //     0xb4b938: mov             x1, NULL
    // 0xb4b93c: b               #0xb4b94c
    // 0xb4b940: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb4b940: ldur            w2, [x1, #0x17]
    // 0xb4b944: DecompressPointer r2
    //     0xb4b944: add             x2, x2, HEAP, lsl #32
    // 0xb4b948: mov             x1, x2
    // 0xb4b94c: cmp             w1, NULL
    // 0xb4b950: b.eq            #0xb4b968
    // 0xb4b954: tbnz            w1, #4, #0xb4b968
    // 0xb4b958: mov             x0, x3
    // 0xb4b95c: r3 = "Free Gift Added!"
    //     0xb4b95c: add             x3, PP, #0x54, lsl #12  ; [pp+0x54428] "Free Gift Added!"
    //     0xb4b960: ldr             x3, [x3, #0x428]
    // 0xb4b964: b               #0xb4b9e0
    // 0xb4b968: r1 = Null
    //     0xb4b968: mov             x1, NULL
    // 0xb4b96c: r2 = 6
    //     0xb4b96c: movz            x2, #0x6
    // 0xb4b970: r0 = AllocateArray()
    //     0xb4b970: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4b974: r16 = "You\'ve saved "
    //     0xb4b974: add             x16, PP, #0x38, lsl #12  ; [pp+0x38cf8] "You\'ve saved "
    //     0xb4b978: ldr             x16, [x16, #0xcf8]
    // 0xb4b97c: StoreField: r0->field_f = r16
    //     0xb4b97c: stur            w16, [x0, #0xf]
    // 0xb4b980: ldur            x1, [fp, #-8]
    // 0xb4b984: cmp             w1, NULL
    // 0xb4b988: b.ne            #0xb4b994
    // 0xb4b98c: r1 = Null
    //     0xb4b98c: mov             x1, NULL
    // 0xb4b990: b               #0xb4b9b4
    // 0xb4b994: LoadField: r2 = r1->field_33
    //     0xb4b994: ldur            w2, [x1, #0x33]
    // 0xb4b998: DecompressPointer r2
    //     0xb4b998: add             x2, x2, HEAP, lsl #32
    // 0xb4b99c: cmp             w2, NULL
    // 0xb4b9a0: b.ne            #0xb4b9ac
    // 0xb4b9a4: r1 = Null
    //     0xb4b9a4: mov             x1, NULL
    // 0xb4b9a8: b               #0xb4b9b4
    // 0xb4b9ac: LoadField: r1 = r2->field_f
    //     0xb4b9ac: ldur            w1, [x2, #0xf]
    // 0xb4b9b0: DecompressPointer r1
    //     0xb4b9b0: add             x1, x1, HEAP, lsl #32
    // 0xb4b9b4: cmp             w1, NULL
    // 0xb4b9b8: b.ne            #0xb4b9c0
    // 0xb4b9bc: r1 = ""
    //     0xb4b9bc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4b9c0: StoreField: r0->field_13 = r1
    //     0xb4b9c0: stur            w1, [x0, #0x13]
    // 0xb4b9c4: r16 = "!"
    //     0xb4b9c4: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d00] "!"
    //     0xb4b9c8: ldr             x16, [x16, #0xd00]
    // 0xb4b9cc: ArrayStore: r0[0] = r16  ; List_4
    //     0xb4b9cc: stur            w16, [x0, #0x17]
    // 0xb4b9d0: str             x0, [SP]
    // 0xb4b9d4: r0 = _interpolate()
    //     0xb4b9d4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb4b9d8: mov             x3, x0
    // 0xb4b9dc: ldur            x0, [fp, #-0x30]
    // 0xb4b9e0: ldur            x2, [fp, #-0x18]
    // 0xb4b9e4: stur            x3, [fp, #-8]
    // 0xb4b9e8: LoadField: r1 = r2->field_13
    //     0xb4b9e8: ldur            w1, [x2, #0x13]
    // 0xb4b9ec: DecompressPointer r1
    //     0xb4b9ec: add             x1, x1, HEAP, lsl #32
    // 0xb4b9f0: r0 = of()
    //     0xb4b9f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4b9f4: LoadField: r1 = r0->field_87
    //     0xb4b9f4: ldur            w1, [x0, #0x87]
    // 0xb4b9f8: DecompressPointer r1
    //     0xb4b9f8: add             x1, x1, HEAP, lsl #32
    // 0xb4b9fc: LoadField: r0 = r1->field_7
    //     0xb4b9fc: ldur            w0, [x1, #7]
    // 0xb4ba00: DecompressPointer r0
    //     0xb4ba00: add             x0, x0, HEAP, lsl #32
    // 0xb4ba04: r16 = Instance_Color
    //     0xb4ba04: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4ba08: r30 = 12.000000
    //     0xb4ba08: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4ba0c: ldr             lr, [lr, #0x9e8]
    // 0xb4ba10: stp             lr, x16, [SP]
    // 0xb4ba14: mov             x1, x0
    // 0xb4ba18: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb4ba18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb4ba1c: ldr             x4, [x4, #0x9b8]
    // 0xb4ba20: r0 = copyWith()
    //     0xb4ba20: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4ba24: stur            x0, [fp, #-0x28]
    // 0xb4ba28: r0 = Text()
    //     0xb4ba28: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4ba2c: mov             x2, x0
    // 0xb4ba30: ldur            x0, [fp, #-8]
    // 0xb4ba34: stur            x2, [fp, #-0x38]
    // 0xb4ba38: StoreField: r2->field_b = r0
    //     0xb4ba38: stur            w0, [x2, #0xb]
    // 0xb4ba3c: ldur            x0, [fp, #-0x28]
    // 0xb4ba40: StoreField: r2->field_13 = r0
    //     0xb4ba40: stur            w0, [x2, #0x13]
    // 0xb4ba44: ldur            x0, [fp, #-0x18]
    // 0xb4ba48: LoadField: r1 = r0->field_13
    //     0xb4ba48: ldur            w1, [x0, #0x13]
    // 0xb4ba4c: DecompressPointer r1
    //     0xb4ba4c: add             x1, x1, HEAP, lsl #32
    // 0xb4ba50: r0 = of()
    //     0xb4ba50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4ba54: LoadField: r1 = r0->field_87
    //     0xb4ba54: ldur            w1, [x0, #0x87]
    // 0xb4ba58: DecompressPointer r1
    //     0xb4ba58: add             x1, x1, HEAP, lsl #32
    // 0xb4ba5c: LoadField: r0 = r1->field_2b
    //     0xb4ba5c: ldur            w0, [x1, #0x2b]
    // 0xb4ba60: DecompressPointer r0
    //     0xb4ba60: add             x0, x0, HEAP, lsl #32
    // 0xb4ba64: r16 = 12.000000
    //     0xb4ba64: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4ba68: ldr             x16, [x16, #0x9e8]
    // 0xb4ba6c: r30 = Instance_Color
    //     0xb4ba6c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb4ba70: ldr             lr, [lr, #0x858]
    // 0xb4ba74: stp             lr, x16, [SP]
    // 0xb4ba78: mov             x1, x0
    // 0xb4ba7c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4ba7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4ba80: ldr             x4, [x4, #0xaa0]
    // 0xb4ba84: r0 = copyWith()
    //     0xb4ba84: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4ba88: stur            x0, [fp, #-8]
    // 0xb4ba8c: r0 = Text()
    //     0xb4ba8c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4ba90: mov             x1, x0
    // 0xb4ba94: r0 = "Offer Applied!"
    //     0xb4ba94: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d38] "Offer Applied!"
    //     0xb4ba98: ldr             x0, [x0, #0xd38]
    // 0xb4ba9c: stur            x1, [fp, #-0x28]
    // 0xb4baa0: StoreField: r1->field_b = r0
    //     0xb4baa0: stur            w0, [x1, #0xb]
    // 0xb4baa4: ldur            x0, [fp, #-8]
    // 0xb4baa8: StoreField: r1->field_13 = r0
    //     0xb4baa8: stur            w0, [x1, #0x13]
    // 0xb4baac: r0 = Padding()
    //     0xb4baac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4bab0: mov             x3, x0
    // 0xb4bab4: r0 = Instance_EdgeInsets
    //     0xb4bab4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xb4bab8: ldr             x0, [x0, #0x990]
    // 0xb4babc: stur            x3, [fp, #-8]
    // 0xb4bac0: StoreField: r3->field_f = r0
    //     0xb4bac0: stur            w0, [x3, #0xf]
    // 0xb4bac4: ldur            x0, [fp, #-0x28]
    // 0xb4bac8: StoreField: r3->field_b = r0
    //     0xb4bac8: stur            w0, [x3, #0xb]
    // 0xb4bacc: r1 = Null
    //     0xb4bacc: mov             x1, NULL
    // 0xb4bad0: r2 = 4
    //     0xb4bad0: movz            x2, #0x4
    // 0xb4bad4: r0 = AllocateArray()
    //     0xb4bad4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4bad8: mov             x2, x0
    // 0xb4badc: ldur            x0, [fp, #-0x38]
    // 0xb4bae0: stur            x2, [fp, #-0x28]
    // 0xb4bae4: StoreField: r2->field_f = r0
    //     0xb4bae4: stur            w0, [x2, #0xf]
    // 0xb4bae8: ldur            x0, [fp, #-8]
    // 0xb4baec: StoreField: r2->field_13 = r0
    //     0xb4baec: stur            w0, [x2, #0x13]
    // 0xb4baf0: r1 = <Widget>
    //     0xb4baf0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4baf4: r0 = AllocateGrowableArray()
    //     0xb4baf4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4baf8: mov             x1, x0
    // 0xb4bafc: ldur            x0, [fp, #-0x28]
    // 0xb4bb00: stur            x1, [fp, #-8]
    // 0xb4bb04: StoreField: r1->field_f = r0
    //     0xb4bb04: stur            w0, [x1, #0xf]
    // 0xb4bb08: r0 = 4
    //     0xb4bb08: movz            x0, #0x4
    // 0xb4bb0c: StoreField: r1->field_b = r0
    //     0xb4bb0c: stur            w0, [x1, #0xb]
    // 0xb4bb10: r0 = Column()
    //     0xb4bb10: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb4bb14: mov             x2, x0
    // 0xb4bb18: r0 = Instance_Axis
    //     0xb4bb18: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb4bb1c: stur            x2, [fp, #-0x28]
    // 0xb4bb20: StoreField: r2->field_f = r0
    //     0xb4bb20: stur            w0, [x2, #0xf]
    // 0xb4bb24: r0 = Instance_MainAxisAlignment
    //     0xb4bb24: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb4bb28: ldr             x0, [x0, #0xab0]
    // 0xb4bb2c: StoreField: r2->field_13 = r0
    //     0xb4bb2c: stur            w0, [x2, #0x13]
    // 0xb4bb30: r0 = Instance_MainAxisSize
    //     0xb4bb30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4bb34: ldr             x0, [x0, #0xa10]
    // 0xb4bb38: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4bb38: stur            w0, [x2, #0x17]
    // 0xb4bb3c: r1 = Instance_CrossAxisAlignment
    //     0xb4bb3c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb4bb40: ldr             x1, [x1, #0x890]
    // 0xb4bb44: StoreField: r2->field_1b = r1
    //     0xb4bb44: stur            w1, [x2, #0x1b]
    // 0xb4bb48: r3 = Instance_VerticalDirection
    //     0xb4bb48: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4bb4c: ldr             x3, [x3, #0xa20]
    // 0xb4bb50: StoreField: r2->field_23 = r3
    //     0xb4bb50: stur            w3, [x2, #0x23]
    // 0xb4bb54: r4 = Instance_Clip
    //     0xb4bb54: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4bb58: ldr             x4, [x4, #0x38]
    // 0xb4bb5c: StoreField: r2->field_2b = r4
    //     0xb4bb5c: stur            w4, [x2, #0x2b]
    // 0xb4bb60: StoreField: r2->field_2f = rZR
    //     0xb4bb60: stur            xzr, [x2, #0x2f]
    // 0xb4bb64: ldur            x1, [fp, #-8]
    // 0xb4bb68: StoreField: r2->field_b = r1
    //     0xb4bb68: stur            w1, [x2, #0xb]
    // 0xb4bb6c: r1 = <FlexParentData>
    //     0xb4bb6c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb4bb70: ldr             x1, [x1, #0xe00]
    // 0xb4bb74: r0 = Expanded()
    //     0xb4bb74: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb4bb78: stur            x0, [fp, #-8]
    // 0xb4bb7c: StoreField: r0->field_13 = rZR
    //     0xb4bb7c: stur            xzr, [x0, #0x13]
    // 0xb4bb80: r1 = Instance_FlexFit
    //     0xb4bb80: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb4bb84: ldr             x1, [x1, #0xe08]
    // 0xb4bb88: StoreField: r0->field_1b = r1
    //     0xb4bb88: stur            w1, [x0, #0x1b]
    // 0xb4bb8c: ldur            x1, [fp, #-0x28]
    // 0xb4bb90: StoreField: r0->field_b = r1
    //     0xb4bb90: stur            w1, [x0, #0xb]
    // 0xb4bb94: ldur            x2, [fp, #-0x18]
    // 0xb4bb98: LoadField: r1 = r2->field_13
    //     0xb4bb98: ldur            w1, [x2, #0x13]
    // 0xb4bb9c: DecompressPointer r1
    //     0xb4bb9c: add             x1, x1, HEAP, lsl #32
    // 0xb4bba0: r0 = of()
    //     0xb4bba0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4bba4: LoadField: r1 = r0->field_87
    //     0xb4bba4: ldur            w1, [x0, #0x87]
    // 0xb4bba8: DecompressPointer r1
    //     0xb4bba8: add             x1, x1, HEAP, lsl #32
    // 0xb4bbac: LoadField: r0 = r1->field_7
    //     0xb4bbac: ldur            w0, [x1, #7]
    // 0xb4bbb0: DecompressPointer r0
    //     0xb4bbb0: add             x0, x0, HEAP, lsl #32
    // 0xb4bbb4: r16 = 14.000000
    //     0xb4bbb4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb4bbb8: ldr             x16, [x16, #0x1d8]
    // 0xb4bbbc: r30 = Instance_Color
    //     0xb4bbbc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb4bbc0: ldr             lr, [lr, #0x858]
    // 0xb4bbc4: stp             lr, x16, [SP]
    // 0xb4bbc8: mov             x1, x0
    // 0xb4bbcc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4bbcc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4bbd0: ldr             x4, [x4, #0xaa0]
    // 0xb4bbd4: r0 = copyWith()
    //     0xb4bbd4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4bbd8: stur            x0, [fp, #-0x28]
    // 0xb4bbdc: r0 = Text()
    //     0xb4bbdc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4bbe0: mov             x1, x0
    // 0xb4bbe4: r0 = "CHANGE"
    //     0xb4bbe4: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d10] "CHANGE"
    //     0xb4bbe8: ldr             x0, [x0, #0xd10]
    // 0xb4bbec: stur            x1, [fp, #-0x38]
    // 0xb4bbf0: StoreField: r1->field_b = r0
    //     0xb4bbf0: stur            w0, [x1, #0xb]
    // 0xb4bbf4: ldur            x0, [fp, #-0x28]
    // 0xb4bbf8: StoreField: r1->field_13 = r0
    //     0xb4bbf8: stur            w0, [x1, #0x13]
    // 0xb4bbfc: r0 = InkWell()
    //     0xb4bbfc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb4bc00: mov             x3, x0
    // 0xb4bc04: ldur            x0, [fp, #-0x38]
    // 0xb4bc08: stur            x3, [fp, #-0x28]
    // 0xb4bc0c: StoreField: r3->field_b = r0
    //     0xb4bc0c: stur            w0, [x3, #0xb]
    // 0xb4bc10: ldur            x2, [fp, #-0x18]
    // 0xb4bc14: r1 = Function '<anonymous closure>':.
    //     0xb4bc14: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b08] AnonymousClosure: (0xb4c0bc), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xb4a86c)
    //     0xb4bc18: ldr             x1, [x1, #0xb08]
    // 0xb4bc1c: r0 = AllocateClosure()
    //     0xb4bc1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4bc20: mov             x1, x0
    // 0xb4bc24: ldur            x0, [fp, #-0x28]
    // 0xb4bc28: StoreField: r0->field_f = r1
    //     0xb4bc28: stur            w1, [x0, #0xf]
    // 0xb4bc2c: r1 = true
    //     0xb4bc2c: add             x1, NULL, #0x20  ; true
    // 0xb4bc30: StoreField: r0->field_43 = r1
    //     0xb4bc30: stur            w1, [x0, #0x43]
    // 0xb4bc34: r2 = Instance_BoxShape
    //     0xb4bc34: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb4bc38: ldr             x2, [x2, #0x80]
    // 0xb4bc3c: StoreField: r0->field_47 = r2
    //     0xb4bc3c: stur            w2, [x0, #0x47]
    // 0xb4bc40: StoreField: r0->field_6f = r1
    //     0xb4bc40: stur            w1, [x0, #0x6f]
    // 0xb4bc44: r3 = false
    //     0xb4bc44: add             x3, NULL, #0x30  ; false
    // 0xb4bc48: StoreField: r0->field_73 = r3
    //     0xb4bc48: stur            w3, [x0, #0x73]
    // 0xb4bc4c: StoreField: r0->field_83 = r1
    //     0xb4bc4c: stur            w1, [x0, #0x83]
    // 0xb4bc50: StoreField: r0->field_7b = r3
    //     0xb4bc50: stur            w3, [x0, #0x7b]
    // 0xb4bc54: r0 = Padding()
    //     0xb4bc54: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4bc58: mov             x3, x0
    // 0xb4bc5c: r0 = Instance_EdgeInsets
    //     0xb4bc5c: add             x0, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb4bc60: ldr             x0, [x0, #0x878]
    // 0xb4bc64: stur            x3, [fp, #-0x38]
    // 0xb4bc68: StoreField: r3->field_f = r0
    //     0xb4bc68: stur            w0, [x3, #0xf]
    // 0xb4bc6c: ldur            x1, [fp, #-0x28]
    // 0xb4bc70: StoreField: r3->field_b = r1
    //     0xb4bc70: stur            w1, [x3, #0xb]
    // 0xb4bc74: r1 = Null
    //     0xb4bc74: mov             x1, NULL
    // 0xb4bc78: r2 = 10
    //     0xb4bc78: movz            x2, #0xa
    // 0xb4bc7c: r0 = AllocateArray()
    //     0xb4bc7c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4bc80: mov             x2, x0
    // 0xb4bc84: ldur            x0, [fp, #-0x30]
    // 0xb4bc88: stur            x2, [fp, #-0x28]
    // 0xb4bc8c: StoreField: r2->field_f = r0
    //     0xb4bc8c: stur            w0, [x2, #0xf]
    // 0xb4bc90: r16 = Instance_SizedBox
    //     0xb4bc90: add             x16, PP, #0x40, lsl #12  ; [pp+0x40890] Obj!SizedBox@d67f81
    //     0xb4bc94: ldr             x16, [x16, #0x890]
    // 0xb4bc98: StoreField: r2->field_13 = r16
    //     0xb4bc98: stur            w16, [x2, #0x13]
    // 0xb4bc9c: ldur            x0, [fp, #-8]
    // 0xb4bca0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4bca0: stur            w0, [x2, #0x17]
    // 0xb4bca4: r16 = Instance_Spacer
    //     0xb4bca4: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb4bca8: ldr             x16, [x16, #0xf0]
    // 0xb4bcac: StoreField: r2->field_1b = r16
    //     0xb4bcac: stur            w16, [x2, #0x1b]
    // 0xb4bcb0: ldur            x0, [fp, #-0x38]
    // 0xb4bcb4: StoreField: r2->field_1f = r0
    //     0xb4bcb4: stur            w0, [x2, #0x1f]
    // 0xb4bcb8: r1 = <Widget>
    //     0xb4bcb8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4bcbc: r0 = AllocateGrowableArray()
    //     0xb4bcbc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4bcc0: mov             x1, x0
    // 0xb4bcc4: ldur            x0, [fp, #-0x28]
    // 0xb4bcc8: stur            x1, [fp, #-8]
    // 0xb4bccc: StoreField: r1->field_f = r0
    //     0xb4bccc: stur            w0, [x1, #0xf]
    // 0xb4bcd0: r0 = 10
    //     0xb4bcd0: movz            x0, #0xa
    // 0xb4bcd4: StoreField: r1->field_b = r0
    //     0xb4bcd4: stur            w0, [x1, #0xb]
    // 0xb4bcd8: r0 = Row()
    //     0xb4bcd8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb4bcdc: mov             x1, x0
    // 0xb4bce0: r0 = Instance_Axis
    //     0xb4bce0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4bce4: stur            x1, [fp, #-0x28]
    // 0xb4bce8: StoreField: r1->field_f = r0
    //     0xb4bce8: stur            w0, [x1, #0xf]
    // 0xb4bcec: r0 = Instance_MainAxisAlignment
    //     0xb4bcec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb4bcf0: ldr             x0, [x0, #0xd10]
    // 0xb4bcf4: StoreField: r1->field_13 = r0
    //     0xb4bcf4: stur            w0, [x1, #0x13]
    // 0xb4bcf8: r0 = Instance_MainAxisSize
    //     0xb4bcf8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4bcfc: ldr             x0, [x0, #0xa10]
    // 0xb4bd00: ArrayStore: r1[0] = r0  ; List_4
    //     0xb4bd00: stur            w0, [x1, #0x17]
    // 0xb4bd04: r0 = Instance_CrossAxisAlignment
    //     0xb4bd04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4bd08: ldr             x0, [x0, #0xa18]
    // 0xb4bd0c: StoreField: r1->field_1b = r0
    //     0xb4bd0c: stur            w0, [x1, #0x1b]
    // 0xb4bd10: r0 = Instance_VerticalDirection
    //     0xb4bd10: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4bd14: ldr             x0, [x0, #0xa20]
    // 0xb4bd18: StoreField: r1->field_23 = r0
    //     0xb4bd18: stur            w0, [x1, #0x23]
    // 0xb4bd1c: r0 = Instance_Clip
    //     0xb4bd1c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4bd20: ldr             x0, [x0, #0x38]
    // 0xb4bd24: StoreField: r1->field_2b = r0
    //     0xb4bd24: stur            w0, [x1, #0x2b]
    // 0xb4bd28: StoreField: r1->field_2f = rZR
    //     0xb4bd28: stur            xzr, [x1, #0x2f]
    // 0xb4bd2c: ldur            x0, [fp, #-8]
    // 0xb4bd30: StoreField: r1->field_b = r0
    //     0xb4bd30: stur            w0, [x1, #0xb]
    // 0xb4bd34: r0 = Container()
    //     0xb4bd34: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb4bd38: stur            x0, [fp, #-8]
    // 0xb4bd3c: ldur            x16, [fp, #-0x20]
    // 0xb4bd40: ldur            lr, [fp, #-0x28]
    // 0xb4bd44: stp             lr, x16, [SP]
    // 0xb4bd48: mov             x1, x0
    // 0xb4bd4c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb4bd4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb4bd50: ldr             x4, [x4, #0x88]
    // 0xb4bd54: r0 = Container()
    //     0xb4bd54: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb4bd58: r0 = Padding()
    //     0xb4bd58: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4bd5c: mov             x1, x0
    // 0xb4bd60: r0 = Instance_EdgeInsets
    //     0xb4bd60: add             x0, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb4bd64: ldr             x0, [x0, #0x878]
    // 0xb4bd68: StoreField: r1->field_f = r0
    //     0xb4bd68: stur            w0, [x1, #0xf]
    // 0xb4bd6c: ldur            x0, [fp, #-8]
    // 0xb4bd70: StoreField: r1->field_b = r0
    //     0xb4bd70: stur            w0, [x1, #0xb]
    // 0xb4bd74: mov             x0, x1
    // 0xb4bd78: stur            x0, [fp, #-8]
    // 0xb4bd7c: r0 = InkWell()
    //     0xb4bd7c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb4bd80: mov             x3, x0
    // 0xb4bd84: ldur            x0, [fp, #-8]
    // 0xb4bd88: stur            x3, [fp, #-0x20]
    // 0xb4bd8c: StoreField: r3->field_b = r0
    //     0xb4bd8c: stur            w0, [x3, #0xb]
    // 0xb4bd90: ldur            x2, [fp, #-0x18]
    // 0xb4bd94: r1 = Function '<anonymous closure>':.
    //     0xb4bd94: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b10] AnonymousClosure: (0xb4be4c), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xb4a86c)
    //     0xb4bd98: ldr             x1, [x1, #0xb10]
    // 0xb4bd9c: r0 = AllocateClosure()
    //     0xb4bd9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4bda0: mov             x1, x0
    // 0xb4bda4: ldur            x0, [fp, #-0x20]
    // 0xb4bda8: StoreField: r0->field_f = r1
    //     0xb4bda8: stur            w1, [x0, #0xf]
    // 0xb4bdac: r1 = true
    //     0xb4bdac: add             x1, NULL, #0x20  ; true
    // 0xb4bdb0: StoreField: r0->field_43 = r1
    //     0xb4bdb0: stur            w1, [x0, #0x43]
    // 0xb4bdb4: r2 = Instance_BoxShape
    //     0xb4bdb4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb4bdb8: ldr             x2, [x2, #0x80]
    // 0xb4bdbc: StoreField: r0->field_47 = r2
    //     0xb4bdbc: stur            w2, [x0, #0x47]
    // 0xb4bdc0: StoreField: r0->field_6f = r1
    //     0xb4bdc0: stur            w1, [x0, #0x6f]
    // 0xb4bdc4: r2 = false
    //     0xb4bdc4: add             x2, NULL, #0x30  ; false
    // 0xb4bdc8: StoreField: r0->field_73 = r2
    //     0xb4bdc8: stur            w2, [x0, #0x73]
    // 0xb4bdcc: StoreField: r0->field_83 = r1
    //     0xb4bdcc: stur            w1, [x0, #0x83]
    // 0xb4bdd0: StoreField: r0->field_7b = r2
    //     0xb4bdd0: stur            w2, [x0, #0x7b]
    // 0xb4bdd4: mov             x1, x0
    // 0xb4bdd8: ldur            x0, [fp, #-0x10]
    // 0xb4bddc: stur            x1, [fp, #-8]
    // 0xb4bde0: r0 = Visibility()
    //     0xb4bde0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb4bde4: ldur            x1, [fp, #-8]
    // 0xb4bde8: StoreField: r0->field_b = r1
    //     0xb4bde8: stur            w1, [x0, #0xb]
    // 0xb4bdec: r1 = Instance_SizedBox
    //     0xb4bdec: ldr             x1, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb4bdf0: StoreField: r0->field_f = r1
    //     0xb4bdf0: stur            w1, [x0, #0xf]
    // 0xb4bdf4: ldur            x1, [fp, #-0x10]
    // 0xb4bdf8: StoreField: r0->field_13 = r1
    //     0xb4bdf8: stur            w1, [x0, #0x13]
    // 0xb4bdfc: r1 = false
    //     0xb4bdfc: add             x1, NULL, #0x30  ; false
    // 0xb4be00: ArrayStore: r0[0] = r1  ; List_4
    //     0xb4be00: stur            w1, [x0, #0x17]
    // 0xb4be04: StoreField: r0->field_1b = r1
    //     0xb4be04: stur            w1, [x0, #0x1b]
    // 0xb4be08: StoreField: r0->field_1f = r1
    //     0xb4be08: stur            w1, [x0, #0x1f]
    // 0xb4be0c: StoreField: r0->field_23 = r1
    //     0xb4be0c: stur            w1, [x0, #0x23]
    // 0xb4be10: StoreField: r0->field_27 = r1
    //     0xb4be10: stur            w1, [x0, #0x27]
    // 0xb4be14: StoreField: r0->field_2b = r1
    //     0xb4be14: stur            w1, [x0, #0x2b]
    // 0xb4be18: LeaveFrame
    //     0xb4be18: mov             SP, fp
    //     0xb4be1c: ldp             fp, lr, [SP], #0x10
    // 0xb4be20: ret
    //     0xb4be20: ret             
    // 0xb4be24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4be24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4be28: b               #0xb4a88c
    // 0xb4be2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4be2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4be30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4be30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4be34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4be34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4be38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4be38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4be3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4be3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4be40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4be40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4be44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4be44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4be48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4be48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb4be4c, size: 0x7c
    // 0xb4be4c: EnterFrame
    //     0xb4be4c: stp             fp, lr, [SP, #-0x10]!
    //     0xb4be50: mov             fp, SP
    // 0xb4be54: AllocStack(0x30)
    //     0xb4be54: sub             SP, SP, #0x30
    // 0xb4be58: SetupParameters()
    //     0xb4be58: ldr             x0, [fp, #0x10]
    //     0xb4be5c: ldur            w2, [x0, #0x17]
    //     0xb4be60: add             x2, x2, HEAP, lsl #32
    // 0xb4be64: CheckStackOverflow
    //     0xb4be64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4be68: cmp             SP, x16
    //     0xb4be6c: b.ls            #0xb4bec0
    // 0xb4be70: LoadField: r0 = r2->field_13
    //     0xb4be70: ldur            w0, [x2, #0x13]
    // 0xb4be74: DecompressPointer r0
    //     0xb4be74: add             x0, x0, HEAP, lsl #32
    // 0xb4be78: stur            x0, [fp, #-8]
    // 0xb4be7c: r1 = Function '<anonymous closure>':.
    //     0xb4be7c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b18] AnonymousClosure: (0xb4bec8), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xb4a86c)
    //     0xb4be80: ldr             x1, [x1, #0xb18]
    // 0xb4be84: r0 = AllocateClosure()
    //     0xb4be84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4be88: stp             x0, NULL, [SP, #0x18]
    // 0xb4be8c: ldur            x16, [fp, #-8]
    // 0xb4be90: r30 = true
    //     0xb4be90: add             lr, NULL, #0x20  ; true
    // 0xb4be94: stp             lr, x16, [SP, #8]
    // 0xb4be98: r16 = Instance_RoundedRectangleBorder
    //     0xb4be98: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0xb4be9c: ldr             x16, [x16, #0xc78]
    // 0xb4bea0: str             x16, [SP]
    // 0xb4bea4: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xb4bea4: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xb4bea8: ldr             x4, [x4, #0xb20]
    // 0xb4beac: r0 = showModalBottomSheet()
    //     0xb4beac: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xb4beb0: r0 = Null
    //     0xb4beb0: mov             x0, NULL
    // 0xb4beb4: LeaveFrame
    //     0xb4beb4: mov             SP, fp
    //     0xb4beb8: ldp             fp, lr, [SP], #0x10
    // 0xb4bebc: ret
    //     0xb4bebc: ret             
    // 0xb4bec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4bec0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4bec4: b               #0xb4be70
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb4bec8, size: 0x1e8
    // 0xb4bec8: EnterFrame
    //     0xb4bec8: stp             fp, lr, [SP, #-0x10]!
    //     0xb4becc: mov             fp, SP
    // 0xb4bed0: AllocStack(0x58)
    //     0xb4bed0: sub             SP, SP, #0x58
    // 0xb4bed4: SetupParameters()
    //     0xb4bed4: ldr             x0, [fp, #0x18]
    //     0xb4bed8: ldur            w1, [x0, #0x17]
    //     0xb4bedc: add             x1, x1, HEAP, lsl #32
    //     0xb4bee0: stur            x1, [fp, #-8]
    // 0xb4bee4: CheckStackOverflow
    //     0xb4bee4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4bee8: cmp             SP, x16
    //     0xb4beec: b.ls            #0xb4c0a4
    // 0xb4bef0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb4bef0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb4bef4: ldr             x0, [x0, #0x1c80]
    //     0xb4bef8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb4befc: cmp             w0, w16
    //     0xb4bf00: b.ne            #0xb4bf0c
    //     0xb4bf04: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb4bf08: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb4bf0c: r0 = GetNavigation.size()
    //     0xb4bf0c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb4bf10: LoadField: d0 = r0->field_f
    //     0xb4bf10: ldur            d0, [x0, #0xf]
    // 0xb4bf14: d1 = 0.750000
    //     0xb4bf14: fmov            d1, #0.75000000
    // 0xb4bf18: fmul            d2, d0, d1
    // 0xb4bf1c: stur            d2, [fp, #-0x50]
    // 0xb4bf20: r0 = GetNavigation.size()
    //     0xb4bf20: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb4bf24: LoadField: d0 = r0->field_f
    //     0xb4bf24: ldur            d0, [x0, #0xf]
    // 0xb4bf28: d1 = 0.550000
    //     0xb4bf28: add             x17, PP, #0x54, lsl #12  ; [pp+0x54470] IMM: double(0.55) from 0x3fe199999999999a
    //     0xb4bf2c: ldr             d1, [x17, #0x470]
    // 0xb4bf30: fmul            d2, d0, d1
    // 0xb4bf34: stur            d2, [fp, #-0x58]
    // 0xb4bf38: r0 = BoxConstraints()
    //     0xb4bf38: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb4bf3c: stur            x0, [fp, #-0x40]
    // 0xb4bf40: StoreField: r0->field_7 = rZR
    //     0xb4bf40: stur            xzr, [x0, #7]
    // 0xb4bf44: d0 = inf
    //     0xb4bf44: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb4bf48: StoreField: r0->field_f = d0
    //     0xb4bf48: stur            d0, [x0, #0xf]
    // 0xb4bf4c: ldur            d0, [fp, #-0x58]
    // 0xb4bf50: ArrayStore: r0[0] = d0  ; List_8
    //     0xb4bf50: stur            d0, [x0, #0x17]
    // 0xb4bf54: ldur            d0, [fp, #-0x50]
    // 0xb4bf58: StoreField: r0->field_1f = d0
    //     0xb4bf58: stur            d0, [x0, #0x1f]
    // 0xb4bf5c: ldur            x1, [fp, #-8]
    // 0xb4bf60: LoadField: r2 = r1->field_f
    //     0xb4bf60: ldur            w2, [x1, #0xf]
    // 0xb4bf64: DecompressPointer r2
    //     0xb4bf64: add             x2, x2, HEAP, lsl #32
    // 0xb4bf68: LoadField: r1 = r2->field_b
    //     0xb4bf68: ldur            w1, [x2, #0xb]
    // 0xb4bf6c: DecompressPointer r1
    //     0xb4bf6c: add             x1, x1, HEAP, lsl #32
    // 0xb4bf70: cmp             w1, NULL
    // 0xb4bf74: b.eq            #0xb4c0ac
    // 0xb4bf78: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb4bf78: ldur            w2, [x1, #0x17]
    // 0xb4bf7c: DecompressPointer r2
    //     0xb4bf7c: add             x2, x2, HEAP, lsl #32
    // 0xb4bf80: stur            x2, [fp, #-0x38]
    // 0xb4bf84: LoadField: r3 = r1->field_1b
    //     0xb4bf84: ldur            w3, [x1, #0x1b]
    // 0xb4bf88: DecompressPointer r3
    //     0xb4bf88: add             x3, x3, HEAP, lsl #32
    // 0xb4bf8c: stur            x3, [fp, #-0x30]
    // 0xb4bf90: LoadField: r4 = r1->field_f
    //     0xb4bf90: ldur            w4, [x1, #0xf]
    // 0xb4bf94: DecompressPointer r4
    //     0xb4bf94: add             x4, x4, HEAP, lsl #32
    // 0xb4bf98: LoadField: r5 = r4->field_b
    //     0xb4bf98: ldur            w5, [x4, #0xb]
    // 0xb4bf9c: DecompressPointer r5
    //     0xb4bf9c: add             x5, x5, HEAP, lsl #32
    // 0xb4bfa0: stur            x5, [fp, #-0x28]
    // 0xb4bfa4: LoadField: r4 = r1->field_23
    //     0xb4bfa4: ldur            w4, [x1, #0x23]
    // 0xb4bfa8: DecompressPointer r4
    //     0xb4bfa8: add             x4, x4, HEAP, lsl #32
    // 0xb4bfac: LoadField: r6 = r4->field_b
    //     0xb4bfac: ldur            w6, [x4, #0xb]
    // 0xb4bfb0: DecompressPointer r6
    //     0xb4bfb0: add             x6, x6, HEAP, lsl #32
    // 0xb4bfb4: cmp             w6, NULL
    // 0xb4bfb8: b.ne            #0xb4bfc4
    // 0xb4bfbc: r4 = Null
    //     0xb4bfbc: mov             x4, NULL
    // 0xb4bfc0: b               #0xb4bfe8
    // 0xb4bfc4: LoadField: r4 = r6->field_33
    //     0xb4bfc4: ldur            w4, [x6, #0x33]
    // 0xb4bfc8: DecompressPointer r4
    //     0xb4bfc8: add             x4, x4, HEAP, lsl #32
    // 0xb4bfcc: cmp             w4, NULL
    // 0xb4bfd0: b.ne            #0xb4bfdc
    // 0xb4bfd4: r4 = Null
    //     0xb4bfd4: mov             x4, NULL
    // 0xb4bfd8: b               #0xb4bfe8
    // 0xb4bfdc: LoadField: r7 = r4->field_7
    //     0xb4bfdc: ldur            w7, [x4, #7]
    // 0xb4bfe0: DecompressPointer r7
    //     0xb4bfe0: add             x7, x7, HEAP, lsl #32
    // 0xb4bfe4: mov             x4, x7
    // 0xb4bfe8: stur            x4, [fp, #-0x20]
    // 0xb4bfec: LoadField: r7 = r1->field_1f
    //     0xb4bfec: ldur            w7, [x1, #0x1f]
    // 0xb4bff0: DecompressPointer r7
    //     0xb4bff0: add             x7, x7, HEAP, lsl #32
    // 0xb4bff4: stur            x7, [fp, #-0x18]
    // 0xb4bff8: LoadField: r8 = r1->field_b
    //     0xb4bff8: ldur            w8, [x1, #0xb]
    // 0xb4bffc: DecompressPointer r8
    //     0xb4bffc: add             x8, x8, HEAP, lsl #32
    // 0xb4c000: stur            x8, [fp, #-0x10]
    // 0xb4c004: cmp             w6, NULL
    // 0xb4c008: b.ne            #0xb4c014
    // 0xb4c00c: r1 = Null
    //     0xb4c00c: mov             x1, NULL
    // 0xb4c010: b               #0xb4c01c
    // 0xb4c014: LoadField: r1 = r6->field_33
    //     0xb4c014: ldur            w1, [x6, #0x33]
    // 0xb4c018: DecompressPointer r1
    //     0xb4c018: add             x1, x1, HEAP, lsl #32
    // 0xb4c01c: stur            x1, [fp, #-8]
    // 0xb4c020: r0 = OffersListWidget()
    //     0xb4c020: bl              #0xb4c0b0  ; AllocateOffersListWidgetStub -> OffersListWidget (size=0x34)
    // 0xb4c024: mov             x1, x0
    // 0xb4c028: ldur            x0, [fp, #-0x28]
    // 0xb4c02c: stur            x1, [fp, #-0x48]
    // 0xb4c030: StoreField: r1->field_b = r0
    //     0xb4c030: stur            w0, [x1, #0xb]
    // 0xb4c034: ldur            x0, [fp, #-0x20]
    // 0xb4c038: StoreField: r1->field_f = r0
    //     0xb4c038: stur            w0, [x1, #0xf]
    // 0xb4c03c: ldur            x0, [fp, #-0x38]
    // 0xb4c040: StoreField: r1->field_13 = r0
    //     0xb4c040: stur            w0, [x1, #0x13]
    // 0xb4c044: ldur            x0, [fp, #-0x30]
    // 0xb4c048: ArrayStore: r1[0] = r0  ; List_4
    //     0xb4c048: stur            w0, [x1, #0x17]
    // 0xb4c04c: r0 = true
    //     0xb4c04c: add             x0, NULL, #0x20  ; true
    // 0xb4c050: StoreField: r1->field_1b = r0
    //     0xb4c050: stur            w0, [x1, #0x1b]
    // 0xb4c054: r0 = "payment_screen"
    //     0xb4c054: add             x0, PP, #0x38, lsl #12  ; [pp+0x38de8] "payment_screen"
    //     0xb4c058: ldr             x0, [x0, #0xde8]
    // 0xb4c05c: StoreField: r1->field_23 = r0
    //     0xb4c05c: stur            w0, [x1, #0x23]
    // 0xb4c060: ldur            x0, [fp, #-0x18]
    // 0xb4c064: StoreField: r1->field_1f = r0
    //     0xb4c064: stur            w0, [x1, #0x1f]
    // 0xb4c068: r0 = "false"
    //     0xb4c068: add             x0, PP, #8, lsl #12  ; [pp+0x8ed8] "false"
    //     0xb4c06c: ldr             x0, [x0, #0xed8]
    // 0xb4c070: StoreField: r1->field_27 = r0
    //     0xb4c070: stur            w0, [x1, #0x27]
    // 0xb4c074: ldur            x0, [fp, #-0x10]
    // 0xb4c078: StoreField: r1->field_2b = r0
    //     0xb4c078: stur            w0, [x1, #0x2b]
    // 0xb4c07c: ldur            x0, [fp, #-8]
    // 0xb4c080: StoreField: r1->field_2f = r0
    //     0xb4c080: stur            w0, [x1, #0x2f]
    // 0xb4c084: r0 = ConstrainedBox()
    //     0xb4c084: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xb4c088: ldur            x1, [fp, #-0x40]
    // 0xb4c08c: StoreField: r0->field_f = r1
    //     0xb4c08c: stur            w1, [x0, #0xf]
    // 0xb4c090: ldur            x1, [fp, #-0x48]
    // 0xb4c094: StoreField: r0->field_b = r1
    //     0xb4c094: stur            w1, [x0, #0xb]
    // 0xb4c098: LeaveFrame
    //     0xb4c098: mov             SP, fp
    //     0xb4c09c: ldp             fp, lr, [SP], #0x10
    // 0xb4c0a0: ret
    //     0xb4c0a0: ret             
    // 0xb4c0a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4c0a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4c0a8: b               #0xb4bef0
    // 0xb4c0ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4c0ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb4c0bc, size: 0x7c
    // 0xb4c0bc: EnterFrame
    //     0xb4c0bc: stp             fp, lr, [SP, #-0x10]!
    //     0xb4c0c0: mov             fp, SP
    // 0xb4c0c4: AllocStack(0x30)
    //     0xb4c0c4: sub             SP, SP, #0x30
    // 0xb4c0c8: SetupParameters()
    //     0xb4c0c8: ldr             x0, [fp, #0x10]
    //     0xb4c0cc: ldur            w2, [x0, #0x17]
    //     0xb4c0d0: add             x2, x2, HEAP, lsl #32
    // 0xb4c0d4: CheckStackOverflow
    //     0xb4c0d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4c0d8: cmp             SP, x16
    //     0xb4c0dc: b.ls            #0xb4c130
    // 0xb4c0e0: LoadField: r0 = r2->field_13
    //     0xb4c0e0: ldur            w0, [x2, #0x13]
    // 0xb4c0e4: DecompressPointer r0
    //     0xb4c0e4: add             x0, x0, HEAP, lsl #32
    // 0xb4c0e8: stur            x0, [fp, #-8]
    // 0xb4c0ec: r1 = Function '<anonymous closure>':.
    //     0xb4c0ec: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b20] AnonymousClosure: (0xb4c138), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xb4a86c)
    //     0xb4c0f0: ldr             x1, [x1, #0xb20]
    // 0xb4c0f4: r0 = AllocateClosure()
    //     0xb4c0f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4c0f8: stp             x0, NULL, [SP, #0x18]
    // 0xb4c0fc: ldur            x16, [fp, #-8]
    // 0xb4c100: r30 = true
    //     0xb4c100: add             lr, NULL, #0x20  ; true
    // 0xb4c104: stp             lr, x16, [SP, #8]
    // 0xb4c108: r16 = Instance_RoundedRectangleBorder
    //     0xb4c108: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0xb4c10c: ldr             x16, [x16, #0xc78]
    // 0xb4c110: str             x16, [SP]
    // 0xb4c114: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xb4c114: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xb4c118: ldr             x4, [x4, #0xb20]
    // 0xb4c11c: r0 = showModalBottomSheet()
    //     0xb4c11c: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xb4c120: r0 = Null
    //     0xb4c120: mov             x0, NULL
    // 0xb4c124: LeaveFrame
    //     0xb4c124: mov             SP, fp
    //     0xb4c128: ldp             fp, lr, [SP], #0x10
    // 0xb4c12c: ret
    //     0xb4c12c: ret             
    // 0xb4c130: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4c130: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4c134: b               #0xb4c0e0
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb4c138, size: 0x1e8
    // 0xb4c138: EnterFrame
    //     0xb4c138: stp             fp, lr, [SP, #-0x10]!
    //     0xb4c13c: mov             fp, SP
    // 0xb4c140: AllocStack(0x58)
    //     0xb4c140: sub             SP, SP, #0x58
    // 0xb4c144: SetupParameters()
    //     0xb4c144: ldr             x0, [fp, #0x18]
    //     0xb4c148: ldur            w1, [x0, #0x17]
    //     0xb4c14c: add             x1, x1, HEAP, lsl #32
    //     0xb4c150: stur            x1, [fp, #-8]
    // 0xb4c154: CheckStackOverflow
    //     0xb4c154: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4c158: cmp             SP, x16
    //     0xb4c15c: b.ls            #0xb4c314
    // 0xb4c160: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb4c160: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb4c164: ldr             x0, [x0, #0x1c80]
    //     0xb4c168: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb4c16c: cmp             w0, w16
    //     0xb4c170: b.ne            #0xb4c17c
    //     0xb4c174: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb4c178: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb4c17c: r0 = GetNavigation.size()
    //     0xb4c17c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb4c180: LoadField: d0 = r0->field_f
    //     0xb4c180: ldur            d0, [x0, #0xf]
    // 0xb4c184: d1 = 0.750000
    //     0xb4c184: fmov            d1, #0.75000000
    // 0xb4c188: fmul            d2, d0, d1
    // 0xb4c18c: stur            d2, [fp, #-0x50]
    // 0xb4c190: r0 = GetNavigation.size()
    //     0xb4c190: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb4c194: LoadField: d0 = r0->field_f
    //     0xb4c194: ldur            d0, [x0, #0xf]
    // 0xb4c198: d1 = 0.550000
    //     0xb4c198: add             x17, PP, #0x54, lsl #12  ; [pp+0x54470] IMM: double(0.55) from 0x3fe199999999999a
    //     0xb4c19c: ldr             d1, [x17, #0x470]
    // 0xb4c1a0: fmul            d2, d0, d1
    // 0xb4c1a4: stur            d2, [fp, #-0x58]
    // 0xb4c1a8: r0 = BoxConstraints()
    //     0xb4c1a8: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb4c1ac: stur            x0, [fp, #-0x40]
    // 0xb4c1b0: StoreField: r0->field_7 = rZR
    //     0xb4c1b0: stur            xzr, [x0, #7]
    // 0xb4c1b4: d0 = inf
    //     0xb4c1b4: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb4c1b8: StoreField: r0->field_f = d0
    //     0xb4c1b8: stur            d0, [x0, #0xf]
    // 0xb4c1bc: ldur            d0, [fp, #-0x58]
    // 0xb4c1c0: ArrayStore: r0[0] = d0  ; List_8
    //     0xb4c1c0: stur            d0, [x0, #0x17]
    // 0xb4c1c4: ldur            d0, [fp, #-0x50]
    // 0xb4c1c8: StoreField: r0->field_1f = d0
    //     0xb4c1c8: stur            d0, [x0, #0x1f]
    // 0xb4c1cc: ldur            x1, [fp, #-8]
    // 0xb4c1d0: LoadField: r2 = r1->field_f
    //     0xb4c1d0: ldur            w2, [x1, #0xf]
    // 0xb4c1d4: DecompressPointer r2
    //     0xb4c1d4: add             x2, x2, HEAP, lsl #32
    // 0xb4c1d8: LoadField: r1 = r2->field_b
    //     0xb4c1d8: ldur            w1, [x2, #0xb]
    // 0xb4c1dc: DecompressPointer r1
    //     0xb4c1dc: add             x1, x1, HEAP, lsl #32
    // 0xb4c1e0: cmp             w1, NULL
    // 0xb4c1e4: b.eq            #0xb4c31c
    // 0xb4c1e8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb4c1e8: ldur            w2, [x1, #0x17]
    // 0xb4c1ec: DecompressPointer r2
    //     0xb4c1ec: add             x2, x2, HEAP, lsl #32
    // 0xb4c1f0: stur            x2, [fp, #-0x38]
    // 0xb4c1f4: LoadField: r3 = r1->field_1b
    //     0xb4c1f4: ldur            w3, [x1, #0x1b]
    // 0xb4c1f8: DecompressPointer r3
    //     0xb4c1f8: add             x3, x3, HEAP, lsl #32
    // 0xb4c1fc: stur            x3, [fp, #-0x30]
    // 0xb4c200: LoadField: r4 = r1->field_f
    //     0xb4c200: ldur            w4, [x1, #0xf]
    // 0xb4c204: DecompressPointer r4
    //     0xb4c204: add             x4, x4, HEAP, lsl #32
    // 0xb4c208: LoadField: r5 = r4->field_b
    //     0xb4c208: ldur            w5, [x4, #0xb]
    // 0xb4c20c: DecompressPointer r5
    //     0xb4c20c: add             x5, x5, HEAP, lsl #32
    // 0xb4c210: stur            x5, [fp, #-0x28]
    // 0xb4c214: LoadField: r4 = r1->field_23
    //     0xb4c214: ldur            w4, [x1, #0x23]
    // 0xb4c218: DecompressPointer r4
    //     0xb4c218: add             x4, x4, HEAP, lsl #32
    // 0xb4c21c: LoadField: r6 = r4->field_b
    //     0xb4c21c: ldur            w6, [x4, #0xb]
    // 0xb4c220: DecompressPointer r6
    //     0xb4c220: add             x6, x6, HEAP, lsl #32
    // 0xb4c224: cmp             w6, NULL
    // 0xb4c228: b.ne            #0xb4c234
    // 0xb4c22c: r4 = Null
    //     0xb4c22c: mov             x4, NULL
    // 0xb4c230: b               #0xb4c258
    // 0xb4c234: LoadField: r4 = r6->field_33
    //     0xb4c234: ldur            w4, [x6, #0x33]
    // 0xb4c238: DecompressPointer r4
    //     0xb4c238: add             x4, x4, HEAP, lsl #32
    // 0xb4c23c: cmp             w4, NULL
    // 0xb4c240: b.ne            #0xb4c24c
    // 0xb4c244: r4 = Null
    //     0xb4c244: mov             x4, NULL
    // 0xb4c248: b               #0xb4c258
    // 0xb4c24c: LoadField: r7 = r4->field_7
    //     0xb4c24c: ldur            w7, [x4, #7]
    // 0xb4c250: DecompressPointer r7
    //     0xb4c250: add             x7, x7, HEAP, lsl #32
    // 0xb4c254: mov             x4, x7
    // 0xb4c258: stur            x4, [fp, #-0x20]
    // 0xb4c25c: LoadField: r7 = r1->field_1f
    //     0xb4c25c: ldur            w7, [x1, #0x1f]
    // 0xb4c260: DecompressPointer r7
    //     0xb4c260: add             x7, x7, HEAP, lsl #32
    // 0xb4c264: stur            x7, [fp, #-0x18]
    // 0xb4c268: LoadField: r8 = r1->field_b
    //     0xb4c268: ldur            w8, [x1, #0xb]
    // 0xb4c26c: DecompressPointer r8
    //     0xb4c26c: add             x8, x8, HEAP, lsl #32
    // 0xb4c270: stur            x8, [fp, #-0x10]
    // 0xb4c274: cmp             w6, NULL
    // 0xb4c278: b.ne            #0xb4c284
    // 0xb4c27c: r1 = Null
    //     0xb4c27c: mov             x1, NULL
    // 0xb4c280: b               #0xb4c28c
    // 0xb4c284: LoadField: r1 = r6->field_33
    //     0xb4c284: ldur            w1, [x6, #0x33]
    // 0xb4c288: DecompressPointer r1
    //     0xb4c288: add             x1, x1, HEAP, lsl #32
    // 0xb4c28c: stur            x1, [fp, #-8]
    // 0xb4c290: r0 = OffersListWidget()
    //     0xb4c290: bl              #0xb4c0b0  ; AllocateOffersListWidgetStub -> OffersListWidget (size=0x34)
    // 0xb4c294: mov             x1, x0
    // 0xb4c298: ldur            x0, [fp, #-0x28]
    // 0xb4c29c: stur            x1, [fp, #-0x48]
    // 0xb4c2a0: StoreField: r1->field_b = r0
    //     0xb4c2a0: stur            w0, [x1, #0xb]
    // 0xb4c2a4: ldur            x0, [fp, #-0x20]
    // 0xb4c2a8: StoreField: r1->field_f = r0
    //     0xb4c2a8: stur            w0, [x1, #0xf]
    // 0xb4c2ac: ldur            x0, [fp, #-0x38]
    // 0xb4c2b0: StoreField: r1->field_13 = r0
    //     0xb4c2b0: stur            w0, [x1, #0x13]
    // 0xb4c2b4: ldur            x0, [fp, #-0x30]
    // 0xb4c2b8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb4c2b8: stur            w0, [x1, #0x17]
    // 0xb4c2bc: r0 = true
    //     0xb4c2bc: add             x0, NULL, #0x20  ; true
    // 0xb4c2c0: StoreField: r1->field_1b = r0
    //     0xb4c2c0: stur            w0, [x1, #0x1b]
    // 0xb4c2c4: r0 = "payment_screen"
    //     0xb4c2c4: add             x0, PP, #0x38, lsl #12  ; [pp+0x38de8] "payment_screen"
    //     0xb4c2c8: ldr             x0, [x0, #0xde8]
    // 0xb4c2cc: StoreField: r1->field_23 = r0
    //     0xb4c2cc: stur            w0, [x1, #0x23]
    // 0xb4c2d0: ldur            x0, [fp, #-0x18]
    // 0xb4c2d4: StoreField: r1->field_1f = r0
    //     0xb4c2d4: stur            w0, [x1, #0x1f]
    // 0xb4c2d8: r0 = "true"
    //     0xb4c2d8: add             x0, PP, #8, lsl #12  ; [pp+0x8ed0] "true"
    //     0xb4c2dc: ldr             x0, [x0, #0xed0]
    // 0xb4c2e0: StoreField: r1->field_27 = r0
    //     0xb4c2e0: stur            w0, [x1, #0x27]
    // 0xb4c2e4: ldur            x0, [fp, #-0x10]
    // 0xb4c2e8: StoreField: r1->field_2b = r0
    //     0xb4c2e8: stur            w0, [x1, #0x2b]
    // 0xb4c2ec: ldur            x0, [fp, #-8]
    // 0xb4c2f0: StoreField: r1->field_2f = r0
    //     0xb4c2f0: stur            w0, [x1, #0x2f]
    // 0xb4c2f4: r0 = ConstrainedBox()
    //     0xb4c2f4: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xb4c2f8: ldur            x1, [fp, #-0x40]
    // 0xb4c2fc: StoreField: r0->field_f = r1
    //     0xb4c2fc: stur            w1, [x0, #0xf]
    // 0xb4c300: ldur            x1, [fp, #-0x48]
    // 0xb4c304: StoreField: r0->field_b = r1
    //     0xb4c304: stur            w1, [x0, #0xb]
    // 0xb4c308: LeaveFrame
    //     0xb4c308: mov             SP, fp
    //     0xb4c30c: ldp             fp, lr, [SP], #0x10
    // 0xb4c310: ret
    //     0xb4c310: ret             
    // 0xb4c314: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4c314: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4c318: b               #0xb4c160
    // 0xb4c31c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4c31c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb4c320, size: 0x7c
    // 0xb4c320: EnterFrame
    //     0xb4c320: stp             fp, lr, [SP, #-0x10]!
    //     0xb4c324: mov             fp, SP
    // 0xb4c328: AllocStack(0x30)
    //     0xb4c328: sub             SP, SP, #0x30
    // 0xb4c32c: SetupParameters()
    //     0xb4c32c: ldr             x0, [fp, #0x10]
    //     0xb4c330: ldur            w2, [x0, #0x17]
    //     0xb4c334: add             x2, x2, HEAP, lsl #32
    // 0xb4c338: CheckStackOverflow
    //     0xb4c338: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4c33c: cmp             SP, x16
    //     0xb4c340: b.ls            #0xb4c394
    // 0xb4c344: LoadField: r0 = r2->field_13
    //     0xb4c344: ldur            w0, [x2, #0x13]
    // 0xb4c348: DecompressPointer r0
    //     0xb4c348: add             x0, x0, HEAP, lsl #32
    // 0xb4c34c: stur            x0, [fp, #-8]
    // 0xb4c350: r1 = Function '<anonymous closure>':.
    //     0xb4c350: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b28] AnonymousClosure: (0xb4c138), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xb4a86c)
    //     0xb4c354: ldr             x1, [x1, #0xb28]
    // 0xb4c358: r0 = AllocateClosure()
    //     0xb4c358: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4c35c: stp             x0, NULL, [SP, #0x18]
    // 0xb4c360: ldur            x16, [fp, #-8]
    // 0xb4c364: r30 = true
    //     0xb4c364: add             lr, NULL, #0x20  ; true
    // 0xb4c368: stp             lr, x16, [SP, #8]
    // 0xb4c36c: r16 = Instance_RoundedRectangleBorder
    //     0xb4c36c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0xb4c370: ldr             x16, [x16, #0xc78]
    // 0xb4c374: str             x16, [SP]
    // 0xb4c378: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xb4c378: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xb4c37c: ldr             x4, [x4, #0xb20]
    // 0xb4c380: r0 = showModalBottomSheet()
    //     0xb4c380: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xb4c384: r0 = Null
    //     0xb4c384: mov             x0, NULL
    // 0xb4c388: LeaveFrame
    //     0xb4c388: mov             SP, fp
    //     0xb4c38c: ldp             fp, lr, [SP], #0x10
    // 0xb4c390: ret
    //     0xb4c390: ret             
    // 0xb4c394: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4c394: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4c398: b               #0xb4c344
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb4c39c, size: 0x7c
    // 0xb4c39c: EnterFrame
    //     0xb4c39c: stp             fp, lr, [SP, #-0x10]!
    //     0xb4c3a0: mov             fp, SP
    // 0xb4c3a4: AllocStack(0x30)
    //     0xb4c3a4: sub             SP, SP, #0x30
    // 0xb4c3a8: SetupParameters()
    //     0xb4c3a8: ldr             x0, [fp, #0x10]
    //     0xb4c3ac: ldur            w2, [x0, #0x17]
    //     0xb4c3b0: add             x2, x2, HEAP, lsl #32
    // 0xb4c3b4: CheckStackOverflow
    //     0xb4c3b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4c3b8: cmp             SP, x16
    //     0xb4c3bc: b.ls            #0xb4c410
    // 0xb4c3c0: LoadField: r0 = r2->field_13
    //     0xb4c3c0: ldur            w0, [x2, #0x13]
    // 0xb4c3c4: DecompressPointer r0
    //     0xb4c3c4: add             x0, x0, HEAP, lsl #32
    // 0xb4c3c8: stur            x0, [fp, #-8]
    // 0xb4c3cc: r1 = Function '<anonymous closure>':.
    //     0xb4c3cc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b30] AnonymousClosure: (0xb4bec8), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xb4a86c)
    //     0xb4c3d0: ldr             x1, [x1, #0xb30]
    // 0xb4c3d4: r0 = AllocateClosure()
    //     0xb4c3d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4c3d8: stp             x0, NULL, [SP, #0x18]
    // 0xb4c3dc: ldur            x16, [fp, #-8]
    // 0xb4c3e0: r30 = true
    //     0xb4c3e0: add             lr, NULL, #0x20  ; true
    // 0xb4c3e4: stp             lr, x16, [SP, #8]
    // 0xb4c3e8: r16 = Instance_RoundedRectangleBorder
    //     0xb4c3e8: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0xb4c3ec: ldr             x16, [x16, #0xc78]
    // 0xb4c3f0: str             x16, [SP]
    // 0xb4c3f4: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xb4c3f4: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xb4c3f8: ldr             x4, [x4, #0xb20]
    // 0xb4c3fc: r0 = showModalBottomSheet()
    //     0xb4c3fc: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xb4c400: r0 = Null
    //     0xb4c400: mov             x0, NULL
    // 0xb4c404: LeaveFrame
    //     0xb4c404: mov             SP, fp
    //     0xb4c408: ldp             fp, lr, [SP], #0x10
    // 0xb4c40c: ret
    //     0xb4c40c: ret             
    // 0xb4c410: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4c410: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4c414: b               #0xb4c3c0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb4c418, size: 0x7c
    // 0xb4c418: EnterFrame
    //     0xb4c418: stp             fp, lr, [SP, #-0x10]!
    //     0xb4c41c: mov             fp, SP
    // 0xb4c420: AllocStack(0x30)
    //     0xb4c420: sub             SP, SP, #0x30
    // 0xb4c424: SetupParameters()
    //     0xb4c424: ldr             x0, [fp, #0x10]
    //     0xb4c428: ldur            w2, [x0, #0x17]
    //     0xb4c42c: add             x2, x2, HEAP, lsl #32
    // 0xb4c430: CheckStackOverflow
    //     0xb4c430: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4c434: cmp             SP, x16
    //     0xb4c438: b.ls            #0xb4c48c
    // 0xb4c43c: LoadField: r0 = r2->field_13
    //     0xb4c43c: ldur            w0, [x2, #0x13]
    // 0xb4c440: DecompressPointer r0
    //     0xb4c440: add             x0, x0, HEAP, lsl #32
    // 0xb4c444: stur            x0, [fp, #-8]
    // 0xb4c448: r1 = Function '<anonymous closure>':.
    //     0xb4c448: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b38] AnonymousClosure: (0xb4bec8), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xb4a86c)
    //     0xb4c44c: ldr             x1, [x1, #0xb38]
    // 0xb4c450: r0 = AllocateClosure()
    //     0xb4c450: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4c454: stp             x0, NULL, [SP, #0x18]
    // 0xb4c458: ldur            x16, [fp, #-8]
    // 0xb4c45c: r30 = true
    //     0xb4c45c: add             lr, NULL, #0x20  ; true
    // 0xb4c460: stp             lr, x16, [SP, #8]
    // 0xb4c464: r16 = Instance_RoundedRectangleBorder
    //     0xb4c464: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0xb4c468: ldr             x16, [x16, #0xc78]
    // 0xb4c46c: str             x16, [SP]
    // 0xb4c470: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xb4c470: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xb4c474: ldr             x4, [x4, #0xb20]
    // 0xb4c478: r0 = showModalBottomSheet()
    //     0xb4c478: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xb4c47c: r0 = Null
    //     0xb4c47c: mov             x0, NULL
    // 0xb4c480: LeaveFrame
    //     0xb4c480: mov             SP, fp
    //     0xb4c484: ldp             fp, lr, [SP], #0x10
    // 0xb4c488: ret
    //     0xb4c488: ret             
    // 0xb4c48c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4c48c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4c490: b               #0xb4c43c
  }
}

// class id: 4096, size: 0x30, field offset: 0xc
//   const constructor, 
class OfferSectionWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7edd8, size: 0x24
    // 0xc7edd8: EnterFrame
    //     0xc7edd8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7eddc: mov             fp, SP
    // 0xc7ede0: mov             x0, x1
    // 0xc7ede4: r1 = <OfferSectionWidget>
    //     0xc7ede4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a38] TypeArguments: <OfferSectionWidget>
    //     0xc7ede8: ldr             x1, [x1, #0xa38]
    // 0xc7edec: r0 = _OfferSectionWidgetState()
    //     0xc7edec: bl              #0xc7edfc  ; Allocate_OfferSectionWidgetStateStub -> _OfferSectionWidgetState (size=0x14)
    // 0xc7edf0: LeaveFrame
    //     0xc7edf0: mov             SP, fp
    //     0xc7edf4: ldp             fp, lr, [SP], #0x10
    // 0xc7edf8: ret
    //     0xc7edf8: ret             
  }
}
