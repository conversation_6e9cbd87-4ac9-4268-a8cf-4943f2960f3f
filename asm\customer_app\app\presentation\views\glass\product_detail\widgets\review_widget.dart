// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/review_widget.dart

// class id: 1049444, size: 0x8
class :: {
}

// class id: 3306, size: 0x1c, field offset: 0x14
class _ReviewWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb8e998, size: 0x110
    // 0xb8e998: EnterFrame
    //     0xb8e998: stp             fp, lr, [SP, #-0x10]!
    //     0xb8e99c: mov             fp, SP
    // 0xb8e9a0: AllocStack(0x30)
    //     0xb8e9a0: sub             SP, SP, #0x30
    // 0xb8e9a4: SetupParameters(_ReviewWidgetState this /* r1 => r1, fp-0x8 */)
    //     0xb8e9a4: stur            x1, [fp, #-8]
    // 0xb8e9a8: CheckStackOverflow
    //     0xb8e9a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8e9ac: cmp             SP, x16
    //     0xb8e9b0: b.ls            #0xb8ea9c
    // 0xb8e9b4: r1 = 1
    //     0xb8e9b4: movz            x1, #0x1
    // 0xb8e9b8: r0 = AllocateContext()
    //     0xb8e9b8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb8e9bc: mov             x3, x0
    // 0xb8e9c0: ldur            x0, [fp, #-8]
    // 0xb8e9c4: stur            x3, [fp, #-0x18]
    // 0xb8e9c8: StoreField: r3->field_f = r0
    //     0xb8e9c8: stur            w0, [x3, #0xf]
    // 0xb8e9cc: LoadField: r1 = r0->field_b
    //     0xb8e9cc: ldur            w1, [x0, #0xb]
    // 0xb8e9d0: DecompressPointer r1
    //     0xb8e9d0: add             x1, x1, HEAP, lsl #32
    // 0xb8e9d4: cmp             w1, NULL
    // 0xb8e9d8: b.eq            #0xb8eaa4
    // 0xb8e9dc: LoadField: r0 = r1->field_b
    //     0xb8e9dc: ldur            w0, [x1, #0xb]
    // 0xb8e9e0: DecompressPointer r0
    //     0xb8e9e0: add             x0, x0, HEAP, lsl #32
    // 0xb8e9e4: cmp             w0, NULL
    // 0xb8e9e8: b.ne            #0xb8e9f4
    // 0xb8e9ec: r0 = Null
    //     0xb8e9ec: mov             x0, NULL
    // 0xb8e9f0: b               #0xb8ea00
    // 0xb8e9f4: LoadField: r1 = r0->field_13
    //     0xb8e9f4: ldur            w1, [x0, #0x13]
    // 0xb8e9f8: DecompressPointer r1
    //     0xb8e9f8: add             x1, x1, HEAP, lsl #32
    // 0xb8e9fc: LoadField: r0 = r1->field_b
    //     0xb8e9fc: ldur            w0, [x1, #0xb]
    // 0xb8ea00: cmp             w0, NULL
    // 0xb8ea04: b.ne            #0xb8ea10
    // 0xb8ea08: r0 = 0
    //     0xb8ea08: movz            x0, #0
    // 0xb8ea0c: b               #0xb8ea18
    // 0xb8ea10: r1 = LoadInt32Instr(r0)
    //     0xb8ea10: sbfx            x1, x0, #1, #0x1f
    // 0xb8ea14: mov             x0, x1
    // 0xb8ea18: stur            x0, [fp, #-0x10]
    // 0xb8ea1c: r1 = Function '<anonymous closure>':.
    //     0xb8ea1c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a6e0] AnonymousClosure: (0xa928e0), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14fc2bc)
    //     0xb8ea20: ldr             x1, [x1, #0x6e0]
    // 0xb8ea24: r2 = Null
    //     0xb8ea24: mov             x2, NULL
    // 0xb8ea28: r0 = AllocateClosure()
    //     0xb8ea28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8ea2c: ldur            x2, [fp, #-0x18]
    // 0xb8ea30: r1 = Function '<anonymous closure>':.
    //     0xb8ea30: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a6e8] AnonymousClosure: (0xb8eaa8), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xb8e998)
    //     0xb8ea34: ldr             x1, [x1, #0x6e8]
    // 0xb8ea38: stur            x0, [fp, #-8]
    // 0xb8ea3c: r0 = AllocateClosure()
    //     0xb8ea3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8ea40: stur            x0, [fp, #-0x18]
    // 0xb8ea44: r0 = ListView()
    //     0xb8ea44: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb8ea48: stur            x0, [fp, #-0x20]
    // 0xb8ea4c: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb8ea4c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb8ea50: ldr             x16, [x16, #0x1c8]
    // 0xb8ea54: r30 = true
    //     0xb8ea54: add             lr, NULL, #0x20  ; true
    // 0xb8ea58: stp             lr, x16, [SP]
    // 0xb8ea5c: mov             x1, x0
    // 0xb8ea60: ldur            x2, [fp, #-0x18]
    // 0xb8ea64: ldur            x3, [fp, #-0x10]
    // 0xb8ea68: ldur            x5, [fp, #-8]
    // 0xb8ea6c: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x4, shrinkWrap, 0x5, null]
    //     0xb8ea6c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f968] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x4, "shrinkWrap", 0x5, Null]
    //     0xb8ea70: ldr             x4, [x4, #0x968]
    // 0xb8ea74: r0 = ListView.separated()
    //     0xb8ea74: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb8ea78: r0 = Padding()
    //     0xb8ea78: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8ea7c: r1 = Instance_EdgeInsets
    //     0xb8ea7c: add             x1, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xb8ea80: ldr             x1, [x1, #0x778]
    // 0xb8ea84: StoreField: r0->field_f = r1
    //     0xb8ea84: stur            w1, [x0, #0xf]
    // 0xb8ea88: ldur            x1, [fp, #-0x20]
    // 0xb8ea8c: StoreField: r0->field_b = r1
    //     0xb8ea8c: stur            w1, [x0, #0xb]
    // 0xb8ea90: LeaveFrame
    //     0xb8ea90: mov             SP, fp
    //     0xb8ea94: ldp             fp, lr, [SP], #0x10
    // 0xb8ea98: ret
    //     0xb8ea98: ret             
    // 0xb8ea9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8ea9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8eaa0: b               #0xb8e9b4
    // 0xb8eaa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8eaa4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb8eaa8, size: 0xf84
    // 0xb8eaa8: EnterFrame
    //     0xb8eaa8: stp             fp, lr, [SP, #-0x10]!
    //     0xb8eaac: mov             fp, SP
    // 0xb8eab0: AllocStack(0x80)
    //     0xb8eab0: sub             SP, SP, #0x80
    // 0xb8eab4: SetupParameters()
    //     0xb8eab4: ldr             x0, [fp, #0x20]
    //     0xb8eab8: ldur            w1, [x0, #0x17]
    //     0xb8eabc: add             x1, x1, HEAP, lsl #32
    //     0xb8eac0: stur            x1, [fp, #-8]
    // 0xb8eac4: CheckStackOverflow
    //     0xb8eac4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8eac8: cmp             SP, x16
    //     0xb8eacc: b.ls            #0xb8fa00
    // 0xb8ead0: r1 = 2
    //     0xb8ead0: movz            x1, #0x2
    // 0xb8ead4: r0 = AllocateContext()
    //     0xb8ead4: bl              #0x16f6108  ; AllocateContextStub
    // 0xb8ead8: mov             x2, x0
    // 0xb8eadc: ldur            x0, [fp, #-8]
    // 0xb8eae0: stur            x2, [fp, #-0x10]
    // 0xb8eae4: StoreField: r2->field_b = r0
    //     0xb8eae4: stur            w0, [x2, #0xb]
    // 0xb8eae8: ldr             x3, [fp, #0x18]
    // 0xb8eaec: StoreField: r2->field_f = r3
    //     0xb8eaec: stur            w3, [x2, #0xf]
    // 0xb8eaf0: LoadField: r1 = r0->field_f
    //     0xb8eaf0: ldur            w1, [x0, #0xf]
    // 0xb8eaf4: DecompressPointer r1
    //     0xb8eaf4: add             x1, x1, HEAP, lsl #32
    // 0xb8eaf8: LoadField: r0 = r1->field_b
    //     0xb8eaf8: ldur            w0, [x1, #0xb]
    // 0xb8eafc: DecompressPointer r0
    //     0xb8eafc: add             x0, x0, HEAP, lsl #32
    // 0xb8eb00: cmp             w0, NULL
    // 0xb8eb04: b.eq            #0xb8fa08
    // 0xb8eb08: LoadField: r1 = r0->field_b
    //     0xb8eb08: ldur            w1, [x0, #0xb]
    // 0xb8eb0c: DecompressPointer r1
    //     0xb8eb0c: add             x1, x1, HEAP, lsl #32
    // 0xb8eb10: cmp             w1, NULL
    // 0xb8eb14: b.ne            #0xb8eb20
    // 0xb8eb18: r0 = Null
    //     0xb8eb18: mov             x0, NULL
    // 0xb8eb1c: b               #0xb8eb64
    // 0xb8eb20: ldr             x0, [fp, #0x10]
    // 0xb8eb24: LoadField: r4 = r1->field_13
    //     0xb8eb24: ldur            w4, [x1, #0x13]
    // 0xb8eb28: DecompressPointer r4
    //     0xb8eb28: add             x4, x4, HEAP, lsl #32
    // 0xb8eb2c: LoadField: r1 = r4->field_b
    //     0xb8eb2c: ldur            w1, [x4, #0xb]
    // 0xb8eb30: r5 = LoadInt32Instr(r0)
    //     0xb8eb30: sbfx            x5, x0, #1, #0x1f
    //     0xb8eb34: tbz             w0, #0, #0xb8eb3c
    //     0xb8eb38: ldur            x5, [x0, #7]
    // 0xb8eb3c: r0 = LoadInt32Instr(r1)
    //     0xb8eb3c: sbfx            x0, x1, #1, #0x1f
    // 0xb8eb40: mov             x1, x5
    // 0xb8eb44: cmp             x1, x0
    // 0xb8eb48: b.hs            #0xb8fa0c
    // 0xb8eb4c: LoadField: r0 = r4->field_f
    //     0xb8eb4c: ldur            w0, [x4, #0xf]
    // 0xb8eb50: DecompressPointer r0
    //     0xb8eb50: add             x0, x0, HEAP, lsl #32
    // 0xb8eb54: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb8eb54: add             x16, x0, x5, lsl #2
    //     0xb8eb58: ldur            w1, [x16, #0xf]
    // 0xb8eb5c: DecompressPointer r1
    //     0xb8eb5c: add             x1, x1, HEAP, lsl #32
    // 0xb8eb60: mov             x0, x1
    // 0xb8eb64: stur            x0, [fp, #-8]
    // 0xb8eb68: StoreField: r2->field_13 = r0
    //     0xb8eb68: stur            w0, [x2, #0x13]
    // 0xb8eb6c: mov             x1, x3
    // 0xb8eb70: r0 = of()
    //     0xb8eb70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8eb74: LoadField: r1 = r0->field_5b
    //     0xb8eb74: ldur            w1, [x0, #0x5b]
    // 0xb8eb78: DecompressPointer r1
    //     0xb8eb78: add             x1, x1, HEAP, lsl #32
    // 0xb8eb7c: r0 = LoadClassIdInstr(r1)
    //     0xb8eb7c: ldur            x0, [x1, #-1]
    //     0xb8eb80: ubfx            x0, x0, #0xc, #0x14
    // 0xb8eb84: d0 = 0.200000
    //     0xb8eb84: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb8eb88: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb8eb88: sub             lr, x0, #0xffa
    //     0xb8eb8c: ldr             lr, [x21, lr, lsl #3]
    //     0xb8eb90: blr             lr
    // 0xb8eb94: mov             x2, x0
    // 0xb8eb98: r1 = Null
    //     0xb8eb98: mov             x1, NULL
    // 0xb8eb9c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb8eb9c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb8eba0: r0 = Border.all()
    //     0xb8eba0: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb8eba4: stur            x0, [fp, #-0x18]
    // 0xb8eba8: r0 = Radius()
    //     0xb8eba8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8ebac: d0 = 15.000000
    //     0xb8ebac: fmov            d0, #15.00000000
    // 0xb8ebb0: stur            x0, [fp, #-0x20]
    // 0xb8ebb4: StoreField: r0->field_7 = d0
    //     0xb8ebb4: stur            d0, [x0, #7]
    // 0xb8ebb8: StoreField: r0->field_f = d0
    //     0xb8ebb8: stur            d0, [x0, #0xf]
    // 0xb8ebbc: r0 = BorderRadius()
    //     0xb8ebbc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8ebc0: mov             x1, x0
    // 0xb8ebc4: ldur            x0, [fp, #-0x20]
    // 0xb8ebc8: stur            x1, [fp, #-0x28]
    // 0xb8ebcc: StoreField: r1->field_7 = r0
    //     0xb8ebcc: stur            w0, [x1, #7]
    // 0xb8ebd0: StoreField: r1->field_b = r0
    //     0xb8ebd0: stur            w0, [x1, #0xb]
    // 0xb8ebd4: StoreField: r1->field_f = r0
    //     0xb8ebd4: stur            w0, [x1, #0xf]
    // 0xb8ebd8: StoreField: r1->field_13 = r0
    //     0xb8ebd8: stur            w0, [x1, #0x13]
    // 0xb8ebdc: r0 = BoxDecoration()
    //     0xb8ebdc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8ebe0: mov             x2, x0
    // 0xb8ebe4: ldur            x0, [fp, #-0x18]
    // 0xb8ebe8: stur            x2, [fp, #-0x20]
    // 0xb8ebec: StoreField: r2->field_f = r0
    //     0xb8ebec: stur            w0, [x2, #0xf]
    // 0xb8ebf0: ldur            x0, [fp, #-0x28]
    // 0xb8ebf4: StoreField: r2->field_13 = r0
    //     0xb8ebf4: stur            w0, [x2, #0x13]
    // 0xb8ebf8: r0 = Instance_BoxShape
    //     0xb8ebf8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8ebfc: ldr             x0, [x0, #0x80]
    // 0xb8ec00: StoreField: r2->field_23 = r0
    //     0xb8ec00: stur            w0, [x2, #0x23]
    // 0xb8ec04: r1 = Instance_Color
    //     0xb8ec04: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8ec08: d0 = 0.050000
    //     0xb8ec08: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0xb8ec0c: r0 = withOpacity()
    //     0xb8ec0c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb8ec10: stur            x0, [fp, #-0x18]
    // 0xb8ec14: r0 = BoxDecoration()
    //     0xb8ec14: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8ec18: mov             x1, x0
    // 0xb8ec1c: ldur            x0, [fp, #-0x18]
    // 0xb8ec20: stur            x1, [fp, #-0x28]
    // 0xb8ec24: StoreField: r1->field_7 = r0
    //     0xb8ec24: stur            w0, [x1, #7]
    // 0xb8ec28: r0 = Instance_BoxShape
    //     0xb8ec28: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb8ec2c: ldr             x0, [x0, #0x970]
    // 0xb8ec30: StoreField: r1->field_23 = r0
    //     0xb8ec30: stur            w0, [x1, #0x23]
    // 0xb8ec34: ldur            x0, [fp, #-8]
    // 0xb8ec38: cmp             w0, NULL
    // 0xb8ec3c: b.ne            #0xb8ec48
    // 0xb8ec40: r0 = Null
    //     0xb8ec40: mov             x0, NULL
    // 0xb8ec44: b               #0xb8ec84
    // 0xb8ec48: LoadField: r2 = r0->field_7
    //     0xb8ec48: ldur            w2, [x0, #7]
    // 0xb8ec4c: DecompressPointer r2
    //     0xb8ec4c: add             x2, x2, HEAP, lsl #32
    // 0xb8ec50: cmp             w2, NULL
    // 0xb8ec54: b.ne            #0xb8ec60
    // 0xb8ec58: r0 = Null
    //     0xb8ec58: mov             x0, NULL
    // 0xb8ec5c: b               #0xb8ec84
    // 0xb8ec60: stp             xzr, x2, [SP]
    // 0xb8ec64: r0 = []()
    //     0xb8ec64: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xb8ec68: r1 = LoadClassIdInstr(r0)
    //     0xb8ec68: ldur            x1, [x0, #-1]
    //     0xb8ec6c: ubfx            x1, x1, #0xc, #0x14
    // 0xb8ec70: str             x0, [SP]
    // 0xb8ec74: mov             x0, x1
    // 0xb8ec78: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb8ec78: sub             lr, x0, #1, lsl #12
    //     0xb8ec7c: ldr             lr, [x21, lr, lsl #3]
    //     0xb8ec80: blr             lr
    // 0xb8ec84: cmp             w0, NULL
    // 0xb8ec88: b.ne            #0xb8ec94
    // 0xb8ec8c: r3 = ""
    //     0xb8ec8c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8ec90: b               #0xb8ec98
    // 0xb8ec94: mov             x3, x0
    // 0xb8ec98: ldur            x2, [fp, #-0x10]
    // 0xb8ec9c: ldur            x0, [fp, #-8]
    // 0xb8eca0: stur            x3, [fp, #-0x18]
    // 0xb8eca4: LoadField: r1 = r2->field_f
    //     0xb8eca4: ldur            w1, [x2, #0xf]
    // 0xb8eca8: DecompressPointer r1
    //     0xb8eca8: add             x1, x1, HEAP, lsl #32
    // 0xb8ecac: r0 = of()
    //     0xb8ecac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8ecb0: LoadField: r1 = r0->field_87
    //     0xb8ecb0: ldur            w1, [x0, #0x87]
    // 0xb8ecb4: DecompressPointer r1
    //     0xb8ecb4: add             x1, x1, HEAP, lsl #32
    // 0xb8ecb8: LoadField: r0 = r1->field_7
    //     0xb8ecb8: ldur            w0, [x1, #7]
    // 0xb8ecbc: DecompressPointer r0
    //     0xb8ecbc: add             x0, x0, HEAP, lsl #32
    // 0xb8ecc0: stur            x0, [fp, #-0x30]
    // 0xb8ecc4: r1 = Instance_Color
    //     0xb8ecc4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8ecc8: d0 = 0.500000
    //     0xb8ecc8: fmov            d0, #0.50000000
    // 0xb8eccc: r0 = withOpacity()
    //     0xb8eccc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb8ecd0: r16 = 16.000000
    //     0xb8ecd0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb8ecd4: ldr             x16, [x16, #0x188]
    // 0xb8ecd8: stp             x0, x16, [SP]
    // 0xb8ecdc: ldur            x1, [fp, #-0x30]
    // 0xb8ece0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8ece0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb8ece4: ldr             x4, [x4, #0xaa0]
    // 0xb8ece8: r0 = copyWith()
    //     0xb8ece8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8ecec: stur            x0, [fp, #-0x30]
    // 0xb8ecf0: r0 = Text()
    //     0xb8ecf0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8ecf4: mov             x1, x0
    // 0xb8ecf8: ldur            x0, [fp, #-0x18]
    // 0xb8ecfc: stur            x1, [fp, #-0x38]
    // 0xb8ed00: StoreField: r1->field_b = r0
    //     0xb8ed00: stur            w0, [x1, #0xb]
    // 0xb8ed04: ldur            x0, [fp, #-0x30]
    // 0xb8ed08: StoreField: r1->field_13 = r0
    //     0xb8ed08: stur            w0, [x1, #0x13]
    // 0xb8ed0c: r0 = Center()
    //     0xb8ed0c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb8ed10: mov             x1, x0
    // 0xb8ed14: r0 = Instance_Alignment
    //     0xb8ed14: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb8ed18: ldr             x0, [x0, #0xb10]
    // 0xb8ed1c: stur            x1, [fp, #-0x18]
    // 0xb8ed20: StoreField: r1->field_f = r0
    //     0xb8ed20: stur            w0, [x1, #0xf]
    // 0xb8ed24: ldur            x0, [fp, #-0x38]
    // 0xb8ed28: StoreField: r1->field_b = r0
    //     0xb8ed28: stur            w0, [x1, #0xb]
    // 0xb8ed2c: r0 = Container()
    //     0xb8ed2c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8ed30: stur            x0, [fp, #-0x30]
    // 0xb8ed34: r16 = 34.000000
    //     0xb8ed34: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb8ed38: ldr             x16, [x16, #0x978]
    // 0xb8ed3c: r30 = 34.000000
    //     0xb8ed3c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb8ed40: ldr             lr, [lr, #0x978]
    // 0xb8ed44: stp             lr, x16, [SP, #0x18]
    // 0xb8ed48: r16 = Instance_EdgeInsets
    //     0xb8ed48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb8ed4c: ldr             x16, [x16, #0x980]
    // 0xb8ed50: ldur            lr, [fp, #-0x28]
    // 0xb8ed54: stp             lr, x16, [SP, #8]
    // 0xb8ed58: ldur            x16, [fp, #-0x18]
    // 0xb8ed5c: str             x16, [SP]
    // 0xb8ed60: mov             x1, x0
    // 0xb8ed64: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xb8ed64: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xb8ed68: ldr             x4, [x4, #0x988]
    // 0xb8ed6c: r0 = Container()
    //     0xb8ed6c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8ed70: ldur            x0, [fp, #-8]
    // 0xb8ed74: cmp             w0, NULL
    // 0xb8ed78: b.ne            #0xb8ed84
    // 0xb8ed7c: r1 = Null
    //     0xb8ed7c: mov             x1, NULL
    // 0xb8ed80: b               #0xb8ed8c
    // 0xb8ed84: LoadField: r1 = r0->field_7
    //     0xb8ed84: ldur            w1, [x0, #7]
    // 0xb8ed88: DecompressPointer r1
    //     0xb8ed88: add             x1, x1, HEAP, lsl #32
    // 0xb8ed8c: cmp             w1, NULL
    // 0xb8ed90: b.ne            #0xb8ed9c
    // 0xb8ed94: r3 = ""
    //     0xb8ed94: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8ed98: b               #0xb8eda0
    // 0xb8ed9c: mov             x3, x1
    // 0xb8eda0: ldur            x2, [fp, #-0x10]
    // 0xb8eda4: stur            x3, [fp, #-0x18]
    // 0xb8eda8: LoadField: r1 = r2->field_f
    //     0xb8eda8: ldur            w1, [x2, #0xf]
    // 0xb8edac: DecompressPointer r1
    //     0xb8edac: add             x1, x1, HEAP, lsl #32
    // 0xb8edb0: r0 = of()
    //     0xb8edb0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8edb4: LoadField: r1 = r0->field_87
    //     0xb8edb4: ldur            w1, [x0, #0x87]
    // 0xb8edb8: DecompressPointer r1
    //     0xb8edb8: add             x1, x1, HEAP, lsl #32
    // 0xb8edbc: LoadField: r0 = r1->field_7
    //     0xb8edbc: ldur            w0, [x1, #7]
    // 0xb8edc0: DecompressPointer r0
    //     0xb8edc0: add             x0, x0, HEAP, lsl #32
    // 0xb8edc4: r16 = 14.000000
    //     0xb8edc4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb8edc8: ldr             x16, [x16, #0x1d8]
    // 0xb8edcc: r30 = Instance_Color
    //     0xb8edcc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8edd0: stp             lr, x16, [SP]
    // 0xb8edd4: mov             x1, x0
    // 0xb8edd8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8edd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb8eddc: ldr             x4, [x4, #0xaa0]
    // 0xb8ede0: r0 = copyWith()
    //     0xb8ede0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8ede4: stur            x0, [fp, #-0x28]
    // 0xb8ede8: r0 = Text()
    //     0xb8ede8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8edec: mov             x2, x0
    // 0xb8edf0: ldur            x0, [fp, #-0x18]
    // 0xb8edf4: stur            x2, [fp, #-0x38]
    // 0xb8edf8: StoreField: r2->field_b = r0
    //     0xb8edf8: stur            w0, [x2, #0xb]
    // 0xb8edfc: ldur            x0, [fp, #-0x28]
    // 0xb8ee00: StoreField: r2->field_13 = r0
    //     0xb8ee00: stur            w0, [x2, #0x13]
    // 0xb8ee04: ldur            x0, [fp, #-8]
    // 0xb8ee08: cmp             w0, NULL
    // 0xb8ee0c: b.ne            #0xb8ee18
    // 0xb8ee10: r1 = Null
    //     0xb8ee10: mov             x1, NULL
    // 0xb8ee14: b               #0xb8ee20
    // 0xb8ee18: LoadField: r1 = r0->field_1f
    //     0xb8ee18: ldur            w1, [x0, #0x1f]
    // 0xb8ee1c: DecompressPointer r1
    //     0xb8ee1c: add             x1, x1, HEAP, lsl #32
    // 0xb8ee20: cmp             w1, NULL
    // 0xb8ee24: b.ne            #0xb8ee30
    // 0xb8ee28: r4 = ""
    //     0xb8ee28: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8ee2c: b               #0xb8ee34
    // 0xb8ee30: mov             x4, x1
    // 0xb8ee34: ldur            x3, [fp, #-0x10]
    // 0xb8ee38: stur            x4, [fp, #-0x18]
    // 0xb8ee3c: LoadField: r1 = r3->field_f
    //     0xb8ee3c: ldur            w1, [x3, #0xf]
    // 0xb8ee40: DecompressPointer r1
    //     0xb8ee40: add             x1, x1, HEAP, lsl #32
    // 0xb8ee44: r0 = of()
    //     0xb8ee44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8ee48: LoadField: r1 = r0->field_87
    //     0xb8ee48: ldur            w1, [x0, #0x87]
    // 0xb8ee4c: DecompressPointer r1
    //     0xb8ee4c: add             x1, x1, HEAP, lsl #32
    // 0xb8ee50: LoadField: r0 = r1->field_33
    //     0xb8ee50: ldur            w0, [x1, #0x33]
    // 0xb8ee54: DecompressPointer r0
    //     0xb8ee54: add             x0, x0, HEAP, lsl #32
    // 0xb8ee58: stur            x0, [fp, #-0x28]
    // 0xb8ee5c: cmp             w0, NULL
    // 0xb8ee60: b.ne            #0xb8ee6c
    // 0xb8ee64: r4 = Null
    //     0xb8ee64: mov             x4, NULL
    // 0xb8ee68: b               #0xb8ee94
    // 0xb8ee6c: r1 = Instance_Color
    //     0xb8ee6c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8ee70: d0 = 0.500000
    //     0xb8ee70: fmov            d0, #0.50000000
    // 0xb8ee74: r0 = withOpacity()
    //     0xb8ee74: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb8ee78: r16 = 10.000000
    //     0xb8ee78: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb8ee7c: stp             x0, x16, [SP]
    // 0xb8ee80: ldur            x1, [fp, #-0x28]
    // 0xb8ee84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8ee84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb8ee88: ldr             x4, [x4, #0xaa0]
    // 0xb8ee8c: r0 = copyWith()
    //     0xb8ee8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8ee90: mov             x4, x0
    // 0xb8ee94: ldur            x1, [fp, #-8]
    // 0xb8ee98: ldur            x3, [fp, #-0x30]
    // 0xb8ee9c: ldur            x0, [fp, #-0x38]
    // 0xb8eea0: ldur            x2, [fp, #-0x18]
    // 0xb8eea4: stur            x4, [fp, #-0x28]
    // 0xb8eea8: r0 = Text()
    //     0xb8eea8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8eeac: mov             x1, x0
    // 0xb8eeb0: ldur            x0, [fp, #-0x18]
    // 0xb8eeb4: stur            x1, [fp, #-0x40]
    // 0xb8eeb8: StoreField: r1->field_b = r0
    //     0xb8eeb8: stur            w0, [x1, #0xb]
    // 0xb8eebc: ldur            x0, [fp, #-0x28]
    // 0xb8eec0: StoreField: r1->field_13 = r0
    //     0xb8eec0: stur            w0, [x1, #0x13]
    // 0xb8eec4: r0 = Padding()
    //     0xb8eec4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8eec8: mov             x3, x0
    // 0xb8eecc: r0 = Instance_EdgeInsets
    //     0xb8eecc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xb8eed0: ldr             x0, [x0, #0x990]
    // 0xb8eed4: stur            x3, [fp, #-0x18]
    // 0xb8eed8: StoreField: r3->field_f = r0
    //     0xb8eed8: stur            w0, [x3, #0xf]
    // 0xb8eedc: ldur            x0, [fp, #-0x40]
    // 0xb8eee0: StoreField: r3->field_b = r0
    //     0xb8eee0: stur            w0, [x3, #0xb]
    // 0xb8eee4: r1 = Null
    //     0xb8eee4: mov             x1, NULL
    // 0xb8eee8: r2 = 4
    //     0xb8eee8: movz            x2, #0x4
    // 0xb8eeec: r0 = AllocateArray()
    //     0xb8eeec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8eef0: mov             x2, x0
    // 0xb8eef4: ldur            x0, [fp, #-0x38]
    // 0xb8eef8: stur            x2, [fp, #-0x28]
    // 0xb8eefc: StoreField: r2->field_f = r0
    //     0xb8eefc: stur            w0, [x2, #0xf]
    // 0xb8ef00: ldur            x0, [fp, #-0x18]
    // 0xb8ef04: StoreField: r2->field_13 = r0
    //     0xb8ef04: stur            w0, [x2, #0x13]
    // 0xb8ef08: r1 = <Widget>
    //     0xb8ef08: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8ef0c: r0 = AllocateGrowableArray()
    //     0xb8ef0c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8ef10: mov             x1, x0
    // 0xb8ef14: ldur            x0, [fp, #-0x28]
    // 0xb8ef18: stur            x1, [fp, #-0x18]
    // 0xb8ef1c: StoreField: r1->field_f = r0
    //     0xb8ef1c: stur            w0, [x1, #0xf]
    // 0xb8ef20: r2 = 4
    //     0xb8ef20: movz            x2, #0x4
    // 0xb8ef24: StoreField: r1->field_b = r2
    //     0xb8ef24: stur            w2, [x1, #0xb]
    // 0xb8ef28: r0 = Column()
    //     0xb8ef28: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8ef2c: mov             x3, x0
    // 0xb8ef30: r0 = Instance_Axis
    //     0xb8ef30: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb8ef34: stur            x3, [fp, #-0x28]
    // 0xb8ef38: StoreField: r3->field_f = r0
    //     0xb8ef38: stur            w0, [x3, #0xf]
    // 0xb8ef3c: r4 = Instance_MainAxisAlignment
    //     0xb8ef3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8ef40: ldr             x4, [x4, #0xa08]
    // 0xb8ef44: StoreField: r3->field_13 = r4
    //     0xb8ef44: stur            w4, [x3, #0x13]
    // 0xb8ef48: r5 = Instance_MainAxisSize
    //     0xb8ef48: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8ef4c: ldr             x5, [x5, #0xa10]
    // 0xb8ef50: ArrayStore: r3[0] = r5  ; List_4
    //     0xb8ef50: stur            w5, [x3, #0x17]
    // 0xb8ef54: r6 = Instance_CrossAxisAlignment
    //     0xb8ef54: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb8ef58: ldr             x6, [x6, #0x890]
    // 0xb8ef5c: StoreField: r3->field_1b = r6
    //     0xb8ef5c: stur            w6, [x3, #0x1b]
    // 0xb8ef60: r7 = Instance_VerticalDirection
    //     0xb8ef60: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8ef64: ldr             x7, [x7, #0xa20]
    // 0xb8ef68: StoreField: r3->field_23 = r7
    //     0xb8ef68: stur            w7, [x3, #0x23]
    // 0xb8ef6c: r8 = Instance_Clip
    //     0xb8ef6c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8ef70: ldr             x8, [x8, #0x38]
    // 0xb8ef74: StoreField: r3->field_2b = r8
    //     0xb8ef74: stur            w8, [x3, #0x2b]
    // 0xb8ef78: StoreField: r3->field_2f = rZR
    //     0xb8ef78: stur            xzr, [x3, #0x2f]
    // 0xb8ef7c: ldur            x1, [fp, #-0x18]
    // 0xb8ef80: StoreField: r3->field_b = r1
    //     0xb8ef80: stur            w1, [x3, #0xb]
    // 0xb8ef84: r1 = Null
    //     0xb8ef84: mov             x1, NULL
    // 0xb8ef88: r2 = 6
    //     0xb8ef88: movz            x2, #0x6
    // 0xb8ef8c: r0 = AllocateArray()
    //     0xb8ef8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8ef90: mov             x2, x0
    // 0xb8ef94: ldur            x0, [fp, #-0x30]
    // 0xb8ef98: stur            x2, [fp, #-0x18]
    // 0xb8ef9c: StoreField: r2->field_f = r0
    //     0xb8ef9c: stur            w0, [x2, #0xf]
    // 0xb8efa0: r16 = Instance_SizedBox
    //     0xb8efa0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb8efa4: ldr             x16, [x16, #0x998]
    // 0xb8efa8: StoreField: r2->field_13 = r16
    //     0xb8efa8: stur            w16, [x2, #0x13]
    // 0xb8efac: ldur            x0, [fp, #-0x28]
    // 0xb8efb0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8efb0: stur            w0, [x2, #0x17]
    // 0xb8efb4: r1 = <Widget>
    //     0xb8efb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8efb8: r0 = AllocateGrowableArray()
    //     0xb8efb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8efbc: mov             x1, x0
    // 0xb8efc0: ldur            x0, [fp, #-0x18]
    // 0xb8efc4: stur            x1, [fp, #-0x28]
    // 0xb8efc8: StoreField: r1->field_f = r0
    //     0xb8efc8: stur            w0, [x1, #0xf]
    // 0xb8efcc: r2 = 6
    //     0xb8efcc: movz            x2, #0x6
    // 0xb8efd0: StoreField: r1->field_b = r2
    //     0xb8efd0: stur            w2, [x1, #0xb]
    // 0xb8efd4: r0 = Row()
    //     0xb8efd4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb8efd8: mov             x2, x0
    // 0xb8efdc: r0 = Instance_Axis
    //     0xb8efdc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb8efe0: stur            x2, [fp, #-0x18]
    // 0xb8efe4: StoreField: r2->field_f = r0
    //     0xb8efe4: stur            w0, [x2, #0xf]
    // 0xb8efe8: r3 = Instance_MainAxisAlignment
    //     0xb8efe8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8efec: ldr             x3, [x3, #0xa08]
    // 0xb8eff0: StoreField: r2->field_13 = r3
    //     0xb8eff0: stur            w3, [x2, #0x13]
    // 0xb8eff4: r4 = Instance_MainAxisSize
    //     0xb8eff4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8eff8: ldr             x4, [x4, #0xa10]
    // 0xb8effc: ArrayStore: r2[0] = r4  ; List_4
    //     0xb8effc: stur            w4, [x2, #0x17]
    // 0xb8f000: r5 = Instance_CrossAxisAlignment
    //     0xb8f000: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb8f004: ldr             x5, [x5, #0xa18]
    // 0xb8f008: StoreField: r2->field_1b = r5
    //     0xb8f008: stur            w5, [x2, #0x1b]
    // 0xb8f00c: r6 = Instance_VerticalDirection
    //     0xb8f00c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8f010: ldr             x6, [x6, #0xa20]
    // 0xb8f014: StoreField: r2->field_23 = r6
    //     0xb8f014: stur            w6, [x2, #0x23]
    // 0xb8f018: r7 = Instance_Clip
    //     0xb8f018: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8f01c: ldr             x7, [x7, #0x38]
    // 0xb8f020: StoreField: r2->field_2b = r7
    //     0xb8f020: stur            w7, [x2, #0x2b]
    // 0xb8f024: StoreField: r2->field_2f = rZR
    //     0xb8f024: stur            xzr, [x2, #0x2f]
    // 0xb8f028: ldur            x1, [fp, #-0x28]
    // 0xb8f02c: StoreField: r2->field_b = r1
    //     0xb8f02c: stur            w1, [x2, #0xb]
    // 0xb8f030: ldur            x8, [fp, #-8]
    // 0xb8f034: cmp             w8, NULL
    // 0xb8f038: b.eq            #0xb8f04c
    // 0xb8f03c: LoadField: r1 = r8->field_f
    //     0xb8f03c: ldur            w1, [x8, #0xf]
    // 0xb8f040: DecompressPointer r1
    //     0xb8f040: add             x1, x1, HEAP, lsl #32
    // 0xb8f044: cmp             w1, #0xa
    // 0xb8f048: b.eq            #0xb8f064
    // 0xb8f04c: cmp             w8, NULL
    // 0xb8f050: b.eq            #0xb8f074
    // 0xb8f054: LoadField: r1 = r8->field_f
    //     0xb8f054: ldur            w1, [x8, #0xf]
    // 0xb8f058: DecompressPointer r1
    //     0xb8f058: add             x1, x1, HEAP, lsl #32
    // 0xb8f05c: cmp             w1, #8
    // 0xb8f060: b.ne            #0xb8f074
    // 0xb8f064: mov             x0, x8
    // 0xb8f068: r1 = Instance_Color
    //     0xb8f068: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb8f06c: ldr             x1, [x1, #0x858]
    // 0xb8f070: b               #0xb8f0e4
    // 0xb8f074: cmp             w8, NULL
    // 0xb8f078: b.ne            #0xb8f084
    // 0xb8f07c: mov             x0, x8
    // 0xb8f080: b               #0xb8f0b8
    // 0xb8f084: LoadField: r1 = r8->field_f
    //     0xb8f084: ldur            w1, [x8, #0xf]
    // 0xb8f088: DecompressPointer r1
    //     0xb8f088: add             x1, x1, HEAP, lsl #32
    // 0xb8f08c: cmp             w1, #6
    // 0xb8f090: b.ne            #0xb8f0b4
    // 0xb8f094: r1 = Instance_Color
    //     0xb8f094: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb8f098: ldr             x1, [x1, #0x858]
    // 0xb8f09c: d0 = 0.700000
    //     0xb8f09c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb8f0a0: ldr             d0, [x17, #0xf48]
    // 0xb8f0a4: r0 = withOpacity()
    //     0xb8f0a4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb8f0a8: mov             x1, x0
    // 0xb8f0ac: ldur            x0, [fp, #-8]
    // 0xb8f0b0: b               #0xb8f0e4
    // 0xb8f0b4: ldur            x0, [fp, #-8]
    // 0xb8f0b8: cmp             w0, NULL
    // 0xb8f0bc: b.eq            #0xb8f0dc
    // 0xb8f0c0: LoadField: r1 = r0->field_f
    //     0xb8f0c0: ldur            w1, [x0, #0xf]
    // 0xb8f0c4: DecompressPointer r1
    //     0xb8f0c4: add             x1, x1, HEAP, lsl #32
    // 0xb8f0c8: cmp             w1, #4
    // 0xb8f0cc: b.ne            #0xb8f0dc
    // 0xb8f0d0: r1 = Instance_Color
    //     0xb8f0d0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb8f0d4: ldr             x1, [x1, #0x860]
    // 0xb8f0d8: b               #0xb8f0e4
    // 0xb8f0dc: r1 = Instance_Color
    //     0xb8f0dc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb8f0e0: ldr             x1, [x1, #0x50]
    // 0xb8f0e4: stur            x1, [fp, #-0x28]
    // 0xb8f0e8: r0 = ColorFilter()
    //     0xb8f0e8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb8f0ec: mov             x1, x0
    // 0xb8f0f0: ldur            x0, [fp, #-0x28]
    // 0xb8f0f4: stur            x1, [fp, #-0x30]
    // 0xb8f0f8: StoreField: r1->field_7 = r0
    //     0xb8f0f8: stur            w0, [x1, #7]
    // 0xb8f0fc: r0 = Instance_BlendMode
    //     0xb8f0fc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb8f100: ldr             x0, [x0, #0xb30]
    // 0xb8f104: StoreField: r1->field_b = r0
    //     0xb8f104: stur            w0, [x1, #0xb]
    // 0xb8f108: r0 = 1
    //     0xb8f108: movz            x0, #0x1
    // 0xb8f10c: StoreField: r1->field_13 = r0
    //     0xb8f10c: stur            x0, [x1, #0x13]
    // 0xb8f110: r0 = SvgPicture()
    //     0xb8f110: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb8f114: stur            x0, [fp, #-0x28]
    // 0xb8f118: ldur            x16, [fp, #-0x30]
    // 0xb8f11c: str             x16, [SP]
    // 0xb8f120: mov             x1, x0
    // 0xb8f124: r2 = "assets/images/green_star.svg"
    //     0xb8f124: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb8f128: ldr             x2, [x2, #0x9a0]
    // 0xb8f12c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb8f12c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb8f130: ldr             x4, [x4, #0xa38]
    // 0xb8f134: r0 = SvgPicture.asset()
    //     0xb8f134: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb8f138: ldur            x1, [fp, #-8]
    // 0xb8f13c: cmp             w1, NULL
    // 0xb8f140: b.ne            #0xb8f14c
    // 0xb8f144: r0 = Null
    //     0xb8f144: mov             x0, NULL
    // 0xb8f148: b               #0xb8f180
    // 0xb8f14c: LoadField: r0 = r1->field_f
    //     0xb8f14c: ldur            w0, [x1, #0xf]
    // 0xb8f150: DecompressPointer r0
    //     0xb8f150: add             x0, x0, HEAP, lsl #32
    // 0xb8f154: r2 = 60
    //     0xb8f154: movz            x2, #0x3c
    // 0xb8f158: branchIfSmi(r0, 0xb8f164)
    //     0xb8f158: tbz             w0, #0, #0xb8f164
    // 0xb8f15c: r2 = LoadClassIdInstr(r0)
    //     0xb8f15c: ldur            x2, [x0, #-1]
    //     0xb8f160: ubfx            x2, x2, #0xc, #0x14
    // 0xb8f164: str             x0, [SP]
    // 0xb8f168: mov             x0, x2
    // 0xb8f16c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb8f16c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb8f170: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb8f170: movz            x17, #0x2700
    //     0xb8f174: add             lr, x0, x17
    //     0xb8f178: ldr             lr, [x21, lr, lsl #3]
    //     0xb8f17c: blr             lr
    // 0xb8f180: cmp             w0, NULL
    // 0xb8f184: b.ne            #0xb8f190
    // 0xb8f188: r5 = ""
    //     0xb8f188: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8f18c: b               #0xb8f194
    // 0xb8f190: mov             x5, x0
    // 0xb8f194: ldur            x4, [fp, #-0x10]
    // 0xb8f198: ldur            x0, [fp, #-8]
    // 0xb8f19c: ldur            x3, [fp, #-0x18]
    // 0xb8f1a0: ldur            x2, [fp, #-0x28]
    // 0xb8f1a4: stur            x5, [fp, #-0x30]
    // 0xb8f1a8: LoadField: r1 = r4->field_f
    //     0xb8f1a8: ldur            w1, [x4, #0xf]
    // 0xb8f1ac: DecompressPointer r1
    //     0xb8f1ac: add             x1, x1, HEAP, lsl #32
    // 0xb8f1b0: r0 = of()
    //     0xb8f1b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8f1b4: LoadField: r1 = r0->field_87
    //     0xb8f1b4: ldur            w1, [x0, #0x87]
    // 0xb8f1b8: DecompressPointer r1
    //     0xb8f1b8: add             x1, x1, HEAP, lsl #32
    // 0xb8f1bc: LoadField: r0 = r1->field_7
    //     0xb8f1bc: ldur            w0, [x1, #7]
    // 0xb8f1c0: DecompressPointer r0
    //     0xb8f1c0: add             x0, x0, HEAP, lsl #32
    // 0xb8f1c4: r16 = 12.000000
    //     0xb8f1c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb8f1c8: ldr             x16, [x16, #0x9e8]
    // 0xb8f1cc: r30 = Instance_Color
    //     0xb8f1cc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8f1d0: stp             lr, x16, [SP]
    // 0xb8f1d4: mov             x1, x0
    // 0xb8f1d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8f1d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb8f1dc: ldr             x4, [x4, #0xaa0]
    // 0xb8f1e0: r0 = copyWith()
    //     0xb8f1e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8f1e4: stur            x0, [fp, #-0x38]
    // 0xb8f1e8: r0 = Text()
    //     0xb8f1e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8f1ec: mov             x3, x0
    // 0xb8f1f0: ldur            x0, [fp, #-0x30]
    // 0xb8f1f4: stur            x3, [fp, #-0x40]
    // 0xb8f1f8: StoreField: r3->field_b = r0
    //     0xb8f1f8: stur            w0, [x3, #0xb]
    // 0xb8f1fc: ldur            x0, [fp, #-0x38]
    // 0xb8f200: StoreField: r3->field_13 = r0
    //     0xb8f200: stur            w0, [x3, #0x13]
    // 0xb8f204: r1 = Null
    //     0xb8f204: mov             x1, NULL
    // 0xb8f208: r2 = 6
    //     0xb8f208: movz            x2, #0x6
    // 0xb8f20c: r0 = AllocateArray()
    //     0xb8f20c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8f210: mov             x2, x0
    // 0xb8f214: ldur            x0, [fp, #-0x28]
    // 0xb8f218: stur            x2, [fp, #-0x30]
    // 0xb8f21c: StoreField: r2->field_f = r0
    //     0xb8f21c: stur            w0, [x2, #0xf]
    // 0xb8f220: r16 = Instance_SizedBox
    //     0xb8f220: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9a8] Obj!SizedBox@d67ea1
    //     0xb8f224: ldr             x16, [x16, #0x9a8]
    // 0xb8f228: StoreField: r2->field_13 = r16
    //     0xb8f228: stur            w16, [x2, #0x13]
    // 0xb8f22c: ldur            x0, [fp, #-0x40]
    // 0xb8f230: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8f230: stur            w0, [x2, #0x17]
    // 0xb8f234: r1 = <Widget>
    //     0xb8f234: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8f238: r0 = AllocateGrowableArray()
    //     0xb8f238: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8f23c: mov             x1, x0
    // 0xb8f240: ldur            x0, [fp, #-0x30]
    // 0xb8f244: stur            x1, [fp, #-0x28]
    // 0xb8f248: StoreField: r1->field_f = r0
    //     0xb8f248: stur            w0, [x1, #0xf]
    // 0xb8f24c: r0 = 6
    //     0xb8f24c: movz            x0, #0x6
    // 0xb8f250: StoreField: r1->field_b = r0
    //     0xb8f250: stur            w0, [x1, #0xb]
    // 0xb8f254: r0 = Row()
    //     0xb8f254: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb8f258: mov             x3, x0
    // 0xb8f25c: r0 = Instance_Axis
    //     0xb8f25c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb8f260: stur            x3, [fp, #-0x30]
    // 0xb8f264: StoreField: r3->field_f = r0
    //     0xb8f264: stur            w0, [x3, #0xf]
    // 0xb8f268: r4 = Instance_MainAxisAlignment
    //     0xb8f268: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8f26c: ldr             x4, [x4, #0xa08]
    // 0xb8f270: StoreField: r3->field_13 = r4
    //     0xb8f270: stur            w4, [x3, #0x13]
    // 0xb8f274: r5 = Instance_MainAxisSize
    //     0xb8f274: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8f278: ldr             x5, [x5, #0xa10]
    // 0xb8f27c: ArrayStore: r3[0] = r5  ; List_4
    //     0xb8f27c: stur            w5, [x3, #0x17]
    // 0xb8f280: r6 = Instance_CrossAxisAlignment
    //     0xb8f280: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb8f284: ldr             x6, [x6, #0xa18]
    // 0xb8f288: StoreField: r3->field_1b = r6
    //     0xb8f288: stur            w6, [x3, #0x1b]
    // 0xb8f28c: r7 = Instance_VerticalDirection
    //     0xb8f28c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8f290: ldr             x7, [x7, #0xa20]
    // 0xb8f294: StoreField: r3->field_23 = r7
    //     0xb8f294: stur            w7, [x3, #0x23]
    // 0xb8f298: r8 = Instance_Clip
    //     0xb8f298: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8f29c: ldr             x8, [x8, #0x38]
    // 0xb8f2a0: StoreField: r3->field_2b = r8
    //     0xb8f2a0: stur            w8, [x3, #0x2b]
    // 0xb8f2a4: StoreField: r3->field_2f = rZR
    //     0xb8f2a4: stur            xzr, [x3, #0x2f]
    // 0xb8f2a8: ldur            x1, [fp, #-0x28]
    // 0xb8f2ac: StoreField: r3->field_b = r1
    //     0xb8f2ac: stur            w1, [x3, #0xb]
    // 0xb8f2b0: r1 = Null
    //     0xb8f2b0: mov             x1, NULL
    // 0xb8f2b4: r2 = 4
    //     0xb8f2b4: movz            x2, #0x4
    // 0xb8f2b8: r0 = AllocateArray()
    //     0xb8f2b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8f2bc: mov             x2, x0
    // 0xb8f2c0: ldur            x0, [fp, #-0x18]
    // 0xb8f2c4: stur            x2, [fp, #-0x28]
    // 0xb8f2c8: StoreField: r2->field_f = r0
    //     0xb8f2c8: stur            w0, [x2, #0xf]
    // 0xb8f2cc: ldur            x0, [fp, #-0x30]
    // 0xb8f2d0: StoreField: r2->field_13 = r0
    //     0xb8f2d0: stur            w0, [x2, #0x13]
    // 0xb8f2d4: r1 = <Widget>
    //     0xb8f2d4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8f2d8: r0 = AllocateGrowableArray()
    //     0xb8f2d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8f2dc: mov             x1, x0
    // 0xb8f2e0: ldur            x0, [fp, #-0x28]
    // 0xb8f2e4: stur            x1, [fp, #-0x18]
    // 0xb8f2e8: StoreField: r1->field_f = r0
    //     0xb8f2e8: stur            w0, [x1, #0xf]
    // 0xb8f2ec: r2 = 4
    //     0xb8f2ec: movz            x2, #0x4
    // 0xb8f2f0: StoreField: r1->field_b = r2
    //     0xb8f2f0: stur            w2, [x1, #0xb]
    // 0xb8f2f4: r0 = Row()
    //     0xb8f2f4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb8f2f8: mov             x3, x0
    // 0xb8f2fc: r0 = Instance_Axis
    //     0xb8f2fc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb8f300: stur            x3, [fp, #-0x28]
    // 0xb8f304: StoreField: r3->field_f = r0
    //     0xb8f304: stur            w0, [x3, #0xf]
    // 0xb8f308: r0 = Instance_MainAxisAlignment
    //     0xb8f308: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb8f30c: ldr             x0, [x0, #0xa8]
    // 0xb8f310: StoreField: r3->field_13 = r0
    //     0xb8f310: stur            w0, [x3, #0x13]
    // 0xb8f314: r0 = Instance_MainAxisSize
    //     0xb8f314: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8f318: ldr             x0, [x0, #0xa10]
    // 0xb8f31c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb8f31c: stur            w0, [x3, #0x17]
    // 0xb8f320: r1 = Instance_CrossAxisAlignment
    //     0xb8f320: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb8f324: ldr             x1, [x1, #0xa18]
    // 0xb8f328: StoreField: r3->field_1b = r1
    //     0xb8f328: stur            w1, [x3, #0x1b]
    // 0xb8f32c: r4 = Instance_VerticalDirection
    //     0xb8f32c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8f330: ldr             x4, [x4, #0xa20]
    // 0xb8f334: StoreField: r3->field_23 = r4
    //     0xb8f334: stur            w4, [x3, #0x23]
    // 0xb8f338: r5 = Instance_Clip
    //     0xb8f338: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8f33c: ldr             x5, [x5, #0x38]
    // 0xb8f340: StoreField: r3->field_2b = r5
    //     0xb8f340: stur            w5, [x3, #0x2b]
    // 0xb8f344: StoreField: r3->field_2f = rZR
    //     0xb8f344: stur            xzr, [x3, #0x2f]
    // 0xb8f348: ldur            x1, [fp, #-0x18]
    // 0xb8f34c: StoreField: r3->field_b = r1
    //     0xb8f34c: stur            w1, [x3, #0xb]
    // 0xb8f350: r1 = Null
    //     0xb8f350: mov             x1, NULL
    // 0xb8f354: r2 = 2
    //     0xb8f354: movz            x2, #0x2
    // 0xb8f358: r0 = AllocateArray()
    //     0xb8f358: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8f35c: mov             x2, x0
    // 0xb8f360: ldur            x0, [fp, #-0x28]
    // 0xb8f364: stur            x2, [fp, #-0x18]
    // 0xb8f368: StoreField: r2->field_f = r0
    //     0xb8f368: stur            w0, [x2, #0xf]
    // 0xb8f36c: r1 = <Widget>
    //     0xb8f36c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8f370: r0 = AllocateGrowableArray()
    //     0xb8f370: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8f374: mov             x2, x0
    // 0xb8f378: ldur            x0, [fp, #-0x18]
    // 0xb8f37c: stur            x2, [fp, #-0x28]
    // 0xb8f380: StoreField: r2->field_f = r0
    //     0xb8f380: stur            w0, [x2, #0xf]
    // 0xb8f384: r0 = 2
    //     0xb8f384: movz            x0, #0x2
    // 0xb8f388: StoreField: r2->field_b = r0
    //     0xb8f388: stur            w0, [x2, #0xb]
    // 0xb8f38c: ldur            x0, [fp, #-8]
    // 0xb8f390: cmp             w0, NULL
    // 0xb8f394: b.ne            #0xb8f3a0
    // 0xb8f398: r1 = Null
    //     0xb8f398: mov             x1, NULL
    // 0xb8f39c: b               #0xb8f3cc
    // 0xb8f3a0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb8f3a0: ldur            w1, [x0, #0x17]
    // 0xb8f3a4: DecompressPointer r1
    //     0xb8f3a4: add             x1, x1, HEAP, lsl #32
    // 0xb8f3a8: cmp             w1, NULL
    // 0xb8f3ac: b.ne            #0xb8f3b8
    // 0xb8f3b0: r1 = Null
    //     0xb8f3b0: mov             x1, NULL
    // 0xb8f3b4: b               #0xb8f3cc
    // 0xb8f3b8: LoadField: r3 = r1->field_7
    //     0xb8f3b8: ldur            w3, [x1, #7]
    // 0xb8f3bc: cbnz            w3, #0xb8f3c8
    // 0xb8f3c0: r1 = false
    //     0xb8f3c0: add             x1, NULL, #0x30  ; false
    // 0xb8f3c4: b               #0xb8f3cc
    // 0xb8f3c8: r1 = true
    //     0xb8f3c8: add             x1, NULL, #0x20  ; true
    // 0xb8f3cc: cmp             w1, NULL
    // 0xb8f3d0: b.ne            #0xb8f3dc
    // 0xb8f3d4: mov             x3, x2
    // 0xb8f3d8: b               #0xb8f614
    // 0xb8f3dc: tbnz            w1, #4, #0xb8f610
    // 0xb8f3e0: cmp             w0, NULL
    // 0xb8f3e4: b.ne            #0xb8f3f0
    // 0xb8f3e8: r0 = Null
    //     0xb8f3e8: mov             x0, NULL
    // 0xb8f3ec: b               #0xb8f40c
    // 0xb8f3f0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb8f3f0: ldur            w1, [x0, #0x17]
    // 0xb8f3f4: DecompressPointer r1
    //     0xb8f3f4: add             x1, x1, HEAP, lsl #32
    // 0xb8f3f8: cmp             w1, NULL
    // 0xb8f3fc: b.ne            #0xb8f408
    // 0xb8f400: r0 = Null
    //     0xb8f400: mov             x0, NULL
    // 0xb8f404: b               #0xb8f40c
    // 0xb8f408: r0 = trim()
    //     0xb8f408: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb8f40c: cmp             w0, NULL
    // 0xb8f410: b.ne            #0xb8f41c
    // 0xb8f414: r3 = ""
    //     0xb8f414: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8f418: b               #0xb8f420
    // 0xb8f41c: mov             x3, x0
    // 0xb8f420: ldur            x2, [fp, #-0x10]
    // 0xb8f424: ldur            x0, [fp, #-0x28]
    // 0xb8f428: stur            x3, [fp, #-0x18]
    // 0xb8f42c: LoadField: r1 = r2->field_f
    //     0xb8f42c: ldur            w1, [x2, #0xf]
    // 0xb8f430: DecompressPointer r1
    //     0xb8f430: add             x1, x1, HEAP, lsl #32
    // 0xb8f434: r0 = of()
    //     0xb8f434: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8f438: LoadField: r1 = r0->field_87
    //     0xb8f438: ldur            w1, [x0, #0x87]
    // 0xb8f43c: DecompressPointer r1
    //     0xb8f43c: add             x1, x1, HEAP, lsl #32
    // 0xb8f440: LoadField: r0 = r1->field_2b
    //     0xb8f440: ldur            w0, [x1, #0x2b]
    // 0xb8f444: DecompressPointer r0
    //     0xb8f444: add             x0, x0, HEAP, lsl #32
    // 0xb8f448: LoadField: r1 = r0->field_13
    //     0xb8f448: ldur            w1, [x0, #0x13]
    // 0xb8f44c: DecompressPointer r1
    //     0xb8f44c: add             x1, x1, HEAP, lsl #32
    // 0xb8f450: r16 = Instance_Color
    //     0xb8f450: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8f454: stp             x16, x1, [SP]
    // 0xb8f458: r1 = Instance_TextStyle
    //     0xb8f458: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9b0] Obj!TextStyle@d62871
    //     0xb8f45c: ldr             x1, [x1, #0x9b0]
    // 0xb8f460: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0xb8f460: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0xb8f464: ldr             x4, [x4, #0x9b8]
    // 0xb8f468: r0 = copyWith()
    //     0xb8f468: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8f46c: ldur            x2, [fp, #-0x10]
    // 0xb8f470: stur            x0, [fp, #-0x30]
    // 0xb8f474: LoadField: r1 = r2->field_f
    //     0xb8f474: ldur            w1, [x2, #0xf]
    // 0xb8f478: DecompressPointer r1
    //     0xb8f478: add             x1, x1, HEAP, lsl #32
    // 0xb8f47c: r0 = of()
    //     0xb8f47c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8f480: LoadField: r1 = r0->field_87
    //     0xb8f480: ldur            w1, [x0, #0x87]
    // 0xb8f484: DecompressPointer r1
    //     0xb8f484: add             x1, x1, HEAP, lsl #32
    // 0xb8f488: LoadField: r0 = r1->field_7
    //     0xb8f488: ldur            w0, [x1, #7]
    // 0xb8f48c: DecompressPointer r0
    //     0xb8f48c: add             x0, x0, HEAP, lsl #32
    // 0xb8f490: r16 = Instance_Color
    //     0xb8f490: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8f494: r30 = 12.000000
    //     0xb8f494: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb8f498: ldr             lr, [lr, #0x9e8]
    // 0xb8f49c: stp             lr, x16, [SP]
    // 0xb8f4a0: mov             x1, x0
    // 0xb8f4a4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb8f4a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb8f4a8: ldr             x4, [x4, #0x9b8]
    // 0xb8f4ac: r0 = copyWith()
    //     0xb8f4ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8f4b0: ldur            x2, [fp, #-0x10]
    // 0xb8f4b4: stur            x0, [fp, #-0x38]
    // 0xb8f4b8: LoadField: r1 = r2->field_f
    //     0xb8f4b8: ldur            w1, [x2, #0xf]
    // 0xb8f4bc: DecompressPointer r1
    //     0xb8f4bc: add             x1, x1, HEAP, lsl #32
    // 0xb8f4c0: r0 = of()
    //     0xb8f4c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8f4c4: LoadField: r1 = r0->field_87
    //     0xb8f4c4: ldur            w1, [x0, #0x87]
    // 0xb8f4c8: DecompressPointer r1
    //     0xb8f4c8: add             x1, x1, HEAP, lsl #32
    // 0xb8f4cc: LoadField: r0 = r1->field_7
    //     0xb8f4cc: ldur            w0, [x1, #7]
    // 0xb8f4d0: DecompressPointer r0
    //     0xb8f4d0: add             x0, x0, HEAP, lsl #32
    // 0xb8f4d4: r16 = Instance_Color
    //     0xb8f4d4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8f4d8: r30 = 12.000000
    //     0xb8f4d8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb8f4dc: ldr             lr, [lr, #0x9e8]
    // 0xb8f4e0: stp             lr, x16, [SP]
    // 0xb8f4e4: mov             x1, x0
    // 0xb8f4e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb8f4e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb8f4ec: ldr             x4, [x4, #0x9b8]
    // 0xb8f4f0: r0 = copyWith()
    //     0xb8f4f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8f4f4: stur            x0, [fp, #-0x40]
    // 0xb8f4f8: r0 = ReadMoreText()
    //     0xb8f4f8: bl              #0x99f824  ; AllocateReadMoreTextStub -> ReadMoreText (size=0x6c)
    // 0xb8f4fc: mov             x1, x0
    // 0xb8f500: ldur            x0, [fp, #-0x18]
    // 0xb8f504: stur            x1, [fp, #-0x48]
    // 0xb8f508: StoreField: r1->field_3f = r0
    //     0xb8f508: stur            w0, [x1, #0x3f]
    // 0xb8f50c: r0 = " Read Less"
    //     0xb8f50c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c0] " Read Less"
    //     0xb8f510: ldr             x0, [x0, #0x9c0]
    // 0xb8f514: StoreField: r1->field_43 = r0
    //     0xb8f514: stur            w0, [x1, #0x43]
    // 0xb8f518: r0 = "Read More"
    //     0xb8f518: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c8] "Read More"
    //     0xb8f51c: ldr             x0, [x0, #0x9c8]
    // 0xb8f520: StoreField: r1->field_47 = r0
    //     0xb8f520: stur            w0, [x1, #0x47]
    // 0xb8f524: r0 = 240
    //     0xb8f524: movz            x0, #0xf0
    // 0xb8f528: StoreField: r1->field_f = r0
    //     0xb8f528: stur            x0, [x1, #0xf]
    // 0xb8f52c: r0 = 2
    //     0xb8f52c: movz            x0, #0x2
    // 0xb8f530: ArrayStore: r1[0] = r0  ; List_8
    //     0xb8f530: stur            x0, [x1, #0x17]
    // 0xb8f534: r0 = Instance_TrimMode
    //     0xb8f534: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] Obj!TrimMode@d70201
    //     0xb8f538: ldr             x0, [x0, #0x9d0]
    // 0xb8f53c: StoreField: r1->field_1f = r0
    //     0xb8f53c: stur            w0, [x1, #0x1f]
    // 0xb8f540: ldur            x0, [fp, #-0x30]
    // 0xb8f544: StoreField: r1->field_4f = r0
    //     0xb8f544: stur            w0, [x1, #0x4f]
    // 0xb8f548: r0 = Instance_TextAlign
    //     0xb8f548: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb8f54c: StoreField: r1->field_53 = r0
    //     0xb8f54c: stur            w0, [x1, #0x53]
    // 0xb8f550: ldur            x0, [fp, #-0x38]
    // 0xb8f554: StoreField: r1->field_23 = r0
    //     0xb8f554: stur            w0, [x1, #0x23]
    // 0xb8f558: ldur            x0, [fp, #-0x40]
    // 0xb8f55c: StoreField: r1->field_27 = r0
    //     0xb8f55c: stur            w0, [x1, #0x27]
    // 0xb8f560: r0 = "… "
    //     0xb8f560: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "… "
    //     0xb8f564: ldr             x0, [x0, #0x9d8]
    // 0xb8f568: StoreField: r1->field_3b = r0
    //     0xb8f568: stur            w0, [x1, #0x3b]
    // 0xb8f56c: r0 = true
    //     0xb8f56c: add             x0, NULL, #0x20  ; true
    // 0xb8f570: StoreField: r1->field_37 = r0
    //     0xb8f570: stur            w0, [x1, #0x37]
    // 0xb8f574: r0 = Padding()
    //     0xb8f574: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8f578: mov             x2, x0
    // 0xb8f57c: r0 = Instance_EdgeInsets
    //     0xb8f57c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9e0] Obj!EdgeInsets@d577a1
    //     0xb8f580: ldr             x0, [x0, #0x9e0]
    // 0xb8f584: stur            x2, [fp, #-0x18]
    // 0xb8f588: StoreField: r2->field_f = r0
    //     0xb8f588: stur            w0, [x2, #0xf]
    // 0xb8f58c: ldur            x0, [fp, #-0x48]
    // 0xb8f590: StoreField: r2->field_b = r0
    //     0xb8f590: stur            w0, [x2, #0xb]
    // 0xb8f594: ldur            x0, [fp, #-0x28]
    // 0xb8f598: LoadField: r1 = r0->field_b
    //     0xb8f598: ldur            w1, [x0, #0xb]
    // 0xb8f59c: LoadField: r3 = r0->field_f
    //     0xb8f59c: ldur            w3, [x0, #0xf]
    // 0xb8f5a0: DecompressPointer r3
    //     0xb8f5a0: add             x3, x3, HEAP, lsl #32
    // 0xb8f5a4: LoadField: r4 = r3->field_b
    //     0xb8f5a4: ldur            w4, [x3, #0xb]
    // 0xb8f5a8: r3 = LoadInt32Instr(r1)
    //     0xb8f5a8: sbfx            x3, x1, #1, #0x1f
    // 0xb8f5ac: stur            x3, [fp, #-0x50]
    // 0xb8f5b0: r1 = LoadInt32Instr(r4)
    //     0xb8f5b0: sbfx            x1, x4, #1, #0x1f
    // 0xb8f5b4: cmp             x3, x1
    // 0xb8f5b8: b.ne            #0xb8f5c4
    // 0xb8f5bc: mov             x1, x0
    // 0xb8f5c0: r0 = _growToNextCapacity()
    //     0xb8f5c0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8f5c4: ldur            x3, [fp, #-0x28]
    // 0xb8f5c8: ldur            x2, [fp, #-0x50]
    // 0xb8f5cc: add             x0, x2, #1
    // 0xb8f5d0: lsl             x1, x0, #1
    // 0xb8f5d4: StoreField: r3->field_b = r1
    //     0xb8f5d4: stur            w1, [x3, #0xb]
    // 0xb8f5d8: LoadField: r1 = r3->field_f
    //     0xb8f5d8: ldur            w1, [x3, #0xf]
    // 0xb8f5dc: DecompressPointer r1
    //     0xb8f5dc: add             x1, x1, HEAP, lsl #32
    // 0xb8f5e0: ldur            x0, [fp, #-0x18]
    // 0xb8f5e4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb8f5e4: add             x25, x1, x2, lsl #2
    //     0xb8f5e8: add             x25, x25, #0xf
    //     0xb8f5ec: str             w0, [x25]
    //     0xb8f5f0: tbz             w0, #0, #0xb8f60c
    //     0xb8f5f4: ldurb           w16, [x1, #-1]
    //     0xb8f5f8: ldurb           w17, [x0, #-1]
    //     0xb8f5fc: and             x16, x17, x16, lsr #2
    //     0xb8f600: tst             x16, HEAP, lsr #32
    //     0xb8f604: b.eq            #0xb8f60c
    //     0xb8f608: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb8f60c: b               #0xb8f614
    // 0xb8f610: mov             x3, x2
    // 0xb8f614: ldur            x0, [fp, #-8]
    // 0xb8f618: cmp             w0, NULL
    // 0xb8f61c: b.ne            #0xb8f628
    // 0xb8f620: r1 = Null
    //     0xb8f620: mov             x1, NULL
    // 0xb8f624: b               #0xb8f638
    // 0xb8f628: LoadField: r1 = r0->field_1b
    //     0xb8f628: ldur            w1, [x0, #0x1b]
    // 0xb8f62c: DecompressPointer r1
    //     0xb8f62c: add             x1, x1, HEAP, lsl #32
    // 0xb8f630: LoadField: r2 = r1->field_b
    //     0xb8f630: ldur            w2, [x1, #0xb]
    // 0xb8f634: mov             x1, x2
    // 0xb8f638: cmp             w1, NULL
    // 0xb8f63c: b.eq            #0xb8fa10
    // 0xb8f640: r2 = LoadInt32Instr(r1)
    //     0xb8f640: sbfx            x2, x1, #1, #0x1f
    // 0xb8f644: cmp             x2, #1
    // 0xb8f648: r16 = true
    //     0xb8f648: add             x16, NULL, #0x20  ; true
    // 0xb8f64c: r17 = false
    //     0xb8f64c: add             x17, NULL, #0x30  ; false
    // 0xb8f650: csel            x4, x16, x17, ge
    // 0xb8f654: stur            x4, [fp, #-0x18]
    // 0xb8f658: cmp             w0, NULL
    // 0xb8f65c: b.ne            #0xb8f668
    // 0xb8f660: r1 = Null
    //     0xb8f660: mov             x1, NULL
    // 0xb8f664: b               #0xb8f684
    // 0xb8f668: LoadField: r1 = r0->field_1b
    //     0xb8f668: ldur            w1, [x0, #0x1b]
    // 0xb8f66c: DecompressPointer r1
    //     0xb8f66c: add             x1, x1, HEAP, lsl #32
    // 0xb8f670: LoadField: r2 = r1->field_b
    //     0xb8f670: ldur            w2, [x1, #0xb]
    // 0xb8f674: cbz             w2, #0xb8f680
    // 0xb8f678: r1 = false
    //     0xb8f678: add             x1, NULL, #0x30  ; false
    // 0xb8f67c: b               #0xb8f684
    // 0xb8f680: r1 = true
    //     0xb8f680: add             x1, NULL, #0x20  ; true
    // 0xb8f684: cmp             w1, NULL
    // 0xb8f688: b.eq            #0xb8f698
    // 0xb8f68c: tbnz            w1, #4, #0xb8f698
    // 0xb8f690: d0 = 0.000000
    //     0xb8f690: eor             v0.16b, v0.16b, v0.16b
    // 0xb8f694: b               #0xb8f69c
    // 0xb8f698: d0 = 48.000000
    //     0xb8f698: ldr             d0, [PP, #0x6d18]  ; [pp+0x6d18] IMM: double(48) from 0x4048000000000000
    // 0xb8f69c: stur            d0, [fp, #-0x58]
    // 0xb8f6a0: cmp             w0, NULL
    // 0xb8f6a4: b.ne            #0xb8f6b0
    // 0xb8f6a8: r0 = Null
    //     0xb8f6a8: mov             x0, NULL
    // 0xb8f6ac: b               #0xb8f6bc
    // 0xb8f6b0: LoadField: r1 = r0->field_1b
    //     0xb8f6b0: ldur            w1, [x0, #0x1b]
    // 0xb8f6b4: DecompressPointer r1
    //     0xb8f6b4: add             x1, x1, HEAP, lsl #32
    // 0xb8f6b8: LoadField: r0 = r1->field_b
    //     0xb8f6b8: ldur            w0, [x1, #0xb]
    // 0xb8f6bc: cmp             w0, NULL
    // 0xb8f6c0: b.ne            #0xb8f6cc
    // 0xb8f6c4: r0 = 0
    //     0xb8f6c4: movz            x0, #0
    // 0xb8f6c8: b               #0xb8f6d4
    // 0xb8f6cc: r1 = LoadInt32Instr(r0)
    //     0xb8f6cc: sbfx            x1, x0, #1, #0x1f
    // 0xb8f6d0: mov             x0, x1
    // 0xb8f6d4: stur            x0, [fp, #-0x50]
    // 0xb8f6d8: r1 = Function '<anonymous closure>':.
    //     0xb8f6d8: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a6f0] AnonymousClosure: (0x9b3480), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb8f6dc: ldr             x1, [x1, #0x6f0]
    // 0xb8f6e0: r2 = Null
    //     0xb8f6e0: mov             x2, NULL
    // 0xb8f6e4: r0 = AllocateClosure()
    //     0xb8f6e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8f6e8: ldur            x2, [fp, #-0x10]
    // 0xb8f6ec: r1 = Function '<anonymous closure>':.
    //     0xb8f6ec: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a6f8] AnonymousClosure: (0xb904dc), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xb8e998)
    //     0xb8f6f0: ldr             x1, [x1, #0x6f8]
    // 0xb8f6f4: stur            x0, [fp, #-8]
    // 0xb8f6f8: r0 = AllocateClosure()
    //     0xb8f6f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8f6fc: stur            x0, [fp, #-0x30]
    // 0xb8f700: r0 = ListView()
    //     0xb8f700: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb8f704: stur            x0, [fp, #-0x38]
    // 0xb8f708: r16 = true
    //     0xb8f708: add             x16, NULL, #0x20  ; true
    // 0xb8f70c: r30 = Instance_Axis
    //     0xb8f70c: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb8f710: stp             lr, x16, [SP, #8]
    // 0xb8f714: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb8f714: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb8f718: ldr             x16, [x16, #0x1c8]
    // 0xb8f71c: str             x16, [SP]
    // 0xb8f720: mov             x1, x0
    // 0xb8f724: ldur            x2, [fp, #-0x30]
    // 0xb8f728: ldur            x3, [fp, #-0x50]
    // 0xb8f72c: ldur            x5, [fp, #-8]
    // 0xb8f730: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x6, scrollDirection, 0x5, shrinkWrap, 0x4, null]
    //     0xb8f730: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a0f8] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x6, "scrollDirection", 0x5, "shrinkWrap", 0x4, Null]
    //     0xb8f734: ldr             x4, [x4, #0xf8]
    // 0xb8f738: r0 = ListView.separated()
    //     0xb8f738: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb8f73c: r0 = SizedBox()
    //     0xb8f73c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb8f740: mov             x1, x0
    // 0xb8f744: r0 = inf
    //     0xb8f744: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb8f748: ldr             x0, [x0, #0x9f8]
    // 0xb8f74c: stur            x1, [fp, #-8]
    // 0xb8f750: StoreField: r1->field_f = r0
    //     0xb8f750: stur            w0, [x1, #0xf]
    // 0xb8f754: ldur            d0, [fp, #-0x58]
    // 0xb8f758: r0 = inline_Allocate_Double()
    //     0xb8f758: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb8f75c: add             x0, x0, #0x10
    //     0xb8f760: cmp             x2, x0
    //     0xb8f764: b.ls            #0xb8fa14
    //     0xb8f768: str             x0, [THR, #0x50]  ; THR::top
    //     0xb8f76c: sub             x0, x0, #0xf
    //     0xb8f770: movz            x2, #0xe15c
    //     0xb8f774: movk            x2, #0x3, lsl #16
    //     0xb8f778: stur            x2, [x0, #-1]
    // 0xb8f77c: StoreField: r0->field_7 = d0
    //     0xb8f77c: stur            d0, [x0, #7]
    // 0xb8f780: StoreField: r1->field_13 = r0
    //     0xb8f780: stur            w0, [x1, #0x13]
    // 0xb8f784: ldur            x0, [fp, #-0x38]
    // 0xb8f788: StoreField: r1->field_b = r0
    //     0xb8f788: stur            w0, [x1, #0xb]
    // 0xb8f78c: r0 = Padding()
    //     0xb8f78c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8f790: mov             x1, x0
    // 0xb8f794: r0 = Instance_EdgeInsets
    //     0xb8f794: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb8f798: ldr             x0, [x0, #0xa00]
    // 0xb8f79c: stur            x1, [fp, #-0x30]
    // 0xb8f7a0: StoreField: r1->field_f = r0
    //     0xb8f7a0: stur            w0, [x1, #0xf]
    // 0xb8f7a4: ldur            x0, [fp, #-8]
    // 0xb8f7a8: StoreField: r1->field_b = r0
    //     0xb8f7a8: stur            w0, [x1, #0xb]
    // 0xb8f7ac: r0 = Visibility()
    //     0xb8f7ac: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb8f7b0: mov             x1, x0
    // 0xb8f7b4: ldur            x0, [fp, #-0x30]
    // 0xb8f7b8: stur            x1, [fp, #-8]
    // 0xb8f7bc: StoreField: r1->field_b = r0
    //     0xb8f7bc: stur            w0, [x1, #0xb]
    // 0xb8f7c0: r0 = Instance_SizedBox
    //     0xb8f7c0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb8f7c4: StoreField: r1->field_f = r0
    //     0xb8f7c4: stur            w0, [x1, #0xf]
    // 0xb8f7c8: ldur            x0, [fp, #-0x18]
    // 0xb8f7cc: StoreField: r1->field_13 = r0
    //     0xb8f7cc: stur            w0, [x1, #0x13]
    // 0xb8f7d0: r0 = false
    //     0xb8f7d0: add             x0, NULL, #0x30  ; false
    // 0xb8f7d4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8f7d4: stur            w0, [x1, #0x17]
    // 0xb8f7d8: StoreField: r1->field_1b = r0
    //     0xb8f7d8: stur            w0, [x1, #0x1b]
    // 0xb8f7dc: StoreField: r1->field_1f = r0
    //     0xb8f7dc: stur            w0, [x1, #0x1f]
    // 0xb8f7e0: StoreField: r1->field_23 = r0
    //     0xb8f7e0: stur            w0, [x1, #0x23]
    // 0xb8f7e4: StoreField: r1->field_27 = r0
    //     0xb8f7e4: stur            w0, [x1, #0x27]
    // 0xb8f7e8: StoreField: r1->field_2b = r0
    //     0xb8f7e8: stur            w0, [x1, #0x2b]
    // 0xb8f7ec: r0 = GestureDetector()
    //     0xb8f7ec: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb8f7f0: ldur            x2, [fp, #-0x10]
    // 0xb8f7f4: r1 = Function '<anonymous closure>':.
    //     0xb8f7f4: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a700] AnonymousClosure: (0xa90680), in [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xc0c210)
    //     0xb8f7f8: ldr             x1, [x1, #0x700]
    // 0xb8f7fc: stur            x0, [fp, #-0x18]
    // 0xb8f800: r0 = AllocateClosure()
    //     0xb8f800: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8f804: ldur            x2, [fp, #-0x10]
    // 0xb8f808: r1 = Function '<anonymous closure>':.
    //     0xb8f808: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a708] AnonymousClosure: (0xb8fa2c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xb8e998)
    //     0xb8f80c: ldr             x1, [x1, #0x708]
    // 0xb8f810: stur            x0, [fp, #-0x10]
    // 0xb8f814: r0 = AllocateClosure()
    //     0xb8f814: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8f818: ldur            x16, [fp, #-0x10]
    // 0xb8f81c: stp             x0, x16, [SP, #8]
    // 0xb8f820: r16 = Instance_Icon
    //     0xb8f820: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa18] Obj!Icon@d65e71
    //     0xb8f824: ldr             x16, [x16, #0xa18]
    // 0xb8f828: str             x16, [SP]
    // 0xb8f82c: ldur            x1, [fp, #-0x18]
    // 0xb8f830: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, onTap, 0x2, onTapDown, 0x1, null]
    //     0xb8f830: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa20] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "onTap", 0x2, "onTapDown", 0x1, Null]
    //     0xb8f834: ldr             x4, [x4, #0xa20]
    // 0xb8f838: r0 = GestureDetector()
    //     0xb8f838: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb8f83c: r0 = Align()
    //     0xb8f83c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb8f840: mov             x3, x0
    // 0xb8f844: r0 = Instance_Alignment
    //     0xb8f844: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0xb8f848: ldr             x0, [x0, #0xa28]
    // 0xb8f84c: stur            x3, [fp, #-0x10]
    // 0xb8f850: StoreField: r3->field_f = r0
    //     0xb8f850: stur            w0, [x3, #0xf]
    // 0xb8f854: ldur            x1, [fp, #-0x18]
    // 0xb8f858: StoreField: r3->field_b = r1
    //     0xb8f858: stur            w1, [x3, #0xb]
    // 0xb8f85c: r1 = Null
    //     0xb8f85c: mov             x1, NULL
    // 0xb8f860: r2 = 4
    //     0xb8f860: movz            x2, #0x4
    // 0xb8f864: r0 = AllocateArray()
    //     0xb8f864: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8f868: mov             x2, x0
    // 0xb8f86c: ldur            x0, [fp, #-8]
    // 0xb8f870: stur            x2, [fp, #-0x18]
    // 0xb8f874: StoreField: r2->field_f = r0
    //     0xb8f874: stur            w0, [x2, #0xf]
    // 0xb8f878: ldur            x0, [fp, #-0x10]
    // 0xb8f87c: StoreField: r2->field_13 = r0
    //     0xb8f87c: stur            w0, [x2, #0x13]
    // 0xb8f880: r1 = <Widget>
    //     0xb8f880: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8f884: r0 = AllocateGrowableArray()
    //     0xb8f884: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8f888: mov             x1, x0
    // 0xb8f88c: ldur            x0, [fp, #-0x18]
    // 0xb8f890: stur            x1, [fp, #-8]
    // 0xb8f894: StoreField: r1->field_f = r0
    //     0xb8f894: stur            w0, [x1, #0xf]
    // 0xb8f898: r0 = 4
    //     0xb8f898: movz            x0, #0x4
    // 0xb8f89c: StoreField: r1->field_b = r0
    //     0xb8f89c: stur            w0, [x1, #0xb]
    // 0xb8f8a0: r0 = Stack()
    //     0xb8f8a0: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb8f8a4: mov             x2, x0
    // 0xb8f8a8: r0 = Instance_Alignment
    //     0xb8f8a8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0xb8f8ac: ldr             x0, [x0, #0xa28]
    // 0xb8f8b0: stur            x2, [fp, #-0x10]
    // 0xb8f8b4: StoreField: r2->field_f = r0
    //     0xb8f8b4: stur            w0, [x2, #0xf]
    // 0xb8f8b8: r0 = Instance_StackFit
    //     0xb8f8b8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb8f8bc: ldr             x0, [x0, #0xfa8]
    // 0xb8f8c0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8f8c0: stur            w0, [x2, #0x17]
    // 0xb8f8c4: r0 = Instance_Clip
    //     0xb8f8c4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb8f8c8: ldr             x0, [x0, #0x7e0]
    // 0xb8f8cc: StoreField: r2->field_1b = r0
    //     0xb8f8cc: stur            w0, [x2, #0x1b]
    // 0xb8f8d0: ldur            x0, [fp, #-8]
    // 0xb8f8d4: StoreField: r2->field_b = r0
    //     0xb8f8d4: stur            w0, [x2, #0xb]
    // 0xb8f8d8: ldur            x0, [fp, #-0x28]
    // 0xb8f8dc: LoadField: r1 = r0->field_b
    //     0xb8f8dc: ldur            w1, [x0, #0xb]
    // 0xb8f8e0: LoadField: r3 = r0->field_f
    //     0xb8f8e0: ldur            w3, [x0, #0xf]
    // 0xb8f8e4: DecompressPointer r3
    //     0xb8f8e4: add             x3, x3, HEAP, lsl #32
    // 0xb8f8e8: LoadField: r4 = r3->field_b
    //     0xb8f8e8: ldur            w4, [x3, #0xb]
    // 0xb8f8ec: r3 = LoadInt32Instr(r1)
    //     0xb8f8ec: sbfx            x3, x1, #1, #0x1f
    // 0xb8f8f0: stur            x3, [fp, #-0x50]
    // 0xb8f8f4: r1 = LoadInt32Instr(r4)
    //     0xb8f8f4: sbfx            x1, x4, #1, #0x1f
    // 0xb8f8f8: cmp             x3, x1
    // 0xb8f8fc: b.ne            #0xb8f908
    // 0xb8f900: mov             x1, x0
    // 0xb8f904: r0 = _growToNextCapacity()
    //     0xb8f904: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8f908: ldur            x2, [fp, #-0x28]
    // 0xb8f90c: ldur            x3, [fp, #-0x50]
    // 0xb8f910: add             x0, x3, #1
    // 0xb8f914: lsl             x1, x0, #1
    // 0xb8f918: StoreField: r2->field_b = r1
    //     0xb8f918: stur            w1, [x2, #0xb]
    // 0xb8f91c: LoadField: r1 = r2->field_f
    //     0xb8f91c: ldur            w1, [x2, #0xf]
    // 0xb8f920: DecompressPointer r1
    //     0xb8f920: add             x1, x1, HEAP, lsl #32
    // 0xb8f924: ldur            x0, [fp, #-0x10]
    // 0xb8f928: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb8f928: add             x25, x1, x3, lsl #2
    //     0xb8f92c: add             x25, x25, #0xf
    //     0xb8f930: str             w0, [x25]
    //     0xb8f934: tbz             w0, #0, #0xb8f950
    //     0xb8f938: ldurb           w16, [x1, #-1]
    //     0xb8f93c: ldurb           w17, [x0, #-1]
    //     0xb8f940: and             x16, x17, x16, lsr #2
    //     0xb8f944: tst             x16, HEAP, lsr #32
    //     0xb8f948: b.eq            #0xb8f950
    //     0xb8f94c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb8f950: r0 = Column()
    //     0xb8f950: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8f954: mov             x1, x0
    // 0xb8f958: r0 = Instance_Axis
    //     0xb8f958: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb8f95c: stur            x1, [fp, #-8]
    // 0xb8f960: StoreField: r1->field_f = r0
    //     0xb8f960: stur            w0, [x1, #0xf]
    // 0xb8f964: r0 = Instance_MainAxisAlignment
    //     0xb8f964: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8f968: ldr             x0, [x0, #0xa08]
    // 0xb8f96c: StoreField: r1->field_13 = r0
    //     0xb8f96c: stur            w0, [x1, #0x13]
    // 0xb8f970: r0 = Instance_MainAxisSize
    //     0xb8f970: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8f974: ldr             x0, [x0, #0xa10]
    // 0xb8f978: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8f978: stur            w0, [x1, #0x17]
    // 0xb8f97c: r0 = Instance_CrossAxisAlignment
    //     0xb8f97c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb8f980: ldr             x0, [x0, #0x890]
    // 0xb8f984: StoreField: r1->field_1b = r0
    //     0xb8f984: stur            w0, [x1, #0x1b]
    // 0xb8f988: r0 = Instance_VerticalDirection
    //     0xb8f988: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8f98c: ldr             x0, [x0, #0xa20]
    // 0xb8f990: StoreField: r1->field_23 = r0
    //     0xb8f990: stur            w0, [x1, #0x23]
    // 0xb8f994: r0 = Instance_Clip
    //     0xb8f994: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8f998: ldr             x0, [x0, #0x38]
    // 0xb8f99c: StoreField: r1->field_2b = r0
    //     0xb8f99c: stur            w0, [x1, #0x2b]
    // 0xb8f9a0: StoreField: r1->field_2f = rZR
    //     0xb8f9a0: stur            xzr, [x1, #0x2f]
    // 0xb8f9a4: ldur            x0, [fp, #-0x28]
    // 0xb8f9a8: StoreField: r1->field_b = r0
    //     0xb8f9a8: stur            w0, [x1, #0xb]
    // 0xb8f9ac: r0 = Padding()
    //     0xb8f9ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8f9b0: mov             x1, x0
    // 0xb8f9b4: r0 = Instance_EdgeInsets
    //     0xb8f9b4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb8f9b8: ldr             x0, [x0, #0x1f0]
    // 0xb8f9bc: stur            x1, [fp, #-0x10]
    // 0xb8f9c0: StoreField: r1->field_f = r0
    //     0xb8f9c0: stur            w0, [x1, #0xf]
    // 0xb8f9c4: ldur            x0, [fp, #-8]
    // 0xb8f9c8: StoreField: r1->field_b = r0
    //     0xb8f9c8: stur            w0, [x1, #0xb]
    // 0xb8f9cc: r0 = Container()
    //     0xb8f9cc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8f9d0: stur            x0, [fp, #-8]
    // 0xb8f9d4: ldur            x16, [fp, #-0x20]
    // 0xb8f9d8: ldur            lr, [fp, #-0x10]
    // 0xb8f9dc: stp             lr, x16, [SP]
    // 0xb8f9e0: mov             x1, x0
    // 0xb8f9e4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb8f9e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb8f9e8: ldr             x4, [x4, #0x88]
    // 0xb8f9ec: r0 = Container()
    //     0xb8f9ec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8f9f0: ldur            x0, [fp, #-8]
    // 0xb8f9f4: LeaveFrame
    //     0xb8f9f4: mov             SP, fp
    //     0xb8f9f8: ldp             fp, lr, [SP], #0x10
    // 0xb8f9fc: ret
    //     0xb8f9fc: ret             
    // 0xb8fa00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8fa00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8fa04: b               #0xb8ead0
    // 0xb8fa08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8fa08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8fa0c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb8fa0c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb8fa10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8fa10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8fa14: SaveReg d0
    //     0xb8fa14: str             q0, [SP, #-0x10]!
    // 0xb8fa18: SaveReg r1
    //     0xb8fa18: str             x1, [SP, #-8]!
    // 0xb8fa1c: r0 = AllocateDouble()
    //     0xb8fa1c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb8fa20: RestoreReg r1
    //     0xb8fa20: ldr             x1, [SP], #8
    // 0xb8fa24: RestoreReg d0
    //     0xb8fa24: ldr             q0, [SP], #0x10
    // 0xb8fa28: b               #0xb8f77c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8fa2c, size: 0x104
    // 0xb8fa2c: EnterFrame
    //     0xb8fa2c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8fa30: mov             fp, SP
    // 0xb8fa34: AllocStack(0x28)
    //     0xb8fa34: sub             SP, SP, #0x28
    // 0xb8fa38: SetupParameters()
    //     0xb8fa38: ldr             x0, [fp, #0x10]
    //     0xb8fa3c: ldur            w1, [x0, #0x17]
    //     0xb8fa40: add             x1, x1, HEAP, lsl #32
    //     0xb8fa44: stur            x1, [fp, #-0x10]
    // 0xb8fa48: CheckStackOverflow
    //     0xb8fa48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8fa4c: cmp             SP, x16
    //     0xb8fa50: b.ls            #0xb8fb24
    // 0xb8fa54: LoadField: r0 = r1->field_b
    //     0xb8fa54: ldur            w0, [x1, #0xb]
    // 0xb8fa58: DecompressPointer r0
    //     0xb8fa58: add             x0, x0, HEAP, lsl #32
    // 0xb8fa5c: stur            x0, [fp, #-8]
    // 0xb8fa60: LoadField: r2 = r0->field_f
    //     0xb8fa60: ldur            w2, [x0, #0xf]
    // 0xb8fa64: DecompressPointer r2
    //     0xb8fa64: add             x2, x2, HEAP, lsl #32
    // 0xb8fa68: LoadField: r3 = r2->field_13
    //     0xb8fa68: ldur            w3, [x2, #0x13]
    // 0xb8fa6c: DecompressPointer r3
    //     0xb8fa6c: add             x3, x3, HEAP, lsl #32
    // 0xb8fa70: cmp             w3, NULL
    // 0xb8fa74: b.eq            #0xb8fb14
    // 0xb8fa78: LoadField: r3 = r2->field_b
    //     0xb8fa78: ldur            w3, [x2, #0xb]
    // 0xb8fa7c: DecompressPointer r3
    //     0xb8fa7c: add             x3, x3, HEAP, lsl #32
    // 0xb8fa80: cmp             w3, NULL
    // 0xb8fa84: b.eq            #0xb8fb2c
    // 0xb8fa88: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xb8fa88: ldur            w2, [x3, #0x17]
    // 0xb8fa8c: DecompressPointer r2
    //     0xb8fa8c: add             x2, x2, HEAP, lsl #32
    // 0xb8fa90: r16 = "flag_dots"
    //     0xb8fa90: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa30] "flag_dots"
    //     0xb8fa94: ldr             x16, [x16, #0xa30]
    // 0xb8fa98: stp             x16, x2, [SP, #8]
    // 0xb8fa9c: r16 = ""
    //     0xb8fa9c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8faa0: str             x16, [SP]
    // 0xb8faa4: r4 = 0
    //     0xb8faa4: movz            x4, #0
    // 0xb8faa8: ldr             x0, [SP, #0x10]
    // 0xb8faac: r16 = UnlinkedCall_0x613b5c
    //     0xb8faac: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a710] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8fab0: add             x16, x16, #0x710
    // 0xb8fab4: ldp             x5, lr, [x16]
    // 0xb8fab8: blr             lr
    // 0xb8fabc: ldur            x0, [fp, #-8]
    // 0xb8fac0: LoadField: r1 = r0->field_f
    //     0xb8fac0: ldur            w1, [x0, #0xf]
    // 0xb8fac4: DecompressPointer r1
    //     0xb8fac4: add             x1, x1, HEAP, lsl #32
    // 0xb8fac8: ldur            x0, [fp, #-0x10]
    // 0xb8facc: LoadField: r2 = r0->field_f
    //     0xb8facc: ldur            w2, [x0, #0xf]
    // 0xb8fad0: DecompressPointer r2
    //     0xb8fad0: add             x2, x2, HEAP, lsl #32
    // 0xb8fad4: LoadField: r3 = r1->field_13
    //     0xb8fad4: ldur            w3, [x1, #0x13]
    // 0xb8fad8: DecompressPointer r3
    //     0xb8fad8: add             x3, x3, HEAP, lsl #32
    // 0xb8fadc: LoadField: r4 = r0->field_13
    //     0xb8fadc: ldur            w4, [x0, #0x13]
    // 0xb8fae0: DecompressPointer r4
    //     0xb8fae0: add             x4, x4, HEAP, lsl #32
    // 0xb8fae4: cmp             w4, NULL
    // 0xb8fae8: b.ne            #0xb8faf4
    // 0xb8faec: r0 = Null
    //     0xb8faec: mov             x0, NULL
    // 0xb8faf0: b               #0xb8fafc
    // 0xb8faf4: LoadField: r0 = r4->field_b
    //     0xb8faf4: ldur            w0, [x4, #0xb]
    // 0xb8faf8: DecompressPointer r0
    //     0xb8faf8: add             x0, x0, HEAP, lsl #32
    // 0xb8fafc: cmp             w0, NULL
    // 0xb8fb00: b.ne            #0xb8fb0c
    // 0xb8fb04: r5 = ""
    //     0xb8fb04: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8fb08: b               #0xb8fb10
    // 0xb8fb0c: mov             x5, x0
    // 0xb8fb10: r0 = showMenuItem()
    //     0xb8fb10: bl              #0xb8fb30  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/review_widget.dart] _ReviewWidgetState::showMenuItem
    // 0xb8fb14: r0 = Null
    //     0xb8fb14: mov             x0, NULL
    // 0xb8fb18: LeaveFrame
    //     0xb8fb18: mov             SP, fp
    //     0xb8fb1c: ldp             fp, lr, [SP], #0x10
    // 0xb8fb20: ret
    //     0xb8fb20: ret             
    // 0xb8fb24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8fb24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8fb28: b               #0xb8fa54
    // 0xb8fb2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8fb2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ showMenuItem(/* No info */) {
    // ** addr: 0xb8fb30, size: 0x6b0
    // 0xb8fb30: EnterFrame
    //     0xb8fb30: stp             fp, lr, [SP, #-0x10]!
    //     0xb8fb34: mov             fp, SP
    // 0xb8fb38: AllocStack(0x98)
    //     0xb8fb38: sub             SP, SP, #0x98
    // 0xb8fb3c: SetupParameters(_ReviewWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */)
    //     0xb8fb3c: mov             x0, x1
    //     0xb8fb40: stur            x1, [fp, #-8]
    //     0xb8fb44: mov             x1, x2
    //     0xb8fb48: stur            x2, [fp, #-0x10]
    //     0xb8fb4c: mov             x2, x5
    //     0xb8fb50: stur            x3, [fp, #-0x18]
    //     0xb8fb54: stur            x5, [fp, #-0x20]
    // 0xb8fb58: CheckStackOverflow
    //     0xb8fb58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8fb5c: cmp             SP, x16
    //     0xb8fb60: b.ls            #0xb90158
    // 0xb8fb64: r1 = 2
    //     0xb8fb64: movz            x1, #0x2
    // 0xb8fb68: r0 = AllocateContext()
    //     0xb8fb68: bl              #0x16f6108  ; AllocateContextStub
    // 0xb8fb6c: mov             x4, x0
    // 0xb8fb70: ldur            x3, [fp, #-8]
    // 0xb8fb74: stur            x4, [fp, #-0x28]
    // 0xb8fb78: StoreField: r4->field_f = r3
    //     0xb8fb78: stur            w3, [x4, #0xf]
    // 0xb8fb7c: ldur            x2, [fp, #-0x20]
    // 0xb8fb80: StoreField: r4->field_13 = r2
    //     0xb8fb80: stur            w2, [x4, #0x13]
    // 0xb8fb84: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb8fb84: ldur            w1, [x3, #0x17]
    // 0xb8fb88: DecompressPointer r1
    //     0xb8fb88: add             x1, x1, HEAP, lsl #32
    // 0xb8fb8c: r0 = LoadClassIdInstr(r1)
    //     0xb8fb8c: ldur            x0, [x1, #-1]
    //     0xb8fb90: ubfx            x0, x0, #0xc, #0x14
    // 0xb8fb94: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb8fb94: sub             lr, x0, #0xfe
    //     0xb8fb98: ldr             lr, [x21, lr, lsl #3]
    //     0xb8fb9c: blr             lr
    // 0xb8fba0: r1 = 60
    //     0xb8fba0: movz            x1, #0x3c
    // 0xb8fba4: branchIfSmi(r0, 0xb8fbb0)
    //     0xb8fba4: tbz             w0, #0, #0xb8fbb0
    // 0xb8fba8: r1 = LoadClassIdInstr(r0)
    //     0xb8fba8: ldur            x1, [x0, #-1]
    //     0xb8fbac: ubfx            x1, x1, #0xc, #0x14
    // 0xb8fbb0: r16 = true
    //     0xb8fbb0: add             x16, NULL, #0x20  ; true
    // 0xb8fbb4: stp             x16, x0, [SP]
    // 0xb8fbb8: mov             x0, x1
    // 0xb8fbbc: mov             lr, x0
    // 0xb8fbc0: ldr             lr, [x21, lr, lsl #3]
    // 0xb8fbc4: blr             lr
    // 0xb8fbc8: tbz             w0, #4, #0xb8fbd8
    // 0xb8fbcc: d0 = 120.000000
    //     0xb8fbcc: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2fa38] IMM: double(120) from 0x405e000000000000
    //     0xb8fbd0: ldr             d0, [x17, #0xa38]
    // 0xb8fbd4: b               #0xb8fbdc
    // 0xb8fbd8: d0 = 100.000000
    //     0xb8fbd8: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xb8fbdc: ldur            x0, [fp, #-0x18]
    // 0xb8fbe0: stur            d0, [fp, #-0x50]
    // 0xb8fbe4: r0 = BoxConstraints()
    //     0xb8fbe4: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb8fbe8: stur            x0, [fp, #-0x20]
    // 0xb8fbec: StoreField: r0->field_7 = rZR
    //     0xb8fbec: stur            xzr, [x0, #7]
    // 0xb8fbf0: ldur            d0, [fp, #-0x50]
    // 0xb8fbf4: StoreField: r0->field_f = d0
    //     0xb8fbf4: stur            d0, [x0, #0xf]
    // 0xb8fbf8: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb8fbf8: stur            xzr, [x0, #0x17]
    // 0xb8fbfc: d0 = inf
    //     0xb8fbfc: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb8fc00: StoreField: r0->field_1f = d0
    //     0xb8fc00: stur            d0, [x0, #0x1f]
    // 0xb8fc04: ldur            x1, [fp, #-0x18]
    // 0xb8fc08: cmp             w1, NULL
    // 0xb8fc0c: b.ne            #0xb8fc18
    // 0xb8fc10: r2 = Null
    //     0xb8fc10: mov             x2, NULL
    // 0xb8fc14: b               #0xb8fc44
    // 0xb8fc18: LoadField: d0 = r1->field_7
    //     0xb8fc18: ldur            d0, [x1, #7]
    // 0xb8fc1c: r2 = inline_Allocate_Double()
    //     0xb8fc1c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb8fc20: add             x2, x2, #0x10
    //     0xb8fc24: cmp             x3, x2
    //     0xb8fc28: b.ls            #0xb90160
    //     0xb8fc2c: str             x2, [THR, #0x50]  ; THR::top
    //     0xb8fc30: sub             x2, x2, #0xf
    //     0xb8fc34: movz            x3, #0xe15c
    //     0xb8fc38: movk            x3, #0x3, lsl #16
    //     0xb8fc3c: stur            x3, [x2, #-1]
    // 0xb8fc40: StoreField: r2->field_7 = d0
    //     0xb8fc40: stur            d0, [x2, #7]
    // 0xb8fc44: cmp             w2, NULL
    // 0xb8fc48: b.ne            #0xb8fc54
    // 0xb8fc4c: d0 = 0.000000
    //     0xb8fc4c: eor             v0.16b, v0.16b, v0.16b
    // 0xb8fc50: b               #0xb8fc58
    // 0xb8fc54: LoadField: d0 = r2->field_7
    //     0xb8fc54: ldur            d0, [x2, #7]
    // 0xb8fc58: stur            d0, [fp, #-0x68]
    // 0xb8fc5c: cmp             w1, NULL
    // 0xb8fc60: b.ne            #0xb8fc6c
    // 0xb8fc64: r2 = Null
    //     0xb8fc64: mov             x2, NULL
    // 0xb8fc68: b               #0xb8fc98
    // 0xb8fc6c: LoadField: d1 = r1->field_f
    //     0xb8fc6c: ldur            d1, [x1, #0xf]
    // 0xb8fc70: r2 = inline_Allocate_Double()
    //     0xb8fc70: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb8fc74: add             x2, x2, #0x10
    //     0xb8fc78: cmp             x3, x2
    //     0xb8fc7c: b.ls            #0xb9017c
    //     0xb8fc80: str             x2, [THR, #0x50]  ; THR::top
    //     0xb8fc84: sub             x2, x2, #0xf
    //     0xb8fc88: movz            x3, #0xe15c
    //     0xb8fc8c: movk            x3, #0x3, lsl #16
    //     0xb8fc90: stur            x3, [x2, #-1]
    // 0xb8fc94: StoreField: r2->field_7 = d1
    //     0xb8fc94: stur            d1, [x2, #7]
    // 0xb8fc98: cmp             w2, NULL
    // 0xb8fc9c: b.ne            #0xb8fca8
    // 0xb8fca0: d2 = 0.000000
    //     0xb8fca0: eor             v2.16b, v2.16b, v2.16b
    // 0xb8fca4: b               #0xb8fcb0
    // 0xb8fca8: LoadField: d1 = r2->field_7
    //     0xb8fca8: ldur            d1, [x2, #7]
    // 0xb8fcac: mov             v2.16b, v1.16b
    // 0xb8fcb0: d1 = 50.000000
    //     0xb8fcb0: ldr             d1, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0xb8fcb4: fsub            d3, d2, d1
    // 0xb8fcb8: stur            d3, [fp, #-0x60]
    // 0xb8fcbc: cmp             w1, NULL
    // 0xb8fcc0: b.ne            #0xb8fccc
    // 0xb8fcc4: r2 = Null
    //     0xb8fcc4: mov             x2, NULL
    // 0xb8fcc8: b               #0xb8fcf8
    // 0xb8fccc: LoadField: d2 = r1->field_7
    //     0xb8fccc: ldur            d2, [x1, #7]
    // 0xb8fcd0: r2 = inline_Allocate_Double()
    //     0xb8fcd0: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb8fcd4: add             x2, x2, #0x10
    //     0xb8fcd8: cmp             x3, x2
    //     0xb8fcdc: b.ls            #0xb90198
    //     0xb8fce0: str             x2, [THR, #0x50]  ; THR::top
    //     0xb8fce4: sub             x2, x2, #0xf
    //     0xb8fce8: movz            x3, #0xe15c
    //     0xb8fcec: movk            x3, #0x3, lsl #16
    //     0xb8fcf0: stur            x3, [x2, #-1]
    // 0xb8fcf4: StoreField: r2->field_7 = d2
    //     0xb8fcf4: stur            d2, [x2, #7]
    // 0xb8fcf8: cmp             w2, NULL
    // 0xb8fcfc: b.ne            #0xb8fd08
    // 0xb8fd00: d2 = 0.000000
    //     0xb8fd00: eor             v2.16b, v2.16b, v2.16b
    // 0xb8fd04: b               #0xb8fd0c
    // 0xb8fd08: LoadField: d2 = r2->field_7
    //     0xb8fd08: ldur            d2, [x2, #7]
    // 0xb8fd0c: fadd            d4, d2, d1
    // 0xb8fd10: stur            d4, [fp, #-0x58]
    // 0xb8fd14: cmp             w1, NULL
    // 0xb8fd18: b.ne            #0xb8fd24
    // 0xb8fd1c: r1 = Null
    //     0xb8fd1c: mov             x1, NULL
    // 0xb8fd20: b               #0xb8fd50
    // 0xb8fd24: LoadField: d1 = r1->field_f
    //     0xb8fd24: ldur            d1, [x1, #0xf]
    // 0xb8fd28: r1 = inline_Allocate_Double()
    //     0xb8fd28: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb8fd2c: add             x1, x1, #0x10
    //     0xb8fd30: cmp             x2, x1
    //     0xb8fd34: b.ls            #0xb901bc
    //     0xb8fd38: str             x1, [THR, #0x50]  ; THR::top
    //     0xb8fd3c: sub             x1, x1, #0xf
    //     0xb8fd40: movz            x2, #0xe15c
    //     0xb8fd44: movk            x2, #0x3, lsl #16
    //     0xb8fd48: stur            x2, [x1, #-1]
    // 0xb8fd4c: StoreField: r1->field_7 = d1
    //     0xb8fd4c: stur            d1, [x1, #7]
    // 0xb8fd50: cmp             w1, NULL
    // 0xb8fd54: b.ne            #0xb8fd60
    // 0xb8fd58: d1 = 0.000000
    //     0xb8fd58: eor             v1.16b, v1.16b, v1.16b
    // 0xb8fd5c: b               #0xb8fd64
    // 0xb8fd60: LoadField: d1 = r1->field_7
    //     0xb8fd60: ldur            d1, [x1, #7]
    // 0xb8fd64: ldur            x1, [fp, #-8]
    // 0xb8fd68: ldur            x2, [fp, #-0x28]
    // 0xb8fd6c: stur            d1, [fp, #-0x50]
    // 0xb8fd70: r0 = RelativeRect()
    //     0xb8fd70: bl              #0x9abaf4  ; AllocateRelativeRectStub -> RelativeRect (size=0x28)
    // 0xb8fd74: ldur            d0, [fp, #-0x68]
    // 0xb8fd78: stur            x0, [fp, #-0x18]
    // 0xb8fd7c: StoreField: r0->field_7 = d0
    //     0xb8fd7c: stur            d0, [x0, #7]
    // 0xb8fd80: ldur            d0, [fp, #-0x60]
    // 0xb8fd84: StoreField: r0->field_f = d0
    //     0xb8fd84: stur            d0, [x0, #0xf]
    // 0xb8fd88: ldur            d0, [fp, #-0x58]
    // 0xb8fd8c: ArrayStore: r0[0] = d0  ; List_8
    //     0xb8fd8c: stur            d0, [x0, #0x17]
    // 0xb8fd90: ldur            d0, [fp, #-0x50]
    // 0xb8fd94: StoreField: r0->field_1f = d0
    //     0xb8fd94: stur            d0, [x0, #0x1f]
    // 0xb8fd98: r1 = <Widget>
    //     0xb8fd98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8fd9c: r2 = 0
    //     0xb8fd9c: movz            x2, #0
    // 0xb8fda0: r0 = _GrowableList()
    //     0xb8fda0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb8fda4: mov             x4, x0
    // 0xb8fda8: ldur            x3, [fp, #-8]
    // 0xb8fdac: stur            x4, [fp, #-0x30]
    // 0xb8fdb0: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb8fdb0: ldur            w1, [x3, #0x17]
    // 0xb8fdb4: DecompressPointer r1
    //     0xb8fdb4: add             x1, x1, HEAP, lsl #32
    // 0xb8fdb8: ldur            x5, [fp, #-0x28]
    // 0xb8fdbc: LoadField: r2 = r5->field_13
    //     0xb8fdbc: ldur            w2, [x5, #0x13]
    // 0xb8fdc0: DecompressPointer r2
    //     0xb8fdc0: add             x2, x2, HEAP, lsl #32
    // 0xb8fdc4: r0 = LoadClassIdInstr(r1)
    //     0xb8fdc4: ldur            x0, [x1, #-1]
    //     0xb8fdc8: ubfx            x0, x0, #0xc, #0x14
    // 0xb8fdcc: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb8fdcc: sub             lr, x0, #0xfe
    //     0xb8fdd0: ldr             lr, [x21, lr, lsl #3]
    //     0xb8fdd4: blr             lr
    // 0xb8fdd8: r16 = true
    //     0xb8fdd8: add             x16, NULL, #0x20  ; true
    // 0xb8fddc: cmp             w0, w16
    // 0xb8fde0: b.ne            #0xb8fe44
    // 0xb8fde4: ldur            x0, [fp, #-0x30]
    // 0xb8fde8: LoadField: r1 = r0->field_b
    //     0xb8fde8: ldur            w1, [x0, #0xb]
    // 0xb8fdec: LoadField: r2 = r0->field_f
    //     0xb8fdec: ldur            w2, [x0, #0xf]
    // 0xb8fdf0: DecompressPointer r2
    //     0xb8fdf0: add             x2, x2, HEAP, lsl #32
    // 0xb8fdf4: LoadField: r3 = r2->field_b
    //     0xb8fdf4: ldur            w3, [x2, #0xb]
    // 0xb8fdf8: r2 = LoadInt32Instr(r1)
    //     0xb8fdf8: sbfx            x2, x1, #1, #0x1f
    // 0xb8fdfc: stur            x2, [fp, #-0x38]
    // 0xb8fe00: r1 = LoadInt32Instr(r3)
    //     0xb8fe00: sbfx            x1, x3, #1, #0x1f
    // 0xb8fe04: cmp             x2, x1
    // 0xb8fe08: b.ne            #0xb8fe14
    // 0xb8fe0c: mov             x1, x0
    // 0xb8fe10: r0 = _growToNextCapacity()
    //     0xb8fe10: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8fe14: ldur            x0, [fp, #-0x30]
    // 0xb8fe18: ldur            x1, [fp, #-0x38]
    // 0xb8fe1c: add             x2, x1, #1
    // 0xb8fe20: lsl             x3, x2, #1
    // 0xb8fe24: StoreField: r0->field_b = r3
    //     0xb8fe24: stur            w3, [x0, #0xb]
    // 0xb8fe28: LoadField: r2 = r0->field_f
    //     0xb8fe28: ldur            w2, [x0, #0xf]
    // 0xb8fe2c: DecompressPointer r2
    //     0xb8fe2c: add             x2, x2, HEAP, lsl #32
    // 0xb8fe30: add             x3, x2, x1, lsl #2
    // 0xb8fe34: r16 = Instance_Icon
    //     0xb8fe34: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa48] Obj!Icon@d65eb1
    //     0xb8fe38: ldr             x16, [x16, #0xa48]
    // 0xb8fe3c: StoreField: r3->field_f = r16
    //     0xb8fe3c: stur            w16, [x3, #0xf]
    // 0xb8fe40: b               #0xb8fe48
    // 0xb8fe44: ldur            x0, [fp, #-0x30]
    // 0xb8fe48: LoadField: r1 = r0->field_b
    //     0xb8fe48: ldur            w1, [x0, #0xb]
    // 0xb8fe4c: LoadField: r2 = r0->field_f
    //     0xb8fe4c: ldur            w2, [x0, #0xf]
    // 0xb8fe50: DecompressPointer r2
    //     0xb8fe50: add             x2, x2, HEAP, lsl #32
    // 0xb8fe54: LoadField: r3 = r2->field_b
    //     0xb8fe54: ldur            w3, [x2, #0xb]
    // 0xb8fe58: r2 = LoadInt32Instr(r1)
    //     0xb8fe58: sbfx            x2, x1, #1, #0x1f
    // 0xb8fe5c: stur            x2, [fp, #-0x38]
    // 0xb8fe60: r1 = LoadInt32Instr(r3)
    //     0xb8fe60: sbfx            x1, x3, #1, #0x1f
    // 0xb8fe64: cmp             x2, x1
    // 0xb8fe68: b.ne            #0xb8fe74
    // 0xb8fe6c: mov             x1, x0
    // 0xb8fe70: r0 = _growToNextCapacity()
    //     0xb8fe70: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8fe74: ldur            x1, [fp, #-8]
    // 0xb8fe78: ldur            x4, [fp, #-0x28]
    // 0xb8fe7c: ldur            x3, [fp, #-0x30]
    // 0xb8fe80: ldur            x0, [fp, #-0x38]
    // 0xb8fe84: add             x2, x0, #1
    // 0xb8fe88: lsl             x5, x2, #1
    // 0xb8fe8c: StoreField: r3->field_b = r5
    //     0xb8fe8c: stur            w5, [x3, #0xb]
    // 0xb8fe90: LoadField: r2 = r3->field_f
    //     0xb8fe90: ldur            w2, [x3, #0xf]
    // 0xb8fe94: DecompressPointer r2
    //     0xb8fe94: add             x2, x2, HEAP, lsl #32
    // 0xb8fe98: add             x5, x2, x0, lsl #2
    // 0xb8fe9c: r16 = Instance_SizedBox
    //     0xb8fe9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb8fea0: ldr             x16, [x16, #0xa50]
    // 0xb8fea4: StoreField: r5->field_f = r16
    //     0xb8fea4: stur            w16, [x5, #0xf]
    // 0xb8fea8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb8fea8: ldur            w0, [x1, #0x17]
    // 0xb8feac: DecompressPointer r0
    //     0xb8feac: add             x0, x0, HEAP, lsl #32
    // 0xb8feb0: LoadField: r2 = r4->field_13
    //     0xb8feb0: ldur            w2, [x4, #0x13]
    // 0xb8feb4: DecompressPointer r2
    //     0xb8feb4: add             x2, x2, HEAP, lsl #32
    // 0xb8feb8: r1 = LoadClassIdInstr(r0)
    //     0xb8feb8: ldur            x1, [x0, #-1]
    //     0xb8febc: ubfx            x1, x1, #0xc, #0x14
    // 0xb8fec0: mov             x16, x0
    // 0xb8fec4: mov             x0, x1
    // 0xb8fec8: mov             x1, x16
    // 0xb8fecc: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb8fecc: sub             lr, x0, #0xfe
    //     0xb8fed0: ldr             lr, [x21, lr, lsl #3]
    //     0xb8fed4: blr             lr
    // 0xb8fed8: r1 = 60
    //     0xb8fed8: movz            x1, #0x3c
    // 0xb8fedc: branchIfSmi(r0, 0xb8fee8)
    //     0xb8fedc: tbz             w0, #0, #0xb8fee8
    // 0xb8fee0: r1 = LoadClassIdInstr(r0)
    //     0xb8fee0: ldur            x1, [x0, #-1]
    //     0xb8fee4: ubfx            x1, x1, #0xc, #0x14
    // 0xb8fee8: r16 = true
    //     0xb8fee8: add             x16, NULL, #0x20  ; true
    // 0xb8feec: stp             x16, x0, [SP]
    // 0xb8fef0: mov             x0, x1
    // 0xb8fef4: mov             lr, x0
    // 0xb8fef8: ldr             lr, [x21, lr, lsl #3]
    // 0xb8fefc: blr             lr
    // 0xb8ff00: tbz             w0, #4, #0xb8ff10
    // 0xb8ff04: r0 = "Flag as abusive"
    //     0xb8ff04: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa60] "Flag as abusive"
    //     0xb8ff08: ldr             x0, [x0, #0xa60]
    // 0xb8ff0c: b               #0xb8ff18
    // 0xb8ff10: r0 = "Flagged"
    //     0xb8ff10: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa58] "Flagged"
    //     0xb8ff14: ldr             x0, [x0, #0xa58]
    // 0xb8ff18: ldur            x1, [fp, #-0x10]
    // 0xb8ff1c: stur            x0, [fp, #-8]
    // 0xb8ff20: r0 = of()
    //     0xb8ff20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8ff24: LoadField: r1 = r0->field_87
    //     0xb8ff24: ldur            w1, [x0, #0x87]
    // 0xb8ff28: DecompressPointer r1
    //     0xb8ff28: add             x1, x1, HEAP, lsl #32
    // 0xb8ff2c: LoadField: r0 = r1->field_33
    //     0xb8ff2c: ldur            w0, [x1, #0x33]
    // 0xb8ff30: DecompressPointer r0
    //     0xb8ff30: add             x0, x0, HEAP, lsl #32
    // 0xb8ff34: cmp             w0, NULL
    // 0xb8ff38: b.ne            #0xb8ff44
    // 0xb8ff3c: r2 = Null
    //     0xb8ff3c: mov             x2, NULL
    // 0xb8ff40: b               #0xb8ff68
    // 0xb8ff44: r16 = 12.000000
    //     0xb8ff44: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb8ff48: ldr             x16, [x16, #0x9e8]
    // 0xb8ff4c: r30 = Instance_Color
    //     0xb8ff4c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8ff50: stp             lr, x16, [SP]
    // 0xb8ff54: mov             x1, x0
    // 0xb8ff58: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8ff58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb8ff5c: ldr             x4, [x4, #0xaa0]
    // 0xb8ff60: r0 = copyWith()
    //     0xb8ff60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8ff64: mov             x2, x0
    // 0xb8ff68: ldur            x1, [fp, #-0x30]
    // 0xb8ff6c: ldur            x0, [fp, #-8]
    // 0xb8ff70: stur            x2, [fp, #-0x40]
    // 0xb8ff74: r0 = Text()
    //     0xb8ff74: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8ff78: mov             x2, x0
    // 0xb8ff7c: ldur            x0, [fp, #-8]
    // 0xb8ff80: stur            x2, [fp, #-0x48]
    // 0xb8ff84: StoreField: r2->field_b = r0
    //     0xb8ff84: stur            w0, [x2, #0xb]
    // 0xb8ff88: ldur            x0, [fp, #-0x40]
    // 0xb8ff8c: StoreField: r2->field_13 = r0
    //     0xb8ff8c: stur            w0, [x2, #0x13]
    // 0xb8ff90: ldur            x0, [fp, #-0x30]
    // 0xb8ff94: LoadField: r1 = r0->field_b
    //     0xb8ff94: ldur            w1, [x0, #0xb]
    // 0xb8ff98: LoadField: r3 = r0->field_f
    //     0xb8ff98: ldur            w3, [x0, #0xf]
    // 0xb8ff9c: DecompressPointer r3
    //     0xb8ff9c: add             x3, x3, HEAP, lsl #32
    // 0xb8ffa0: LoadField: r4 = r3->field_b
    //     0xb8ffa0: ldur            w4, [x3, #0xb]
    // 0xb8ffa4: r3 = LoadInt32Instr(r1)
    //     0xb8ffa4: sbfx            x3, x1, #1, #0x1f
    // 0xb8ffa8: stur            x3, [fp, #-0x38]
    // 0xb8ffac: r1 = LoadInt32Instr(r4)
    //     0xb8ffac: sbfx            x1, x4, #1, #0x1f
    // 0xb8ffb0: cmp             x3, x1
    // 0xb8ffb4: b.ne            #0xb8ffc0
    // 0xb8ffb8: mov             x1, x0
    // 0xb8ffbc: r0 = _growToNextCapacity()
    //     0xb8ffbc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8ffc0: ldur            x2, [fp, #-0x30]
    // 0xb8ffc4: ldur            x3, [fp, #-0x38]
    // 0xb8ffc8: add             x0, x3, #1
    // 0xb8ffcc: lsl             x1, x0, #1
    // 0xb8ffd0: StoreField: r2->field_b = r1
    //     0xb8ffd0: stur            w1, [x2, #0xb]
    // 0xb8ffd4: LoadField: r1 = r2->field_f
    //     0xb8ffd4: ldur            w1, [x2, #0xf]
    // 0xb8ffd8: DecompressPointer r1
    //     0xb8ffd8: add             x1, x1, HEAP, lsl #32
    // 0xb8ffdc: ldur            x0, [fp, #-0x48]
    // 0xb8ffe0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb8ffe0: add             x25, x1, x3, lsl #2
    //     0xb8ffe4: add             x25, x25, #0xf
    //     0xb8ffe8: str             w0, [x25]
    //     0xb8ffec: tbz             w0, #0, #0xb90008
    //     0xb8fff0: ldurb           w16, [x1, #-1]
    //     0xb8fff4: ldurb           w17, [x0, #-1]
    //     0xb8fff8: and             x16, x17, x16, lsr #2
    //     0xb8fffc: tst             x16, HEAP, lsr #32
    //     0xb90000: b.eq            #0xb90008
    //     0xb90004: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb90008: r0 = Row()
    //     0xb90008: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb9000c: mov             x2, x0
    // 0xb90010: r0 = Instance_Axis
    //     0xb90010: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb90014: stur            x2, [fp, #-8]
    // 0xb90018: StoreField: r2->field_f = r0
    //     0xb90018: stur            w0, [x2, #0xf]
    // 0xb9001c: r0 = Instance_MainAxisAlignment
    //     0xb9001c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb90020: ldr             x0, [x0, #0xa08]
    // 0xb90024: StoreField: r2->field_13 = r0
    //     0xb90024: stur            w0, [x2, #0x13]
    // 0xb90028: r0 = Instance_MainAxisSize
    //     0xb90028: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9002c: ldr             x0, [x0, #0xa10]
    // 0xb90030: ArrayStore: r2[0] = r0  ; List_4
    //     0xb90030: stur            w0, [x2, #0x17]
    // 0xb90034: r0 = Instance_CrossAxisAlignment
    //     0xb90034: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb90038: ldr             x0, [x0, #0xa18]
    // 0xb9003c: StoreField: r2->field_1b = r0
    //     0xb9003c: stur            w0, [x2, #0x1b]
    // 0xb90040: r0 = Instance_VerticalDirection
    //     0xb90040: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb90044: ldr             x0, [x0, #0xa20]
    // 0xb90048: StoreField: r2->field_23 = r0
    //     0xb90048: stur            w0, [x2, #0x23]
    // 0xb9004c: r0 = Instance_Clip
    //     0xb9004c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb90050: ldr             x0, [x0, #0x38]
    // 0xb90054: StoreField: r2->field_2b = r0
    //     0xb90054: stur            w0, [x2, #0x2b]
    // 0xb90058: StoreField: r2->field_2f = rZR
    //     0xb90058: stur            xzr, [x2, #0x2f]
    // 0xb9005c: ldur            x0, [fp, #-0x30]
    // 0xb90060: StoreField: r2->field_b = r0
    //     0xb90060: stur            w0, [x2, #0xb]
    // 0xb90064: r1 = <String>
    //     0xb90064: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb90068: r0 = PopupMenuItem()
    //     0xb90068: bl              #0x9abca4  ; AllocatePopupMenuItemStub -> PopupMenuItem<X0> (size=0x38)
    // 0xb9006c: mov             x3, x0
    // 0xb90070: r0 = "flag"
    //     0xb90070: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xb90074: ldr             x0, [x0, #0xa68]
    // 0xb90078: stur            x3, [fp, #-0x30]
    // 0xb9007c: StoreField: r3->field_f = r0
    //     0xb9007c: stur            w0, [x3, #0xf]
    // 0xb90080: ldur            x2, [fp, #-0x28]
    // 0xb90084: r1 = Function '<anonymous closure>':.
    //     0xb90084: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a720] AnonymousClosure: (0xb90378), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/review_widget.dart] _ReviewWidgetState::showMenuItem (0xb8fb30)
    //     0xb90088: ldr             x1, [x1, #0x720]
    // 0xb9008c: r0 = AllocateClosure()
    //     0xb9008c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb90090: mov             x1, x0
    // 0xb90094: ldur            x0, [fp, #-0x30]
    // 0xb90098: StoreField: r0->field_13 = r1
    //     0xb90098: stur            w1, [x0, #0x13]
    // 0xb9009c: r1 = true
    //     0xb9009c: add             x1, NULL, #0x20  ; true
    // 0xb900a0: ArrayStore: r0[0] = r1  ; List_4
    //     0xb900a0: stur            w1, [x0, #0x17]
    // 0xb900a4: d0 = 25.000000
    //     0xb900a4: fmov            d0, #25.00000000
    // 0xb900a8: StoreField: r0->field_1b = d0
    //     0xb900a8: stur            d0, [x0, #0x1b]
    // 0xb900ac: ldur            x1, [fp, #-8]
    // 0xb900b0: StoreField: r0->field_33 = r1
    //     0xb900b0: stur            w1, [x0, #0x33]
    // 0xb900b4: r1 = Null
    //     0xb900b4: mov             x1, NULL
    // 0xb900b8: r2 = 2
    //     0xb900b8: movz            x2, #0x2
    // 0xb900bc: r0 = AllocateArray()
    //     0xb900bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb900c0: mov             x2, x0
    // 0xb900c4: ldur            x0, [fp, #-0x30]
    // 0xb900c8: stur            x2, [fp, #-8]
    // 0xb900cc: StoreField: r2->field_f = r0
    //     0xb900cc: stur            w0, [x2, #0xf]
    // 0xb900d0: r1 = <PopupMenuEntry<String>>
    //     0xb900d0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa70] TypeArguments: <PopupMenuEntry<String>>
    //     0xb900d4: ldr             x1, [x1, #0xa70]
    // 0xb900d8: r0 = AllocateGrowableArray()
    //     0xb900d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb900dc: mov             x1, x0
    // 0xb900e0: ldur            x0, [fp, #-8]
    // 0xb900e4: StoreField: r1->field_f = r0
    //     0xb900e4: stur            w0, [x1, #0xf]
    // 0xb900e8: r0 = 2
    //     0xb900e8: movz            x0, #0x2
    // 0xb900ec: StoreField: r1->field_b = r0
    //     0xb900ec: stur            w0, [x1, #0xb]
    // 0xb900f0: r16 = <String>
    //     0xb900f0: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb900f4: ldur            lr, [fp, #-0x10]
    // 0xb900f8: stp             lr, x16, [SP, #0x20]
    // 0xb900fc: ldur            x16, [fp, #-0x18]
    // 0xb90100: stp             x16, x1, [SP, #0x10]
    // 0xb90104: r16 = Instance_Color
    //     0xb90104: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb90108: ldur            lr, [fp, #-0x20]
    // 0xb9010c: stp             lr, x16, [SP]
    // 0xb90110: r4 = const [0x1, 0x5, 0x5, 0x3, color, 0x3, constraints, 0x4, null]
    //     0xb90110: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa78] List(9) [0x1, 0x5, 0x5, 0x3, "color", 0x3, "constraints", 0x4, Null]
    //     0xb90114: ldr             x4, [x4, #0xa78]
    // 0xb90118: r0 = showMenu()
    //     0xb90118: bl              #0x9ab6c4  ; [package:flutter/src/material/popup_menu.dart] ::showMenu
    // 0xb9011c: ldur            x2, [fp, #-0x28]
    // 0xb90120: r1 = Function '<anonymous closure>':.
    //     0xb90120: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a728] AnonymousClosure: (0xb901e0), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/review_widget.dart] _ReviewWidgetState::showMenuItem (0xb8fb30)
    //     0xb90124: ldr             x1, [x1, #0x728]
    // 0xb90128: stur            x0, [fp, #-8]
    // 0xb9012c: r0 = AllocateClosure()
    //     0xb9012c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb90130: r16 = <Null?>
    //     0xb90130: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xb90134: ldur            lr, [fp, #-8]
    // 0xb90138: stp             lr, x16, [SP, #8]
    // 0xb9013c: str             x0, [SP]
    // 0xb90140: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb90140: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb90144: r0 = then()
    //     0xb90144: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xb90148: r0 = Null
    //     0xb90148: mov             x0, NULL
    // 0xb9014c: LeaveFrame
    //     0xb9014c: mov             SP, fp
    //     0xb90150: ldp             fp, lr, [SP], #0x10
    // 0xb90154: ret
    //     0xb90154: ret             
    // 0xb90158: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb90158: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9015c: b               #0xb8fb64
    // 0xb90160: SaveReg d0
    //     0xb90160: str             q0, [SP, #-0x10]!
    // 0xb90164: stp             x0, x1, [SP, #-0x10]!
    // 0xb90168: r0 = AllocateDouble()
    //     0xb90168: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb9016c: mov             x2, x0
    // 0xb90170: ldp             x0, x1, [SP], #0x10
    // 0xb90174: RestoreReg d0
    //     0xb90174: ldr             q0, [SP], #0x10
    // 0xb90178: b               #0xb8fc40
    // 0xb9017c: stp             q0, q1, [SP, #-0x20]!
    // 0xb90180: stp             x0, x1, [SP, #-0x10]!
    // 0xb90184: r0 = AllocateDouble()
    //     0xb90184: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb90188: mov             x2, x0
    // 0xb9018c: ldp             x0, x1, [SP], #0x10
    // 0xb90190: ldp             q0, q1, [SP], #0x20
    // 0xb90194: b               #0xb8fc94
    // 0xb90198: stp             q2, q3, [SP, #-0x20]!
    // 0xb9019c: stp             q0, q1, [SP, #-0x20]!
    // 0xb901a0: stp             x0, x1, [SP, #-0x10]!
    // 0xb901a4: r0 = AllocateDouble()
    //     0xb901a4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb901a8: mov             x2, x0
    // 0xb901ac: ldp             x0, x1, [SP], #0x10
    // 0xb901b0: ldp             q0, q1, [SP], #0x20
    // 0xb901b4: ldp             q2, q3, [SP], #0x20
    // 0xb901b8: b               #0xb8fcf4
    // 0xb901bc: stp             q3, q4, [SP, #-0x20]!
    // 0xb901c0: stp             q0, q1, [SP, #-0x20]!
    // 0xb901c4: SaveReg r0
    //     0xb901c4: str             x0, [SP, #-8]!
    // 0xb901c8: r0 = AllocateDouble()
    //     0xb901c8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb901cc: mov             x1, x0
    // 0xb901d0: RestoreReg r0
    //     0xb901d0: ldr             x0, [SP], #8
    // 0xb901d4: ldp             q0, q1, [SP], #0x20
    // 0xb901d8: ldp             q3, q4, [SP], #0x20
    // 0xb901dc: b               #0xb8fd4c
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb901e0, size: 0x94
    // 0xb901e0: EnterFrame
    //     0xb901e0: stp             fp, lr, [SP, #-0x10]!
    //     0xb901e4: mov             fp, SP
    // 0xb901e8: AllocStack(0x20)
    //     0xb901e8: sub             SP, SP, #0x20
    // 0xb901ec: SetupParameters()
    //     0xb901ec: ldr             x0, [fp, #0x18]
    //     0xb901f0: ldur            w2, [x0, #0x17]
    //     0xb901f4: add             x2, x2, HEAP, lsl #32
    //     0xb901f8: stur            x2, [fp, #-8]
    // 0xb901fc: CheckStackOverflow
    //     0xb901fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb90200: cmp             SP, x16
    //     0xb90204: b.ls            #0xb9026c
    // 0xb90208: ldr             x0, [fp, #0x10]
    // 0xb9020c: r1 = LoadClassIdInstr(r0)
    //     0xb9020c: ldur            x1, [x0, #-1]
    //     0xb90210: ubfx            x1, x1, #0xc, #0x14
    // 0xb90214: r16 = "flag"
    //     0xb90214: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xb90218: ldr             x16, [x16, #0xa68]
    // 0xb9021c: stp             x16, x0, [SP]
    // 0xb90220: mov             x0, x1
    // 0xb90224: mov             lr, x0
    // 0xb90228: ldr             lr, [x21, lr, lsl #3]
    // 0xb9022c: blr             lr
    // 0xb90230: tbnz            w0, #4, #0xb9025c
    // 0xb90234: ldur            x2, [fp, #-8]
    // 0xb90238: LoadField: r0 = r2->field_f
    //     0xb90238: ldur            w0, [x2, #0xf]
    // 0xb9023c: DecompressPointer r0
    //     0xb9023c: add             x0, x0, HEAP, lsl #32
    // 0xb90240: stur            x0, [fp, #-0x10]
    // 0xb90244: r1 = Function '<anonymous closure>':.
    //     0xb90244: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a730] AnonymousClosure: (0xb90274), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/review_widget.dart] _ReviewWidgetState::showMenuItem (0xb8fb30)
    //     0xb90248: ldr             x1, [x1, #0x730]
    // 0xb9024c: r0 = AllocateClosure()
    //     0xb9024c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb90250: ldur            x1, [fp, #-0x10]
    // 0xb90254: mov             x2, x0
    // 0xb90258: r0 = setState()
    //     0xb90258: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb9025c: r0 = Null
    //     0xb9025c: mov             x0, NULL
    // 0xb90260: LeaveFrame
    //     0xb90260: mov             SP, fp
    //     0xb90264: ldp             fp, lr, [SP], #0x10
    // 0xb90268: ret
    //     0xb90268: ret             
    // 0xb9026c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9026c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb90270: b               #0xb90208
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb90274, size: 0x104
    // 0xb90274: EnterFrame
    //     0xb90274: stp             fp, lr, [SP, #-0x10]!
    //     0xb90278: mov             fp, SP
    // 0xb9027c: AllocStack(0x20)
    //     0xb9027c: sub             SP, SP, #0x20
    // 0xb90280: SetupParameters()
    //     0xb90280: ldr             x0, [fp, #0x10]
    //     0xb90284: ldur            w4, [x0, #0x17]
    //     0xb90288: add             x4, x4, HEAP, lsl #32
    //     0xb9028c: stur            x4, [fp, #-8]
    // 0xb90290: CheckStackOverflow
    //     0xb90290: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb90294: cmp             SP, x16
    //     0xb90298: b.ls            #0xb90368
    // 0xb9029c: LoadField: r0 = r4->field_f
    //     0xb9029c: ldur            w0, [x4, #0xf]
    // 0xb902a0: DecompressPointer r0
    //     0xb902a0: add             x0, x0, HEAP, lsl #32
    // 0xb902a4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb902a4: ldur            w1, [x0, #0x17]
    // 0xb902a8: DecompressPointer r1
    //     0xb902a8: add             x1, x1, HEAP, lsl #32
    // 0xb902ac: LoadField: r2 = r4->field_13
    //     0xb902ac: ldur            w2, [x4, #0x13]
    // 0xb902b0: DecompressPointer r2
    //     0xb902b0: add             x2, x2, HEAP, lsl #32
    // 0xb902b4: r0 = LoadClassIdInstr(r1)
    //     0xb902b4: ldur            x0, [x1, #-1]
    //     0xb902b8: ubfx            x0, x0, #0xc, #0x14
    // 0xb902bc: r3 = true
    //     0xb902bc: add             x3, NULL, #0x20  ; true
    // 0xb902c0: r0 = GDT[cid_x0 + 0x35a]()
    //     0xb902c0: add             lr, x0, #0x35a
    //     0xb902c4: ldr             lr, [x21, lr, lsl #3]
    //     0xb902c8: blr             lr
    // 0xb902cc: ldur            x1, [fp, #-8]
    // 0xb902d0: LoadField: r0 = r1->field_f
    //     0xb902d0: ldur            w0, [x1, #0xf]
    // 0xb902d4: DecompressPointer r0
    //     0xb902d4: add             x0, x0, HEAP, lsl #32
    // 0xb902d8: LoadField: r2 = r0->field_b
    //     0xb902d8: ldur            w2, [x0, #0xb]
    // 0xb902dc: DecompressPointer r2
    //     0xb902dc: add             x2, x2, HEAP, lsl #32
    // 0xb902e0: cmp             w2, NULL
    // 0xb902e4: b.eq            #0xb90370
    // 0xb902e8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb902e8: ldur            w3, [x0, #0x17]
    // 0xb902ec: DecompressPointer r3
    //     0xb902ec: add             x3, x3, HEAP, lsl #32
    // 0xb902f0: LoadField: r0 = r2->field_f
    //     0xb902f0: ldur            w0, [x2, #0xf]
    // 0xb902f4: DecompressPointer r0
    //     0xb902f4: add             x0, x0, HEAP, lsl #32
    // 0xb902f8: stp             x3, x0, [SP]
    // 0xb902fc: ClosureCall
    //     0xb902fc: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb90300: ldur            x2, [x0, #0x1f]
    //     0xb90304: blr             x2
    // 0xb90308: ldur            x0, [fp, #-8]
    // 0xb9030c: LoadField: r1 = r0->field_f
    //     0xb9030c: ldur            w1, [x0, #0xf]
    // 0xb90310: DecompressPointer r1
    //     0xb90310: add             x1, x1, HEAP, lsl #32
    // 0xb90314: LoadField: r0 = r1->field_b
    //     0xb90314: ldur            w0, [x1, #0xb]
    // 0xb90318: DecompressPointer r0
    //     0xb90318: add             x0, x0, HEAP, lsl #32
    // 0xb9031c: cmp             w0, NULL
    // 0xb90320: b.eq            #0xb90374
    // 0xb90324: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb90324: ldur            w1, [x0, #0x17]
    // 0xb90328: DecompressPointer r1
    //     0xb90328: add             x1, x1, HEAP, lsl #32
    // 0xb9032c: r16 = "flag_abusive"
    //     0xb9032c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa88] "flag_abusive"
    //     0xb90330: ldr             x16, [x16, #0xa88]
    // 0xb90334: stp             x16, x1, [SP, #8]
    // 0xb90338: r16 = ""
    //     0xb90338: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb9033c: str             x16, [SP]
    // 0xb90340: r4 = 0
    //     0xb90340: movz            x4, #0
    // 0xb90344: ldr             x0, [SP, #0x10]
    // 0xb90348: r16 = UnlinkedCall_0x613b5c
    //     0xb90348: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a738] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb9034c: add             x16, x16, #0x738
    // 0xb90350: ldp             x5, lr, [x16]
    // 0xb90354: blr             lr
    // 0xb90358: r0 = Null
    //     0xb90358: mov             x0, NULL
    // 0xb9035c: LeaveFrame
    //     0xb9035c: mov             SP, fp
    //     0xb90360: ldp             fp, lr, [SP], #0x10
    // 0xb90364: ret
    //     0xb90364: ret             
    // 0xb90368: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb90368: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9036c: b               #0xb9029c
    // 0xb90370: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb90370: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb90374: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb90374: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb90378, size: 0x60
    // 0xb90378: EnterFrame
    //     0xb90378: stp             fp, lr, [SP, #-0x10]!
    //     0xb9037c: mov             fp, SP
    // 0xb90380: AllocStack(0x8)
    //     0xb90380: sub             SP, SP, #8
    // 0xb90384: SetupParameters()
    //     0xb90384: ldr             x0, [fp, #0x10]
    //     0xb90388: ldur            w2, [x0, #0x17]
    //     0xb9038c: add             x2, x2, HEAP, lsl #32
    // 0xb90390: CheckStackOverflow
    //     0xb90390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb90394: cmp             SP, x16
    //     0xb90398: b.ls            #0xb903d0
    // 0xb9039c: LoadField: r0 = r2->field_f
    //     0xb9039c: ldur            w0, [x2, #0xf]
    // 0xb903a0: DecompressPointer r0
    //     0xb903a0: add             x0, x0, HEAP, lsl #32
    // 0xb903a4: stur            x0, [fp, #-8]
    // 0xb903a8: r1 = Function '<anonymous closure>':.
    //     0xb903a8: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a748] AnonymousClosure: (0xb903d8), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/review_widget.dart] _ReviewWidgetState::showMenuItem (0xb8fb30)
    //     0xb903ac: ldr             x1, [x1, #0x748]
    // 0xb903b0: r0 = AllocateClosure()
    //     0xb903b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb903b4: ldur            x1, [fp, #-8]
    // 0xb903b8: mov             x2, x0
    // 0xb903bc: r0 = setState()
    //     0xb903bc: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb903c0: r0 = Null
    //     0xb903c0: mov             x0, NULL
    // 0xb903c4: LeaveFrame
    //     0xb903c4: mov             SP, fp
    //     0xb903c8: ldp             fp, lr, [SP], #0x10
    // 0xb903cc: ret
    //     0xb903cc: ret             
    // 0xb903d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb903d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb903d4: b               #0xb9039c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb903d8, size: 0x104
    // 0xb903d8: EnterFrame
    //     0xb903d8: stp             fp, lr, [SP, #-0x10]!
    //     0xb903dc: mov             fp, SP
    // 0xb903e0: AllocStack(0x20)
    //     0xb903e0: sub             SP, SP, #0x20
    // 0xb903e4: SetupParameters()
    //     0xb903e4: ldr             x0, [fp, #0x10]
    //     0xb903e8: ldur            w4, [x0, #0x17]
    //     0xb903ec: add             x4, x4, HEAP, lsl #32
    //     0xb903f0: stur            x4, [fp, #-8]
    // 0xb903f4: CheckStackOverflow
    //     0xb903f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb903f8: cmp             SP, x16
    //     0xb903fc: b.ls            #0xb904cc
    // 0xb90400: LoadField: r0 = r4->field_f
    //     0xb90400: ldur            w0, [x4, #0xf]
    // 0xb90404: DecompressPointer r0
    //     0xb90404: add             x0, x0, HEAP, lsl #32
    // 0xb90408: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb90408: ldur            w1, [x0, #0x17]
    // 0xb9040c: DecompressPointer r1
    //     0xb9040c: add             x1, x1, HEAP, lsl #32
    // 0xb90410: LoadField: r2 = r4->field_13
    //     0xb90410: ldur            w2, [x4, #0x13]
    // 0xb90414: DecompressPointer r2
    //     0xb90414: add             x2, x2, HEAP, lsl #32
    // 0xb90418: r0 = LoadClassIdInstr(r1)
    //     0xb90418: ldur            x0, [x1, #-1]
    //     0xb9041c: ubfx            x0, x0, #0xc, #0x14
    // 0xb90420: r3 = true
    //     0xb90420: add             x3, NULL, #0x20  ; true
    // 0xb90424: r0 = GDT[cid_x0 + 0x35a]()
    //     0xb90424: add             lr, x0, #0x35a
    //     0xb90428: ldr             lr, [x21, lr, lsl #3]
    //     0xb9042c: blr             lr
    // 0xb90430: ldur            x1, [fp, #-8]
    // 0xb90434: LoadField: r0 = r1->field_f
    //     0xb90434: ldur            w0, [x1, #0xf]
    // 0xb90438: DecompressPointer r0
    //     0xb90438: add             x0, x0, HEAP, lsl #32
    // 0xb9043c: LoadField: r2 = r0->field_b
    //     0xb9043c: ldur            w2, [x0, #0xb]
    // 0xb90440: DecompressPointer r2
    //     0xb90440: add             x2, x2, HEAP, lsl #32
    // 0xb90444: cmp             w2, NULL
    // 0xb90448: b.eq            #0xb904d4
    // 0xb9044c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb9044c: ldur            w3, [x0, #0x17]
    // 0xb90450: DecompressPointer r3
    //     0xb90450: add             x3, x3, HEAP, lsl #32
    // 0xb90454: LoadField: r0 = r2->field_f
    //     0xb90454: ldur            w0, [x2, #0xf]
    // 0xb90458: DecompressPointer r0
    //     0xb90458: add             x0, x0, HEAP, lsl #32
    // 0xb9045c: stp             x3, x0, [SP]
    // 0xb90460: ClosureCall
    //     0xb90460: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb90464: ldur            x2, [x0, #0x1f]
    //     0xb90468: blr             x2
    // 0xb9046c: ldur            x0, [fp, #-8]
    // 0xb90470: LoadField: r1 = r0->field_f
    //     0xb90470: ldur            w1, [x0, #0xf]
    // 0xb90474: DecompressPointer r1
    //     0xb90474: add             x1, x1, HEAP, lsl #32
    // 0xb90478: LoadField: r0 = r1->field_b
    //     0xb90478: ldur            w0, [x1, #0xb]
    // 0xb9047c: DecompressPointer r0
    //     0xb9047c: add             x0, x0, HEAP, lsl #32
    // 0xb90480: cmp             w0, NULL
    // 0xb90484: b.eq            #0xb904d8
    // 0xb90488: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb90488: ldur            w1, [x0, #0x17]
    // 0xb9048c: DecompressPointer r1
    //     0xb9048c: add             x1, x1, HEAP, lsl #32
    // 0xb90490: r16 = "flag_abusive"
    //     0xb90490: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa88] "flag_abusive"
    //     0xb90494: ldr             x16, [x16, #0xa88]
    // 0xb90498: stp             x16, x1, [SP, #8]
    // 0xb9049c: r16 = ""
    //     0xb9049c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb904a0: str             x16, [SP]
    // 0xb904a4: r4 = 0
    //     0xb904a4: movz            x4, #0
    // 0xb904a8: ldr             x0, [SP, #0x10]
    // 0xb904ac: r16 = UnlinkedCall_0x613b5c
    //     0xb904ac: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a750] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb904b0: add             x16, x16, #0x750
    // 0xb904b4: ldp             x5, lr, [x16]
    // 0xb904b8: blr             lr
    // 0xb904bc: r0 = Null
    //     0xb904bc: mov             x0, NULL
    // 0xb904c0: LeaveFrame
    //     0xb904c0: mov             SP, fp
    //     0xb904c4: ldp             fp, lr, [SP], #0x10
    // 0xb904c8: ret
    //     0xb904c8: ret             
    // 0xb904cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb904cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb904d0: b               #0xb90400
    // 0xb904d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb904d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb904d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb904d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb904dc, size: 0x2c0
    // 0xb904dc: EnterFrame
    //     0xb904dc: stp             fp, lr, [SP, #-0x10]!
    //     0xb904e0: mov             fp, SP
    // 0xb904e4: AllocStack(0x60)
    //     0xb904e4: sub             SP, SP, #0x60
    // 0xb904e8: SetupParameters()
    //     0xb904e8: ldr             x0, [fp, #0x20]
    //     0xb904ec: ldur            w1, [x0, #0x17]
    //     0xb904f0: add             x1, x1, HEAP, lsl #32
    //     0xb904f4: stur            x1, [fp, #-8]
    // 0xb904f8: CheckStackOverflow
    //     0xb904f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb904fc: cmp             SP, x16
    //     0xb90500: b.ls            #0xb9078c
    // 0xb90504: r1 = 3
    //     0xb90504: movz            x1, #0x3
    // 0xb90508: r0 = AllocateContext()
    //     0xb90508: bl              #0x16f6108  ; AllocateContextStub
    // 0xb9050c: mov             x2, x0
    // 0xb90510: ldur            x0, [fp, #-8]
    // 0xb90514: stur            x2, [fp, #-0x10]
    // 0xb90518: StoreField: r2->field_b = r0
    //     0xb90518: stur            w0, [x2, #0xb]
    // 0xb9051c: ldr             x1, [fp, #0x18]
    // 0xb90520: StoreField: r2->field_f = r1
    //     0xb90520: stur            w1, [x2, #0xf]
    // 0xb90524: ldr             x3, [fp, #0x10]
    // 0xb90528: StoreField: r2->field_13 = r3
    //     0xb90528: stur            w3, [x2, #0x13]
    // 0xb9052c: LoadField: r4 = r0->field_13
    //     0xb9052c: ldur            w4, [x0, #0x13]
    // 0xb90530: DecompressPointer r4
    //     0xb90530: add             x4, x4, HEAP, lsl #32
    // 0xb90534: cmp             w4, NULL
    // 0xb90538: b.ne            #0xb90544
    // 0xb9053c: r5 = Null
    //     0xb9053c: mov             x5, NULL
    // 0xb90540: b               #0xb90590
    // 0xb90544: LoadField: r5 = r4->field_1b
    //     0xb90544: ldur            w5, [x4, #0x1b]
    // 0xb90548: DecompressPointer r5
    //     0xb90548: add             x5, x5, HEAP, lsl #32
    // 0xb9054c: LoadField: r0 = r5->field_b
    //     0xb9054c: ldur            w0, [x5, #0xb]
    // 0xb90550: r6 = LoadInt32Instr(r3)
    //     0xb90550: sbfx            x6, x3, #1, #0x1f
    //     0xb90554: tbz             w3, #0, #0xb9055c
    //     0xb90558: ldur            x6, [x3, #7]
    // 0xb9055c: r1 = LoadInt32Instr(r0)
    //     0xb9055c: sbfx            x1, x0, #1, #0x1f
    // 0xb90560: mov             x0, x1
    // 0xb90564: mov             x1, x6
    // 0xb90568: cmp             x1, x0
    // 0xb9056c: b.hs            #0xb90794
    // 0xb90570: LoadField: r0 = r5->field_f
    //     0xb90570: ldur            w0, [x5, #0xf]
    // 0xb90574: DecompressPointer r0
    //     0xb90574: add             x0, x0, HEAP, lsl #32
    // 0xb90578: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb90578: add             x16, x0, x6, lsl #2
    //     0xb9057c: ldur            w1, [x16, #0xf]
    // 0xb90580: DecompressPointer r1
    //     0xb90580: add             x1, x1, HEAP, lsl #32
    // 0xb90584: LoadField: r0 = r1->field_13
    //     0xb90584: ldur            w0, [x1, #0x13]
    // 0xb90588: DecompressPointer r0
    //     0xb90588: add             x0, x0, HEAP, lsl #32
    // 0xb9058c: mov             x5, x0
    // 0xb90590: stur            x5, [fp, #-8]
    // 0xb90594: ArrayStore: r2[0] = r5  ; List_4
    //     0xb90594: stur            w5, [x2, #0x17]
    // 0xb90598: cmp             w4, NULL
    // 0xb9059c: b.ne            #0xb905a8
    // 0xb905a0: r0 = Null
    //     0xb905a0: mov             x0, NULL
    // 0xb905a4: b               #0xb905f0
    // 0xb905a8: LoadField: r6 = r4->field_1b
    //     0xb905a8: ldur            w6, [x4, #0x1b]
    // 0xb905ac: DecompressPointer r6
    //     0xb905ac: add             x6, x6, HEAP, lsl #32
    // 0xb905b0: LoadField: r0 = r6->field_b
    //     0xb905b0: ldur            w0, [x6, #0xb]
    // 0xb905b4: r4 = LoadInt32Instr(r3)
    //     0xb905b4: sbfx            x4, x3, #1, #0x1f
    //     0xb905b8: tbz             w3, #0, #0xb905c0
    //     0xb905bc: ldur            x4, [x3, #7]
    // 0xb905c0: r1 = LoadInt32Instr(r0)
    //     0xb905c0: sbfx            x1, x0, #1, #0x1f
    // 0xb905c4: mov             x0, x1
    // 0xb905c8: mov             x1, x4
    // 0xb905cc: cmp             x1, x0
    // 0xb905d0: b.hs            #0xb90798
    // 0xb905d4: LoadField: r0 = r6->field_f
    //     0xb905d4: ldur            w0, [x6, #0xf]
    // 0xb905d8: DecompressPointer r0
    //     0xb905d8: add             x0, x0, HEAP, lsl #32
    // 0xb905dc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb905dc: add             x16, x0, x4, lsl #2
    //     0xb905e0: ldur            w1, [x16, #0xf]
    // 0xb905e4: DecompressPointer r1
    //     0xb905e4: add             x1, x1, HEAP, lsl #32
    // 0xb905e8: LoadField: r0 = r1->field_f
    //     0xb905e8: ldur            w0, [x1, #0xf]
    // 0xb905ec: DecompressPointer r0
    //     0xb905ec: add             x0, x0, HEAP, lsl #32
    // 0xb905f0: r1 = LoadClassIdInstr(r0)
    //     0xb905f0: ldur            x1, [x0, #-1]
    //     0xb905f4: ubfx            x1, x1, #0xc, #0x14
    // 0xb905f8: r16 = "image"
    //     0xb905f8: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0xb905fc: stp             x16, x0, [SP]
    // 0xb90600: mov             x0, x1
    // 0xb90604: mov             lr, x0
    // 0xb90608: ldr             lr, [x21, lr, lsl #3]
    // 0xb9060c: blr             lr
    // 0xb90610: tbnz            w0, #4, #0xb906fc
    // 0xb90614: ldur            x0, [fp, #-8]
    // 0xb90618: r0 = Radius()
    //     0xb90618: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb9061c: d0 = 12.000000
    //     0xb9061c: fmov            d0, #12.00000000
    // 0xb90620: stur            x0, [fp, #-0x18]
    // 0xb90624: StoreField: r0->field_7 = d0
    //     0xb90624: stur            d0, [x0, #7]
    // 0xb90628: StoreField: r0->field_f = d0
    //     0xb90628: stur            d0, [x0, #0xf]
    // 0xb9062c: r0 = BorderRadius()
    //     0xb9062c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb90630: mov             x3, x0
    // 0xb90634: ldur            x0, [fp, #-0x18]
    // 0xb90638: stur            x3, [fp, #-0x20]
    // 0xb9063c: StoreField: r3->field_7 = r0
    //     0xb9063c: stur            w0, [x3, #7]
    // 0xb90640: StoreField: r3->field_b = r0
    //     0xb90640: stur            w0, [x3, #0xb]
    // 0xb90644: StoreField: r3->field_f = r0
    //     0xb90644: stur            w0, [x3, #0xf]
    // 0xb90648: StoreField: r3->field_13 = r0
    //     0xb90648: stur            w0, [x3, #0x13]
    // 0xb9064c: ldur            x0, [fp, #-8]
    // 0xb90650: cmp             w0, NULL
    // 0xb90654: b.ne            #0xb9065c
    // 0xb90658: r0 = ""
    //     0xb90658: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb9065c: stur            x0, [fp, #-0x18]
    // 0xb90660: r1 = Function '<anonymous closure>':.
    //     0xb90660: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a760] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb90664: ldr             x1, [x1, #0x760]
    // 0xb90668: r2 = Null
    //     0xb90668: mov             x2, NULL
    // 0xb9066c: r0 = AllocateClosure()
    //     0xb9066c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb90670: r1 = Function '<anonymous closure>':.
    //     0xb90670: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a768] AnonymousClosure: (0x9b17ac), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb90674: ldr             x1, [x1, #0x768]
    // 0xb90678: r2 = Null
    //     0xb90678: mov             x2, NULL
    // 0xb9067c: stur            x0, [fp, #-0x28]
    // 0xb90680: r0 = AllocateClosure()
    //     0xb90680: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb90684: stur            x0, [fp, #-0x30]
    // 0xb90688: r0 = CachedNetworkImage()
    //     0xb90688: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb9068c: stur            x0, [fp, #-0x38]
    // 0xb90690: r16 = Instance_BoxFit
    //     0xb90690: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb90694: ldr             x16, [x16, #0x118]
    // 0xb90698: r30 = 48.000000
    //     0xb90698: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xb9069c: ldr             lr, [lr, #0xad8]
    // 0xb906a0: stp             lr, x16, [SP, #0x18]
    // 0xb906a4: r16 = 48.000000
    //     0xb906a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xb906a8: ldr             x16, [x16, #0xad8]
    // 0xb906ac: ldur            lr, [fp, #-0x28]
    // 0xb906b0: stp             lr, x16, [SP, #8]
    // 0xb906b4: ldur            x16, [fp, #-0x30]
    // 0xb906b8: str             x16, [SP]
    // 0xb906bc: mov             x1, x0
    // 0xb906c0: ldur            x2, [fp, #-0x18]
    // 0xb906c4: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x2, height, 0x4, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xb906c4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fae0] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x2, "height", 0x4, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xb906c8: ldr             x4, [x4, #0xae0]
    // 0xb906cc: r0 = CachedNetworkImage()
    //     0xb906cc: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb906d0: r0 = ClipRRect()
    //     0xb906d0: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb906d4: mov             x1, x0
    // 0xb906d8: ldur            x0, [fp, #-0x20]
    // 0xb906dc: StoreField: r1->field_f = r0
    //     0xb906dc: stur            w0, [x1, #0xf]
    // 0xb906e0: r0 = Instance_Clip
    //     0xb906e0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb906e4: ldr             x0, [x0, #0x138]
    // 0xb906e8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb906e8: stur            w0, [x1, #0x17]
    // 0xb906ec: ldur            x0, [fp, #-0x38]
    // 0xb906f0: StoreField: r1->field_b = r0
    //     0xb906f0: stur            w0, [x1, #0xb]
    // 0xb906f4: mov             x0, x1
    // 0xb906f8: b               #0xb90748
    // 0xb906fc: ldur            x0, [fp, #-8]
    // 0xb90700: cmp             w0, NULL
    // 0xb90704: b.ne            #0xb9070c
    // 0xb90708: r0 = ""
    //     0xb90708: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb9070c: stur            x0, [fp, #-8]
    // 0xb90710: r0 = VideoPlayerWidget()
    //     0xb90710: bl              #0xa971e0  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0xb90714: mov             x1, x0
    // 0xb90718: ldur            x0, [fp, #-8]
    // 0xb9071c: stur            x1, [fp, #-0x18]
    // 0xb90720: StoreField: r1->field_b = r0
    //     0xb90720: stur            w0, [x1, #0xb]
    // 0xb90724: r0 = SizedBox()
    //     0xb90724: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb90728: mov             x1, x0
    // 0xb9072c: r0 = 48.000000
    //     0xb9072c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xb90730: ldr             x0, [x0, #0xad8]
    // 0xb90734: StoreField: r1->field_f = r0
    //     0xb90734: stur            w0, [x1, #0xf]
    // 0xb90738: StoreField: r1->field_13 = r0
    //     0xb90738: stur            w0, [x1, #0x13]
    // 0xb9073c: ldur            x0, [fp, #-0x18]
    // 0xb90740: StoreField: r1->field_b = r0
    //     0xb90740: stur            w0, [x1, #0xb]
    // 0xb90744: mov             x0, x1
    // 0xb90748: stur            x0, [fp, #-8]
    // 0xb9074c: r0 = GestureDetector()
    //     0xb9074c: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb90750: ldur            x2, [fp, #-0x10]
    // 0xb90754: r1 = Function '<anonymous closure>':.
    //     0xb90754: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a770] AnonymousClosure: (0xb9079c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xb8e998)
    //     0xb90758: ldr             x1, [x1, #0x770]
    // 0xb9075c: stur            x0, [fp, #-0x10]
    // 0xb90760: r0 = AllocateClosure()
    //     0xb90760: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb90764: ldur            x16, [fp, #-8]
    // 0xb90768: stp             x16, x0, [SP]
    // 0xb9076c: ldur            x1, [fp, #-0x10]
    // 0xb90770: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb90770: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb90774: ldr             x4, [x4, #0xaf0]
    // 0xb90778: r0 = GestureDetector()
    //     0xb90778: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb9077c: ldur            x0, [fp, #-0x10]
    // 0xb90780: LeaveFrame
    //     0xb90780: mov             SP, fp
    //     0xb90784: ldp             fp, lr, [SP], #0x10
    // 0xb90788: ret
    //     0xb90788: ret             
    // 0xb9078c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9078c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb90790: b               #0xb90504
    // 0xb90794: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb90794: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb90798: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb90798: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb9079c, size: 0x104
    // 0xb9079c: EnterFrame
    //     0xb9079c: stp             fp, lr, [SP, #-0x10]!
    //     0xb907a0: mov             fp, SP
    // 0xb907a4: AllocStack(0x28)
    //     0xb907a4: sub             SP, SP, #0x28
    // 0xb907a8: SetupParameters()
    //     0xb907a8: ldr             x0, [fp, #0x10]
    //     0xb907ac: ldur            w2, [x0, #0x17]
    //     0xb907b0: add             x2, x2, HEAP, lsl #32
    //     0xb907b4: stur            x2, [fp, #-8]
    // 0xb907b8: CheckStackOverflow
    //     0xb907b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb907bc: cmp             SP, x16
    //     0xb907c0: b.ls            #0xb90894
    // 0xb907c4: LoadField: r0 = r2->field_b
    //     0xb907c4: ldur            w0, [x2, #0xb]
    // 0xb907c8: DecompressPointer r0
    //     0xb907c8: add             x0, x0, HEAP, lsl #32
    // 0xb907cc: LoadField: r1 = r0->field_b
    //     0xb907cc: ldur            w1, [x0, #0xb]
    // 0xb907d0: DecompressPointer r1
    //     0xb907d0: add             x1, x1, HEAP, lsl #32
    // 0xb907d4: LoadField: r0 = r1->field_f
    //     0xb907d4: ldur            w0, [x1, #0xf]
    // 0xb907d8: DecompressPointer r0
    //     0xb907d8: add             x0, x0, HEAP, lsl #32
    // 0xb907dc: LoadField: r1 = r0->field_b
    //     0xb907dc: ldur            w1, [x0, #0xb]
    // 0xb907e0: DecompressPointer r1
    //     0xb907e0: add             x1, x1, HEAP, lsl #32
    // 0xb907e4: cmp             w1, NULL
    // 0xb907e8: b.eq            #0xb9089c
    // 0xb907ec: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb907ec: ldur            w0, [x2, #0x17]
    // 0xb907f0: DecompressPointer r0
    //     0xb907f0: add             x0, x0, HEAP, lsl #32
    // 0xb907f4: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb907f4: ldur            w3, [x1, #0x17]
    // 0xb907f8: DecompressPointer r3
    //     0xb907f8: add             x3, x3, HEAP, lsl #32
    // 0xb907fc: r16 = "single_media"
    //     0xb907fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fab0] "single_media"
    //     0xb90800: ldr             x16, [x16, #0xab0]
    // 0xb90804: stp             x16, x3, [SP, #8]
    // 0xb90808: str             x0, [SP]
    // 0xb9080c: r4 = 0
    //     0xb9080c: movz            x4, #0
    // 0xb90810: ldr             x0, [SP, #0x10]
    // 0xb90814: r16 = UnlinkedCall_0x613b5c
    //     0xb90814: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a778] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb90818: add             x16, x16, #0x778
    // 0xb9081c: ldp             x5, lr, [x16]
    // 0xb90820: blr             lr
    // 0xb90824: ldur            x2, [fp, #-8]
    // 0xb90828: LoadField: r1 = r2->field_f
    //     0xb90828: ldur            w1, [x2, #0xf]
    // 0xb9082c: DecompressPointer r1
    //     0xb9082c: add             x1, x1, HEAP, lsl #32
    // 0xb90830: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb90830: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb90834: r0 = of()
    //     0xb90834: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xb90838: ldur            x2, [fp, #-8]
    // 0xb9083c: r1 = Function '<anonymous closure>':.
    //     0xb9083c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a788] AnonymousClosure: (0xb908a0), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xb8e998)
    //     0xb90840: ldr             x1, [x1, #0x788]
    // 0xb90844: stur            x0, [fp, #-8]
    // 0xb90848: r0 = AllocateClosure()
    //     0xb90848: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9084c: r1 = Null
    //     0xb9084c: mov             x1, NULL
    // 0xb90850: stur            x0, [fp, #-0x10]
    // 0xb90854: r0 = MaterialPageRoute()
    //     0xb90854: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xb90858: mov             x1, x0
    // 0xb9085c: ldur            x2, [fp, #-0x10]
    // 0xb90860: stur            x0, [fp, #-0x10]
    // 0xb90864: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb90864: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb90868: r0 = MaterialPageRoute()
    //     0xb90868: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xb9086c: ldur            x16, [fp, #-8]
    // 0xb90870: stp             x16, NULL, [SP, #8]
    // 0xb90874: ldur            x16, [fp, #-0x10]
    // 0xb90878: str             x16, [SP]
    // 0xb9087c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb9087c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb90880: r0 = push()
    //     0xb90880: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xb90884: r0 = Null
    //     0xb90884: mov             x0, NULL
    // 0xb90888: LeaveFrame
    //     0xb90888: mov             SP, fp
    //     0xb9088c: ldp             fp, lr, [SP], #0x10
    // 0xb90890: ret
    //     0xb90890: ret             
    // 0xb90894: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb90894: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb90898: b               #0xb907c4
    // 0xb9089c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9089c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RatingReviewOnTapImage <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb908a0, size: 0xc4
    // 0xb908a0: EnterFrame
    //     0xb908a0: stp             fp, lr, [SP, #-0x10]!
    //     0xb908a4: mov             fp, SP
    // 0xb908a8: AllocStack(0x28)
    //     0xb908a8: sub             SP, SP, #0x28
    // 0xb908ac: SetupParameters()
    //     0xb908ac: ldr             x0, [fp, #0x18]
    //     0xb908b0: ldur            w2, [x0, #0x17]
    //     0xb908b4: add             x2, x2, HEAP, lsl #32
    //     0xb908b8: stur            x2, [fp, #-0x20]
    // 0xb908bc: LoadField: r0 = r2->field_13
    //     0xb908bc: ldur            w0, [x2, #0x13]
    // 0xb908c0: DecompressPointer r0
    //     0xb908c0: add             x0, x0, HEAP, lsl #32
    // 0xb908c4: stur            x0, [fp, #-0x18]
    // 0xb908c8: LoadField: r1 = r2->field_b
    //     0xb908c8: ldur            w1, [x2, #0xb]
    // 0xb908cc: DecompressPointer r1
    //     0xb908cc: add             x1, x1, HEAP, lsl #32
    // 0xb908d0: LoadField: r3 = r1->field_b
    //     0xb908d0: ldur            w3, [x1, #0xb]
    // 0xb908d4: DecompressPointer r3
    //     0xb908d4: add             x3, x3, HEAP, lsl #32
    // 0xb908d8: LoadField: r4 = r3->field_f
    //     0xb908d8: ldur            w4, [x3, #0xf]
    // 0xb908dc: DecompressPointer r4
    //     0xb908dc: add             x4, x4, HEAP, lsl #32
    // 0xb908e0: LoadField: r3 = r4->field_b
    //     0xb908e0: ldur            w3, [x4, #0xb]
    // 0xb908e4: DecompressPointer r3
    //     0xb908e4: add             x3, x3, HEAP, lsl #32
    // 0xb908e8: cmp             w3, NULL
    // 0xb908ec: b.eq            #0xb90960
    // 0xb908f0: LoadField: r4 = r3->field_13
    //     0xb908f0: ldur            w4, [x3, #0x13]
    // 0xb908f4: DecompressPointer r4
    //     0xb908f4: add             x4, x4, HEAP, lsl #32
    // 0xb908f8: stur            x4, [fp, #-0x10]
    // 0xb908fc: LoadField: r3 = r1->field_13
    //     0xb908fc: ldur            w3, [x1, #0x13]
    // 0xb90900: DecompressPointer r3
    //     0xb90900: add             x3, x3, HEAP, lsl #32
    // 0xb90904: stur            x3, [fp, #-8]
    // 0xb90908: r0 = RatingReviewOnTapImage()
    //     0xb90908: bl              #0xa9741c  ; AllocateRatingReviewOnTapImageStub -> RatingReviewOnTapImage (size=0x20)
    // 0xb9090c: mov             x3, x0
    // 0xb90910: ldur            x0, [fp, #-8]
    // 0xb90914: stur            x3, [fp, #-0x28]
    // 0xb90918: StoreField: r3->field_b = r0
    //     0xb90918: stur            w0, [x3, #0xb]
    // 0xb9091c: ldur            x0, [fp, #-0x18]
    // 0xb90920: r1 = LoadInt32Instr(r0)
    //     0xb90920: sbfx            x1, x0, #1, #0x1f
    //     0xb90924: tbz             w0, #0, #0xb9092c
    //     0xb90928: ldur            x1, [x0, #7]
    // 0xb9092c: StoreField: r3->field_f = r1
    //     0xb9092c: stur            x1, [x3, #0xf]
    // 0xb90930: ldur            x2, [fp, #-0x20]
    // 0xb90934: r1 = Function '<anonymous closure>':.
    //     0xb90934: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a790] AnonymousClosure: (0xa923cc), in [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xc0c210)
    //     0xb90938: ldr             x1, [x1, #0x790]
    // 0xb9093c: r0 = AllocateClosure()
    //     0xb9093c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb90940: mov             x1, x0
    // 0xb90944: ldur            x0, [fp, #-0x28]
    // 0xb90948: ArrayStore: r0[0] = r1  ; List_4
    //     0xb90948: stur            w1, [x0, #0x17]
    // 0xb9094c: ldur            x1, [fp, #-0x10]
    // 0xb90950: StoreField: r0->field_1b = r1
    //     0xb90950: stur            w1, [x0, #0x1b]
    // 0xb90954: LeaveFrame
    //     0xb90954: mov             SP, fp
    //     0xb90958: ldp             fp, lr, [SP], #0x10
    // 0xb9095c: ret
    //     0xb9095c: ret             
    // 0xb90960: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb90960: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4049, size: 0x1c, field offset: 0xc
//   const constructor, 
class ReviewWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7fa58, size: 0x54
    // 0xc7fa58: EnterFrame
    //     0xc7fa58: stp             fp, lr, [SP, #-0x10]!
    //     0xc7fa5c: mov             fp, SP
    // 0xc7fa60: AllocStack(0x18)
    //     0xc7fa60: sub             SP, SP, #0x18
    // 0xc7fa64: CheckStackOverflow
    //     0xc7fa64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7fa68: cmp             SP, x16
    //     0xc7fa6c: b.ls            #0xc7faa4
    // 0xc7fa70: r16 = <String, dynamic>
    //     0xc7fa70: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xc7fa74: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc7fa78: stp             lr, x16, [SP]
    // 0xc7fa7c: r0 = Map._fromLiteral()
    //     0xc7fa7c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xc7fa80: r1 = <ReviewWidget>
    //     0xc7fa80: add             x1, PP, #0x61, lsl #12  ; [pp+0x61d58] TypeArguments: <ReviewWidget>
    //     0xc7fa84: ldr             x1, [x1, #0xd58]
    // 0xc7fa88: stur            x0, [fp, #-8]
    // 0xc7fa8c: r0 = _ReviewWidgetState()
    //     0xc7fa8c: bl              #0xc7faac  ; Allocate_ReviewWidgetStateStub -> _ReviewWidgetState (size=0x1c)
    // 0xc7fa90: ldur            x1, [fp, #-8]
    // 0xc7fa94: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7fa94: stur            w1, [x0, #0x17]
    // 0xc7fa98: LeaveFrame
    //     0xc7fa98: mov             SP, fp
    //     0xc7fa9c: ldp             fp, lr, [SP], #0x10
    // 0xc7faa0: ret
    //     0xc7faa0: ret             
    // 0xc7faa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7faa4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7faa8: b               #0xc7fa70
  }
}
