// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/accordion.dart

// class id: 1049429, size: 0x8
class :: {
}

// class id: 3321, size: 0x18, field offset: 0x14
class _AccordionState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb7abc0, size: 0x3c8
    // 0xb7abc0: EnterFrame
    //     0xb7abc0: stp             fp, lr, [SP, #-0x10]!
    //     0xb7abc4: mov             fp, SP
    // 0xb7abc8: AllocStack(0x50)
    //     0xb7abc8: sub             SP, SP, #0x50
    // 0xb7abcc: SetupParameters(_AccordionState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb7abcc: mov             x0, x1
    //     0xb7abd0: stur            x1, [fp, #-8]
    //     0xb7abd4: mov             x1, x2
    //     0xb7abd8: stur            x2, [fp, #-0x10]
    // 0xb7abdc: CheckStackOverflow
    //     0xb7abdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb7abe0: cmp             SP, x16
    //     0xb7abe4: b.ls            #0xb7af60
    // 0xb7abe8: r1 = 1
    //     0xb7abe8: movz            x1, #0x1
    // 0xb7abec: r0 = AllocateContext()
    //     0xb7abec: bl              #0x16f6108  ; AllocateContextStub
    // 0xb7abf0: mov             x2, x0
    // 0xb7abf4: ldur            x0, [fp, #-8]
    // 0xb7abf8: stur            x2, [fp, #-0x28]
    // 0xb7abfc: StoreField: r2->field_f = r0
    //     0xb7abfc: stur            w0, [x2, #0xf]
    // 0xb7ac00: LoadField: r1 = r0->field_b
    //     0xb7ac00: ldur            w1, [x0, #0xb]
    // 0xb7ac04: DecompressPointer r1
    //     0xb7ac04: add             x1, x1, HEAP, lsl #32
    // 0xb7ac08: cmp             w1, NULL
    // 0xb7ac0c: b.eq            #0xb7af68
    // 0xb7ac10: LoadField: r3 = r1->field_b
    //     0xb7ac10: ldur            w3, [x1, #0xb]
    // 0xb7ac14: DecompressPointer r3
    //     0xb7ac14: add             x3, x3, HEAP, lsl #32
    // 0xb7ac18: stur            x3, [fp, #-0x20]
    // 0xb7ac1c: LoadField: r4 = r0->field_13
    //     0xb7ac1c: ldur            w4, [x0, #0x13]
    // 0xb7ac20: DecompressPointer r4
    //     0xb7ac20: add             x4, x4, HEAP, lsl #32
    // 0xb7ac24: tbnz            w4, #4, #0xb7ac34
    // 0xb7ac28: r5 = Instance_IconData
    //     0xb7ac28: add             x5, PP, #0x53, lsl #12  ; [pp+0x53440] Obj!IconData@d55621
    //     0xb7ac2c: ldr             x5, [x5, #0x440]
    // 0xb7ac30: b               #0xb7ac3c
    // 0xb7ac34: r5 = Instance_IconData
    //     0xb7ac34: add             x5, PP, #0x53, lsl #12  ; [pp+0x53448] Obj!IconData@d553a1
    //     0xb7ac38: ldr             x5, [x5, #0x448]
    // 0xb7ac3c: stur            x5, [fp, #-0x18]
    // 0xb7ac40: LoadField: d0 = r1->field_1b
    //     0xb7ac40: ldur            d0, [x1, #0x1b]
    // 0xb7ac44: stur            d0, [fp, #-0x40]
    // 0xb7ac48: tbnz            w4, #4, #0xb7ac64
    // 0xb7ac4c: ldur            x1, [fp, #-0x10]
    // 0xb7ac50: r0 = of()
    //     0xb7ac50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7ac54: LoadField: r1 = r0->field_5b
    //     0xb7ac54: ldur            w1, [x0, #0x5b]
    // 0xb7ac58: DecompressPointer r1
    //     0xb7ac58: add             x1, x1, HEAP, lsl #32
    // 0xb7ac5c: mov             x3, x1
    // 0xb7ac60: b               #0xb7ac78
    // 0xb7ac64: ldur            x1, [fp, #-0x10]
    // 0xb7ac68: r0 = of()
    //     0xb7ac68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7ac6c: LoadField: r1 = r0->field_5b
    //     0xb7ac6c: ldur            w1, [x0, #0x5b]
    // 0xb7ac70: DecompressPointer r1
    //     0xb7ac70: add             x1, x1, HEAP, lsl #32
    // 0xb7ac74: mov             x3, x1
    // 0xb7ac78: ldur            x0, [fp, #-8]
    // 0xb7ac7c: ldur            x1, [fp, #-0x20]
    // 0xb7ac80: ldur            x2, [fp, #-0x18]
    // 0xb7ac84: ldur            d0, [fp, #-0x40]
    // 0xb7ac88: stur            x3, [fp, #-0x10]
    // 0xb7ac8c: r0 = Icon()
    //     0xb7ac8c: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb7ac90: mov             x1, x0
    // 0xb7ac94: ldur            x0, [fp, #-0x18]
    // 0xb7ac98: stur            x1, [fp, #-0x30]
    // 0xb7ac9c: StoreField: r1->field_b = r0
    //     0xb7ac9c: stur            w0, [x1, #0xb]
    // 0xb7aca0: ldur            d0, [fp, #-0x40]
    // 0xb7aca4: r0 = inline_Allocate_Double()
    //     0xb7aca4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb7aca8: add             x0, x0, #0x10
    //     0xb7acac: cmp             x2, x0
    //     0xb7acb0: b.ls            #0xb7af6c
    //     0xb7acb4: str             x0, [THR, #0x50]  ; THR::top
    //     0xb7acb8: sub             x0, x0, #0xf
    //     0xb7acbc: movz            x2, #0xe15c
    //     0xb7acc0: movk            x2, #0x3, lsl #16
    //     0xb7acc4: stur            x2, [x0, #-1]
    // 0xb7acc8: StoreField: r0->field_7 = d0
    //     0xb7acc8: stur            d0, [x0, #7]
    // 0xb7accc: StoreField: r1->field_f = r0
    //     0xb7accc: stur            w0, [x1, #0xf]
    // 0xb7acd0: ldur            x0, [fp, #-0x10]
    // 0xb7acd4: StoreField: r1->field_23 = r0
    //     0xb7acd4: stur            w0, [x1, #0x23]
    // 0xb7acd8: ldur            x0, [fp, #-8]
    // 0xb7acdc: LoadField: r2 = r0->field_b
    //     0xb7acdc: ldur            w2, [x0, #0xb]
    // 0xb7ace0: DecompressPointer r2
    //     0xb7ace0: add             x2, x2, HEAP, lsl #32
    // 0xb7ace4: stur            x2, [fp, #-0x10]
    // 0xb7ace8: cmp             w2, NULL
    // 0xb7acec: b.eq            #0xb7af84
    // 0xb7acf0: r0 = RichTextIcon()
    //     0xb7acf0: bl              #0xb78568  ; AllocateRichTextIconStub -> RichTextIcon (size=0x14)
    // 0xb7acf4: mov             x1, x0
    // 0xb7acf8: ldur            x0, [fp, #-0x30]
    // 0xb7acfc: stur            x1, [fp, #-0x18]
    // 0xb7ad00: StoreField: r1->field_b = r0
    //     0xb7ad00: stur            w0, [x1, #0xb]
    // 0xb7ad04: r0 = ""
    //     0xb7ad04: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb7ad08: StoreField: r1->field_f = r0
    //     0xb7ad08: stur            w0, [x1, #0xf]
    // 0xb7ad0c: r0 = ListTile()
    //     0xb7ad0c: bl              #0x98bcd8  ; AllocateListTileStub -> ListTile (size=0xa0)
    // 0xb7ad10: mov             x3, x0
    // 0xb7ad14: ldur            x0, [fp, #-0x20]
    // 0xb7ad18: stur            x3, [fp, #-0x30]
    // 0xb7ad1c: StoreField: r3->field_f = r0
    //     0xb7ad1c: stur            w0, [x3, #0xf]
    // 0xb7ad20: ldur            x0, [fp, #-0x18]
    // 0xb7ad24: ArrayStore: r3[0] = r0  ; List_4
    //     0xb7ad24: stur            w0, [x3, #0x17]
    // 0xb7ad28: r0 = Instance_EdgeInsets
    //     0xb7ad28: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb7ad2c: StoreField: r3->field_47 = r0
    //     0xb7ad2c: stur            w0, [x3, #0x47]
    // 0xb7ad30: r0 = true
    //     0xb7ad30: add             x0, NULL, #0x20  ; true
    // 0xb7ad34: StoreField: r3->field_4b = r0
    //     0xb7ad34: stur            w0, [x3, #0x4b]
    // 0xb7ad38: ldur            x2, [fp, #-0x28]
    // 0xb7ad3c: r1 = Function '<anonymous closure>':.
    //     0xb7ad3c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57188] AnonymousClosure: (0xb7af88), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/accordion.dart] _AccordionState::build (0xb7abc0)
    //     0xb7ad40: ldr             x1, [x1, #0x188]
    // 0xb7ad44: r0 = AllocateClosure()
    //     0xb7ad44: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7ad48: mov             x1, x0
    // 0xb7ad4c: ldur            x0, [fp, #-0x30]
    // 0xb7ad50: StoreField: r0->field_4f = r1
    //     0xb7ad50: stur            w1, [x0, #0x4f]
    // 0xb7ad54: r1 = false
    //     0xb7ad54: add             x1, NULL, #0x30  ; false
    // 0xb7ad58: StoreField: r0->field_5f = r1
    //     0xb7ad58: stur            w1, [x0, #0x5f]
    // 0xb7ad5c: StoreField: r0->field_73 = r1
    //     0xb7ad5c: stur            w1, [x0, #0x73]
    // 0xb7ad60: r1 = 0.000000
    //     0xb7ad60: ldr             x1, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb7ad64: StoreField: r0->field_8b = r1
    //     0xb7ad64: stur            w1, [x0, #0x8b]
    // 0xb7ad68: r1 = true
    //     0xb7ad68: add             x1, NULL, #0x20  ; true
    // 0xb7ad6c: StoreField: r0->field_97 = r1
    //     0xb7ad6c: stur            w1, [x0, #0x97]
    // 0xb7ad70: r1 = Null
    //     0xb7ad70: mov             x1, NULL
    // 0xb7ad74: r2 = 2
    //     0xb7ad74: movz            x2, #0x2
    // 0xb7ad78: r0 = AllocateArray()
    //     0xb7ad78: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7ad7c: mov             x2, x0
    // 0xb7ad80: ldur            x0, [fp, #-0x30]
    // 0xb7ad84: stur            x2, [fp, #-0x18]
    // 0xb7ad88: StoreField: r2->field_f = r0
    //     0xb7ad88: stur            w0, [x2, #0xf]
    // 0xb7ad8c: r1 = <Widget>
    //     0xb7ad8c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7ad90: r0 = AllocateGrowableArray()
    //     0xb7ad90: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7ad94: mov             x1, x0
    // 0xb7ad98: ldur            x0, [fp, #-0x18]
    // 0xb7ad9c: stur            x1, [fp, #-0x20]
    // 0xb7ada0: StoreField: r1->field_f = r0
    //     0xb7ada0: stur            w0, [x1, #0xf]
    // 0xb7ada4: r0 = 2
    //     0xb7ada4: movz            x0, #0x2
    // 0xb7ada8: StoreField: r1->field_b = r0
    //     0xb7ada8: stur            w0, [x1, #0xb]
    // 0xb7adac: ldur            x0, [fp, #-8]
    // 0xb7adb0: LoadField: r2 = r0->field_13
    //     0xb7adb0: ldur            w2, [x0, #0x13]
    // 0xb7adb4: DecompressPointer r2
    //     0xb7adb4: add             x2, x2, HEAP, lsl #32
    // 0xb7adb8: tbnz            w2, #4, #0xb7ae70
    // 0xb7adbc: ldur            x0, [fp, #-0x10]
    // 0xb7adc0: LoadField: r2 = r0->field_13
    //     0xb7adc0: ldur            w2, [x0, #0x13]
    // 0xb7adc4: DecompressPointer r2
    //     0xb7adc4: add             x2, x2, HEAP, lsl #32
    // 0xb7adc8: stur            x2, [fp, #-8]
    // 0xb7adcc: r0 = Container()
    //     0xb7adcc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb7add0: stur            x0, [fp, #-0x10]
    // 0xb7add4: r16 = Instance_EdgeInsets
    //     0xb7add4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xb7add8: ldr             x16, [x16, #0xf30]
    // 0xb7addc: ldur            lr, [fp, #-8]
    // 0xb7ade0: stp             lr, x16, [SP]
    // 0xb7ade4: mov             x1, x0
    // 0xb7ade8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xb7ade8: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xb7adec: ldr             x4, [x4, #0x30]
    // 0xb7adf0: r0 = Container()
    //     0xb7adf0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb7adf4: ldur            x0, [fp, #-0x20]
    // 0xb7adf8: LoadField: r1 = r0->field_b
    //     0xb7adf8: ldur            w1, [x0, #0xb]
    // 0xb7adfc: LoadField: r2 = r0->field_f
    //     0xb7adfc: ldur            w2, [x0, #0xf]
    // 0xb7ae00: DecompressPointer r2
    //     0xb7ae00: add             x2, x2, HEAP, lsl #32
    // 0xb7ae04: LoadField: r3 = r2->field_b
    //     0xb7ae04: ldur            w3, [x2, #0xb]
    // 0xb7ae08: r2 = LoadInt32Instr(r1)
    //     0xb7ae08: sbfx            x2, x1, #1, #0x1f
    // 0xb7ae0c: stur            x2, [fp, #-0x38]
    // 0xb7ae10: r1 = LoadInt32Instr(r3)
    //     0xb7ae10: sbfx            x1, x3, #1, #0x1f
    // 0xb7ae14: cmp             x2, x1
    // 0xb7ae18: b.ne            #0xb7ae24
    // 0xb7ae1c: mov             x1, x0
    // 0xb7ae20: r0 = _growToNextCapacity()
    //     0xb7ae20: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb7ae24: ldur            x2, [fp, #-0x20]
    // 0xb7ae28: ldur            x3, [fp, #-0x38]
    // 0xb7ae2c: add             x0, x3, #1
    // 0xb7ae30: lsl             x1, x0, #1
    // 0xb7ae34: StoreField: r2->field_b = r1
    //     0xb7ae34: stur            w1, [x2, #0xb]
    // 0xb7ae38: LoadField: r1 = r2->field_f
    //     0xb7ae38: ldur            w1, [x2, #0xf]
    // 0xb7ae3c: DecompressPointer r1
    //     0xb7ae3c: add             x1, x1, HEAP, lsl #32
    // 0xb7ae40: ldur            x0, [fp, #-0x10]
    // 0xb7ae44: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb7ae44: add             x25, x1, x3, lsl #2
    //     0xb7ae48: add             x25, x25, #0xf
    //     0xb7ae4c: str             w0, [x25]
    //     0xb7ae50: tbz             w0, #0, #0xb7ae6c
    //     0xb7ae54: ldurb           w16, [x1, #-1]
    //     0xb7ae58: ldurb           w17, [x0, #-1]
    //     0xb7ae5c: and             x16, x17, x16, lsr #2
    //     0xb7ae60: tst             x16, HEAP, lsr #32
    //     0xb7ae64: b.eq            #0xb7ae6c
    //     0xb7ae68: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb7ae6c: b               #0xb7af00
    // 0xb7ae70: mov             x2, x1
    // 0xb7ae74: r0 = Container()
    //     0xb7ae74: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb7ae78: mov             x1, x0
    // 0xb7ae7c: stur            x0, [fp, #-8]
    // 0xb7ae80: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb7ae80: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb7ae84: r0 = Container()
    //     0xb7ae84: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb7ae88: ldur            x0, [fp, #-0x20]
    // 0xb7ae8c: LoadField: r1 = r0->field_b
    //     0xb7ae8c: ldur            w1, [x0, #0xb]
    // 0xb7ae90: LoadField: r2 = r0->field_f
    //     0xb7ae90: ldur            w2, [x0, #0xf]
    // 0xb7ae94: DecompressPointer r2
    //     0xb7ae94: add             x2, x2, HEAP, lsl #32
    // 0xb7ae98: LoadField: r3 = r2->field_b
    //     0xb7ae98: ldur            w3, [x2, #0xb]
    // 0xb7ae9c: r2 = LoadInt32Instr(r1)
    //     0xb7ae9c: sbfx            x2, x1, #1, #0x1f
    // 0xb7aea0: stur            x2, [fp, #-0x38]
    // 0xb7aea4: r1 = LoadInt32Instr(r3)
    //     0xb7aea4: sbfx            x1, x3, #1, #0x1f
    // 0xb7aea8: cmp             x2, x1
    // 0xb7aeac: b.ne            #0xb7aeb8
    // 0xb7aeb0: mov             x1, x0
    // 0xb7aeb4: r0 = _growToNextCapacity()
    //     0xb7aeb4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb7aeb8: ldur            x2, [fp, #-0x20]
    // 0xb7aebc: ldur            x3, [fp, #-0x38]
    // 0xb7aec0: add             x0, x3, #1
    // 0xb7aec4: lsl             x1, x0, #1
    // 0xb7aec8: StoreField: r2->field_b = r1
    //     0xb7aec8: stur            w1, [x2, #0xb]
    // 0xb7aecc: LoadField: r1 = r2->field_f
    //     0xb7aecc: ldur            w1, [x2, #0xf]
    // 0xb7aed0: DecompressPointer r1
    //     0xb7aed0: add             x1, x1, HEAP, lsl #32
    // 0xb7aed4: ldur            x0, [fp, #-8]
    // 0xb7aed8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb7aed8: add             x25, x1, x3, lsl #2
    //     0xb7aedc: add             x25, x25, #0xf
    //     0xb7aee0: str             w0, [x25]
    //     0xb7aee4: tbz             w0, #0, #0xb7af00
    //     0xb7aee8: ldurb           w16, [x1, #-1]
    //     0xb7aeec: ldurb           w17, [x0, #-1]
    //     0xb7aef0: and             x16, x17, x16, lsr #2
    //     0xb7aef4: tst             x16, HEAP, lsr #32
    //     0xb7aef8: b.eq            #0xb7af00
    //     0xb7aefc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb7af00: r0 = Column()
    //     0xb7af00: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb7af04: r1 = Instance_Axis
    //     0xb7af04: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb7af08: StoreField: r0->field_f = r1
    //     0xb7af08: stur            w1, [x0, #0xf]
    // 0xb7af0c: r1 = Instance_MainAxisAlignment
    //     0xb7af0c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb7af10: ldr             x1, [x1, #0xa08]
    // 0xb7af14: StoreField: r0->field_13 = r1
    //     0xb7af14: stur            w1, [x0, #0x13]
    // 0xb7af18: r1 = Instance_MainAxisSize
    //     0xb7af18: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb7af1c: ldr             x1, [x1, #0xa10]
    // 0xb7af20: ArrayStore: r0[0] = r1  ; List_4
    //     0xb7af20: stur            w1, [x0, #0x17]
    // 0xb7af24: r1 = Instance_CrossAxisAlignment
    //     0xb7af24: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb7af28: ldr             x1, [x1, #0xa18]
    // 0xb7af2c: StoreField: r0->field_1b = r1
    //     0xb7af2c: stur            w1, [x0, #0x1b]
    // 0xb7af30: r1 = Instance_VerticalDirection
    //     0xb7af30: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb7af34: ldr             x1, [x1, #0xa20]
    // 0xb7af38: StoreField: r0->field_23 = r1
    //     0xb7af38: stur            w1, [x0, #0x23]
    // 0xb7af3c: r1 = Instance_Clip
    //     0xb7af3c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7af40: ldr             x1, [x1, #0x38]
    // 0xb7af44: StoreField: r0->field_2b = r1
    //     0xb7af44: stur            w1, [x0, #0x2b]
    // 0xb7af48: StoreField: r0->field_2f = rZR
    //     0xb7af48: stur            xzr, [x0, #0x2f]
    // 0xb7af4c: ldur            x1, [fp, #-0x20]
    // 0xb7af50: StoreField: r0->field_b = r1
    //     0xb7af50: stur            w1, [x0, #0xb]
    // 0xb7af54: LeaveFrame
    //     0xb7af54: mov             SP, fp
    //     0xb7af58: ldp             fp, lr, [SP], #0x10
    // 0xb7af5c: ret
    //     0xb7af5c: ret             
    // 0xb7af60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7af60: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb7af64: b               #0xb7abe8
    // 0xb7af68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7af68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7af6c: SaveReg d0
    //     0xb7af6c: str             q0, [SP, #-0x10]!
    // 0xb7af70: SaveReg r1
    //     0xb7af70: str             x1, [SP, #-8]!
    // 0xb7af74: r0 = AllocateDouble()
    //     0xb7af74: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb7af78: RestoreReg r1
    //     0xb7af78: ldr             x1, [SP], #8
    // 0xb7af7c: RestoreReg d0
    //     0xb7af7c: ldr             q0, [SP], #0x10
    // 0xb7af80: b               #0xb7acc8
    // 0xb7af84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7af84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb7af88, size: 0xac
    // 0xb7af88: EnterFrame
    //     0xb7af88: stp             fp, lr, [SP, #-0x10]!
    //     0xb7af8c: mov             fp, SP
    // 0xb7af90: AllocStack(0x18)
    //     0xb7af90: sub             SP, SP, #0x18
    // 0xb7af94: SetupParameters()
    //     0xb7af94: ldr             x0, [fp, #0x10]
    //     0xb7af98: ldur            w3, [x0, #0x17]
    //     0xb7af9c: add             x3, x3, HEAP, lsl #32
    //     0xb7afa0: stur            x3, [fp, #-0x10]
    // 0xb7afa4: CheckStackOverflow
    //     0xb7afa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb7afa8: cmp             SP, x16
    //     0xb7afac: b.ls            #0xb7b028
    // 0xb7afb0: LoadField: r0 = r3->field_f
    //     0xb7afb0: ldur            w0, [x3, #0xf]
    // 0xb7afb4: DecompressPointer r0
    //     0xb7afb4: add             x0, x0, HEAP, lsl #32
    // 0xb7afb8: mov             x2, x3
    // 0xb7afbc: stur            x0, [fp, #-8]
    // 0xb7afc0: r1 = Function '<anonymous closure>':.
    //     0xb7afc0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57190] AnonymousClosure: (0x9a129c), in [package:customer_app/app/presentation/views/line/browse/browse_accordion.dart] _AccordionState::build (0xbaa494)
    //     0xb7afc4: ldr             x1, [x1, #0x190]
    // 0xb7afc8: r0 = AllocateClosure()
    //     0xb7afc8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7afcc: ldur            x1, [fp, #-8]
    // 0xb7afd0: mov             x2, x0
    // 0xb7afd4: r0 = setState()
    //     0xb7afd4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb7afd8: ldur            x0, [fp, #-0x10]
    // 0xb7afdc: LoadField: r1 = r0->field_f
    //     0xb7afdc: ldur            w1, [x0, #0xf]
    // 0xb7afe0: DecompressPointer r1
    //     0xb7afe0: add             x1, x1, HEAP, lsl #32
    // 0xb7afe4: LoadField: r0 = r1->field_b
    //     0xb7afe4: ldur            w0, [x1, #0xb]
    // 0xb7afe8: DecompressPointer r0
    //     0xb7afe8: add             x0, x0, HEAP, lsl #32
    // 0xb7afec: cmp             w0, NULL
    // 0xb7aff0: b.eq            #0xb7b030
    // 0xb7aff4: LoadField: r1 = r0->field_3b
    //     0xb7aff4: ldur            w1, [x0, #0x3b]
    // 0xb7aff8: DecompressPointer r1
    //     0xb7aff8: add             x1, x1, HEAP, lsl #32
    // 0xb7affc: cmp             w1, NULL
    // 0xb7b000: b.eq            #0xb7b018
    // 0xb7b004: str             x1, [SP]
    // 0xb7b008: mov             x0, x1
    // 0xb7b00c: ClosureCall
    //     0xb7b00c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xb7b010: ldur            x2, [x0, #0x1f]
    //     0xb7b014: blr             x2
    // 0xb7b018: r0 = Null
    //     0xb7b018: mov             x0, NULL
    // 0xb7b01c: LeaveFrame
    //     0xb7b01c: mov             SP, fp
    //     0xb7b020: ldp             fp, lr, [SP], #0x10
    // 0xb7b024: ret
    //     0xb7b024: ret             
    // 0xb7b028: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7b028: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb7b02c: b               #0xb7afb0
    // 0xb7b030: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7b030: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4063, size: 0x40, field offset: 0xc
//   const constructor, 
class Accordion extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f6dc, size: 0x2c
    // 0xc7f6dc: EnterFrame
    //     0xc7f6dc: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f6e0: mov             fp, SP
    // 0xc7f6e4: mov             x0, x1
    // 0xc7f6e8: r1 = <Accordion>
    //     0xc7f6e8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a88] TypeArguments: <Accordion>
    //     0xc7f6ec: ldr             x1, [x1, #0xa88]
    // 0xc7f6f0: r0 = _AccordionState()
    //     0xc7f6f0: bl              #0xc7f708  ; Allocate_AccordionStateStub -> _AccordionState (size=0x18)
    // 0xc7f6f4: r1 = false
    //     0xc7f6f4: add             x1, NULL, #0x30  ; false
    // 0xc7f6f8: StoreField: r0->field_13 = r1
    //     0xc7f6f8: stur            w1, [x0, #0x13]
    // 0xc7f6fc: LeaveFrame
    //     0xc7f6fc: mov             SP, fp
    //     0xc7f700: ldp             fp, lr, [SP], #0x10
    // 0xc7f704: ret
    //     0xc7f704: ret             
  }
}
