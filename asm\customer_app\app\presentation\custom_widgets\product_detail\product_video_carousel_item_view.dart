// lib: , url: package:customer_app/app/presentation/custom_widgets/product_detail/product_video_carousel_item_view.dart

// class id: 1049083, size: 0x8
class :: {
}

// class id: 3576, size: 0x20, field offset: 0x14
class _ProductVideoCarouselItemViewState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  [closure] VideoCarouselWidget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x98e864, size: 0xc0
    // 0x98e864: EnterFrame
    //     0x98e864: stp             fp, lr, [SP, #-0x10]!
    //     0x98e868: mov             fp, SP
    // 0x98e86c: AllocStack(0x10)
    //     0x98e86c: sub             SP, SP, #0x10
    // 0x98e870: SetupParameters()
    //     0x98e870: ldr             x0, [fp, #0x20]
    //     0x98e874: ldur            w1, [x0, #0x17]
    //     0x98e878: add             x1, x1, HEAP, lsl #32
    // 0x98e87c: LoadField: r0 = r1->field_f
    //     0x98e87c: ldur            w0, [x1, #0xf]
    // 0x98e880: DecompressPointer r0
    //     0x98e880: add             x0, x0, HEAP, lsl #32
    // 0x98e884: LoadField: r2 = r0->field_b
    //     0x98e884: ldur            w2, [x0, #0xb]
    // 0x98e888: DecompressPointer r2
    //     0x98e888: add             x2, x2, HEAP, lsl #32
    // 0x98e88c: cmp             w2, NULL
    // 0x98e890: b.eq            #0x98e91c
    // 0x98e894: LoadField: r3 = r2->field_b
    //     0x98e894: ldur            w3, [x2, #0xb]
    // 0x98e898: DecompressPointer r3
    //     0x98e898: add             x3, x3, HEAP, lsl #32
    // 0x98e89c: cmp             w3, NULL
    // 0x98e8a0: b.ne            #0x98e8ac
    // 0x98e8a4: r0 = Null
    //     0x98e8a4: mov             x0, NULL
    // 0x98e8a8: b               #0x98e8ec
    // 0x98e8ac: ldr             x0, [fp, #0x10]
    // 0x98e8b0: LoadField: r1 = r3->field_b
    //     0x98e8b0: ldur            w1, [x3, #0xb]
    // 0x98e8b4: r4 = LoadInt32Instr(r0)
    //     0x98e8b4: sbfx            x4, x0, #1, #0x1f
    //     0x98e8b8: tbz             w0, #0, #0x98e8c0
    //     0x98e8bc: ldur            x4, [x0, #7]
    // 0x98e8c0: r0 = LoadInt32Instr(r1)
    //     0x98e8c0: sbfx            x0, x1, #1, #0x1f
    // 0x98e8c4: mov             x1, x4
    // 0x98e8c8: cmp             x1, x0
    // 0x98e8cc: b.hs            #0x98e920
    // 0x98e8d0: LoadField: r0 = r3->field_f
    //     0x98e8d0: ldur            w0, [x3, #0xf]
    // 0x98e8d4: DecompressPointer r0
    //     0x98e8d4: add             x0, x0, HEAP, lsl #32
    // 0x98e8d8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x98e8d8: add             x16, x0, x4, lsl #2
    //     0x98e8dc: ldur            w1, [x16, #0xf]
    // 0x98e8e0: DecompressPointer r1
    //     0x98e8e0: add             x1, x1, HEAP, lsl #32
    // 0x98e8e4: LoadField: r0 = r1->field_13
    //     0x98e8e4: ldur            w0, [x1, #0x13]
    // 0x98e8e8: DecompressPointer r0
    //     0x98e8e8: add             x0, x0, HEAP, lsl #32
    // 0x98e8ec: stur            x0, [fp, #-0x10]
    // 0x98e8f0: LoadField: r1 = r2->field_f
    //     0x98e8f0: ldur            w1, [x2, #0xf]
    // 0x98e8f4: DecompressPointer r1
    //     0x98e8f4: add             x1, x1, HEAP, lsl #32
    // 0x98e8f8: stur            x1, [fp, #-8]
    // 0x98e8fc: r0 = VideoCarouselWidget()
    //     0x98e8fc: bl              #0x98e948  ; AllocateVideoCarouselWidgetStub -> VideoCarouselWidget (size=0x14)
    // 0x98e900: ldur            x1, [fp, #-0x10]
    // 0x98e904: StoreField: r0->field_b = r1
    //     0x98e904: stur            w1, [x0, #0xb]
    // 0x98e908: ldur            x1, [fp, #-8]
    // 0x98e90c: StoreField: r0->field_f = r1
    //     0x98e90c: stur            w1, [x0, #0xf]
    // 0x98e910: LeaveFrame
    //     0x98e910: mov             SP, fp
    //     0x98e914: ldp             fp, lr, [SP], #0x10
    // 0x98e918: ret
    //     0x98e918: ret             
    // 0x98e91c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98e91c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98e920: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x98e920: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0x9abe2c, size: 0x3f4
    // 0x9abe2c: EnterFrame
    //     0x9abe2c: stp             fp, lr, [SP, #-0x10]!
    //     0x9abe30: mov             fp, SP
    // 0x9abe34: AllocStack(0x60)
    //     0x9abe34: sub             SP, SP, #0x60
    // 0x9abe38: SetupParameters(_ProductVideoCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x9abe38: mov             x0, x1
    //     0x9abe3c: stur            x1, [fp, #-8]
    //     0x9abe40: mov             x1, x2
    //     0x9abe44: stur            x2, [fp, #-0x10]
    // 0x9abe48: CheckStackOverflow
    //     0x9abe48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9abe4c: cmp             SP, x16
    //     0x9abe50: b.ls            #0x9ac204
    // 0x9abe54: r1 = 1
    //     0x9abe54: movz            x1, #0x1
    // 0x9abe58: r0 = AllocateContext()
    //     0x9abe58: bl              #0x16f6108  ; AllocateContextStub
    // 0x9abe5c: mov             x3, x0
    // 0x9abe60: ldur            x0, [fp, #-8]
    // 0x9abe64: stur            x3, [fp, #-0x30]
    // 0x9abe68: StoreField: r3->field_f = r0
    //     0x9abe68: stur            w0, [x3, #0xf]
    // 0x9abe6c: LoadField: r1 = r0->field_b
    //     0x9abe6c: ldur            w1, [x0, #0xb]
    // 0x9abe70: DecompressPointer r1
    //     0x9abe70: add             x1, x1, HEAP, lsl #32
    // 0x9abe74: cmp             w1, NULL
    // 0x9abe78: b.eq            #0x9ac20c
    // 0x9abe7c: LoadField: r4 = r1->field_13
    //     0x9abe7c: ldur            w4, [x1, #0x13]
    // 0x9abe80: DecompressPointer r4
    //     0x9abe80: add             x4, x4, HEAP, lsl #32
    // 0x9abe84: stur            x4, [fp, #-0x28]
    // 0x9abe88: LoadField: r2 = r1->field_b
    //     0x9abe88: ldur            w2, [x1, #0xb]
    // 0x9abe8c: DecompressPointer r2
    //     0x9abe8c: add             x2, x2, HEAP, lsl #32
    // 0x9abe90: cmp             w2, NULL
    // 0x9abe94: b.ne            #0x9abea0
    // 0x9abe98: r5 = Null
    //     0x9abe98: mov             x5, NULL
    // 0x9abe9c: b               #0x9abea8
    // 0x9abea0: LoadField: r1 = r2->field_b
    //     0x9abea0: ldur            w1, [x2, #0xb]
    // 0x9abea4: mov             x5, x1
    // 0x9abea8: stur            x5, [fp, #-0x20]
    // 0x9abeac: LoadField: r6 = r0->field_13
    //     0x9abeac: ldur            w6, [x0, #0x13]
    // 0x9abeb0: DecompressPointer r6
    //     0x9abeb0: add             x6, x6, HEAP, lsl #32
    // 0x9abeb4: r16 = Sentinel
    //     0x9abeb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9abeb8: cmp             w6, w16
    // 0x9abebc: b.eq            #0x9ac210
    // 0x9abec0: mov             x2, x3
    // 0x9abec4: stur            x6, [fp, #-0x18]
    // 0x9abec8: r1 = Function '<anonymous closure>':.
    //     0x9abec8: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bbb0] AnonymousClosure: (0x9ac220), in [package:customer_app/app/presentation/custom_widgets/product_detail/product_video_carousel_item_view.dart] _ProductVideoCarouselItemViewState::build (0x9abe2c)
    //     0x9abecc: ldr             x1, [x1, #0xbb0]
    // 0x9abed0: r0 = AllocateClosure()
    //     0x9abed0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9abed4: ldur            x2, [fp, #-0x30]
    // 0x9abed8: r1 = Function '<anonymous closure>':.
    //     0x9abed8: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bbb8] AnonymousClosure: (0x98e864), in [package:customer_app/app/presentation/custom_widgets/product_detail/product_video_carousel_item_view.dart] _ProductVideoCarouselItemViewState::build (0x9abe2c)
    //     0x9abedc: ldr             x1, [x1, #0xbb8]
    // 0x9abee0: stur            x0, [fp, #-0x30]
    // 0x9abee4: r0 = AllocateClosure()
    //     0x9abee4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9abee8: stur            x0, [fp, #-0x38]
    // 0x9abeec: r0 = PageView()
    //     0x9abeec: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0x9abef0: stur            x0, [fp, #-0x40]
    // 0x9abef4: r16 = Instance_BouncingScrollPhysics
    //     0x9abef4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0x9abef8: ldr             x16, [x16, #0x890]
    // 0x9abefc: ldur            lr, [fp, #-0x18]
    // 0x9abf00: stp             lr, x16, [SP]
    // 0x9abf04: mov             x1, x0
    // 0x9abf08: ldur            x2, [fp, #-0x38]
    // 0x9abf0c: ldur            x3, [fp, #-0x20]
    // 0x9abf10: ldur            x5, [fp, #-0x30]
    // 0x9abf14: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0x9abf14: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0x9abf18: ldr             x4, [x4, #0xe40]
    // 0x9abf1c: r0 = PageView.builder()
    //     0x9abf1c: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0x9abf20: r0 = AspectRatio()
    //     0x9abf20: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0x9abf24: d0 = 1.777778
    //     0x9abf24: add             x17, PP, #0x4e, lsl #12  ; [pp+0x4e8f0] IMM: double(1.7777777777777777) from 0x3ffc71c71c71c71c
    //     0x9abf28: ldr             d0, [x17, #0x8f0]
    // 0x9abf2c: stur            x0, [fp, #-0x18]
    // 0x9abf30: StoreField: r0->field_f = d0
    //     0x9abf30: stur            d0, [x0, #0xf]
    // 0x9abf34: ldur            x1, [fp, #-0x40]
    // 0x9abf38: StoreField: r0->field_b = r1
    //     0x9abf38: stur            w1, [x0, #0xb]
    // 0x9abf3c: r1 = Null
    //     0x9abf3c: mov             x1, NULL
    // 0x9abf40: r2 = 2
    //     0x9abf40: movz            x2, #0x2
    // 0x9abf44: r0 = AllocateArray()
    //     0x9abf44: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9abf48: mov             x2, x0
    // 0x9abf4c: ldur            x0, [fp, #-0x18]
    // 0x9abf50: stur            x2, [fp, #-0x20]
    // 0x9abf54: StoreField: r2->field_f = r0
    //     0x9abf54: stur            w0, [x2, #0xf]
    // 0x9abf58: r1 = <Widget>
    //     0x9abf58: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9abf5c: r0 = AllocateGrowableArray()
    //     0x9abf5c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9abf60: mov             x2, x0
    // 0x9abf64: ldur            x0, [fp, #-0x20]
    // 0x9abf68: stur            x2, [fp, #-0x18]
    // 0x9abf6c: StoreField: r2->field_f = r0
    //     0x9abf6c: stur            w0, [x2, #0xf]
    // 0x9abf70: r0 = 2
    //     0x9abf70: movz            x0, #0x2
    // 0x9abf74: StoreField: r2->field_b = r0
    //     0x9abf74: stur            w0, [x2, #0xb]
    // 0x9abf78: ldur            x1, [fp, #-8]
    // 0x9abf7c: LoadField: r3 = r1->field_b
    //     0x9abf7c: ldur            w3, [x1, #0xb]
    // 0x9abf80: DecompressPointer r3
    //     0x9abf80: add             x3, x3, HEAP, lsl #32
    // 0x9abf84: cmp             w3, NULL
    // 0x9abf88: b.eq            #0x9ac21c
    // 0x9abf8c: LoadField: r4 = r3->field_b
    //     0x9abf8c: ldur            w4, [x3, #0xb]
    // 0x9abf90: DecompressPointer r4
    //     0x9abf90: add             x4, x4, HEAP, lsl #32
    // 0x9abf94: cmp             w4, NULL
    // 0x9abf98: b.ne            #0x9abfa4
    // 0x9abf9c: r3 = Null
    //     0x9abf9c: mov             x3, NULL
    // 0x9abfa0: b               #0x9abfa8
    // 0x9abfa4: LoadField: r3 = r4->field_b
    //     0x9abfa4: ldur            w3, [x4, #0xb]
    // 0x9abfa8: cmp             w3, NULL
    // 0x9abfac: b.ne            #0x9abfb8
    // 0x9abfb0: r3 = 0
    //     0x9abfb0: movz            x3, #0
    // 0x9abfb4: b               #0x9abfc0
    // 0x9abfb8: r5 = LoadInt32Instr(r3)
    //     0x9abfb8: sbfx            x5, x3, #1, #0x1f
    // 0x9abfbc: mov             x3, x5
    // 0x9abfc0: cmp             x3, #1
    // 0x9abfc4: b.le            #0x9ac174
    // 0x9abfc8: cmp             w4, NULL
    // 0x9abfcc: b.ne            #0x9abfd8
    // 0x9abfd0: r3 = Null
    //     0x9abfd0: mov             x3, NULL
    // 0x9abfd4: b               #0x9abfdc
    // 0x9abfd8: LoadField: r3 = r4->field_b
    //     0x9abfd8: ldur            w3, [x4, #0xb]
    // 0x9abfdc: cmp             w3, NULL
    // 0x9abfe0: b.ne            #0x9abfec
    // 0x9abfe4: r3 = 0
    //     0x9abfe4: movz            x3, #0
    // 0x9abfe8: b               #0x9abff4
    // 0x9abfec: r4 = LoadInt32Instr(r3)
    //     0x9abfec: sbfx            x4, x3, #1, #0x1f
    // 0x9abff0: mov             x3, x4
    // 0x9abff4: stur            x3, [fp, #-0x50]
    // 0x9abff8: ArrayLoad: r4 = r1[0]  ; List_8
    //     0x9abff8: ldur            x4, [x1, #0x17]
    // 0x9abffc: ldur            x1, [fp, #-0x10]
    // 0x9ac000: stur            x4, [fp, #-0x48]
    // 0x9ac004: r0 = of()
    //     0x9ac004: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ac008: LoadField: r1 = r0->field_5b
    //     0x9ac008: ldur            w1, [x0, #0x5b]
    // 0x9ac00c: DecompressPointer r1
    //     0x9ac00c: add             x1, x1, HEAP, lsl #32
    // 0x9ac010: stur            x1, [fp, #-8]
    // 0x9ac014: r0 = CarouselIndicator()
    //     0x9ac014: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0x9ac018: mov             x3, x0
    // 0x9ac01c: ldur            x0, [fp, #-0x50]
    // 0x9ac020: stur            x3, [fp, #-0x10]
    // 0x9ac024: StoreField: r3->field_b = r0
    //     0x9ac024: stur            x0, [x3, #0xb]
    // 0x9ac028: ldur            x0, [fp, #-0x48]
    // 0x9ac02c: StoreField: r3->field_13 = r0
    //     0x9ac02c: stur            x0, [x3, #0x13]
    // 0x9ac030: ldur            x0, [fp, #-8]
    // 0x9ac034: StoreField: r3->field_1b = r0
    //     0x9ac034: stur            w0, [x3, #0x1b]
    // 0x9ac038: r0 = Instance_Color
    //     0x9ac038: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x9ac03c: ldr             x0, [x0, #0x90]
    // 0x9ac040: StoreField: r3->field_1f = r0
    //     0x9ac040: stur            w0, [x3, #0x1f]
    // 0x9ac044: r1 = Null
    //     0x9ac044: mov             x1, NULL
    // 0x9ac048: r2 = 2
    //     0x9ac048: movz            x2, #0x2
    // 0x9ac04c: r0 = AllocateArray()
    //     0x9ac04c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9ac050: mov             x2, x0
    // 0x9ac054: ldur            x0, [fp, #-0x10]
    // 0x9ac058: stur            x2, [fp, #-8]
    // 0x9ac05c: StoreField: r2->field_f = r0
    //     0x9ac05c: stur            w0, [x2, #0xf]
    // 0x9ac060: r1 = <Widget>
    //     0x9ac060: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9ac064: r0 = AllocateGrowableArray()
    //     0x9ac064: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9ac068: mov             x1, x0
    // 0x9ac06c: ldur            x0, [fp, #-8]
    // 0x9ac070: stur            x1, [fp, #-0x10]
    // 0x9ac074: StoreField: r1->field_f = r0
    //     0x9ac074: stur            w0, [x1, #0xf]
    // 0x9ac078: r0 = 2
    //     0x9ac078: movz            x0, #0x2
    // 0x9ac07c: StoreField: r1->field_b = r0
    //     0x9ac07c: stur            w0, [x1, #0xb]
    // 0x9ac080: r0 = Row()
    //     0x9ac080: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9ac084: mov             x1, x0
    // 0x9ac088: r0 = Instance_Axis
    //     0x9ac088: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9ac08c: stur            x1, [fp, #-8]
    // 0x9ac090: StoreField: r1->field_f = r0
    //     0x9ac090: stur            w0, [x1, #0xf]
    // 0x9ac094: r0 = Instance_MainAxisAlignment
    //     0x9ac094: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x9ac098: ldr             x0, [x0, #0xab0]
    // 0x9ac09c: StoreField: r1->field_13 = r0
    //     0x9ac09c: stur            w0, [x1, #0x13]
    // 0x9ac0a0: r0 = Instance_MainAxisSize
    //     0x9ac0a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9ac0a4: ldr             x0, [x0, #0xa10]
    // 0x9ac0a8: ArrayStore: r1[0] = r0  ; List_4
    //     0x9ac0a8: stur            w0, [x1, #0x17]
    // 0x9ac0ac: r2 = Instance_CrossAxisAlignment
    //     0x9ac0ac: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9ac0b0: ldr             x2, [x2, #0xa18]
    // 0x9ac0b4: StoreField: r1->field_1b = r2
    //     0x9ac0b4: stur            w2, [x1, #0x1b]
    // 0x9ac0b8: r3 = Instance_VerticalDirection
    //     0x9ac0b8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9ac0bc: ldr             x3, [x3, #0xa20]
    // 0x9ac0c0: StoreField: r1->field_23 = r3
    //     0x9ac0c0: stur            w3, [x1, #0x23]
    // 0x9ac0c4: r4 = Instance_Clip
    //     0x9ac0c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9ac0c8: ldr             x4, [x4, #0x38]
    // 0x9ac0cc: StoreField: r1->field_2b = r4
    //     0x9ac0cc: stur            w4, [x1, #0x2b]
    // 0x9ac0d0: StoreField: r1->field_2f = rZR
    //     0x9ac0d0: stur            xzr, [x1, #0x2f]
    // 0x9ac0d4: ldur            x5, [fp, #-0x10]
    // 0x9ac0d8: StoreField: r1->field_b = r5
    //     0x9ac0d8: stur            w5, [x1, #0xb]
    // 0x9ac0dc: r0 = Padding()
    //     0x9ac0dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9ac0e0: mov             x2, x0
    // 0x9ac0e4: r0 = Instance_EdgeInsets
    //     0x9ac0e4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x9ac0e8: ldr             x0, [x0, #0x770]
    // 0x9ac0ec: stur            x2, [fp, #-0x10]
    // 0x9ac0f0: StoreField: r2->field_f = r0
    //     0x9ac0f0: stur            w0, [x2, #0xf]
    // 0x9ac0f4: ldur            x0, [fp, #-8]
    // 0x9ac0f8: StoreField: r2->field_b = r0
    //     0x9ac0f8: stur            w0, [x2, #0xb]
    // 0x9ac0fc: ldur            x0, [fp, #-0x18]
    // 0x9ac100: LoadField: r1 = r0->field_b
    //     0x9ac100: ldur            w1, [x0, #0xb]
    // 0x9ac104: LoadField: r3 = r0->field_f
    //     0x9ac104: ldur            w3, [x0, #0xf]
    // 0x9ac108: DecompressPointer r3
    //     0x9ac108: add             x3, x3, HEAP, lsl #32
    // 0x9ac10c: LoadField: r4 = r3->field_b
    //     0x9ac10c: ldur            w4, [x3, #0xb]
    // 0x9ac110: r3 = LoadInt32Instr(r1)
    //     0x9ac110: sbfx            x3, x1, #1, #0x1f
    // 0x9ac114: stur            x3, [fp, #-0x48]
    // 0x9ac118: r1 = LoadInt32Instr(r4)
    //     0x9ac118: sbfx            x1, x4, #1, #0x1f
    // 0x9ac11c: cmp             x3, x1
    // 0x9ac120: b.ne            #0x9ac12c
    // 0x9ac124: mov             x1, x0
    // 0x9ac128: r0 = _growToNextCapacity()
    //     0x9ac128: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9ac12c: ldur            x2, [fp, #-0x18]
    // 0x9ac130: ldur            x3, [fp, #-0x48]
    // 0x9ac134: add             x0, x3, #1
    // 0x9ac138: lsl             x1, x0, #1
    // 0x9ac13c: StoreField: r2->field_b = r1
    //     0x9ac13c: stur            w1, [x2, #0xb]
    // 0x9ac140: LoadField: r1 = r2->field_f
    //     0x9ac140: ldur            w1, [x2, #0xf]
    // 0x9ac144: DecompressPointer r1
    //     0x9ac144: add             x1, x1, HEAP, lsl #32
    // 0x9ac148: ldur            x0, [fp, #-0x10]
    // 0x9ac14c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9ac14c: add             x25, x1, x3, lsl #2
    //     0x9ac150: add             x25, x25, #0xf
    //     0x9ac154: str             w0, [x25]
    //     0x9ac158: tbz             w0, #0, #0x9ac174
    //     0x9ac15c: ldurb           w16, [x1, #-1]
    //     0x9ac160: ldurb           w17, [x0, #-1]
    //     0x9ac164: and             x16, x17, x16, lsr #2
    //     0x9ac168: tst             x16, HEAP, lsr #32
    //     0x9ac16c: b.eq            #0x9ac174
    //     0x9ac170: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9ac174: r0 = Column()
    //     0x9ac174: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9ac178: mov             x1, x0
    // 0x9ac17c: r0 = Instance_Axis
    //     0x9ac17c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9ac180: stur            x1, [fp, #-8]
    // 0x9ac184: StoreField: r1->field_f = r0
    //     0x9ac184: stur            w0, [x1, #0xf]
    // 0x9ac188: r0 = Instance_MainAxisAlignment
    //     0x9ac188: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9ac18c: ldr             x0, [x0, #0xa08]
    // 0x9ac190: StoreField: r1->field_13 = r0
    //     0x9ac190: stur            w0, [x1, #0x13]
    // 0x9ac194: r0 = Instance_MainAxisSize
    //     0x9ac194: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9ac198: ldr             x0, [x0, #0xa10]
    // 0x9ac19c: ArrayStore: r1[0] = r0  ; List_4
    //     0x9ac19c: stur            w0, [x1, #0x17]
    // 0x9ac1a0: r0 = Instance_CrossAxisAlignment
    //     0x9ac1a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9ac1a4: ldr             x0, [x0, #0xa18]
    // 0x9ac1a8: StoreField: r1->field_1b = r0
    //     0x9ac1a8: stur            w0, [x1, #0x1b]
    // 0x9ac1ac: r0 = Instance_VerticalDirection
    //     0x9ac1ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9ac1b0: ldr             x0, [x0, #0xa20]
    // 0x9ac1b4: StoreField: r1->field_23 = r0
    //     0x9ac1b4: stur            w0, [x1, #0x23]
    // 0x9ac1b8: r0 = Instance_Clip
    //     0x9ac1b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9ac1bc: ldr             x0, [x0, #0x38]
    // 0x9ac1c0: StoreField: r1->field_2b = r0
    //     0x9ac1c0: stur            w0, [x1, #0x2b]
    // 0x9ac1c4: StoreField: r1->field_2f = rZR
    //     0x9ac1c4: stur            xzr, [x1, #0x2f]
    // 0x9ac1c8: ldur            x0, [fp, #-0x18]
    // 0x9ac1cc: StoreField: r1->field_b = r0
    //     0x9ac1cc: stur            w0, [x1, #0xb]
    // 0x9ac1d0: r0 = Container()
    //     0x9ac1d0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9ac1d4: stur            x0, [fp, #-0x10]
    // 0x9ac1d8: ldur            x16, [fp, #-0x28]
    // 0x9ac1dc: ldur            lr, [fp, #-8]
    // 0x9ac1e0: stp             lr, x16, [SP]
    // 0x9ac1e4: mov             x1, x0
    // 0x9ac1e8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, margin, 0x1, null]
    //     0x9ac1e8: add             x4, PP, #0x53, lsl #12  ; [pp+0x53400] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "margin", 0x1, Null]
    //     0x9ac1ec: ldr             x4, [x4, #0x400]
    // 0x9ac1f0: r0 = Container()
    //     0x9ac1f0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9ac1f4: ldur            x0, [fp, #-0x10]
    // 0x9ac1f8: LeaveFrame
    //     0x9ac1f8: mov             SP, fp
    //     0x9ac1fc: ldp             fp, lr, [SP], #0x10
    // 0x9ac200: ret
    //     0x9ac200: ret             
    // 0x9ac204: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ac204: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ac208: b               #0x9abe54
    // 0x9ac20c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ac20c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ac210: r9 = _pageController
    //     0x9ac210: add             x9, PP, #0x5b, lsl #12  ; [pp+0x5bba8] Field <_ProductVideoCarouselItemViewState@1250187829._pageController@1250187829>: late (offset: 0x14)
    //     0x9ac214: ldr             x9, [x9, #0xba8]
    // 0x9ac218: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9ac218: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9ac21c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ac21c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0x9ac220, size: 0x84
    // 0x9ac220: EnterFrame
    //     0x9ac220: stp             fp, lr, [SP, #-0x10]!
    //     0x9ac224: mov             fp, SP
    // 0x9ac228: AllocStack(0x10)
    //     0x9ac228: sub             SP, SP, #0x10
    // 0x9ac22c: SetupParameters()
    //     0x9ac22c: ldr             x0, [fp, #0x18]
    //     0x9ac230: ldur            w1, [x0, #0x17]
    //     0x9ac234: add             x1, x1, HEAP, lsl #32
    //     0x9ac238: stur            x1, [fp, #-8]
    // 0x9ac23c: CheckStackOverflow
    //     0x9ac23c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ac240: cmp             SP, x16
    //     0x9ac244: b.ls            #0x9ac29c
    // 0x9ac248: r1 = 1
    //     0x9ac248: movz            x1, #0x1
    // 0x9ac24c: r0 = AllocateContext()
    //     0x9ac24c: bl              #0x16f6108  ; AllocateContextStub
    // 0x9ac250: mov             x1, x0
    // 0x9ac254: ldur            x0, [fp, #-8]
    // 0x9ac258: StoreField: r1->field_b = r0
    //     0x9ac258: stur            w0, [x1, #0xb]
    // 0x9ac25c: ldr             x2, [fp, #0x10]
    // 0x9ac260: StoreField: r1->field_f = r2
    //     0x9ac260: stur            w2, [x1, #0xf]
    // 0x9ac264: LoadField: r3 = r0->field_f
    //     0x9ac264: ldur            w3, [x0, #0xf]
    // 0x9ac268: DecompressPointer r3
    //     0x9ac268: add             x3, x3, HEAP, lsl #32
    // 0x9ac26c: mov             x2, x1
    // 0x9ac270: stur            x3, [fp, #-0x10]
    // 0x9ac274: r1 = Function '<anonymous closure>':.
    //     0x9ac274: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bbc0] AnonymousClosure: (0x98e9d8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0x9ac278: ldr             x1, [x1, #0xbc0]
    // 0x9ac27c: r0 = AllocateClosure()
    //     0x9ac27c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9ac280: ldur            x1, [fp, #-0x10]
    // 0x9ac284: mov             x2, x0
    // 0x9ac288: r0 = setState()
    //     0x9ac288: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9ac28c: r0 = Null
    //     0x9ac28c: mov             x0, NULL
    // 0x9ac290: LeaveFrame
    //     0x9ac290: mov             SP, fp
    //     0x9ac294: ldp             fp, lr, [SP], #0x10
    // 0x9ac298: ret
    //     0x9ac298: ret             
    // 0x9ac29c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ac29c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ac2a0: b               #0x9ac248
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc86a2c, size: 0x54
    // 0xc86a2c: EnterFrame
    //     0xc86a2c: stp             fp, lr, [SP, #-0x10]!
    //     0xc86a30: mov             fp, SP
    // 0xc86a34: CheckStackOverflow
    //     0xc86a34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc86a38: cmp             SP, x16
    //     0xc86a3c: b.ls            #0xc86a6c
    // 0xc86a40: LoadField: r0 = r1->field_13
    //     0xc86a40: ldur            w0, [x1, #0x13]
    // 0xc86a44: DecompressPointer r0
    //     0xc86a44: add             x0, x0, HEAP, lsl #32
    // 0xc86a48: r16 = Sentinel
    //     0xc86a48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc86a4c: cmp             w0, w16
    // 0xc86a50: b.eq            #0xc86a74
    // 0xc86a54: mov             x1, x0
    // 0xc86a58: r0 = dispose()
    //     0xc86a58: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc86a5c: r0 = Null
    //     0xc86a5c: mov             x0, NULL
    // 0xc86a60: LeaveFrame
    //     0xc86a60: mov             SP, fp
    //     0xc86a64: ldp             fp, lr, [SP], #0x10
    // 0xc86a68: ret
    //     0xc86a68: ret             
    // 0xc86a6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc86a6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc86a70: b               #0xc86a40
    // 0xc86a74: r9 = _pageController
    //     0xc86a74: add             x9, PP, #0x5b, lsl #12  ; [pp+0x5bba8] Field <_ProductVideoCarouselItemViewState@1250187829._pageController@1250187829>: late (offset: 0x14)
    //     0xc86a78: ldr             x9, [x9, #0xba8]
    // 0xc86a7c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc86a7c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4303, size: 0x18, field offset: 0xc
//   const constructor, 
class ProductVideoCarouselItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc79de0, size: 0x30
    // 0xc79de0: EnterFrame
    //     0xc79de0: stp             fp, lr, [SP, #-0x10]!
    //     0xc79de4: mov             fp, SP
    // 0xc79de8: mov             x0, x1
    // 0xc79dec: r1 = <ProductVideoCarouselItemView>
    //     0xc79dec: add             x1, PP, #0x49, lsl #12  ; [pp+0x492c0] TypeArguments: <ProductVideoCarouselItemView>
    //     0xc79df0: ldr             x1, [x1, #0x2c0]
    // 0xc79df4: r0 = _ProductVideoCarouselItemViewState()
    //     0xc79df4: bl              #0xc79e10  ; Allocate_ProductVideoCarouselItemViewStateStub -> _ProductVideoCarouselItemViewState (size=0x20)
    // 0xc79df8: r1 = Sentinel
    //     0xc79df8: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc79dfc: StoreField: r0->field_13 = r1
    //     0xc79dfc: stur            w1, [x0, #0x13]
    // 0xc79e00: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc79e00: stur            xzr, [x0, #0x17]
    // 0xc79e04: LeaveFrame
    //     0xc79e04: mov             SP, fp
    //     0xc79e08: ldp             fp, lr, [SP], #0x10
    // 0xc79e0c: ret
    //     0xc79e0c: ret             
  }
}
