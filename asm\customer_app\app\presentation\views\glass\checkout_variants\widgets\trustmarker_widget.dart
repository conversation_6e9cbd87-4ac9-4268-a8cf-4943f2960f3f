// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/trustmarker_widget.dart

// class id: 1049379, size: 0x8
class :: {
}

// class id: 3351, size: 0x14, field offset: 0x14
class _TrustMarkerWidgetState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x941600, size: 0x130
    // 0x941600: EnterFrame
    //     0x941600: stp             fp, lr, [SP, #-0x10]!
    //     0x941604: mov             fp, SP
    // 0x941608: AllocStack(0x18)
    //     0x941608: sub             SP, SP, #0x18
    // 0x94160c: SetupParameters(_TrustMarkerWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x94160c: stur            x1, [fp, #-8]
    // 0x941610: CheckStackOverflow
    //     0x941610: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x941614: cmp             SP, x16
    //     0x941618: b.ls            #0x941724
    // 0x94161c: r1 = 1
    //     0x94161c: movz            x1, #0x1
    // 0x941620: r0 = AllocateContext()
    //     0x941620: bl              #0x16f6108  ; AllocateContextStub
    // 0x941624: mov             x1, x0
    // 0x941628: ldur            x0, [fp, #-8]
    // 0x94162c: StoreField: r1->field_f = r0
    //     0x94162c: stur            w0, [x1, #0xf]
    // 0x941630: r0 = LoadStaticField(0x878)
    //     0x941630: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x941634: ldr             x0, [x0, #0x10f0]
    // 0x941638: cmp             w0, NULL
    // 0x94163c: b.eq            #0x94172c
    // 0x941640: LoadField: r3 = r0->field_53
    //     0x941640: ldur            w3, [x0, #0x53]
    // 0x941644: DecompressPointer r3
    //     0x941644: add             x3, x3, HEAP, lsl #32
    // 0x941648: stur            x3, [fp, #-0x10]
    // 0x94164c: LoadField: r0 = r3->field_7
    //     0x94164c: ldur            w0, [x3, #7]
    // 0x941650: DecompressPointer r0
    //     0x941650: add             x0, x0, HEAP, lsl #32
    // 0x941654: mov             x2, x1
    // 0x941658: stur            x0, [fp, #-8]
    // 0x94165c: r1 = Function '<anonymous closure>':.
    //     0x94165c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56980] AnonymousClosure: (0x9071e8), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/trustmarker_widget.dart] _TrustMarkerWidgetState::initState (0x9488e8)
    //     0x941660: ldr             x1, [x1, #0x980]
    // 0x941664: r0 = AllocateClosure()
    //     0x941664: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x941668: ldur            x2, [fp, #-8]
    // 0x94166c: mov             x3, x0
    // 0x941670: r1 = Null
    //     0x941670: mov             x1, NULL
    // 0x941674: stur            x3, [fp, #-8]
    // 0x941678: cmp             w2, NULL
    // 0x94167c: b.eq            #0x94169c
    // 0x941680: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x941680: ldur            w4, [x2, #0x17]
    // 0x941684: DecompressPointer r4
    //     0x941684: add             x4, x4, HEAP, lsl #32
    // 0x941688: r8 = X0
    //     0x941688: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x94168c: LoadField: r9 = r4->field_7
    //     0x94168c: ldur            x9, [x4, #7]
    // 0x941690: r3 = Null
    //     0x941690: add             x3, PP, #0x56, lsl #12  ; [pp+0x56988] Null
    //     0x941694: ldr             x3, [x3, #0x988]
    // 0x941698: blr             x9
    // 0x94169c: ldur            x0, [fp, #-0x10]
    // 0x9416a0: LoadField: r1 = r0->field_b
    //     0x9416a0: ldur            w1, [x0, #0xb]
    // 0x9416a4: LoadField: r2 = r0->field_f
    //     0x9416a4: ldur            w2, [x0, #0xf]
    // 0x9416a8: DecompressPointer r2
    //     0x9416a8: add             x2, x2, HEAP, lsl #32
    // 0x9416ac: LoadField: r3 = r2->field_b
    //     0x9416ac: ldur            w3, [x2, #0xb]
    // 0x9416b0: r2 = LoadInt32Instr(r1)
    //     0x9416b0: sbfx            x2, x1, #1, #0x1f
    // 0x9416b4: stur            x2, [fp, #-0x18]
    // 0x9416b8: r1 = LoadInt32Instr(r3)
    //     0x9416b8: sbfx            x1, x3, #1, #0x1f
    // 0x9416bc: cmp             x2, x1
    // 0x9416c0: b.ne            #0x9416cc
    // 0x9416c4: mov             x1, x0
    // 0x9416c8: r0 = _growToNextCapacity()
    //     0x9416c8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9416cc: ldur            x2, [fp, #-0x10]
    // 0x9416d0: ldur            x3, [fp, #-0x18]
    // 0x9416d4: add             x4, x3, #1
    // 0x9416d8: lsl             x5, x4, #1
    // 0x9416dc: StoreField: r2->field_b = r5
    //     0x9416dc: stur            w5, [x2, #0xb]
    // 0x9416e0: LoadField: r1 = r2->field_f
    //     0x9416e0: ldur            w1, [x2, #0xf]
    // 0x9416e4: DecompressPointer r1
    //     0x9416e4: add             x1, x1, HEAP, lsl #32
    // 0x9416e8: ldur            x0, [fp, #-8]
    // 0x9416ec: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9416ec: add             x25, x1, x3, lsl #2
    //     0x9416f0: add             x25, x25, #0xf
    //     0x9416f4: str             w0, [x25]
    //     0x9416f8: tbz             w0, #0, #0x941714
    //     0x9416fc: ldurb           w16, [x1, #-1]
    //     0x941700: ldurb           w17, [x0, #-1]
    //     0x941704: and             x16, x17, x16, lsr #2
    //     0x941708: tst             x16, HEAP, lsr #32
    //     0x94170c: b.eq            #0x941714
    //     0x941710: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x941714: r0 = Null
    //     0x941714: mov             x0, NULL
    // 0x941718: LeaveFrame
    //     0x941718: mov             SP, fp
    //     0x94171c: ldp             fp, lr, [SP], #0x10
    // 0x941720: ret
    //     0x941720: ret             
    // 0x941724: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x941724: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x941728: b               #0x94161c
    // 0x94172c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94172c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb53688, size: 0x210
    // 0xb53688: EnterFrame
    //     0xb53688: stp             fp, lr, [SP, #-0x10]!
    //     0xb5368c: mov             fp, SP
    // 0xb53690: AllocStack(0x48)
    //     0xb53690: sub             SP, SP, #0x48
    // 0xb53694: SetupParameters(_TrustMarkerWidgetState this /* r1 => r1, fp-0x8 */)
    //     0xb53694: stur            x1, [fp, #-8]
    // 0xb53698: CheckStackOverflow
    //     0xb53698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5369c: cmp             SP, x16
    //     0xb536a0: b.ls            #0xb53858
    // 0xb536a4: r1 = 1
    //     0xb536a4: movz            x1, #0x1
    // 0xb536a8: r0 = AllocateContext()
    //     0xb536a8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb536ac: mov             x1, x0
    // 0xb536b0: ldur            x0, [fp, #-8]
    // 0xb536b4: stur            x1, [fp, #-0x10]
    // 0xb536b8: StoreField: r1->field_f = r0
    //     0xb536b8: stur            w0, [x1, #0xf]
    // 0xb536bc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb536bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb536c0: ldr             x0, [x0, #0x1c80]
    //     0xb536c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb536c8: cmp             w0, w16
    //     0xb536cc: b.ne            #0xb536d8
    //     0xb536d0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb536d4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb536d8: r0 = GetNavigation.size()
    //     0xb536d8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb536dc: LoadField: d0 = r0->field_f
    //     0xb536dc: ldur            d0, [x0, #0xf]
    // 0xb536e0: d1 = 0.160000
    //     0xb536e0: add             x17, PP, #0x54, lsl #12  ; [pp+0x54288] IMM: double(0.16) from 0x3fc47ae147ae147b
    //     0xb536e4: ldr             d1, [x17, #0x288]
    // 0xb536e8: fmul            d2, d0, d1
    // 0xb536ec: stur            d2, [fp, #-0x28]
    // 0xb536f0: r0 = GetNavigation.size()
    //     0xb536f0: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb536f4: LoadField: d0 = r0->field_7
    //     0xb536f4: ldur            d0, [x0, #7]
    // 0xb536f8: ldur            x0, [fp, #-8]
    // 0xb536fc: stur            d0, [fp, #-0x30]
    // 0xb53700: LoadField: r1 = r0->field_b
    //     0xb53700: ldur            w1, [x0, #0xb]
    // 0xb53704: DecompressPointer r1
    //     0xb53704: add             x1, x1, HEAP, lsl #32
    // 0xb53708: cmp             w1, NULL
    // 0xb5370c: b.eq            #0xb53860
    // 0xb53710: LoadField: r0 = r1->field_b
    //     0xb53710: ldur            w0, [x1, #0xb]
    // 0xb53714: DecompressPointer r0
    //     0xb53714: add             x0, x0, HEAP, lsl #32
    // 0xb53718: LoadField: r1 = r0->field_6b
    //     0xb53718: ldur            w1, [x0, #0x6b]
    // 0xb5371c: DecompressPointer r1
    //     0xb5371c: add             x1, x1, HEAP, lsl #32
    // 0xb53720: cmp             w1, NULL
    // 0xb53724: b.ne            #0xb53730
    // 0xb53728: r0 = Null
    //     0xb53728: mov             x0, NULL
    // 0xb5372c: b               #0xb53734
    // 0xb53730: LoadField: r0 = r1->field_b
    //     0xb53730: ldur            w0, [x1, #0xb]
    // 0xb53734: cmp             w0, NULL
    // 0xb53738: b.ne            #0xb53744
    // 0xb5373c: r3 = 0
    //     0xb5373c: movz            x3, #0
    // 0xb53740: b               #0xb5374c
    // 0xb53744: r1 = LoadInt32Instr(r0)
    //     0xb53744: sbfx            x1, x0, #1, #0x1f
    // 0xb53748: mov             x3, x1
    // 0xb5374c: ldur            d1, [fp, #-0x28]
    // 0xb53750: ldur            x2, [fp, #-0x10]
    // 0xb53754: stur            x3, [fp, #-0x18]
    // 0xb53758: r1 = Function '<anonymous closure>':.
    //     0xb53758: add             x1, PP, #0x56, lsl #12  ; [pp+0x56960] AnonymousClosure: (0xb53898), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/trustmarker_widget.dart] _TrustMarkerWidgetState::build (0xb53688)
    //     0xb5375c: ldr             x1, [x1, #0x960]
    // 0xb53760: r0 = AllocateClosure()
    //     0xb53760: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb53764: r1 = Function '<anonymous closure>':.
    //     0xb53764: add             x1, PP, #0x56, lsl #12  ; [pp+0x56968] AnonymousClosure: (0x998918), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/trustmarker_widget.dart] _TrustMarkerWidgetState::build (0xbd43d4)
    //     0xb53768: ldr             x1, [x1, #0x968]
    // 0xb5376c: r2 = Null
    //     0xb5376c: mov             x2, NULL
    // 0xb53770: stur            x0, [fp, #-8]
    // 0xb53774: r0 = AllocateClosure()
    //     0xb53774: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb53778: stur            x0, [fp, #-0x10]
    // 0xb5377c: r0 = ListView()
    //     0xb5377c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb53780: stur            x0, [fp, #-0x20]
    // 0xb53784: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb53784: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb53788: ldr             x16, [x16, #0x1c8]
    // 0xb5378c: r30 = true
    //     0xb5378c: add             lr, NULL, #0x20  ; true
    // 0xb53790: stp             lr, x16, [SP, #8]
    // 0xb53794: r16 = Instance_Axis
    //     0xb53794: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb53798: str             x16, [SP]
    // 0xb5379c: mov             x1, x0
    // 0xb537a0: ldur            x2, [fp, #-8]
    // 0xb537a4: ldur            x3, [fp, #-0x18]
    // 0xb537a8: ldur            x5, [fp, #-0x10]
    // 0xb537ac: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x4, scrollDirection, 0x6, shrinkWrap, 0x5, null]
    //     0xb537ac: add             x4, PP, #0x33, lsl #12  ; [pp+0x33898] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x4, "scrollDirection", 0x6, "shrinkWrap", 0x5, Null]
    //     0xb537b0: ldr             x4, [x4, #0x898]
    // 0xb537b4: r0 = ListView.separated()
    //     0xb537b4: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb537b8: r0 = Center()
    //     0xb537b8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb537bc: mov             x1, x0
    // 0xb537c0: r0 = Instance_Alignment
    //     0xb537c0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb537c4: ldr             x0, [x0, #0xb10]
    // 0xb537c8: stur            x1, [fp, #-0x10]
    // 0xb537cc: StoreField: r1->field_f = r0
    //     0xb537cc: stur            w0, [x1, #0xf]
    // 0xb537d0: ldur            x0, [fp, #-0x20]
    // 0xb537d4: StoreField: r1->field_b = r0
    //     0xb537d4: stur            w0, [x1, #0xb]
    // 0xb537d8: ldur            d0, [fp, #-0x30]
    // 0xb537dc: r0 = inline_Allocate_Double()
    //     0xb537dc: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb537e0: add             x0, x0, #0x10
    //     0xb537e4: cmp             x2, x0
    //     0xb537e8: b.ls            #0xb53864
    //     0xb537ec: str             x0, [THR, #0x50]  ; THR::top
    //     0xb537f0: sub             x0, x0, #0xf
    //     0xb537f4: movz            x2, #0xe15c
    //     0xb537f8: movk            x2, #0x3, lsl #16
    //     0xb537fc: stur            x2, [x0, #-1]
    // 0xb53800: StoreField: r0->field_7 = d0
    //     0xb53800: stur            d0, [x0, #7]
    // 0xb53804: stur            x0, [fp, #-8]
    // 0xb53808: r0 = SizedBox()
    //     0xb53808: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb5380c: ldur            x1, [fp, #-8]
    // 0xb53810: StoreField: r0->field_f = r1
    //     0xb53810: stur            w1, [x0, #0xf]
    // 0xb53814: ldur            d0, [fp, #-0x28]
    // 0xb53818: r1 = inline_Allocate_Double()
    //     0xb53818: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb5381c: add             x1, x1, #0x10
    //     0xb53820: cmp             x2, x1
    //     0xb53824: b.ls            #0xb5387c
    //     0xb53828: str             x1, [THR, #0x50]  ; THR::top
    //     0xb5382c: sub             x1, x1, #0xf
    //     0xb53830: movz            x2, #0xe15c
    //     0xb53834: movk            x2, #0x3, lsl #16
    //     0xb53838: stur            x2, [x1, #-1]
    // 0xb5383c: StoreField: r1->field_7 = d0
    //     0xb5383c: stur            d0, [x1, #7]
    // 0xb53840: StoreField: r0->field_13 = r1
    //     0xb53840: stur            w1, [x0, #0x13]
    // 0xb53844: ldur            x1, [fp, #-0x10]
    // 0xb53848: StoreField: r0->field_b = r1
    //     0xb53848: stur            w1, [x0, #0xb]
    // 0xb5384c: LeaveFrame
    //     0xb5384c: mov             SP, fp
    //     0xb53850: ldp             fp, lr, [SP], #0x10
    // 0xb53854: ret
    //     0xb53854: ret             
    // 0xb53858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb53858: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5385c: b               #0xb536a4
    // 0xb53860: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb53860: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb53864: SaveReg d0
    //     0xb53864: str             q0, [SP, #-0x10]!
    // 0xb53868: SaveReg r1
    //     0xb53868: str             x1, [SP, #-8]!
    // 0xb5386c: r0 = AllocateDouble()
    //     0xb5386c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb53870: RestoreReg r1
    //     0xb53870: ldr             x1, [SP], #8
    // 0xb53874: RestoreReg d0
    //     0xb53874: ldr             q0, [SP], #0x10
    // 0xb53878: b               #0xb53800
    // 0xb5387c: SaveReg d0
    //     0xb5387c: str             q0, [SP, #-0x10]!
    // 0xb53880: SaveReg r0
    //     0xb53880: str             x0, [SP, #-8]!
    // 0xb53884: r0 = AllocateDouble()
    //     0xb53884: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb53888: mov             x1, x0
    // 0xb5388c: RestoreReg r0
    //     0xb5388c: ldr             x0, [SP], #8
    // 0xb53890: RestoreReg d0
    //     0xb53890: ldr             q0, [SP], #0x10
    // 0xb53894: b               #0xb5383c
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb53898, size: 0x7c
    // 0xb53898: EnterFrame
    //     0xb53898: stp             fp, lr, [SP, #-0x10]!
    //     0xb5389c: mov             fp, SP
    // 0xb538a0: ldr             x0, [fp, #0x20]
    // 0xb538a4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb538a4: ldur            w1, [x0, #0x17]
    // 0xb538a8: DecompressPointer r1
    //     0xb538a8: add             x1, x1, HEAP, lsl #32
    // 0xb538ac: CheckStackOverflow
    //     0xb538ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb538b0: cmp             SP, x16
    //     0xb538b4: b.ls            #0xb53908
    // 0xb538b8: LoadField: r0 = r1->field_f
    //     0xb538b8: ldur            w0, [x1, #0xf]
    // 0xb538bc: DecompressPointer r0
    //     0xb538bc: add             x0, x0, HEAP, lsl #32
    // 0xb538c0: LoadField: r1 = r0->field_b
    //     0xb538c0: ldur            w1, [x0, #0xb]
    // 0xb538c4: DecompressPointer r1
    //     0xb538c4: add             x1, x1, HEAP, lsl #32
    // 0xb538c8: cmp             w1, NULL
    // 0xb538cc: b.eq            #0xb53910
    // 0xb538d0: LoadField: r2 = r1->field_b
    //     0xb538d0: ldur            w2, [x1, #0xb]
    // 0xb538d4: DecompressPointer r2
    //     0xb538d4: add             x2, x2, HEAP, lsl #32
    // 0xb538d8: LoadField: r1 = r2->field_6b
    //     0xb538d8: ldur            w1, [x2, #0x6b]
    // 0xb538dc: DecompressPointer r1
    //     0xb538dc: add             x1, x1, HEAP, lsl #32
    // 0xb538e0: ldr             x2, [fp, #0x10]
    // 0xb538e4: r3 = LoadInt32Instr(r2)
    //     0xb538e4: sbfx            x3, x2, #1, #0x1f
    //     0xb538e8: tbz             w2, #0, #0xb538f0
    //     0xb538ec: ldur            x3, [x2, #7]
    // 0xb538f0: mov             x2, x1
    // 0xb538f4: mov             x1, x0
    // 0xb538f8: r0 = trustMarkerGlassTheme()
    //     0xb538f8: bl              #0xb53914  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/trustmarker_widget.dart] _TrustMarkerWidgetState::trustMarkerGlassTheme
    // 0xb538fc: LeaveFrame
    //     0xb538fc: mov             SP, fp
    //     0xb53900: ldp             fp, lr, [SP], #0x10
    // 0xb53904: ret
    //     0xb53904: ret             
    // 0xb53908: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb53908: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5390c: b               #0xb538b8
    // 0xb53910: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb53910: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ trustMarkerGlassTheme(/* No info */) {
    // ** addr: 0xb53914, size: 0x2c0
    // 0xb53914: EnterFrame
    //     0xb53914: stp             fp, lr, [SP, #-0x10]!
    //     0xb53918: mov             fp, SP
    // 0xb5391c: AllocStack(0x60)
    //     0xb5391c: sub             SP, SP, #0x60
    // 0xb53920: SetupParameters(_TrustMarkerWidgetState this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xb53920: mov             x5, x1
    //     0xb53924: mov             x4, x2
    //     0xb53928: stur            x1, [fp, #-0x10]
    //     0xb5392c: stur            x2, [fp, #-0x18]
    //     0xb53930: stur            x3, [fp, #-0x20]
    // 0xb53934: CheckStackOverflow
    //     0xb53934: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb53938: cmp             SP, x16
    //     0xb5393c: b.ls            #0xb53bc0
    // 0xb53940: cmp             w4, NULL
    // 0xb53944: b.ne            #0xb53950
    // 0xb53948: r0 = Null
    //     0xb53948: mov             x0, NULL
    // 0xb5394c: b               #0xb53984
    // 0xb53950: LoadField: r0 = r4->field_b
    //     0xb53950: ldur            w0, [x4, #0xb]
    // 0xb53954: r1 = LoadInt32Instr(r0)
    //     0xb53954: sbfx            x1, x0, #1, #0x1f
    // 0xb53958: mov             x0, x1
    // 0xb5395c: mov             x1, x3
    // 0xb53960: cmp             x1, x0
    // 0xb53964: b.hs            #0xb53bc8
    // 0xb53968: LoadField: r0 = r4->field_f
    //     0xb53968: ldur            w0, [x4, #0xf]
    // 0xb5396c: DecompressPointer r0
    //     0xb5396c: add             x0, x0, HEAP, lsl #32
    // 0xb53970: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb53970: add             x16, x0, x3, lsl #2
    //     0xb53974: ldur            w1, [x16, #0xf]
    // 0xb53978: DecompressPointer r1
    //     0xb53978: add             x1, x1, HEAP, lsl #32
    // 0xb5397c: LoadField: r0 = r1->field_b
    //     0xb5397c: ldur            w0, [x1, #0xb]
    // 0xb53980: DecompressPointer r0
    //     0xb53980: add             x0, x0, HEAP, lsl #32
    // 0xb53984: cmp             w0, NULL
    // 0xb53988: b.ne            #0xb53990
    // 0xb5398c: r0 = ""
    //     0xb5398c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb53990: stur            x0, [fp, #-8]
    // 0xb53994: r1 = Function '<anonymous closure>':.
    //     0xb53994: add             x1, PP, #0x56, lsl #12  ; [pp+0x56970] AnonymousClosure: (0xa2c264), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/trustmarker_widget.dart] _TrustMarkerWidgetState::trustMarkerLineTheme (0xa2c384)
    //     0xb53998: ldr             x1, [x1, #0x970]
    // 0xb5399c: r2 = Null
    //     0xb5399c: mov             x2, NULL
    // 0xb539a0: r0 = AllocateClosure()
    //     0xb539a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb539a4: r1 = Function '<anonymous closure>':.
    //     0xb539a4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56978] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb539a8: ldr             x1, [x1, #0x978]
    // 0xb539ac: r2 = Null
    //     0xb539ac: mov             x2, NULL
    // 0xb539b0: stur            x0, [fp, #-0x28]
    // 0xb539b4: r0 = AllocateClosure()
    //     0xb539b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb539b8: stur            x0, [fp, #-0x30]
    // 0xb539bc: r0 = CachedNetworkImage()
    //     0xb539bc: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb539c0: stur            x0, [fp, #-0x38]
    // 0xb539c4: r16 = 64.000000
    //     0xb539c4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb539c8: ldr             x16, [x16, #0x838]
    // 0xb539cc: r30 = 64.000000
    //     0xb539cc: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb539d0: ldr             lr, [lr, #0x838]
    // 0xb539d4: stp             lr, x16, [SP, #0x18]
    // 0xb539d8: ldur            x16, [fp, #-0x28]
    // 0xb539dc: ldur            lr, [fp, #-0x30]
    // 0xb539e0: stp             lr, x16, [SP, #8]
    // 0xb539e4: r16 = Instance_BoxFit
    //     0xb539e4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb539e8: ldr             x16, [x16, #0xb18]
    // 0xb539ec: str             x16, [SP]
    // 0xb539f0: mov             x1, x0
    // 0xb539f4: ldur            x2, [fp, #-8]
    // 0xb539f8: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x5, fit, 0x6, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xb539f8: add             x4, PP, #0x54, lsl #12  ; [pp+0x542b0] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x5, "fit", 0x6, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xb539fc: ldr             x4, [x4, #0x2b0]
    // 0xb53a00: r0 = CachedNetworkImage()
    //     0xb53a00: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb53a04: ldur            x2, [fp, #-0x18]
    // 0xb53a08: cmp             w2, NULL
    // 0xb53a0c: b.ne            #0xb53a18
    // 0xb53a10: r2 = Null
    //     0xb53a10: mov             x2, NULL
    // 0xb53a14: b               #0xb53a54
    // 0xb53a18: ldur            x3, [fp, #-0x20]
    // 0xb53a1c: LoadField: r0 = r2->field_b
    //     0xb53a1c: ldur            w0, [x2, #0xb]
    // 0xb53a20: r1 = LoadInt32Instr(r0)
    //     0xb53a20: sbfx            x1, x0, #1, #0x1f
    // 0xb53a24: mov             x0, x1
    // 0xb53a28: mov             x1, x3
    // 0xb53a2c: cmp             x1, x0
    // 0xb53a30: b.hs            #0xb53bcc
    // 0xb53a34: LoadField: r0 = r2->field_f
    //     0xb53a34: ldur            w0, [x2, #0xf]
    // 0xb53a38: DecompressPointer r0
    //     0xb53a38: add             x0, x0, HEAP, lsl #32
    // 0xb53a3c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb53a3c: add             x16, x0, x3, lsl #2
    //     0xb53a40: ldur            w1, [x16, #0xf]
    // 0xb53a44: DecompressPointer r1
    //     0xb53a44: add             x1, x1, HEAP, lsl #32
    // 0xb53a48: LoadField: r0 = r1->field_7
    //     0xb53a48: ldur            w0, [x1, #7]
    // 0xb53a4c: DecompressPointer r0
    //     0xb53a4c: add             x0, x0, HEAP, lsl #32
    // 0xb53a50: mov             x2, x0
    // 0xb53a54: ldur            x1, [fp, #-0x10]
    // 0xb53a58: ldur            x0, [fp, #-0x38]
    // 0xb53a5c: str             x2, [SP]
    // 0xb53a60: r0 = _interpolateSingle()
    //     0xb53a60: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb53a64: mov             x2, x0
    // 0xb53a68: ldur            x0, [fp, #-0x10]
    // 0xb53a6c: stur            x2, [fp, #-8]
    // 0xb53a70: LoadField: r1 = r0->field_f
    //     0xb53a70: ldur            w1, [x0, #0xf]
    // 0xb53a74: DecompressPointer r1
    //     0xb53a74: add             x1, x1, HEAP, lsl #32
    // 0xb53a78: cmp             w1, NULL
    // 0xb53a7c: b.eq            #0xb53bd0
    // 0xb53a80: r0 = of()
    //     0xb53a80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb53a84: LoadField: r1 = r0->field_87
    //     0xb53a84: ldur            w1, [x0, #0x87]
    // 0xb53a88: DecompressPointer r1
    //     0xb53a88: add             x1, x1, HEAP, lsl #32
    // 0xb53a8c: LoadField: r0 = r1->field_2b
    //     0xb53a8c: ldur            w0, [x1, #0x2b]
    // 0xb53a90: DecompressPointer r0
    //     0xb53a90: add             x0, x0, HEAP, lsl #32
    // 0xb53a94: r16 = 12.000000
    //     0xb53a94: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb53a98: ldr             x16, [x16, #0x9e8]
    // 0xb53a9c: r30 = Instance_Color
    //     0xb53a9c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb53aa0: stp             lr, x16, [SP]
    // 0xb53aa4: mov             x1, x0
    // 0xb53aa8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb53aa8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb53aac: ldr             x4, [x4, #0xaa0]
    // 0xb53ab0: r0 = copyWith()
    //     0xb53ab0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb53ab4: stur            x0, [fp, #-0x10]
    // 0xb53ab8: r0 = Text()
    //     0xb53ab8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb53abc: mov             x1, x0
    // 0xb53ac0: ldur            x0, [fp, #-8]
    // 0xb53ac4: stur            x1, [fp, #-0x18]
    // 0xb53ac8: StoreField: r1->field_b = r0
    //     0xb53ac8: stur            w0, [x1, #0xb]
    // 0xb53acc: ldur            x0, [fp, #-0x10]
    // 0xb53ad0: StoreField: r1->field_13 = r0
    //     0xb53ad0: stur            w0, [x1, #0x13]
    // 0xb53ad4: r0 = Instance_TextAlign
    //     0xb53ad4: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb53ad8: StoreField: r1->field_1b = r0
    //     0xb53ad8: stur            w0, [x1, #0x1b]
    // 0xb53adc: r0 = 6
    //     0xb53adc: movz            x0, #0x6
    // 0xb53ae0: StoreField: r1->field_37 = r0
    //     0xb53ae0: stur            w0, [x1, #0x37]
    // 0xb53ae4: r0 = SizedBox()
    //     0xb53ae4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb53ae8: mov             x3, x0
    // 0xb53aec: r0 = 100.000000
    //     0xb53aec: ldr             x0, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xb53af0: stur            x3, [fp, #-8]
    // 0xb53af4: StoreField: r3->field_f = r0
    //     0xb53af4: stur            w0, [x3, #0xf]
    // 0xb53af8: ldur            x1, [fp, #-0x18]
    // 0xb53afc: StoreField: r3->field_b = r1
    //     0xb53afc: stur            w1, [x3, #0xb]
    // 0xb53b00: r1 = Null
    //     0xb53b00: mov             x1, NULL
    // 0xb53b04: r2 = 4
    //     0xb53b04: movz            x2, #0x4
    // 0xb53b08: r0 = AllocateArray()
    //     0xb53b08: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb53b0c: mov             x2, x0
    // 0xb53b10: ldur            x0, [fp, #-0x38]
    // 0xb53b14: stur            x2, [fp, #-0x10]
    // 0xb53b18: StoreField: r2->field_f = r0
    //     0xb53b18: stur            w0, [x2, #0xf]
    // 0xb53b1c: ldur            x0, [fp, #-8]
    // 0xb53b20: StoreField: r2->field_13 = r0
    //     0xb53b20: stur            w0, [x2, #0x13]
    // 0xb53b24: r1 = <Widget>
    //     0xb53b24: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb53b28: r0 = AllocateGrowableArray()
    //     0xb53b28: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb53b2c: mov             x1, x0
    // 0xb53b30: ldur            x0, [fp, #-0x10]
    // 0xb53b34: stur            x1, [fp, #-8]
    // 0xb53b38: StoreField: r1->field_f = r0
    //     0xb53b38: stur            w0, [x1, #0xf]
    // 0xb53b3c: r0 = 4
    //     0xb53b3c: movz            x0, #0x4
    // 0xb53b40: StoreField: r1->field_b = r0
    //     0xb53b40: stur            w0, [x1, #0xb]
    // 0xb53b44: r0 = Column()
    //     0xb53b44: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb53b48: mov             x1, x0
    // 0xb53b4c: r0 = Instance_Axis
    //     0xb53b4c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb53b50: stur            x1, [fp, #-0x10]
    // 0xb53b54: StoreField: r1->field_f = r0
    //     0xb53b54: stur            w0, [x1, #0xf]
    // 0xb53b58: r0 = Instance_MainAxisAlignment
    //     0xb53b58: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb53b5c: ldr             x0, [x0, #0xa08]
    // 0xb53b60: StoreField: r1->field_13 = r0
    //     0xb53b60: stur            w0, [x1, #0x13]
    // 0xb53b64: r0 = Instance_MainAxisSize
    //     0xb53b64: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb53b68: ldr             x0, [x0, #0xa10]
    // 0xb53b6c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb53b6c: stur            w0, [x1, #0x17]
    // 0xb53b70: r0 = Instance_CrossAxisAlignment
    //     0xb53b70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb53b74: ldr             x0, [x0, #0xa18]
    // 0xb53b78: StoreField: r1->field_1b = r0
    //     0xb53b78: stur            w0, [x1, #0x1b]
    // 0xb53b7c: r0 = Instance_VerticalDirection
    //     0xb53b7c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb53b80: ldr             x0, [x0, #0xa20]
    // 0xb53b84: StoreField: r1->field_23 = r0
    //     0xb53b84: stur            w0, [x1, #0x23]
    // 0xb53b88: r0 = Instance_Clip
    //     0xb53b88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb53b8c: ldr             x0, [x0, #0x38]
    // 0xb53b90: StoreField: r1->field_2b = r0
    //     0xb53b90: stur            w0, [x1, #0x2b]
    // 0xb53b94: StoreField: r1->field_2f = rZR
    //     0xb53b94: stur            xzr, [x1, #0x2f]
    // 0xb53b98: ldur            x0, [fp, #-8]
    // 0xb53b9c: StoreField: r1->field_b = r0
    //     0xb53b9c: stur            w0, [x1, #0xb]
    // 0xb53ba0: r0 = SizedBox()
    //     0xb53ba0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb53ba4: r1 = 100.000000
    //     0xb53ba4: ldr             x1, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xb53ba8: StoreField: r0->field_f = r1
    //     0xb53ba8: stur            w1, [x0, #0xf]
    // 0xb53bac: ldur            x1, [fp, #-0x10]
    // 0xb53bb0: StoreField: r0->field_b = r1
    //     0xb53bb0: stur            w1, [x0, #0xb]
    // 0xb53bb4: LeaveFrame
    //     0xb53bb4: mov             SP, fp
    //     0xb53bb8: ldp             fp, lr, [SP], #0x10
    // 0xb53bbc: ret
    //     0xb53bbc: ret             
    // 0xb53bc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb53bc0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb53bc4: b               #0xb53940
    // 0xb53bc8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb53bc8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb53bcc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb53bcc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb53bd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb53bd0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4091, size: 0x14, field offset: 0xc
//   const constructor, 
class TrustMarkerWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7ef98, size: 0x24
    // 0xc7ef98: EnterFrame
    //     0xc7ef98: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ef9c: mov             fp, SP
    // 0xc7efa0: mov             x0, x1
    // 0xc7efa4: r1 = <TrustMarkerWidget>
    //     0xc7efa4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a18] TypeArguments: <TrustMarkerWidget>
    //     0xc7efa8: ldr             x1, [x1, #0xa18]
    // 0xc7efac: r0 = _TrustMarkerWidgetState()
    //     0xc7efac: bl              #0xc7efbc  ; Allocate_TrustMarkerWidgetStateStub -> _TrustMarkerWidgetState (size=0x14)
    // 0xc7efb0: LeaveFrame
    //     0xc7efb0: mov             SP, fp
    //     0xc7efb4: ldp             fp, lr, [SP], #0x10
    // 0xc7efb8: ret
    //     0xc7efb8: ret             
  }
}
