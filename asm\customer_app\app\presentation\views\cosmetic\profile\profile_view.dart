// lib: , url: package:customer_app/app/presentation/views/cosmetic/profile/profile_view.dart

// class id: 1049331, size: 0x8
class :: {
}

// class id: 4586, size: 0x14, field offset: 0x14
//   const constructor, 
class ProfileView extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14d0714, size: 0x64
    // 0x14d0714: EnterFrame
    //     0x14d0714: stp             fp, lr, [SP, #-0x10]!
    //     0x14d0718: mov             fp, SP
    // 0x14d071c: AllocStack(0x18)
    //     0x14d071c: sub             SP, SP, #0x18
    // 0x14d0720: SetupParameters(ProfileView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14d0720: stur            x1, [fp, #-8]
    //     0x14d0724: stur            x2, [fp, #-0x10]
    // 0x14d0728: r1 = 2
    //     0x14d0728: movz            x1, #0x2
    // 0x14d072c: r0 = AllocateContext()
    //     0x14d072c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d0730: mov             x1, x0
    // 0x14d0734: ldur            x0, [fp, #-8]
    // 0x14d0738: stur            x1, [fp, #-0x18]
    // 0x14d073c: StoreField: r1->field_f = r0
    //     0x14d073c: stur            w0, [x1, #0xf]
    // 0x14d0740: ldur            x0, [fp, #-0x10]
    // 0x14d0744: StoreField: r1->field_13 = r0
    //     0x14d0744: stur            w0, [x1, #0x13]
    // 0x14d0748: r0 = Obx()
    //     0x14d0748: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14d074c: ldur            x2, [fp, #-0x18]
    // 0x14d0750: r1 = Function '<anonymous closure>':.
    //     0x14d0750: add             x1, PP, #0x43, lsl #12  ; [pp+0x43098] AnonymousClosure: (0x14d0778), in [package:customer_app/app/presentation/views/cosmetic/profile/profile_view.dart] ProfileView::body (0x14d0714)
    //     0x14d0754: ldr             x1, [x1, #0x98]
    // 0x14d0758: stur            x0, [fp, #-8]
    // 0x14d075c: r0 = AllocateClosure()
    //     0x14d075c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d0760: mov             x1, x0
    // 0x14d0764: ldur            x0, [fp, #-8]
    // 0x14d0768: StoreField: r0->field_b = r1
    //     0x14d0768: stur            w1, [x0, #0xb]
    // 0x14d076c: LeaveFrame
    //     0x14d076c: mov             SP, fp
    //     0x14d0770: ldp             fp, lr, [SP], #0x10
    // 0x14d0774: ret
    //     0x14d0774: ret             
  }
  [closure] Card <anonymous closure>(dynamic) {
    // ** addr: 0x14d0778, size: 0xe3c
    // 0x14d0778: EnterFrame
    //     0x14d0778: stp             fp, lr, [SP, #-0x10]!
    //     0x14d077c: mov             fp, SP
    // 0x14d0780: AllocStack(0x30)
    //     0x14d0780: sub             SP, SP, #0x30
    // 0x14d0784: SetupParameters()
    //     0x14d0784: ldr             x0, [fp, #0x10]
    //     0x14d0788: ldur            w2, [x0, #0x17]
    //     0x14d078c: add             x2, x2, HEAP, lsl #32
    //     0x14d0790: stur            x2, [fp, #-8]
    // 0x14d0794: CheckStackOverflow
    //     0x14d0794: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d0798: cmp             SP, x16
    //     0x14d079c: b.ls            #0x14d15ac
    // 0x14d07a0: LoadField: r1 = r2->field_f
    //     0x14d07a0: ldur            w1, [x2, #0xf]
    // 0x14d07a4: DecompressPointer r1
    //     0x14d07a4: add             x1, x1, HEAP, lsl #32
    // 0x14d07a8: r0 = controller()
    //     0x14d07a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d07ac: LoadField: r1 = r0->field_5b
    //     0x14d07ac: ldur            w1, [x0, #0x5b]
    // 0x14d07b0: DecompressPointer r1
    //     0x14d07b0: add             x1, x1, HEAP, lsl #32
    // 0x14d07b4: r0 = value()
    //     0x14d07b4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d07b8: eor             x2, x0, #0x10
    // 0x14d07bc: ldur            x0, [fp, #-8]
    // 0x14d07c0: stur            x2, [fp, #-0x10]
    // 0x14d07c4: LoadField: r1 = r0->field_13
    //     0x14d07c4: ldur            w1, [x0, #0x13]
    // 0x14d07c8: DecompressPointer r1
    //     0x14d07c8: add             x1, x1, HEAP, lsl #32
    // 0x14d07cc: r0 = of()
    //     0x14d07cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d07d0: LoadField: r1 = r0->field_87
    //     0x14d07d0: ldur            w1, [x0, #0x87]
    // 0x14d07d4: DecompressPointer r1
    //     0x14d07d4: add             x1, x1, HEAP, lsl #32
    // 0x14d07d8: LoadField: r0 = r1->field_7
    //     0x14d07d8: ldur            w0, [x1, #7]
    // 0x14d07dc: DecompressPointer r0
    //     0x14d07dc: add             x0, x0, HEAP, lsl #32
    // 0x14d07e0: r16 = 14.000000
    //     0x14d07e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14d07e4: ldr             x16, [x16, #0x1d8]
    // 0x14d07e8: str             x16, [SP]
    // 0x14d07ec: mov             x1, x0
    // 0x14d07f0: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x14d07f0: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x14d07f4: ldr             x4, [x4, #0x798]
    // 0x14d07f8: r0 = copyWith()
    //     0x14d07f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d07fc: stur            x0, [fp, #-0x18]
    // 0x14d0800: r0 = Text()
    //     0x14d0800: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d0804: mov             x3, x0
    // 0x14d0808: r0 = "Login"
    //     0x14d0808: add             x0, PP, #0x23, lsl #12  ; [pp+0x23770] "Login"
    //     0x14d080c: ldr             x0, [x0, #0x770]
    // 0x14d0810: stur            x3, [fp, #-0x20]
    // 0x14d0814: StoreField: r3->field_b = r0
    //     0x14d0814: stur            w0, [x3, #0xb]
    // 0x14d0818: ldur            x0, [fp, #-0x18]
    // 0x14d081c: StoreField: r3->field_13 = r0
    //     0x14d081c: stur            w0, [x3, #0x13]
    // 0x14d0820: r1 = Null
    //     0x14d0820: mov             x1, NULL
    // 0x14d0824: r2 = 6
    //     0x14d0824: movz            x2, #0x6
    // 0x14d0828: r0 = AllocateArray()
    //     0x14d0828: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d082c: stur            x0, [fp, #-0x18]
    // 0x14d0830: r16 = Instance_Icon
    //     0x14d0830: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c38] Obj!Icon@d66a71
    //     0x14d0834: ldr             x16, [x16, #0xc38]
    // 0x14d0838: StoreField: r0->field_f = r16
    //     0x14d0838: stur            w16, [x0, #0xf]
    // 0x14d083c: r16 = Instance_SizedBox
    //     0x14d083c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x14d0840: ldr             x16, [x16, #0xaa8]
    // 0x14d0844: StoreField: r0->field_13 = r16
    //     0x14d0844: stur            w16, [x0, #0x13]
    // 0x14d0848: ldur            x1, [fp, #-0x20]
    // 0x14d084c: ArrayStore: r0[0] = r1  ; List_4
    //     0x14d084c: stur            w1, [x0, #0x17]
    // 0x14d0850: r1 = <Widget>
    //     0x14d0850: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d0854: r0 = AllocateGrowableArray()
    //     0x14d0854: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d0858: mov             x1, x0
    // 0x14d085c: ldur            x0, [fp, #-0x18]
    // 0x14d0860: stur            x1, [fp, #-0x20]
    // 0x14d0864: StoreField: r1->field_f = r0
    //     0x14d0864: stur            w0, [x1, #0xf]
    // 0x14d0868: r2 = 6
    //     0x14d0868: movz            x2, #0x6
    // 0x14d086c: StoreField: r1->field_b = r2
    //     0x14d086c: stur            w2, [x1, #0xb]
    // 0x14d0870: r0 = Row()
    //     0x14d0870: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14d0874: mov             x1, x0
    // 0x14d0878: r0 = Instance_Axis
    //     0x14d0878: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14d087c: stur            x1, [fp, #-0x18]
    // 0x14d0880: StoreField: r1->field_f = r0
    //     0x14d0880: stur            w0, [x1, #0xf]
    // 0x14d0884: r2 = Instance_MainAxisAlignment
    //     0x14d0884: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14d0888: ldr             x2, [x2, #0xa08]
    // 0x14d088c: StoreField: r1->field_13 = r2
    //     0x14d088c: stur            w2, [x1, #0x13]
    // 0x14d0890: r3 = Instance_MainAxisSize
    //     0x14d0890: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14d0894: ldr             x3, [x3, #0xa10]
    // 0x14d0898: ArrayStore: r1[0] = r3  ; List_4
    //     0x14d0898: stur            w3, [x1, #0x17]
    // 0x14d089c: r4 = Instance_CrossAxisAlignment
    //     0x14d089c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d08a0: ldr             x4, [x4, #0xa18]
    // 0x14d08a4: StoreField: r1->field_1b = r4
    //     0x14d08a4: stur            w4, [x1, #0x1b]
    // 0x14d08a8: r5 = Instance_VerticalDirection
    //     0x14d08a8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d08ac: ldr             x5, [x5, #0xa20]
    // 0x14d08b0: StoreField: r1->field_23 = r5
    //     0x14d08b0: stur            w5, [x1, #0x23]
    // 0x14d08b4: r6 = Instance_Clip
    //     0x14d08b4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d08b8: ldr             x6, [x6, #0x38]
    // 0x14d08bc: StoreField: r1->field_2b = r6
    //     0x14d08bc: stur            w6, [x1, #0x2b]
    // 0x14d08c0: StoreField: r1->field_2f = rZR
    //     0x14d08c0: stur            xzr, [x1, #0x2f]
    // 0x14d08c4: ldur            x7, [fp, #-0x20]
    // 0x14d08c8: StoreField: r1->field_b = r7
    //     0x14d08c8: stur            w7, [x1, #0xb]
    // 0x14d08cc: r0 = InkWell()
    //     0x14d08cc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d08d0: mov             x3, x0
    // 0x14d08d4: ldur            x0, [fp, #-0x18]
    // 0x14d08d8: stur            x3, [fp, #-0x20]
    // 0x14d08dc: StoreField: r3->field_b = r0
    //     0x14d08dc: stur            w0, [x3, #0xb]
    // 0x14d08e0: ldur            x2, [fp, #-8]
    // 0x14d08e4: r1 = Function '<anonymous closure>':.
    //     0x14d08e4: add             x1, PP, #0x43, lsl #12  ; [pp+0x430a0] AnonymousClosure: (0x14778c4), in [package:customer_app/app/presentation/views/glass/profile/profile_view.dart] ProfileView::body (0x14fc37c)
    //     0x14d08e8: ldr             x1, [x1, #0xa0]
    // 0x14d08ec: r0 = AllocateClosure()
    //     0x14d08ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d08f0: mov             x1, x0
    // 0x14d08f4: ldur            x0, [fp, #-0x20]
    // 0x14d08f8: StoreField: r0->field_f = r1
    //     0x14d08f8: stur            w1, [x0, #0xf]
    // 0x14d08fc: r1 = true
    //     0x14d08fc: add             x1, NULL, #0x20  ; true
    // 0x14d0900: StoreField: r0->field_43 = r1
    //     0x14d0900: stur            w1, [x0, #0x43]
    // 0x14d0904: r2 = Instance_BoxShape
    //     0x14d0904: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d0908: ldr             x2, [x2, #0x80]
    // 0x14d090c: StoreField: r0->field_47 = r2
    //     0x14d090c: stur            w2, [x0, #0x47]
    // 0x14d0910: r3 = Instance_Color
    //     0x14d0910: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x14d0914: ldr             x3, [x3, #0xf88]
    // 0x14d0918: StoreField: r0->field_5f = r3
    //     0x14d0918: stur            w3, [x0, #0x5f]
    // 0x14d091c: r4 = Instance__NoSplashFactory
    //     0x14d091c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x14d0920: ldr             x4, [x4, #0xc48]
    // 0x14d0924: StoreField: r0->field_6b = r4
    //     0x14d0924: stur            w4, [x0, #0x6b]
    // 0x14d0928: StoreField: r0->field_6f = r1
    //     0x14d0928: stur            w1, [x0, #0x6f]
    // 0x14d092c: r5 = false
    //     0x14d092c: add             x5, NULL, #0x30  ; false
    // 0x14d0930: StoreField: r0->field_73 = r5
    //     0x14d0930: stur            w5, [x0, #0x73]
    // 0x14d0934: StoreField: r0->field_83 = r1
    //     0x14d0934: stur            w1, [x0, #0x83]
    // 0x14d0938: StoreField: r0->field_7b = r5
    //     0x14d0938: stur            w5, [x0, #0x7b]
    // 0x14d093c: r0 = Visibility()
    //     0x14d093c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14d0940: mov             x3, x0
    // 0x14d0944: ldur            x0, [fp, #-0x20]
    // 0x14d0948: stur            x3, [fp, #-0x18]
    // 0x14d094c: StoreField: r3->field_b = r0
    //     0x14d094c: stur            w0, [x3, #0xb]
    // 0x14d0950: r0 = Instance_SizedBox
    //     0x14d0950: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14d0954: StoreField: r3->field_f = r0
    //     0x14d0954: stur            w0, [x3, #0xf]
    // 0x14d0958: ldur            x1, [fp, #-0x10]
    // 0x14d095c: StoreField: r3->field_13 = r1
    //     0x14d095c: stur            w1, [x3, #0x13]
    // 0x14d0960: r4 = false
    //     0x14d0960: add             x4, NULL, #0x30  ; false
    // 0x14d0964: ArrayStore: r3[0] = r4  ; List_4
    //     0x14d0964: stur            w4, [x3, #0x17]
    // 0x14d0968: StoreField: r3->field_1b = r4
    //     0x14d0968: stur            w4, [x3, #0x1b]
    // 0x14d096c: StoreField: r3->field_1f = r4
    //     0x14d096c: stur            w4, [x3, #0x1f]
    // 0x14d0970: StoreField: r3->field_23 = r4
    //     0x14d0970: stur            w4, [x3, #0x23]
    // 0x14d0974: StoreField: r3->field_27 = r4
    //     0x14d0974: stur            w4, [x3, #0x27]
    // 0x14d0978: StoreField: r3->field_2b = r4
    //     0x14d0978: stur            w4, [x3, #0x2b]
    // 0x14d097c: r1 = <Widget>
    //     0x14d097c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d0980: r2 = 26
    //     0x14d0980: movz            x2, #0x1a
    // 0x14d0984: r0 = AllocateArray()
    //     0x14d0984: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d0988: mov             x2, x0
    // 0x14d098c: ldur            x0, [fp, #-0x18]
    // 0x14d0990: stur            x2, [fp, #-0x10]
    // 0x14d0994: StoreField: r2->field_f = r0
    //     0x14d0994: stur            w0, [x2, #0xf]
    // 0x14d0998: ldur            x0, [fp, #-8]
    // 0x14d099c: LoadField: r1 = r0->field_f
    //     0x14d099c: ldur            w1, [x0, #0xf]
    // 0x14d09a0: DecompressPointer r1
    //     0x14d09a0: add             x1, x1, HEAP, lsl #32
    // 0x14d09a4: r0 = controller()
    //     0x14d09a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d09a8: LoadField: r1 = r0->field_5b
    //     0x14d09a8: ldur            w1, [x0, #0x5b]
    // 0x14d09ac: DecompressPointer r1
    //     0x14d09ac: add             x1, x1, HEAP, lsl #32
    // 0x14d09b0: r0 = value()
    //     0x14d09b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d09b4: eor             x1, x0, #0x10
    // 0x14d09b8: stur            x1, [fp, #-0x18]
    // 0x14d09bc: r0 = Visibility()
    //     0x14d09bc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14d09c0: mov             x1, x0
    // 0x14d09c4: r0 = Instance_SizedBox
    //     0x14d09c4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x14d09c8: ldr             x0, [x0, #0xc50]
    // 0x14d09cc: StoreField: r1->field_b = r0
    //     0x14d09cc: stur            w0, [x1, #0xb]
    // 0x14d09d0: r2 = Instance_SizedBox
    //     0x14d09d0: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14d09d4: StoreField: r1->field_f = r2
    //     0x14d09d4: stur            w2, [x1, #0xf]
    // 0x14d09d8: ldur            x0, [fp, #-0x18]
    // 0x14d09dc: StoreField: r1->field_13 = r0
    //     0x14d09dc: stur            w0, [x1, #0x13]
    // 0x14d09e0: r3 = false
    //     0x14d09e0: add             x3, NULL, #0x30  ; false
    // 0x14d09e4: ArrayStore: r1[0] = r3  ; List_4
    //     0x14d09e4: stur            w3, [x1, #0x17]
    // 0x14d09e8: StoreField: r1->field_1b = r3
    //     0x14d09e8: stur            w3, [x1, #0x1b]
    // 0x14d09ec: StoreField: r1->field_1f = r3
    //     0x14d09ec: stur            w3, [x1, #0x1f]
    // 0x14d09f0: StoreField: r1->field_23 = r3
    //     0x14d09f0: stur            w3, [x1, #0x23]
    // 0x14d09f4: StoreField: r1->field_27 = r3
    //     0x14d09f4: stur            w3, [x1, #0x27]
    // 0x14d09f8: StoreField: r1->field_2b = r3
    //     0x14d09f8: stur            w3, [x1, #0x2b]
    // 0x14d09fc: mov             x0, x1
    // 0x14d0a00: ldur            x1, [fp, #-0x10]
    // 0x14d0a04: ArrayStore: r1[1] = r0  ; List_4
    //     0x14d0a04: add             x25, x1, #0x13
    //     0x14d0a08: str             w0, [x25]
    //     0x14d0a0c: tbz             w0, #0, #0x14d0a28
    //     0x14d0a10: ldurb           w16, [x1, #-1]
    //     0x14d0a14: ldurb           w17, [x0, #-1]
    //     0x14d0a18: and             x16, x17, x16, lsr #2
    //     0x14d0a1c: tst             x16, HEAP, lsr #32
    //     0x14d0a20: b.eq            #0x14d0a28
    //     0x14d0a24: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14d0a28: ldur            x0, [fp, #-8]
    // 0x14d0a2c: LoadField: r1 = r0->field_13
    //     0x14d0a2c: ldur            w1, [x0, #0x13]
    // 0x14d0a30: DecompressPointer r1
    //     0x14d0a30: add             x1, x1, HEAP, lsl #32
    // 0x14d0a34: r0 = of()
    //     0x14d0a34: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d0a38: LoadField: r1 = r0->field_87
    //     0x14d0a38: ldur            w1, [x0, #0x87]
    // 0x14d0a3c: DecompressPointer r1
    //     0x14d0a3c: add             x1, x1, HEAP, lsl #32
    // 0x14d0a40: LoadField: r0 = r1->field_7
    //     0x14d0a40: ldur            w0, [x1, #7]
    // 0x14d0a44: DecompressPointer r0
    //     0x14d0a44: add             x0, x0, HEAP, lsl #32
    // 0x14d0a48: r16 = 14.000000
    //     0x14d0a48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14d0a4c: ldr             x16, [x16, #0x1d8]
    // 0x14d0a50: str             x16, [SP]
    // 0x14d0a54: mov             x1, x0
    // 0x14d0a58: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x14d0a58: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x14d0a5c: ldr             x4, [x4, #0x798]
    // 0x14d0a60: r0 = copyWith()
    //     0x14d0a60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d0a64: stur            x0, [fp, #-0x18]
    // 0x14d0a68: r0 = Text()
    //     0x14d0a68: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d0a6c: mov             x3, x0
    // 0x14d0a70: r0 = "About Us"
    //     0x14d0a70: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c58] "About Us"
    //     0x14d0a74: ldr             x0, [x0, #0xc58]
    // 0x14d0a78: stur            x3, [fp, #-0x20]
    // 0x14d0a7c: StoreField: r3->field_b = r0
    //     0x14d0a7c: stur            w0, [x3, #0xb]
    // 0x14d0a80: ldur            x0, [fp, #-0x18]
    // 0x14d0a84: StoreField: r3->field_13 = r0
    //     0x14d0a84: stur            w0, [x3, #0x13]
    // 0x14d0a88: r1 = Null
    //     0x14d0a88: mov             x1, NULL
    // 0x14d0a8c: r2 = 2
    //     0x14d0a8c: movz            x2, #0x2
    // 0x14d0a90: r0 = AllocateArray()
    //     0x14d0a90: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d0a94: mov             x2, x0
    // 0x14d0a98: ldur            x0, [fp, #-0x20]
    // 0x14d0a9c: stur            x2, [fp, #-0x18]
    // 0x14d0aa0: StoreField: r2->field_f = r0
    //     0x14d0aa0: stur            w0, [x2, #0xf]
    // 0x14d0aa4: r1 = <Widget>
    //     0x14d0aa4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d0aa8: r0 = AllocateGrowableArray()
    //     0x14d0aa8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d0aac: mov             x1, x0
    // 0x14d0ab0: ldur            x0, [fp, #-0x18]
    // 0x14d0ab4: stur            x1, [fp, #-0x20]
    // 0x14d0ab8: StoreField: r1->field_f = r0
    //     0x14d0ab8: stur            w0, [x1, #0xf]
    // 0x14d0abc: r2 = 2
    //     0x14d0abc: movz            x2, #0x2
    // 0x14d0ac0: StoreField: r1->field_b = r2
    //     0x14d0ac0: stur            w2, [x1, #0xb]
    // 0x14d0ac4: r0 = Row()
    //     0x14d0ac4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14d0ac8: mov             x1, x0
    // 0x14d0acc: r0 = Instance_Axis
    //     0x14d0acc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14d0ad0: stur            x1, [fp, #-0x18]
    // 0x14d0ad4: StoreField: r1->field_f = r0
    //     0x14d0ad4: stur            w0, [x1, #0xf]
    // 0x14d0ad8: r2 = Instance_MainAxisAlignment
    //     0x14d0ad8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14d0adc: ldr             x2, [x2, #0xa08]
    // 0x14d0ae0: StoreField: r1->field_13 = r2
    //     0x14d0ae0: stur            w2, [x1, #0x13]
    // 0x14d0ae4: r3 = Instance_MainAxisSize
    //     0x14d0ae4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14d0ae8: ldr             x3, [x3, #0xa10]
    // 0x14d0aec: ArrayStore: r1[0] = r3  ; List_4
    //     0x14d0aec: stur            w3, [x1, #0x17]
    // 0x14d0af0: r4 = Instance_CrossAxisAlignment
    //     0x14d0af0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d0af4: ldr             x4, [x4, #0xa18]
    // 0x14d0af8: StoreField: r1->field_1b = r4
    //     0x14d0af8: stur            w4, [x1, #0x1b]
    // 0x14d0afc: r5 = Instance_VerticalDirection
    //     0x14d0afc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d0b00: ldr             x5, [x5, #0xa20]
    // 0x14d0b04: StoreField: r1->field_23 = r5
    //     0x14d0b04: stur            w5, [x1, #0x23]
    // 0x14d0b08: r6 = Instance_Clip
    //     0x14d0b08: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d0b0c: ldr             x6, [x6, #0x38]
    // 0x14d0b10: StoreField: r1->field_2b = r6
    //     0x14d0b10: stur            w6, [x1, #0x2b]
    // 0x14d0b14: StoreField: r1->field_2f = rZR
    //     0x14d0b14: stur            xzr, [x1, #0x2f]
    // 0x14d0b18: ldur            x7, [fp, #-0x20]
    // 0x14d0b1c: StoreField: r1->field_b = r7
    //     0x14d0b1c: stur            w7, [x1, #0xb]
    // 0x14d0b20: r0 = InkWell()
    //     0x14d0b20: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d0b24: mov             x3, x0
    // 0x14d0b28: ldur            x0, [fp, #-0x18]
    // 0x14d0b2c: stur            x3, [fp, #-0x20]
    // 0x14d0b30: StoreField: r3->field_b = r0
    //     0x14d0b30: stur            w0, [x3, #0xb]
    // 0x14d0b34: ldur            x2, [fp, #-8]
    // 0x14d0b38: r1 = Function '<anonymous closure>':.
    //     0x14d0b38: add             x1, PP, #0x43, lsl #12  ; [pp+0x430a8] AnonymousClosure: (0x14d16f4), in [package:customer_app/app/presentation/views/cosmetic/profile/profile_view.dart] ProfileView::body (0x14d0714)
    //     0x14d0b3c: ldr             x1, [x1, #0xa8]
    // 0x14d0b40: r0 = AllocateClosure()
    //     0x14d0b40: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d0b44: mov             x1, x0
    // 0x14d0b48: ldur            x0, [fp, #-0x20]
    // 0x14d0b4c: StoreField: r0->field_f = r1
    //     0x14d0b4c: stur            w1, [x0, #0xf]
    // 0x14d0b50: r1 = true
    //     0x14d0b50: add             x1, NULL, #0x20  ; true
    // 0x14d0b54: StoreField: r0->field_43 = r1
    //     0x14d0b54: stur            w1, [x0, #0x43]
    // 0x14d0b58: r2 = Instance_BoxShape
    //     0x14d0b58: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d0b5c: ldr             x2, [x2, #0x80]
    // 0x14d0b60: StoreField: r0->field_47 = r2
    //     0x14d0b60: stur            w2, [x0, #0x47]
    // 0x14d0b64: r3 = Instance_Color
    //     0x14d0b64: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x14d0b68: ldr             x3, [x3, #0xf88]
    // 0x14d0b6c: StoreField: r0->field_5f = r3
    //     0x14d0b6c: stur            w3, [x0, #0x5f]
    // 0x14d0b70: r4 = Instance__NoSplashFactory
    //     0x14d0b70: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x14d0b74: ldr             x4, [x4, #0xc48]
    // 0x14d0b78: StoreField: r0->field_6b = r4
    //     0x14d0b78: stur            w4, [x0, #0x6b]
    // 0x14d0b7c: StoreField: r0->field_6f = r1
    //     0x14d0b7c: stur            w1, [x0, #0x6f]
    // 0x14d0b80: r5 = false
    //     0x14d0b80: add             x5, NULL, #0x30  ; false
    // 0x14d0b84: StoreField: r0->field_73 = r5
    //     0x14d0b84: stur            w5, [x0, #0x73]
    // 0x14d0b88: StoreField: r0->field_83 = r1
    //     0x14d0b88: stur            w1, [x0, #0x83]
    // 0x14d0b8c: StoreField: r0->field_7b = r5
    //     0x14d0b8c: stur            w5, [x0, #0x7b]
    // 0x14d0b90: r0 = Align()
    //     0x14d0b90: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14d0b94: r2 = Instance_Alignment
    //     0x14d0b94: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x14d0b98: ldr             x2, [x2, #0xfa0]
    // 0x14d0b9c: StoreField: r0->field_f = r2
    //     0x14d0b9c: stur            w2, [x0, #0xf]
    // 0x14d0ba0: ldur            x1, [fp, #-0x20]
    // 0x14d0ba4: StoreField: r0->field_b = r1
    //     0x14d0ba4: stur            w1, [x0, #0xb]
    // 0x14d0ba8: ldur            x1, [fp, #-0x10]
    // 0x14d0bac: ArrayStore: r1[2] = r0  ; List_4
    //     0x14d0bac: add             x25, x1, #0x17
    //     0x14d0bb0: str             w0, [x25]
    //     0x14d0bb4: tbz             w0, #0, #0x14d0bd0
    //     0x14d0bb8: ldurb           w16, [x1, #-1]
    //     0x14d0bbc: ldurb           w17, [x0, #-1]
    //     0x14d0bc0: and             x16, x17, x16, lsr #2
    //     0x14d0bc4: tst             x16, HEAP, lsr #32
    //     0x14d0bc8: b.eq            #0x14d0bd0
    //     0x14d0bcc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14d0bd0: ldur            x0, [fp, #-0x10]
    // 0x14d0bd4: r16 = Instance_SizedBox
    //     0x14d0bd4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x14d0bd8: ldr             x16, [x16, #0xc50]
    // 0x14d0bdc: StoreField: r0->field_1b = r16
    //     0x14d0bdc: stur            w16, [x0, #0x1b]
    // 0x14d0be0: ldur            x3, [fp, #-8]
    // 0x14d0be4: LoadField: r1 = r3->field_13
    //     0x14d0be4: ldur            w1, [x3, #0x13]
    // 0x14d0be8: DecompressPointer r1
    //     0x14d0be8: add             x1, x1, HEAP, lsl #32
    // 0x14d0bec: r0 = of()
    //     0x14d0bec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d0bf0: LoadField: r1 = r0->field_87
    //     0x14d0bf0: ldur            w1, [x0, #0x87]
    // 0x14d0bf4: DecompressPointer r1
    //     0x14d0bf4: add             x1, x1, HEAP, lsl #32
    // 0x14d0bf8: LoadField: r0 = r1->field_7
    //     0x14d0bf8: ldur            w0, [x1, #7]
    // 0x14d0bfc: DecompressPointer r0
    //     0x14d0bfc: add             x0, x0, HEAP, lsl #32
    // 0x14d0c00: r16 = 14.000000
    //     0x14d0c00: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14d0c04: ldr             x16, [x16, #0x1d8]
    // 0x14d0c08: str             x16, [SP]
    // 0x14d0c0c: mov             x1, x0
    // 0x14d0c10: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x14d0c10: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x14d0c14: ldr             x4, [x4, #0x798]
    // 0x14d0c18: r0 = copyWith()
    //     0x14d0c18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d0c1c: stur            x0, [fp, #-0x18]
    // 0x14d0c20: r0 = Text()
    //     0x14d0c20: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d0c24: mov             x3, x0
    // 0x14d0c28: r0 = "Privacy Policy"
    //     0x14d0c28: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c68] "Privacy Policy"
    //     0x14d0c2c: ldr             x0, [x0, #0xc68]
    // 0x14d0c30: stur            x3, [fp, #-0x20]
    // 0x14d0c34: StoreField: r3->field_b = r0
    //     0x14d0c34: stur            w0, [x3, #0xb]
    // 0x14d0c38: ldur            x0, [fp, #-0x18]
    // 0x14d0c3c: StoreField: r3->field_13 = r0
    //     0x14d0c3c: stur            w0, [x3, #0x13]
    // 0x14d0c40: r1 = Null
    //     0x14d0c40: mov             x1, NULL
    // 0x14d0c44: r2 = 2
    //     0x14d0c44: movz            x2, #0x2
    // 0x14d0c48: r0 = AllocateArray()
    //     0x14d0c48: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d0c4c: mov             x2, x0
    // 0x14d0c50: ldur            x0, [fp, #-0x20]
    // 0x14d0c54: stur            x2, [fp, #-0x18]
    // 0x14d0c58: StoreField: r2->field_f = r0
    //     0x14d0c58: stur            w0, [x2, #0xf]
    // 0x14d0c5c: r1 = <Widget>
    //     0x14d0c5c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d0c60: r0 = AllocateGrowableArray()
    //     0x14d0c60: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d0c64: mov             x1, x0
    // 0x14d0c68: ldur            x0, [fp, #-0x18]
    // 0x14d0c6c: stur            x1, [fp, #-0x20]
    // 0x14d0c70: StoreField: r1->field_f = r0
    //     0x14d0c70: stur            w0, [x1, #0xf]
    // 0x14d0c74: r2 = 2
    //     0x14d0c74: movz            x2, #0x2
    // 0x14d0c78: StoreField: r1->field_b = r2
    //     0x14d0c78: stur            w2, [x1, #0xb]
    // 0x14d0c7c: r0 = Row()
    //     0x14d0c7c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14d0c80: mov             x1, x0
    // 0x14d0c84: r0 = Instance_Axis
    //     0x14d0c84: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14d0c88: stur            x1, [fp, #-0x18]
    // 0x14d0c8c: StoreField: r1->field_f = r0
    //     0x14d0c8c: stur            w0, [x1, #0xf]
    // 0x14d0c90: r2 = Instance_MainAxisAlignment
    //     0x14d0c90: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14d0c94: ldr             x2, [x2, #0xa08]
    // 0x14d0c98: StoreField: r1->field_13 = r2
    //     0x14d0c98: stur            w2, [x1, #0x13]
    // 0x14d0c9c: r3 = Instance_MainAxisSize
    //     0x14d0c9c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14d0ca0: ldr             x3, [x3, #0xa10]
    // 0x14d0ca4: ArrayStore: r1[0] = r3  ; List_4
    //     0x14d0ca4: stur            w3, [x1, #0x17]
    // 0x14d0ca8: r4 = Instance_CrossAxisAlignment
    //     0x14d0ca8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d0cac: ldr             x4, [x4, #0xa18]
    // 0x14d0cb0: StoreField: r1->field_1b = r4
    //     0x14d0cb0: stur            w4, [x1, #0x1b]
    // 0x14d0cb4: r5 = Instance_VerticalDirection
    //     0x14d0cb4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d0cb8: ldr             x5, [x5, #0xa20]
    // 0x14d0cbc: StoreField: r1->field_23 = r5
    //     0x14d0cbc: stur            w5, [x1, #0x23]
    // 0x14d0cc0: r6 = Instance_Clip
    //     0x14d0cc0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d0cc4: ldr             x6, [x6, #0x38]
    // 0x14d0cc8: StoreField: r1->field_2b = r6
    //     0x14d0cc8: stur            w6, [x1, #0x2b]
    // 0x14d0ccc: StoreField: r1->field_2f = rZR
    //     0x14d0ccc: stur            xzr, [x1, #0x2f]
    // 0x14d0cd0: ldur            x7, [fp, #-0x20]
    // 0x14d0cd4: StoreField: r1->field_b = r7
    //     0x14d0cd4: stur            w7, [x1, #0xb]
    // 0x14d0cd8: r0 = InkWell()
    //     0x14d0cd8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d0cdc: mov             x3, x0
    // 0x14d0ce0: ldur            x0, [fp, #-0x18]
    // 0x14d0ce4: stur            x3, [fp, #-0x20]
    // 0x14d0ce8: StoreField: r3->field_b = r0
    //     0x14d0ce8: stur            w0, [x3, #0xb]
    // 0x14d0cec: ldur            x2, [fp, #-8]
    // 0x14d0cf0: r1 = Function '<anonymous closure>':.
    //     0x14d0cf0: add             x1, PP, #0x43, lsl #12  ; [pp+0x430b0] AnonymousClosure: (0x14d16a4), in [package:customer_app/app/presentation/views/cosmetic/profile/profile_view.dart] ProfileView::body (0x14d0714)
    //     0x14d0cf4: ldr             x1, [x1, #0xb0]
    // 0x14d0cf8: r0 = AllocateClosure()
    //     0x14d0cf8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d0cfc: mov             x1, x0
    // 0x14d0d00: ldur            x0, [fp, #-0x20]
    // 0x14d0d04: StoreField: r0->field_f = r1
    //     0x14d0d04: stur            w1, [x0, #0xf]
    // 0x14d0d08: r1 = true
    //     0x14d0d08: add             x1, NULL, #0x20  ; true
    // 0x14d0d0c: StoreField: r0->field_43 = r1
    //     0x14d0d0c: stur            w1, [x0, #0x43]
    // 0x14d0d10: r2 = Instance_BoxShape
    //     0x14d0d10: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d0d14: ldr             x2, [x2, #0x80]
    // 0x14d0d18: StoreField: r0->field_47 = r2
    //     0x14d0d18: stur            w2, [x0, #0x47]
    // 0x14d0d1c: r3 = Instance_Color
    //     0x14d0d1c: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x14d0d20: ldr             x3, [x3, #0xf88]
    // 0x14d0d24: StoreField: r0->field_5f = r3
    //     0x14d0d24: stur            w3, [x0, #0x5f]
    // 0x14d0d28: r4 = Instance__NoSplashFactory
    //     0x14d0d28: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x14d0d2c: ldr             x4, [x4, #0xc48]
    // 0x14d0d30: StoreField: r0->field_6b = r4
    //     0x14d0d30: stur            w4, [x0, #0x6b]
    // 0x14d0d34: StoreField: r0->field_6f = r1
    //     0x14d0d34: stur            w1, [x0, #0x6f]
    // 0x14d0d38: r5 = false
    //     0x14d0d38: add             x5, NULL, #0x30  ; false
    // 0x14d0d3c: StoreField: r0->field_73 = r5
    //     0x14d0d3c: stur            w5, [x0, #0x73]
    // 0x14d0d40: StoreField: r0->field_83 = r1
    //     0x14d0d40: stur            w1, [x0, #0x83]
    // 0x14d0d44: StoreField: r0->field_7b = r5
    //     0x14d0d44: stur            w5, [x0, #0x7b]
    // 0x14d0d48: r0 = Align()
    //     0x14d0d48: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14d0d4c: r2 = Instance_Alignment
    //     0x14d0d4c: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x14d0d50: ldr             x2, [x2, #0xfa0]
    // 0x14d0d54: StoreField: r0->field_f = r2
    //     0x14d0d54: stur            w2, [x0, #0xf]
    // 0x14d0d58: ldur            x1, [fp, #-0x20]
    // 0x14d0d5c: StoreField: r0->field_b = r1
    //     0x14d0d5c: stur            w1, [x0, #0xb]
    // 0x14d0d60: ldur            x1, [fp, #-0x10]
    // 0x14d0d64: ArrayStore: r1[4] = r0  ; List_4
    //     0x14d0d64: add             x25, x1, #0x1f
    //     0x14d0d68: str             w0, [x25]
    //     0x14d0d6c: tbz             w0, #0, #0x14d0d88
    //     0x14d0d70: ldurb           w16, [x1, #-1]
    //     0x14d0d74: ldurb           w17, [x0, #-1]
    //     0x14d0d78: and             x16, x17, x16, lsr #2
    //     0x14d0d7c: tst             x16, HEAP, lsr #32
    //     0x14d0d80: b.eq            #0x14d0d88
    //     0x14d0d84: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14d0d88: ldur            x0, [fp, #-0x10]
    // 0x14d0d8c: r16 = Instance_SizedBox
    //     0x14d0d8c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x14d0d90: ldr             x16, [x16, #0xc50]
    // 0x14d0d94: StoreField: r0->field_23 = r16
    //     0x14d0d94: stur            w16, [x0, #0x23]
    // 0x14d0d98: ldur            x3, [fp, #-8]
    // 0x14d0d9c: LoadField: r1 = r3->field_13
    //     0x14d0d9c: ldur            w1, [x3, #0x13]
    // 0x14d0da0: DecompressPointer r1
    //     0x14d0da0: add             x1, x1, HEAP, lsl #32
    // 0x14d0da4: r0 = of()
    //     0x14d0da4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d0da8: LoadField: r1 = r0->field_87
    //     0x14d0da8: ldur            w1, [x0, #0x87]
    // 0x14d0dac: DecompressPointer r1
    //     0x14d0dac: add             x1, x1, HEAP, lsl #32
    // 0x14d0db0: LoadField: r0 = r1->field_7
    //     0x14d0db0: ldur            w0, [x1, #7]
    // 0x14d0db4: DecompressPointer r0
    //     0x14d0db4: add             x0, x0, HEAP, lsl #32
    // 0x14d0db8: r16 = 14.000000
    //     0x14d0db8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14d0dbc: ldr             x16, [x16, #0x1d8]
    // 0x14d0dc0: str             x16, [SP]
    // 0x14d0dc4: mov             x1, x0
    // 0x14d0dc8: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x14d0dc8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x14d0dcc: ldr             x4, [x4, #0x798]
    // 0x14d0dd0: r0 = copyWith()
    //     0x14d0dd0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d0dd4: stur            x0, [fp, #-0x18]
    // 0x14d0dd8: r0 = Text()
    //     0x14d0dd8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d0ddc: mov             x3, x0
    // 0x14d0de0: r0 = "Return Policy"
    //     0x14d0de0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c78] "Return Policy"
    //     0x14d0de4: ldr             x0, [x0, #0xc78]
    // 0x14d0de8: stur            x3, [fp, #-0x20]
    // 0x14d0dec: StoreField: r3->field_b = r0
    //     0x14d0dec: stur            w0, [x3, #0xb]
    // 0x14d0df0: ldur            x0, [fp, #-0x18]
    // 0x14d0df4: StoreField: r3->field_13 = r0
    //     0x14d0df4: stur            w0, [x3, #0x13]
    // 0x14d0df8: r1 = Null
    //     0x14d0df8: mov             x1, NULL
    // 0x14d0dfc: r2 = 2
    //     0x14d0dfc: movz            x2, #0x2
    // 0x14d0e00: r0 = AllocateArray()
    //     0x14d0e00: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d0e04: mov             x2, x0
    // 0x14d0e08: ldur            x0, [fp, #-0x20]
    // 0x14d0e0c: stur            x2, [fp, #-0x18]
    // 0x14d0e10: StoreField: r2->field_f = r0
    //     0x14d0e10: stur            w0, [x2, #0xf]
    // 0x14d0e14: r1 = <Widget>
    //     0x14d0e14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d0e18: r0 = AllocateGrowableArray()
    //     0x14d0e18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d0e1c: mov             x1, x0
    // 0x14d0e20: ldur            x0, [fp, #-0x18]
    // 0x14d0e24: stur            x1, [fp, #-0x20]
    // 0x14d0e28: StoreField: r1->field_f = r0
    //     0x14d0e28: stur            w0, [x1, #0xf]
    // 0x14d0e2c: r2 = 2
    //     0x14d0e2c: movz            x2, #0x2
    // 0x14d0e30: StoreField: r1->field_b = r2
    //     0x14d0e30: stur            w2, [x1, #0xb]
    // 0x14d0e34: r0 = Row()
    //     0x14d0e34: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14d0e38: mov             x1, x0
    // 0x14d0e3c: r0 = Instance_Axis
    //     0x14d0e3c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14d0e40: stur            x1, [fp, #-0x18]
    // 0x14d0e44: StoreField: r1->field_f = r0
    //     0x14d0e44: stur            w0, [x1, #0xf]
    // 0x14d0e48: r2 = Instance_MainAxisAlignment
    //     0x14d0e48: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14d0e4c: ldr             x2, [x2, #0xa08]
    // 0x14d0e50: StoreField: r1->field_13 = r2
    //     0x14d0e50: stur            w2, [x1, #0x13]
    // 0x14d0e54: r3 = Instance_MainAxisSize
    //     0x14d0e54: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14d0e58: ldr             x3, [x3, #0xa10]
    // 0x14d0e5c: ArrayStore: r1[0] = r3  ; List_4
    //     0x14d0e5c: stur            w3, [x1, #0x17]
    // 0x14d0e60: r4 = Instance_CrossAxisAlignment
    //     0x14d0e60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d0e64: ldr             x4, [x4, #0xa18]
    // 0x14d0e68: StoreField: r1->field_1b = r4
    //     0x14d0e68: stur            w4, [x1, #0x1b]
    // 0x14d0e6c: r5 = Instance_VerticalDirection
    //     0x14d0e6c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d0e70: ldr             x5, [x5, #0xa20]
    // 0x14d0e74: StoreField: r1->field_23 = r5
    //     0x14d0e74: stur            w5, [x1, #0x23]
    // 0x14d0e78: r6 = Instance_Clip
    //     0x14d0e78: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d0e7c: ldr             x6, [x6, #0x38]
    // 0x14d0e80: StoreField: r1->field_2b = r6
    //     0x14d0e80: stur            w6, [x1, #0x2b]
    // 0x14d0e84: StoreField: r1->field_2f = rZR
    //     0x14d0e84: stur            xzr, [x1, #0x2f]
    // 0x14d0e88: ldur            x7, [fp, #-0x20]
    // 0x14d0e8c: StoreField: r1->field_b = r7
    //     0x14d0e8c: stur            w7, [x1, #0xb]
    // 0x14d0e90: r0 = InkWell()
    //     0x14d0e90: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d0e94: mov             x3, x0
    // 0x14d0e98: ldur            x0, [fp, #-0x18]
    // 0x14d0e9c: stur            x3, [fp, #-0x20]
    // 0x14d0ea0: StoreField: r3->field_b = r0
    //     0x14d0ea0: stur            w0, [x3, #0xb]
    // 0x14d0ea4: ldur            x2, [fp, #-8]
    // 0x14d0ea8: r1 = Function '<anonymous closure>':.
    //     0x14d0ea8: add             x1, PP, #0x43, lsl #12  ; [pp+0x430b8] AnonymousClosure: (0x14d1654), in [package:customer_app/app/presentation/views/cosmetic/profile/profile_view.dart] ProfileView::body (0x14d0714)
    //     0x14d0eac: ldr             x1, [x1, #0xb8]
    // 0x14d0eb0: r0 = AllocateClosure()
    //     0x14d0eb0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d0eb4: mov             x1, x0
    // 0x14d0eb8: ldur            x0, [fp, #-0x20]
    // 0x14d0ebc: StoreField: r0->field_f = r1
    //     0x14d0ebc: stur            w1, [x0, #0xf]
    // 0x14d0ec0: r1 = true
    //     0x14d0ec0: add             x1, NULL, #0x20  ; true
    // 0x14d0ec4: StoreField: r0->field_43 = r1
    //     0x14d0ec4: stur            w1, [x0, #0x43]
    // 0x14d0ec8: r2 = Instance_BoxShape
    //     0x14d0ec8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d0ecc: ldr             x2, [x2, #0x80]
    // 0x14d0ed0: StoreField: r0->field_47 = r2
    //     0x14d0ed0: stur            w2, [x0, #0x47]
    // 0x14d0ed4: r3 = Instance_Color
    //     0x14d0ed4: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x14d0ed8: ldr             x3, [x3, #0xf88]
    // 0x14d0edc: StoreField: r0->field_5f = r3
    //     0x14d0edc: stur            w3, [x0, #0x5f]
    // 0x14d0ee0: r4 = Instance__NoSplashFactory
    //     0x14d0ee0: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x14d0ee4: ldr             x4, [x4, #0xc48]
    // 0x14d0ee8: StoreField: r0->field_6b = r4
    //     0x14d0ee8: stur            w4, [x0, #0x6b]
    // 0x14d0eec: StoreField: r0->field_6f = r1
    //     0x14d0eec: stur            w1, [x0, #0x6f]
    // 0x14d0ef0: r5 = false
    //     0x14d0ef0: add             x5, NULL, #0x30  ; false
    // 0x14d0ef4: StoreField: r0->field_73 = r5
    //     0x14d0ef4: stur            w5, [x0, #0x73]
    // 0x14d0ef8: StoreField: r0->field_83 = r1
    //     0x14d0ef8: stur            w1, [x0, #0x83]
    // 0x14d0efc: StoreField: r0->field_7b = r5
    //     0x14d0efc: stur            w5, [x0, #0x7b]
    // 0x14d0f00: r0 = Align()
    //     0x14d0f00: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14d0f04: r2 = Instance_Alignment
    //     0x14d0f04: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x14d0f08: ldr             x2, [x2, #0xfa0]
    // 0x14d0f0c: StoreField: r0->field_f = r2
    //     0x14d0f0c: stur            w2, [x0, #0xf]
    // 0x14d0f10: ldur            x1, [fp, #-0x20]
    // 0x14d0f14: StoreField: r0->field_b = r1
    //     0x14d0f14: stur            w1, [x0, #0xb]
    // 0x14d0f18: ldur            x1, [fp, #-0x10]
    // 0x14d0f1c: ArrayStore: r1[6] = r0  ; List_4
    //     0x14d0f1c: add             x25, x1, #0x27
    //     0x14d0f20: str             w0, [x25]
    //     0x14d0f24: tbz             w0, #0, #0x14d0f40
    //     0x14d0f28: ldurb           w16, [x1, #-1]
    //     0x14d0f2c: ldurb           w17, [x0, #-1]
    //     0x14d0f30: and             x16, x17, x16, lsr #2
    //     0x14d0f34: tst             x16, HEAP, lsr #32
    //     0x14d0f38: b.eq            #0x14d0f40
    //     0x14d0f3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14d0f40: ldur            x0, [fp, #-0x10]
    // 0x14d0f44: r16 = Instance_SizedBox
    //     0x14d0f44: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x14d0f48: ldr             x16, [x16, #0xc50]
    // 0x14d0f4c: StoreField: r0->field_2b = r16
    //     0x14d0f4c: stur            w16, [x0, #0x2b]
    // 0x14d0f50: ldur            x3, [fp, #-8]
    // 0x14d0f54: LoadField: r1 = r3->field_13
    //     0x14d0f54: ldur            w1, [x3, #0x13]
    // 0x14d0f58: DecompressPointer r1
    //     0x14d0f58: add             x1, x1, HEAP, lsl #32
    // 0x14d0f5c: r0 = of()
    //     0x14d0f5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d0f60: LoadField: r1 = r0->field_87
    //     0x14d0f60: ldur            w1, [x0, #0x87]
    // 0x14d0f64: DecompressPointer r1
    //     0x14d0f64: add             x1, x1, HEAP, lsl #32
    // 0x14d0f68: LoadField: r0 = r1->field_7
    //     0x14d0f68: ldur            w0, [x1, #7]
    // 0x14d0f6c: DecompressPointer r0
    //     0x14d0f6c: add             x0, x0, HEAP, lsl #32
    // 0x14d0f70: r16 = 14.000000
    //     0x14d0f70: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14d0f74: ldr             x16, [x16, #0x1d8]
    // 0x14d0f78: str             x16, [SP]
    // 0x14d0f7c: mov             x1, x0
    // 0x14d0f80: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x14d0f80: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x14d0f84: ldr             x4, [x4, #0x798]
    // 0x14d0f88: r0 = copyWith()
    //     0x14d0f88: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d0f8c: stur            x0, [fp, #-0x18]
    // 0x14d0f90: r0 = Text()
    //     0x14d0f90: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d0f94: mov             x3, x0
    // 0x14d0f98: r0 = "Shipping Policy"
    //     0x14d0f98: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c88] "Shipping Policy"
    //     0x14d0f9c: ldr             x0, [x0, #0xc88]
    // 0x14d0fa0: stur            x3, [fp, #-0x20]
    // 0x14d0fa4: StoreField: r3->field_b = r0
    //     0x14d0fa4: stur            w0, [x3, #0xb]
    // 0x14d0fa8: ldur            x0, [fp, #-0x18]
    // 0x14d0fac: StoreField: r3->field_13 = r0
    //     0x14d0fac: stur            w0, [x3, #0x13]
    // 0x14d0fb0: r1 = Null
    //     0x14d0fb0: mov             x1, NULL
    // 0x14d0fb4: r2 = 2
    //     0x14d0fb4: movz            x2, #0x2
    // 0x14d0fb8: r0 = AllocateArray()
    //     0x14d0fb8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d0fbc: mov             x2, x0
    // 0x14d0fc0: ldur            x0, [fp, #-0x20]
    // 0x14d0fc4: stur            x2, [fp, #-0x18]
    // 0x14d0fc8: StoreField: r2->field_f = r0
    //     0x14d0fc8: stur            w0, [x2, #0xf]
    // 0x14d0fcc: r1 = <Widget>
    //     0x14d0fcc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d0fd0: r0 = AllocateGrowableArray()
    //     0x14d0fd0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d0fd4: mov             x1, x0
    // 0x14d0fd8: ldur            x0, [fp, #-0x18]
    // 0x14d0fdc: stur            x1, [fp, #-0x20]
    // 0x14d0fe0: StoreField: r1->field_f = r0
    //     0x14d0fe0: stur            w0, [x1, #0xf]
    // 0x14d0fe4: r2 = 2
    //     0x14d0fe4: movz            x2, #0x2
    // 0x14d0fe8: StoreField: r1->field_b = r2
    //     0x14d0fe8: stur            w2, [x1, #0xb]
    // 0x14d0fec: r0 = Row()
    //     0x14d0fec: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14d0ff0: mov             x1, x0
    // 0x14d0ff4: r0 = Instance_Axis
    //     0x14d0ff4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14d0ff8: stur            x1, [fp, #-0x18]
    // 0x14d0ffc: StoreField: r1->field_f = r0
    //     0x14d0ffc: stur            w0, [x1, #0xf]
    // 0x14d1000: r2 = Instance_MainAxisAlignment
    //     0x14d1000: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14d1004: ldr             x2, [x2, #0xa08]
    // 0x14d1008: StoreField: r1->field_13 = r2
    //     0x14d1008: stur            w2, [x1, #0x13]
    // 0x14d100c: r3 = Instance_MainAxisSize
    //     0x14d100c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14d1010: ldr             x3, [x3, #0xa10]
    // 0x14d1014: ArrayStore: r1[0] = r3  ; List_4
    //     0x14d1014: stur            w3, [x1, #0x17]
    // 0x14d1018: r4 = Instance_CrossAxisAlignment
    //     0x14d1018: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d101c: ldr             x4, [x4, #0xa18]
    // 0x14d1020: StoreField: r1->field_1b = r4
    //     0x14d1020: stur            w4, [x1, #0x1b]
    // 0x14d1024: r5 = Instance_VerticalDirection
    //     0x14d1024: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d1028: ldr             x5, [x5, #0xa20]
    // 0x14d102c: StoreField: r1->field_23 = r5
    //     0x14d102c: stur            w5, [x1, #0x23]
    // 0x14d1030: r6 = Instance_Clip
    //     0x14d1030: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d1034: ldr             x6, [x6, #0x38]
    // 0x14d1038: StoreField: r1->field_2b = r6
    //     0x14d1038: stur            w6, [x1, #0x2b]
    // 0x14d103c: StoreField: r1->field_2f = rZR
    //     0x14d103c: stur            xzr, [x1, #0x2f]
    // 0x14d1040: ldur            x7, [fp, #-0x20]
    // 0x14d1044: StoreField: r1->field_b = r7
    //     0x14d1044: stur            w7, [x1, #0xb]
    // 0x14d1048: r0 = InkWell()
    //     0x14d1048: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d104c: mov             x3, x0
    // 0x14d1050: ldur            x0, [fp, #-0x18]
    // 0x14d1054: stur            x3, [fp, #-0x20]
    // 0x14d1058: StoreField: r3->field_b = r0
    //     0x14d1058: stur            w0, [x3, #0xb]
    // 0x14d105c: ldur            x2, [fp, #-8]
    // 0x14d1060: r1 = Function '<anonymous closure>':.
    //     0x14d1060: add             x1, PP, #0x43, lsl #12  ; [pp+0x430c0] AnonymousClosure: (0x14d1604), in [package:customer_app/app/presentation/views/cosmetic/profile/profile_view.dart] ProfileView::body (0x14d0714)
    //     0x14d1064: ldr             x1, [x1, #0xc0]
    // 0x14d1068: r0 = AllocateClosure()
    //     0x14d1068: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d106c: mov             x1, x0
    // 0x14d1070: ldur            x0, [fp, #-0x20]
    // 0x14d1074: StoreField: r0->field_f = r1
    //     0x14d1074: stur            w1, [x0, #0xf]
    // 0x14d1078: r1 = true
    //     0x14d1078: add             x1, NULL, #0x20  ; true
    // 0x14d107c: StoreField: r0->field_43 = r1
    //     0x14d107c: stur            w1, [x0, #0x43]
    // 0x14d1080: r2 = Instance_BoxShape
    //     0x14d1080: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d1084: ldr             x2, [x2, #0x80]
    // 0x14d1088: StoreField: r0->field_47 = r2
    //     0x14d1088: stur            w2, [x0, #0x47]
    // 0x14d108c: r3 = Instance_Color
    //     0x14d108c: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x14d1090: ldr             x3, [x3, #0xf88]
    // 0x14d1094: StoreField: r0->field_5f = r3
    //     0x14d1094: stur            w3, [x0, #0x5f]
    // 0x14d1098: r4 = Instance__NoSplashFactory
    //     0x14d1098: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x14d109c: ldr             x4, [x4, #0xc48]
    // 0x14d10a0: StoreField: r0->field_6b = r4
    //     0x14d10a0: stur            w4, [x0, #0x6b]
    // 0x14d10a4: StoreField: r0->field_6f = r1
    //     0x14d10a4: stur            w1, [x0, #0x6f]
    // 0x14d10a8: r5 = false
    //     0x14d10a8: add             x5, NULL, #0x30  ; false
    // 0x14d10ac: StoreField: r0->field_73 = r5
    //     0x14d10ac: stur            w5, [x0, #0x73]
    // 0x14d10b0: StoreField: r0->field_83 = r1
    //     0x14d10b0: stur            w1, [x0, #0x83]
    // 0x14d10b4: StoreField: r0->field_7b = r5
    //     0x14d10b4: stur            w5, [x0, #0x7b]
    // 0x14d10b8: r0 = Align()
    //     0x14d10b8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14d10bc: r2 = Instance_Alignment
    //     0x14d10bc: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x14d10c0: ldr             x2, [x2, #0xfa0]
    // 0x14d10c4: StoreField: r0->field_f = r2
    //     0x14d10c4: stur            w2, [x0, #0xf]
    // 0x14d10c8: ldur            x1, [fp, #-0x20]
    // 0x14d10cc: StoreField: r0->field_b = r1
    //     0x14d10cc: stur            w1, [x0, #0xb]
    // 0x14d10d0: ldur            x1, [fp, #-0x10]
    // 0x14d10d4: ArrayStore: r1[8] = r0  ; List_4
    //     0x14d10d4: add             x25, x1, #0x2f
    //     0x14d10d8: str             w0, [x25]
    //     0x14d10dc: tbz             w0, #0, #0x14d10f8
    //     0x14d10e0: ldurb           w16, [x1, #-1]
    //     0x14d10e4: ldurb           w17, [x0, #-1]
    //     0x14d10e8: and             x16, x17, x16, lsr #2
    //     0x14d10ec: tst             x16, HEAP, lsr #32
    //     0x14d10f0: b.eq            #0x14d10f8
    //     0x14d10f4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14d10f8: ldur            x0, [fp, #-0x10]
    // 0x14d10fc: r16 = Instance_SizedBox
    //     0x14d10fc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x14d1100: ldr             x16, [x16, #0xc50]
    // 0x14d1104: StoreField: r0->field_33 = r16
    //     0x14d1104: stur            w16, [x0, #0x33]
    // 0x14d1108: ldur            x3, [fp, #-8]
    // 0x14d110c: LoadField: r1 = r3->field_13
    //     0x14d110c: ldur            w1, [x3, #0x13]
    // 0x14d1110: DecompressPointer r1
    //     0x14d1110: add             x1, x1, HEAP, lsl #32
    // 0x14d1114: r0 = of()
    //     0x14d1114: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d1118: LoadField: r1 = r0->field_87
    //     0x14d1118: ldur            w1, [x0, #0x87]
    // 0x14d111c: DecompressPointer r1
    //     0x14d111c: add             x1, x1, HEAP, lsl #32
    // 0x14d1120: LoadField: r0 = r1->field_7
    //     0x14d1120: ldur            w0, [x1, #7]
    // 0x14d1124: DecompressPointer r0
    //     0x14d1124: add             x0, x0, HEAP, lsl #32
    // 0x14d1128: r16 = 14.000000
    //     0x14d1128: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14d112c: ldr             x16, [x16, #0x1d8]
    // 0x14d1130: str             x16, [SP]
    // 0x14d1134: mov             x1, x0
    // 0x14d1138: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x14d1138: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x14d113c: ldr             x4, [x4, #0x798]
    // 0x14d1140: r0 = copyWith()
    //     0x14d1140: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d1144: stur            x0, [fp, #-0x18]
    // 0x14d1148: r0 = Text()
    //     0x14d1148: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d114c: mov             x3, x0
    // 0x14d1150: r0 = "Terms & Condition"
    //     0x14d1150: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c98] "Terms & Condition"
    //     0x14d1154: ldr             x0, [x0, #0xc98]
    // 0x14d1158: stur            x3, [fp, #-0x20]
    // 0x14d115c: StoreField: r3->field_b = r0
    //     0x14d115c: stur            w0, [x3, #0xb]
    // 0x14d1160: ldur            x0, [fp, #-0x18]
    // 0x14d1164: StoreField: r3->field_13 = r0
    //     0x14d1164: stur            w0, [x3, #0x13]
    // 0x14d1168: r1 = Null
    //     0x14d1168: mov             x1, NULL
    // 0x14d116c: r2 = 2
    //     0x14d116c: movz            x2, #0x2
    // 0x14d1170: r0 = AllocateArray()
    //     0x14d1170: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d1174: mov             x2, x0
    // 0x14d1178: ldur            x0, [fp, #-0x20]
    // 0x14d117c: stur            x2, [fp, #-0x18]
    // 0x14d1180: StoreField: r2->field_f = r0
    //     0x14d1180: stur            w0, [x2, #0xf]
    // 0x14d1184: r1 = <Widget>
    //     0x14d1184: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d1188: r0 = AllocateGrowableArray()
    //     0x14d1188: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d118c: mov             x1, x0
    // 0x14d1190: ldur            x0, [fp, #-0x18]
    // 0x14d1194: stur            x1, [fp, #-0x20]
    // 0x14d1198: StoreField: r1->field_f = r0
    //     0x14d1198: stur            w0, [x1, #0xf]
    // 0x14d119c: r0 = 2
    //     0x14d119c: movz            x0, #0x2
    // 0x14d11a0: StoreField: r1->field_b = r0
    //     0x14d11a0: stur            w0, [x1, #0xb]
    // 0x14d11a4: r0 = Row()
    //     0x14d11a4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14d11a8: mov             x1, x0
    // 0x14d11ac: r0 = Instance_Axis
    //     0x14d11ac: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14d11b0: stur            x1, [fp, #-0x18]
    // 0x14d11b4: StoreField: r1->field_f = r0
    //     0x14d11b4: stur            w0, [x1, #0xf]
    // 0x14d11b8: r2 = Instance_MainAxisAlignment
    //     0x14d11b8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14d11bc: ldr             x2, [x2, #0xa08]
    // 0x14d11c0: StoreField: r1->field_13 = r2
    //     0x14d11c0: stur            w2, [x1, #0x13]
    // 0x14d11c4: r3 = Instance_MainAxisSize
    //     0x14d11c4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14d11c8: ldr             x3, [x3, #0xa10]
    // 0x14d11cc: ArrayStore: r1[0] = r3  ; List_4
    //     0x14d11cc: stur            w3, [x1, #0x17]
    // 0x14d11d0: r4 = Instance_CrossAxisAlignment
    //     0x14d11d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d11d4: ldr             x4, [x4, #0xa18]
    // 0x14d11d8: StoreField: r1->field_1b = r4
    //     0x14d11d8: stur            w4, [x1, #0x1b]
    // 0x14d11dc: r5 = Instance_VerticalDirection
    //     0x14d11dc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d11e0: ldr             x5, [x5, #0xa20]
    // 0x14d11e4: StoreField: r1->field_23 = r5
    //     0x14d11e4: stur            w5, [x1, #0x23]
    // 0x14d11e8: r6 = Instance_Clip
    //     0x14d11e8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d11ec: ldr             x6, [x6, #0x38]
    // 0x14d11f0: StoreField: r1->field_2b = r6
    //     0x14d11f0: stur            w6, [x1, #0x2b]
    // 0x14d11f4: StoreField: r1->field_2f = rZR
    //     0x14d11f4: stur            xzr, [x1, #0x2f]
    // 0x14d11f8: ldur            x7, [fp, #-0x20]
    // 0x14d11fc: StoreField: r1->field_b = r7
    //     0x14d11fc: stur            w7, [x1, #0xb]
    // 0x14d1200: r0 = InkWell()
    //     0x14d1200: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d1204: mov             x3, x0
    // 0x14d1208: ldur            x0, [fp, #-0x18]
    // 0x14d120c: stur            x3, [fp, #-0x20]
    // 0x14d1210: StoreField: r3->field_b = r0
    //     0x14d1210: stur            w0, [x3, #0xb]
    // 0x14d1214: ldur            x2, [fp, #-8]
    // 0x14d1218: r1 = Function '<anonymous closure>':.
    //     0x14d1218: add             x1, PP, #0x43, lsl #12  ; [pp+0x430c8] AnonymousClosure: (0x14d15b4), in [package:customer_app/app/presentation/views/cosmetic/profile/profile_view.dart] ProfileView::body (0x14d0714)
    //     0x14d121c: ldr             x1, [x1, #0xc8]
    // 0x14d1220: r0 = AllocateClosure()
    //     0x14d1220: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d1224: mov             x1, x0
    // 0x14d1228: ldur            x0, [fp, #-0x20]
    // 0x14d122c: StoreField: r0->field_f = r1
    //     0x14d122c: stur            w1, [x0, #0xf]
    // 0x14d1230: r1 = true
    //     0x14d1230: add             x1, NULL, #0x20  ; true
    // 0x14d1234: StoreField: r0->field_43 = r1
    //     0x14d1234: stur            w1, [x0, #0x43]
    // 0x14d1238: r2 = Instance_BoxShape
    //     0x14d1238: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d123c: ldr             x2, [x2, #0x80]
    // 0x14d1240: StoreField: r0->field_47 = r2
    //     0x14d1240: stur            w2, [x0, #0x47]
    // 0x14d1244: r3 = Instance_Color
    //     0x14d1244: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x14d1248: ldr             x3, [x3, #0xf88]
    // 0x14d124c: StoreField: r0->field_5f = r3
    //     0x14d124c: stur            w3, [x0, #0x5f]
    // 0x14d1250: r4 = Instance__NoSplashFactory
    //     0x14d1250: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x14d1254: ldr             x4, [x4, #0xc48]
    // 0x14d1258: StoreField: r0->field_6b = r4
    //     0x14d1258: stur            w4, [x0, #0x6b]
    // 0x14d125c: StoreField: r0->field_6f = r1
    //     0x14d125c: stur            w1, [x0, #0x6f]
    // 0x14d1260: r5 = false
    //     0x14d1260: add             x5, NULL, #0x30  ; false
    // 0x14d1264: StoreField: r0->field_73 = r5
    //     0x14d1264: stur            w5, [x0, #0x73]
    // 0x14d1268: StoreField: r0->field_83 = r1
    //     0x14d1268: stur            w1, [x0, #0x83]
    // 0x14d126c: StoreField: r0->field_7b = r5
    //     0x14d126c: stur            w5, [x0, #0x7b]
    // 0x14d1270: r0 = Align()
    //     0x14d1270: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14d1274: mov             x1, x0
    // 0x14d1278: r0 = Instance_Alignment
    //     0x14d1278: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x14d127c: ldr             x0, [x0, #0xfa0]
    // 0x14d1280: StoreField: r1->field_f = r0
    //     0x14d1280: stur            w0, [x1, #0xf]
    // 0x14d1284: ldur            x0, [fp, #-0x20]
    // 0x14d1288: StoreField: r1->field_b = r0
    //     0x14d1288: stur            w0, [x1, #0xb]
    // 0x14d128c: mov             x0, x1
    // 0x14d1290: ldur            x1, [fp, #-0x10]
    // 0x14d1294: ArrayStore: r1[10] = r0  ; List_4
    //     0x14d1294: add             x25, x1, #0x37
    //     0x14d1298: str             w0, [x25]
    //     0x14d129c: tbz             w0, #0, #0x14d12b8
    //     0x14d12a0: ldurb           w16, [x1, #-1]
    //     0x14d12a4: ldurb           w17, [x0, #-1]
    //     0x14d12a8: and             x16, x17, x16, lsr #2
    //     0x14d12ac: tst             x16, HEAP, lsr #32
    //     0x14d12b0: b.eq            #0x14d12b8
    //     0x14d12b4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14d12b8: ldur            x0, [fp, #-0x10]
    // 0x14d12bc: r16 = Instance_SizedBox
    //     0x14d12bc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x14d12c0: ldr             x16, [x16, #0xc50]
    // 0x14d12c4: StoreField: r0->field_3b = r16
    //     0x14d12c4: stur            w16, [x0, #0x3b]
    // 0x14d12c8: ldur            x2, [fp, #-8]
    // 0x14d12cc: LoadField: r1 = r2->field_f
    //     0x14d12cc: ldur            w1, [x2, #0xf]
    // 0x14d12d0: DecompressPointer r1
    //     0x14d12d0: add             x1, x1, HEAP, lsl #32
    // 0x14d12d4: r0 = controller()
    //     0x14d12d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d12d8: LoadField: r1 = r0->field_5b
    //     0x14d12d8: ldur            w1, [x0, #0x5b]
    // 0x14d12dc: DecompressPointer r1
    //     0x14d12dc: add             x1, x1, HEAP, lsl #32
    // 0x14d12e0: r0 = value()
    //     0x14d12e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d12e4: ldur            x2, [fp, #-8]
    // 0x14d12e8: stur            x0, [fp, #-0x18]
    // 0x14d12ec: LoadField: r1 = r2->field_13
    //     0x14d12ec: ldur            w1, [x2, #0x13]
    // 0x14d12f0: DecompressPointer r1
    //     0x14d12f0: add             x1, x1, HEAP, lsl #32
    // 0x14d12f4: r0 = of()
    //     0x14d12f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d12f8: LoadField: r1 = r0->field_87
    //     0x14d12f8: ldur            w1, [x0, #0x87]
    // 0x14d12fc: DecompressPointer r1
    //     0x14d12fc: add             x1, x1, HEAP, lsl #32
    // 0x14d1300: LoadField: r0 = r1->field_7
    //     0x14d1300: ldur            w0, [x1, #7]
    // 0x14d1304: DecompressPointer r0
    //     0x14d1304: add             x0, x0, HEAP, lsl #32
    // 0x14d1308: r16 = 14.000000
    //     0x14d1308: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14d130c: ldr             x16, [x16, #0x1d8]
    // 0x14d1310: str             x16, [SP]
    // 0x14d1314: mov             x1, x0
    // 0x14d1318: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x14d1318: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x14d131c: ldr             x4, [x4, #0x798]
    // 0x14d1320: r0 = copyWith()
    //     0x14d1320: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d1324: stur            x0, [fp, #-0x20]
    // 0x14d1328: r0 = Text()
    //     0x14d1328: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d132c: mov             x3, x0
    // 0x14d1330: r0 = "Logout"
    //     0x14d1330: add             x0, PP, #0x36, lsl #12  ; [pp+0x36cb8] "Logout"
    //     0x14d1334: ldr             x0, [x0, #0xcb8]
    // 0x14d1338: stur            x3, [fp, #-0x28]
    // 0x14d133c: StoreField: r3->field_b = r0
    //     0x14d133c: stur            w0, [x3, #0xb]
    // 0x14d1340: ldur            x0, [fp, #-0x20]
    // 0x14d1344: StoreField: r3->field_13 = r0
    //     0x14d1344: stur            w0, [x3, #0x13]
    // 0x14d1348: r1 = Null
    //     0x14d1348: mov             x1, NULL
    // 0x14d134c: r2 = 6
    //     0x14d134c: movz            x2, #0x6
    // 0x14d1350: r0 = AllocateArray()
    //     0x14d1350: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d1354: stur            x0, [fp, #-0x20]
    // 0x14d1358: r16 = Instance_Icon
    //     0x14d1358: add             x16, PP, #0x36, lsl #12  ; [pp+0x36cc0] Obj!Icon@d66a31
    //     0x14d135c: ldr             x16, [x16, #0xcc0]
    // 0x14d1360: StoreField: r0->field_f = r16
    //     0x14d1360: stur            w16, [x0, #0xf]
    // 0x14d1364: r16 = Instance_SizedBox
    //     0x14d1364: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x14d1368: ldr             x16, [x16, #0xaa8]
    // 0x14d136c: StoreField: r0->field_13 = r16
    //     0x14d136c: stur            w16, [x0, #0x13]
    // 0x14d1370: ldur            x1, [fp, #-0x28]
    // 0x14d1374: ArrayStore: r0[0] = r1  ; List_4
    //     0x14d1374: stur            w1, [x0, #0x17]
    // 0x14d1378: r1 = <Widget>
    //     0x14d1378: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d137c: r0 = AllocateGrowableArray()
    //     0x14d137c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d1380: mov             x1, x0
    // 0x14d1384: ldur            x0, [fp, #-0x20]
    // 0x14d1388: stur            x1, [fp, #-0x28]
    // 0x14d138c: StoreField: r1->field_f = r0
    //     0x14d138c: stur            w0, [x1, #0xf]
    // 0x14d1390: r0 = 6
    //     0x14d1390: movz            x0, #0x6
    // 0x14d1394: StoreField: r1->field_b = r0
    //     0x14d1394: stur            w0, [x1, #0xb]
    // 0x14d1398: r0 = Row()
    //     0x14d1398: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14d139c: mov             x1, x0
    // 0x14d13a0: r0 = Instance_Axis
    //     0x14d13a0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14d13a4: stur            x1, [fp, #-0x20]
    // 0x14d13a8: StoreField: r1->field_f = r0
    //     0x14d13a8: stur            w0, [x1, #0xf]
    // 0x14d13ac: r0 = Instance_MainAxisAlignment
    //     0x14d13ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14d13b0: ldr             x0, [x0, #0xa08]
    // 0x14d13b4: StoreField: r1->field_13 = r0
    //     0x14d13b4: stur            w0, [x1, #0x13]
    // 0x14d13b8: r2 = Instance_MainAxisSize
    //     0x14d13b8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14d13bc: ldr             x2, [x2, #0xa10]
    // 0x14d13c0: ArrayStore: r1[0] = r2  ; List_4
    //     0x14d13c0: stur            w2, [x1, #0x17]
    // 0x14d13c4: r3 = Instance_CrossAxisAlignment
    //     0x14d13c4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d13c8: ldr             x3, [x3, #0xa18]
    // 0x14d13cc: StoreField: r1->field_1b = r3
    //     0x14d13cc: stur            w3, [x1, #0x1b]
    // 0x14d13d0: r4 = Instance_VerticalDirection
    //     0x14d13d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d13d4: ldr             x4, [x4, #0xa20]
    // 0x14d13d8: StoreField: r1->field_23 = r4
    //     0x14d13d8: stur            w4, [x1, #0x23]
    // 0x14d13dc: r5 = Instance_Clip
    //     0x14d13dc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d13e0: ldr             x5, [x5, #0x38]
    // 0x14d13e4: StoreField: r1->field_2b = r5
    //     0x14d13e4: stur            w5, [x1, #0x2b]
    // 0x14d13e8: StoreField: r1->field_2f = rZR
    //     0x14d13e8: stur            xzr, [x1, #0x2f]
    // 0x14d13ec: ldur            x6, [fp, #-0x28]
    // 0x14d13f0: StoreField: r1->field_b = r6
    //     0x14d13f0: stur            w6, [x1, #0xb]
    // 0x14d13f4: r0 = InkWell()
    //     0x14d13f4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d13f8: mov             x3, x0
    // 0x14d13fc: ldur            x0, [fp, #-0x20]
    // 0x14d1400: stur            x3, [fp, #-0x28]
    // 0x14d1404: StoreField: r3->field_b = r0
    //     0x14d1404: stur            w0, [x3, #0xb]
    // 0x14d1408: ldur            x2, [fp, #-8]
    // 0x14d140c: r1 = Function '<anonymous closure>':.
    //     0x14d140c: add             x1, PP, #0x43, lsl #12  ; [pp+0x430d0] AnonymousClosure: (0x14762a4), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::body (0x1509484)
    //     0x14d1410: ldr             x1, [x1, #0xd0]
    // 0x14d1414: r0 = AllocateClosure()
    //     0x14d1414: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d1418: mov             x1, x0
    // 0x14d141c: ldur            x0, [fp, #-0x28]
    // 0x14d1420: StoreField: r0->field_f = r1
    //     0x14d1420: stur            w1, [x0, #0xf]
    // 0x14d1424: r1 = true
    //     0x14d1424: add             x1, NULL, #0x20  ; true
    // 0x14d1428: StoreField: r0->field_43 = r1
    //     0x14d1428: stur            w1, [x0, #0x43]
    // 0x14d142c: r2 = Instance_BoxShape
    //     0x14d142c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d1430: ldr             x2, [x2, #0x80]
    // 0x14d1434: StoreField: r0->field_47 = r2
    //     0x14d1434: stur            w2, [x0, #0x47]
    // 0x14d1438: r2 = Instance_Color
    //     0x14d1438: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x14d143c: ldr             x2, [x2, #0xf88]
    // 0x14d1440: StoreField: r0->field_5f = r2
    //     0x14d1440: stur            w2, [x0, #0x5f]
    // 0x14d1444: r2 = Instance__NoSplashFactory
    //     0x14d1444: add             x2, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x14d1448: ldr             x2, [x2, #0xc48]
    // 0x14d144c: StoreField: r0->field_6b = r2
    //     0x14d144c: stur            w2, [x0, #0x6b]
    // 0x14d1450: StoreField: r0->field_6f = r1
    //     0x14d1450: stur            w1, [x0, #0x6f]
    // 0x14d1454: r2 = false
    //     0x14d1454: add             x2, NULL, #0x30  ; false
    // 0x14d1458: StoreField: r0->field_73 = r2
    //     0x14d1458: stur            w2, [x0, #0x73]
    // 0x14d145c: StoreField: r0->field_83 = r1
    //     0x14d145c: stur            w1, [x0, #0x83]
    // 0x14d1460: StoreField: r0->field_7b = r2
    //     0x14d1460: stur            w2, [x0, #0x7b]
    // 0x14d1464: r0 = Visibility()
    //     0x14d1464: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14d1468: mov             x1, x0
    // 0x14d146c: ldur            x0, [fp, #-0x28]
    // 0x14d1470: StoreField: r1->field_b = r0
    //     0x14d1470: stur            w0, [x1, #0xb]
    // 0x14d1474: r0 = Instance_SizedBox
    //     0x14d1474: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14d1478: StoreField: r1->field_f = r0
    //     0x14d1478: stur            w0, [x1, #0xf]
    // 0x14d147c: ldur            x0, [fp, #-0x18]
    // 0x14d1480: StoreField: r1->field_13 = r0
    //     0x14d1480: stur            w0, [x1, #0x13]
    // 0x14d1484: r0 = false
    //     0x14d1484: add             x0, NULL, #0x30  ; false
    // 0x14d1488: ArrayStore: r1[0] = r0  ; List_4
    //     0x14d1488: stur            w0, [x1, #0x17]
    // 0x14d148c: StoreField: r1->field_1b = r0
    //     0x14d148c: stur            w0, [x1, #0x1b]
    // 0x14d1490: StoreField: r1->field_1f = r0
    //     0x14d1490: stur            w0, [x1, #0x1f]
    // 0x14d1494: StoreField: r1->field_23 = r0
    //     0x14d1494: stur            w0, [x1, #0x23]
    // 0x14d1498: StoreField: r1->field_27 = r0
    //     0x14d1498: stur            w0, [x1, #0x27]
    // 0x14d149c: StoreField: r1->field_2b = r0
    //     0x14d149c: stur            w0, [x1, #0x2b]
    // 0x14d14a0: mov             x0, x1
    // 0x14d14a4: ldur            x1, [fp, #-0x10]
    // 0x14d14a8: ArrayStore: r1[12] = r0  ; List_4
    //     0x14d14a8: add             x25, x1, #0x3f
    //     0x14d14ac: str             w0, [x25]
    //     0x14d14b0: tbz             w0, #0, #0x14d14cc
    //     0x14d14b4: ldurb           w16, [x1, #-1]
    //     0x14d14b8: ldurb           w17, [x0, #-1]
    //     0x14d14bc: and             x16, x17, x16, lsr #2
    //     0x14d14c0: tst             x16, HEAP, lsr #32
    //     0x14d14c4: b.eq            #0x14d14cc
    //     0x14d14c8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14d14cc: r1 = <Widget>
    //     0x14d14cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d14d0: r0 = AllocateGrowableArray()
    //     0x14d14d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d14d4: mov             x1, x0
    // 0x14d14d8: ldur            x0, [fp, #-0x10]
    // 0x14d14dc: stur            x1, [fp, #-8]
    // 0x14d14e0: StoreField: r1->field_f = r0
    //     0x14d14e0: stur            w0, [x1, #0xf]
    // 0x14d14e4: r0 = 26
    //     0x14d14e4: movz            x0, #0x1a
    // 0x14d14e8: StoreField: r1->field_b = r0
    //     0x14d14e8: stur            w0, [x1, #0xb]
    // 0x14d14ec: r0 = Column()
    //     0x14d14ec: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14d14f0: mov             x1, x0
    // 0x14d14f4: r0 = Instance_Axis
    //     0x14d14f4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14d14f8: stur            x1, [fp, #-0x10]
    // 0x14d14fc: StoreField: r1->field_f = r0
    //     0x14d14fc: stur            w0, [x1, #0xf]
    // 0x14d1500: r0 = Instance_MainAxisAlignment
    //     0x14d1500: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14d1504: ldr             x0, [x0, #0xa08]
    // 0x14d1508: StoreField: r1->field_13 = r0
    //     0x14d1508: stur            w0, [x1, #0x13]
    // 0x14d150c: r0 = Instance_MainAxisSize
    //     0x14d150c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14d1510: ldr             x0, [x0, #0xa10]
    // 0x14d1514: ArrayStore: r1[0] = r0  ; List_4
    //     0x14d1514: stur            w0, [x1, #0x17]
    // 0x14d1518: r0 = Instance_CrossAxisAlignment
    //     0x14d1518: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d151c: ldr             x0, [x0, #0xa18]
    // 0x14d1520: StoreField: r1->field_1b = r0
    //     0x14d1520: stur            w0, [x1, #0x1b]
    // 0x14d1524: r0 = Instance_VerticalDirection
    //     0x14d1524: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d1528: ldr             x0, [x0, #0xa20]
    // 0x14d152c: StoreField: r1->field_23 = r0
    //     0x14d152c: stur            w0, [x1, #0x23]
    // 0x14d1530: r0 = Instance_Clip
    //     0x14d1530: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d1534: ldr             x0, [x0, #0x38]
    // 0x14d1538: StoreField: r1->field_2b = r0
    //     0x14d1538: stur            w0, [x1, #0x2b]
    // 0x14d153c: StoreField: r1->field_2f = rZR
    //     0x14d153c: stur            xzr, [x1, #0x2f]
    // 0x14d1540: ldur            x0, [fp, #-8]
    // 0x14d1544: StoreField: r1->field_b = r0
    //     0x14d1544: stur            w0, [x1, #0xb]
    // 0x14d1548: r0 = Padding()
    //     0x14d1548: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14d154c: mov             x1, x0
    // 0x14d1550: r0 = Instance_EdgeInsets
    //     0x14d1550: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x14d1554: ldr             x0, [x0, #0x980]
    // 0x14d1558: stur            x1, [fp, #-8]
    // 0x14d155c: StoreField: r1->field_f = r0
    //     0x14d155c: stur            w0, [x1, #0xf]
    // 0x14d1560: ldur            x0, [fp, #-0x10]
    // 0x14d1564: StoreField: r1->field_b = r0
    //     0x14d1564: stur            w0, [x1, #0xb]
    // 0x14d1568: r0 = Card()
    //     0x14d1568: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0x14d156c: r1 = Instance_Color
    //     0x14d156c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x14d1570: ldr             x1, [x1, #0x90]
    // 0x14d1574: StoreField: r0->field_b = r1
    //     0x14d1574: stur            w1, [x0, #0xb]
    // 0x14d1578: r1 = 0.000000
    //     0x14d1578: ldr             x1, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x14d157c: ArrayStore: r0[0] = r1  ; List_4
    //     0x14d157c: stur            w1, [x0, #0x17]
    // 0x14d1580: r1 = true
    //     0x14d1580: add             x1, NULL, #0x20  ; true
    // 0x14d1584: StoreField: r0->field_1f = r1
    //     0x14d1584: stur            w1, [x0, #0x1f]
    // 0x14d1588: ldur            x2, [fp, #-8]
    // 0x14d158c: StoreField: r0->field_2f = r2
    //     0x14d158c: stur            w2, [x0, #0x2f]
    // 0x14d1590: StoreField: r0->field_2b = r1
    //     0x14d1590: stur            w1, [x0, #0x2b]
    // 0x14d1594: r1 = Instance__CardVariant
    //     0x14d1594: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0x14d1598: ldr             x1, [x1, #0xa68]
    // 0x14d159c: StoreField: r0->field_33 = r1
    //     0x14d159c: stur            w1, [x0, #0x33]
    // 0x14d15a0: LeaveFrame
    //     0x14d15a0: mov             SP, fp
    //     0x14d15a4: ldp             fp, lr, [SP], #0x10
    // 0x14d15a8: ret
    //     0x14d15a8: ret             
    // 0x14d15ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d15ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d15b0: b               #0x14d07a0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14d15b4, size: 0x50
    // 0x14d15b4: EnterFrame
    //     0x14d15b4: stp             fp, lr, [SP, #-0x10]!
    //     0x14d15b8: mov             fp, SP
    // 0x14d15bc: ldr             x0, [fp, #0x10]
    // 0x14d15c0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14d15c0: ldur            w1, [x0, #0x17]
    // 0x14d15c4: DecompressPointer r1
    //     0x14d15c4: add             x1, x1, HEAP, lsl #32
    // 0x14d15c8: CheckStackOverflow
    //     0x14d15c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d15cc: cmp             SP, x16
    //     0x14d15d0: b.ls            #0x14d15fc
    // 0x14d15d4: LoadField: r0 = r1->field_f
    //     0x14d15d4: ldur            w0, [x1, #0xf]
    // 0x14d15d8: DecompressPointer r0
    //     0x14d15d8: add             x0, x0, HEAP, lsl #32
    // 0x14d15dc: mov             x1, x0
    // 0x14d15e0: r2 = "terms_and_conditions"
    //     0x14d15e0: add             x2, PP, #0x36, lsl #12  ; [pp+0x36cd0] "terms_and_conditions"
    //     0x14d15e4: ldr             x2, [x2, #0xcd0]
    // 0x14d15e8: r0 = openPolicyPage()
    //     0x14d15e8: bl              #0x1477424  ; [package:customer_app/app/presentation/views/basic/profile/profile_view.dart] ProfileView::openPolicyPage
    // 0x14d15ec: r0 = Null
    //     0x14d15ec: mov             x0, NULL
    // 0x14d15f0: LeaveFrame
    //     0x14d15f0: mov             SP, fp
    //     0x14d15f4: ldp             fp, lr, [SP], #0x10
    // 0x14d15f8: ret
    //     0x14d15f8: ret             
    // 0x14d15fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d15fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d1600: b               #0x14d15d4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14d1604, size: 0x50
    // 0x14d1604: EnterFrame
    //     0x14d1604: stp             fp, lr, [SP, #-0x10]!
    //     0x14d1608: mov             fp, SP
    // 0x14d160c: ldr             x0, [fp, #0x10]
    // 0x14d1610: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14d1610: ldur            w1, [x0, #0x17]
    // 0x14d1614: DecompressPointer r1
    //     0x14d1614: add             x1, x1, HEAP, lsl #32
    // 0x14d1618: CheckStackOverflow
    //     0x14d1618: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d161c: cmp             SP, x16
    //     0x14d1620: b.ls            #0x14d164c
    // 0x14d1624: LoadField: r0 = r1->field_f
    //     0x14d1624: ldur            w0, [x1, #0xf]
    // 0x14d1628: DecompressPointer r0
    //     0x14d1628: add             x0, x0, HEAP, lsl #32
    // 0x14d162c: mov             x1, x0
    // 0x14d1630: r2 = "shipping_policy"
    //     0x14d1630: add             x2, PP, #0x36, lsl #12  ; [pp+0x36cd8] "shipping_policy"
    //     0x14d1634: ldr             x2, [x2, #0xcd8]
    // 0x14d1638: r0 = openPolicyPage()
    //     0x14d1638: bl              #0x1477424  ; [package:customer_app/app/presentation/views/basic/profile/profile_view.dart] ProfileView::openPolicyPage
    // 0x14d163c: r0 = Null
    //     0x14d163c: mov             x0, NULL
    // 0x14d1640: LeaveFrame
    //     0x14d1640: mov             SP, fp
    //     0x14d1644: ldp             fp, lr, [SP], #0x10
    // 0x14d1648: ret
    //     0x14d1648: ret             
    // 0x14d164c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d164c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d1650: b               #0x14d1624
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14d1654, size: 0x50
    // 0x14d1654: EnterFrame
    //     0x14d1654: stp             fp, lr, [SP, #-0x10]!
    //     0x14d1658: mov             fp, SP
    // 0x14d165c: ldr             x0, [fp, #0x10]
    // 0x14d1660: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14d1660: ldur            w1, [x0, #0x17]
    // 0x14d1664: DecompressPointer r1
    //     0x14d1664: add             x1, x1, HEAP, lsl #32
    // 0x14d1668: CheckStackOverflow
    //     0x14d1668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d166c: cmp             SP, x16
    //     0x14d1670: b.ls            #0x14d169c
    // 0x14d1674: LoadField: r0 = r1->field_f
    //     0x14d1674: ldur            w0, [x1, #0xf]
    // 0x14d1678: DecompressPointer r0
    //     0x14d1678: add             x0, x0, HEAP, lsl #32
    // 0x14d167c: mov             x1, x0
    // 0x14d1680: r2 = "return_policy"
    //     0x14d1680: add             x2, PP, #0x36, lsl #12  ; [pp+0x36ce0] "return_policy"
    //     0x14d1684: ldr             x2, [x2, #0xce0]
    // 0x14d1688: r0 = openPolicyPage()
    //     0x14d1688: bl              #0x1477424  ; [package:customer_app/app/presentation/views/basic/profile/profile_view.dart] ProfileView::openPolicyPage
    // 0x14d168c: r0 = Null
    //     0x14d168c: mov             x0, NULL
    // 0x14d1690: LeaveFrame
    //     0x14d1690: mov             SP, fp
    //     0x14d1694: ldp             fp, lr, [SP], #0x10
    // 0x14d1698: ret
    //     0x14d1698: ret             
    // 0x14d169c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d169c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d16a0: b               #0x14d1674
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14d16a4, size: 0x50
    // 0x14d16a4: EnterFrame
    //     0x14d16a4: stp             fp, lr, [SP, #-0x10]!
    //     0x14d16a8: mov             fp, SP
    // 0x14d16ac: ldr             x0, [fp, #0x10]
    // 0x14d16b0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14d16b0: ldur            w1, [x0, #0x17]
    // 0x14d16b4: DecompressPointer r1
    //     0x14d16b4: add             x1, x1, HEAP, lsl #32
    // 0x14d16b8: CheckStackOverflow
    //     0x14d16b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d16bc: cmp             SP, x16
    //     0x14d16c0: b.ls            #0x14d16ec
    // 0x14d16c4: LoadField: r0 = r1->field_f
    //     0x14d16c4: ldur            w0, [x1, #0xf]
    // 0x14d16c8: DecompressPointer r0
    //     0x14d16c8: add             x0, x0, HEAP, lsl #32
    // 0x14d16cc: mov             x1, x0
    // 0x14d16d0: r2 = "privacy_policy"
    //     0x14d16d0: add             x2, PP, #0x36, lsl #12  ; [pp+0x36ce8] "privacy_policy"
    //     0x14d16d4: ldr             x2, [x2, #0xce8]
    // 0x14d16d8: r0 = openPolicyPage()
    //     0x14d16d8: bl              #0x1477424  ; [package:customer_app/app/presentation/views/basic/profile/profile_view.dart] ProfileView::openPolicyPage
    // 0x14d16dc: r0 = Null
    //     0x14d16dc: mov             x0, NULL
    // 0x14d16e0: LeaveFrame
    //     0x14d16e0: mov             SP, fp
    //     0x14d16e4: ldp             fp, lr, [SP], #0x10
    // 0x14d16e8: ret
    //     0x14d16e8: ret             
    // 0x14d16ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d16ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d16f0: b               #0x14d16c4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14d16f4, size: 0x50
    // 0x14d16f4: EnterFrame
    //     0x14d16f4: stp             fp, lr, [SP, #-0x10]!
    //     0x14d16f8: mov             fp, SP
    // 0x14d16fc: ldr             x0, [fp, #0x10]
    // 0x14d1700: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14d1700: ldur            w1, [x0, #0x17]
    // 0x14d1704: DecompressPointer r1
    //     0x14d1704: add             x1, x1, HEAP, lsl #32
    // 0x14d1708: CheckStackOverflow
    //     0x14d1708: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d170c: cmp             SP, x16
    //     0x14d1710: b.ls            #0x14d173c
    // 0x14d1714: LoadField: r0 = r1->field_f
    //     0x14d1714: ldur            w0, [x1, #0xf]
    // 0x14d1718: DecompressPointer r0
    //     0x14d1718: add             x0, x0, HEAP, lsl #32
    // 0x14d171c: mov             x1, x0
    // 0x14d1720: r2 = "about_us"
    //     0x14d1720: add             x2, PP, #0x36, lsl #12  ; [pp+0x36cf0] "about_us"
    //     0x14d1724: ldr             x2, [x2, #0xcf0]
    // 0x14d1728: r0 = openPolicyPage()
    //     0x14d1728: bl              #0x1477424  ; [package:customer_app/app/presentation/views/basic/profile/profile_view.dart] ProfileView::openPolicyPage
    // 0x14d172c: r0 = Null
    //     0x14d172c: mov             x0, NULL
    // 0x14d1730: LeaveFrame
    //     0x14d1730: mov             SP, fp
    //     0x14d1734: ldp             fp, lr, [SP], #0x10
    // 0x14d1738: ret
    //     0x14d1738: ret             
    // 0x14d173c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d173c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d1740: b               #0x14d1714
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15de4ec, size: 0x2b4
    // 0x15de4ec: EnterFrame
    //     0x15de4ec: stp             fp, lr, [SP, #-0x10]!
    //     0x15de4f0: mov             fp, SP
    // 0x15de4f4: AllocStack(0x30)
    //     0x15de4f4: sub             SP, SP, #0x30
    // 0x15de4f8: SetupParameters(ProfileView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15de4f8: stur            x1, [fp, #-8]
    //     0x15de4fc: stur            x2, [fp, #-0x10]
    // 0x15de500: CheckStackOverflow
    //     0x15de500: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15de504: cmp             SP, x16
    //     0x15de508: b.ls            #0x15de798
    // 0x15de50c: r1 = 2
    //     0x15de50c: movz            x1, #0x2
    // 0x15de510: r0 = AllocateContext()
    //     0x15de510: bl              #0x16f6108  ; AllocateContextStub
    // 0x15de514: ldur            x1, [fp, #-8]
    // 0x15de518: stur            x0, [fp, #-0x18]
    // 0x15de51c: StoreField: r0->field_f = r1
    //     0x15de51c: stur            w1, [x0, #0xf]
    // 0x15de520: ldur            x2, [fp, #-0x10]
    // 0x15de524: StoreField: r0->field_13 = r2
    //     0x15de524: stur            w2, [x0, #0x13]
    // 0x15de528: r0 = Obx()
    //     0x15de528: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15de52c: ldur            x2, [fp, #-0x18]
    // 0x15de530: r1 = Function '<anonymous closure>':.
    //     0x15de530: add             x1, PP, #0x43, lsl #12  ; [pp+0x430d8] AnonymousClosure: (0x15d3254), in [package:customer_app/app/presentation/views/glass/profile/profile_view.dart] ProfileView::appBar (0x15e5558)
    //     0x15de534: ldr             x1, [x1, #0xd8]
    // 0x15de538: stur            x0, [fp, #-0x10]
    // 0x15de53c: r0 = AllocateClosure()
    //     0x15de53c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15de540: mov             x1, x0
    // 0x15de544: ldur            x0, [fp, #-0x10]
    // 0x15de548: StoreField: r0->field_b = r1
    //     0x15de548: stur            w1, [x0, #0xb]
    // 0x15de54c: ldur            x1, [fp, #-8]
    // 0x15de550: r0 = controller()
    //     0x15de550: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15de554: LoadField: r1 = r0->field_7b
    //     0x15de554: ldur            w1, [x0, #0x7b]
    // 0x15de558: DecompressPointer r1
    //     0x15de558: add             x1, x1, HEAP, lsl #32
    // 0x15de55c: r0 = value()
    //     0x15de55c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15de560: tbnz            w0, #4, #0x15de5f8
    // 0x15de564: ldur            x2, [fp, #-0x18]
    // 0x15de568: LoadField: r1 = r2->field_13
    //     0x15de568: ldur            w1, [x2, #0x13]
    // 0x15de56c: DecompressPointer r1
    //     0x15de56c: add             x1, x1, HEAP, lsl #32
    // 0x15de570: r0 = of()
    //     0x15de570: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15de574: LoadField: r1 = r0->field_5b
    //     0x15de574: ldur            w1, [x0, #0x5b]
    // 0x15de578: DecompressPointer r1
    //     0x15de578: add             x1, x1, HEAP, lsl #32
    // 0x15de57c: stur            x1, [fp, #-8]
    // 0x15de580: r0 = ColorFilter()
    //     0x15de580: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15de584: mov             x1, x0
    // 0x15de588: ldur            x0, [fp, #-8]
    // 0x15de58c: stur            x1, [fp, #-0x20]
    // 0x15de590: StoreField: r1->field_7 = r0
    //     0x15de590: stur            w0, [x1, #7]
    // 0x15de594: r0 = Instance_BlendMode
    //     0x15de594: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15de598: ldr             x0, [x0, #0xb30]
    // 0x15de59c: StoreField: r1->field_b = r0
    //     0x15de59c: stur            w0, [x1, #0xb]
    // 0x15de5a0: r2 = 1
    //     0x15de5a0: movz            x2, #0x1
    // 0x15de5a4: StoreField: r1->field_13 = r2
    //     0x15de5a4: stur            x2, [x1, #0x13]
    // 0x15de5a8: r0 = SvgPicture()
    //     0x15de5a8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15de5ac: stur            x0, [fp, #-8]
    // 0x15de5b0: ldur            x16, [fp, #-0x20]
    // 0x15de5b4: str             x16, [SP]
    // 0x15de5b8: mov             x1, x0
    // 0x15de5bc: r2 = "assets/images/search.svg"
    //     0x15de5bc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15de5c0: ldr             x2, [x2, #0xa30]
    // 0x15de5c4: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15de5c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15de5c8: ldr             x4, [x4, #0xa38]
    // 0x15de5cc: r0 = SvgPicture.asset()
    //     0x15de5cc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15de5d0: r0 = Align()
    //     0x15de5d0: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15de5d4: r3 = Instance_Alignment
    //     0x15de5d4: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15de5d8: ldr             x3, [x3, #0xb10]
    // 0x15de5dc: StoreField: r0->field_f = r3
    //     0x15de5dc: stur            w3, [x0, #0xf]
    // 0x15de5e0: r4 = 1.000000
    //     0x15de5e0: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15de5e4: StoreField: r0->field_13 = r4
    //     0x15de5e4: stur            w4, [x0, #0x13]
    // 0x15de5e8: ArrayStore: r0[0] = r4  ; List_4
    //     0x15de5e8: stur            w4, [x0, #0x17]
    // 0x15de5ec: ldur            x1, [fp, #-8]
    // 0x15de5f0: StoreField: r0->field_b = r1
    //     0x15de5f0: stur            w1, [x0, #0xb]
    // 0x15de5f4: b               #0x15de6a8
    // 0x15de5f8: ldur            x5, [fp, #-0x18]
    // 0x15de5fc: r4 = 1.000000
    //     0x15de5fc: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15de600: r0 = Instance_BlendMode
    //     0x15de600: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15de604: ldr             x0, [x0, #0xb30]
    // 0x15de608: r3 = Instance_Alignment
    //     0x15de608: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15de60c: ldr             x3, [x3, #0xb10]
    // 0x15de610: r2 = 1
    //     0x15de610: movz            x2, #0x1
    // 0x15de614: LoadField: r1 = r5->field_13
    //     0x15de614: ldur            w1, [x5, #0x13]
    // 0x15de618: DecompressPointer r1
    //     0x15de618: add             x1, x1, HEAP, lsl #32
    // 0x15de61c: r0 = of()
    //     0x15de61c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15de620: LoadField: r1 = r0->field_5b
    //     0x15de620: ldur            w1, [x0, #0x5b]
    // 0x15de624: DecompressPointer r1
    //     0x15de624: add             x1, x1, HEAP, lsl #32
    // 0x15de628: stur            x1, [fp, #-8]
    // 0x15de62c: r0 = ColorFilter()
    //     0x15de62c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15de630: mov             x1, x0
    // 0x15de634: ldur            x0, [fp, #-8]
    // 0x15de638: stur            x1, [fp, #-0x20]
    // 0x15de63c: StoreField: r1->field_7 = r0
    //     0x15de63c: stur            w0, [x1, #7]
    // 0x15de640: r0 = Instance_BlendMode
    //     0x15de640: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15de644: ldr             x0, [x0, #0xb30]
    // 0x15de648: StoreField: r1->field_b = r0
    //     0x15de648: stur            w0, [x1, #0xb]
    // 0x15de64c: r0 = 1
    //     0x15de64c: movz            x0, #0x1
    // 0x15de650: StoreField: r1->field_13 = r0
    //     0x15de650: stur            x0, [x1, #0x13]
    // 0x15de654: r0 = SvgPicture()
    //     0x15de654: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15de658: stur            x0, [fp, #-8]
    // 0x15de65c: ldur            x16, [fp, #-0x20]
    // 0x15de660: str             x16, [SP]
    // 0x15de664: mov             x1, x0
    // 0x15de668: r2 = "assets/images/appbar_arrow.svg"
    //     0x15de668: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15de66c: ldr             x2, [x2, #0xa40]
    // 0x15de670: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15de670: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15de674: ldr             x4, [x4, #0xa38]
    // 0x15de678: r0 = SvgPicture.asset()
    //     0x15de678: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15de67c: r0 = Align()
    //     0x15de67c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15de680: mov             x1, x0
    // 0x15de684: r0 = Instance_Alignment
    //     0x15de684: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15de688: ldr             x0, [x0, #0xb10]
    // 0x15de68c: StoreField: r1->field_f = r0
    //     0x15de68c: stur            w0, [x1, #0xf]
    // 0x15de690: r0 = 1.000000
    //     0x15de690: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15de694: StoreField: r1->field_13 = r0
    //     0x15de694: stur            w0, [x1, #0x13]
    // 0x15de698: ArrayStore: r1[0] = r0  ; List_4
    //     0x15de698: stur            w0, [x1, #0x17]
    // 0x15de69c: ldur            x0, [fp, #-8]
    // 0x15de6a0: StoreField: r1->field_b = r0
    //     0x15de6a0: stur            w0, [x1, #0xb]
    // 0x15de6a4: mov             x0, x1
    // 0x15de6a8: stur            x0, [fp, #-8]
    // 0x15de6ac: r0 = InkWell()
    //     0x15de6ac: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15de6b0: mov             x3, x0
    // 0x15de6b4: ldur            x0, [fp, #-8]
    // 0x15de6b8: stur            x3, [fp, #-0x20]
    // 0x15de6bc: StoreField: r3->field_b = r0
    //     0x15de6bc: stur            w0, [x3, #0xb]
    // 0x15de6c0: ldur            x2, [fp, #-0x18]
    // 0x15de6c4: r1 = Function '<anonymous closure>':.
    //     0x15de6c4: add             x1, PP, #0x43, lsl #12  ; [pp+0x430e0] AnonymousClosure: (0x15d71a8), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::appBar (0x15edec4)
    //     0x15de6c8: ldr             x1, [x1, #0xe0]
    // 0x15de6cc: r0 = AllocateClosure()
    //     0x15de6cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15de6d0: ldur            x2, [fp, #-0x20]
    // 0x15de6d4: StoreField: r2->field_f = r0
    //     0x15de6d4: stur            w0, [x2, #0xf]
    // 0x15de6d8: r0 = true
    //     0x15de6d8: add             x0, NULL, #0x20  ; true
    // 0x15de6dc: StoreField: r2->field_43 = r0
    //     0x15de6dc: stur            w0, [x2, #0x43]
    // 0x15de6e0: r1 = Instance_BoxShape
    //     0x15de6e0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15de6e4: ldr             x1, [x1, #0x80]
    // 0x15de6e8: StoreField: r2->field_47 = r1
    //     0x15de6e8: stur            w1, [x2, #0x47]
    // 0x15de6ec: StoreField: r2->field_6f = r0
    //     0x15de6ec: stur            w0, [x2, #0x6f]
    // 0x15de6f0: r1 = false
    //     0x15de6f0: add             x1, NULL, #0x30  ; false
    // 0x15de6f4: StoreField: r2->field_73 = r1
    //     0x15de6f4: stur            w1, [x2, #0x73]
    // 0x15de6f8: StoreField: r2->field_83 = r0
    //     0x15de6f8: stur            w0, [x2, #0x83]
    // 0x15de6fc: StoreField: r2->field_7b = r1
    //     0x15de6fc: stur            w1, [x2, #0x7b]
    // 0x15de700: r0 = Obx()
    //     0x15de700: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15de704: ldur            x2, [fp, #-0x18]
    // 0x15de708: r1 = Function '<anonymous closure>':.
    //     0x15de708: add             x1, PP, #0x43, lsl #12  ; [pp+0x430e8] AnonymousClosure: (0x15de7a0), in [package:customer_app/app/presentation/views/cosmetic/profile/profile_view.dart] ProfileView::appBar (0x15de4ec)
    //     0x15de70c: ldr             x1, [x1, #0xe8]
    // 0x15de710: stur            x0, [fp, #-8]
    // 0x15de714: r0 = AllocateClosure()
    //     0x15de714: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15de718: mov             x1, x0
    // 0x15de71c: ldur            x0, [fp, #-8]
    // 0x15de720: StoreField: r0->field_b = r1
    //     0x15de720: stur            w1, [x0, #0xb]
    // 0x15de724: r1 = Null
    //     0x15de724: mov             x1, NULL
    // 0x15de728: r2 = 2
    //     0x15de728: movz            x2, #0x2
    // 0x15de72c: r0 = AllocateArray()
    //     0x15de72c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15de730: mov             x2, x0
    // 0x15de734: ldur            x0, [fp, #-8]
    // 0x15de738: stur            x2, [fp, #-0x18]
    // 0x15de73c: StoreField: r2->field_f = r0
    //     0x15de73c: stur            w0, [x2, #0xf]
    // 0x15de740: r1 = <Widget>
    //     0x15de740: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15de744: r0 = AllocateGrowableArray()
    //     0x15de744: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15de748: mov             x1, x0
    // 0x15de74c: ldur            x0, [fp, #-0x18]
    // 0x15de750: stur            x1, [fp, #-8]
    // 0x15de754: StoreField: r1->field_f = r0
    //     0x15de754: stur            w0, [x1, #0xf]
    // 0x15de758: r0 = 2
    //     0x15de758: movz            x0, #0x2
    // 0x15de75c: StoreField: r1->field_b = r0
    //     0x15de75c: stur            w0, [x1, #0xb]
    // 0x15de760: r0 = AppBar()
    //     0x15de760: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15de764: stur            x0, [fp, #-0x18]
    // 0x15de768: ldur            x16, [fp, #-0x10]
    // 0x15de76c: ldur            lr, [fp, #-8]
    // 0x15de770: stp             lr, x16, [SP]
    // 0x15de774: mov             x1, x0
    // 0x15de778: ldur            x2, [fp, #-0x20]
    // 0x15de77c: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15de77c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15de780: ldr             x4, [x4, #0xa58]
    // 0x15de784: r0 = AppBar()
    //     0x15de784: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15de788: ldur            x0, [fp, #-0x18]
    // 0x15de78c: LeaveFrame
    //     0x15de78c: mov             SP, fp
    //     0x15de790: ldp             fp, lr, [SP], #0x10
    // 0x15de794: ret
    //     0x15de794: ret             
    // 0x15de798: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15de798: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15de79c: b               #0x15de50c
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15de7a0, size: 0x2fc
    // 0x15de7a0: EnterFrame
    //     0x15de7a0: stp             fp, lr, [SP, #-0x10]!
    //     0x15de7a4: mov             fp, SP
    // 0x15de7a8: AllocStack(0x58)
    //     0x15de7a8: sub             SP, SP, #0x58
    // 0x15de7ac: SetupParameters()
    //     0x15de7ac: ldr             x0, [fp, #0x10]
    //     0x15de7b0: ldur            w2, [x0, #0x17]
    //     0x15de7b4: add             x2, x2, HEAP, lsl #32
    //     0x15de7b8: stur            x2, [fp, #-8]
    // 0x15de7bc: CheckStackOverflow
    //     0x15de7bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15de7c0: cmp             SP, x16
    //     0x15de7c4: b.ls            #0x15dea94
    // 0x15de7c8: LoadField: r1 = r2->field_f
    //     0x15de7c8: ldur            w1, [x2, #0xf]
    // 0x15de7cc: DecompressPointer r1
    //     0x15de7cc: add             x1, x1, HEAP, lsl #32
    // 0x15de7d0: r0 = controller()
    //     0x15de7d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15de7d4: LoadField: r1 = r0->field_6b
    //     0x15de7d4: ldur            w1, [x0, #0x6b]
    // 0x15de7d8: DecompressPointer r1
    //     0x15de7d8: add             x1, x1, HEAP, lsl #32
    // 0x15de7dc: r0 = value()
    //     0x15de7dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15de7e0: LoadField: r1 = r0->field_1f
    //     0x15de7e0: ldur            w1, [x0, #0x1f]
    // 0x15de7e4: DecompressPointer r1
    //     0x15de7e4: add             x1, x1, HEAP, lsl #32
    // 0x15de7e8: cmp             w1, NULL
    // 0x15de7ec: b.ne            #0x15de7f8
    // 0x15de7f0: r0 = Null
    //     0x15de7f0: mov             x0, NULL
    // 0x15de7f4: b               #0x15de800
    // 0x15de7f8: LoadField: r0 = r1->field_7
    //     0x15de7f8: ldur            w0, [x1, #7]
    // 0x15de7fc: DecompressPointer r0
    //     0x15de7fc: add             x0, x0, HEAP, lsl #32
    // 0x15de800: cmp             w0, NULL
    // 0x15de804: b.ne            #0x15de810
    // 0x15de808: r0 = false
    //     0x15de808: add             x0, NULL, #0x30  ; false
    // 0x15de80c: b               #0x15de9fc
    // 0x15de810: tbnz            w0, #4, #0x15de9f8
    // 0x15de814: ldur            x2, [fp, #-8]
    // 0x15de818: LoadField: r1 = r2->field_f
    //     0x15de818: ldur            w1, [x2, #0xf]
    // 0x15de81c: DecompressPointer r1
    //     0x15de81c: add             x1, x1, HEAP, lsl #32
    // 0x15de820: r0 = controller()
    //     0x15de820: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15de824: LoadField: r1 = r0->field_7f
    //     0x15de824: ldur            w1, [x0, #0x7f]
    // 0x15de828: DecompressPointer r1
    //     0x15de828: add             x1, x1, HEAP, lsl #32
    // 0x15de82c: r0 = value()
    //     0x15de82c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15de830: ldur            x2, [fp, #-8]
    // 0x15de834: stur            x0, [fp, #-0x10]
    // 0x15de838: LoadField: r1 = r2->field_13
    //     0x15de838: ldur            w1, [x2, #0x13]
    // 0x15de83c: DecompressPointer r1
    //     0x15de83c: add             x1, x1, HEAP, lsl #32
    // 0x15de840: r0 = of()
    //     0x15de840: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15de844: LoadField: r2 = r0->field_5b
    //     0x15de844: ldur            w2, [x0, #0x5b]
    // 0x15de848: DecompressPointer r2
    //     0x15de848: add             x2, x2, HEAP, lsl #32
    // 0x15de84c: ldur            x0, [fp, #-8]
    // 0x15de850: stur            x2, [fp, #-0x18]
    // 0x15de854: LoadField: r1 = r0->field_f
    //     0x15de854: ldur            w1, [x0, #0xf]
    // 0x15de858: DecompressPointer r1
    //     0x15de858: add             x1, x1, HEAP, lsl #32
    // 0x15de85c: r0 = controller()
    //     0x15de85c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15de860: LoadField: r1 = r0->field_83
    //     0x15de860: ldur            w1, [x0, #0x83]
    // 0x15de864: DecompressPointer r1
    //     0x15de864: add             x1, x1, HEAP, lsl #32
    // 0x15de868: r0 = value()
    //     0x15de868: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15de86c: cmp             w0, NULL
    // 0x15de870: r16 = true
    //     0x15de870: add             x16, NULL, #0x20  ; true
    // 0x15de874: r17 = false
    //     0x15de874: add             x17, NULL, #0x30  ; false
    // 0x15de878: csel            x2, x16, x17, ne
    // 0x15de87c: ldur            x0, [fp, #-8]
    // 0x15de880: stur            x2, [fp, #-0x20]
    // 0x15de884: LoadField: r1 = r0->field_f
    //     0x15de884: ldur            w1, [x0, #0xf]
    // 0x15de888: DecompressPointer r1
    //     0x15de888: add             x1, x1, HEAP, lsl #32
    // 0x15de88c: r0 = controller()
    //     0x15de88c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15de890: LoadField: r1 = r0->field_83
    //     0x15de890: ldur            w1, [x0, #0x83]
    // 0x15de894: DecompressPointer r1
    //     0x15de894: add             x1, x1, HEAP, lsl #32
    // 0x15de898: r0 = value()
    //     0x15de898: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15de89c: str             x0, [SP]
    // 0x15de8a0: r0 = _interpolateSingle()
    //     0x15de8a0: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15de8a4: ldur            x2, [fp, #-8]
    // 0x15de8a8: stur            x0, [fp, #-0x28]
    // 0x15de8ac: LoadField: r1 = r2->field_13
    //     0x15de8ac: ldur            w1, [x2, #0x13]
    // 0x15de8b0: DecompressPointer r1
    //     0x15de8b0: add             x1, x1, HEAP, lsl #32
    // 0x15de8b4: r0 = of()
    //     0x15de8b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15de8b8: LoadField: r1 = r0->field_87
    //     0x15de8b8: ldur            w1, [x0, #0x87]
    // 0x15de8bc: DecompressPointer r1
    //     0x15de8bc: add             x1, x1, HEAP, lsl #32
    // 0x15de8c0: LoadField: r0 = r1->field_27
    //     0x15de8c0: ldur            w0, [x1, #0x27]
    // 0x15de8c4: DecompressPointer r0
    //     0x15de8c4: add             x0, x0, HEAP, lsl #32
    // 0x15de8c8: r16 = Instance_Color
    //     0x15de8c8: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15de8cc: str             x16, [SP]
    // 0x15de8d0: mov             x1, x0
    // 0x15de8d4: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15de8d4: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15de8d8: ldr             x4, [x4, #0xf40]
    // 0x15de8dc: r0 = copyWith()
    //     0x15de8dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15de8e0: stur            x0, [fp, #-0x30]
    // 0x15de8e4: r0 = Text()
    //     0x15de8e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15de8e8: mov             x2, x0
    // 0x15de8ec: ldur            x0, [fp, #-0x28]
    // 0x15de8f0: stur            x2, [fp, #-0x38]
    // 0x15de8f4: StoreField: r2->field_b = r0
    //     0x15de8f4: stur            w0, [x2, #0xb]
    // 0x15de8f8: ldur            x0, [fp, #-0x30]
    // 0x15de8fc: StoreField: r2->field_13 = r0
    //     0x15de8fc: stur            w0, [x2, #0x13]
    // 0x15de900: ldur            x0, [fp, #-8]
    // 0x15de904: LoadField: r1 = r0->field_13
    //     0x15de904: ldur            w1, [x0, #0x13]
    // 0x15de908: DecompressPointer r1
    //     0x15de908: add             x1, x1, HEAP, lsl #32
    // 0x15de90c: r0 = of()
    //     0x15de90c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15de910: LoadField: r1 = r0->field_5b
    //     0x15de910: ldur            w1, [x0, #0x5b]
    // 0x15de914: DecompressPointer r1
    //     0x15de914: add             x1, x1, HEAP, lsl #32
    // 0x15de918: stur            x1, [fp, #-0x28]
    // 0x15de91c: r0 = ColorFilter()
    //     0x15de91c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15de920: mov             x1, x0
    // 0x15de924: ldur            x0, [fp, #-0x28]
    // 0x15de928: stur            x1, [fp, #-0x30]
    // 0x15de92c: StoreField: r1->field_7 = r0
    //     0x15de92c: stur            w0, [x1, #7]
    // 0x15de930: r0 = Instance_BlendMode
    //     0x15de930: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15de934: ldr             x0, [x0, #0xb30]
    // 0x15de938: StoreField: r1->field_b = r0
    //     0x15de938: stur            w0, [x1, #0xb]
    // 0x15de93c: r0 = 1
    //     0x15de93c: movz            x0, #0x1
    // 0x15de940: StoreField: r1->field_13 = r0
    //     0x15de940: stur            x0, [x1, #0x13]
    // 0x15de944: r0 = SvgPicture()
    //     0x15de944: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15de948: stur            x0, [fp, #-0x28]
    // 0x15de94c: r16 = Instance_BoxFit
    //     0x15de94c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15de950: ldr             x16, [x16, #0xb18]
    // 0x15de954: r30 = 24.000000
    //     0x15de954: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15de958: ldr             lr, [lr, #0xba8]
    // 0x15de95c: stp             lr, x16, [SP, #0x10]
    // 0x15de960: r16 = 24.000000
    //     0x15de960: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15de964: ldr             x16, [x16, #0xba8]
    // 0x15de968: ldur            lr, [fp, #-0x30]
    // 0x15de96c: stp             lr, x16, [SP]
    // 0x15de970: mov             x1, x0
    // 0x15de974: r2 = "assets/images/shopping_bag.svg"
    //     0x15de974: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15de978: ldr             x2, [x2, #0xa60]
    // 0x15de97c: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15de97c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15de980: ldr             x4, [x4, #0xa68]
    // 0x15de984: r0 = SvgPicture.asset()
    //     0x15de984: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15de988: r0 = Badge()
    //     0x15de988: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15de98c: mov             x1, x0
    // 0x15de990: ldur            x0, [fp, #-0x18]
    // 0x15de994: stur            x1, [fp, #-0x30]
    // 0x15de998: StoreField: r1->field_b = r0
    //     0x15de998: stur            w0, [x1, #0xb]
    // 0x15de99c: ldur            x0, [fp, #-0x38]
    // 0x15de9a0: StoreField: r1->field_27 = r0
    //     0x15de9a0: stur            w0, [x1, #0x27]
    // 0x15de9a4: ldur            x0, [fp, #-0x20]
    // 0x15de9a8: StoreField: r1->field_2b = r0
    //     0x15de9a8: stur            w0, [x1, #0x2b]
    // 0x15de9ac: ldur            x0, [fp, #-0x28]
    // 0x15de9b0: StoreField: r1->field_2f = r0
    //     0x15de9b0: stur            w0, [x1, #0x2f]
    // 0x15de9b4: r0 = Visibility()
    //     0x15de9b4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15de9b8: mov             x1, x0
    // 0x15de9bc: ldur            x0, [fp, #-0x30]
    // 0x15de9c0: StoreField: r1->field_b = r0
    //     0x15de9c0: stur            w0, [x1, #0xb]
    // 0x15de9c4: r0 = Instance_SizedBox
    //     0x15de9c4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15de9c8: StoreField: r1->field_f = r0
    //     0x15de9c8: stur            w0, [x1, #0xf]
    // 0x15de9cc: ldur            x0, [fp, #-0x10]
    // 0x15de9d0: StoreField: r1->field_13 = r0
    //     0x15de9d0: stur            w0, [x1, #0x13]
    // 0x15de9d4: r0 = false
    //     0x15de9d4: add             x0, NULL, #0x30  ; false
    // 0x15de9d8: ArrayStore: r1[0] = r0  ; List_4
    //     0x15de9d8: stur            w0, [x1, #0x17]
    // 0x15de9dc: StoreField: r1->field_1b = r0
    //     0x15de9dc: stur            w0, [x1, #0x1b]
    // 0x15de9e0: StoreField: r1->field_1f = r0
    //     0x15de9e0: stur            w0, [x1, #0x1f]
    // 0x15de9e4: StoreField: r1->field_23 = r0
    //     0x15de9e4: stur            w0, [x1, #0x23]
    // 0x15de9e8: StoreField: r1->field_27 = r0
    //     0x15de9e8: stur            w0, [x1, #0x27]
    // 0x15de9ec: StoreField: r1->field_2b = r0
    //     0x15de9ec: stur            w0, [x1, #0x2b]
    // 0x15de9f0: mov             x0, x1
    // 0x15de9f4: b               #0x15dea14
    // 0x15de9f8: r0 = false
    //     0x15de9f8: add             x0, NULL, #0x30  ; false
    // 0x15de9fc: r0 = Container()
    //     0x15de9fc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15dea00: mov             x1, x0
    // 0x15dea04: stur            x0, [fp, #-0x10]
    // 0x15dea08: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15dea08: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15dea0c: r0 = Container()
    //     0x15dea0c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15dea10: ldur            x0, [fp, #-0x10]
    // 0x15dea14: stur            x0, [fp, #-0x10]
    // 0x15dea18: r0 = InkWell()
    //     0x15dea18: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15dea1c: mov             x3, x0
    // 0x15dea20: ldur            x0, [fp, #-0x10]
    // 0x15dea24: stur            x3, [fp, #-0x18]
    // 0x15dea28: StoreField: r3->field_b = r0
    //     0x15dea28: stur            w0, [x3, #0xb]
    // 0x15dea2c: ldur            x2, [fp, #-8]
    // 0x15dea30: r1 = Function '<anonymous closure>':.
    //     0x15dea30: add             x1, PP, #0x43, lsl #12  ; [pp+0x430f0] AnonymousClosure: (0x15dea9c), in [package:customer_app/app/presentation/views/cosmetic/profile/profile_view.dart] ProfileView::appBar (0x15de4ec)
    //     0x15dea34: ldr             x1, [x1, #0xf0]
    // 0x15dea38: r0 = AllocateClosure()
    //     0x15dea38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dea3c: mov             x1, x0
    // 0x15dea40: ldur            x0, [fp, #-0x18]
    // 0x15dea44: StoreField: r0->field_f = r1
    //     0x15dea44: stur            w1, [x0, #0xf]
    // 0x15dea48: r1 = true
    //     0x15dea48: add             x1, NULL, #0x20  ; true
    // 0x15dea4c: StoreField: r0->field_43 = r1
    //     0x15dea4c: stur            w1, [x0, #0x43]
    // 0x15dea50: r2 = Instance_BoxShape
    //     0x15dea50: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15dea54: ldr             x2, [x2, #0x80]
    // 0x15dea58: StoreField: r0->field_47 = r2
    //     0x15dea58: stur            w2, [x0, #0x47]
    // 0x15dea5c: StoreField: r0->field_6f = r1
    //     0x15dea5c: stur            w1, [x0, #0x6f]
    // 0x15dea60: r2 = false
    //     0x15dea60: add             x2, NULL, #0x30  ; false
    // 0x15dea64: StoreField: r0->field_73 = r2
    //     0x15dea64: stur            w2, [x0, #0x73]
    // 0x15dea68: StoreField: r0->field_83 = r1
    //     0x15dea68: stur            w1, [x0, #0x83]
    // 0x15dea6c: StoreField: r0->field_7b = r2
    //     0x15dea6c: stur            w2, [x0, #0x7b]
    // 0x15dea70: r0 = Padding()
    //     0x15dea70: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15dea74: r1 = Instance_EdgeInsets
    //     0x15dea74: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea78] Obj!EdgeInsets@d5a0e1
    //     0x15dea78: ldr             x1, [x1, #0xa78]
    // 0x15dea7c: StoreField: r0->field_f = r1
    //     0x15dea7c: stur            w1, [x0, #0xf]
    // 0x15dea80: ldur            x1, [fp, #-0x18]
    // 0x15dea84: StoreField: r0->field_b = r1
    //     0x15dea84: stur            w1, [x0, #0xb]
    // 0x15dea88: LeaveFrame
    //     0x15dea88: mov             SP, fp
    //     0x15dea8c: ldp             fp, lr, [SP], #0x10
    // 0x15dea90: ret
    //     0x15dea90: ret             
    // 0x15dea94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dea94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dea98: b               #0x15de7c8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15dea9c, size: 0xdc
    // 0x15dea9c: EnterFrame
    //     0x15dea9c: stp             fp, lr, [SP, #-0x10]!
    //     0x15deaa0: mov             fp, SP
    // 0x15deaa4: AllocStack(0x28)
    //     0x15deaa4: sub             SP, SP, #0x28
    // 0x15deaa8: SetupParameters()
    //     0x15deaa8: ldr             x0, [fp, #0x10]
    //     0x15deaac: ldur            w2, [x0, #0x17]
    //     0x15deab0: add             x2, x2, HEAP, lsl #32
    //     0x15deab4: stur            x2, [fp, #-8]
    // 0x15deab8: CheckStackOverflow
    //     0x15deab8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15deabc: cmp             SP, x16
    //     0x15deac0: b.ls            #0x15deb70
    // 0x15deac4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15deac4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15deac8: ldr             x0, [x0, #0x1c80]
    //     0x15deacc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15dead0: cmp             w0, w16
    //     0x15dead4: b.ne            #0x15deae0
    //     0x15dead8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15deadc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15deae0: r1 = Null
    //     0x15deae0: mov             x1, NULL
    // 0x15deae4: r2 = 4
    //     0x15deae4: movz            x2, #0x4
    // 0x15deae8: r0 = AllocateArray()
    //     0x15deae8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15deaec: r16 = "previousScreenSource"
    //     0x15deaec: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x15deaf0: ldr             x16, [x16, #0x448]
    // 0x15deaf4: StoreField: r0->field_f = r16
    //     0x15deaf4: stur            w16, [x0, #0xf]
    // 0x15deaf8: r16 = "profile_page"
    //     0x15deaf8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36cf8] "profile_page"
    //     0x15deafc: ldr             x16, [x16, #0xcf8]
    // 0x15deb00: StoreField: r0->field_13 = r16
    //     0x15deb00: stur            w16, [x0, #0x13]
    // 0x15deb04: r16 = <String, String>
    //     0x15deb04: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x15deb08: ldr             x16, [x16, #0x788]
    // 0x15deb0c: stp             x0, x16, [SP]
    // 0x15deb10: r0 = Map._fromLiteral()
    //     0x15deb10: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x15deb14: r16 = "/bag"
    //     0x15deb14: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x15deb18: ldr             x16, [x16, #0x468]
    // 0x15deb1c: stp             x16, NULL, [SP, #8]
    // 0x15deb20: str             x0, [SP]
    // 0x15deb24: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x15deb24: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x15deb28: ldr             x4, [x4, #0x438]
    // 0x15deb2c: r0 = GetNavigation.toNamed()
    //     0x15deb2c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15deb30: stur            x0, [fp, #-0x10]
    // 0x15deb34: cmp             w0, NULL
    // 0x15deb38: b.eq            #0x15deb60
    // 0x15deb3c: ldur            x2, [fp, #-8]
    // 0x15deb40: r1 = Function '<anonymous closure>':.
    //     0x15deb40: add             x1, PP, #0x43, lsl #12  ; [pp+0x430f8] AnonymousClosure: (0x15d6d84), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::appBar (0x15edec4)
    //     0x15deb44: ldr             x1, [x1, #0xf8]
    // 0x15deb48: r0 = AllocateClosure()
    //     0x15deb48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15deb4c: ldur            x16, [fp, #-0x10]
    // 0x15deb50: stp             x16, NULL, [SP, #8]
    // 0x15deb54: str             x0, [SP]
    // 0x15deb58: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15deb58: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15deb5c: r0 = then()
    //     0x15deb5c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15deb60: r0 = Null
    //     0x15deb60: mov             x0, NULL
    // 0x15deb64: LeaveFrame
    //     0x15deb64: mov             SP, fp
    //     0x15deb68: ldp             fp, lr, [SP], #0x10
    // 0x15deb6c: ret
    //     0x15deb6c: ret             
    // 0x15deb70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15deb70: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15deb74: b               #0x15deac4
  }
}
