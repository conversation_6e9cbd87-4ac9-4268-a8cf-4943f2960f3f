// lib: , url: package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart

// class id: 1049503, size: 0x8
class :: {
}

// class id: 3260, size: 0x34, field offset: 0x14
class _CollectionFilterState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x948a18, size: 0x610
    // 0x948a18: EnterFrame
    //     0x948a18: stp             fp, lr, [SP, #-0x10]!
    //     0x948a1c: mov             fp, SP
    // 0x948a20: AllocStack(0x50)
    //     0x948a20: sub             SP, SP, #0x50
    // 0x948a24: SetupParameters(_CollectionFilterState this /* r1 => r0, fp-0x8 */)
    //     0x948a24: mov             x0, x1
    //     0x948a28: stur            x1, [fp, #-8]
    // 0x948a2c: CheckStackOverflow
    //     0x948a2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x948a30: cmp             SP, x16
    //     0x948a34: b.ls            #0x948ff8
    // 0x948a38: LoadField: r1 = r0->field_b
    //     0x948a38: ldur            w1, [x0, #0xb]
    // 0x948a3c: DecompressPointer r1
    //     0x948a3c: add             x1, x1, HEAP, lsl #32
    // 0x948a40: cmp             w1, NULL
    // 0x948a44: b.eq            #0x949000
    // 0x948a48: LoadField: r2 = r1->field_f
    //     0x948a48: ldur            w2, [x1, #0xf]
    // 0x948a4c: DecompressPointer r2
    //     0x948a4c: add             x2, x2, HEAP, lsl #32
    // 0x948a50: LoadField: r1 = r2->field_7
    //     0x948a50: ldur            w1, [x2, #7]
    // 0x948a54: DecompressPointer r1
    //     0x948a54: add             x1, x1, HEAP, lsl #32
    // 0x948a58: cmp             w1, NULL
    // 0x948a5c: b.eq            #0x948b2c
    // 0x948a60: LoadField: r2 = r0->field_13
    //     0x948a60: ldur            w2, [x0, #0x13]
    // 0x948a64: DecompressPointer r2
    //     0x948a64: add             x2, x2, HEAP, lsl #32
    // 0x948a68: mov             x16, x1
    // 0x948a6c: mov             x1, x2
    // 0x948a70: mov             x2, x16
    // 0x948a74: r0 = addAll()
    //     0x948a74: bl              #0x715914  ; [dart:core] _GrowableList::addAll
    // 0x948a78: ldur            x0, [fp, #-8]
    // 0x948a7c: LoadField: r1 = r0->field_1b
    //     0x948a7c: ldur            w1, [x0, #0x1b]
    // 0x948a80: DecompressPointer r1
    //     0x948a80: add             x1, x1, HEAP, lsl #32
    // 0x948a84: stur            x1, [fp, #-0x10]
    // 0x948a88: LoadField: r2 = r0->field_b
    //     0x948a88: ldur            w2, [x0, #0xb]
    // 0x948a8c: DecompressPointer r2
    //     0x948a8c: add             x2, x2, HEAP, lsl #32
    // 0x948a90: cmp             w2, NULL
    // 0x948a94: b.eq            #0x949004
    // 0x948a98: LoadField: r3 = r2->field_f
    //     0x948a98: ldur            w3, [x2, #0xf]
    // 0x948a9c: DecompressPointer r3
    //     0x948a9c: add             x3, x3, HEAP, lsl #32
    // 0x948aa0: LoadField: r2 = r3->field_7
    //     0x948aa0: ldur            w2, [x3, #7]
    // 0x948aa4: DecompressPointer r2
    //     0x948aa4: add             x2, x2, HEAP, lsl #32
    // 0x948aa8: cmp             w2, NULL
    // 0x948aac: b.ne            #0x948aec
    // 0x948ab0: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x948ab0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x948ab4: ldr             x0, [x0]
    //     0x948ab8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x948abc: cmp             w0, w16
    //     0x948ac0: b.ne            #0x948acc
    //     0x948ac4: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x948ac8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x948acc: r1 = <String>
    //     0x948acc: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x948ad0: stur            x0, [fp, #-0x18]
    // 0x948ad4: r0 = AllocateGrowableArray()
    //     0x948ad4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x948ad8: mov             x1, x0
    // 0x948adc: ldur            x0, [fp, #-0x18]
    // 0x948ae0: StoreField: r1->field_f = r0
    //     0x948ae0: stur            w0, [x1, #0xf]
    // 0x948ae4: StoreField: r1->field_b = rZR
    //     0x948ae4: stur            wzr, [x1, #0xb]
    // 0x948ae8: mov             x2, x1
    // 0x948aec: ldur            x0, [fp, #-8]
    // 0x948af0: ldur            x1, [fp, #-0x10]
    // 0x948af4: r0 = addAll()
    //     0x948af4: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x948af8: ldur            x3, [fp, #-8]
    // 0x948afc: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x948afc: ldur            w1, [x3, #0x17]
    // 0x948b00: DecompressPointer r1
    //     0x948b00: add             x1, x1, HEAP, lsl #32
    // 0x948b04: LoadField: r0 = r3->field_1b
    //     0x948b04: ldur            w0, [x3, #0x1b]
    // 0x948b08: DecompressPointer r0
    //     0x948b08: add             x0, x0, HEAP, lsl #32
    // 0x948b0c: StoreField: r1->field_7 = r0
    //     0x948b0c: stur            w0, [x1, #7]
    //     0x948b10: ldurb           w16, [x1, #-1]
    //     0x948b14: ldurb           w17, [x0, #-1]
    //     0x948b18: and             x16, x17, x16, lsr #2
    //     0x948b1c: tst             x16, HEAP, lsr #32
    //     0x948b20: b.eq            #0x948b28
    //     0x948b24: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x948b28: b               #0x948b30
    // 0x948b2c: mov             x3, x0
    // 0x948b30: LoadField: r0 = r3->field_b
    //     0x948b30: ldur            w0, [x3, #0xb]
    // 0x948b34: DecompressPointer r0
    //     0x948b34: add             x0, x0, HEAP, lsl #32
    // 0x948b38: cmp             w0, NULL
    // 0x948b3c: b.eq            #0x949008
    // 0x948b40: LoadField: r1 = r0->field_f
    //     0x948b40: ldur            w1, [x0, #0xf]
    // 0x948b44: DecompressPointer r1
    //     0x948b44: add             x1, x1, HEAP, lsl #32
    // 0x948b48: LoadField: r2 = r1->field_b
    //     0x948b48: ldur            w2, [x1, #0xb]
    // 0x948b4c: DecompressPointer r2
    //     0x948b4c: add             x2, x2, HEAP, lsl #32
    // 0x948b50: cmp             w2, NULL
    // 0x948b54: b.eq            #0x948c14
    // 0x948b58: LoadField: r1 = r3->field_13
    //     0x948b58: ldur            w1, [x3, #0x13]
    // 0x948b5c: DecompressPointer r1
    //     0x948b5c: add             x1, x1, HEAP, lsl #32
    // 0x948b60: r0 = addAll()
    //     0x948b60: bl              #0x715914  ; [dart:core] _GrowableList::addAll
    // 0x948b64: ldur            x0, [fp, #-8]
    // 0x948b68: LoadField: r1 = r0->field_2b
    //     0x948b68: ldur            w1, [x0, #0x2b]
    // 0x948b6c: DecompressPointer r1
    //     0x948b6c: add             x1, x1, HEAP, lsl #32
    // 0x948b70: stur            x1, [fp, #-0x10]
    // 0x948b74: LoadField: r2 = r0->field_b
    //     0x948b74: ldur            w2, [x0, #0xb]
    // 0x948b78: DecompressPointer r2
    //     0x948b78: add             x2, x2, HEAP, lsl #32
    // 0x948b7c: cmp             w2, NULL
    // 0x948b80: b.eq            #0x94900c
    // 0x948b84: LoadField: r3 = r2->field_f
    //     0x948b84: ldur            w3, [x2, #0xf]
    // 0x948b88: DecompressPointer r3
    //     0x948b88: add             x3, x3, HEAP, lsl #32
    // 0x948b8c: LoadField: r2 = r3->field_b
    //     0x948b8c: ldur            w2, [x3, #0xb]
    // 0x948b90: DecompressPointer r2
    //     0x948b90: add             x2, x2, HEAP, lsl #32
    // 0x948b94: cmp             w2, NULL
    // 0x948b98: b.ne            #0x948bd8
    // 0x948b9c: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x948b9c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x948ba0: ldr             x0, [x0]
    //     0x948ba4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x948ba8: cmp             w0, w16
    //     0x948bac: b.ne            #0x948bb8
    //     0x948bb0: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x948bb4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x948bb8: r1 = <String>
    //     0x948bb8: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x948bbc: stur            x0, [fp, #-0x18]
    // 0x948bc0: r0 = AllocateGrowableArray()
    //     0x948bc0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x948bc4: mov             x1, x0
    // 0x948bc8: ldur            x0, [fp, #-0x18]
    // 0x948bcc: StoreField: r1->field_f = r0
    //     0x948bcc: stur            w0, [x1, #0xf]
    // 0x948bd0: StoreField: r1->field_b = rZR
    //     0x948bd0: stur            wzr, [x1, #0xb]
    // 0x948bd4: mov             x2, x1
    // 0x948bd8: ldur            x0, [fp, #-8]
    // 0x948bdc: ldur            x1, [fp, #-0x10]
    // 0x948be0: r0 = addAll()
    //     0x948be0: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x948be4: ldur            x3, [fp, #-8]
    // 0x948be8: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x948be8: ldur            w1, [x3, #0x17]
    // 0x948bec: DecompressPointer r1
    //     0x948bec: add             x1, x1, HEAP, lsl #32
    // 0x948bf0: LoadField: r0 = r3->field_2b
    //     0x948bf0: ldur            w0, [x3, #0x2b]
    // 0x948bf4: DecompressPointer r0
    //     0x948bf4: add             x0, x0, HEAP, lsl #32
    // 0x948bf8: StoreField: r1->field_f = r0
    //     0x948bf8: stur            w0, [x1, #0xf]
    //     0x948bfc: ldurb           w16, [x1, #-1]
    //     0x948c00: ldurb           w17, [x0, #-1]
    //     0x948c04: and             x16, x17, x16, lsr #2
    //     0x948c08: tst             x16, HEAP, lsr #32
    //     0x948c0c: b.eq            #0x948c14
    //     0x948c10: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x948c14: LoadField: r0 = r3->field_b
    //     0x948c14: ldur            w0, [x3, #0xb]
    // 0x948c18: DecompressPointer r0
    //     0x948c18: add             x0, x0, HEAP, lsl #32
    // 0x948c1c: cmp             w0, NULL
    // 0x948c20: b.eq            #0x949010
    // 0x948c24: LoadField: r1 = r0->field_f
    //     0x948c24: ldur            w1, [x0, #0xf]
    // 0x948c28: DecompressPointer r1
    //     0x948c28: add             x1, x1, HEAP, lsl #32
    // 0x948c2c: LoadField: r2 = r1->field_f
    //     0x948c2c: ldur            w2, [x1, #0xf]
    // 0x948c30: DecompressPointer r2
    //     0x948c30: add             x2, x2, HEAP, lsl #32
    // 0x948c34: cmp             w2, NULL
    // 0x948c38: b.eq            #0x948cfc
    // 0x948c3c: LoadField: r1 = r3->field_13
    //     0x948c3c: ldur            w1, [x3, #0x13]
    // 0x948c40: DecompressPointer r1
    //     0x948c40: add             x1, x1, HEAP, lsl #32
    // 0x948c44: r0 = addAll()
    //     0x948c44: bl              #0x715914  ; [dart:core] _GrowableList::addAll
    // 0x948c48: ldur            x0, [fp, #-8]
    // 0x948c4c: LoadField: r1 = r0->field_23
    //     0x948c4c: ldur            w1, [x0, #0x23]
    // 0x948c50: DecompressPointer r1
    //     0x948c50: add             x1, x1, HEAP, lsl #32
    // 0x948c54: stur            x1, [fp, #-0x10]
    // 0x948c58: LoadField: r2 = r0->field_b
    //     0x948c58: ldur            w2, [x0, #0xb]
    // 0x948c5c: DecompressPointer r2
    //     0x948c5c: add             x2, x2, HEAP, lsl #32
    // 0x948c60: cmp             w2, NULL
    // 0x948c64: b.eq            #0x949014
    // 0x948c68: LoadField: r3 = r2->field_f
    //     0x948c68: ldur            w3, [x2, #0xf]
    // 0x948c6c: DecompressPointer r3
    //     0x948c6c: add             x3, x3, HEAP, lsl #32
    // 0x948c70: LoadField: r2 = r3->field_f
    //     0x948c70: ldur            w2, [x3, #0xf]
    // 0x948c74: DecompressPointer r2
    //     0x948c74: add             x2, x2, HEAP, lsl #32
    // 0x948c78: cmp             w2, NULL
    // 0x948c7c: b.ne            #0x948cbc
    // 0x948c80: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x948c80: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x948c84: ldr             x0, [x0]
    //     0x948c88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x948c8c: cmp             w0, w16
    //     0x948c90: b.ne            #0x948c9c
    //     0x948c94: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x948c98: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x948c9c: r1 = <String>
    //     0x948c9c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x948ca0: stur            x0, [fp, #-0x18]
    // 0x948ca4: r0 = AllocateGrowableArray()
    //     0x948ca4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x948ca8: mov             x1, x0
    // 0x948cac: ldur            x0, [fp, #-0x18]
    // 0x948cb0: StoreField: r1->field_f = r0
    //     0x948cb0: stur            w0, [x1, #0xf]
    // 0x948cb4: StoreField: r1->field_b = rZR
    //     0x948cb4: stur            wzr, [x1, #0xb]
    // 0x948cb8: mov             x2, x1
    // 0x948cbc: ldur            x0, [fp, #-8]
    // 0x948cc0: ldur            x1, [fp, #-0x10]
    // 0x948cc4: r0 = addAll()
    //     0x948cc4: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x948cc8: ldur            x1, [fp, #-8]
    // 0x948ccc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x948ccc: ldur            w2, [x1, #0x17]
    // 0x948cd0: DecompressPointer r2
    //     0x948cd0: add             x2, x2, HEAP, lsl #32
    // 0x948cd4: LoadField: r0 = r1->field_23
    //     0x948cd4: ldur            w0, [x1, #0x23]
    // 0x948cd8: DecompressPointer r0
    //     0x948cd8: add             x0, x0, HEAP, lsl #32
    // 0x948cdc: StoreField: r2->field_b = r0
    //     0x948cdc: stur            w0, [x2, #0xb]
    //     0x948ce0: ldurb           w16, [x2, #-1]
    //     0x948ce4: ldurb           w17, [x0, #-1]
    //     0x948ce8: and             x16, x17, x16, lsr #2
    //     0x948cec: tst             x16, HEAP, lsr #32
    //     0x948cf0: b.eq            #0x948cf8
    //     0x948cf4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x948cf8: b               #0x948d00
    // 0x948cfc: mov             x1, x3
    // 0x948d00: LoadField: r0 = r1->field_b
    //     0x948d00: ldur            w0, [x1, #0xb]
    // 0x948d04: DecompressPointer r0
    //     0x948d04: add             x0, x0, HEAP, lsl #32
    // 0x948d08: cmp             w0, NULL
    // 0x948d0c: b.eq            #0x949018
    // 0x948d10: LoadField: r2 = r0->field_f
    //     0x948d10: ldur            w2, [x0, #0xf]
    // 0x948d14: DecompressPointer r2
    //     0x948d14: add             x2, x2, HEAP, lsl #32
    // 0x948d18: LoadField: r0 = r2->field_13
    //     0x948d18: ldur            w0, [x2, #0x13]
    // 0x948d1c: DecompressPointer r0
    //     0x948d1c: add             x0, x0, HEAP, lsl #32
    // 0x948d20: cmp             w0, NULL
    // 0x948d24: b.eq            #0x948fc8
    // 0x948d28: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x948d28: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x948d2c: ldr             x0, [x0]
    //     0x948d30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x948d34: cmp             w0, w16
    //     0x948d38: b.ne            #0x948d44
    //     0x948d3c: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x948d40: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x948d44: r1 = <String>
    //     0x948d44: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x948d48: stur            x0, [fp, #-0x10]
    // 0x948d4c: r0 = AllocateGrowableArray()
    //     0x948d4c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x948d50: mov             x2, x0
    // 0x948d54: ldur            x0, [fp, #-0x10]
    // 0x948d58: stur            x2, [fp, #-0x18]
    // 0x948d5c: StoreField: r2->field_f = r0
    //     0x948d5c: stur            w0, [x2, #0xf]
    // 0x948d60: StoreField: r2->field_b = rZR
    //     0x948d60: stur            wzr, [x2, #0xb]
    // 0x948d64: ldur            x3, [fp, #-8]
    // 0x948d68: LoadField: r1 = r3->field_b
    //     0x948d68: ldur            w1, [x3, #0xb]
    // 0x948d6c: DecompressPointer r1
    //     0x948d6c: add             x1, x1, HEAP, lsl #32
    // 0x948d70: cmp             w1, NULL
    // 0x948d74: b.eq            #0x94901c
    // 0x948d78: LoadField: r4 = r1->field_f
    //     0x948d78: ldur            w4, [x1, #0xf]
    // 0x948d7c: DecompressPointer r4
    //     0x948d7c: add             x4, x4, HEAP, lsl #32
    // 0x948d80: LoadField: r1 = r4->field_13
    //     0x948d80: ldur            w1, [x4, #0x13]
    // 0x948d84: DecompressPointer r1
    //     0x948d84: add             x1, x1, HEAP, lsl #32
    // 0x948d88: cmp             w1, NULL
    // 0x948d8c: b.ne            #0x948db4
    // 0x948d90: r1 = <PriceRangeFilter>
    //     0x948d90: add             x1, PP, #0xd, lsl #12  ; [pp+0xdb08] TypeArguments: <PriceRangeFilter>
    //     0x948d94: ldr             x1, [x1, #0xb08]
    // 0x948d98: r0 = AllocateGrowableArray()
    //     0x948d98: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x948d9c: mov             x1, x0
    // 0x948da0: ldur            x0, [fp, #-0x10]
    // 0x948da4: StoreField: r1->field_f = r0
    //     0x948da4: stur            w0, [x1, #0xf]
    // 0x948da8: StoreField: r1->field_b = rZR
    //     0x948da8: stur            wzr, [x1, #0xb]
    // 0x948dac: mov             x3, x1
    // 0x948db0: b               #0x948db8
    // 0x948db4: mov             x3, x1
    // 0x948db8: stur            x3, [fp, #-0x38]
    // 0x948dbc: LoadField: r4 = r3->field_7
    //     0x948dbc: ldur            w4, [x3, #7]
    // 0x948dc0: DecompressPointer r4
    //     0x948dc0: add             x4, x4, HEAP, lsl #32
    // 0x948dc4: stur            x4, [fp, #-0x30]
    // 0x948dc8: LoadField: r0 = r3->field_b
    //     0x948dc8: ldur            w0, [x3, #0xb]
    // 0x948dcc: r5 = LoadInt32Instr(r0)
    //     0x948dcc: sbfx            x5, x0, #1, #0x1f
    // 0x948dd0: stur            x5, [fp, #-0x28]
    // 0x948dd4: r0 = 0
    //     0x948dd4: movz            x0, #0
    // 0x948dd8: ldur            x7, [fp, #-8]
    // 0x948ddc: ldur            x6, [fp, #-0x18]
    // 0x948de0: CheckStackOverflow
    //     0x948de0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x948de4: cmp             SP, x16
    //     0x948de8: b.ls            #0x949020
    // 0x948dec: LoadField: r1 = r3->field_b
    //     0x948dec: ldur            w1, [x3, #0xb]
    // 0x948df0: r2 = LoadInt32Instr(r1)
    //     0x948df0: sbfx            x2, x1, #1, #0x1f
    // 0x948df4: cmp             x5, x2
    // 0x948df8: b.ne            #0x948fd8
    // 0x948dfc: cmp             x0, x2
    // 0x948e00: b.ge            #0x948f84
    // 0x948e04: LoadField: r1 = r3->field_f
    //     0x948e04: ldur            w1, [x3, #0xf]
    // 0x948e08: DecompressPointer r1
    //     0x948e08: add             x1, x1, HEAP, lsl #32
    // 0x948e0c: ArrayLoad: r8 = r1[r0]  ; Unknown_4
    //     0x948e0c: add             x16, x1, x0, lsl #2
    //     0x948e10: ldur            w8, [x16, #0xf]
    // 0x948e14: DecompressPointer r8
    //     0x948e14: add             x8, x8, HEAP, lsl #32
    // 0x948e18: stur            x8, [fp, #-0x10]
    // 0x948e1c: add             x9, x0, #1
    // 0x948e20: stur            x9, [fp, #-0x20]
    // 0x948e24: cmp             w8, NULL
    // 0x948e28: b.ne            #0x948e5c
    // 0x948e2c: mov             x0, x8
    // 0x948e30: mov             x2, x4
    // 0x948e34: r1 = Null
    //     0x948e34: mov             x1, NULL
    // 0x948e38: cmp             w2, NULL
    // 0x948e3c: b.eq            #0x948e5c
    // 0x948e40: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x948e40: ldur            w4, [x2, #0x17]
    // 0x948e44: DecompressPointer r4
    //     0x948e44: add             x4, x4, HEAP, lsl #32
    // 0x948e48: r8 = X0
    //     0x948e48: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x948e4c: LoadField: r9 = r4->field_7
    //     0x948e4c: ldur            x9, [x4, #7]
    // 0x948e50: r3 = Null
    //     0x948e50: add             x3, PP, #0x53, lsl #12  ; [pp+0x53db8] Null
    //     0x948e54: ldr             x3, [x3, #0xdb8]
    // 0x948e58: blr             x9
    // 0x948e5c: ldur            x0, [fp, #-0x10]
    // 0x948e60: LoadField: r3 = r0->field_7
    //     0x948e60: ldur            w3, [x0, #7]
    // 0x948e64: DecompressPointer r3
    //     0x948e64: add             x3, x3, HEAP, lsl #32
    // 0x948e68: stur            x3, [fp, #-0x40]
    // 0x948e6c: r1 = Null
    //     0x948e6c: mov             x1, NULL
    // 0x948e70: r2 = 6
    //     0x948e70: movz            x2, #0x6
    // 0x948e74: r0 = AllocateArray()
    //     0x948e74: bl              #0x16f7198  ; AllocateArrayStub
    // 0x948e78: mov             x1, x0
    // 0x948e7c: ldur            x0, [fp, #-0x40]
    // 0x948e80: StoreField: r1->field_f = r0
    //     0x948e80: stur            w0, [x1, #0xf]
    // 0x948e84: r16 = " - "
    //     0x948e84: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0x948e88: ldr             x16, [x16, #0xc08]
    // 0x948e8c: StoreField: r1->field_13 = r16
    //     0x948e8c: stur            w16, [x1, #0x13]
    // 0x948e90: ldur            x2, [fp, #-0x10]
    // 0x948e94: LoadField: r3 = r2->field_b
    //     0x948e94: ldur            w3, [x2, #0xb]
    // 0x948e98: DecompressPointer r3
    //     0x948e98: add             x3, x3, HEAP, lsl #32
    // 0x948e9c: cmp             w3, NULL
    // 0x948ea0: b.ne            #0x948eac
    // 0x948ea4: r3 = "above"
    //     0x948ea4: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bc10] "above"
    //     0x948ea8: ldr             x3, [x3, #0xc10]
    // 0x948eac: ldur            x0, [fp, #-0x18]
    // 0x948eb0: ArrayStore: r1[0] = r3  ; List_4
    //     0x948eb0: stur            w3, [x1, #0x17]
    // 0x948eb4: str             x1, [SP]
    // 0x948eb8: r0 = _interpolate()
    //     0x948eb8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x948ebc: mov             x2, x0
    // 0x948ec0: ldur            x0, [fp, #-0x18]
    // 0x948ec4: stur            x2, [fp, #-0x40]
    // 0x948ec8: LoadField: r1 = r0->field_b
    //     0x948ec8: ldur            w1, [x0, #0xb]
    // 0x948ecc: LoadField: r3 = r0->field_f
    //     0x948ecc: ldur            w3, [x0, #0xf]
    // 0x948ed0: DecompressPointer r3
    //     0x948ed0: add             x3, x3, HEAP, lsl #32
    // 0x948ed4: LoadField: r4 = r3->field_b
    //     0x948ed4: ldur            w4, [x3, #0xb]
    // 0x948ed8: r3 = LoadInt32Instr(r1)
    //     0x948ed8: sbfx            x3, x1, #1, #0x1f
    // 0x948edc: stur            x3, [fp, #-0x48]
    // 0x948ee0: r1 = LoadInt32Instr(r4)
    //     0x948ee0: sbfx            x1, x4, #1, #0x1f
    // 0x948ee4: cmp             x3, x1
    // 0x948ee8: b.ne            #0x948ef4
    // 0x948eec: mov             x1, x0
    // 0x948ef0: r0 = _growToNextCapacity()
    //     0x948ef0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x948ef4: ldur            x4, [fp, #-8]
    // 0x948ef8: ldur            x2, [fp, #-0x18]
    // 0x948efc: ldur            x3, [fp, #-0x48]
    // 0x948f00: add             x0, x3, #1
    // 0x948f04: lsl             x1, x0, #1
    // 0x948f08: StoreField: r2->field_b = r1
    //     0x948f08: stur            w1, [x2, #0xb]
    // 0x948f0c: LoadField: r1 = r2->field_f
    //     0x948f0c: ldur            w1, [x2, #0xf]
    // 0x948f10: DecompressPointer r1
    //     0x948f10: add             x1, x1, HEAP, lsl #32
    // 0x948f14: ldur            x0, [fp, #-0x40]
    // 0x948f18: ArrayStore: r1[r3] = r0  ; List_4
    //     0x948f18: add             x25, x1, x3, lsl #2
    //     0x948f1c: add             x25, x25, #0xf
    //     0x948f20: str             w0, [x25]
    //     0x948f24: tbz             w0, #0, #0x948f40
    //     0x948f28: ldurb           w16, [x1, #-1]
    //     0x948f2c: ldurb           w17, [x0, #-1]
    //     0x948f30: and             x16, x17, x16, lsr #2
    //     0x948f34: tst             x16, HEAP, lsr #32
    //     0x948f38: b.eq            #0x948f40
    //     0x948f3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x948f40: LoadField: r1 = r4->field_27
    //     0x948f40: ldur            w1, [x4, #0x27]
    // 0x948f44: DecompressPointer r1
    //     0x948f44: add             x1, x1, HEAP, lsl #32
    // 0x948f48: stur            x1, [fp, #-0x40]
    // 0x948f4c: ldur            x16, [fp, #-0x10]
    // 0x948f50: str             x16, [SP]
    // 0x948f54: r0 = hashCode()
    //     0x948f54: bl              #0x153c88c  ; [package:customer_app/app/data/models/collection/filter_response.dart] PriceRangeFilter::hashCode
    // 0x948f58: r3 = LoadInt32Instr(r0)
    //     0x948f58: sbfx            x3, x0, #1, #0x1f
    //     0x948f5c: tbz             w0, #0, #0x948f64
    //     0x948f60: ldur            x3, [x0, #7]
    // 0x948f64: ldur            x1, [fp, #-0x40]
    // 0x948f68: ldur            x2, [fp, #-0x10]
    // 0x948f6c: r0 = _add()
    //     0x948f6c: bl              #0x69d2b0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::_add
    // 0x948f70: ldur            x0, [fp, #-0x20]
    // 0x948f74: ldur            x3, [fp, #-0x38]
    // 0x948f78: ldur            x4, [fp, #-0x30]
    // 0x948f7c: ldur            x5, [fp, #-0x28]
    // 0x948f80: b               #0x948dd8
    // 0x948f84: mov             x1, x7
    // 0x948f88: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x948f88: ldur            w2, [x1, #0x17]
    // 0x948f8c: DecompressPointer r2
    //     0x948f8c: add             x2, x2, HEAP, lsl #32
    // 0x948f90: LoadField: r0 = r1->field_27
    //     0x948f90: ldur            w0, [x1, #0x27]
    // 0x948f94: DecompressPointer r0
    //     0x948f94: add             x0, x0, HEAP, lsl #32
    // 0x948f98: StoreField: r2->field_13 = r0
    //     0x948f98: stur            w0, [x2, #0x13]
    //     0x948f9c: ldurb           w16, [x2, #-1]
    //     0x948fa0: ldurb           w17, [x0, #-1]
    //     0x948fa4: and             x16, x17, x16, lsr #2
    //     0x948fa8: tst             x16, HEAP, lsr #32
    //     0x948fac: b.eq            #0x948fb4
    //     0x948fb0: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x948fb4: LoadField: r0 = r1->field_13
    //     0x948fb4: ldur            w0, [x1, #0x13]
    // 0x948fb8: DecompressPointer r0
    //     0x948fb8: add             x0, x0, HEAP, lsl #32
    // 0x948fbc: mov             x1, x0
    // 0x948fc0: ldur            x2, [fp, #-0x18]
    // 0x948fc4: r0 = addAll()
    //     0x948fc4: bl              #0x715914  ; [dart:core] _GrowableList::addAll
    // 0x948fc8: r0 = Null
    //     0x948fc8: mov             x0, NULL
    // 0x948fcc: LeaveFrame
    //     0x948fcc: mov             SP, fp
    //     0x948fd0: ldp             fp, lr, [SP], #0x10
    // 0x948fd4: ret
    //     0x948fd4: ret             
    // 0x948fd8: mov             x0, x3
    // 0x948fdc: r0 = ConcurrentModificationError()
    //     0x948fdc: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x948fe0: mov             x1, x0
    // 0x948fe4: ldur            x0, [fp, #-0x38]
    // 0x948fe8: StoreField: r1->field_b = r0
    //     0x948fe8: stur            w0, [x1, #0xb]
    // 0x948fec: mov             x0, x1
    // 0x948ff0: r0 = Throw()
    //     0x948ff0: bl              #0x16f5420  ; ThrowStub
    // 0x948ff4: brk             #0
    // 0x948ff8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x948ff8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x948ffc: b               #0x948a38
    // 0x949000: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x949000: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x949004: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x949004: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x949008: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x949008: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94900c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94900c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x949010: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x949010: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x949014: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x949014: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x949018: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x949018: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94901c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94901c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x949020: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x949020: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x949024: b               #0x948dec
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa2da14, size: 0x6c
    // 0xa2da14: EnterFrame
    //     0xa2da14: stp             fp, lr, [SP, #-0x10]!
    //     0xa2da18: mov             fp, SP
    // 0xa2da1c: AllocStack(0x18)
    //     0xa2da1c: sub             SP, SP, #0x18
    // 0xa2da20: SetupParameters()
    //     0xa2da20: ldr             x0, [fp, #0x10]
    //     0xa2da24: ldur            w1, [x0, #0x17]
    //     0xa2da28: add             x1, x1, HEAP, lsl #32
    // 0xa2da2c: CheckStackOverflow
    //     0xa2da2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa2da30: cmp             SP, x16
    //     0xa2da34: b.ls            #0xa2da78
    // 0xa2da38: LoadField: r0 = r1->field_13
    //     0xa2da38: ldur            w0, [x1, #0x13]
    // 0xa2da3c: DecompressPointer r0
    //     0xa2da3c: add             x0, x0, HEAP, lsl #32
    // 0xa2da40: LoadField: r2 = r1->field_f
    //     0xa2da40: ldur            w2, [x1, #0xf]
    // 0xa2da44: DecompressPointer r2
    //     0xa2da44: add             x2, x2, HEAP, lsl #32
    // 0xa2da48: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa2da48: ldur            w1, [x2, #0x17]
    // 0xa2da4c: DecompressPointer r1
    //     0xa2da4c: add             x1, x1, HEAP, lsl #32
    // 0xa2da50: r16 = <FilterResponse>
    //     0xa2da50: add             x16, PP, #0x53, lsl #12  ; [pp+0x53d60] TypeArguments: <FilterResponse>
    //     0xa2da54: ldr             x16, [x16, #0xd60]
    // 0xa2da58: stp             x0, x16, [SP, #8]
    // 0xa2da5c: str             x1, [SP]
    // 0xa2da60: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa2da60: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa2da64: r0 = pop()
    //     0xa2da64: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xa2da68: r0 = Null
    //     0xa2da68: mov             x0, NULL
    // 0xa2da6c: LeaveFrame
    //     0xa2da6c: mov             SP, fp
    //     0xa2da70: ldp             fp, lr, [SP], #0x10
    // 0xa2da74: ret
    //     0xa2da74: ret             
    // 0xa2da78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa2da78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa2da7c: b               #0xa2da38
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa2e6ec, size: 0x628
    // 0xa2e6ec: EnterFrame
    //     0xa2e6ec: stp             fp, lr, [SP, #-0x10]!
    //     0xa2e6f0: mov             fp, SP
    // 0xa2e6f4: AllocStack(0x28)
    //     0xa2e6f4: sub             SP, SP, #0x28
    // 0xa2e6f8: SetupParameters()
    //     0xa2e6f8: ldr             x0, [fp, #0x10]
    //     0xa2e6fc: ldur            w2, [x0, #0x17]
    //     0xa2e700: add             x2, x2, HEAP, lsl #32
    //     0xa2e704: stur            x2, [fp, #-0x20]
    // 0xa2e708: CheckStackOverflow
    //     0xa2e708: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa2e70c: cmp             SP, x16
    //     0xa2e710: b.ls            #0xa2ecfc
    // 0xa2e714: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xa2e714: ldur            w0, [x2, #0x17]
    // 0xa2e718: DecompressPointer r0
    //     0xa2e718: add             x0, x0, HEAP, lsl #32
    // 0xa2e71c: tbnz            w0, #4, #0xa2ea3c
    // 0xa2e720: LoadField: r0 = r2->field_f
    //     0xa2e720: ldur            w0, [x2, #0xf]
    // 0xa2e724: DecompressPointer r0
    //     0xa2e724: add             x0, x0, HEAP, lsl #32
    // 0xa2e728: LoadField: r3 = r0->field_13
    //     0xa2e728: ldur            w3, [x0, #0x13]
    // 0xa2e72c: DecompressPointer r3
    //     0xa2e72c: add             x3, x3, HEAP, lsl #32
    // 0xa2e730: stur            x3, [fp, #-0x18]
    // 0xa2e734: LoadField: r0 = r2->field_13
    //     0xa2e734: ldur            w0, [x2, #0x13]
    // 0xa2e738: DecompressPointer r0
    //     0xa2e738: add             x0, x0, HEAP, lsl #32
    // 0xa2e73c: stur            x0, [fp, #-0x10]
    // 0xa2e740: LoadField: r1 = r3->field_b
    //     0xa2e740: ldur            w1, [x3, #0xb]
    // 0xa2e744: LoadField: r4 = r3->field_f
    //     0xa2e744: ldur            w4, [x3, #0xf]
    // 0xa2e748: DecompressPointer r4
    //     0xa2e748: add             x4, x4, HEAP, lsl #32
    // 0xa2e74c: LoadField: r5 = r4->field_b
    //     0xa2e74c: ldur            w5, [x4, #0xb]
    // 0xa2e750: r4 = LoadInt32Instr(r1)
    //     0xa2e750: sbfx            x4, x1, #1, #0x1f
    // 0xa2e754: stur            x4, [fp, #-8]
    // 0xa2e758: r1 = LoadInt32Instr(r5)
    //     0xa2e758: sbfx            x1, x5, #1, #0x1f
    // 0xa2e75c: cmp             x4, x1
    // 0xa2e760: b.ne            #0xa2e76c
    // 0xa2e764: mov             x1, x3
    // 0xa2e768: r0 = _growToNextCapacity()
    //     0xa2e768: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa2e76c: ldur            x3, [fp, #-0x20]
    // 0xa2e770: ldur            x0, [fp, #-0x18]
    // 0xa2e774: ldur            x2, [fp, #-8]
    // 0xa2e778: add             x1, x2, #1
    // 0xa2e77c: lsl             x4, x1, #1
    // 0xa2e780: StoreField: r0->field_b = r4
    //     0xa2e780: stur            w4, [x0, #0xb]
    // 0xa2e784: LoadField: r1 = r0->field_f
    //     0xa2e784: ldur            w1, [x0, #0xf]
    // 0xa2e788: DecompressPointer r1
    //     0xa2e788: add             x1, x1, HEAP, lsl #32
    // 0xa2e78c: ldur            x0, [fp, #-0x10]
    // 0xa2e790: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa2e790: add             x25, x1, x2, lsl #2
    //     0xa2e794: add             x25, x25, #0xf
    //     0xa2e798: str             w0, [x25]
    //     0xa2e79c: tbz             w0, #0, #0xa2e7b8
    //     0xa2e7a0: ldurb           w16, [x1, #-1]
    //     0xa2e7a4: ldurb           w17, [x0, #-1]
    //     0xa2e7a8: and             x16, x17, x16, lsr #2
    //     0xa2e7ac: tst             x16, HEAP, lsr #32
    //     0xa2e7b0: b.eq            #0xa2e7b8
    //     0xa2e7b4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa2e7b8: LoadField: r0 = r3->field_1b
    //     0xa2e7b8: ldur            w0, [x3, #0x1b]
    // 0xa2e7bc: DecompressPointer r0
    //     0xa2e7bc: add             x0, x0, HEAP, lsl #32
    // 0xa2e7c0: LoadField: r1 = r0->field_7
    //     0xa2e7c0: ldur            x1, [x0, #7]
    // 0xa2e7c4: cmp             x1, #1
    // 0xa2e7c8: b.gt            #0xa2e898
    // 0xa2e7cc: cmp             x1, #0
    // 0xa2e7d0: b.gt            #0xa2e82c
    // 0xa2e7d4: LoadField: r0 = r3->field_f
    //     0xa2e7d4: ldur            w0, [x3, #0xf]
    // 0xa2e7d8: DecompressPointer r0
    //     0xa2e7d8: add             x0, x0, HEAP, lsl #32
    // 0xa2e7dc: LoadField: r1 = r0->field_1b
    //     0xa2e7dc: ldur            w1, [x0, #0x1b]
    // 0xa2e7e0: DecompressPointer r1
    //     0xa2e7e0: add             x1, x1, HEAP, lsl #32
    // 0xa2e7e4: LoadField: r2 = r3->field_13
    //     0xa2e7e4: ldur            w2, [x3, #0x13]
    // 0xa2e7e8: DecompressPointer r2
    //     0xa2e7e8: add             x2, x2, HEAP, lsl #32
    // 0xa2e7ec: r0 = add()
    //     0xa2e7ec: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xa2e7f0: ldur            x0, [fp, #-0x20]
    // 0xa2e7f4: LoadField: r1 = r0->field_f
    //     0xa2e7f4: ldur            w1, [x0, #0xf]
    // 0xa2e7f8: DecompressPointer r1
    //     0xa2e7f8: add             x1, x1, HEAP, lsl #32
    // 0xa2e7fc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa2e7fc: ldur            w2, [x1, #0x17]
    // 0xa2e800: DecompressPointer r2
    //     0xa2e800: add             x2, x2, HEAP, lsl #32
    // 0xa2e804: LoadField: r0 = r1->field_1b
    //     0xa2e804: ldur            w0, [x1, #0x1b]
    // 0xa2e808: DecompressPointer r0
    //     0xa2e808: add             x0, x0, HEAP, lsl #32
    // 0xa2e80c: StoreField: r2->field_7 = r0
    //     0xa2e80c: stur            w0, [x2, #7]
    //     0xa2e810: ldurb           w16, [x2, #-1]
    //     0xa2e814: ldurb           w17, [x0, #-1]
    //     0xa2e818: and             x16, x17, x16, lsr #2
    //     0xa2e81c: tst             x16, HEAP, lsr #32
    //     0xa2e820: b.eq            #0xa2e828
    //     0xa2e824: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa2e828: b               #0xa2ecec
    // 0xa2e82c: mov             x0, x3
    // 0xa2e830: LoadField: r1 = r0->field_f
    //     0xa2e830: ldur            w1, [x0, #0xf]
    // 0xa2e834: DecompressPointer r1
    //     0xa2e834: add             x1, x1, HEAP, lsl #32
    // 0xa2e838: LoadField: r2 = r1->field_2b
    //     0xa2e838: ldur            w2, [x1, #0x2b]
    // 0xa2e83c: DecompressPointer r2
    //     0xa2e83c: add             x2, x2, HEAP, lsl #32
    // 0xa2e840: LoadField: r1 = r0->field_13
    //     0xa2e840: ldur            w1, [x0, #0x13]
    // 0xa2e844: DecompressPointer r1
    //     0xa2e844: add             x1, x1, HEAP, lsl #32
    // 0xa2e848: mov             x16, x1
    // 0xa2e84c: mov             x1, x2
    // 0xa2e850: mov             x2, x16
    // 0xa2e854: r0 = add()
    //     0xa2e854: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xa2e858: ldur            x3, [fp, #-0x20]
    // 0xa2e85c: LoadField: r0 = r3->field_f
    //     0xa2e85c: ldur            w0, [x3, #0xf]
    // 0xa2e860: DecompressPointer r0
    //     0xa2e860: add             x0, x0, HEAP, lsl #32
    // 0xa2e864: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa2e864: ldur            w1, [x0, #0x17]
    // 0xa2e868: DecompressPointer r1
    //     0xa2e868: add             x1, x1, HEAP, lsl #32
    // 0xa2e86c: LoadField: r2 = r0->field_2b
    //     0xa2e86c: ldur            w2, [x0, #0x2b]
    // 0xa2e870: DecompressPointer r2
    //     0xa2e870: add             x2, x2, HEAP, lsl #32
    // 0xa2e874: mov             x0, x2
    // 0xa2e878: StoreField: r1->field_f = r0
    //     0xa2e878: stur            w0, [x1, #0xf]
    //     0xa2e87c: ldurb           w16, [x1, #-1]
    //     0xa2e880: ldurb           w17, [x0, #-1]
    //     0xa2e884: and             x16, x17, x16, lsr #2
    //     0xa2e888: tst             x16, HEAP, lsr #32
    //     0xa2e88c: b.eq            #0xa2e894
    //     0xa2e890: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xa2e894: b               #0xa2ecec
    // 0xa2e898: cmp             x1, #2
    // 0xa2e89c: b.gt            #0xa2e92c
    // 0xa2e8a0: LoadField: r1 = r3->field_f
    //     0xa2e8a0: ldur            w1, [x3, #0xf]
    // 0xa2e8a4: DecompressPointer r1
    //     0xa2e8a4: add             x1, x1, HEAP, lsl #32
    // 0xa2e8a8: LoadField: r2 = r1->field_1f
    //     0xa2e8a8: ldur            w2, [x1, #0x1f]
    // 0xa2e8ac: DecompressPointer r2
    //     0xa2e8ac: add             x2, x2, HEAP, lsl #32
    // 0xa2e8b0: LoadField: r4 = r3->field_13
    //     0xa2e8b0: ldur            w4, [x3, #0x13]
    // 0xa2e8b4: DecompressPointer r4
    //     0xa2e8b4: add             x4, x4, HEAP, lsl #32
    // 0xa2e8b8: mov             x0, x4
    // 0xa2e8bc: StoreField: r2->field_7 = r0
    //     0xa2e8bc: stur            w0, [x2, #7]
    //     0xa2e8c0: ldurb           w16, [x2, #-1]
    //     0xa2e8c4: ldurb           w17, [x0, #-1]
    //     0xa2e8c8: and             x16, x17, x16, lsr #2
    //     0xa2e8cc: tst             x16, HEAP, lsr #32
    //     0xa2e8d0: b.eq            #0xa2e8d8
    //     0xa2e8d4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa2e8d8: LoadField: r0 = r1->field_23
    //     0xa2e8d8: ldur            w0, [x1, #0x23]
    // 0xa2e8dc: DecompressPointer r0
    //     0xa2e8dc: add             x0, x0, HEAP, lsl #32
    // 0xa2e8e0: mov             x1, x0
    // 0xa2e8e4: mov             x2, x4
    // 0xa2e8e8: r0 = add()
    //     0xa2e8e8: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xa2e8ec: ldur            x3, [fp, #-0x20]
    // 0xa2e8f0: LoadField: r0 = r3->field_f
    //     0xa2e8f0: ldur            w0, [x3, #0xf]
    // 0xa2e8f4: DecompressPointer r0
    //     0xa2e8f4: add             x0, x0, HEAP, lsl #32
    // 0xa2e8f8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa2e8f8: ldur            w1, [x0, #0x17]
    // 0xa2e8fc: DecompressPointer r1
    //     0xa2e8fc: add             x1, x1, HEAP, lsl #32
    // 0xa2e900: LoadField: r2 = r0->field_23
    //     0xa2e900: ldur            w2, [x0, #0x23]
    // 0xa2e904: DecompressPointer r2
    //     0xa2e904: add             x2, x2, HEAP, lsl #32
    // 0xa2e908: mov             x0, x2
    // 0xa2e90c: StoreField: r1->field_b = r0
    //     0xa2e90c: stur            w0, [x1, #0xb]
    //     0xa2e910: ldurb           w16, [x1, #-1]
    //     0xa2e914: ldurb           w17, [x0, #-1]
    //     0xa2e918: and             x16, x17, x16, lsr #2
    //     0xa2e91c: tst             x16, HEAP, lsr #32
    //     0xa2e920: b.eq            #0xa2e928
    //     0xa2e924: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xa2e928: b               #0xa2ecec
    // 0xa2e92c: LoadField: r1 = r3->field_13
    //     0xa2e92c: ldur            w1, [x3, #0x13]
    // 0xa2e930: DecompressPointer r1
    //     0xa2e930: add             x1, x1, HEAP, lsl #32
    // 0xa2e934: r0 = LoadClassIdInstr(r1)
    //     0xa2e934: ldur            x0, [x1, #-1]
    //     0xa2e938: ubfx            x0, x0, #0xc, #0x14
    // 0xa2e93c: r2 = " - "
    //     0xa2e93c: add             x2, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0xa2e940: ldr             x2, [x2, #0xc08]
    // 0xa2e944: r0 = GDT[cid_x0 + -0xffc]()
    //     0xa2e944: sub             lr, x0, #0xffc
    //     0xa2e948: ldr             lr, [x21, lr, lsl #3]
    //     0xa2e94c: blr             lr
    // 0xa2e950: mov             x2, x0
    // 0xa2e954: stur            x2, [fp, #-0x10]
    // 0xa2e958: LoadField: r0 = r2->field_b
    //     0xa2e958: ldur            w0, [x2, #0xb]
    // 0xa2e95c: r1 = LoadInt32Instr(r0)
    //     0xa2e95c: sbfx            x1, x0, #1, #0x1f
    // 0xa2e960: mov             x0, x1
    // 0xa2e964: r1 = 0
    //     0xa2e964: movz            x1, #0
    // 0xa2e968: cmp             x1, x0
    // 0xa2e96c: b.hs            #0xa2ed04
    // 0xa2e970: LoadField: r0 = r2->field_f
    //     0xa2e970: ldur            w0, [x2, #0xf]
    // 0xa2e974: DecompressPointer r0
    //     0xa2e974: add             x0, x0, HEAP, lsl #32
    // 0xa2e978: LoadField: r1 = r0->field_f
    //     0xa2e978: ldur            w1, [x0, #0xf]
    // 0xa2e97c: DecompressPointer r1
    //     0xa2e97c: add             x1, x1, HEAP, lsl #32
    // 0xa2e980: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa2e980: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa2e984: r0 = tryParse()
    //     0xa2e984: bl              #0x62d820  ; [dart:core] int::tryParse
    // 0xa2e988: mov             x3, x0
    // 0xa2e98c: ldur            x2, [fp, #-0x10]
    // 0xa2e990: stur            x3, [fp, #-0x18]
    // 0xa2e994: LoadField: r0 = r2->field_b
    //     0xa2e994: ldur            w0, [x2, #0xb]
    // 0xa2e998: r1 = LoadInt32Instr(r0)
    //     0xa2e998: sbfx            x1, x0, #1, #0x1f
    // 0xa2e99c: mov             x0, x1
    // 0xa2e9a0: r1 = 1
    //     0xa2e9a0: movz            x1, #0x1
    // 0xa2e9a4: cmp             x1, x0
    // 0xa2e9a8: b.hs            #0xa2ed08
    // 0xa2e9ac: LoadField: r0 = r2->field_f
    //     0xa2e9ac: ldur            w0, [x2, #0xf]
    // 0xa2e9b0: DecompressPointer r0
    //     0xa2e9b0: add             x0, x0, HEAP, lsl #32
    // 0xa2e9b4: LoadField: r1 = r0->field_13
    //     0xa2e9b4: ldur            w1, [x0, #0x13]
    // 0xa2e9b8: DecompressPointer r1
    //     0xa2e9b8: add             x1, x1, HEAP, lsl #32
    // 0xa2e9bc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa2e9bc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa2e9c0: r0 = tryParse()
    //     0xa2e9c0: bl              #0x62d820  ; [dart:core] int::tryParse
    // 0xa2e9c4: stur            x0, [fp, #-0x10]
    // 0xa2e9c8: r0 = PriceRangeFilter()
    //     0xa2e9c8: bl              #0x91d7f0  ; AllocatePriceRangeFilterStub -> PriceRangeFilter (size=0x10)
    // 0xa2e9cc: mov             x1, x0
    // 0xa2e9d0: ldur            x0, [fp, #-0x18]
    // 0xa2e9d4: StoreField: r1->field_7 = r0
    //     0xa2e9d4: stur            w0, [x1, #7]
    // 0xa2e9d8: ldur            x0, [fp, #-0x10]
    // 0xa2e9dc: StoreField: r1->field_b = r0
    //     0xa2e9dc: stur            w0, [x1, #0xb]
    // 0xa2e9e0: ldur            x0, [fp, #-0x20]
    // 0xa2e9e4: LoadField: r2 = r0->field_f
    //     0xa2e9e4: ldur            w2, [x0, #0xf]
    // 0xa2e9e8: DecompressPointer r2
    //     0xa2e9e8: add             x2, x2, HEAP, lsl #32
    // 0xa2e9ec: LoadField: r3 = r2->field_27
    //     0xa2e9ec: ldur            w3, [x2, #0x27]
    // 0xa2e9f0: DecompressPointer r3
    //     0xa2e9f0: add             x3, x3, HEAP, lsl #32
    // 0xa2e9f4: mov             x2, x1
    // 0xa2e9f8: mov             x1, x3
    // 0xa2e9fc: r0 = add()
    //     0xa2e9fc: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xa2ea00: ldur            x0, [fp, #-0x20]
    // 0xa2ea04: LoadField: r1 = r0->field_f
    //     0xa2ea04: ldur            w1, [x0, #0xf]
    // 0xa2ea08: DecompressPointer r1
    //     0xa2ea08: add             x1, x1, HEAP, lsl #32
    // 0xa2ea0c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa2ea0c: ldur            w2, [x1, #0x17]
    // 0xa2ea10: DecompressPointer r2
    //     0xa2ea10: add             x2, x2, HEAP, lsl #32
    // 0xa2ea14: LoadField: r0 = r1->field_27
    //     0xa2ea14: ldur            w0, [x1, #0x27]
    // 0xa2ea18: DecompressPointer r0
    //     0xa2ea18: add             x0, x0, HEAP, lsl #32
    // 0xa2ea1c: StoreField: r2->field_13 = r0
    //     0xa2ea1c: stur            w0, [x2, #0x13]
    //     0xa2ea20: ldurb           w16, [x2, #-1]
    //     0xa2ea24: ldurb           w17, [x0, #-1]
    //     0xa2ea28: and             x16, x17, x16, lsr #2
    //     0xa2ea2c: tst             x16, HEAP, lsr #32
    //     0xa2ea30: b.eq            #0xa2ea38
    //     0xa2ea34: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa2ea38: b               #0xa2ecec
    // 0xa2ea3c: mov             x0, x2
    // 0xa2ea40: LoadField: r1 = r0->field_1b
    //     0xa2ea40: ldur            w1, [x0, #0x1b]
    // 0xa2ea44: DecompressPointer r1
    //     0xa2ea44: add             x1, x1, HEAP, lsl #32
    // 0xa2ea48: LoadField: r2 = r1->field_7
    //     0xa2ea48: ldur            x2, [x1, #7]
    // 0xa2ea4c: cmp             x2, #1
    // 0xa2ea50: b.gt            #0xa2eb2c
    // 0xa2ea54: cmp             x2, #0
    // 0xa2ea58: b.gt            #0xa2eac8
    // 0xa2ea5c: LoadField: r1 = r0->field_f
    //     0xa2ea5c: ldur            w1, [x0, #0xf]
    // 0xa2ea60: DecompressPointer r1
    //     0xa2ea60: add             x1, x1, HEAP, lsl #32
    // 0xa2ea64: LoadField: r2 = r1->field_1b
    //     0xa2ea64: ldur            w2, [x1, #0x1b]
    // 0xa2ea68: DecompressPointer r2
    //     0xa2ea68: add             x2, x2, HEAP, lsl #32
    // 0xa2ea6c: LoadField: r1 = r0->field_13
    //     0xa2ea6c: ldur            w1, [x0, #0x13]
    // 0xa2ea70: DecompressPointer r1
    //     0xa2ea70: add             x1, x1, HEAP, lsl #32
    // 0xa2ea74: mov             x16, x1
    // 0xa2ea78: mov             x1, x2
    // 0xa2ea7c: mov             x2, x16
    // 0xa2ea80: r0 = remove()
    //     0xa2ea80: bl              #0x16981d8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0xa2ea84: ldur            x3, [fp, #-0x20]
    // 0xa2ea88: LoadField: r1 = r3->field_f
    //     0xa2ea88: ldur            w1, [x3, #0xf]
    // 0xa2ea8c: DecompressPointer r1
    //     0xa2ea8c: add             x1, x1, HEAP, lsl #32
    // 0xa2ea90: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa2ea90: ldur            w2, [x1, #0x17]
    // 0xa2ea94: DecompressPointer r2
    //     0xa2ea94: add             x2, x2, HEAP, lsl #32
    // 0xa2ea98: LoadField: r0 = r1->field_1b
    //     0xa2ea98: ldur            w0, [x1, #0x1b]
    // 0xa2ea9c: DecompressPointer r0
    //     0xa2ea9c: add             x0, x0, HEAP, lsl #32
    // 0xa2eaa0: StoreField: r2->field_7 = r0
    //     0xa2eaa0: stur            w0, [x2, #7]
    //     0xa2eaa4: ldurb           w16, [x2, #-1]
    //     0xa2eaa8: ldurb           w17, [x0, #-1]
    //     0xa2eaac: and             x16, x17, x16, lsr #2
    //     0xa2eab0: tst             x16, HEAP, lsr #32
    //     0xa2eab4: b.eq            #0xa2eabc
    //     0xa2eab8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa2eabc: mov             x0, x1
    // 0xa2eac0: mov             x1, x3
    // 0xa2eac4: b               #0xa2ecd0
    // 0xa2eac8: mov             x3, x0
    // 0xa2eacc: LoadField: r0 = r3->field_f
    //     0xa2eacc: ldur            w0, [x3, #0xf]
    // 0xa2ead0: DecompressPointer r0
    //     0xa2ead0: add             x0, x0, HEAP, lsl #32
    // 0xa2ead4: LoadField: r1 = r0->field_2b
    //     0xa2ead4: ldur            w1, [x0, #0x2b]
    // 0xa2ead8: DecompressPointer r1
    //     0xa2ead8: add             x1, x1, HEAP, lsl #32
    // 0xa2eadc: LoadField: r2 = r3->field_13
    //     0xa2eadc: ldur            w2, [x3, #0x13]
    // 0xa2eae0: DecompressPointer r2
    //     0xa2eae0: add             x2, x2, HEAP, lsl #32
    // 0xa2eae4: r0 = remove()
    //     0xa2eae4: bl              #0x16981d8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0xa2eae8: ldur            x3, [fp, #-0x20]
    // 0xa2eaec: LoadField: r1 = r3->field_f
    //     0xa2eaec: ldur            w1, [x3, #0xf]
    // 0xa2eaf0: DecompressPointer r1
    //     0xa2eaf0: add             x1, x1, HEAP, lsl #32
    // 0xa2eaf4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa2eaf4: ldur            w2, [x1, #0x17]
    // 0xa2eaf8: DecompressPointer r2
    //     0xa2eaf8: add             x2, x2, HEAP, lsl #32
    // 0xa2eafc: LoadField: r0 = r1->field_2b
    //     0xa2eafc: ldur            w0, [x1, #0x2b]
    // 0xa2eb00: DecompressPointer r0
    //     0xa2eb00: add             x0, x0, HEAP, lsl #32
    // 0xa2eb04: StoreField: r2->field_f = r0
    //     0xa2eb04: stur            w0, [x2, #0xf]
    //     0xa2eb08: ldurb           w16, [x2, #-1]
    //     0xa2eb0c: ldurb           w17, [x0, #-1]
    //     0xa2eb10: and             x16, x17, x16, lsr #2
    //     0xa2eb14: tst             x16, HEAP, lsr #32
    //     0xa2eb18: b.eq            #0xa2eb20
    //     0xa2eb1c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa2eb20: mov             x0, x1
    // 0xa2eb24: mov             x1, x3
    // 0xa2eb28: b               #0xa2ecd0
    // 0xa2eb2c: mov             x3, x0
    // 0xa2eb30: cmp             x2, #2
    // 0xa2eb34: b.gt            #0xa2eb98
    // 0xa2eb38: LoadField: r0 = r3->field_f
    //     0xa2eb38: ldur            w0, [x3, #0xf]
    // 0xa2eb3c: DecompressPointer r0
    //     0xa2eb3c: add             x0, x0, HEAP, lsl #32
    // 0xa2eb40: LoadField: r1 = r0->field_23
    //     0xa2eb40: ldur            w1, [x0, #0x23]
    // 0xa2eb44: DecompressPointer r1
    //     0xa2eb44: add             x1, x1, HEAP, lsl #32
    // 0xa2eb48: LoadField: r2 = r3->field_13
    //     0xa2eb48: ldur            w2, [x3, #0x13]
    // 0xa2eb4c: DecompressPointer r2
    //     0xa2eb4c: add             x2, x2, HEAP, lsl #32
    // 0xa2eb50: r0 = remove()
    //     0xa2eb50: bl              #0x16981d8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0xa2eb54: ldur            x3, [fp, #-0x20]
    // 0xa2eb58: LoadField: r1 = r3->field_f
    //     0xa2eb58: ldur            w1, [x3, #0xf]
    // 0xa2eb5c: DecompressPointer r1
    //     0xa2eb5c: add             x1, x1, HEAP, lsl #32
    // 0xa2eb60: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa2eb60: ldur            w2, [x1, #0x17]
    // 0xa2eb64: DecompressPointer r2
    //     0xa2eb64: add             x2, x2, HEAP, lsl #32
    // 0xa2eb68: LoadField: r0 = r1->field_23
    //     0xa2eb68: ldur            w0, [x1, #0x23]
    // 0xa2eb6c: DecompressPointer r0
    //     0xa2eb6c: add             x0, x0, HEAP, lsl #32
    // 0xa2eb70: StoreField: r2->field_b = r0
    //     0xa2eb70: stur            w0, [x2, #0xb]
    //     0xa2eb74: ldurb           w16, [x2, #-1]
    //     0xa2eb78: ldurb           w17, [x0, #-1]
    //     0xa2eb7c: and             x16, x17, x16, lsr #2
    //     0xa2eb80: tst             x16, HEAP, lsr #32
    //     0xa2eb84: b.eq            #0xa2eb8c
    //     0xa2eb88: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa2eb8c: mov             x0, x1
    // 0xa2eb90: mov             x1, x3
    // 0xa2eb94: b               #0xa2ecd0
    // 0xa2eb98: LoadField: r1 = r3->field_13
    //     0xa2eb98: ldur            w1, [x3, #0x13]
    // 0xa2eb9c: DecompressPointer r1
    //     0xa2eb9c: add             x1, x1, HEAP, lsl #32
    // 0xa2eba0: r0 = LoadClassIdInstr(r1)
    //     0xa2eba0: ldur            x0, [x1, #-1]
    //     0xa2eba4: ubfx            x0, x0, #0xc, #0x14
    // 0xa2eba8: r2 = " - "
    //     0xa2eba8: add             x2, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0xa2ebac: ldr             x2, [x2, #0xc08]
    // 0xa2ebb0: r0 = GDT[cid_x0 + -0xffc]()
    //     0xa2ebb0: sub             lr, x0, #0xffc
    //     0xa2ebb4: ldr             lr, [x21, lr, lsl #3]
    //     0xa2ebb8: blr             lr
    // 0xa2ebbc: mov             x2, x0
    // 0xa2ebc0: stur            x2, [fp, #-0x10]
    // 0xa2ebc4: LoadField: r0 = r2->field_b
    //     0xa2ebc4: ldur            w0, [x2, #0xb]
    // 0xa2ebc8: r1 = LoadInt32Instr(r0)
    //     0xa2ebc8: sbfx            x1, x0, #1, #0x1f
    // 0xa2ebcc: mov             x0, x1
    // 0xa2ebd0: r1 = 0
    //     0xa2ebd0: movz            x1, #0
    // 0xa2ebd4: cmp             x1, x0
    // 0xa2ebd8: b.hs            #0xa2ed0c
    // 0xa2ebdc: LoadField: r0 = r2->field_f
    //     0xa2ebdc: ldur            w0, [x2, #0xf]
    // 0xa2ebe0: DecompressPointer r0
    //     0xa2ebe0: add             x0, x0, HEAP, lsl #32
    // 0xa2ebe4: LoadField: r1 = r0->field_f
    //     0xa2ebe4: ldur            w1, [x0, #0xf]
    // 0xa2ebe8: DecompressPointer r1
    //     0xa2ebe8: add             x1, x1, HEAP, lsl #32
    // 0xa2ebec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa2ebec: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa2ebf0: r0 = tryParse()
    //     0xa2ebf0: bl              #0x62d820  ; [dart:core] int::tryParse
    // 0xa2ebf4: mov             x3, x0
    // 0xa2ebf8: ldur            x2, [fp, #-0x10]
    // 0xa2ebfc: stur            x3, [fp, #-0x18]
    // 0xa2ec00: LoadField: r0 = r2->field_b
    //     0xa2ec00: ldur            w0, [x2, #0xb]
    // 0xa2ec04: r1 = LoadInt32Instr(r0)
    //     0xa2ec04: sbfx            x1, x0, #1, #0x1f
    // 0xa2ec08: mov             x0, x1
    // 0xa2ec0c: r1 = 1
    //     0xa2ec0c: movz            x1, #0x1
    // 0xa2ec10: cmp             x1, x0
    // 0xa2ec14: b.hs            #0xa2ed10
    // 0xa2ec18: LoadField: r0 = r2->field_f
    //     0xa2ec18: ldur            w0, [x2, #0xf]
    // 0xa2ec1c: DecompressPointer r0
    //     0xa2ec1c: add             x0, x0, HEAP, lsl #32
    // 0xa2ec20: LoadField: r1 = r0->field_13
    //     0xa2ec20: ldur            w1, [x0, #0x13]
    // 0xa2ec24: DecompressPointer r1
    //     0xa2ec24: add             x1, x1, HEAP, lsl #32
    // 0xa2ec28: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa2ec28: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa2ec2c: r0 = tryParse()
    //     0xa2ec2c: bl              #0x62d820  ; [dart:core] int::tryParse
    // 0xa2ec30: stur            x0, [fp, #-0x10]
    // 0xa2ec34: r0 = PriceRangeFilter()
    //     0xa2ec34: bl              #0x91d7f0  ; AllocatePriceRangeFilterStub -> PriceRangeFilter (size=0x10)
    // 0xa2ec38: mov             x3, x0
    // 0xa2ec3c: ldur            x0, [fp, #-0x18]
    // 0xa2ec40: stur            x3, [fp, #-0x28]
    // 0xa2ec44: StoreField: r3->field_7 = r0
    //     0xa2ec44: stur            w0, [x3, #7]
    // 0xa2ec48: ldur            x0, [fp, #-0x10]
    // 0xa2ec4c: StoreField: r3->field_b = r0
    //     0xa2ec4c: stur            w0, [x3, #0xb]
    // 0xa2ec50: ldur            x0, [fp, #-0x20]
    // 0xa2ec54: LoadField: r1 = r0->field_f
    //     0xa2ec54: ldur            w1, [x0, #0xf]
    // 0xa2ec58: DecompressPointer r1
    //     0xa2ec58: add             x1, x1, HEAP, lsl #32
    // 0xa2ec5c: LoadField: r2 = r1->field_27
    //     0xa2ec5c: ldur            w2, [x1, #0x27]
    // 0xa2ec60: DecompressPointer r2
    //     0xa2ec60: add             x2, x2, HEAP, lsl #32
    // 0xa2ec64: mov             x1, x2
    // 0xa2ec68: mov             x2, x3
    // 0xa2ec6c: r0 = contains()
    //     0xa2ec6c: bl              #0x7deb98  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::contains
    // 0xa2ec70: tbnz            w0, #4, #0xa2ec94
    // 0xa2ec74: ldur            x0, [fp, #-0x20]
    // 0xa2ec78: LoadField: r1 = r0->field_f
    //     0xa2ec78: ldur            w1, [x0, #0xf]
    // 0xa2ec7c: DecompressPointer r1
    //     0xa2ec7c: add             x1, x1, HEAP, lsl #32
    // 0xa2ec80: LoadField: r2 = r1->field_27
    //     0xa2ec80: ldur            w2, [x1, #0x27]
    // 0xa2ec84: DecompressPointer r2
    //     0xa2ec84: add             x2, x2, HEAP, lsl #32
    // 0xa2ec88: mov             x1, x2
    // 0xa2ec8c: ldur            x2, [fp, #-0x28]
    // 0xa2ec90: r0 = remove()
    //     0xa2ec90: bl              #0x16981d8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0xa2ec94: ldur            x1, [fp, #-0x20]
    // 0xa2ec98: LoadField: r2 = r1->field_f
    //     0xa2ec98: ldur            w2, [x1, #0xf]
    // 0xa2ec9c: DecompressPointer r2
    //     0xa2ec9c: add             x2, x2, HEAP, lsl #32
    // 0xa2eca0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa2eca0: ldur            w3, [x2, #0x17]
    // 0xa2eca4: DecompressPointer r3
    //     0xa2eca4: add             x3, x3, HEAP, lsl #32
    // 0xa2eca8: LoadField: r0 = r2->field_27
    //     0xa2eca8: ldur            w0, [x2, #0x27]
    // 0xa2ecac: DecompressPointer r0
    //     0xa2ecac: add             x0, x0, HEAP, lsl #32
    // 0xa2ecb0: StoreField: r3->field_13 = r0
    //     0xa2ecb0: stur            w0, [x3, #0x13]
    //     0xa2ecb4: ldurb           w16, [x3, #-1]
    //     0xa2ecb8: ldurb           w17, [x0, #-1]
    //     0xa2ecbc: and             x16, x17, x16, lsr #2
    //     0xa2ecc0: tst             x16, HEAP, lsr #32
    //     0xa2ecc4: b.eq            #0xa2eccc
    //     0xa2ecc8: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xa2eccc: mov             x0, x2
    // 0xa2ecd0: LoadField: r2 = r0->field_13
    //     0xa2ecd0: ldur            w2, [x0, #0x13]
    // 0xa2ecd4: DecompressPointer r2
    //     0xa2ecd4: add             x2, x2, HEAP, lsl #32
    // 0xa2ecd8: LoadField: r0 = r1->field_13
    //     0xa2ecd8: ldur            w0, [x1, #0x13]
    // 0xa2ecdc: DecompressPointer r0
    //     0xa2ecdc: add             x0, x0, HEAP, lsl #32
    // 0xa2ece0: mov             x1, x2
    // 0xa2ece4: mov             x2, x0
    // 0xa2ece8: r0 = remove()
    //     0xa2ece8: bl              #0x71df18  ; [dart:core] _GrowableList::remove
    // 0xa2ecec: r0 = Null
    //     0xa2ecec: mov             x0, NULL
    // 0xa2ecf0: LeaveFrame
    //     0xa2ecf0: mov             SP, fp
    //     0xa2ecf4: ldp             fp, lr, [SP], #0x10
    // 0xa2ecf8: ret
    //     0xa2ecf8: ret             
    // 0xa2ecfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa2ecfc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa2ed00: b               #0xa2e714
    // 0xa2ed04: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa2ed04: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa2ed08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa2ed08: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa2ed0c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa2ed0c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa2ed10: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa2ed10: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _onItemCheckedChange(/* No info */) {
    // ** addr: 0xa2ed14, size: 0x88
    // 0xa2ed14: EnterFrame
    //     0xa2ed14: stp             fp, lr, [SP, #-0x10]!
    //     0xa2ed18: mov             fp, SP
    // 0xa2ed1c: AllocStack(0x20)
    //     0xa2ed1c: sub             SP, SP, #0x20
    // 0xa2ed20: SetupParameters(_CollectionFilterState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xa2ed20: stur            x1, [fp, #-8]
    //     0xa2ed24: stur            x2, [fp, #-0x10]
    //     0xa2ed28: stur            x3, [fp, #-0x18]
    //     0xa2ed2c: stur            x5, [fp, #-0x20]
    // 0xa2ed30: CheckStackOverflow
    //     0xa2ed30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa2ed34: cmp             SP, x16
    //     0xa2ed38: b.ls            #0xa2ed94
    // 0xa2ed3c: r1 = 4
    //     0xa2ed3c: movz            x1, #0x4
    // 0xa2ed40: r0 = AllocateContext()
    //     0xa2ed40: bl              #0x16f6108  ; AllocateContextStub
    // 0xa2ed44: mov             x1, x0
    // 0xa2ed48: ldur            x0, [fp, #-8]
    // 0xa2ed4c: StoreField: r1->field_f = r0
    //     0xa2ed4c: stur            w0, [x1, #0xf]
    // 0xa2ed50: ldur            x2, [fp, #-0x10]
    // 0xa2ed54: StoreField: r1->field_13 = r2
    //     0xa2ed54: stur            w2, [x1, #0x13]
    // 0xa2ed58: ldur            x2, [fp, #-0x18]
    // 0xa2ed5c: ArrayStore: r1[0] = r2  ; List_4
    //     0xa2ed5c: stur            w2, [x1, #0x17]
    // 0xa2ed60: ldur            x2, [fp, #-0x20]
    // 0xa2ed64: StoreField: r1->field_1b = r2
    //     0xa2ed64: stur            w2, [x1, #0x1b]
    // 0xa2ed68: mov             x2, x1
    // 0xa2ed6c: r1 = Function '<anonymous closure>':.
    //     0xa2ed6c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53d88] AnonymousClosure: (0xa2e6ec), in [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::_onItemCheckedChange (0xa2ed14)
    //     0xa2ed70: ldr             x1, [x1, #0xd88]
    // 0xa2ed74: r0 = AllocateClosure()
    //     0xa2ed74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa2ed78: ldur            x1, [fp, #-8]
    // 0xa2ed7c: mov             x2, x0
    // 0xa2ed80: r0 = setState()
    //     0xa2ed80: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa2ed84: r0 = Null
    //     0xa2ed84: mov             x0, NULL
    // 0xa2ed88: LeaveFrame
    //     0xa2ed88: mov             SP, fp
    //     0xa2ed8c: ldp             fp, lr, [SP], #0x10
    // 0xa2ed90: ret
    //     0xa2ed90: ret             
    // 0xa2ed94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa2ed94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa2ed98: b               #0xa2ed3c
  }
  _ build(/* No info */) {
    // ** addr: 0xbd4660, size: 0x1198
    // 0xbd4660: EnterFrame
    //     0xbd4660: stp             fp, lr, [SP, #-0x10]!
    //     0xbd4664: mov             fp, SP
    // 0xbd4668: AllocStack(0x68)
    //     0xbd4668: sub             SP, SP, #0x68
    // 0xbd466c: SetupParameters(_CollectionFilterState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbd466c: stur            x1, [fp, #-8]
    //     0xbd4670: stur            x2, [fp, #-0x10]
    // 0xbd4674: CheckStackOverflow
    //     0xbd4674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd4678: cmp             SP, x16
    //     0xbd467c: b.ls            #0xbd57c8
    // 0xbd4680: r1 = 2
    //     0xbd4680: movz            x1, #0x2
    // 0xbd4684: r0 = AllocateContext()
    //     0xbd4684: bl              #0x16f6108  ; AllocateContextStub
    // 0xbd4688: mov             x1, x0
    // 0xbd468c: ldur            x0, [fp, #-8]
    // 0xbd4690: stur            x1, [fp, #-0x30]
    // 0xbd4694: StoreField: r1->field_f = r0
    //     0xbd4694: stur            w0, [x1, #0xf]
    // 0xbd4698: ldur            x2, [fp, #-0x10]
    // 0xbd469c: StoreField: r1->field_13 = r2
    //     0xbd469c: stur            w2, [x1, #0x13]
    // 0xbd46a0: LoadField: r2 = r0->field_2f
    //     0xbd46a0: ldur            w2, [x0, #0x2f]
    // 0xbd46a4: DecompressPointer r2
    //     0xbd46a4: add             x2, x2, HEAP, lsl #32
    // 0xbd46a8: tbnz            w2, #4, #0xbd46b4
    // 0xbd46ac: r3 = Instance_Brightness
    //     0xbd46ac: ldr             x3, [PP, #0x5468]  ; [pp+0x5468] Obj!Brightness@d76341
    // 0xbd46b0: b               #0xbd46b8
    // 0xbd46b4: r3 = Instance_Brightness
    //     0xbd46b4: ldr             x3, [PP, #0x5470]  ; [pp+0x5470] Obj!Brightness@d76361
    // 0xbd46b8: stur            x3, [fp, #-0x28]
    // 0xbd46bc: tbnz            w2, #4, #0xbd46c8
    // 0xbd46c0: r4 = Instance_Brightness
    //     0xbd46c0: ldr             x4, [PP, #0x5470]  ; [pp+0x5470] Obj!Brightness@d76361
    // 0xbd46c4: b               #0xbd46cc
    // 0xbd46c8: r4 = Instance_Brightness
    //     0xbd46c8: ldr             x4, [PP, #0x5468]  ; [pp+0x5468] Obj!Brightness@d76341
    // 0xbd46cc: stur            x4, [fp, #-0x20]
    // 0xbd46d0: tbnz            w2, #4, #0xbd46dc
    // 0xbd46d4: r5 = Instance_Color
    //     0xbd46d4: ldr             x5, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd46d8: b               #0xbd46e0
    // 0xbd46dc: r5 = Instance_Color
    //     0xbd46dc: ldr             x5, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbd46e0: stur            x5, [fp, #-0x18]
    // 0xbd46e4: tbnz            w2, #4, #0xbd46f0
    // 0xbd46e8: r2 = Instance_Brightness
    //     0xbd46e8: ldr             x2, [PP, #0x5468]  ; [pp+0x5468] Obj!Brightness@d76341
    // 0xbd46ec: b               #0xbd46f4
    // 0xbd46f0: r2 = Instance_Brightness
    //     0xbd46f0: ldr             x2, [PP, #0x5470]  ; [pp+0x5470] Obj!Brightness@d76361
    // 0xbd46f4: stur            x2, [fp, #-0x10]
    // 0xbd46f8: r0 = SystemUiOverlayStyle()
    //     0xbd46f8: bl              #0x6e633c  ; AllocateSystemUiOverlayStyleStub -> SystemUiOverlayStyle (size=0x28)
    // 0xbd46fc: mov             x1, x0
    // 0xbd4700: ldur            x0, [fp, #-0x18]
    // 0xbd4704: stur            x1, [fp, #-0x38]
    // 0xbd4708: StoreField: r1->field_7 = r0
    //     0xbd4708: stur            w0, [x1, #7]
    // 0xbd470c: r0 = Instance_Color
    //     0xbd470c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xbd4710: ldr             x0, [x0, #0xf88]
    // 0xbd4714: StoreField: r1->field_b = r0
    //     0xbd4714: stur            w0, [x1, #0xb]
    // 0xbd4718: ldur            x2, [fp, #-0x10]
    // 0xbd471c: StoreField: r1->field_f = r2
    //     0xbd471c: stur            w2, [x1, #0xf]
    // 0xbd4720: r2 = false
    //     0xbd4720: add             x2, NULL, #0x30  ; false
    // 0xbd4724: StoreField: r1->field_13 = r2
    //     0xbd4724: stur            w2, [x1, #0x13]
    // 0xbd4728: ArrayStore: r1[0] = r0  ; List_4
    //     0xbd4728: stur            w0, [x1, #0x17]
    // 0xbd472c: ldur            x0, [fp, #-0x20]
    // 0xbd4730: StoreField: r1->field_1b = r0
    //     0xbd4730: stur            w0, [x1, #0x1b]
    // 0xbd4734: ldur            x0, [fp, #-0x28]
    // 0xbd4738: StoreField: r1->field_1f = r0
    //     0xbd4738: stur            w0, [x1, #0x1f]
    // 0xbd473c: StoreField: r1->field_23 = r2
    //     0xbd473c: stur            w2, [x1, #0x23]
    // 0xbd4740: r0 = SvgPicture()
    //     0xbd4740: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbd4744: stur            x0, [fp, #-0x10]
    // 0xbd4748: r16 = "arrow right"
    //     0xbd4748: add             x16, PP, #0x53, lsl #12  ; [pp+0x53cc8] "arrow right"
    //     0xbd474c: ldr             x16, [x16, #0xcc8]
    // 0xbd4750: r30 = Instance_BoxFit
    //     0xbd4750: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbd4754: ldr             lr, [lr, #0xb18]
    // 0xbd4758: stp             lr, x16, [SP]
    // 0xbd475c: mov             x1, x0
    // 0xbd4760: r2 = "assets/images/arrow_right.svg"
    //     0xbd4760: add             x2, PP, #0x53, lsl #12  ; [pp+0x53cd0] "assets/images/arrow_right.svg"
    //     0xbd4764: ldr             x2, [x2, #0xcd0]
    // 0xbd4768: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xbd4768: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xbd476c: ldr             x4, [x4, #0xb28]
    // 0xbd4770: r0 = SvgPicture.asset()
    //     0xbd4770: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbd4774: r0 = Padding()
    //     0xbd4774: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd4778: mov             x1, x0
    // 0xbd477c: r0 = Instance_EdgeInsets
    //     0xbd477c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbd4780: ldr             x0, [x0, #0x1f0]
    // 0xbd4784: stur            x1, [fp, #-0x18]
    // 0xbd4788: StoreField: r1->field_f = r0
    //     0xbd4788: stur            w0, [x1, #0xf]
    // 0xbd478c: ldur            x2, [fp, #-0x10]
    // 0xbd4790: StoreField: r1->field_b = r2
    //     0xbd4790: stur            w2, [x1, #0xb]
    // 0xbd4794: r0 = InkWell()
    //     0xbd4794: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbd4798: mov             x3, x0
    // 0xbd479c: ldur            x0, [fp, #-0x18]
    // 0xbd47a0: stur            x3, [fp, #-0x10]
    // 0xbd47a4: StoreField: r3->field_b = r0
    //     0xbd47a4: stur            w0, [x3, #0xb]
    // 0xbd47a8: r1 = Function '<anonymous closure>':.
    //     0xbd47a8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53cd8] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbd47ac: ldr             x1, [x1, #0xcd8]
    // 0xbd47b0: r2 = Null
    //     0xbd47b0: mov             x2, NULL
    // 0xbd47b4: r0 = AllocateClosure()
    //     0xbd47b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd47b8: mov             x1, x0
    // 0xbd47bc: ldur            x0, [fp, #-0x10]
    // 0xbd47c0: StoreField: r0->field_f = r1
    //     0xbd47c0: stur            w1, [x0, #0xf]
    // 0xbd47c4: r2 = true
    //     0xbd47c4: add             x2, NULL, #0x20  ; true
    // 0xbd47c8: StoreField: r0->field_43 = r2
    //     0xbd47c8: stur            w2, [x0, #0x43]
    // 0xbd47cc: r3 = Instance_BoxShape
    //     0xbd47cc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbd47d0: ldr             x3, [x3, #0x80]
    // 0xbd47d4: StoreField: r0->field_47 = r3
    //     0xbd47d4: stur            w3, [x0, #0x47]
    // 0xbd47d8: StoreField: r0->field_6f = r2
    //     0xbd47d8: stur            w2, [x0, #0x6f]
    // 0xbd47dc: r4 = false
    //     0xbd47dc: add             x4, NULL, #0x30  ; false
    // 0xbd47e0: StoreField: r0->field_73 = r4
    //     0xbd47e0: stur            w4, [x0, #0x73]
    // 0xbd47e4: StoreField: r0->field_83 = r2
    //     0xbd47e4: stur            w2, [x0, #0x83]
    // 0xbd47e8: StoreField: r0->field_7b = r4
    //     0xbd47e8: stur            w4, [x0, #0x7b]
    // 0xbd47ec: r1 = <FlexParentData>
    //     0xbd47ec: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbd47f0: ldr             x1, [x1, #0xe00]
    // 0xbd47f4: r0 = Expanded()
    //     0xbd47f4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbd47f8: stur            x0, [fp, #-0x18]
    // 0xbd47fc: StoreField: r0->field_13 = rZR
    //     0xbd47fc: stur            xzr, [x0, #0x13]
    // 0xbd4800: r2 = Instance_FlexFit
    //     0xbd4800: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbd4804: ldr             x2, [x2, #0xe08]
    // 0xbd4808: StoreField: r0->field_1b = r2
    //     0xbd4808: stur            w2, [x0, #0x1b]
    // 0xbd480c: ldur            x1, [fp, #-0x10]
    // 0xbd4810: StoreField: r0->field_b = r1
    //     0xbd4810: stur            w1, [x0, #0xb]
    // 0xbd4814: ldur            x3, [fp, #-0x30]
    // 0xbd4818: LoadField: r1 = r3->field_13
    //     0xbd4818: ldur            w1, [x3, #0x13]
    // 0xbd481c: DecompressPointer r1
    //     0xbd481c: add             x1, x1, HEAP, lsl #32
    // 0xbd4820: r0 = of()
    //     0xbd4820: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd4824: LoadField: r1 = r0->field_87
    //     0xbd4824: ldur            w1, [x0, #0x87]
    // 0xbd4828: DecompressPointer r1
    //     0xbd4828: add             x1, x1, HEAP, lsl #32
    // 0xbd482c: LoadField: r0 = r1->field_7
    //     0xbd482c: ldur            w0, [x1, #7]
    // 0xbd4830: DecompressPointer r0
    //     0xbd4830: add             x0, x0, HEAP, lsl #32
    // 0xbd4834: stur            x0, [fp, #-0x10]
    // 0xbd4838: r1 = Instance_Color
    //     0xbd4838: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd483c: d0 = 0.700000
    //     0xbd483c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd4840: ldr             d0, [x17, #0xf48]
    // 0xbd4844: r0 = withOpacity()
    //     0xbd4844: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd4848: r16 = 16.000000
    //     0xbd4848: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbd484c: ldr             x16, [x16, #0x188]
    // 0xbd4850: stp             x0, x16, [SP]
    // 0xbd4854: ldur            x1, [fp, #-0x10]
    // 0xbd4858: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd4858: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd485c: ldr             x4, [x4, #0xaa0]
    // 0xbd4860: r0 = copyWith()
    //     0xbd4860: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd4864: stur            x0, [fp, #-0x10]
    // 0xbd4868: r0 = Text()
    //     0xbd4868: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd486c: mov             x2, x0
    // 0xbd4870: r0 = "Filters"
    //     0xbd4870: add             x0, PP, #0x40, lsl #12  ; [pp+0x40bb0] "Filters"
    //     0xbd4874: ldr             x0, [x0, #0xbb0]
    // 0xbd4878: stur            x2, [fp, #-0x20]
    // 0xbd487c: StoreField: r2->field_b = r0
    //     0xbd487c: stur            w0, [x2, #0xb]
    // 0xbd4880: ldur            x0, [fp, #-0x10]
    // 0xbd4884: StoreField: r2->field_13 = r0
    //     0xbd4884: stur            w0, [x2, #0x13]
    // 0xbd4888: r1 = <FlexParentData>
    //     0xbd4888: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbd488c: ldr             x1, [x1, #0xe00]
    // 0xbd4890: r0 = Expanded()
    //     0xbd4890: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbd4894: stur            x0, [fp, #-0x10]
    // 0xbd4898: StoreField: r0->field_13 = rZR
    //     0xbd4898: stur            xzr, [x0, #0x13]
    // 0xbd489c: r2 = Instance_FlexFit
    //     0xbd489c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbd48a0: ldr             x2, [x2, #0xe08]
    // 0xbd48a4: StoreField: r0->field_1b = r2
    //     0xbd48a4: stur            w2, [x0, #0x1b]
    // 0xbd48a8: ldur            x1, [fp, #-0x20]
    // 0xbd48ac: StoreField: r0->field_b = r1
    //     0xbd48ac: stur            w1, [x0, #0xb]
    // 0xbd48b0: r1 = Instance_Color
    //     0xbd48b0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd48b4: d0 = 0.100000
    //     0xbd48b4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbd48b8: r0 = withOpacity()
    //     0xbd48b8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd48bc: mov             x2, x0
    // 0xbd48c0: r1 = Null
    //     0xbd48c0: mov             x1, NULL
    // 0xbd48c4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbd48c4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbd48c8: r0 = Border.all()
    //     0xbd48c8: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbd48cc: stur            x0, [fp, #-0x20]
    // 0xbd48d0: r0 = BoxDecoration()
    //     0xbd48d0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbd48d4: mov             x2, x0
    // 0xbd48d8: ldur            x0, [fp, #-0x20]
    // 0xbd48dc: stur            x2, [fp, #-0x28]
    // 0xbd48e0: StoreField: r2->field_f = r0
    //     0xbd48e0: stur            w0, [x2, #0xf]
    // 0xbd48e4: r0 = Instance_BoxShape
    //     0xbd48e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbd48e8: ldr             x0, [x0, #0x80]
    // 0xbd48ec: StoreField: r2->field_23 = r0
    //     0xbd48ec: stur            w0, [x2, #0x23]
    // 0xbd48f0: ldur            x3, [fp, #-0x30]
    // 0xbd48f4: LoadField: r1 = r3->field_13
    //     0xbd48f4: ldur            w1, [x3, #0x13]
    // 0xbd48f8: DecompressPointer r1
    //     0xbd48f8: add             x1, x1, HEAP, lsl #32
    // 0xbd48fc: r0 = of()
    //     0xbd48fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd4900: LoadField: r1 = r0->field_87
    //     0xbd4900: ldur            w1, [x0, #0x87]
    // 0xbd4904: DecompressPointer r1
    //     0xbd4904: add             x1, x1, HEAP, lsl #32
    // 0xbd4908: LoadField: r0 = r1->field_7
    //     0xbd4908: ldur            w0, [x1, #7]
    // 0xbd490c: DecompressPointer r0
    //     0xbd490c: add             x0, x0, HEAP, lsl #32
    // 0xbd4910: ldur            x2, [fp, #-0x30]
    // 0xbd4914: stur            x0, [fp, #-0x20]
    // 0xbd4918: LoadField: r1 = r2->field_13
    //     0xbd4918: ldur            w1, [x2, #0x13]
    // 0xbd491c: DecompressPointer r1
    //     0xbd491c: add             x1, x1, HEAP, lsl #32
    // 0xbd4920: r0 = of()
    //     0xbd4920: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd4924: LoadField: r1 = r0->field_5b
    //     0xbd4924: ldur            w1, [x0, #0x5b]
    // 0xbd4928: DecompressPointer r1
    //     0xbd4928: add             x1, x1, HEAP, lsl #32
    // 0xbd492c: r0 = LoadClassIdInstr(r1)
    //     0xbd492c: ldur            x0, [x1, #-1]
    //     0xbd4930: ubfx            x0, x0, #0xc, #0x14
    // 0xbd4934: d0 = 0.700000
    //     0xbd4934: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd4938: ldr             d0, [x17, #0xf48]
    // 0xbd493c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbd493c: sub             lr, x0, #0xffa
    //     0xbd4940: ldr             lr, [x21, lr, lsl #3]
    //     0xbd4944: blr             lr
    // 0xbd4948: r16 = 14.000000
    //     0xbd4948: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd494c: ldr             x16, [x16, #0x1d8]
    // 0xbd4950: stp             x0, x16, [SP]
    // 0xbd4954: ldur            x1, [fp, #-0x20]
    // 0xbd4958: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd4958: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd495c: ldr             x4, [x4, #0xaa0]
    // 0xbd4960: r0 = copyWith()
    //     0xbd4960: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd4964: stur            x0, [fp, #-0x20]
    // 0xbd4968: r0 = Text()
    //     0xbd4968: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd496c: mov             x1, x0
    // 0xbd4970: r0 = "CLEAR ALL"
    //     0xbd4970: add             x0, PP, #0x53, lsl #12  ; [pp+0x53ce0] "CLEAR ALL"
    //     0xbd4974: ldr             x0, [x0, #0xce0]
    // 0xbd4978: stur            x1, [fp, #-0x40]
    // 0xbd497c: StoreField: r1->field_b = r0
    //     0xbd497c: stur            w0, [x1, #0xb]
    // 0xbd4980: ldur            x0, [fp, #-0x20]
    // 0xbd4984: StoreField: r1->field_13 = r0
    //     0xbd4984: stur            w0, [x1, #0x13]
    // 0xbd4988: r0 = Instance_TextAlign
    //     0xbd4988: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xbd498c: StoreField: r1->field_1b = r0
    //     0xbd498c: stur            w0, [x1, #0x1b]
    // 0xbd4990: r0 = Center()
    //     0xbd4990: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbd4994: mov             x1, x0
    // 0xbd4998: r0 = Instance_Alignment
    //     0xbd4998: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbd499c: ldr             x0, [x0, #0xb10]
    // 0xbd49a0: stur            x1, [fp, #-0x20]
    // 0xbd49a4: StoreField: r1->field_f = r0
    //     0xbd49a4: stur            w0, [x1, #0xf]
    // 0xbd49a8: ldur            x0, [fp, #-0x40]
    // 0xbd49ac: StoreField: r1->field_b = r0
    //     0xbd49ac: stur            w0, [x1, #0xb]
    // 0xbd49b0: r0 = Container()
    //     0xbd49b0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbd49b4: stur            x0, [fp, #-0x40]
    // 0xbd49b8: r16 = Instance_EdgeInsets
    //     0xbd49b8: add             x16, PP, #0x53, lsl #12  ; [pp+0x53ce8] Obj!EdgeInsets@d596c1
    //     0xbd49bc: ldr             x16, [x16, #0xce8]
    // 0xbd49c0: r30 = 32.000000
    //     0xbd49c0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xbd49c4: ldr             lr, [lr, #0x848]
    // 0xbd49c8: stp             lr, x16, [SP, #0x10]
    // 0xbd49cc: ldur            x16, [fp, #-0x28]
    // 0xbd49d0: ldur            lr, [fp, #-0x20]
    // 0xbd49d4: stp             lr, x16, [SP]
    // 0xbd49d8: mov             x1, x0
    // 0xbd49dc: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, padding, 0x1, null]
    //     0xbd49dc: add             x4, PP, #0x53, lsl #12  ; [pp+0x53cf0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "padding", 0x1, Null]
    //     0xbd49e0: ldr             x4, [x4, #0xcf0]
    // 0xbd49e4: r0 = Container()
    //     0xbd49e4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbd49e8: r0 = Padding()
    //     0xbd49e8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd49ec: mov             x1, x0
    // 0xbd49f0: r0 = Instance_EdgeInsets
    //     0xbd49f0: add             x0, PP, #0x53, lsl #12  ; [pp+0x53cf8] Obj!EdgeInsets@d58281
    //     0xbd49f4: ldr             x0, [x0, #0xcf8]
    // 0xbd49f8: stur            x1, [fp, #-0x20]
    // 0xbd49fc: StoreField: r1->field_f = r0
    //     0xbd49fc: stur            w0, [x1, #0xf]
    // 0xbd4a00: ldur            x0, [fp, #-0x40]
    // 0xbd4a04: StoreField: r1->field_b = r0
    //     0xbd4a04: stur            w0, [x1, #0xb]
    // 0xbd4a08: r0 = InkWell()
    //     0xbd4a08: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbd4a0c: mov             x3, x0
    // 0xbd4a10: ldur            x0, [fp, #-0x20]
    // 0xbd4a14: stur            x3, [fp, #-0x28]
    // 0xbd4a18: StoreField: r3->field_b = r0
    //     0xbd4a18: stur            w0, [x3, #0xb]
    // 0xbd4a1c: ldur            x2, [fp, #-0x30]
    // 0xbd4a20: r1 = Function '<anonymous closure>':.
    //     0xbd4a20: add             x1, PP, #0x53, lsl #12  ; [pp+0x53d00] AnonymousClosure: (0xbd6ee0), in [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xbd4660)
    //     0xbd4a24: ldr             x1, [x1, #0xd00]
    // 0xbd4a28: r0 = AllocateClosure()
    //     0xbd4a28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd4a2c: mov             x1, x0
    // 0xbd4a30: ldur            x0, [fp, #-0x28]
    // 0xbd4a34: StoreField: r0->field_f = r1
    //     0xbd4a34: stur            w1, [x0, #0xf]
    // 0xbd4a38: r2 = true
    //     0xbd4a38: add             x2, NULL, #0x20  ; true
    // 0xbd4a3c: StoreField: r0->field_43 = r2
    //     0xbd4a3c: stur            w2, [x0, #0x43]
    // 0xbd4a40: r1 = Instance_BoxShape
    //     0xbd4a40: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbd4a44: ldr             x1, [x1, #0x80]
    // 0xbd4a48: StoreField: r0->field_47 = r1
    //     0xbd4a48: stur            w1, [x0, #0x47]
    // 0xbd4a4c: StoreField: r0->field_6f = r2
    //     0xbd4a4c: stur            w2, [x0, #0x6f]
    // 0xbd4a50: r3 = false
    //     0xbd4a50: add             x3, NULL, #0x30  ; false
    // 0xbd4a54: StoreField: r0->field_73 = r3
    //     0xbd4a54: stur            w3, [x0, #0x73]
    // 0xbd4a58: StoreField: r0->field_83 = r2
    //     0xbd4a58: stur            w2, [x0, #0x83]
    // 0xbd4a5c: StoreField: r0->field_7b = r3
    //     0xbd4a5c: stur            w3, [x0, #0x7b]
    // 0xbd4a60: r1 = <FlexParentData>
    //     0xbd4a60: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbd4a64: ldr             x1, [x1, #0xe00]
    // 0xbd4a68: r0 = Expanded()
    //     0xbd4a68: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbd4a6c: stur            x0, [fp, #-0x20]
    // 0xbd4a70: StoreField: r0->field_13 = rZR
    //     0xbd4a70: stur            xzr, [x0, #0x13]
    // 0xbd4a74: r1 = Instance_FlexFit
    //     0xbd4a74: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbd4a78: ldr             x1, [x1, #0xe08]
    // 0xbd4a7c: StoreField: r0->field_1b = r1
    //     0xbd4a7c: stur            w1, [x0, #0x1b]
    // 0xbd4a80: ldur            x1, [fp, #-0x28]
    // 0xbd4a84: StoreField: r0->field_b = r1
    //     0xbd4a84: stur            w1, [x0, #0xb]
    // 0xbd4a88: r1 = Null
    //     0xbd4a88: mov             x1, NULL
    // 0xbd4a8c: r2 = 8
    //     0xbd4a8c: movz            x2, #0x8
    // 0xbd4a90: r0 = AllocateArray()
    //     0xbd4a90: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd4a94: mov             x2, x0
    // 0xbd4a98: ldur            x0, [fp, #-0x18]
    // 0xbd4a9c: stur            x2, [fp, #-0x28]
    // 0xbd4aa0: StoreField: r2->field_f = r0
    //     0xbd4aa0: stur            w0, [x2, #0xf]
    // 0xbd4aa4: ldur            x0, [fp, #-0x10]
    // 0xbd4aa8: StoreField: r2->field_13 = r0
    //     0xbd4aa8: stur            w0, [x2, #0x13]
    // 0xbd4aac: r16 = Instance_Spacer
    //     0xbd4aac: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbd4ab0: ldr             x16, [x16, #0xf0]
    // 0xbd4ab4: ArrayStore: r2[0] = r16  ; List_4
    //     0xbd4ab4: stur            w16, [x2, #0x17]
    // 0xbd4ab8: ldur            x0, [fp, #-0x20]
    // 0xbd4abc: StoreField: r2->field_1b = r0
    //     0xbd4abc: stur            w0, [x2, #0x1b]
    // 0xbd4ac0: r1 = <Widget>
    //     0xbd4ac0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd4ac4: r0 = AllocateGrowableArray()
    //     0xbd4ac4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd4ac8: mov             x1, x0
    // 0xbd4acc: ldur            x0, [fp, #-0x28]
    // 0xbd4ad0: stur            x1, [fp, #-0x10]
    // 0xbd4ad4: StoreField: r1->field_f = r0
    //     0xbd4ad4: stur            w0, [x1, #0xf]
    // 0xbd4ad8: r0 = 8
    //     0xbd4ad8: movz            x0, #0x8
    // 0xbd4adc: StoreField: r1->field_b = r0
    //     0xbd4adc: stur            w0, [x1, #0xb]
    // 0xbd4ae0: r0 = Row()
    //     0xbd4ae0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbd4ae4: mov             x1, x0
    // 0xbd4ae8: r0 = Instance_Axis
    //     0xbd4ae8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd4aec: stur            x1, [fp, #-0x18]
    // 0xbd4af0: StoreField: r1->field_f = r0
    //     0xbd4af0: stur            w0, [x1, #0xf]
    // 0xbd4af4: r0 = Instance_MainAxisAlignment
    //     0xbd4af4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd4af8: ldr             x0, [x0, #0xa08]
    // 0xbd4afc: StoreField: r1->field_13 = r0
    //     0xbd4afc: stur            w0, [x1, #0x13]
    // 0xbd4b00: r2 = Instance_MainAxisSize
    //     0xbd4b00: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd4b04: ldr             x2, [x2, #0xa10]
    // 0xbd4b08: ArrayStore: r1[0] = r2  ; List_4
    //     0xbd4b08: stur            w2, [x1, #0x17]
    // 0xbd4b0c: r3 = Instance_CrossAxisAlignment
    //     0xbd4b0c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd4b10: ldr             x3, [x3, #0xa18]
    // 0xbd4b14: StoreField: r1->field_1b = r3
    //     0xbd4b14: stur            w3, [x1, #0x1b]
    // 0xbd4b18: r4 = Instance_VerticalDirection
    //     0xbd4b18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd4b1c: ldr             x4, [x4, #0xa20]
    // 0xbd4b20: StoreField: r1->field_23 = r4
    //     0xbd4b20: stur            w4, [x1, #0x23]
    // 0xbd4b24: r5 = Instance_Clip
    //     0xbd4b24: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd4b28: ldr             x5, [x5, #0x38]
    // 0xbd4b2c: StoreField: r1->field_2b = r5
    //     0xbd4b2c: stur            w5, [x1, #0x2b]
    // 0xbd4b30: StoreField: r1->field_2f = rZR
    //     0xbd4b30: stur            xzr, [x1, #0x2f]
    // 0xbd4b34: ldur            x6, [fp, #-0x10]
    // 0xbd4b38: StoreField: r1->field_b = r6
    //     0xbd4b38: stur            w6, [x1, #0xb]
    // 0xbd4b3c: r0 = SizedBox()
    //     0xbd4b3c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbd4b40: mov             x3, x0
    // 0xbd4b44: r0 = 48.000000
    //     0xbd4b44: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xbd4b48: ldr             x0, [x0, #0xad8]
    // 0xbd4b4c: stur            x3, [fp, #-0x10]
    // 0xbd4b50: StoreField: r3->field_13 = r0
    //     0xbd4b50: stur            w0, [x3, #0x13]
    // 0xbd4b54: ldur            x0, [fp, #-0x18]
    // 0xbd4b58: StoreField: r3->field_b = r0
    //     0xbd4b58: stur            w0, [x3, #0xb]
    // 0xbd4b5c: r1 = <Widget>
    //     0xbd4b5c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd4b60: r2 = 42
    //     0xbd4b60: movz            x2, #0x2a
    // 0xbd4b64: r0 = AllocateArray()
    //     0xbd4b64: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd4b68: mov             x2, x0
    // 0xbd4b6c: ldur            x0, [fp, #-0x10]
    // 0xbd4b70: stur            x2, [fp, #-0x18]
    // 0xbd4b74: StoreField: r2->field_f = r0
    //     0xbd4b74: stur            w0, [x2, #0xf]
    // 0xbd4b78: r16 = Instance_SizedBox
    //     0xbd4b78: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xbd4b7c: ldr             x16, [x16, #0x568]
    // 0xbd4b80: StoreField: r2->field_13 = r16
    //     0xbd4b80: stur            w16, [x2, #0x13]
    // 0xbd4b84: r16 = Instance_Divider
    //     0xbd4b84: add             x16, PP, #0x48, lsl #12  ; [pp+0x484d0] Obj!Divider@d66ca1
    //     0xbd4b88: ldr             x16, [x16, #0x4d0]
    // 0xbd4b8c: ArrayStore: r2[0] = r16  ; List_4
    //     0xbd4b8c: stur            w16, [x2, #0x17]
    // 0xbd4b90: r16 = Instance_SizedBox
    //     0xbd4b90: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xbd4b94: ldr             x16, [x16, #0x568]
    // 0xbd4b98: StoreField: r2->field_1b = r16
    //     0xbd4b98: stur            w16, [x2, #0x1b]
    // 0xbd4b9c: ldur            x0, [fp, #-0x30]
    // 0xbd4ba0: LoadField: r1 = r0->field_13
    //     0xbd4ba0: ldur            w1, [x0, #0x13]
    // 0xbd4ba4: DecompressPointer r1
    //     0xbd4ba4: add             x1, x1, HEAP, lsl #32
    // 0xbd4ba8: r0 = of()
    //     0xbd4ba8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd4bac: LoadField: r1 = r0->field_87
    //     0xbd4bac: ldur            w1, [x0, #0x87]
    // 0xbd4bb0: DecompressPointer r1
    //     0xbd4bb0: add             x1, x1, HEAP, lsl #32
    // 0xbd4bb4: LoadField: r0 = r1->field_7
    //     0xbd4bb4: ldur            w0, [x1, #7]
    // 0xbd4bb8: DecompressPointer r0
    //     0xbd4bb8: add             x0, x0, HEAP, lsl #32
    // 0xbd4bbc: stur            x0, [fp, #-0x10]
    // 0xbd4bc0: r1 = Instance_Color
    //     0xbd4bc0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd4bc4: d0 = 0.700000
    //     0xbd4bc4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd4bc8: ldr             d0, [x17, #0xf48]
    // 0xbd4bcc: r0 = withOpacity()
    //     0xbd4bcc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd4bd0: r16 = 14.000000
    //     0xbd4bd0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd4bd4: ldr             x16, [x16, #0x1d8]
    // 0xbd4bd8: stp             x0, x16, [SP]
    // 0xbd4bdc: ldur            x1, [fp, #-0x10]
    // 0xbd4be0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd4be0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd4be4: ldr             x4, [x4, #0xaa0]
    // 0xbd4be8: r0 = copyWith()
    //     0xbd4be8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd4bec: stur            x0, [fp, #-0x10]
    // 0xbd4bf0: r0 = Text()
    //     0xbd4bf0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd4bf4: mov             x3, x0
    // 0xbd4bf8: r0 = "Product Type"
    //     0xbd4bf8: add             x0, PP, #0x53, lsl #12  ; [pp+0x53d08] "Product Type"
    //     0xbd4bfc: ldr             x0, [x0, #0xd08]
    // 0xbd4c00: stur            x3, [fp, #-0x20]
    // 0xbd4c04: StoreField: r3->field_b = r0
    //     0xbd4c04: stur            w0, [x3, #0xb]
    // 0xbd4c08: ldur            x0, [fp, #-0x10]
    // 0xbd4c0c: StoreField: r3->field_13 = r0
    //     0xbd4c0c: stur            w0, [x3, #0x13]
    // 0xbd4c10: ldur            x0, [fp, #-8]
    // 0xbd4c14: LoadField: r1 = r0->field_b
    //     0xbd4c14: ldur            w1, [x0, #0xb]
    // 0xbd4c18: DecompressPointer r1
    //     0xbd4c18: add             x1, x1, HEAP, lsl #32
    // 0xbd4c1c: cmp             w1, NULL
    // 0xbd4c20: b.eq            #0xbd57d0
    // 0xbd4c24: LoadField: r2 = r1->field_b
    //     0xbd4c24: ldur            w2, [x1, #0xb]
    // 0xbd4c28: DecompressPointer r2
    //     0xbd4c28: add             x2, x2, HEAP, lsl #32
    // 0xbd4c2c: cmp             w2, NULL
    // 0xbd4c30: b.ne            #0xbd4c3c
    // 0xbd4c34: r6 = Null
    //     0xbd4c34: mov             x6, NULL
    // 0xbd4c38: b               #0xbd4c60
    // 0xbd4c3c: LoadField: r1 = r2->field_7
    //     0xbd4c3c: ldur            w1, [x2, #7]
    // 0xbd4c40: DecompressPointer r1
    //     0xbd4c40: add             x1, x1, HEAP, lsl #32
    // 0xbd4c44: cmp             w1, NULL
    // 0xbd4c48: b.ne            #0xbd4c54
    // 0xbd4c4c: r1 = Null
    //     0xbd4c4c: mov             x1, NULL
    // 0xbd4c50: b               #0xbd4c5c
    // 0xbd4c54: LoadField: r2 = r1->field_b
    //     0xbd4c54: ldur            w2, [x1, #0xb]
    // 0xbd4c58: mov             x1, x2
    // 0xbd4c5c: mov             x6, x1
    // 0xbd4c60: ldur            x5, [fp, #-0x30]
    // 0xbd4c64: ldur            x4, [fp, #-0x18]
    // 0xbd4c68: mov             x2, x5
    // 0xbd4c6c: stur            x6, [fp, #-0x10]
    // 0xbd4c70: r1 = Function '<anonymous closure>':.
    //     0xbd4c70: add             x1, PP, #0x53, lsl #12  ; [pp+0x53d10] AnonymousClosure: (0xbd6b3c), in [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xbd4660)
    //     0xbd4c74: ldr             x1, [x1, #0xd10]
    // 0xbd4c78: r0 = AllocateClosure()
    //     0xbd4c78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd4c7c: stur            x0, [fp, #-0x28]
    // 0xbd4c80: r0 = ListView()
    //     0xbd4c80: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbd4c84: stur            x0, [fp, #-0x40]
    // 0xbd4c88: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbd4c88: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbd4c8c: ldr             x16, [x16, #0x1c8]
    // 0xbd4c90: r30 = true
    //     0xbd4c90: add             lr, NULL, #0x20  ; true
    // 0xbd4c94: stp             lr, x16, [SP]
    // 0xbd4c98: mov             x1, x0
    // 0xbd4c9c: ldur            x2, [fp, #-0x28]
    // 0xbd4ca0: ldur            x3, [fp, #-0x10]
    // 0xbd4ca4: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xbd4ca4: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xbd4ca8: ldr             x4, [x4, #0xd18]
    // 0xbd4cac: r0 = ListView.builder()
    //     0xbd4cac: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbd4cb0: ldur            x2, [fp, #-0x30]
    // 0xbd4cb4: LoadField: r1 = r2->field_13
    //     0xbd4cb4: ldur            w1, [x2, #0x13]
    // 0xbd4cb8: DecompressPointer r1
    //     0xbd4cb8: add             x1, x1, HEAP, lsl #32
    // 0xbd4cbc: r0 = of()
    //     0xbd4cbc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd4cc0: LoadField: r1 = r0->field_87
    //     0xbd4cc0: ldur            w1, [x0, #0x87]
    // 0xbd4cc4: DecompressPointer r1
    //     0xbd4cc4: add             x1, x1, HEAP, lsl #32
    // 0xbd4cc8: LoadField: r0 = r1->field_27
    //     0xbd4cc8: ldur            w0, [x1, #0x27]
    // 0xbd4ccc: DecompressPointer r0
    //     0xbd4ccc: add             x0, x0, HEAP, lsl #32
    // 0xbd4cd0: ldur            x2, [fp, #-0x30]
    // 0xbd4cd4: stur            x0, [fp, #-0x10]
    // 0xbd4cd8: LoadField: r1 = r2->field_13
    //     0xbd4cd8: ldur            w1, [x2, #0x13]
    // 0xbd4cdc: DecompressPointer r1
    //     0xbd4cdc: add             x1, x1, HEAP, lsl #32
    // 0xbd4ce0: r0 = of()
    //     0xbd4ce0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd4ce4: LoadField: r1 = r0->field_5b
    //     0xbd4ce4: ldur            w1, [x0, #0x5b]
    // 0xbd4ce8: DecompressPointer r1
    //     0xbd4ce8: add             x1, x1, HEAP, lsl #32
    // 0xbd4cec: r0 = LoadClassIdInstr(r1)
    //     0xbd4cec: ldur            x0, [x1, #-1]
    //     0xbd4cf0: ubfx            x0, x0, #0xc, #0x14
    // 0xbd4cf4: d0 = 0.400000
    //     0xbd4cf4: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbd4cf8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbd4cf8: sub             lr, x0, #0xffa
    //     0xbd4cfc: ldr             lr, [x21, lr, lsl #3]
    //     0xbd4d00: blr             lr
    // 0xbd4d04: r16 = 16.000000
    //     0xbd4d04: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbd4d08: ldr             x16, [x16, #0x188]
    // 0xbd4d0c: stp             x0, x16, [SP]
    // 0xbd4d10: ldur            x1, [fp, #-0x10]
    // 0xbd4d14: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd4d14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd4d18: ldr             x4, [x4, #0xaa0]
    // 0xbd4d1c: r0 = copyWith()
    //     0xbd4d1c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd4d20: r0 = Accordion()
    //     0xbd4d20: bl              #0xba4874  ; AllocateAccordionStub -> Accordion (size=0x3c)
    // 0xbd4d24: mov             x1, x0
    // 0xbd4d28: ldur            x0, [fp, #-0x20]
    // 0xbd4d2c: stur            x1, [fp, #-0x10]
    // 0xbd4d30: StoreField: r1->field_b = r0
    //     0xbd4d30: stur            w0, [x1, #0xb]
    // 0xbd4d34: ldur            x0, [fp, #-0x40]
    // 0xbd4d38: StoreField: r1->field_13 = r0
    //     0xbd4d38: stur            w0, [x1, #0x13]
    // 0xbd4d3c: r0 = false
    //     0xbd4d3c: add             x0, NULL, #0x30  ; false
    // 0xbd4d40: ArrayStore: r1[0] = r0  ; List_4
    //     0xbd4d40: stur            w0, [x1, #0x17]
    // 0xbd4d44: r2 = true
    //     0xbd4d44: add             x2, NULL, #0x20  ; true
    // 0xbd4d48: StoreField: r1->field_1f = r2
    //     0xbd4d48: stur            w2, [x1, #0x1f]
    // 0xbd4d4c: r0 = Padding()
    //     0xbd4d4c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd4d50: r2 = Instance_EdgeInsets
    //     0xbd4d50: add             x2, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xbd4d54: ldr             x2, [x2, #0xf30]
    // 0xbd4d58: StoreField: r0->field_f = r2
    //     0xbd4d58: stur            w2, [x0, #0xf]
    // 0xbd4d5c: ldur            x1, [fp, #-0x10]
    // 0xbd4d60: StoreField: r0->field_b = r1
    //     0xbd4d60: stur            w1, [x0, #0xb]
    // 0xbd4d64: ldur            x1, [fp, #-0x18]
    // 0xbd4d68: ArrayStore: r1[4] = r0  ; List_4
    //     0xbd4d68: add             x25, x1, #0x1f
    //     0xbd4d6c: str             w0, [x25]
    //     0xbd4d70: tbz             w0, #0, #0xbd4d8c
    //     0xbd4d74: ldurb           w16, [x1, #-1]
    //     0xbd4d78: ldurb           w17, [x0, #-1]
    //     0xbd4d7c: and             x16, x17, x16, lsr #2
    //     0xbd4d80: tst             x16, HEAP, lsr #32
    //     0xbd4d84: b.eq            #0xbd4d8c
    //     0xbd4d88: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd4d8c: ldur            x0, [fp, #-0x18]
    // 0xbd4d90: r16 = Instance_SizedBox
    //     0xbd4d90: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xbd4d94: ldr             x16, [x16, #0x568]
    // 0xbd4d98: StoreField: r0->field_23 = r16
    //     0xbd4d98: stur            w16, [x0, #0x23]
    // 0xbd4d9c: r16 = Instance_Padding
    //     0xbd4d9c: add             x16, PP, #0x53, lsl #12  ; [pp+0x53d20] Obj!Padding@d684c1
    //     0xbd4da0: ldr             x16, [x16, #0xd20]
    // 0xbd4da4: StoreField: r0->field_27 = r16
    //     0xbd4da4: stur            w16, [x0, #0x27]
    // 0xbd4da8: r16 = Instance_SizedBox
    //     0xbd4da8: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xbd4dac: ldr             x16, [x16, #0x568]
    // 0xbd4db0: StoreField: r0->field_2b = r16
    //     0xbd4db0: stur            w16, [x0, #0x2b]
    // 0xbd4db4: ldur            x3, [fp, #-0x30]
    // 0xbd4db8: LoadField: r1 = r3->field_13
    //     0xbd4db8: ldur            w1, [x3, #0x13]
    // 0xbd4dbc: DecompressPointer r1
    //     0xbd4dbc: add             x1, x1, HEAP, lsl #32
    // 0xbd4dc0: r0 = of()
    //     0xbd4dc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd4dc4: LoadField: r1 = r0->field_87
    //     0xbd4dc4: ldur            w1, [x0, #0x87]
    // 0xbd4dc8: DecompressPointer r1
    //     0xbd4dc8: add             x1, x1, HEAP, lsl #32
    // 0xbd4dcc: LoadField: r0 = r1->field_7
    //     0xbd4dcc: ldur            w0, [x1, #7]
    // 0xbd4dd0: DecompressPointer r0
    //     0xbd4dd0: add             x0, x0, HEAP, lsl #32
    // 0xbd4dd4: stur            x0, [fp, #-0x10]
    // 0xbd4dd8: r1 = Instance_Color
    //     0xbd4dd8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd4ddc: d0 = 0.700000
    //     0xbd4ddc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd4de0: ldr             d0, [x17, #0xf48]
    // 0xbd4de4: r0 = withOpacity()
    //     0xbd4de4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd4de8: r16 = 14.000000
    //     0xbd4de8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd4dec: ldr             x16, [x16, #0x1d8]
    // 0xbd4df0: stp             x0, x16, [SP]
    // 0xbd4df4: ldur            x1, [fp, #-0x10]
    // 0xbd4df8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd4df8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd4dfc: ldr             x4, [x4, #0xaa0]
    // 0xbd4e00: r0 = copyWith()
    //     0xbd4e00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd4e04: stur            x0, [fp, #-0x10]
    // 0xbd4e08: r0 = Text()
    //     0xbd4e08: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd4e0c: mov             x3, x0
    // 0xbd4e10: r0 = "Size"
    //     0xbd4e10: add             x0, PP, #0x52, lsl #12  ; [pp+0x52730] "Size"
    //     0xbd4e14: ldr             x0, [x0, #0x730]
    // 0xbd4e18: stur            x3, [fp, #-0x20]
    // 0xbd4e1c: StoreField: r3->field_b = r0
    //     0xbd4e1c: stur            w0, [x3, #0xb]
    // 0xbd4e20: ldur            x0, [fp, #-0x10]
    // 0xbd4e24: StoreField: r3->field_13 = r0
    //     0xbd4e24: stur            w0, [x3, #0x13]
    // 0xbd4e28: ldur            x0, [fp, #-8]
    // 0xbd4e2c: LoadField: r1 = r0->field_b
    //     0xbd4e2c: ldur            w1, [x0, #0xb]
    // 0xbd4e30: DecompressPointer r1
    //     0xbd4e30: add             x1, x1, HEAP, lsl #32
    // 0xbd4e34: cmp             w1, NULL
    // 0xbd4e38: b.eq            #0xbd57d4
    // 0xbd4e3c: LoadField: r2 = r1->field_b
    //     0xbd4e3c: ldur            w2, [x1, #0xb]
    // 0xbd4e40: DecompressPointer r2
    //     0xbd4e40: add             x2, x2, HEAP, lsl #32
    // 0xbd4e44: cmp             w2, NULL
    // 0xbd4e48: b.ne            #0xbd4e54
    // 0xbd4e4c: r6 = Null
    //     0xbd4e4c: mov             x6, NULL
    // 0xbd4e50: b               #0xbd4e78
    // 0xbd4e54: LoadField: r1 = r2->field_f
    //     0xbd4e54: ldur            w1, [x2, #0xf]
    // 0xbd4e58: DecompressPointer r1
    //     0xbd4e58: add             x1, x1, HEAP, lsl #32
    // 0xbd4e5c: cmp             w1, NULL
    // 0xbd4e60: b.ne            #0xbd4e6c
    // 0xbd4e64: r1 = Null
    //     0xbd4e64: mov             x1, NULL
    // 0xbd4e68: b               #0xbd4e74
    // 0xbd4e6c: LoadField: r2 = r1->field_b
    //     0xbd4e6c: ldur            w2, [x1, #0xb]
    // 0xbd4e70: mov             x1, x2
    // 0xbd4e74: mov             x6, x1
    // 0xbd4e78: ldur            x5, [fp, #-0x30]
    // 0xbd4e7c: ldur            x4, [fp, #-0x18]
    // 0xbd4e80: mov             x2, x5
    // 0xbd4e84: stur            x6, [fp, #-0x10]
    // 0xbd4e88: r1 = Function '<anonymous closure>':.
    //     0xbd4e88: add             x1, PP, #0x53, lsl #12  ; [pp+0x53d28] AnonymousClosure: (0xbd67c8), in [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xbd4660)
    //     0xbd4e8c: ldr             x1, [x1, #0xd28]
    // 0xbd4e90: r0 = AllocateClosure()
    //     0xbd4e90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd4e94: stur            x0, [fp, #-0x28]
    // 0xbd4e98: r0 = ListView()
    //     0xbd4e98: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbd4e9c: stur            x0, [fp, #-0x40]
    // 0xbd4ea0: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbd4ea0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbd4ea4: ldr             x16, [x16, #0x1c8]
    // 0xbd4ea8: r30 = true
    //     0xbd4ea8: add             lr, NULL, #0x20  ; true
    // 0xbd4eac: stp             lr, x16, [SP]
    // 0xbd4eb0: mov             x1, x0
    // 0xbd4eb4: ldur            x2, [fp, #-0x28]
    // 0xbd4eb8: ldur            x3, [fp, #-0x10]
    // 0xbd4ebc: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xbd4ebc: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xbd4ec0: ldr             x4, [x4, #0xd18]
    // 0xbd4ec4: r0 = ListView.builder()
    //     0xbd4ec4: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbd4ec8: ldur            x2, [fp, #-0x30]
    // 0xbd4ecc: LoadField: r1 = r2->field_13
    //     0xbd4ecc: ldur            w1, [x2, #0x13]
    // 0xbd4ed0: DecompressPointer r1
    //     0xbd4ed0: add             x1, x1, HEAP, lsl #32
    // 0xbd4ed4: r0 = of()
    //     0xbd4ed4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd4ed8: LoadField: r1 = r0->field_87
    //     0xbd4ed8: ldur            w1, [x0, #0x87]
    // 0xbd4edc: DecompressPointer r1
    //     0xbd4edc: add             x1, x1, HEAP, lsl #32
    // 0xbd4ee0: LoadField: r0 = r1->field_7
    //     0xbd4ee0: ldur            w0, [x1, #7]
    // 0xbd4ee4: DecompressPointer r0
    //     0xbd4ee4: add             x0, x0, HEAP, lsl #32
    // 0xbd4ee8: stur            x0, [fp, #-0x10]
    // 0xbd4eec: r1 = Instance_Color
    //     0xbd4eec: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd4ef0: d0 = 0.700000
    //     0xbd4ef0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd4ef4: ldr             d0, [x17, #0xf48]
    // 0xbd4ef8: r0 = withOpacity()
    //     0xbd4ef8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd4efc: r16 = 16.000000
    //     0xbd4efc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbd4f00: ldr             x16, [x16, #0x188]
    // 0xbd4f04: stp             x0, x16, [SP]
    // 0xbd4f08: ldur            x1, [fp, #-0x10]
    // 0xbd4f0c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd4f0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd4f10: ldr             x4, [x4, #0xaa0]
    // 0xbd4f14: r0 = copyWith()
    //     0xbd4f14: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd4f18: r0 = Accordion()
    //     0xbd4f18: bl              #0xba4874  ; AllocateAccordionStub -> Accordion (size=0x3c)
    // 0xbd4f1c: mov             x1, x0
    // 0xbd4f20: ldur            x0, [fp, #-0x20]
    // 0xbd4f24: stur            x1, [fp, #-0x10]
    // 0xbd4f28: StoreField: r1->field_b = r0
    //     0xbd4f28: stur            w0, [x1, #0xb]
    // 0xbd4f2c: ldur            x0, [fp, #-0x40]
    // 0xbd4f30: StoreField: r1->field_13 = r0
    //     0xbd4f30: stur            w0, [x1, #0x13]
    // 0xbd4f34: r0 = false
    //     0xbd4f34: add             x0, NULL, #0x30  ; false
    // 0xbd4f38: ArrayStore: r1[0] = r0  ; List_4
    //     0xbd4f38: stur            w0, [x1, #0x17]
    // 0xbd4f3c: r2 = true
    //     0xbd4f3c: add             x2, NULL, #0x20  ; true
    // 0xbd4f40: StoreField: r1->field_1f = r2
    //     0xbd4f40: stur            w2, [x1, #0x1f]
    // 0xbd4f44: r0 = Padding()
    //     0xbd4f44: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd4f48: r2 = Instance_EdgeInsets
    //     0xbd4f48: add             x2, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xbd4f4c: ldr             x2, [x2, #0xf30]
    // 0xbd4f50: StoreField: r0->field_f = r2
    //     0xbd4f50: stur            w2, [x0, #0xf]
    // 0xbd4f54: ldur            x1, [fp, #-0x10]
    // 0xbd4f58: StoreField: r0->field_b = r1
    //     0xbd4f58: stur            w1, [x0, #0xb]
    // 0xbd4f5c: ldur            x1, [fp, #-0x18]
    // 0xbd4f60: ArrayStore: r1[8] = r0  ; List_4
    //     0xbd4f60: add             x25, x1, #0x2f
    //     0xbd4f64: str             w0, [x25]
    //     0xbd4f68: tbz             w0, #0, #0xbd4f84
    //     0xbd4f6c: ldurb           w16, [x1, #-1]
    //     0xbd4f70: ldurb           w17, [x0, #-1]
    //     0xbd4f74: and             x16, x17, x16, lsr #2
    //     0xbd4f78: tst             x16, HEAP, lsr #32
    //     0xbd4f7c: b.eq            #0xbd4f84
    //     0xbd4f80: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd4f84: ldur            x0, [fp, #-0x18]
    // 0xbd4f88: r16 = Instance_SizedBox
    //     0xbd4f88: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xbd4f8c: ldr             x16, [x16, #0x568]
    // 0xbd4f90: StoreField: r0->field_33 = r16
    //     0xbd4f90: stur            w16, [x0, #0x33]
    // 0xbd4f94: r16 = Instance_Padding
    //     0xbd4f94: add             x16, PP, #0x53, lsl #12  ; [pp+0x53d20] Obj!Padding@d684c1
    //     0xbd4f98: ldr             x16, [x16, #0xd20]
    // 0xbd4f9c: StoreField: r0->field_37 = r16
    //     0xbd4f9c: stur            w16, [x0, #0x37]
    // 0xbd4fa0: r16 = Instance_SizedBox
    //     0xbd4fa0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xbd4fa4: ldr             x16, [x16, #0x568]
    // 0xbd4fa8: StoreField: r0->field_3b = r16
    //     0xbd4fa8: stur            w16, [x0, #0x3b]
    // 0xbd4fac: ldur            x3, [fp, #-0x30]
    // 0xbd4fb0: LoadField: r1 = r3->field_13
    //     0xbd4fb0: ldur            w1, [x3, #0x13]
    // 0xbd4fb4: DecompressPointer r1
    //     0xbd4fb4: add             x1, x1, HEAP, lsl #32
    // 0xbd4fb8: r0 = of()
    //     0xbd4fb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd4fbc: LoadField: r1 = r0->field_87
    //     0xbd4fbc: ldur            w1, [x0, #0x87]
    // 0xbd4fc0: DecompressPointer r1
    //     0xbd4fc0: add             x1, x1, HEAP, lsl #32
    // 0xbd4fc4: LoadField: r0 = r1->field_7
    //     0xbd4fc4: ldur            w0, [x1, #7]
    // 0xbd4fc8: DecompressPointer r0
    //     0xbd4fc8: add             x0, x0, HEAP, lsl #32
    // 0xbd4fcc: stur            x0, [fp, #-0x10]
    // 0xbd4fd0: r1 = Instance_Color
    //     0xbd4fd0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd4fd4: d0 = 0.700000
    //     0xbd4fd4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd4fd8: ldr             d0, [x17, #0xf48]
    // 0xbd4fdc: r0 = withOpacity()
    //     0xbd4fdc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd4fe0: r16 = 16.000000
    //     0xbd4fe0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbd4fe4: ldr             x16, [x16, #0x188]
    // 0xbd4fe8: stp             x0, x16, [SP]
    // 0xbd4fec: ldur            x1, [fp, #-0x10]
    // 0xbd4ff0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd4ff0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd4ff4: ldr             x4, [x4, #0xaa0]
    // 0xbd4ff8: r0 = copyWith()
    //     0xbd4ff8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd4ffc: stur            x0, [fp, #-0x10]
    // 0xbd5000: r0 = Text()
    //     0xbd5000: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd5004: mov             x3, x0
    // 0xbd5008: r0 = "Colors"
    //     0xbd5008: add             x0, PP, #0x53, lsl #12  ; [pp+0x53d30] "Colors"
    //     0xbd500c: ldr             x0, [x0, #0xd30]
    // 0xbd5010: stur            x3, [fp, #-0x20]
    // 0xbd5014: StoreField: r3->field_b = r0
    //     0xbd5014: stur            w0, [x3, #0xb]
    // 0xbd5018: ldur            x0, [fp, #-0x10]
    // 0xbd501c: StoreField: r3->field_13 = r0
    //     0xbd501c: stur            w0, [x3, #0x13]
    // 0xbd5020: ldur            x0, [fp, #-8]
    // 0xbd5024: LoadField: r1 = r0->field_b
    //     0xbd5024: ldur            w1, [x0, #0xb]
    // 0xbd5028: DecompressPointer r1
    //     0xbd5028: add             x1, x1, HEAP, lsl #32
    // 0xbd502c: cmp             w1, NULL
    // 0xbd5030: b.eq            #0xbd57d8
    // 0xbd5034: LoadField: r2 = r1->field_b
    //     0xbd5034: ldur            w2, [x1, #0xb]
    // 0xbd5038: DecompressPointer r2
    //     0xbd5038: add             x2, x2, HEAP, lsl #32
    // 0xbd503c: cmp             w2, NULL
    // 0xbd5040: b.ne            #0xbd504c
    // 0xbd5044: r6 = Null
    //     0xbd5044: mov             x6, NULL
    // 0xbd5048: b               #0xbd5070
    // 0xbd504c: LoadField: r1 = r2->field_b
    //     0xbd504c: ldur            w1, [x2, #0xb]
    // 0xbd5050: DecompressPointer r1
    //     0xbd5050: add             x1, x1, HEAP, lsl #32
    // 0xbd5054: cmp             w1, NULL
    // 0xbd5058: b.ne            #0xbd5064
    // 0xbd505c: r1 = Null
    //     0xbd505c: mov             x1, NULL
    // 0xbd5060: b               #0xbd506c
    // 0xbd5064: LoadField: r2 = r1->field_b
    //     0xbd5064: ldur            w2, [x1, #0xb]
    // 0xbd5068: mov             x1, x2
    // 0xbd506c: mov             x6, x1
    // 0xbd5070: ldur            x5, [fp, #-0x30]
    // 0xbd5074: ldur            x4, [fp, #-0x18]
    // 0xbd5078: mov             x2, x5
    // 0xbd507c: stur            x6, [fp, #-0x10]
    // 0xbd5080: r1 = Function '<anonymous closure>':.
    //     0xbd5080: add             x1, PP, #0x53, lsl #12  ; [pp+0x53d38] AnonymousClosure: (0xbd6438), in [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xbd4660)
    //     0xbd5084: ldr             x1, [x1, #0xd38]
    // 0xbd5088: r0 = AllocateClosure()
    //     0xbd5088: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd508c: stur            x0, [fp, #-0x28]
    // 0xbd5090: r0 = ListView()
    //     0xbd5090: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbd5094: stur            x0, [fp, #-0x40]
    // 0xbd5098: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbd5098: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbd509c: ldr             x16, [x16, #0x1c8]
    // 0xbd50a0: r30 = true
    //     0xbd50a0: add             lr, NULL, #0x20  ; true
    // 0xbd50a4: stp             lr, x16, [SP]
    // 0xbd50a8: mov             x1, x0
    // 0xbd50ac: ldur            x2, [fp, #-0x28]
    // 0xbd50b0: ldur            x3, [fp, #-0x10]
    // 0xbd50b4: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xbd50b4: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xbd50b8: ldr             x4, [x4, #0xd18]
    // 0xbd50bc: r0 = ListView.builder()
    //     0xbd50bc: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbd50c0: ldur            x2, [fp, #-0x30]
    // 0xbd50c4: LoadField: r1 = r2->field_13
    //     0xbd50c4: ldur            w1, [x2, #0x13]
    // 0xbd50c8: DecompressPointer r1
    //     0xbd50c8: add             x1, x1, HEAP, lsl #32
    // 0xbd50cc: r0 = of()
    //     0xbd50cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd50d0: LoadField: r1 = r0->field_87
    //     0xbd50d0: ldur            w1, [x0, #0x87]
    // 0xbd50d4: DecompressPointer r1
    //     0xbd50d4: add             x1, x1, HEAP, lsl #32
    // 0xbd50d8: LoadField: r0 = r1->field_7
    //     0xbd50d8: ldur            w0, [x1, #7]
    // 0xbd50dc: DecompressPointer r0
    //     0xbd50dc: add             x0, x0, HEAP, lsl #32
    // 0xbd50e0: ldur            x2, [fp, #-0x30]
    // 0xbd50e4: stur            x0, [fp, #-0x10]
    // 0xbd50e8: LoadField: r1 = r2->field_13
    //     0xbd50e8: ldur            w1, [x2, #0x13]
    // 0xbd50ec: DecompressPointer r1
    //     0xbd50ec: add             x1, x1, HEAP, lsl #32
    // 0xbd50f0: r0 = of()
    //     0xbd50f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd50f4: LoadField: r1 = r0->field_5b
    //     0xbd50f4: ldur            w1, [x0, #0x5b]
    // 0xbd50f8: DecompressPointer r1
    //     0xbd50f8: add             x1, x1, HEAP, lsl #32
    // 0xbd50fc: r0 = LoadClassIdInstr(r1)
    //     0xbd50fc: ldur            x0, [x1, #-1]
    //     0xbd5100: ubfx            x0, x0, #0xc, #0x14
    // 0xbd5104: d0 = 0.700000
    //     0xbd5104: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd5108: ldr             d0, [x17, #0xf48]
    // 0xbd510c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbd510c: sub             lr, x0, #0xffa
    //     0xbd5110: ldr             lr, [x21, lr, lsl #3]
    //     0xbd5114: blr             lr
    // 0xbd5118: r16 = 16.000000
    //     0xbd5118: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbd511c: ldr             x16, [x16, #0x188]
    // 0xbd5120: stp             x0, x16, [SP]
    // 0xbd5124: ldur            x1, [fp, #-0x10]
    // 0xbd5128: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd5128: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd512c: ldr             x4, [x4, #0xaa0]
    // 0xbd5130: r0 = copyWith()
    //     0xbd5130: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd5134: r0 = Accordion()
    //     0xbd5134: bl              #0xba4874  ; AllocateAccordionStub -> Accordion (size=0x3c)
    // 0xbd5138: mov             x1, x0
    // 0xbd513c: ldur            x0, [fp, #-0x20]
    // 0xbd5140: stur            x1, [fp, #-0x10]
    // 0xbd5144: StoreField: r1->field_b = r0
    //     0xbd5144: stur            w0, [x1, #0xb]
    // 0xbd5148: ldur            x0, [fp, #-0x40]
    // 0xbd514c: StoreField: r1->field_13 = r0
    //     0xbd514c: stur            w0, [x1, #0x13]
    // 0xbd5150: r0 = false
    //     0xbd5150: add             x0, NULL, #0x30  ; false
    // 0xbd5154: ArrayStore: r1[0] = r0  ; List_4
    //     0xbd5154: stur            w0, [x1, #0x17]
    // 0xbd5158: r2 = true
    //     0xbd5158: add             x2, NULL, #0x20  ; true
    // 0xbd515c: StoreField: r1->field_1f = r2
    //     0xbd515c: stur            w2, [x1, #0x1f]
    // 0xbd5160: r0 = Padding()
    //     0xbd5160: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd5164: r2 = Instance_EdgeInsets
    //     0xbd5164: add             x2, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xbd5168: ldr             x2, [x2, #0xf30]
    // 0xbd516c: StoreField: r0->field_f = r2
    //     0xbd516c: stur            w2, [x0, #0xf]
    // 0xbd5170: ldur            x1, [fp, #-0x10]
    // 0xbd5174: StoreField: r0->field_b = r1
    //     0xbd5174: stur            w1, [x0, #0xb]
    // 0xbd5178: ldur            x1, [fp, #-0x18]
    // 0xbd517c: ArrayStore: r1[12] = r0  ; List_4
    //     0xbd517c: add             x25, x1, #0x3f
    //     0xbd5180: str             w0, [x25]
    //     0xbd5184: tbz             w0, #0, #0xbd51a0
    //     0xbd5188: ldurb           w16, [x1, #-1]
    //     0xbd518c: ldurb           w17, [x0, #-1]
    //     0xbd5190: and             x16, x17, x16, lsr #2
    //     0xbd5194: tst             x16, HEAP, lsr #32
    //     0xbd5198: b.eq            #0xbd51a0
    //     0xbd519c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd51a0: ldur            x0, [fp, #-0x18]
    // 0xbd51a4: r16 = Instance_SizedBox
    //     0xbd51a4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xbd51a8: ldr             x16, [x16, #0x568]
    // 0xbd51ac: StoreField: r0->field_43 = r16
    //     0xbd51ac: stur            w16, [x0, #0x43]
    // 0xbd51b0: r16 = Instance_Padding
    //     0xbd51b0: add             x16, PP, #0x53, lsl #12  ; [pp+0x53d20] Obj!Padding@d684c1
    //     0xbd51b4: ldr             x16, [x16, #0xd20]
    // 0xbd51b8: StoreField: r0->field_47 = r16
    //     0xbd51b8: stur            w16, [x0, #0x47]
    // 0xbd51bc: r16 = Instance_SizedBox
    //     0xbd51bc: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xbd51c0: ldr             x16, [x16, #0x568]
    // 0xbd51c4: StoreField: r0->field_4b = r16
    //     0xbd51c4: stur            w16, [x0, #0x4b]
    // 0xbd51c8: ldur            x3, [fp, #-0x30]
    // 0xbd51cc: LoadField: r1 = r3->field_13
    //     0xbd51cc: ldur            w1, [x3, #0x13]
    // 0xbd51d0: DecompressPointer r1
    //     0xbd51d0: add             x1, x1, HEAP, lsl #32
    // 0xbd51d4: r0 = of()
    //     0xbd51d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd51d8: LoadField: r1 = r0->field_87
    //     0xbd51d8: ldur            w1, [x0, #0x87]
    // 0xbd51dc: DecompressPointer r1
    //     0xbd51dc: add             x1, x1, HEAP, lsl #32
    // 0xbd51e0: LoadField: r0 = r1->field_7
    //     0xbd51e0: ldur            w0, [x1, #7]
    // 0xbd51e4: DecompressPointer r0
    //     0xbd51e4: add             x0, x0, HEAP, lsl #32
    // 0xbd51e8: stur            x0, [fp, #-0x10]
    // 0xbd51ec: r1 = Instance_Color
    //     0xbd51ec: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd51f0: d0 = 0.700000
    //     0xbd51f0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd51f4: ldr             d0, [x17, #0xf48]
    // 0xbd51f8: r0 = withOpacity()
    //     0xbd51f8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd51fc: r16 = 16.000000
    //     0xbd51fc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbd5200: ldr             x16, [x16, #0x188]
    // 0xbd5204: stp             x0, x16, [SP]
    // 0xbd5208: ldur            x1, [fp, #-0x10]
    // 0xbd520c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd520c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd5210: ldr             x4, [x4, #0xaa0]
    // 0xbd5214: r0 = copyWith()
    //     0xbd5214: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd5218: stur            x0, [fp, #-0x10]
    // 0xbd521c: r0 = Text()
    //     0xbd521c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd5220: mov             x3, x0
    // 0xbd5224: r0 = "Price Range"
    //     0xbd5224: add             x0, PP, #0x53, lsl #12  ; [pp+0x53d40] "Price Range"
    //     0xbd5228: ldr             x0, [x0, #0xd40]
    // 0xbd522c: stur            x3, [fp, #-0x20]
    // 0xbd5230: StoreField: r3->field_b = r0
    //     0xbd5230: stur            w0, [x3, #0xb]
    // 0xbd5234: ldur            x0, [fp, #-0x10]
    // 0xbd5238: StoreField: r3->field_13 = r0
    //     0xbd5238: stur            w0, [x3, #0x13]
    // 0xbd523c: ldur            x0, [fp, #-8]
    // 0xbd5240: LoadField: r1 = r0->field_b
    //     0xbd5240: ldur            w1, [x0, #0xb]
    // 0xbd5244: DecompressPointer r1
    //     0xbd5244: add             x1, x1, HEAP, lsl #32
    // 0xbd5248: cmp             w1, NULL
    // 0xbd524c: b.eq            #0xbd57dc
    // 0xbd5250: LoadField: r2 = r1->field_b
    //     0xbd5250: ldur            w2, [x1, #0xb]
    // 0xbd5254: DecompressPointer r2
    //     0xbd5254: add             x2, x2, HEAP, lsl #32
    // 0xbd5258: cmp             w2, NULL
    // 0xbd525c: b.ne            #0xbd5268
    // 0xbd5260: r7 = Null
    //     0xbd5260: mov             x7, NULL
    // 0xbd5264: b               #0xbd528c
    // 0xbd5268: LoadField: r1 = r2->field_13
    //     0xbd5268: ldur            w1, [x2, #0x13]
    // 0xbd526c: DecompressPointer r1
    //     0xbd526c: add             x1, x1, HEAP, lsl #32
    // 0xbd5270: cmp             w1, NULL
    // 0xbd5274: b.ne            #0xbd5280
    // 0xbd5278: r1 = Null
    //     0xbd5278: mov             x1, NULL
    // 0xbd527c: b               #0xbd5288
    // 0xbd5280: LoadField: r2 = r1->field_b
    //     0xbd5280: ldur            w2, [x1, #0xb]
    // 0xbd5284: mov             x1, x2
    // 0xbd5288: mov             x7, x1
    // 0xbd528c: ldur            x5, [fp, #-0x30]
    // 0xbd5290: ldur            x6, [fp, #-0x38]
    // 0xbd5294: ldur            x4, [fp, #-0x18]
    // 0xbd5298: mov             x2, x5
    // 0xbd529c: stur            x7, [fp, #-0x10]
    // 0xbd52a0: r1 = Function '<anonymous closure>':.
    //     0xbd52a0: add             x1, PP, #0x53, lsl #12  ; [pp+0x53d48] AnonymousClosure: (0xbd57f8), in [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xbd4660)
    //     0xbd52a4: ldr             x1, [x1, #0xd48]
    // 0xbd52a8: r0 = AllocateClosure()
    //     0xbd52a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd52ac: stur            x0, [fp, #-0x28]
    // 0xbd52b0: r0 = ListView()
    //     0xbd52b0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbd52b4: stur            x0, [fp, #-0x40]
    // 0xbd52b8: r16 = true
    //     0xbd52b8: add             x16, NULL, #0x20  ; true
    // 0xbd52bc: r30 = Instance_NeverScrollableScrollPhysics
    //     0xbd52bc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbd52c0: ldr             lr, [lr, #0x1c8]
    // 0xbd52c4: stp             lr, x16, [SP]
    // 0xbd52c8: mov             x1, x0
    // 0xbd52cc: ldur            x2, [fp, #-0x28]
    // 0xbd52d0: ldur            x3, [fp, #-0x10]
    // 0xbd52d4: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xbd52d4: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xbd52d8: ldr             x4, [x4, #8]
    // 0xbd52dc: r0 = ListView.builder()
    //     0xbd52dc: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbd52e0: ldur            x2, [fp, #-0x30]
    // 0xbd52e4: LoadField: r1 = r2->field_13
    //     0xbd52e4: ldur            w1, [x2, #0x13]
    // 0xbd52e8: DecompressPointer r1
    //     0xbd52e8: add             x1, x1, HEAP, lsl #32
    // 0xbd52ec: r0 = of()
    //     0xbd52ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd52f0: LoadField: r1 = r0->field_87
    //     0xbd52f0: ldur            w1, [x0, #0x87]
    // 0xbd52f4: DecompressPointer r1
    //     0xbd52f4: add             x1, x1, HEAP, lsl #32
    // 0xbd52f8: LoadField: r0 = r1->field_27
    //     0xbd52f8: ldur            w0, [x1, #0x27]
    // 0xbd52fc: DecompressPointer r0
    //     0xbd52fc: add             x0, x0, HEAP, lsl #32
    // 0xbd5300: ldur            x2, [fp, #-0x30]
    // 0xbd5304: stur            x0, [fp, #-0x10]
    // 0xbd5308: LoadField: r1 = r2->field_13
    //     0xbd5308: ldur            w1, [x2, #0x13]
    // 0xbd530c: DecompressPointer r1
    //     0xbd530c: add             x1, x1, HEAP, lsl #32
    // 0xbd5310: r0 = of()
    //     0xbd5310: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd5314: LoadField: r1 = r0->field_5b
    //     0xbd5314: ldur            w1, [x0, #0x5b]
    // 0xbd5318: DecompressPointer r1
    //     0xbd5318: add             x1, x1, HEAP, lsl #32
    // 0xbd531c: r0 = LoadClassIdInstr(r1)
    //     0xbd531c: ldur            x0, [x1, #-1]
    //     0xbd5320: ubfx            x0, x0, #0xc, #0x14
    // 0xbd5324: d0 = 0.700000
    //     0xbd5324: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd5328: ldr             d0, [x17, #0xf48]
    // 0xbd532c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbd532c: sub             lr, x0, #0xffa
    //     0xbd5330: ldr             lr, [x21, lr, lsl #3]
    //     0xbd5334: blr             lr
    // 0xbd5338: r16 = 16.000000
    //     0xbd5338: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbd533c: ldr             x16, [x16, #0x188]
    // 0xbd5340: stp             x0, x16, [SP]
    // 0xbd5344: ldur            x1, [fp, #-0x10]
    // 0xbd5348: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd5348: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd534c: ldr             x4, [x4, #0xaa0]
    // 0xbd5350: r0 = copyWith()
    //     0xbd5350: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd5354: r0 = Accordion()
    //     0xbd5354: bl              #0xba4874  ; AllocateAccordionStub -> Accordion (size=0x3c)
    // 0xbd5358: mov             x1, x0
    // 0xbd535c: ldur            x0, [fp, #-0x20]
    // 0xbd5360: stur            x1, [fp, #-0x10]
    // 0xbd5364: StoreField: r1->field_b = r0
    //     0xbd5364: stur            w0, [x1, #0xb]
    // 0xbd5368: ldur            x0, [fp, #-0x40]
    // 0xbd536c: StoreField: r1->field_13 = r0
    //     0xbd536c: stur            w0, [x1, #0x13]
    // 0xbd5370: r0 = false
    //     0xbd5370: add             x0, NULL, #0x30  ; false
    // 0xbd5374: ArrayStore: r1[0] = r0  ; List_4
    //     0xbd5374: stur            w0, [x1, #0x17]
    // 0xbd5378: r2 = true
    //     0xbd5378: add             x2, NULL, #0x20  ; true
    // 0xbd537c: StoreField: r1->field_1f = r2
    //     0xbd537c: stur            w2, [x1, #0x1f]
    // 0xbd5380: r0 = Padding()
    //     0xbd5380: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd5384: mov             x1, x0
    // 0xbd5388: r0 = Instance_EdgeInsets
    //     0xbd5388: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xbd538c: ldr             x0, [x0, #0xf30]
    // 0xbd5390: StoreField: r1->field_f = r0
    //     0xbd5390: stur            w0, [x1, #0xf]
    // 0xbd5394: ldur            x0, [fp, #-0x10]
    // 0xbd5398: StoreField: r1->field_b = r0
    //     0xbd5398: stur            w0, [x1, #0xb]
    // 0xbd539c: mov             x0, x1
    // 0xbd53a0: ldur            x1, [fp, #-0x18]
    // 0xbd53a4: ArrayStore: r1[16] = r0  ; List_4
    //     0xbd53a4: add             x25, x1, #0x4f
    //     0xbd53a8: str             w0, [x25]
    //     0xbd53ac: tbz             w0, #0, #0xbd53c8
    //     0xbd53b0: ldurb           w16, [x1, #-1]
    //     0xbd53b4: ldurb           w17, [x0, #-1]
    //     0xbd53b8: and             x16, x17, x16, lsr #2
    //     0xbd53bc: tst             x16, HEAP, lsr #32
    //     0xbd53c0: b.eq            #0xbd53c8
    //     0xbd53c4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd53c8: ldur            x0, [fp, #-0x18]
    // 0xbd53cc: r16 = Instance_SizedBox
    //     0xbd53cc: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xbd53d0: ldr             x16, [x16, #0x568]
    // 0xbd53d4: StoreField: r0->field_53 = r16
    //     0xbd53d4: stur            w16, [x0, #0x53]
    // 0xbd53d8: r16 = Instance_Padding
    //     0xbd53d8: add             x16, PP, #0x53, lsl #12  ; [pp+0x53d20] Obj!Padding@d684c1
    //     0xbd53dc: ldr             x16, [x16, #0xd20]
    // 0xbd53e0: StoreField: r0->field_57 = r16
    //     0xbd53e0: stur            w16, [x0, #0x57]
    // 0xbd53e4: r16 = Instance_SizedBox
    //     0xbd53e4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xbd53e8: ldr             x16, [x16, #0x568]
    // 0xbd53ec: StoreField: r0->field_5b = r16
    //     0xbd53ec: stur            w16, [x0, #0x5b]
    // 0xbd53f0: ldur            x1, [fp, #-8]
    // 0xbd53f4: LoadField: r2 = r1->field_13
    //     0xbd53f4: ldur            w2, [x1, #0x13]
    // 0xbd53f8: DecompressPointer r2
    //     0xbd53f8: add             x2, x2, HEAP, lsl #32
    // 0xbd53fc: LoadField: r1 = r2->field_b
    //     0xbd53fc: ldur            w1, [x2, #0xb]
    // 0xbd5400: cbnz            w1, #0xbd540c
    // 0xbd5404: r2 = false
    //     0xbd5404: add             x2, NULL, #0x30  ; false
    // 0xbd5408: b               #0xbd5410
    // 0xbd540c: r2 = true
    //     0xbd540c: add             x2, NULL, #0x20  ; true
    // 0xbd5410: ldur            x3, [fp, #-0x30]
    // 0xbd5414: stur            x2, [fp, #-8]
    // 0xbd5418: LoadField: r1 = r3->field_13
    //     0xbd5418: ldur            w1, [x3, #0x13]
    // 0xbd541c: DecompressPointer r1
    //     0xbd541c: add             x1, x1, HEAP, lsl #32
    // 0xbd5420: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbd5420: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbd5424: r0 = _of()
    //     0xbd5424: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbd5428: LoadField: r1 = r0->field_7
    //     0xbd5428: ldur            w1, [x0, #7]
    // 0xbd542c: DecompressPointer r1
    //     0xbd542c: add             x1, x1, HEAP, lsl #32
    // 0xbd5430: LoadField: d0 = r1->field_7
    //     0xbd5430: ldur            d0, [x1, #7]
    // 0xbd5434: stur            d0, [fp, #-0x48]
    // 0xbd5438: r16 = <EdgeInsets>
    //     0xbd5438: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbd543c: ldr             x16, [x16, #0xda0]
    // 0xbd5440: r30 = Instance_EdgeInsets
    //     0xbd5440: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbd5444: ldr             lr, [lr, #0x1f0]
    // 0xbd5448: stp             lr, x16, [SP]
    // 0xbd544c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbd544c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbd5450: r0 = all()
    //     0xbd5450: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbd5454: ldur            x2, [fp, #-0x30]
    // 0xbd5458: stur            x0, [fp, #-0x10]
    // 0xbd545c: LoadField: r1 = r2->field_13
    //     0xbd545c: ldur            w1, [x2, #0x13]
    // 0xbd5460: DecompressPointer r1
    //     0xbd5460: add             x1, x1, HEAP, lsl #32
    // 0xbd5464: r0 = of()
    //     0xbd5464: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd5468: LoadField: r1 = r0->field_5b
    //     0xbd5468: ldur            w1, [x0, #0x5b]
    // 0xbd546c: DecompressPointer r1
    //     0xbd546c: add             x1, x1, HEAP, lsl #32
    // 0xbd5470: r16 = <Color>
    //     0xbd5470: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbd5474: ldr             x16, [x16, #0xf80]
    // 0xbd5478: stp             x1, x16, [SP]
    // 0xbd547c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbd547c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbd5480: r0 = all()
    //     0xbd5480: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbd5484: stur            x0, [fp, #-0x20]
    // 0xbd5488: r16 = <RoundedRectangleBorder>
    //     0xbd5488: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbd548c: ldr             x16, [x16, #0xf78]
    // 0xbd5490: r30 = Instance_RoundedRectangleBorder
    //     0xbd5490: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbd5494: ldr             lr, [lr, #0xd68]
    // 0xbd5498: stp             lr, x16, [SP]
    // 0xbd549c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbd549c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbd54a0: r0 = all()
    //     0xbd54a0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbd54a4: stur            x0, [fp, #-0x28]
    // 0xbd54a8: r0 = ButtonStyle()
    //     0xbd54a8: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbd54ac: mov             x1, x0
    // 0xbd54b0: ldur            x0, [fp, #-0x20]
    // 0xbd54b4: stur            x1, [fp, #-0x40]
    // 0xbd54b8: StoreField: r1->field_b = r0
    //     0xbd54b8: stur            w0, [x1, #0xb]
    // 0xbd54bc: ldur            x0, [fp, #-0x10]
    // 0xbd54c0: StoreField: r1->field_23 = r0
    //     0xbd54c0: stur            w0, [x1, #0x23]
    // 0xbd54c4: ldur            x0, [fp, #-0x28]
    // 0xbd54c8: StoreField: r1->field_43 = r0
    //     0xbd54c8: stur            w0, [x1, #0x43]
    // 0xbd54cc: r0 = TextButtonThemeData()
    //     0xbd54cc: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbd54d0: mov             x2, x0
    // 0xbd54d4: ldur            x0, [fp, #-0x40]
    // 0xbd54d8: stur            x2, [fp, #-0x10]
    // 0xbd54dc: StoreField: r2->field_7 = r0
    //     0xbd54dc: stur            w0, [x2, #7]
    // 0xbd54e0: ldur            x0, [fp, #-0x30]
    // 0xbd54e4: LoadField: r1 = r0->field_13
    //     0xbd54e4: ldur            w1, [x0, #0x13]
    // 0xbd54e8: DecompressPointer r1
    //     0xbd54e8: add             x1, x1, HEAP, lsl #32
    // 0xbd54ec: r0 = of()
    //     0xbd54ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd54f0: LoadField: r1 = r0->field_87
    //     0xbd54f0: ldur            w1, [x0, #0x87]
    // 0xbd54f4: DecompressPointer r1
    //     0xbd54f4: add             x1, x1, HEAP, lsl #32
    // 0xbd54f8: LoadField: r0 = r1->field_7
    //     0xbd54f8: ldur            w0, [x1, #7]
    // 0xbd54fc: DecompressPointer r0
    //     0xbd54fc: add             x0, x0, HEAP, lsl #32
    // 0xbd5500: r16 = 14.000000
    //     0xbd5500: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd5504: ldr             x16, [x16, #0x1d8]
    // 0xbd5508: r30 = Instance_Color
    //     0xbd5508: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbd550c: stp             lr, x16, [SP]
    // 0xbd5510: mov             x1, x0
    // 0xbd5514: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd5514: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd5518: ldr             x4, [x4, #0xaa0]
    // 0xbd551c: r0 = copyWith()
    //     0xbd551c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd5520: stur            x0, [fp, #-0x20]
    // 0xbd5524: r0 = Text()
    //     0xbd5524: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd5528: mov             x3, x0
    // 0xbd552c: r0 = "SHOW RESULT"
    //     0xbd552c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53d50] "SHOW RESULT"
    //     0xbd5530: ldr             x0, [x0, #0xd50]
    // 0xbd5534: stur            x3, [fp, #-0x28]
    // 0xbd5538: StoreField: r3->field_b = r0
    //     0xbd5538: stur            w0, [x3, #0xb]
    // 0xbd553c: ldur            x0, [fp, #-0x20]
    // 0xbd5540: StoreField: r3->field_13 = r0
    //     0xbd5540: stur            w0, [x3, #0x13]
    // 0xbd5544: ldur            x2, [fp, #-0x30]
    // 0xbd5548: r1 = Function '<anonymous closure>':.
    //     0xbd5548: add             x1, PP, #0x53, lsl #12  ; [pp+0x53d58] AnonymousClosure: (0xa2da14), in [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xbd4660)
    //     0xbd554c: ldr             x1, [x1, #0xd58]
    // 0xbd5550: r0 = AllocateClosure()
    //     0xbd5550: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd5554: stur            x0, [fp, #-0x20]
    // 0xbd5558: r0 = TextButton()
    //     0xbd5558: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbd555c: mov             x1, x0
    // 0xbd5560: ldur            x0, [fp, #-0x20]
    // 0xbd5564: stur            x1, [fp, #-0x30]
    // 0xbd5568: StoreField: r1->field_b = r0
    //     0xbd5568: stur            w0, [x1, #0xb]
    // 0xbd556c: r0 = false
    //     0xbd556c: add             x0, NULL, #0x30  ; false
    // 0xbd5570: StoreField: r1->field_27 = r0
    //     0xbd5570: stur            w0, [x1, #0x27]
    // 0xbd5574: r2 = true
    //     0xbd5574: add             x2, NULL, #0x20  ; true
    // 0xbd5578: StoreField: r1->field_2f = r2
    //     0xbd5578: stur            w2, [x1, #0x2f]
    // 0xbd557c: ldur            x3, [fp, #-0x28]
    // 0xbd5580: StoreField: r1->field_37 = r3
    //     0xbd5580: stur            w3, [x1, #0x37]
    // 0xbd5584: r0 = TextButtonTheme()
    //     0xbd5584: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbd5588: mov             x1, x0
    // 0xbd558c: ldur            x0, [fp, #-0x10]
    // 0xbd5590: stur            x1, [fp, #-0x20]
    // 0xbd5594: StoreField: r1->field_f = r0
    //     0xbd5594: stur            w0, [x1, #0xf]
    // 0xbd5598: ldur            x0, [fp, #-0x30]
    // 0xbd559c: StoreField: r1->field_b = r0
    //     0xbd559c: stur            w0, [x1, #0xb]
    // 0xbd55a0: ldur            d0, [fp, #-0x48]
    // 0xbd55a4: r0 = inline_Allocate_Double()
    //     0xbd55a4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbd55a8: add             x0, x0, #0x10
    //     0xbd55ac: cmp             x2, x0
    //     0xbd55b0: b.ls            #0xbd57e0
    //     0xbd55b4: str             x0, [THR, #0x50]  ; THR::top
    //     0xbd55b8: sub             x0, x0, #0xf
    //     0xbd55bc: movz            x2, #0xe15c
    //     0xbd55c0: movk            x2, #0x3, lsl #16
    //     0xbd55c4: stur            x2, [x0, #-1]
    // 0xbd55c8: StoreField: r0->field_7 = d0
    //     0xbd55c8: stur            d0, [x0, #7]
    // 0xbd55cc: stur            x0, [fp, #-0x10]
    // 0xbd55d0: r0 = SizedBox()
    //     0xbd55d0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbd55d4: mov             x1, x0
    // 0xbd55d8: ldur            x0, [fp, #-0x10]
    // 0xbd55dc: stur            x1, [fp, #-0x28]
    // 0xbd55e0: StoreField: r1->field_f = r0
    //     0xbd55e0: stur            w0, [x1, #0xf]
    // 0xbd55e4: ldur            x0, [fp, #-0x20]
    // 0xbd55e8: StoreField: r1->field_b = r0
    //     0xbd55e8: stur            w0, [x1, #0xb]
    // 0xbd55ec: r0 = Padding()
    //     0xbd55ec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd55f0: mov             x1, x0
    // 0xbd55f4: r0 = Instance_EdgeInsets
    //     0xbd55f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbd55f8: ldr             x0, [x0, #0x1f0]
    // 0xbd55fc: stur            x1, [fp, #-0x10]
    // 0xbd5600: StoreField: r1->field_f = r0
    //     0xbd5600: stur            w0, [x1, #0xf]
    // 0xbd5604: ldur            x0, [fp, #-0x28]
    // 0xbd5608: StoreField: r1->field_b = r0
    //     0xbd5608: stur            w0, [x1, #0xb]
    // 0xbd560c: r0 = Visibility()
    //     0xbd560c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbd5610: mov             x1, x0
    // 0xbd5614: ldur            x0, [fp, #-0x10]
    // 0xbd5618: StoreField: r1->field_b = r0
    //     0xbd5618: stur            w0, [x1, #0xb]
    // 0xbd561c: r0 = Instance_SizedBox
    //     0xbd561c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbd5620: StoreField: r1->field_f = r0
    //     0xbd5620: stur            w0, [x1, #0xf]
    // 0xbd5624: ldur            x0, [fp, #-8]
    // 0xbd5628: StoreField: r1->field_13 = r0
    //     0xbd5628: stur            w0, [x1, #0x13]
    // 0xbd562c: r2 = false
    //     0xbd562c: add             x2, NULL, #0x30  ; false
    // 0xbd5630: ArrayStore: r1[0] = r2  ; List_4
    //     0xbd5630: stur            w2, [x1, #0x17]
    // 0xbd5634: StoreField: r1->field_1b = r2
    //     0xbd5634: stur            w2, [x1, #0x1b]
    // 0xbd5638: StoreField: r1->field_1f = r2
    //     0xbd5638: stur            w2, [x1, #0x1f]
    // 0xbd563c: StoreField: r1->field_23 = r2
    //     0xbd563c: stur            w2, [x1, #0x23]
    // 0xbd5640: StoreField: r1->field_27 = r2
    //     0xbd5640: stur            w2, [x1, #0x27]
    // 0xbd5644: StoreField: r1->field_2b = r2
    //     0xbd5644: stur            w2, [x1, #0x2b]
    // 0xbd5648: mov             x0, x1
    // 0xbd564c: ldur            x1, [fp, #-0x18]
    // 0xbd5650: ArrayStore: r1[20] = r0  ; List_4
    //     0xbd5650: add             x25, x1, #0x5f
    //     0xbd5654: str             w0, [x25]
    //     0xbd5658: tbz             w0, #0, #0xbd5674
    //     0xbd565c: ldurb           w16, [x1, #-1]
    //     0xbd5660: ldurb           w17, [x0, #-1]
    //     0xbd5664: and             x16, x17, x16, lsr #2
    //     0xbd5668: tst             x16, HEAP, lsr #32
    //     0xbd566c: b.eq            #0xbd5674
    //     0xbd5670: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd5674: r1 = <Widget>
    //     0xbd5674: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd5678: r0 = AllocateGrowableArray()
    //     0xbd5678: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd567c: mov             x1, x0
    // 0xbd5680: ldur            x0, [fp, #-0x18]
    // 0xbd5684: stur            x1, [fp, #-8]
    // 0xbd5688: StoreField: r1->field_f = r0
    //     0xbd5688: stur            w0, [x1, #0xf]
    // 0xbd568c: r0 = 42
    //     0xbd568c: movz            x0, #0x2a
    // 0xbd5690: StoreField: r1->field_b = r0
    //     0xbd5690: stur            w0, [x1, #0xb]
    // 0xbd5694: r0 = Column()
    //     0xbd5694: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbd5698: mov             x1, x0
    // 0xbd569c: r0 = Instance_Axis
    //     0xbd569c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbd56a0: stur            x1, [fp, #-0x10]
    // 0xbd56a4: StoreField: r1->field_f = r0
    //     0xbd56a4: stur            w0, [x1, #0xf]
    // 0xbd56a8: r2 = Instance_MainAxisAlignment
    //     0xbd56a8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd56ac: ldr             x2, [x2, #0xa08]
    // 0xbd56b0: StoreField: r1->field_13 = r2
    //     0xbd56b0: stur            w2, [x1, #0x13]
    // 0xbd56b4: r2 = Instance_MainAxisSize
    //     0xbd56b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd56b8: ldr             x2, [x2, #0xa10]
    // 0xbd56bc: ArrayStore: r1[0] = r2  ; List_4
    //     0xbd56bc: stur            w2, [x1, #0x17]
    // 0xbd56c0: r2 = Instance_CrossAxisAlignment
    //     0xbd56c0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd56c4: ldr             x2, [x2, #0xa18]
    // 0xbd56c8: StoreField: r1->field_1b = r2
    //     0xbd56c8: stur            w2, [x1, #0x1b]
    // 0xbd56cc: r2 = Instance_VerticalDirection
    //     0xbd56cc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd56d0: ldr             x2, [x2, #0xa20]
    // 0xbd56d4: StoreField: r1->field_23 = r2
    //     0xbd56d4: stur            w2, [x1, #0x23]
    // 0xbd56d8: r2 = Instance_Clip
    //     0xbd56d8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd56dc: ldr             x2, [x2, #0x38]
    // 0xbd56e0: StoreField: r1->field_2b = r2
    //     0xbd56e0: stur            w2, [x1, #0x2b]
    // 0xbd56e4: StoreField: r1->field_2f = rZR
    //     0xbd56e4: stur            xzr, [x1, #0x2f]
    // 0xbd56e8: ldur            x2, [fp, #-8]
    // 0xbd56ec: StoreField: r1->field_b = r2
    //     0xbd56ec: stur            w2, [x1, #0xb]
    // 0xbd56f0: r0 = SingleChildScrollView()
    //     0xbd56f0: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xbd56f4: mov             x1, x0
    // 0xbd56f8: r0 = Instance_Axis
    //     0xbd56f8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbd56fc: stur            x1, [fp, #-8]
    // 0xbd5700: StoreField: r1->field_b = r0
    //     0xbd5700: stur            w0, [x1, #0xb]
    // 0xbd5704: r0 = false
    //     0xbd5704: add             x0, NULL, #0x30  ; false
    // 0xbd5708: StoreField: r1->field_f = r0
    //     0xbd5708: stur            w0, [x1, #0xf]
    // 0xbd570c: ldur            x2, [fp, #-0x10]
    // 0xbd5710: StoreField: r1->field_23 = r2
    //     0xbd5710: stur            w2, [x1, #0x23]
    // 0xbd5714: r2 = Instance_DragStartBehavior
    //     0xbd5714: ldr             x2, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xbd5718: StoreField: r1->field_27 = r2
    //     0xbd5718: stur            w2, [x1, #0x27]
    // 0xbd571c: r2 = Instance_Clip
    //     0xbd571c: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbd5720: ldr             x2, [x2, #0x7e0]
    // 0xbd5724: StoreField: r1->field_2b = r2
    //     0xbd5724: stur            w2, [x1, #0x2b]
    // 0xbd5728: r2 = Instance_HitTestBehavior
    //     0xbd5728: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xbd572c: ldr             x2, [x2, #0x288]
    // 0xbd5730: StoreField: r1->field_2f = r2
    //     0xbd5730: stur            w2, [x1, #0x2f]
    // 0xbd5734: r0 = SafeArea()
    //     0xbd5734: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xbd5738: mov             x1, x0
    // 0xbd573c: r0 = true
    //     0xbd573c: add             x0, NULL, #0x20  ; true
    // 0xbd5740: stur            x1, [fp, #-0x10]
    // 0xbd5744: StoreField: r1->field_b = r0
    //     0xbd5744: stur            w0, [x1, #0xb]
    // 0xbd5748: StoreField: r1->field_f = r0
    //     0xbd5748: stur            w0, [x1, #0xf]
    // 0xbd574c: StoreField: r1->field_13 = r0
    //     0xbd574c: stur            w0, [x1, #0x13]
    // 0xbd5750: ArrayStore: r1[0] = r0  ; List_4
    //     0xbd5750: stur            w0, [x1, #0x17]
    // 0xbd5754: r2 = Instance_EdgeInsets
    //     0xbd5754: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xbd5758: StoreField: r1->field_1b = r2
    //     0xbd5758: stur            w2, [x1, #0x1b]
    // 0xbd575c: r2 = false
    //     0xbd575c: add             x2, NULL, #0x30  ; false
    // 0xbd5760: StoreField: r1->field_1f = r2
    //     0xbd5760: stur            w2, [x1, #0x1f]
    // 0xbd5764: ldur            x3, [fp, #-8]
    // 0xbd5768: StoreField: r1->field_23 = r3
    //     0xbd5768: stur            w3, [x1, #0x23]
    // 0xbd576c: r0 = Scaffold()
    //     0xbd576c: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xbd5770: mov             x2, x0
    // 0xbd5774: ldur            x0, [fp, #-0x10]
    // 0xbd5778: stur            x2, [fp, #-8]
    // 0xbd577c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd577c: stur            w0, [x2, #0x17]
    // 0xbd5780: r0 = Instance_Color
    //     0xbd5780: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbd5784: StoreField: r2->field_33 = r0
    //     0xbd5784: stur            w0, [x2, #0x33]
    // 0xbd5788: r0 = true
    //     0xbd5788: add             x0, NULL, #0x20  ; true
    // 0xbd578c: StoreField: r2->field_43 = r0
    //     0xbd578c: stur            w0, [x2, #0x43]
    // 0xbd5790: r1 = false
    //     0xbd5790: add             x1, NULL, #0x30  ; false
    // 0xbd5794: StoreField: r2->field_b = r1
    //     0xbd5794: stur            w1, [x2, #0xb]
    // 0xbd5798: StoreField: r2->field_f = r1
    //     0xbd5798: stur            w1, [x2, #0xf]
    // 0xbd579c: r1 = <SystemUiOverlayStyle>
    //     0xbd579c: ldr             x1, [PP, #0x2848]  ; [pp+0x2848] TypeArguments: <SystemUiOverlayStyle>
    // 0xbd57a0: r0 = AnnotatedRegion()
    //     0xbd57a0: bl              #0xa2da08  ; AllocateAnnotatedRegionStub -> AnnotatedRegion<X0> (size=0x1c)
    // 0xbd57a4: ldur            x1, [fp, #-0x38]
    // 0xbd57a8: StoreField: r0->field_13 = r1
    //     0xbd57a8: stur            w1, [x0, #0x13]
    // 0xbd57ac: r1 = true
    //     0xbd57ac: add             x1, NULL, #0x20  ; true
    // 0xbd57b0: ArrayStore: r0[0] = r1  ; List_4
    //     0xbd57b0: stur            w1, [x0, #0x17]
    // 0xbd57b4: ldur            x1, [fp, #-8]
    // 0xbd57b8: StoreField: r0->field_b = r1
    //     0xbd57b8: stur            w1, [x0, #0xb]
    // 0xbd57bc: LeaveFrame
    //     0xbd57bc: mov             SP, fp
    //     0xbd57c0: ldp             fp, lr, [SP], #0x10
    // 0xbd57c4: ret
    //     0xbd57c4: ret             
    // 0xbd57c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd57c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd57cc: b               #0xbd4680
    // 0xbd57d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd57d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd57d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd57d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd57d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd57d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd57dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd57dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd57e0: SaveReg d0
    //     0xbd57e0: str             q0, [SP, #-0x10]!
    // 0xbd57e4: SaveReg r1
    //     0xbd57e4: str             x1, [SP, #-8]!
    // 0xbd57e8: r0 = AllocateDouble()
    //     0xbd57e8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbd57ec: RestoreReg r1
    //     0xbd57ec: ldr             x1, [SP], #8
    // 0xbd57f0: RestoreReg d0
    //     0xbd57f0: ldr             q0, [SP], #0x10
    // 0xbd57f4: b               #0xbd55c8
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbd57f8, size: 0x47c
    // 0xbd57f8: EnterFrame
    //     0xbd57f8: stp             fp, lr, [SP, #-0x10]!
    //     0xbd57fc: mov             fp, SP
    // 0xbd5800: AllocStack(0x40)
    //     0xbd5800: sub             SP, SP, #0x40
    // 0xbd5804: SetupParameters()
    //     0xbd5804: ldr             x0, [fp, #0x20]
    //     0xbd5808: ldur            w4, [x0, #0x17]
    //     0xbd580c: add             x4, x4, HEAP, lsl #32
    //     0xbd5810: stur            x4, [fp, #-0x10]
    // 0xbd5814: CheckStackOverflow
    //     0xbd5814: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd5818: cmp             SP, x16
    //     0xbd581c: b.ls            #0xbd5c50
    // 0xbd5820: LoadField: r1 = r4->field_f
    //     0xbd5820: ldur            w1, [x4, #0xf]
    // 0xbd5824: DecompressPointer r1
    //     0xbd5824: add             x1, x1, HEAP, lsl #32
    // 0xbd5828: LoadField: r0 = r1->field_b
    //     0xbd5828: ldur            w0, [x1, #0xb]
    // 0xbd582c: DecompressPointer r0
    //     0xbd582c: add             x0, x0, HEAP, lsl #32
    // 0xbd5830: cmp             w0, NULL
    // 0xbd5834: b.eq            #0xbd5c58
    // 0xbd5838: LoadField: r2 = r0->field_b
    //     0xbd5838: ldur            w2, [x0, #0xb]
    // 0xbd583c: DecompressPointer r2
    //     0xbd583c: add             x2, x2, HEAP, lsl #32
    // 0xbd5840: ldr             x0, [fp, #0x10]
    // 0xbd5844: r6 = LoadInt32Instr(r0)
    //     0xbd5844: sbfx            x6, x0, #1, #0x1f
    //     0xbd5848: tbz             w0, #0, #0xbd5850
    //     0xbd584c: ldur            x6, [x0, #7]
    // 0xbd5850: stur            x6, [fp, #-8]
    // 0xbd5854: ldr             x16, [fp, #0x18]
    // 0xbd5858: str             x16, [SP]
    // 0xbd585c: mov             x5, x6
    // 0xbd5860: r3 = Instance_ItemFilterType
    //     0xbd5860: add             x3, PP, #0x53, lsl #12  ; [pp+0x53d68] Obj!ItemFilterType@d752a1
    //     0xbd5864: ldr             x3, [x3, #0xd68]
    // 0xbd5868: r4 = const [0, 0x5, 0x1, 0x4, context, 0x4, null]
    //     0xbd5868: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d70] List(7) [0, 0x5, 0x1, 0x4, "context", 0x4, Null]
    //     0xbd586c: ldr             x4, [x4, #0xd70]
    // 0xbd5870: r0 = _productBuildItem()
    //     0xbd5870: bl              #0xbd5c74  ; [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::_productBuildItem
    // 0xbd5874: mov             x3, x0
    // 0xbd5878: ldur            x2, [fp, #-0x10]
    // 0xbd587c: stur            x3, [fp, #-0x18]
    // 0xbd5880: LoadField: r0 = r2->field_f
    //     0xbd5880: ldur            w0, [x2, #0xf]
    // 0xbd5884: DecompressPointer r0
    //     0xbd5884: add             x0, x0, HEAP, lsl #32
    // 0xbd5888: LoadField: r1 = r0->field_b
    //     0xbd5888: ldur            w1, [x0, #0xb]
    // 0xbd588c: DecompressPointer r1
    //     0xbd588c: add             x1, x1, HEAP, lsl #32
    // 0xbd5890: cmp             w1, NULL
    // 0xbd5894: b.eq            #0xbd5c5c
    // 0xbd5898: LoadField: r0 = r1->field_b
    //     0xbd5898: ldur            w0, [x1, #0xb]
    // 0xbd589c: DecompressPointer r0
    //     0xbd589c: add             x0, x0, HEAP, lsl #32
    // 0xbd58a0: cmp             w0, NULL
    // 0xbd58a4: b.ne            #0xbd58b0
    // 0xbd58a8: r0 = Null
    //     0xbd58a8: mov             x0, NULL
    // 0xbd58ac: b               #0xbd592c
    // 0xbd58b0: LoadField: r4 = r0->field_13
    //     0xbd58b0: ldur            w4, [x0, #0x13]
    // 0xbd58b4: DecompressPointer r4
    //     0xbd58b4: add             x4, x4, HEAP, lsl #32
    // 0xbd58b8: cmp             w4, NULL
    // 0xbd58bc: b.ne            #0xbd58c8
    // 0xbd58c0: r0 = Null
    //     0xbd58c0: mov             x0, NULL
    // 0xbd58c4: b               #0xbd592c
    // 0xbd58c8: ldur            x5, [fp, #-8]
    // 0xbd58cc: LoadField: r0 = r4->field_b
    //     0xbd58cc: ldur            w0, [x4, #0xb]
    // 0xbd58d0: r1 = LoadInt32Instr(r0)
    //     0xbd58d0: sbfx            x1, x0, #1, #0x1f
    // 0xbd58d4: mov             x0, x1
    // 0xbd58d8: mov             x1, x5
    // 0xbd58dc: cmp             x1, x0
    // 0xbd58e0: b.hs            #0xbd5c60
    // 0xbd58e4: LoadField: r0 = r4->field_f
    //     0xbd58e4: ldur            w0, [x4, #0xf]
    // 0xbd58e8: DecompressPointer r0
    //     0xbd58e8: add             x0, x0, HEAP, lsl #32
    // 0xbd58ec: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbd58ec: add             x16, x0, x5, lsl #2
    //     0xbd58f0: ldur            w1, [x16, #0xf]
    // 0xbd58f4: DecompressPointer r1
    //     0xbd58f4: add             x1, x1, HEAP, lsl #32
    // 0xbd58f8: LoadField: r0 = r1->field_7
    //     0xbd58f8: ldur            w0, [x1, #7]
    // 0xbd58fc: DecompressPointer r0
    //     0xbd58fc: add             x0, x0, HEAP, lsl #32
    // 0xbd5900: r1 = 60
    //     0xbd5900: movz            x1, #0x3c
    // 0xbd5904: branchIfSmi(r0, 0xbd5910)
    //     0xbd5904: tbz             w0, #0, #0xbd5910
    // 0xbd5908: r1 = LoadClassIdInstr(r0)
    //     0xbd5908: ldur            x1, [x0, #-1]
    //     0xbd590c: ubfx            x1, x1, #0xc, #0x14
    // 0xbd5910: str             x0, [SP]
    // 0xbd5914: mov             x0, x1
    // 0xbd5918: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbd5918: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbd591c: r0 = GDT[cid_x0 + 0x2700]()
    //     0xbd591c: movz            x17, #0x2700
    //     0xbd5920: add             lr, x0, x17
    //     0xbd5924: ldr             lr, [x21, lr, lsl #3]
    //     0xbd5928: blr             lr
    // 0xbd592c: cmp             w0, NULL
    // 0xbd5930: b.ne            #0xbd593c
    // 0xbd5934: r3 = ""
    //     0xbd5934: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd5938: b               #0xbd5940
    // 0xbd593c: mov             x3, x0
    // 0xbd5940: ldur            x0, [fp, #-0x10]
    // 0xbd5944: stur            x3, [fp, #-0x20]
    // 0xbd5948: r1 = Null
    //     0xbd5948: mov             x1, NULL
    // 0xbd594c: r2 = 6
    //     0xbd594c: movz            x2, #0x6
    // 0xbd5950: r0 = AllocateArray()
    //     0xbd5950: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd5954: mov             x2, x0
    // 0xbd5958: ldur            x0, [fp, #-0x20]
    // 0xbd595c: StoreField: r2->field_f = r0
    //     0xbd595c: stur            w0, [x2, #0xf]
    // 0xbd5960: r16 = " - "
    //     0xbd5960: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0xbd5964: ldr             x16, [x16, #0xc08]
    // 0xbd5968: StoreField: r2->field_13 = r16
    //     0xbd5968: stur            w16, [x2, #0x13]
    // 0xbd596c: ldur            x3, [fp, #-0x10]
    // 0xbd5970: LoadField: r0 = r3->field_f
    //     0xbd5970: ldur            w0, [x3, #0xf]
    // 0xbd5974: DecompressPointer r0
    //     0xbd5974: add             x0, x0, HEAP, lsl #32
    // 0xbd5978: LoadField: r1 = r0->field_b
    //     0xbd5978: ldur            w1, [x0, #0xb]
    // 0xbd597c: DecompressPointer r1
    //     0xbd597c: add             x1, x1, HEAP, lsl #32
    // 0xbd5980: cmp             w1, NULL
    // 0xbd5984: b.eq            #0xbd5c64
    // 0xbd5988: LoadField: r0 = r1->field_b
    //     0xbd5988: ldur            w0, [x1, #0xb]
    // 0xbd598c: DecompressPointer r0
    //     0xbd598c: add             x0, x0, HEAP, lsl #32
    // 0xbd5990: cmp             w0, NULL
    // 0xbd5994: b.ne            #0xbd59a4
    // 0xbd5998: ldur            x5, [fp, #-8]
    // 0xbd599c: r0 = Null
    //     0xbd599c: mov             x0, NULL
    // 0xbd59a0: b               #0xbd59f8
    // 0xbd59a4: LoadField: r4 = r0->field_13
    //     0xbd59a4: ldur            w4, [x0, #0x13]
    // 0xbd59a8: DecompressPointer r4
    //     0xbd59a8: add             x4, x4, HEAP, lsl #32
    // 0xbd59ac: cmp             w4, NULL
    // 0xbd59b0: b.ne            #0xbd59c0
    // 0xbd59b4: ldur            x5, [fp, #-8]
    // 0xbd59b8: r0 = Null
    //     0xbd59b8: mov             x0, NULL
    // 0xbd59bc: b               #0xbd59f8
    // 0xbd59c0: ldur            x5, [fp, #-8]
    // 0xbd59c4: LoadField: r0 = r4->field_b
    //     0xbd59c4: ldur            w0, [x4, #0xb]
    // 0xbd59c8: r1 = LoadInt32Instr(r0)
    //     0xbd59c8: sbfx            x1, x0, #1, #0x1f
    // 0xbd59cc: mov             x0, x1
    // 0xbd59d0: mov             x1, x5
    // 0xbd59d4: cmp             x1, x0
    // 0xbd59d8: b.hs            #0xbd5c68
    // 0xbd59dc: LoadField: r0 = r4->field_f
    //     0xbd59dc: ldur            w0, [x4, #0xf]
    // 0xbd59e0: DecompressPointer r0
    //     0xbd59e0: add             x0, x0, HEAP, lsl #32
    // 0xbd59e4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbd59e4: add             x16, x0, x5, lsl #2
    //     0xbd59e8: ldur            w1, [x16, #0xf]
    // 0xbd59ec: DecompressPointer r1
    //     0xbd59ec: add             x1, x1, HEAP, lsl #32
    // 0xbd59f0: LoadField: r0 = r1->field_b
    //     0xbd59f0: ldur            w0, [x1, #0xb]
    // 0xbd59f4: DecompressPointer r0
    //     0xbd59f4: add             x0, x0, HEAP, lsl #32
    // 0xbd59f8: cmp             w0, NULL
    // 0xbd59fc: b.ne            #0xbd5a08
    // 0xbd5a00: r0 = "above"
    //     0xbd5a00: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bc10] "above"
    //     0xbd5a04: ldr             x0, [x0, #0xc10]
    // 0xbd5a08: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd5a08: stur            w0, [x2, #0x17]
    // 0xbd5a0c: str             x2, [SP]
    // 0xbd5a10: r0 = _interpolate()
    //     0xbd5a10: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbd5a14: ldr             x1, [fp, #0x18]
    // 0xbd5a18: stur            x0, [fp, #-0x20]
    // 0xbd5a1c: r0 = of()
    //     0xbd5a1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd5a20: LoadField: r1 = r0->field_87
    //     0xbd5a20: ldur            w1, [x0, #0x87]
    // 0xbd5a24: DecompressPointer r1
    //     0xbd5a24: add             x1, x1, HEAP, lsl #32
    // 0xbd5a28: LoadField: r0 = r1->field_2b
    //     0xbd5a28: ldur            w0, [x1, #0x2b]
    // 0xbd5a2c: DecompressPointer r0
    //     0xbd5a2c: add             x0, x0, HEAP, lsl #32
    // 0xbd5a30: r16 = 14.000000
    //     0xbd5a30: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd5a34: ldr             x16, [x16, #0x1d8]
    // 0xbd5a38: r30 = Instance_Color
    //     0xbd5a38: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd5a3c: stp             lr, x16, [SP]
    // 0xbd5a40: mov             x1, x0
    // 0xbd5a44: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd5a44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd5a48: ldr             x4, [x4, #0xaa0]
    // 0xbd5a4c: r0 = copyWith()
    //     0xbd5a4c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd5a50: stur            x0, [fp, #-0x28]
    // 0xbd5a54: r0 = Text()
    //     0xbd5a54: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd5a58: mov             x2, x0
    // 0xbd5a5c: ldur            x0, [fp, #-0x20]
    // 0xbd5a60: stur            x2, [fp, #-0x30]
    // 0xbd5a64: StoreField: r2->field_b = r0
    //     0xbd5a64: stur            w0, [x2, #0xb]
    // 0xbd5a68: ldur            x0, [fp, #-0x28]
    // 0xbd5a6c: StoreField: r2->field_13 = r0
    //     0xbd5a6c: stur            w0, [x2, #0x13]
    // 0xbd5a70: ldur            x0, [fp, #-0x10]
    // 0xbd5a74: LoadField: r1 = r0->field_f
    //     0xbd5a74: ldur            w1, [x0, #0xf]
    // 0xbd5a78: DecompressPointer r1
    //     0xbd5a78: add             x1, x1, HEAP, lsl #32
    // 0xbd5a7c: LoadField: r0 = r1->field_b
    //     0xbd5a7c: ldur            w0, [x1, #0xb]
    // 0xbd5a80: DecompressPointer r0
    //     0xbd5a80: add             x0, x0, HEAP, lsl #32
    // 0xbd5a84: cmp             w0, NULL
    // 0xbd5a88: b.eq            #0xbd5c6c
    // 0xbd5a8c: LoadField: r1 = r0->field_b
    //     0xbd5a8c: ldur            w1, [x0, #0xb]
    // 0xbd5a90: DecompressPointer r1
    //     0xbd5a90: add             x1, x1, HEAP, lsl #32
    // 0xbd5a94: cmp             w1, NULL
    // 0xbd5a98: b.ne            #0xbd5aa4
    // 0xbd5a9c: r0 = Null
    //     0xbd5a9c: mov             x0, NULL
    // 0xbd5aa0: b               #0xbd5b20
    // 0xbd5aa4: LoadField: r3 = r1->field_13
    //     0xbd5aa4: ldur            w3, [x1, #0x13]
    // 0xbd5aa8: DecompressPointer r3
    //     0xbd5aa8: add             x3, x3, HEAP, lsl #32
    // 0xbd5aac: cmp             w3, NULL
    // 0xbd5ab0: b.ne            #0xbd5abc
    // 0xbd5ab4: r0 = Null
    //     0xbd5ab4: mov             x0, NULL
    // 0xbd5ab8: b               #0xbd5b20
    // 0xbd5abc: ldur            x4, [fp, #-8]
    // 0xbd5ac0: LoadField: r0 = r3->field_b
    //     0xbd5ac0: ldur            w0, [x3, #0xb]
    // 0xbd5ac4: r1 = LoadInt32Instr(r0)
    //     0xbd5ac4: sbfx            x1, x0, #1, #0x1f
    // 0xbd5ac8: mov             x0, x1
    // 0xbd5acc: mov             x1, x4
    // 0xbd5ad0: cmp             x1, x0
    // 0xbd5ad4: b.hs            #0xbd5c70
    // 0xbd5ad8: LoadField: r0 = r3->field_f
    //     0xbd5ad8: ldur            w0, [x3, #0xf]
    // 0xbd5adc: DecompressPointer r0
    //     0xbd5adc: add             x0, x0, HEAP, lsl #32
    // 0xbd5ae0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbd5ae0: add             x16, x0, x4, lsl #2
    //     0xbd5ae4: ldur            w1, [x16, #0xf]
    // 0xbd5ae8: DecompressPointer r1
    //     0xbd5ae8: add             x1, x1, HEAP, lsl #32
    // 0xbd5aec: LoadField: r0 = r1->field_f
    //     0xbd5aec: ldur            w0, [x1, #0xf]
    // 0xbd5af0: DecompressPointer r0
    //     0xbd5af0: add             x0, x0, HEAP, lsl #32
    // 0xbd5af4: r1 = 60
    //     0xbd5af4: movz            x1, #0x3c
    // 0xbd5af8: branchIfSmi(r0, 0xbd5b04)
    //     0xbd5af8: tbz             w0, #0, #0xbd5b04
    // 0xbd5afc: r1 = LoadClassIdInstr(r0)
    //     0xbd5afc: ldur            x1, [x0, #-1]
    //     0xbd5b00: ubfx            x1, x1, #0xc, #0x14
    // 0xbd5b04: str             x0, [SP]
    // 0xbd5b08: mov             x0, x1
    // 0xbd5b0c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbd5b0c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbd5b10: r0 = GDT[cid_x0 + 0x2700]()
    //     0xbd5b10: movz            x17, #0x2700
    //     0xbd5b14: add             lr, x0, x17
    //     0xbd5b18: ldr             lr, [x21, lr, lsl #3]
    //     0xbd5b1c: blr             lr
    // 0xbd5b20: cmp             w0, NULL
    // 0xbd5b24: b.ne            #0xbd5b30
    // 0xbd5b28: r3 = ""
    //     0xbd5b28: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd5b2c: b               #0xbd5b34
    // 0xbd5b30: mov             x3, x0
    // 0xbd5b34: ldur            x2, [fp, #-0x18]
    // 0xbd5b38: ldur            x0, [fp, #-0x30]
    // 0xbd5b3c: ldr             x1, [fp, #0x18]
    // 0xbd5b40: stur            x3, [fp, #-0x10]
    // 0xbd5b44: r0 = of()
    //     0xbd5b44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd5b48: LoadField: r1 = r0->field_87
    //     0xbd5b48: ldur            w1, [x0, #0x87]
    // 0xbd5b4c: DecompressPointer r1
    //     0xbd5b4c: add             x1, x1, HEAP, lsl #32
    // 0xbd5b50: LoadField: r0 = r1->field_2b
    //     0xbd5b50: ldur            w0, [x1, #0x2b]
    // 0xbd5b54: DecompressPointer r0
    //     0xbd5b54: add             x0, x0, HEAP, lsl #32
    // 0xbd5b58: r16 = 14.000000
    //     0xbd5b58: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd5b5c: ldr             x16, [x16, #0x1d8]
    // 0xbd5b60: r30 = Instance_Color
    //     0xbd5b60: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd5b64: stp             lr, x16, [SP]
    // 0xbd5b68: mov             x1, x0
    // 0xbd5b6c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd5b6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd5b70: ldr             x4, [x4, #0xaa0]
    // 0xbd5b74: r0 = copyWith()
    //     0xbd5b74: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd5b78: stur            x0, [fp, #-0x20]
    // 0xbd5b7c: r0 = Text()
    //     0xbd5b7c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd5b80: mov             x3, x0
    // 0xbd5b84: ldur            x0, [fp, #-0x10]
    // 0xbd5b88: stur            x3, [fp, #-0x28]
    // 0xbd5b8c: StoreField: r3->field_b = r0
    //     0xbd5b8c: stur            w0, [x3, #0xb]
    // 0xbd5b90: ldur            x0, [fp, #-0x20]
    // 0xbd5b94: StoreField: r3->field_13 = r0
    //     0xbd5b94: stur            w0, [x3, #0x13]
    // 0xbd5b98: r1 = Null
    //     0xbd5b98: mov             x1, NULL
    // 0xbd5b9c: r2 = 8
    //     0xbd5b9c: movz            x2, #0x8
    // 0xbd5ba0: r0 = AllocateArray()
    //     0xbd5ba0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd5ba4: mov             x2, x0
    // 0xbd5ba8: ldur            x0, [fp, #-0x18]
    // 0xbd5bac: stur            x2, [fp, #-0x10]
    // 0xbd5bb0: StoreField: r2->field_f = r0
    //     0xbd5bb0: stur            w0, [x2, #0xf]
    // 0xbd5bb4: ldur            x0, [fp, #-0x30]
    // 0xbd5bb8: StoreField: r2->field_13 = r0
    //     0xbd5bb8: stur            w0, [x2, #0x13]
    // 0xbd5bbc: r16 = Instance_Spacer
    //     0xbd5bbc: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbd5bc0: ldr             x16, [x16, #0xf0]
    // 0xbd5bc4: ArrayStore: r2[0] = r16  ; List_4
    //     0xbd5bc4: stur            w16, [x2, #0x17]
    // 0xbd5bc8: ldur            x0, [fp, #-0x28]
    // 0xbd5bcc: StoreField: r2->field_1b = r0
    //     0xbd5bcc: stur            w0, [x2, #0x1b]
    // 0xbd5bd0: r1 = <Widget>
    //     0xbd5bd0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd5bd4: r0 = AllocateGrowableArray()
    //     0xbd5bd4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd5bd8: mov             x1, x0
    // 0xbd5bdc: ldur            x0, [fp, #-0x10]
    // 0xbd5be0: stur            x1, [fp, #-0x18]
    // 0xbd5be4: StoreField: r1->field_f = r0
    //     0xbd5be4: stur            w0, [x1, #0xf]
    // 0xbd5be8: r0 = 8
    //     0xbd5be8: movz            x0, #0x8
    // 0xbd5bec: StoreField: r1->field_b = r0
    //     0xbd5bec: stur            w0, [x1, #0xb]
    // 0xbd5bf0: r0 = Row()
    //     0xbd5bf0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbd5bf4: r1 = Instance_Axis
    //     0xbd5bf4: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd5bf8: StoreField: r0->field_f = r1
    //     0xbd5bf8: stur            w1, [x0, #0xf]
    // 0xbd5bfc: r1 = Instance_MainAxisAlignment
    //     0xbd5bfc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd5c00: ldr             x1, [x1, #0xa08]
    // 0xbd5c04: StoreField: r0->field_13 = r1
    //     0xbd5c04: stur            w1, [x0, #0x13]
    // 0xbd5c08: r1 = Instance_MainAxisSize
    //     0xbd5c08: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd5c0c: ldr             x1, [x1, #0xa10]
    // 0xbd5c10: ArrayStore: r0[0] = r1  ; List_4
    //     0xbd5c10: stur            w1, [x0, #0x17]
    // 0xbd5c14: r1 = Instance_CrossAxisAlignment
    //     0xbd5c14: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd5c18: ldr             x1, [x1, #0xa18]
    // 0xbd5c1c: StoreField: r0->field_1b = r1
    //     0xbd5c1c: stur            w1, [x0, #0x1b]
    // 0xbd5c20: r1 = Instance_VerticalDirection
    //     0xbd5c20: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd5c24: ldr             x1, [x1, #0xa20]
    // 0xbd5c28: StoreField: r0->field_23 = r1
    //     0xbd5c28: stur            w1, [x0, #0x23]
    // 0xbd5c2c: r1 = Instance_Clip
    //     0xbd5c2c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd5c30: ldr             x1, [x1, #0x38]
    // 0xbd5c34: StoreField: r0->field_2b = r1
    //     0xbd5c34: stur            w1, [x0, #0x2b]
    // 0xbd5c38: StoreField: r0->field_2f = rZR
    //     0xbd5c38: stur            xzr, [x0, #0x2f]
    // 0xbd5c3c: ldur            x1, [fp, #-0x18]
    // 0xbd5c40: StoreField: r0->field_b = r1
    //     0xbd5c40: stur            w1, [x0, #0xb]
    // 0xbd5c44: LeaveFrame
    //     0xbd5c44: mov             SP, fp
    //     0xbd5c48: ldp             fp, lr, [SP], #0x10
    // 0xbd5c4c: ret
    //     0xbd5c4c: ret             
    // 0xbd5c50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd5c50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd5c54: b               #0xbd5820
    // 0xbd5c58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd5c58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd5c5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd5c5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd5c60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd5c60: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd5c64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd5c64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd5c68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd5c68: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd5c6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd5c6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd5c70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd5c70: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _productBuildItem(/* No info */) {
    // ** addr: 0xbd5c74, size: 0x75c
    // 0xbd5c74: EnterFrame
    //     0xbd5c74: stp             fp, lr, [SP, #-0x10]!
    //     0xbd5c78: mov             fp, SP
    // 0xbd5c7c: AllocStack(0x40)
    //     0xbd5c7c: sub             SP, SP, #0x40
    // 0xbd5c80: SetupParameters(_CollectionFilterState this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r1, fp-0x28 */, {dynamic context = Null /* r4, fp-0x8 */})
    //     0xbd5c80: mov             x0, x1
    //     0xbd5c84: stur            x1, [fp, #-0x10]
    //     0xbd5c88: mov             x1, x5
    //     0xbd5c8c: stur            x2, [fp, #-0x18]
    //     0xbd5c90: stur            x3, [fp, #-0x20]
    //     0xbd5c94: stur            x5, [fp, #-0x28]
    //     0xbd5c98: ldur            w5, [x4, #0x13]
    //     0xbd5c9c: ldur            w6, [x4, #0x1f]
    //     0xbd5ca0: add             x6, x6, HEAP, lsl #32
    //     0xbd5ca4: ldr             x16, [PP, #0x4ac8]  ; [pp+0x4ac8] "context"
    //     0xbd5ca8: cmp             w6, w16
    //     0xbd5cac: b.ne            #0xbd5ccc
    //     0xbd5cb0: ldur            w6, [x4, #0x23]
    //     0xbd5cb4: add             x6, x6, HEAP, lsl #32
    //     0xbd5cb8: sub             w4, w5, w6
    //     0xbd5cbc: add             x5, fp, w4, sxtw #2
    //     0xbd5cc0: ldr             x5, [x5, #8]
    //     0xbd5cc4: mov             x4, x5
    //     0xbd5cc8: b               #0xbd5cd0
    //     0xbd5ccc: mov             x4, NULL
    //     0xbd5cd0: stur            x4, [fp, #-8]
    // 0xbd5cd4: CheckStackOverflow
    //     0xbd5cd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd5cd8: cmp             SP, x16
    //     0xbd5cdc: b.ls            #0xbd639c
    // 0xbd5ce0: r1 = 3
    //     0xbd5ce0: movz            x1, #0x3
    // 0xbd5ce4: r0 = AllocateContext()
    //     0xbd5ce4: bl              #0x16f6108  ; AllocateContextStub
    // 0xbd5ce8: mov             x4, x0
    // 0xbd5cec: ldur            x3, [fp, #-0x10]
    // 0xbd5cf0: stur            x4, [fp, #-0x30]
    // 0xbd5cf4: StoreField: r4->field_f = r3
    //     0xbd5cf4: stur            w3, [x4, #0xf]
    // 0xbd5cf8: ldur            x0, [fp, #-0x20]
    // 0xbd5cfc: StoreField: r4->field_13 = r0
    //     0xbd5cfc: stur            w0, [x4, #0x13]
    // 0xbd5d00: r1 = ""
    //     0xbd5d00: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd5d04: ArrayStore: r4[0] = r1  ; List_4
    //     0xbd5d04: stur            w1, [x4, #0x17]
    // 0xbd5d08: LoadField: r1 = r0->field_7
    //     0xbd5d08: ldur            x1, [x0, #7]
    // 0xbd5d0c: cmp             x1, #1
    // 0xbd5d10: b.gt            #0xbd5f50
    // 0xbd5d14: cmp             x1, #0
    // 0xbd5d18: b.gt            #0xbd5e38
    // 0xbd5d1c: ldur            x5, [fp, #-0x18]
    // 0xbd5d20: LoadField: r2 = r3->field_13
    //     0xbd5d20: ldur            w2, [x3, #0x13]
    // 0xbd5d24: DecompressPointer r2
    //     0xbd5d24: add             x2, x2, HEAP, lsl #32
    // 0xbd5d28: cmp             w5, NULL
    // 0xbd5d2c: b.ne            #0xbd5d3c
    // 0xbd5d30: ldur            x6, [fp, #-0x28]
    // 0xbd5d34: r0 = Null
    //     0xbd5d34: mov             x0, NULL
    // 0xbd5d38: b               #0xbd5d90
    // 0xbd5d3c: LoadField: r3 = r5->field_7
    //     0xbd5d3c: ldur            w3, [x5, #7]
    // 0xbd5d40: DecompressPointer r3
    //     0xbd5d40: add             x3, x3, HEAP, lsl #32
    // 0xbd5d44: cmp             w3, NULL
    // 0xbd5d48: b.ne            #0xbd5d58
    // 0xbd5d4c: ldur            x6, [fp, #-0x28]
    // 0xbd5d50: r0 = Null
    //     0xbd5d50: mov             x0, NULL
    // 0xbd5d54: b               #0xbd5d90
    // 0xbd5d58: ldur            x6, [fp, #-0x28]
    // 0xbd5d5c: LoadField: r0 = r3->field_b
    //     0xbd5d5c: ldur            w0, [x3, #0xb]
    // 0xbd5d60: r1 = LoadInt32Instr(r0)
    //     0xbd5d60: sbfx            x1, x0, #1, #0x1f
    // 0xbd5d64: mov             x0, x1
    // 0xbd5d68: mov             x1, x6
    // 0xbd5d6c: cmp             x1, x0
    // 0xbd5d70: b.hs            #0xbd63a4
    // 0xbd5d74: LoadField: r0 = r3->field_f
    //     0xbd5d74: ldur            w0, [x3, #0xf]
    // 0xbd5d78: DecompressPointer r0
    //     0xbd5d78: add             x0, x0, HEAP, lsl #32
    // 0xbd5d7c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbd5d7c: add             x16, x0, x6, lsl #2
    //     0xbd5d80: ldur            w1, [x16, #0xf]
    // 0xbd5d84: DecompressPointer r1
    //     0xbd5d84: add             x1, x1, HEAP, lsl #32
    // 0xbd5d88: LoadField: r0 = r1->field_7
    //     0xbd5d88: ldur            w0, [x1, #7]
    // 0xbd5d8c: DecompressPointer r0
    //     0xbd5d8c: add             x0, x0, HEAP, lsl #32
    // 0xbd5d90: mov             x1, x2
    // 0xbd5d94: mov             x2, x0
    // 0xbd5d98: r0 = contains()
    //     0xbd5d98: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xbd5d9c: mov             x2, x0
    // 0xbd5da0: ldur            x4, [fp, #-0x18]
    // 0xbd5da4: cmp             w4, NULL
    // 0xbd5da8: b.ne            #0xbd5db4
    // 0xbd5dac: r0 = Null
    //     0xbd5dac: mov             x0, NULL
    // 0xbd5db0: b               #0xbd5e04
    // 0xbd5db4: LoadField: r3 = r4->field_7
    //     0xbd5db4: ldur            w3, [x4, #7]
    // 0xbd5db8: DecompressPointer r3
    //     0xbd5db8: add             x3, x3, HEAP, lsl #32
    // 0xbd5dbc: cmp             w3, NULL
    // 0xbd5dc0: b.ne            #0xbd5dcc
    // 0xbd5dc4: r0 = Null
    //     0xbd5dc4: mov             x0, NULL
    // 0xbd5dc8: b               #0xbd5e04
    // 0xbd5dcc: ldur            x5, [fp, #-0x28]
    // 0xbd5dd0: LoadField: r0 = r3->field_b
    //     0xbd5dd0: ldur            w0, [x3, #0xb]
    // 0xbd5dd4: r1 = LoadInt32Instr(r0)
    //     0xbd5dd4: sbfx            x1, x0, #1, #0x1f
    // 0xbd5dd8: mov             x0, x1
    // 0xbd5ddc: mov             x1, x5
    // 0xbd5de0: cmp             x1, x0
    // 0xbd5de4: b.hs            #0xbd63a8
    // 0xbd5de8: LoadField: r0 = r3->field_f
    //     0xbd5de8: ldur            w0, [x3, #0xf]
    // 0xbd5dec: DecompressPointer r0
    //     0xbd5dec: add             x0, x0, HEAP, lsl #32
    // 0xbd5df0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbd5df0: add             x16, x0, x5, lsl #2
    //     0xbd5df4: ldur            w1, [x16, #0xf]
    // 0xbd5df8: DecompressPointer r1
    //     0xbd5df8: add             x1, x1, HEAP, lsl #32
    // 0xbd5dfc: LoadField: r0 = r1->field_7
    //     0xbd5dfc: ldur            w0, [x1, #7]
    // 0xbd5e00: DecompressPointer r0
    //     0xbd5e00: add             x0, x0, HEAP, lsl #32
    // 0xbd5e04: cmp             w0, NULL
    // 0xbd5e08: b.ne            #0xbd5e10
    // 0xbd5e0c: r0 = ""
    //     0xbd5e0c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd5e10: ldur            x6, [fp, #-0x30]
    // 0xbd5e14: ArrayStore: r6[0] = r0  ; List_4
    //     0xbd5e14: stur            w0, [x6, #0x17]
    //     0xbd5e18: ldurb           w16, [x6, #-1]
    //     0xbd5e1c: ldurb           w17, [x0, #-1]
    //     0xbd5e20: and             x16, x17, x16, lsr #2
    //     0xbd5e24: tst             x16, HEAP, lsr #32
    //     0xbd5e28: b.eq            #0xbd5e30
    //     0xbd5e2c: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0xbd5e30: mov             x1, x2
    // 0xbd5e34: b               #0xbd62e0
    // 0xbd5e38: mov             x6, x4
    // 0xbd5e3c: ldur            x4, [fp, #-0x18]
    // 0xbd5e40: ldur            x5, [fp, #-0x28]
    // 0xbd5e44: LoadField: r2 = r3->field_13
    //     0xbd5e44: ldur            w2, [x3, #0x13]
    // 0xbd5e48: DecompressPointer r2
    //     0xbd5e48: add             x2, x2, HEAP, lsl #32
    // 0xbd5e4c: cmp             w4, NULL
    // 0xbd5e50: b.ne            #0xbd5e5c
    // 0xbd5e54: r0 = Null
    //     0xbd5e54: mov             x0, NULL
    // 0xbd5e58: b               #0xbd5ea8
    // 0xbd5e5c: LoadField: r3 = r4->field_f
    //     0xbd5e5c: ldur            w3, [x4, #0xf]
    // 0xbd5e60: DecompressPointer r3
    //     0xbd5e60: add             x3, x3, HEAP, lsl #32
    // 0xbd5e64: cmp             w3, NULL
    // 0xbd5e68: b.ne            #0xbd5e74
    // 0xbd5e6c: r0 = Null
    //     0xbd5e6c: mov             x0, NULL
    // 0xbd5e70: b               #0xbd5ea8
    // 0xbd5e74: LoadField: r0 = r3->field_b
    //     0xbd5e74: ldur            w0, [x3, #0xb]
    // 0xbd5e78: r1 = LoadInt32Instr(r0)
    //     0xbd5e78: sbfx            x1, x0, #1, #0x1f
    // 0xbd5e7c: mov             x0, x1
    // 0xbd5e80: mov             x1, x5
    // 0xbd5e84: cmp             x1, x0
    // 0xbd5e88: b.hs            #0xbd63ac
    // 0xbd5e8c: LoadField: r0 = r3->field_f
    //     0xbd5e8c: ldur            w0, [x3, #0xf]
    // 0xbd5e90: DecompressPointer r0
    //     0xbd5e90: add             x0, x0, HEAP, lsl #32
    // 0xbd5e94: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbd5e94: add             x16, x0, x5, lsl #2
    //     0xbd5e98: ldur            w1, [x16, #0xf]
    // 0xbd5e9c: DecompressPointer r1
    //     0xbd5e9c: add             x1, x1, HEAP, lsl #32
    // 0xbd5ea0: LoadField: r0 = r1->field_7
    //     0xbd5ea0: ldur            w0, [x1, #7]
    // 0xbd5ea4: DecompressPointer r0
    //     0xbd5ea4: add             x0, x0, HEAP, lsl #32
    // 0xbd5ea8: mov             x1, x2
    // 0xbd5eac: mov             x2, x0
    // 0xbd5eb0: r0 = contains()
    //     0xbd5eb0: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xbd5eb4: mov             x2, x0
    // 0xbd5eb8: ldur            x4, [fp, #-0x18]
    // 0xbd5ebc: cmp             w4, NULL
    // 0xbd5ec0: b.ne            #0xbd5ecc
    // 0xbd5ec4: r0 = Null
    //     0xbd5ec4: mov             x0, NULL
    // 0xbd5ec8: b               #0xbd5f1c
    // 0xbd5ecc: LoadField: r3 = r4->field_f
    //     0xbd5ecc: ldur            w3, [x4, #0xf]
    // 0xbd5ed0: DecompressPointer r3
    //     0xbd5ed0: add             x3, x3, HEAP, lsl #32
    // 0xbd5ed4: cmp             w3, NULL
    // 0xbd5ed8: b.ne            #0xbd5ee4
    // 0xbd5edc: r0 = Null
    //     0xbd5edc: mov             x0, NULL
    // 0xbd5ee0: b               #0xbd5f1c
    // 0xbd5ee4: ldur            x5, [fp, #-0x28]
    // 0xbd5ee8: LoadField: r0 = r3->field_b
    //     0xbd5ee8: ldur            w0, [x3, #0xb]
    // 0xbd5eec: r1 = LoadInt32Instr(r0)
    //     0xbd5eec: sbfx            x1, x0, #1, #0x1f
    // 0xbd5ef0: mov             x0, x1
    // 0xbd5ef4: mov             x1, x5
    // 0xbd5ef8: cmp             x1, x0
    // 0xbd5efc: b.hs            #0xbd63b0
    // 0xbd5f00: LoadField: r0 = r3->field_f
    //     0xbd5f00: ldur            w0, [x3, #0xf]
    // 0xbd5f04: DecompressPointer r0
    //     0xbd5f04: add             x0, x0, HEAP, lsl #32
    // 0xbd5f08: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbd5f08: add             x16, x0, x5, lsl #2
    //     0xbd5f0c: ldur            w1, [x16, #0xf]
    // 0xbd5f10: DecompressPointer r1
    //     0xbd5f10: add             x1, x1, HEAP, lsl #32
    // 0xbd5f14: LoadField: r0 = r1->field_7
    //     0xbd5f14: ldur            w0, [x1, #7]
    // 0xbd5f18: DecompressPointer r0
    //     0xbd5f18: add             x0, x0, HEAP, lsl #32
    // 0xbd5f1c: cmp             w0, NULL
    // 0xbd5f20: b.ne            #0xbd5f28
    // 0xbd5f24: r0 = ""
    //     0xbd5f24: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd5f28: ldur            x6, [fp, #-0x30]
    // 0xbd5f2c: ArrayStore: r6[0] = r0  ; List_4
    //     0xbd5f2c: stur            w0, [x6, #0x17]
    //     0xbd5f30: ldurb           w16, [x6, #-1]
    //     0xbd5f34: ldurb           w17, [x0, #-1]
    //     0xbd5f38: and             x16, x17, x16, lsr #2
    //     0xbd5f3c: tst             x16, HEAP, lsr #32
    //     0xbd5f40: b.eq            #0xbd5f48
    //     0xbd5f44: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0xbd5f48: mov             x1, x2
    // 0xbd5f4c: b               #0xbd62e0
    // 0xbd5f50: mov             x6, x4
    // 0xbd5f54: ldur            x4, [fp, #-0x18]
    // 0xbd5f58: ldur            x5, [fp, #-0x28]
    // 0xbd5f5c: cmp             x1, #2
    // 0xbd5f60: b.gt            #0xbd6070
    // 0xbd5f64: LoadField: r2 = r3->field_13
    //     0xbd5f64: ldur            w2, [x3, #0x13]
    // 0xbd5f68: DecompressPointer r2
    //     0xbd5f68: add             x2, x2, HEAP, lsl #32
    // 0xbd5f6c: cmp             w4, NULL
    // 0xbd5f70: b.ne            #0xbd5f7c
    // 0xbd5f74: r0 = Null
    //     0xbd5f74: mov             x0, NULL
    // 0xbd5f78: b               #0xbd5fc8
    // 0xbd5f7c: LoadField: r3 = r4->field_b
    //     0xbd5f7c: ldur            w3, [x4, #0xb]
    // 0xbd5f80: DecompressPointer r3
    //     0xbd5f80: add             x3, x3, HEAP, lsl #32
    // 0xbd5f84: cmp             w3, NULL
    // 0xbd5f88: b.ne            #0xbd5f94
    // 0xbd5f8c: r0 = Null
    //     0xbd5f8c: mov             x0, NULL
    // 0xbd5f90: b               #0xbd5fc8
    // 0xbd5f94: LoadField: r0 = r3->field_b
    //     0xbd5f94: ldur            w0, [x3, #0xb]
    // 0xbd5f98: r1 = LoadInt32Instr(r0)
    //     0xbd5f98: sbfx            x1, x0, #1, #0x1f
    // 0xbd5f9c: mov             x0, x1
    // 0xbd5fa0: mov             x1, x5
    // 0xbd5fa4: cmp             x1, x0
    // 0xbd5fa8: b.hs            #0xbd63b4
    // 0xbd5fac: LoadField: r0 = r3->field_f
    //     0xbd5fac: ldur            w0, [x3, #0xf]
    // 0xbd5fb0: DecompressPointer r0
    //     0xbd5fb0: add             x0, x0, HEAP, lsl #32
    // 0xbd5fb4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbd5fb4: add             x16, x0, x5, lsl #2
    //     0xbd5fb8: ldur            w1, [x16, #0xf]
    // 0xbd5fbc: DecompressPointer r1
    //     0xbd5fbc: add             x1, x1, HEAP, lsl #32
    // 0xbd5fc0: LoadField: r0 = r1->field_7
    //     0xbd5fc0: ldur            w0, [x1, #7]
    // 0xbd5fc4: DecompressPointer r0
    //     0xbd5fc4: add             x0, x0, HEAP, lsl #32
    // 0xbd5fc8: mov             x1, x2
    // 0xbd5fcc: mov             x2, x0
    // 0xbd5fd0: r0 = contains()
    //     0xbd5fd0: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xbd5fd4: mov             x2, x0
    // 0xbd5fd8: ldur            x4, [fp, #-0x18]
    // 0xbd5fdc: cmp             w4, NULL
    // 0xbd5fe0: b.ne            #0xbd5fec
    // 0xbd5fe4: r0 = Null
    //     0xbd5fe4: mov             x0, NULL
    // 0xbd5fe8: b               #0xbd603c
    // 0xbd5fec: LoadField: r3 = r4->field_b
    //     0xbd5fec: ldur            w3, [x4, #0xb]
    // 0xbd5ff0: DecompressPointer r3
    //     0xbd5ff0: add             x3, x3, HEAP, lsl #32
    // 0xbd5ff4: cmp             w3, NULL
    // 0xbd5ff8: b.ne            #0xbd6004
    // 0xbd5ffc: r0 = Null
    //     0xbd5ffc: mov             x0, NULL
    // 0xbd6000: b               #0xbd603c
    // 0xbd6004: ldur            x5, [fp, #-0x28]
    // 0xbd6008: LoadField: r0 = r3->field_b
    //     0xbd6008: ldur            w0, [x3, #0xb]
    // 0xbd600c: r1 = LoadInt32Instr(r0)
    //     0xbd600c: sbfx            x1, x0, #1, #0x1f
    // 0xbd6010: mov             x0, x1
    // 0xbd6014: mov             x1, x5
    // 0xbd6018: cmp             x1, x0
    // 0xbd601c: b.hs            #0xbd63b8
    // 0xbd6020: LoadField: r0 = r3->field_f
    //     0xbd6020: ldur            w0, [x3, #0xf]
    // 0xbd6024: DecompressPointer r0
    //     0xbd6024: add             x0, x0, HEAP, lsl #32
    // 0xbd6028: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbd6028: add             x16, x0, x5, lsl #2
    //     0xbd602c: ldur            w1, [x16, #0xf]
    // 0xbd6030: DecompressPointer r1
    //     0xbd6030: add             x1, x1, HEAP, lsl #32
    // 0xbd6034: LoadField: r0 = r1->field_7
    //     0xbd6034: ldur            w0, [x1, #7]
    // 0xbd6038: DecompressPointer r0
    //     0xbd6038: add             x0, x0, HEAP, lsl #32
    // 0xbd603c: cmp             w0, NULL
    // 0xbd6040: b.ne            #0xbd6048
    // 0xbd6044: r0 = ""
    //     0xbd6044: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd6048: ldur            x6, [fp, #-0x30]
    // 0xbd604c: ArrayStore: r6[0] = r0  ; List_4
    //     0xbd604c: stur            w0, [x6, #0x17]
    //     0xbd6050: ldurb           w16, [x6, #-1]
    //     0xbd6054: ldurb           w17, [x0, #-1]
    //     0xbd6058: and             x16, x17, x16, lsr #2
    //     0xbd605c: tst             x16, HEAP, lsr #32
    //     0xbd6060: b.eq            #0xbd6068
    //     0xbd6064: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0xbd6068: mov             x1, x2
    // 0xbd606c: b               #0xbd62e0
    // 0xbd6070: cmp             w4, NULL
    // 0xbd6074: b.ne            #0xbd6080
    // 0xbd6078: r0 = Null
    //     0xbd6078: mov             x0, NULL
    // 0xbd607c: b               #0xbd60cc
    // 0xbd6080: LoadField: r2 = r4->field_13
    //     0xbd6080: ldur            w2, [x4, #0x13]
    // 0xbd6084: DecompressPointer r2
    //     0xbd6084: add             x2, x2, HEAP, lsl #32
    // 0xbd6088: cmp             w2, NULL
    // 0xbd608c: b.ne            #0xbd6098
    // 0xbd6090: r0 = Null
    //     0xbd6090: mov             x0, NULL
    // 0xbd6094: b               #0xbd60cc
    // 0xbd6098: LoadField: r0 = r2->field_b
    //     0xbd6098: ldur            w0, [x2, #0xb]
    // 0xbd609c: r1 = LoadInt32Instr(r0)
    //     0xbd609c: sbfx            x1, x0, #1, #0x1f
    // 0xbd60a0: mov             x0, x1
    // 0xbd60a4: mov             x1, x5
    // 0xbd60a8: cmp             x1, x0
    // 0xbd60ac: b.hs            #0xbd63bc
    // 0xbd60b0: LoadField: r0 = r2->field_f
    //     0xbd60b0: ldur            w0, [x2, #0xf]
    // 0xbd60b4: DecompressPointer r0
    //     0xbd60b4: add             x0, x0, HEAP, lsl #32
    // 0xbd60b8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbd60b8: add             x16, x0, x5, lsl #2
    //     0xbd60bc: ldur            w1, [x16, #0xf]
    // 0xbd60c0: DecompressPointer r1
    //     0xbd60c0: add             x1, x1, HEAP, lsl #32
    // 0xbd60c4: LoadField: r0 = r1->field_7
    //     0xbd60c4: ldur            w0, [x1, #7]
    // 0xbd60c8: DecompressPointer r0
    //     0xbd60c8: add             x0, x0, HEAP, lsl #32
    // 0xbd60cc: stur            x0, [fp, #-0x20]
    // 0xbd60d0: r1 = Null
    //     0xbd60d0: mov             x1, NULL
    // 0xbd60d4: r2 = 6
    //     0xbd60d4: movz            x2, #0x6
    // 0xbd60d8: r0 = AllocateArray()
    //     0xbd60d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd60dc: mov             x2, x0
    // 0xbd60e0: ldur            x0, [fp, #-0x20]
    // 0xbd60e4: StoreField: r2->field_f = r0
    //     0xbd60e4: stur            w0, [x2, #0xf]
    // 0xbd60e8: r16 = " - "
    //     0xbd60e8: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0xbd60ec: ldr             x16, [x16, #0xc08]
    // 0xbd60f0: StoreField: r2->field_13 = r16
    //     0xbd60f0: stur            w16, [x2, #0x13]
    // 0xbd60f4: ldur            x3, [fp, #-0x18]
    // 0xbd60f8: cmp             w3, NULL
    // 0xbd60fc: b.ne            #0xbd610c
    // 0xbd6100: ldur            x5, [fp, #-0x28]
    // 0xbd6104: r0 = Null
    //     0xbd6104: mov             x0, NULL
    // 0xbd6108: b               #0xbd6160
    // 0xbd610c: LoadField: r4 = r3->field_13
    //     0xbd610c: ldur            w4, [x3, #0x13]
    // 0xbd6110: DecompressPointer r4
    //     0xbd6110: add             x4, x4, HEAP, lsl #32
    // 0xbd6114: cmp             w4, NULL
    // 0xbd6118: b.ne            #0xbd6128
    // 0xbd611c: ldur            x5, [fp, #-0x28]
    // 0xbd6120: r0 = Null
    //     0xbd6120: mov             x0, NULL
    // 0xbd6124: b               #0xbd6160
    // 0xbd6128: ldur            x5, [fp, #-0x28]
    // 0xbd612c: LoadField: r0 = r4->field_b
    //     0xbd612c: ldur            w0, [x4, #0xb]
    // 0xbd6130: r1 = LoadInt32Instr(r0)
    //     0xbd6130: sbfx            x1, x0, #1, #0x1f
    // 0xbd6134: mov             x0, x1
    // 0xbd6138: mov             x1, x5
    // 0xbd613c: cmp             x1, x0
    // 0xbd6140: b.hs            #0xbd63c0
    // 0xbd6144: LoadField: r0 = r4->field_f
    //     0xbd6144: ldur            w0, [x4, #0xf]
    // 0xbd6148: DecompressPointer r0
    //     0xbd6148: add             x0, x0, HEAP, lsl #32
    // 0xbd614c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbd614c: add             x16, x0, x5, lsl #2
    //     0xbd6150: ldur            w1, [x16, #0xf]
    // 0xbd6154: DecompressPointer r1
    //     0xbd6154: add             x1, x1, HEAP, lsl #32
    // 0xbd6158: LoadField: r0 = r1->field_b
    //     0xbd6158: ldur            w0, [x1, #0xb]
    // 0xbd615c: DecompressPointer r0
    //     0xbd615c: add             x0, x0, HEAP, lsl #32
    // 0xbd6160: cmp             w0, NULL
    // 0xbd6164: b.ne            #0xbd6170
    // 0xbd6168: r0 = "above"
    //     0xbd6168: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bc10] "above"
    //     0xbd616c: ldr             x0, [x0, #0xc10]
    // 0xbd6170: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd6170: stur            w0, [x2, #0x17]
    // 0xbd6174: str             x2, [SP]
    // 0xbd6178: r0 = _interpolate()
    //     0xbd6178: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbd617c: mov             x4, x0
    // 0xbd6180: ldur            x3, [fp, #-0x18]
    // 0xbd6184: stur            x4, [fp, #-0x38]
    // 0xbd6188: cmp             w3, NULL
    // 0xbd618c: b.ne            #0xbd619c
    // 0xbd6190: ldur            x5, [fp, #-0x28]
    // 0xbd6194: r0 = Null
    //     0xbd6194: mov             x0, NULL
    // 0xbd6198: b               #0xbd61f0
    // 0xbd619c: LoadField: r2 = r3->field_13
    //     0xbd619c: ldur            w2, [x3, #0x13]
    // 0xbd61a0: DecompressPointer r2
    //     0xbd61a0: add             x2, x2, HEAP, lsl #32
    // 0xbd61a4: cmp             w2, NULL
    // 0xbd61a8: b.ne            #0xbd61b8
    // 0xbd61ac: ldur            x5, [fp, #-0x28]
    // 0xbd61b0: r0 = Null
    //     0xbd61b0: mov             x0, NULL
    // 0xbd61b4: b               #0xbd61f0
    // 0xbd61b8: ldur            x5, [fp, #-0x28]
    // 0xbd61bc: LoadField: r0 = r2->field_b
    //     0xbd61bc: ldur            w0, [x2, #0xb]
    // 0xbd61c0: r1 = LoadInt32Instr(r0)
    //     0xbd61c0: sbfx            x1, x0, #1, #0x1f
    // 0xbd61c4: mov             x0, x1
    // 0xbd61c8: mov             x1, x5
    // 0xbd61cc: cmp             x1, x0
    // 0xbd61d0: b.hs            #0xbd63c4
    // 0xbd61d4: LoadField: r0 = r2->field_f
    //     0xbd61d4: ldur            w0, [x2, #0xf]
    // 0xbd61d8: DecompressPointer r0
    //     0xbd61d8: add             x0, x0, HEAP, lsl #32
    // 0xbd61dc: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbd61dc: add             x16, x0, x5, lsl #2
    //     0xbd61e0: ldur            w1, [x16, #0xf]
    // 0xbd61e4: DecompressPointer r1
    //     0xbd61e4: add             x1, x1, HEAP, lsl #32
    // 0xbd61e8: LoadField: r0 = r1->field_7
    //     0xbd61e8: ldur            w0, [x1, #7]
    // 0xbd61ec: DecompressPointer r0
    //     0xbd61ec: add             x0, x0, HEAP, lsl #32
    // 0xbd61f0: stur            x0, [fp, #-0x20]
    // 0xbd61f4: r1 = Null
    //     0xbd61f4: mov             x1, NULL
    // 0xbd61f8: r2 = 6
    //     0xbd61f8: movz            x2, #0x6
    // 0xbd61fc: r0 = AllocateArray()
    //     0xbd61fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd6200: mov             x2, x0
    // 0xbd6204: ldur            x0, [fp, #-0x20]
    // 0xbd6208: StoreField: r2->field_f = r0
    //     0xbd6208: stur            w0, [x2, #0xf]
    // 0xbd620c: r16 = " - "
    //     0xbd620c: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0xbd6210: ldr             x16, [x16, #0xc08]
    // 0xbd6214: StoreField: r2->field_13 = r16
    //     0xbd6214: stur            w16, [x2, #0x13]
    // 0xbd6218: ldur            x0, [fp, #-0x18]
    // 0xbd621c: cmp             w0, NULL
    // 0xbd6220: b.ne            #0xbd622c
    // 0xbd6224: r0 = Null
    //     0xbd6224: mov             x0, NULL
    // 0xbd6228: b               #0xbd627c
    // 0xbd622c: LoadField: r3 = r0->field_13
    //     0xbd622c: ldur            w3, [x0, #0x13]
    // 0xbd6230: DecompressPointer r3
    //     0xbd6230: add             x3, x3, HEAP, lsl #32
    // 0xbd6234: cmp             w3, NULL
    // 0xbd6238: b.ne            #0xbd6244
    // 0xbd623c: r0 = Null
    //     0xbd623c: mov             x0, NULL
    // 0xbd6240: b               #0xbd627c
    // 0xbd6244: ldur            x4, [fp, #-0x28]
    // 0xbd6248: LoadField: r0 = r3->field_b
    //     0xbd6248: ldur            w0, [x3, #0xb]
    // 0xbd624c: r1 = LoadInt32Instr(r0)
    //     0xbd624c: sbfx            x1, x0, #1, #0x1f
    // 0xbd6250: mov             x0, x1
    // 0xbd6254: mov             x1, x4
    // 0xbd6258: cmp             x1, x0
    // 0xbd625c: b.hs            #0xbd63c8
    // 0xbd6260: LoadField: r0 = r3->field_f
    //     0xbd6260: ldur            w0, [x3, #0xf]
    // 0xbd6264: DecompressPointer r0
    //     0xbd6264: add             x0, x0, HEAP, lsl #32
    // 0xbd6268: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbd6268: add             x16, x0, x4, lsl #2
    //     0xbd626c: ldur            w1, [x16, #0xf]
    // 0xbd6270: DecompressPointer r1
    //     0xbd6270: add             x1, x1, HEAP, lsl #32
    // 0xbd6274: LoadField: r0 = r1->field_b
    //     0xbd6274: ldur            w0, [x1, #0xb]
    // 0xbd6278: DecompressPointer r0
    //     0xbd6278: add             x0, x0, HEAP, lsl #32
    // 0xbd627c: cmp             w0, NULL
    // 0xbd6280: b.ne            #0xbd6290
    // 0xbd6284: r3 = "above"
    //     0xbd6284: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bc10] "above"
    //     0xbd6288: ldr             x3, [x3, #0xc10]
    // 0xbd628c: b               #0xbd6294
    // 0xbd6290: mov             x3, x0
    // 0xbd6294: ldur            x0, [fp, #-0x10]
    // 0xbd6298: ldur            x1, [fp, #-0x30]
    // 0xbd629c: ArrayStore: r2[0] = r3  ; List_4
    //     0xbd629c: stur            w3, [x2, #0x17]
    // 0xbd62a0: str             x2, [SP]
    // 0xbd62a4: r0 = _interpolate()
    //     0xbd62a4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbd62a8: ldur            x3, [fp, #-0x30]
    // 0xbd62ac: ArrayStore: r3[0] = r0  ; List_4
    //     0xbd62ac: stur            w0, [x3, #0x17]
    //     0xbd62b0: ldurb           w16, [x3, #-1]
    //     0xbd62b4: ldurb           w17, [x0, #-1]
    //     0xbd62b8: and             x16, x17, x16, lsr #2
    //     0xbd62bc: tst             x16, HEAP, lsr #32
    //     0xbd62c0: b.eq            #0xbd62c8
    //     0xbd62c4: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xbd62c8: ldur            x0, [fp, #-0x10]
    // 0xbd62cc: LoadField: r1 = r0->field_13
    //     0xbd62cc: ldur            w1, [x0, #0x13]
    // 0xbd62d0: DecompressPointer r1
    //     0xbd62d0: add             x1, x1, HEAP, lsl #32
    // 0xbd62d4: ldur            x2, [fp, #-0x38]
    // 0xbd62d8: r0 = contains()
    //     0xbd62d8: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xbd62dc: mov             x1, x0
    // 0xbd62e0: ldur            x0, [fp, #-8]
    // 0xbd62e4: stur            x1, [fp, #-0x10]
    // 0xbd62e8: cmp             w0, NULL
    // 0xbd62ec: b.ne            #0xbd6320
    // 0xbd62f0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbd62f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbd62f4: ldr             x0, [x0, #0x1c80]
    //     0xbd62f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbd62fc: cmp             w0, w16
    //     0xbd6300: b.ne            #0xbd630c
    //     0xbd6304: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbd6308: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbd630c: r0 = GetNavigation.context()
    //     0xbd630c: bl              #0x8a54d0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0xbd6310: cmp             w0, NULL
    // 0xbd6314: b.eq            #0xbd63cc
    // 0xbd6318: mov             x1, x0
    // 0xbd631c: b               #0xbd6324
    // 0xbd6320: mov             x1, x0
    // 0xbd6324: ldur            x0, [fp, #-0x10]
    // 0xbd6328: r0 = of()
    //     0xbd6328: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd632c: LoadField: r1 = r0->field_5b
    //     0xbd632c: ldur            w1, [x0, #0x5b]
    // 0xbd6330: DecompressPointer r1
    //     0xbd6330: add             x1, x1, HEAP, lsl #32
    // 0xbd6334: stur            x1, [fp, #-8]
    // 0xbd6338: r0 = Checkbox()
    //     0xbd6338: bl              #0xa2e5f0  ; AllocateCheckboxStub -> Checkbox (size=0x5c)
    // 0xbd633c: mov             x3, x0
    // 0xbd6340: ldur            x0, [fp, #-0x10]
    // 0xbd6344: stur            x3, [fp, #-0x18]
    // 0xbd6348: StoreField: r3->field_b = r0
    //     0xbd6348: stur            w0, [x3, #0xb]
    // 0xbd634c: r0 = false
    //     0xbd634c: add             x0, NULL, #0x30  ; false
    // 0xbd6350: StoreField: r3->field_23 = r0
    //     0xbd6350: stur            w0, [x3, #0x23]
    // 0xbd6354: ldur            x2, [fp, #-0x30]
    // 0xbd6358: r1 = Function '<anonymous closure>':.
    //     0xbd6358: add             x1, PP, #0x53, lsl #12  ; [pp+0x53d78] AnonymousClosure: (0xbd63d0), in [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::_productBuildItem (0xbd5c74)
    //     0xbd635c: ldr             x1, [x1, #0xd78]
    // 0xbd6360: r0 = AllocateClosure()
    //     0xbd6360: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd6364: mov             x1, x0
    // 0xbd6368: ldur            x0, [fp, #-0x18]
    // 0xbd636c: StoreField: r0->field_f = r1
    //     0xbd636c: stur            w1, [x0, #0xf]
    // 0xbd6370: ldur            x1, [fp, #-8]
    // 0xbd6374: ArrayStore: r0[0] = r1  ; List_4
    //     0xbd6374: stur            w1, [x0, #0x17]
    // 0xbd6378: r1 = false
    //     0xbd6378: add             x1, NULL, #0x30  ; false
    // 0xbd637c: StoreField: r0->field_43 = r1
    //     0xbd637c: stur            w1, [x0, #0x43]
    // 0xbd6380: StoreField: r0->field_4f = r1
    //     0xbd6380: stur            w1, [x0, #0x4f]
    // 0xbd6384: r1 = Instance__CheckboxType
    //     0xbd6384: add             x1, PP, #0x53, lsl #12  ; [pp+0x53d80] Obj!_CheckboxType@d74601
    //     0xbd6388: ldr             x1, [x1, #0xd80]
    // 0xbd638c: StoreField: r0->field_57 = r1
    //     0xbd638c: stur            w1, [x0, #0x57]
    // 0xbd6390: LeaveFrame
    //     0xbd6390: mov             SP, fp
    //     0xbd6394: ldp             fp, lr, [SP], #0x10
    // 0xbd6398: ret
    //     0xbd6398: ret             
    // 0xbd639c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd639c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd63a0: b               #0xbd5ce0
    // 0xbd63a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd63a4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd63a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd63a8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd63ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd63ac: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd63b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd63b0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd63b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd63b4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd63b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd63b8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd63bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd63bc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd63c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd63c0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd63c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd63c4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd63c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd63c8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd63cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd63cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, bool?) {
    // ** addr: 0xbd63d0, size: 0x68
    // 0xbd63d0: EnterFrame
    //     0xbd63d0: stp             fp, lr, [SP, #-0x10]!
    //     0xbd63d4: mov             fp, SP
    // 0xbd63d8: ldr             x0, [fp, #0x18]
    // 0xbd63dc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbd63dc: ldur            w1, [x0, #0x17]
    // 0xbd63e0: DecompressPointer r1
    //     0xbd63e0: add             x1, x1, HEAP, lsl #32
    // 0xbd63e4: CheckStackOverflow
    //     0xbd63e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd63e8: cmp             SP, x16
    //     0xbd63ec: b.ls            #0xbd642c
    // 0xbd63f0: LoadField: r0 = r1->field_f
    //     0xbd63f0: ldur            w0, [x1, #0xf]
    // 0xbd63f4: DecompressPointer r0
    //     0xbd63f4: add             x0, x0, HEAP, lsl #32
    // 0xbd63f8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbd63f8: ldur            w2, [x1, #0x17]
    // 0xbd63fc: DecompressPointer r2
    //     0xbd63fc: add             x2, x2, HEAP, lsl #32
    // 0xbd6400: ldr             x3, [fp, #0x10]
    // 0xbd6404: cmp             w3, NULL
    // 0xbd6408: b.eq            #0xbd6434
    // 0xbd640c: LoadField: r5 = r1->field_13
    //     0xbd640c: ldur            w5, [x1, #0x13]
    // 0xbd6410: DecompressPointer r5
    //     0xbd6410: add             x5, x5, HEAP, lsl #32
    // 0xbd6414: mov             x1, x0
    // 0xbd6418: r0 = _onItemCheckedChange()
    //     0xbd6418: bl              #0xa2ed14  ; [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::_onItemCheckedChange
    // 0xbd641c: r0 = Null
    //     0xbd641c: mov             x0, NULL
    // 0xbd6420: LeaveFrame
    //     0xbd6420: mov             SP, fp
    //     0xbd6424: ldp             fp, lr, [SP], #0x10
    // 0xbd6428: ret
    //     0xbd6428: ret             
    // 0xbd642c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd642c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd6430: b               #0xbd63f0
    // 0xbd6434: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd6434: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbd6438, size: 0x390
    // 0xbd6438: EnterFrame
    //     0xbd6438: stp             fp, lr, [SP, #-0x10]!
    //     0xbd643c: mov             fp, SP
    // 0xbd6440: AllocStack(0x40)
    //     0xbd6440: sub             SP, SP, #0x40
    // 0xbd6444: SetupParameters()
    //     0xbd6444: ldr             x0, [fp, #0x20]
    //     0xbd6448: ldur            w4, [x0, #0x17]
    //     0xbd644c: add             x4, x4, HEAP, lsl #32
    //     0xbd6450: stur            x4, [fp, #-0x10]
    // 0xbd6454: CheckStackOverflow
    //     0xbd6454: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd6458: cmp             SP, x16
    //     0xbd645c: b.ls            #0xbd67ac
    // 0xbd6460: LoadField: r1 = r4->field_f
    //     0xbd6460: ldur            w1, [x4, #0xf]
    // 0xbd6464: DecompressPointer r1
    //     0xbd6464: add             x1, x1, HEAP, lsl #32
    // 0xbd6468: LoadField: r0 = r1->field_b
    //     0xbd6468: ldur            w0, [x1, #0xb]
    // 0xbd646c: DecompressPointer r0
    //     0xbd646c: add             x0, x0, HEAP, lsl #32
    // 0xbd6470: cmp             w0, NULL
    // 0xbd6474: b.eq            #0xbd67b4
    // 0xbd6478: LoadField: r2 = r0->field_b
    //     0xbd6478: ldur            w2, [x0, #0xb]
    // 0xbd647c: DecompressPointer r2
    //     0xbd647c: add             x2, x2, HEAP, lsl #32
    // 0xbd6480: ldr             x0, [fp, #0x10]
    // 0xbd6484: r6 = LoadInt32Instr(r0)
    //     0xbd6484: sbfx            x6, x0, #1, #0x1f
    //     0xbd6488: tbz             w0, #0, #0xbd6490
    //     0xbd648c: ldur            x6, [x0, #7]
    // 0xbd6490: mov             x5, x6
    // 0xbd6494: stur            x6, [fp, #-8]
    // 0xbd6498: r3 = Instance_ItemFilterType
    //     0xbd6498: add             x3, PP, #0x53, lsl #12  ; [pp+0x53d90] Obj!ItemFilterType@d752c1
    //     0xbd649c: ldr             x3, [x3, #0xd90]
    // 0xbd64a0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xbd64a0: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xbd64a4: r0 = _productBuildItem()
    //     0xbd64a4: bl              #0xbd5c74  ; [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::_productBuildItem
    // 0xbd64a8: mov             x3, x0
    // 0xbd64ac: ldur            x2, [fp, #-0x10]
    // 0xbd64b0: stur            x3, [fp, #-0x20]
    // 0xbd64b4: LoadField: r0 = r2->field_f
    //     0xbd64b4: ldur            w0, [x2, #0xf]
    // 0xbd64b8: DecompressPointer r0
    //     0xbd64b8: add             x0, x0, HEAP, lsl #32
    // 0xbd64bc: LoadField: r1 = r0->field_b
    //     0xbd64bc: ldur            w1, [x0, #0xb]
    // 0xbd64c0: DecompressPointer r1
    //     0xbd64c0: add             x1, x1, HEAP, lsl #32
    // 0xbd64c4: cmp             w1, NULL
    // 0xbd64c8: b.eq            #0xbd67b8
    // 0xbd64cc: LoadField: r0 = r1->field_b
    //     0xbd64cc: ldur            w0, [x1, #0xb]
    // 0xbd64d0: DecompressPointer r0
    //     0xbd64d0: add             x0, x0, HEAP, lsl #32
    // 0xbd64d4: cmp             w0, NULL
    // 0xbd64d8: b.ne            #0xbd64e8
    // 0xbd64dc: ldur            x5, [fp, #-8]
    // 0xbd64e0: r0 = Null
    //     0xbd64e0: mov             x0, NULL
    // 0xbd64e4: b               #0xbd653c
    // 0xbd64e8: LoadField: r4 = r0->field_b
    //     0xbd64e8: ldur            w4, [x0, #0xb]
    // 0xbd64ec: DecompressPointer r4
    //     0xbd64ec: add             x4, x4, HEAP, lsl #32
    // 0xbd64f0: cmp             w4, NULL
    // 0xbd64f4: b.ne            #0xbd6504
    // 0xbd64f8: ldur            x5, [fp, #-8]
    // 0xbd64fc: r0 = Null
    //     0xbd64fc: mov             x0, NULL
    // 0xbd6500: b               #0xbd653c
    // 0xbd6504: ldur            x5, [fp, #-8]
    // 0xbd6508: LoadField: r0 = r4->field_b
    //     0xbd6508: ldur            w0, [x4, #0xb]
    // 0xbd650c: r1 = LoadInt32Instr(r0)
    //     0xbd650c: sbfx            x1, x0, #1, #0x1f
    // 0xbd6510: mov             x0, x1
    // 0xbd6514: mov             x1, x5
    // 0xbd6518: cmp             x1, x0
    // 0xbd651c: b.hs            #0xbd67bc
    // 0xbd6520: LoadField: r0 = r4->field_f
    //     0xbd6520: ldur            w0, [x4, #0xf]
    // 0xbd6524: DecompressPointer r0
    //     0xbd6524: add             x0, x0, HEAP, lsl #32
    // 0xbd6528: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbd6528: add             x16, x0, x5, lsl #2
    //     0xbd652c: ldur            w1, [x16, #0xf]
    // 0xbd6530: DecompressPointer r1
    //     0xbd6530: add             x1, x1, HEAP, lsl #32
    // 0xbd6534: LoadField: r0 = r1->field_7
    //     0xbd6534: ldur            w0, [x1, #7]
    // 0xbd6538: DecompressPointer r0
    //     0xbd6538: add             x0, x0, HEAP, lsl #32
    // 0xbd653c: cmp             w0, NULL
    // 0xbd6540: b.ne            #0xbd6548
    // 0xbd6544: r0 = ""
    //     0xbd6544: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd6548: ldr             x1, [fp, #0x18]
    // 0xbd654c: stur            x0, [fp, #-0x18]
    // 0xbd6550: r0 = of()
    //     0xbd6550: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd6554: LoadField: r1 = r0->field_87
    //     0xbd6554: ldur            w1, [x0, #0x87]
    // 0xbd6558: DecompressPointer r1
    //     0xbd6558: add             x1, x1, HEAP, lsl #32
    // 0xbd655c: LoadField: r0 = r1->field_2b
    //     0xbd655c: ldur            w0, [x1, #0x2b]
    // 0xbd6560: DecompressPointer r0
    //     0xbd6560: add             x0, x0, HEAP, lsl #32
    // 0xbd6564: r16 = 14.000000
    //     0xbd6564: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd6568: ldr             x16, [x16, #0x1d8]
    // 0xbd656c: r30 = Instance_Color
    //     0xbd656c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd6570: stp             lr, x16, [SP]
    // 0xbd6574: mov             x1, x0
    // 0xbd6578: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd6578: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd657c: ldr             x4, [x4, #0xaa0]
    // 0xbd6580: r0 = copyWith()
    //     0xbd6580: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd6584: stur            x0, [fp, #-0x28]
    // 0xbd6588: r0 = Text()
    //     0xbd6588: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd658c: mov             x2, x0
    // 0xbd6590: ldur            x0, [fp, #-0x18]
    // 0xbd6594: stur            x2, [fp, #-0x30]
    // 0xbd6598: StoreField: r2->field_b = r0
    //     0xbd6598: stur            w0, [x2, #0xb]
    // 0xbd659c: ldur            x0, [fp, #-0x28]
    // 0xbd65a0: StoreField: r2->field_13 = r0
    //     0xbd65a0: stur            w0, [x2, #0x13]
    // 0xbd65a4: ldur            x0, [fp, #-0x10]
    // 0xbd65a8: LoadField: r1 = r0->field_f
    //     0xbd65a8: ldur            w1, [x0, #0xf]
    // 0xbd65ac: DecompressPointer r1
    //     0xbd65ac: add             x1, x1, HEAP, lsl #32
    // 0xbd65b0: LoadField: r0 = r1->field_b
    //     0xbd65b0: ldur            w0, [x1, #0xb]
    // 0xbd65b4: DecompressPointer r0
    //     0xbd65b4: add             x0, x0, HEAP, lsl #32
    // 0xbd65b8: cmp             w0, NULL
    // 0xbd65bc: b.eq            #0xbd67c0
    // 0xbd65c0: LoadField: r1 = r0->field_b
    //     0xbd65c0: ldur            w1, [x0, #0xb]
    // 0xbd65c4: DecompressPointer r1
    //     0xbd65c4: add             x1, x1, HEAP, lsl #32
    // 0xbd65c8: cmp             w1, NULL
    // 0xbd65cc: b.ne            #0xbd65d8
    // 0xbd65d0: r0 = Null
    //     0xbd65d0: mov             x0, NULL
    // 0xbd65d4: b               #0xbd6654
    // 0xbd65d8: LoadField: r3 = r1->field_b
    //     0xbd65d8: ldur            w3, [x1, #0xb]
    // 0xbd65dc: DecompressPointer r3
    //     0xbd65dc: add             x3, x3, HEAP, lsl #32
    // 0xbd65e0: cmp             w3, NULL
    // 0xbd65e4: b.ne            #0xbd65f0
    // 0xbd65e8: r0 = Null
    //     0xbd65e8: mov             x0, NULL
    // 0xbd65ec: b               #0xbd6654
    // 0xbd65f0: ldur            x4, [fp, #-8]
    // 0xbd65f4: LoadField: r0 = r3->field_b
    //     0xbd65f4: ldur            w0, [x3, #0xb]
    // 0xbd65f8: r1 = LoadInt32Instr(r0)
    //     0xbd65f8: sbfx            x1, x0, #1, #0x1f
    // 0xbd65fc: mov             x0, x1
    // 0xbd6600: mov             x1, x4
    // 0xbd6604: cmp             x1, x0
    // 0xbd6608: b.hs            #0xbd67c4
    // 0xbd660c: LoadField: r0 = r3->field_f
    //     0xbd660c: ldur            w0, [x3, #0xf]
    // 0xbd6610: DecompressPointer r0
    //     0xbd6610: add             x0, x0, HEAP, lsl #32
    // 0xbd6614: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbd6614: add             x16, x0, x4, lsl #2
    //     0xbd6618: ldur            w1, [x16, #0xf]
    // 0xbd661c: DecompressPointer r1
    //     0xbd661c: add             x1, x1, HEAP, lsl #32
    // 0xbd6620: LoadField: r0 = r1->field_b
    //     0xbd6620: ldur            w0, [x1, #0xb]
    // 0xbd6624: DecompressPointer r0
    //     0xbd6624: add             x0, x0, HEAP, lsl #32
    // 0xbd6628: r1 = 60
    //     0xbd6628: movz            x1, #0x3c
    // 0xbd662c: branchIfSmi(r0, 0xbd6638)
    //     0xbd662c: tbz             w0, #0, #0xbd6638
    // 0xbd6630: r1 = LoadClassIdInstr(r0)
    //     0xbd6630: ldur            x1, [x0, #-1]
    //     0xbd6634: ubfx            x1, x1, #0xc, #0x14
    // 0xbd6638: str             x0, [SP]
    // 0xbd663c: mov             x0, x1
    // 0xbd6640: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbd6640: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbd6644: r0 = GDT[cid_x0 + 0x2700]()
    //     0xbd6644: movz            x17, #0x2700
    //     0xbd6648: add             lr, x0, x17
    //     0xbd664c: ldr             lr, [x21, lr, lsl #3]
    //     0xbd6650: blr             lr
    // 0xbd6654: cmp             w0, NULL
    // 0xbd6658: b.ne            #0xbd6664
    // 0xbd665c: r3 = ""
    //     0xbd665c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd6660: b               #0xbd6668
    // 0xbd6664: mov             x3, x0
    // 0xbd6668: ldur            x2, [fp, #-0x20]
    // 0xbd666c: ldur            x0, [fp, #-0x30]
    // 0xbd6670: ldr             x1, [fp, #0x18]
    // 0xbd6674: stur            x3, [fp, #-0x10]
    // 0xbd6678: r0 = of()
    //     0xbd6678: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd667c: LoadField: r1 = r0->field_87
    //     0xbd667c: ldur            w1, [x0, #0x87]
    // 0xbd6680: DecompressPointer r1
    //     0xbd6680: add             x1, x1, HEAP, lsl #32
    // 0xbd6684: LoadField: r0 = r1->field_2b
    //     0xbd6684: ldur            w0, [x1, #0x2b]
    // 0xbd6688: DecompressPointer r0
    //     0xbd6688: add             x0, x0, HEAP, lsl #32
    // 0xbd668c: ldr             x1, [fp, #0x18]
    // 0xbd6690: stur            x0, [fp, #-0x18]
    // 0xbd6694: r0 = of()
    //     0xbd6694: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd6698: LoadField: r1 = r0->field_5b
    //     0xbd6698: ldur            w1, [x0, #0x5b]
    // 0xbd669c: DecompressPointer r1
    //     0xbd669c: add             x1, x1, HEAP, lsl #32
    // 0xbd66a0: r0 = LoadClassIdInstr(r1)
    //     0xbd66a0: ldur            x0, [x1, #-1]
    //     0xbd66a4: ubfx            x0, x0, #0xc, #0x14
    // 0xbd66a8: d0 = 0.400000
    //     0xbd66a8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbd66ac: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbd66ac: sub             lr, x0, #0xffa
    //     0xbd66b0: ldr             lr, [x21, lr, lsl #3]
    //     0xbd66b4: blr             lr
    // 0xbd66b8: r16 = 12.000000
    //     0xbd66b8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbd66bc: ldr             x16, [x16, #0x9e8]
    // 0xbd66c0: stp             x0, x16, [SP]
    // 0xbd66c4: ldur            x1, [fp, #-0x18]
    // 0xbd66c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd66c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd66cc: ldr             x4, [x4, #0xaa0]
    // 0xbd66d0: r0 = copyWith()
    //     0xbd66d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd66d4: stur            x0, [fp, #-0x18]
    // 0xbd66d8: r0 = Text()
    //     0xbd66d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd66dc: mov             x3, x0
    // 0xbd66e0: ldur            x0, [fp, #-0x10]
    // 0xbd66e4: stur            x3, [fp, #-0x28]
    // 0xbd66e8: StoreField: r3->field_b = r0
    //     0xbd66e8: stur            w0, [x3, #0xb]
    // 0xbd66ec: ldur            x0, [fp, #-0x18]
    // 0xbd66f0: StoreField: r3->field_13 = r0
    //     0xbd66f0: stur            w0, [x3, #0x13]
    // 0xbd66f4: r1 = Null
    //     0xbd66f4: mov             x1, NULL
    // 0xbd66f8: r2 = 8
    //     0xbd66f8: movz            x2, #0x8
    // 0xbd66fc: r0 = AllocateArray()
    //     0xbd66fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd6700: mov             x2, x0
    // 0xbd6704: ldur            x0, [fp, #-0x20]
    // 0xbd6708: stur            x2, [fp, #-0x10]
    // 0xbd670c: StoreField: r2->field_f = r0
    //     0xbd670c: stur            w0, [x2, #0xf]
    // 0xbd6710: ldur            x0, [fp, #-0x30]
    // 0xbd6714: StoreField: r2->field_13 = r0
    //     0xbd6714: stur            w0, [x2, #0x13]
    // 0xbd6718: r16 = Instance_Spacer
    //     0xbd6718: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbd671c: ldr             x16, [x16, #0xf0]
    // 0xbd6720: ArrayStore: r2[0] = r16  ; List_4
    //     0xbd6720: stur            w16, [x2, #0x17]
    // 0xbd6724: ldur            x0, [fp, #-0x28]
    // 0xbd6728: StoreField: r2->field_1b = r0
    //     0xbd6728: stur            w0, [x2, #0x1b]
    // 0xbd672c: r1 = <Widget>
    //     0xbd672c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd6730: r0 = AllocateGrowableArray()
    //     0xbd6730: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd6734: mov             x1, x0
    // 0xbd6738: ldur            x0, [fp, #-0x10]
    // 0xbd673c: stur            x1, [fp, #-0x18]
    // 0xbd6740: StoreField: r1->field_f = r0
    //     0xbd6740: stur            w0, [x1, #0xf]
    // 0xbd6744: r0 = 8
    //     0xbd6744: movz            x0, #0x8
    // 0xbd6748: StoreField: r1->field_b = r0
    //     0xbd6748: stur            w0, [x1, #0xb]
    // 0xbd674c: r0 = Row()
    //     0xbd674c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbd6750: r1 = Instance_Axis
    //     0xbd6750: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd6754: StoreField: r0->field_f = r1
    //     0xbd6754: stur            w1, [x0, #0xf]
    // 0xbd6758: r1 = Instance_MainAxisAlignment
    //     0xbd6758: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd675c: ldr             x1, [x1, #0xa08]
    // 0xbd6760: StoreField: r0->field_13 = r1
    //     0xbd6760: stur            w1, [x0, #0x13]
    // 0xbd6764: r1 = Instance_MainAxisSize
    //     0xbd6764: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd6768: ldr             x1, [x1, #0xa10]
    // 0xbd676c: ArrayStore: r0[0] = r1  ; List_4
    //     0xbd676c: stur            w1, [x0, #0x17]
    // 0xbd6770: r1 = Instance_CrossAxisAlignment
    //     0xbd6770: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd6774: ldr             x1, [x1, #0xa18]
    // 0xbd6778: StoreField: r0->field_1b = r1
    //     0xbd6778: stur            w1, [x0, #0x1b]
    // 0xbd677c: r1 = Instance_VerticalDirection
    //     0xbd677c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd6780: ldr             x1, [x1, #0xa20]
    // 0xbd6784: StoreField: r0->field_23 = r1
    //     0xbd6784: stur            w1, [x0, #0x23]
    // 0xbd6788: r1 = Instance_Clip
    //     0xbd6788: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd678c: ldr             x1, [x1, #0x38]
    // 0xbd6790: StoreField: r0->field_2b = r1
    //     0xbd6790: stur            w1, [x0, #0x2b]
    // 0xbd6794: StoreField: r0->field_2f = rZR
    //     0xbd6794: stur            xzr, [x0, #0x2f]
    // 0xbd6798: ldur            x1, [fp, #-0x18]
    // 0xbd679c: StoreField: r0->field_b = r1
    //     0xbd679c: stur            w1, [x0, #0xb]
    // 0xbd67a0: LeaveFrame
    //     0xbd67a0: mov             SP, fp
    //     0xbd67a4: ldp             fp, lr, [SP], #0x10
    // 0xbd67a8: ret
    //     0xbd67a8: ret             
    // 0xbd67ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd67ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd67b0: b               #0xbd6460
    // 0xbd67b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd67b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd67b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd67b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd67bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd67bc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd67c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd67c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd67c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd67c4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbd67c8, size: 0x374
    // 0xbd67c8: EnterFrame
    //     0xbd67c8: stp             fp, lr, [SP, #-0x10]!
    //     0xbd67cc: mov             fp, SP
    // 0xbd67d0: AllocStack(0x40)
    //     0xbd67d0: sub             SP, SP, #0x40
    // 0xbd67d4: SetupParameters()
    //     0xbd67d4: ldr             x0, [fp, #0x20]
    //     0xbd67d8: ldur            w4, [x0, #0x17]
    //     0xbd67dc: add             x4, x4, HEAP, lsl #32
    //     0xbd67e0: stur            x4, [fp, #-0x10]
    // 0xbd67e4: CheckStackOverflow
    //     0xbd67e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd67e8: cmp             SP, x16
    //     0xbd67ec: b.ls            #0xbd6b20
    // 0xbd67f0: LoadField: r1 = r4->field_f
    //     0xbd67f0: ldur            w1, [x4, #0xf]
    // 0xbd67f4: DecompressPointer r1
    //     0xbd67f4: add             x1, x1, HEAP, lsl #32
    // 0xbd67f8: LoadField: r0 = r1->field_b
    //     0xbd67f8: ldur            w0, [x1, #0xb]
    // 0xbd67fc: DecompressPointer r0
    //     0xbd67fc: add             x0, x0, HEAP, lsl #32
    // 0xbd6800: cmp             w0, NULL
    // 0xbd6804: b.eq            #0xbd6b28
    // 0xbd6808: LoadField: r2 = r0->field_b
    //     0xbd6808: ldur            w2, [x0, #0xb]
    // 0xbd680c: DecompressPointer r2
    //     0xbd680c: add             x2, x2, HEAP, lsl #32
    // 0xbd6810: ldr             x0, [fp, #0x10]
    // 0xbd6814: r6 = LoadInt32Instr(r0)
    //     0xbd6814: sbfx            x6, x0, #1, #0x1f
    //     0xbd6818: tbz             w0, #0, #0xbd6820
    //     0xbd681c: ldur            x6, [x0, #7]
    // 0xbd6820: mov             x5, x6
    // 0xbd6824: stur            x6, [fp, #-8]
    // 0xbd6828: r3 = Instance_ItemFilterType
    //     0xbd6828: add             x3, PP, #0x53, lsl #12  ; [pp+0x53d98] Obj!ItemFilterType@d752e1
    //     0xbd682c: ldr             x3, [x3, #0xd98]
    // 0xbd6830: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xbd6830: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xbd6834: r0 = _productBuildItem()
    //     0xbd6834: bl              #0xbd5c74  ; [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::_productBuildItem
    // 0xbd6838: mov             x3, x0
    // 0xbd683c: ldur            x2, [fp, #-0x10]
    // 0xbd6840: stur            x3, [fp, #-0x20]
    // 0xbd6844: LoadField: r0 = r2->field_f
    //     0xbd6844: ldur            w0, [x2, #0xf]
    // 0xbd6848: DecompressPointer r0
    //     0xbd6848: add             x0, x0, HEAP, lsl #32
    // 0xbd684c: LoadField: r1 = r0->field_b
    //     0xbd684c: ldur            w1, [x0, #0xb]
    // 0xbd6850: DecompressPointer r1
    //     0xbd6850: add             x1, x1, HEAP, lsl #32
    // 0xbd6854: cmp             w1, NULL
    // 0xbd6858: b.eq            #0xbd6b2c
    // 0xbd685c: LoadField: r0 = r1->field_b
    //     0xbd685c: ldur            w0, [x1, #0xb]
    // 0xbd6860: DecompressPointer r0
    //     0xbd6860: add             x0, x0, HEAP, lsl #32
    // 0xbd6864: cmp             w0, NULL
    // 0xbd6868: b.ne            #0xbd6878
    // 0xbd686c: ldur            x5, [fp, #-8]
    // 0xbd6870: r0 = Null
    //     0xbd6870: mov             x0, NULL
    // 0xbd6874: b               #0xbd68cc
    // 0xbd6878: LoadField: r4 = r0->field_f
    //     0xbd6878: ldur            w4, [x0, #0xf]
    // 0xbd687c: DecompressPointer r4
    //     0xbd687c: add             x4, x4, HEAP, lsl #32
    // 0xbd6880: cmp             w4, NULL
    // 0xbd6884: b.ne            #0xbd6894
    // 0xbd6888: ldur            x5, [fp, #-8]
    // 0xbd688c: r0 = Null
    //     0xbd688c: mov             x0, NULL
    // 0xbd6890: b               #0xbd68cc
    // 0xbd6894: ldur            x5, [fp, #-8]
    // 0xbd6898: LoadField: r0 = r4->field_b
    //     0xbd6898: ldur            w0, [x4, #0xb]
    // 0xbd689c: r1 = LoadInt32Instr(r0)
    //     0xbd689c: sbfx            x1, x0, #1, #0x1f
    // 0xbd68a0: mov             x0, x1
    // 0xbd68a4: mov             x1, x5
    // 0xbd68a8: cmp             x1, x0
    // 0xbd68ac: b.hs            #0xbd6b30
    // 0xbd68b0: LoadField: r0 = r4->field_f
    //     0xbd68b0: ldur            w0, [x4, #0xf]
    // 0xbd68b4: DecompressPointer r0
    //     0xbd68b4: add             x0, x0, HEAP, lsl #32
    // 0xbd68b8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbd68b8: add             x16, x0, x5, lsl #2
    //     0xbd68bc: ldur            w1, [x16, #0xf]
    // 0xbd68c0: DecompressPointer r1
    //     0xbd68c0: add             x1, x1, HEAP, lsl #32
    // 0xbd68c4: LoadField: r0 = r1->field_7
    //     0xbd68c4: ldur            w0, [x1, #7]
    // 0xbd68c8: DecompressPointer r0
    //     0xbd68c8: add             x0, x0, HEAP, lsl #32
    // 0xbd68cc: cmp             w0, NULL
    // 0xbd68d0: b.ne            #0xbd68d8
    // 0xbd68d4: r0 = ""
    //     0xbd68d4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd68d8: ldr             x1, [fp, #0x18]
    // 0xbd68dc: stur            x0, [fp, #-0x18]
    // 0xbd68e0: r0 = of()
    //     0xbd68e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd68e4: LoadField: r1 = r0->field_87
    //     0xbd68e4: ldur            w1, [x0, #0x87]
    // 0xbd68e8: DecompressPointer r1
    //     0xbd68e8: add             x1, x1, HEAP, lsl #32
    // 0xbd68ec: LoadField: r0 = r1->field_2b
    //     0xbd68ec: ldur            w0, [x1, #0x2b]
    // 0xbd68f0: DecompressPointer r0
    //     0xbd68f0: add             x0, x0, HEAP, lsl #32
    // 0xbd68f4: r16 = 14.000000
    //     0xbd68f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd68f8: ldr             x16, [x16, #0x1d8]
    // 0xbd68fc: r30 = Instance_Color
    //     0xbd68fc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd6900: stp             lr, x16, [SP]
    // 0xbd6904: mov             x1, x0
    // 0xbd6908: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd6908: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd690c: ldr             x4, [x4, #0xaa0]
    // 0xbd6910: r0 = copyWith()
    //     0xbd6910: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd6914: stur            x0, [fp, #-0x28]
    // 0xbd6918: r0 = Text()
    //     0xbd6918: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd691c: mov             x2, x0
    // 0xbd6920: ldur            x0, [fp, #-0x18]
    // 0xbd6924: stur            x2, [fp, #-0x30]
    // 0xbd6928: StoreField: r2->field_b = r0
    //     0xbd6928: stur            w0, [x2, #0xb]
    // 0xbd692c: ldur            x0, [fp, #-0x28]
    // 0xbd6930: StoreField: r2->field_13 = r0
    //     0xbd6930: stur            w0, [x2, #0x13]
    // 0xbd6934: ldur            x0, [fp, #-0x10]
    // 0xbd6938: LoadField: r1 = r0->field_f
    //     0xbd6938: ldur            w1, [x0, #0xf]
    // 0xbd693c: DecompressPointer r1
    //     0xbd693c: add             x1, x1, HEAP, lsl #32
    // 0xbd6940: LoadField: r0 = r1->field_b
    //     0xbd6940: ldur            w0, [x1, #0xb]
    // 0xbd6944: DecompressPointer r0
    //     0xbd6944: add             x0, x0, HEAP, lsl #32
    // 0xbd6948: cmp             w0, NULL
    // 0xbd694c: b.eq            #0xbd6b34
    // 0xbd6950: LoadField: r1 = r0->field_b
    //     0xbd6950: ldur            w1, [x0, #0xb]
    // 0xbd6954: DecompressPointer r1
    //     0xbd6954: add             x1, x1, HEAP, lsl #32
    // 0xbd6958: cmp             w1, NULL
    // 0xbd695c: b.ne            #0xbd6968
    // 0xbd6960: r0 = Null
    //     0xbd6960: mov             x0, NULL
    // 0xbd6964: b               #0xbd69e4
    // 0xbd6968: LoadField: r3 = r1->field_f
    //     0xbd6968: ldur            w3, [x1, #0xf]
    // 0xbd696c: DecompressPointer r3
    //     0xbd696c: add             x3, x3, HEAP, lsl #32
    // 0xbd6970: cmp             w3, NULL
    // 0xbd6974: b.ne            #0xbd6980
    // 0xbd6978: r0 = Null
    //     0xbd6978: mov             x0, NULL
    // 0xbd697c: b               #0xbd69e4
    // 0xbd6980: ldur            x4, [fp, #-8]
    // 0xbd6984: LoadField: r0 = r3->field_b
    //     0xbd6984: ldur            w0, [x3, #0xb]
    // 0xbd6988: r1 = LoadInt32Instr(r0)
    //     0xbd6988: sbfx            x1, x0, #1, #0x1f
    // 0xbd698c: mov             x0, x1
    // 0xbd6990: mov             x1, x4
    // 0xbd6994: cmp             x1, x0
    // 0xbd6998: b.hs            #0xbd6b38
    // 0xbd699c: LoadField: r0 = r3->field_f
    //     0xbd699c: ldur            w0, [x3, #0xf]
    // 0xbd69a0: DecompressPointer r0
    //     0xbd69a0: add             x0, x0, HEAP, lsl #32
    // 0xbd69a4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbd69a4: add             x16, x0, x4, lsl #2
    //     0xbd69a8: ldur            w1, [x16, #0xf]
    // 0xbd69ac: DecompressPointer r1
    //     0xbd69ac: add             x1, x1, HEAP, lsl #32
    // 0xbd69b0: LoadField: r0 = r1->field_b
    //     0xbd69b0: ldur            w0, [x1, #0xb]
    // 0xbd69b4: DecompressPointer r0
    //     0xbd69b4: add             x0, x0, HEAP, lsl #32
    // 0xbd69b8: r1 = 60
    //     0xbd69b8: movz            x1, #0x3c
    // 0xbd69bc: branchIfSmi(r0, 0xbd69c8)
    //     0xbd69bc: tbz             w0, #0, #0xbd69c8
    // 0xbd69c0: r1 = LoadClassIdInstr(r0)
    //     0xbd69c0: ldur            x1, [x0, #-1]
    //     0xbd69c4: ubfx            x1, x1, #0xc, #0x14
    // 0xbd69c8: str             x0, [SP]
    // 0xbd69cc: mov             x0, x1
    // 0xbd69d0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbd69d0: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbd69d4: r0 = GDT[cid_x0 + 0x2700]()
    //     0xbd69d4: movz            x17, #0x2700
    //     0xbd69d8: add             lr, x0, x17
    //     0xbd69dc: ldr             lr, [x21, lr, lsl #3]
    //     0xbd69e0: blr             lr
    // 0xbd69e4: cmp             w0, NULL
    // 0xbd69e8: b.ne            #0xbd69f4
    // 0xbd69ec: r3 = ""
    //     0xbd69ec: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd69f0: b               #0xbd69f8
    // 0xbd69f4: mov             x3, x0
    // 0xbd69f8: ldur            x2, [fp, #-0x20]
    // 0xbd69fc: ldur            x0, [fp, #-0x30]
    // 0xbd6a00: ldr             x1, [fp, #0x18]
    // 0xbd6a04: stur            x3, [fp, #-0x10]
    // 0xbd6a08: r0 = of()
    //     0xbd6a08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd6a0c: LoadField: r1 = r0->field_87
    //     0xbd6a0c: ldur            w1, [x0, #0x87]
    // 0xbd6a10: DecompressPointer r1
    //     0xbd6a10: add             x1, x1, HEAP, lsl #32
    // 0xbd6a14: LoadField: r0 = r1->field_2b
    //     0xbd6a14: ldur            w0, [x1, #0x2b]
    // 0xbd6a18: DecompressPointer r0
    //     0xbd6a18: add             x0, x0, HEAP, lsl #32
    // 0xbd6a1c: stur            x0, [fp, #-0x18]
    // 0xbd6a20: r1 = Instance_Color
    //     0xbd6a20: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd6a24: d0 = 0.400000
    //     0xbd6a24: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbd6a28: r0 = withOpacity()
    //     0xbd6a28: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd6a2c: r16 = 12.000000
    //     0xbd6a2c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbd6a30: ldr             x16, [x16, #0x9e8]
    // 0xbd6a34: stp             x0, x16, [SP]
    // 0xbd6a38: ldur            x1, [fp, #-0x18]
    // 0xbd6a3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd6a3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd6a40: ldr             x4, [x4, #0xaa0]
    // 0xbd6a44: r0 = copyWith()
    //     0xbd6a44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd6a48: stur            x0, [fp, #-0x18]
    // 0xbd6a4c: r0 = Text()
    //     0xbd6a4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd6a50: mov             x3, x0
    // 0xbd6a54: ldur            x0, [fp, #-0x10]
    // 0xbd6a58: stur            x3, [fp, #-0x28]
    // 0xbd6a5c: StoreField: r3->field_b = r0
    //     0xbd6a5c: stur            w0, [x3, #0xb]
    // 0xbd6a60: ldur            x0, [fp, #-0x18]
    // 0xbd6a64: StoreField: r3->field_13 = r0
    //     0xbd6a64: stur            w0, [x3, #0x13]
    // 0xbd6a68: r1 = Null
    //     0xbd6a68: mov             x1, NULL
    // 0xbd6a6c: r2 = 8
    //     0xbd6a6c: movz            x2, #0x8
    // 0xbd6a70: r0 = AllocateArray()
    //     0xbd6a70: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd6a74: mov             x2, x0
    // 0xbd6a78: ldur            x0, [fp, #-0x20]
    // 0xbd6a7c: stur            x2, [fp, #-0x10]
    // 0xbd6a80: StoreField: r2->field_f = r0
    //     0xbd6a80: stur            w0, [x2, #0xf]
    // 0xbd6a84: ldur            x0, [fp, #-0x30]
    // 0xbd6a88: StoreField: r2->field_13 = r0
    //     0xbd6a88: stur            w0, [x2, #0x13]
    // 0xbd6a8c: r16 = Instance_Spacer
    //     0xbd6a8c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbd6a90: ldr             x16, [x16, #0xf0]
    // 0xbd6a94: ArrayStore: r2[0] = r16  ; List_4
    //     0xbd6a94: stur            w16, [x2, #0x17]
    // 0xbd6a98: ldur            x0, [fp, #-0x28]
    // 0xbd6a9c: StoreField: r2->field_1b = r0
    //     0xbd6a9c: stur            w0, [x2, #0x1b]
    // 0xbd6aa0: r1 = <Widget>
    //     0xbd6aa0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd6aa4: r0 = AllocateGrowableArray()
    //     0xbd6aa4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd6aa8: mov             x1, x0
    // 0xbd6aac: ldur            x0, [fp, #-0x10]
    // 0xbd6ab0: stur            x1, [fp, #-0x18]
    // 0xbd6ab4: StoreField: r1->field_f = r0
    //     0xbd6ab4: stur            w0, [x1, #0xf]
    // 0xbd6ab8: r0 = 8
    //     0xbd6ab8: movz            x0, #0x8
    // 0xbd6abc: StoreField: r1->field_b = r0
    //     0xbd6abc: stur            w0, [x1, #0xb]
    // 0xbd6ac0: r0 = Row()
    //     0xbd6ac0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbd6ac4: r1 = Instance_Axis
    //     0xbd6ac4: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd6ac8: StoreField: r0->field_f = r1
    //     0xbd6ac8: stur            w1, [x0, #0xf]
    // 0xbd6acc: r1 = Instance_MainAxisAlignment
    //     0xbd6acc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd6ad0: ldr             x1, [x1, #0xa08]
    // 0xbd6ad4: StoreField: r0->field_13 = r1
    //     0xbd6ad4: stur            w1, [x0, #0x13]
    // 0xbd6ad8: r1 = Instance_MainAxisSize
    //     0xbd6ad8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd6adc: ldr             x1, [x1, #0xa10]
    // 0xbd6ae0: ArrayStore: r0[0] = r1  ; List_4
    //     0xbd6ae0: stur            w1, [x0, #0x17]
    // 0xbd6ae4: r1 = Instance_CrossAxisAlignment
    //     0xbd6ae4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd6ae8: ldr             x1, [x1, #0xa18]
    // 0xbd6aec: StoreField: r0->field_1b = r1
    //     0xbd6aec: stur            w1, [x0, #0x1b]
    // 0xbd6af0: r1 = Instance_VerticalDirection
    //     0xbd6af0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd6af4: ldr             x1, [x1, #0xa20]
    // 0xbd6af8: StoreField: r0->field_23 = r1
    //     0xbd6af8: stur            w1, [x0, #0x23]
    // 0xbd6afc: r1 = Instance_Clip
    //     0xbd6afc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd6b00: ldr             x1, [x1, #0x38]
    // 0xbd6b04: StoreField: r0->field_2b = r1
    //     0xbd6b04: stur            w1, [x0, #0x2b]
    // 0xbd6b08: StoreField: r0->field_2f = rZR
    //     0xbd6b08: stur            xzr, [x0, #0x2f]
    // 0xbd6b0c: ldur            x1, [fp, #-0x18]
    // 0xbd6b10: StoreField: r0->field_b = r1
    //     0xbd6b10: stur            w1, [x0, #0xb]
    // 0xbd6b14: LeaveFrame
    //     0xbd6b14: mov             SP, fp
    //     0xbd6b18: ldp             fp, lr, [SP], #0x10
    // 0xbd6b1c: ret
    //     0xbd6b1c: ret             
    // 0xbd6b20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd6b20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd6b24: b               #0xbd67f0
    // 0xbd6b28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd6b28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd6b2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd6b2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd6b30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6b30: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6b34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd6b34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd6b38: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6b38: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbd6b3c, size: 0x3a4
    // 0xbd6b3c: EnterFrame
    //     0xbd6b3c: stp             fp, lr, [SP, #-0x10]!
    //     0xbd6b40: mov             fp, SP
    // 0xbd6b44: AllocStack(0x40)
    //     0xbd6b44: sub             SP, SP, #0x40
    // 0xbd6b48: SetupParameters()
    //     0xbd6b48: ldr             x0, [fp, #0x20]
    //     0xbd6b4c: ldur            w4, [x0, #0x17]
    //     0xbd6b50: add             x4, x4, HEAP, lsl #32
    //     0xbd6b54: stur            x4, [fp, #-0x10]
    // 0xbd6b58: CheckStackOverflow
    //     0xbd6b58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd6b5c: cmp             SP, x16
    //     0xbd6b60: b.ls            #0xbd6ec4
    // 0xbd6b64: LoadField: r1 = r4->field_f
    //     0xbd6b64: ldur            w1, [x4, #0xf]
    // 0xbd6b68: DecompressPointer r1
    //     0xbd6b68: add             x1, x1, HEAP, lsl #32
    // 0xbd6b6c: LoadField: r0 = r1->field_b
    //     0xbd6b6c: ldur            w0, [x1, #0xb]
    // 0xbd6b70: DecompressPointer r0
    //     0xbd6b70: add             x0, x0, HEAP, lsl #32
    // 0xbd6b74: cmp             w0, NULL
    // 0xbd6b78: b.eq            #0xbd6ecc
    // 0xbd6b7c: LoadField: r2 = r0->field_b
    //     0xbd6b7c: ldur            w2, [x0, #0xb]
    // 0xbd6b80: DecompressPointer r2
    //     0xbd6b80: add             x2, x2, HEAP, lsl #32
    // 0xbd6b84: ldr             x0, [fp, #0x10]
    // 0xbd6b88: r6 = LoadInt32Instr(r0)
    //     0xbd6b88: sbfx            x6, x0, #1, #0x1f
    //     0xbd6b8c: tbz             w0, #0, #0xbd6b94
    //     0xbd6b90: ldur            x6, [x0, #7]
    // 0xbd6b94: mov             x5, x6
    // 0xbd6b98: stur            x6, [fp, #-8]
    // 0xbd6b9c: r3 = Instance_ItemFilterType
    //     0xbd6b9c: add             x3, PP, #0x53, lsl #12  ; [pp+0x53da0] Obj!ItemFilterType@d75301
    //     0xbd6ba0: ldr             x3, [x3, #0xda0]
    // 0xbd6ba4: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xbd6ba4: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xbd6ba8: r0 = _productBuildItem()
    //     0xbd6ba8: bl              #0xbd5c74  ; [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::_productBuildItem
    // 0xbd6bac: mov             x3, x0
    // 0xbd6bb0: ldur            x2, [fp, #-0x10]
    // 0xbd6bb4: stur            x3, [fp, #-0x20]
    // 0xbd6bb8: LoadField: r0 = r2->field_f
    //     0xbd6bb8: ldur            w0, [x2, #0xf]
    // 0xbd6bbc: DecompressPointer r0
    //     0xbd6bbc: add             x0, x0, HEAP, lsl #32
    // 0xbd6bc0: LoadField: r1 = r0->field_b
    //     0xbd6bc0: ldur            w1, [x0, #0xb]
    // 0xbd6bc4: DecompressPointer r1
    //     0xbd6bc4: add             x1, x1, HEAP, lsl #32
    // 0xbd6bc8: cmp             w1, NULL
    // 0xbd6bcc: b.eq            #0xbd6ed0
    // 0xbd6bd0: LoadField: r0 = r1->field_b
    //     0xbd6bd0: ldur            w0, [x1, #0xb]
    // 0xbd6bd4: DecompressPointer r0
    //     0xbd6bd4: add             x0, x0, HEAP, lsl #32
    // 0xbd6bd8: cmp             w0, NULL
    // 0xbd6bdc: b.ne            #0xbd6bec
    // 0xbd6be0: ldur            x5, [fp, #-8]
    // 0xbd6be4: r0 = Null
    //     0xbd6be4: mov             x0, NULL
    // 0xbd6be8: b               #0xbd6c40
    // 0xbd6bec: LoadField: r4 = r0->field_7
    //     0xbd6bec: ldur            w4, [x0, #7]
    // 0xbd6bf0: DecompressPointer r4
    //     0xbd6bf0: add             x4, x4, HEAP, lsl #32
    // 0xbd6bf4: cmp             w4, NULL
    // 0xbd6bf8: b.ne            #0xbd6c08
    // 0xbd6bfc: ldur            x5, [fp, #-8]
    // 0xbd6c00: r0 = Null
    //     0xbd6c00: mov             x0, NULL
    // 0xbd6c04: b               #0xbd6c40
    // 0xbd6c08: ldur            x5, [fp, #-8]
    // 0xbd6c0c: LoadField: r0 = r4->field_b
    //     0xbd6c0c: ldur            w0, [x4, #0xb]
    // 0xbd6c10: r1 = LoadInt32Instr(r0)
    //     0xbd6c10: sbfx            x1, x0, #1, #0x1f
    // 0xbd6c14: mov             x0, x1
    // 0xbd6c18: mov             x1, x5
    // 0xbd6c1c: cmp             x1, x0
    // 0xbd6c20: b.hs            #0xbd6ed4
    // 0xbd6c24: LoadField: r0 = r4->field_f
    //     0xbd6c24: ldur            w0, [x4, #0xf]
    // 0xbd6c28: DecompressPointer r0
    //     0xbd6c28: add             x0, x0, HEAP, lsl #32
    // 0xbd6c2c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbd6c2c: add             x16, x0, x5, lsl #2
    //     0xbd6c30: ldur            w1, [x16, #0xf]
    // 0xbd6c34: DecompressPointer r1
    //     0xbd6c34: add             x1, x1, HEAP, lsl #32
    // 0xbd6c38: LoadField: r0 = r1->field_f
    //     0xbd6c38: ldur            w0, [x1, #0xf]
    // 0xbd6c3c: DecompressPointer r0
    //     0xbd6c3c: add             x0, x0, HEAP, lsl #32
    // 0xbd6c40: cmp             w0, NULL
    // 0xbd6c44: b.ne            #0xbd6c4c
    // 0xbd6c48: r0 = ""
    //     0xbd6c48: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd6c4c: ldr             x1, [fp, #0x18]
    // 0xbd6c50: stur            x0, [fp, #-0x18]
    // 0xbd6c54: r0 = of()
    //     0xbd6c54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd6c58: LoadField: r1 = r0->field_87
    //     0xbd6c58: ldur            w1, [x0, #0x87]
    // 0xbd6c5c: DecompressPointer r1
    //     0xbd6c5c: add             x1, x1, HEAP, lsl #32
    // 0xbd6c60: LoadField: r0 = r1->field_2b
    //     0xbd6c60: ldur            w0, [x1, #0x2b]
    // 0xbd6c64: DecompressPointer r0
    //     0xbd6c64: add             x0, x0, HEAP, lsl #32
    // 0xbd6c68: r16 = 14.000000
    //     0xbd6c68: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd6c6c: ldr             x16, [x16, #0x1d8]
    // 0xbd6c70: r30 = Instance_Color
    //     0xbd6c70: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd6c74: stp             lr, x16, [SP]
    // 0xbd6c78: mov             x1, x0
    // 0xbd6c7c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd6c7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd6c80: ldr             x4, [x4, #0xaa0]
    // 0xbd6c84: r0 = copyWith()
    //     0xbd6c84: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd6c88: stur            x0, [fp, #-0x28]
    // 0xbd6c8c: r0 = Text()
    //     0xbd6c8c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd6c90: mov             x2, x0
    // 0xbd6c94: ldur            x0, [fp, #-0x18]
    // 0xbd6c98: stur            x2, [fp, #-0x30]
    // 0xbd6c9c: StoreField: r2->field_b = r0
    //     0xbd6c9c: stur            w0, [x2, #0xb]
    // 0xbd6ca0: ldur            x0, [fp, #-0x28]
    // 0xbd6ca4: StoreField: r2->field_13 = r0
    //     0xbd6ca4: stur            w0, [x2, #0x13]
    // 0xbd6ca8: r1 = <FlexParentData>
    //     0xbd6ca8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbd6cac: ldr             x1, [x1, #0xe00]
    // 0xbd6cb0: r0 = Flexible()
    //     0xbd6cb0: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xbd6cb4: mov             x2, x0
    // 0xbd6cb8: r0 = 1
    //     0xbd6cb8: movz            x0, #0x1
    // 0xbd6cbc: stur            x2, [fp, #-0x18]
    // 0xbd6cc0: StoreField: r2->field_13 = r0
    //     0xbd6cc0: stur            x0, [x2, #0x13]
    // 0xbd6cc4: r0 = Instance_FlexFit
    //     0xbd6cc4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbd6cc8: ldr             x0, [x0, #0xe08]
    // 0xbd6ccc: StoreField: r2->field_1b = r0
    //     0xbd6ccc: stur            w0, [x2, #0x1b]
    // 0xbd6cd0: ldur            x0, [fp, #-0x30]
    // 0xbd6cd4: StoreField: r2->field_b = r0
    //     0xbd6cd4: stur            w0, [x2, #0xb]
    // 0xbd6cd8: ldur            x0, [fp, #-0x10]
    // 0xbd6cdc: LoadField: r1 = r0->field_f
    //     0xbd6cdc: ldur            w1, [x0, #0xf]
    // 0xbd6ce0: DecompressPointer r1
    //     0xbd6ce0: add             x1, x1, HEAP, lsl #32
    // 0xbd6ce4: LoadField: r0 = r1->field_b
    //     0xbd6ce4: ldur            w0, [x1, #0xb]
    // 0xbd6ce8: DecompressPointer r0
    //     0xbd6ce8: add             x0, x0, HEAP, lsl #32
    // 0xbd6cec: cmp             w0, NULL
    // 0xbd6cf0: b.eq            #0xbd6ed8
    // 0xbd6cf4: LoadField: r1 = r0->field_b
    //     0xbd6cf4: ldur            w1, [x0, #0xb]
    // 0xbd6cf8: DecompressPointer r1
    //     0xbd6cf8: add             x1, x1, HEAP, lsl #32
    // 0xbd6cfc: cmp             w1, NULL
    // 0xbd6d00: b.ne            #0xbd6d0c
    // 0xbd6d04: r0 = Null
    //     0xbd6d04: mov             x0, NULL
    // 0xbd6d08: b               #0xbd6d88
    // 0xbd6d0c: LoadField: r3 = r1->field_7
    //     0xbd6d0c: ldur            w3, [x1, #7]
    // 0xbd6d10: DecompressPointer r3
    //     0xbd6d10: add             x3, x3, HEAP, lsl #32
    // 0xbd6d14: cmp             w3, NULL
    // 0xbd6d18: b.ne            #0xbd6d24
    // 0xbd6d1c: r0 = Null
    //     0xbd6d1c: mov             x0, NULL
    // 0xbd6d20: b               #0xbd6d88
    // 0xbd6d24: ldur            x4, [fp, #-8]
    // 0xbd6d28: LoadField: r0 = r3->field_b
    //     0xbd6d28: ldur            w0, [x3, #0xb]
    // 0xbd6d2c: r1 = LoadInt32Instr(r0)
    //     0xbd6d2c: sbfx            x1, x0, #1, #0x1f
    // 0xbd6d30: mov             x0, x1
    // 0xbd6d34: mov             x1, x4
    // 0xbd6d38: cmp             x1, x0
    // 0xbd6d3c: b.hs            #0xbd6edc
    // 0xbd6d40: LoadField: r0 = r3->field_f
    //     0xbd6d40: ldur            w0, [x3, #0xf]
    // 0xbd6d44: DecompressPointer r0
    //     0xbd6d44: add             x0, x0, HEAP, lsl #32
    // 0xbd6d48: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbd6d48: add             x16, x0, x4, lsl #2
    //     0xbd6d4c: ldur            w1, [x16, #0xf]
    // 0xbd6d50: DecompressPointer r1
    //     0xbd6d50: add             x1, x1, HEAP, lsl #32
    // 0xbd6d54: LoadField: r0 = r1->field_b
    //     0xbd6d54: ldur            w0, [x1, #0xb]
    // 0xbd6d58: DecompressPointer r0
    //     0xbd6d58: add             x0, x0, HEAP, lsl #32
    // 0xbd6d5c: r1 = 60
    //     0xbd6d5c: movz            x1, #0x3c
    // 0xbd6d60: branchIfSmi(r0, 0xbd6d6c)
    //     0xbd6d60: tbz             w0, #0, #0xbd6d6c
    // 0xbd6d64: r1 = LoadClassIdInstr(r0)
    //     0xbd6d64: ldur            x1, [x0, #-1]
    //     0xbd6d68: ubfx            x1, x1, #0xc, #0x14
    // 0xbd6d6c: str             x0, [SP]
    // 0xbd6d70: mov             x0, x1
    // 0xbd6d74: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbd6d74: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbd6d78: r0 = GDT[cid_x0 + 0x2700]()
    //     0xbd6d78: movz            x17, #0x2700
    //     0xbd6d7c: add             lr, x0, x17
    //     0xbd6d80: ldr             lr, [x21, lr, lsl #3]
    //     0xbd6d84: blr             lr
    // 0xbd6d88: cmp             w0, NULL
    // 0xbd6d8c: b.ne            #0xbd6d98
    // 0xbd6d90: r3 = ""
    //     0xbd6d90: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd6d94: b               #0xbd6d9c
    // 0xbd6d98: mov             x3, x0
    // 0xbd6d9c: ldur            x2, [fp, #-0x20]
    // 0xbd6da0: ldur            x0, [fp, #-0x18]
    // 0xbd6da4: ldr             x1, [fp, #0x18]
    // 0xbd6da8: stur            x3, [fp, #-0x10]
    // 0xbd6dac: r0 = of()
    //     0xbd6dac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd6db0: LoadField: r1 = r0->field_87
    //     0xbd6db0: ldur            w1, [x0, #0x87]
    // 0xbd6db4: DecompressPointer r1
    //     0xbd6db4: add             x1, x1, HEAP, lsl #32
    // 0xbd6db8: LoadField: r0 = r1->field_2b
    //     0xbd6db8: ldur            w0, [x1, #0x2b]
    // 0xbd6dbc: DecompressPointer r0
    //     0xbd6dbc: add             x0, x0, HEAP, lsl #32
    // 0xbd6dc0: stur            x0, [fp, #-0x28]
    // 0xbd6dc4: r1 = Instance_Color
    //     0xbd6dc4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd6dc8: d0 = 0.400000
    //     0xbd6dc8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbd6dcc: r0 = withOpacity()
    //     0xbd6dcc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd6dd0: r16 = 12.000000
    //     0xbd6dd0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbd6dd4: ldr             x16, [x16, #0x9e8]
    // 0xbd6dd8: stp             x0, x16, [SP]
    // 0xbd6ddc: ldur            x1, [fp, #-0x28]
    // 0xbd6de0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd6de0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd6de4: ldr             x4, [x4, #0xaa0]
    // 0xbd6de8: r0 = copyWith()
    //     0xbd6de8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd6dec: stur            x0, [fp, #-0x28]
    // 0xbd6df0: r0 = Text()
    //     0xbd6df0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd6df4: mov             x3, x0
    // 0xbd6df8: ldur            x0, [fp, #-0x10]
    // 0xbd6dfc: stur            x3, [fp, #-0x30]
    // 0xbd6e00: StoreField: r3->field_b = r0
    //     0xbd6e00: stur            w0, [x3, #0xb]
    // 0xbd6e04: ldur            x0, [fp, #-0x28]
    // 0xbd6e08: StoreField: r3->field_13 = r0
    //     0xbd6e08: stur            w0, [x3, #0x13]
    // 0xbd6e0c: r1 = Null
    //     0xbd6e0c: mov             x1, NULL
    // 0xbd6e10: r2 = 8
    //     0xbd6e10: movz            x2, #0x8
    // 0xbd6e14: r0 = AllocateArray()
    //     0xbd6e14: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd6e18: mov             x2, x0
    // 0xbd6e1c: ldur            x0, [fp, #-0x20]
    // 0xbd6e20: stur            x2, [fp, #-0x10]
    // 0xbd6e24: StoreField: r2->field_f = r0
    //     0xbd6e24: stur            w0, [x2, #0xf]
    // 0xbd6e28: ldur            x0, [fp, #-0x18]
    // 0xbd6e2c: StoreField: r2->field_13 = r0
    //     0xbd6e2c: stur            w0, [x2, #0x13]
    // 0xbd6e30: r16 = Instance_Spacer
    //     0xbd6e30: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbd6e34: ldr             x16, [x16, #0xf0]
    // 0xbd6e38: ArrayStore: r2[0] = r16  ; List_4
    //     0xbd6e38: stur            w16, [x2, #0x17]
    // 0xbd6e3c: ldur            x0, [fp, #-0x30]
    // 0xbd6e40: StoreField: r2->field_1b = r0
    //     0xbd6e40: stur            w0, [x2, #0x1b]
    // 0xbd6e44: r1 = <Widget>
    //     0xbd6e44: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd6e48: r0 = AllocateGrowableArray()
    //     0xbd6e48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd6e4c: mov             x1, x0
    // 0xbd6e50: ldur            x0, [fp, #-0x10]
    // 0xbd6e54: stur            x1, [fp, #-0x18]
    // 0xbd6e58: StoreField: r1->field_f = r0
    //     0xbd6e58: stur            w0, [x1, #0xf]
    // 0xbd6e5c: r0 = 8
    //     0xbd6e5c: movz            x0, #0x8
    // 0xbd6e60: StoreField: r1->field_b = r0
    //     0xbd6e60: stur            w0, [x1, #0xb]
    // 0xbd6e64: r0 = Row()
    //     0xbd6e64: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbd6e68: r1 = Instance_Axis
    //     0xbd6e68: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd6e6c: StoreField: r0->field_f = r1
    //     0xbd6e6c: stur            w1, [x0, #0xf]
    // 0xbd6e70: r1 = Instance_MainAxisAlignment
    //     0xbd6e70: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd6e74: ldr             x1, [x1, #0xa08]
    // 0xbd6e78: StoreField: r0->field_13 = r1
    //     0xbd6e78: stur            w1, [x0, #0x13]
    // 0xbd6e7c: r1 = Instance_MainAxisSize
    //     0xbd6e7c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd6e80: ldr             x1, [x1, #0xa10]
    // 0xbd6e84: ArrayStore: r0[0] = r1  ; List_4
    //     0xbd6e84: stur            w1, [x0, #0x17]
    // 0xbd6e88: r1 = Instance_CrossAxisAlignment
    //     0xbd6e88: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd6e8c: ldr             x1, [x1, #0xa18]
    // 0xbd6e90: StoreField: r0->field_1b = r1
    //     0xbd6e90: stur            w1, [x0, #0x1b]
    // 0xbd6e94: r1 = Instance_VerticalDirection
    //     0xbd6e94: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd6e98: ldr             x1, [x1, #0xa20]
    // 0xbd6e9c: StoreField: r0->field_23 = r1
    //     0xbd6e9c: stur            w1, [x0, #0x23]
    // 0xbd6ea0: r1 = Instance_Clip
    //     0xbd6ea0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd6ea4: ldr             x1, [x1, #0x38]
    // 0xbd6ea8: StoreField: r0->field_2b = r1
    //     0xbd6ea8: stur            w1, [x0, #0x2b]
    // 0xbd6eac: StoreField: r0->field_2f = rZR
    //     0xbd6eac: stur            xzr, [x0, #0x2f]
    // 0xbd6eb0: ldur            x1, [fp, #-0x18]
    // 0xbd6eb4: StoreField: r0->field_b = r1
    //     0xbd6eb4: stur            w1, [x0, #0xb]
    // 0xbd6eb8: LeaveFrame
    //     0xbd6eb8: mov             SP, fp
    //     0xbd6ebc: ldp             fp, lr, [SP], #0x10
    // 0xbd6ec0: ret
    //     0xbd6ec0: ret             
    // 0xbd6ec4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd6ec4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd6ec8: b               #0xbd6b64
    // 0xbd6ecc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd6ecc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd6ed0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd6ed0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd6ed4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6ed4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6ed8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd6ed8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd6edc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6edc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbd6ee0, size: 0x98
    // 0xbd6ee0: EnterFrame
    //     0xbd6ee0: stp             fp, lr, [SP, #-0x10]!
    //     0xbd6ee4: mov             fp, SP
    // 0xbd6ee8: AllocStack(0x10)
    //     0xbd6ee8: sub             SP, SP, #0x10
    // 0xbd6eec: SetupParameters()
    //     0xbd6eec: ldr             x0, [fp, #0x10]
    //     0xbd6ef0: ldur            w2, [x0, #0x17]
    //     0xbd6ef4: add             x2, x2, HEAP, lsl #32
    //     0xbd6ef8: stur            x2, [fp, #-8]
    // 0xbd6efc: CheckStackOverflow
    //     0xbd6efc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd6f00: cmp             SP, x16
    //     0xbd6f04: b.ls            #0xbd6f6c
    // 0xbd6f08: LoadField: r0 = r2->field_f
    //     0xbd6f08: ldur            w0, [x2, #0xf]
    // 0xbd6f0c: DecompressPointer r0
    //     0xbd6f0c: add             x0, x0, HEAP, lsl #32
    // 0xbd6f10: LoadField: r1 = r0->field_13
    //     0xbd6f10: ldur            w1, [x0, #0x13]
    // 0xbd6f14: DecompressPointer r1
    //     0xbd6f14: add             x1, x1, HEAP, lsl #32
    // 0xbd6f18: r0 = clear()
    //     0xbd6f18: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xbd6f1c: ldur            x0, [fp, #-8]
    // 0xbd6f20: LoadField: r1 = r0->field_f
    //     0xbd6f20: ldur            w1, [x0, #0xf]
    // 0xbd6f24: DecompressPointer r1
    //     0xbd6f24: add             x1, x1, HEAP, lsl #32
    // 0xbd6f28: LoadField: r0 = r1->field_b
    //     0xbd6f28: ldur            w0, [x1, #0xb]
    // 0xbd6f2c: DecompressPointer r0
    //     0xbd6f2c: add             x0, x0, HEAP, lsl #32
    // 0xbd6f30: cmp             w0, NULL
    // 0xbd6f34: b.eq            #0xbd6f74
    // 0xbd6f38: LoadField: r1 = r0->field_13
    //     0xbd6f38: ldur            w1, [x0, #0x13]
    // 0xbd6f3c: DecompressPointer r1
    //     0xbd6f3c: add             x1, x1, HEAP, lsl #32
    // 0xbd6f40: str             x1, [SP]
    // 0xbd6f44: r4 = 0
    //     0xbd6f44: movz            x4, #0
    // 0xbd6f48: ldr             x0, [SP]
    // 0xbd6f4c: r16 = UnlinkedCall_0x613b5c
    //     0xbd6f4c: add             x16, PP, #0x53, lsl #12  ; [pp+0x53da8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbd6f50: add             x16, x16, #0xda8
    // 0xbd6f54: ldp             x5, lr, [x16]
    // 0xbd6f58: blr             lr
    // 0xbd6f5c: r0 = Null
    //     0xbd6f5c: mov             x0, NULL
    // 0xbd6f60: LeaveFrame
    //     0xbd6f60: mov             SP, fp
    //     0xbd6f64: ldp             fp, lr, [SP], #0x10
    // 0xbd6f68: ret
    //     0xbd6f68: ret             
    // 0xbd6f6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd6f6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd6f70: b               #0xbd6f08
    // 0xbd6f74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd6f74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4006, size: 0x18, field offset: 0xc
//   const constructor, 
class CollectionFilter extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80740, size: 0x48
    // 0xc80740: EnterFrame
    //     0xc80740: stp             fp, lr, [SP, #-0x10]!
    //     0xc80744: mov             fp, SP
    // 0xc80748: AllocStack(0x8)
    //     0xc80748: sub             SP, SP, #8
    // 0xc8074c: CheckStackOverflow
    //     0xc8074c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc80750: cmp             SP, x16
    //     0xc80754: b.ls            #0xc80780
    // 0xc80758: r1 = <CollectionFilter>
    //     0xc80758: add             x1, PP, #0x48, lsl #12  ; [pp+0x485f8] TypeArguments: <CollectionFilter>
    //     0xc8075c: ldr             x1, [x1, #0x5f8]
    // 0xc80760: r0 = _CollectionFilterState()
    //     0xc80760: bl              #0xc80788  ; Allocate_CollectionFilterStateStub -> _CollectionFilterState (size=0x34)
    // 0xc80764: mov             x1, x0
    // 0xc80768: stur            x0, [fp, #-8]
    // 0xc8076c: r0 = _CollectionFilterState()
    //     0xc8076c: bl              #0xc7b32c  ; [package:customer_app/app/presentation/views/basic/collections/widgets/collection_filter.dart] _CollectionFilterState::_CollectionFilterState
    // 0xc80770: ldur            x0, [fp, #-8]
    // 0xc80774: LeaveFrame
    //     0xc80774: mov             SP, fp
    //     0xc80778: ldp             fp, lr, [SP], #0x10
    // 0xc8077c: ret
    //     0xc8077c: ret             
    // 0xc80780: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc80780: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc80784: b               #0xc80758
  }
}

// class id: 7081, size: 0x14, field offset: 0x14
enum ItemFilterType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0x15856f0, size: 0x64
    // 0x15856f0: EnterFrame
    //     0x15856f0: stp             fp, lr, [SP, #-0x10]!
    //     0x15856f4: mov             fp, SP
    // 0x15856f8: AllocStack(0x10)
    //     0x15856f8: sub             SP, SP, #0x10
    // 0x15856fc: SetupParameters(ItemFilterType this /* r1 => r0, fp-0x8 */)
    //     0x15856fc: mov             x0, x1
    //     0x1585700: stur            x1, [fp, #-8]
    // 0x1585704: CheckStackOverflow
    //     0x1585704: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1585708: cmp             SP, x16
    //     0x158570c: b.ls            #0x158574c
    // 0x1585710: r1 = Null
    //     0x1585710: mov             x1, NULL
    // 0x1585714: r2 = 4
    //     0x1585714: movz            x2, #0x4
    // 0x1585718: r0 = AllocateArray()
    //     0x1585718: bl              #0x16f7198  ; AllocateArrayStub
    // 0x158571c: r16 = "ItemFilterType."
    //     0x158571c: add             x16, PP, #0x61, lsl #12  ; [pp+0x61d08] "ItemFilterType."
    //     0x1585720: ldr             x16, [x16, #0xd08]
    // 0x1585724: StoreField: r0->field_f = r16
    //     0x1585724: stur            w16, [x0, #0xf]
    // 0x1585728: ldur            x1, [fp, #-8]
    // 0x158572c: LoadField: r2 = r1->field_f
    //     0x158572c: ldur            w2, [x1, #0xf]
    // 0x1585730: DecompressPointer r2
    //     0x1585730: add             x2, x2, HEAP, lsl #32
    // 0x1585734: StoreField: r0->field_13 = r2
    //     0x1585734: stur            w2, [x0, #0x13]
    // 0x1585738: str             x0, [SP]
    // 0x158573c: r0 = _interpolate()
    //     0x158573c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1585740: LeaveFrame
    //     0x1585740: mov             SP, fp
    //     0x1585744: ldp             fp, lr, [SP], #0x10
    // 0x1585748: ret
    //     0x1585748: ret             
    // 0x158574c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x158574c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1585750: b               #0x1585710
  }
}
