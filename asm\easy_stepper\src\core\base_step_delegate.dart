// lib: , url: package:easy_stepper/src/core/base_step_delegate.dart

// class id: 1049626, size: 0x8
class :: {
}

// class id: 4968, size: 0x20, field offset: 0x10
class BaseStepDelegate extends MultiChildLayoutDelegate {

  _ shouldRelayout(/* No info */) {
    // ** addr: 0x1692dac, size: 0x5c
    // 0x1692dac: EnterFrame
    //     0x1692dac: stp             fp, lr, [SP, #-0x10]!
    //     0x1692db0: mov             fp, SP
    // 0x1692db4: mov             x0, x2
    // 0x1692db8: mov             x4, x1
    // 0x1692dbc: mov             x3, x2
    // 0x1692dc0: r2 = Null
    //     0x1692dc0: mov             x2, NULL
    // 0x1692dc4: r1 = Null
    //     0x1692dc4: mov             x1, NULL
    // 0x1692dc8: r4 = 60
    //     0x1692dc8: movz            x4, #0x3c
    // 0x1692dcc: branchIfSmi(r0, 0x1692dd8)
    //     0x1692dcc: tbz             w0, #0, #0x1692dd8
    // 0x1692dd0: r4 = LoadClassIdInstr(r0)
    //     0x1692dd0: ldur            x4, [x0, #-1]
    //     0x1692dd4: ubfx            x4, x4, #0xc, #0x14
    // 0x1692dd8: r17 = 4968
    //     0x1692dd8: movz            x17, #0x1368
    // 0x1692ddc: cmp             x4, x17
    // 0x1692de0: b.eq            #0x1692df8
    // 0x1692de4: r8 = BaseStepDelegate
    //     0x1692de4: add             x8, PP, #0x60, lsl #12  ; [pp+0x60198] Type: BaseStepDelegate
    //     0x1692de8: ldr             x8, [x8, #0x198]
    // 0x1692dec: r3 = Null
    //     0x1692dec: add             x3, PP, #0x71, lsl #12  ; [pp+0x71440] Null
    //     0x1692df0: ldr             x3, [x3, #0x440]
    // 0x1692df4: r0 = DefaultTypeTest()
    //     0x1692df4: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x1692df8: r0 = false
    //     0x1692df8: add             x0, NULL, #0x30  ; false
    // 0x1692dfc: LeaveFrame
    //     0x1692dfc: mov             SP, fp
    //     0x1692e00: ldp             fp, lr, [SP], #0x10
    // 0x1692e04: ret
    //     0x1692e04: ret             
  }
  _ performLayout(/* No info */) {
    // ** addr: 0x16950fc, size: 0x128
    // 0x16950fc: EnterFrame
    //     0x16950fc: stp             fp, lr, [SP, #-0x10]!
    //     0x1695100: mov             fp, SP
    // 0x1695104: AllocStack(0x20)
    //     0x1695104: sub             SP, SP, #0x20
    // 0x1695108: SetupParameters(BaseStepDelegate this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1695108: stur            x1, [fp, #-8]
    //     0x169510c: stur            x2, [fp, #-0x10]
    // 0x1695110: CheckStackOverflow
    //     0x1695110: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1695114: cmp             SP, x16
    //     0x1695118: b.ls            #0x169521c
    // 0x169511c: r0 = BoxConstraints()
    //     0x169511c: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x1695120: StoreField: r0->field_7 = rZR
    //     0x1695120: stur            xzr, [x0, #7]
    // 0x1695124: ldur            x1, [fp, #-0x10]
    // 0x1695128: LoadField: d0 = r1->field_7
    //     0x1695128: ldur            d0, [x1, #7]
    // 0x169512c: stur            d0, [fp, #-0x18]
    // 0x1695130: StoreField: r0->field_f = d0
    //     0x1695130: stur            d0, [x0, #0xf]
    // 0x1695134: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1695134: stur            xzr, [x0, #0x17]
    // 0x1695138: LoadField: d1 = r1->field_f
    //     0x1695138: ldur            d1, [x1, #0xf]
    // 0x169513c: StoreField: r0->field_1f = d1
    //     0x169513c: stur            d1, [x0, #0x1f]
    // 0x1695140: ldur            x1, [fp, #-8]
    // 0x1695144: mov             x3, x0
    // 0x1695148: r2 = Instance_BaseStepElem
    //     0x1695148: add             x2, PP, #0x6e, lsl #12  ; [pp+0x6ec78] Obj!BaseStepElem@d75021
    //     0x169514c: ldr             x2, [x2, #0xc78]
    // 0x1695150: r0 = layoutChild()
    //     0x1695150: bl              #0x1695394  ; [package:flutter/src/rendering/custom_layout.dart] MultiChildLayoutDelegate::layoutChild
    // 0x1695154: ldur            d0, [fp, #-0x18]
    // 0x1695158: d1 = 20.000000
    //     0x1695158: fmov            d1, #20.00000000
    // 0x169515c: fsub            d2, d0, d1
    // 0x1695160: d1 = 2.000000
    //     0x1695160: fmov            d1, #2.00000000
    // 0x1695164: fdiv            d3, d2, d1
    // 0x1695168: stur            d3, [fp, #-0x20]
    // 0x169516c: r0 = Offset()
    //     0x169516c: bl              #0x63f63c  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x1695170: ldur            d0, [fp, #-0x20]
    // 0x1695174: StoreField: r0->field_7 = d0
    //     0x1695174: stur            d0, [x0, #7]
    // 0x1695178: StoreField: r0->field_f = rZR
    //     0x1695178: stur            xzr, [x0, #0xf]
    // 0x169517c: ldur            x1, [fp, #-8]
    // 0x1695180: mov             x3, x0
    // 0x1695184: r2 = Instance_BaseStepElem
    //     0x1695184: add             x2, PP, #0x6e, lsl #12  ; [pp+0x6ec78] Obj!BaseStepElem@d75021
    //     0x1695188: ldr             x2, [x2, #0xc78]
    // 0x169518c: r0 = positionChild()
    //     0x169518c: bl              #0x16952a0  ; [package:flutter/src/rendering/custom_layout.dart] MultiChildLayoutDelegate::positionChild
    // 0x1695190: ldur            x1, [fp, #-8]
    // 0x1695194: r2 = Instance_BaseStepElem
    //     0x1695194: add             x2, PP, #0x6e, lsl #12  ; [pp+0x6ec80] Obj!BaseStepElem@d75001
    //     0x1695198: ldr             x2, [x2, #0xc80]
    // 0x169519c: r0 = hasChild()
    //     0x169519c: bl              #0x1695224  ; [package:flutter/src/rendering/custom_layout.dart] MultiChildLayoutDelegate::hasChild
    // 0x16951a0: tbnz            w0, #4, #0x169520c
    // 0x16951a4: ldur            d0, [fp, #-0x18]
    // 0x16951a8: ldur            x1, [fp, #-8]
    // 0x16951ac: r2 = Instance_BaseStepElem
    //     0x16951ac: add             x2, PP, #0x6e, lsl #12  ; [pp+0x6ec80] Obj!BaseStepElem@d75001
    //     0x16951b0: ldr             x2, [x2, #0xc80]
    // 0x16951b4: r3 = Instance_BoxConstraints
    //     0x16951b4: add             x3, PP, #0x60, lsl #12  ; [pp+0x60e50] Obj!BoxConstraints@d56571
    //     0x16951b8: ldr             x3, [x3, #0xe50]
    // 0x16951bc: r0 = layoutChild()
    //     0x16951bc: bl              #0x1695394  ; [package:flutter/src/rendering/custom_layout.dart] MultiChildLayoutDelegate::layoutChild
    // 0x16951c0: LoadField: d0 = r0->field_7
    //     0x16951c0: ldur            d0, [x0, #7]
    // 0x16951c4: fneg            d1, d0
    // 0x16951c8: d0 = 2.000000
    //     0x16951c8: fmov            d0, #2.00000000
    // 0x16951cc: fdiv            d2, d1, d0
    // 0x16951d0: ldur            d1, [fp, #-0x18]
    // 0x16951d4: fdiv            d3, d1, d0
    // 0x16951d8: fadd            d0, d2, d3
    // 0x16951dc: stur            d0, [fp, #-0x18]
    // 0x16951e0: r0 = Offset()
    //     0x16951e0: bl              #0x63f63c  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x16951e4: ldur            d0, [fp, #-0x18]
    // 0x16951e8: StoreField: r0->field_7 = d0
    //     0x16951e8: stur            d0, [x0, #7]
    // 0x16951ec: d0 = 23.500000
    //     0x16951ec: add             x17, PP, #0x71, lsl #12  ; [pp+0x71450] IMM: double(23.5) from 0x4037800000000000
    //     0x16951f0: ldr             d0, [x17, #0x450]
    // 0x16951f4: StoreField: r0->field_f = d0
    //     0x16951f4: stur            d0, [x0, #0xf]
    // 0x16951f8: ldur            x1, [fp, #-8]
    // 0x16951fc: mov             x3, x0
    // 0x1695200: r2 = Instance_BaseStepElem
    //     0x1695200: add             x2, PP, #0x6e, lsl #12  ; [pp+0x6ec80] Obj!BaseStepElem@d75001
    //     0x1695204: ldr             x2, [x2, #0xc80]
    // 0x1695208: r0 = positionChild()
    //     0x1695208: bl              #0x16952a0  ; [package:flutter/src/rendering/custom_layout.dart] MultiChildLayoutDelegate::positionChild
    // 0x169520c: r0 = Null
    //     0x169520c: mov             x0, NULL
    // 0x1695210: LeaveFrame
    //     0x1695210: mov             SP, fp
    //     0x1695214: ldp             fp, lr, [SP], #0x10
    // 0x1695218: ret
    //     0x1695218: ret             
    // 0x169521c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x169521c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1695220: b               #0x169511c
  }
}

// class id: 7073, size: 0x14, field offset: 0x14
enum BaseStepElem extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0x15859ac, size: 0x64
    // 0x15859ac: EnterFrame
    //     0x15859ac: stp             fp, lr, [SP, #-0x10]!
    //     0x15859b0: mov             fp, SP
    // 0x15859b4: AllocStack(0x10)
    //     0x15859b4: sub             SP, SP, #0x10
    // 0x15859b8: SetupParameters(BaseStepElem this /* r1 => r0, fp-0x8 */)
    //     0x15859b8: mov             x0, x1
    //     0x15859bc: stur            x1, [fp, #-8]
    // 0x15859c0: CheckStackOverflow
    //     0x15859c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15859c4: cmp             SP, x16
    //     0x15859c8: b.ls            #0x1585a08
    // 0x15859cc: r1 = Null
    //     0x15859cc: mov             x1, NULL
    // 0x15859d0: r2 = 4
    //     0x15859d0: movz            x2, #0x4
    // 0x15859d4: r0 = AllocateArray()
    //     0x15859d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15859d8: r16 = "BaseStepElem."
    //     0x15859d8: add             x16, PP, #0x71, lsl #12  ; [pp+0x71458] "BaseStepElem."
    //     0x15859dc: ldr             x16, [x16, #0x458]
    // 0x15859e0: StoreField: r0->field_f = r16
    //     0x15859e0: stur            w16, [x0, #0xf]
    // 0x15859e4: ldur            x1, [fp, #-8]
    // 0x15859e8: LoadField: r2 = r1->field_f
    //     0x15859e8: ldur            w2, [x1, #0xf]
    // 0x15859ec: DecompressPointer r2
    //     0x15859ec: add             x2, x2, HEAP, lsl #32
    // 0x15859f0: StoreField: r0->field_13 = r2
    //     0x15859f0: stur            w2, [x0, #0x13]
    // 0x15859f4: str             x0, [SP]
    // 0x15859f8: r0 = _interpolate()
    //     0x15859f8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x15859fc: LeaveFrame
    //     0x15859fc: mov             SP, fp
    //     0x1585a00: ldp             fp, lr, [SP], #0x10
    // 0x1585a04: ret
    //     0x1585a04: ret             
    // 0x1585a08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1585a08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1585a0c: b               #0x15859cc
  }
}
