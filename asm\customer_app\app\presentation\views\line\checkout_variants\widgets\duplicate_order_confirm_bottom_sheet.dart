// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/duplicate_order_confirm_bottom_sheet.dart

// class id: 1049492, size: 0x8
class :: {

  static _ bagLineThemeItem(/* No info */) {
    // ** addr: 0xbb8de4, size: 0x75c
    // 0xbb8de4: EnterFrame
    //     0xbb8de4: stp             fp, lr, [SP, #-0x10]!
    //     0xbb8de8: mov             fp, SP
    // 0xbb8dec: AllocStack(0x78)
    //     0xbb8dec: sub             SP, SP, #0x78
    // 0xbb8df0: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbb8df0: mov             x0, x1
    //     0xbb8df4: stur            x1, [fp, #-8]
    //     0xbb8df8: stur            x2, [fp, #-0x10]
    // 0xbb8dfc: CheckStackOverflow
    //     0xbb8dfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8e00: cmp             SP, x16
    //     0xbb8e04: b.ls            #0xbb9538
    // 0xbb8e08: r1 = Instance_Color
    //     0xbb8e08: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb8e0c: d0 = 0.100000
    //     0xbb8e0c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbb8e10: r0 = withOpacity()
    //     0xbb8e10: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb8e14: mov             x2, x0
    // 0xbb8e18: r1 = Null
    //     0xbb8e18: mov             x1, NULL
    // 0xbb8e1c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbb8e1c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbb8e20: r0 = Border.all()
    //     0xbb8e20: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbb8e24: stur            x0, [fp, #-0x18]
    // 0xbb8e28: r0 = BoxDecoration()
    //     0xbb8e28: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbb8e2c: mov             x3, x0
    // 0xbb8e30: ldur            x0, [fp, #-0x18]
    // 0xbb8e34: stur            x3, [fp, #-0x20]
    // 0xbb8e38: StoreField: r3->field_f = r0
    //     0xbb8e38: stur            w0, [x3, #0xf]
    // 0xbb8e3c: r0 = Instance_BoxShape
    //     0xbb8e3c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbb8e40: ldr             x0, [x0, #0x80]
    // 0xbb8e44: StoreField: r3->field_23 = r0
    //     0xbb8e44: stur            w0, [x3, #0x23]
    // 0xbb8e48: ldur            x0, [fp, #-0x10]
    // 0xbb8e4c: LoadField: r1 = r0->field_13
    //     0xbb8e4c: ldur            w1, [x0, #0x13]
    // 0xbb8e50: DecompressPointer r1
    //     0xbb8e50: add             x1, x1, HEAP, lsl #32
    // 0xbb8e54: cmp             w1, NULL
    // 0xbb8e58: b.ne            #0xbb8e64
    // 0xbb8e5c: r4 = ""
    //     0xbb8e5c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb8e60: b               #0xbb8e68
    // 0xbb8e64: mov             x4, x1
    // 0xbb8e68: stur            x4, [fp, #-0x18]
    // 0xbb8e6c: r1 = Function '<anonymous closure>': static.
    //     0xbb8e6c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54770] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbb8e70: ldr             x1, [x1, #0x770]
    // 0xbb8e74: r2 = Null
    //     0xbb8e74: mov             x2, NULL
    // 0xbb8e78: r0 = AllocateClosure()
    //     0xbb8e78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb8e7c: r1 = Function '<anonymous closure>': static.
    //     0xbb8e7c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54778] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbb8e80: ldr             x1, [x1, #0x778]
    // 0xbb8e84: r2 = Null
    //     0xbb8e84: mov             x2, NULL
    // 0xbb8e88: stur            x0, [fp, #-0x28]
    // 0xbb8e8c: r0 = AllocateClosure()
    //     0xbb8e8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb8e90: stur            x0, [fp, #-0x30]
    // 0xbb8e94: r0 = CachedNetworkImage()
    //     0xbb8e94: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbb8e98: stur            x0, [fp, #-0x38]
    // 0xbb8e9c: r16 = Instance_BoxFit
    //     0xbb8e9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbb8ea0: ldr             x16, [x16, #0x118]
    // 0xbb8ea4: r30 = 60.000000
    //     0xbb8ea4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xbb8ea8: ldr             lr, [lr, #0x110]
    // 0xbb8eac: stp             lr, x16, [SP, #0x18]
    // 0xbb8eb0: r16 = 60.000000
    //     0xbb8eb0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xbb8eb4: ldr             x16, [x16, #0x110]
    // 0xbb8eb8: ldur            lr, [fp, #-0x28]
    // 0xbb8ebc: stp             lr, x16, [SP, #8]
    // 0xbb8ec0: ldur            x16, [fp, #-0x30]
    // 0xbb8ec4: str             x16, [SP]
    // 0xbb8ec8: mov             x1, x0
    // 0xbb8ecc: ldur            x2, [fp, #-0x18]
    // 0xbb8ed0: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x2, height, 0x4, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xbb8ed0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fae0] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x2, "height", 0x4, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xbb8ed4: ldr             x4, [x4, #0xae0]
    // 0xbb8ed8: r0 = CachedNetworkImage()
    //     0xbb8ed8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbb8edc: ldur            x0, [fp, #-0x10]
    // 0xbb8ee0: LoadField: r1 = r0->field_f
    //     0xbb8ee0: ldur            w1, [x0, #0xf]
    // 0xbb8ee4: DecompressPointer r1
    //     0xbb8ee4: add             x1, x1, HEAP, lsl #32
    // 0xbb8ee8: cmp             w1, NULL
    // 0xbb8eec: b.ne            #0xbb8ef8
    // 0xbb8ef0: r3 = ""
    //     0xbb8ef0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb8ef4: b               #0xbb8efc
    // 0xbb8ef8: mov             x3, x1
    // 0xbb8efc: ldur            x2, [fp, #-0x38]
    // 0xbb8f00: ldur            x1, [fp, #-8]
    // 0xbb8f04: stur            x3, [fp, #-0x18]
    // 0xbb8f08: r0 = of()
    //     0xbb8f08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb8f0c: LoadField: r1 = r0->field_87
    //     0xbb8f0c: ldur            w1, [x0, #0x87]
    // 0xbb8f10: DecompressPointer r1
    //     0xbb8f10: add             x1, x1, HEAP, lsl #32
    // 0xbb8f14: LoadField: r0 = r1->field_2b
    //     0xbb8f14: ldur            w0, [x1, #0x2b]
    // 0xbb8f18: DecompressPointer r0
    //     0xbb8f18: add             x0, x0, HEAP, lsl #32
    // 0xbb8f1c: stur            x0, [fp, #-0x28]
    // 0xbb8f20: r1 = Instance_Color
    //     0xbb8f20: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb8f24: d0 = 0.700000
    //     0xbb8f24: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbb8f28: ldr             d0, [x17, #0xf48]
    // 0xbb8f2c: r0 = withOpacity()
    //     0xbb8f2c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb8f30: r16 = 12.000000
    //     0xbb8f30: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb8f34: ldr             x16, [x16, #0x9e8]
    // 0xbb8f38: stp             x0, x16, [SP]
    // 0xbb8f3c: ldur            x1, [fp, #-0x28]
    // 0xbb8f40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb8f40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb8f44: ldr             x4, [x4, #0xaa0]
    // 0xbb8f48: r0 = copyWith()
    //     0xbb8f48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb8f4c: stur            x0, [fp, #-0x28]
    // 0xbb8f50: r0 = Text()
    //     0xbb8f50: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb8f54: mov             x3, x0
    // 0xbb8f58: ldur            x0, [fp, #-0x18]
    // 0xbb8f5c: stur            x3, [fp, #-0x30]
    // 0xbb8f60: StoreField: r3->field_b = r0
    //     0xbb8f60: stur            w0, [x3, #0xb]
    // 0xbb8f64: ldur            x0, [fp, #-0x28]
    // 0xbb8f68: StoreField: r3->field_13 = r0
    //     0xbb8f68: stur            w0, [x3, #0x13]
    // 0xbb8f6c: r0 = 4
    //     0xbb8f6c: movz            x0, #0x4
    // 0xbb8f70: StoreField: r3->field_37 = r0
    //     0xbb8f70: stur            w0, [x3, #0x37]
    // 0xbb8f74: r1 = Null
    //     0xbb8f74: mov             x1, NULL
    // 0xbb8f78: r2 = 10
    //     0xbb8f78: movz            x2, #0xa
    // 0xbb8f7c: r0 = AllocateArray()
    //     0xbb8f7c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb8f80: r16 = "Size: "
    //     0xbb8f80: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xbb8f84: ldr             x16, [x16, #0xf00]
    // 0xbb8f88: StoreField: r0->field_f = r16
    //     0xbb8f88: stur            w16, [x0, #0xf]
    // 0xbb8f8c: ldur            x1, [fp, #-0x10]
    // 0xbb8f90: LoadField: r2 = r1->field_2f
    //     0xbb8f90: ldur            w2, [x1, #0x2f]
    // 0xbb8f94: DecompressPointer r2
    //     0xbb8f94: add             x2, x2, HEAP, lsl #32
    // 0xbb8f98: StoreField: r0->field_13 = r2
    //     0xbb8f98: stur            w2, [x0, #0x13]
    // 0xbb8f9c: r16 = " / Qty: "
    //     0xbb8f9c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xbb8fa0: ldr             x16, [x16, #0x760]
    // 0xbb8fa4: ArrayStore: r0[0] = r16  ; List_4
    //     0xbb8fa4: stur            w16, [x0, #0x17]
    // 0xbb8fa8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbb8fa8: ldur            w2, [x1, #0x17]
    // 0xbb8fac: DecompressPointer r2
    //     0xbb8fac: add             x2, x2, HEAP, lsl #32
    // 0xbb8fb0: StoreField: r0->field_1b = r2
    //     0xbb8fb0: stur            w2, [x0, #0x1b]
    // 0xbb8fb4: r16 = " "
    //     0xbb8fb4: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xbb8fb8: StoreField: r0->field_1f = r16
    //     0xbb8fb8: stur            w16, [x0, #0x1f]
    // 0xbb8fbc: str             x0, [SP]
    // 0xbb8fc0: r0 = _interpolate()
    //     0xbb8fc0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbb8fc4: ldur            x1, [fp, #-8]
    // 0xbb8fc8: stur            x0, [fp, #-0x18]
    // 0xbb8fcc: r0 = of()
    //     0xbb8fcc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb8fd0: LoadField: r1 = r0->field_87
    //     0xbb8fd0: ldur            w1, [x0, #0x87]
    // 0xbb8fd4: DecompressPointer r1
    //     0xbb8fd4: add             x1, x1, HEAP, lsl #32
    // 0xbb8fd8: LoadField: r0 = r1->field_7
    //     0xbb8fd8: ldur            w0, [x1, #7]
    // 0xbb8fdc: DecompressPointer r0
    //     0xbb8fdc: add             x0, x0, HEAP, lsl #32
    // 0xbb8fe0: r16 = 12.000000
    //     0xbb8fe0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb8fe4: ldr             x16, [x16, #0x9e8]
    // 0xbb8fe8: r30 = Instance_Color
    //     0xbb8fe8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb8fec: stp             lr, x16, [SP]
    // 0xbb8ff0: mov             x1, x0
    // 0xbb8ff4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb8ff4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb8ff8: ldr             x4, [x4, #0xaa0]
    // 0xbb8ffc: r0 = copyWith()
    //     0xbb8ffc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb9000: stur            x0, [fp, #-0x28]
    // 0xbb9004: r0 = Text()
    //     0xbb9004: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb9008: mov             x1, x0
    // 0xbb900c: ldur            x0, [fp, #-0x18]
    // 0xbb9010: stur            x1, [fp, #-0x40]
    // 0xbb9014: StoreField: r1->field_b = r0
    //     0xbb9014: stur            w0, [x1, #0xb]
    // 0xbb9018: ldur            x0, [fp, #-0x28]
    // 0xbb901c: StoreField: r1->field_13 = r0
    //     0xbb901c: stur            w0, [x1, #0x13]
    // 0xbb9020: r0 = Padding()
    //     0xbb9020: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb9024: mov             x1, x0
    // 0xbb9028: r0 = Instance_EdgeInsets
    //     0xbb9028: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xbb902c: ldr             x0, [x0, #0x990]
    // 0xbb9030: stur            x1, [fp, #-0x18]
    // 0xbb9034: StoreField: r1->field_f = r0
    //     0xbb9034: stur            w0, [x1, #0xf]
    // 0xbb9038: ldur            x0, [fp, #-0x40]
    // 0xbb903c: StoreField: r1->field_b = r0
    //     0xbb903c: stur            w0, [x1, #0xb]
    // 0xbb9040: ldur            x0, [fp, #-0x10]
    // 0xbb9044: LoadField: r2 = r0->field_1f
    //     0xbb9044: ldur            w2, [x0, #0x1f]
    // 0xbb9048: DecompressPointer r2
    //     0xbb9048: add             x2, x2, HEAP, lsl #32
    // 0xbb904c: str             x2, [SP]
    // 0xbb9050: r0 = _interpolateSingle()
    //     0xbb9050: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbb9054: ldur            x1, [fp, #-8]
    // 0xbb9058: stur            x0, [fp, #-0x28]
    // 0xbb905c: r0 = of()
    //     0xbb905c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb9060: LoadField: r1 = r0->field_87
    //     0xbb9060: ldur            w1, [x0, #0x87]
    // 0xbb9064: DecompressPointer r1
    //     0xbb9064: add             x1, x1, HEAP, lsl #32
    // 0xbb9068: LoadField: r0 = r1->field_7
    //     0xbb9068: ldur            w0, [x1, #7]
    // 0xbb906c: DecompressPointer r0
    //     0xbb906c: add             x0, x0, HEAP, lsl #32
    // 0xbb9070: r16 = 12.000000
    //     0xbb9070: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb9074: ldr             x16, [x16, #0x9e8]
    // 0xbb9078: r30 = Instance_Color
    //     0xbb9078: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb907c: stp             lr, x16, [SP]
    // 0xbb9080: mov             x1, x0
    // 0xbb9084: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb9084: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb9088: ldr             x4, [x4, #0xaa0]
    // 0xbb908c: r0 = copyWith()
    //     0xbb908c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb9090: stur            x0, [fp, #-0x40]
    // 0xbb9094: r0 = Text()
    //     0xbb9094: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb9098: mov             x1, x0
    // 0xbb909c: ldur            x0, [fp, #-0x28]
    // 0xbb90a0: stur            x1, [fp, #-0x48]
    // 0xbb90a4: StoreField: r1->field_b = r0
    //     0xbb90a4: stur            w0, [x1, #0xb]
    // 0xbb90a8: ldur            x0, [fp, #-0x40]
    // 0xbb90ac: StoreField: r1->field_13 = r0
    //     0xbb90ac: stur            w0, [x1, #0x13]
    // 0xbb90b0: ldur            x0, [fp, #-0x10]
    // 0xbb90b4: LoadField: r2 = r0->field_27
    //     0xbb90b4: ldur            w2, [x0, #0x27]
    // 0xbb90b8: DecompressPointer r2
    //     0xbb90b8: add             x2, x2, HEAP, lsl #32
    // 0xbb90bc: str             x2, [SP]
    // 0xbb90c0: r0 = _interpolateSingle()
    //     0xbb90c0: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbb90c4: ldur            x1, [fp, #-8]
    // 0xbb90c8: stur            x0, [fp, #-0x28]
    // 0xbb90cc: r0 = of()
    //     0xbb90cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb90d0: LoadField: r1 = r0->field_87
    //     0xbb90d0: ldur            w1, [x0, #0x87]
    // 0xbb90d4: DecompressPointer r1
    //     0xbb90d4: add             x1, x1, HEAP, lsl #32
    // 0xbb90d8: LoadField: r0 = r1->field_7
    //     0xbb90d8: ldur            w0, [x1, #7]
    // 0xbb90dc: DecompressPointer r0
    //     0xbb90dc: add             x0, x0, HEAP, lsl #32
    // 0xbb90e0: stur            x0, [fp, #-0x40]
    // 0xbb90e4: r1 = Instance_Color
    //     0xbb90e4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb90e8: d0 = 0.400000
    //     0xbb90e8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbb90ec: r0 = withOpacity()
    //     0xbb90ec: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb90f0: r16 = Instance_TextDecoration
    //     0xbb90f0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xbb90f4: ldr             x16, [x16, #0xe30]
    // 0xbb90f8: r30 = 12.000000
    //     0xbb90f8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb90fc: ldr             lr, [lr, #0x9e8]
    // 0xbb9100: stp             lr, x16, [SP, #8]
    // 0xbb9104: str             x0, [SP]
    // 0xbb9108: ldur            x1, [fp, #-0x40]
    // 0xbb910c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, decoration, 0x1, fontSize, 0x2, null]
    //     0xbb910c: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb60] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "decoration", 0x1, "fontSize", 0x2, Null]
    //     0xbb9110: ldr             x4, [x4, #0xb60]
    // 0xbb9114: r0 = copyWith()
    //     0xbb9114: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb9118: stur            x0, [fp, #-0x40]
    // 0xbb911c: r0 = Text()
    //     0xbb911c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb9120: mov             x3, x0
    // 0xbb9124: ldur            x0, [fp, #-0x28]
    // 0xbb9128: stur            x3, [fp, #-0x50]
    // 0xbb912c: StoreField: r3->field_b = r0
    //     0xbb912c: stur            w0, [x3, #0xb]
    // 0xbb9130: ldur            x0, [fp, #-0x40]
    // 0xbb9134: StoreField: r3->field_13 = r0
    //     0xbb9134: stur            w0, [x3, #0x13]
    // 0xbb9138: r1 = Null
    //     0xbb9138: mov             x1, NULL
    // 0xbb913c: r2 = 6
    //     0xbb913c: movz            x2, #0x6
    // 0xbb9140: r0 = AllocateArray()
    //     0xbb9140: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb9144: mov             x2, x0
    // 0xbb9148: ldur            x0, [fp, #-0x48]
    // 0xbb914c: stur            x2, [fp, #-0x28]
    // 0xbb9150: StoreField: r2->field_f = r0
    //     0xbb9150: stur            w0, [x2, #0xf]
    // 0xbb9154: r16 = Instance_SizedBox
    //     0xbb9154: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xbb9158: ldr             x16, [x16, #0xb20]
    // 0xbb915c: StoreField: r2->field_13 = r16
    //     0xbb915c: stur            w16, [x2, #0x13]
    // 0xbb9160: ldur            x0, [fp, #-0x50]
    // 0xbb9164: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb9164: stur            w0, [x2, #0x17]
    // 0xbb9168: r1 = <Widget>
    //     0xbb9168: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb916c: r0 = AllocateGrowableArray()
    //     0xbb916c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb9170: mov             x1, x0
    // 0xbb9174: ldur            x0, [fp, #-0x28]
    // 0xbb9178: stur            x1, [fp, #-0x40]
    // 0xbb917c: StoreField: r1->field_f = r0
    //     0xbb917c: stur            w0, [x1, #0xf]
    // 0xbb9180: r2 = 6
    //     0xbb9180: movz            x2, #0x6
    // 0xbb9184: StoreField: r1->field_b = r2
    //     0xbb9184: stur            w2, [x1, #0xb]
    // 0xbb9188: r0 = Row()
    //     0xbb9188: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbb918c: mov             x1, x0
    // 0xbb9190: r0 = Instance_Axis
    //     0xbb9190: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb9194: stur            x1, [fp, #-0x28]
    // 0xbb9198: StoreField: r1->field_f = r0
    //     0xbb9198: stur            w0, [x1, #0xf]
    // 0xbb919c: r2 = Instance_MainAxisAlignment
    //     0xbb919c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb91a0: ldr             x2, [x2, #0xa08]
    // 0xbb91a4: StoreField: r1->field_13 = r2
    //     0xbb91a4: stur            w2, [x1, #0x13]
    // 0xbb91a8: r3 = Instance_MainAxisSize
    //     0xbb91a8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb91ac: ldr             x3, [x3, #0xa10]
    // 0xbb91b0: ArrayStore: r1[0] = r3  ; List_4
    //     0xbb91b0: stur            w3, [x1, #0x17]
    // 0xbb91b4: r4 = Instance_CrossAxisAlignment
    //     0xbb91b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb91b8: ldr             x4, [x4, #0xa18]
    // 0xbb91bc: StoreField: r1->field_1b = r4
    //     0xbb91bc: stur            w4, [x1, #0x1b]
    // 0xbb91c0: r5 = Instance_VerticalDirection
    //     0xbb91c0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb91c4: ldr             x5, [x5, #0xa20]
    // 0xbb91c8: StoreField: r1->field_23 = r5
    //     0xbb91c8: stur            w5, [x1, #0x23]
    // 0xbb91cc: r6 = Instance_Clip
    //     0xbb91cc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb91d0: ldr             x6, [x6, #0x38]
    // 0xbb91d4: StoreField: r1->field_2b = r6
    //     0xbb91d4: stur            w6, [x1, #0x2b]
    // 0xbb91d8: StoreField: r1->field_2f = rZR
    //     0xbb91d8: stur            xzr, [x1, #0x2f]
    // 0xbb91dc: ldur            x7, [fp, #-0x40]
    // 0xbb91e0: StoreField: r1->field_b = r7
    //     0xbb91e0: stur            w7, [x1, #0xb]
    // 0xbb91e4: r0 = Padding()
    //     0xbb91e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb91e8: mov             x3, x0
    // 0xbb91ec: r0 = Instance_EdgeInsets
    //     0xbb91ec: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbb91f0: ldr             x0, [x0, #0x770]
    // 0xbb91f4: stur            x3, [fp, #-0x40]
    // 0xbb91f8: StoreField: r3->field_f = r0
    //     0xbb91f8: stur            w0, [x3, #0xf]
    // 0xbb91fc: ldur            x0, [fp, #-0x28]
    // 0xbb9200: StoreField: r3->field_b = r0
    //     0xbb9200: stur            w0, [x3, #0xb]
    // 0xbb9204: r1 = Null
    //     0xbb9204: mov             x1, NULL
    // 0xbb9208: r2 = 6
    //     0xbb9208: movz            x2, #0x6
    // 0xbb920c: r0 = AllocateArray()
    //     0xbb920c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb9210: mov             x2, x0
    // 0xbb9214: ldur            x0, [fp, #-0x30]
    // 0xbb9218: stur            x2, [fp, #-0x28]
    // 0xbb921c: StoreField: r2->field_f = r0
    //     0xbb921c: stur            w0, [x2, #0xf]
    // 0xbb9220: ldur            x0, [fp, #-0x18]
    // 0xbb9224: StoreField: r2->field_13 = r0
    //     0xbb9224: stur            w0, [x2, #0x13]
    // 0xbb9228: ldur            x0, [fp, #-0x40]
    // 0xbb922c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb922c: stur            w0, [x2, #0x17]
    // 0xbb9230: r1 = <Widget>
    //     0xbb9230: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb9234: r0 = AllocateGrowableArray()
    //     0xbb9234: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb9238: mov             x1, x0
    // 0xbb923c: ldur            x0, [fp, #-0x28]
    // 0xbb9240: stur            x1, [fp, #-0x18]
    // 0xbb9244: StoreField: r1->field_f = r0
    //     0xbb9244: stur            w0, [x1, #0xf]
    // 0xbb9248: r0 = 6
    //     0xbb9248: movz            x0, #0x6
    // 0xbb924c: StoreField: r1->field_b = r0
    //     0xbb924c: stur            w0, [x1, #0xb]
    // 0xbb9250: r0 = Column()
    //     0xbb9250: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb9254: mov             x1, x0
    // 0xbb9258: r0 = Instance_Axis
    //     0xbb9258: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbb925c: stur            x1, [fp, #-0x28]
    // 0xbb9260: StoreField: r1->field_f = r0
    //     0xbb9260: stur            w0, [x1, #0xf]
    // 0xbb9264: r2 = Instance_MainAxisAlignment
    //     0xbb9264: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb9268: ldr             x2, [x2, #0xa08]
    // 0xbb926c: StoreField: r1->field_13 = r2
    //     0xbb926c: stur            w2, [x1, #0x13]
    // 0xbb9270: r3 = Instance_MainAxisSize
    //     0xbb9270: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb9274: ldr             x3, [x3, #0xa10]
    // 0xbb9278: ArrayStore: r1[0] = r3  ; List_4
    //     0xbb9278: stur            w3, [x1, #0x17]
    // 0xbb927c: r4 = Instance_CrossAxisAlignment
    //     0xbb927c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbb9280: ldr             x4, [x4, #0x890]
    // 0xbb9284: StoreField: r1->field_1b = r4
    //     0xbb9284: stur            w4, [x1, #0x1b]
    // 0xbb9288: r5 = Instance_VerticalDirection
    //     0xbb9288: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb928c: ldr             x5, [x5, #0xa20]
    // 0xbb9290: StoreField: r1->field_23 = r5
    //     0xbb9290: stur            w5, [x1, #0x23]
    // 0xbb9294: r6 = Instance_Clip
    //     0xbb9294: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb9298: ldr             x6, [x6, #0x38]
    // 0xbb929c: StoreField: r1->field_2b = r6
    //     0xbb929c: stur            w6, [x1, #0x2b]
    // 0xbb92a0: StoreField: r1->field_2f = rZR
    //     0xbb92a0: stur            xzr, [x1, #0x2f]
    // 0xbb92a4: ldur            x7, [fp, #-0x18]
    // 0xbb92a8: StoreField: r1->field_b = r7
    //     0xbb92a8: stur            w7, [x1, #0xb]
    // 0xbb92ac: r0 = Padding()
    //     0xbb92ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb92b0: mov             x2, x0
    // 0xbb92b4: r0 = Instance_EdgeInsets
    //     0xbb92b4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbb92b8: ldr             x0, [x0, #0xa78]
    // 0xbb92bc: stur            x2, [fp, #-0x18]
    // 0xbb92c0: StoreField: r2->field_f = r0
    //     0xbb92c0: stur            w0, [x2, #0xf]
    // 0xbb92c4: ldur            x0, [fp, #-0x28]
    // 0xbb92c8: StoreField: r2->field_b = r0
    //     0xbb92c8: stur            w0, [x2, #0xb]
    // 0xbb92cc: r1 = <FlexParentData>
    //     0xbb92cc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbb92d0: ldr             x1, [x1, #0xe00]
    // 0xbb92d4: r0 = Expanded()
    //     0xbb92d4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbb92d8: mov             x3, x0
    // 0xbb92dc: r0 = 1
    //     0xbb92dc: movz            x0, #0x1
    // 0xbb92e0: stur            x3, [fp, #-0x28]
    // 0xbb92e4: StoreField: r3->field_13 = r0
    //     0xbb92e4: stur            x0, [x3, #0x13]
    // 0xbb92e8: r0 = Instance_FlexFit
    //     0xbb92e8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbb92ec: ldr             x0, [x0, #0xe08]
    // 0xbb92f0: StoreField: r3->field_1b = r0
    //     0xbb92f0: stur            w0, [x3, #0x1b]
    // 0xbb92f4: ldur            x0, [fp, #-0x18]
    // 0xbb92f8: StoreField: r3->field_b = r0
    //     0xbb92f8: stur            w0, [x3, #0xb]
    // 0xbb92fc: r1 = Null
    //     0xbb92fc: mov             x1, NULL
    // 0xbb9300: r2 = 4
    //     0xbb9300: movz            x2, #0x4
    // 0xbb9304: r0 = AllocateArray()
    //     0xbb9304: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb9308: mov             x2, x0
    // 0xbb930c: ldur            x0, [fp, #-0x38]
    // 0xbb9310: stur            x2, [fp, #-0x18]
    // 0xbb9314: StoreField: r2->field_f = r0
    //     0xbb9314: stur            w0, [x2, #0xf]
    // 0xbb9318: ldur            x0, [fp, #-0x28]
    // 0xbb931c: StoreField: r2->field_13 = r0
    //     0xbb931c: stur            w0, [x2, #0x13]
    // 0xbb9320: r1 = <Widget>
    //     0xbb9320: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb9324: r0 = AllocateGrowableArray()
    //     0xbb9324: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb9328: mov             x1, x0
    // 0xbb932c: ldur            x0, [fp, #-0x18]
    // 0xbb9330: stur            x1, [fp, #-0x28]
    // 0xbb9334: StoreField: r1->field_f = r0
    //     0xbb9334: stur            w0, [x1, #0xf]
    // 0xbb9338: r0 = 4
    //     0xbb9338: movz            x0, #0x4
    // 0xbb933c: StoreField: r1->field_b = r0
    //     0xbb933c: stur            w0, [x1, #0xb]
    // 0xbb9340: r0 = Row()
    //     0xbb9340: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbb9344: mov             x1, x0
    // 0xbb9348: r0 = Instance_Axis
    //     0xbb9348: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb934c: stur            x1, [fp, #-0x18]
    // 0xbb9350: StoreField: r1->field_f = r0
    //     0xbb9350: stur            w0, [x1, #0xf]
    // 0xbb9354: r0 = Instance_MainAxisAlignment
    //     0xbb9354: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb9358: ldr             x0, [x0, #0xa08]
    // 0xbb935c: StoreField: r1->field_13 = r0
    //     0xbb935c: stur            w0, [x1, #0x13]
    // 0xbb9360: r2 = Instance_MainAxisSize
    //     0xbb9360: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb9364: ldr             x2, [x2, #0xa10]
    // 0xbb9368: ArrayStore: r1[0] = r2  ; List_4
    //     0xbb9368: stur            w2, [x1, #0x17]
    // 0xbb936c: r3 = Instance_CrossAxisAlignment
    //     0xbb936c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbb9370: ldr             x3, [x3, #0x890]
    // 0xbb9374: StoreField: r1->field_1b = r3
    //     0xbb9374: stur            w3, [x1, #0x1b]
    // 0xbb9378: r3 = Instance_VerticalDirection
    //     0xbb9378: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb937c: ldr             x3, [x3, #0xa20]
    // 0xbb9380: StoreField: r1->field_23 = r3
    //     0xbb9380: stur            w3, [x1, #0x23]
    // 0xbb9384: r4 = Instance_Clip
    //     0xbb9384: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb9388: ldr             x4, [x4, #0x38]
    // 0xbb938c: StoreField: r1->field_2b = r4
    //     0xbb938c: stur            w4, [x1, #0x2b]
    // 0xbb9390: StoreField: r1->field_2f = rZR
    //     0xbb9390: stur            xzr, [x1, #0x2f]
    // 0xbb9394: ldur            x5, [fp, #-0x28]
    // 0xbb9398: StoreField: r1->field_b = r5
    //     0xbb9398: stur            w5, [x1, #0xb]
    // 0xbb939c: r0 = Padding()
    //     0xbb939c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb93a0: mov             x1, x0
    // 0xbb93a4: r0 = Instance_EdgeInsets
    //     0xbb93a4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbb93a8: ldr             x0, [x0, #0x1f0]
    // 0xbb93ac: stur            x1, [fp, #-0x28]
    // 0xbb93b0: StoreField: r1->field_f = r0
    //     0xbb93b0: stur            w0, [x1, #0xf]
    // 0xbb93b4: ldur            x0, [fp, #-0x18]
    // 0xbb93b8: StoreField: r1->field_b = r0
    //     0xbb93b8: stur            w0, [x1, #0xb]
    // 0xbb93bc: r0 = Container()
    //     0xbb93bc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbb93c0: stur            x0, [fp, #-0x18]
    // 0xbb93c4: ldur            x16, [fp, #-0x20]
    // 0xbb93c8: ldur            lr, [fp, #-0x28]
    // 0xbb93cc: stp             lr, x16, [SP]
    // 0xbb93d0: mov             x1, x0
    // 0xbb93d4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbb93d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbb93d8: ldr             x4, [x4, #0x88]
    // 0xbb93dc: r0 = Container()
    //     0xbb93dc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbb93e0: ldur            x0, [fp, #-0x10]
    // 0xbb93e4: LoadField: r1 = r0->field_2b
    //     0xbb93e4: ldur            w1, [x0, #0x2b]
    // 0xbb93e8: DecompressPointer r1
    //     0xbb93e8: add             x1, x1, HEAP, lsl #32
    // 0xbb93ec: str             x1, [SP]
    // 0xbb93f0: r0 = _interpolateSingle()
    //     0xbb93f0: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbb93f4: ldur            x1, [fp, #-8]
    // 0xbb93f8: stur            x0, [fp, #-8]
    // 0xbb93fc: r0 = of()
    //     0xbb93fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb9400: LoadField: r1 = r0->field_87
    //     0xbb9400: ldur            w1, [x0, #0x87]
    // 0xbb9404: DecompressPointer r1
    //     0xbb9404: add             x1, x1, HEAP, lsl #32
    // 0xbb9408: LoadField: r0 = r1->field_7
    //     0xbb9408: ldur            w0, [x1, #7]
    // 0xbb940c: DecompressPointer r0
    //     0xbb940c: add             x0, x0, HEAP, lsl #32
    // 0xbb9410: stur            x0, [fp, #-0x10]
    // 0xbb9414: r1 = Instance_Color
    //     0xbb9414: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb9418: d0 = 0.400000
    //     0xbb9418: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbb941c: r0 = withOpacity()
    //     0xbb941c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb9420: r16 = 14.000000
    //     0xbb9420: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb9424: ldr             x16, [x16, #0x1d8]
    // 0xbb9428: stp             x0, x16, [SP]
    // 0xbb942c: ldur            x1, [fp, #-0x10]
    // 0xbb9430: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb9430: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb9434: ldr             x4, [x4, #0xaa0]
    // 0xbb9438: r0 = copyWith()
    //     0xbb9438: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb943c: stur            x0, [fp, #-0x10]
    // 0xbb9440: r0 = Text()
    //     0xbb9440: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb9444: mov             x1, x0
    // 0xbb9448: ldur            x0, [fp, #-8]
    // 0xbb944c: stur            x1, [fp, #-0x20]
    // 0xbb9450: StoreField: r1->field_b = r0
    //     0xbb9450: stur            w0, [x1, #0xb]
    // 0xbb9454: ldur            x0, [fp, #-0x10]
    // 0xbb9458: StoreField: r1->field_13 = r0
    //     0xbb9458: stur            w0, [x1, #0x13]
    // 0xbb945c: r0 = Align()
    //     0xbb945c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xbb9460: mov             x3, x0
    // 0xbb9464: r0 = Instance_Alignment
    //     0xbb9464: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f98] Obj!Alignment@d5a7a1
    //     0xbb9468: ldr             x0, [x0, #0xf98]
    // 0xbb946c: stur            x3, [fp, #-8]
    // 0xbb9470: StoreField: r3->field_f = r0
    //     0xbb9470: stur            w0, [x3, #0xf]
    // 0xbb9474: ldur            x0, [fp, #-0x20]
    // 0xbb9478: StoreField: r3->field_b = r0
    //     0xbb9478: stur            w0, [x3, #0xb]
    // 0xbb947c: r1 = Null
    //     0xbb947c: mov             x1, NULL
    // 0xbb9480: r2 = 8
    //     0xbb9480: movz            x2, #0x8
    // 0xbb9484: r0 = AllocateArray()
    //     0xbb9484: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb9488: mov             x2, x0
    // 0xbb948c: ldur            x0, [fp, #-0x18]
    // 0xbb9490: stur            x2, [fp, #-0x10]
    // 0xbb9494: StoreField: r2->field_f = r0
    //     0xbb9494: stur            w0, [x2, #0xf]
    // 0xbb9498: r16 = Instance_SizedBox
    //     0xbb9498: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xbb949c: ldr             x16, [x16, #0x8b8]
    // 0xbb94a0: StoreField: r2->field_13 = r16
    //     0xbb94a0: stur            w16, [x2, #0x13]
    // 0xbb94a4: ldur            x0, [fp, #-8]
    // 0xbb94a8: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb94a8: stur            w0, [x2, #0x17]
    // 0xbb94ac: r16 = Instance_SizedBox
    //     0xbb94ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xbb94b0: ldr             x16, [x16, #0x8f0]
    // 0xbb94b4: StoreField: r2->field_1b = r16
    //     0xbb94b4: stur            w16, [x2, #0x1b]
    // 0xbb94b8: r1 = <Widget>
    //     0xbb94b8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb94bc: r0 = AllocateGrowableArray()
    //     0xbb94bc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb94c0: mov             x1, x0
    // 0xbb94c4: ldur            x0, [fp, #-0x10]
    // 0xbb94c8: stur            x1, [fp, #-8]
    // 0xbb94cc: StoreField: r1->field_f = r0
    //     0xbb94cc: stur            w0, [x1, #0xf]
    // 0xbb94d0: r0 = 8
    //     0xbb94d0: movz            x0, #0x8
    // 0xbb94d4: StoreField: r1->field_b = r0
    //     0xbb94d4: stur            w0, [x1, #0xb]
    // 0xbb94d8: r0 = Column()
    //     0xbb94d8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb94dc: r1 = Instance_Axis
    //     0xbb94dc: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbb94e0: StoreField: r0->field_f = r1
    //     0xbb94e0: stur            w1, [x0, #0xf]
    // 0xbb94e4: r1 = Instance_MainAxisAlignment
    //     0xbb94e4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb94e8: ldr             x1, [x1, #0xa08]
    // 0xbb94ec: StoreField: r0->field_13 = r1
    //     0xbb94ec: stur            w1, [x0, #0x13]
    // 0xbb94f0: r1 = Instance_MainAxisSize
    //     0xbb94f0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb94f4: ldr             x1, [x1, #0xa10]
    // 0xbb94f8: ArrayStore: r0[0] = r1  ; List_4
    //     0xbb94f8: stur            w1, [x0, #0x17]
    // 0xbb94fc: r1 = Instance_CrossAxisAlignment
    //     0xbb94fc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb9500: ldr             x1, [x1, #0xa18]
    // 0xbb9504: StoreField: r0->field_1b = r1
    //     0xbb9504: stur            w1, [x0, #0x1b]
    // 0xbb9508: r1 = Instance_VerticalDirection
    //     0xbb9508: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb950c: ldr             x1, [x1, #0xa20]
    // 0xbb9510: StoreField: r0->field_23 = r1
    //     0xbb9510: stur            w1, [x0, #0x23]
    // 0xbb9514: r1 = Instance_Clip
    //     0xbb9514: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb9518: ldr             x1, [x1, #0x38]
    // 0xbb951c: StoreField: r0->field_2b = r1
    //     0xbb951c: stur            w1, [x0, #0x2b]
    // 0xbb9520: StoreField: r0->field_2f = rZR
    //     0xbb9520: stur            xzr, [x0, #0x2f]
    // 0xbb9524: ldur            x1, [fp, #-8]
    // 0xbb9528: StoreField: r0->field_b = r1
    //     0xbb9528: stur            w1, [x0, #0xb]
    // 0xbb952c: LeaveFrame
    //     0xbb952c: mov             SP, fp
    //     0xbb9530: ldp             fp, lr, [SP], #0x10
    // 0xbb9534: ret
    //     0xbb9534: ret             
    // 0xbb9538: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb9538: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb953c: b               #0xbb8e08
  }
}

// class id: 3272, size: 0x14, field offset: 0x14
class _DuplicateOrderConfirmBottomSheet extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbb84f8, size: 0x694
    // 0xbb84f8: EnterFrame
    //     0xbb84f8: stp             fp, lr, [SP, #-0x10]!
    //     0xbb84fc: mov             fp, SP
    // 0xbb8500: AllocStack(0x60)
    //     0xbb8500: sub             SP, SP, #0x60
    // 0xbb8504: SetupParameters(_DuplicateOrderConfirmBottomSheet this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbb8504: mov             x0, x1
    //     0xbb8508: stur            x1, [fp, #-8]
    //     0xbb850c: mov             x1, x2
    //     0xbb8510: stur            x2, [fp, #-0x10]
    // 0xbb8514: CheckStackOverflow
    //     0xbb8514: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8518: cmp             SP, x16
    //     0xbb851c: b.ls            #0xbb8b7c
    // 0xbb8520: r1 = 1
    //     0xbb8520: movz            x1, #0x1
    // 0xbb8524: r0 = AllocateContext()
    //     0xbb8524: bl              #0x16f6108  ; AllocateContextStub
    // 0xbb8528: mov             x2, x0
    // 0xbb852c: ldur            x0, [fp, #-8]
    // 0xbb8530: stur            x2, [fp, #-0x18]
    // 0xbb8534: StoreField: r2->field_f = r0
    //     0xbb8534: stur            w0, [x2, #0xf]
    // 0xbb8538: ldur            x1, [fp, #-0x10]
    // 0xbb853c: r0 = of()
    //     0xbb853c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb8540: LoadField: r1 = r0->field_87
    //     0xbb8540: ldur            w1, [x0, #0x87]
    // 0xbb8544: DecompressPointer r1
    //     0xbb8544: add             x1, x1, HEAP, lsl #32
    // 0xbb8548: LoadField: r0 = r1->field_7
    //     0xbb8548: ldur            w0, [x1, #7]
    // 0xbb854c: DecompressPointer r0
    //     0xbb854c: add             x0, x0, HEAP, lsl #32
    // 0xbb8550: stur            x0, [fp, #-0x20]
    // 0xbb8554: r1 = Instance_Color
    //     0xbb8554: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb8558: d0 = 0.700000
    //     0xbb8558: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbb855c: ldr             d0, [x17, #0xf48]
    // 0xbb8560: r0 = withOpacity()
    //     0xbb8560: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb8564: r16 = 14.000000
    //     0xbb8564: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb8568: ldr             x16, [x16, #0x1d8]
    // 0xbb856c: stp             x16, x0, [SP]
    // 0xbb8570: ldur            x1, [fp, #-0x20]
    // 0xbb8574: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb8574: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb8578: ldr             x4, [x4, #0x9b8]
    // 0xbb857c: r0 = copyWith()
    //     0xbb857c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb8580: stur            x0, [fp, #-0x20]
    // 0xbb8584: r0 = Text()
    //     0xbb8584: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb8588: mov             x1, x0
    // 0xbb858c: r0 = "You have Already Ordered The Following Products!"
    //     0xbb858c: add             x0, PP, #0x54, lsl #12  ; [pp+0x546f8] "You have Already Ordered The Following Products!"
    //     0xbb8590: ldr             x0, [x0, #0x6f8]
    // 0xbb8594: stur            x1, [fp, #-0x28]
    // 0xbb8598: StoreField: r1->field_b = r0
    //     0xbb8598: stur            w0, [x1, #0xb]
    // 0xbb859c: ldur            x0, [fp, #-0x20]
    // 0xbb85a0: StoreField: r1->field_13 = r0
    //     0xbb85a0: stur            w0, [x1, #0x13]
    // 0xbb85a4: r0 = Center()
    //     0xbb85a4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbb85a8: mov             x1, x0
    // 0xbb85ac: r0 = Instance_Alignment
    //     0xbb85ac: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbb85b0: ldr             x0, [x0, #0xb10]
    // 0xbb85b4: stur            x1, [fp, #-0x20]
    // 0xbb85b8: StoreField: r1->field_f = r0
    //     0xbb85b8: stur            w0, [x1, #0xf]
    // 0xbb85bc: ldur            x0, [fp, #-0x28]
    // 0xbb85c0: StoreField: r1->field_b = r0
    //     0xbb85c0: stur            w0, [x1, #0xb]
    // 0xbb85c4: r0 = Padding()
    //     0xbb85c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb85c8: mov             x3, x0
    // 0xbb85cc: r0 = Instance_EdgeInsets
    //     0xbb85cc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xbb85d0: ldr             x0, [x0, #0xa00]
    // 0xbb85d4: stur            x3, [fp, #-0x28]
    // 0xbb85d8: StoreField: r3->field_f = r0
    //     0xbb85d8: stur            w0, [x3, #0xf]
    // 0xbb85dc: ldur            x1, [fp, #-0x20]
    // 0xbb85e0: StoreField: r3->field_b = r1
    //     0xbb85e0: stur            w1, [x3, #0xb]
    // 0xbb85e4: ldur            x4, [fp, #-8]
    // 0xbb85e8: LoadField: r1 = r4->field_b
    //     0xbb85e8: ldur            w1, [x4, #0xb]
    // 0xbb85ec: DecompressPointer r1
    //     0xbb85ec: add             x1, x1, HEAP, lsl #32
    // 0xbb85f0: cmp             w1, NULL
    // 0xbb85f4: b.eq            #0xbb8b84
    // 0xbb85f8: LoadField: r2 = r1->field_b
    //     0xbb85f8: ldur            w2, [x1, #0xb]
    // 0xbb85fc: DecompressPointer r2
    //     0xbb85fc: add             x2, x2, HEAP, lsl #32
    // 0xbb8600: LoadField: r1 = r2->field_7
    //     0xbb8600: ldur            w1, [x2, #7]
    // 0xbb8604: DecompressPointer r1
    //     0xbb8604: add             x1, x1, HEAP, lsl #32
    // 0xbb8608: cmp             w1, NULL
    // 0xbb860c: b.ne            #0xbb8618
    // 0xbb8610: r1 = Null
    //     0xbb8610: mov             x1, NULL
    // 0xbb8614: b               #0xbb8620
    // 0xbb8618: LoadField: r2 = r1->field_b
    //     0xbb8618: ldur            w2, [x1, #0xb]
    // 0xbb861c: mov             x1, x2
    // 0xbb8620: cmp             w1, NULL
    // 0xbb8624: b.ne            #0xbb8630
    // 0xbb8628: r1 = 0
    //     0xbb8628: movz            x1, #0
    // 0xbb862c: b               #0xbb8638
    // 0xbb8630: r2 = LoadInt32Instr(r1)
    //     0xbb8630: sbfx            x2, x1, #1, #0x1f
    // 0xbb8634: mov             x1, x2
    // 0xbb8638: lsl             x5, x1, #1
    // 0xbb863c: ldur            x2, [fp, #-0x18]
    // 0xbb8640: stur            x5, [fp, #-0x20]
    // 0xbb8644: r1 = Function '<anonymous closure>':.
    //     0xbb8644: add             x1, PP, #0x54, lsl #12  ; [pp+0x54700] AnonymousClosure: (0xbb8d14), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/duplicate_order_confirm_bottom_sheet.dart] _DuplicateOrderConfirmBottomSheet::build (0xbb84f8)
    //     0xbb8648: ldr             x1, [x1, #0x700]
    // 0xbb864c: r0 = AllocateClosure()
    //     0xbb864c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb8650: stur            x0, [fp, #-0x30]
    // 0xbb8654: r0 = ListView()
    //     0xbb8654: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbb8658: stur            x0, [fp, #-0x38]
    // 0xbb865c: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbb865c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbb8660: ldr             x16, [x16, #0x1c8]
    // 0xbb8664: r30 = true
    //     0xbb8664: add             lr, NULL, #0x20  ; true
    // 0xbb8668: stp             lr, x16, [SP]
    // 0xbb866c: mov             x1, x0
    // 0xbb8670: ldur            x2, [fp, #-0x30]
    // 0xbb8674: ldur            x3, [fp, #-0x20]
    // 0xbb8678: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xbb8678: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xbb867c: ldr             x4, [x4, #0xd18]
    // 0xbb8680: r0 = ListView.builder()
    //     0xbb8680: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbb8684: r0 = Padding()
    //     0xbb8684: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb8688: mov             x1, x0
    // 0xbb868c: r0 = Instance_EdgeInsets
    //     0xbb868c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xbb8690: ldr             x0, [x0, #0xa00]
    // 0xbb8694: stur            x1, [fp, #-0x20]
    // 0xbb8698: StoreField: r1->field_f = r0
    //     0xbb8698: stur            w0, [x1, #0xf]
    // 0xbb869c: ldur            x0, [fp, #-0x38]
    // 0xbb86a0: StoreField: r1->field_b = r0
    //     0xbb86a0: stur            w0, [x1, #0xb]
    // 0xbb86a4: r16 = <EdgeInsets>
    //     0xbb86a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbb86a8: ldr             x16, [x16, #0xda0]
    // 0xbb86ac: r30 = Instance_EdgeInsets
    //     0xbb86ac: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbb86b0: ldr             lr, [lr, #0x1f0]
    // 0xbb86b4: stp             lr, x16, [SP]
    // 0xbb86b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbb86b8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbb86bc: r0 = all()
    //     0xbb86bc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbb86c0: ldur            x1, [fp, #-0x10]
    // 0xbb86c4: stur            x0, [fp, #-0x30]
    // 0xbb86c8: r0 = of()
    //     0xbb86c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb86cc: LoadField: r1 = r0->field_5b
    //     0xbb86cc: ldur            w1, [x0, #0x5b]
    // 0xbb86d0: DecompressPointer r1
    //     0xbb86d0: add             x1, x1, HEAP, lsl #32
    // 0xbb86d4: stur            x1, [fp, #-0x38]
    // 0xbb86d8: r0 = BorderSide()
    //     0xbb86d8: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbb86dc: mov             x1, x0
    // 0xbb86e0: ldur            x0, [fp, #-0x38]
    // 0xbb86e4: stur            x1, [fp, #-0x40]
    // 0xbb86e8: StoreField: r1->field_7 = r0
    //     0xbb86e8: stur            w0, [x1, #7]
    // 0xbb86ec: d0 = 1.000000
    //     0xbb86ec: fmov            d0, #1.00000000
    // 0xbb86f0: StoreField: r1->field_b = d0
    //     0xbb86f0: stur            d0, [x1, #0xb]
    // 0xbb86f4: r0 = Instance_BorderStyle
    //     0xbb86f4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbb86f8: ldr             x0, [x0, #0xf68]
    // 0xbb86fc: StoreField: r1->field_13 = r0
    //     0xbb86fc: stur            w0, [x1, #0x13]
    // 0xbb8700: d0 = -1.000000
    //     0xbb8700: fmov            d0, #-1.00000000
    // 0xbb8704: ArrayStore: r1[0] = d0  ; List_8
    //     0xbb8704: stur            d0, [x1, #0x17]
    // 0xbb8708: r0 = RoundedRectangleBorder()
    //     0xbb8708: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xbb870c: mov             x1, x0
    // 0xbb8710: r0 = Instance_BorderRadius
    //     0xbb8710: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbb8714: ldr             x0, [x0, #0xf70]
    // 0xbb8718: StoreField: r1->field_b = r0
    //     0xbb8718: stur            w0, [x1, #0xb]
    // 0xbb871c: ldur            x0, [fp, #-0x40]
    // 0xbb8720: StoreField: r1->field_7 = r0
    //     0xbb8720: stur            w0, [x1, #7]
    // 0xbb8724: r16 = <RoundedRectangleBorder>
    //     0xbb8724: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbb8728: ldr             x16, [x16, #0xf78]
    // 0xbb872c: stp             x1, x16, [SP]
    // 0xbb8730: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbb8730: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbb8734: r0 = all()
    //     0xbb8734: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbb8738: stur            x0, [fp, #-0x38]
    // 0xbb873c: r0 = ButtonStyle()
    //     0xbb873c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbb8740: mov             x1, x0
    // 0xbb8744: ldur            x0, [fp, #-0x30]
    // 0xbb8748: stur            x1, [fp, #-0x40]
    // 0xbb874c: StoreField: r1->field_23 = r0
    //     0xbb874c: stur            w0, [x1, #0x23]
    // 0xbb8750: ldur            x0, [fp, #-0x38]
    // 0xbb8754: StoreField: r1->field_43 = r0
    //     0xbb8754: stur            w0, [x1, #0x43]
    // 0xbb8758: r0 = TextButtonThemeData()
    //     0xbb8758: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbb875c: mov             x2, x0
    // 0xbb8760: ldur            x0, [fp, #-0x40]
    // 0xbb8764: stur            x2, [fp, #-0x30]
    // 0xbb8768: StoreField: r2->field_7 = r0
    //     0xbb8768: stur            w0, [x2, #7]
    // 0xbb876c: ldur            x1, [fp, #-0x10]
    // 0xbb8770: r0 = of()
    //     0xbb8770: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb8774: LoadField: r1 = r0->field_87
    //     0xbb8774: ldur            w1, [x0, #0x87]
    // 0xbb8778: DecompressPointer r1
    //     0xbb8778: add             x1, x1, HEAP, lsl #32
    // 0xbb877c: LoadField: r0 = r1->field_7
    //     0xbb877c: ldur            w0, [x1, #7]
    // 0xbb8780: DecompressPointer r0
    //     0xbb8780: add             x0, x0, HEAP, lsl #32
    // 0xbb8784: ldur            x1, [fp, #-0x10]
    // 0xbb8788: stur            x0, [fp, #-0x38]
    // 0xbb878c: r0 = of()
    //     0xbb878c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb8790: LoadField: r1 = r0->field_5b
    //     0xbb8790: ldur            w1, [x0, #0x5b]
    // 0xbb8794: DecompressPointer r1
    //     0xbb8794: add             x1, x1, HEAP, lsl #32
    // 0xbb8798: r16 = 14.000000
    //     0xbb8798: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb879c: ldr             x16, [x16, #0x1d8]
    // 0xbb87a0: stp             x1, x16, [SP]
    // 0xbb87a4: ldur            x1, [fp, #-0x38]
    // 0xbb87a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb87a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb87ac: ldr             x4, [x4, #0xaa0]
    // 0xbb87b0: r0 = copyWith()
    //     0xbb87b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb87b4: stur            x0, [fp, #-0x38]
    // 0xbb87b8: r0 = Text()
    //     0xbb87b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb87bc: mov             x3, x0
    // 0xbb87c0: r0 = "I WANT TO ORDER MORE"
    //     0xbb87c0: add             x0, PP, #0x54, lsl #12  ; [pp+0x54708] "I WANT TO ORDER MORE"
    //     0xbb87c4: ldr             x0, [x0, #0x708]
    // 0xbb87c8: stur            x3, [fp, #-0x40]
    // 0xbb87cc: StoreField: r3->field_b = r0
    //     0xbb87cc: stur            w0, [x3, #0xb]
    // 0xbb87d0: ldur            x0, [fp, #-0x38]
    // 0xbb87d4: StoreField: r3->field_13 = r0
    //     0xbb87d4: stur            w0, [x3, #0x13]
    // 0xbb87d8: ldur            x2, [fp, #-0x18]
    // 0xbb87dc: r1 = Function '<anonymous closure>':.
    //     0xbb87dc: add             x1, PP, #0x54, lsl #12  ; [pp+0x54710] AnonymousClosure: (0xbb8c84), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/duplicate_order_confirm_bottom_sheet.dart] _DuplicateOrderConfirmBottomSheet::build (0xbb84f8)
    //     0xbb87e0: ldr             x1, [x1, #0x710]
    // 0xbb87e4: r0 = AllocateClosure()
    //     0xbb87e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb87e8: stur            x0, [fp, #-0x38]
    // 0xbb87ec: r0 = TextButton()
    //     0xbb87ec: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbb87f0: mov             x1, x0
    // 0xbb87f4: ldur            x0, [fp, #-0x38]
    // 0xbb87f8: stur            x1, [fp, #-0x48]
    // 0xbb87fc: StoreField: r1->field_b = r0
    //     0xbb87fc: stur            w0, [x1, #0xb]
    // 0xbb8800: r0 = false
    //     0xbb8800: add             x0, NULL, #0x30  ; false
    // 0xbb8804: StoreField: r1->field_27 = r0
    //     0xbb8804: stur            w0, [x1, #0x27]
    // 0xbb8808: r2 = true
    //     0xbb8808: add             x2, NULL, #0x20  ; true
    // 0xbb880c: StoreField: r1->field_2f = r2
    //     0xbb880c: stur            w2, [x1, #0x2f]
    // 0xbb8810: ldur            x3, [fp, #-0x40]
    // 0xbb8814: StoreField: r1->field_37 = r3
    //     0xbb8814: stur            w3, [x1, #0x37]
    // 0xbb8818: r0 = TextButtonTheme()
    //     0xbb8818: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbb881c: mov             x1, x0
    // 0xbb8820: ldur            x0, [fp, #-0x30]
    // 0xbb8824: stur            x1, [fp, #-0x38]
    // 0xbb8828: StoreField: r1->field_f = r0
    //     0xbb8828: stur            w0, [x1, #0xf]
    // 0xbb882c: ldur            x0, [fp, #-0x48]
    // 0xbb8830: StoreField: r1->field_b = r0
    //     0xbb8830: stur            w0, [x1, #0xb]
    // 0xbb8834: r0 = SizedBox()
    //     0xbb8834: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbb8838: mov             x1, x0
    // 0xbb883c: r0 = inf
    //     0xbb883c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbb8840: ldr             x0, [x0, #0x9f8]
    // 0xbb8844: stur            x1, [fp, #-0x30]
    // 0xbb8848: StoreField: r1->field_f = r0
    //     0xbb8848: stur            w0, [x1, #0xf]
    // 0xbb884c: ldur            x2, [fp, #-0x38]
    // 0xbb8850: StoreField: r1->field_b = r2
    //     0xbb8850: stur            w2, [x1, #0xb]
    // 0xbb8854: r16 = <EdgeInsets>
    //     0xbb8854: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbb8858: ldr             x16, [x16, #0xda0]
    // 0xbb885c: r30 = Instance_EdgeInsets
    //     0xbb885c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbb8860: ldr             lr, [lr, #0x1f0]
    // 0xbb8864: stp             lr, x16, [SP]
    // 0xbb8868: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbb8868: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbb886c: r0 = all()
    //     0xbb886c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbb8870: ldur            x1, [fp, #-0x10]
    // 0xbb8874: stur            x0, [fp, #-0x38]
    // 0xbb8878: r0 = of()
    //     0xbb8878: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb887c: LoadField: r1 = r0->field_5b
    //     0xbb887c: ldur            w1, [x0, #0x5b]
    // 0xbb8880: DecompressPointer r1
    //     0xbb8880: add             x1, x1, HEAP, lsl #32
    // 0xbb8884: r16 = <Color>
    //     0xbb8884: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbb8888: ldr             x16, [x16, #0xf80]
    // 0xbb888c: stp             x1, x16, [SP]
    // 0xbb8890: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbb8890: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbb8894: r0 = all()
    //     0xbb8894: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbb8898: stur            x0, [fp, #-0x40]
    // 0xbb889c: r16 = <RoundedRectangleBorder>
    //     0xbb889c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbb88a0: ldr             x16, [x16, #0xf78]
    // 0xbb88a4: r30 = Instance_RoundedRectangleBorder
    //     0xbb88a4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbb88a8: ldr             lr, [lr, #0xd68]
    // 0xbb88ac: stp             lr, x16, [SP]
    // 0xbb88b0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbb88b0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbb88b4: r0 = all()
    //     0xbb88b4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbb88b8: stur            x0, [fp, #-0x48]
    // 0xbb88bc: r0 = ButtonStyle()
    //     0xbb88bc: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbb88c0: mov             x1, x0
    // 0xbb88c4: ldur            x0, [fp, #-0x40]
    // 0xbb88c8: stur            x1, [fp, #-0x50]
    // 0xbb88cc: StoreField: r1->field_b = r0
    //     0xbb88cc: stur            w0, [x1, #0xb]
    // 0xbb88d0: ldur            x0, [fp, #-0x38]
    // 0xbb88d4: StoreField: r1->field_23 = r0
    //     0xbb88d4: stur            w0, [x1, #0x23]
    // 0xbb88d8: ldur            x0, [fp, #-0x48]
    // 0xbb88dc: StoreField: r1->field_43 = r0
    //     0xbb88dc: stur            w0, [x1, #0x43]
    // 0xbb88e0: r0 = TextButtonThemeData()
    //     0xbb88e0: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbb88e4: mov             x2, x0
    // 0xbb88e8: ldur            x0, [fp, #-0x50]
    // 0xbb88ec: stur            x2, [fp, #-0x38]
    // 0xbb88f0: StoreField: r2->field_7 = r0
    //     0xbb88f0: stur            w0, [x2, #7]
    // 0xbb88f4: ldur            x0, [fp, #-8]
    // 0xbb88f8: LoadField: r1 = r0->field_b
    //     0xbb88f8: ldur            w1, [x0, #0xb]
    // 0xbb88fc: DecompressPointer r1
    //     0xbb88fc: add             x1, x1, HEAP, lsl #32
    // 0xbb8900: cmp             w1, NULL
    // 0xbb8904: b.eq            #0xbb8b88
    // 0xbb8908: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbb8908: ldur            w0, [x1, #0x17]
    // 0xbb890c: DecompressPointer r0
    //     0xbb890c: add             x0, x0, HEAP, lsl #32
    // 0xbb8910: tbnz            w0, #4, #0xbb8920
    // 0xbb8914: r5 = "DON\'T ORDER"
    //     0xbb8914: add             x5, PP, #0x54, lsl #12  ; [pp+0x54718] "DON\'T ORDER"
    //     0xbb8918: ldr             x5, [x5, #0x718]
    // 0xbb891c: b               #0xbb8928
    // 0xbb8920: r5 = "EDIT BAG"
    //     0xbb8920: add             x5, PP, #0x54, lsl #12  ; [pp+0x54720] "EDIT BAG"
    //     0xbb8924: ldr             x5, [x5, #0x720]
    // 0xbb8928: ldur            x4, [fp, #-0x28]
    // 0xbb892c: ldur            x3, [fp, #-0x20]
    // 0xbb8930: ldur            x0, [fp, #-0x30]
    // 0xbb8934: ldur            x1, [fp, #-0x10]
    // 0xbb8938: stur            x5, [fp, #-8]
    // 0xbb893c: r0 = of()
    //     0xbb893c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb8940: LoadField: r1 = r0->field_87
    //     0xbb8940: ldur            w1, [x0, #0x87]
    // 0xbb8944: DecompressPointer r1
    //     0xbb8944: add             x1, x1, HEAP, lsl #32
    // 0xbb8948: LoadField: r0 = r1->field_7
    //     0xbb8948: ldur            w0, [x1, #7]
    // 0xbb894c: DecompressPointer r0
    //     0xbb894c: add             x0, x0, HEAP, lsl #32
    // 0xbb8950: r16 = 14.000000
    //     0xbb8950: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb8954: ldr             x16, [x16, #0x1d8]
    // 0xbb8958: r30 = Instance_Color
    //     0xbb8958: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbb895c: stp             lr, x16, [SP]
    // 0xbb8960: mov             x1, x0
    // 0xbb8964: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb8964: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb8968: ldr             x4, [x4, #0xaa0]
    // 0xbb896c: r0 = copyWith()
    //     0xbb896c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb8970: stur            x0, [fp, #-0x10]
    // 0xbb8974: r0 = Text()
    //     0xbb8974: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb8978: mov             x3, x0
    // 0xbb897c: ldur            x0, [fp, #-8]
    // 0xbb8980: stur            x3, [fp, #-0x40]
    // 0xbb8984: StoreField: r3->field_b = r0
    //     0xbb8984: stur            w0, [x3, #0xb]
    // 0xbb8988: ldur            x0, [fp, #-0x10]
    // 0xbb898c: StoreField: r3->field_13 = r0
    //     0xbb898c: stur            w0, [x3, #0x13]
    // 0xbb8990: ldur            x2, [fp, #-0x18]
    // 0xbb8994: r1 = Function '<anonymous closure>':.
    //     0xbb8994: add             x1, PP, #0x54, lsl #12  ; [pp+0x54728] AnonymousClosure: (0xbb8bac), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/duplicate_order_confirm_bottom_sheet.dart] _DuplicateOrderConfirmBottomSheet::build (0xbb84f8)
    //     0xbb8998: ldr             x1, [x1, #0x728]
    // 0xbb899c: r0 = AllocateClosure()
    //     0xbb899c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb89a0: stur            x0, [fp, #-8]
    // 0xbb89a4: r0 = TextButton()
    //     0xbb89a4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbb89a8: mov             x1, x0
    // 0xbb89ac: ldur            x0, [fp, #-8]
    // 0xbb89b0: stur            x1, [fp, #-0x10]
    // 0xbb89b4: StoreField: r1->field_b = r0
    //     0xbb89b4: stur            w0, [x1, #0xb]
    // 0xbb89b8: r0 = false
    //     0xbb89b8: add             x0, NULL, #0x30  ; false
    // 0xbb89bc: StoreField: r1->field_27 = r0
    //     0xbb89bc: stur            w0, [x1, #0x27]
    // 0xbb89c0: r0 = true
    //     0xbb89c0: add             x0, NULL, #0x20  ; true
    // 0xbb89c4: StoreField: r1->field_2f = r0
    //     0xbb89c4: stur            w0, [x1, #0x2f]
    // 0xbb89c8: ldur            x0, [fp, #-0x40]
    // 0xbb89cc: StoreField: r1->field_37 = r0
    //     0xbb89cc: stur            w0, [x1, #0x37]
    // 0xbb89d0: r0 = TextButtonTheme()
    //     0xbb89d0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbb89d4: mov             x1, x0
    // 0xbb89d8: ldur            x0, [fp, #-0x38]
    // 0xbb89dc: stur            x1, [fp, #-8]
    // 0xbb89e0: StoreField: r1->field_f = r0
    //     0xbb89e0: stur            w0, [x1, #0xf]
    // 0xbb89e4: ldur            x0, [fp, #-0x10]
    // 0xbb89e8: StoreField: r1->field_b = r0
    //     0xbb89e8: stur            w0, [x1, #0xb]
    // 0xbb89ec: r0 = SizedBox()
    //     0xbb89ec: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbb89f0: mov             x3, x0
    // 0xbb89f4: r0 = inf
    //     0xbb89f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbb89f8: ldr             x0, [x0, #0x9f8]
    // 0xbb89fc: stur            x3, [fp, #-0x10]
    // 0xbb8a00: StoreField: r3->field_f = r0
    //     0xbb8a00: stur            w0, [x3, #0xf]
    // 0xbb8a04: ldur            x0, [fp, #-8]
    // 0xbb8a08: StoreField: r3->field_b = r0
    //     0xbb8a08: stur            w0, [x3, #0xb]
    // 0xbb8a0c: r1 = Null
    //     0xbb8a0c: mov             x1, NULL
    // 0xbb8a10: r2 = 4
    //     0xbb8a10: movz            x2, #0x4
    // 0xbb8a14: r0 = AllocateArray()
    //     0xbb8a14: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb8a18: mov             x2, x0
    // 0xbb8a1c: ldur            x0, [fp, #-0x30]
    // 0xbb8a20: stur            x2, [fp, #-8]
    // 0xbb8a24: StoreField: r2->field_f = r0
    //     0xbb8a24: stur            w0, [x2, #0xf]
    // 0xbb8a28: ldur            x0, [fp, #-0x10]
    // 0xbb8a2c: StoreField: r2->field_13 = r0
    //     0xbb8a2c: stur            w0, [x2, #0x13]
    // 0xbb8a30: r1 = <Widget>
    //     0xbb8a30: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb8a34: r0 = AllocateGrowableArray()
    //     0xbb8a34: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb8a38: mov             x1, x0
    // 0xbb8a3c: ldur            x0, [fp, #-8]
    // 0xbb8a40: stur            x1, [fp, #-0x10]
    // 0xbb8a44: StoreField: r1->field_f = r0
    //     0xbb8a44: stur            w0, [x1, #0xf]
    // 0xbb8a48: r0 = 4
    //     0xbb8a48: movz            x0, #0x4
    // 0xbb8a4c: StoreField: r1->field_b = r0
    //     0xbb8a4c: stur            w0, [x1, #0xb]
    // 0xbb8a50: r0 = Column()
    //     0xbb8a50: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb8a54: mov             x3, x0
    // 0xbb8a58: r0 = Instance_Axis
    //     0xbb8a58: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbb8a5c: stur            x3, [fp, #-8]
    // 0xbb8a60: StoreField: r3->field_f = r0
    //     0xbb8a60: stur            w0, [x3, #0xf]
    // 0xbb8a64: r0 = Instance_MainAxisAlignment
    //     0xbb8a64: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb8a68: ldr             x0, [x0, #0xa08]
    // 0xbb8a6c: StoreField: r3->field_13 = r0
    //     0xbb8a6c: stur            w0, [x3, #0x13]
    // 0xbb8a70: r0 = Instance_MainAxisSize
    //     0xbb8a70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb8a74: ldr             x0, [x0, #0xa10]
    // 0xbb8a78: ArrayStore: r3[0] = r0  ; List_4
    //     0xbb8a78: stur            w0, [x3, #0x17]
    // 0xbb8a7c: r0 = Instance_CrossAxisAlignment
    //     0xbb8a7c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb8a80: ldr             x0, [x0, #0xa18]
    // 0xbb8a84: StoreField: r3->field_1b = r0
    //     0xbb8a84: stur            w0, [x3, #0x1b]
    // 0xbb8a88: r0 = Instance_VerticalDirection
    //     0xbb8a88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb8a8c: ldr             x0, [x0, #0xa20]
    // 0xbb8a90: StoreField: r3->field_23 = r0
    //     0xbb8a90: stur            w0, [x3, #0x23]
    // 0xbb8a94: r4 = Instance_Clip
    //     0xbb8a94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb8a98: ldr             x4, [x4, #0x38]
    // 0xbb8a9c: StoreField: r3->field_2b = r4
    //     0xbb8a9c: stur            w4, [x3, #0x2b]
    // 0xbb8aa0: StoreField: r3->field_2f = rZR
    //     0xbb8aa0: stur            xzr, [x3, #0x2f]
    // 0xbb8aa4: ldur            x1, [fp, #-0x10]
    // 0xbb8aa8: StoreField: r3->field_b = r1
    //     0xbb8aa8: stur            w1, [x3, #0xb]
    // 0xbb8aac: r1 = Null
    //     0xbb8aac: mov             x1, NULL
    // 0xbb8ab0: r2 = 8
    //     0xbb8ab0: movz            x2, #0x8
    // 0xbb8ab4: r0 = AllocateArray()
    //     0xbb8ab4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb8ab8: stur            x0, [fp, #-0x10]
    // 0xbb8abc: r16 = Instance_Center
    //     0xbb8abc: add             x16, PP, #0x54, lsl #12  ; [pp+0x54730] Obj!Center@d682c1
    //     0xbb8ac0: ldr             x16, [x16, #0x730]
    // 0xbb8ac4: StoreField: r0->field_f = r16
    //     0xbb8ac4: stur            w16, [x0, #0xf]
    // 0xbb8ac8: ldur            x1, [fp, #-0x28]
    // 0xbb8acc: StoreField: r0->field_13 = r1
    //     0xbb8acc: stur            w1, [x0, #0x13]
    // 0xbb8ad0: ldur            x1, [fp, #-0x20]
    // 0xbb8ad4: ArrayStore: r0[0] = r1  ; List_4
    //     0xbb8ad4: stur            w1, [x0, #0x17]
    // 0xbb8ad8: ldur            x1, [fp, #-8]
    // 0xbb8adc: StoreField: r0->field_1b = r1
    //     0xbb8adc: stur            w1, [x0, #0x1b]
    // 0xbb8ae0: r1 = <Widget>
    //     0xbb8ae0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb8ae4: r0 = AllocateGrowableArray()
    //     0xbb8ae4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb8ae8: mov             x1, x0
    // 0xbb8aec: ldur            x0, [fp, #-0x10]
    // 0xbb8af0: stur            x1, [fp, #-8]
    // 0xbb8af4: StoreField: r1->field_f = r0
    //     0xbb8af4: stur            w0, [x1, #0xf]
    // 0xbb8af8: r0 = 8
    //     0xbb8af8: movz            x0, #0x8
    // 0xbb8afc: StoreField: r1->field_b = r0
    //     0xbb8afc: stur            w0, [x1, #0xb]
    // 0xbb8b00: r0 = Wrap()
    //     0xbb8b00: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0xbb8b04: mov             x1, x0
    // 0xbb8b08: r0 = Instance_Axis
    //     0xbb8b08: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb8b0c: stur            x1, [fp, #-0x10]
    // 0xbb8b10: StoreField: r1->field_f = r0
    //     0xbb8b10: stur            w0, [x1, #0xf]
    // 0xbb8b14: r0 = Instance_WrapAlignment
    //     0xbb8b14: add             x0, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0xbb8b18: ldr             x0, [x0, #0x6e8]
    // 0xbb8b1c: StoreField: r1->field_13 = r0
    //     0xbb8b1c: stur            w0, [x1, #0x13]
    // 0xbb8b20: ArrayStore: r1[0] = rZR  ; List_8
    //     0xbb8b20: stur            xzr, [x1, #0x17]
    // 0xbb8b24: StoreField: r1->field_1f = r0
    //     0xbb8b24: stur            w0, [x1, #0x1f]
    // 0xbb8b28: StoreField: r1->field_23 = rZR
    //     0xbb8b28: stur            xzr, [x1, #0x23]
    // 0xbb8b2c: r0 = Instance_WrapCrossAlignment
    //     0xbb8b2c: add             x0, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0xbb8b30: ldr             x0, [x0, #0x6f0]
    // 0xbb8b34: StoreField: r1->field_2b = r0
    //     0xbb8b34: stur            w0, [x1, #0x2b]
    // 0xbb8b38: r0 = Instance_VerticalDirection
    //     0xbb8b38: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb8b3c: ldr             x0, [x0, #0xa20]
    // 0xbb8b40: StoreField: r1->field_33 = r0
    //     0xbb8b40: stur            w0, [x1, #0x33]
    // 0xbb8b44: r0 = Instance_Clip
    //     0xbb8b44: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb8b48: ldr             x0, [x0, #0x38]
    // 0xbb8b4c: StoreField: r1->field_37 = r0
    //     0xbb8b4c: stur            w0, [x1, #0x37]
    // 0xbb8b50: ldur            x0, [fp, #-8]
    // 0xbb8b54: StoreField: r1->field_b = r0
    //     0xbb8b54: stur            w0, [x1, #0xb]
    // 0xbb8b58: r0 = Padding()
    //     0xbb8b58: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb8b5c: r1 = Instance_EdgeInsets
    //     0xbb8b5c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0xbb8b60: ldr             x1, [x1, #0xa98]
    // 0xbb8b64: StoreField: r0->field_f = r1
    //     0xbb8b64: stur            w1, [x0, #0xf]
    // 0xbb8b68: ldur            x1, [fp, #-0x10]
    // 0xbb8b6c: StoreField: r0->field_b = r1
    //     0xbb8b6c: stur            w1, [x0, #0xb]
    // 0xbb8b70: LeaveFrame
    //     0xbb8b70: mov             SP, fp
    //     0xbb8b74: ldp             fp, lr, [SP], #0x10
    // 0xbb8b78: ret
    //     0xbb8b78: ret             
    // 0xbb8b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb8b7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb8b80: b               #0xbb8520
    // 0xbb8b84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb8b84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb8b88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb8b88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbb8bac, size: 0xd8
    // 0xbb8bac: EnterFrame
    //     0xbb8bac: stp             fp, lr, [SP, #-0x10]!
    //     0xbb8bb0: mov             fp, SP
    // 0xbb8bb4: AllocStack(0x20)
    //     0xbb8bb4: sub             SP, SP, #0x20
    // 0xbb8bb8: SetupParameters()
    //     0xbb8bb8: ldr             x0, [fp, #0x10]
    //     0xbb8bbc: ldur            w1, [x0, #0x17]
    //     0xbb8bc0: add             x1, x1, HEAP, lsl #32
    //     0xbb8bc4: stur            x1, [fp, #-8]
    // 0xbb8bc8: CheckStackOverflow
    //     0xbb8bc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8bcc: cmp             SP, x16
    //     0xbb8bd0: b.ls            #0xbb8c78
    // 0xbb8bd4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbb8bd4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbb8bd8: ldr             x0, [x0, #0x1c80]
    //     0xbb8bdc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbb8be0: cmp             w0, w16
    //     0xbb8be4: b.ne            #0xbb8bf0
    //     0xbb8be8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbb8bec: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbb8bf0: str             NULL, [SP]
    // 0xbb8bf4: r4 = const [0x1, 0, 0, 0, null]
    //     0xbb8bf4: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbb8bf8: r0 = GetNavigation.back()
    //     0xbb8bf8: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xbb8bfc: ldur            x0, [fp, #-8]
    // 0xbb8c00: LoadField: r1 = r0->field_f
    //     0xbb8c00: ldur            w1, [x0, #0xf]
    // 0xbb8c04: DecompressPointer r1
    //     0xbb8c04: add             x1, x1, HEAP, lsl #32
    // 0xbb8c08: LoadField: r0 = r1->field_b
    //     0xbb8c08: ldur            w0, [x1, #0xb]
    // 0xbb8c0c: DecompressPointer r0
    //     0xbb8c0c: add             x0, x0, HEAP, lsl #32
    // 0xbb8c10: cmp             w0, NULL
    // 0xbb8c14: b.eq            #0xbb8c80
    // 0xbb8c18: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb8c18: ldur            w1, [x0, #0x17]
    // 0xbb8c1c: DecompressPointer r1
    //     0xbb8c1c: add             x1, x1, HEAP, lsl #32
    // 0xbb8c20: tbnz            w1, #4, #0xbb8c30
    // 0xbb8c24: r1 = "Don\'t Order"
    //     0xbb8c24: add             x1, PP, #0x54, lsl #12  ; [pp+0x54738] "Don\'t Order"
    //     0xbb8c28: ldr             x1, [x1, #0x738]
    // 0xbb8c2c: b               #0xbb8c38
    // 0xbb8c30: r1 = "Edit Bag"
    //     0xbb8c30: add             x1, PP, #0x54, lsl #12  ; [pp+0x54740] "Edit Bag"
    //     0xbb8c34: ldr             x1, [x1, #0x740]
    // 0xbb8c38: LoadField: r2 = r0->field_13
    //     0xbb8c38: ldur            w2, [x0, #0x13]
    // 0xbb8c3c: DecompressPointer r2
    //     0xbb8c3c: add             x2, x2, HEAP, lsl #32
    // 0xbb8c40: r16 = "popup_cta"
    //     0xbb8c40: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3ca60] "popup_cta"
    //     0xbb8c44: ldr             x16, [x16, #0xa60]
    // 0xbb8c48: stp             x16, x2, [SP, #8]
    // 0xbb8c4c: str             x1, [SP]
    // 0xbb8c50: r4 = 0
    //     0xbb8c50: movz            x4, #0
    // 0xbb8c54: ldr             x0, [SP, #0x10]
    // 0xbb8c58: r16 = UnlinkedCall_0x613b5c
    //     0xbb8c58: add             x16, PP, #0x54, lsl #12  ; [pp+0x54748] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbb8c5c: add             x16, x16, #0x748
    // 0xbb8c60: ldp             x5, lr, [x16]
    // 0xbb8c64: blr             lr
    // 0xbb8c68: r0 = Null
    //     0xbb8c68: mov             x0, NULL
    // 0xbb8c6c: LeaveFrame
    //     0xbb8c6c: mov             SP, fp
    //     0xbb8c70: ldp             fp, lr, [SP], #0x10
    // 0xbb8c74: ret
    //     0xbb8c74: ret             
    // 0xbb8c78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb8c78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb8c7c: b               #0xbb8bd4
    // 0xbb8c80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb8c80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbb8c84, size: 0x90
    // 0xbb8c84: EnterFrame
    //     0xbb8c84: stp             fp, lr, [SP, #-0x10]!
    //     0xbb8c88: mov             fp, SP
    // 0xbb8c8c: AllocStack(0x18)
    //     0xbb8c8c: sub             SP, SP, #0x18
    // 0xbb8c90: SetupParameters()
    //     0xbb8c90: ldr             x0, [fp, #0x10]
    //     0xbb8c94: ldur            w1, [x0, #0x17]
    //     0xbb8c98: add             x1, x1, HEAP, lsl #32
    // 0xbb8c9c: CheckStackOverflow
    //     0xbb8c9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8ca0: cmp             SP, x16
    //     0xbb8ca4: b.ls            #0xbb8d08
    // 0xbb8ca8: LoadField: r0 = r1->field_f
    //     0xbb8ca8: ldur            w0, [x1, #0xf]
    // 0xbb8cac: DecompressPointer r0
    //     0xbb8cac: add             x0, x0, HEAP, lsl #32
    // 0xbb8cb0: LoadField: r1 = r0->field_b
    //     0xbb8cb0: ldur            w1, [x0, #0xb]
    // 0xbb8cb4: DecompressPointer r1
    //     0xbb8cb4: add             x1, x1, HEAP, lsl #32
    // 0xbb8cb8: cmp             w1, NULL
    // 0xbb8cbc: b.eq            #0xbb8d10
    // 0xbb8cc0: LoadField: r0 = r1->field_f
    //     0xbb8cc0: ldur            w0, [x1, #0xf]
    // 0xbb8cc4: DecompressPointer r0
    //     0xbb8cc4: add             x0, x0, HEAP, lsl #32
    // 0xbb8cc8: r16 = "popup_cta"
    //     0xbb8cc8: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3ca60] "popup_cta"
    //     0xbb8ccc: ldr             x16, [x16, #0xa60]
    // 0xbb8cd0: stp             x16, x0, [SP, #8]
    // 0xbb8cd4: r16 = "i want to order more"
    //     0xbb8cd4: add             x16, PP, #0x54, lsl #12  ; [pp+0x54758] "i want to order more"
    //     0xbb8cd8: ldr             x16, [x16, #0x758]
    // 0xbb8cdc: str             x16, [SP]
    // 0xbb8ce0: r4 = 0
    //     0xbb8ce0: movz            x4, #0
    // 0xbb8ce4: ldr             x0, [SP, #0x10]
    // 0xbb8ce8: r16 = UnlinkedCall_0x613b5c
    //     0xbb8ce8: add             x16, PP, #0x54, lsl #12  ; [pp+0x54760] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbb8cec: add             x16, x16, #0x760
    // 0xbb8cf0: ldp             x5, lr, [x16]
    // 0xbb8cf4: blr             lr
    // 0xbb8cf8: r0 = Null
    //     0xbb8cf8: mov             x0, NULL
    // 0xbb8cfc: LeaveFrame
    //     0xbb8cfc: mov             SP, fp
    //     0xbb8d00: ldp             fp, lr, [SP], #0x10
    // 0xbb8d04: ret
    //     0xbb8d04: ret             
    // 0xbb8d08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb8d08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb8d0c: b               #0xbb8ca8
    // 0xbb8d10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb8d10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbb8d14, size: 0xd0
    // 0xbb8d14: EnterFrame
    //     0xbb8d14: stp             fp, lr, [SP, #-0x10]!
    //     0xbb8d18: mov             fp, SP
    // 0xbb8d1c: ldr             x0, [fp, #0x20]
    // 0xbb8d20: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb8d20: ldur            w1, [x0, #0x17]
    // 0xbb8d24: DecompressPointer r1
    //     0xbb8d24: add             x1, x1, HEAP, lsl #32
    // 0xbb8d28: CheckStackOverflow
    //     0xbb8d28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8d2c: cmp             SP, x16
    //     0xbb8d30: b.ls            #0xbb8dd4
    // 0xbb8d34: LoadField: r0 = r1->field_f
    //     0xbb8d34: ldur            w0, [x1, #0xf]
    // 0xbb8d38: DecompressPointer r0
    //     0xbb8d38: add             x0, x0, HEAP, lsl #32
    // 0xbb8d3c: LoadField: r1 = r0->field_b
    //     0xbb8d3c: ldur            w1, [x0, #0xb]
    // 0xbb8d40: DecompressPointer r1
    //     0xbb8d40: add             x1, x1, HEAP, lsl #32
    // 0xbb8d44: cmp             w1, NULL
    // 0xbb8d48: b.eq            #0xbb8ddc
    // 0xbb8d4c: LoadField: r0 = r1->field_b
    //     0xbb8d4c: ldur            w0, [x1, #0xb]
    // 0xbb8d50: DecompressPointer r0
    //     0xbb8d50: add             x0, x0, HEAP, lsl #32
    // 0xbb8d54: LoadField: r2 = r0->field_7
    //     0xbb8d54: ldur            w2, [x0, #7]
    // 0xbb8d58: DecompressPointer r2
    //     0xbb8d58: add             x2, x2, HEAP, lsl #32
    // 0xbb8d5c: cmp             w2, NULL
    // 0xbb8d60: b.ne            #0xbb8d6c
    // 0xbb8d64: r0 = Null
    //     0xbb8d64: mov             x0, NULL
    // 0xbb8d68: b               #0xbb8da8
    // 0xbb8d6c: ldr             x0, [fp, #0x10]
    // 0xbb8d70: LoadField: r1 = r2->field_b
    //     0xbb8d70: ldur            w1, [x2, #0xb]
    // 0xbb8d74: r3 = LoadInt32Instr(r0)
    //     0xbb8d74: sbfx            x3, x0, #1, #0x1f
    //     0xbb8d78: tbz             w0, #0, #0xbb8d80
    //     0xbb8d7c: ldur            x3, [x0, #7]
    // 0xbb8d80: r0 = LoadInt32Instr(r1)
    //     0xbb8d80: sbfx            x0, x1, #1, #0x1f
    // 0xbb8d84: mov             x1, x3
    // 0xbb8d88: cmp             x1, x0
    // 0xbb8d8c: b.hs            #0xbb8de0
    // 0xbb8d90: LoadField: r0 = r2->field_f
    //     0xbb8d90: ldur            w0, [x2, #0xf]
    // 0xbb8d94: DecompressPointer r0
    //     0xbb8d94: add             x0, x0, HEAP, lsl #32
    // 0xbb8d98: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xbb8d98: add             x16, x0, x3, lsl #2
    //     0xbb8d9c: ldur            w1, [x16, #0xf]
    // 0xbb8da0: DecompressPointer r1
    //     0xbb8da0: add             x1, x1, HEAP, lsl #32
    // 0xbb8da4: mov             x0, x1
    // 0xbb8da8: cmp             w0, NULL
    // 0xbb8dac: b.ne            #0xbb8dbc
    // 0xbb8db0: r0 = DEntities()
    //     0xbb8db0: bl              #0xa0d26c  ; AllocateDEntitiesStub -> DEntities (size=0x34)
    // 0xbb8db4: mov             x2, x0
    // 0xbb8db8: b               #0xbb8dc0
    // 0xbb8dbc: mov             x2, x0
    // 0xbb8dc0: ldr             x1, [fp, #0x18]
    // 0xbb8dc4: r0 = bagLineThemeItem()
    //     0xbb8dc4: bl              #0xbb8de4  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/duplicate_order_confirm_bottom_sheet.dart] ::bagLineThemeItem
    // 0xbb8dc8: LeaveFrame
    //     0xbb8dc8: mov             SP, fp
    //     0xbb8dcc: ldp             fp, lr, [SP], #0x10
    // 0xbb8dd0: ret
    //     0xbb8dd0: ret             
    // 0xbb8dd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb8dd4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb8dd8: b               #0xbb8d34
    // 0xbb8ddc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb8ddc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb8de0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb8de0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4016, size: 0x20, field offset: 0xc
//   const constructor, 
class DuplicateOrderConfirmBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc8038c, size: 0x24
    // 0xc8038c: EnterFrame
    //     0xc8038c: stp             fp, lr, [SP, #-0x10]!
    //     0xc80390: mov             fp, SP
    // 0xc80394: mov             x0, x1
    // 0xc80398: r1 = <DuplicateOrderConfirmBottomSheet>
    //     0xc80398: add             x1, PP, #0x48, lsl #12  ; [pp+0x48698] TypeArguments: <DuplicateOrderConfirmBottomSheet>
    //     0xc8039c: ldr             x1, [x1, #0x698]
    // 0xc803a0: r0 = _DuplicateOrderConfirmBottomSheet()
    //     0xc803a0: bl              #0xc803b0  ; Allocate_DuplicateOrderConfirmBottomSheetStub -> _DuplicateOrderConfirmBottomSheet (size=0x14)
    // 0xc803a4: LeaveFrame
    //     0xc803a4: mov             SP, fp
    //     0xc803a8: ldp             fp, lr, [SP], #0x10
    // 0xc803ac: ret
    //     0xc803ac: ret             
  }
}
