// lib: , url: package:customer_app/app/presentation/views/line/home/<USER>/product_grid_item_view.dart

// class id: 1049525, size: 0x8
class :: {
}

// class id: 3246, size: 0x14, field offset: 0x14
class _ProductGridItemState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbe4594, size: 0x1a0
    // 0xbe4594: EnterFrame
    //     0xbe4594: stp             fp, lr, [SP, #-0x10]!
    //     0xbe4598: mov             fp, SP
    // 0xbe459c: AllocStack(0x38)
    //     0xbe459c: sub             SP, SP, #0x38
    // 0xbe45a0: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */)
    //     0xbe45a0: stur            x1, [fp, #-8]
    // 0xbe45a4: CheckStackOverflow
    //     0xbe45a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe45a8: cmp             SP, x16
    //     0xbe45ac: b.ls            #0xbe4720
    // 0xbe45b0: r1 = 1
    //     0xbe45b0: movz            x1, #0x1
    // 0xbe45b4: r0 = AllocateContext()
    //     0xbe45b4: bl              #0x16f6108  ; AllocateContextStub
    // 0xbe45b8: mov             x2, x0
    // 0xbe45bc: ldur            x1, [fp, #-8]
    // 0xbe45c0: stur            x2, [fp, #-0x10]
    // 0xbe45c4: StoreField: r2->field_f = r1
    //     0xbe45c4: stur            w1, [x2, #0xf]
    // 0xbe45c8: LoadField: r0 = r1->field_b
    //     0xbe45c8: ldur            w0, [x1, #0xb]
    // 0xbe45cc: DecompressPointer r0
    //     0xbe45cc: add             x0, x0, HEAP, lsl #32
    // 0xbe45d0: cmp             w0, NULL
    // 0xbe45d4: b.eq            #0xbe4728
    // 0xbe45d8: LoadField: r3 = r0->field_2b
    //     0xbe45d8: ldur            w3, [x0, #0x2b]
    // 0xbe45dc: DecompressPointer r3
    //     0xbe45dc: add             x3, x3, HEAP, lsl #32
    // 0xbe45e0: r0 = LoadClassIdInstr(r3)
    //     0xbe45e0: ldur            x0, [x3, #-1]
    //     0xbe45e4: ubfx            x0, x0, #0xc, #0x14
    // 0xbe45e8: r16 = "search_page"
    //     0xbe45e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee58] "search_page"
    //     0xbe45ec: ldr             x16, [x16, #0xe58]
    // 0xbe45f0: stp             x16, x3, [SP]
    // 0xbe45f4: mov             lr, x0
    // 0xbe45f8: ldr             lr, [x21, lr, lsl #3]
    // 0xbe45fc: blr             lr
    // 0xbe4600: tbnz            w0, #4, #0xbe460c
    // 0xbe4604: d0 = 16.000000
    //     0xbe4604: fmov            d0, #16.00000000
    // 0xbe4608: b               #0xbe4610
    // 0xbe460c: d0 = 2.000000
    //     0xbe460c: fmov            d0, #2.00000000
    // 0xbe4610: ldur            x1, [fp, #-8]
    // 0xbe4614: stur            d0, [fp, #-0x20]
    // 0xbe4618: LoadField: r0 = r1->field_b
    //     0xbe4618: ldur            w0, [x1, #0xb]
    // 0xbe461c: DecompressPointer r0
    //     0xbe461c: add             x0, x0, HEAP, lsl #32
    // 0xbe4620: cmp             w0, NULL
    // 0xbe4624: b.eq            #0xbe472c
    // 0xbe4628: LoadField: r2 = r0->field_2b
    //     0xbe4628: ldur            w2, [x0, #0x2b]
    // 0xbe462c: DecompressPointer r2
    //     0xbe462c: add             x2, x2, HEAP, lsl #32
    // 0xbe4630: r0 = LoadClassIdInstr(r2)
    //     0xbe4630: ldur            x0, [x2, #-1]
    //     0xbe4634: ubfx            x0, x0, #0xc, #0x14
    // 0xbe4638: r16 = "search_page"
    //     0xbe4638: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee58] "search_page"
    //     0xbe463c: ldr             x16, [x16, #0xe58]
    // 0xbe4640: stp             x16, x2, [SP]
    // 0xbe4644: mov             lr, x0
    // 0xbe4648: ldr             lr, [x21, lr, lsl #3]
    // 0xbe464c: blr             lr
    // 0xbe4650: tbnz            w0, #4, #0xbe4660
    // 0xbe4654: d1 = 0.526316
    //     0xbe4654: add             x17, PP, #0x58, lsl #12  ; [pp+0x58708] IMM: double(0.5263157894736842) from 0x3fe0d79435e50d79
    //     0xbe4658: ldr             d1, [x17, #0x708]
    // 0xbe465c: b               #0xbe4668
    // 0xbe4660: d1 = 0.454545
    //     0xbe4660: add             x17, PP, #0x61, lsl #12  ; [pp+0x61c50] IMM: double(0.45454545454545453) from 0x3fdd1745d1745d17
    //     0xbe4664: ldr             d1, [x17, #0xc50]
    // 0xbe4668: ldur            x0, [fp, #-8]
    // 0xbe466c: ldur            d0, [fp, #-0x20]
    // 0xbe4670: stur            d1, [fp, #-0x28]
    // 0xbe4674: r0 = SliverGridDelegateWithFixedCrossAxisCount()
    //     0xbe4674: bl              #0xa4d630  ; AllocateSliverGridDelegateWithFixedCrossAxisCountStub -> SliverGridDelegateWithFixedCrossAxisCount (size=0x2c)
    // 0xbe4678: mov             x1, x0
    // 0xbe467c: r0 = 2
    //     0xbe467c: movz            x0, #0x2
    // 0xbe4680: stur            x1, [fp, #-0x18]
    // 0xbe4684: StoreField: r1->field_7 = r0
    //     0xbe4684: stur            x0, [x1, #7]
    // 0xbe4688: ldur            d0, [fp, #-0x20]
    // 0xbe468c: StoreField: r1->field_f = d0
    //     0xbe468c: stur            d0, [x1, #0xf]
    // 0xbe4690: d0 = 4.000000
    //     0xbe4690: fmov            d0, #4.00000000
    // 0xbe4694: ArrayStore: r1[0] = d0  ; List_8
    //     0xbe4694: stur            d0, [x1, #0x17]
    // 0xbe4698: ldur            d0, [fp, #-0x28]
    // 0xbe469c: StoreField: r1->field_1f = d0
    //     0xbe469c: stur            d0, [x1, #0x1f]
    // 0xbe46a0: ldur            x0, [fp, #-8]
    // 0xbe46a4: LoadField: r2 = r0->field_b
    //     0xbe46a4: ldur            w2, [x0, #0xb]
    // 0xbe46a8: DecompressPointer r2
    //     0xbe46a8: add             x2, x2, HEAP, lsl #32
    // 0xbe46ac: cmp             w2, NULL
    // 0xbe46b0: b.eq            #0xbe4730
    // 0xbe46b4: LoadField: r0 = r2->field_b
    //     0xbe46b4: ldur            w0, [x2, #0xb]
    // 0xbe46b8: DecompressPointer r0
    //     0xbe46b8: add             x0, x0, HEAP, lsl #32
    // 0xbe46bc: r2 = LoadClassIdInstr(r0)
    //     0xbe46bc: ldur            x2, [x0, #-1]
    //     0xbe46c0: ubfx            x2, x2, #0xc, #0x14
    // 0xbe46c4: str             x0, [SP]
    // 0xbe46c8: mov             x0, x2
    // 0xbe46cc: r0 = GDT[cid_x0 + 0xc898]()
    //     0xbe46cc: movz            x17, #0xc898
    //     0xbe46d0: add             lr, x0, x17
    //     0xbe46d4: ldr             lr, [x21, lr, lsl #3]
    //     0xbe46d8: blr             lr
    // 0xbe46dc: ldur            x2, [fp, #-0x10]
    // 0xbe46e0: r1 = Function '<anonymous closure>':.
    //     0xbe46e0: add             x1, PP, #0x61, lsl #12  ; [pp+0x61c58] AnonymousClosure: (0xbe4754), in [package:customer_app/app/presentation/views/line/home/<USER>/product_grid_item_view.dart] _ProductGridItemState::build (0xbe4594)
    //     0xbe46e4: ldr             x1, [x1, #0xc58]
    // 0xbe46e8: stur            x0, [fp, #-8]
    // 0xbe46ec: r0 = AllocateClosure()
    //     0xbe46ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe46f0: stur            x0, [fp, #-0x10]
    // 0xbe46f4: r0 = GridView()
    //     0xbe46f4: bl              #0x9010e0  ; AllocateGridViewStub -> GridView (size=0x60)
    // 0xbe46f8: mov             x1, x0
    // 0xbe46fc: ldur            x2, [fp, #-0x18]
    // 0xbe4700: ldur            x3, [fp, #-0x10]
    // 0xbe4704: ldur            x5, [fp, #-8]
    // 0xbe4708: stur            x0, [fp, #-8]
    // 0xbe470c: r0 = GridView.builder()
    //     0xbe470c: bl              #0x900fc8  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.builder
    // 0xbe4710: ldur            x0, [fp, #-8]
    // 0xbe4714: LeaveFrame
    //     0xbe4714: mov             SP, fp
    //     0xbe4718: ldp             fp, lr, [SP], #0x10
    // 0xbe471c: ret
    //     0xbe471c: ret             
    // 0xbe4720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe4720: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe4724: b               #0xbe45b0
    // 0xbe4728: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe4728: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe472c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbe472c: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbe4730: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe4730: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbe4754, size: 0x4f0
    // 0xbe4754: EnterFrame
    //     0xbe4754: stp             fp, lr, [SP, #-0x10]!
    //     0xbe4758: mov             fp, SP
    // 0xbe475c: AllocStack(0x58)
    //     0xbe475c: sub             SP, SP, #0x58
    // 0xbe4760: SetupParameters()
    //     0xbe4760: ldr             x0, [fp, #0x20]
    //     0xbe4764: ldur            w1, [x0, #0x17]
    //     0xbe4768: add             x1, x1, HEAP, lsl #32
    //     0xbe476c: stur            x1, [fp, #-8]
    // 0xbe4770: CheckStackOverflow
    //     0xbe4770: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe4774: cmp             SP, x16
    //     0xbe4778: b.ls            #0xbe4c2c
    // 0xbe477c: r1 = 1
    //     0xbe477c: movz            x1, #0x1
    // 0xbe4780: r0 = AllocateContext()
    //     0xbe4780: bl              #0x16f6108  ; AllocateContextStub
    // 0xbe4784: mov             x1, x0
    // 0xbe4788: ldur            x0, [fp, #-8]
    // 0xbe478c: stur            x1, [fp, #-0x10]
    // 0xbe4790: StoreField: r1->field_b = r0
    //     0xbe4790: stur            w0, [x1, #0xb]
    // 0xbe4794: ldr             x2, [fp, #0x10]
    // 0xbe4798: StoreField: r1->field_f = r2
    //     0xbe4798: stur            w2, [x1, #0xf]
    // 0xbe479c: str             x2, [SP]
    // 0xbe47a0: r0 = _interpolateSingle()
    //     0xbe47a0: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbe47a4: r1 = <String>
    //     0xbe47a4: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbe47a8: stur            x0, [fp, #-0x18]
    // 0xbe47ac: r0 = ValueKey()
    //     0xbe47ac: bl              #0x68b554  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xbe47b0: mov             x1, x0
    // 0xbe47b4: ldur            x0, [fp, #-0x18]
    // 0xbe47b8: stur            x1, [fp, #-0x20]
    // 0xbe47bc: StoreField: r1->field_b = r0
    //     0xbe47bc: stur            w0, [x1, #0xb]
    // 0xbe47c0: ldur            x2, [fp, #-8]
    // 0xbe47c4: LoadField: r0 = r2->field_f
    //     0xbe47c4: ldur            w0, [x2, #0xf]
    // 0xbe47c8: DecompressPointer r0
    //     0xbe47c8: add             x0, x0, HEAP, lsl #32
    // 0xbe47cc: LoadField: r3 = r0->field_b
    //     0xbe47cc: ldur            w3, [x0, #0xb]
    // 0xbe47d0: DecompressPointer r3
    //     0xbe47d0: add             x3, x3, HEAP, lsl #32
    // 0xbe47d4: cmp             w3, NULL
    // 0xbe47d8: b.eq            #0xbe4c34
    // 0xbe47dc: LoadField: r0 = r3->field_2b
    //     0xbe47dc: ldur            w0, [x3, #0x2b]
    // 0xbe47e0: DecompressPointer r0
    //     0xbe47e0: add             x0, x0, HEAP, lsl #32
    // 0xbe47e4: r3 = LoadClassIdInstr(r0)
    //     0xbe47e4: ldur            x3, [x0, #-1]
    //     0xbe47e8: ubfx            x3, x3, #0xc, #0x14
    // 0xbe47ec: r16 = "search_page"
    //     0xbe47ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee58] "search_page"
    //     0xbe47f0: ldr             x16, [x16, #0xe58]
    // 0xbe47f4: stp             x16, x0, [SP]
    // 0xbe47f8: mov             x0, x3
    // 0xbe47fc: mov             lr, x0
    // 0xbe4800: ldr             lr, [x21, lr, lsl #3]
    // 0xbe4804: blr             lr
    // 0xbe4808: tbnz            w0, #4, #0xbe4818
    // 0xbe480c: d0 = 0.714286
    //     0xbe480c: add             x17, PP, #0x61, lsl #12  ; [pp+0x61c60] IMM: double(0.7142857142857143) from 0x3fe6db6db6db6db7
    //     0xbe4810: ldr             d0, [x17, #0xc60]
    // 0xbe4814: b               #0xbe4820
    // 0xbe4818: d0 = 0.740741
    //     0xbe4818: add             x17, PP, #0x52, lsl #12  ; [pp+0x52d68] IMM: double(0.7407407407407407) from 0x3fe7b425ed097b42
    //     0xbe481c: ldr             d0, [x17, #0xd68]
    // 0xbe4820: ldur            x1, [fp, #-8]
    // 0xbe4824: ldur            x2, [fp, #-0x10]
    // 0xbe4828: stur            d0, [fp, #-0x48]
    // 0xbe482c: LoadField: r0 = r1->field_f
    //     0xbe482c: ldur            w0, [x1, #0xf]
    // 0xbe4830: DecompressPointer r0
    //     0xbe4830: add             x0, x0, HEAP, lsl #32
    // 0xbe4834: LoadField: r3 = r0->field_b
    //     0xbe4834: ldur            w3, [x0, #0xb]
    // 0xbe4838: DecompressPointer r3
    //     0xbe4838: add             x3, x3, HEAP, lsl #32
    // 0xbe483c: cmp             w3, NULL
    // 0xbe4840: b.eq            #0xbe4c38
    // 0xbe4844: LoadField: r0 = r3->field_b
    //     0xbe4844: ldur            w0, [x3, #0xb]
    // 0xbe4848: DecompressPointer r0
    //     0xbe4848: add             x0, x0, HEAP, lsl #32
    // 0xbe484c: LoadField: r3 = r2->field_f
    //     0xbe484c: ldur            w3, [x2, #0xf]
    // 0xbe4850: DecompressPointer r3
    //     0xbe4850: add             x3, x3, HEAP, lsl #32
    // 0xbe4854: r4 = LoadClassIdInstr(r0)
    //     0xbe4854: ldur            x4, [x0, #-1]
    //     0xbe4858: ubfx            x4, x4, #0xc, #0x14
    // 0xbe485c: stp             x3, x0, [SP]
    // 0xbe4860: mov             x0, x4
    // 0xbe4864: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe4864: sub             lr, x0, #0xb7
    //     0xbe4868: ldr             lr, [x21, lr, lsl #3]
    //     0xbe486c: blr             lr
    // 0xbe4870: cmp             w0, NULL
    // 0xbe4874: b.ne            #0xbe4880
    // 0xbe4878: r4 = Null
    //     0xbe4878: mov             x4, NULL
    // 0xbe487c: b               #0xbe4898
    // 0xbe4880: LoadField: r1 = r0->field_37
    //     0xbe4880: ldur            w1, [x0, #0x37]
    // 0xbe4884: DecompressPointer r1
    //     0xbe4884: add             x1, x1, HEAP, lsl #32
    // 0xbe4888: cmp             w1, NULL
    // 0xbe488c: b.eq            #0xbe4c3c
    // 0xbe4890: LoadField: r0 = r1->field_b
    //     0xbe4890: ldur            w0, [x1, #0xb]
    // 0xbe4894: mov             x4, x0
    // 0xbe4898: ldur            x0, [fp, #-8]
    // 0xbe489c: ldur            x3, [fp, #-0x10]
    // 0xbe48a0: ldur            d0, [fp, #-0x48]
    // 0xbe48a4: stur            x4, [fp, #-0x18]
    // 0xbe48a8: r1 = Function '<anonymous closure>':.
    //     0xbe48a8: add             x1, PP, #0x61, lsl #12  ; [pp+0x61c68] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xbe48ac: ldr             x1, [x1, #0xc68]
    // 0xbe48b0: r2 = Null
    //     0xbe48b0: mov             x2, NULL
    // 0xbe48b4: r0 = AllocateClosure()
    //     0xbe48b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe48b8: ldur            x2, [fp, #-0x10]
    // 0xbe48bc: r1 = Function '<anonymous closure>':.
    //     0xbe48bc: add             x1, PP, #0x61, lsl #12  ; [pp+0x61c70] AnonymousClosure: (0xbe6e14), in [package:customer_app/app/presentation/views/line/home/<USER>/product_grid_item_view.dart] _ProductGridItemState::build (0xbe4594)
    //     0xbe48c0: ldr             x1, [x1, #0xc70]
    // 0xbe48c4: stur            x0, [fp, #-0x28]
    // 0xbe48c8: r0 = AllocateClosure()
    //     0xbe48c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe48cc: stur            x0, [fp, #-0x30]
    // 0xbe48d0: r0 = PageView()
    //     0xbe48d0: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xbe48d4: mov             x1, x0
    // 0xbe48d8: ldur            x2, [fp, #-0x30]
    // 0xbe48dc: ldur            x3, [fp, #-0x18]
    // 0xbe48e0: ldur            x5, [fp, #-0x28]
    // 0xbe48e4: stur            x0, [fp, #-0x18]
    // 0xbe48e8: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xbe48e8: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xbe48ec: r0 = PageView.builder()
    //     0xbe48ec: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xbe48f0: r0 = AspectRatio()
    //     0xbe48f0: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xbe48f4: mov             x1, x0
    // 0xbe48f8: ldur            d0, [fp, #-0x48]
    // 0xbe48fc: stur            x1, [fp, #-0x28]
    // 0xbe4900: StoreField: r1->field_f = d0
    //     0xbe4900: stur            d0, [x1, #0xf]
    // 0xbe4904: ldur            x0, [fp, #-0x18]
    // 0xbe4908: StoreField: r1->field_b = r0
    //     0xbe4908: stur            w0, [x1, #0xb]
    // 0xbe490c: ldur            x2, [fp, #-8]
    // 0xbe4910: LoadField: r0 = r2->field_f
    //     0xbe4910: ldur            w0, [x2, #0xf]
    // 0xbe4914: DecompressPointer r0
    //     0xbe4914: add             x0, x0, HEAP, lsl #32
    // 0xbe4918: LoadField: r3 = r0->field_b
    //     0xbe4918: ldur            w3, [x0, #0xb]
    // 0xbe491c: DecompressPointer r3
    //     0xbe491c: add             x3, x3, HEAP, lsl #32
    // 0xbe4920: cmp             w3, NULL
    // 0xbe4924: b.eq            #0xbe4c40
    // 0xbe4928: LoadField: r0 = r3->field_b
    //     0xbe4928: ldur            w0, [x3, #0xb]
    // 0xbe492c: DecompressPointer r0
    //     0xbe492c: add             x0, x0, HEAP, lsl #32
    // 0xbe4930: ldur            x3, [fp, #-0x10]
    // 0xbe4934: LoadField: r4 = r3->field_f
    //     0xbe4934: ldur            w4, [x3, #0xf]
    // 0xbe4938: DecompressPointer r4
    //     0xbe4938: add             x4, x4, HEAP, lsl #32
    // 0xbe493c: r5 = LoadClassIdInstr(r0)
    //     0xbe493c: ldur            x5, [x0, #-1]
    //     0xbe4940: ubfx            x5, x5, #0xc, #0x14
    // 0xbe4944: stp             x4, x0, [SP]
    // 0xbe4948: mov             x0, x5
    // 0xbe494c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe494c: sub             lr, x0, #0xb7
    //     0xbe4950: ldr             lr, [x21, lr, lsl #3]
    //     0xbe4954: blr             lr
    // 0xbe4958: cmp             w0, NULL
    // 0xbe495c: b.ne            #0xbe4968
    // 0xbe4960: r0 = Null
    //     0xbe4960: mov             x0, NULL
    // 0xbe4964: b               #0xbe4988
    // 0xbe4968: LoadField: r1 = r0->field_33
    //     0xbe4968: ldur            w1, [x0, #0x33]
    // 0xbe496c: DecompressPointer r1
    //     0xbe496c: add             x1, x1, HEAP, lsl #32
    // 0xbe4970: cmp             w1, NULL
    // 0xbe4974: b.ne            #0xbe4980
    // 0xbe4978: r0 = Null
    //     0xbe4978: mov             x0, NULL
    // 0xbe497c: b               #0xbe4988
    // 0xbe4980: LoadField: r0 = r1->field_7
    //     0xbe4980: ldur            w0, [x1, #7]
    // 0xbe4984: DecompressPointer r0
    //     0xbe4984: add             x0, x0, HEAP, lsl #32
    // 0xbe4988: cmp             w0, NULL
    // 0xbe498c: b.ne            #0xbe4998
    // 0xbe4990: r1 = ""
    //     0xbe4990: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe4994: b               #0xbe499c
    // 0xbe4998: mov             x1, x0
    // 0xbe499c: ldur            x2, [fp, #-8]
    // 0xbe49a0: ldur            x3, [fp, #-0x10]
    // 0xbe49a4: ldur            x4, [fp, #-0x20]
    // 0xbe49a8: ldur            x0, [fp, #-0x28]
    // 0xbe49ac: r0 = capitalizeFirstWord()
    //     0xbe49ac: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xbe49b0: ldr             x1, [fp, #0x18]
    // 0xbe49b4: stur            x0, [fp, #-0x18]
    // 0xbe49b8: r0 = of()
    //     0xbe49b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe49bc: LoadField: r1 = r0->field_87
    //     0xbe49bc: ldur            w1, [x0, #0x87]
    // 0xbe49c0: DecompressPointer r1
    //     0xbe49c0: add             x1, x1, HEAP, lsl #32
    // 0xbe49c4: LoadField: r0 = r1->field_2b
    //     0xbe49c4: ldur            w0, [x1, #0x2b]
    // 0xbe49c8: DecompressPointer r0
    //     0xbe49c8: add             x0, x0, HEAP, lsl #32
    // 0xbe49cc: r16 = 12.000000
    //     0xbe49cc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe49d0: ldr             x16, [x16, #0x9e8]
    // 0xbe49d4: r30 = Instance_Color
    //     0xbe49d4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe49d8: stp             lr, x16, [SP]
    // 0xbe49dc: mov             x1, x0
    // 0xbe49e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe49e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe49e4: ldr             x4, [x4, #0xaa0]
    // 0xbe49e8: r0 = copyWith()
    //     0xbe49e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe49ec: stur            x0, [fp, #-0x30]
    // 0xbe49f0: r0 = Text()
    //     0xbe49f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe49f4: mov             x1, x0
    // 0xbe49f8: ldur            x0, [fp, #-0x18]
    // 0xbe49fc: stur            x1, [fp, #-0x38]
    // 0xbe4a00: StoreField: r1->field_b = r0
    //     0xbe4a00: stur            w0, [x1, #0xb]
    // 0xbe4a04: ldur            x0, [fp, #-0x30]
    // 0xbe4a08: StoreField: r1->field_13 = r0
    //     0xbe4a08: stur            w0, [x1, #0x13]
    // 0xbe4a0c: r0 = Instance_TextOverflow
    //     0xbe4a0c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbe4a10: ldr             x0, [x0, #0xe10]
    // 0xbe4a14: StoreField: r1->field_2b = r0
    //     0xbe4a14: stur            w0, [x1, #0x2b]
    // 0xbe4a18: r0 = 2
    //     0xbe4a18: movz            x0, #0x2
    // 0xbe4a1c: StoreField: r1->field_37 = r0
    //     0xbe4a1c: stur            w0, [x1, #0x37]
    // 0xbe4a20: r0 = Padding()
    //     0xbe4a20: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe4a24: mov             x3, x0
    // 0xbe4a28: r0 = Instance_EdgeInsets
    //     0xbe4a28: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbe4a2c: ldr             x0, [x0, #0x770]
    // 0xbe4a30: stur            x3, [fp, #-0x18]
    // 0xbe4a34: StoreField: r3->field_f = r0
    //     0xbe4a34: stur            w0, [x3, #0xf]
    // 0xbe4a38: ldur            x0, [fp, #-0x38]
    // 0xbe4a3c: StoreField: r3->field_b = r0
    //     0xbe4a3c: stur            w0, [x3, #0xb]
    // 0xbe4a40: ldur            x0, [fp, #-8]
    // 0xbe4a44: LoadField: r1 = r0->field_f
    //     0xbe4a44: ldur            w1, [x0, #0xf]
    // 0xbe4a48: DecompressPointer r1
    //     0xbe4a48: add             x1, x1, HEAP, lsl #32
    // 0xbe4a4c: ldur            x4, [fp, #-0x10]
    // 0xbe4a50: LoadField: r2 = r4->field_f
    //     0xbe4a50: ldur            w2, [x4, #0xf]
    // 0xbe4a54: DecompressPointer r2
    //     0xbe4a54: add             x2, x2, HEAP, lsl #32
    // 0xbe4a58: r5 = LoadInt32Instr(r2)
    //     0xbe4a58: sbfx            x5, x2, #1, #0x1f
    //     0xbe4a5c: tbz             w2, #0, #0xbe4a64
    //     0xbe4a60: ldur            x5, [x2, #7]
    // 0xbe4a64: mov             x2, x5
    // 0xbe4a68: r0 = _buildRatingSection()
    //     0xbe4a68: bl              #0xbe5c34  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_grid_item_view.dart] _ProductGridItemState::_buildRatingSection
    // 0xbe4a6c: mov             x3, x0
    // 0xbe4a70: ldur            x0, [fp, #-8]
    // 0xbe4a74: stur            x3, [fp, #-0x30]
    // 0xbe4a78: LoadField: r1 = r0->field_f
    //     0xbe4a78: ldur            w1, [x0, #0xf]
    // 0xbe4a7c: DecompressPointer r1
    //     0xbe4a7c: add             x1, x1, HEAP, lsl #32
    // 0xbe4a80: ldur            x4, [fp, #-0x10]
    // 0xbe4a84: LoadField: r2 = r4->field_f
    //     0xbe4a84: ldur            w2, [x4, #0xf]
    // 0xbe4a88: DecompressPointer r2
    //     0xbe4a88: add             x2, x2, HEAP, lsl #32
    // 0xbe4a8c: r5 = LoadInt32Instr(r2)
    //     0xbe4a8c: sbfx            x5, x2, #1, #0x1f
    //     0xbe4a90: tbz             w2, #0, #0xbe4a98
    //     0xbe4a94: ldur            x5, [x2, #7]
    // 0xbe4a98: mov             x2, x5
    // 0xbe4a9c: r0 = _buildPriceSection()
    //     0xbe4a9c: bl              #0xbe563c  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_grid_item_view.dart] _ProductGridItemState::_buildPriceSection
    // 0xbe4aa0: mov             x3, x0
    // 0xbe4aa4: ldur            x0, [fp, #-8]
    // 0xbe4aa8: stur            x3, [fp, #-0x38]
    // 0xbe4aac: LoadField: r1 = r0->field_f
    //     0xbe4aac: ldur            w1, [x0, #0xf]
    // 0xbe4ab0: DecompressPointer r1
    //     0xbe4ab0: add             x1, x1, HEAP, lsl #32
    // 0xbe4ab4: ldur            x0, [fp, #-0x10]
    // 0xbe4ab8: LoadField: r2 = r0->field_f
    //     0xbe4ab8: ldur            w2, [x0, #0xf]
    // 0xbe4abc: DecompressPointer r2
    //     0xbe4abc: add             x2, x2, HEAP, lsl #32
    // 0xbe4ac0: r4 = LoadInt32Instr(r2)
    //     0xbe4ac0: sbfx            x4, x2, #1, #0x1f
    //     0xbe4ac4: tbz             w2, #0, #0xbe4acc
    //     0xbe4ac8: ldur            x4, [x2, #7]
    // 0xbe4acc: mov             x2, x4
    // 0xbe4ad0: r0 = _buildAddToBagSection()
    //     0xbe4ad0: bl              #0xbe4c44  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_grid_item_view.dart] _ProductGridItemState::_buildAddToBagSection
    // 0xbe4ad4: r1 = Null
    //     0xbe4ad4: mov             x1, NULL
    // 0xbe4ad8: r2 = 10
    //     0xbe4ad8: movz            x2, #0xa
    // 0xbe4adc: stur            x0, [fp, #-8]
    // 0xbe4ae0: r0 = AllocateArray()
    //     0xbe4ae0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe4ae4: mov             x2, x0
    // 0xbe4ae8: ldur            x0, [fp, #-0x28]
    // 0xbe4aec: stur            x2, [fp, #-0x40]
    // 0xbe4af0: StoreField: r2->field_f = r0
    //     0xbe4af0: stur            w0, [x2, #0xf]
    // 0xbe4af4: ldur            x0, [fp, #-0x18]
    // 0xbe4af8: StoreField: r2->field_13 = r0
    //     0xbe4af8: stur            w0, [x2, #0x13]
    // 0xbe4afc: ldur            x0, [fp, #-0x30]
    // 0xbe4b00: ArrayStore: r2[0] = r0  ; List_4
    //     0xbe4b00: stur            w0, [x2, #0x17]
    // 0xbe4b04: ldur            x0, [fp, #-0x38]
    // 0xbe4b08: StoreField: r2->field_1b = r0
    //     0xbe4b08: stur            w0, [x2, #0x1b]
    // 0xbe4b0c: ldur            x0, [fp, #-8]
    // 0xbe4b10: StoreField: r2->field_1f = r0
    //     0xbe4b10: stur            w0, [x2, #0x1f]
    // 0xbe4b14: r1 = <Widget>
    //     0xbe4b14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe4b18: r0 = AllocateGrowableArray()
    //     0xbe4b18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe4b1c: mov             x1, x0
    // 0xbe4b20: ldur            x0, [fp, #-0x40]
    // 0xbe4b24: stur            x1, [fp, #-8]
    // 0xbe4b28: StoreField: r1->field_f = r0
    //     0xbe4b28: stur            w0, [x1, #0xf]
    // 0xbe4b2c: r0 = 10
    //     0xbe4b2c: movz            x0, #0xa
    // 0xbe4b30: StoreField: r1->field_b = r0
    //     0xbe4b30: stur            w0, [x1, #0xb]
    // 0xbe4b34: r0 = Column()
    //     0xbe4b34: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbe4b38: mov             x3, x0
    // 0xbe4b3c: r0 = Instance_Axis
    //     0xbe4b3c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbe4b40: stur            x3, [fp, #-0x18]
    // 0xbe4b44: StoreField: r3->field_f = r0
    //     0xbe4b44: stur            w0, [x3, #0xf]
    // 0xbe4b48: r0 = Instance_MainAxisAlignment
    //     0xbe4b48: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbe4b4c: ldr             x0, [x0, #0xa08]
    // 0xbe4b50: StoreField: r3->field_13 = r0
    //     0xbe4b50: stur            w0, [x3, #0x13]
    // 0xbe4b54: r0 = Instance_MainAxisSize
    //     0xbe4b54: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xbe4b58: ldr             x0, [x0, #0xdd0]
    // 0xbe4b5c: ArrayStore: r3[0] = r0  ; List_4
    //     0xbe4b5c: stur            w0, [x3, #0x17]
    // 0xbe4b60: r0 = Instance_CrossAxisAlignment
    //     0xbe4b60: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbe4b64: ldr             x0, [x0, #0x890]
    // 0xbe4b68: StoreField: r3->field_1b = r0
    //     0xbe4b68: stur            w0, [x3, #0x1b]
    // 0xbe4b6c: r0 = Instance_VerticalDirection
    //     0xbe4b6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe4b70: ldr             x0, [x0, #0xa20]
    // 0xbe4b74: StoreField: r3->field_23 = r0
    //     0xbe4b74: stur            w0, [x3, #0x23]
    // 0xbe4b78: r0 = Instance_Clip
    //     0xbe4b78: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe4b7c: ldr             x0, [x0, #0x38]
    // 0xbe4b80: StoreField: r3->field_2b = r0
    //     0xbe4b80: stur            w0, [x3, #0x2b]
    // 0xbe4b84: StoreField: r3->field_2f = rZR
    //     0xbe4b84: stur            xzr, [x3, #0x2f]
    // 0xbe4b88: ldur            x0, [fp, #-8]
    // 0xbe4b8c: StoreField: r3->field_b = r0
    //     0xbe4b8c: stur            w0, [x3, #0xb]
    // 0xbe4b90: ldur            x2, [fp, #-0x10]
    // 0xbe4b94: r1 = Function '<anonymous closure>':.
    //     0xbe4b94: add             x1, PP, #0x61, lsl #12  ; [pp+0x61c78] AnonymousClosure: (0xbe6b18), in [package:customer_app/app/presentation/views/line/home/<USER>/product_grid_item_view.dart] _ProductGridItemState::build (0xbe4594)
    //     0xbe4b98: ldr             x1, [x1, #0xc78]
    // 0xbe4b9c: r0 = AllocateClosure()
    //     0xbe4b9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe4ba0: stur            x0, [fp, #-8]
    // 0xbe4ba4: r0 = VisibilityDetector()
    //     0xbe4ba4: bl              #0xa4f4ac  ; AllocateVisibilityDetectorStub -> VisibilityDetector (size=0x14)
    // 0xbe4ba8: mov             x1, x0
    // 0xbe4bac: ldur            x0, [fp, #-8]
    // 0xbe4bb0: stur            x1, [fp, #-0x28]
    // 0xbe4bb4: StoreField: r1->field_f = r0
    //     0xbe4bb4: stur            w0, [x1, #0xf]
    // 0xbe4bb8: ldur            x0, [fp, #-0x18]
    // 0xbe4bbc: StoreField: r1->field_b = r0
    //     0xbe4bbc: stur            w0, [x1, #0xb]
    // 0xbe4bc0: ldur            x0, [fp, #-0x20]
    // 0xbe4bc4: StoreField: r1->field_7 = r0
    //     0xbe4bc4: stur            w0, [x1, #7]
    // 0xbe4bc8: r0 = InkWell()
    //     0xbe4bc8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbe4bcc: mov             x3, x0
    // 0xbe4bd0: ldur            x0, [fp, #-0x28]
    // 0xbe4bd4: stur            x3, [fp, #-8]
    // 0xbe4bd8: StoreField: r3->field_b = r0
    //     0xbe4bd8: stur            w0, [x3, #0xb]
    // 0xbe4bdc: ldur            x2, [fp, #-0x10]
    // 0xbe4be0: r1 = Function '<anonymous closure>':.
    //     0xbe4be0: add             x1, PP, #0x61, lsl #12  ; [pp+0x61c80] AnonymousClosure: (0xbe68bc), in [package:customer_app/app/presentation/views/line/home/<USER>/product_grid_item_view.dart] _ProductGridItemState::build (0xbe4594)
    //     0xbe4be4: ldr             x1, [x1, #0xc80]
    // 0xbe4be8: r0 = AllocateClosure()
    //     0xbe4be8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe4bec: mov             x1, x0
    // 0xbe4bf0: ldur            x0, [fp, #-8]
    // 0xbe4bf4: StoreField: r0->field_f = r1
    //     0xbe4bf4: stur            w1, [x0, #0xf]
    // 0xbe4bf8: r1 = true
    //     0xbe4bf8: add             x1, NULL, #0x20  ; true
    // 0xbe4bfc: StoreField: r0->field_43 = r1
    //     0xbe4bfc: stur            w1, [x0, #0x43]
    // 0xbe4c00: r2 = Instance_BoxShape
    //     0xbe4c00: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbe4c04: ldr             x2, [x2, #0x80]
    // 0xbe4c08: StoreField: r0->field_47 = r2
    //     0xbe4c08: stur            w2, [x0, #0x47]
    // 0xbe4c0c: StoreField: r0->field_6f = r1
    //     0xbe4c0c: stur            w1, [x0, #0x6f]
    // 0xbe4c10: r2 = false
    //     0xbe4c10: add             x2, NULL, #0x30  ; false
    // 0xbe4c14: StoreField: r0->field_73 = r2
    //     0xbe4c14: stur            w2, [x0, #0x73]
    // 0xbe4c18: StoreField: r0->field_83 = r1
    //     0xbe4c18: stur            w1, [x0, #0x83]
    // 0xbe4c1c: StoreField: r0->field_7b = r2
    //     0xbe4c1c: stur            w2, [x0, #0x7b]
    // 0xbe4c20: LeaveFrame
    //     0xbe4c20: mov             SP, fp
    //     0xbe4c24: ldp             fp, lr, [SP], #0x10
    // 0xbe4c28: ret
    //     0xbe4c28: ret             
    // 0xbe4c2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe4c2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe4c30: b               #0xbe477c
    // 0xbe4c34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe4c34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe4c38: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbe4c38: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbe4c3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe4c3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe4c40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe4c40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildAddToBagSection(/* No info */) {
    // ** addr: 0xbe4c44, size: 0x674
    // 0xbe4c44: EnterFrame
    //     0xbe4c44: stp             fp, lr, [SP, #-0x10]!
    //     0xbe4c48: mov             fp, SP
    // 0xbe4c4c: AllocStack(0x58)
    //     0xbe4c4c: sub             SP, SP, #0x58
    // 0xbe4c50: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbe4c50: stur            x1, [fp, #-8]
    //     0xbe4c54: stur            x2, [fp, #-0x10]
    // 0xbe4c58: CheckStackOverflow
    //     0xbe4c58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe4c5c: cmp             SP, x16
    //     0xbe4c60: b.ls            #0xbe5284
    // 0xbe4c64: r1 = 2
    //     0xbe4c64: movz            x1, #0x2
    // 0xbe4c68: r0 = AllocateContext()
    //     0xbe4c68: bl              #0x16f6108  ; AllocateContextStub
    // 0xbe4c6c: mov             x3, x0
    // 0xbe4c70: ldur            x2, [fp, #-8]
    // 0xbe4c74: stur            x3, [fp, #-0x20]
    // 0xbe4c78: StoreField: r3->field_f = r2
    //     0xbe4c78: stur            w2, [x3, #0xf]
    // 0xbe4c7c: ldur            x4, [fp, #-0x10]
    // 0xbe4c80: r0 = BoxInt64Instr(r4)
    //     0xbe4c80: sbfiz           x0, x4, #1, #0x1f
    //     0xbe4c84: cmp             x4, x0, asr #1
    //     0xbe4c88: b.eq            #0xbe4c94
    //     0xbe4c8c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe4c90: stur            x4, [x0, #7]
    // 0xbe4c94: stur            x0, [fp, #-0x18]
    // 0xbe4c98: StoreField: r3->field_13 = r0
    //     0xbe4c98: stur            w0, [x3, #0x13]
    // 0xbe4c9c: LoadField: r1 = r2->field_b
    //     0xbe4c9c: ldur            w1, [x2, #0xb]
    // 0xbe4ca0: DecompressPointer r1
    //     0xbe4ca0: add             x1, x1, HEAP, lsl #32
    // 0xbe4ca4: cmp             w1, NULL
    // 0xbe4ca8: b.eq            #0xbe528c
    // 0xbe4cac: LoadField: r4 = r1->field_f
    //     0xbe4cac: ldur            w4, [x1, #0xf]
    // 0xbe4cb0: DecompressPointer r4
    //     0xbe4cb0: add             x4, x4, HEAP, lsl #32
    // 0xbe4cb4: LoadField: r5 = r4->field_1f
    //     0xbe4cb4: ldur            w5, [x4, #0x1f]
    // 0xbe4cb8: DecompressPointer r5
    //     0xbe4cb8: add             x5, x5, HEAP, lsl #32
    // 0xbe4cbc: cmp             w5, NULL
    // 0xbe4cc0: b.ne            #0xbe4ccc
    // 0xbe4cc4: r5 = Null
    //     0xbe4cc4: mov             x5, NULL
    // 0xbe4cc8: b               #0xbe4cd8
    // 0xbe4ccc: LoadField: r6 = r5->field_7
    //     0xbe4ccc: ldur            w6, [x5, #7]
    // 0xbe4cd0: DecompressPointer r6
    //     0xbe4cd0: add             x6, x6, HEAP, lsl #32
    // 0xbe4cd4: mov             x5, x6
    // 0xbe4cd8: cmp             w5, NULL
    // 0xbe4cdc: b.eq            #0xbe5274
    // 0xbe4ce0: tbnz            w5, #4, #0xbe5274
    // 0xbe4ce4: LoadField: r5 = r4->field_3f
    //     0xbe4ce4: ldur            w5, [x4, #0x3f]
    // 0xbe4ce8: DecompressPointer r5
    //     0xbe4ce8: add             x5, x5, HEAP, lsl #32
    // 0xbe4cec: cmp             w5, NULL
    // 0xbe4cf0: b.ne            #0xbe4cfc
    // 0xbe4cf4: r4 = Null
    //     0xbe4cf4: mov             x4, NULL
    // 0xbe4cf8: b               #0xbe4d04
    // 0xbe4cfc: LoadField: r4 = r5->field_23
    //     0xbe4cfc: ldur            w4, [x5, #0x23]
    // 0xbe4d00: DecompressPointer r4
    //     0xbe4d00: add             x4, x4, HEAP, lsl #32
    // 0xbe4d04: cmp             w4, NULL
    // 0xbe4d08: b.eq            #0xbe5274
    // 0xbe4d0c: tbnz            w4, #4, #0xbe5274
    // 0xbe4d10: LoadField: r4 = r1->field_33
    //     0xbe4d10: ldur            w4, [x1, #0x33]
    // 0xbe4d14: DecompressPointer r4
    //     0xbe4d14: add             x4, x4, HEAP, lsl #32
    // 0xbe4d18: tbnz            w4, #4, #0xbe5274
    // 0xbe4d1c: r16 = <Size?>
    //     0xbe4d1c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27768] TypeArguments: <Size?>
    //     0xbe4d20: ldr             x16, [x16, #0x768]
    // 0xbe4d24: r30 = Instance_Size
    //     0xbe4d24: add             lr, PP, #0x52, lsl #12  ; [pp+0x52da0] Obj!Size@d6c221
    //     0xbe4d28: ldr             lr, [lr, #0xda0]
    // 0xbe4d2c: stp             lr, x16, [SP]
    // 0xbe4d30: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe4d30: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe4d34: r0 = all()
    //     0xbe4d34: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe4d38: mov             x2, x0
    // 0xbe4d3c: ldur            x1, [fp, #-8]
    // 0xbe4d40: stur            x2, [fp, #-0x28]
    // 0xbe4d44: LoadField: r0 = r1->field_b
    //     0xbe4d44: ldur            w0, [x1, #0xb]
    // 0xbe4d48: DecompressPointer r0
    //     0xbe4d48: add             x0, x0, HEAP, lsl #32
    // 0xbe4d4c: cmp             w0, NULL
    // 0xbe4d50: b.eq            #0xbe5290
    // 0xbe4d54: LoadField: r3 = r0->field_b
    //     0xbe4d54: ldur            w3, [x0, #0xb]
    // 0xbe4d58: DecompressPointer r3
    //     0xbe4d58: add             x3, x3, HEAP, lsl #32
    // 0xbe4d5c: r0 = LoadClassIdInstr(r3)
    //     0xbe4d5c: ldur            x0, [x3, #-1]
    //     0xbe4d60: ubfx            x0, x0, #0xc, #0x14
    // 0xbe4d64: ldur            x16, [fp, #-0x18]
    // 0xbe4d68: stp             x16, x3, [SP]
    // 0xbe4d6c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe4d6c: sub             lr, x0, #0xb7
    //     0xbe4d70: ldr             lr, [x21, lr, lsl #3]
    //     0xbe4d74: blr             lr
    // 0xbe4d78: cmp             w0, NULL
    // 0xbe4d7c: b.ne            #0xbe4d88
    // 0xbe4d80: r0 = Null
    //     0xbe4d80: mov             x0, NULL
    // 0xbe4d84: b               #0xbe4d94
    // 0xbe4d88: LoadField: r1 = r0->field_4f
    //     0xbe4d88: ldur            w1, [x0, #0x4f]
    // 0xbe4d8c: DecompressPointer r1
    //     0xbe4d8c: add             x1, x1, HEAP, lsl #32
    // 0xbe4d90: mov             x0, x1
    // 0xbe4d94: cmp             w0, NULL
    // 0xbe4d98: b.eq            #0xbe4da0
    // 0xbe4d9c: tbnz            w0, #4, #0xbe4df4
    // 0xbe4da0: ldur            x0, [fp, #-8]
    // 0xbe4da4: LoadField: r1 = r0->field_f
    //     0xbe4da4: ldur            w1, [x0, #0xf]
    // 0xbe4da8: DecompressPointer r1
    //     0xbe4da8: add             x1, x1, HEAP, lsl #32
    // 0xbe4dac: cmp             w1, NULL
    // 0xbe4db0: b.eq            #0xbe5294
    // 0xbe4db4: r0 = of()
    //     0xbe4db4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe4db8: LoadField: r1 = r0->field_5b
    //     0xbe4db8: ldur            w1, [x0, #0x5b]
    // 0xbe4dbc: DecompressPointer r1
    //     0xbe4dbc: add             x1, x1, HEAP, lsl #32
    // 0xbe4dc0: r0 = LoadClassIdInstr(r1)
    //     0xbe4dc0: ldur            x0, [x1, #-1]
    //     0xbe4dc4: ubfx            x0, x0, #0xc, #0x14
    // 0xbe4dc8: d0 = 0.100000
    //     0xbe4dc8: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbe4dcc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbe4dcc: sub             lr, x0, #0xffa
    //     0xbe4dd0: ldr             lr, [x21, lr, lsl #3]
    //     0xbe4dd4: blr             lr
    // 0xbe4dd8: r16 = <Color>
    //     0xbe4dd8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbe4ddc: ldr             x16, [x16, #0xf80]
    // 0xbe4de0: stp             x0, x16, [SP]
    // 0xbe4de4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe4de4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe4de8: r0 = all()
    //     0xbe4de8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe4dec: mov             x2, x0
    // 0xbe4df0: b               #0xbe4e10
    // 0xbe4df4: r16 = <Color>
    //     0xbe4df4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbe4df8: ldr             x16, [x16, #0xf80]
    // 0xbe4dfc: r30 = Instance_Color
    //     0xbe4dfc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbe4e00: stp             lr, x16, [SP]
    // 0xbe4e04: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe4e04: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe4e08: r0 = all()
    //     0xbe4e08: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe4e0c: mov             x2, x0
    // 0xbe4e10: ldur            x1, [fp, #-8]
    // 0xbe4e14: stur            x2, [fp, #-0x30]
    // 0xbe4e18: LoadField: r0 = r1->field_b
    //     0xbe4e18: ldur            w0, [x1, #0xb]
    // 0xbe4e1c: DecompressPointer r0
    //     0xbe4e1c: add             x0, x0, HEAP, lsl #32
    // 0xbe4e20: cmp             w0, NULL
    // 0xbe4e24: b.eq            #0xbe5298
    // 0xbe4e28: LoadField: r3 = r0->field_b
    //     0xbe4e28: ldur            w3, [x0, #0xb]
    // 0xbe4e2c: DecompressPointer r3
    //     0xbe4e2c: add             x3, x3, HEAP, lsl #32
    // 0xbe4e30: r0 = LoadClassIdInstr(r3)
    //     0xbe4e30: ldur            x0, [x3, #-1]
    //     0xbe4e34: ubfx            x0, x0, #0xc, #0x14
    // 0xbe4e38: ldur            x16, [fp, #-0x18]
    // 0xbe4e3c: stp             x16, x3, [SP]
    // 0xbe4e40: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe4e40: sub             lr, x0, #0xb7
    //     0xbe4e44: ldr             lr, [x21, lr, lsl #3]
    //     0xbe4e48: blr             lr
    // 0xbe4e4c: cmp             w0, NULL
    // 0xbe4e50: b.ne            #0xbe4e5c
    // 0xbe4e54: r0 = Null
    //     0xbe4e54: mov             x0, NULL
    // 0xbe4e58: b               #0xbe4e68
    // 0xbe4e5c: LoadField: r1 = r0->field_4f
    //     0xbe4e5c: ldur            w1, [x0, #0x4f]
    // 0xbe4e60: DecompressPointer r1
    //     0xbe4e60: add             x1, x1, HEAP, lsl #32
    // 0xbe4e64: mov             x0, x1
    // 0xbe4e68: cmp             w0, NULL
    // 0xbe4e6c: b.eq            #0xbe4e74
    // 0xbe4e70: tbnz            w0, #4, #0xbe4ec8
    // 0xbe4e74: ldur            x0, [fp, #-8]
    // 0xbe4e78: LoadField: r1 = r0->field_f
    //     0xbe4e78: ldur            w1, [x0, #0xf]
    // 0xbe4e7c: DecompressPointer r1
    //     0xbe4e7c: add             x1, x1, HEAP, lsl #32
    // 0xbe4e80: cmp             w1, NULL
    // 0xbe4e84: b.eq            #0xbe529c
    // 0xbe4e88: r0 = of()
    //     0xbe4e88: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe4e8c: LoadField: r1 = r0->field_5b
    //     0xbe4e8c: ldur            w1, [x0, #0x5b]
    // 0xbe4e90: DecompressPointer r1
    //     0xbe4e90: add             x1, x1, HEAP, lsl #32
    // 0xbe4e94: r0 = LoadClassIdInstr(r1)
    //     0xbe4e94: ldur            x0, [x1, #-1]
    //     0xbe4e98: ubfx            x0, x0, #0xc, #0x14
    // 0xbe4e9c: d0 = 0.100000
    //     0xbe4e9c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbe4ea0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbe4ea0: sub             lr, x0, #0xffa
    //     0xbe4ea4: ldr             lr, [x21, lr, lsl #3]
    //     0xbe4ea8: blr             lr
    // 0xbe4eac: r16 = <Color>
    //     0xbe4eac: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbe4eb0: ldr             x16, [x16, #0xf80]
    // 0xbe4eb4: stp             x0, x16, [SP]
    // 0xbe4eb8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe4eb8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe4ebc: r0 = all()
    //     0xbe4ebc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe4ec0: mov             x3, x0
    // 0xbe4ec4: b               #0xbe4f00
    // 0xbe4ec8: ldur            x0, [fp, #-8]
    // 0xbe4ecc: LoadField: r1 = r0->field_f
    //     0xbe4ecc: ldur            w1, [x0, #0xf]
    // 0xbe4ed0: DecompressPointer r1
    //     0xbe4ed0: add             x1, x1, HEAP, lsl #32
    // 0xbe4ed4: cmp             w1, NULL
    // 0xbe4ed8: b.eq            #0xbe52a0
    // 0xbe4edc: r0 = of()
    //     0xbe4edc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe4ee0: LoadField: r1 = r0->field_5b
    //     0xbe4ee0: ldur            w1, [x0, #0x5b]
    // 0xbe4ee4: DecompressPointer r1
    //     0xbe4ee4: add             x1, x1, HEAP, lsl #32
    // 0xbe4ee8: r16 = <Color>
    //     0xbe4ee8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbe4eec: ldr             x16, [x16, #0xf80]
    // 0xbe4ef0: stp             x1, x16, [SP]
    // 0xbe4ef4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe4ef4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe4ef8: r0 = all()
    //     0xbe4ef8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe4efc: mov             x3, x0
    // 0xbe4f00: ldur            x0, [fp, #-8]
    // 0xbe4f04: ldur            x2, [fp, #-0x28]
    // 0xbe4f08: ldur            x1, [fp, #-0x30]
    // 0xbe4f0c: stur            x3, [fp, #-0x38]
    // 0xbe4f10: r16 = <OutlinedBorder?>
    //     0xbe4f10: add             x16, PP, #0x52, lsl #12  ; [pp+0x52da8] TypeArguments: <OutlinedBorder?>
    //     0xbe4f14: ldr             x16, [x16, #0xda8]
    // 0xbe4f18: r30 = Instance_RoundedRectangleBorder
    //     0xbe4f18: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbe4f1c: ldr             lr, [lr, #0xd68]
    // 0xbe4f20: stp             lr, x16, [SP]
    // 0xbe4f24: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe4f24: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe4f28: r0 = all()
    //     0xbe4f28: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe4f2c: stur            x0, [fp, #-0x40]
    // 0xbe4f30: r0 = ButtonStyle()
    //     0xbe4f30: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbe4f34: mov             x1, x0
    // 0xbe4f38: ldur            x0, [fp, #-0x38]
    // 0xbe4f3c: stur            x1, [fp, #-0x48]
    // 0xbe4f40: StoreField: r1->field_b = r0
    //     0xbe4f40: stur            w0, [x1, #0xb]
    // 0xbe4f44: ldur            x0, [fp, #-0x30]
    // 0xbe4f48: StoreField: r1->field_f = r0
    //     0xbe4f48: stur            w0, [x1, #0xf]
    // 0xbe4f4c: ldur            x0, [fp, #-0x28]
    // 0xbe4f50: StoreField: r1->field_27 = r0
    //     0xbe4f50: stur            w0, [x1, #0x27]
    // 0xbe4f54: ldur            x0, [fp, #-0x40]
    // 0xbe4f58: StoreField: r1->field_43 = r0
    //     0xbe4f58: stur            w0, [x1, #0x43]
    // 0xbe4f5c: r0 = TextButtonThemeData()
    //     0xbe4f5c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbe4f60: mov             x1, x0
    // 0xbe4f64: ldur            x0, [fp, #-0x48]
    // 0xbe4f68: stur            x1, [fp, #-0x28]
    // 0xbe4f6c: StoreField: r1->field_7 = r0
    //     0xbe4f6c: stur            w0, [x1, #7]
    // 0xbe4f70: ldur            x2, [fp, #-8]
    // 0xbe4f74: LoadField: r0 = r2->field_b
    //     0xbe4f74: ldur            w0, [x2, #0xb]
    // 0xbe4f78: DecompressPointer r0
    //     0xbe4f78: add             x0, x0, HEAP, lsl #32
    // 0xbe4f7c: cmp             w0, NULL
    // 0xbe4f80: b.eq            #0xbe52a4
    // 0xbe4f84: LoadField: r3 = r0->field_b
    //     0xbe4f84: ldur            w3, [x0, #0xb]
    // 0xbe4f88: DecompressPointer r3
    //     0xbe4f88: add             x3, x3, HEAP, lsl #32
    // 0xbe4f8c: r0 = LoadClassIdInstr(r3)
    //     0xbe4f8c: ldur            x0, [x3, #-1]
    //     0xbe4f90: ubfx            x0, x0, #0xc, #0x14
    // 0xbe4f94: ldur            x16, [fp, #-0x18]
    // 0xbe4f98: stp             x16, x3, [SP]
    // 0xbe4f9c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe4f9c: sub             lr, x0, #0xb7
    //     0xbe4fa0: ldr             lr, [x21, lr, lsl #3]
    //     0xbe4fa4: blr             lr
    // 0xbe4fa8: cmp             w0, NULL
    // 0xbe4fac: b.ne            #0xbe4fb8
    // 0xbe4fb0: r0 = Null
    //     0xbe4fb0: mov             x0, NULL
    // 0xbe4fb4: b               #0xbe4fc4
    // 0xbe4fb8: LoadField: r1 = r0->field_4f
    //     0xbe4fb8: ldur            w1, [x0, #0x4f]
    // 0xbe4fbc: DecompressPointer r1
    //     0xbe4fbc: add             x1, x1, HEAP, lsl #32
    // 0xbe4fc0: mov             x0, x1
    // 0xbe4fc4: cmp             w0, NULL
    // 0xbe4fc8: b.eq            #0xbe4fd0
    // 0xbe4fcc: tbnz            w0, #4, #0xbe50d4
    // 0xbe4fd0: ldur            x1, [fp, #-8]
    // 0xbe4fd4: LoadField: r0 = r1->field_b
    //     0xbe4fd4: ldur            w0, [x1, #0xb]
    // 0xbe4fd8: DecompressPointer r0
    //     0xbe4fd8: add             x0, x0, HEAP, lsl #32
    // 0xbe4fdc: cmp             w0, NULL
    // 0xbe4fe0: b.eq            #0xbe52a8
    // 0xbe4fe4: LoadField: r2 = r0->field_b
    //     0xbe4fe4: ldur            w2, [x0, #0xb]
    // 0xbe4fe8: DecompressPointer r2
    //     0xbe4fe8: add             x2, x2, HEAP, lsl #32
    // 0xbe4fec: r0 = LoadClassIdInstr(r2)
    //     0xbe4fec: ldur            x0, [x2, #-1]
    //     0xbe4ff0: ubfx            x0, x0, #0xc, #0x14
    // 0xbe4ff4: ldur            x16, [fp, #-0x18]
    // 0xbe4ff8: stp             x16, x2, [SP]
    // 0xbe4ffc: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe4ffc: sub             lr, x0, #0xb7
    //     0xbe5000: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5004: blr             lr
    // 0xbe5008: cmp             w0, NULL
    // 0xbe500c: b.ne            #0xbe5018
    // 0xbe5010: r0 = Null
    //     0xbe5010: mov             x0, NULL
    // 0xbe5014: b               #0xbe5048
    // 0xbe5018: LoadField: r1 = r0->field_f
    //     0xbe5018: ldur            w1, [x0, #0xf]
    // 0xbe501c: DecompressPointer r1
    //     0xbe501c: add             x1, x1, HEAP, lsl #32
    // 0xbe5020: cmp             w1, NULL
    // 0xbe5024: b.ne            #0xbe5030
    // 0xbe5028: r0 = Null
    //     0xbe5028: mov             x0, NULL
    // 0xbe502c: b               #0xbe5048
    // 0xbe5030: r0 = LoadClassIdInstr(r1)
    //     0xbe5030: ldur            x0, [x1, #-1]
    //     0xbe5034: ubfx            x0, x0, #0xc, #0x14
    // 0xbe5038: str             x1, [SP]
    // 0xbe503c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbe503c: sub             lr, x0, #1, lsl #12
    //     0xbe5040: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5044: blr             lr
    // 0xbe5048: cmp             w0, NULL
    // 0xbe504c: b.ne            #0xbe5054
    // 0xbe5050: r0 = ""
    //     0xbe5050: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe5054: ldur            x1, [fp, #-8]
    // 0xbe5058: stur            x0, [fp, #-0x30]
    // 0xbe505c: LoadField: r2 = r1->field_f
    //     0xbe505c: ldur            w2, [x1, #0xf]
    // 0xbe5060: DecompressPointer r2
    //     0xbe5060: add             x2, x2, HEAP, lsl #32
    // 0xbe5064: cmp             w2, NULL
    // 0xbe5068: b.eq            #0xbe52ac
    // 0xbe506c: mov             x1, x2
    // 0xbe5070: r0 = of()
    //     0xbe5070: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe5074: LoadField: r1 = r0->field_87
    //     0xbe5074: ldur            w1, [x0, #0x87]
    // 0xbe5078: DecompressPointer r1
    //     0xbe5078: add             x1, x1, HEAP, lsl #32
    // 0xbe507c: LoadField: r0 = r1->field_7
    //     0xbe507c: ldur            w0, [x1, #7]
    // 0xbe5080: DecompressPointer r0
    //     0xbe5080: add             x0, x0, HEAP, lsl #32
    // 0xbe5084: stur            x0, [fp, #-0x38]
    // 0xbe5088: r1 = Instance_Color
    //     0xbe5088: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe508c: d0 = 0.400000
    //     0xbe508c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbe5090: r0 = withOpacity()
    //     0xbe5090: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbe5094: r16 = 14.000000
    //     0xbe5094: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbe5098: ldr             x16, [x16, #0x1d8]
    // 0xbe509c: stp             x0, x16, [SP]
    // 0xbe50a0: ldur            x1, [fp, #-0x38]
    // 0xbe50a4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe50a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe50a8: ldr             x4, [x4, #0xaa0]
    // 0xbe50ac: r0 = copyWith()
    //     0xbe50ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe50b0: stur            x0, [fp, #-0x38]
    // 0xbe50b4: r0 = Text()
    //     0xbe50b4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe50b8: mov             x1, x0
    // 0xbe50bc: ldur            x0, [fp, #-0x30]
    // 0xbe50c0: StoreField: r1->field_b = r0
    //     0xbe50c0: stur            w0, [x1, #0xb]
    // 0xbe50c4: ldur            x0, [fp, #-0x38]
    // 0xbe50c8: StoreField: r1->field_13 = r0
    //     0xbe50c8: stur            w0, [x1, #0x13]
    // 0xbe50cc: mov             x3, x1
    // 0xbe50d0: b               #0xbe51cc
    // 0xbe50d4: ldur            x1, [fp, #-8]
    // 0xbe50d8: LoadField: r0 = r1->field_b
    //     0xbe50d8: ldur            w0, [x1, #0xb]
    // 0xbe50dc: DecompressPointer r0
    //     0xbe50dc: add             x0, x0, HEAP, lsl #32
    // 0xbe50e0: cmp             w0, NULL
    // 0xbe50e4: b.eq            #0xbe52b0
    // 0xbe50e8: LoadField: r2 = r0->field_b
    //     0xbe50e8: ldur            w2, [x0, #0xb]
    // 0xbe50ec: DecompressPointer r2
    //     0xbe50ec: add             x2, x2, HEAP, lsl #32
    // 0xbe50f0: r0 = LoadClassIdInstr(r2)
    //     0xbe50f0: ldur            x0, [x2, #-1]
    //     0xbe50f4: ubfx            x0, x0, #0xc, #0x14
    // 0xbe50f8: ldur            x16, [fp, #-0x18]
    // 0xbe50fc: stp             x16, x2, [SP]
    // 0xbe5100: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe5100: sub             lr, x0, #0xb7
    //     0xbe5104: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5108: blr             lr
    // 0xbe510c: cmp             w0, NULL
    // 0xbe5110: b.ne            #0xbe511c
    // 0xbe5114: r0 = Null
    //     0xbe5114: mov             x0, NULL
    // 0xbe5118: b               #0xbe514c
    // 0xbe511c: LoadField: r1 = r0->field_f
    //     0xbe511c: ldur            w1, [x0, #0xf]
    // 0xbe5120: DecompressPointer r1
    //     0xbe5120: add             x1, x1, HEAP, lsl #32
    // 0xbe5124: cmp             w1, NULL
    // 0xbe5128: b.ne            #0xbe5134
    // 0xbe512c: r0 = Null
    //     0xbe512c: mov             x0, NULL
    // 0xbe5130: b               #0xbe514c
    // 0xbe5134: r0 = LoadClassIdInstr(r1)
    //     0xbe5134: ldur            x0, [x1, #-1]
    //     0xbe5138: ubfx            x0, x0, #0xc, #0x14
    // 0xbe513c: str             x1, [SP]
    // 0xbe5140: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbe5140: sub             lr, x0, #1, lsl #12
    //     0xbe5144: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5148: blr             lr
    // 0xbe514c: cmp             w0, NULL
    // 0xbe5150: b.ne            #0xbe515c
    // 0xbe5154: r2 = ""
    //     0xbe5154: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe5158: b               #0xbe5160
    // 0xbe515c: mov             x2, x0
    // 0xbe5160: ldur            x0, [fp, #-8]
    // 0xbe5164: stur            x2, [fp, #-0x18]
    // 0xbe5168: LoadField: r1 = r0->field_f
    //     0xbe5168: ldur            w1, [x0, #0xf]
    // 0xbe516c: DecompressPointer r1
    //     0xbe516c: add             x1, x1, HEAP, lsl #32
    // 0xbe5170: cmp             w1, NULL
    // 0xbe5174: b.eq            #0xbe52b4
    // 0xbe5178: r0 = of()
    //     0xbe5178: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe517c: LoadField: r1 = r0->field_87
    //     0xbe517c: ldur            w1, [x0, #0x87]
    // 0xbe5180: DecompressPointer r1
    //     0xbe5180: add             x1, x1, HEAP, lsl #32
    // 0xbe5184: LoadField: r0 = r1->field_7
    //     0xbe5184: ldur            w0, [x1, #7]
    // 0xbe5188: DecompressPointer r0
    //     0xbe5188: add             x0, x0, HEAP, lsl #32
    // 0xbe518c: r16 = 14.000000
    //     0xbe518c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbe5190: ldr             x16, [x16, #0x1d8]
    // 0xbe5194: r30 = Instance_Color
    //     0xbe5194: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbe5198: stp             lr, x16, [SP]
    // 0xbe519c: mov             x1, x0
    // 0xbe51a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe51a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe51a4: ldr             x4, [x4, #0xaa0]
    // 0xbe51a8: r0 = copyWith()
    //     0xbe51a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe51ac: stur            x0, [fp, #-8]
    // 0xbe51b0: r0 = Text()
    //     0xbe51b0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe51b4: mov             x1, x0
    // 0xbe51b8: ldur            x0, [fp, #-0x18]
    // 0xbe51bc: StoreField: r1->field_b = r0
    //     0xbe51bc: stur            w0, [x1, #0xb]
    // 0xbe51c0: ldur            x0, [fp, #-8]
    // 0xbe51c4: StoreField: r1->field_13 = r0
    //     0xbe51c4: stur            w0, [x1, #0x13]
    // 0xbe51c8: mov             x3, x1
    // 0xbe51cc: ldur            x0, [fp, #-0x28]
    // 0xbe51d0: ldur            x2, [fp, #-0x20]
    // 0xbe51d4: stur            x3, [fp, #-8]
    // 0xbe51d8: r1 = Function '<anonymous closure>':.
    //     0xbe51d8: add             x1, PP, #0x61, lsl #12  ; [pp+0x61ce0] AnonymousClosure: (0xbe52b8), in [package:customer_app/app/presentation/views/line/home/<USER>/product_grid_item_view.dart] _ProductGridItemState::_buildAddToBagSection (0xbe4c44)
    //     0xbe51dc: ldr             x1, [x1, #0xce0]
    // 0xbe51e0: r0 = AllocateClosure()
    //     0xbe51e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe51e4: stur            x0, [fp, #-0x18]
    // 0xbe51e8: r0 = TextButton()
    //     0xbe51e8: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbe51ec: mov             x1, x0
    // 0xbe51f0: ldur            x0, [fp, #-0x18]
    // 0xbe51f4: stur            x1, [fp, #-0x20]
    // 0xbe51f8: StoreField: r1->field_b = r0
    //     0xbe51f8: stur            w0, [x1, #0xb]
    // 0xbe51fc: r0 = false
    //     0xbe51fc: add             x0, NULL, #0x30  ; false
    // 0xbe5200: StoreField: r1->field_27 = r0
    //     0xbe5200: stur            w0, [x1, #0x27]
    // 0xbe5204: r0 = true
    //     0xbe5204: add             x0, NULL, #0x20  ; true
    // 0xbe5208: StoreField: r1->field_2f = r0
    //     0xbe5208: stur            w0, [x1, #0x2f]
    // 0xbe520c: ldur            x0, [fp, #-8]
    // 0xbe5210: StoreField: r1->field_37 = r0
    //     0xbe5210: stur            w0, [x1, #0x37]
    // 0xbe5214: r0 = TextButtonTheme()
    //     0xbe5214: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbe5218: mov             x1, x0
    // 0xbe521c: ldur            x0, [fp, #-0x28]
    // 0xbe5220: stur            x1, [fp, #-8]
    // 0xbe5224: StoreField: r1->field_f = r0
    //     0xbe5224: stur            w0, [x1, #0xf]
    // 0xbe5228: ldur            x0, [fp, #-0x20]
    // 0xbe522c: StoreField: r1->field_b = r0
    //     0xbe522c: stur            w0, [x1, #0xb]
    // 0xbe5230: r0 = SizedBox()
    //     0xbe5230: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbe5234: mov             x1, x0
    // 0xbe5238: r0 = inf
    //     0xbe5238: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbe523c: ldr             x0, [x0, #0x9f8]
    // 0xbe5240: stur            x1, [fp, #-0x18]
    // 0xbe5244: StoreField: r1->field_f = r0
    //     0xbe5244: stur            w0, [x1, #0xf]
    // 0xbe5248: ldur            x0, [fp, #-8]
    // 0xbe524c: StoreField: r1->field_b = r0
    //     0xbe524c: stur            w0, [x1, #0xb]
    // 0xbe5250: r0 = Padding()
    //     0xbe5250: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe5254: r1 = Instance_EdgeInsets
    //     0xbe5254: add             x1, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbe5258: ldr             x1, [x1, #0x770]
    // 0xbe525c: StoreField: r0->field_f = r1
    //     0xbe525c: stur            w1, [x0, #0xf]
    // 0xbe5260: ldur            x1, [fp, #-0x18]
    // 0xbe5264: StoreField: r0->field_b = r1
    //     0xbe5264: stur            w1, [x0, #0xb]
    // 0xbe5268: LeaveFrame
    //     0xbe5268: mov             SP, fp
    //     0xbe526c: ldp             fp, lr, [SP], #0x10
    // 0xbe5270: ret
    //     0xbe5270: ret             
    // 0xbe5274: r0 = Instance_SizedBox
    //     0xbe5274: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbe5278: LeaveFrame
    //     0xbe5278: mov             SP, fp
    //     0xbe527c: ldp             fp, lr, [SP], #0x10
    // 0xbe5280: ret
    //     0xbe5280: ret             
    // 0xbe5284: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe5284: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe5288: b               #0xbe4c64
    // 0xbe528c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe528c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe5290: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5290: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe5294: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5294: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe5298: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5298: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe529c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe529c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe52a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe52a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe52a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe52a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe52a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe52a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe52ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe52ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe52b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe52b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe52b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe52b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbe52b8, size: 0x384
    // 0xbe52b8: EnterFrame
    //     0xbe52b8: stp             fp, lr, [SP, #-0x10]!
    //     0xbe52bc: mov             fp, SP
    // 0xbe52c0: AllocStack(0x40)
    //     0xbe52c0: sub             SP, SP, #0x40
    // 0xbe52c4: SetupParameters()
    //     0xbe52c4: ldr             x0, [fp, #0x10]
    //     0xbe52c8: ldur            w1, [x0, #0x17]
    //     0xbe52cc: add             x1, x1, HEAP, lsl #32
    //     0xbe52d0: stur            x1, [fp, #-8]
    // 0xbe52d4: CheckStackOverflow
    //     0xbe52d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe52d8: cmp             SP, x16
    //     0xbe52dc: b.ls            #0xbe561c
    // 0xbe52e0: LoadField: r0 = r1->field_f
    //     0xbe52e0: ldur            w0, [x1, #0xf]
    // 0xbe52e4: DecompressPointer r0
    //     0xbe52e4: add             x0, x0, HEAP, lsl #32
    // 0xbe52e8: LoadField: r2 = r0->field_b
    //     0xbe52e8: ldur            w2, [x0, #0xb]
    // 0xbe52ec: DecompressPointer r2
    //     0xbe52ec: add             x2, x2, HEAP, lsl #32
    // 0xbe52f0: cmp             w2, NULL
    // 0xbe52f4: b.eq            #0xbe5624
    // 0xbe52f8: LoadField: r0 = r2->field_b
    //     0xbe52f8: ldur            w0, [x2, #0xb]
    // 0xbe52fc: DecompressPointer r0
    //     0xbe52fc: add             x0, x0, HEAP, lsl #32
    // 0xbe5300: LoadField: r2 = r1->field_13
    //     0xbe5300: ldur            w2, [x1, #0x13]
    // 0xbe5304: DecompressPointer r2
    //     0xbe5304: add             x2, x2, HEAP, lsl #32
    // 0xbe5308: r3 = LoadClassIdInstr(r0)
    //     0xbe5308: ldur            x3, [x0, #-1]
    //     0xbe530c: ubfx            x3, x3, #0xc, #0x14
    // 0xbe5310: stp             x2, x0, [SP]
    // 0xbe5314: mov             x0, x3
    // 0xbe5318: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe5318: sub             lr, x0, #0xb7
    //     0xbe531c: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5320: blr             lr
    // 0xbe5324: cmp             w0, NULL
    // 0xbe5328: b.ne            #0xbe5334
    // 0xbe532c: r0 = Null
    //     0xbe532c: mov             x0, NULL
    // 0xbe5330: b               #0xbe5340
    // 0xbe5334: LoadField: r1 = r0->field_4f
    //     0xbe5334: ldur            w1, [x0, #0x4f]
    // 0xbe5338: DecompressPointer r1
    //     0xbe5338: add             x1, x1, HEAP, lsl #32
    // 0xbe533c: mov             x0, x1
    // 0xbe5340: cmp             w0, NULL
    // 0xbe5344: b.eq            #0xbe534c
    // 0xbe5348: tbz             w0, #4, #0xbe560c
    // 0xbe534c: ldur            x1, [fp, #-8]
    // 0xbe5350: LoadField: r0 = r1->field_f
    //     0xbe5350: ldur            w0, [x1, #0xf]
    // 0xbe5354: DecompressPointer r0
    //     0xbe5354: add             x0, x0, HEAP, lsl #32
    // 0xbe5358: LoadField: r2 = r0->field_b
    //     0xbe5358: ldur            w2, [x0, #0xb]
    // 0xbe535c: DecompressPointer r2
    //     0xbe535c: add             x2, x2, HEAP, lsl #32
    // 0xbe5360: stur            x2, [fp, #-0x10]
    // 0xbe5364: cmp             w2, NULL
    // 0xbe5368: b.eq            #0xbe5628
    // 0xbe536c: LoadField: r0 = r2->field_b
    //     0xbe536c: ldur            w0, [x2, #0xb]
    // 0xbe5370: DecompressPointer r0
    //     0xbe5370: add             x0, x0, HEAP, lsl #32
    // 0xbe5374: LoadField: r3 = r1->field_13
    //     0xbe5374: ldur            w3, [x1, #0x13]
    // 0xbe5378: DecompressPointer r3
    //     0xbe5378: add             x3, x3, HEAP, lsl #32
    // 0xbe537c: r4 = LoadClassIdInstr(r0)
    //     0xbe537c: ldur            x4, [x0, #-1]
    //     0xbe5380: ubfx            x4, x4, #0xc, #0x14
    // 0xbe5384: stp             x3, x0, [SP]
    // 0xbe5388: mov             x0, x4
    // 0xbe538c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe538c: sub             lr, x0, #0xb7
    //     0xbe5390: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5394: blr             lr
    // 0xbe5398: cmp             w0, NULL
    // 0xbe539c: b.ne            #0xbe53a8
    // 0xbe53a0: r2 = Null
    //     0xbe53a0: mov             x2, NULL
    // 0xbe53a4: b               #0xbe53b4
    // 0xbe53a8: LoadField: r1 = r0->field_2b
    //     0xbe53a8: ldur            w1, [x0, #0x2b]
    // 0xbe53ac: DecompressPointer r1
    //     0xbe53ac: add             x1, x1, HEAP, lsl #32
    // 0xbe53b0: mov             x2, x1
    // 0xbe53b4: ldur            x1, [fp, #-8]
    // 0xbe53b8: stur            x2, [fp, #-0x18]
    // 0xbe53bc: LoadField: r0 = r1->field_f
    //     0xbe53bc: ldur            w0, [x1, #0xf]
    // 0xbe53c0: DecompressPointer r0
    //     0xbe53c0: add             x0, x0, HEAP, lsl #32
    // 0xbe53c4: LoadField: r3 = r0->field_b
    //     0xbe53c4: ldur            w3, [x0, #0xb]
    // 0xbe53c8: DecompressPointer r3
    //     0xbe53c8: add             x3, x3, HEAP, lsl #32
    // 0xbe53cc: cmp             w3, NULL
    // 0xbe53d0: b.eq            #0xbe562c
    // 0xbe53d4: LoadField: r0 = r3->field_b
    //     0xbe53d4: ldur            w0, [x3, #0xb]
    // 0xbe53d8: DecompressPointer r0
    //     0xbe53d8: add             x0, x0, HEAP, lsl #32
    // 0xbe53dc: LoadField: r3 = r1->field_13
    //     0xbe53dc: ldur            w3, [x1, #0x13]
    // 0xbe53e0: DecompressPointer r3
    //     0xbe53e0: add             x3, x3, HEAP, lsl #32
    // 0xbe53e4: r4 = LoadClassIdInstr(r0)
    //     0xbe53e4: ldur            x4, [x0, #-1]
    //     0xbe53e8: ubfx            x4, x4, #0xc, #0x14
    // 0xbe53ec: stp             x3, x0, [SP]
    // 0xbe53f0: mov             x0, x4
    // 0xbe53f4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe53f4: sub             lr, x0, #0xb7
    //     0xbe53f8: ldr             lr, [x21, lr, lsl #3]
    //     0xbe53fc: blr             lr
    // 0xbe5400: cmp             w0, NULL
    // 0xbe5404: b.ne            #0xbe5410
    // 0xbe5408: r2 = Null
    //     0xbe5408: mov             x2, NULL
    // 0xbe540c: b               #0xbe5434
    // 0xbe5410: LoadField: r1 = r0->field_3b
    //     0xbe5410: ldur            w1, [x0, #0x3b]
    // 0xbe5414: DecompressPointer r1
    //     0xbe5414: add             x1, x1, HEAP, lsl #32
    // 0xbe5418: cmp             w1, NULL
    // 0xbe541c: b.ne            #0xbe5428
    // 0xbe5420: r0 = Null
    //     0xbe5420: mov             x0, NULL
    // 0xbe5424: b               #0xbe5430
    // 0xbe5428: LoadField: r0 = r1->field_7
    //     0xbe5428: ldur            w0, [x1, #7]
    // 0xbe542c: DecompressPointer r0
    //     0xbe542c: add             x0, x0, HEAP, lsl #32
    // 0xbe5430: mov             x2, x0
    // 0xbe5434: ldur            x1, [fp, #-8]
    // 0xbe5438: stur            x2, [fp, #-0x20]
    // 0xbe543c: LoadField: r0 = r1->field_f
    //     0xbe543c: ldur            w0, [x1, #0xf]
    // 0xbe5440: DecompressPointer r0
    //     0xbe5440: add             x0, x0, HEAP, lsl #32
    // 0xbe5444: LoadField: r3 = r0->field_b
    //     0xbe5444: ldur            w3, [x0, #0xb]
    // 0xbe5448: DecompressPointer r3
    //     0xbe5448: add             x3, x3, HEAP, lsl #32
    // 0xbe544c: cmp             w3, NULL
    // 0xbe5450: b.eq            #0xbe5630
    // 0xbe5454: LoadField: r0 = r3->field_b
    //     0xbe5454: ldur            w0, [x3, #0xb]
    // 0xbe5458: DecompressPointer r0
    //     0xbe5458: add             x0, x0, HEAP, lsl #32
    // 0xbe545c: LoadField: r3 = r1->field_13
    //     0xbe545c: ldur            w3, [x1, #0x13]
    // 0xbe5460: DecompressPointer r3
    //     0xbe5460: add             x3, x3, HEAP, lsl #32
    // 0xbe5464: r4 = LoadClassIdInstr(r0)
    //     0xbe5464: ldur            x4, [x0, #-1]
    //     0xbe5468: ubfx            x4, x4, #0xc, #0x14
    // 0xbe546c: stp             x3, x0, [SP]
    // 0xbe5470: mov             x0, x4
    // 0xbe5474: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe5474: sub             lr, x0, #0xb7
    //     0xbe5478: ldr             lr, [x21, lr, lsl #3]
    //     0xbe547c: blr             lr
    // 0xbe5480: cmp             w0, NULL
    // 0xbe5484: b.ne            #0xbe5490
    // 0xbe5488: r2 = Null
    //     0xbe5488: mov             x2, NULL
    // 0xbe548c: b               #0xbe54b4
    // 0xbe5490: LoadField: r1 = r0->field_3b
    //     0xbe5490: ldur            w1, [x0, #0x3b]
    // 0xbe5494: DecompressPointer r1
    //     0xbe5494: add             x1, x1, HEAP, lsl #32
    // 0xbe5498: cmp             w1, NULL
    // 0xbe549c: b.ne            #0xbe54a8
    // 0xbe54a0: r0 = Null
    //     0xbe54a0: mov             x0, NULL
    // 0xbe54a4: b               #0xbe54b0
    // 0xbe54a8: LoadField: r0 = r1->field_b
    //     0xbe54a8: ldur            w0, [x1, #0xb]
    // 0xbe54ac: DecompressPointer r0
    //     0xbe54ac: add             x0, x0, HEAP, lsl #32
    // 0xbe54b0: mov             x2, x0
    // 0xbe54b4: ldur            x0, [fp, #-8]
    // 0xbe54b8: ldur            x1, [fp, #-0x10]
    // 0xbe54bc: LoadField: r3 = r1->field_3b
    //     0xbe54bc: ldur            w3, [x1, #0x3b]
    // 0xbe54c0: DecompressPointer r3
    //     0xbe54c0: add             x3, x3, HEAP, lsl #32
    // 0xbe54c4: ldur            x16, [fp, #-0x18]
    // 0xbe54c8: stp             x16, x3, [SP, #0x10]
    // 0xbe54cc: ldur            x16, [fp, #-0x20]
    // 0xbe54d0: stp             x2, x16, [SP]
    // 0xbe54d4: r4 = 0
    //     0xbe54d4: movz            x4, #0
    // 0xbe54d8: ldr             x0, [SP, #0x18]
    // 0xbe54dc: r16 = UnlinkedCall_0x613b5c
    //     0xbe54dc: add             x16, PP, #0x61, lsl #12  ; [pp+0x61ce8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe54e0: add             x16, x16, #0xce8
    // 0xbe54e4: ldp             x5, lr, [x16]
    // 0xbe54e8: blr             lr
    // 0xbe54ec: ldur            x1, [fp, #-8]
    // 0xbe54f0: LoadField: r0 = r1->field_f
    //     0xbe54f0: ldur            w0, [x1, #0xf]
    // 0xbe54f4: DecompressPointer r0
    //     0xbe54f4: add             x0, x0, HEAP, lsl #32
    // 0xbe54f8: LoadField: r2 = r0->field_b
    //     0xbe54f8: ldur            w2, [x0, #0xb]
    // 0xbe54fc: DecompressPointer r2
    //     0xbe54fc: add             x2, x2, HEAP, lsl #32
    // 0xbe5500: stur            x2, [fp, #-0x10]
    // 0xbe5504: cmp             w2, NULL
    // 0xbe5508: b.eq            #0xbe5634
    // 0xbe550c: LoadField: r0 = r2->field_b
    //     0xbe550c: ldur            w0, [x2, #0xb]
    // 0xbe5510: DecompressPointer r0
    //     0xbe5510: add             x0, x0, HEAP, lsl #32
    // 0xbe5514: LoadField: r3 = r1->field_13
    //     0xbe5514: ldur            w3, [x1, #0x13]
    // 0xbe5518: DecompressPointer r3
    //     0xbe5518: add             x3, x3, HEAP, lsl #32
    // 0xbe551c: r4 = LoadClassIdInstr(r0)
    //     0xbe551c: ldur            x4, [x0, #-1]
    //     0xbe5520: ubfx            x4, x4, #0xc, #0x14
    // 0xbe5524: stp             x3, x0, [SP]
    // 0xbe5528: mov             x0, x4
    // 0xbe552c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe552c: sub             lr, x0, #0xb7
    //     0xbe5530: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5534: blr             lr
    // 0xbe5538: cmp             w0, NULL
    // 0xbe553c: b.ne            #0xbe554c
    // 0xbe5540: r0 = Entity()
    //     0xbe5540: bl              #0x9118a4  ; AllocateEntityStub -> Entity (size=0xc0)
    // 0xbe5544: mov             x1, x0
    // 0xbe5548: b               #0xbe5550
    // 0xbe554c: mov             x1, x0
    // 0xbe5550: ldur            x0, [fp, #-8]
    // 0xbe5554: stur            x1, [fp, #-0x20]
    // 0xbe5558: LoadField: r2 = r0->field_f
    //     0xbe5558: ldur            w2, [x0, #0xf]
    // 0xbe555c: DecompressPointer r2
    //     0xbe555c: add             x2, x2, HEAP, lsl #32
    // 0xbe5560: LoadField: r3 = r2->field_b
    //     0xbe5560: ldur            w3, [x2, #0xb]
    // 0xbe5564: DecompressPointer r3
    //     0xbe5564: add             x3, x3, HEAP, lsl #32
    // 0xbe5568: cmp             w3, NULL
    // 0xbe556c: b.eq            #0xbe5638
    // 0xbe5570: LoadField: r2 = r3->field_2f
    //     0xbe5570: ldur            w2, [x3, #0x2f]
    // 0xbe5574: DecompressPointer r2
    //     0xbe5574: add             x2, x2, HEAP, lsl #32
    // 0xbe5578: cmp             w2, NULL
    // 0xbe557c: b.ne            #0xbe5584
    // 0xbe5580: r2 = ""
    //     0xbe5580: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe5584: stur            x2, [fp, #-0x18]
    // 0xbe5588: LoadField: r4 = r3->field_b
    //     0xbe5588: ldur            w4, [x3, #0xb]
    // 0xbe558c: DecompressPointer r4
    //     0xbe558c: add             x4, x4, HEAP, lsl #32
    // 0xbe5590: LoadField: r3 = r0->field_13
    //     0xbe5590: ldur            w3, [x0, #0x13]
    // 0xbe5594: DecompressPointer r3
    //     0xbe5594: add             x3, x3, HEAP, lsl #32
    // 0xbe5598: r0 = LoadClassIdInstr(r4)
    //     0xbe5598: ldur            x0, [x4, #-1]
    //     0xbe559c: ubfx            x0, x0, #0xc, #0x14
    // 0xbe55a0: stp             x3, x4, [SP]
    // 0xbe55a4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe55a4: sub             lr, x0, #0xb7
    //     0xbe55a8: ldr             lr, [x21, lr, lsl #3]
    //     0xbe55ac: blr             lr
    // 0xbe55b0: cmp             w0, NULL
    // 0xbe55b4: b.ne            #0xbe55c0
    // 0xbe55b8: r0 = Null
    //     0xbe55b8: mov             x0, NULL
    // 0xbe55bc: b               #0xbe55cc
    // 0xbe55c0: LoadField: r1 = r0->field_b3
    //     0xbe55c0: ldur            w1, [x0, #0xb3]
    // 0xbe55c4: DecompressPointer r1
    //     0xbe55c4: add             x1, x1, HEAP, lsl #32
    // 0xbe55c8: mov             x0, x1
    // 0xbe55cc: cmp             w0, NULL
    // 0xbe55d0: b.ne            #0xbe55dc
    // 0xbe55d4: r1 = ""
    //     0xbe55d4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe55d8: b               #0xbe55e0
    // 0xbe55dc: mov             x1, x0
    // 0xbe55e0: ldur            x0, [fp, #-0x10]
    // 0xbe55e4: LoadField: r2 = r0->field_47
    //     0xbe55e4: ldur            w2, [x0, #0x47]
    // 0xbe55e8: DecompressPointer r2
    //     0xbe55e8: add             x2, x2, HEAP, lsl #32
    // 0xbe55ec: ldur            x16, [fp, #-0x20]
    // 0xbe55f0: stp             x16, x2, [SP, #0x10]
    // 0xbe55f4: ldur            x16, [fp, #-0x18]
    // 0xbe55f8: stp             x1, x16, [SP]
    // 0xbe55fc: mov             x0, x2
    // 0xbe5600: ClosureCall
    //     0xbe5600: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xbe5604: ldur            x2, [x0, #0x1f]
    //     0xbe5608: blr             x2
    // 0xbe560c: r0 = Null
    //     0xbe560c: mov             x0, NULL
    // 0xbe5610: LeaveFrame
    //     0xbe5610: mov             SP, fp
    //     0xbe5614: ldp             fp, lr, [SP], #0x10
    // 0xbe5618: ret
    //     0xbe5618: ret             
    // 0xbe561c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe561c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe5620: b               #0xbe52e0
    // 0xbe5624: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5624: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe5628: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5628: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe562c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe562c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe5630: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5630: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe5634: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5634: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe5638: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5638: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildPriceSection(/* No info */) {
    // ** addr: 0xbe563c, size: 0x5f8
    // 0xbe563c: EnterFrame
    //     0xbe563c: stp             fp, lr, [SP, #-0x10]!
    //     0xbe5640: mov             fp, SP
    // 0xbe5644: AllocStack(0x48)
    //     0xbe5644: sub             SP, SP, #0x48
    // 0xbe5648: SetupParameters(_ProductGridItemState this /* r1 => r3, fp-0x10 */)
    //     0xbe5648: mov             x3, x1
    //     0xbe564c: stur            x1, [fp, #-0x10]
    // 0xbe5650: CheckStackOverflow
    //     0xbe5650: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe5654: cmp             SP, x16
    //     0xbe5658: b.ls            #0xbe5c08
    // 0xbe565c: LoadField: r0 = r3->field_b
    //     0xbe565c: ldur            w0, [x3, #0xb]
    // 0xbe5660: DecompressPointer r0
    //     0xbe5660: add             x0, x0, HEAP, lsl #32
    // 0xbe5664: cmp             w0, NULL
    // 0xbe5668: b.eq            #0xbe5c10
    // 0xbe566c: LoadField: r4 = r0->field_b
    //     0xbe566c: ldur            w4, [x0, #0xb]
    // 0xbe5670: DecompressPointer r4
    //     0xbe5670: add             x4, x4, HEAP, lsl #32
    // 0xbe5674: r0 = BoxInt64Instr(r2)
    //     0xbe5674: sbfiz           x0, x2, #1, #0x1f
    //     0xbe5678: cmp             x2, x0, asr #1
    //     0xbe567c: b.eq            #0xbe5688
    //     0xbe5680: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe5684: stur            x2, [x0, #7]
    // 0xbe5688: mov             x1, x0
    // 0xbe568c: stur            x1, [fp, #-8]
    // 0xbe5690: r0 = LoadClassIdInstr(r4)
    //     0xbe5690: ldur            x0, [x4, #-1]
    //     0xbe5694: ubfx            x0, x0, #0xc, #0x14
    // 0xbe5698: stp             x1, x4, [SP]
    // 0xbe569c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe569c: sub             lr, x0, #0xb7
    //     0xbe56a0: ldr             lr, [x21, lr, lsl #3]
    //     0xbe56a4: blr             lr
    // 0xbe56a8: cmp             w0, NULL
    // 0xbe56ac: b.ne            #0xbe56b8
    // 0xbe56b0: r0 = Null
    //     0xbe56b0: mov             x0, NULL
    // 0xbe56b4: b               #0xbe56e8
    // 0xbe56b8: LoadField: r1 = r0->field_43
    //     0xbe56b8: ldur            w1, [x0, #0x43]
    // 0xbe56bc: DecompressPointer r1
    //     0xbe56bc: add             x1, x1, HEAP, lsl #32
    // 0xbe56c0: cmp             w1, NULL
    // 0xbe56c4: b.ne            #0xbe56d0
    // 0xbe56c8: r0 = Null
    //     0xbe56c8: mov             x0, NULL
    // 0xbe56cc: b               #0xbe56e8
    // 0xbe56d0: LoadField: r0 = r1->field_7
    //     0xbe56d0: ldur            w0, [x1, #7]
    // 0xbe56d4: cbnz            w0, #0xbe56e0
    // 0xbe56d8: r1 = false
    //     0xbe56d8: add             x1, NULL, #0x30  ; false
    // 0xbe56dc: b               #0xbe56e4
    // 0xbe56e0: r1 = true
    //     0xbe56e0: add             x1, NULL, #0x20  ; true
    // 0xbe56e4: mov             x0, x1
    // 0xbe56e8: cmp             w0, NULL
    // 0xbe56ec: b.eq            #0xbe56f4
    // 0xbe56f0: tbz             w0, #4, #0xbe5704
    // 0xbe56f4: r0 = Instance_SizedBox
    //     0xbe56f4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbe56f8: LeaveFrame
    //     0xbe56f8: mov             SP, fp
    //     0xbe56fc: ldp             fp, lr, [SP], #0x10
    // 0xbe5700: ret
    //     0xbe5700: ret             
    // 0xbe5704: ldur            x1, [fp, #-0x10]
    // 0xbe5708: LoadField: r0 = r1->field_b
    //     0xbe5708: ldur            w0, [x1, #0xb]
    // 0xbe570c: DecompressPointer r0
    //     0xbe570c: add             x0, x0, HEAP, lsl #32
    // 0xbe5710: cmp             w0, NULL
    // 0xbe5714: b.eq            #0xbe5c14
    // 0xbe5718: LoadField: r2 = r0->field_b
    //     0xbe5718: ldur            w2, [x0, #0xb]
    // 0xbe571c: DecompressPointer r2
    //     0xbe571c: add             x2, x2, HEAP, lsl #32
    // 0xbe5720: r0 = LoadClassIdInstr(r2)
    //     0xbe5720: ldur            x0, [x2, #-1]
    //     0xbe5724: ubfx            x0, x0, #0xc, #0x14
    // 0xbe5728: ldur            x16, [fp, #-8]
    // 0xbe572c: stp             x16, x2, [SP]
    // 0xbe5730: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe5730: sub             lr, x0, #0xb7
    //     0xbe5734: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5738: blr             lr
    // 0xbe573c: cmp             w0, NULL
    // 0xbe5740: b.ne            #0xbe574c
    // 0xbe5744: r3 = Null
    //     0xbe5744: mov             x3, NULL
    // 0xbe5748: b               #0xbe5758
    // 0xbe574c: LoadField: r1 = r0->field_43
    //     0xbe574c: ldur            w1, [x0, #0x43]
    // 0xbe5750: DecompressPointer r1
    //     0xbe5750: add             x1, x1, HEAP, lsl #32
    // 0xbe5754: mov             x3, x1
    // 0xbe5758: ldur            x0, [fp, #-0x10]
    // 0xbe575c: stur            x3, [fp, #-0x18]
    // 0xbe5760: r1 = Null
    //     0xbe5760: mov             x1, NULL
    // 0xbe5764: r2 = 4
    //     0xbe5764: movz            x2, #0x4
    // 0xbe5768: r0 = AllocateArray()
    //     0xbe5768: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe576c: mov             x1, x0
    // 0xbe5770: ldur            x0, [fp, #-0x18]
    // 0xbe5774: StoreField: r1->field_f = r0
    //     0xbe5774: stur            w0, [x1, #0xf]
    // 0xbe5778: r16 = "  "
    //     0xbe5778: ldr             x16, [PP, #0xc58]  ; [pp+0xc58] "  "
    // 0xbe577c: StoreField: r1->field_13 = r16
    //     0xbe577c: stur            w16, [x1, #0x13]
    // 0xbe5780: str             x1, [SP]
    // 0xbe5784: r0 = _interpolate()
    //     0xbe5784: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbe5788: mov             x2, x0
    // 0xbe578c: ldur            x0, [fp, #-0x10]
    // 0xbe5790: stur            x2, [fp, #-0x18]
    // 0xbe5794: LoadField: r1 = r0->field_f
    //     0xbe5794: ldur            w1, [x0, #0xf]
    // 0xbe5798: DecompressPointer r1
    //     0xbe5798: add             x1, x1, HEAP, lsl #32
    // 0xbe579c: cmp             w1, NULL
    // 0xbe57a0: b.eq            #0xbe5c18
    // 0xbe57a4: r0 = of()
    //     0xbe57a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe57a8: LoadField: r1 = r0->field_87
    //     0xbe57a8: ldur            w1, [x0, #0x87]
    // 0xbe57ac: DecompressPointer r1
    //     0xbe57ac: add             x1, x1, HEAP, lsl #32
    // 0xbe57b0: LoadField: r0 = r1->field_7
    //     0xbe57b0: ldur            w0, [x1, #7]
    // 0xbe57b4: DecompressPointer r0
    //     0xbe57b4: add             x0, x0, HEAP, lsl #32
    // 0xbe57b8: r16 = 14.000000
    //     0xbe57b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbe57bc: ldr             x16, [x16, #0x1d8]
    // 0xbe57c0: r30 = Instance_Color
    //     0xbe57c0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe57c4: stp             lr, x16, [SP]
    // 0xbe57c8: mov             x1, x0
    // 0xbe57cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe57cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe57d0: ldr             x4, [x4, #0xaa0]
    // 0xbe57d4: r0 = copyWith()
    //     0xbe57d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe57d8: stur            x0, [fp, #-0x20]
    // 0xbe57dc: r0 = Text()
    //     0xbe57dc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe57e0: mov             x1, x0
    // 0xbe57e4: ldur            x0, [fp, #-0x18]
    // 0xbe57e8: stur            x1, [fp, #-0x28]
    // 0xbe57ec: StoreField: r1->field_b = r0
    //     0xbe57ec: stur            w0, [x1, #0xb]
    // 0xbe57f0: ldur            x0, [fp, #-0x20]
    // 0xbe57f4: StoreField: r1->field_13 = r0
    //     0xbe57f4: stur            w0, [x1, #0x13]
    // 0xbe57f8: r0 = WidgetSpan()
    //     0xbe57f8: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xbe57fc: mov             x1, x0
    // 0xbe5800: ldur            x0, [fp, #-0x28]
    // 0xbe5804: stur            x1, [fp, #-0x18]
    // 0xbe5808: StoreField: r1->field_13 = r0
    //     0xbe5808: stur            w0, [x1, #0x13]
    // 0xbe580c: r0 = Instance_PlaceholderAlignment
    //     0xbe580c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] Obj!PlaceholderAlignment@d76401
    //     0xbe5810: ldr             x0, [x0, #0xa0]
    // 0xbe5814: StoreField: r1->field_b = r0
    //     0xbe5814: stur            w0, [x1, #0xb]
    // 0xbe5818: ldur            x2, [fp, #-0x10]
    // 0xbe581c: LoadField: r0 = r2->field_b
    //     0xbe581c: ldur            w0, [x2, #0xb]
    // 0xbe5820: DecompressPointer r0
    //     0xbe5820: add             x0, x0, HEAP, lsl #32
    // 0xbe5824: cmp             w0, NULL
    // 0xbe5828: b.eq            #0xbe5c1c
    // 0xbe582c: LoadField: r3 = r0->field_b
    //     0xbe582c: ldur            w3, [x0, #0xb]
    // 0xbe5830: DecompressPointer r3
    //     0xbe5830: add             x3, x3, HEAP, lsl #32
    // 0xbe5834: r0 = LoadClassIdInstr(r3)
    //     0xbe5834: ldur            x0, [x3, #-1]
    //     0xbe5838: ubfx            x0, x0, #0xc, #0x14
    // 0xbe583c: ldur            x16, [fp, #-8]
    // 0xbe5840: stp             x16, x3, [SP]
    // 0xbe5844: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe5844: sub             lr, x0, #0xb7
    //     0xbe5848: ldr             lr, [x21, lr, lsl #3]
    //     0xbe584c: blr             lr
    // 0xbe5850: cmp             w0, NULL
    // 0xbe5854: b.ne            #0xbe5860
    // 0xbe5858: r1 = Null
    //     0xbe5858: mov             x1, NULL
    // 0xbe585c: b               #0xbe5868
    // 0xbe5860: LoadField: r1 = r0->field_4b
    //     0xbe5860: ldur            w1, [x0, #0x4b]
    // 0xbe5864: DecompressPointer r1
    //     0xbe5864: add             x1, x1, HEAP, lsl #32
    // 0xbe5868: ldur            x0, [fp, #-0x10]
    // 0xbe586c: str             x1, [SP]
    // 0xbe5870: r0 = _interpolateSingle()
    //     0xbe5870: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbe5874: mov             x2, x0
    // 0xbe5878: ldur            x0, [fp, #-0x10]
    // 0xbe587c: stur            x2, [fp, #-0x20]
    // 0xbe5880: LoadField: r1 = r0->field_f
    //     0xbe5880: ldur            w1, [x0, #0xf]
    // 0xbe5884: DecompressPointer r1
    //     0xbe5884: add             x1, x1, HEAP, lsl #32
    // 0xbe5888: cmp             w1, NULL
    // 0xbe588c: b.eq            #0xbe5c20
    // 0xbe5890: r0 = of()
    //     0xbe5890: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe5894: LoadField: r1 = r0->field_87
    //     0xbe5894: ldur            w1, [x0, #0x87]
    // 0xbe5898: DecompressPointer r1
    //     0xbe5898: add             x1, x1, HEAP, lsl #32
    // 0xbe589c: LoadField: r0 = r1->field_2b
    //     0xbe589c: ldur            w0, [x1, #0x2b]
    // 0xbe58a0: DecompressPointer r0
    //     0xbe58a0: add             x0, x0, HEAP, lsl #32
    // 0xbe58a4: stur            x0, [fp, #-0x28]
    // 0xbe58a8: r1 = Instance_Color
    //     0xbe58a8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe58ac: d0 = 0.400000
    //     0xbe58ac: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbe58b0: r0 = withOpacity()
    //     0xbe58b0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbe58b4: r16 = 12.000000
    //     0xbe58b4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe58b8: ldr             x16, [x16, #0x9e8]
    // 0xbe58bc: stp             x16, x0, [SP, #8]
    // 0xbe58c0: r16 = Instance_TextDecoration
    //     0xbe58c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xbe58c4: ldr             x16, [x16, #0xe30]
    // 0xbe58c8: str             x16, [SP]
    // 0xbe58cc: ldur            x1, [fp, #-0x28]
    // 0xbe58d0: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, decoration, 0x3, fontSize, 0x2, null]
    //     0xbe58d0: add             x4, PP, #0x40, lsl #12  ; [pp+0x407c8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "decoration", 0x3, "fontSize", 0x2, Null]
    //     0xbe58d4: ldr             x4, [x4, #0x7c8]
    // 0xbe58d8: r0 = copyWith()
    //     0xbe58d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe58dc: stur            x0, [fp, #-0x28]
    // 0xbe58e0: r0 = TextSpan()
    //     0xbe58e0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbe58e4: mov             x1, x0
    // 0xbe58e8: ldur            x0, [fp, #-0x20]
    // 0xbe58ec: stur            x1, [fp, #-0x30]
    // 0xbe58f0: StoreField: r1->field_b = r0
    //     0xbe58f0: stur            w0, [x1, #0xb]
    // 0xbe58f4: r2 = Instance__DeferringMouseCursor
    //     0xbe58f4: ldr             x2, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbe58f8: ArrayStore: r1[0] = r2  ; List_4
    //     0xbe58f8: stur            w2, [x1, #0x17]
    // 0xbe58fc: ldur            x0, [fp, #-0x28]
    // 0xbe5900: StoreField: r1->field_7 = r0
    //     0xbe5900: stur            w0, [x1, #7]
    // 0xbe5904: ldur            x3, [fp, #-0x10]
    // 0xbe5908: LoadField: r0 = r3->field_b
    //     0xbe5908: ldur            w0, [x3, #0xb]
    // 0xbe590c: DecompressPointer r0
    //     0xbe590c: add             x0, x0, HEAP, lsl #32
    // 0xbe5910: cmp             w0, NULL
    // 0xbe5914: b.eq            #0xbe5c24
    // 0xbe5918: LoadField: r4 = r0->field_b
    //     0xbe5918: ldur            w4, [x0, #0xb]
    // 0xbe591c: DecompressPointer r4
    //     0xbe591c: add             x4, x4, HEAP, lsl #32
    // 0xbe5920: r0 = LoadClassIdInstr(r4)
    //     0xbe5920: ldur            x0, [x4, #-1]
    //     0xbe5924: ubfx            x0, x0, #0xc, #0x14
    // 0xbe5928: ldur            x16, [fp, #-8]
    // 0xbe592c: stp             x16, x4, [SP]
    // 0xbe5930: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe5930: sub             lr, x0, #0xb7
    //     0xbe5934: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5938: blr             lr
    // 0xbe593c: cmp             w0, NULL
    // 0xbe5940: b.ne            #0xbe594c
    // 0xbe5944: r0 = Null
    //     0xbe5944: mov             x0, NULL
    // 0xbe5948: b               #0xbe5958
    // 0xbe594c: LoadField: r1 = r0->field_63
    //     0xbe594c: ldur            w1, [x0, #0x63]
    // 0xbe5950: DecompressPointer r1
    //     0xbe5950: add             x1, x1, HEAP, lsl #32
    // 0xbe5954: mov             x0, x1
    // 0xbe5958: r1 = 60
    //     0xbe5958: movz            x1, #0x3c
    // 0xbe595c: branchIfSmi(r0, 0xbe5968)
    //     0xbe595c: tbz             w0, #0, #0xbe5968
    // 0xbe5960: r1 = LoadClassIdInstr(r0)
    //     0xbe5960: ldur            x1, [x0, #-1]
    //     0xbe5964: ubfx            x1, x1, #0xc, #0x14
    // 0xbe5968: r16 = 0.000000
    //     0xbe5968: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xbe596c: stp             x16, x0, [SP]
    // 0xbe5970: mov             x0, x1
    // 0xbe5974: mov             lr, x0
    // 0xbe5978: ldr             lr, [x21, lr, lsl #3]
    // 0xbe597c: blr             lr
    // 0xbe5980: tbz             w0, #4, #0xbe5abc
    // 0xbe5984: ldur            x1, [fp, #-0x10]
    // 0xbe5988: LoadField: r0 = r1->field_b
    //     0xbe5988: ldur            w0, [x1, #0xb]
    // 0xbe598c: DecompressPointer r0
    //     0xbe598c: add             x0, x0, HEAP, lsl #32
    // 0xbe5990: cmp             w0, NULL
    // 0xbe5994: b.eq            #0xbe5c28
    // 0xbe5998: LoadField: r2 = r0->field_b
    //     0xbe5998: ldur            w2, [x0, #0xb]
    // 0xbe599c: DecompressPointer r2
    //     0xbe599c: add             x2, x2, HEAP, lsl #32
    // 0xbe59a0: r0 = LoadClassIdInstr(r2)
    //     0xbe59a0: ldur            x0, [x2, #-1]
    //     0xbe59a4: ubfx            x0, x0, #0xc, #0x14
    // 0xbe59a8: ldur            x16, [fp, #-8]
    // 0xbe59ac: stp             x16, x2, [SP]
    // 0xbe59b0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe59b0: sub             lr, x0, #0xb7
    //     0xbe59b4: ldr             lr, [x21, lr, lsl #3]
    //     0xbe59b8: blr             lr
    // 0xbe59bc: cmp             w0, NULL
    // 0xbe59c0: b.eq            #0xbe5abc
    // 0xbe59c4: LoadField: r1 = r0->field_63
    //     0xbe59c4: ldur            w1, [x0, #0x63]
    // 0xbe59c8: DecompressPointer r1
    //     0xbe59c8: add             x1, x1, HEAP, lsl #32
    // 0xbe59cc: cmp             w1, NULL
    // 0xbe59d0: b.eq            #0xbe5abc
    // 0xbe59d4: ldur            x0, [fp, #-0x10]
    // 0xbe59d8: r1 = Null
    //     0xbe59d8: mov             x1, NULL
    // 0xbe59dc: r2 = 6
    //     0xbe59dc: movz            x2, #0x6
    // 0xbe59e0: r0 = AllocateArray()
    //     0xbe59e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe59e4: mov             x1, x0
    // 0xbe59e8: stur            x1, [fp, #-0x20]
    // 0xbe59ec: r16 = " | "
    //     0xbe59ec: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d80] " | "
    //     0xbe59f0: ldr             x16, [x16, #0xd80]
    // 0xbe59f4: StoreField: r1->field_f = r16
    //     0xbe59f4: stur            w16, [x1, #0xf]
    // 0xbe59f8: ldur            x2, [fp, #-0x10]
    // 0xbe59fc: LoadField: r0 = r2->field_b
    //     0xbe59fc: ldur            w0, [x2, #0xb]
    // 0xbe5a00: DecompressPointer r0
    //     0xbe5a00: add             x0, x0, HEAP, lsl #32
    // 0xbe5a04: cmp             w0, NULL
    // 0xbe5a08: b.eq            #0xbe5c2c
    // 0xbe5a0c: LoadField: r3 = r0->field_b
    //     0xbe5a0c: ldur            w3, [x0, #0xb]
    // 0xbe5a10: DecompressPointer r3
    //     0xbe5a10: add             x3, x3, HEAP, lsl #32
    // 0xbe5a14: r0 = LoadClassIdInstr(r3)
    //     0xbe5a14: ldur            x0, [x3, #-1]
    //     0xbe5a18: ubfx            x0, x0, #0xc, #0x14
    // 0xbe5a1c: ldur            x16, [fp, #-8]
    // 0xbe5a20: stp             x16, x3, [SP]
    // 0xbe5a24: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe5a24: sub             lr, x0, #0xb7
    //     0xbe5a28: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5a2c: blr             lr
    // 0xbe5a30: cmp             w0, NULL
    // 0xbe5a34: b.ne            #0xbe5a40
    // 0xbe5a38: r0 = Null
    //     0xbe5a38: mov             x0, NULL
    // 0xbe5a3c: b               #0xbe5a74
    // 0xbe5a40: LoadField: r1 = r0->field_63
    //     0xbe5a40: ldur            w1, [x0, #0x63]
    // 0xbe5a44: DecompressPointer r1
    //     0xbe5a44: add             x1, x1, HEAP, lsl #32
    // 0xbe5a48: cmp             w1, NULL
    // 0xbe5a4c: b.ne            #0xbe5a58
    // 0xbe5a50: r0 = Null
    //     0xbe5a50: mov             x0, NULL
    // 0xbe5a54: b               #0xbe5a74
    // 0xbe5a58: stp             xzr, x1, [SP]
    // 0xbe5a5c: r4 = 0
    //     0xbe5a5c: movz            x4, #0
    // 0xbe5a60: ldr             x0, [SP, #8]
    // 0xbe5a64: r16 = UnlinkedCall_0x613b5c
    //     0xbe5a64: add             x16, PP, #0x61, lsl #12  ; [pp+0x61cf8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe5a68: add             x16, x16, #0xcf8
    // 0xbe5a6c: ldp             x5, lr, [x16]
    // 0xbe5a70: blr             lr
    // 0xbe5a74: ldur            x2, [fp, #-0x20]
    // 0xbe5a78: mov             x1, x2
    // 0xbe5a7c: ArrayStore: r1[1] = r0  ; List_4
    //     0xbe5a7c: add             x25, x1, #0x13
    //     0xbe5a80: str             w0, [x25]
    //     0xbe5a84: tbz             w0, #0, #0xbe5aa0
    //     0xbe5a88: ldurb           w16, [x1, #-1]
    //     0xbe5a8c: ldurb           w17, [x0, #-1]
    //     0xbe5a90: and             x16, x17, x16, lsr #2
    //     0xbe5a94: tst             x16, HEAP, lsr #32
    //     0xbe5a98: b.eq            #0xbe5aa0
    //     0xbe5a9c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe5aa0: r16 = "% OFF"
    //     0xbe5aa0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d98] "% OFF"
    //     0xbe5aa4: ldr             x16, [x16, #0xd98]
    // 0xbe5aa8: ArrayStore: r2[0] = r16  ; List_4
    //     0xbe5aa8: stur            w16, [x2, #0x17]
    // 0xbe5aac: str             x2, [SP]
    // 0xbe5ab0: r0 = _interpolate()
    //     0xbe5ab0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbe5ab4: mov             x4, x0
    // 0xbe5ab8: b               #0xbe5ac0
    // 0xbe5abc: r4 = ""
    //     0xbe5abc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe5ac0: ldur            x0, [fp, #-0x10]
    // 0xbe5ac4: ldur            x3, [fp, #-0x18]
    // 0xbe5ac8: ldur            x2, [fp, #-0x30]
    // 0xbe5acc: stur            x4, [fp, #-8]
    // 0xbe5ad0: LoadField: r1 = r0->field_f
    //     0xbe5ad0: ldur            w1, [x0, #0xf]
    // 0xbe5ad4: DecompressPointer r1
    //     0xbe5ad4: add             x1, x1, HEAP, lsl #32
    // 0xbe5ad8: cmp             w1, NULL
    // 0xbe5adc: b.eq            #0xbe5c30
    // 0xbe5ae0: r0 = of()
    //     0xbe5ae0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe5ae4: LoadField: r1 = r0->field_87
    //     0xbe5ae4: ldur            w1, [x0, #0x87]
    // 0xbe5ae8: DecompressPointer r1
    //     0xbe5ae8: add             x1, x1, HEAP, lsl #32
    // 0xbe5aec: LoadField: r0 = r1->field_2b
    //     0xbe5aec: ldur            w0, [x1, #0x2b]
    // 0xbe5af0: DecompressPointer r0
    //     0xbe5af0: add             x0, x0, HEAP, lsl #32
    // 0xbe5af4: r16 = 12.000000
    //     0xbe5af4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe5af8: ldr             x16, [x16, #0x9e8]
    // 0xbe5afc: r30 = Instance_Color
    //     0xbe5afc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbe5b00: ldr             lr, [lr, #0x858]
    // 0xbe5b04: stp             lr, x16, [SP]
    // 0xbe5b08: mov             x1, x0
    // 0xbe5b0c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe5b0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe5b10: ldr             x4, [x4, #0xaa0]
    // 0xbe5b14: r0 = copyWith()
    //     0xbe5b14: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe5b18: stur            x0, [fp, #-0x10]
    // 0xbe5b1c: r0 = TextSpan()
    //     0xbe5b1c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbe5b20: mov             x3, x0
    // 0xbe5b24: ldur            x0, [fp, #-8]
    // 0xbe5b28: stur            x3, [fp, #-0x20]
    // 0xbe5b2c: StoreField: r3->field_b = r0
    //     0xbe5b2c: stur            w0, [x3, #0xb]
    // 0xbe5b30: r0 = Instance__DeferringMouseCursor
    //     0xbe5b30: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbe5b34: ArrayStore: r3[0] = r0  ; List_4
    //     0xbe5b34: stur            w0, [x3, #0x17]
    // 0xbe5b38: ldur            x1, [fp, #-0x10]
    // 0xbe5b3c: StoreField: r3->field_7 = r1
    //     0xbe5b3c: stur            w1, [x3, #7]
    // 0xbe5b40: r1 = Null
    //     0xbe5b40: mov             x1, NULL
    // 0xbe5b44: r2 = 6
    //     0xbe5b44: movz            x2, #0x6
    // 0xbe5b48: r0 = AllocateArray()
    //     0xbe5b48: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe5b4c: mov             x2, x0
    // 0xbe5b50: ldur            x0, [fp, #-0x18]
    // 0xbe5b54: stur            x2, [fp, #-8]
    // 0xbe5b58: StoreField: r2->field_f = r0
    //     0xbe5b58: stur            w0, [x2, #0xf]
    // 0xbe5b5c: ldur            x0, [fp, #-0x30]
    // 0xbe5b60: StoreField: r2->field_13 = r0
    //     0xbe5b60: stur            w0, [x2, #0x13]
    // 0xbe5b64: ldur            x0, [fp, #-0x20]
    // 0xbe5b68: ArrayStore: r2[0] = r0  ; List_4
    //     0xbe5b68: stur            w0, [x2, #0x17]
    // 0xbe5b6c: r1 = <InlineSpan>
    //     0xbe5b6c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xbe5b70: ldr             x1, [x1, #0xe40]
    // 0xbe5b74: r0 = AllocateGrowableArray()
    //     0xbe5b74: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe5b78: mov             x1, x0
    // 0xbe5b7c: ldur            x0, [fp, #-8]
    // 0xbe5b80: stur            x1, [fp, #-0x10]
    // 0xbe5b84: StoreField: r1->field_f = r0
    //     0xbe5b84: stur            w0, [x1, #0xf]
    // 0xbe5b88: r0 = 6
    //     0xbe5b88: movz            x0, #0x6
    // 0xbe5b8c: StoreField: r1->field_b = r0
    //     0xbe5b8c: stur            w0, [x1, #0xb]
    // 0xbe5b90: r0 = TextSpan()
    //     0xbe5b90: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbe5b94: mov             x1, x0
    // 0xbe5b98: ldur            x0, [fp, #-0x10]
    // 0xbe5b9c: stur            x1, [fp, #-8]
    // 0xbe5ba0: StoreField: r1->field_f = r0
    //     0xbe5ba0: stur            w0, [x1, #0xf]
    // 0xbe5ba4: r0 = Instance__DeferringMouseCursor
    //     0xbe5ba4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbe5ba8: ArrayStore: r1[0] = r0  ; List_4
    //     0xbe5ba8: stur            w0, [x1, #0x17]
    // 0xbe5bac: r0 = RichText()
    //     0xbe5bac: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xbe5bb0: mov             x1, x0
    // 0xbe5bb4: ldur            x2, [fp, #-8]
    // 0xbe5bb8: stur            x0, [fp, #-8]
    // 0xbe5bbc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbe5bbc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbe5bc0: r0 = RichText()
    //     0xbe5bc0: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xbe5bc4: r0 = SizedBox()
    //     0xbe5bc4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbe5bc8: mov             x1, x0
    // 0xbe5bcc: r0 = 32.000000
    //     0xbe5bcc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xbe5bd0: ldr             x0, [x0, #0x848]
    // 0xbe5bd4: stur            x1, [fp, #-0x10]
    // 0xbe5bd8: StoreField: r1->field_13 = r0
    //     0xbe5bd8: stur            w0, [x1, #0x13]
    // 0xbe5bdc: ldur            x0, [fp, #-8]
    // 0xbe5be0: StoreField: r1->field_b = r0
    //     0xbe5be0: stur            w0, [x1, #0xb]
    // 0xbe5be4: r0 = Padding()
    //     0xbe5be4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe5be8: r1 = Instance_EdgeInsets
    //     0xbe5be8: add             x1, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbe5bec: ldr             x1, [x1, #0x770]
    // 0xbe5bf0: StoreField: r0->field_f = r1
    //     0xbe5bf0: stur            w1, [x0, #0xf]
    // 0xbe5bf4: ldur            x1, [fp, #-0x10]
    // 0xbe5bf8: StoreField: r0->field_b = r1
    //     0xbe5bf8: stur            w1, [x0, #0xb]
    // 0xbe5bfc: LeaveFrame
    //     0xbe5bfc: mov             SP, fp
    //     0xbe5c00: ldp             fp, lr, [SP], #0x10
    // 0xbe5c04: ret
    //     0xbe5c04: ret             
    // 0xbe5c08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe5c08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe5c0c: b               #0xbe565c
    // 0xbe5c10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5c10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe5c14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5c14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe5c18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5c18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe5c1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5c1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe5c20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5c20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe5c24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5c24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe5c28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5c28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe5c2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5c2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe5c30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe5c30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildRatingSection(/* No info */) {
    // ** addr: 0xbe5c34, size: 0xc88
    // 0xbe5c34: EnterFrame
    //     0xbe5c34: stp             fp, lr, [SP, #-0x10]!
    //     0xbe5c38: mov             fp, SP
    // 0xbe5c3c: AllocStack(0x50)
    //     0xbe5c3c: sub             SP, SP, #0x50
    // 0xbe5c40: SetupParameters(_ProductGridItemState this /* r1 => r3, fp-0x10 */)
    //     0xbe5c40: mov             x3, x1
    //     0xbe5c44: stur            x1, [fp, #-0x10]
    // 0xbe5c48: CheckStackOverflow
    //     0xbe5c48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe5c4c: cmp             SP, x16
    //     0xbe5c50: b.ls            #0xbe6854
    // 0xbe5c54: LoadField: r0 = r3->field_b
    //     0xbe5c54: ldur            w0, [x3, #0xb]
    // 0xbe5c58: DecompressPointer r0
    //     0xbe5c58: add             x0, x0, HEAP, lsl #32
    // 0xbe5c5c: cmp             w0, NULL
    // 0xbe5c60: b.eq            #0xbe685c
    // 0xbe5c64: LoadField: r4 = r0->field_b
    //     0xbe5c64: ldur            w4, [x0, #0xb]
    // 0xbe5c68: DecompressPointer r4
    //     0xbe5c68: add             x4, x4, HEAP, lsl #32
    // 0xbe5c6c: r0 = BoxInt64Instr(r2)
    //     0xbe5c6c: sbfiz           x0, x2, #1, #0x1f
    //     0xbe5c70: cmp             x2, x0, asr #1
    //     0xbe5c74: b.eq            #0xbe5c80
    //     0xbe5c78: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe5c7c: stur            x2, [x0, #7]
    // 0xbe5c80: mov             x1, x0
    // 0xbe5c84: stur            x1, [fp, #-8]
    // 0xbe5c88: r0 = LoadClassIdInstr(r4)
    //     0xbe5c88: ldur            x0, [x4, #-1]
    //     0xbe5c8c: ubfx            x0, x0, #0xc, #0x14
    // 0xbe5c90: stp             x1, x4, [SP]
    // 0xbe5c94: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe5c94: sub             lr, x0, #0xb7
    //     0xbe5c98: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5c9c: blr             lr
    // 0xbe5ca0: cmp             w0, NULL
    // 0xbe5ca4: b.ne            #0xbe5cb0
    // 0xbe5ca8: r0 = Null
    //     0xbe5ca8: mov             x0, NULL
    // 0xbe5cac: b               #0xbe5cd0
    // 0xbe5cb0: LoadField: r1 = r0->field_7b
    //     0xbe5cb0: ldur            w1, [x0, #0x7b]
    // 0xbe5cb4: DecompressPointer r1
    //     0xbe5cb4: add             x1, x1, HEAP, lsl #32
    // 0xbe5cb8: cmp             w1, NULL
    // 0xbe5cbc: b.ne            #0xbe5cc8
    // 0xbe5cc0: r0 = Null
    //     0xbe5cc0: mov             x0, NULL
    // 0xbe5cc4: b               #0xbe5cd0
    // 0xbe5cc8: LoadField: r0 = r1->field_7
    //     0xbe5cc8: ldur            w0, [x1, #7]
    // 0xbe5ccc: DecompressPointer r0
    //     0xbe5ccc: add             x0, x0, HEAP, lsl #32
    // 0xbe5cd0: r1 = LoadClassIdInstr(r0)
    //     0xbe5cd0: ldur            x1, [x0, #-1]
    //     0xbe5cd4: ubfx            x1, x1, #0xc, #0x14
    // 0xbe5cd8: r16 = 0.000000
    //     0xbe5cd8: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xbe5cdc: stp             x16, x0, [SP]
    // 0xbe5ce0: mov             x0, x1
    // 0xbe5ce4: mov             lr, x0
    // 0xbe5ce8: ldr             lr, [x21, lr, lsl #3]
    // 0xbe5cec: blr             lr
    // 0xbe5cf0: eor             x1, x0, #0x10
    // 0xbe5cf4: tbz             w1, #4, #0xbe5d08
    // 0xbe5cf8: r0 = Instance_SizedBox
    //     0xbe5cf8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbe5cfc: LeaveFrame
    //     0xbe5cfc: mov             SP, fp
    //     0xbe5d00: ldp             fp, lr, [SP], #0x10
    // 0xbe5d04: ret
    //     0xbe5d04: ret             
    // 0xbe5d08: ldur            x1, [fp, #-0x10]
    // 0xbe5d0c: LoadField: r0 = r1->field_b
    //     0xbe5d0c: ldur            w0, [x1, #0xb]
    // 0xbe5d10: DecompressPointer r0
    //     0xbe5d10: add             x0, x0, HEAP, lsl #32
    // 0xbe5d14: cmp             w0, NULL
    // 0xbe5d18: b.eq            #0xbe6860
    // 0xbe5d1c: LoadField: r2 = r0->field_b
    //     0xbe5d1c: ldur            w2, [x0, #0xb]
    // 0xbe5d20: DecompressPointer r2
    //     0xbe5d20: add             x2, x2, HEAP, lsl #32
    // 0xbe5d24: r0 = LoadClassIdInstr(r2)
    //     0xbe5d24: ldur            x0, [x2, #-1]
    //     0xbe5d28: ubfx            x0, x0, #0xc, #0x14
    // 0xbe5d2c: ldur            x16, [fp, #-8]
    // 0xbe5d30: stp             x16, x2, [SP]
    // 0xbe5d34: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe5d34: sub             lr, x0, #0xb7
    //     0xbe5d38: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5d3c: blr             lr
    // 0xbe5d40: cmp             w0, NULL
    // 0xbe5d44: b.ne            #0xbe5d50
    // 0xbe5d48: r0 = Null
    //     0xbe5d48: mov             x0, NULL
    // 0xbe5d4c: b               #0xbe5d70
    // 0xbe5d50: LoadField: r1 = r0->field_7b
    //     0xbe5d50: ldur            w1, [x0, #0x7b]
    // 0xbe5d54: DecompressPointer r1
    //     0xbe5d54: add             x1, x1, HEAP, lsl #32
    // 0xbe5d58: cmp             w1, NULL
    // 0xbe5d5c: b.ne            #0xbe5d68
    // 0xbe5d60: r0 = Null
    //     0xbe5d60: mov             x0, NULL
    // 0xbe5d64: b               #0xbe5d70
    // 0xbe5d68: LoadField: r0 = r1->field_f
    //     0xbe5d68: ldur            w0, [x1, #0xf]
    // 0xbe5d6c: DecompressPointer r0
    //     0xbe5d6c: add             x0, x0, HEAP, lsl #32
    // 0xbe5d70: r1 = LoadClassIdInstr(r0)
    //     0xbe5d70: ldur            x1, [x0, #-1]
    //     0xbe5d74: ubfx            x1, x1, #0xc, #0x14
    // 0xbe5d78: r16 = "product_rating"
    //     0xbe5d78: add             x16, PP, #0x23, lsl #12  ; [pp+0x23f20] "product_rating"
    //     0xbe5d7c: ldr             x16, [x16, #0xf20]
    // 0xbe5d80: stp             x16, x0, [SP]
    // 0xbe5d84: mov             x0, x1
    // 0xbe5d88: mov             lr, x0
    // 0xbe5d8c: ldr             lr, [x21, lr, lsl #3]
    // 0xbe5d90: blr             lr
    // 0xbe5d94: tbnz            w0, #4, #0xbe6494
    // 0xbe5d98: ldur            x1, [fp, #-0x10]
    // 0xbe5d9c: LoadField: r0 = r1->field_b
    //     0xbe5d9c: ldur            w0, [x1, #0xb]
    // 0xbe5da0: DecompressPointer r0
    //     0xbe5da0: add             x0, x0, HEAP, lsl #32
    // 0xbe5da4: cmp             w0, NULL
    // 0xbe5da8: b.eq            #0xbe6864
    // 0xbe5dac: LoadField: r2 = r0->field_b
    //     0xbe5dac: ldur            w2, [x0, #0xb]
    // 0xbe5db0: DecompressPointer r2
    //     0xbe5db0: add             x2, x2, HEAP, lsl #32
    // 0xbe5db4: r0 = LoadClassIdInstr(r2)
    //     0xbe5db4: ldur            x0, [x2, #-1]
    //     0xbe5db8: ubfx            x0, x0, #0xc, #0x14
    // 0xbe5dbc: ldur            x16, [fp, #-8]
    // 0xbe5dc0: stp             x16, x2, [SP]
    // 0xbe5dc4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe5dc4: sub             lr, x0, #0xb7
    //     0xbe5dc8: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5dcc: blr             lr
    // 0xbe5dd0: cmp             w0, NULL
    // 0xbe5dd4: b.ne            #0xbe5de0
    // 0xbe5dd8: r0 = Null
    //     0xbe5dd8: mov             x0, NULL
    // 0xbe5ddc: b               #0xbe5e00
    // 0xbe5de0: LoadField: r1 = r0->field_7b
    //     0xbe5de0: ldur            w1, [x0, #0x7b]
    // 0xbe5de4: DecompressPointer r1
    //     0xbe5de4: add             x1, x1, HEAP, lsl #32
    // 0xbe5de8: cmp             w1, NULL
    // 0xbe5dec: b.ne            #0xbe5df8
    // 0xbe5df0: r0 = Null
    //     0xbe5df0: mov             x0, NULL
    // 0xbe5df4: b               #0xbe5e00
    // 0xbe5df8: LoadField: r0 = r1->field_7
    //     0xbe5df8: ldur            w0, [x1, #7]
    // 0xbe5dfc: DecompressPointer r0
    //     0xbe5dfc: add             x0, x0, HEAP, lsl #32
    // 0xbe5e00: ldur            x1, [fp, #-0x10]
    // 0xbe5e04: cmp             w0, NULL
    // 0xbe5e08: r16 = true
    //     0xbe5e08: add             x16, NULL, #0x20  ; true
    // 0xbe5e0c: r17 = false
    //     0xbe5e0c: add             x17, NULL, #0x30  ; false
    // 0xbe5e10: csel            x2, x16, x17, ne
    // 0xbe5e14: stur            x2, [fp, #-0x18]
    // 0xbe5e18: LoadField: r0 = r1->field_b
    //     0xbe5e18: ldur            w0, [x1, #0xb]
    // 0xbe5e1c: DecompressPointer r0
    //     0xbe5e1c: add             x0, x0, HEAP, lsl #32
    // 0xbe5e20: cmp             w0, NULL
    // 0xbe5e24: b.eq            #0xbe6868
    // 0xbe5e28: LoadField: r3 = r0->field_b
    //     0xbe5e28: ldur            w3, [x0, #0xb]
    // 0xbe5e2c: DecompressPointer r3
    //     0xbe5e2c: add             x3, x3, HEAP, lsl #32
    // 0xbe5e30: r0 = LoadClassIdInstr(r3)
    //     0xbe5e30: ldur            x0, [x3, #-1]
    //     0xbe5e34: ubfx            x0, x0, #0xc, #0x14
    // 0xbe5e38: ldur            x16, [fp, #-8]
    // 0xbe5e3c: stp             x16, x3, [SP]
    // 0xbe5e40: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe5e40: sub             lr, x0, #0xb7
    //     0xbe5e44: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5e48: blr             lr
    // 0xbe5e4c: cmp             w0, NULL
    // 0xbe5e50: b.ne            #0xbe5e5c
    // 0xbe5e54: r0 = Null
    //     0xbe5e54: mov             x0, NULL
    // 0xbe5e58: b               #0xbe5e7c
    // 0xbe5e5c: LoadField: r1 = r0->field_7b
    //     0xbe5e5c: ldur            w1, [x0, #0x7b]
    // 0xbe5e60: DecompressPointer r1
    //     0xbe5e60: add             x1, x1, HEAP, lsl #32
    // 0xbe5e64: cmp             w1, NULL
    // 0xbe5e68: b.ne            #0xbe5e74
    // 0xbe5e6c: r0 = Null
    //     0xbe5e6c: mov             x0, NULL
    // 0xbe5e70: b               #0xbe5e7c
    // 0xbe5e74: LoadField: r0 = r1->field_7
    //     0xbe5e74: ldur            w0, [x1, #7]
    // 0xbe5e78: DecompressPointer r0
    //     0xbe5e78: add             x0, x0, HEAP, lsl #32
    // 0xbe5e7c: cmp             w0, NULL
    // 0xbe5e80: b.ne            #0xbe5e8c
    // 0xbe5e84: d1 = 0.000000
    //     0xbe5e84: eor             v1.16b, v1.16b, v1.16b
    // 0xbe5e88: b               #0xbe5e94
    // 0xbe5e8c: LoadField: d0 = r0->field_7
    //     0xbe5e8c: ldur            d0, [x0, #7]
    // 0xbe5e90: mov             v1.16b, v0.16b
    // 0xbe5e94: d0 = 4.000000
    //     0xbe5e94: fmov            d0, #4.00000000
    // 0xbe5e98: fcmp            d1, d0
    // 0xbe5e9c: b.lt            #0xbe5eac
    // 0xbe5ea0: r1 = Instance_Color
    //     0xbe5ea0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbe5ea4: ldr             x1, [x1, #0x858]
    // 0xbe5ea8: b               #0xbe5ff4
    // 0xbe5eac: ldur            x1, [fp, #-0x10]
    // 0xbe5eb0: LoadField: r0 = r1->field_b
    //     0xbe5eb0: ldur            w0, [x1, #0xb]
    // 0xbe5eb4: DecompressPointer r0
    //     0xbe5eb4: add             x0, x0, HEAP, lsl #32
    // 0xbe5eb8: cmp             w0, NULL
    // 0xbe5ebc: b.eq            #0xbe686c
    // 0xbe5ec0: LoadField: r2 = r0->field_b
    //     0xbe5ec0: ldur            w2, [x0, #0xb]
    // 0xbe5ec4: DecompressPointer r2
    //     0xbe5ec4: add             x2, x2, HEAP, lsl #32
    // 0xbe5ec8: r0 = LoadClassIdInstr(r2)
    //     0xbe5ec8: ldur            x0, [x2, #-1]
    //     0xbe5ecc: ubfx            x0, x0, #0xc, #0x14
    // 0xbe5ed0: ldur            x16, [fp, #-8]
    // 0xbe5ed4: stp             x16, x2, [SP]
    // 0xbe5ed8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe5ed8: sub             lr, x0, #0xb7
    //     0xbe5edc: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5ee0: blr             lr
    // 0xbe5ee4: cmp             w0, NULL
    // 0xbe5ee8: b.ne            #0xbe5ef4
    // 0xbe5eec: r0 = Null
    //     0xbe5eec: mov             x0, NULL
    // 0xbe5ef0: b               #0xbe5f14
    // 0xbe5ef4: LoadField: r1 = r0->field_7b
    //     0xbe5ef4: ldur            w1, [x0, #0x7b]
    // 0xbe5ef8: DecompressPointer r1
    //     0xbe5ef8: add             x1, x1, HEAP, lsl #32
    // 0xbe5efc: cmp             w1, NULL
    // 0xbe5f00: b.ne            #0xbe5f0c
    // 0xbe5f04: r0 = Null
    //     0xbe5f04: mov             x0, NULL
    // 0xbe5f08: b               #0xbe5f14
    // 0xbe5f0c: LoadField: r0 = r1->field_7
    //     0xbe5f0c: ldur            w0, [x1, #7]
    // 0xbe5f10: DecompressPointer r0
    //     0xbe5f10: add             x0, x0, HEAP, lsl #32
    // 0xbe5f14: cmp             w0, NULL
    // 0xbe5f18: b.ne            #0xbe5f24
    // 0xbe5f1c: d1 = 0.000000
    //     0xbe5f1c: eor             v1.16b, v1.16b, v1.16b
    // 0xbe5f20: b               #0xbe5f2c
    // 0xbe5f24: LoadField: d0 = r0->field_7
    //     0xbe5f24: ldur            d0, [x0, #7]
    // 0xbe5f28: mov             v1.16b, v0.16b
    // 0xbe5f2c: d0 = 3.500000
    //     0xbe5f2c: fmov            d0, #3.50000000
    // 0xbe5f30: fcmp            d1, d0
    // 0xbe5f34: b.lt            #0xbe5f50
    // 0xbe5f38: r1 = Instance_Color
    //     0xbe5f38: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbe5f3c: ldr             x1, [x1, #0x858]
    // 0xbe5f40: d0 = 0.700000
    //     0xbe5f40: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbe5f44: ldr             d0, [x17, #0xf48]
    // 0xbe5f48: r0 = withOpacity()
    //     0xbe5f48: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbe5f4c: b               #0xbe5ff0
    // 0xbe5f50: ldur            x1, [fp, #-0x10]
    // 0xbe5f54: LoadField: r0 = r1->field_b
    //     0xbe5f54: ldur            w0, [x1, #0xb]
    // 0xbe5f58: DecompressPointer r0
    //     0xbe5f58: add             x0, x0, HEAP, lsl #32
    // 0xbe5f5c: cmp             w0, NULL
    // 0xbe5f60: b.eq            #0xbe6870
    // 0xbe5f64: LoadField: r2 = r0->field_b
    //     0xbe5f64: ldur            w2, [x0, #0xb]
    // 0xbe5f68: DecompressPointer r2
    //     0xbe5f68: add             x2, x2, HEAP, lsl #32
    // 0xbe5f6c: r0 = LoadClassIdInstr(r2)
    //     0xbe5f6c: ldur            x0, [x2, #-1]
    //     0xbe5f70: ubfx            x0, x0, #0xc, #0x14
    // 0xbe5f74: ldur            x16, [fp, #-8]
    // 0xbe5f78: stp             x16, x2, [SP]
    // 0xbe5f7c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe5f7c: sub             lr, x0, #0xb7
    //     0xbe5f80: ldr             lr, [x21, lr, lsl #3]
    //     0xbe5f84: blr             lr
    // 0xbe5f88: cmp             w0, NULL
    // 0xbe5f8c: b.ne            #0xbe5f98
    // 0xbe5f90: r0 = Null
    //     0xbe5f90: mov             x0, NULL
    // 0xbe5f94: b               #0xbe5fb8
    // 0xbe5f98: LoadField: r1 = r0->field_7b
    //     0xbe5f98: ldur            w1, [x0, #0x7b]
    // 0xbe5f9c: DecompressPointer r1
    //     0xbe5f9c: add             x1, x1, HEAP, lsl #32
    // 0xbe5fa0: cmp             w1, NULL
    // 0xbe5fa4: b.ne            #0xbe5fb0
    // 0xbe5fa8: r0 = Null
    //     0xbe5fa8: mov             x0, NULL
    // 0xbe5fac: b               #0xbe5fb8
    // 0xbe5fb0: LoadField: r0 = r1->field_7
    //     0xbe5fb0: ldur            w0, [x1, #7]
    // 0xbe5fb4: DecompressPointer r0
    //     0xbe5fb4: add             x0, x0, HEAP, lsl #32
    // 0xbe5fb8: cmp             w0, NULL
    // 0xbe5fbc: b.ne            #0xbe5fc8
    // 0xbe5fc0: d1 = 0.000000
    //     0xbe5fc0: eor             v1.16b, v1.16b, v1.16b
    // 0xbe5fc4: b               #0xbe5fd0
    // 0xbe5fc8: LoadField: d0 = r0->field_7
    //     0xbe5fc8: ldur            d0, [x0, #7]
    // 0xbe5fcc: mov             v1.16b, v0.16b
    // 0xbe5fd0: d0 = 2.000000
    //     0xbe5fd0: fmov            d0, #2.00000000
    // 0xbe5fd4: fcmp            d1, d0
    // 0xbe5fd8: b.lt            #0xbe5fe8
    // 0xbe5fdc: r0 = Instance_Color
    //     0xbe5fdc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xbe5fe0: ldr             x0, [x0, #0x860]
    // 0xbe5fe4: b               #0xbe5ff0
    // 0xbe5fe8: r0 = Instance_Color
    //     0xbe5fe8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbe5fec: ldr             x0, [x0, #0x50]
    // 0xbe5ff0: mov             x1, x0
    // 0xbe5ff4: ldur            x0, [fp, #-0x10]
    // 0xbe5ff8: stur            x1, [fp, #-0x20]
    // 0xbe5ffc: r0 = ColorFilter()
    //     0xbe5ffc: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbe6000: mov             x1, x0
    // 0xbe6004: ldur            x0, [fp, #-0x20]
    // 0xbe6008: stur            x1, [fp, #-0x28]
    // 0xbe600c: StoreField: r1->field_7 = r0
    //     0xbe600c: stur            w0, [x1, #7]
    // 0xbe6010: r2 = Instance_BlendMode
    //     0xbe6010: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbe6014: ldr             x2, [x2, #0xb30]
    // 0xbe6018: StoreField: r1->field_b = r2
    //     0xbe6018: stur            w2, [x1, #0xb]
    // 0xbe601c: r3 = 1
    //     0xbe601c: movz            x3, #0x1
    // 0xbe6020: StoreField: r1->field_13 = r3
    //     0xbe6020: stur            x3, [x1, #0x13]
    // 0xbe6024: r0 = SvgPicture()
    //     0xbe6024: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbe6028: stur            x0, [fp, #-0x20]
    // 0xbe602c: ldur            x16, [fp, #-0x28]
    // 0xbe6030: str             x16, [SP]
    // 0xbe6034: mov             x1, x0
    // 0xbe6038: r2 = "assets/images/green_star.svg"
    //     0xbe6038: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xbe603c: ldr             x2, [x2, #0x9a0]
    // 0xbe6040: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xbe6040: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xbe6044: ldr             x4, [x4, #0xa38]
    // 0xbe6048: r0 = SvgPicture.asset()
    //     0xbe6048: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbe604c: ldur            x1, [fp, #-0x10]
    // 0xbe6050: LoadField: r0 = r1->field_b
    //     0xbe6050: ldur            w0, [x1, #0xb]
    // 0xbe6054: DecompressPointer r0
    //     0xbe6054: add             x0, x0, HEAP, lsl #32
    // 0xbe6058: cmp             w0, NULL
    // 0xbe605c: b.eq            #0xbe6874
    // 0xbe6060: LoadField: r2 = r0->field_b
    //     0xbe6060: ldur            w2, [x0, #0xb]
    // 0xbe6064: DecompressPointer r2
    //     0xbe6064: add             x2, x2, HEAP, lsl #32
    // 0xbe6068: r0 = LoadClassIdInstr(r2)
    //     0xbe6068: ldur            x0, [x2, #-1]
    //     0xbe606c: ubfx            x0, x0, #0xc, #0x14
    // 0xbe6070: ldur            x16, [fp, #-8]
    // 0xbe6074: stp             x16, x2, [SP]
    // 0xbe6078: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe6078: sub             lr, x0, #0xb7
    //     0xbe607c: ldr             lr, [x21, lr, lsl #3]
    //     0xbe6080: blr             lr
    // 0xbe6084: cmp             w0, NULL
    // 0xbe6088: b.ne            #0xbe6094
    // 0xbe608c: r0 = Null
    //     0xbe608c: mov             x0, NULL
    // 0xbe6090: b               #0xbe60cc
    // 0xbe6094: LoadField: r1 = r0->field_7b
    //     0xbe6094: ldur            w1, [x0, #0x7b]
    // 0xbe6098: DecompressPointer r1
    //     0xbe6098: add             x1, x1, HEAP, lsl #32
    // 0xbe609c: cmp             w1, NULL
    // 0xbe60a0: b.ne            #0xbe60ac
    // 0xbe60a4: r0 = Null
    //     0xbe60a4: mov             x0, NULL
    // 0xbe60a8: b               #0xbe60cc
    // 0xbe60ac: LoadField: r0 = r1->field_7
    //     0xbe60ac: ldur            w0, [x1, #7]
    // 0xbe60b0: DecompressPointer r0
    //     0xbe60b0: add             x0, x0, HEAP, lsl #32
    // 0xbe60b4: cmp             w0, NULL
    // 0xbe60b8: b.ne            #0xbe60c4
    // 0xbe60bc: r0 = Null
    //     0xbe60bc: mov             x0, NULL
    // 0xbe60c0: b               #0xbe60cc
    // 0xbe60c4: str             x0, [SP]
    // 0xbe60c8: r0 = toString()
    //     0xbe60c8: bl              #0x1583704  ; [dart:core] _Double::toString
    // 0xbe60cc: cmp             w0, NULL
    // 0xbe60d0: b.ne            #0xbe60dc
    // 0xbe60d4: r3 = ""
    //     0xbe60d4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe60d8: b               #0xbe60e0
    // 0xbe60dc: mov             x3, x0
    // 0xbe60e0: ldur            x0, [fp, #-0x10]
    // 0xbe60e4: ldur            x2, [fp, #-0x20]
    // 0xbe60e8: stur            x3, [fp, #-0x28]
    // 0xbe60ec: LoadField: r1 = r0->field_f
    //     0xbe60ec: ldur            w1, [x0, #0xf]
    // 0xbe60f0: DecompressPointer r1
    //     0xbe60f0: add             x1, x1, HEAP, lsl #32
    // 0xbe60f4: cmp             w1, NULL
    // 0xbe60f8: b.eq            #0xbe6878
    // 0xbe60fc: r0 = of()
    //     0xbe60fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe6100: LoadField: r1 = r0->field_87
    //     0xbe6100: ldur            w1, [x0, #0x87]
    // 0xbe6104: DecompressPointer r1
    //     0xbe6104: add             x1, x1, HEAP, lsl #32
    // 0xbe6108: LoadField: r0 = r1->field_7
    //     0xbe6108: ldur            w0, [x1, #7]
    // 0xbe610c: DecompressPointer r0
    //     0xbe610c: add             x0, x0, HEAP, lsl #32
    // 0xbe6110: r16 = 12.000000
    //     0xbe6110: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe6114: ldr             x16, [x16, #0x9e8]
    // 0xbe6118: r30 = Instance_Color
    //     0xbe6118: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe611c: stp             lr, x16, [SP]
    // 0xbe6120: mov             x1, x0
    // 0xbe6124: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe6124: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe6128: ldr             x4, [x4, #0xaa0]
    // 0xbe612c: r0 = copyWith()
    //     0xbe612c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe6130: stur            x0, [fp, #-0x30]
    // 0xbe6134: r0 = Text()
    //     0xbe6134: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe6138: mov             x3, x0
    // 0xbe613c: ldur            x0, [fp, #-0x28]
    // 0xbe6140: stur            x3, [fp, #-0x38]
    // 0xbe6144: StoreField: r3->field_b = r0
    //     0xbe6144: stur            w0, [x3, #0xb]
    // 0xbe6148: ldur            x0, [fp, #-0x30]
    // 0xbe614c: StoreField: r3->field_13 = r0
    //     0xbe614c: stur            w0, [x3, #0x13]
    // 0xbe6150: r1 = Null
    //     0xbe6150: mov             x1, NULL
    // 0xbe6154: r2 = 4
    //     0xbe6154: movz            x2, #0x4
    // 0xbe6158: r0 = AllocateArray()
    //     0xbe6158: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe615c: mov             x2, x0
    // 0xbe6160: ldur            x0, [fp, #-0x20]
    // 0xbe6164: stur            x2, [fp, #-0x28]
    // 0xbe6168: StoreField: r2->field_f = r0
    //     0xbe6168: stur            w0, [x2, #0xf]
    // 0xbe616c: ldur            x0, [fp, #-0x38]
    // 0xbe6170: StoreField: r2->field_13 = r0
    //     0xbe6170: stur            w0, [x2, #0x13]
    // 0xbe6174: r1 = <Widget>
    //     0xbe6174: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe6178: r0 = AllocateGrowableArray()
    //     0xbe6178: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe617c: mov             x1, x0
    // 0xbe6180: ldur            x0, [fp, #-0x28]
    // 0xbe6184: stur            x1, [fp, #-0x20]
    // 0xbe6188: StoreField: r1->field_f = r0
    //     0xbe6188: stur            w0, [x1, #0xf]
    // 0xbe618c: r0 = 4
    //     0xbe618c: movz            x0, #0x4
    // 0xbe6190: StoreField: r1->field_b = r0
    //     0xbe6190: stur            w0, [x1, #0xb]
    // 0xbe6194: ldur            x2, [fp, #-0x10]
    // 0xbe6198: LoadField: r0 = r2->field_b
    //     0xbe6198: ldur            w0, [x2, #0xb]
    // 0xbe619c: DecompressPointer r0
    //     0xbe619c: add             x0, x0, HEAP, lsl #32
    // 0xbe61a0: cmp             w0, NULL
    // 0xbe61a4: b.eq            #0xbe687c
    // 0xbe61a8: LoadField: r3 = r0->field_b
    //     0xbe61a8: ldur            w3, [x0, #0xb]
    // 0xbe61ac: DecompressPointer r3
    //     0xbe61ac: add             x3, x3, HEAP, lsl #32
    // 0xbe61b0: r0 = LoadClassIdInstr(r3)
    //     0xbe61b0: ldur            x0, [x3, #-1]
    //     0xbe61b4: ubfx            x0, x0, #0xc, #0x14
    // 0xbe61b8: ldur            x16, [fp, #-8]
    // 0xbe61bc: stp             x16, x3, [SP]
    // 0xbe61c0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe61c0: sub             lr, x0, #0xb7
    //     0xbe61c4: ldr             lr, [x21, lr, lsl #3]
    //     0xbe61c8: blr             lr
    // 0xbe61cc: cmp             w0, NULL
    // 0xbe61d0: b.ne            #0xbe61dc
    // 0xbe61d4: ldur            x2, [fp, #-0x20]
    // 0xbe61d8: b               #0xbe63f4
    // 0xbe61dc: LoadField: r1 = r0->field_7b
    //     0xbe61dc: ldur            w1, [x0, #0x7b]
    // 0xbe61e0: DecompressPointer r1
    //     0xbe61e0: add             x1, x1, HEAP, lsl #32
    // 0xbe61e4: cmp             w1, NULL
    // 0xbe61e8: b.ne            #0xbe61f4
    // 0xbe61ec: ldur            x2, [fp, #-0x20]
    // 0xbe61f0: b               #0xbe63f4
    // 0xbe61f4: LoadField: r0 = r1->field_b
    //     0xbe61f4: ldur            w0, [x1, #0xb]
    // 0xbe61f8: DecompressPointer r0
    //     0xbe61f8: add             x0, x0, HEAP, lsl #32
    // 0xbe61fc: cmp             w0, NULL
    // 0xbe6200: b.eq            #0xbe63f0
    // 0xbe6204: ldur            x0, [fp, #-0x10]
    // 0xbe6208: r1 = Null
    //     0xbe6208: mov             x1, NULL
    // 0xbe620c: r2 = 6
    //     0xbe620c: movz            x2, #0x6
    // 0xbe6210: r0 = AllocateArray()
    //     0xbe6210: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe6214: mov             x1, x0
    // 0xbe6218: stur            x1, [fp, #-0x28]
    // 0xbe621c: r16 = " | ("
    //     0xbe621c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d70] " | ("
    //     0xbe6220: ldr             x16, [x16, #0xd70]
    // 0xbe6224: StoreField: r1->field_f = r16
    //     0xbe6224: stur            w16, [x1, #0xf]
    // 0xbe6228: ldur            x2, [fp, #-0x10]
    // 0xbe622c: LoadField: r0 = r2->field_b
    //     0xbe622c: ldur            w0, [x2, #0xb]
    // 0xbe6230: DecompressPointer r0
    //     0xbe6230: add             x0, x0, HEAP, lsl #32
    // 0xbe6234: cmp             w0, NULL
    // 0xbe6238: b.eq            #0xbe6880
    // 0xbe623c: LoadField: r3 = r0->field_b
    //     0xbe623c: ldur            w3, [x0, #0xb]
    // 0xbe6240: DecompressPointer r3
    //     0xbe6240: add             x3, x3, HEAP, lsl #32
    // 0xbe6244: r0 = LoadClassIdInstr(r3)
    //     0xbe6244: ldur            x0, [x3, #-1]
    //     0xbe6248: ubfx            x0, x0, #0xc, #0x14
    // 0xbe624c: ldur            x16, [fp, #-8]
    // 0xbe6250: stp             x16, x3, [SP]
    // 0xbe6254: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe6254: sub             lr, x0, #0xb7
    //     0xbe6258: ldr             lr, [x21, lr, lsl #3]
    //     0xbe625c: blr             lr
    // 0xbe6260: cmp             w0, NULL
    // 0xbe6264: b.ne            #0xbe6270
    // 0xbe6268: r0 = Null
    //     0xbe6268: mov             x0, NULL
    // 0xbe626c: b               #0xbe62c0
    // 0xbe6270: LoadField: r1 = r0->field_7b
    //     0xbe6270: ldur            w1, [x0, #0x7b]
    // 0xbe6274: DecompressPointer r1
    //     0xbe6274: add             x1, x1, HEAP, lsl #32
    // 0xbe6278: cmp             w1, NULL
    // 0xbe627c: b.ne            #0xbe6288
    // 0xbe6280: r0 = Null
    //     0xbe6280: mov             x0, NULL
    // 0xbe6284: b               #0xbe62c0
    // 0xbe6288: LoadField: r0 = r1->field_b
    //     0xbe6288: ldur            w0, [x1, #0xb]
    // 0xbe628c: DecompressPointer r0
    //     0xbe628c: add             x0, x0, HEAP, lsl #32
    // 0xbe6290: cmp             w0, NULL
    // 0xbe6294: b.ne            #0xbe62a0
    // 0xbe6298: r0 = Null
    //     0xbe6298: mov             x0, NULL
    // 0xbe629c: b               #0xbe62c0
    // 0xbe62a0: LoadField: d0 = r0->field_7
    //     0xbe62a0: ldur            d0, [x0, #7]
    // 0xbe62a4: fcmp            d0, d0
    // 0xbe62a8: b.vs            #0xbe6884
    // 0xbe62ac: fcvtzs          x0, d0
    // 0xbe62b0: asr             x16, x0, #0x1e
    // 0xbe62b4: cmp             x16, x0, asr #63
    // 0xbe62b8: b.ne            #0xbe6884
    // 0xbe62bc: lsl             x0, x0, #1
    // 0xbe62c0: ldur            x3, [fp, #-0x10]
    // 0xbe62c4: ldur            x2, [fp, #-0x28]
    // 0xbe62c8: ldur            x4, [fp, #-0x20]
    // 0xbe62cc: mov             x1, x2
    // 0xbe62d0: ArrayStore: r1[1] = r0  ; List_4
    //     0xbe62d0: add             x25, x1, #0x13
    //     0xbe62d4: str             w0, [x25]
    //     0xbe62d8: tbz             w0, #0, #0xbe62f4
    //     0xbe62dc: ldurb           w16, [x1, #-1]
    //     0xbe62e0: ldurb           w17, [x0, #-1]
    //     0xbe62e4: and             x16, x17, x16, lsr #2
    //     0xbe62e8: tst             x16, HEAP, lsr #32
    //     0xbe62ec: b.eq            #0xbe62f4
    //     0xbe62f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe62f4: r16 = ")"
    //     0xbe62f4: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xbe62f8: ArrayStore: r2[0] = r16  ; List_4
    //     0xbe62f8: stur            w16, [x2, #0x17]
    // 0xbe62fc: str             x2, [SP]
    // 0xbe6300: r0 = _interpolate()
    //     0xbe6300: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbe6304: ldur            x1, [fp, #-0x10]
    // 0xbe6308: stur            x0, [fp, #-0x28]
    // 0xbe630c: LoadField: r2 = r1->field_f
    //     0xbe630c: ldur            w2, [x1, #0xf]
    // 0xbe6310: DecompressPointer r2
    //     0xbe6310: add             x2, x2, HEAP, lsl #32
    // 0xbe6314: cmp             w2, NULL
    // 0xbe6318: b.eq            #0xbe68a0
    // 0xbe631c: mov             x1, x2
    // 0xbe6320: r0 = of()
    //     0xbe6320: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe6324: LoadField: r1 = r0->field_87
    //     0xbe6324: ldur            w1, [x0, #0x87]
    // 0xbe6328: DecompressPointer r1
    //     0xbe6328: add             x1, x1, HEAP, lsl #32
    // 0xbe632c: LoadField: r0 = r1->field_2b
    //     0xbe632c: ldur            w0, [x1, #0x2b]
    // 0xbe6330: DecompressPointer r0
    //     0xbe6330: add             x0, x0, HEAP, lsl #32
    // 0xbe6334: r16 = 12.000000
    //     0xbe6334: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe6338: ldr             x16, [x16, #0x9e8]
    // 0xbe633c: r30 = Instance_Color
    //     0xbe633c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe6340: stp             lr, x16, [SP]
    // 0xbe6344: mov             x1, x0
    // 0xbe6348: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe6348: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe634c: ldr             x4, [x4, #0xaa0]
    // 0xbe6350: r0 = copyWith()
    //     0xbe6350: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe6354: stur            x0, [fp, #-0x30]
    // 0xbe6358: r0 = Text()
    //     0xbe6358: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe635c: mov             x2, x0
    // 0xbe6360: ldur            x0, [fp, #-0x28]
    // 0xbe6364: stur            x2, [fp, #-0x38]
    // 0xbe6368: StoreField: r2->field_b = r0
    //     0xbe6368: stur            w0, [x2, #0xb]
    // 0xbe636c: ldur            x0, [fp, #-0x30]
    // 0xbe6370: StoreField: r2->field_13 = r0
    //     0xbe6370: stur            w0, [x2, #0x13]
    // 0xbe6374: ldur            x0, [fp, #-0x20]
    // 0xbe6378: LoadField: r1 = r0->field_b
    //     0xbe6378: ldur            w1, [x0, #0xb]
    // 0xbe637c: LoadField: r3 = r0->field_f
    //     0xbe637c: ldur            w3, [x0, #0xf]
    // 0xbe6380: DecompressPointer r3
    //     0xbe6380: add             x3, x3, HEAP, lsl #32
    // 0xbe6384: LoadField: r4 = r3->field_b
    //     0xbe6384: ldur            w4, [x3, #0xb]
    // 0xbe6388: r3 = LoadInt32Instr(r1)
    //     0xbe6388: sbfx            x3, x1, #1, #0x1f
    // 0xbe638c: stur            x3, [fp, #-0x40]
    // 0xbe6390: r1 = LoadInt32Instr(r4)
    //     0xbe6390: sbfx            x1, x4, #1, #0x1f
    // 0xbe6394: cmp             x3, x1
    // 0xbe6398: b.ne            #0xbe63a4
    // 0xbe639c: mov             x1, x0
    // 0xbe63a0: r0 = _growToNextCapacity()
    //     0xbe63a0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbe63a4: ldur            x2, [fp, #-0x20]
    // 0xbe63a8: ldur            x3, [fp, #-0x40]
    // 0xbe63ac: add             x0, x3, #1
    // 0xbe63b0: lsl             x1, x0, #1
    // 0xbe63b4: StoreField: r2->field_b = r1
    //     0xbe63b4: stur            w1, [x2, #0xb]
    // 0xbe63b8: LoadField: r1 = r2->field_f
    //     0xbe63b8: ldur            w1, [x2, #0xf]
    // 0xbe63bc: DecompressPointer r1
    //     0xbe63bc: add             x1, x1, HEAP, lsl #32
    // 0xbe63c0: ldur            x0, [fp, #-0x38]
    // 0xbe63c4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbe63c4: add             x25, x1, x3, lsl #2
    //     0xbe63c8: add             x25, x25, #0xf
    //     0xbe63cc: str             w0, [x25]
    //     0xbe63d0: tbz             w0, #0, #0xbe63ec
    //     0xbe63d4: ldurb           w16, [x1, #-1]
    //     0xbe63d8: ldurb           w17, [x0, #-1]
    //     0xbe63dc: and             x16, x17, x16, lsr #2
    //     0xbe63e0: tst             x16, HEAP, lsr #32
    //     0xbe63e4: b.eq            #0xbe63ec
    //     0xbe63e8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe63ec: b               #0xbe63f4
    // 0xbe63f0: ldur            x2, [fp, #-0x20]
    // 0xbe63f4: ldur            x0, [fp, #-0x18]
    // 0xbe63f8: r0 = Row()
    //     0xbe63f8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbe63fc: r4 = Instance_Axis
    //     0xbe63fc: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbe6400: stur            x0, [fp, #-0x28]
    // 0xbe6404: StoreField: r0->field_f = r4
    //     0xbe6404: stur            w4, [x0, #0xf]
    // 0xbe6408: r5 = Instance_MainAxisAlignment
    //     0xbe6408: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbe640c: ldr             x5, [x5, #0xa08]
    // 0xbe6410: StoreField: r0->field_13 = r5
    //     0xbe6410: stur            w5, [x0, #0x13]
    // 0xbe6414: r6 = Instance_MainAxisSize
    //     0xbe6414: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbe6418: ldr             x6, [x6, #0xa10]
    // 0xbe641c: ArrayStore: r0[0] = r6  ; List_4
    //     0xbe641c: stur            w6, [x0, #0x17]
    // 0xbe6420: r7 = Instance_CrossAxisAlignment
    //     0xbe6420: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbe6424: ldr             x7, [x7, #0xa18]
    // 0xbe6428: StoreField: r0->field_1b = r7
    //     0xbe6428: stur            w7, [x0, #0x1b]
    // 0xbe642c: r8 = Instance_VerticalDirection
    //     0xbe642c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe6430: ldr             x8, [x8, #0xa20]
    // 0xbe6434: StoreField: r0->field_23 = r8
    //     0xbe6434: stur            w8, [x0, #0x23]
    // 0xbe6438: r9 = Instance_Clip
    //     0xbe6438: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe643c: ldr             x9, [x9, #0x38]
    // 0xbe6440: StoreField: r0->field_2b = r9
    //     0xbe6440: stur            w9, [x0, #0x2b]
    // 0xbe6444: StoreField: r0->field_2f = rZR
    //     0xbe6444: stur            xzr, [x0, #0x2f]
    // 0xbe6448: ldur            x1, [fp, #-0x20]
    // 0xbe644c: StoreField: r0->field_b = r1
    //     0xbe644c: stur            w1, [x0, #0xb]
    // 0xbe6450: r0 = Visibility()
    //     0xbe6450: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbe6454: mov             x1, x0
    // 0xbe6458: ldur            x0, [fp, #-0x28]
    // 0xbe645c: StoreField: r1->field_b = r0
    //     0xbe645c: stur            w0, [x1, #0xb]
    // 0xbe6460: r10 = Instance_SizedBox
    //     0xbe6460: ldr             x10, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbe6464: StoreField: r1->field_f = r10
    //     0xbe6464: stur            w10, [x1, #0xf]
    // 0xbe6468: ldur            x0, [fp, #-0x18]
    // 0xbe646c: StoreField: r1->field_13 = r0
    //     0xbe646c: stur            w0, [x1, #0x13]
    // 0xbe6470: r11 = false
    //     0xbe6470: add             x11, NULL, #0x30  ; false
    // 0xbe6474: ArrayStore: r1[0] = r11  ; List_4
    //     0xbe6474: stur            w11, [x1, #0x17]
    // 0xbe6478: StoreField: r1->field_1b = r11
    //     0xbe6478: stur            w11, [x1, #0x1b]
    // 0xbe647c: StoreField: r1->field_1f = r11
    //     0xbe647c: stur            w11, [x1, #0x1f]
    // 0xbe6480: StoreField: r1->field_23 = r11
    //     0xbe6480: stur            w11, [x1, #0x23]
    // 0xbe6484: StoreField: r1->field_27 = r11
    //     0xbe6484: stur            w11, [x1, #0x27]
    // 0xbe6488: StoreField: r1->field_2b = r11
    //     0xbe6488: stur            w11, [x1, #0x2b]
    // 0xbe648c: mov             x0, x1
    // 0xbe6490: b               #0xbe6848
    // 0xbe6494: ldur            x1, [fp, #-0x10]
    // 0xbe6498: r5 = Instance_MainAxisAlignment
    //     0xbe6498: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbe649c: ldr             x5, [x5, #0xa08]
    // 0xbe64a0: r10 = Instance_SizedBox
    //     0xbe64a0: ldr             x10, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbe64a4: r7 = Instance_CrossAxisAlignment
    //     0xbe64a4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbe64a8: ldr             x7, [x7, #0xa18]
    // 0xbe64ac: r6 = Instance_MainAxisSize
    //     0xbe64ac: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbe64b0: ldr             x6, [x6, #0xa10]
    // 0xbe64b4: r4 = Instance_Axis
    //     0xbe64b4: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbe64b8: r8 = Instance_VerticalDirection
    //     0xbe64b8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe64bc: ldr             x8, [x8, #0xa20]
    // 0xbe64c0: r11 = false
    //     0xbe64c0: add             x11, NULL, #0x30  ; false
    // 0xbe64c4: r2 = Instance_BlendMode
    //     0xbe64c4: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbe64c8: ldr             x2, [x2, #0xb30]
    // 0xbe64cc: r9 = Instance_Clip
    //     0xbe64cc: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe64d0: ldr             x9, [x9, #0x38]
    // 0xbe64d4: r3 = 1
    //     0xbe64d4: movz            x3, #0x1
    // 0xbe64d8: LoadField: r0 = r1->field_b
    //     0xbe64d8: ldur            w0, [x1, #0xb]
    // 0xbe64dc: DecompressPointer r0
    //     0xbe64dc: add             x0, x0, HEAP, lsl #32
    // 0xbe64e0: cmp             w0, NULL
    // 0xbe64e4: b.eq            #0xbe68a4
    // 0xbe64e8: LoadField: r12 = r0->field_b
    //     0xbe64e8: ldur            w12, [x0, #0xb]
    // 0xbe64ec: DecompressPointer r12
    //     0xbe64ec: add             x12, x12, HEAP, lsl #32
    // 0xbe64f0: r0 = LoadClassIdInstr(r12)
    //     0xbe64f0: ldur            x0, [x12, #-1]
    //     0xbe64f4: ubfx            x0, x0, #0xc, #0x14
    // 0xbe64f8: ldur            x16, [fp, #-8]
    // 0xbe64fc: stp             x16, x12, [SP]
    // 0xbe6500: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe6500: sub             lr, x0, #0xb7
    //     0xbe6504: ldr             lr, [x21, lr, lsl #3]
    //     0xbe6508: blr             lr
    // 0xbe650c: cmp             w0, NULL
    // 0xbe6510: b.ne            #0xbe651c
    // 0xbe6514: r1 = Null
    //     0xbe6514: mov             x1, NULL
    // 0xbe6518: b               #0xbe6540
    // 0xbe651c: LoadField: r1 = r0->field_7b
    //     0xbe651c: ldur            w1, [x0, #0x7b]
    // 0xbe6520: DecompressPointer r1
    //     0xbe6520: add             x1, x1, HEAP, lsl #32
    // 0xbe6524: cmp             w1, NULL
    // 0xbe6528: b.ne            #0xbe6534
    // 0xbe652c: r0 = Null
    //     0xbe652c: mov             x0, NULL
    // 0xbe6530: b               #0xbe653c
    // 0xbe6534: LoadField: r0 = r1->field_7
    //     0xbe6534: ldur            w0, [x1, #7]
    // 0xbe6538: DecompressPointer r0
    //     0xbe6538: add             x0, x0, HEAP, lsl #32
    // 0xbe653c: mov             x1, x0
    // 0xbe6540: ldur            x0, [fp, #-0x10]
    // 0xbe6544: cmp             w1, NULL
    // 0xbe6548: r16 = true
    //     0xbe6548: add             x16, NULL, #0x20  ; true
    // 0xbe654c: r17 = false
    //     0xbe654c: add             x17, NULL, #0x30  ; false
    // 0xbe6550: csel            x2, x16, x17, ne
    // 0xbe6554: stur            x2, [fp, #-0x18]
    // 0xbe6558: LoadField: r1 = r0->field_f
    //     0xbe6558: ldur            w1, [x0, #0xf]
    // 0xbe655c: DecompressPointer r1
    //     0xbe655c: add             x1, x1, HEAP, lsl #32
    // 0xbe6560: cmp             w1, NULL
    // 0xbe6564: b.eq            #0xbe68a8
    // 0xbe6568: r0 = of()
    //     0xbe6568: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe656c: LoadField: r1 = r0->field_5b
    //     0xbe656c: ldur            w1, [x0, #0x5b]
    // 0xbe6570: DecompressPointer r1
    //     0xbe6570: add             x1, x1, HEAP, lsl #32
    // 0xbe6574: stur            x1, [fp, #-0x20]
    // 0xbe6578: r0 = ColorFilter()
    //     0xbe6578: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbe657c: mov             x1, x0
    // 0xbe6580: ldur            x0, [fp, #-0x20]
    // 0xbe6584: stur            x1, [fp, #-0x28]
    // 0xbe6588: StoreField: r1->field_7 = r0
    //     0xbe6588: stur            w0, [x1, #7]
    // 0xbe658c: r0 = Instance_BlendMode
    //     0xbe658c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbe6590: ldr             x0, [x0, #0xb30]
    // 0xbe6594: StoreField: r1->field_b = r0
    //     0xbe6594: stur            w0, [x1, #0xb]
    // 0xbe6598: r0 = 1
    //     0xbe6598: movz            x0, #0x1
    // 0xbe659c: StoreField: r1->field_13 = r0
    //     0xbe659c: stur            x0, [x1, #0x13]
    // 0xbe65a0: r0 = SvgPicture()
    //     0xbe65a0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbe65a4: stur            x0, [fp, #-0x20]
    // 0xbe65a8: ldur            x16, [fp, #-0x28]
    // 0xbe65ac: str             x16, [SP]
    // 0xbe65b0: mov             x1, x0
    // 0xbe65b4: r2 = "assets/images/green_star.svg"
    //     0xbe65b4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xbe65b8: ldr             x2, [x2, #0x9a0]
    // 0xbe65bc: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xbe65bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xbe65c0: ldr             x4, [x4, #0xa38]
    // 0xbe65c4: r0 = SvgPicture.asset()
    //     0xbe65c4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbe65c8: ldur            x1, [fp, #-0x10]
    // 0xbe65cc: LoadField: r0 = r1->field_b
    //     0xbe65cc: ldur            w0, [x1, #0xb]
    // 0xbe65d0: DecompressPointer r0
    //     0xbe65d0: add             x0, x0, HEAP, lsl #32
    // 0xbe65d4: cmp             w0, NULL
    // 0xbe65d8: b.eq            #0xbe68ac
    // 0xbe65dc: LoadField: r2 = r0->field_b
    //     0xbe65dc: ldur            w2, [x0, #0xb]
    // 0xbe65e0: DecompressPointer r2
    //     0xbe65e0: add             x2, x2, HEAP, lsl #32
    // 0xbe65e4: r0 = LoadClassIdInstr(r2)
    //     0xbe65e4: ldur            x0, [x2, #-1]
    //     0xbe65e8: ubfx            x0, x0, #0xc, #0x14
    // 0xbe65ec: ldur            x16, [fp, #-8]
    // 0xbe65f0: stp             x16, x2, [SP]
    // 0xbe65f4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe65f4: sub             lr, x0, #0xb7
    //     0xbe65f8: ldr             lr, [x21, lr, lsl #3]
    //     0xbe65fc: blr             lr
    // 0xbe6600: cmp             w0, NULL
    // 0xbe6604: b.ne            #0xbe6610
    // 0xbe6608: r0 = Null
    //     0xbe6608: mov             x0, NULL
    // 0xbe660c: b               #0xbe6654
    // 0xbe6610: LoadField: r1 = r0->field_7b
    //     0xbe6610: ldur            w1, [x0, #0x7b]
    // 0xbe6614: DecompressPointer r1
    //     0xbe6614: add             x1, x1, HEAP, lsl #32
    // 0xbe6618: cmp             w1, NULL
    // 0xbe661c: b.ne            #0xbe6628
    // 0xbe6620: r0 = Null
    //     0xbe6620: mov             x0, NULL
    // 0xbe6624: b               #0xbe6654
    // 0xbe6628: LoadField: r0 = r1->field_7
    //     0xbe6628: ldur            w0, [x1, #7]
    // 0xbe662c: DecompressPointer r0
    //     0xbe662c: add             x0, x0, HEAP, lsl #32
    // 0xbe6630: r1 = LoadClassIdInstr(r0)
    //     0xbe6630: ldur            x1, [x0, #-1]
    //     0xbe6634: ubfx            x1, x1, #0xc, #0x14
    // 0xbe6638: str             x0, [SP]
    // 0xbe663c: mov             x0, x1
    // 0xbe6640: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbe6640: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbe6644: r0 = GDT[cid_x0 + 0x2700]()
    //     0xbe6644: movz            x17, #0x2700
    //     0xbe6648: add             lr, x0, x17
    //     0xbe664c: ldr             lr, [x21, lr, lsl #3]
    //     0xbe6650: blr             lr
    // 0xbe6654: cmp             w0, NULL
    // 0xbe6658: b.ne            #0xbe6664
    // 0xbe665c: r4 = ""
    //     0xbe665c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe6660: b               #0xbe6668
    // 0xbe6664: mov             x4, x0
    // 0xbe6668: ldur            x0, [fp, #-0x10]
    // 0xbe666c: ldur            x3, [fp, #-0x18]
    // 0xbe6670: ldur            x2, [fp, #-0x20]
    // 0xbe6674: stur            x4, [fp, #-8]
    // 0xbe6678: LoadField: r1 = r0->field_f
    //     0xbe6678: ldur            w1, [x0, #0xf]
    // 0xbe667c: DecompressPointer r1
    //     0xbe667c: add             x1, x1, HEAP, lsl #32
    // 0xbe6680: cmp             w1, NULL
    // 0xbe6684: b.eq            #0xbe68b0
    // 0xbe6688: r0 = of()
    //     0xbe6688: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe668c: LoadField: r1 = r0->field_87
    //     0xbe668c: ldur            w1, [x0, #0x87]
    // 0xbe6690: DecompressPointer r1
    //     0xbe6690: add             x1, x1, HEAP, lsl #32
    // 0xbe6694: LoadField: r0 = r1->field_7
    //     0xbe6694: ldur            w0, [x1, #7]
    // 0xbe6698: DecompressPointer r0
    //     0xbe6698: add             x0, x0, HEAP, lsl #32
    // 0xbe669c: r16 = 12.000000
    //     0xbe669c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe66a0: ldr             x16, [x16, #0x9e8]
    // 0xbe66a4: r30 = Instance_Color
    //     0xbe66a4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe66a8: stp             lr, x16, [SP]
    // 0xbe66ac: mov             x1, x0
    // 0xbe66b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe66b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe66b4: ldr             x4, [x4, #0xaa0]
    // 0xbe66b8: r0 = copyWith()
    //     0xbe66b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe66bc: stur            x0, [fp, #-0x28]
    // 0xbe66c0: r0 = Text()
    //     0xbe66c0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe66c4: mov             x2, x0
    // 0xbe66c8: ldur            x0, [fp, #-8]
    // 0xbe66cc: stur            x2, [fp, #-0x30]
    // 0xbe66d0: StoreField: r2->field_b = r0
    //     0xbe66d0: stur            w0, [x2, #0xb]
    // 0xbe66d4: ldur            x0, [fp, #-0x28]
    // 0xbe66d8: StoreField: r2->field_13 = r0
    //     0xbe66d8: stur            w0, [x2, #0x13]
    // 0xbe66dc: ldur            x0, [fp, #-0x10]
    // 0xbe66e0: LoadField: r1 = r0->field_f
    //     0xbe66e0: ldur            w1, [x0, #0xf]
    // 0xbe66e4: DecompressPointer r1
    //     0xbe66e4: add             x1, x1, HEAP, lsl #32
    // 0xbe66e8: cmp             w1, NULL
    // 0xbe66ec: b.eq            #0xbe68b4
    // 0xbe66f0: r0 = of()
    //     0xbe66f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe66f4: LoadField: r1 = r0->field_87
    //     0xbe66f4: ldur            w1, [x0, #0x87]
    // 0xbe66f8: DecompressPointer r1
    //     0xbe66f8: add             x1, x1, HEAP, lsl #32
    // 0xbe66fc: LoadField: r0 = r1->field_2b
    //     0xbe66fc: ldur            w0, [x1, #0x2b]
    // 0xbe6700: DecompressPointer r0
    //     0xbe6700: add             x0, x0, HEAP, lsl #32
    // 0xbe6704: ldur            x1, [fp, #-0x10]
    // 0xbe6708: stur            x0, [fp, #-8]
    // 0xbe670c: LoadField: r2 = r1->field_f
    //     0xbe670c: ldur            w2, [x1, #0xf]
    // 0xbe6710: DecompressPointer r2
    //     0xbe6710: add             x2, x2, HEAP, lsl #32
    // 0xbe6714: cmp             w2, NULL
    // 0xbe6718: b.eq            #0xbe68b8
    // 0xbe671c: mov             x1, x2
    // 0xbe6720: r0 = of()
    //     0xbe6720: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe6724: LoadField: r1 = r0->field_5b
    //     0xbe6724: ldur            w1, [x0, #0x5b]
    // 0xbe6728: DecompressPointer r1
    //     0xbe6728: add             x1, x1, HEAP, lsl #32
    // 0xbe672c: r16 = 10.000000
    //     0xbe672c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xbe6730: stp             x1, x16, [SP]
    // 0xbe6734: ldur            x1, [fp, #-8]
    // 0xbe6738: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe6738: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe673c: ldr             x4, [x4, #0xaa0]
    // 0xbe6740: r0 = copyWith()
    //     0xbe6740: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe6744: stur            x0, [fp, #-8]
    // 0xbe6748: r0 = Text()
    //     0xbe6748: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe674c: mov             x3, x0
    // 0xbe6750: r0 = " Brand Rating"
    //     0xbe6750: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d78] " Brand Rating"
    //     0xbe6754: ldr             x0, [x0, #0xd78]
    // 0xbe6758: stur            x3, [fp, #-0x10]
    // 0xbe675c: StoreField: r3->field_b = r0
    //     0xbe675c: stur            w0, [x3, #0xb]
    // 0xbe6760: ldur            x0, [fp, #-8]
    // 0xbe6764: StoreField: r3->field_13 = r0
    //     0xbe6764: stur            w0, [x3, #0x13]
    // 0xbe6768: r1 = Null
    //     0xbe6768: mov             x1, NULL
    // 0xbe676c: r2 = 6
    //     0xbe676c: movz            x2, #0x6
    // 0xbe6770: r0 = AllocateArray()
    //     0xbe6770: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe6774: mov             x2, x0
    // 0xbe6778: ldur            x0, [fp, #-0x20]
    // 0xbe677c: stur            x2, [fp, #-8]
    // 0xbe6780: StoreField: r2->field_f = r0
    //     0xbe6780: stur            w0, [x2, #0xf]
    // 0xbe6784: ldur            x0, [fp, #-0x30]
    // 0xbe6788: StoreField: r2->field_13 = r0
    //     0xbe6788: stur            w0, [x2, #0x13]
    // 0xbe678c: ldur            x0, [fp, #-0x10]
    // 0xbe6790: ArrayStore: r2[0] = r0  ; List_4
    //     0xbe6790: stur            w0, [x2, #0x17]
    // 0xbe6794: r1 = <Widget>
    //     0xbe6794: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe6798: r0 = AllocateGrowableArray()
    //     0xbe6798: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe679c: mov             x1, x0
    // 0xbe67a0: ldur            x0, [fp, #-8]
    // 0xbe67a4: stur            x1, [fp, #-0x10]
    // 0xbe67a8: StoreField: r1->field_f = r0
    //     0xbe67a8: stur            w0, [x1, #0xf]
    // 0xbe67ac: r0 = 6
    //     0xbe67ac: movz            x0, #0x6
    // 0xbe67b0: StoreField: r1->field_b = r0
    //     0xbe67b0: stur            w0, [x1, #0xb]
    // 0xbe67b4: r0 = Row()
    //     0xbe67b4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbe67b8: mov             x1, x0
    // 0xbe67bc: r0 = Instance_Axis
    //     0xbe67bc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbe67c0: stur            x1, [fp, #-8]
    // 0xbe67c4: StoreField: r1->field_f = r0
    //     0xbe67c4: stur            w0, [x1, #0xf]
    // 0xbe67c8: r0 = Instance_MainAxisAlignment
    //     0xbe67c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbe67cc: ldr             x0, [x0, #0xa08]
    // 0xbe67d0: StoreField: r1->field_13 = r0
    //     0xbe67d0: stur            w0, [x1, #0x13]
    // 0xbe67d4: r0 = Instance_MainAxisSize
    //     0xbe67d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbe67d8: ldr             x0, [x0, #0xa10]
    // 0xbe67dc: ArrayStore: r1[0] = r0  ; List_4
    //     0xbe67dc: stur            w0, [x1, #0x17]
    // 0xbe67e0: r0 = Instance_CrossAxisAlignment
    //     0xbe67e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbe67e4: ldr             x0, [x0, #0xa18]
    // 0xbe67e8: StoreField: r1->field_1b = r0
    //     0xbe67e8: stur            w0, [x1, #0x1b]
    // 0xbe67ec: r0 = Instance_VerticalDirection
    //     0xbe67ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe67f0: ldr             x0, [x0, #0xa20]
    // 0xbe67f4: StoreField: r1->field_23 = r0
    //     0xbe67f4: stur            w0, [x1, #0x23]
    // 0xbe67f8: r0 = Instance_Clip
    //     0xbe67f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe67fc: ldr             x0, [x0, #0x38]
    // 0xbe6800: StoreField: r1->field_2b = r0
    //     0xbe6800: stur            w0, [x1, #0x2b]
    // 0xbe6804: StoreField: r1->field_2f = rZR
    //     0xbe6804: stur            xzr, [x1, #0x2f]
    // 0xbe6808: ldur            x0, [fp, #-0x10]
    // 0xbe680c: StoreField: r1->field_b = r0
    //     0xbe680c: stur            w0, [x1, #0xb]
    // 0xbe6810: r0 = Visibility()
    //     0xbe6810: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbe6814: ldur            x1, [fp, #-8]
    // 0xbe6818: StoreField: r0->field_b = r1
    //     0xbe6818: stur            w1, [x0, #0xb]
    // 0xbe681c: r1 = Instance_SizedBox
    //     0xbe681c: ldr             x1, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbe6820: StoreField: r0->field_f = r1
    //     0xbe6820: stur            w1, [x0, #0xf]
    // 0xbe6824: ldur            x1, [fp, #-0x18]
    // 0xbe6828: StoreField: r0->field_13 = r1
    //     0xbe6828: stur            w1, [x0, #0x13]
    // 0xbe682c: r1 = false
    //     0xbe682c: add             x1, NULL, #0x30  ; false
    // 0xbe6830: ArrayStore: r0[0] = r1  ; List_4
    //     0xbe6830: stur            w1, [x0, #0x17]
    // 0xbe6834: StoreField: r0->field_1b = r1
    //     0xbe6834: stur            w1, [x0, #0x1b]
    // 0xbe6838: StoreField: r0->field_1f = r1
    //     0xbe6838: stur            w1, [x0, #0x1f]
    // 0xbe683c: StoreField: r0->field_23 = r1
    //     0xbe683c: stur            w1, [x0, #0x23]
    // 0xbe6840: StoreField: r0->field_27 = r1
    //     0xbe6840: stur            w1, [x0, #0x27]
    // 0xbe6844: StoreField: r0->field_2b = r1
    //     0xbe6844: stur            w1, [x0, #0x2b]
    // 0xbe6848: LeaveFrame
    //     0xbe6848: mov             SP, fp
    //     0xbe684c: ldp             fp, lr, [SP], #0x10
    // 0xbe6850: ret
    //     0xbe6850: ret             
    // 0xbe6854: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe6854: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe6858: b               #0xbe5c54
    // 0xbe685c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe685c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe6860: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe6860: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe6864: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe6864: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe6868: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe6868: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe686c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe686c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe6870: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe6870: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe6874: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe6874: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe6878: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe6878: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe687c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe687c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe6880: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe6880: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe6884: SaveReg d0
    //     0xbe6884: str             q0, [SP, #-0x10]!
    // 0xbe6888: r0 = 74
    //     0xbe6888: movz            x0, #0x4a
    // 0xbe688c: r30 = DoubleToIntegerStub
    //     0xbe688c: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xbe6890: LoadField: r30 = r30->field_7
    //     0xbe6890: ldur            lr, [lr, #7]
    // 0xbe6894: blr             lr
    // 0xbe6898: RestoreReg d0
    //     0xbe6898: ldr             q0, [SP], #0x10
    // 0xbe689c: b               #0xbe62c0
    // 0xbe68a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe68a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe68a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe68a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe68a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe68a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe68ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe68ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe68b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe68b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe68b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe68b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe68b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe68b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbe68bc, size: 0x25c
    // 0xbe68bc: EnterFrame
    //     0xbe68bc: stp             fp, lr, [SP, #-0x10]!
    //     0xbe68c0: mov             fp, SP
    // 0xbe68c4: AllocStack(0x98)
    //     0xbe68c4: sub             SP, SP, #0x98
    // 0xbe68c8: SetupParameters()
    //     0xbe68c8: ldr             x0, [fp, #0x10]
    //     0xbe68cc: ldur            w1, [x0, #0x17]
    //     0xbe68d0: add             x1, x1, HEAP, lsl #32
    //     0xbe68d4: stur            x1, [fp, #-0x48]
    // 0xbe68d8: CheckStackOverflow
    //     0xbe68d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe68dc: cmp             SP, x16
    //     0xbe68e0: b.ls            #0xbe6b08
    // 0xbe68e4: LoadField: r2 = r1->field_b
    //     0xbe68e4: ldur            w2, [x1, #0xb]
    // 0xbe68e8: DecompressPointer r2
    //     0xbe68e8: add             x2, x2, HEAP, lsl #32
    // 0xbe68ec: stur            x2, [fp, #-0x40]
    // 0xbe68f0: LoadField: r0 = r2->field_f
    //     0xbe68f0: ldur            w0, [x2, #0xf]
    // 0xbe68f4: DecompressPointer r0
    //     0xbe68f4: add             x0, x0, HEAP, lsl #32
    // 0xbe68f8: LoadField: r3 = r0->field_b
    //     0xbe68f8: ldur            w3, [x0, #0xb]
    // 0xbe68fc: DecompressPointer r3
    //     0xbe68fc: add             x3, x3, HEAP, lsl #32
    // 0xbe6900: stur            x3, [fp, #-0x38]
    // 0xbe6904: cmp             w3, NULL
    // 0xbe6908: b.eq            #0xbe6b10
    // 0xbe690c: LoadField: r0 = r3->field_1b
    //     0xbe690c: ldur            w0, [x3, #0x1b]
    // 0xbe6910: DecompressPointer r0
    //     0xbe6910: add             x0, x0, HEAP, lsl #32
    // 0xbe6914: cmp             w0, NULL
    // 0xbe6918: b.ne            #0xbe6924
    // 0xbe691c: r4 = ""
    //     0xbe691c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe6920: b               #0xbe6928
    // 0xbe6924: mov             x4, x0
    // 0xbe6928: stur            x4, [fp, #-0x30]
    // 0xbe692c: LoadField: r0 = r3->field_13
    //     0xbe692c: ldur            w0, [x3, #0x13]
    // 0xbe6930: DecompressPointer r0
    //     0xbe6930: add             x0, x0, HEAP, lsl #32
    // 0xbe6934: cmp             w0, NULL
    // 0xbe6938: b.ne            #0xbe6944
    // 0xbe693c: r5 = ""
    //     0xbe693c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe6940: b               #0xbe6948
    // 0xbe6944: mov             x5, x0
    // 0xbe6948: stur            x5, [fp, #-0x28]
    // 0xbe694c: LoadField: r0 = r3->field_23
    //     0xbe694c: ldur            w0, [x3, #0x23]
    // 0xbe6950: DecompressPointer r0
    //     0xbe6950: add             x0, x0, HEAP, lsl #32
    // 0xbe6954: cmp             w0, NULL
    // 0xbe6958: b.ne            #0xbe6964
    // 0xbe695c: r6 = ""
    //     0xbe695c: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe6960: b               #0xbe6968
    // 0xbe6964: mov             x6, x0
    // 0xbe6968: stur            x6, [fp, #-0x20]
    // 0xbe696c: LoadField: r7 = r3->field_27
    //     0xbe696c: ldur            w7, [x3, #0x27]
    // 0xbe6970: DecompressPointer r7
    //     0xbe6970: add             x7, x7, HEAP, lsl #32
    // 0xbe6974: stur            x7, [fp, #-0x18]
    // 0xbe6978: LoadField: r0 = r3->field_1f
    //     0xbe6978: ldur            w0, [x3, #0x1f]
    // 0xbe697c: DecompressPointer r0
    //     0xbe697c: add             x0, x0, HEAP, lsl #32
    // 0xbe6980: cmp             w0, NULL
    // 0xbe6984: b.ne            #0xbe6990
    // 0xbe6988: r8 = ""
    //     0xbe6988: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe698c: b               #0xbe6994
    // 0xbe6990: mov             x8, x0
    // 0xbe6994: stur            x8, [fp, #-0x10]
    // 0xbe6998: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbe6998: ldur            w0, [x3, #0x17]
    // 0xbe699c: DecompressPointer r0
    //     0xbe699c: add             x0, x0, HEAP, lsl #32
    // 0xbe69a0: cmp             w0, NULL
    // 0xbe69a4: b.ne            #0xbe69b0
    // 0xbe69a8: r9 = ""
    //     0xbe69a8: ldr             x9, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe69ac: b               #0xbe69b4
    // 0xbe69b0: mov             x9, x0
    // 0xbe69b4: stur            x9, [fp, #-8]
    // 0xbe69b8: LoadField: r0 = r3->field_b
    //     0xbe69b8: ldur            w0, [x3, #0xb]
    // 0xbe69bc: DecompressPointer r0
    //     0xbe69bc: add             x0, x0, HEAP, lsl #32
    // 0xbe69c0: LoadField: r10 = r1->field_f
    //     0xbe69c0: ldur            w10, [x1, #0xf]
    // 0xbe69c4: DecompressPointer r10
    //     0xbe69c4: add             x10, x10, HEAP, lsl #32
    // 0xbe69c8: r11 = LoadClassIdInstr(r0)
    //     0xbe69c8: ldur            x11, [x0, #-1]
    //     0xbe69cc: ubfx            x11, x11, #0xc, #0x14
    // 0xbe69d0: stp             x10, x0, [SP]
    // 0xbe69d4: mov             x0, x11
    // 0xbe69d8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe69d8: sub             lr, x0, #0xb7
    //     0xbe69dc: ldr             lr, [x21, lr, lsl #3]
    //     0xbe69e0: blr             lr
    // 0xbe69e4: cmp             w0, NULL
    // 0xbe69e8: b.ne            #0xbe69f4
    // 0xbe69ec: r0 = Null
    //     0xbe69ec: mov             x0, NULL
    // 0xbe69f0: b               #0xbe6a00
    // 0xbe69f4: LoadField: r1 = r0->field_2b
    //     0xbe69f4: ldur            w1, [x0, #0x2b]
    // 0xbe69f8: DecompressPointer r1
    //     0xbe69f8: add             x1, x1, HEAP, lsl #32
    // 0xbe69fc: mov             x0, x1
    // 0xbe6a00: cmp             w0, NULL
    // 0xbe6a04: b.ne            #0xbe6a10
    // 0xbe6a08: r2 = ""
    //     0xbe6a08: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe6a0c: b               #0xbe6a14
    // 0xbe6a10: mov             x2, x0
    // 0xbe6a14: ldur            x0, [fp, #-0x48]
    // 0xbe6a18: ldur            x1, [fp, #-0x40]
    // 0xbe6a1c: stur            x2, [fp, #-0x50]
    // 0xbe6a20: LoadField: r3 = r1->field_f
    //     0xbe6a20: ldur            w3, [x1, #0xf]
    // 0xbe6a24: DecompressPointer r3
    //     0xbe6a24: add             x3, x3, HEAP, lsl #32
    // 0xbe6a28: LoadField: r1 = r3->field_b
    //     0xbe6a28: ldur            w1, [x3, #0xb]
    // 0xbe6a2c: DecompressPointer r1
    //     0xbe6a2c: add             x1, x1, HEAP, lsl #32
    // 0xbe6a30: cmp             w1, NULL
    // 0xbe6a34: b.eq            #0xbe6b14
    // 0xbe6a38: LoadField: r3 = r1->field_b
    //     0xbe6a38: ldur            w3, [x1, #0xb]
    // 0xbe6a3c: DecompressPointer r3
    //     0xbe6a3c: add             x3, x3, HEAP, lsl #32
    // 0xbe6a40: LoadField: r1 = r0->field_f
    //     0xbe6a40: ldur            w1, [x0, #0xf]
    // 0xbe6a44: DecompressPointer r1
    //     0xbe6a44: add             x1, x1, HEAP, lsl #32
    // 0xbe6a48: r0 = LoadClassIdInstr(r3)
    //     0xbe6a48: ldur            x0, [x3, #-1]
    //     0xbe6a4c: ubfx            x0, x0, #0xc, #0x14
    // 0xbe6a50: stp             x1, x3, [SP]
    // 0xbe6a54: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe6a54: sub             lr, x0, #0xb7
    //     0xbe6a58: ldr             lr, [x21, lr, lsl #3]
    //     0xbe6a5c: blr             lr
    // 0xbe6a60: cmp             w0, NULL
    // 0xbe6a64: b.ne            #0xbe6a70
    // 0xbe6a68: r0 = Null
    //     0xbe6a68: mov             x0, NULL
    // 0xbe6a6c: b               #0xbe6a90
    // 0xbe6a70: LoadField: r1 = r0->field_3b
    //     0xbe6a70: ldur            w1, [x0, #0x3b]
    // 0xbe6a74: DecompressPointer r1
    //     0xbe6a74: add             x1, x1, HEAP, lsl #32
    // 0xbe6a78: cmp             w1, NULL
    // 0xbe6a7c: b.ne            #0xbe6a88
    // 0xbe6a80: r0 = Null
    //     0xbe6a80: mov             x0, NULL
    // 0xbe6a84: b               #0xbe6a90
    // 0xbe6a88: LoadField: r0 = r1->field_b
    //     0xbe6a88: ldur            w0, [x1, #0xb]
    // 0xbe6a8c: DecompressPointer r0
    //     0xbe6a8c: add             x0, x0, HEAP, lsl #32
    // 0xbe6a90: cmp             w0, NULL
    // 0xbe6a94: b.ne            #0xbe6aa0
    // 0xbe6a98: r1 = ""
    //     0xbe6a98: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe6a9c: b               #0xbe6aa4
    // 0xbe6aa0: mov             x1, x0
    // 0xbe6aa4: ldur            x0, [fp, #-0x38]
    // 0xbe6aa8: LoadField: r2 = r0->field_3f
    //     0xbe6aa8: ldur            w2, [x0, #0x3f]
    // 0xbe6aac: DecompressPointer r2
    //     0xbe6aac: add             x2, x2, HEAP, lsl #32
    // 0xbe6ab0: ldur            x16, [fp, #-0x30]
    // 0xbe6ab4: stp             x16, x2, [SP, #0x38]
    // 0xbe6ab8: ldur            x16, [fp, #-0x28]
    // 0xbe6abc: ldur            lr, [fp, #-0x20]
    // 0xbe6ac0: stp             lr, x16, [SP, #0x28]
    // 0xbe6ac4: ldur            x16, [fp, #-0x18]
    // 0xbe6ac8: ldur            lr, [fp, #-0x10]
    // 0xbe6acc: stp             lr, x16, [SP, #0x18]
    // 0xbe6ad0: ldur            x16, [fp, #-8]
    // 0xbe6ad4: ldur            lr, [fp, #-0x50]
    // 0xbe6ad8: stp             lr, x16, [SP, #8]
    // 0xbe6adc: str             x1, [SP]
    // 0xbe6ae0: r4 = 0
    //     0xbe6ae0: movz            x4, #0
    // 0xbe6ae4: ldr             x0, [SP, #0x40]
    // 0xbe6ae8: r16 = UnlinkedCall_0x613b5c
    //     0xbe6ae8: add             x16, PP, #0x61, lsl #12  ; [pp+0x61c88] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe6aec: add             x16, x16, #0xc88
    // 0xbe6af0: ldp             x5, lr, [x16]
    // 0xbe6af4: blr             lr
    // 0xbe6af8: r0 = Null
    //     0xbe6af8: mov             x0, NULL
    // 0xbe6afc: LeaveFrame
    //     0xbe6afc: mov             SP, fp
    //     0xbe6b00: ldp             fp, lr, [SP], #0x10
    // 0xbe6b04: ret
    //     0xbe6b04: ret             
    // 0xbe6b08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe6b08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe6b0c: b               #0xbe68e4
    // 0xbe6b10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe6b10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe6b14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe6b14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, VisibilityInfo) {
    // ** addr: 0xbe6b18, size: 0x2fc
    // 0xbe6b18: EnterFrame
    //     0xbe6b18: stp             fp, lr, [SP, #-0x10]!
    //     0xbe6b1c: mov             fp, SP
    // 0xbe6b20: AllocStack(0x68)
    //     0xbe6b20: sub             SP, SP, #0x68
    // 0xbe6b24: SetupParameters()
    //     0xbe6b24: ldr             x0, [fp, #0x18]
    //     0xbe6b28: ldur            w2, [x0, #0x17]
    //     0xbe6b2c: add             x2, x2, HEAP, lsl #32
    //     0xbe6b30: stur            x2, [fp, #-8]
    // 0xbe6b34: CheckStackOverflow
    //     0xbe6b34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe6b38: cmp             SP, x16
    //     0xbe6b3c: b.ls            #0xbe6df8
    // 0xbe6b40: ldr             x1, [fp, #0x10]
    // 0xbe6b44: r0 = visibleFraction()
    //     0xbe6b44: bl              #0xa4fa10  ; [package:visibility_detector/src/visibility_detector.dart] VisibilityInfo::visibleFraction
    // 0xbe6b48: mov             v1.16b, v0.16b
    // 0xbe6b4c: d0 = 0.500000
    //     0xbe6b4c: fmov            d0, #0.50000000
    // 0xbe6b50: fcmp            d1, d0
    // 0xbe6b54: b.le            #0xbe6de8
    // 0xbe6b58: ldur            x1, [fp, #-8]
    // 0xbe6b5c: LoadField: r2 = r1->field_b
    //     0xbe6b5c: ldur            w2, [x1, #0xb]
    // 0xbe6b60: DecompressPointer r2
    //     0xbe6b60: add             x2, x2, HEAP, lsl #32
    // 0xbe6b64: stur            x2, [fp, #-0x18]
    // 0xbe6b68: LoadField: r0 = r2->field_f
    //     0xbe6b68: ldur            w0, [x2, #0xf]
    // 0xbe6b6c: DecompressPointer r0
    //     0xbe6b6c: add             x0, x0, HEAP, lsl #32
    // 0xbe6b70: LoadField: r3 = r0->field_b
    //     0xbe6b70: ldur            w3, [x0, #0xb]
    // 0xbe6b74: DecompressPointer r3
    //     0xbe6b74: add             x3, x3, HEAP, lsl #32
    // 0xbe6b78: stur            x3, [fp, #-0x10]
    // 0xbe6b7c: cmp             w3, NULL
    // 0xbe6b80: b.eq            #0xbe6e00
    // 0xbe6b84: LoadField: r0 = r3->field_b
    //     0xbe6b84: ldur            w0, [x3, #0xb]
    // 0xbe6b88: DecompressPointer r0
    //     0xbe6b88: add             x0, x0, HEAP, lsl #32
    // 0xbe6b8c: LoadField: r4 = r1->field_f
    //     0xbe6b8c: ldur            w4, [x1, #0xf]
    // 0xbe6b90: DecompressPointer r4
    //     0xbe6b90: add             x4, x4, HEAP, lsl #32
    // 0xbe6b94: r5 = LoadClassIdInstr(r0)
    //     0xbe6b94: ldur            x5, [x0, #-1]
    //     0xbe6b98: ubfx            x5, x5, #0xc, #0x14
    // 0xbe6b9c: stp             x4, x0, [SP]
    // 0xbe6ba0: mov             x0, x5
    // 0xbe6ba4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe6ba4: sub             lr, x0, #0xb7
    //     0xbe6ba8: ldr             lr, [x21, lr, lsl #3]
    //     0xbe6bac: blr             lr
    // 0xbe6bb0: cmp             w0, NULL
    // 0xbe6bb4: b.ne            #0xbe6bc0
    // 0xbe6bb8: r3 = Null
    //     0xbe6bb8: mov             x3, NULL
    // 0xbe6bbc: b               #0xbe6bcc
    // 0xbe6bc0: LoadField: r1 = r0->field_53
    //     0xbe6bc0: ldur            w1, [x0, #0x53]
    // 0xbe6bc4: DecompressPointer r1
    //     0xbe6bc4: add             x1, x1, HEAP, lsl #32
    // 0xbe6bc8: mov             x3, x1
    // 0xbe6bcc: ldur            x1, [fp, #-8]
    // 0xbe6bd0: ldur            x2, [fp, #-0x18]
    // 0xbe6bd4: stur            x3, [fp, #-0x20]
    // 0xbe6bd8: LoadField: r0 = r2->field_f
    //     0xbe6bd8: ldur            w0, [x2, #0xf]
    // 0xbe6bdc: DecompressPointer r0
    //     0xbe6bdc: add             x0, x0, HEAP, lsl #32
    // 0xbe6be0: LoadField: r4 = r0->field_b
    //     0xbe6be0: ldur            w4, [x0, #0xb]
    // 0xbe6be4: DecompressPointer r4
    //     0xbe6be4: add             x4, x4, HEAP, lsl #32
    // 0xbe6be8: cmp             w4, NULL
    // 0xbe6bec: b.eq            #0xbe6e04
    // 0xbe6bf0: LoadField: r0 = r4->field_b
    //     0xbe6bf0: ldur            w0, [x4, #0xb]
    // 0xbe6bf4: DecompressPointer r0
    //     0xbe6bf4: add             x0, x0, HEAP, lsl #32
    // 0xbe6bf8: LoadField: r4 = r1->field_f
    //     0xbe6bf8: ldur            w4, [x1, #0xf]
    // 0xbe6bfc: DecompressPointer r4
    //     0xbe6bfc: add             x4, x4, HEAP, lsl #32
    // 0xbe6c00: r5 = LoadClassIdInstr(r0)
    //     0xbe6c00: ldur            x5, [x0, #-1]
    //     0xbe6c04: ubfx            x5, x5, #0xc, #0x14
    // 0xbe6c08: stp             x4, x0, [SP]
    // 0xbe6c0c: mov             x0, x5
    // 0xbe6c10: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe6c10: sub             lr, x0, #0xb7
    //     0xbe6c14: ldr             lr, [x21, lr, lsl #3]
    //     0xbe6c18: blr             lr
    // 0xbe6c1c: cmp             w0, NULL
    // 0xbe6c20: b.ne            #0xbe6c2c
    // 0xbe6c24: r3 = Null
    //     0xbe6c24: mov             x3, NULL
    // 0xbe6c28: b               #0xbe6c50
    // 0xbe6c2c: LoadField: r1 = r0->field_3b
    //     0xbe6c2c: ldur            w1, [x0, #0x3b]
    // 0xbe6c30: DecompressPointer r1
    //     0xbe6c30: add             x1, x1, HEAP, lsl #32
    // 0xbe6c34: cmp             w1, NULL
    // 0xbe6c38: b.ne            #0xbe6c44
    // 0xbe6c3c: r0 = Null
    //     0xbe6c3c: mov             x0, NULL
    // 0xbe6c40: b               #0xbe6c4c
    // 0xbe6c44: LoadField: r0 = r1->field_b
    //     0xbe6c44: ldur            w0, [x1, #0xb]
    // 0xbe6c48: DecompressPointer r0
    //     0xbe6c48: add             x0, x0, HEAP, lsl #32
    // 0xbe6c4c: mov             x3, x0
    // 0xbe6c50: ldur            x1, [fp, #-8]
    // 0xbe6c54: ldur            x2, [fp, #-0x18]
    // 0xbe6c58: stur            x3, [fp, #-0x28]
    // 0xbe6c5c: LoadField: r0 = r2->field_f
    //     0xbe6c5c: ldur            w0, [x2, #0xf]
    // 0xbe6c60: DecompressPointer r0
    //     0xbe6c60: add             x0, x0, HEAP, lsl #32
    // 0xbe6c64: LoadField: r4 = r0->field_b
    //     0xbe6c64: ldur            w4, [x0, #0xb]
    // 0xbe6c68: DecompressPointer r4
    //     0xbe6c68: add             x4, x4, HEAP, lsl #32
    // 0xbe6c6c: cmp             w4, NULL
    // 0xbe6c70: b.eq            #0xbe6e08
    // 0xbe6c74: LoadField: r0 = r4->field_b
    //     0xbe6c74: ldur            w0, [x4, #0xb]
    // 0xbe6c78: DecompressPointer r0
    //     0xbe6c78: add             x0, x0, HEAP, lsl #32
    // 0xbe6c7c: LoadField: r4 = r1->field_f
    //     0xbe6c7c: ldur            w4, [x1, #0xf]
    // 0xbe6c80: DecompressPointer r4
    //     0xbe6c80: add             x4, x4, HEAP, lsl #32
    // 0xbe6c84: r5 = LoadClassIdInstr(r0)
    //     0xbe6c84: ldur            x5, [x0, #-1]
    //     0xbe6c88: ubfx            x5, x5, #0xc, #0x14
    // 0xbe6c8c: stp             x4, x0, [SP]
    // 0xbe6c90: mov             x0, x5
    // 0xbe6c94: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe6c94: sub             lr, x0, #0xb7
    //     0xbe6c98: ldr             lr, [x21, lr, lsl #3]
    //     0xbe6c9c: blr             lr
    // 0xbe6ca0: cmp             w0, NULL
    // 0xbe6ca4: b.ne            #0xbe6cb0
    // 0xbe6ca8: r3 = Null
    //     0xbe6ca8: mov             x3, NULL
    // 0xbe6cac: b               #0xbe6cbc
    // 0xbe6cb0: LoadField: r1 = r0->field_2b
    //     0xbe6cb0: ldur            w1, [x0, #0x2b]
    // 0xbe6cb4: DecompressPointer r1
    //     0xbe6cb4: add             x1, x1, HEAP, lsl #32
    // 0xbe6cb8: mov             x3, x1
    // 0xbe6cbc: ldur            x1, [fp, #-8]
    // 0xbe6cc0: ldur            x2, [fp, #-0x18]
    // 0xbe6cc4: stur            x3, [fp, #-0x30]
    // 0xbe6cc8: LoadField: r0 = r2->field_f
    //     0xbe6cc8: ldur            w0, [x2, #0xf]
    // 0xbe6ccc: DecompressPointer r0
    //     0xbe6ccc: add             x0, x0, HEAP, lsl #32
    // 0xbe6cd0: LoadField: r4 = r0->field_b
    //     0xbe6cd0: ldur            w4, [x0, #0xb]
    // 0xbe6cd4: DecompressPointer r4
    //     0xbe6cd4: add             x4, x4, HEAP, lsl #32
    // 0xbe6cd8: cmp             w4, NULL
    // 0xbe6cdc: b.eq            #0xbe6e0c
    // 0xbe6ce0: LoadField: r0 = r4->field_b
    //     0xbe6ce0: ldur            w0, [x4, #0xb]
    // 0xbe6ce4: DecompressPointer r0
    //     0xbe6ce4: add             x0, x0, HEAP, lsl #32
    // 0xbe6ce8: LoadField: r4 = r1->field_f
    //     0xbe6ce8: ldur            w4, [x1, #0xf]
    // 0xbe6cec: DecompressPointer r4
    //     0xbe6cec: add             x4, x4, HEAP, lsl #32
    // 0xbe6cf0: r5 = LoadClassIdInstr(r0)
    //     0xbe6cf0: ldur            x5, [x0, #-1]
    //     0xbe6cf4: ubfx            x5, x5, #0xc, #0x14
    // 0xbe6cf8: stp             x4, x0, [SP]
    // 0xbe6cfc: mov             x0, x5
    // 0xbe6d00: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe6d00: sub             lr, x0, #0xb7
    //     0xbe6d04: ldr             lr, [x21, lr, lsl #3]
    //     0xbe6d08: blr             lr
    // 0xbe6d0c: cmp             w0, NULL
    // 0xbe6d10: b.ne            #0xbe6d1c
    // 0xbe6d14: r2 = Null
    //     0xbe6d14: mov             x2, NULL
    // 0xbe6d18: b               #0xbe6d28
    // 0xbe6d1c: LoadField: r1 = r0->field_57
    //     0xbe6d1c: ldur            w1, [x0, #0x57]
    // 0xbe6d20: DecompressPointer r1
    //     0xbe6d20: add             x1, x1, HEAP, lsl #32
    // 0xbe6d24: mov             x2, x1
    // 0xbe6d28: ldur            x0, [fp, #-8]
    // 0xbe6d2c: ldur            x1, [fp, #-0x18]
    // 0xbe6d30: stur            x2, [fp, #-0x38]
    // 0xbe6d34: LoadField: r3 = r1->field_f
    //     0xbe6d34: ldur            w3, [x1, #0xf]
    // 0xbe6d38: DecompressPointer r3
    //     0xbe6d38: add             x3, x3, HEAP, lsl #32
    // 0xbe6d3c: LoadField: r1 = r3->field_b
    //     0xbe6d3c: ldur            w1, [x3, #0xb]
    // 0xbe6d40: DecompressPointer r1
    //     0xbe6d40: add             x1, x1, HEAP, lsl #32
    // 0xbe6d44: cmp             w1, NULL
    // 0xbe6d48: b.eq            #0xbe6e10
    // 0xbe6d4c: LoadField: r3 = r1->field_b
    //     0xbe6d4c: ldur            w3, [x1, #0xb]
    // 0xbe6d50: DecompressPointer r3
    //     0xbe6d50: add             x3, x3, HEAP, lsl #32
    // 0xbe6d54: LoadField: r1 = r0->field_f
    //     0xbe6d54: ldur            w1, [x0, #0xf]
    // 0xbe6d58: DecompressPointer r1
    //     0xbe6d58: add             x1, x1, HEAP, lsl #32
    // 0xbe6d5c: r0 = LoadClassIdInstr(r3)
    //     0xbe6d5c: ldur            x0, [x3, #-1]
    //     0xbe6d60: ubfx            x0, x0, #0xc, #0x14
    // 0xbe6d64: stp             x1, x3, [SP]
    // 0xbe6d68: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe6d68: sub             lr, x0, #0xb7
    //     0xbe6d6c: ldr             lr, [x21, lr, lsl #3]
    //     0xbe6d70: blr             lr
    // 0xbe6d74: cmp             w0, NULL
    // 0xbe6d78: b.ne            #0xbe6d84
    // 0xbe6d7c: r0 = Null
    //     0xbe6d7c: mov             x0, NULL
    // 0xbe6d80: b               #0xbe6d90
    // 0xbe6d84: LoadField: r1 = r0->field_7b
    //     0xbe6d84: ldur            w1, [x0, #0x7b]
    // 0xbe6d88: DecompressPointer r1
    //     0xbe6d88: add             x1, x1, HEAP, lsl #32
    // 0xbe6d8c: mov             x0, x1
    // 0xbe6d90: cmp             w0, NULL
    // 0xbe6d94: b.ne            #0xbe6da4
    // 0xbe6d98: r0 = ProductRating()
    //     0xbe6d98: bl              #0x911a74  ; AllocateProductRatingStub -> ProductRating (size=0x18)
    // 0xbe6d9c: mov             x1, x0
    // 0xbe6da0: b               #0xbe6da8
    // 0xbe6da4: mov             x1, x0
    // 0xbe6da8: ldur            x0, [fp, #-0x10]
    // 0xbe6dac: LoadField: r2 = r0->field_43
    //     0xbe6dac: ldur            w2, [x0, #0x43]
    // 0xbe6db0: DecompressPointer r2
    //     0xbe6db0: add             x2, x2, HEAP, lsl #32
    // 0xbe6db4: ldur            x16, [fp, #-0x20]
    // 0xbe6db8: stp             x16, x2, [SP, #0x20]
    // 0xbe6dbc: ldur            x16, [fp, #-0x28]
    // 0xbe6dc0: ldur            lr, [fp, #-0x30]
    // 0xbe6dc4: stp             lr, x16, [SP, #0x10]
    // 0xbe6dc8: ldur            x16, [fp, #-0x38]
    // 0xbe6dcc: stp             x1, x16, [SP]
    // 0xbe6dd0: r4 = 0
    //     0xbe6dd0: movz            x4, #0
    // 0xbe6dd4: ldr             x0, [SP, #0x28]
    // 0xbe6dd8: r16 = UnlinkedCall_0x613b5c
    //     0xbe6dd8: add             x16, PP, #0x61, lsl #12  ; [pp+0x61c98] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe6ddc: add             x16, x16, #0xc98
    // 0xbe6de0: ldp             x5, lr, [x16]
    // 0xbe6de4: blr             lr
    // 0xbe6de8: r0 = Null
    //     0xbe6de8: mov             x0, NULL
    // 0xbe6dec: LeaveFrame
    //     0xbe6dec: mov             SP, fp
    //     0xbe6df0: ldp             fp, lr, [SP], #0x10
    // 0xbe6df4: ret
    //     0xbe6df4: ret             
    // 0xbe6df8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe6df8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe6dfc: b               #0xbe6b40
    // 0xbe6e00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe6e00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe6e04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe6e04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe6e08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe6e08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe6e0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe6e0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe6e10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe6e10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Stack <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbe6e14, size: 0x2dc
    // 0xbe6e14: EnterFrame
    //     0xbe6e14: stp             fp, lr, [SP, #-0x10]!
    //     0xbe6e18: mov             fp, SP
    // 0xbe6e1c: AllocStack(0x30)
    //     0xbe6e1c: sub             SP, SP, #0x30
    // 0xbe6e20: SetupParameters()
    //     0xbe6e20: ldr             x0, [fp, #0x20]
    //     0xbe6e24: ldur            w1, [x0, #0x17]
    //     0xbe6e28: add             x1, x1, HEAP, lsl #32
    //     0xbe6e2c: stur            x1, [fp, #-0x18]
    // 0xbe6e30: CheckStackOverflow
    //     0xbe6e30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe6e34: cmp             SP, x16
    //     0xbe6e38: b.ls            #0xbe70d4
    // 0xbe6e3c: LoadField: r2 = r1->field_b
    //     0xbe6e3c: ldur            w2, [x1, #0xb]
    // 0xbe6e40: DecompressPointer r2
    //     0xbe6e40: add             x2, x2, HEAP, lsl #32
    // 0xbe6e44: stur            x2, [fp, #-0x10]
    // 0xbe6e48: LoadField: r3 = r2->field_f
    //     0xbe6e48: ldur            w3, [x2, #0xf]
    // 0xbe6e4c: DecompressPointer r3
    //     0xbe6e4c: add             x3, x3, HEAP, lsl #32
    // 0xbe6e50: stur            x3, [fp, #-8]
    // 0xbe6e54: LoadField: r0 = r3->field_b
    //     0xbe6e54: ldur            w0, [x3, #0xb]
    // 0xbe6e58: DecompressPointer r0
    //     0xbe6e58: add             x0, x0, HEAP, lsl #32
    // 0xbe6e5c: cmp             w0, NULL
    // 0xbe6e60: b.eq            #0xbe70dc
    // 0xbe6e64: LoadField: r4 = r0->field_b
    //     0xbe6e64: ldur            w4, [x0, #0xb]
    // 0xbe6e68: DecompressPointer r4
    //     0xbe6e68: add             x4, x4, HEAP, lsl #32
    // 0xbe6e6c: LoadField: r0 = r1->field_f
    //     0xbe6e6c: ldur            w0, [x1, #0xf]
    // 0xbe6e70: DecompressPointer r0
    //     0xbe6e70: add             x0, x0, HEAP, lsl #32
    // 0xbe6e74: r5 = LoadClassIdInstr(r4)
    //     0xbe6e74: ldur            x5, [x4, #-1]
    //     0xbe6e78: ubfx            x5, x5, #0xc, #0x14
    // 0xbe6e7c: stp             x0, x4, [SP]
    // 0xbe6e80: mov             x0, x5
    // 0xbe6e84: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe6e84: sub             lr, x0, #0xb7
    //     0xbe6e88: ldr             lr, [x21, lr, lsl #3]
    //     0xbe6e8c: blr             lr
    // 0xbe6e90: cmp             w0, NULL
    // 0xbe6e94: b.ne            #0xbe6ea0
    // 0xbe6e98: r3 = Null
    //     0xbe6e98: mov             x3, NULL
    // 0xbe6e9c: b               #0xbe6ef0
    // 0xbe6ea0: ldr             x1, [fp, #0x10]
    // 0xbe6ea4: LoadField: r2 = r0->field_37
    //     0xbe6ea4: ldur            w2, [x0, #0x37]
    // 0xbe6ea8: DecompressPointer r2
    //     0xbe6ea8: add             x2, x2, HEAP, lsl #32
    // 0xbe6eac: cmp             w2, NULL
    // 0xbe6eb0: b.eq            #0xbe70e0
    // 0xbe6eb4: LoadField: r0 = r2->field_b
    //     0xbe6eb4: ldur            w0, [x2, #0xb]
    // 0xbe6eb8: r3 = LoadInt32Instr(r1)
    //     0xbe6eb8: sbfx            x3, x1, #1, #0x1f
    //     0xbe6ebc: tbz             w1, #0, #0xbe6ec4
    //     0xbe6ec0: ldur            x3, [x1, #7]
    // 0xbe6ec4: r1 = LoadInt32Instr(r0)
    //     0xbe6ec4: sbfx            x1, x0, #1, #0x1f
    // 0xbe6ec8: mov             x0, x1
    // 0xbe6ecc: mov             x1, x3
    // 0xbe6ed0: cmp             x1, x0
    // 0xbe6ed4: b.hs            #0xbe70e4
    // 0xbe6ed8: LoadField: r0 = r2->field_f
    //     0xbe6ed8: ldur            w0, [x2, #0xf]
    // 0xbe6edc: DecompressPointer r0
    //     0xbe6edc: add             x0, x0, HEAP, lsl #32
    // 0xbe6ee0: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xbe6ee0: add             x16, x0, x3, lsl #2
    //     0xbe6ee4: ldur            w1, [x16, #0xf]
    // 0xbe6ee8: DecompressPointer r1
    //     0xbe6ee8: add             x1, x1, HEAP, lsl #32
    // 0xbe6eec: mov             x3, x1
    // 0xbe6ef0: ldur            x1, [fp, #-0x18]
    // 0xbe6ef4: ldur            x2, [fp, #-0x10]
    // 0xbe6ef8: stur            x3, [fp, #-0x20]
    // 0xbe6efc: LoadField: r0 = r2->field_f
    //     0xbe6efc: ldur            w0, [x2, #0xf]
    // 0xbe6f00: DecompressPointer r0
    //     0xbe6f00: add             x0, x0, HEAP, lsl #32
    // 0xbe6f04: LoadField: r4 = r0->field_b
    //     0xbe6f04: ldur            w4, [x0, #0xb]
    // 0xbe6f08: DecompressPointer r4
    //     0xbe6f08: add             x4, x4, HEAP, lsl #32
    // 0xbe6f0c: cmp             w4, NULL
    // 0xbe6f10: b.eq            #0xbe70e8
    // 0xbe6f14: LoadField: r0 = r4->field_b
    //     0xbe6f14: ldur            w0, [x4, #0xb]
    // 0xbe6f18: DecompressPointer r0
    //     0xbe6f18: add             x0, x0, HEAP, lsl #32
    // 0xbe6f1c: LoadField: r4 = r1->field_f
    //     0xbe6f1c: ldur            w4, [x1, #0xf]
    // 0xbe6f20: DecompressPointer r4
    //     0xbe6f20: add             x4, x4, HEAP, lsl #32
    // 0xbe6f24: r5 = LoadClassIdInstr(r0)
    //     0xbe6f24: ldur            x5, [x0, #-1]
    //     0xbe6f28: ubfx            x5, x5, #0xc, #0x14
    // 0xbe6f2c: stp             x4, x0, [SP]
    // 0xbe6f30: mov             x0, x5
    // 0xbe6f34: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe6f34: sub             lr, x0, #0xb7
    //     0xbe6f38: ldr             lr, [x21, lr, lsl #3]
    //     0xbe6f3c: blr             lr
    // 0xbe6f40: ldur            x1, [fp, #-8]
    // 0xbe6f44: ldur            x2, [fp, #-0x20]
    // 0xbe6f48: mov             x3, x0
    // 0xbe6f4c: r0 = lineThemeSlider()
    //     0xbe6f4c: bl              #0xbe70f0  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_grid_item_view.dart] _ProductGridItemState::lineThemeSlider
    // 0xbe6f50: mov             x1, x0
    // 0xbe6f54: ldur            x0, [fp, #-0x10]
    // 0xbe6f58: stur            x1, [fp, #-8]
    // 0xbe6f5c: LoadField: r2 = r0->field_f
    //     0xbe6f5c: ldur            w2, [x0, #0xf]
    // 0xbe6f60: DecompressPointer r2
    //     0xbe6f60: add             x2, x2, HEAP, lsl #32
    // 0xbe6f64: LoadField: r0 = r2->field_b
    //     0xbe6f64: ldur            w0, [x2, #0xb]
    // 0xbe6f68: DecompressPointer r0
    //     0xbe6f68: add             x0, x0, HEAP, lsl #32
    // 0xbe6f6c: cmp             w0, NULL
    // 0xbe6f70: b.eq            #0xbe70ec
    // 0xbe6f74: LoadField: r2 = r0->field_b
    //     0xbe6f74: ldur            w2, [x0, #0xb]
    // 0xbe6f78: DecompressPointer r2
    //     0xbe6f78: add             x2, x2, HEAP, lsl #32
    // 0xbe6f7c: ldur            x0, [fp, #-0x18]
    // 0xbe6f80: LoadField: r3 = r0->field_f
    //     0xbe6f80: ldur            w3, [x0, #0xf]
    // 0xbe6f84: DecompressPointer r3
    //     0xbe6f84: add             x3, x3, HEAP, lsl #32
    // 0xbe6f88: r0 = LoadClassIdInstr(r2)
    //     0xbe6f88: ldur            x0, [x2, #-1]
    //     0xbe6f8c: ubfx            x0, x0, #0xc, #0x14
    // 0xbe6f90: stp             x3, x2, [SP]
    // 0xbe6f94: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbe6f94: sub             lr, x0, #0xb7
    //     0xbe6f98: ldr             lr, [x21, lr, lsl #3]
    //     0xbe6f9c: blr             lr
    // 0xbe6fa0: cmp             w0, NULL
    // 0xbe6fa4: b.ne            #0xbe6fb0
    // 0xbe6fa8: r0 = Null
    //     0xbe6fa8: mov             x0, NULL
    // 0xbe6fac: b               #0xbe6fbc
    // 0xbe6fb0: LoadField: r1 = r0->field_bb
    //     0xbe6fb0: ldur            w1, [x0, #0xbb]
    // 0xbe6fb4: DecompressPointer r1
    //     0xbe6fb4: add             x1, x1, HEAP, lsl #32
    // 0xbe6fb8: mov             x0, x1
    // 0xbe6fbc: cmp             w0, NULL
    // 0xbe6fc0: b.ne            #0xbe6fcc
    // 0xbe6fc4: r1 = false
    //     0xbe6fc4: add             x1, NULL, #0x30  ; false
    // 0xbe6fc8: b               #0xbe6fd0
    // 0xbe6fcc: mov             x1, x0
    // 0xbe6fd0: ldur            x0, [fp, #-8]
    // 0xbe6fd4: stur            x1, [fp, #-0x10]
    // 0xbe6fd8: r0 = SvgPicture()
    //     0xbe6fd8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbe6fdc: mov             x1, x0
    // 0xbe6fe0: r2 = "assets/images/free-gift-icon.svg"
    //     0xbe6fe0: add             x2, PP, #0x52, lsl #12  ; [pp+0x52d40] "assets/images/free-gift-icon.svg"
    //     0xbe6fe4: ldr             x2, [x2, #0xd40]
    // 0xbe6fe8: stur            x0, [fp, #-0x18]
    // 0xbe6fec: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbe6fec: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbe6ff0: r0 = SvgPicture.asset()
    //     0xbe6ff0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbe6ff4: r0 = Padding()
    //     0xbe6ff4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe6ff8: mov             x1, x0
    // 0xbe6ffc: r0 = Instance_EdgeInsets
    //     0xbe6ffc: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d48] Obj!EdgeInsets@d584c1
    //     0xbe7000: ldr             x0, [x0, #0xd48]
    // 0xbe7004: stur            x1, [fp, #-0x20]
    // 0xbe7008: StoreField: r1->field_f = r0
    //     0xbe7008: stur            w0, [x1, #0xf]
    // 0xbe700c: ldur            x0, [fp, #-0x18]
    // 0xbe7010: StoreField: r1->field_b = r0
    //     0xbe7010: stur            w0, [x1, #0xb]
    // 0xbe7014: r0 = Visibility()
    //     0xbe7014: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbe7018: mov             x3, x0
    // 0xbe701c: ldur            x0, [fp, #-0x20]
    // 0xbe7020: stur            x3, [fp, #-0x18]
    // 0xbe7024: StoreField: r3->field_b = r0
    //     0xbe7024: stur            w0, [x3, #0xb]
    // 0xbe7028: r0 = Instance_SizedBox
    //     0xbe7028: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbe702c: StoreField: r3->field_f = r0
    //     0xbe702c: stur            w0, [x3, #0xf]
    // 0xbe7030: ldur            x0, [fp, #-0x10]
    // 0xbe7034: StoreField: r3->field_13 = r0
    //     0xbe7034: stur            w0, [x3, #0x13]
    // 0xbe7038: r0 = false
    //     0xbe7038: add             x0, NULL, #0x30  ; false
    // 0xbe703c: ArrayStore: r3[0] = r0  ; List_4
    //     0xbe703c: stur            w0, [x3, #0x17]
    // 0xbe7040: StoreField: r3->field_1b = r0
    //     0xbe7040: stur            w0, [x3, #0x1b]
    // 0xbe7044: StoreField: r3->field_1f = r0
    //     0xbe7044: stur            w0, [x3, #0x1f]
    // 0xbe7048: StoreField: r3->field_23 = r0
    //     0xbe7048: stur            w0, [x3, #0x23]
    // 0xbe704c: StoreField: r3->field_27 = r0
    //     0xbe704c: stur            w0, [x3, #0x27]
    // 0xbe7050: StoreField: r3->field_2b = r0
    //     0xbe7050: stur            w0, [x3, #0x2b]
    // 0xbe7054: r1 = Null
    //     0xbe7054: mov             x1, NULL
    // 0xbe7058: r2 = 4
    //     0xbe7058: movz            x2, #0x4
    // 0xbe705c: r0 = AllocateArray()
    //     0xbe705c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe7060: mov             x2, x0
    // 0xbe7064: ldur            x0, [fp, #-8]
    // 0xbe7068: stur            x2, [fp, #-0x10]
    // 0xbe706c: StoreField: r2->field_f = r0
    //     0xbe706c: stur            w0, [x2, #0xf]
    // 0xbe7070: ldur            x0, [fp, #-0x18]
    // 0xbe7074: StoreField: r2->field_13 = r0
    //     0xbe7074: stur            w0, [x2, #0x13]
    // 0xbe7078: r1 = <Widget>
    //     0xbe7078: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe707c: r0 = AllocateGrowableArray()
    //     0xbe707c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe7080: mov             x1, x0
    // 0xbe7084: ldur            x0, [fp, #-0x10]
    // 0xbe7088: stur            x1, [fp, #-8]
    // 0xbe708c: StoreField: r1->field_f = r0
    //     0xbe708c: stur            w0, [x1, #0xf]
    // 0xbe7090: r0 = 4
    //     0xbe7090: movz            x0, #0x4
    // 0xbe7094: StoreField: r1->field_b = r0
    //     0xbe7094: stur            w0, [x1, #0xb]
    // 0xbe7098: r0 = Stack()
    //     0xbe7098: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbe709c: r1 = Instance_Alignment
    //     0xbe709c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xbe70a0: ldr             x1, [x1, #0x950]
    // 0xbe70a4: StoreField: r0->field_f = r1
    //     0xbe70a4: stur            w1, [x0, #0xf]
    // 0xbe70a8: r1 = Instance_StackFit
    //     0xbe70a8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbe70ac: ldr             x1, [x1, #0xfa8]
    // 0xbe70b0: ArrayStore: r0[0] = r1  ; List_4
    //     0xbe70b0: stur            w1, [x0, #0x17]
    // 0xbe70b4: r1 = Instance_Clip
    //     0xbe70b4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbe70b8: ldr             x1, [x1, #0x7e0]
    // 0xbe70bc: StoreField: r0->field_1b = r1
    //     0xbe70bc: stur            w1, [x0, #0x1b]
    // 0xbe70c0: ldur            x1, [fp, #-8]
    // 0xbe70c4: StoreField: r0->field_b = r1
    //     0xbe70c4: stur            w1, [x0, #0xb]
    // 0xbe70c8: LeaveFrame
    //     0xbe70c8: mov             SP, fp
    //     0xbe70cc: ldp             fp, lr, [SP], #0x10
    // 0xbe70d0: ret
    //     0xbe70d0: ret             
    // 0xbe70d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe70d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe70d8: b               #0xbe6e3c
    // 0xbe70dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe70dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe70e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe70e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe70e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbe70e4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbe70e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe70e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe70ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe70ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ lineThemeSlider(/* No info */) {
    // ** addr: 0xbe70f0, size: 0xf28
    // 0xbe70f0: EnterFrame
    //     0xbe70f0: stp             fp, lr, [SP, #-0x10]!
    //     0xbe70f4: mov             fp, SP
    // 0xbe70f8: AllocStack(0x90)
    //     0xbe70f8: sub             SP, SP, #0x90
    // 0xbe70fc: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xbe70fc: stur            x1, [fp, #-8]
    //     0xbe7100: stur            x2, [fp, #-0x10]
    //     0xbe7104: stur            x3, [fp, #-0x18]
    // 0xbe7108: CheckStackOverflow
    //     0xbe7108: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe710c: cmp             SP, x16
    //     0xbe7110: b.ls            #0xbe7fd0
    // 0xbe7114: r1 = 2
    //     0xbe7114: movz            x1, #0x2
    // 0xbe7118: r0 = AllocateContext()
    //     0xbe7118: bl              #0x16f6108  ; AllocateContextStub
    // 0xbe711c: mov             x1, x0
    // 0xbe7120: ldur            x0, [fp, #-8]
    // 0xbe7124: stur            x1, [fp, #-0x20]
    // 0xbe7128: StoreField: r1->field_f = r0
    //     0xbe7128: stur            w0, [x1, #0xf]
    // 0xbe712c: ldur            x2, [fp, #-0x18]
    // 0xbe7130: StoreField: r1->field_13 = r2
    //     0xbe7130: stur            w2, [x1, #0x13]
    // 0xbe7134: ldur            x2, [fp, #-0x10]
    // 0xbe7138: cmp             w2, NULL
    // 0xbe713c: b.eq            #0xbe7fd8
    // 0xbe7140: LoadField: r3 = r2->field_b
    //     0xbe7140: ldur            w3, [x2, #0xb]
    // 0xbe7144: DecompressPointer r3
    //     0xbe7144: add             x3, x3, HEAP, lsl #32
    // 0xbe7148: stur            x3, [fp, #-0x18]
    // 0xbe714c: cmp             w3, NULL
    // 0xbe7150: b.eq            #0xbe7fdc
    // 0xbe7154: r0 = ImageHeaders.forImages()
    //     0xbe7154: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbe7158: r1 = Function '<anonymous closure>':.
    //     0xbe7158: add             x1, PP, #0x61, lsl #12  ; [pp+0x61ca8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbe715c: ldr             x1, [x1, #0xca8]
    // 0xbe7160: r2 = Null
    //     0xbe7160: mov             x2, NULL
    // 0xbe7164: stur            x0, [fp, #-0x10]
    // 0xbe7168: r0 = AllocateClosure()
    //     0xbe7168: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe716c: r1 = Function '<anonymous closure>':.
    //     0xbe716c: add             x1, PP, #0x61, lsl #12  ; [pp+0x61cb0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbe7170: ldr             x1, [x1, #0xcb0]
    // 0xbe7174: r2 = Null
    //     0xbe7174: mov             x2, NULL
    // 0xbe7178: stur            x0, [fp, #-0x28]
    // 0xbe717c: r0 = AllocateClosure()
    //     0xbe717c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe7180: stur            x0, [fp, #-0x30]
    // 0xbe7184: r0 = CachedNetworkImage()
    //     0xbe7184: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbe7188: stur            x0, [fp, #-0x38]
    // 0xbe718c: ldur            x16, [fp, #-0x10]
    // 0xbe7190: r30 = Instance_BoxFit
    //     0xbe7190: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbe7194: ldr             lr, [lr, #0x118]
    // 0xbe7198: stp             lr, x16, [SP, #0x10]
    // 0xbe719c: ldur            x16, [fp, #-0x28]
    // 0xbe71a0: ldur            lr, [fp, #-0x30]
    // 0xbe71a4: stp             lr, x16, [SP]
    // 0xbe71a8: mov             x1, x0
    // 0xbe71ac: ldur            x2, [fp, #-0x18]
    // 0xbe71b0: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x3, httpHeaders, 0x2, progressIndicatorBuilder, 0x4, null]
    //     0xbe71b0: add             x4, PP, #0x52, lsl #12  ; [pp+0x52828] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x3, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x4, Null]
    //     0xbe71b4: ldr             x4, [x4, #0x828]
    // 0xbe71b8: r0 = CachedNetworkImage()
    //     0xbe71b8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbe71bc: r0 = Center()
    //     0xbe71bc: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbe71c0: mov             x1, x0
    // 0xbe71c4: r0 = Instance_Alignment
    //     0xbe71c4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbe71c8: ldr             x0, [x0, #0xb10]
    // 0xbe71cc: stur            x1, [fp, #-0x30]
    // 0xbe71d0: StoreField: r1->field_f = r0
    //     0xbe71d0: stur            w0, [x1, #0xf]
    // 0xbe71d4: ldur            x0, [fp, #-0x38]
    // 0xbe71d8: StoreField: r1->field_b = r0
    //     0xbe71d8: stur            w0, [x1, #0xb]
    // 0xbe71dc: ldur            x2, [fp, #-0x20]
    // 0xbe71e0: LoadField: r0 = r2->field_13
    //     0xbe71e0: ldur            w0, [x2, #0x13]
    // 0xbe71e4: DecompressPointer r0
    //     0xbe71e4: add             x0, x0, HEAP, lsl #32
    // 0xbe71e8: stur            x0, [fp, #-0x28]
    // 0xbe71ec: cmp             w0, NULL
    // 0xbe71f0: b.ne            #0xbe71fc
    // 0xbe71f4: r3 = Null
    //     0xbe71f4: mov             x3, NULL
    // 0xbe71f8: b               #0xbe7228
    // 0xbe71fc: LoadField: r3 = r0->field_7f
    //     0xbe71fc: ldur            w3, [x0, #0x7f]
    // 0xbe7200: DecompressPointer r3
    //     0xbe7200: add             x3, x3, HEAP, lsl #32
    // 0xbe7204: cmp             w3, NULL
    // 0xbe7208: b.ne            #0xbe7214
    // 0xbe720c: r3 = Null
    //     0xbe720c: mov             x3, NULL
    // 0xbe7210: b               #0xbe7228
    // 0xbe7214: LoadField: r4 = r3->field_7
    //     0xbe7214: ldur            w4, [x3, #7]
    // 0xbe7218: cbnz            w4, #0xbe7224
    // 0xbe721c: r3 = false
    //     0xbe721c: add             x3, NULL, #0x30  ; false
    // 0xbe7220: b               #0xbe7228
    // 0xbe7224: r3 = true
    //     0xbe7224: add             x3, NULL, #0x20  ; true
    // 0xbe7228: cmp             w3, NULL
    // 0xbe722c: b.ne            #0xbe7234
    // 0xbe7230: r3 = false
    //     0xbe7230: add             x3, NULL, #0x30  ; false
    // 0xbe7234: stur            x3, [fp, #-0x18]
    // 0xbe7238: cmp             w0, NULL
    // 0xbe723c: b.ne            #0xbe7248
    // 0xbe7240: r4 = Null
    //     0xbe7240: mov             x4, NULL
    // 0xbe7244: b               #0xbe7250
    // 0xbe7248: LoadField: r4 = r0->field_93
    //     0xbe7248: ldur            w4, [x0, #0x93]
    // 0xbe724c: DecompressPointer r4
    //     0xbe724c: add             x4, x4, HEAP, lsl #32
    // 0xbe7250: cmp             w4, NULL
    // 0xbe7254: b.ne            #0xbe727c
    // 0xbe7258: mov             x5, x2
    // 0xbe725c: r3 = Instance_EdgeInsets
    //     0xbe725c: add             x3, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xbe7260: ldr             x3, [x3, #0xe50]
    // 0xbe7264: r0 = Instance_Alignment
    //     0xbe7264: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xbe7268: ldr             x0, [x0, #0xfa0]
    // 0xbe726c: r2 = 4
    //     0xbe726c: movz            x2, #0x4
    // 0xbe7270: d0 = 0.700000
    //     0xbe7270: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbe7274: ldr             d0, [x17, #0xf48]
    // 0xbe7278: b               #0xbe778c
    // 0xbe727c: tbnz            w4, #4, #0xbe776c
    // 0xbe7280: ldur            x4, [fp, #-8]
    // 0xbe7284: LoadField: r5 = r4->field_b
    //     0xbe7284: ldur            w5, [x4, #0xb]
    // 0xbe7288: DecompressPointer r5
    //     0xbe7288: add             x5, x5, HEAP, lsl #32
    // 0xbe728c: cmp             w5, NULL
    // 0xbe7290: b.eq            #0xbe7fe0
    // 0xbe7294: LoadField: r6 = r5->field_37
    //     0xbe7294: ldur            w6, [x5, #0x37]
    // 0xbe7298: DecompressPointer r6
    //     0xbe7298: add             x6, x6, HEAP, lsl #32
    // 0xbe729c: stur            x6, [fp, #-0x10]
    // 0xbe72a0: cmp             w6, NULL
    // 0xbe72a4: b.ne            #0xbe72b0
    // 0xbe72a8: r5 = Null
    //     0xbe72a8: mov             x5, NULL
    // 0xbe72ac: b               #0xbe72d4
    // 0xbe72b0: LoadField: r5 = r6->field_13
    //     0xbe72b0: ldur            w5, [x6, #0x13]
    // 0xbe72b4: DecompressPointer r5
    //     0xbe72b4: add             x5, x5, HEAP, lsl #32
    // 0xbe72b8: cmp             w5, NULL
    // 0xbe72bc: b.ne            #0xbe72c8
    // 0xbe72c0: r5 = Null
    //     0xbe72c0: mov             x5, NULL
    // 0xbe72c4: b               #0xbe72d4
    // 0xbe72c8: LoadField: r7 = r5->field_7
    //     0xbe72c8: ldur            w7, [x5, #7]
    // 0xbe72cc: DecompressPointer r7
    //     0xbe72cc: add             x7, x7, HEAP, lsl #32
    // 0xbe72d0: mov             x5, x7
    // 0xbe72d4: cmp             w5, NULL
    // 0xbe72d8: b.ne            #0xbe72e4
    // 0xbe72dc: r5 = 0
    //     0xbe72dc: movz            x5, #0
    // 0xbe72e0: b               #0xbe72f4
    // 0xbe72e4: r7 = LoadInt32Instr(r5)
    //     0xbe72e4: sbfx            x7, x5, #1, #0x1f
    //     0xbe72e8: tbz             w5, #0, #0xbe72f0
    //     0xbe72ec: ldur            x7, [x5, #7]
    // 0xbe72f0: mov             x5, x7
    // 0xbe72f4: stur            x5, [fp, #-0x50]
    // 0xbe72f8: cmp             w6, NULL
    // 0xbe72fc: b.ne            #0xbe7308
    // 0xbe7300: r7 = Null
    //     0xbe7300: mov             x7, NULL
    // 0xbe7304: b               #0xbe732c
    // 0xbe7308: LoadField: r7 = r6->field_13
    //     0xbe7308: ldur            w7, [x6, #0x13]
    // 0xbe730c: DecompressPointer r7
    //     0xbe730c: add             x7, x7, HEAP, lsl #32
    // 0xbe7310: cmp             w7, NULL
    // 0xbe7314: b.ne            #0xbe7320
    // 0xbe7318: r7 = Null
    //     0xbe7318: mov             x7, NULL
    // 0xbe731c: b               #0xbe732c
    // 0xbe7320: LoadField: r8 = r7->field_b
    //     0xbe7320: ldur            w8, [x7, #0xb]
    // 0xbe7324: DecompressPointer r8
    //     0xbe7324: add             x8, x8, HEAP, lsl #32
    // 0xbe7328: mov             x7, x8
    // 0xbe732c: cmp             w7, NULL
    // 0xbe7330: b.ne            #0xbe733c
    // 0xbe7334: r7 = 0
    //     0xbe7334: movz            x7, #0
    // 0xbe7338: b               #0xbe734c
    // 0xbe733c: r8 = LoadInt32Instr(r7)
    //     0xbe733c: sbfx            x8, x7, #1, #0x1f
    //     0xbe7340: tbz             w7, #0, #0xbe7348
    //     0xbe7344: ldur            x8, [x7, #7]
    // 0xbe7348: mov             x7, x8
    // 0xbe734c: stur            x7, [fp, #-0x48]
    // 0xbe7350: cmp             w6, NULL
    // 0xbe7354: b.ne            #0xbe7360
    // 0xbe7358: r8 = Null
    //     0xbe7358: mov             x8, NULL
    // 0xbe735c: b               #0xbe7384
    // 0xbe7360: LoadField: r8 = r6->field_13
    //     0xbe7360: ldur            w8, [x6, #0x13]
    // 0xbe7364: DecompressPointer r8
    //     0xbe7364: add             x8, x8, HEAP, lsl #32
    // 0xbe7368: cmp             w8, NULL
    // 0xbe736c: b.ne            #0xbe7378
    // 0xbe7370: r8 = Null
    //     0xbe7370: mov             x8, NULL
    // 0xbe7374: b               #0xbe7384
    // 0xbe7378: LoadField: r9 = r8->field_f
    //     0xbe7378: ldur            w9, [x8, #0xf]
    // 0xbe737c: DecompressPointer r9
    //     0xbe737c: add             x9, x9, HEAP, lsl #32
    // 0xbe7380: mov             x8, x9
    // 0xbe7384: cmp             w8, NULL
    // 0xbe7388: b.ne            #0xbe7394
    // 0xbe738c: r8 = 0
    //     0xbe738c: movz            x8, #0
    // 0xbe7390: b               #0xbe73a4
    // 0xbe7394: r9 = LoadInt32Instr(r8)
    //     0xbe7394: sbfx            x9, x8, #1, #0x1f
    //     0xbe7398: tbz             w8, #0, #0xbe73a0
    //     0xbe739c: ldur            x9, [x8, #7]
    // 0xbe73a0: mov             x8, x9
    // 0xbe73a4: stur            x8, [fp, #-0x40]
    // 0xbe73a8: r0 = Color()
    //     0xbe73a8: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbe73ac: mov             x1, x0
    // 0xbe73b0: r0 = Instance_ColorSpace
    //     0xbe73b0: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbe73b4: stur            x1, [fp, #-0x38]
    // 0xbe73b8: StoreField: r1->field_27 = r0
    //     0xbe73b8: stur            w0, [x1, #0x27]
    // 0xbe73bc: d0 = 1.000000
    //     0xbe73bc: fmov            d0, #1.00000000
    // 0xbe73c0: StoreField: r1->field_7 = d0
    //     0xbe73c0: stur            d0, [x1, #7]
    // 0xbe73c4: ldur            x2, [fp, #-0x50]
    // 0xbe73c8: ubfx            x2, x2, #0, #0x20
    // 0xbe73cc: and             w3, w2, #0xff
    // 0xbe73d0: ubfx            x3, x3, #0, #0x20
    // 0xbe73d4: scvtf           d0, x3
    // 0xbe73d8: d1 = 255.000000
    //     0xbe73d8: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbe73dc: fdiv            d2, d0, d1
    // 0xbe73e0: StoreField: r1->field_f = d2
    //     0xbe73e0: stur            d2, [x1, #0xf]
    // 0xbe73e4: ldur            x2, [fp, #-0x48]
    // 0xbe73e8: ubfx            x2, x2, #0, #0x20
    // 0xbe73ec: and             w3, w2, #0xff
    // 0xbe73f0: ubfx            x3, x3, #0, #0x20
    // 0xbe73f4: scvtf           d0, x3
    // 0xbe73f8: fdiv            d2, d0, d1
    // 0xbe73fc: ArrayStore: r1[0] = d2  ; List_8
    //     0xbe73fc: stur            d2, [x1, #0x17]
    // 0xbe7400: ldur            x2, [fp, #-0x40]
    // 0xbe7404: ubfx            x2, x2, #0, #0x20
    // 0xbe7408: and             w3, w2, #0xff
    // 0xbe740c: ubfx            x3, x3, #0, #0x20
    // 0xbe7410: scvtf           d0, x3
    // 0xbe7414: fdiv            d2, d0, d1
    // 0xbe7418: StoreField: r1->field_1f = d2
    //     0xbe7418: stur            d2, [x1, #0x1f]
    // 0xbe741c: ldur            x2, [fp, #-0x10]
    // 0xbe7420: cmp             w2, NULL
    // 0xbe7424: b.ne            #0xbe7430
    // 0xbe7428: r3 = Null
    //     0xbe7428: mov             x3, NULL
    // 0xbe742c: b               #0xbe7454
    // 0xbe7430: LoadField: r3 = r2->field_13
    //     0xbe7430: ldur            w3, [x2, #0x13]
    // 0xbe7434: DecompressPointer r3
    //     0xbe7434: add             x3, x3, HEAP, lsl #32
    // 0xbe7438: cmp             w3, NULL
    // 0xbe743c: b.ne            #0xbe7448
    // 0xbe7440: r3 = Null
    //     0xbe7440: mov             x3, NULL
    // 0xbe7444: b               #0xbe7454
    // 0xbe7448: LoadField: r4 = r3->field_7
    //     0xbe7448: ldur            w4, [x3, #7]
    // 0xbe744c: DecompressPointer r4
    //     0xbe744c: add             x4, x4, HEAP, lsl #32
    // 0xbe7450: mov             x3, x4
    // 0xbe7454: cmp             w3, NULL
    // 0xbe7458: b.ne            #0xbe7464
    // 0xbe745c: r3 = 0
    //     0xbe745c: movz            x3, #0
    // 0xbe7460: b               #0xbe7474
    // 0xbe7464: r4 = LoadInt32Instr(r3)
    //     0xbe7464: sbfx            x4, x3, #1, #0x1f
    //     0xbe7468: tbz             w3, #0, #0xbe7470
    //     0xbe746c: ldur            x4, [x3, #7]
    // 0xbe7470: mov             x3, x4
    // 0xbe7474: stur            x3, [fp, #-0x50]
    // 0xbe7478: cmp             w2, NULL
    // 0xbe747c: b.ne            #0xbe7488
    // 0xbe7480: r4 = Null
    //     0xbe7480: mov             x4, NULL
    // 0xbe7484: b               #0xbe74ac
    // 0xbe7488: LoadField: r4 = r2->field_13
    //     0xbe7488: ldur            w4, [x2, #0x13]
    // 0xbe748c: DecompressPointer r4
    //     0xbe748c: add             x4, x4, HEAP, lsl #32
    // 0xbe7490: cmp             w4, NULL
    // 0xbe7494: b.ne            #0xbe74a0
    // 0xbe7498: r4 = Null
    //     0xbe7498: mov             x4, NULL
    // 0xbe749c: b               #0xbe74ac
    // 0xbe74a0: LoadField: r5 = r4->field_b
    //     0xbe74a0: ldur            w5, [x4, #0xb]
    // 0xbe74a4: DecompressPointer r5
    //     0xbe74a4: add             x5, x5, HEAP, lsl #32
    // 0xbe74a8: mov             x4, x5
    // 0xbe74ac: cmp             w4, NULL
    // 0xbe74b0: b.ne            #0xbe74bc
    // 0xbe74b4: r4 = 0
    //     0xbe74b4: movz            x4, #0
    // 0xbe74b8: b               #0xbe74cc
    // 0xbe74bc: r5 = LoadInt32Instr(r4)
    //     0xbe74bc: sbfx            x5, x4, #1, #0x1f
    //     0xbe74c0: tbz             w4, #0, #0xbe74c8
    //     0xbe74c4: ldur            x5, [x4, #7]
    // 0xbe74c8: mov             x4, x5
    // 0xbe74cc: stur            x4, [fp, #-0x48]
    // 0xbe74d0: cmp             w2, NULL
    // 0xbe74d4: b.ne            #0xbe74e0
    // 0xbe74d8: r2 = Null
    //     0xbe74d8: mov             x2, NULL
    // 0xbe74dc: b               #0xbe7500
    // 0xbe74e0: LoadField: r5 = r2->field_13
    //     0xbe74e0: ldur            w5, [x2, #0x13]
    // 0xbe74e4: DecompressPointer r5
    //     0xbe74e4: add             x5, x5, HEAP, lsl #32
    // 0xbe74e8: cmp             w5, NULL
    // 0xbe74ec: b.ne            #0xbe74f8
    // 0xbe74f0: r2 = Null
    //     0xbe74f0: mov             x2, NULL
    // 0xbe74f4: b               #0xbe7500
    // 0xbe74f8: LoadField: r2 = r5->field_f
    //     0xbe74f8: ldur            w2, [x5, #0xf]
    // 0xbe74fc: DecompressPointer r2
    //     0xbe74fc: add             x2, x2, HEAP, lsl #32
    // 0xbe7500: cmp             w2, NULL
    // 0xbe7504: b.ne            #0xbe7510
    // 0xbe7508: r5 = 0
    //     0xbe7508: movz            x5, #0
    // 0xbe750c: b               #0xbe751c
    // 0xbe7510: r5 = LoadInt32Instr(r2)
    //     0xbe7510: sbfx            x5, x2, #1, #0x1f
    //     0xbe7514: tbz             w2, #0, #0xbe751c
    //     0xbe7518: ldur            x5, [x2, #7]
    // 0xbe751c: ldur            x2, [fp, #-0x28]
    // 0xbe7520: stur            x5, [fp, #-0x40]
    // 0xbe7524: r0 = Color()
    //     0xbe7524: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbe7528: mov             x3, x0
    // 0xbe752c: r0 = Instance_ColorSpace
    //     0xbe752c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbe7530: stur            x3, [fp, #-0x10]
    // 0xbe7534: StoreField: r3->field_27 = r0
    //     0xbe7534: stur            w0, [x3, #0x27]
    // 0xbe7538: d0 = 0.700000
    //     0xbe7538: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbe753c: ldr             d0, [x17, #0xf48]
    // 0xbe7540: StoreField: r3->field_7 = d0
    //     0xbe7540: stur            d0, [x3, #7]
    // 0xbe7544: ldur            x0, [fp, #-0x50]
    // 0xbe7548: ubfx            x0, x0, #0, #0x20
    // 0xbe754c: and             w1, w0, #0xff
    // 0xbe7550: ubfx            x1, x1, #0, #0x20
    // 0xbe7554: scvtf           d0, x1
    // 0xbe7558: d1 = 255.000000
    //     0xbe7558: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbe755c: fdiv            d2, d0, d1
    // 0xbe7560: StoreField: r3->field_f = d2
    //     0xbe7560: stur            d2, [x3, #0xf]
    // 0xbe7564: ldur            x0, [fp, #-0x48]
    // 0xbe7568: ubfx            x0, x0, #0, #0x20
    // 0xbe756c: and             w1, w0, #0xff
    // 0xbe7570: ubfx            x1, x1, #0, #0x20
    // 0xbe7574: scvtf           d0, x1
    // 0xbe7578: fdiv            d2, d0, d1
    // 0xbe757c: ArrayStore: r3[0] = d2  ; List_8
    //     0xbe757c: stur            d2, [x3, #0x17]
    // 0xbe7580: ldur            x0, [fp, #-0x40]
    // 0xbe7584: ubfx            x0, x0, #0, #0x20
    // 0xbe7588: and             w1, w0, #0xff
    // 0xbe758c: ubfx            x1, x1, #0, #0x20
    // 0xbe7590: scvtf           d0, x1
    // 0xbe7594: fdiv            d2, d0, d1
    // 0xbe7598: StoreField: r3->field_1f = d2
    //     0xbe7598: stur            d2, [x3, #0x1f]
    // 0xbe759c: r1 = Null
    //     0xbe759c: mov             x1, NULL
    // 0xbe75a0: r2 = 4
    //     0xbe75a0: movz            x2, #0x4
    // 0xbe75a4: r0 = AllocateArray()
    //     0xbe75a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe75a8: mov             x2, x0
    // 0xbe75ac: ldur            x0, [fp, #-0x38]
    // 0xbe75b0: stur            x2, [fp, #-0x58]
    // 0xbe75b4: StoreField: r2->field_f = r0
    //     0xbe75b4: stur            w0, [x2, #0xf]
    // 0xbe75b8: ldur            x0, [fp, #-0x10]
    // 0xbe75bc: StoreField: r2->field_13 = r0
    //     0xbe75bc: stur            w0, [x2, #0x13]
    // 0xbe75c0: r1 = <Color>
    //     0xbe75c0: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbe75c4: ldr             x1, [x1, #0xf80]
    // 0xbe75c8: r0 = AllocateGrowableArray()
    //     0xbe75c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe75cc: mov             x1, x0
    // 0xbe75d0: ldur            x0, [fp, #-0x58]
    // 0xbe75d4: stur            x1, [fp, #-0x10]
    // 0xbe75d8: StoreField: r1->field_f = r0
    //     0xbe75d8: stur            w0, [x1, #0xf]
    // 0xbe75dc: r2 = 4
    //     0xbe75dc: movz            x2, #0x4
    // 0xbe75e0: StoreField: r1->field_b = r2
    //     0xbe75e0: stur            w2, [x1, #0xb]
    // 0xbe75e4: r0 = LinearGradient()
    //     0xbe75e4: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xbe75e8: mov             x1, x0
    // 0xbe75ec: r0 = Instance_Alignment
    //     0xbe75ec: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xbe75f0: ldr             x0, [x0, #0xce0]
    // 0xbe75f4: stur            x1, [fp, #-0x38]
    // 0xbe75f8: StoreField: r1->field_13 = r0
    //     0xbe75f8: stur            w0, [x1, #0x13]
    // 0xbe75fc: r0 = Instance_Alignment
    //     0xbe75fc: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xbe7600: ldr             x0, [x0, #0xce8]
    // 0xbe7604: ArrayStore: r1[0] = r0  ; List_4
    //     0xbe7604: stur            w0, [x1, #0x17]
    // 0xbe7608: r0 = Instance_TileMode
    //     0xbe7608: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xbe760c: ldr             x0, [x0, #0xcf0]
    // 0xbe7610: StoreField: r1->field_1b = r0
    //     0xbe7610: stur            w0, [x1, #0x1b]
    // 0xbe7614: ldur            x0, [fp, #-0x10]
    // 0xbe7618: StoreField: r1->field_7 = r0
    //     0xbe7618: stur            w0, [x1, #7]
    // 0xbe761c: r0 = BoxDecoration()
    //     0xbe761c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbe7620: mov             x2, x0
    // 0xbe7624: ldur            x0, [fp, #-0x38]
    // 0xbe7628: stur            x2, [fp, #-0x58]
    // 0xbe762c: StoreField: r2->field_1b = r0
    //     0xbe762c: stur            w0, [x2, #0x1b]
    // 0xbe7630: r0 = Instance_BoxShape
    //     0xbe7630: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbe7634: ldr             x0, [x0, #0x80]
    // 0xbe7638: StoreField: r2->field_23 = r0
    //     0xbe7638: stur            w0, [x2, #0x23]
    // 0xbe763c: ldur            x1, [fp, #-0x28]
    // 0xbe7640: cmp             w1, NULL
    // 0xbe7644: b.ne            #0xbe7650
    // 0xbe7648: r1 = Null
    //     0xbe7648: mov             x1, NULL
    // 0xbe764c: b               #0xbe765c
    // 0xbe7650: LoadField: r3 = r1->field_7f
    //     0xbe7650: ldur            w3, [x1, #0x7f]
    // 0xbe7654: DecompressPointer r3
    //     0xbe7654: add             x3, x3, HEAP, lsl #32
    // 0xbe7658: mov             x1, x3
    // 0xbe765c: cmp             w1, NULL
    // 0xbe7660: b.ne            #0xbe766c
    // 0xbe7664: r4 = ""
    //     0xbe7664: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe7668: b               #0xbe7670
    // 0xbe766c: mov             x4, x1
    // 0xbe7670: ldur            x3, [fp, #-8]
    // 0xbe7674: stur            x4, [fp, #-0x10]
    // 0xbe7678: LoadField: r1 = r3->field_f
    //     0xbe7678: ldur            w1, [x3, #0xf]
    // 0xbe767c: DecompressPointer r1
    //     0xbe767c: add             x1, x1, HEAP, lsl #32
    // 0xbe7680: cmp             w1, NULL
    // 0xbe7684: b.eq            #0xbe7fe4
    // 0xbe7688: r0 = of()
    //     0xbe7688: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe768c: LoadField: r1 = r0->field_87
    //     0xbe768c: ldur            w1, [x0, #0x87]
    // 0xbe7690: DecompressPointer r1
    //     0xbe7690: add             x1, x1, HEAP, lsl #32
    // 0xbe7694: LoadField: r0 = r1->field_7
    //     0xbe7694: ldur            w0, [x1, #7]
    // 0xbe7698: DecompressPointer r0
    //     0xbe7698: add             x0, x0, HEAP, lsl #32
    // 0xbe769c: r16 = 12.000000
    //     0xbe769c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe76a0: ldr             x16, [x16, #0x9e8]
    // 0xbe76a4: r30 = Instance_Color
    //     0xbe76a4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbe76a8: stp             lr, x16, [SP]
    // 0xbe76ac: mov             x1, x0
    // 0xbe76b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe76b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe76b4: ldr             x4, [x4, #0xaa0]
    // 0xbe76b8: r0 = copyWith()
    //     0xbe76b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe76bc: stur            x0, [fp, #-0x28]
    // 0xbe76c0: r0 = Text()
    //     0xbe76c0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe76c4: mov             x1, x0
    // 0xbe76c8: ldur            x0, [fp, #-0x10]
    // 0xbe76cc: stur            x1, [fp, #-0x38]
    // 0xbe76d0: StoreField: r1->field_b = r0
    //     0xbe76d0: stur            w0, [x1, #0xb]
    // 0xbe76d4: ldur            x0, [fp, #-0x28]
    // 0xbe76d8: StoreField: r1->field_13 = r0
    //     0xbe76d8: stur            w0, [x1, #0x13]
    // 0xbe76dc: r0 = Padding()
    //     0xbe76dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe76e0: mov             x1, x0
    // 0xbe76e4: r0 = Instance_EdgeInsets
    //     0xbe76e4: add             x0, PP, #0x46, lsl #12  ; [pp+0x46f10] Obj!EdgeInsets@d58191
    //     0xbe76e8: ldr             x0, [x0, #0xf10]
    // 0xbe76ec: stur            x1, [fp, #-0x10]
    // 0xbe76f0: StoreField: r1->field_f = r0
    //     0xbe76f0: stur            w0, [x1, #0xf]
    // 0xbe76f4: ldur            x0, [fp, #-0x38]
    // 0xbe76f8: StoreField: r1->field_b = r0
    //     0xbe76f8: stur            w0, [x1, #0xb]
    // 0xbe76fc: r0 = Container()
    //     0xbe76fc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbe7700: stur            x0, [fp, #-0x28]
    // 0xbe7704: r16 = 20.000000
    //     0xbe7704: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xbe7708: ldr             x16, [x16, #0xac8]
    // 0xbe770c: ldur            lr, [fp, #-0x58]
    // 0xbe7710: stp             lr, x16, [SP, #8]
    // 0xbe7714: ldur            x16, [fp, #-0x10]
    // 0xbe7718: str             x16, [SP]
    // 0xbe771c: mov             x1, x0
    // 0xbe7720: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xbe7720: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xbe7724: ldr             x4, [x4, #0xc78]
    // 0xbe7728: r0 = Container()
    //     0xbe7728: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbe772c: r0 = Align()
    //     0xbe772c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xbe7730: mov             x1, x0
    // 0xbe7734: r0 = Instance_Alignment
    //     0xbe7734: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xbe7738: ldr             x0, [x0, #0xfa0]
    // 0xbe773c: stur            x1, [fp, #-0x10]
    // 0xbe7740: StoreField: r1->field_f = r0
    //     0xbe7740: stur            w0, [x1, #0xf]
    // 0xbe7744: ldur            x0, [fp, #-0x28]
    // 0xbe7748: StoreField: r1->field_b = r0
    //     0xbe7748: stur            w0, [x1, #0xb]
    // 0xbe774c: r0 = Padding()
    //     0xbe774c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe7750: r3 = Instance_EdgeInsets
    //     0xbe7750: add             x3, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xbe7754: ldr             x3, [x3, #0xe50]
    // 0xbe7758: StoreField: r0->field_f = r3
    //     0xbe7758: stur            w3, [x0, #0xf]
    // 0xbe775c: ldur            x1, [fp, #-0x10]
    // 0xbe7760: StoreField: r0->field_b = r1
    //     0xbe7760: stur            w1, [x0, #0xb]
    // 0xbe7764: mov             x1, x0
    // 0xbe7768: b               #0xbe7a64
    // 0xbe776c: r3 = Instance_EdgeInsets
    //     0xbe776c: add             x3, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xbe7770: ldr             x3, [x3, #0xe50]
    // 0xbe7774: r0 = Instance_Alignment
    //     0xbe7774: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xbe7778: ldr             x0, [x0, #0xfa0]
    // 0xbe777c: r2 = 4
    //     0xbe777c: movz            x2, #0x4
    // 0xbe7780: d0 = 0.700000
    //     0xbe7780: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbe7784: ldr             d0, [x17, #0xf48]
    // 0xbe7788: ldur            x5, [fp, #-0x20]
    // 0xbe778c: ldur            x4, [fp, #-8]
    // 0xbe7790: LoadField: r1 = r4->field_f
    //     0xbe7790: ldur            w1, [x4, #0xf]
    // 0xbe7794: DecompressPointer r1
    //     0xbe7794: add             x1, x1, HEAP, lsl #32
    // 0xbe7798: cmp             w1, NULL
    // 0xbe779c: b.eq            #0xbe7fe8
    // 0xbe77a0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbe77a0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbe77a4: r0 = _of()
    //     0xbe77a4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbe77a8: LoadField: r1 = r0->field_7
    //     0xbe77a8: ldur            w1, [x0, #7]
    // 0xbe77ac: DecompressPointer r1
    //     0xbe77ac: add             x1, x1, HEAP, lsl #32
    // 0xbe77b0: LoadField: d0 = r1->field_7
    //     0xbe77b0: ldur            d0, [x1, #7]
    // 0xbe77b4: d1 = 0.370000
    //     0xbe77b4: add             x17, PP, #0x52, lsl #12  ; [pp+0x52e40] IMM: double(0.37) from 0x3fd7ae147ae147ae
    //     0xbe77b8: ldr             d1, [x17, #0xe40]
    // 0xbe77bc: fmul            d2, d0, d1
    // 0xbe77c0: ldur            x0, [fp, #-8]
    // 0xbe77c4: stur            d2, [fp, #-0x70]
    // 0xbe77c8: LoadField: r1 = r0->field_f
    //     0xbe77c8: ldur            w1, [x0, #0xf]
    // 0xbe77cc: DecompressPointer r1
    //     0xbe77cc: add             x1, x1, HEAP, lsl #32
    // 0xbe77d0: cmp             w1, NULL
    // 0xbe77d4: b.eq            #0xbe7fec
    // 0xbe77d8: r0 = of()
    //     0xbe77d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe77dc: LoadField: r1 = r0->field_5b
    //     0xbe77dc: ldur            w1, [x0, #0x5b]
    // 0xbe77e0: DecompressPointer r1
    //     0xbe77e0: add             x1, x1, HEAP, lsl #32
    // 0xbe77e4: stur            x1, [fp, #-0x10]
    // 0xbe77e8: r0 = ColorFilter()
    //     0xbe77e8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbe77ec: mov             x1, x0
    // 0xbe77f0: ldur            x0, [fp, #-0x10]
    // 0xbe77f4: stur            x1, [fp, #-0x28]
    // 0xbe77f8: StoreField: r1->field_7 = r0
    //     0xbe77f8: stur            w0, [x1, #7]
    // 0xbe77fc: r0 = Instance_BlendMode
    //     0xbe77fc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbe7800: ldr             x0, [x0, #0xb30]
    // 0xbe7804: StoreField: r1->field_b = r0
    //     0xbe7804: stur            w0, [x1, #0xb]
    // 0xbe7808: r0 = 1
    //     0xbe7808: movz            x0, #0x1
    // 0xbe780c: StoreField: r1->field_13 = r0
    //     0xbe780c: stur            x0, [x1, #0x13]
    // 0xbe7810: r0 = SvgPicture()
    //     0xbe7810: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbe7814: stur            x0, [fp, #-0x10]
    // 0xbe7818: ldur            x16, [fp, #-0x28]
    // 0xbe781c: str             x16, [SP]
    // 0xbe7820: mov             x1, x0
    // 0xbe7824: r2 = "assets/images/bumper_coupon.svg"
    //     0xbe7824: add             x2, PP, #0x52, lsl #12  ; [pp+0x52e48] "assets/images/bumper_coupon.svg"
    //     0xbe7828: ldr             x2, [x2, #0xe48]
    // 0xbe782c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xbe782c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xbe7830: ldr             x4, [x4, #0xa38]
    // 0xbe7834: r0 = SvgPicture.asset()
    //     0xbe7834: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbe7838: ldur            x2, [fp, #-0x20]
    // 0xbe783c: LoadField: r0 = r2->field_13
    //     0xbe783c: ldur            w0, [x2, #0x13]
    // 0xbe7840: DecompressPointer r0
    //     0xbe7840: add             x0, x0, HEAP, lsl #32
    // 0xbe7844: cmp             w0, NULL
    // 0xbe7848: b.ne            #0xbe7854
    // 0xbe784c: r0 = Null
    //     0xbe784c: mov             x0, NULL
    // 0xbe7850: b               #0xbe7860
    // 0xbe7854: LoadField: r1 = r0->field_7f
    //     0xbe7854: ldur            w1, [x0, #0x7f]
    // 0xbe7858: DecompressPointer r1
    //     0xbe7858: add             x1, x1, HEAP, lsl #32
    // 0xbe785c: mov             x0, x1
    // 0xbe7860: cmp             w0, NULL
    // 0xbe7864: b.ne            #0xbe7870
    // 0xbe7868: r4 = ""
    //     0xbe7868: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe786c: b               #0xbe7874
    // 0xbe7870: mov             x4, x0
    // 0xbe7874: ldur            x3, [fp, #-8]
    // 0xbe7878: ldur            d0, [fp, #-0x70]
    // 0xbe787c: ldur            x0, [fp, #-0x10]
    // 0xbe7880: stur            x4, [fp, #-0x28]
    // 0xbe7884: LoadField: r1 = r3->field_f
    //     0xbe7884: ldur            w1, [x3, #0xf]
    // 0xbe7888: DecompressPointer r1
    //     0xbe7888: add             x1, x1, HEAP, lsl #32
    // 0xbe788c: cmp             w1, NULL
    // 0xbe7890: b.eq            #0xbe7ff0
    // 0xbe7894: r0 = of()
    //     0xbe7894: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe7898: LoadField: r1 = r0->field_87
    //     0xbe7898: ldur            w1, [x0, #0x87]
    // 0xbe789c: DecompressPointer r1
    //     0xbe789c: add             x1, x1, HEAP, lsl #32
    // 0xbe78a0: LoadField: r0 = r1->field_2b
    //     0xbe78a0: ldur            w0, [x1, #0x2b]
    // 0xbe78a4: DecompressPointer r0
    //     0xbe78a4: add             x0, x0, HEAP, lsl #32
    // 0xbe78a8: stur            x0, [fp, #-0x38]
    // 0xbe78ac: r1 = Instance_Color
    //     0xbe78ac: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe78b0: d0 = 0.700000
    //     0xbe78b0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbe78b4: ldr             d0, [x17, #0xf48]
    // 0xbe78b8: r0 = withOpacity()
    //     0xbe78b8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbe78bc: r16 = 12.000000
    //     0xbe78bc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe78c0: ldr             x16, [x16, #0x9e8]
    // 0xbe78c4: stp             x0, x16, [SP]
    // 0xbe78c8: ldur            x1, [fp, #-0x38]
    // 0xbe78cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe78cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe78d0: ldr             x4, [x4, #0xaa0]
    // 0xbe78d4: r0 = copyWith()
    //     0xbe78d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe78d8: stur            x0, [fp, #-0x38]
    // 0xbe78dc: r0 = Text()
    //     0xbe78dc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe78e0: mov             x1, x0
    // 0xbe78e4: ldur            x0, [fp, #-0x28]
    // 0xbe78e8: stur            x1, [fp, #-0x58]
    // 0xbe78ec: StoreField: r1->field_b = r0
    //     0xbe78ec: stur            w0, [x1, #0xb]
    // 0xbe78f0: ldur            x0, [fp, #-0x38]
    // 0xbe78f4: StoreField: r1->field_13 = r0
    //     0xbe78f4: stur            w0, [x1, #0x13]
    // 0xbe78f8: r0 = Instance_TextAlign
    //     0xbe78f8: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xbe78fc: StoreField: r1->field_1b = r0
    //     0xbe78fc: stur            w0, [x1, #0x1b]
    // 0xbe7900: r0 = Padding()
    //     0xbe7900: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe7904: mov             x3, x0
    // 0xbe7908: r0 = Instance_EdgeInsets
    //     0xbe7908: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xbe790c: ldr             x0, [x0, #0xe60]
    // 0xbe7910: stur            x3, [fp, #-0x28]
    // 0xbe7914: StoreField: r3->field_f = r0
    //     0xbe7914: stur            w0, [x3, #0xf]
    // 0xbe7918: ldur            x0, [fp, #-0x58]
    // 0xbe791c: StoreField: r3->field_b = r0
    //     0xbe791c: stur            w0, [x3, #0xb]
    // 0xbe7920: r1 = Null
    //     0xbe7920: mov             x1, NULL
    // 0xbe7924: r2 = 4
    //     0xbe7924: movz            x2, #0x4
    // 0xbe7928: r0 = AllocateArray()
    //     0xbe7928: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe792c: mov             x2, x0
    // 0xbe7930: ldur            x0, [fp, #-0x10]
    // 0xbe7934: stur            x2, [fp, #-0x38]
    // 0xbe7938: StoreField: r2->field_f = r0
    //     0xbe7938: stur            w0, [x2, #0xf]
    // 0xbe793c: ldur            x0, [fp, #-0x28]
    // 0xbe7940: StoreField: r2->field_13 = r0
    //     0xbe7940: stur            w0, [x2, #0x13]
    // 0xbe7944: r1 = <Widget>
    //     0xbe7944: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe7948: r0 = AllocateGrowableArray()
    //     0xbe7948: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe794c: mov             x1, x0
    // 0xbe7950: ldur            x0, [fp, #-0x38]
    // 0xbe7954: stur            x1, [fp, #-0x10]
    // 0xbe7958: StoreField: r1->field_f = r0
    //     0xbe7958: stur            w0, [x1, #0xf]
    // 0xbe795c: r0 = 4
    //     0xbe795c: movz            x0, #0x4
    // 0xbe7960: StoreField: r1->field_b = r0
    //     0xbe7960: stur            w0, [x1, #0xb]
    // 0xbe7964: r0 = Row()
    //     0xbe7964: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbe7968: mov             x1, x0
    // 0xbe796c: r0 = Instance_Axis
    //     0xbe796c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbe7970: stur            x1, [fp, #-0x28]
    // 0xbe7974: StoreField: r1->field_f = r0
    //     0xbe7974: stur            w0, [x1, #0xf]
    // 0xbe7978: r0 = Instance_MainAxisAlignment
    //     0xbe7978: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbe797c: ldr             x0, [x0, #0xa08]
    // 0xbe7980: StoreField: r1->field_13 = r0
    //     0xbe7980: stur            w0, [x1, #0x13]
    // 0xbe7984: r0 = Instance_MainAxisSize
    //     0xbe7984: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbe7988: ldr             x0, [x0, #0xa10]
    // 0xbe798c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbe798c: stur            w0, [x1, #0x17]
    // 0xbe7990: r0 = Instance_CrossAxisAlignment
    //     0xbe7990: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbe7994: ldr             x0, [x0, #0xa18]
    // 0xbe7998: StoreField: r1->field_1b = r0
    //     0xbe7998: stur            w0, [x1, #0x1b]
    // 0xbe799c: r0 = Instance_VerticalDirection
    //     0xbe799c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe79a0: ldr             x0, [x0, #0xa20]
    // 0xbe79a4: StoreField: r1->field_23 = r0
    //     0xbe79a4: stur            w0, [x1, #0x23]
    // 0xbe79a8: r0 = Instance_Clip
    //     0xbe79a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe79ac: ldr             x0, [x0, #0x38]
    // 0xbe79b0: StoreField: r1->field_2b = r0
    //     0xbe79b0: stur            w0, [x1, #0x2b]
    // 0xbe79b4: StoreField: r1->field_2f = rZR
    //     0xbe79b4: stur            xzr, [x1, #0x2f]
    // 0xbe79b8: ldur            x0, [fp, #-0x10]
    // 0xbe79bc: StoreField: r1->field_b = r0
    //     0xbe79bc: stur            w0, [x1, #0xb]
    // 0xbe79c0: ldur            d0, [fp, #-0x70]
    // 0xbe79c4: r0 = inline_Allocate_Double()
    //     0xbe79c4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbe79c8: add             x0, x0, #0x10
    //     0xbe79cc: cmp             x2, x0
    //     0xbe79d0: b.ls            #0xbe7ff4
    //     0xbe79d4: str             x0, [THR, #0x50]  ; THR::top
    //     0xbe79d8: sub             x0, x0, #0xf
    //     0xbe79dc: movz            x2, #0xe15c
    //     0xbe79e0: movk            x2, #0x3, lsl #16
    //     0xbe79e4: stur            x2, [x0, #-1]
    // 0xbe79e8: StoreField: r0->field_7 = d0
    //     0xbe79e8: stur            d0, [x0, #7]
    // 0xbe79ec: stur            x0, [fp, #-0x10]
    // 0xbe79f0: r0 = Container()
    //     0xbe79f0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbe79f4: stur            x0, [fp, #-0x38]
    // 0xbe79f8: r16 = 20.000000
    //     0xbe79f8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xbe79fc: ldr             x16, [x16, #0xac8]
    // 0xbe7a00: ldur            lr, [fp, #-0x10]
    // 0xbe7a04: stp             lr, x16, [SP, #0x10]
    // 0xbe7a08: r16 = Instance_BoxDecoration
    //     0xbe7a08: add             x16, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xbe7a0c: ldr             x16, [x16, #0x5a8]
    // 0xbe7a10: ldur            lr, [fp, #-0x28]
    // 0xbe7a14: stp             lr, x16, [SP]
    // 0xbe7a18: mov             x1, x0
    // 0xbe7a1c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xbe7a1c: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xbe7a20: ldr             x4, [x4, #0x8c0]
    // 0xbe7a24: r0 = Container()
    //     0xbe7a24: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbe7a28: r0 = Padding()
    //     0xbe7a28: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe7a2c: mov             x1, x0
    // 0xbe7a30: r0 = Instance_EdgeInsets
    //     0xbe7a30: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xbe7a34: ldr             x0, [x0, #0xe50]
    // 0xbe7a38: stur            x1, [fp, #-0x10]
    // 0xbe7a3c: StoreField: r1->field_f = r0
    //     0xbe7a3c: stur            w0, [x1, #0xf]
    // 0xbe7a40: ldur            x0, [fp, #-0x38]
    // 0xbe7a44: StoreField: r1->field_b = r0
    //     0xbe7a44: stur            w0, [x1, #0xb]
    // 0xbe7a48: r0 = Align()
    //     0xbe7a48: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xbe7a4c: mov             x1, x0
    // 0xbe7a50: r0 = Instance_Alignment
    //     0xbe7a50: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xbe7a54: ldr             x0, [x0, #0xfa0]
    // 0xbe7a58: StoreField: r1->field_f = r0
    //     0xbe7a58: stur            w0, [x1, #0xf]
    // 0xbe7a5c: ldur            x0, [fp, #-0x10]
    // 0xbe7a60: StoreField: r1->field_b = r0
    //     0xbe7a60: stur            w0, [x1, #0xb]
    // 0xbe7a64: ldur            x2, [fp, #-0x20]
    // 0xbe7a68: ldur            x0, [fp, #-0x18]
    // 0xbe7a6c: stur            x1, [fp, #-0x10]
    // 0xbe7a70: r0 = Visibility()
    //     0xbe7a70: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbe7a74: mov             x1, x0
    // 0xbe7a78: ldur            x0, [fp, #-0x10]
    // 0xbe7a7c: stur            x1, [fp, #-0x28]
    // 0xbe7a80: StoreField: r1->field_b = r0
    //     0xbe7a80: stur            w0, [x1, #0xb]
    // 0xbe7a84: r0 = Instance_SizedBox
    //     0xbe7a84: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbe7a88: StoreField: r1->field_f = r0
    //     0xbe7a88: stur            w0, [x1, #0xf]
    // 0xbe7a8c: ldur            x2, [fp, #-0x18]
    // 0xbe7a90: StoreField: r1->field_13 = r2
    //     0xbe7a90: stur            w2, [x1, #0x13]
    // 0xbe7a94: r2 = false
    //     0xbe7a94: add             x2, NULL, #0x30  ; false
    // 0xbe7a98: ArrayStore: r1[0] = r2  ; List_4
    //     0xbe7a98: stur            w2, [x1, #0x17]
    // 0xbe7a9c: StoreField: r1->field_1b = r2
    //     0xbe7a9c: stur            w2, [x1, #0x1b]
    // 0xbe7aa0: StoreField: r1->field_1f = r2
    //     0xbe7aa0: stur            w2, [x1, #0x1f]
    // 0xbe7aa4: StoreField: r1->field_23 = r2
    //     0xbe7aa4: stur            w2, [x1, #0x23]
    // 0xbe7aa8: StoreField: r1->field_27 = r2
    //     0xbe7aa8: stur            w2, [x1, #0x27]
    // 0xbe7aac: StoreField: r1->field_2b = r2
    //     0xbe7aac: stur            w2, [x1, #0x2b]
    // 0xbe7ab0: ldur            x3, [fp, #-0x20]
    // 0xbe7ab4: LoadField: r4 = r3->field_13
    //     0xbe7ab4: ldur            w4, [x3, #0x13]
    // 0xbe7ab8: DecompressPointer r4
    //     0xbe7ab8: add             x4, x4, HEAP, lsl #32
    // 0xbe7abc: cmp             w4, NULL
    // 0xbe7ac0: b.ne            #0xbe7acc
    // 0xbe7ac4: r5 = Null
    //     0xbe7ac4: mov             x5, NULL
    // 0xbe7ac8: b               #0xbe7af8
    // 0xbe7acc: LoadField: r5 = r4->field_b7
    //     0xbe7acc: ldur            w5, [x4, #0xb7]
    // 0xbe7ad0: DecompressPointer r5
    //     0xbe7ad0: add             x5, x5, HEAP, lsl #32
    // 0xbe7ad4: cmp             w5, NULL
    // 0xbe7ad8: b.ne            #0xbe7ae4
    // 0xbe7adc: r5 = Null
    //     0xbe7adc: mov             x5, NULL
    // 0xbe7ae0: b               #0xbe7af8
    // 0xbe7ae4: LoadField: r6 = r5->field_7
    //     0xbe7ae4: ldur            w6, [x5, #7]
    // 0xbe7ae8: cbnz            w6, #0xbe7af4
    // 0xbe7aec: r5 = false
    //     0xbe7aec: add             x5, NULL, #0x30  ; false
    // 0xbe7af0: b               #0xbe7af8
    // 0xbe7af4: r5 = true
    //     0xbe7af4: add             x5, NULL, #0x20  ; true
    // 0xbe7af8: cmp             w5, NULL
    // 0xbe7afc: b.ne            #0xbe7b04
    // 0xbe7b00: r5 = false
    //     0xbe7b00: add             x5, NULL, #0x30  ; false
    // 0xbe7b04: stur            x5, [fp, #-0x10]
    // 0xbe7b08: cmp             w4, NULL
    // 0xbe7b0c: b.ne            #0xbe7b18
    // 0xbe7b10: r4 = Null
    //     0xbe7b10: mov             x4, NULL
    // 0xbe7b14: b               #0xbe7b24
    // 0xbe7b18: LoadField: r6 = r4->field_8b
    //     0xbe7b18: ldur            w6, [x4, #0x8b]
    // 0xbe7b1c: DecompressPointer r6
    //     0xbe7b1c: add             x6, x6, HEAP, lsl #32
    // 0xbe7b20: mov             x4, x6
    // 0xbe7b24: cmp             w4, NULL
    // 0xbe7b28: b.eq            #0xbe7b3c
    // 0xbe7b2c: tbnz            w4, #4, #0xbe7b3c
    // 0xbe7b30: d0 = 38.000000
    //     0xbe7b30: add             x17, PP, #0x50, lsl #12  ; [pp+0x50d10] IMM: double(38) from 0x4043000000000000
    //     0xbe7b34: ldr             d0, [x17, #0xd10]
    // 0xbe7b38: b               #0xbe7b40
    // 0xbe7b3c: d0 = 4.000000
    //     0xbe7b3c: fmov            d0, #4.00000000
    // 0xbe7b40: stur            d0, [fp, #-0x70]
    // 0xbe7b44: r0 = EdgeInsets()
    //     0xbe7b44: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xbe7b48: d0 = 8.000000
    //     0xbe7b48: fmov            d0, #8.00000000
    // 0xbe7b4c: stur            x0, [fp, #-0x18]
    // 0xbe7b50: StoreField: r0->field_7 = d0
    //     0xbe7b50: stur            d0, [x0, #7]
    // 0xbe7b54: StoreField: r0->field_f = rZR
    //     0xbe7b54: stur            xzr, [x0, #0xf]
    // 0xbe7b58: ArrayStore: r0[0] = rZR  ; List_8
    //     0xbe7b58: stur            xzr, [x0, #0x17]
    // 0xbe7b5c: ldur            d0, [fp, #-0x70]
    // 0xbe7b60: StoreField: r0->field_1f = d0
    //     0xbe7b60: stur            d0, [x0, #0x1f]
    // 0xbe7b64: r16 = <Color>
    //     0xbe7b64: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbe7b68: ldr             x16, [x16, #0xf80]
    // 0xbe7b6c: r30 = Instance_Color
    //     0xbe7b6c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbe7b70: stp             lr, x16, [SP]
    // 0xbe7b74: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe7b74: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe7b78: r0 = all()
    //     0xbe7b78: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe7b7c: stur            x0, [fp, #-0x38]
    // 0xbe7b80: r16 = <RoundedRectangleBorder>
    //     0xbe7b80: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbe7b84: ldr             x16, [x16, #0xf78]
    // 0xbe7b88: r30 = Instance_RoundedRectangleBorder
    //     0xbe7b88: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbe7b8c: ldr             lr, [lr, #0xd68]
    // 0xbe7b90: stp             lr, x16, [SP]
    // 0xbe7b94: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe7b94: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe7b98: r0 = all()
    //     0xbe7b98: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe7b9c: stur            x0, [fp, #-0x58]
    // 0xbe7ba0: r0 = ButtonStyle()
    //     0xbe7ba0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbe7ba4: mov             x1, x0
    // 0xbe7ba8: ldur            x0, [fp, #-0x38]
    // 0xbe7bac: stur            x1, [fp, #-0x60]
    // 0xbe7bb0: StoreField: r1->field_b = r0
    //     0xbe7bb0: stur            w0, [x1, #0xb]
    // 0xbe7bb4: ldur            x0, [fp, #-0x58]
    // 0xbe7bb8: StoreField: r1->field_43 = r0
    //     0xbe7bb8: stur            w0, [x1, #0x43]
    // 0xbe7bbc: r0 = TextButtonThemeData()
    //     0xbe7bbc: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbe7bc0: mov             x1, x0
    // 0xbe7bc4: ldur            x0, [fp, #-0x60]
    // 0xbe7bc8: stur            x1, [fp, #-0x38]
    // 0xbe7bcc: StoreField: r1->field_7 = r0
    //     0xbe7bcc: stur            w0, [x1, #7]
    // 0xbe7bd0: ldur            x2, [fp, #-0x20]
    // 0xbe7bd4: LoadField: r0 = r2->field_13
    //     0xbe7bd4: ldur            w0, [x2, #0x13]
    // 0xbe7bd8: DecompressPointer r0
    //     0xbe7bd8: add             x0, x0, HEAP, lsl #32
    // 0xbe7bdc: cmp             w0, NULL
    // 0xbe7be0: b.ne            #0xbe7bec
    // 0xbe7be4: r5 = Null
    //     0xbe7be4: mov             x5, NULL
    // 0xbe7be8: b               #0xbe7bf8
    // 0xbe7bec: LoadField: r3 = r0->field_b7
    //     0xbe7bec: ldur            w3, [x0, #0xb7]
    // 0xbe7bf0: DecompressPointer r3
    //     0xbe7bf0: add             x3, x3, HEAP, lsl #32
    // 0xbe7bf4: mov             x5, x3
    // 0xbe7bf8: ldur            x4, [fp, #-8]
    // 0xbe7bfc: ldur            x3, [fp, #-0x10]
    // 0xbe7c00: ldur            x0, [fp, #-0x18]
    // 0xbe7c04: str             x5, [SP]
    // 0xbe7c08: r0 = _interpolateSingle()
    //     0xbe7c08: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbe7c0c: mov             x2, x0
    // 0xbe7c10: ldur            x0, [fp, #-8]
    // 0xbe7c14: stur            x2, [fp, #-0x58]
    // 0xbe7c18: LoadField: r1 = r0->field_f
    //     0xbe7c18: ldur            w1, [x0, #0xf]
    // 0xbe7c1c: DecompressPointer r1
    //     0xbe7c1c: add             x1, x1, HEAP, lsl #32
    // 0xbe7c20: cmp             w1, NULL
    // 0xbe7c24: b.eq            #0xbe800c
    // 0xbe7c28: r0 = of()
    //     0xbe7c28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe7c2c: LoadField: r1 = r0->field_87
    //     0xbe7c2c: ldur            w1, [x0, #0x87]
    // 0xbe7c30: DecompressPointer r1
    //     0xbe7c30: add             x1, x1, HEAP, lsl #32
    // 0xbe7c34: LoadField: r0 = r1->field_2b
    //     0xbe7c34: ldur            w0, [x1, #0x2b]
    // 0xbe7c38: DecompressPointer r0
    //     0xbe7c38: add             x0, x0, HEAP, lsl #32
    // 0xbe7c3c: r16 = 12.000000
    //     0xbe7c3c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe7c40: ldr             x16, [x16, #0x9e8]
    // 0xbe7c44: r30 = Instance_Color
    //     0xbe7c44: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe7c48: stp             lr, x16, [SP]
    // 0xbe7c4c: mov             x1, x0
    // 0xbe7c50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe7c50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe7c54: ldr             x4, [x4, #0xaa0]
    // 0xbe7c58: r0 = copyWith()
    //     0xbe7c58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe7c5c: stur            x0, [fp, #-0x60]
    // 0xbe7c60: r0 = Text()
    //     0xbe7c60: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe7c64: mov             x3, x0
    // 0xbe7c68: ldur            x0, [fp, #-0x58]
    // 0xbe7c6c: stur            x3, [fp, #-0x68]
    // 0xbe7c70: StoreField: r3->field_b = r0
    //     0xbe7c70: stur            w0, [x3, #0xb]
    // 0xbe7c74: ldur            x0, [fp, #-0x60]
    // 0xbe7c78: StoreField: r3->field_13 = r0
    //     0xbe7c78: stur            w0, [x3, #0x13]
    // 0xbe7c7c: r1 = Function '<anonymous closure>':.
    //     0xbe7c7c: add             x1, PP, #0x61, lsl #12  ; [pp+0x61cb8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xbe7c80: ldr             x1, [x1, #0xcb8]
    // 0xbe7c84: r2 = Null
    //     0xbe7c84: mov             x2, NULL
    // 0xbe7c88: r0 = AllocateClosure()
    //     0xbe7c88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe7c8c: stur            x0, [fp, #-0x58]
    // 0xbe7c90: r0 = TextButton()
    //     0xbe7c90: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbe7c94: mov             x1, x0
    // 0xbe7c98: ldur            x0, [fp, #-0x58]
    // 0xbe7c9c: stur            x1, [fp, #-0x60]
    // 0xbe7ca0: StoreField: r1->field_b = r0
    //     0xbe7ca0: stur            w0, [x1, #0xb]
    // 0xbe7ca4: r0 = false
    //     0xbe7ca4: add             x0, NULL, #0x30  ; false
    // 0xbe7ca8: StoreField: r1->field_27 = r0
    //     0xbe7ca8: stur            w0, [x1, #0x27]
    // 0xbe7cac: r2 = true
    //     0xbe7cac: add             x2, NULL, #0x20  ; true
    // 0xbe7cb0: StoreField: r1->field_2f = r2
    //     0xbe7cb0: stur            w2, [x1, #0x2f]
    // 0xbe7cb4: ldur            x3, [fp, #-0x68]
    // 0xbe7cb8: StoreField: r1->field_37 = r3
    //     0xbe7cb8: stur            w3, [x1, #0x37]
    // 0xbe7cbc: r0 = TextButtonTheme()
    //     0xbe7cbc: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbe7cc0: mov             x1, x0
    // 0xbe7cc4: ldur            x0, [fp, #-0x38]
    // 0xbe7cc8: stur            x1, [fp, #-0x58]
    // 0xbe7ccc: StoreField: r1->field_f = r0
    //     0xbe7ccc: stur            w0, [x1, #0xf]
    // 0xbe7cd0: ldur            x0, [fp, #-0x60]
    // 0xbe7cd4: StoreField: r1->field_b = r0
    //     0xbe7cd4: stur            w0, [x1, #0xb]
    // 0xbe7cd8: r0 = Padding()
    //     0xbe7cd8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe7cdc: mov             x1, x0
    // 0xbe7ce0: ldur            x0, [fp, #-0x18]
    // 0xbe7ce4: stur            x1, [fp, #-0x38]
    // 0xbe7ce8: StoreField: r1->field_f = r0
    //     0xbe7ce8: stur            w0, [x1, #0xf]
    // 0xbe7cec: ldur            x0, [fp, #-0x58]
    // 0xbe7cf0: StoreField: r1->field_b = r0
    //     0xbe7cf0: stur            w0, [x1, #0xb]
    // 0xbe7cf4: r0 = Visibility()
    //     0xbe7cf4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbe7cf8: mov             x2, x0
    // 0xbe7cfc: ldur            x0, [fp, #-0x38]
    // 0xbe7d00: stur            x2, [fp, #-0x18]
    // 0xbe7d04: StoreField: r2->field_b = r0
    //     0xbe7d04: stur            w0, [x2, #0xb]
    // 0xbe7d08: r0 = Instance_SizedBox
    //     0xbe7d08: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbe7d0c: StoreField: r2->field_f = r0
    //     0xbe7d0c: stur            w0, [x2, #0xf]
    // 0xbe7d10: ldur            x0, [fp, #-0x10]
    // 0xbe7d14: StoreField: r2->field_13 = r0
    //     0xbe7d14: stur            w0, [x2, #0x13]
    // 0xbe7d18: r0 = false
    //     0xbe7d18: add             x0, NULL, #0x30  ; false
    // 0xbe7d1c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbe7d1c: stur            w0, [x2, #0x17]
    // 0xbe7d20: StoreField: r2->field_1b = r0
    //     0xbe7d20: stur            w0, [x2, #0x1b]
    // 0xbe7d24: StoreField: r2->field_1f = r0
    //     0xbe7d24: stur            w0, [x2, #0x1f]
    // 0xbe7d28: StoreField: r2->field_23 = r0
    //     0xbe7d28: stur            w0, [x2, #0x23]
    // 0xbe7d2c: StoreField: r2->field_27 = r0
    //     0xbe7d2c: stur            w0, [x2, #0x27]
    // 0xbe7d30: StoreField: r2->field_2b = r0
    //     0xbe7d30: stur            w0, [x2, #0x2b]
    // 0xbe7d34: ldur            x3, [fp, #-0x20]
    // 0xbe7d38: LoadField: r1 = r3->field_13
    //     0xbe7d38: ldur            w1, [x3, #0x13]
    // 0xbe7d3c: DecompressPointer r1
    //     0xbe7d3c: add             x1, x1, HEAP, lsl #32
    // 0xbe7d40: cmp             w1, NULL
    // 0xbe7d44: b.ne            #0xbe7d50
    // 0xbe7d48: r1 = Null
    //     0xbe7d48: mov             x1, NULL
    // 0xbe7d4c: b               #0xbe7d5c
    // 0xbe7d50: LoadField: r4 = r1->field_8b
    //     0xbe7d50: ldur            w4, [x1, #0x8b]
    // 0xbe7d54: DecompressPointer r4
    //     0xbe7d54: add             x4, x4, HEAP, lsl #32
    // 0xbe7d58: mov             x1, x4
    // 0xbe7d5c: cmp             w1, NULL
    // 0xbe7d60: b.eq            #0xbe7e7c
    // 0xbe7d64: tbnz            w1, #4, #0xbe7e7c
    // 0xbe7d68: ldur            x4, [fp, #-8]
    // 0xbe7d6c: LoadField: r1 = r4->field_f
    //     0xbe7d6c: ldur            w1, [x4, #0xf]
    // 0xbe7d70: DecompressPointer r1
    //     0xbe7d70: add             x1, x1, HEAP, lsl #32
    // 0xbe7d74: cmp             w1, NULL
    // 0xbe7d78: b.eq            #0xbe8010
    // 0xbe7d7c: r0 = of()
    //     0xbe7d7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe7d80: r17 = 307
    //     0xbe7d80: movz            x17, #0x133
    // 0xbe7d84: ldr             w2, [x0, x17]
    // 0xbe7d88: DecompressPointer r2
    //     0xbe7d88: add             x2, x2, HEAP, lsl #32
    // 0xbe7d8c: ldur            x0, [fp, #-8]
    // 0xbe7d90: stur            x2, [fp, #-0x10]
    // 0xbe7d94: LoadField: r1 = r0->field_f
    //     0xbe7d94: ldur            w1, [x0, #0xf]
    // 0xbe7d98: DecompressPointer r1
    //     0xbe7d98: add             x1, x1, HEAP, lsl #32
    // 0xbe7d9c: cmp             w1, NULL
    // 0xbe7da0: b.eq            #0xbe8014
    // 0xbe7da4: r0 = of()
    //     0xbe7da4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe7da8: LoadField: r1 = r0->field_87
    //     0xbe7da8: ldur            w1, [x0, #0x87]
    // 0xbe7dac: DecompressPointer r1
    //     0xbe7dac: add             x1, x1, HEAP, lsl #32
    // 0xbe7db0: LoadField: r0 = r1->field_2b
    //     0xbe7db0: ldur            w0, [x1, #0x2b]
    // 0xbe7db4: DecompressPointer r0
    //     0xbe7db4: add             x0, x0, HEAP, lsl #32
    // 0xbe7db8: r16 = 12.000000
    //     0xbe7db8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe7dbc: ldr             x16, [x16, #0x9e8]
    // 0xbe7dc0: r30 = Instance_Color
    //     0xbe7dc0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbe7dc4: stp             lr, x16, [SP]
    // 0xbe7dc8: mov             x1, x0
    // 0xbe7dcc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe7dcc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe7dd0: ldr             x4, [x4, #0xaa0]
    // 0xbe7dd4: r0 = copyWith()
    //     0xbe7dd4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe7dd8: stur            x0, [fp, #-8]
    // 0xbe7ddc: r0 = Text()
    //     0xbe7ddc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe7de0: mov             x3, x0
    // 0xbe7de4: r0 = "Customisable"
    //     0xbe7de4: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xbe7de8: ldr             x0, [x0, #0x970]
    // 0xbe7dec: stur            x3, [fp, #-0x38]
    // 0xbe7df0: StoreField: r3->field_b = r0
    //     0xbe7df0: stur            w0, [x3, #0xb]
    // 0xbe7df4: ldur            x0, [fp, #-8]
    // 0xbe7df8: StoreField: r3->field_13 = r0
    //     0xbe7df8: stur            w0, [x3, #0x13]
    // 0xbe7dfc: r1 = Function '<anonymous closure>':.
    //     0xbe7dfc: add             x1, PP, #0x61, lsl #12  ; [pp+0x61cc0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xbe7e00: ldr             x1, [x1, #0xcc0]
    // 0xbe7e04: r2 = Null
    //     0xbe7e04: mov             x2, NULL
    // 0xbe7e08: r0 = AllocateClosure()
    //     0xbe7e08: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe7e0c: stur            x0, [fp, #-8]
    // 0xbe7e10: r0 = TextButton()
    //     0xbe7e10: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbe7e14: mov             x1, x0
    // 0xbe7e18: ldur            x0, [fp, #-8]
    // 0xbe7e1c: stur            x1, [fp, #-0x58]
    // 0xbe7e20: StoreField: r1->field_b = r0
    //     0xbe7e20: stur            w0, [x1, #0xb]
    // 0xbe7e24: r0 = false
    //     0xbe7e24: add             x0, NULL, #0x30  ; false
    // 0xbe7e28: StoreField: r1->field_27 = r0
    //     0xbe7e28: stur            w0, [x1, #0x27]
    // 0xbe7e2c: r2 = true
    //     0xbe7e2c: add             x2, NULL, #0x20  ; true
    // 0xbe7e30: StoreField: r1->field_2f = r2
    //     0xbe7e30: stur            w2, [x1, #0x2f]
    // 0xbe7e34: ldur            x3, [fp, #-0x38]
    // 0xbe7e38: StoreField: r1->field_37 = r3
    //     0xbe7e38: stur            w3, [x1, #0x37]
    // 0xbe7e3c: r0 = TextButtonTheme()
    //     0xbe7e3c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbe7e40: mov             x1, x0
    // 0xbe7e44: ldur            x0, [fp, #-0x10]
    // 0xbe7e48: stur            x1, [fp, #-8]
    // 0xbe7e4c: StoreField: r1->field_f = r0
    //     0xbe7e4c: stur            w0, [x1, #0xf]
    // 0xbe7e50: ldur            x0, [fp, #-0x58]
    // 0xbe7e54: StoreField: r1->field_b = r0
    //     0xbe7e54: stur            w0, [x1, #0xb]
    // 0xbe7e58: r0 = SizedBox()
    //     0xbe7e58: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbe7e5c: mov             x1, x0
    // 0xbe7e60: r0 = 30.000000
    //     0xbe7e60: add             x0, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xbe7e64: ldr             x0, [x0, #0x768]
    // 0xbe7e68: StoreField: r1->field_13 = r0
    //     0xbe7e68: stur            w0, [x1, #0x13]
    // 0xbe7e6c: ldur            x0, [fp, #-8]
    // 0xbe7e70: StoreField: r1->field_b = r0
    //     0xbe7e70: stur            w0, [x1, #0xb]
    // 0xbe7e74: mov             x3, x1
    // 0xbe7e78: b               #0xbe7e80
    // 0xbe7e7c: r3 = Instance_SizedBox
    //     0xbe7e7c: ldr             x3, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbe7e80: ldur            x2, [fp, #-0x30]
    // 0xbe7e84: ldur            x1, [fp, #-0x28]
    // 0xbe7e88: ldur            x0, [fp, #-0x18]
    // 0xbe7e8c: stur            x3, [fp, #-8]
    // 0xbe7e90: r0 = Padding()
    //     0xbe7e90: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe7e94: mov             x3, x0
    // 0xbe7e98: r0 = Instance_EdgeInsets
    //     0xbe7e98: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e68] Obj!EdgeInsets@d58461
    //     0xbe7e9c: ldr             x0, [x0, #0xe68]
    // 0xbe7ea0: stur            x3, [fp, #-0x10]
    // 0xbe7ea4: StoreField: r3->field_f = r0
    //     0xbe7ea4: stur            w0, [x3, #0xf]
    // 0xbe7ea8: ldur            x0, [fp, #-8]
    // 0xbe7eac: StoreField: r3->field_b = r0
    //     0xbe7eac: stur            w0, [x3, #0xb]
    // 0xbe7eb0: r1 = Null
    //     0xbe7eb0: mov             x1, NULL
    // 0xbe7eb4: r2 = 8
    //     0xbe7eb4: movz            x2, #0x8
    // 0xbe7eb8: r0 = AllocateArray()
    //     0xbe7eb8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe7ebc: mov             x2, x0
    // 0xbe7ec0: ldur            x0, [fp, #-0x30]
    // 0xbe7ec4: stur            x2, [fp, #-8]
    // 0xbe7ec8: StoreField: r2->field_f = r0
    //     0xbe7ec8: stur            w0, [x2, #0xf]
    // 0xbe7ecc: ldur            x0, [fp, #-0x28]
    // 0xbe7ed0: StoreField: r2->field_13 = r0
    //     0xbe7ed0: stur            w0, [x2, #0x13]
    // 0xbe7ed4: ldur            x0, [fp, #-0x18]
    // 0xbe7ed8: ArrayStore: r2[0] = r0  ; List_4
    //     0xbe7ed8: stur            w0, [x2, #0x17]
    // 0xbe7edc: ldur            x0, [fp, #-0x10]
    // 0xbe7ee0: StoreField: r2->field_1b = r0
    //     0xbe7ee0: stur            w0, [x2, #0x1b]
    // 0xbe7ee4: r1 = <Widget>
    //     0xbe7ee4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe7ee8: r0 = AllocateGrowableArray()
    //     0xbe7ee8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe7eec: mov             x1, x0
    // 0xbe7ef0: ldur            x0, [fp, #-8]
    // 0xbe7ef4: stur            x1, [fp, #-0x10]
    // 0xbe7ef8: StoreField: r1->field_f = r0
    //     0xbe7ef8: stur            w0, [x1, #0xf]
    // 0xbe7efc: r0 = 8
    //     0xbe7efc: movz            x0, #0x8
    // 0xbe7f00: StoreField: r1->field_b = r0
    //     0xbe7f00: stur            w0, [x1, #0xb]
    // 0xbe7f04: r0 = Stack()
    //     0xbe7f04: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbe7f08: mov             x1, x0
    // 0xbe7f0c: r0 = Instance_Alignment
    //     0xbe7f0c: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xbe7f10: ldr             x0, [x0, #0x5b8]
    // 0xbe7f14: stur            x1, [fp, #-8]
    // 0xbe7f18: StoreField: r1->field_f = r0
    //     0xbe7f18: stur            w0, [x1, #0xf]
    // 0xbe7f1c: r0 = Instance_StackFit
    //     0xbe7f1c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbe7f20: ldr             x0, [x0, #0xfa8]
    // 0xbe7f24: ArrayStore: r1[0] = r0  ; List_4
    //     0xbe7f24: stur            w0, [x1, #0x17]
    // 0xbe7f28: r0 = Instance_Clip
    //     0xbe7f28: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbe7f2c: ldr             x0, [x0, #0x7e0]
    // 0xbe7f30: StoreField: r1->field_1b = r0
    //     0xbe7f30: stur            w0, [x1, #0x1b]
    // 0xbe7f34: ldur            x0, [fp, #-0x10]
    // 0xbe7f38: StoreField: r1->field_b = r0
    //     0xbe7f38: stur            w0, [x1, #0xb]
    // 0xbe7f3c: r0 = InkWell()
    //     0xbe7f3c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbe7f40: mov             x3, x0
    // 0xbe7f44: ldur            x0, [fp, #-8]
    // 0xbe7f48: stur            x3, [fp, #-0x10]
    // 0xbe7f4c: StoreField: r3->field_b = r0
    //     0xbe7f4c: stur            w0, [x3, #0xb]
    // 0xbe7f50: ldur            x2, [fp, #-0x20]
    // 0xbe7f54: r1 = Function '<anonymous closure>':.
    //     0xbe7f54: add             x1, PP, #0x61, lsl #12  ; [pp+0x61cc8] AnonymousClosure: (0xbe8018), in [package:customer_app/app/presentation/views/line/home/<USER>/product_grid_item_view.dart] _ProductGridItemState::lineThemeSlider (0xbe70f0)
    //     0xbe7f58: ldr             x1, [x1, #0xcc8]
    // 0xbe7f5c: r0 = AllocateClosure()
    //     0xbe7f5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe7f60: ldur            x2, [fp, #-0x10]
    // 0xbe7f64: StoreField: r2->field_f = r0
    //     0xbe7f64: stur            w0, [x2, #0xf]
    // 0xbe7f68: r0 = true
    //     0xbe7f68: add             x0, NULL, #0x20  ; true
    // 0xbe7f6c: StoreField: r2->field_43 = r0
    //     0xbe7f6c: stur            w0, [x2, #0x43]
    // 0xbe7f70: r1 = Instance_BoxShape
    //     0xbe7f70: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbe7f74: ldr             x1, [x1, #0x80]
    // 0xbe7f78: StoreField: r2->field_47 = r1
    //     0xbe7f78: stur            w1, [x2, #0x47]
    // 0xbe7f7c: StoreField: r2->field_6f = r0
    //     0xbe7f7c: stur            w0, [x2, #0x6f]
    // 0xbe7f80: r1 = false
    //     0xbe7f80: add             x1, NULL, #0x30  ; false
    // 0xbe7f84: StoreField: r2->field_73 = r1
    //     0xbe7f84: stur            w1, [x2, #0x73]
    // 0xbe7f88: StoreField: r2->field_83 = r0
    //     0xbe7f88: stur            w0, [x2, #0x83]
    // 0xbe7f8c: StoreField: r2->field_7b = r1
    //     0xbe7f8c: stur            w1, [x2, #0x7b]
    // 0xbe7f90: r0 = AnimatedContainer()
    //     0xbe7f90: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xbe7f94: stur            x0, [fp, #-8]
    // 0xbe7f98: r16 = Instance_Cubic
    //     0xbe7f98: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xbe7f9c: ldr             x16, [x16, #0xaf8]
    // 0xbe7fa0: str             x16, [SP]
    // 0xbe7fa4: mov             x1, x0
    // 0xbe7fa8: ldur            x2, [fp, #-0x10]
    // 0xbe7fac: r3 = Instance_Duration
    //     0xbe7fac: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e150] Obj!Duration@d77761
    //     0xbe7fb0: ldr             x3, [x3, #0x150]
    // 0xbe7fb4: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xbe7fb4: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xbe7fb8: ldr             x4, [x4, #0xbc8]
    // 0xbe7fbc: r0 = AnimatedContainer()
    //     0xbe7fbc: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xbe7fc0: ldur            x0, [fp, #-8]
    // 0xbe7fc4: LeaveFrame
    //     0xbe7fc4: mov             SP, fp
    //     0xbe7fc8: ldp             fp, lr, [SP], #0x10
    // 0xbe7fcc: ret
    //     0xbe7fcc: ret             
    // 0xbe7fd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe7fd0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe7fd4: b               #0xbe7114
    // 0xbe7fd8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xbe7fd8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xbe7fdc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe7fdc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe7fe0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe7fe0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe7fe4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe7fe4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe7fe8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbe7fe8: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbe7fec: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbe7fec: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbe7ff0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbe7ff0: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbe7ff4: SaveReg d0
    //     0xbe7ff4: str             q0, [SP, #-0x10]!
    // 0xbe7ff8: SaveReg r1
    //     0xbe7ff8: str             x1, [SP, #-8]!
    // 0xbe7ffc: r0 = AllocateDouble()
    //     0xbe7ffc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbe8000: RestoreReg r1
    //     0xbe8000: ldr             x1, [SP], #8
    // 0xbe8004: RestoreReg d0
    //     0xbe8004: ldr             q0, [SP], #0x10
    // 0xbe8008: b               #0xbe79e8
    // 0xbe800c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe800c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe8010: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe8010: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe8014: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe8014: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbe8018, size: 0x13c
    // 0xbe8018: EnterFrame
    //     0xbe8018: stp             fp, lr, [SP, #-0x10]!
    //     0xbe801c: mov             fp, SP
    // 0xbe8020: AllocStack(0x48)
    //     0xbe8020: sub             SP, SP, #0x48
    // 0xbe8024: SetupParameters()
    //     0xbe8024: ldr             x0, [fp, #0x10]
    //     0xbe8028: ldur            w1, [x0, #0x17]
    //     0xbe802c: add             x1, x1, HEAP, lsl #32
    // 0xbe8030: CheckStackOverflow
    //     0xbe8030: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe8034: cmp             SP, x16
    //     0xbe8038: b.ls            #0xbe8148
    // 0xbe803c: LoadField: r0 = r1->field_f
    //     0xbe803c: ldur            w0, [x1, #0xf]
    // 0xbe8040: DecompressPointer r0
    //     0xbe8040: add             x0, x0, HEAP, lsl #32
    // 0xbe8044: LoadField: r2 = r0->field_b
    //     0xbe8044: ldur            w2, [x0, #0xb]
    // 0xbe8048: DecompressPointer r2
    //     0xbe8048: add             x2, x2, HEAP, lsl #32
    // 0xbe804c: cmp             w2, NULL
    // 0xbe8050: b.eq            #0xbe8150
    // 0xbe8054: LoadField: r0 = r2->field_1b
    //     0xbe8054: ldur            w0, [x2, #0x1b]
    // 0xbe8058: DecompressPointer r0
    //     0xbe8058: add             x0, x0, HEAP, lsl #32
    // 0xbe805c: cmp             w0, NULL
    // 0xbe8060: b.ne            #0xbe8068
    // 0xbe8064: r0 = ""
    //     0xbe8064: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe8068: LoadField: r3 = r2->field_13
    //     0xbe8068: ldur            w3, [x2, #0x13]
    // 0xbe806c: DecompressPointer r3
    //     0xbe806c: add             x3, x3, HEAP, lsl #32
    // 0xbe8070: cmp             w3, NULL
    // 0xbe8074: b.ne            #0xbe807c
    // 0xbe8078: r3 = ""
    //     0xbe8078: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe807c: LoadField: r4 = r2->field_23
    //     0xbe807c: ldur            w4, [x2, #0x23]
    // 0xbe8080: DecompressPointer r4
    //     0xbe8080: add             x4, x4, HEAP, lsl #32
    // 0xbe8084: LoadField: r5 = r2->field_27
    //     0xbe8084: ldur            w5, [x2, #0x27]
    // 0xbe8088: DecompressPointer r5
    //     0xbe8088: add             x5, x5, HEAP, lsl #32
    // 0xbe808c: LoadField: r6 = r2->field_1f
    //     0xbe808c: ldur            w6, [x2, #0x1f]
    // 0xbe8090: DecompressPointer r6
    //     0xbe8090: add             x6, x6, HEAP, lsl #32
    // 0xbe8094: cmp             w6, NULL
    // 0xbe8098: b.ne            #0xbe80a0
    // 0xbe809c: r6 = ""
    //     0xbe809c: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe80a0: ArrayLoad: r7 = r2[0]  ; List_4
    //     0xbe80a0: ldur            w7, [x2, #0x17]
    // 0xbe80a4: DecompressPointer r7
    //     0xbe80a4: add             x7, x7, HEAP, lsl #32
    // 0xbe80a8: cmp             w7, NULL
    // 0xbe80ac: b.ne            #0xbe80b4
    // 0xbe80b0: r7 = ""
    //     0xbe80b0: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe80b4: LoadField: r8 = r1->field_13
    //     0xbe80b4: ldur            w8, [x1, #0x13]
    // 0xbe80b8: DecompressPointer r8
    //     0xbe80b8: add             x8, x8, HEAP, lsl #32
    // 0xbe80bc: cmp             w8, NULL
    // 0xbe80c0: b.ne            #0xbe80cc
    // 0xbe80c4: r1 = Null
    //     0xbe80c4: mov             x1, NULL
    // 0xbe80c8: b               #0xbe80d4
    // 0xbe80cc: LoadField: r1 = r8->field_2b
    //     0xbe80cc: ldur            w1, [x8, #0x2b]
    // 0xbe80d0: DecompressPointer r1
    //     0xbe80d0: add             x1, x1, HEAP, lsl #32
    // 0xbe80d4: cmp             w8, NULL
    // 0xbe80d8: b.ne            #0xbe80e4
    // 0xbe80dc: r8 = Null
    //     0xbe80dc: mov             x8, NULL
    // 0xbe80e0: b               #0xbe8104
    // 0xbe80e4: LoadField: r9 = r8->field_3b
    //     0xbe80e4: ldur            w9, [x8, #0x3b]
    // 0xbe80e8: DecompressPointer r9
    //     0xbe80e8: add             x9, x9, HEAP, lsl #32
    // 0xbe80ec: cmp             w9, NULL
    // 0xbe80f0: b.ne            #0xbe80fc
    // 0xbe80f4: r8 = Null
    //     0xbe80f4: mov             x8, NULL
    // 0xbe80f8: b               #0xbe8104
    // 0xbe80fc: LoadField: r8 = r9->field_b
    //     0xbe80fc: ldur            w8, [x9, #0xb]
    // 0xbe8100: DecompressPointer r8
    //     0xbe8100: add             x8, x8, HEAP, lsl #32
    // 0xbe8104: LoadField: r9 = r2->field_3f
    //     0xbe8104: ldur            w9, [x2, #0x3f]
    // 0xbe8108: DecompressPointer r9
    //     0xbe8108: add             x9, x9, HEAP, lsl #32
    // 0xbe810c: stp             x0, x9, [SP, #0x38]
    // 0xbe8110: stp             x4, x3, [SP, #0x28]
    // 0xbe8114: stp             x6, x5, [SP, #0x18]
    // 0xbe8118: stp             x1, x7, [SP, #8]
    // 0xbe811c: str             x8, [SP]
    // 0xbe8120: r4 = 0
    //     0xbe8120: movz            x4, #0
    // 0xbe8124: ldr             x0, [SP, #0x40]
    // 0xbe8128: r16 = UnlinkedCall_0x613b5c
    //     0xbe8128: add             x16, PP, #0x61, lsl #12  ; [pp+0x61cd0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe812c: add             x16, x16, #0xcd0
    // 0xbe8130: ldp             x5, lr, [x16]
    // 0xbe8134: blr             lr
    // 0xbe8138: r0 = Null
    //     0xbe8138: mov             x0, NULL
    // 0xbe813c: LeaveFrame
    //     0xbe813c: mov             SP, fp
    //     0xbe8140: ldp             fp, lr, [SP], #0x10
    // 0xbe8144: ret
    //     0xbe8144: ret             
    // 0xbe8148: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe8148: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe814c: b               #0xbe803c
    // 0xbe8150: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe8150: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3992, size: 0x4c, field offset: 0xc
//   const constructor, 
class _ProductGridItem extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80aa0, size: 0x24
    // 0xc80aa0: EnterFrame
    //     0xc80aa0: stp             fp, lr, [SP, #-0x10]!
    //     0xc80aa4: mov             fp, SP
    // 0xc80aa8: mov             x0, x1
    // 0xc80aac: r1 = <_ProductGridItem>
    //     0xc80aac: add             x1, PP, #0x53, lsl #12  ; [pp+0x53cc0] TypeArguments: <_ProductGridItem>
    //     0xc80ab0: ldr             x1, [x1, #0xcc0]
    // 0xc80ab4: r0 = _ProductGridItemState()
    //     0xc80ab4: bl              #0xc80ac4  ; Allocate_ProductGridItemStateStub -> _ProductGridItemState (size=0x14)
    // 0xc80ab8: LeaveFrame
    //     0xc80ab8: mov             SP, fp
    //     0xc80abc: ldp             fp, lr, [SP], #0x10
    // 0xc80ac0: ret
    //     0xc80ac0: ret             
  }
}

// class id: 4486, size: 0x5c, field offset: 0xc
//   const constructor, 
class ProductGridItemView extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0x1296830, size: 0x12d8
    // 0x1296830: EnterFrame
    //     0x1296830: stp             fp, lr, [SP, #-0x10]!
    //     0x1296834: mov             fp, SP
    // 0x1296838: AllocStack(0xf0)
    //     0x1296838: sub             SP, SP, #0xf0
    // 0x129683c: SetupParameters(ProductGridItemView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x129683c: mov             x0, x1
    //     0x1296840: stur            x1, [fp, #-8]
    //     0x1296844: mov             x1, x2
    //     0x1296848: stur            x2, [fp, #-0x10]
    // 0x129684c: CheckStackOverflow
    //     0x129684c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1296850: cmp             SP, x16
    //     0x1296854: b.ls            #0x1297aec
    // 0x1296858: r1 = 1
    //     0x1296858: movz            x1, #0x1
    // 0x129685c: r0 = AllocateContext()
    //     0x129685c: bl              #0x16f6108  ; AllocateContextStub
    // 0x1296860: mov             x1, x0
    // 0x1296864: ldur            x0, [fp, #-8]
    // 0x1296868: stur            x1, [fp, #-0x20]
    // 0x129686c: StoreField: r1->field_f = r0
    //     0x129686c: stur            w0, [x1, #0xf]
    // 0x1296870: LoadField: r2 = r0->field_f
    //     0x1296870: ldur            w2, [x0, #0xf]
    // 0x1296874: DecompressPointer r2
    //     0x1296874: add             x2, x2, HEAP, lsl #32
    // 0x1296878: stur            x2, [fp, #-0x18]
    // 0x129687c: cmp             w2, NULL
    // 0x1296880: b.ne            #0x129688c
    // 0x1296884: r3 = Null
    //     0x1296884: mov             x3, NULL
    // 0x1296888: b               #0x12968a4
    // 0x129688c: LoadField: r3 = r2->field_7
    //     0x129688c: ldur            w3, [x2, #7]
    // 0x1296890: cbz             w3, #0x129689c
    // 0x1296894: r4 = false
    //     0x1296894: add             x4, NULL, #0x30  ; false
    // 0x1296898: b               #0x12968a0
    // 0x129689c: r4 = true
    //     0x129689c: add             x4, NULL, #0x20  ; true
    // 0x12968a0: mov             x3, x4
    // 0x12968a4: cmp             w3, NULL
    // 0x12968a8: b.eq            #0x129690c
    // 0x12968ac: tbnz            w3, #4, #0x129690c
    // 0x12968b0: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x12968b0: ldur            w3, [x0, #0x17]
    // 0x12968b4: DecompressPointer r3
    //     0x12968b4: add             x3, x3, HEAP, lsl #32
    // 0x12968b8: cmp             w3, NULL
    // 0x12968bc: b.ne            #0x12968c8
    // 0x12968c0: r3 = Null
    //     0x12968c0: mov             x3, NULL
    // 0x12968c4: b               #0x12968f8
    // 0x12968c8: LoadField: r4 = r3->field_7
    //     0x12968c8: ldur            w4, [x3, #7]
    // 0x12968cc: DecompressPointer r4
    //     0x12968cc: add             x4, x4, HEAP, lsl #32
    // 0x12968d0: cmp             w4, NULL
    // 0x12968d4: b.ne            #0x12968e0
    // 0x12968d8: r3 = Null
    //     0x12968d8: mov             x3, NULL
    // 0x12968dc: b               #0x12968f8
    // 0x12968e0: LoadField: r3 = r4->field_7
    //     0x12968e0: ldur            w3, [x4, #7]
    // 0x12968e4: cbnz            w3, #0x12968f0
    // 0x12968e8: r4 = false
    //     0x12968e8: add             x4, NULL, #0x30  ; false
    // 0x12968ec: b               #0x12968f4
    // 0x12968f0: r4 = true
    //     0x12968f0: add             x4, NULL, #0x20  ; true
    // 0x12968f4: mov             x3, x4
    // 0x12968f8: cmp             w3, NULL
    // 0x12968fc: b.eq            #0x129690c
    // 0x1296900: tbnz            w3, #4, #0x129690c
    // 0x1296904: d0 = 12.000000
    //     0x1296904: fmov            d0, #12.00000000
    // 0x1296908: b               #0x1296910
    // 0x129690c: d0 = 0.000000
    //     0x129690c: eor             v0.16b, v0.16b, v0.16b
    // 0x1296910: stur            d0, [fp, #-0xd8]
    // 0x1296914: r0 = EdgeInsets()
    //     0x1296914: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x1296918: d0 = 12.000000
    //     0x1296918: fmov            d0, #12.00000000
    // 0x129691c: stur            x0, [fp, #-0x40]
    // 0x1296920: StoreField: r0->field_7 = d0
    //     0x1296920: stur            d0, [x0, #7]
    // 0x1296924: ldur            d1, [fp, #-0xd8]
    // 0x1296928: StoreField: r0->field_f = d1
    //     0x1296928: stur            d1, [x0, #0xf]
    // 0x129692c: ArrayStore: r0[0] = d0  ; List_8
    //     0x129692c: stur            d0, [x0, #0x17]
    // 0x1296930: d0 = 24.000000
    //     0x1296930: fmov            d0, #24.00000000
    // 0x1296934: StoreField: r0->field_1f = d0
    //     0x1296934: stur            d0, [x0, #0x1f]
    // 0x1296938: ldur            x3, [fp, #-8]
    // 0x129693c: LoadField: r1 = r3->field_1b
    //     0x129693c: ldur            w1, [x3, #0x1b]
    // 0x1296940: DecompressPointer r1
    //     0x1296940: add             x1, x1, HEAP, lsl #32
    // 0x1296944: tbnz            w1, #4, #0x1296954
    // 0x1296948: r4 = Instance_MainAxisAlignment
    //     0x1296948: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x129694c: ldr             x4, [x4, #0xa08]
    // 0x1296950: b               #0x129695c
    // 0x1296954: r4 = Instance_MainAxisAlignment
    //     0x1296954: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x1296958: ldr             x4, [x4, #0xab0]
    // 0x129695c: stur            x4, [fp, #-0x38]
    // 0x1296960: LoadField: r5 = r3->field_23
    //     0x1296960: ldur            w5, [x3, #0x23]
    // 0x1296964: DecompressPointer r5
    //     0x1296964: add             x5, x5, HEAP, lsl #32
    // 0x1296968: stur            x5, [fp, #-0x30]
    // 0x129696c: LoadField: r1 = r5->field_7
    //     0x129696c: ldur            w1, [x5, #7]
    // 0x1296970: DecompressPointer r1
    //     0x1296970: add             x1, x1, HEAP, lsl #32
    // 0x1296974: cmp             w1, NULL
    // 0x1296978: b.ne            #0x1296984
    // 0x129697c: r1 = Instance_TitleAlignment
    //     0x129697c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x1296980: ldr             x1, [x1, #0x518]
    // 0x1296984: r16 = Instance_TitleAlignment
    //     0x1296984: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0x1296988: ldr             x16, [x16, #0x520]
    // 0x129698c: cmp             w1, w16
    // 0x1296990: b.ne            #0x12969a0
    // 0x1296994: r7 = Instance_CrossAxisAlignment
    //     0x1296994: add             x7, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0x1296998: ldr             x7, [x7, #0xc68]
    // 0x129699c: b               #0x12969c4
    // 0x12969a0: r16 = Instance_TitleAlignment
    //     0x12969a0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x12969a4: ldr             x16, [x16, #0x518]
    // 0x12969a8: cmp             w1, w16
    // 0x12969ac: b.ne            #0x12969bc
    // 0x12969b0: r7 = Instance_CrossAxisAlignment
    //     0x12969b0: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x12969b4: ldr             x7, [x7, #0x890]
    // 0x12969b8: b               #0x12969c4
    // 0x12969bc: r7 = Instance_CrossAxisAlignment
    //     0x12969bc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x12969c0: ldr             x7, [x7, #0xa18]
    // 0x12969c4: ldur            x6, [fp, #-0x18]
    // 0x12969c8: stur            x7, [fp, #-0x28]
    // 0x12969cc: r1 = <Widget>
    //     0x12969cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x12969d0: r2 = 0
    //     0x12969d0: movz            x2, #0
    // 0x12969d4: r0 = _GrowableList()
    //     0x12969d4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x12969d8: mov             x2, x0
    // 0x12969dc: ldur            x1, [fp, #-0x18]
    // 0x12969e0: stur            x2, [fp, #-0x48]
    // 0x12969e4: cmp             w1, NULL
    // 0x12969e8: b.ne            #0x12969f4
    // 0x12969ec: r0 = Null
    //     0x12969ec: mov             x0, NULL
    // 0x12969f0: b               #0x1296a0c
    // 0x12969f4: LoadField: r0 = r1->field_7
    //     0x12969f4: ldur            w0, [x1, #7]
    // 0x12969f8: cbnz            w0, #0x1296a04
    // 0x12969fc: r3 = false
    //     0x12969fc: add             x3, NULL, #0x30  ; false
    // 0x1296a00: b               #0x1296a08
    // 0x1296a04: r3 = true
    //     0x1296a04: add             x3, NULL, #0x20  ; true
    // 0x1296a08: mov             x0, x3
    // 0x1296a0c: cmp             w0, NULL
    // 0x1296a10: b.eq            #0x1296ba4
    // 0x1296a14: tbnz            w0, #4, #0x1296ba4
    // 0x1296a18: cmp             w1, NULL
    // 0x1296a1c: b.ne            #0x1296a28
    // 0x1296a20: r0 = Null
    //     0x1296a20: mov             x0, NULL
    // 0x1296a24: b               #0x1296a40
    // 0x1296a28: r0 = LoadClassIdInstr(r1)
    //     0x1296a28: ldur            x0, [x1, #-1]
    //     0x1296a2c: ubfx            x0, x0, #0xc, #0x14
    // 0x1296a30: str             x1, [SP]
    // 0x1296a34: r0 = GDT[cid_x0 + -0x1000]()
    //     0x1296a34: sub             lr, x0, #1, lsl #12
    //     0x1296a38: ldr             lr, [x21, lr, lsl #3]
    //     0x1296a3c: blr             lr
    // 0x1296a40: cmp             w0, NULL
    // 0x1296a44: b.ne            #0x1296a50
    // 0x1296a48: r2 = ""
    //     0x1296a48: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1296a4c: b               #0x1296a54
    // 0x1296a50: mov             x2, x0
    // 0x1296a54: ldur            x0, [fp, #-0x30]
    // 0x1296a58: stur            x2, [fp, #-0x50]
    // 0x1296a5c: LoadField: r1 = r0->field_7
    //     0x1296a5c: ldur            w1, [x0, #7]
    // 0x1296a60: DecompressPointer r1
    //     0x1296a60: add             x1, x1, HEAP, lsl #32
    // 0x1296a64: cmp             w1, NULL
    // 0x1296a68: b.ne            #0x1296a78
    // 0x1296a6c: r0 = Instance_TitleAlignment
    //     0x1296a6c: add             x0, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x1296a70: ldr             x0, [x0, #0x518]
    // 0x1296a74: b               #0x1296a7c
    // 0x1296a78: mov             x0, x1
    // 0x1296a7c: r16 = Instance_TitleAlignment
    //     0x1296a7c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0x1296a80: ldr             x16, [x16, #0x520]
    // 0x1296a84: cmp             w0, w16
    // 0x1296a88: b.ne            #0x1296a94
    // 0x1296a8c: r3 = Instance_TextAlign
    //     0x1296a8c: ldr             x3, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0x1296a90: b               #0x1296ab0
    // 0x1296a94: r16 = Instance_TitleAlignment
    //     0x1296a94: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x1296a98: ldr             x16, [x16, #0x518]
    // 0x1296a9c: cmp             w0, w16
    // 0x1296aa0: b.ne            #0x1296aac
    // 0x1296aa4: r3 = Instance_TextAlign
    //     0x1296aa4: ldr             x3, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0x1296aa8: b               #0x1296ab0
    // 0x1296aac: r3 = Instance_TextAlign
    //     0x1296aac: ldr             x3, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0x1296ab0: ldur            x0, [fp, #-0x48]
    // 0x1296ab4: ldur            x1, [fp, #-0x10]
    // 0x1296ab8: stur            x3, [fp, #-0x30]
    // 0x1296abc: r0 = of()
    //     0x1296abc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1296ac0: LoadField: r1 = r0->field_87
    //     0x1296ac0: ldur            w1, [x0, #0x87]
    // 0x1296ac4: DecompressPointer r1
    //     0x1296ac4: add             x1, x1, HEAP, lsl #32
    // 0x1296ac8: LoadField: r0 = r1->field_27
    //     0x1296ac8: ldur            w0, [x1, #0x27]
    // 0x1296acc: DecompressPointer r0
    //     0x1296acc: add             x0, x0, HEAP, lsl #32
    // 0x1296ad0: r16 = 21.000000
    //     0x1296ad0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x1296ad4: ldr             x16, [x16, #0x9b0]
    // 0x1296ad8: r30 = Instance_Color
    //     0x1296ad8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1296adc: stp             lr, x16, [SP]
    // 0x1296ae0: mov             x1, x0
    // 0x1296ae4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1296ae4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1296ae8: ldr             x4, [x4, #0xaa0]
    // 0x1296aec: r0 = copyWith()
    //     0x1296aec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1296af0: stur            x0, [fp, #-0x58]
    // 0x1296af4: r0 = Text()
    //     0x1296af4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1296af8: mov             x2, x0
    // 0x1296afc: ldur            x0, [fp, #-0x50]
    // 0x1296b00: stur            x2, [fp, #-0x68]
    // 0x1296b04: StoreField: r2->field_b = r0
    //     0x1296b04: stur            w0, [x2, #0xb]
    // 0x1296b08: ldur            x0, [fp, #-0x58]
    // 0x1296b0c: StoreField: r2->field_13 = r0
    //     0x1296b0c: stur            w0, [x2, #0x13]
    // 0x1296b10: ldur            x0, [fp, #-0x30]
    // 0x1296b14: StoreField: r2->field_1b = r0
    //     0x1296b14: stur            w0, [x2, #0x1b]
    // 0x1296b18: ldur            x0, [fp, #-0x48]
    // 0x1296b1c: LoadField: r1 = r0->field_b
    //     0x1296b1c: ldur            w1, [x0, #0xb]
    // 0x1296b20: LoadField: r3 = r0->field_f
    //     0x1296b20: ldur            w3, [x0, #0xf]
    // 0x1296b24: DecompressPointer r3
    //     0x1296b24: add             x3, x3, HEAP, lsl #32
    // 0x1296b28: LoadField: r4 = r3->field_b
    //     0x1296b28: ldur            w4, [x3, #0xb]
    // 0x1296b2c: r3 = LoadInt32Instr(r1)
    //     0x1296b2c: sbfx            x3, x1, #1, #0x1f
    // 0x1296b30: stur            x3, [fp, #-0x60]
    // 0x1296b34: r1 = LoadInt32Instr(r4)
    //     0x1296b34: sbfx            x1, x4, #1, #0x1f
    // 0x1296b38: cmp             x3, x1
    // 0x1296b3c: b.ne            #0x1296b48
    // 0x1296b40: mov             x1, x0
    // 0x1296b44: r0 = _growToNextCapacity()
    //     0x1296b44: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1296b48: ldur            x2, [fp, #-0x48]
    // 0x1296b4c: ldur            x3, [fp, #-0x60]
    // 0x1296b50: add             x4, x3, #1
    // 0x1296b54: lsl             x0, x4, #1
    // 0x1296b58: StoreField: r2->field_b = r0
    //     0x1296b58: stur            w0, [x2, #0xb]
    // 0x1296b5c: LoadField: r5 = r2->field_f
    //     0x1296b5c: ldur            w5, [x2, #0xf]
    // 0x1296b60: DecompressPointer r5
    //     0x1296b60: add             x5, x5, HEAP, lsl #32
    // 0x1296b64: mov             x1, x5
    // 0x1296b68: ldur            x0, [fp, #-0x68]
    // 0x1296b6c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1296b6c: add             x25, x1, x3, lsl #2
    //     0x1296b70: add             x25, x25, #0xf
    //     0x1296b74: str             w0, [x25]
    //     0x1296b78: tbz             w0, #0, #0x1296b94
    //     0x1296b7c: ldurb           w16, [x1, #-1]
    //     0x1296b80: ldurb           w17, [x0, #-1]
    //     0x1296b84: and             x16, x17, x16, lsr #2
    //     0x1296b88: tst             x16, HEAP, lsr #32
    //     0x1296b8c: b.eq            #0x1296b94
    //     0x1296b90: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1296b94: mov             x3, x4
    // 0x1296b98: mov             x1, x5
    // 0x1296b9c: mov             x0, x2
    // 0x1296ba0: b               #0x1296c00
    // 0x1296ba4: LoadField: r0 = r2->field_b
    //     0x1296ba4: ldur            w0, [x2, #0xb]
    // 0x1296ba8: LoadField: r1 = r2->field_f
    //     0x1296ba8: ldur            w1, [x2, #0xf]
    // 0x1296bac: DecompressPointer r1
    //     0x1296bac: add             x1, x1, HEAP, lsl #32
    // 0x1296bb0: LoadField: r3 = r1->field_b
    //     0x1296bb0: ldur            w3, [x1, #0xb]
    // 0x1296bb4: r4 = LoadInt32Instr(r0)
    //     0x1296bb4: sbfx            x4, x0, #1, #0x1f
    // 0x1296bb8: stur            x4, [fp, #-0x60]
    // 0x1296bbc: r0 = LoadInt32Instr(r3)
    //     0x1296bbc: sbfx            x0, x3, #1, #0x1f
    // 0x1296bc0: cmp             x4, x0
    // 0x1296bc4: b.ne            #0x1296bd0
    // 0x1296bc8: mov             x1, x2
    // 0x1296bcc: r0 = _growToNextCapacity()
    //     0x1296bcc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1296bd0: ldur            x0, [fp, #-0x48]
    // 0x1296bd4: ldur            x1, [fp, #-0x60]
    // 0x1296bd8: add             x2, x1, #1
    // 0x1296bdc: lsl             x3, x2, #1
    // 0x1296be0: StoreField: r0->field_b = r3
    //     0x1296be0: stur            w3, [x0, #0xb]
    // 0x1296be4: LoadField: r3 = r0->field_f
    //     0x1296be4: ldur            w3, [x0, #0xf]
    // 0x1296be8: DecompressPointer r3
    //     0x1296be8: add             x3, x3, HEAP, lsl #32
    // 0x1296bec: add             x4, x3, x1, lsl #2
    // 0x1296bf0: r16 = Instance_SizedBox
    //     0x1296bf0: ldr             x16, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1296bf4: StoreField: r4->field_f = r16
    //     0x1296bf4: stur            w16, [x4, #0xf]
    // 0x1296bf8: mov             x1, x3
    // 0x1296bfc: mov             x3, x2
    // 0x1296c00: ldur            x2, [fp, #-8]
    // 0x1296c04: stur            x3, [fp, #-0x60]
    // 0x1296c08: LoadField: r4 = r2->field_13
    //     0x1296c08: ldur            w4, [x2, #0x13]
    // 0x1296c0c: DecompressPointer r4
    //     0x1296c0c: add             x4, x4, HEAP, lsl #32
    // 0x1296c10: stur            x4, [fp, #-0x30]
    // 0x1296c14: tbnz            w4, #4, #0x1296cd8
    // 0x1296c18: ArrayLoad: r5 = r2[0]  ; List_4
    //     0x1296c18: ldur            w5, [x2, #0x17]
    // 0x1296c1c: DecompressPointer r5
    //     0x1296c1c: add             x5, x5, HEAP, lsl #32
    // 0x1296c20: cmp             w5, NULL
    // 0x1296c24: b.ne            #0x1296c30
    // 0x1296c28: r5 = Null
    //     0x1296c28: mov             x5, NULL
    // 0x1296c2c: b               #0x1296c60
    // 0x1296c30: LoadField: r6 = r5->field_7
    //     0x1296c30: ldur            w6, [x5, #7]
    // 0x1296c34: DecompressPointer r6
    //     0x1296c34: add             x6, x6, HEAP, lsl #32
    // 0x1296c38: cmp             w6, NULL
    // 0x1296c3c: b.ne            #0x1296c48
    // 0x1296c40: r5 = Null
    //     0x1296c40: mov             x5, NULL
    // 0x1296c44: b               #0x1296c60
    // 0x1296c48: LoadField: r5 = r6->field_7
    //     0x1296c48: ldur            w5, [x6, #7]
    // 0x1296c4c: cbnz            w5, #0x1296c58
    // 0x1296c50: r6 = false
    //     0x1296c50: add             x6, NULL, #0x30  ; false
    // 0x1296c54: b               #0x1296c5c
    // 0x1296c58: r6 = true
    //     0x1296c58: add             x6, NULL, #0x20  ; true
    // 0x1296c5c: mov             x5, x6
    // 0x1296c60: cmp             w5, NULL
    // 0x1296c64: b.ne            #0x1296c70
    // 0x1296c68: mov             x2, x0
    // 0x1296c6c: b               #0x1296cdc
    // 0x1296c70: tbnz            w5, #4, #0x1296cd0
    // 0x1296c74: LoadField: r5 = r1->field_b
    //     0x1296c74: ldur            w5, [x1, #0xb]
    // 0x1296c78: r1 = LoadInt32Instr(r5)
    //     0x1296c78: sbfx            x1, x5, #1, #0x1f
    // 0x1296c7c: cmp             x3, x1
    // 0x1296c80: b.ne            #0x1296c8c
    // 0x1296c84: mov             x1, x0
    // 0x1296c88: r0 = _growToNextCapacity()
    //     0x1296c88: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1296c8c: ldur            x2, [fp, #-0x48]
    // 0x1296c90: ldur            x3, [fp, #-0x60]
    // 0x1296c94: add             x4, x3, #1
    // 0x1296c98: lsl             x0, x4, #1
    // 0x1296c9c: StoreField: r2->field_b = r0
    //     0x1296c9c: stur            w0, [x2, #0xb]
    // 0x1296ca0: mov             x0, x4
    // 0x1296ca4: mov             x1, x3
    // 0x1296ca8: cmp             x1, x0
    // 0x1296cac: b.hs            #0x1297af4
    // 0x1296cb0: LoadField: r0 = r2->field_f
    //     0x1296cb0: ldur            w0, [x2, #0xf]
    // 0x1296cb4: DecompressPointer r0
    //     0x1296cb4: add             x0, x0, HEAP, lsl #32
    // 0x1296cb8: add             x1, x0, x3, lsl #2
    // 0x1296cbc: r16 = Instance_SizedBox
    //     0x1296cbc: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x1296cc0: ldr             x16, [x16, #0xc70]
    // 0x1296cc4: StoreField: r1->field_f = r16
    //     0x1296cc4: stur            w16, [x1, #0xf]
    // 0x1296cc8: mov             x3, x4
    // 0x1296ccc: b               #0x1296d30
    // 0x1296cd0: mov             x2, x0
    // 0x1296cd4: b               #0x1296cdc
    // 0x1296cd8: mov             x2, x0
    // 0x1296cdc: LoadField: r0 = r1->field_b
    //     0x1296cdc: ldur            w0, [x1, #0xb]
    // 0x1296ce0: r1 = LoadInt32Instr(r0)
    //     0x1296ce0: sbfx            x1, x0, #1, #0x1f
    // 0x1296ce4: cmp             x3, x1
    // 0x1296ce8: b.ne            #0x1296cf4
    // 0x1296cec: mov             x1, x2
    // 0x1296cf0: r0 = _growToNextCapacity()
    //     0x1296cf0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1296cf4: ldur            x2, [fp, #-0x48]
    // 0x1296cf8: ldur            x3, [fp, #-0x60]
    // 0x1296cfc: add             x4, x3, #1
    // 0x1296d00: lsl             x0, x4, #1
    // 0x1296d04: StoreField: r2->field_b = r0
    //     0x1296d04: stur            w0, [x2, #0xb]
    // 0x1296d08: mov             x0, x4
    // 0x1296d0c: mov             x1, x3
    // 0x1296d10: cmp             x1, x0
    // 0x1296d14: b.hs            #0x1297af8
    // 0x1296d18: LoadField: r0 = r2->field_f
    //     0x1296d18: ldur            w0, [x2, #0xf]
    // 0x1296d1c: DecompressPointer r0
    //     0x1296d1c: add             x0, x0, HEAP, lsl #32
    // 0x1296d20: add             x1, x0, x3, lsl #2
    // 0x1296d24: r16 = Instance_SizedBox
    //     0x1296d24: ldr             x16, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1296d28: StoreField: r1->field_f = r16
    //     0x1296d28: stur            w16, [x1, #0xf]
    // 0x1296d2c: mov             x3, x4
    // 0x1296d30: ldur            x1, [fp, #-0x30]
    // 0x1296d34: stur            x3, [fp, #-0x78]
    // 0x1296d38: tbnz            w1, #4, #0x1297000
    // 0x1296d3c: ldur            x4, [fp, #-8]
    // 0x1296d40: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x1296d40: ldur            w5, [x4, #0x17]
    // 0x1296d44: DecompressPointer r5
    //     0x1296d44: add             x5, x5, HEAP, lsl #32
    // 0x1296d48: cmp             w5, NULL
    // 0x1296d4c: b.ne            #0x1296d58
    // 0x1296d50: r6 = Null
    //     0x1296d50: mov             x6, NULL
    // 0x1296d54: b               #0x1296d84
    // 0x1296d58: LoadField: r6 = r5->field_7
    //     0x1296d58: ldur            w6, [x5, #7]
    // 0x1296d5c: DecompressPointer r6
    //     0x1296d5c: add             x6, x6, HEAP, lsl #32
    // 0x1296d60: cmp             w6, NULL
    // 0x1296d64: b.ne            #0x1296d70
    // 0x1296d68: r6 = Null
    //     0x1296d68: mov             x6, NULL
    // 0x1296d6c: b               #0x1296d84
    // 0x1296d70: LoadField: r7 = r6->field_7
    //     0x1296d70: ldur            w7, [x6, #7]
    // 0x1296d74: cbnz            w7, #0x1296d80
    // 0x1296d78: r6 = false
    //     0x1296d78: add             x6, NULL, #0x30  ; false
    // 0x1296d7c: b               #0x1296d84
    // 0x1296d80: r6 = true
    //     0x1296d80: add             x6, NULL, #0x20  ; true
    // 0x1296d84: cmp             w6, NULL
    // 0x1296d88: b.eq            #0x1297000
    // 0x1296d8c: tbnz            w6, #4, #0x1297000
    // 0x1296d90: cmp             w5, NULL
    // 0x1296d94: b.ne            #0x1296da0
    // 0x1296d98: r0 = Null
    //     0x1296d98: mov             x0, NULL
    // 0x1296d9c: b               #0x1296dcc
    // 0x1296da0: LoadField: r0 = r5->field_7
    //     0x1296da0: ldur            w0, [x5, #7]
    // 0x1296da4: DecompressPointer r0
    //     0x1296da4: add             x0, x0, HEAP, lsl #32
    // 0x1296da8: cmp             w0, NULL
    // 0x1296dac: b.ne            #0x1296db8
    // 0x1296db0: r0 = Null
    //     0x1296db0: mov             x0, NULL
    // 0x1296db4: b               #0x1296dcc
    // 0x1296db8: LoadField: r3 = r0->field_7
    //     0x1296db8: ldur            w3, [x0, #7]
    // 0x1296dbc: cbnz            w3, #0x1296dc8
    // 0x1296dc0: r0 = false
    //     0x1296dc0: add             x0, NULL, #0x30  ; false
    // 0x1296dc4: b               #0x1296dcc
    // 0x1296dc8: r0 = true
    //     0x1296dc8: add             x0, NULL, #0x20  ; true
    // 0x1296dcc: cmp             w0, NULL
    // 0x1296dd0: b.ne            #0x1296ddc
    // 0x1296dd4: r3 = false
    //     0x1296dd4: add             x3, NULL, #0x30  ; false
    // 0x1296dd8: b               #0x1296de0
    // 0x1296ddc: mov             x3, x0
    // 0x1296de0: stur            x3, [fp, #-0x50]
    // 0x1296de4: cmp             w5, NULL
    // 0x1296de8: b.ne            #0x1296df4
    // 0x1296dec: r0 = Null
    //     0x1296dec: mov             x0, NULL
    // 0x1296df0: b               #0x1296e28
    // 0x1296df4: LoadField: r0 = r5->field_7
    //     0x1296df4: ldur            w0, [x5, #7]
    // 0x1296df8: DecompressPointer r0
    //     0x1296df8: add             x0, x0, HEAP, lsl #32
    // 0x1296dfc: cmp             w0, NULL
    // 0x1296e00: b.ne            #0x1296e0c
    // 0x1296e04: r0 = Null
    //     0x1296e04: mov             x0, NULL
    // 0x1296e08: b               #0x1296e28
    // 0x1296e0c: r5 = LoadClassIdInstr(r0)
    //     0x1296e0c: ldur            x5, [x0, #-1]
    //     0x1296e10: ubfx            x5, x5, #0xc, #0x14
    // 0x1296e14: str             x0, [SP]
    // 0x1296e18: mov             x0, x5
    // 0x1296e1c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x1296e1c: sub             lr, x0, #1, lsl #12
    //     0x1296e20: ldr             lr, [x21, lr, lsl #3]
    //     0x1296e24: blr             lr
    // 0x1296e28: cmp             w0, NULL
    // 0x1296e2c: b.ne            #0x1296e38
    // 0x1296e30: r3 = ""
    //     0x1296e30: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1296e34: b               #0x1296e3c
    // 0x1296e38: mov             x3, x0
    // 0x1296e3c: ldur            x0, [fp, #-0x48]
    // 0x1296e40: ldur            x2, [fp, #-0x50]
    // 0x1296e44: ldur            x1, [fp, #-0x10]
    // 0x1296e48: stur            x3, [fp, #-0x58]
    // 0x1296e4c: r0 = of()
    //     0x1296e4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1296e50: LoadField: r1 = r0->field_87
    //     0x1296e50: ldur            w1, [x0, #0x87]
    // 0x1296e54: DecompressPointer r1
    //     0x1296e54: add             x1, x1, HEAP, lsl #32
    // 0x1296e58: LoadField: r0 = r1->field_2b
    //     0x1296e58: ldur            w0, [x1, #0x2b]
    // 0x1296e5c: DecompressPointer r0
    //     0x1296e5c: add             x0, x0, HEAP, lsl #32
    // 0x1296e60: stur            x0, [fp, #-0x68]
    // 0x1296e64: r1 = Instance_Color
    //     0x1296e64: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1296e68: d0 = 0.700000
    //     0x1296e68: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1296e6c: ldr             d0, [x17, #0xf48]
    // 0x1296e70: r0 = withOpacity()
    //     0x1296e70: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1296e74: r16 = 12.000000
    //     0x1296e74: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1296e78: ldr             x16, [x16, #0x9e8]
    // 0x1296e7c: stp             x0, x16, [SP, #8]
    // 0x1296e80: r16 = Instance_TextDecoration
    //     0x1296e80: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0x1296e84: ldr             x16, [x16, #0x10]
    // 0x1296e88: str             x16, [SP]
    // 0x1296e8c: ldur            x1, [fp, #-0x68]
    // 0x1296e90: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0x1296e90: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0x1296e94: ldr             x4, [x4, #0xe38]
    // 0x1296e98: r0 = copyWith()
    //     0x1296e98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1296e9c: stur            x0, [fp, #-0x68]
    // 0x1296ea0: r0 = Text()
    //     0x1296ea0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1296ea4: mov             x1, x0
    // 0x1296ea8: ldur            x0, [fp, #-0x58]
    // 0x1296eac: stur            x1, [fp, #-0x70]
    // 0x1296eb0: StoreField: r1->field_b = r0
    //     0x1296eb0: stur            w0, [x1, #0xb]
    // 0x1296eb4: ldur            x0, [fp, #-0x68]
    // 0x1296eb8: StoreField: r1->field_13 = r0
    //     0x1296eb8: stur            w0, [x1, #0x13]
    // 0x1296ebc: r0 = Visibility()
    //     0x1296ebc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1296ec0: mov             x1, x0
    // 0x1296ec4: ldur            x0, [fp, #-0x70]
    // 0x1296ec8: stur            x1, [fp, #-0x58]
    // 0x1296ecc: StoreField: r1->field_b = r0
    //     0x1296ecc: stur            w0, [x1, #0xb]
    // 0x1296ed0: r0 = Instance_SizedBox
    //     0x1296ed0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1296ed4: StoreField: r1->field_f = r0
    //     0x1296ed4: stur            w0, [x1, #0xf]
    // 0x1296ed8: ldur            x0, [fp, #-0x50]
    // 0x1296edc: StoreField: r1->field_13 = r0
    //     0x1296edc: stur            w0, [x1, #0x13]
    // 0x1296ee0: r0 = false
    //     0x1296ee0: add             x0, NULL, #0x30  ; false
    // 0x1296ee4: ArrayStore: r1[0] = r0  ; List_4
    //     0x1296ee4: stur            w0, [x1, #0x17]
    // 0x1296ee8: StoreField: r1->field_1b = r0
    //     0x1296ee8: stur            w0, [x1, #0x1b]
    // 0x1296eec: StoreField: r1->field_1f = r0
    //     0x1296eec: stur            w0, [x1, #0x1f]
    // 0x1296ef0: StoreField: r1->field_23 = r0
    //     0x1296ef0: stur            w0, [x1, #0x23]
    // 0x1296ef4: StoreField: r1->field_27 = r0
    //     0x1296ef4: stur            w0, [x1, #0x27]
    // 0x1296ef8: StoreField: r1->field_2b = r0
    //     0x1296ef8: stur            w0, [x1, #0x2b]
    // 0x1296efc: r0 = InkWell()
    //     0x1296efc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1296f00: mov             x3, x0
    // 0x1296f04: ldur            x0, [fp, #-0x58]
    // 0x1296f08: stur            x3, [fp, #-0x50]
    // 0x1296f0c: StoreField: r3->field_b = r0
    //     0x1296f0c: stur            w0, [x3, #0xb]
    // 0x1296f10: ldur            x2, [fp, #-0x20]
    // 0x1296f14: r1 = Function '<anonymous closure>':.
    //     0x1296f14: add             x1, PP, #0x48, lsl #12  ; [pp+0x48460] AnonymousClosure: (0x1297b14), in [package:customer_app/app/presentation/views/line/home/<USER>/product_grid_item_view.dart] ProductGridItemView::build (0x1296830)
    //     0x1296f18: ldr             x1, [x1, #0x460]
    // 0x1296f1c: r0 = AllocateClosure()
    //     0x1296f1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1296f20: mov             x1, x0
    // 0x1296f24: ldur            x0, [fp, #-0x50]
    // 0x1296f28: StoreField: r0->field_f = r1
    //     0x1296f28: stur            w1, [x0, #0xf]
    // 0x1296f2c: r1 = true
    //     0x1296f2c: add             x1, NULL, #0x20  ; true
    // 0x1296f30: StoreField: r0->field_43 = r1
    //     0x1296f30: stur            w1, [x0, #0x43]
    // 0x1296f34: r2 = Instance_BoxShape
    //     0x1296f34: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1296f38: ldr             x2, [x2, #0x80]
    // 0x1296f3c: StoreField: r0->field_47 = r2
    //     0x1296f3c: stur            w2, [x0, #0x47]
    // 0x1296f40: StoreField: r0->field_6f = r1
    //     0x1296f40: stur            w1, [x0, #0x6f]
    // 0x1296f44: r3 = false
    //     0x1296f44: add             x3, NULL, #0x30  ; false
    // 0x1296f48: StoreField: r0->field_73 = r3
    //     0x1296f48: stur            w3, [x0, #0x73]
    // 0x1296f4c: StoreField: r0->field_83 = r1
    //     0x1296f4c: stur            w1, [x0, #0x83]
    // 0x1296f50: StoreField: r0->field_7b = r3
    //     0x1296f50: stur            w3, [x0, #0x7b]
    // 0x1296f54: r0 = Padding()
    //     0x1296f54: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1296f58: mov             x2, x0
    // 0x1296f5c: r0 = Instance_EdgeInsets
    //     0x1296f5c: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0x1296f60: ldr             x0, [x0, #0x240]
    // 0x1296f64: stur            x2, [fp, #-0x20]
    // 0x1296f68: StoreField: r2->field_f = r0
    //     0x1296f68: stur            w0, [x2, #0xf]
    // 0x1296f6c: ldur            x0, [fp, #-0x50]
    // 0x1296f70: StoreField: r2->field_b = r0
    //     0x1296f70: stur            w0, [x2, #0xb]
    // 0x1296f74: ldur            x0, [fp, #-0x48]
    // 0x1296f78: LoadField: r1 = r0->field_b
    //     0x1296f78: ldur            w1, [x0, #0xb]
    // 0x1296f7c: LoadField: r3 = r0->field_f
    //     0x1296f7c: ldur            w3, [x0, #0xf]
    // 0x1296f80: DecompressPointer r3
    //     0x1296f80: add             x3, x3, HEAP, lsl #32
    // 0x1296f84: LoadField: r4 = r3->field_b
    //     0x1296f84: ldur            w4, [x3, #0xb]
    // 0x1296f88: r3 = LoadInt32Instr(r1)
    //     0x1296f88: sbfx            x3, x1, #1, #0x1f
    // 0x1296f8c: stur            x3, [fp, #-0x60]
    // 0x1296f90: r1 = LoadInt32Instr(r4)
    //     0x1296f90: sbfx            x1, x4, #1, #0x1f
    // 0x1296f94: cmp             x3, x1
    // 0x1296f98: b.ne            #0x1296fa4
    // 0x1296f9c: mov             x1, x0
    // 0x1296fa0: r0 = _growToNextCapacity()
    //     0x1296fa0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1296fa4: ldur            x2, [fp, #-0x48]
    // 0x1296fa8: ldur            x3, [fp, #-0x60]
    // 0x1296fac: add             x4, x3, #1
    // 0x1296fb0: lsl             x0, x4, #1
    // 0x1296fb4: StoreField: r2->field_b = r0
    //     0x1296fb4: stur            w0, [x2, #0xb]
    // 0x1296fb8: LoadField: r5 = r2->field_f
    //     0x1296fb8: ldur            w5, [x2, #0xf]
    // 0x1296fbc: DecompressPointer r5
    //     0x1296fbc: add             x5, x5, HEAP, lsl #32
    // 0x1296fc0: mov             x1, x5
    // 0x1296fc4: ldur            x0, [fp, #-0x20]
    // 0x1296fc8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1296fc8: add             x25, x1, x3, lsl #2
    //     0x1296fcc: add             x25, x25, #0xf
    //     0x1296fd0: str             w0, [x25]
    //     0x1296fd4: tbz             w0, #0, #0x1296ff0
    //     0x1296fd8: ldurb           w16, [x1, #-1]
    //     0x1296fdc: ldurb           w17, [x0, #-1]
    //     0x1296fe0: and             x16, x17, x16, lsr #2
    //     0x1296fe4: tst             x16, HEAP, lsr #32
    //     0x1296fe8: b.eq            #0x1296ff0
    //     0x1296fec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1296ff0: mov             x3, x2
    // 0x1296ff4: mov             x2, x4
    // 0x1296ff8: mov             x1, x5
    // 0x1296ffc: b               #0x1297058
    // 0x1297000: LoadField: r1 = r0->field_b
    //     0x1297000: ldur            w1, [x0, #0xb]
    // 0x1297004: r0 = LoadInt32Instr(r1)
    //     0x1297004: sbfx            x0, x1, #1, #0x1f
    // 0x1297008: cmp             x3, x0
    // 0x129700c: b.ne            #0x1297018
    // 0x1297010: mov             x1, x2
    // 0x1297014: r0 = _growToNextCapacity()
    //     0x1297014: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1297018: ldur            x3, [fp, #-0x48]
    // 0x129701c: ldur            x2, [fp, #-0x78]
    // 0x1297020: add             x4, x2, #1
    // 0x1297024: lsl             x0, x4, #1
    // 0x1297028: StoreField: r3->field_b = r0
    //     0x1297028: stur            w0, [x3, #0xb]
    // 0x129702c: mov             x0, x4
    // 0x1297030: mov             x1, x2
    // 0x1297034: cmp             x1, x0
    // 0x1297038: b.hs            #0x1297afc
    // 0x129703c: LoadField: r0 = r3->field_f
    //     0x129703c: ldur            w0, [x3, #0xf]
    // 0x1297040: DecompressPointer r0
    //     0x1297040: add             x0, x0, HEAP, lsl #32
    // 0x1297044: add             x1, x0, x2, lsl #2
    // 0x1297048: r16 = Instance_SizedBox
    //     0x1297048: ldr             x16, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x129704c: StoreField: r1->field_f = r16
    //     0x129704c: stur            w16, [x1, #0xf]
    // 0x1297050: mov             x2, x4
    // 0x1297054: mov             x1, x0
    // 0x1297058: ldur            x0, [fp, #-8]
    // 0x129705c: stur            x2, [fp, #-0x98]
    // 0x1297060: LoadField: r4 = r0->field_43
    //     0x1297060: ldur            w4, [x0, #0x43]
    // 0x1297064: DecompressPointer r4
    //     0x1297064: add             x4, x4, HEAP, lsl #32
    // 0x1297068: stur            x4, [fp, #-0x50]
    // 0x129706c: cmp             w4, NULL
    // 0x1297070: b.eq            #0x1297860
    // 0x1297074: LoadField: r5 = r4->field_f
    //     0x1297074: ldur            w5, [x4, #0xf]
    // 0x1297078: DecompressPointer r5
    //     0x1297078: add             x5, x5, HEAP, lsl #32
    // 0x129707c: cmp             w5, NULL
    // 0x1297080: b.eq            #0x1297860
    // 0x1297084: LoadField: r1 = r4->field_13
    //     0x1297084: ldur            w1, [x4, #0x13]
    // 0x1297088: DecompressPointer r1
    //     0x1297088: add             x1, x1, HEAP, lsl #32
    // 0x129708c: stur            x1, [fp, #-0x20]
    // 0x1297090: cmp             w1, NULL
    // 0x1297094: b.ne            #0x12970a0
    // 0x1297098: r2 = Null
    //     0x1297098: mov             x2, NULL
    // 0x129709c: b               #0x12970a8
    // 0x12970a0: LoadField: r2 = r1->field_7
    //     0x12970a0: ldur            w2, [x1, #7]
    // 0x12970a4: DecompressPointer r2
    //     0x12970a4: add             x2, x2, HEAP, lsl #32
    // 0x12970a8: cmp             w2, NULL
    // 0x12970ac: b.ne            #0x12970b8
    // 0x12970b0: r2 = 0
    //     0x12970b0: movz            x2, #0
    // 0x12970b4: b               #0x12970c8
    // 0x12970b8: r5 = LoadInt32Instr(r2)
    //     0x12970b8: sbfx            x5, x2, #1, #0x1f
    //     0x12970bc: tbz             w2, #0, #0x12970c4
    //     0x12970c0: ldur            x5, [x2, #7]
    // 0x12970c4: mov             x2, x5
    // 0x12970c8: stur            x2, [fp, #-0x80]
    // 0x12970cc: cmp             w1, NULL
    // 0x12970d0: b.ne            #0x12970dc
    // 0x12970d4: r5 = Null
    //     0x12970d4: mov             x5, NULL
    // 0x12970d8: b               #0x12970e4
    // 0x12970dc: LoadField: r5 = r1->field_b
    //     0x12970dc: ldur            w5, [x1, #0xb]
    // 0x12970e0: DecompressPointer r5
    //     0x12970e0: add             x5, x5, HEAP, lsl #32
    // 0x12970e4: cmp             w5, NULL
    // 0x12970e8: b.ne            #0x12970f4
    // 0x12970ec: r5 = 0
    //     0x12970ec: movz            x5, #0
    // 0x12970f0: b               #0x1297104
    // 0x12970f4: r6 = LoadInt32Instr(r5)
    //     0x12970f4: sbfx            x6, x5, #1, #0x1f
    //     0x12970f8: tbz             w5, #0, #0x1297100
    //     0x12970fc: ldur            x6, [x5, #7]
    // 0x1297100: mov             x5, x6
    // 0x1297104: stur            x5, [fp, #-0x78]
    // 0x1297108: cmp             w1, NULL
    // 0x129710c: b.ne            #0x1297118
    // 0x1297110: r6 = Null
    //     0x1297110: mov             x6, NULL
    // 0x1297114: b               #0x1297120
    // 0x1297118: LoadField: r6 = r1->field_f
    //     0x1297118: ldur            w6, [x1, #0xf]
    // 0x129711c: DecompressPointer r6
    //     0x129711c: add             x6, x6, HEAP, lsl #32
    // 0x1297120: cmp             w6, NULL
    // 0x1297124: b.ne            #0x1297130
    // 0x1297128: r6 = 0
    //     0x1297128: movz            x6, #0
    // 0x129712c: b               #0x1297140
    // 0x1297130: r7 = LoadInt32Instr(r6)
    //     0x1297130: sbfx            x7, x6, #1, #0x1f
    //     0x1297134: tbz             w6, #0, #0x129713c
    //     0x1297138: ldur            x7, [x6, #7]
    // 0x129713c: mov             x6, x7
    // 0x1297140: stur            x6, [fp, #-0x60]
    // 0x1297144: r0 = Color()
    //     0x1297144: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x1297148: mov             x1, x0
    // 0x129714c: r0 = Instance_ColorSpace
    //     0x129714c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x1297150: stur            x1, [fp, #-0x58]
    // 0x1297154: StoreField: r1->field_27 = r0
    //     0x1297154: stur            w0, [x1, #0x27]
    // 0x1297158: d0 = 1.000000
    //     0x1297158: fmov            d0, #1.00000000
    // 0x129715c: StoreField: r1->field_7 = d0
    //     0x129715c: stur            d0, [x1, #7]
    // 0x1297160: ldur            x2, [fp, #-0x80]
    // 0x1297164: ubfx            x2, x2, #0, #0x20
    // 0x1297168: and             w3, w2, #0xff
    // 0x129716c: ubfx            x3, x3, #0, #0x20
    // 0x1297170: scvtf           d1, x3
    // 0x1297174: d2 = 255.000000
    //     0x1297174: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x1297178: fdiv            d3, d1, d2
    // 0x129717c: StoreField: r1->field_f = d3
    //     0x129717c: stur            d3, [x1, #0xf]
    // 0x1297180: ldur            x2, [fp, #-0x78]
    // 0x1297184: ubfx            x2, x2, #0, #0x20
    // 0x1297188: and             w3, w2, #0xff
    // 0x129718c: ubfx            x3, x3, #0, #0x20
    // 0x1297190: scvtf           d1, x3
    // 0x1297194: fdiv            d3, d1, d2
    // 0x1297198: ArrayStore: r1[0] = d3  ; List_8
    //     0x1297198: stur            d3, [x1, #0x17]
    // 0x129719c: ldur            x2, [fp, #-0x60]
    // 0x12971a0: ubfx            x2, x2, #0, #0x20
    // 0x12971a4: and             w3, w2, #0xff
    // 0x12971a8: ubfx            x3, x3, #0, #0x20
    // 0x12971ac: scvtf           d1, x3
    // 0x12971b0: fdiv            d3, d1, d2
    // 0x12971b4: StoreField: r1->field_1f = d3
    //     0x12971b4: stur            d3, [x1, #0x1f]
    // 0x12971b8: ldur            x2, [fp, #-0x20]
    // 0x12971bc: cmp             w2, NULL
    // 0x12971c0: b.ne            #0x12971cc
    // 0x12971c4: r3 = Null
    //     0x12971c4: mov             x3, NULL
    // 0x12971c8: b               #0x12971d4
    // 0x12971cc: LoadField: r3 = r2->field_7
    //     0x12971cc: ldur            w3, [x2, #7]
    // 0x12971d0: DecompressPointer r3
    //     0x12971d0: add             x3, x3, HEAP, lsl #32
    // 0x12971d4: cmp             w3, NULL
    // 0x12971d8: b.ne            #0x12971e4
    // 0x12971dc: r3 = 0
    //     0x12971dc: movz            x3, #0
    // 0x12971e0: b               #0x12971f4
    // 0x12971e4: r4 = LoadInt32Instr(r3)
    //     0x12971e4: sbfx            x4, x3, #1, #0x1f
    //     0x12971e8: tbz             w3, #0, #0x12971f0
    //     0x12971ec: ldur            x4, [x3, #7]
    // 0x12971f0: mov             x3, x4
    // 0x12971f4: stur            x3, [fp, #-0x80]
    // 0x12971f8: cmp             w2, NULL
    // 0x12971fc: b.ne            #0x1297208
    // 0x1297200: r4 = Null
    //     0x1297200: mov             x4, NULL
    // 0x1297204: b               #0x1297210
    // 0x1297208: LoadField: r4 = r2->field_b
    //     0x1297208: ldur            w4, [x2, #0xb]
    // 0x129720c: DecompressPointer r4
    //     0x129720c: add             x4, x4, HEAP, lsl #32
    // 0x1297210: cmp             w4, NULL
    // 0x1297214: b.ne            #0x1297220
    // 0x1297218: r4 = 0
    //     0x1297218: movz            x4, #0
    // 0x129721c: b               #0x1297230
    // 0x1297220: r5 = LoadInt32Instr(r4)
    //     0x1297220: sbfx            x5, x4, #1, #0x1f
    //     0x1297224: tbz             w4, #0, #0x129722c
    //     0x1297228: ldur            x5, [x4, #7]
    // 0x129722c: mov             x4, x5
    // 0x1297230: stur            x4, [fp, #-0x78]
    // 0x1297234: cmp             w2, NULL
    // 0x1297238: b.ne            #0x1297244
    // 0x129723c: r2 = Null
    //     0x129723c: mov             x2, NULL
    // 0x1297240: b               #0x1297250
    // 0x1297244: LoadField: r5 = r2->field_f
    //     0x1297244: ldur            w5, [x2, #0xf]
    // 0x1297248: DecompressPointer r5
    //     0x1297248: add             x5, x5, HEAP, lsl #32
    // 0x129724c: mov             x2, x5
    // 0x1297250: cmp             w2, NULL
    // 0x1297254: b.ne            #0x1297260
    // 0x1297258: r6 = 0
    //     0x1297258: movz            x6, #0
    // 0x129725c: b               #0x1297270
    // 0x1297260: r5 = LoadInt32Instr(r2)
    //     0x1297260: sbfx            x5, x2, #1, #0x1f
    //     0x1297264: tbz             w2, #0, #0x129726c
    //     0x1297268: ldur            x5, [x2, #7]
    // 0x129726c: mov             x6, x5
    // 0x1297270: ldur            x2, [fp, #-0x48]
    // 0x1297274: ldur            x5, [fp, #-0x50]
    // 0x1297278: stur            x6, [fp, #-0x60]
    // 0x129727c: r0 = Color()
    //     0x129727c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x1297280: mov             x3, x0
    // 0x1297284: r0 = Instance_ColorSpace
    //     0x1297284: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x1297288: stur            x3, [fp, #-0x20]
    // 0x129728c: StoreField: r3->field_27 = r0
    //     0x129728c: stur            w0, [x3, #0x27]
    // 0x1297290: d0 = 0.700000
    //     0x1297290: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1297294: ldr             d0, [x17, #0xf48]
    // 0x1297298: StoreField: r3->field_7 = d0
    //     0x1297298: stur            d0, [x3, #7]
    // 0x129729c: ldur            x0, [fp, #-0x80]
    // 0x12972a0: ubfx            x0, x0, #0, #0x20
    // 0x12972a4: and             w1, w0, #0xff
    // 0x12972a8: ubfx            x1, x1, #0, #0x20
    // 0x12972ac: scvtf           d0, x1
    // 0x12972b0: d1 = 255.000000
    //     0x12972b0: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x12972b4: fdiv            d2, d0, d1
    // 0x12972b8: StoreField: r3->field_f = d2
    //     0x12972b8: stur            d2, [x3, #0xf]
    // 0x12972bc: ldur            x0, [fp, #-0x78]
    // 0x12972c0: ubfx            x0, x0, #0, #0x20
    // 0x12972c4: and             w1, w0, #0xff
    // 0x12972c8: ubfx            x1, x1, #0, #0x20
    // 0x12972cc: scvtf           d0, x1
    // 0x12972d0: fdiv            d2, d0, d1
    // 0x12972d4: ArrayStore: r3[0] = d2  ; List_8
    //     0x12972d4: stur            d2, [x3, #0x17]
    // 0x12972d8: ldur            x0, [fp, #-0x60]
    // 0x12972dc: ubfx            x0, x0, #0, #0x20
    // 0x12972e0: and             w1, w0, #0xff
    // 0x12972e4: ubfx            x1, x1, #0, #0x20
    // 0x12972e8: scvtf           d0, x1
    // 0x12972ec: fdiv            d2, d0, d1
    // 0x12972f0: StoreField: r3->field_1f = d2
    //     0x12972f0: stur            d2, [x3, #0x1f]
    // 0x12972f4: r1 = Null
    //     0x12972f4: mov             x1, NULL
    // 0x12972f8: r2 = 4
    //     0x12972f8: movz            x2, #0x4
    // 0x12972fc: r0 = AllocateArray()
    //     0x12972fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1297300: mov             x2, x0
    // 0x1297304: ldur            x0, [fp, #-0x58]
    // 0x1297308: stur            x2, [fp, #-0x68]
    // 0x129730c: StoreField: r2->field_f = r0
    //     0x129730c: stur            w0, [x2, #0xf]
    // 0x1297310: ldur            x0, [fp, #-0x20]
    // 0x1297314: StoreField: r2->field_13 = r0
    //     0x1297314: stur            w0, [x2, #0x13]
    // 0x1297318: r1 = <Color>
    //     0x1297318: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x129731c: ldr             x1, [x1, #0xf80]
    // 0x1297320: r0 = AllocateGrowableArray()
    //     0x1297320: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1297324: mov             x1, x0
    // 0x1297328: ldur            x0, [fp, #-0x68]
    // 0x129732c: stur            x1, [fp, #-0x20]
    // 0x1297330: StoreField: r1->field_f = r0
    //     0x1297330: stur            w0, [x1, #0xf]
    // 0x1297334: r2 = 4
    //     0x1297334: movz            x2, #0x4
    // 0x1297338: StoreField: r1->field_b = r2
    //     0x1297338: stur            w2, [x1, #0xb]
    // 0x129733c: r0 = LinearGradient()
    //     0x129733c: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0x1297340: mov             x1, x0
    // 0x1297344: r0 = Instance_Alignment
    //     0x1297344: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0x1297348: ldr             x0, [x0, #0xce0]
    // 0x129734c: stur            x1, [fp, #-0x58]
    // 0x1297350: StoreField: r1->field_13 = r0
    //     0x1297350: stur            w0, [x1, #0x13]
    // 0x1297354: r0 = Instance_Alignment
    //     0x1297354: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0x1297358: ldr             x0, [x0, #0xce8]
    // 0x129735c: ArrayStore: r1[0] = r0  ; List_4
    //     0x129735c: stur            w0, [x1, #0x17]
    // 0x1297360: r0 = Instance_TileMode
    //     0x1297360: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0x1297364: ldr             x0, [x0, #0xcf0]
    // 0x1297368: StoreField: r1->field_1b = r0
    //     0x1297368: stur            w0, [x1, #0x1b]
    // 0x129736c: ldur            x0, [fp, #-0x20]
    // 0x1297370: StoreField: r1->field_7 = r0
    //     0x1297370: stur            w0, [x1, #7]
    // 0x1297374: r0 = BoxDecoration()
    //     0x1297374: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1297378: mov             x2, x0
    // 0x129737c: r0 = Instance_BorderRadius
    //     0x129737c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x1297380: ldr             x0, [x0, #0xf70]
    // 0x1297384: stur            x2, [fp, #-0x20]
    // 0x1297388: StoreField: r2->field_13 = r0
    //     0x1297388: stur            w0, [x2, #0x13]
    // 0x129738c: ldur            x0, [fp, #-0x58]
    // 0x1297390: StoreField: r2->field_1b = r0
    //     0x1297390: stur            w0, [x2, #0x1b]
    // 0x1297394: r0 = Instance_BoxShape
    //     0x1297394: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1297398: ldr             x0, [x0, #0x80]
    // 0x129739c: StoreField: r2->field_23 = r0
    //     0x129739c: stur            w0, [x2, #0x23]
    // 0x12973a0: ldur            x1, [fp, #-0x10]
    // 0x12973a4: r0 = of()
    //     0x12973a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12973a8: LoadField: r1 = r0->field_87
    //     0x12973a8: ldur            w1, [x0, #0x87]
    // 0x12973ac: DecompressPointer r1
    //     0x12973ac: add             x1, x1, HEAP, lsl #32
    // 0x12973b0: LoadField: r0 = r1->field_7
    //     0x12973b0: ldur            w0, [x1, #7]
    // 0x12973b4: DecompressPointer r0
    //     0x12973b4: add             x0, x0, HEAP, lsl #32
    // 0x12973b8: r16 = 16.000000
    //     0x12973b8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x12973bc: ldr             x16, [x16, #0x188]
    // 0x12973c0: r30 = Instance_Color
    //     0x12973c0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x12973c4: stp             lr, x16, [SP]
    // 0x12973c8: mov             x1, x0
    // 0x12973cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x12973cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x12973d0: ldr             x4, [x4, #0xaa0]
    // 0x12973d4: r0 = copyWith()
    //     0x12973d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12973d8: stur            x0, [fp, #-0x58]
    // 0x12973dc: r0 = TextSpan()
    //     0x12973dc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x12973e0: mov             x2, x0
    // 0x12973e4: r0 = "BUMPER OFFER\n"
    //     0x12973e4: add             x0, PP, #0x48, lsl #12  ; [pp+0x48338] "BUMPER OFFER\n"
    //     0x12973e8: ldr             x0, [x0, #0x338]
    // 0x12973ec: stur            x2, [fp, #-0x68]
    // 0x12973f0: StoreField: r2->field_b = r0
    //     0x12973f0: stur            w0, [x2, #0xb]
    // 0x12973f4: r0 = Instance__DeferringMouseCursor
    //     0x12973f4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x12973f8: ArrayStore: r2[0] = r0  ; List_4
    //     0x12973f8: stur            w0, [x2, #0x17]
    // 0x12973fc: ldur            x1, [fp, #-0x58]
    // 0x1297400: StoreField: r2->field_7 = r1
    //     0x1297400: stur            w1, [x2, #7]
    // 0x1297404: ldur            x1, [fp, #-0x10]
    // 0x1297408: r0 = of()
    //     0x1297408: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x129740c: LoadField: r1 = r0->field_87
    //     0x129740c: ldur            w1, [x0, #0x87]
    // 0x1297410: DecompressPointer r1
    //     0x1297410: add             x1, x1, HEAP, lsl #32
    // 0x1297414: LoadField: r0 = r1->field_2b
    //     0x1297414: ldur            w0, [x1, #0x2b]
    // 0x1297418: DecompressPointer r0
    //     0x1297418: add             x0, x0, HEAP, lsl #32
    // 0x129741c: r16 = 12.000000
    //     0x129741c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1297420: ldr             x16, [x16, #0x9e8]
    // 0x1297424: r30 = Instance_Color
    //     0x1297424: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1297428: stp             lr, x16, [SP]
    // 0x129742c: mov             x1, x0
    // 0x1297430: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1297430: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1297434: ldr             x4, [x4, #0xaa0]
    // 0x1297438: r0 = copyWith()
    //     0x1297438: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x129743c: stur            x0, [fp, #-0x58]
    // 0x1297440: r0 = TextSpan()
    //     0x1297440: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1297444: mov             x3, x0
    // 0x1297448: r0 = "Unlocked from your last order"
    //     0x1297448: add             x0, PP, #0x48, lsl #12  ; [pp+0x48340] "Unlocked from your last order"
    //     0x129744c: ldr             x0, [x0, #0x340]
    // 0x1297450: stur            x3, [fp, #-0x70]
    // 0x1297454: StoreField: r3->field_b = r0
    //     0x1297454: stur            w0, [x3, #0xb]
    // 0x1297458: r0 = Instance__DeferringMouseCursor
    //     0x1297458: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x129745c: ArrayStore: r3[0] = r0  ; List_4
    //     0x129745c: stur            w0, [x3, #0x17]
    // 0x1297460: ldur            x1, [fp, #-0x58]
    // 0x1297464: StoreField: r3->field_7 = r1
    //     0x1297464: stur            w1, [x3, #7]
    // 0x1297468: r1 = Null
    //     0x1297468: mov             x1, NULL
    // 0x129746c: r2 = 4
    //     0x129746c: movz            x2, #0x4
    // 0x1297470: r0 = AllocateArray()
    //     0x1297470: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1297474: mov             x2, x0
    // 0x1297478: ldur            x0, [fp, #-0x68]
    // 0x129747c: stur            x2, [fp, #-0x58]
    // 0x1297480: StoreField: r2->field_f = r0
    //     0x1297480: stur            w0, [x2, #0xf]
    // 0x1297484: ldur            x0, [fp, #-0x70]
    // 0x1297488: StoreField: r2->field_13 = r0
    //     0x1297488: stur            w0, [x2, #0x13]
    // 0x129748c: r1 = <InlineSpan>
    //     0x129748c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x1297490: ldr             x1, [x1, #0xe40]
    // 0x1297494: r0 = AllocateGrowableArray()
    //     0x1297494: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1297498: mov             x1, x0
    // 0x129749c: ldur            x0, [fp, #-0x58]
    // 0x12974a0: stur            x1, [fp, #-0x68]
    // 0x12974a4: StoreField: r1->field_f = r0
    //     0x12974a4: stur            w0, [x1, #0xf]
    // 0x12974a8: r2 = 4
    //     0x12974a8: movz            x2, #0x4
    // 0x12974ac: StoreField: r1->field_b = r2
    //     0x12974ac: stur            w2, [x1, #0xb]
    // 0x12974b0: r0 = TextSpan()
    //     0x12974b0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x12974b4: mov             x1, x0
    // 0x12974b8: ldur            x0, [fp, #-0x68]
    // 0x12974bc: stur            x1, [fp, #-0x58]
    // 0x12974c0: StoreField: r1->field_f = r0
    //     0x12974c0: stur            w0, [x1, #0xf]
    // 0x12974c4: r0 = Instance__DeferringMouseCursor
    //     0x12974c4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x12974c8: ArrayStore: r1[0] = r0  ; List_4
    //     0x12974c8: stur            w0, [x1, #0x17]
    // 0x12974cc: r0 = RichText()
    //     0x12974cc: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x12974d0: mov             x1, x0
    // 0x12974d4: ldur            x2, [fp, #-0x58]
    // 0x12974d8: stur            x0, [fp, #-0x58]
    // 0x12974dc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x12974dc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x12974e0: r0 = RichText()
    //     0x12974e0: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x12974e4: r1 = Instance_Color
    //     0x12974e4: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x12974e8: d0 = 0.500000
    //     0x12974e8: fmov            d0, #0.50000000
    // 0x12974ec: r0 = withOpacity()
    //     0x12974ec: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x12974f0: stur            x0, [fp, #-0x68]
    // 0x12974f4: r0 = VerticalDivider()
    //     0x12974f4: bl              #0x99b78c  ; AllocateVerticalDividerStub -> VerticalDivider (size=0x28)
    // 0x12974f8: d0 = 1.000000
    //     0x12974f8: fmov            d0, #1.00000000
    // 0x12974fc: stur            x0, [fp, #-0x70]
    // 0x1297500: StoreField: r0->field_f = d0
    //     0x1297500: stur            d0, [x0, #0xf]
    // 0x1297504: ldur            x1, [fp, #-0x68]
    // 0x1297508: StoreField: r0->field_1f = r1
    //     0x1297508: stur            w1, [x0, #0x1f]
    // 0x129750c: ldur            x3, [fp, #-0x50]
    // 0x1297510: LoadField: r4 = r3->field_7
    //     0x1297510: ldur            w4, [x3, #7]
    // 0x1297514: DecompressPointer r4
    //     0x1297514: add             x4, x4, HEAP, lsl #32
    // 0x1297518: stur            x4, [fp, #-0x68]
    // 0x129751c: r1 = Null
    //     0x129751c: mov             x1, NULL
    // 0x1297520: r2 = 4
    //     0x1297520: movz            x2, #0x4
    // 0x1297524: r0 = AllocateArray()
    //     0x1297524: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1297528: mov             x1, x0
    // 0x129752c: ldur            x0, [fp, #-0x68]
    // 0x1297530: StoreField: r1->field_f = r0
    //     0x1297530: stur            w0, [x1, #0xf]
    // 0x1297534: r16 = "\n"
    //     0x1297534: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0x1297538: StoreField: r1->field_13 = r16
    //     0x1297538: stur            w16, [x1, #0x13]
    // 0x129753c: str             x1, [SP]
    // 0x1297540: r0 = _interpolate()
    //     0x1297540: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1297544: ldur            x1, [fp, #-0x10]
    // 0x1297548: stur            x0, [fp, #-0x68]
    // 0x129754c: r0 = of()
    //     0x129754c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1297550: LoadField: r1 = r0->field_87
    //     0x1297550: ldur            w1, [x0, #0x87]
    // 0x1297554: DecompressPointer r1
    //     0x1297554: add             x1, x1, HEAP, lsl #32
    // 0x1297558: LoadField: r0 = r1->field_7
    //     0x1297558: ldur            w0, [x1, #7]
    // 0x129755c: DecompressPointer r0
    //     0x129755c: add             x0, x0, HEAP, lsl #32
    // 0x1297560: r16 = Instance_Color
    //     0x1297560: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1297564: r30 = 32.000000
    //     0x1297564: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0x1297568: ldr             lr, [lr, #0x848]
    // 0x129756c: stp             lr, x16, [SP]
    // 0x1297570: mov             x1, x0
    // 0x1297574: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1297574: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1297578: ldr             x4, [x4, #0x9b8]
    // 0x129757c: r0 = copyWith()
    //     0x129757c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1297580: ldur            x1, [fp, #-0x10]
    // 0x1297584: stur            x0, [fp, #-0x10]
    // 0x1297588: r0 = of()
    //     0x1297588: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x129758c: LoadField: r1 = r0->field_87
    //     0x129758c: ldur            w1, [x0, #0x87]
    // 0x1297590: DecompressPointer r1
    //     0x1297590: add             x1, x1, HEAP, lsl #32
    // 0x1297594: LoadField: r0 = r1->field_7
    //     0x1297594: ldur            w0, [x1, #7]
    // 0x1297598: DecompressPointer r0
    //     0x1297598: add             x0, x0, HEAP, lsl #32
    // 0x129759c: r16 = Instance_Color
    //     0x129759c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x12975a0: r30 = 16.000000
    //     0x12975a0: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x12975a4: ldr             lr, [lr, #0x188]
    // 0x12975a8: stp             lr, x16, [SP]
    // 0x12975ac: mov             x1, x0
    // 0x12975b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x12975b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x12975b4: ldr             x4, [x4, #0x9b8]
    // 0x12975b8: r0 = copyWith()
    //     0x12975b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12975bc: stur            x0, [fp, #-0x88]
    // 0x12975c0: r0 = TextSpan()
    //     0x12975c0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x12975c4: mov             x3, x0
    // 0x12975c8: r0 = "OFF"
    //     0x12975c8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48348] "OFF"
    //     0x12975cc: ldr             x0, [x0, #0x348]
    // 0x12975d0: stur            x3, [fp, #-0x90]
    // 0x12975d4: StoreField: r3->field_b = r0
    //     0x12975d4: stur            w0, [x3, #0xb]
    // 0x12975d8: r0 = Instance__DeferringMouseCursor
    //     0x12975d8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x12975dc: ArrayStore: r3[0] = r0  ; List_4
    //     0x12975dc: stur            w0, [x3, #0x17]
    // 0x12975e0: ldur            x1, [fp, #-0x88]
    // 0x12975e4: StoreField: r3->field_7 = r1
    //     0x12975e4: stur            w1, [x3, #7]
    // 0x12975e8: r1 = Null
    //     0x12975e8: mov             x1, NULL
    // 0x12975ec: r2 = 2
    //     0x12975ec: movz            x2, #0x2
    // 0x12975f0: r0 = AllocateArray()
    //     0x12975f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12975f4: mov             x2, x0
    // 0x12975f8: ldur            x0, [fp, #-0x90]
    // 0x12975fc: stur            x2, [fp, #-0x88]
    // 0x1297600: StoreField: r2->field_f = r0
    //     0x1297600: stur            w0, [x2, #0xf]
    // 0x1297604: r1 = <InlineSpan>
    //     0x1297604: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x1297608: ldr             x1, [x1, #0xe40]
    // 0x129760c: r0 = AllocateGrowableArray()
    //     0x129760c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1297610: mov             x1, x0
    // 0x1297614: ldur            x0, [fp, #-0x88]
    // 0x1297618: stur            x1, [fp, #-0x90]
    // 0x129761c: StoreField: r1->field_f = r0
    //     0x129761c: stur            w0, [x1, #0xf]
    // 0x1297620: r0 = 2
    //     0x1297620: movz            x0, #0x2
    // 0x1297624: StoreField: r1->field_b = r0
    //     0x1297624: stur            w0, [x1, #0xb]
    // 0x1297628: r0 = TextSpan()
    //     0x1297628: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x129762c: mov             x1, x0
    // 0x1297630: ldur            x0, [fp, #-0x68]
    // 0x1297634: stur            x1, [fp, #-0x88]
    // 0x1297638: StoreField: r1->field_b = r0
    //     0x1297638: stur            w0, [x1, #0xb]
    // 0x129763c: ldur            x0, [fp, #-0x90]
    // 0x1297640: StoreField: r1->field_f = r0
    //     0x1297640: stur            w0, [x1, #0xf]
    // 0x1297644: r0 = Instance__DeferringMouseCursor
    //     0x1297644: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1297648: ArrayStore: r1[0] = r0  ; List_4
    //     0x1297648: stur            w0, [x1, #0x17]
    // 0x129764c: ldur            x0, [fp, #-0x10]
    // 0x1297650: StoreField: r1->field_7 = r0
    //     0x1297650: stur            w0, [x1, #7]
    // 0x1297654: r0 = RichText()
    //     0x1297654: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x1297658: stur            x0, [fp, #-0x10]
    // 0x129765c: r16 = Instance_TextAlign
    //     0x129765c: ldr             x16, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0x1297660: str             x16, [SP]
    // 0x1297664: mov             x1, x0
    // 0x1297668: ldur            x2, [fp, #-0x88]
    // 0x129766c: r4 = const [0, 0x3, 0x1, 0x2, textAlign, 0x2, null]
    //     0x129766c: add             x4, PP, #0x48, lsl #12  ; [pp+0x48350] List(7) [0, 0x3, 0x1, 0x2, "textAlign", 0x2, Null]
    //     0x1297670: ldr             x4, [x4, #0x350]
    // 0x1297674: r0 = RichText()
    //     0x1297674: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x1297678: r1 = Null
    //     0x1297678: mov             x1, NULL
    // 0x129767c: r2 = 6
    //     0x129767c: movz            x2, #0x6
    // 0x1297680: r0 = AllocateArray()
    //     0x1297680: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1297684: mov             x2, x0
    // 0x1297688: ldur            x0, [fp, #-0x58]
    // 0x129768c: stur            x2, [fp, #-0x68]
    // 0x1297690: StoreField: r2->field_f = r0
    //     0x1297690: stur            w0, [x2, #0xf]
    // 0x1297694: ldur            x0, [fp, #-0x70]
    // 0x1297698: StoreField: r2->field_13 = r0
    //     0x1297698: stur            w0, [x2, #0x13]
    // 0x129769c: ldur            x0, [fp, #-0x10]
    // 0x12976a0: ArrayStore: r2[0] = r0  ; List_4
    //     0x12976a0: stur            w0, [x2, #0x17]
    // 0x12976a4: r1 = <Widget>
    //     0x12976a4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x12976a8: r0 = AllocateGrowableArray()
    //     0x12976a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12976ac: mov             x1, x0
    // 0x12976b0: ldur            x0, [fp, #-0x68]
    // 0x12976b4: stur            x1, [fp, #-0x10]
    // 0x12976b8: StoreField: r1->field_f = r0
    //     0x12976b8: stur            w0, [x1, #0xf]
    // 0x12976bc: r0 = 6
    //     0x12976bc: movz            x0, #0x6
    // 0x12976c0: StoreField: r1->field_b = r0
    //     0x12976c0: stur            w0, [x1, #0xb]
    // 0x12976c4: r0 = Row()
    //     0x12976c4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x12976c8: mov             x1, x0
    // 0x12976cc: r0 = Instance_Axis
    //     0x12976cc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x12976d0: stur            x1, [fp, #-0x58]
    // 0x12976d4: StoreField: r1->field_f = r0
    //     0x12976d4: stur            w0, [x1, #0xf]
    // 0x12976d8: r0 = Instance_MainAxisAlignment
    //     0x12976d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x12976dc: ldr             x0, [x0, #0xa8]
    // 0x12976e0: StoreField: r1->field_13 = r0
    //     0x12976e0: stur            w0, [x1, #0x13]
    // 0x12976e4: r0 = Instance_MainAxisSize
    //     0x12976e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x12976e8: ldr             x0, [x0, #0xa10]
    // 0x12976ec: ArrayStore: r1[0] = r0  ; List_4
    //     0x12976ec: stur            w0, [x1, #0x17]
    // 0x12976f0: r2 = Instance_CrossAxisAlignment
    //     0x12976f0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x12976f4: ldr             x2, [x2, #0xa18]
    // 0x12976f8: StoreField: r1->field_1b = r2
    //     0x12976f8: stur            w2, [x1, #0x1b]
    // 0x12976fc: r2 = Instance_VerticalDirection
    //     0x12976fc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1297700: ldr             x2, [x2, #0xa20]
    // 0x1297704: StoreField: r1->field_23 = r2
    //     0x1297704: stur            w2, [x1, #0x23]
    // 0x1297708: r3 = Instance_Clip
    //     0x1297708: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x129770c: ldr             x3, [x3, #0x38]
    // 0x1297710: StoreField: r1->field_2b = r3
    //     0x1297710: stur            w3, [x1, #0x2b]
    // 0x1297714: StoreField: r1->field_2f = rZR
    //     0x1297714: stur            xzr, [x1, #0x2f]
    // 0x1297718: ldur            x4, [fp, #-0x10]
    // 0x129771c: StoreField: r1->field_b = r4
    //     0x129771c: stur            w4, [x1, #0xb]
    // 0x1297720: r0 = Padding()
    //     0x1297720: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1297724: mov             x1, x0
    // 0x1297728: r0 = Instance_EdgeInsets
    //     0x1297728: add             x0, PP, #0x48, lsl #12  ; [pp+0x48358] Obj!EdgeInsets@d57411
    //     0x129772c: ldr             x0, [x0, #0x358]
    // 0x1297730: stur            x1, [fp, #-0x10]
    // 0x1297734: StoreField: r1->field_f = r0
    //     0x1297734: stur            w0, [x1, #0xf]
    // 0x1297738: ldur            x0, [fp, #-0x58]
    // 0x129773c: StoreField: r1->field_b = r0
    //     0x129773c: stur            w0, [x1, #0xb]
    // 0x1297740: r0 = IntrinsicHeight()
    //     0x1297740: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0x1297744: mov             x1, x0
    // 0x1297748: ldur            x0, [fp, #-0x10]
    // 0x129774c: stur            x1, [fp, #-0x58]
    // 0x1297750: StoreField: r1->field_b = r0
    //     0x1297750: stur            w0, [x1, #0xb]
    // 0x1297754: r0 = Container()
    //     0x1297754: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1297758: stur            x0, [fp, #-0x10]
    // 0x129775c: r16 = 93.000000
    //     0x129775c: add             x16, PP, #0x48, lsl #12  ; [pp+0x48360] 93
    //     0x1297760: ldr             x16, [x16, #0x360]
    // 0x1297764: ldur            lr, [fp, #-0x20]
    // 0x1297768: stp             lr, x16, [SP, #8]
    // 0x129776c: ldur            x16, [fp, #-0x58]
    // 0x1297770: str             x16, [SP]
    // 0x1297774: mov             x1, x0
    // 0x1297778: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0x1297778: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0x129777c: ldr             x4, [x4, #0xc78]
    // 0x1297780: r0 = Container()
    //     0x1297780: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1297784: r1 = <Path>
    //     0x1297784: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0x1297788: ldr             x1, [x1, #0xd30]
    // 0x129778c: r0 = MovieTicketClipper()
    //     0x129778c: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0x1297790: stur            x0, [fp, #-0x20]
    // 0x1297794: r0 = ClipPath()
    //     0x1297794: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0x1297798: mov             x1, x0
    // 0x129779c: ldur            x0, [fp, #-0x20]
    // 0x12977a0: stur            x1, [fp, #-0x58]
    // 0x12977a4: StoreField: r1->field_f = r0
    //     0x12977a4: stur            w0, [x1, #0xf]
    // 0x12977a8: r0 = Instance_Clip
    //     0x12977a8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x12977ac: ldr             x0, [x0, #0x138]
    // 0x12977b0: StoreField: r1->field_13 = r0
    //     0x12977b0: stur            w0, [x1, #0x13]
    // 0x12977b4: ldur            x0, [fp, #-0x10]
    // 0x12977b8: StoreField: r1->field_b = r0
    //     0x12977b8: stur            w0, [x1, #0xb]
    // 0x12977bc: r0 = Padding()
    //     0x12977bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x12977c0: mov             x2, x0
    // 0x12977c4: r0 = Instance_EdgeInsets
    //     0x12977c4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0x12977c8: ldr             x0, [x0, #0x778]
    // 0x12977cc: stur            x2, [fp, #-0x10]
    // 0x12977d0: StoreField: r2->field_f = r0
    //     0x12977d0: stur            w0, [x2, #0xf]
    // 0x12977d4: ldur            x0, [fp, #-0x58]
    // 0x12977d8: StoreField: r2->field_b = r0
    //     0x12977d8: stur            w0, [x2, #0xb]
    // 0x12977dc: ldur            x0, [fp, #-0x48]
    // 0x12977e0: LoadField: r1 = r0->field_b
    //     0x12977e0: ldur            w1, [x0, #0xb]
    // 0x12977e4: LoadField: r3 = r0->field_f
    //     0x12977e4: ldur            w3, [x0, #0xf]
    // 0x12977e8: DecompressPointer r3
    //     0x12977e8: add             x3, x3, HEAP, lsl #32
    // 0x12977ec: LoadField: r4 = r3->field_b
    //     0x12977ec: ldur            w4, [x3, #0xb]
    // 0x12977f0: r3 = LoadInt32Instr(r1)
    //     0x12977f0: sbfx            x3, x1, #1, #0x1f
    // 0x12977f4: stur            x3, [fp, #-0x60]
    // 0x12977f8: r1 = LoadInt32Instr(r4)
    //     0x12977f8: sbfx            x1, x4, #1, #0x1f
    // 0x12977fc: cmp             x3, x1
    // 0x1297800: b.ne            #0x129780c
    // 0x1297804: mov             x1, x0
    // 0x1297808: r0 = _growToNextCapacity()
    //     0x1297808: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x129780c: ldur            x3, [fp, #-0x48]
    // 0x1297810: ldur            x2, [fp, #-0x60]
    // 0x1297814: add             x4, x2, #1
    // 0x1297818: lsl             x0, x4, #1
    // 0x129781c: StoreField: r3->field_b = r0
    //     0x129781c: stur            w0, [x3, #0xb]
    // 0x1297820: LoadField: r5 = r3->field_f
    //     0x1297820: ldur            w5, [x3, #0xf]
    // 0x1297824: DecompressPointer r5
    //     0x1297824: add             x5, x5, HEAP, lsl #32
    // 0x1297828: mov             x1, x5
    // 0x129782c: ldur            x0, [fp, #-0x10]
    // 0x1297830: ArrayStore: r1[r2] = r0  ; List_4
    //     0x1297830: add             x25, x1, x2, lsl #2
    //     0x1297834: add             x25, x25, #0xf
    //     0x1297838: str             w0, [x25]
    //     0x129783c: tbz             w0, #0, #0x1297858
    //     0x1297840: ldurb           w16, [x1, #-1]
    //     0x1297844: ldurb           w17, [x0, #-1]
    //     0x1297848: and             x16, x17, x16, lsr #2
    //     0x129784c: tst             x16, HEAP, lsr #32
    //     0x1297850: b.eq            #0x1297858
    //     0x1297854: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1297858: mov             x6, x4
    // 0x129785c: b               #0x12978b8
    // 0x1297860: LoadField: r0 = r1->field_b
    //     0x1297860: ldur            w0, [x1, #0xb]
    // 0x1297864: r1 = LoadInt32Instr(r0)
    //     0x1297864: sbfx            x1, x0, #1, #0x1f
    // 0x1297868: cmp             x2, x1
    // 0x129786c: b.ne            #0x1297878
    // 0x1297870: mov             x1, x3
    // 0x1297874: r0 = _growToNextCapacity()
    //     0x1297874: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1297878: ldur            x3, [fp, #-0x48]
    // 0x129787c: ldur            x2, [fp, #-0x98]
    // 0x1297880: add             x4, x2, #1
    // 0x1297884: lsl             x0, x4, #1
    // 0x1297888: StoreField: r3->field_b = r0
    //     0x1297888: stur            w0, [x3, #0xb]
    // 0x129788c: mov             x0, x4
    // 0x1297890: mov             x1, x2
    // 0x1297894: cmp             x1, x0
    // 0x1297898: b.hs            #0x1297b00
    // 0x129789c: LoadField: r0 = r3->field_f
    //     0x129789c: ldur            w0, [x3, #0xf]
    // 0x12978a0: DecompressPointer r0
    //     0x12978a0: add             x0, x0, HEAP, lsl #32
    // 0x12978a4: add             x1, x0, x2, lsl #2
    // 0x12978a8: r16 = Instance_SizedBox
    //     0x12978a8: ldr             x16, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x12978ac: StoreField: r1->field_f = r16
    //     0x12978ac: stur            w16, [x1, #0xf]
    // 0x12978b0: mov             x6, x4
    // 0x12978b4: mov             x5, x0
    // 0x12978b8: ldur            x1, [fp, #-8]
    // 0x12978bc: ldur            x4, [fp, #-0x18]
    // 0x12978c0: ldur            x2, [fp, #-0x30]
    // 0x12978c4: ldur            x0, [fp, #-0x50]
    // 0x12978c8: stur            x6, [fp, #-0x60]
    // 0x12978cc: stur            x5, [fp, #-0xd0]
    // 0x12978d0: LoadField: r7 = r1->field_b
    //     0x12978d0: ldur            w7, [x1, #0xb]
    // 0x12978d4: DecompressPointer r7
    //     0x12978d4: add             x7, x7, HEAP, lsl #32
    // 0x12978d8: stur            x7, [fp, #-0xc8]
    // 0x12978dc: LoadField: r8 = r1->field_2f
    //     0x12978dc: ldur            w8, [x1, #0x2f]
    // 0x12978e0: DecompressPointer r8
    //     0x12978e0: add             x8, x8, HEAP, lsl #32
    // 0x12978e4: stur            x8, [fp, #-0xc0]
    // 0x12978e8: LoadField: r9 = r1->field_2b
    //     0x12978e8: ldur            w9, [x1, #0x2b]
    // 0x12978ec: DecompressPointer r9
    //     0x12978ec: add             x9, x9, HEAP, lsl #32
    // 0x12978f0: stur            x9, [fp, #-0xb8]
    // 0x12978f4: LoadField: r10 = r1->field_37
    //     0x12978f4: ldur            w10, [x1, #0x37]
    // 0x12978f8: DecompressPointer r10
    //     0x12978f8: add             x10, x10, HEAP, lsl #32
    // 0x12978fc: stur            x10, [fp, #-0xb0]
    // 0x1297900: LoadField: r11 = r1->field_3b
    //     0x1297900: ldur            w11, [x1, #0x3b]
    // 0x1297904: DecompressPointer r11
    //     0x1297904: add             x11, x11, HEAP, lsl #32
    // 0x1297908: stur            x11, [fp, #-0xa8]
    // 0x129790c: LoadField: r12 = r1->field_33
    //     0x129790c: ldur            w12, [x1, #0x33]
    // 0x1297910: DecompressPointer r12
    //     0x1297910: add             x12, x12, HEAP, lsl #32
    // 0x1297914: stur            x12, [fp, #-0xa0]
    // 0x1297918: LoadField: r13 = r1->field_3f
    //     0x1297918: ldur            w13, [x1, #0x3f]
    // 0x129791c: DecompressPointer r13
    //     0x129791c: add             x13, x13, HEAP, lsl #32
    // 0x1297920: stur            x13, [fp, #-0x90]
    // 0x1297924: LoadField: r14 = r1->field_47
    //     0x1297924: ldur            w14, [x1, #0x47]
    // 0x1297928: DecompressPointer r14
    //     0x1297928: add             x14, x14, HEAP, lsl #32
    // 0x129792c: stur            x14, [fp, #-0x88]
    // 0x1297930: LoadField: r19 = r1->field_1f
    //     0x1297930: ldur            w19, [x1, #0x1f]
    // 0x1297934: DecompressPointer r19
    //     0x1297934: add             x19, x19, HEAP, lsl #32
    // 0x1297938: stur            x19, [fp, #-0x70]
    // 0x129793c: LoadField: r20 = r1->field_53
    //     0x129793c: ldur            w20, [x1, #0x53]
    // 0x1297940: DecompressPointer r20
    //     0x1297940: add             x20, x20, HEAP, lsl #32
    // 0x1297944: stur            x20, [fp, #-0x68]
    // 0x1297948: LoadField: r23 = r1->field_4f
    //     0x1297948: ldur            w23, [x1, #0x4f]
    // 0x129794c: DecompressPointer r23
    //     0x129794c: add             x23, x23, HEAP, lsl #32
    // 0x1297950: stur            x23, [fp, #-0x58]
    // 0x1297954: LoadField: r24 = r1->field_27
    //     0x1297954: ldur            w24, [x1, #0x27]
    // 0x1297958: DecompressPointer r24
    //     0x1297958: add             x24, x24, HEAP, lsl #32
    // 0x129795c: stur            x24, [fp, #-0x20]
    // 0x1297960: LoadField: r25 = r1->field_57
    //     0x1297960: ldur            w25, [x1, #0x57]
    // 0x1297964: DecompressPointer r25
    //     0x1297964: add             x25, x25, HEAP, lsl #32
    // 0x1297968: stur            x25, [fp, #-0x10]
    // 0x129796c: r0 = _ProductGridItem()
    //     0x129796c: bl              #0x1297b08  ; Allocate_ProductGridItemStub -> _ProductGridItem (size=0x4c)
    // 0x1297970: mov             x2, x0
    // 0x1297974: ldur            x0, [fp, #-0xc8]
    // 0x1297978: stur            x2, [fp, #-8]
    // 0x129797c: StoreField: r2->field_b = r0
    //     0x129797c: stur            w0, [x2, #0xb]
    // 0x1297980: ldur            x0, [fp, #-0x70]
    // 0x1297984: StoreField: r2->field_f = r0
    //     0x1297984: stur            w0, [x2, #0xf]
    // 0x1297988: ldur            x0, [fp, #-0x18]
    // 0x129798c: ArrayStore: r2[0] = r0  ; List_4
    //     0x129798c: stur            w0, [x2, #0x17]
    // 0x1297990: ldur            x0, [fp, #-0xc0]
    // 0x1297994: StoreField: r2->field_1b = r0
    //     0x1297994: stur            w0, [x2, #0x1b]
    // 0x1297998: ldur            x0, [fp, #-0x30]
    // 0x129799c: StoreField: r2->field_33 = r0
    //     0x129799c: stur            w0, [x2, #0x33]
    // 0x12979a0: ldur            x0, [fp, #-0xb8]
    // 0x12979a4: StoreField: r2->field_13 = r0
    //     0x12979a4: stur            w0, [x2, #0x13]
    // 0x12979a8: ldur            x0, [fp, #-0xa0]
    // 0x12979ac: StoreField: r2->field_1f = r0
    //     0x12979ac: stur            w0, [x2, #0x1f]
    // 0x12979b0: ldur            x0, [fp, #-0xb0]
    // 0x12979b4: StoreField: r2->field_23 = r0
    //     0x12979b4: stur            w0, [x2, #0x23]
    // 0x12979b8: ldur            x0, [fp, #-0x50]
    // 0x12979bc: StoreField: r2->field_37 = r0
    //     0x12979bc: stur            w0, [x2, #0x37]
    // 0x12979c0: ldur            x0, [fp, #-0xa8]
    // 0x12979c4: StoreField: r2->field_27 = r0
    //     0x12979c4: stur            w0, [x2, #0x27]
    // 0x12979c8: ldur            x0, [fp, #-0x88]
    // 0x12979cc: StoreField: r2->field_3b = r0
    //     0x12979cc: stur            w0, [x2, #0x3b]
    // 0x12979d0: ldur            x0, [fp, #-0x58]
    // 0x12979d4: StoreField: r2->field_3f = r0
    //     0x12979d4: stur            w0, [x2, #0x3f]
    // 0x12979d8: ldur            x0, [fp, #-0x68]
    // 0x12979dc: StoreField: r2->field_43 = r0
    //     0x12979dc: stur            w0, [x2, #0x43]
    // 0x12979e0: ldur            x0, [fp, #-0x10]
    // 0x12979e4: StoreField: r2->field_47 = r0
    //     0x12979e4: stur            w0, [x2, #0x47]
    // 0x12979e8: ldur            x0, [fp, #-0x90]
    // 0x12979ec: StoreField: r2->field_2b = r0
    //     0x12979ec: stur            w0, [x2, #0x2b]
    // 0x12979f0: ldur            x0, [fp, #-0x20]
    // 0x12979f4: StoreField: r2->field_2f = r0
    //     0x12979f4: stur            w0, [x2, #0x2f]
    // 0x12979f8: ldur            x0, [fp, #-0xd0]
    // 0x12979fc: LoadField: r1 = r0->field_b
    //     0x12979fc: ldur            w1, [x0, #0xb]
    // 0x1297a00: r0 = LoadInt32Instr(r1)
    //     0x1297a00: sbfx            x0, x1, #1, #0x1f
    // 0x1297a04: ldur            x3, [fp, #-0x60]
    // 0x1297a08: cmp             x3, x0
    // 0x1297a0c: b.ne            #0x1297a18
    // 0x1297a10: ldur            x1, [fp, #-0x48]
    // 0x1297a14: r0 = _growToNextCapacity()
    //     0x1297a14: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1297a18: ldur            x4, [fp, #-0x40]
    // 0x1297a1c: ldur            x5, [fp, #-0x38]
    // 0x1297a20: ldur            x3, [fp, #-0x48]
    // 0x1297a24: ldur            x6, [fp, #-0x28]
    // 0x1297a28: ldur            x2, [fp, #-0x60]
    // 0x1297a2c: add             x0, x2, #1
    // 0x1297a30: lsl             x1, x0, #1
    // 0x1297a34: StoreField: r3->field_b = r1
    //     0x1297a34: stur            w1, [x3, #0xb]
    // 0x1297a38: mov             x1, x2
    // 0x1297a3c: cmp             x1, x0
    // 0x1297a40: b.hs            #0x1297b04
    // 0x1297a44: LoadField: r1 = r3->field_f
    //     0x1297a44: ldur            w1, [x3, #0xf]
    // 0x1297a48: DecompressPointer r1
    //     0x1297a48: add             x1, x1, HEAP, lsl #32
    // 0x1297a4c: ldur            x0, [fp, #-8]
    // 0x1297a50: ArrayStore: r1[r2] = r0  ; List_4
    //     0x1297a50: add             x25, x1, x2, lsl #2
    //     0x1297a54: add             x25, x25, #0xf
    //     0x1297a58: str             w0, [x25]
    //     0x1297a5c: tbz             w0, #0, #0x1297a78
    //     0x1297a60: ldurb           w16, [x1, #-1]
    //     0x1297a64: ldurb           w17, [x0, #-1]
    //     0x1297a68: and             x16, x17, x16, lsr #2
    //     0x1297a6c: tst             x16, HEAP, lsr #32
    //     0x1297a70: b.eq            #0x1297a78
    //     0x1297a74: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1297a78: r0 = Column()
    //     0x1297a78: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1297a7c: mov             x1, x0
    // 0x1297a80: r0 = Instance_Axis
    //     0x1297a80: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1297a84: stur            x1, [fp, #-8]
    // 0x1297a88: StoreField: r1->field_f = r0
    //     0x1297a88: stur            w0, [x1, #0xf]
    // 0x1297a8c: ldur            x0, [fp, #-0x38]
    // 0x1297a90: StoreField: r1->field_13 = r0
    //     0x1297a90: stur            w0, [x1, #0x13]
    // 0x1297a94: r0 = Instance_MainAxisSize
    //     0x1297a94: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1297a98: ldr             x0, [x0, #0xa10]
    // 0x1297a9c: ArrayStore: r1[0] = r0  ; List_4
    //     0x1297a9c: stur            w0, [x1, #0x17]
    // 0x1297aa0: ldur            x0, [fp, #-0x28]
    // 0x1297aa4: StoreField: r1->field_1b = r0
    //     0x1297aa4: stur            w0, [x1, #0x1b]
    // 0x1297aa8: r0 = Instance_VerticalDirection
    //     0x1297aa8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1297aac: ldr             x0, [x0, #0xa20]
    // 0x1297ab0: StoreField: r1->field_23 = r0
    //     0x1297ab0: stur            w0, [x1, #0x23]
    // 0x1297ab4: r0 = Instance_Clip
    //     0x1297ab4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1297ab8: ldr             x0, [x0, #0x38]
    // 0x1297abc: StoreField: r1->field_2b = r0
    //     0x1297abc: stur            w0, [x1, #0x2b]
    // 0x1297ac0: StoreField: r1->field_2f = rZR
    //     0x1297ac0: stur            xzr, [x1, #0x2f]
    // 0x1297ac4: ldur            x0, [fp, #-0x48]
    // 0x1297ac8: StoreField: r1->field_b = r0
    //     0x1297ac8: stur            w0, [x1, #0xb]
    // 0x1297acc: r0 = Padding()
    //     0x1297acc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1297ad0: ldur            x1, [fp, #-0x40]
    // 0x1297ad4: StoreField: r0->field_f = r1
    //     0x1297ad4: stur            w1, [x0, #0xf]
    // 0x1297ad8: ldur            x1, [fp, #-8]
    // 0x1297adc: StoreField: r0->field_b = r1
    //     0x1297adc: stur            w1, [x0, #0xb]
    // 0x1297ae0: LeaveFrame
    //     0x1297ae0: mov             SP, fp
    //     0x1297ae4: ldp             fp, lr, [SP], #0x10
    // 0x1297ae8: ret
    //     0x1297ae8: ret             
    // 0x1297aec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1297aec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1297af0: b               #0x1296858
    // 0x1297af4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1297af4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1297af8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1297af8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1297afc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1297afc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1297b00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1297b00: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1297b04: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1297b04: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1297b14, size: 0xb4
    // 0x1297b14: EnterFrame
    //     0x1297b14: stp             fp, lr, [SP, #-0x10]!
    //     0x1297b18: mov             fp, SP
    // 0x1297b1c: AllocStack(0x30)
    //     0x1297b1c: sub             SP, SP, #0x30
    // 0x1297b20: SetupParameters()
    //     0x1297b20: ldr             x0, [fp, #0x10]
    //     0x1297b24: ldur            w1, [x0, #0x17]
    //     0x1297b28: add             x1, x1, HEAP, lsl #32
    // 0x1297b2c: CheckStackOverflow
    //     0x1297b2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1297b30: cmp             SP, x16
    //     0x1297b34: b.ls            #0x1297bc0
    // 0x1297b38: LoadField: r0 = r1->field_f
    //     0x1297b38: ldur            w0, [x1, #0xf]
    // 0x1297b3c: DecompressPointer r0
    //     0x1297b3c: add             x0, x0, HEAP, lsl #32
    // 0x1297b40: LoadField: r1 = r0->field_2f
    //     0x1297b40: ldur            w1, [x0, #0x2f]
    // 0x1297b44: DecompressPointer r1
    //     0x1297b44: add             x1, x1, HEAP, lsl #32
    // 0x1297b48: LoadField: r2 = r0->field_2b
    //     0x1297b48: ldur            w2, [x0, #0x2b]
    // 0x1297b4c: DecompressPointer r2
    //     0x1297b4c: add             x2, x2, HEAP, lsl #32
    // 0x1297b50: LoadField: r3 = r0->field_37
    //     0x1297b50: ldur            w3, [x0, #0x37]
    // 0x1297b54: DecompressPointer r3
    //     0x1297b54: add             x3, x3, HEAP, lsl #32
    // 0x1297b58: LoadField: r4 = r0->field_33
    //     0x1297b58: ldur            w4, [x0, #0x33]
    // 0x1297b5c: DecompressPointer r4
    //     0x1297b5c: add             x4, x4, HEAP, lsl #32
    // 0x1297b60: ArrayLoad: r5 = r0[0]  ; List_4
    //     0x1297b60: ldur            w5, [x0, #0x17]
    // 0x1297b64: DecompressPointer r5
    //     0x1297b64: add             x5, x5, HEAP, lsl #32
    // 0x1297b68: cmp             w5, NULL
    // 0x1297b6c: b.ne            #0x1297b78
    // 0x1297b70: r5 = Null
    //     0x1297b70: mov             x5, NULL
    // 0x1297b74: b               #0x1297b84
    // 0x1297b78: LoadField: r6 = r5->field_b
    //     0x1297b78: ldur            w6, [x5, #0xb]
    // 0x1297b7c: DecompressPointer r6
    //     0x1297b7c: add             x6, x6, HEAP, lsl #32
    // 0x1297b80: mov             x5, x6
    // 0x1297b84: LoadField: r6 = r0->field_4b
    //     0x1297b84: ldur            w6, [x0, #0x4b]
    // 0x1297b88: DecompressPointer r6
    //     0x1297b88: add             x6, x6, HEAP, lsl #32
    // 0x1297b8c: stp             x1, x6, [SP, #0x20]
    // 0x1297b90: stp             x3, x2, [SP, #0x10]
    // 0x1297b94: stp             x5, x4, [SP]
    // 0x1297b98: r4 = 0
    //     0x1297b98: movz            x4, #0
    // 0x1297b9c: ldr             x0, [SP, #0x28]
    // 0x1297ba0: r16 = UnlinkedCall_0x613b5c
    //     0x1297ba0: add             x16, PP, #0x48, lsl #12  ; [pp+0x48468] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x1297ba4: add             x16, x16, #0x468
    // 0x1297ba8: ldp             x5, lr, [x16]
    // 0x1297bac: blr             lr
    // 0x1297bb0: r0 = Null
    //     0x1297bb0: mov             x0, NULL
    // 0x1297bb4: LeaveFrame
    //     0x1297bb4: mov             SP, fp
    //     0x1297bb8: ldp             fp, lr, [SP], #0x10
    // 0x1297bbc: ret
    //     0x1297bbc: ret             
    // 0x1297bc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1297bc0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1297bc4: b               #0x1297b38
  }
}
