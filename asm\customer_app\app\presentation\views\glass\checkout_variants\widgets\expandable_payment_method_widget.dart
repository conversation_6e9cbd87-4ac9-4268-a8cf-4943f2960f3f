// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart

// class id: 1049373, size: 0x8
class :: {
}

// class id: 3358, size: 0x14, field offset: 0x14
class _ExpandablePaymentMethodWidgetState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x940c18, size: 0x30
    // 0x940c18: EnterFrame
    //     0x940c18: stp             fp, lr, [SP, #-0x10]!
    //     0x940c1c: mov             fp, SP
    // 0x940c20: CheckStackOverflow
    //     0x940c20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x940c24: cmp             SP, x16
    //     0x940c28: b.ls            #0x940c40
    // 0x940c2c: r0 = initializePaymentModes()
    //     0x940c2c: bl              #0x940c6c  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::initializePaymentModes
    // 0x940c30: r0 = Null
    //     0x940c30: mov             x0, NULL
    // 0x940c34: LeaveFrame
    //     0x940c34: mov             SP, fp
    //     0x940c38: ldp             fp, lr, [SP], #0x10
    // 0x940c3c: ret
    //     0x940c3c: ret             
    // 0x940c40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x940c40: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x940c44: b               #0x940c2c
  }
  _ initializePaymentModes(/* No info */) {
    // ** addr: 0x940c6c, size: 0x3c0
    // 0x940c6c: EnterFrame
    //     0x940c6c: stp             fp, lr, [SP, #-0x10]!
    //     0x940c70: mov             fp, SP
    // 0x940c74: AllocStack(0x38)
    //     0x940c74: sub             SP, SP, #0x38
    // 0x940c78: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x940c78: stur            x1, [fp, #-8]
    // 0x940c7c: CheckStackOverflow
    //     0x940c7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x940c80: cmp             SP, x16
    //     0x940c84: b.ls            #0x941008
    // 0x940c88: r1 = 1
    //     0x940c88: movz            x1, #0x1
    // 0x940c8c: r0 = AllocateContext()
    //     0x940c8c: bl              #0x16f6108  ; AllocateContextStub
    // 0x940c90: mov             x2, x0
    // 0x940c94: ldur            x1, [fp, #-8]
    // 0x940c98: stur            x2, [fp, #-0x10]
    // 0x940c9c: StoreField: r2->field_f = r1
    //     0x940c9c: stur            w1, [x2, #0xf]
    // 0x940ca0: LoadField: r0 = r1->field_b
    //     0x940ca0: ldur            w0, [x1, #0xb]
    // 0x940ca4: DecompressPointer r0
    //     0x940ca4: add             x0, x0, HEAP, lsl #32
    // 0x940ca8: cmp             w0, NULL
    // 0x940cac: b.eq            #0x941010
    // 0x940cb0: LoadField: r3 = r0->field_13
    //     0x940cb0: ldur            w3, [x0, #0x13]
    // 0x940cb4: DecompressPointer r3
    //     0x940cb4: add             x3, x3, HEAP, lsl #32
    // 0x940cb8: r0 = LoadClassIdInstr(r3)
    //     0x940cb8: ldur            x0, [x3, #-1]
    //     0x940cbc: ubfx            x0, x0, #0xc, #0x14
    // 0x940cc0: r16 = "online"
    //     0x940cc0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a50] "online"
    //     0x940cc4: ldr             x16, [x16, #0xa50]
    // 0x940cc8: stp             x16, x3, [SP]
    // 0x940ccc: mov             lr, x0
    // 0x940cd0: ldr             lr, [x21, lr, lsl #3]
    // 0x940cd4: blr             lr
    // 0x940cd8: tbz             w0, #4, #0x940d1c
    // 0x940cdc: ldur            x1, [fp, #-8]
    // 0x940ce0: LoadField: r0 = r1->field_b
    //     0x940ce0: ldur            w0, [x1, #0xb]
    // 0x940ce4: DecompressPointer r0
    //     0x940ce4: add             x0, x0, HEAP, lsl #32
    // 0x940ce8: cmp             w0, NULL
    // 0x940cec: b.eq            #0x941014
    // 0x940cf0: LoadField: r2 = r0->field_13
    //     0x940cf0: ldur            w2, [x0, #0x13]
    // 0x940cf4: DecompressPointer r2
    //     0x940cf4: add             x2, x2, HEAP, lsl #32
    // 0x940cf8: r0 = LoadClassIdInstr(r2)
    //     0x940cf8: ldur            x0, [x2, #-1]
    //     0x940cfc: ubfx            x0, x0, #0xc, #0x14
    // 0x940d00: r16 = "partial-cod"
    //     0x940d00: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c830] "partial-cod"
    //     0x940d04: ldr             x16, [x16, #0x830]
    // 0x940d08: stp             x16, x2, [SP]
    // 0x940d0c: mov             lr, x0
    // 0x940d10: ldr             lr, [x21, lr, lsl #3]
    // 0x940d14: blr             lr
    // 0x940d18: tbnz            w0, #4, #0x940ef4
    // 0x940d1c: ldur            x0, [fp, #-8]
    // 0x940d20: LoadField: r2 = r0->field_b
    //     0x940d20: ldur            w2, [x0, #0xb]
    // 0x940d24: DecompressPointer r2
    //     0x940d24: add             x2, x2, HEAP, lsl #32
    // 0x940d28: cmp             w2, NULL
    // 0x940d2c: b.eq            #0x941018
    // 0x940d30: LoadField: r0 = r2->field_b
    //     0x940d30: ldur            w0, [x2, #0xb]
    // 0x940d34: DecompressPointer r0
    //     0x940d34: add             x0, x0, HEAP, lsl #32
    // 0x940d38: LoadField: r1 = r0->field_b
    //     0x940d38: ldur            w1, [x0, #0xb]
    // 0x940d3c: DecompressPointer r1
    //     0x940d3c: add             x1, x1, HEAP, lsl #32
    // 0x940d40: cmp             w1, NULL
    // 0x940d44: b.ne            #0x940d50
    // 0x940d48: r0 = Null
    //     0x940d48: mov             x0, NULL
    // 0x940d4c: b               #0x940d7c
    // 0x940d50: LoadField: r0 = r1->field_27
    //     0x940d50: ldur            w0, [x1, #0x27]
    // 0x940d54: DecompressPointer r0
    //     0x940d54: add             x0, x0, HEAP, lsl #32
    // 0x940d58: cmp             w0, NULL
    // 0x940d5c: b.ne            #0x940d68
    // 0x940d60: r0 = Null
    //     0x940d60: mov             x0, NULL
    // 0x940d64: b               #0x940d7c
    // 0x940d68: LoadField: r3 = r0->field_b
    //     0x940d68: ldur            w3, [x0, #0xb]
    // 0x940d6c: cbnz            w3, #0x940d78
    // 0x940d70: r0 = false
    //     0x940d70: add             x0, NULL, #0x30  ; false
    // 0x940d74: b               #0x940d7c
    // 0x940d78: r0 = true
    //     0x940d78: add             x0, NULL, #0x20  ; true
    // 0x940d7c: cmp             w0, NULL
    // 0x940d80: b.eq            #0x940ff8
    // 0x940d84: tbnz            w0, #4, #0x940ff8
    // 0x940d88: cmp             w1, NULL
    // 0x940d8c: b.ne            #0x940d98
    // 0x940d90: r0 = Null
    //     0x940d90: mov             x0, NULL
    // 0x940d94: b               #0x940de0
    // 0x940d98: LoadField: r3 = r1->field_27
    //     0x940d98: ldur            w3, [x1, #0x27]
    // 0x940d9c: DecompressPointer r3
    //     0x940d9c: add             x3, x3, HEAP, lsl #32
    // 0x940da0: cmp             w3, NULL
    // 0x940da4: b.ne            #0x940db0
    // 0x940da8: r0 = Null
    //     0x940da8: mov             x0, NULL
    // 0x940dac: b               #0x940de0
    // 0x940db0: LoadField: r0 = r3->field_b
    //     0x940db0: ldur            w0, [x3, #0xb]
    // 0x940db4: r1 = LoadInt32Instr(r0)
    //     0x940db4: sbfx            x1, x0, #1, #0x1f
    // 0x940db8: mov             x0, x1
    // 0x940dbc: r1 = 0
    //     0x940dbc: movz            x1, #0
    // 0x940dc0: cmp             x1, x0
    // 0x940dc4: b.hs            #0x94101c
    // 0x940dc8: LoadField: r0 = r3->field_f
    //     0x940dc8: ldur            w0, [x3, #0xf]
    // 0x940dcc: DecompressPointer r0
    //     0x940dcc: add             x0, x0, HEAP, lsl #32
    // 0x940dd0: LoadField: r1 = r0->field_f
    //     0x940dd0: ldur            w1, [x0, #0xf]
    // 0x940dd4: DecompressPointer r1
    //     0x940dd4: add             x1, x1, HEAP, lsl #32
    // 0x940dd8: LoadField: r0 = r1->field_f
    //     0x940dd8: ldur            w0, [x1, #0xf]
    // 0x940ddc: DecompressPointer r0
    //     0x940ddc: add             x0, x0, HEAP, lsl #32
    // 0x940de0: cmp             w0, NULL
    // 0x940de4: b.ne            #0x940dec
    // 0x940de8: r0 = ""
    //     0x940de8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x940dec: ArrayStore: r2[0] = r0  ; List_4
    //     0x940dec: stur            w0, [x2, #0x17]
    //     0x940df0: ldurb           w16, [x2, #-1]
    //     0x940df4: ldurb           w17, [x0, #-1]
    //     0x940df8: and             x16, x17, x16, lsr #2
    //     0x940dfc: tst             x16, HEAP, lsr #32
    //     0x940e00: b.eq            #0x940e08
    //     0x940e04: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x940e08: StoreField: r2->field_1b = rZR
    //     0x940e08: stur            xzr, [x2, #0x1b]
    // 0x940e0c: r0 = LoadStaticField(0x878)
    //     0x940e0c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x940e10: ldr             x0, [x0, #0x10f0]
    // 0x940e14: cmp             w0, NULL
    // 0x940e18: b.eq            #0x941020
    // 0x940e1c: LoadField: r3 = r0->field_53
    //     0x940e1c: ldur            w3, [x0, #0x53]
    // 0x940e20: DecompressPointer r3
    //     0x940e20: add             x3, x3, HEAP, lsl #32
    // 0x940e24: stur            x3, [fp, #-0x20]
    // 0x940e28: LoadField: r0 = r3->field_7
    //     0x940e28: ldur            w0, [x3, #7]
    // 0x940e2c: DecompressPointer r0
    //     0x940e2c: add             x0, x0, HEAP, lsl #32
    // 0x940e30: ldur            x2, [fp, #-0x10]
    // 0x940e34: stur            x0, [fp, #-0x18]
    // 0x940e38: r1 = Function '<anonymous closure>':.
    //     0x940e38: add             x1, PP, #0x56, lsl #12  ; [pp+0x56c40] AnonymousClosure: (0x941130), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::initializePaymentModes (0x940c6c)
    //     0x940e3c: ldr             x1, [x1, #0xc40]
    // 0x940e40: r0 = AllocateClosure()
    //     0x940e40: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x940e44: ldur            x2, [fp, #-0x18]
    // 0x940e48: mov             x3, x0
    // 0x940e4c: r1 = Null
    //     0x940e4c: mov             x1, NULL
    // 0x940e50: stur            x3, [fp, #-0x18]
    // 0x940e54: cmp             w2, NULL
    // 0x940e58: b.eq            #0x940e78
    // 0x940e5c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x940e5c: ldur            w4, [x2, #0x17]
    // 0x940e60: DecompressPointer r4
    //     0x940e60: add             x4, x4, HEAP, lsl #32
    // 0x940e64: r8 = X0
    //     0x940e64: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x940e68: LoadField: r9 = r4->field_7
    //     0x940e68: ldur            x9, [x4, #7]
    // 0x940e6c: r3 = Null
    //     0x940e6c: add             x3, PP, #0x56, lsl #12  ; [pp+0x56c48] Null
    //     0x940e70: ldr             x3, [x3, #0xc48]
    // 0x940e74: blr             x9
    // 0x940e78: ldur            x0, [fp, #-0x20]
    // 0x940e7c: LoadField: r1 = r0->field_b
    //     0x940e7c: ldur            w1, [x0, #0xb]
    // 0x940e80: LoadField: r2 = r0->field_f
    //     0x940e80: ldur            w2, [x0, #0xf]
    // 0x940e84: DecompressPointer r2
    //     0x940e84: add             x2, x2, HEAP, lsl #32
    // 0x940e88: LoadField: r3 = r2->field_b
    //     0x940e88: ldur            w3, [x2, #0xb]
    // 0x940e8c: r2 = LoadInt32Instr(r1)
    //     0x940e8c: sbfx            x2, x1, #1, #0x1f
    // 0x940e90: stur            x2, [fp, #-0x28]
    // 0x940e94: r1 = LoadInt32Instr(r3)
    //     0x940e94: sbfx            x1, x3, #1, #0x1f
    // 0x940e98: cmp             x2, x1
    // 0x940e9c: b.ne            #0x940ea8
    // 0x940ea0: mov             x1, x0
    // 0x940ea4: r0 = _growToNextCapacity()
    //     0x940ea4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x940ea8: ldur            x0, [fp, #-0x20]
    // 0x940eac: ldur            x2, [fp, #-0x28]
    // 0x940eb0: add             x1, x2, #1
    // 0x940eb4: lsl             x3, x1, #1
    // 0x940eb8: StoreField: r0->field_b = r3
    //     0x940eb8: stur            w3, [x0, #0xb]
    // 0x940ebc: LoadField: r1 = r0->field_f
    //     0x940ebc: ldur            w1, [x0, #0xf]
    // 0x940ec0: DecompressPointer r1
    //     0x940ec0: add             x1, x1, HEAP, lsl #32
    // 0x940ec4: ldur            x0, [fp, #-0x18]
    // 0x940ec8: ArrayStore: r1[r2] = r0  ; List_4
    //     0x940ec8: add             x25, x1, x2, lsl #2
    //     0x940ecc: add             x25, x25, #0xf
    //     0x940ed0: str             w0, [x25]
    //     0x940ed4: tbz             w0, #0, #0x940ef0
    //     0x940ed8: ldurb           w16, [x1, #-1]
    //     0x940edc: ldurb           w17, [x0, #-1]
    //     0x940ee0: and             x16, x17, x16, lsr #2
    //     0x940ee4: tst             x16, HEAP, lsr #32
    //     0x940ee8: b.eq            #0x940ef0
    //     0x940eec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x940ef0: b               #0x940ff8
    // 0x940ef4: ldur            x0, [fp, #-8]
    // 0x940ef8: r1 = ""
    //     0x940ef8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x940efc: LoadField: r2 = r0->field_b
    //     0x940efc: ldur            w2, [x0, #0xb]
    // 0x940f00: DecompressPointer r2
    //     0x940f00: add             x2, x2, HEAP, lsl #32
    // 0x940f04: cmp             w2, NULL
    // 0x940f08: b.eq            #0x941024
    // 0x940f0c: StoreField: r2->field_1b = rZR
    //     0x940f0c: stur            xzr, [x2, #0x1b]
    // 0x940f10: ArrayStore: r2[0] = r1  ; List_4
    //     0x940f10: stur            w1, [x2, #0x17]
    // 0x940f14: r0 = LoadStaticField(0x878)
    //     0x940f14: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x940f18: ldr             x0, [x0, #0x10f0]
    // 0x940f1c: cmp             w0, NULL
    // 0x940f20: b.eq            #0x941028
    // 0x940f24: LoadField: r3 = r0->field_53
    //     0x940f24: ldur            w3, [x0, #0x53]
    // 0x940f28: DecompressPointer r3
    //     0x940f28: add             x3, x3, HEAP, lsl #32
    // 0x940f2c: stur            x3, [fp, #-0x18]
    // 0x940f30: LoadField: r0 = r3->field_7
    //     0x940f30: ldur            w0, [x3, #7]
    // 0x940f34: DecompressPointer r0
    //     0x940f34: add             x0, x0, HEAP, lsl #32
    // 0x940f38: ldur            x2, [fp, #-0x10]
    // 0x940f3c: stur            x0, [fp, #-8]
    // 0x940f40: r1 = Function '<anonymous closure>':.
    //     0x940f40: add             x1, PP, #0x56, lsl #12  ; [pp+0x56c58] AnonymousClosure: (0x94102c), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::initializePaymentModes (0x940c6c)
    //     0x940f44: ldr             x1, [x1, #0xc58]
    // 0x940f48: r0 = AllocateClosure()
    //     0x940f48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x940f4c: ldur            x2, [fp, #-8]
    // 0x940f50: mov             x3, x0
    // 0x940f54: r1 = Null
    //     0x940f54: mov             x1, NULL
    // 0x940f58: stur            x3, [fp, #-8]
    // 0x940f5c: cmp             w2, NULL
    // 0x940f60: b.eq            #0x940f80
    // 0x940f64: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x940f64: ldur            w4, [x2, #0x17]
    // 0x940f68: DecompressPointer r4
    //     0x940f68: add             x4, x4, HEAP, lsl #32
    // 0x940f6c: r8 = X0
    //     0x940f6c: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x940f70: LoadField: r9 = r4->field_7
    //     0x940f70: ldur            x9, [x4, #7]
    // 0x940f74: r3 = Null
    //     0x940f74: add             x3, PP, #0x56, lsl #12  ; [pp+0x56c60] Null
    //     0x940f78: ldr             x3, [x3, #0xc60]
    // 0x940f7c: blr             x9
    // 0x940f80: ldur            x0, [fp, #-0x18]
    // 0x940f84: LoadField: r1 = r0->field_b
    //     0x940f84: ldur            w1, [x0, #0xb]
    // 0x940f88: LoadField: r2 = r0->field_f
    //     0x940f88: ldur            w2, [x0, #0xf]
    // 0x940f8c: DecompressPointer r2
    //     0x940f8c: add             x2, x2, HEAP, lsl #32
    // 0x940f90: LoadField: r3 = r2->field_b
    //     0x940f90: ldur            w3, [x2, #0xb]
    // 0x940f94: r2 = LoadInt32Instr(r1)
    //     0x940f94: sbfx            x2, x1, #1, #0x1f
    // 0x940f98: stur            x2, [fp, #-0x28]
    // 0x940f9c: r1 = LoadInt32Instr(r3)
    //     0x940f9c: sbfx            x1, x3, #1, #0x1f
    // 0x940fa0: cmp             x2, x1
    // 0x940fa4: b.ne            #0x940fb0
    // 0x940fa8: mov             x1, x0
    // 0x940fac: r0 = _growToNextCapacity()
    //     0x940fac: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x940fb0: ldur            x2, [fp, #-0x18]
    // 0x940fb4: ldur            x3, [fp, #-0x28]
    // 0x940fb8: add             x4, x3, #1
    // 0x940fbc: lsl             x5, x4, #1
    // 0x940fc0: StoreField: r2->field_b = r5
    //     0x940fc0: stur            w5, [x2, #0xb]
    // 0x940fc4: LoadField: r1 = r2->field_f
    //     0x940fc4: ldur            w1, [x2, #0xf]
    // 0x940fc8: DecompressPointer r1
    //     0x940fc8: add             x1, x1, HEAP, lsl #32
    // 0x940fcc: ldur            x0, [fp, #-8]
    // 0x940fd0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x940fd0: add             x25, x1, x3, lsl #2
    //     0x940fd4: add             x25, x25, #0xf
    //     0x940fd8: str             w0, [x25]
    //     0x940fdc: tbz             w0, #0, #0x940ff8
    //     0x940fe0: ldurb           w16, [x1, #-1]
    //     0x940fe4: ldurb           w17, [x0, #-1]
    //     0x940fe8: and             x16, x17, x16, lsr #2
    //     0x940fec: tst             x16, HEAP, lsr #32
    //     0x940ff0: b.eq            #0x940ff8
    //     0x940ff4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x940ff8: r0 = Null
    //     0x940ff8: mov             x0, NULL
    // 0x940ffc: LeaveFrame
    //     0x940ffc: mov             SP, fp
    //     0x941000: ldp             fp, lr, [SP], #0x10
    // 0x941004: ret
    //     0x941004: ret             
    // 0x941008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x941008: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94100c: b               #0x940c88
    // 0x941010: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941010: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941014: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941014: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941018: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941018: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94101c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x94101c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x941020: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941020: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941024: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941024: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941028: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941028: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x94102c, size: 0x104
    // 0x94102c: EnterFrame
    //     0x94102c: stp             fp, lr, [SP, #-0x10]!
    //     0x941030: mov             fp, SP
    // 0x941034: AllocStack(0x30)
    //     0x941034: sub             SP, SP, #0x30
    // 0x941038: SetupParameters()
    //     0x941038: ldr             x0, [fp, #0x18]
    //     0x94103c: ldur            w2, [x0, #0x17]
    //     0x941040: add             x2, x2, HEAP, lsl #32
    //     0x941044: stur            x2, [fp, #-8]
    // 0x941048: CheckStackOverflow
    //     0x941048: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94104c: cmp             SP, x16
    //     0x941050: b.ls            #0x941120
    // 0x941054: LoadField: r0 = r2->field_f
    //     0x941054: ldur            w0, [x2, #0xf]
    // 0x941058: DecompressPointer r0
    //     0x941058: add             x0, x0, HEAP, lsl #32
    // 0x94105c: LoadField: r1 = r0->field_b
    //     0x94105c: ldur            w1, [x0, #0xb]
    // 0x941060: DecompressPointer r1
    //     0x941060: add             x1, x1, HEAP, lsl #32
    // 0x941064: cmp             w1, NULL
    // 0x941068: b.eq            #0x941128
    // 0x94106c: LoadField: r3 = r1->field_13
    //     0x94106c: ldur            w3, [x1, #0x13]
    // 0x941070: DecompressPointer r3
    //     0x941070: add             x3, x3, HEAP, lsl #32
    // 0x941074: LoadField: r4 = r1->field_1b
    //     0x941074: ldur            x4, [x1, #0x1b]
    // 0x941078: LoadField: r5 = r1->field_f
    //     0x941078: ldur            w5, [x1, #0xf]
    // 0x94107c: DecompressPointer r5
    //     0x94107c: add             x5, x5, HEAP, lsl #32
    // 0x941080: r0 = BoxInt64Instr(r4)
    //     0x941080: sbfiz           x0, x4, #1, #0x1f
    //     0x941084: cmp             x4, x0, asr #1
    //     0x941088: b.eq            #0x941094
    //     0x94108c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x941090: stur            x4, [x0, #7]
    // 0x941094: stp             x3, x5, [SP, #0x18]
    // 0x941098: r16 = ""
    //     0x941098: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x94109c: stp             x0, x16, [SP, #8]
    // 0x9410a0: r16 = true
    //     0x9410a0: add             x16, NULL, #0x20  ; true
    // 0x9410a4: str             x16, [SP]
    // 0x9410a8: r4 = 0
    //     0x9410a8: movz            x4, #0
    // 0x9410ac: ldr             x0, [SP, #0x20]
    // 0x9410b0: r16 = UnlinkedCall_0x613b5c
    //     0x9410b0: add             x16, PP, #0x56, lsl #12  ; [pp+0x56c70] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9410b4: add             x16, x16, #0xc70
    // 0x9410b8: ldp             x5, lr, [x16]
    // 0x9410bc: blr             lr
    // 0x9410c0: ldur            x0, [fp, #-8]
    // 0x9410c4: LoadField: r1 = r0->field_f
    //     0x9410c4: ldur            w1, [x0, #0xf]
    // 0x9410c8: DecompressPointer r1
    //     0x9410c8: add             x1, x1, HEAP, lsl #32
    // 0x9410cc: LoadField: r0 = r1->field_b
    //     0x9410cc: ldur            w0, [x1, #0xb]
    // 0x9410d0: DecompressPointer r0
    //     0x9410d0: add             x0, x0, HEAP, lsl #32
    // 0x9410d4: cmp             w0, NULL
    // 0x9410d8: b.eq            #0x94112c
    // 0x9410dc: LoadField: r1 = r0->field_13
    //     0x9410dc: ldur            w1, [x0, #0x13]
    // 0x9410e0: DecompressPointer r1
    //     0x9410e0: add             x1, x1, HEAP, lsl #32
    // 0x9410e4: LoadField: r2 = r0->field_23
    //     0x9410e4: ldur            w2, [x0, #0x23]
    // 0x9410e8: DecompressPointer r2
    //     0x9410e8: add             x2, x2, HEAP, lsl #32
    // 0x9410ec: stp             x1, x2, [SP, #8]
    // 0x9410f0: r16 = ""
    //     0x9410f0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9410f4: str             x16, [SP]
    // 0x9410f8: r4 = 0
    //     0x9410f8: movz            x4, #0
    // 0x9410fc: ldr             x0, [SP, #0x10]
    // 0x941100: r16 = UnlinkedCall_0x613b5c
    //     0x941100: add             x16, PP, #0x56, lsl #12  ; [pp+0x56c80] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x941104: add             x16, x16, #0xc80
    // 0x941108: ldp             x5, lr, [x16]
    // 0x94110c: blr             lr
    // 0x941110: r0 = Null
    //     0x941110: mov             x0, NULL
    // 0x941114: LeaveFrame
    //     0x941114: mov             SP, fp
    //     0x941118: ldp             fp, lr, [SP], #0x10
    // 0x94111c: ret
    //     0x94111c: ret             
    // 0x941120: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x941120: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x941124: b               #0x941054
    // 0x941128: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941128: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94112c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94112c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x941130, size: 0x10c
    // 0x941130: EnterFrame
    //     0x941130: stp             fp, lr, [SP, #-0x10]!
    //     0x941134: mov             fp, SP
    // 0x941138: AllocStack(0x30)
    //     0x941138: sub             SP, SP, #0x30
    // 0x94113c: SetupParameters()
    //     0x94113c: ldr             x0, [fp, #0x18]
    //     0x941140: ldur            w2, [x0, #0x17]
    //     0x941144: add             x2, x2, HEAP, lsl #32
    //     0x941148: stur            x2, [fp, #-8]
    // 0x94114c: CheckStackOverflow
    //     0x94114c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x941150: cmp             SP, x16
    //     0x941154: b.ls            #0x94122c
    // 0x941158: LoadField: r0 = r2->field_f
    //     0x941158: ldur            w0, [x2, #0xf]
    // 0x94115c: DecompressPointer r0
    //     0x94115c: add             x0, x0, HEAP, lsl #32
    // 0x941160: LoadField: r1 = r0->field_b
    //     0x941160: ldur            w1, [x0, #0xb]
    // 0x941164: DecompressPointer r1
    //     0x941164: add             x1, x1, HEAP, lsl #32
    // 0x941168: cmp             w1, NULL
    // 0x94116c: b.eq            #0x941234
    // 0x941170: LoadField: r3 = r1->field_13
    //     0x941170: ldur            w3, [x1, #0x13]
    // 0x941174: DecompressPointer r3
    //     0x941174: add             x3, x3, HEAP, lsl #32
    // 0x941178: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x941178: ldur            w4, [x1, #0x17]
    // 0x94117c: DecompressPointer r4
    //     0x94117c: add             x4, x4, HEAP, lsl #32
    // 0x941180: LoadField: r5 = r1->field_1b
    //     0x941180: ldur            x5, [x1, #0x1b]
    // 0x941184: LoadField: r6 = r1->field_f
    //     0x941184: ldur            w6, [x1, #0xf]
    // 0x941188: DecompressPointer r6
    //     0x941188: add             x6, x6, HEAP, lsl #32
    // 0x94118c: r0 = BoxInt64Instr(r5)
    //     0x94118c: sbfiz           x0, x5, #1, #0x1f
    //     0x941190: cmp             x5, x0, asr #1
    //     0x941194: b.eq            #0x9411a0
    //     0x941198: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94119c: stur            x5, [x0, #7]
    // 0x9411a0: stp             x3, x6, [SP, #0x18]
    // 0x9411a4: stp             x0, x4, [SP, #8]
    // 0x9411a8: r16 = true
    //     0x9411a8: add             x16, NULL, #0x20  ; true
    // 0x9411ac: str             x16, [SP]
    // 0x9411b0: r4 = 0
    //     0x9411b0: movz            x4, #0
    // 0x9411b4: ldr             x0, [SP, #0x20]
    // 0x9411b8: r16 = UnlinkedCall_0x613b5c
    //     0x9411b8: add             x16, PP, #0x56, lsl #12  ; [pp+0x56c90] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9411bc: add             x16, x16, #0xc90
    // 0x9411c0: ldp             x5, lr, [x16]
    // 0x9411c4: blr             lr
    // 0x9411c8: ldur            x0, [fp, #-8]
    // 0x9411cc: LoadField: r1 = r0->field_f
    //     0x9411cc: ldur            w1, [x0, #0xf]
    // 0x9411d0: DecompressPointer r1
    //     0x9411d0: add             x1, x1, HEAP, lsl #32
    // 0x9411d4: LoadField: r0 = r1->field_b
    //     0x9411d4: ldur            w0, [x1, #0xb]
    // 0x9411d8: DecompressPointer r0
    //     0x9411d8: add             x0, x0, HEAP, lsl #32
    // 0x9411dc: cmp             w0, NULL
    // 0x9411e0: b.eq            #0x941238
    // 0x9411e4: LoadField: r1 = r0->field_13
    //     0x9411e4: ldur            w1, [x0, #0x13]
    // 0x9411e8: DecompressPointer r1
    //     0x9411e8: add             x1, x1, HEAP, lsl #32
    // 0x9411ec: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x9411ec: ldur            w2, [x0, #0x17]
    // 0x9411f0: DecompressPointer r2
    //     0x9411f0: add             x2, x2, HEAP, lsl #32
    // 0x9411f4: LoadField: r3 = r0->field_23
    //     0x9411f4: ldur            w3, [x0, #0x23]
    // 0x9411f8: DecompressPointer r3
    //     0x9411f8: add             x3, x3, HEAP, lsl #32
    // 0x9411fc: stp             x1, x3, [SP, #8]
    // 0x941200: str             x2, [SP]
    // 0x941204: r4 = 0
    //     0x941204: movz            x4, #0
    // 0x941208: ldr             x0, [SP, #0x10]
    // 0x94120c: r16 = UnlinkedCall_0x613b5c
    //     0x94120c: add             x16, PP, #0x56, lsl #12  ; [pp+0x56ca0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x941210: add             x16, x16, #0xca0
    // 0x941214: ldp             x5, lr, [x16]
    // 0x941218: blr             lr
    // 0x94121c: r0 = Null
    //     0x94121c: mov             x0, NULL
    // 0x941220: LeaveFrame
    //     0x941220: mov             SP, fp
    //     0x941224: ldp             fp, lr, [SP], #0x10
    // 0x941228: ret
    //     0x941228: ret             
    // 0x94122c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94122c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x941230: b               #0x941158
    // 0x941234: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941234: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941238: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941238: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa21634, size: 0x1bc
    // 0xa21634: EnterFrame
    //     0xa21634: stp             fp, lr, [SP, #-0x10]!
    //     0xa21638: mov             fp, SP
    // 0xa2163c: AllocStack(0x40)
    //     0xa2163c: sub             SP, SP, #0x40
    // 0xa21640: CheckStackOverflow
    //     0xa21640: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa21644: cmp             SP, x16
    //     0xa21648: b.ls            #0xa217e0
    // 0xa2164c: r1 = <Widget>
    //     0xa2164c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa21650: r2 = 60
    //     0xa21650: movz            x2, #0x3c
    // 0xa21654: r0 = _GrowableList()
    //     0xa21654: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xa21658: stur            x0, [fp, #-0x20]
    // 0xa2165c: LoadField: r1 = r0->field_b
    //     0xa2165c: ldur            w1, [x0, #0xb]
    // 0xa21660: r2 = LoadInt32Instr(r1)
    //     0xa21660: sbfx            x2, x1, #1, #0x1f
    // 0xa21664: stur            x2, [fp, #-0x18]
    // 0xa21668: LoadField: r3 = r0->field_f
    //     0xa21668: ldur            w3, [x0, #0xf]
    // 0xa2166c: DecompressPointer r3
    //     0xa2166c: add             x3, x3, HEAP, lsl #32
    // 0xa21670: stur            x3, [fp, #-0x10]
    // 0xa21674: r4 = 0
    //     0xa21674: movz            x4, #0
    // 0xa21678: stur            x4, [fp, #-8]
    // 0xa2167c: CheckStackOverflow
    //     0xa2167c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa21680: cmp             SP, x16
    //     0xa21684: b.ls            #0xa217e8
    // 0xa21688: cmp             x4, x2
    // 0xa2168c: b.ge            #0xa21780
    // 0xa21690: tbnz            w4, #0, #0xa216a4
    // 0xa21694: mov             x0, x4
    // 0xa21698: r1 = Instance_Color
    //     0xa21698: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xa2169c: ldr             x1, [x1, #0xf88]
    // 0xa216a0: b               #0xa216d4
    // 0xa216a4: ldr             x1, [fp, #0x18]
    // 0xa216a8: r0 = of()
    //     0xa216a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa216ac: LoadField: r1 = r0->field_5b
    //     0xa216ac: ldur            w1, [x0, #0x5b]
    // 0xa216b0: DecompressPointer r1
    //     0xa216b0: add             x1, x1, HEAP, lsl #32
    // 0xa216b4: r0 = LoadClassIdInstr(r1)
    //     0xa216b4: ldur            x0, [x1, #-1]
    //     0xa216b8: ubfx            x0, x0, #0xc, #0x14
    // 0xa216bc: d0 = 0.100000
    //     0xa216bc: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xa216c0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa216c0: sub             lr, x0, #0xffa
    //     0xa216c4: ldr             lr, [x21, lr, lsl #3]
    //     0xa216c8: blr             lr
    // 0xa216cc: mov             x1, x0
    // 0xa216d0: ldur            x0, [fp, #-8]
    // 0xa216d4: stur            x1, [fp, #-0x28]
    // 0xa216d8: r0 = Container()
    //     0xa216d8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa216dc: stur            x0, [fp, #-0x30]
    // 0xa216e0: ldur            x16, [fp, #-0x28]
    // 0xa216e4: r30 = 0.800000
    //     0xa216e4: add             lr, PP, #0x3c, lsl #12  ; [pp+0x3c3e0] 0.8
    //     0xa216e8: ldr             lr, [lr, #0x3e0]
    // 0xa216ec: stp             lr, x16, [SP]
    // 0xa216f0: mov             x1, x0
    // 0xa216f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, height, 0x2, null]
    //     0xa216f4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd30] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "height", 0x2, Null]
    //     0xa216f8: ldr             x4, [x4, #0xd30]
    // 0xa216fc: r0 = Container()
    //     0xa216fc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa21700: r1 = <FlexParentData>
    //     0xa21700: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa21704: ldr             x1, [x1, #0xe00]
    // 0xa21708: r0 = Expanded()
    //     0xa21708: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa2170c: mov             x2, x0
    // 0xa21710: r0 = 1
    //     0xa21710: movz            x0, #0x1
    // 0xa21714: stur            x2, [fp, #-0x28]
    // 0xa21718: StoreField: r2->field_13 = r0
    //     0xa21718: stur            x0, [x2, #0x13]
    // 0xa2171c: r3 = Instance_FlexFit
    //     0xa2171c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa21720: ldr             x3, [x3, #0xe08]
    // 0xa21724: StoreField: r2->field_1b = r3
    //     0xa21724: stur            w3, [x2, #0x1b]
    // 0xa21728: ldur            x1, [fp, #-0x30]
    // 0xa2172c: StoreField: r2->field_b = r1
    //     0xa2172c: stur            w1, [x2, #0xb]
    // 0xa21730: mov             x1, x2
    // 0xa21734: r0 = _NativeScene._()
    //     0xa21734: bl              #0x16ed860  ; [dart:ui] _NativeScene::_NativeScene._
    // 0xa21738: ldur            x1, [fp, #-0x10]
    // 0xa2173c: ldur            x0, [fp, #-0x28]
    // 0xa21740: ldur            x2, [fp, #-8]
    // 0xa21744: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa21744: add             x25, x1, x2, lsl #2
    //     0xa21748: add             x25, x25, #0xf
    //     0xa2174c: str             w0, [x25]
    //     0xa21750: tbz             w0, #0, #0xa2176c
    //     0xa21754: ldurb           w16, [x1, #-1]
    //     0xa21758: ldurb           w17, [x0, #-1]
    //     0xa2175c: and             x16, x17, x16, lsr #2
    //     0xa21760: tst             x16, HEAP, lsr #32
    //     0xa21764: b.eq            #0xa2176c
    //     0xa21768: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa2176c: add             x4, x2, #1
    // 0xa21770: ldur            x0, [fp, #-0x20]
    // 0xa21774: ldur            x3, [fp, #-0x10]
    // 0xa21778: ldur            x2, [fp, #-0x18]
    // 0xa2177c: b               #0xa21678
    // 0xa21780: r0 = Row()
    //     0xa21780: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa21784: r1 = Instance_Axis
    //     0xa21784: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa21788: StoreField: r0->field_f = r1
    //     0xa21788: stur            w1, [x0, #0xf]
    // 0xa2178c: r1 = Instance_MainAxisAlignment
    //     0xa2178c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa21790: ldr             x1, [x1, #0xa08]
    // 0xa21794: StoreField: r0->field_13 = r1
    //     0xa21794: stur            w1, [x0, #0x13]
    // 0xa21798: r1 = Instance_MainAxisSize
    //     0xa21798: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa2179c: ldr             x1, [x1, #0xa10]
    // 0xa217a0: ArrayStore: r0[0] = r1  ; List_4
    //     0xa217a0: stur            w1, [x0, #0x17]
    // 0xa217a4: r1 = Instance_CrossAxisAlignment
    //     0xa217a4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa217a8: ldr             x1, [x1, #0xa18]
    // 0xa217ac: StoreField: r0->field_1b = r1
    //     0xa217ac: stur            w1, [x0, #0x1b]
    // 0xa217b0: r1 = Instance_VerticalDirection
    //     0xa217b0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa217b4: ldr             x1, [x1, #0xa20]
    // 0xa217b8: StoreField: r0->field_23 = r1
    //     0xa217b8: stur            w1, [x0, #0x23]
    // 0xa217bc: r1 = Instance_Clip
    //     0xa217bc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa217c0: ldr             x1, [x1, #0x38]
    // 0xa217c4: StoreField: r0->field_2b = r1
    //     0xa217c4: stur            w1, [x0, #0x2b]
    // 0xa217c8: StoreField: r0->field_2f = rZR
    //     0xa217c8: stur            xzr, [x0, #0x2f]
    // 0xa217cc: ldur            x1, [fp, #-0x20]
    // 0xa217d0: StoreField: r0->field_b = r1
    //     0xa217d0: stur            w1, [x0, #0xb]
    // 0xa217d4: LeaveFrame
    //     0xa217d4: mov             SP, fp
    //     0xa217d8: ldp             fp, lr, [SP], #0x10
    // 0xa217dc: ret
    //     0xa217dc: ret             
    // 0xa217e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa217e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa217e4: b               #0xa2164c
    // 0xa217e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa217e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa217ec: b               #0xa21688
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa217f0, size: 0x184c
    // 0xa217f0: EnterFrame
    //     0xa217f0: stp             fp, lr, [SP, #-0x10]!
    //     0xa217f4: mov             fp, SP
    // 0xa217f8: AllocStack(0x68)
    //     0xa217f8: sub             SP, SP, #0x68
    // 0xa217fc: SetupParameters()
    //     0xa217fc: ldr             x0, [fp, #0x20]
    //     0xa21800: ldur            w1, [x0, #0x17]
    //     0xa21804: add             x1, x1, HEAP, lsl #32
    //     0xa21808: stur            x1, [fp, #-8]
    // 0xa2180c: CheckStackOverflow
    //     0xa2180c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa21810: cmp             SP, x16
    //     0xa21814: b.ls            #0xa22fe8
    // 0xa21818: r1 = 1
    //     0xa21818: movz            x1, #0x1
    // 0xa2181c: r0 = AllocateContext()
    //     0xa2181c: bl              #0x16f6108  ; AllocateContextStub
    // 0xa21820: mov             x3, x0
    // 0xa21824: ldur            x2, [fp, #-8]
    // 0xa21828: stur            x3, [fp, #-0x18]
    // 0xa2182c: StoreField: r3->field_b = r2
    //     0xa2182c: stur            w2, [x3, #0xb]
    // 0xa21830: ldr             x0, [fp, #0x10]
    // 0xa21834: StoreField: r3->field_f = r0
    //     0xa21834: stur            w0, [x3, #0xf]
    // 0xa21838: LoadField: r1 = r2->field_f
    //     0xa21838: ldur            w1, [x2, #0xf]
    // 0xa2183c: DecompressPointer r1
    //     0xa2183c: add             x1, x1, HEAP, lsl #32
    // 0xa21840: LoadField: r4 = r1->field_b
    //     0xa21840: ldur            w4, [x1, #0xb]
    // 0xa21844: DecompressPointer r4
    //     0xa21844: add             x4, x4, HEAP, lsl #32
    // 0xa21848: cmp             w4, NULL
    // 0xa2184c: b.eq            #0xa22ff0
    // 0xa21850: LoadField: r1 = r4->field_b
    //     0xa21850: ldur            w1, [x4, #0xb]
    // 0xa21854: DecompressPointer r1
    //     0xa21854: add             x1, x1, HEAP, lsl #32
    // 0xa21858: LoadField: r4 = r1->field_b
    //     0xa21858: ldur            w4, [x1, #0xb]
    // 0xa2185c: DecompressPointer r4
    //     0xa2185c: add             x4, x4, HEAP, lsl #32
    // 0xa21860: cmp             w4, NULL
    // 0xa21864: b.ne            #0xa21870
    // 0xa21868: r0 = Null
    //     0xa21868: mov             x0, NULL
    // 0xa2186c: b               #0xa218dc
    // 0xa21870: LoadField: r1 = r4->field_1f
    //     0xa21870: ldur            w1, [x4, #0x1f]
    // 0xa21874: DecompressPointer r1
    //     0xa21874: add             x1, x1, HEAP, lsl #32
    // 0xa21878: cmp             w1, NULL
    // 0xa2187c: b.ne            #0xa21888
    // 0xa21880: r0 = Null
    //     0xa21880: mov             x0, NULL
    // 0xa21884: b               #0xa218dc
    // 0xa21888: LoadField: r4 = r1->field_7
    //     0xa21888: ldur            w4, [x1, #7]
    // 0xa2188c: DecompressPointer r4
    //     0xa2188c: add             x4, x4, HEAP, lsl #32
    // 0xa21890: cmp             w4, NULL
    // 0xa21894: b.ne            #0xa218a0
    // 0xa21898: r0 = Null
    //     0xa21898: mov             x0, NULL
    // 0xa2189c: b               #0xa218dc
    // 0xa218a0: LoadField: r1 = r4->field_b
    //     0xa218a0: ldur            w1, [x4, #0xb]
    // 0xa218a4: r5 = LoadInt32Instr(r0)
    //     0xa218a4: sbfx            x5, x0, #1, #0x1f
    //     0xa218a8: tbz             w0, #0, #0xa218b0
    //     0xa218ac: ldur            x5, [x0, #7]
    // 0xa218b0: r0 = LoadInt32Instr(r1)
    //     0xa218b0: sbfx            x0, x1, #1, #0x1f
    // 0xa218b4: mov             x1, x5
    // 0xa218b8: cmp             x1, x0
    // 0xa218bc: b.hs            #0xa22ff4
    // 0xa218c0: LoadField: r0 = r4->field_f
    //     0xa218c0: ldur            w0, [x4, #0xf]
    // 0xa218c4: DecompressPointer r0
    //     0xa218c4: add             x0, x0, HEAP, lsl #32
    // 0xa218c8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa218c8: add             x16, x0, x5, lsl #2
    //     0xa218cc: ldur            w1, [x16, #0xf]
    // 0xa218d0: DecompressPointer r1
    //     0xa218d0: add             x1, x1, HEAP, lsl #32
    // 0xa218d4: LoadField: r0 = r1->field_b
    //     0xa218d4: ldur            w0, [x1, #0xb]
    // 0xa218d8: DecompressPointer r0
    //     0xa218d8: add             x0, x0, HEAP, lsl #32
    // 0xa218dc: cmp             w0, NULL
    // 0xa218e0: b.ne            #0xa218e8
    // 0xa218e4: r0 = ""
    //     0xa218e4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa218e8: ldr             x1, [fp, #0x18]
    // 0xa218ec: stur            x0, [fp, #-0x10]
    // 0xa218f0: r0 = of()
    //     0xa218f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa218f4: LoadField: r1 = r0->field_87
    //     0xa218f4: ldur            w1, [x0, #0x87]
    // 0xa218f8: DecompressPointer r1
    //     0xa218f8: add             x1, x1, HEAP, lsl #32
    // 0xa218fc: LoadField: r0 = r1->field_7
    //     0xa218fc: ldur            w0, [x1, #7]
    // 0xa21900: DecompressPointer r0
    //     0xa21900: add             x0, x0, HEAP, lsl #32
    // 0xa21904: stur            x0, [fp, #-0x20]
    // 0xa21908: r1 = Instance_Color
    //     0xa21908: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa2190c: d0 = 0.700000
    //     0xa2190c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa21910: ldr             d0, [x17, #0xf48]
    // 0xa21914: r0 = withOpacity()
    //     0xa21914: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa21918: r16 = 14.000000
    //     0xa21918: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa2191c: ldr             x16, [x16, #0x1d8]
    // 0xa21920: stp             x0, x16, [SP]
    // 0xa21924: ldur            x1, [fp, #-0x20]
    // 0xa21928: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa21928: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa2192c: ldr             x4, [x4, #0xaa0]
    // 0xa21930: r0 = copyWith()
    //     0xa21930: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa21934: stur            x0, [fp, #-0x20]
    // 0xa21938: r0 = Text()
    //     0xa21938: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa2193c: mov             x2, x0
    // 0xa21940: ldur            x0, [fp, #-0x10]
    // 0xa21944: stur            x2, [fp, #-0x28]
    // 0xa21948: StoreField: r2->field_b = r0
    //     0xa21948: stur            w0, [x2, #0xb]
    // 0xa2194c: ldur            x0, [fp, #-0x20]
    // 0xa21950: StoreField: r2->field_13 = r0
    //     0xa21950: stur            w0, [x2, #0x13]
    // 0xa21954: ldur            x3, [fp, #-8]
    // 0xa21958: LoadField: r0 = r3->field_f
    //     0xa21958: ldur            w0, [x3, #0xf]
    // 0xa2195c: DecompressPointer r0
    //     0xa2195c: add             x0, x0, HEAP, lsl #32
    // 0xa21960: LoadField: r1 = r0->field_b
    //     0xa21960: ldur            w1, [x0, #0xb]
    // 0xa21964: DecompressPointer r1
    //     0xa21964: add             x1, x1, HEAP, lsl #32
    // 0xa21968: cmp             w1, NULL
    // 0xa2196c: b.eq            #0xa22ff8
    // 0xa21970: LoadField: r0 = r1->field_b
    //     0xa21970: ldur            w0, [x1, #0xb]
    // 0xa21974: DecompressPointer r0
    //     0xa21974: add             x0, x0, HEAP, lsl #32
    // 0xa21978: LoadField: r1 = r0->field_b
    //     0xa21978: ldur            w1, [x0, #0xb]
    // 0xa2197c: DecompressPointer r1
    //     0xa2197c: add             x1, x1, HEAP, lsl #32
    // 0xa21980: cmp             w1, NULL
    // 0xa21984: b.ne            #0xa21994
    // 0xa21988: ldur            x5, [fp, #-0x18]
    // 0xa2198c: r0 = Null
    //     0xa2198c: mov             x0, NULL
    // 0xa21990: b               #0xa21a14
    // 0xa21994: LoadField: r0 = r1->field_1f
    //     0xa21994: ldur            w0, [x1, #0x1f]
    // 0xa21998: DecompressPointer r0
    //     0xa21998: add             x0, x0, HEAP, lsl #32
    // 0xa2199c: cmp             w0, NULL
    // 0xa219a0: b.ne            #0xa219b0
    // 0xa219a4: ldur            x5, [fp, #-0x18]
    // 0xa219a8: r0 = Null
    //     0xa219a8: mov             x0, NULL
    // 0xa219ac: b               #0xa21a14
    // 0xa219b0: LoadField: r4 = r0->field_7
    //     0xa219b0: ldur            w4, [x0, #7]
    // 0xa219b4: DecompressPointer r4
    //     0xa219b4: add             x4, x4, HEAP, lsl #32
    // 0xa219b8: cmp             w4, NULL
    // 0xa219bc: b.ne            #0xa219cc
    // 0xa219c0: ldur            x5, [fp, #-0x18]
    // 0xa219c4: r0 = Null
    //     0xa219c4: mov             x0, NULL
    // 0xa219c8: b               #0xa21a14
    // 0xa219cc: ldur            x5, [fp, #-0x18]
    // 0xa219d0: LoadField: r0 = r5->field_f
    //     0xa219d0: ldur            w0, [x5, #0xf]
    // 0xa219d4: DecompressPointer r0
    //     0xa219d4: add             x0, x0, HEAP, lsl #32
    // 0xa219d8: LoadField: r1 = r4->field_b
    //     0xa219d8: ldur            w1, [x4, #0xb]
    // 0xa219dc: r6 = LoadInt32Instr(r0)
    //     0xa219dc: sbfx            x6, x0, #1, #0x1f
    //     0xa219e0: tbz             w0, #0, #0xa219e8
    //     0xa219e4: ldur            x6, [x0, #7]
    // 0xa219e8: r0 = LoadInt32Instr(r1)
    //     0xa219e8: sbfx            x0, x1, #1, #0x1f
    // 0xa219ec: mov             x1, x6
    // 0xa219f0: cmp             x1, x0
    // 0xa219f4: b.hs            #0xa22ffc
    // 0xa219f8: LoadField: r0 = r4->field_f
    //     0xa219f8: ldur            w0, [x4, #0xf]
    // 0xa219fc: DecompressPointer r0
    //     0xa219fc: add             x0, x0, HEAP, lsl #32
    // 0xa21a00: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa21a00: add             x16, x0, x6, lsl #2
    //     0xa21a04: ldur            w1, [x16, #0xf]
    // 0xa21a08: DecompressPointer r1
    //     0xa21a08: add             x1, x1, HEAP, lsl #32
    // 0xa21a0c: LoadField: r0 = r1->field_7
    //     0xa21a0c: ldur            w0, [x1, #7]
    // 0xa21a10: DecompressPointer r0
    //     0xa21a10: add             x0, x0, HEAP, lsl #32
    // 0xa21a14: r1 = LoadClassIdInstr(r0)
    //     0xa21a14: ldur            x1, [x0, #-1]
    //     0xa21a18: ubfx            x1, x1, #0xc, #0x14
    // 0xa21a1c: r16 = "partial-cod"
    //     0xa21a1c: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c830] "partial-cod"
    //     0xa21a20: ldr             x16, [x16, #0x830]
    // 0xa21a24: stp             x16, x0, [SP]
    // 0xa21a28: mov             x0, x1
    // 0xa21a2c: mov             lr, x0
    // 0xa21a30: ldr             lr, [x21, lr, lsl #3]
    // 0xa21a34: blr             lr
    // 0xa21a38: r1 = Instance_Color
    //     0xa21a38: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb18] Obj!Color@d6ad71
    //     0xa21a3c: ldr             x1, [x1, #0xb18]
    // 0xa21a40: d0 = 0.080000
    //     0xa21a40: add             x17, PP, #0x27, lsl #12  ; [pp+0x27798] IMM: double(0.08) from 0x3fb47ae147ae147b
    //     0xa21a44: ldr             d0, [x17, #0x798]
    // 0xa21a48: stur            x0, [fp, #-0x10]
    // 0xa21a4c: r0 = withOpacity()
    //     0xa21a4c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa21a50: stur            x0, [fp, #-0x20]
    // 0xa21a54: r0 = BoxDecoration()
    //     0xa21a54: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa21a58: mov             x2, x0
    // 0xa21a5c: ldur            x0, [fp, #-0x20]
    // 0xa21a60: stur            x2, [fp, #-0x30]
    // 0xa21a64: StoreField: r2->field_7 = r0
    //     0xa21a64: stur            w0, [x2, #7]
    // 0xa21a68: r0 = Instance_BorderRadius
    //     0xa21a68: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be8] Obj!BorderRadius@d5a2c1
    //     0xa21a6c: ldr             x0, [x0, #0xbe8]
    // 0xa21a70: StoreField: r2->field_13 = r0
    //     0xa21a70: stur            w0, [x2, #0x13]
    // 0xa21a74: r0 = Instance_BoxShape
    //     0xa21a74: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa21a78: ldr             x0, [x0, #0x80]
    // 0xa21a7c: StoreField: r2->field_23 = r0
    //     0xa21a7c: stur            w0, [x2, #0x23]
    // 0xa21a80: ldr             x1, [fp, #0x18]
    // 0xa21a84: r0 = of()
    //     0xa21a84: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa21a88: LoadField: r1 = r0->field_87
    //     0xa21a88: ldur            w1, [x0, #0x87]
    // 0xa21a8c: DecompressPointer r1
    //     0xa21a8c: add             x1, x1, HEAP, lsl #32
    // 0xa21a90: LoadField: r0 = r1->field_7
    //     0xa21a90: ldur            w0, [x1, #7]
    // 0xa21a94: DecompressPointer r0
    //     0xa21a94: add             x0, x0, HEAP, lsl #32
    // 0xa21a98: r16 = 12.000000
    //     0xa21a98: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa21a9c: ldr             x16, [x16, #0x9e8]
    // 0xa21aa0: r30 = Instance_Color
    //     0xa21aa0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa21aa4: ldr             lr, [lr, #0x858]
    // 0xa21aa8: stp             lr, x16, [SP]
    // 0xa21aac: mov             x1, x0
    // 0xa21ab0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa21ab0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa21ab4: ldr             x4, [x4, #0xaa0]
    // 0xa21ab8: r0 = copyWith()
    //     0xa21ab8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa21abc: stur            x0, [fp, #-0x20]
    // 0xa21ac0: r0 = Text()
    //     0xa21ac0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa21ac4: mov             x1, x0
    // 0xa21ac8: r0 = "New"
    //     0xa21ac8: add             x0, PP, #0x54, lsl #12  ; [pp+0x544d0] "New"
    //     0xa21acc: ldr             x0, [x0, #0x4d0]
    // 0xa21ad0: stur            x1, [fp, #-0x38]
    // 0xa21ad4: StoreField: r1->field_b = r0
    //     0xa21ad4: stur            w0, [x1, #0xb]
    // 0xa21ad8: ldur            x0, [fp, #-0x20]
    // 0xa21adc: StoreField: r1->field_13 = r0
    //     0xa21adc: stur            w0, [x1, #0x13]
    // 0xa21ae0: r0 = Padding()
    //     0xa21ae0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa21ae4: mov             x1, x0
    // 0xa21ae8: r0 = Instance_EdgeInsets
    //     0xa21ae8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38db0] Obj!EdgeInsets@d58101
    //     0xa21aec: ldr             x0, [x0, #0xdb0]
    // 0xa21af0: stur            x1, [fp, #-0x20]
    // 0xa21af4: StoreField: r1->field_f = r0
    //     0xa21af4: stur            w0, [x1, #0xf]
    // 0xa21af8: ldur            x2, [fp, #-0x38]
    // 0xa21afc: StoreField: r1->field_b = r2
    //     0xa21afc: stur            w2, [x1, #0xb]
    // 0xa21b00: r0 = Container()
    //     0xa21b00: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa21b04: stur            x0, [fp, #-0x38]
    // 0xa21b08: ldur            x16, [fp, #-0x30]
    // 0xa21b0c: ldur            lr, [fp, #-0x20]
    // 0xa21b10: stp             lr, x16, [SP]
    // 0xa21b14: mov             x1, x0
    // 0xa21b18: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xa21b18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xa21b1c: ldr             x4, [x4, #0x88]
    // 0xa21b20: r0 = Container()
    //     0xa21b20: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa21b24: r0 = Padding()
    //     0xa21b24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa21b28: mov             x1, x0
    // 0xa21b2c: r0 = Instance_EdgeInsets
    //     0xa21b2c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xa21b30: ldr             x0, [x0, #0xa78]
    // 0xa21b34: stur            x1, [fp, #-0x20]
    // 0xa21b38: StoreField: r1->field_f = r0
    //     0xa21b38: stur            w0, [x1, #0xf]
    // 0xa21b3c: ldur            x0, [fp, #-0x38]
    // 0xa21b40: StoreField: r1->field_b = r0
    //     0xa21b40: stur            w0, [x1, #0xb]
    // 0xa21b44: r0 = Visibility()
    //     0xa21b44: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa21b48: mov             x2, x0
    // 0xa21b4c: ldur            x0, [fp, #-0x20]
    // 0xa21b50: stur            x2, [fp, #-0x30]
    // 0xa21b54: StoreField: r2->field_b = r0
    //     0xa21b54: stur            w0, [x2, #0xb]
    // 0xa21b58: r3 = Instance_SizedBox
    //     0xa21b58: ldr             x3, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa21b5c: StoreField: r2->field_f = r3
    //     0xa21b5c: stur            w3, [x2, #0xf]
    // 0xa21b60: ldur            x0, [fp, #-0x10]
    // 0xa21b64: StoreField: r2->field_13 = r0
    //     0xa21b64: stur            w0, [x2, #0x13]
    // 0xa21b68: r4 = false
    //     0xa21b68: add             x4, NULL, #0x30  ; false
    // 0xa21b6c: ArrayStore: r2[0] = r4  ; List_4
    //     0xa21b6c: stur            w4, [x2, #0x17]
    // 0xa21b70: StoreField: r2->field_1b = r4
    //     0xa21b70: stur            w4, [x2, #0x1b]
    // 0xa21b74: StoreField: r2->field_1f = r4
    //     0xa21b74: stur            w4, [x2, #0x1f]
    // 0xa21b78: StoreField: r2->field_23 = r4
    //     0xa21b78: stur            w4, [x2, #0x23]
    // 0xa21b7c: StoreField: r2->field_27 = r4
    //     0xa21b7c: stur            w4, [x2, #0x27]
    // 0xa21b80: StoreField: r2->field_2b = r4
    //     0xa21b80: stur            w4, [x2, #0x2b]
    // 0xa21b84: ldur            x5, [fp, #-8]
    // 0xa21b88: LoadField: r0 = r5->field_f
    //     0xa21b88: ldur            w0, [x5, #0xf]
    // 0xa21b8c: DecompressPointer r0
    //     0xa21b8c: add             x0, x0, HEAP, lsl #32
    // 0xa21b90: LoadField: r6 = r0->field_b
    //     0xa21b90: ldur            w6, [x0, #0xb]
    // 0xa21b94: DecompressPointer r6
    //     0xa21b94: add             x6, x6, HEAP, lsl #32
    // 0xa21b98: cmp             w6, NULL
    // 0xa21b9c: b.eq            #0xa23000
    // 0xa21ba0: LoadField: r0 = r6->field_b
    //     0xa21ba0: ldur            w0, [x6, #0xb]
    // 0xa21ba4: DecompressPointer r0
    //     0xa21ba4: add             x0, x0, HEAP, lsl #32
    // 0xa21ba8: LoadField: r1 = r0->field_b
    //     0xa21ba8: ldur            w1, [x0, #0xb]
    // 0xa21bac: DecompressPointer r1
    //     0xa21bac: add             x1, x1, HEAP, lsl #32
    // 0xa21bb0: cmp             w1, NULL
    // 0xa21bb4: b.ne            #0xa21bc4
    // 0xa21bb8: ldur            x8, [fp, #-0x18]
    // 0xa21bbc: r0 = Null
    //     0xa21bbc: mov             x0, NULL
    // 0xa21bc0: b               #0xa21c68
    // 0xa21bc4: LoadField: r0 = r1->field_1f
    //     0xa21bc4: ldur            w0, [x1, #0x1f]
    // 0xa21bc8: DecompressPointer r0
    //     0xa21bc8: add             x0, x0, HEAP, lsl #32
    // 0xa21bcc: cmp             w0, NULL
    // 0xa21bd0: b.ne            #0xa21be0
    // 0xa21bd4: ldur            x8, [fp, #-0x18]
    // 0xa21bd8: r0 = Null
    //     0xa21bd8: mov             x0, NULL
    // 0xa21bdc: b               #0xa21c68
    // 0xa21be0: LoadField: r7 = r0->field_7
    //     0xa21be0: ldur            w7, [x0, #7]
    // 0xa21be4: DecompressPointer r7
    //     0xa21be4: add             x7, x7, HEAP, lsl #32
    // 0xa21be8: cmp             w7, NULL
    // 0xa21bec: b.ne            #0xa21bfc
    // 0xa21bf0: ldur            x8, [fp, #-0x18]
    // 0xa21bf4: r0 = Null
    //     0xa21bf4: mov             x0, NULL
    // 0xa21bf8: b               #0xa21c68
    // 0xa21bfc: ldur            x8, [fp, #-0x18]
    // 0xa21c00: LoadField: r0 = r8->field_f
    //     0xa21c00: ldur            w0, [x8, #0xf]
    // 0xa21c04: DecompressPointer r0
    //     0xa21c04: add             x0, x0, HEAP, lsl #32
    // 0xa21c08: LoadField: r1 = r7->field_b
    //     0xa21c08: ldur            w1, [x7, #0xb]
    // 0xa21c0c: r9 = LoadInt32Instr(r0)
    //     0xa21c0c: sbfx            x9, x0, #1, #0x1f
    //     0xa21c10: tbz             w0, #0, #0xa21c18
    //     0xa21c14: ldur            x9, [x0, #7]
    // 0xa21c18: r0 = LoadInt32Instr(r1)
    //     0xa21c18: sbfx            x0, x1, #1, #0x1f
    // 0xa21c1c: mov             x1, x9
    // 0xa21c20: cmp             x1, x0
    // 0xa21c24: b.hs            #0xa23004
    // 0xa21c28: LoadField: r0 = r7->field_f
    //     0xa21c28: ldur            w0, [x7, #0xf]
    // 0xa21c2c: DecompressPointer r0
    //     0xa21c2c: add             x0, x0, HEAP, lsl #32
    // 0xa21c30: ArrayLoad: r1 = r0[r9]  ; Unknown_4
    //     0xa21c30: add             x16, x0, x9, lsl #2
    //     0xa21c34: ldur            w1, [x16, #0xf]
    // 0xa21c38: DecompressPointer r1
    //     0xa21c38: add             x1, x1, HEAP, lsl #32
    // 0xa21c3c: LoadField: r0 = r1->field_f
    //     0xa21c3c: ldur            w0, [x1, #0xf]
    // 0xa21c40: DecompressPointer r0
    //     0xa21c40: add             x0, x0, HEAP, lsl #32
    // 0xa21c44: cmp             w0, NULL
    // 0xa21c48: b.ne            #0xa21c54
    // 0xa21c4c: r0 = Null
    //     0xa21c4c: mov             x0, NULL
    // 0xa21c50: b               #0xa21c68
    // 0xa21c54: LoadField: r1 = r0->field_7
    //     0xa21c54: ldur            w1, [x0, #7]
    // 0xa21c58: cbnz            w1, #0xa21c64
    // 0xa21c5c: r0 = false
    //     0xa21c5c: add             x0, NULL, #0x30  ; false
    // 0xa21c60: b               #0xa21c68
    // 0xa21c64: r0 = true
    //     0xa21c64: add             x0, NULL, #0x20  ; true
    // 0xa21c68: cmp             w0, NULL
    // 0xa21c6c: b.ne            #0xa21c78
    // 0xa21c70: r7 = false
    //     0xa21c70: add             x7, NULL, #0x30  ; false
    // 0xa21c74: b               #0xa21c7c
    // 0xa21c78: mov             x7, x0
    // 0xa21c7c: stur            x7, [fp, #-0x20]
    // 0xa21c80: LoadField: r0 = r6->field_b
    //     0xa21c80: ldur            w0, [x6, #0xb]
    // 0xa21c84: DecompressPointer r0
    //     0xa21c84: add             x0, x0, HEAP, lsl #32
    // 0xa21c88: LoadField: r1 = r0->field_b
    //     0xa21c88: ldur            w1, [x0, #0xb]
    // 0xa21c8c: DecompressPointer r1
    //     0xa21c8c: add             x1, x1, HEAP, lsl #32
    // 0xa21c90: cmp             w1, NULL
    // 0xa21c94: b.ne            #0xa21ca0
    // 0xa21c98: r0 = Null
    //     0xa21c98: mov             x0, NULL
    // 0xa21c9c: b               #0xa21d14
    // 0xa21ca0: LoadField: r0 = r1->field_1f
    //     0xa21ca0: ldur            w0, [x1, #0x1f]
    // 0xa21ca4: DecompressPointer r0
    //     0xa21ca4: add             x0, x0, HEAP, lsl #32
    // 0xa21ca8: cmp             w0, NULL
    // 0xa21cac: b.ne            #0xa21cb8
    // 0xa21cb0: r0 = Null
    //     0xa21cb0: mov             x0, NULL
    // 0xa21cb4: b               #0xa21d14
    // 0xa21cb8: LoadField: r6 = r0->field_7
    //     0xa21cb8: ldur            w6, [x0, #7]
    // 0xa21cbc: DecompressPointer r6
    //     0xa21cbc: add             x6, x6, HEAP, lsl #32
    // 0xa21cc0: cmp             w6, NULL
    // 0xa21cc4: b.ne            #0xa21cd0
    // 0xa21cc8: r0 = Null
    //     0xa21cc8: mov             x0, NULL
    // 0xa21ccc: b               #0xa21d14
    // 0xa21cd0: LoadField: r0 = r8->field_f
    //     0xa21cd0: ldur            w0, [x8, #0xf]
    // 0xa21cd4: DecompressPointer r0
    //     0xa21cd4: add             x0, x0, HEAP, lsl #32
    // 0xa21cd8: LoadField: r1 = r6->field_b
    //     0xa21cd8: ldur            w1, [x6, #0xb]
    // 0xa21cdc: r9 = LoadInt32Instr(r0)
    //     0xa21cdc: sbfx            x9, x0, #1, #0x1f
    //     0xa21ce0: tbz             w0, #0, #0xa21ce8
    //     0xa21ce4: ldur            x9, [x0, #7]
    // 0xa21ce8: r0 = LoadInt32Instr(r1)
    //     0xa21ce8: sbfx            x0, x1, #1, #0x1f
    // 0xa21cec: mov             x1, x9
    // 0xa21cf0: cmp             x1, x0
    // 0xa21cf4: b.hs            #0xa23008
    // 0xa21cf8: LoadField: r0 = r6->field_f
    //     0xa21cf8: ldur            w0, [x6, #0xf]
    // 0xa21cfc: DecompressPointer r0
    //     0xa21cfc: add             x0, x0, HEAP, lsl #32
    // 0xa21d00: ArrayLoad: r1 = r0[r9]  ; Unknown_4
    //     0xa21d00: add             x16, x0, x9, lsl #2
    //     0xa21d04: ldur            w1, [x16, #0xf]
    // 0xa21d08: DecompressPointer r1
    //     0xa21d08: add             x1, x1, HEAP, lsl #32
    // 0xa21d0c: LoadField: r0 = r1->field_f
    //     0xa21d0c: ldur            w0, [x1, #0xf]
    // 0xa21d10: DecompressPointer r0
    //     0xa21d10: add             x0, x0, HEAP, lsl #32
    // 0xa21d14: cmp             w0, NULL
    // 0xa21d18: b.ne            #0xa21d20
    // 0xa21d1c: r0 = ""
    //     0xa21d1c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa21d20: ldr             x1, [fp, #0x18]
    // 0xa21d24: stur            x0, [fp, #-0x10]
    // 0xa21d28: r0 = of()
    //     0xa21d28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa21d2c: LoadField: r1 = r0->field_87
    //     0xa21d2c: ldur            w1, [x0, #0x87]
    // 0xa21d30: DecompressPointer r1
    //     0xa21d30: add             x1, x1, HEAP, lsl #32
    // 0xa21d34: LoadField: r2 = r1->field_7
    //     0xa21d34: ldur            w2, [x1, #7]
    // 0xa21d38: DecompressPointer r2
    //     0xa21d38: add             x2, x2, HEAP, lsl #32
    // 0xa21d3c: ldur            x3, [fp, #-8]
    // 0xa21d40: stur            x2, [fp, #-0x38]
    // 0xa21d44: LoadField: r0 = r3->field_f
    //     0xa21d44: ldur            w0, [x3, #0xf]
    // 0xa21d48: DecompressPointer r0
    //     0xa21d48: add             x0, x0, HEAP, lsl #32
    // 0xa21d4c: LoadField: r1 = r0->field_b
    //     0xa21d4c: ldur            w1, [x0, #0xb]
    // 0xa21d50: DecompressPointer r1
    //     0xa21d50: add             x1, x1, HEAP, lsl #32
    // 0xa21d54: cmp             w1, NULL
    // 0xa21d58: b.eq            #0xa2300c
    // 0xa21d5c: LoadField: r0 = r1->field_b
    //     0xa21d5c: ldur            w0, [x1, #0xb]
    // 0xa21d60: DecompressPointer r0
    //     0xa21d60: add             x0, x0, HEAP, lsl #32
    // 0xa21d64: LoadField: r1 = r0->field_b
    //     0xa21d64: ldur            w1, [x0, #0xb]
    // 0xa21d68: DecompressPointer r1
    //     0xa21d68: add             x1, x1, HEAP, lsl #32
    // 0xa21d6c: cmp             w1, NULL
    // 0xa21d70: b.ne            #0xa21d80
    // 0xa21d74: ldur            x5, [fp, #-0x18]
    // 0xa21d78: r0 = Null
    //     0xa21d78: mov             x0, NULL
    // 0xa21d7c: b               #0xa21e00
    // 0xa21d80: LoadField: r0 = r1->field_1f
    //     0xa21d80: ldur            w0, [x1, #0x1f]
    // 0xa21d84: DecompressPointer r0
    //     0xa21d84: add             x0, x0, HEAP, lsl #32
    // 0xa21d88: cmp             w0, NULL
    // 0xa21d8c: b.ne            #0xa21d9c
    // 0xa21d90: ldur            x5, [fp, #-0x18]
    // 0xa21d94: r0 = Null
    //     0xa21d94: mov             x0, NULL
    // 0xa21d98: b               #0xa21e00
    // 0xa21d9c: LoadField: r4 = r0->field_7
    //     0xa21d9c: ldur            w4, [x0, #7]
    // 0xa21da0: DecompressPointer r4
    //     0xa21da0: add             x4, x4, HEAP, lsl #32
    // 0xa21da4: cmp             w4, NULL
    // 0xa21da8: b.ne            #0xa21db8
    // 0xa21dac: ldur            x5, [fp, #-0x18]
    // 0xa21db0: r0 = Null
    //     0xa21db0: mov             x0, NULL
    // 0xa21db4: b               #0xa21e00
    // 0xa21db8: ldur            x5, [fp, #-0x18]
    // 0xa21dbc: LoadField: r0 = r5->field_f
    //     0xa21dbc: ldur            w0, [x5, #0xf]
    // 0xa21dc0: DecompressPointer r0
    //     0xa21dc0: add             x0, x0, HEAP, lsl #32
    // 0xa21dc4: LoadField: r1 = r4->field_b
    //     0xa21dc4: ldur            w1, [x4, #0xb]
    // 0xa21dc8: r6 = LoadInt32Instr(r0)
    //     0xa21dc8: sbfx            x6, x0, #1, #0x1f
    //     0xa21dcc: tbz             w0, #0, #0xa21dd4
    //     0xa21dd0: ldur            x6, [x0, #7]
    // 0xa21dd4: r0 = LoadInt32Instr(r1)
    //     0xa21dd4: sbfx            x0, x1, #1, #0x1f
    // 0xa21dd8: mov             x1, x6
    // 0xa21ddc: cmp             x1, x0
    // 0xa21de0: b.hs            #0xa23010
    // 0xa21de4: LoadField: r0 = r4->field_f
    //     0xa21de4: ldur            w0, [x4, #0xf]
    // 0xa21de8: DecompressPointer r0
    //     0xa21de8: add             x0, x0, HEAP, lsl #32
    // 0xa21dec: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa21dec: add             x16, x0, x6, lsl #2
    //     0xa21df0: ldur            w1, [x16, #0xf]
    // 0xa21df4: DecompressPointer r1
    //     0xa21df4: add             x1, x1, HEAP, lsl #32
    // 0xa21df8: LoadField: r0 = r1->field_7
    //     0xa21df8: ldur            w0, [x1, #7]
    // 0xa21dfc: DecompressPointer r0
    //     0xa21dfc: add             x0, x0, HEAP, lsl #32
    // 0xa21e00: cmp             w0, NULL
    // 0xa21e04: b.ne            #0xa21e0c
    // 0xa21e08: r0 = ""
    //     0xa21e08: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa21e0c: r1 = LoadClassIdInstr(r0)
    //     0xa21e0c: ldur            x1, [x0, #-1]
    //     0xa21e10: ubfx            x1, x1, #0xc, #0x14
    // 0xa21e14: r16 = "cod"
    //     0xa21e14: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a28] "cod"
    //     0xa21e18: ldr             x16, [x16, #0xa28]
    // 0xa21e1c: stp             x16, x0, [SP]
    // 0xa21e20: mov             x0, x1
    // 0xa21e24: mov             lr, x0
    // 0xa21e28: ldr             lr, [x21, lr, lsl #3]
    // 0xa21e2c: blr             lr
    // 0xa21e30: tbnz            w0, #4, #0xa21e40
    // 0xa21e34: r1 = Instance_Color
    //     0xa21e34: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xa21e38: ldr             x1, [x1, #0x50]
    // 0xa21e3c: b               #0xa21e48
    // 0xa21e40: r1 = Instance_Color
    //     0xa21e40: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa21e44: ldr             x1, [x1, #0x858]
    // 0xa21e48: ldur            x0, [fp, #-8]
    // 0xa21e4c: ldur            x5, [fp, #-0x28]
    // 0xa21e50: ldur            x2, [fp, #-0x30]
    // 0xa21e54: ldur            x3, [fp, #-0x20]
    // 0xa21e58: ldur            x4, [fp, #-0x10]
    // 0xa21e5c: r16 = 12.000000
    //     0xa21e5c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa21e60: ldr             x16, [x16, #0x9e8]
    // 0xa21e64: stp             x1, x16, [SP]
    // 0xa21e68: ldur            x1, [fp, #-0x38]
    // 0xa21e6c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa21e6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa21e70: ldr             x4, [x4, #0xaa0]
    // 0xa21e74: r0 = copyWith()
    //     0xa21e74: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa21e78: stur            x0, [fp, #-0x38]
    // 0xa21e7c: r0 = Text()
    //     0xa21e7c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa21e80: mov             x1, x0
    // 0xa21e84: ldur            x0, [fp, #-0x10]
    // 0xa21e88: stur            x1, [fp, #-0x40]
    // 0xa21e8c: StoreField: r1->field_b = r0
    //     0xa21e8c: stur            w0, [x1, #0xb]
    // 0xa21e90: ldur            x0, [fp, #-0x38]
    // 0xa21e94: StoreField: r1->field_13 = r0
    //     0xa21e94: stur            w0, [x1, #0x13]
    // 0xa21e98: r0 = Padding()
    //     0xa21e98: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa21e9c: mov             x1, x0
    // 0xa21ea0: r0 = Instance_EdgeInsets
    //     0xa21ea0: add             x0, PP, #0x38, lsl #12  ; [pp+0x38db0] Obj!EdgeInsets@d58101
    //     0xa21ea4: ldr             x0, [x0, #0xdb0]
    // 0xa21ea8: stur            x1, [fp, #-0x10]
    // 0xa21eac: StoreField: r1->field_f = r0
    //     0xa21eac: stur            w0, [x1, #0xf]
    // 0xa21eb0: ldur            x0, [fp, #-0x40]
    // 0xa21eb4: StoreField: r1->field_b = r0
    //     0xa21eb4: stur            w0, [x1, #0xb]
    // 0xa21eb8: r0 = Visibility()
    //     0xa21eb8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa21ebc: mov             x3, x0
    // 0xa21ec0: ldur            x0, [fp, #-0x10]
    // 0xa21ec4: stur            x3, [fp, #-0x38]
    // 0xa21ec8: StoreField: r3->field_b = r0
    //     0xa21ec8: stur            w0, [x3, #0xb]
    // 0xa21ecc: r0 = Instance_SizedBox
    //     0xa21ecc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa21ed0: StoreField: r3->field_f = r0
    //     0xa21ed0: stur            w0, [x3, #0xf]
    // 0xa21ed4: ldur            x0, [fp, #-0x20]
    // 0xa21ed8: StoreField: r3->field_13 = r0
    //     0xa21ed8: stur            w0, [x3, #0x13]
    // 0xa21edc: r0 = false
    //     0xa21edc: add             x0, NULL, #0x30  ; false
    // 0xa21ee0: ArrayStore: r3[0] = r0  ; List_4
    //     0xa21ee0: stur            w0, [x3, #0x17]
    // 0xa21ee4: StoreField: r3->field_1b = r0
    //     0xa21ee4: stur            w0, [x3, #0x1b]
    // 0xa21ee8: StoreField: r3->field_1f = r0
    //     0xa21ee8: stur            w0, [x3, #0x1f]
    // 0xa21eec: StoreField: r3->field_23 = r0
    //     0xa21eec: stur            w0, [x3, #0x23]
    // 0xa21ef0: StoreField: r3->field_27 = r0
    //     0xa21ef0: stur            w0, [x3, #0x27]
    // 0xa21ef4: StoreField: r3->field_2b = r0
    //     0xa21ef4: stur            w0, [x3, #0x2b]
    // 0xa21ef8: r1 = Null
    //     0xa21ef8: mov             x1, NULL
    // 0xa21efc: r2 = 8
    //     0xa21efc: movz            x2, #0x8
    // 0xa21f00: r0 = AllocateArray()
    //     0xa21f00: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa21f04: mov             x2, x0
    // 0xa21f08: ldur            x0, [fp, #-0x28]
    // 0xa21f0c: stur            x2, [fp, #-0x10]
    // 0xa21f10: StoreField: r2->field_f = r0
    //     0xa21f10: stur            w0, [x2, #0xf]
    // 0xa21f14: ldur            x0, [fp, #-0x30]
    // 0xa21f18: StoreField: r2->field_13 = r0
    //     0xa21f18: stur            w0, [x2, #0x13]
    // 0xa21f1c: r16 = Instance_Spacer
    //     0xa21f1c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xa21f20: ldr             x16, [x16, #0xf0]
    // 0xa21f24: ArrayStore: r2[0] = r16  ; List_4
    //     0xa21f24: stur            w16, [x2, #0x17]
    // 0xa21f28: ldur            x0, [fp, #-0x38]
    // 0xa21f2c: StoreField: r2->field_1b = r0
    //     0xa21f2c: stur            w0, [x2, #0x1b]
    // 0xa21f30: r1 = <Widget>
    //     0xa21f30: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa21f34: r0 = AllocateGrowableArray()
    //     0xa21f34: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa21f38: mov             x1, x0
    // 0xa21f3c: ldur            x0, [fp, #-0x10]
    // 0xa21f40: stur            x1, [fp, #-0x20]
    // 0xa21f44: StoreField: r1->field_f = r0
    //     0xa21f44: stur            w0, [x1, #0xf]
    // 0xa21f48: r0 = 8
    //     0xa21f48: movz            x0, #0x8
    // 0xa21f4c: StoreField: r1->field_b = r0
    //     0xa21f4c: stur            w0, [x1, #0xb]
    // 0xa21f50: r0 = Row()
    //     0xa21f50: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa21f54: mov             x3, x0
    // 0xa21f58: r0 = Instance_Axis
    //     0xa21f58: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa21f5c: stur            x3, [fp, #-0x10]
    // 0xa21f60: StoreField: r3->field_f = r0
    //     0xa21f60: stur            w0, [x3, #0xf]
    // 0xa21f64: r4 = Instance_MainAxisAlignment
    //     0xa21f64: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa21f68: ldr             x4, [x4, #0xa08]
    // 0xa21f6c: StoreField: r3->field_13 = r4
    //     0xa21f6c: stur            w4, [x3, #0x13]
    // 0xa21f70: r5 = Instance_MainAxisSize
    //     0xa21f70: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa21f74: ldr             x5, [x5, #0xa10]
    // 0xa21f78: ArrayStore: r3[0] = r5  ; List_4
    //     0xa21f78: stur            w5, [x3, #0x17]
    // 0xa21f7c: r6 = Instance_CrossAxisAlignment
    //     0xa21f7c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa21f80: ldr             x6, [x6, #0xa18]
    // 0xa21f84: StoreField: r3->field_1b = r6
    //     0xa21f84: stur            w6, [x3, #0x1b]
    // 0xa21f88: r7 = Instance_VerticalDirection
    //     0xa21f88: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa21f8c: ldr             x7, [x7, #0xa20]
    // 0xa21f90: StoreField: r3->field_23 = r7
    //     0xa21f90: stur            w7, [x3, #0x23]
    // 0xa21f94: r8 = Instance_Clip
    //     0xa21f94: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa21f98: ldr             x8, [x8, #0x38]
    // 0xa21f9c: StoreField: r3->field_2b = r8
    //     0xa21f9c: stur            w8, [x3, #0x2b]
    // 0xa21fa0: StoreField: r3->field_2f = rZR
    //     0xa21fa0: stur            xzr, [x3, #0x2f]
    // 0xa21fa4: ldur            x1, [fp, #-0x20]
    // 0xa21fa8: StoreField: r3->field_b = r1
    //     0xa21fa8: stur            w1, [x3, #0xb]
    // 0xa21fac: r1 = Null
    //     0xa21fac: mov             x1, NULL
    // 0xa21fb0: r2 = 2
    //     0xa21fb0: movz            x2, #0x2
    // 0xa21fb4: r0 = AllocateArray()
    //     0xa21fb4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa21fb8: mov             x2, x0
    // 0xa21fbc: ldur            x0, [fp, #-0x10]
    // 0xa21fc0: stur            x2, [fp, #-0x20]
    // 0xa21fc4: StoreField: r2->field_f = r0
    //     0xa21fc4: stur            w0, [x2, #0xf]
    // 0xa21fc8: r1 = <Widget>
    //     0xa21fc8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa21fcc: r0 = AllocateGrowableArray()
    //     0xa21fcc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa21fd0: mov             x2, x0
    // 0xa21fd4: ldur            x0, [fp, #-0x20]
    // 0xa21fd8: stur            x2, [fp, #-0x10]
    // 0xa21fdc: StoreField: r2->field_f = r0
    //     0xa21fdc: stur            w0, [x2, #0xf]
    // 0xa21fe0: r0 = 2
    //     0xa21fe0: movz            x0, #0x2
    // 0xa21fe4: StoreField: r2->field_b = r0
    //     0xa21fe4: stur            w0, [x2, #0xb]
    // 0xa21fe8: ldur            x3, [fp, #-8]
    // 0xa21fec: LoadField: r0 = r3->field_f
    //     0xa21fec: ldur            w0, [x3, #0xf]
    // 0xa21ff0: DecompressPointer r0
    //     0xa21ff0: add             x0, x0, HEAP, lsl #32
    // 0xa21ff4: LoadField: r1 = r0->field_b
    //     0xa21ff4: ldur            w1, [x0, #0xb]
    // 0xa21ff8: DecompressPointer r1
    //     0xa21ff8: add             x1, x1, HEAP, lsl #32
    // 0xa21ffc: cmp             w1, NULL
    // 0xa22000: b.eq            #0xa23014
    // 0xa22004: LoadField: r0 = r1->field_b
    //     0xa22004: ldur            w0, [x1, #0xb]
    // 0xa22008: DecompressPointer r0
    //     0xa22008: add             x0, x0, HEAP, lsl #32
    // 0xa2200c: LoadField: r1 = r0->field_b
    //     0xa2200c: ldur            w1, [x0, #0xb]
    // 0xa22010: DecompressPointer r1
    //     0xa22010: add             x1, x1, HEAP, lsl #32
    // 0xa22014: cmp             w1, NULL
    // 0xa22018: b.ne            #0xa22028
    // 0xa2201c: ldur            x5, [fp, #-0x18]
    // 0xa22020: r0 = Null
    //     0xa22020: mov             x0, NULL
    // 0xa22024: b               #0xa220a8
    // 0xa22028: LoadField: r0 = r1->field_1f
    //     0xa22028: ldur            w0, [x1, #0x1f]
    // 0xa2202c: DecompressPointer r0
    //     0xa2202c: add             x0, x0, HEAP, lsl #32
    // 0xa22030: cmp             w0, NULL
    // 0xa22034: b.ne            #0xa22044
    // 0xa22038: ldur            x5, [fp, #-0x18]
    // 0xa2203c: r0 = Null
    //     0xa2203c: mov             x0, NULL
    // 0xa22040: b               #0xa220a8
    // 0xa22044: LoadField: r4 = r0->field_7
    //     0xa22044: ldur            w4, [x0, #7]
    // 0xa22048: DecompressPointer r4
    //     0xa22048: add             x4, x4, HEAP, lsl #32
    // 0xa2204c: cmp             w4, NULL
    // 0xa22050: b.ne            #0xa22060
    // 0xa22054: ldur            x5, [fp, #-0x18]
    // 0xa22058: r0 = Null
    //     0xa22058: mov             x0, NULL
    // 0xa2205c: b               #0xa220a8
    // 0xa22060: ldur            x5, [fp, #-0x18]
    // 0xa22064: LoadField: r0 = r5->field_f
    //     0xa22064: ldur            w0, [x5, #0xf]
    // 0xa22068: DecompressPointer r0
    //     0xa22068: add             x0, x0, HEAP, lsl #32
    // 0xa2206c: LoadField: r1 = r4->field_b
    //     0xa2206c: ldur            w1, [x4, #0xb]
    // 0xa22070: r6 = LoadInt32Instr(r0)
    //     0xa22070: sbfx            x6, x0, #1, #0x1f
    //     0xa22074: tbz             w0, #0, #0xa2207c
    //     0xa22078: ldur            x6, [x0, #7]
    // 0xa2207c: r0 = LoadInt32Instr(r1)
    //     0xa2207c: sbfx            x0, x1, #1, #0x1f
    // 0xa22080: mov             x1, x6
    // 0xa22084: cmp             x1, x0
    // 0xa22088: b.hs            #0xa23018
    // 0xa2208c: LoadField: r0 = r4->field_f
    //     0xa2208c: ldur            w0, [x4, #0xf]
    // 0xa22090: DecompressPointer r0
    //     0xa22090: add             x0, x0, HEAP, lsl #32
    // 0xa22094: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa22094: add             x16, x0, x6, lsl #2
    //     0xa22098: ldur            w1, [x16, #0xf]
    // 0xa2209c: DecompressPointer r1
    //     0xa2209c: add             x1, x1, HEAP, lsl #32
    // 0xa220a0: LoadField: r0 = r1->field_7
    //     0xa220a0: ldur            w0, [x1, #7]
    // 0xa220a4: DecompressPointer r0
    //     0xa220a4: add             x0, x0, HEAP, lsl #32
    // 0xa220a8: r1 = LoadClassIdInstr(r0)
    //     0xa220a8: ldur            x1, [x0, #-1]
    //     0xa220ac: ubfx            x1, x1, #0xc, #0x14
    // 0xa220b0: r16 = "online"
    //     0xa220b0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a50] "online"
    //     0xa220b4: ldr             x16, [x16, #0xa50]
    // 0xa220b8: stp             x16, x0, [SP]
    // 0xa220bc: mov             x0, x1
    // 0xa220c0: mov             lr, x0
    // 0xa220c4: ldr             lr, [x21, lr, lsl #3]
    // 0xa220c8: blr             lr
    // 0xa220cc: tbnz            w0, #4, #0xa22304
    // 0xa220d0: ldur            x0, [fp, #-8]
    // 0xa220d4: ldr             x1, [fp, #0x18]
    // 0xa220d8: r0 = of()
    //     0xa220d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa220dc: LoadField: r1 = r0->field_5b
    //     0xa220dc: ldur            w1, [x0, #0x5b]
    // 0xa220e0: DecompressPointer r1
    //     0xa220e0: add             x1, x1, HEAP, lsl #32
    // 0xa220e4: r0 = LoadClassIdInstr(r1)
    //     0xa220e4: ldur            x0, [x1, #-1]
    //     0xa220e8: ubfx            x0, x0, #0xc, #0x14
    // 0xa220ec: d0 = 0.100000
    //     0xa220ec: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xa220f0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa220f0: sub             lr, x0, #0xffa
    //     0xa220f4: ldr             lr, [x21, lr, lsl #3]
    //     0xa220f8: blr             lr
    // 0xa220fc: mov             x2, x0
    // 0xa22100: r1 = Null
    //     0xa22100: mov             x1, NULL
    // 0xa22104: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa22104: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa22108: r0 = Border.all()
    //     0xa22108: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa2210c: stur            x0, [fp, #-0x20]
    // 0xa22110: r0 = Radius()
    //     0xa22110: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa22114: d0 = 20.000000
    //     0xa22114: fmov            d0, #20.00000000
    // 0xa22118: stur            x0, [fp, #-0x28]
    // 0xa2211c: StoreField: r0->field_7 = d0
    //     0xa2211c: stur            d0, [x0, #7]
    // 0xa22120: StoreField: r0->field_f = d0
    //     0xa22120: stur            d0, [x0, #0xf]
    // 0xa22124: r0 = BorderRadius()
    //     0xa22124: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa22128: mov             x1, x0
    // 0xa2212c: ldur            x0, [fp, #-0x28]
    // 0xa22130: stur            x1, [fp, #-0x30]
    // 0xa22134: StoreField: r1->field_7 = r0
    //     0xa22134: stur            w0, [x1, #7]
    // 0xa22138: StoreField: r1->field_b = r0
    //     0xa22138: stur            w0, [x1, #0xb]
    // 0xa2213c: StoreField: r1->field_f = r0
    //     0xa2213c: stur            w0, [x1, #0xf]
    // 0xa22140: StoreField: r1->field_13 = r0
    //     0xa22140: stur            w0, [x1, #0x13]
    // 0xa22144: r0 = BoxDecoration()
    //     0xa22144: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa22148: mov             x3, x0
    // 0xa2214c: ldur            x0, [fp, #-0x20]
    // 0xa22150: stur            x3, [fp, #-0x28]
    // 0xa22154: StoreField: r3->field_f = r0
    //     0xa22154: stur            w0, [x3, #0xf]
    // 0xa22158: ldur            x0, [fp, #-0x30]
    // 0xa2215c: StoreField: r3->field_13 = r0
    //     0xa2215c: stur            w0, [x3, #0x13]
    // 0xa22160: r2 = Instance_BoxShape
    //     0xa22160: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa22164: ldr             x2, [x2, #0x80]
    // 0xa22168: StoreField: r3->field_23 = r2
    //     0xa22168: stur            w2, [x3, #0x23]
    // 0xa2216c: ldur            x4, [fp, #-8]
    // 0xa22170: LoadField: r0 = r4->field_f
    //     0xa22170: ldur            w0, [x4, #0xf]
    // 0xa22174: DecompressPointer r0
    //     0xa22174: add             x0, x0, HEAP, lsl #32
    // 0xa22178: LoadField: r1 = r0->field_b
    //     0xa22178: ldur            w1, [x0, #0xb]
    // 0xa2217c: DecompressPointer r1
    //     0xa2217c: add             x1, x1, HEAP, lsl #32
    // 0xa22180: cmp             w1, NULL
    // 0xa22184: b.eq            #0xa2301c
    // 0xa22188: LoadField: r0 = r1->field_b
    //     0xa22188: ldur            w0, [x1, #0xb]
    // 0xa2218c: DecompressPointer r0
    //     0xa2218c: add             x0, x0, HEAP, lsl #32
    // 0xa22190: LoadField: r1 = r0->field_b
    //     0xa22190: ldur            w1, [x0, #0xb]
    // 0xa22194: DecompressPointer r1
    //     0xa22194: add             x1, x1, HEAP, lsl #32
    // 0xa22198: cmp             w1, NULL
    // 0xa2219c: b.ne            #0xa221a8
    // 0xa221a0: r0 = Null
    //     0xa221a0: mov             x0, NULL
    // 0xa221a4: b               #0xa221c8
    // 0xa221a8: LoadField: r0 = r1->field_27
    //     0xa221a8: ldur            w0, [x1, #0x27]
    // 0xa221ac: DecompressPointer r0
    //     0xa221ac: add             x0, x0, HEAP, lsl #32
    // 0xa221b0: cmp             w0, NULL
    // 0xa221b4: b.ne            #0xa221c0
    // 0xa221b8: r0 = Null
    //     0xa221b8: mov             x0, NULL
    // 0xa221bc: b               #0xa221c8
    // 0xa221c0: LoadField: r1 = r0->field_b
    //     0xa221c0: ldur            w1, [x0, #0xb]
    // 0xa221c4: mov             x0, x1
    // 0xa221c8: cmp             w0, NULL
    // 0xa221cc: b.ne            #0xa221d8
    // 0xa221d0: r4 = 0
    //     0xa221d0: movz            x4, #0
    // 0xa221d4: b               #0xa221e0
    // 0xa221d8: r1 = LoadInt32Instr(r0)
    //     0xa221d8: sbfx            x1, x0, #1, #0x1f
    // 0xa221dc: mov             x4, x1
    // 0xa221e0: ldur            x0, [fp, #-0x10]
    // 0xa221e4: ldur            x2, [fp, #-0x18]
    // 0xa221e8: stur            x4, [fp, #-0x48]
    // 0xa221ec: r1 = Function '<anonymous closure>':.
    //     0xa221ec: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b60] AnonymousClosure: (0xa23dfc), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::build (0xb4a564)
    //     0xa221f0: ldr             x1, [x1, #0xb60]
    // 0xa221f4: r0 = AllocateClosure()
    //     0xa221f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa221f8: r1 = Function '<anonymous closure>':.
    //     0xa221f8: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b68] AnonymousClosure: (0xa21634), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::build (0xb4a564)
    //     0xa221fc: ldr             x1, [x1, #0xb68]
    // 0xa22200: r2 = Null
    //     0xa22200: mov             x2, NULL
    // 0xa22204: stur            x0, [fp, #-0x20]
    // 0xa22208: r0 = AllocateClosure()
    //     0xa22208: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa2220c: stur            x0, [fp, #-0x30]
    // 0xa22210: r0 = ListView()
    //     0xa22210: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xa22214: stur            x0, [fp, #-0x38]
    // 0xa22218: r16 = true
    //     0xa22218: add             x16, NULL, #0x20  ; true
    // 0xa2221c: r30 = Instance_NeverScrollableScrollPhysics
    //     0xa2221c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xa22220: ldr             lr, [lr, #0x1c8]
    // 0xa22224: stp             lr, x16, [SP]
    // 0xa22228: mov             x1, x0
    // 0xa2222c: ldur            x2, [fp, #-0x20]
    // 0xa22230: ldur            x3, [fp, #-0x48]
    // 0xa22234: ldur            x5, [fp, #-0x30]
    // 0xa22238: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x5, shrinkWrap, 0x4, null]
    //     0xa22238: add             x4, PP, #0x40, lsl #12  ; [pp+0x40738] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0xa2223c: ldr             x4, [x4, #0x738]
    // 0xa22240: r0 = ListView.separated()
    //     0xa22240: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xa22244: r0 = Container()
    //     0xa22244: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa22248: stur            x0, [fp, #-0x20]
    // 0xa2224c: ldur            x16, [fp, #-0x28]
    // 0xa22250: ldur            lr, [fp, #-0x38]
    // 0xa22254: stp             lr, x16, [SP]
    // 0xa22258: mov             x1, x0
    // 0xa2225c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xa2225c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xa22260: ldr             x4, [x4, #0x88]
    // 0xa22264: r0 = Container()
    //     0xa22264: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa22268: r0 = Padding()
    //     0xa22268: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa2226c: r3 = Instance_EdgeInsets
    //     0xa2226c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xa22270: ldr             x3, [x3, #0xa00]
    // 0xa22274: stur            x0, [fp, #-0x28]
    // 0xa22278: StoreField: r0->field_f = r3
    //     0xa22278: stur            w3, [x0, #0xf]
    // 0xa2227c: ldur            x1, [fp, #-0x20]
    // 0xa22280: StoreField: r0->field_b = r1
    //     0xa22280: stur            w1, [x0, #0xb]
    // 0xa22284: ldur            x2, [fp, #-0x10]
    // 0xa22288: LoadField: r1 = r2->field_b
    //     0xa22288: ldur            w1, [x2, #0xb]
    // 0xa2228c: LoadField: r3 = r2->field_f
    //     0xa2228c: ldur            w3, [x2, #0xf]
    // 0xa22290: DecompressPointer r3
    //     0xa22290: add             x3, x3, HEAP, lsl #32
    // 0xa22294: LoadField: r4 = r3->field_b
    //     0xa22294: ldur            w4, [x3, #0xb]
    // 0xa22298: r3 = LoadInt32Instr(r1)
    //     0xa22298: sbfx            x3, x1, #1, #0x1f
    // 0xa2229c: stur            x3, [fp, #-0x48]
    // 0xa222a0: r1 = LoadInt32Instr(r4)
    //     0xa222a0: sbfx            x1, x4, #1, #0x1f
    // 0xa222a4: cmp             x3, x1
    // 0xa222a8: b.ne            #0xa222b4
    // 0xa222ac: mov             x1, x2
    // 0xa222b0: r0 = _growToNextCapacity()
    //     0xa222b0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa222b4: ldur            x5, [fp, #-0x10]
    // 0xa222b8: ldur            x2, [fp, #-0x48]
    // 0xa222bc: add             x0, x2, #1
    // 0xa222c0: lsl             x1, x0, #1
    // 0xa222c4: StoreField: r5->field_b = r1
    //     0xa222c4: stur            w1, [x5, #0xb]
    // 0xa222c8: LoadField: r1 = r5->field_f
    //     0xa222c8: ldur            w1, [x5, #0xf]
    // 0xa222cc: DecompressPointer r1
    //     0xa222cc: add             x1, x1, HEAP, lsl #32
    // 0xa222d0: ldur            x0, [fp, #-0x28]
    // 0xa222d4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa222d4: add             x25, x1, x2, lsl #2
    //     0xa222d8: add             x25, x25, #0xf
    //     0xa222dc: str             w0, [x25]
    //     0xa222e0: tbz             w0, #0, #0xa222fc
    //     0xa222e4: ldurb           w16, [x1, #-1]
    //     0xa222e8: ldurb           w17, [x0, #-1]
    //     0xa222ec: and             x16, x17, x16, lsr #2
    //     0xa222f0: tst             x16, HEAP, lsr #32
    //     0xa222f4: b.eq            #0xa222fc
    //     0xa222f8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa222fc: mov             x2, x5
    // 0xa22300: b               #0xa22f68
    // 0xa22304: ldur            x4, [fp, #-8]
    // 0xa22308: ldur            x5, [fp, #-0x10]
    // 0xa2230c: r3 = Instance_EdgeInsets
    //     0xa2230c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xa22310: ldr             x3, [x3, #0xa00]
    // 0xa22314: r2 = Instance_BoxShape
    //     0xa22314: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa22318: ldr             x2, [x2, #0x80]
    // 0xa2231c: d0 = 20.000000
    //     0xa2231c: fmov            d0, #20.00000000
    // 0xa22320: LoadField: r0 = r4->field_f
    //     0xa22320: ldur            w0, [x4, #0xf]
    // 0xa22324: DecompressPointer r0
    //     0xa22324: add             x0, x0, HEAP, lsl #32
    // 0xa22328: LoadField: r1 = r0->field_b
    //     0xa22328: ldur            w1, [x0, #0xb]
    // 0xa2232c: DecompressPointer r1
    //     0xa2232c: add             x1, x1, HEAP, lsl #32
    // 0xa22330: cmp             w1, NULL
    // 0xa22334: b.eq            #0xa23020
    // 0xa22338: LoadField: r0 = r1->field_b
    //     0xa22338: ldur            w0, [x1, #0xb]
    // 0xa2233c: DecompressPointer r0
    //     0xa2233c: add             x0, x0, HEAP, lsl #32
    // 0xa22340: LoadField: r1 = r0->field_b
    //     0xa22340: ldur            w1, [x0, #0xb]
    // 0xa22344: DecompressPointer r1
    //     0xa22344: add             x1, x1, HEAP, lsl #32
    // 0xa22348: cmp             w1, NULL
    // 0xa2234c: b.ne            #0xa2235c
    // 0xa22350: ldur            x7, [fp, #-0x18]
    // 0xa22354: r0 = Null
    //     0xa22354: mov             x0, NULL
    // 0xa22358: b               #0xa223dc
    // 0xa2235c: LoadField: r0 = r1->field_1f
    //     0xa2235c: ldur            w0, [x1, #0x1f]
    // 0xa22360: DecompressPointer r0
    //     0xa22360: add             x0, x0, HEAP, lsl #32
    // 0xa22364: cmp             w0, NULL
    // 0xa22368: b.ne            #0xa22378
    // 0xa2236c: ldur            x7, [fp, #-0x18]
    // 0xa22370: r0 = Null
    //     0xa22370: mov             x0, NULL
    // 0xa22374: b               #0xa223dc
    // 0xa22378: LoadField: r6 = r0->field_7
    //     0xa22378: ldur            w6, [x0, #7]
    // 0xa2237c: DecompressPointer r6
    //     0xa2237c: add             x6, x6, HEAP, lsl #32
    // 0xa22380: cmp             w6, NULL
    // 0xa22384: b.ne            #0xa22394
    // 0xa22388: ldur            x7, [fp, #-0x18]
    // 0xa2238c: r0 = Null
    //     0xa2238c: mov             x0, NULL
    // 0xa22390: b               #0xa223dc
    // 0xa22394: ldur            x7, [fp, #-0x18]
    // 0xa22398: LoadField: r0 = r7->field_f
    //     0xa22398: ldur            w0, [x7, #0xf]
    // 0xa2239c: DecompressPointer r0
    //     0xa2239c: add             x0, x0, HEAP, lsl #32
    // 0xa223a0: LoadField: r1 = r6->field_b
    //     0xa223a0: ldur            w1, [x6, #0xb]
    // 0xa223a4: r8 = LoadInt32Instr(r0)
    //     0xa223a4: sbfx            x8, x0, #1, #0x1f
    //     0xa223a8: tbz             w0, #0, #0xa223b0
    //     0xa223ac: ldur            x8, [x0, #7]
    // 0xa223b0: r0 = LoadInt32Instr(r1)
    //     0xa223b0: sbfx            x0, x1, #1, #0x1f
    // 0xa223b4: mov             x1, x8
    // 0xa223b8: cmp             x1, x0
    // 0xa223bc: b.hs            #0xa23024
    // 0xa223c0: LoadField: r0 = r6->field_f
    //     0xa223c0: ldur            w0, [x6, #0xf]
    // 0xa223c4: DecompressPointer r0
    //     0xa223c4: add             x0, x0, HEAP, lsl #32
    // 0xa223c8: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xa223c8: add             x16, x0, x8, lsl #2
    //     0xa223cc: ldur            w1, [x16, #0xf]
    // 0xa223d0: DecompressPointer r1
    //     0xa223d0: add             x1, x1, HEAP, lsl #32
    // 0xa223d4: LoadField: r0 = r1->field_7
    //     0xa223d4: ldur            w0, [x1, #7]
    // 0xa223d8: DecompressPointer r0
    //     0xa223d8: add             x0, x0, HEAP, lsl #32
    // 0xa223dc: r1 = LoadClassIdInstr(r0)
    //     0xa223dc: ldur            x1, [x0, #-1]
    //     0xa223e0: ubfx            x1, x1, #0xc, #0x14
    // 0xa223e4: r16 = "cod"
    //     0xa223e4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a28] "cod"
    //     0xa223e8: ldr             x16, [x16, #0xa28]
    // 0xa223ec: stp             x16, x0, [SP]
    // 0xa223f0: mov             x0, x1
    // 0xa223f4: mov             lr, x0
    // 0xa223f8: ldr             lr, [x21, lr, lsl #3]
    // 0xa223fc: blr             lr
    // 0xa22400: tbnz            w0, #4, #0xa22668
    // 0xa22404: ldur            x0, [fp, #-8]
    // 0xa22408: ldr             x1, [fp, #0x18]
    // 0xa2240c: r0 = of()
    //     0xa2240c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa22410: LoadField: r1 = r0->field_5b
    //     0xa22410: ldur            w1, [x0, #0x5b]
    // 0xa22414: DecompressPointer r1
    //     0xa22414: add             x1, x1, HEAP, lsl #32
    // 0xa22418: r0 = LoadClassIdInstr(r1)
    //     0xa22418: ldur            x0, [x1, #-1]
    //     0xa2241c: ubfx            x0, x0, #0xc, #0x14
    // 0xa22420: d0 = 0.100000
    //     0xa22420: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xa22424: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa22424: sub             lr, x0, #0xffa
    //     0xa22428: ldr             lr, [x21, lr, lsl #3]
    //     0xa2242c: blr             lr
    // 0xa22430: mov             x2, x0
    // 0xa22434: r1 = Null
    //     0xa22434: mov             x1, NULL
    // 0xa22438: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa22438: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa2243c: r0 = Border.all()
    //     0xa2243c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa22440: stur            x0, [fp, #-0x20]
    // 0xa22444: r0 = Radius()
    //     0xa22444: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa22448: d0 = 20.000000
    //     0xa22448: fmov            d0, #20.00000000
    // 0xa2244c: stur            x0, [fp, #-0x28]
    // 0xa22450: StoreField: r0->field_7 = d0
    //     0xa22450: stur            d0, [x0, #7]
    // 0xa22454: StoreField: r0->field_f = d0
    //     0xa22454: stur            d0, [x0, #0xf]
    // 0xa22458: r0 = BorderRadius()
    //     0xa22458: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa2245c: mov             x1, x0
    // 0xa22460: ldur            x0, [fp, #-0x28]
    // 0xa22464: stur            x1, [fp, #-0x30]
    // 0xa22468: StoreField: r1->field_7 = r0
    //     0xa22468: stur            w0, [x1, #7]
    // 0xa2246c: StoreField: r1->field_b = r0
    //     0xa2246c: stur            w0, [x1, #0xb]
    // 0xa22470: StoreField: r1->field_f = r0
    //     0xa22470: stur            w0, [x1, #0xf]
    // 0xa22474: StoreField: r1->field_13 = r0
    //     0xa22474: stur            w0, [x1, #0x13]
    // 0xa22478: r0 = BoxDecoration()
    //     0xa22478: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa2247c: mov             x4, x0
    // 0xa22480: ldur            x0, [fp, #-0x20]
    // 0xa22484: stur            x4, [fp, #-0x28]
    // 0xa22488: StoreField: r4->field_f = r0
    //     0xa22488: stur            w0, [x4, #0xf]
    // 0xa2248c: ldur            x0, [fp, #-0x30]
    // 0xa22490: StoreField: r4->field_13 = r0
    //     0xa22490: stur            w0, [x4, #0x13]
    // 0xa22494: r0 = Instance_BoxShape
    //     0xa22494: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa22498: ldr             x0, [x0, #0x80]
    // 0xa2249c: StoreField: r4->field_23 = r0
    //     0xa2249c: stur            w0, [x4, #0x23]
    // 0xa224a0: ldur            x2, [fp, #-8]
    // 0xa224a4: LoadField: r3 = r2->field_f
    //     0xa224a4: ldur            w3, [x2, #0xf]
    // 0xa224a8: DecompressPointer r3
    //     0xa224a8: add             x3, x3, HEAP, lsl #32
    // 0xa224ac: LoadField: r0 = r3->field_b
    //     0xa224ac: ldur            w0, [x3, #0xb]
    // 0xa224b0: DecompressPointer r0
    //     0xa224b0: add             x0, x0, HEAP, lsl #32
    // 0xa224b4: cmp             w0, NULL
    // 0xa224b8: b.eq            #0xa23028
    // 0xa224bc: LoadField: r1 = r0->field_b
    //     0xa224bc: ldur            w1, [x0, #0xb]
    // 0xa224c0: DecompressPointer r1
    //     0xa224c0: add             x1, x1, HEAP, lsl #32
    // 0xa224c4: LoadField: r0 = r1->field_b
    //     0xa224c4: ldur            w0, [x1, #0xb]
    // 0xa224c8: DecompressPointer r0
    //     0xa224c8: add             x0, x0, HEAP, lsl #32
    // 0xa224cc: cmp             w0, NULL
    // 0xa224d0: b.ne            #0xa224e0
    // 0xa224d4: ldur            x5, [fp, #-0x18]
    // 0xa224d8: r2 = Null
    //     0xa224d8: mov             x2, NULL
    // 0xa224dc: b               #0xa22560
    // 0xa224e0: LoadField: r1 = r0->field_1f
    //     0xa224e0: ldur            w1, [x0, #0x1f]
    // 0xa224e4: DecompressPointer r1
    //     0xa224e4: add             x1, x1, HEAP, lsl #32
    // 0xa224e8: cmp             w1, NULL
    // 0xa224ec: b.ne            #0xa224fc
    // 0xa224f0: ldur            x5, [fp, #-0x18]
    // 0xa224f4: r0 = Null
    //     0xa224f4: mov             x0, NULL
    // 0xa224f8: b               #0xa2255c
    // 0xa224fc: LoadField: r2 = r1->field_7
    //     0xa224fc: ldur            w2, [x1, #7]
    // 0xa22500: DecompressPointer r2
    //     0xa22500: add             x2, x2, HEAP, lsl #32
    // 0xa22504: cmp             w2, NULL
    // 0xa22508: b.ne            #0xa22518
    // 0xa2250c: ldur            x5, [fp, #-0x18]
    // 0xa22510: r0 = Null
    //     0xa22510: mov             x0, NULL
    // 0xa22514: b               #0xa2255c
    // 0xa22518: ldur            x5, [fp, #-0x18]
    // 0xa2251c: LoadField: r0 = r5->field_f
    //     0xa2251c: ldur            w0, [x5, #0xf]
    // 0xa22520: DecompressPointer r0
    //     0xa22520: add             x0, x0, HEAP, lsl #32
    // 0xa22524: LoadField: r1 = r2->field_b
    //     0xa22524: ldur            w1, [x2, #0xb]
    // 0xa22528: r6 = LoadInt32Instr(r0)
    //     0xa22528: sbfx            x6, x0, #1, #0x1f
    //     0xa2252c: tbz             w0, #0, #0xa22534
    //     0xa22530: ldur            x6, [x0, #7]
    // 0xa22534: r0 = LoadInt32Instr(r1)
    //     0xa22534: sbfx            x0, x1, #1, #0x1f
    // 0xa22538: mov             x1, x6
    // 0xa2253c: cmp             x1, x0
    // 0xa22540: b.hs            #0xa2302c
    // 0xa22544: LoadField: r0 = r2->field_f
    //     0xa22544: ldur            w0, [x2, #0xf]
    // 0xa22548: DecompressPointer r0
    //     0xa22548: add             x0, x0, HEAP, lsl #32
    // 0xa2254c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa2254c: add             x16, x0, x6, lsl #2
    //     0xa22550: ldur            w1, [x16, #0xf]
    // 0xa22554: DecompressPointer r1
    //     0xa22554: add             x1, x1, HEAP, lsl #32
    // 0xa22558: mov             x0, x1
    // 0xa2255c: mov             x2, x0
    // 0xa22560: ldur            x0, [fp, #-0x10]
    // 0xa22564: LoadField: r1 = r5->field_f
    //     0xa22564: ldur            w1, [x5, #0xf]
    // 0xa22568: DecompressPointer r1
    //     0xa22568: add             x1, x1, HEAP, lsl #32
    // 0xa2256c: r5 = LoadInt32Instr(r1)
    //     0xa2256c: sbfx            x5, x1, #1, #0x1f
    //     0xa22570: tbz             w1, #0, #0xa22578
    //     0xa22574: ldur            x5, [x1, #7]
    // 0xa22578: mov             x1, x3
    // 0xa2257c: mov             x3, x5
    // 0xa22580: ldr             x5, [fp, #0x18]
    // 0xa22584: r0 = codPaymentMethodCard()
    //     0xa22584: bl              #0xa23068  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::codPaymentMethodCard
    // 0xa22588: stur            x0, [fp, #-0x20]
    // 0xa2258c: r0 = Padding()
    //     0xa2258c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa22590: r3 = Instance_EdgeInsets
    //     0xa22590: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xa22594: ldr             x3, [x3, #0x1f0]
    // 0xa22598: stur            x0, [fp, #-0x30]
    // 0xa2259c: StoreField: r0->field_f = r3
    //     0xa2259c: stur            w3, [x0, #0xf]
    // 0xa225a0: ldur            x1, [fp, #-0x20]
    // 0xa225a4: StoreField: r0->field_b = r1
    //     0xa225a4: stur            w1, [x0, #0xb]
    // 0xa225a8: r0 = Container()
    //     0xa225a8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa225ac: stur            x0, [fp, #-0x20]
    // 0xa225b0: ldur            x16, [fp, #-0x28]
    // 0xa225b4: ldur            lr, [fp, #-0x30]
    // 0xa225b8: stp             lr, x16, [SP]
    // 0xa225bc: mov             x1, x0
    // 0xa225c0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xa225c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xa225c4: ldr             x4, [x4, #0x88]
    // 0xa225c8: r0 = Container()
    //     0xa225c8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa225cc: r0 = Padding()
    //     0xa225cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa225d0: r4 = Instance_EdgeInsets
    //     0xa225d0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xa225d4: ldr             x4, [x4, #0xa00]
    // 0xa225d8: stur            x0, [fp, #-0x28]
    // 0xa225dc: StoreField: r0->field_f = r4
    //     0xa225dc: stur            w4, [x0, #0xf]
    // 0xa225e0: ldur            x1, [fp, #-0x20]
    // 0xa225e4: StoreField: r0->field_b = r1
    //     0xa225e4: stur            w1, [x0, #0xb]
    // 0xa225e8: ldur            x2, [fp, #-0x10]
    // 0xa225ec: LoadField: r1 = r2->field_b
    //     0xa225ec: ldur            w1, [x2, #0xb]
    // 0xa225f0: LoadField: r3 = r2->field_f
    //     0xa225f0: ldur            w3, [x2, #0xf]
    // 0xa225f4: DecompressPointer r3
    //     0xa225f4: add             x3, x3, HEAP, lsl #32
    // 0xa225f8: LoadField: r4 = r3->field_b
    //     0xa225f8: ldur            w4, [x3, #0xb]
    // 0xa225fc: r3 = LoadInt32Instr(r1)
    //     0xa225fc: sbfx            x3, x1, #1, #0x1f
    // 0xa22600: stur            x3, [fp, #-0x48]
    // 0xa22604: r1 = LoadInt32Instr(r4)
    //     0xa22604: sbfx            x1, x4, #1, #0x1f
    // 0xa22608: cmp             x3, x1
    // 0xa2260c: b.ne            #0xa22618
    // 0xa22610: mov             x1, x2
    // 0xa22614: r0 = _growToNextCapacity()
    //     0xa22614: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa22618: ldur            x6, [fp, #-0x10]
    // 0xa2261c: ldur            x2, [fp, #-0x48]
    // 0xa22620: add             x0, x2, #1
    // 0xa22624: lsl             x1, x0, #1
    // 0xa22628: StoreField: r6->field_b = r1
    //     0xa22628: stur            w1, [x6, #0xb]
    // 0xa2262c: LoadField: r1 = r6->field_f
    //     0xa2262c: ldur            w1, [x6, #0xf]
    // 0xa22630: DecompressPointer r1
    //     0xa22630: add             x1, x1, HEAP, lsl #32
    // 0xa22634: ldur            x0, [fp, #-0x28]
    // 0xa22638: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa22638: add             x25, x1, x2, lsl #2
    //     0xa2263c: add             x25, x25, #0xf
    //     0xa22640: str             w0, [x25]
    //     0xa22644: tbz             w0, #0, #0xa22660
    //     0xa22648: ldurb           w16, [x1, #-1]
    //     0xa2264c: ldurb           w17, [x0, #-1]
    //     0xa22650: and             x16, x17, x16, lsr #2
    //     0xa22654: tst             x16, HEAP, lsr #32
    //     0xa22658: b.eq            #0xa22660
    //     0xa2265c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa22660: mov             x2, x6
    // 0xa22664: b               #0xa22f68
    // 0xa22668: ldur            x2, [fp, #-8]
    // 0xa2266c: ldur            x5, [fp, #-0x18]
    // 0xa22670: ldur            x6, [fp, #-0x10]
    // 0xa22674: r3 = Instance_EdgeInsets
    //     0xa22674: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xa22678: ldr             x3, [x3, #0x1f0]
    // 0xa2267c: r4 = Instance_EdgeInsets
    //     0xa2267c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xa22680: ldr             x4, [x4, #0xa00]
    // 0xa22684: r0 = Instance_BoxShape
    //     0xa22684: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa22688: ldr             x0, [x0, #0x80]
    // 0xa2268c: d0 = 20.000000
    //     0xa2268c: fmov            d0, #20.00000000
    // 0xa22690: ldr             x1, [fp, #0x18]
    // 0xa22694: r0 = of()
    //     0xa22694: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa22698: LoadField: r1 = r0->field_5b
    //     0xa22698: ldur            w1, [x0, #0x5b]
    // 0xa2269c: DecompressPointer r1
    //     0xa2269c: add             x1, x1, HEAP, lsl #32
    // 0xa226a0: r0 = LoadClassIdInstr(r1)
    //     0xa226a0: ldur            x0, [x1, #-1]
    //     0xa226a4: ubfx            x0, x0, #0xc, #0x14
    // 0xa226a8: d0 = 0.100000
    //     0xa226a8: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xa226ac: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa226ac: sub             lr, x0, #0xffa
    //     0xa226b0: ldr             lr, [x21, lr, lsl #3]
    //     0xa226b4: blr             lr
    // 0xa226b8: mov             x2, x0
    // 0xa226bc: r1 = Null
    //     0xa226bc: mov             x1, NULL
    // 0xa226c0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa226c0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa226c4: r0 = Border.all()
    //     0xa226c4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa226c8: stur            x0, [fp, #-0x20]
    // 0xa226cc: r0 = Radius()
    //     0xa226cc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa226d0: d0 = 20.000000
    //     0xa226d0: fmov            d0, #20.00000000
    // 0xa226d4: stur            x0, [fp, #-0x28]
    // 0xa226d8: StoreField: r0->field_7 = d0
    //     0xa226d8: stur            d0, [x0, #7]
    // 0xa226dc: StoreField: r0->field_f = d0
    //     0xa226dc: stur            d0, [x0, #0xf]
    // 0xa226e0: r0 = BorderRadius()
    //     0xa226e0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa226e4: mov             x1, x0
    // 0xa226e8: ldur            x0, [fp, #-0x28]
    // 0xa226ec: stur            x1, [fp, #-0x30]
    // 0xa226f0: StoreField: r1->field_7 = r0
    //     0xa226f0: stur            w0, [x1, #7]
    // 0xa226f4: StoreField: r1->field_b = r0
    //     0xa226f4: stur            w0, [x1, #0xb]
    // 0xa226f8: StoreField: r1->field_f = r0
    //     0xa226f8: stur            w0, [x1, #0xf]
    // 0xa226fc: StoreField: r1->field_13 = r0
    //     0xa226fc: stur            w0, [x1, #0x13]
    // 0xa22700: r0 = BoxDecoration()
    //     0xa22700: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa22704: mov             x1, x0
    // 0xa22708: ldur            x0, [fp, #-0x20]
    // 0xa2270c: stur            x1, [fp, #-0x28]
    // 0xa22710: StoreField: r1->field_f = r0
    //     0xa22710: stur            w0, [x1, #0xf]
    // 0xa22714: ldur            x0, [fp, #-0x30]
    // 0xa22718: StoreField: r1->field_13 = r0
    //     0xa22718: stur            w0, [x1, #0x13]
    // 0xa2271c: r0 = Instance_BoxShape
    //     0xa2271c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa22720: ldr             x0, [x0, #0x80]
    // 0xa22724: StoreField: r1->field_23 = r0
    //     0xa22724: stur            w0, [x1, #0x23]
    // 0xa22728: r0 = Image()
    //     0xa22728: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0xa2272c: mov             x1, x0
    // 0xa22730: r2 = "assets/images/pay_advance.png"
    //     0xa22730: add             x2, PP, #0x54, lsl #12  ; [pp+0x544e0] "assets/images/pay_advance.png"
    //     0xa22734: ldr             x2, [x2, #0x4e0]
    // 0xa22738: stur            x0, [fp, #-0x20]
    // 0xa2273c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa2273c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa22740: r0 = Image.asset()
    //     0xa22740: bl              #0xa20f60  ; [package:flutter/src/widgets/image.dart] Image::Image.asset
    // 0xa22744: ldur            x2, [fp, #-8]
    // 0xa22748: LoadField: r0 = r2->field_f
    //     0xa22748: ldur            w0, [x2, #0xf]
    // 0xa2274c: DecompressPointer r0
    //     0xa2274c: add             x0, x0, HEAP, lsl #32
    // 0xa22750: LoadField: r1 = r0->field_b
    //     0xa22750: ldur            w1, [x0, #0xb]
    // 0xa22754: DecompressPointer r1
    //     0xa22754: add             x1, x1, HEAP, lsl #32
    // 0xa22758: cmp             w1, NULL
    // 0xa2275c: b.eq            #0xa23030
    // 0xa22760: LoadField: r0 = r1->field_b
    //     0xa22760: ldur            w0, [x1, #0xb]
    // 0xa22764: DecompressPointer r0
    //     0xa22764: add             x0, x0, HEAP, lsl #32
    // 0xa22768: LoadField: r1 = r0->field_b
    //     0xa22768: ldur            w1, [x0, #0xb]
    // 0xa2276c: DecompressPointer r1
    //     0xa2276c: add             x1, x1, HEAP, lsl #32
    // 0xa22770: cmp             w1, NULL
    // 0xa22774: b.ne            #0xa22784
    // 0xa22778: ldur            x4, [fp, #-0x18]
    // 0xa2277c: r0 = Null
    //     0xa2277c: mov             x0, NULL
    // 0xa22780: b               #0xa22804
    // 0xa22784: LoadField: r0 = r1->field_1f
    //     0xa22784: ldur            w0, [x1, #0x1f]
    // 0xa22788: DecompressPointer r0
    //     0xa22788: add             x0, x0, HEAP, lsl #32
    // 0xa2278c: cmp             w0, NULL
    // 0xa22790: b.ne            #0xa227a0
    // 0xa22794: ldur            x4, [fp, #-0x18]
    // 0xa22798: r0 = Null
    //     0xa22798: mov             x0, NULL
    // 0xa2279c: b               #0xa22804
    // 0xa227a0: LoadField: r3 = r0->field_7
    //     0xa227a0: ldur            w3, [x0, #7]
    // 0xa227a4: DecompressPointer r3
    //     0xa227a4: add             x3, x3, HEAP, lsl #32
    // 0xa227a8: cmp             w3, NULL
    // 0xa227ac: b.ne            #0xa227bc
    // 0xa227b0: ldur            x4, [fp, #-0x18]
    // 0xa227b4: r0 = Null
    //     0xa227b4: mov             x0, NULL
    // 0xa227b8: b               #0xa22804
    // 0xa227bc: ldur            x4, [fp, #-0x18]
    // 0xa227c0: LoadField: r0 = r4->field_f
    //     0xa227c0: ldur            w0, [x4, #0xf]
    // 0xa227c4: DecompressPointer r0
    //     0xa227c4: add             x0, x0, HEAP, lsl #32
    // 0xa227c8: LoadField: r1 = r3->field_b
    //     0xa227c8: ldur            w1, [x3, #0xb]
    // 0xa227cc: r5 = LoadInt32Instr(r0)
    //     0xa227cc: sbfx            x5, x0, #1, #0x1f
    //     0xa227d0: tbz             w0, #0, #0xa227d8
    //     0xa227d4: ldur            x5, [x0, #7]
    // 0xa227d8: r0 = LoadInt32Instr(r1)
    //     0xa227d8: sbfx            x0, x1, #1, #0x1f
    // 0xa227dc: mov             x1, x5
    // 0xa227e0: cmp             x1, x0
    // 0xa227e4: b.hs            #0xa23034
    // 0xa227e8: LoadField: r0 = r3->field_f
    //     0xa227e8: ldur            w0, [x3, #0xf]
    // 0xa227ec: DecompressPointer r0
    //     0xa227ec: add             x0, x0, HEAP, lsl #32
    // 0xa227f0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa227f0: add             x16, x0, x5, lsl #2
    //     0xa227f4: ldur            w1, [x16, #0xf]
    // 0xa227f8: DecompressPointer r1
    //     0xa227f8: add             x1, x1, HEAP, lsl #32
    // 0xa227fc: LoadField: r0 = r1->field_13
    //     0xa227fc: ldur            w0, [x1, #0x13]
    // 0xa22800: DecompressPointer r0
    //     0xa22800: add             x0, x0, HEAP, lsl #32
    // 0xa22804: cmp             w0, NULL
    // 0xa22808: b.ne            #0xa22814
    // 0xa2280c: r3 = ""
    //     0xa2280c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa22810: b               #0xa22818
    // 0xa22814: mov             x3, x0
    // 0xa22818: ldur            x0, [fp, #-0x20]
    // 0xa2281c: ldr             x1, [fp, #0x18]
    // 0xa22820: stur            x3, [fp, #-0x30]
    // 0xa22824: r0 = of()
    //     0xa22824: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa22828: LoadField: r1 = r0->field_87
    //     0xa22828: ldur            w1, [x0, #0x87]
    // 0xa2282c: DecompressPointer r1
    //     0xa2282c: add             x1, x1, HEAP, lsl #32
    // 0xa22830: LoadField: r0 = r1->field_2b
    //     0xa22830: ldur            w0, [x1, #0x2b]
    // 0xa22834: DecompressPointer r0
    //     0xa22834: add             x0, x0, HEAP, lsl #32
    // 0xa22838: ldr             x1, [fp, #0x18]
    // 0xa2283c: stur            x0, [fp, #-0x38]
    // 0xa22840: r0 = of()
    //     0xa22840: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa22844: LoadField: r1 = r0->field_5b
    //     0xa22844: ldur            w1, [x0, #0x5b]
    // 0xa22848: DecompressPointer r1
    //     0xa22848: add             x1, x1, HEAP, lsl #32
    // 0xa2284c: r0 = LoadClassIdInstr(r1)
    //     0xa2284c: ldur            x0, [x1, #-1]
    //     0xa22850: ubfx            x0, x0, #0xc, #0x14
    // 0xa22854: d0 = 0.700000
    //     0xa22854: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa22858: ldr             d0, [x17, #0xf48]
    // 0xa2285c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa2285c: sub             lr, x0, #0xffa
    //     0xa22860: ldr             lr, [x21, lr, lsl #3]
    //     0xa22864: blr             lr
    // 0xa22868: r16 = 14.000000
    //     0xa22868: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa2286c: ldr             x16, [x16, #0x1d8]
    // 0xa22870: stp             x0, x16, [SP]
    // 0xa22874: ldur            x1, [fp, #-0x38]
    // 0xa22878: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa22878: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa2287c: ldr             x4, [x4, #0xaa0]
    // 0xa22880: r0 = copyWith()
    //     0xa22880: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa22884: stur            x0, [fp, #-0x38]
    // 0xa22888: r0 = TextSpan()
    //     0xa22888: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa2288c: mov             x1, x0
    // 0xa22890: ldur            x0, [fp, #-0x30]
    // 0xa22894: stur            x1, [fp, #-0x40]
    // 0xa22898: StoreField: r1->field_b = r0
    //     0xa22898: stur            w0, [x1, #0xb]
    // 0xa2289c: r0 = Instance__DeferringMouseCursor
    //     0xa2289c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa228a0: ArrayStore: r1[0] = r0  ; List_4
    //     0xa228a0: stur            w0, [x1, #0x17]
    // 0xa228a4: ldur            x2, [fp, #-0x38]
    // 0xa228a8: StoreField: r1->field_7 = r2
    //     0xa228a8: stur            w2, [x1, #7]
    // 0xa228ac: r0 = TapGestureRecognizer()
    //     0xa228ac: bl              #0x7ce314  ; AllocateTapGestureRecognizerStub -> TapGestureRecognizer (size=0x88)
    // 0xa228b0: stur            x0, [fp, #-0x30]
    // 0xa228b4: r16 = -1.000000
    //     0xa228b4: ldr             x16, [PP, #0x5bc0]  ; [pp+0x5bc0] -1
    // 0xa228b8: stp             x16, NULL, [SP]
    // 0xa228bc: mov             x1, x0
    // 0xa228c0: r4 = const [0, 0x3, 0x2, 0x1, postAcceptSlopTolerance, 0x2, supportedDevices, 0x1, null]
    //     0xa228c0: add             x4, PP, #0x47, lsl #12  ; [pp+0x47c80] List(9) [0, 0x3, 0x2, 0x1, "postAcceptSlopTolerance", 0x2, "supportedDevices", 0x1, Null]
    //     0xa228c4: ldr             x4, [x4, #0xc80]
    // 0xa228c8: r0 = BaseTapGestureRecognizer()
    //     0xa228c8: bl              #0x7ce238  ; [package:flutter/src/gestures/tap.dart] BaseTapGestureRecognizer::BaseTapGestureRecognizer
    // 0xa228cc: ldur            x2, [fp, #-0x18]
    // 0xa228d0: r1 = Function '<anonymous closure>':.
    //     0xa228d0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b70] AnonymousClosure: (0xa23d78), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::build (0xb4a564)
    //     0xa228d4: ldr             x1, [x1, #0xb70]
    // 0xa228d8: r0 = AllocateClosure()
    //     0xa228d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa228dc: ldur            x2, [fp, #-0x30]
    // 0xa228e0: StoreField: r2->field_5f = r0
    //     0xa228e0: stur            w0, [x2, #0x5f]
    //     0xa228e4: ldurb           w16, [x2, #-1]
    //     0xa228e8: ldurb           w17, [x0, #-1]
    //     0xa228ec: and             x16, x17, x16, lsr #2
    //     0xa228f0: tst             x16, HEAP, lsr #32
    //     0xa228f4: b.eq            #0xa228fc
    //     0xa228f8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa228fc: ldr             x1, [fp, #0x18]
    // 0xa22900: r0 = of()
    //     0xa22900: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa22904: LoadField: r1 = r0->field_87
    //     0xa22904: ldur            w1, [x0, #0x87]
    // 0xa22908: DecompressPointer r1
    //     0xa22908: add             x1, x1, HEAP, lsl #32
    // 0xa2290c: LoadField: r0 = r1->field_7
    //     0xa2290c: ldur            w0, [x1, #7]
    // 0xa22910: DecompressPointer r0
    //     0xa22910: add             x0, x0, HEAP, lsl #32
    // 0xa22914: ldr             x1, [fp, #0x18]
    // 0xa22918: stur            x0, [fp, #-0x38]
    // 0xa2291c: r0 = of()
    //     0xa2291c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa22920: LoadField: r1 = r0->field_5b
    //     0xa22920: ldur            w1, [x0, #0x5b]
    // 0xa22924: DecompressPointer r1
    //     0xa22924: add             x1, x1, HEAP, lsl #32
    // 0xa22928: r16 = 12.000000
    //     0xa22928: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa2292c: ldr             x16, [x16, #0x9e8]
    // 0xa22930: stp             x1, x16, [SP, #8]
    // 0xa22934: r16 = Instance_TextDecoration
    //     0xa22934: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xa22938: ldr             x16, [x16, #0x10]
    // 0xa2293c: str             x16, [SP]
    // 0xa22940: ldur            x1, [fp, #-0x38]
    // 0xa22944: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xa22944: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xa22948: ldr             x4, [x4, #0xe38]
    // 0xa2294c: r0 = copyWith()
    //     0xa2294c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa22950: stur            x0, [fp, #-0x38]
    // 0xa22954: r0 = TextSpan()
    //     0xa22954: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa22958: mov             x3, x0
    // 0xa2295c: r0 = "Know More"
    //     0xa2295c: add             x0, PP, #0x42, lsl #12  ; [pp+0x42f00] "Know More"
    //     0xa22960: ldr             x0, [x0, #0xf00]
    // 0xa22964: stur            x3, [fp, #-0x50]
    // 0xa22968: StoreField: r3->field_b = r0
    //     0xa22968: stur            w0, [x3, #0xb]
    // 0xa2296c: ldur            x0, [fp, #-0x30]
    // 0xa22970: StoreField: r3->field_13 = r0
    //     0xa22970: stur            w0, [x3, #0x13]
    // 0xa22974: r0 = Instance_SystemMouseCursor
    //     0xa22974: add             x0, PP, #0x4b, lsl #12  ; [pp+0x4bfe0] Obj!SystemMouseCursor@d645c1
    //     0xa22978: ldr             x0, [x0, #0xfe0]
    // 0xa2297c: ArrayStore: r3[0] = r0  ; List_4
    //     0xa2297c: stur            w0, [x3, #0x17]
    // 0xa22980: ldur            x0, [fp, #-0x38]
    // 0xa22984: StoreField: r3->field_7 = r0
    //     0xa22984: stur            w0, [x3, #7]
    // 0xa22988: r1 = Null
    //     0xa22988: mov             x1, NULL
    // 0xa2298c: r2 = 6
    //     0xa2298c: movz            x2, #0x6
    // 0xa22990: r0 = AllocateArray()
    //     0xa22990: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa22994: mov             x2, x0
    // 0xa22998: ldur            x0, [fp, #-0x40]
    // 0xa2299c: stur            x2, [fp, #-0x30]
    // 0xa229a0: StoreField: r2->field_f = r0
    //     0xa229a0: stur            w0, [x2, #0xf]
    // 0xa229a4: r16 = Instance_TextSpan
    //     0xa229a4: add             x16, PP, #0x54, lsl #12  ; [pp+0x54358] Obj!TextSpan@d65581
    //     0xa229a8: ldr             x16, [x16, #0x358]
    // 0xa229ac: StoreField: r2->field_13 = r16
    //     0xa229ac: stur            w16, [x2, #0x13]
    // 0xa229b0: ldur            x0, [fp, #-0x50]
    // 0xa229b4: ArrayStore: r2[0] = r0  ; List_4
    //     0xa229b4: stur            w0, [x2, #0x17]
    // 0xa229b8: r1 = <InlineSpan>
    //     0xa229b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xa229bc: ldr             x1, [x1, #0xe40]
    // 0xa229c0: r0 = AllocateGrowableArray()
    //     0xa229c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa229c4: mov             x1, x0
    // 0xa229c8: ldur            x0, [fp, #-0x30]
    // 0xa229cc: stur            x1, [fp, #-0x38]
    // 0xa229d0: StoreField: r1->field_f = r0
    //     0xa229d0: stur            w0, [x1, #0xf]
    // 0xa229d4: r2 = 6
    //     0xa229d4: movz            x2, #0x6
    // 0xa229d8: StoreField: r1->field_b = r2
    //     0xa229d8: stur            w2, [x1, #0xb]
    // 0xa229dc: r0 = TextSpan()
    //     0xa229dc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa229e0: mov             x1, x0
    // 0xa229e4: ldur            x0, [fp, #-0x38]
    // 0xa229e8: stur            x1, [fp, #-0x30]
    // 0xa229ec: StoreField: r1->field_f = r0
    //     0xa229ec: stur            w0, [x1, #0xf]
    // 0xa229f0: r0 = Instance__DeferringMouseCursor
    //     0xa229f0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa229f4: ArrayStore: r1[0] = r0  ; List_4
    //     0xa229f4: stur            w0, [x1, #0x17]
    // 0xa229f8: r0 = RichText()
    //     0xa229f8: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xa229fc: mov             x1, x0
    // 0xa22a00: ldur            x2, [fp, #-0x30]
    // 0xa22a04: stur            x0, [fp, #-0x30]
    // 0xa22a08: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa22a08: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa22a0c: r0 = RichText()
    //     0xa22a0c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xa22a10: r1 = <FlexParentData>
    //     0xa22a10: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa22a14: ldr             x1, [x1, #0xe00]
    // 0xa22a18: r0 = Expanded()
    //     0xa22a18: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa22a1c: mov             x3, x0
    // 0xa22a20: r0 = 1
    //     0xa22a20: movz            x0, #0x1
    // 0xa22a24: stur            x3, [fp, #-0x38]
    // 0xa22a28: StoreField: r3->field_13 = r0
    //     0xa22a28: stur            x0, [x3, #0x13]
    // 0xa22a2c: r0 = Instance_FlexFit
    //     0xa22a2c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa22a30: ldr             x0, [x0, #0xe08]
    // 0xa22a34: StoreField: r3->field_1b = r0
    //     0xa22a34: stur            w0, [x3, #0x1b]
    // 0xa22a38: ldur            x0, [fp, #-0x30]
    // 0xa22a3c: StoreField: r3->field_b = r0
    //     0xa22a3c: stur            w0, [x3, #0xb]
    // 0xa22a40: r1 = Null
    //     0xa22a40: mov             x1, NULL
    // 0xa22a44: r2 = 6
    //     0xa22a44: movz            x2, #0x6
    // 0xa22a48: r0 = AllocateArray()
    //     0xa22a48: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa22a4c: mov             x2, x0
    // 0xa22a50: ldur            x0, [fp, #-0x20]
    // 0xa22a54: stur            x2, [fp, #-0x30]
    // 0xa22a58: StoreField: r2->field_f = r0
    //     0xa22a58: stur            w0, [x2, #0xf]
    // 0xa22a5c: r16 = Instance_SizedBox
    //     0xa22a5c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xa22a60: ldr             x16, [x16, #0xb20]
    // 0xa22a64: StoreField: r2->field_13 = r16
    //     0xa22a64: stur            w16, [x2, #0x13]
    // 0xa22a68: ldur            x0, [fp, #-0x38]
    // 0xa22a6c: ArrayStore: r2[0] = r0  ; List_4
    //     0xa22a6c: stur            w0, [x2, #0x17]
    // 0xa22a70: r1 = <Widget>
    //     0xa22a70: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa22a74: r0 = AllocateGrowableArray()
    //     0xa22a74: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa22a78: mov             x1, x0
    // 0xa22a7c: ldur            x0, [fp, #-0x30]
    // 0xa22a80: stur            x1, [fp, #-0x20]
    // 0xa22a84: StoreField: r1->field_f = r0
    //     0xa22a84: stur            w0, [x1, #0xf]
    // 0xa22a88: r2 = 6
    //     0xa22a88: movz            x2, #0x6
    // 0xa22a8c: StoreField: r1->field_b = r2
    //     0xa22a8c: stur            w2, [x1, #0xb]
    // 0xa22a90: r0 = Row()
    //     0xa22a90: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa22a94: mov             x1, x0
    // 0xa22a98: r0 = Instance_Axis
    //     0xa22a98: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa22a9c: stur            x1, [fp, #-0x30]
    // 0xa22aa0: StoreField: r1->field_f = r0
    //     0xa22aa0: stur            w0, [x1, #0xf]
    // 0xa22aa4: r0 = Instance_MainAxisAlignment
    //     0xa22aa4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa22aa8: ldr             x0, [x0, #0xa08]
    // 0xa22aac: StoreField: r1->field_13 = r0
    //     0xa22aac: stur            w0, [x1, #0x13]
    // 0xa22ab0: r2 = Instance_MainAxisSize
    //     0xa22ab0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa22ab4: ldr             x2, [x2, #0xa10]
    // 0xa22ab8: ArrayStore: r1[0] = r2  ; List_4
    //     0xa22ab8: stur            w2, [x1, #0x17]
    // 0xa22abc: r3 = Instance_CrossAxisAlignment
    //     0xa22abc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa22ac0: ldr             x3, [x3, #0xa18]
    // 0xa22ac4: StoreField: r1->field_1b = r3
    //     0xa22ac4: stur            w3, [x1, #0x1b]
    // 0xa22ac8: r4 = Instance_VerticalDirection
    //     0xa22ac8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa22acc: ldr             x4, [x4, #0xa20]
    // 0xa22ad0: StoreField: r1->field_23 = r4
    //     0xa22ad0: stur            w4, [x1, #0x23]
    // 0xa22ad4: r5 = Instance_Clip
    //     0xa22ad4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa22ad8: ldr             x5, [x5, #0x38]
    // 0xa22adc: StoreField: r1->field_2b = r5
    //     0xa22adc: stur            w5, [x1, #0x2b]
    // 0xa22ae0: StoreField: r1->field_2f = rZR
    //     0xa22ae0: stur            xzr, [x1, #0x2f]
    // 0xa22ae4: ldur            x6, [fp, #-0x20]
    // 0xa22ae8: StoreField: r1->field_b = r6
    //     0xa22ae8: stur            w6, [x1, #0xb]
    // 0xa22aec: r0 = Padding()
    //     0xa22aec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa22af0: mov             x2, x0
    // 0xa22af4: r0 = Instance_EdgeInsets
    //     0xa22af4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xa22af8: ldr             x0, [x0, #0x1f0]
    // 0xa22afc: stur            x2, [fp, #-0x20]
    // 0xa22b00: StoreField: r2->field_f = r0
    //     0xa22b00: stur            w0, [x2, #0xf]
    // 0xa22b04: ldur            x0, [fp, #-0x30]
    // 0xa22b08: StoreField: r2->field_b = r0
    //     0xa22b08: stur            w0, [x2, #0xb]
    // 0xa22b0c: ldr             x1, [fp, #0x18]
    // 0xa22b10: r0 = of()
    //     0xa22b10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa22b14: LoadField: r1 = r0->field_5b
    //     0xa22b14: ldur            w1, [x0, #0x5b]
    // 0xa22b18: DecompressPointer r1
    //     0xa22b18: add             x1, x1, HEAP, lsl #32
    // 0xa22b1c: r0 = LoadClassIdInstr(r1)
    //     0xa22b1c: ldur            x0, [x1, #-1]
    //     0xa22b20: ubfx            x0, x0, #0xc, #0x14
    // 0xa22b24: d0 = 0.100000
    //     0xa22b24: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xa22b28: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa22b28: sub             lr, x0, #0xffa
    //     0xa22b2c: ldr             lr, [x21, lr, lsl #3]
    //     0xa22b30: blr             lr
    // 0xa22b34: stur            x0, [fp, #-0x30]
    // 0xa22b38: r0 = Divider()
    //     0xa22b38: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xa22b3c: mov             x1, x0
    // 0xa22b40: ldur            x0, [fp, #-0x30]
    // 0xa22b44: stur            x1, [fp, #-0x38]
    // 0xa22b48: StoreField: r1->field_1f = r0
    //     0xa22b48: stur            w0, [x1, #0x1f]
    // 0xa22b4c: r0 = Padding()
    //     0xa22b4c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa22b50: mov             x2, x0
    // 0xa22b54: r0 = Instance_EdgeInsets
    //     0xa22b54: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0xa22b58: ldr             x0, [x0, #0xb0]
    // 0xa22b5c: stur            x2, [fp, #-0x30]
    // 0xa22b60: StoreField: r2->field_f = r0
    //     0xa22b60: stur            w0, [x2, #0xf]
    // 0xa22b64: ldur            x0, [fp, #-0x38]
    // 0xa22b68: StoreField: r2->field_b = r0
    //     0xa22b68: stur            w0, [x2, #0xb]
    // 0xa22b6c: ldr             x1, [fp, #0x18]
    // 0xa22b70: r0 = of()
    //     0xa22b70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa22b74: LoadField: r1 = r0->field_87
    //     0xa22b74: ldur            w1, [x0, #0x87]
    // 0xa22b78: DecompressPointer r1
    //     0xa22b78: add             x1, x1, HEAP, lsl #32
    // 0xa22b7c: LoadField: r0 = r1->field_7
    //     0xa22b7c: ldur            w0, [x1, #7]
    // 0xa22b80: DecompressPointer r0
    //     0xa22b80: add             x0, x0, HEAP, lsl #32
    // 0xa22b84: r16 = Instance_Color
    //     0xa22b84: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa22b88: r30 = 12.000000
    //     0xa22b88: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa22b8c: ldr             lr, [lr, #0x9e8]
    // 0xa22b90: stp             lr, x16, [SP]
    // 0xa22b94: mov             x1, x0
    // 0xa22b98: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa22b98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa22b9c: ldr             x4, [x4, #0x9b8]
    // 0xa22ba0: r0 = copyWith()
    //     0xa22ba0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa22ba4: stur            x0, [fp, #-0x38]
    // 0xa22ba8: r0 = TextSpan()
    //     0xa22ba8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa22bac: mov             x2, x0
    // 0xa22bb0: r0 = "Select online payment method"
    //     0xa22bb0: add             x0, PP, #0x54, lsl #12  ; [pp+0x54510] "Select online payment method"
    //     0xa22bb4: ldr             x0, [x0, #0x510]
    // 0xa22bb8: stur            x2, [fp, #-0x40]
    // 0xa22bbc: StoreField: r2->field_b = r0
    //     0xa22bbc: stur            w0, [x2, #0xb]
    // 0xa22bc0: r0 = Instance__DeferringMouseCursor
    //     0xa22bc0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa22bc4: ArrayStore: r2[0] = r0  ; List_4
    //     0xa22bc4: stur            w0, [x2, #0x17]
    // 0xa22bc8: ldur            x1, [fp, #-0x38]
    // 0xa22bcc: StoreField: r2->field_7 = r1
    //     0xa22bcc: stur            w1, [x2, #7]
    // 0xa22bd0: ldr             x1, [fp, #0x18]
    // 0xa22bd4: r0 = of()
    //     0xa22bd4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa22bd8: LoadField: r1 = r0->field_87
    //     0xa22bd8: ldur            w1, [x0, #0x87]
    // 0xa22bdc: DecompressPointer r1
    //     0xa22bdc: add             x1, x1, HEAP, lsl #32
    // 0xa22be0: LoadField: r0 = r1->field_7
    //     0xa22be0: ldur            w0, [x1, #7]
    // 0xa22be4: DecompressPointer r0
    //     0xa22be4: add             x0, x0, HEAP, lsl #32
    // 0xa22be8: r16 = Instance_Color
    //     0xa22be8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xa22bec: ldr             x16, [x16, #0x50]
    // 0xa22bf0: r30 = 12.000000
    //     0xa22bf0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa22bf4: ldr             lr, [lr, #0x9e8]
    // 0xa22bf8: stp             lr, x16, [SP]
    // 0xa22bfc: mov             x1, x0
    // 0xa22c00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa22c00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa22c04: ldr             x4, [x4, #0x9b8]
    // 0xa22c08: r0 = copyWith()
    //     0xa22c08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa22c0c: stur            x0, [fp, #-0x38]
    // 0xa22c10: r0 = TextSpan()
    //     0xa22c10: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa22c14: mov             x3, x0
    // 0xa22c18: r0 = "*"
    //     0xa22c18: add             x0, PP, #0x33, lsl #12  ; [pp+0x337a8] "*"
    //     0xa22c1c: ldr             x0, [x0, #0x7a8]
    // 0xa22c20: stur            x3, [fp, #-0x50]
    // 0xa22c24: StoreField: r3->field_b = r0
    //     0xa22c24: stur            w0, [x3, #0xb]
    // 0xa22c28: r0 = Instance__DeferringMouseCursor
    //     0xa22c28: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa22c2c: ArrayStore: r3[0] = r0  ; List_4
    //     0xa22c2c: stur            w0, [x3, #0x17]
    // 0xa22c30: ldur            x1, [fp, #-0x38]
    // 0xa22c34: StoreField: r3->field_7 = r1
    //     0xa22c34: stur            w1, [x3, #7]
    // 0xa22c38: r1 = Null
    //     0xa22c38: mov             x1, NULL
    // 0xa22c3c: r2 = 4
    //     0xa22c3c: movz            x2, #0x4
    // 0xa22c40: r0 = AllocateArray()
    //     0xa22c40: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa22c44: mov             x2, x0
    // 0xa22c48: ldur            x0, [fp, #-0x40]
    // 0xa22c4c: stur            x2, [fp, #-0x38]
    // 0xa22c50: StoreField: r2->field_f = r0
    //     0xa22c50: stur            w0, [x2, #0xf]
    // 0xa22c54: ldur            x0, [fp, #-0x50]
    // 0xa22c58: StoreField: r2->field_13 = r0
    //     0xa22c58: stur            w0, [x2, #0x13]
    // 0xa22c5c: r1 = <InlineSpan>
    //     0xa22c5c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xa22c60: ldr             x1, [x1, #0xe40]
    // 0xa22c64: r0 = AllocateGrowableArray()
    //     0xa22c64: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa22c68: mov             x1, x0
    // 0xa22c6c: ldur            x0, [fp, #-0x38]
    // 0xa22c70: stur            x1, [fp, #-0x40]
    // 0xa22c74: StoreField: r1->field_f = r0
    //     0xa22c74: stur            w0, [x1, #0xf]
    // 0xa22c78: r0 = 4
    //     0xa22c78: movz            x0, #0x4
    // 0xa22c7c: StoreField: r1->field_b = r0
    //     0xa22c7c: stur            w0, [x1, #0xb]
    // 0xa22c80: r0 = TextSpan()
    //     0xa22c80: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa22c84: mov             x1, x0
    // 0xa22c88: ldur            x0, [fp, #-0x40]
    // 0xa22c8c: stur            x1, [fp, #-0x38]
    // 0xa22c90: StoreField: r1->field_f = r0
    //     0xa22c90: stur            w0, [x1, #0xf]
    // 0xa22c94: r0 = Instance__DeferringMouseCursor
    //     0xa22c94: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa22c98: ArrayStore: r1[0] = r0  ; List_4
    //     0xa22c98: stur            w0, [x1, #0x17]
    // 0xa22c9c: r0 = RichText()
    //     0xa22c9c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xa22ca0: mov             x1, x0
    // 0xa22ca4: ldur            x2, [fp, #-0x38]
    // 0xa22ca8: stur            x0, [fp, #-0x38]
    // 0xa22cac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa22cac: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa22cb0: r0 = RichText()
    //     0xa22cb0: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xa22cb4: ldur            x0, [fp, #-8]
    // 0xa22cb8: LoadField: r1 = r0->field_f
    //     0xa22cb8: ldur            w1, [x0, #0xf]
    // 0xa22cbc: DecompressPointer r1
    //     0xa22cbc: add             x1, x1, HEAP, lsl #32
    // 0xa22cc0: LoadField: r0 = r1->field_b
    //     0xa22cc0: ldur            w0, [x1, #0xb]
    // 0xa22cc4: DecompressPointer r0
    //     0xa22cc4: add             x0, x0, HEAP, lsl #32
    // 0xa22cc8: cmp             w0, NULL
    // 0xa22ccc: b.eq            #0xa23038
    // 0xa22cd0: LoadField: r1 = r0->field_b
    //     0xa22cd0: ldur            w1, [x0, #0xb]
    // 0xa22cd4: DecompressPointer r1
    //     0xa22cd4: add             x1, x1, HEAP, lsl #32
    // 0xa22cd8: LoadField: r0 = r1->field_b
    //     0xa22cd8: ldur            w0, [x1, #0xb]
    // 0xa22cdc: DecompressPointer r0
    //     0xa22cdc: add             x0, x0, HEAP, lsl #32
    // 0xa22ce0: cmp             w0, NULL
    // 0xa22ce4: b.ne            #0xa22cf0
    // 0xa22ce8: r0 = Null
    //     0xa22ce8: mov             x0, NULL
    // 0xa22cec: b               #0xa22cfc
    // 0xa22cf0: LoadField: r1 = r0->field_27
    //     0xa22cf0: ldur            w1, [x0, #0x27]
    // 0xa22cf4: DecompressPointer r1
    //     0xa22cf4: add             x1, x1, HEAP, lsl #32
    // 0xa22cf8: mov             x0, x1
    // 0xa22cfc: cmp             w0, NULL
    // 0xa22d00: b.ne            #0xa22d14
    // 0xa22d04: r1 = <PaymentOptions>
    //     0xa22d04: add             x1, PP, #0x22, lsl #12  ; [pp+0x225a8] TypeArguments: <PaymentOptions>
    //     0xa22d08: ldr             x1, [x1, #0x5a8]
    // 0xa22d0c: r2 = 0
    //     0xa22d0c: movz            x2, #0
    // 0xa22d10: r0 = AllocateArray()
    //     0xa22d10: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa22d14: ldur            x3, [fp, #-0x20]
    // 0xa22d18: ldur            x2, [fp, #-0x30]
    // 0xa22d1c: ldur            x1, [fp, #-0x38]
    // 0xa22d20: ldur            x4, [fp, #-0x10]
    // 0xa22d24: r5 = LoadClassIdInstr(r0)
    //     0xa22d24: ldur            x5, [x0, #-1]
    //     0xa22d28: ubfx            x5, x5, #0xc, #0x14
    // 0xa22d2c: str             x0, [SP]
    // 0xa22d30: mov             x0, x5
    // 0xa22d34: r0 = GDT[cid_x0 + 0xc898]()
    //     0xa22d34: movz            x17, #0xc898
    //     0xa22d38: add             lr, x0, x17
    //     0xa22d3c: ldr             lr, [x21, lr, lsl #3]
    //     0xa22d40: blr             lr
    // 0xa22d44: r3 = LoadInt32Instr(r0)
    //     0xa22d44: sbfx            x3, x0, #1, #0x1f
    // 0xa22d48: ldur            x2, [fp, #-0x18]
    // 0xa22d4c: stur            x3, [fp, #-0x48]
    // 0xa22d50: r1 = Function '<anonymous closure>':.
    //     0xa22d50: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b78] AnonymousClosure: (0xa235e4), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::build (0xb4a564)
    //     0xa22d54: ldr             x1, [x1, #0xb78]
    // 0xa22d58: r0 = AllocateClosure()
    //     0xa22d58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa22d5c: r1 = Function '<anonymous closure>':.
    //     0xa22d5c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b80] AnonymousClosure: (0xa21634), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::build (0xb4a564)
    //     0xa22d60: ldr             x1, [x1, #0xb80]
    // 0xa22d64: r2 = Null
    //     0xa22d64: mov             x2, NULL
    // 0xa22d68: stur            x0, [fp, #-8]
    // 0xa22d6c: r0 = AllocateClosure()
    //     0xa22d6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa22d70: stur            x0, [fp, #-0x40]
    // 0xa22d74: r0 = ListView()
    //     0xa22d74: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xa22d78: stur            x0, [fp, #-0x50]
    // 0xa22d7c: r16 = true
    //     0xa22d7c: add             x16, NULL, #0x20  ; true
    // 0xa22d80: r30 = false
    //     0xa22d80: add             lr, NULL, #0x30  ; false
    // 0xa22d84: stp             lr, x16, [SP]
    // 0xa22d88: mov             x1, x0
    // 0xa22d8c: ldur            x2, [fp, #-8]
    // 0xa22d90: ldur            x3, [fp, #-0x48]
    // 0xa22d94: ldur            x5, [fp, #-0x40]
    // 0xa22d98: r4 = const [0, 0x6, 0x2, 0x4, primary, 0x5, shrinkWrap, 0x4, null]
    //     0xa22d98: add             x4, PP, #0x38, lsl #12  ; [pp+0x38c98] List(9) [0, 0x6, 0x2, 0x4, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0xa22d9c: ldr             x4, [x4, #0xc98]
    // 0xa22da0: r0 = ListView.separated()
    //     0xa22da0: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xa22da4: r1 = Null
    //     0xa22da4: mov             x1, NULL
    // 0xa22da8: r2 = 6
    //     0xa22da8: movz            x2, #0x6
    // 0xa22dac: r0 = AllocateArray()
    //     0xa22dac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa22db0: mov             x2, x0
    // 0xa22db4: ldur            x0, [fp, #-0x30]
    // 0xa22db8: stur            x2, [fp, #-8]
    // 0xa22dbc: StoreField: r2->field_f = r0
    //     0xa22dbc: stur            w0, [x2, #0xf]
    // 0xa22dc0: ldur            x0, [fp, #-0x38]
    // 0xa22dc4: StoreField: r2->field_13 = r0
    //     0xa22dc4: stur            w0, [x2, #0x13]
    // 0xa22dc8: ldur            x0, [fp, #-0x50]
    // 0xa22dcc: ArrayStore: r2[0] = r0  ; List_4
    //     0xa22dcc: stur            w0, [x2, #0x17]
    // 0xa22dd0: r1 = <Widget>
    //     0xa22dd0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa22dd4: r0 = AllocateGrowableArray()
    //     0xa22dd4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa22dd8: mov             x1, x0
    // 0xa22ddc: ldur            x0, [fp, #-8]
    // 0xa22de0: stur            x1, [fp, #-0x30]
    // 0xa22de4: StoreField: r1->field_f = r0
    //     0xa22de4: stur            w0, [x1, #0xf]
    // 0xa22de8: r0 = 6
    //     0xa22de8: movz            x0, #0x6
    // 0xa22dec: StoreField: r1->field_b = r0
    //     0xa22dec: stur            w0, [x1, #0xb]
    // 0xa22df0: r0 = Column()
    //     0xa22df0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa22df4: mov             x1, x0
    // 0xa22df8: r0 = Instance_Axis
    //     0xa22df8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa22dfc: stur            x1, [fp, #-8]
    // 0xa22e00: StoreField: r1->field_f = r0
    //     0xa22e00: stur            w0, [x1, #0xf]
    // 0xa22e04: r2 = Instance_MainAxisAlignment
    //     0xa22e04: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa22e08: ldr             x2, [x2, #0xa08]
    // 0xa22e0c: StoreField: r1->field_13 = r2
    //     0xa22e0c: stur            w2, [x1, #0x13]
    // 0xa22e10: r3 = Instance_MainAxisSize
    //     0xa22e10: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa22e14: ldr             x3, [x3, #0xa10]
    // 0xa22e18: ArrayStore: r1[0] = r3  ; List_4
    //     0xa22e18: stur            w3, [x1, #0x17]
    // 0xa22e1c: r4 = Instance_CrossAxisAlignment
    //     0xa22e1c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa22e20: ldr             x4, [x4, #0x890]
    // 0xa22e24: StoreField: r1->field_1b = r4
    //     0xa22e24: stur            w4, [x1, #0x1b]
    // 0xa22e28: r4 = Instance_VerticalDirection
    //     0xa22e28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa22e2c: ldr             x4, [x4, #0xa20]
    // 0xa22e30: StoreField: r1->field_23 = r4
    //     0xa22e30: stur            w4, [x1, #0x23]
    // 0xa22e34: r5 = Instance_Clip
    //     0xa22e34: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa22e38: ldr             x5, [x5, #0x38]
    // 0xa22e3c: StoreField: r1->field_2b = r5
    //     0xa22e3c: stur            w5, [x1, #0x2b]
    // 0xa22e40: StoreField: r1->field_2f = rZR
    //     0xa22e40: stur            xzr, [x1, #0x2f]
    // 0xa22e44: ldur            x6, [fp, #-0x30]
    // 0xa22e48: StoreField: r1->field_b = r6
    //     0xa22e48: stur            w6, [x1, #0xb]
    // 0xa22e4c: r0 = Container()
    //     0xa22e4c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa22e50: mov             x1, x0
    // 0xa22e54: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa22e54: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa22e58: r0 = Container()
    //     0xa22e58: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa22e5c: r0 = Accordion()
    //     0xa22e5c: bl              #0xa2303c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0xa22e60: mov             x3, x0
    // 0xa22e64: ldur            x0, [fp, #-0x20]
    // 0xa22e68: stur            x3, [fp, #-0x30]
    // 0xa22e6c: StoreField: r3->field_b = r0
    //     0xa22e6c: stur            w0, [x3, #0xb]
    // 0xa22e70: ldur            x0, [fp, #-8]
    // 0xa22e74: StoreField: r3->field_13 = r0
    //     0xa22e74: stur            w0, [x3, #0x13]
    // 0xa22e78: r0 = false
    //     0xa22e78: add             x0, NULL, #0x30  ; false
    // 0xa22e7c: ArrayStore: r3[0] = r0  ; List_4
    //     0xa22e7c: stur            w0, [x3, #0x17]
    // 0xa22e80: d0 = 25.000000
    //     0xa22e80: fmov            d0, #25.00000000
    // 0xa22e84: StoreField: r3->field_1b = d0
    //     0xa22e84: stur            d0, [x3, #0x1b]
    // 0xa22e88: r0 = true
    //     0xa22e88: add             x0, NULL, #0x20  ; true
    // 0xa22e8c: StoreField: r3->field_23 = r0
    //     0xa22e8c: stur            w0, [x3, #0x23]
    // 0xa22e90: ldur            x2, [fp, #-0x18]
    // 0xa22e94: r1 = Function '<anonymous closure>':.
    //     0xa22e94: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b88] AnonymousClosure: (0xa2354c), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::build (0xb4a564)
    //     0xa22e98: ldr             x1, [x1, #0xb88]
    // 0xa22e9c: r0 = AllocateClosure()
    //     0xa22e9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa22ea0: mov             x1, x0
    // 0xa22ea4: ldur            x0, [fp, #-0x30]
    // 0xa22ea8: StoreField: r0->field_3b = r1
    //     0xa22ea8: stur            w1, [x0, #0x3b]
    // 0xa22eac: r0 = Container()
    //     0xa22eac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa22eb0: stur            x0, [fp, #-8]
    // 0xa22eb4: ldur            x16, [fp, #-0x28]
    // 0xa22eb8: ldur            lr, [fp, #-0x30]
    // 0xa22ebc: stp             lr, x16, [SP]
    // 0xa22ec0: mov             x1, x0
    // 0xa22ec4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xa22ec4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xa22ec8: ldr             x4, [x4, #0x88]
    // 0xa22ecc: r0 = Container()
    //     0xa22ecc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa22ed0: r0 = Padding()
    //     0xa22ed0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa22ed4: mov             x2, x0
    // 0xa22ed8: r0 = Instance_EdgeInsets
    //     0xa22ed8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xa22edc: ldr             x0, [x0, #0xa00]
    // 0xa22ee0: stur            x2, [fp, #-0x18]
    // 0xa22ee4: StoreField: r2->field_f = r0
    //     0xa22ee4: stur            w0, [x2, #0xf]
    // 0xa22ee8: ldur            x0, [fp, #-8]
    // 0xa22eec: StoreField: r2->field_b = r0
    //     0xa22eec: stur            w0, [x2, #0xb]
    // 0xa22ef0: ldur            x0, [fp, #-0x10]
    // 0xa22ef4: LoadField: r1 = r0->field_b
    //     0xa22ef4: ldur            w1, [x0, #0xb]
    // 0xa22ef8: LoadField: r3 = r0->field_f
    //     0xa22ef8: ldur            w3, [x0, #0xf]
    // 0xa22efc: DecompressPointer r3
    //     0xa22efc: add             x3, x3, HEAP, lsl #32
    // 0xa22f00: LoadField: r4 = r3->field_b
    //     0xa22f00: ldur            w4, [x3, #0xb]
    // 0xa22f04: r3 = LoadInt32Instr(r1)
    //     0xa22f04: sbfx            x3, x1, #1, #0x1f
    // 0xa22f08: stur            x3, [fp, #-0x48]
    // 0xa22f0c: r1 = LoadInt32Instr(r4)
    //     0xa22f0c: sbfx            x1, x4, #1, #0x1f
    // 0xa22f10: cmp             x3, x1
    // 0xa22f14: b.ne            #0xa22f20
    // 0xa22f18: mov             x1, x0
    // 0xa22f1c: r0 = _growToNextCapacity()
    //     0xa22f1c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa22f20: ldur            x2, [fp, #-0x10]
    // 0xa22f24: ldur            x3, [fp, #-0x48]
    // 0xa22f28: add             x0, x3, #1
    // 0xa22f2c: lsl             x1, x0, #1
    // 0xa22f30: StoreField: r2->field_b = r1
    //     0xa22f30: stur            w1, [x2, #0xb]
    // 0xa22f34: LoadField: r1 = r2->field_f
    //     0xa22f34: ldur            w1, [x2, #0xf]
    // 0xa22f38: DecompressPointer r1
    //     0xa22f38: add             x1, x1, HEAP, lsl #32
    // 0xa22f3c: ldur            x0, [fp, #-0x18]
    // 0xa22f40: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa22f40: add             x25, x1, x3, lsl #2
    //     0xa22f44: add             x25, x25, #0xf
    //     0xa22f48: str             w0, [x25]
    //     0xa22f4c: tbz             w0, #0, #0xa22f68
    //     0xa22f50: ldurb           w16, [x1, #-1]
    //     0xa22f54: ldurb           w17, [x0, #-1]
    //     0xa22f58: and             x16, x17, x16, lsr #2
    //     0xa22f5c: tst             x16, HEAP, lsr #32
    //     0xa22f60: b.eq            #0xa22f68
    //     0xa22f64: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa22f68: r0 = Column()
    //     0xa22f68: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa22f6c: mov             x1, x0
    // 0xa22f70: r0 = Instance_Axis
    //     0xa22f70: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa22f74: stur            x1, [fp, #-8]
    // 0xa22f78: StoreField: r1->field_f = r0
    //     0xa22f78: stur            w0, [x1, #0xf]
    // 0xa22f7c: r0 = Instance_MainAxisAlignment
    //     0xa22f7c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa22f80: ldr             x0, [x0, #0xa08]
    // 0xa22f84: StoreField: r1->field_13 = r0
    //     0xa22f84: stur            w0, [x1, #0x13]
    // 0xa22f88: r0 = Instance_MainAxisSize
    //     0xa22f88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa22f8c: ldr             x0, [x0, #0xa10]
    // 0xa22f90: ArrayStore: r1[0] = r0  ; List_4
    //     0xa22f90: stur            w0, [x1, #0x17]
    // 0xa22f94: r0 = Instance_CrossAxisAlignment
    //     0xa22f94: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa22f98: ldr             x0, [x0, #0xa18]
    // 0xa22f9c: StoreField: r1->field_1b = r0
    //     0xa22f9c: stur            w0, [x1, #0x1b]
    // 0xa22fa0: r0 = Instance_VerticalDirection
    //     0xa22fa0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa22fa4: ldr             x0, [x0, #0xa20]
    // 0xa22fa8: StoreField: r1->field_23 = r0
    //     0xa22fa8: stur            w0, [x1, #0x23]
    // 0xa22fac: r0 = Instance_Clip
    //     0xa22fac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa22fb0: ldr             x0, [x0, #0x38]
    // 0xa22fb4: StoreField: r1->field_2b = r0
    //     0xa22fb4: stur            w0, [x1, #0x2b]
    // 0xa22fb8: StoreField: r1->field_2f = rZR
    //     0xa22fb8: stur            xzr, [x1, #0x2f]
    // 0xa22fbc: ldur            x0, [fp, #-0x10]
    // 0xa22fc0: StoreField: r1->field_b = r0
    //     0xa22fc0: stur            w0, [x1, #0xb]
    // 0xa22fc4: r0 = Padding()
    //     0xa22fc4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa22fc8: r1 = Instance_EdgeInsets
    //     0xa22fc8: add             x1, PP, #0x54, lsl #12  ; [pp+0x544d8] Obj!EdgeInsets@d580d1
    //     0xa22fcc: ldr             x1, [x1, #0x4d8]
    // 0xa22fd0: StoreField: r0->field_f = r1
    //     0xa22fd0: stur            w1, [x0, #0xf]
    // 0xa22fd4: ldur            x1, [fp, #-8]
    // 0xa22fd8: StoreField: r0->field_b = r1
    //     0xa22fd8: stur            w1, [x0, #0xb]
    // 0xa22fdc: LeaveFrame
    //     0xa22fdc: mov             SP, fp
    //     0xa22fe0: ldp             fp, lr, [SP], #0x10
    // 0xa22fe4: ret
    //     0xa22fe4: ret             
    // 0xa22fe8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa22fe8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa22fec: b               #0xa21818
    // 0xa22ff0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa22ff0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa22ff4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa22ff4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa22ff8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa22ff8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa22ffc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa22ffc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa23000: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa23000: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa23004: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa23004: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa23008: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa23008: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa2300c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa2300c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa23010: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa23010: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa23014: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa23014: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa23018: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa23018: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa2301c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa2301c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa23020: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa23020: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xa23024: r0 = RangeErrorSharedWithFPURegs()
    //     0xa23024: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xa23028: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa23028: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa2302c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa2302c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa23030: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa23030: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa23034: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa23034: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa23038: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa23038: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ codPaymentMethodCard(/* No info */) {
    // ** addr: 0xa23068, size: 0x304
    // 0xa23068: EnterFrame
    //     0xa23068: stp             fp, lr, [SP, #-0x10]!
    //     0xa2306c: mov             fp, SP
    // 0xa23070: AllocStack(0x50)
    //     0xa23070: sub             SP, SP, #0x50
    // 0xa23074: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */)
    //     0xa23074: mov             x0, x1
    //     0xa23078: stur            x1, [fp, #-8]
    //     0xa2307c: mov             x1, x5
    //     0xa23080: stur            x2, [fp, #-0x10]
    //     0xa23084: stur            x3, [fp, #-0x18]
    //     0xa23088: stur            x5, [fp, #-0x20]
    // 0xa2308c: CheckStackOverflow
    //     0xa2308c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa23090: cmp             SP, x16
    //     0xa23094: b.ls            #0xa23360
    // 0xa23098: r1 = 3
    //     0xa23098: movz            x1, #0x3
    // 0xa2309c: r0 = AllocateContext()
    //     0xa2309c: bl              #0x16f6108  ; AllocateContextStub
    // 0xa230a0: mov             x3, x0
    // 0xa230a4: ldur            x2, [fp, #-8]
    // 0xa230a8: stur            x3, [fp, #-0x28]
    // 0xa230ac: StoreField: r3->field_f = r2
    //     0xa230ac: stur            w2, [x3, #0xf]
    // 0xa230b0: ldur            x4, [fp, #-0x10]
    // 0xa230b4: StoreField: r3->field_13 = r4
    //     0xa230b4: stur            w4, [x3, #0x13]
    // 0xa230b8: ldur            x5, [fp, #-0x18]
    // 0xa230bc: r0 = BoxInt64Instr(r5)
    //     0xa230bc: sbfiz           x0, x5, #1, #0x1f
    //     0xa230c0: cmp             x5, x0, asr #1
    //     0xa230c4: b.eq            #0xa230d0
    //     0xa230c8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa230cc: stur            x5, [x0, #7]
    // 0xa230d0: ArrayStore: r3[0] = r0  ; List_4
    //     0xa230d0: stur            w0, [x3, #0x17]
    // 0xa230d4: r0 = Image()
    //     0xa230d4: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0xa230d8: mov             x1, x0
    // 0xa230dc: r2 = "assets/images/cod.png"
    //     0xa230dc: add             x2, PP, #0x54, lsl #12  ; [pp+0x545d0] "assets/images/cod.png"
    //     0xa230e0: ldr             x2, [x2, #0x5d0]
    // 0xa230e4: stur            x0, [fp, #-0x30]
    // 0xa230e8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa230e8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa230ec: r0 = Image.asset()
    //     0xa230ec: bl              #0xa20f60  ; [package:flutter/src/widgets/image.dart] Image::Image.asset
    // 0xa230f0: ldur            x0, [fp, #-0x10]
    // 0xa230f4: cmp             w0, NULL
    // 0xa230f8: b.ne            #0xa23104
    // 0xa230fc: r0 = Null
    //     0xa230fc: mov             x0, NULL
    // 0xa23100: b               #0xa23110
    // 0xa23104: LoadField: r1 = r0->field_b
    //     0xa23104: ldur            w1, [x0, #0xb]
    // 0xa23108: DecompressPointer r1
    //     0xa23108: add             x1, x1, HEAP, lsl #32
    // 0xa2310c: mov             x0, x1
    // 0xa23110: cmp             w0, NULL
    // 0xa23114: b.ne            #0xa23120
    // 0xa23118: r2 = ""
    //     0xa23118: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa2311c: b               #0xa23124
    // 0xa23120: mov             x2, x0
    // 0xa23124: ldur            x0, [fp, #-8]
    // 0xa23128: ldur            x1, [fp, #-0x20]
    // 0xa2312c: stur            x2, [fp, #-0x10]
    // 0xa23130: r0 = of()
    //     0xa23130: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa23134: LoadField: r1 = r0->field_87
    //     0xa23134: ldur            w1, [x0, #0x87]
    // 0xa23138: DecompressPointer r1
    //     0xa23138: add             x1, x1, HEAP, lsl #32
    // 0xa2313c: LoadField: r0 = r1->field_2b
    //     0xa2313c: ldur            w0, [x1, #0x2b]
    // 0xa23140: DecompressPointer r0
    //     0xa23140: add             x0, x0, HEAP, lsl #32
    // 0xa23144: stur            x0, [fp, #-0x38]
    // 0xa23148: r1 = Instance_Color
    //     0xa23148: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa2314c: d0 = 0.700000
    //     0xa2314c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa23150: ldr             d0, [x17, #0xf48]
    // 0xa23154: r0 = withOpacity()
    //     0xa23154: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa23158: r16 = 14.000000
    //     0xa23158: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa2315c: ldr             x16, [x16, #0x1d8]
    // 0xa23160: stp             x16, x0, [SP]
    // 0xa23164: ldur            x1, [fp, #-0x38]
    // 0xa23168: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa23168: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa2316c: ldr             x4, [x4, #0x9b8]
    // 0xa23170: r0 = copyWith()
    //     0xa23170: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa23174: stur            x0, [fp, #-0x38]
    // 0xa23178: r0 = Text()
    //     0xa23178: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa2317c: mov             x1, x0
    // 0xa23180: ldur            x0, [fp, #-0x10]
    // 0xa23184: stur            x1, [fp, #-0x40]
    // 0xa23188: StoreField: r1->field_b = r0
    //     0xa23188: stur            w0, [x1, #0xb]
    // 0xa2318c: ldur            x0, [fp, #-0x38]
    // 0xa23190: StoreField: r1->field_13 = r0
    //     0xa23190: stur            w0, [x1, #0x13]
    // 0xa23194: r0 = Padding()
    //     0xa23194: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa23198: mov             x1, x0
    // 0xa2319c: r0 = Instance_EdgeInsets
    //     0xa2319c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xa231a0: ldr             x0, [x0, #0xa78]
    // 0xa231a4: stur            x1, [fp, #-0x10]
    // 0xa231a8: StoreField: r1->field_f = r0
    //     0xa231a8: stur            w0, [x1, #0xf]
    // 0xa231ac: ldur            x0, [fp, #-0x40]
    // 0xa231b0: StoreField: r1->field_b = r0
    //     0xa231b0: stur            w0, [x1, #0xb]
    // 0xa231b4: ldur            x0, [fp, #-8]
    // 0xa231b8: LoadField: r2 = r0->field_b
    //     0xa231b8: ldur            w2, [x0, #0xb]
    // 0xa231bc: DecompressPointer r2
    //     0xa231bc: add             x2, x2, HEAP, lsl #32
    // 0xa231c0: cmp             w2, NULL
    // 0xa231c4: b.eq            #0xa23368
    // 0xa231c8: LoadField: r0 = r2->field_13
    //     0xa231c8: ldur            w0, [x2, #0x13]
    // 0xa231cc: DecompressPointer r0
    //     0xa231cc: add             x0, x0, HEAP, lsl #32
    // 0xa231d0: r2 = LoadClassIdInstr(r0)
    //     0xa231d0: ldur            x2, [x0, #-1]
    //     0xa231d4: ubfx            x2, x2, #0xc, #0x14
    // 0xa231d8: r16 = "cod"
    //     0xa231d8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a28] "cod"
    //     0xa231dc: ldr             x16, [x16, #0xa28]
    // 0xa231e0: stp             x16, x0, [SP]
    // 0xa231e4: mov             x0, x2
    // 0xa231e8: mov             lr, x0
    // 0xa231ec: ldr             lr, [x21, lr, lsl #3]
    // 0xa231f0: blr             lr
    // 0xa231f4: tbnz            w0, #4, #0xa23204
    // 0xa231f8: r3 = Instance_IconData
    //     0xa231f8: add             x3, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0xa231fc: ldr             x3, [x3, #0x30]
    // 0xa23200: b               #0xa2320c
    // 0xa23204: r3 = Instance_IconData
    //     0xa23204: add             x3, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0xa23208: ldr             x3, [x3, #0x38]
    // 0xa2320c: ldur            x2, [fp, #-0x30]
    // 0xa23210: ldur            x0, [fp, #-0x10]
    // 0xa23214: ldur            x1, [fp, #-0x20]
    // 0xa23218: stur            x3, [fp, #-8]
    // 0xa2321c: r0 = of()
    //     0xa2321c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa23220: LoadField: r1 = r0->field_5b
    //     0xa23220: ldur            w1, [x0, #0x5b]
    // 0xa23224: DecompressPointer r1
    //     0xa23224: add             x1, x1, HEAP, lsl #32
    // 0xa23228: stur            x1, [fp, #-0x20]
    // 0xa2322c: r0 = Icon()
    //     0xa2322c: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xa23230: mov             x3, x0
    // 0xa23234: ldur            x0, [fp, #-8]
    // 0xa23238: stur            x3, [fp, #-0x38]
    // 0xa2323c: StoreField: r3->field_b = r0
    //     0xa2323c: stur            w0, [x3, #0xb]
    // 0xa23240: ldur            x0, [fp, #-0x20]
    // 0xa23244: StoreField: r3->field_23 = r0
    //     0xa23244: stur            w0, [x3, #0x23]
    // 0xa23248: r1 = Null
    //     0xa23248: mov             x1, NULL
    // 0xa2324c: r2 = 8
    //     0xa2324c: movz            x2, #0x8
    // 0xa23250: r0 = AllocateArray()
    //     0xa23250: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa23254: mov             x2, x0
    // 0xa23258: ldur            x0, [fp, #-0x30]
    // 0xa2325c: stur            x2, [fp, #-8]
    // 0xa23260: StoreField: r2->field_f = r0
    //     0xa23260: stur            w0, [x2, #0xf]
    // 0xa23264: ldur            x0, [fp, #-0x10]
    // 0xa23268: StoreField: r2->field_13 = r0
    //     0xa23268: stur            w0, [x2, #0x13]
    // 0xa2326c: r16 = Instance_Spacer
    //     0xa2326c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xa23270: ldr             x16, [x16, #0xf0]
    // 0xa23274: ArrayStore: r2[0] = r16  ; List_4
    //     0xa23274: stur            w16, [x2, #0x17]
    // 0xa23278: ldur            x0, [fp, #-0x38]
    // 0xa2327c: StoreField: r2->field_1b = r0
    //     0xa2327c: stur            w0, [x2, #0x1b]
    // 0xa23280: r1 = <Widget>
    //     0xa23280: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa23284: r0 = AllocateGrowableArray()
    //     0xa23284: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa23288: mov             x1, x0
    // 0xa2328c: ldur            x0, [fp, #-8]
    // 0xa23290: stur            x1, [fp, #-0x10]
    // 0xa23294: StoreField: r1->field_f = r0
    //     0xa23294: stur            w0, [x1, #0xf]
    // 0xa23298: r0 = 8
    //     0xa23298: movz            x0, #0x8
    // 0xa2329c: StoreField: r1->field_b = r0
    //     0xa2329c: stur            w0, [x1, #0xb]
    // 0xa232a0: r0 = Row()
    //     0xa232a0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa232a4: mov             x1, x0
    // 0xa232a8: r0 = Instance_Axis
    //     0xa232a8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa232ac: stur            x1, [fp, #-8]
    // 0xa232b0: StoreField: r1->field_f = r0
    //     0xa232b0: stur            w0, [x1, #0xf]
    // 0xa232b4: r0 = Instance_MainAxisAlignment
    //     0xa232b4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xa232b8: ldr             x0, [x0, #0xa8]
    // 0xa232bc: StoreField: r1->field_13 = r0
    //     0xa232bc: stur            w0, [x1, #0x13]
    // 0xa232c0: r0 = Instance_MainAxisSize
    //     0xa232c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa232c4: ldr             x0, [x0, #0xa10]
    // 0xa232c8: ArrayStore: r1[0] = r0  ; List_4
    //     0xa232c8: stur            w0, [x1, #0x17]
    // 0xa232cc: r0 = Instance_CrossAxisAlignment
    //     0xa232cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa232d0: ldr             x0, [x0, #0xa18]
    // 0xa232d4: StoreField: r1->field_1b = r0
    //     0xa232d4: stur            w0, [x1, #0x1b]
    // 0xa232d8: r0 = Instance_VerticalDirection
    //     0xa232d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa232dc: ldr             x0, [x0, #0xa20]
    // 0xa232e0: StoreField: r1->field_23 = r0
    //     0xa232e0: stur            w0, [x1, #0x23]
    // 0xa232e4: r0 = Instance_Clip
    //     0xa232e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa232e8: ldr             x0, [x0, #0x38]
    // 0xa232ec: StoreField: r1->field_2b = r0
    //     0xa232ec: stur            w0, [x1, #0x2b]
    // 0xa232f0: StoreField: r1->field_2f = rZR
    //     0xa232f0: stur            xzr, [x1, #0x2f]
    // 0xa232f4: ldur            x0, [fp, #-0x10]
    // 0xa232f8: StoreField: r1->field_b = r0
    //     0xa232f8: stur            w0, [x1, #0xb]
    // 0xa232fc: r0 = InkWell()
    //     0xa232fc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa23300: mov             x3, x0
    // 0xa23304: ldur            x0, [fp, #-8]
    // 0xa23308: stur            x3, [fp, #-0x10]
    // 0xa2330c: StoreField: r3->field_b = r0
    //     0xa2330c: stur            w0, [x3, #0xb]
    // 0xa23310: ldur            x2, [fp, #-0x28]
    // 0xa23314: r1 = Function '<anonymous closure>':.
    //     0xa23314: add             x1, PP, #0x56, lsl #12  ; [pp+0x56c10] AnonymousClosure: (0xa2336c), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::codPaymentMethodCard (0xa23068)
    //     0xa23318: ldr             x1, [x1, #0xc10]
    // 0xa2331c: r0 = AllocateClosure()
    //     0xa2331c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa23320: mov             x1, x0
    // 0xa23324: ldur            x0, [fp, #-0x10]
    // 0xa23328: StoreField: r0->field_f = r1
    //     0xa23328: stur            w1, [x0, #0xf]
    // 0xa2332c: r1 = true
    //     0xa2332c: add             x1, NULL, #0x20  ; true
    // 0xa23330: StoreField: r0->field_43 = r1
    //     0xa23330: stur            w1, [x0, #0x43]
    // 0xa23334: r2 = Instance_BoxShape
    //     0xa23334: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa23338: ldr             x2, [x2, #0x80]
    // 0xa2333c: StoreField: r0->field_47 = r2
    //     0xa2333c: stur            w2, [x0, #0x47]
    // 0xa23340: StoreField: r0->field_6f = r1
    //     0xa23340: stur            w1, [x0, #0x6f]
    // 0xa23344: r2 = false
    //     0xa23344: add             x2, NULL, #0x30  ; false
    // 0xa23348: StoreField: r0->field_73 = r2
    //     0xa23348: stur            w2, [x0, #0x73]
    // 0xa2334c: StoreField: r0->field_83 = r1
    //     0xa2334c: stur            w1, [x0, #0x83]
    // 0xa23350: StoreField: r0->field_7b = r2
    //     0xa23350: stur            w2, [x0, #0x7b]
    // 0xa23354: LeaveFrame
    //     0xa23354: mov             SP, fp
    //     0xa23358: ldp             fp, lr, [SP], #0x10
    // 0xa2335c: ret
    //     0xa2335c: ret             
    // 0xa23360: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa23360: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa23364: b               #0xa23098
    // 0xa23368: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa23368: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa2336c, size: 0x60
    // 0xa2336c: EnterFrame
    //     0xa2336c: stp             fp, lr, [SP, #-0x10]!
    //     0xa23370: mov             fp, SP
    // 0xa23374: AllocStack(0x8)
    //     0xa23374: sub             SP, SP, #8
    // 0xa23378: SetupParameters()
    //     0xa23378: ldr             x0, [fp, #0x10]
    //     0xa2337c: ldur            w2, [x0, #0x17]
    //     0xa23380: add             x2, x2, HEAP, lsl #32
    // 0xa23384: CheckStackOverflow
    //     0xa23384: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa23388: cmp             SP, x16
    //     0xa2338c: b.ls            #0xa233c4
    // 0xa23390: LoadField: r0 = r2->field_f
    //     0xa23390: ldur            w0, [x2, #0xf]
    // 0xa23394: DecompressPointer r0
    //     0xa23394: add             x0, x0, HEAP, lsl #32
    // 0xa23398: stur            x0, [fp, #-8]
    // 0xa2339c: r1 = Function '<anonymous closure>':.
    //     0xa2339c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56c18] AnonymousClosure: (0xa233cc), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::codPaymentMethodCard (0xa23068)
    //     0xa233a0: ldr             x1, [x1, #0xc18]
    // 0xa233a4: r0 = AllocateClosure()
    //     0xa233a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa233a8: ldur            x1, [fp, #-8]
    // 0xa233ac: mov             x2, x0
    // 0xa233b0: r0 = setState()
    //     0xa233b0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa233b4: r0 = Null
    //     0xa233b4: mov             x0, NULL
    // 0xa233b8: LeaveFrame
    //     0xa233b8: mov             SP, fp
    //     0xa233bc: ldp             fp, lr, [SP], #0x10
    // 0xa233c0: ret
    //     0xa233c0: ret             
    // 0xa233c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa233c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa233c8: b               #0xa23390
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa233cc, size: 0x180
    // 0xa233cc: EnterFrame
    //     0xa233cc: stp             fp, lr, [SP, #-0x10]!
    //     0xa233d0: mov             fp, SP
    // 0xa233d4: AllocStack(0x30)
    //     0xa233d4: sub             SP, SP, #0x30
    // 0xa233d8: SetupParameters()
    //     0xa233d8: ldr             x0, [fp, #0x10]
    //     0xa233dc: ldur            w1, [x0, #0x17]
    //     0xa233e0: add             x1, x1, HEAP, lsl #32
    //     0xa233e4: stur            x1, [fp, #-8]
    // 0xa233e8: CheckStackOverflow
    //     0xa233e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa233ec: cmp             SP, x16
    //     0xa233f0: b.ls            #0xa2353c
    // 0xa233f4: LoadField: r0 = r1->field_f
    //     0xa233f4: ldur            w0, [x1, #0xf]
    // 0xa233f8: DecompressPointer r0
    //     0xa233f8: add             x0, x0, HEAP, lsl #32
    // 0xa233fc: LoadField: r2 = r0->field_b
    //     0xa233fc: ldur            w2, [x0, #0xb]
    // 0xa23400: DecompressPointer r2
    //     0xa23400: add             x2, x2, HEAP, lsl #32
    // 0xa23404: cmp             w2, NULL
    // 0xa23408: b.eq            #0xa23544
    // 0xa2340c: LoadField: r3 = r1->field_13
    //     0xa2340c: ldur            w3, [x1, #0x13]
    // 0xa23410: DecompressPointer r3
    //     0xa23410: add             x3, x3, HEAP, lsl #32
    // 0xa23414: cmp             w3, NULL
    // 0xa23418: b.ne            #0xa23424
    // 0xa2341c: r0 = Null
    //     0xa2341c: mov             x0, NULL
    // 0xa23420: b               #0xa2342c
    // 0xa23424: LoadField: r0 = r3->field_7
    //     0xa23424: ldur            w0, [x3, #7]
    // 0xa23428: DecompressPointer r0
    //     0xa23428: add             x0, x0, HEAP, lsl #32
    // 0xa2342c: cmp             w0, NULL
    // 0xa23430: b.ne            #0xa23438
    // 0xa23434: r0 = ""
    //     0xa23434: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa23438: r4 = ""
    //     0xa23438: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa2343c: StoreField: r2->field_13 = r0
    //     0xa2343c: stur            w0, [x2, #0x13]
    //     0xa23440: ldurb           w16, [x2, #-1]
    //     0xa23444: ldurb           w17, [x0, #-1]
    //     0xa23448: and             x16, x17, x16, lsr #2
    //     0xa2344c: tst             x16, HEAP, lsr #32
    //     0xa23450: b.eq            #0xa23458
    //     0xa23454: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa23458: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa23458: ldur            w0, [x1, #0x17]
    // 0xa2345c: DecompressPointer r0
    //     0xa2345c: add             x0, x0, HEAP, lsl #32
    // 0xa23460: r5 = LoadInt32Instr(r0)
    //     0xa23460: sbfx            x5, x0, #1, #0x1f
    //     0xa23464: tbz             w0, #0, #0xa2346c
    //     0xa23468: ldur            x5, [x0, #7]
    // 0xa2346c: StoreField: r2->field_1b = r5
    //     0xa2346c: stur            x5, [x2, #0x1b]
    // 0xa23470: ArrayStore: r2[0] = r4  ; List_4
    //     0xa23470: stur            w4, [x2, #0x17]
    // 0xa23474: cmp             w3, NULL
    // 0xa23478: b.ne            #0xa23484
    // 0xa2347c: r3 = Null
    //     0xa2347c: mov             x3, NULL
    // 0xa23480: b               #0xa23490
    // 0xa23484: LoadField: r4 = r3->field_7
    //     0xa23484: ldur            w4, [x3, #7]
    // 0xa23488: DecompressPointer r4
    //     0xa23488: add             x4, x4, HEAP, lsl #32
    // 0xa2348c: mov             x3, x4
    // 0xa23490: LoadField: r4 = r2->field_f
    //     0xa23490: ldur            w4, [x2, #0xf]
    // 0xa23494: DecompressPointer r4
    //     0xa23494: add             x4, x4, HEAP, lsl #32
    // 0xa23498: stp             x3, x4, [SP, #0x18]
    // 0xa2349c: r16 = ""
    //     0xa2349c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa234a0: stp             x0, x16, [SP, #8]
    // 0xa234a4: r16 = true
    //     0xa234a4: add             x16, NULL, #0x20  ; true
    // 0xa234a8: str             x16, [SP]
    // 0xa234ac: r4 = 0
    //     0xa234ac: movz            x4, #0
    // 0xa234b0: ldr             x0, [SP, #0x20]
    // 0xa234b4: r16 = UnlinkedCall_0x613b5c
    //     0xa234b4: add             x16, PP, #0x56, lsl #12  ; [pp+0x56c20] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa234b8: add             x16, x16, #0xc20
    // 0xa234bc: ldp             x5, lr, [x16]
    // 0xa234c0: blr             lr
    // 0xa234c4: ldur            x0, [fp, #-8]
    // 0xa234c8: LoadField: r1 = r0->field_f
    //     0xa234c8: ldur            w1, [x0, #0xf]
    // 0xa234cc: DecompressPointer r1
    //     0xa234cc: add             x1, x1, HEAP, lsl #32
    // 0xa234d0: LoadField: r2 = r1->field_b
    //     0xa234d0: ldur            w2, [x1, #0xb]
    // 0xa234d4: DecompressPointer r2
    //     0xa234d4: add             x2, x2, HEAP, lsl #32
    // 0xa234d8: cmp             w2, NULL
    // 0xa234dc: b.eq            #0xa23548
    // 0xa234e0: LoadField: r1 = r0->field_13
    //     0xa234e0: ldur            w1, [x0, #0x13]
    // 0xa234e4: DecompressPointer r1
    //     0xa234e4: add             x1, x1, HEAP, lsl #32
    // 0xa234e8: cmp             w1, NULL
    // 0xa234ec: b.ne            #0xa234f8
    // 0xa234f0: r0 = Null
    //     0xa234f0: mov             x0, NULL
    // 0xa234f4: b               #0xa23500
    // 0xa234f8: LoadField: r0 = r1->field_7
    //     0xa234f8: ldur            w0, [x1, #7]
    // 0xa234fc: DecompressPointer r0
    //     0xa234fc: add             x0, x0, HEAP, lsl #32
    // 0xa23500: LoadField: r1 = r2->field_23
    //     0xa23500: ldur            w1, [x2, #0x23]
    // 0xa23504: DecompressPointer r1
    //     0xa23504: add             x1, x1, HEAP, lsl #32
    // 0xa23508: stp             x0, x1, [SP, #8]
    // 0xa2350c: r16 = ""
    //     0xa2350c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa23510: str             x16, [SP]
    // 0xa23514: r4 = 0
    //     0xa23514: movz            x4, #0
    // 0xa23518: ldr             x0, [SP, #0x10]
    // 0xa2351c: r16 = UnlinkedCall_0x613b5c
    //     0xa2351c: add             x16, PP, #0x56, lsl #12  ; [pp+0x56c30] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa23520: add             x16, x16, #0xc30
    // 0xa23524: ldp             x5, lr, [x16]
    // 0xa23528: blr             lr
    // 0xa2352c: r0 = Null
    //     0xa2352c: mov             x0, NULL
    // 0xa23530: LeaveFrame
    //     0xa23530: mov             SP, fp
    //     0xa23534: ldp             fp, lr, [SP], #0x10
    // 0xa23538: ret
    //     0xa23538: ret             
    // 0xa2353c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa2353c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa23540: b               #0xa233f4
    // 0xa23544: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa23544: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa23548: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa23548: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa2354c, size: 0x98
    // 0xa2354c: EnterFrame
    //     0xa2354c: stp             fp, lr, [SP, #-0x10]!
    //     0xa23550: mov             fp, SP
    // 0xa23554: AllocStack(0x18)
    //     0xa23554: sub             SP, SP, #0x18
    // 0xa23558: SetupParameters()
    //     0xa23558: ldr             x0, [fp, #0x10]
    //     0xa2355c: ldur            w1, [x0, #0x17]
    //     0xa23560: add             x1, x1, HEAP, lsl #32
    // 0xa23564: CheckStackOverflow
    //     0xa23564: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa23568: cmp             SP, x16
    //     0xa2356c: b.ls            #0xa235d8
    // 0xa23570: LoadField: r0 = r1->field_b
    //     0xa23570: ldur            w0, [x1, #0xb]
    // 0xa23574: DecompressPointer r0
    //     0xa23574: add             x0, x0, HEAP, lsl #32
    // 0xa23578: LoadField: r1 = r0->field_f
    //     0xa23578: ldur            w1, [x0, #0xf]
    // 0xa2357c: DecompressPointer r1
    //     0xa2357c: add             x1, x1, HEAP, lsl #32
    // 0xa23580: LoadField: r0 = r1->field_b
    //     0xa23580: ldur            w0, [x1, #0xb]
    // 0xa23584: DecompressPointer r0
    //     0xa23584: add             x0, x0, HEAP, lsl #32
    // 0xa23588: cmp             w0, NULL
    // 0xa2358c: b.eq            #0xa235e0
    // 0xa23590: LoadField: r1 = r0->field_23
    //     0xa23590: ldur            w1, [x0, #0x23]
    // 0xa23594: DecompressPointer r1
    //     0xa23594: add             x1, x1, HEAP, lsl #32
    // 0xa23598: r16 = "partial_cod_accordion_click"
    //     0xa23598: add             x16, PP, #0x54, lsl #12  ; [pp+0x545a0] "partial_cod_accordion_click"
    //     0xa2359c: ldr             x16, [x16, #0x5a0]
    // 0xa235a0: stp             x16, x1, [SP, #8]
    // 0xa235a4: r16 = "Partial-Cod Accordion Click"
    //     0xa235a4: add             x16, PP, #0x54, lsl #12  ; [pp+0x545a8] "Partial-Cod Accordion Click"
    //     0xa235a8: ldr             x16, [x16, #0x5a8]
    // 0xa235ac: str             x16, [SP]
    // 0xa235b0: r4 = 0
    //     0xa235b0: movz            x4, #0
    // 0xa235b4: ldr             x0, [SP, #0x10]
    // 0xa235b8: r16 = UnlinkedCall_0x613b5c
    //     0xa235b8: add             x16, PP, #0x56, lsl #12  ; [pp+0x56b90] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa235bc: add             x16, x16, #0xb90
    // 0xa235c0: ldp             x5, lr, [x16]
    // 0xa235c4: blr             lr
    // 0xa235c8: r0 = Null
    //     0xa235c8: mov             x0, NULL
    // 0xa235cc: LeaveFrame
    //     0xa235cc: mov             SP, fp
    //     0xa235d0: ldp             fp, lr, [SP], #0x10
    // 0xa235d4: ret
    //     0xa235d4: ret             
    // 0xa235d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa235d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa235dc: b               #0xa23570
    // 0xa235e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa235e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa235e4, size: 0x1cc
    // 0xa235e4: EnterFrame
    //     0xa235e4: stp             fp, lr, [SP, #-0x10]!
    //     0xa235e8: mov             fp, SP
    // 0xa235ec: AllocStack(0x8)
    //     0xa235ec: sub             SP, SP, #8
    // 0xa235f0: SetupParameters()
    //     0xa235f0: ldr             x0, [fp, #0x20]
    //     0xa235f4: ldur            w2, [x0, #0x17]
    //     0xa235f8: add             x2, x2, HEAP, lsl #32
    // 0xa235fc: CheckStackOverflow
    //     0xa235fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa23600: cmp             SP, x16
    //     0xa23604: b.ls            #0xa2379c
    // 0xa23608: LoadField: r0 = r2->field_b
    //     0xa23608: ldur            w0, [x2, #0xb]
    // 0xa2360c: DecompressPointer r0
    //     0xa2360c: add             x0, x0, HEAP, lsl #32
    // 0xa23610: LoadField: r3 = r0->field_f
    //     0xa23610: ldur            w3, [x0, #0xf]
    // 0xa23614: DecompressPointer r3
    //     0xa23614: add             x3, x3, HEAP, lsl #32
    // 0xa23618: LoadField: r4 = r3->field_b
    //     0xa23618: ldur            w4, [x3, #0xb]
    // 0xa2361c: DecompressPointer r4
    //     0xa2361c: add             x4, x4, HEAP, lsl #32
    // 0xa23620: cmp             w4, NULL
    // 0xa23624: b.eq            #0xa237a4
    // 0xa23628: LoadField: r0 = r4->field_b
    //     0xa23628: ldur            w0, [x4, #0xb]
    // 0xa2362c: DecompressPointer r0
    //     0xa2362c: add             x0, x0, HEAP, lsl #32
    // 0xa23630: LoadField: r1 = r0->field_b
    //     0xa23630: ldur            w1, [x0, #0xb]
    // 0xa23634: DecompressPointer r1
    //     0xa23634: add             x1, x1, HEAP, lsl #32
    // 0xa23638: cmp             w1, NULL
    // 0xa2363c: b.ne            #0xa2364c
    // 0xa23640: ldr             x6, [fp, #0x10]
    // 0xa23644: r5 = Null
    //     0xa23644: mov             x5, NULL
    // 0xa23648: b               #0xa236ac
    // 0xa2364c: LoadField: r5 = r1->field_27
    //     0xa2364c: ldur            w5, [x1, #0x27]
    // 0xa23650: DecompressPointer r5
    //     0xa23650: add             x5, x5, HEAP, lsl #32
    // 0xa23654: cmp             w5, NULL
    // 0xa23658: b.ne            #0xa23668
    // 0xa2365c: ldr             x6, [fp, #0x10]
    // 0xa23660: r0 = Null
    //     0xa23660: mov             x0, NULL
    // 0xa23664: b               #0xa236a8
    // 0xa23668: ldr             x6, [fp, #0x10]
    // 0xa2366c: LoadField: r0 = r5->field_b
    //     0xa2366c: ldur            w0, [x5, #0xb]
    // 0xa23670: r7 = LoadInt32Instr(r6)
    //     0xa23670: sbfx            x7, x6, #1, #0x1f
    //     0xa23674: tbz             w6, #0, #0xa2367c
    //     0xa23678: ldur            x7, [x6, #7]
    // 0xa2367c: r1 = LoadInt32Instr(r0)
    //     0xa2367c: sbfx            x1, x0, #1, #0x1f
    // 0xa23680: mov             x0, x1
    // 0xa23684: mov             x1, x7
    // 0xa23688: cmp             x1, x0
    // 0xa2368c: b.hs            #0xa237a8
    // 0xa23690: LoadField: r0 = r5->field_f
    //     0xa23690: ldur            w0, [x5, #0xf]
    // 0xa23694: DecompressPointer r0
    //     0xa23694: add             x0, x0, HEAP, lsl #32
    // 0xa23698: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xa23698: add             x16, x0, x7, lsl #2
    //     0xa2369c: ldur            w1, [x16, #0xf]
    // 0xa236a0: DecompressPointer r1
    //     0xa236a0: add             x1, x1, HEAP, lsl #32
    // 0xa236a4: mov             x0, x1
    // 0xa236a8: mov             x5, x0
    // 0xa236ac: LoadField: r0 = r4->field_b
    //     0xa236ac: ldur            w0, [x4, #0xb]
    // 0xa236b0: DecompressPointer r0
    //     0xa236b0: add             x0, x0, HEAP, lsl #32
    // 0xa236b4: LoadField: r1 = r0->field_b
    //     0xa236b4: ldur            w1, [x0, #0xb]
    // 0xa236b8: DecompressPointer r1
    //     0xa236b8: add             x1, x1, HEAP, lsl #32
    // 0xa236bc: cmp             w1, NULL
    // 0xa236c0: b.ne            #0xa236cc
    // 0xa236c4: r0 = Null
    //     0xa236c4: mov             x0, NULL
    // 0xa236c8: b               #0xa23740
    // 0xa236cc: LoadField: r0 = r1->field_1f
    //     0xa236cc: ldur            w0, [x1, #0x1f]
    // 0xa236d0: DecompressPointer r0
    //     0xa236d0: add             x0, x0, HEAP, lsl #32
    // 0xa236d4: cmp             w0, NULL
    // 0xa236d8: b.ne            #0xa236e4
    // 0xa236dc: r0 = Null
    //     0xa236dc: mov             x0, NULL
    // 0xa236e0: b               #0xa23740
    // 0xa236e4: LoadField: r4 = r0->field_7
    //     0xa236e4: ldur            w4, [x0, #7]
    // 0xa236e8: DecompressPointer r4
    //     0xa236e8: add             x4, x4, HEAP, lsl #32
    // 0xa236ec: cmp             w4, NULL
    // 0xa236f0: b.ne            #0xa236fc
    // 0xa236f4: r0 = Null
    //     0xa236f4: mov             x0, NULL
    // 0xa236f8: b               #0xa23740
    // 0xa236fc: LoadField: r0 = r2->field_f
    //     0xa236fc: ldur            w0, [x2, #0xf]
    // 0xa23700: DecompressPointer r0
    //     0xa23700: add             x0, x0, HEAP, lsl #32
    // 0xa23704: LoadField: r1 = r4->field_b
    //     0xa23704: ldur            w1, [x4, #0xb]
    // 0xa23708: r2 = LoadInt32Instr(r0)
    //     0xa23708: sbfx            x2, x0, #1, #0x1f
    //     0xa2370c: tbz             w0, #0, #0xa23714
    //     0xa23710: ldur            x2, [x0, #7]
    // 0xa23714: r0 = LoadInt32Instr(r1)
    //     0xa23714: sbfx            x0, x1, #1, #0x1f
    // 0xa23718: mov             x1, x2
    // 0xa2371c: cmp             x1, x0
    // 0xa23720: b.hs            #0xa237ac
    // 0xa23724: LoadField: r0 = r4->field_f
    //     0xa23724: ldur            w0, [x4, #0xf]
    // 0xa23728: DecompressPointer r0
    //     0xa23728: add             x0, x0, HEAP, lsl #32
    // 0xa2372c: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xa2372c: add             x16, x0, x2, lsl #2
    //     0xa23730: ldur            w1, [x16, #0xf]
    // 0xa23734: DecompressPointer r1
    //     0xa23734: add             x1, x1, HEAP, lsl #32
    // 0xa23738: LoadField: r0 = r1->field_7
    //     0xa23738: ldur            w0, [x1, #7]
    // 0xa2373c: DecompressPointer r0
    //     0xa2373c: add             x0, x0, HEAP, lsl #32
    // 0xa23740: cmp             w0, NULL
    // 0xa23744: b.ne            #0xa2374c
    // 0xa23748: r0 = ""
    //     0xa23748: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa2374c: r1 = LoadInt32Instr(r6)
    //     0xa2374c: sbfx            x1, x6, #1, #0x1f
    //     0xa23750: tbz             w6, #0, #0xa23758
    //     0xa23754: ldur            x1, [x6, #7]
    // 0xa23758: mov             x16, x1
    // 0xa2375c: mov             x1, x3
    // 0xa23760: mov             x3, x16
    // 0xa23764: mov             x2, x5
    // 0xa23768: ldr             x5, [fp, #0x18]
    // 0xa2376c: mov             x6, x0
    // 0xa23770: r0 = partialCodPaymentMethodCard()
    //     0xa23770: bl              #0xa237b0  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::partialCodPaymentMethodCard
    // 0xa23774: stur            x0, [fp, #-8]
    // 0xa23778: r0 = Padding()
    //     0xa23778: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa2377c: r1 = Instance_EdgeInsets
    //     0xa2377c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b00] Obj!EdgeInsets@d57cb1
    //     0xa23780: ldr             x1, [x1, #0xb00]
    // 0xa23784: StoreField: r0->field_f = r1
    //     0xa23784: stur            w1, [x0, #0xf]
    // 0xa23788: ldur            x1, [fp, #-8]
    // 0xa2378c: StoreField: r0->field_b = r1
    //     0xa2378c: stur            w1, [x0, #0xb]
    // 0xa23790: LeaveFrame
    //     0xa23790: mov             SP, fp
    //     0xa23794: ldp             fp, lr, [SP], #0x10
    // 0xa23798: ret
    //     0xa23798: ret             
    // 0xa2379c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa2379c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa237a0: b               #0xa23608
    // 0xa237a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa237a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa237a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa237a8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa237ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa237ac: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ partialCodPaymentMethodCard(/* No info */) {
    // ** addr: 0xa237b0, size: 0x3b4
    // 0xa237b0: EnterFrame
    //     0xa237b0: stp             fp, lr, [SP, #-0x10]!
    //     0xa237b4: mov             fp, SP
    // 0xa237b8: AllocStack(0x60)
    //     0xa237b8: sub             SP, SP, #0x60
    // 0xa237bc: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */)
    //     0xa237bc: mov             x0, x1
    //     0xa237c0: stur            x1, [fp, #-8]
    //     0xa237c4: mov             x1, x5
    //     0xa237c8: stur            x2, [fp, #-0x10]
    //     0xa237cc: stur            x3, [fp, #-0x18]
    //     0xa237d0: stur            x5, [fp, #-0x20]
    //     0xa237d4: stur            x6, [fp, #-0x28]
    // 0xa237d8: CheckStackOverflow
    //     0xa237d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa237dc: cmp             SP, x16
    //     0xa237e0: b.ls            #0xa23b54
    // 0xa237e4: r1 = 4
    //     0xa237e4: movz            x1, #0x4
    // 0xa237e8: r0 = AllocateContext()
    //     0xa237e8: bl              #0x16f6108  ; AllocateContextStub
    // 0xa237ec: mov             x3, x0
    // 0xa237f0: ldur            x2, [fp, #-8]
    // 0xa237f4: stur            x3, [fp, #-0x38]
    // 0xa237f8: StoreField: r3->field_f = r2
    //     0xa237f8: stur            w2, [x3, #0xf]
    // 0xa237fc: ldur            x4, [fp, #-0x10]
    // 0xa23800: StoreField: r3->field_13 = r4
    //     0xa23800: stur            w4, [x3, #0x13]
    // 0xa23804: ldur            x5, [fp, #-0x18]
    // 0xa23808: r0 = BoxInt64Instr(r5)
    //     0xa23808: sbfiz           x0, x5, #1, #0x1f
    //     0xa2380c: cmp             x5, x0, asr #1
    //     0xa23810: b.eq            #0xa2381c
    //     0xa23814: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa23818: stur            x5, [x0, #7]
    // 0xa2381c: ArrayStore: r3[0] = r0  ; List_4
    //     0xa2381c: stur            w0, [x3, #0x17]
    // 0xa23820: ldur            x0, [fp, #-0x28]
    // 0xa23824: StoreField: r3->field_1b = r0
    //     0xa23824: stur            w0, [x3, #0x1b]
    // 0xa23828: cmp             w4, NULL
    // 0xa2382c: b.ne            #0xa23838
    // 0xa23830: r1 = Null
    //     0xa23830: mov             x1, NULL
    // 0xa23834: b               #0xa23840
    // 0xa23838: LoadField: r1 = r4->field_7
    //     0xa23838: ldur            w1, [x4, #7]
    // 0xa2383c: DecompressPointer r1
    //     0xa2383c: add             x1, x1, HEAP, lsl #32
    // 0xa23840: cmp             w1, NULL
    // 0xa23844: b.ne            #0xa2384c
    // 0xa23848: r1 = ""
    //     0xa23848: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa2384c: stur            x1, [fp, #-0x30]
    // 0xa23850: r0 = CachedNetworkImage()
    //     0xa23850: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa23854: stur            x0, [fp, #-0x40]
    // 0xa23858: r16 = 48.000000
    //     0xa23858: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xa2385c: ldr             x16, [x16, #0xad8]
    // 0xa23860: r30 = 48.000000
    //     0xa23860: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xa23864: ldr             lr, [lr, #0xad8]
    // 0xa23868: stp             lr, x16, [SP]
    // 0xa2386c: mov             x1, x0
    // 0xa23870: ldur            x2, [fp, #-0x30]
    // 0xa23874: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0xa23874: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0xa23878: ldr             x4, [x4, #0x900]
    // 0xa2387c: r0 = CachedNetworkImage()
    //     0xa2387c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa23880: ldur            x0, [fp, #-0x10]
    // 0xa23884: cmp             w0, NULL
    // 0xa23888: b.ne            #0xa23894
    // 0xa2388c: r1 = Null
    //     0xa2388c: mov             x1, NULL
    // 0xa23890: b               #0xa2389c
    // 0xa23894: LoadField: r1 = r0->field_b
    //     0xa23894: ldur            w1, [x0, #0xb]
    // 0xa23898: DecompressPointer r1
    //     0xa23898: add             x1, x1, HEAP, lsl #32
    // 0xa2389c: cmp             w1, NULL
    // 0xa238a0: b.ne            #0xa238ac
    // 0xa238a4: r3 = ""
    //     0xa238a4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa238a8: b               #0xa238b0
    // 0xa238ac: mov             x3, x1
    // 0xa238b0: ldur            x2, [fp, #-8]
    // 0xa238b4: ldur            x1, [fp, #-0x20]
    // 0xa238b8: stur            x3, [fp, #-0x30]
    // 0xa238bc: r0 = of()
    //     0xa238bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa238c0: LoadField: r1 = r0->field_87
    //     0xa238c0: ldur            w1, [x0, #0x87]
    // 0xa238c4: DecompressPointer r1
    //     0xa238c4: add             x1, x1, HEAP, lsl #32
    // 0xa238c8: LoadField: r0 = r1->field_2b
    //     0xa238c8: ldur            w0, [x1, #0x2b]
    // 0xa238cc: DecompressPointer r0
    //     0xa238cc: add             x0, x0, HEAP, lsl #32
    // 0xa238d0: stur            x0, [fp, #-0x48]
    // 0xa238d4: r1 = Instance_Color
    //     0xa238d4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa238d8: d0 = 0.700000
    //     0xa238d8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa238dc: ldr             d0, [x17, #0xf48]
    // 0xa238e0: r0 = withOpacity()
    //     0xa238e0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa238e4: r16 = 14.000000
    //     0xa238e4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa238e8: ldr             x16, [x16, #0x1d8]
    // 0xa238ec: stp             x16, x0, [SP]
    // 0xa238f0: ldur            x1, [fp, #-0x48]
    // 0xa238f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa238f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa238f8: ldr             x4, [x4, #0x9b8]
    // 0xa238fc: r0 = copyWith()
    //     0xa238fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa23900: stur            x0, [fp, #-0x48]
    // 0xa23904: r0 = Text()
    //     0xa23904: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa23908: mov             x1, x0
    // 0xa2390c: ldur            x0, [fp, #-0x30]
    // 0xa23910: stur            x1, [fp, #-0x50]
    // 0xa23914: StoreField: r1->field_b = r0
    //     0xa23914: stur            w0, [x1, #0xb]
    // 0xa23918: ldur            x0, [fp, #-0x48]
    // 0xa2391c: StoreField: r1->field_13 = r0
    //     0xa2391c: stur            w0, [x1, #0x13]
    // 0xa23920: r0 = Padding()
    //     0xa23920: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa23924: mov             x1, x0
    // 0xa23928: r0 = Instance_EdgeInsets
    //     0xa23928: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xa2392c: ldr             x0, [x0, #0xa78]
    // 0xa23930: stur            x1, [fp, #-0x30]
    // 0xa23934: StoreField: r1->field_f = r0
    //     0xa23934: stur            w0, [x1, #0xf]
    // 0xa23938: ldur            x0, [fp, #-0x50]
    // 0xa2393c: StoreField: r1->field_b = r0
    //     0xa2393c: stur            w0, [x1, #0xb]
    // 0xa23940: ldur            x2, [fp, #-8]
    // 0xa23944: LoadField: r0 = r2->field_b
    //     0xa23944: ldur            w0, [x2, #0xb]
    // 0xa23948: DecompressPointer r0
    //     0xa23948: add             x0, x0, HEAP, lsl #32
    // 0xa2394c: cmp             w0, NULL
    // 0xa23950: b.eq            #0xa23b5c
    // 0xa23954: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa23954: ldur            w3, [x0, #0x17]
    // 0xa23958: DecompressPointer r3
    //     0xa23958: add             x3, x3, HEAP, lsl #32
    // 0xa2395c: ldur            x0, [fp, #-0x10]
    // 0xa23960: cmp             w0, NULL
    // 0xa23964: b.ne            #0xa23970
    // 0xa23968: r0 = Null
    //     0xa23968: mov             x0, NULL
    // 0xa2396c: b               #0xa2397c
    // 0xa23970: LoadField: r4 = r0->field_f
    //     0xa23970: ldur            w4, [x0, #0xf]
    // 0xa23974: DecompressPointer r4
    //     0xa23974: add             x4, x4, HEAP, lsl #32
    // 0xa23978: mov             x0, x4
    // 0xa2397c: r4 = LoadClassIdInstr(r3)
    //     0xa2397c: ldur            x4, [x3, #-1]
    //     0xa23980: ubfx            x4, x4, #0xc, #0x14
    // 0xa23984: stp             x0, x3, [SP]
    // 0xa23988: mov             x0, x4
    // 0xa2398c: mov             lr, x0
    // 0xa23990: ldr             lr, [x21, lr, lsl #3]
    // 0xa23994: blr             lr
    // 0xa23998: tbnz            w0, #4, #0xa239f8
    // 0xa2399c: ldur            x0, [fp, #-8]
    // 0xa239a0: ldur            x1, [fp, #-0x18]
    // 0xa239a4: LoadField: r2 = r0->field_b
    //     0xa239a4: ldur            w2, [x0, #0xb]
    // 0xa239a8: DecompressPointer r2
    //     0xa239a8: add             x2, x2, HEAP, lsl #32
    // 0xa239ac: cmp             w2, NULL
    // 0xa239b0: b.eq            #0xa23b60
    // 0xa239b4: LoadField: r0 = r2->field_1b
    //     0xa239b4: ldur            x0, [x2, #0x1b]
    // 0xa239b8: cmp             x0, x1
    // 0xa239bc: b.ne            #0xa239f8
    // 0xa239c0: ldur            x0, [fp, #-0x28]
    // 0xa239c4: LoadField: r1 = r2->field_13
    //     0xa239c4: ldur            w1, [x2, #0x13]
    // 0xa239c8: DecompressPointer r1
    //     0xa239c8: add             x1, x1, HEAP, lsl #32
    // 0xa239cc: r2 = LoadClassIdInstr(r0)
    //     0xa239cc: ldur            x2, [x0, #-1]
    //     0xa239d0: ubfx            x2, x2, #0xc, #0x14
    // 0xa239d4: stp             x1, x0, [SP]
    // 0xa239d8: mov             x0, x2
    // 0xa239dc: mov             lr, x0
    // 0xa239e0: ldr             lr, [x21, lr, lsl #3]
    // 0xa239e4: blr             lr
    // 0xa239e8: tbnz            w0, #4, #0xa239f8
    // 0xa239ec: r3 = Instance_IconData
    //     0xa239ec: add             x3, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0xa239f0: ldr             x3, [x3, #0x30]
    // 0xa239f4: b               #0xa23a00
    // 0xa239f8: r3 = Instance_IconData
    //     0xa239f8: add             x3, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0xa239fc: ldr             x3, [x3, #0x38]
    // 0xa23a00: ldur            x2, [fp, #-0x40]
    // 0xa23a04: ldur            x0, [fp, #-0x30]
    // 0xa23a08: ldur            x1, [fp, #-0x20]
    // 0xa23a0c: stur            x3, [fp, #-8]
    // 0xa23a10: r0 = of()
    //     0xa23a10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa23a14: LoadField: r1 = r0->field_5b
    //     0xa23a14: ldur            w1, [x0, #0x5b]
    // 0xa23a18: DecompressPointer r1
    //     0xa23a18: add             x1, x1, HEAP, lsl #32
    // 0xa23a1c: stur            x1, [fp, #-0x10]
    // 0xa23a20: r0 = Icon()
    //     0xa23a20: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xa23a24: mov             x3, x0
    // 0xa23a28: ldur            x0, [fp, #-8]
    // 0xa23a2c: stur            x3, [fp, #-0x20]
    // 0xa23a30: StoreField: r3->field_b = r0
    //     0xa23a30: stur            w0, [x3, #0xb]
    // 0xa23a34: ldur            x0, [fp, #-0x10]
    // 0xa23a38: StoreField: r3->field_23 = r0
    //     0xa23a38: stur            w0, [x3, #0x23]
    // 0xa23a3c: r1 = Null
    //     0xa23a3c: mov             x1, NULL
    // 0xa23a40: r2 = 8
    //     0xa23a40: movz            x2, #0x8
    // 0xa23a44: r0 = AllocateArray()
    //     0xa23a44: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa23a48: mov             x2, x0
    // 0xa23a4c: ldur            x0, [fp, #-0x40]
    // 0xa23a50: stur            x2, [fp, #-8]
    // 0xa23a54: StoreField: r2->field_f = r0
    //     0xa23a54: stur            w0, [x2, #0xf]
    // 0xa23a58: ldur            x0, [fp, #-0x30]
    // 0xa23a5c: StoreField: r2->field_13 = r0
    //     0xa23a5c: stur            w0, [x2, #0x13]
    // 0xa23a60: r16 = Instance_Spacer
    //     0xa23a60: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xa23a64: ldr             x16, [x16, #0xf0]
    // 0xa23a68: ArrayStore: r2[0] = r16  ; List_4
    //     0xa23a68: stur            w16, [x2, #0x17]
    // 0xa23a6c: ldur            x0, [fp, #-0x20]
    // 0xa23a70: StoreField: r2->field_1b = r0
    //     0xa23a70: stur            w0, [x2, #0x1b]
    // 0xa23a74: r1 = <Widget>
    //     0xa23a74: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa23a78: r0 = AllocateGrowableArray()
    //     0xa23a78: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa23a7c: mov             x1, x0
    // 0xa23a80: ldur            x0, [fp, #-8]
    // 0xa23a84: stur            x1, [fp, #-0x10]
    // 0xa23a88: StoreField: r1->field_f = r0
    //     0xa23a88: stur            w0, [x1, #0xf]
    // 0xa23a8c: r0 = 8
    //     0xa23a8c: movz            x0, #0x8
    // 0xa23a90: StoreField: r1->field_b = r0
    //     0xa23a90: stur            w0, [x1, #0xb]
    // 0xa23a94: r0 = Row()
    //     0xa23a94: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa23a98: mov             x1, x0
    // 0xa23a9c: r0 = Instance_Axis
    //     0xa23a9c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa23aa0: stur            x1, [fp, #-8]
    // 0xa23aa4: StoreField: r1->field_f = r0
    //     0xa23aa4: stur            w0, [x1, #0xf]
    // 0xa23aa8: r0 = Instance_MainAxisAlignment
    //     0xa23aa8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xa23aac: ldr             x0, [x0, #0xa8]
    // 0xa23ab0: StoreField: r1->field_13 = r0
    //     0xa23ab0: stur            w0, [x1, #0x13]
    // 0xa23ab4: r0 = Instance_MainAxisSize
    //     0xa23ab4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa23ab8: ldr             x0, [x0, #0xa10]
    // 0xa23abc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa23abc: stur            w0, [x1, #0x17]
    // 0xa23ac0: r0 = Instance_CrossAxisAlignment
    //     0xa23ac0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa23ac4: ldr             x0, [x0, #0xa18]
    // 0xa23ac8: StoreField: r1->field_1b = r0
    //     0xa23ac8: stur            w0, [x1, #0x1b]
    // 0xa23acc: r0 = Instance_VerticalDirection
    //     0xa23acc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa23ad0: ldr             x0, [x0, #0xa20]
    // 0xa23ad4: StoreField: r1->field_23 = r0
    //     0xa23ad4: stur            w0, [x1, #0x23]
    // 0xa23ad8: r0 = Instance_Clip
    //     0xa23ad8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa23adc: ldr             x0, [x0, #0x38]
    // 0xa23ae0: StoreField: r1->field_2b = r0
    //     0xa23ae0: stur            w0, [x1, #0x2b]
    // 0xa23ae4: StoreField: r1->field_2f = rZR
    //     0xa23ae4: stur            xzr, [x1, #0x2f]
    // 0xa23ae8: ldur            x0, [fp, #-0x10]
    // 0xa23aec: StoreField: r1->field_b = r0
    //     0xa23aec: stur            w0, [x1, #0xb]
    // 0xa23af0: r0 = InkWell()
    //     0xa23af0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa23af4: mov             x3, x0
    // 0xa23af8: ldur            x0, [fp, #-8]
    // 0xa23afc: stur            x3, [fp, #-0x10]
    // 0xa23b00: StoreField: r3->field_b = r0
    //     0xa23b00: stur            w0, [x3, #0xb]
    // 0xa23b04: ldur            x2, [fp, #-0x38]
    // 0xa23b08: r1 = Function '<anonymous closure>':.
    //     0xa23b08: add             x1, PP, #0x56, lsl #12  ; [pp+0x56ba0] AnonymousClosure: (0xa23b64), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::partialCodPaymentMethodCard (0xa237b0)
    //     0xa23b0c: ldr             x1, [x1, #0xba0]
    // 0xa23b10: r0 = AllocateClosure()
    //     0xa23b10: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa23b14: mov             x1, x0
    // 0xa23b18: ldur            x0, [fp, #-0x10]
    // 0xa23b1c: StoreField: r0->field_f = r1
    //     0xa23b1c: stur            w1, [x0, #0xf]
    // 0xa23b20: r1 = true
    //     0xa23b20: add             x1, NULL, #0x20  ; true
    // 0xa23b24: StoreField: r0->field_43 = r1
    //     0xa23b24: stur            w1, [x0, #0x43]
    // 0xa23b28: r2 = Instance_BoxShape
    //     0xa23b28: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa23b2c: ldr             x2, [x2, #0x80]
    // 0xa23b30: StoreField: r0->field_47 = r2
    //     0xa23b30: stur            w2, [x0, #0x47]
    // 0xa23b34: StoreField: r0->field_6f = r1
    //     0xa23b34: stur            w1, [x0, #0x6f]
    // 0xa23b38: r2 = false
    //     0xa23b38: add             x2, NULL, #0x30  ; false
    // 0xa23b3c: StoreField: r0->field_73 = r2
    //     0xa23b3c: stur            w2, [x0, #0x73]
    // 0xa23b40: StoreField: r0->field_83 = r1
    //     0xa23b40: stur            w1, [x0, #0x83]
    // 0xa23b44: StoreField: r0->field_7b = r2
    //     0xa23b44: stur            w2, [x0, #0x7b]
    // 0xa23b48: LeaveFrame
    //     0xa23b48: mov             SP, fp
    //     0xa23b4c: ldp             fp, lr, [SP], #0x10
    // 0xa23b50: ret
    //     0xa23b50: ret             
    // 0xa23b54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa23b54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa23b58: b               #0xa237e4
    // 0xa23b5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa23b5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa23b60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa23b60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa23b64, size: 0x60
    // 0xa23b64: EnterFrame
    //     0xa23b64: stp             fp, lr, [SP, #-0x10]!
    //     0xa23b68: mov             fp, SP
    // 0xa23b6c: AllocStack(0x8)
    //     0xa23b6c: sub             SP, SP, #8
    // 0xa23b70: SetupParameters()
    //     0xa23b70: ldr             x0, [fp, #0x10]
    //     0xa23b74: ldur            w2, [x0, #0x17]
    //     0xa23b78: add             x2, x2, HEAP, lsl #32
    // 0xa23b7c: CheckStackOverflow
    //     0xa23b7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa23b80: cmp             SP, x16
    //     0xa23b84: b.ls            #0xa23bbc
    // 0xa23b88: LoadField: r0 = r2->field_f
    //     0xa23b88: ldur            w0, [x2, #0xf]
    // 0xa23b8c: DecompressPointer r0
    //     0xa23b8c: add             x0, x0, HEAP, lsl #32
    // 0xa23b90: stur            x0, [fp, #-8]
    // 0xa23b94: r1 = Function '<anonymous closure>':.
    //     0xa23b94: add             x1, PP, #0x56, lsl #12  ; [pp+0x56ba8] AnonymousClosure: (0xa23bc4), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::partialCodPaymentMethodCard (0xa237b0)
    //     0xa23b98: ldr             x1, [x1, #0xba8]
    // 0xa23b9c: r0 = AllocateClosure()
    //     0xa23b9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa23ba0: ldur            x1, [fp, #-8]
    // 0xa23ba4: mov             x2, x0
    // 0xa23ba8: r0 = setState()
    //     0xa23ba8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa23bac: r0 = Null
    //     0xa23bac: mov             x0, NULL
    // 0xa23bb0: LeaveFrame
    //     0xa23bb0: mov             SP, fp
    //     0xa23bb4: ldp             fp, lr, [SP], #0x10
    // 0xa23bb8: ret
    //     0xa23bb8: ret             
    // 0xa23bbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa23bbc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa23bc0: b               #0xa23b88
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa23bc4, size: 0x1b4
    // 0xa23bc4: EnterFrame
    //     0xa23bc4: stp             fp, lr, [SP, #-0x10]!
    //     0xa23bc8: mov             fp, SP
    // 0xa23bcc: AllocStack(0x30)
    //     0xa23bcc: sub             SP, SP, #0x30
    // 0xa23bd0: SetupParameters()
    //     0xa23bd0: ldr             x0, [fp, #0x10]
    //     0xa23bd4: ldur            w1, [x0, #0x17]
    //     0xa23bd8: add             x1, x1, HEAP, lsl #32
    //     0xa23bdc: stur            x1, [fp, #-8]
    // 0xa23be0: CheckStackOverflow
    //     0xa23be0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa23be4: cmp             SP, x16
    //     0xa23be8: b.ls            #0xa23d68
    // 0xa23bec: LoadField: r0 = r1->field_f
    //     0xa23bec: ldur            w0, [x1, #0xf]
    // 0xa23bf0: DecompressPointer r0
    //     0xa23bf0: add             x0, x0, HEAP, lsl #32
    // 0xa23bf4: LoadField: r2 = r0->field_b
    //     0xa23bf4: ldur            w2, [x0, #0xb]
    // 0xa23bf8: DecompressPointer r2
    //     0xa23bf8: add             x2, x2, HEAP, lsl #32
    // 0xa23bfc: cmp             w2, NULL
    // 0xa23c00: b.eq            #0xa23d70
    // 0xa23c04: LoadField: r3 = r1->field_1b
    //     0xa23c04: ldur            w3, [x1, #0x1b]
    // 0xa23c08: DecompressPointer r3
    //     0xa23c08: add             x3, x3, HEAP, lsl #32
    // 0xa23c0c: mov             x0, x3
    // 0xa23c10: StoreField: r2->field_13 = r0
    //     0xa23c10: stur            w0, [x2, #0x13]
    //     0xa23c14: ldurb           w16, [x2, #-1]
    //     0xa23c18: ldurb           w17, [x0, #-1]
    //     0xa23c1c: and             x16, x17, x16, lsr #2
    //     0xa23c20: tst             x16, HEAP, lsr #32
    //     0xa23c24: b.eq            #0xa23c2c
    //     0xa23c28: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa23c2c: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xa23c2c: ldur            w4, [x1, #0x17]
    // 0xa23c30: DecompressPointer r4
    //     0xa23c30: add             x4, x4, HEAP, lsl #32
    // 0xa23c34: r0 = LoadInt32Instr(r4)
    //     0xa23c34: sbfx            x0, x4, #1, #0x1f
    //     0xa23c38: tbz             w4, #0, #0xa23c40
    //     0xa23c3c: ldur            x0, [x4, #7]
    // 0xa23c40: StoreField: r2->field_1b = r0
    //     0xa23c40: stur            x0, [x2, #0x1b]
    // 0xa23c44: LoadField: r5 = r1->field_13
    //     0xa23c44: ldur            w5, [x1, #0x13]
    // 0xa23c48: DecompressPointer r5
    //     0xa23c48: add             x5, x5, HEAP, lsl #32
    // 0xa23c4c: cmp             w5, NULL
    // 0xa23c50: b.ne            #0xa23c5c
    // 0xa23c54: r0 = Null
    //     0xa23c54: mov             x0, NULL
    // 0xa23c58: b               #0xa23c64
    // 0xa23c5c: LoadField: r0 = r5->field_f
    //     0xa23c5c: ldur            w0, [x5, #0xf]
    // 0xa23c60: DecompressPointer r0
    //     0xa23c60: add             x0, x0, HEAP, lsl #32
    // 0xa23c64: cmp             w0, NULL
    // 0xa23c68: b.ne            #0xa23c70
    // 0xa23c6c: r0 = ""
    //     0xa23c6c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa23c70: ArrayStore: r2[0] = r0  ; List_4
    //     0xa23c70: stur            w0, [x2, #0x17]
    //     0xa23c74: ldurb           w16, [x2, #-1]
    //     0xa23c78: ldurb           w17, [x0, #-1]
    //     0xa23c7c: and             x16, x17, x16, lsr #2
    //     0xa23c80: tst             x16, HEAP, lsr #32
    //     0xa23c84: b.eq            #0xa23c8c
    //     0xa23c88: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa23c8c: cmp             w5, NULL
    // 0xa23c90: b.ne            #0xa23c9c
    // 0xa23c94: r0 = Null
    //     0xa23c94: mov             x0, NULL
    // 0xa23c98: b               #0xa23ca4
    // 0xa23c9c: LoadField: r0 = r5->field_f
    //     0xa23c9c: ldur            w0, [x5, #0xf]
    // 0xa23ca0: DecompressPointer r0
    //     0xa23ca0: add             x0, x0, HEAP, lsl #32
    // 0xa23ca4: cmp             w0, NULL
    // 0xa23ca8: b.ne            #0xa23cb0
    // 0xa23cac: r0 = ""
    //     0xa23cac: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa23cb0: LoadField: r5 = r2->field_f
    //     0xa23cb0: ldur            w5, [x2, #0xf]
    // 0xa23cb4: DecompressPointer r5
    //     0xa23cb4: add             x5, x5, HEAP, lsl #32
    // 0xa23cb8: stp             x3, x5, [SP, #0x18]
    // 0xa23cbc: stp             x4, x0, [SP, #8]
    // 0xa23cc0: r16 = true
    //     0xa23cc0: add             x16, NULL, #0x20  ; true
    // 0xa23cc4: str             x16, [SP]
    // 0xa23cc8: r4 = 0
    //     0xa23cc8: movz            x4, #0
    // 0xa23ccc: ldr             x0, [SP, #0x20]
    // 0xa23cd0: r16 = UnlinkedCall_0x613b5c
    //     0xa23cd0: add             x16, PP, #0x56, lsl #12  ; [pp+0x56bb0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa23cd4: add             x16, x16, #0xbb0
    // 0xa23cd8: ldp             x5, lr, [x16]
    // 0xa23cdc: blr             lr
    // 0xa23ce0: ldur            x0, [fp, #-8]
    // 0xa23ce4: LoadField: r1 = r0->field_f
    //     0xa23ce4: ldur            w1, [x0, #0xf]
    // 0xa23ce8: DecompressPointer r1
    //     0xa23ce8: add             x1, x1, HEAP, lsl #32
    // 0xa23cec: LoadField: r2 = r1->field_b
    //     0xa23cec: ldur            w2, [x1, #0xb]
    // 0xa23cf0: DecompressPointer r2
    //     0xa23cf0: add             x2, x2, HEAP, lsl #32
    // 0xa23cf4: cmp             w2, NULL
    // 0xa23cf8: b.eq            #0xa23d74
    // 0xa23cfc: LoadField: r1 = r0->field_1b
    //     0xa23cfc: ldur            w1, [x0, #0x1b]
    // 0xa23d00: DecompressPointer r1
    //     0xa23d00: add             x1, x1, HEAP, lsl #32
    // 0xa23d04: LoadField: r3 = r0->field_13
    //     0xa23d04: ldur            w3, [x0, #0x13]
    // 0xa23d08: DecompressPointer r3
    //     0xa23d08: add             x3, x3, HEAP, lsl #32
    // 0xa23d0c: cmp             w3, NULL
    // 0xa23d10: b.ne            #0xa23d1c
    // 0xa23d14: r0 = Null
    //     0xa23d14: mov             x0, NULL
    // 0xa23d18: b               #0xa23d24
    // 0xa23d1c: LoadField: r0 = r3->field_f
    //     0xa23d1c: ldur            w0, [x3, #0xf]
    // 0xa23d20: DecompressPointer r0
    //     0xa23d20: add             x0, x0, HEAP, lsl #32
    // 0xa23d24: cmp             w0, NULL
    // 0xa23d28: b.ne            #0xa23d30
    // 0xa23d2c: r0 = ""
    //     0xa23d2c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa23d30: LoadField: r3 = r2->field_23
    //     0xa23d30: ldur            w3, [x2, #0x23]
    // 0xa23d34: DecompressPointer r3
    //     0xa23d34: add             x3, x3, HEAP, lsl #32
    // 0xa23d38: stp             x1, x3, [SP, #8]
    // 0xa23d3c: str             x0, [SP]
    // 0xa23d40: r4 = 0
    //     0xa23d40: movz            x4, #0
    // 0xa23d44: ldr             x0, [SP, #0x10]
    // 0xa23d48: r16 = UnlinkedCall_0x613b5c
    //     0xa23d48: add             x16, PP, #0x56, lsl #12  ; [pp+0x56bc0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa23d4c: add             x16, x16, #0xbc0
    // 0xa23d50: ldp             x5, lr, [x16]
    // 0xa23d54: blr             lr
    // 0xa23d58: r0 = Null
    //     0xa23d58: mov             x0, NULL
    // 0xa23d5c: LeaveFrame
    //     0xa23d5c: mov             SP, fp
    //     0xa23d60: ldp             fp, lr, [SP], #0x10
    // 0xa23d64: ret
    //     0xa23d64: ret             
    // 0xa23d68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa23d68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa23d6c: b               #0xa23bec
    // 0xa23d70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa23d70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa23d74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa23d74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa23d78, size: 0x84
    // 0xa23d78: EnterFrame
    //     0xa23d78: stp             fp, lr, [SP, #-0x10]!
    //     0xa23d7c: mov             fp, SP
    // 0xa23d80: AllocStack(0x8)
    //     0xa23d80: sub             SP, SP, #8
    // 0xa23d84: SetupParameters()
    //     0xa23d84: ldr             x0, [fp, #0x10]
    //     0xa23d88: ldur            w1, [x0, #0x17]
    //     0xa23d8c: add             x1, x1, HEAP, lsl #32
    // 0xa23d90: CheckStackOverflow
    //     0xa23d90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa23d94: cmp             SP, x16
    //     0xa23d98: b.ls            #0xa23df0
    // 0xa23d9c: LoadField: r0 = r1->field_b
    //     0xa23d9c: ldur            w0, [x1, #0xb]
    // 0xa23da0: DecompressPointer r0
    //     0xa23da0: add             x0, x0, HEAP, lsl #32
    // 0xa23da4: LoadField: r1 = r0->field_f
    //     0xa23da4: ldur            w1, [x0, #0xf]
    // 0xa23da8: DecompressPointer r1
    //     0xa23da8: add             x1, x1, HEAP, lsl #32
    // 0xa23dac: LoadField: r0 = r1->field_b
    //     0xa23dac: ldur            w0, [x1, #0xb]
    // 0xa23db0: DecompressPointer r0
    //     0xa23db0: add             x0, x0, HEAP, lsl #32
    // 0xa23db4: cmp             w0, NULL
    // 0xa23db8: b.eq            #0xa23df8
    // 0xa23dbc: LoadField: r1 = r0->field_27
    //     0xa23dbc: ldur            w1, [x0, #0x27]
    // 0xa23dc0: DecompressPointer r1
    //     0xa23dc0: add             x1, x1, HEAP, lsl #32
    // 0xa23dc4: str             x1, [SP]
    // 0xa23dc8: r4 = 0
    //     0xa23dc8: movz            x4, #0
    // 0xa23dcc: ldr             x0, [SP]
    // 0xa23dd0: r16 = UnlinkedCall_0x613b5c
    //     0xa23dd0: add             x16, PP, #0x56, lsl #12  ; [pp+0x56bd0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa23dd4: add             x16, x16, #0xbd0
    // 0xa23dd8: ldp             x5, lr, [x16]
    // 0xa23ddc: blr             lr
    // 0xa23de0: r0 = Null
    //     0xa23de0: mov             x0, NULL
    // 0xa23de4: LeaveFrame
    //     0xa23de4: mov             SP, fp
    //     0xa23de8: ldp             fp, lr, [SP], #0x10
    // 0xa23dec: ret
    //     0xa23dec: ret             
    // 0xa23df0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa23df0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa23df4: b               #0xa23d9c
    // 0xa23df8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa23df8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa23dfc, size: 0x1cc
    // 0xa23dfc: EnterFrame
    //     0xa23dfc: stp             fp, lr, [SP, #-0x10]!
    //     0xa23e00: mov             fp, SP
    // 0xa23e04: AllocStack(0x8)
    //     0xa23e04: sub             SP, SP, #8
    // 0xa23e08: SetupParameters()
    //     0xa23e08: ldr             x0, [fp, #0x20]
    //     0xa23e0c: ldur            w2, [x0, #0x17]
    //     0xa23e10: add             x2, x2, HEAP, lsl #32
    // 0xa23e14: CheckStackOverflow
    //     0xa23e14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa23e18: cmp             SP, x16
    //     0xa23e1c: b.ls            #0xa23fb4
    // 0xa23e20: LoadField: r0 = r2->field_b
    //     0xa23e20: ldur            w0, [x2, #0xb]
    // 0xa23e24: DecompressPointer r0
    //     0xa23e24: add             x0, x0, HEAP, lsl #32
    // 0xa23e28: LoadField: r3 = r0->field_f
    //     0xa23e28: ldur            w3, [x0, #0xf]
    // 0xa23e2c: DecompressPointer r3
    //     0xa23e2c: add             x3, x3, HEAP, lsl #32
    // 0xa23e30: LoadField: r4 = r3->field_b
    //     0xa23e30: ldur            w4, [x3, #0xb]
    // 0xa23e34: DecompressPointer r4
    //     0xa23e34: add             x4, x4, HEAP, lsl #32
    // 0xa23e38: cmp             w4, NULL
    // 0xa23e3c: b.eq            #0xa23fbc
    // 0xa23e40: LoadField: r0 = r4->field_b
    //     0xa23e40: ldur            w0, [x4, #0xb]
    // 0xa23e44: DecompressPointer r0
    //     0xa23e44: add             x0, x0, HEAP, lsl #32
    // 0xa23e48: LoadField: r1 = r0->field_b
    //     0xa23e48: ldur            w1, [x0, #0xb]
    // 0xa23e4c: DecompressPointer r1
    //     0xa23e4c: add             x1, x1, HEAP, lsl #32
    // 0xa23e50: cmp             w1, NULL
    // 0xa23e54: b.ne            #0xa23e64
    // 0xa23e58: ldr             x6, [fp, #0x10]
    // 0xa23e5c: r5 = Null
    //     0xa23e5c: mov             x5, NULL
    // 0xa23e60: b               #0xa23ec4
    // 0xa23e64: LoadField: r5 = r1->field_27
    //     0xa23e64: ldur            w5, [x1, #0x27]
    // 0xa23e68: DecompressPointer r5
    //     0xa23e68: add             x5, x5, HEAP, lsl #32
    // 0xa23e6c: cmp             w5, NULL
    // 0xa23e70: b.ne            #0xa23e80
    // 0xa23e74: ldr             x6, [fp, #0x10]
    // 0xa23e78: r0 = Null
    //     0xa23e78: mov             x0, NULL
    // 0xa23e7c: b               #0xa23ec0
    // 0xa23e80: ldr             x6, [fp, #0x10]
    // 0xa23e84: LoadField: r0 = r5->field_b
    //     0xa23e84: ldur            w0, [x5, #0xb]
    // 0xa23e88: r7 = LoadInt32Instr(r6)
    //     0xa23e88: sbfx            x7, x6, #1, #0x1f
    //     0xa23e8c: tbz             w6, #0, #0xa23e94
    //     0xa23e90: ldur            x7, [x6, #7]
    // 0xa23e94: r1 = LoadInt32Instr(r0)
    //     0xa23e94: sbfx            x1, x0, #1, #0x1f
    // 0xa23e98: mov             x0, x1
    // 0xa23e9c: mov             x1, x7
    // 0xa23ea0: cmp             x1, x0
    // 0xa23ea4: b.hs            #0xa23fc0
    // 0xa23ea8: LoadField: r0 = r5->field_f
    //     0xa23ea8: ldur            w0, [x5, #0xf]
    // 0xa23eac: DecompressPointer r0
    //     0xa23eac: add             x0, x0, HEAP, lsl #32
    // 0xa23eb0: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xa23eb0: add             x16, x0, x7, lsl #2
    //     0xa23eb4: ldur            w1, [x16, #0xf]
    // 0xa23eb8: DecompressPointer r1
    //     0xa23eb8: add             x1, x1, HEAP, lsl #32
    // 0xa23ebc: mov             x0, x1
    // 0xa23ec0: mov             x5, x0
    // 0xa23ec4: LoadField: r0 = r4->field_b
    //     0xa23ec4: ldur            w0, [x4, #0xb]
    // 0xa23ec8: DecompressPointer r0
    //     0xa23ec8: add             x0, x0, HEAP, lsl #32
    // 0xa23ecc: LoadField: r1 = r0->field_b
    //     0xa23ecc: ldur            w1, [x0, #0xb]
    // 0xa23ed0: DecompressPointer r1
    //     0xa23ed0: add             x1, x1, HEAP, lsl #32
    // 0xa23ed4: cmp             w1, NULL
    // 0xa23ed8: b.ne            #0xa23ee4
    // 0xa23edc: r0 = Null
    //     0xa23edc: mov             x0, NULL
    // 0xa23ee0: b               #0xa23f58
    // 0xa23ee4: LoadField: r0 = r1->field_1f
    //     0xa23ee4: ldur            w0, [x1, #0x1f]
    // 0xa23ee8: DecompressPointer r0
    //     0xa23ee8: add             x0, x0, HEAP, lsl #32
    // 0xa23eec: cmp             w0, NULL
    // 0xa23ef0: b.ne            #0xa23efc
    // 0xa23ef4: r0 = Null
    //     0xa23ef4: mov             x0, NULL
    // 0xa23ef8: b               #0xa23f58
    // 0xa23efc: LoadField: r4 = r0->field_7
    //     0xa23efc: ldur            w4, [x0, #7]
    // 0xa23f00: DecompressPointer r4
    //     0xa23f00: add             x4, x4, HEAP, lsl #32
    // 0xa23f04: cmp             w4, NULL
    // 0xa23f08: b.ne            #0xa23f14
    // 0xa23f0c: r0 = Null
    //     0xa23f0c: mov             x0, NULL
    // 0xa23f10: b               #0xa23f58
    // 0xa23f14: LoadField: r0 = r2->field_f
    //     0xa23f14: ldur            w0, [x2, #0xf]
    // 0xa23f18: DecompressPointer r0
    //     0xa23f18: add             x0, x0, HEAP, lsl #32
    // 0xa23f1c: LoadField: r1 = r4->field_b
    //     0xa23f1c: ldur            w1, [x4, #0xb]
    // 0xa23f20: r2 = LoadInt32Instr(r0)
    //     0xa23f20: sbfx            x2, x0, #1, #0x1f
    //     0xa23f24: tbz             w0, #0, #0xa23f2c
    //     0xa23f28: ldur            x2, [x0, #7]
    // 0xa23f2c: r0 = LoadInt32Instr(r1)
    //     0xa23f2c: sbfx            x0, x1, #1, #0x1f
    // 0xa23f30: mov             x1, x2
    // 0xa23f34: cmp             x1, x0
    // 0xa23f38: b.hs            #0xa23fc4
    // 0xa23f3c: LoadField: r0 = r4->field_f
    //     0xa23f3c: ldur            w0, [x4, #0xf]
    // 0xa23f40: DecompressPointer r0
    //     0xa23f40: add             x0, x0, HEAP, lsl #32
    // 0xa23f44: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xa23f44: add             x16, x0, x2, lsl #2
    //     0xa23f48: ldur            w1, [x16, #0xf]
    // 0xa23f4c: DecompressPointer r1
    //     0xa23f4c: add             x1, x1, HEAP, lsl #32
    // 0xa23f50: LoadField: r0 = r1->field_7
    //     0xa23f50: ldur            w0, [x1, #7]
    // 0xa23f54: DecompressPointer r0
    //     0xa23f54: add             x0, x0, HEAP, lsl #32
    // 0xa23f58: cmp             w0, NULL
    // 0xa23f5c: b.ne            #0xa23f64
    // 0xa23f60: r0 = ""
    //     0xa23f60: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa23f64: r1 = LoadInt32Instr(r6)
    //     0xa23f64: sbfx            x1, x6, #1, #0x1f
    //     0xa23f68: tbz             w6, #0, #0xa23f70
    //     0xa23f6c: ldur            x1, [x6, #7]
    // 0xa23f70: mov             x16, x1
    // 0xa23f74: mov             x1, x3
    // 0xa23f78: mov             x3, x16
    // 0xa23f7c: mov             x2, x5
    // 0xa23f80: ldr             x5, [fp, #0x18]
    // 0xa23f84: mov             x6, x0
    // 0xa23f88: r0 = onlinePaymentMethodCard()
    //     0xa23f88: bl              #0xa23fc8  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::onlinePaymentMethodCard
    // 0xa23f8c: stur            x0, [fp, #-8]
    // 0xa23f90: r0 = Padding()
    //     0xa23f90: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa23f94: r1 = Instance_EdgeInsets
    //     0xa23f94: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xa23f98: ldr             x1, [x1, #0x1f0]
    // 0xa23f9c: StoreField: r0->field_f = r1
    //     0xa23f9c: stur            w1, [x0, #0xf]
    // 0xa23fa0: ldur            x1, [fp, #-8]
    // 0xa23fa4: StoreField: r0->field_b = r1
    //     0xa23fa4: stur            w1, [x0, #0xb]
    // 0xa23fa8: LeaveFrame
    //     0xa23fa8: mov             SP, fp
    //     0xa23fac: ldp             fp, lr, [SP], #0x10
    // 0xa23fb0: ret
    //     0xa23fb0: ret             
    // 0xa23fb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa23fb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa23fb8: b               #0xa23e20
    // 0xa23fbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa23fbc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa23fc0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa23fc0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa23fc4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa23fc4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ onlinePaymentMethodCard(/* No info */) {
    // ** addr: 0xa23fc8, size: 0x3b4
    // 0xa23fc8: EnterFrame
    //     0xa23fc8: stp             fp, lr, [SP, #-0x10]!
    //     0xa23fcc: mov             fp, SP
    // 0xa23fd0: AllocStack(0x60)
    //     0xa23fd0: sub             SP, SP, #0x60
    // 0xa23fd4: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */)
    //     0xa23fd4: mov             x0, x1
    //     0xa23fd8: stur            x1, [fp, #-8]
    //     0xa23fdc: mov             x1, x5
    //     0xa23fe0: stur            x2, [fp, #-0x10]
    //     0xa23fe4: stur            x3, [fp, #-0x18]
    //     0xa23fe8: stur            x5, [fp, #-0x20]
    //     0xa23fec: stur            x6, [fp, #-0x28]
    // 0xa23ff0: CheckStackOverflow
    //     0xa23ff0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa23ff4: cmp             SP, x16
    //     0xa23ff8: b.ls            #0xa2436c
    // 0xa23ffc: r1 = 4
    //     0xa23ffc: movz            x1, #0x4
    // 0xa24000: r0 = AllocateContext()
    //     0xa24000: bl              #0x16f6108  ; AllocateContextStub
    // 0xa24004: mov             x3, x0
    // 0xa24008: ldur            x2, [fp, #-8]
    // 0xa2400c: stur            x3, [fp, #-0x38]
    // 0xa24010: StoreField: r3->field_f = r2
    //     0xa24010: stur            w2, [x3, #0xf]
    // 0xa24014: ldur            x4, [fp, #-0x10]
    // 0xa24018: StoreField: r3->field_13 = r4
    //     0xa24018: stur            w4, [x3, #0x13]
    // 0xa2401c: ldur            x5, [fp, #-0x18]
    // 0xa24020: r0 = BoxInt64Instr(r5)
    //     0xa24020: sbfiz           x0, x5, #1, #0x1f
    //     0xa24024: cmp             x5, x0, asr #1
    //     0xa24028: b.eq            #0xa24034
    //     0xa2402c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa24030: stur            x5, [x0, #7]
    // 0xa24034: ArrayStore: r3[0] = r0  ; List_4
    //     0xa24034: stur            w0, [x3, #0x17]
    // 0xa24038: ldur            x0, [fp, #-0x28]
    // 0xa2403c: StoreField: r3->field_1b = r0
    //     0xa2403c: stur            w0, [x3, #0x1b]
    // 0xa24040: cmp             w4, NULL
    // 0xa24044: b.ne            #0xa24050
    // 0xa24048: r1 = Null
    //     0xa24048: mov             x1, NULL
    // 0xa2404c: b               #0xa24058
    // 0xa24050: LoadField: r1 = r4->field_7
    //     0xa24050: ldur            w1, [x4, #7]
    // 0xa24054: DecompressPointer r1
    //     0xa24054: add             x1, x1, HEAP, lsl #32
    // 0xa24058: cmp             w1, NULL
    // 0xa2405c: b.ne            #0xa24064
    // 0xa24060: r1 = ""
    //     0xa24060: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa24064: stur            x1, [fp, #-0x30]
    // 0xa24068: r0 = CachedNetworkImage()
    //     0xa24068: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa2406c: stur            x0, [fp, #-0x40]
    // 0xa24070: r16 = 48.000000
    //     0xa24070: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xa24074: ldr             x16, [x16, #0xad8]
    // 0xa24078: r30 = 48.000000
    //     0xa24078: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xa2407c: ldr             lr, [lr, #0xad8]
    // 0xa24080: stp             lr, x16, [SP]
    // 0xa24084: mov             x1, x0
    // 0xa24088: ldur            x2, [fp, #-0x30]
    // 0xa2408c: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0xa2408c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0xa24090: ldr             x4, [x4, #0x900]
    // 0xa24094: r0 = CachedNetworkImage()
    //     0xa24094: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa24098: ldur            x0, [fp, #-0x10]
    // 0xa2409c: cmp             w0, NULL
    // 0xa240a0: b.ne            #0xa240ac
    // 0xa240a4: r1 = Null
    //     0xa240a4: mov             x1, NULL
    // 0xa240a8: b               #0xa240b4
    // 0xa240ac: LoadField: r1 = r0->field_b
    //     0xa240ac: ldur            w1, [x0, #0xb]
    // 0xa240b0: DecompressPointer r1
    //     0xa240b0: add             x1, x1, HEAP, lsl #32
    // 0xa240b4: cmp             w1, NULL
    // 0xa240b8: b.ne            #0xa240c4
    // 0xa240bc: r3 = ""
    //     0xa240bc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa240c0: b               #0xa240c8
    // 0xa240c4: mov             x3, x1
    // 0xa240c8: ldur            x2, [fp, #-8]
    // 0xa240cc: ldur            x1, [fp, #-0x20]
    // 0xa240d0: stur            x3, [fp, #-0x30]
    // 0xa240d4: r0 = of()
    //     0xa240d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa240d8: LoadField: r1 = r0->field_87
    //     0xa240d8: ldur            w1, [x0, #0x87]
    // 0xa240dc: DecompressPointer r1
    //     0xa240dc: add             x1, x1, HEAP, lsl #32
    // 0xa240e0: LoadField: r0 = r1->field_2b
    //     0xa240e0: ldur            w0, [x1, #0x2b]
    // 0xa240e4: DecompressPointer r0
    //     0xa240e4: add             x0, x0, HEAP, lsl #32
    // 0xa240e8: stur            x0, [fp, #-0x48]
    // 0xa240ec: r1 = Instance_Color
    //     0xa240ec: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa240f0: d0 = 0.700000
    //     0xa240f0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa240f4: ldr             d0, [x17, #0xf48]
    // 0xa240f8: r0 = withOpacity()
    //     0xa240f8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa240fc: r16 = 14.000000
    //     0xa240fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa24100: ldr             x16, [x16, #0x1d8]
    // 0xa24104: stp             x16, x0, [SP]
    // 0xa24108: ldur            x1, [fp, #-0x48]
    // 0xa2410c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa2410c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa24110: ldr             x4, [x4, #0x9b8]
    // 0xa24114: r0 = copyWith()
    //     0xa24114: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa24118: stur            x0, [fp, #-0x48]
    // 0xa2411c: r0 = Text()
    //     0xa2411c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa24120: mov             x1, x0
    // 0xa24124: ldur            x0, [fp, #-0x30]
    // 0xa24128: stur            x1, [fp, #-0x50]
    // 0xa2412c: StoreField: r1->field_b = r0
    //     0xa2412c: stur            w0, [x1, #0xb]
    // 0xa24130: ldur            x0, [fp, #-0x48]
    // 0xa24134: StoreField: r1->field_13 = r0
    //     0xa24134: stur            w0, [x1, #0x13]
    // 0xa24138: r0 = Padding()
    //     0xa24138: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa2413c: mov             x1, x0
    // 0xa24140: r0 = Instance_EdgeInsets
    //     0xa24140: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xa24144: ldr             x0, [x0, #0xa78]
    // 0xa24148: stur            x1, [fp, #-0x30]
    // 0xa2414c: StoreField: r1->field_f = r0
    //     0xa2414c: stur            w0, [x1, #0xf]
    // 0xa24150: ldur            x0, [fp, #-0x50]
    // 0xa24154: StoreField: r1->field_b = r0
    //     0xa24154: stur            w0, [x1, #0xb]
    // 0xa24158: ldur            x2, [fp, #-8]
    // 0xa2415c: LoadField: r0 = r2->field_b
    //     0xa2415c: ldur            w0, [x2, #0xb]
    // 0xa24160: DecompressPointer r0
    //     0xa24160: add             x0, x0, HEAP, lsl #32
    // 0xa24164: cmp             w0, NULL
    // 0xa24168: b.eq            #0xa24374
    // 0xa2416c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa2416c: ldur            w3, [x0, #0x17]
    // 0xa24170: DecompressPointer r3
    //     0xa24170: add             x3, x3, HEAP, lsl #32
    // 0xa24174: ldur            x0, [fp, #-0x10]
    // 0xa24178: cmp             w0, NULL
    // 0xa2417c: b.ne            #0xa24188
    // 0xa24180: r0 = Null
    //     0xa24180: mov             x0, NULL
    // 0xa24184: b               #0xa24194
    // 0xa24188: LoadField: r4 = r0->field_f
    //     0xa24188: ldur            w4, [x0, #0xf]
    // 0xa2418c: DecompressPointer r4
    //     0xa2418c: add             x4, x4, HEAP, lsl #32
    // 0xa24190: mov             x0, x4
    // 0xa24194: r4 = LoadClassIdInstr(r3)
    //     0xa24194: ldur            x4, [x3, #-1]
    //     0xa24198: ubfx            x4, x4, #0xc, #0x14
    // 0xa2419c: stp             x0, x3, [SP]
    // 0xa241a0: mov             x0, x4
    // 0xa241a4: mov             lr, x0
    // 0xa241a8: ldr             lr, [x21, lr, lsl #3]
    // 0xa241ac: blr             lr
    // 0xa241b0: tbnz            w0, #4, #0xa24210
    // 0xa241b4: ldur            x0, [fp, #-8]
    // 0xa241b8: ldur            x1, [fp, #-0x18]
    // 0xa241bc: LoadField: r2 = r0->field_b
    //     0xa241bc: ldur            w2, [x0, #0xb]
    // 0xa241c0: DecompressPointer r2
    //     0xa241c0: add             x2, x2, HEAP, lsl #32
    // 0xa241c4: cmp             w2, NULL
    // 0xa241c8: b.eq            #0xa24378
    // 0xa241cc: LoadField: r0 = r2->field_1b
    //     0xa241cc: ldur            x0, [x2, #0x1b]
    // 0xa241d0: cmp             x0, x1
    // 0xa241d4: b.ne            #0xa24210
    // 0xa241d8: ldur            x0, [fp, #-0x28]
    // 0xa241dc: LoadField: r1 = r2->field_13
    //     0xa241dc: ldur            w1, [x2, #0x13]
    // 0xa241e0: DecompressPointer r1
    //     0xa241e0: add             x1, x1, HEAP, lsl #32
    // 0xa241e4: r2 = LoadClassIdInstr(r0)
    //     0xa241e4: ldur            x2, [x0, #-1]
    //     0xa241e8: ubfx            x2, x2, #0xc, #0x14
    // 0xa241ec: stp             x1, x0, [SP]
    // 0xa241f0: mov             x0, x2
    // 0xa241f4: mov             lr, x0
    // 0xa241f8: ldr             lr, [x21, lr, lsl #3]
    // 0xa241fc: blr             lr
    // 0xa24200: tbnz            w0, #4, #0xa24210
    // 0xa24204: r3 = Instance_IconData
    //     0xa24204: add             x3, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0xa24208: ldr             x3, [x3, #0x30]
    // 0xa2420c: b               #0xa24218
    // 0xa24210: r3 = Instance_IconData
    //     0xa24210: add             x3, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0xa24214: ldr             x3, [x3, #0x38]
    // 0xa24218: ldur            x2, [fp, #-0x40]
    // 0xa2421c: ldur            x0, [fp, #-0x30]
    // 0xa24220: ldur            x1, [fp, #-0x20]
    // 0xa24224: stur            x3, [fp, #-8]
    // 0xa24228: r0 = of()
    //     0xa24228: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa2422c: LoadField: r1 = r0->field_5b
    //     0xa2422c: ldur            w1, [x0, #0x5b]
    // 0xa24230: DecompressPointer r1
    //     0xa24230: add             x1, x1, HEAP, lsl #32
    // 0xa24234: stur            x1, [fp, #-0x10]
    // 0xa24238: r0 = Icon()
    //     0xa24238: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xa2423c: mov             x3, x0
    // 0xa24240: ldur            x0, [fp, #-8]
    // 0xa24244: stur            x3, [fp, #-0x20]
    // 0xa24248: StoreField: r3->field_b = r0
    //     0xa24248: stur            w0, [x3, #0xb]
    // 0xa2424c: ldur            x0, [fp, #-0x10]
    // 0xa24250: StoreField: r3->field_23 = r0
    //     0xa24250: stur            w0, [x3, #0x23]
    // 0xa24254: r1 = Null
    //     0xa24254: mov             x1, NULL
    // 0xa24258: r2 = 8
    //     0xa24258: movz            x2, #0x8
    // 0xa2425c: r0 = AllocateArray()
    //     0xa2425c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa24260: mov             x2, x0
    // 0xa24264: ldur            x0, [fp, #-0x40]
    // 0xa24268: stur            x2, [fp, #-8]
    // 0xa2426c: StoreField: r2->field_f = r0
    //     0xa2426c: stur            w0, [x2, #0xf]
    // 0xa24270: ldur            x0, [fp, #-0x30]
    // 0xa24274: StoreField: r2->field_13 = r0
    //     0xa24274: stur            w0, [x2, #0x13]
    // 0xa24278: r16 = Instance_Spacer
    //     0xa24278: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xa2427c: ldr             x16, [x16, #0xf0]
    // 0xa24280: ArrayStore: r2[0] = r16  ; List_4
    //     0xa24280: stur            w16, [x2, #0x17]
    // 0xa24284: ldur            x0, [fp, #-0x20]
    // 0xa24288: StoreField: r2->field_1b = r0
    //     0xa24288: stur            w0, [x2, #0x1b]
    // 0xa2428c: r1 = <Widget>
    //     0xa2428c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa24290: r0 = AllocateGrowableArray()
    //     0xa24290: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa24294: mov             x1, x0
    // 0xa24298: ldur            x0, [fp, #-8]
    // 0xa2429c: stur            x1, [fp, #-0x10]
    // 0xa242a0: StoreField: r1->field_f = r0
    //     0xa242a0: stur            w0, [x1, #0xf]
    // 0xa242a4: r0 = 8
    //     0xa242a4: movz            x0, #0x8
    // 0xa242a8: StoreField: r1->field_b = r0
    //     0xa242a8: stur            w0, [x1, #0xb]
    // 0xa242ac: r0 = Row()
    //     0xa242ac: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa242b0: mov             x1, x0
    // 0xa242b4: r0 = Instance_Axis
    //     0xa242b4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa242b8: stur            x1, [fp, #-8]
    // 0xa242bc: StoreField: r1->field_f = r0
    //     0xa242bc: stur            w0, [x1, #0xf]
    // 0xa242c0: r0 = Instance_MainAxisAlignment
    //     0xa242c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xa242c4: ldr             x0, [x0, #0xa8]
    // 0xa242c8: StoreField: r1->field_13 = r0
    //     0xa242c8: stur            w0, [x1, #0x13]
    // 0xa242cc: r0 = Instance_MainAxisSize
    //     0xa242cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa242d0: ldr             x0, [x0, #0xa10]
    // 0xa242d4: ArrayStore: r1[0] = r0  ; List_4
    //     0xa242d4: stur            w0, [x1, #0x17]
    // 0xa242d8: r0 = Instance_CrossAxisAlignment
    //     0xa242d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa242dc: ldr             x0, [x0, #0xa18]
    // 0xa242e0: StoreField: r1->field_1b = r0
    //     0xa242e0: stur            w0, [x1, #0x1b]
    // 0xa242e4: r0 = Instance_VerticalDirection
    //     0xa242e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa242e8: ldr             x0, [x0, #0xa20]
    // 0xa242ec: StoreField: r1->field_23 = r0
    //     0xa242ec: stur            w0, [x1, #0x23]
    // 0xa242f0: r0 = Instance_Clip
    //     0xa242f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa242f4: ldr             x0, [x0, #0x38]
    // 0xa242f8: StoreField: r1->field_2b = r0
    //     0xa242f8: stur            w0, [x1, #0x2b]
    // 0xa242fc: StoreField: r1->field_2f = rZR
    //     0xa242fc: stur            xzr, [x1, #0x2f]
    // 0xa24300: ldur            x0, [fp, #-0x10]
    // 0xa24304: StoreField: r1->field_b = r0
    //     0xa24304: stur            w0, [x1, #0xb]
    // 0xa24308: r0 = InkWell()
    //     0xa24308: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa2430c: mov             x3, x0
    // 0xa24310: ldur            x0, [fp, #-8]
    // 0xa24314: stur            x3, [fp, #-0x10]
    // 0xa24318: StoreField: r3->field_b = r0
    //     0xa24318: stur            w0, [x3, #0xb]
    // 0xa2431c: ldur            x2, [fp, #-0x38]
    // 0xa24320: r1 = Function '<anonymous closure>':.
    //     0xa24320: add             x1, PP, #0x56, lsl #12  ; [pp+0x56be0] AnonymousClosure: (0xa2437c), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::onlinePaymentMethodCard (0xa23fc8)
    //     0xa24324: ldr             x1, [x1, #0xbe0]
    // 0xa24328: r0 = AllocateClosure()
    //     0xa24328: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa2432c: mov             x1, x0
    // 0xa24330: ldur            x0, [fp, #-0x10]
    // 0xa24334: StoreField: r0->field_f = r1
    //     0xa24334: stur            w1, [x0, #0xf]
    // 0xa24338: r1 = true
    //     0xa24338: add             x1, NULL, #0x20  ; true
    // 0xa2433c: StoreField: r0->field_43 = r1
    //     0xa2433c: stur            w1, [x0, #0x43]
    // 0xa24340: r2 = Instance_BoxShape
    //     0xa24340: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa24344: ldr             x2, [x2, #0x80]
    // 0xa24348: StoreField: r0->field_47 = r2
    //     0xa24348: stur            w2, [x0, #0x47]
    // 0xa2434c: StoreField: r0->field_6f = r1
    //     0xa2434c: stur            w1, [x0, #0x6f]
    // 0xa24350: r2 = false
    //     0xa24350: add             x2, NULL, #0x30  ; false
    // 0xa24354: StoreField: r0->field_73 = r2
    //     0xa24354: stur            w2, [x0, #0x73]
    // 0xa24358: StoreField: r0->field_83 = r1
    //     0xa24358: stur            w1, [x0, #0x83]
    // 0xa2435c: StoreField: r0->field_7b = r2
    //     0xa2435c: stur            w2, [x0, #0x7b]
    // 0xa24360: LeaveFrame
    //     0xa24360: mov             SP, fp
    //     0xa24364: ldp             fp, lr, [SP], #0x10
    // 0xa24368: ret
    //     0xa24368: ret             
    // 0xa2436c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa2436c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa24370: b               #0xa23ffc
    // 0xa24374: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa24374: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa24378: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa24378: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa2437c, size: 0x60
    // 0xa2437c: EnterFrame
    //     0xa2437c: stp             fp, lr, [SP, #-0x10]!
    //     0xa24380: mov             fp, SP
    // 0xa24384: AllocStack(0x8)
    //     0xa24384: sub             SP, SP, #8
    // 0xa24388: SetupParameters()
    //     0xa24388: ldr             x0, [fp, #0x10]
    //     0xa2438c: ldur            w2, [x0, #0x17]
    //     0xa24390: add             x2, x2, HEAP, lsl #32
    // 0xa24394: CheckStackOverflow
    //     0xa24394: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa24398: cmp             SP, x16
    //     0xa2439c: b.ls            #0xa243d4
    // 0xa243a0: LoadField: r0 = r2->field_f
    //     0xa243a0: ldur            w0, [x2, #0xf]
    // 0xa243a4: DecompressPointer r0
    //     0xa243a4: add             x0, x0, HEAP, lsl #32
    // 0xa243a8: stur            x0, [fp, #-8]
    // 0xa243ac: r1 = Function '<anonymous closure>':.
    //     0xa243ac: add             x1, PP, #0x56, lsl #12  ; [pp+0x56be8] AnonymousClosure: (0xa243dc), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::onlinePaymentMethodCard (0xa23fc8)
    //     0xa243b0: ldr             x1, [x1, #0xbe8]
    // 0xa243b4: r0 = AllocateClosure()
    //     0xa243b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa243b8: ldur            x1, [fp, #-8]
    // 0xa243bc: mov             x2, x0
    // 0xa243c0: r0 = setState()
    //     0xa243c0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa243c4: r0 = Null
    //     0xa243c4: mov             x0, NULL
    // 0xa243c8: LeaveFrame
    //     0xa243c8: mov             SP, fp
    //     0xa243cc: ldp             fp, lr, [SP], #0x10
    // 0xa243d0: ret
    //     0xa243d0: ret             
    // 0xa243d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa243d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa243d8: b               #0xa243a0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa243dc, size: 0x1b4
    // 0xa243dc: EnterFrame
    //     0xa243dc: stp             fp, lr, [SP, #-0x10]!
    //     0xa243e0: mov             fp, SP
    // 0xa243e4: AllocStack(0x30)
    //     0xa243e4: sub             SP, SP, #0x30
    // 0xa243e8: SetupParameters()
    //     0xa243e8: ldr             x0, [fp, #0x10]
    //     0xa243ec: ldur            w1, [x0, #0x17]
    //     0xa243f0: add             x1, x1, HEAP, lsl #32
    //     0xa243f4: stur            x1, [fp, #-8]
    // 0xa243f8: CheckStackOverflow
    //     0xa243f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa243fc: cmp             SP, x16
    //     0xa24400: b.ls            #0xa24580
    // 0xa24404: LoadField: r0 = r1->field_f
    //     0xa24404: ldur            w0, [x1, #0xf]
    // 0xa24408: DecompressPointer r0
    //     0xa24408: add             x0, x0, HEAP, lsl #32
    // 0xa2440c: LoadField: r2 = r0->field_b
    //     0xa2440c: ldur            w2, [x0, #0xb]
    // 0xa24410: DecompressPointer r2
    //     0xa24410: add             x2, x2, HEAP, lsl #32
    // 0xa24414: cmp             w2, NULL
    // 0xa24418: b.eq            #0xa24588
    // 0xa2441c: LoadField: r3 = r1->field_1b
    //     0xa2441c: ldur            w3, [x1, #0x1b]
    // 0xa24420: DecompressPointer r3
    //     0xa24420: add             x3, x3, HEAP, lsl #32
    // 0xa24424: mov             x0, x3
    // 0xa24428: StoreField: r2->field_13 = r0
    //     0xa24428: stur            w0, [x2, #0x13]
    //     0xa2442c: ldurb           w16, [x2, #-1]
    //     0xa24430: ldurb           w17, [x0, #-1]
    //     0xa24434: and             x16, x17, x16, lsr #2
    //     0xa24438: tst             x16, HEAP, lsr #32
    //     0xa2443c: b.eq            #0xa24444
    //     0xa24440: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa24444: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xa24444: ldur            w4, [x1, #0x17]
    // 0xa24448: DecompressPointer r4
    //     0xa24448: add             x4, x4, HEAP, lsl #32
    // 0xa2444c: r0 = LoadInt32Instr(r4)
    //     0xa2444c: sbfx            x0, x4, #1, #0x1f
    //     0xa24450: tbz             w4, #0, #0xa24458
    //     0xa24454: ldur            x0, [x4, #7]
    // 0xa24458: StoreField: r2->field_1b = r0
    //     0xa24458: stur            x0, [x2, #0x1b]
    // 0xa2445c: LoadField: r5 = r1->field_13
    //     0xa2445c: ldur            w5, [x1, #0x13]
    // 0xa24460: DecompressPointer r5
    //     0xa24460: add             x5, x5, HEAP, lsl #32
    // 0xa24464: cmp             w5, NULL
    // 0xa24468: b.ne            #0xa24474
    // 0xa2446c: r0 = Null
    //     0xa2446c: mov             x0, NULL
    // 0xa24470: b               #0xa2447c
    // 0xa24474: LoadField: r0 = r5->field_f
    //     0xa24474: ldur            w0, [x5, #0xf]
    // 0xa24478: DecompressPointer r0
    //     0xa24478: add             x0, x0, HEAP, lsl #32
    // 0xa2447c: cmp             w0, NULL
    // 0xa24480: b.ne            #0xa24488
    // 0xa24484: r0 = ""
    //     0xa24484: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa24488: ArrayStore: r2[0] = r0  ; List_4
    //     0xa24488: stur            w0, [x2, #0x17]
    //     0xa2448c: ldurb           w16, [x2, #-1]
    //     0xa24490: ldurb           w17, [x0, #-1]
    //     0xa24494: and             x16, x17, x16, lsr #2
    //     0xa24498: tst             x16, HEAP, lsr #32
    //     0xa2449c: b.eq            #0xa244a4
    //     0xa244a0: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa244a4: cmp             w5, NULL
    // 0xa244a8: b.ne            #0xa244b4
    // 0xa244ac: r0 = Null
    //     0xa244ac: mov             x0, NULL
    // 0xa244b0: b               #0xa244bc
    // 0xa244b4: LoadField: r0 = r5->field_f
    //     0xa244b4: ldur            w0, [x5, #0xf]
    // 0xa244b8: DecompressPointer r0
    //     0xa244b8: add             x0, x0, HEAP, lsl #32
    // 0xa244bc: cmp             w0, NULL
    // 0xa244c0: b.ne            #0xa244c8
    // 0xa244c4: r0 = ""
    //     0xa244c4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa244c8: LoadField: r5 = r2->field_f
    //     0xa244c8: ldur            w5, [x2, #0xf]
    // 0xa244cc: DecompressPointer r5
    //     0xa244cc: add             x5, x5, HEAP, lsl #32
    // 0xa244d0: stp             x3, x5, [SP, #0x18]
    // 0xa244d4: stp             x4, x0, [SP, #8]
    // 0xa244d8: r16 = true
    //     0xa244d8: add             x16, NULL, #0x20  ; true
    // 0xa244dc: str             x16, [SP]
    // 0xa244e0: r4 = 0
    //     0xa244e0: movz            x4, #0
    // 0xa244e4: ldr             x0, [SP, #0x20]
    // 0xa244e8: r16 = UnlinkedCall_0x613b5c
    //     0xa244e8: add             x16, PP, #0x56, lsl #12  ; [pp+0x56bf0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa244ec: add             x16, x16, #0xbf0
    // 0xa244f0: ldp             x5, lr, [x16]
    // 0xa244f4: blr             lr
    // 0xa244f8: ldur            x0, [fp, #-8]
    // 0xa244fc: LoadField: r1 = r0->field_f
    //     0xa244fc: ldur            w1, [x0, #0xf]
    // 0xa24500: DecompressPointer r1
    //     0xa24500: add             x1, x1, HEAP, lsl #32
    // 0xa24504: LoadField: r2 = r1->field_b
    //     0xa24504: ldur            w2, [x1, #0xb]
    // 0xa24508: DecompressPointer r2
    //     0xa24508: add             x2, x2, HEAP, lsl #32
    // 0xa2450c: cmp             w2, NULL
    // 0xa24510: b.eq            #0xa2458c
    // 0xa24514: LoadField: r1 = r0->field_1b
    //     0xa24514: ldur            w1, [x0, #0x1b]
    // 0xa24518: DecompressPointer r1
    //     0xa24518: add             x1, x1, HEAP, lsl #32
    // 0xa2451c: LoadField: r3 = r0->field_13
    //     0xa2451c: ldur            w3, [x0, #0x13]
    // 0xa24520: DecompressPointer r3
    //     0xa24520: add             x3, x3, HEAP, lsl #32
    // 0xa24524: cmp             w3, NULL
    // 0xa24528: b.ne            #0xa24534
    // 0xa2452c: r0 = Null
    //     0xa2452c: mov             x0, NULL
    // 0xa24530: b               #0xa2453c
    // 0xa24534: LoadField: r0 = r3->field_f
    //     0xa24534: ldur            w0, [x3, #0xf]
    // 0xa24538: DecompressPointer r0
    //     0xa24538: add             x0, x0, HEAP, lsl #32
    // 0xa2453c: cmp             w0, NULL
    // 0xa24540: b.ne            #0xa24548
    // 0xa24544: r0 = ""
    //     0xa24544: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa24548: LoadField: r3 = r2->field_23
    //     0xa24548: ldur            w3, [x2, #0x23]
    // 0xa2454c: DecompressPointer r3
    //     0xa2454c: add             x3, x3, HEAP, lsl #32
    // 0xa24550: stp             x1, x3, [SP, #8]
    // 0xa24554: str             x0, [SP]
    // 0xa24558: r4 = 0
    //     0xa24558: movz            x4, #0
    // 0xa2455c: ldr             x0, [SP, #0x10]
    // 0xa24560: r16 = UnlinkedCall_0x613b5c
    //     0xa24560: add             x16, PP, #0x56, lsl #12  ; [pp+0x56c00] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa24564: add             x16, x16, #0xc00
    // 0xa24568: ldp             x5, lr, [x16]
    // 0xa2456c: blr             lr
    // 0xa24570: r0 = Null
    //     0xa24570: mov             x0, NULL
    // 0xa24574: LeaveFrame
    //     0xa24574: mov             SP, fp
    //     0xa24578: ldp             fp, lr, [SP], #0x10
    // 0xa2457c: ret
    //     0xa2457c: ret             
    // 0xa24580: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa24580: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa24584: b               #0xa24404
    // 0xa24588: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa24588: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa2458c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa2458c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb4a564, size: 0x308
    // 0xb4a564: EnterFrame
    //     0xb4a564: stp             fp, lr, [SP, #-0x10]!
    //     0xb4a568: mov             fp, SP
    // 0xb4a56c: AllocStack(0x40)
    //     0xb4a56c: sub             SP, SP, #0x40
    // 0xb4a570: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb4a570: mov             x0, x1
    //     0xb4a574: stur            x1, [fp, #-8]
    //     0xb4a578: mov             x1, x2
    //     0xb4a57c: stur            x2, [fp, #-0x10]
    // 0xb4a580: CheckStackOverflow
    //     0xb4a580: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4a584: cmp             SP, x16
    //     0xb4a588: b.ls            #0xb4a860
    // 0xb4a58c: r1 = 1
    //     0xb4a58c: movz            x1, #0x1
    // 0xb4a590: r0 = AllocateContext()
    //     0xb4a590: bl              #0x16f6108  ; AllocateContextStub
    // 0xb4a594: mov             x2, x0
    // 0xb4a598: ldur            x0, [fp, #-8]
    // 0xb4a59c: stur            x2, [fp, #-0x18]
    // 0xb4a5a0: StoreField: r2->field_f = r0
    //     0xb4a5a0: stur            w0, [x2, #0xf]
    // 0xb4a5a4: ldur            x1, [fp, #-0x10]
    // 0xb4a5a8: r0 = of()
    //     0xb4a5a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4a5ac: LoadField: r1 = r0->field_87
    //     0xb4a5ac: ldur            w1, [x0, #0x87]
    // 0xb4a5b0: DecompressPointer r1
    //     0xb4a5b0: add             x1, x1, HEAP, lsl #32
    // 0xb4a5b4: LoadField: r0 = r1->field_7
    //     0xb4a5b4: ldur            w0, [x1, #7]
    // 0xb4a5b8: DecompressPointer r0
    //     0xb4a5b8: add             x0, x0, HEAP, lsl #32
    // 0xb4a5bc: r16 = Instance_Color
    //     0xb4a5bc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4a5c0: r30 = 14.000000
    //     0xb4a5c0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb4a5c4: ldr             lr, [lr, #0x1d8]
    // 0xb4a5c8: stp             lr, x16, [SP]
    // 0xb4a5cc: mov             x1, x0
    // 0xb4a5d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb4a5d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb4a5d4: ldr             x4, [x4, #0x9b8]
    // 0xb4a5d8: r0 = copyWith()
    //     0xb4a5d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4a5dc: stur            x0, [fp, #-0x10]
    // 0xb4a5e0: r0 = Text()
    //     0xb4a5e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4a5e4: mov             x1, x0
    // 0xb4a5e8: r0 = "Payment Method"
    //     0xb4a5e8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34110] "Payment Method"
    //     0xb4a5ec: ldr             x0, [x0, #0x110]
    // 0xb4a5f0: stur            x1, [fp, #-0x20]
    // 0xb4a5f4: StoreField: r1->field_b = r0
    //     0xb4a5f4: stur            w0, [x1, #0xb]
    // 0xb4a5f8: ldur            x0, [fp, #-0x10]
    // 0xb4a5fc: StoreField: r1->field_13 = r0
    //     0xb4a5fc: stur            w0, [x1, #0x13]
    // 0xb4a600: r0 = SvgPicture()
    //     0xb4a600: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb4a604: stur            x0, [fp, #-0x10]
    // 0xb4a608: r16 = "return order"
    //     0xb4a608: add             x16, PP, #0x38, lsl #12  ; [pp+0x38c78] "return order"
    //     0xb4a60c: ldr             x16, [x16, #0xc78]
    // 0xb4a610: r30 = Instance_BoxFit
    //     0xb4a610: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb4a614: ldr             lr, [lr, #0xb18]
    // 0xb4a618: stp             lr, x16, [SP]
    // 0xb4a61c: mov             x1, x0
    // 0xb4a620: r2 = "assets/images/secure_icon.svg"
    //     0xb4a620: add             x2, PP, #0x38, lsl #12  ; [pp+0x38c80] "assets/images/secure_icon.svg"
    //     0xb4a624: ldr             x2, [x2, #0xc80]
    // 0xb4a628: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xb4a628: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xb4a62c: ldr             x4, [x4, #0xb28]
    // 0xb4a630: r0 = SvgPicture.asset()
    //     0xb4a630: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb4a634: r1 = Null
    //     0xb4a634: mov             x1, NULL
    // 0xb4a638: r2 = 4
    //     0xb4a638: movz            x2, #0x4
    // 0xb4a63c: r0 = AllocateArray()
    //     0xb4a63c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4a640: mov             x2, x0
    // 0xb4a644: ldur            x0, [fp, #-0x20]
    // 0xb4a648: stur            x2, [fp, #-0x28]
    // 0xb4a64c: StoreField: r2->field_f = r0
    //     0xb4a64c: stur            w0, [x2, #0xf]
    // 0xb4a650: ldur            x0, [fp, #-0x10]
    // 0xb4a654: StoreField: r2->field_13 = r0
    //     0xb4a654: stur            w0, [x2, #0x13]
    // 0xb4a658: r1 = <Widget>
    //     0xb4a658: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4a65c: r0 = AllocateGrowableArray()
    //     0xb4a65c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4a660: mov             x1, x0
    // 0xb4a664: ldur            x0, [fp, #-0x28]
    // 0xb4a668: stur            x1, [fp, #-0x10]
    // 0xb4a66c: StoreField: r1->field_f = r0
    //     0xb4a66c: stur            w0, [x1, #0xf]
    // 0xb4a670: r0 = 4
    //     0xb4a670: movz            x0, #0x4
    // 0xb4a674: StoreField: r1->field_b = r0
    //     0xb4a674: stur            w0, [x1, #0xb]
    // 0xb4a678: r0 = Row()
    //     0xb4a678: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb4a67c: mov             x1, x0
    // 0xb4a680: r0 = Instance_Axis
    //     0xb4a680: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4a684: stur            x1, [fp, #-0x20]
    // 0xb4a688: StoreField: r1->field_f = r0
    //     0xb4a688: stur            w0, [x1, #0xf]
    // 0xb4a68c: r0 = Instance_MainAxisAlignment
    //     0xb4a68c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb4a690: ldr             x0, [x0, #0xa8]
    // 0xb4a694: StoreField: r1->field_13 = r0
    //     0xb4a694: stur            w0, [x1, #0x13]
    // 0xb4a698: r0 = Instance_MainAxisSize
    //     0xb4a698: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4a69c: ldr             x0, [x0, #0xa10]
    // 0xb4a6a0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb4a6a0: stur            w0, [x1, #0x17]
    // 0xb4a6a4: r2 = Instance_CrossAxisAlignment
    //     0xb4a6a4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4a6a8: ldr             x2, [x2, #0xa18]
    // 0xb4a6ac: StoreField: r1->field_1b = r2
    //     0xb4a6ac: stur            w2, [x1, #0x1b]
    // 0xb4a6b0: r3 = Instance_VerticalDirection
    //     0xb4a6b0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4a6b4: ldr             x3, [x3, #0xa20]
    // 0xb4a6b8: StoreField: r1->field_23 = r3
    //     0xb4a6b8: stur            w3, [x1, #0x23]
    // 0xb4a6bc: r4 = Instance_Clip
    //     0xb4a6bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4a6c0: ldr             x4, [x4, #0x38]
    // 0xb4a6c4: StoreField: r1->field_2b = r4
    //     0xb4a6c4: stur            w4, [x1, #0x2b]
    // 0xb4a6c8: StoreField: r1->field_2f = rZR
    //     0xb4a6c8: stur            xzr, [x1, #0x2f]
    // 0xb4a6cc: ldur            x5, [fp, #-0x10]
    // 0xb4a6d0: StoreField: r1->field_b = r5
    //     0xb4a6d0: stur            w5, [x1, #0xb]
    // 0xb4a6d4: r0 = Padding()
    //     0xb4a6d4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4a6d8: mov             x3, x0
    // 0xb4a6dc: r0 = Instance_EdgeInsets
    //     0xb4a6dc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb4a6e0: ldr             x0, [x0, #0x1f0]
    // 0xb4a6e4: stur            x3, [fp, #-0x10]
    // 0xb4a6e8: StoreField: r3->field_f = r0
    //     0xb4a6e8: stur            w0, [x3, #0xf]
    // 0xb4a6ec: ldur            x0, [fp, #-0x20]
    // 0xb4a6f0: StoreField: r3->field_b = r0
    //     0xb4a6f0: stur            w0, [x3, #0xb]
    // 0xb4a6f4: ldur            x0, [fp, #-8]
    // 0xb4a6f8: LoadField: r1 = r0->field_b
    //     0xb4a6f8: ldur            w1, [x0, #0xb]
    // 0xb4a6fc: DecompressPointer r1
    //     0xb4a6fc: add             x1, x1, HEAP, lsl #32
    // 0xb4a700: cmp             w1, NULL
    // 0xb4a704: b.eq            #0xb4a868
    // 0xb4a708: LoadField: r0 = r1->field_b
    //     0xb4a708: ldur            w0, [x1, #0xb]
    // 0xb4a70c: DecompressPointer r0
    //     0xb4a70c: add             x0, x0, HEAP, lsl #32
    // 0xb4a710: LoadField: r1 = r0->field_b
    //     0xb4a710: ldur            w1, [x0, #0xb]
    // 0xb4a714: DecompressPointer r1
    //     0xb4a714: add             x1, x1, HEAP, lsl #32
    // 0xb4a718: cmp             w1, NULL
    // 0xb4a71c: b.ne            #0xb4a728
    // 0xb4a720: r0 = Null
    //     0xb4a720: mov             x0, NULL
    // 0xb4a724: b               #0xb4a75c
    // 0xb4a728: LoadField: r0 = r1->field_1f
    //     0xb4a728: ldur            w0, [x1, #0x1f]
    // 0xb4a72c: DecompressPointer r0
    //     0xb4a72c: add             x0, x0, HEAP, lsl #32
    // 0xb4a730: cmp             w0, NULL
    // 0xb4a734: b.ne            #0xb4a740
    // 0xb4a738: r0 = Null
    //     0xb4a738: mov             x0, NULL
    // 0xb4a73c: b               #0xb4a75c
    // 0xb4a740: LoadField: r1 = r0->field_7
    //     0xb4a740: ldur            w1, [x0, #7]
    // 0xb4a744: DecompressPointer r1
    //     0xb4a744: add             x1, x1, HEAP, lsl #32
    // 0xb4a748: cmp             w1, NULL
    // 0xb4a74c: b.ne            #0xb4a758
    // 0xb4a750: r0 = Null
    //     0xb4a750: mov             x0, NULL
    // 0xb4a754: b               #0xb4a75c
    // 0xb4a758: LoadField: r0 = r1->field_b
    //     0xb4a758: ldur            w0, [x1, #0xb]
    // 0xb4a75c: ldur            x2, [fp, #-0x18]
    // 0xb4a760: stur            x0, [fp, #-8]
    // 0xb4a764: r1 = Function '<anonymous closure>':.
    //     0xb4a764: add             x1, PP, #0x56, lsl #12  ; [pp+0x56b58] AnonymousClosure: (0xa217f0), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::build (0xb4a564)
    //     0xb4a768: ldr             x1, [x1, #0xb58]
    // 0xb4a76c: r0 = AllocateClosure()
    //     0xb4a76c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4a770: stur            x0, [fp, #-0x18]
    // 0xb4a774: r0 = ListView()
    //     0xb4a774: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb4a778: stur            x0, [fp, #-0x20]
    // 0xb4a77c: r16 = Instance_EdgeInsets
    //     0xb4a77c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb4a780: ldr             x16, [x16, #0x668]
    // 0xb4a784: r30 = true
    //     0xb4a784: add             lr, NULL, #0x20  ; true
    // 0xb4a788: stp             lr, x16, [SP, #8]
    // 0xb4a78c: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb4a78c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb4a790: ldr             x16, [x16, #0x1c8]
    // 0xb4a794: str             x16, [SP]
    // 0xb4a798: mov             x1, x0
    // 0xb4a79c: ldur            x2, [fp, #-0x18]
    // 0xb4a7a0: ldur            x3, [fp, #-8]
    // 0xb4a7a4: r4 = const [0, 0x6, 0x3, 0x3, padding, 0x3, physics, 0x5, shrinkWrap, 0x4, null]
    //     0xb4a7a4: add             x4, PP, #0x54, lsl #12  ; [pp+0x544c0] List(11) [0, 0x6, 0x3, 0x3, "padding", 0x3, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0xb4a7a8: ldr             x4, [x4, #0x4c0]
    // 0xb4a7ac: r0 = ListView.builder()
    //     0xb4a7ac: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb4a7b0: r1 = Null
    //     0xb4a7b0: mov             x1, NULL
    // 0xb4a7b4: r2 = 6
    //     0xb4a7b4: movz            x2, #0x6
    // 0xb4a7b8: r0 = AllocateArray()
    //     0xb4a7b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4a7bc: mov             x2, x0
    // 0xb4a7c0: ldur            x0, [fp, #-0x10]
    // 0xb4a7c4: stur            x2, [fp, #-8]
    // 0xb4a7c8: StoreField: r2->field_f = r0
    //     0xb4a7c8: stur            w0, [x2, #0xf]
    // 0xb4a7cc: r16 = Instance_SizedBox
    //     0xb4a7cc: add             x16, PP, #0x54, lsl #12  ; [pp+0x544c8] Obj!SizedBox@d67f61
    //     0xb4a7d0: ldr             x16, [x16, #0x4c8]
    // 0xb4a7d4: StoreField: r2->field_13 = r16
    //     0xb4a7d4: stur            w16, [x2, #0x13]
    // 0xb4a7d8: ldur            x0, [fp, #-0x20]
    // 0xb4a7dc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4a7dc: stur            w0, [x2, #0x17]
    // 0xb4a7e0: r1 = <Widget>
    //     0xb4a7e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4a7e4: r0 = AllocateGrowableArray()
    //     0xb4a7e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4a7e8: mov             x1, x0
    // 0xb4a7ec: ldur            x0, [fp, #-8]
    // 0xb4a7f0: stur            x1, [fp, #-0x10]
    // 0xb4a7f4: StoreField: r1->field_f = r0
    //     0xb4a7f4: stur            w0, [x1, #0xf]
    // 0xb4a7f8: r0 = 6
    //     0xb4a7f8: movz            x0, #0x6
    // 0xb4a7fc: StoreField: r1->field_b = r0
    //     0xb4a7fc: stur            w0, [x1, #0xb]
    // 0xb4a800: r0 = Column()
    //     0xb4a800: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb4a804: r1 = Instance_Axis
    //     0xb4a804: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb4a808: StoreField: r0->field_f = r1
    //     0xb4a808: stur            w1, [x0, #0xf]
    // 0xb4a80c: r1 = Instance_MainAxisAlignment
    //     0xb4a80c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb4a810: ldr             x1, [x1, #0xa08]
    // 0xb4a814: StoreField: r0->field_13 = r1
    //     0xb4a814: stur            w1, [x0, #0x13]
    // 0xb4a818: r1 = Instance_MainAxisSize
    //     0xb4a818: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4a81c: ldr             x1, [x1, #0xa10]
    // 0xb4a820: ArrayStore: r0[0] = r1  ; List_4
    //     0xb4a820: stur            w1, [x0, #0x17]
    // 0xb4a824: r1 = Instance_CrossAxisAlignment
    //     0xb4a824: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4a828: ldr             x1, [x1, #0xa18]
    // 0xb4a82c: StoreField: r0->field_1b = r1
    //     0xb4a82c: stur            w1, [x0, #0x1b]
    // 0xb4a830: r1 = Instance_VerticalDirection
    //     0xb4a830: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4a834: ldr             x1, [x1, #0xa20]
    // 0xb4a838: StoreField: r0->field_23 = r1
    //     0xb4a838: stur            w1, [x0, #0x23]
    // 0xb4a83c: r1 = Instance_Clip
    //     0xb4a83c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4a840: ldr             x1, [x1, #0x38]
    // 0xb4a844: StoreField: r0->field_2b = r1
    //     0xb4a844: stur            w1, [x0, #0x2b]
    // 0xb4a848: StoreField: r0->field_2f = rZR
    //     0xb4a848: stur            xzr, [x0, #0x2f]
    // 0xb4a84c: ldur            x1, [fp, #-0x10]
    // 0xb4a850: StoreField: r0->field_b = r1
    //     0xb4a850: stur            w1, [x0, #0xb]
    // 0xb4a854: LeaveFrame
    //     0xb4a854: mov             SP, fp
    //     0xb4a858: ldp             fp, lr, [SP], #0x10
    // 0xb4a85c: ret
    //     0xb4a85c: ret             
    // 0xb4a860: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4a860: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4a864: b               #0xb4a58c
    // 0xb4a868: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4a868: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4097, size: 0x2c, field offset: 0xc
class ExpandablePaymentMethodWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7ed60, size: 0x6c
    // 0xc7ed60: EnterFrame
    //     0xc7ed60: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ed64: mov             fp, SP
    // 0xc7ed68: AllocStack(0x8)
    //     0xc7ed68: sub             SP, SP, #8
    // 0xc7ed6c: CheckStackOverflow
    //     0xc7ed6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7ed70: cmp             SP, x16
    //     0xc7ed74: b.ls            #0xc7edc4
    // 0xc7ed78: r1 = <bool>
    //     0xc7ed78: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0xc7ed7c: r0 = RxBool()
    //     0xc7ed7c: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0xc7ed80: mov             x2, x0
    // 0xc7ed84: r0 = Sentinel
    //     0xc7ed84: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7ed88: stur            x2, [fp, #-8]
    // 0xc7ed8c: StoreField: r2->field_13 = r0
    //     0xc7ed8c: stur            w0, [x2, #0x13]
    // 0xc7ed90: r0 = true
    //     0xc7ed90: add             x0, NULL, #0x20  ; true
    // 0xc7ed94: ArrayStore: r2[0] = r0  ; List_4
    //     0xc7ed94: stur            w0, [x2, #0x17]
    // 0xc7ed98: mov             x1, x2
    // 0xc7ed9c: r0 = RxNotifier()
    //     0xc7ed9c: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0xc7eda0: ldur            x0, [fp, #-8]
    // 0xc7eda4: r1 = false
    //     0xc7eda4: add             x1, NULL, #0x30  ; false
    // 0xc7eda8: StoreField: r0->field_13 = r1
    //     0xc7eda8: stur            w1, [x0, #0x13]
    // 0xc7edac: r1 = <ExpandablePaymentMethodWidget>
    //     0xc7edac: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a40] TypeArguments: <ExpandablePaymentMethodWidget>
    //     0xc7edb0: ldr             x1, [x1, #0xa40]
    // 0xc7edb4: r0 = _ExpandablePaymentMethodWidgetState()
    //     0xc7edb4: bl              #0xc7edcc  ; Allocate_ExpandablePaymentMethodWidgetStateStub -> _ExpandablePaymentMethodWidgetState (size=0x14)
    // 0xc7edb8: LeaveFrame
    //     0xc7edb8: mov             SP, fp
    //     0xc7edbc: ldp             fp, lr, [SP], #0x10
    // 0xc7edc0: ret
    //     0xc7edc0: ret             
    // 0xc7edc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7edc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7edc8: b               #0xc7ed78
  }
}
