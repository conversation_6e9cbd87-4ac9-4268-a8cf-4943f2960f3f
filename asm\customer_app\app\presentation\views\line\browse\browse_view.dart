// lib: , url: package:customer_app/app/presentation/views/line/browse/browse_view.dart

// class id: 1049474, size: 0x8
class :: {
}

// class id: 4547, size: 0x14, field offset: 0x14
//   const constructor, 
class BrowseView extends BaseView<dynamic> {

  [closure] SizedBox <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x137cf60, size: 0xc
    // 0x137cf60: r0 = Instance_SizedBox
    //     0x137cf60: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0x137cf64: ldr             x0, [x0, #0x8b8]
    // 0x137cf68: ret
    //     0x137cf68: ret             
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x137cf6c, size: 0x100
    // 0x137cf6c: EnterFrame
    //     0x137cf6c: stp             fp, lr, [SP, #-0x10]!
    //     0x137cf70: mov             fp, SP
    // 0x137cf74: AllocStack(0x38)
    //     0x137cf74: sub             SP, SP, #0x38
    // 0x137cf78: SetupParameters()
    //     0x137cf78: ldr             x0, [fp, #0x10]
    //     0x137cf7c: ldur            w2, [x0, #0x17]
    //     0x137cf80: add             x2, x2, HEAP, lsl #32
    //     0x137cf84: stur            x2, [fp, #-8]
    // 0x137cf88: CheckStackOverflow
    //     0x137cf88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137cf8c: cmp             SP, x16
    //     0x137cf90: b.ls            #0x137d064
    // 0x137cf94: LoadField: r1 = r2->field_f
    //     0x137cf94: ldur            w1, [x2, #0xf]
    // 0x137cf98: DecompressPointer r1
    //     0x137cf98: add             x1, x1, HEAP, lsl #32
    // 0x137cf9c: r0 = controller()
    //     0x137cf9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137cfa0: mov             x1, x0
    // 0x137cfa4: r0 = configData()
    //     0x137cfa4: bl              #0x13187d8  ; [package:customer_app/app/presentation/controllers/browse/browse_controller.dart] BrowseController::configData
    // 0x137cfa8: LoadField: r1 = r0->field_63
    //     0x137cfa8: ldur            w1, [x0, #0x63]
    // 0x137cfac: DecompressPointer r1
    //     0x137cfac: add             x1, x1, HEAP, lsl #32
    // 0x137cfb0: cmp             w1, NULL
    // 0x137cfb4: b.ne            #0x137cfc0
    // 0x137cfb8: r0 = Null
    //     0x137cfb8: mov             x0, NULL
    // 0x137cfbc: b               #0x137cfc4
    // 0x137cfc0: LoadField: r0 = r1->field_b
    //     0x137cfc0: ldur            w0, [x1, #0xb]
    // 0x137cfc4: cmp             w0, NULL
    // 0x137cfc8: b.ne            #0x137cfd4
    // 0x137cfcc: r3 = 0
    //     0x137cfcc: movz            x3, #0
    // 0x137cfd0: b               #0x137cfdc
    // 0x137cfd4: r1 = LoadInt32Instr(r0)
    //     0x137cfd4: sbfx            x1, x0, #1, #0x1f
    // 0x137cfd8: mov             x3, x1
    // 0x137cfdc: ldur            x2, [fp, #-8]
    // 0x137cfe0: stur            x3, [fp, #-0x10]
    // 0x137cfe4: r1 = Function '<anonymous closure>':.
    //     0x137cfe4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e518] AnonymousClosure: (0x137d06c), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x137cfe8: ldr             x1, [x1, #0x518]
    // 0x137cfec: r0 = AllocateClosure()
    //     0x137cfec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137cff0: r1 = Function '<anonymous closure>':.
    //     0x137cff0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e520] AnonymousClosure: (0x137cf60), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x137cff4: ldr             x1, [x1, #0x520]
    // 0x137cff8: r2 = Null
    //     0x137cff8: mov             x2, NULL
    // 0x137cffc: stur            x0, [fp, #-8]
    // 0x137d000: r0 = AllocateClosure()
    //     0x137d000: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137d004: stur            x0, [fp, #-0x18]
    // 0x137d008: r0 = ListView()
    //     0x137d008: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x137d00c: stur            x0, [fp, #-0x20]
    // 0x137d010: r16 = Instance_Axis
    //     0x137d010: ldr             x16, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x137d014: r30 = true
    //     0x137d014: add             lr, NULL, #0x20  ; true
    // 0x137d018: stp             lr, x16, [SP, #8]
    // 0x137d01c: r16 = false
    //     0x137d01c: add             x16, NULL, #0x30  ; false
    // 0x137d020: str             x16, [SP]
    // 0x137d024: mov             x1, x0
    // 0x137d028: ldur            x2, [fp, #-8]
    // 0x137d02c: ldur            x3, [fp, #-0x10]
    // 0x137d030: ldur            x5, [fp, #-0x18]
    // 0x137d034: r4 = const [0, 0x7, 0x3, 0x4, primary, 0x6, scrollDirection, 0x4, shrinkWrap, 0x5, null]
    //     0x137d034: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e528] List(11) [0, 0x7, 0x3, 0x4, "primary", 0x6, "scrollDirection", 0x4, "shrinkWrap", 0x5, Null]
    //     0x137d038: ldr             x4, [x4, #0x528]
    // 0x137d03c: r0 = ListView.separated()
    //     0x137d03c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x137d040: r0 = Padding()
    //     0x137d040: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x137d044: r1 = Instance_EdgeInsets
    //     0x137d044: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0x137d048: ldr             x1, [x1, #0xa00]
    // 0x137d04c: StoreField: r0->field_f = r1
    //     0x137d04c: stur            w1, [x0, #0xf]
    // 0x137d050: ldur            x1, [fp, #-0x20]
    // 0x137d054: StoreField: r0->field_b = r1
    //     0x137d054: stur            w1, [x0, #0xb]
    // 0x137d058: LeaveFrame
    //     0x137d058: mov             SP, fp
    //     0x137d05c: ldp             fp, lr, [SP], #0x10
    // 0x137d060: ret
    //     0x137d060: ret             
    // 0x137d064: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137d064: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137d068: b               #0x137cf94
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x137d06c, size: 0x57c
    // 0x137d06c: EnterFrame
    //     0x137d06c: stp             fp, lr, [SP, #-0x10]!
    //     0x137d070: mov             fp, SP
    // 0x137d074: AllocStack(0x50)
    //     0x137d074: sub             SP, SP, #0x50
    // 0x137d078: SetupParameters()
    //     0x137d078: ldr             x0, [fp, #0x20]
    //     0x137d07c: ldur            w1, [x0, #0x17]
    //     0x137d080: add             x1, x1, HEAP, lsl #32
    //     0x137d084: stur            x1, [fp, #-8]
    // 0x137d088: CheckStackOverflow
    //     0x137d088: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137d08c: cmp             SP, x16
    //     0x137d090: b.ls            #0x137d5d0
    // 0x137d094: r1 = 1
    //     0x137d094: movz            x1, #0x1
    // 0x137d098: r0 = AllocateContext()
    //     0x137d098: bl              #0x16f6108  ; AllocateContextStub
    // 0x137d09c: mov             x2, x0
    // 0x137d0a0: ldur            x0, [fp, #-8]
    // 0x137d0a4: stur            x2, [fp, #-0x10]
    // 0x137d0a8: StoreField: r2->field_b = r0
    //     0x137d0a8: stur            w0, [x2, #0xb]
    // 0x137d0ac: ldr             x1, [fp, #0x10]
    // 0x137d0b0: StoreField: r2->field_f = r1
    //     0x137d0b0: stur            w1, [x2, #0xf]
    // 0x137d0b4: LoadField: r1 = r0->field_f
    //     0x137d0b4: ldur            w1, [x0, #0xf]
    // 0x137d0b8: DecompressPointer r1
    //     0x137d0b8: add             x1, x1, HEAP, lsl #32
    // 0x137d0bc: r0 = controller()
    //     0x137d0bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137d0c0: LoadField: r1 = r0->field_4b
    //     0x137d0c0: ldur            w1, [x0, #0x4b]
    // 0x137d0c4: DecompressPointer r1
    //     0x137d0c4: add             x1, x1, HEAP, lsl #32
    // 0x137d0c8: r0 = value()
    //     0x137d0c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137d0cc: LoadField: r2 = r0->field_63
    //     0x137d0cc: ldur            w2, [x0, #0x63]
    // 0x137d0d0: DecompressPointer r2
    //     0x137d0d0: add             x2, x2, HEAP, lsl #32
    // 0x137d0d4: cmp             w2, NULL
    // 0x137d0d8: b.ne            #0x137d0e8
    // 0x137d0dc: ldur            x3, [fp, #-0x10]
    // 0x137d0e0: r0 = Null
    //     0x137d0e0: mov             x0, NULL
    // 0x137d0e4: b               #0x137d164
    // 0x137d0e8: ldur            x3, [fp, #-0x10]
    // 0x137d0ec: LoadField: r0 = r3->field_f
    //     0x137d0ec: ldur            w0, [x3, #0xf]
    // 0x137d0f0: DecompressPointer r0
    //     0x137d0f0: add             x0, x0, HEAP, lsl #32
    // 0x137d0f4: LoadField: r1 = r2->field_b
    //     0x137d0f4: ldur            w1, [x2, #0xb]
    // 0x137d0f8: r4 = LoadInt32Instr(r0)
    //     0x137d0f8: sbfx            x4, x0, #1, #0x1f
    //     0x137d0fc: tbz             w0, #0, #0x137d104
    //     0x137d100: ldur            x4, [x0, #7]
    // 0x137d104: r0 = LoadInt32Instr(r1)
    //     0x137d104: sbfx            x0, x1, #1, #0x1f
    // 0x137d108: mov             x1, x4
    // 0x137d10c: cmp             x1, x0
    // 0x137d110: b.hs            #0x137d5d8
    // 0x137d114: LoadField: r0 = r2->field_f
    //     0x137d114: ldur            w0, [x2, #0xf]
    // 0x137d118: DecompressPointer r0
    //     0x137d118: add             x0, x0, HEAP, lsl #32
    // 0x137d11c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x137d11c: add             x16, x0, x4, lsl #2
    //     0x137d120: ldur            w1, [x16, #0xf]
    // 0x137d124: DecompressPointer r1
    //     0x137d124: add             x1, x1, HEAP, lsl #32
    // 0x137d128: cmp             w1, NULL
    // 0x137d12c: b.ne            #0x137d138
    // 0x137d130: r0 = Null
    //     0x137d130: mov             x0, NULL
    // 0x137d134: b               #0x137d164
    // 0x137d138: LoadField: r0 = r1->field_1f
    //     0x137d138: ldur            w0, [x1, #0x1f]
    // 0x137d13c: DecompressPointer r0
    //     0x137d13c: add             x0, x0, HEAP, lsl #32
    // 0x137d140: cmp             w0, NULL
    // 0x137d144: b.ne            #0x137d150
    // 0x137d148: r0 = Null
    //     0x137d148: mov             x0, NULL
    // 0x137d14c: b               #0x137d164
    // 0x137d150: LoadField: r1 = r0->field_b
    //     0x137d150: ldur            w1, [x0, #0xb]
    // 0x137d154: cbnz            w1, #0x137d160
    // 0x137d158: r0 = false
    //     0x137d158: add             x0, NULL, #0x30  ; false
    // 0x137d15c: b               #0x137d164
    // 0x137d160: r0 = true
    //     0x137d160: add             x0, NULL, #0x20  ; true
    // 0x137d164: cmp             w0, NULL
    // 0x137d168: b.eq            #0x137d17c
    // 0x137d16c: tbnz            w0, #4, #0x137d17c
    // 0x137d170: r2 = Instance_Icon
    //     0x137d170: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e530] Obj!Icon@d66771
    //     0x137d174: ldr             x2, [x2, #0x530]
    // 0x137d178: b               #0x137d194
    // 0x137d17c: r0 = Container()
    //     0x137d17c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x137d180: mov             x1, x0
    // 0x137d184: stur            x0, [fp, #-0x18]
    // 0x137d188: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x137d188: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x137d18c: r0 = Container()
    //     0x137d18c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x137d190: ldur            x2, [fp, #-0x18]
    // 0x137d194: ldur            x0, [fp, #-8]
    // 0x137d198: stur            x2, [fp, #-0x18]
    // 0x137d19c: LoadField: r1 = r0->field_f
    //     0x137d19c: ldur            w1, [x0, #0xf]
    // 0x137d1a0: DecompressPointer r1
    //     0x137d1a0: add             x1, x1, HEAP, lsl #32
    // 0x137d1a4: r0 = controller()
    //     0x137d1a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137d1a8: LoadField: r1 = r0->field_4b
    //     0x137d1a8: ldur            w1, [x0, #0x4b]
    // 0x137d1ac: DecompressPointer r1
    //     0x137d1ac: add             x1, x1, HEAP, lsl #32
    // 0x137d1b0: r0 = value()
    //     0x137d1b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137d1b4: LoadField: r2 = r0->field_63
    //     0x137d1b4: ldur            w2, [x0, #0x63]
    // 0x137d1b8: DecompressPointer r2
    //     0x137d1b8: add             x2, x2, HEAP, lsl #32
    // 0x137d1bc: cmp             w2, NULL
    // 0x137d1c0: b.ne            #0x137d1d0
    // 0x137d1c4: ldur            x3, [fp, #-0x10]
    // 0x137d1c8: r0 = Null
    //     0x137d1c8: mov             x0, NULL
    // 0x137d1cc: b               #0x137d24c
    // 0x137d1d0: ldur            x3, [fp, #-0x10]
    // 0x137d1d4: LoadField: r0 = r3->field_f
    //     0x137d1d4: ldur            w0, [x3, #0xf]
    // 0x137d1d8: DecompressPointer r0
    //     0x137d1d8: add             x0, x0, HEAP, lsl #32
    // 0x137d1dc: LoadField: r1 = r2->field_b
    //     0x137d1dc: ldur            w1, [x2, #0xb]
    // 0x137d1e0: r4 = LoadInt32Instr(r0)
    //     0x137d1e0: sbfx            x4, x0, #1, #0x1f
    //     0x137d1e4: tbz             w0, #0, #0x137d1ec
    //     0x137d1e8: ldur            x4, [x0, #7]
    // 0x137d1ec: r0 = LoadInt32Instr(r1)
    //     0x137d1ec: sbfx            x0, x1, #1, #0x1f
    // 0x137d1f0: mov             x1, x4
    // 0x137d1f4: cmp             x1, x0
    // 0x137d1f8: b.hs            #0x137d5dc
    // 0x137d1fc: LoadField: r0 = r2->field_f
    //     0x137d1fc: ldur            w0, [x2, #0xf]
    // 0x137d200: DecompressPointer r0
    //     0x137d200: add             x0, x0, HEAP, lsl #32
    // 0x137d204: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x137d204: add             x16, x0, x4, lsl #2
    //     0x137d208: ldur            w1, [x16, #0xf]
    // 0x137d20c: DecompressPointer r1
    //     0x137d20c: add             x1, x1, HEAP, lsl #32
    // 0x137d210: cmp             w1, NULL
    // 0x137d214: b.ne            #0x137d220
    // 0x137d218: r0 = Null
    //     0x137d218: mov             x0, NULL
    // 0x137d21c: b               #0x137d24c
    // 0x137d220: LoadField: r0 = r1->field_1f
    //     0x137d220: ldur            w0, [x1, #0x1f]
    // 0x137d224: DecompressPointer r0
    //     0x137d224: add             x0, x0, HEAP, lsl #32
    // 0x137d228: cmp             w0, NULL
    // 0x137d22c: b.ne            #0x137d238
    // 0x137d230: r0 = Null
    //     0x137d230: mov             x0, NULL
    // 0x137d234: b               #0x137d24c
    // 0x137d238: LoadField: r1 = r0->field_b
    //     0x137d238: ldur            w1, [x0, #0xb]
    // 0x137d23c: cbnz            w1, #0x137d248
    // 0x137d240: r0 = false
    //     0x137d240: add             x0, NULL, #0x30  ; false
    // 0x137d244: b               #0x137d24c
    // 0x137d248: r0 = true
    //     0x137d248: add             x0, NULL, #0x20  ; true
    // 0x137d24c: cmp             w0, NULL
    // 0x137d250: b.eq            #0x137d264
    // 0x137d254: tbnz            w0, #4, #0x137d264
    // 0x137d258: r2 = Instance_Icon
    //     0x137d258: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e538] Obj!Icon@d66931
    //     0x137d25c: ldr             x2, [x2, #0x538]
    // 0x137d260: b               #0x137d27c
    // 0x137d264: r0 = Container()
    //     0x137d264: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x137d268: mov             x1, x0
    // 0x137d26c: stur            x0, [fp, #-0x20]
    // 0x137d270: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x137d270: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x137d274: r0 = Container()
    //     0x137d274: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x137d278: ldur            x2, [fp, #-0x20]
    // 0x137d27c: ldur            x0, [fp, #-8]
    // 0x137d280: ldr             x1, [fp, #0x18]
    // 0x137d284: stur            x2, [fp, #-0x20]
    // 0x137d288: r0 = of()
    //     0x137d288: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x137d28c: ldr             x1, [fp, #0x18]
    // 0x137d290: r0 = of()
    //     0x137d290: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x137d294: ldur            x0, [fp, #-8]
    // 0x137d298: LoadField: r1 = r0->field_f
    //     0x137d298: ldur            w1, [x0, #0xf]
    // 0x137d29c: DecompressPointer r1
    //     0x137d29c: add             x1, x1, HEAP, lsl #32
    // 0x137d2a0: r0 = controller()
    //     0x137d2a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137d2a4: LoadField: r1 = r0->field_4b
    //     0x137d2a4: ldur            w1, [x0, #0x4b]
    // 0x137d2a8: DecompressPointer r1
    //     0x137d2a8: add             x1, x1, HEAP, lsl #32
    // 0x137d2ac: r0 = value()
    //     0x137d2ac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137d2b0: LoadField: r2 = r0->field_63
    //     0x137d2b0: ldur            w2, [x0, #0x63]
    // 0x137d2b4: DecompressPointer r2
    //     0x137d2b4: add             x2, x2, HEAP, lsl #32
    // 0x137d2b8: cmp             w2, NULL
    // 0x137d2bc: b.ne            #0x137d2cc
    // 0x137d2c0: ldur            x3, [fp, #-0x10]
    // 0x137d2c4: r0 = Null
    //     0x137d2c4: mov             x0, NULL
    // 0x137d2c8: b               #0x137d324
    // 0x137d2cc: ldur            x3, [fp, #-0x10]
    // 0x137d2d0: LoadField: r0 = r3->field_f
    //     0x137d2d0: ldur            w0, [x3, #0xf]
    // 0x137d2d4: DecompressPointer r0
    //     0x137d2d4: add             x0, x0, HEAP, lsl #32
    // 0x137d2d8: LoadField: r1 = r2->field_b
    //     0x137d2d8: ldur            w1, [x2, #0xb]
    // 0x137d2dc: r4 = LoadInt32Instr(r0)
    //     0x137d2dc: sbfx            x4, x0, #1, #0x1f
    //     0x137d2e0: tbz             w0, #0, #0x137d2e8
    //     0x137d2e4: ldur            x4, [x0, #7]
    // 0x137d2e8: r0 = LoadInt32Instr(r1)
    //     0x137d2e8: sbfx            x0, x1, #1, #0x1f
    // 0x137d2ec: mov             x1, x4
    // 0x137d2f0: cmp             x1, x0
    // 0x137d2f4: b.hs            #0x137d5e0
    // 0x137d2f8: LoadField: r0 = r2->field_f
    //     0x137d2f8: ldur            w0, [x2, #0xf]
    // 0x137d2fc: DecompressPointer r0
    //     0x137d2fc: add             x0, x0, HEAP, lsl #32
    // 0x137d300: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x137d300: add             x16, x0, x4, lsl #2
    //     0x137d304: ldur            w1, [x16, #0xf]
    // 0x137d308: DecompressPointer r1
    //     0x137d308: add             x1, x1, HEAP, lsl #32
    // 0x137d30c: cmp             w1, NULL
    // 0x137d310: b.ne            #0x137d31c
    // 0x137d314: r0 = Null
    //     0x137d314: mov             x0, NULL
    // 0x137d318: b               #0x137d324
    // 0x137d31c: LoadField: r0 = r1->field_f
    //     0x137d31c: ldur            w0, [x1, #0xf]
    // 0x137d320: DecompressPointer r0
    //     0x137d320: add             x0, x0, HEAP, lsl #32
    // 0x137d324: cmp             w0, NULL
    // 0x137d328: b.ne            #0x137d334
    // 0x137d32c: r2 = ""
    //     0x137d32c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x137d330: b               #0x137d338
    // 0x137d334: mov             x2, x0
    // 0x137d338: ldur            x0, [fp, #-8]
    // 0x137d33c: ldr             x1, [fp, #0x18]
    // 0x137d340: stur            x2, [fp, #-0x28]
    // 0x137d344: r0 = of()
    //     0x137d344: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x137d348: LoadField: r1 = r0->field_87
    //     0x137d348: ldur            w1, [x0, #0x87]
    // 0x137d34c: DecompressPointer r1
    //     0x137d34c: add             x1, x1, HEAP, lsl #32
    // 0x137d350: LoadField: r0 = r1->field_7
    //     0x137d350: ldur            w0, [x1, #7]
    // 0x137d354: DecompressPointer r0
    //     0x137d354: add             x0, x0, HEAP, lsl #32
    // 0x137d358: r16 = 14.000000
    //     0x137d358: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x137d35c: ldr             x16, [x16, #0x1d8]
    // 0x137d360: r30 = Instance_Color
    //     0x137d360: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x137d364: stp             lr, x16, [SP]
    // 0x137d368: mov             x1, x0
    // 0x137d36c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x137d36c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x137d370: ldr             x4, [x4, #0xaa0]
    // 0x137d374: r0 = copyWith()
    //     0x137d374: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x137d378: stur            x0, [fp, #-0x30]
    // 0x137d37c: r0 = Text()
    //     0x137d37c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x137d380: mov             x1, x0
    // 0x137d384: ldur            x0, [fp, #-0x28]
    // 0x137d388: stur            x1, [fp, #-0x38]
    // 0x137d38c: StoreField: r1->field_b = r0
    //     0x137d38c: stur            w0, [x1, #0xb]
    // 0x137d390: ldur            x0, [fp, #-0x30]
    // 0x137d394: StoreField: r1->field_13 = r0
    //     0x137d394: stur            w0, [x1, #0x13]
    // 0x137d398: r0 = InkWell()
    //     0x137d398: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x137d39c: mov             x3, x0
    // 0x137d3a0: ldur            x0, [fp, #-0x38]
    // 0x137d3a4: stur            x3, [fp, #-0x28]
    // 0x137d3a8: StoreField: r3->field_b = r0
    //     0x137d3a8: stur            w0, [x3, #0xb]
    // 0x137d3ac: ldur            x2, [fp, #-0x10]
    // 0x137d3b0: r1 = Function '<anonymous closure>':.
    //     0x137d3b0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e540] AnonymousClosure: (0x137e97c), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x137d3b4: ldr             x1, [x1, #0x540]
    // 0x137d3b8: r0 = AllocateClosure()
    //     0x137d3b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137d3bc: mov             x1, x0
    // 0x137d3c0: ldur            x0, [fp, #-0x28]
    // 0x137d3c4: StoreField: r0->field_f = r1
    //     0x137d3c4: stur            w1, [x0, #0xf]
    // 0x137d3c8: r2 = true
    //     0x137d3c8: add             x2, NULL, #0x20  ; true
    // 0x137d3cc: StoreField: r0->field_43 = r2
    //     0x137d3cc: stur            w2, [x0, #0x43]
    // 0x137d3d0: r3 = Instance_BoxShape
    //     0x137d3d0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x137d3d4: ldr             x3, [x3, #0x80]
    // 0x137d3d8: StoreField: r0->field_47 = r3
    //     0x137d3d8: stur            w3, [x0, #0x47]
    // 0x137d3dc: StoreField: r0->field_6f = r2
    //     0x137d3dc: stur            w2, [x0, #0x6f]
    // 0x137d3e0: r4 = false
    //     0x137d3e0: add             x4, NULL, #0x30  ; false
    // 0x137d3e4: StoreField: r0->field_73 = r4
    //     0x137d3e4: stur            w4, [x0, #0x73]
    // 0x137d3e8: StoreField: r0->field_83 = r2
    //     0x137d3e8: stur            w2, [x0, #0x83]
    // 0x137d3ec: StoreField: r0->field_7b = r4
    //     0x137d3ec: stur            w4, [x0, #0x7b]
    // 0x137d3f0: ldur            x1, [fp, #-8]
    // 0x137d3f4: LoadField: r5 = r1->field_f
    //     0x137d3f4: ldur            w5, [x1, #0xf]
    // 0x137d3f8: DecompressPointer r5
    //     0x137d3f8: add             x5, x5, HEAP, lsl #32
    // 0x137d3fc: mov             x1, x5
    // 0x137d400: r0 = controller()
    //     0x137d400: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137d404: LoadField: r1 = r0->field_4b
    //     0x137d404: ldur            w1, [x0, #0x4b]
    // 0x137d408: DecompressPointer r1
    //     0x137d408: add             x1, x1, HEAP, lsl #32
    // 0x137d40c: r0 = value()
    //     0x137d40c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137d410: LoadField: r2 = r0->field_63
    //     0x137d410: ldur            w2, [x0, #0x63]
    // 0x137d414: DecompressPointer r2
    //     0x137d414: add             x2, x2, HEAP, lsl #32
    // 0x137d418: cmp             w2, NULL
    // 0x137d41c: b.ne            #0x137d42c
    // 0x137d420: ldur            x3, [fp, #-0x10]
    // 0x137d424: r0 = Null
    //     0x137d424: mov             x0, NULL
    // 0x137d428: b               #0x137d49c
    // 0x137d42c: ldur            x3, [fp, #-0x10]
    // 0x137d430: LoadField: r0 = r3->field_f
    //     0x137d430: ldur            w0, [x3, #0xf]
    // 0x137d434: DecompressPointer r0
    //     0x137d434: add             x0, x0, HEAP, lsl #32
    // 0x137d438: LoadField: r1 = r2->field_b
    //     0x137d438: ldur            w1, [x2, #0xb]
    // 0x137d43c: r4 = LoadInt32Instr(r0)
    //     0x137d43c: sbfx            x4, x0, #1, #0x1f
    //     0x137d440: tbz             w0, #0, #0x137d448
    //     0x137d444: ldur            x4, [x0, #7]
    // 0x137d448: r0 = LoadInt32Instr(r1)
    //     0x137d448: sbfx            x0, x1, #1, #0x1f
    // 0x137d44c: mov             x1, x4
    // 0x137d450: cmp             x1, x0
    // 0x137d454: b.hs            #0x137d5e4
    // 0x137d458: LoadField: r0 = r2->field_f
    //     0x137d458: ldur            w0, [x2, #0xf]
    // 0x137d45c: DecompressPointer r0
    //     0x137d45c: add             x0, x0, HEAP, lsl #32
    // 0x137d460: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x137d460: add             x16, x0, x4, lsl #2
    //     0x137d464: ldur            w1, [x16, #0xf]
    // 0x137d468: DecompressPointer r1
    //     0x137d468: add             x1, x1, HEAP, lsl #32
    // 0x137d46c: cmp             w1, NULL
    // 0x137d470: b.ne            #0x137d47c
    // 0x137d474: r0 = Null
    //     0x137d474: mov             x0, NULL
    // 0x137d478: b               #0x137d49c
    // 0x137d47c: LoadField: r0 = r1->field_1f
    //     0x137d47c: ldur            w0, [x1, #0x1f]
    // 0x137d480: DecompressPointer r0
    //     0x137d480: add             x0, x0, HEAP, lsl #32
    // 0x137d484: cmp             w0, NULL
    // 0x137d488: b.ne            #0x137d494
    // 0x137d48c: r0 = Null
    //     0x137d48c: mov             x0, NULL
    // 0x137d490: b               #0x137d49c
    // 0x137d494: LoadField: r1 = r0->field_b
    //     0x137d494: ldur            w1, [x0, #0xb]
    // 0x137d498: mov             x0, x1
    // 0x137d49c: cmp             w0, NULL
    // 0x137d4a0: b.ne            #0x137d4ac
    // 0x137d4a4: r6 = 0
    //     0x137d4a4: movz            x6, #0
    // 0x137d4a8: b               #0x137d4b4
    // 0x137d4ac: r1 = LoadInt32Instr(r0)
    //     0x137d4ac: sbfx            x1, x0, #1, #0x1f
    // 0x137d4b0: mov             x6, x1
    // 0x137d4b4: ldur            x5, [fp, #-0x18]
    // 0x137d4b8: ldur            x4, [fp, #-0x20]
    // 0x137d4bc: ldur            x0, [fp, #-0x28]
    // 0x137d4c0: mov             x2, x3
    // 0x137d4c4: stur            x6, [fp, #-0x40]
    // 0x137d4c8: r1 = Function '<anonymous closure>':.
    //     0x137d4c8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e548] AnonymousClosure: (0x137d64c), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x137d4cc: ldr             x1, [x1, #0x548]
    // 0x137d4d0: r0 = AllocateClosure()
    //     0x137d4d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137d4d4: r1 = Function '<anonymous closure>':.
    //     0x137d4d4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e550] AnonymousClosure: (0x137d640), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x137d4d8: ldr             x1, [x1, #0x550]
    // 0x137d4dc: r2 = Null
    //     0x137d4dc: mov             x2, NULL
    // 0x137d4e0: stur            x0, [fp, #-8]
    // 0x137d4e4: r0 = AllocateClosure()
    //     0x137d4e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137d4e8: stur            x0, [fp, #-0x10]
    // 0x137d4ec: r0 = ListView()
    //     0x137d4ec: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x137d4f0: stur            x0, [fp, #-0x30]
    // 0x137d4f4: r16 = Instance_NeverScrollableScrollPhysics
    //     0x137d4f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x137d4f8: ldr             x16, [x16, #0x1c8]
    // 0x137d4fc: r30 = true
    //     0x137d4fc: add             lr, NULL, #0x20  ; true
    // 0x137d500: stp             lr, x16, [SP]
    // 0x137d504: mov             x1, x0
    // 0x137d508: ldur            x2, [fp, #-8]
    // 0x137d50c: ldur            x3, [fp, #-0x40]
    // 0x137d510: ldur            x5, [fp, #-0x10]
    // 0x137d514: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x4, shrinkWrap, 0x5, null]
    //     0x137d514: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f968] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x4, "shrinkWrap", 0x5, Null]
    //     0x137d518: ldr             x4, [x4, #0x968]
    // 0x137d51c: r0 = ListView.separated()
    //     0x137d51c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x137d520: r0 = BrowseAccordian()
    //     0x137d520: bl              #0x137d5e8  ; AllocateBrowseAccordianStub -> BrowseAccordian (size=0x24)
    // 0x137d524: mov             x1, x0
    // 0x137d528: ldur            x0, [fp, #-0x28]
    // 0x137d52c: stur            x1, [fp, #-8]
    // 0x137d530: StoreField: r1->field_b = r0
    //     0x137d530: stur            w0, [x1, #0xb]
    // 0x137d534: ldur            x0, [fp, #-0x30]
    // 0x137d538: StoreField: r1->field_f = r0
    //     0x137d538: stur            w0, [x1, #0xf]
    // 0x137d53c: r0 = true
    //     0x137d53c: add             x0, NULL, #0x20  ; true
    // 0x137d540: StoreField: r1->field_13 = r0
    //     0x137d540: stur            w0, [x1, #0x13]
    // 0x137d544: ldur            x2, [fp, #-0x18]
    // 0x137d548: StoreField: r1->field_1b = r2
    //     0x137d548: stur            w2, [x1, #0x1b]
    // 0x137d54c: ldur            x2, [fp, #-0x20]
    // 0x137d550: StoreField: r1->field_1f = r2
    //     0x137d550: stur            w2, [x1, #0x1f]
    // 0x137d554: r0 = InkWell()
    //     0x137d554: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x137d558: mov             x3, x0
    // 0x137d55c: ldur            x0, [fp, #-8]
    // 0x137d560: stur            x3, [fp, #-0x10]
    // 0x137d564: StoreField: r3->field_b = r0
    //     0x137d564: stur            w0, [x3, #0xb]
    // 0x137d568: r1 = Function '<anonymous closure>':.
    //     0x137d568: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e558] AnonymousClosure: (0x137d5f4), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x137d56c: ldr             x1, [x1, #0x558]
    // 0x137d570: r2 = Null
    //     0x137d570: mov             x2, NULL
    // 0x137d574: r0 = AllocateClosure()
    //     0x137d574: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137d578: mov             x1, x0
    // 0x137d57c: ldur            x0, [fp, #-0x10]
    // 0x137d580: StoreField: r0->field_f = r1
    //     0x137d580: stur            w1, [x0, #0xf]
    // 0x137d584: r1 = true
    //     0x137d584: add             x1, NULL, #0x20  ; true
    // 0x137d588: StoreField: r0->field_43 = r1
    //     0x137d588: stur            w1, [x0, #0x43]
    // 0x137d58c: r2 = Instance_BoxShape
    //     0x137d58c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x137d590: ldr             x2, [x2, #0x80]
    // 0x137d594: StoreField: r0->field_47 = r2
    //     0x137d594: stur            w2, [x0, #0x47]
    // 0x137d598: StoreField: r0->field_6f = r1
    //     0x137d598: stur            w1, [x0, #0x6f]
    // 0x137d59c: r2 = false
    //     0x137d59c: add             x2, NULL, #0x30  ; false
    // 0x137d5a0: StoreField: r0->field_73 = r2
    //     0x137d5a0: stur            w2, [x0, #0x73]
    // 0x137d5a4: StoreField: r0->field_83 = r1
    //     0x137d5a4: stur            w1, [x0, #0x83]
    // 0x137d5a8: StoreField: r0->field_7b = r2
    //     0x137d5a8: stur            w2, [x0, #0x7b]
    // 0x137d5ac: r0 = Padding()
    //     0x137d5ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x137d5b0: r1 = Instance_EdgeInsets
    //     0x137d5b0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e560] Obj!EdgeInsets@d582e1
    //     0x137d5b4: ldr             x1, [x1, #0x560]
    // 0x137d5b8: StoreField: r0->field_f = r1
    //     0x137d5b8: stur            w1, [x0, #0xf]
    // 0x137d5bc: ldur            x1, [fp, #-0x10]
    // 0x137d5c0: StoreField: r0->field_b = r1
    //     0x137d5c0: stur            w1, [x0, #0xb]
    // 0x137d5c4: LeaveFrame
    //     0x137d5c4: mov             SP, fp
    //     0x137d5c8: ldp             fp, lr, [SP], #0x10
    // 0x137d5cc: ret
    //     0x137d5cc: ret             
    // 0x137d5d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137d5d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137d5d4: b               #0x137d094
    // 0x137d5d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137d5d8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137d5dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137d5dc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137d5e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137d5e0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137d5e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137d5e4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x137d5f4, size: 0x4c
    // 0x137d5f4: EnterFrame
    //     0x137d5f4: stp             fp, lr, [SP, #-0x10]!
    //     0x137d5f8: mov             fp, SP
    // 0x137d5fc: AllocStack(0x10)
    //     0x137d5fc: sub             SP, SP, #0x10
    // 0x137d600: SetupParameters(BrowseView this /* r1 */)
    //     0x137d600: stur            NULL, [fp, #-8]
    //     0x137d604: movz            x0, #0
    //     0x137d608: add             x1, fp, w0, sxtw #2
    //     0x137d60c: ldr             x1, [x1, #0x10]
    //     0x137d610: ldur            w2, [x1, #0x17]
    //     0x137d614: add             x2, x2, HEAP, lsl #32
    //     0x137d618: stur            x2, [fp, #-0x10]
    // 0x137d61c: CheckStackOverflow
    //     0x137d61c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137d620: cmp             SP, x16
    //     0x137d624: b.ls            #0x137d638
    // 0x137d628: InitAsync() -> Future<void?>
    //     0x137d628: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x137d62c: bl              #0x6326e0  ; InitAsyncStub
    // 0x137d630: r0 = Null
    //     0x137d630: mov             x0, NULL
    // 0x137d634: r0 = ReturnAsyncNotFuture()
    //     0x137d634: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x137d638: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137d638: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137d63c: b               #0x137d628
  }
  [closure] SizedBox <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x137d640, size: 0xc
    // 0x137d640: r0 = Instance_SizedBox
    //     0x137d640: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0x137d644: ldr             x0, [x0, #0x568]
    // 0x137d648: ret
    //     0x137d648: ret             
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x137d64c, size: 0x8d8
    // 0x137d64c: EnterFrame
    //     0x137d64c: stp             fp, lr, [SP, #-0x10]!
    //     0x137d650: mov             fp, SP
    // 0x137d654: AllocStack(0x68)
    //     0x137d654: sub             SP, SP, #0x68
    // 0x137d658: SetupParameters()
    //     0x137d658: ldr             x0, [fp, #0x20]
    //     0x137d65c: ldur            w1, [x0, #0x17]
    //     0x137d660: add             x1, x1, HEAP, lsl #32
    //     0x137d664: stur            x1, [fp, #-8]
    // 0x137d668: CheckStackOverflow
    //     0x137d668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137d66c: cmp             SP, x16
    //     0x137d670: b.ls            #0x137def4
    // 0x137d674: r1 = 1
    //     0x137d674: movz            x1, #0x1
    // 0x137d678: r0 = AllocateContext()
    //     0x137d678: bl              #0x16f6108  ; AllocateContextStub
    // 0x137d67c: mov             x2, x0
    // 0x137d680: ldur            x0, [fp, #-8]
    // 0x137d684: stur            x2, [fp, #-0x18]
    // 0x137d688: StoreField: r2->field_b = r0
    //     0x137d688: stur            w0, [x2, #0xb]
    // 0x137d68c: ldr             x1, [fp, #0x10]
    // 0x137d690: StoreField: r2->field_f = r1
    //     0x137d690: stur            w1, [x2, #0xf]
    // 0x137d694: LoadField: r3 = r0->field_b
    //     0x137d694: ldur            w3, [x0, #0xb]
    // 0x137d698: DecompressPointer r3
    //     0x137d698: add             x3, x3, HEAP, lsl #32
    // 0x137d69c: stur            x3, [fp, #-0x10]
    // 0x137d6a0: LoadField: r1 = r3->field_f
    //     0x137d6a0: ldur            w1, [x3, #0xf]
    // 0x137d6a4: DecompressPointer r1
    //     0x137d6a4: add             x1, x1, HEAP, lsl #32
    // 0x137d6a8: r0 = controller()
    //     0x137d6a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137d6ac: LoadField: r1 = r0->field_4b
    //     0x137d6ac: ldur            w1, [x0, #0x4b]
    // 0x137d6b0: DecompressPointer r1
    //     0x137d6b0: add             x1, x1, HEAP, lsl #32
    // 0x137d6b4: r0 = value()
    //     0x137d6b4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137d6b8: LoadField: r2 = r0->field_63
    //     0x137d6b8: ldur            w2, [x0, #0x63]
    // 0x137d6bc: DecompressPointer r2
    //     0x137d6bc: add             x2, x2, HEAP, lsl #32
    // 0x137d6c0: cmp             w2, NULL
    // 0x137d6c4: b.ne            #0x137d6d8
    // 0x137d6c8: ldur            x3, [fp, #-8]
    // 0x137d6cc: ldur            x4, [fp, #-0x18]
    // 0x137d6d0: r0 = Null
    //     0x137d6d0: mov             x0, NULL
    // 0x137d6d4: b               #0x137d7c4
    // 0x137d6d8: ldur            x3, [fp, #-8]
    // 0x137d6dc: LoadField: r0 = r3->field_f
    //     0x137d6dc: ldur            w0, [x3, #0xf]
    // 0x137d6e0: DecompressPointer r0
    //     0x137d6e0: add             x0, x0, HEAP, lsl #32
    // 0x137d6e4: LoadField: r1 = r2->field_b
    //     0x137d6e4: ldur            w1, [x2, #0xb]
    // 0x137d6e8: r4 = LoadInt32Instr(r0)
    //     0x137d6e8: sbfx            x4, x0, #1, #0x1f
    //     0x137d6ec: tbz             w0, #0, #0x137d6f4
    //     0x137d6f0: ldur            x4, [x0, #7]
    // 0x137d6f4: r0 = LoadInt32Instr(r1)
    //     0x137d6f4: sbfx            x0, x1, #1, #0x1f
    // 0x137d6f8: mov             x1, x4
    // 0x137d6fc: cmp             x1, x0
    // 0x137d700: b.hs            #0x137defc
    // 0x137d704: LoadField: r0 = r2->field_f
    //     0x137d704: ldur            w0, [x2, #0xf]
    // 0x137d708: DecompressPointer r0
    //     0x137d708: add             x0, x0, HEAP, lsl #32
    // 0x137d70c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x137d70c: add             x16, x0, x4, lsl #2
    //     0x137d710: ldur            w1, [x16, #0xf]
    // 0x137d714: DecompressPointer r1
    //     0x137d714: add             x1, x1, HEAP, lsl #32
    // 0x137d718: cmp             w1, NULL
    // 0x137d71c: b.ne            #0x137d72c
    // 0x137d720: ldur            x4, [fp, #-0x18]
    // 0x137d724: r0 = Null
    //     0x137d724: mov             x0, NULL
    // 0x137d728: b               #0x137d7c4
    // 0x137d72c: LoadField: r2 = r1->field_1f
    //     0x137d72c: ldur            w2, [x1, #0x1f]
    // 0x137d730: DecompressPointer r2
    //     0x137d730: add             x2, x2, HEAP, lsl #32
    // 0x137d734: cmp             w2, NULL
    // 0x137d738: b.ne            #0x137d748
    // 0x137d73c: ldur            x4, [fp, #-0x18]
    // 0x137d740: r0 = Null
    //     0x137d740: mov             x0, NULL
    // 0x137d744: b               #0x137d7c4
    // 0x137d748: ldur            x4, [fp, #-0x18]
    // 0x137d74c: LoadField: r0 = r4->field_f
    //     0x137d74c: ldur            w0, [x4, #0xf]
    // 0x137d750: DecompressPointer r0
    //     0x137d750: add             x0, x0, HEAP, lsl #32
    // 0x137d754: LoadField: r1 = r2->field_b
    //     0x137d754: ldur            w1, [x2, #0xb]
    // 0x137d758: r5 = LoadInt32Instr(r0)
    //     0x137d758: sbfx            x5, x0, #1, #0x1f
    //     0x137d75c: tbz             w0, #0, #0x137d764
    //     0x137d760: ldur            x5, [x0, #7]
    // 0x137d764: r0 = LoadInt32Instr(r1)
    //     0x137d764: sbfx            x0, x1, #1, #0x1f
    // 0x137d768: mov             x1, x5
    // 0x137d76c: cmp             x1, x0
    // 0x137d770: b.hs            #0x137df00
    // 0x137d774: LoadField: r0 = r2->field_f
    //     0x137d774: ldur            w0, [x2, #0xf]
    // 0x137d778: DecompressPointer r0
    //     0x137d778: add             x0, x0, HEAP, lsl #32
    // 0x137d77c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x137d77c: add             x16, x0, x5, lsl #2
    //     0x137d780: ldur            w1, [x16, #0xf]
    // 0x137d784: DecompressPointer r1
    //     0x137d784: add             x1, x1, HEAP, lsl #32
    // 0x137d788: cmp             w1, NULL
    // 0x137d78c: b.ne            #0x137d798
    // 0x137d790: r0 = Null
    //     0x137d790: mov             x0, NULL
    // 0x137d794: b               #0x137d7c4
    // 0x137d798: LoadField: r0 = r1->field_f
    //     0x137d798: ldur            w0, [x1, #0xf]
    // 0x137d79c: DecompressPointer r0
    //     0x137d79c: add             x0, x0, HEAP, lsl #32
    // 0x137d7a0: cmp             w0, NULL
    // 0x137d7a4: b.ne            #0x137d7b0
    // 0x137d7a8: r0 = Null
    //     0x137d7a8: mov             x0, NULL
    // 0x137d7ac: b               #0x137d7c4
    // 0x137d7b0: LoadField: r1 = r0->field_7
    //     0x137d7b0: ldur            w1, [x0, #7]
    // 0x137d7b4: cbnz            w1, #0x137d7c0
    // 0x137d7b8: r0 = false
    //     0x137d7b8: add             x0, NULL, #0x30  ; false
    // 0x137d7bc: b               #0x137d7c4
    // 0x137d7c0: r0 = true
    //     0x137d7c0: add             x0, NULL, #0x20  ; true
    // 0x137d7c4: cmp             w0, NULL
    // 0x137d7c8: b.ne            #0x137d7d8
    // 0x137d7cc: r0 = Instance_BoxShape
    //     0x137d7cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x137d7d0: ldr             x0, [x0, #0x80]
    // 0x137d7d4: b               #0x137d834
    // 0x137d7d8: tbnz            w0, #4, #0x137d82c
    // 0x137d7dc: r1 = Instance_Color
    //     0x137d7dc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x137d7e0: d0 = 0.100000
    //     0x137d7e0: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x137d7e4: r0 = withOpacity()
    //     0x137d7e4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x137d7e8: mov             x2, x0
    // 0x137d7ec: r1 = Null
    //     0x137d7ec: mov             x1, NULL
    // 0x137d7f0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x137d7f0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x137d7f4: r0 = Border.all()
    //     0x137d7f4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x137d7f8: stur            x0, [fp, #-0x20]
    // 0x137d7fc: r0 = BoxDecoration()
    //     0x137d7fc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x137d800: mov             x1, x0
    // 0x137d804: ldur            x0, [fp, #-0x20]
    // 0x137d808: StoreField: r1->field_f = r0
    //     0x137d808: stur            w0, [x1, #0xf]
    // 0x137d80c: r0 = Instance_BorderRadius
    //     0x137d80c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x137d810: ldr             x0, [x0, #0xf70]
    // 0x137d814: StoreField: r1->field_13 = r0
    //     0x137d814: stur            w0, [x1, #0x13]
    // 0x137d818: r0 = Instance_BoxShape
    //     0x137d818: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x137d81c: ldr             x0, [x0, #0x80]
    // 0x137d820: StoreField: r1->field_23 = r0
    //     0x137d820: stur            w0, [x1, #0x23]
    // 0x137d824: mov             x3, x1
    // 0x137d828: b               #0x137d83c
    // 0x137d82c: r0 = Instance_BoxShape
    //     0x137d82c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x137d830: ldr             x0, [x0, #0x80]
    // 0x137d834: r3 = Instance_BoxDecoration
    //     0x137d834: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e570] Obj!BoxDecoration@d647d1
    //     0x137d838: ldr             x3, [x3, #0x570]
    // 0x137d83c: ldur            x2, [fp, #-0x10]
    // 0x137d840: stur            x3, [fp, #-0x20]
    // 0x137d844: LoadField: r1 = r2->field_f
    //     0x137d844: ldur            w1, [x2, #0xf]
    // 0x137d848: DecompressPointer r1
    //     0x137d848: add             x1, x1, HEAP, lsl #32
    // 0x137d84c: r0 = controller()
    //     0x137d84c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137d850: LoadField: r1 = r0->field_4b
    //     0x137d850: ldur            w1, [x0, #0x4b]
    // 0x137d854: DecompressPointer r1
    //     0x137d854: add             x1, x1, HEAP, lsl #32
    // 0x137d858: r0 = value()
    //     0x137d858: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137d85c: LoadField: r2 = r0->field_63
    //     0x137d85c: ldur            w2, [x0, #0x63]
    // 0x137d860: DecompressPointer r2
    //     0x137d860: add             x2, x2, HEAP, lsl #32
    // 0x137d864: cmp             w2, NULL
    // 0x137d868: b.ne            #0x137d87c
    // 0x137d86c: ldur            x3, [fp, #-8]
    // 0x137d870: ldur            x4, [fp, #-0x18]
    // 0x137d874: r0 = Null
    //     0x137d874: mov             x0, NULL
    // 0x137d878: b               #0x137d968
    // 0x137d87c: ldur            x3, [fp, #-8]
    // 0x137d880: LoadField: r0 = r3->field_f
    //     0x137d880: ldur            w0, [x3, #0xf]
    // 0x137d884: DecompressPointer r0
    //     0x137d884: add             x0, x0, HEAP, lsl #32
    // 0x137d888: LoadField: r1 = r2->field_b
    //     0x137d888: ldur            w1, [x2, #0xb]
    // 0x137d88c: r4 = LoadInt32Instr(r0)
    //     0x137d88c: sbfx            x4, x0, #1, #0x1f
    //     0x137d890: tbz             w0, #0, #0x137d898
    //     0x137d894: ldur            x4, [x0, #7]
    // 0x137d898: r0 = LoadInt32Instr(r1)
    //     0x137d898: sbfx            x0, x1, #1, #0x1f
    // 0x137d89c: mov             x1, x4
    // 0x137d8a0: cmp             x1, x0
    // 0x137d8a4: b.hs            #0x137df04
    // 0x137d8a8: LoadField: r0 = r2->field_f
    //     0x137d8a8: ldur            w0, [x2, #0xf]
    // 0x137d8ac: DecompressPointer r0
    //     0x137d8ac: add             x0, x0, HEAP, lsl #32
    // 0x137d8b0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x137d8b0: add             x16, x0, x4, lsl #2
    //     0x137d8b4: ldur            w1, [x16, #0xf]
    // 0x137d8b8: DecompressPointer r1
    //     0x137d8b8: add             x1, x1, HEAP, lsl #32
    // 0x137d8bc: cmp             w1, NULL
    // 0x137d8c0: b.ne            #0x137d8d0
    // 0x137d8c4: ldur            x4, [fp, #-0x18]
    // 0x137d8c8: r0 = Null
    //     0x137d8c8: mov             x0, NULL
    // 0x137d8cc: b               #0x137d968
    // 0x137d8d0: LoadField: r2 = r1->field_1f
    //     0x137d8d0: ldur            w2, [x1, #0x1f]
    // 0x137d8d4: DecompressPointer r2
    //     0x137d8d4: add             x2, x2, HEAP, lsl #32
    // 0x137d8d8: cmp             w2, NULL
    // 0x137d8dc: b.ne            #0x137d8ec
    // 0x137d8e0: ldur            x4, [fp, #-0x18]
    // 0x137d8e4: r0 = Null
    //     0x137d8e4: mov             x0, NULL
    // 0x137d8e8: b               #0x137d968
    // 0x137d8ec: ldur            x4, [fp, #-0x18]
    // 0x137d8f0: LoadField: r0 = r4->field_f
    //     0x137d8f0: ldur            w0, [x4, #0xf]
    // 0x137d8f4: DecompressPointer r0
    //     0x137d8f4: add             x0, x0, HEAP, lsl #32
    // 0x137d8f8: LoadField: r1 = r2->field_b
    //     0x137d8f8: ldur            w1, [x2, #0xb]
    // 0x137d8fc: r5 = LoadInt32Instr(r0)
    //     0x137d8fc: sbfx            x5, x0, #1, #0x1f
    //     0x137d900: tbz             w0, #0, #0x137d908
    //     0x137d904: ldur            x5, [x0, #7]
    // 0x137d908: r0 = LoadInt32Instr(r1)
    //     0x137d908: sbfx            x0, x1, #1, #0x1f
    // 0x137d90c: mov             x1, x5
    // 0x137d910: cmp             x1, x0
    // 0x137d914: b.hs            #0x137df08
    // 0x137d918: LoadField: r0 = r2->field_f
    //     0x137d918: ldur            w0, [x2, #0xf]
    // 0x137d91c: DecompressPointer r0
    //     0x137d91c: add             x0, x0, HEAP, lsl #32
    // 0x137d920: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x137d920: add             x16, x0, x5, lsl #2
    //     0x137d924: ldur            w1, [x16, #0xf]
    // 0x137d928: DecompressPointer r1
    //     0x137d928: add             x1, x1, HEAP, lsl #32
    // 0x137d92c: cmp             w1, NULL
    // 0x137d930: b.ne            #0x137d93c
    // 0x137d934: r0 = Null
    //     0x137d934: mov             x0, NULL
    // 0x137d938: b               #0x137d968
    // 0x137d93c: LoadField: r0 = r1->field_1f
    //     0x137d93c: ldur            w0, [x1, #0x1f]
    // 0x137d940: DecompressPointer r0
    //     0x137d940: add             x0, x0, HEAP, lsl #32
    // 0x137d944: cmp             w0, NULL
    // 0x137d948: b.ne            #0x137d954
    // 0x137d94c: r0 = Null
    //     0x137d94c: mov             x0, NULL
    // 0x137d950: b               #0x137d968
    // 0x137d954: LoadField: r1 = r0->field_b
    //     0x137d954: ldur            w1, [x0, #0xb]
    // 0x137d958: cbnz            w1, #0x137d964
    // 0x137d95c: r0 = false
    //     0x137d95c: add             x0, NULL, #0x30  ; false
    // 0x137d960: b               #0x137d968
    // 0x137d964: r0 = true
    //     0x137d964: add             x0, NULL, #0x20  ; true
    // 0x137d968: cmp             w0, NULL
    // 0x137d96c: b.eq            #0x137d980
    // 0x137d970: tbnz            w0, #4, #0x137d980
    // 0x137d974: r2 = Instance_Icon
    //     0x137d974: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e578] Obj!Icon@d668f1
    //     0x137d978: ldr             x2, [x2, #0x578]
    // 0x137d97c: b               #0x137d998
    // 0x137d980: r0 = Container()
    //     0x137d980: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x137d984: mov             x1, x0
    // 0x137d988: stur            x0, [fp, #-0x28]
    // 0x137d98c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x137d98c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x137d990: r0 = Container()
    //     0x137d990: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x137d994: ldur            x2, [fp, #-0x28]
    // 0x137d998: ldur            x0, [fp, #-0x10]
    // 0x137d99c: stur            x2, [fp, #-0x28]
    // 0x137d9a0: LoadField: r1 = r0->field_f
    //     0x137d9a0: ldur            w1, [x0, #0xf]
    // 0x137d9a4: DecompressPointer r1
    //     0x137d9a4: add             x1, x1, HEAP, lsl #32
    // 0x137d9a8: r0 = controller()
    //     0x137d9a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137d9ac: LoadField: r1 = r0->field_4b
    //     0x137d9ac: ldur            w1, [x0, #0x4b]
    // 0x137d9b0: DecompressPointer r1
    //     0x137d9b0: add             x1, x1, HEAP, lsl #32
    // 0x137d9b4: r0 = value()
    //     0x137d9b4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137d9b8: LoadField: r2 = r0->field_63
    //     0x137d9b8: ldur            w2, [x0, #0x63]
    // 0x137d9bc: DecompressPointer r2
    //     0x137d9bc: add             x2, x2, HEAP, lsl #32
    // 0x137d9c0: cmp             w2, NULL
    // 0x137d9c4: b.ne            #0x137d9d8
    // 0x137d9c8: ldur            x3, [fp, #-8]
    // 0x137d9cc: ldur            x4, [fp, #-0x18]
    // 0x137d9d0: r0 = Null
    //     0x137d9d0: mov             x0, NULL
    // 0x137d9d4: b               #0x137dac4
    // 0x137d9d8: ldur            x3, [fp, #-8]
    // 0x137d9dc: LoadField: r0 = r3->field_f
    //     0x137d9dc: ldur            w0, [x3, #0xf]
    // 0x137d9e0: DecompressPointer r0
    //     0x137d9e0: add             x0, x0, HEAP, lsl #32
    // 0x137d9e4: LoadField: r1 = r2->field_b
    //     0x137d9e4: ldur            w1, [x2, #0xb]
    // 0x137d9e8: r4 = LoadInt32Instr(r0)
    //     0x137d9e8: sbfx            x4, x0, #1, #0x1f
    //     0x137d9ec: tbz             w0, #0, #0x137d9f4
    //     0x137d9f0: ldur            x4, [x0, #7]
    // 0x137d9f4: r0 = LoadInt32Instr(r1)
    //     0x137d9f4: sbfx            x0, x1, #1, #0x1f
    // 0x137d9f8: mov             x1, x4
    // 0x137d9fc: cmp             x1, x0
    // 0x137da00: b.hs            #0x137df0c
    // 0x137da04: LoadField: r0 = r2->field_f
    //     0x137da04: ldur            w0, [x2, #0xf]
    // 0x137da08: DecompressPointer r0
    //     0x137da08: add             x0, x0, HEAP, lsl #32
    // 0x137da0c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x137da0c: add             x16, x0, x4, lsl #2
    //     0x137da10: ldur            w1, [x16, #0xf]
    // 0x137da14: DecompressPointer r1
    //     0x137da14: add             x1, x1, HEAP, lsl #32
    // 0x137da18: cmp             w1, NULL
    // 0x137da1c: b.ne            #0x137da2c
    // 0x137da20: ldur            x4, [fp, #-0x18]
    // 0x137da24: r0 = Null
    //     0x137da24: mov             x0, NULL
    // 0x137da28: b               #0x137dac4
    // 0x137da2c: LoadField: r2 = r1->field_1f
    //     0x137da2c: ldur            w2, [x1, #0x1f]
    // 0x137da30: DecompressPointer r2
    //     0x137da30: add             x2, x2, HEAP, lsl #32
    // 0x137da34: cmp             w2, NULL
    // 0x137da38: b.ne            #0x137da48
    // 0x137da3c: ldur            x4, [fp, #-0x18]
    // 0x137da40: r0 = Null
    //     0x137da40: mov             x0, NULL
    // 0x137da44: b               #0x137dac4
    // 0x137da48: ldur            x4, [fp, #-0x18]
    // 0x137da4c: LoadField: r0 = r4->field_f
    //     0x137da4c: ldur            w0, [x4, #0xf]
    // 0x137da50: DecompressPointer r0
    //     0x137da50: add             x0, x0, HEAP, lsl #32
    // 0x137da54: LoadField: r1 = r2->field_b
    //     0x137da54: ldur            w1, [x2, #0xb]
    // 0x137da58: r5 = LoadInt32Instr(r0)
    //     0x137da58: sbfx            x5, x0, #1, #0x1f
    //     0x137da5c: tbz             w0, #0, #0x137da64
    //     0x137da60: ldur            x5, [x0, #7]
    // 0x137da64: r0 = LoadInt32Instr(r1)
    //     0x137da64: sbfx            x0, x1, #1, #0x1f
    // 0x137da68: mov             x1, x5
    // 0x137da6c: cmp             x1, x0
    // 0x137da70: b.hs            #0x137df10
    // 0x137da74: LoadField: r0 = r2->field_f
    //     0x137da74: ldur            w0, [x2, #0xf]
    // 0x137da78: DecompressPointer r0
    //     0x137da78: add             x0, x0, HEAP, lsl #32
    // 0x137da7c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x137da7c: add             x16, x0, x5, lsl #2
    //     0x137da80: ldur            w1, [x16, #0xf]
    // 0x137da84: DecompressPointer r1
    //     0x137da84: add             x1, x1, HEAP, lsl #32
    // 0x137da88: cmp             w1, NULL
    // 0x137da8c: b.ne            #0x137da98
    // 0x137da90: r0 = Null
    //     0x137da90: mov             x0, NULL
    // 0x137da94: b               #0x137dac4
    // 0x137da98: LoadField: r0 = r1->field_1f
    //     0x137da98: ldur            w0, [x1, #0x1f]
    // 0x137da9c: DecompressPointer r0
    //     0x137da9c: add             x0, x0, HEAP, lsl #32
    // 0x137daa0: cmp             w0, NULL
    // 0x137daa4: b.ne            #0x137dab0
    // 0x137daa8: r0 = Null
    //     0x137daa8: mov             x0, NULL
    // 0x137daac: b               #0x137dac4
    // 0x137dab0: LoadField: r1 = r0->field_b
    //     0x137dab0: ldur            w1, [x0, #0xb]
    // 0x137dab4: cbnz            w1, #0x137dac0
    // 0x137dab8: r0 = false
    //     0x137dab8: add             x0, NULL, #0x30  ; false
    // 0x137dabc: b               #0x137dac4
    // 0x137dac0: r0 = true
    //     0x137dac0: add             x0, NULL, #0x20  ; true
    // 0x137dac4: cmp             w0, NULL
    // 0x137dac8: b.eq            #0x137dadc
    // 0x137dacc: tbnz            w0, #4, #0x137dadc
    // 0x137dad0: r2 = Instance_Icon
    //     0x137dad0: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e580] Obj!Icon@d668b1
    //     0x137dad4: ldr             x2, [x2, #0x580]
    // 0x137dad8: b               #0x137daf4
    // 0x137dadc: r0 = Container()
    //     0x137dadc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x137dae0: mov             x1, x0
    // 0x137dae4: stur            x0, [fp, #-0x30]
    // 0x137dae8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x137dae8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x137daec: r0 = Container()
    //     0x137daec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x137daf0: ldur            x2, [fp, #-0x30]
    // 0x137daf4: ldur            x0, [fp, #-0x10]
    // 0x137daf8: ldr             x1, [fp, #0x18]
    // 0x137dafc: stur            x2, [fp, #-0x30]
    // 0x137db00: r0 = of()
    //     0x137db00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x137db04: ldr             x1, [fp, #0x18]
    // 0x137db08: r0 = of()
    //     0x137db08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x137db0c: ldur            x0, [fp, #-0x10]
    // 0x137db10: LoadField: r1 = r0->field_f
    //     0x137db10: ldur            w1, [x0, #0xf]
    // 0x137db14: DecompressPointer r1
    //     0x137db14: add             x1, x1, HEAP, lsl #32
    // 0x137db18: r0 = controller()
    //     0x137db18: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137db1c: LoadField: r1 = r0->field_4b
    //     0x137db1c: ldur            w1, [x0, #0x4b]
    // 0x137db20: DecompressPointer r1
    //     0x137db20: add             x1, x1, HEAP, lsl #32
    // 0x137db24: r0 = value()
    //     0x137db24: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137db28: LoadField: r2 = r0->field_63
    //     0x137db28: ldur            w2, [x0, #0x63]
    // 0x137db2c: DecompressPointer r2
    //     0x137db2c: add             x2, x2, HEAP, lsl #32
    // 0x137db30: cmp             w2, NULL
    // 0x137db34: b.ne            #0x137db48
    // 0x137db38: ldur            x3, [fp, #-8]
    // 0x137db3c: ldur            x4, [fp, #-0x18]
    // 0x137db40: r0 = Null
    //     0x137db40: mov             x0, NULL
    // 0x137db44: b               #0x137dc10
    // 0x137db48: ldur            x3, [fp, #-8]
    // 0x137db4c: LoadField: r0 = r3->field_f
    //     0x137db4c: ldur            w0, [x3, #0xf]
    // 0x137db50: DecompressPointer r0
    //     0x137db50: add             x0, x0, HEAP, lsl #32
    // 0x137db54: LoadField: r1 = r2->field_b
    //     0x137db54: ldur            w1, [x2, #0xb]
    // 0x137db58: r4 = LoadInt32Instr(r0)
    //     0x137db58: sbfx            x4, x0, #1, #0x1f
    //     0x137db5c: tbz             w0, #0, #0x137db64
    //     0x137db60: ldur            x4, [x0, #7]
    // 0x137db64: r0 = LoadInt32Instr(r1)
    //     0x137db64: sbfx            x0, x1, #1, #0x1f
    // 0x137db68: mov             x1, x4
    // 0x137db6c: cmp             x1, x0
    // 0x137db70: b.hs            #0x137df14
    // 0x137db74: LoadField: r0 = r2->field_f
    //     0x137db74: ldur            w0, [x2, #0xf]
    // 0x137db78: DecompressPointer r0
    //     0x137db78: add             x0, x0, HEAP, lsl #32
    // 0x137db7c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x137db7c: add             x16, x0, x4, lsl #2
    //     0x137db80: ldur            w1, [x16, #0xf]
    // 0x137db84: DecompressPointer r1
    //     0x137db84: add             x1, x1, HEAP, lsl #32
    // 0x137db88: cmp             w1, NULL
    // 0x137db8c: b.ne            #0x137db9c
    // 0x137db90: ldur            x4, [fp, #-0x18]
    // 0x137db94: r0 = Null
    //     0x137db94: mov             x0, NULL
    // 0x137db98: b               #0x137dc10
    // 0x137db9c: LoadField: r2 = r1->field_1f
    //     0x137db9c: ldur            w2, [x1, #0x1f]
    // 0x137dba0: DecompressPointer r2
    //     0x137dba0: add             x2, x2, HEAP, lsl #32
    // 0x137dba4: cmp             w2, NULL
    // 0x137dba8: b.ne            #0x137dbb8
    // 0x137dbac: ldur            x4, [fp, #-0x18]
    // 0x137dbb0: r0 = Null
    //     0x137dbb0: mov             x0, NULL
    // 0x137dbb4: b               #0x137dc10
    // 0x137dbb8: ldur            x4, [fp, #-0x18]
    // 0x137dbbc: LoadField: r0 = r4->field_f
    //     0x137dbbc: ldur            w0, [x4, #0xf]
    // 0x137dbc0: DecompressPointer r0
    //     0x137dbc0: add             x0, x0, HEAP, lsl #32
    // 0x137dbc4: LoadField: r1 = r2->field_b
    //     0x137dbc4: ldur            w1, [x2, #0xb]
    // 0x137dbc8: r5 = LoadInt32Instr(r0)
    //     0x137dbc8: sbfx            x5, x0, #1, #0x1f
    //     0x137dbcc: tbz             w0, #0, #0x137dbd4
    //     0x137dbd0: ldur            x5, [x0, #7]
    // 0x137dbd4: r0 = LoadInt32Instr(r1)
    //     0x137dbd4: sbfx            x0, x1, #1, #0x1f
    // 0x137dbd8: mov             x1, x5
    // 0x137dbdc: cmp             x1, x0
    // 0x137dbe0: b.hs            #0x137df18
    // 0x137dbe4: LoadField: r0 = r2->field_f
    //     0x137dbe4: ldur            w0, [x2, #0xf]
    // 0x137dbe8: DecompressPointer r0
    //     0x137dbe8: add             x0, x0, HEAP, lsl #32
    // 0x137dbec: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x137dbec: add             x16, x0, x5, lsl #2
    //     0x137dbf0: ldur            w1, [x16, #0xf]
    // 0x137dbf4: DecompressPointer r1
    //     0x137dbf4: add             x1, x1, HEAP, lsl #32
    // 0x137dbf8: cmp             w1, NULL
    // 0x137dbfc: b.ne            #0x137dc08
    // 0x137dc00: r0 = Null
    //     0x137dc00: mov             x0, NULL
    // 0x137dc04: b               #0x137dc10
    // 0x137dc08: LoadField: r0 = r1->field_f
    //     0x137dc08: ldur            w0, [x1, #0xf]
    // 0x137dc0c: DecompressPointer r0
    //     0x137dc0c: add             x0, x0, HEAP, lsl #32
    // 0x137dc10: cmp             w0, NULL
    // 0x137dc14: b.ne            #0x137dc20
    // 0x137dc18: r2 = ""
    //     0x137dc18: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x137dc1c: b               #0x137dc24
    // 0x137dc20: mov             x2, x0
    // 0x137dc24: ldur            x0, [fp, #-0x10]
    // 0x137dc28: ldr             x1, [fp, #0x18]
    // 0x137dc2c: stur            x2, [fp, #-0x38]
    // 0x137dc30: r0 = of()
    //     0x137dc30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x137dc34: LoadField: r1 = r0->field_87
    //     0x137dc34: ldur            w1, [x0, #0x87]
    // 0x137dc38: DecompressPointer r1
    //     0x137dc38: add             x1, x1, HEAP, lsl #32
    // 0x137dc3c: LoadField: r0 = r1->field_7
    //     0x137dc3c: ldur            w0, [x1, #7]
    // 0x137dc40: DecompressPointer r0
    //     0x137dc40: add             x0, x0, HEAP, lsl #32
    // 0x137dc44: r16 = Instance_Color
    //     0x137dc44: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x137dc48: r30 = 14.000000
    //     0x137dc48: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x137dc4c: ldr             lr, [lr, #0x1d8]
    // 0x137dc50: stp             lr, x16, [SP]
    // 0x137dc54: mov             x1, x0
    // 0x137dc58: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x137dc58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x137dc5c: ldr             x4, [x4, #0x9b8]
    // 0x137dc60: r0 = copyWith()
    //     0x137dc60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x137dc64: stur            x0, [fp, #-0x40]
    // 0x137dc68: r0 = Text()
    //     0x137dc68: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x137dc6c: mov             x1, x0
    // 0x137dc70: ldur            x0, [fp, #-0x38]
    // 0x137dc74: stur            x1, [fp, #-0x48]
    // 0x137dc78: StoreField: r1->field_b = r0
    //     0x137dc78: stur            w0, [x1, #0xb]
    // 0x137dc7c: ldur            x0, [fp, #-0x40]
    // 0x137dc80: StoreField: r1->field_13 = r0
    //     0x137dc80: stur            w0, [x1, #0x13]
    // 0x137dc84: r0 = InkWell()
    //     0x137dc84: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x137dc88: mov             x3, x0
    // 0x137dc8c: ldur            x0, [fp, #-0x48]
    // 0x137dc90: stur            x3, [fp, #-0x38]
    // 0x137dc94: StoreField: r3->field_b = r0
    //     0x137dc94: stur            w0, [x3, #0xb]
    // 0x137dc98: ldur            x2, [fp, #-0x18]
    // 0x137dc9c: r1 = Function '<anonymous closure>':.
    //     0x137dc9c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e588] AnonymousClosure: (0x137e628), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x137dca0: ldr             x1, [x1, #0x588]
    // 0x137dca4: r0 = AllocateClosure()
    //     0x137dca4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137dca8: mov             x1, x0
    // 0x137dcac: ldur            x0, [fp, #-0x38]
    // 0x137dcb0: StoreField: r0->field_f = r1
    //     0x137dcb0: stur            w1, [x0, #0xf]
    // 0x137dcb4: r2 = true
    //     0x137dcb4: add             x2, NULL, #0x20  ; true
    // 0x137dcb8: StoreField: r0->field_43 = r2
    //     0x137dcb8: stur            w2, [x0, #0x43]
    // 0x137dcbc: r1 = Instance_BoxShape
    //     0x137dcbc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x137dcc0: ldr             x1, [x1, #0x80]
    // 0x137dcc4: StoreField: r0->field_47 = r1
    //     0x137dcc4: stur            w1, [x0, #0x47]
    // 0x137dcc8: StoreField: r0->field_6f = r2
    //     0x137dcc8: stur            w2, [x0, #0x6f]
    // 0x137dccc: r1 = false
    //     0x137dccc: add             x1, NULL, #0x30  ; false
    // 0x137dcd0: StoreField: r0->field_73 = r1
    //     0x137dcd0: stur            w1, [x0, #0x73]
    // 0x137dcd4: StoreField: r0->field_83 = r2
    //     0x137dcd4: stur            w2, [x0, #0x83]
    // 0x137dcd8: StoreField: r0->field_7b = r1
    //     0x137dcd8: stur            w1, [x0, #0x7b]
    // 0x137dcdc: ldur            x1, [fp, #-0x10]
    // 0x137dce0: LoadField: r3 = r1->field_f
    //     0x137dce0: ldur            w3, [x1, #0xf]
    // 0x137dce4: DecompressPointer r3
    //     0x137dce4: add             x3, x3, HEAP, lsl #32
    // 0x137dce8: mov             x1, x3
    // 0x137dcec: r0 = controller()
    //     0x137dcec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137dcf0: LoadField: r1 = r0->field_4b
    //     0x137dcf0: ldur            w1, [x0, #0x4b]
    // 0x137dcf4: DecompressPointer r1
    //     0x137dcf4: add             x1, x1, HEAP, lsl #32
    // 0x137dcf8: r0 = value()
    //     0x137dcf8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137dcfc: LoadField: r2 = r0->field_63
    //     0x137dcfc: ldur            w2, [x0, #0x63]
    // 0x137dd00: DecompressPointer r2
    //     0x137dd00: add             x2, x2, HEAP, lsl #32
    // 0x137dd04: cmp             w2, NULL
    // 0x137dd08: b.ne            #0x137dd18
    // 0x137dd0c: ldur            x3, [fp, #-0x18]
    // 0x137dd10: r0 = Null
    //     0x137dd10: mov             x0, NULL
    // 0x137dd14: b               #0x137ddfc
    // 0x137dd18: ldur            x0, [fp, #-8]
    // 0x137dd1c: LoadField: r1 = r0->field_f
    //     0x137dd1c: ldur            w1, [x0, #0xf]
    // 0x137dd20: DecompressPointer r1
    //     0x137dd20: add             x1, x1, HEAP, lsl #32
    // 0x137dd24: LoadField: r0 = r2->field_b
    //     0x137dd24: ldur            w0, [x2, #0xb]
    // 0x137dd28: r3 = LoadInt32Instr(r1)
    //     0x137dd28: sbfx            x3, x1, #1, #0x1f
    //     0x137dd2c: tbz             w1, #0, #0x137dd34
    //     0x137dd30: ldur            x3, [x1, #7]
    // 0x137dd34: r1 = LoadInt32Instr(r0)
    //     0x137dd34: sbfx            x1, x0, #1, #0x1f
    // 0x137dd38: mov             x0, x1
    // 0x137dd3c: mov             x1, x3
    // 0x137dd40: cmp             x1, x0
    // 0x137dd44: b.hs            #0x137df1c
    // 0x137dd48: LoadField: r0 = r2->field_f
    //     0x137dd48: ldur            w0, [x2, #0xf]
    // 0x137dd4c: DecompressPointer r0
    //     0x137dd4c: add             x0, x0, HEAP, lsl #32
    // 0x137dd50: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x137dd50: add             x16, x0, x3, lsl #2
    //     0x137dd54: ldur            w1, [x16, #0xf]
    // 0x137dd58: DecompressPointer r1
    //     0x137dd58: add             x1, x1, HEAP, lsl #32
    // 0x137dd5c: cmp             w1, NULL
    // 0x137dd60: b.ne            #0x137dd70
    // 0x137dd64: ldur            x3, [fp, #-0x18]
    // 0x137dd68: r0 = Null
    //     0x137dd68: mov             x0, NULL
    // 0x137dd6c: b               #0x137ddfc
    // 0x137dd70: LoadField: r2 = r1->field_1f
    //     0x137dd70: ldur            w2, [x1, #0x1f]
    // 0x137dd74: DecompressPointer r2
    //     0x137dd74: add             x2, x2, HEAP, lsl #32
    // 0x137dd78: cmp             w2, NULL
    // 0x137dd7c: b.ne            #0x137dd8c
    // 0x137dd80: ldur            x3, [fp, #-0x18]
    // 0x137dd84: r0 = Null
    //     0x137dd84: mov             x0, NULL
    // 0x137dd88: b               #0x137ddfc
    // 0x137dd8c: ldur            x3, [fp, #-0x18]
    // 0x137dd90: LoadField: r0 = r3->field_f
    //     0x137dd90: ldur            w0, [x3, #0xf]
    // 0x137dd94: DecompressPointer r0
    //     0x137dd94: add             x0, x0, HEAP, lsl #32
    // 0x137dd98: LoadField: r1 = r2->field_b
    //     0x137dd98: ldur            w1, [x2, #0xb]
    // 0x137dd9c: r4 = LoadInt32Instr(r0)
    //     0x137dd9c: sbfx            x4, x0, #1, #0x1f
    //     0x137dda0: tbz             w0, #0, #0x137dda8
    //     0x137dda4: ldur            x4, [x0, #7]
    // 0x137dda8: r0 = LoadInt32Instr(r1)
    //     0x137dda8: sbfx            x0, x1, #1, #0x1f
    // 0x137ddac: mov             x1, x4
    // 0x137ddb0: cmp             x1, x0
    // 0x137ddb4: b.hs            #0x137df20
    // 0x137ddb8: LoadField: r0 = r2->field_f
    //     0x137ddb8: ldur            w0, [x2, #0xf]
    // 0x137ddbc: DecompressPointer r0
    //     0x137ddbc: add             x0, x0, HEAP, lsl #32
    // 0x137ddc0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x137ddc0: add             x16, x0, x4, lsl #2
    //     0x137ddc4: ldur            w1, [x16, #0xf]
    // 0x137ddc8: DecompressPointer r1
    //     0x137ddc8: add             x1, x1, HEAP, lsl #32
    // 0x137ddcc: cmp             w1, NULL
    // 0x137ddd0: b.ne            #0x137dddc
    // 0x137ddd4: r0 = Null
    //     0x137ddd4: mov             x0, NULL
    // 0x137ddd8: b               #0x137ddfc
    // 0x137dddc: LoadField: r0 = r1->field_1f
    //     0x137dddc: ldur            w0, [x1, #0x1f]
    // 0x137dde0: DecompressPointer r0
    //     0x137dde0: add             x0, x0, HEAP, lsl #32
    // 0x137dde4: cmp             w0, NULL
    // 0x137dde8: b.ne            #0x137ddf4
    // 0x137ddec: r0 = Null
    //     0x137ddec: mov             x0, NULL
    // 0x137ddf0: b               #0x137ddfc
    // 0x137ddf4: LoadField: r1 = r0->field_b
    //     0x137ddf4: ldur            w1, [x0, #0xb]
    // 0x137ddf8: mov             x0, x1
    // 0x137ddfc: cmp             w0, NULL
    // 0x137de00: b.ne            #0x137de0c
    // 0x137de04: r6 = 0
    //     0x137de04: movz            x6, #0
    // 0x137de08: b               #0x137de14
    // 0x137de0c: r1 = LoadInt32Instr(r0)
    //     0x137de0c: sbfx            x1, x0, #1, #0x1f
    // 0x137de10: mov             x6, x1
    // 0x137de14: ldur            x5, [fp, #-0x28]
    // 0x137de18: ldur            x4, [fp, #-0x30]
    // 0x137de1c: ldur            x0, [fp, #-0x38]
    // 0x137de20: mov             x2, x3
    // 0x137de24: stur            x6, [fp, #-0x50]
    // 0x137de28: r1 = Function '<anonymous closure>':.
    //     0x137de28: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e590] AnonymousClosure: (0x137df30), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x137de2c: ldr             x1, [x1, #0x590]
    // 0x137de30: r0 = AllocateClosure()
    //     0x137de30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137de34: r1 = Function '<anonymous closure>':.
    //     0x137de34: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e598] AnonymousClosure: (0x137df24), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x137de38: ldr             x1, [x1, #0x598]
    // 0x137de3c: r2 = Null
    //     0x137de3c: mov             x2, NULL
    // 0x137de40: stur            x0, [fp, #-8]
    // 0x137de44: r0 = AllocateClosure()
    //     0x137de44: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137de48: stur            x0, [fp, #-0x10]
    // 0x137de4c: r0 = ListView()
    //     0x137de4c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x137de50: stur            x0, [fp, #-0x18]
    // 0x137de54: r16 = Instance_NeverScrollableScrollPhysics
    //     0x137de54: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x137de58: ldr             x16, [x16, #0x1c8]
    // 0x137de5c: r30 = true
    //     0x137de5c: add             lr, NULL, #0x20  ; true
    // 0x137de60: stp             lr, x16, [SP]
    // 0x137de64: mov             x1, x0
    // 0x137de68: ldur            x2, [fp, #-8]
    // 0x137de6c: ldur            x3, [fp, #-0x50]
    // 0x137de70: ldur            x5, [fp, #-0x10]
    // 0x137de74: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x4, shrinkWrap, 0x5, null]
    //     0x137de74: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f968] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x4, "shrinkWrap", 0x5, Null]
    //     0x137de78: ldr             x4, [x4, #0x968]
    // 0x137de7c: r0 = ListView.separated()
    //     0x137de7c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x137de80: r0 = BrowseAccordian()
    //     0x137de80: bl              #0x137d5e8  ; AllocateBrowseAccordianStub -> BrowseAccordian (size=0x24)
    // 0x137de84: mov             x1, x0
    // 0x137de88: ldur            x0, [fp, #-0x38]
    // 0x137de8c: stur            x1, [fp, #-8]
    // 0x137de90: StoreField: r1->field_b = r0
    //     0x137de90: stur            w0, [x1, #0xb]
    // 0x137de94: ldur            x0, [fp, #-0x18]
    // 0x137de98: StoreField: r1->field_f = r0
    //     0x137de98: stur            w0, [x1, #0xf]
    // 0x137de9c: r0 = true
    //     0x137de9c: add             x0, NULL, #0x20  ; true
    // 0x137dea0: StoreField: r1->field_13 = r0
    //     0x137dea0: stur            w0, [x1, #0x13]
    // 0x137dea4: ldur            x0, [fp, #-0x28]
    // 0x137dea8: StoreField: r1->field_1b = r0
    //     0x137dea8: stur            w0, [x1, #0x1b]
    // 0x137deac: ldur            x0, [fp, #-0x30]
    // 0x137deb0: StoreField: r1->field_1f = r0
    //     0x137deb0: stur            w0, [x1, #0x1f]
    // 0x137deb4: r0 = Container()
    //     0x137deb4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x137deb8: stur            x0, [fp, #-0x10]
    // 0x137debc: ldur            x16, [fp, #-0x20]
    // 0x137dec0: r30 = Instance_EdgeInsets
    //     0x137dec0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x137dec4: ldr             lr, [lr, #0x1f0]
    // 0x137dec8: stp             lr, x16, [SP, #8]
    // 0x137decc: ldur            x16, [fp, #-8]
    // 0x137ded0: str             x16, [SP]
    // 0x137ded4: mov             x1, x0
    // 0x137ded8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0x137ded8: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0x137dedc: ldr             x4, [x4, #0xb40]
    // 0x137dee0: r0 = Container()
    //     0x137dee0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x137dee4: ldur            x0, [fp, #-0x10]
    // 0x137dee8: LeaveFrame
    //     0x137dee8: mov             SP, fp
    //     0x137deec: ldp             fp, lr, [SP], #0x10
    // 0x137def0: ret
    //     0x137def0: ret             
    // 0x137def4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137def4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137def8: b               #0x137d674
    // 0x137defc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137defc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137df00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137df00: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137df04: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137df04: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137df08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137df08: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137df0c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137df0c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137df10: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137df10: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137df14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137df14: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137df18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137df18: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137df1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137df1c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137df20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137df20: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x137df24, size: 0xc
    // 0x137df24: r0 = Instance_Padding
    //     0x137df24: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e5a0] Obj!Padding@d68581
    //     0x137df28: ldr             x0, [x0, #0x5a0]
    // 0x137df2c: ret
    //     0x137df2c: ret             
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x137df30, size: 0x298
    // 0x137df30: EnterFrame
    //     0x137df30: stp             fp, lr, [SP, #-0x10]!
    //     0x137df34: mov             fp, SP
    // 0x137df38: AllocStack(0x30)
    //     0x137df38: sub             SP, SP, #0x30
    // 0x137df3c: SetupParameters()
    //     0x137df3c: ldr             x0, [fp, #0x20]
    //     0x137df40: ldur            w1, [x0, #0x17]
    //     0x137df44: add             x1, x1, HEAP, lsl #32
    //     0x137df48: stur            x1, [fp, #-8]
    // 0x137df4c: CheckStackOverflow
    //     0x137df4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137df50: cmp             SP, x16
    //     0x137df54: b.ls            #0x137e1b4
    // 0x137df58: r1 = 1
    //     0x137df58: movz            x1, #0x1
    // 0x137df5c: r0 = AllocateContext()
    //     0x137df5c: bl              #0x16f6108  ; AllocateContextStub
    // 0x137df60: mov             x2, x0
    // 0x137df64: ldur            x0, [fp, #-8]
    // 0x137df68: stur            x2, [fp, #-0x18]
    // 0x137df6c: StoreField: r2->field_b = r0
    //     0x137df6c: stur            w0, [x2, #0xb]
    // 0x137df70: ldr             x3, [fp, #0x10]
    // 0x137df74: StoreField: r2->field_f = r3
    //     0x137df74: stur            w3, [x2, #0xf]
    // 0x137df78: LoadField: r4 = r0->field_b
    //     0x137df78: ldur            w4, [x0, #0xb]
    // 0x137df7c: DecompressPointer r4
    //     0x137df7c: add             x4, x4, HEAP, lsl #32
    // 0x137df80: stur            x4, [fp, #-0x10]
    // 0x137df84: LoadField: r1 = r4->field_b
    //     0x137df84: ldur            w1, [x4, #0xb]
    // 0x137df88: DecompressPointer r1
    //     0x137df88: add             x1, x1, HEAP, lsl #32
    // 0x137df8c: LoadField: r5 = r1->field_f
    //     0x137df8c: ldur            w5, [x1, #0xf]
    // 0x137df90: DecompressPointer r5
    //     0x137df90: add             x5, x5, HEAP, lsl #32
    // 0x137df94: mov             x1, x5
    // 0x137df98: r0 = controller()
    //     0x137df98: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137df9c: LoadField: r1 = r0->field_4b
    //     0x137df9c: ldur            w1, [x0, #0x4b]
    // 0x137dfa0: DecompressPointer r1
    //     0x137dfa0: add             x1, x1, HEAP, lsl #32
    // 0x137dfa4: r0 = value()
    //     0x137dfa4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137dfa8: LoadField: r2 = r0->field_63
    //     0x137dfa8: ldur            w2, [x0, #0x63]
    // 0x137dfac: DecompressPointer r2
    //     0x137dfac: add             x2, x2, HEAP, lsl #32
    // 0x137dfb0: cmp             w2, NULL
    // 0x137dfb4: b.ne            #0x137dfc0
    // 0x137dfb8: r0 = Null
    //     0x137dfb8: mov             x0, NULL
    // 0x137dfbc: b               #0x137e0e8
    // 0x137dfc0: ldur            x0, [fp, #-0x10]
    // 0x137dfc4: LoadField: r1 = r0->field_f
    //     0x137dfc4: ldur            w1, [x0, #0xf]
    // 0x137dfc8: DecompressPointer r1
    //     0x137dfc8: add             x1, x1, HEAP, lsl #32
    // 0x137dfcc: LoadField: r0 = r2->field_b
    //     0x137dfcc: ldur            w0, [x2, #0xb]
    // 0x137dfd0: r3 = LoadInt32Instr(r1)
    //     0x137dfd0: sbfx            x3, x1, #1, #0x1f
    //     0x137dfd4: tbz             w1, #0, #0x137dfdc
    //     0x137dfd8: ldur            x3, [x1, #7]
    // 0x137dfdc: r1 = LoadInt32Instr(r0)
    //     0x137dfdc: sbfx            x1, x0, #1, #0x1f
    // 0x137dfe0: mov             x0, x1
    // 0x137dfe4: mov             x1, x3
    // 0x137dfe8: cmp             x1, x0
    // 0x137dfec: b.hs            #0x137e1bc
    // 0x137dff0: LoadField: r0 = r2->field_f
    //     0x137dff0: ldur            w0, [x2, #0xf]
    // 0x137dff4: DecompressPointer r0
    //     0x137dff4: add             x0, x0, HEAP, lsl #32
    // 0x137dff8: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x137dff8: add             x16, x0, x3, lsl #2
    //     0x137dffc: ldur            w1, [x16, #0xf]
    // 0x137e000: DecompressPointer r1
    //     0x137e000: add             x1, x1, HEAP, lsl #32
    // 0x137e004: cmp             w1, NULL
    // 0x137e008: b.ne            #0x137e014
    // 0x137e00c: r0 = Null
    //     0x137e00c: mov             x0, NULL
    // 0x137e010: b               #0x137e0e8
    // 0x137e014: LoadField: r2 = r1->field_1f
    //     0x137e014: ldur            w2, [x1, #0x1f]
    // 0x137e018: DecompressPointer r2
    //     0x137e018: add             x2, x2, HEAP, lsl #32
    // 0x137e01c: cmp             w2, NULL
    // 0x137e020: b.ne            #0x137e02c
    // 0x137e024: r0 = Null
    //     0x137e024: mov             x0, NULL
    // 0x137e028: b               #0x137e0e8
    // 0x137e02c: ldur            x0, [fp, #-8]
    // 0x137e030: LoadField: r1 = r0->field_f
    //     0x137e030: ldur            w1, [x0, #0xf]
    // 0x137e034: DecompressPointer r1
    //     0x137e034: add             x1, x1, HEAP, lsl #32
    // 0x137e038: LoadField: r0 = r2->field_b
    //     0x137e038: ldur            w0, [x2, #0xb]
    // 0x137e03c: r3 = LoadInt32Instr(r1)
    //     0x137e03c: sbfx            x3, x1, #1, #0x1f
    //     0x137e040: tbz             w1, #0, #0x137e048
    //     0x137e044: ldur            x3, [x1, #7]
    // 0x137e048: r1 = LoadInt32Instr(r0)
    //     0x137e048: sbfx            x1, x0, #1, #0x1f
    // 0x137e04c: mov             x0, x1
    // 0x137e050: mov             x1, x3
    // 0x137e054: cmp             x1, x0
    // 0x137e058: b.hs            #0x137e1c0
    // 0x137e05c: LoadField: r0 = r2->field_f
    //     0x137e05c: ldur            w0, [x2, #0xf]
    // 0x137e060: DecompressPointer r0
    //     0x137e060: add             x0, x0, HEAP, lsl #32
    // 0x137e064: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x137e064: add             x16, x0, x3, lsl #2
    //     0x137e068: ldur            w1, [x16, #0xf]
    // 0x137e06c: DecompressPointer r1
    //     0x137e06c: add             x1, x1, HEAP, lsl #32
    // 0x137e070: cmp             w1, NULL
    // 0x137e074: b.ne            #0x137e080
    // 0x137e078: r0 = Null
    //     0x137e078: mov             x0, NULL
    // 0x137e07c: b               #0x137e0e8
    // 0x137e080: LoadField: r2 = r1->field_1f
    //     0x137e080: ldur            w2, [x1, #0x1f]
    // 0x137e084: DecompressPointer r2
    //     0x137e084: add             x2, x2, HEAP, lsl #32
    // 0x137e088: cmp             w2, NULL
    // 0x137e08c: b.ne            #0x137e098
    // 0x137e090: r0 = Null
    //     0x137e090: mov             x0, NULL
    // 0x137e094: b               #0x137e0e8
    // 0x137e098: ldr             x0, [fp, #0x10]
    // 0x137e09c: LoadField: r1 = r2->field_b
    //     0x137e09c: ldur            w1, [x2, #0xb]
    // 0x137e0a0: r3 = LoadInt32Instr(r0)
    //     0x137e0a0: sbfx            x3, x0, #1, #0x1f
    //     0x137e0a4: tbz             w0, #0, #0x137e0ac
    //     0x137e0a8: ldur            x3, [x0, #7]
    // 0x137e0ac: r0 = LoadInt32Instr(r1)
    //     0x137e0ac: sbfx            x0, x1, #1, #0x1f
    // 0x137e0b0: mov             x1, x3
    // 0x137e0b4: cmp             x1, x0
    // 0x137e0b8: b.hs            #0x137e1c4
    // 0x137e0bc: LoadField: r0 = r2->field_f
    //     0x137e0bc: ldur            w0, [x2, #0xf]
    // 0x137e0c0: DecompressPointer r0
    //     0x137e0c0: add             x0, x0, HEAP, lsl #32
    // 0x137e0c4: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x137e0c4: add             x16, x0, x3, lsl #2
    //     0x137e0c8: ldur            w1, [x16, #0xf]
    // 0x137e0cc: DecompressPointer r1
    //     0x137e0cc: add             x1, x1, HEAP, lsl #32
    // 0x137e0d0: cmp             w1, NULL
    // 0x137e0d4: b.ne            #0x137e0e0
    // 0x137e0d8: r0 = Null
    //     0x137e0d8: mov             x0, NULL
    // 0x137e0dc: b               #0x137e0e8
    // 0x137e0e0: LoadField: r0 = r1->field_f
    //     0x137e0e0: ldur            w0, [x1, #0xf]
    // 0x137e0e4: DecompressPointer r0
    //     0x137e0e4: add             x0, x0, HEAP, lsl #32
    // 0x137e0e8: cmp             w0, NULL
    // 0x137e0ec: b.ne            #0x137e0f4
    // 0x137e0f0: r0 = ""
    //     0x137e0f0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x137e0f4: ldr             x1, [fp, #0x18]
    // 0x137e0f8: stur            x0, [fp, #-8]
    // 0x137e0fc: r0 = of()
    //     0x137e0fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x137e100: LoadField: r1 = r0->field_87
    //     0x137e100: ldur            w1, [x0, #0x87]
    // 0x137e104: DecompressPointer r1
    //     0x137e104: add             x1, x1, HEAP, lsl #32
    // 0x137e108: LoadField: r0 = r1->field_7
    //     0x137e108: ldur            w0, [x1, #7]
    // 0x137e10c: DecompressPointer r0
    //     0x137e10c: add             x0, x0, HEAP, lsl #32
    // 0x137e110: r16 = Instance_Color
    //     0x137e110: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x137e114: r30 = 14.000000
    //     0x137e114: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x137e118: ldr             lr, [lr, #0x1d8]
    // 0x137e11c: stp             lr, x16, [SP]
    // 0x137e120: mov             x1, x0
    // 0x137e124: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x137e124: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x137e128: ldr             x4, [x4, #0x9b8]
    // 0x137e12c: r0 = copyWith()
    //     0x137e12c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x137e130: stur            x0, [fp, #-0x10]
    // 0x137e134: r0 = Text()
    //     0x137e134: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x137e138: mov             x1, x0
    // 0x137e13c: ldur            x0, [fp, #-8]
    // 0x137e140: stur            x1, [fp, #-0x20]
    // 0x137e144: StoreField: r1->field_b = r0
    //     0x137e144: stur            w0, [x1, #0xb]
    // 0x137e148: ldur            x0, [fp, #-0x10]
    // 0x137e14c: StoreField: r1->field_13 = r0
    //     0x137e14c: stur            w0, [x1, #0x13]
    // 0x137e150: r0 = InkWell()
    //     0x137e150: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x137e154: mov             x3, x0
    // 0x137e158: ldur            x0, [fp, #-0x20]
    // 0x137e15c: stur            x3, [fp, #-8]
    // 0x137e160: StoreField: r3->field_b = r0
    //     0x137e160: stur            w0, [x3, #0xb]
    // 0x137e164: ldur            x2, [fp, #-0x18]
    // 0x137e168: r1 = Function '<anonymous closure>':.
    //     0x137e168: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e5a8] AnonymousClosure: (0x137e1c8), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x137e16c: ldr             x1, [x1, #0x5a8]
    // 0x137e170: r0 = AllocateClosure()
    //     0x137e170: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137e174: mov             x1, x0
    // 0x137e178: ldur            x0, [fp, #-8]
    // 0x137e17c: StoreField: r0->field_f = r1
    //     0x137e17c: stur            w1, [x0, #0xf]
    // 0x137e180: r1 = true
    //     0x137e180: add             x1, NULL, #0x20  ; true
    // 0x137e184: StoreField: r0->field_43 = r1
    //     0x137e184: stur            w1, [x0, #0x43]
    // 0x137e188: r2 = Instance_BoxShape
    //     0x137e188: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x137e18c: ldr             x2, [x2, #0x80]
    // 0x137e190: StoreField: r0->field_47 = r2
    //     0x137e190: stur            w2, [x0, #0x47]
    // 0x137e194: StoreField: r0->field_6f = r1
    //     0x137e194: stur            w1, [x0, #0x6f]
    // 0x137e198: r2 = false
    //     0x137e198: add             x2, NULL, #0x30  ; false
    // 0x137e19c: StoreField: r0->field_73 = r2
    //     0x137e19c: stur            w2, [x0, #0x73]
    // 0x137e1a0: StoreField: r0->field_83 = r1
    //     0x137e1a0: stur            w1, [x0, #0x83]
    // 0x137e1a4: StoreField: r0->field_7b = r2
    //     0x137e1a4: stur            w2, [x0, #0x7b]
    // 0x137e1a8: LeaveFrame
    //     0x137e1a8: mov             SP, fp
    //     0x137e1ac: ldp             fp, lr, [SP], #0x10
    // 0x137e1b0: ret
    //     0x137e1b0: ret             
    // 0x137e1b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137e1b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137e1b8: b               #0x137df58
    // 0x137e1bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137e1bc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137e1c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137e1c0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137e1c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137e1c4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x137e1c8, size: 0x460
    // 0x137e1c8: EnterFrame
    //     0x137e1c8: stp             fp, lr, [SP, #-0x10]!
    //     0x137e1cc: mov             fp, SP
    // 0x137e1d0: AllocStack(0x48)
    //     0x137e1d0: sub             SP, SP, #0x48
    // 0x137e1d4: SetupParameters(BrowseView this /* r1 */)
    //     0x137e1d4: stur            NULL, [fp, #-8]
    //     0x137e1d8: movz            x0, #0
    //     0x137e1dc: add             x1, fp, w0, sxtw #2
    //     0x137e1e0: ldr             x1, [x1, #0x10]
    //     0x137e1e4: ldur            w2, [x1, #0x17]
    //     0x137e1e8: add             x2, x2, HEAP, lsl #32
    //     0x137e1ec: stur            x2, [fp, #-0x10]
    // 0x137e1f0: CheckStackOverflow
    //     0x137e1f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137e1f4: cmp             SP, x16
    //     0x137e1f8: b.ls            #0x137e608
    // 0x137e1fc: InitAsync() -> Future<void?>
    //     0x137e1fc: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x137e200: bl              #0x6326e0  ; InitAsyncStub
    // 0x137e204: ldur            x0, [fp, #-0x10]
    // 0x137e208: LoadField: r2 = r0->field_b
    //     0x137e208: ldur            w2, [x0, #0xb]
    // 0x137e20c: DecompressPointer r2
    //     0x137e20c: add             x2, x2, HEAP, lsl #32
    // 0x137e210: stur            x2, [fp, #-0x28]
    // 0x137e214: LoadField: r3 = r2->field_b
    //     0x137e214: ldur            w3, [x2, #0xb]
    // 0x137e218: DecompressPointer r3
    //     0x137e218: add             x3, x3, HEAP, lsl #32
    // 0x137e21c: stur            x3, [fp, #-0x20]
    // 0x137e220: LoadField: r4 = r3->field_b
    //     0x137e220: ldur            w4, [x3, #0xb]
    // 0x137e224: DecompressPointer r4
    //     0x137e224: add             x4, x4, HEAP, lsl #32
    // 0x137e228: stur            x4, [fp, #-0x18]
    // 0x137e22c: LoadField: r1 = r4->field_f
    //     0x137e22c: ldur            w1, [x4, #0xf]
    // 0x137e230: DecompressPointer r1
    //     0x137e230: add             x1, x1, HEAP, lsl #32
    // 0x137e234: r0 = controller()
    //     0x137e234: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137e238: LoadField: r1 = r0->field_4b
    //     0x137e238: ldur            w1, [x0, #0x4b]
    // 0x137e23c: DecompressPointer r1
    //     0x137e23c: add             x1, x1, HEAP, lsl #32
    // 0x137e240: r0 = value()
    //     0x137e240: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137e244: LoadField: r2 = r0->field_63
    //     0x137e244: ldur            w2, [x0, #0x63]
    // 0x137e248: DecompressPointer r2
    //     0x137e248: add             x2, x2, HEAP, lsl #32
    // 0x137e24c: cmp             w2, NULL
    // 0x137e250: b.ne            #0x137e268
    // 0x137e254: ldur            x5, [fp, #-0x10]
    // 0x137e258: ldur            x4, [fp, #-0x28]
    // 0x137e25c: ldur            x3, [fp, #-0x20]
    // 0x137e260: r0 = Null
    //     0x137e260: mov             x0, NULL
    // 0x137e264: b               #0x137e3a8
    // 0x137e268: ldur            x3, [fp, #-0x20]
    // 0x137e26c: LoadField: r0 = r3->field_f
    //     0x137e26c: ldur            w0, [x3, #0xf]
    // 0x137e270: DecompressPointer r0
    //     0x137e270: add             x0, x0, HEAP, lsl #32
    // 0x137e274: LoadField: r1 = r2->field_b
    //     0x137e274: ldur            w1, [x2, #0xb]
    // 0x137e278: r4 = LoadInt32Instr(r0)
    //     0x137e278: sbfx            x4, x0, #1, #0x1f
    //     0x137e27c: tbz             w0, #0, #0x137e284
    //     0x137e280: ldur            x4, [x0, #7]
    // 0x137e284: r0 = LoadInt32Instr(r1)
    //     0x137e284: sbfx            x0, x1, #1, #0x1f
    // 0x137e288: mov             x1, x4
    // 0x137e28c: cmp             x1, x0
    // 0x137e290: b.hs            #0x137e610
    // 0x137e294: LoadField: r0 = r2->field_f
    //     0x137e294: ldur            w0, [x2, #0xf]
    // 0x137e298: DecompressPointer r0
    //     0x137e298: add             x0, x0, HEAP, lsl #32
    // 0x137e29c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x137e29c: add             x16, x0, x4, lsl #2
    //     0x137e2a0: ldur            w1, [x16, #0xf]
    // 0x137e2a4: DecompressPointer r1
    //     0x137e2a4: add             x1, x1, HEAP, lsl #32
    // 0x137e2a8: cmp             w1, NULL
    // 0x137e2ac: b.ne            #0x137e2c0
    // 0x137e2b0: ldur            x5, [fp, #-0x10]
    // 0x137e2b4: ldur            x4, [fp, #-0x28]
    // 0x137e2b8: r0 = Null
    //     0x137e2b8: mov             x0, NULL
    // 0x137e2bc: b               #0x137e3a8
    // 0x137e2c0: LoadField: r2 = r1->field_1f
    //     0x137e2c0: ldur            w2, [x1, #0x1f]
    // 0x137e2c4: DecompressPointer r2
    //     0x137e2c4: add             x2, x2, HEAP, lsl #32
    // 0x137e2c8: cmp             w2, NULL
    // 0x137e2cc: b.ne            #0x137e2e0
    // 0x137e2d0: ldur            x5, [fp, #-0x10]
    // 0x137e2d4: ldur            x4, [fp, #-0x28]
    // 0x137e2d8: r0 = Null
    //     0x137e2d8: mov             x0, NULL
    // 0x137e2dc: b               #0x137e3a8
    // 0x137e2e0: ldur            x4, [fp, #-0x28]
    // 0x137e2e4: LoadField: r0 = r4->field_f
    //     0x137e2e4: ldur            w0, [x4, #0xf]
    // 0x137e2e8: DecompressPointer r0
    //     0x137e2e8: add             x0, x0, HEAP, lsl #32
    // 0x137e2ec: LoadField: r1 = r2->field_b
    //     0x137e2ec: ldur            w1, [x2, #0xb]
    // 0x137e2f0: r5 = LoadInt32Instr(r0)
    //     0x137e2f0: sbfx            x5, x0, #1, #0x1f
    //     0x137e2f4: tbz             w0, #0, #0x137e2fc
    //     0x137e2f8: ldur            x5, [x0, #7]
    // 0x137e2fc: r0 = LoadInt32Instr(r1)
    //     0x137e2fc: sbfx            x0, x1, #1, #0x1f
    // 0x137e300: mov             x1, x5
    // 0x137e304: cmp             x1, x0
    // 0x137e308: b.hs            #0x137e614
    // 0x137e30c: LoadField: r0 = r2->field_f
    //     0x137e30c: ldur            w0, [x2, #0xf]
    // 0x137e310: DecompressPointer r0
    //     0x137e310: add             x0, x0, HEAP, lsl #32
    // 0x137e314: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x137e314: add             x16, x0, x5, lsl #2
    //     0x137e318: ldur            w1, [x16, #0xf]
    // 0x137e31c: DecompressPointer r1
    //     0x137e31c: add             x1, x1, HEAP, lsl #32
    // 0x137e320: cmp             w1, NULL
    // 0x137e324: b.ne            #0x137e334
    // 0x137e328: ldur            x5, [fp, #-0x10]
    // 0x137e32c: r0 = Null
    //     0x137e32c: mov             x0, NULL
    // 0x137e330: b               #0x137e3a8
    // 0x137e334: LoadField: r2 = r1->field_1f
    //     0x137e334: ldur            w2, [x1, #0x1f]
    // 0x137e338: DecompressPointer r2
    //     0x137e338: add             x2, x2, HEAP, lsl #32
    // 0x137e33c: cmp             w2, NULL
    // 0x137e340: b.ne            #0x137e350
    // 0x137e344: ldur            x5, [fp, #-0x10]
    // 0x137e348: r0 = Null
    //     0x137e348: mov             x0, NULL
    // 0x137e34c: b               #0x137e3a8
    // 0x137e350: ldur            x5, [fp, #-0x10]
    // 0x137e354: LoadField: r0 = r5->field_f
    //     0x137e354: ldur            w0, [x5, #0xf]
    // 0x137e358: DecompressPointer r0
    //     0x137e358: add             x0, x0, HEAP, lsl #32
    // 0x137e35c: LoadField: r1 = r2->field_b
    //     0x137e35c: ldur            w1, [x2, #0xb]
    // 0x137e360: r6 = LoadInt32Instr(r0)
    //     0x137e360: sbfx            x6, x0, #1, #0x1f
    //     0x137e364: tbz             w0, #0, #0x137e36c
    //     0x137e368: ldur            x6, [x0, #7]
    // 0x137e36c: r0 = LoadInt32Instr(r1)
    //     0x137e36c: sbfx            x0, x1, #1, #0x1f
    // 0x137e370: mov             x1, x6
    // 0x137e374: cmp             x1, x0
    // 0x137e378: b.hs            #0x137e618
    // 0x137e37c: LoadField: r0 = r2->field_f
    //     0x137e37c: ldur            w0, [x2, #0xf]
    // 0x137e380: DecompressPointer r0
    //     0x137e380: add             x0, x0, HEAP, lsl #32
    // 0x137e384: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0x137e384: add             x16, x0, x6, lsl #2
    //     0x137e388: ldur            w1, [x16, #0xf]
    // 0x137e38c: DecompressPointer r1
    //     0x137e38c: add             x1, x1, HEAP, lsl #32
    // 0x137e390: cmp             w1, NULL
    // 0x137e394: b.ne            #0x137e3a0
    // 0x137e398: r0 = Null
    //     0x137e398: mov             x0, NULL
    // 0x137e39c: b               #0x137e3a8
    // 0x137e3a0: LoadField: r0 = r1->field_13
    //     0x137e3a0: ldur            w0, [x1, #0x13]
    // 0x137e3a4: DecompressPointer r0
    //     0x137e3a4: add             x0, x0, HEAP, lsl #32
    // 0x137e3a8: r1 = LoadClassIdInstr(r0)
    //     0x137e3a8: ldur            x1, [x0, #-1]
    //     0x137e3ac: ubfx            x1, x1, #0xc, #0x14
    // 0x137e3b0: r16 = ""
    //     0x137e3b0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x137e3b4: stp             x16, x0, [SP]
    // 0x137e3b8: mov             x0, x1
    // 0x137e3bc: mov             lr, x0
    // 0x137e3c0: ldr             lr, [x21, lr, lsl #3]
    // 0x137e3c4: blr             lr
    // 0x137e3c8: tbz             w0, #4, #0x137e600
    // 0x137e3cc: ldur            x0, [fp, #-0x18]
    // 0x137e3d0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x137e3d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x137e3d4: ldr             x0, [x0, #0x1c80]
    //     0x137e3d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x137e3dc: cmp             w0, w16
    //     0x137e3e0: b.ne            #0x137e3ec
    //     0x137e3e4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x137e3e8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x137e3ec: r1 = Null
    //     0x137e3ec: mov             x1, NULL
    // 0x137e3f0: r2 = 12
    //     0x137e3f0: movz            x2, #0xc
    // 0x137e3f4: r0 = AllocateArray()
    //     0x137e3f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x137e3f8: stur            x0, [fp, #-0x30]
    // 0x137e3fc: r16 = "link"
    //     0x137e3fc: ldr             x16, [PP, #0x7c28]  ; [pp+0x7c28] "link"
    // 0x137e400: StoreField: r0->field_f = r16
    //     0x137e400: stur            w16, [x0, #0xf]
    // 0x137e404: ldur            x1, [fp, #-0x18]
    // 0x137e408: LoadField: r2 = r1->field_f
    //     0x137e408: ldur            w2, [x1, #0xf]
    // 0x137e40c: DecompressPointer r2
    //     0x137e40c: add             x2, x2, HEAP, lsl #32
    // 0x137e410: mov             x1, x2
    // 0x137e414: r0 = controller()
    //     0x137e414: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137e418: LoadField: r1 = r0->field_4b
    //     0x137e418: ldur            w1, [x0, #0x4b]
    // 0x137e41c: DecompressPointer r1
    //     0x137e41c: add             x1, x1, HEAP, lsl #32
    // 0x137e420: r0 = value()
    //     0x137e420: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137e424: LoadField: r2 = r0->field_63
    //     0x137e424: ldur            w2, [x0, #0x63]
    // 0x137e428: DecompressPointer r2
    //     0x137e428: add             x2, x2, HEAP, lsl #32
    // 0x137e42c: cmp             w2, NULL
    // 0x137e430: b.ne            #0x137e43c
    // 0x137e434: r0 = Null
    //     0x137e434: mov             x0, NULL
    // 0x137e438: b               #0x137e570
    // 0x137e43c: ldur            x0, [fp, #-0x20]
    // 0x137e440: LoadField: r1 = r0->field_f
    //     0x137e440: ldur            w1, [x0, #0xf]
    // 0x137e444: DecompressPointer r1
    //     0x137e444: add             x1, x1, HEAP, lsl #32
    // 0x137e448: LoadField: r0 = r2->field_b
    //     0x137e448: ldur            w0, [x2, #0xb]
    // 0x137e44c: r3 = LoadInt32Instr(r1)
    //     0x137e44c: sbfx            x3, x1, #1, #0x1f
    //     0x137e450: tbz             w1, #0, #0x137e458
    //     0x137e454: ldur            x3, [x1, #7]
    // 0x137e458: r1 = LoadInt32Instr(r0)
    //     0x137e458: sbfx            x1, x0, #1, #0x1f
    // 0x137e45c: mov             x0, x1
    // 0x137e460: mov             x1, x3
    // 0x137e464: cmp             x1, x0
    // 0x137e468: b.hs            #0x137e61c
    // 0x137e46c: LoadField: r0 = r2->field_f
    //     0x137e46c: ldur            w0, [x2, #0xf]
    // 0x137e470: DecompressPointer r0
    //     0x137e470: add             x0, x0, HEAP, lsl #32
    // 0x137e474: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x137e474: add             x16, x0, x3, lsl #2
    //     0x137e478: ldur            w1, [x16, #0xf]
    // 0x137e47c: DecompressPointer r1
    //     0x137e47c: add             x1, x1, HEAP, lsl #32
    // 0x137e480: cmp             w1, NULL
    // 0x137e484: b.ne            #0x137e490
    // 0x137e488: r0 = Null
    //     0x137e488: mov             x0, NULL
    // 0x137e48c: b               #0x137e570
    // 0x137e490: LoadField: r2 = r1->field_1f
    //     0x137e490: ldur            w2, [x1, #0x1f]
    // 0x137e494: DecompressPointer r2
    //     0x137e494: add             x2, x2, HEAP, lsl #32
    // 0x137e498: cmp             w2, NULL
    // 0x137e49c: b.ne            #0x137e4a8
    // 0x137e4a0: r0 = Null
    //     0x137e4a0: mov             x0, NULL
    // 0x137e4a4: b               #0x137e570
    // 0x137e4a8: ldur            x0, [fp, #-0x28]
    // 0x137e4ac: LoadField: r1 = r0->field_f
    //     0x137e4ac: ldur            w1, [x0, #0xf]
    // 0x137e4b0: DecompressPointer r1
    //     0x137e4b0: add             x1, x1, HEAP, lsl #32
    // 0x137e4b4: LoadField: r0 = r2->field_b
    //     0x137e4b4: ldur            w0, [x2, #0xb]
    // 0x137e4b8: r3 = LoadInt32Instr(r1)
    //     0x137e4b8: sbfx            x3, x1, #1, #0x1f
    //     0x137e4bc: tbz             w1, #0, #0x137e4c4
    //     0x137e4c0: ldur            x3, [x1, #7]
    // 0x137e4c4: r1 = LoadInt32Instr(r0)
    //     0x137e4c4: sbfx            x1, x0, #1, #0x1f
    // 0x137e4c8: mov             x0, x1
    // 0x137e4cc: mov             x1, x3
    // 0x137e4d0: cmp             x1, x0
    // 0x137e4d4: b.hs            #0x137e620
    // 0x137e4d8: LoadField: r0 = r2->field_f
    //     0x137e4d8: ldur            w0, [x2, #0xf]
    // 0x137e4dc: DecompressPointer r0
    //     0x137e4dc: add             x0, x0, HEAP, lsl #32
    // 0x137e4e0: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x137e4e0: add             x16, x0, x3, lsl #2
    //     0x137e4e4: ldur            w1, [x16, #0xf]
    // 0x137e4e8: DecompressPointer r1
    //     0x137e4e8: add             x1, x1, HEAP, lsl #32
    // 0x137e4ec: cmp             w1, NULL
    // 0x137e4f0: b.ne            #0x137e4fc
    // 0x137e4f4: r0 = Null
    //     0x137e4f4: mov             x0, NULL
    // 0x137e4f8: b               #0x137e570
    // 0x137e4fc: LoadField: r2 = r1->field_1f
    //     0x137e4fc: ldur            w2, [x1, #0x1f]
    // 0x137e500: DecompressPointer r2
    //     0x137e500: add             x2, x2, HEAP, lsl #32
    // 0x137e504: cmp             w2, NULL
    // 0x137e508: b.ne            #0x137e514
    // 0x137e50c: r0 = Null
    //     0x137e50c: mov             x0, NULL
    // 0x137e510: b               #0x137e570
    // 0x137e514: ldur            x0, [fp, #-0x10]
    // 0x137e518: LoadField: r1 = r0->field_f
    //     0x137e518: ldur            w1, [x0, #0xf]
    // 0x137e51c: DecompressPointer r1
    //     0x137e51c: add             x1, x1, HEAP, lsl #32
    // 0x137e520: LoadField: r0 = r2->field_b
    //     0x137e520: ldur            w0, [x2, #0xb]
    // 0x137e524: r3 = LoadInt32Instr(r1)
    //     0x137e524: sbfx            x3, x1, #1, #0x1f
    //     0x137e528: tbz             w1, #0, #0x137e530
    //     0x137e52c: ldur            x3, [x1, #7]
    // 0x137e530: r1 = LoadInt32Instr(r0)
    //     0x137e530: sbfx            x1, x0, #1, #0x1f
    // 0x137e534: mov             x0, x1
    // 0x137e538: mov             x1, x3
    // 0x137e53c: cmp             x1, x0
    // 0x137e540: b.hs            #0x137e624
    // 0x137e544: LoadField: r0 = r2->field_f
    //     0x137e544: ldur            w0, [x2, #0xf]
    // 0x137e548: DecompressPointer r0
    //     0x137e548: add             x0, x0, HEAP, lsl #32
    // 0x137e54c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x137e54c: add             x16, x0, x3, lsl #2
    //     0x137e550: ldur            w1, [x16, #0xf]
    // 0x137e554: DecompressPointer r1
    //     0x137e554: add             x1, x1, HEAP, lsl #32
    // 0x137e558: cmp             w1, NULL
    // 0x137e55c: b.ne            #0x137e568
    // 0x137e560: r0 = Null
    //     0x137e560: mov             x0, NULL
    // 0x137e564: b               #0x137e570
    // 0x137e568: LoadField: r0 = r1->field_13
    //     0x137e568: ldur            w0, [x1, #0x13]
    // 0x137e56c: DecompressPointer r0
    //     0x137e56c: add             x0, x0, HEAP, lsl #32
    // 0x137e570: ldur            x1, [fp, #-0x30]
    // 0x137e574: str             x0, [SP]
    // 0x137e578: r0 = _interpolateSingle()
    //     0x137e578: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x137e57c: ldur            x1, [fp, #-0x30]
    // 0x137e580: ArrayStore: r1[1] = r0  ; List_4
    //     0x137e580: add             x25, x1, #0x13
    //     0x137e584: str             w0, [x25]
    //     0x137e588: tbz             w0, #0, #0x137e5a4
    //     0x137e58c: ldurb           w16, [x1, #-1]
    //     0x137e590: ldurb           w17, [x0, #-1]
    //     0x137e594: and             x16, x17, x16, lsr #2
    //     0x137e598: tst             x16, HEAP, lsr #32
    //     0x137e59c: b.eq            #0x137e5a4
    //     0x137e5a0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x137e5a4: ldur            x0, [fp, #-0x30]
    // 0x137e5a8: r16 = "previousScreenSource"
    //     0x137e5a8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x137e5ac: ldr             x16, [x16, #0x448]
    // 0x137e5b0: ArrayStore: r0[0] = r16  ; List_4
    //     0x137e5b0: stur            w16, [x0, #0x17]
    // 0x137e5b4: r16 = ""
    //     0x137e5b4: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x137e5b8: StoreField: r0->field_1b = r16
    //     0x137e5b8: stur            w16, [x0, #0x1b]
    // 0x137e5bc: r16 = "screenSource"
    //     0x137e5bc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0x137e5c0: ldr             x16, [x16, #0x450]
    // 0x137e5c4: StoreField: r0->field_1f = r16
    //     0x137e5c4: stur            w16, [x0, #0x1f]
    // 0x137e5c8: r16 = "browse"
    //     0x137e5c8: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e5b0] "browse"
    //     0x137e5cc: ldr             x16, [x16, #0x5b0]
    // 0x137e5d0: StoreField: r0->field_23 = r16
    //     0x137e5d0: stur            w16, [x0, #0x23]
    // 0x137e5d4: r16 = <String, String>
    //     0x137e5d4: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x137e5d8: ldr             x16, [x16, #0x788]
    // 0x137e5dc: stp             x0, x16, [SP]
    // 0x137e5e0: r0 = Map._fromLiteral()
    //     0x137e5e0: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x137e5e4: r16 = "/collection"
    //     0x137e5e4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb458] "/collection"
    //     0x137e5e8: ldr             x16, [x16, #0x458]
    // 0x137e5ec: stp             x16, NULL, [SP, #8]
    // 0x137e5f0: str             x0, [SP]
    // 0x137e5f4: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x137e5f4: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x137e5f8: ldr             x4, [x4, #0x438]
    // 0x137e5fc: r0 = GetNavigation.toNamed()
    //     0x137e5fc: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x137e600: r0 = Null
    //     0x137e600: mov             x0, NULL
    // 0x137e604: r0 = ReturnAsyncNotFuture()
    //     0x137e604: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x137e608: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137e608: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137e60c: b               #0x137e1fc
    // 0x137e610: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137e610: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137e614: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137e614: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137e618: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137e618: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137e61c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137e61c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137e620: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137e620: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137e624: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137e624: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x137e628, size: 0x354
    // 0x137e628: EnterFrame
    //     0x137e628: stp             fp, lr, [SP, #-0x10]!
    //     0x137e62c: mov             fp, SP
    // 0x137e630: AllocStack(0x38)
    //     0x137e630: sub             SP, SP, #0x38
    // 0x137e634: SetupParameters()
    //     0x137e634: ldr             x0, [fp, #0x10]
    //     0x137e638: ldur            w2, [x0, #0x17]
    //     0x137e63c: add             x2, x2, HEAP, lsl #32
    //     0x137e640: stur            x2, [fp, #-0x18]
    // 0x137e644: CheckStackOverflow
    //     0x137e644: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137e648: cmp             SP, x16
    //     0x137e64c: b.ls            #0x137e964
    // 0x137e650: LoadField: r0 = r2->field_b
    //     0x137e650: ldur            w0, [x2, #0xb]
    // 0x137e654: DecompressPointer r0
    //     0x137e654: add             x0, x0, HEAP, lsl #32
    // 0x137e658: stur            x0, [fp, #-0x10]
    // 0x137e65c: LoadField: r3 = r0->field_b
    //     0x137e65c: ldur            w3, [x0, #0xb]
    // 0x137e660: DecompressPointer r3
    //     0x137e660: add             x3, x3, HEAP, lsl #32
    // 0x137e664: stur            x3, [fp, #-8]
    // 0x137e668: LoadField: r1 = r3->field_f
    //     0x137e668: ldur            w1, [x3, #0xf]
    // 0x137e66c: DecompressPointer r1
    //     0x137e66c: add             x1, x1, HEAP, lsl #32
    // 0x137e670: r0 = controller()
    //     0x137e670: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137e674: LoadField: r1 = r0->field_4b
    //     0x137e674: ldur            w1, [x0, #0x4b]
    // 0x137e678: DecompressPointer r1
    //     0x137e678: add             x1, x1, HEAP, lsl #32
    // 0x137e67c: r0 = value()
    //     0x137e67c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137e680: LoadField: r2 = r0->field_63
    //     0x137e680: ldur            w2, [x0, #0x63]
    // 0x137e684: DecompressPointer r2
    //     0x137e684: add             x2, x2, HEAP, lsl #32
    // 0x137e688: cmp             w2, NULL
    // 0x137e68c: b.ne            #0x137e6a0
    // 0x137e690: ldur            x4, [fp, #-0x18]
    // 0x137e694: ldur            x3, [fp, #-0x10]
    // 0x137e698: r0 = Null
    //     0x137e698: mov             x0, NULL
    // 0x137e69c: b               #0x137e768
    // 0x137e6a0: ldur            x3, [fp, #-0x10]
    // 0x137e6a4: LoadField: r0 = r3->field_f
    //     0x137e6a4: ldur            w0, [x3, #0xf]
    // 0x137e6a8: DecompressPointer r0
    //     0x137e6a8: add             x0, x0, HEAP, lsl #32
    // 0x137e6ac: LoadField: r1 = r2->field_b
    //     0x137e6ac: ldur            w1, [x2, #0xb]
    // 0x137e6b0: r4 = LoadInt32Instr(r0)
    //     0x137e6b0: sbfx            x4, x0, #1, #0x1f
    //     0x137e6b4: tbz             w0, #0, #0x137e6bc
    //     0x137e6b8: ldur            x4, [x0, #7]
    // 0x137e6bc: r0 = LoadInt32Instr(r1)
    //     0x137e6bc: sbfx            x0, x1, #1, #0x1f
    // 0x137e6c0: mov             x1, x4
    // 0x137e6c4: cmp             x1, x0
    // 0x137e6c8: b.hs            #0x137e96c
    // 0x137e6cc: LoadField: r0 = r2->field_f
    //     0x137e6cc: ldur            w0, [x2, #0xf]
    // 0x137e6d0: DecompressPointer r0
    //     0x137e6d0: add             x0, x0, HEAP, lsl #32
    // 0x137e6d4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x137e6d4: add             x16, x0, x4, lsl #2
    //     0x137e6d8: ldur            w1, [x16, #0xf]
    // 0x137e6dc: DecompressPointer r1
    //     0x137e6dc: add             x1, x1, HEAP, lsl #32
    // 0x137e6e0: cmp             w1, NULL
    // 0x137e6e4: b.ne            #0x137e6f4
    // 0x137e6e8: ldur            x4, [fp, #-0x18]
    // 0x137e6ec: r0 = Null
    //     0x137e6ec: mov             x0, NULL
    // 0x137e6f0: b               #0x137e768
    // 0x137e6f4: LoadField: r2 = r1->field_1f
    //     0x137e6f4: ldur            w2, [x1, #0x1f]
    // 0x137e6f8: DecompressPointer r2
    //     0x137e6f8: add             x2, x2, HEAP, lsl #32
    // 0x137e6fc: cmp             w2, NULL
    // 0x137e700: b.ne            #0x137e710
    // 0x137e704: ldur            x4, [fp, #-0x18]
    // 0x137e708: r0 = Null
    //     0x137e708: mov             x0, NULL
    // 0x137e70c: b               #0x137e768
    // 0x137e710: ldur            x4, [fp, #-0x18]
    // 0x137e714: LoadField: r0 = r4->field_f
    //     0x137e714: ldur            w0, [x4, #0xf]
    // 0x137e718: DecompressPointer r0
    //     0x137e718: add             x0, x0, HEAP, lsl #32
    // 0x137e71c: LoadField: r1 = r2->field_b
    //     0x137e71c: ldur            w1, [x2, #0xb]
    // 0x137e720: r5 = LoadInt32Instr(r0)
    //     0x137e720: sbfx            x5, x0, #1, #0x1f
    //     0x137e724: tbz             w0, #0, #0x137e72c
    //     0x137e728: ldur            x5, [x0, #7]
    // 0x137e72c: r0 = LoadInt32Instr(r1)
    //     0x137e72c: sbfx            x0, x1, #1, #0x1f
    // 0x137e730: mov             x1, x5
    // 0x137e734: cmp             x1, x0
    // 0x137e738: b.hs            #0x137e970
    // 0x137e73c: LoadField: r0 = r2->field_f
    //     0x137e73c: ldur            w0, [x2, #0xf]
    // 0x137e740: DecompressPointer r0
    //     0x137e740: add             x0, x0, HEAP, lsl #32
    // 0x137e744: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x137e744: add             x16, x0, x5, lsl #2
    //     0x137e748: ldur            w1, [x16, #0xf]
    // 0x137e74c: DecompressPointer r1
    //     0x137e74c: add             x1, x1, HEAP, lsl #32
    // 0x137e750: cmp             w1, NULL
    // 0x137e754: b.ne            #0x137e760
    // 0x137e758: r0 = Null
    //     0x137e758: mov             x0, NULL
    // 0x137e75c: b               #0x137e768
    // 0x137e760: LoadField: r0 = r1->field_13
    //     0x137e760: ldur            w0, [x1, #0x13]
    // 0x137e764: DecompressPointer r0
    //     0x137e764: add             x0, x0, HEAP, lsl #32
    // 0x137e768: r1 = LoadClassIdInstr(r0)
    //     0x137e768: ldur            x1, [x0, #-1]
    //     0x137e76c: ubfx            x1, x1, #0xc, #0x14
    // 0x137e770: r16 = ""
    //     0x137e770: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x137e774: stp             x16, x0, [SP]
    // 0x137e778: mov             x0, x1
    // 0x137e77c: mov             lr, x0
    // 0x137e780: ldr             lr, [x21, lr, lsl #3]
    // 0x137e784: blr             lr
    // 0x137e788: tbz             w0, #4, #0x137e954
    // 0x137e78c: ldur            x0, [fp, #-8]
    // 0x137e790: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x137e790: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x137e794: ldr             x0, [x0, #0x1c80]
    //     0x137e798: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x137e79c: cmp             w0, w16
    //     0x137e7a0: b.ne            #0x137e7ac
    //     0x137e7a4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x137e7a8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x137e7ac: r1 = Null
    //     0x137e7ac: mov             x1, NULL
    // 0x137e7b0: r2 = 12
    //     0x137e7b0: movz            x2, #0xc
    // 0x137e7b4: r0 = AllocateArray()
    //     0x137e7b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x137e7b8: stur            x0, [fp, #-0x20]
    // 0x137e7bc: r16 = "link"
    //     0x137e7bc: ldr             x16, [PP, #0x7c28]  ; [pp+0x7c28] "link"
    // 0x137e7c0: StoreField: r0->field_f = r16
    //     0x137e7c0: stur            w16, [x0, #0xf]
    // 0x137e7c4: ldur            x1, [fp, #-8]
    // 0x137e7c8: LoadField: r2 = r1->field_f
    //     0x137e7c8: ldur            w2, [x1, #0xf]
    // 0x137e7cc: DecompressPointer r2
    //     0x137e7cc: add             x2, x2, HEAP, lsl #32
    // 0x137e7d0: mov             x1, x2
    // 0x137e7d4: r0 = controller()
    //     0x137e7d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137e7d8: LoadField: r1 = r0->field_4b
    //     0x137e7d8: ldur            w1, [x0, #0x4b]
    // 0x137e7dc: DecompressPointer r1
    //     0x137e7dc: add             x1, x1, HEAP, lsl #32
    // 0x137e7e0: r0 = value()
    //     0x137e7e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137e7e4: LoadField: r2 = r0->field_63
    //     0x137e7e4: ldur            w2, [x0, #0x63]
    // 0x137e7e8: DecompressPointer r2
    //     0x137e7e8: add             x2, x2, HEAP, lsl #32
    // 0x137e7ec: cmp             w2, NULL
    // 0x137e7f0: b.ne            #0x137e7fc
    // 0x137e7f4: r0 = Null
    //     0x137e7f4: mov             x0, NULL
    // 0x137e7f8: b               #0x137e8c4
    // 0x137e7fc: ldur            x0, [fp, #-0x10]
    // 0x137e800: LoadField: r1 = r0->field_f
    //     0x137e800: ldur            w1, [x0, #0xf]
    // 0x137e804: DecompressPointer r1
    //     0x137e804: add             x1, x1, HEAP, lsl #32
    // 0x137e808: LoadField: r0 = r2->field_b
    //     0x137e808: ldur            w0, [x2, #0xb]
    // 0x137e80c: r3 = LoadInt32Instr(r1)
    //     0x137e80c: sbfx            x3, x1, #1, #0x1f
    //     0x137e810: tbz             w1, #0, #0x137e818
    //     0x137e814: ldur            x3, [x1, #7]
    // 0x137e818: r1 = LoadInt32Instr(r0)
    //     0x137e818: sbfx            x1, x0, #1, #0x1f
    // 0x137e81c: mov             x0, x1
    // 0x137e820: mov             x1, x3
    // 0x137e824: cmp             x1, x0
    // 0x137e828: b.hs            #0x137e974
    // 0x137e82c: LoadField: r0 = r2->field_f
    //     0x137e82c: ldur            w0, [x2, #0xf]
    // 0x137e830: DecompressPointer r0
    //     0x137e830: add             x0, x0, HEAP, lsl #32
    // 0x137e834: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x137e834: add             x16, x0, x3, lsl #2
    //     0x137e838: ldur            w1, [x16, #0xf]
    // 0x137e83c: DecompressPointer r1
    //     0x137e83c: add             x1, x1, HEAP, lsl #32
    // 0x137e840: cmp             w1, NULL
    // 0x137e844: b.ne            #0x137e850
    // 0x137e848: r0 = Null
    //     0x137e848: mov             x0, NULL
    // 0x137e84c: b               #0x137e8c4
    // 0x137e850: LoadField: r2 = r1->field_1f
    //     0x137e850: ldur            w2, [x1, #0x1f]
    // 0x137e854: DecompressPointer r2
    //     0x137e854: add             x2, x2, HEAP, lsl #32
    // 0x137e858: cmp             w2, NULL
    // 0x137e85c: b.ne            #0x137e868
    // 0x137e860: r0 = Null
    //     0x137e860: mov             x0, NULL
    // 0x137e864: b               #0x137e8c4
    // 0x137e868: ldur            x0, [fp, #-0x18]
    // 0x137e86c: LoadField: r1 = r0->field_f
    //     0x137e86c: ldur            w1, [x0, #0xf]
    // 0x137e870: DecompressPointer r1
    //     0x137e870: add             x1, x1, HEAP, lsl #32
    // 0x137e874: LoadField: r0 = r2->field_b
    //     0x137e874: ldur            w0, [x2, #0xb]
    // 0x137e878: r3 = LoadInt32Instr(r1)
    //     0x137e878: sbfx            x3, x1, #1, #0x1f
    //     0x137e87c: tbz             w1, #0, #0x137e884
    //     0x137e880: ldur            x3, [x1, #7]
    // 0x137e884: r1 = LoadInt32Instr(r0)
    //     0x137e884: sbfx            x1, x0, #1, #0x1f
    // 0x137e888: mov             x0, x1
    // 0x137e88c: mov             x1, x3
    // 0x137e890: cmp             x1, x0
    // 0x137e894: b.hs            #0x137e978
    // 0x137e898: LoadField: r0 = r2->field_f
    //     0x137e898: ldur            w0, [x2, #0xf]
    // 0x137e89c: DecompressPointer r0
    //     0x137e89c: add             x0, x0, HEAP, lsl #32
    // 0x137e8a0: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x137e8a0: add             x16, x0, x3, lsl #2
    //     0x137e8a4: ldur            w1, [x16, #0xf]
    // 0x137e8a8: DecompressPointer r1
    //     0x137e8a8: add             x1, x1, HEAP, lsl #32
    // 0x137e8ac: cmp             w1, NULL
    // 0x137e8b0: b.ne            #0x137e8bc
    // 0x137e8b4: r0 = Null
    //     0x137e8b4: mov             x0, NULL
    // 0x137e8b8: b               #0x137e8c4
    // 0x137e8bc: LoadField: r0 = r1->field_13
    //     0x137e8bc: ldur            w0, [x1, #0x13]
    // 0x137e8c0: DecompressPointer r0
    //     0x137e8c0: add             x0, x0, HEAP, lsl #32
    // 0x137e8c4: ldur            x1, [fp, #-0x20]
    // 0x137e8c8: str             x0, [SP]
    // 0x137e8cc: r0 = _interpolateSingle()
    //     0x137e8cc: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x137e8d0: ldur            x1, [fp, #-0x20]
    // 0x137e8d4: ArrayStore: r1[1] = r0  ; List_4
    //     0x137e8d4: add             x25, x1, #0x13
    //     0x137e8d8: str             w0, [x25]
    //     0x137e8dc: tbz             w0, #0, #0x137e8f8
    //     0x137e8e0: ldurb           w16, [x1, #-1]
    //     0x137e8e4: ldurb           w17, [x0, #-1]
    //     0x137e8e8: and             x16, x17, x16, lsr #2
    //     0x137e8ec: tst             x16, HEAP, lsr #32
    //     0x137e8f0: b.eq            #0x137e8f8
    //     0x137e8f4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x137e8f8: ldur            x0, [fp, #-0x20]
    // 0x137e8fc: r16 = "previousScreenSource"
    //     0x137e8fc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x137e900: ldr             x16, [x16, #0x448]
    // 0x137e904: ArrayStore: r0[0] = r16  ; List_4
    //     0x137e904: stur            w16, [x0, #0x17]
    // 0x137e908: r16 = ""
    //     0x137e908: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x137e90c: StoreField: r0->field_1b = r16
    //     0x137e90c: stur            w16, [x0, #0x1b]
    // 0x137e910: r16 = "screenSource"
    //     0x137e910: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0x137e914: ldr             x16, [x16, #0x450]
    // 0x137e918: StoreField: r0->field_1f = r16
    //     0x137e918: stur            w16, [x0, #0x1f]
    // 0x137e91c: r16 = "browse"
    //     0x137e91c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e5b0] "browse"
    //     0x137e920: ldr             x16, [x16, #0x5b0]
    // 0x137e924: StoreField: r0->field_23 = r16
    //     0x137e924: stur            w16, [x0, #0x23]
    // 0x137e928: r16 = <String, String>
    //     0x137e928: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x137e92c: ldr             x16, [x16, #0x788]
    // 0x137e930: stp             x0, x16, [SP]
    // 0x137e934: r0 = Map._fromLiteral()
    //     0x137e934: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x137e938: r16 = "/collection"
    //     0x137e938: add             x16, PP, #0xb, lsl #12  ; [pp+0xb458] "/collection"
    //     0x137e93c: ldr             x16, [x16, #0x458]
    // 0x137e940: stp             x16, NULL, [SP, #8]
    // 0x137e944: str             x0, [SP]
    // 0x137e948: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x137e948: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x137e94c: ldr             x4, [x4, #0x438]
    // 0x137e950: r0 = GetNavigation.toNamed()
    //     0x137e950: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x137e954: r0 = Null
    //     0x137e954: mov             x0, NULL
    // 0x137e958: LeaveFrame
    //     0x137e958: mov             SP, fp
    //     0x137e95c: ldp             fp, lr, [SP], #0x10
    // 0x137e960: ret
    //     0x137e960: ret             
    // 0x137e964: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137e964: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137e968: b               #0x137e650
    // 0x137e96c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137e96c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137e970: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137e970: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137e974: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137e974: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137e978: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137e978: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x137e97c, size: 0x260
    // 0x137e97c: EnterFrame
    //     0x137e97c: stp             fp, lr, [SP, #-0x10]!
    //     0x137e980: mov             fp, SP
    // 0x137e984: AllocStack(0x30)
    //     0x137e984: sub             SP, SP, #0x30
    // 0x137e988: SetupParameters()
    //     0x137e988: ldr             x0, [fp, #0x10]
    //     0x137e98c: ldur            w2, [x0, #0x17]
    //     0x137e990: add             x2, x2, HEAP, lsl #32
    //     0x137e994: stur            x2, [fp, #-0x10]
    // 0x137e998: CheckStackOverflow
    //     0x137e998: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137e99c: cmp             SP, x16
    //     0x137e9a0: b.ls            #0x137ebcc
    // 0x137e9a4: LoadField: r0 = r2->field_b
    //     0x137e9a4: ldur            w0, [x2, #0xb]
    // 0x137e9a8: DecompressPointer r0
    //     0x137e9a8: add             x0, x0, HEAP, lsl #32
    // 0x137e9ac: stur            x0, [fp, #-8]
    // 0x137e9b0: LoadField: r1 = r0->field_f
    //     0x137e9b0: ldur            w1, [x0, #0xf]
    // 0x137e9b4: DecompressPointer r1
    //     0x137e9b4: add             x1, x1, HEAP, lsl #32
    // 0x137e9b8: r0 = controller()
    //     0x137e9b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137e9bc: LoadField: r1 = r0->field_4b
    //     0x137e9bc: ldur            w1, [x0, #0x4b]
    // 0x137e9c0: DecompressPointer r1
    //     0x137e9c0: add             x1, x1, HEAP, lsl #32
    // 0x137e9c4: r0 = value()
    //     0x137e9c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137e9c8: LoadField: r2 = r0->field_63
    //     0x137e9c8: ldur            w2, [x0, #0x63]
    // 0x137e9cc: DecompressPointer r2
    //     0x137e9cc: add             x2, x2, HEAP, lsl #32
    // 0x137e9d0: cmp             w2, NULL
    // 0x137e9d4: b.ne            #0x137e9e4
    // 0x137e9d8: ldur            x3, [fp, #-0x10]
    // 0x137e9dc: r0 = Null
    //     0x137e9dc: mov             x0, NULL
    // 0x137e9e0: b               #0x137ea3c
    // 0x137e9e4: ldur            x3, [fp, #-0x10]
    // 0x137e9e8: LoadField: r0 = r3->field_f
    //     0x137e9e8: ldur            w0, [x3, #0xf]
    // 0x137e9ec: DecompressPointer r0
    //     0x137e9ec: add             x0, x0, HEAP, lsl #32
    // 0x137e9f0: LoadField: r1 = r2->field_b
    //     0x137e9f0: ldur            w1, [x2, #0xb]
    // 0x137e9f4: r4 = LoadInt32Instr(r0)
    //     0x137e9f4: sbfx            x4, x0, #1, #0x1f
    //     0x137e9f8: tbz             w0, #0, #0x137ea00
    //     0x137e9fc: ldur            x4, [x0, #7]
    // 0x137ea00: r0 = LoadInt32Instr(r1)
    //     0x137ea00: sbfx            x0, x1, #1, #0x1f
    // 0x137ea04: mov             x1, x4
    // 0x137ea08: cmp             x1, x0
    // 0x137ea0c: b.hs            #0x137ebd4
    // 0x137ea10: LoadField: r0 = r2->field_f
    //     0x137ea10: ldur            w0, [x2, #0xf]
    // 0x137ea14: DecompressPointer r0
    //     0x137ea14: add             x0, x0, HEAP, lsl #32
    // 0x137ea18: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x137ea18: add             x16, x0, x4, lsl #2
    //     0x137ea1c: ldur            w1, [x16, #0xf]
    // 0x137ea20: DecompressPointer r1
    //     0x137ea20: add             x1, x1, HEAP, lsl #32
    // 0x137ea24: cmp             w1, NULL
    // 0x137ea28: b.ne            #0x137ea34
    // 0x137ea2c: r0 = Null
    //     0x137ea2c: mov             x0, NULL
    // 0x137ea30: b               #0x137ea3c
    // 0x137ea34: LoadField: r0 = r1->field_13
    //     0x137ea34: ldur            w0, [x1, #0x13]
    // 0x137ea38: DecompressPointer r0
    //     0x137ea38: add             x0, x0, HEAP, lsl #32
    // 0x137ea3c: r1 = LoadClassIdInstr(r0)
    //     0x137ea3c: ldur            x1, [x0, #-1]
    //     0x137ea40: ubfx            x1, x1, #0xc, #0x14
    // 0x137ea44: r16 = ""
    //     0x137ea44: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x137ea48: stp             x16, x0, [SP]
    // 0x137ea4c: mov             x0, x1
    // 0x137ea50: mov             lr, x0
    // 0x137ea54: ldr             lr, [x21, lr, lsl #3]
    // 0x137ea58: blr             lr
    // 0x137ea5c: tbz             w0, #4, #0x137ebbc
    // 0x137ea60: ldur            x0, [fp, #-8]
    // 0x137ea64: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x137ea64: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x137ea68: ldr             x0, [x0, #0x1c80]
    //     0x137ea6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x137ea70: cmp             w0, w16
    //     0x137ea74: b.ne            #0x137ea80
    //     0x137ea78: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x137ea7c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x137ea80: r1 = Null
    //     0x137ea80: mov             x1, NULL
    // 0x137ea84: r2 = 12
    //     0x137ea84: movz            x2, #0xc
    // 0x137ea88: r0 = AllocateArray()
    //     0x137ea88: bl              #0x16f7198  ; AllocateArrayStub
    // 0x137ea8c: stur            x0, [fp, #-0x18]
    // 0x137ea90: r16 = "link"
    //     0x137ea90: ldr             x16, [PP, #0x7c28]  ; [pp+0x7c28] "link"
    // 0x137ea94: StoreField: r0->field_f = r16
    //     0x137ea94: stur            w16, [x0, #0xf]
    // 0x137ea98: ldur            x1, [fp, #-8]
    // 0x137ea9c: LoadField: r2 = r1->field_f
    //     0x137ea9c: ldur            w2, [x1, #0xf]
    // 0x137eaa0: DecompressPointer r2
    //     0x137eaa0: add             x2, x2, HEAP, lsl #32
    // 0x137eaa4: mov             x1, x2
    // 0x137eaa8: r0 = controller()
    //     0x137eaa8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137eaac: LoadField: r1 = r0->field_4b
    //     0x137eaac: ldur            w1, [x0, #0x4b]
    // 0x137eab0: DecompressPointer r1
    //     0x137eab0: add             x1, x1, HEAP, lsl #32
    // 0x137eab4: r0 = value()
    //     0x137eab4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137eab8: LoadField: r2 = r0->field_63
    //     0x137eab8: ldur            w2, [x0, #0x63]
    // 0x137eabc: DecompressPointer r2
    //     0x137eabc: add             x2, x2, HEAP, lsl #32
    // 0x137eac0: cmp             w2, NULL
    // 0x137eac4: b.ne            #0x137ead0
    // 0x137eac8: r0 = Null
    //     0x137eac8: mov             x0, NULL
    // 0x137eacc: b               #0x137eb2c
    // 0x137ead0: ldur            x0, [fp, #-0x10]
    // 0x137ead4: LoadField: r1 = r0->field_f
    //     0x137ead4: ldur            w1, [x0, #0xf]
    // 0x137ead8: DecompressPointer r1
    //     0x137ead8: add             x1, x1, HEAP, lsl #32
    // 0x137eadc: LoadField: r0 = r2->field_b
    //     0x137eadc: ldur            w0, [x2, #0xb]
    // 0x137eae0: r3 = LoadInt32Instr(r1)
    //     0x137eae0: sbfx            x3, x1, #1, #0x1f
    //     0x137eae4: tbz             w1, #0, #0x137eaec
    //     0x137eae8: ldur            x3, [x1, #7]
    // 0x137eaec: r1 = LoadInt32Instr(r0)
    //     0x137eaec: sbfx            x1, x0, #1, #0x1f
    // 0x137eaf0: mov             x0, x1
    // 0x137eaf4: mov             x1, x3
    // 0x137eaf8: cmp             x1, x0
    // 0x137eafc: b.hs            #0x137ebd8
    // 0x137eb00: LoadField: r0 = r2->field_f
    //     0x137eb00: ldur            w0, [x2, #0xf]
    // 0x137eb04: DecompressPointer r0
    //     0x137eb04: add             x0, x0, HEAP, lsl #32
    // 0x137eb08: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x137eb08: add             x16, x0, x3, lsl #2
    //     0x137eb0c: ldur            w1, [x16, #0xf]
    // 0x137eb10: DecompressPointer r1
    //     0x137eb10: add             x1, x1, HEAP, lsl #32
    // 0x137eb14: cmp             w1, NULL
    // 0x137eb18: b.ne            #0x137eb24
    // 0x137eb1c: r0 = Null
    //     0x137eb1c: mov             x0, NULL
    // 0x137eb20: b               #0x137eb2c
    // 0x137eb24: LoadField: r0 = r1->field_13
    //     0x137eb24: ldur            w0, [x1, #0x13]
    // 0x137eb28: DecompressPointer r0
    //     0x137eb28: add             x0, x0, HEAP, lsl #32
    // 0x137eb2c: ldur            x1, [fp, #-0x18]
    // 0x137eb30: str             x0, [SP]
    // 0x137eb34: r0 = _interpolateSingle()
    //     0x137eb34: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x137eb38: ldur            x1, [fp, #-0x18]
    // 0x137eb3c: ArrayStore: r1[1] = r0  ; List_4
    //     0x137eb3c: add             x25, x1, #0x13
    //     0x137eb40: str             w0, [x25]
    //     0x137eb44: tbz             w0, #0, #0x137eb60
    //     0x137eb48: ldurb           w16, [x1, #-1]
    //     0x137eb4c: ldurb           w17, [x0, #-1]
    //     0x137eb50: and             x16, x17, x16, lsr #2
    //     0x137eb54: tst             x16, HEAP, lsr #32
    //     0x137eb58: b.eq            #0x137eb60
    //     0x137eb5c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x137eb60: ldur            x0, [fp, #-0x18]
    // 0x137eb64: r16 = "previousScreenSource"
    //     0x137eb64: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x137eb68: ldr             x16, [x16, #0x448]
    // 0x137eb6c: ArrayStore: r0[0] = r16  ; List_4
    //     0x137eb6c: stur            w16, [x0, #0x17]
    // 0x137eb70: r16 = ""
    //     0x137eb70: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x137eb74: StoreField: r0->field_1b = r16
    //     0x137eb74: stur            w16, [x0, #0x1b]
    // 0x137eb78: r16 = "screenSource"
    //     0x137eb78: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0x137eb7c: ldr             x16, [x16, #0x450]
    // 0x137eb80: StoreField: r0->field_1f = r16
    //     0x137eb80: stur            w16, [x0, #0x1f]
    // 0x137eb84: r16 = "browse"
    //     0x137eb84: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e5b0] "browse"
    //     0x137eb88: ldr             x16, [x16, #0x5b0]
    // 0x137eb8c: StoreField: r0->field_23 = r16
    //     0x137eb8c: stur            w16, [x0, #0x23]
    // 0x137eb90: r16 = <String, String>
    //     0x137eb90: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x137eb94: ldr             x16, [x16, #0x788]
    // 0x137eb98: stp             x0, x16, [SP]
    // 0x137eb9c: r0 = Map._fromLiteral()
    //     0x137eb9c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x137eba0: r16 = "/collection"
    //     0x137eba0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb458] "/collection"
    //     0x137eba4: ldr             x16, [x16, #0x458]
    // 0x137eba8: stp             x16, NULL, [SP, #8]
    // 0x137ebac: str             x0, [SP]
    // 0x137ebb0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x137ebb0: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x137ebb4: ldr             x4, [x4, #0x438]
    // 0x137ebb8: r0 = GetNavigation.toNamed()
    //     0x137ebb8: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x137ebbc: r0 = Null
    //     0x137ebbc: mov             x0, NULL
    // 0x137ebc0: LeaveFrame
    //     0x137ebc0: mov             SP, fp
    //     0x137ebc4: ldp             fp, lr, [SP], #0x10
    // 0x137ebc8: ret
    //     0x137ebc8: ret             
    // 0x137ebcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137ebcc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137ebd0: b               #0x137e9a4
    // 0x137ebd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137ebd4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x137ebd8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x137ebd8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ body(/* No info */) {
    // ** addr: 0x14fdb8c, size: 0x58
    // 0x14fdb8c: EnterFrame
    //     0x14fdb8c: stp             fp, lr, [SP, #-0x10]!
    //     0x14fdb90: mov             fp, SP
    // 0x14fdb94: AllocStack(0x10)
    //     0x14fdb94: sub             SP, SP, #0x10
    // 0x14fdb98: SetupParameters(BrowseView this /* r1 => r1, fp-0x8 */)
    //     0x14fdb98: stur            x1, [fp, #-8]
    // 0x14fdb9c: r1 = 1
    //     0x14fdb9c: movz            x1, #0x1
    // 0x14fdba0: r0 = AllocateContext()
    //     0x14fdba0: bl              #0x16f6108  ; AllocateContextStub
    // 0x14fdba4: mov             x1, x0
    // 0x14fdba8: ldur            x0, [fp, #-8]
    // 0x14fdbac: stur            x1, [fp, #-0x10]
    // 0x14fdbb0: StoreField: r1->field_f = r0
    //     0x14fdbb0: stur            w0, [x1, #0xf]
    // 0x14fdbb4: r0 = Obx()
    //     0x14fdbb4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14fdbb8: ldur            x2, [fp, #-0x10]
    // 0x14fdbbc: r1 = Function '<anonymous closure>':.
    //     0x14fdbbc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e510] AnonymousClosure: (0x137cf6c), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x14fdbc0: ldr             x1, [x1, #0x510]
    // 0x14fdbc4: stur            x0, [fp, #-8]
    // 0x14fdbc8: r0 = AllocateClosure()
    //     0x14fdbc8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fdbcc: mov             x1, x0
    // 0x14fdbd0: ldur            x0, [fp, #-8]
    // 0x14fdbd4: StoreField: r0->field_b = r1
    //     0x14fdbd4: stur            w1, [x0, #0xb]
    // 0x14fdbd8: LeaveFrame
    //     0x14fdbd8: mov             SP, fp
    //     0x14fdbdc: ldp             fp, lr, [SP], #0x10
    // 0x14fdbe0: ret
    //     0x14fdbe0: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15cbc74, size: 0xb8
    // 0x15cbc74: EnterFrame
    //     0x15cbc74: stp             fp, lr, [SP, #-0x10]!
    //     0x15cbc78: mov             fp, SP
    // 0x15cbc7c: AllocStack(0x10)
    //     0x15cbc7c: sub             SP, SP, #0x10
    // 0x15cbc80: SetupParameters()
    //     0x15cbc80: ldr             x0, [fp, #0x10]
    //     0x15cbc84: ldur            w1, [x0, #0x17]
    //     0x15cbc88: add             x1, x1, HEAP, lsl #32
    // 0x15cbc8c: CheckStackOverflow
    //     0x15cbc8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15cbc90: cmp             SP, x16
    //     0x15cbc94: b.ls            #0x15cbd24
    // 0x15cbc98: LoadField: r0 = r1->field_f
    //     0x15cbc98: ldur            w0, [x1, #0xf]
    // 0x15cbc9c: DecompressPointer r0
    //     0x15cbc9c: add             x0, x0, HEAP, lsl #32
    // 0x15cbca0: mov             x1, x0
    // 0x15cbca4: r0 = controller()
    //     0x15cbca4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cbca8: LoadField: r1 = r0->field_57
    //     0x15cbca8: ldur            w1, [x0, #0x57]
    // 0x15cbcac: DecompressPointer r1
    //     0x15cbcac: add             x1, x1, HEAP, lsl #32
    // 0x15cbcb0: r0 = value()
    //     0x15cbcb0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cbcb4: tbnz            w0, #4, #0x15cbcec
    // 0x15cbcb8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15cbcb8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15cbcbc: ldr             x0, [x0, #0x1c80]
    //     0x15cbcc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15cbcc4: cmp             w0, w16
    //     0x15cbcc8: b.ne            #0x15cbcd4
    //     0x15cbccc: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15cbcd0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15cbcd4: r16 = "/search"
    //     0x15cbcd4: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15cbcd8: ldr             x16, [x16, #0x838]
    // 0x15cbcdc: stp             x16, NULL, [SP]
    // 0x15cbce0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15cbce0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15cbce4: r0 = GetNavigation.toNamed()
    //     0x15cbce4: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15cbce8: b               #0x15cbd14
    // 0x15cbcec: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15cbcec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15cbcf0: ldr             x0, [x0, #0x1c80]
    //     0x15cbcf4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15cbcf8: cmp             w0, w16
    //     0x15cbcfc: b.ne            #0x15cbd08
    //     0x15cbd00: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15cbd04: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15cbd08: str             NULL, [SP]
    // 0x15cbd0c: r4 = const [0x1, 0, 0, 0, null]
    //     0x15cbd0c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x15cbd10: r0 = GetNavigation.back()
    //     0x15cbd10: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x15cbd14: r0 = Null
    //     0x15cbd14: mov             x0, NULL
    // 0x15cbd18: LeaveFrame
    //     0x15cbd18: mov             SP, fp
    //     0x15cbd1c: ldp             fp, lr, [SP], #0x10
    // 0x15cbd20: ret
    //     0x15cbd20: ret             
    // 0x15cbd24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15cbd24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15cbd28: b               #0x15cbc98
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e6ee8, size: 0x2b0
    // 0x15e6ee8: EnterFrame
    //     0x15e6ee8: stp             fp, lr, [SP, #-0x10]!
    //     0x15e6eec: mov             fp, SP
    // 0x15e6ef0: AllocStack(0x30)
    //     0x15e6ef0: sub             SP, SP, #0x30
    // 0x15e6ef4: SetupParameters(BrowseView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e6ef4: stur            x1, [fp, #-8]
    //     0x15e6ef8: stur            x2, [fp, #-0x10]
    // 0x15e6efc: CheckStackOverflow
    //     0x15e6efc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e6f00: cmp             SP, x16
    //     0x15e6f04: b.ls            #0x15e7190
    // 0x15e6f08: r1 = 2
    //     0x15e6f08: movz            x1, #0x2
    // 0x15e6f0c: r0 = AllocateContext()
    //     0x15e6f0c: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e6f10: ldur            x1, [fp, #-8]
    // 0x15e6f14: stur            x0, [fp, #-0x18]
    // 0x15e6f18: StoreField: r0->field_f = r1
    //     0x15e6f18: stur            w1, [x0, #0xf]
    // 0x15e6f1c: ldur            x2, [fp, #-0x10]
    // 0x15e6f20: StoreField: r0->field_13 = r2
    //     0x15e6f20: stur            w2, [x0, #0x13]
    // 0x15e6f24: r0 = Obx()
    //     0x15e6f24: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e6f28: ldur            x2, [fp, #-0x18]
    // 0x15e6f2c: r1 = Function '<anonymous closure>':.
    //     0x15e6f2c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e5b8] AnonymousClosure: (0x15e7498), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::appBar (0x15e6ee8)
    //     0x15e6f30: ldr             x1, [x1, #0x5b8]
    // 0x15e6f34: stur            x0, [fp, #-0x10]
    // 0x15e6f38: r0 = AllocateClosure()
    //     0x15e6f38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e6f3c: mov             x1, x0
    // 0x15e6f40: ldur            x0, [fp, #-0x10]
    // 0x15e6f44: StoreField: r0->field_b = r1
    //     0x15e6f44: stur            w1, [x0, #0xb]
    // 0x15e6f48: ldur            x1, [fp, #-8]
    // 0x15e6f4c: r0 = controller()
    //     0x15e6f4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e6f50: mov             x1, x0
    // 0x15e6f54: r0 = configResponse()
    //     0x15e6f54: bl              #0x8b5174  ; [package:customer_app/app/config_controller.dart] ConfigController::configResponse
    // 0x15e6f58: tbnz            w0, #4, #0x15e6ff0
    // 0x15e6f5c: ldur            x2, [fp, #-0x18]
    // 0x15e6f60: LoadField: r1 = r2->field_13
    //     0x15e6f60: ldur            w1, [x2, #0x13]
    // 0x15e6f64: DecompressPointer r1
    //     0x15e6f64: add             x1, x1, HEAP, lsl #32
    // 0x15e6f68: r0 = of()
    //     0x15e6f68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e6f6c: LoadField: r1 = r0->field_5b
    //     0x15e6f6c: ldur            w1, [x0, #0x5b]
    // 0x15e6f70: DecompressPointer r1
    //     0x15e6f70: add             x1, x1, HEAP, lsl #32
    // 0x15e6f74: stur            x1, [fp, #-8]
    // 0x15e6f78: r0 = ColorFilter()
    //     0x15e6f78: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e6f7c: mov             x1, x0
    // 0x15e6f80: ldur            x0, [fp, #-8]
    // 0x15e6f84: stur            x1, [fp, #-0x20]
    // 0x15e6f88: StoreField: r1->field_7 = r0
    //     0x15e6f88: stur            w0, [x1, #7]
    // 0x15e6f8c: r0 = Instance_BlendMode
    //     0x15e6f8c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e6f90: ldr             x0, [x0, #0xb30]
    // 0x15e6f94: StoreField: r1->field_b = r0
    //     0x15e6f94: stur            w0, [x1, #0xb]
    // 0x15e6f98: r2 = 1
    //     0x15e6f98: movz            x2, #0x1
    // 0x15e6f9c: StoreField: r1->field_13 = r2
    //     0x15e6f9c: stur            x2, [x1, #0x13]
    // 0x15e6fa0: r0 = SvgPicture()
    //     0x15e6fa0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e6fa4: stur            x0, [fp, #-8]
    // 0x15e6fa8: ldur            x16, [fp, #-0x20]
    // 0x15e6fac: str             x16, [SP]
    // 0x15e6fb0: mov             x1, x0
    // 0x15e6fb4: r2 = "assets/images/search.svg"
    //     0x15e6fb4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e6fb8: ldr             x2, [x2, #0xa30]
    // 0x15e6fbc: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e6fbc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e6fc0: ldr             x4, [x4, #0xa38]
    // 0x15e6fc4: r0 = SvgPicture.asset()
    //     0x15e6fc4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e6fc8: r0 = Align()
    //     0x15e6fc8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e6fcc: r3 = Instance_Alignment
    //     0x15e6fcc: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e6fd0: ldr             x3, [x3, #0xb10]
    // 0x15e6fd4: StoreField: r0->field_f = r3
    //     0x15e6fd4: stur            w3, [x0, #0xf]
    // 0x15e6fd8: r4 = 1.000000
    //     0x15e6fd8: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e6fdc: StoreField: r0->field_13 = r4
    //     0x15e6fdc: stur            w4, [x0, #0x13]
    // 0x15e6fe0: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e6fe0: stur            w4, [x0, #0x17]
    // 0x15e6fe4: ldur            x1, [fp, #-8]
    // 0x15e6fe8: StoreField: r0->field_b = r1
    //     0x15e6fe8: stur            w1, [x0, #0xb]
    // 0x15e6fec: b               #0x15e70a0
    // 0x15e6ff0: ldur            x5, [fp, #-0x18]
    // 0x15e6ff4: r4 = 1.000000
    //     0x15e6ff4: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e6ff8: r0 = Instance_BlendMode
    //     0x15e6ff8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e6ffc: ldr             x0, [x0, #0xb30]
    // 0x15e7000: r3 = Instance_Alignment
    //     0x15e7000: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e7004: ldr             x3, [x3, #0xb10]
    // 0x15e7008: r2 = 1
    //     0x15e7008: movz            x2, #0x1
    // 0x15e700c: LoadField: r1 = r5->field_13
    //     0x15e700c: ldur            w1, [x5, #0x13]
    // 0x15e7010: DecompressPointer r1
    //     0x15e7010: add             x1, x1, HEAP, lsl #32
    // 0x15e7014: r0 = of()
    //     0x15e7014: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e7018: LoadField: r1 = r0->field_5b
    //     0x15e7018: ldur            w1, [x0, #0x5b]
    // 0x15e701c: DecompressPointer r1
    //     0x15e701c: add             x1, x1, HEAP, lsl #32
    // 0x15e7020: stur            x1, [fp, #-8]
    // 0x15e7024: r0 = ColorFilter()
    //     0x15e7024: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e7028: mov             x1, x0
    // 0x15e702c: ldur            x0, [fp, #-8]
    // 0x15e7030: stur            x1, [fp, #-0x20]
    // 0x15e7034: StoreField: r1->field_7 = r0
    //     0x15e7034: stur            w0, [x1, #7]
    // 0x15e7038: r0 = Instance_BlendMode
    //     0x15e7038: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e703c: ldr             x0, [x0, #0xb30]
    // 0x15e7040: StoreField: r1->field_b = r0
    //     0x15e7040: stur            w0, [x1, #0xb]
    // 0x15e7044: r0 = 1
    //     0x15e7044: movz            x0, #0x1
    // 0x15e7048: StoreField: r1->field_13 = r0
    //     0x15e7048: stur            x0, [x1, #0x13]
    // 0x15e704c: r0 = SvgPicture()
    //     0x15e704c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e7050: stur            x0, [fp, #-8]
    // 0x15e7054: ldur            x16, [fp, #-0x20]
    // 0x15e7058: str             x16, [SP]
    // 0x15e705c: mov             x1, x0
    // 0x15e7060: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e7060: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e7064: ldr             x2, [x2, #0xa40]
    // 0x15e7068: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e7068: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e706c: ldr             x4, [x4, #0xa38]
    // 0x15e7070: r0 = SvgPicture.asset()
    //     0x15e7070: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e7074: r0 = Align()
    //     0x15e7074: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e7078: mov             x1, x0
    // 0x15e707c: r0 = Instance_Alignment
    //     0x15e707c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e7080: ldr             x0, [x0, #0xb10]
    // 0x15e7084: StoreField: r1->field_f = r0
    //     0x15e7084: stur            w0, [x1, #0xf]
    // 0x15e7088: r0 = 1.000000
    //     0x15e7088: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e708c: StoreField: r1->field_13 = r0
    //     0x15e708c: stur            w0, [x1, #0x13]
    // 0x15e7090: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e7090: stur            w0, [x1, #0x17]
    // 0x15e7094: ldur            x0, [fp, #-8]
    // 0x15e7098: StoreField: r1->field_b = r0
    //     0x15e7098: stur            w0, [x1, #0xb]
    // 0x15e709c: mov             x0, x1
    // 0x15e70a0: stur            x0, [fp, #-8]
    // 0x15e70a4: r0 = InkWell()
    //     0x15e70a4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e70a8: mov             x3, x0
    // 0x15e70ac: ldur            x0, [fp, #-8]
    // 0x15e70b0: stur            x3, [fp, #-0x20]
    // 0x15e70b4: StoreField: r3->field_b = r0
    //     0x15e70b4: stur            w0, [x3, #0xb]
    // 0x15e70b8: ldur            x2, [fp, #-0x18]
    // 0x15e70bc: r1 = Function '<anonymous closure>':.
    //     0x15e70bc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e5c0] AnonymousClosure: (0x15cbc74), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::appBar (0x15e6ee8)
    //     0x15e70c0: ldr             x1, [x1, #0x5c0]
    // 0x15e70c4: r0 = AllocateClosure()
    //     0x15e70c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e70c8: ldur            x2, [fp, #-0x20]
    // 0x15e70cc: StoreField: r2->field_f = r0
    //     0x15e70cc: stur            w0, [x2, #0xf]
    // 0x15e70d0: r0 = true
    //     0x15e70d0: add             x0, NULL, #0x20  ; true
    // 0x15e70d4: StoreField: r2->field_43 = r0
    //     0x15e70d4: stur            w0, [x2, #0x43]
    // 0x15e70d8: r1 = Instance_BoxShape
    //     0x15e70d8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e70dc: ldr             x1, [x1, #0x80]
    // 0x15e70e0: StoreField: r2->field_47 = r1
    //     0x15e70e0: stur            w1, [x2, #0x47]
    // 0x15e70e4: StoreField: r2->field_6f = r0
    //     0x15e70e4: stur            w0, [x2, #0x6f]
    // 0x15e70e8: r1 = false
    //     0x15e70e8: add             x1, NULL, #0x30  ; false
    // 0x15e70ec: StoreField: r2->field_73 = r1
    //     0x15e70ec: stur            w1, [x2, #0x73]
    // 0x15e70f0: StoreField: r2->field_83 = r0
    //     0x15e70f0: stur            w0, [x2, #0x83]
    // 0x15e70f4: StoreField: r2->field_7b = r1
    //     0x15e70f4: stur            w1, [x2, #0x7b]
    // 0x15e70f8: r0 = Obx()
    //     0x15e70f8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e70fc: ldur            x2, [fp, #-0x18]
    // 0x15e7100: r1 = Function '<anonymous closure>':.
    //     0x15e7100: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e5c8] AnonymousClosure: (0x15e7198), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::appBar (0x15e6ee8)
    //     0x15e7104: ldr             x1, [x1, #0x5c8]
    // 0x15e7108: stur            x0, [fp, #-8]
    // 0x15e710c: r0 = AllocateClosure()
    //     0x15e710c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e7110: mov             x1, x0
    // 0x15e7114: ldur            x0, [fp, #-8]
    // 0x15e7118: StoreField: r0->field_b = r1
    //     0x15e7118: stur            w1, [x0, #0xb]
    // 0x15e711c: r1 = Null
    //     0x15e711c: mov             x1, NULL
    // 0x15e7120: r2 = 2
    //     0x15e7120: movz            x2, #0x2
    // 0x15e7124: r0 = AllocateArray()
    //     0x15e7124: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15e7128: mov             x2, x0
    // 0x15e712c: ldur            x0, [fp, #-8]
    // 0x15e7130: stur            x2, [fp, #-0x18]
    // 0x15e7134: StoreField: r2->field_f = r0
    //     0x15e7134: stur            w0, [x2, #0xf]
    // 0x15e7138: r1 = <Widget>
    //     0x15e7138: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15e713c: r0 = AllocateGrowableArray()
    //     0x15e713c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15e7140: mov             x1, x0
    // 0x15e7144: ldur            x0, [fp, #-0x18]
    // 0x15e7148: stur            x1, [fp, #-8]
    // 0x15e714c: StoreField: r1->field_f = r0
    //     0x15e714c: stur            w0, [x1, #0xf]
    // 0x15e7150: r0 = 2
    //     0x15e7150: movz            x0, #0x2
    // 0x15e7154: StoreField: r1->field_b = r0
    //     0x15e7154: stur            w0, [x1, #0xb]
    // 0x15e7158: r0 = AppBar()
    //     0x15e7158: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e715c: stur            x0, [fp, #-0x18]
    // 0x15e7160: ldur            x16, [fp, #-0x10]
    // 0x15e7164: ldur            lr, [fp, #-8]
    // 0x15e7168: stp             lr, x16, [SP]
    // 0x15e716c: mov             x1, x0
    // 0x15e7170: ldur            x2, [fp, #-0x20]
    // 0x15e7174: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15e7174: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15e7178: ldr             x4, [x4, #0xa58]
    // 0x15e717c: r0 = AppBar()
    //     0x15e717c: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e7180: ldur            x0, [fp, #-0x18]
    // 0x15e7184: LeaveFrame
    //     0x15e7184: mov             SP, fp
    //     0x15e7188: ldp             fp, lr, [SP], #0x10
    // 0x15e718c: ret
    //     0x15e718c: ret             
    // 0x15e7190: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e7190: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e7194: b               #0x15e6f08
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15e7198, size: 0x300
    // 0x15e7198: EnterFrame
    //     0x15e7198: stp             fp, lr, [SP, #-0x10]!
    //     0x15e719c: mov             fp, SP
    // 0x15e71a0: AllocStack(0x58)
    //     0x15e71a0: sub             SP, SP, #0x58
    // 0x15e71a4: SetupParameters()
    //     0x15e71a4: ldr             x0, [fp, #0x10]
    //     0x15e71a8: ldur            w2, [x0, #0x17]
    //     0x15e71ac: add             x2, x2, HEAP, lsl #32
    //     0x15e71b0: stur            x2, [fp, #-8]
    // 0x15e71b4: CheckStackOverflow
    //     0x15e71b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e71b8: cmp             SP, x16
    //     0x15e71bc: b.ls            #0x15e7490
    // 0x15e71c0: LoadField: r1 = r2->field_f
    //     0x15e71c0: ldur            w1, [x2, #0xf]
    // 0x15e71c4: DecompressPointer r1
    //     0x15e71c4: add             x1, x1, HEAP, lsl #32
    // 0x15e71c8: r0 = controller()
    //     0x15e71c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e71cc: LoadField: r1 = r0->field_4b
    //     0x15e71cc: ldur            w1, [x0, #0x4b]
    // 0x15e71d0: DecompressPointer r1
    //     0x15e71d0: add             x1, x1, HEAP, lsl #32
    // 0x15e71d4: r0 = value()
    //     0x15e71d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e71d8: LoadField: r1 = r0->field_1f
    //     0x15e71d8: ldur            w1, [x0, #0x1f]
    // 0x15e71dc: DecompressPointer r1
    //     0x15e71dc: add             x1, x1, HEAP, lsl #32
    // 0x15e71e0: cmp             w1, NULL
    // 0x15e71e4: b.ne            #0x15e71f0
    // 0x15e71e8: r0 = Null
    //     0x15e71e8: mov             x0, NULL
    // 0x15e71ec: b               #0x15e71f8
    // 0x15e71f0: LoadField: r0 = r1->field_7
    //     0x15e71f0: ldur            w0, [x1, #7]
    // 0x15e71f4: DecompressPointer r0
    //     0x15e71f4: add             x0, x0, HEAP, lsl #32
    // 0x15e71f8: cmp             w0, NULL
    // 0x15e71fc: b.ne            #0x15e7208
    // 0x15e7200: r0 = false
    //     0x15e7200: add             x0, NULL, #0x30  ; false
    // 0x15e7204: b               #0x15e73f8
    // 0x15e7208: tbnz            w0, #4, #0x15e73f4
    // 0x15e720c: ldur            x0, [fp, #-8]
    // 0x15e7210: LoadField: r1 = r0->field_f
    //     0x15e7210: ldur            w1, [x0, #0xf]
    // 0x15e7214: DecompressPointer r1
    //     0x15e7214: add             x1, x1, HEAP, lsl #32
    // 0x15e7218: r0 = controller()
    //     0x15e7218: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e721c: mov             x1, x0
    // 0x15e7220: r0 = appConfigResponse()
    //     0x15e7220: bl              #0x933060  ; [package:customer_app/app/config_controller.dart] ConfigController::appConfigResponse
    // 0x15e7224: mov             x2, x0
    // 0x15e7228: ldur            x0, [fp, #-8]
    // 0x15e722c: stur            x2, [fp, #-0x10]
    // 0x15e7230: LoadField: r1 = r0->field_13
    //     0x15e7230: ldur            w1, [x0, #0x13]
    // 0x15e7234: DecompressPointer r1
    //     0x15e7234: add             x1, x1, HEAP, lsl #32
    // 0x15e7238: r0 = of()
    //     0x15e7238: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e723c: LoadField: r2 = r0->field_5b
    //     0x15e723c: ldur            w2, [x0, #0x5b]
    // 0x15e7240: DecompressPointer r2
    //     0x15e7240: add             x2, x2, HEAP, lsl #32
    // 0x15e7244: ldur            x0, [fp, #-8]
    // 0x15e7248: stur            x2, [fp, #-0x18]
    // 0x15e724c: LoadField: r1 = r0->field_f
    //     0x15e724c: ldur            w1, [x0, #0xf]
    // 0x15e7250: DecompressPointer r1
    //     0x15e7250: add             x1, x1, HEAP, lsl #32
    // 0x15e7254: r0 = controller()
    //     0x15e7254: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e7258: LoadField: r1 = r0->field_5f
    //     0x15e7258: ldur            w1, [x0, #0x5f]
    // 0x15e725c: DecompressPointer r1
    //     0x15e725c: add             x1, x1, HEAP, lsl #32
    // 0x15e7260: r0 = value()
    //     0x15e7260: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e7264: cmp             w0, NULL
    // 0x15e7268: r16 = true
    //     0x15e7268: add             x16, NULL, #0x20  ; true
    // 0x15e726c: r17 = false
    //     0x15e726c: add             x17, NULL, #0x30  ; false
    // 0x15e7270: csel            x2, x16, x17, ne
    // 0x15e7274: ldur            x0, [fp, #-8]
    // 0x15e7278: stur            x2, [fp, #-0x20]
    // 0x15e727c: LoadField: r1 = r0->field_f
    //     0x15e727c: ldur            w1, [x0, #0xf]
    // 0x15e7280: DecompressPointer r1
    //     0x15e7280: add             x1, x1, HEAP, lsl #32
    // 0x15e7284: r0 = controller()
    //     0x15e7284: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e7288: LoadField: r1 = r0->field_5f
    //     0x15e7288: ldur            w1, [x0, #0x5f]
    // 0x15e728c: DecompressPointer r1
    //     0x15e728c: add             x1, x1, HEAP, lsl #32
    // 0x15e7290: r0 = value()
    //     0x15e7290: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e7294: str             x0, [SP]
    // 0x15e7298: r0 = _interpolateSingle()
    //     0x15e7298: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15e729c: mov             x2, x0
    // 0x15e72a0: ldur            x0, [fp, #-8]
    // 0x15e72a4: stur            x2, [fp, #-0x28]
    // 0x15e72a8: LoadField: r1 = r0->field_13
    //     0x15e72a8: ldur            w1, [x0, #0x13]
    // 0x15e72ac: DecompressPointer r1
    //     0x15e72ac: add             x1, x1, HEAP, lsl #32
    // 0x15e72b0: r0 = of()
    //     0x15e72b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e72b4: LoadField: r1 = r0->field_87
    //     0x15e72b4: ldur            w1, [x0, #0x87]
    // 0x15e72b8: DecompressPointer r1
    //     0x15e72b8: add             x1, x1, HEAP, lsl #32
    // 0x15e72bc: LoadField: r0 = r1->field_27
    //     0x15e72bc: ldur            w0, [x1, #0x27]
    // 0x15e72c0: DecompressPointer r0
    //     0x15e72c0: add             x0, x0, HEAP, lsl #32
    // 0x15e72c4: r16 = Instance_Color
    //     0x15e72c4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15e72c8: str             x16, [SP]
    // 0x15e72cc: mov             x1, x0
    // 0x15e72d0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15e72d0: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15e72d4: ldr             x4, [x4, #0xf40]
    // 0x15e72d8: r0 = copyWith()
    //     0x15e72d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15e72dc: stur            x0, [fp, #-0x30]
    // 0x15e72e0: r0 = Text()
    //     0x15e72e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15e72e4: mov             x2, x0
    // 0x15e72e8: ldur            x0, [fp, #-0x28]
    // 0x15e72ec: stur            x2, [fp, #-0x38]
    // 0x15e72f0: StoreField: r2->field_b = r0
    //     0x15e72f0: stur            w0, [x2, #0xb]
    // 0x15e72f4: ldur            x0, [fp, #-0x30]
    // 0x15e72f8: StoreField: r2->field_13 = r0
    //     0x15e72f8: stur            w0, [x2, #0x13]
    // 0x15e72fc: ldur            x0, [fp, #-8]
    // 0x15e7300: LoadField: r1 = r0->field_13
    //     0x15e7300: ldur            w1, [x0, #0x13]
    // 0x15e7304: DecompressPointer r1
    //     0x15e7304: add             x1, x1, HEAP, lsl #32
    // 0x15e7308: r0 = of()
    //     0x15e7308: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e730c: LoadField: r1 = r0->field_5b
    //     0x15e730c: ldur            w1, [x0, #0x5b]
    // 0x15e7310: DecompressPointer r1
    //     0x15e7310: add             x1, x1, HEAP, lsl #32
    // 0x15e7314: stur            x1, [fp, #-8]
    // 0x15e7318: r0 = ColorFilter()
    //     0x15e7318: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e731c: mov             x1, x0
    // 0x15e7320: ldur            x0, [fp, #-8]
    // 0x15e7324: stur            x1, [fp, #-0x28]
    // 0x15e7328: StoreField: r1->field_7 = r0
    //     0x15e7328: stur            w0, [x1, #7]
    // 0x15e732c: r0 = Instance_BlendMode
    //     0x15e732c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e7330: ldr             x0, [x0, #0xb30]
    // 0x15e7334: StoreField: r1->field_b = r0
    //     0x15e7334: stur            w0, [x1, #0xb]
    // 0x15e7338: r0 = 1
    //     0x15e7338: movz            x0, #0x1
    // 0x15e733c: StoreField: r1->field_13 = r0
    //     0x15e733c: stur            x0, [x1, #0x13]
    // 0x15e7340: r0 = SvgPicture()
    //     0x15e7340: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e7344: stur            x0, [fp, #-8]
    // 0x15e7348: r16 = Instance_BoxFit
    //     0x15e7348: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15e734c: ldr             x16, [x16, #0xb18]
    // 0x15e7350: r30 = 24.000000
    //     0x15e7350: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15e7354: ldr             lr, [lr, #0xba8]
    // 0x15e7358: stp             lr, x16, [SP, #0x10]
    // 0x15e735c: r16 = 24.000000
    //     0x15e735c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15e7360: ldr             x16, [x16, #0xba8]
    // 0x15e7364: ldur            lr, [fp, #-0x28]
    // 0x15e7368: stp             lr, x16, [SP]
    // 0x15e736c: mov             x1, x0
    // 0x15e7370: r2 = "assets/images/shopping_bag.svg"
    //     0x15e7370: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15e7374: ldr             x2, [x2, #0xa60]
    // 0x15e7378: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15e7378: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15e737c: ldr             x4, [x4, #0xa68]
    // 0x15e7380: r0 = SvgPicture.asset()
    //     0x15e7380: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e7384: r0 = Badge()
    //     0x15e7384: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15e7388: mov             x1, x0
    // 0x15e738c: ldur            x0, [fp, #-0x18]
    // 0x15e7390: stur            x1, [fp, #-0x28]
    // 0x15e7394: StoreField: r1->field_b = r0
    //     0x15e7394: stur            w0, [x1, #0xb]
    // 0x15e7398: ldur            x0, [fp, #-0x38]
    // 0x15e739c: StoreField: r1->field_27 = r0
    //     0x15e739c: stur            w0, [x1, #0x27]
    // 0x15e73a0: ldur            x0, [fp, #-0x20]
    // 0x15e73a4: StoreField: r1->field_2b = r0
    //     0x15e73a4: stur            w0, [x1, #0x2b]
    // 0x15e73a8: ldur            x0, [fp, #-8]
    // 0x15e73ac: StoreField: r1->field_2f = r0
    //     0x15e73ac: stur            w0, [x1, #0x2f]
    // 0x15e73b0: r0 = Visibility()
    //     0x15e73b0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15e73b4: mov             x1, x0
    // 0x15e73b8: ldur            x0, [fp, #-0x28]
    // 0x15e73bc: StoreField: r1->field_b = r0
    //     0x15e73bc: stur            w0, [x1, #0xb]
    // 0x15e73c0: r0 = Instance_SizedBox
    //     0x15e73c0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15e73c4: StoreField: r1->field_f = r0
    //     0x15e73c4: stur            w0, [x1, #0xf]
    // 0x15e73c8: ldur            x0, [fp, #-0x10]
    // 0x15e73cc: StoreField: r1->field_13 = r0
    //     0x15e73cc: stur            w0, [x1, #0x13]
    // 0x15e73d0: r0 = false
    //     0x15e73d0: add             x0, NULL, #0x30  ; false
    // 0x15e73d4: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e73d4: stur            w0, [x1, #0x17]
    // 0x15e73d8: StoreField: r1->field_1b = r0
    //     0x15e73d8: stur            w0, [x1, #0x1b]
    // 0x15e73dc: StoreField: r1->field_1f = r0
    //     0x15e73dc: stur            w0, [x1, #0x1f]
    // 0x15e73e0: StoreField: r1->field_23 = r0
    //     0x15e73e0: stur            w0, [x1, #0x23]
    // 0x15e73e4: StoreField: r1->field_27 = r0
    //     0x15e73e4: stur            w0, [x1, #0x27]
    // 0x15e73e8: StoreField: r1->field_2b = r0
    //     0x15e73e8: stur            w0, [x1, #0x2b]
    // 0x15e73ec: mov             x0, x1
    // 0x15e73f0: b               #0x15e7410
    // 0x15e73f4: r0 = false
    //     0x15e73f4: add             x0, NULL, #0x30  ; false
    // 0x15e73f8: r0 = Container()
    //     0x15e73f8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15e73fc: mov             x1, x0
    // 0x15e7400: stur            x0, [fp, #-8]
    // 0x15e7404: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15e7404: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15e7408: r0 = Container()
    //     0x15e7408: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15e740c: ldur            x0, [fp, #-8]
    // 0x15e7410: stur            x0, [fp, #-8]
    // 0x15e7414: r0 = InkWell()
    //     0x15e7414: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e7418: mov             x3, x0
    // 0x15e741c: ldur            x0, [fp, #-8]
    // 0x15e7420: stur            x3, [fp, #-0x10]
    // 0x15e7424: StoreField: r3->field_b = r0
    //     0x15e7424: stur            w0, [x3, #0xb]
    // 0x15e7428: r1 = Function '<anonymous closure>':.
    //     0x15e7428: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e5d0] AnonymousClosure: (0x15cae64), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::appBar (0x15e9764)
    //     0x15e742c: ldr             x1, [x1, #0x5d0]
    // 0x15e7430: r2 = Null
    //     0x15e7430: mov             x2, NULL
    // 0x15e7434: r0 = AllocateClosure()
    //     0x15e7434: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e7438: mov             x1, x0
    // 0x15e743c: ldur            x0, [fp, #-0x10]
    // 0x15e7440: StoreField: r0->field_f = r1
    //     0x15e7440: stur            w1, [x0, #0xf]
    // 0x15e7444: r1 = true
    //     0x15e7444: add             x1, NULL, #0x20  ; true
    // 0x15e7448: StoreField: r0->field_43 = r1
    //     0x15e7448: stur            w1, [x0, #0x43]
    // 0x15e744c: r2 = Instance_BoxShape
    //     0x15e744c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e7450: ldr             x2, [x2, #0x80]
    // 0x15e7454: StoreField: r0->field_47 = r2
    //     0x15e7454: stur            w2, [x0, #0x47]
    // 0x15e7458: StoreField: r0->field_6f = r1
    //     0x15e7458: stur            w1, [x0, #0x6f]
    // 0x15e745c: r2 = false
    //     0x15e745c: add             x2, NULL, #0x30  ; false
    // 0x15e7460: StoreField: r0->field_73 = r2
    //     0x15e7460: stur            w2, [x0, #0x73]
    // 0x15e7464: StoreField: r0->field_83 = r1
    //     0x15e7464: stur            w1, [x0, #0x83]
    // 0x15e7468: StoreField: r0->field_7b = r2
    //     0x15e7468: stur            w2, [x0, #0x7b]
    // 0x15e746c: r0 = Padding()
    //     0x15e746c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15e7470: r1 = Instance_EdgeInsets
    //     0x15e7470: add             x1, PP, #0x37, lsl #12  ; [pp+0x37290] Obj!EdgeInsets@d5a111
    //     0x15e7474: ldr             x1, [x1, #0x290]
    // 0x15e7478: StoreField: r0->field_f = r1
    //     0x15e7478: stur            w1, [x0, #0xf]
    // 0x15e747c: ldur            x1, [fp, #-0x10]
    // 0x15e7480: StoreField: r0->field_b = r1
    //     0x15e7480: stur            w1, [x0, #0xb]
    // 0x15e7484: LeaveFrame
    //     0x15e7484: mov             SP, fp
    //     0x15e7488: ldp             fp, lr, [SP], #0x10
    // 0x15e748c: ret
    //     0x15e748c: ret             
    // 0x15e7490: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e7490: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e7494: b               #0x15e71c0
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0x15e7498, size: 0x460
    // 0x15e7498: EnterFrame
    //     0x15e7498: stp             fp, lr, [SP, #-0x10]!
    //     0x15e749c: mov             fp, SP
    // 0x15e74a0: AllocStack(0x68)
    //     0x15e74a0: sub             SP, SP, #0x68
    // 0x15e74a4: SetupParameters()
    //     0x15e74a4: ldr             x0, [fp, #0x10]
    //     0x15e74a8: ldur            w2, [x0, #0x17]
    //     0x15e74ac: add             x2, x2, HEAP, lsl #32
    //     0x15e74b0: stur            x2, [fp, #-8]
    // 0x15e74b4: CheckStackOverflow
    //     0x15e74b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e74b8: cmp             SP, x16
    //     0x15e74bc: b.ls            #0x15e78f0
    // 0x15e74c0: LoadField: r1 = r2->field_f
    //     0x15e74c0: ldur            w1, [x2, #0xf]
    // 0x15e74c4: DecompressPointer r1
    //     0x15e74c4: add             x1, x1, HEAP, lsl #32
    // 0x15e74c8: r0 = controller()
    //     0x15e74c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e74cc: LoadField: r1 = r0->field_4b
    //     0x15e74cc: ldur            w1, [x0, #0x4b]
    // 0x15e74d0: DecompressPointer r1
    //     0x15e74d0: add             x1, x1, HEAP, lsl #32
    // 0x15e74d4: r0 = value()
    //     0x15e74d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e74d8: LoadField: r1 = r0->field_3f
    //     0x15e74d8: ldur            w1, [x0, #0x3f]
    // 0x15e74dc: DecompressPointer r1
    //     0x15e74dc: add             x1, x1, HEAP, lsl #32
    // 0x15e74e0: cmp             w1, NULL
    // 0x15e74e4: b.ne            #0x15e74f0
    // 0x15e74e8: r0 = Null
    //     0x15e74e8: mov             x0, NULL
    // 0x15e74ec: b               #0x15e74f8
    // 0x15e74f0: LoadField: r0 = r1->field_f
    //     0x15e74f0: ldur            w0, [x1, #0xf]
    // 0x15e74f4: DecompressPointer r0
    //     0x15e74f4: add             x0, x0, HEAP, lsl #32
    // 0x15e74f8: r1 = LoadClassIdInstr(r0)
    //     0x15e74f8: ldur            x1, [x0, #-1]
    //     0x15e74fc: ubfx            x1, x1, #0xc, #0x14
    // 0x15e7500: r16 = "image_text"
    //     0x15e7500: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15e7504: ldr             x16, [x16, #0xa88]
    // 0x15e7508: stp             x16, x0, [SP]
    // 0x15e750c: mov             x0, x1
    // 0x15e7510: mov             lr, x0
    // 0x15e7514: ldr             lr, [x21, lr, lsl #3]
    // 0x15e7518: blr             lr
    // 0x15e751c: tbnz            w0, #4, #0x15e7528
    // 0x15e7520: r2 = true
    //     0x15e7520: add             x2, NULL, #0x20  ; true
    // 0x15e7524: b               #0x15e7588
    // 0x15e7528: ldur            x0, [fp, #-8]
    // 0x15e752c: LoadField: r1 = r0->field_f
    //     0x15e752c: ldur            w1, [x0, #0xf]
    // 0x15e7530: DecompressPointer r1
    //     0x15e7530: add             x1, x1, HEAP, lsl #32
    // 0x15e7534: r0 = controller()
    //     0x15e7534: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e7538: LoadField: r1 = r0->field_4b
    //     0x15e7538: ldur            w1, [x0, #0x4b]
    // 0x15e753c: DecompressPointer r1
    //     0x15e753c: add             x1, x1, HEAP, lsl #32
    // 0x15e7540: r0 = value()
    //     0x15e7540: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e7544: LoadField: r1 = r0->field_3f
    //     0x15e7544: ldur            w1, [x0, #0x3f]
    // 0x15e7548: DecompressPointer r1
    //     0x15e7548: add             x1, x1, HEAP, lsl #32
    // 0x15e754c: cmp             w1, NULL
    // 0x15e7550: b.ne            #0x15e755c
    // 0x15e7554: r0 = Null
    //     0x15e7554: mov             x0, NULL
    // 0x15e7558: b               #0x15e7564
    // 0x15e755c: LoadField: r0 = r1->field_f
    //     0x15e755c: ldur            w0, [x1, #0xf]
    // 0x15e7560: DecompressPointer r0
    //     0x15e7560: add             x0, x0, HEAP, lsl #32
    // 0x15e7564: r1 = LoadClassIdInstr(r0)
    //     0x15e7564: ldur            x1, [x0, #-1]
    //     0x15e7568: ubfx            x1, x1, #0xc, #0x14
    // 0x15e756c: r16 = "image"
    //     0x15e756c: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x15e7570: stp             x16, x0, [SP]
    // 0x15e7574: mov             x0, x1
    // 0x15e7578: mov             lr, x0
    // 0x15e757c: ldr             lr, [x21, lr, lsl #3]
    // 0x15e7580: blr             lr
    // 0x15e7584: mov             x2, x0
    // 0x15e7588: ldur            x0, [fp, #-8]
    // 0x15e758c: stur            x2, [fp, #-0x10]
    // 0x15e7590: LoadField: r1 = r0->field_f
    //     0x15e7590: ldur            w1, [x0, #0xf]
    // 0x15e7594: DecompressPointer r1
    //     0x15e7594: add             x1, x1, HEAP, lsl #32
    // 0x15e7598: r0 = controller()
    //     0x15e7598: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e759c: LoadField: r1 = r0->field_4b
    //     0x15e759c: ldur            w1, [x0, #0x4b]
    // 0x15e75a0: DecompressPointer r1
    //     0x15e75a0: add             x1, x1, HEAP, lsl #32
    // 0x15e75a4: r0 = value()
    //     0x15e75a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e75a8: LoadField: r1 = r0->field_27
    //     0x15e75a8: ldur            w1, [x0, #0x27]
    // 0x15e75ac: DecompressPointer r1
    //     0x15e75ac: add             x1, x1, HEAP, lsl #32
    // 0x15e75b0: cmp             w1, NULL
    // 0x15e75b4: b.ne            #0x15e75c0
    // 0x15e75b8: r2 = ""
    //     0x15e75b8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15e75bc: b               #0x15e75c4
    // 0x15e75c0: mov             x2, x1
    // 0x15e75c4: ldur            x0, [fp, #-8]
    // 0x15e75c8: ldur            x1, [fp, #-0x10]
    // 0x15e75cc: stur            x2, [fp, #-0x18]
    // 0x15e75d0: r0 = ImageHeaders.forImages()
    //     0x15e75d0: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x15e75d4: r1 = Function '<anonymous closure>':.
    //     0x15e75d4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e5d8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x15e75d8: ldr             x1, [x1, #0x5d8]
    // 0x15e75dc: r2 = Null
    //     0x15e75dc: mov             x2, NULL
    // 0x15e75e0: stur            x0, [fp, #-0x20]
    // 0x15e75e4: r0 = AllocateClosure()
    //     0x15e75e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e75e8: r1 = Function '<anonymous closure>':.
    //     0x15e75e8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e5e0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x15e75ec: ldr             x1, [x1, #0x5e0]
    // 0x15e75f0: r2 = Null
    //     0x15e75f0: mov             x2, NULL
    // 0x15e75f4: stur            x0, [fp, #-0x28]
    // 0x15e75f8: r0 = AllocateClosure()
    //     0x15e75f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e75fc: stur            x0, [fp, #-0x30]
    // 0x15e7600: r0 = CachedNetworkImage()
    //     0x15e7600: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15e7604: stur            x0, [fp, #-0x38]
    // 0x15e7608: ldur            x16, [fp, #-0x20]
    // 0x15e760c: r30 = Instance_BoxFit
    //     0x15e760c: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15e7610: ldr             lr, [lr, #0xb18]
    // 0x15e7614: stp             lr, x16, [SP, #0x20]
    // 0x15e7618: r16 = 50.000000
    //     0x15e7618: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15e761c: ldr             x16, [x16, #0xa90]
    // 0x15e7620: r30 = 50.000000
    //     0x15e7620: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15e7624: ldr             lr, [lr, #0xa90]
    // 0x15e7628: stp             lr, x16, [SP, #0x10]
    // 0x15e762c: ldur            x16, [fp, #-0x28]
    // 0x15e7630: ldur            lr, [fp, #-0x30]
    // 0x15e7634: stp             lr, x16, [SP]
    // 0x15e7638: mov             x1, x0
    // 0x15e763c: ldur            x2, [fp, #-0x18]
    // 0x15e7640: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x3, height, 0x4, httpHeaders, 0x2, progressIndicatorBuilder, 0x6, width, 0x5, null]
    //     0x15e7640: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e5e8] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x3, "height", 0x4, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x6, "width", 0x5, Null]
    //     0x15e7644: ldr             x4, [x4, #0x5e8]
    // 0x15e7648: r0 = CachedNetworkImage()
    //     0x15e7648: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15e764c: r0 = Visibility()
    //     0x15e764c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15e7650: mov             x2, x0
    // 0x15e7654: ldur            x0, [fp, #-0x38]
    // 0x15e7658: stur            x2, [fp, #-0x18]
    // 0x15e765c: StoreField: r2->field_b = r0
    //     0x15e765c: stur            w0, [x2, #0xb]
    // 0x15e7660: r0 = Instance_SizedBox
    //     0x15e7660: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15e7664: StoreField: r2->field_f = r0
    //     0x15e7664: stur            w0, [x2, #0xf]
    // 0x15e7668: ldur            x1, [fp, #-0x10]
    // 0x15e766c: StoreField: r2->field_13 = r1
    //     0x15e766c: stur            w1, [x2, #0x13]
    // 0x15e7670: r3 = false
    //     0x15e7670: add             x3, NULL, #0x30  ; false
    // 0x15e7674: ArrayStore: r2[0] = r3  ; List_4
    //     0x15e7674: stur            w3, [x2, #0x17]
    // 0x15e7678: StoreField: r2->field_1b = r3
    //     0x15e7678: stur            w3, [x2, #0x1b]
    // 0x15e767c: StoreField: r2->field_1f = r3
    //     0x15e767c: stur            w3, [x2, #0x1f]
    // 0x15e7680: StoreField: r2->field_23 = r3
    //     0x15e7680: stur            w3, [x2, #0x23]
    // 0x15e7684: StoreField: r2->field_27 = r3
    //     0x15e7684: stur            w3, [x2, #0x27]
    // 0x15e7688: StoreField: r2->field_2b = r3
    //     0x15e7688: stur            w3, [x2, #0x2b]
    // 0x15e768c: ldur            x4, [fp, #-8]
    // 0x15e7690: LoadField: r1 = r4->field_f
    //     0x15e7690: ldur            w1, [x4, #0xf]
    // 0x15e7694: DecompressPointer r1
    //     0x15e7694: add             x1, x1, HEAP, lsl #32
    // 0x15e7698: r0 = controller()
    //     0x15e7698: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e769c: LoadField: r1 = r0->field_4b
    //     0x15e769c: ldur            w1, [x0, #0x4b]
    // 0x15e76a0: DecompressPointer r1
    //     0x15e76a0: add             x1, x1, HEAP, lsl #32
    // 0x15e76a4: r0 = value()
    //     0x15e76a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e76a8: LoadField: r1 = r0->field_3f
    //     0x15e76a8: ldur            w1, [x0, #0x3f]
    // 0x15e76ac: DecompressPointer r1
    //     0x15e76ac: add             x1, x1, HEAP, lsl #32
    // 0x15e76b0: cmp             w1, NULL
    // 0x15e76b4: b.ne            #0x15e76c0
    // 0x15e76b8: r0 = Null
    //     0x15e76b8: mov             x0, NULL
    // 0x15e76bc: b               #0x15e76c8
    // 0x15e76c0: LoadField: r0 = r1->field_f
    //     0x15e76c0: ldur            w0, [x1, #0xf]
    // 0x15e76c4: DecompressPointer r0
    //     0x15e76c4: add             x0, x0, HEAP, lsl #32
    // 0x15e76c8: r1 = LoadClassIdInstr(r0)
    //     0x15e76c8: ldur            x1, [x0, #-1]
    //     0x15e76cc: ubfx            x1, x1, #0xc, #0x14
    // 0x15e76d0: r16 = "image_text"
    //     0x15e76d0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15e76d4: ldr             x16, [x16, #0xa88]
    // 0x15e76d8: stp             x16, x0, [SP]
    // 0x15e76dc: mov             x0, x1
    // 0x15e76e0: mov             lr, x0
    // 0x15e76e4: ldr             lr, [x21, lr, lsl #3]
    // 0x15e76e8: blr             lr
    // 0x15e76ec: tbnz            w0, #4, #0x15e76f8
    // 0x15e76f0: r2 = true
    //     0x15e76f0: add             x2, NULL, #0x20  ; true
    // 0x15e76f4: b               #0x15e7758
    // 0x15e76f8: ldur            x0, [fp, #-8]
    // 0x15e76fc: LoadField: r1 = r0->field_f
    //     0x15e76fc: ldur            w1, [x0, #0xf]
    // 0x15e7700: DecompressPointer r1
    //     0x15e7700: add             x1, x1, HEAP, lsl #32
    // 0x15e7704: r0 = controller()
    //     0x15e7704: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e7708: LoadField: r1 = r0->field_4b
    //     0x15e7708: ldur            w1, [x0, #0x4b]
    // 0x15e770c: DecompressPointer r1
    //     0x15e770c: add             x1, x1, HEAP, lsl #32
    // 0x15e7710: r0 = value()
    //     0x15e7710: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e7714: LoadField: r1 = r0->field_3f
    //     0x15e7714: ldur            w1, [x0, #0x3f]
    // 0x15e7718: DecompressPointer r1
    //     0x15e7718: add             x1, x1, HEAP, lsl #32
    // 0x15e771c: cmp             w1, NULL
    // 0x15e7720: b.ne            #0x15e772c
    // 0x15e7724: r0 = Null
    //     0x15e7724: mov             x0, NULL
    // 0x15e7728: b               #0x15e7734
    // 0x15e772c: LoadField: r0 = r1->field_f
    //     0x15e772c: ldur            w0, [x1, #0xf]
    // 0x15e7730: DecompressPointer r0
    //     0x15e7730: add             x0, x0, HEAP, lsl #32
    // 0x15e7734: r1 = LoadClassIdInstr(r0)
    //     0x15e7734: ldur            x1, [x0, #-1]
    //     0x15e7738: ubfx            x1, x1, #0xc, #0x14
    // 0x15e773c: r16 = "text"
    //     0x15e773c: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15e7740: stp             x16, x0, [SP]
    // 0x15e7744: mov             x0, x1
    // 0x15e7748: mov             lr, x0
    // 0x15e774c: ldr             lr, [x21, lr, lsl #3]
    // 0x15e7750: blr             lr
    // 0x15e7754: mov             x2, x0
    // 0x15e7758: ldur            x0, [fp, #-8]
    // 0x15e775c: stur            x2, [fp, #-0x10]
    // 0x15e7760: LoadField: r1 = r0->field_f
    //     0x15e7760: ldur            w1, [x0, #0xf]
    // 0x15e7764: DecompressPointer r1
    //     0x15e7764: add             x1, x1, HEAP, lsl #32
    // 0x15e7768: r0 = controller()
    //     0x15e7768: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e776c: LoadField: r1 = r0->field_4b
    //     0x15e776c: ldur            w1, [x0, #0x4b]
    // 0x15e7770: DecompressPointer r1
    //     0x15e7770: add             x1, x1, HEAP, lsl #32
    // 0x15e7774: r0 = value()
    //     0x15e7774: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e7778: LoadField: r1 = r0->field_2b
    //     0x15e7778: ldur            w1, [x0, #0x2b]
    // 0x15e777c: DecompressPointer r1
    //     0x15e777c: add             x1, x1, HEAP, lsl #32
    // 0x15e7780: cmp             w1, NULL
    // 0x15e7784: b.ne            #0x15e7790
    // 0x15e7788: r4 = ""
    //     0x15e7788: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15e778c: b               #0x15e7794
    // 0x15e7790: mov             x4, x1
    // 0x15e7794: ldur            x0, [fp, #-8]
    // 0x15e7798: ldur            x3, [fp, #-0x18]
    // 0x15e779c: ldur            x2, [fp, #-0x10]
    // 0x15e77a0: stur            x4, [fp, #-0x20]
    // 0x15e77a4: LoadField: r1 = r0->field_13
    //     0x15e77a4: ldur            w1, [x0, #0x13]
    // 0x15e77a8: DecompressPointer r1
    //     0x15e77a8: add             x1, x1, HEAP, lsl #32
    // 0x15e77ac: r0 = of()
    //     0x15e77ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e77b0: LoadField: r1 = r0->field_87
    //     0x15e77b0: ldur            w1, [x0, #0x87]
    // 0x15e77b4: DecompressPointer r1
    //     0x15e77b4: add             x1, x1, HEAP, lsl #32
    // 0x15e77b8: LoadField: r0 = r1->field_2b
    //     0x15e77b8: ldur            w0, [x1, #0x2b]
    // 0x15e77bc: DecompressPointer r0
    //     0x15e77bc: add             x0, x0, HEAP, lsl #32
    // 0x15e77c0: r16 = 16.000000
    //     0x15e77c0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15e77c4: ldr             x16, [x16, #0x188]
    // 0x15e77c8: r30 = Instance_Color
    //     0x15e77c8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15e77cc: stp             lr, x16, [SP]
    // 0x15e77d0: mov             x1, x0
    // 0x15e77d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15e77d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15e77d8: ldr             x4, [x4, #0xaa0]
    // 0x15e77dc: r0 = copyWith()
    //     0x15e77dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15e77e0: stur            x0, [fp, #-8]
    // 0x15e77e4: r0 = Text()
    //     0x15e77e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15e77e8: mov             x1, x0
    // 0x15e77ec: ldur            x0, [fp, #-0x20]
    // 0x15e77f0: stur            x1, [fp, #-0x28]
    // 0x15e77f4: StoreField: r1->field_b = r0
    //     0x15e77f4: stur            w0, [x1, #0xb]
    // 0x15e77f8: ldur            x0, [fp, #-8]
    // 0x15e77fc: StoreField: r1->field_13 = r0
    //     0x15e77fc: stur            w0, [x1, #0x13]
    // 0x15e7800: r0 = Visibility()
    //     0x15e7800: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15e7804: mov             x3, x0
    // 0x15e7808: ldur            x0, [fp, #-0x28]
    // 0x15e780c: stur            x3, [fp, #-8]
    // 0x15e7810: StoreField: r3->field_b = r0
    //     0x15e7810: stur            w0, [x3, #0xb]
    // 0x15e7814: r0 = Instance_SizedBox
    //     0x15e7814: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15e7818: StoreField: r3->field_f = r0
    //     0x15e7818: stur            w0, [x3, #0xf]
    // 0x15e781c: ldur            x0, [fp, #-0x10]
    // 0x15e7820: StoreField: r3->field_13 = r0
    //     0x15e7820: stur            w0, [x3, #0x13]
    // 0x15e7824: r0 = false
    //     0x15e7824: add             x0, NULL, #0x30  ; false
    // 0x15e7828: ArrayStore: r3[0] = r0  ; List_4
    //     0x15e7828: stur            w0, [x3, #0x17]
    // 0x15e782c: StoreField: r3->field_1b = r0
    //     0x15e782c: stur            w0, [x3, #0x1b]
    // 0x15e7830: StoreField: r3->field_1f = r0
    //     0x15e7830: stur            w0, [x3, #0x1f]
    // 0x15e7834: StoreField: r3->field_23 = r0
    //     0x15e7834: stur            w0, [x3, #0x23]
    // 0x15e7838: StoreField: r3->field_27 = r0
    //     0x15e7838: stur            w0, [x3, #0x27]
    // 0x15e783c: StoreField: r3->field_2b = r0
    //     0x15e783c: stur            w0, [x3, #0x2b]
    // 0x15e7840: r1 = Null
    //     0x15e7840: mov             x1, NULL
    // 0x15e7844: r2 = 6
    //     0x15e7844: movz            x2, #0x6
    // 0x15e7848: r0 = AllocateArray()
    //     0x15e7848: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15e784c: mov             x2, x0
    // 0x15e7850: ldur            x0, [fp, #-0x18]
    // 0x15e7854: stur            x2, [fp, #-0x10]
    // 0x15e7858: StoreField: r2->field_f = r0
    //     0x15e7858: stur            w0, [x2, #0xf]
    // 0x15e785c: r16 = Instance_SizedBox
    //     0x15e785c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x15e7860: ldr             x16, [x16, #0xaa8]
    // 0x15e7864: StoreField: r2->field_13 = r16
    //     0x15e7864: stur            w16, [x2, #0x13]
    // 0x15e7868: ldur            x0, [fp, #-8]
    // 0x15e786c: ArrayStore: r2[0] = r0  ; List_4
    //     0x15e786c: stur            w0, [x2, #0x17]
    // 0x15e7870: r1 = <Widget>
    //     0x15e7870: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15e7874: r0 = AllocateGrowableArray()
    //     0x15e7874: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15e7878: mov             x1, x0
    // 0x15e787c: ldur            x0, [fp, #-0x10]
    // 0x15e7880: stur            x1, [fp, #-8]
    // 0x15e7884: StoreField: r1->field_f = r0
    //     0x15e7884: stur            w0, [x1, #0xf]
    // 0x15e7888: r0 = 6
    //     0x15e7888: movz            x0, #0x6
    // 0x15e788c: StoreField: r1->field_b = r0
    //     0x15e788c: stur            w0, [x1, #0xb]
    // 0x15e7890: r0 = Row()
    //     0x15e7890: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15e7894: r1 = Instance_Axis
    //     0x15e7894: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15e7898: StoreField: r0->field_f = r1
    //     0x15e7898: stur            w1, [x0, #0xf]
    // 0x15e789c: r1 = Instance_MainAxisAlignment
    //     0x15e789c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x15e78a0: ldr             x1, [x1, #0xab0]
    // 0x15e78a4: StoreField: r0->field_13 = r1
    //     0x15e78a4: stur            w1, [x0, #0x13]
    // 0x15e78a8: r1 = Instance_MainAxisSize
    //     0x15e78a8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15e78ac: ldr             x1, [x1, #0xa10]
    // 0x15e78b0: ArrayStore: r0[0] = r1  ; List_4
    //     0x15e78b0: stur            w1, [x0, #0x17]
    // 0x15e78b4: r1 = Instance_CrossAxisAlignment
    //     0x15e78b4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15e78b8: ldr             x1, [x1, #0xa18]
    // 0x15e78bc: StoreField: r0->field_1b = r1
    //     0x15e78bc: stur            w1, [x0, #0x1b]
    // 0x15e78c0: r1 = Instance_VerticalDirection
    //     0x15e78c0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15e78c4: ldr             x1, [x1, #0xa20]
    // 0x15e78c8: StoreField: r0->field_23 = r1
    //     0x15e78c8: stur            w1, [x0, #0x23]
    // 0x15e78cc: r1 = Instance_Clip
    //     0x15e78cc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15e78d0: ldr             x1, [x1, #0x38]
    // 0x15e78d4: StoreField: r0->field_2b = r1
    //     0x15e78d4: stur            w1, [x0, #0x2b]
    // 0x15e78d8: StoreField: r0->field_2f = rZR
    //     0x15e78d8: stur            xzr, [x0, #0x2f]
    // 0x15e78dc: ldur            x1, [fp, #-8]
    // 0x15e78e0: StoreField: r0->field_b = r1
    //     0x15e78e0: stur            w1, [x0, #0xb]
    // 0x15e78e4: LeaveFrame
    //     0x15e78e4: mov             SP, fp
    //     0x15e78e8: ldp             fp, lr, [SP], #0x10
    // 0x15e78ec: ret
    //     0x15e78ec: ret             
    // 0x15e78f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e78f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e78f4: b               #0x15e74c0
  }
}
