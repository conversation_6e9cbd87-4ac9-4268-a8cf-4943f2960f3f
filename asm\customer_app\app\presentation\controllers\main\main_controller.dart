// lib: , url: package:customer_app/app/presentation/controllers/main/main_controller.dart

// class id: 1049046, size: 0x8
class :: {
}

// class id: 1289, size: 0x60, field offset: 0x48
class MainController extends BaseController {

  late HomeRepo homeRepo; // offset: 0x48

  _ onInit(/* No info */) {
    // ** addr: 0x15a2238, size: 0x8c
    // 0x15a2238: EnterFrame
    //     0x15a2238: stp             fp, lr, [SP, #-0x10]!
    //     0x15a223c: mov             fp, SP
    // 0x15a2240: AllocStack(0x28)
    //     0x15a2240: sub             SP, SP, #0x28
    // 0x15a2244: SetupParameters(MainController this /* r1 => r1, fp-0x8 */)
    //     0x15a2244: stur            x1, [fp, #-8]
    // 0x15a2248: CheckStackOverflow
    //     0x15a2248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a224c: cmp             SP, x16
    //     0x15a2250: b.ls            #0x15a22bc
    // 0x15a2254: r1 = 1
    //     0x15a2254: movz            x1, #0x1
    // 0x15a2258: r0 = AllocateContext()
    //     0x15a2258: bl              #0x16f6108  ; AllocateContextStub
    // 0x15a225c: mov             x2, x0
    // 0x15a2260: ldur            x0, [fp, #-8]
    // 0x15a2264: stur            x2, [fp, #-0x10]
    // 0x15a2268: StoreField: r2->field_f = r0
    //     0x15a2268: stur            w0, [x2, #0xf]
    // 0x15a226c: LoadField: r1 = r0->field_4f
    //     0x15a226c: ldur            w1, [x0, #0x4f]
    // 0x15a2270: DecompressPointer r1
    //     0x15a2270: add             x1, x1, HEAP, lsl #32
    // 0x15a2274: r0 = getConnectivityType()
    //     0x15a2274: bl              #0x8a43bc  ; [package:customer_app/app/network/connection_controller.dart] ConnectionController::getConnectivityType
    // 0x15a2278: ldur            x2, [fp, #-0x10]
    // 0x15a227c: r1 = Function '<anonymous closure>':.
    //     0x15a227c: add             x1, PP, #0x12, lsl #12  ; [pp+0x127d0] AnonymousClosure: (0x15a22c4), in [package:customer_app/app/presentation/controllers/main/main_controller.dart] MainController::onInit (0x15a2238)
    //     0x15a2280: ldr             x1, [x1, #0x7d0]
    // 0x15a2284: stur            x0, [fp, #-0x10]
    // 0x15a2288: r0 = AllocateClosure()
    //     0x15a2288: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15a228c: r16 = <Null?>
    //     0x15a228c: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x15a2290: ldur            lr, [fp, #-0x10]
    // 0x15a2294: stp             lr, x16, [SP, #8]
    // 0x15a2298: str             x0, [SP]
    // 0x15a229c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15a229c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15a22a0: r0 = then()
    //     0x15a22a0: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15a22a4: ldur            x1, [fp, #-8]
    // 0x15a22a8: r0 = onInit()
    //     0x15a22a8: bl              #0x158ae60  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::onInit
    // 0x15a22ac: r0 = Null
    //     0x15a22ac: mov             x0, NULL
    // 0x15a22b0: LeaveFrame
    //     0x15a22b0: mov             SP, fp
    //     0x15a22b4: ldp             fp, lr, [SP], #0x10
    // 0x15a22b8: ret
    //     0x15a22b8: ret             
    // 0x15a22bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a22bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a22c0: b               #0x15a2254
  }
  [closure] Future<Null> <anonymous closure>(dynamic, bool) async {
    // ** addr: 0x15a22c4, size: 0x90
    // 0x15a22c4: EnterFrame
    //     0x15a22c4: stp             fp, lr, [SP, #-0x10]!
    //     0x15a22c8: mov             fp, SP
    // 0x15a22cc: AllocStack(0x18)
    //     0x15a22cc: sub             SP, SP, #0x18
    // 0x15a22d0: SetupParameters(MainController this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x15a22d0: stur            NULL, [fp, #-8]
    //     0x15a22d4: movz            x0, #0
    //     0x15a22d8: add             x1, fp, w0, sxtw #2
    //     0x15a22dc: ldr             x1, [x1, #0x18]
    //     0x15a22e0: add             x2, fp, w0, sxtw #2
    //     0x15a22e4: ldr             x2, [x2, #0x10]
    //     0x15a22e8: stur            x2, [fp, #-0x18]
    //     0x15a22ec: ldur            w3, [x1, #0x17]
    //     0x15a22f0: add             x3, x3, HEAP, lsl #32
    //     0x15a22f4: stur            x3, [fp, #-0x10]
    // 0x15a22f8: CheckStackOverflow
    //     0x15a22f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a22fc: cmp             SP, x16
    //     0x15a2300: b.ls            #0x15a234c
    // 0x15a2304: InitAsync() -> Future<Null?>
    //     0x15a2304: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0x15a2308: bl              #0x6326e0  ; InitAsyncStub
    // 0x15a230c: ldur            x0, [fp, #-0x10]
    // 0x15a2310: LoadField: r1 = r0->field_f
    //     0x15a2310: ldur            w1, [x0, #0xf]
    // 0x15a2314: DecompressPointer r1
    //     0x15a2314: add             x1, x1, HEAP, lsl #32
    // 0x15a2318: ldur            x2, [fp, #-0x18]
    // 0x15a231c: StoreField: r1->field_53 = r2
    //     0x15a231c: stur            w2, [x1, #0x53]
    // 0x15a2320: tbnz            w2, #4, #0x15a2344
    // 0x15a2324: r0 = fetchConfig()
    //     0x15a2324: bl              #0x15a2354  ; [package:customer_app/app/presentation/controllers/main/main_controller.dart] MainController::fetchConfig
    // 0x15a2328: mov             x1, x0
    // 0x15a232c: stur            x1, [fp, #-0x18]
    // 0x15a2330: r0 = Await()
    //     0x15a2330: bl              #0x63248c  ; AwaitStub
    // 0x15a2334: ldur            x0, [fp, #-0x10]
    // 0x15a2338: LoadField: r1 = r0->field_f
    //     0x15a2338: ldur            w1, [x0, #0xf]
    // 0x15a233c: DecompressPointer r1
    //     0x15a233c: add             x1, x1, HEAP, lsl #32
    // 0x15a2340: r0 = getSalesAndCouponData()
    //     0x15a2340: bl              #0x8b2268  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::getSalesAndCouponData
    // 0x15a2344: r0 = Null
    //     0x15a2344: mov             x0, NULL
    // 0x15a2348: r0 = ReturnAsyncNotFuture()
    //     0x15a2348: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x15a234c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a234c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a2350: b               #0x15a2304
  }
  _ fetchConfig(/* No info */) async {
    // ** addr: 0x15a2354, size: 0xb8
    // 0x15a2354: EnterFrame
    //     0x15a2354: stp             fp, lr, [SP, #-0x10]!
    //     0x15a2358: mov             fp, SP
    // 0x15a235c: AllocStack(0x48)
    //     0x15a235c: sub             SP, SP, #0x48
    // 0x15a2360: SetupParameters(MainController this /* r1 => r2, fp-0x10 */)
    //     0x15a2360: stur            NULL, [fp, #-8]
    //     0x15a2364: mov             x2, x1
    //     0x15a2368: stur            x1, [fp, #-0x10]
    // 0x15a236c: CheckStackOverflow
    //     0x15a236c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a2370: cmp             SP, x16
    //     0x15a2374: b.ls            #0x15a23f8
    // 0x15a2378: InitAsync() -> Future<ConfigResponse>
    //     0x15a2378: add             x0, PP, #0xa, lsl #12  ; [pp+0xab08] TypeArguments: <ConfigResponse>
    //     0x15a237c: ldr             x0, [x0, #0xb08]
    //     0x15a2380: bl              #0x6326e0  ; InitAsyncStub
    // 0x15a2384: ldur            x2, [fp, #-0x10]
    // 0x15a2388: LoadField: r1 = r2->field_47
    //     0x15a2388: ldur            w1, [x2, #0x47]
    // 0x15a238c: DecompressPointer r1
    //     0x15a238c: add             x1, x1, HEAP, lsl #32
    // 0x15a2390: r16 = Sentinel
    //     0x15a2390: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x15a2394: cmp             w1, w16
    // 0x15a2398: b.eq            #0x15a2400
    // 0x15a239c: r0 = fetchWebsiteConfig()
    //     0x15a239c: bl              #0x90a2d4  ; [package:customer_app/app/data/repositories/home/<USER>
    // 0x15a23a0: ldur            x2, [fp, #-0x10]
    // 0x15a23a4: r1 = Function '_handleFetchWebConfig@1162135768':.
    //     0x15a23a4: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f10] AnonymousClosure: (0x15a24d0), in [package:customer_app/app/presentation/controllers/main/main_controller.dart] MainController::_handleFetchWebConfig (0x15a250c)
    //     0x15a23a8: ldr             x1, [x1, #0xf10]
    // 0x15a23ac: stur            x0, [fp, #-0x18]
    // 0x15a23b0: r0 = AllocateClosure()
    //     0x15a23b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15a23b4: ldur            x2, [fp, #-0x10]
    // 0x15a23b8: r1 = Function '_handleOnError@1162135768':.
    //     0x15a23b8: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f18] AnonymousClosure: (0x15a240c), in [package:customer_app/app/presentation/controllers/main/main_controller.dart] MainController::_handleOnError (0x15a2448)
    //     0x15a23bc: ldr             x1, [x1, #0xf18]
    // 0x15a23c0: stur            x0, [fp, #-0x20]
    // 0x15a23c4: r0 = AllocateClosure()
    //     0x15a23c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15a23c8: r16 = <ConfigResponse>
    //     0x15a23c8: add             x16, PP, #0xa, lsl #12  ; [pp+0xab08] TypeArguments: <ConfigResponse>
    //     0x15a23cc: ldr             x16, [x16, #0xb08]
    // 0x15a23d0: ldur            lr, [fp, #-0x10]
    // 0x15a23d4: stp             lr, x16, [SP, #0x18]
    // 0x15a23d8: ldur            x16, [fp, #-0x18]
    // 0x15a23dc: stp             x0, x16, [SP, #8]
    // 0x15a23e0: ldur            x16, [fp, #-0x20]
    // 0x15a23e4: str             x16, [SP]
    // 0x15a23e8: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x15a23e8: ldr             x4, [PP, #0x5a8]  ; [pp+0x5a8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x15a23ec: r0 = callDataService()
    //     0x15a23ec: bl              #0x860494  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::callDataService
    // 0x15a23f0: ldur            x0, [fp, #-0x18]
    // 0x15a23f4: r0 = ReturnAsync()
    //     0x15a23f4: b               #0x63cf54  ; ReturnAsyncStub
    // 0x15a23f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a23f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a23fc: b               #0x15a2378
    // 0x15a2400: r9 = homeRepo
    //     0x15a2400: add             x9, PP, #0x12, lsl #12  ; [pp+0x12f20] Field <MainController.homeRepo>: late (offset: 0x48)
    //     0x15a2404: ldr             x9, [x9, #0xf20]
    // 0x15a2408: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x15a2408: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic _handleOnError(dynamic, Exception) {
    // ** addr: 0x15a240c, size: 0x3c
    // 0x15a240c: EnterFrame
    //     0x15a240c: stp             fp, lr, [SP, #-0x10]!
    //     0x15a2410: mov             fp, SP
    // 0x15a2414: ldr             x0, [fp, #0x18]
    // 0x15a2418: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x15a2418: ldur            w1, [x0, #0x17]
    // 0x15a241c: DecompressPointer r1
    //     0x15a241c: add             x1, x1, HEAP, lsl #32
    // 0x15a2420: CheckStackOverflow
    //     0x15a2420: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a2424: cmp             SP, x16
    //     0x15a2428: b.ls            #0x15a2440
    // 0x15a242c: ldr             x2, [fp, #0x10]
    // 0x15a2430: r0 = _handleOnError()
    //     0x15a2430: bl              #0x15a2448  ; [package:customer_app/app/presentation/controllers/main/main_controller.dart] MainController::_handleOnError
    // 0x15a2434: LeaveFrame
    //     0x15a2434: mov             SP, fp
    //     0x15a2438: ldp             fp, lr, [SP], #0x10
    // 0x15a243c: ret
    //     0x15a243c: ret             
    // 0x15a2440: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a2440: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a2444: b               #0x15a242c
  }
  _ _handleOnError(/* No info */) {
    // ** addr: 0x15a2448, size: 0x88
    // 0x15a2448: EnterFrame
    //     0x15a2448: stp             fp, lr, [SP, #-0x10]!
    //     0x15a244c: mov             fp, SP
    // 0x15a2450: AllocStack(0x10)
    //     0x15a2450: sub             SP, SP, #0x10
    // 0x15a2454: SetupParameters(MainController this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x15a2454: mov             x4, x1
    //     0x15a2458: mov             x3, x2
    //     0x15a245c: stur            x1, [fp, #-8]
    //     0x15a2460: stur            x2, [fp, #-0x10]
    // 0x15a2464: CheckStackOverflow
    //     0x15a2464: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a2468: cmp             SP, x16
    //     0x15a246c: b.ls            #0x15a24c8
    // 0x15a2470: mov             x0, x3
    // 0x15a2474: r2 = Null
    //     0x15a2474: mov             x2, NULL
    // 0x15a2478: r1 = Null
    //     0x15a2478: mov             x1, NULL
    // 0x15a247c: r4 = LoadClassIdInstr(r0)
    //     0x15a247c: ldur            x4, [x0, #-1]
    //     0x15a2480: ubfx            x4, x4, #0xc, #0x14
    // 0x15a2484: r17 = -5018
    //     0x15a2484: movn            x17, #0x1399
    // 0x15a2488: add             x4, x4, x17
    // 0x15a248c: cmp             x4, #8
    // 0x15a2490: b.ls            #0x15a24a4
    // 0x15a2494: r8 = BaseException
    //     0x15a2494: ldr             x8, [PP, #0x7e90]  ; [pp+0x7e90] Type: BaseException
    // 0x15a2498: r3 = Null
    //     0x15a2498: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f28] Null
    //     0x15a249c: ldr             x3, [x3, #0xf28]
    // 0x15a24a0: r0 = DefaultTypeTest()
    //     0x15a24a0: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x15a24a4: ldur            x0, [fp, #-0x10]
    // 0x15a24a8: LoadField: r2 = r0->field_7
    //     0x15a24a8: ldur            w2, [x0, #7]
    // 0x15a24ac: DecompressPointer r2
    //     0x15a24ac: add             x2, x2, HEAP, lsl #32
    // 0x15a24b0: ldur            x1, [fp, #-8]
    // 0x15a24b4: r0 = showErrorMessage()
    //     0x15a24b4: bl              #0x89d444  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::showErrorMessage
    // 0x15a24b8: r0 = Null
    //     0x15a24b8: mov             x0, NULL
    // 0x15a24bc: LeaveFrame
    //     0x15a24bc: mov             SP, fp
    //     0x15a24c0: ldp             fp, lr, [SP], #0x10
    // 0x15a24c4: ret
    //     0x15a24c4: ret             
    // 0x15a24c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a24c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a24cc: b               #0x15a2470
  }
  [closure] dynamic _handleFetchWebConfig(dynamic, dynamic) {
    // ** addr: 0x15a24d0, size: 0x3c
    // 0x15a24d0: EnterFrame
    //     0x15a24d0: stp             fp, lr, [SP, #-0x10]!
    //     0x15a24d4: mov             fp, SP
    // 0x15a24d8: ldr             x0, [fp, #0x18]
    // 0x15a24dc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x15a24dc: ldur            w1, [x0, #0x17]
    // 0x15a24e0: DecompressPointer r1
    //     0x15a24e0: add             x1, x1, HEAP, lsl #32
    // 0x15a24e4: CheckStackOverflow
    //     0x15a24e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a24e8: cmp             SP, x16
    //     0x15a24ec: b.ls            #0x15a2504
    // 0x15a24f0: ldr             x2, [fp, #0x10]
    // 0x15a24f4: r0 = _handleFetchWebConfig()
    //     0x15a24f4: bl              #0x15a250c  ; [package:customer_app/app/presentation/controllers/main/main_controller.dart] MainController::_handleFetchWebConfig
    // 0x15a24f8: LeaveFrame
    //     0x15a24f8: mov             SP, fp
    //     0x15a24fc: ldp             fp, lr, [SP], #0x10
    // 0x15a2500: ret
    //     0x15a2500: ret             
    // 0x15a2504: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a2504: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a2508: b               #0x15a24f0
  }
  _ _handleFetchWebConfig(/* No info */) {
    // ** addr: 0x15a250c, size: 0x54
    // 0x15a250c: EnterFrame
    //     0x15a250c: stp             fp, lr, [SP, #-0x10]!
    //     0x15a2510: mov             fp, SP
    // 0x15a2514: AllocStack(0x8)
    //     0x15a2514: sub             SP, SP, #8
    // 0x15a2518: SetupParameters(MainController this /* r1 => r0, fp-0x8 */)
    //     0x15a2518: mov             x0, x1
    //     0x15a251c: stur            x1, [fp, #-8]
    // 0x15a2520: CheckStackOverflow
    //     0x15a2520: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a2524: cmp             SP, x16
    //     0x15a2528: b.ls            #0x15a2558
    // 0x15a252c: mov             x1, x0
    // 0x15a2530: r0 = configResponse=()
    //     0x15a2530: bl              #0x15a7088  ; [package:customer_app/app/presentation/controllers/main/main_controller.dart] MainController::configResponse=
    // 0x15a2534: ldur            x1, [fp, #-8]
    // 0x15a2538: r0 = configResponse()
    //     0x15a2538: bl              #0x8b5174  ; [package:customer_app/app/config_controller.dart] ConfigController::configResponse
    // 0x15a253c: ldur            x1, [fp, #-8]
    // 0x15a2540: mov             x2, x0
    // 0x15a2544: r0 = saveLocal()
    //     0x15a2544: bl              #0x15a2560  ; [package:customer_app/app/presentation/controllers/main/main_controller.dart] MainController::saveLocal
    // 0x15a2548: r0 = Null
    //     0x15a2548: mov             x0, NULL
    // 0x15a254c: LeaveFrame
    //     0x15a254c: mov             SP, fp
    //     0x15a2550: ldp             fp, lr, [SP], #0x10
    // 0x15a2554: ret
    //     0x15a2554: ret             
    // 0x15a2558: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a2558: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a255c: b               #0x15a252c
  }
  _ saveLocal(/* No info */) async {
    // ** addr: 0x15a2560, size: 0x4b28
    // 0x15a2560: EnterFrame
    //     0x15a2560: stp             fp, lr, [SP, #-0x10]!
    //     0x15a2564: mov             fp, SP
    // 0x15a2568: AllocStack(0x1a0)
    //     0x15a2568: sub             SP, SP, #0x1a0
    // 0x15a256c: SetupParameters(MainController this /* r1 => r1, fp-0xb8 */, dynamic _ /* r2 => r2, fp-0xc0 */)
    //     0x15a256c: stur            NULL, [fp, #-8]
    //     0x15a2570: stur            x1, [fp, #-0xb8]
    //     0x15a2574: stur            x2, [fp, #-0xc0]
    // 0x15a2578: CheckStackOverflow
    //     0x15a2578: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a257c: cmp             SP, x16
    //     0x15a2580: b.ls            #0x15a7080
    // 0x15a2584: InitAsync() -> Future<void?>
    //     0x15a2584: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x15a2588: bl              #0x6326e0  ; InitAsyncStub
    // 0x15a258c: ldur            x0, [fp, #-0xc0]
    // 0x15a2590: LoadField: r1 = r0->field_b
    //     0x15a2590: ldur            w1, [x0, #0xb]
    // 0x15a2594: DecompressPointer r1
    //     0x15a2594: add             x1, x1, HEAP, lsl #32
    // 0x15a2598: cmp             w1, NULL
    // 0x15a259c: b.eq            #0x15a25c8
    // 0x15a25a0: ldur            x2, [fp, #-0xb8]
    // 0x15a25a4: r0 = LocalConfigDataExtension.toLocalConfigData()
    //     0x15a25a4: bl              #0x90d72c  ; [package:customer_app/app/core/extension/extension_function.dart] ::LocalConfigDataExtension.toLocalConfigData
    // 0x15a25a8: mov             x1, x0
    // 0x15a25ac: ldur            x0, [fp, #-0xb8]
    // 0x15a25b0: LoadField: r2 = r0->field_4b
    //     0x15a25b0: ldur            w2, [x0, #0x4b]
    // 0x15a25b4: DecompressPointer r2
    //     0x15a25b4: add             x2, x2, HEAP, lsl #32
    // 0x15a25b8: mov             x16, x1
    // 0x15a25bc: mov             x1, x2
    // 0x15a25c0: mov             x2, x16
    // 0x15a25c4: r0 = setConfigData()
    //     0x15a25c4: bl              #0x90d650  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::setConfigData
    // 0x15a25c8: ldur            x0, [fp, #-0xb8]
    // 0x15a25cc: LoadField: r1 = r0->field_4b
    //     0x15a25cc: ldur            w1, [x0, #0x4b]
    // 0x15a25d0: DecompressPointer r1
    //     0x15a25d0: add             x1, x1, HEAP, lsl #32
    // 0x15a25d4: r2 = "policySaved"
    //     0x15a25d4: add             x2, PP, #0x10, lsl #12  ; [pp+0x10ae8] "policySaved"
    //     0x15a25d8: ldr             x2, [x2, #0xae8]
    // 0x15a25dc: r3 = true
    //     0x15a25dc: add             x3, NULL, #0x20  ; true
    // 0x15a25e0: r0 = setBool()
    //     0x15a25e0: bl              #0x90d550  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::setBool
    // 0x15a25e4: ldur            x0, [fp, #-0xc0]
    // 0x15a25e8: r0 = Color()
    //     0x15a25e8: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a25ec: mov             x2, x0
    // 0x15a25f0: ldur            x0, [fp, #-0xc0]
    // 0x15a25f4: stur            x2, [fp, #-0xd0]
    // 0x15a25f8: LoadField: r1 = r0->field_b
    //     0x15a25f8: ldur            w1, [x0, #0xb]
    // 0x15a25fc: DecompressPointer r1
    //     0x15a25fc: add             x1, x1, HEAP, lsl #32
    // 0x15a2600: cmp             w1, NULL
    // 0x15a2604: b.ne            #0x15a2610
    // 0x15a2608: r3 = Null
    //     0x15a2608: mov             x3, NULL
    // 0x15a260c: b               #0x15a2648
    // 0x15a2610: LoadField: r3 = r1->field_57
    //     0x15a2610: ldur            w3, [x1, #0x57]
    // 0x15a2614: DecompressPointer r3
    //     0x15a2614: add             x3, x3, HEAP, lsl #32
    // 0x15a2618: cmp             w3, NULL
    // 0x15a261c: b.ne            #0x15a2628
    // 0x15a2620: r3 = Null
    //     0x15a2620: mov             x3, NULL
    // 0x15a2624: b               #0x15a2648
    // 0x15a2628: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a2628: ldur            w4, [x3, #0x17]
    // 0x15a262c: DecompressPointer r4
    //     0x15a262c: add             x4, x4, HEAP, lsl #32
    // 0x15a2630: cmp             w4, NULL
    // 0x15a2634: b.ne            #0x15a2640
    // 0x15a2638: r3 = Null
    //     0x15a2638: mov             x3, NULL
    // 0x15a263c: b               #0x15a2648
    // 0x15a2640: LoadField: r3 = r4->field_7
    //     0x15a2640: ldur            w3, [x4, #7]
    // 0x15a2644: DecompressPointer r3
    //     0x15a2644: add             x3, x3, HEAP, lsl #32
    // 0x15a2648: cmp             w3, NULL
    // 0x15a264c: b.ne            #0x15a2658
    // 0x15a2650: r3 = 0
    //     0x15a2650: movz            x3, #0
    // 0x15a2654: b               #0x15a2668
    // 0x15a2658: r4 = LoadInt32Instr(r3)
    //     0x15a2658: sbfx            x4, x3, #1, #0x1f
    //     0x15a265c: tbz             w3, #0, #0x15a2664
    //     0x15a2660: ldur            x4, [x3, #7]
    // 0x15a2664: mov             x3, x4
    // 0x15a2668: cmp             w1, NULL
    // 0x15a266c: b.ne            #0x15a2678
    // 0x15a2670: r4 = Null
    //     0x15a2670: mov             x4, NULL
    // 0x15a2674: b               #0x15a26b0
    // 0x15a2678: LoadField: r4 = r1->field_57
    //     0x15a2678: ldur            w4, [x1, #0x57]
    // 0x15a267c: DecompressPointer r4
    //     0x15a267c: add             x4, x4, HEAP, lsl #32
    // 0x15a2680: cmp             w4, NULL
    // 0x15a2684: b.ne            #0x15a2690
    // 0x15a2688: r4 = Null
    //     0x15a2688: mov             x4, NULL
    // 0x15a268c: b               #0x15a26b0
    // 0x15a2690: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a2690: ldur            w5, [x4, #0x17]
    // 0x15a2694: DecompressPointer r5
    //     0x15a2694: add             x5, x5, HEAP, lsl #32
    // 0x15a2698: cmp             w5, NULL
    // 0x15a269c: b.ne            #0x15a26a8
    // 0x15a26a0: r4 = Null
    //     0x15a26a0: mov             x4, NULL
    // 0x15a26a4: b               #0x15a26b0
    // 0x15a26a8: LoadField: r4 = r5->field_b
    //     0x15a26a8: ldur            w4, [x5, #0xb]
    // 0x15a26ac: DecompressPointer r4
    //     0x15a26ac: add             x4, x4, HEAP, lsl #32
    // 0x15a26b0: cmp             w4, NULL
    // 0x15a26b4: b.ne            #0x15a26c0
    // 0x15a26b8: r4 = 0
    //     0x15a26b8: movz            x4, #0
    // 0x15a26bc: b               #0x15a26d0
    // 0x15a26c0: r5 = LoadInt32Instr(r4)
    //     0x15a26c0: sbfx            x5, x4, #1, #0x1f
    //     0x15a26c4: tbz             w4, #0, #0x15a26cc
    //     0x15a26c8: ldur            x5, [x4, #7]
    // 0x15a26cc: mov             x4, x5
    // 0x15a26d0: cmp             w1, NULL
    // 0x15a26d4: b.ne            #0x15a26e0
    // 0x15a26d8: r1 = Null
    //     0x15a26d8: mov             x1, NULL
    // 0x15a26dc: b               #0x15a271c
    // 0x15a26e0: LoadField: r5 = r1->field_57
    //     0x15a26e0: ldur            w5, [x1, #0x57]
    // 0x15a26e4: DecompressPointer r5
    //     0x15a26e4: add             x5, x5, HEAP, lsl #32
    // 0x15a26e8: cmp             w5, NULL
    // 0x15a26ec: b.ne            #0x15a26f8
    // 0x15a26f0: r1 = Null
    //     0x15a26f0: mov             x1, NULL
    // 0x15a26f4: b               #0x15a271c
    // 0x15a26f8: ArrayLoad: r1 = r5[0]  ; List_4
    //     0x15a26f8: ldur            w1, [x5, #0x17]
    // 0x15a26fc: DecompressPointer r1
    //     0x15a26fc: add             x1, x1, HEAP, lsl #32
    // 0x15a2700: cmp             w1, NULL
    // 0x15a2704: b.ne            #0x15a2710
    // 0x15a2708: r1 = Null
    //     0x15a2708: mov             x1, NULL
    // 0x15a270c: b               #0x15a271c
    // 0x15a2710: LoadField: r5 = r1->field_f
    //     0x15a2710: ldur            w5, [x1, #0xf]
    // 0x15a2714: DecompressPointer r5
    //     0x15a2714: add             x5, x5, HEAP, lsl #32
    // 0x15a2718: mov             x1, x5
    // 0x15a271c: cmp             w1, NULL
    // 0x15a2720: b.ne            #0x15a272c
    // 0x15a2724: r6 = 0
    //     0x15a2724: movz            x6, #0
    // 0x15a2728: b               #0x15a273c
    // 0x15a272c: r5 = LoadInt32Instr(r1)
    //     0x15a272c: sbfx            x5, x1, #1, #0x1f
    //     0x15a2730: tbz             w1, #0, #0x15a2738
    //     0x15a2734: ldur            x5, [x1, #7]
    // 0x15a2738: mov             x6, x5
    // 0x15a273c: r5 = Instance_ColorSpace
    //     0x15a273c: ldr             x5, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a2740: d1 = 255.000000
    //     0x15a2740: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a2744: d0 = 1.000000
    //     0x15a2744: fmov            d0, #1.00000000
    // 0x15a2748: stur            x6, [fp, #-0xc8]
    // 0x15a274c: StoreField: r2->field_27 = r5
    //     0x15a274c: stur            w5, [x2, #0x27]
    // 0x15a2750: StoreField: r2->field_7 = d0
    //     0x15a2750: stur            d0, [x2, #7]
    // 0x15a2754: ubfx            x3, x3, #0, #0x20
    // 0x15a2758: and             w1, w3, #0xff
    // 0x15a275c: ubfx            x1, x1, #0, #0x20
    // 0x15a2760: scvtf           d2, x1
    // 0x15a2764: fdiv            d3, d2, d1
    // 0x15a2768: StoreField: r2->field_f = d3
    //     0x15a2768: stur            d3, [x2, #0xf]
    // 0x15a276c: ubfx            x4, x4, #0, #0x20
    // 0x15a2770: and             w1, w4, #0xff
    // 0x15a2774: ubfx            x1, x1, #0, #0x20
    // 0x15a2778: scvtf           d2, x1
    // 0x15a277c: fdiv            d3, d2, d1
    // 0x15a2780: ArrayStore: r2[0] = d3  ; List_8
    //     0x15a2780: stur            d3, [x2, #0x17]
    // 0x15a2784: mov             x1, x6
    // 0x15a2788: ubfx            x1, x1, #0, #0x20
    // 0x15a278c: and             w3, w1, #0xff
    // 0x15a2790: ubfx            x3, x3, #0, #0x20
    // 0x15a2794: scvtf           d2, x3
    // 0x15a2798: fdiv            d3, d2, d1
    // 0x15a279c: StoreField: r2->field_1f = d3
    //     0x15a279c: stur            d3, [x2, #0x1f]
    // 0x15a27a0: r1 = Null
    //     0x15a27a0: mov             x1, NULL
    // 0x15a27a4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15a27a4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15a27a8: r0 = ColorScheme.fromSwatch()
    //     0x15a27a8: bl              #0x6b3aa4  ; [package:flutter/src/material/color_scheme.dart] ColorScheme::ColorScheme.fromSwatch
    // 0x15a27ac: stur            x0, [fp, #-0xd8]
    // 0x15a27b0: r0 = Color()
    //     0x15a27b0: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a27b4: mov             x1, x0
    // 0x15a27b8: ldur            x0, [fp, #-0xc0]
    // 0x15a27bc: stur            x1, [fp, #-0xe8]
    // 0x15a27c0: LoadField: r2 = r0->field_b
    //     0x15a27c0: ldur            w2, [x0, #0xb]
    // 0x15a27c4: DecompressPointer r2
    //     0x15a27c4: add             x2, x2, HEAP, lsl #32
    // 0x15a27c8: stur            x2, [fp, #-0xe0]
    // 0x15a27cc: cmp             w2, NULL
    // 0x15a27d0: b.ne            #0x15a27dc
    // 0x15a27d4: r3 = Null
    //     0x15a27d4: mov             x3, NULL
    // 0x15a27d8: b               #0x15a2814
    // 0x15a27dc: LoadField: r3 = r2->field_57
    //     0x15a27dc: ldur            w3, [x2, #0x57]
    // 0x15a27e0: DecompressPointer r3
    //     0x15a27e0: add             x3, x3, HEAP, lsl #32
    // 0x15a27e4: cmp             w3, NULL
    // 0x15a27e8: b.ne            #0x15a27f4
    // 0x15a27ec: r3 = Null
    //     0x15a27ec: mov             x3, NULL
    // 0x15a27f0: b               #0x15a2814
    // 0x15a27f4: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a27f4: ldur            w4, [x3, #0x17]
    // 0x15a27f8: DecompressPointer r4
    //     0x15a27f8: add             x4, x4, HEAP, lsl #32
    // 0x15a27fc: cmp             w4, NULL
    // 0x15a2800: b.ne            #0x15a280c
    // 0x15a2804: r3 = Null
    //     0x15a2804: mov             x3, NULL
    // 0x15a2808: b               #0x15a2814
    // 0x15a280c: LoadField: r3 = r4->field_7
    //     0x15a280c: ldur            w3, [x4, #7]
    // 0x15a2810: DecompressPointer r3
    //     0x15a2810: add             x3, x3, HEAP, lsl #32
    // 0x15a2814: cmp             w3, NULL
    // 0x15a2818: b.ne            #0x15a2824
    // 0x15a281c: r3 = 0
    //     0x15a281c: movz            x3, #0
    // 0x15a2820: b               #0x15a2834
    // 0x15a2824: r4 = LoadInt32Instr(r3)
    //     0x15a2824: sbfx            x4, x3, #1, #0x1f
    //     0x15a2828: tbz             w3, #0, #0x15a2830
    //     0x15a282c: ldur            x4, [x3, #7]
    // 0x15a2830: mov             x3, x4
    // 0x15a2834: cmp             w2, NULL
    // 0x15a2838: b.ne            #0x15a2844
    // 0x15a283c: r4 = Null
    //     0x15a283c: mov             x4, NULL
    // 0x15a2840: b               #0x15a287c
    // 0x15a2844: LoadField: r4 = r2->field_57
    //     0x15a2844: ldur            w4, [x2, #0x57]
    // 0x15a2848: DecompressPointer r4
    //     0x15a2848: add             x4, x4, HEAP, lsl #32
    // 0x15a284c: cmp             w4, NULL
    // 0x15a2850: b.ne            #0x15a285c
    // 0x15a2854: r4 = Null
    //     0x15a2854: mov             x4, NULL
    // 0x15a2858: b               #0x15a287c
    // 0x15a285c: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a285c: ldur            w5, [x4, #0x17]
    // 0x15a2860: DecompressPointer r5
    //     0x15a2860: add             x5, x5, HEAP, lsl #32
    // 0x15a2864: cmp             w5, NULL
    // 0x15a2868: b.ne            #0x15a2874
    // 0x15a286c: r4 = Null
    //     0x15a286c: mov             x4, NULL
    // 0x15a2870: b               #0x15a287c
    // 0x15a2874: LoadField: r4 = r5->field_b
    //     0x15a2874: ldur            w4, [x5, #0xb]
    // 0x15a2878: DecompressPointer r4
    //     0x15a2878: add             x4, x4, HEAP, lsl #32
    // 0x15a287c: cmp             w4, NULL
    // 0x15a2880: b.ne            #0x15a288c
    // 0x15a2884: r4 = 0
    //     0x15a2884: movz            x4, #0
    // 0x15a2888: b               #0x15a289c
    // 0x15a288c: r5 = LoadInt32Instr(r4)
    //     0x15a288c: sbfx            x5, x4, #1, #0x1f
    //     0x15a2890: tbz             w4, #0, #0x15a2898
    //     0x15a2894: ldur            x5, [x4, #7]
    // 0x15a2898: mov             x4, x5
    // 0x15a289c: cmp             w2, NULL
    // 0x15a28a0: b.ne            #0x15a28ac
    // 0x15a28a4: r5 = Null
    //     0x15a28a4: mov             x5, NULL
    // 0x15a28a8: b               #0x15a28e4
    // 0x15a28ac: LoadField: r5 = r2->field_57
    //     0x15a28ac: ldur            w5, [x2, #0x57]
    // 0x15a28b0: DecompressPointer r5
    //     0x15a28b0: add             x5, x5, HEAP, lsl #32
    // 0x15a28b4: cmp             w5, NULL
    // 0x15a28b8: b.ne            #0x15a28c4
    // 0x15a28bc: r5 = Null
    //     0x15a28bc: mov             x5, NULL
    // 0x15a28c0: b               #0x15a28e4
    // 0x15a28c4: ArrayLoad: r6 = r5[0]  ; List_4
    //     0x15a28c4: ldur            w6, [x5, #0x17]
    // 0x15a28c8: DecompressPointer r6
    //     0x15a28c8: add             x6, x6, HEAP, lsl #32
    // 0x15a28cc: cmp             w6, NULL
    // 0x15a28d0: b.ne            #0x15a28dc
    // 0x15a28d4: r5 = Null
    //     0x15a28d4: mov             x5, NULL
    // 0x15a28d8: b               #0x15a28e4
    // 0x15a28dc: LoadField: r5 = r6->field_f
    //     0x15a28dc: ldur            w5, [x6, #0xf]
    // 0x15a28e0: DecompressPointer r5
    //     0x15a28e0: add             x5, x5, HEAP, lsl #32
    // 0x15a28e4: cmp             w5, NULL
    // 0x15a28e8: b.ne            #0x15a28f4
    // 0x15a28ec: r6 = 0
    //     0x15a28ec: movz            x6, #0
    // 0x15a28f0: b               #0x15a2900
    // 0x15a28f4: r6 = LoadInt32Instr(r5)
    //     0x15a28f4: sbfx            x6, x5, #1, #0x1f
    //     0x15a28f8: tbz             w5, #0, #0x15a2900
    //     0x15a28fc: ldur            x6, [x5, #7]
    // 0x15a2900: r5 = Instance_ColorSpace
    //     0x15a2900: ldr             x5, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a2904: d1 = 255.000000
    //     0x15a2904: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a2908: d0 = 1.000000
    //     0x15a2908: fmov            d0, #1.00000000
    // 0x15a290c: stur            x6, [fp, #-0xc8]
    // 0x15a2910: StoreField: r1->field_27 = r5
    //     0x15a2910: stur            w5, [x1, #0x27]
    // 0x15a2914: StoreField: r1->field_7 = d0
    //     0x15a2914: stur            d0, [x1, #7]
    // 0x15a2918: ubfx            x3, x3, #0, #0x20
    // 0x15a291c: and             w7, w3, #0xff
    // 0x15a2920: ubfx            x7, x7, #0, #0x20
    // 0x15a2924: scvtf           d2, x7
    // 0x15a2928: fdiv            d3, d2, d1
    // 0x15a292c: StoreField: r1->field_f = d3
    //     0x15a292c: stur            d3, [x1, #0xf]
    // 0x15a2930: ubfx            x4, x4, #0, #0x20
    // 0x15a2934: and             w3, w4, #0xff
    // 0x15a2938: ubfx            x3, x3, #0, #0x20
    // 0x15a293c: scvtf           d2, x3
    // 0x15a2940: fdiv            d3, d2, d1
    // 0x15a2944: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a2944: stur            d3, [x1, #0x17]
    // 0x15a2948: mov             x3, x6
    // 0x15a294c: ubfx            x3, x3, #0, #0x20
    // 0x15a2950: and             w4, w3, #0xff
    // 0x15a2954: ubfx            x4, x4, #0, #0x20
    // 0x15a2958: scvtf           d2, x4
    // 0x15a295c: fdiv            d3, d2, d1
    // 0x15a2960: StoreField: r1->field_1f = d3
    //     0x15a2960: stur            d3, [x1, #0x1f]
    // 0x15a2964: r0 = Color()
    //     0x15a2964: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a2968: mov             x1, x0
    // 0x15a296c: ldur            x0, [fp, #-0xe0]
    // 0x15a2970: cmp             w0, NULL
    // 0x15a2974: b.ne            #0x15a2980
    // 0x15a2978: r2 = Null
    //     0x15a2978: mov             x2, NULL
    // 0x15a297c: b               #0x15a29b8
    // 0x15a2980: LoadField: r2 = r0->field_57
    //     0x15a2980: ldur            w2, [x0, #0x57]
    // 0x15a2984: DecompressPointer r2
    //     0x15a2984: add             x2, x2, HEAP, lsl #32
    // 0x15a2988: cmp             w2, NULL
    // 0x15a298c: b.ne            #0x15a2998
    // 0x15a2990: r2 = Null
    //     0x15a2990: mov             x2, NULL
    // 0x15a2994: b               #0x15a29b8
    // 0x15a2998: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x15a2998: ldur            w3, [x2, #0x17]
    // 0x15a299c: DecompressPointer r3
    //     0x15a299c: add             x3, x3, HEAP, lsl #32
    // 0x15a29a0: cmp             w3, NULL
    // 0x15a29a4: b.ne            #0x15a29b0
    // 0x15a29a8: r2 = Null
    //     0x15a29a8: mov             x2, NULL
    // 0x15a29ac: b               #0x15a29b8
    // 0x15a29b0: LoadField: r2 = r3->field_7
    //     0x15a29b0: ldur            w2, [x3, #7]
    // 0x15a29b4: DecompressPointer r2
    //     0x15a29b4: add             x2, x2, HEAP, lsl #32
    // 0x15a29b8: cmp             w2, NULL
    // 0x15a29bc: b.ne            #0x15a29c8
    // 0x15a29c0: r2 = 0
    //     0x15a29c0: movz            x2, #0
    // 0x15a29c4: b               #0x15a29d8
    // 0x15a29c8: r3 = LoadInt32Instr(r2)
    //     0x15a29c8: sbfx            x3, x2, #1, #0x1f
    //     0x15a29cc: tbz             w2, #0, #0x15a29d4
    //     0x15a29d0: ldur            x3, [x2, #7]
    // 0x15a29d4: mov             x2, x3
    // 0x15a29d8: cmp             w0, NULL
    // 0x15a29dc: b.ne            #0x15a29e8
    // 0x15a29e0: r3 = Null
    //     0x15a29e0: mov             x3, NULL
    // 0x15a29e4: b               #0x15a2a20
    // 0x15a29e8: LoadField: r3 = r0->field_57
    //     0x15a29e8: ldur            w3, [x0, #0x57]
    // 0x15a29ec: DecompressPointer r3
    //     0x15a29ec: add             x3, x3, HEAP, lsl #32
    // 0x15a29f0: cmp             w3, NULL
    // 0x15a29f4: b.ne            #0x15a2a00
    // 0x15a29f8: r3 = Null
    //     0x15a29f8: mov             x3, NULL
    // 0x15a29fc: b               #0x15a2a20
    // 0x15a2a00: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a2a00: ldur            w4, [x3, #0x17]
    // 0x15a2a04: DecompressPointer r4
    //     0x15a2a04: add             x4, x4, HEAP, lsl #32
    // 0x15a2a08: cmp             w4, NULL
    // 0x15a2a0c: b.ne            #0x15a2a18
    // 0x15a2a10: r3 = Null
    //     0x15a2a10: mov             x3, NULL
    // 0x15a2a14: b               #0x15a2a20
    // 0x15a2a18: LoadField: r3 = r4->field_b
    //     0x15a2a18: ldur            w3, [x4, #0xb]
    // 0x15a2a1c: DecompressPointer r3
    //     0x15a2a1c: add             x3, x3, HEAP, lsl #32
    // 0x15a2a20: cmp             w3, NULL
    // 0x15a2a24: b.ne            #0x15a2a30
    // 0x15a2a28: r3 = 0
    //     0x15a2a28: movz            x3, #0
    // 0x15a2a2c: b               #0x15a2a40
    // 0x15a2a30: r4 = LoadInt32Instr(r3)
    //     0x15a2a30: sbfx            x4, x3, #1, #0x1f
    //     0x15a2a34: tbz             w3, #0, #0x15a2a3c
    //     0x15a2a38: ldur            x4, [x3, #7]
    // 0x15a2a3c: mov             x3, x4
    // 0x15a2a40: cmp             w0, NULL
    // 0x15a2a44: b.ne            #0x15a2a50
    // 0x15a2a48: r0 = Null
    //     0x15a2a48: mov             x0, NULL
    // 0x15a2a4c: b               #0x15a2a8c
    // 0x15a2a50: LoadField: r4 = r0->field_57
    //     0x15a2a50: ldur            w4, [x0, #0x57]
    // 0x15a2a54: DecompressPointer r4
    //     0x15a2a54: add             x4, x4, HEAP, lsl #32
    // 0x15a2a58: cmp             w4, NULL
    // 0x15a2a5c: b.ne            #0x15a2a68
    // 0x15a2a60: r0 = Null
    //     0x15a2a60: mov             x0, NULL
    // 0x15a2a64: b               #0x15a2a8c
    // 0x15a2a68: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x15a2a68: ldur            w0, [x4, #0x17]
    // 0x15a2a6c: DecompressPointer r0
    //     0x15a2a6c: add             x0, x0, HEAP, lsl #32
    // 0x15a2a70: cmp             w0, NULL
    // 0x15a2a74: b.ne            #0x15a2a80
    // 0x15a2a78: r0 = Null
    //     0x15a2a78: mov             x0, NULL
    // 0x15a2a7c: b               #0x15a2a8c
    // 0x15a2a80: LoadField: r4 = r0->field_f
    //     0x15a2a80: ldur            w4, [x0, #0xf]
    // 0x15a2a84: DecompressPointer r4
    //     0x15a2a84: add             x4, x4, HEAP, lsl #32
    // 0x15a2a88: mov             x0, x4
    // 0x15a2a8c: cmp             w0, NULL
    // 0x15a2a90: b.ne            #0x15a2a9c
    // 0x15a2a94: r5 = 0
    //     0x15a2a94: movz            x5, #0
    // 0x15a2a98: b               #0x15a2aac
    // 0x15a2a9c: r4 = LoadInt32Instr(r0)
    //     0x15a2a9c: sbfx            x4, x0, #1, #0x1f
    //     0x15a2aa0: tbz             w0, #0, #0x15a2aa8
    //     0x15a2aa4: ldur            x4, [x0, #7]
    // 0x15a2aa8: mov             x5, x4
    // 0x15a2aac: ldur            x0, [fp, #-0xc0]
    // 0x15a2ab0: r4 = Instance_ColorSpace
    //     0x15a2ab0: ldr             x4, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a2ab4: d1 = 255.000000
    //     0x15a2ab4: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a2ab8: d0 = 1.000000
    //     0x15a2ab8: fmov            d0, #1.00000000
    // 0x15a2abc: stur            x5, [fp, #-0xc8]
    // 0x15a2ac0: StoreField: r1->field_27 = r4
    //     0x15a2ac0: stur            w4, [x1, #0x27]
    // 0x15a2ac4: StoreField: r1->field_7 = d0
    //     0x15a2ac4: stur            d0, [x1, #7]
    // 0x15a2ac8: ubfx            x2, x2, #0, #0x20
    // 0x15a2acc: and             w6, w2, #0xff
    // 0x15a2ad0: ubfx            x6, x6, #0, #0x20
    // 0x15a2ad4: scvtf           d2, x6
    // 0x15a2ad8: fdiv            d3, d2, d1
    // 0x15a2adc: StoreField: r1->field_f = d3
    //     0x15a2adc: stur            d3, [x1, #0xf]
    // 0x15a2ae0: ubfx            x3, x3, #0, #0x20
    // 0x15a2ae4: and             w2, w3, #0xff
    // 0x15a2ae8: ubfx            x2, x2, #0, #0x20
    // 0x15a2aec: scvtf           d2, x2
    // 0x15a2af0: fdiv            d3, d2, d1
    // 0x15a2af4: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a2af4: stur            d3, [x1, #0x17]
    // 0x15a2af8: mov             x2, x5
    // 0x15a2afc: ubfx            x2, x2, #0, #0x20
    // 0x15a2b00: and             w3, w2, #0xff
    // 0x15a2b04: ubfx            x3, x3, #0, #0x20
    // 0x15a2b08: scvtf           d2, x3
    // 0x15a2b0c: fdiv            d3, d2, d1
    // 0x15a2b10: StoreField: r1->field_1f = d3
    //     0x15a2b10: stur            d3, [x1, #0x1f]
    // 0x15a2b14: ldur            x16, [fp, #-0xe8]
    // 0x15a2b18: stp             x1, x16, [SP]
    // 0x15a2b1c: ldur            x1, [fp, #-0xd8]
    // 0x15a2b20: r4 = const [0, 0x3, 0x2, 0x1, primary, 0x1, secondary, 0x2, null]
    //     0x15a2b20: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f38] List(9) [0, 0x3, 0x2, 0x1, "primary", 0x1, "secondary", 0x2, Null]
    //     0x15a2b24: ldr             x4, [x4, #0xf38]
    // 0x15a2b28: r0 = copyWith()
    //     0x15a2b28: bl              #0x6aebac  ; [package:flutter/src/material/color_scheme.dart] ColorScheme::copyWith
    // 0x15a2b2c: stur            x0, [fp, #-0xd8]
    // 0x15a2b30: r0 = Color()
    //     0x15a2b30: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a2b34: mov             x1, x0
    // 0x15a2b38: ldur            x0, [fp, #-0xc0]
    // 0x15a2b3c: stur            x1, [fp, #-0xe8]
    // 0x15a2b40: LoadField: r2 = r0->field_b
    //     0x15a2b40: ldur            w2, [x0, #0xb]
    // 0x15a2b44: DecompressPointer r2
    //     0x15a2b44: add             x2, x2, HEAP, lsl #32
    // 0x15a2b48: stur            x2, [fp, #-0xe0]
    // 0x15a2b4c: cmp             w2, NULL
    // 0x15a2b50: b.ne            #0x15a2b5c
    // 0x15a2b54: r3 = Null
    //     0x15a2b54: mov             x3, NULL
    // 0x15a2b58: b               #0x15a2b94
    // 0x15a2b5c: LoadField: r3 = r2->field_57
    //     0x15a2b5c: ldur            w3, [x2, #0x57]
    // 0x15a2b60: DecompressPointer r3
    //     0x15a2b60: add             x3, x3, HEAP, lsl #32
    // 0x15a2b64: cmp             w3, NULL
    // 0x15a2b68: b.ne            #0x15a2b74
    // 0x15a2b6c: r3 = Null
    //     0x15a2b6c: mov             x3, NULL
    // 0x15a2b70: b               #0x15a2b94
    // 0x15a2b74: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a2b74: ldur            w4, [x3, #0x17]
    // 0x15a2b78: DecompressPointer r4
    //     0x15a2b78: add             x4, x4, HEAP, lsl #32
    // 0x15a2b7c: cmp             w4, NULL
    // 0x15a2b80: b.ne            #0x15a2b8c
    // 0x15a2b84: r3 = Null
    //     0x15a2b84: mov             x3, NULL
    // 0x15a2b88: b               #0x15a2b94
    // 0x15a2b8c: LoadField: r3 = r4->field_7
    //     0x15a2b8c: ldur            w3, [x4, #7]
    // 0x15a2b90: DecompressPointer r3
    //     0x15a2b90: add             x3, x3, HEAP, lsl #32
    // 0x15a2b94: cmp             w3, NULL
    // 0x15a2b98: b.ne            #0x15a2ba4
    // 0x15a2b9c: r3 = 0
    //     0x15a2b9c: movz            x3, #0
    // 0x15a2ba0: b               #0x15a2bb4
    // 0x15a2ba4: r4 = LoadInt32Instr(r3)
    //     0x15a2ba4: sbfx            x4, x3, #1, #0x1f
    //     0x15a2ba8: tbz             w3, #0, #0x15a2bb0
    //     0x15a2bac: ldur            x4, [x3, #7]
    // 0x15a2bb0: mov             x3, x4
    // 0x15a2bb4: cmp             w2, NULL
    // 0x15a2bb8: b.ne            #0x15a2bc4
    // 0x15a2bbc: r4 = Null
    //     0x15a2bbc: mov             x4, NULL
    // 0x15a2bc0: b               #0x15a2bfc
    // 0x15a2bc4: LoadField: r4 = r2->field_57
    //     0x15a2bc4: ldur            w4, [x2, #0x57]
    // 0x15a2bc8: DecompressPointer r4
    //     0x15a2bc8: add             x4, x4, HEAP, lsl #32
    // 0x15a2bcc: cmp             w4, NULL
    // 0x15a2bd0: b.ne            #0x15a2bdc
    // 0x15a2bd4: r4 = Null
    //     0x15a2bd4: mov             x4, NULL
    // 0x15a2bd8: b               #0x15a2bfc
    // 0x15a2bdc: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a2bdc: ldur            w5, [x4, #0x17]
    // 0x15a2be0: DecompressPointer r5
    //     0x15a2be0: add             x5, x5, HEAP, lsl #32
    // 0x15a2be4: cmp             w5, NULL
    // 0x15a2be8: b.ne            #0x15a2bf4
    // 0x15a2bec: r4 = Null
    //     0x15a2bec: mov             x4, NULL
    // 0x15a2bf0: b               #0x15a2bfc
    // 0x15a2bf4: LoadField: r4 = r5->field_b
    //     0x15a2bf4: ldur            w4, [x5, #0xb]
    // 0x15a2bf8: DecompressPointer r4
    //     0x15a2bf8: add             x4, x4, HEAP, lsl #32
    // 0x15a2bfc: cmp             w4, NULL
    // 0x15a2c00: b.ne            #0x15a2c0c
    // 0x15a2c04: r4 = 0
    //     0x15a2c04: movz            x4, #0
    // 0x15a2c08: b               #0x15a2c1c
    // 0x15a2c0c: r5 = LoadInt32Instr(r4)
    //     0x15a2c0c: sbfx            x5, x4, #1, #0x1f
    //     0x15a2c10: tbz             w4, #0, #0x15a2c18
    //     0x15a2c14: ldur            x5, [x4, #7]
    // 0x15a2c18: mov             x4, x5
    // 0x15a2c1c: cmp             w2, NULL
    // 0x15a2c20: b.ne            #0x15a2c2c
    // 0x15a2c24: r5 = Null
    //     0x15a2c24: mov             x5, NULL
    // 0x15a2c28: b               #0x15a2c64
    // 0x15a2c2c: LoadField: r5 = r2->field_57
    //     0x15a2c2c: ldur            w5, [x2, #0x57]
    // 0x15a2c30: DecompressPointer r5
    //     0x15a2c30: add             x5, x5, HEAP, lsl #32
    // 0x15a2c34: cmp             w5, NULL
    // 0x15a2c38: b.ne            #0x15a2c44
    // 0x15a2c3c: r5 = Null
    //     0x15a2c3c: mov             x5, NULL
    // 0x15a2c40: b               #0x15a2c64
    // 0x15a2c44: ArrayLoad: r6 = r5[0]  ; List_4
    //     0x15a2c44: ldur            w6, [x5, #0x17]
    // 0x15a2c48: DecompressPointer r6
    //     0x15a2c48: add             x6, x6, HEAP, lsl #32
    // 0x15a2c4c: cmp             w6, NULL
    // 0x15a2c50: b.ne            #0x15a2c5c
    // 0x15a2c54: r5 = Null
    //     0x15a2c54: mov             x5, NULL
    // 0x15a2c58: b               #0x15a2c64
    // 0x15a2c5c: LoadField: r5 = r6->field_f
    //     0x15a2c5c: ldur            w5, [x6, #0xf]
    // 0x15a2c60: DecompressPointer r5
    //     0x15a2c60: add             x5, x5, HEAP, lsl #32
    // 0x15a2c64: cmp             w5, NULL
    // 0x15a2c68: b.ne            #0x15a2c74
    // 0x15a2c6c: r6 = 0
    //     0x15a2c6c: movz            x6, #0
    // 0x15a2c70: b               #0x15a2c80
    // 0x15a2c74: r6 = LoadInt32Instr(r5)
    //     0x15a2c74: sbfx            x6, x5, #1, #0x1f
    //     0x15a2c78: tbz             w5, #0, #0x15a2c80
    //     0x15a2c7c: ldur            x6, [x5, #7]
    // 0x15a2c80: r5 = Instance_ColorSpace
    //     0x15a2c80: ldr             x5, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a2c84: d1 = 255.000000
    //     0x15a2c84: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a2c88: d0 = 1.000000
    //     0x15a2c88: fmov            d0, #1.00000000
    // 0x15a2c8c: stur            x6, [fp, #-0xc8]
    // 0x15a2c90: StoreField: r1->field_27 = r5
    //     0x15a2c90: stur            w5, [x1, #0x27]
    // 0x15a2c94: StoreField: r1->field_7 = d0
    //     0x15a2c94: stur            d0, [x1, #7]
    // 0x15a2c98: ubfx            x3, x3, #0, #0x20
    // 0x15a2c9c: and             w7, w3, #0xff
    // 0x15a2ca0: ubfx            x7, x7, #0, #0x20
    // 0x15a2ca4: scvtf           d2, x7
    // 0x15a2ca8: fdiv            d3, d2, d1
    // 0x15a2cac: StoreField: r1->field_f = d3
    //     0x15a2cac: stur            d3, [x1, #0xf]
    // 0x15a2cb0: ubfx            x4, x4, #0, #0x20
    // 0x15a2cb4: and             w3, w4, #0xff
    // 0x15a2cb8: ubfx            x3, x3, #0, #0x20
    // 0x15a2cbc: scvtf           d2, x3
    // 0x15a2cc0: fdiv            d3, d2, d1
    // 0x15a2cc4: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a2cc4: stur            d3, [x1, #0x17]
    // 0x15a2cc8: mov             x3, x6
    // 0x15a2ccc: ubfx            x3, x3, #0, #0x20
    // 0x15a2cd0: and             w4, w3, #0xff
    // 0x15a2cd4: ubfx            x4, x4, #0, #0x20
    // 0x15a2cd8: scvtf           d2, x4
    // 0x15a2cdc: fdiv            d3, d2, d1
    // 0x15a2ce0: StoreField: r1->field_1f = d3
    //     0x15a2ce0: stur            d3, [x1, #0x1f]
    // 0x15a2ce4: r0 = IconThemeData()
    //     0x15a2ce4: bl              #0x6b2d30  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0x15a2ce8: mov             x1, x0
    // 0x15a2cec: ldur            x0, [fp, #-0xe8]
    // 0x15a2cf0: stur            x1, [fp, #-0xf0]
    // 0x15a2cf4: StoreField: r1->field_1b = r0
    //     0x15a2cf4: stur            w0, [x1, #0x1b]
    // 0x15a2cf8: r0 = Color()
    //     0x15a2cf8: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a2cfc: mov             x1, x0
    // 0x15a2d00: ldur            x0, [fp, #-0xe0]
    // 0x15a2d04: cmp             w0, NULL
    // 0x15a2d08: b.ne            #0x15a2d14
    // 0x15a2d0c: r2 = Null
    //     0x15a2d0c: mov             x2, NULL
    // 0x15a2d10: b               #0x15a2d4c
    // 0x15a2d14: LoadField: r2 = r0->field_57
    //     0x15a2d14: ldur            w2, [x0, #0x57]
    // 0x15a2d18: DecompressPointer r2
    //     0x15a2d18: add             x2, x2, HEAP, lsl #32
    // 0x15a2d1c: cmp             w2, NULL
    // 0x15a2d20: b.ne            #0x15a2d2c
    // 0x15a2d24: r2 = Null
    //     0x15a2d24: mov             x2, NULL
    // 0x15a2d28: b               #0x15a2d4c
    // 0x15a2d2c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x15a2d2c: ldur            w3, [x2, #0x17]
    // 0x15a2d30: DecompressPointer r3
    //     0x15a2d30: add             x3, x3, HEAP, lsl #32
    // 0x15a2d34: cmp             w3, NULL
    // 0x15a2d38: b.ne            #0x15a2d44
    // 0x15a2d3c: r2 = Null
    //     0x15a2d3c: mov             x2, NULL
    // 0x15a2d40: b               #0x15a2d4c
    // 0x15a2d44: LoadField: r2 = r3->field_7
    //     0x15a2d44: ldur            w2, [x3, #7]
    // 0x15a2d48: DecompressPointer r2
    //     0x15a2d48: add             x2, x2, HEAP, lsl #32
    // 0x15a2d4c: cmp             w2, NULL
    // 0x15a2d50: b.ne            #0x15a2d5c
    // 0x15a2d54: r2 = 0
    //     0x15a2d54: movz            x2, #0
    // 0x15a2d58: b               #0x15a2d6c
    // 0x15a2d5c: r3 = LoadInt32Instr(r2)
    //     0x15a2d5c: sbfx            x3, x2, #1, #0x1f
    //     0x15a2d60: tbz             w2, #0, #0x15a2d68
    //     0x15a2d64: ldur            x3, [x2, #7]
    // 0x15a2d68: mov             x2, x3
    // 0x15a2d6c: cmp             w0, NULL
    // 0x15a2d70: b.ne            #0x15a2d7c
    // 0x15a2d74: r3 = Null
    //     0x15a2d74: mov             x3, NULL
    // 0x15a2d78: b               #0x15a2db4
    // 0x15a2d7c: LoadField: r3 = r0->field_57
    //     0x15a2d7c: ldur            w3, [x0, #0x57]
    // 0x15a2d80: DecompressPointer r3
    //     0x15a2d80: add             x3, x3, HEAP, lsl #32
    // 0x15a2d84: cmp             w3, NULL
    // 0x15a2d88: b.ne            #0x15a2d94
    // 0x15a2d8c: r3 = Null
    //     0x15a2d8c: mov             x3, NULL
    // 0x15a2d90: b               #0x15a2db4
    // 0x15a2d94: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a2d94: ldur            w4, [x3, #0x17]
    // 0x15a2d98: DecompressPointer r4
    //     0x15a2d98: add             x4, x4, HEAP, lsl #32
    // 0x15a2d9c: cmp             w4, NULL
    // 0x15a2da0: b.ne            #0x15a2dac
    // 0x15a2da4: r3 = Null
    //     0x15a2da4: mov             x3, NULL
    // 0x15a2da8: b               #0x15a2db4
    // 0x15a2dac: LoadField: r3 = r4->field_b
    //     0x15a2dac: ldur            w3, [x4, #0xb]
    // 0x15a2db0: DecompressPointer r3
    //     0x15a2db0: add             x3, x3, HEAP, lsl #32
    // 0x15a2db4: cmp             w3, NULL
    // 0x15a2db8: b.ne            #0x15a2dc4
    // 0x15a2dbc: r3 = 0
    //     0x15a2dbc: movz            x3, #0
    // 0x15a2dc0: b               #0x15a2dd4
    // 0x15a2dc4: r4 = LoadInt32Instr(r3)
    //     0x15a2dc4: sbfx            x4, x3, #1, #0x1f
    //     0x15a2dc8: tbz             w3, #0, #0x15a2dd0
    //     0x15a2dcc: ldur            x4, [x3, #7]
    // 0x15a2dd0: mov             x3, x4
    // 0x15a2dd4: cmp             w0, NULL
    // 0x15a2dd8: b.ne            #0x15a2de4
    // 0x15a2ddc: r0 = Null
    //     0x15a2ddc: mov             x0, NULL
    // 0x15a2de0: b               #0x15a2e20
    // 0x15a2de4: LoadField: r4 = r0->field_57
    //     0x15a2de4: ldur            w4, [x0, #0x57]
    // 0x15a2de8: DecompressPointer r4
    //     0x15a2de8: add             x4, x4, HEAP, lsl #32
    // 0x15a2dec: cmp             w4, NULL
    // 0x15a2df0: b.ne            #0x15a2dfc
    // 0x15a2df4: r0 = Null
    //     0x15a2df4: mov             x0, NULL
    // 0x15a2df8: b               #0x15a2e20
    // 0x15a2dfc: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x15a2dfc: ldur            w0, [x4, #0x17]
    // 0x15a2e00: DecompressPointer r0
    //     0x15a2e00: add             x0, x0, HEAP, lsl #32
    // 0x15a2e04: cmp             w0, NULL
    // 0x15a2e08: b.ne            #0x15a2e14
    // 0x15a2e0c: r0 = Null
    //     0x15a2e0c: mov             x0, NULL
    // 0x15a2e10: b               #0x15a2e20
    // 0x15a2e14: LoadField: r4 = r0->field_f
    //     0x15a2e14: ldur            w4, [x0, #0xf]
    // 0x15a2e18: DecompressPointer r4
    //     0x15a2e18: add             x4, x4, HEAP, lsl #32
    // 0x15a2e1c: mov             x0, x4
    // 0x15a2e20: cmp             w0, NULL
    // 0x15a2e24: b.ne            #0x15a2e30
    // 0x15a2e28: r5 = 0
    //     0x15a2e28: movz            x5, #0
    // 0x15a2e2c: b               #0x15a2e40
    // 0x15a2e30: r4 = LoadInt32Instr(r0)
    //     0x15a2e30: sbfx            x4, x0, #1, #0x1f
    //     0x15a2e34: tbz             w0, #0, #0x15a2e3c
    //     0x15a2e38: ldur            x4, [x0, #7]
    // 0x15a2e3c: mov             x5, x4
    // 0x15a2e40: ldur            x0, [fp, #-0xc0]
    // 0x15a2e44: r4 = Instance_ColorSpace
    //     0x15a2e44: ldr             x4, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a2e48: d1 = 255.000000
    //     0x15a2e48: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a2e4c: d0 = 1.000000
    //     0x15a2e4c: fmov            d0, #1.00000000
    // 0x15a2e50: stur            x5, [fp, #-0xc8]
    // 0x15a2e54: StoreField: r1->field_27 = r4
    //     0x15a2e54: stur            w4, [x1, #0x27]
    // 0x15a2e58: StoreField: r1->field_7 = d0
    //     0x15a2e58: stur            d0, [x1, #7]
    // 0x15a2e5c: ubfx            x2, x2, #0, #0x20
    // 0x15a2e60: and             w6, w2, #0xff
    // 0x15a2e64: ubfx            x6, x6, #0, #0x20
    // 0x15a2e68: scvtf           d2, x6
    // 0x15a2e6c: fdiv            d3, d2, d1
    // 0x15a2e70: StoreField: r1->field_f = d3
    //     0x15a2e70: stur            d3, [x1, #0xf]
    // 0x15a2e74: ubfx            x3, x3, #0, #0x20
    // 0x15a2e78: and             w2, w3, #0xff
    // 0x15a2e7c: ubfx            x2, x2, #0, #0x20
    // 0x15a2e80: scvtf           d2, x2
    // 0x15a2e84: fdiv            d3, d2, d1
    // 0x15a2e88: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a2e88: stur            d3, [x1, #0x17]
    // 0x15a2e8c: mov             x2, x5
    // 0x15a2e90: ubfx            x2, x2, #0, #0x20
    // 0x15a2e94: and             w3, w2, #0xff
    // 0x15a2e98: ubfx            x3, x3, #0, #0x20
    // 0x15a2e9c: scvtf           d2, x3
    // 0x15a2ea0: fdiv            d3, d2, d1
    // 0x15a2ea4: StoreField: r1->field_1f = d3
    //     0x15a2ea4: stur            d3, [x1, #0x1f]
    // 0x15a2ea8: str             x1, [SP]
    // 0x15a2eac: ldur            x1, [fp, #-0xf0]
    // 0x15a2eb0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15a2eb0: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15a2eb4: ldr             x4, [x4, #0xf40]
    // 0x15a2eb8: r0 = copyWith()
    //     0x15a2eb8: bl              #0x16abf9c  ; [package:flutter/src/widgets/icon_theme_data.dart] IconThemeData::copyWith
    // 0x15a2ebc: stur            x0, [fp, #-0xe0]
    // 0x15a2ec0: r0 = Color()
    //     0x15a2ec0: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a2ec4: mov             x1, x0
    // 0x15a2ec8: ldur            x0, [fp, #-0xc0]
    // 0x15a2ecc: stur            x1, [fp, #-0xf0]
    // 0x15a2ed0: LoadField: r2 = r0->field_b
    //     0x15a2ed0: ldur            w2, [x0, #0xb]
    // 0x15a2ed4: DecompressPointer r2
    //     0x15a2ed4: add             x2, x2, HEAP, lsl #32
    // 0x15a2ed8: stur            x2, [fp, #-0xe8]
    // 0x15a2edc: cmp             w2, NULL
    // 0x15a2ee0: b.ne            #0x15a2eec
    // 0x15a2ee4: r3 = Null
    //     0x15a2ee4: mov             x3, NULL
    // 0x15a2ee8: b               #0x15a2f24
    // 0x15a2eec: LoadField: r3 = r2->field_57
    //     0x15a2eec: ldur            w3, [x2, #0x57]
    // 0x15a2ef0: DecompressPointer r3
    //     0x15a2ef0: add             x3, x3, HEAP, lsl #32
    // 0x15a2ef4: cmp             w3, NULL
    // 0x15a2ef8: b.ne            #0x15a2f04
    // 0x15a2efc: r3 = Null
    //     0x15a2efc: mov             x3, NULL
    // 0x15a2f00: b               #0x15a2f24
    // 0x15a2f04: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a2f04: ldur            w4, [x3, #0x17]
    // 0x15a2f08: DecompressPointer r4
    //     0x15a2f08: add             x4, x4, HEAP, lsl #32
    // 0x15a2f0c: cmp             w4, NULL
    // 0x15a2f10: b.ne            #0x15a2f1c
    // 0x15a2f14: r3 = Null
    //     0x15a2f14: mov             x3, NULL
    // 0x15a2f18: b               #0x15a2f24
    // 0x15a2f1c: LoadField: r3 = r4->field_7
    //     0x15a2f1c: ldur            w3, [x4, #7]
    // 0x15a2f20: DecompressPointer r3
    //     0x15a2f20: add             x3, x3, HEAP, lsl #32
    // 0x15a2f24: cmp             w3, NULL
    // 0x15a2f28: b.ne            #0x15a2f34
    // 0x15a2f2c: r3 = 0
    //     0x15a2f2c: movz            x3, #0
    // 0x15a2f30: b               #0x15a2f44
    // 0x15a2f34: r4 = LoadInt32Instr(r3)
    //     0x15a2f34: sbfx            x4, x3, #1, #0x1f
    //     0x15a2f38: tbz             w3, #0, #0x15a2f40
    //     0x15a2f3c: ldur            x4, [x3, #7]
    // 0x15a2f40: mov             x3, x4
    // 0x15a2f44: cmp             w2, NULL
    // 0x15a2f48: b.ne            #0x15a2f54
    // 0x15a2f4c: r4 = Null
    //     0x15a2f4c: mov             x4, NULL
    // 0x15a2f50: b               #0x15a2f8c
    // 0x15a2f54: LoadField: r4 = r2->field_57
    //     0x15a2f54: ldur            w4, [x2, #0x57]
    // 0x15a2f58: DecompressPointer r4
    //     0x15a2f58: add             x4, x4, HEAP, lsl #32
    // 0x15a2f5c: cmp             w4, NULL
    // 0x15a2f60: b.ne            #0x15a2f6c
    // 0x15a2f64: r4 = Null
    //     0x15a2f64: mov             x4, NULL
    // 0x15a2f68: b               #0x15a2f8c
    // 0x15a2f6c: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a2f6c: ldur            w5, [x4, #0x17]
    // 0x15a2f70: DecompressPointer r5
    //     0x15a2f70: add             x5, x5, HEAP, lsl #32
    // 0x15a2f74: cmp             w5, NULL
    // 0x15a2f78: b.ne            #0x15a2f84
    // 0x15a2f7c: r4 = Null
    //     0x15a2f7c: mov             x4, NULL
    // 0x15a2f80: b               #0x15a2f8c
    // 0x15a2f84: LoadField: r4 = r5->field_b
    //     0x15a2f84: ldur            w4, [x5, #0xb]
    // 0x15a2f88: DecompressPointer r4
    //     0x15a2f88: add             x4, x4, HEAP, lsl #32
    // 0x15a2f8c: cmp             w4, NULL
    // 0x15a2f90: b.ne            #0x15a2f9c
    // 0x15a2f94: r4 = 0
    //     0x15a2f94: movz            x4, #0
    // 0x15a2f98: b               #0x15a2fac
    // 0x15a2f9c: r5 = LoadInt32Instr(r4)
    //     0x15a2f9c: sbfx            x5, x4, #1, #0x1f
    //     0x15a2fa0: tbz             w4, #0, #0x15a2fa8
    //     0x15a2fa4: ldur            x5, [x4, #7]
    // 0x15a2fa8: mov             x4, x5
    // 0x15a2fac: cmp             w2, NULL
    // 0x15a2fb0: b.ne            #0x15a2fbc
    // 0x15a2fb4: r5 = Null
    //     0x15a2fb4: mov             x5, NULL
    // 0x15a2fb8: b               #0x15a2ff4
    // 0x15a2fbc: LoadField: r5 = r2->field_57
    //     0x15a2fbc: ldur            w5, [x2, #0x57]
    // 0x15a2fc0: DecompressPointer r5
    //     0x15a2fc0: add             x5, x5, HEAP, lsl #32
    // 0x15a2fc4: cmp             w5, NULL
    // 0x15a2fc8: b.ne            #0x15a2fd4
    // 0x15a2fcc: r5 = Null
    //     0x15a2fcc: mov             x5, NULL
    // 0x15a2fd0: b               #0x15a2ff4
    // 0x15a2fd4: ArrayLoad: r6 = r5[0]  ; List_4
    //     0x15a2fd4: ldur            w6, [x5, #0x17]
    // 0x15a2fd8: DecompressPointer r6
    //     0x15a2fd8: add             x6, x6, HEAP, lsl #32
    // 0x15a2fdc: cmp             w6, NULL
    // 0x15a2fe0: b.ne            #0x15a2fec
    // 0x15a2fe4: r5 = Null
    //     0x15a2fe4: mov             x5, NULL
    // 0x15a2fe8: b               #0x15a2ff4
    // 0x15a2fec: LoadField: r5 = r6->field_f
    //     0x15a2fec: ldur            w5, [x6, #0xf]
    // 0x15a2ff0: DecompressPointer r5
    //     0x15a2ff0: add             x5, x5, HEAP, lsl #32
    // 0x15a2ff4: cmp             w5, NULL
    // 0x15a2ff8: b.ne            #0x15a3004
    // 0x15a2ffc: r6 = 0
    //     0x15a2ffc: movz            x6, #0
    // 0x15a3000: b               #0x15a3010
    // 0x15a3004: r6 = LoadInt32Instr(r5)
    //     0x15a3004: sbfx            x6, x5, #1, #0x1f
    //     0x15a3008: tbz             w5, #0, #0x15a3010
    //     0x15a300c: ldur            x6, [x5, #7]
    // 0x15a3010: r5 = Instance_ColorSpace
    //     0x15a3010: ldr             x5, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a3014: d0 = 255.000000
    //     0x15a3014: ldr             d0, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a3018: stur            x6, [fp, #-0xc8]
    // 0x15a301c: StoreField: r1->field_27 = r5
    //     0x15a301c: stur            w5, [x1, #0x27]
    // 0x15a3020: StoreField: r1->field_7 = rZR
    //     0x15a3020: stur            xzr, [x1, #7]
    // 0x15a3024: ubfx            x3, x3, #0, #0x20
    // 0x15a3028: and             w7, w3, #0xff
    // 0x15a302c: ubfx            x7, x7, #0, #0x20
    // 0x15a3030: scvtf           d1, x7
    // 0x15a3034: fdiv            d2, d1, d0
    // 0x15a3038: StoreField: r1->field_f = d2
    //     0x15a3038: stur            d2, [x1, #0xf]
    // 0x15a303c: ubfx            x4, x4, #0, #0x20
    // 0x15a3040: and             w3, w4, #0xff
    // 0x15a3044: ubfx            x3, x3, #0, #0x20
    // 0x15a3048: scvtf           d1, x3
    // 0x15a304c: fdiv            d2, d1, d0
    // 0x15a3050: ArrayStore: r1[0] = d2  ; List_8
    //     0x15a3050: stur            d2, [x1, #0x17]
    // 0x15a3054: mov             x3, x6
    // 0x15a3058: ubfx            x3, x3, #0, #0x20
    // 0x15a305c: and             w4, w3, #0xff
    // 0x15a3060: ubfx            x4, x4, #0, #0x20
    // 0x15a3064: scvtf           d1, x4
    // 0x15a3068: fdiv            d2, d1, d0
    // 0x15a306c: StoreField: r1->field_1f = d2
    //     0x15a306c: stur            d2, [x1, #0x1f]
    // 0x15a3070: r0 = IconThemeData()
    //     0x15a3070: bl              #0x6b2d30  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0x15a3074: mov             x1, x0
    // 0x15a3078: ldur            x0, [fp, #-0xf0]
    // 0x15a307c: stur            x1, [fp, #-0xf8]
    // 0x15a3080: StoreField: r1->field_1b = r0
    //     0x15a3080: stur            w0, [x1, #0x1b]
    // 0x15a3084: r0 = Color()
    //     0x15a3084: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a3088: mov             x1, x0
    // 0x15a308c: ldur            x0, [fp, #-0xe8]
    // 0x15a3090: stur            x1, [fp, #-0xf0]
    // 0x15a3094: cmp             w0, NULL
    // 0x15a3098: b.ne            #0x15a30a4
    // 0x15a309c: r2 = Null
    //     0x15a309c: mov             x2, NULL
    // 0x15a30a0: b               #0x15a30dc
    // 0x15a30a4: LoadField: r2 = r0->field_57
    //     0x15a30a4: ldur            w2, [x0, #0x57]
    // 0x15a30a8: DecompressPointer r2
    //     0x15a30a8: add             x2, x2, HEAP, lsl #32
    // 0x15a30ac: cmp             w2, NULL
    // 0x15a30b0: b.ne            #0x15a30bc
    // 0x15a30b4: r2 = Null
    //     0x15a30b4: mov             x2, NULL
    // 0x15a30b8: b               #0x15a30dc
    // 0x15a30bc: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x15a30bc: ldur            w3, [x2, #0x17]
    // 0x15a30c0: DecompressPointer r3
    //     0x15a30c0: add             x3, x3, HEAP, lsl #32
    // 0x15a30c4: cmp             w3, NULL
    // 0x15a30c8: b.ne            #0x15a30d4
    // 0x15a30cc: r2 = Null
    //     0x15a30cc: mov             x2, NULL
    // 0x15a30d0: b               #0x15a30dc
    // 0x15a30d4: LoadField: r2 = r3->field_7
    //     0x15a30d4: ldur            w2, [x3, #7]
    // 0x15a30d8: DecompressPointer r2
    //     0x15a30d8: add             x2, x2, HEAP, lsl #32
    // 0x15a30dc: cmp             w2, NULL
    // 0x15a30e0: b.ne            #0x15a30ec
    // 0x15a30e4: r2 = 0
    //     0x15a30e4: movz            x2, #0
    // 0x15a30e8: b               #0x15a30fc
    // 0x15a30ec: r3 = LoadInt32Instr(r2)
    //     0x15a30ec: sbfx            x3, x2, #1, #0x1f
    //     0x15a30f0: tbz             w2, #0, #0x15a30f8
    //     0x15a30f4: ldur            x3, [x2, #7]
    // 0x15a30f8: mov             x2, x3
    // 0x15a30fc: cmp             w0, NULL
    // 0x15a3100: b.ne            #0x15a310c
    // 0x15a3104: r3 = Null
    //     0x15a3104: mov             x3, NULL
    // 0x15a3108: b               #0x15a3144
    // 0x15a310c: LoadField: r3 = r0->field_57
    //     0x15a310c: ldur            w3, [x0, #0x57]
    // 0x15a3110: DecompressPointer r3
    //     0x15a3110: add             x3, x3, HEAP, lsl #32
    // 0x15a3114: cmp             w3, NULL
    // 0x15a3118: b.ne            #0x15a3124
    // 0x15a311c: r3 = Null
    //     0x15a311c: mov             x3, NULL
    // 0x15a3120: b               #0x15a3144
    // 0x15a3124: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a3124: ldur            w4, [x3, #0x17]
    // 0x15a3128: DecompressPointer r4
    //     0x15a3128: add             x4, x4, HEAP, lsl #32
    // 0x15a312c: cmp             w4, NULL
    // 0x15a3130: b.ne            #0x15a313c
    // 0x15a3134: r3 = Null
    //     0x15a3134: mov             x3, NULL
    // 0x15a3138: b               #0x15a3144
    // 0x15a313c: LoadField: r3 = r4->field_b
    //     0x15a313c: ldur            w3, [x4, #0xb]
    // 0x15a3140: DecompressPointer r3
    //     0x15a3140: add             x3, x3, HEAP, lsl #32
    // 0x15a3144: cmp             w3, NULL
    // 0x15a3148: b.ne            #0x15a3154
    // 0x15a314c: r3 = 0
    //     0x15a314c: movz            x3, #0
    // 0x15a3150: b               #0x15a3164
    // 0x15a3154: r4 = LoadInt32Instr(r3)
    //     0x15a3154: sbfx            x4, x3, #1, #0x1f
    //     0x15a3158: tbz             w3, #0, #0x15a3160
    //     0x15a315c: ldur            x4, [x3, #7]
    // 0x15a3160: mov             x3, x4
    // 0x15a3164: cmp             w0, NULL
    // 0x15a3168: b.ne            #0x15a3174
    // 0x15a316c: r4 = Null
    //     0x15a316c: mov             x4, NULL
    // 0x15a3170: b               #0x15a31ac
    // 0x15a3174: LoadField: r4 = r0->field_57
    //     0x15a3174: ldur            w4, [x0, #0x57]
    // 0x15a3178: DecompressPointer r4
    //     0x15a3178: add             x4, x4, HEAP, lsl #32
    // 0x15a317c: cmp             w4, NULL
    // 0x15a3180: b.ne            #0x15a318c
    // 0x15a3184: r4 = Null
    //     0x15a3184: mov             x4, NULL
    // 0x15a3188: b               #0x15a31ac
    // 0x15a318c: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a318c: ldur            w5, [x4, #0x17]
    // 0x15a3190: DecompressPointer r5
    //     0x15a3190: add             x5, x5, HEAP, lsl #32
    // 0x15a3194: cmp             w5, NULL
    // 0x15a3198: b.ne            #0x15a31a4
    // 0x15a319c: r4 = Null
    //     0x15a319c: mov             x4, NULL
    // 0x15a31a0: b               #0x15a31ac
    // 0x15a31a4: LoadField: r4 = r5->field_f
    //     0x15a31a4: ldur            w4, [x5, #0xf]
    // 0x15a31a8: DecompressPointer r4
    //     0x15a31a8: add             x4, x4, HEAP, lsl #32
    // 0x15a31ac: cmp             w4, NULL
    // 0x15a31b0: b.ne            #0x15a31bc
    // 0x15a31b4: r5 = 0
    //     0x15a31b4: movz            x5, #0
    // 0x15a31b8: b               #0x15a31c8
    // 0x15a31bc: r5 = LoadInt32Instr(r4)
    //     0x15a31bc: sbfx            x5, x4, #1, #0x1f
    //     0x15a31c0: tbz             w4, #0, #0x15a31c8
    //     0x15a31c4: ldur            x5, [x4, #7]
    // 0x15a31c8: r4 = Instance_ColorSpace
    //     0x15a31c8: ldr             x4, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a31cc: d0 = 255.000000
    //     0x15a31cc: ldr             d0, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a31d0: d1 = 1.000000
    //     0x15a31d0: fmov            d1, #1.00000000
    // 0x15a31d4: stur            x5, [fp, #-0xc8]
    // 0x15a31d8: StoreField: r1->field_27 = r4
    //     0x15a31d8: stur            w4, [x1, #0x27]
    // 0x15a31dc: StoreField: r1->field_7 = d1
    //     0x15a31dc: stur            d1, [x1, #7]
    // 0x15a31e0: ubfx            x2, x2, #0, #0x20
    // 0x15a31e4: and             w6, w2, #0xff
    // 0x15a31e8: ubfx            x6, x6, #0, #0x20
    // 0x15a31ec: scvtf           d2, x6
    // 0x15a31f0: fdiv            d3, d2, d0
    // 0x15a31f4: StoreField: r1->field_f = d3
    //     0x15a31f4: stur            d3, [x1, #0xf]
    // 0x15a31f8: ubfx            x3, x3, #0, #0x20
    // 0x15a31fc: and             w2, w3, #0xff
    // 0x15a3200: ubfx            x2, x2, #0, #0x20
    // 0x15a3204: scvtf           d2, x2
    // 0x15a3208: fdiv            d3, d2, d0
    // 0x15a320c: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a320c: stur            d3, [x1, #0x17]
    // 0x15a3210: mov             x2, x5
    // 0x15a3214: ubfx            x2, x2, #0, #0x20
    // 0x15a3218: and             w3, w2, #0xff
    // 0x15a321c: ubfx            x3, x3, #0, #0x20
    // 0x15a3220: scvtf           d2, x3
    // 0x15a3224: fdiv            d3, d2, d0
    // 0x15a3228: StoreField: r1->field_1f = d3
    //     0x15a3228: stur            d3, [x1, #0x1f]
    // 0x15a322c: r0 = Color()
    //     0x15a322c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a3230: mov             x2, x0
    // 0x15a3234: ldur            x0, [fp, #-0xe8]
    // 0x15a3238: stur            x2, [fp, #-0x100]
    // 0x15a323c: cmp             w0, NULL
    // 0x15a3240: b.ne            #0x15a324c
    // 0x15a3244: r1 = Null
    //     0x15a3244: mov             x1, NULL
    // 0x15a3248: b               #0x15a3284
    // 0x15a324c: LoadField: r1 = r0->field_57
    //     0x15a324c: ldur            w1, [x0, #0x57]
    // 0x15a3250: DecompressPointer r1
    //     0x15a3250: add             x1, x1, HEAP, lsl #32
    // 0x15a3254: cmp             w1, NULL
    // 0x15a3258: b.ne            #0x15a3264
    // 0x15a325c: r1 = Null
    //     0x15a325c: mov             x1, NULL
    // 0x15a3260: b               #0x15a3284
    // 0x15a3264: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x15a3264: ldur            w3, [x1, #0x17]
    // 0x15a3268: DecompressPointer r3
    //     0x15a3268: add             x3, x3, HEAP, lsl #32
    // 0x15a326c: cmp             w3, NULL
    // 0x15a3270: b.ne            #0x15a327c
    // 0x15a3274: r1 = Null
    //     0x15a3274: mov             x1, NULL
    // 0x15a3278: b               #0x15a3284
    // 0x15a327c: LoadField: r1 = r3->field_7
    //     0x15a327c: ldur            w1, [x3, #7]
    // 0x15a3280: DecompressPointer r1
    //     0x15a3280: add             x1, x1, HEAP, lsl #32
    // 0x15a3284: cmp             w1, NULL
    // 0x15a3288: b.ne            #0x15a3294
    // 0x15a328c: r1 = 0
    //     0x15a328c: movz            x1, #0
    // 0x15a3290: b               #0x15a32a4
    // 0x15a3294: r3 = LoadInt32Instr(r1)
    //     0x15a3294: sbfx            x3, x1, #1, #0x1f
    //     0x15a3298: tbz             w1, #0, #0x15a32a0
    //     0x15a329c: ldur            x3, [x1, #7]
    // 0x15a32a0: mov             x1, x3
    // 0x15a32a4: cmp             w0, NULL
    // 0x15a32a8: b.ne            #0x15a32b4
    // 0x15a32ac: r3 = Null
    //     0x15a32ac: mov             x3, NULL
    // 0x15a32b0: b               #0x15a32ec
    // 0x15a32b4: LoadField: r3 = r0->field_57
    //     0x15a32b4: ldur            w3, [x0, #0x57]
    // 0x15a32b8: DecompressPointer r3
    //     0x15a32b8: add             x3, x3, HEAP, lsl #32
    // 0x15a32bc: cmp             w3, NULL
    // 0x15a32c0: b.ne            #0x15a32cc
    // 0x15a32c4: r3 = Null
    //     0x15a32c4: mov             x3, NULL
    // 0x15a32c8: b               #0x15a32ec
    // 0x15a32cc: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a32cc: ldur            w4, [x3, #0x17]
    // 0x15a32d0: DecompressPointer r4
    //     0x15a32d0: add             x4, x4, HEAP, lsl #32
    // 0x15a32d4: cmp             w4, NULL
    // 0x15a32d8: b.ne            #0x15a32e4
    // 0x15a32dc: r3 = Null
    //     0x15a32dc: mov             x3, NULL
    // 0x15a32e0: b               #0x15a32ec
    // 0x15a32e4: LoadField: r3 = r4->field_b
    //     0x15a32e4: ldur            w3, [x4, #0xb]
    // 0x15a32e8: DecompressPointer r3
    //     0x15a32e8: add             x3, x3, HEAP, lsl #32
    // 0x15a32ec: cmp             w3, NULL
    // 0x15a32f0: b.ne            #0x15a32fc
    // 0x15a32f4: r3 = 0
    //     0x15a32f4: movz            x3, #0
    // 0x15a32f8: b               #0x15a330c
    // 0x15a32fc: r4 = LoadInt32Instr(r3)
    //     0x15a32fc: sbfx            x4, x3, #1, #0x1f
    //     0x15a3300: tbz             w3, #0, #0x15a3308
    //     0x15a3304: ldur            x4, [x3, #7]
    // 0x15a3308: mov             x3, x4
    // 0x15a330c: cmp             w0, NULL
    // 0x15a3310: b.ne            #0x15a331c
    // 0x15a3314: r4 = Null
    //     0x15a3314: mov             x4, NULL
    // 0x15a3318: b               #0x15a3354
    // 0x15a331c: LoadField: r4 = r0->field_57
    //     0x15a331c: ldur            w4, [x0, #0x57]
    // 0x15a3320: DecompressPointer r4
    //     0x15a3320: add             x4, x4, HEAP, lsl #32
    // 0x15a3324: cmp             w4, NULL
    // 0x15a3328: b.ne            #0x15a3334
    // 0x15a332c: r4 = Null
    //     0x15a332c: mov             x4, NULL
    // 0x15a3330: b               #0x15a3354
    // 0x15a3334: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a3334: ldur            w5, [x4, #0x17]
    // 0x15a3338: DecompressPointer r5
    //     0x15a3338: add             x5, x5, HEAP, lsl #32
    // 0x15a333c: cmp             w5, NULL
    // 0x15a3340: b.ne            #0x15a334c
    // 0x15a3344: r4 = Null
    //     0x15a3344: mov             x4, NULL
    // 0x15a3348: b               #0x15a3354
    // 0x15a334c: LoadField: r4 = r5->field_f
    //     0x15a334c: ldur            w4, [x5, #0xf]
    // 0x15a3350: DecompressPointer r4
    //     0x15a3350: add             x4, x4, HEAP, lsl #32
    // 0x15a3354: cmp             w4, NULL
    // 0x15a3358: b.ne            #0x15a3364
    // 0x15a335c: r5 = 0
    //     0x15a335c: movz            x5, #0
    // 0x15a3360: b               #0x15a3370
    // 0x15a3364: r5 = LoadInt32Instr(r4)
    //     0x15a3364: sbfx            x5, x4, #1, #0x1f
    //     0x15a3368: tbz             w4, #0, #0x15a3370
    //     0x15a336c: ldur            x5, [x4, #7]
    // 0x15a3370: r4 = Instance_ColorSpace
    //     0x15a3370: ldr             x4, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a3374: d0 = 255.000000
    //     0x15a3374: ldr             d0, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a3378: d1 = 0.700000
    //     0x15a3378: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x15a337c: ldr             d1, [x17, #0xf48]
    // 0x15a3380: StoreField: r2->field_27 = r4
    //     0x15a3380: stur            w4, [x2, #0x27]
    // 0x15a3384: StoreField: r2->field_7 = d1
    //     0x15a3384: stur            d1, [x2, #7]
    // 0x15a3388: ubfx            x1, x1, #0, #0x20
    // 0x15a338c: and             w6, w1, #0xff
    // 0x15a3390: ubfx            x6, x6, #0, #0x20
    // 0x15a3394: scvtf           d2, x6
    // 0x15a3398: fdiv            d3, d2, d0
    // 0x15a339c: StoreField: r2->field_f = d3
    //     0x15a339c: stur            d3, [x2, #0xf]
    // 0x15a33a0: ubfx            x3, x3, #0, #0x20
    // 0x15a33a4: and             w1, w3, #0xff
    // 0x15a33a8: ubfx            x1, x1, #0, #0x20
    // 0x15a33ac: scvtf           d2, x1
    // 0x15a33b0: fdiv            d3, d2, d0
    // 0x15a33b4: ArrayStore: r2[0] = d3  ; List_8
    //     0x15a33b4: stur            d3, [x2, #0x17]
    // 0x15a33b8: ubfx            x5, x5, #0, #0x20
    // 0x15a33bc: and             w1, w5, #0xff
    // 0x15a33c0: ubfx            x1, x1, #0, #0x20
    // 0x15a33c4: scvtf           d2, x1
    // 0x15a33c8: fdiv            d3, d2, d0
    // 0x15a33cc: StoreField: r2->field_1f = d3
    //     0x15a33cc: stur            d3, [x2, #0x1f]
    // 0x15a33d0: cmp             w0, NULL
    // 0x15a33d4: b.ne            #0x15a33e0
    // 0x15a33d8: r0 = Null
    //     0x15a33d8: mov             x0, NULL
    // 0x15a33dc: b               #0x15a3430
    // 0x15a33e0: LoadField: r1 = r0->field_57
    //     0x15a33e0: ldur            w1, [x0, #0x57]
    // 0x15a33e4: DecompressPointer r1
    //     0x15a33e4: add             x1, x1, HEAP, lsl #32
    // 0x15a33e8: cmp             w1, NULL
    // 0x15a33ec: b.ne            #0x15a33f8
    // 0x15a33f0: r0 = Null
    //     0x15a33f0: mov             x0, NULL
    // 0x15a33f4: b               #0x15a3430
    // 0x15a33f8: LoadField: r0 = r1->field_13
    //     0x15a33f8: ldur            w0, [x1, #0x13]
    // 0x15a33fc: DecompressPointer r0
    //     0x15a33fc: add             x0, x0, HEAP, lsl #32
    // 0x15a3400: cmp             w0, NULL
    // 0x15a3404: b.ne            #0x15a3410
    // 0x15a3408: r0 = Null
    //     0x15a3408: mov             x0, NULL
    // 0x15a340c: b               #0x15a3430
    // 0x15a3410: LoadField: r1 = r0->field_13
    //     0x15a3410: ldur            w1, [x0, #0x13]
    // 0x15a3414: DecompressPointer r1
    //     0x15a3414: add             x1, x1, HEAP, lsl #32
    // 0x15a3418: cmp             w1, NULL
    // 0x15a341c: b.ne            #0x15a3428
    // 0x15a3420: r0 = Null
    //     0x15a3420: mov             x0, NULL
    // 0x15a3424: b               #0x15a3430
    // 0x15a3428: LoadField: r0 = r1->field_7
    //     0x15a3428: ldur            w0, [x1, #7]
    // 0x15a342c: DecompressPointer r0
    //     0x15a342c: add             x0, x0, HEAP, lsl #32
    // 0x15a3430: cmp             w0, NULL
    // 0x15a3434: b.ne            #0x15a3444
    // 0x15a3438: r3 = "Montserrat"
    //     0x15a3438: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f50] "Montserrat"
    //     0x15a343c: ldr             x3, [x3, #0xf50]
    // 0x15a3440: b               #0x15a3448
    // 0x15a3444: mov             x3, x0
    // 0x15a3448: ldur            x0, [fp, #-0xc0]
    // 0x15a344c: mov             x1, x3
    // 0x15a3450: stur            x3, [fp, #-0xe8]
    // 0x15a3454: r0 = getFont()
    //     0x15a3454: bl              #0xc994b0  ; [package:google_fonts/google_fonts.dart] GoogleFonts::getFont
    // 0x15a3458: r17 = -264
    //     0x15a3458: movn            x17, #0x107
    // 0x15a345c: str             x0, [fp, x17]
    // 0x15a3460: r0 = Color()
    //     0x15a3460: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a3464: mov             x1, x0
    // 0x15a3468: ldur            x0, [fp, #-0xc0]
    // 0x15a346c: LoadField: r2 = r0->field_b
    //     0x15a346c: ldur            w2, [x0, #0xb]
    // 0x15a3470: DecompressPointer r2
    //     0x15a3470: add             x2, x2, HEAP, lsl #32
    // 0x15a3474: cmp             w2, NULL
    // 0x15a3478: b.ne            #0x15a3484
    // 0x15a347c: r3 = Null
    //     0x15a347c: mov             x3, NULL
    // 0x15a3480: b               #0x15a34bc
    // 0x15a3484: LoadField: r3 = r2->field_57
    //     0x15a3484: ldur            w3, [x2, #0x57]
    // 0x15a3488: DecompressPointer r3
    //     0x15a3488: add             x3, x3, HEAP, lsl #32
    // 0x15a348c: cmp             w3, NULL
    // 0x15a3490: b.ne            #0x15a349c
    // 0x15a3494: r3 = Null
    //     0x15a3494: mov             x3, NULL
    // 0x15a3498: b               #0x15a34bc
    // 0x15a349c: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a349c: ldur            w4, [x3, #0x17]
    // 0x15a34a0: DecompressPointer r4
    //     0x15a34a0: add             x4, x4, HEAP, lsl #32
    // 0x15a34a4: cmp             w4, NULL
    // 0x15a34a8: b.ne            #0x15a34b4
    // 0x15a34ac: r3 = Null
    //     0x15a34ac: mov             x3, NULL
    // 0x15a34b0: b               #0x15a34bc
    // 0x15a34b4: LoadField: r3 = r4->field_7
    //     0x15a34b4: ldur            w3, [x4, #7]
    // 0x15a34b8: DecompressPointer r3
    //     0x15a34b8: add             x3, x3, HEAP, lsl #32
    // 0x15a34bc: cmp             w3, NULL
    // 0x15a34c0: b.ne            #0x15a34cc
    // 0x15a34c4: r3 = 0
    //     0x15a34c4: movz            x3, #0
    // 0x15a34c8: b               #0x15a34dc
    // 0x15a34cc: r4 = LoadInt32Instr(r3)
    //     0x15a34cc: sbfx            x4, x3, #1, #0x1f
    //     0x15a34d0: tbz             w3, #0, #0x15a34d8
    //     0x15a34d4: ldur            x4, [x3, #7]
    // 0x15a34d8: mov             x3, x4
    // 0x15a34dc: cmp             w2, NULL
    // 0x15a34e0: b.ne            #0x15a34ec
    // 0x15a34e4: r4 = Null
    //     0x15a34e4: mov             x4, NULL
    // 0x15a34e8: b               #0x15a3524
    // 0x15a34ec: LoadField: r4 = r2->field_57
    //     0x15a34ec: ldur            w4, [x2, #0x57]
    // 0x15a34f0: DecompressPointer r4
    //     0x15a34f0: add             x4, x4, HEAP, lsl #32
    // 0x15a34f4: cmp             w4, NULL
    // 0x15a34f8: b.ne            #0x15a3504
    // 0x15a34fc: r4 = Null
    //     0x15a34fc: mov             x4, NULL
    // 0x15a3500: b               #0x15a3524
    // 0x15a3504: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a3504: ldur            w5, [x4, #0x17]
    // 0x15a3508: DecompressPointer r5
    //     0x15a3508: add             x5, x5, HEAP, lsl #32
    // 0x15a350c: cmp             w5, NULL
    // 0x15a3510: b.ne            #0x15a351c
    // 0x15a3514: r4 = Null
    //     0x15a3514: mov             x4, NULL
    // 0x15a3518: b               #0x15a3524
    // 0x15a351c: LoadField: r4 = r5->field_b
    //     0x15a351c: ldur            w4, [x5, #0xb]
    // 0x15a3520: DecompressPointer r4
    //     0x15a3520: add             x4, x4, HEAP, lsl #32
    // 0x15a3524: cmp             w4, NULL
    // 0x15a3528: b.ne            #0x15a3534
    // 0x15a352c: r4 = 0
    //     0x15a352c: movz            x4, #0
    // 0x15a3530: b               #0x15a3544
    // 0x15a3534: r5 = LoadInt32Instr(r4)
    //     0x15a3534: sbfx            x5, x4, #1, #0x1f
    //     0x15a3538: tbz             w4, #0, #0x15a3540
    //     0x15a353c: ldur            x5, [x4, #7]
    // 0x15a3540: mov             x4, x5
    // 0x15a3544: cmp             w2, NULL
    // 0x15a3548: b.ne            #0x15a3554
    // 0x15a354c: r2 = Null
    //     0x15a354c: mov             x2, NULL
    // 0x15a3550: b               #0x15a3590
    // 0x15a3554: LoadField: r5 = r2->field_57
    //     0x15a3554: ldur            w5, [x2, #0x57]
    // 0x15a3558: DecompressPointer r5
    //     0x15a3558: add             x5, x5, HEAP, lsl #32
    // 0x15a355c: cmp             w5, NULL
    // 0x15a3560: b.ne            #0x15a356c
    // 0x15a3564: r2 = Null
    //     0x15a3564: mov             x2, NULL
    // 0x15a3568: b               #0x15a3590
    // 0x15a356c: ArrayLoad: r2 = r5[0]  ; List_4
    //     0x15a356c: ldur            w2, [x5, #0x17]
    // 0x15a3570: DecompressPointer r2
    //     0x15a3570: add             x2, x2, HEAP, lsl #32
    // 0x15a3574: cmp             w2, NULL
    // 0x15a3578: b.ne            #0x15a3584
    // 0x15a357c: r2 = Null
    //     0x15a357c: mov             x2, NULL
    // 0x15a3580: b               #0x15a3590
    // 0x15a3584: LoadField: r5 = r2->field_f
    //     0x15a3584: ldur            w5, [x2, #0xf]
    // 0x15a3588: DecompressPointer r5
    //     0x15a3588: add             x5, x5, HEAP, lsl #32
    // 0x15a358c: mov             x2, x5
    // 0x15a3590: cmp             w2, NULL
    // 0x15a3594: b.ne            #0x15a35a0
    // 0x15a3598: r5 = 0
    //     0x15a3598: movz            x5, #0
    // 0x15a359c: b               #0x15a35ac
    // 0x15a35a0: r5 = LoadInt32Instr(r2)
    //     0x15a35a0: sbfx            x5, x2, #1, #0x1f
    //     0x15a35a4: tbz             w2, #0, #0x15a35ac
    //     0x15a35a8: ldur            x5, [x2, #7]
    // 0x15a35ac: r2 = Instance_ColorSpace
    //     0x15a35ac: ldr             x2, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a35b0: d0 = 255.000000
    //     0x15a35b0: ldr             d0, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a35b4: d1 = 1.000000
    //     0x15a35b4: fmov            d1, #1.00000000
    // 0x15a35b8: stur            x5, [fp, #-0xc8]
    // 0x15a35bc: StoreField: r1->field_27 = r2
    //     0x15a35bc: stur            w2, [x1, #0x27]
    // 0x15a35c0: StoreField: r1->field_7 = d1
    //     0x15a35c0: stur            d1, [x1, #7]
    // 0x15a35c4: ubfx            x3, x3, #0, #0x20
    // 0x15a35c8: and             w6, w3, #0xff
    // 0x15a35cc: ubfx            x6, x6, #0, #0x20
    // 0x15a35d0: scvtf           d2, x6
    // 0x15a35d4: fdiv            d3, d2, d0
    // 0x15a35d8: StoreField: r1->field_f = d3
    //     0x15a35d8: stur            d3, [x1, #0xf]
    // 0x15a35dc: ubfx            x4, x4, #0, #0x20
    // 0x15a35e0: and             w3, w4, #0xff
    // 0x15a35e4: ubfx            x3, x3, #0, #0x20
    // 0x15a35e8: scvtf           d2, x3
    // 0x15a35ec: fdiv            d3, d2, d0
    // 0x15a35f0: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a35f0: stur            d3, [x1, #0x17]
    // 0x15a35f4: mov             x3, x5
    // 0x15a35f8: ubfx            x3, x3, #0, #0x20
    // 0x15a35fc: and             w4, w3, #0xff
    // 0x15a3600: ubfx            x4, x4, #0, #0x20
    // 0x15a3604: scvtf           d2, x4
    // 0x15a3608: fdiv            d3, d2, d0
    // 0x15a360c: StoreField: r1->field_1f = d3
    //     0x15a360c: stur            d3, [x1, #0x1f]
    // 0x15a3610: str             x1, [SP]
    // 0x15a3614: r17 = -264
    //     0x15a3614: movn            x17, #0x107
    // 0x15a3618: ldr             x1, [fp, x17]
    // 0x15a361c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15a361c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15a3620: ldr             x4, [x4, #0xf40]
    // 0x15a3624: r0 = copyWith()
    //     0x15a3624: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15a3628: mov             x2, x0
    // 0x15a362c: ldur            x0, [fp, #-0xc0]
    // 0x15a3630: r17 = -264
    //     0x15a3630: movn            x17, #0x107
    // 0x15a3634: str             x2, [fp, x17]
    // 0x15a3638: LoadField: r1 = r0->field_b
    //     0x15a3638: ldur            w1, [x0, #0xb]
    // 0x15a363c: DecompressPointer r1
    //     0x15a363c: add             x1, x1, HEAP, lsl #32
    // 0x15a3640: cmp             w1, NULL
    // 0x15a3644: b.ne            #0x15a3650
    // 0x15a3648: r1 = Null
    //     0x15a3648: mov             x1, NULL
    // 0x15a364c: b               #0x15a36a0
    // 0x15a3650: LoadField: r3 = r1->field_57
    //     0x15a3650: ldur            w3, [x1, #0x57]
    // 0x15a3654: DecompressPointer r3
    //     0x15a3654: add             x3, x3, HEAP, lsl #32
    // 0x15a3658: cmp             w3, NULL
    // 0x15a365c: b.ne            #0x15a3668
    // 0x15a3660: r1 = Null
    //     0x15a3660: mov             x1, NULL
    // 0x15a3664: b               #0x15a36a0
    // 0x15a3668: LoadField: r1 = r3->field_13
    //     0x15a3668: ldur            w1, [x3, #0x13]
    // 0x15a366c: DecompressPointer r1
    //     0x15a366c: add             x1, x1, HEAP, lsl #32
    // 0x15a3670: cmp             w1, NULL
    // 0x15a3674: b.ne            #0x15a3680
    // 0x15a3678: r1 = Null
    //     0x15a3678: mov             x1, NULL
    // 0x15a367c: b               #0x15a36a0
    // 0x15a3680: LoadField: r3 = r1->field_13
    //     0x15a3680: ldur            w3, [x1, #0x13]
    // 0x15a3684: DecompressPointer r3
    //     0x15a3684: add             x3, x3, HEAP, lsl #32
    // 0x15a3688: cmp             w3, NULL
    // 0x15a368c: b.ne            #0x15a3698
    // 0x15a3690: r1 = Null
    //     0x15a3690: mov             x1, NULL
    // 0x15a3694: b               #0x15a36a0
    // 0x15a3698: LoadField: r1 = r3->field_7
    //     0x15a3698: ldur            w1, [x3, #7]
    // 0x15a369c: DecompressPointer r1
    //     0x15a369c: add             x1, x1, HEAP, lsl #32
    // 0x15a36a0: cmp             w1, NULL
    // 0x15a36a4: b.ne            #0x15a36b4
    // 0x15a36a8: r3 = "Montserrat"
    //     0x15a36a8: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f50] "Montserrat"
    //     0x15a36ac: ldr             x3, [x3, #0xf50]
    // 0x15a36b0: b               #0x15a36b8
    // 0x15a36b4: mov             x3, x1
    // 0x15a36b8: mov             x1, x3
    // 0x15a36bc: stur            x3, [fp, #-0xe8]
    // 0x15a36c0: r0 = getFont()
    //     0x15a36c0: bl              #0xc994b0  ; [package:google_fonts/google_fonts.dart] GoogleFonts::getFont
    // 0x15a36c4: r17 = -272
    //     0x15a36c4: movn            x17, #0x10f
    // 0x15a36c8: str             x0, [fp, x17]
    // 0x15a36cc: r0 = Color()
    //     0x15a36cc: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a36d0: mov             x1, x0
    // 0x15a36d4: ldur            x0, [fp, #-0xc0]
    // 0x15a36d8: LoadField: r2 = r0->field_b
    //     0x15a36d8: ldur            w2, [x0, #0xb]
    // 0x15a36dc: DecompressPointer r2
    //     0x15a36dc: add             x2, x2, HEAP, lsl #32
    // 0x15a36e0: cmp             w2, NULL
    // 0x15a36e4: b.ne            #0x15a36f0
    // 0x15a36e8: r3 = Null
    //     0x15a36e8: mov             x3, NULL
    // 0x15a36ec: b               #0x15a3728
    // 0x15a36f0: LoadField: r3 = r2->field_57
    //     0x15a36f0: ldur            w3, [x2, #0x57]
    // 0x15a36f4: DecompressPointer r3
    //     0x15a36f4: add             x3, x3, HEAP, lsl #32
    // 0x15a36f8: cmp             w3, NULL
    // 0x15a36fc: b.ne            #0x15a3708
    // 0x15a3700: r3 = Null
    //     0x15a3700: mov             x3, NULL
    // 0x15a3704: b               #0x15a3728
    // 0x15a3708: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a3708: ldur            w4, [x3, #0x17]
    // 0x15a370c: DecompressPointer r4
    //     0x15a370c: add             x4, x4, HEAP, lsl #32
    // 0x15a3710: cmp             w4, NULL
    // 0x15a3714: b.ne            #0x15a3720
    // 0x15a3718: r3 = Null
    //     0x15a3718: mov             x3, NULL
    // 0x15a371c: b               #0x15a3728
    // 0x15a3720: LoadField: r3 = r4->field_7
    //     0x15a3720: ldur            w3, [x4, #7]
    // 0x15a3724: DecompressPointer r3
    //     0x15a3724: add             x3, x3, HEAP, lsl #32
    // 0x15a3728: cmp             w3, NULL
    // 0x15a372c: b.ne            #0x15a3738
    // 0x15a3730: r3 = 0
    //     0x15a3730: movz            x3, #0
    // 0x15a3734: b               #0x15a3748
    // 0x15a3738: r4 = LoadInt32Instr(r3)
    //     0x15a3738: sbfx            x4, x3, #1, #0x1f
    //     0x15a373c: tbz             w3, #0, #0x15a3744
    //     0x15a3740: ldur            x4, [x3, #7]
    // 0x15a3744: mov             x3, x4
    // 0x15a3748: cmp             w2, NULL
    // 0x15a374c: b.ne            #0x15a3758
    // 0x15a3750: r4 = Null
    //     0x15a3750: mov             x4, NULL
    // 0x15a3754: b               #0x15a3790
    // 0x15a3758: LoadField: r4 = r2->field_57
    //     0x15a3758: ldur            w4, [x2, #0x57]
    // 0x15a375c: DecompressPointer r4
    //     0x15a375c: add             x4, x4, HEAP, lsl #32
    // 0x15a3760: cmp             w4, NULL
    // 0x15a3764: b.ne            #0x15a3770
    // 0x15a3768: r4 = Null
    //     0x15a3768: mov             x4, NULL
    // 0x15a376c: b               #0x15a3790
    // 0x15a3770: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a3770: ldur            w5, [x4, #0x17]
    // 0x15a3774: DecompressPointer r5
    //     0x15a3774: add             x5, x5, HEAP, lsl #32
    // 0x15a3778: cmp             w5, NULL
    // 0x15a377c: b.ne            #0x15a3788
    // 0x15a3780: r4 = Null
    //     0x15a3780: mov             x4, NULL
    // 0x15a3784: b               #0x15a3790
    // 0x15a3788: LoadField: r4 = r5->field_b
    //     0x15a3788: ldur            w4, [x5, #0xb]
    // 0x15a378c: DecompressPointer r4
    //     0x15a378c: add             x4, x4, HEAP, lsl #32
    // 0x15a3790: cmp             w4, NULL
    // 0x15a3794: b.ne            #0x15a37a0
    // 0x15a3798: r4 = 0
    //     0x15a3798: movz            x4, #0
    // 0x15a379c: b               #0x15a37b0
    // 0x15a37a0: r5 = LoadInt32Instr(r4)
    //     0x15a37a0: sbfx            x5, x4, #1, #0x1f
    //     0x15a37a4: tbz             w4, #0, #0x15a37ac
    //     0x15a37a8: ldur            x5, [x4, #7]
    // 0x15a37ac: mov             x4, x5
    // 0x15a37b0: cmp             w2, NULL
    // 0x15a37b4: b.ne            #0x15a37c0
    // 0x15a37b8: r2 = Null
    //     0x15a37b8: mov             x2, NULL
    // 0x15a37bc: b               #0x15a37fc
    // 0x15a37c0: LoadField: r5 = r2->field_57
    //     0x15a37c0: ldur            w5, [x2, #0x57]
    // 0x15a37c4: DecompressPointer r5
    //     0x15a37c4: add             x5, x5, HEAP, lsl #32
    // 0x15a37c8: cmp             w5, NULL
    // 0x15a37cc: b.ne            #0x15a37d8
    // 0x15a37d0: r2 = Null
    //     0x15a37d0: mov             x2, NULL
    // 0x15a37d4: b               #0x15a37fc
    // 0x15a37d8: ArrayLoad: r2 = r5[0]  ; List_4
    //     0x15a37d8: ldur            w2, [x5, #0x17]
    // 0x15a37dc: DecompressPointer r2
    //     0x15a37dc: add             x2, x2, HEAP, lsl #32
    // 0x15a37e0: cmp             w2, NULL
    // 0x15a37e4: b.ne            #0x15a37f0
    // 0x15a37e8: r2 = Null
    //     0x15a37e8: mov             x2, NULL
    // 0x15a37ec: b               #0x15a37fc
    // 0x15a37f0: LoadField: r5 = r2->field_f
    //     0x15a37f0: ldur            w5, [x2, #0xf]
    // 0x15a37f4: DecompressPointer r5
    //     0x15a37f4: add             x5, x5, HEAP, lsl #32
    // 0x15a37f8: mov             x2, x5
    // 0x15a37fc: cmp             w2, NULL
    // 0x15a3800: b.ne            #0x15a380c
    // 0x15a3804: r10 = 0
    //     0x15a3804: movz            x10, #0
    // 0x15a3808: b               #0x15a381c
    // 0x15a380c: r5 = LoadInt32Instr(r2)
    //     0x15a380c: sbfx            x5, x2, #1, #0x1f
    //     0x15a3810: tbz             w2, #0, #0x15a3818
    //     0x15a3814: ldur            x5, [x2, #7]
    // 0x15a3818: mov             x10, x5
    // 0x15a381c: ldur            x9, [fp, #-0xe0]
    // 0x15a3820: ldur            x8, [fp, #-0xf8]
    // 0x15a3824: ldur            x7, [fp, #-0xf0]
    // 0x15a3828: ldur            x6, [fp, #-0x100]
    // 0x15a382c: r17 = -264
    //     0x15a382c: movn            x17, #0x107
    // 0x15a3830: ldr             x2, [fp, x17]
    // 0x15a3834: r5 = Instance_ColorSpace
    //     0x15a3834: ldr             x5, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a3838: d0 = 255.000000
    //     0x15a3838: ldr             d0, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a383c: d1 = 0.400000
    //     0x15a383c: ldr             d1, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x15a3840: stur            x10, [fp, #-0xc8]
    // 0x15a3844: StoreField: r1->field_27 = r5
    //     0x15a3844: stur            w5, [x1, #0x27]
    // 0x15a3848: StoreField: r1->field_7 = d1
    //     0x15a3848: stur            d1, [x1, #7]
    // 0x15a384c: ubfx            x3, x3, #0, #0x20
    // 0x15a3850: and             w11, w3, #0xff
    // 0x15a3854: ubfx            x11, x11, #0, #0x20
    // 0x15a3858: scvtf           d2, x11
    // 0x15a385c: fdiv            d3, d2, d0
    // 0x15a3860: StoreField: r1->field_f = d3
    //     0x15a3860: stur            d3, [x1, #0xf]
    // 0x15a3864: ubfx            x4, x4, #0, #0x20
    // 0x15a3868: and             w3, w4, #0xff
    // 0x15a386c: ubfx            x3, x3, #0, #0x20
    // 0x15a3870: scvtf           d2, x3
    // 0x15a3874: fdiv            d3, d2, d0
    // 0x15a3878: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a3878: stur            d3, [x1, #0x17]
    // 0x15a387c: mov             x3, x10
    // 0x15a3880: ubfx            x3, x3, #0, #0x20
    // 0x15a3884: and             w4, w3, #0xff
    // 0x15a3888: ubfx            x4, x4, #0, #0x20
    // 0x15a388c: scvtf           d2, x4
    // 0x15a3890: fdiv            d3, d2, d0
    // 0x15a3894: StoreField: r1->field_1f = d3
    //     0x15a3894: stur            d3, [x1, #0x1f]
    // 0x15a3898: str             x1, [SP]
    // 0x15a389c: r17 = -272
    //     0x15a389c: movn            x17, #0x10f
    // 0x15a38a0: ldr             x1, [fp, x17]
    // 0x15a38a4: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15a38a4: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15a38a8: ldr             x4, [x4, #0xf40]
    // 0x15a38ac: r0 = copyWith()
    //     0x15a38ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15a38b0: stur            x0, [fp, #-0xe8]
    // 0x15a38b4: r0 = BottomNavigationBarThemeData()
    //     0x15a38b4: bl              #0xc959f4  ; AllocateBottomNavigationBarThemeDataStub -> BottomNavigationBarThemeData (size=0x40)
    // 0x15a38b8: mov             x1, x0
    // 0x15a38bc: ldur            x0, [fp, #-0xe0]
    // 0x15a38c0: r17 = -272
    //     0x15a38c0: movn            x17, #0x10f
    // 0x15a38c4: str             x1, [fp, x17]
    // 0x15a38c8: StoreField: r1->field_f = r0
    //     0x15a38c8: stur            w0, [x1, #0xf]
    // 0x15a38cc: ldur            x2, [fp, #-0xf8]
    // 0x15a38d0: StoreField: r1->field_13 = r2
    //     0x15a38d0: stur            w2, [x1, #0x13]
    // 0x15a38d4: ldur            x3, [fp, #-0xf0]
    // 0x15a38d8: ArrayStore: r1[0] = r3  ; List_4
    //     0x15a38d8: stur            w3, [x1, #0x17]
    // 0x15a38dc: ldur            x4, [fp, #-0x100]
    // 0x15a38e0: StoreField: r1->field_1b = r4
    //     0x15a38e0: stur            w4, [x1, #0x1b]
    // 0x15a38e4: r17 = -264
    //     0x15a38e4: movn            x17, #0x107
    // 0x15a38e8: ldr             x5, [fp, x17]
    // 0x15a38ec: StoreField: r1->field_1f = r5
    //     0x15a38ec: stur            w5, [x1, #0x1f]
    // 0x15a38f0: ldur            x6, [fp, #-0xe8]
    // 0x15a38f4: StoreField: r1->field_23 = r6
    //     0x15a38f4: stur            w6, [x1, #0x23]
    // 0x15a38f8: r0 = AppBarTheme()
    //     0x15a38f8: bl              #0x7958fc  ; AllocateAppBarThemeStub -> AppBarTheme (size=0x58)
    // 0x15a38fc: r17 = -280
    //     0x15a38fc: movn            x17, #0x117
    // 0x15a3900: str             x0, [fp, x17]
    // 0x15a3904: r0 = Color()
    //     0x15a3904: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a3908: mov             x1, x0
    // 0x15a390c: ldur            x0, [fp, #-0xc0]
    // 0x15a3910: stur            x1, [fp, #-0xe8]
    // 0x15a3914: LoadField: r2 = r0->field_b
    //     0x15a3914: ldur            w2, [x0, #0xb]
    // 0x15a3918: DecompressPointer r2
    //     0x15a3918: add             x2, x2, HEAP, lsl #32
    // 0x15a391c: stur            x2, [fp, #-0xe0]
    // 0x15a3920: cmp             w2, NULL
    // 0x15a3924: b.ne            #0x15a3930
    // 0x15a3928: r3 = Null
    //     0x15a3928: mov             x3, NULL
    // 0x15a392c: b               #0x15a3968
    // 0x15a3930: LoadField: r3 = r2->field_57
    //     0x15a3930: ldur            w3, [x2, #0x57]
    // 0x15a3934: DecompressPointer r3
    //     0x15a3934: add             x3, x3, HEAP, lsl #32
    // 0x15a3938: cmp             w3, NULL
    // 0x15a393c: b.ne            #0x15a3948
    // 0x15a3940: r3 = Null
    //     0x15a3940: mov             x3, NULL
    // 0x15a3944: b               #0x15a3968
    // 0x15a3948: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a3948: ldur            w4, [x3, #0x17]
    // 0x15a394c: DecompressPointer r4
    //     0x15a394c: add             x4, x4, HEAP, lsl #32
    // 0x15a3950: cmp             w4, NULL
    // 0x15a3954: b.ne            #0x15a3960
    // 0x15a3958: r3 = Null
    //     0x15a3958: mov             x3, NULL
    // 0x15a395c: b               #0x15a3968
    // 0x15a3960: LoadField: r3 = r4->field_7
    //     0x15a3960: ldur            w3, [x4, #7]
    // 0x15a3964: DecompressPointer r3
    //     0x15a3964: add             x3, x3, HEAP, lsl #32
    // 0x15a3968: cmp             w3, NULL
    // 0x15a396c: b.ne            #0x15a3978
    // 0x15a3970: r3 = 0
    //     0x15a3970: movz            x3, #0
    // 0x15a3974: b               #0x15a3988
    // 0x15a3978: r4 = LoadInt32Instr(r3)
    //     0x15a3978: sbfx            x4, x3, #1, #0x1f
    //     0x15a397c: tbz             w3, #0, #0x15a3984
    //     0x15a3980: ldur            x4, [x3, #7]
    // 0x15a3984: mov             x3, x4
    // 0x15a3988: cmp             w2, NULL
    // 0x15a398c: b.ne            #0x15a3998
    // 0x15a3990: r4 = Null
    //     0x15a3990: mov             x4, NULL
    // 0x15a3994: b               #0x15a39d0
    // 0x15a3998: LoadField: r4 = r2->field_57
    //     0x15a3998: ldur            w4, [x2, #0x57]
    // 0x15a399c: DecompressPointer r4
    //     0x15a399c: add             x4, x4, HEAP, lsl #32
    // 0x15a39a0: cmp             w4, NULL
    // 0x15a39a4: b.ne            #0x15a39b0
    // 0x15a39a8: r4 = Null
    //     0x15a39a8: mov             x4, NULL
    // 0x15a39ac: b               #0x15a39d0
    // 0x15a39b0: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a39b0: ldur            w5, [x4, #0x17]
    // 0x15a39b4: DecompressPointer r5
    //     0x15a39b4: add             x5, x5, HEAP, lsl #32
    // 0x15a39b8: cmp             w5, NULL
    // 0x15a39bc: b.ne            #0x15a39c8
    // 0x15a39c0: r4 = Null
    //     0x15a39c0: mov             x4, NULL
    // 0x15a39c4: b               #0x15a39d0
    // 0x15a39c8: LoadField: r4 = r5->field_b
    //     0x15a39c8: ldur            w4, [x5, #0xb]
    // 0x15a39cc: DecompressPointer r4
    //     0x15a39cc: add             x4, x4, HEAP, lsl #32
    // 0x15a39d0: cmp             w4, NULL
    // 0x15a39d4: b.ne            #0x15a39e0
    // 0x15a39d8: r4 = 0
    //     0x15a39d8: movz            x4, #0
    // 0x15a39dc: b               #0x15a39f0
    // 0x15a39e0: r5 = LoadInt32Instr(r4)
    //     0x15a39e0: sbfx            x5, x4, #1, #0x1f
    //     0x15a39e4: tbz             w4, #0, #0x15a39ec
    //     0x15a39e8: ldur            x5, [x4, #7]
    // 0x15a39ec: mov             x4, x5
    // 0x15a39f0: cmp             w2, NULL
    // 0x15a39f4: b.ne            #0x15a3a00
    // 0x15a39f8: r5 = Null
    //     0x15a39f8: mov             x5, NULL
    // 0x15a39fc: b               #0x15a3a38
    // 0x15a3a00: LoadField: r5 = r2->field_57
    //     0x15a3a00: ldur            w5, [x2, #0x57]
    // 0x15a3a04: DecompressPointer r5
    //     0x15a3a04: add             x5, x5, HEAP, lsl #32
    // 0x15a3a08: cmp             w5, NULL
    // 0x15a3a0c: b.ne            #0x15a3a18
    // 0x15a3a10: r5 = Null
    //     0x15a3a10: mov             x5, NULL
    // 0x15a3a14: b               #0x15a3a38
    // 0x15a3a18: ArrayLoad: r6 = r5[0]  ; List_4
    //     0x15a3a18: ldur            w6, [x5, #0x17]
    // 0x15a3a1c: DecompressPointer r6
    //     0x15a3a1c: add             x6, x6, HEAP, lsl #32
    // 0x15a3a20: cmp             w6, NULL
    // 0x15a3a24: b.ne            #0x15a3a30
    // 0x15a3a28: r5 = Null
    //     0x15a3a28: mov             x5, NULL
    // 0x15a3a2c: b               #0x15a3a38
    // 0x15a3a30: LoadField: r5 = r6->field_f
    //     0x15a3a30: ldur            w5, [x6, #0xf]
    // 0x15a3a34: DecompressPointer r5
    //     0x15a3a34: add             x5, x5, HEAP, lsl #32
    // 0x15a3a38: cmp             w5, NULL
    // 0x15a3a3c: b.ne            #0x15a3a48
    // 0x15a3a40: r6 = 0
    //     0x15a3a40: movz            x6, #0
    // 0x15a3a44: b               #0x15a3a54
    // 0x15a3a48: r6 = LoadInt32Instr(r5)
    //     0x15a3a48: sbfx            x6, x5, #1, #0x1f
    //     0x15a3a4c: tbz             w5, #0, #0x15a3a54
    //     0x15a3a50: ldur            x6, [x5, #7]
    // 0x15a3a54: r5 = Instance_ColorSpace
    //     0x15a3a54: ldr             x5, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a3a58: d0 = 255.000000
    //     0x15a3a58: ldr             d0, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a3a5c: d1 = 1.000000
    //     0x15a3a5c: fmov            d1, #1.00000000
    // 0x15a3a60: stur            x6, [fp, #-0xc8]
    // 0x15a3a64: StoreField: r1->field_27 = r5
    //     0x15a3a64: stur            w5, [x1, #0x27]
    // 0x15a3a68: StoreField: r1->field_7 = d1
    //     0x15a3a68: stur            d1, [x1, #7]
    // 0x15a3a6c: ubfx            x3, x3, #0, #0x20
    // 0x15a3a70: and             w7, w3, #0xff
    // 0x15a3a74: ubfx            x7, x7, #0, #0x20
    // 0x15a3a78: scvtf           d2, x7
    // 0x15a3a7c: fdiv            d3, d2, d0
    // 0x15a3a80: StoreField: r1->field_f = d3
    //     0x15a3a80: stur            d3, [x1, #0xf]
    // 0x15a3a84: ubfx            x4, x4, #0, #0x20
    // 0x15a3a88: and             w3, w4, #0xff
    // 0x15a3a8c: ubfx            x3, x3, #0, #0x20
    // 0x15a3a90: scvtf           d2, x3
    // 0x15a3a94: fdiv            d3, d2, d0
    // 0x15a3a98: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a3a98: stur            d3, [x1, #0x17]
    // 0x15a3a9c: mov             x3, x6
    // 0x15a3aa0: ubfx            x3, x3, #0, #0x20
    // 0x15a3aa4: and             w4, w3, #0xff
    // 0x15a3aa8: ubfx            x4, x4, #0, #0x20
    // 0x15a3aac: scvtf           d2, x4
    // 0x15a3ab0: fdiv            d3, d2, d0
    // 0x15a3ab4: StoreField: r1->field_1f = d3
    //     0x15a3ab4: stur            d3, [x1, #0x1f]
    // 0x15a3ab8: r0 = IconThemeData()
    //     0x15a3ab8: bl              #0x6b2d30  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0x15a3abc: mov             x1, x0
    // 0x15a3ac0: ldur            x0, [fp, #-0xe8]
    // 0x15a3ac4: stur            x1, [fp, #-0xf0]
    // 0x15a3ac8: StoreField: r1->field_1b = r0
    //     0x15a3ac8: stur            w0, [x1, #0x1b]
    // 0x15a3acc: r0 = Color()
    //     0x15a3acc: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a3ad0: mov             x1, x0
    // 0x15a3ad4: ldur            x0, [fp, #-0xe0]
    // 0x15a3ad8: stur            x1, [fp, #-0xe8]
    // 0x15a3adc: cmp             w0, NULL
    // 0x15a3ae0: b.ne            #0x15a3aec
    // 0x15a3ae4: r2 = Null
    //     0x15a3ae4: mov             x2, NULL
    // 0x15a3ae8: b               #0x15a3b24
    // 0x15a3aec: LoadField: r2 = r0->field_57
    //     0x15a3aec: ldur            w2, [x0, #0x57]
    // 0x15a3af0: DecompressPointer r2
    //     0x15a3af0: add             x2, x2, HEAP, lsl #32
    // 0x15a3af4: cmp             w2, NULL
    // 0x15a3af8: b.ne            #0x15a3b04
    // 0x15a3afc: r2 = Null
    //     0x15a3afc: mov             x2, NULL
    // 0x15a3b00: b               #0x15a3b24
    // 0x15a3b04: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x15a3b04: ldur            w3, [x2, #0x17]
    // 0x15a3b08: DecompressPointer r3
    //     0x15a3b08: add             x3, x3, HEAP, lsl #32
    // 0x15a3b0c: cmp             w3, NULL
    // 0x15a3b10: b.ne            #0x15a3b1c
    // 0x15a3b14: r2 = Null
    //     0x15a3b14: mov             x2, NULL
    // 0x15a3b18: b               #0x15a3b24
    // 0x15a3b1c: LoadField: r2 = r3->field_7
    //     0x15a3b1c: ldur            w2, [x3, #7]
    // 0x15a3b20: DecompressPointer r2
    //     0x15a3b20: add             x2, x2, HEAP, lsl #32
    // 0x15a3b24: cmp             w2, NULL
    // 0x15a3b28: b.ne            #0x15a3b34
    // 0x15a3b2c: r2 = 0
    //     0x15a3b2c: movz            x2, #0
    // 0x15a3b30: b               #0x15a3b44
    // 0x15a3b34: r3 = LoadInt32Instr(r2)
    //     0x15a3b34: sbfx            x3, x2, #1, #0x1f
    //     0x15a3b38: tbz             w2, #0, #0x15a3b40
    //     0x15a3b3c: ldur            x3, [x2, #7]
    // 0x15a3b40: mov             x2, x3
    // 0x15a3b44: cmp             w0, NULL
    // 0x15a3b48: b.ne            #0x15a3b54
    // 0x15a3b4c: r3 = Null
    //     0x15a3b4c: mov             x3, NULL
    // 0x15a3b50: b               #0x15a3b8c
    // 0x15a3b54: LoadField: r3 = r0->field_57
    //     0x15a3b54: ldur            w3, [x0, #0x57]
    // 0x15a3b58: DecompressPointer r3
    //     0x15a3b58: add             x3, x3, HEAP, lsl #32
    // 0x15a3b5c: cmp             w3, NULL
    // 0x15a3b60: b.ne            #0x15a3b6c
    // 0x15a3b64: r3 = Null
    //     0x15a3b64: mov             x3, NULL
    // 0x15a3b68: b               #0x15a3b8c
    // 0x15a3b6c: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a3b6c: ldur            w4, [x3, #0x17]
    // 0x15a3b70: DecompressPointer r4
    //     0x15a3b70: add             x4, x4, HEAP, lsl #32
    // 0x15a3b74: cmp             w4, NULL
    // 0x15a3b78: b.ne            #0x15a3b84
    // 0x15a3b7c: r3 = Null
    //     0x15a3b7c: mov             x3, NULL
    // 0x15a3b80: b               #0x15a3b8c
    // 0x15a3b84: LoadField: r3 = r4->field_b
    //     0x15a3b84: ldur            w3, [x4, #0xb]
    // 0x15a3b88: DecompressPointer r3
    //     0x15a3b88: add             x3, x3, HEAP, lsl #32
    // 0x15a3b8c: cmp             w3, NULL
    // 0x15a3b90: b.ne            #0x15a3b9c
    // 0x15a3b94: r3 = 0
    //     0x15a3b94: movz            x3, #0
    // 0x15a3b98: b               #0x15a3bac
    // 0x15a3b9c: r4 = LoadInt32Instr(r3)
    //     0x15a3b9c: sbfx            x4, x3, #1, #0x1f
    //     0x15a3ba0: tbz             w3, #0, #0x15a3ba8
    //     0x15a3ba4: ldur            x4, [x3, #7]
    // 0x15a3ba8: mov             x3, x4
    // 0x15a3bac: cmp             w0, NULL
    // 0x15a3bb0: b.ne            #0x15a3bbc
    // 0x15a3bb4: r0 = Null
    //     0x15a3bb4: mov             x0, NULL
    // 0x15a3bb8: b               #0x15a3bf8
    // 0x15a3bbc: LoadField: r4 = r0->field_57
    //     0x15a3bbc: ldur            w4, [x0, #0x57]
    // 0x15a3bc0: DecompressPointer r4
    //     0x15a3bc0: add             x4, x4, HEAP, lsl #32
    // 0x15a3bc4: cmp             w4, NULL
    // 0x15a3bc8: b.ne            #0x15a3bd4
    // 0x15a3bcc: r0 = Null
    //     0x15a3bcc: mov             x0, NULL
    // 0x15a3bd0: b               #0x15a3bf8
    // 0x15a3bd4: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x15a3bd4: ldur            w0, [x4, #0x17]
    // 0x15a3bd8: DecompressPointer r0
    //     0x15a3bd8: add             x0, x0, HEAP, lsl #32
    // 0x15a3bdc: cmp             w0, NULL
    // 0x15a3be0: b.ne            #0x15a3bec
    // 0x15a3be4: r0 = Null
    //     0x15a3be4: mov             x0, NULL
    // 0x15a3be8: b               #0x15a3bf8
    // 0x15a3bec: LoadField: r4 = r0->field_f
    //     0x15a3bec: ldur            w4, [x0, #0xf]
    // 0x15a3bf0: DecompressPointer r4
    //     0x15a3bf0: add             x4, x4, HEAP, lsl #32
    // 0x15a3bf4: mov             x0, x4
    // 0x15a3bf8: cmp             w0, NULL
    // 0x15a3bfc: b.ne            #0x15a3c08
    // 0x15a3c00: r7 = 0
    //     0x15a3c00: movz            x7, #0
    // 0x15a3c04: b               #0x15a3c18
    // 0x15a3c08: r4 = LoadInt32Instr(r0)
    //     0x15a3c08: sbfx            x4, x0, #1, #0x1f
    //     0x15a3c0c: tbz             w0, #0, #0x15a3c14
    //     0x15a3c10: ldur            x4, [x0, #7]
    // 0x15a3c14: mov             x7, x4
    // 0x15a3c18: ldur            x4, [fp, #-0xc0]
    // 0x15a3c1c: r17 = -280
    //     0x15a3c1c: movn            x17, #0x117
    // 0x15a3c20: ldr             x6, [fp, x17]
    // 0x15a3c24: ldur            x0, [fp, #-0xf0]
    // 0x15a3c28: r5 = Instance_ColorSpace
    //     0x15a3c28: ldr             x5, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a3c2c: d0 = 255.000000
    //     0x15a3c2c: ldr             d0, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a3c30: d1 = 1.000000
    //     0x15a3c30: fmov            d1, #1.00000000
    // 0x15a3c34: stur            x7, [fp, #-0xc8]
    // 0x15a3c38: StoreField: r1->field_27 = r5
    //     0x15a3c38: stur            w5, [x1, #0x27]
    // 0x15a3c3c: StoreField: r1->field_7 = d1
    //     0x15a3c3c: stur            d1, [x1, #7]
    // 0x15a3c40: ubfx            x2, x2, #0, #0x20
    // 0x15a3c44: and             w8, w2, #0xff
    // 0x15a3c48: ubfx            x8, x8, #0, #0x20
    // 0x15a3c4c: scvtf           d2, x8
    // 0x15a3c50: fdiv            d3, d2, d0
    // 0x15a3c54: StoreField: r1->field_f = d3
    //     0x15a3c54: stur            d3, [x1, #0xf]
    // 0x15a3c58: ubfx            x3, x3, #0, #0x20
    // 0x15a3c5c: and             w2, w3, #0xff
    // 0x15a3c60: ubfx            x2, x2, #0, #0x20
    // 0x15a3c64: scvtf           d2, x2
    // 0x15a3c68: fdiv            d3, d2, d0
    // 0x15a3c6c: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a3c6c: stur            d3, [x1, #0x17]
    // 0x15a3c70: mov             x2, x7
    // 0x15a3c74: ubfx            x2, x2, #0, #0x20
    // 0x15a3c78: and             w3, w2, #0xff
    // 0x15a3c7c: ubfx            x3, x3, #0, #0x20
    // 0x15a3c80: scvtf           d2, x3
    // 0x15a3c84: fdiv            d3, d2, d0
    // 0x15a3c88: StoreField: r1->field_1f = d3
    //     0x15a3c88: stur            d3, [x1, #0x1f]
    // 0x15a3c8c: r0 = IconThemeData()
    //     0x15a3c8c: bl              #0x6b2d30  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0x15a3c90: mov             x2, x0
    // 0x15a3c94: ldur            x0, [fp, #-0xe8]
    // 0x15a3c98: stur            x2, [fp, #-0xe0]
    // 0x15a3c9c: StoreField: r2->field_1b = r0
    //     0x15a3c9c: stur            w0, [x2, #0x1b]
    // 0x15a3ca0: r17 = -280
    //     0x15a3ca0: movn            x17, #0x117
    // 0x15a3ca4: ldr             x3, [fp, x17]
    // 0x15a3ca8: StoreField: r3->field_2f = r2
    //     0x15a3ca8: stur            w2, [x3, #0x2f]
    // 0x15a3cac: ldur            x4, [fp, #-0xf0]
    // 0x15a3cb0: StoreField: r3->field_33 = r4
    //     0x15a3cb0: stur            w4, [x3, #0x33]
    // 0x15a3cb4: r5 = Instance_SizedBox
    //     0x15a3cb4: add             x5, PP, #0x12, lsl #12  ; [pp+0x12f58] Obj!SizedBox@d67da1
    //     0x15a3cb8: ldr             x5, [x5, #0xf58]
    // 0x15a3cbc: StoreField: r3->field_b = r5
    //     0x15a3cbc: stur            w5, [x3, #0xb]
    // 0x15a3cc0: mov             x1, x3
    // 0x15a3cc4: r0 = _NativeScene._()
    //     0x15a3cc4: bl              #0x16ed860  ; [dart:ui] _NativeScene::_NativeScene._
    // 0x15a3cc8: r0 = Color()
    //     0x15a3cc8: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a3ccc: mov             x1, x0
    // 0x15a3cd0: ldur            x0, [fp, #-0xc0]
    // 0x15a3cd4: LoadField: r2 = r0->field_b
    //     0x15a3cd4: ldur            w2, [x0, #0xb]
    // 0x15a3cd8: DecompressPointer r2
    //     0x15a3cd8: add             x2, x2, HEAP, lsl #32
    // 0x15a3cdc: cmp             w2, NULL
    // 0x15a3ce0: b.ne            #0x15a3cec
    // 0x15a3ce4: r3 = Null
    //     0x15a3ce4: mov             x3, NULL
    // 0x15a3ce8: b               #0x15a3d24
    // 0x15a3cec: LoadField: r3 = r2->field_57
    //     0x15a3cec: ldur            w3, [x2, #0x57]
    // 0x15a3cf0: DecompressPointer r3
    //     0x15a3cf0: add             x3, x3, HEAP, lsl #32
    // 0x15a3cf4: cmp             w3, NULL
    // 0x15a3cf8: b.ne            #0x15a3d04
    // 0x15a3cfc: r3 = Null
    //     0x15a3cfc: mov             x3, NULL
    // 0x15a3d00: b               #0x15a3d24
    // 0x15a3d04: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a3d04: ldur            w4, [x3, #0x17]
    // 0x15a3d08: DecompressPointer r4
    //     0x15a3d08: add             x4, x4, HEAP, lsl #32
    // 0x15a3d0c: cmp             w4, NULL
    // 0x15a3d10: b.ne            #0x15a3d1c
    // 0x15a3d14: r3 = Null
    //     0x15a3d14: mov             x3, NULL
    // 0x15a3d18: b               #0x15a3d24
    // 0x15a3d1c: LoadField: r3 = r4->field_7
    //     0x15a3d1c: ldur            w3, [x4, #7]
    // 0x15a3d20: DecompressPointer r3
    //     0x15a3d20: add             x3, x3, HEAP, lsl #32
    // 0x15a3d24: cmp             w3, NULL
    // 0x15a3d28: b.ne            #0x15a3d34
    // 0x15a3d2c: r3 = 0
    //     0x15a3d2c: movz            x3, #0
    // 0x15a3d30: b               #0x15a3d44
    // 0x15a3d34: r4 = LoadInt32Instr(r3)
    //     0x15a3d34: sbfx            x4, x3, #1, #0x1f
    //     0x15a3d38: tbz             w3, #0, #0x15a3d40
    //     0x15a3d3c: ldur            x4, [x3, #7]
    // 0x15a3d40: mov             x3, x4
    // 0x15a3d44: cmp             w2, NULL
    // 0x15a3d48: b.ne            #0x15a3d54
    // 0x15a3d4c: r4 = Null
    //     0x15a3d4c: mov             x4, NULL
    // 0x15a3d50: b               #0x15a3d8c
    // 0x15a3d54: LoadField: r4 = r2->field_57
    //     0x15a3d54: ldur            w4, [x2, #0x57]
    // 0x15a3d58: DecompressPointer r4
    //     0x15a3d58: add             x4, x4, HEAP, lsl #32
    // 0x15a3d5c: cmp             w4, NULL
    // 0x15a3d60: b.ne            #0x15a3d6c
    // 0x15a3d64: r4 = Null
    //     0x15a3d64: mov             x4, NULL
    // 0x15a3d68: b               #0x15a3d8c
    // 0x15a3d6c: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a3d6c: ldur            w5, [x4, #0x17]
    // 0x15a3d70: DecompressPointer r5
    //     0x15a3d70: add             x5, x5, HEAP, lsl #32
    // 0x15a3d74: cmp             w5, NULL
    // 0x15a3d78: b.ne            #0x15a3d84
    // 0x15a3d7c: r4 = Null
    //     0x15a3d7c: mov             x4, NULL
    // 0x15a3d80: b               #0x15a3d8c
    // 0x15a3d84: LoadField: r4 = r5->field_b
    //     0x15a3d84: ldur            w4, [x5, #0xb]
    // 0x15a3d88: DecompressPointer r4
    //     0x15a3d88: add             x4, x4, HEAP, lsl #32
    // 0x15a3d8c: cmp             w4, NULL
    // 0x15a3d90: b.ne            #0x15a3d9c
    // 0x15a3d94: r4 = 0
    //     0x15a3d94: movz            x4, #0
    // 0x15a3d98: b               #0x15a3dac
    // 0x15a3d9c: r5 = LoadInt32Instr(r4)
    //     0x15a3d9c: sbfx            x5, x4, #1, #0x1f
    //     0x15a3da0: tbz             w4, #0, #0x15a3da8
    //     0x15a3da4: ldur            x5, [x4, #7]
    // 0x15a3da8: mov             x4, x5
    // 0x15a3dac: cmp             w2, NULL
    // 0x15a3db0: b.ne            #0x15a3dbc
    // 0x15a3db4: r2 = Null
    //     0x15a3db4: mov             x2, NULL
    // 0x15a3db8: b               #0x15a3df8
    // 0x15a3dbc: LoadField: r5 = r2->field_57
    //     0x15a3dbc: ldur            w5, [x2, #0x57]
    // 0x15a3dc0: DecompressPointer r5
    //     0x15a3dc0: add             x5, x5, HEAP, lsl #32
    // 0x15a3dc4: cmp             w5, NULL
    // 0x15a3dc8: b.ne            #0x15a3dd4
    // 0x15a3dcc: r2 = Null
    //     0x15a3dcc: mov             x2, NULL
    // 0x15a3dd0: b               #0x15a3df8
    // 0x15a3dd4: ArrayLoad: r2 = r5[0]  ; List_4
    //     0x15a3dd4: ldur            w2, [x5, #0x17]
    // 0x15a3dd8: DecompressPointer r2
    //     0x15a3dd8: add             x2, x2, HEAP, lsl #32
    // 0x15a3ddc: cmp             w2, NULL
    // 0x15a3de0: b.ne            #0x15a3dec
    // 0x15a3de4: r2 = Null
    //     0x15a3de4: mov             x2, NULL
    // 0x15a3de8: b               #0x15a3df8
    // 0x15a3dec: LoadField: r5 = r2->field_f
    //     0x15a3dec: ldur            w5, [x2, #0xf]
    // 0x15a3df0: DecompressPointer r5
    //     0x15a3df0: add             x5, x5, HEAP, lsl #32
    // 0x15a3df4: mov             x2, x5
    // 0x15a3df8: cmp             w2, NULL
    // 0x15a3dfc: b.ne            #0x15a3e08
    // 0x15a3e00: r5 = 0
    //     0x15a3e00: movz            x5, #0
    // 0x15a3e04: b               #0x15a3e14
    // 0x15a3e08: r5 = LoadInt32Instr(r2)
    //     0x15a3e08: sbfx            x5, x2, #1, #0x1f
    //     0x15a3e0c: tbz             w2, #0, #0x15a3e14
    //     0x15a3e10: ldur            x5, [x2, #7]
    // 0x15a3e14: r2 = Instance_ColorSpace
    //     0x15a3e14: ldr             x2, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a3e18: d0 = 255.000000
    //     0x15a3e18: ldr             d0, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a3e1c: d1 = 1.000000
    //     0x15a3e1c: fmov            d1, #1.00000000
    // 0x15a3e20: stur            x5, [fp, #-0xc8]
    // 0x15a3e24: StoreField: r1->field_27 = r2
    //     0x15a3e24: stur            w2, [x1, #0x27]
    // 0x15a3e28: StoreField: r1->field_7 = d1
    //     0x15a3e28: stur            d1, [x1, #7]
    // 0x15a3e2c: ubfx            x3, x3, #0, #0x20
    // 0x15a3e30: and             w6, w3, #0xff
    // 0x15a3e34: ubfx            x6, x6, #0, #0x20
    // 0x15a3e38: scvtf           d2, x6
    // 0x15a3e3c: fdiv            d3, d2, d0
    // 0x15a3e40: StoreField: r1->field_f = d3
    //     0x15a3e40: stur            d3, [x1, #0xf]
    // 0x15a3e44: ubfx            x4, x4, #0, #0x20
    // 0x15a3e48: and             w3, w4, #0xff
    // 0x15a3e4c: ubfx            x3, x3, #0, #0x20
    // 0x15a3e50: scvtf           d2, x3
    // 0x15a3e54: fdiv            d3, d2, d0
    // 0x15a3e58: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a3e58: stur            d3, [x1, #0x17]
    // 0x15a3e5c: mov             x3, x5
    // 0x15a3e60: ubfx            x3, x3, #0, #0x20
    // 0x15a3e64: and             w4, w3, #0xff
    // 0x15a3e68: ubfx            x4, x4, #0, #0x20
    // 0x15a3e6c: scvtf           d2, x4
    // 0x15a3e70: fdiv            d3, d2, d0
    // 0x15a3e74: StoreField: r1->field_1f = d3
    //     0x15a3e74: stur            d3, [x1, #0x1f]
    // 0x15a3e78: str             x1, [SP]
    // 0x15a3e7c: r1 = Instance_IconThemeData
    //     0x15a3e7c: ldr             x1, [PP, #0x5560]  ; [pp+0x5560] Obj!IconThemeData@d64df1
    // 0x15a3e80: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15a3e80: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15a3e84: ldr             x4, [x4, #0xf40]
    // 0x15a3e88: r0 = copyWith()
    //     0x15a3e88: bl              #0x16abf9c  ; [package:flutter/src/widgets/icon_theme_data.dart] IconThemeData::copyWith
    // 0x15a3e8c: mov             x2, x0
    // 0x15a3e90: ldur            x0, [fp, #-0xc0]
    // 0x15a3e94: stur            x2, [fp, #-0xe8]
    // 0x15a3e98: LoadField: r1 = r0->field_b
    //     0x15a3e98: ldur            w1, [x0, #0xb]
    // 0x15a3e9c: DecompressPointer r1
    //     0x15a3e9c: add             x1, x1, HEAP, lsl #32
    // 0x15a3ea0: cmp             w1, NULL
    // 0x15a3ea4: b.ne            #0x15a3eb0
    // 0x15a3ea8: r1 = Null
    //     0x15a3ea8: mov             x1, NULL
    // 0x15a3eac: b               #0x15a3f00
    // 0x15a3eb0: LoadField: r3 = r1->field_57
    //     0x15a3eb0: ldur            w3, [x1, #0x57]
    // 0x15a3eb4: DecompressPointer r3
    //     0x15a3eb4: add             x3, x3, HEAP, lsl #32
    // 0x15a3eb8: cmp             w3, NULL
    // 0x15a3ebc: b.ne            #0x15a3ec8
    // 0x15a3ec0: r1 = Null
    //     0x15a3ec0: mov             x1, NULL
    // 0x15a3ec4: b               #0x15a3f00
    // 0x15a3ec8: LoadField: r1 = r3->field_13
    //     0x15a3ec8: ldur            w1, [x3, #0x13]
    // 0x15a3ecc: DecompressPointer r1
    //     0x15a3ecc: add             x1, x1, HEAP, lsl #32
    // 0x15a3ed0: cmp             w1, NULL
    // 0x15a3ed4: b.ne            #0x15a3ee0
    // 0x15a3ed8: r1 = Null
    //     0x15a3ed8: mov             x1, NULL
    // 0x15a3edc: b               #0x15a3f00
    // 0x15a3ee0: LoadField: r3 = r1->field_b
    //     0x15a3ee0: ldur            w3, [x1, #0xb]
    // 0x15a3ee4: DecompressPointer r3
    //     0x15a3ee4: add             x3, x3, HEAP, lsl #32
    // 0x15a3ee8: cmp             w3, NULL
    // 0x15a3eec: b.ne            #0x15a3ef8
    // 0x15a3ef0: r1 = Null
    //     0x15a3ef0: mov             x1, NULL
    // 0x15a3ef4: b               #0x15a3f00
    // 0x15a3ef8: LoadField: r1 = r3->field_7
    //     0x15a3ef8: ldur            w1, [x3, #7]
    // 0x15a3efc: DecompressPointer r1
    //     0x15a3efc: add             x1, x1, HEAP, lsl #32
    // 0x15a3f00: cmp             w1, NULL
    // 0x15a3f04: b.ne            #0x15a3f14
    // 0x15a3f08: r3 = "Montserrat"
    //     0x15a3f08: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f50] "Montserrat"
    //     0x15a3f0c: ldr             x3, [x3, #0xf50]
    // 0x15a3f10: b               #0x15a3f18
    // 0x15a3f14: mov             x3, x1
    // 0x15a3f18: mov             x1, x3
    // 0x15a3f1c: stur            x3, [fp, #-0xe0]
    // 0x15a3f20: r0 = getFont()
    //     0x15a3f20: bl              #0xc994b0  ; [package:google_fonts/google_fonts.dart] GoogleFonts::getFont
    // 0x15a3f24: mov             x2, x0
    // 0x15a3f28: ldur            x0, [fp, #-0xc0]
    // 0x15a3f2c: stur            x2, [fp, #-0xf0]
    // 0x15a3f30: LoadField: r1 = r0->field_b
    //     0x15a3f30: ldur            w1, [x0, #0xb]
    // 0x15a3f34: DecompressPointer r1
    //     0x15a3f34: add             x1, x1, HEAP, lsl #32
    // 0x15a3f38: cmp             w1, NULL
    // 0x15a3f3c: b.ne            #0x15a3f48
    // 0x15a3f40: r3 = Null
    //     0x15a3f40: mov             x3, NULL
    // 0x15a3f44: b               #0x15a3f9c
    // 0x15a3f48: LoadField: r3 = r1->field_57
    //     0x15a3f48: ldur            w3, [x1, #0x57]
    // 0x15a3f4c: DecompressPointer r3
    //     0x15a3f4c: add             x3, x3, HEAP, lsl #32
    // 0x15a3f50: cmp             w3, NULL
    // 0x15a3f54: b.ne            #0x15a3f60
    // 0x15a3f58: r1 = Null
    //     0x15a3f58: mov             x1, NULL
    // 0x15a3f5c: b               #0x15a3f98
    // 0x15a3f60: LoadField: r1 = r3->field_13
    //     0x15a3f60: ldur            w1, [x3, #0x13]
    // 0x15a3f64: DecompressPointer r1
    //     0x15a3f64: add             x1, x1, HEAP, lsl #32
    // 0x15a3f68: cmp             w1, NULL
    // 0x15a3f6c: b.ne            #0x15a3f78
    // 0x15a3f70: r1 = Null
    //     0x15a3f70: mov             x1, NULL
    // 0x15a3f74: b               #0x15a3f98
    // 0x15a3f78: LoadField: r3 = r1->field_b
    //     0x15a3f78: ldur            w3, [x1, #0xb]
    // 0x15a3f7c: DecompressPointer r3
    //     0x15a3f7c: add             x3, x3, HEAP, lsl #32
    // 0x15a3f80: cmp             w3, NULL
    // 0x15a3f84: b.ne            #0x15a3f90
    // 0x15a3f88: r1 = Null
    //     0x15a3f88: mov             x1, NULL
    // 0x15a3f8c: b               #0x15a3f98
    // 0x15a3f90: LoadField: r1 = r3->field_b
    //     0x15a3f90: ldur            w1, [x3, #0xb]
    // 0x15a3f94: DecompressPointer r1
    //     0x15a3f94: add             x1, x1, HEAP, lsl #32
    // 0x15a3f98: mov             x3, x1
    // 0x15a3f9c: mov             x1, x3
    // 0x15a3fa0: stur            x3, [fp, #-0xe0]
    // 0x15a3fa4: r0 = getFontWeight()
    //     0x15a3fa4: bl              #0xc95834  ; [package:customer_app/app/core/utils/utils.dart] ::getFontWeight
    // 0x15a3fa8: r16 = Instance_Color
    //     0x15a3fa8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15a3fac: stp             x16, x0, [SP]
    // 0x15a3fb0: ldur            x1, [fp, #-0xf0]
    // 0x15a3fb4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontWeight, 0x1, null]
    //     0x15a3fb4: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f60] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontWeight", 0x1, Null]
    //     0x15a3fb8: ldr             x4, [x4, #0xf60]
    // 0x15a3fbc: r0 = copyWith()
    //     0x15a3fbc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15a3fc0: mov             x2, x0
    // 0x15a3fc4: ldur            x0, [fp, #-0xc0]
    // 0x15a3fc8: stur            x2, [fp, #-0xf0]
    // 0x15a3fcc: LoadField: r1 = r0->field_b
    //     0x15a3fcc: ldur            w1, [x0, #0xb]
    // 0x15a3fd0: DecompressPointer r1
    //     0x15a3fd0: add             x1, x1, HEAP, lsl #32
    // 0x15a3fd4: cmp             w1, NULL
    // 0x15a3fd8: b.ne            #0x15a3fe4
    // 0x15a3fdc: r1 = Null
    //     0x15a3fdc: mov             x1, NULL
    // 0x15a3fe0: b               #0x15a4034
    // 0x15a3fe4: LoadField: r3 = r1->field_57
    //     0x15a3fe4: ldur            w3, [x1, #0x57]
    // 0x15a3fe8: DecompressPointer r3
    //     0x15a3fe8: add             x3, x3, HEAP, lsl #32
    // 0x15a3fec: cmp             w3, NULL
    // 0x15a3ff0: b.ne            #0x15a3ffc
    // 0x15a3ff4: r1 = Null
    //     0x15a3ff4: mov             x1, NULL
    // 0x15a3ff8: b               #0x15a4034
    // 0x15a3ffc: LoadField: r1 = r3->field_13
    //     0x15a3ffc: ldur            w1, [x3, #0x13]
    // 0x15a4000: DecompressPointer r1
    //     0x15a4000: add             x1, x1, HEAP, lsl #32
    // 0x15a4004: cmp             w1, NULL
    // 0x15a4008: b.ne            #0x15a4014
    // 0x15a400c: r1 = Null
    //     0x15a400c: mov             x1, NULL
    // 0x15a4010: b               #0x15a4034
    // 0x15a4014: LoadField: r3 = r1->field_7
    //     0x15a4014: ldur            w3, [x1, #7]
    // 0x15a4018: DecompressPointer r3
    //     0x15a4018: add             x3, x3, HEAP, lsl #32
    // 0x15a401c: cmp             w3, NULL
    // 0x15a4020: b.ne            #0x15a402c
    // 0x15a4024: r1 = Null
    //     0x15a4024: mov             x1, NULL
    // 0x15a4028: b               #0x15a4034
    // 0x15a402c: LoadField: r1 = r3->field_7
    //     0x15a402c: ldur            w1, [x3, #7]
    // 0x15a4030: DecompressPointer r1
    //     0x15a4030: add             x1, x1, HEAP, lsl #32
    // 0x15a4034: cmp             w1, NULL
    // 0x15a4038: b.ne            #0x15a4048
    // 0x15a403c: r3 = "Montserrat"
    //     0x15a403c: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f50] "Montserrat"
    //     0x15a4040: ldr             x3, [x3, #0xf50]
    // 0x15a4044: b               #0x15a404c
    // 0x15a4048: mov             x3, x1
    // 0x15a404c: mov             x1, x3
    // 0x15a4050: stur            x3, [fp, #-0xe0]
    // 0x15a4054: r0 = getFont()
    //     0x15a4054: bl              #0xc994b0  ; [package:google_fonts/google_fonts.dart] GoogleFonts::getFont
    // 0x15a4058: mov             x2, x0
    // 0x15a405c: ldur            x0, [fp, #-0xc0]
    // 0x15a4060: stur            x2, [fp, #-0xf8]
    // 0x15a4064: LoadField: r1 = r0->field_b
    //     0x15a4064: ldur            w1, [x0, #0xb]
    // 0x15a4068: DecompressPointer r1
    //     0x15a4068: add             x1, x1, HEAP, lsl #32
    // 0x15a406c: cmp             w1, NULL
    // 0x15a4070: b.ne            #0x15a407c
    // 0x15a4074: r3 = Null
    //     0x15a4074: mov             x3, NULL
    // 0x15a4078: b               #0x15a40d0
    // 0x15a407c: LoadField: r3 = r1->field_57
    //     0x15a407c: ldur            w3, [x1, #0x57]
    // 0x15a4080: DecompressPointer r3
    //     0x15a4080: add             x3, x3, HEAP, lsl #32
    // 0x15a4084: cmp             w3, NULL
    // 0x15a4088: b.ne            #0x15a4094
    // 0x15a408c: r1 = Null
    //     0x15a408c: mov             x1, NULL
    // 0x15a4090: b               #0x15a40cc
    // 0x15a4094: LoadField: r1 = r3->field_13
    //     0x15a4094: ldur            w1, [x3, #0x13]
    // 0x15a4098: DecompressPointer r1
    //     0x15a4098: add             x1, x1, HEAP, lsl #32
    // 0x15a409c: cmp             w1, NULL
    // 0x15a40a0: b.ne            #0x15a40ac
    // 0x15a40a4: r1 = Null
    //     0x15a40a4: mov             x1, NULL
    // 0x15a40a8: b               #0x15a40cc
    // 0x15a40ac: LoadField: r3 = r1->field_7
    //     0x15a40ac: ldur            w3, [x1, #7]
    // 0x15a40b0: DecompressPointer r3
    //     0x15a40b0: add             x3, x3, HEAP, lsl #32
    // 0x15a40b4: cmp             w3, NULL
    // 0x15a40b8: b.ne            #0x15a40c4
    // 0x15a40bc: r1 = Null
    //     0x15a40bc: mov             x1, NULL
    // 0x15a40c0: b               #0x15a40cc
    // 0x15a40c4: LoadField: r1 = r3->field_b
    //     0x15a40c4: ldur            w1, [x3, #0xb]
    // 0x15a40c8: DecompressPointer r1
    //     0x15a40c8: add             x1, x1, HEAP, lsl #32
    // 0x15a40cc: mov             x3, x1
    // 0x15a40d0: mov             x1, x3
    // 0x15a40d4: stur            x3, [fp, #-0xe0]
    // 0x15a40d8: r0 = getFontWeight()
    //     0x15a40d8: bl              #0xc95834  ; [package:customer_app/app/core/utils/utils.dart] ::getFontWeight
    // 0x15a40dc: r16 = Instance_Color
    //     0x15a40dc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15a40e0: stp             x16, x0, [SP]
    // 0x15a40e4: ldur            x1, [fp, #-0xf8]
    // 0x15a40e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontWeight, 0x1, null]
    //     0x15a40e8: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f60] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontWeight", 0x1, Null]
    //     0x15a40ec: ldr             x4, [x4, #0xf60]
    // 0x15a40f0: r0 = copyWith()
    //     0x15a40f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15a40f4: mov             x2, x0
    // 0x15a40f8: ldur            x0, [fp, #-0xc0]
    // 0x15a40fc: stur            x2, [fp, #-0xf8]
    // 0x15a4100: LoadField: r1 = r0->field_b
    //     0x15a4100: ldur            w1, [x0, #0xb]
    // 0x15a4104: DecompressPointer r1
    //     0x15a4104: add             x1, x1, HEAP, lsl #32
    // 0x15a4108: cmp             w1, NULL
    // 0x15a410c: b.ne            #0x15a4118
    // 0x15a4110: r1 = Null
    //     0x15a4110: mov             x1, NULL
    // 0x15a4114: b               #0x15a4168
    // 0x15a4118: LoadField: r3 = r1->field_57
    //     0x15a4118: ldur            w3, [x1, #0x57]
    // 0x15a411c: DecompressPointer r3
    //     0x15a411c: add             x3, x3, HEAP, lsl #32
    // 0x15a4120: cmp             w3, NULL
    // 0x15a4124: b.ne            #0x15a4130
    // 0x15a4128: r1 = Null
    //     0x15a4128: mov             x1, NULL
    // 0x15a412c: b               #0x15a4168
    // 0x15a4130: LoadField: r1 = r3->field_13
    //     0x15a4130: ldur            w1, [x3, #0x13]
    // 0x15a4134: DecompressPointer r1
    //     0x15a4134: add             x1, x1, HEAP, lsl #32
    // 0x15a4138: cmp             w1, NULL
    // 0x15a413c: b.ne            #0x15a4148
    // 0x15a4140: r1 = Null
    //     0x15a4140: mov             x1, NULL
    // 0x15a4144: b               #0x15a4168
    // 0x15a4148: LoadField: r3 = r1->field_f
    //     0x15a4148: ldur            w3, [x1, #0xf]
    // 0x15a414c: DecompressPointer r3
    //     0x15a414c: add             x3, x3, HEAP, lsl #32
    // 0x15a4150: cmp             w3, NULL
    // 0x15a4154: b.ne            #0x15a4160
    // 0x15a4158: r1 = Null
    //     0x15a4158: mov             x1, NULL
    // 0x15a415c: b               #0x15a4168
    // 0x15a4160: LoadField: r1 = r3->field_7
    //     0x15a4160: ldur            w1, [x3, #7]
    // 0x15a4164: DecompressPointer r1
    //     0x15a4164: add             x1, x1, HEAP, lsl #32
    // 0x15a4168: cmp             w1, NULL
    // 0x15a416c: b.ne            #0x15a417c
    // 0x15a4170: r3 = "Montserrat"
    //     0x15a4170: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f50] "Montserrat"
    //     0x15a4174: ldr             x3, [x3, #0xf50]
    // 0x15a4178: b               #0x15a4180
    // 0x15a417c: mov             x3, x1
    // 0x15a4180: mov             x1, x3
    // 0x15a4184: stur            x3, [fp, #-0xe0]
    // 0x15a4188: r0 = getFont()
    //     0x15a4188: bl              #0xc994b0  ; [package:google_fonts/google_fonts.dart] GoogleFonts::getFont
    // 0x15a418c: mov             x2, x0
    // 0x15a4190: ldur            x0, [fp, #-0xc0]
    // 0x15a4194: stur            x2, [fp, #-0x100]
    // 0x15a4198: LoadField: r1 = r0->field_b
    //     0x15a4198: ldur            w1, [x0, #0xb]
    // 0x15a419c: DecompressPointer r1
    //     0x15a419c: add             x1, x1, HEAP, lsl #32
    // 0x15a41a0: cmp             w1, NULL
    // 0x15a41a4: b.ne            #0x15a41b0
    // 0x15a41a8: r3 = Null
    //     0x15a41a8: mov             x3, NULL
    // 0x15a41ac: b               #0x15a4204
    // 0x15a41b0: LoadField: r3 = r1->field_57
    //     0x15a41b0: ldur            w3, [x1, #0x57]
    // 0x15a41b4: DecompressPointer r3
    //     0x15a41b4: add             x3, x3, HEAP, lsl #32
    // 0x15a41b8: cmp             w3, NULL
    // 0x15a41bc: b.ne            #0x15a41c8
    // 0x15a41c0: r1 = Null
    //     0x15a41c0: mov             x1, NULL
    // 0x15a41c4: b               #0x15a4200
    // 0x15a41c8: LoadField: r1 = r3->field_13
    //     0x15a41c8: ldur            w1, [x3, #0x13]
    // 0x15a41cc: DecompressPointer r1
    //     0x15a41cc: add             x1, x1, HEAP, lsl #32
    // 0x15a41d0: cmp             w1, NULL
    // 0x15a41d4: b.ne            #0x15a41e0
    // 0x15a41d8: r1 = Null
    //     0x15a41d8: mov             x1, NULL
    // 0x15a41dc: b               #0x15a4200
    // 0x15a41e0: LoadField: r3 = r1->field_f
    //     0x15a41e0: ldur            w3, [x1, #0xf]
    // 0x15a41e4: DecompressPointer r3
    //     0x15a41e4: add             x3, x3, HEAP, lsl #32
    // 0x15a41e8: cmp             w3, NULL
    // 0x15a41ec: b.ne            #0x15a41f8
    // 0x15a41f0: r1 = Null
    //     0x15a41f0: mov             x1, NULL
    // 0x15a41f4: b               #0x15a4200
    // 0x15a41f8: LoadField: r1 = r3->field_b
    //     0x15a41f8: ldur            w1, [x3, #0xb]
    // 0x15a41fc: DecompressPointer r1
    //     0x15a41fc: add             x1, x1, HEAP, lsl #32
    // 0x15a4200: mov             x3, x1
    // 0x15a4204: mov             x1, x3
    // 0x15a4208: stur            x3, [fp, #-0xe0]
    // 0x15a420c: r0 = getFontWeight()
    //     0x15a420c: bl              #0xc95834  ; [package:customer_app/app/core/utils/utils.dart] ::getFontWeight
    // 0x15a4210: r16 = Instance_Color
    //     0x15a4210: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15a4214: stp             x16, x0, [SP]
    // 0x15a4218: ldur            x1, [fp, #-0x100]
    // 0x15a421c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontWeight, 0x1, null]
    //     0x15a421c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f60] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontWeight", 0x1, Null]
    //     0x15a4220: ldr             x4, [x4, #0xf60]
    // 0x15a4224: r0 = copyWith()
    //     0x15a4224: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15a4228: mov             x2, x0
    // 0x15a422c: ldur            x0, [fp, #-0xc0]
    // 0x15a4230: stur            x2, [fp, #-0x100]
    // 0x15a4234: LoadField: r1 = r0->field_b
    //     0x15a4234: ldur            w1, [x0, #0xb]
    // 0x15a4238: DecompressPointer r1
    //     0x15a4238: add             x1, x1, HEAP, lsl #32
    // 0x15a423c: cmp             w1, NULL
    // 0x15a4240: b.ne            #0x15a424c
    // 0x15a4244: r1 = Null
    //     0x15a4244: mov             x1, NULL
    // 0x15a4248: b               #0x15a429c
    // 0x15a424c: LoadField: r3 = r1->field_57
    //     0x15a424c: ldur            w3, [x1, #0x57]
    // 0x15a4250: DecompressPointer r3
    //     0x15a4250: add             x3, x3, HEAP, lsl #32
    // 0x15a4254: cmp             w3, NULL
    // 0x15a4258: b.ne            #0x15a4264
    // 0x15a425c: r1 = Null
    //     0x15a425c: mov             x1, NULL
    // 0x15a4260: b               #0x15a429c
    // 0x15a4264: LoadField: r1 = r3->field_13
    //     0x15a4264: ldur            w1, [x3, #0x13]
    // 0x15a4268: DecompressPointer r1
    //     0x15a4268: add             x1, x1, HEAP, lsl #32
    // 0x15a426c: cmp             w1, NULL
    // 0x15a4270: b.ne            #0x15a427c
    // 0x15a4274: r1 = Null
    //     0x15a4274: mov             x1, NULL
    // 0x15a4278: b               #0x15a429c
    // 0x15a427c: LoadField: r3 = r1->field_13
    //     0x15a427c: ldur            w3, [x1, #0x13]
    // 0x15a4280: DecompressPointer r3
    //     0x15a4280: add             x3, x3, HEAP, lsl #32
    // 0x15a4284: cmp             w3, NULL
    // 0x15a4288: b.ne            #0x15a4294
    // 0x15a428c: r1 = Null
    //     0x15a428c: mov             x1, NULL
    // 0x15a4290: b               #0x15a429c
    // 0x15a4294: LoadField: r1 = r3->field_7
    //     0x15a4294: ldur            w1, [x3, #7]
    // 0x15a4298: DecompressPointer r1
    //     0x15a4298: add             x1, x1, HEAP, lsl #32
    // 0x15a429c: cmp             w1, NULL
    // 0x15a42a0: b.ne            #0x15a42b0
    // 0x15a42a4: r3 = "Montserrat"
    //     0x15a42a4: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f50] "Montserrat"
    //     0x15a42a8: ldr             x3, [x3, #0xf50]
    // 0x15a42ac: b               #0x15a42b4
    // 0x15a42b0: mov             x3, x1
    // 0x15a42b4: mov             x1, x3
    // 0x15a42b8: stur            x3, [fp, #-0xe0]
    // 0x15a42bc: r0 = getFont()
    //     0x15a42bc: bl              #0xc994b0  ; [package:google_fonts/google_fonts.dart] GoogleFonts::getFont
    // 0x15a42c0: mov             x2, x0
    // 0x15a42c4: ldur            x0, [fp, #-0xc0]
    // 0x15a42c8: r17 = -264
    //     0x15a42c8: movn            x17, #0x107
    // 0x15a42cc: str             x2, [fp, x17]
    // 0x15a42d0: LoadField: r1 = r0->field_b
    //     0x15a42d0: ldur            w1, [x0, #0xb]
    // 0x15a42d4: DecompressPointer r1
    //     0x15a42d4: add             x1, x1, HEAP, lsl #32
    // 0x15a42d8: cmp             w1, NULL
    // 0x15a42dc: b.ne            #0x15a42e8
    // 0x15a42e0: r3 = Null
    //     0x15a42e0: mov             x3, NULL
    // 0x15a42e4: b               #0x15a433c
    // 0x15a42e8: LoadField: r3 = r1->field_57
    //     0x15a42e8: ldur            w3, [x1, #0x57]
    // 0x15a42ec: DecompressPointer r3
    //     0x15a42ec: add             x3, x3, HEAP, lsl #32
    // 0x15a42f0: cmp             w3, NULL
    // 0x15a42f4: b.ne            #0x15a4300
    // 0x15a42f8: r1 = Null
    //     0x15a42f8: mov             x1, NULL
    // 0x15a42fc: b               #0x15a4338
    // 0x15a4300: LoadField: r1 = r3->field_13
    //     0x15a4300: ldur            w1, [x3, #0x13]
    // 0x15a4304: DecompressPointer r1
    //     0x15a4304: add             x1, x1, HEAP, lsl #32
    // 0x15a4308: cmp             w1, NULL
    // 0x15a430c: b.ne            #0x15a4318
    // 0x15a4310: r1 = Null
    //     0x15a4310: mov             x1, NULL
    // 0x15a4314: b               #0x15a4338
    // 0x15a4318: LoadField: r3 = r1->field_13
    //     0x15a4318: ldur            w3, [x1, #0x13]
    // 0x15a431c: DecompressPointer r3
    //     0x15a431c: add             x3, x3, HEAP, lsl #32
    // 0x15a4320: cmp             w3, NULL
    // 0x15a4324: b.ne            #0x15a4330
    // 0x15a4328: r1 = Null
    //     0x15a4328: mov             x1, NULL
    // 0x15a432c: b               #0x15a4338
    // 0x15a4330: LoadField: r1 = r3->field_b
    //     0x15a4330: ldur            w1, [x3, #0xb]
    // 0x15a4334: DecompressPointer r1
    //     0x15a4334: add             x1, x1, HEAP, lsl #32
    // 0x15a4338: mov             x3, x1
    // 0x15a433c: mov             x1, x3
    // 0x15a4340: stur            x3, [fp, #-0xe0]
    // 0x15a4344: r0 = getFontWeight()
    //     0x15a4344: bl              #0xc95834  ; [package:customer_app/app/core/utils/utils.dart] ::getFontWeight
    // 0x15a4348: r16 = Instance_Color
    //     0x15a4348: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15a434c: stp             x16, x0, [SP]
    // 0x15a4350: r17 = -264
    //     0x15a4350: movn            x17, #0x107
    // 0x15a4354: ldr             x1, [fp, x17]
    // 0x15a4358: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontWeight, 0x1, null]
    //     0x15a4358: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f60] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontWeight", 0x1, Null]
    //     0x15a435c: ldr             x4, [x4, #0xf60]
    // 0x15a4360: r0 = copyWith()
    //     0x15a4360: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15a4364: mov             x2, x0
    // 0x15a4368: ldur            x0, [fp, #-0xc0]
    // 0x15a436c: r17 = -264
    //     0x15a436c: movn            x17, #0x107
    // 0x15a4370: str             x2, [fp, x17]
    // 0x15a4374: LoadField: r1 = r0->field_b
    //     0x15a4374: ldur            w1, [x0, #0xb]
    // 0x15a4378: DecompressPointer r1
    //     0x15a4378: add             x1, x1, HEAP, lsl #32
    // 0x15a437c: cmp             w1, NULL
    // 0x15a4380: b.ne            #0x15a438c
    // 0x15a4384: r1 = Null
    //     0x15a4384: mov             x1, NULL
    // 0x15a4388: b               #0x15a43dc
    // 0x15a438c: LoadField: r3 = r1->field_57
    //     0x15a438c: ldur            w3, [x1, #0x57]
    // 0x15a4390: DecompressPointer r3
    //     0x15a4390: add             x3, x3, HEAP, lsl #32
    // 0x15a4394: cmp             w3, NULL
    // 0x15a4398: b.ne            #0x15a43a4
    // 0x15a439c: r1 = Null
    //     0x15a439c: mov             x1, NULL
    // 0x15a43a0: b               #0x15a43dc
    // 0x15a43a4: LoadField: r1 = r3->field_13
    //     0x15a43a4: ldur            w1, [x3, #0x13]
    // 0x15a43a8: DecompressPointer r1
    //     0x15a43a8: add             x1, x1, HEAP, lsl #32
    // 0x15a43ac: cmp             w1, NULL
    // 0x15a43b0: b.ne            #0x15a43bc
    // 0x15a43b4: r1 = Null
    //     0x15a43b4: mov             x1, NULL
    // 0x15a43b8: b               #0x15a43dc
    // 0x15a43bc: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x15a43bc: ldur            w3, [x1, #0x17]
    // 0x15a43c0: DecompressPointer r3
    //     0x15a43c0: add             x3, x3, HEAP, lsl #32
    // 0x15a43c4: cmp             w3, NULL
    // 0x15a43c8: b.ne            #0x15a43d4
    // 0x15a43cc: r1 = Null
    //     0x15a43cc: mov             x1, NULL
    // 0x15a43d0: b               #0x15a43dc
    // 0x15a43d4: LoadField: r1 = r3->field_7
    //     0x15a43d4: ldur            w1, [x3, #7]
    // 0x15a43d8: DecompressPointer r1
    //     0x15a43d8: add             x1, x1, HEAP, lsl #32
    // 0x15a43dc: cmp             w1, NULL
    // 0x15a43e0: b.ne            #0x15a43f0
    // 0x15a43e4: r3 = "Montserrat"
    //     0x15a43e4: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f50] "Montserrat"
    //     0x15a43e8: ldr             x3, [x3, #0xf50]
    // 0x15a43ec: b               #0x15a43f4
    // 0x15a43f0: mov             x3, x1
    // 0x15a43f4: mov             x1, x3
    // 0x15a43f8: stur            x3, [fp, #-0xe0]
    // 0x15a43fc: r0 = getFont()
    //     0x15a43fc: bl              #0xc994b0  ; [package:google_fonts/google_fonts.dart] GoogleFonts::getFont
    // 0x15a4400: mov             x2, x0
    // 0x15a4404: ldur            x0, [fp, #-0xc0]
    // 0x15a4408: r17 = -288
    //     0x15a4408: movn            x17, #0x11f
    // 0x15a440c: str             x2, [fp, x17]
    // 0x15a4410: LoadField: r1 = r0->field_b
    //     0x15a4410: ldur            w1, [x0, #0xb]
    // 0x15a4414: DecompressPointer r1
    //     0x15a4414: add             x1, x1, HEAP, lsl #32
    // 0x15a4418: cmp             w1, NULL
    // 0x15a441c: b.ne            #0x15a4428
    // 0x15a4420: r7 = Null
    //     0x15a4420: mov             x7, NULL
    // 0x15a4424: b               #0x15a447c
    // 0x15a4428: LoadField: r3 = r1->field_57
    //     0x15a4428: ldur            w3, [x1, #0x57]
    // 0x15a442c: DecompressPointer r3
    //     0x15a442c: add             x3, x3, HEAP, lsl #32
    // 0x15a4430: cmp             w3, NULL
    // 0x15a4434: b.ne            #0x15a4440
    // 0x15a4438: r1 = Null
    //     0x15a4438: mov             x1, NULL
    // 0x15a443c: b               #0x15a4478
    // 0x15a4440: LoadField: r1 = r3->field_13
    //     0x15a4440: ldur            w1, [x3, #0x13]
    // 0x15a4444: DecompressPointer r1
    //     0x15a4444: add             x1, x1, HEAP, lsl #32
    // 0x15a4448: cmp             w1, NULL
    // 0x15a444c: b.ne            #0x15a4458
    // 0x15a4450: r1 = Null
    //     0x15a4450: mov             x1, NULL
    // 0x15a4454: b               #0x15a4478
    // 0x15a4458: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x15a4458: ldur            w3, [x1, #0x17]
    // 0x15a445c: DecompressPointer r3
    //     0x15a445c: add             x3, x3, HEAP, lsl #32
    // 0x15a4460: cmp             w3, NULL
    // 0x15a4464: b.ne            #0x15a4470
    // 0x15a4468: r1 = Null
    //     0x15a4468: mov             x1, NULL
    // 0x15a446c: b               #0x15a4478
    // 0x15a4470: LoadField: r1 = r3->field_b
    //     0x15a4470: ldur            w1, [x3, #0xb]
    // 0x15a4474: DecompressPointer r1
    //     0x15a4474: add             x1, x1, HEAP, lsl #32
    // 0x15a4478: mov             x7, x1
    // 0x15a447c: ldur            x6, [fp, #-0xf0]
    // 0x15a4480: ldur            x5, [fp, #-0xf8]
    // 0x15a4484: ldur            x4, [fp, #-0x100]
    // 0x15a4488: r17 = -264
    //     0x15a4488: movn            x17, #0x107
    // 0x15a448c: ldr             x3, [fp, x17]
    // 0x15a4490: mov             x1, x7
    // 0x15a4494: stur            x7, [fp, #-0xe0]
    // 0x15a4498: r0 = getFontWeight()
    //     0x15a4498: bl              #0xc95834  ; [package:customer_app/app/core/utils/utils.dart] ::getFontWeight
    // 0x15a449c: r16 = Instance_Color
    //     0x15a449c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15a44a0: stp             x16, x0, [SP]
    // 0x15a44a4: r17 = -288
    //     0x15a44a4: movn            x17, #0x11f
    // 0x15a44a8: ldr             x1, [fp, x17]
    // 0x15a44ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontWeight, 0x1, null]
    //     0x15a44ac: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f60] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontWeight", 0x1, Null]
    //     0x15a44b0: ldr             x4, [x4, #0xf60]
    // 0x15a44b4: r0 = copyWith()
    //     0x15a44b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15a44b8: r17 = -288
    //     0x15a44b8: movn            x17, #0x11f
    // 0x15a44bc: str             x0, [fp, x17]
    // 0x15a44c0: r0 = TextTheme()
    //     0x15a44c0: bl              #0x6afc20  ; AllocateTextThemeStub -> TextTheme (size=0x44)
    // 0x15a44c4: mov             x1, x0
    // 0x15a44c8: r17 = -264
    //     0x15a44c8: movn            x17, #0x107
    // 0x15a44cc: ldr             x0, [fp, x17]
    // 0x15a44d0: r17 = -296
    //     0x15a44d0: movn            x17, #0x127
    // 0x15a44d4: str             x1, [fp, x17]
    // 0x15a44d8: StoreField: r1->field_7 = r0
    //     0x15a44d8: stur            w0, [x1, #7]
    // 0x15a44dc: ldur            x2, [fp, #-0xf8]
    // 0x15a44e0: StoreField: r1->field_1f = r2
    //     0x15a44e0: stur            w2, [x1, #0x1f]
    // 0x15a44e4: ldur            x3, [fp, #-0xf0]
    // 0x15a44e8: StoreField: r1->field_23 = r3
    //     0x15a44e8: stur            w3, [x1, #0x23]
    // 0x15a44ec: ldur            x4, [fp, #-0x100]
    // 0x15a44f0: StoreField: r1->field_27 = r4
    //     0x15a44f0: stur            w4, [x1, #0x27]
    // 0x15a44f4: r17 = -288
    //     0x15a44f4: movn            x17, #0x11f
    // 0x15a44f8: ldr             x5, [fp, x17]
    // 0x15a44fc: StoreField: r1->field_2b = r5
    //     0x15a44fc: stur            w5, [x1, #0x2b]
    // 0x15a4500: r0 = ButtonStyle()
    //     0x15a4500: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x15a4504: r17 = -304
    //     0x15a4504: movn            x17, #0x12f
    // 0x15a4508: str             x0, [fp, x17]
    // 0x15a450c: r0 = RoundedRectangleBorder()
    //     0x15a450c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x15a4510: r17 = -312
    //     0x15a4510: movn            x17, #0x137
    // 0x15a4514: str             x0, [fp, x17]
    // 0x15a4518: r0 = BorderSide()
    //     0x15a4518: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x15a451c: r17 = -320
    //     0x15a451c: movn            x17, #0x13f
    // 0x15a4520: str             x0, [fp, x17]
    // 0x15a4524: r0 = Color()
    //     0x15a4524: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a4528: mov             x1, x0
    // 0x15a452c: ldur            x0, [fp, #-0xc0]
    // 0x15a4530: LoadField: r2 = r0->field_b
    //     0x15a4530: ldur            w2, [x0, #0xb]
    // 0x15a4534: DecompressPointer r2
    //     0x15a4534: add             x2, x2, HEAP, lsl #32
    // 0x15a4538: cmp             w2, NULL
    // 0x15a453c: b.ne            #0x15a4548
    // 0x15a4540: r3 = Null
    //     0x15a4540: mov             x3, NULL
    // 0x15a4544: b               #0x15a4580
    // 0x15a4548: LoadField: r3 = r2->field_57
    //     0x15a4548: ldur            w3, [x2, #0x57]
    // 0x15a454c: DecompressPointer r3
    //     0x15a454c: add             x3, x3, HEAP, lsl #32
    // 0x15a4550: cmp             w3, NULL
    // 0x15a4554: b.ne            #0x15a4560
    // 0x15a4558: r3 = Null
    //     0x15a4558: mov             x3, NULL
    // 0x15a455c: b               #0x15a4580
    // 0x15a4560: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a4560: ldur            w4, [x3, #0x17]
    // 0x15a4564: DecompressPointer r4
    //     0x15a4564: add             x4, x4, HEAP, lsl #32
    // 0x15a4568: cmp             w4, NULL
    // 0x15a456c: b.ne            #0x15a4578
    // 0x15a4570: r3 = Null
    //     0x15a4570: mov             x3, NULL
    // 0x15a4574: b               #0x15a4580
    // 0x15a4578: LoadField: r3 = r4->field_7
    //     0x15a4578: ldur            w3, [x4, #7]
    // 0x15a457c: DecompressPointer r3
    //     0x15a457c: add             x3, x3, HEAP, lsl #32
    // 0x15a4580: cmp             w3, NULL
    // 0x15a4584: b.ne            #0x15a4590
    // 0x15a4588: r3 = 0
    //     0x15a4588: movz            x3, #0
    // 0x15a458c: b               #0x15a45a0
    // 0x15a4590: r4 = LoadInt32Instr(r3)
    //     0x15a4590: sbfx            x4, x3, #1, #0x1f
    //     0x15a4594: tbz             w3, #0, #0x15a459c
    //     0x15a4598: ldur            x4, [x3, #7]
    // 0x15a459c: mov             x3, x4
    // 0x15a45a0: cmp             w2, NULL
    // 0x15a45a4: b.ne            #0x15a45b0
    // 0x15a45a8: r4 = Null
    //     0x15a45a8: mov             x4, NULL
    // 0x15a45ac: b               #0x15a45e8
    // 0x15a45b0: LoadField: r4 = r2->field_57
    //     0x15a45b0: ldur            w4, [x2, #0x57]
    // 0x15a45b4: DecompressPointer r4
    //     0x15a45b4: add             x4, x4, HEAP, lsl #32
    // 0x15a45b8: cmp             w4, NULL
    // 0x15a45bc: b.ne            #0x15a45c8
    // 0x15a45c0: r4 = Null
    //     0x15a45c0: mov             x4, NULL
    // 0x15a45c4: b               #0x15a45e8
    // 0x15a45c8: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a45c8: ldur            w5, [x4, #0x17]
    // 0x15a45cc: DecompressPointer r5
    //     0x15a45cc: add             x5, x5, HEAP, lsl #32
    // 0x15a45d0: cmp             w5, NULL
    // 0x15a45d4: b.ne            #0x15a45e0
    // 0x15a45d8: r4 = Null
    //     0x15a45d8: mov             x4, NULL
    // 0x15a45dc: b               #0x15a45e8
    // 0x15a45e0: LoadField: r4 = r5->field_b
    //     0x15a45e0: ldur            w4, [x5, #0xb]
    // 0x15a45e4: DecompressPointer r4
    //     0x15a45e4: add             x4, x4, HEAP, lsl #32
    // 0x15a45e8: cmp             w4, NULL
    // 0x15a45ec: b.ne            #0x15a45f8
    // 0x15a45f0: r4 = 0
    //     0x15a45f0: movz            x4, #0
    // 0x15a45f4: b               #0x15a4608
    // 0x15a45f8: r5 = LoadInt32Instr(r4)
    //     0x15a45f8: sbfx            x5, x4, #1, #0x1f
    //     0x15a45fc: tbz             w4, #0, #0x15a4604
    //     0x15a4600: ldur            x5, [x4, #7]
    // 0x15a4604: mov             x4, x5
    // 0x15a4608: cmp             w2, NULL
    // 0x15a460c: b.ne            #0x15a4618
    // 0x15a4610: r2 = Null
    //     0x15a4610: mov             x2, NULL
    // 0x15a4614: b               #0x15a4654
    // 0x15a4618: LoadField: r5 = r2->field_57
    //     0x15a4618: ldur            w5, [x2, #0x57]
    // 0x15a461c: DecompressPointer r5
    //     0x15a461c: add             x5, x5, HEAP, lsl #32
    // 0x15a4620: cmp             w5, NULL
    // 0x15a4624: b.ne            #0x15a4630
    // 0x15a4628: r2 = Null
    //     0x15a4628: mov             x2, NULL
    // 0x15a462c: b               #0x15a4654
    // 0x15a4630: ArrayLoad: r2 = r5[0]  ; List_4
    //     0x15a4630: ldur            w2, [x5, #0x17]
    // 0x15a4634: DecompressPointer r2
    //     0x15a4634: add             x2, x2, HEAP, lsl #32
    // 0x15a4638: cmp             w2, NULL
    // 0x15a463c: b.ne            #0x15a4648
    // 0x15a4640: r2 = Null
    //     0x15a4640: mov             x2, NULL
    // 0x15a4644: b               #0x15a4654
    // 0x15a4648: LoadField: r5 = r2->field_f
    //     0x15a4648: ldur            w5, [x2, #0xf]
    // 0x15a464c: DecompressPointer r5
    //     0x15a464c: add             x5, x5, HEAP, lsl #32
    // 0x15a4650: mov             x2, x5
    // 0x15a4654: cmp             w2, NULL
    // 0x15a4658: b.ne            #0x15a4664
    // 0x15a465c: r9 = 0
    //     0x15a465c: movz            x9, #0
    // 0x15a4660: b               #0x15a4674
    // 0x15a4664: r5 = LoadInt32Instr(r2)
    //     0x15a4664: sbfx            x5, x2, #1, #0x1f
    //     0x15a4668: tbz             w2, #0, #0x15a4670
    //     0x15a466c: ldur            x5, [x2, #7]
    // 0x15a4670: mov             x9, x5
    // 0x15a4674: r17 = -312
    //     0x15a4674: movn            x17, #0x137
    // 0x15a4678: ldr             x5, [fp, x17]
    // 0x15a467c: r17 = -320
    //     0x15a467c: movn            x17, #0x13f
    // 0x15a4680: ldr             x2, [fp, x17]
    // 0x15a4684: r8 = Instance_BorderStyle
    //     0x15a4684: add             x8, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x15a4688: ldr             x8, [x8, #0xf68]
    // 0x15a468c: r7 = Instance_BorderRadius
    //     0x15a468c: add             x7, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x15a4690: ldr             x7, [x7, #0xf70]
    // 0x15a4694: r6 = Instance_ColorSpace
    //     0x15a4694: ldr             x6, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a4698: d0 = 255.000000
    //     0x15a4698: ldr             d0, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a469c: d1 = 1.000000
    //     0x15a469c: fmov            d1, #1.00000000
    // 0x15a46a0: d2 = -1.000000
    //     0x15a46a0: fmov            d2, #-1.00000000
    // 0x15a46a4: stur            x9, [fp, #-0xc8]
    // 0x15a46a8: StoreField: r1->field_27 = r6
    //     0x15a46a8: stur            w6, [x1, #0x27]
    // 0x15a46ac: StoreField: r1->field_7 = d1
    //     0x15a46ac: stur            d1, [x1, #7]
    // 0x15a46b0: ubfx            x3, x3, #0, #0x20
    // 0x15a46b4: and             w10, w3, #0xff
    // 0x15a46b8: ubfx            x10, x10, #0, #0x20
    // 0x15a46bc: scvtf           d3, x10
    // 0x15a46c0: fdiv            d4, d3, d0
    // 0x15a46c4: StoreField: r1->field_f = d4
    //     0x15a46c4: stur            d4, [x1, #0xf]
    // 0x15a46c8: ubfx            x4, x4, #0, #0x20
    // 0x15a46cc: and             w3, w4, #0xff
    // 0x15a46d0: ubfx            x3, x3, #0, #0x20
    // 0x15a46d4: scvtf           d3, x3
    // 0x15a46d8: fdiv            d4, d3, d0
    // 0x15a46dc: ArrayStore: r1[0] = d4  ; List_8
    //     0x15a46dc: stur            d4, [x1, #0x17]
    // 0x15a46e0: mov             x3, x9
    // 0x15a46e4: ubfx            x3, x3, #0, #0x20
    // 0x15a46e8: and             w4, w3, #0xff
    // 0x15a46ec: ubfx            x4, x4, #0, #0x20
    // 0x15a46f0: scvtf           d3, x4
    // 0x15a46f4: fdiv            d4, d3, d0
    // 0x15a46f8: StoreField: r1->field_1f = d4
    //     0x15a46f8: stur            d4, [x1, #0x1f]
    // 0x15a46fc: StoreField: r2->field_7 = r1
    //     0x15a46fc: stur            w1, [x2, #7]
    // 0x15a4700: StoreField: r2->field_b = d1
    //     0x15a4700: stur            d1, [x2, #0xb]
    // 0x15a4704: StoreField: r2->field_13 = r8
    //     0x15a4704: stur            w8, [x2, #0x13]
    // 0x15a4708: ArrayStore: r2[0] = d2  ; List_8
    //     0x15a4708: stur            d2, [x2, #0x17]
    // 0x15a470c: StoreField: r5->field_b = r7
    //     0x15a470c: stur            w7, [x5, #0xb]
    // 0x15a4710: StoreField: r5->field_7 = r2
    //     0x15a4710: stur            w2, [x5, #7]
    // 0x15a4714: r16 = <RoundedRectangleBorder>
    //     0x15a4714: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x15a4718: ldr             x16, [x16, #0xf78]
    // 0x15a471c: stp             x5, x16, [SP]
    // 0x15a4720: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15a4720: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15a4724: r0 = all()
    //     0x15a4724: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x15a4728: stur            x0, [fp, #-0xe0]
    // 0x15a472c: r0 = Color()
    //     0x15a472c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a4730: mov             x1, x0
    // 0x15a4734: ldur            x0, [fp, #-0xc0]
    // 0x15a4738: LoadField: r2 = r0->field_b
    //     0x15a4738: ldur            w2, [x0, #0xb]
    // 0x15a473c: DecompressPointer r2
    //     0x15a473c: add             x2, x2, HEAP, lsl #32
    // 0x15a4740: cmp             w2, NULL
    // 0x15a4744: b.ne            #0x15a4750
    // 0x15a4748: r3 = Null
    //     0x15a4748: mov             x3, NULL
    // 0x15a474c: b               #0x15a4788
    // 0x15a4750: LoadField: r3 = r2->field_57
    //     0x15a4750: ldur            w3, [x2, #0x57]
    // 0x15a4754: DecompressPointer r3
    //     0x15a4754: add             x3, x3, HEAP, lsl #32
    // 0x15a4758: cmp             w3, NULL
    // 0x15a475c: b.ne            #0x15a4768
    // 0x15a4760: r3 = Null
    //     0x15a4760: mov             x3, NULL
    // 0x15a4764: b               #0x15a4788
    // 0x15a4768: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a4768: ldur            w4, [x3, #0x17]
    // 0x15a476c: DecompressPointer r4
    //     0x15a476c: add             x4, x4, HEAP, lsl #32
    // 0x15a4770: cmp             w4, NULL
    // 0x15a4774: b.ne            #0x15a4780
    // 0x15a4778: r3 = Null
    //     0x15a4778: mov             x3, NULL
    // 0x15a477c: b               #0x15a4788
    // 0x15a4780: LoadField: r3 = r4->field_7
    //     0x15a4780: ldur            w3, [x4, #7]
    // 0x15a4784: DecompressPointer r3
    //     0x15a4784: add             x3, x3, HEAP, lsl #32
    // 0x15a4788: cmp             w3, NULL
    // 0x15a478c: b.ne            #0x15a4798
    // 0x15a4790: r3 = 0
    //     0x15a4790: movz            x3, #0
    // 0x15a4794: b               #0x15a47a8
    // 0x15a4798: r4 = LoadInt32Instr(r3)
    //     0x15a4798: sbfx            x4, x3, #1, #0x1f
    //     0x15a479c: tbz             w3, #0, #0x15a47a4
    //     0x15a47a0: ldur            x4, [x3, #7]
    // 0x15a47a4: mov             x3, x4
    // 0x15a47a8: cmp             w2, NULL
    // 0x15a47ac: b.ne            #0x15a47b8
    // 0x15a47b0: r4 = Null
    //     0x15a47b0: mov             x4, NULL
    // 0x15a47b4: b               #0x15a47f0
    // 0x15a47b8: LoadField: r4 = r2->field_57
    //     0x15a47b8: ldur            w4, [x2, #0x57]
    // 0x15a47bc: DecompressPointer r4
    //     0x15a47bc: add             x4, x4, HEAP, lsl #32
    // 0x15a47c0: cmp             w4, NULL
    // 0x15a47c4: b.ne            #0x15a47d0
    // 0x15a47c8: r4 = Null
    //     0x15a47c8: mov             x4, NULL
    // 0x15a47cc: b               #0x15a47f0
    // 0x15a47d0: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a47d0: ldur            w5, [x4, #0x17]
    // 0x15a47d4: DecompressPointer r5
    //     0x15a47d4: add             x5, x5, HEAP, lsl #32
    // 0x15a47d8: cmp             w5, NULL
    // 0x15a47dc: b.ne            #0x15a47e8
    // 0x15a47e0: r4 = Null
    //     0x15a47e0: mov             x4, NULL
    // 0x15a47e4: b               #0x15a47f0
    // 0x15a47e8: LoadField: r4 = r5->field_b
    //     0x15a47e8: ldur            w4, [x5, #0xb]
    // 0x15a47ec: DecompressPointer r4
    //     0x15a47ec: add             x4, x4, HEAP, lsl #32
    // 0x15a47f0: cmp             w4, NULL
    // 0x15a47f4: b.ne            #0x15a4800
    // 0x15a47f8: r4 = 0
    //     0x15a47f8: movz            x4, #0
    // 0x15a47fc: b               #0x15a4810
    // 0x15a4800: r5 = LoadInt32Instr(r4)
    //     0x15a4800: sbfx            x5, x4, #1, #0x1f
    //     0x15a4804: tbz             w4, #0, #0x15a480c
    //     0x15a4808: ldur            x5, [x4, #7]
    // 0x15a480c: mov             x4, x5
    // 0x15a4810: cmp             w2, NULL
    // 0x15a4814: b.ne            #0x15a4820
    // 0x15a4818: r2 = Null
    //     0x15a4818: mov             x2, NULL
    // 0x15a481c: b               #0x15a485c
    // 0x15a4820: LoadField: r5 = r2->field_57
    //     0x15a4820: ldur            w5, [x2, #0x57]
    // 0x15a4824: DecompressPointer r5
    //     0x15a4824: add             x5, x5, HEAP, lsl #32
    // 0x15a4828: cmp             w5, NULL
    // 0x15a482c: b.ne            #0x15a4838
    // 0x15a4830: r2 = Null
    //     0x15a4830: mov             x2, NULL
    // 0x15a4834: b               #0x15a485c
    // 0x15a4838: ArrayLoad: r2 = r5[0]  ; List_4
    //     0x15a4838: ldur            w2, [x5, #0x17]
    // 0x15a483c: DecompressPointer r2
    //     0x15a483c: add             x2, x2, HEAP, lsl #32
    // 0x15a4840: cmp             w2, NULL
    // 0x15a4844: b.ne            #0x15a4850
    // 0x15a4848: r2 = Null
    //     0x15a4848: mov             x2, NULL
    // 0x15a484c: b               #0x15a485c
    // 0x15a4850: LoadField: r5 = r2->field_f
    //     0x15a4850: ldur            w5, [x2, #0xf]
    // 0x15a4854: DecompressPointer r5
    //     0x15a4854: add             x5, x5, HEAP, lsl #32
    // 0x15a4858: mov             x2, x5
    // 0x15a485c: cmp             w2, NULL
    // 0x15a4860: b.ne            #0x15a486c
    // 0x15a4864: r5 = 0
    //     0x15a4864: movz            x5, #0
    // 0x15a4868: b               #0x15a4878
    // 0x15a486c: r5 = LoadInt32Instr(r2)
    //     0x15a486c: sbfx            x5, x2, #1, #0x1f
    //     0x15a4870: tbz             w2, #0, #0x15a4878
    //     0x15a4874: ldur            x5, [x2, #7]
    // 0x15a4878: r2 = Instance_ColorSpace
    //     0x15a4878: ldr             x2, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a487c: d0 = 255.000000
    //     0x15a487c: ldr             d0, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a4880: d1 = 1.000000
    //     0x15a4880: fmov            d1, #1.00000000
    // 0x15a4884: stur            x5, [fp, #-0xc8]
    // 0x15a4888: StoreField: r1->field_27 = r2
    //     0x15a4888: stur            w2, [x1, #0x27]
    // 0x15a488c: StoreField: r1->field_7 = d1
    //     0x15a488c: stur            d1, [x1, #7]
    // 0x15a4890: ubfx            x3, x3, #0, #0x20
    // 0x15a4894: and             w6, w3, #0xff
    // 0x15a4898: ubfx            x6, x6, #0, #0x20
    // 0x15a489c: scvtf           d2, x6
    // 0x15a48a0: fdiv            d3, d2, d0
    // 0x15a48a4: StoreField: r1->field_f = d3
    //     0x15a48a4: stur            d3, [x1, #0xf]
    // 0x15a48a8: ubfx            x4, x4, #0, #0x20
    // 0x15a48ac: and             w3, w4, #0xff
    // 0x15a48b0: ubfx            x3, x3, #0, #0x20
    // 0x15a48b4: scvtf           d2, x3
    // 0x15a48b8: fdiv            d3, d2, d0
    // 0x15a48bc: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a48bc: stur            d3, [x1, #0x17]
    // 0x15a48c0: mov             x3, x5
    // 0x15a48c4: ubfx            x3, x3, #0, #0x20
    // 0x15a48c8: and             w4, w3, #0xff
    // 0x15a48cc: ubfx            x4, x4, #0, #0x20
    // 0x15a48d0: scvtf           d2, x4
    // 0x15a48d4: fdiv            d3, d2, d0
    // 0x15a48d8: StoreField: r1->field_1f = d3
    //     0x15a48d8: stur            d3, [x1, #0x1f]
    // 0x15a48dc: r16 = <Color>
    //     0x15a48dc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x15a48e0: ldr             x16, [x16, #0xf80]
    // 0x15a48e4: stp             x1, x16, [SP]
    // 0x15a48e8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15a48e8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15a48ec: r0 = all()
    //     0x15a48ec: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x15a48f0: stur            x0, [fp, #-0xf0]
    // 0x15a48f4: r0 = Color()
    //     0x15a48f4: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a48f8: mov             x1, x0
    // 0x15a48fc: ldur            x0, [fp, #-0xc0]
    // 0x15a4900: LoadField: r2 = r0->field_b
    //     0x15a4900: ldur            w2, [x0, #0xb]
    // 0x15a4904: DecompressPointer r2
    //     0x15a4904: add             x2, x2, HEAP, lsl #32
    // 0x15a4908: cmp             w2, NULL
    // 0x15a490c: b.ne            #0x15a4918
    // 0x15a4910: r3 = Null
    //     0x15a4910: mov             x3, NULL
    // 0x15a4914: b               #0x15a4950
    // 0x15a4918: LoadField: r3 = r2->field_57
    //     0x15a4918: ldur            w3, [x2, #0x57]
    // 0x15a491c: DecompressPointer r3
    //     0x15a491c: add             x3, x3, HEAP, lsl #32
    // 0x15a4920: cmp             w3, NULL
    // 0x15a4924: b.ne            #0x15a4930
    // 0x15a4928: r3 = Null
    //     0x15a4928: mov             x3, NULL
    // 0x15a492c: b               #0x15a4950
    // 0x15a4930: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a4930: ldur            w4, [x3, #0x17]
    // 0x15a4934: DecompressPointer r4
    //     0x15a4934: add             x4, x4, HEAP, lsl #32
    // 0x15a4938: cmp             w4, NULL
    // 0x15a493c: b.ne            #0x15a4948
    // 0x15a4940: r3 = Null
    //     0x15a4940: mov             x3, NULL
    // 0x15a4944: b               #0x15a4950
    // 0x15a4948: LoadField: r3 = r4->field_7
    //     0x15a4948: ldur            w3, [x4, #7]
    // 0x15a494c: DecompressPointer r3
    //     0x15a494c: add             x3, x3, HEAP, lsl #32
    // 0x15a4950: cmp             w3, NULL
    // 0x15a4954: b.ne            #0x15a4960
    // 0x15a4958: r3 = 0
    //     0x15a4958: movz            x3, #0
    // 0x15a495c: b               #0x15a4970
    // 0x15a4960: r4 = LoadInt32Instr(r3)
    //     0x15a4960: sbfx            x4, x3, #1, #0x1f
    //     0x15a4964: tbz             w3, #0, #0x15a496c
    //     0x15a4968: ldur            x4, [x3, #7]
    // 0x15a496c: mov             x3, x4
    // 0x15a4970: cmp             w2, NULL
    // 0x15a4974: b.ne            #0x15a4980
    // 0x15a4978: r4 = Null
    //     0x15a4978: mov             x4, NULL
    // 0x15a497c: b               #0x15a49b8
    // 0x15a4980: LoadField: r4 = r2->field_57
    //     0x15a4980: ldur            w4, [x2, #0x57]
    // 0x15a4984: DecompressPointer r4
    //     0x15a4984: add             x4, x4, HEAP, lsl #32
    // 0x15a4988: cmp             w4, NULL
    // 0x15a498c: b.ne            #0x15a4998
    // 0x15a4990: r4 = Null
    //     0x15a4990: mov             x4, NULL
    // 0x15a4994: b               #0x15a49b8
    // 0x15a4998: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a4998: ldur            w5, [x4, #0x17]
    // 0x15a499c: DecompressPointer r5
    //     0x15a499c: add             x5, x5, HEAP, lsl #32
    // 0x15a49a0: cmp             w5, NULL
    // 0x15a49a4: b.ne            #0x15a49b0
    // 0x15a49a8: r4 = Null
    //     0x15a49a8: mov             x4, NULL
    // 0x15a49ac: b               #0x15a49b8
    // 0x15a49b0: LoadField: r4 = r5->field_b
    //     0x15a49b0: ldur            w4, [x5, #0xb]
    // 0x15a49b4: DecompressPointer r4
    //     0x15a49b4: add             x4, x4, HEAP, lsl #32
    // 0x15a49b8: cmp             w4, NULL
    // 0x15a49bc: b.ne            #0x15a49c8
    // 0x15a49c0: r4 = 0
    //     0x15a49c0: movz            x4, #0
    // 0x15a49c4: b               #0x15a49d8
    // 0x15a49c8: r5 = LoadInt32Instr(r4)
    //     0x15a49c8: sbfx            x5, x4, #1, #0x1f
    //     0x15a49cc: tbz             w4, #0, #0x15a49d4
    //     0x15a49d0: ldur            x5, [x4, #7]
    // 0x15a49d4: mov             x4, x5
    // 0x15a49d8: cmp             w2, NULL
    // 0x15a49dc: b.ne            #0x15a49e8
    // 0x15a49e0: r2 = Null
    //     0x15a49e0: mov             x2, NULL
    // 0x15a49e4: b               #0x15a4a24
    // 0x15a49e8: LoadField: r5 = r2->field_57
    //     0x15a49e8: ldur            w5, [x2, #0x57]
    // 0x15a49ec: DecompressPointer r5
    //     0x15a49ec: add             x5, x5, HEAP, lsl #32
    // 0x15a49f0: cmp             w5, NULL
    // 0x15a49f4: b.ne            #0x15a4a00
    // 0x15a49f8: r2 = Null
    //     0x15a49f8: mov             x2, NULL
    // 0x15a49fc: b               #0x15a4a24
    // 0x15a4a00: ArrayLoad: r2 = r5[0]  ; List_4
    //     0x15a4a00: ldur            w2, [x5, #0x17]
    // 0x15a4a04: DecompressPointer r2
    //     0x15a4a04: add             x2, x2, HEAP, lsl #32
    // 0x15a4a08: cmp             w2, NULL
    // 0x15a4a0c: b.ne            #0x15a4a18
    // 0x15a4a10: r2 = Null
    //     0x15a4a10: mov             x2, NULL
    // 0x15a4a14: b               #0x15a4a24
    // 0x15a4a18: LoadField: r5 = r2->field_f
    //     0x15a4a18: ldur            w5, [x2, #0xf]
    // 0x15a4a1c: DecompressPointer r5
    //     0x15a4a1c: add             x5, x5, HEAP, lsl #32
    // 0x15a4a20: mov             x2, x5
    // 0x15a4a24: cmp             w2, NULL
    // 0x15a4a28: b.ne            #0x15a4a34
    // 0x15a4a2c: r7 = 0
    //     0x15a4a2c: movz            x7, #0
    // 0x15a4a30: b               #0x15a4a44
    // 0x15a4a34: r5 = LoadInt32Instr(r2)
    //     0x15a4a34: sbfx            x5, x2, #1, #0x1f
    //     0x15a4a38: tbz             w2, #0, #0x15a4a40
    //     0x15a4a3c: ldur            x5, [x2, #7]
    // 0x15a4a40: mov             x7, x5
    // 0x15a4a44: ldur            x6, [fp, #-0xb8]
    // 0x15a4a48: r17 = -304
    //     0x15a4a48: movn            x17, #0x12f
    // 0x15a4a4c: ldr             x5, [fp, x17]
    // 0x15a4a50: r2 = Instance_ColorSpace
    //     0x15a4a50: ldr             x2, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a4a54: d0 = 255.000000
    //     0x15a4a54: ldr             d0, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a4a58: d1 = 1.000000
    //     0x15a4a58: fmov            d1, #1.00000000
    // 0x15a4a5c: stur            x7, [fp, #-0xc8]
    // 0x15a4a60: StoreField: r1->field_27 = r2
    //     0x15a4a60: stur            w2, [x1, #0x27]
    // 0x15a4a64: StoreField: r1->field_7 = d1
    //     0x15a4a64: stur            d1, [x1, #7]
    // 0x15a4a68: ubfx            x3, x3, #0, #0x20
    // 0x15a4a6c: and             w8, w3, #0xff
    // 0x15a4a70: ubfx            x8, x8, #0, #0x20
    // 0x15a4a74: scvtf           d2, x8
    // 0x15a4a78: fdiv            d3, d2, d0
    // 0x15a4a7c: StoreField: r1->field_f = d3
    //     0x15a4a7c: stur            d3, [x1, #0xf]
    // 0x15a4a80: ubfx            x4, x4, #0, #0x20
    // 0x15a4a84: and             w3, w4, #0xff
    // 0x15a4a88: ubfx            x3, x3, #0, #0x20
    // 0x15a4a8c: scvtf           d2, x3
    // 0x15a4a90: fdiv            d3, d2, d0
    // 0x15a4a94: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a4a94: stur            d3, [x1, #0x17]
    // 0x15a4a98: mov             x3, x7
    // 0x15a4a9c: ubfx            x3, x3, #0, #0x20
    // 0x15a4aa0: and             w4, w3, #0xff
    // 0x15a4aa4: ubfx            x4, x4, #0, #0x20
    // 0x15a4aa8: scvtf           d2, x4
    // 0x15a4aac: fdiv            d3, d2, d0
    // 0x15a4ab0: StoreField: r1->field_1f = d3
    //     0x15a4ab0: stur            d3, [x1, #0x1f]
    // 0x15a4ab4: r16 = <Color>
    //     0x15a4ab4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x15a4ab8: ldr             x16, [x16, #0xf80]
    // 0x15a4abc: stp             x1, x16, [SP]
    // 0x15a4ac0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15a4ac0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15a4ac4: r0 = all()
    //     0x15a4ac4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x15a4ac8: mov             x2, x0
    // 0x15a4acc: ldur            x0, [fp, #-0xf0]
    // 0x15a4ad0: r17 = -304
    //     0x15a4ad0: movn            x17, #0x12f
    // 0x15a4ad4: ldr             x1, [fp, x17]
    // 0x15a4ad8: StoreField: r1->field_b = r0
    //     0x15a4ad8: stur            w0, [x1, #0xb]
    //     0x15a4adc: ldurb           w16, [x1, #-1]
    //     0x15a4ae0: ldurb           w17, [x0, #-1]
    //     0x15a4ae4: and             x16, x17, x16, lsr #2
    //     0x15a4ae8: tst             x16, HEAP, lsr #32
    //     0x15a4aec: b.eq            #0x15a4af4
    //     0x15a4af0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x15a4af4: mov             x0, x2
    // 0x15a4af8: StoreField: r1->field_f = r0
    //     0x15a4af8: stur            w0, [x1, #0xf]
    //     0x15a4afc: ldurb           w16, [x1, #-1]
    //     0x15a4b00: ldurb           w17, [x0, #-1]
    //     0x15a4b04: and             x16, x17, x16, lsr #2
    //     0x15a4b08: tst             x16, HEAP, lsr #32
    //     0x15a4b0c: b.eq            #0x15a4b14
    //     0x15a4b10: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x15a4b14: ldur            x0, [fp, #-0xe0]
    // 0x15a4b18: StoreField: r1->field_43 = r0
    //     0x15a4b18: stur            w0, [x1, #0x43]
    //     0x15a4b1c: ldurb           w16, [x1, #-1]
    //     0x15a4b20: ldurb           w17, [x0, #-1]
    //     0x15a4b24: and             x16, x17, x16, lsr #2
    //     0x15a4b28: tst             x16, HEAP, lsr #32
    //     0x15a4b2c: b.eq            #0x15a4b34
    //     0x15a4b30: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x15a4b34: r0 = TextButtonThemeData()
    //     0x15a4b34: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x15a4b38: mov             x1, x0
    // 0x15a4b3c: r17 = -304
    //     0x15a4b3c: movn            x17, #0x12f
    // 0x15a4b40: ldr             x0, [fp, x17]
    // 0x15a4b44: StoreField: r1->field_7 = r0
    //     0x15a4b44: stur            w0, [x1, #7]
    // 0x15a4b48: r16 = Instance_Color
    //     0x15a4b48: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x15a4b4c: ldr             x16, [x16, #0xf88]
    // 0x15a4b50: r30 = Instance_Color
    //     0x15a4b50: add             lr, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x15a4b54: ldr             lr, [lr, #0xf88]
    // 0x15a4b58: stp             lr, x16, [SP, #0x40]
    // 0x15a4b5c: r16 = Instance_Color
    //     0x15a4b5c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x15a4b60: ldr             x16, [x16, #0xf88]
    // 0x15a4b64: ldur            lr, [fp, #-0xd0]
    // 0x15a4b68: stp             lr, x16, [SP, #0x30]
    // 0x15a4b6c: ldur            x16, [fp, #-0xd8]
    // 0x15a4b70: r17 = -272
    //     0x15a4b70: movn            x17, #0x10f
    // 0x15a4b74: ldr             lr, [fp, x17]
    // 0x15a4b78: stp             lr, x16, [SP, #0x20]
    // 0x15a4b7c: r17 = -280
    //     0x15a4b7c: movn            x17, #0x117
    // 0x15a4b80: ldr             x16, [fp, x17]
    // 0x15a4b84: ldur            lr, [fp, #-0xe8]
    // 0x15a4b88: stp             lr, x16, [SP, #0x10]
    // 0x15a4b8c: r17 = -296
    //     0x15a4b8c: movn            x17, #0x127
    // 0x15a4b90: ldr             x16, [fp, x17]
    // 0x15a4b94: stp             x1, x16, [SP]
    // 0x15a4b98: r1 = Null
    //     0x15a4b98: mov             x1, NULL
    // 0x15a4b9c: r4 = const [0, 0xb, 0xa, 0x1, appBarTheme, 0x7, bottomNavigationBarTheme, 0x6, colorScheme, 0x5, highlightColor, 0x2, hoverColor, 0x3, iconTheme, 0x8, primaryColor, 0x4, splashColor, 0x1, textButtonTheme, 0xa, textTheme, 0x9, null]
    //     0x15a4b9c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f90] List(25) [0, 0xb, 0xa, 0x1, "appBarTheme", 0x7, "bottomNavigationBarTheme", 0x6, "colorScheme", 0x5, "highlightColor", 0x2, "hoverColor", 0x3, "iconTheme", 0x8, "primaryColor", 0x4, "splashColor", 0x1, "textButtonTheme", 0xa, "textTheme", 0x9, Null]
    //     0x15a4ba0: ldr             x4, [x4, #0xf90]
    // 0x15a4ba4: r0 = ThemeData()
    //     0x15a4ba4: bl              #0x6b0c60  ; [package:flutter/src/material/theme_data.dart] ThemeData::ThemeData
    // 0x15a4ba8: mov             x3, x0
    // 0x15a4bac: ldur            x0, [fp, #-0xb8]
    // 0x15a4bb0: stur            x3, [fp, #-0xd0]
    // 0x15a4bb4: LoadField: r1 = r0->field_5b
    //     0x15a4bb4: ldur            w1, [x0, #0x5b]
    // 0x15a4bb8: DecompressPointer r1
    //     0x15a4bb8: add             x1, x1, HEAP, lsl #32
    // 0x15a4bbc: mov             x2, x3
    // 0x15a4bc0: r0 = value=()
    //     0x15a4bc0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15a4bc4: b               #0x15a7068
    // 0x15a4bc8: sub             SP, fp, #0x1a0
    // 0x15a4bcc: mov             x4, x0
    // 0x15a4bd0: mov             x3, x1
    // 0x15a4bd4: stur            x0, [fp, #-0xd0]
    // 0x15a4bd8: stur            x1, [fp, #-0xd8]
    // 0x15a4bdc: r2 = Null
    //     0x15a4bdc: mov             x2, NULL
    // 0x15a4be0: r1 = Null
    //     0x15a4be0: mov             x1, NULL
    // 0x15a4be4: cmp             w0, NULL
    // 0x15a4be8: b.eq            #0x15a4c74
    // 0x15a4bec: branchIfSmi(r0, 0x15a4c74)
    //     0x15a4bec: tbz             w0, #0, #0x15a4c74
    // 0x15a4bf0: r3 = LoadClassIdInstr(r0)
    //     0x15a4bf0: ldur            x3, [x0, #-1]
    //     0x15a4bf4: ubfx            x3, x3, #0xc, #0x14
    // 0x15a4bf8: r4 = LoadClassIdInstr(r0)
    //     0x15a4bf8: ldur            x4, [x0, #-1]
    //     0x15a4bfc: ubfx            x4, x4, #0xc, #0x14
    // 0x15a4c00: ldr             x3, [THR, #0x778]  ; THR::isolate_group
    // 0x15a4c04: ldr             x3, [x3, #0x18]
    // 0x15a4c08: ldr             x3, [x3, x4, lsl #3]
    // 0x15a4c0c: LoadField: r3 = r3->field_2b
    //     0x15a4c0c: ldur            w3, [x3, #0x2b]
    // 0x15a4c10: DecompressPointer r3
    //     0x15a4c10: add             x3, x3, HEAP, lsl #32
    // 0x15a4c14: cmp             w3, NULL
    // 0x15a4c18: b.eq            #0x15a4c74
    // 0x15a4c1c: LoadField: r3 = r3->field_f
    //     0x15a4c1c: ldur            w3, [x3, #0xf]
    // 0x15a4c20: lsr             x3, x3, #3
    // 0x15a4c24: r17 = 6746
    //     0x15a4c24: movz            x17, #0x1a5a
    // 0x15a4c28: cmp             x3, x17
    // 0x15a4c2c: b.eq            #0x15a4c7c
    // 0x15a4c30: r3 = SubtypeTestCache
    //     0x15a4c30: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f98] SubtypeTestCache
    //     0x15a4c34: ldr             x3, [x3, #0xf98]
    // 0x15a4c38: r30 = Subtype1TestCacheStub
    //     0x15a4c38: ldr             lr, [PP, #0x398]  ; [pp+0x398] Stub: Subtype1TestCache (0x612f30)
    // 0x15a4c3c: LoadField: r30 = r30->field_7
    //     0x15a4c3c: ldur            lr, [lr, #7]
    // 0x15a4c40: blr             lr
    // 0x15a4c44: cmp             w7, NULL
    // 0x15a4c48: b.eq            #0x15a4c54
    // 0x15a4c4c: tbnz            w7, #4, #0x15a4c74
    // 0x15a4c50: b               #0x15a4c7c
    // 0x15a4c54: r8 = Exception
    //     0x15a4c54: add             x8, PP, #0x12, lsl #12  ; [pp+0x12fa0] Type: Exception
    //     0x15a4c58: ldr             x8, [x8, #0xfa0]
    // 0x15a4c5c: r3 = SubtypeTestCache
    //     0x15a4c5c: add             x3, PP, #0x12, lsl #12  ; [pp+0x12fa8] SubtypeTestCache
    //     0x15a4c60: ldr             x3, [x3, #0xfa8]
    // 0x15a4c64: r30 = InstanceOfStub
    //     0x15a4c64: ldr             lr, [PP, #0x340]  ; [pp+0x340] Stub: InstanceOf (0x60127c)
    // 0x15a4c68: LoadField: r30 = r30->field_7
    //     0x15a4c68: ldur            lr, [lr, #7]
    // 0x15a4c6c: blr             lr
    // 0x15a4c70: b               #0x15a4c80
    // 0x15a4c74: r0 = false
    //     0x15a4c74: add             x0, NULL, #0x30  ; false
    // 0x15a4c78: b               #0x15a4c80
    // 0x15a4c7c: r0 = true
    //     0x15a4c7c: add             x0, NULL, #0x20  ; true
    // 0x15a4c80: tbnz            w0, #4, #0x15a7070
    // 0x15a4c84: ldur            x0, [fp, #-0xc0]
    // 0x15a4c88: LoadField: r1 = r0->field_b
    //     0x15a4c88: ldur            w1, [x0, #0xb]
    // 0x15a4c8c: DecompressPointer r1
    //     0x15a4c8c: add             x1, x1, HEAP, lsl #32
    // 0x15a4c90: cmp             w1, NULL
    // 0x15a4c94: b.ne            #0x15a4ca0
    // 0x15a4c98: r2 = Null
    //     0x15a4c98: mov             x2, NULL
    // 0x15a4c9c: b               #0x15a4cd8
    // 0x15a4ca0: LoadField: r2 = r1->field_57
    //     0x15a4ca0: ldur            w2, [x1, #0x57]
    // 0x15a4ca4: DecompressPointer r2
    //     0x15a4ca4: add             x2, x2, HEAP, lsl #32
    // 0x15a4ca8: cmp             w2, NULL
    // 0x15a4cac: b.ne            #0x15a4cb8
    // 0x15a4cb0: r2 = Null
    //     0x15a4cb0: mov             x2, NULL
    // 0x15a4cb4: b               #0x15a4cd8
    // 0x15a4cb8: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x15a4cb8: ldur            w3, [x2, #0x17]
    // 0x15a4cbc: DecompressPointer r3
    //     0x15a4cbc: add             x3, x3, HEAP, lsl #32
    // 0x15a4cc0: cmp             w3, NULL
    // 0x15a4cc4: b.ne            #0x15a4cd0
    // 0x15a4cc8: r2 = Null
    //     0x15a4cc8: mov             x2, NULL
    // 0x15a4ccc: b               #0x15a4cd8
    // 0x15a4cd0: LoadField: r2 = r3->field_7
    //     0x15a4cd0: ldur            w2, [x3, #7]
    // 0x15a4cd4: DecompressPointer r2
    //     0x15a4cd4: add             x2, x2, HEAP, lsl #32
    // 0x15a4cd8: cmp             w2, NULL
    // 0x15a4cdc: b.ne            #0x15a4ce8
    // 0x15a4ce0: r2 = 0
    //     0x15a4ce0: movz            x2, #0
    // 0x15a4ce4: b               #0x15a4cf8
    // 0x15a4ce8: r3 = LoadInt32Instr(r2)
    //     0x15a4ce8: sbfx            x3, x2, #1, #0x1f
    //     0x15a4cec: tbz             w2, #0, #0x15a4cf4
    //     0x15a4cf0: ldur            x3, [x2, #7]
    // 0x15a4cf4: mov             x2, x3
    // 0x15a4cf8: r17 = -336
    //     0x15a4cf8: movn            x17, #0x14f
    // 0x15a4cfc: str             x2, [fp, x17]
    // 0x15a4d00: cmp             w1, NULL
    // 0x15a4d04: b.ne            #0x15a4d10
    // 0x15a4d08: r3 = Null
    //     0x15a4d08: mov             x3, NULL
    // 0x15a4d0c: b               #0x15a4d48
    // 0x15a4d10: LoadField: r3 = r1->field_57
    //     0x15a4d10: ldur            w3, [x1, #0x57]
    // 0x15a4d14: DecompressPointer r3
    //     0x15a4d14: add             x3, x3, HEAP, lsl #32
    // 0x15a4d18: cmp             w3, NULL
    // 0x15a4d1c: b.ne            #0x15a4d28
    // 0x15a4d20: r3 = Null
    //     0x15a4d20: mov             x3, NULL
    // 0x15a4d24: b               #0x15a4d48
    // 0x15a4d28: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a4d28: ldur            w4, [x3, #0x17]
    // 0x15a4d2c: DecompressPointer r4
    //     0x15a4d2c: add             x4, x4, HEAP, lsl #32
    // 0x15a4d30: cmp             w4, NULL
    // 0x15a4d34: b.ne            #0x15a4d40
    // 0x15a4d38: r3 = Null
    //     0x15a4d38: mov             x3, NULL
    // 0x15a4d3c: b               #0x15a4d48
    // 0x15a4d40: LoadField: r3 = r4->field_b
    //     0x15a4d40: ldur            w3, [x4, #0xb]
    // 0x15a4d44: DecompressPointer r3
    //     0x15a4d44: add             x3, x3, HEAP, lsl #32
    // 0x15a4d48: cmp             w3, NULL
    // 0x15a4d4c: b.ne            #0x15a4d58
    // 0x15a4d50: r3 = 0
    //     0x15a4d50: movz            x3, #0
    // 0x15a4d54: b               #0x15a4d68
    // 0x15a4d58: r4 = LoadInt32Instr(r3)
    //     0x15a4d58: sbfx            x4, x3, #1, #0x1f
    //     0x15a4d5c: tbz             w3, #0, #0x15a4d64
    //     0x15a4d60: ldur            x4, [x3, #7]
    // 0x15a4d64: mov             x3, x4
    // 0x15a4d68: r17 = -328
    //     0x15a4d68: movn            x17, #0x147
    // 0x15a4d6c: str             x3, [fp, x17]
    // 0x15a4d70: cmp             w1, NULL
    // 0x15a4d74: b.ne            #0x15a4d80
    // 0x15a4d78: r1 = Null
    //     0x15a4d78: mov             x1, NULL
    // 0x15a4d7c: b               #0x15a4dbc
    // 0x15a4d80: LoadField: r4 = r1->field_57
    //     0x15a4d80: ldur            w4, [x1, #0x57]
    // 0x15a4d84: DecompressPointer r4
    //     0x15a4d84: add             x4, x4, HEAP, lsl #32
    // 0x15a4d88: cmp             w4, NULL
    // 0x15a4d8c: b.ne            #0x15a4d98
    // 0x15a4d90: r1 = Null
    //     0x15a4d90: mov             x1, NULL
    // 0x15a4d94: b               #0x15a4dbc
    // 0x15a4d98: ArrayLoad: r1 = r4[0]  ; List_4
    //     0x15a4d98: ldur            w1, [x4, #0x17]
    // 0x15a4d9c: DecompressPointer r1
    //     0x15a4d9c: add             x1, x1, HEAP, lsl #32
    // 0x15a4da0: cmp             w1, NULL
    // 0x15a4da4: b.ne            #0x15a4db0
    // 0x15a4da8: r1 = Null
    //     0x15a4da8: mov             x1, NULL
    // 0x15a4dac: b               #0x15a4dbc
    // 0x15a4db0: LoadField: r4 = r1->field_f
    //     0x15a4db0: ldur            w4, [x1, #0xf]
    // 0x15a4db4: DecompressPointer r4
    //     0x15a4db4: add             x4, x4, HEAP, lsl #32
    // 0x15a4db8: mov             x1, x4
    // 0x15a4dbc: cmp             w1, NULL
    // 0x15a4dc0: b.ne            #0x15a4dcc
    // 0x15a4dc4: r1 = 0
    //     0x15a4dc4: movz            x1, #0
    // 0x15a4dc8: b               #0x15a4ddc
    // 0x15a4dcc: r4 = LoadInt32Instr(r1)
    //     0x15a4dcc: sbfx            x4, x1, #1, #0x1f
    //     0x15a4dd0: tbz             w1, #0, #0x15a4dd8
    //     0x15a4dd4: ldur            x4, [x1, #7]
    // 0x15a4dd8: mov             x1, x4
    // 0x15a4ddc: stur            x1, [fp, #-0xc8]
    // 0x15a4de0: r0 = Color()
    //     0x15a4de0: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a4de4: mov             x2, x0
    // 0x15a4de8: r0 = Instance_ColorSpace
    //     0x15a4de8: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a4dec: stur            x2, [fp, #-0xe0]
    // 0x15a4df0: StoreField: r2->field_27 = r0
    //     0x15a4df0: stur            w0, [x2, #0x27]
    // 0x15a4df4: d0 = 1.000000
    //     0x15a4df4: fmov            d0, #1.00000000
    // 0x15a4df8: StoreField: r2->field_7 = d0
    //     0x15a4df8: stur            d0, [x2, #7]
    // 0x15a4dfc: r17 = -336
    //     0x15a4dfc: movn            x17, #0x14f
    // 0x15a4e00: ldr             x1, [fp, x17]
    // 0x15a4e04: ubfx            x1, x1, #0, #0x20
    // 0x15a4e08: and             w3, w1, #0xff
    // 0x15a4e0c: ubfx            x3, x3, #0, #0x20
    // 0x15a4e10: scvtf           d1, x3
    // 0x15a4e14: d2 = 255.000000
    //     0x15a4e14: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a4e18: fdiv            d3, d1, d2
    // 0x15a4e1c: StoreField: r2->field_f = d3
    //     0x15a4e1c: stur            d3, [x2, #0xf]
    // 0x15a4e20: r17 = -328
    //     0x15a4e20: movn            x17, #0x147
    // 0x15a4e24: ldr             x1, [fp, x17]
    // 0x15a4e28: ubfx            x1, x1, #0, #0x20
    // 0x15a4e2c: and             w3, w1, #0xff
    // 0x15a4e30: ubfx            x3, x3, #0, #0x20
    // 0x15a4e34: scvtf           d1, x3
    // 0x15a4e38: fdiv            d3, d1, d2
    // 0x15a4e3c: ArrayStore: r2[0] = d3  ; List_8
    //     0x15a4e3c: stur            d3, [x2, #0x17]
    // 0x15a4e40: ldur            x1, [fp, #-0xc8]
    // 0x15a4e44: ubfx            x1, x1, #0, #0x20
    // 0x15a4e48: and             w3, w1, #0xff
    // 0x15a4e4c: ubfx            x3, x3, #0, #0x20
    // 0x15a4e50: scvtf           d1, x3
    // 0x15a4e54: fdiv            d3, d1, d2
    // 0x15a4e58: StoreField: r2->field_1f = d3
    //     0x15a4e58: stur            d3, [x2, #0x1f]
    // 0x15a4e5c: r1 = Null
    //     0x15a4e5c: mov             x1, NULL
    // 0x15a4e60: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15a4e60: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15a4e64: r0 = ColorScheme.fromSwatch()
    //     0x15a4e64: bl              #0x6b3aa4  ; [package:flutter/src/material/color_scheme.dart] ColorScheme::ColorScheme.fromSwatch
    // 0x15a4e68: mov             x1, x0
    // 0x15a4e6c: ldur            x0, [fp, #-0xc0]
    // 0x15a4e70: stur            x1, [fp, #-0xf0]
    // 0x15a4e74: LoadField: r2 = r0->field_b
    //     0x15a4e74: ldur            w2, [x0, #0xb]
    // 0x15a4e78: DecompressPointer r2
    //     0x15a4e78: add             x2, x2, HEAP, lsl #32
    // 0x15a4e7c: stur            x2, [fp, #-0xe8]
    // 0x15a4e80: cmp             w2, NULL
    // 0x15a4e84: b.ne            #0x15a4e90
    // 0x15a4e88: r3 = Null
    //     0x15a4e88: mov             x3, NULL
    // 0x15a4e8c: b               #0x15a4ec8
    // 0x15a4e90: LoadField: r3 = r2->field_57
    //     0x15a4e90: ldur            w3, [x2, #0x57]
    // 0x15a4e94: DecompressPointer r3
    //     0x15a4e94: add             x3, x3, HEAP, lsl #32
    // 0x15a4e98: cmp             w3, NULL
    // 0x15a4e9c: b.ne            #0x15a4ea8
    // 0x15a4ea0: r3 = Null
    //     0x15a4ea0: mov             x3, NULL
    // 0x15a4ea4: b               #0x15a4ec8
    // 0x15a4ea8: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a4ea8: ldur            w4, [x3, #0x17]
    // 0x15a4eac: DecompressPointer r4
    //     0x15a4eac: add             x4, x4, HEAP, lsl #32
    // 0x15a4eb0: cmp             w4, NULL
    // 0x15a4eb4: b.ne            #0x15a4ec0
    // 0x15a4eb8: r3 = Null
    //     0x15a4eb8: mov             x3, NULL
    // 0x15a4ebc: b               #0x15a4ec8
    // 0x15a4ec0: LoadField: r3 = r4->field_7
    //     0x15a4ec0: ldur            w3, [x4, #7]
    // 0x15a4ec4: DecompressPointer r3
    //     0x15a4ec4: add             x3, x3, HEAP, lsl #32
    // 0x15a4ec8: cmp             w3, NULL
    // 0x15a4ecc: b.ne            #0x15a4ed8
    // 0x15a4ed0: r3 = 0
    //     0x15a4ed0: movz            x3, #0
    // 0x15a4ed4: b               #0x15a4ee8
    // 0x15a4ed8: r4 = LoadInt32Instr(r3)
    //     0x15a4ed8: sbfx            x4, x3, #1, #0x1f
    //     0x15a4edc: tbz             w3, #0, #0x15a4ee4
    //     0x15a4ee0: ldur            x4, [x3, #7]
    // 0x15a4ee4: mov             x3, x4
    // 0x15a4ee8: r17 = -336
    //     0x15a4ee8: movn            x17, #0x14f
    // 0x15a4eec: str             x3, [fp, x17]
    // 0x15a4ef0: cmp             w2, NULL
    // 0x15a4ef4: b.ne            #0x15a4f00
    // 0x15a4ef8: r4 = Null
    //     0x15a4ef8: mov             x4, NULL
    // 0x15a4efc: b               #0x15a4f38
    // 0x15a4f00: LoadField: r4 = r2->field_57
    //     0x15a4f00: ldur            w4, [x2, #0x57]
    // 0x15a4f04: DecompressPointer r4
    //     0x15a4f04: add             x4, x4, HEAP, lsl #32
    // 0x15a4f08: cmp             w4, NULL
    // 0x15a4f0c: b.ne            #0x15a4f18
    // 0x15a4f10: r4 = Null
    //     0x15a4f10: mov             x4, NULL
    // 0x15a4f14: b               #0x15a4f38
    // 0x15a4f18: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a4f18: ldur            w5, [x4, #0x17]
    // 0x15a4f1c: DecompressPointer r5
    //     0x15a4f1c: add             x5, x5, HEAP, lsl #32
    // 0x15a4f20: cmp             w5, NULL
    // 0x15a4f24: b.ne            #0x15a4f30
    // 0x15a4f28: r4 = Null
    //     0x15a4f28: mov             x4, NULL
    // 0x15a4f2c: b               #0x15a4f38
    // 0x15a4f30: LoadField: r4 = r5->field_b
    //     0x15a4f30: ldur            w4, [x5, #0xb]
    // 0x15a4f34: DecompressPointer r4
    //     0x15a4f34: add             x4, x4, HEAP, lsl #32
    // 0x15a4f38: cmp             w4, NULL
    // 0x15a4f3c: b.ne            #0x15a4f48
    // 0x15a4f40: r4 = 0
    //     0x15a4f40: movz            x4, #0
    // 0x15a4f44: b               #0x15a4f58
    // 0x15a4f48: r5 = LoadInt32Instr(r4)
    //     0x15a4f48: sbfx            x5, x4, #1, #0x1f
    //     0x15a4f4c: tbz             w4, #0, #0x15a4f54
    //     0x15a4f50: ldur            x5, [x4, #7]
    // 0x15a4f54: mov             x4, x5
    // 0x15a4f58: r17 = -328
    //     0x15a4f58: movn            x17, #0x147
    // 0x15a4f5c: str             x4, [fp, x17]
    // 0x15a4f60: cmp             w2, NULL
    // 0x15a4f64: b.ne            #0x15a4f70
    // 0x15a4f68: r5 = Null
    //     0x15a4f68: mov             x5, NULL
    // 0x15a4f6c: b               #0x15a4fa8
    // 0x15a4f70: LoadField: r5 = r2->field_57
    //     0x15a4f70: ldur            w5, [x2, #0x57]
    // 0x15a4f74: DecompressPointer r5
    //     0x15a4f74: add             x5, x5, HEAP, lsl #32
    // 0x15a4f78: cmp             w5, NULL
    // 0x15a4f7c: b.ne            #0x15a4f88
    // 0x15a4f80: r5 = Null
    //     0x15a4f80: mov             x5, NULL
    // 0x15a4f84: b               #0x15a4fa8
    // 0x15a4f88: ArrayLoad: r6 = r5[0]  ; List_4
    //     0x15a4f88: ldur            w6, [x5, #0x17]
    // 0x15a4f8c: DecompressPointer r6
    //     0x15a4f8c: add             x6, x6, HEAP, lsl #32
    // 0x15a4f90: cmp             w6, NULL
    // 0x15a4f94: b.ne            #0x15a4fa0
    // 0x15a4f98: r5 = Null
    //     0x15a4f98: mov             x5, NULL
    // 0x15a4f9c: b               #0x15a4fa8
    // 0x15a4fa0: LoadField: r5 = r6->field_f
    //     0x15a4fa0: ldur            w5, [x6, #0xf]
    // 0x15a4fa4: DecompressPointer r5
    //     0x15a4fa4: add             x5, x5, HEAP, lsl #32
    // 0x15a4fa8: cmp             w5, NULL
    // 0x15a4fac: b.ne            #0x15a4fb8
    // 0x15a4fb0: r5 = 0
    //     0x15a4fb0: movz            x5, #0
    // 0x15a4fb4: b               #0x15a4fc8
    // 0x15a4fb8: r6 = LoadInt32Instr(r5)
    //     0x15a4fb8: sbfx            x6, x5, #1, #0x1f
    //     0x15a4fbc: tbz             w5, #0, #0x15a4fc4
    //     0x15a4fc0: ldur            x6, [x5, #7]
    // 0x15a4fc4: mov             x5, x6
    // 0x15a4fc8: stur            x5, [fp, #-0xc8]
    // 0x15a4fcc: r0 = Color()
    //     0x15a4fcc: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a4fd0: mov             x1, x0
    // 0x15a4fd4: r0 = Instance_ColorSpace
    //     0x15a4fd4: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a4fd8: stur            x1, [fp, #-0xf8]
    // 0x15a4fdc: StoreField: r1->field_27 = r0
    //     0x15a4fdc: stur            w0, [x1, #0x27]
    // 0x15a4fe0: d0 = 1.000000
    //     0x15a4fe0: fmov            d0, #1.00000000
    // 0x15a4fe4: StoreField: r1->field_7 = d0
    //     0x15a4fe4: stur            d0, [x1, #7]
    // 0x15a4fe8: r17 = -336
    //     0x15a4fe8: movn            x17, #0x14f
    // 0x15a4fec: ldr             x2, [fp, x17]
    // 0x15a4ff0: ubfx            x2, x2, #0, #0x20
    // 0x15a4ff4: and             w3, w2, #0xff
    // 0x15a4ff8: ubfx            x3, x3, #0, #0x20
    // 0x15a4ffc: scvtf           d1, x3
    // 0x15a5000: d2 = 255.000000
    //     0x15a5000: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a5004: fdiv            d3, d1, d2
    // 0x15a5008: StoreField: r1->field_f = d3
    //     0x15a5008: stur            d3, [x1, #0xf]
    // 0x15a500c: r17 = -328
    //     0x15a500c: movn            x17, #0x147
    // 0x15a5010: ldr             x2, [fp, x17]
    // 0x15a5014: ubfx            x2, x2, #0, #0x20
    // 0x15a5018: and             w3, w2, #0xff
    // 0x15a501c: ubfx            x3, x3, #0, #0x20
    // 0x15a5020: scvtf           d1, x3
    // 0x15a5024: fdiv            d3, d1, d2
    // 0x15a5028: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a5028: stur            d3, [x1, #0x17]
    // 0x15a502c: ldur            x2, [fp, #-0xc8]
    // 0x15a5030: ubfx            x2, x2, #0, #0x20
    // 0x15a5034: and             w3, w2, #0xff
    // 0x15a5038: ubfx            x3, x3, #0, #0x20
    // 0x15a503c: scvtf           d1, x3
    // 0x15a5040: fdiv            d3, d1, d2
    // 0x15a5044: StoreField: r1->field_1f = d3
    //     0x15a5044: stur            d3, [x1, #0x1f]
    // 0x15a5048: ldur            x2, [fp, #-0xe8]
    // 0x15a504c: cmp             w2, NULL
    // 0x15a5050: b.ne            #0x15a505c
    // 0x15a5054: r3 = Null
    //     0x15a5054: mov             x3, NULL
    // 0x15a5058: b               #0x15a5094
    // 0x15a505c: LoadField: r3 = r2->field_57
    //     0x15a505c: ldur            w3, [x2, #0x57]
    // 0x15a5060: DecompressPointer r3
    //     0x15a5060: add             x3, x3, HEAP, lsl #32
    // 0x15a5064: cmp             w3, NULL
    // 0x15a5068: b.ne            #0x15a5074
    // 0x15a506c: r3 = Null
    //     0x15a506c: mov             x3, NULL
    // 0x15a5070: b               #0x15a5094
    // 0x15a5074: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a5074: ldur            w4, [x3, #0x17]
    // 0x15a5078: DecompressPointer r4
    //     0x15a5078: add             x4, x4, HEAP, lsl #32
    // 0x15a507c: cmp             w4, NULL
    // 0x15a5080: b.ne            #0x15a508c
    // 0x15a5084: r3 = Null
    //     0x15a5084: mov             x3, NULL
    // 0x15a5088: b               #0x15a5094
    // 0x15a508c: LoadField: r3 = r4->field_7
    //     0x15a508c: ldur            w3, [x4, #7]
    // 0x15a5090: DecompressPointer r3
    //     0x15a5090: add             x3, x3, HEAP, lsl #32
    // 0x15a5094: cmp             w3, NULL
    // 0x15a5098: b.ne            #0x15a50a4
    // 0x15a509c: r3 = 0
    //     0x15a509c: movz            x3, #0
    // 0x15a50a0: b               #0x15a50b4
    // 0x15a50a4: r4 = LoadInt32Instr(r3)
    //     0x15a50a4: sbfx            x4, x3, #1, #0x1f
    //     0x15a50a8: tbz             w3, #0, #0x15a50b0
    //     0x15a50ac: ldur            x4, [x3, #7]
    // 0x15a50b0: mov             x3, x4
    // 0x15a50b4: r17 = -336
    //     0x15a50b4: movn            x17, #0x14f
    // 0x15a50b8: str             x3, [fp, x17]
    // 0x15a50bc: cmp             w2, NULL
    // 0x15a50c0: b.ne            #0x15a50cc
    // 0x15a50c4: r4 = Null
    //     0x15a50c4: mov             x4, NULL
    // 0x15a50c8: b               #0x15a5104
    // 0x15a50cc: LoadField: r4 = r2->field_57
    //     0x15a50cc: ldur            w4, [x2, #0x57]
    // 0x15a50d0: DecompressPointer r4
    //     0x15a50d0: add             x4, x4, HEAP, lsl #32
    // 0x15a50d4: cmp             w4, NULL
    // 0x15a50d8: b.ne            #0x15a50e4
    // 0x15a50dc: r4 = Null
    //     0x15a50dc: mov             x4, NULL
    // 0x15a50e0: b               #0x15a5104
    // 0x15a50e4: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a50e4: ldur            w5, [x4, #0x17]
    // 0x15a50e8: DecompressPointer r5
    //     0x15a50e8: add             x5, x5, HEAP, lsl #32
    // 0x15a50ec: cmp             w5, NULL
    // 0x15a50f0: b.ne            #0x15a50fc
    // 0x15a50f4: r4 = Null
    //     0x15a50f4: mov             x4, NULL
    // 0x15a50f8: b               #0x15a5104
    // 0x15a50fc: LoadField: r4 = r5->field_b
    //     0x15a50fc: ldur            w4, [x5, #0xb]
    // 0x15a5100: DecompressPointer r4
    //     0x15a5100: add             x4, x4, HEAP, lsl #32
    // 0x15a5104: cmp             w4, NULL
    // 0x15a5108: b.ne            #0x15a5114
    // 0x15a510c: r4 = 0
    //     0x15a510c: movz            x4, #0
    // 0x15a5110: b               #0x15a5124
    // 0x15a5114: r5 = LoadInt32Instr(r4)
    //     0x15a5114: sbfx            x5, x4, #1, #0x1f
    //     0x15a5118: tbz             w4, #0, #0x15a5120
    //     0x15a511c: ldur            x5, [x4, #7]
    // 0x15a5120: mov             x4, x5
    // 0x15a5124: r17 = -328
    //     0x15a5124: movn            x17, #0x147
    // 0x15a5128: str             x4, [fp, x17]
    // 0x15a512c: cmp             w2, NULL
    // 0x15a5130: b.ne            #0x15a513c
    // 0x15a5134: r2 = Null
    //     0x15a5134: mov             x2, NULL
    // 0x15a5138: b               #0x15a5178
    // 0x15a513c: LoadField: r5 = r2->field_57
    //     0x15a513c: ldur            w5, [x2, #0x57]
    // 0x15a5140: DecompressPointer r5
    //     0x15a5140: add             x5, x5, HEAP, lsl #32
    // 0x15a5144: cmp             w5, NULL
    // 0x15a5148: b.ne            #0x15a5154
    // 0x15a514c: r2 = Null
    //     0x15a514c: mov             x2, NULL
    // 0x15a5150: b               #0x15a5178
    // 0x15a5154: ArrayLoad: r2 = r5[0]  ; List_4
    //     0x15a5154: ldur            w2, [x5, #0x17]
    // 0x15a5158: DecompressPointer r2
    //     0x15a5158: add             x2, x2, HEAP, lsl #32
    // 0x15a515c: cmp             w2, NULL
    // 0x15a5160: b.ne            #0x15a516c
    // 0x15a5164: r2 = Null
    //     0x15a5164: mov             x2, NULL
    // 0x15a5168: b               #0x15a5178
    // 0x15a516c: LoadField: r5 = r2->field_f
    //     0x15a516c: ldur            w5, [x2, #0xf]
    // 0x15a5170: DecompressPointer r5
    //     0x15a5170: add             x5, x5, HEAP, lsl #32
    // 0x15a5174: mov             x2, x5
    // 0x15a5178: cmp             w2, NULL
    // 0x15a517c: b.ne            #0x15a5188
    // 0x15a5180: r5 = 0
    //     0x15a5180: movz            x5, #0
    // 0x15a5184: b               #0x15a5194
    // 0x15a5188: r5 = LoadInt32Instr(r2)
    //     0x15a5188: sbfx            x5, x2, #1, #0x1f
    //     0x15a518c: tbz             w2, #0, #0x15a5194
    //     0x15a5190: ldur            x5, [x2, #7]
    // 0x15a5194: ldur            x2, [fp, #-0xc0]
    // 0x15a5198: stur            x5, [fp, #-0xc8]
    // 0x15a519c: r0 = Color()
    //     0x15a519c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a51a0: mov             x1, x0
    // 0x15a51a4: r0 = Instance_ColorSpace
    //     0x15a51a4: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a51a8: StoreField: r1->field_27 = r0
    //     0x15a51a8: stur            w0, [x1, #0x27]
    // 0x15a51ac: d0 = 1.000000
    //     0x15a51ac: fmov            d0, #1.00000000
    // 0x15a51b0: StoreField: r1->field_7 = d0
    //     0x15a51b0: stur            d0, [x1, #7]
    // 0x15a51b4: r17 = -336
    //     0x15a51b4: movn            x17, #0x14f
    // 0x15a51b8: ldr             x2, [fp, x17]
    // 0x15a51bc: ubfx            x2, x2, #0, #0x20
    // 0x15a51c0: and             w3, w2, #0xff
    // 0x15a51c4: ubfx            x3, x3, #0, #0x20
    // 0x15a51c8: scvtf           d1, x3
    // 0x15a51cc: d2 = 255.000000
    //     0x15a51cc: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a51d0: fdiv            d3, d1, d2
    // 0x15a51d4: StoreField: r1->field_f = d3
    //     0x15a51d4: stur            d3, [x1, #0xf]
    // 0x15a51d8: r17 = -328
    //     0x15a51d8: movn            x17, #0x147
    // 0x15a51dc: ldr             x2, [fp, x17]
    // 0x15a51e0: ubfx            x2, x2, #0, #0x20
    // 0x15a51e4: and             w3, w2, #0xff
    // 0x15a51e8: ubfx            x3, x3, #0, #0x20
    // 0x15a51ec: scvtf           d1, x3
    // 0x15a51f0: fdiv            d3, d1, d2
    // 0x15a51f4: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a51f4: stur            d3, [x1, #0x17]
    // 0x15a51f8: ldur            x2, [fp, #-0xc8]
    // 0x15a51fc: ubfx            x2, x2, #0, #0x20
    // 0x15a5200: and             w3, w2, #0xff
    // 0x15a5204: ubfx            x3, x3, #0, #0x20
    // 0x15a5208: scvtf           d1, x3
    // 0x15a520c: fdiv            d3, d1, d2
    // 0x15a5210: StoreField: r1->field_1f = d3
    //     0x15a5210: stur            d3, [x1, #0x1f]
    // 0x15a5214: ldur            x16, [fp, #-0xf8]
    // 0x15a5218: stp             x1, x16, [SP]
    // 0x15a521c: ldur            x1, [fp, #-0xf0]
    // 0x15a5220: r4 = const [0, 0x3, 0x2, 0x1, primary, 0x1, secondary, 0x2, null]
    //     0x15a5220: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f38] List(9) [0, 0x3, 0x2, 0x1, "primary", 0x1, "secondary", 0x2, Null]
    //     0x15a5224: ldr             x4, [x4, #0xf38]
    // 0x15a5228: r0 = copyWith()
    //     0x15a5228: bl              #0x6aebac  ; [package:flutter/src/material/color_scheme.dart] ColorScheme::copyWith
    // 0x15a522c: mov             x1, x0
    // 0x15a5230: ldur            x0, [fp, #-0xc0]
    // 0x15a5234: stur            x1, [fp, #-0xf0]
    // 0x15a5238: LoadField: r2 = r0->field_b
    //     0x15a5238: ldur            w2, [x0, #0xb]
    // 0x15a523c: DecompressPointer r2
    //     0x15a523c: add             x2, x2, HEAP, lsl #32
    // 0x15a5240: stur            x2, [fp, #-0xe8]
    // 0x15a5244: cmp             w2, NULL
    // 0x15a5248: b.ne            #0x15a5254
    // 0x15a524c: r3 = Null
    //     0x15a524c: mov             x3, NULL
    // 0x15a5250: b               #0x15a528c
    // 0x15a5254: LoadField: r3 = r2->field_57
    //     0x15a5254: ldur            w3, [x2, #0x57]
    // 0x15a5258: DecompressPointer r3
    //     0x15a5258: add             x3, x3, HEAP, lsl #32
    // 0x15a525c: cmp             w3, NULL
    // 0x15a5260: b.ne            #0x15a526c
    // 0x15a5264: r3 = Null
    //     0x15a5264: mov             x3, NULL
    // 0x15a5268: b               #0x15a528c
    // 0x15a526c: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a526c: ldur            w4, [x3, #0x17]
    // 0x15a5270: DecompressPointer r4
    //     0x15a5270: add             x4, x4, HEAP, lsl #32
    // 0x15a5274: cmp             w4, NULL
    // 0x15a5278: b.ne            #0x15a5284
    // 0x15a527c: r3 = Null
    //     0x15a527c: mov             x3, NULL
    // 0x15a5280: b               #0x15a528c
    // 0x15a5284: LoadField: r3 = r4->field_7
    //     0x15a5284: ldur            w3, [x4, #7]
    // 0x15a5288: DecompressPointer r3
    //     0x15a5288: add             x3, x3, HEAP, lsl #32
    // 0x15a528c: cmp             w3, NULL
    // 0x15a5290: b.ne            #0x15a529c
    // 0x15a5294: r3 = 0
    //     0x15a5294: movz            x3, #0
    // 0x15a5298: b               #0x15a52ac
    // 0x15a529c: r4 = LoadInt32Instr(r3)
    //     0x15a529c: sbfx            x4, x3, #1, #0x1f
    //     0x15a52a0: tbz             w3, #0, #0x15a52a8
    //     0x15a52a4: ldur            x4, [x3, #7]
    // 0x15a52a8: mov             x3, x4
    // 0x15a52ac: r17 = -336
    //     0x15a52ac: movn            x17, #0x14f
    // 0x15a52b0: str             x3, [fp, x17]
    // 0x15a52b4: cmp             w2, NULL
    // 0x15a52b8: b.ne            #0x15a52c4
    // 0x15a52bc: r4 = Null
    //     0x15a52bc: mov             x4, NULL
    // 0x15a52c0: b               #0x15a52fc
    // 0x15a52c4: LoadField: r4 = r2->field_57
    //     0x15a52c4: ldur            w4, [x2, #0x57]
    // 0x15a52c8: DecompressPointer r4
    //     0x15a52c8: add             x4, x4, HEAP, lsl #32
    // 0x15a52cc: cmp             w4, NULL
    // 0x15a52d0: b.ne            #0x15a52dc
    // 0x15a52d4: r4 = Null
    //     0x15a52d4: mov             x4, NULL
    // 0x15a52d8: b               #0x15a52fc
    // 0x15a52dc: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a52dc: ldur            w5, [x4, #0x17]
    // 0x15a52e0: DecompressPointer r5
    //     0x15a52e0: add             x5, x5, HEAP, lsl #32
    // 0x15a52e4: cmp             w5, NULL
    // 0x15a52e8: b.ne            #0x15a52f4
    // 0x15a52ec: r4 = Null
    //     0x15a52ec: mov             x4, NULL
    // 0x15a52f0: b               #0x15a52fc
    // 0x15a52f4: LoadField: r4 = r5->field_b
    //     0x15a52f4: ldur            w4, [x5, #0xb]
    // 0x15a52f8: DecompressPointer r4
    //     0x15a52f8: add             x4, x4, HEAP, lsl #32
    // 0x15a52fc: cmp             w4, NULL
    // 0x15a5300: b.ne            #0x15a530c
    // 0x15a5304: r4 = 0
    //     0x15a5304: movz            x4, #0
    // 0x15a5308: b               #0x15a531c
    // 0x15a530c: r5 = LoadInt32Instr(r4)
    //     0x15a530c: sbfx            x5, x4, #1, #0x1f
    //     0x15a5310: tbz             w4, #0, #0x15a5318
    //     0x15a5314: ldur            x5, [x4, #7]
    // 0x15a5318: mov             x4, x5
    // 0x15a531c: r17 = -328
    //     0x15a531c: movn            x17, #0x147
    // 0x15a5320: str             x4, [fp, x17]
    // 0x15a5324: cmp             w2, NULL
    // 0x15a5328: b.ne            #0x15a5334
    // 0x15a532c: r5 = Null
    //     0x15a532c: mov             x5, NULL
    // 0x15a5330: b               #0x15a536c
    // 0x15a5334: LoadField: r5 = r2->field_57
    //     0x15a5334: ldur            w5, [x2, #0x57]
    // 0x15a5338: DecompressPointer r5
    //     0x15a5338: add             x5, x5, HEAP, lsl #32
    // 0x15a533c: cmp             w5, NULL
    // 0x15a5340: b.ne            #0x15a534c
    // 0x15a5344: r5 = Null
    //     0x15a5344: mov             x5, NULL
    // 0x15a5348: b               #0x15a536c
    // 0x15a534c: ArrayLoad: r6 = r5[0]  ; List_4
    //     0x15a534c: ldur            w6, [x5, #0x17]
    // 0x15a5350: DecompressPointer r6
    //     0x15a5350: add             x6, x6, HEAP, lsl #32
    // 0x15a5354: cmp             w6, NULL
    // 0x15a5358: b.ne            #0x15a5364
    // 0x15a535c: r5 = Null
    //     0x15a535c: mov             x5, NULL
    // 0x15a5360: b               #0x15a536c
    // 0x15a5364: LoadField: r5 = r6->field_f
    //     0x15a5364: ldur            w5, [x6, #0xf]
    // 0x15a5368: DecompressPointer r5
    //     0x15a5368: add             x5, x5, HEAP, lsl #32
    // 0x15a536c: cmp             w5, NULL
    // 0x15a5370: b.ne            #0x15a537c
    // 0x15a5374: r5 = 0
    //     0x15a5374: movz            x5, #0
    // 0x15a5378: b               #0x15a538c
    // 0x15a537c: r6 = LoadInt32Instr(r5)
    //     0x15a537c: sbfx            x6, x5, #1, #0x1f
    //     0x15a5380: tbz             w5, #0, #0x15a5388
    //     0x15a5384: ldur            x6, [x5, #7]
    // 0x15a5388: mov             x5, x6
    // 0x15a538c: stur            x5, [fp, #-0xc8]
    // 0x15a5390: r0 = Color()
    //     0x15a5390: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a5394: mov             x1, x0
    // 0x15a5398: r0 = Instance_ColorSpace
    //     0x15a5398: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a539c: stur            x1, [fp, #-0xf8]
    // 0x15a53a0: StoreField: r1->field_27 = r0
    //     0x15a53a0: stur            w0, [x1, #0x27]
    // 0x15a53a4: d0 = 1.000000
    //     0x15a53a4: fmov            d0, #1.00000000
    // 0x15a53a8: StoreField: r1->field_7 = d0
    //     0x15a53a8: stur            d0, [x1, #7]
    // 0x15a53ac: r17 = -336
    //     0x15a53ac: movn            x17, #0x14f
    // 0x15a53b0: ldr             x2, [fp, x17]
    // 0x15a53b4: ubfx            x2, x2, #0, #0x20
    // 0x15a53b8: and             w3, w2, #0xff
    // 0x15a53bc: ubfx            x3, x3, #0, #0x20
    // 0x15a53c0: scvtf           d1, x3
    // 0x15a53c4: d2 = 255.000000
    //     0x15a53c4: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a53c8: fdiv            d3, d1, d2
    // 0x15a53cc: StoreField: r1->field_f = d3
    //     0x15a53cc: stur            d3, [x1, #0xf]
    // 0x15a53d0: r17 = -328
    //     0x15a53d0: movn            x17, #0x147
    // 0x15a53d4: ldr             x2, [fp, x17]
    // 0x15a53d8: ubfx            x2, x2, #0, #0x20
    // 0x15a53dc: and             w3, w2, #0xff
    // 0x15a53e0: ubfx            x3, x3, #0, #0x20
    // 0x15a53e4: scvtf           d1, x3
    // 0x15a53e8: fdiv            d3, d1, d2
    // 0x15a53ec: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a53ec: stur            d3, [x1, #0x17]
    // 0x15a53f0: ldur            x2, [fp, #-0xc8]
    // 0x15a53f4: ubfx            x2, x2, #0, #0x20
    // 0x15a53f8: and             w3, w2, #0xff
    // 0x15a53fc: ubfx            x3, x3, #0, #0x20
    // 0x15a5400: scvtf           d1, x3
    // 0x15a5404: fdiv            d3, d1, d2
    // 0x15a5408: StoreField: r1->field_1f = d3
    //     0x15a5408: stur            d3, [x1, #0x1f]
    // 0x15a540c: r0 = IconThemeData()
    //     0x15a540c: bl              #0x6b2d30  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0x15a5410: mov             x1, x0
    // 0x15a5414: ldur            x0, [fp, #-0xf8]
    // 0x15a5418: stur            x1, [fp, #-0x100]
    // 0x15a541c: StoreField: r1->field_1b = r0
    //     0x15a541c: stur            w0, [x1, #0x1b]
    // 0x15a5420: ldur            x0, [fp, #-0xe8]
    // 0x15a5424: cmp             w0, NULL
    // 0x15a5428: b.ne            #0x15a5434
    // 0x15a542c: r2 = Null
    //     0x15a542c: mov             x2, NULL
    // 0x15a5430: b               #0x15a546c
    // 0x15a5434: LoadField: r2 = r0->field_57
    //     0x15a5434: ldur            w2, [x0, #0x57]
    // 0x15a5438: DecompressPointer r2
    //     0x15a5438: add             x2, x2, HEAP, lsl #32
    // 0x15a543c: cmp             w2, NULL
    // 0x15a5440: b.ne            #0x15a544c
    // 0x15a5444: r2 = Null
    //     0x15a5444: mov             x2, NULL
    // 0x15a5448: b               #0x15a546c
    // 0x15a544c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x15a544c: ldur            w3, [x2, #0x17]
    // 0x15a5450: DecompressPointer r3
    //     0x15a5450: add             x3, x3, HEAP, lsl #32
    // 0x15a5454: cmp             w3, NULL
    // 0x15a5458: b.ne            #0x15a5464
    // 0x15a545c: r2 = Null
    //     0x15a545c: mov             x2, NULL
    // 0x15a5460: b               #0x15a546c
    // 0x15a5464: LoadField: r2 = r3->field_7
    //     0x15a5464: ldur            w2, [x3, #7]
    // 0x15a5468: DecompressPointer r2
    //     0x15a5468: add             x2, x2, HEAP, lsl #32
    // 0x15a546c: cmp             w2, NULL
    // 0x15a5470: b.ne            #0x15a547c
    // 0x15a5474: r2 = 0
    //     0x15a5474: movz            x2, #0
    // 0x15a5478: b               #0x15a548c
    // 0x15a547c: r3 = LoadInt32Instr(r2)
    //     0x15a547c: sbfx            x3, x2, #1, #0x1f
    //     0x15a5480: tbz             w2, #0, #0x15a5488
    //     0x15a5484: ldur            x3, [x2, #7]
    // 0x15a5488: mov             x2, x3
    // 0x15a548c: r17 = -336
    //     0x15a548c: movn            x17, #0x14f
    // 0x15a5490: str             x2, [fp, x17]
    // 0x15a5494: cmp             w0, NULL
    // 0x15a5498: b.ne            #0x15a54a4
    // 0x15a549c: r3 = Null
    //     0x15a549c: mov             x3, NULL
    // 0x15a54a0: b               #0x15a54dc
    // 0x15a54a4: LoadField: r3 = r0->field_57
    //     0x15a54a4: ldur            w3, [x0, #0x57]
    // 0x15a54a8: DecompressPointer r3
    //     0x15a54a8: add             x3, x3, HEAP, lsl #32
    // 0x15a54ac: cmp             w3, NULL
    // 0x15a54b0: b.ne            #0x15a54bc
    // 0x15a54b4: r3 = Null
    //     0x15a54b4: mov             x3, NULL
    // 0x15a54b8: b               #0x15a54dc
    // 0x15a54bc: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a54bc: ldur            w4, [x3, #0x17]
    // 0x15a54c0: DecompressPointer r4
    //     0x15a54c0: add             x4, x4, HEAP, lsl #32
    // 0x15a54c4: cmp             w4, NULL
    // 0x15a54c8: b.ne            #0x15a54d4
    // 0x15a54cc: r3 = Null
    //     0x15a54cc: mov             x3, NULL
    // 0x15a54d0: b               #0x15a54dc
    // 0x15a54d4: LoadField: r3 = r4->field_b
    //     0x15a54d4: ldur            w3, [x4, #0xb]
    // 0x15a54d8: DecompressPointer r3
    //     0x15a54d8: add             x3, x3, HEAP, lsl #32
    // 0x15a54dc: cmp             w3, NULL
    // 0x15a54e0: b.ne            #0x15a54ec
    // 0x15a54e4: r3 = 0
    //     0x15a54e4: movz            x3, #0
    // 0x15a54e8: b               #0x15a54fc
    // 0x15a54ec: r4 = LoadInt32Instr(r3)
    //     0x15a54ec: sbfx            x4, x3, #1, #0x1f
    //     0x15a54f0: tbz             w3, #0, #0x15a54f8
    //     0x15a54f4: ldur            x4, [x3, #7]
    // 0x15a54f8: mov             x3, x4
    // 0x15a54fc: r17 = -328
    //     0x15a54fc: movn            x17, #0x147
    // 0x15a5500: str             x3, [fp, x17]
    // 0x15a5504: cmp             w0, NULL
    // 0x15a5508: b.ne            #0x15a5514
    // 0x15a550c: r0 = Null
    //     0x15a550c: mov             x0, NULL
    // 0x15a5510: b               #0x15a5550
    // 0x15a5514: LoadField: r4 = r0->field_57
    //     0x15a5514: ldur            w4, [x0, #0x57]
    // 0x15a5518: DecompressPointer r4
    //     0x15a5518: add             x4, x4, HEAP, lsl #32
    // 0x15a551c: cmp             w4, NULL
    // 0x15a5520: b.ne            #0x15a552c
    // 0x15a5524: r0 = Null
    //     0x15a5524: mov             x0, NULL
    // 0x15a5528: b               #0x15a5550
    // 0x15a552c: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x15a552c: ldur            w0, [x4, #0x17]
    // 0x15a5530: DecompressPointer r0
    //     0x15a5530: add             x0, x0, HEAP, lsl #32
    // 0x15a5534: cmp             w0, NULL
    // 0x15a5538: b.ne            #0x15a5544
    // 0x15a553c: r0 = Null
    //     0x15a553c: mov             x0, NULL
    // 0x15a5540: b               #0x15a5550
    // 0x15a5544: LoadField: r4 = r0->field_f
    //     0x15a5544: ldur            w4, [x0, #0xf]
    // 0x15a5548: DecompressPointer r4
    //     0x15a5548: add             x4, x4, HEAP, lsl #32
    // 0x15a554c: mov             x0, x4
    // 0x15a5550: cmp             w0, NULL
    // 0x15a5554: b.ne            #0x15a5560
    // 0x15a5558: r4 = 0
    //     0x15a5558: movz            x4, #0
    // 0x15a555c: b               #0x15a556c
    // 0x15a5560: r4 = LoadInt32Instr(r0)
    //     0x15a5560: sbfx            x4, x0, #1, #0x1f
    //     0x15a5564: tbz             w0, #0, #0x15a556c
    //     0x15a5568: ldur            x4, [x0, #7]
    // 0x15a556c: ldur            x0, [fp, #-0xc0]
    // 0x15a5570: stur            x4, [fp, #-0xc8]
    // 0x15a5574: r0 = Color()
    //     0x15a5574: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a5578: mov             x1, x0
    // 0x15a557c: r0 = Instance_ColorSpace
    //     0x15a557c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a5580: StoreField: r1->field_27 = r0
    //     0x15a5580: stur            w0, [x1, #0x27]
    // 0x15a5584: d0 = 1.000000
    //     0x15a5584: fmov            d0, #1.00000000
    // 0x15a5588: StoreField: r1->field_7 = d0
    //     0x15a5588: stur            d0, [x1, #7]
    // 0x15a558c: r17 = -336
    //     0x15a558c: movn            x17, #0x14f
    // 0x15a5590: ldr             x2, [fp, x17]
    // 0x15a5594: ubfx            x2, x2, #0, #0x20
    // 0x15a5598: and             w3, w2, #0xff
    // 0x15a559c: ubfx            x3, x3, #0, #0x20
    // 0x15a55a0: scvtf           d1, x3
    // 0x15a55a4: d2 = 255.000000
    //     0x15a55a4: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a55a8: fdiv            d3, d1, d2
    // 0x15a55ac: StoreField: r1->field_f = d3
    //     0x15a55ac: stur            d3, [x1, #0xf]
    // 0x15a55b0: r17 = -328
    //     0x15a55b0: movn            x17, #0x147
    // 0x15a55b4: ldr             x2, [fp, x17]
    // 0x15a55b8: ubfx            x2, x2, #0, #0x20
    // 0x15a55bc: and             w3, w2, #0xff
    // 0x15a55c0: ubfx            x3, x3, #0, #0x20
    // 0x15a55c4: scvtf           d1, x3
    // 0x15a55c8: fdiv            d3, d1, d2
    // 0x15a55cc: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a55cc: stur            d3, [x1, #0x17]
    // 0x15a55d0: ldur            x2, [fp, #-0xc8]
    // 0x15a55d4: ubfx            x2, x2, #0, #0x20
    // 0x15a55d8: and             w3, w2, #0xff
    // 0x15a55dc: ubfx            x3, x3, #0, #0x20
    // 0x15a55e0: scvtf           d1, x3
    // 0x15a55e4: fdiv            d3, d1, d2
    // 0x15a55e8: StoreField: r1->field_1f = d3
    //     0x15a55e8: stur            d3, [x1, #0x1f]
    // 0x15a55ec: str             x1, [SP]
    // 0x15a55f0: ldur            x1, [fp, #-0x100]
    // 0x15a55f4: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15a55f4: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15a55f8: ldr             x4, [x4, #0xf40]
    // 0x15a55fc: r0 = copyWith()
    //     0x15a55fc: bl              #0x16abf9c  ; [package:flutter/src/widgets/icon_theme_data.dart] IconThemeData::copyWith
    // 0x15a5600: mov             x1, x0
    // 0x15a5604: ldur            x0, [fp, #-0xc0]
    // 0x15a5608: stur            x1, [fp, #-0xf8]
    // 0x15a560c: LoadField: r2 = r0->field_b
    //     0x15a560c: ldur            w2, [x0, #0xb]
    // 0x15a5610: DecompressPointer r2
    //     0x15a5610: add             x2, x2, HEAP, lsl #32
    // 0x15a5614: stur            x2, [fp, #-0xe8]
    // 0x15a5618: cmp             w2, NULL
    // 0x15a561c: b.ne            #0x15a5628
    // 0x15a5620: r3 = Null
    //     0x15a5620: mov             x3, NULL
    // 0x15a5624: b               #0x15a5660
    // 0x15a5628: LoadField: r3 = r2->field_57
    //     0x15a5628: ldur            w3, [x2, #0x57]
    // 0x15a562c: DecompressPointer r3
    //     0x15a562c: add             x3, x3, HEAP, lsl #32
    // 0x15a5630: cmp             w3, NULL
    // 0x15a5634: b.ne            #0x15a5640
    // 0x15a5638: r3 = Null
    //     0x15a5638: mov             x3, NULL
    // 0x15a563c: b               #0x15a5660
    // 0x15a5640: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a5640: ldur            w4, [x3, #0x17]
    // 0x15a5644: DecompressPointer r4
    //     0x15a5644: add             x4, x4, HEAP, lsl #32
    // 0x15a5648: cmp             w4, NULL
    // 0x15a564c: b.ne            #0x15a5658
    // 0x15a5650: r3 = Null
    //     0x15a5650: mov             x3, NULL
    // 0x15a5654: b               #0x15a5660
    // 0x15a5658: LoadField: r3 = r4->field_7
    //     0x15a5658: ldur            w3, [x4, #7]
    // 0x15a565c: DecompressPointer r3
    //     0x15a565c: add             x3, x3, HEAP, lsl #32
    // 0x15a5660: cmp             w3, NULL
    // 0x15a5664: b.ne            #0x15a5670
    // 0x15a5668: r3 = 0
    //     0x15a5668: movz            x3, #0
    // 0x15a566c: b               #0x15a5680
    // 0x15a5670: r4 = LoadInt32Instr(r3)
    //     0x15a5670: sbfx            x4, x3, #1, #0x1f
    //     0x15a5674: tbz             w3, #0, #0x15a567c
    //     0x15a5678: ldur            x4, [x3, #7]
    // 0x15a567c: mov             x3, x4
    // 0x15a5680: r17 = -336
    //     0x15a5680: movn            x17, #0x14f
    // 0x15a5684: str             x3, [fp, x17]
    // 0x15a5688: cmp             w2, NULL
    // 0x15a568c: b.ne            #0x15a5698
    // 0x15a5690: r4 = Null
    //     0x15a5690: mov             x4, NULL
    // 0x15a5694: b               #0x15a56d0
    // 0x15a5698: LoadField: r4 = r2->field_57
    //     0x15a5698: ldur            w4, [x2, #0x57]
    // 0x15a569c: DecompressPointer r4
    //     0x15a569c: add             x4, x4, HEAP, lsl #32
    // 0x15a56a0: cmp             w4, NULL
    // 0x15a56a4: b.ne            #0x15a56b0
    // 0x15a56a8: r4 = Null
    //     0x15a56a8: mov             x4, NULL
    // 0x15a56ac: b               #0x15a56d0
    // 0x15a56b0: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a56b0: ldur            w5, [x4, #0x17]
    // 0x15a56b4: DecompressPointer r5
    //     0x15a56b4: add             x5, x5, HEAP, lsl #32
    // 0x15a56b8: cmp             w5, NULL
    // 0x15a56bc: b.ne            #0x15a56c8
    // 0x15a56c0: r4 = Null
    //     0x15a56c0: mov             x4, NULL
    // 0x15a56c4: b               #0x15a56d0
    // 0x15a56c8: LoadField: r4 = r5->field_b
    //     0x15a56c8: ldur            w4, [x5, #0xb]
    // 0x15a56cc: DecompressPointer r4
    //     0x15a56cc: add             x4, x4, HEAP, lsl #32
    // 0x15a56d0: cmp             w4, NULL
    // 0x15a56d4: b.ne            #0x15a56e0
    // 0x15a56d8: r4 = 0
    //     0x15a56d8: movz            x4, #0
    // 0x15a56dc: b               #0x15a56f0
    // 0x15a56e0: r5 = LoadInt32Instr(r4)
    //     0x15a56e0: sbfx            x5, x4, #1, #0x1f
    //     0x15a56e4: tbz             w4, #0, #0x15a56ec
    //     0x15a56e8: ldur            x5, [x4, #7]
    // 0x15a56ec: mov             x4, x5
    // 0x15a56f0: r17 = -328
    //     0x15a56f0: movn            x17, #0x147
    // 0x15a56f4: str             x4, [fp, x17]
    // 0x15a56f8: cmp             w2, NULL
    // 0x15a56fc: b.ne            #0x15a5708
    // 0x15a5700: r5 = Null
    //     0x15a5700: mov             x5, NULL
    // 0x15a5704: b               #0x15a5740
    // 0x15a5708: LoadField: r5 = r2->field_57
    //     0x15a5708: ldur            w5, [x2, #0x57]
    // 0x15a570c: DecompressPointer r5
    //     0x15a570c: add             x5, x5, HEAP, lsl #32
    // 0x15a5710: cmp             w5, NULL
    // 0x15a5714: b.ne            #0x15a5720
    // 0x15a5718: r5 = Null
    //     0x15a5718: mov             x5, NULL
    // 0x15a571c: b               #0x15a5740
    // 0x15a5720: ArrayLoad: r6 = r5[0]  ; List_4
    //     0x15a5720: ldur            w6, [x5, #0x17]
    // 0x15a5724: DecompressPointer r6
    //     0x15a5724: add             x6, x6, HEAP, lsl #32
    // 0x15a5728: cmp             w6, NULL
    // 0x15a572c: b.ne            #0x15a5738
    // 0x15a5730: r5 = Null
    //     0x15a5730: mov             x5, NULL
    // 0x15a5734: b               #0x15a5740
    // 0x15a5738: LoadField: r5 = r6->field_f
    //     0x15a5738: ldur            w5, [x6, #0xf]
    // 0x15a573c: DecompressPointer r5
    //     0x15a573c: add             x5, x5, HEAP, lsl #32
    // 0x15a5740: cmp             w5, NULL
    // 0x15a5744: b.ne            #0x15a5750
    // 0x15a5748: r5 = 0
    //     0x15a5748: movz            x5, #0
    // 0x15a574c: b               #0x15a5760
    // 0x15a5750: r6 = LoadInt32Instr(r5)
    //     0x15a5750: sbfx            x6, x5, #1, #0x1f
    //     0x15a5754: tbz             w5, #0, #0x15a575c
    //     0x15a5758: ldur            x6, [x5, #7]
    // 0x15a575c: mov             x5, x6
    // 0x15a5760: stur            x5, [fp, #-0xc8]
    // 0x15a5764: r0 = Color()
    //     0x15a5764: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a5768: mov             x1, x0
    // 0x15a576c: r0 = Instance_ColorSpace
    //     0x15a576c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a5770: stur            x1, [fp, #-0x100]
    // 0x15a5774: StoreField: r1->field_27 = r0
    //     0x15a5774: stur            w0, [x1, #0x27]
    // 0x15a5778: StoreField: r1->field_7 = rZR
    //     0x15a5778: stur            xzr, [x1, #7]
    // 0x15a577c: r17 = -336
    //     0x15a577c: movn            x17, #0x14f
    // 0x15a5780: ldr             x2, [fp, x17]
    // 0x15a5784: ubfx            x2, x2, #0, #0x20
    // 0x15a5788: and             w3, w2, #0xff
    // 0x15a578c: ubfx            x3, x3, #0, #0x20
    // 0x15a5790: scvtf           d0, x3
    // 0x15a5794: d1 = 255.000000
    //     0x15a5794: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a5798: fdiv            d2, d0, d1
    // 0x15a579c: StoreField: r1->field_f = d2
    //     0x15a579c: stur            d2, [x1, #0xf]
    // 0x15a57a0: r17 = -328
    //     0x15a57a0: movn            x17, #0x147
    // 0x15a57a4: ldr             x2, [fp, x17]
    // 0x15a57a8: ubfx            x2, x2, #0, #0x20
    // 0x15a57ac: and             w3, w2, #0xff
    // 0x15a57b0: ubfx            x3, x3, #0, #0x20
    // 0x15a57b4: scvtf           d0, x3
    // 0x15a57b8: fdiv            d2, d0, d1
    // 0x15a57bc: ArrayStore: r1[0] = d2  ; List_8
    //     0x15a57bc: stur            d2, [x1, #0x17]
    // 0x15a57c0: ldur            x2, [fp, #-0xc8]
    // 0x15a57c4: ubfx            x2, x2, #0, #0x20
    // 0x15a57c8: and             w3, w2, #0xff
    // 0x15a57cc: ubfx            x3, x3, #0, #0x20
    // 0x15a57d0: scvtf           d0, x3
    // 0x15a57d4: fdiv            d2, d0, d1
    // 0x15a57d8: StoreField: r1->field_1f = d2
    //     0x15a57d8: stur            d2, [x1, #0x1f]
    // 0x15a57dc: r0 = IconThemeData()
    //     0x15a57dc: bl              #0x6b2d30  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0x15a57e0: mov             x1, x0
    // 0x15a57e4: ldur            x0, [fp, #-0x100]
    // 0x15a57e8: r17 = -264
    //     0x15a57e8: movn            x17, #0x107
    // 0x15a57ec: str             x1, [fp, x17]
    // 0x15a57f0: StoreField: r1->field_1b = r0
    //     0x15a57f0: stur            w0, [x1, #0x1b]
    // 0x15a57f4: ldur            x0, [fp, #-0xe8]
    // 0x15a57f8: cmp             w0, NULL
    // 0x15a57fc: b.ne            #0x15a5808
    // 0x15a5800: r2 = Null
    //     0x15a5800: mov             x2, NULL
    // 0x15a5804: b               #0x15a5840
    // 0x15a5808: LoadField: r2 = r0->field_57
    //     0x15a5808: ldur            w2, [x0, #0x57]
    // 0x15a580c: DecompressPointer r2
    //     0x15a580c: add             x2, x2, HEAP, lsl #32
    // 0x15a5810: cmp             w2, NULL
    // 0x15a5814: b.ne            #0x15a5820
    // 0x15a5818: r2 = Null
    //     0x15a5818: mov             x2, NULL
    // 0x15a581c: b               #0x15a5840
    // 0x15a5820: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x15a5820: ldur            w3, [x2, #0x17]
    // 0x15a5824: DecompressPointer r3
    //     0x15a5824: add             x3, x3, HEAP, lsl #32
    // 0x15a5828: cmp             w3, NULL
    // 0x15a582c: b.ne            #0x15a5838
    // 0x15a5830: r2 = Null
    //     0x15a5830: mov             x2, NULL
    // 0x15a5834: b               #0x15a5840
    // 0x15a5838: LoadField: r2 = r3->field_7
    //     0x15a5838: ldur            w2, [x3, #7]
    // 0x15a583c: DecompressPointer r2
    //     0x15a583c: add             x2, x2, HEAP, lsl #32
    // 0x15a5840: cmp             w2, NULL
    // 0x15a5844: b.ne            #0x15a5850
    // 0x15a5848: r2 = 0
    //     0x15a5848: movz            x2, #0
    // 0x15a584c: b               #0x15a5860
    // 0x15a5850: r3 = LoadInt32Instr(r2)
    //     0x15a5850: sbfx            x3, x2, #1, #0x1f
    //     0x15a5854: tbz             w2, #0, #0x15a585c
    //     0x15a5858: ldur            x3, [x2, #7]
    // 0x15a585c: mov             x2, x3
    // 0x15a5860: r17 = -336
    //     0x15a5860: movn            x17, #0x14f
    // 0x15a5864: str             x2, [fp, x17]
    // 0x15a5868: cmp             w0, NULL
    // 0x15a586c: b.ne            #0x15a5878
    // 0x15a5870: r3 = Null
    //     0x15a5870: mov             x3, NULL
    // 0x15a5874: b               #0x15a58b0
    // 0x15a5878: LoadField: r3 = r0->field_57
    //     0x15a5878: ldur            w3, [x0, #0x57]
    // 0x15a587c: DecompressPointer r3
    //     0x15a587c: add             x3, x3, HEAP, lsl #32
    // 0x15a5880: cmp             w3, NULL
    // 0x15a5884: b.ne            #0x15a5890
    // 0x15a5888: r3 = Null
    //     0x15a5888: mov             x3, NULL
    // 0x15a588c: b               #0x15a58b0
    // 0x15a5890: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a5890: ldur            w4, [x3, #0x17]
    // 0x15a5894: DecompressPointer r4
    //     0x15a5894: add             x4, x4, HEAP, lsl #32
    // 0x15a5898: cmp             w4, NULL
    // 0x15a589c: b.ne            #0x15a58a8
    // 0x15a58a0: r3 = Null
    //     0x15a58a0: mov             x3, NULL
    // 0x15a58a4: b               #0x15a58b0
    // 0x15a58a8: LoadField: r3 = r4->field_b
    //     0x15a58a8: ldur            w3, [x4, #0xb]
    // 0x15a58ac: DecompressPointer r3
    //     0x15a58ac: add             x3, x3, HEAP, lsl #32
    // 0x15a58b0: cmp             w3, NULL
    // 0x15a58b4: b.ne            #0x15a58c0
    // 0x15a58b8: r3 = 0
    //     0x15a58b8: movz            x3, #0
    // 0x15a58bc: b               #0x15a58d0
    // 0x15a58c0: r4 = LoadInt32Instr(r3)
    //     0x15a58c0: sbfx            x4, x3, #1, #0x1f
    //     0x15a58c4: tbz             w3, #0, #0x15a58cc
    //     0x15a58c8: ldur            x4, [x3, #7]
    // 0x15a58cc: mov             x3, x4
    // 0x15a58d0: r17 = -328
    //     0x15a58d0: movn            x17, #0x147
    // 0x15a58d4: str             x3, [fp, x17]
    // 0x15a58d8: cmp             w0, NULL
    // 0x15a58dc: b.ne            #0x15a58e8
    // 0x15a58e0: r4 = Null
    //     0x15a58e0: mov             x4, NULL
    // 0x15a58e4: b               #0x15a5920
    // 0x15a58e8: LoadField: r4 = r0->field_57
    //     0x15a58e8: ldur            w4, [x0, #0x57]
    // 0x15a58ec: DecompressPointer r4
    //     0x15a58ec: add             x4, x4, HEAP, lsl #32
    // 0x15a58f0: cmp             w4, NULL
    // 0x15a58f4: b.ne            #0x15a5900
    // 0x15a58f8: r4 = Null
    //     0x15a58f8: mov             x4, NULL
    // 0x15a58fc: b               #0x15a5920
    // 0x15a5900: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a5900: ldur            w5, [x4, #0x17]
    // 0x15a5904: DecompressPointer r5
    //     0x15a5904: add             x5, x5, HEAP, lsl #32
    // 0x15a5908: cmp             w5, NULL
    // 0x15a590c: b.ne            #0x15a5918
    // 0x15a5910: r4 = Null
    //     0x15a5910: mov             x4, NULL
    // 0x15a5914: b               #0x15a5920
    // 0x15a5918: LoadField: r4 = r5->field_f
    //     0x15a5918: ldur            w4, [x5, #0xf]
    // 0x15a591c: DecompressPointer r4
    //     0x15a591c: add             x4, x4, HEAP, lsl #32
    // 0x15a5920: cmp             w4, NULL
    // 0x15a5924: b.ne            #0x15a5930
    // 0x15a5928: r4 = 0
    //     0x15a5928: movz            x4, #0
    // 0x15a592c: b               #0x15a5940
    // 0x15a5930: r5 = LoadInt32Instr(r4)
    //     0x15a5930: sbfx            x5, x4, #1, #0x1f
    //     0x15a5934: tbz             w4, #0, #0x15a593c
    //     0x15a5938: ldur            x5, [x4, #7]
    // 0x15a593c: mov             x4, x5
    // 0x15a5940: stur            x4, [fp, #-0xc8]
    // 0x15a5944: r0 = Color()
    //     0x15a5944: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a5948: mov             x1, x0
    // 0x15a594c: r0 = Instance_ColorSpace
    //     0x15a594c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a5950: stur            x1, [fp, #-0x100]
    // 0x15a5954: StoreField: r1->field_27 = r0
    //     0x15a5954: stur            w0, [x1, #0x27]
    // 0x15a5958: d0 = 1.000000
    //     0x15a5958: fmov            d0, #1.00000000
    // 0x15a595c: StoreField: r1->field_7 = d0
    //     0x15a595c: stur            d0, [x1, #7]
    // 0x15a5960: r17 = -336
    //     0x15a5960: movn            x17, #0x14f
    // 0x15a5964: ldr             x2, [fp, x17]
    // 0x15a5968: ubfx            x2, x2, #0, #0x20
    // 0x15a596c: and             w3, w2, #0xff
    // 0x15a5970: ubfx            x3, x3, #0, #0x20
    // 0x15a5974: scvtf           d1, x3
    // 0x15a5978: d2 = 255.000000
    //     0x15a5978: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a597c: fdiv            d3, d1, d2
    // 0x15a5980: StoreField: r1->field_f = d3
    //     0x15a5980: stur            d3, [x1, #0xf]
    // 0x15a5984: r17 = -328
    //     0x15a5984: movn            x17, #0x147
    // 0x15a5988: ldr             x2, [fp, x17]
    // 0x15a598c: ubfx            x2, x2, #0, #0x20
    // 0x15a5990: and             w3, w2, #0xff
    // 0x15a5994: ubfx            x3, x3, #0, #0x20
    // 0x15a5998: scvtf           d1, x3
    // 0x15a599c: fdiv            d3, d1, d2
    // 0x15a59a0: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a59a0: stur            d3, [x1, #0x17]
    // 0x15a59a4: ldur            x2, [fp, #-0xc8]
    // 0x15a59a8: ubfx            x2, x2, #0, #0x20
    // 0x15a59ac: and             w3, w2, #0xff
    // 0x15a59b0: ubfx            x3, x3, #0, #0x20
    // 0x15a59b4: scvtf           d1, x3
    // 0x15a59b8: fdiv            d3, d1, d2
    // 0x15a59bc: StoreField: r1->field_1f = d3
    //     0x15a59bc: stur            d3, [x1, #0x1f]
    // 0x15a59c0: ldur            x2, [fp, #-0xe8]
    // 0x15a59c4: cmp             w2, NULL
    // 0x15a59c8: b.ne            #0x15a59d4
    // 0x15a59cc: r3 = Null
    //     0x15a59cc: mov             x3, NULL
    // 0x15a59d0: b               #0x15a5a0c
    // 0x15a59d4: LoadField: r3 = r2->field_57
    //     0x15a59d4: ldur            w3, [x2, #0x57]
    // 0x15a59d8: DecompressPointer r3
    //     0x15a59d8: add             x3, x3, HEAP, lsl #32
    // 0x15a59dc: cmp             w3, NULL
    // 0x15a59e0: b.ne            #0x15a59ec
    // 0x15a59e4: r3 = Null
    //     0x15a59e4: mov             x3, NULL
    // 0x15a59e8: b               #0x15a5a0c
    // 0x15a59ec: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a59ec: ldur            w4, [x3, #0x17]
    // 0x15a59f0: DecompressPointer r4
    //     0x15a59f0: add             x4, x4, HEAP, lsl #32
    // 0x15a59f4: cmp             w4, NULL
    // 0x15a59f8: b.ne            #0x15a5a04
    // 0x15a59fc: r3 = Null
    //     0x15a59fc: mov             x3, NULL
    // 0x15a5a00: b               #0x15a5a0c
    // 0x15a5a04: LoadField: r3 = r4->field_7
    //     0x15a5a04: ldur            w3, [x4, #7]
    // 0x15a5a08: DecompressPointer r3
    //     0x15a5a08: add             x3, x3, HEAP, lsl #32
    // 0x15a5a0c: cmp             w3, NULL
    // 0x15a5a10: b.ne            #0x15a5a1c
    // 0x15a5a14: r3 = 0
    //     0x15a5a14: movz            x3, #0
    // 0x15a5a18: b               #0x15a5a2c
    // 0x15a5a1c: r4 = LoadInt32Instr(r3)
    //     0x15a5a1c: sbfx            x4, x3, #1, #0x1f
    //     0x15a5a20: tbz             w3, #0, #0x15a5a28
    //     0x15a5a24: ldur            x4, [x3, #7]
    // 0x15a5a28: mov             x3, x4
    // 0x15a5a2c: r17 = -336
    //     0x15a5a2c: movn            x17, #0x14f
    // 0x15a5a30: str             x3, [fp, x17]
    // 0x15a5a34: cmp             w2, NULL
    // 0x15a5a38: b.ne            #0x15a5a44
    // 0x15a5a3c: r4 = Null
    //     0x15a5a3c: mov             x4, NULL
    // 0x15a5a40: b               #0x15a5a7c
    // 0x15a5a44: LoadField: r4 = r2->field_57
    //     0x15a5a44: ldur            w4, [x2, #0x57]
    // 0x15a5a48: DecompressPointer r4
    //     0x15a5a48: add             x4, x4, HEAP, lsl #32
    // 0x15a5a4c: cmp             w4, NULL
    // 0x15a5a50: b.ne            #0x15a5a5c
    // 0x15a5a54: r4 = Null
    //     0x15a5a54: mov             x4, NULL
    // 0x15a5a58: b               #0x15a5a7c
    // 0x15a5a5c: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a5a5c: ldur            w5, [x4, #0x17]
    // 0x15a5a60: DecompressPointer r5
    //     0x15a5a60: add             x5, x5, HEAP, lsl #32
    // 0x15a5a64: cmp             w5, NULL
    // 0x15a5a68: b.ne            #0x15a5a74
    // 0x15a5a6c: r4 = Null
    //     0x15a5a6c: mov             x4, NULL
    // 0x15a5a70: b               #0x15a5a7c
    // 0x15a5a74: LoadField: r4 = r5->field_b
    //     0x15a5a74: ldur            w4, [x5, #0xb]
    // 0x15a5a78: DecompressPointer r4
    //     0x15a5a78: add             x4, x4, HEAP, lsl #32
    // 0x15a5a7c: cmp             w4, NULL
    // 0x15a5a80: b.ne            #0x15a5a8c
    // 0x15a5a84: r4 = 0
    //     0x15a5a84: movz            x4, #0
    // 0x15a5a88: b               #0x15a5a9c
    // 0x15a5a8c: r5 = LoadInt32Instr(r4)
    //     0x15a5a8c: sbfx            x5, x4, #1, #0x1f
    //     0x15a5a90: tbz             w4, #0, #0x15a5a98
    //     0x15a5a94: ldur            x5, [x4, #7]
    // 0x15a5a98: mov             x4, x5
    // 0x15a5a9c: r17 = -328
    //     0x15a5a9c: movn            x17, #0x147
    // 0x15a5aa0: str             x4, [fp, x17]
    // 0x15a5aa4: cmp             w2, NULL
    // 0x15a5aa8: b.ne            #0x15a5ab4
    // 0x15a5aac: r2 = Null
    //     0x15a5aac: mov             x2, NULL
    // 0x15a5ab0: b               #0x15a5af0
    // 0x15a5ab4: LoadField: r5 = r2->field_57
    //     0x15a5ab4: ldur            w5, [x2, #0x57]
    // 0x15a5ab8: DecompressPointer r5
    //     0x15a5ab8: add             x5, x5, HEAP, lsl #32
    // 0x15a5abc: cmp             w5, NULL
    // 0x15a5ac0: b.ne            #0x15a5acc
    // 0x15a5ac4: r2 = Null
    //     0x15a5ac4: mov             x2, NULL
    // 0x15a5ac8: b               #0x15a5af0
    // 0x15a5acc: ArrayLoad: r2 = r5[0]  ; List_4
    //     0x15a5acc: ldur            w2, [x5, #0x17]
    // 0x15a5ad0: DecompressPointer r2
    //     0x15a5ad0: add             x2, x2, HEAP, lsl #32
    // 0x15a5ad4: cmp             w2, NULL
    // 0x15a5ad8: b.ne            #0x15a5ae4
    // 0x15a5adc: r2 = Null
    //     0x15a5adc: mov             x2, NULL
    // 0x15a5ae0: b               #0x15a5af0
    // 0x15a5ae4: LoadField: r5 = r2->field_f
    //     0x15a5ae4: ldur            w5, [x2, #0xf]
    // 0x15a5ae8: DecompressPointer r5
    //     0x15a5ae8: add             x5, x5, HEAP, lsl #32
    // 0x15a5aec: mov             x2, x5
    // 0x15a5af0: cmp             w2, NULL
    // 0x15a5af4: b.ne            #0x15a5b00
    // 0x15a5af8: r5 = 0
    //     0x15a5af8: movz            x5, #0
    // 0x15a5afc: b               #0x15a5b0c
    // 0x15a5b00: r5 = LoadInt32Instr(r2)
    //     0x15a5b00: sbfx            x5, x2, #1, #0x1f
    //     0x15a5b04: tbz             w2, #0, #0x15a5b0c
    //     0x15a5b08: ldur            x5, [x2, #7]
    // 0x15a5b0c: ldur            x2, [fp, #-0xc0]
    // 0x15a5b10: stur            x5, [fp, #-0xc8]
    // 0x15a5b14: r0 = Color()
    //     0x15a5b14: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a5b18: mov             x2, x0
    // 0x15a5b1c: r0 = Instance_ColorSpace
    //     0x15a5b1c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a5b20: stur            x2, [fp, #-0xe8]
    // 0x15a5b24: StoreField: r2->field_27 = r0
    //     0x15a5b24: stur            w0, [x2, #0x27]
    // 0x15a5b28: d0 = 0.700000
    //     0x15a5b28: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x15a5b2c: ldr             d0, [x17, #0xf48]
    // 0x15a5b30: StoreField: r2->field_7 = d0
    //     0x15a5b30: stur            d0, [x2, #7]
    // 0x15a5b34: r17 = -336
    //     0x15a5b34: movn            x17, #0x14f
    // 0x15a5b38: ldr             x1, [fp, x17]
    // 0x15a5b3c: ubfx            x1, x1, #0, #0x20
    // 0x15a5b40: and             w3, w1, #0xff
    // 0x15a5b44: ubfx            x3, x3, #0, #0x20
    // 0x15a5b48: scvtf           d0, x3
    // 0x15a5b4c: d1 = 255.000000
    //     0x15a5b4c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a5b50: fdiv            d2, d0, d1
    // 0x15a5b54: StoreField: r2->field_f = d2
    //     0x15a5b54: stur            d2, [x2, #0xf]
    // 0x15a5b58: r17 = -328
    //     0x15a5b58: movn            x17, #0x147
    // 0x15a5b5c: ldr             x1, [fp, x17]
    // 0x15a5b60: ubfx            x1, x1, #0, #0x20
    // 0x15a5b64: and             w3, w1, #0xff
    // 0x15a5b68: ubfx            x3, x3, #0, #0x20
    // 0x15a5b6c: scvtf           d0, x3
    // 0x15a5b70: fdiv            d2, d0, d1
    // 0x15a5b74: ArrayStore: r2[0] = d2  ; List_8
    //     0x15a5b74: stur            d2, [x2, #0x17]
    // 0x15a5b78: ldur            x1, [fp, #-0xc8]
    // 0x15a5b7c: ubfx            x1, x1, #0, #0x20
    // 0x15a5b80: and             w3, w1, #0xff
    // 0x15a5b84: ubfx            x3, x3, #0, #0x20
    // 0x15a5b88: scvtf           d0, x3
    // 0x15a5b8c: fdiv            d2, d0, d1
    // 0x15a5b90: StoreField: r2->field_1f = d2
    //     0x15a5b90: stur            d2, [x2, #0x1f]
    // 0x15a5b94: r1 = "Montserrat"
    //     0x15a5b94: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f50] "Montserrat"
    //     0x15a5b98: ldr             x1, [x1, #0xf50]
    // 0x15a5b9c: r0 = getFont()
    //     0x15a5b9c: bl              #0xc994b0  ; [package:google_fonts/google_fonts.dart] GoogleFonts::getFont
    // 0x15a5ba0: mov             x1, x0
    // 0x15a5ba4: ldur            x0, [fp, #-0xc0]
    // 0x15a5ba8: r17 = -272
    //     0x15a5ba8: movn            x17, #0x10f
    // 0x15a5bac: str             x1, [fp, x17]
    // 0x15a5bb0: LoadField: r2 = r0->field_b
    //     0x15a5bb0: ldur            w2, [x0, #0xb]
    // 0x15a5bb4: DecompressPointer r2
    //     0x15a5bb4: add             x2, x2, HEAP, lsl #32
    // 0x15a5bb8: cmp             w2, NULL
    // 0x15a5bbc: b.ne            #0x15a5bc8
    // 0x15a5bc0: r3 = Null
    //     0x15a5bc0: mov             x3, NULL
    // 0x15a5bc4: b               #0x15a5c00
    // 0x15a5bc8: LoadField: r3 = r2->field_57
    //     0x15a5bc8: ldur            w3, [x2, #0x57]
    // 0x15a5bcc: DecompressPointer r3
    //     0x15a5bcc: add             x3, x3, HEAP, lsl #32
    // 0x15a5bd0: cmp             w3, NULL
    // 0x15a5bd4: b.ne            #0x15a5be0
    // 0x15a5bd8: r3 = Null
    //     0x15a5bd8: mov             x3, NULL
    // 0x15a5bdc: b               #0x15a5c00
    // 0x15a5be0: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a5be0: ldur            w4, [x3, #0x17]
    // 0x15a5be4: DecompressPointer r4
    //     0x15a5be4: add             x4, x4, HEAP, lsl #32
    // 0x15a5be8: cmp             w4, NULL
    // 0x15a5bec: b.ne            #0x15a5bf8
    // 0x15a5bf0: r3 = Null
    //     0x15a5bf0: mov             x3, NULL
    // 0x15a5bf4: b               #0x15a5c00
    // 0x15a5bf8: LoadField: r3 = r4->field_7
    //     0x15a5bf8: ldur            w3, [x4, #7]
    // 0x15a5bfc: DecompressPointer r3
    //     0x15a5bfc: add             x3, x3, HEAP, lsl #32
    // 0x15a5c00: cmp             w3, NULL
    // 0x15a5c04: b.ne            #0x15a5c10
    // 0x15a5c08: r3 = 0
    //     0x15a5c08: movz            x3, #0
    // 0x15a5c0c: b               #0x15a5c20
    // 0x15a5c10: r4 = LoadInt32Instr(r3)
    //     0x15a5c10: sbfx            x4, x3, #1, #0x1f
    //     0x15a5c14: tbz             w3, #0, #0x15a5c1c
    //     0x15a5c18: ldur            x4, [x3, #7]
    // 0x15a5c1c: mov             x3, x4
    // 0x15a5c20: r17 = -336
    //     0x15a5c20: movn            x17, #0x14f
    // 0x15a5c24: str             x3, [fp, x17]
    // 0x15a5c28: cmp             w2, NULL
    // 0x15a5c2c: b.ne            #0x15a5c38
    // 0x15a5c30: r4 = Null
    //     0x15a5c30: mov             x4, NULL
    // 0x15a5c34: b               #0x15a5c70
    // 0x15a5c38: LoadField: r4 = r2->field_57
    //     0x15a5c38: ldur            w4, [x2, #0x57]
    // 0x15a5c3c: DecompressPointer r4
    //     0x15a5c3c: add             x4, x4, HEAP, lsl #32
    // 0x15a5c40: cmp             w4, NULL
    // 0x15a5c44: b.ne            #0x15a5c50
    // 0x15a5c48: r4 = Null
    //     0x15a5c48: mov             x4, NULL
    // 0x15a5c4c: b               #0x15a5c70
    // 0x15a5c50: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a5c50: ldur            w5, [x4, #0x17]
    // 0x15a5c54: DecompressPointer r5
    //     0x15a5c54: add             x5, x5, HEAP, lsl #32
    // 0x15a5c58: cmp             w5, NULL
    // 0x15a5c5c: b.ne            #0x15a5c68
    // 0x15a5c60: r4 = Null
    //     0x15a5c60: mov             x4, NULL
    // 0x15a5c64: b               #0x15a5c70
    // 0x15a5c68: LoadField: r4 = r5->field_b
    //     0x15a5c68: ldur            w4, [x5, #0xb]
    // 0x15a5c6c: DecompressPointer r4
    //     0x15a5c6c: add             x4, x4, HEAP, lsl #32
    // 0x15a5c70: cmp             w4, NULL
    // 0x15a5c74: b.ne            #0x15a5c80
    // 0x15a5c78: r4 = 0
    //     0x15a5c78: movz            x4, #0
    // 0x15a5c7c: b               #0x15a5c90
    // 0x15a5c80: r5 = LoadInt32Instr(r4)
    //     0x15a5c80: sbfx            x5, x4, #1, #0x1f
    //     0x15a5c84: tbz             w4, #0, #0x15a5c8c
    //     0x15a5c88: ldur            x5, [x4, #7]
    // 0x15a5c8c: mov             x4, x5
    // 0x15a5c90: r17 = -328
    //     0x15a5c90: movn            x17, #0x147
    // 0x15a5c94: str             x4, [fp, x17]
    // 0x15a5c98: cmp             w2, NULL
    // 0x15a5c9c: b.ne            #0x15a5ca8
    // 0x15a5ca0: r2 = Null
    //     0x15a5ca0: mov             x2, NULL
    // 0x15a5ca4: b               #0x15a5ce4
    // 0x15a5ca8: LoadField: r5 = r2->field_57
    //     0x15a5ca8: ldur            w5, [x2, #0x57]
    // 0x15a5cac: DecompressPointer r5
    //     0x15a5cac: add             x5, x5, HEAP, lsl #32
    // 0x15a5cb0: cmp             w5, NULL
    // 0x15a5cb4: b.ne            #0x15a5cc0
    // 0x15a5cb8: r2 = Null
    //     0x15a5cb8: mov             x2, NULL
    // 0x15a5cbc: b               #0x15a5ce4
    // 0x15a5cc0: ArrayLoad: r2 = r5[0]  ; List_4
    //     0x15a5cc0: ldur            w2, [x5, #0x17]
    // 0x15a5cc4: DecompressPointer r2
    //     0x15a5cc4: add             x2, x2, HEAP, lsl #32
    // 0x15a5cc8: cmp             w2, NULL
    // 0x15a5ccc: b.ne            #0x15a5cd8
    // 0x15a5cd0: r2 = Null
    //     0x15a5cd0: mov             x2, NULL
    // 0x15a5cd4: b               #0x15a5ce4
    // 0x15a5cd8: LoadField: r5 = r2->field_f
    //     0x15a5cd8: ldur            w5, [x2, #0xf]
    // 0x15a5cdc: DecompressPointer r5
    //     0x15a5cdc: add             x5, x5, HEAP, lsl #32
    // 0x15a5ce0: mov             x2, x5
    // 0x15a5ce4: cmp             w2, NULL
    // 0x15a5ce8: b.ne            #0x15a5cf4
    // 0x15a5cec: r2 = 0
    //     0x15a5cec: movz            x2, #0
    // 0x15a5cf0: b               #0x15a5d04
    // 0x15a5cf4: r5 = LoadInt32Instr(r2)
    //     0x15a5cf4: sbfx            x5, x2, #1, #0x1f
    //     0x15a5cf8: tbz             w2, #0, #0x15a5d00
    //     0x15a5cfc: ldur            x5, [x2, #7]
    // 0x15a5d00: mov             x2, x5
    // 0x15a5d04: stur            x2, [fp, #-0xc8]
    // 0x15a5d08: r0 = Color()
    //     0x15a5d08: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a5d0c: mov             x1, x0
    // 0x15a5d10: r0 = Instance_ColorSpace
    //     0x15a5d10: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a5d14: StoreField: r1->field_27 = r0
    //     0x15a5d14: stur            w0, [x1, #0x27]
    // 0x15a5d18: d0 = 1.000000
    //     0x15a5d18: fmov            d0, #1.00000000
    // 0x15a5d1c: StoreField: r1->field_7 = d0
    //     0x15a5d1c: stur            d0, [x1, #7]
    // 0x15a5d20: r17 = -336
    //     0x15a5d20: movn            x17, #0x14f
    // 0x15a5d24: ldr             x2, [fp, x17]
    // 0x15a5d28: ubfx            x2, x2, #0, #0x20
    // 0x15a5d2c: and             w3, w2, #0xff
    // 0x15a5d30: ubfx            x3, x3, #0, #0x20
    // 0x15a5d34: scvtf           d1, x3
    // 0x15a5d38: d2 = 255.000000
    //     0x15a5d38: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a5d3c: fdiv            d3, d1, d2
    // 0x15a5d40: StoreField: r1->field_f = d3
    //     0x15a5d40: stur            d3, [x1, #0xf]
    // 0x15a5d44: r17 = -328
    //     0x15a5d44: movn            x17, #0x147
    // 0x15a5d48: ldr             x2, [fp, x17]
    // 0x15a5d4c: ubfx            x2, x2, #0, #0x20
    // 0x15a5d50: and             w3, w2, #0xff
    // 0x15a5d54: ubfx            x3, x3, #0, #0x20
    // 0x15a5d58: scvtf           d1, x3
    // 0x15a5d5c: fdiv            d3, d1, d2
    // 0x15a5d60: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a5d60: stur            d3, [x1, #0x17]
    // 0x15a5d64: ldur            x2, [fp, #-0xc8]
    // 0x15a5d68: ubfx            x2, x2, #0, #0x20
    // 0x15a5d6c: and             w3, w2, #0xff
    // 0x15a5d70: ubfx            x3, x3, #0, #0x20
    // 0x15a5d74: scvtf           d1, x3
    // 0x15a5d78: fdiv            d3, d1, d2
    // 0x15a5d7c: StoreField: r1->field_1f = d3
    //     0x15a5d7c: stur            d3, [x1, #0x1f]
    // 0x15a5d80: str             x1, [SP]
    // 0x15a5d84: r17 = -272
    //     0x15a5d84: movn            x17, #0x10f
    // 0x15a5d88: ldr             x1, [fp, x17]
    // 0x15a5d8c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15a5d8c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15a5d90: ldr             x4, [x4, #0xf40]
    // 0x15a5d94: r0 = copyWith()
    //     0x15a5d94: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15a5d98: r1 = "Montserrat"
    //     0x15a5d98: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f50] "Montserrat"
    //     0x15a5d9c: ldr             x1, [x1, #0xf50]
    // 0x15a5da0: r17 = -272
    //     0x15a5da0: movn            x17, #0x10f
    // 0x15a5da4: str             x0, [fp, x17]
    // 0x15a5da8: r0 = getFont()
    //     0x15a5da8: bl              #0xc994b0  ; [package:google_fonts/google_fonts.dart] GoogleFonts::getFont
    // 0x15a5dac: mov             x1, x0
    // 0x15a5db0: ldur            x0, [fp, #-0xc0]
    // 0x15a5db4: r17 = -280
    //     0x15a5db4: movn            x17, #0x117
    // 0x15a5db8: str             x1, [fp, x17]
    // 0x15a5dbc: LoadField: r2 = r0->field_b
    //     0x15a5dbc: ldur            w2, [x0, #0xb]
    // 0x15a5dc0: DecompressPointer r2
    //     0x15a5dc0: add             x2, x2, HEAP, lsl #32
    // 0x15a5dc4: cmp             w2, NULL
    // 0x15a5dc8: b.ne            #0x15a5dd4
    // 0x15a5dcc: r3 = Null
    //     0x15a5dcc: mov             x3, NULL
    // 0x15a5dd0: b               #0x15a5e0c
    // 0x15a5dd4: LoadField: r3 = r2->field_57
    //     0x15a5dd4: ldur            w3, [x2, #0x57]
    // 0x15a5dd8: DecompressPointer r3
    //     0x15a5dd8: add             x3, x3, HEAP, lsl #32
    // 0x15a5ddc: cmp             w3, NULL
    // 0x15a5de0: b.ne            #0x15a5dec
    // 0x15a5de4: r3 = Null
    //     0x15a5de4: mov             x3, NULL
    // 0x15a5de8: b               #0x15a5e0c
    // 0x15a5dec: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a5dec: ldur            w4, [x3, #0x17]
    // 0x15a5df0: DecompressPointer r4
    //     0x15a5df0: add             x4, x4, HEAP, lsl #32
    // 0x15a5df4: cmp             w4, NULL
    // 0x15a5df8: b.ne            #0x15a5e04
    // 0x15a5dfc: r3 = Null
    //     0x15a5dfc: mov             x3, NULL
    // 0x15a5e00: b               #0x15a5e0c
    // 0x15a5e04: LoadField: r3 = r4->field_7
    //     0x15a5e04: ldur            w3, [x4, #7]
    // 0x15a5e08: DecompressPointer r3
    //     0x15a5e08: add             x3, x3, HEAP, lsl #32
    // 0x15a5e0c: cmp             w3, NULL
    // 0x15a5e10: b.ne            #0x15a5e1c
    // 0x15a5e14: r3 = 0
    //     0x15a5e14: movz            x3, #0
    // 0x15a5e18: b               #0x15a5e2c
    // 0x15a5e1c: r4 = LoadInt32Instr(r3)
    //     0x15a5e1c: sbfx            x4, x3, #1, #0x1f
    //     0x15a5e20: tbz             w3, #0, #0x15a5e28
    //     0x15a5e24: ldur            x4, [x3, #7]
    // 0x15a5e28: mov             x3, x4
    // 0x15a5e2c: r17 = -336
    //     0x15a5e2c: movn            x17, #0x14f
    // 0x15a5e30: str             x3, [fp, x17]
    // 0x15a5e34: cmp             w2, NULL
    // 0x15a5e38: b.ne            #0x15a5e44
    // 0x15a5e3c: r4 = Null
    //     0x15a5e3c: mov             x4, NULL
    // 0x15a5e40: b               #0x15a5e7c
    // 0x15a5e44: LoadField: r4 = r2->field_57
    //     0x15a5e44: ldur            w4, [x2, #0x57]
    // 0x15a5e48: DecompressPointer r4
    //     0x15a5e48: add             x4, x4, HEAP, lsl #32
    // 0x15a5e4c: cmp             w4, NULL
    // 0x15a5e50: b.ne            #0x15a5e5c
    // 0x15a5e54: r4 = Null
    //     0x15a5e54: mov             x4, NULL
    // 0x15a5e58: b               #0x15a5e7c
    // 0x15a5e5c: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a5e5c: ldur            w5, [x4, #0x17]
    // 0x15a5e60: DecompressPointer r5
    //     0x15a5e60: add             x5, x5, HEAP, lsl #32
    // 0x15a5e64: cmp             w5, NULL
    // 0x15a5e68: b.ne            #0x15a5e74
    // 0x15a5e6c: r4 = Null
    //     0x15a5e6c: mov             x4, NULL
    // 0x15a5e70: b               #0x15a5e7c
    // 0x15a5e74: LoadField: r4 = r5->field_b
    //     0x15a5e74: ldur            w4, [x5, #0xb]
    // 0x15a5e78: DecompressPointer r4
    //     0x15a5e78: add             x4, x4, HEAP, lsl #32
    // 0x15a5e7c: cmp             w4, NULL
    // 0x15a5e80: b.ne            #0x15a5e8c
    // 0x15a5e84: r4 = 0
    //     0x15a5e84: movz            x4, #0
    // 0x15a5e88: b               #0x15a5e9c
    // 0x15a5e8c: r5 = LoadInt32Instr(r4)
    //     0x15a5e8c: sbfx            x5, x4, #1, #0x1f
    //     0x15a5e90: tbz             w4, #0, #0x15a5e98
    //     0x15a5e94: ldur            x5, [x4, #7]
    // 0x15a5e98: mov             x4, x5
    // 0x15a5e9c: r17 = -328
    //     0x15a5e9c: movn            x17, #0x147
    // 0x15a5ea0: str             x4, [fp, x17]
    // 0x15a5ea4: cmp             w2, NULL
    // 0x15a5ea8: b.ne            #0x15a5eb4
    // 0x15a5eac: r2 = Null
    //     0x15a5eac: mov             x2, NULL
    // 0x15a5eb0: b               #0x15a5ef0
    // 0x15a5eb4: LoadField: r5 = r2->field_57
    //     0x15a5eb4: ldur            w5, [x2, #0x57]
    // 0x15a5eb8: DecompressPointer r5
    //     0x15a5eb8: add             x5, x5, HEAP, lsl #32
    // 0x15a5ebc: cmp             w5, NULL
    // 0x15a5ec0: b.ne            #0x15a5ecc
    // 0x15a5ec4: r2 = Null
    //     0x15a5ec4: mov             x2, NULL
    // 0x15a5ec8: b               #0x15a5ef0
    // 0x15a5ecc: ArrayLoad: r2 = r5[0]  ; List_4
    //     0x15a5ecc: ldur            w2, [x5, #0x17]
    // 0x15a5ed0: DecompressPointer r2
    //     0x15a5ed0: add             x2, x2, HEAP, lsl #32
    // 0x15a5ed4: cmp             w2, NULL
    // 0x15a5ed8: b.ne            #0x15a5ee4
    // 0x15a5edc: r2 = Null
    //     0x15a5edc: mov             x2, NULL
    // 0x15a5ee0: b               #0x15a5ef0
    // 0x15a5ee4: LoadField: r5 = r2->field_f
    //     0x15a5ee4: ldur            w5, [x2, #0xf]
    // 0x15a5ee8: DecompressPointer r5
    //     0x15a5ee8: add             x5, x5, HEAP, lsl #32
    // 0x15a5eec: mov             x2, x5
    // 0x15a5ef0: cmp             w2, NULL
    // 0x15a5ef4: b.ne            #0x15a5f00
    // 0x15a5ef8: r9 = 0
    //     0x15a5ef8: movz            x9, #0
    // 0x15a5efc: b               #0x15a5f10
    // 0x15a5f00: r5 = LoadInt32Instr(r2)
    //     0x15a5f00: sbfx            x5, x2, #1, #0x1f
    //     0x15a5f04: tbz             w2, #0, #0x15a5f0c
    //     0x15a5f08: ldur            x5, [x2, #7]
    // 0x15a5f0c: mov             x9, x5
    // 0x15a5f10: ldur            x8, [fp, #-0xf8]
    // 0x15a5f14: r17 = -264
    //     0x15a5f14: movn            x17, #0x107
    // 0x15a5f18: ldr             x7, [fp, x17]
    // 0x15a5f1c: ldur            x6, [fp, #-0x100]
    // 0x15a5f20: ldur            x5, [fp, #-0xe8]
    // 0x15a5f24: r17 = -272
    //     0x15a5f24: movn            x17, #0x10f
    // 0x15a5f28: ldr             x2, [fp, x17]
    // 0x15a5f2c: stur            x9, [fp, #-0xc8]
    // 0x15a5f30: r0 = Color()
    //     0x15a5f30: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a5f34: mov             x1, x0
    // 0x15a5f38: r0 = Instance_ColorSpace
    //     0x15a5f38: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a5f3c: StoreField: r1->field_27 = r0
    //     0x15a5f3c: stur            w0, [x1, #0x27]
    // 0x15a5f40: d0 = 0.400000
    //     0x15a5f40: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x15a5f44: StoreField: r1->field_7 = d0
    //     0x15a5f44: stur            d0, [x1, #7]
    // 0x15a5f48: r17 = -336
    //     0x15a5f48: movn            x17, #0x14f
    // 0x15a5f4c: ldr             x2, [fp, x17]
    // 0x15a5f50: ubfx            x2, x2, #0, #0x20
    // 0x15a5f54: and             w3, w2, #0xff
    // 0x15a5f58: ubfx            x3, x3, #0, #0x20
    // 0x15a5f5c: scvtf           d0, x3
    // 0x15a5f60: d1 = 255.000000
    //     0x15a5f60: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a5f64: fdiv            d2, d0, d1
    // 0x15a5f68: StoreField: r1->field_f = d2
    //     0x15a5f68: stur            d2, [x1, #0xf]
    // 0x15a5f6c: r17 = -328
    //     0x15a5f6c: movn            x17, #0x147
    // 0x15a5f70: ldr             x2, [fp, x17]
    // 0x15a5f74: ubfx            x2, x2, #0, #0x20
    // 0x15a5f78: and             w3, w2, #0xff
    // 0x15a5f7c: ubfx            x3, x3, #0, #0x20
    // 0x15a5f80: scvtf           d0, x3
    // 0x15a5f84: fdiv            d2, d0, d1
    // 0x15a5f88: ArrayStore: r1[0] = d2  ; List_8
    //     0x15a5f88: stur            d2, [x1, #0x17]
    // 0x15a5f8c: ldur            x2, [fp, #-0xc8]
    // 0x15a5f90: ubfx            x2, x2, #0, #0x20
    // 0x15a5f94: and             w3, w2, #0xff
    // 0x15a5f98: ubfx            x3, x3, #0, #0x20
    // 0x15a5f9c: scvtf           d0, x3
    // 0x15a5fa0: fdiv            d2, d0, d1
    // 0x15a5fa4: StoreField: r1->field_1f = d2
    //     0x15a5fa4: stur            d2, [x1, #0x1f]
    // 0x15a5fa8: str             x1, [SP]
    // 0x15a5fac: r17 = -280
    //     0x15a5fac: movn            x17, #0x117
    // 0x15a5fb0: ldr             x1, [fp, x17]
    // 0x15a5fb4: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15a5fb4: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15a5fb8: ldr             x4, [x4, #0xf40]
    // 0x15a5fbc: r0 = copyWith()
    //     0x15a5fbc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15a5fc0: r17 = -280
    //     0x15a5fc0: movn            x17, #0x117
    // 0x15a5fc4: str             x0, [fp, x17]
    // 0x15a5fc8: r0 = BottomNavigationBarThemeData()
    //     0x15a5fc8: bl              #0xc959f4  ; AllocateBottomNavigationBarThemeDataStub -> BottomNavigationBarThemeData (size=0x40)
    // 0x15a5fcc: mov             x1, x0
    // 0x15a5fd0: ldur            x0, [fp, #-0xf8]
    // 0x15a5fd4: r17 = -288
    //     0x15a5fd4: movn            x17, #0x11f
    // 0x15a5fd8: str             x1, [fp, x17]
    // 0x15a5fdc: StoreField: r1->field_f = r0
    //     0x15a5fdc: stur            w0, [x1, #0xf]
    // 0x15a5fe0: r17 = -264
    //     0x15a5fe0: movn            x17, #0x107
    // 0x15a5fe4: ldr             x0, [fp, x17]
    // 0x15a5fe8: StoreField: r1->field_13 = r0
    //     0x15a5fe8: stur            w0, [x1, #0x13]
    // 0x15a5fec: ldur            x0, [fp, #-0x100]
    // 0x15a5ff0: ArrayStore: r1[0] = r0  ; List_4
    //     0x15a5ff0: stur            w0, [x1, #0x17]
    // 0x15a5ff4: ldur            x0, [fp, #-0xe8]
    // 0x15a5ff8: StoreField: r1->field_1b = r0
    //     0x15a5ff8: stur            w0, [x1, #0x1b]
    // 0x15a5ffc: r17 = -272
    //     0x15a5ffc: movn            x17, #0x10f
    // 0x15a6000: ldr             x0, [fp, x17]
    // 0x15a6004: StoreField: r1->field_1f = r0
    //     0x15a6004: stur            w0, [x1, #0x1f]
    // 0x15a6008: r17 = -280
    //     0x15a6008: movn            x17, #0x117
    // 0x15a600c: ldr             x0, [fp, x17]
    // 0x15a6010: StoreField: r1->field_23 = r0
    //     0x15a6010: stur            w0, [x1, #0x23]
    // 0x15a6014: ldur            x0, [fp, #-0xc0]
    // 0x15a6018: LoadField: r2 = r0->field_b
    //     0x15a6018: ldur            w2, [x0, #0xb]
    // 0x15a601c: DecompressPointer r2
    //     0x15a601c: add             x2, x2, HEAP, lsl #32
    // 0x15a6020: stur            x2, [fp, #-0xe8]
    // 0x15a6024: cmp             w2, NULL
    // 0x15a6028: b.ne            #0x15a6034
    // 0x15a602c: r3 = Null
    //     0x15a602c: mov             x3, NULL
    // 0x15a6030: b               #0x15a606c
    // 0x15a6034: LoadField: r3 = r2->field_57
    //     0x15a6034: ldur            w3, [x2, #0x57]
    // 0x15a6038: DecompressPointer r3
    //     0x15a6038: add             x3, x3, HEAP, lsl #32
    // 0x15a603c: cmp             w3, NULL
    // 0x15a6040: b.ne            #0x15a604c
    // 0x15a6044: r3 = Null
    //     0x15a6044: mov             x3, NULL
    // 0x15a6048: b               #0x15a606c
    // 0x15a604c: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a604c: ldur            w4, [x3, #0x17]
    // 0x15a6050: DecompressPointer r4
    //     0x15a6050: add             x4, x4, HEAP, lsl #32
    // 0x15a6054: cmp             w4, NULL
    // 0x15a6058: b.ne            #0x15a6064
    // 0x15a605c: r3 = Null
    //     0x15a605c: mov             x3, NULL
    // 0x15a6060: b               #0x15a606c
    // 0x15a6064: LoadField: r3 = r4->field_7
    //     0x15a6064: ldur            w3, [x4, #7]
    // 0x15a6068: DecompressPointer r3
    //     0x15a6068: add             x3, x3, HEAP, lsl #32
    // 0x15a606c: cmp             w3, NULL
    // 0x15a6070: b.ne            #0x15a607c
    // 0x15a6074: r3 = 0
    //     0x15a6074: movz            x3, #0
    // 0x15a6078: b               #0x15a608c
    // 0x15a607c: r4 = LoadInt32Instr(r3)
    //     0x15a607c: sbfx            x4, x3, #1, #0x1f
    //     0x15a6080: tbz             w3, #0, #0x15a6088
    //     0x15a6084: ldur            x4, [x3, #7]
    // 0x15a6088: mov             x3, x4
    // 0x15a608c: r17 = -336
    //     0x15a608c: movn            x17, #0x14f
    // 0x15a6090: str             x3, [fp, x17]
    // 0x15a6094: cmp             w2, NULL
    // 0x15a6098: b.ne            #0x15a60a4
    // 0x15a609c: r4 = Null
    //     0x15a609c: mov             x4, NULL
    // 0x15a60a0: b               #0x15a60dc
    // 0x15a60a4: LoadField: r4 = r2->field_57
    //     0x15a60a4: ldur            w4, [x2, #0x57]
    // 0x15a60a8: DecompressPointer r4
    //     0x15a60a8: add             x4, x4, HEAP, lsl #32
    // 0x15a60ac: cmp             w4, NULL
    // 0x15a60b0: b.ne            #0x15a60bc
    // 0x15a60b4: r4 = Null
    //     0x15a60b4: mov             x4, NULL
    // 0x15a60b8: b               #0x15a60dc
    // 0x15a60bc: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a60bc: ldur            w5, [x4, #0x17]
    // 0x15a60c0: DecompressPointer r5
    //     0x15a60c0: add             x5, x5, HEAP, lsl #32
    // 0x15a60c4: cmp             w5, NULL
    // 0x15a60c8: b.ne            #0x15a60d4
    // 0x15a60cc: r4 = Null
    //     0x15a60cc: mov             x4, NULL
    // 0x15a60d0: b               #0x15a60dc
    // 0x15a60d4: LoadField: r4 = r5->field_b
    //     0x15a60d4: ldur            w4, [x5, #0xb]
    // 0x15a60d8: DecompressPointer r4
    //     0x15a60d8: add             x4, x4, HEAP, lsl #32
    // 0x15a60dc: cmp             w4, NULL
    // 0x15a60e0: b.ne            #0x15a60ec
    // 0x15a60e4: r4 = 0
    //     0x15a60e4: movz            x4, #0
    // 0x15a60e8: b               #0x15a60fc
    // 0x15a60ec: r5 = LoadInt32Instr(r4)
    //     0x15a60ec: sbfx            x5, x4, #1, #0x1f
    //     0x15a60f0: tbz             w4, #0, #0x15a60f8
    //     0x15a60f4: ldur            x5, [x4, #7]
    // 0x15a60f8: mov             x4, x5
    // 0x15a60fc: r17 = -328
    //     0x15a60fc: movn            x17, #0x147
    // 0x15a6100: str             x4, [fp, x17]
    // 0x15a6104: cmp             w2, NULL
    // 0x15a6108: b.ne            #0x15a6114
    // 0x15a610c: r5 = Null
    //     0x15a610c: mov             x5, NULL
    // 0x15a6110: b               #0x15a614c
    // 0x15a6114: LoadField: r5 = r2->field_57
    //     0x15a6114: ldur            w5, [x2, #0x57]
    // 0x15a6118: DecompressPointer r5
    //     0x15a6118: add             x5, x5, HEAP, lsl #32
    // 0x15a611c: cmp             w5, NULL
    // 0x15a6120: b.ne            #0x15a612c
    // 0x15a6124: r5 = Null
    //     0x15a6124: mov             x5, NULL
    // 0x15a6128: b               #0x15a614c
    // 0x15a612c: ArrayLoad: r6 = r5[0]  ; List_4
    //     0x15a612c: ldur            w6, [x5, #0x17]
    // 0x15a6130: DecompressPointer r6
    //     0x15a6130: add             x6, x6, HEAP, lsl #32
    // 0x15a6134: cmp             w6, NULL
    // 0x15a6138: b.ne            #0x15a6144
    // 0x15a613c: r5 = Null
    //     0x15a613c: mov             x5, NULL
    // 0x15a6140: b               #0x15a614c
    // 0x15a6144: LoadField: r5 = r6->field_f
    //     0x15a6144: ldur            w5, [x6, #0xf]
    // 0x15a6148: DecompressPointer r5
    //     0x15a6148: add             x5, x5, HEAP, lsl #32
    // 0x15a614c: cmp             w5, NULL
    // 0x15a6150: b.ne            #0x15a615c
    // 0x15a6154: r5 = 0
    //     0x15a6154: movz            x5, #0
    // 0x15a6158: b               #0x15a616c
    // 0x15a615c: r6 = LoadInt32Instr(r5)
    //     0x15a615c: sbfx            x6, x5, #1, #0x1f
    //     0x15a6160: tbz             w5, #0, #0x15a6168
    //     0x15a6164: ldur            x6, [x5, #7]
    // 0x15a6168: mov             x5, x6
    // 0x15a616c: stur            x5, [fp, #-0xc8]
    // 0x15a6170: r0 = Color()
    //     0x15a6170: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a6174: mov             x1, x0
    // 0x15a6178: r0 = Instance_ColorSpace
    //     0x15a6178: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a617c: stur            x1, [fp, #-0xf8]
    // 0x15a6180: StoreField: r1->field_27 = r0
    //     0x15a6180: stur            w0, [x1, #0x27]
    // 0x15a6184: d0 = 1.000000
    //     0x15a6184: fmov            d0, #1.00000000
    // 0x15a6188: StoreField: r1->field_7 = d0
    //     0x15a6188: stur            d0, [x1, #7]
    // 0x15a618c: r17 = -336
    //     0x15a618c: movn            x17, #0x14f
    // 0x15a6190: ldr             x2, [fp, x17]
    // 0x15a6194: ubfx            x2, x2, #0, #0x20
    // 0x15a6198: and             w3, w2, #0xff
    // 0x15a619c: ubfx            x3, x3, #0, #0x20
    // 0x15a61a0: scvtf           d1, x3
    // 0x15a61a4: d2 = 255.000000
    //     0x15a61a4: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a61a8: fdiv            d3, d1, d2
    // 0x15a61ac: StoreField: r1->field_f = d3
    //     0x15a61ac: stur            d3, [x1, #0xf]
    // 0x15a61b0: r17 = -328
    //     0x15a61b0: movn            x17, #0x147
    // 0x15a61b4: ldr             x2, [fp, x17]
    // 0x15a61b8: ubfx            x2, x2, #0, #0x20
    // 0x15a61bc: and             w3, w2, #0xff
    // 0x15a61c0: ubfx            x3, x3, #0, #0x20
    // 0x15a61c4: scvtf           d1, x3
    // 0x15a61c8: fdiv            d3, d1, d2
    // 0x15a61cc: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a61cc: stur            d3, [x1, #0x17]
    // 0x15a61d0: ldur            x2, [fp, #-0xc8]
    // 0x15a61d4: ubfx            x2, x2, #0, #0x20
    // 0x15a61d8: and             w3, w2, #0xff
    // 0x15a61dc: ubfx            x3, x3, #0, #0x20
    // 0x15a61e0: scvtf           d1, x3
    // 0x15a61e4: fdiv            d3, d1, d2
    // 0x15a61e8: StoreField: r1->field_1f = d3
    //     0x15a61e8: stur            d3, [x1, #0x1f]
    // 0x15a61ec: r0 = IconThemeData()
    //     0x15a61ec: bl              #0x6b2d30  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0x15a61f0: mov             x1, x0
    // 0x15a61f4: ldur            x0, [fp, #-0xf8]
    // 0x15a61f8: stur            x1, [fp, #-0x100]
    // 0x15a61fc: StoreField: r1->field_1b = r0
    //     0x15a61fc: stur            w0, [x1, #0x1b]
    // 0x15a6200: ldur            x0, [fp, #-0xe8]
    // 0x15a6204: cmp             w0, NULL
    // 0x15a6208: b.ne            #0x15a6214
    // 0x15a620c: r2 = Null
    //     0x15a620c: mov             x2, NULL
    // 0x15a6210: b               #0x15a624c
    // 0x15a6214: LoadField: r2 = r0->field_57
    //     0x15a6214: ldur            w2, [x0, #0x57]
    // 0x15a6218: DecompressPointer r2
    //     0x15a6218: add             x2, x2, HEAP, lsl #32
    // 0x15a621c: cmp             w2, NULL
    // 0x15a6220: b.ne            #0x15a622c
    // 0x15a6224: r2 = Null
    //     0x15a6224: mov             x2, NULL
    // 0x15a6228: b               #0x15a624c
    // 0x15a622c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x15a622c: ldur            w3, [x2, #0x17]
    // 0x15a6230: DecompressPointer r3
    //     0x15a6230: add             x3, x3, HEAP, lsl #32
    // 0x15a6234: cmp             w3, NULL
    // 0x15a6238: b.ne            #0x15a6244
    // 0x15a623c: r2 = Null
    //     0x15a623c: mov             x2, NULL
    // 0x15a6240: b               #0x15a624c
    // 0x15a6244: LoadField: r2 = r3->field_7
    //     0x15a6244: ldur            w2, [x3, #7]
    // 0x15a6248: DecompressPointer r2
    //     0x15a6248: add             x2, x2, HEAP, lsl #32
    // 0x15a624c: cmp             w2, NULL
    // 0x15a6250: b.ne            #0x15a625c
    // 0x15a6254: r2 = 0
    //     0x15a6254: movz            x2, #0
    // 0x15a6258: b               #0x15a626c
    // 0x15a625c: r3 = LoadInt32Instr(r2)
    //     0x15a625c: sbfx            x3, x2, #1, #0x1f
    //     0x15a6260: tbz             w2, #0, #0x15a6268
    //     0x15a6264: ldur            x3, [x2, #7]
    // 0x15a6268: mov             x2, x3
    // 0x15a626c: r17 = -336
    //     0x15a626c: movn            x17, #0x14f
    // 0x15a6270: str             x2, [fp, x17]
    // 0x15a6274: cmp             w0, NULL
    // 0x15a6278: b.ne            #0x15a6284
    // 0x15a627c: r3 = Null
    //     0x15a627c: mov             x3, NULL
    // 0x15a6280: b               #0x15a62bc
    // 0x15a6284: LoadField: r3 = r0->field_57
    //     0x15a6284: ldur            w3, [x0, #0x57]
    // 0x15a6288: DecompressPointer r3
    //     0x15a6288: add             x3, x3, HEAP, lsl #32
    // 0x15a628c: cmp             w3, NULL
    // 0x15a6290: b.ne            #0x15a629c
    // 0x15a6294: r3 = Null
    //     0x15a6294: mov             x3, NULL
    // 0x15a6298: b               #0x15a62bc
    // 0x15a629c: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a629c: ldur            w4, [x3, #0x17]
    // 0x15a62a0: DecompressPointer r4
    //     0x15a62a0: add             x4, x4, HEAP, lsl #32
    // 0x15a62a4: cmp             w4, NULL
    // 0x15a62a8: b.ne            #0x15a62b4
    // 0x15a62ac: r3 = Null
    //     0x15a62ac: mov             x3, NULL
    // 0x15a62b0: b               #0x15a62bc
    // 0x15a62b4: LoadField: r3 = r4->field_b
    //     0x15a62b4: ldur            w3, [x4, #0xb]
    // 0x15a62b8: DecompressPointer r3
    //     0x15a62b8: add             x3, x3, HEAP, lsl #32
    // 0x15a62bc: cmp             w3, NULL
    // 0x15a62c0: b.ne            #0x15a62cc
    // 0x15a62c4: r3 = 0
    //     0x15a62c4: movz            x3, #0
    // 0x15a62c8: b               #0x15a62dc
    // 0x15a62cc: r4 = LoadInt32Instr(r3)
    //     0x15a62cc: sbfx            x4, x3, #1, #0x1f
    //     0x15a62d0: tbz             w3, #0, #0x15a62d8
    //     0x15a62d4: ldur            x4, [x3, #7]
    // 0x15a62d8: mov             x3, x4
    // 0x15a62dc: r17 = -328
    //     0x15a62dc: movn            x17, #0x147
    // 0x15a62e0: str             x3, [fp, x17]
    // 0x15a62e4: cmp             w0, NULL
    // 0x15a62e8: b.ne            #0x15a62f4
    // 0x15a62ec: r0 = Null
    //     0x15a62ec: mov             x0, NULL
    // 0x15a62f0: b               #0x15a6330
    // 0x15a62f4: LoadField: r4 = r0->field_57
    //     0x15a62f4: ldur            w4, [x0, #0x57]
    // 0x15a62f8: DecompressPointer r4
    //     0x15a62f8: add             x4, x4, HEAP, lsl #32
    // 0x15a62fc: cmp             w4, NULL
    // 0x15a6300: b.ne            #0x15a630c
    // 0x15a6304: r0 = Null
    //     0x15a6304: mov             x0, NULL
    // 0x15a6308: b               #0x15a6330
    // 0x15a630c: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x15a630c: ldur            w0, [x4, #0x17]
    // 0x15a6310: DecompressPointer r0
    //     0x15a6310: add             x0, x0, HEAP, lsl #32
    // 0x15a6314: cmp             w0, NULL
    // 0x15a6318: b.ne            #0x15a6324
    // 0x15a631c: r0 = Null
    //     0x15a631c: mov             x0, NULL
    // 0x15a6320: b               #0x15a6330
    // 0x15a6324: LoadField: r4 = r0->field_f
    //     0x15a6324: ldur            w4, [x0, #0xf]
    // 0x15a6328: DecompressPointer r4
    //     0x15a6328: add             x4, x4, HEAP, lsl #32
    // 0x15a632c: mov             x0, x4
    // 0x15a6330: cmp             w0, NULL
    // 0x15a6334: b.ne            #0x15a6340
    // 0x15a6338: r4 = 0
    //     0x15a6338: movz            x4, #0
    // 0x15a633c: b               #0x15a634c
    // 0x15a6340: r4 = LoadInt32Instr(r0)
    //     0x15a6340: sbfx            x4, x0, #1, #0x1f
    //     0x15a6344: tbz             w0, #0, #0x15a634c
    //     0x15a6348: ldur            x4, [x0, #7]
    // 0x15a634c: ldur            x0, [fp, #-0xc0]
    // 0x15a6350: stur            x4, [fp, #-0xc8]
    // 0x15a6354: r0 = Color()
    //     0x15a6354: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a6358: mov             x1, x0
    // 0x15a635c: r0 = Instance_ColorSpace
    //     0x15a635c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a6360: stur            x1, [fp, #-0xe8]
    // 0x15a6364: StoreField: r1->field_27 = r0
    //     0x15a6364: stur            w0, [x1, #0x27]
    // 0x15a6368: d0 = 1.000000
    //     0x15a6368: fmov            d0, #1.00000000
    // 0x15a636c: StoreField: r1->field_7 = d0
    //     0x15a636c: stur            d0, [x1, #7]
    // 0x15a6370: r17 = -336
    //     0x15a6370: movn            x17, #0x14f
    // 0x15a6374: ldr             x2, [fp, x17]
    // 0x15a6378: ubfx            x2, x2, #0, #0x20
    // 0x15a637c: and             w3, w2, #0xff
    // 0x15a6380: ubfx            x3, x3, #0, #0x20
    // 0x15a6384: scvtf           d1, x3
    // 0x15a6388: d2 = 255.000000
    //     0x15a6388: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a638c: fdiv            d3, d1, d2
    // 0x15a6390: StoreField: r1->field_f = d3
    //     0x15a6390: stur            d3, [x1, #0xf]
    // 0x15a6394: r17 = -328
    //     0x15a6394: movn            x17, #0x147
    // 0x15a6398: ldr             x2, [fp, x17]
    // 0x15a639c: ubfx            x2, x2, #0, #0x20
    // 0x15a63a0: and             w3, w2, #0xff
    // 0x15a63a4: ubfx            x3, x3, #0, #0x20
    // 0x15a63a8: scvtf           d1, x3
    // 0x15a63ac: fdiv            d3, d1, d2
    // 0x15a63b0: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a63b0: stur            d3, [x1, #0x17]
    // 0x15a63b4: ldur            x2, [fp, #-0xc8]
    // 0x15a63b8: ubfx            x2, x2, #0, #0x20
    // 0x15a63bc: and             w3, w2, #0xff
    // 0x15a63c0: ubfx            x3, x3, #0, #0x20
    // 0x15a63c4: scvtf           d1, x3
    // 0x15a63c8: fdiv            d3, d1, d2
    // 0x15a63cc: StoreField: r1->field_1f = d3
    //     0x15a63cc: stur            d3, [x1, #0x1f]
    // 0x15a63d0: r0 = IconThemeData()
    //     0x15a63d0: bl              #0x6b2d30  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0x15a63d4: mov             x1, x0
    // 0x15a63d8: ldur            x0, [fp, #-0xe8]
    // 0x15a63dc: stur            x1, [fp, #-0xf8]
    // 0x15a63e0: StoreField: r1->field_1b = r0
    //     0x15a63e0: stur            w0, [x1, #0x1b]
    // 0x15a63e4: r0 = AppBarTheme()
    //     0x15a63e4: bl              #0x7958fc  ; AllocateAppBarThemeStub -> AppBarTheme (size=0x58)
    // 0x15a63e8: mov             x2, x0
    // 0x15a63ec: ldur            x0, [fp, #-0xf8]
    // 0x15a63f0: stur            x2, [fp, #-0xe8]
    // 0x15a63f4: StoreField: r2->field_2f = r0
    //     0x15a63f4: stur            w0, [x2, #0x2f]
    // 0x15a63f8: ldur            x0, [fp, #-0x100]
    // 0x15a63fc: StoreField: r2->field_33 = r0
    //     0x15a63fc: stur            w0, [x2, #0x33]
    // 0x15a6400: r0 = Instance_SizedBox
    //     0x15a6400: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f58] Obj!SizedBox@d67da1
    //     0x15a6404: ldr             x0, [x0, #0xf58]
    // 0x15a6408: StoreField: r2->field_b = r0
    //     0x15a6408: stur            w0, [x2, #0xb]
    // 0x15a640c: mov             x1, x2
    // 0x15a6410: r0 = _NativeScene._()
    //     0x15a6410: bl              #0x16ed860  ; [dart:ui] _NativeScene::_NativeScene._
    // 0x15a6414: ldur            x0, [fp, #-0xc0]
    // 0x15a6418: LoadField: r1 = r0->field_b
    //     0x15a6418: ldur            w1, [x0, #0xb]
    // 0x15a641c: DecompressPointer r1
    //     0x15a641c: add             x1, x1, HEAP, lsl #32
    // 0x15a6420: cmp             w1, NULL
    // 0x15a6424: b.ne            #0x15a6430
    // 0x15a6428: r2 = Null
    //     0x15a6428: mov             x2, NULL
    // 0x15a642c: b               #0x15a6468
    // 0x15a6430: LoadField: r2 = r1->field_57
    //     0x15a6430: ldur            w2, [x1, #0x57]
    // 0x15a6434: DecompressPointer r2
    //     0x15a6434: add             x2, x2, HEAP, lsl #32
    // 0x15a6438: cmp             w2, NULL
    // 0x15a643c: b.ne            #0x15a6448
    // 0x15a6440: r2 = Null
    //     0x15a6440: mov             x2, NULL
    // 0x15a6444: b               #0x15a6468
    // 0x15a6448: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x15a6448: ldur            w3, [x2, #0x17]
    // 0x15a644c: DecompressPointer r3
    //     0x15a644c: add             x3, x3, HEAP, lsl #32
    // 0x15a6450: cmp             w3, NULL
    // 0x15a6454: b.ne            #0x15a6460
    // 0x15a6458: r2 = Null
    //     0x15a6458: mov             x2, NULL
    // 0x15a645c: b               #0x15a6468
    // 0x15a6460: LoadField: r2 = r3->field_7
    //     0x15a6460: ldur            w2, [x3, #7]
    // 0x15a6464: DecompressPointer r2
    //     0x15a6464: add             x2, x2, HEAP, lsl #32
    // 0x15a6468: cmp             w2, NULL
    // 0x15a646c: b.ne            #0x15a6478
    // 0x15a6470: r2 = 0
    //     0x15a6470: movz            x2, #0
    // 0x15a6474: b               #0x15a6488
    // 0x15a6478: r3 = LoadInt32Instr(r2)
    //     0x15a6478: sbfx            x3, x2, #1, #0x1f
    //     0x15a647c: tbz             w2, #0, #0x15a6484
    //     0x15a6480: ldur            x3, [x2, #7]
    // 0x15a6484: mov             x2, x3
    // 0x15a6488: r17 = -336
    //     0x15a6488: movn            x17, #0x14f
    // 0x15a648c: str             x2, [fp, x17]
    // 0x15a6490: cmp             w1, NULL
    // 0x15a6494: b.ne            #0x15a64a0
    // 0x15a6498: r3 = Null
    //     0x15a6498: mov             x3, NULL
    // 0x15a649c: b               #0x15a64d8
    // 0x15a64a0: LoadField: r3 = r1->field_57
    //     0x15a64a0: ldur            w3, [x1, #0x57]
    // 0x15a64a4: DecompressPointer r3
    //     0x15a64a4: add             x3, x3, HEAP, lsl #32
    // 0x15a64a8: cmp             w3, NULL
    // 0x15a64ac: b.ne            #0x15a64b8
    // 0x15a64b0: r3 = Null
    //     0x15a64b0: mov             x3, NULL
    // 0x15a64b4: b               #0x15a64d8
    // 0x15a64b8: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a64b8: ldur            w4, [x3, #0x17]
    // 0x15a64bc: DecompressPointer r4
    //     0x15a64bc: add             x4, x4, HEAP, lsl #32
    // 0x15a64c0: cmp             w4, NULL
    // 0x15a64c4: b.ne            #0x15a64d0
    // 0x15a64c8: r3 = Null
    //     0x15a64c8: mov             x3, NULL
    // 0x15a64cc: b               #0x15a64d8
    // 0x15a64d0: LoadField: r3 = r4->field_b
    //     0x15a64d0: ldur            w3, [x4, #0xb]
    // 0x15a64d4: DecompressPointer r3
    //     0x15a64d4: add             x3, x3, HEAP, lsl #32
    // 0x15a64d8: cmp             w3, NULL
    // 0x15a64dc: b.ne            #0x15a64e8
    // 0x15a64e0: r3 = 0
    //     0x15a64e0: movz            x3, #0
    // 0x15a64e4: b               #0x15a64f8
    // 0x15a64e8: r4 = LoadInt32Instr(r3)
    //     0x15a64e8: sbfx            x4, x3, #1, #0x1f
    //     0x15a64ec: tbz             w3, #0, #0x15a64f4
    //     0x15a64f0: ldur            x4, [x3, #7]
    // 0x15a64f4: mov             x3, x4
    // 0x15a64f8: r17 = -328
    //     0x15a64f8: movn            x17, #0x147
    // 0x15a64fc: str             x3, [fp, x17]
    // 0x15a6500: cmp             w1, NULL
    // 0x15a6504: b.ne            #0x15a6510
    // 0x15a6508: r1 = Null
    //     0x15a6508: mov             x1, NULL
    // 0x15a650c: b               #0x15a654c
    // 0x15a6510: LoadField: r4 = r1->field_57
    //     0x15a6510: ldur            w4, [x1, #0x57]
    // 0x15a6514: DecompressPointer r4
    //     0x15a6514: add             x4, x4, HEAP, lsl #32
    // 0x15a6518: cmp             w4, NULL
    // 0x15a651c: b.ne            #0x15a6528
    // 0x15a6520: r1 = Null
    //     0x15a6520: mov             x1, NULL
    // 0x15a6524: b               #0x15a654c
    // 0x15a6528: ArrayLoad: r1 = r4[0]  ; List_4
    //     0x15a6528: ldur            w1, [x4, #0x17]
    // 0x15a652c: DecompressPointer r1
    //     0x15a652c: add             x1, x1, HEAP, lsl #32
    // 0x15a6530: cmp             w1, NULL
    // 0x15a6534: b.ne            #0x15a6540
    // 0x15a6538: r1 = Null
    //     0x15a6538: mov             x1, NULL
    // 0x15a653c: b               #0x15a654c
    // 0x15a6540: LoadField: r4 = r1->field_f
    //     0x15a6540: ldur            w4, [x1, #0xf]
    // 0x15a6544: DecompressPointer r4
    //     0x15a6544: add             x4, x4, HEAP, lsl #32
    // 0x15a6548: mov             x1, x4
    // 0x15a654c: cmp             w1, NULL
    // 0x15a6550: b.ne            #0x15a655c
    // 0x15a6554: r1 = 0
    //     0x15a6554: movz            x1, #0
    // 0x15a6558: b               #0x15a656c
    // 0x15a655c: r4 = LoadInt32Instr(r1)
    //     0x15a655c: sbfx            x4, x1, #1, #0x1f
    //     0x15a6560: tbz             w1, #0, #0x15a6568
    //     0x15a6564: ldur            x4, [x1, #7]
    // 0x15a6568: mov             x1, x4
    // 0x15a656c: stur            x1, [fp, #-0xc8]
    // 0x15a6570: r0 = Color()
    //     0x15a6570: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a6574: mov             x1, x0
    // 0x15a6578: r0 = Instance_ColorSpace
    //     0x15a6578: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a657c: StoreField: r1->field_27 = r0
    //     0x15a657c: stur            w0, [x1, #0x27]
    // 0x15a6580: d0 = 1.000000
    //     0x15a6580: fmov            d0, #1.00000000
    // 0x15a6584: StoreField: r1->field_7 = d0
    //     0x15a6584: stur            d0, [x1, #7]
    // 0x15a6588: r17 = -336
    //     0x15a6588: movn            x17, #0x14f
    // 0x15a658c: ldr             x2, [fp, x17]
    // 0x15a6590: ubfx            x2, x2, #0, #0x20
    // 0x15a6594: and             w3, w2, #0xff
    // 0x15a6598: ubfx            x3, x3, #0, #0x20
    // 0x15a659c: scvtf           d1, x3
    // 0x15a65a0: d2 = 255.000000
    //     0x15a65a0: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a65a4: fdiv            d3, d1, d2
    // 0x15a65a8: StoreField: r1->field_f = d3
    //     0x15a65a8: stur            d3, [x1, #0xf]
    // 0x15a65ac: r17 = -328
    //     0x15a65ac: movn            x17, #0x147
    // 0x15a65b0: ldr             x2, [fp, x17]
    // 0x15a65b4: ubfx            x2, x2, #0, #0x20
    // 0x15a65b8: and             w3, w2, #0xff
    // 0x15a65bc: ubfx            x3, x3, #0, #0x20
    // 0x15a65c0: scvtf           d1, x3
    // 0x15a65c4: fdiv            d3, d1, d2
    // 0x15a65c8: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a65c8: stur            d3, [x1, #0x17]
    // 0x15a65cc: ldur            x2, [fp, #-0xc8]
    // 0x15a65d0: ubfx            x2, x2, #0, #0x20
    // 0x15a65d4: and             w3, w2, #0xff
    // 0x15a65d8: ubfx            x3, x3, #0, #0x20
    // 0x15a65dc: scvtf           d1, x3
    // 0x15a65e0: fdiv            d3, d1, d2
    // 0x15a65e4: StoreField: r1->field_1f = d3
    //     0x15a65e4: stur            d3, [x1, #0x1f]
    // 0x15a65e8: str             x1, [SP]
    // 0x15a65ec: r1 = Instance_IconThemeData
    //     0x15a65ec: ldr             x1, [PP, #0x5560]  ; [pp+0x5560] Obj!IconThemeData@d64df1
    // 0x15a65f0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15a65f0: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15a65f4: ldr             x4, [x4, #0xf40]
    // 0x15a65f8: r0 = copyWith()
    //     0x15a65f8: bl              #0x16abf9c  ; [package:flutter/src/widgets/icon_theme_data.dart] IconThemeData::copyWith
    // 0x15a65fc: r1 = "Montserrat"
    //     0x15a65fc: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f50] "Montserrat"
    //     0x15a6600: ldr             x1, [x1, #0xf50]
    // 0x15a6604: stur            x0, [fp, #-0xf8]
    // 0x15a6608: r0 = getFont()
    //     0x15a6608: bl              #0xc994b0  ; [package:google_fonts/google_fonts.dart] GoogleFonts::getFont
    // 0x15a660c: mov             x2, x0
    // 0x15a6610: ldur            x0, [fp, #-0xc0]
    // 0x15a6614: stur            x2, [fp, #-0x100]
    // 0x15a6618: LoadField: r1 = r0->field_b
    //     0x15a6618: ldur            w1, [x0, #0xb]
    // 0x15a661c: DecompressPointer r1
    //     0x15a661c: add             x1, x1, HEAP, lsl #32
    // 0x15a6620: cmp             w1, NULL
    // 0x15a6624: b.ne            #0x15a6630
    // 0x15a6628: r1 = Null
    //     0x15a6628: mov             x1, NULL
    // 0x15a662c: b               #0x15a6680
    // 0x15a6630: LoadField: r3 = r1->field_57
    //     0x15a6630: ldur            w3, [x1, #0x57]
    // 0x15a6634: DecompressPointer r3
    //     0x15a6634: add             x3, x3, HEAP, lsl #32
    // 0x15a6638: cmp             w3, NULL
    // 0x15a663c: b.ne            #0x15a6648
    // 0x15a6640: r1 = Null
    //     0x15a6640: mov             x1, NULL
    // 0x15a6644: b               #0x15a6680
    // 0x15a6648: LoadField: r1 = r3->field_13
    //     0x15a6648: ldur            w1, [x3, #0x13]
    // 0x15a664c: DecompressPointer r1
    //     0x15a664c: add             x1, x1, HEAP, lsl #32
    // 0x15a6650: cmp             w1, NULL
    // 0x15a6654: b.ne            #0x15a6660
    // 0x15a6658: r1 = Null
    //     0x15a6658: mov             x1, NULL
    // 0x15a665c: b               #0x15a6680
    // 0x15a6660: LoadField: r3 = r1->field_b
    //     0x15a6660: ldur            w3, [x1, #0xb]
    // 0x15a6664: DecompressPointer r3
    //     0x15a6664: add             x3, x3, HEAP, lsl #32
    // 0x15a6668: cmp             w3, NULL
    // 0x15a666c: b.ne            #0x15a6678
    // 0x15a6670: r1 = Null
    //     0x15a6670: mov             x1, NULL
    // 0x15a6674: b               #0x15a6680
    // 0x15a6678: LoadField: r1 = r3->field_b
    //     0x15a6678: ldur            w1, [x3, #0xb]
    // 0x15a667c: DecompressPointer r1
    //     0x15a667c: add             x1, x1, HEAP, lsl #32
    // 0x15a6680: r0 = getFontWeight()
    //     0x15a6680: bl              #0xc95834  ; [package:customer_app/app/core/utils/utils.dart] ::getFontWeight
    // 0x15a6684: r16 = Instance_Color
    //     0x15a6684: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15a6688: stp             x16, x0, [SP]
    // 0x15a668c: ldur            x1, [fp, #-0x100]
    // 0x15a6690: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontWeight, 0x1, null]
    //     0x15a6690: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f60] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontWeight", 0x1, Null]
    //     0x15a6694: ldr             x4, [x4, #0xf60]
    // 0x15a6698: r0 = copyWith()
    //     0x15a6698: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15a669c: r1 = "Montserrat"
    //     0x15a669c: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f50] "Montserrat"
    //     0x15a66a0: ldr             x1, [x1, #0xf50]
    // 0x15a66a4: stur            x0, [fp, #-0x100]
    // 0x15a66a8: r0 = getFont()
    //     0x15a66a8: bl              #0xc994b0  ; [package:google_fonts/google_fonts.dart] GoogleFonts::getFont
    // 0x15a66ac: mov             x2, x0
    // 0x15a66b0: ldur            x0, [fp, #-0xc0]
    // 0x15a66b4: r17 = -264
    //     0x15a66b4: movn            x17, #0x107
    // 0x15a66b8: str             x2, [fp, x17]
    // 0x15a66bc: LoadField: r1 = r0->field_b
    //     0x15a66bc: ldur            w1, [x0, #0xb]
    // 0x15a66c0: DecompressPointer r1
    //     0x15a66c0: add             x1, x1, HEAP, lsl #32
    // 0x15a66c4: cmp             w1, NULL
    // 0x15a66c8: b.ne            #0x15a66d4
    // 0x15a66cc: r1 = Null
    //     0x15a66cc: mov             x1, NULL
    // 0x15a66d0: b               #0x15a6724
    // 0x15a66d4: LoadField: r3 = r1->field_57
    //     0x15a66d4: ldur            w3, [x1, #0x57]
    // 0x15a66d8: DecompressPointer r3
    //     0x15a66d8: add             x3, x3, HEAP, lsl #32
    // 0x15a66dc: cmp             w3, NULL
    // 0x15a66e0: b.ne            #0x15a66ec
    // 0x15a66e4: r1 = Null
    //     0x15a66e4: mov             x1, NULL
    // 0x15a66e8: b               #0x15a6724
    // 0x15a66ec: LoadField: r1 = r3->field_13
    //     0x15a66ec: ldur            w1, [x3, #0x13]
    // 0x15a66f0: DecompressPointer r1
    //     0x15a66f0: add             x1, x1, HEAP, lsl #32
    // 0x15a66f4: cmp             w1, NULL
    // 0x15a66f8: b.ne            #0x15a6704
    // 0x15a66fc: r1 = Null
    //     0x15a66fc: mov             x1, NULL
    // 0x15a6700: b               #0x15a6724
    // 0x15a6704: LoadField: r3 = r1->field_7
    //     0x15a6704: ldur            w3, [x1, #7]
    // 0x15a6708: DecompressPointer r3
    //     0x15a6708: add             x3, x3, HEAP, lsl #32
    // 0x15a670c: cmp             w3, NULL
    // 0x15a6710: b.ne            #0x15a671c
    // 0x15a6714: r1 = Null
    //     0x15a6714: mov             x1, NULL
    // 0x15a6718: b               #0x15a6724
    // 0x15a671c: LoadField: r1 = r3->field_b
    //     0x15a671c: ldur            w1, [x3, #0xb]
    // 0x15a6720: DecompressPointer r1
    //     0x15a6720: add             x1, x1, HEAP, lsl #32
    // 0x15a6724: r0 = getFontWeight()
    //     0x15a6724: bl              #0xc95834  ; [package:customer_app/app/core/utils/utils.dart] ::getFontWeight
    // 0x15a6728: r16 = Instance_Color
    //     0x15a6728: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15a672c: stp             x16, x0, [SP]
    // 0x15a6730: r17 = -264
    //     0x15a6730: movn            x17, #0x107
    // 0x15a6734: ldr             x1, [fp, x17]
    // 0x15a6738: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontWeight, 0x1, null]
    //     0x15a6738: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f60] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontWeight", 0x1, Null]
    //     0x15a673c: ldr             x4, [x4, #0xf60]
    // 0x15a6740: r0 = copyWith()
    //     0x15a6740: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15a6744: r1 = "Montserrat"
    //     0x15a6744: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f50] "Montserrat"
    //     0x15a6748: ldr             x1, [x1, #0xf50]
    // 0x15a674c: r17 = -264
    //     0x15a674c: movn            x17, #0x107
    // 0x15a6750: str             x0, [fp, x17]
    // 0x15a6754: r0 = getFont()
    //     0x15a6754: bl              #0xc994b0  ; [package:google_fonts/google_fonts.dart] GoogleFonts::getFont
    // 0x15a6758: mov             x2, x0
    // 0x15a675c: ldur            x0, [fp, #-0xc0]
    // 0x15a6760: r17 = -272
    //     0x15a6760: movn            x17, #0x10f
    // 0x15a6764: str             x2, [fp, x17]
    // 0x15a6768: LoadField: r1 = r0->field_b
    //     0x15a6768: ldur            w1, [x0, #0xb]
    // 0x15a676c: DecompressPointer r1
    //     0x15a676c: add             x1, x1, HEAP, lsl #32
    // 0x15a6770: cmp             w1, NULL
    // 0x15a6774: b.ne            #0x15a6780
    // 0x15a6778: r1 = Null
    //     0x15a6778: mov             x1, NULL
    // 0x15a677c: b               #0x15a67d0
    // 0x15a6780: LoadField: r3 = r1->field_57
    //     0x15a6780: ldur            w3, [x1, #0x57]
    // 0x15a6784: DecompressPointer r3
    //     0x15a6784: add             x3, x3, HEAP, lsl #32
    // 0x15a6788: cmp             w3, NULL
    // 0x15a678c: b.ne            #0x15a6798
    // 0x15a6790: r1 = Null
    //     0x15a6790: mov             x1, NULL
    // 0x15a6794: b               #0x15a67d0
    // 0x15a6798: LoadField: r1 = r3->field_13
    //     0x15a6798: ldur            w1, [x3, #0x13]
    // 0x15a679c: DecompressPointer r1
    //     0x15a679c: add             x1, x1, HEAP, lsl #32
    // 0x15a67a0: cmp             w1, NULL
    // 0x15a67a4: b.ne            #0x15a67b0
    // 0x15a67a8: r1 = Null
    //     0x15a67a8: mov             x1, NULL
    // 0x15a67ac: b               #0x15a67d0
    // 0x15a67b0: LoadField: r3 = r1->field_f
    //     0x15a67b0: ldur            w3, [x1, #0xf]
    // 0x15a67b4: DecompressPointer r3
    //     0x15a67b4: add             x3, x3, HEAP, lsl #32
    // 0x15a67b8: cmp             w3, NULL
    // 0x15a67bc: b.ne            #0x15a67c8
    // 0x15a67c0: r1 = Null
    //     0x15a67c0: mov             x1, NULL
    // 0x15a67c4: b               #0x15a67d0
    // 0x15a67c8: LoadField: r1 = r3->field_b
    //     0x15a67c8: ldur            w1, [x3, #0xb]
    // 0x15a67cc: DecompressPointer r1
    //     0x15a67cc: add             x1, x1, HEAP, lsl #32
    // 0x15a67d0: r0 = getFontWeight()
    //     0x15a67d0: bl              #0xc95834  ; [package:customer_app/app/core/utils/utils.dart] ::getFontWeight
    // 0x15a67d4: r16 = Instance_Color
    //     0x15a67d4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15a67d8: stp             x16, x0, [SP]
    // 0x15a67dc: r17 = -272
    //     0x15a67dc: movn            x17, #0x10f
    // 0x15a67e0: ldr             x1, [fp, x17]
    // 0x15a67e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontWeight, 0x1, null]
    //     0x15a67e4: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f60] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontWeight", 0x1, Null]
    //     0x15a67e8: ldr             x4, [x4, #0xf60]
    // 0x15a67ec: r0 = copyWith()
    //     0x15a67ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15a67f0: r1 = "Montserrat"
    //     0x15a67f0: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f50] "Montserrat"
    //     0x15a67f4: ldr             x1, [x1, #0xf50]
    // 0x15a67f8: r17 = -272
    //     0x15a67f8: movn            x17, #0x10f
    // 0x15a67fc: str             x0, [fp, x17]
    // 0x15a6800: r0 = getFont()
    //     0x15a6800: bl              #0xc994b0  ; [package:google_fonts/google_fonts.dart] GoogleFonts::getFont
    // 0x15a6804: mov             x2, x0
    // 0x15a6808: ldur            x0, [fp, #-0xc0]
    // 0x15a680c: r17 = -280
    //     0x15a680c: movn            x17, #0x117
    // 0x15a6810: str             x2, [fp, x17]
    // 0x15a6814: LoadField: r1 = r0->field_b
    //     0x15a6814: ldur            w1, [x0, #0xb]
    // 0x15a6818: DecompressPointer r1
    //     0x15a6818: add             x1, x1, HEAP, lsl #32
    // 0x15a681c: cmp             w1, NULL
    // 0x15a6820: b.ne            #0x15a682c
    // 0x15a6824: r1 = Null
    //     0x15a6824: mov             x1, NULL
    // 0x15a6828: b               #0x15a687c
    // 0x15a682c: LoadField: r3 = r1->field_57
    //     0x15a682c: ldur            w3, [x1, #0x57]
    // 0x15a6830: DecompressPointer r3
    //     0x15a6830: add             x3, x3, HEAP, lsl #32
    // 0x15a6834: cmp             w3, NULL
    // 0x15a6838: b.ne            #0x15a6844
    // 0x15a683c: r1 = Null
    //     0x15a683c: mov             x1, NULL
    // 0x15a6840: b               #0x15a687c
    // 0x15a6844: LoadField: r1 = r3->field_13
    //     0x15a6844: ldur            w1, [x3, #0x13]
    // 0x15a6848: DecompressPointer r1
    //     0x15a6848: add             x1, x1, HEAP, lsl #32
    // 0x15a684c: cmp             w1, NULL
    // 0x15a6850: b.ne            #0x15a685c
    // 0x15a6854: r1 = Null
    //     0x15a6854: mov             x1, NULL
    // 0x15a6858: b               #0x15a687c
    // 0x15a685c: LoadField: r3 = r1->field_13
    //     0x15a685c: ldur            w3, [x1, #0x13]
    // 0x15a6860: DecompressPointer r3
    //     0x15a6860: add             x3, x3, HEAP, lsl #32
    // 0x15a6864: cmp             w3, NULL
    // 0x15a6868: b.ne            #0x15a6874
    // 0x15a686c: r1 = Null
    //     0x15a686c: mov             x1, NULL
    // 0x15a6870: b               #0x15a687c
    // 0x15a6874: LoadField: r1 = r3->field_b
    //     0x15a6874: ldur            w1, [x3, #0xb]
    // 0x15a6878: DecompressPointer r1
    //     0x15a6878: add             x1, x1, HEAP, lsl #32
    // 0x15a687c: r0 = getFontWeight()
    //     0x15a687c: bl              #0xc95834  ; [package:customer_app/app/core/utils/utils.dart] ::getFontWeight
    // 0x15a6880: r16 = Instance_Color
    //     0x15a6880: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15a6884: stp             x16, x0, [SP]
    // 0x15a6888: r17 = -280
    //     0x15a6888: movn            x17, #0x117
    // 0x15a688c: ldr             x1, [fp, x17]
    // 0x15a6890: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontWeight, 0x1, null]
    //     0x15a6890: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f60] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontWeight", 0x1, Null]
    //     0x15a6894: ldr             x4, [x4, #0xf60]
    // 0x15a6898: r0 = copyWith()
    //     0x15a6898: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15a689c: r1 = "Montserrat"
    //     0x15a689c: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f50] "Montserrat"
    //     0x15a68a0: ldr             x1, [x1, #0xf50]
    // 0x15a68a4: r17 = -280
    //     0x15a68a4: movn            x17, #0x117
    // 0x15a68a8: str             x0, [fp, x17]
    // 0x15a68ac: r0 = getFont()
    //     0x15a68ac: bl              #0xc994b0  ; [package:google_fonts/google_fonts.dart] GoogleFonts::getFont
    // 0x15a68b0: mov             x2, x0
    // 0x15a68b4: ldur            x0, [fp, #-0xc0]
    // 0x15a68b8: r17 = -296
    //     0x15a68b8: movn            x17, #0x127
    // 0x15a68bc: str             x2, [fp, x17]
    // 0x15a68c0: LoadField: r1 = r0->field_b
    //     0x15a68c0: ldur            w1, [x0, #0xb]
    // 0x15a68c4: DecompressPointer r1
    //     0x15a68c4: add             x1, x1, HEAP, lsl #32
    // 0x15a68c8: cmp             w1, NULL
    // 0x15a68cc: b.ne            #0x15a68d8
    // 0x15a68d0: r1 = Null
    //     0x15a68d0: mov             x1, NULL
    // 0x15a68d4: b               #0x15a6928
    // 0x15a68d8: LoadField: r3 = r1->field_57
    //     0x15a68d8: ldur            w3, [x1, #0x57]
    // 0x15a68dc: DecompressPointer r3
    //     0x15a68dc: add             x3, x3, HEAP, lsl #32
    // 0x15a68e0: cmp             w3, NULL
    // 0x15a68e4: b.ne            #0x15a68f0
    // 0x15a68e8: r1 = Null
    //     0x15a68e8: mov             x1, NULL
    // 0x15a68ec: b               #0x15a6928
    // 0x15a68f0: LoadField: r1 = r3->field_13
    //     0x15a68f0: ldur            w1, [x3, #0x13]
    // 0x15a68f4: DecompressPointer r1
    //     0x15a68f4: add             x1, x1, HEAP, lsl #32
    // 0x15a68f8: cmp             w1, NULL
    // 0x15a68fc: b.ne            #0x15a6908
    // 0x15a6900: r1 = Null
    //     0x15a6900: mov             x1, NULL
    // 0x15a6904: b               #0x15a6928
    // 0x15a6908: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x15a6908: ldur            w3, [x1, #0x17]
    // 0x15a690c: DecompressPointer r3
    //     0x15a690c: add             x3, x3, HEAP, lsl #32
    // 0x15a6910: cmp             w3, NULL
    // 0x15a6914: b.ne            #0x15a6920
    // 0x15a6918: r1 = Null
    //     0x15a6918: mov             x1, NULL
    // 0x15a691c: b               #0x15a6928
    // 0x15a6920: LoadField: r1 = r3->field_b
    //     0x15a6920: ldur            w1, [x3, #0xb]
    // 0x15a6924: DecompressPointer r1
    //     0x15a6924: add             x1, x1, HEAP, lsl #32
    // 0x15a6928: ldur            x6, [fp, #-0x100]
    // 0x15a692c: r17 = -264
    //     0x15a692c: movn            x17, #0x107
    // 0x15a6930: ldr             x5, [fp, x17]
    // 0x15a6934: r17 = -272
    //     0x15a6934: movn            x17, #0x10f
    // 0x15a6938: ldr             x4, [fp, x17]
    // 0x15a693c: r17 = -280
    //     0x15a693c: movn            x17, #0x117
    // 0x15a6940: ldr             x3, [fp, x17]
    // 0x15a6944: r0 = getFontWeight()
    //     0x15a6944: bl              #0xc95834  ; [package:customer_app/app/core/utils/utils.dart] ::getFontWeight
    // 0x15a6948: r16 = Instance_Color
    //     0x15a6948: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15a694c: stp             x16, x0, [SP]
    // 0x15a6950: r17 = -296
    //     0x15a6950: movn            x17, #0x127
    // 0x15a6954: ldr             x1, [fp, x17]
    // 0x15a6958: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontWeight, 0x1, null]
    //     0x15a6958: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f60] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontWeight", 0x1, Null]
    //     0x15a695c: ldr             x4, [x4, #0xf60]
    // 0x15a6960: r0 = copyWith()
    //     0x15a6960: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15a6964: r17 = -296
    //     0x15a6964: movn            x17, #0x127
    // 0x15a6968: str             x0, [fp, x17]
    // 0x15a696c: r0 = TextTheme()
    //     0x15a696c: bl              #0x6afc20  ; AllocateTextThemeStub -> TextTheme (size=0x44)
    // 0x15a6970: mov             x1, x0
    // 0x15a6974: r17 = -280
    //     0x15a6974: movn            x17, #0x117
    // 0x15a6978: ldr             x0, [fp, x17]
    // 0x15a697c: r17 = -304
    //     0x15a697c: movn            x17, #0x12f
    // 0x15a6980: str             x1, [fp, x17]
    // 0x15a6984: StoreField: r1->field_7 = r0
    //     0x15a6984: stur            w0, [x1, #7]
    // 0x15a6988: r17 = -264
    //     0x15a6988: movn            x17, #0x107
    // 0x15a698c: ldr             x0, [fp, x17]
    // 0x15a6990: StoreField: r1->field_1f = r0
    //     0x15a6990: stur            w0, [x1, #0x1f]
    // 0x15a6994: ldur            x0, [fp, #-0x100]
    // 0x15a6998: StoreField: r1->field_23 = r0
    //     0x15a6998: stur            w0, [x1, #0x23]
    // 0x15a699c: r17 = -272
    //     0x15a699c: movn            x17, #0x10f
    // 0x15a69a0: ldr             x0, [fp, x17]
    // 0x15a69a4: StoreField: r1->field_27 = r0
    //     0x15a69a4: stur            w0, [x1, #0x27]
    // 0x15a69a8: r17 = -296
    //     0x15a69a8: movn            x17, #0x127
    // 0x15a69ac: ldr             x0, [fp, x17]
    // 0x15a69b0: StoreField: r1->field_2b = r0
    //     0x15a69b0: stur            w0, [x1, #0x2b]
    // 0x15a69b4: ldur            x0, [fp, #-0xc0]
    // 0x15a69b8: LoadField: r2 = r0->field_b
    //     0x15a69b8: ldur            w2, [x0, #0xb]
    // 0x15a69bc: DecompressPointer r2
    //     0x15a69bc: add             x2, x2, HEAP, lsl #32
    // 0x15a69c0: cmp             w2, NULL
    // 0x15a69c4: b.ne            #0x15a69d0
    // 0x15a69c8: r3 = Null
    //     0x15a69c8: mov             x3, NULL
    // 0x15a69cc: b               #0x15a6a08
    // 0x15a69d0: LoadField: r3 = r2->field_57
    //     0x15a69d0: ldur            w3, [x2, #0x57]
    // 0x15a69d4: DecompressPointer r3
    //     0x15a69d4: add             x3, x3, HEAP, lsl #32
    // 0x15a69d8: cmp             w3, NULL
    // 0x15a69dc: b.ne            #0x15a69e8
    // 0x15a69e0: r3 = Null
    //     0x15a69e0: mov             x3, NULL
    // 0x15a69e4: b               #0x15a6a08
    // 0x15a69e8: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a69e8: ldur            w4, [x3, #0x17]
    // 0x15a69ec: DecompressPointer r4
    //     0x15a69ec: add             x4, x4, HEAP, lsl #32
    // 0x15a69f0: cmp             w4, NULL
    // 0x15a69f4: b.ne            #0x15a6a00
    // 0x15a69f8: r3 = Null
    //     0x15a69f8: mov             x3, NULL
    // 0x15a69fc: b               #0x15a6a08
    // 0x15a6a00: LoadField: r3 = r4->field_7
    //     0x15a6a00: ldur            w3, [x4, #7]
    // 0x15a6a04: DecompressPointer r3
    //     0x15a6a04: add             x3, x3, HEAP, lsl #32
    // 0x15a6a08: cmp             w3, NULL
    // 0x15a6a0c: b.ne            #0x15a6a18
    // 0x15a6a10: r3 = 0
    //     0x15a6a10: movz            x3, #0
    // 0x15a6a14: b               #0x15a6a28
    // 0x15a6a18: r4 = LoadInt32Instr(r3)
    //     0x15a6a18: sbfx            x4, x3, #1, #0x1f
    //     0x15a6a1c: tbz             w3, #0, #0x15a6a24
    //     0x15a6a20: ldur            x4, [x3, #7]
    // 0x15a6a24: mov             x3, x4
    // 0x15a6a28: r17 = -336
    //     0x15a6a28: movn            x17, #0x14f
    // 0x15a6a2c: str             x3, [fp, x17]
    // 0x15a6a30: cmp             w2, NULL
    // 0x15a6a34: b.ne            #0x15a6a40
    // 0x15a6a38: r4 = Null
    //     0x15a6a38: mov             x4, NULL
    // 0x15a6a3c: b               #0x15a6a78
    // 0x15a6a40: LoadField: r4 = r2->field_57
    //     0x15a6a40: ldur            w4, [x2, #0x57]
    // 0x15a6a44: DecompressPointer r4
    //     0x15a6a44: add             x4, x4, HEAP, lsl #32
    // 0x15a6a48: cmp             w4, NULL
    // 0x15a6a4c: b.ne            #0x15a6a58
    // 0x15a6a50: r4 = Null
    //     0x15a6a50: mov             x4, NULL
    // 0x15a6a54: b               #0x15a6a78
    // 0x15a6a58: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a6a58: ldur            w5, [x4, #0x17]
    // 0x15a6a5c: DecompressPointer r5
    //     0x15a6a5c: add             x5, x5, HEAP, lsl #32
    // 0x15a6a60: cmp             w5, NULL
    // 0x15a6a64: b.ne            #0x15a6a70
    // 0x15a6a68: r4 = Null
    //     0x15a6a68: mov             x4, NULL
    // 0x15a6a6c: b               #0x15a6a78
    // 0x15a6a70: LoadField: r4 = r5->field_b
    //     0x15a6a70: ldur            w4, [x5, #0xb]
    // 0x15a6a74: DecompressPointer r4
    //     0x15a6a74: add             x4, x4, HEAP, lsl #32
    // 0x15a6a78: cmp             w4, NULL
    // 0x15a6a7c: b.ne            #0x15a6a88
    // 0x15a6a80: r4 = 0
    //     0x15a6a80: movz            x4, #0
    // 0x15a6a84: b               #0x15a6a98
    // 0x15a6a88: r5 = LoadInt32Instr(r4)
    //     0x15a6a88: sbfx            x5, x4, #1, #0x1f
    //     0x15a6a8c: tbz             w4, #0, #0x15a6a94
    //     0x15a6a90: ldur            x5, [x4, #7]
    // 0x15a6a94: mov             x4, x5
    // 0x15a6a98: r17 = -328
    //     0x15a6a98: movn            x17, #0x147
    // 0x15a6a9c: str             x4, [fp, x17]
    // 0x15a6aa0: cmp             w2, NULL
    // 0x15a6aa4: b.ne            #0x15a6ab0
    // 0x15a6aa8: r2 = Null
    //     0x15a6aa8: mov             x2, NULL
    // 0x15a6aac: b               #0x15a6aec
    // 0x15a6ab0: LoadField: r5 = r2->field_57
    //     0x15a6ab0: ldur            w5, [x2, #0x57]
    // 0x15a6ab4: DecompressPointer r5
    //     0x15a6ab4: add             x5, x5, HEAP, lsl #32
    // 0x15a6ab8: cmp             w5, NULL
    // 0x15a6abc: b.ne            #0x15a6ac8
    // 0x15a6ac0: r2 = Null
    //     0x15a6ac0: mov             x2, NULL
    // 0x15a6ac4: b               #0x15a6aec
    // 0x15a6ac8: ArrayLoad: r2 = r5[0]  ; List_4
    //     0x15a6ac8: ldur            w2, [x5, #0x17]
    // 0x15a6acc: DecompressPointer r2
    //     0x15a6acc: add             x2, x2, HEAP, lsl #32
    // 0x15a6ad0: cmp             w2, NULL
    // 0x15a6ad4: b.ne            #0x15a6ae0
    // 0x15a6ad8: r2 = Null
    //     0x15a6ad8: mov             x2, NULL
    // 0x15a6adc: b               #0x15a6aec
    // 0x15a6ae0: LoadField: r5 = r2->field_f
    //     0x15a6ae0: ldur            w5, [x2, #0xf]
    // 0x15a6ae4: DecompressPointer r5
    //     0x15a6ae4: add             x5, x5, HEAP, lsl #32
    // 0x15a6ae8: mov             x2, x5
    // 0x15a6aec: cmp             w2, NULL
    // 0x15a6af0: b.ne            #0x15a6afc
    // 0x15a6af4: r2 = 0
    //     0x15a6af4: movz            x2, #0
    // 0x15a6af8: b               #0x15a6b0c
    // 0x15a6afc: r5 = LoadInt32Instr(r2)
    //     0x15a6afc: sbfx            x5, x2, #1, #0x1f
    //     0x15a6b00: tbz             w2, #0, #0x15a6b08
    //     0x15a6b04: ldur            x5, [x2, #7]
    // 0x15a6b08: mov             x2, x5
    // 0x15a6b0c: stur            x2, [fp, #-0xc8]
    // 0x15a6b10: r0 = Color()
    //     0x15a6b10: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a6b14: mov             x1, x0
    // 0x15a6b18: r0 = Instance_ColorSpace
    //     0x15a6b18: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a6b1c: stur            x1, [fp, #-0x100]
    // 0x15a6b20: StoreField: r1->field_27 = r0
    //     0x15a6b20: stur            w0, [x1, #0x27]
    // 0x15a6b24: d0 = 1.000000
    //     0x15a6b24: fmov            d0, #1.00000000
    // 0x15a6b28: StoreField: r1->field_7 = d0
    //     0x15a6b28: stur            d0, [x1, #7]
    // 0x15a6b2c: r17 = -336
    //     0x15a6b2c: movn            x17, #0x14f
    // 0x15a6b30: ldr             x2, [fp, x17]
    // 0x15a6b34: ubfx            x2, x2, #0, #0x20
    // 0x15a6b38: and             w3, w2, #0xff
    // 0x15a6b3c: ubfx            x3, x3, #0, #0x20
    // 0x15a6b40: scvtf           d1, x3
    // 0x15a6b44: d2 = 255.000000
    //     0x15a6b44: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a6b48: fdiv            d3, d1, d2
    // 0x15a6b4c: StoreField: r1->field_f = d3
    //     0x15a6b4c: stur            d3, [x1, #0xf]
    // 0x15a6b50: r17 = -328
    //     0x15a6b50: movn            x17, #0x147
    // 0x15a6b54: ldr             x2, [fp, x17]
    // 0x15a6b58: ubfx            x2, x2, #0, #0x20
    // 0x15a6b5c: and             w3, w2, #0xff
    // 0x15a6b60: ubfx            x3, x3, #0, #0x20
    // 0x15a6b64: scvtf           d1, x3
    // 0x15a6b68: fdiv            d3, d1, d2
    // 0x15a6b6c: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a6b6c: stur            d3, [x1, #0x17]
    // 0x15a6b70: ldur            x2, [fp, #-0xc8]
    // 0x15a6b74: ubfx            x2, x2, #0, #0x20
    // 0x15a6b78: and             w3, w2, #0xff
    // 0x15a6b7c: ubfx            x3, x3, #0, #0x20
    // 0x15a6b80: scvtf           d1, x3
    // 0x15a6b84: fdiv            d3, d1, d2
    // 0x15a6b88: StoreField: r1->field_1f = d3
    //     0x15a6b88: stur            d3, [x1, #0x1f]
    // 0x15a6b8c: r0 = BorderSide()
    //     0x15a6b8c: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x15a6b90: mov             x1, x0
    // 0x15a6b94: ldur            x0, [fp, #-0x100]
    // 0x15a6b98: r17 = -264
    //     0x15a6b98: movn            x17, #0x107
    // 0x15a6b9c: str             x1, [fp, x17]
    // 0x15a6ba0: StoreField: r1->field_7 = r0
    //     0x15a6ba0: stur            w0, [x1, #7]
    // 0x15a6ba4: d0 = 1.000000
    //     0x15a6ba4: fmov            d0, #1.00000000
    // 0x15a6ba8: StoreField: r1->field_b = d0
    //     0x15a6ba8: stur            d0, [x1, #0xb]
    // 0x15a6bac: r0 = Instance_BorderStyle
    //     0x15a6bac: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x15a6bb0: ldr             x0, [x0, #0xf68]
    // 0x15a6bb4: StoreField: r1->field_13 = r0
    //     0x15a6bb4: stur            w0, [x1, #0x13]
    // 0x15a6bb8: d1 = -1.000000
    //     0x15a6bb8: fmov            d1, #-1.00000000
    // 0x15a6bbc: ArrayStore: r1[0] = d1  ; List_8
    //     0x15a6bbc: stur            d1, [x1, #0x17]
    // 0x15a6bc0: r0 = RoundedRectangleBorder()
    //     0x15a6bc0: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x15a6bc4: mov             x1, x0
    // 0x15a6bc8: r0 = Instance_BorderRadius
    //     0x15a6bc8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x15a6bcc: ldr             x0, [x0, #0xf70]
    // 0x15a6bd0: StoreField: r1->field_b = r0
    //     0x15a6bd0: stur            w0, [x1, #0xb]
    // 0x15a6bd4: r17 = -264
    //     0x15a6bd4: movn            x17, #0x107
    // 0x15a6bd8: ldr             x0, [fp, x17]
    // 0x15a6bdc: StoreField: r1->field_7 = r0
    //     0x15a6bdc: stur            w0, [x1, #7]
    // 0x15a6be0: r16 = <RoundedRectangleBorder>
    //     0x15a6be0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x15a6be4: ldr             x16, [x16, #0xf78]
    // 0x15a6be8: stp             x1, x16, [SP]
    // 0x15a6bec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15a6bec: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15a6bf0: r0 = all()
    //     0x15a6bf0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x15a6bf4: mov             x1, x0
    // 0x15a6bf8: ldur            x0, [fp, #-0xc0]
    // 0x15a6bfc: stur            x1, [fp, #-0x100]
    // 0x15a6c00: LoadField: r2 = r0->field_b
    //     0x15a6c00: ldur            w2, [x0, #0xb]
    // 0x15a6c04: DecompressPointer r2
    //     0x15a6c04: add             x2, x2, HEAP, lsl #32
    // 0x15a6c08: cmp             w2, NULL
    // 0x15a6c0c: b.ne            #0x15a6c18
    // 0x15a6c10: r3 = Null
    //     0x15a6c10: mov             x3, NULL
    // 0x15a6c14: b               #0x15a6c50
    // 0x15a6c18: LoadField: r3 = r2->field_57
    //     0x15a6c18: ldur            w3, [x2, #0x57]
    // 0x15a6c1c: DecompressPointer r3
    //     0x15a6c1c: add             x3, x3, HEAP, lsl #32
    // 0x15a6c20: cmp             w3, NULL
    // 0x15a6c24: b.ne            #0x15a6c30
    // 0x15a6c28: r3 = Null
    //     0x15a6c28: mov             x3, NULL
    // 0x15a6c2c: b               #0x15a6c50
    // 0x15a6c30: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a6c30: ldur            w4, [x3, #0x17]
    // 0x15a6c34: DecompressPointer r4
    //     0x15a6c34: add             x4, x4, HEAP, lsl #32
    // 0x15a6c38: cmp             w4, NULL
    // 0x15a6c3c: b.ne            #0x15a6c48
    // 0x15a6c40: r3 = Null
    //     0x15a6c40: mov             x3, NULL
    // 0x15a6c44: b               #0x15a6c50
    // 0x15a6c48: LoadField: r3 = r4->field_7
    //     0x15a6c48: ldur            w3, [x4, #7]
    // 0x15a6c4c: DecompressPointer r3
    //     0x15a6c4c: add             x3, x3, HEAP, lsl #32
    // 0x15a6c50: cmp             w3, NULL
    // 0x15a6c54: b.ne            #0x15a6c60
    // 0x15a6c58: r3 = 0
    //     0x15a6c58: movz            x3, #0
    // 0x15a6c5c: b               #0x15a6c70
    // 0x15a6c60: r4 = LoadInt32Instr(r3)
    //     0x15a6c60: sbfx            x4, x3, #1, #0x1f
    //     0x15a6c64: tbz             w3, #0, #0x15a6c6c
    //     0x15a6c68: ldur            x4, [x3, #7]
    // 0x15a6c6c: mov             x3, x4
    // 0x15a6c70: r17 = -336
    //     0x15a6c70: movn            x17, #0x14f
    // 0x15a6c74: str             x3, [fp, x17]
    // 0x15a6c78: cmp             w2, NULL
    // 0x15a6c7c: b.ne            #0x15a6c88
    // 0x15a6c80: r4 = Null
    //     0x15a6c80: mov             x4, NULL
    // 0x15a6c84: b               #0x15a6cc0
    // 0x15a6c88: LoadField: r4 = r2->field_57
    //     0x15a6c88: ldur            w4, [x2, #0x57]
    // 0x15a6c8c: DecompressPointer r4
    //     0x15a6c8c: add             x4, x4, HEAP, lsl #32
    // 0x15a6c90: cmp             w4, NULL
    // 0x15a6c94: b.ne            #0x15a6ca0
    // 0x15a6c98: r4 = Null
    //     0x15a6c98: mov             x4, NULL
    // 0x15a6c9c: b               #0x15a6cc0
    // 0x15a6ca0: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x15a6ca0: ldur            w5, [x4, #0x17]
    // 0x15a6ca4: DecompressPointer r5
    //     0x15a6ca4: add             x5, x5, HEAP, lsl #32
    // 0x15a6ca8: cmp             w5, NULL
    // 0x15a6cac: b.ne            #0x15a6cb8
    // 0x15a6cb0: r4 = Null
    //     0x15a6cb0: mov             x4, NULL
    // 0x15a6cb4: b               #0x15a6cc0
    // 0x15a6cb8: LoadField: r4 = r5->field_b
    //     0x15a6cb8: ldur            w4, [x5, #0xb]
    // 0x15a6cbc: DecompressPointer r4
    //     0x15a6cbc: add             x4, x4, HEAP, lsl #32
    // 0x15a6cc0: cmp             w4, NULL
    // 0x15a6cc4: b.ne            #0x15a6cd0
    // 0x15a6cc8: r4 = 0
    //     0x15a6cc8: movz            x4, #0
    // 0x15a6ccc: b               #0x15a6ce0
    // 0x15a6cd0: r5 = LoadInt32Instr(r4)
    //     0x15a6cd0: sbfx            x5, x4, #1, #0x1f
    //     0x15a6cd4: tbz             w4, #0, #0x15a6cdc
    //     0x15a6cd8: ldur            x5, [x4, #7]
    // 0x15a6cdc: mov             x4, x5
    // 0x15a6ce0: r17 = -328
    //     0x15a6ce0: movn            x17, #0x147
    // 0x15a6ce4: str             x4, [fp, x17]
    // 0x15a6ce8: cmp             w2, NULL
    // 0x15a6cec: b.ne            #0x15a6cf8
    // 0x15a6cf0: r2 = Null
    //     0x15a6cf0: mov             x2, NULL
    // 0x15a6cf4: b               #0x15a6d34
    // 0x15a6cf8: LoadField: r5 = r2->field_57
    //     0x15a6cf8: ldur            w5, [x2, #0x57]
    // 0x15a6cfc: DecompressPointer r5
    //     0x15a6cfc: add             x5, x5, HEAP, lsl #32
    // 0x15a6d00: cmp             w5, NULL
    // 0x15a6d04: b.ne            #0x15a6d10
    // 0x15a6d08: r2 = Null
    //     0x15a6d08: mov             x2, NULL
    // 0x15a6d0c: b               #0x15a6d34
    // 0x15a6d10: ArrayLoad: r2 = r5[0]  ; List_4
    //     0x15a6d10: ldur            w2, [x5, #0x17]
    // 0x15a6d14: DecompressPointer r2
    //     0x15a6d14: add             x2, x2, HEAP, lsl #32
    // 0x15a6d18: cmp             w2, NULL
    // 0x15a6d1c: b.ne            #0x15a6d28
    // 0x15a6d20: r2 = Null
    //     0x15a6d20: mov             x2, NULL
    // 0x15a6d24: b               #0x15a6d34
    // 0x15a6d28: LoadField: r5 = r2->field_f
    //     0x15a6d28: ldur            w5, [x2, #0xf]
    // 0x15a6d2c: DecompressPointer r5
    //     0x15a6d2c: add             x5, x5, HEAP, lsl #32
    // 0x15a6d30: mov             x2, x5
    // 0x15a6d34: cmp             w2, NULL
    // 0x15a6d38: b.ne            #0x15a6d44
    // 0x15a6d3c: r2 = 0
    //     0x15a6d3c: movz            x2, #0
    // 0x15a6d40: b               #0x15a6d54
    // 0x15a6d44: r5 = LoadInt32Instr(r2)
    //     0x15a6d44: sbfx            x5, x2, #1, #0x1f
    //     0x15a6d48: tbz             w2, #0, #0x15a6d50
    //     0x15a6d4c: ldur            x5, [x2, #7]
    // 0x15a6d50: mov             x2, x5
    // 0x15a6d54: stur            x2, [fp, #-0xc8]
    // 0x15a6d58: r0 = Color()
    //     0x15a6d58: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a6d5c: mov             x1, x0
    // 0x15a6d60: r0 = Instance_ColorSpace
    //     0x15a6d60: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a6d64: StoreField: r1->field_27 = r0
    //     0x15a6d64: stur            w0, [x1, #0x27]
    // 0x15a6d68: d0 = 1.000000
    //     0x15a6d68: fmov            d0, #1.00000000
    // 0x15a6d6c: StoreField: r1->field_7 = d0
    //     0x15a6d6c: stur            d0, [x1, #7]
    // 0x15a6d70: r17 = -336
    //     0x15a6d70: movn            x17, #0x14f
    // 0x15a6d74: ldr             x2, [fp, x17]
    // 0x15a6d78: ubfx            x2, x2, #0, #0x20
    // 0x15a6d7c: and             w3, w2, #0xff
    // 0x15a6d80: ubfx            x3, x3, #0, #0x20
    // 0x15a6d84: scvtf           d1, x3
    // 0x15a6d88: d2 = 255.000000
    //     0x15a6d88: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a6d8c: fdiv            d3, d1, d2
    // 0x15a6d90: StoreField: r1->field_f = d3
    //     0x15a6d90: stur            d3, [x1, #0xf]
    // 0x15a6d94: r17 = -328
    //     0x15a6d94: movn            x17, #0x147
    // 0x15a6d98: ldr             x2, [fp, x17]
    // 0x15a6d9c: ubfx            x2, x2, #0, #0x20
    // 0x15a6da0: and             w3, w2, #0xff
    // 0x15a6da4: ubfx            x3, x3, #0, #0x20
    // 0x15a6da8: scvtf           d1, x3
    // 0x15a6dac: fdiv            d3, d1, d2
    // 0x15a6db0: ArrayStore: r1[0] = d3  ; List_8
    //     0x15a6db0: stur            d3, [x1, #0x17]
    // 0x15a6db4: ldur            x2, [fp, #-0xc8]
    // 0x15a6db8: ubfx            x2, x2, #0, #0x20
    // 0x15a6dbc: and             w3, w2, #0xff
    // 0x15a6dc0: ubfx            x3, x3, #0, #0x20
    // 0x15a6dc4: scvtf           d1, x3
    // 0x15a6dc8: fdiv            d3, d1, d2
    // 0x15a6dcc: StoreField: r1->field_1f = d3
    //     0x15a6dcc: stur            d3, [x1, #0x1f]
    // 0x15a6dd0: r16 = <Color>
    //     0x15a6dd0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x15a6dd4: ldr             x16, [x16, #0xf80]
    // 0x15a6dd8: stp             x1, x16, [SP]
    // 0x15a6ddc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15a6ddc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15a6de0: r0 = all()
    //     0x15a6de0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x15a6de4: mov             x1, x0
    // 0x15a6de8: ldur            x0, [fp, #-0xc0]
    // 0x15a6dec: r17 = -264
    //     0x15a6dec: movn            x17, #0x107
    // 0x15a6df0: str             x1, [fp, x17]
    // 0x15a6df4: LoadField: r2 = r0->field_b
    //     0x15a6df4: ldur            w2, [x0, #0xb]
    // 0x15a6df8: DecompressPointer r2
    //     0x15a6df8: add             x2, x2, HEAP, lsl #32
    // 0x15a6dfc: cmp             w2, NULL
    // 0x15a6e00: b.ne            #0x15a6e0c
    // 0x15a6e04: r0 = Null
    //     0x15a6e04: mov             x0, NULL
    // 0x15a6e08: b               #0x15a6e44
    // 0x15a6e0c: LoadField: r0 = r2->field_57
    //     0x15a6e0c: ldur            w0, [x2, #0x57]
    // 0x15a6e10: DecompressPointer r0
    //     0x15a6e10: add             x0, x0, HEAP, lsl #32
    // 0x15a6e14: cmp             w0, NULL
    // 0x15a6e18: b.ne            #0x15a6e24
    // 0x15a6e1c: r0 = Null
    //     0x15a6e1c: mov             x0, NULL
    // 0x15a6e20: b               #0x15a6e44
    // 0x15a6e24: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x15a6e24: ldur            w3, [x0, #0x17]
    // 0x15a6e28: DecompressPointer r3
    //     0x15a6e28: add             x3, x3, HEAP, lsl #32
    // 0x15a6e2c: cmp             w3, NULL
    // 0x15a6e30: b.ne            #0x15a6e3c
    // 0x15a6e34: r0 = Null
    //     0x15a6e34: mov             x0, NULL
    // 0x15a6e38: b               #0x15a6e44
    // 0x15a6e3c: LoadField: r0 = r3->field_7
    //     0x15a6e3c: ldur            w0, [x3, #7]
    // 0x15a6e40: DecompressPointer r0
    //     0x15a6e40: add             x0, x0, HEAP, lsl #32
    // 0x15a6e44: cmp             w0, NULL
    // 0x15a6e48: b.ne            #0x15a6e54
    // 0x15a6e4c: r0 = 0
    //     0x15a6e4c: movz            x0, #0
    // 0x15a6e50: b               #0x15a6e64
    // 0x15a6e54: r3 = LoadInt32Instr(r0)
    //     0x15a6e54: sbfx            x3, x0, #1, #0x1f
    //     0x15a6e58: tbz             w0, #0, #0x15a6e60
    //     0x15a6e5c: ldur            x3, [x0, #7]
    // 0x15a6e60: mov             x0, x3
    // 0x15a6e64: r17 = -336
    //     0x15a6e64: movn            x17, #0x14f
    // 0x15a6e68: str             x0, [fp, x17]
    // 0x15a6e6c: cmp             w2, NULL
    // 0x15a6e70: b.ne            #0x15a6e7c
    // 0x15a6e74: r3 = Null
    //     0x15a6e74: mov             x3, NULL
    // 0x15a6e78: b               #0x15a6eb4
    // 0x15a6e7c: LoadField: r3 = r2->field_57
    //     0x15a6e7c: ldur            w3, [x2, #0x57]
    // 0x15a6e80: DecompressPointer r3
    //     0x15a6e80: add             x3, x3, HEAP, lsl #32
    // 0x15a6e84: cmp             w3, NULL
    // 0x15a6e88: b.ne            #0x15a6e94
    // 0x15a6e8c: r3 = Null
    //     0x15a6e8c: mov             x3, NULL
    // 0x15a6e90: b               #0x15a6eb4
    // 0x15a6e94: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x15a6e94: ldur            w4, [x3, #0x17]
    // 0x15a6e98: DecompressPointer r4
    //     0x15a6e98: add             x4, x4, HEAP, lsl #32
    // 0x15a6e9c: cmp             w4, NULL
    // 0x15a6ea0: b.ne            #0x15a6eac
    // 0x15a6ea4: r3 = Null
    //     0x15a6ea4: mov             x3, NULL
    // 0x15a6ea8: b               #0x15a6eb4
    // 0x15a6eac: LoadField: r3 = r4->field_b
    //     0x15a6eac: ldur            w3, [x4, #0xb]
    // 0x15a6eb0: DecompressPointer r3
    //     0x15a6eb0: add             x3, x3, HEAP, lsl #32
    // 0x15a6eb4: cmp             w3, NULL
    // 0x15a6eb8: b.ne            #0x15a6ec4
    // 0x15a6ebc: r3 = 0
    //     0x15a6ebc: movz            x3, #0
    // 0x15a6ec0: b               #0x15a6ed4
    // 0x15a6ec4: r4 = LoadInt32Instr(r3)
    //     0x15a6ec4: sbfx            x4, x3, #1, #0x1f
    //     0x15a6ec8: tbz             w3, #0, #0x15a6ed0
    //     0x15a6ecc: ldur            x4, [x3, #7]
    // 0x15a6ed0: mov             x3, x4
    // 0x15a6ed4: r17 = -328
    //     0x15a6ed4: movn            x17, #0x147
    // 0x15a6ed8: str             x3, [fp, x17]
    // 0x15a6edc: cmp             w2, NULL
    // 0x15a6ee0: b.ne            #0x15a6eec
    // 0x15a6ee4: r2 = Null
    //     0x15a6ee4: mov             x2, NULL
    // 0x15a6ee8: b               #0x15a6f28
    // 0x15a6eec: LoadField: r4 = r2->field_57
    //     0x15a6eec: ldur            w4, [x2, #0x57]
    // 0x15a6ef0: DecompressPointer r4
    //     0x15a6ef0: add             x4, x4, HEAP, lsl #32
    // 0x15a6ef4: cmp             w4, NULL
    // 0x15a6ef8: b.ne            #0x15a6f04
    // 0x15a6efc: r2 = Null
    //     0x15a6efc: mov             x2, NULL
    // 0x15a6f00: b               #0x15a6f28
    // 0x15a6f04: ArrayLoad: r2 = r4[0]  ; List_4
    //     0x15a6f04: ldur            w2, [x4, #0x17]
    // 0x15a6f08: DecompressPointer r2
    //     0x15a6f08: add             x2, x2, HEAP, lsl #32
    // 0x15a6f0c: cmp             w2, NULL
    // 0x15a6f10: b.ne            #0x15a6f1c
    // 0x15a6f14: r2 = Null
    //     0x15a6f14: mov             x2, NULL
    // 0x15a6f18: b               #0x15a6f28
    // 0x15a6f1c: LoadField: r4 = r2->field_f
    //     0x15a6f1c: ldur            w4, [x2, #0xf]
    // 0x15a6f20: DecompressPointer r4
    //     0x15a6f20: add             x4, x4, HEAP, lsl #32
    // 0x15a6f24: mov             x2, x4
    // 0x15a6f28: cmp             w2, NULL
    // 0x15a6f2c: b.ne            #0x15a6f38
    // 0x15a6f30: r4 = 0
    //     0x15a6f30: movz            x4, #0
    // 0x15a6f34: b               #0x15a6f44
    // 0x15a6f38: r4 = LoadInt32Instr(r2)
    //     0x15a6f38: sbfx            x4, x2, #1, #0x1f
    //     0x15a6f3c: tbz             w2, #0, #0x15a6f44
    //     0x15a6f40: ldur            x4, [x2, #7]
    // 0x15a6f44: ldur            x2, [fp, #-0x100]
    // 0x15a6f48: stur            x4, [fp, #-0xc8]
    // 0x15a6f4c: r0 = Color()
    //     0x15a6f4c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x15a6f50: mov             x1, x0
    // 0x15a6f54: r0 = Instance_ColorSpace
    //     0x15a6f54: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x15a6f58: StoreField: r1->field_27 = r0
    //     0x15a6f58: stur            w0, [x1, #0x27]
    // 0x15a6f5c: d0 = 1.000000
    //     0x15a6f5c: fmov            d0, #1.00000000
    // 0x15a6f60: StoreField: r1->field_7 = d0
    //     0x15a6f60: stur            d0, [x1, #7]
    // 0x15a6f64: r17 = -336
    //     0x15a6f64: movn            x17, #0x14f
    // 0x15a6f68: ldr             x0, [fp, x17]
    // 0x15a6f6c: ubfx            x0, x0, #0, #0x20
    // 0x15a6f70: and             w2, w0, #0xff
    // 0x15a6f74: ubfx            x2, x2, #0, #0x20
    // 0x15a6f78: scvtf           d0, x2
    // 0x15a6f7c: d1 = 255.000000
    //     0x15a6f7c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x15a6f80: fdiv            d2, d0, d1
    // 0x15a6f84: StoreField: r1->field_f = d2
    //     0x15a6f84: stur            d2, [x1, #0xf]
    // 0x15a6f88: r17 = -328
    //     0x15a6f88: movn            x17, #0x147
    // 0x15a6f8c: ldr             x0, [fp, x17]
    // 0x15a6f90: ubfx            x0, x0, #0, #0x20
    // 0x15a6f94: and             w2, w0, #0xff
    // 0x15a6f98: ubfx            x2, x2, #0, #0x20
    // 0x15a6f9c: scvtf           d0, x2
    // 0x15a6fa0: fdiv            d2, d0, d1
    // 0x15a6fa4: ArrayStore: r1[0] = d2  ; List_8
    //     0x15a6fa4: stur            d2, [x1, #0x17]
    // 0x15a6fa8: ldur            x0, [fp, #-0xc8]
    // 0x15a6fac: ubfx            x0, x0, #0, #0x20
    // 0x15a6fb0: and             w2, w0, #0xff
    // 0x15a6fb4: ubfx            x2, x2, #0, #0x20
    // 0x15a6fb8: scvtf           d0, x2
    // 0x15a6fbc: fdiv            d2, d0, d1
    // 0x15a6fc0: StoreField: r1->field_1f = d2
    //     0x15a6fc0: stur            d2, [x1, #0x1f]
    // 0x15a6fc4: r16 = <Color>
    //     0x15a6fc4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x15a6fc8: ldr             x16, [x16, #0xf80]
    // 0x15a6fcc: stp             x1, x16, [SP]
    // 0x15a6fd0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15a6fd0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15a6fd4: r0 = all()
    //     0x15a6fd4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x15a6fd8: stur            x0, [fp, #-0xc0]
    // 0x15a6fdc: r0 = ButtonStyle()
    //     0x15a6fdc: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x15a6fe0: mov             x1, x0
    // 0x15a6fe4: r17 = -264
    //     0x15a6fe4: movn            x17, #0x107
    // 0x15a6fe8: ldr             x0, [fp, x17]
    // 0x15a6fec: r17 = -272
    //     0x15a6fec: movn            x17, #0x10f
    // 0x15a6ff0: str             x1, [fp, x17]
    // 0x15a6ff4: StoreField: r1->field_b = r0
    //     0x15a6ff4: stur            w0, [x1, #0xb]
    // 0x15a6ff8: ldur            x0, [fp, #-0xc0]
    // 0x15a6ffc: StoreField: r1->field_f = r0
    //     0x15a6ffc: stur            w0, [x1, #0xf]
    // 0x15a7000: ldur            x0, [fp, #-0x100]
    // 0x15a7004: StoreField: r1->field_43 = r0
    //     0x15a7004: stur            w0, [x1, #0x43]
    // 0x15a7008: r0 = TextButtonThemeData()
    //     0x15a7008: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x15a700c: mov             x1, x0
    // 0x15a7010: r17 = -272
    //     0x15a7010: movn            x17, #0x10f
    // 0x15a7014: ldr             x0, [fp, x17]
    // 0x15a7018: StoreField: r1->field_7 = r0
    //     0x15a7018: stur            w0, [x1, #7]
    // 0x15a701c: ldur            x16, [fp, #-0xe0]
    // 0x15a7020: ldur            lr, [fp, #-0xf0]
    // 0x15a7024: stp             lr, x16, [SP, #0x28]
    // 0x15a7028: r17 = -288
    //     0x15a7028: movn            x17, #0x11f
    // 0x15a702c: ldr             x16, [fp, x17]
    // 0x15a7030: ldur            lr, [fp, #-0xe8]
    // 0x15a7034: stp             lr, x16, [SP, #0x18]
    // 0x15a7038: ldur            x16, [fp, #-0xf8]
    // 0x15a703c: r17 = -304
    //     0x15a703c: movn            x17, #0x12f
    // 0x15a7040: ldr             lr, [fp, x17]
    // 0x15a7044: stp             lr, x16, [SP, #8]
    // 0x15a7048: str             x1, [SP]
    // 0x15a704c: r1 = Null
    //     0x15a704c: mov             x1, NULL
    // 0x15a7050: r4 = const [0, 0x8, 0x7, 0x1, appBarTheme, 0x4, bottomNavigationBarTheme, 0x3, colorScheme, 0x2, iconTheme, 0x5, primaryColor, 0x1, textButtonTheme, 0x7, textTheme, 0x6, null]
    //     0x15a7050: add             x4, PP, #0x12, lsl #12  ; [pp+0x12fb0] List(19) [0, 0x8, 0x7, 0x1, "appBarTheme", 0x4, "bottomNavigationBarTheme", 0x3, "colorScheme", 0x2, "iconTheme", 0x5, "primaryColor", 0x1, "textButtonTheme", 0x7, "textTheme", 0x6, Null]
    //     0x15a7054: ldr             x4, [x4, #0xfb0]
    // 0x15a7058: r0 = ThemeData()
    //     0x15a7058: bl              #0x6b0c60  ; [package:flutter/src/material/theme_data.dart] ThemeData::ThemeData
    // 0x15a705c: ldur            x1, [fp, #-0xb8]
    // 0x15a7060: mov             x2, x0
    // 0x15a7064: r0 = post=()
    //     0x15a7064: bl              #0x12c1ab8  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::post=
    // 0x15a7068: r0 = Null
    //     0x15a7068: mov             x0, NULL
    // 0x15a706c: r0 = ReturnAsyncNotFuture()
    //     0x15a706c: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x15a7070: ldur            x0, [fp, #-0xd0]
    // 0x15a7074: ldur            x1, [fp, #-0xd8]
    // 0x15a7078: r0 = ReThrow()
    //     0x15a7078: bl              #0x16f53f4  ; ReThrowStub
    // 0x15a707c: brk             #0
    // 0x15a7080: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a7080: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a7084: b               #0x15a2584
  }
  set _ configResponse=(/* No info */) {
    // ** addr: 0x15a7088, size: 0x8c
    // 0x15a7088: EnterFrame
    //     0x15a7088: stp             fp, lr, [SP, #-0x10]!
    //     0x15a708c: mov             fp, SP
    // 0x15a7090: AllocStack(0x10)
    //     0x15a7090: sub             SP, SP, #0x10
    // 0x15a7094: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x15a7094: mov             x3, x2
    //     0x15a7098: stur            x2, [fp, #-0x10]
    // 0x15a709c: CheckStackOverflow
    //     0x15a709c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a70a0: cmp             SP, x16
    //     0x15a70a4: b.ls            #0x15a710c
    // 0x15a70a8: LoadField: r4 = r1->field_57
    //     0x15a70a8: ldur            w4, [x1, #0x57]
    // 0x15a70ac: DecompressPointer r4
    //     0x15a70ac: add             x4, x4, HEAP, lsl #32
    // 0x15a70b0: mov             x0, x3
    // 0x15a70b4: stur            x4, [fp, #-8]
    // 0x15a70b8: r2 = Null
    //     0x15a70b8: mov             x2, NULL
    // 0x15a70bc: r1 = Null
    //     0x15a70bc: mov             x1, NULL
    // 0x15a70c0: r4 = 60
    //     0x15a70c0: movz            x4, #0x3c
    // 0x15a70c4: branchIfSmi(r0, 0x15a70d0)
    //     0x15a70c4: tbz             w0, #0, #0x15a70d0
    // 0x15a70c8: r4 = LoadClassIdInstr(r0)
    //     0x15a70c8: ldur            x4, [x0, #-1]
    //     0x15a70cc: ubfx            x4, x4, #0xc, #0x14
    // 0x15a70d0: r17 = 5487
    //     0x15a70d0: movz            x17, #0x156f
    // 0x15a70d4: cmp             x4, x17
    // 0x15a70d8: b.eq            #0x15a70f0
    // 0x15a70dc: r8 = ConfigResponse
    //     0x15a70dc: add             x8, PP, #0x10, lsl #12  ; [pp+0x10b08] Type: ConfigResponse
    //     0x15a70e0: ldr             x8, [x8, #0xb08]
    // 0x15a70e4: r3 = Null
    //     0x15a70e4: add             x3, PP, #0x22, lsl #12  ; [pp+0x221d8] Null
    //     0x15a70e8: ldr             x3, [x3, #0x1d8]
    // 0x15a70ec: r0 = DefaultTypeTest()
    //     0x15a70ec: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x15a70f0: ldur            x1, [fp, #-8]
    // 0x15a70f4: ldur            x2, [fp, #-0x10]
    // 0x15a70f8: r0 = value=()
    //     0x15a70f8: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15a70fc: ldur            x0, [fp, #-0x10]
    // 0x15a7100: LeaveFrame
    //     0x15a7100: mov             SP, fp
    //     0x15a7104: ldp             fp, lr, [SP], #0x10
    // 0x15a7108: ret
    //     0x15a7108: ret             
    // 0x15a710c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a710c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a7110: b               #0x15a70a8
  }
  _ MainController(/* No info */) {
    // ** addr: 0x1602154, size: 0x1f0
    // 0x1602154: EnterFrame
    //     0x1602154: stp             fp, lr, [SP, #-0x10]!
    //     0x1602158: mov             fp, SP
    // 0x160215c: AllocStack(0x28)
    //     0x160215c: sub             SP, SP, #0x28
    // 0x1602160: r2 = Sentinel
    //     0x1602160: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x1602164: r0 = false
    //     0x1602164: add             x0, NULL, #0x30  ; false
    // 0x1602168: stur            x1, [fp, #-8]
    // 0x160216c: CheckStackOverflow
    //     0x160216c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1602170: cmp             SP, x16
    //     0x1602174: b.ls            #0x160233c
    // 0x1602178: StoreField: r1->field_47 = r2
    //     0x1602178: stur            w2, [x1, #0x47]
    // 0x160217c: StoreField: r1->field_53 = r0
    //     0x160217c: stur            w0, [x1, #0x53]
    // 0x1602180: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1602180: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1602184: ldr             x0, [x0, #0x1c80]
    //     0x1602188: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x160218c: cmp             w0, w16
    //     0x1602190: b.ne            #0x160219c
    //     0x1602194: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1602198: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x160219c: r16 = PreferenceManager
    //     0x160219c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0x16021a0: ldr             x16, [x16, #0x878]
    // 0x16021a4: str             x16, [SP]
    // 0x16021a8: r0 = toString()
    //     0x16021a8: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x16021ac: r16 = <PreferenceManager>
    //     0x16021ac: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0x16021b0: ldr             x16, [x16, #0x880]
    // 0x16021b4: stp             x0, x16, [SP]
    // 0x16021b8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x16021b8: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x16021bc: r0 = Inst.find()
    //     0x16021bc: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x16021c0: ldur            x1, [fp, #-8]
    // 0x16021c4: StoreField: r1->field_4b = r0
    //     0x16021c4: stur            w0, [x1, #0x4b]
    //     0x16021c8: ldurb           w16, [x1, #-1]
    //     0x16021cc: ldurb           w17, [x0, #-1]
    //     0x16021d0: and             x16, x17, x16, lsr #2
    //     0x16021d4: tst             x16, HEAP, lsr #32
    //     0x16021d8: b.eq            #0x16021e0
    //     0x16021dc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x16021e0: r16 = ConnectionController
    //     0x16021e0: add             x16, PP, #0xa, lsl #12  ; [pp+0xaaf8] Type: ConnectionController
    //     0x16021e4: ldr             x16, [x16, #0xaf8]
    // 0x16021e8: str             x16, [SP]
    // 0x16021ec: r0 = toString()
    //     0x16021ec: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x16021f0: r16 = <ConnectionController>
    //     0x16021f0: add             x16, PP, #0xa, lsl #12  ; [pp+0xab00] TypeArguments: <ConnectionController>
    //     0x16021f4: ldr             x16, [x16, #0xb00]
    // 0x16021f8: stp             x0, x16, [SP]
    // 0x16021fc: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x16021fc: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x1602200: r0 = Inst.find()
    //     0x1602200: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x1602204: ldur            x1, [fp, #-8]
    // 0x1602208: StoreField: r1->field_4f = r0
    //     0x1602208: stur            w0, [x1, #0x4f]
    //     0x160220c: ldurb           w16, [x1, #-1]
    //     0x1602210: ldurb           w17, [x0, #-1]
    //     0x1602214: and             x16, x17, x16, lsr #2
    //     0x1602218: tst             x16, HEAP, lsr #32
    //     0x160221c: b.eq            #0x1602224
    //     0x1602220: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1602224: r0 = ConfigResponse()
    //     0x1602224: bl              #0x90cd04  ; AllocateConfigResponseStub -> ConfigResponse (size=0x10)
    // 0x1602228: r16 = <ConfigResponse>
    //     0x1602228: add             x16, PP, #0xa, lsl #12  ; [pp+0xab08] TypeArguments: <ConfigResponse>
    //     0x160222c: ldr             x16, [x16, #0xb08]
    // 0x1602230: stp             x0, x16, [SP]
    // 0x1602234: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1602234: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1602238: r0 = RxT.obs()
    //     0x1602238: bl              #0x12c6190  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x160223c: ldur            x2, [fp, #-8]
    // 0x1602240: StoreField: r2->field_57 = r0
    //     0x1602240: stur            w0, [x2, #0x57]
    //     0x1602244: ldurb           w16, [x2, #-1]
    //     0x1602248: ldurb           w17, [x0, #-1]
    //     0x160224c: and             x16, x17, x16, lsr #2
    //     0x1602250: tst             x16, HEAP, lsr #32
    //     0x1602254: b.eq            #0x160225c
    //     0x1602258: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x160225c: r1 = Null
    //     0x160225c: mov             x1, NULL
    // 0x1602260: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1602260: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1602264: r0 = ThemeData()
    //     0x1602264: bl              #0x6b0c60  ; [package:flutter/src/material/theme_data.dart] ThemeData::ThemeData
    // 0x1602268: r16 = <ThemeData>
    //     0x1602268: add             x16, PP, #0xa, lsl #12  ; [pp+0xab10] TypeArguments: <ThemeData>
    //     0x160226c: ldr             x16, [x16, #0xb10]
    // 0x1602270: stp             x0, x16, [SP]
    // 0x1602274: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1602274: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1602278: r0 = RxT.obs()
    //     0x1602278: bl              #0x12c6190  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x160227c: ldur            x2, [fp, #-8]
    // 0x1602280: StoreField: r2->field_5b = r0
    //     0x1602280: stur            w0, [x2, #0x5b]
    //     0x1602284: ldurb           w16, [x2, #-1]
    //     0x1602288: ldurb           w17, [x0, #-1]
    //     0x160228c: and             x16, x17, x16, lsr #2
    //     0x1602290: tst             x16, HEAP, lsr #32
    //     0x1602294: b.eq            #0x160229c
    //     0x1602298: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x160229c: mov             x1, x2
    // 0x16022a0: r0 = BaseController()
    //     0x16022a0: bl              #0x12c396c  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::BaseController
    // 0x16022a4: r16 = HomeRepo
    //     0x16022a4: add             x16, PP, #0xa, lsl #12  ; [pp+0xaab0] Type: HomeRepo
    //     0x16022a8: ldr             x16, [x16, #0xab0]
    // 0x16022ac: str             x16, [SP]
    // 0x16022b0: r0 = toString()
    //     0x16022b0: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x16022b4: r1 = Function '<anonymous closure>':.
    //     0x16022b4: add             x1, PP, #0xb, lsl #12  ; [pp+0xb230] AnonymousClosure: (0x15f94cc), in [package:customer_app/app/bindings/config_repo_bindings.dart] ConfigRepoBindings::dependencies (0x15f96b8)
    //     0x16022b8: ldr             x1, [x1, #0x230]
    // 0x16022bc: r2 = Null
    //     0x16022bc: mov             x2, NULL
    // 0x16022c0: stur            x0, [fp, #-0x10]
    // 0x16022c4: r0 = AllocateClosure()
    //     0x16022c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x16022c8: r16 = <HomeRepo>
    //     0x16022c8: add             x16, PP, #0xa, lsl #12  ; [pp+0xaac0] TypeArguments: <HomeRepo>
    //     0x16022cc: ldr             x16, [x16, #0xac0]
    // 0x16022d0: stp             x0, x16, [SP, #8]
    // 0x16022d4: ldur            x16, [fp, #-0x10]
    // 0x16022d8: str             x16, [SP]
    // 0x16022dc: r4 = const [0x1, 0x2, 0x2, 0x1, tag, 0x1, null]
    //     0x16022dc: add             x4, PP, #0xa, lsl #12  ; [pp+0xaac8] List(7) [0x1, 0x2, 0x2, 0x1, "tag", 0x1, Null]
    //     0x16022e0: ldr             x4, [x4, #0xac8]
    // 0x16022e4: r0 = Inst.lazyPut()
    //     0x16022e4: bl              #0x12c3844  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x16022e8: r16 = HomeRepo
    //     0x16022e8: add             x16, PP, #0xa, lsl #12  ; [pp+0xaab0] Type: HomeRepo
    //     0x16022ec: ldr             x16, [x16, #0xab0]
    // 0x16022f0: str             x16, [SP]
    // 0x16022f4: r0 = toString()
    //     0x16022f4: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x16022f8: r16 = <HomeRepo>
    //     0x16022f8: add             x16, PP, #0xa, lsl #12  ; [pp+0xaac0] TypeArguments: <HomeRepo>
    //     0x16022fc: ldr             x16, [x16, #0xac0]
    // 0x1602300: stp             x0, x16, [SP]
    // 0x1602304: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x1602304: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x1602308: r0 = Inst.find()
    //     0x1602308: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x160230c: ldur            x1, [fp, #-8]
    // 0x1602310: StoreField: r1->field_47 = r0
    //     0x1602310: stur            w0, [x1, #0x47]
    //     0x1602314: ldurb           w16, [x1, #-1]
    //     0x1602318: ldurb           w17, [x0, #-1]
    //     0x160231c: and             x16, x17, x16, lsr #2
    //     0x1602320: tst             x16, HEAP, lsr #32
    //     0x1602324: b.eq            #0x160232c
    //     0x1602328: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x160232c: r0 = Null
    //     0x160232c: mov             x0, NULL
    // 0x1602330: LeaveFrame
    //     0x1602330: mov             SP, fp
    //     0x1602334: ldp             fp, lr, [SP], #0x10
    // 0x1602338: ret
    //     0x1602338: ret             
    // 0x160233c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x160233c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1602340: b               #0x1602178
  }
}
