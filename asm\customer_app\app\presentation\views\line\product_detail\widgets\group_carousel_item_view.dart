// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/group_carousel_item_view.dart

// class id: 1049554, size: 0x8
class :: {
}

// class id: 3230, size: 0x24, field offset: 0x14
class _GroupCarouselItemViewState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14
  late PageController _imagePageController; // offset: 0x18

  _ initState(/* No info */) {
    // ** addr: 0x94a8ac, size: 0xcc
    // 0x94a8ac: EnterFrame
    //     0x94a8ac: stp             fp, lr, [SP, #-0x10]!
    //     0x94a8b0: mov             fp, SP
    // 0x94a8b4: AllocStack(0x10)
    //     0x94a8b4: sub             SP, SP, #0x10
    // 0x94a8b8: SetupParameters(_GroupCarouselItemViewState this /* r1 => r1, fp-0x8 */)
    //     0x94a8b8: stur            x1, [fp, #-8]
    // 0x94a8bc: CheckStackOverflow
    //     0x94a8bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94a8c0: cmp             SP, x16
    //     0x94a8c4: b.ls            #0x94a970
    // 0x94a8c8: r0 = PageController()
    //     0x94a8c8: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x94a8cc: stur            x0, [fp, #-0x10]
    // 0x94a8d0: StoreField: r0->field_3f = rZR
    //     0x94a8d0: stur            xzr, [x0, #0x3f]
    // 0x94a8d4: r2 = true
    //     0x94a8d4: add             x2, NULL, #0x20  ; true
    // 0x94a8d8: StoreField: r0->field_47 = r2
    //     0x94a8d8: stur            w2, [x0, #0x47]
    // 0x94a8dc: d0 = 0.500000
    //     0x94a8dc: fmov            d0, #0.50000000
    // 0x94a8e0: StoreField: r0->field_4b = d0
    //     0x94a8e0: stur            d0, [x0, #0x4b]
    // 0x94a8e4: mov             x1, x0
    // 0x94a8e8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x94a8e8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x94a8ec: r0 = ScrollController()
    //     0x94a8ec: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x94a8f0: ldur            x0, [fp, #-0x10]
    // 0x94a8f4: ldur            x1, [fp, #-8]
    // 0x94a8f8: StoreField: r1->field_13 = r0
    //     0x94a8f8: stur            w0, [x1, #0x13]
    //     0x94a8fc: ldurb           w16, [x1, #-1]
    //     0x94a900: ldurb           w17, [x0, #-1]
    //     0x94a904: and             x16, x17, x16, lsr #2
    //     0x94a908: tst             x16, HEAP, lsr #32
    //     0x94a90c: b.eq            #0x94a914
    //     0x94a910: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x94a914: r0 = PageController()
    //     0x94a914: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x94a918: stur            x0, [fp, #-0x10]
    // 0x94a91c: StoreField: r0->field_3f = rZR
    //     0x94a91c: stur            xzr, [x0, #0x3f]
    // 0x94a920: r1 = true
    //     0x94a920: add             x1, NULL, #0x20  ; true
    // 0x94a924: StoreField: r0->field_47 = r1
    //     0x94a924: stur            w1, [x0, #0x47]
    // 0x94a928: d0 = 1.000000
    //     0x94a928: fmov            d0, #1.00000000
    // 0x94a92c: StoreField: r0->field_4b = d0
    //     0x94a92c: stur            d0, [x0, #0x4b]
    // 0x94a930: mov             x1, x0
    // 0x94a934: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x94a934: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x94a938: r0 = ScrollController()
    //     0x94a938: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x94a93c: ldur            x0, [fp, #-0x10]
    // 0x94a940: ldur            x1, [fp, #-8]
    // 0x94a944: ArrayStore: r1[0] = r0  ; List_4
    //     0x94a944: stur            w0, [x1, #0x17]
    //     0x94a948: ldurb           w16, [x1, #-1]
    //     0x94a94c: ldurb           w17, [x0, #-1]
    //     0x94a950: and             x16, x17, x16, lsr #2
    //     0x94a954: tst             x16, HEAP, lsr #32
    //     0x94a958: b.eq            #0x94a960
    //     0x94a95c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x94a960: r0 = Null
    //     0x94a960: mov             x0, NULL
    // 0x94a964: LeaveFrame
    //     0x94a964: mov             SP, fp
    //     0x94a968: ldp             fp, lr, [SP], #0x10
    // 0x94a96c: ret
    //     0x94a96c: ret             
    // 0x94a970: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94a970: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94a974: b               #0x94a8c8
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xa75c08, size: 0x30
    // 0xa75c08: ldr             x1, [SP, #8]
    // 0xa75c0c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa75c0c: ldur            w2, [x1, #0x17]
    // 0xa75c10: DecompressPointer r2
    //     0xa75c10: add             x2, x2, HEAP, lsl #32
    // 0xa75c14: LoadField: r1 = r2->field_f
    //     0xa75c14: ldur            w1, [x2, #0xf]
    // 0xa75c18: DecompressPointer r1
    //     0xa75c18: add             x1, x1, HEAP, lsl #32
    // 0xa75c1c: ldr             x2, [SP]
    // 0xa75c20: r3 = LoadInt32Instr(r2)
    //     0xa75c20: sbfx            x3, x2, #1, #0x1f
    //     0xa75c24: tbz             w2, #0, #0xa75c2c
    //     0xa75c28: ldur            x3, [x2, #7]
    // 0xa75c2c: StoreField: r1->field_1b = r3
    //     0xa75c2c: stur            x3, [x1, #0x1b]
    // 0xa75c30: r0 = Null
    //     0xa75c30: mov             x0, NULL
    // 0xa75c34: ret
    //     0xa75c34: ret             
  }
  _ lineThemeSlider(/* No info */) {
    // ** addr: 0xa75c38, size: 0x15a8
    // 0xa75c38: EnterFrame
    //     0xa75c38: stp             fp, lr, [SP, #-0x10]!
    //     0xa75c3c: mov             fp, SP
    // 0xa75c40: AllocStack(0x80)
    //     0xa75c40: sub             SP, SP, #0x80
    // 0xa75c44: SetupParameters(_GroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xa75c44: stur            x1, [fp, #-8]
    //     0xa75c48: stur            x2, [fp, #-0x10]
    //     0xa75c4c: stur            x3, [fp, #-0x18]
    // 0xa75c50: CheckStackOverflow
    //     0xa75c50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa75c54: cmp             SP, x16
    //     0xa75c58: b.ls            #0xa7711c
    // 0xa75c5c: r1 = 3
    //     0xa75c5c: movz            x1, #0x3
    // 0xa75c60: r0 = AllocateContext()
    //     0xa75c60: bl              #0x16f6108  ; AllocateContextStub
    // 0xa75c64: mov             x4, x0
    // 0xa75c68: ldur            x3, [fp, #-8]
    // 0xa75c6c: stur            x4, [fp, #-0x28]
    // 0xa75c70: StoreField: r4->field_f = r3
    //     0xa75c70: stur            w3, [x4, #0xf]
    // 0xa75c74: ldur            x2, [fp, #-0x10]
    // 0xa75c78: StoreField: r4->field_13 = r2
    //     0xa75c78: stur            w2, [x4, #0x13]
    // 0xa75c7c: ldur            x5, [fp, #-0x18]
    // 0xa75c80: r0 = BoxInt64Instr(r5)
    //     0xa75c80: sbfiz           x0, x5, #1, #0x1f
    //     0xa75c84: cmp             x5, x0, asr #1
    //     0xa75c88: b.eq            #0xa75c94
    //     0xa75c8c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa75c90: stur            x5, [x0, #7]
    // 0xa75c94: ArrayStore: r4[0] = r0  ; List_4
    //     0xa75c94: stur            w0, [x4, #0x17]
    // 0xa75c98: cmp             w2, NULL
    // 0xa75c9c: b.eq            #0xa77124
    // 0xa75ca0: LoadField: r0 = r2->field_e7
    //     0xa75ca0: ldur            w0, [x2, #0xe7]
    // 0xa75ca4: DecompressPointer r0
    //     0xa75ca4: add             x0, x0, HEAP, lsl #32
    // 0xa75ca8: cmp             w0, NULL
    // 0xa75cac: b.eq            #0xa77128
    // 0xa75cb0: LoadField: r5 = r0->field_b
    //     0xa75cb0: ldur            w5, [x0, #0xb]
    // 0xa75cb4: stur            x5, [fp, #-0x20]
    // 0xa75cb8: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xa75cb8: ldur            w0, [x3, #0x17]
    // 0xa75cbc: DecompressPointer r0
    //     0xa75cbc: add             x0, x0, HEAP, lsl #32
    // 0xa75cc0: r16 = Sentinel
    //     0xa75cc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa75cc4: cmp             w0, w16
    // 0xa75cc8: b.eq            #0xa7712c
    // 0xa75ccc: mov             x2, x4
    // 0xa75cd0: stur            x0, [fp, #-0x10]
    // 0xa75cd4: r1 = Function '<anonymous closure>':.
    //     0xa75cd4: add             x1, PP, #0x52, lsl #12  ; [pp+0x52d50] AnonymousClosure: (0xa75c08), in [package:customer_app/app/presentation/views/line/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::lineThemeSlider (0xa75c38)
    //     0xa75cd8: ldr             x1, [x1, #0xd50]
    // 0xa75cdc: r0 = AllocateClosure()
    //     0xa75cdc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa75ce0: ldur            x2, [fp, #-0x28]
    // 0xa75ce4: r1 = Function '<anonymous closure>':.
    //     0xa75ce4: add             x1, PP, #0x52, lsl #12  ; [pp+0x52d58] AnonymousClosure: (0xa772f0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::lineThemeSlider (0xa75c38)
    //     0xa75ce8: ldr             x1, [x1, #0xd58]
    // 0xa75cec: stur            x0, [fp, #-0x30]
    // 0xa75cf0: r0 = AllocateClosure()
    //     0xa75cf0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa75cf4: stur            x0, [fp, #-0x38]
    // 0xa75cf8: r0 = PageView()
    //     0xa75cf8: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xa75cfc: stur            x0, [fp, #-0x40]
    // 0xa75d00: ldur            x16, [fp, #-0x10]
    // 0xa75d04: str             x16, [SP]
    // 0xa75d08: mov             x1, x0
    // 0xa75d0c: ldur            x2, [fp, #-0x38]
    // 0xa75d10: ldur            x3, [fp, #-0x20]
    // 0xa75d14: ldur            x5, [fp, #-0x30]
    // 0xa75d18: r4 = const [0, 0x5, 0x1, 0x4, controller, 0x4, null]
    //     0xa75d18: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d60] List(7) [0, 0x5, 0x1, 0x4, "controller", 0x4, Null]
    //     0xa75d1c: ldr             x4, [x4, #0xd60]
    // 0xa75d20: r0 = PageView.builder()
    //     0xa75d20: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xa75d24: r0 = AspectRatio()
    //     0xa75d24: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xa75d28: d0 = 0.740741
    //     0xa75d28: add             x17, PP, #0x52, lsl #12  ; [pp+0x52d68] IMM: double(0.7407407407407407) from 0x3fe7b425ed097b42
    //     0xa75d2c: ldr             d0, [x17, #0xd68]
    // 0xa75d30: stur            x0, [fp, #-0x20]
    // 0xa75d34: StoreField: r0->field_f = d0
    //     0xa75d34: stur            d0, [x0, #0xf]
    // 0xa75d38: ldur            x1, [fp, #-0x40]
    // 0xa75d3c: StoreField: r0->field_b = r1
    //     0xa75d3c: stur            w1, [x0, #0xb]
    // 0xa75d40: ldur            x2, [fp, #-0x28]
    // 0xa75d44: LoadField: r1 = r2->field_13
    //     0xa75d44: ldur            w1, [x2, #0x13]
    // 0xa75d48: DecompressPointer r1
    //     0xa75d48: add             x1, x1, HEAP, lsl #32
    // 0xa75d4c: cmp             w1, NULL
    // 0xa75d50: b.eq            #0xa77138
    // 0xa75d54: LoadField: r3 = r1->field_53
    //     0xa75d54: ldur            w3, [x1, #0x53]
    // 0xa75d58: DecompressPointer r3
    //     0xa75d58: add             x3, x3, HEAP, lsl #32
    // 0xa75d5c: cmp             w3, NULL
    // 0xa75d60: b.ne            #0xa75d6c
    // 0xa75d64: r1 = Null
    //     0xa75d64: mov             x1, NULL
    // 0xa75d68: b               #0xa75d74
    // 0xa75d6c: LoadField: r1 = r3->field_b
    //     0xa75d6c: ldur            w1, [x3, #0xb]
    // 0xa75d70: DecompressPointer r1
    //     0xa75d70: add             x1, x1, HEAP, lsl #32
    // 0xa75d74: cmp             w1, NULL
    // 0xa75d78: b.ne            #0xa75d84
    // 0xa75d7c: r4 = ""
    //     0xa75d7c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa75d80: b               #0xa75d88
    // 0xa75d84: mov             x4, x1
    // 0xa75d88: ldur            x3, [fp, #-8]
    // 0xa75d8c: stur            x4, [fp, #-0x10]
    // 0xa75d90: LoadField: r1 = r3->field_f
    //     0xa75d90: ldur            w1, [x3, #0xf]
    // 0xa75d94: DecompressPointer r1
    //     0xa75d94: add             x1, x1, HEAP, lsl #32
    // 0xa75d98: cmp             w1, NULL
    // 0xa75d9c: b.eq            #0xa7713c
    // 0xa75da0: r0 = of()
    //     0xa75da0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa75da4: LoadField: r1 = r0->field_87
    //     0xa75da4: ldur            w1, [x0, #0x87]
    // 0xa75da8: DecompressPointer r1
    //     0xa75da8: add             x1, x1, HEAP, lsl #32
    // 0xa75dac: LoadField: r0 = r1->field_2b
    //     0xa75dac: ldur            w0, [x1, #0x2b]
    // 0xa75db0: DecompressPointer r0
    //     0xa75db0: add             x0, x0, HEAP, lsl #32
    // 0xa75db4: stur            x0, [fp, #-0x30]
    // 0xa75db8: r1 = Instance_Color
    //     0xa75db8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa75dbc: d0 = 0.700000
    //     0xa75dbc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa75dc0: ldr             d0, [x17, #0xf48]
    // 0xa75dc4: r0 = withOpacity()
    //     0xa75dc4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa75dc8: r16 = 12.000000
    //     0xa75dc8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa75dcc: ldr             x16, [x16, #0x9e8]
    // 0xa75dd0: stp             x0, x16, [SP]
    // 0xa75dd4: ldur            x1, [fp, #-0x30]
    // 0xa75dd8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa75dd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa75ddc: ldr             x4, [x4, #0xaa0]
    // 0xa75de0: r0 = copyWith()
    //     0xa75de0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa75de4: stur            x0, [fp, #-0x30]
    // 0xa75de8: r0 = Text()
    //     0xa75de8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa75dec: mov             x1, x0
    // 0xa75df0: ldur            x0, [fp, #-0x10]
    // 0xa75df4: stur            x1, [fp, #-0x38]
    // 0xa75df8: StoreField: r1->field_b = r0
    //     0xa75df8: stur            w0, [x1, #0xb]
    // 0xa75dfc: ldur            x0, [fp, #-0x30]
    // 0xa75e00: StoreField: r1->field_13 = r0
    //     0xa75e00: stur            w0, [x1, #0x13]
    // 0xa75e04: r0 = Instance_TextOverflow
    //     0xa75e04: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xa75e08: ldr             x0, [x0, #0xe10]
    // 0xa75e0c: StoreField: r1->field_2b = r0
    //     0xa75e0c: stur            w0, [x1, #0x2b]
    // 0xa75e10: r0 = 2
    //     0xa75e10: movz            x0, #0x2
    // 0xa75e14: StoreField: r1->field_37 = r0
    //     0xa75e14: stur            w0, [x1, #0x37]
    // 0xa75e18: r0 = Padding()
    //     0xa75e18: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa75e1c: mov             x2, x0
    // 0xa75e20: r1 = Instance_EdgeInsets
    //     0xa75e20: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xa75e24: ldr             x1, [x1, #0x990]
    // 0xa75e28: stur            x2, [fp, #-0x10]
    // 0xa75e2c: StoreField: r2->field_f = r1
    //     0xa75e2c: stur            w1, [x2, #0xf]
    // 0xa75e30: ldur            x0, [fp, #-0x38]
    // 0xa75e34: StoreField: r2->field_b = r0
    //     0xa75e34: stur            w0, [x2, #0xb]
    // 0xa75e38: ldur            x3, [fp, #-0x28]
    // 0xa75e3c: LoadField: r0 = r3->field_13
    //     0xa75e3c: ldur            w0, [x3, #0x13]
    // 0xa75e40: DecompressPointer r0
    //     0xa75e40: add             x0, x0, HEAP, lsl #32
    // 0xa75e44: cmp             w0, NULL
    // 0xa75e48: b.eq            #0xa77140
    // 0xa75e4c: LoadField: r4 = r0->field_e3
    //     0xa75e4c: ldur            w4, [x0, #0xe3]
    // 0xa75e50: DecompressPointer r4
    //     0xa75e50: add             x4, x4, HEAP, lsl #32
    // 0xa75e54: cmp             w4, NULL
    // 0xa75e58: b.ne            #0xa75e64
    // 0xa75e5c: r0 = Null
    //     0xa75e5c: mov             x0, NULL
    // 0xa75e60: b               #0xa75e6c
    // 0xa75e64: LoadField: r0 = r4->field_7
    //     0xa75e64: ldur            w0, [x4, #7]
    // 0xa75e68: DecompressPointer r0
    //     0xa75e68: add             x0, x0, HEAP, lsl #32
    // 0xa75e6c: r4 = LoadClassIdInstr(r0)
    //     0xa75e6c: ldur            x4, [x0, #-1]
    //     0xa75e70: ubfx            x4, x4, #0xc, #0x14
    // 0xa75e74: r16 = 0.000000
    //     0xa75e74: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xa75e78: stp             x16, x0, [SP]
    // 0xa75e7c: mov             x0, x4
    // 0xa75e80: mov             lr, x0
    // 0xa75e84: ldr             lr, [x21, lr, lsl #3]
    // 0xa75e88: blr             lr
    // 0xa75e8c: eor             x1, x0, #0x10
    // 0xa75e90: ldur            x2, [fp, #-0x28]
    // 0xa75e94: stur            x1, [fp, #-0x30]
    // 0xa75e98: LoadField: r0 = r2->field_13
    //     0xa75e98: ldur            w0, [x2, #0x13]
    // 0xa75e9c: DecompressPointer r0
    //     0xa75e9c: add             x0, x0, HEAP, lsl #32
    // 0xa75ea0: cmp             w0, NULL
    // 0xa75ea4: b.eq            #0xa77144
    // 0xa75ea8: LoadField: r3 = r0->field_e3
    //     0xa75ea8: ldur            w3, [x0, #0xe3]
    // 0xa75eac: DecompressPointer r3
    //     0xa75eac: add             x3, x3, HEAP, lsl #32
    // 0xa75eb0: cmp             w3, NULL
    // 0xa75eb4: b.ne            #0xa75ec0
    // 0xa75eb8: r0 = Null
    //     0xa75eb8: mov             x0, NULL
    // 0xa75ebc: b               #0xa75ec8
    // 0xa75ec0: LoadField: r0 = r3->field_f
    //     0xa75ec0: ldur            w0, [x3, #0xf]
    // 0xa75ec4: DecompressPointer r0
    //     0xa75ec4: add             x0, x0, HEAP, lsl #32
    // 0xa75ec8: r3 = LoadClassIdInstr(r0)
    //     0xa75ec8: ldur            x3, [x0, #-1]
    //     0xa75ecc: ubfx            x3, x3, #0xc, #0x14
    // 0xa75ed0: r16 = "product_rating"
    //     0xa75ed0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23f20] "product_rating"
    //     0xa75ed4: ldr             x16, [x16, #0xf20]
    // 0xa75ed8: stp             x16, x0, [SP]
    // 0xa75edc: mov             x0, x3
    // 0xa75ee0: mov             lr, x0
    // 0xa75ee4: ldr             lr, [x21, lr, lsl #3]
    // 0xa75ee8: blr             lr
    // 0xa75eec: tbnz            w0, #4, #0xa763d8
    // 0xa75ef0: ldur            x2, [fp, #-0x28]
    // 0xa75ef4: LoadField: r0 = r2->field_13
    //     0xa75ef4: ldur            w0, [x2, #0x13]
    // 0xa75ef8: DecompressPointer r0
    //     0xa75ef8: add             x0, x0, HEAP, lsl #32
    // 0xa75efc: cmp             w0, NULL
    // 0xa75f00: b.eq            #0xa77148
    // 0xa75f04: LoadField: r1 = r0->field_e3
    //     0xa75f04: ldur            w1, [x0, #0xe3]
    // 0xa75f08: DecompressPointer r1
    //     0xa75f08: add             x1, x1, HEAP, lsl #32
    // 0xa75f0c: cmp             w1, NULL
    // 0xa75f10: b.ne            #0xa75f1c
    // 0xa75f14: r0 = Null
    //     0xa75f14: mov             x0, NULL
    // 0xa75f18: b               #0xa75f24
    // 0xa75f1c: LoadField: r0 = r1->field_7
    //     0xa75f1c: ldur            w0, [x1, #7]
    // 0xa75f20: DecompressPointer r0
    //     0xa75f20: add             x0, x0, HEAP, lsl #32
    // 0xa75f24: cmp             w0, NULL
    // 0xa75f28: r16 = true
    //     0xa75f28: add             x16, NULL, #0x20  ; true
    // 0xa75f2c: r17 = false
    //     0xa75f2c: add             x17, NULL, #0x30  ; false
    // 0xa75f30: csel            x3, x16, x17, ne
    // 0xa75f34: stur            x3, [fp, #-0x38]
    // 0xa75f38: cmp             w1, NULL
    // 0xa75f3c: b.ne            #0xa75f48
    // 0xa75f40: r0 = Null
    //     0xa75f40: mov             x0, NULL
    // 0xa75f44: b               #0xa75f50
    // 0xa75f48: LoadField: r0 = r1->field_7
    //     0xa75f48: ldur            w0, [x1, #7]
    // 0xa75f4c: DecompressPointer r0
    //     0xa75f4c: add             x0, x0, HEAP, lsl #32
    // 0xa75f50: cmp             w0, NULL
    // 0xa75f54: b.ne            #0xa75f60
    // 0xa75f58: d1 = 0.000000
    //     0xa75f58: eor             v1.16b, v1.16b, v1.16b
    // 0xa75f5c: b               #0xa75f68
    // 0xa75f60: LoadField: d0 = r0->field_7
    //     0xa75f60: ldur            d0, [x0, #7]
    // 0xa75f64: mov             v1.16b, v0.16b
    // 0xa75f68: d0 = 4.000000
    //     0xa75f68: fmov            d0, #4.00000000
    // 0xa75f6c: fcmp            d1, d0
    // 0xa75f70: b.lt            #0xa75f80
    // 0xa75f74: r0 = Instance_Color
    //     0xa75f74: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa75f78: ldr             x0, [x0, #0x858]
    // 0xa75f7c: b               #0xa76028
    // 0xa75f80: cmp             w1, NULL
    // 0xa75f84: b.ne            #0xa75f90
    // 0xa75f88: r0 = Null
    //     0xa75f88: mov             x0, NULL
    // 0xa75f8c: b               #0xa75f98
    // 0xa75f90: LoadField: r0 = r1->field_7
    //     0xa75f90: ldur            w0, [x1, #7]
    // 0xa75f94: DecompressPointer r0
    //     0xa75f94: add             x0, x0, HEAP, lsl #32
    // 0xa75f98: cmp             w0, NULL
    // 0xa75f9c: b.ne            #0xa75fa8
    // 0xa75fa0: d1 = 0.000000
    //     0xa75fa0: eor             v1.16b, v1.16b, v1.16b
    // 0xa75fa4: b               #0xa75fb0
    // 0xa75fa8: LoadField: d0 = r0->field_7
    //     0xa75fa8: ldur            d0, [x0, #7]
    // 0xa75fac: mov             v1.16b, v0.16b
    // 0xa75fb0: d0 = 3.500000
    //     0xa75fb0: fmov            d0, #3.50000000
    // 0xa75fb4: fcmp            d1, d0
    // 0xa75fb8: b.lt            #0xa75fd4
    // 0xa75fbc: r1 = Instance_Color
    //     0xa75fbc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa75fc0: ldr             x1, [x1, #0x858]
    // 0xa75fc4: d0 = 0.700000
    //     0xa75fc4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa75fc8: ldr             d0, [x17, #0xf48]
    // 0xa75fcc: r0 = withOpacity()
    //     0xa75fcc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa75fd0: b               #0xa76024
    // 0xa75fd4: cmp             w1, NULL
    // 0xa75fd8: b.ne            #0xa75fe4
    // 0xa75fdc: r0 = Null
    //     0xa75fdc: mov             x0, NULL
    // 0xa75fe0: b               #0xa75fec
    // 0xa75fe4: LoadField: r0 = r1->field_7
    //     0xa75fe4: ldur            w0, [x1, #7]
    // 0xa75fe8: DecompressPointer r0
    //     0xa75fe8: add             x0, x0, HEAP, lsl #32
    // 0xa75fec: cmp             w0, NULL
    // 0xa75ff0: b.ne            #0xa75ffc
    // 0xa75ff4: d1 = 0.000000
    //     0xa75ff4: eor             v1.16b, v1.16b, v1.16b
    // 0xa75ff8: b               #0xa76004
    // 0xa75ffc: LoadField: d0 = r0->field_7
    //     0xa75ffc: ldur            d0, [x0, #7]
    // 0xa76000: mov             v1.16b, v0.16b
    // 0xa76004: d0 = 2.000000
    //     0xa76004: fmov            d0, #2.00000000
    // 0xa76008: fcmp            d1, d0
    // 0xa7600c: b.lt            #0xa7601c
    // 0xa76010: r0 = Instance_Color
    //     0xa76010: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xa76014: ldr             x0, [x0, #0x860]
    // 0xa76018: b               #0xa76024
    // 0xa7601c: r0 = Instance_Color
    //     0xa7601c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xa76020: ldr             x0, [x0, #0x50]
    // 0xa76024: ldur            x2, [fp, #-0x28]
    // 0xa76028: stur            x0, [fp, #-0x40]
    // 0xa7602c: r0 = ColorFilter()
    //     0xa7602c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xa76030: mov             x1, x0
    // 0xa76034: ldur            x0, [fp, #-0x40]
    // 0xa76038: stur            x1, [fp, #-0x48]
    // 0xa7603c: StoreField: r1->field_7 = r0
    //     0xa7603c: stur            w0, [x1, #7]
    // 0xa76040: r0 = Instance_BlendMode
    //     0xa76040: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa76044: ldr             x0, [x0, #0xb30]
    // 0xa76048: StoreField: r1->field_b = r0
    //     0xa76048: stur            w0, [x1, #0xb]
    // 0xa7604c: r2 = 1
    //     0xa7604c: movz            x2, #0x1
    // 0xa76050: StoreField: r1->field_13 = r2
    //     0xa76050: stur            x2, [x1, #0x13]
    // 0xa76054: r0 = SvgPicture()
    //     0xa76054: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa76058: stur            x0, [fp, #-0x40]
    // 0xa7605c: ldur            x16, [fp, #-0x48]
    // 0xa76060: str             x16, [SP]
    // 0xa76064: mov             x1, x0
    // 0xa76068: r2 = "assets/images/green_star.svg"
    //     0xa76068: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xa7606c: ldr             x2, [x2, #0x9a0]
    // 0xa76070: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xa76070: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xa76074: ldr             x4, [x4, #0xa38]
    // 0xa76078: r0 = SvgPicture.asset()
    //     0xa76078: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa7607c: ldur            x0, [fp, #-0x28]
    // 0xa76080: LoadField: r1 = r0->field_13
    //     0xa76080: ldur            w1, [x0, #0x13]
    // 0xa76084: DecompressPointer r1
    //     0xa76084: add             x1, x1, HEAP, lsl #32
    // 0xa76088: cmp             w1, NULL
    // 0xa7608c: b.eq            #0xa7714c
    // 0xa76090: LoadField: r2 = r1->field_e3
    //     0xa76090: ldur            w2, [x1, #0xe3]
    // 0xa76094: DecompressPointer r2
    //     0xa76094: add             x2, x2, HEAP, lsl #32
    // 0xa76098: cmp             w2, NULL
    // 0xa7609c: b.ne            #0xa760a8
    // 0xa760a0: r0 = Null
    //     0xa760a0: mov             x0, NULL
    // 0xa760a4: b               #0xa760c8
    // 0xa760a8: LoadField: r1 = r2->field_7
    //     0xa760a8: ldur            w1, [x2, #7]
    // 0xa760ac: DecompressPointer r1
    //     0xa760ac: add             x1, x1, HEAP, lsl #32
    // 0xa760b0: cmp             w1, NULL
    // 0xa760b4: b.ne            #0xa760c0
    // 0xa760b8: r0 = Null
    //     0xa760b8: mov             x0, NULL
    // 0xa760bc: b               #0xa760c8
    // 0xa760c0: r2 = 1
    //     0xa760c0: movz            x2, #0x1
    // 0xa760c4: r0 = toStringAsFixed()
    //     0xa760c4: bl              #0x7c1ef8  ; [dart:core] _Double::toStringAsFixed
    // 0xa760c8: cmp             w0, NULL
    // 0xa760cc: b.ne            #0xa760d8
    // 0xa760d0: r4 = ""
    //     0xa760d0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa760d4: b               #0xa760dc
    // 0xa760d8: mov             x4, x0
    // 0xa760dc: ldur            x3, [fp, #-8]
    // 0xa760e0: ldur            x2, [fp, #-0x28]
    // 0xa760e4: ldur            x0, [fp, #-0x40]
    // 0xa760e8: stur            x4, [fp, #-0x48]
    // 0xa760ec: LoadField: r1 = r3->field_f
    //     0xa760ec: ldur            w1, [x3, #0xf]
    // 0xa760f0: DecompressPointer r1
    //     0xa760f0: add             x1, x1, HEAP, lsl #32
    // 0xa760f4: cmp             w1, NULL
    // 0xa760f8: b.eq            #0xa77150
    // 0xa760fc: r0 = of()
    //     0xa760fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa76100: LoadField: r1 = r0->field_87
    //     0xa76100: ldur            w1, [x0, #0x87]
    // 0xa76104: DecompressPointer r1
    //     0xa76104: add             x1, x1, HEAP, lsl #32
    // 0xa76108: LoadField: r0 = r1->field_7
    //     0xa76108: ldur            w0, [x1, #7]
    // 0xa7610c: DecompressPointer r0
    //     0xa7610c: add             x0, x0, HEAP, lsl #32
    // 0xa76110: r16 = 12.000000
    //     0xa76110: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa76114: ldr             x16, [x16, #0x9e8]
    // 0xa76118: r30 = Instance_Color
    //     0xa76118: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa7611c: stp             lr, x16, [SP]
    // 0xa76120: mov             x1, x0
    // 0xa76124: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa76124: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa76128: ldr             x4, [x4, #0xaa0]
    // 0xa7612c: r0 = copyWith()
    //     0xa7612c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa76130: stur            x0, [fp, #-0x50]
    // 0xa76134: r0 = Text()
    //     0xa76134: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa76138: mov             x3, x0
    // 0xa7613c: ldur            x0, [fp, #-0x48]
    // 0xa76140: stur            x3, [fp, #-0x58]
    // 0xa76144: StoreField: r3->field_b = r0
    //     0xa76144: stur            w0, [x3, #0xb]
    // 0xa76148: ldur            x0, [fp, #-0x50]
    // 0xa7614c: StoreField: r3->field_13 = r0
    //     0xa7614c: stur            w0, [x3, #0x13]
    // 0xa76150: r1 = Null
    //     0xa76150: mov             x1, NULL
    // 0xa76154: r2 = 4
    //     0xa76154: movz            x2, #0x4
    // 0xa76158: r0 = AllocateArray()
    //     0xa76158: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa7615c: mov             x2, x0
    // 0xa76160: ldur            x0, [fp, #-0x40]
    // 0xa76164: stur            x2, [fp, #-0x48]
    // 0xa76168: StoreField: r2->field_f = r0
    //     0xa76168: stur            w0, [x2, #0xf]
    // 0xa7616c: ldur            x0, [fp, #-0x58]
    // 0xa76170: StoreField: r2->field_13 = r0
    //     0xa76170: stur            w0, [x2, #0x13]
    // 0xa76174: r1 = <Widget>
    //     0xa76174: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa76178: r0 = AllocateGrowableArray()
    //     0xa76178: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa7617c: mov             x3, x0
    // 0xa76180: ldur            x0, [fp, #-0x48]
    // 0xa76184: stur            x3, [fp, #-0x50]
    // 0xa76188: StoreField: r3->field_f = r0
    //     0xa76188: stur            w0, [x3, #0xf]
    // 0xa7618c: r0 = 4
    //     0xa7618c: movz            x0, #0x4
    // 0xa76190: StoreField: r3->field_b = r0
    //     0xa76190: stur            w0, [x3, #0xb]
    // 0xa76194: ldur            x4, [fp, #-0x28]
    // 0xa76198: LoadField: r1 = r4->field_13
    //     0xa76198: ldur            w1, [x4, #0x13]
    // 0xa7619c: DecompressPointer r1
    //     0xa7619c: add             x1, x1, HEAP, lsl #32
    // 0xa761a0: cmp             w1, NULL
    // 0xa761a4: b.eq            #0xa77154
    // 0xa761a8: LoadField: r2 = r1->field_e3
    //     0xa761a8: ldur            w2, [x1, #0xe3]
    // 0xa761ac: DecompressPointer r2
    //     0xa761ac: add             x2, x2, HEAP, lsl #32
    // 0xa761b0: cmp             w2, NULL
    // 0xa761b4: b.ne            #0xa761c0
    // 0xa761b8: mov             x2, x3
    // 0xa761bc: b               #0xa76330
    // 0xa761c0: LoadField: r5 = r2->field_b
    //     0xa761c0: ldur            w5, [x2, #0xb]
    // 0xa761c4: DecompressPointer r5
    //     0xa761c4: add             x5, x5, HEAP, lsl #32
    // 0xa761c8: stur            x5, [fp, #-0x40]
    // 0xa761cc: cmp             w5, NULL
    // 0xa761d0: b.eq            #0xa7632c
    // 0xa761d4: r1 = Null
    //     0xa761d4: mov             x1, NULL
    // 0xa761d8: r2 = 6
    //     0xa761d8: movz            x2, #0x6
    // 0xa761dc: r0 = AllocateArray()
    //     0xa761dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa761e0: r16 = " | ("
    //     0xa761e0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d70] " | ("
    //     0xa761e4: ldr             x16, [x16, #0xd70]
    // 0xa761e8: StoreField: r0->field_f = r16
    //     0xa761e8: stur            w16, [x0, #0xf]
    // 0xa761ec: ldur            x1, [fp, #-0x40]
    // 0xa761f0: cmp             w1, NULL
    // 0xa761f4: b.ne            #0xa76200
    // 0xa761f8: r3 = Null
    //     0xa761f8: mov             x3, NULL
    // 0xa761fc: b               #0xa76224
    // 0xa76200: LoadField: d0 = r1->field_7
    //     0xa76200: ldur            d0, [x1, #7]
    // 0xa76204: fcmp            d0, d0
    // 0xa76208: b.vs            #0xa77158
    // 0xa7620c: fcvtzs          x1, d0
    // 0xa76210: asr             x16, x1, #0x1e
    // 0xa76214: cmp             x16, x1, asr #63
    // 0xa76218: b.ne            #0xa77158
    // 0xa7621c: lsl             x1, x1, #1
    // 0xa76220: mov             x3, x1
    // 0xa76224: ldur            x2, [fp, #-8]
    // 0xa76228: ldur            x1, [fp, #-0x50]
    // 0xa7622c: StoreField: r0->field_13 = r3
    //     0xa7622c: stur            w3, [x0, #0x13]
    // 0xa76230: r16 = ")"
    //     0xa76230: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xa76234: ArrayStore: r0[0] = r16  ; List_4
    //     0xa76234: stur            w16, [x0, #0x17]
    // 0xa76238: str             x0, [SP]
    // 0xa7623c: r0 = _interpolate()
    //     0xa7623c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa76240: mov             x2, x0
    // 0xa76244: ldur            x0, [fp, #-8]
    // 0xa76248: stur            x2, [fp, #-0x40]
    // 0xa7624c: LoadField: r1 = r0->field_f
    //     0xa7624c: ldur            w1, [x0, #0xf]
    // 0xa76250: DecompressPointer r1
    //     0xa76250: add             x1, x1, HEAP, lsl #32
    // 0xa76254: cmp             w1, NULL
    // 0xa76258: b.eq            #0xa77180
    // 0xa7625c: r0 = of()
    //     0xa7625c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa76260: LoadField: r1 = r0->field_87
    //     0xa76260: ldur            w1, [x0, #0x87]
    // 0xa76264: DecompressPointer r1
    //     0xa76264: add             x1, x1, HEAP, lsl #32
    // 0xa76268: LoadField: r0 = r1->field_2b
    //     0xa76268: ldur            w0, [x1, #0x2b]
    // 0xa7626c: DecompressPointer r0
    //     0xa7626c: add             x0, x0, HEAP, lsl #32
    // 0xa76270: r16 = 12.000000
    //     0xa76270: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa76274: ldr             x16, [x16, #0x9e8]
    // 0xa76278: r30 = Instance_Color
    //     0xa76278: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa7627c: stp             lr, x16, [SP]
    // 0xa76280: mov             x1, x0
    // 0xa76284: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa76284: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa76288: ldr             x4, [x4, #0xaa0]
    // 0xa7628c: r0 = copyWith()
    //     0xa7628c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa76290: stur            x0, [fp, #-0x48]
    // 0xa76294: r0 = Text()
    //     0xa76294: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa76298: mov             x2, x0
    // 0xa7629c: ldur            x0, [fp, #-0x40]
    // 0xa762a0: stur            x2, [fp, #-0x58]
    // 0xa762a4: StoreField: r2->field_b = r0
    //     0xa762a4: stur            w0, [x2, #0xb]
    // 0xa762a8: ldur            x0, [fp, #-0x48]
    // 0xa762ac: StoreField: r2->field_13 = r0
    //     0xa762ac: stur            w0, [x2, #0x13]
    // 0xa762b0: ldur            x0, [fp, #-0x50]
    // 0xa762b4: LoadField: r1 = r0->field_b
    //     0xa762b4: ldur            w1, [x0, #0xb]
    // 0xa762b8: LoadField: r3 = r0->field_f
    //     0xa762b8: ldur            w3, [x0, #0xf]
    // 0xa762bc: DecompressPointer r3
    //     0xa762bc: add             x3, x3, HEAP, lsl #32
    // 0xa762c0: LoadField: r4 = r3->field_b
    //     0xa762c0: ldur            w4, [x3, #0xb]
    // 0xa762c4: r3 = LoadInt32Instr(r1)
    //     0xa762c4: sbfx            x3, x1, #1, #0x1f
    // 0xa762c8: stur            x3, [fp, #-0x18]
    // 0xa762cc: r1 = LoadInt32Instr(r4)
    //     0xa762cc: sbfx            x1, x4, #1, #0x1f
    // 0xa762d0: cmp             x3, x1
    // 0xa762d4: b.ne            #0xa762e0
    // 0xa762d8: mov             x1, x0
    // 0xa762dc: r0 = _growToNextCapacity()
    //     0xa762dc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa762e0: ldur            x2, [fp, #-0x50]
    // 0xa762e4: ldur            x3, [fp, #-0x18]
    // 0xa762e8: add             x0, x3, #1
    // 0xa762ec: lsl             x1, x0, #1
    // 0xa762f0: StoreField: r2->field_b = r1
    //     0xa762f0: stur            w1, [x2, #0xb]
    // 0xa762f4: LoadField: r1 = r2->field_f
    //     0xa762f4: ldur            w1, [x2, #0xf]
    // 0xa762f8: DecompressPointer r1
    //     0xa762f8: add             x1, x1, HEAP, lsl #32
    // 0xa762fc: ldur            x0, [fp, #-0x58]
    // 0xa76300: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa76300: add             x25, x1, x3, lsl #2
    //     0xa76304: add             x25, x25, #0xf
    //     0xa76308: str             w0, [x25]
    //     0xa7630c: tbz             w0, #0, #0xa76328
    //     0xa76310: ldurb           w16, [x1, #-1]
    //     0xa76314: ldurb           w17, [x0, #-1]
    //     0xa76318: and             x16, x17, x16, lsr #2
    //     0xa7631c: tst             x16, HEAP, lsr #32
    //     0xa76320: b.eq            #0xa76328
    //     0xa76324: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa76328: b               #0xa76330
    // 0xa7632c: mov             x2, x3
    // 0xa76330: ldur            x0, [fp, #-0x38]
    // 0xa76334: r0 = Row()
    //     0xa76334: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa76338: r2 = Instance_Axis
    //     0xa76338: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa7633c: stur            x0, [fp, #-0x40]
    // 0xa76340: StoreField: r0->field_f = r2
    //     0xa76340: stur            w2, [x0, #0xf]
    // 0xa76344: r1 = Instance_MainAxisAlignment
    //     0xa76344: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa76348: ldr             x1, [x1, #0xa08]
    // 0xa7634c: StoreField: r0->field_13 = r1
    //     0xa7634c: stur            w1, [x0, #0x13]
    // 0xa76350: r3 = Instance_MainAxisSize
    //     0xa76350: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa76354: ldr             x3, [x3, #0xa10]
    // 0xa76358: ArrayStore: r0[0] = r3  ; List_4
    //     0xa76358: stur            w3, [x0, #0x17]
    // 0xa7635c: r4 = Instance_CrossAxisAlignment
    //     0xa7635c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa76360: ldr             x4, [x4, #0xa18]
    // 0xa76364: StoreField: r0->field_1b = r4
    //     0xa76364: stur            w4, [x0, #0x1b]
    // 0xa76368: r2 = Instance_VerticalDirection
    //     0xa76368: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa7636c: ldr             x2, [x2, #0xa20]
    // 0xa76370: StoreField: r0->field_23 = r2
    //     0xa76370: stur            w2, [x0, #0x23]
    // 0xa76374: r3 = Instance_Clip
    //     0xa76374: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa76378: ldr             x3, [x3, #0x38]
    // 0xa7637c: StoreField: r0->field_2b = r3
    //     0xa7637c: stur            w3, [x0, #0x2b]
    // 0xa76380: StoreField: r0->field_2f = rZR
    //     0xa76380: stur            xzr, [x0, #0x2f]
    // 0xa76384: ldur            x4, [fp, #-0x50]
    // 0xa76388: StoreField: r0->field_b = r4
    //     0xa76388: stur            w4, [x0, #0xb]
    // 0xa7638c: r0 = Visibility()
    //     0xa7638c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa76390: mov             x1, x0
    // 0xa76394: ldur            x0, [fp, #-0x40]
    // 0xa76398: StoreField: r1->field_b = r0
    //     0xa76398: stur            w0, [x1, #0xb]
    // 0xa7639c: r5 = Instance_SizedBox
    //     0xa7639c: ldr             x5, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa763a0: StoreField: r1->field_f = r5
    //     0xa763a0: stur            w5, [x1, #0xf]
    // 0xa763a4: ldur            x0, [fp, #-0x38]
    // 0xa763a8: StoreField: r1->field_13 = r0
    //     0xa763a8: stur            w0, [x1, #0x13]
    // 0xa763ac: r6 = false
    //     0xa763ac: add             x6, NULL, #0x30  ; false
    // 0xa763b0: ArrayStore: r1[0] = r6  ; List_4
    //     0xa763b0: stur            w6, [x1, #0x17]
    // 0xa763b4: StoreField: r1->field_1b = r6
    //     0xa763b4: stur            w6, [x1, #0x1b]
    // 0xa763b8: StoreField: r1->field_1f = r6
    //     0xa763b8: stur            w6, [x1, #0x1f]
    // 0xa763bc: StoreField: r1->field_23 = r6
    //     0xa763bc: stur            w6, [x1, #0x23]
    // 0xa763c0: StoreField: r1->field_27 = r6
    //     0xa763c0: stur            w6, [x1, #0x27]
    // 0xa763c4: StoreField: r1->field_2b = r6
    //     0xa763c4: stur            w6, [x1, #0x2b]
    // 0xa763c8: mov             x0, x5
    // 0xa763cc: mov             x5, x1
    // 0xa763d0: mov             x2, x6
    // 0xa763d4: b               #0xa766fc
    // 0xa763d8: ldur            x7, [fp, #-0x28]
    // 0xa763dc: r5 = Instance_SizedBox
    //     0xa763dc: ldr             x5, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa763e0: r6 = false
    //     0xa763e0: add             x6, NULL, #0x30  ; false
    // 0xa763e4: r4 = Instance_CrossAxisAlignment
    //     0xa763e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa763e8: ldr             x4, [x4, #0xa18]
    // 0xa763ec: r3 = Instance_MainAxisSize
    //     0xa763ec: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa763f0: ldr             x3, [x3, #0xa10]
    // 0xa763f4: r2 = Instance_Axis
    //     0xa763f4: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa763f8: r0 = Instance_BlendMode
    //     0xa763f8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa763fc: ldr             x0, [x0, #0xb30]
    // 0xa76400: LoadField: r1 = r7->field_13
    //     0xa76400: ldur            w1, [x7, #0x13]
    // 0xa76404: DecompressPointer r1
    //     0xa76404: add             x1, x1, HEAP, lsl #32
    // 0xa76408: cmp             w1, NULL
    // 0xa7640c: b.eq            #0xa77184
    // 0xa76410: LoadField: r8 = r1->field_e3
    //     0xa76410: ldur            w8, [x1, #0xe3]
    // 0xa76414: DecompressPointer r8
    //     0xa76414: add             x8, x8, HEAP, lsl #32
    // 0xa76418: cmp             w8, NULL
    // 0xa7641c: b.ne            #0xa76428
    // 0xa76420: r1 = Null
    //     0xa76420: mov             x1, NULL
    // 0xa76424: b               #0xa76430
    // 0xa76428: LoadField: r1 = r8->field_7
    //     0xa76428: ldur            w1, [x8, #7]
    // 0xa7642c: DecompressPointer r1
    //     0xa7642c: add             x1, x1, HEAP, lsl #32
    // 0xa76430: ldur            x8, [fp, #-8]
    // 0xa76434: cmp             w1, NULL
    // 0xa76438: r16 = true
    //     0xa76438: add             x16, NULL, #0x20  ; true
    // 0xa7643c: r17 = false
    //     0xa7643c: add             x17, NULL, #0x30  ; false
    // 0xa76440: csel            x9, x16, x17, ne
    // 0xa76444: stur            x9, [fp, #-0x38]
    // 0xa76448: LoadField: r1 = r8->field_f
    //     0xa76448: ldur            w1, [x8, #0xf]
    // 0xa7644c: DecompressPointer r1
    //     0xa7644c: add             x1, x1, HEAP, lsl #32
    // 0xa76450: cmp             w1, NULL
    // 0xa76454: b.eq            #0xa77188
    // 0xa76458: r0 = of()
    //     0xa76458: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa7645c: LoadField: r1 = r0->field_5b
    //     0xa7645c: ldur            w1, [x0, #0x5b]
    // 0xa76460: DecompressPointer r1
    //     0xa76460: add             x1, x1, HEAP, lsl #32
    // 0xa76464: stur            x1, [fp, #-0x40]
    // 0xa76468: r0 = ColorFilter()
    //     0xa76468: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xa7646c: mov             x1, x0
    // 0xa76470: ldur            x0, [fp, #-0x40]
    // 0xa76474: stur            x1, [fp, #-0x48]
    // 0xa76478: StoreField: r1->field_7 = r0
    //     0xa76478: stur            w0, [x1, #7]
    // 0xa7647c: r0 = Instance_BlendMode
    //     0xa7647c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa76480: ldr             x0, [x0, #0xb30]
    // 0xa76484: StoreField: r1->field_b = r0
    //     0xa76484: stur            w0, [x1, #0xb]
    // 0xa76488: r2 = 1
    //     0xa76488: movz            x2, #0x1
    // 0xa7648c: StoreField: r1->field_13 = r2
    //     0xa7648c: stur            x2, [x1, #0x13]
    // 0xa76490: r0 = SvgPicture()
    //     0xa76490: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa76494: stur            x0, [fp, #-0x40]
    // 0xa76498: ldur            x16, [fp, #-0x48]
    // 0xa7649c: str             x16, [SP]
    // 0xa764a0: mov             x1, x0
    // 0xa764a4: r2 = "assets/images/green_star.svg"
    //     0xa764a4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xa764a8: ldr             x2, [x2, #0x9a0]
    // 0xa764ac: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xa764ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xa764b0: ldr             x4, [x4, #0xa38]
    // 0xa764b4: r0 = SvgPicture.asset()
    //     0xa764b4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa764b8: ldur            x0, [fp, #-0x28]
    // 0xa764bc: LoadField: r1 = r0->field_13
    //     0xa764bc: ldur            w1, [x0, #0x13]
    // 0xa764c0: DecompressPointer r1
    //     0xa764c0: add             x1, x1, HEAP, lsl #32
    // 0xa764c4: cmp             w1, NULL
    // 0xa764c8: b.eq            #0xa7718c
    // 0xa764cc: LoadField: r2 = r1->field_e3
    //     0xa764cc: ldur            w2, [x1, #0xe3]
    // 0xa764d0: DecompressPointer r2
    //     0xa764d0: add             x2, x2, HEAP, lsl #32
    // 0xa764d4: cmp             w2, NULL
    // 0xa764d8: b.ne            #0xa764e4
    // 0xa764dc: r0 = Null
    //     0xa764dc: mov             x0, NULL
    // 0xa764e0: b               #0xa76504
    // 0xa764e4: LoadField: r1 = r2->field_7
    //     0xa764e4: ldur            w1, [x2, #7]
    // 0xa764e8: DecompressPointer r1
    //     0xa764e8: add             x1, x1, HEAP, lsl #32
    // 0xa764ec: cmp             w1, NULL
    // 0xa764f0: b.ne            #0xa764fc
    // 0xa764f4: r0 = Null
    //     0xa764f4: mov             x0, NULL
    // 0xa764f8: b               #0xa76504
    // 0xa764fc: r2 = 1
    //     0xa764fc: movz            x2, #0x1
    // 0xa76500: r0 = toStringAsFixed()
    //     0xa76500: bl              #0x7c1ef8  ; [dart:core] _Double::toStringAsFixed
    // 0xa76504: cmp             w0, NULL
    // 0xa76508: b.ne            #0xa76514
    // 0xa7650c: r4 = ""
    //     0xa7650c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa76510: b               #0xa76518
    // 0xa76514: mov             x4, x0
    // 0xa76518: ldur            x2, [fp, #-8]
    // 0xa7651c: ldur            x3, [fp, #-0x38]
    // 0xa76520: ldur            x0, [fp, #-0x40]
    // 0xa76524: stur            x4, [fp, #-0x48]
    // 0xa76528: LoadField: r1 = r2->field_f
    //     0xa76528: ldur            w1, [x2, #0xf]
    // 0xa7652c: DecompressPointer r1
    //     0xa7652c: add             x1, x1, HEAP, lsl #32
    // 0xa76530: cmp             w1, NULL
    // 0xa76534: b.eq            #0xa77190
    // 0xa76538: r0 = of()
    //     0xa76538: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa7653c: LoadField: r1 = r0->field_87
    //     0xa7653c: ldur            w1, [x0, #0x87]
    // 0xa76540: DecompressPointer r1
    //     0xa76540: add             x1, x1, HEAP, lsl #32
    // 0xa76544: LoadField: r0 = r1->field_7
    //     0xa76544: ldur            w0, [x1, #7]
    // 0xa76548: DecompressPointer r0
    //     0xa76548: add             x0, x0, HEAP, lsl #32
    // 0xa7654c: r16 = 12.000000
    //     0xa7654c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa76550: ldr             x16, [x16, #0x9e8]
    // 0xa76554: r30 = Instance_Color
    //     0xa76554: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa76558: stp             lr, x16, [SP]
    // 0xa7655c: mov             x1, x0
    // 0xa76560: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa76560: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa76564: ldr             x4, [x4, #0xaa0]
    // 0xa76568: r0 = copyWith()
    //     0xa76568: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa7656c: stur            x0, [fp, #-0x50]
    // 0xa76570: r0 = Text()
    //     0xa76570: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa76574: mov             x2, x0
    // 0xa76578: ldur            x0, [fp, #-0x48]
    // 0xa7657c: stur            x2, [fp, #-0x58]
    // 0xa76580: StoreField: r2->field_b = r0
    //     0xa76580: stur            w0, [x2, #0xb]
    // 0xa76584: ldur            x0, [fp, #-0x50]
    // 0xa76588: StoreField: r2->field_13 = r0
    //     0xa76588: stur            w0, [x2, #0x13]
    // 0xa7658c: ldur            x0, [fp, #-8]
    // 0xa76590: LoadField: r1 = r0->field_f
    //     0xa76590: ldur            w1, [x0, #0xf]
    // 0xa76594: DecompressPointer r1
    //     0xa76594: add             x1, x1, HEAP, lsl #32
    // 0xa76598: cmp             w1, NULL
    // 0xa7659c: b.eq            #0xa77194
    // 0xa765a0: r0 = of()
    //     0xa765a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa765a4: LoadField: r1 = r0->field_87
    //     0xa765a4: ldur            w1, [x0, #0x87]
    // 0xa765a8: DecompressPointer r1
    //     0xa765a8: add             x1, x1, HEAP, lsl #32
    // 0xa765ac: LoadField: r0 = r1->field_2b
    //     0xa765ac: ldur            w0, [x1, #0x2b]
    // 0xa765b0: DecompressPointer r0
    //     0xa765b0: add             x0, x0, HEAP, lsl #32
    // 0xa765b4: ldur            x2, [fp, #-8]
    // 0xa765b8: stur            x0, [fp, #-0x48]
    // 0xa765bc: LoadField: r1 = r2->field_f
    //     0xa765bc: ldur            w1, [x2, #0xf]
    // 0xa765c0: DecompressPointer r1
    //     0xa765c0: add             x1, x1, HEAP, lsl #32
    // 0xa765c4: cmp             w1, NULL
    // 0xa765c8: b.eq            #0xa77198
    // 0xa765cc: r0 = of()
    //     0xa765cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa765d0: LoadField: r1 = r0->field_5b
    //     0xa765d0: ldur            w1, [x0, #0x5b]
    // 0xa765d4: DecompressPointer r1
    //     0xa765d4: add             x1, x1, HEAP, lsl #32
    // 0xa765d8: r16 = 10.000000
    //     0xa765d8: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xa765dc: stp             x1, x16, [SP]
    // 0xa765e0: ldur            x1, [fp, #-0x48]
    // 0xa765e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa765e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa765e8: ldr             x4, [x4, #0xaa0]
    // 0xa765ec: r0 = copyWith()
    //     0xa765ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa765f0: stur            x0, [fp, #-0x48]
    // 0xa765f4: r0 = Text()
    //     0xa765f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa765f8: mov             x3, x0
    // 0xa765fc: r0 = " Brand Rating"
    //     0xa765fc: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d78] " Brand Rating"
    //     0xa76600: ldr             x0, [x0, #0xd78]
    // 0xa76604: stur            x3, [fp, #-0x50]
    // 0xa76608: StoreField: r3->field_b = r0
    //     0xa76608: stur            w0, [x3, #0xb]
    // 0xa7660c: ldur            x0, [fp, #-0x48]
    // 0xa76610: StoreField: r3->field_13 = r0
    //     0xa76610: stur            w0, [x3, #0x13]
    // 0xa76614: r1 = Null
    //     0xa76614: mov             x1, NULL
    // 0xa76618: r2 = 6
    //     0xa76618: movz            x2, #0x6
    // 0xa7661c: r0 = AllocateArray()
    //     0xa7661c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa76620: mov             x2, x0
    // 0xa76624: ldur            x0, [fp, #-0x40]
    // 0xa76628: stur            x2, [fp, #-0x48]
    // 0xa7662c: StoreField: r2->field_f = r0
    //     0xa7662c: stur            w0, [x2, #0xf]
    // 0xa76630: ldur            x0, [fp, #-0x58]
    // 0xa76634: StoreField: r2->field_13 = r0
    //     0xa76634: stur            w0, [x2, #0x13]
    // 0xa76638: ldur            x0, [fp, #-0x50]
    // 0xa7663c: ArrayStore: r2[0] = r0  ; List_4
    //     0xa7663c: stur            w0, [x2, #0x17]
    // 0xa76640: r1 = <Widget>
    //     0xa76640: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa76644: r0 = AllocateGrowableArray()
    //     0xa76644: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa76648: mov             x1, x0
    // 0xa7664c: ldur            x0, [fp, #-0x48]
    // 0xa76650: stur            x1, [fp, #-0x40]
    // 0xa76654: StoreField: r1->field_f = r0
    //     0xa76654: stur            w0, [x1, #0xf]
    // 0xa76658: r2 = 6
    //     0xa76658: movz            x2, #0x6
    // 0xa7665c: StoreField: r1->field_b = r2
    //     0xa7665c: stur            w2, [x1, #0xb]
    // 0xa76660: r0 = Row()
    //     0xa76660: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa76664: mov             x1, x0
    // 0xa76668: r0 = Instance_Axis
    //     0xa76668: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa7666c: stur            x1, [fp, #-0x48]
    // 0xa76670: StoreField: r1->field_f = r0
    //     0xa76670: stur            w0, [x1, #0xf]
    // 0xa76674: r0 = Instance_MainAxisAlignment
    //     0xa76674: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa76678: ldr             x0, [x0, #0xa08]
    // 0xa7667c: StoreField: r1->field_13 = r0
    //     0xa7667c: stur            w0, [x1, #0x13]
    // 0xa76680: r2 = Instance_MainAxisSize
    //     0xa76680: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa76684: ldr             x2, [x2, #0xa10]
    // 0xa76688: ArrayStore: r1[0] = r2  ; List_4
    //     0xa76688: stur            w2, [x1, #0x17]
    // 0xa7668c: r2 = Instance_CrossAxisAlignment
    //     0xa7668c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa76690: ldr             x2, [x2, #0xa18]
    // 0xa76694: StoreField: r1->field_1b = r2
    //     0xa76694: stur            w2, [x1, #0x1b]
    // 0xa76698: r2 = Instance_VerticalDirection
    //     0xa76698: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa7669c: ldr             x2, [x2, #0xa20]
    // 0xa766a0: StoreField: r1->field_23 = r2
    //     0xa766a0: stur            w2, [x1, #0x23]
    // 0xa766a4: r3 = Instance_Clip
    //     0xa766a4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa766a8: ldr             x3, [x3, #0x38]
    // 0xa766ac: StoreField: r1->field_2b = r3
    //     0xa766ac: stur            w3, [x1, #0x2b]
    // 0xa766b0: StoreField: r1->field_2f = rZR
    //     0xa766b0: stur            xzr, [x1, #0x2f]
    // 0xa766b4: ldur            x4, [fp, #-0x40]
    // 0xa766b8: StoreField: r1->field_b = r4
    //     0xa766b8: stur            w4, [x1, #0xb]
    // 0xa766bc: r0 = Visibility()
    //     0xa766bc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa766c0: mov             x1, x0
    // 0xa766c4: ldur            x0, [fp, #-0x48]
    // 0xa766c8: StoreField: r1->field_b = r0
    //     0xa766c8: stur            w0, [x1, #0xb]
    // 0xa766cc: r0 = Instance_SizedBox
    //     0xa766cc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa766d0: StoreField: r1->field_f = r0
    //     0xa766d0: stur            w0, [x1, #0xf]
    // 0xa766d4: ldur            x2, [fp, #-0x38]
    // 0xa766d8: StoreField: r1->field_13 = r2
    //     0xa766d8: stur            w2, [x1, #0x13]
    // 0xa766dc: r2 = false
    //     0xa766dc: add             x2, NULL, #0x30  ; false
    // 0xa766e0: ArrayStore: r1[0] = r2  ; List_4
    //     0xa766e0: stur            w2, [x1, #0x17]
    // 0xa766e4: StoreField: r1->field_1b = r2
    //     0xa766e4: stur            w2, [x1, #0x1b]
    // 0xa766e8: StoreField: r1->field_1f = r2
    //     0xa766e8: stur            w2, [x1, #0x1f]
    // 0xa766ec: StoreField: r1->field_23 = r2
    //     0xa766ec: stur            w2, [x1, #0x23]
    // 0xa766f0: StoreField: r1->field_27 = r2
    //     0xa766f0: stur            w2, [x1, #0x27]
    // 0xa766f4: StoreField: r1->field_2b = r2
    //     0xa766f4: stur            w2, [x1, #0x2b]
    // 0xa766f8: mov             x5, x1
    // 0xa766fc: ldur            x1, [fp, #-8]
    // 0xa76700: ldur            x3, [fp, #-0x28]
    // 0xa76704: ldur            x4, [fp, #-0x30]
    // 0xa76708: stur            x5, [fp, #-0x38]
    // 0xa7670c: r0 = Visibility()
    //     0xa7670c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa76710: mov             x3, x0
    // 0xa76714: ldur            x0, [fp, #-0x38]
    // 0xa76718: stur            x3, [fp, #-0x40]
    // 0xa7671c: StoreField: r3->field_b = r0
    //     0xa7671c: stur            w0, [x3, #0xb]
    // 0xa76720: r0 = Instance_SizedBox
    //     0xa76720: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa76724: StoreField: r3->field_f = r0
    //     0xa76724: stur            w0, [x3, #0xf]
    // 0xa76728: ldur            x1, [fp, #-0x30]
    // 0xa7672c: StoreField: r3->field_13 = r1
    //     0xa7672c: stur            w1, [x3, #0x13]
    // 0xa76730: r4 = false
    //     0xa76730: add             x4, NULL, #0x30  ; false
    // 0xa76734: ArrayStore: r3[0] = r4  ; List_4
    //     0xa76734: stur            w4, [x3, #0x17]
    // 0xa76738: StoreField: r3->field_1b = r4
    //     0xa76738: stur            w4, [x3, #0x1b]
    // 0xa7673c: StoreField: r3->field_1f = r4
    //     0xa7673c: stur            w4, [x3, #0x1f]
    // 0xa76740: StoreField: r3->field_23 = r4
    //     0xa76740: stur            w4, [x3, #0x23]
    // 0xa76744: StoreField: r3->field_27 = r4
    //     0xa76744: stur            w4, [x3, #0x27]
    // 0xa76748: StoreField: r3->field_2b = r4
    //     0xa76748: stur            w4, [x3, #0x2b]
    // 0xa7674c: ldur            x5, [fp, #-0x28]
    // 0xa76750: LoadField: r1 = r5->field_13
    //     0xa76750: ldur            w1, [x5, #0x13]
    // 0xa76754: DecompressPointer r1
    //     0xa76754: add             x1, x1, HEAP, lsl #32
    // 0xa76758: cmp             w1, NULL
    // 0xa7675c: b.eq            #0xa7719c
    // 0xa76760: LoadField: r6 = r1->field_f3
    //     0xa76760: ldur            w6, [x1, #0xf3]
    // 0xa76764: DecompressPointer r6
    //     0xa76764: add             x6, x6, HEAP, lsl #32
    // 0xa76768: stur            x6, [fp, #-0x30]
    // 0xa7676c: r1 = Null
    //     0xa7676c: mov             x1, NULL
    // 0xa76770: r2 = 4
    //     0xa76770: movz            x2, #0x4
    // 0xa76774: r0 = AllocateArray()
    //     0xa76774: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa76778: mov             x1, x0
    // 0xa7677c: ldur            x0, [fp, #-0x30]
    // 0xa76780: StoreField: r1->field_f = r0
    //     0xa76780: stur            w0, [x1, #0xf]
    // 0xa76784: r16 = "  "
    //     0xa76784: ldr             x16, [PP, #0xc58]  ; [pp+0xc58] "  "
    // 0xa76788: StoreField: r1->field_13 = r16
    //     0xa76788: stur            w16, [x1, #0x13]
    // 0xa7678c: str             x1, [SP]
    // 0xa76790: r0 = _interpolate()
    //     0xa76790: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa76794: mov             x2, x0
    // 0xa76798: ldur            x0, [fp, #-8]
    // 0xa7679c: stur            x2, [fp, #-0x30]
    // 0xa767a0: LoadField: r1 = r0->field_f
    //     0xa767a0: ldur            w1, [x0, #0xf]
    // 0xa767a4: DecompressPointer r1
    //     0xa767a4: add             x1, x1, HEAP, lsl #32
    // 0xa767a8: cmp             w1, NULL
    // 0xa767ac: b.eq            #0xa771a0
    // 0xa767b0: r0 = of()
    //     0xa767b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa767b4: LoadField: r1 = r0->field_87
    //     0xa767b4: ldur            w1, [x0, #0x87]
    // 0xa767b8: DecompressPointer r1
    //     0xa767b8: add             x1, x1, HEAP, lsl #32
    // 0xa767bc: LoadField: r0 = r1->field_7
    //     0xa767bc: ldur            w0, [x1, #7]
    // 0xa767c0: DecompressPointer r0
    //     0xa767c0: add             x0, x0, HEAP, lsl #32
    // 0xa767c4: r16 = 16.000000
    //     0xa767c4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa767c8: ldr             x16, [x16, #0x188]
    // 0xa767cc: r30 = Instance_Color
    //     0xa767cc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa767d0: stp             lr, x16, [SP]
    // 0xa767d4: mov             x1, x0
    // 0xa767d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa767d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa767dc: ldr             x4, [x4, #0xaa0]
    // 0xa767e0: r0 = copyWith()
    //     0xa767e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa767e4: stur            x0, [fp, #-0x38]
    // 0xa767e8: r0 = Text()
    //     0xa767e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa767ec: mov             x1, x0
    // 0xa767f0: ldur            x0, [fp, #-0x30]
    // 0xa767f4: stur            x1, [fp, #-0x48]
    // 0xa767f8: StoreField: r1->field_b = r0
    //     0xa767f8: stur            w0, [x1, #0xb]
    // 0xa767fc: ldur            x0, [fp, #-0x38]
    // 0xa76800: StoreField: r1->field_13 = r0
    //     0xa76800: stur            w0, [x1, #0x13]
    // 0xa76804: r0 = WidgetSpan()
    //     0xa76804: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xa76808: mov             x1, x0
    // 0xa7680c: ldur            x0, [fp, #-0x48]
    // 0xa76810: stur            x1, [fp, #-0x30]
    // 0xa76814: StoreField: r1->field_13 = r0
    //     0xa76814: stur            w0, [x1, #0x13]
    // 0xa76818: r0 = Instance_PlaceholderAlignment
    //     0xa76818: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] Obj!PlaceholderAlignment@d76401
    //     0xa7681c: ldr             x0, [x0, #0xa0]
    // 0xa76820: StoreField: r1->field_b = r0
    //     0xa76820: stur            w0, [x1, #0xb]
    // 0xa76824: ldur            x2, [fp, #-0x28]
    // 0xa76828: LoadField: r0 = r2->field_13
    //     0xa76828: ldur            w0, [x2, #0x13]
    // 0xa7682c: DecompressPointer r0
    //     0xa7682c: add             x0, x0, HEAP, lsl #32
    // 0xa76830: cmp             w0, NULL
    // 0xa76834: b.eq            #0xa771a4
    // 0xa76838: LoadField: r3 = r0->field_fb
    //     0xa76838: ldur            w3, [x0, #0xfb]
    // 0xa7683c: DecompressPointer r3
    //     0xa7683c: add             x3, x3, HEAP, lsl #32
    // 0xa76840: str             x3, [SP]
    // 0xa76844: r0 = _interpolateSingle()
    //     0xa76844: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa76848: mov             x2, x0
    // 0xa7684c: ldur            x0, [fp, #-8]
    // 0xa76850: stur            x2, [fp, #-0x38]
    // 0xa76854: LoadField: r1 = r0->field_f
    //     0xa76854: ldur            w1, [x0, #0xf]
    // 0xa76858: DecompressPointer r1
    //     0xa76858: add             x1, x1, HEAP, lsl #32
    // 0xa7685c: cmp             w1, NULL
    // 0xa76860: b.eq            #0xa771a8
    // 0xa76864: r0 = of()
    //     0xa76864: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa76868: LoadField: r1 = r0->field_87
    //     0xa76868: ldur            w1, [x0, #0x87]
    // 0xa7686c: DecompressPointer r1
    //     0xa7686c: add             x1, x1, HEAP, lsl #32
    // 0xa76870: LoadField: r0 = r1->field_2b
    //     0xa76870: ldur            w0, [x1, #0x2b]
    // 0xa76874: DecompressPointer r0
    //     0xa76874: add             x0, x0, HEAP, lsl #32
    // 0xa76878: ldur            x2, [fp, #-8]
    // 0xa7687c: stur            x0, [fp, #-0x48]
    // 0xa76880: LoadField: r1 = r2->field_f
    //     0xa76880: ldur            w1, [x2, #0xf]
    // 0xa76884: DecompressPointer r1
    //     0xa76884: add             x1, x1, HEAP, lsl #32
    // 0xa76888: cmp             w1, NULL
    // 0xa7688c: b.eq            #0xa771ac
    // 0xa76890: r0 = of()
    //     0xa76890: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa76894: LoadField: r1 = r0->field_5b
    //     0xa76894: ldur            w1, [x0, #0x5b]
    // 0xa76898: DecompressPointer r1
    //     0xa76898: add             x1, x1, HEAP, lsl #32
    // 0xa7689c: r0 = LoadClassIdInstr(r1)
    //     0xa7689c: ldur            x0, [x1, #-1]
    //     0xa768a0: ubfx            x0, x0, #0xc, #0x14
    // 0xa768a4: d0 = 0.400000
    //     0xa768a4: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xa768a8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa768a8: sub             lr, x0, #0xffa
    //     0xa768ac: ldr             lr, [x21, lr, lsl #3]
    //     0xa768b0: blr             lr
    // 0xa768b4: r16 = Instance_TextDecoration
    //     0xa768b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xa768b8: ldr             x16, [x16, #0xe30]
    // 0xa768bc: r30 = 12.000000
    //     0xa768bc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa768c0: ldr             lr, [lr, #0x9e8]
    // 0xa768c4: stp             lr, x16, [SP, #8]
    // 0xa768c8: str             x0, [SP]
    // 0xa768cc: ldur            x1, [fp, #-0x48]
    // 0xa768d0: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, decoration, 0x1, fontSize, 0x2, null]
    //     0xa768d0: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb60] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "decoration", 0x1, "fontSize", 0x2, Null]
    //     0xa768d4: ldr             x4, [x4, #0xb60]
    // 0xa768d8: r0 = copyWith()
    //     0xa768d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa768dc: stur            x0, [fp, #-0x48]
    // 0xa768e0: r0 = TextSpan()
    //     0xa768e0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa768e4: mov             x3, x0
    // 0xa768e8: ldur            x0, [fp, #-0x38]
    // 0xa768ec: stur            x3, [fp, #-0x50]
    // 0xa768f0: StoreField: r3->field_b = r0
    //     0xa768f0: stur            w0, [x3, #0xb]
    // 0xa768f4: r0 = Instance__DeferringMouseCursor
    //     0xa768f4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa768f8: ArrayStore: r3[0] = r0  ; List_4
    //     0xa768f8: stur            w0, [x3, #0x17]
    // 0xa768fc: ldur            x1, [fp, #-0x48]
    // 0xa76900: StoreField: r3->field_7 = r1
    //     0xa76900: stur            w1, [x3, #7]
    // 0xa76904: r1 = Null
    //     0xa76904: mov             x1, NULL
    // 0xa76908: r2 = 6
    //     0xa76908: movz            x2, #0x6
    // 0xa7690c: r0 = AllocateArray()
    //     0xa7690c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa76910: stur            x0, [fp, #-0x38]
    // 0xa76914: r16 = " | "
    //     0xa76914: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d80] " | "
    //     0xa76918: ldr             x16, [x16, #0xd80]
    // 0xa7691c: StoreField: r0->field_f = r16
    //     0xa7691c: stur            w16, [x0, #0xf]
    // 0xa76920: ldur            x2, [fp, #-0x28]
    // 0xa76924: LoadField: r1 = r2->field_13
    //     0xa76924: ldur            w1, [x2, #0x13]
    // 0xa76928: DecompressPointer r1
    //     0xa76928: add             x1, x1, HEAP, lsl #32
    // 0xa7692c: cmp             w1, NULL
    // 0xa76930: b.eq            #0xa771b0
    // 0xa76934: LoadField: r3 = r1->field_5f
    //     0xa76934: ldur            w3, [x1, #0x5f]
    // 0xa76938: DecompressPointer r3
    //     0xa76938: add             x3, x3, HEAP, lsl #32
    // 0xa7693c: stp             xzr, x3, [SP]
    // 0xa76940: r4 = 0
    //     0xa76940: movz            x4, #0
    // 0xa76944: ldr             x0, [SP, #8]
    // 0xa76948: r16 = UnlinkedCall_0x613b5c
    //     0xa76948: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d88] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa7694c: add             x16, x16, #0xd88
    // 0xa76950: ldp             x5, lr, [x16]
    // 0xa76954: blr             lr
    // 0xa76958: ldur            x1, [fp, #-0x38]
    // 0xa7695c: ArrayStore: r1[1] = r0  ; List_4
    //     0xa7695c: add             x25, x1, #0x13
    //     0xa76960: str             w0, [x25]
    //     0xa76964: tbz             w0, #0, #0xa76980
    //     0xa76968: ldurb           w16, [x1, #-1]
    //     0xa7696c: ldurb           w17, [x0, #-1]
    //     0xa76970: and             x16, x17, x16, lsr #2
    //     0xa76974: tst             x16, HEAP, lsr #32
    //     0xa76978: b.eq            #0xa76980
    //     0xa7697c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa76980: ldur            x0, [fp, #-0x38]
    // 0xa76984: r16 = "% OFF"
    //     0xa76984: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d98] "% OFF"
    //     0xa76988: ldr             x16, [x16, #0xd98]
    // 0xa7698c: ArrayStore: r0[0] = r16  ; List_4
    //     0xa7698c: stur            w16, [x0, #0x17]
    // 0xa76990: str             x0, [SP]
    // 0xa76994: r0 = _interpolate()
    //     0xa76994: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa76998: mov             x2, x0
    // 0xa7699c: ldur            x0, [fp, #-8]
    // 0xa769a0: stur            x2, [fp, #-0x38]
    // 0xa769a4: LoadField: r1 = r0->field_f
    //     0xa769a4: ldur            w1, [x0, #0xf]
    // 0xa769a8: DecompressPointer r1
    //     0xa769a8: add             x1, x1, HEAP, lsl #32
    // 0xa769ac: cmp             w1, NULL
    // 0xa769b0: b.eq            #0xa771b4
    // 0xa769b4: r0 = of()
    //     0xa769b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa769b8: LoadField: r1 = r0->field_87
    //     0xa769b8: ldur            w1, [x0, #0x87]
    // 0xa769bc: DecompressPointer r1
    //     0xa769bc: add             x1, x1, HEAP, lsl #32
    // 0xa769c0: LoadField: r0 = r1->field_2b
    //     0xa769c0: ldur            w0, [x1, #0x2b]
    // 0xa769c4: DecompressPointer r0
    //     0xa769c4: add             x0, x0, HEAP, lsl #32
    // 0xa769c8: r16 = Instance_Color
    //     0xa769c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa769cc: ldr             x16, [x16, #0x858]
    // 0xa769d0: r30 = 12.000000
    //     0xa769d0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa769d4: ldr             lr, [lr, #0x9e8]
    // 0xa769d8: stp             lr, x16, [SP]
    // 0xa769dc: mov             x1, x0
    // 0xa769e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa769e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa769e4: ldr             x4, [x4, #0x9b8]
    // 0xa769e8: r0 = copyWith()
    //     0xa769e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa769ec: stur            x0, [fp, #-0x48]
    // 0xa769f0: r0 = TextSpan()
    //     0xa769f0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa769f4: mov             x3, x0
    // 0xa769f8: ldur            x0, [fp, #-0x38]
    // 0xa769fc: stur            x3, [fp, #-0x58]
    // 0xa76a00: StoreField: r3->field_b = r0
    //     0xa76a00: stur            w0, [x3, #0xb]
    // 0xa76a04: r0 = Instance__DeferringMouseCursor
    //     0xa76a04: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa76a08: ArrayStore: r3[0] = r0  ; List_4
    //     0xa76a08: stur            w0, [x3, #0x17]
    // 0xa76a0c: ldur            x1, [fp, #-0x48]
    // 0xa76a10: StoreField: r3->field_7 = r1
    //     0xa76a10: stur            w1, [x3, #7]
    // 0xa76a14: r1 = Null
    //     0xa76a14: mov             x1, NULL
    // 0xa76a18: r2 = 6
    //     0xa76a18: movz            x2, #0x6
    // 0xa76a1c: r0 = AllocateArray()
    //     0xa76a1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa76a20: mov             x2, x0
    // 0xa76a24: ldur            x0, [fp, #-0x30]
    // 0xa76a28: stur            x2, [fp, #-0x38]
    // 0xa76a2c: StoreField: r2->field_f = r0
    //     0xa76a2c: stur            w0, [x2, #0xf]
    // 0xa76a30: ldur            x0, [fp, #-0x50]
    // 0xa76a34: StoreField: r2->field_13 = r0
    //     0xa76a34: stur            w0, [x2, #0x13]
    // 0xa76a38: ldur            x0, [fp, #-0x58]
    // 0xa76a3c: ArrayStore: r2[0] = r0  ; List_4
    //     0xa76a3c: stur            w0, [x2, #0x17]
    // 0xa76a40: r1 = <InlineSpan>
    //     0xa76a40: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xa76a44: ldr             x1, [x1, #0xe40]
    // 0xa76a48: r0 = AllocateGrowableArray()
    //     0xa76a48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa76a4c: mov             x1, x0
    // 0xa76a50: ldur            x0, [fp, #-0x38]
    // 0xa76a54: stur            x1, [fp, #-0x30]
    // 0xa76a58: StoreField: r1->field_f = r0
    //     0xa76a58: stur            w0, [x1, #0xf]
    // 0xa76a5c: r0 = 6
    //     0xa76a5c: movz            x0, #0x6
    // 0xa76a60: StoreField: r1->field_b = r0
    //     0xa76a60: stur            w0, [x1, #0xb]
    // 0xa76a64: r0 = TextSpan()
    //     0xa76a64: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa76a68: mov             x1, x0
    // 0xa76a6c: ldur            x0, [fp, #-0x30]
    // 0xa76a70: stur            x1, [fp, #-0x38]
    // 0xa76a74: StoreField: r1->field_f = r0
    //     0xa76a74: stur            w0, [x1, #0xf]
    // 0xa76a78: r0 = Instance__DeferringMouseCursor
    //     0xa76a78: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa76a7c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa76a7c: stur            w0, [x1, #0x17]
    // 0xa76a80: r0 = RichText()
    //     0xa76a80: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xa76a84: mov             x1, x0
    // 0xa76a88: ldur            x2, [fp, #-0x38]
    // 0xa76a8c: stur            x0, [fp, #-0x30]
    // 0xa76a90: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa76a90: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa76a94: r0 = RichText()
    //     0xa76a94: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xa76a98: r0 = SizedBox()
    //     0xa76a98: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa76a9c: mov             x1, x0
    // 0xa76aa0: r0 = 32.000000
    //     0xa76aa0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xa76aa4: ldr             x0, [x0, #0x848]
    // 0xa76aa8: stur            x1, [fp, #-0x38]
    // 0xa76aac: StoreField: r1->field_13 = r0
    //     0xa76aac: stur            w0, [x1, #0x13]
    // 0xa76ab0: ldur            x0, [fp, #-0x30]
    // 0xa76ab4: StoreField: r1->field_b = r0
    //     0xa76ab4: stur            w0, [x1, #0xb]
    // 0xa76ab8: r0 = Padding()
    //     0xa76ab8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa76abc: mov             x1, x0
    // 0xa76ac0: r0 = Instance_EdgeInsets
    //     0xa76ac0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xa76ac4: ldr             x0, [x0, #0x990]
    // 0xa76ac8: stur            x1, [fp, #-0x48]
    // 0xa76acc: StoreField: r1->field_f = r0
    //     0xa76acc: stur            w0, [x1, #0xf]
    // 0xa76ad0: ldur            x0, [fp, #-0x38]
    // 0xa76ad4: StoreField: r1->field_b = r0
    //     0xa76ad4: stur            w0, [x1, #0xb]
    // 0xa76ad8: ldur            x0, [fp, #-8]
    // 0xa76adc: LoadField: r2 = r0->field_b
    //     0xa76adc: ldur            w2, [x0, #0xb]
    // 0xa76ae0: DecompressPointer r2
    //     0xa76ae0: add             x2, x2, HEAP, lsl #32
    // 0xa76ae4: cmp             w2, NULL
    // 0xa76ae8: b.eq            #0xa771b8
    // 0xa76aec: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa76aec: ldur            w3, [x2, #0x17]
    // 0xa76af0: DecompressPointer r3
    //     0xa76af0: add             x3, x3, HEAP, lsl #32
    // 0xa76af4: LoadField: r2 = r3->field_1f
    //     0xa76af4: ldur            w2, [x3, #0x1f]
    // 0xa76af8: DecompressPointer r2
    //     0xa76af8: add             x2, x2, HEAP, lsl #32
    // 0xa76afc: cmp             w2, NULL
    // 0xa76b00: b.ne            #0xa76b0c
    // 0xa76b04: r2 = Null
    //     0xa76b04: mov             x2, NULL
    // 0xa76b08: b               #0xa76b18
    // 0xa76b0c: LoadField: r4 = r2->field_7
    //     0xa76b0c: ldur            w4, [x2, #7]
    // 0xa76b10: DecompressPointer r4
    //     0xa76b10: add             x4, x4, HEAP, lsl #32
    // 0xa76b14: mov             x2, x4
    // 0xa76b18: cmp             w2, NULL
    // 0xa76b1c: b.ne            #0xa76b24
    // 0xa76b20: r2 = false
    //     0xa76b20: add             x2, NULL, #0x30  ; false
    // 0xa76b24: stur            x2, [fp, #-0x30]
    // 0xa76b28: LoadField: r4 = r3->field_3f
    //     0xa76b28: ldur            w4, [x3, #0x3f]
    // 0xa76b2c: DecompressPointer r4
    //     0xa76b2c: add             x4, x4, HEAP, lsl #32
    // 0xa76b30: cmp             w4, NULL
    // 0xa76b34: b.ne            #0xa76b40
    // 0xa76b38: r3 = Null
    //     0xa76b38: mov             x3, NULL
    // 0xa76b3c: b               #0xa76b48
    // 0xa76b40: LoadField: r3 = r4->field_23
    //     0xa76b40: ldur            w3, [x4, #0x23]
    // 0xa76b44: DecompressPointer r3
    //     0xa76b44: add             x3, x3, HEAP, lsl #32
    // 0xa76b48: cmp             w3, NULL
    // 0xa76b4c: b.eq            #0xa76f78
    // 0xa76b50: tbnz            w3, #4, #0xa76f78
    // 0xa76b54: ldur            x3, [fp, #-0x28]
    // 0xa76b58: r16 = <Size?>
    //     0xa76b58: add             x16, PP, #0x27, lsl #12  ; [pp+0x27768] TypeArguments: <Size?>
    //     0xa76b5c: ldr             x16, [x16, #0x768]
    // 0xa76b60: r30 = Instance_Size
    //     0xa76b60: add             lr, PP, #0x52, lsl #12  ; [pp+0x52da0] Obj!Size@d6c221
    //     0xa76b64: ldr             lr, [lr, #0xda0]
    // 0xa76b68: stp             lr, x16, [SP]
    // 0xa76b6c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa76b6c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa76b70: r0 = all()
    //     0xa76b70: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa76b74: ldur            x2, [fp, #-0x28]
    // 0xa76b78: stur            x0, [fp, #-0x38]
    // 0xa76b7c: LoadField: r1 = r2->field_13
    //     0xa76b7c: ldur            w1, [x2, #0x13]
    // 0xa76b80: DecompressPointer r1
    //     0xa76b80: add             x1, x1, HEAP, lsl #32
    // 0xa76b84: cmp             w1, NULL
    // 0xa76b88: b.eq            #0xa771bc
    // 0xa76b8c: r17 = 275
    //     0xa76b8c: movz            x17, #0x113
    // 0xa76b90: ldr             w3, [x1, x17]
    // 0xa76b94: DecompressPointer r3
    //     0xa76b94: add             x3, x3, HEAP, lsl #32
    // 0xa76b98: cmp             w3, NULL
    // 0xa76b9c: b.eq            #0xa76ba4
    // 0xa76ba0: tbnz            w3, #4, #0xa76bf4
    // 0xa76ba4: ldur            x3, [fp, #-8]
    // 0xa76ba8: LoadField: r1 = r3->field_f
    //     0xa76ba8: ldur            w1, [x3, #0xf]
    // 0xa76bac: DecompressPointer r1
    //     0xa76bac: add             x1, x1, HEAP, lsl #32
    // 0xa76bb0: cmp             w1, NULL
    // 0xa76bb4: b.eq            #0xa771c0
    // 0xa76bb8: r0 = of()
    //     0xa76bb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa76bbc: LoadField: r1 = r0->field_5b
    //     0xa76bbc: ldur            w1, [x0, #0x5b]
    // 0xa76bc0: DecompressPointer r1
    //     0xa76bc0: add             x1, x1, HEAP, lsl #32
    // 0xa76bc4: r0 = LoadClassIdInstr(r1)
    //     0xa76bc4: ldur            x0, [x1, #-1]
    //     0xa76bc8: ubfx            x0, x0, #0xc, #0x14
    // 0xa76bcc: d0 = 0.400000
    //     0xa76bcc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xa76bd0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa76bd0: sub             lr, x0, #0xffa
    //     0xa76bd4: ldr             lr, [x21, lr, lsl #3]
    //     0xa76bd8: blr             lr
    // 0xa76bdc: r16 = <Color>
    //     0xa76bdc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa76be0: ldr             x16, [x16, #0xf80]
    // 0xa76be4: stp             x0, x16, [SP]
    // 0xa76be8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa76be8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa76bec: r0 = all()
    //     0xa76bec: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa76bf0: b               #0xa76c0c
    // 0xa76bf4: r16 = <Color>
    //     0xa76bf4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa76bf8: ldr             x16, [x16, #0xf80]
    // 0xa76bfc: r30 = Instance_Color
    //     0xa76bfc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa76c00: stp             lr, x16, [SP]
    // 0xa76c04: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa76c04: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa76c08: r0 = all()
    //     0xa76c08: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa76c0c: ldur            x2, [fp, #-0x28]
    // 0xa76c10: stur            x0, [fp, #-0x50]
    // 0xa76c14: LoadField: r1 = r2->field_13
    //     0xa76c14: ldur            w1, [x2, #0x13]
    // 0xa76c18: DecompressPointer r1
    //     0xa76c18: add             x1, x1, HEAP, lsl #32
    // 0xa76c1c: cmp             w1, NULL
    // 0xa76c20: b.eq            #0xa771c4
    // 0xa76c24: r17 = 275
    //     0xa76c24: movz            x17, #0x113
    // 0xa76c28: ldr             w3, [x1, x17]
    // 0xa76c2c: DecompressPointer r3
    //     0xa76c2c: add             x3, x3, HEAP, lsl #32
    // 0xa76c30: cmp             w3, NULL
    // 0xa76c34: b.eq            #0xa76c3c
    // 0xa76c38: tbnz            w3, #4, #0xa76c90
    // 0xa76c3c: ldur            x3, [fp, #-8]
    // 0xa76c40: LoadField: r1 = r3->field_f
    //     0xa76c40: ldur            w1, [x3, #0xf]
    // 0xa76c44: DecompressPointer r1
    //     0xa76c44: add             x1, x1, HEAP, lsl #32
    // 0xa76c48: cmp             w1, NULL
    // 0xa76c4c: b.eq            #0xa771c8
    // 0xa76c50: r0 = of()
    //     0xa76c50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa76c54: LoadField: r1 = r0->field_5b
    //     0xa76c54: ldur            w1, [x0, #0x5b]
    // 0xa76c58: DecompressPointer r1
    //     0xa76c58: add             x1, x1, HEAP, lsl #32
    // 0xa76c5c: r0 = LoadClassIdInstr(r1)
    //     0xa76c5c: ldur            x0, [x1, #-1]
    //     0xa76c60: ubfx            x0, x0, #0xc, #0x14
    // 0xa76c64: d0 = 0.100000
    //     0xa76c64: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xa76c68: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa76c68: sub             lr, x0, #0xffa
    //     0xa76c6c: ldr             lr, [x21, lr, lsl #3]
    //     0xa76c70: blr             lr
    // 0xa76c74: r16 = <Color>
    //     0xa76c74: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa76c78: ldr             x16, [x16, #0xf80]
    // 0xa76c7c: stp             x0, x16, [SP]
    // 0xa76c80: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa76c80: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa76c84: r0 = all()
    //     0xa76c84: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa76c88: mov             x3, x0
    // 0xa76c8c: b               #0xa76cc8
    // 0xa76c90: ldur            x0, [fp, #-8]
    // 0xa76c94: LoadField: r1 = r0->field_f
    //     0xa76c94: ldur            w1, [x0, #0xf]
    // 0xa76c98: DecompressPointer r1
    //     0xa76c98: add             x1, x1, HEAP, lsl #32
    // 0xa76c9c: cmp             w1, NULL
    // 0xa76ca0: b.eq            #0xa771cc
    // 0xa76ca4: r0 = of()
    //     0xa76ca4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa76ca8: LoadField: r1 = r0->field_5b
    //     0xa76ca8: ldur            w1, [x0, #0x5b]
    // 0xa76cac: DecompressPointer r1
    //     0xa76cac: add             x1, x1, HEAP, lsl #32
    // 0xa76cb0: r16 = <Color>
    //     0xa76cb0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa76cb4: ldr             x16, [x16, #0xf80]
    // 0xa76cb8: stp             x1, x16, [SP]
    // 0xa76cbc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa76cbc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa76cc0: r0 = all()
    //     0xa76cc0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa76cc4: mov             x3, x0
    // 0xa76cc8: ldur            x2, [fp, #-0x28]
    // 0xa76ccc: ldur            x1, [fp, #-0x38]
    // 0xa76cd0: ldur            x0, [fp, #-0x50]
    // 0xa76cd4: stur            x3, [fp, #-0x58]
    // 0xa76cd8: r16 = <OutlinedBorder?>
    //     0xa76cd8: add             x16, PP, #0x52, lsl #12  ; [pp+0x52da8] TypeArguments: <OutlinedBorder?>
    //     0xa76cdc: ldr             x16, [x16, #0xda8]
    // 0xa76ce0: r30 = Instance_RoundedRectangleBorder
    //     0xa76ce0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xa76ce4: ldr             lr, [lr, #0xd68]
    // 0xa76ce8: stp             lr, x16, [SP]
    // 0xa76cec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa76cec: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa76cf0: r0 = all()
    //     0xa76cf0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa76cf4: stur            x0, [fp, #-0x60]
    // 0xa76cf8: r0 = ButtonStyle()
    //     0xa76cf8: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xa76cfc: mov             x1, x0
    // 0xa76d00: ldur            x0, [fp, #-0x58]
    // 0xa76d04: stur            x1, [fp, #-0x68]
    // 0xa76d08: StoreField: r1->field_b = r0
    //     0xa76d08: stur            w0, [x1, #0xb]
    // 0xa76d0c: ldur            x0, [fp, #-0x50]
    // 0xa76d10: StoreField: r1->field_f = r0
    //     0xa76d10: stur            w0, [x1, #0xf]
    // 0xa76d14: ldur            x0, [fp, #-0x38]
    // 0xa76d18: StoreField: r1->field_27 = r0
    //     0xa76d18: stur            w0, [x1, #0x27]
    // 0xa76d1c: ldur            x0, [fp, #-0x60]
    // 0xa76d20: StoreField: r1->field_43 = r0
    //     0xa76d20: stur            w0, [x1, #0x43]
    // 0xa76d24: r0 = TextButtonThemeData()
    //     0xa76d24: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xa76d28: mov             x1, x0
    // 0xa76d2c: ldur            x0, [fp, #-0x68]
    // 0xa76d30: stur            x1, [fp, #-0x38]
    // 0xa76d34: StoreField: r1->field_7 = r0
    //     0xa76d34: stur            w0, [x1, #7]
    // 0xa76d38: ldur            x2, [fp, #-0x28]
    // 0xa76d3c: LoadField: r0 = r2->field_13
    //     0xa76d3c: ldur            w0, [x2, #0x13]
    // 0xa76d40: DecompressPointer r0
    //     0xa76d40: add             x0, x0, HEAP, lsl #32
    // 0xa76d44: cmp             w0, NULL
    // 0xa76d48: b.eq            #0xa771d0
    // 0xa76d4c: r17 = 275
    //     0xa76d4c: movz            x17, #0x113
    // 0xa76d50: ldr             w3, [x0, x17]
    // 0xa76d54: DecompressPointer r3
    //     0xa76d54: add             x3, x3, HEAP, lsl #32
    // 0xa76d58: cmp             w3, NULL
    // 0xa76d5c: b.eq            #0xa76d64
    // 0xa76d60: tbnz            w3, #4, #0xa76e58
    // 0xa76d64: LoadField: r3 = r0->field_f
    //     0xa76d64: ldur            w3, [x0, #0xf]
    // 0xa76d68: DecompressPointer r3
    //     0xa76d68: add             x3, x3, HEAP, lsl #32
    // 0xa76d6c: cmp             w3, NULL
    // 0xa76d70: b.ne            #0xa76d7c
    // 0xa76d74: r0 = Null
    //     0xa76d74: mov             x0, NULL
    // 0xa76d78: b               #0xa76d98
    // 0xa76d7c: str             x3, [SP]
    // 0xa76d80: r4 = 0
    //     0xa76d80: movz            x4, #0
    // 0xa76d84: ldr             x0, [SP]
    // 0xa76d88: r16 = UnlinkedCall_0x613b5c
    //     0xa76d88: add             x16, PP, #0x52, lsl #12  ; [pp+0x52db0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa76d8c: add             x16, x16, #0xdb0
    // 0xa76d90: ldp             x5, lr, [x16]
    // 0xa76d94: blr             lr
    // 0xa76d98: cmp             w0, NULL
    // 0xa76d9c: b.ne            #0xa76da8
    // 0xa76da0: r2 = ""
    //     0xa76da0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa76da4: b               #0xa76dac
    // 0xa76da8: mov             x2, x0
    // 0xa76dac: ldur            x0, [fp, #-8]
    // 0xa76db0: stur            x2, [fp, #-0x50]
    // 0xa76db4: LoadField: r1 = r0->field_f
    //     0xa76db4: ldur            w1, [x0, #0xf]
    // 0xa76db8: DecompressPointer r1
    //     0xa76db8: add             x1, x1, HEAP, lsl #32
    // 0xa76dbc: cmp             w1, NULL
    // 0xa76dc0: b.eq            #0xa771d4
    // 0xa76dc4: r0 = of()
    //     0xa76dc4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa76dc8: LoadField: r1 = r0->field_87
    //     0xa76dc8: ldur            w1, [x0, #0x87]
    // 0xa76dcc: DecompressPointer r1
    //     0xa76dcc: add             x1, x1, HEAP, lsl #32
    // 0xa76dd0: LoadField: r0 = r1->field_7
    //     0xa76dd0: ldur            w0, [x1, #7]
    // 0xa76dd4: DecompressPointer r0
    //     0xa76dd4: add             x0, x0, HEAP, lsl #32
    // 0xa76dd8: ldur            x1, [fp, #-8]
    // 0xa76ddc: stur            x0, [fp, #-0x58]
    // 0xa76de0: LoadField: r2 = r1->field_f
    //     0xa76de0: ldur            w2, [x1, #0xf]
    // 0xa76de4: DecompressPointer r2
    //     0xa76de4: add             x2, x2, HEAP, lsl #32
    // 0xa76de8: cmp             w2, NULL
    // 0xa76dec: b.eq            #0xa771d8
    // 0xa76df0: mov             x1, x2
    // 0xa76df4: r0 = of()
    //     0xa76df4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa76df8: LoadField: r1 = r0->field_5b
    //     0xa76df8: ldur            w1, [x0, #0x5b]
    // 0xa76dfc: DecompressPointer r1
    //     0xa76dfc: add             x1, x1, HEAP, lsl #32
    // 0xa76e00: r0 = LoadClassIdInstr(r1)
    //     0xa76e00: ldur            x0, [x1, #-1]
    //     0xa76e04: ubfx            x0, x0, #0xc, #0x14
    // 0xa76e08: d0 = 0.400000
    //     0xa76e08: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xa76e0c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa76e0c: sub             lr, x0, #0xffa
    //     0xa76e10: ldr             lr, [x21, lr, lsl #3]
    //     0xa76e14: blr             lr
    // 0xa76e18: r16 = 14.000000
    //     0xa76e18: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa76e1c: ldr             x16, [x16, #0x1d8]
    // 0xa76e20: stp             x0, x16, [SP]
    // 0xa76e24: ldur            x1, [fp, #-0x58]
    // 0xa76e28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa76e28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa76e2c: ldr             x4, [x4, #0xaa0]
    // 0xa76e30: r0 = copyWith()
    //     0xa76e30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa76e34: stur            x0, [fp, #-0x58]
    // 0xa76e38: r0 = Text()
    //     0xa76e38: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa76e3c: mov             x1, x0
    // 0xa76e40: ldur            x0, [fp, #-0x50]
    // 0xa76e44: StoreField: r1->field_b = r0
    //     0xa76e44: stur            w0, [x1, #0xb]
    // 0xa76e48: ldur            x0, [fp, #-0x58]
    // 0xa76e4c: StoreField: r1->field_13 = r0
    //     0xa76e4c: stur            w0, [x1, #0x13]
    // 0xa76e50: mov             x3, x1
    // 0xa76e54: b               #0xa76f10
    // 0xa76e58: ldur            x1, [fp, #-8]
    // 0xa76e5c: LoadField: r2 = r0->field_f
    //     0xa76e5c: ldur            w2, [x0, #0xf]
    // 0xa76e60: DecompressPointer r2
    //     0xa76e60: add             x2, x2, HEAP, lsl #32
    // 0xa76e64: cmp             w2, NULL
    // 0xa76e68: b.ne            #0xa76e74
    // 0xa76e6c: r0 = Null
    //     0xa76e6c: mov             x0, NULL
    // 0xa76e70: b               #0xa76e90
    // 0xa76e74: str             x2, [SP]
    // 0xa76e78: r4 = 0
    //     0xa76e78: movz            x4, #0
    // 0xa76e7c: ldr             x0, [SP]
    // 0xa76e80: r16 = UnlinkedCall_0x613b5c
    //     0xa76e80: add             x16, PP, #0x52, lsl #12  ; [pp+0x52dc0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa76e84: add             x16, x16, #0xdc0
    // 0xa76e88: ldp             x5, lr, [x16]
    // 0xa76e8c: blr             lr
    // 0xa76e90: cmp             w0, NULL
    // 0xa76e94: b.ne            #0xa76ea0
    // 0xa76e98: r2 = ""
    //     0xa76e98: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa76e9c: b               #0xa76ea4
    // 0xa76ea0: mov             x2, x0
    // 0xa76ea4: ldur            x0, [fp, #-8]
    // 0xa76ea8: stur            x2, [fp, #-0x50]
    // 0xa76eac: LoadField: r1 = r0->field_f
    //     0xa76eac: ldur            w1, [x0, #0xf]
    // 0xa76eb0: DecompressPointer r1
    //     0xa76eb0: add             x1, x1, HEAP, lsl #32
    // 0xa76eb4: cmp             w1, NULL
    // 0xa76eb8: b.eq            #0xa771dc
    // 0xa76ebc: r0 = of()
    //     0xa76ebc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa76ec0: LoadField: r1 = r0->field_87
    //     0xa76ec0: ldur            w1, [x0, #0x87]
    // 0xa76ec4: DecompressPointer r1
    //     0xa76ec4: add             x1, x1, HEAP, lsl #32
    // 0xa76ec8: LoadField: r0 = r1->field_7
    //     0xa76ec8: ldur            w0, [x1, #7]
    // 0xa76ecc: DecompressPointer r0
    //     0xa76ecc: add             x0, x0, HEAP, lsl #32
    // 0xa76ed0: r16 = 14.000000
    //     0xa76ed0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa76ed4: ldr             x16, [x16, #0x1d8]
    // 0xa76ed8: r30 = Instance_Color
    //     0xa76ed8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa76edc: stp             lr, x16, [SP]
    // 0xa76ee0: mov             x1, x0
    // 0xa76ee4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa76ee4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa76ee8: ldr             x4, [x4, #0xaa0]
    // 0xa76eec: r0 = copyWith()
    //     0xa76eec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa76ef0: stur            x0, [fp, #-8]
    // 0xa76ef4: r0 = Text()
    //     0xa76ef4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa76ef8: mov             x1, x0
    // 0xa76efc: ldur            x0, [fp, #-0x50]
    // 0xa76f00: StoreField: r1->field_b = r0
    //     0xa76f00: stur            w0, [x1, #0xb]
    // 0xa76f04: ldur            x0, [fp, #-8]
    // 0xa76f08: StoreField: r1->field_13 = r0
    //     0xa76f08: stur            w0, [x1, #0x13]
    // 0xa76f0c: mov             x3, x1
    // 0xa76f10: ldur            x0, [fp, #-0x38]
    // 0xa76f14: ldur            x2, [fp, #-0x28]
    // 0xa76f18: stur            x3, [fp, #-8]
    // 0xa76f1c: r1 = Function '<anonymous closure>':.
    //     0xa76f1c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52dd0] AnonymousClosure: (0xa771e0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::lineThemeSlider (0xa75c38)
    //     0xa76f20: ldr             x1, [x1, #0xdd0]
    // 0xa76f24: r0 = AllocateClosure()
    //     0xa76f24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa76f28: stur            x0, [fp, #-0x28]
    // 0xa76f2c: r0 = TextButton()
    //     0xa76f2c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa76f30: mov             x1, x0
    // 0xa76f34: ldur            x0, [fp, #-0x28]
    // 0xa76f38: stur            x1, [fp, #-0x50]
    // 0xa76f3c: StoreField: r1->field_b = r0
    //     0xa76f3c: stur            w0, [x1, #0xb]
    // 0xa76f40: r0 = false
    //     0xa76f40: add             x0, NULL, #0x30  ; false
    // 0xa76f44: StoreField: r1->field_27 = r0
    //     0xa76f44: stur            w0, [x1, #0x27]
    // 0xa76f48: r2 = true
    //     0xa76f48: add             x2, NULL, #0x20  ; true
    // 0xa76f4c: StoreField: r1->field_2f = r2
    //     0xa76f4c: stur            w2, [x1, #0x2f]
    // 0xa76f50: ldur            x2, [fp, #-8]
    // 0xa76f54: StoreField: r1->field_37 = r2
    //     0xa76f54: stur            w2, [x1, #0x37]
    // 0xa76f58: r0 = TextButtonTheme()
    //     0xa76f58: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa76f5c: mov             x1, x0
    // 0xa76f60: ldur            x0, [fp, #-0x38]
    // 0xa76f64: StoreField: r1->field_f = r0
    //     0xa76f64: stur            w0, [x1, #0xf]
    // 0xa76f68: ldur            x0, [fp, #-0x50]
    // 0xa76f6c: StoreField: r1->field_b = r0
    //     0xa76f6c: stur            w0, [x1, #0xb]
    // 0xa76f70: mov             x5, x1
    // 0xa76f74: b               #0xa76f7c
    // 0xa76f78: r5 = Instance_SizedBox
    //     0xa76f78: ldr             x5, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa76f7c: ldur            x4, [fp, #-0x20]
    // 0xa76f80: ldur            x3, [fp, #-0x10]
    // 0xa76f84: ldur            x2, [fp, #-0x40]
    // 0xa76f88: ldur            x0, [fp, #-0x48]
    // 0xa76f8c: ldur            x1, [fp, #-0x30]
    // 0xa76f90: stur            x5, [fp, #-8]
    // 0xa76f94: r0 = Container()
    //     0xa76f94: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa76f98: stur            x0, [fp, #-0x28]
    // 0xa76f9c: r16 = inf
    //     0xa76f9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xa76fa0: ldr             x16, [x16, #0x9f8]
    // 0xa76fa4: r30 = Instance_Alignment
    //     0xa76fa4: add             lr, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xa76fa8: ldr             lr, [lr, #0xcb0]
    // 0xa76fac: stp             lr, x16, [SP, #8]
    // 0xa76fb0: ldur            x16, [fp, #-8]
    // 0xa76fb4: str             x16, [SP]
    // 0xa76fb8: mov             x1, x0
    // 0xa76fbc: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x2, child, 0x3, width, 0x1, null]
    //     0xa76fbc: add             x4, PP, #0x52, lsl #12  ; [pp+0x52dd8] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x2, "child", 0x3, "width", 0x1, Null]
    //     0xa76fc0: ldr             x4, [x4, #0xdd8]
    // 0xa76fc4: r0 = Container()
    //     0xa76fc4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa76fc8: r0 = Visibility()
    //     0xa76fc8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa76fcc: mov             x3, x0
    // 0xa76fd0: ldur            x0, [fp, #-0x28]
    // 0xa76fd4: stur            x3, [fp, #-8]
    // 0xa76fd8: StoreField: r3->field_b = r0
    //     0xa76fd8: stur            w0, [x3, #0xb]
    // 0xa76fdc: r0 = Instance_SizedBox
    //     0xa76fdc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa76fe0: StoreField: r3->field_f = r0
    //     0xa76fe0: stur            w0, [x3, #0xf]
    // 0xa76fe4: ldur            x0, [fp, #-0x30]
    // 0xa76fe8: StoreField: r3->field_13 = r0
    //     0xa76fe8: stur            w0, [x3, #0x13]
    // 0xa76fec: r0 = false
    //     0xa76fec: add             x0, NULL, #0x30  ; false
    // 0xa76ff0: ArrayStore: r3[0] = r0  ; List_4
    //     0xa76ff0: stur            w0, [x3, #0x17]
    // 0xa76ff4: StoreField: r3->field_1b = r0
    //     0xa76ff4: stur            w0, [x3, #0x1b]
    // 0xa76ff8: StoreField: r3->field_1f = r0
    //     0xa76ff8: stur            w0, [x3, #0x1f]
    // 0xa76ffc: StoreField: r3->field_23 = r0
    //     0xa76ffc: stur            w0, [x3, #0x23]
    // 0xa77000: StoreField: r3->field_27 = r0
    //     0xa77000: stur            w0, [x3, #0x27]
    // 0xa77004: StoreField: r3->field_2b = r0
    //     0xa77004: stur            w0, [x3, #0x2b]
    // 0xa77008: r1 = Null
    //     0xa77008: mov             x1, NULL
    // 0xa7700c: r2 = 10
    //     0xa7700c: movz            x2, #0xa
    // 0xa77010: r0 = AllocateArray()
    //     0xa77010: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa77014: mov             x2, x0
    // 0xa77018: ldur            x0, [fp, #-0x20]
    // 0xa7701c: stur            x2, [fp, #-0x28]
    // 0xa77020: StoreField: r2->field_f = r0
    //     0xa77020: stur            w0, [x2, #0xf]
    // 0xa77024: ldur            x0, [fp, #-0x10]
    // 0xa77028: StoreField: r2->field_13 = r0
    //     0xa77028: stur            w0, [x2, #0x13]
    // 0xa7702c: ldur            x0, [fp, #-0x40]
    // 0xa77030: ArrayStore: r2[0] = r0  ; List_4
    //     0xa77030: stur            w0, [x2, #0x17]
    // 0xa77034: ldur            x0, [fp, #-0x48]
    // 0xa77038: StoreField: r2->field_1b = r0
    //     0xa77038: stur            w0, [x2, #0x1b]
    // 0xa7703c: ldur            x0, [fp, #-8]
    // 0xa77040: StoreField: r2->field_1f = r0
    //     0xa77040: stur            w0, [x2, #0x1f]
    // 0xa77044: r1 = <Widget>
    //     0xa77044: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa77048: r0 = AllocateGrowableArray()
    //     0xa77048: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa7704c: mov             x1, x0
    // 0xa77050: ldur            x0, [fp, #-0x28]
    // 0xa77054: stur            x1, [fp, #-8]
    // 0xa77058: StoreField: r1->field_f = r0
    //     0xa77058: stur            w0, [x1, #0xf]
    // 0xa7705c: r0 = 10
    //     0xa7705c: movz            x0, #0xa
    // 0xa77060: StoreField: r1->field_b = r0
    //     0xa77060: stur            w0, [x1, #0xb]
    // 0xa77064: r0 = Column()
    //     0xa77064: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa77068: mov             x1, x0
    // 0xa7706c: r0 = Instance_Axis
    //     0xa7706c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa77070: stur            x1, [fp, #-0x10]
    // 0xa77074: StoreField: r1->field_f = r0
    //     0xa77074: stur            w0, [x1, #0xf]
    // 0xa77078: r0 = Instance_MainAxisAlignment
    //     0xa77078: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa7707c: ldr             x0, [x0, #0xa08]
    // 0xa77080: StoreField: r1->field_13 = r0
    //     0xa77080: stur            w0, [x1, #0x13]
    // 0xa77084: r0 = Instance_MainAxisSize
    //     0xa77084: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xa77088: ldr             x0, [x0, #0xdd0]
    // 0xa7708c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa7708c: stur            w0, [x1, #0x17]
    // 0xa77090: r0 = Instance_CrossAxisAlignment
    //     0xa77090: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa77094: ldr             x0, [x0, #0x890]
    // 0xa77098: StoreField: r1->field_1b = r0
    //     0xa77098: stur            w0, [x1, #0x1b]
    // 0xa7709c: r0 = Instance_VerticalDirection
    //     0xa7709c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa770a0: ldr             x0, [x0, #0xa20]
    // 0xa770a4: StoreField: r1->field_23 = r0
    //     0xa770a4: stur            w0, [x1, #0x23]
    // 0xa770a8: r0 = Instance_Clip
    //     0xa770a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa770ac: ldr             x0, [x0, #0x38]
    // 0xa770b0: StoreField: r1->field_2b = r0
    //     0xa770b0: stur            w0, [x1, #0x2b]
    // 0xa770b4: StoreField: r1->field_2f = rZR
    //     0xa770b4: stur            xzr, [x1, #0x2f]
    // 0xa770b8: ldur            x0, [fp, #-8]
    // 0xa770bc: StoreField: r1->field_b = r0
    //     0xa770bc: stur            w0, [x1, #0xb]
    // 0xa770c0: r0 = SizedBox()
    //     0xa770c0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa770c4: mov             x1, x0
    // 0xa770c8: r0 = inf
    //     0xa770c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xa770cc: ldr             x0, [x0, #0x9f8]
    // 0xa770d0: stur            x1, [fp, #-8]
    // 0xa770d4: StoreField: r1->field_13 = r0
    //     0xa770d4: stur            w0, [x1, #0x13]
    // 0xa770d8: ldur            x0, [fp, #-0x10]
    // 0xa770dc: StoreField: r1->field_b = r0
    //     0xa770dc: stur            w0, [x1, #0xb]
    // 0xa770e0: r0 = AnimatedContainer()
    //     0xa770e0: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xa770e4: stur            x0, [fp, #-0x10]
    // 0xa770e8: r16 = Instance_Cubic
    //     0xa770e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xa770ec: ldr             x16, [x16, #0xaf8]
    // 0xa770f0: str             x16, [SP]
    // 0xa770f4: mov             x1, x0
    // 0xa770f8: ldur            x2, [fp, #-8]
    // 0xa770fc: r3 = Instance_Duration
    //     0xa770fc: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xa77100: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xa77100: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xa77104: ldr             x4, [x4, #0xbc8]
    // 0xa77108: r0 = AnimatedContainer()
    //     0xa77108: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xa7710c: ldur            x0, [fp, #-0x10]
    // 0xa77110: LeaveFrame
    //     0xa77110: mov             SP, fp
    //     0xa77114: ldp             fp, lr, [SP], #0x10
    // 0xa77118: ret
    //     0xa77118: ret             
    // 0xa7711c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7711c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa77120: b               #0xa75c5c
    // 0xa77124: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa77124: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa77128: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa77128: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa7712c: r9 = _imagePageController
    //     0xa7712c: add             x9, PP, #0x52, lsl #12  ; [pp+0x52de8] Field <_GroupCarouselItemViewState@1732154658._imagePageController@1732154658>: late (offset: 0x18)
    //     0xa77130: ldr             x9, [x9, #0xde8]
    // 0xa77134: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa77134: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa77138: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa77138: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa7713c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa7713c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa77140: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa77140: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa77144: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa77144: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa77148: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa77148: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa7714c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa7714c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa77150: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa77150: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa77154: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa77154: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa77158: SaveReg d0
    //     0xa77158: str             q0, [SP, #-0x10]!
    // 0xa7715c: SaveReg r0
    //     0xa7715c: str             x0, [SP, #-8]!
    // 0xa77160: r0 = 74
    //     0xa77160: movz            x0, #0x4a
    // 0xa77164: r30 = DoubleToIntegerStub
    //     0xa77164: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xa77168: LoadField: r30 = r30->field_7
    //     0xa77168: ldur            lr, [lr, #7]
    // 0xa7716c: blr             lr
    // 0xa77170: mov             x1, x0
    // 0xa77174: RestoreReg r0
    //     0xa77174: ldr             x0, [SP], #8
    // 0xa77178: RestoreReg d0
    //     0xa77178: ldr             q0, [SP], #0x10
    // 0xa7717c: b               #0xa76220
    // 0xa77180: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa77180: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa77184: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa77184: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa77188: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa77188: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa7718c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa7718c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa77190: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa77190: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa77194: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa77194: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa77198: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa77198: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa7719c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa7719c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa771a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa771a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa771a4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa771a4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa771a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa771a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa771ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa771ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa771b0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa771b0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa771b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa771b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa771b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa771b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa771bc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa771bc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa771c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa771c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa771c4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa771c4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa771c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa771c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa771cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa771cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa771d0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa771d0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa771d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa771d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa771d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa771d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa771dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa771dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa771e0, size: 0x110
    // 0xa771e0: EnterFrame
    //     0xa771e0: stp             fp, lr, [SP, #-0x10]!
    //     0xa771e4: mov             fp, SP
    // 0xa771e8: AllocStack(0x20)
    //     0xa771e8: sub             SP, SP, #0x20
    // 0xa771ec: SetupParameters()
    //     0xa771ec: ldr             x0, [fp, #0x10]
    //     0xa771f0: ldur            w1, [x0, #0x17]
    //     0xa771f4: add             x1, x1, HEAP, lsl #32
    //     0xa771f8: stur            x1, [fp, #-8]
    // 0xa771fc: CheckStackOverflow
    //     0xa771fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa77200: cmp             SP, x16
    //     0xa77204: b.ls            #0xa772dc
    // 0xa77208: LoadField: r0 = r1->field_13
    //     0xa77208: ldur            w0, [x1, #0x13]
    // 0xa7720c: DecompressPointer r0
    //     0xa7720c: add             x0, x0, HEAP, lsl #32
    // 0xa77210: cmp             w0, NULL
    // 0xa77214: b.eq            #0xa772e4
    // 0xa77218: r17 = 275
    //     0xa77218: movz            x17, #0x113
    // 0xa7721c: ldr             w2, [x0, x17]
    // 0xa77220: DecompressPointer r2
    //     0xa77220: add             x2, x2, HEAP, lsl #32
    // 0xa77224: cmp             w2, NULL
    // 0xa77228: b.eq            #0xa77230
    // 0xa7722c: tbz             w2, #4, #0xa772cc
    // 0xa77230: LoadField: r0 = r1->field_f
    //     0xa77230: ldur            w0, [x1, #0xf]
    // 0xa77234: DecompressPointer r0
    //     0xa77234: add             x0, x0, HEAP, lsl #32
    // 0xa77238: LoadField: r2 = r0->field_b
    //     0xa77238: ldur            w2, [x0, #0xb]
    // 0xa7723c: DecompressPointer r2
    //     0xa7723c: add             x2, x2, HEAP, lsl #32
    // 0xa77240: cmp             w2, NULL
    // 0xa77244: b.eq            #0xa772e8
    // 0xa77248: LoadField: r0 = r2->field_37
    //     0xa77248: ldur            w0, [x2, #0x37]
    // 0xa7724c: DecompressPointer r0
    //     0xa7724c: add             x0, x0, HEAP, lsl #32
    // 0xa77250: r16 = "add_to_bag"
    //     0xa77250: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xa77254: ldr             x16, [x16, #0xa38]
    // 0xa77258: stp             x16, x0, [SP]
    // 0xa7725c: r4 = 0
    //     0xa7725c: movz            x4, #0
    // 0xa77260: ldr             x0, [SP, #8]
    // 0xa77264: r16 = UnlinkedCall_0x613b5c
    //     0xa77264: add             x16, PP, #0x52, lsl #12  ; [pp+0x52e20] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa77268: add             x16, x16, #0xe20
    // 0xa7726c: ldp             x5, lr, [x16]
    // 0xa77270: blr             lr
    // 0xa77274: ldur            x0, [fp, #-8]
    // 0xa77278: LoadField: r1 = r0->field_f
    //     0xa77278: ldur            w1, [x0, #0xf]
    // 0xa7727c: DecompressPointer r1
    //     0xa7727c: add             x1, x1, HEAP, lsl #32
    // 0xa77280: LoadField: r2 = r1->field_b
    //     0xa77280: ldur            w2, [x1, #0xb]
    // 0xa77284: DecompressPointer r2
    //     0xa77284: add             x2, x2, HEAP, lsl #32
    // 0xa77288: stur            x2, [fp, #-0x10]
    // 0xa7728c: cmp             w2, NULL
    // 0xa77290: b.eq            #0xa772ec
    // 0xa77294: LoadField: r1 = r0->field_13
    //     0xa77294: ldur            w1, [x0, #0x13]
    // 0xa77298: DecompressPointer r1
    //     0xa77298: add             x1, x1, HEAP, lsl #32
    // 0xa7729c: cmp             w1, NULL
    // 0xa772a0: b.ne            #0xa772ac
    // 0xa772a4: r0 = WidgetEntity()
    //     0xa772a4: bl              #0xa749ec  ; AllocateWidgetEntityStub -> WidgetEntity (size=0x140)
    // 0xa772a8: mov             x1, x0
    // 0xa772ac: ldur            x0, [fp, #-0x10]
    // 0xa772b0: LoadField: r2 = r0->field_43
    //     0xa772b0: ldur            w2, [x0, #0x43]
    // 0xa772b4: DecompressPointer r2
    //     0xa772b4: add             x2, x2, HEAP, lsl #32
    // 0xa772b8: stp             x1, x2, [SP]
    // 0xa772bc: mov             x0, x2
    // 0xa772c0: ClosureCall
    //     0xa772c0: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xa772c4: ldur            x2, [x0, #0x1f]
    //     0xa772c8: blr             x2
    // 0xa772cc: r0 = Null
    //     0xa772cc: mov             x0, NULL
    // 0xa772d0: LeaveFrame
    //     0xa772d0: mov             SP, fp
    //     0xa772d4: ldp             fp, lr, [SP], #0x10
    // 0xa772d8: ret
    //     0xa772d8: ret             
    // 0xa772dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa772dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa772e0: b               #0xa77208
    // 0xa772e4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa772e4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa772e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa772e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa772ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa772ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] AnimatedContainer <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa772f0, size: 0xc8
    // 0xa772f0: EnterFrame
    //     0xa772f0: stp             fp, lr, [SP, #-0x10]!
    //     0xa772f4: mov             fp, SP
    // 0xa772f8: AllocStack(0x20)
    //     0xa772f8: sub             SP, SP, #0x20
    // 0xa772fc: SetupParameters()
    //     0xa772fc: ldr             x0, [fp, #0x20]
    //     0xa77300: ldur            w1, [x0, #0x17]
    //     0xa77304: add             x1, x1, HEAP, lsl #32
    // 0xa77308: CheckStackOverflow
    //     0xa77308: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7730c: cmp             SP, x16
    //     0xa77310: b.ls            #0xa773a8
    // 0xa77314: LoadField: r2 = r1->field_f
    //     0xa77314: ldur            w2, [x1, #0xf]
    // 0xa77318: DecompressPointer r2
    //     0xa77318: add             x2, x2, HEAP, lsl #32
    // 0xa7731c: stur            x2, [fp, #-0x10]
    // 0xa77320: LoadField: r0 = r1->field_13
    //     0xa77320: ldur            w0, [x1, #0x13]
    // 0xa77324: DecompressPointer r0
    //     0xa77324: add             x0, x0, HEAP, lsl #32
    // 0xa77328: cmp             w0, NULL
    // 0xa7732c: b.eq            #0xa773b0
    // 0xa77330: LoadField: r3 = r0->field_e7
    //     0xa77330: ldur            w3, [x0, #0xe7]
    // 0xa77334: DecompressPointer r3
    //     0xa77334: add             x3, x3, HEAP, lsl #32
    // 0xa77338: stur            x3, [fp, #-8]
    // 0xa7733c: LoadField: r0 = r2->field_b
    //     0xa7733c: ldur            w0, [x2, #0xb]
    // 0xa77340: DecompressPointer r0
    //     0xa77340: add             x0, x0, HEAP, lsl #32
    // 0xa77344: cmp             w0, NULL
    // 0xa77348: b.eq            #0xa773b4
    // 0xa7734c: LoadField: r4 = r0->field_b
    //     0xa7734c: ldur            w4, [x0, #0xb]
    // 0xa77350: DecompressPointer r4
    //     0xa77350: add             x4, x4, HEAP, lsl #32
    // 0xa77354: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa77354: ldur            w0, [x1, #0x17]
    // 0xa77358: DecompressPointer r0
    //     0xa77358: add             x0, x0, HEAP, lsl #32
    // 0xa7735c: r1 = LoadClassIdInstr(r4)
    //     0xa7735c: ldur            x1, [x4, #-1]
    //     0xa77360: ubfx            x1, x1, #0xc, #0x14
    // 0xa77364: stp             x0, x4, [SP]
    // 0xa77368: mov             x0, x1
    // 0xa7736c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa7736c: sub             lr, x0, #0xb7
    //     0xa77370: ldr             lr, [x21, lr, lsl #3]
    //     0xa77374: blr             lr
    // 0xa77378: mov             x1, x0
    // 0xa7737c: ldr             x0, [fp, #0x10]
    // 0xa77380: r5 = LoadInt32Instr(r0)
    //     0xa77380: sbfx            x5, x0, #1, #0x1f
    //     0xa77384: tbz             w0, #0, #0xa7738c
    //     0xa77388: ldur            x5, [x0, #7]
    // 0xa7738c: mov             x3, x1
    // 0xa77390: ldur            x1, [fp, #-0x10]
    // 0xa77394: ldur            x2, [fp, #-8]
    // 0xa77398: r0 = imageSlider()
    //     0xa77398: bl              #0xa773b8  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::imageSlider
    // 0xa7739c: LeaveFrame
    //     0xa7739c: mov             SP, fp
    //     0xa773a0: ldp             fp, lr, [SP], #0x10
    // 0xa773a4: ret
    //     0xa773a4: ret             
    // 0xa773a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa773a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa773ac: b               #0xa77314
    // 0xa773b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa773b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa773b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa773b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ imageSlider(/* No info */) {
    // ** addr: 0xa773b8, size: 0xf30
    // 0xa773b8: EnterFrame
    //     0xa773b8: stp             fp, lr, [SP, #-0x10]!
    //     0xa773bc: mov             fp, SP
    // 0xa773c0: AllocStack(0x90)
    //     0xa773c0: sub             SP, SP, #0x90
    // 0xa773c4: SetupParameters(_GroupCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */)
    //     0xa773c4: mov             x0, x1
    //     0xa773c8: stur            x1, [fp, #-8]
    //     0xa773cc: mov             x1, x5
    //     0xa773d0: stur            x2, [fp, #-0x10]
    //     0xa773d4: stur            x3, [fp, #-0x18]
    //     0xa773d8: stur            x5, [fp, #-0x20]
    // 0xa773dc: CheckStackOverflow
    //     0xa773dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa773e0: cmp             SP, x16
    //     0xa773e4: b.ls            #0xa7829c
    // 0xa773e8: r1 = 2
    //     0xa773e8: movz            x1, #0x2
    // 0xa773ec: r0 = AllocateContext()
    //     0xa773ec: bl              #0x16f6108  ; AllocateContextStub
    // 0xa773f0: mov             x1, x0
    // 0xa773f4: ldur            x0, [fp, #-8]
    // 0xa773f8: stur            x1, [fp, #-0x28]
    // 0xa773fc: StoreField: r1->field_f = r0
    //     0xa773fc: stur            w0, [x1, #0xf]
    // 0xa77400: ldur            x2, [fp, #-0x18]
    // 0xa77404: StoreField: r1->field_13 = r2
    //     0xa77404: stur            w2, [x1, #0x13]
    // 0xa77408: r0 = ImageHeaders.forImages()
    //     0xa77408: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xa7740c: mov             x3, x0
    // 0xa77410: ldur            x2, [fp, #-0x10]
    // 0xa77414: stur            x3, [fp, #-0x18]
    // 0xa77418: cmp             w2, NULL
    // 0xa7741c: b.eq            #0xa782a4
    // 0xa77420: LoadField: r0 = r2->field_b
    //     0xa77420: ldur            w0, [x2, #0xb]
    // 0xa77424: r1 = LoadInt32Instr(r0)
    //     0xa77424: sbfx            x1, x0, #1, #0x1f
    // 0xa77428: mov             x0, x1
    // 0xa7742c: ldur            x1, [fp, #-0x20]
    // 0xa77430: cmp             x1, x0
    // 0xa77434: b.hs            #0xa782a8
    // 0xa77438: LoadField: r0 = r2->field_f
    //     0xa77438: ldur            w0, [x2, #0xf]
    // 0xa7743c: DecompressPointer r0
    //     0xa7743c: add             x0, x0, HEAP, lsl #32
    // 0xa77440: ldur            x1, [fp, #-0x20]
    // 0xa77444: ArrayLoad: r2 = r0[r1]  ; Unknown_4
    //     0xa77444: add             x16, x0, x1, lsl #2
    //     0xa77448: ldur            w2, [x16, #0xf]
    // 0xa7744c: DecompressPointer r2
    //     0xa7744c: add             x2, x2, HEAP, lsl #32
    // 0xa77450: LoadField: r0 = r2->field_b
    //     0xa77450: ldur            w0, [x2, #0xb]
    // 0xa77454: DecompressPointer r0
    //     0xa77454: add             x0, x0, HEAP, lsl #32
    // 0xa77458: stur            x0, [fp, #-0x10]
    // 0xa7745c: cmp             w0, NULL
    // 0xa77460: b.eq            #0xa782ac
    // 0xa77464: r1 = Function '<anonymous closure>':.
    //     0xa77464: add             x1, PP, #0x52, lsl #12  ; [pp+0x52e30] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa77468: ldr             x1, [x1, #0xe30]
    // 0xa7746c: r2 = Null
    //     0xa7746c: mov             x2, NULL
    // 0xa77470: r0 = AllocateClosure()
    //     0xa77470: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa77474: r1 = Function '<anonymous closure>':.
    //     0xa77474: add             x1, PP, #0x52, lsl #12  ; [pp+0x52e38] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa77478: ldr             x1, [x1, #0xe38]
    // 0xa7747c: r2 = Null
    //     0xa7747c: mov             x2, NULL
    // 0xa77480: stur            x0, [fp, #-0x30]
    // 0xa77484: r0 = AllocateClosure()
    //     0xa77484: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa77488: stur            x0, [fp, #-0x38]
    // 0xa7748c: r0 = CachedNetworkImage()
    //     0xa7748c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa77490: stur            x0, [fp, #-0x40]
    // 0xa77494: ldur            x16, [fp, #-0x18]
    // 0xa77498: r30 = Instance_BoxFit
    //     0xa77498: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xa7749c: ldr             lr, [lr, #0x118]
    // 0xa774a0: stp             lr, x16, [SP, #0x10]
    // 0xa774a4: ldur            x16, [fp, #-0x30]
    // 0xa774a8: ldur            lr, [fp, #-0x38]
    // 0xa774ac: stp             lr, x16, [SP]
    // 0xa774b0: mov             x1, x0
    // 0xa774b4: ldur            x2, [fp, #-0x10]
    // 0xa774b8: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x3, httpHeaders, 0x2, progressIndicatorBuilder, 0x4, null]
    //     0xa774b8: add             x4, PP, #0x52, lsl #12  ; [pp+0x52828] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x3, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x4, Null]
    //     0xa774bc: ldr             x4, [x4, #0x828]
    // 0xa774c0: r0 = CachedNetworkImage()
    //     0xa774c0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa774c4: r0 = Center()
    //     0xa774c4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa774c8: mov             x1, x0
    // 0xa774cc: r0 = Instance_Alignment
    //     0xa774cc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa774d0: ldr             x0, [x0, #0xb10]
    // 0xa774d4: stur            x1, [fp, #-0x38]
    // 0xa774d8: StoreField: r1->field_f = r0
    //     0xa774d8: stur            w0, [x1, #0xf]
    // 0xa774dc: ldur            x2, [fp, #-0x40]
    // 0xa774e0: StoreField: r1->field_b = r2
    //     0xa774e0: stur            w2, [x1, #0xb]
    // 0xa774e4: ldur            x2, [fp, #-0x28]
    // 0xa774e8: LoadField: r3 = r2->field_13
    //     0xa774e8: ldur            w3, [x2, #0x13]
    // 0xa774ec: DecompressPointer r3
    //     0xa774ec: add             x3, x3, HEAP, lsl #32
    // 0xa774f0: stur            x3, [fp, #-0x30]
    // 0xa774f4: cmp             w3, NULL
    // 0xa774f8: b.ne            #0xa77504
    // 0xa774fc: r4 = Null
    //     0xa774fc: mov             x4, NULL
    // 0xa77500: b               #0xa77534
    // 0xa77504: r17 = 295
    //     0xa77504: movz            x17, #0x127
    // 0xa77508: ldr             w4, [x3, x17]
    // 0xa7750c: DecompressPointer r4
    //     0xa7750c: add             x4, x4, HEAP, lsl #32
    // 0xa77510: cmp             w4, NULL
    // 0xa77514: b.ne            #0xa77520
    // 0xa77518: r4 = Null
    //     0xa77518: mov             x4, NULL
    // 0xa7751c: b               #0xa77534
    // 0xa77520: LoadField: r5 = r4->field_7
    //     0xa77520: ldur            w5, [x4, #7]
    // 0xa77524: cbnz            w5, #0xa77530
    // 0xa77528: r4 = false
    //     0xa77528: add             x4, NULL, #0x30  ; false
    // 0xa7752c: b               #0xa77534
    // 0xa77530: r4 = true
    //     0xa77530: add             x4, NULL, #0x20  ; true
    // 0xa77534: cmp             w4, NULL
    // 0xa77538: b.ne            #0xa77540
    // 0xa7753c: r4 = false
    //     0xa7753c: add             x4, NULL, #0x30  ; false
    // 0xa77540: stur            x4, [fp, #-0x18]
    // 0xa77544: cmp             w3, NULL
    // 0xa77548: b.ne            #0xa77554
    // 0xa7754c: r5 = Null
    //     0xa7754c: mov             x5, NULL
    // 0xa77550: b               #0xa77560
    // 0xa77554: r17 = 271
    //     0xa77554: movz            x17, #0x10f
    // 0xa77558: ldr             w5, [x3, x17]
    // 0xa7755c: DecompressPointer r5
    //     0xa7755c: add             x5, x5, HEAP, lsl #32
    // 0xa77560: cmp             w5, NULL
    // 0xa77564: b.ne            #0xa77584
    // 0xa77568: mov             x5, x2
    // 0xa7756c: r3 = Instance_Alignment
    //     0xa7756c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xa77570: ldr             x3, [x3, #0xfa0]
    // 0xa77574: r2 = 4
    //     0xa77574: movz            x2, #0x4
    // 0xa77578: d0 = 0.700000
    //     0xa77578: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa7757c: ldr             d0, [x17, #0xf48]
    // 0xa77580: b               #0xa779e4
    // 0xa77584: tbnz            w5, #4, #0xa779cc
    // 0xa77588: ldur            x0, [fp, #-8]
    // 0xa7758c: LoadField: r5 = r0->field_b
    //     0xa7758c: ldur            w5, [x0, #0xb]
    // 0xa77590: DecompressPointer r5
    //     0xa77590: add             x5, x5, HEAP, lsl #32
    // 0xa77594: cmp             w5, NULL
    // 0xa77598: b.eq            #0xa782b0
    // 0xa7759c: LoadField: r6 = r5->field_2f
    //     0xa7759c: ldur            w6, [x5, #0x2f]
    // 0xa775a0: DecompressPointer r6
    //     0xa775a0: add             x6, x6, HEAP, lsl #32
    // 0xa775a4: LoadField: r5 = r6->field_13
    //     0xa775a4: ldur            w5, [x6, #0x13]
    // 0xa775a8: DecompressPointer r5
    //     0xa775a8: add             x5, x5, HEAP, lsl #32
    // 0xa775ac: stur            x5, [fp, #-0x10]
    // 0xa775b0: cmp             w5, NULL
    // 0xa775b4: b.ne            #0xa775c0
    // 0xa775b8: r6 = Null
    //     0xa775b8: mov             x6, NULL
    // 0xa775bc: b               #0xa775c8
    // 0xa775c0: LoadField: r6 = r5->field_7
    //     0xa775c0: ldur            w6, [x5, #7]
    // 0xa775c4: DecompressPointer r6
    //     0xa775c4: add             x6, x6, HEAP, lsl #32
    // 0xa775c8: cmp             w6, NULL
    // 0xa775cc: b.ne            #0xa775d8
    // 0xa775d0: r6 = 0
    //     0xa775d0: movz            x6, #0
    // 0xa775d4: b               #0xa775e8
    // 0xa775d8: r7 = LoadInt32Instr(r6)
    //     0xa775d8: sbfx            x7, x6, #1, #0x1f
    //     0xa775dc: tbz             w6, #0, #0xa775e4
    //     0xa775e0: ldur            x7, [x6, #7]
    // 0xa775e4: mov             x6, x7
    // 0xa775e8: stur            x6, [fp, #-0x50]
    // 0xa775ec: cmp             w5, NULL
    // 0xa775f0: b.ne            #0xa775fc
    // 0xa775f4: r7 = Null
    //     0xa775f4: mov             x7, NULL
    // 0xa775f8: b               #0xa77604
    // 0xa775fc: LoadField: r7 = r5->field_b
    //     0xa775fc: ldur            w7, [x5, #0xb]
    // 0xa77600: DecompressPointer r7
    //     0xa77600: add             x7, x7, HEAP, lsl #32
    // 0xa77604: cmp             w7, NULL
    // 0xa77608: b.ne            #0xa77614
    // 0xa7760c: r7 = 0
    //     0xa7760c: movz            x7, #0
    // 0xa77610: b               #0xa77624
    // 0xa77614: r8 = LoadInt32Instr(r7)
    //     0xa77614: sbfx            x8, x7, #1, #0x1f
    //     0xa77618: tbz             w7, #0, #0xa77620
    //     0xa7761c: ldur            x8, [x7, #7]
    // 0xa77620: mov             x7, x8
    // 0xa77624: stur            x7, [fp, #-0x48]
    // 0xa77628: cmp             w5, NULL
    // 0xa7762c: b.ne            #0xa77638
    // 0xa77630: r8 = Null
    //     0xa77630: mov             x8, NULL
    // 0xa77634: b               #0xa77640
    // 0xa77638: LoadField: r8 = r5->field_f
    //     0xa77638: ldur            w8, [x5, #0xf]
    // 0xa7763c: DecompressPointer r8
    //     0xa7763c: add             x8, x8, HEAP, lsl #32
    // 0xa77640: cmp             w8, NULL
    // 0xa77644: b.ne            #0xa77650
    // 0xa77648: r8 = 0
    //     0xa77648: movz            x8, #0
    // 0xa7764c: b               #0xa77660
    // 0xa77650: r9 = LoadInt32Instr(r8)
    //     0xa77650: sbfx            x9, x8, #1, #0x1f
    //     0xa77654: tbz             w8, #0, #0xa7765c
    //     0xa77658: ldur            x9, [x8, #7]
    // 0xa7765c: mov             x8, x9
    // 0xa77660: stur            x8, [fp, #-0x20]
    // 0xa77664: r0 = Color()
    //     0xa77664: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xa77668: mov             x1, x0
    // 0xa7766c: r0 = Instance_ColorSpace
    //     0xa7766c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xa77670: stur            x1, [fp, #-0x40]
    // 0xa77674: StoreField: r1->field_27 = r0
    //     0xa77674: stur            w0, [x1, #0x27]
    // 0xa77678: d0 = 1.000000
    //     0xa77678: fmov            d0, #1.00000000
    // 0xa7767c: StoreField: r1->field_7 = d0
    //     0xa7767c: stur            d0, [x1, #7]
    // 0xa77680: ldur            x2, [fp, #-0x50]
    // 0xa77684: ubfx            x2, x2, #0, #0x20
    // 0xa77688: and             w3, w2, #0xff
    // 0xa7768c: ubfx            x3, x3, #0, #0x20
    // 0xa77690: scvtf           d0, x3
    // 0xa77694: d1 = 255.000000
    //     0xa77694: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xa77698: fdiv            d2, d0, d1
    // 0xa7769c: StoreField: r1->field_f = d2
    //     0xa7769c: stur            d2, [x1, #0xf]
    // 0xa776a0: ldur            x2, [fp, #-0x48]
    // 0xa776a4: ubfx            x2, x2, #0, #0x20
    // 0xa776a8: and             w3, w2, #0xff
    // 0xa776ac: ubfx            x3, x3, #0, #0x20
    // 0xa776b0: scvtf           d0, x3
    // 0xa776b4: fdiv            d2, d0, d1
    // 0xa776b8: ArrayStore: r1[0] = d2  ; List_8
    //     0xa776b8: stur            d2, [x1, #0x17]
    // 0xa776bc: ldur            x2, [fp, #-0x20]
    // 0xa776c0: ubfx            x2, x2, #0, #0x20
    // 0xa776c4: and             w3, w2, #0xff
    // 0xa776c8: ubfx            x3, x3, #0, #0x20
    // 0xa776cc: scvtf           d0, x3
    // 0xa776d0: fdiv            d2, d0, d1
    // 0xa776d4: StoreField: r1->field_1f = d2
    //     0xa776d4: stur            d2, [x1, #0x1f]
    // 0xa776d8: ldur            x2, [fp, #-0x10]
    // 0xa776dc: cmp             w2, NULL
    // 0xa776e0: b.ne            #0xa776ec
    // 0xa776e4: r3 = Null
    //     0xa776e4: mov             x3, NULL
    // 0xa776e8: b               #0xa776f4
    // 0xa776ec: LoadField: r3 = r2->field_7
    //     0xa776ec: ldur            w3, [x2, #7]
    // 0xa776f0: DecompressPointer r3
    //     0xa776f0: add             x3, x3, HEAP, lsl #32
    // 0xa776f4: cmp             w3, NULL
    // 0xa776f8: b.ne            #0xa77704
    // 0xa776fc: r3 = 0
    //     0xa776fc: movz            x3, #0
    // 0xa77700: b               #0xa77714
    // 0xa77704: r4 = LoadInt32Instr(r3)
    //     0xa77704: sbfx            x4, x3, #1, #0x1f
    //     0xa77708: tbz             w3, #0, #0xa77710
    //     0xa7770c: ldur            x4, [x3, #7]
    // 0xa77710: mov             x3, x4
    // 0xa77714: stur            x3, [fp, #-0x50]
    // 0xa77718: cmp             w2, NULL
    // 0xa7771c: b.ne            #0xa77728
    // 0xa77720: r4 = Null
    //     0xa77720: mov             x4, NULL
    // 0xa77724: b               #0xa77730
    // 0xa77728: LoadField: r4 = r2->field_b
    //     0xa77728: ldur            w4, [x2, #0xb]
    // 0xa7772c: DecompressPointer r4
    //     0xa7772c: add             x4, x4, HEAP, lsl #32
    // 0xa77730: cmp             w4, NULL
    // 0xa77734: b.ne            #0xa77740
    // 0xa77738: r4 = 0
    //     0xa77738: movz            x4, #0
    // 0xa7773c: b               #0xa77750
    // 0xa77740: r5 = LoadInt32Instr(r4)
    //     0xa77740: sbfx            x5, x4, #1, #0x1f
    //     0xa77744: tbz             w4, #0, #0xa7774c
    //     0xa77748: ldur            x5, [x4, #7]
    // 0xa7774c: mov             x4, x5
    // 0xa77750: stur            x4, [fp, #-0x48]
    // 0xa77754: cmp             w2, NULL
    // 0xa77758: b.ne            #0xa77764
    // 0xa7775c: r2 = Null
    //     0xa7775c: mov             x2, NULL
    // 0xa77760: b               #0xa77770
    // 0xa77764: LoadField: r5 = r2->field_f
    //     0xa77764: ldur            w5, [x2, #0xf]
    // 0xa77768: DecompressPointer r5
    //     0xa77768: add             x5, x5, HEAP, lsl #32
    // 0xa7776c: mov             x2, x5
    // 0xa77770: cmp             w2, NULL
    // 0xa77774: b.ne            #0xa77780
    // 0xa77778: r5 = 0
    //     0xa77778: movz            x5, #0
    // 0xa7777c: b               #0xa7778c
    // 0xa77780: r5 = LoadInt32Instr(r2)
    //     0xa77780: sbfx            x5, x2, #1, #0x1f
    //     0xa77784: tbz             w2, #0, #0xa7778c
    //     0xa77788: ldur            x5, [x2, #7]
    // 0xa7778c: ldur            x2, [fp, #-0x30]
    // 0xa77790: stur            x5, [fp, #-0x20]
    // 0xa77794: r0 = Color()
    //     0xa77794: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xa77798: mov             x3, x0
    // 0xa7779c: r0 = Instance_ColorSpace
    //     0xa7779c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xa777a0: stur            x3, [fp, #-0x10]
    // 0xa777a4: StoreField: r3->field_27 = r0
    //     0xa777a4: stur            w0, [x3, #0x27]
    // 0xa777a8: d0 = 0.700000
    //     0xa777a8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa777ac: ldr             d0, [x17, #0xf48]
    // 0xa777b0: StoreField: r3->field_7 = d0
    //     0xa777b0: stur            d0, [x3, #7]
    // 0xa777b4: ldur            x0, [fp, #-0x50]
    // 0xa777b8: ubfx            x0, x0, #0, #0x20
    // 0xa777bc: and             w1, w0, #0xff
    // 0xa777c0: ubfx            x1, x1, #0, #0x20
    // 0xa777c4: scvtf           d0, x1
    // 0xa777c8: d1 = 255.000000
    //     0xa777c8: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xa777cc: fdiv            d2, d0, d1
    // 0xa777d0: StoreField: r3->field_f = d2
    //     0xa777d0: stur            d2, [x3, #0xf]
    // 0xa777d4: ldur            x0, [fp, #-0x48]
    // 0xa777d8: ubfx            x0, x0, #0, #0x20
    // 0xa777dc: and             w1, w0, #0xff
    // 0xa777e0: ubfx            x1, x1, #0, #0x20
    // 0xa777e4: scvtf           d0, x1
    // 0xa777e8: fdiv            d2, d0, d1
    // 0xa777ec: ArrayStore: r3[0] = d2  ; List_8
    //     0xa777ec: stur            d2, [x3, #0x17]
    // 0xa777f0: ldur            x0, [fp, #-0x20]
    // 0xa777f4: ubfx            x0, x0, #0, #0x20
    // 0xa777f8: and             w1, w0, #0xff
    // 0xa777fc: ubfx            x1, x1, #0, #0x20
    // 0xa77800: scvtf           d0, x1
    // 0xa77804: fdiv            d2, d0, d1
    // 0xa77808: StoreField: r3->field_1f = d2
    //     0xa77808: stur            d2, [x3, #0x1f]
    // 0xa7780c: r1 = Null
    //     0xa7780c: mov             x1, NULL
    // 0xa77810: r2 = 4
    //     0xa77810: movz            x2, #0x4
    // 0xa77814: r0 = AllocateArray()
    //     0xa77814: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa77818: mov             x2, x0
    // 0xa7781c: ldur            x0, [fp, #-0x40]
    // 0xa77820: stur            x2, [fp, #-0x58]
    // 0xa77824: StoreField: r2->field_f = r0
    //     0xa77824: stur            w0, [x2, #0xf]
    // 0xa77828: ldur            x0, [fp, #-0x10]
    // 0xa7782c: StoreField: r2->field_13 = r0
    //     0xa7782c: stur            w0, [x2, #0x13]
    // 0xa77830: r1 = <Color>
    //     0xa77830: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa77834: ldr             x1, [x1, #0xf80]
    // 0xa77838: r0 = AllocateGrowableArray()
    //     0xa77838: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa7783c: mov             x1, x0
    // 0xa77840: ldur            x0, [fp, #-0x58]
    // 0xa77844: stur            x1, [fp, #-0x10]
    // 0xa77848: StoreField: r1->field_f = r0
    //     0xa77848: stur            w0, [x1, #0xf]
    // 0xa7784c: r2 = 4
    //     0xa7784c: movz            x2, #0x4
    // 0xa77850: StoreField: r1->field_b = r2
    //     0xa77850: stur            w2, [x1, #0xb]
    // 0xa77854: r0 = LinearGradient()
    //     0xa77854: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xa77858: mov             x1, x0
    // 0xa7785c: r0 = Instance_Alignment
    //     0xa7785c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xa77860: ldr             x0, [x0, #0xce0]
    // 0xa77864: stur            x1, [fp, #-0x40]
    // 0xa77868: StoreField: r1->field_13 = r0
    //     0xa77868: stur            w0, [x1, #0x13]
    // 0xa7786c: r0 = Instance_Alignment
    //     0xa7786c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xa77870: ldr             x0, [x0, #0xce8]
    // 0xa77874: ArrayStore: r1[0] = r0  ; List_4
    //     0xa77874: stur            w0, [x1, #0x17]
    // 0xa77878: r0 = Instance_TileMode
    //     0xa77878: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xa7787c: ldr             x0, [x0, #0xcf0]
    // 0xa77880: StoreField: r1->field_1b = r0
    //     0xa77880: stur            w0, [x1, #0x1b]
    // 0xa77884: ldur            x0, [fp, #-0x10]
    // 0xa77888: StoreField: r1->field_7 = r0
    //     0xa77888: stur            w0, [x1, #7]
    // 0xa7788c: r0 = BoxDecoration()
    //     0xa7788c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa77890: mov             x2, x0
    // 0xa77894: r0 = Instance_BorderRadius
    //     0xa77894: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xa77898: ldr             x0, [x0, #0xf70]
    // 0xa7789c: stur            x2, [fp, #-0x58]
    // 0xa778a0: StoreField: r2->field_13 = r0
    //     0xa778a0: stur            w0, [x2, #0x13]
    // 0xa778a4: ldur            x0, [fp, #-0x40]
    // 0xa778a8: StoreField: r2->field_1b = r0
    //     0xa778a8: stur            w0, [x2, #0x1b]
    // 0xa778ac: r0 = Instance_BoxShape
    //     0xa778ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa778b0: ldr             x0, [x0, #0x80]
    // 0xa778b4: StoreField: r2->field_23 = r0
    //     0xa778b4: stur            w0, [x2, #0x23]
    // 0xa778b8: ldur            x1, [fp, #-0x30]
    // 0xa778bc: cmp             w1, NULL
    // 0xa778c0: b.ne            #0xa778cc
    // 0xa778c4: r1 = Null
    //     0xa778c4: mov             x1, NULL
    // 0xa778c8: b               #0xa778dc
    // 0xa778cc: r17 = 295
    //     0xa778cc: movz            x17, #0x127
    // 0xa778d0: ldr             w3, [x1, x17]
    // 0xa778d4: DecompressPointer r3
    //     0xa778d4: add             x3, x3, HEAP, lsl #32
    // 0xa778d8: mov             x1, x3
    // 0xa778dc: cmp             w1, NULL
    // 0xa778e0: b.ne            #0xa778ec
    // 0xa778e4: r4 = ""
    //     0xa778e4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa778e8: b               #0xa778f0
    // 0xa778ec: mov             x4, x1
    // 0xa778f0: ldur            x3, [fp, #-8]
    // 0xa778f4: stur            x4, [fp, #-0x10]
    // 0xa778f8: LoadField: r1 = r3->field_f
    //     0xa778f8: ldur            w1, [x3, #0xf]
    // 0xa778fc: DecompressPointer r1
    //     0xa778fc: add             x1, x1, HEAP, lsl #32
    // 0xa77900: cmp             w1, NULL
    // 0xa77904: b.eq            #0xa782b4
    // 0xa77908: r0 = of()
    //     0xa77908: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa7790c: LoadField: r1 = r0->field_87
    //     0xa7790c: ldur            w1, [x0, #0x87]
    // 0xa77910: DecompressPointer r1
    //     0xa77910: add             x1, x1, HEAP, lsl #32
    // 0xa77914: LoadField: r0 = r1->field_7
    //     0xa77914: ldur            w0, [x1, #7]
    // 0xa77918: DecompressPointer r0
    //     0xa77918: add             x0, x0, HEAP, lsl #32
    // 0xa7791c: r16 = 12.000000
    //     0xa7791c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa77920: ldr             x16, [x16, #0x9e8]
    // 0xa77924: r30 = Instance_Color
    //     0xa77924: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa77928: stp             lr, x16, [SP]
    // 0xa7792c: mov             x1, x0
    // 0xa77930: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa77930: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa77934: ldr             x4, [x4, #0xaa0]
    // 0xa77938: r0 = copyWith()
    //     0xa77938: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa7793c: stur            x0, [fp, #-0x30]
    // 0xa77940: r0 = Text()
    //     0xa77940: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa77944: mov             x1, x0
    // 0xa77948: ldur            x0, [fp, #-0x10]
    // 0xa7794c: stur            x1, [fp, #-0x40]
    // 0xa77950: StoreField: r1->field_b = r0
    //     0xa77950: stur            w0, [x1, #0xb]
    // 0xa77954: ldur            x0, [fp, #-0x30]
    // 0xa77958: StoreField: r1->field_13 = r0
    //     0xa77958: stur            w0, [x1, #0x13]
    // 0xa7795c: r0 = Padding()
    //     0xa7795c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa77960: mov             x1, x0
    // 0xa77964: r0 = Instance_EdgeInsets
    //     0xa77964: add             x0, PP, #0x46, lsl #12  ; [pp+0x46f10] Obj!EdgeInsets@d58191
    //     0xa77968: ldr             x0, [x0, #0xf10]
    // 0xa7796c: stur            x1, [fp, #-0x10]
    // 0xa77970: StoreField: r1->field_f = r0
    //     0xa77970: stur            w0, [x1, #0xf]
    // 0xa77974: ldur            x0, [fp, #-0x40]
    // 0xa77978: StoreField: r1->field_b = r0
    //     0xa77978: stur            w0, [x1, #0xb]
    // 0xa7797c: r0 = Container()
    //     0xa7797c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa77980: stur            x0, [fp, #-0x30]
    // 0xa77984: r16 = 20.000000
    //     0xa77984: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xa77988: ldr             x16, [x16, #0xac8]
    // 0xa7798c: ldur            lr, [fp, #-0x58]
    // 0xa77990: stp             lr, x16, [SP, #8]
    // 0xa77994: ldur            x16, [fp, #-0x10]
    // 0xa77998: str             x16, [SP]
    // 0xa7799c: mov             x1, x0
    // 0xa779a0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xa779a0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xa779a4: ldr             x4, [x4, #0xc78]
    // 0xa779a8: r0 = Container()
    //     0xa779a8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa779ac: r0 = Align()
    //     0xa779ac: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa779b0: r3 = Instance_Alignment
    //     0xa779b0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xa779b4: ldr             x3, [x3, #0xfa0]
    // 0xa779b8: StoreField: r0->field_f = r3
    //     0xa779b8: stur            w3, [x0, #0xf]
    // 0xa779bc: ldur            x1, [fp, #-0x30]
    // 0xa779c0: StoreField: r0->field_b = r1
    //     0xa779c0: stur            w1, [x0, #0xb]
    // 0xa779c4: mov             x1, x0
    // 0xa779c8: b               #0xa77ce0
    // 0xa779cc: r3 = Instance_Alignment
    //     0xa779cc: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xa779d0: ldr             x3, [x3, #0xfa0]
    // 0xa779d4: r2 = 4
    //     0xa779d4: movz            x2, #0x4
    // 0xa779d8: d0 = 0.700000
    //     0xa779d8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa779dc: ldr             d0, [x17, #0xf48]
    // 0xa779e0: ldur            x5, [fp, #-0x28]
    // 0xa779e4: ldur            x4, [fp, #-8]
    // 0xa779e8: LoadField: r1 = r4->field_f
    //     0xa779e8: ldur            w1, [x4, #0xf]
    // 0xa779ec: DecompressPointer r1
    //     0xa779ec: add             x1, x1, HEAP, lsl #32
    // 0xa779f0: cmp             w1, NULL
    // 0xa779f4: b.eq            #0xa782b8
    // 0xa779f8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa779f8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa779fc: r0 = _of()
    //     0xa779fc: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xa77a00: LoadField: r1 = r0->field_7
    //     0xa77a00: ldur            w1, [x0, #7]
    // 0xa77a04: DecompressPointer r1
    //     0xa77a04: add             x1, x1, HEAP, lsl #32
    // 0xa77a08: LoadField: d0 = r1->field_7
    //     0xa77a08: ldur            d0, [x1, #7]
    // 0xa77a0c: d1 = 0.370000
    //     0xa77a0c: add             x17, PP, #0x52, lsl #12  ; [pp+0x52e40] IMM: double(0.37) from 0x3fd7ae147ae147ae
    //     0xa77a10: ldr             d1, [x17, #0xe40]
    // 0xa77a14: fmul            d2, d0, d1
    // 0xa77a18: ldur            x0, [fp, #-8]
    // 0xa77a1c: stur            d2, [fp, #-0x70]
    // 0xa77a20: LoadField: r1 = r0->field_f
    //     0xa77a20: ldur            w1, [x0, #0xf]
    // 0xa77a24: DecompressPointer r1
    //     0xa77a24: add             x1, x1, HEAP, lsl #32
    // 0xa77a28: cmp             w1, NULL
    // 0xa77a2c: b.eq            #0xa782bc
    // 0xa77a30: r0 = of()
    //     0xa77a30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa77a34: LoadField: r1 = r0->field_5b
    //     0xa77a34: ldur            w1, [x0, #0x5b]
    // 0xa77a38: DecompressPointer r1
    //     0xa77a38: add             x1, x1, HEAP, lsl #32
    // 0xa77a3c: stur            x1, [fp, #-0x10]
    // 0xa77a40: r0 = ColorFilter()
    //     0xa77a40: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xa77a44: mov             x1, x0
    // 0xa77a48: ldur            x0, [fp, #-0x10]
    // 0xa77a4c: stur            x1, [fp, #-0x30]
    // 0xa77a50: StoreField: r1->field_7 = r0
    //     0xa77a50: stur            w0, [x1, #7]
    // 0xa77a54: r0 = Instance_BlendMode
    //     0xa77a54: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa77a58: ldr             x0, [x0, #0xb30]
    // 0xa77a5c: StoreField: r1->field_b = r0
    //     0xa77a5c: stur            w0, [x1, #0xb]
    // 0xa77a60: r0 = 1
    //     0xa77a60: movz            x0, #0x1
    // 0xa77a64: StoreField: r1->field_13 = r0
    //     0xa77a64: stur            x0, [x1, #0x13]
    // 0xa77a68: r0 = SvgPicture()
    //     0xa77a68: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa77a6c: stur            x0, [fp, #-0x10]
    // 0xa77a70: ldur            x16, [fp, #-0x30]
    // 0xa77a74: str             x16, [SP]
    // 0xa77a78: mov             x1, x0
    // 0xa77a7c: r2 = "assets/images/bumper_coupon.svg"
    //     0xa77a7c: add             x2, PP, #0x52, lsl #12  ; [pp+0x52e48] "assets/images/bumper_coupon.svg"
    //     0xa77a80: ldr             x2, [x2, #0xe48]
    // 0xa77a84: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xa77a84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xa77a88: ldr             x4, [x4, #0xa38]
    // 0xa77a8c: r0 = SvgPicture.asset()
    //     0xa77a8c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa77a90: ldur            x2, [fp, #-0x28]
    // 0xa77a94: LoadField: r0 = r2->field_13
    //     0xa77a94: ldur            w0, [x2, #0x13]
    // 0xa77a98: DecompressPointer r0
    //     0xa77a98: add             x0, x0, HEAP, lsl #32
    // 0xa77a9c: cmp             w0, NULL
    // 0xa77aa0: b.ne            #0xa77aac
    // 0xa77aa4: r0 = Null
    //     0xa77aa4: mov             x0, NULL
    // 0xa77aa8: b               #0xa77abc
    // 0xa77aac: r17 = 295
    //     0xa77aac: movz            x17, #0x127
    // 0xa77ab0: ldr             w1, [x0, x17]
    // 0xa77ab4: DecompressPointer r1
    //     0xa77ab4: add             x1, x1, HEAP, lsl #32
    // 0xa77ab8: mov             x0, x1
    // 0xa77abc: cmp             w0, NULL
    // 0xa77ac0: b.ne            #0xa77acc
    // 0xa77ac4: r4 = ""
    //     0xa77ac4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa77ac8: b               #0xa77ad0
    // 0xa77acc: mov             x4, x0
    // 0xa77ad0: ldur            x3, [fp, #-8]
    // 0xa77ad4: ldur            d0, [fp, #-0x70]
    // 0xa77ad8: ldur            x0, [fp, #-0x10]
    // 0xa77adc: stur            x4, [fp, #-0x30]
    // 0xa77ae0: LoadField: r1 = r3->field_f
    //     0xa77ae0: ldur            w1, [x3, #0xf]
    // 0xa77ae4: DecompressPointer r1
    //     0xa77ae4: add             x1, x1, HEAP, lsl #32
    // 0xa77ae8: cmp             w1, NULL
    // 0xa77aec: b.eq            #0xa782c0
    // 0xa77af0: r0 = of()
    //     0xa77af0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa77af4: LoadField: r1 = r0->field_87
    //     0xa77af4: ldur            w1, [x0, #0x87]
    // 0xa77af8: DecompressPointer r1
    //     0xa77af8: add             x1, x1, HEAP, lsl #32
    // 0xa77afc: LoadField: r0 = r1->field_2b
    //     0xa77afc: ldur            w0, [x1, #0x2b]
    // 0xa77b00: DecompressPointer r0
    //     0xa77b00: add             x0, x0, HEAP, lsl #32
    // 0xa77b04: stur            x0, [fp, #-0x40]
    // 0xa77b08: r1 = Instance_Color
    //     0xa77b08: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa77b0c: d0 = 0.700000
    //     0xa77b0c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa77b10: ldr             d0, [x17, #0xf48]
    // 0xa77b14: r0 = withOpacity()
    //     0xa77b14: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa77b18: r16 = 12.000000
    //     0xa77b18: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa77b1c: ldr             x16, [x16, #0x9e8]
    // 0xa77b20: stp             x0, x16, [SP]
    // 0xa77b24: ldur            x1, [fp, #-0x40]
    // 0xa77b28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa77b28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa77b2c: ldr             x4, [x4, #0xaa0]
    // 0xa77b30: r0 = copyWith()
    //     0xa77b30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa77b34: stur            x0, [fp, #-0x40]
    // 0xa77b38: r0 = Text()
    //     0xa77b38: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa77b3c: mov             x1, x0
    // 0xa77b40: ldur            x0, [fp, #-0x30]
    // 0xa77b44: stur            x1, [fp, #-0x58]
    // 0xa77b48: StoreField: r1->field_b = r0
    //     0xa77b48: stur            w0, [x1, #0xb]
    // 0xa77b4c: ldur            x0, [fp, #-0x40]
    // 0xa77b50: StoreField: r1->field_13 = r0
    //     0xa77b50: stur            w0, [x1, #0x13]
    // 0xa77b54: r0 = Instance_TextAlign
    //     0xa77b54: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xa77b58: StoreField: r1->field_1b = r0
    //     0xa77b58: stur            w0, [x1, #0x1b]
    // 0xa77b5c: r0 = Padding()
    //     0xa77b5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa77b60: mov             x3, x0
    // 0xa77b64: r0 = Instance_EdgeInsets
    //     0xa77b64: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xa77b68: ldr             x0, [x0, #0xe60]
    // 0xa77b6c: stur            x3, [fp, #-0x30]
    // 0xa77b70: StoreField: r3->field_f = r0
    //     0xa77b70: stur            w0, [x3, #0xf]
    // 0xa77b74: ldur            x0, [fp, #-0x58]
    // 0xa77b78: StoreField: r3->field_b = r0
    //     0xa77b78: stur            w0, [x3, #0xb]
    // 0xa77b7c: r1 = Null
    //     0xa77b7c: mov             x1, NULL
    // 0xa77b80: r2 = 4
    //     0xa77b80: movz            x2, #0x4
    // 0xa77b84: r0 = AllocateArray()
    //     0xa77b84: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa77b88: mov             x2, x0
    // 0xa77b8c: ldur            x0, [fp, #-0x10]
    // 0xa77b90: stur            x2, [fp, #-0x40]
    // 0xa77b94: StoreField: r2->field_f = r0
    //     0xa77b94: stur            w0, [x2, #0xf]
    // 0xa77b98: ldur            x0, [fp, #-0x30]
    // 0xa77b9c: StoreField: r2->field_13 = r0
    //     0xa77b9c: stur            w0, [x2, #0x13]
    // 0xa77ba0: r1 = <Widget>
    //     0xa77ba0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa77ba4: r0 = AllocateGrowableArray()
    //     0xa77ba4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa77ba8: mov             x1, x0
    // 0xa77bac: ldur            x0, [fp, #-0x40]
    // 0xa77bb0: stur            x1, [fp, #-0x10]
    // 0xa77bb4: StoreField: r1->field_f = r0
    //     0xa77bb4: stur            w0, [x1, #0xf]
    // 0xa77bb8: r0 = 4
    //     0xa77bb8: movz            x0, #0x4
    // 0xa77bbc: StoreField: r1->field_b = r0
    //     0xa77bbc: stur            w0, [x1, #0xb]
    // 0xa77bc0: r0 = Row()
    //     0xa77bc0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa77bc4: mov             x1, x0
    // 0xa77bc8: r0 = Instance_Axis
    //     0xa77bc8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa77bcc: stur            x1, [fp, #-0x30]
    // 0xa77bd0: StoreField: r1->field_f = r0
    //     0xa77bd0: stur            w0, [x1, #0xf]
    // 0xa77bd4: r0 = Instance_MainAxisAlignment
    //     0xa77bd4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xa77bd8: ldr             x0, [x0, #0xab0]
    // 0xa77bdc: StoreField: r1->field_13 = r0
    //     0xa77bdc: stur            w0, [x1, #0x13]
    // 0xa77be0: r0 = Instance_MainAxisSize
    //     0xa77be0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa77be4: ldr             x0, [x0, #0xa10]
    // 0xa77be8: ArrayStore: r1[0] = r0  ; List_4
    //     0xa77be8: stur            w0, [x1, #0x17]
    // 0xa77bec: r0 = Instance_CrossAxisAlignment
    //     0xa77bec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa77bf0: ldr             x0, [x0, #0xa18]
    // 0xa77bf4: StoreField: r1->field_1b = r0
    //     0xa77bf4: stur            w0, [x1, #0x1b]
    // 0xa77bf8: r0 = Instance_VerticalDirection
    //     0xa77bf8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa77bfc: ldr             x0, [x0, #0xa20]
    // 0xa77c00: StoreField: r1->field_23 = r0
    //     0xa77c00: stur            w0, [x1, #0x23]
    // 0xa77c04: r0 = Instance_Clip
    //     0xa77c04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa77c08: ldr             x0, [x0, #0x38]
    // 0xa77c0c: StoreField: r1->field_2b = r0
    //     0xa77c0c: stur            w0, [x1, #0x2b]
    // 0xa77c10: StoreField: r1->field_2f = rZR
    //     0xa77c10: stur            xzr, [x1, #0x2f]
    // 0xa77c14: ldur            x0, [fp, #-0x10]
    // 0xa77c18: StoreField: r1->field_b = r0
    //     0xa77c18: stur            w0, [x1, #0xb]
    // 0xa77c1c: r0 = Center()
    //     0xa77c1c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa77c20: mov             x1, x0
    // 0xa77c24: r0 = Instance_Alignment
    //     0xa77c24: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa77c28: ldr             x0, [x0, #0xb10]
    // 0xa77c2c: stur            x1, [fp, #-0x40]
    // 0xa77c30: StoreField: r1->field_f = r0
    //     0xa77c30: stur            w0, [x1, #0xf]
    // 0xa77c34: ldur            x0, [fp, #-0x30]
    // 0xa77c38: StoreField: r1->field_b = r0
    //     0xa77c38: stur            w0, [x1, #0xb]
    // 0xa77c3c: ldur            d0, [fp, #-0x70]
    // 0xa77c40: r0 = inline_Allocate_Double()
    //     0xa77c40: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xa77c44: add             x0, x0, #0x10
    //     0xa77c48: cmp             x2, x0
    //     0xa77c4c: b.ls            #0xa782c4
    //     0xa77c50: str             x0, [THR, #0x50]  ; THR::top
    //     0xa77c54: sub             x0, x0, #0xf
    //     0xa77c58: movz            x2, #0xe15c
    //     0xa77c5c: movk            x2, #0x3, lsl #16
    //     0xa77c60: stur            x2, [x0, #-1]
    // 0xa77c64: StoreField: r0->field_7 = d0
    //     0xa77c64: stur            d0, [x0, #7]
    // 0xa77c68: stur            x0, [fp, #-0x10]
    // 0xa77c6c: r0 = Container()
    //     0xa77c6c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa77c70: stur            x0, [fp, #-0x30]
    // 0xa77c74: r16 = 20.000000
    //     0xa77c74: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xa77c78: ldr             x16, [x16, #0xac8]
    // 0xa77c7c: ldur            lr, [fp, #-0x10]
    // 0xa77c80: stp             lr, x16, [SP, #0x10]
    // 0xa77c84: r16 = Instance_BoxDecoration
    //     0xa77c84: add             x16, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xa77c88: ldr             x16, [x16, #0x5a8]
    // 0xa77c8c: ldur            lr, [fp, #-0x40]
    // 0xa77c90: stp             lr, x16, [SP]
    // 0xa77c94: mov             x1, x0
    // 0xa77c98: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xa77c98: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa77c9c: ldr             x4, [x4, #0x8c0]
    // 0xa77ca0: r0 = Container()
    //     0xa77ca0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa77ca4: r0 = Padding()
    //     0xa77ca4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa77ca8: mov             x1, x0
    // 0xa77cac: r0 = Instance_EdgeInsets
    //     0xa77cac: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xa77cb0: ldr             x0, [x0, #0xe50]
    // 0xa77cb4: stur            x1, [fp, #-0x10]
    // 0xa77cb8: StoreField: r1->field_f = r0
    //     0xa77cb8: stur            w0, [x1, #0xf]
    // 0xa77cbc: ldur            x0, [fp, #-0x30]
    // 0xa77cc0: StoreField: r1->field_b = r0
    //     0xa77cc0: stur            w0, [x1, #0xb]
    // 0xa77cc4: r0 = Align()
    //     0xa77cc4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa77cc8: mov             x1, x0
    // 0xa77ccc: r0 = Instance_Alignment
    //     0xa77ccc: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xa77cd0: ldr             x0, [x0, #0xfa0]
    // 0xa77cd4: StoreField: r1->field_f = r0
    //     0xa77cd4: stur            w0, [x1, #0xf]
    // 0xa77cd8: ldur            x0, [fp, #-0x10]
    // 0xa77cdc: StoreField: r1->field_b = r0
    //     0xa77cdc: stur            w0, [x1, #0xb]
    // 0xa77ce0: ldur            x2, [fp, #-0x28]
    // 0xa77ce4: ldur            x0, [fp, #-0x18]
    // 0xa77ce8: stur            x1, [fp, #-0x10]
    // 0xa77cec: r0 = Visibility()
    //     0xa77cec: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa77cf0: mov             x1, x0
    // 0xa77cf4: ldur            x0, [fp, #-0x10]
    // 0xa77cf8: stur            x1, [fp, #-0x30]
    // 0xa77cfc: StoreField: r1->field_b = r0
    //     0xa77cfc: stur            w0, [x1, #0xb]
    // 0xa77d00: r0 = Instance_SizedBox
    //     0xa77d00: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa77d04: StoreField: r1->field_f = r0
    //     0xa77d04: stur            w0, [x1, #0xf]
    // 0xa77d08: ldur            x2, [fp, #-0x18]
    // 0xa77d0c: StoreField: r1->field_13 = r2
    //     0xa77d0c: stur            w2, [x1, #0x13]
    // 0xa77d10: r2 = false
    //     0xa77d10: add             x2, NULL, #0x30  ; false
    // 0xa77d14: ArrayStore: r1[0] = r2  ; List_4
    //     0xa77d14: stur            w2, [x1, #0x17]
    // 0xa77d18: StoreField: r1->field_1b = r2
    //     0xa77d18: stur            w2, [x1, #0x1b]
    // 0xa77d1c: StoreField: r1->field_1f = r2
    //     0xa77d1c: stur            w2, [x1, #0x1f]
    // 0xa77d20: StoreField: r1->field_23 = r2
    //     0xa77d20: stur            w2, [x1, #0x23]
    // 0xa77d24: StoreField: r1->field_27 = r2
    //     0xa77d24: stur            w2, [x1, #0x27]
    // 0xa77d28: StoreField: r1->field_2b = r2
    //     0xa77d28: stur            w2, [x1, #0x2b]
    // 0xa77d2c: ldur            x3, [fp, #-0x28]
    // 0xa77d30: LoadField: r4 = r3->field_13
    //     0xa77d30: ldur            w4, [x3, #0x13]
    // 0xa77d34: DecompressPointer r4
    //     0xa77d34: add             x4, x4, HEAP, lsl #32
    // 0xa77d38: cmp             w4, NULL
    // 0xa77d3c: b.ne            #0xa77d48
    // 0xa77d40: r5 = Null
    //     0xa77d40: mov             x5, NULL
    // 0xa77d44: b               #0xa77d78
    // 0xa77d48: r17 = 311
    //     0xa77d48: movz            x17, #0x137
    // 0xa77d4c: ldr             w5, [x4, x17]
    // 0xa77d50: DecompressPointer r5
    //     0xa77d50: add             x5, x5, HEAP, lsl #32
    // 0xa77d54: cmp             w5, NULL
    // 0xa77d58: b.ne            #0xa77d64
    // 0xa77d5c: r5 = Null
    //     0xa77d5c: mov             x5, NULL
    // 0xa77d60: b               #0xa77d78
    // 0xa77d64: LoadField: r6 = r5->field_7
    //     0xa77d64: ldur            w6, [x5, #7]
    // 0xa77d68: cbnz            w6, #0xa77d74
    // 0xa77d6c: r5 = false
    //     0xa77d6c: add             x5, NULL, #0x30  ; false
    // 0xa77d70: b               #0xa77d78
    // 0xa77d74: r5 = true
    //     0xa77d74: add             x5, NULL, #0x20  ; true
    // 0xa77d78: cmp             w5, NULL
    // 0xa77d7c: b.ne            #0xa77d84
    // 0xa77d80: r5 = false
    //     0xa77d80: add             x5, NULL, #0x30  ; false
    // 0xa77d84: stur            x5, [fp, #-0x10]
    // 0xa77d88: cmp             w4, NULL
    // 0xa77d8c: b.ne            #0xa77d98
    // 0xa77d90: r4 = Null
    //     0xa77d90: mov             x4, NULL
    // 0xa77d94: b               #0xa77da8
    // 0xa77d98: r17 = 263
    //     0xa77d98: movz            x17, #0x107
    // 0xa77d9c: ldr             w6, [x4, x17]
    // 0xa77da0: DecompressPointer r6
    //     0xa77da0: add             x6, x6, HEAP, lsl #32
    // 0xa77da4: mov             x4, x6
    // 0xa77da8: cmp             w4, NULL
    // 0xa77dac: b.eq            #0xa77dc0
    // 0xa77db0: tbnz            w4, #4, #0xa77dc0
    // 0xa77db4: d0 = 38.000000
    //     0xa77db4: add             x17, PP, #0x50, lsl #12  ; [pp+0x50d10] IMM: double(38) from 0x4043000000000000
    //     0xa77db8: ldr             d0, [x17, #0xd10]
    // 0xa77dbc: b               #0xa77dc4
    // 0xa77dc0: d0 = 4.000000
    //     0xa77dc0: fmov            d0, #4.00000000
    // 0xa77dc4: stur            d0, [fp, #-0x70]
    // 0xa77dc8: r0 = EdgeInsets()
    //     0xa77dc8: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xa77dcc: d0 = 8.000000
    //     0xa77dcc: fmov            d0, #8.00000000
    // 0xa77dd0: stur            x0, [fp, #-0x18]
    // 0xa77dd4: StoreField: r0->field_7 = d0
    //     0xa77dd4: stur            d0, [x0, #7]
    // 0xa77dd8: StoreField: r0->field_f = rZR
    //     0xa77dd8: stur            xzr, [x0, #0xf]
    // 0xa77ddc: ArrayStore: r0[0] = rZR  ; List_8
    //     0xa77ddc: stur            xzr, [x0, #0x17]
    // 0xa77de0: ldur            d0, [fp, #-0x70]
    // 0xa77de4: StoreField: r0->field_1f = d0
    //     0xa77de4: stur            d0, [x0, #0x1f]
    // 0xa77de8: r16 = <EdgeInsets>
    //     0xa77de8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xa77dec: ldr             x16, [x16, #0xda0]
    // 0xa77df0: r30 = Instance_EdgeInsets
    //     0xa77df0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xa77df4: ldr             lr, [lr, #0x668]
    // 0xa77df8: stp             lr, x16, [SP]
    // 0xa77dfc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa77dfc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa77e00: r0 = all()
    //     0xa77e00: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa77e04: stur            x0, [fp, #-0x40]
    // 0xa77e08: r16 = <Color>
    //     0xa77e08: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa77e0c: ldr             x16, [x16, #0xf80]
    // 0xa77e10: r30 = Instance_Color
    //     0xa77e10: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa77e14: stp             lr, x16, [SP]
    // 0xa77e18: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa77e18: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa77e1c: r0 = all()
    //     0xa77e1c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa77e20: stur            x0, [fp, #-0x58]
    // 0xa77e24: r16 = <RoundedRectangleBorder>
    //     0xa77e24: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xa77e28: ldr             x16, [x16, #0xf78]
    // 0xa77e2c: r30 = Instance_RoundedRectangleBorder
    //     0xa77e2c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xa77e30: ldr             lr, [lr, #0xd68]
    // 0xa77e34: stp             lr, x16, [SP]
    // 0xa77e38: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa77e38: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa77e3c: r0 = all()
    //     0xa77e3c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa77e40: stur            x0, [fp, #-0x60]
    // 0xa77e44: r0 = ButtonStyle()
    //     0xa77e44: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xa77e48: mov             x1, x0
    // 0xa77e4c: ldur            x0, [fp, #-0x58]
    // 0xa77e50: stur            x1, [fp, #-0x68]
    // 0xa77e54: StoreField: r1->field_b = r0
    //     0xa77e54: stur            w0, [x1, #0xb]
    // 0xa77e58: ldur            x0, [fp, #-0x40]
    // 0xa77e5c: StoreField: r1->field_23 = r0
    //     0xa77e5c: stur            w0, [x1, #0x23]
    // 0xa77e60: ldur            x0, [fp, #-0x60]
    // 0xa77e64: StoreField: r1->field_43 = r0
    //     0xa77e64: stur            w0, [x1, #0x43]
    // 0xa77e68: r0 = TextButtonThemeData()
    //     0xa77e68: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xa77e6c: mov             x1, x0
    // 0xa77e70: ldur            x0, [fp, #-0x68]
    // 0xa77e74: stur            x1, [fp, #-0x40]
    // 0xa77e78: StoreField: r1->field_7 = r0
    //     0xa77e78: stur            w0, [x1, #7]
    // 0xa77e7c: ldur            x2, [fp, #-0x28]
    // 0xa77e80: LoadField: r0 = r2->field_13
    //     0xa77e80: ldur            w0, [x2, #0x13]
    // 0xa77e84: DecompressPointer r0
    //     0xa77e84: add             x0, x0, HEAP, lsl #32
    // 0xa77e88: cmp             w0, NULL
    // 0xa77e8c: b.ne            #0xa77e98
    // 0xa77e90: r5 = Null
    //     0xa77e90: mov             x5, NULL
    // 0xa77e94: b               #0xa77ea8
    // 0xa77e98: r17 = 311
    //     0xa77e98: movz            x17, #0x137
    // 0xa77e9c: ldr             w3, [x0, x17]
    // 0xa77ea0: DecompressPointer r3
    //     0xa77ea0: add             x3, x3, HEAP, lsl #32
    // 0xa77ea4: mov             x5, x3
    // 0xa77ea8: ldur            x4, [fp, #-8]
    // 0xa77eac: ldur            x3, [fp, #-0x10]
    // 0xa77eb0: ldur            x0, [fp, #-0x18]
    // 0xa77eb4: str             x5, [SP]
    // 0xa77eb8: r0 = _interpolateSingle()
    //     0xa77eb8: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa77ebc: mov             x2, x0
    // 0xa77ec0: ldur            x0, [fp, #-8]
    // 0xa77ec4: stur            x2, [fp, #-0x58]
    // 0xa77ec8: LoadField: r1 = r0->field_f
    //     0xa77ec8: ldur            w1, [x0, #0xf]
    // 0xa77ecc: DecompressPointer r1
    //     0xa77ecc: add             x1, x1, HEAP, lsl #32
    // 0xa77ed0: cmp             w1, NULL
    // 0xa77ed4: b.eq            #0xa782dc
    // 0xa77ed8: r0 = of()
    //     0xa77ed8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa77edc: LoadField: r1 = r0->field_87
    //     0xa77edc: ldur            w1, [x0, #0x87]
    // 0xa77ee0: DecompressPointer r1
    //     0xa77ee0: add             x1, x1, HEAP, lsl #32
    // 0xa77ee4: LoadField: r0 = r1->field_2b
    //     0xa77ee4: ldur            w0, [x1, #0x2b]
    // 0xa77ee8: DecompressPointer r0
    //     0xa77ee8: add             x0, x0, HEAP, lsl #32
    // 0xa77eec: r16 = 12.000000
    //     0xa77eec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa77ef0: ldr             x16, [x16, #0x9e8]
    // 0xa77ef4: r30 = Instance_Color
    //     0xa77ef4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa77ef8: stp             lr, x16, [SP]
    // 0xa77efc: mov             x1, x0
    // 0xa77f00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa77f00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa77f04: ldr             x4, [x4, #0xaa0]
    // 0xa77f08: r0 = copyWith()
    //     0xa77f08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa77f0c: stur            x0, [fp, #-0x60]
    // 0xa77f10: r0 = Text()
    //     0xa77f10: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa77f14: mov             x3, x0
    // 0xa77f18: ldur            x0, [fp, #-0x58]
    // 0xa77f1c: stur            x3, [fp, #-0x68]
    // 0xa77f20: StoreField: r3->field_b = r0
    //     0xa77f20: stur            w0, [x3, #0xb]
    // 0xa77f24: ldur            x0, [fp, #-0x60]
    // 0xa77f28: StoreField: r3->field_13 = r0
    //     0xa77f28: stur            w0, [x3, #0x13]
    // 0xa77f2c: r1 = Function '<anonymous closure>':.
    //     0xa77f2c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52e58] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xa77f30: ldr             x1, [x1, #0xe58]
    // 0xa77f34: r2 = Null
    //     0xa77f34: mov             x2, NULL
    // 0xa77f38: r0 = AllocateClosure()
    //     0xa77f38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa77f3c: stur            x0, [fp, #-0x58]
    // 0xa77f40: r0 = TextButton()
    //     0xa77f40: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa77f44: mov             x1, x0
    // 0xa77f48: ldur            x0, [fp, #-0x58]
    // 0xa77f4c: stur            x1, [fp, #-0x60]
    // 0xa77f50: StoreField: r1->field_b = r0
    //     0xa77f50: stur            w0, [x1, #0xb]
    // 0xa77f54: r0 = false
    //     0xa77f54: add             x0, NULL, #0x30  ; false
    // 0xa77f58: StoreField: r1->field_27 = r0
    //     0xa77f58: stur            w0, [x1, #0x27]
    // 0xa77f5c: r2 = true
    //     0xa77f5c: add             x2, NULL, #0x20  ; true
    // 0xa77f60: StoreField: r1->field_2f = r2
    //     0xa77f60: stur            w2, [x1, #0x2f]
    // 0xa77f64: ldur            x3, [fp, #-0x68]
    // 0xa77f68: StoreField: r1->field_37 = r3
    //     0xa77f68: stur            w3, [x1, #0x37]
    // 0xa77f6c: r0 = TextButtonTheme()
    //     0xa77f6c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa77f70: mov             x1, x0
    // 0xa77f74: ldur            x0, [fp, #-0x40]
    // 0xa77f78: stur            x1, [fp, #-0x58]
    // 0xa77f7c: StoreField: r1->field_f = r0
    //     0xa77f7c: stur            w0, [x1, #0xf]
    // 0xa77f80: ldur            x0, [fp, #-0x60]
    // 0xa77f84: StoreField: r1->field_b = r0
    //     0xa77f84: stur            w0, [x1, #0xb]
    // 0xa77f88: r0 = Padding()
    //     0xa77f88: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa77f8c: mov             x1, x0
    // 0xa77f90: ldur            x0, [fp, #-0x18]
    // 0xa77f94: stur            x1, [fp, #-0x40]
    // 0xa77f98: StoreField: r1->field_f = r0
    //     0xa77f98: stur            w0, [x1, #0xf]
    // 0xa77f9c: ldur            x0, [fp, #-0x58]
    // 0xa77fa0: StoreField: r1->field_b = r0
    //     0xa77fa0: stur            w0, [x1, #0xb]
    // 0xa77fa4: r0 = Visibility()
    //     0xa77fa4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa77fa8: mov             x2, x0
    // 0xa77fac: ldur            x0, [fp, #-0x40]
    // 0xa77fb0: stur            x2, [fp, #-0x18]
    // 0xa77fb4: StoreField: r2->field_b = r0
    //     0xa77fb4: stur            w0, [x2, #0xb]
    // 0xa77fb8: r0 = Instance_SizedBox
    //     0xa77fb8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa77fbc: StoreField: r2->field_f = r0
    //     0xa77fbc: stur            w0, [x2, #0xf]
    // 0xa77fc0: ldur            x0, [fp, #-0x10]
    // 0xa77fc4: StoreField: r2->field_13 = r0
    //     0xa77fc4: stur            w0, [x2, #0x13]
    // 0xa77fc8: r0 = false
    //     0xa77fc8: add             x0, NULL, #0x30  ; false
    // 0xa77fcc: ArrayStore: r2[0] = r0  ; List_4
    //     0xa77fcc: stur            w0, [x2, #0x17]
    // 0xa77fd0: StoreField: r2->field_1b = r0
    //     0xa77fd0: stur            w0, [x2, #0x1b]
    // 0xa77fd4: StoreField: r2->field_1f = r0
    //     0xa77fd4: stur            w0, [x2, #0x1f]
    // 0xa77fd8: StoreField: r2->field_23 = r0
    //     0xa77fd8: stur            w0, [x2, #0x23]
    // 0xa77fdc: StoreField: r2->field_27 = r0
    //     0xa77fdc: stur            w0, [x2, #0x27]
    // 0xa77fe0: StoreField: r2->field_2b = r0
    //     0xa77fe0: stur            w0, [x2, #0x2b]
    // 0xa77fe4: ldur            x3, [fp, #-0x28]
    // 0xa77fe8: LoadField: r1 = r3->field_13
    //     0xa77fe8: ldur            w1, [x3, #0x13]
    // 0xa77fec: DecompressPointer r1
    //     0xa77fec: add             x1, x1, HEAP, lsl #32
    // 0xa77ff0: cmp             w1, NULL
    // 0xa77ff4: b.ne            #0xa78000
    // 0xa77ff8: r1 = Null
    //     0xa77ff8: mov             x1, NULL
    // 0xa77ffc: b               #0xa78010
    // 0xa78000: r17 = 263
    //     0xa78000: movz            x17, #0x107
    // 0xa78004: ldr             w4, [x1, x17]
    // 0xa78008: DecompressPointer r4
    //     0xa78008: add             x4, x4, HEAP, lsl #32
    // 0xa7800c: mov             x1, x4
    // 0xa78010: cmp             w1, NULL
    // 0xa78014: b.eq            #0xa78138
    // 0xa78018: tbnz            w1, #4, #0xa78138
    // 0xa7801c: ldur            x4, [fp, #-8]
    // 0xa78020: LoadField: r1 = r4->field_f
    //     0xa78020: ldur            w1, [x4, #0xf]
    // 0xa78024: DecompressPointer r1
    //     0xa78024: add             x1, x1, HEAP, lsl #32
    // 0xa78028: cmp             w1, NULL
    // 0xa7802c: b.eq            #0xa782e0
    // 0xa78030: r0 = of()
    //     0xa78030: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa78034: r17 = 307
    //     0xa78034: movz            x17, #0x133
    // 0xa78038: ldr             w2, [x0, x17]
    // 0xa7803c: DecompressPointer r2
    //     0xa7803c: add             x2, x2, HEAP, lsl #32
    // 0xa78040: ldur            x0, [fp, #-8]
    // 0xa78044: stur            x2, [fp, #-0x10]
    // 0xa78048: LoadField: r1 = r0->field_f
    //     0xa78048: ldur            w1, [x0, #0xf]
    // 0xa7804c: DecompressPointer r1
    //     0xa7804c: add             x1, x1, HEAP, lsl #32
    // 0xa78050: cmp             w1, NULL
    // 0xa78054: b.eq            #0xa782e4
    // 0xa78058: r0 = of()
    //     0xa78058: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa7805c: LoadField: r1 = r0->field_87
    //     0xa7805c: ldur            w1, [x0, #0x87]
    // 0xa78060: DecompressPointer r1
    //     0xa78060: add             x1, x1, HEAP, lsl #32
    // 0xa78064: LoadField: r0 = r1->field_2b
    //     0xa78064: ldur            w0, [x1, #0x2b]
    // 0xa78068: DecompressPointer r0
    //     0xa78068: add             x0, x0, HEAP, lsl #32
    // 0xa7806c: r16 = 12.000000
    //     0xa7806c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa78070: ldr             x16, [x16, #0x9e8]
    // 0xa78074: r30 = Instance_Color
    //     0xa78074: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa78078: stp             lr, x16, [SP]
    // 0xa7807c: mov             x1, x0
    // 0xa78080: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa78080: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa78084: ldr             x4, [x4, #0xaa0]
    // 0xa78088: r0 = copyWith()
    //     0xa78088: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa7808c: stur            x0, [fp, #-8]
    // 0xa78090: r0 = Text()
    //     0xa78090: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa78094: mov             x3, x0
    // 0xa78098: r0 = "Customisable"
    //     0xa78098: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xa7809c: ldr             x0, [x0, #0x970]
    // 0xa780a0: stur            x3, [fp, #-0x40]
    // 0xa780a4: StoreField: r3->field_b = r0
    //     0xa780a4: stur            w0, [x3, #0xb]
    // 0xa780a8: ldur            x0, [fp, #-8]
    // 0xa780ac: StoreField: r3->field_13 = r0
    //     0xa780ac: stur            w0, [x3, #0x13]
    // 0xa780b0: r0 = Instance_TextAlign
    //     0xa780b0: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xa780b4: StoreField: r3->field_1b = r0
    //     0xa780b4: stur            w0, [x3, #0x1b]
    // 0xa780b8: r1 = Function '<anonymous closure>':.
    //     0xa780b8: add             x1, PP, #0x52, lsl #12  ; [pp+0x52e60] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xa780bc: ldr             x1, [x1, #0xe60]
    // 0xa780c0: r2 = Null
    //     0xa780c0: mov             x2, NULL
    // 0xa780c4: r0 = AllocateClosure()
    //     0xa780c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa780c8: stur            x0, [fp, #-8]
    // 0xa780cc: r0 = TextButton()
    //     0xa780cc: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa780d0: mov             x1, x0
    // 0xa780d4: ldur            x0, [fp, #-8]
    // 0xa780d8: stur            x1, [fp, #-0x58]
    // 0xa780dc: StoreField: r1->field_b = r0
    //     0xa780dc: stur            w0, [x1, #0xb]
    // 0xa780e0: r0 = false
    //     0xa780e0: add             x0, NULL, #0x30  ; false
    // 0xa780e4: StoreField: r1->field_27 = r0
    //     0xa780e4: stur            w0, [x1, #0x27]
    // 0xa780e8: r2 = true
    //     0xa780e8: add             x2, NULL, #0x20  ; true
    // 0xa780ec: StoreField: r1->field_2f = r2
    //     0xa780ec: stur            w2, [x1, #0x2f]
    // 0xa780f0: ldur            x3, [fp, #-0x40]
    // 0xa780f4: StoreField: r1->field_37 = r3
    //     0xa780f4: stur            w3, [x1, #0x37]
    // 0xa780f8: r0 = TextButtonTheme()
    //     0xa780f8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa780fc: mov             x1, x0
    // 0xa78100: ldur            x0, [fp, #-0x10]
    // 0xa78104: stur            x1, [fp, #-8]
    // 0xa78108: StoreField: r1->field_f = r0
    //     0xa78108: stur            w0, [x1, #0xf]
    // 0xa7810c: ldur            x0, [fp, #-0x58]
    // 0xa78110: StoreField: r1->field_b = r0
    //     0xa78110: stur            w0, [x1, #0xb]
    // 0xa78114: r0 = SizedBox()
    //     0xa78114: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa78118: mov             x1, x0
    // 0xa7811c: r0 = 35.000000
    //     0xa7811c: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0xa78120: ldr             x0, [x0, #0x2b0]
    // 0xa78124: StoreField: r1->field_13 = r0
    //     0xa78124: stur            w0, [x1, #0x13]
    // 0xa78128: ldur            x0, [fp, #-8]
    // 0xa7812c: StoreField: r1->field_b = r0
    //     0xa7812c: stur            w0, [x1, #0xb]
    // 0xa78130: mov             x3, x1
    // 0xa78134: b               #0xa78150
    // 0xa78138: r0 = Container()
    //     0xa78138: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa7813c: mov             x1, x0
    // 0xa78140: stur            x0, [fp, #-8]
    // 0xa78144: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa78144: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa78148: r0 = Container()
    //     0xa78148: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa7814c: ldur            x3, [fp, #-8]
    // 0xa78150: ldur            x2, [fp, #-0x38]
    // 0xa78154: ldur            x1, [fp, #-0x30]
    // 0xa78158: ldur            x0, [fp, #-0x18]
    // 0xa7815c: stur            x3, [fp, #-8]
    // 0xa78160: r0 = Padding()
    //     0xa78160: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa78164: mov             x3, x0
    // 0xa78168: r0 = Instance_EdgeInsets
    //     0xa78168: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e68] Obj!EdgeInsets@d58461
    //     0xa7816c: ldr             x0, [x0, #0xe68]
    // 0xa78170: stur            x3, [fp, #-0x10]
    // 0xa78174: StoreField: r3->field_f = r0
    //     0xa78174: stur            w0, [x3, #0xf]
    // 0xa78178: ldur            x0, [fp, #-8]
    // 0xa7817c: StoreField: r3->field_b = r0
    //     0xa7817c: stur            w0, [x3, #0xb]
    // 0xa78180: r1 = Null
    //     0xa78180: mov             x1, NULL
    // 0xa78184: r2 = 8
    //     0xa78184: movz            x2, #0x8
    // 0xa78188: r0 = AllocateArray()
    //     0xa78188: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa7818c: mov             x2, x0
    // 0xa78190: ldur            x0, [fp, #-0x38]
    // 0xa78194: stur            x2, [fp, #-8]
    // 0xa78198: StoreField: r2->field_f = r0
    //     0xa78198: stur            w0, [x2, #0xf]
    // 0xa7819c: ldur            x0, [fp, #-0x30]
    // 0xa781a0: StoreField: r2->field_13 = r0
    //     0xa781a0: stur            w0, [x2, #0x13]
    // 0xa781a4: ldur            x0, [fp, #-0x18]
    // 0xa781a8: ArrayStore: r2[0] = r0  ; List_4
    //     0xa781a8: stur            w0, [x2, #0x17]
    // 0xa781ac: ldur            x0, [fp, #-0x10]
    // 0xa781b0: StoreField: r2->field_1b = r0
    //     0xa781b0: stur            w0, [x2, #0x1b]
    // 0xa781b4: r1 = <Widget>
    //     0xa781b4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa781b8: r0 = AllocateGrowableArray()
    //     0xa781b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa781bc: mov             x1, x0
    // 0xa781c0: ldur            x0, [fp, #-8]
    // 0xa781c4: stur            x1, [fp, #-0x10]
    // 0xa781c8: StoreField: r1->field_f = r0
    //     0xa781c8: stur            w0, [x1, #0xf]
    // 0xa781cc: r0 = 8
    //     0xa781cc: movz            x0, #0x8
    // 0xa781d0: StoreField: r1->field_b = r0
    //     0xa781d0: stur            w0, [x1, #0xb]
    // 0xa781d4: r0 = Stack()
    //     0xa781d4: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa781d8: mov             x1, x0
    // 0xa781dc: r0 = Instance_Alignment
    //     0xa781dc: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xa781e0: ldr             x0, [x0, #0x5b8]
    // 0xa781e4: stur            x1, [fp, #-8]
    // 0xa781e8: StoreField: r1->field_f = r0
    //     0xa781e8: stur            w0, [x1, #0xf]
    // 0xa781ec: r0 = Instance_StackFit
    //     0xa781ec: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa781f0: ldr             x0, [x0, #0xfa8]
    // 0xa781f4: ArrayStore: r1[0] = r0  ; List_4
    //     0xa781f4: stur            w0, [x1, #0x17]
    // 0xa781f8: r0 = Instance_Clip
    //     0xa781f8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa781fc: ldr             x0, [x0, #0x7e0]
    // 0xa78200: StoreField: r1->field_1b = r0
    //     0xa78200: stur            w0, [x1, #0x1b]
    // 0xa78204: ldur            x0, [fp, #-0x10]
    // 0xa78208: StoreField: r1->field_b = r0
    //     0xa78208: stur            w0, [x1, #0xb]
    // 0xa7820c: r0 = InkWell()
    //     0xa7820c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa78210: mov             x3, x0
    // 0xa78214: ldur            x0, [fp, #-8]
    // 0xa78218: stur            x3, [fp, #-0x10]
    // 0xa7821c: StoreField: r3->field_b = r0
    //     0xa7821c: stur            w0, [x3, #0xb]
    // 0xa78220: ldur            x2, [fp, #-0x28]
    // 0xa78224: r1 = Function '<anonymous closure>':.
    //     0xa78224: add             x1, PP, #0x52, lsl #12  ; [pp+0x52e70] AnonymousClosure: (0xa782e8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::imageSlider (0xa773b8)
    //     0xa78228: ldr             x1, [x1, #0xe70]
    // 0xa7822c: r0 = AllocateClosure()
    //     0xa7822c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa78230: ldur            x2, [fp, #-0x10]
    // 0xa78234: StoreField: r2->field_f = r0
    //     0xa78234: stur            w0, [x2, #0xf]
    // 0xa78238: r0 = true
    //     0xa78238: add             x0, NULL, #0x20  ; true
    // 0xa7823c: StoreField: r2->field_43 = r0
    //     0xa7823c: stur            w0, [x2, #0x43]
    // 0xa78240: r1 = Instance_BoxShape
    //     0xa78240: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa78244: ldr             x1, [x1, #0x80]
    // 0xa78248: StoreField: r2->field_47 = r1
    //     0xa78248: stur            w1, [x2, #0x47]
    // 0xa7824c: StoreField: r2->field_6f = r0
    //     0xa7824c: stur            w0, [x2, #0x6f]
    // 0xa78250: r1 = false
    //     0xa78250: add             x1, NULL, #0x30  ; false
    // 0xa78254: StoreField: r2->field_73 = r1
    //     0xa78254: stur            w1, [x2, #0x73]
    // 0xa78258: StoreField: r2->field_83 = r0
    //     0xa78258: stur            w0, [x2, #0x83]
    // 0xa7825c: StoreField: r2->field_7b = r1
    //     0xa7825c: stur            w1, [x2, #0x7b]
    // 0xa78260: r0 = AnimatedContainer()
    //     0xa78260: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xa78264: stur            x0, [fp, #-8]
    // 0xa78268: r16 = Instance_Cubic
    //     0xa78268: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xa7826c: ldr             x16, [x16, #0xaf8]
    // 0xa78270: str             x16, [SP]
    // 0xa78274: mov             x1, x0
    // 0xa78278: ldur            x2, [fp, #-0x10]
    // 0xa7827c: r3 = Instance_Duration
    //     0xa7827c: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xa78280: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xa78280: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xa78284: ldr             x4, [x4, #0xbc8]
    // 0xa78288: r0 = AnimatedContainer()
    //     0xa78288: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xa7828c: ldur            x0, [fp, #-8]
    // 0xa78290: LeaveFrame
    //     0xa78290: mov             SP, fp
    //     0xa78294: ldp             fp, lr, [SP], #0x10
    // 0xa78298: ret
    //     0xa78298: ret             
    // 0xa7829c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7829c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa782a0: b               #0xa773e8
    // 0xa782a4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa782a4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa782a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa782a8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa782ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa782ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa782b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa782b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa782b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa782b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa782b8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa782b8: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xa782bc: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa782bc: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xa782c0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa782c0: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xa782c4: SaveReg d0
    //     0xa782c4: str             q0, [SP, #-0x10]!
    // 0xa782c8: SaveReg r1
    //     0xa782c8: str             x1, [SP, #-8]!
    // 0xa782cc: r0 = AllocateDouble()
    //     0xa782cc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa782d0: RestoreReg r1
    //     0xa782d0: ldr             x1, [SP], #8
    // 0xa782d4: RestoreReg d0
    //     0xa782d4: ldr             q0, [SP], #0x10
    // 0xa782d8: b               #0xa77c64
    // 0xa782dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa782dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa782e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa782e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa782e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa782e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa782e8, size: 0xe8
    // 0xa782e8: EnterFrame
    //     0xa782e8: stp             fp, lr, [SP, #-0x10]!
    //     0xa782ec: mov             fp, SP
    // 0xa782f0: AllocStack(0x38)
    //     0xa782f0: sub             SP, SP, #0x38
    // 0xa782f4: SetupParameters()
    //     0xa782f4: ldr             x0, [fp, #0x10]
    //     0xa782f8: ldur            w1, [x0, #0x17]
    //     0xa782fc: add             x1, x1, HEAP, lsl #32
    // 0xa78300: CheckStackOverflow
    //     0xa78300: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa78304: cmp             SP, x16
    //     0xa78308: b.ls            #0xa783c4
    // 0xa7830c: LoadField: r0 = r1->field_f
    //     0xa7830c: ldur            w0, [x1, #0xf]
    // 0xa78310: DecompressPointer r0
    //     0xa78310: add             x0, x0, HEAP, lsl #32
    // 0xa78314: LoadField: r2 = r0->field_b
    //     0xa78314: ldur            w2, [x0, #0xb]
    // 0xa78318: DecompressPointer r2
    //     0xa78318: add             x2, x2, HEAP, lsl #32
    // 0xa7831c: cmp             w2, NULL
    // 0xa78320: b.eq            #0xa783cc
    // 0xa78324: LoadField: r0 = r2->field_23
    //     0xa78324: ldur            w0, [x2, #0x23]
    // 0xa78328: DecompressPointer r0
    //     0xa78328: add             x0, x0, HEAP, lsl #32
    // 0xa7832c: cmp             w0, NULL
    // 0xa78330: b.ne            #0xa78338
    // 0xa78334: r0 = ""
    //     0xa78334: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa78338: LoadField: r3 = r2->field_1f
    //     0xa78338: ldur            w3, [x2, #0x1f]
    // 0xa7833c: DecompressPointer r3
    //     0xa7833c: add             x3, x3, HEAP, lsl #32
    // 0xa78340: cmp             w3, NULL
    // 0xa78344: b.ne            #0xa7834c
    // 0xa78348: r3 = ""
    //     0xa78348: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa7834c: LoadField: r4 = r2->field_27
    //     0xa7834c: ldur            w4, [x2, #0x27]
    // 0xa78350: DecompressPointer r4
    //     0xa78350: add             x4, x4, HEAP, lsl #32
    // 0xa78354: cmp             w4, NULL
    // 0xa78358: b.ne            #0xa78360
    // 0xa7835c: r4 = ""
    //     0xa7835c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa78360: LoadField: r5 = r2->field_f
    //     0xa78360: ldur            w5, [x2, #0xf]
    // 0xa78364: DecompressPointer r5
    //     0xa78364: add             x5, x5, HEAP, lsl #32
    // 0xa78368: cmp             w5, NULL
    // 0xa7836c: b.ne            #0xa78374
    // 0xa78370: r5 = ""
    //     0xa78370: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa78374: LoadField: r6 = r1->field_13
    //     0xa78374: ldur            w6, [x1, #0x13]
    // 0xa78378: DecompressPointer r6
    //     0xa78378: add             x6, x6, HEAP, lsl #32
    // 0xa7837c: LoadField: r1 = r2->field_3f
    //     0xa7837c: ldur            w1, [x2, #0x3f]
    // 0xa78380: DecompressPointer r1
    //     0xa78380: add             x1, x1, HEAP, lsl #32
    // 0xa78384: stp             x0, x1, [SP, #0x28]
    // 0xa78388: r16 = "product_page"
    //     0xa78388: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa7838c: ldr             x16, [x16, #0x480]
    // 0xa78390: stp             x16, x3, [SP, #0x18]
    // 0xa78394: stp             x5, x4, [SP, #8]
    // 0xa78398: str             x6, [SP]
    // 0xa7839c: r4 = 0
    //     0xa7839c: movz            x4, #0
    // 0xa783a0: ldr             x0, [SP, #0x30]
    // 0xa783a4: r16 = UnlinkedCall_0x613b5c
    //     0xa783a4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52e78] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa783a8: add             x16, x16, #0xe78
    // 0xa783ac: ldp             x5, lr, [x16]
    // 0xa783b0: blr             lr
    // 0xa783b4: r0 = Null
    //     0xa783b4: mov             x0, NULL
    // 0xa783b8: LeaveFrame
    //     0xa783b8: mov             SP, fp
    //     0xa783bc: ldp             fp, lr, [SP], #0x10
    // 0xa783c0: ret
    //     0xa783c0: ret             
    // 0xa783c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa783c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa783c8: b               #0xa7830c
    // 0xa783cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa783cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbfdac0, size: 0x131c
    // 0xbfdac0: EnterFrame
    //     0xbfdac0: stp             fp, lr, [SP, #-0x10]!
    //     0xbfdac4: mov             fp, SP
    // 0xbfdac8: AllocStack(0xa8)
    //     0xbfdac8: sub             SP, SP, #0xa8
    // 0xbfdacc: SetupParameters(_GroupCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbfdacc: mov             x0, x1
    //     0xbfdad0: stur            x1, [fp, #-8]
    //     0xbfdad4: mov             x1, x2
    //     0xbfdad8: stur            x2, [fp, #-0x10]
    // 0xbfdadc: CheckStackOverflow
    //     0xbfdadc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfdae0: cmp             SP, x16
    //     0xbfdae4: b.ls            #0xbfed7c
    // 0xbfdae8: r1 = 1
    //     0xbfdae8: movz            x1, #0x1
    // 0xbfdaec: r0 = AllocateContext()
    //     0xbfdaec: bl              #0x16f6108  ; AllocateContextStub
    // 0xbfdaf0: mov             x3, x0
    // 0xbfdaf4: ldur            x0, [fp, #-8]
    // 0xbfdaf8: stur            x3, [fp, #-0x20]
    // 0xbfdafc: StoreField: r3->field_f = r0
    //     0xbfdafc: stur            w0, [x3, #0xf]
    // 0xbfdb00: LoadField: r1 = r0->field_b
    //     0xbfdb00: ldur            w1, [x0, #0xb]
    // 0xbfdb04: DecompressPointer r1
    //     0xbfdb04: add             x1, x1, HEAP, lsl #32
    // 0xbfdb08: cmp             w1, NULL
    // 0xbfdb0c: b.eq            #0xbfed84
    // 0xbfdb10: LoadField: r2 = r1->field_1b
    //     0xbfdb10: ldur            w2, [x1, #0x1b]
    // 0xbfdb14: DecompressPointer r2
    //     0xbfdb14: add             x2, x2, HEAP, lsl #32
    // 0xbfdb18: LoadField: r1 = r2->field_7
    //     0xbfdb18: ldur            w1, [x2, #7]
    // 0xbfdb1c: DecompressPointer r1
    //     0xbfdb1c: add             x1, x1, HEAP, lsl #32
    // 0xbfdb20: cmp             w1, NULL
    // 0xbfdb24: b.ne            #0xbfdb30
    // 0xbfdb28: r1 = Instance_TitleAlignment
    //     0xbfdb28: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbfdb2c: ldr             x1, [x1, #0x518]
    // 0xbfdb30: r16 = Instance_TitleAlignment
    //     0xbfdb30: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xbfdb34: ldr             x16, [x16, #0x520]
    // 0xbfdb38: cmp             w1, w16
    // 0xbfdb3c: b.ne            #0xbfdb4c
    // 0xbfdb40: r4 = Instance_CrossAxisAlignment
    //     0xbfdb40: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xbfdb44: ldr             x4, [x4, #0xc68]
    // 0xbfdb48: b               #0xbfdb70
    // 0xbfdb4c: r16 = Instance_TitleAlignment
    //     0xbfdb4c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbfdb50: ldr             x16, [x16, #0x518]
    // 0xbfdb54: cmp             w1, w16
    // 0xbfdb58: b.ne            #0xbfdb68
    // 0xbfdb5c: r4 = Instance_CrossAxisAlignment
    //     0xbfdb5c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbfdb60: ldr             x4, [x4, #0x890]
    // 0xbfdb64: b               #0xbfdb70
    // 0xbfdb68: r4 = Instance_CrossAxisAlignment
    //     0xbfdb68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbfdb6c: ldr             x4, [x4, #0xa18]
    // 0xbfdb70: stur            x4, [fp, #-0x18]
    // 0xbfdb74: r1 = <Widget>
    //     0xbfdb74: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfdb78: r2 = 0
    //     0xbfdb78: movz            x2, #0
    // 0xbfdb7c: r0 = _GrowableList()
    //     0xbfdb7c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xbfdb80: mov             x2, x0
    // 0xbfdb84: ldur            x1, [fp, #-8]
    // 0xbfdb88: stur            x2, [fp, #-0x28]
    // 0xbfdb8c: LoadField: r0 = r1->field_b
    //     0xbfdb8c: ldur            w0, [x1, #0xb]
    // 0xbfdb90: DecompressPointer r0
    //     0xbfdb90: add             x0, x0, HEAP, lsl #32
    // 0xbfdb94: cmp             w0, NULL
    // 0xbfdb98: b.eq            #0xbfed88
    // 0xbfdb9c: LoadField: r3 = r0->field_f
    //     0xbfdb9c: ldur            w3, [x0, #0xf]
    // 0xbfdba0: DecompressPointer r3
    //     0xbfdba0: add             x3, x3, HEAP, lsl #32
    // 0xbfdba4: cmp             w3, NULL
    // 0xbfdba8: b.ne            #0xbfdbb4
    // 0xbfdbac: r0 = Null
    //     0xbfdbac: mov             x0, NULL
    // 0xbfdbb0: b               #0xbfdbcc
    // 0xbfdbb4: LoadField: r0 = r3->field_7
    //     0xbfdbb4: ldur            w0, [x3, #7]
    // 0xbfdbb8: cbnz            w0, #0xbfdbc4
    // 0xbfdbbc: r4 = false
    //     0xbfdbbc: add             x4, NULL, #0x30  ; false
    // 0xbfdbc0: b               #0xbfdbc8
    // 0xbfdbc4: r4 = true
    //     0xbfdbc4: add             x4, NULL, #0x20  ; true
    // 0xbfdbc8: mov             x0, x4
    // 0xbfdbcc: cmp             w0, NULL
    // 0xbfdbd0: b.eq            #0xbfde04
    // 0xbfdbd4: tbnz            w0, #4, #0xbfde04
    // 0xbfdbd8: cmp             w3, NULL
    // 0xbfdbdc: b.ne            #0xbfdbe8
    // 0xbfdbe0: r0 = Null
    //     0xbfdbe0: mov             x0, NULL
    // 0xbfdbe4: b               #0xbfdc00
    // 0xbfdbe8: r0 = LoadClassIdInstr(r3)
    //     0xbfdbe8: ldur            x0, [x3, #-1]
    //     0xbfdbec: ubfx            x0, x0, #0xc, #0x14
    // 0xbfdbf0: str             x3, [SP]
    // 0xbfdbf4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbfdbf4: sub             lr, x0, #1, lsl #12
    //     0xbfdbf8: ldr             lr, [x21, lr, lsl #3]
    //     0xbfdbfc: blr             lr
    // 0xbfdc00: cmp             w0, NULL
    // 0xbfdc04: b.ne            #0xbfdc10
    // 0xbfdc08: r2 = ""
    //     0xbfdc08: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbfdc0c: b               #0xbfdc14
    // 0xbfdc10: mov             x2, x0
    // 0xbfdc14: ldur            x0, [fp, #-8]
    // 0xbfdc18: stur            x2, [fp, #-0x38]
    // 0xbfdc1c: LoadField: r1 = r0->field_b
    //     0xbfdc1c: ldur            w1, [x0, #0xb]
    // 0xbfdc20: DecompressPointer r1
    //     0xbfdc20: add             x1, x1, HEAP, lsl #32
    // 0xbfdc24: cmp             w1, NULL
    // 0xbfdc28: b.eq            #0xbfed8c
    // 0xbfdc2c: LoadField: r3 = r1->field_1b
    //     0xbfdc2c: ldur            w3, [x1, #0x1b]
    // 0xbfdc30: DecompressPointer r3
    //     0xbfdc30: add             x3, x3, HEAP, lsl #32
    // 0xbfdc34: LoadField: r1 = r3->field_7
    //     0xbfdc34: ldur            w1, [x3, #7]
    // 0xbfdc38: DecompressPointer r1
    //     0xbfdc38: add             x1, x1, HEAP, lsl #32
    // 0xbfdc3c: cmp             w1, NULL
    // 0xbfdc40: b.ne            #0xbfdc4c
    // 0xbfdc44: r1 = Instance_TitleAlignment
    //     0xbfdc44: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbfdc48: ldr             x1, [x1, #0x518]
    // 0xbfdc4c: r16 = Instance_TitleAlignment
    //     0xbfdc4c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xbfdc50: ldr             x16, [x16, #0x520]
    // 0xbfdc54: cmp             w1, w16
    // 0xbfdc58: b.ne            #0xbfdc64
    // 0xbfdc5c: r4 = Instance_TextAlign
    //     0xbfdc5c: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xbfdc60: b               #0xbfdc80
    // 0xbfdc64: r16 = Instance_TitleAlignment
    //     0xbfdc64: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbfdc68: ldr             x16, [x16, #0x518]
    // 0xbfdc6c: cmp             w1, w16
    // 0xbfdc70: b.ne            #0xbfdc7c
    // 0xbfdc74: r4 = Instance_TextAlign
    //     0xbfdc74: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xbfdc78: b               #0xbfdc80
    // 0xbfdc7c: r4 = Instance_TextAlign
    //     0xbfdc7c: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xbfdc80: ldur            x3, [fp, #-0x28]
    // 0xbfdc84: ldur            x1, [fp, #-0x10]
    // 0xbfdc88: stur            x4, [fp, #-0x30]
    // 0xbfdc8c: r0 = of()
    //     0xbfdc8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfdc90: LoadField: r1 = r0->field_87
    //     0xbfdc90: ldur            w1, [x0, #0x87]
    // 0xbfdc94: DecompressPointer r1
    //     0xbfdc94: add             x1, x1, HEAP, lsl #32
    // 0xbfdc98: LoadField: r0 = r1->field_1f
    //     0xbfdc98: ldur            w0, [x1, #0x1f]
    // 0xbfdc9c: DecompressPointer r0
    //     0xbfdc9c: add             x0, x0, HEAP, lsl #32
    // 0xbfdca0: r16 = 21.000000
    //     0xbfdca0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xbfdca4: ldr             x16, [x16, #0x9b0]
    // 0xbfdca8: r30 = Instance_Color
    //     0xbfdca8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfdcac: stp             lr, x16, [SP]
    // 0xbfdcb0: mov             x1, x0
    // 0xbfdcb4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbfdcb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbfdcb8: ldr             x4, [x4, #0xaa0]
    // 0xbfdcbc: r0 = copyWith()
    //     0xbfdcbc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfdcc0: stur            x0, [fp, #-0x40]
    // 0xbfdcc4: r0 = Text()
    //     0xbfdcc4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfdcc8: mov             x3, x0
    // 0xbfdccc: ldur            x0, [fp, #-0x38]
    // 0xbfdcd0: stur            x3, [fp, #-0x48]
    // 0xbfdcd4: StoreField: r3->field_b = r0
    //     0xbfdcd4: stur            w0, [x3, #0xb]
    // 0xbfdcd8: ldur            x0, [fp, #-0x40]
    // 0xbfdcdc: StoreField: r3->field_13 = r0
    //     0xbfdcdc: stur            w0, [x3, #0x13]
    // 0xbfdce0: ldur            x0, [fp, #-0x30]
    // 0xbfdce4: StoreField: r3->field_1b = r0
    //     0xbfdce4: stur            w0, [x3, #0x1b]
    // 0xbfdce8: r1 = Null
    //     0xbfdce8: mov             x1, NULL
    // 0xbfdcec: r2 = 4
    //     0xbfdcec: movz            x2, #0x4
    // 0xbfdcf0: r0 = AllocateArray()
    //     0xbfdcf0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfdcf4: mov             x2, x0
    // 0xbfdcf8: ldur            x0, [fp, #-0x48]
    // 0xbfdcfc: stur            x2, [fp, #-0x30]
    // 0xbfdd00: StoreField: r2->field_f = r0
    //     0xbfdd00: stur            w0, [x2, #0xf]
    // 0xbfdd04: r16 = Instance_SizedBox
    //     0xbfdd04: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xbfdd08: ldr             x16, [x16, #0xc70]
    // 0xbfdd0c: StoreField: r2->field_13 = r16
    //     0xbfdd0c: stur            w16, [x2, #0x13]
    // 0xbfdd10: r1 = <Widget>
    //     0xbfdd10: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfdd14: r0 = AllocateGrowableArray()
    //     0xbfdd14: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfdd18: mov             x1, x0
    // 0xbfdd1c: ldur            x0, [fp, #-0x30]
    // 0xbfdd20: stur            x1, [fp, #-0x38]
    // 0xbfdd24: StoreField: r1->field_f = r0
    //     0xbfdd24: stur            w0, [x1, #0xf]
    // 0xbfdd28: r2 = 4
    //     0xbfdd28: movz            x2, #0x4
    // 0xbfdd2c: StoreField: r1->field_b = r2
    //     0xbfdd2c: stur            w2, [x1, #0xb]
    // 0xbfdd30: r0 = Column()
    //     0xbfdd30: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbfdd34: mov             x2, x0
    // 0xbfdd38: r0 = Instance_Axis
    //     0xbfdd38: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbfdd3c: stur            x2, [fp, #-0x30]
    // 0xbfdd40: StoreField: r2->field_f = r0
    //     0xbfdd40: stur            w0, [x2, #0xf]
    // 0xbfdd44: r3 = Instance_MainAxisAlignment
    //     0xbfdd44: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfdd48: ldr             x3, [x3, #0xa08]
    // 0xbfdd4c: StoreField: r2->field_13 = r3
    //     0xbfdd4c: stur            w3, [x2, #0x13]
    // 0xbfdd50: r4 = Instance_MainAxisSize
    //     0xbfdd50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfdd54: ldr             x4, [x4, #0xa10]
    // 0xbfdd58: ArrayStore: r2[0] = r4  ; List_4
    //     0xbfdd58: stur            w4, [x2, #0x17]
    // 0xbfdd5c: r1 = Instance_CrossAxisAlignment
    //     0xbfdd5c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbfdd60: ldr             x1, [x1, #0x890]
    // 0xbfdd64: StoreField: r2->field_1b = r1
    //     0xbfdd64: stur            w1, [x2, #0x1b]
    // 0xbfdd68: r5 = Instance_VerticalDirection
    //     0xbfdd68: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfdd6c: ldr             x5, [x5, #0xa20]
    // 0xbfdd70: StoreField: r2->field_23 = r5
    //     0xbfdd70: stur            w5, [x2, #0x23]
    // 0xbfdd74: r6 = Instance_Clip
    //     0xbfdd74: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfdd78: ldr             x6, [x6, #0x38]
    // 0xbfdd7c: StoreField: r2->field_2b = r6
    //     0xbfdd7c: stur            w6, [x2, #0x2b]
    // 0xbfdd80: StoreField: r2->field_2f = rZR
    //     0xbfdd80: stur            xzr, [x2, #0x2f]
    // 0xbfdd84: ldur            x1, [fp, #-0x38]
    // 0xbfdd88: StoreField: r2->field_b = r1
    //     0xbfdd88: stur            w1, [x2, #0xb]
    // 0xbfdd8c: ldur            x7, [fp, #-0x28]
    // 0xbfdd90: LoadField: r1 = r7->field_b
    //     0xbfdd90: ldur            w1, [x7, #0xb]
    // 0xbfdd94: LoadField: r8 = r7->field_f
    //     0xbfdd94: ldur            w8, [x7, #0xf]
    // 0xbfdd98: DecompressPointer r8
    //     0xbfdd98: add             x8, x8, HEAP, lsl #32
    // 0xbfdd9c: LoadField: r9 = r8->field_b
    //     0xbfdd9c: ldur            w9, [x8, #0xb]
    // 0xbfdda0: r8 = LoadInt32Instr(r1)
    //     0xbfdda0: sbfx            x8, x1, #1, #0x1f
    // 0xbfdda4: stur            x8, [fp, #-0x50]
    // 0xbfdda8: r1 = LoadInt32Instr(r9)
    //     0xbfdda8: sbfx            x1, x9, #1, #0x1f
    // 0xbfddac: cmp             x8, x1
    // 0xbfddb0: b.ne            #0xbfddbc
    // 0xbfddb4: mov             x1, x7
    // 0xbfddb8: r0 = _growToNextCapacity()
    //     0xbfddb8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbfddbc: ldur            x2, [fp, #-0x28]
    // 0xbfddc0: ldur            x3, [fp, #-0x50]
    // 0xbfddc4: add             x0, x3, #1
    // 0xbfddc8: lsl             x1, x0, #1
    // 0xbfddcc: StoreField: r2->field_b = r1
    //     0xbfddcc: stur            w1, [x2, #0xb]
    // 0xbfddd0: LoadField: r1 = r2->field_f
    //     0xbfddd0: ldur            w1, [x2, #0xf]
    // 0xbfddd4: DecompressPointer r1
    //     0xbfddd4: add             x1, x1, HEAP, lsl #32
    // 0xbfddd8: ldur            x0, [fp, #-0x30]
    // 0xbfdddc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbfdddc: add             x25, x1, x3, lsl #2
    //     0xbfdde0: add             x25, x25, #0xf
    //     0xbfdde4: str             w0, [x25]
    //     0xbfdde8: tbz             w0, #0, #0xbfde04
    //     0xbfddec: ldurb           w16, [x1, #-1]
    //     0xbfddf0: ldurb           w17, [x0, #-1]
    //     0xbfddf4: and             x16, x17, x16, lsr #2
    //     0xbfddf8: tst             x16, HEAP, lsr #32
    //     0xbfddfc: b.eq            #0xbfde04
    //     0xbfde00: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbfde04: ldur            x0, [fp, #-8]
    // 0xbfde08: LoadField: r1 = r0->field_b
    //     0xbfde08: ldur            w1, [x0, #0xb]
    // 0xbfde0c: DecompressPointer r1
    //     0xbfde0c: add             x1, x1, HEAP, lsl #32
    // 0xbfde10: cmp             w1, NULL
    // 0xbfde14: b.eq            #0xbfed90
    // 0xbfde18: LoadField: r3 = r1->field_13
    //     0xbfde18: ldur            w3, [x1, #0x13]
    // 0xbfde1c: DecompressPointer r3
    //     0xbfde1c: add             x3, x3, HEAP, lsl #32
    // 0xbfde20: cmp             w3, NULL
    // 0xbfde24: b.ne            #0xbfde30
    // 0xbfde28: r1 = Null
    //     0xbfde28: mov             x1, NULL
    // 0xbfde2c: b               #0xbfde5c
    // 0xbfde30: LoadField: r1 = r3->field_7
    //     0xbfde30: ldur            w1, [x3, #7]
    // 0xbfde34: DecompressPointer r1
    //     0xbfde34: add             x1, x1, HEAP, lsl #32
    // 0xbfde38: cmp             w1, NULL
    // 0xbfde3c: b.ne            #0xbfde48
    // 0xbfde40: r1 = Null
    //     0xbfde40: mov             x1, NULL
    // 0xbfde44: b               #0xbfde5c
    // 0xbfde48: LoadField: r4 = r1->field_7
    //     0xbfde48: ldur            w4, [x1, #7]
    // 0xbfde4c: cbnz            w4, #0xbfde58
    // 0xbfde50: r1 = false
    //     0xbfde50: add             x1, NULL, #0x30  ; false
    // 0xbfde54: b               #0xbfde5c
    // 0xbfde58: r1 = true
    //     0xbfde58: add             x1, NULL, #0x20  ; true
    // 0xbfde5c: cmp             w1, NULL
    // 0xbfde60: b.ne            #0xbfde6c
    // 0xbfde64: r4 = false
    //     0xbfde64: add             x4, NULL, #0x30  ; false
    // 0xbfde68: b               #0xbfde70
    // 0xbfde6c: mov             x4, x1
    // 0xbfde70: stur            x4, [fp, #-0x38]
    // 0xbfde74: cmp             w3, NULL
    // 0xbfde78: b.ne            #0xbfde84
    // 0xbfde7c: r1 = Null
    //     0xbfde7c: mov             x1, NULL
    // 0xbfde80: b               #0xbfde8c
    // 0xbfde84: LoadField: r1 = r3->field_7
    //     0xbfde84: ldur            w1, [x3, #7]
    // 0xbfde88: DecompressPointer r1
    //     0xbfde88: add             x1, x1, HEAP, lsl #32
    // 0xbfde8c: cmp             w1, NULL
    // 0xbfde90: b.ne            #0xbfde9c
    // 0xbfde94: r3 = ""
    //     0xbfde94: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbfde98: b               #0xbfdea0
    // 0xbfde9c: mov             x3, x1
    // 0xbfdea0: ldur            x1, [fp, #-0x10]
    // 0xbfdea4: stur            x3, [fp, #-0x30]
    // 0xbfdea8: r0 = of()
    //     0xbfdea8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfdeac: LoadField: r1 = r0->field_87
    //     0xbfdeac: ldur            w1, [x0, #0x87]
    // 0xbfdeb0: DecompressPointer r1
    //     0xbfdeb0: add             x1, x1, HEAP, lsl #32
    // 0xbfdeb4: LoadField: r0 = r1->field_2b
    //     0xbfdeb4: ldur            w0, [x1, #0x2b]
    // 0xbfdeb8: DecompressPointer r0
    //     0xbfdeb8: add             x0, x0, HEAP, lsl #32
    // 0xbfdebc: stur            x0, [fp, #-0x40]
    // 0xbfdec0: r1 = Instance_Color
    //     0xbfdec0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfdec4: d0 = 0.700000
    //     0xbfdec4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbfdec8: ldr             d0, [x17, #0xf48]
    // 0xbfdecc: r0 = withOpacity()
    //     0xbfdecc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbfded0: r16 = 12.000000
    //     0xbfded0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfded4: ldr             x16, [x16, #0x9e8]
    // 0xbfded8: stp             x0, x16, [SP, #8]
    // 0xbfdedc: r16 = Instance_TextDecoration
    //     0xbfdedc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xbfdee0: ldr             x16, [x16, #0x10]
    // 0xbfdee4: str             x16, [SP]
    // 0xbfdee8: ldur            x1, [fp, #-0x40]
    // 0xbfdeec: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xbfdeec: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xbfdef0: ldr             x4, [x4, #0xe38]
    // 0xbfdef4: r0 = copyWith()
    //     0xbfdef4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfdef8: stur            x0, [fp, #-0x40]
    // 0xbfdefc: r0 = Text()
    //     0xbfdefc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfdf00: mov             x1, x0
    // 0xbfdf04: ldur            x0, [fp, #-0x30]
    // 0xbfdf08: stur            x1, [fp, #-0x48]
    // 0xbfdf0c: StoreField: r1->field_b = r0
    //     0xbfdf0c: stur            w0, [x1, #0xb]
    // 0xbfdf10: ldur            x0, [fp, #-0x40]
    // 0xbfdf14: StoreField: r1->field_13 = r0
    //     0xbfdf14: stur            w0, [x1, #0x13]
    // 0xbfdf18: r0 = InkWell()
    //     0xbfdf18: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbfdf1c: mov             x3, x0
    // 0xbfdf20: ldur            x0, [fp, #-0x48]
    // 0xbfdf24: stur            x3, [fp, #-0x30]
    // 0xbfdf28: StoreField: r3->field_b = r0
    //     0xbfdf28: stur            w0, [x3, #0xb]
    // 0xbfdf2c: ldur            x2, [fp, #-0x20]
    // 0xbfdf30: r1 = Function '<anonymous closure>':.
    //     0xbfdf30: add             x1, PP, #0x52, lsl #12  ; [pp+0x52cf8] AnonymousClosure: (0xbff330), in [package:customer_app/app/presentation/views/line/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::build (0xbfdac0)
    //     0xbfdf34: ldr             x1, [x1, #0xcf8]
    // 0xbfdf38: r0 = AllocateClosure()
    //     0xbfdf38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfdf3c: mov             x1, x0
    // 0xbfdf40: ldur            x0, [fp, #-0x30]
    // 0xbfdf44: StoreField: r0->field_f = r1
    //     0xbfdf44: stur            w1, [x0, #0xf]
    // 0xbfdf48: r1 = true
    //     0xbfdf48: add             x1, NULL, #0x20  ; true
    // 0xbfdf4c: StoreField: r0->field_43 = r1
    //     0xbfdf4c: stur            w1, [x0, #0x43]
    // 0xbfdf50: r2 = Instance_BoxShape
    //     0xbfdf50: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbfdf54: ldr             x2, [x2, #0x80]
    // 0xbfdf58: StoreField: r0->field_47 = r2
    //     0xbfdf58: stur            w2, [x0, #0x47]
    // 0xbfdf5c: StoreField: r0->field_6f = r1
    //     0xbfdf5c: stur            w1, [x0, #0x6f]
    // 0xbfdf60: r3 = false
    //     0xbfdf60: add             x3, NULL, #0x30  ; false
    // 0xbfdf64: StoreField: r0->field_73 = r3
    //     0xbfdf64: stur            w3, [x0, #0x73]
    // 0xbfdf68: StoreField: r0->field_83 = r1
    //     0xbfdf68: stur            w1, [x0, #0x83]
    // 0xbfdf6c: StoreField: r0->field_7b = r3
    //     0xbfdf6c: stur            w3, [x0, #0x7b]
    // 0xbfdf70: r0 = Visibility()
    //     0xbfdf70: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbfdf74: mov             x2, x0
    // 0xbfdf78: ldur            x0, [fp, #-0x30]
    // 0xbfdf7c: stur            x2, [fp, #-0x40]
    // 0xbfdf80: StoreField: r2->field_b = r0
    //     0xbfdf80: stur            w0, [x2, #0xb]
    // 0xbfdf84: r0 = Instance_SizedBox
    //     0xbfdf84: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbfdf88: StoreField: r2->field_f = r0
    //     0xbfdf88: stur            w0, [x2, #0xf]
    // 0xbfdf8c: ldur            x1, [fp, #-0x38]
    // 0xbfdf90: StoreField: r2->field_13 = r1
    //     0xbfdf90: stur            w1, [x2, #0x13]
    // 0xbfdf94: r3 = false
    //     0xbfdf94: add             x3, NULL, #0x30  ; false
    // 0xbfdf98: ArrayStore: r2[0] = r3  ; List_4
    //     0xbfdf98: stur            w3, [x2, #0x17]
    // 0xbfdf9c: StoreField: r2->field_1b = r3
    //     0xbfdf9c: stur            w3, [x2, #0x1b]
    // 0xbfdfa0: StoreField: r2->field_1f = r3
    //     0xbfdfa0: stur            w3, [x2, #0x1f]
    // 0xbfdfa4: StoreField: r2->field_23 = r3
    //     0xbfdfa4: stur            w3, [x2, #0x23]
    // 0xbfdfa8: StoreField: r2->field_27 = r3
    //     0xbfdfa8: stur            w3, [x2, #0x27]
    // 0xbfdfac: StoreField: r2->field_2b = r3
    //     0xbfdfac: stur            w3, [x2, #0x2b]
    // 0xbfdfb0: ldur            x4, [fp, #-0x28]
    // 0xbfdfb4: LoadField: r1 = r4->field_b
    //     0xbfdfb4: ldur            w1, [x4, #0xb]
    // 0xbfdfb8: LoadField: r5 = r4->field_f
    //     0xbfdfb8: ldur            w5, [x4, #0xf]
    // 0xbfdfbc: DecompressPointer r5
    //     0xbfdfbc: add             x5, x5, HEAP, lsl #32
    // 0xbfdfc0: LoadField: r6 = r5->field_b
    //     0xbfdfc0: ldur            w6, [x5, #0xb]
    // 0xbfdfc4: r5 = LoadInt32Instr(r1)
    //     0xbfdfc4: sbfx            x5, x1, #1, #0x1f
    // 0xbfdfc8: stur            x5, [fp, #-0x50]
    // 0xbfdfcc: r1 = LoadInt32Instr(r6)
    //     0xbfdfcc: sbfx            x1, x6, #1, #0x1f
    // 0xbfdfd0: cmp             x5, x1
    // 0xbfdfd4: b.ne            #0xbfdfe0
    // 0xbfdfd8: mov             x1, x4
    // 0xbfdfdc: r0 = _growToNextCapacity()
    //     0xbfdfdc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbfdfe0: ldur            x2, [fp, #-0x28]
    // 0xbfdfe4: ldur            x3, [fp, #-0x50]
    // 0xbfdfe8: add             x4, x3, #1
    // 0xbfdfec: stur            x4, [fp, #-0x58]
    // 0xbfdff0: lsl             x0, x4, #1
    // 0xbfdff4: StoreField: r2->field_b = r0
    //     0xbfdff4: stur            w0, [x2, #0xb]
    // 0xbfdff8: LoadField: r5 = r2->field_f
    //     0xbfdff8: ldur            w5, [x2, #0xf]
    // 0xbfdffc: DecompressPointer r5
    //     0xbfdffc: add             x5, x5, HEAP, lsl #32
    // 0xbfe000: mov             x1, x5
    // 0xbfe004: ldur            x0, [fp, #-0x40]
    // 0xbfe008: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbfe008: add             x25, x1, x3, lsl #2
    //     0xbfe00c: add             x25, x25, #0xf
    //     0xbfe010: str             w0, [x25]
    //     0xbfe014: tbz             w0, #0, #0xbfe030
    //     0xbfe018: ldurb           w16, [x1, #-1]
    //     0xbfe01c: ldurb           w17, [x0, #-1]
    //     0xbfe020: and             x16, x17, x16, lsr #2
    //     0xbfe024: tst             x16, HEAP, lsr #32
    //     0xbfe028: b.eq            #0xbfe030
    //     0xbfe02c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbfe030: LoadField: r0 = r5->field_b
    //     0xbfe030: ldur            w0, [x5, #0xb]
    // 0xbfe034: r1 = LoadInt32Instr(r0)
    //     0xbfe034: sbfx            x1, x0, #1, #0x1f
    // 0xbfe038: cmp             x4, x1
    // 0xbfe03c: b.ne            #0xbfe048
    // 0xbfe040: mov             x1, x2
    // 0xbfe044: r0 = _growToNextCapacity()
    //     0xbfe044: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbfe048: ldur            x2, [fp, #-8]
    // 0xbfe04c: ldur            x1, [fp, #-0x28]
    // 0xbfe050: ldur            x0, [fp, #-0x58]
    // 0xbfe054: add             x3, x0, #1
    // 0xbfe058: lsl             x4, x3, #1
    // 0xbfe05c: StoreField: r1->field_b = r4
    //     0xbfe05c: stur            w4, [x1, #0xb]
    // 0xbfe060: LoadField: r3 = r1->field_f
    //     0xbfe060: ldur            w3, [x1, #0xf]
    // 0xbfe064: DecompressPointer r3
    //     0xbfe064: add             x3, x3, HEAP, lsl #32
    // 0xbfe068: add             x4, x3, x0, lsl #2
    // 0xbfe06c: r16 = Instance_SizedBox
    //     0xbfe06c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xbfe070: ldr             x16, [x16, #0x9f0]
    // 0xbfe074: StoreField: r4->field_f = r16
    //     0xbfe074: stur            w16, [x4, #0xf]
    // 0xbfe078: LoadField: r0 = r2->field_b
    //     0xbfe078: ldur            w0, [x2, #0xb]
    // 0xbfe07c: DecompressPointer r0
    //     0xbfe07c: add             x0, x0, HEAP, lsl #32
    // 0xbfe080: cmp             w0, NULL
    // 0xbfe084: b.eq            #0xbfed94
    // 0xbfe088: LoadField: r3 = r0->field_2f
    //     0xbfe088: ldur            w3, [x0, #0x2f]
    // 0xbfe08c: DecompressPointer r3
    //     0xbfe08c: add             x3, x3, HEAP, lsl #32
    // 0xbfe090: LoadField: r0 = r3->field_f
    //     0xbfe090: ldur            w0, [x3, #0xf]
    // 0xbfe094: DecompressPointer r0
    //     0xbfe094: add             x0, x0, HEAP, lsl #32
    // 0xbfe098: cmp             w0, NULL
    // 0xbfe09c: r16 = true
    //     0xbfe09c: add             x16, NULL, #0x20  ; true
    // 0xbfe0a0: r17 = false
    //     0xbfe0a0: add             x17, NULL, #0x30  ; false
    // 0xbfe0a4: csel            x4, x16, x17, ne
    // 0xbfe0a8: stur            x4, [fp, #-0x38]
    // 0xbfe0ac: LoadField: r0 = r3->field_13
    //     0xbfe0ac: ldur            w0, [x3, #0x13]
    // 0xbfe0b0: DecompressPointer r0
    //     0xbfe0b0: add             x0, x0, HEAP, lsl #32
    // 0xbfe0b4: stur            x0, [fp, #-0x30]
    // 0xbfe0b8: cmp             w0, NULL
    // 0xbfe0bc: b.ne            #0xbfe0c8
    // 0xbfe0c0: r3 = Null
    //     0xbfe0c0: mov             x3, NULL
    // 0xbfe0c4: b               #0xbfe0d0
    // 0xbfe0c8: LoadField: r3 = r0->field_7
    //     0xbfe0c8: ldur            w3, [x0, #7]
    // 0xbfe0cc: DecompressPointer r3
    //     0xbfe0cc: add             x3, x3, HEAP, lsl #32
    // 0xbfe0d0: cmp             w3, NULL
    // 0xbfe0d4: b.ne            #0xbfe0e0
    // 0xbfe0d8: r3 = 0
    //     0xbfe0d8: movz            x3, #0
    // 0xbfe0dc: b               #0xbfe0f0
    // 0xbfe0e0: r5 = LoadInt32Instr(r3)
    //     0xbfe0e0: sbfx            x5, x3, #1, #0x1f
    //     0xbfe0e4: tbz             w3, #0, #0xbfe0ec
    //     0xbfe0e8: ldur            x5, [x3, #7]
    // 0xbfe0ec: mov             x3, x5
    // 0xbfe0f0: stur            x3, [fp, #-0x60]
    // 0xbfe0f4: cmp             w0, NULL
    // 0xbfe0f8: b.ne            #0xbfe104
    // 0xbfe0fc: r5 = Null
    //     0xbfe0fc: mov             x5, NULL
    // 0xbfe100: b               #0xbfe10c
    // 0xbfe104: LoadField: r5 = r0->field_b
    //     0xbfe104: ldur            w5, [x0, #0xb]
    // 0xbfe108: DecompressPointer r5
    //     0xbfe108: add             x5, x5, HEAP, lsl #32
    // 0xbfe10c: cmp             w5, NULL
    // 0xbfe110: b.ne            #0xbfe11c
    // 0xbfe114: r5 = 0
    //     0xbfe114: movz            x5, #0
    // 0xbfe118: b               #0xbfe12c
    // 0xbfe11c: r6 = LoadInt32Instr(r5)
    //     0xbfe11c: sbfx            x6, x5, #1, #0x1f
    //     0xbfe120: tbz             w5, #0, #0xbfe128
    //     0xbfe124: ldur            x6, [x5, #7]
    // 0xbfe128: mov             x5, x6
    // 0xbfe12c: stur            x5, [fp, #-0x58]
    // 0xbfe130: cmp             w0, NULL
    // 0xbfe134: b.ne            #0xbfe140
    // 0xbfe138: r6 = Null
    //     0xbfe138: mov             x6, NULL
    // 0xbfe13c: b               #0xbfe148
    // 0xbfe140: LoadField: r6 = r0->field_f
    //     0xbfe140: ldur            w6, [x0, #0xf]
    // 0xbfe144: DecompressPointer r6
    //     0xbfe144: add             x6, x6, HEAP, lsl #32
    // 0xbfe148: cmp             w6, NULL
    // 0xbfe14c: b.ne            #0xbfe158
    // 0xbfe150: r6 = 0
    //     0xbfe150: movz            x6, #0
    // 0xbfe154: b               #0xbfe168
    // 0xbfe158: r7 = LoadInt32Instr(r6)
    //     0xbfe158: sbfx            x7, x6, #1, #0x1f
    //     0xbfe15c: tbz             w6, #0, #0xbfe164
    //     0xbfe160: ldur            x7, [x6, #7]
    // 0xbfe164: mov             x6, x7
    // 0xbfe168: stur            x6, [fp, #-0x50]
    // 0xbfe16c: r0 = Color()
    //     0xbfe16c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbfe170: mov             x1, x0
    // 0xbfe174: r0 = Instance_ColorSpace
    //     0xbfe174: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbfe178: stur            x1, [fp, #-0x40]
    // 0xbfe17c: StoreField: r1->field_27 = r0
    //     0xbfe17c: stur            w0, [x1, #0x27]
    // 0xbfe180: d0 = 1.000000
    //     0xbfe180: fmov            d0, #1.00000000
    // 0xbfe184: StoreField: r1->field_7 = d0
    //     0xbfe184: stur            d0, [x1, #7]
    // 0xbfe188: ldur            x2, [fp, #-0x60]
    // 0xbfe18c: ubfx            x2, x2, #0, #0x20
    // 0xbfe190: and             w3, w2, #0xff
    // 0xbfe194: ubfx            x3, x3, #0, #0x20
    // 0xbfe198: scvtf           d1, x3
    // 0xbfe19c: d2 = 255.000000
    //     0xbfe19c: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbfe1a0: fdiv            d3, d1, d2
    // 0xbfe1a4: StoreField: r1->field_f = d3
    //     0xbfe1a4: stur            d3, [x1, #0xf]
    // 0xbfe1a8: ldur            x2, [fp, #-0x58]
    // 0xbfe1ac: ubfx            x2, x2, #0, #0x20
    // 0xbfe1b0: and             w3, w2, #0xff
    // 0xbfe1b4: ubfx            x3, x3, #0, #0x20
    // 0xbfe1b8: scvtf           d1, x3
    // 0xbfe1bc: fdiv            d3, d1, d2
    // 0xbfe1c0: ArrayStore: r1[0] = d3  ; List_8
    //     0xbfe1c0: stur            d3, [x1, #0x17]
    // 0xbfe1c4: ldur            x2, [fp, #-0x50]
    // 0xbfe1c8: ubfx            x2, x2, #0, #0x20
    // 0xbfe1cc: and             w3, w2, #0xff
    // 0xbfe1d0: ubfx            x3, x3, #0, #0x20
    // 0xbfe1d4: scvtf           d1, x3
    // 0xbfe1d8: fdiv            d3, d1, d2
    // 0xbfe1dc: StoreField: r1->field_1f = d3
    //     0xbfe1dc: stur            d3, [x1, #0x1f]
    // 0xbfe1e0: ldur            x2, [fp, #-0x30]
    // 0xbfe1e4: cmp             w2, NULL
    // 0xbfe1e8: b.ne            #0xbfe1f4
    // 0xbfe1ec: r3 = Null
    //     0xbfe1ec: mov             x3, NULL
    // 0xbfe1f0: b               #0xbfe1fc
    // 0xbfe1f4: LoadField: r3 = r2->field_7
    //     0xbfe1f4: ldur            w3, [x2, #7]
    // 0xbfe1f8: DecompressPointer r3
    //     0xbfe1f8: add             x3, x3, HEAP, lsl #32
    // 0xbfe1fc: cmp             w3, NULL
    // 0xbfe200: b.ne            #0xbfe20c
    // 0xbfe204: r3 = 0
    //     0xbfe204: movz            x3, #0
    // 0xbfe208: b               #0xbfe21c
    // 0xbfe20c: r4 = LoadInt32Instr(r3)
    //     0xbfe20c: sbfx            x4, x3, #1, #0x1f
    //     0xbfe210: tbz             w3, #0, #0xbfe218
    //     0xbfe214: ldur            x4, [x3, #7]
    // 0xbfe218: mov             x3, x4
    // 0xbfe21c: stur            x3, [fp, #-0x60]
    // 0xbfe220: cmp             w2, NULL
    // 0xbfe224: b.ne            #0xbfe230
    // 0xbfe228: r4 = Null
    //     0xbfe228: mov             x4, NULL
    // 0xbfe22c: b               #0xbfe238
    // 0xbfe230: LoadField: r4 = r2->field_b
    //     0xbfe230: ldur            w4, [x2, #0xb]
    // 0xbfe234: DecompressPointer r4
    //     0xbfe234: add             x4, x4, HEAP, lsl #32
    // 0xbfe238: cmp             w4, NULL
    // 0xbfe23c: b.ne            #0xbfe248
    // 0xbfe240: r4 = 0
    //     0xbfe240: movz            x4, #0
    // 0xbfe244: b               #0xbfe258
    // 0xbfe248: r5 = LoadInt32Instr(r4)
    //     0xbfe248: sbfx            x5, x4, #1, #0x1f
    //     0xbfe24c: tbz             w4, #0, #0xbfe254
    //     0xbfe250: ldur            x5, [x4, #7]
    // 0xbfe254: mov             x4, x5
    // 0xbfe258: stur            x4, [fp, #-0x58]
    // 0xbfe25c: cmp             w2, NULL
    // 0xbfe260: b.ne            #0xbfe26c
    // 0xbfe264: r2 = Null
    //     0xbfe264: mov             x2, NULL
    // 0xbfe268: b               #0xbfe278
    // 0xbfe26c: LoadField: r5 = r2->field_f
    //     0xbfe26c: ldur            w5, [x2, #0xf]
    // 0xbfe270: DecompressPointer r5
    //     0xbfe270: add             x5, x5, HEAP, lsl #32
    // 0xbfe274: mov             x2, x5
    // 0xbfe278: cmp             w2, NULL
    // 0xbfe27c: b.ne            #0xbfe288
    // 0xbfe280: r7 = 0
    //     0xbfe280: movz            x7, #0
    // 0xbfe284: b               #0xbfe298
    // 0xbfe288: r5 = LoadInt32Instr(r2)
    //     0xbfe288: sbfx            x5, x2, #1, #0x1f
    //     0xbfe28c: tbz             w2, #0, #0xbfe294
    //     0xbfe290: ldur            x5, [x2, #7]
    // 0xbfe294: mov             x7, x5
    // 0xbfe298: ldur            x5, [fp, #-8]
    // 0xbfe29c: ldur            x2, [fp, #-0x28]
    // 0xbfe2a0: ldur            x6, [fp, #-0x38]
    // 0xbfe2a4: stur            x7, [fp, #-0x50]
    // 0xbfe2a8: r0 = Color()
    //     0xbfe2a8: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbfe2ac: mov             x3, x0
    // 0xbfe2b0: r0 = Instance_ColorSpace
    //     0xbfe2b0: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbfe2b4: stur            x3, [fp, #-0x30]
    // 0xbfe2b8: StoreField: r3->field_27 = r0
    //     0xbfe2b8: stur            w0, [x3, #0x27]
    // 0xbfe2bc: d0 = 0.700000
    //     0xbfe2bc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbfe2c0: ldr             d0, [x17, #0xf48]
    // 0xbfe2c4: StoreField: r3->field_7 = d0
    //     0xbfe2c4: stur            d0, [x3, #7]
    // 0xbfe2c8: ldur            x0, [fp, #-0x60]
    // 0xbfe2cc: ubfx            x0, x0, #0, #0x20
    // 0xbfe2d0: and             w1, w0, #0xff
    // 0xbfe2d4: ubfx            x1, x1, #0, #0x20
    // 0xbfe2d8: scvtf           d0, x1
    // 0xbfe2dc: d1 = 255.000000
    //     0xbfe2dc: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbfe2e0: fdiv            d2, d0, d1
    // 0xbfe2e4: StoreField: r3->field_f = d2
    //     0xbfe2e4: stur            d2, [x3, #0xf]
    // 0xbfe2e8: ldur            x0, [fp, #-0x58]
    // 0xbfe2ec: ubfx            x0, x0, #0, #0x20
    // 0xbfe2f0: and             w1, w0, #0xff
    // 0xbfe2f4: ubfx            x1, x1, #0, #0x20
    // 0xbfe2f8: scvtf           d0, x1
    // 0xbfe2fc: fdiv            d2, d0, d1
    // 0xbfe300: ArrayStore: r3[0] = d2  ; List_8
    //     0xbfe300: stur            d2, [x3, #0x17]
    // 0xbfe304: ldur            x0, [fp, #-0x50]
    // 0xbfe308: ubfx            x0, x0, #0, #0x20
    // 0xbfe30c: and             w1, w0, #0xff
    // 0xbfe310: ubfx            x1, x1, #0, #0x20
    // 0xbfe314: scvtf           d0, x1
    // 0xbfe318: fdiv            d2, d0, d1
    // 0xbfe31c: StoreField: r3->field_1f = d2
    //     0xbfe31c: stur            d2, [x3, #0x1f]
    // 0xbfe320: r1 = Null
    //     0xbfe320: mov             x1, NULL
    // 0xbfe324: r2 = 4
    //     0xbfe324: movz            x2, #0x4
    // 0xbfe328: r0 = AllocateArray()
    //     0xbfe328: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfe32c: mov             x2, x0
    // 0xbfe330: ldur            x0, [fp, #-0x40]
    // 0xbfe334: stur            x2, [fp, #-0x48]
    // 0xbfe338: StoreField: r2->field_f = r0
    //     0xbfe338: stur            w0, [x2, #0xf]
    // 0xbfe33c: ldur            x0, [fp, #-0x30]
    // 0xbfe340: StoreField: r2->field_13 = r0
    //     0xbfe340: stur            w0, [x2, #0x13]
    // 0xbfe344: r1 = <Color>
    //     0xbfe344: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbfe348: ldr             x1, [x1, #0xf80]
    // 0xbfe34c: r0 = AllocateGrowableArray()
    //     0xbfe34c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfe350: mov             x1, x0
    // 0xbfe354: ldur            x0, [fp, #-0x48]
    // 0xbfe358: stur            x1, [fp, #-0x30]
    // 0xbfe35c: StoreField: r1->field_f = r0
    //     0xbfe35c: stur            w0, [x1, #0xf]
    // 0xbfe360: r2 = 4
    //     0xbfe360: movz            x2, #0x4
    // 0xbfe364: StoreField: r1->field_b = r2
    //     0xbfe364: stur            w2, [x1, #0xb]
    // 0xbfe368: r0 = LinearGradient()
    //     0xbfe368: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xbfe36c: mov             x1, x0
    // 0xbfe370: r0 = Instance_Alignment
    //     0xbfe370: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xbfe374: ldr             x0, [x0, #0xce0]
    // 0xbfe378: stur            x1, [fp, #-0x40]
    // 0xbfe37c: StoreField: r1->field_13 = r0
    //     0xbfe37c: stur            w0, [x1, #0x13]
    // 0xbfe380: r0 = Instance_Alignment
    //     0xbfe380: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xbfe384: ldr             x0, [x0, #0xce8]
    // 0xbfe388: ArrayStore: r1[0] = r0  ; List_4
    //     0xbfe388: stur            w0, [x1, #0x17]
    // 0xbfe38c: r0 = Instance_TileMode
    //     0xbfe38c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xbfe390: ldr             x0, [x0, #0xcf0]
    // 0xbfe394: StoreField: r1->field_1b = r0
    //     0xbfe394: stur            w0, [x1, #0x1b]
    // 0xbfe398: ldur            x0, [fp, #-0x30]
    // 0xbfe39c: StoreField: r1->field_7 = r0
    //     0xbfe39c: stur            w0, [x1, #7]
    // 0xbfe3a0: r0 = BoxDecoration()
    //     0xbfe3a0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbfe3a4: mov             x2, x0
    // 0xbfe3a8: r0 = Instance_BorderRadius
    //     0xbfe3a8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbfe3ac: ldr             x0, [x0, #0xf70]
    // 0xbfe3b0: stur            x2, [fp, #-0x30]
    // 0xbfe3b4: StoreField: r2->field_13 = r0
    //     0xbfe3b4: stur            w0, [x2, #0x13]
    // 0xbfe3b8: ldur            x0, [fp, #-0x40]
    // 0xbfe3bc: StoreField: r2->field_1b = r0
    //     0xbfe3bc: stur            w0, [x2, #0x1b]
    // 0xbfe3c0: r0 = Instance_BoxShape
    //     0xbfe3c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbfe3c4: ldr             x0, [x0, #0x80]
    // 0xbfe3c8: StoreField: r2->field_23 = r0
    //     0xbfe3c8: stur            w0, [x2, #0x23]
    // 0xbfe3cc: ldur            x1, [fp, #-0x10]
    // 0xbfe3d0: r0 = of()
    //     0xbfe3d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfe3d4: LoadField: r1 = r0->field_87
    //     0xbfe3d4: ldur            w1, [x0, #0x87]
    // 0xbfe3d8: DecompressPointer r1
    //     0xbfe3d8: add             x1, x1, HEAP, lsl #32
    // 0xbfe3dc: LoadField: r0 = r1->field_7
    //     0xbfe3dc: ldur            w0, [x1, #7]
    // 0xbfe3e0: DecompressPointer r0
    //     0xbfe3e0: add             x0, x0, HEAP, lsl #32
    // 0xbfe3e4: r16 = 16.000000
    //     0xbfe3e4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbfe3e8: ldr             x16, [x16, #0x188]
    // 0xbfe3ec: r30 = Instance_Color
    //     0xbfe3ec: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbfe3f0: stp             lr, x16, [SP]
    // 0xbfe3f4: mov             x1, x0
    // 0xbfe3f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbfe3f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbfe3fc: ldr             x4, [x4, #0xaa0]
    // 0xbfe400: r0 = copyWith()
    //     0xbfe400: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfe404: stur            x0, [fp, #-0x40]
    // 0xbfe408: r0 = TextSpan()
    //     0xbfe408: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbfe40c: mov             x2, x0
    // 0xbfe410: r0 = "BUMPER OFFER\n"
    //     0xbfe410: add             x0, PP, #0x48, lsl #12  ; [pp+0x48338] "BUMPER OFFER\n"
    //     0xbfe414: ldr             x0, [x0, #0x338]
    // 0xbfe418: stur            x2, [fp, #-0x48]
    // 0xbfe41c: StoreField: r2->field_b = r0
    //     0xbfe41c: stur            w0, [x2, #0xb]
    // 0xbfe420: r0 = Instance__DeferringMouseCursor
    //     0xbfe420: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbfe424: ArrayStore: r2[0] = r0  ; List_4
    //     0xbfe424: stur            w0, [x2, #0x17]
    // 0xbfe428: ldur            x1, [fp, #-0x40]
    // 0xbfe42c: StoreField: r2->field_7 = r1
    //     0xbfe42c: stur            w1, [x2, #7]
    // 0xbfe430: ldur            x1, [fp, #-0x10]
    // 0xbfe434: r0 = of()
    //     0xbfe434: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfe438: LoadField: r1 = r0->field_87
    //     0xbfe438: ldur            w1, [x0, #0x87]
    // 0xbfe43c: DecompressPointer r1
    //     0xbfe43c: add             x1, x1, HEAP, lsl #32
    // 0xbfe440: LoadField: r0 = r1->field_2b
    //     0xbfe440: ldur            w0, [x1, #0x2b]
    // 0xbfe444: DecompressPointer r0
    //     0xbfe444: add             x0, x0, HEAP, lsl #32
    // 0xbfe448: r16 = 12.000000
    //     0xbfe448: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfe44c: ldr             x16, [x16, #0x9e8]
    // 0xbfe450: r30 = Instance_Color
    //     0xbfe450: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbfe454: stp             lr, x16, [SP]
    // 0xbfe458: mov             x1, x0
    // 0xbfe45c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbfe45c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbfe460: ldr             x4, [x4, #0xaa0]
    // 0xbfe464: r0 = copyWith()
    //     0xbfe464: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfe468: stur            x0, [fp, #-0x40]
    // 0xbfe46c: r0 = TextSpan()
    //     0xbfe46c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbfe470: mov             x3, x0
    // 0xbfe474: r0 = "Unlocked from your last order"
    //     0xbfe474: add             x0, PP, #0x48, lsl #12  ; [pp+0x48340] "Unlocked from your last order"
    //     0xbfe478: ldr             x0, [x0, #0x340]
    // 0xbfe47c: stur            x3, [fp, #-0x68]
    // 0xbfe480: StoreField: r3->field_b = r0
    //     0xbfe480: stur            w0, [x3, #0xb]
    // 0xbfe484: r0 = Instance__DeferringMouseCursor
    //     0xbfe484: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbfe488: ArrayStore: r3[0] = r0  ; List_4
    //     0xbfe488: stur            w0, [x3, #0x17]
    // 0xbfe48c: ldur            x1, [fp, #-0x40]
    // 0xbfe490: StoreField: r3->field_7 = r1
    //     0xbfe490: stur            w1, [x3, #7]
    // 0xbfe494: r1 = Null
    //     0xbfe494: mov             x1, NULL
    // 0xbfe498: r2 = 4
    //     0xbfe498: movz            x2, #0x4
    // 0xbfe49c: r0 = AllocateArray()
    //     0xbfe49c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfe4a0: mov             x2, x0
    // 0xbfe4a4: ldur            x0, [fp, #-0x48]
    // 0xbfe4a8: stur            x2, [fp, #-0x40]
    // 0xbfe4ac: StoreField: r2->field_f = r0
    //     0xbfe4ac: stur            w0, [x2, #0xf]
    // 0xbfe4b0: ldur            x0, [fp, #-0x68]
    // 0xbfe4b4: StoreField: r2->field_13 = r0
    //     0xbfe4b4: stur            w0, [x2, #0x13]
    // 0xbfe4b8: r1 = <InlineSpan>
    //     0xbfe4b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xbfe4bc: ldr             x1, [x1, #0xe40]
    // 0xbfe4c0: r0 = AllocateGrowableArray()
    //     0xbfe4c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfe4c4: mov             x1, x0
    // 0xbfe4c8: ldur            x0, [fp, #-0x40]
    // 0xbfe4cc: stur            x1, [fp, #-0x48]
    // 0xbfe4d0: StoreField: r1->field_f = r0
    //     0xbfe4d0: stur            w0, [x1, #0xf]
    // 0xbfe4d4: r2 = 4
    //     0xbfe4d4: movz            x2, #0x4
    // 0xbfe4d8: StoreField: r1->field_b = r2
    //     0xbfe4d8: stur            w2, [x1, #0xb]
    // 0xbfe4dc: r0 = TextSpan()
    //     0xbfe4dc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbfe4e0: mov             x1, x0
    // 0xbfe4e4: ldur            x0, [fp, #-0x48]
    // 0xbfe4e8: stur            x1, [fp, #-0x40]
    // 0xbfe4ec: StoreField: r1->field_f = r0
    //     0xbfe4ec: stur            w0, [x1, #0xf]
    // 0xbfe4f0: r0 = Instance__DeferringMouseCursor
    //     0xbfe4f0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbfe4f4: ArrayStore: r1[0] = r0  ; List_4
    //     0xbfe4f4: stur            w0, [x1, #0x17]
    // 0xbfe4f8: r0 = RichText()
    //     0xbfe4f8: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xbfe4fc: mov             x1, x0
    // 0xbfe500: ldur            x2, [fp, #-0x40]
    // 0xbfe504: stur            x0, [fp, #-0x40]
    // 0xbfe508: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbfe508: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbfe50c: r0 = RichText()
    //     0xbfe50c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xbfe510: r1 = Instance_Color
    //     0xbfe510: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbfe514: d0 = 0.500000
    //     0xbfe514: fmov            d0, #0.50000000
    // 0xbfe518: r0 = withOpacity()
    //     0xbfe518: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbfe51c: stur            x0, [fp, #-0x48]
    // 0xbfe520: r0 = VerticalDivider()
    //     0xbfe520: bl              #0x99b78c  ; AllocateVerticalDividerStub -> VerticalDivider (size=0x28)
    // 0xbfe524: d0 = 1.000000
    //     0xbfe524: fmov            d0, #1.00000000
    // 0xbfe528: stur            x0, [fp, #-0x68]
    // 0xbfe52c: StoreField: r0->field_f = d0
    //     0xbfe52c: stur            d0, [x0, #0xf]
    // 0xbfe530: ldur            x1, [fp, #-0x48]
    // 0xbfe534: StoreField: r0->field_1f = r1
    //     0xbfe534: stur            w1, [x0, #0x1f]
    // 0xbfe538: ldur            x3, [fp, #-8]
    // 0xbfe53c: LoadField: r1 = r3->field_b
    //     0xbfe53c: ldur            w1, [x3, #0xb]
    // 0xbfe540: DecompressPointer r1
    //     0xbfe540: add             x1, x1, HEAP, lsl #32
    // 0xbfe544: cmp             w1, NULL
    // 0xbfe548: b.eq            #0xbfed98
    // 0xbfe54c: LoadField: r2 = r1->field_2f
    //     0xbfe54c: ldur            w2, [x1, #0x2f]
    // 0xbfe550: DecompressPointer r2
    //     0xbfe550: add             x2, x2, HEAP, lsl #32
    // 0xbfe554: LoadField: r4 = r2->field_7
    //     0xbfe554: ldur            w4, [x2, #7]
    // 0xbfe558: DecompressPointer r4
    //     0xbfe558: add             x4, x4, HEAP, lsl #32
    // 0xbfe55c: stur            x4, [fp, #-0x48]
    // 0xbfe560: r1 = Null
    //     0xbfe560: mov             x1, NULL
    // 0xbfe564: r2 = 4
    //     0xbfe564: movz            x2, #0x4
    // 0xbfe568: r0 = AllocateArray()
    //     0xbfe568: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfe56c: mov             x1, x0
    // 0xbfe570: ldur            x0, [fp, #-0x48]
    // 0xbfe574: StoreField: r1->field_f = r0
    //     0xbfe574: stur            w0, [x1, #0xf]
    // 0xbfe578: r16 = "\n"
    //     0xbfe578: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0xbfe57c: StoreField: r1->field_13 = r16
    //     0xbfe57c: stur            w16, [x1, #0x13]
    // 0xbfe580: str             x1, [SP]
    // 0xbfe584: r0 = _interpolate()
    //     0xbfe584: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbfe588: ldur            x1, [fp, #-0x10]
    // 0xbfe58c: stur            x0, [fp, #-0x48]
    // 0xbfe590: r0 = of()
    //     0xbfe590: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfe594: LoadField: r1 = r0->field_87
    //     0xbfe594: ldur            w1, [x0, #0x87]
    // 0xbfe598: DecompressPointer r1
    //     0xbfe598: add             x1, x1, HEAP, lsl #32
    // 0xbfe59c: LoadField: r0 = r1->field_23
    //     0xbfe59c: ldur            w0, [x1, #0x23]
    // 0xbfe5a0: DecompressPointer r0
    //     0xbfe5a0: add             x0, x0, HEAP, lsl #32
    // 0xbfe5a4: r16 = 32.000000
    //     0xbfe5a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xbfe5a8: ldr             x16, [x16, #0x848]
    // 0xbfe5ac: r30 = Instance_Color
    //     0xbfe5ac: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbfe5b0: stp             lr, x16, [SP]
    // 0xbfe5b4: mov             x1, x0
    // 0xbfe5b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbfe5b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbfe5bc: ldr             x4, [x4, #0xaa0]
    // 0xbfe5c0: r0 = copyWith()
    //     0xbfe5c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfe5c4: ldur            x1, [fp, #-0x10]
    // 0xbfe5c8: stur            x0, [fp, #-0x70]
    // 0xbfe5cc: r0 = of()
    //     0xbfe5cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfe5d0: LoadField: r1 = r0->field_87
    //     0xbfe5d0: ldur            w1, [x0, #0x87]
    // 0xbfe5d4: DecompressPointer r1
    //     0xbfe5d4: add             x1, x1, HEAP, lsl #32
    // 0xbfe5d8: LoadField: r0 = r1->field_27
    //     0xbfe5d8: ldur            w0, [x1, #0x27]
    // 0xbfe5dc: DecompressPointer r0
    //     0xbfe5dc: add             x0, x0, HEAP, lsl #32
    // 0xbfe5e0: r16 = Instance_FontWeight
    //     0xbfe5e0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12ff0] Obj!FontWeight@d68ca1
    //     0xbfe5e4: ldr             x16, [x16, #0xff0]
    // 0xbfe5e8: r30 = Instance_Color
    //     0xbfe5e8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbfe5ec: stp             lr, x16, [SP, #8]
    // 0xbfe5f0: r16 = 14.000000
    //     0xbfe5f0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbfe5f4: ldr             x16, [x16, #0x1d8]
    // 0xbfe5f8: str             x16, [SP]
    // 0xbfe5fc: mov             x1, x0
    // 0xbfe600: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x3, fontWeight, 0x1, null]
    //     0xbfe600: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d00] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x3, "fontWeight", 0x1, Null]
    //     0xbfe604: ldr             x4, [x4, #0xd00]
    // 0xbfe608: r0 = copyWith()
    //     0xbfe608: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfe60c: stur            x0, [fp, #-0x78]
    // 0xbfe610: r0 = TextSpan()
    //     0xbfe610: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbfe614: mov             x3, x0
    // 0xbfe618: r0 = "OFF"
    //     0xbfe618: add             x0, PP, #0x48, lsl #12  ; [pp+0x48348] "OFF"
    //     0xbfe61c: ldr             x0, [x0, #0x348]
    // 0xbfe620: stur            x3, [fp, #-0x80]
    // 0xbfe624: StoreField: r3->field_b = r0
    //     0xbfe624: stur            w0, [x3, #0xb]
    // 0xbfe628: r0 = Instance__DeferringMouseCursor
    //     0xbfe628: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbfe62c: ArrayStore: r3[0] = r0  ; List_4
    //     0xbfe62c: stur            w0, [x3, #0x17]
    // 0xbfe630: ldur            x1, [fp, #-0x78]
    // 0xbfe634: StoreField: r3->field_7 = r1
    //     0xbfe634: stur            w1, [x3, #7]
    // 0xbfe638: r1 = Null
    //     0xbfe638: mov             x1, NULL
    // 0xbfe63c: r2 = 2
    //     0xbfe63c: movz            x2, #0x2
    // 0xbfe640: r0 = AllocateArray()
    //     0xbfe640: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfe644: mov             x2, x0
    // 0xbfe648: ldur            x0, [fp, #-0x80]
    // 0xbfe64c: stur            x2, [fp, #-0x78]
    // 0xbfe650: StoreField: r2->field_f = r0
    //     0xbfe650: stur            w0, [x2, #0xf]
    // 0xbfe654: r1 = <InlineSpan>
    //     0xbfe654: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xbfe658: ldr             x1, [x1, #0xe40]
    // 0xbfe65c: r0 = AllocateGrowableArray()
    //     0xbfe65c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfe660: mov             x1, x0
    // 0xbfe664: ldur            x0, [fp, #-0x78]
    // 0xbfe668: stur            x1, [fp, #-0x80]
    // 0xbfe66c: StoreField: r1->field_f = r0
    //     0xbfe66c: stur            w0, [x1, #0xf]
    // 0xbfe670: r2 = 2
    //     0xbfe670: movz            x2, #0x2
    // 0xbfe674: StoreField: r1->field_b = r2
    //     0xbfe674: stur            w2, [x1, #0xb]
    // 0xbfe678: r0 = TextSpan()
    //     0xbfe678: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbfe67c: mov             x1, x0
    // 0xbfe680: ldur            x0, [fp, #-0x48]
    // 0xbfe684: stur            x1, [fp, #-0x78]
    // 0xbfe688: StoreField: r1->field_b = r0
    //     0xbfe688: stur            w0, [x1, #0xb]
    // 0xbfe68c: ldur            x0, [fp, #-0x80]
    // 0xbfe690: StoreField: r1->field_f = r0
    //     0xbfe690: stur            w0, [x1, #0xf]
    // 0xbfe694: r0 = Instance__DeferringMouseCursor
    //     0xbfe694: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbfe698: ArrayStore: r1[0] = r0  ; List_4
    //     0xbfe698: stur            w0, [x1, #0x17]
    // 0xbfe69c: ldur            x0, [fp, #-0x70]
    // 0xbfe6a0: StoreField: r1->field_7 = r0
    //     0xbfe6a0: stur            w0, [x1, #7]
    // 0xbfe6a4: r0 = RichText()
    //     0xbfe6a4: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xbfe6a8: stur            x0, [fp, #-0x48]
    // 0xbfe6ac: r16 = Instance_TextAlign
    //     0xbfe6ac: ldr             x16, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xbfe6b0: str             x16, [SP]
    // 0xbfe6b4: mov             x1, x0
    // 0xbfe6b8: ldur            x2, [fp, #-0x78]
    // 0xbfe6bc: r4 = const [0, 0x3, 0x1, 0x2, textAlign, 0x2, null]
    //     0xbfe6bc: add             x4, PP, #0x48, lsl #12  ; [pp+0x48350] List(7) [0, 0x3, 0x1, 0x2, "textAlign", 0x2, Null]
    //     0xbfe6c0: ldr             x4, [x4, #0x350]
    // 0xbfe6c4: r0 = RichText()
    //     0xbfe6c4: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xbfe6c8: r1 = Null
    //     0xbfe6c8: mov             x1, NULL
    // 0xbfe6cc: r2 = 6
    //     0xbfe6cc: movz            x2, #0x6
    // 0xbfe6d0: r0 = AllocateArray()
    //     0xbfe6d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfe6d4: mov             x2, x0
    // 0xbfe6d8: ldur            x0, [fp, #-0x40]
    // 0xbfe6dc: stur            x2, [fp, #-0x70]
    // 0xbfe6e0: StoreField: r2->field_f = r0
    //     0xbfe6e0: stur            w0, [x2, #0xf]
    // 0xbfe6e4: ldur            x0, [fp, #-0x68]
    // 0xbfe6e8: StoreField: r2->field_13 = r0
    //     0xbfe6e8: stur            w0, [x2, #0x13]
    // 0xbfe6ec: ldur            x0, [fp, #-0x48]
    // 0xbfe6f0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbfe6f0: stur            w0, [x2, #0x17]
    // 0xbfe6f4: r1 = <Widget>
    //     0xbfe6f4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfe6f8: r0 = AllocateGrowableArray()
    //     0xbfe6f8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfe6fc: mov             x1, x0
    // 0xbfe700: ldur            x0, [fp, #-0x70]
    // 0xbfe704: stur            x1, [fp, #-0x40]
    // 0xbfe708: StoreField: r1->field_f = r0
    //     0xbfe708: stur            w0, [x1, #0xf]
    // 0xbfe70c: r0 = 6
    //     0xbfe70c: movz            x0, #0x6
    // 0xbfe710: StoreField: r1->field_b = r0
    //     0xbfe710: stur            w0, [x1, #0xb]
    // 0xbfe714: r0 = Row()
    //     0xbfe714: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbfe718: mov             x1, x0
    // 0xbfe71c: r0 = Instance_Axis
    //     0xbfe71c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbfe720: stur            x1, [fp, #-0x48]
    // 0xbfe724: StoreField: r1->field_f = r0
    //     0xbfe724: stur            w0, [x1, #0xf]
    // 0xbfe728: r2 = Instance_MainAxisAlignment
    //     0xbfe728: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbfe72c: ldr             x2, [x2, #0xa8]
    // 0xbfe730: StoreField: r1->field_13 = r2
    //     0xbfe730: stur            w2, [x1, #0x13]
    // 0xbfe734: r2 = Instance_MainAxisSize
    //     0xbfe734: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfe738: ldr             x2, [x2, #0xa10]
    // 0xbfe73c: ArrayStore: r1[0] = r2  ; List_4
    //     0xbfe73c: stur            w2, [x1, #0x17]
    // 0xbfe740: r3 = Instance_CrossAxisAlignment
    //     0xbfe740: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbfe744: ldr             x3, [x3, #0xa18]
    // 0xbfe748: StoreField: r1->field_1b = r3
    //     0xbfe748: stur            w3, [x1, #0x1b]
    // 0xbfe74c: r4 = Instance_VerticalDirection
    //     0xbfe74c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfe750: ldr             x4, [x4, #0xa20]
    // 0xbfe754: StoreField: r1->field_23 = r4
    //     0xbfe754: stur            w4, [x1, #0x23]
    // 0xbfe758: r5 = Instance_Clip
    //     0xbfe758: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfe75c: ldr             x5, [x5, #0x38]
    // 0xbfe760: StoreField: r1->field_2b = r5
    //     0xbfe760: stur            w5, [x1, #0x2b]
    // 0xbfe764: StoreField: r1->field_2f = rZR
    //     0xbfe764: stur            xzr, [x1, #0x2f]
    // 0xbfe768: ldur            x6, [fp, #-0x40]
    // 0xbfe76c: StoreField: r1->field_b = r6
    //     0xbfe76c: stur            w6, [x1, #0xb]
    // 0xbfe770: r0 = Padding()
    //     0xbfe770: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbfe774: mov             x1, x0
    // 0xbfe778: r0 = Instance_EdgeInsets
    //     0xbfe778: add             x0, PP, #0x48, lsl #12  ; [pp+0x48358] Obj!EdgeInsets@d57411
    //     0xbfe77c: ldr             x0, [x0, #0x358]
    // 0xbfe780: stur            x1, [fp, #-0x40]
    // 0xbfe784: StoreField: r1->field_f = r0
    //     0xbfe784: stur            w0, [x1, #0xf]
    // 0xbfe788: ldur            x0, [fp, #-0x48]
    // 0xbfe78c: StoreField: r1->field_b = r0
    //     0xbfe78c: stur            w0, [x1, #0xb]
    // 0xbfe790: r0 = IntrinsicHeight()
    //     0xbfe790: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xbfe794: mov             x1, x0
    // 0xbfe798: ldur            x0, [fp, #-0x40]
    // 0xbfe79c: stur            x1, [fp, #-0x48]
    // 0xbfe7a0: StoreField: r1->field_b = r0
    //     0xbfe7a0: stur            w0, [x1, #0xb]
    // 0xbfe7a4: r0 = Container()
    //     0xbfe7a4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbfe7a8: stur            x0, [fp, #-0x40]
    // 0xbfe7ac: r16 = 93.000000
    //     0xbfe7ac: add             x16, PP, #0x48, lsl #12  ; [pp+0x48360] 93
    //     0xbfe7b0: ldr             x16, [x16, #0x360]
    // 0xbfe7b4: ldur            lr, [fp, #-0x30]
    // 0xbfe7b8: stp             lr, x16, [SP, #8]
    // 0xbfe7bc: ldur            x16, [fp, #-0x48]
    // 0xbfe7c0: str             x16, [SP]
    // 0xbfe7c4: mov             x1, x0
    // 0xbfe7c8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xbfe7c8: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xbfe7cc: ldr             x4, [x4, #0xc78]
    // 0xbfe7d0: r0 = Container()
    //     0xbfe7d0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbfe7d4: r1 = <Path>
    //     0xbfe7d4: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xbfe7d8: ldr             x1, [x1, #0xd30]
    // 0xbfe7dc: r0 = MovieTicketClipper()
    //     0xbfe7dc: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0xbfe7e0: stur            x0, [fp, #-0x30]
    // 0xbfe7e4: r0 = ClipPath()
    //     0xbfe7e4: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xbfe7e8: mov             x1, x0
    // 0xbfe7ec: ldur            x0, [fp, #-0x30]
    // 0xbfe7f0: stur            x1, [fp, #-0x48]
    // 0xbfe7f4: StoreField: r1->field_f = r0
    //     0xbfe7f4: stur            w0, [x1, #0xf]
    // 0xbfe7f8: r0 = Instance_Clip
    //     0xbfe7f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xbfe7fc: ldr             x0, [x0, #0x138]
    // 0xbfe800: StoreField: r1->field_13 = r0
    //     0xbfe800: stur            w0, [x1, #0x13]
    // 0xbfe804: ldur            x0, [fp, #-0x40]
    // 0xbfe808: StoreField: r1->field_b = r0
    //     0xbfe808: stur            w0, [x1, #0xb]
    // 0xbfe80c: r0 = Visibility()
    //     0xbfe80c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbfe810: mov             x1, x0
    // 0xbfe814: ldur            x0, [fp, #-0x48]
    // 0xbfe818: stur            x1, [fp, #-0x30]
    // 0xbfe81c: StoreField: r1->field_b = r0
    //     0xbfe81c: stur            w0, [x1, #0xb]
    // 0xbfe820: r0 = Instance_SizedBox
    //     0xbfe820: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbfe824: StoreField: r1->field_f = r0
    //     0xbfe824: stur            w0, [x1, #0xf]
    // 0xbfe828: ldur            x0, [fp, #-0x38]
    // 0xbfe82c: StoreField: r1->field_13 = r0
    //     0xbfe82c: stur            w0, [x1, #0x13]
    // 0xbfe830: r0 = false
    //     0xbfe830: add             x0, NULL, #0x30  ; false
    // 0xbfe834: ArrayStore: r1[0] = r0  ; List_4
    //     0xbfe834: stur            w0, [x1, #0x17]
    // 0xbfe838: StoreField: r1->field_1b = r0
    //     0xbfe838: stur            w0, [x1, #0x1b]
    // 0xbfe83c: StoreField: r1->field_1f = r0
    //     0xbfe83c: stur            w0, [x1, #0x1f]
    // 0xbfe840: StoreField: r1->field_23 = r0
    //     0xbfe840: stur            w0, [x1, #0x23]
    // 0xbfe844: StoreField: r1->field_27 = r0
    //     0xbfe844: stur            w0, [x1, #0x27]
    // 0xbfe848: StoreField: r1->field_2b = r0
    //     0xbfe848: stur            w0, [x1, #0x2b]
    // 0xbfe84c: r0 = Padding()
    //     0xbfe84c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbfe850: mov             x2, x0
    // 0xbfe854: r0 = Instance_EdgeInsets
    //     0xbfe854: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xbfe858: ldr             x0, [x0, #0x778]
    // 0xbfe85c: stur            x2, [fp, #-0x38]
    // 0xbfe860: StoreField: r2->field_f = r0
    //     0xbfe860: stur            w0, [x2, #0xf]
    // 0xbfe864: ldur            x0, [fp, #-0x30]
    // 0xbfe868: StoreField: r2->field_b = r0
    //     0xbfe868: stur            w0, [x2, #0xb]
    // 0xbfe86c: ldur            x0, [fp, #-0x28]
    // 0xbfe870: LoadField: r1 = r0->field_b
    //     0xbfe870: ldur            w1, [x0, #0xb]
    // 0xbfe874: LoadField: r3 = r0->field_f
    //     0xbfe874: ldur            w3, [x0, #0xf]
    // 0xbfe878: DecompressPointer r3
    //     0xbfe878: add             x3, x3, HEAP, lsl #32
    // 0xbfe87c: LoadField: r4 = r3->field_b
    //     0xbfe87c: ldur            w4, [x3, #0xb]
    // 0xbfe880: r3 = LoadInt32Instr(r1)
    //     0xbfe880: sbfx            x3, x1, #1, #0x1f
    // 0xbfe884: stur            x3, [fp, #-0x50]
    // 0xbfe888: r1 = LoadInt32Instr(r4)
    //     0xbfe888: sbfx            x1, x4, #1, #0x1f
    // 0xbfe88c: cmp             x3, x1
    // 0xbfe890: b.ne            #0xbfe89c
    // 0xbfe894: mov             x1, x0
    // 0xbfe898: r0 = _growToNextCapacity()
    //     0xbfe898: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbfe89c: ldur            x4, [fp, #-8]
    // 0xbfe8a0: ldur            x2, [fp, #-0x28]
    // 0xbfe8a4: ldur            x3, [fp, #-0x50]
    // 0xbfe8a8: add             x0, x3, #1
    // 0xbfe8ac: lsl             x1, x0, #1
    // 0xbfe8b0: StoreField: r2->field_b = r1
    //     0xbfe8b0: stur            w1, [x2, #0xb]
    // 0xbfe8b4: LoadField: r1 = r2->field_f
    //     0xbfe8b4: ldur            w1, [x2, #0xf]
    // 0xbfe8b8: DecompressPointer r1
    //     0xbfe8b8: add             x1, x1, HEAP, lsl #32
    // 0xbfe8bc: ldur            x0, [fp, #-0x38]
    // 0xbfe8c0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbfe8c0: add             x25, x1, x3, lsl #2
    //     0xbfe8c4: add             x25, x25, #0xf
    //     0xbfe8c8: str             w0, [x25]
    //     0xbfe8cc: tbz             w0, #0, #0xbfe8e8
    //     0xbfe8d0: ldurb           w16, [x1, #-1]
    //     0xbfe8d4: ldurb           w17, [x0, #-1]
    //     0xbfe8d8: and             x16, x17, x16, lsr #2
    //     0xbfe8dc: tst             x16, HEAP, lsr #32
    //     0xbfe8e0: b.eq            #0xbfe8e8
    //     0xbfe8e4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbfe8e8: ldur            x1, [fp, #-0x10]
    // 0xbfe8ec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbfe8ec: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbfe8f0: r0 = _of()
    //     0xbfe8f0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbfe8f4: LoadField: r1 = r0->field_7
    //     0xbfe8f4: ldur            w1, [x0, #7]
    // 0xbfe8f8: DecompressPointer r1
    //     0xbfe8f8: add             x1, x1, HEAP, lsl #32
    // 0xbfe8fc: LoadField: d0 = r1->field_f
    //     0xbfe8fc: ldur            d0, [x1, #0xf]
    // 0xbfe900: d1 = 0.450000
    //     0xbfe900: add             x17, PP, #0x52, lsl #12  ; [pp+0x52d08] IMM: double(0.45) from 0x3fdccccccccccccd
    //     0xbfe904: ldr             d1, [x17, #0xd08]
    // 0xbfe908: fmul            d2, d0, d1
    // 0xbfe90c: ldur            x1, [fp, #-0x10]
    // 0xbfe910: stur            d2, [fp, #-0x88]
    // 0xbfe914: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbfe914: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbfe918: r0 = _of()
    //     0xbfe918: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbfe91c: LoadField: r1 = r0->field_7
    //     0xbfe91c: ldur            w1, [x0, #7]
    // 0xbfe920: DecompressPointer r1
    //     0xbfe920: add             x1, x1, HEAP, lsl #32
    // 0xbfe924: LoadField: d0 = r1->field_7
    //     0xbfe924: ldur            d0, [x1, #7]
    // 0xbfe928: ldur            x1, [fp, #-8]
    // 0xbfe92c: stur            d0, [fp, #-0x90]
    // 0xbfe930: LoadField: r0 = r1->field_b
    //     0xbfe930: ldur            w0, [x1, #0xb]
    // 0xbfe934: DecompressPointer r0
    //     0xbfe934: add             x0, x0, HEAP, lsl #32
    // 0xbfe938: cmp             w0, NULL
    // 0xbfe93c: b.eq            #0xbfed9c
    // 0xbfe940: LoadField: r2 = r0->field_b
    //     0xbfe940: ldur            w2, [x0, #0xb]
    // 0xbfe944: DecompressPointer r2
    //     0xbfe944: add             x2, x2, HEAP, lsl #32
    // 0xbfe948: r0 = LoadClassIdInstr(r2)
    //     0xbfe948: ldur            x0, [x2, #-1]
    //     0xbfe94c: ubfx            x0, x0, #0xc, #0x14
    // 0xbfe950: str             x2, [SP]
    // 0xbfe954: r0 = GDT[cid_x0 + 0xc898]()
    //     0xbfe954: movz            x17, #0xc898
    //     0xbfe958: add             lr, x0, x17
    //     0xbfe95c: ldr             lr, [x21, lr, lsl #3]
    //     0xbfe960: blr             lr
    // 0xbfe964: mov             x3, x0
    // 0xbfe968: ldur            x0, [fp, #-8]
    // 0xbfe96c: stur            x3, [fp, #-0x38]
    // 0xbfe970: LoadField: r4 = r0->field_13
    //     0xbfe970: ldur            w4, [x0, #0x13]
    // 0xbfe974: DecompressPointer r4
    //     0xbfe974: add             x4, x4, HEAP, lsl #32
    // 0xbfe978: r16 = Sentinel
    //     0xbfe978: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbfe97c: cmp             w4, w16
    // 0xbfe980: b.eq            #0xbfeda0
    // 0xbfe984: ldur            x2, [fp, #-0x20]
    // 0xbfe988: stur            x4, [fp, #-0x30]
    // 0xbfe98c: r1 = Function '<anonymous closure>':.
    //     0xbfe98c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52d10] AnonymousClosure: (0xbff2ac), in [package:customer_app/app/presentation/views/line/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::build (0xbfdac0)
    //     0xbfe990: ldr             x1, [x1, #0xd10]
    // 0xbfe994: r0 = AllocateClosure()
    //     0xbfe994: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfe998: ldur            x2, [fp, #-0x20]
    // 0xbfe99c: r1 = Function '<anonymous closure>':.
    //     0xbfe99c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52d18] AnonymousClosure: (0xbfeddc), in [package:customer_app/app/presentation/views/line/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::build (0xbfdac0)
    //     0xbfe9a0: ldr             x1, [x1, #0xd18]
    // 0xbfe9a4: stur            x0, [fp, #-0x20]
    // 0xbfe9a8: r0 = AllocateClosure()
    //     0xbfe9a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfe9ac: stur            x0, [fp, #-0x40]
    // 0xbfe9b0: r0 = PageView()
    //     0xbfe9b0: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xbfe9b4: stur            x0, [fp, #-0x48]
    // 0xbfe9b8: r16 = Instance_BouncingScrollPhysics
    //     0xbfe9b8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xbfe9bc: ldr             x16, [x16, #0x890]
    // 0xbfe9c0: r30 = false
    //     0xbfe9c0: add             lr, NULL, #0x30  ; false
    // 0xbfe9c4: stp             lr, x16, [SP, #8]
    // 0xbfe9c8: ldur            x16, [fp, #-0x30]
    // 0xbfe9cc: str             x16, [SP]
    // 0xbfe9d0: mov             x1, x0
    // 0xbfe9d4: ldur            x2, [fp, #-0x40]
    // 0xbfe9d8: ldur            x3, [fp, #-0x38]
    // 0xbfe9dc: ldur            x5, [fp, #-0x20]
    // 0xbfe9e0: r4 = const [0, 0x7, 0x3, 0x4, controller, 0x6, padEnds, 0x5, physics, 0x4, null]
    //     0xbfe9e0: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d20] List(11) [0, 0x7, 0x3, 0x4, "controller", 0x6, "padEnds", 0x5, "physics", 0x4, Null]
    //     0xbfe9e4: ldr             x4, [x4, #0xd20]
    // 0xbfe9e8: r0 = PageView.builder()
    //     0xbfe9e8: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xbfe9ec: ldur            d0, [fp, #-0x90]
    // 0xbfe9f0: r0 = inline_Allocate_Double()
    //     0xbfe9f0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbfe9f4: add             x0, x0, #0x10
    //     0xbfe9f8: cmp             x1, x0
    //     0xbfe9fc: b.ls            #0xbfedac
    //     0xbfea00: str             x0, [THR, #0x50]  ; THR::top
    //     0xbfea04: sub             x0, x0, #0xf
    //     0xbfea08: movz            x1, #0xe15c
    //     0xbfea0c: movk            x1, #0x3, lsl #16
    //     0xbfea10: stur            x1, [x0, #-1]
    // 0xbfea14: StoreField: r0->field_7 = d0
    //     0xbfea14: stur            d0, [x0, #7]
    // 0xbfea18: stur            x0, [fp, #-0x20]
    // 0xbfea1c: r0 = SizedBox()
    //     0xbfea1c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbfea20: mov             x2, x0
    // 0xbfea24: ldur            x0, [fp, #-0x20]
    // 0xbfea28: stur            x2, [fp, #-0x30]
    // 0xbfea2c: StoreField: r2->field_f = r0
    //     0xbfea2c: stur            w0, [x2, #0xf]
    // 0xbfea30: ldur            d0, [fp, #-0x88]
    // 0xbfea34: r0 = inline_Allocate_Double()
    //     0xbfea34: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbfea38: add             x0, x0, #0x10
    //     0xbfea3c: cmp             x1, x0
    //     0xbfea40: b.ls            #0xbfedbc
    //     0xbfea44: str             x0, [THR, #0x50]  ; THR::top
    //     0xbfea48: sub             x0, x0, #0xf
    //     0xbfea4c: movz            x1, #0xe15c
    //     0xbfea50: movk            x1, #0x3, lsl #16
    //     0xbfea54: stur            x1, [x0, #-1]
    // 0xbfea58: StoreField: r0->field_7 = d0
    //     0xbfea58: stur            d0, [x0, #7]
    // 0xbfea5c: StoreField: r2->field_13 = r0
    //     0xbfea5c: stur            w0, [x2, #0x13]
    // 0xbfea60: ldur            x0, [fp, #-0x48]
    // 0xbfea64: StoreField: r2->field_b = r0
    //     0xbfea64: stur            w0, [x2, #0xb]
    // 0xbfea68: ldur            x0, [fp, #-0x28]
    // 0xbfea6c: LoadField: r1 = r0->field_b
    //     0xbfea6c: ldur            w1, [x0, #0xb]
    // 0xbfea70: LoadField: r3 = r0->field_f
    //     0xbfea70: ldur            w3, [x0, #0xf]
    // 0xbfea74: DecompressPointer r3
    //     0xbfea74: add             x3, x3, HEAP, lsl #32
    // 0xbfea78: LoadField: r4 = r3->field_b
    //     0xbfea78: ldur            w4, [x3, #0xb]
    // 0xbfea7c: r3 = LoadInt32Instr(r1)
    //     0xbfea7c: sbfx            x3, x1, #1, #0x1f
    // 0xbfea80: stur            x3, [fp, #-0x50]
    // 0xbfea84: r1 = LoadInt32Instr(r4)
    //     0xbfea84: sbfx            x1, x4, #1, #0x1f
    // 0xbfea88: cmp             x3, x1
    // 0xbfea8c: b.ne            #0xbfea98
    // 0xbfea90: mov             x1, x0
    // 0xbfea94: r0 = _growToNextCapacity()
    //     0xbfea94: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbfea98: ldur            x4, [fp, #-8]
    // 0xbfea9c: ldur            x2, [fp, #-0x28]
    // 0xbfeaa0: ldur            x3, [fp, #-0x50]
    // 0xbfeaa4: add             x0, x3, #1
    // 0xbfeaa8: lsl             x1, x0, #1
    // 0xbfeaac: StoreField: r2->field_b = r1
    //     0xbfeaac: stur            w1, [x2, #0xb]
    // 0xbfeab0: LoadField: r1 = r2->field_f
    //     0xbfeab0: ldur            w1, [x2, #0xf]
    // 0xbfeab4: DecompressPointer r1
    //     0xbfeab4: add             x1, x1, HEAP, lsl #32
    // 0xbfeab8: ldur            x0, [fp, #-0x30]
    // 0xbfeabc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbfeabc: add             x25, x1, x3, lsl #2
    //     0xbfeac0: add             x25, x25, #0xf
    //     0xbfeac4: str             w0, [x25]
    //     0xbfeac8: tbz             w0, #0, #0xbfeae4
    //     0xbfeacc: ldurb           w16, [x1, #-1]
    //     0xbfead0: ldurb           w17, [x0, #-1]
    //     0xbfead4: and             x16, x17, x16, lsr #2
    //     0xbfead8: tst             x16, HEAP, lsr #32
    //     0xbfeadc: b.eq            #0xbfeae4
    //     0xbfeae0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbfeae4: LoadField: r0 = r4->field_b
    //     0xbfeae4: ldur            w0, [x4, #0xb]
    // 0xbfeae8: DecompressPointer r0
    //     0xbfeae8: add             x0, x0, HEAP, lsl #32
    // 0xbfeaec: cmp             w0, NULL
    // 0xbfeaf0: b.eq            #0xbfedd4
    // 0xbfeaf4: LoadField: r1 = r0->field_b
    //     0xbfeaf4: ldur            w1, [x0, #0xb]
    // 0xbfeaf8: DecompressPointer r1
    //     0xbfeaf8: add             x1, x1, HEAP, lsl #32
    // 0xbfeafc: r0 = LoadClassIdInstr(r1)
    //     0xbfeafc: ldur            x0, [x1, #-1]
    //     0xbfeb00: ubfx            x0, x0, #0xc, #0x14
    // 0xbfeb04: str             x1, [SP]
    // 0xbfeb08: r0 = GDT[cid_x0 + 0xc898]()
    //     0xbfeb08: movz            x17, #0xc898
    //     0xbfeb0c: add             lr, x0, x17
    //     0xbfeb10: ldr             lr, [x21, lr, lsl #3]
    //     0xbfeb14: blr             lr
    // 0xbfeb18: r1 = LoadInt32Instr(r0)
    //     0xbfeb18: sbfx            x1, x0, #1, #0x1f
    // 0xbfeb1c: cmp             x1, #2
    // 0xbfeb20: b.le            #0xbfecf8
    // 0xbfeb24: ldur            x2, [fp, #-8]
    // 0xbfeb28: ldur            x1, [fp, #-0x28]
    // 0xbfeb2c: LoadField: r0 = r2->field_b
    //     0xbfeb2c: ldur            w0, [x2, #0xb]
    // 0xbfeb30: DecompressPointer r0
    //     0xbfeb30: add             x0, x0, HEAP, lsl #32
    // 0xbfeb34: cmp             w0, NULL
    // 0xbfeb38: b.eq            #0xbfedd8
    // 0xbfeb3c: LoadField: r3 = r0->field_b
    //     0xbfeb3c: ldur            w3, [x0, #0xb]
    // 0xbfeb40: DecompressPointer r3
    //     0xbfeb40: add             x3, x3, HEAP, lsl #32
    // 0xbfeb44: r0 = LoadClassIdInstr(r3)
    //     0xbfeb44: ldur            x0, [x3, #-1]
    //     0xbfeb48: ubfx            x0, x0, #0xc, #0x14
    // 0xbfeb4c: str             x3, [SP]
    // 0xbfeb50: r0 = GDT[cid_x0 + 0xc898]()
    //     0xbfeb50: movz            x17, #0xc898
    //     0xbfeb54: add             lr, x0, x17
    //     0xbfeb58: ldr             lr, [x21, lr, lsl #3]
    //     0xbfeb5c: blr             lr
    // 0xbfeb60: mov             x2, x0
    // 0xbfeb64: ldur            x0, [fp, #-8]
    // 0xbfeb68: stur            x2, [fp, #-0x20]
    // 0xbfeb6c: LoadField: r3 = r0->field_1b
    //     0xbfeb6c: ldur            x3, [x0, #0x1b]
    // 0xbfeb70: ldur            x1, [fp, #-0x10]
    // 0xbfeb74: stur            x3, [fp, #-0x50]
    // 0xbfeb78: r0 = of()
    //     0xbfeb78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfeb7c: LoadField: r1 = r0->field_5b
    //     0xbfeb7c: ldur            w1, [x0, #0x5b]
    // 0xbfeb80: DecompressPointer r1
    //     0xbfeb80: add             x1, x1, HEAP, lsl #32
    // 0xbfeb84: ldur            x0, [fp, #-0x20]
    // 0xbfeb88: stur            x1, [fp, #-8]
    // 0xbfeb8c: r2 = LoadInt32Instr(r0)
    //     0xbfeb8c: sbfx            x2, x0, #1, #0x1f
    // 0xbfeb90: stur            x2, [fp, #-0x58]
    // 0xbfeb94: r0 = CarouselIndicator()
    //     0xbfeb94: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xbfeb98: mov             x3, x0
    // 0xbfeb9c: ldur            x0, [fp, #-0x58]
    // 0xbfeba0: stur            x3, [fp, #-0x10]
    // 0xbfeba4: StoreField: r3->field_b = r0
    //     0xbfeba4: stur            x0, [x3, #0xb]
    // 0xbfeba8: ldur            x0, [fp, #-0x50]
    // 0xbfebac: StoreField: r3->field_13 = r0
    //     0xbfebac: stur            x0, [x3, #0x13]
    // 0xbfebb0: ldur            x0, [fp, #-8]
    // 0xbfebb4: StoreField: r3->field_1b = r0
    //     0xbfebb4: stur            w0, [x3, #0x1b]
    // 0xbfebb8: r0 = Instance_Color
    //     0xbfebb8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xbfebbc: ldr             x0, [x0, #0x90]
    // 0xbfebc0: StoreField: r3->field_1f = r0
    //     0xbfebc0: stur            w0, [x3, #0x1f]
    // 0xbfebc4: r1 = Null
    //     0xbfebc4: mov             x1, NULL
    // 0xbfebc8: r2 = 2
    //     0xbfebc8: movz            x2, #0x2
    // 0xbfebcc: r0 = AllocateArray()
    //     0xbfebcc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfebd0: mov             x2, x0
    // 0xbfebd4: ldur            x0, [fp, #-0x10]
    // 0xbfebd8: stur            x2, [fp, #-8]
    // 0xbfebdc: StoreField: r2->field_f = r0
    //     0xbfebdc: stur            w0, [x2, #0xf]
    // 0xbfebe0: r1 = <Widget>
    //     0xbfebe0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfebe4: r0 = AllocateGrowableArray()
    //     0xbfebe4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfebe8: mov             x1, x0
    // 0xbfebec: ldur            x0, [fp, #-8]
    // 0xbfebf0: stur            x1, [fp, #-0x10]
    // 0xbfebf4: StoreField: r1->field_f = r0
    //     0xbfebf4: stur            w0, [x1, #0xf]
    // 0xbfebf8: r0 = 2
    //     0xbfebf8: movz            x0, #0x2
    // 0xbfebfc: StoreField: r1->field_b = r0
    //     0xbfebfc: stur            w0, [x1, #0xb]
    // 0xbfec00: r0 = Row()
    //     0xbfec00: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbfec04: mov             x1, x0
    // 0xbfec08: r0 = Instance_Axis
    //     0xbfec08: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbfec0c: stur            x1, [fp, #-8]
    // 0xbfec10: StoreField: r1->field_f = r0
    //     0xbfec10: stur            w0, [x1, #0xf]
    // 0xbfec14: r0 = Instance_MainAxisAlignment
    //     0xbfec14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbfec18: ldr             x0, [x0, #0xab0]
    // 0xbfec1c: StoreField: r1->field_13 = r0
    //     0xbfec1c: stur            w0, [x1, #0x13]
    // 0xbfec20: r0 = Instance_MainAxisSize
    //     0xbfec20: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfec24: ldr             x0, [x0, #0xa10]
    // 0xbfec28: ArrayStore: r1[0] = r0  ; List_4
    //     0xbfec28: stur            w0, [x1, #0x17]
    // 0xbfec2c: r2 = Instance_CrossAxisAlignment
    //     0xbfec2c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbfec30: ldr             x2, [x2, #0xa18]
    // 0xbfec34: StoreField: r1->field_1b = r2
    //     0xbfec34: stur            w2, [x1, #0x1b]
    // 0xbfec38: r2 = Instance_VerticalDirection
    //     0xbfec38: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfec3c: ldr             x2, [x2, #0xa20]
    // 0xbfec40: StoreField: r1->field_23 = r2
    //     0xbfec40: stur            w2, [x1, #0x23]
    // 0xbfec44: r3 = Instance_Clip
    //     0xbfec44: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfec48: ldr             x3, [x3, #0x38]
    // 0xbfec4c: StoreField: r1->field_2b = r3
    //     0xbfec4c: stur            w3, [x1, #0x2b]
    // 0xbfec50: StoreField: r1->field_2f = rZR
    //     0xbfec50: stur            xzr, [x1, #0x2f]
    // 0xbfec54: ldur            x4, [fp, #-0x10]
    // 0xbfec58: StoreField: r1->field_b = r4
    //     0xbfec58: stur            w4, [x1, #0xb]
    // 0xbfec5c: r0 = Padding()
    //     0xbfec5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbfec60: mov             x2, x0
    // 0xbfec64: r0 = Instance_EdgeInsets
    //     0xbfec64: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xbfec68: ldr             x0, [x0, #0x990]
    // 0xbfec6c: stur            x2, [fp, #-0x10]
    // 0xbfec70: StoreField: r2->field_f = r0
    //     0xbfec70: stur            w0, [x2, #0xf]
    // 0xbfec74: ldur            x0, [fp, #-8]
    // 0xbfec78: StoreField: r2->field_b = r0
    //     0xbfec78: stur            w0, [x2, #0xb]
    // 0xbfec7c: ldur            x0, [fp, #-0x28]
    // 0xbfec80: LoadField: r1 = r0->field_b
    //     0xbfec80: ldur            w1, [x0, #0xb]
    // 0xbfec84: LoadField: r3 = r0->field_f
    //     0xbfec84: ldur            w3, [x0, #0xf]
    // 0xbfec88: DecompressPointer r3
    //     0xbfec88: add             x3, x3, HEAP, lsl #32
    // 0xbfec8c: LoadField: r4 = r3->field_b
    //     0xbfec8c: ldur            w4, [x3, #0xb]
    // 0xbfec90: r3 = LoadInt32Instr(r1)
    //     0xbfec90: sbfx            x3, x1, #1, #0x1f
    // 0xbfec94: stur            x3, [fp, #-0x50]
    // 0xbfec98: r1 = LoadInt32Instr(r4)
    //     0xbfec98: sbfx            x1, x4, #1, #0x1f
    // 0xbfec9c: cmp             x3, x1
    // 0xbfeca0: b.ne            #0xbfecac
    // 0xbfeca4: mov             x1, x0
    // 0xbfeca8: r0 = _growToNextCapacity()
    //     0xbfeca8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbfecac: ldur            x2, [fp, #-0x28]
    // 0xbfecb0: ldur            x3, [fp, #-0x50]
    // 0xbfecb4: add             x0, x3, #1
    // 0xbfecb8: lsl             x1, x0, #1
    // 0xbfecbc: StoreField: r2->field_b = r1
    //     0xbfecbc: stur            w1, [x2, #0xb]
    // 0xbfecc0: LoadField: r1 = r2->field_f
    //     0xbfecc0: ldur            w1, [x2, #0xf]
    // 0xbfecc4: DecompressPointer r1
    //     0xbfecc4: add             x1, x1, HEAP, lsl #32
    // 0xbfecc8: ldur            x0, [fp, #-0x10]
    // 0xbfeccc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbfeccc: add             x25, x1, x3, lsl #2
    //     0xbfecd0: add             x25, x25, #0xf
    //     0xbfecd4: str             w0, [x25]
    //     0xbfecd8: tbz             w0, #0, #0xbfecf4
    //     0xbfecdc: ldurb           w16, [x1, #-1]
    //     0xbfece0: ldurb           w17, [x0, #-1]
    //     0xbfece4: and             x16, x17, x16, lsr #2
    //     0xbfece8: tst             x16, HEAP, lsr #32
    //     0xbfecec: b.eq            #0xbfecf4
    //     0xbfecf0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbfecf4: b               #0xbfecfc
    // 0xbfecf8: ldur            x2, [fp, #-0x28]
    // 0xbfecfc: ldur            x0, [fp, #-0x18]
    // 0xbfed00: r0 = Column()
    //     0xbfed00: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbfed04: mov             x1, x0
    // 0xbfed08: r0 = Instance_Axis
    //     0xbfed08: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbfed0c: stur            x1, [fp, #-8]
    // 0xbfed10: StoreField: r1->field_f = r0
    //     0xbfed10: stur            w0, [x1, #0xf]
    // 0xbfed14: r0 = Instance_MainAxisAlignment
    //     0xbfed14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfed18: ldr             x0, [x0, #0xa08]
    // 0xbfed1c: StoreField: r1->field_13 = r0
    //     0xbfed1c: stur            w0, [x1, #0x13]
    // 0xbfed20: r0 = Instance_MainAxisSize
    //     0xbfed20: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfed24: ldr             x0, [x0, #0xa10]
    // 0xbfed28: ArrayStore: r1[0] = r0  ; List_4
    //     0xbfed28: stur            w0, [x1, #0x17]
    // 0xbfed2c: ldur            x0, [fp, #-0x18]
    // 0xbfed30: StoreField: r1->field_1b = r0
    //     0xbfed30: stur            w0, [x1, #0x1b]
    // 0xbfed34: r0 = Instance_VerticalDirection
    //     0xbfed34: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfed38: ldr             x0, [x0, #0xa20]
    // 0xbfed3c: StoreField: r1->field_23 = r0
    //     0xbfed3c: stur            w0, [x1, #0x23]
    // 0xbfed40: r0 = Instance_Clip
    //     0xbfed40: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfed44: ldr             x0, [x0, #0x38]
    // 0xbfed48: StoreField: r1->field_2b = r0
    //     0xbfed48: stur            w0, [x1, #0x2b]
    // 0xbfed4c: StoreField: r1->field_2f = rZR
    //     0xbfed4c: stur            xzr, [x1, #0x2f]
    // 0xbfed50: ldur            x0, [fp, #-0x28]
    // 0xbfed54: StoreField: r1->field_b = r0
    //     0xbfed54: stur            w0, [x1, #0xb]
    // 0xbfed58: r0 = Padding()
    //     0xbfed58: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbfed5c: r1 = Instance_EdgeInsets
    //     0xbfed5c: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xbfed60: ldr             x1, [x1, #0x110]
    // 0xbfed64: StoreField: r0->field_f = r1
    //     0xbfed64: stur            w1, [x0, #0xf]
    // 0xbfed68: ldur            x1, [fp, #-8]
    // 0xbfed6c: StoreField: r0->field_b = r1
    //     0xbfed6c: stur            w1, [x0, #0xb]
    // 0xbfed70: LeaveFrame
    //     0xbfed70: mov             SP, fp
    //     0xbfed74: ldp             fp, lr, [SP], #0x10
    // 0xbfed78: ret
    //     0xbfed78: ret             
    // 0xbfed7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfed7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfed80: b               #0xbfdae8
    // 0xbfed84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfed84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfed88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfed88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfed8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfed8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfed90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfed90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfed94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfed94: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfed98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfed98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfed9c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbfed9c: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbfeda0: r9 = _pageController
    //     0xbfeda0: add             x9, PP, #0x52, lsl #12  ; [pp+0x52d28] Field <_GroupCarouselItemViewState@1732154658._pageController@1732154658>: late (offset: 0x14)
    //     0xbfeda4: ldr             x9, [x9, #0xd28]
    // 0xbfeda8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbfeda8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbfedac: SaveReg d0
    //     0xbfedac: str             q0, [SP, #-0x10]!
    // 0xbfedb0: r0 = AllocateDouble()
    //     0xbfedb0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbfedb4: RestoreReg d0
    //     0xbfedb4: ldr             q0, [SP], #0x10
    // 0xbfedb8: b               #0xbfea14
    // 0xbfedbc: SaveReg d0
    //     0xbfedbc: str             q0, [SP, #-0x10]!
    // 0xbfedc0: SaveReg r2
    //     0xbfedc0: str             x2, [SP, #-8]!
    // 0xbfedc4: r0 = AllocateDouble()
    //     0xbfedc4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbfedc8: RestoreReg r2
    //     0xbfedc8: ldr             x2, [SP], #8
    // 0xbfedcc: RestoreReg d0
    //     0xbfedcc: ldr             q0, [SP], #0x10
    // 0xbfedd0: b               #0xbfea58
    // 0xbfedd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfedd4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfedd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfedd8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbfeddc, size: 0x4d0
    // 0xbfeddc: EnterFrame
    //     0xbfeddc: stp             fp, lr, [SP, #-0x10]!
    //     0xbfede0: mov             fp, SP
    // 0xbfede4: AllocStack(0x68)
    //     0xbfede4: sub             SP, SP, #0x68
    // 0xbfede8: SetupParameters()
    //     0xbfede8: ldr             x0, [fp, #0x20]
    //     0xbfedec: ldur            w1, [x0, #0x17]
    //     0xbfedf0: add             x1, x1, HEAP, lsl #32
    //     0xbfedf4: stur            x1, [fp, #-0x18]
    // 0xbfedf8: CheckStackOverflow
    //     0xbfedf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfedfc: cmp             SP, x16
    //     0xbfee00: b.ls            #0xbff288
    // 0xbfee04: LoadField: r0 = r1->field_f
    //     0xbfee04: ldur            w0, [x1, #0xf]
    // 0xbfee08: DecompressPointer r0
    //     0xbfee08: add             x0, x0, HEAP, lsl #32
    // 0xbfee0c: LoadField: r2 = r0->field_1b
    //     0xbfee0c: ldur            x2, [x0, #0x1b]
    // 0xbfee10: ldr             x3, [fp, #0x10]
    // 0xbfee14: r4 = LoadInt32Instr(r3)
    //     0xbfee14: sbfx            x4, x3, #1, #0x1f
    //     0xbfee18: tbz             w3, #0, #0xbfee20
    //     0xbfee1c: ldur            x4, [x3, #7]
    // 0xbfee20: stur            x4, [fp, #-0x10]
    // 0xbfee24: cmp             x4, x2
    // 0xbfee28: b.ne            #0xbff094
    // 0xbfee2c: LoadField: r2 = r0->field_b
    //     0xbfee2c: ldur            w2, [x0, #0xb]
    // 0xbfee30: DecompressPointer r2
    //     0xbfee30: add             x2, x2, HEAP, lsl #32
    // 0xbfee34: stur            x2, [fp, #-8]
    // 0xbfee38: cmp             w2, NULL
    // 0xbfee3c: b.eq            #0xbff290
    // 0xbfee40: LoadField: r0 = r2->field_b
    //     0xbfee40: ldur            w0, [x2, #0xb]
    // 0xbfee44: DecompressPointer r0
    //     0xbfee44: add             x0, x0, HEAP, lsl #32
    // 0xbfee48: r5 = LoadClassIdInstr(r0)
    //     0xbfee48: ldur            x5, [x0, #-1]
    //     0xbfee4c: ubfx            x5, x5, #0xc, #0x14
    // 0xbfee50: stp             x3, x0, [SP]
    // 0xbfee54: mov             x0, x5
    // 0xbfee58: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbfee58: sub             lr, x0, #0xb7
    //     0xbfee5c: ldr             lr, [x21, lr, lsl #3]
    //     0xbfee60: blr             lr
    // 0xbfee64: cmp             w0, NULL
    // 0xbfee68: b.ne            #0xbfee74
    // 0xbfee6c: r2 = Null
    //     0xbfee6c: mov             x2, NULL
    // 0xbfee70: b               #0xbfee98
    // 0xbfee74: LoadField: r1 = r0->field_eb
    //     0xbfee74: ldur            w1, [x0, #0xeb]
    // 0xbfee78: DecompressPointer r1
    //     0xbfee78: add             x1, x1, HEAP, lsl #32
    // 0xbfee7c: cmp             w1, NULL
    // 0xbfee80: b.ne            #0xbfee8c
    // 0xbfee84: r0 = Null
    //     0xbfee84: mov             x0, NULL
    // 0xbfee88: b               #0xbfee94
    // 0xbfee8c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbfee8c: ldur            w0, [x1, #0x17]
    // 0xbfee90: DecompressPointer r0
    //     0xbfee90: add             x0, x0, HEAP, lsl #32
    // 0xbfee94: mov             x2, x0
    // 0xbfee98: ldur            x1, [fp, #-0x18]
    // 0xbfee9c: stur            x2, [fp, #-0x20]
    // 0xbfeea0: LoadField: r0 = r1->field_f
    //     0xbfeea0: ldur            w0, [x1, #0xf]
    // 0xbfeea4: DecompressPointer r0
    //     0xbfeea4: add             x0, x0, HEAP, lsl #32
    // 0xbfeea8: LoadField: r3 = r0->field_b
    //     0xbfeea8: ldur            w3, [x0, #0xb]
    // 0xbfeeac: DecompressPointer r3
    //     0xbfeeac: add             x3, x3, HEAP, lsl #32
    // 0xbfeeb0: cmp             w3, NULL
    // 0xbfeeb4: b.eq            #0xbff294
    // 0xbfeeb8: LoadField: r0 = r3->field_b
    //     0xbfeeb8: ldur            w0, [x3, #0xb]
    // 0xbfeebc: DecompressPointer r0
    //     0xbfeebc: add             x0, x0, HEAP, lsl #32
    // 0xbfeec0: r3 = LoadClassIdInstr(r0)
    //     0xbfeec0: ldur            x3, [x0, #-1]
    //     0xbfeec4: ubfx            x3, x3, #0xc, #0x14
    // 0xbfeec8: ldr             x16, [fp, #0x10]
    // 0xbfeecc: stp             x16, x0, [SP]
    // 0xbfeed0: mov             x0, x3
    // 0xbfeed4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbfeed4: sub             lr, x0, #0xb7
    //     0xbfeed8: ldr             lr, [x21, lr, lsl #3]
    //     0xbfeedc: blr             lr
    // 0xbfeee0: cmp             w0, NULL
    // 0xbfeee4: b.ne            #0xbfeef0
    // 0xbfeee8: r2 = Null
    //     0xbfeee8: mov             x2, NULL
    // 0xbfeeec: b               #0xbfef14
    // 0xbfeef0: LoadField: r1 = r0->field_eb
    //     0xbfeef0: ldur            w1, [x0, #0xeb]
    // 0xbfeef4: DecompressPointer r1
    //     0xbfeef4: add             x1, x1, HEAP, lsl #32
    // 0xbfeef8: cmp             w1, NULL
    // 0xbfeefc: b.ne            #0xbfef08
    // 0xbfef00: r0 = Null
    //     0xbfef00: mov             x0, NULL
    // 0xbfef04: b               #0xbfef10
    // 0xbfef08: LoadField: r0 = r1->field_23
    //     0xbfef08: ldur            w0, [x1, #0x23]
    // 0xbfef0c: DecompressPointer r0
    //     0xbfef0c: add             x0, x0, HEAP, lsl #32
    // 0xbfef10: mov             x2, x0
    // 0xbfef14: ldur            x1, [fp, #-0x18]
    // 0xbfef18: stur            x2, [fp, #-0x28]
    // 0xbfef1c: LoadField: r0 = r1->field_f
    //     0xbfef1c: ldur            w0, [x1, #0xf]
    // 0xbfef20: DecompressPointer r0
    //     0xbfef20: add             x0, x0, HEAP, lsl #32
    // 0xbfef24: LoadField: r3 = r0->field_b
    //     0xbfef24: ldur            w3, [x0, #0xb]
    // 0xbfef28: DecompressPointer r3
    //     0xbfef28: add             x3, x3, HEAP, lsl #32
    // 0xbfef2c: cmp             w3, NULL
    // 0xbfef30: b.eq            #0xbff298
    // 0xbfef34: LoadField: r0 = r3->field_b
    //     0xbfef34: ldur            w0, [x3, #0xb]
    // 0xbfef38: DecompressPointer r0
    //     0xbfef38: add             x0, x0, HEAP, lsl #32
    // 0xbfef3c: r3 = LoadClassIdInstr(r0)
    //     0xbfef3c: ldur            x3, [x0, #-1]
    //     0xbfef40: ubfx            x3, x3, #0xc, #0x14
    // 0xbfef44: ldr             x16, [fp, #0x10]
    // 0xbfef48: stp             x16, x0, [SP]
    // 0xbfef4c: mov             x0, x3
    // 0xbfef50: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbfef50: sub             lr, x0, #0xb7
    //     0xbfef54: ldr             lr, [x21, lr, lsl #3]
    //     0xbfef58: blr             lr
    // 0xbfef5c: cmp             w0, NULL
    // 0xbfef60: b.ne            #0xbfef6c
    // 0xbfef64: r2 = Null
    //     0xbfef64: mov             x2, NULL
    // 0xbfef68: b               #0xbfef78
    // 0xbfef6c: LoadField: r1 = r0->field_47
    //     0xbfef6c: ldur            w1, [x0, #0x47]
    // 0xbfef70: DecompressPointer r1
    //     0xbfef70: add             x1, x1, HEAP, lsl #32
    // 0xbfef74: mov             x2, x1
    // 0xbfef78: ldur            x1, [fp, #-0x18]
    // 0xbfef7c: stur            x2, [fp, #-0x30]
    // 0xbfef80: LoadField: r0 = r1->field_f
    //     0xbfef80: ldur            w0, [x1, #0xf]
    // 0xbfef84: DecompressPointer r0
    //     0xbfef84: add             x0, x0, HEAP, lsl #32
    // 0xbfef88: LoadField: r3 = r0->field_b
    //     0xbfef88: ldur            w3, [x0, #0xb]
    // 0xbfef8c: DecompressPointer r3
    //     0xbfef8c: add             x3, x3, HEAP, lsl #32
    // 0xbfef90: cmp             w3, NULL
    // 0xbfef94: b.eq            #0xbff29c
    // 0xbfef98: LoadField: r0 = r3->field_b
    //     0xbfef98: ldur            w0, [x3, #0xb]
    // 0xbfef9c: DecompressPointer r0
    //     0xbfef9c: add             x0, x0, HEAP, lsl #32
    // 0xbfefa0: r3 = LoadClassIdInstr(r0)
    //     0xbfefa0: ldur            x3, [x0, #-1]
    //     0xbfefa4: ubfx            x3, x3, #0xc, #0x14
    // 0xbfefa8: ldr             x16, [fp, #0x10]
    // 0xbfefac: stp             x16, x0, [SP]
    // 0xbfefb0: mov             x0, x3
    // 0xbfefb4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbfefb4: sub             lr, x0, #0xb7
    //     0xbfefb8: ldr             lr, [x21, lr, lsl #3]
    //     0xbfefbc: blr             lr
    // 0xbfefc0: cmp             w0, NULL
    // 0xbfefc4: b.ne            #0xbfefd0
    // 0xbfefc8: r2 = Null
    //     0xbfefc8: mov             x2, NULL
    // 0xbfefcc: b               #0xbfeff4
    // 0xbfefd0: LoadField: r1 = r0->field_eb
    //     0xbfefd0: ldur            w1, [x0, #0xeb]
    // 0xbfefd4: DecompressPointer r1
    //     0xbfefd4: add             x1, x1, HEAP, lsl #32
    // 0xbfefd8: cmp             w1, NULL
    // 0xbfefdc: b.ne            #0xbfefe8
    // 0xbfefe0: r0 = Null
    //     0xbfefe0: mov             x0, NULL
    // 0xbfefe4: b               #0xbfeff0
    // 0xbfefe8: LoadField: r0 = r1->field_13
    //     0xbfefe8: ldur            w0, [x1, #0x13]
    // 0xbfefec: DecompressPointer r0
    //     0xbfefec: add             x0, x0, HEAP, lsl #32
    // 0xbfeff0: mov             x2, x0
    // 0xbfeff4: ldur            x1, [fp, #-0x18]
    // 0xbfeff8: stur            x2, [fp, #-0x38]
    // 0xbfeffc: LoadField: r0 = r1->field_f
    //     0xbfeffc: ldur            w0, [x1, #0xf]
    // 0xbff000: DecompressPointer r0
    //     0xbff000: add             x0, x0, HEAP, lsl #32
    // 0xbff004: LoadField: r3 = r0->field_b
    //     0xbff004: ldur            w3, [x0, #0xb]
    // 0xbff008: DecompressPointer r3
    //     0xbff008: add             x3, x3, HEAP, lsl #32
    // 0xbff00c: cmp             w3, NULL
    // 0xbff010: b.eq            #0xbff2a0
    // 0xbff014: LoadField: r0 = r3->field_b
    //     0xbff014: ldur            w0, [x3, #0xb]
    // 0xbff018: DecompressPointer r0
    //     0xbff018: add             x0, x0, HEAP, lsl #32
    // 0xbff01c: r3 = LoadClassIdInstr(r0)
    //     0xbff01c: ldur            x3, [x0, #-1]
    //     0xbff020: ubfx            x3, x3, #0xc, #0x14
    // 0xbff024: ldr             x16, [fp, #0x10]
    // 0xbff028: stp             x16, x0, [SP]
    // 0xbff02c: mov             x0, x3
    // 0xbff030: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbff030: sub             lr, x0, #0xb7
    //     0xbff034: ldr             lr, [x21, lr, lsl #3]
    //     0xbff038: blr             lr
    // 0xbff03c: cmp             w0, NULL
    // 0xbff040: b.ne            #0xbff04c
    // 0xbff044: r1 = Null
    //     0xbff044: mov             x1, NULL
    // 0xbff048: b               #0xbff054
    // 0xbff04c: LoadField: r1 = r0->field_e3
    //     0xbff04c: ldur            w1, [x0, #0xe3]
    // 0xbff050: DecompressPointer r1
    //     0xbff050: add             x1, x1, HEAP, lsl #32
    // 0xbff054: ldur            x0, [fp, #-8]
    // 0xbff058: LoadField: r2 = r0->field_33
    //     0xbff058: ldur            w2, [x0, #0x33]
    // 0xbff05c: DecompressPointer r2
    //     0xbff05c: add             x2, x2, HEAP, lsl #32
    // 0xbff060: ldur            x16, [fp, #-0x20]
    // 0xbff064: stp             x16, x2, [SP, #0x20]
    // 0xbff068: ldur            x16, [fp, #-0x28]
    // 0xbff06c: ldur            lr, [fp, #-0x30]
    // 0xbff070: stp             lr, x16, [SP, #0x10]
    // 0xbff074: ldur            x16, [fp, #-0x38]
    // 0xbff078: stp             x1, x16, [SP]
    // 0xbff07c: r4 = 0
    //     0xbff07c: movz            x4, #0
    // 0xbff080: ldr             x0, [SP, #0x28]
    // 0xbff084: r16 = UnlinkedCall_0x613b5c
    //     0xbff084: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d30] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbff088: add             x16, x16, #0xd30
    // 0xbff08c: ldp             x5, lr, [x16]
    // 0xbff090: blr             lr
    // 0xbff094: ldur            x1, [fp, #-0x18]
    // 0xbff098: LoadField: r2 = r1->field_f
    //     0xbff098: ldur            w2, [x1, #0xf]
    // 0xbff09c: DecompressPointer r2
    //     0xbff09c: add             x2, x2, HEAP, lsl #32
    // 0xbff0a0: stur            x2, [fp, #-8]
    // 0xbff0a4: LoadField: r0 = r2->field_b
    //     0xbff0a4: ldur            w0, [x2, #0xb]
    // 0xbff0a8: DecompressPointer r0
    //     0xbff0a8: add             x0, x0, HEAP, lsl #32
    // 0xbff0ac: cmp             w0, NULL
    // 0xbff0b0: b.eq            #0xbff2a4
    // 0xbff0b4: LoadField: r3 = r0->field_b
    //     0xbff0b4: ldur            w3, [x0, #0xb]
    // 0xbff0b8: DecompressPointer r3
    //     0xbff0b8: add             x3, x3, HEAP, lsl #32
    // 0xbff0bc: r0 = LoadClassIdInstr(r3)
    //     0xbff0bc: ldur            x0, [x3, #-1]
    //     0xbff0c0: ubfx            x0, x0, #0xc, #0x14
    // 0xbff0c4: ldr             x16, [fp, #0x10]
    // 0xbff0c8: stp             x16, x3, [SP]
    // 0xbff0cc: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbff0cc: sub             lr, x0, #0xb7
    //     0xbff0d0: ldr             lr, [x21, lr, lsl #3]
    //     0xbff0d4: blr             lr
    // 0xbff0d8: ldur            x1, [fp, #-8]
    // 0xbff0dc: mov             x2, x0
    // 0xbff0e0: ldur            x3, [fp, #-0x10]
    // 0xbff0e4: r0 = lineThemeSlider()
    //     0xbff0e4: bl              #0xa75c38  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::lineThemeSlider
    // 0xbff0e8: mov             x1, x0
    // 0xbff0ec: ldur            x0, [fp, #-0x18]
    // 0xbff0f0: stur            x1, [fp, #-8]
    // 0xbff0f4: LoadField: r2 = r0->field_f
    //     0xbff0f4: ldur            w2, [x0, #0xf]
    // 0xbff0f8: DecompressPointer r2
    //     0xbff0f8: add             x2, x2, HEAP, lsl #32
    // 0xbff0fc: LoadField: r0 = r2->field_b
    //     0xbff0fc: ldur            w0, [x2, #0xb]
    // 0xbff100: DecompressPointer r0
    //     0xbff100: add             x0, x0, HEAP, lsl #32
    // 0xbff104: cmp             w0, NULL
    // 0xbff108: b.eq            #0xbff2a8
    // 0xbff10c: LoadField: r2 = r0->field_b
    //     0xbff10c: ldur            w2, [x0, #0xb]
    // 0xbff110: DecompressPointer r2
    //     0xbff110: add             x2, x2, HEAP, lsl #32
    // 0xbff114: r0 = LoadClassIdInstr(r2)
    //     0xbff114: ldur            x0, [x2, #-1]
    //     0xbff118: ubfx            x0, x0, #0xc, #0x14
    // 0xbff11c: ldr             x16, [fp, #0x10]
    // 0xbff120: stp             x16, x2, [SP]
    // 0xbff124: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbff124: sub             lr, x0, #0xb7
    //     0xbff128: ldr             lr, [x21, lr, lsl #3]
    //     0xbff12c: blr             lr
    // 0xbff130: cmp             w0, NULL
    // 0xbff134: b.ne            #0xbff140
    // 0xbff138: r0 = Null
    //     0xbff138: mov             x0, NULL
    // 0xbff13c: b               #0xbff150
    // 0xbff140: r17 = 315
    //     0xbff140: movz            x17, #0x13b
    // 0xbff144: ldr             w1, [x0, x17]
    // 0xbff148: DecompressPointer r1
    //     0xbff148: add             x1, x1, HEAP, lsl #32
    // 0xbff14c: mov             x0, x1
    // 0xbff150: cmp             w0, NULL
    // 0xbff154: b.ne            #0xbff160
    // 0xbff158: r1 = false
    //     0xbff158: add             x1, NULL, #0x30  ; false
    // 0xbff15c: b               #0xbff164
    // 0xbff160: mov             x1, x0
    // 0xbff164: ldur            x0, [fp, #-8]
    // 0xbff168: stur            x1, [fp, #-0x18]
    // 0xbff16c: r0 = SvgPicture()
    //     0xbff16c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbff170: mov             x1, x0
    // 0xbff174: r2 = "assets/images/free-gift-icon.svg"
    //     0xbff174: add             x2, PP, #0x52, lsl #12  ; [pp+0x52d40] "assets/images/free-gift-icon.svg"
    //     0xbff178: ldr             x2, [x2, #0xd40]
    // 0xbff17c: stur            x0, [fp, #-0x20]
    // 0xbff180: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbff180: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbff184: r0 = SvgPicture.asset()
    //     0xbff184: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbff188: r0 = Padding()
    //     0xbff188: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbff18c: mov             x1, x0
    // 0xbff190: r0 = Instance_EdgeInsets
    //     0xbff190: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d48] Obj!EdgeInsets@d584c1
    //     0xbff194: ldr             x0, [x0, #0xd48]
    // 0xbff198: stur            x1, [fp, #-0x28]
    // 0xbff19c: StoreField: r1->field_f = r0
    //     0xbff19c: stur            w0, [x1, #0xf]
    // 0xbff1a0: ldur            x0, [fp, #-0x20]
    // 0xbff1a4: StoreField: r1->field_b = r0
    //     0xbff1a4: stur            w0, [x1, #0xb]
    // 0xbff1a8: r0 = Visibility()
    //     0xbff1a8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbff1ac: mov             x3, x0
    // 0xbff1b0: ldur            x0, [fp, #-0x28]
    // 0xbff1b4: stur            x3, [fp, #-0x20]
    // 0xbff1b8: StoreField: r3->field_b = r0
    //     0xbff1b8: stur            w0, [x3, #0xb]
    // 0xbff1bc: r0 = Instance_SizedBox
    //     0xbff1bc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbff1c0: StoreField: r3->field_f = r0
    //     0xbff1c0: stur            w0, [x3, #0xf]
    // 0xbff1c4: ldur            x0, [fp, #-0x18]
    // 0xbff1c8: StoreField: r3->field_13 = r0
    //     0xbff1c8: stur            w0, [x3, #0x13]
    // 0xbff1cc: r0 = false
    //     0xbff1cc: add             x0, NULL, #0x30  ; false
    // 0xbff1d0: ArrayStore: r3[0] = r0  ; List_4
    //     0xbff1d0: stur            w0, [x3, #0x17]
    // 0xbff1d4: StoreField: r3->field_1b = r0
    //     0xbff1d4: stur            w0, [x3, #0x1b]
    // 0xbff1d8: StoreField: r3->field_1f = r0
    //     0xbff1d8: stur            w0, [x3, #0x1f]
    // 0xbff1dc: StoreField: r3->field_23 = r0
    //     0xbff1dc: stur            w0, [x3, #0x23]
    // 0xbff1e0: StoreField: r3->field_27 = r0
    //     0xbff1e0: stur            w0, [x3, #0x27]
    // 0xbff1e4: StoreField: r3->field_2b = r0
    //     0xbff1e4: stur            w0, [x3, #0x2b]
    // 0xbff1e8: r1 = Null
    //     0xbff1e8: mov             x1, NULL
    // 0xbff1ec: r2 = 4
    //     0xbff1ec: movz            x2, #0x4
    // 0xbff1f0: r0 = AllocateArray()
    //     0xbff1f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbff1f4: mov             x2, x0
    // 0xbff1f8: ldur            x0, [fp, #-8]
    // 0xbff1fc: stur            x2, [fp, #-0x18]
    // 0xbff200: StoreField: r2->field_f = r0
    //     0xbff200: stur            w0, [x2, #0xf]
    // 0xbff204: ldur            x0, [fp, #-0x20]
    // 0xbff208: StoreField: r2->field_13 = r0
    //     0xbff208: stur            w0, [x2, #0x13]
    // 0xbff20c: r1 = <Widget>
    //     0xbff20c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbff210: r0 = AllocateGrowableArray()
    //     0xbff210: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbff214: mov             x1, x0
    // 0xbff218: ldur            x0, [fp, #-0x18]
    // 0xbff21c: stur            x1, [fp, #-8]
    // 0xbff220: StoreField: r1->field_f = r0
    //     0xbff220: stur            w0, [x1, #0xf]
    // 0xbff224: r0 = 4
    //     0xbff224: movz            x0, #0x4
    // 0xbff228: StoreField: r1->field_b = r0
    //     0xbff228: stur            w0, [x1, #0xb]
    // 0xbff22c: r0 = Stack()
    //     0xbff22c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbff230: mov             x1, x0
    // 0xbff234: r0 = Instance_Alignment
    //     0xbff234: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xbff238: ldr             x0, [x0, #0x950]
    // 0xbff23c: stur            x1, [fp, #-0x18]
    // 0xbff240: StoreField: r1->field_f = r0
    //     0xbff240: stur            w0, [x1, #0xf]
    // 0xbff244: r0 = Instance_StackFit
    //     0xbff244: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbff248: ldr             x0, [x0, #0xfa8]
    // 0xbff24c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbff24c: stur            w0, [x1, #0x17]
    // 0xbff250: r0 = Instance_Clip
    //     0xbff250: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbff254: ldr             x0, [x0, #0x7e0]
    // 0xbff258: StoreField: r1->field_1b = r0
    //     0xbff258: stur            w0, [x1, #0x1b]
    // 0xbff25c: ldur            x0, [fp, #-8]
    // 0xbff260: StoreField: r1->field_b = r0
    //     0xbff260: stur            w0, [x1, #0xb]
    // 0xbff264: r0 = Padding()
    //     0xbff264: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbff268: r1 = Instance_EdgeInsets
    //     0xbff268: add             x1, PP, #0x52, lsl #12  ; [pp+0x52018] Obj!EdgeInsets@d586d1
    //     0xbff26c: ldr             x1, [x1, #0x18]
    // 0xbff270: StoreField: r0->field_f = r1
    //     0xbff270: stur            w1, [x0, #0xf]
    // 0xbff274: ldur            x1, [fp, #-0x18]
    // 0xbff278: StoreField: r0->field_b = r1
    //     0xbff278: stur            w1, [x0, #0xb]
    // 0xbff27c: LeaveFrame
    //     0xbff27c: mov             SP, fp
    //     0xbff280: ldp             fp, lr, [SP], #0x10
    // 0xbff284: ret
    //     0xbff284: ret             
    // 0xbff288: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbff288: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbff28c: b               #0xbfee04
    // 0xbff290: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbff290: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbff294: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbff294: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbff298: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbff298: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbff29c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbff29c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbff2a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbff2a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbff2a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbff2a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbff2a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbff2a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xbff2ac, size: 0x84
    // 0xbff2ac: EnterFrame
    //     0xbff2ac: stp             fp, lr, [SP, #-0x10]!
    //     0xbff2b0: mov             fp, SP
    // 0xbff2b4: AllocStack(0x10)
    //     0xbff2b4: sub             SP, SP, #0x10
    // 0xbff2b8: SetupParameters()
    //     0xbff2b8: ldr             x0, [fp, #0x18]
    //     0xbff2bc: ldur            w1, [x0, #0x17]
    //     0xbff2c0: add             x1, x1, HEAP, lsl #32
    //     0xbff2c4: stur            x1, [fp, #-8]
    // 0xbff2c8: CheckStackOverflow
    //     0xbff2c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbff2cc: cmp             SP, x16
    //     0xbff2d0: b.ls            #0xbff328
    // 0xbff2d4: r1 = 1
    //     0xbff2d4: movz            x1, #0x1
    // 0xbff2d8: r0 = AllocateContext()
    //     0xbff2d8: bl              #0x16f6108  ; AllocateContextStub
    // 0xbff2dc: mov             x1, x0
    // 0xbff2e0: ldur            x0, [fp, #-8]
    // 0xbff2e4: StoreField: r1->field_b = r0
    //     0xbff2e4: stur            w0, [x1, #0xb]
    // 0xbff2e8: ldr             x2, [fp, #0x10]
    // 0xbff2ec: StoreField: r1->field_f = r2
    //     0xbff2ec: stur            w2, [x1, #0xf]
    // 0xbff2f0: LoadField: r3 = r0->field_f
    //     0xbff2f0: ldur            w3, [x0, #0xf]
    // 0xbff2f4: DecompressPointer r3
    //     0xbff2f4: add             x3, x3, HEAP, lsl #32
    // 0xbff2f8: mov             x2, x1
    // 0xbff2fc: stur            x3, [fp, #-0x10]
    // 0xbff300: r1 = Function '<anonymous closure>':.
    //     0xbff300: add             x1, PP, #0x52, lsl #12  ; [pp+0x52e88] AnonymousClosure: (0xa59828), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildMediaCarousel (0xa598e8)
    //     0xbff304: ldr             x1, [x1, #0xe88]
    // 0xbff308: r0 = AllocateClosure()
    //     0xbff308: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbff30c: ldur            x1, [fp, #-0x10]
    // 0xbff310: mov             x2, x0
    // 0xbff314: r0 = setState()
    //     0xbff314: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbff318: r0 = Null
    //     0xbff318: mov             x0, NULL
    // 0xbff31c: LeaveFrame
    //     0xbff31c: mov             SP, fp
    //     0xbff320: ldp             fp, lr, [SP], #0x10
    // 0xbff324: ret
    //     0xbff324: ret             
    // 0xbff328: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbff328: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbff32c: b               #0xbff2d4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbff330, size: 0xd4
    // 0xbff330: EnterFrame
    //     0xbff330: stp             fp, lr, [SP, #-0x10]!
    //     0xbff334: mov             fp, SP
    // 0xbff338: AllocStack(0x38)
    //     0xbff338: sub             SP, SP, #0x38
    // 0xbff33c: SetupParameters()
    //     0xbff33c: ldr             x0, [fp, #0x10]
    //     0xbff340: ldur            w1, [x0, #0x17]
    //     0xbff344: add             x1, x1, HEAP, lsl #32
    // 0xbff348: CheckStackOverflow
    //     0xbff348: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbff34c: cmp             SP, x16
    //     0xbff350: b.ls            #0xbff3f8
    // 0xbff354: LoadField: r0 = r1->field_f
    //     0xbff354: ldur            w0, [x1, #0xf]
    // 0xbff358: DecompressPointer r0
    //     0xbff358: add             x0, x0, HEAP, lsl #32
    // 0xbff35c: LoadField: r1 = r0->field_b
    //     0xbff35c: ldur            w1, [x0, #0xb]
    // 0xbff360: DecompressPointer r1
    //     0xbff360: add             x1, x1, HEAP, lsl #32
    // 0xbff364: cmp             w1, NULL
    // 0xbff368: b.eq            #0xbff400
    // 0xbff36c: LoadField: r0 = r1->field_23
    //     0xbff36c: ldur            w0, [x1, #0x23]
    // 0xbff370: DecompressPointer r0
    //     0xbff370: add             x0, x0, HEAP, lsl #32
    // 0xbff374: LoadField: r2 = r1->field_1f
    //     0xbff374: ldur            w2, [x1, #0x1f]
    // 0xbff378: DecompressPointer r2
    //     0xbff378: add             x2, x2, HEAP, lsl #32
    // 0xbff37c: LoadField: r3 = r1->field_27
    //     0xbff37c: ldur            w3, [x1, #0x27]
    // 0xbff380: DecompressPointer r3
    //     0xbff380: add             x3, x3, HEAP, lsl #32
    // 0xbff384: LoadField: r4 = r1->field_13
    //     0xbff384: ldur            w4, [x1, #0x13]
    // 0xbff388: DecompressPointer r4
    //     0xbff388: add             x4, x4, HEAP, lsl #32
    // 0xbff38c: cmp             w4, NULL
    // 0xbff390: b.ne            #0xbff39c
    // 0xbff394: r4 = Null
    //     0xbff394: mov             x4, NULL
    // 0xbff398: b               #0xbff3a8
    // 0xbff39c: LoadField: r5 = r4->field_b
    //     0xbff39c: ldur            w5, [x4, #0xb]
    // 0xbff3a0: DecompressPointer r5
    //     0xbff3a0: add             x5, x5, HEAP, lsl #32
    // 0xbff3a4: mov             x4, x5
    // 0xbff3a8: LoadField: r5 = r1->field_f
    //     0xbff3a8: ldur            w5, [x1, #0xf]
    // 0xbff3ac: DecompressPointer r5
    //     0xbff3ac: add             x5, x5, HEAP, lsl #32
    // 0xbff3b0: LoadField: r6 = r1->field_3b
    //     0xbff3b0: ldur            w6, [x1, #0x3b]
    // 0xbff3b4: DecompressPointer r6
    //     0xbff3b4: add             x6, x6, HEAP, lsl #32
    // 0xbff3b8: stp             x0, x6, [SP, #0x28]
    // 0xbff3bc: r16 = "product_page"
    //     0xbff3bc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xbff3c0: ldr             x16, [x16, #0x480]
    // 0xbff3c4: stp             x16, x2, [SP, #0x18]
    // 0xbff3c8: stp             x4, x3, [SP, #8]
    // 0xbff3cc: str             x5, [SP]
    // 0xbff3d0: r4 = 0
    //     0xbff3d0: movz            x4, #0
    // 0xbff3d4: ldr             x0, [SP, #0x30]
    // 0xbff3d8: r16 = UnlinkedCall_0x613b5c
    //     0xbff3d8: add             x16, PP, #0x52, lsl #12  ; [pp+0x52e90] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbff3dc: add             x16, x16, #0xe90
    // 0xbff3e0: ldp             x5, lr, [x16]
    // 0xbff3e4: blr             lr
    // 0xbff3e8: r0 = Null
    //     0xbff3e8: mov             x0, NULL
    // 0xbff3ec: LeaveFrame
    //     0xbff3ec: mov             SP, fp
    //     0xbff3f0: ldp             fp, lr, [SP], #0x10
    // 0xbff3f4: ret
    //     0xbff3f4: ret             
    // 0xbff3f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbff3f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbff3fc: b               #0xbff354
    // 0xbff400: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbff400: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc88434, size: 0x84
    // 0xc88434: EnterFrame
    //     0xc88434: stp             fp, lr, [SP, #-0x10]!
    //     0xc88438: mov             fp, SP
    // 0xc8843c: AllocStack(0x8)
    //     0xc8843c: sub             SP, SP, #8
    // 0xc88440: SetupParameters(_GroupCarouselItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xc88440: mov             x0, x1
    //     0xc88444: stur            x1, [fp, #-8]
    // 0xc88448: CheckStackOverflow
    //     0xc88448: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc8844c: cmp             SP, x16
    //     0xc88450: b.ls            #0xc88498
    // 0xc88454: LoadField: r1 = r0->field_13
    //     0xc88454: ldur            w1, [x0, #0x13]
    // 0xc88458: DecompressPointer r1
    //     0xc88458: add             x1, x1, HEAP, lsl #32
    // 0xc8845c: r16 = Sentinel
    //     0xc8845c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc88460: cmp             w1, w16
    // 0xc88464: b.eq            #0xc884a0
    // 0xc88468: r0 = dispose()
    //     0xc88468: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc8846c: ldur            x0, [fp, #-8]
    // 0xc88470: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc88470: ldur            w1, [x0, #0x17]
    // 0xc88474: DecompressPointer r1
    //     0xc88474: add             x1, x1, HEAP, lsl #32
    // 0xc88478: r16 = Sentinel
    //     0xc88478: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc8847c: cmp             w1, w16
    // 0xc88480: b.eq            #0xc884ac
    // 0xc88484: r0 = dispose()
    //     0xc88484: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc88488: r0 = Null
    //     0xc88488: mov             x0, NULL
    // 0xc8848c: LeaveFrame
    //     0xc8848c: mov             SP, fp
    //     0xc88490: ldp             fp, lr, [SP], #0x10
    // 0xc88494: ret
    //     0xc88494: ret             
    // 0xc88498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc88498: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc8849c: b               #0xc88454
    // 0xc884a0: r9 = _pageController
    //     0xc884a0: add             x9, PP, #0x52, lsl #12  ; [pp+0x52d28] Field <_GroupCarouselItemViewState@1732154658._pageController@1732154658>: late (offset: 0x14)
    //     0xc884a4: ldr             x9, [x9, #0xd28]
    // 0xc884a8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc884a8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc884ac: r9 = _imagePageController
    //     0xc884ac: add             x9, PP, #0x52, lsl #12  ; [pp+0x52de8] Field <_GroupCarouselItemViewState@1732154658._imagePageController@1732154658>: late (offset: 0x18)
    //     0xc884b0: ldr             x9, [x9, #0xde8]
    // 0xc884b4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc884b4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 3977, size: 0x48, field offset: 0xc
//   const constructor, 
class GroupCarouselItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80eb0, size: 0x34
    // 0xc80eb0: EnterFrame
    //     0xc80eb0: stp             fp, lr, [SP, #-0x10]!
    //     0xc80eb4: mov             fp, SP
    // 0xc80eb8: mov             x0, x1
    // 0xc80ebc: r1 = <GroupCarouselItemView>
    //     0xc80ebc: add             x1, PP, #0x48, lsl #12  ; [pp+0x48388] TypeArguments: <GroupCarouselItemView>
    //     0xc80ec0: ldr             x1, [x1, #0x388]
    // 0xc80ec4: r0 = _GroupCarouselItemViewState()
    //     0xc80ec4: bl              #0xc80ee4  ; Allocate_GroupCarouselItemViewStateStub -> _GroupCarouselItemViewState (size=0x24)
    // 0xc80ec8: r1 = Sentinel
    //     0xc80ec8: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc80ecc: StoreField: r0->field_13 = r1
    //     0xc80ecc: stur            w1, [x0, #0x13]
    // 0xc80ed0: ArrayStore: r0[0] = r1  ; List_4
    //     0xc80ed0: stur            w1, [x0, #0x17]
    // 0xc80ed4: StoreField: r0->field_1b = rZR
    //     0xc80ed4: stur            xzr, [x0, #0x1b]
    // 0xc80ed8: LeaveFrame
    //     0xc80ed8: mov             SP, fp
    //     0xc80edc: ldp             fp, lr, [SP], #0x10
    // 0xc80ee0: ret
    //     0xc80ee0: ret             
  }
}
