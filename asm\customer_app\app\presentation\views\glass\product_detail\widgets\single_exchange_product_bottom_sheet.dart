// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/single_exchange_product_bottom_sheet.dart

// class id: 1049448, size: 0x8
class :: {
}

// class id: 3304, size: 0x18, field offset: 0x14
class _SingleExchangeProductBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb91378, size: 0x239c
    // 0xb91378: EnterFrame
    //     0xb91378: stp             fp, lr, [SP, #-0x10]!
    //     0xb9137c: mov             fp, SP
    // 0xb91380: AllocStack(0xa0)
    //     0xb91380: sub             SP, SP, #0xa0
    // 0xb91384: SetupParameters(_SingleExchangeProductBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0xb91384: stur            x1, [fp, #-8]
    // 0xb91388: CheckStackOverflow
    //     0xb91388: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9138c: cmp             SP, x16
    //     0xb91390: b.ls            #0xb936c8
    // 0xb91394: r1 = 1
    //     0xb91394: movz            x1, #0x1
    // 0xb91398: r0 = AllocateContext()
    //     0xb91398: bl              #0x16f6108  ; AllocateContextStub
    // 0xb9139c: mov             x1, x0
    // 0xb913a0: ldur            x0, [fp, #-8]
    // 0xb913a4: stur            x1, [fp, #-0x10]
    // 0xb913a8: StoreField: r1->field_f = r0
    //     0xb913a8: stur            w0, [x1, #0xf]
    // 0xb913ac: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0xb913ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb913b0: ldr             x0, [x0, #0x1ab0]
    //     0xb913b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb913b8: cmp             w0, w16
    //     0xb913bc: b.ne            #0xb913cc
    //     0xb913c0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0xb913c4: ldr             x2, [x2, #0x60]
    //     0xb913c8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb913cc: LoadField: r2 = r0->field_87
    //     0xb913cc: ldur            w2, [x0, #0x87]
    // 0xb913d0: DecompressPointer r2
    //     0xb913d0: add             x2, x2, HEAP, lsl #32
    // 0xb913d4: stur            x2, [fp, #-0x20]
    // 0xb913d8: LoadField: r0 = r2->field_7
    //     0xb913d8: ldur            w0, [x2, #7]
    // 0xb913dc: DecompressPointer r0
    //     0xb913dc: add             x0, x0, HEAP, lsl #32
    // 0xb913e0: stur            x0, [fp, #-0x18]
    // 0xb913e4: r16 = Instance_Color
    //     0xb913e4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb913e8: r30 = 16.000000
    //     0xb913e8: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb913ec: ldr             lr, [lr, #0x188]
    // 0xb913f0: stp             lr, x16, [SP]
    // 0xb913f4: mov             x1, x0
    // 0xb913f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb913f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb913fc: ldr             x4, [x4, #0x9b8]
    // 0xb91400: r0 = copyWith()
    //     0xb91400: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb91404: stur            x0, [fp, #-0x28]
    // 0xb91408: r0 = Text()
    //     0xb91408: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9140c: mov             x3, x0
    // 0xb91410: r0 = "Ready for exchange\?"
    //     0xb91410: add             x0, PP, #0x55, lsl #12  ; [pp+0x55ff8] "Ready for exchange\?"
    //     0xb91414: ldr             x0, [x0, #0xff8]
    // 0xb91418: stur            x3, [fp, #-0x30]
    // 0xb9141c: StoreField: r3->field_b = r0
    //     0xb9141c: stur            w0, [x3, #0xb]
    // 0xb91420: ldur            x0, [fp, #-0x28]
    // 0xb91424: StoreField: r3->field_13 = r0
    //     0xb91424: stur            w0, [x3, #0x13]
    // 0xb91428: r1 = Function '<anonymous closure>':.
    //     0xb91428: add             x1, PP, #0x56, lsl #12  ; [pp+0x56000] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb9142c: ldr             x1, [x1]
    // 0xb91430: r2 = Null
    //     0xb91430: mov             x2, NULL
    // 0xb91434: r0 = AllocateClosure()
    //     0xb91434: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb91438: stur            x0, [fp, #-0x28]
    // 0xb9143c: r0 = IconButton()
    //     0xb9143c: bl              #0x9881fc  ; AllocateIconButtonStub -> IconButton (size=0x70)
    // 0xb91440: mov             x3, x0
    // 0xb91444: ldur            x0, [fp, #-0x28]
    // 0xb91448: stur            x3, [fp, #-0x38]
    // 0xb9144c: StoreField: r3->field_3b = r0
    //     0xb9144c: stur            w0, [x3, #0x3b]
    // 0xb91450: r0 = false
    //     0xb91450: add             x0, NULL, #0x30  ; false
    // 0xb91454: StoreField: r3->field_4f = r0
    //     0xb91454: stur            w0, [x3, #0x4f]
    // 0xb91458: r1 = Instance_Icon
    //     0xb91458: add             x1, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xb9145c: ldr             x1, [x1, #0x2b8]
    // 0xb91460: StoreField: r3->field_1f = r1
    //     0xb91460: stur            w1, [x3, #0x1f]
    // 0xb91464: r1 = Instance__IconButtonVariant
    //     0xb91464: add             x1, PP, #0x52, lsl #12  ; [pp+0x52900] Obj!_IconButtonVariant@d745e1
    //     0xb91468: ldr             x1, [x1, #0x900]
    // 0xb9146c: StoreField: r3->field_6b = r1
    //     0xb9146c: stur            w1, [x3, #0x6b]
    // 0xb91470: r1 = Null
    //     0xb91470: mov             x1, NULL
    // 0xb91474: r2 = 4
    //     0xb91474: movz            x2, #0x4
    // 0xb91478: r0 = AllocateArray()
    //     0xb91478: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9147c: mov             x2, x0
    // 0xb91480: ldur            x0, [fp, #-0x30]
    // 0xb91484: stur            x2, [fp, #-0x28]
    // 0xb91488: StoreField: r2->field_f = r0
    //     0xb91488: stur            w0, [x2, #0xf]
    // 0xb9148c: ldur            x0, [fp, #-0x38]
    // 0xb91490: StoreField: r2->field_13 = r0
    //     0xb91490: stur            w0, [x2, #0x13]
    // 0xb91494: r1 = <Widget>
    //     0xb91494: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb91498: r0 = AllocateGrowableArray()
    //     0xb91498: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9149c: mov             x1, x0
    // 0xb914a0: ldur            x0, [fp, #-0x28]
    // 0xb914a4: stur            x1, [fp, #-0x30]
    // 0xb914a8: StoreField: r1->field_f = r0
    //     0xb914a8: stur            w0, [x1, #0xf]
    // 0xb914ac: r2 = 4
    //     0xb914ac: movz            x2, #0x4
    // 0xb914b0: StoreField: r1->field_b = r2
    //     0xb914b0: stur            w2, [x1, #0xb]
    // 0xb914b4: r0 = Row()
    //     0xb914b4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb914b8: mov             x1, x0
    // 0xb914bc: r0 = Instance_Axis
    //     0xb914bc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb914c0: stur            x1, [fp, #-0x28]
    // 0xb914c4: StoreField: r1->field_f = r0
    //     0xb914c4: stur            w0, [x1, #0xf]
    // 0xb914c8: r2 = Instance_MainAxisAlignment
    //     0xb914c8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb914cc: ldr             x2, [x2, #0xa8]
    // 0xb914d0: StoreField: r1->field_13 = r2
    //     0xb914d0: stur            w2, [x1, #0x13]
    // 0xb914d4: r3 = Instance_MainAxisSize
    //     0xb914d4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb914d8: ldr             x3, [x3, #0xa10]
    // 0xb914dc: ArrayStore: r1[0] = r3  ; List_4
    //     0xb914dc: stur            w3, [x1, #0x17]
    // 0xb914e0: r4 = Instance_CrossAxisAlignment
    //     0xb914e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb914e4: ldr             x4, [x4, #0xa18]
    // 0xb914e8: StoreField: r1->field_1b = r4
    //     0xb914e8: stur            w4, [x1, #0x1b]
    // 0xb914ec: r5 = Instance_VerticalDirection
    //     0xb914ec: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb914f0: ldr             x5, [x5, #0xa20]
    // 0xb914f4: StoreField: r1->field_23 = r5
    //     0xb914f4: stur            w5, [x1, #0x23]
    // 0xb914f8: r6 = Instance_Clip
    //     0xb914f8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb914fc: ldr             x6, [x6, #0x38]
    // 0xb91500: StoreField: r1->field_2b = r6
    //     0xb91500: stur            w6, [x1, #0x2b]
    // 0xb91504: StoreField: r1->field_2f = rZR
    //     0xb91504: stur            xzr, [x1, #0x2f]
    // 0xb91508: ldur            x7, [fp, #-0x30]
    // 0xb9150c: StoreField: r1->field_b = r7
    //     0xb9150c: stur            w7, [x1, #0xb]
    // 0xb91510: r0 = Padding()
    //     0xb91510: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb91514: mov             x1, x0
    // 0xb91518: r0 = Instance_EdgeInsets
    //     0xb91518: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb9151c: ldr             x0, [x0, #0x668]
    // 0xb91520: stur            x1, [fp, #-0x30]
    // 0xb91524: StoreField: r1->field_f = r0
    //     0xb91524: stur            w0, [x1, #0xf]
    // 0xb91528: ldur            x2, [fp, #-0x28]
    // 0xb9152c: StoreField: r1->field_b = r2
    //     0xb9152c: stur            w2, [x1, #0xb]
    // 0xb91530: r0 = Radius()
    //     0xb91530: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb91534: d0 = 15.000000
    //     0xb91534: fmov            d0, #15.00000000
    // 0xb91538: stur            x0, [fp, #-0x28]
    // 0xb9153c: StoreField: r0->field_7 = d0
    //     0xb9153c: stur            d0, [x0, #7]
    // 0xb91540: StoreField: r0->field_f = d0
    //     0xb91540: stur            d0, [x0, #0xf]
    // 0xb91544: r0 = BorderRadius()
    //     0xb91544: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb91548: mov             x2, x0
    // 0xb9154c: ldur            x0, [fp, #-0x28]
    // 0xb91550: stur            x2, [fp, #-0x38]
    // 0xb91554: StoreField: r2->field_7 = r0
    //     0xb91554: stur            w0, [x2, #7]
    // 0xb91558: StoreField: r2->field_b = r0
    //     0xb91558: stur            w0, [x2, #0xb]
    // 0xb9155c: StoreField: r2->field_f = r0
    //     0xb9155c: stur            w0, [x2, #0xf]
    // 0xb91560: StoreField: r2->field_13 = r0
    //     0xb91560: stur            w0, [x2, #0x13]
    // 0xb91564: r1 = Instance_Color
    //     0xb91564: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb91568: d0 = 0.100000
    //     0xb91568: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb9156c: r0 = withOpacity()
    //     0xb9156c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb91570: mov             x2, x0
    // 0xb91574: r1 = Null
    //     0xb91574: mov             x1, NULL
    // 0xb91578: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb91578: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb9157c: r0 = Border.all()
    //     0xb9157c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb91580: stur            x0, [fp, #-0x28]
    // 0xb91584: r0 = BoxDecoration()
    //     0xb91584: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb91588: mov             x1, x0
    // 0xb9158c: ldur            x0, [fp, #-0x28]
    // 0xb91590: stur            x1, [fp, #-0x58]
    // 0xb91594: StoreField: r1->field_f = r0
    //     0xb91594: stur            w0, [x1, #0xf]
    // 0xb91598: ldur            x0, [fp, #-0x38]
    // 0xb9159c: StoreField: r1->field_13 = r0
    //     0xb9159c: stur            w0, [x1, #0x13]
    // 0xb915a0: r0 = Instance_BoxShape
    //     0xb915a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb915a4: ldr             x0, [x0, #0x80]
    // 0xb915a8: StoreField: r1->field_23 = r0
    //     0xb915a8: stur            w0, [x1, #0x23]
    // 0xb915ac: ldur            x2, [fp, #-8]
    // 0xb915b0: LoadField: r3 = r2->field_b
    //     0xb915b0: ldur            w3, [x2, #0xb]
    // 0xb915b4: DecompressPointer r3
    //     0xb915b4: add             x3, x3, HEAP, lsl #32
    // 0xb915b8: stur            x3, [fp, #-0x28]
    // 0xb915bc: cmp             w3, NULL
    // 0xb915c0: b.eq            #0xb936d0
    // 0xb915c4: LoadField: r4 = r3->field_2f
    //     0xb915c4: ldur            w4, [x3, #0x2f]
    // 0xb915c8: DecompressPointer r4
    //     0xb915c8: add             x4, x4, HEAP, lsl #32
    // 0xb915cc: LoadField: r5 = r4->field_3f
    //     0xb915cc: ldur            w5, [x4, #0x3f]
    // 0xb915d0: DecompressPointer r5
    //     0xb915d0: add             x5, x5, HEAP, lsl #32
    // 0xb915d4: cmp             w5, NULL
    // 0xb915d8: b.ne            #0xb915e4
    // 0xb915dc: r4 = Null
    //     0xb915dc: mov             x4, NULL
    // 0xb915e0: b               #0xb91608
    // 0xb915e4: ArrayLoad: r4 = r5[0]  ; List_4
    //     0xb915e4: ldur            w4, [x5, #0x17]
    // 0xb915e8: DecompressPointer r4
    //     0xb915e8: add             x4, x4, HEAP, lsl #32
    // 0xb915ec: cmp             w4, NULL
    // 0xb915f0: b.ne            #0xb915fc
    // 0xb915f4: r4 = Null
    //     0xb915f4: mov             x4, NULL
    // 0xb915f8: b               #0xb91608
    // 0xb915fc: LoadField: r6 = r4->field_7
    //     0xb915fc: ldur            w6, [x4, #7]
    // 0xb91600: DecompressPointer r6
    //     0xb91600: add             x6, x6, HEAP, lsl #32
    // 0xb91604: mov             x4, x6
    // 0xb91608: cmp             w4, NULL
    // 0xb9160c: b.ne            #0xb91618
    // 0xb91610: r4 = 0
    //     0xb91610: movz            x4, #0
    // 0xb91614: b               #0xb91628
    // 0xb91618: r6 = LoadInt32Instr(r4)
    //     0xb91618: sbfx            x6, x4, #1, #0x1f
    //     0xb9161c: tbz             w4, #0, #0xb91624
    //     0xb91620: ldur            x6, [x4, #7]
    // 0xb91624: mov             x4, x6
    // 0xb91628: stur            x4, [fp, #-0x50]
    // 0xb9162c: cmp             w5, NULL
    // 0xb91630: b.ne            #0xb9163c
    // 0xb91634: r6 = Null
    //     0xb91634: mov             x6, NULL
    // 0xb91638: b               #0xb91660
    // 0xb9163c: ArrayLoad: r6 = r5[0]  ; List_4
    //     0xb9163c: ldur            w6, [x5, #0x17]
    // 0xb91640: DecompressPointer r6
    //     0xb91640: add             x6, x6, HEAP, lsl #32
    // 0xb91644: cmp             w6, NULL
    // 0xb91648: b.ne            #0xb91654
    // 0xb9164c: r6 = Null
    //     0xb9164c: mov             x6, NULL
    // 0xb91650: b               #0xb91660
    // 0xb91654: LoadField: r7 = r6->field_b
    //     0xb91654: ldur            w7, [x6, #0xb]
    // 0xb91658: DecompressPointer r7
    //     0xb91658: add             x7, x7, HEAP, lsl #32
    // 0xb9165c: mov             x6, x7
    // 0xb91660: cmp             w6, NULL
    // 0xb91664: b.ne            #0xb91670
    // 0xb91668: r6 = 0
    //     0xb91668: movz            x6, #0
    // 0xb9166c: b               #0xb91680
    // 0xb91670: r7 = LoadInt32Instr(r6)
    //     0xb91670: sbfx            x7, x6, #1, #0x1f
    //     0xb91674: tbz             w6, #0, #0xb9167c
    //     0xb91678: ldur            x7, [x6, #7]
    // 0xb9167c: mov             x6, x7
    // 0xb91680: stur            x6, [fp, #-0x48]
    // 0xb91684: cmp             w5, NULL
    // 0xb91688: b.ne            #0xb91694
    // 0xb9168c: r5 = Null
    //     0xb9168c: mov             x5, NULL
    // 0xb91690: b               #0xb916b4
    // 0xb91694: ArrayLoad: r7 = r5[0]  ; List_4
    //     0xb91694: ldur            w7, [x5, #0x17]
    // 0xb91698: DecompressPointer r7
    //     0xb91698: add             x7, x7, HEAP, lsl #32
    // 0xb9169c: cmp             w7, NULL
    // 0xb916a0: b.ne            #0xb916ac
    // 0xb916a4: r5 = Null
    //     0xb916a4: mov             x5, NULL
    // 0xb916a8: b               #0xb916b4
    // 0xb916ac: LoadField: r5 = r7->field_f
    //     0xb916ac: ldur            w5, [x7, #0xf]
    // 0xb916b0: DecompressPointer r5
    //     0xb916b0: add             x5, x5, HEAP, lsl #32
    // 0xb916b4: cmp             w5, NULL
    // 0xb916b8: b.ne            #0xb916c4
    // 0xb916bc: r5 = 0
    //     0xb916bc: movz            x5, #0
    // 0xb916c0: b               #0xb916d4
    // 0xb916c4: r7 = LoadInt32Instr(r5)
    //     0xb916c4: sbfx            x7, x5, #1, #0x1f
    //     0xb916c8: tbz             w5, #0, #0xb916d0
    //     0xb916cc: ldur            x7, [x5, #7]
    // 0xb916d0: mov             x5, x7
    // 0xb916d4: stur            x5, [fp, #-0x40]
    // 0xb916d8: r0 = Color()
    //     0xb916d8: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb916dc: mov             x1, x0
    // 0xb916e0: r0 = Instance_ColorSpace
    //     0xb916e0: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb916e4: stur            x1, [fp, #-0x38]
    // 0xb916e8: StoreField: r1->field_27 = r0
    //     0xb916e8: stur            w0, [x1, #0x27]
    // 0xb916ec: d0 = 1.000000
    //     0xb916ec: fmov            d0, #1.00000000
    // 0xb916f0: StoreField: r1->field_7 = d0
    //     0xb916f0: stur            d0, [x1, #7]
    // 0xb916f4: ldur            x2, [fp, #-0x50]
    // 0xb916f8: ubfx            x2, x2, #0, #0x20
    // 0xb916fc: and             w3, w2, #0xff
    // 0xb91700: ubfx            x3, x3, #0, #0x20
    // 0xb91704: scvtf           d1, x3
    // 0xb91708: d2 = 255.000000
    //     0xb91708: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb9170c: fdiv            d3, d1, d2
    // 0xb91710: StoreField: r1->field_f = d3
    //     0xb91710: stur            d3, [x1, #0xf]
    // 0xb91714: ldur            x2, [fp, #-0x48]
    // 0xb91718: ubfx            x2, x2, #0, #0x20
    // 0xb9171c: and             w3, w2, #0xff
    // 0xb91720: ubfx            x3, x3, #0, #0x20
    // 0xb91724: scvtf           d1, x3
    // 0xb91728: fdiv            d3, d1, d2
    // 0xb9172c: ArrayStore: r1[0] = d3  ; List_8
    //     0xb9172c: stur            d3, [x1, #0x17]
    // 0xb91730: ldur            x2, [fp, #-0x40]
    // 0xb91734: ubfx            x2, x2, #0, #0x20
    // 0xb91738: and             w3, w2, #0xff
    // 0xb9173c: ubfx            x3, x3, #0, #0x20
    // 0xb91740: scvtf           d1, x3
    // 0xb91744: fdiv            d3, d1, d2
    // 0xb91748: StoreField: r1->field_1f = d3
    //     0xb91748: stur            d3, [x1, #0x1f]
    // 0xb9174c: r0 = BoxDecoration()
    //     0xb9174c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb91750: mov             x1, x0
    // 0xb91754: ldur            x0, [fp, #-0x38]
    // 0xb91758: stur            x1, [fp, #-0x60]
    // 0xb9175c: StoreField: r1->field_7 = r0
    //     0xb9175c: stur            w0, [x1, #7]
    // 0xb91760: r0 = Instance_BorderRadius
    //     0xb91760: add             x0, PP, #0x56, lsl #12  ; [pp+0x56008] Obj!BorderRadius@d5a361
    //     0xb91764: ldr             x0, [x0, #8]
    // 0xb91768: StoreField: r1->field_13 = r0
    //     0xb91768: stur            w0, [x1, #0x13]
    // 0xb9176c: r0 = Instance_BoxShape
    //     0xb9176c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb91770: ldr             x0, [x0, #0x80]
    // 0xb91774: StoreField: r1->field_23 = r0
    //     0xb91774: stur            w0, [x1, #0x23]
    // 0xb91778: r0 = Radius()
    //     0xb91778: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb9177c: d0 = 12.000000
    //     0xb9177c: fmov            d0, #12.00000000
    // 0xb91780: stur            x0, [fp, #-0x38]
    // 0xb91784: StoreField: r0->field_7 = d0
    //     0xb91784: stur            d0, [x0, #7]
    // 0xb91788: StoreField: r0->field_f = d0
    //     0xb91788: stur            d0, [x0, #0xf]
    // 0xb9178c: r0 = BorderRadius()
    //     0xb9178c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb91790: mov             x3, x0
    // 0xb91794: ldur            x0, [fp, #-0x38]
    // 0xb91798: stur            x3, [fp, #-0x68]
    // 0xb9179c: StoreField: r3->field_7 = r0
    //     0xb9179c: stur            w0, [x3, #7]
    // 0xb917a0: StoreField: r3->field_b = r0
    //     0xb917a0: stur            w0, [x3, #0xb]
    // 0xb917a4: StoreField: r3->field_f = r0
    //     0xb917a4: stur            w0, [x3, #0xf]
    // 0xb917a8: StoreField: r3->field_13 = r0
    //     0xb917a8: stur            w0, [x3, #0x13]
    // 0xb917ac: ldur            x0, [fp, #-0x28]
    // 0xb917b0: LoadField: r1 = r0->field_b
    //     0xb917b0: ldur            w1, [x0, #0xb]
    // 0xb917b4: DecompressPointer r1
    //     0xb917b4: add             x1, x1, HEAP, lsl #32
    // 0xb917b8: cmp             w1, NULL
    // 0xb917bc: b.ne            #0xb917c8
    // 0xb917c0: r0 = Null
    //     0xb917c0: mov             x0, NULL
    // 0xb917c4: b               #0xb91800
    // 0xb917c8: LoadField: r0 = r1->field_b
    //     0xb917c8: ldur            w0, [x1, #0xb]
    // 0xb917cc: DecompressPointer r0
    //     0xb917cc: add             x0, x0, HEAP, lsl #32
    // 0xb917d0: cmp             w0, NULL
    // 0xb917d4: b.ne            #0xb917e0
    // 0xb917d8: r0 = Null
    //     0xb917d8: mov             x0, NULL
    // 0xb917dc: b               #0xb91800
    // 0xb917e0: LoadField: r1 = r0->field_7
    //     0xb917e0: ldur            w1, [x0, #7]
    // 0xb917e4: DecompressPointer r1
    //     0xb917e4: add             x1, x1, HEAP, lsl #32
    // 0xb917e8: cmp             w1, NULL
    // 0xb917ec: b.ne            #0xb917f8
    // 0xb917f0: r0 = Null
    //     0xb917f0: mov             x0, NULL
    // 0xb917f4: b               #0xb91800
    // 0xb917f8: LoadField: r0 = r1->field_7
    //     0xb917f8: ldur            w0, [x1, #7]
    // 0xb917fc: DecompressPointer r0
    //     0xb917fc: add             x0, x0, HEAP, lsl #32
    // 0xb91800: cmp             w0, NULL
    // 0xb91804: b.ne            #0xb91810
    // 0xb91808: r4 = ""
    //     0xb91808: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb9180c: b               #0xb91814
    // 0xb91810: mov             x4, x0
    // 0xb91814: ldur            x0, [fp, #-8]
    // 0xb91818: stur            x4, [fp, #-0x28]
    // 0xb9181c: r1 = Function '<anonymous closure>':.
    //     0xb9181c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56010] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb91820: ldr             x1, [x1, #0x10]
    // 0xb91824: r2 = Null
    //     0xb91824: mov             x2, NULL
    // 0xb91828: r0 = AllocateClosure()
    //     0xb91828: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9182c: stur            x0, [fp, #-0x38]
    // 0xb91830: r0 = CachedNetworkImage()
    //     0xb91830: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb91834: stur            x0, [fp, #-0x70]
    // 0xb91838: r16 = 80.000000
    //     0xb91838: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xb9183c: ldr             x16, [x16, #0x2f8]
    // 0xb91840: r30 = 80.000000
    //     0xb91840: add             lr, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xb91844: ldr             lr, [lr, #0x2f8]
    // 0xb91848: stp             lr, x16, [SP, #0x10]
    // 0xb9184c: r16 = Instance_BoxFit
    //     0xb9184c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb91850: ldr             x16, [x16, #0x118]
    // 0xb91854: ldur            lr, [fp, #-0x38]
    // 0xb91858: stp             lr, x16, [SP]
    // 0xb9185c: mov             x1, x0
    // 0xb91860: ldur            x2, [fp, #-0x28]
    // 0xb91864: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x4, height, 0x2, width, 0x3, null]
    //     0xb91864: add             x4, PP, #0x56, lsl #12  ; [pp+0x56018] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0xb91868: ldr             x4, [x4, #0x18]
    // 0xb9186c: r0 = CachedNetworkImage()
    //     0xb9186c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb91870: r0 = ClipRRect()
    //     0xb91870: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb91874: mov             x2, x0
    // 0xb91878: ldur            x0, [fp, #-0x68]
    // 0xb9187c: stur            x2, [fp, #-0x38]
    // 0xb91880: StoreField: r2->field_f = r0
    //     0xb91880: stur            w0, [x2, #0xf]
    // 0xb91884: r0 = Instance_Clip
    //     0xb91884: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb91888: ldr             x0, [x0, #0x138]
    // 0xb9188c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9188c: stur            w0, [x2, #0x17]
    // 0xb91890: ldur            x1, [fp, #-0x70]
    // 0xb91894: StoreField: r2->field_b = r1
    //     0xb91894: stur            w1, [x2, #0xb]
    // 0xb91898: ldur            x3, [fp, #-8]
    // 0xb9189c: LoadField: r1 = r3->field_b
    //     0xb9189c: ldur            w1, [x3, #0xb]
    // 0xb918a0: DecompressPointer r1
    //     0xb918a0: add             x1, x1, HEAP, lsl #32
    // 0xb918a4: cmp             w1, NULL
    // 0xb918a8: b.eq            #0xb936d4
    // 0xb918ac: LoadField: r4 = r1->field_b
    //     0xb918ac: ldur            w4, [x1, #0xb]
    // 0xb918b0: DecompressPointer r4
    //     0xb918b0: add             x4, x4, HEAP, lsl #32
    // 0xb918b4: cmp             w4, NULL
    // 0xb918b8: b.ne            #0xb918c4
    // 0xb918bc: r1 = Null
    //     0xb918bc: mov             x1, NULL
    // 0xb918c0: b               #0xb918fc
    // 0xb918c4: LoadField: r1 = r4->field_b
    //     0xb918c4: ldur            w1, [x4, #0xb]
    // 0xb918c8: DecompressPointer r1
    //     0xb918c8: add             x1, x1, HEAP, lsl #32
    // 0xb918cc: cmp             w1, NULL
    // 0xb918d0: b.ne            #0xb918dc
    // 0xb918d4: r1 = Null
    //     0xb918d4: mov             x1, NULL
    // 0xb918d8: b               #0xb918fc
    // 0xb918dc: LoadField: r4 = r1->field_7
    //     0xb918dc: ldur            w4, [x1, #7]
    // 0xb918e0: DecompressPointer r4
    //     0xb918e0: add             x4, x4, HEAP, lsl #32
    // 0xb918e4: cmp             w4, NULL
    // 0xb918e8: b.ne            #0xb918f4
    // 0xb918ec: r1 = Null
    //     0xb918ec: mov             x1, NULL
    // 0xb918f0: b               #0xb918fc
    // 0xb918f4: LoadField: r1 = r4->field_b
    //     0xb918f4: ldur            w1, [x4, #0xb]
    // 0xb918f8: DecompressPointer r1
    //     0xb918f8: add             x1, x1, HEAP, lsl #32
    // 0xb918fc: cmp             w1, NULL
    // 0xb91900: b.ne            #0xb9190c
    // 0xb91904: r4 = ""
    //     0xb91904: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb91908: b               #0xb91910
    // 0xb9190c: mov             x4, x1
    // 0xb91910: stur            x4, [fp, #-0x28]
    // 0xb91914: r16 = Instance_Color
    //     0xb91914: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb91918: r30 = 12.000000
    //     0xb91918: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb9191c: ldr             lr, [lr, #0x9e8]
    // 0xb91920: stp             lr, x16, [SP]
    // 0xb91924: ldur            x1, [fp, #-0x18]
    // 0xb91928: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb91928: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb9192c: ldr             x4, [x4, #0x9b8]
    // 0xb91930: r0 = copyWith()
    //     0xb91930: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb91934: stur            x0, [fp, #-0x68]
    // 0xb91938: r0 = Text()
    //     0xb91938: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9193c: mov             x3, x0
    // 0xb91940: ldur            x0, [fp, #-0x28]
    // 0xb91944: stur            x3, [fp, #-0x70]
    // 0xb91948: StoreField: r3->field_b = r0
    //     0xb91948: stur            w0, [x3, #0xb]
    // 0xb9194c: ldur            x0, [fp, #-0x68]
    // 0xb91950: StoreField: r3->field_13 = r0
    //     0xb91950: stur            w0, [x3, #0x13]
    // 0xb91954: r0 = 2
    //     0xb91954: movz            x0, #0x2
    // 0xb91958: StoreField: r3->field_37 = r0
    //     0xb91958: stur            w0, [x3, #0x37]
    // 0xb9195c: r1 = Null
    //     0xb9195c: mov             x1, NULL
    // 0xb91960: r2 = 8
    //     0xb91960: movz            x2, #0x8
    // 0xb91964: r0 = AllocateArray()
    //     0xb91964: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb91968: r16 = "Size: "
    //     0xb91968: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xb9196c: ldr             x16, [x16, #0xf00]
    // 0xb91970: StoreField: r0->field_f = r16
    //     0xb91970: stur            w16, [x0, #0xf]
    // 0xb91974: ldur            x1, [fp, #-8]
    // 0xb91978: LoadField: r2 = r1->field_b
    //     0xb91978: ldur            w2, [x1, #0xb]
    // 0xb9197c: DecompressPointer r2
    //     0xb9197c: add             x2, x2, HEAP, lsl #32
    // 0xb91980: cmp             w2, NULL
    // 0xb91984: b.eq            #0xb936d8
    // 0xb91988: LoadField: r3 = r2->field_b
    //     0xb91988: ldur            w3, [x2, #0xb]
    // 0xb9198c: DecompressPointer r3
    //     0xb9198c: add             x3, x3, HEAP, lsl #32
    // 0xb91990: cmp             w3, NULL
    // 0xb91994: b.ne            #0xb919a0
    // 0xb91998: r2 = Null
    //     0xb91998: mov             x2, NULL
    // 0xb9199c: b               #0xb919d8
    // 0xb919a0: LoadField: r2 = r3->field_b
    //     0xb919a0: ldur            w2, [x3, #0xb]
    // 0xb919a4: DecompressPointer r2
    //     0xb919a4: add             x2, x2, HEAP, lsl #32
    // 0xb919a8: cmp             w2, NULL
    // 0xb919ac: b.ne            #0xb919b8
    // 0xb919b0: r2 = Null
    //     0xb919b0: mov             x2, NULL
    // 0xb919b4: b               #0xb919d8
    // 0xb919b8: LoadField: r4 = r2->field_7
    //     0xb919b8: ldur            w4, [x2, #7]
    // 0xb919bc: DecompressPointer r4
    //     0xb919bc: add             x4, x4, HEAP, lsl #32
    // 0xb919c0: cmp             w4, NULL
    // 0xb919c4: b.ne            #0xb919d0
    // 0xb919c8: r2 = Null
    //     0xb919c8: mov             x2, NULL
    // 0xb919cc: b               #0xb919d8
    // 0xb919d0: LoadField: r2 = r4->field_f
    //     0xb919d0: ldur            w2, [x4, #0xf]
    // 0xb919d4: DecompressPointer r2
    //     0xb919d4: add             x2, x2, HEAP, lsl #32
    // 0xb919d8: cmp             w2, NULL
    // 0xb919dc: b.ne            #0xb919e4
    // 0xb919e0: r2 = ""
    //     0xb919e0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb919e4: StoreField: r0->field_13 = r2
    //     0xb919e4: stur            w2, [x0, #0x13]
    // 0xb919e8: r16 = " / Qty: "
    //     0xb919e8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb919ec: ldr             x16, [x16, #0x760]
    // 0xb919f0: ArrayStore: r0[0] = r16  ; List_4
    //     0xb919f0: stur            w16, [x0, #0x17]
    // 0xb919f4: cmp             w3, NULL
    // 0xb919f8: b.ne            #0xb91a04
    // 0xb919fc: r2 = Null
    //     0xb919fc: mov             x2, NULL
    // 0xb91a00: b               #0xb91a3c
    // 0xb91a04: LoadField: r2 = r3->field_b
    //     0xb91a04: ldur            w2, [x3, #0xb]
    // 0xb91a08: DecompressPointer r2
    //     0xb91a08: add             x2, x2, HEAP, lsl #32
    // 0xb91a0c: cmp             w2, NULL
    // 0xb91a10: b.ne            #0xb91a1c
    // 0xb91a14: r2 = Null
    //     0xb91a14: mov             x2, NULL
    // 0xb91a18: b               #0xb91a3c
    // 0xb91a1c: LoadField: r3 = r2->field_7
    //     0xb91a1c: ldur            w3, [x2, #7]
    // 0xb91a20: DecompressPointer r3
    //     0xb91a20: add             x3, x3, HEAP, lsl #32
    // 0xb91a24: cmp             w3, NULL
    // 0xb91a28: b.ne            #0xb91a34
    // 0xb91a2c: r2 = Null
    //     0xb91a2c: mov             x2, NULL
    // 0xb91a30: b               #0xb91a3c
    // 0xb91a34: LoadField: r2 = r3->field_13
    //     0xb91a34: ldur            w2, [x3, #0x13]
    // 0xb91a38: DecompressPointer r2
    //     0xb91a38: add             x2, x2, HEAP, lsl #32
    // 0xb91a3c: cmp             w2, NULL
    // 0xb91a40: b.ne            #0xb91a4c
    // 0xb91a44: r3 = ""
    //     0xb91a44: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb91a48: b               #0xb91a50
    // 0xb91a4c: mov             x3, x2
    // 0xb91a50: ldur            x2, [fp, #-0x20]
    // 0xb91a54: StoreField: r0->field_1b = r3
    //     0xb91a54: stur            w3, [x0, #0x1b]
    // 0xb91a58: str             x0, [SP]
    // 0xb91a5c: r0 = _interpolate()
    //     0xb91a5c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb91a60: mov             x2, x0
    // 0xb91a64: ldur            x0, [fp, #-0x20]
    // 0xb91a68: stur            x2, [fp, #-0x68]
    // 0xb91a6c: LoadField: r3 = r0->field_2b
    //     0xb91a6c: ldur            w3, [x0, #0x2b]
    // 0xb91a70: DecompressPointer r3
    //     0xb91a70: add             x3, x3, HEAP, lsl #32
    // 0xb91a74: stur            x3, [fp, #-0x28]
    // 0xb91a78: r16 = Instance_Color
    //     0xb91a78: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb91a7c: r30 = 12.000000
    //     0xb91a7c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb91a80: ldr             lr, [lr, #0x9e8]
    // 0xb91a84: stp             lr, x16, [SP]
    // 0xb91a88: mov             x1, x3
    // 0xb91a8c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb91a8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb91a90: ldr             x4, [x4, #0x9b8]
    // 0xb91a94: r0 = copyWith()
    //     0xb91a94: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb91a98: stur            x0, [fp, #-0x20]
    // 0xb91a9c: r0 = Text()
    //     0xb91a9c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb91aa0: mov             x2, x0
    // 0xb91aa4: ldur            x0, [fp, #-0x68]
    // 0xb91aa8: stur            x2, [fp, #-0x78]
    // 0xb91aac: StoreField: r2->field_b = r0
    //     0xb91aac: stur            w0, [x2, #0xb]
    // 0xb91ab0: ldur            x0, [fp, #-0x20]
    // 0xb91ab4: StoreField: r2->field_13 = r0
    //     0xb91ab4: stur            w0, [x2, #0x13]
    // 0xb91ab8: ldur            x0, [fp, #-8]
    // 0xb91abc: LoadField: r1 = r0->field_b
    //     0xb91abc: ldur            w1, [x0, #0xb]
    // 0xb91ac0: DecompressPointer r1
    //     0xb91ac0: add             x1, x1, HEAP, lsl #32
    // 0xb91ac4: cmp             w1, NULL
    // 0xb91ac8: b.eq            #0xb936dc
    // 0xb91acc: LoadField: r3 = r1->field_b
    //     0xb91acc: ldur            w3, [x1, #0xb]
    // 0xb91ad0: DecompressPointer r3
    //     0xb91ad0: add             x3, x3, HEAP, lsl #32
    // 0xb91ad4: cmp             w3, NULL
    // 0xb91ad8: b.ne            #0xb91ae4
    // 0xb91adc: r1 = Null
    //     0xb91adc: mov             x1, NULL
    // 0xb91ae0: b               #0xb91b38
    // 0xb91ae4: LoadField: r1 = r3->field_b
    //     0xb91ae4: ldur            w1, [x3, #0xb]
    // 0xb91ae8: DecompressPointer r1
    //     0xb91ae8: add             x1, x1, HEAP, lsl #32
    // 0xb91aec: cmp             w1, NULL
    // 0xb91af0: b.ne            #0xb91afc
    // 0xb91af4: r1 = Null
    //     0xb91af4: mov             x1, NULL
    // 0xb91af8: b               #0xb91b38
    // 0xb91afc: LoadField: r3 = r1->field_7
    //     0xb91afc: ldur            w3, [x1, #7]
    // 0xb91b00: DecompressPointer r3
    //     0xb91b00: add             x3, x3, HEAP, lsl #32
    // 0xb91b04: cmp             w3, NULL
    // 0xb91b08: b.ne            #0xb91b14
    // 0xb91b0c: r1 = Null
    //     0xb91b0c: mov             x1, NULL
    // 0xb91b10: b               #0xb91b38
    // 0xb91b14: LoadField: r1 = r3->field_1b
    //     0xb91b14: ldur            w1, [x3, #0x1b]
    // 0xb91b18: DecompressPointer r1
    //     0xb91b18: add             x1, x1, HEAP, lsl #32
    // 0xb91b1c: cmp             w1, NULL
    // 0xb91b20: b.ne            #0xb91b2c
    // 0xb91b24: r1 = Null
    //     0xb91b24: mov             x1, NULL
    // 0xb91b28: b               #0xb91b38
    // 0xb91b2c: LoadField: r3 = r1->field_7
    //     0xb91b2c: ldur            w3, [x1, #7]
    // 0xb91b30: DecompressPointer r3
    //     0xb91b30: add             x3, x3, HEAP, lsl #32
    // 0xb91b34: mov             x1, x3
    // 0xb91b38: cmp             w1, NULL
    // 0xb91b3c: b.ne            #0xb91b48
    // 0xb91b40: r5 = ""
    //     0xb91b40: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb91b44: b               #0xb91b4c
    // 0xb91b48: mov             x5, x1
    // 0xb91b4c: ldur            x4, [fp, #-0x38]
    // 0xb91b50: ldur            x3, [fp, #-0x70]
    // 0xb91b54: stur            x5, [fp, #-0x20]
    // 0xb91b58: r16 = Instance_Color
    //     0xb91b58: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb91b5c: r30 = 12.000000
    //     0xb91b5c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb91b60: ldr             lr, [lr, #0x9e8]
    // 0xb91b64: stp             lr, x16, [SP]
    // 0xb91b68: ldur            x1, [fp, #-0x18]
    // 0xb91b6c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb91b6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb91b70: ldr             x4, [x4, #0x9b8]
    // 0xb91b74: r0 = copyWith()
    //     0xb91b74: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb91b78: stur            x0, [fp, #-0x68]
    // 0xb91b7c: r0 = Text()
    //     0xb91b7c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb91b80: mov             x3, x0
    // 0xb91b84: ldur            x0, [fp, #-0x20]
    // 0xb91b88: stur            x3, [fp, #-0x80]
    // 0xb91b8c: StoreField: r3->field_b = r0
    //     0xb91b8c: stur            w0, [x3, #0xb]
    // 0xb91b90: ldur            x0, [fp, #-0x68]
    // 0xb91b94: StoreField: r3->field_13 = r0
    //     0xb91b94: stur            w0, [x3, #0x13]
    // 0xb91b98: r1 = Null
    //     0xb91b98: mov             x1, NULL
    // 0xb91b9c: r2 = 6
    //     0xb91b9c: movz            x2, #0x6
    // 0xb91ba0: r0 = AllocateArray()
    //     0xb91ba0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb91ba4: mov             x2, x0
    // 0xb91ba8: ldur            x0, [fp, #-0x70]
    // 0xb91bac: stur            x2, [fp, #-0x20]
    // 0xb91bb0: StoreField: r2->field_f = r0
    //     0xb91bb0: stur            w0, [x2, #0xf]
    // 0xb91bb4: ldur            x0, [fp, #-0x78]
    // 0xb91bb8: StoreField: r2->field_13 = r0
    //     0xb91bb8: stur            w0, [x2, #0x13]
    // 0xb91bbc: ldur            x0, [fp, #-0x80]
    // 0xb91bc0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb91bc0: stur            w0, [x2, #0x17]
    // 0xb91bc4: r1 = <Widget>
    //     0xb91bc4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb91bc8: r0 = AllocateGrowableArray()
    //     0xb91bc8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb91bcc: mov             x1, x0
    // 0xb91bd0: ldur            x0, [fp, #-0x20]
    // 0xb91bd4: stur            x1, [fp, #-0x68]
    // 0xb91bd8: StoreField: r1->field_f = r0
    //     0xb91bd8: stur            w0, [x1, #0xf]
    // 0xb91bdc: r2 = 6
    //     0xb91bdc: movz            x2, #0x6
    // 0xb91be0: StoreField: r1->field_b = r2
    //     0xb91be0: stur            w2, [x1, #0xb]
    // 0xb91be4: r0 = Column()
    //     0xb91be4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb91be8: mov             x2, x0
    // 0xb91bec: r0 = Instance_Axis
    //     0xb91bec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb91bf0: stur            x2, [fp, #-0x20]
    // 0xb91bf4: StoreField: r2->field_f = r0
    //     0xb91bf4: stur            w0, [x2, #0xf]
    // 0xb91bf8: r3 = Instance_MainAxisAlignment
    //     0xb91bf8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb91bfc: ldr             x3, [x3, #0xa08]
    // 0xb91c00: StoreField: r2->field_13 = r3
    //     0xb91c00: stur            w3, [x2, #0x13]
    // 0xb91c04: r4 = Instance_MainAxisSize
    //     0xb91c04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb91c08: ldr             x4, [x4, #0xa10]
    // 0xb91c0c: ArrayStore: r2[0] = r4  ; List_4
    //     0xb91c0c: stur            w4, [x2, #0x17]
    // 0xb91c10: r5 = Instance_CrossAxisAlignment
    //     0xb91c10: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb91c14: ldr             x5, [x5, #0x890]
    // 0xb91c18: StoreField: r2->field_1b = r5
    //     0xb91c18: stur            w5, [x2, #0x1b]
    // 0xb91c1c: r6 = Instance_VerticalDirection
    //     0xb91c1c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb91c20: ldr             x6, [x6, #0xa20]
    // 0xb91c24: StoreField: r2->field_23 = r6
    //     0xb91c24: stur            w6, [x2, #0x23]
    // 0xb91c28: r7 = Instance_Clip
    //     0xb91c28: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb91c2c: ldr             x7, [x7, #0x38]
    // 0xb91c30: StoreField: r2->field_2b = r7
    //     0xb91c30: stur            w7, [x2, #0x2b]
    // 0xb91c34: StoreField: r2->field_2f = rZR
    //     0xb91c34: stur            xzr, [x2, #0x2f]
    // 0xb91c38: ldur            x1, [fp, #-0x68]
    // 0xb91c3c: StoreField: r2->field_b = r1
    //     0xb91c3c: stur            w1, [x2, #0xb]
    // 0xb91c40: r1 = <FlexParentData>
    //     0xb91c40: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb91c44: ldr             x1, [x1, #0xe00]
    // 0xb91c48: r0 = Expanded()
    //     0xb91c48: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb91c4c: mov             x3, x0
    // 0xb91c50: r0 = 1
    //     0xb91c50: movz            x0, #0x1
    // 0xb91c54: stur            x3, [fp, #-0x68]
    // 0xb91c58: StoreField: r3->field_13 = r0
    //     0xb91c58: stur            x0, [x3, #0x13]
    // 0xb91c5c: r4 = Instance_FlexFit
    //     0xb91c5c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb91c60: ldr             x4, [x4, #0xe08]
    // 0xb91c64: StoreField: r3->field_1b = r4
    //     0xb91c64: stur            w4, [x3, #0x1b]
    // 0xb91c68: ldur            x1, [fp, #-0x20]
    // 0xb91c6c: StoreField: r3->field_b = r1
    //     0xb91c6c: stur            w1, [x3, #0xb]
    // 0xb91c70: r1 = Null
    //     0xb91c70: mov             x1, NULL
    // 0xb91c74: r2 = 6
    //     0xb91c74: movz            x2, #0x6
    // 0xb91c78: r0 = AllocateArray()
    //     0xb91c78: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb91c7c: mov             x2, x0
    // 0xb91c80: ldur            x0, [fp, #-0x38]
    // 0xb91c84: stur            x2, [fp, #-0x20]
    // 0xb91c88: StoreField: r2->field_f = r0
    //     0xb91c88: stur            w0, [x2, #0xf]
    // 0xb91c8c: r16 = Instance_SizedBox
    //     0xb91c8c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb91c90: ldr             x16, [x16, #0xb20]
    // 0xb91c94: StoreField: r2->field_13 = r16
    //     0xb91c94: stur            w16, [x2, #0x13]
    // 0xb91c98: ldur            x0, [fp, #-0x68]
    // 0xb91c9c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb91c9c: stur            w0, [x2, #0x17]
    // 0xb91ca0: r1 = <Widget>
    //     0xb91ca0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb91ca4: r0 = AllocateGrowableArray()
    //     0xb91ca4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb91ca8: mov             x1, x0
    // 0xb91cac: ldur            x0, [fp, #-0x20]
    // 0xb91cb0: stur            x1, [fp, #-0x38]
    // 0xb91cb4: StoreField: r1->field_f = r0
    //     0xb91cb4: stur            w0, [x1, #0xf]
    // 0xb91cb8: r2 = 6
    //     0xb91cb8: movz            x2, #0x6
    // 0xb91cbc: StoreField: r1->field_b = r2
    //     0xb91cbc: stur            w2, [x1, #0xb]
    // 0xb91cc0: r0 = Row()
    //     0xb91cc0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb91cc4: mov             x1, x0
    // 0xb91cc8: r0 = Instance_Axis
    //     0xb91cc8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb91ccc: stur            x1, [fp, #-0x20]
    // 0xb91cd0: StoreField: r1->field_f = r0
    //     0xb91cd0: stur            w0, [x1, #0xf]
    // 0xb91cd4: r2 = Instance_MainAxisAlignment
    //     0xb91cd4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb91cd8: ldr             x2, [x2, #0xa08]
    // 0xb91cdc: StoreField: r1->field_13 = r2
    //     0xb91cdc: stur            w2, [x1, #0x13]
    // 0xb91ce0: r3 = Instance_MainAxisSize
    //     0xb91ce0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb91ce4: ldr             x3, [x3, #0xa10]
    // 0xb91ce8: ArrayStore: r1[0] = r3  ; List_4
    //     0xb91ce8: stur            w3, [x1, #0x17]
    // 0xb91cec: r4 = Instance_CrossAxisAlignment
    //     0xb91cec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb91cf0: ldr             x4, [x4, #0xa18]
    // 0xb91cf4: StoreField: r1->field_1b = r4
    //     0xb91cf4: stur            w4, [x1, #0x1b]
    // 0xb91cf8: r5 = Instance_VerticalDirection
    //     0xb91cf8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb91cfc: ldr             x5, [x5, #0xa20]
    // 0xb91d00: StoreField: r1->field_23 = r5
    //     0xb91d00: stur            w5, [x1, #0x23]
    // 0xb91d04: r6 = Instance_Clip
    //     0xb91d04: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb91d08: ldr             x6, [x6, #0x38]
    // 0xb91d0c: StoreField: r1->field_2b = r6
    //     0xb91d0c: stur            w6, [x1, #0x2b]
    // 0xb91d10: StoreField: r1->field_2f = rZR
    //     0xb91d10: stur            xzr, [x1, #0x2f]
    // 0xb91d14: ldur            x7, [fp, #-0x38]
    // 0xb91d18: StoreField: r1->field_b = r7
    //     0xb91d18: stur            w7, [x1, #0xb]
    // 0xb91d1c: r0 = Padding()
    //     0xb91d1c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb91d20: mov             x1, x0
    // 0xb91d24: r0 = Instance_EdgeInsets
    //     0xb91d24: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0xb91d28: ldr             x0, [x0, #0xf98]
    // 0xb91d2c: stur            x1, [fp, #-0x38]
    // 0xb91d30: StoreField: r1->field_f = r0
    //     0xb91d30: stur            w0, [x1, #0xf]
    // 0xb91d34: ldur            x0, [fp, #-0x20]
    // 0xb91d38: StoreField: r1->field_b = r0
    //     0xb91d38: stur            w0, [x1, #0xb]
    // 0xb91d3c: r0 = Container()
    //     0xb91d3c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb91d40: stur            x0, [fp, #-0x20]
    // 0xb91d44: ldur            x16, [fp, #-0x60]
    // 0xb91d48: ldur            lr, [fp, #-0x38]
    // 0xb91d4c: stp             lr, x16, [SP]
    // 0xb91d50: mov             x1, x0
    // 0xb91d54: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb91d54: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb91d58: ldr             x4, [x4, #0x88]
    // 0xb91d5c: r0 = Container()
    //     0xb91d5c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb91d60: r0 = Radius()
    //     0xb91d60: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb91d64: d0 = 12.000000
    //     0xb91d64: fmov            d0, #12.00000000
    // 0xb91d68: stur            x0, [fp, #-0x38]
    // 0xb91d6c: StoreField: r0->field_7 = d0
    //     0xb91d6c: stur            d0, [x0, #7]
    // 0xb91d70: StoreField: r0->field_f = d0
    //     0xb91d70: stur            d0, [x0, #0xf]
    // 0xb91d74: r0 = BorderRadius()
    //     0xb91d74: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb91d78: mov             x3, x0
    // 0xb91d7c: ldur            x0, [fp, #-0x38]
    // 0xb91d80: stur            x3, [fp, #-0x60]
    // 0xb91d84: StoreField: r3->field_7 = r0
    //     0xb91d84: stur            w0, [x3, #7]
    // 0xb91d88: StoreField: r3->field_b = r0
    //     0xb91d88: stur            w0, [x3, #0xb]
    // 0xb91d8c: StoreField: r3->field_f = r0
    //     0xb91d8c: stur            w0, [x3, #0xf]
    // 0xb91d90: StoreField: r3->field_13 = r0
    //     0xb91d90: stur            w0, [x3, #0x13]
    // 0xb91d94: ldur            x0, [fp, #-8]
    // 0xb91d98: LoadField: r1 = r0->field_b
    //     0xb91d98: ldur            w1, [x0, #0xb]
    // 0xb91d9c: DecompressPointer r1
    //     0xb91d9c: add             x1, x1, HEAP, lsl #32
    // 0xb91da0: cmp             w1, NULL
    // 0xb91da4: b.eq            #0xb936e0
    // 0xb91da8: LoadField: r2 = r1->field_13
    //     0xb91da8: ldur            w2, [x1, #0x13]
    // 0xb91dac: DecompressPointer r2
    //     0xb91dac: add             x2, x2, HEAP, lsl #32
    // 0xb91db0: cmp             w2, NULL
    // 0xb91db4: b.ne            #0xb91dc0
    // 0xb91db8: r4 = ""
    //     0xb91db8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb91dbc: b               #0xb91dc4
    // 0xb91dc0: mov             x4, x2
    // 0xb91dc4: stur            x4, [fp, #-0x38]
    // 0xb91dc8: r1 = Function '<anonymous closure>':.
    //     0xb91dc8: add             x1, PP, #0x56, lsl #12  ; [pp+0x56020] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb91dcc: ldr             x1, [x1, #0x20]
    // 0xb91dd0: r2 = Null
    //     0xb91dd0: mov             x2, NULL
    // 0xb91dd4: r0 = AllocateClosure()
    //     0xb91dd4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb91dd8: stur            x0, [fp, #-0x68]
    // 0xb91ddc: r0 = CachedNetworkImage()
    //     0xb91ddc: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb91de0: stur            x0, [fp, #-0x70]
    // 0xb91de4: r16 = 80.000000
    //     0xb91de4: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xb91de8: ldr             x16, [x16, #0x2f8]
    // 0xb91dec: r30 = 80.000000
    //     0xb91dec: add             lr, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xb91df0: ldr             lr, [lr, #0x2f8]
    // 0xb91df4: stp             lr, x16, [SP, #0x10]
    // 0xb91df8: r16 = Instance_BoxFit
    //     0xb91df8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb91dfc: ldr             x16, [x16, #0x118]
    // 0xb91e00: ldur            lr, [fp, #-0x68]
    // 0xb91e04: stp             lr, x16, [SP]
    // 0xb91e08: mov             x1, x0
    // 0xb91e0c: ldur            x2, [fp, #-0x38]
    // 0xb91e10: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x4, height, 0x2, width, 0x3, null]
    //     0xb91e10: add             x4, PP, #0x56, lsl #12  ; [pp+0x56018] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0xb91e14: ldr             x4, [x4, #0x18]
    // 0xb91e18: r0 = CachedNetworkImage()
    //     0xb91e18: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb91e1c: r0 = ClipRRect()
    //     0xb91e1c: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb91e20: mov             x2, x0
    // 0xb91e24: ldur            x0, [fp, #-0x60]
    // 0xb91e28: stur            x2, [fp, #-0x68]
    // 0xb91e2c: StoreField: r2->field_f = r0
    //     0xb91e2c: stur            w0, [x2, #0xf]
    // 0xb91e30: r0 = Instance_Clip
    //     0xb91e30: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb91e34: ldr             x0, [x0, #0x138]
    // 0xb91e38: ArrayStore: r2[0] = r0  ; List_4
    //     0xb91e38: stur            w0, [x2, #0x17]
    // 0xb91e3c: ldur            x0, [fp, #-0x70]
    // 0xb91e40: StoreField: r2->field_b = r0
    //     0xb91e40: stur            w0, [x2, #0xb]
    // 0xb91e44: ldur            x0, [fp, #-8]
    // 0xb91e48: LoadField: r1 = r0->field_b
    //     0xb91e48: ldur            w1, [x0, #0xb]
    // 0xb91e4c: DecompressPointer r1
    //     0xb91e4c: add             x1, x1, HEAP, lsl #32
    // 0xb91e50: cmp             w1, NULL
    // 0xb91e54: b.eq            #0xb936e4
    // 0xb91e58: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb91e58: ldur            w3, [x1, #0x17]
    // 0xb91e5c: DecompressPointer r3
    //     0xb91e5c: add             x3, x3, HEAP, lsl #32
    // 0xb91e60: cmp             w3, NULL
    // 0xb91e64: b.ne            #0xb91e70
    // 0xb91e68: r1 = Null
    //     0xb91e68: mov             x1, NULL
    // 0xb91e6c: b               #0xb91e78
    // 0xb91e70: LoadField: r1 = r3->field_f
    //     0xb91e70: ldur            w1, [x3, #0xf]
    // 0xb91e74: DecompressPointer r1
    //     0xb91e74: add             x1, x1, HEAP, lsl #32
    // 0xb91e78: cmp             w1, NULL
    // 0xb91e7c: b.ne            #0xb91e88
    // 0xb91e80: r3 = ""
    //     0xb91e80: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb91e84: b               #0xb91e8c
    // 0xb91e88: mov             x3, x1
    // 0xb91e8c: stur            x3, [fp, #-0x38]
    // 0xb91e90: r16 = Instance_Color
    //     0xb91e90: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb91e94: r30 = 12.000000
    //     0xb91e94: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb91e98: ldr             lr, [lr, #0x9e8]
    // 0xb91e9c: stp             lr, x16, [SP]
    // 0xb91ea0: ldur            x1, [fp, #-0x18]
    // 0xb91ea4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb91ea4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb91ea8: ldr             x4, [x4, #0x9b8]
    // 0xb91eac: r0 = copyWith()
    //     0xb91eac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb91eb0: stur            x0, [fp, #-0x60]
    // 0xb91eb4: r0 = Text()
    //     0xb91eb4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb91eb8: mov             x3, x0
    // 0xb91ebc: ldur            x0, [fp, #-0x38]
    // 0xb91ec0: stur            x3, [fp, #-0x70]
    // 0xb91ec4: StoreField: r3->field_b = r0
    //     0xb91ec4: stur            w0, [x3, #0xb]
    // 0xb91ec8: ldur            x0, [fp, #-0x60]
    // 0xb91ecc: StoreField: r3->field_13 = r0
    //     0xb91ecc: stur            w0, [x3, #0x13]
    // 0xb91ed0: r0 = 2
    //     0xb91ed0: movz            x0, #0x2
    // 0xb91ed4: StoreField: r3->field_37 = r0
    //     0xb91ed4: stur            w0, [x3, #0x37]
    // 0xb91ed8: r1 = Null
    //     0xb91ed8: mov             x1, NULL
    // 0xb91edc: r2 = 8
    //     0xb91edc: movz            x2, #0x8
    // 0xb91ee0: r0 = AllocateArray()
    //     0xb91ee0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb91ee4: r16 = "Size: "
    //     0xb91ee4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xb91ee8: ldr             x16, [x16, #0xf00]
    // 0xb91eec: StoreField: r0->field_f = r16
    //     0xb91eec: stur            w16, [x0, #0xf]
    // 0xb91ef0: ldur            x1, [fp, #-8]
    // 0xb91ef4: LoadField: r2 = r1->field_b
    //     0xb91ef4: ldur            w2, [x1, #0xb]
    // 0xb91ef8: DecompressPointer r2
    //     0xb91ef8: add             x2, x2, HEAP, lsl #32
    // 0xb91efc: cmp             w2, NULL
    // 0xb91f00: b.eq            #0xb936e8
    // 0xb91f04: LoadField: r3 = r2->field_f
    //     0xb91f04: ldur            w3, [x2, #0xf]
    // 0xb91f08: DecompressPointer r3
    //     0xb91f08: add             x3, x3, HEAP, lsl #32
    // 0xb91f0c: cmp             w3, NULL
    // 0xb91f10: b.ne            #0xb91f1c
    // 0xb91f14: r3 = Null
    //     0xb91f14: mov             x3, NULL
    // 0xb91f18: b               #0xb91f28
    // 0xb91f1c: LoadField: r4 = r3->field_1f
    //     0xb91f1c: ldur            w4, [x3, #0x1f]
    // 0xb91f20: DecompressPointer r4
    //     0xb91f20: add             x4, x4, HEAP, lsl #32
    // 0xb91f24: mov             x3, x4
    // 0xb91f28: cmp             w3, NULL
    // 0xb91f2c: b.ne            #0xb91f34
    // 0xb91f30: r3 = ""
    //     0xb91f30: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb91f34: StoreField: r0->field_13 = r3
    //     0xb91f34: stur            w3, [x0, #0x13]
    // 0xb91f38: r16 = " / Qty: "
    //     0xb91f38: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb91f3c: ldr             x16, [x16, #0x760]
    // 0xb91f40: ArrayStore: r0[0] = r16  ; List_4
    //     0xb91f40: stur            w16, [x0, #0x17]
    // 0xb91f44: LoadField: r3 = r2->field_1b
    //     0xb91f44: ldur            w3, [x2, #0x1b]
    // 0xb91f48: DecompressPointer r3
    //     0xb91f48: add             x3, x3, HEAP, lsl #32
    // 0xb91f4c: StoreField: r0->field_1b = r3
    //     0xb91f4c: stur            w3, [x0, #0x1b]
    // 0xb91f50: str             x0, [SP]
    // 0xb91f54: r0 = _interpolate()
    //     0xb91f54: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb91f58: stur            x0, [fp, #-0x38]
    // 0xb91f5c: r16 = Instance_Color
    //     0xb91f5c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb91f60: r30 = 12.000000
    //     0xb91f60: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb91f64: ldr             lr, [lr, #0x9e8]
    // 0xb91f68: stp             lr, x16, [SP]
    // 0xb91f6c: ldur            x1, [fp, #-0x28]
    // 0xb91f70: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb91f70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb91f74: ldr             x4, [x4, #0x9b8]
    // 0xb91f78: r0 = copyWith()
    //     0xb91f78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb91f7c: stur            x0, [fp, #-0x60]
    // 0xb91f80: r0 = Text()
    //     0xb91f80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb91f84: mov             x2, x0
    // 0xb91f88: ldur            x0, [fp, #-0x38]
    // 0xb91f8c: stur            x2, [fp, #-0x78]
    // 0xb91f90: StoreField: r2->field_b = r0
    //     0xb91f90: stur            w0, [x2, #0xb]
    // 0xb91f94: ldur            x0, [fp, #-0x60]
    // 0xb91f98: StoreField: r2->field_13 = r0
    //     0xb91f98: stur            w0, [x2, #0x13]
    // 0xb91f9c: ldur            x0, [fp, #-8]
    // 0xb91fa0: LoadField: r1 = r0->field_b
    //     0xb91fa0: ldur            w1, [x0, #0xb]
    // 0xb91fa4: DecompressPointer r1
    //     0xb91fa4: add             x1, x1, HEAP, lsl #32
    // 0xb91fa8: cmp             w1, NULL
    // 0xb91fac: b.eq            #0xb936ec
    // 0xb91fb0: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb91fb0: ldur            w3, [x1, #0x17]
    // 0xb91fb4: DecompressPointer r3
    //     0xb91fb4: add             x3, x3, HEAP, lsl #32
    // 0xb91fb8: cmp             w3, NULL
    // 0xb91fbc: b.ne            #0xb91fc8
    // 0xb91fc0: r1 = Null
    //     0xb91fc0: mov             x1, NULL
    // 0xb91fc4: b               #0xb91fd0
    // 0xb91fc8: LoadField: r1 = r3->field_43
    //     0xb91fc8: ldur            w1, [x3, #0x43]
    // 0xb91fcc: DecompressPointer r1
    //     0xb91fcc: add             x1, x1, HEAP, lsl #32
    // 0xb91fd0: cmp             w1, NULL
    // 0xb91fd4: b.ne            #0xb91fe0
    // 0xb91fd8: r6 = ""
    //     0xb91fd8: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb91fdc: b               #0xb91fe4
    // 0xb91fe0: mov             x6, x1
    // 0xb91fe4: ldur            x5, [fp, #-0x20]
    // 0xb91fe8: ldur            x4, [fp, #-0x68]
    // 0xb91fec: ldur            x3, [fp, #-0x70]
    // 0xb91ff0: stur            x6, [fp, #-0x38]
    // 0xb91ff4: r16 = Instance_Color
    //     0xb91ff4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb91ff8: r30 = 12.000000
    //     0xb91ff8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb91ffc: ldr             lr, [lr, #0x9e8]
    // 0xb92000: stp             lr, x16, [SP]
    // 0xb92004: ldur            x1, [fp, #-0x18]
    // 0xb92008: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb92008: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb9200c: ldr             x4, [x4, #0x9b8]
    // 0xb92010: r0 = copyWith()
    //     0xb92010: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb92014: stur            x0, [fp, #-0x60]
    // 0xb92018: r0 = Text()
    //     0xb92018: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9201c: mov             x3, x0
    // 0xb92020: ldur            x0, [fp, #-0x38]
    // 0xb92024: stur            x3, [fp, #-0x80]
    // 0xb92028: StoreField: r3->field_b = r0
    //     0xb92028: stur            w0, [x3, #0xb]
    // 0xb9202c: ldur            x0, [fp, #-0x60]
    // 0xb92030: StoreField: r3->field_13 = r0
    //     0xb92030: stur            w0, [x3, #0x13]
    // 0xb92034: r1 = Null
    //     0xb92034: mov             x1, NULL
    // 0xb92038: r2 = 6
    //     0xb92038: movz            x2, #0x6
    // 0xb9203c: r0 = AllocateArray()
    //     0xb9203c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb92040: mov             x2, x0
    // 0xb92044: ldur            x0, [fp, #-0x70]
    // 0xb92048: stur            x2, [fp, #-0x38]
    // 0xb9204c: StoreField: r2->field_f = r0
    //     0xb9204c: stur            w0, [x2, #0xf]
    // 0xb92050: ldur            x0, [fp, #-0x78]
    // 0xb92054: StoreField: r2->field_13 = r0
    //     0xb92054: stur            w0, [x2, #0x13]
    // 0xb92058: ldur            x0, [fp, #-0x80]
    // 0xb9205c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9205c: stur            w0, [x2, #0x17]
    // 0xb92060: r1 = <Widget>
    //     0xb92060: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb92064: r0 = AllocateGrowableArray()
    //     0xb92064: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb92068: mov             x1, x0
    // 0xb9206c: ldur            x0, [fp, #-0x38]
    // 0xb92070: stur            x1, [fp, #-0x60]
    // 0xb92074: StoreField: r1->field_f = r0
    //     0xb92074: stur            w0, [x1, #0xf]
    // 0xb92078: r2 = 6
    //     0xb92078: movz            x2, #0x6
    // 0xb9207c: StoreField: r1->field_b = r2
    //     0xb9207c: stur            w2, [x1, #0xb]
    // 0xb92080: r0 = Column()
    //     0xb92080: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb92084: mov             x2, x0
    // 0xb92088: r0 = Instance_Axis
    //     0xb92088: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb9208c: stur            x2, [fp, #-0x38]
    // 0xb92090: StoreField: r2->field_f = r0
    //     0xb92090: stur            w0, [x2, #0xf]
    // 0xb92094: r3 = Instance_MainAxisAlignment
    //     0xb92094: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb92098: ldr             x3, [x3, #0xa08]
    // 0xb9209c: StoreField: r2->field_13 = r3
    //     0xb9209c: stur            w3, [x2, #0x13]
    // 0xb920a0: r4 = Instance_MainAxisSize
    //     0xb920a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb920a4: ldr             x4, [x4, #0xa10]
    // 0xb920a8: ArrayStore: r2[0] = r4  ; List_4
    //     0xb920a8: stur            w4, [x2, #0x17]
    // 0xb920ac: r5 = Instance_CrossAxisAlignment
    //     0xb920ac: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb920b0: ldr             x5, [x5, #0x890]
    // 0xb920b4: StoreField: r2->field_1b = r5
    //     0xb920b4: stur            w5, [x2, #0x1b]
    // 0xb920b8: r6 = Instance_VerticalDirection
    //     0xb920b8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb920bc: ldr             x6, [x6, #0xa20]
    // 0xb920c0: StoreField: r2->field_23 = r6
    //     0xb920c0: stur            w6, [x2, #0x23]
    // 0xb920c4: r7 = Instance_Clip
    //     0xb920c4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb920c8: ldr             x7, [x7, #0x38]
    // 0xb920cc: StoreField: r2->field_2b = r7
    //     0xb920cc: stur            w7, [x2, #0x2b]
    // 0xb920d0: StoreField: r2->field_2f = rZR
    //     0xb920d0: stur            xzr, [x2, #0x2f]
    // 0xb920d4: ldur            x1, [fp, #-0x60]
    // 0xb920d8: StoreField: r2->field_b = r1
    //     0xb920d8: stur            w1, [x2, #0xb]
    // 0xb920dc: r1 = <FlexParentData>
    //     0xb920dc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb920e0: ldr             x1, [x1, #0xe00]
    // 0xb920e4: r0 = Expanded()
    //     0xb920e4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb920e8: mov             x3, x0
    // 0xb920ec: r0 = 1
    //     0xb920ec: movz            x0, #0x1
    // 0xb920f0: stur            x3, [fp, #-0x60]
    // 0xb920f4: StoreField: r3->field_13 = r0
    //     0xb920f4: stur            x0, [x3, #0x13]
    // 0xb920f8: r4 = Instance_FlexFit
    //     0xb920f8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb920fc: ldr             x4, [x4, #0xe08]
    // 0xb92100: StoreField: r3->field_1b = r4
    //     0xb92100: stur            w4, [x3, #0x1b]
    // 0xb92104: ldur            x1, [fp, #-0x38]
    // 0xb92108: StoreField: r3->field_b = r1
    //     0xb92108: stur            w1, [x3, #0xb]
    // 0xb9210c: r1 = Null
    //     0xb9210c: mov             x1, NULL
    // 0xb92110: r2 = 6
    //     0xb92110: movz            x2, #0x6
    // 0xb92114: r0 = AllocateArray()
    //     0xb92114: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb92118: mov             x2, x0
    // 0xb9211c: ldur            x0, [fp, #-0x68]
    // 0xb92120: stur            x2, [fp, #-0x38]
    // 0xb92124: StoreField: r2->field_f = r0
    //     0xb92124: stur            w0, [x2, #0xf]
    // 0xb92128: r16 = Instance_SizedBox
    //     0xb92128: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb9212c: ldr             x16, [x16, #0xb20]
    // 0xb92130: StoreField: r2->field_13 = r16
    //     0xb92130: stur            w16, [x2, #0x13]
    // 0xb92134: ldur            x0, [fp, #-0x60]
    // 0xb92138: ArrayStore: r2[0] = r0  ; List_4
    //     0xb92138: stur            w0, [x2, #0x17]
    // 0xb9213c: r1 = <Widget>
    //     0xb9213c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb92140: r0 = AllocateGrowableArray()
    //     0xb92140: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb92144: mov             x1, x0
    // 0xb92148: ldur            x0, [fp, #-0x38]
    // 0xb9214c: stur            x1, [fp, #-0x60]
    // 0xb92150: StoreField: r1->field_f = r0
    //     0xb92150: stur            w0, [x1, #0xf]
    // 0xb92154: r2 = 6
    //     0xb92154: movz            x2, #0x6
    // 0xb92158: StoreField: r1->field_b = r2
    //     0xb92158: stur            w2, [x1, #0xb]
    // 0xb9215c: r0 = Row()
    //     0xb9215c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb92160: mov             x1, x0
    // 0xb92164: r0 = Instance_Axis
    //     0xb92164: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb92168: stur            x1, [fp, #-0x38]
    // 0xb9216c: StoreField: r1->field_f = r0
    //     0xb9216c: stur            w0, [x1, #0xf]
    // 0xb92170: r2 = Instance_MainAxisAlignment
    //     0xb92170: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb92174: ldr             x2, [x2, #0xa08]
    // 0xb92178: StoreField: r1->field_13 = r2
    //     0xb92178: stur            w2, [x1, #0x13]
    // 0xb9217c: r3 = Instance_MainAxisSize
    //     0xb9217c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb92180: ldr             x3, [x3, #0xa10]
    // 0xb92184: ArrayStore: r1[0] = r3  ; List_4
    //     0xb92184: stur            w3, [x1, #0x17]
    // 0xb92188: r4 = Instance_CrossAxisAlignment
    //     0xb92188: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9218c: ldr             x4, [x4, #0xa18]
    // 0xb92190: StoreField: r1->field_1b = r4
    //     0xb92190: stur            w4, [x1, #0x1b]
    // 0xb92194: r5 = Instance_VerticalDirection
    //     0xb92194: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb92198: ldr             x5, [x5, #0xa20]
    // 0xb9219c: StoreField: r1->field_23 = r5
    //     0xb9219c: stur            w5, [x1, #0x23]
    // 0xb921a0: r6 = Instance_Clip
    //     0xb921a0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb921a4: ldr             x6, [x6, #0x38]
    // 0xb921a8: StoreField: r1->field_2b = r6
    //     0xb921a8: stur            w6, [x1, #0x2b]
    // 0xb921ac: StoreField: r1->field_2f = rZR
    //     0xb921ac: stur            xzr, [x1, #0x2f]
    // 0xb921b0: ldur            x7, [fp, #-0x60]
    // 0xb921b4: StoreField: r1->field_b = r7
    //     0xb921b4: stur            w7, [x1, #0xb]
    // 0xb921b8: r0 = Padding()
    //     0xb921b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb921bc: mov             x3, x0
    // 0xb921c0: r0 = Instance_EdgeInsets
    //     0xb921c0: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fc30] Obj!EdgeInsets@d57891
    //     0xb921c4: ldr             x0, [x0, #0xc30]
    // 0xb921c8: stur            x3, [fp, #-0x60]
    // 0xb921cc: StoreField: r3->field_f = r0
    //     0xb921cc: stur            w0, [x3, #0xf]
    // 0xb921d0: ldur            x0, [fp, #-0x38]
    // 0xb921d4: StoreField: r3->field_b = r0
    //     0xb921d4: stur            w0, [x3, #0xb]
    // 0xb921d8: r1 = Null
    //     0xb921d8: mov             x1, NULL
    // 0xb921dc: r2 = 4
    //     0xb921dc: movz            x2, #0x4
    // 0xb921e0: r0 = AllocateArray()
    //     0xb921e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb921e4: mov             x2, x0
    // 0xb921e8: ldur            x0, [fp, #-0x20]
    // 0xb921ec: stur            x2, [fp, #-0x38]
    // 0xb921f0: StoreField: r2->field_f = r0
    //     0xb921f0: stur            w0, [x2, #0xf]
    // 0xb921f4: ldur            x0, [fp, #-0x60]
    // 0xb921f8: StoreField: r2->field_13 = r0
    //     0xb921f8: stur            w0, [x2, #0x13]
    // 0xb921fc: r1 = <Widget>
    //     0xb921fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb92200: r0 = AllocateGrowableArray()
    //     0xb92200: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb92204: mov             x1, x0
    // 0xb92208: ldur            x0, [fp, #-0x38]
    // 0xb9220c: stur            x1, [fp, #-0x20]
    // 0xb92210: StoreField: r1->field_f = r0
    //     0xb92210: stur            w0, [x1, #0xf]
    // 0xb92214: r2 = 4
    //     0xb92214: movz            x2, #0x4
    // 0xb92218: StoreField: r1->field_b = r2
    //     0xb92218: stur            w2, [x1, #0xb]
    // 0xb9221c: r0 = Column()
    //     0xb9221c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb92220: mov             x1, x0
    // 0xb92224: r0 = Instance_Axis
    //     0xb92224: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb92228: stur            x1, [fp, #-0x38]
    // 0xb9222c: StoreField: r1->field_f = r0
    //     0xb9222c: stur            w0, [x1, #0xf]
    // 0xb92230: r2 = Instance_MainAxisAlignment
    //     0xb92230: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb92234: ldr             x2, [x2, #0xa08]
    // 0xb92238: StoreField: r1->field_13 = r2
    //     0xb92238: stur            w2, [x1, #0x13]
    // 0xb9223c: r3 = Instance_MainAxisSize
    //     0xb9223c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb92240: ldr             x3, [x3, #0xa10]
    // 0xb92244: ArrayStore: r1[0] = r3  ; List_4
    //     0xb92244: stur            w3, [x1, #0x17]
    // 0xb92248: r4 = Instance_CrossAxisAlignment
    //     0xb92248: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9224c: ldr             x4, [x4, #0xa18]
    // 0xb92250: StoreField: r1->field_1b = r4
    //     0xb92250: stur            w4, [x1, #0x1b]
    // 0xb92254: r5 = Instance_VerticalDirection
    //     0xb92254: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb92258: ldr             x5, [x5, #0xa20]
    // 0xb9225c: StoreField: r1->field_23 = r5
    //     0xb9225c: stur            w5, [x1, #0x23]
    // 0xb92260: r6 = Instance_Clip
    //     0xb92260: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb92264: ldr             x6, [x6, #0x38]
    // 0xb92268: StoreField: r1->field_2b = r6
    //     0xb92268: stur            w6, [x1, #0x2b]
    // 0xb9226c: StoreField: r1->field_2f = rZR
    //     0xb9226c: stur            xzr, [x1, #0x2f]
    // 0xb92270: ldur            x7, [fp, #-0x20]
    // 0xb92274: StoreField: r1->field_b = r7
    //     0xb92274: stur            w7, [x1, #0xb]
    // 0xb92278: r0 = SvgPicture()
    //     0xb92278: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb9227c: stur            x0, [fp, #-0x20]
    // 0xb92280: r16 = Instance_BoxFit
    //     0xb92280: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb92284: ldr             x16, [x16, #0xb18]
    // 0xb92288: str             x16, [SP]
    // 0xb9228c: mov             x1, x0
    // 0xb92290: r2 = "assets/images/product_between_icon.svg"
    //     0xb92290: add             x2, PP, #0x35, lsl #12  ; [pp+0x35f28] "assets/images/product_between_icon.svg"
    //     0xb92294: ldr             x2, [x2, #0xf28]
    // 0xb92298: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb92298: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb9229c: ldr             x4, [x4, #0xb0]
    // 0xb922a0: r0 = SvgPicture.asset()
    //     0xb922a0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb922a4: r1 = <StackParentData>
    //     0xb922a4: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb922a8: ldr             x1, [x1, #0x8e0]
    // 0xb922ac: r0 = Positioned()
    //     0xb922ac: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb922b0: mov             x3, x0
    // 0xb922b4: r0 = 0.000000
    //     0xb922b4: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb922b8: stur            x3, [fp, #-0x60]
    // 0xb922bc: StoreField: r3->field_13 = r0
    //     0xb922bc: stur            w0, [x3, #0x13]
    // 0xb922c0: ArrayStore: r3[0] = r0  ; List_4
    //     0xb922c0: stur            w0, [x3, #0x17]
    // 0xb922c4: StoreField: r3->field_1b = r0
    //     0xb922c4: stur            w0, [x3, #0x1b]
    // 0xb922c8: StoreField: r3->field_1f = r0
    //     0xb922c8: stur            w0, [x3, #0x1f]
    // 0xb922cc: ldur            x0, [fp, #-0x20]
    // 0xb922d0: StoreField: r3->field_b = r0
    //     0xb922d0: stur            w0, [x3, #0xb]
    // 0xb922d4: r1 = Null
    //     0xb922d4: mov             x1, NULL
    // 0xb922d8: r2 = 4
    //     0xb922d8: movz            x2, #0x4
    // 0xb922dc: r0 = AllocateArray()
    //     0xb922dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb922e0: mov             x2, x0
    // 0xb922e4: ldur            x0, [fp, #-0x38]
    // 0xb922e8: stur            x2, [fp, #-0x20]
    // 0xb922ec: StoreField: r2->field_f = r0
    //     0xb922ec: stur            w0, [x2, #0xf]
    // 0xb922f0: ldur            x0, [fp, #-0x60]
    // 0xb922f4: StoreField: r2->field_13 = r0
    //     0xb922f4: stur            w0, [x2, #0x13]
    // 0xb922f8: r1 = <Widget>
    //     0xb922f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb922fc: r0 = AllocateGrowableArray()
    //     0xb922fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb92300: mov             x1, x0
    // 0xb92304: ldur            x0, [fp, #-0x20]
    // 0xb92308: stur            x1, [fp, #-0x38]
    // 0xb9230c: StoreField: r1->field_f = r0
    //     0xb9230c: stur            w0, [x1, #0xf]
    // 0xb92310: r2 = 4
    //     0xb92310: movz            x2, #0x4
    // 0xb92314: StoreField: r1->field_b = r2
    //     0xb92314: stur            w2, [x1, #0xb]
    // 0xb92318: r0 = Stack()
    //     0xb92318: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb9231c: mov             x2, x0
    // 0xb92320: r0 = Instance_Alignment
    //     0xb92320: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb92324: ldr             x0, [x0, #0xb10]
    // 0xb92328: stur            x2, [fp, #-0x20]
    // 0xb9232c: StoreField: r2->field_f = r0
    //     0xb9232c: stur            w0, [x2, #0xf]
    // 0xb92330: r0 = Instance_StackFit
    //     0xb92330: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb92334: ldr             x0, [x0, #0xfa8]
    // 0xb92338: ArrayStore: r2[0] = r0  ; List_4
    //     0xb92338: stur            w0, [x2, #0x17]
    // 0xb9233c: r0 = Instance_Clip
    //     0xb9233c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb92340: ldr             x0, [x0, #0x7e0]
    // 0xb92344: StoreField: r2->field_1b = r0
    //     0xb92344: stur            w0, [x2, #0x1b]
    // 0xb92348: ldur            x0, [fp, #-0x38]
    // 0xb9234c: StoreField: r2->field_b = r0
    //     0xb9234c: stur            w0, [x2, #0xb]
    // 0xb92350: ldur            x3, [fp, #-8]
    // 0xb92354: LoadField: r0 = r3->field_b
    //     0xb92354: ldur            w0, [x3, #0xb]
    // 0xb92358: DecompressPointer r0
    //     0xb92358: add             x0, x0, HEAP, lsl #32
    // 0xb9235c: cmp             w0, NULL
    // 0xb92360: b.eq            #0xb936f0
    // 0xb92364: LoadField: r1 = r0->field_2b
    //     0xb92364: ldur            w1, [x0, #0x2b]
    // 0xb92368: DecompressPointer r1
    //     0xb92368: add             x1, x1, HEAP, lsl #32
    // 0xb9236c: cmp             w1, NULL
    // 0xb92370: b.ne            #0xb9237c
    // 0xb92374: r0 = Null
    //     0xb92374: mov             x0, NULL
    // 0xb92378: b               #0xb92394
    // 0xb9237c: r0 = LoadClassIdInstr(r1)
    //     0xb9237c: ldur            x0, [x1, #-1]
    //     0xb92380: ubfx            x0, x0, #0xc, #0x14
    // 0xb92384: r0 = GDT[cid_x0 + 0xe517]()
    //     0xb92384: movz            x17, #0xe517
    //     0xb92388: add             lr, x0, x17
    //     0xb9238c: ldr             lr, [x21, lr, lsl #3]
    //     0xb92390: blr             lr
    // 0xb92394: cmp             w0, NULL
    // 0xb92398: b.ne            #0xb923a4
    // 0xb9239c: r1 = false
    //     0xb9239c: add             x1, NULL, #0x30  ; false
    // 0xb923a0: b               #0xb923a8
    // 0xb923a4: mov             x1, x0
    // 0xb923a8: ldur            x0, [fp, #-8]
    // 0xb923ac: stur            x1, [fp, #-0x68]
    // 0xb923b0: LoadField: r2 = r0->field_b
    //     0xb923b0: ldur            w2, [x0, #0xb]
    // 0xb923b4: DecompressPointer r2
    //     0xb923b4: add             x2, x2, HEAP, lsl #32
    // 0xb923b8: cmp             w2, NULL
    // 0xb923bc: b.eq            #0xb936f4
    // 0xb923c0: LoadField: r3 = r2->field_2b
    //     0xb923c0: ldur            w3, [x2, #0x2b]
    // 0xb923c4: DecompressPointer r3
    //     0xb923c4: add             x3, x3, HEAP, lsl #32
    // 0xb923c8: stur            x3, [fp, #-0x60]
    // 0xb923cc: LoadField: r4 = r2->field_27
    //     0xb923cc: ldur            w4, [x2, #0x27]
    // 0xb923d0: DecompressPointer r4
    //     0xb923d0: add             x4, x4, HEAP, lsl #32
    // 0xb923d4: stur            x4, [fp, #-0x38]
    // 0xb923d8: r0 = CustomisedStrip()
    //     0xb923d8: bl              #0xa1c2f0  ; AllocateCustomisedStripStub -> CustomisedStrip (size=0x18)
    // 0xb923dc: mov             x1, x0
    // 0xb923e0: ldur            x0, [fp, #-0x60]
    // 0xb923e4: stur            x1, [fp, #-0x70]
    // 0xb923e8: StoreField: r1->field_b = r0
    //     0xb923e8: stur            w0, [x1, #0xb]
    // 0xb923ec: ldur            x0, [fp, #-0x38]
    // 0xb923f0: StoreField: r1->field_13 = r0
    //     0xb923f0: stur            w0, [x1, #0x13]
    // 0xb923f4: r0 = Padding()
    //     0xb923f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb923f8: mov             x1, x0
    // 0xb923fc: r0 = Instance_EdgeInsets
    //     0xb923fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb92400: ldr             x0, [x0, #0x668]
    // 0xb92404: stur            x1, [fp, #-0x38]
    // 0xb92408: StoreField: r1->field_f = r0
    //     0xb92408: stur            w0, [x1, #0xf]
    // 0xb9240c: ldur            x2, [fp, #-0x70]
    // 0xb92410: StoreField: r1->field_b = r2
    //     0xb92410: stur            w2, [x1, #0xb]
    // 0xb92414: r0 = Visibility()
    //     0xb92414: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb92418: mov             x2, x0
    // 0xb9241c: ldur            x0, [fp, #-0x38]
    // 0xb92420: stur            x2, [fp, #-0x60]
    // 0xb92424: StoreField: r2->field_b = r0
    //     0xb92424: stur            w0, [x2, #0xb]
    // 0xb92428: r0 = Instance_SizedBox
    //     0xb92428: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb9242c: StoreField: r2->field_f = r0
    //     0xb9242c: stur            w0, [x2, #0xf]
    // 0xb92430: ldur            x0, [fp, #-0x68]
    // 0xb92434: StoreField: r2->field_13 = r0
    //     0xb92434: stur            w0, [x2, #0x13]
    // 0xb92438: r0 = false
    //     0xb92438: add             x0, NULL, #0x30  ; false
    // 0xb9243c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9243c: stur            w0, [x2, #0x17]
    // 0xb92440: StoreField: r2->field_1b = r0
    //     0xb92440: stur            w0, [x2, #0x1b]
    // 0xb92444: StoreField: r2->field_1f = r0
    //     0xb92444: stur            w0, [x2, #0x1f]
    // 0xb92448: StoreField: r2->field_23 = r0
    //     0xb92448: stur            w0, [x2, #0x23]
    // 0xb9244c: StoreField: r2->field_27 = r0
    //     0xb9244c: stur            w0, [x2, #0x27]
    // 0xb92450: StoreField: r2->field_2b = r0
    //     0xb92450: stur            w0, [x2, #0x2b]
    // 0xb92454: r1 = Instance_Color
    //     0xb92454: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb92458: d0 = 0.700000
    //     0xb92458: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb9245c: ldr             d0, [x17, #0xf48]
    // 0xb92460: r0 = withOpacity()
    //     0xb92460: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb92464: r16 = 12.000000
    //     0xb92464: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb92468: ldr             x16, [x16, #0x9e8]
    // 0xb9246c: stp             x16, x0, [SP]
    // 0xb92470: ldur            x1, [fp, #-0x28]
    // 0xb92474: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb92474: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb92478: ldr             x4, [x4, #0x9b8]
    // 0xb9247c: r0 = copyWith()
    //     0xb9247c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb92480: stur            x0, [fp, #-0x38]
    // 0xb92484: r0 = TextSpan()
    //     0xb92484: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb92488: mov             x3, x0
    // 0xb9248c: r0 = "• Exchange credit will be added "
    //     0xb9248c: add             x0, PP, #0x53, lsl #12  ; [pp+0x539c8] "• Exchange credit will be added "
    //     0xb92490: ldr             x0, [x0, #0x9c8]
    // 0xb92494: stur            x3, [fp, #-0x68]
    // 0xb92498: StoreField: r3->field_b = r0
    //     0xb92498: stur            w0, [x3, #0xb]
    // 0xb9249c: r0 = Instance__DeferringMouseCursor
    //     0xb9249c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb924a0: ArrayStore: r3[0] = r0  ; List_4
    //     0xb924a0: stur            w0, [x3, #0x17]
    // 0xb924a4: ldur            x1, [fp, #-0x38]
    // 0xb924a8: StoreField: r3->field_7 = r1
    //     0xb924a8: stur            w1, [x3, #7]
    // 0xb924ac: r1 = Null
    //     0xb924ac: mov             x1, NULL
    // 0xb924b0: r2 = 4
    //     0xb924b0: movz            x2, #0x4
    // 0xb924b4: r0 = AllocateArray()
    //     0xb924b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb924b8: r16 = "worth "
    //     0xb924b8: add             x16, PP, #0x53, lsl #12  ; [pp+0x539d0] "worth "
    //     0xb924bc: ldr             x16, [x16, #0x9d0]
    // 0xb924c0: StoreField: r0->field_f = r16
    //     0xb924c0: stur            w16, [x0, #0xf]
    // 0xb924c4: ldur            x1, [fp, #-8]
    // 0xb924c8: LoadField: r2 = r1->field_b
    //     0xb924c8: ldur            w2, [x1, #0xb]
    // 0xb924cc: DecompressPointer r2
    //     0xb924cc: add             x2, x2, HEAP, lsl #32
    // 0xb924d0: cmp             w2, NULL
    // 0xb924d4: b.eq            #0xb936f8
    // 0xb924d8: LoadField: r3 = r2->field_b
    //     0xb924d8: ldur            w3, [x2, #0xb]
    // 0xb924dc: DecompressPointer r3
    //     0xb924dc: add             x3, x3, HEAP, lsl #32
    // 0xb924e0: cmp             w3, NULL
    // 0xb924e4: b.ne            #0xb924f0
    // 0xb924e8: r2 = Null
    //     0xb924e8: mov             x2, NULL
    // 0xb924ec: b               #0xb92528
    // 0xb924f0: LoadField: r2 = r3->field_b
    //     0xb924f0: ldur            w2, [x3, #0xb]
    // 0xb924f4: DecompressPointer r2
    //     0xb924f4: add             x2, x2, HEAP, lsl #32
    // 0xb924f8: cmp             w2, NULL
    // 0xb924fc: b.ne            #0xb92508
    // 0xb92500: r2 = Null
    //     0xb92500: mov             x2, NULL
    // 0xb92504: b               #0xb92528
    // 0xb92508: LoadField: r3 = r2->field_b
    //     0xb92508: ldur            w3, [x2, #0xb]
    // 0xb9250c: DecompressPointer r3
    //     0xb9250c: add             x3, x3, HEAP, lsl #32
    // 0xb92510: cmp             w3, NULL
    // 0xb92514: b.ne            #0xb92520
    // 0xb92518: r2 = Null
    //     0xb92518: mov             x2, NULL
    // 0xb9251c: b               #0xb92528
    // 0xb92520: LoadField: r2 = r3->field_7
    //     0xb92520: ldur            w2, [x3, #7]
    // 0xb92524: DecompressPointer r2
    //     0xb92524: add             x2, x2, HEAP, lsl #32
    // 0xb92528: cmp             w2, NULL
    // 0xb9252c: b.ne            #0xb92538
    // 0xb92530: r3 = ""
    //     0xb92530: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb92534: b               #0xb9253c
    // 0xb92538: mov             x3, x2
    // 0xb9253c: ldur            x2, [fp, #-0x68]
    // 0xb92540: StoreField: r0->field_13 = r3
    //     0xb92540: stur            w3, [x0, #0x13]
    // 0xb92544: str             x0, [SP]
    // 0xb92548: r0 = _interpolate()
    //     0xb92548: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb9254c: r1 = Instance_Color
    //     0xb9254c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb92550: d0 = 0.700000
    //     0xb92550: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb92554: ldr             d0, [x17, #0xf48]
    // 0xb92558: stur            x0, [fp, #-0x38]
    // 0xb9255c: r0 = withOpacity()
    //     0xb9255c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb92560: r16 = 12.000000
    //     0xb92560: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb92564: ldr             x16, [x16, #0x9e8]
    // 0xb92568: stp             x16, x0, [SP]
    // 0xb9256c: ldur            x1, [fp, #-0x18]
    // 0xb92570: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb92570: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb92574: ldr             x4, [x4, #0x9b8]
    // 0xb92578: r0 = copyWith()
    //     0xb92578: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9257c: stur            x0, [fp, #-0x70]
    // 0xb92580: r0 = TextSpan()
    //     0xb92580: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb92584: mov             x3, x0
    // 0xb92588: ldur            x0, [fp, #-0x38]
    // 0xb9258c: stur            x3, [fp, #-0x78]
    // 0xb92590: StoreField: r3->field_b = r0
    //     0xb92590: stur            w0, [x3, #0xb]
    // 0xb92594: r0 = Instance__DeferringMouseCursor
    //     0xb92594: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb92598: ArrayStore: r3[0] = r0  ; List_4
    //     0xb92598: stur            w0, [x3, #0x17]
    // 0xb9259c: ldur            x1, [fp, #-0x70]
    // 0xb925a0: StoreField: r3->field_7 = r1
    //     0xb925a0: stur            w1, [x3, #7]
    // 0xb925a4: r1 = Null
    //     0xb925a4: mov             x1, NULL
    // 0xb925a8: r2 = 4
    //     0xb925a8: movz            x2, #0x4
    // 0xb925ac: r0 = AllocateArray()
    //     0xb925ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb925b0: mov             x2, x0
    // 0xb925b4: ldur            x0, [fp, #-0x68]
    // 0xb925b8: stur            x2, [fp, #-0x38]
    // 0xb925bc: StoreField: r2->field_f = r0
    //     0xb925bc: stur            w0, [x2, #0xf]
    // 0xb925c0: ldur            x0, [fp, #-0x78]
    // 0xb925c4: StoreField: r2->field_13 = r0
    //     0xb925c4: stur            w0, [x2, #0x13]
    // 0xb925c8: r1 = <InlineSpan>
    //     0xb925c8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb925cc: ldr             x1, [x1, #0xe40]
    // 0xb925d0: r0 = AllocateGrowableArray()
    //     0xb925d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb925d4: mov             x1, x0
    // 0xb925d8: ldur            x0, [fp, #-0x38]
    // 0xb925dc: stur            x1, [fp, #-0x68]
    // 0xb925e0: StoreField: r1->field_f = r0
    //     0xb925e0: stur            w0, [x1, #0xf]
    // 0xb925e4: r2 = 4
    //     0xb925e4: movz            x2, #0x4
    // 0xb925e8: StoreField: r1->field_b = r2
    //     0xb925e8: stur            w2, [x1, #0xb]
    // 0xb925ec: r0 = TextSpan()
    //     0xb925ec: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb925f0: mov             x1, x0
    // 0xb925f4: ldur            x0, [fp, #-0x68]
    // 0xb925f8: stur            x1, [fp, #-0x38]
    // 0xb925fc: StoreField: r1->field_f = r0
    //     0xb925fc: stur            w0, [x1, #0xf]
    // 0xb92600: r0 = Instance__DeferringMouseCursor
    //     0xb92600: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb92604: ArrayStore: r1[0] = r0  ; List_4
    //     0xb92604: stur            w0, [x1, #0x17]
    // 0xb92608: r0 = RichText()
    //     0xb92608: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb9260c: mov             x1, x0
    // 0xb92610: ldur            x2, [fp, #-0x38]
    // 0xb92614: stur            x0, [fp, #-0x38]
    // 0xb92618: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb92618: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb9261c: r0 = RichText()
    //     0xb9261c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb92620: r0 = Padding()
    //     0xb92620: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb92624: mov             x2, x0
    // 0xb92628: r0 = Instance_EdgeInsets
    //     0xb92628: add             x0, PP, #0x56, lsl #12  ; [pp+0x56028] Obj!EdgeInsets@d59181
    //     0xb9262c: ldr             x0, [x0, #0x28]
    // 0xb92630: stur            x2, [fp, #-0x68]
    // 0xb92634: StoreField: r2->field_f = r0
    //     0xb92634: stur            w0, [x2, #0xf]
    // 0xb92638: ldur            x0, [fp, #-0x38]
    // 0xb9263c: StoreField: r2->field_b = r0
    //     0xb9263c: stur            w0, [x2, #0xb]
    // 0xb92640: r1 = Instance_Color
    //     0xb92640: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb92644: d0 = 0.700000
    //     0xb92644: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb92648: ldr             d0, [x17, #0xf48]
    // 0xb9264c: r0 = withOpacity()
    //     0xb9264c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb92650: r16 = 12.000000
    //     0xb92650: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb92654: ldr             x16, [x16, #0x9e8]
    // 0xb92658: stp             x16, x0, [SP]
    // 0xb9265c: ldur            x1, [fp, #-0x28]
    // 0xb92660: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb92660: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb92664: ldr             x4, [x4, #0x9b8]
    // 0xb92668: r0 = copyWith()
    //     0xb92668: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9266c: stur            x0, [fp, #-0x38]
    // 0xb92670: r0 = TextSpan()
    //     0xb92670: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb92674: mov             x2, x0
    // 0xb92678: r0 = "• Exchange any new item by "
    //     0xb92678: add             x0, PP, #0x53, lsl #12  ; [pp+0x539e0] "• Exchange any new item by "
    //     0xb9267c: ldr             x0, [x0, #0x9e0]
    // 0xb92680: stur            x2, [fp, #-0x70]
    // 0xb92684: StoreField: r2->field_b = r0
    //     0xb92684: stur            w0, [x2, #0xb]
    // 0xb92688: r0 = Instance__DeferringMouseCursor
    //     0xb92688: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb9268c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9268c: stur            w0, [x2, #0x17]
    // 0xb92690: ldur            x1, [fp, #-0x38]
    // 0xb92694: StoreField: r2->field_7 = r1
    //     0xb92694: stur            w1, [x2, #7]
    // 0xb92698: ldur            x3, [fp, #-8]
    // 0xb9269c: LoadField: r1 = r3->field_b
    //     0xb9269c: ldur            w1, [x3, #0xb]
    // 0xb926a0: DecompressPointer r1
    //     0xb926a0: add             x1, x1, HEAP, lsl #32
    // 0xb926a4: cmp             w1, NULL
    // 0xb926a8: b.eq            #0xb936fc
    // 0xb926ac: LoadField: r4 = r1->field_b
    //     0xb926ac: ldur            w4, [x1, #0xb]
    // 0xb926b0: DecompressPointer r4
    //     0xb926b0: add             x4, x4, HEAP, lsl #32
    // 0xb926b4: cmp             w4, NULL
    // 0xb926b8: b.ne            #0xb926c4
    // 0xb926bc: r1 = Null
    //     0xb926bc: mov             x1, NULL
    // 0xb926c0: b               #0xb926e8
    // 0xb926c4: LoadField: r1 = r4->field_b
    //     0xb926c4: ldur            w1, [x4, #0xb]
    // 0xb926c8: DecompressPointer r1
    //     0xb926c8: add             x1, x1, HEAP, lsl #32
    // 0xb926cc: cmp             w1, NULL
    // 0xb926d0: b.ne            #0xb926dc
    // 0xb926d4: r1 = Null
    //     0xb926d4: mov             x1, NULL
    // 0xb926d8: b               #0xb926e8
    // 0xb926dc: LoadField: r4 = r1->field_13
    //     0xb926dc: ldur            w4, [x1, #0x13]
    // 0xb926e0: DecompressPointer r4
    //     0xb926e0: add             x4, x4, HEAP, lsl #32
    // 0xb926e4: mov             x1, x4
    // 0xb926e8: cmp             w1, NULL
    // 0xb926ec: b.ne            #0xb926f8
    // 0xb926f0: r7 = ""
    //     0xb926f0: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb926f4: b               #0xb926fc
    // 0xb926f8: mov             x7, x1
    // 0xb926fc: ldur            x6, [fp, #-0x20]
    // 0xb92700: ldur            x5, [fp, #-0x60]
    // 0xb92704: ldur            x4, [fp, #-0x68]
    // 0xb92708: stur            x7, [fp, #-0x38]
    // 0xb9270c: r1 = Instance_Color
    //     0xb9270c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb92710: d0 = 0.700000
    //     0xb92710: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb92714: ldr             d0, [x17, #0xf48]
    // 0xb92718: r0 = withOpacity()
    //     0xb92718: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb9271c: r16 = 12.000000
    //     0xb9271c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb92720: ldr             x16, [x16, #0x9e8]
    // 0xb92724: stp             x16, x0, [SP]
    // 0xb92728: ldur            x1, [fp, #-0x18]
    // 0xb9272c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb9272c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb92730: ldr             x4, [x4, #0x9b8]
    // 0xb92734: r0 = copyWith()
    //     0xb92734: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb92738: stur            x0, [fp, #-0x78]
    // 0xb9273c: r0 = TextSpan()
    //     0xb9273c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb92740: mov             x3, x0
    // 0xb92744: ldur            x0, [fp, #-0x38]
    // 0xb92748: stur            x3, [fp, #-0x80]
    // 0xb9274c: StoreField: r3->field_b = r0
    //     0xb9274c: stur            w0, [x3, #0xb]
    // 0xb92750: r0 = Instance__DeferringMouseCursor
    //     0xb92750: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb92754: ArrayStore: r3[0] = r0  ; List_4
    //     0xb92754: stur            w0, [x3, #0x17]
    // 0xb92758: ldur            x1, [fp, #-0x78]
    // 0xb9275c: StoreField: r3->field_7 = r1
    //     0xb9275c: stur            w1, [x3, #7]
    // 0xb92760: r1 = Null
    //     0xb92760: mov             x1, NULL
    // 0xb92764: r2 = 4
    //     0xb92764: movz            x2, #0x4
    // 0xb92768: r0 = AllocateArray()
    //     0xb92768: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9276c: mov             x2, x0
    // 0xb92770: ldur            x0, [fp, #-0x70]
    // 0xb92774: stur            x2, [fp, #-0x38]
    // 0xb92778: StoreField: r2->field_f = r0
    //     0xb92778: stur            w0, [x2, #0xf]
    // 0xb9277c: ldur            x0, [fp, #-0x80]
    // 0xb92780: StoreField: r2->field_13 = r0
    //     0xb92780: stur            w0, [x2, #0x13]
    // 0xb92784: r1 = <InlineSpan>
    //     0xb92784: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb92788: ldr             x1, [x1, #0xe40]
    // 0xb9278c: r0 = AllocateGrowableArray()
    //     0xb9278c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb92790: mov             x1, x0
    // 0xb92794: ldur            x0, [fp, #-0x38]
    // 0xb92798: stur            x1, [fp, #-0x70]
    // 0xb9279c: StoreField: r1->field_f = r0
    //     0xb9279c: stur            w0, [x1, #0xf]
    // 0xb927a0: r2 = 4
    //     0xb927a0: movz            x2, #0x4
    // 0xb927a4: StoreField: r1->field_b = r2
    //     0xb927a4: stur            w2, [x1, #0xb]
    // 0xb927a8: r0 = TextSpan()
    //     0xb927a8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb927ac: mov             x1, x0
    // 0xb927b0: ldur            x0, [fp, #-0x70]
    // 0xb927b4: stur            x1, [fp, #-0x38]
    // 0xb927b8: StoreField: r1->field_f = r0
    //     0xb927b8: stur            w0, [x1, #0xf]
    // 0xb927bc: r0 = Instance__DeferringMouseCursor
    //     0xb927bc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb927c0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb927c0: stur            w0, [x1, #0x17]
    // 0xb927c4: r0 = RichText()
    //     0xb927c4: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb927c8: mov             x1, x0
    // 0xb927cc: ldur            x2, [fp, #-0x38]
    // 0xb927d0: stur            x0, [fp, #-0x38]
    // 0xb927d4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb927d4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb927d8: r0 = RichText()
    //     0xb927d8: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb927dc: r0 = Padding()
    //     0xb927dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb927e0: mov             x3, x0
    // 0xb927e4: r0 = Instance_EdgeInsets
    //     0xb927e4: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f30] Obj!EdgeInsets@d57b31
    //     0xb927e8: ldr             x0, [x0, #0xf30]
    // 0xb927ec: stur            x3, [fp, #-0x70]
    // 0xb927f0: StoreField: r3->field_f = r0
    //     0xb927f0: stur            w0, [x3, #0xf]
    // 0xb927f4: ldur            x0, [fp, #-0x38]
    // 0xb927f8: StoreField: r3->field_b = r0
    //     0xb927f8: stur            w0, [x3, #0xb]
    // 0xb927fc: r1 = Null
    //     0xb927fc: mov             x1, NULL
    // 0xb92800: r2 = 8
    //     0xb92800: movz            x2, #0x8
    // 0xb92804: r0 = AllocateArray()
    //     0xb92804: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb92808: mov             x2, x0
    // 0xb9280c: ldur            x0, [fp, #-0x20]
    // 0xb92810: stur            x2, [fp, #-0x38]
    // 0xb92814: StoreField: r2->field_f = r0
    //     0xb92814: stur            w0, [x2, #0xf]
    // 0xb92818: ldur            x0, [fp, #-0x60]
    // 0xb9281c: StoreField: r2->field_13 = r0
    //     0xb9281c: stur            w0, [x2, #0x13]
    // 0xb92820: ldur            x0, [fp, #-0x68]
    // 0xb92824: ArrayStore: r2[0] = r0  ; List_4
    //     0xb92824: stur            w0, [x2, #0x17]
    // 0xb92828: ldur            x0, [fp, #-0x70]
    // 0xb9282c: StoreField: r2->field_1b = r0
    //     0xb9282c: stur            w0, [x2, #0x1b]
    // 0xb92830: r1 = <Widget>
    //     0xb92830: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb92834: r0 = AllocateGrowableArray()
    //     0xb92834: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb92838: mov             x1, x0
    // 0xb9283c: ldur            x0, [fp, #-0x38]
    // 0xb92840: stur            x1, [fp, #-0x20]
    // 0xb92844: StoreField: r1->field_f = r0
    //     0xb92844: stur            w0, [x1, #0xf]
    // 0xb92848: r0 = 8
    //     0xb92848: movz            x0, #0x8
    // 0xb9284c: StoreField: r1->field_b = r0
    //     0xb9284c: stur            w0, [x1, #0xb]
    // 0xb92850: r0 = Column()
    //     0xb92850: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb92854: mov             x1, x0
    // 0xb92858: r0 = Instance_Axis
    //     0xb92858: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb9285c: stur            x1, [fp, #-0x38]
    // 0xb92860: StoreField: r1->field_f = r0
    //     0xb92860: stur            w0, [x1, #0xf]
    // 0xb92864: r2 = Instance_MainAxisAlignment
    //     0xb92864: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb92868: ldr             x2, [x2, #0xa08]
    // 0xb9286c: StoreField: r1->field_13 = r2
    //     0xb9286c: stur            w2, [x1, #0x13]
    // 0xb92870: r3 = Instance_MainAxisSize
    //     0xb92870: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb92874: ldr             x3, [x3, #0xa10]
    // 0xb92878: ArrayStore: r1[0] = r3  ; List_4
    //     0xb92878: stur            w3, [x1, #0x17]
    // 0xb9287c: r4 = Instance_CrossAxisAlignment
    //     0xb9287c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb92880: ldr             x4, [x4, #0x890]
    // 0xb92884: StoreField: r1->field_1b = r4
    //     0xb92884: stur            w4, [x1, #0x1b]
    // 0xb92888: r5 = Instance_VerticalDirection
    //     0xb92888: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9288c: ldr             x5, [x5, #0xa20]
    // 0xb92890: StoreField: r1->field_23 = r5
    //     0xb92890: stur            w5, [x1, #0x23]
    // 0xb92894: r6 = Instance_Clip
    //     0xb92894: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb92898: ldr             x6, [x6, #0x38]
    // 0xb9289c: StoreField: r1->field_2b = r6
    //     0xb9289c: stur            w6, [x1, #0x2b]
    // 0xb928a0: StoreField: r1->field_2f = rZR
    //     0xb928a0: stur            xzr, [x1, #0x2f]
    // 0xb928a4: ldur            x7, [fp, #-0x20]
    // 0xb928a8: StoreField: r1->field_b = r7
    //     0xb928a8: stur            w7, [x1, #0xb]
    // 0xb928ac: r0 = Container()
    //     0xb928ac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb928b0: stur            x0, [fp, #-0x20]
    // 0xb928b4: ldur            x16, [fp, #-0x58]
    // 0xb928b8: ldur            lr, [fp, #-0x38]
    // 0xb928bc: stp             lr, x16, [SP]
    // 0xb928c0: mov             x1, x0
    // 0xb928c4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb928c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb928c8: ldr             x4, [x4, #0x88]
    // 0xb928cc: r0 = Container()
    //     0xb928cc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb928d0: r0 = Padding()
    //     0xb928d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb928d4: mov             x2, x0
    // 0xb928d8: r0 = Instance_EdgeInsets
    //     0xb928d8: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0e0] Obj!EdgeInsets@d594b1
    //     0xb928dc: ldr             x0, [x0, #0xe0]
    // 0xb928e0: stur            x2, [fp, #-0x38]
    // 0xb928e4: StoreField: r2->field_f = r0
    //     0xb928e4: stur            w0, [x2, #0xf]
    // 0xb928e8: ldur            x0, [fp, #-0x20]
    // 0xb928ec: StoreField: r2->field_b = r0
    //     0xb928ec: stur            w0, [x2, #0xb]
    // 0xb928f0: r1 = Instance_Color
    //     0xb928f0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb928f4: d0 = 0.100000
    //     0xb928f4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb928f8: r0 = withOpacity()
    //     0xb928f8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb928fc: mov             x2, x0
    // 0xb92900: r1 = Null
    //     0xb92900: mov             x1, NULL
    // 0xb92904: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb92904: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb92908: r0 = Border.all()
    //     0xb92908: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb9290c: stur            x0, [fp, #-0x20]
    // 0xb92910: r0 = Radius()
    //     0xb92910: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb92914: d0 = 20.000000
    //     0xb92914: fmov            d0, #20.00000000
    // 0xb92918: stur            x0, [fp, #-0x58]
    // 0xb9291c: StoreField: r0->field_7 = d0
    //     0xb9291c: stur            d0, [x0, #7]
    // 0xb92920: StoreField: r0->field_f = d0
    //     0xb92920: stur            d0, [x0, #0xf]
    // 0xb92924: r0 = BorderRadius()
    //     0xb92924: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb92928: mov             x1, x0
    // 0xb9292c: ldur            x0, [fp, #-0x58]
    // 0xb92930: stur            x1, [fp, #-0x60]
    // 0xb92934: StoreField: r1->field_7 = r0
    //     0xb92934: stur            w0, [x1, #7]
    // 0xb92938: StoreField: r1->field_b = r0
    //     0xb92938: stur            w0, [x1, #0xb]
    // 0xb9293c: StoreField: r1->field_f = r0
    //     0xb9293c: stur            w0, [x1, #0xf]
    // 0xb92940: StoreField: r1->field_13 = r0
    //     0xb92940: stur            w0, [x1, #0x13]
    // 0xb92944: r0 = BoxDecoration()
    //     0xb92944: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb92948: mov             x3, x0
    // 0xb9294c: ldur            x0, [fp, #-0x20]
    // 0xb92950: stur            x3, [fp, #-0x58]
    // 0xb92954: StoreField: r3->field_f = r0
    //     0xb92954: stur            w0, [x3, #0xf]
    // 0xb92958: ldur            x0, [fp, #-0x60]
    // 0xb9295c: StoreField: r3->field_13 = r0
    //     0xb9295c: stur            w0, [x3, #0x13]
    // 0xb92960: r0 = Instance_BoxShape
    //     0xb92960: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb92964: ldr             x0, [x0, #0x80]
    // 0xb92968: StoreField: r3->field_23 = r0
    //     0xb92968: stur            w0, [x3, #0x23]
    // 0xb9296c: ldur            x0, [fp, #-8]
    // 0xb92970: LoadField: r1 = r0->field_b
    //     0xb92970: ldur            w1, [x0, #0xb]
    // 0xb92974: DecompressPointer r1
    //     0xb92974: add             x1, x1, HEAP, lsl #32
    // 0xb92978: cmp             w1, NULL
    // 0xb9297c: b.eq            #0xb93700
    // 0xb92980: LoadField: r2 = r1->field_b
    //     0xb92980: ldur            w2, [x1, #0xb]
    // 0xb92984: DecompressPointer r2
    //     0xb92984: add             x2, x2, HEAP, lsl #32
    // 0xb92988: cmp             w2, NULL
    // 0xb9298c: b.ne            #0xb92998
    // 0xb92990: r1 = Null
    //     0xb92990: mov             x1, NULL
    // 0xb92994: b               #0xb929ec
    // 0xb92998: LoadField: r1 = r2->field_b
    //     0xb92998: ldur            w1, [x2, #0xb]
    // 0xb9299c: DecompressPointer r1
    //     0xb9299c: add             x1, x1, HEAP, lsl #32
    // 0xb929a0: cmp             w1, NULL
    // 0xb929a4: b.ne            #0xb929b0
    // 0xb929a8: r1 = Null
    //     0xb929a8: mov             x1, NULL
    // 0xb929ac: b               #0xb929ec
    // 0xb929b0: LoadField: r2 = r1->field_7
    //     0xb929b0: ldur            w2, [x1, #7]
    // 0xb929b4: DecompressPointer r2
    //     0xb929b4: add             x2, x2, HEAP, lsl #32
    // 0xb929b8: cmp             w2, NULL
    // 0xb929bc: b.ne            #0xb929c8
    // 0xb929c0: r1 = Null
    //     0xb929c0: mov             x1, NULL
    // 0xb929c4: b               #0xb929ec
    // 0xb929c8: LoadField: r1 = r2->field_1f
    //     0xb929c8: ldur            w1, [x2, #0x1f]
    // 0xb929cc: DecompressPointer r1
    //     0xb929cc: add             x1, x1, HEAP, lsl #32
    // 0xb929d0: cmp             w1, NULL
    // 0xb929d4: b.ne            #0xb929e0
    // 0xb929d8: r1 = Null
    //     0xb929d8: mov             x1, NULL
    // 0xb929dc: b               #0xb929ec
    // 0xb929e0: LoadField: r2 = r1->field_7
    //     0xb929e0: ldur            w2, [x1, #7]
    // 0xb929e4: DecompressPointer r2
    //     0xb929e4: add             x2, x2, HEAP, lsl #32
    // 0xb929e8: mov             x1, x2
    // 0xb929ec: cmp             w1, NULL
    // 0xb929f0: b.ne            #0xb929fc
    // 0xb929f4: r4 = ""
    //     0xb929f4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb929f8: b               #0xb92a00
    // 0xb929fc: mov             x4, x1
    // 0xb92a00: stur            x4, [fp, #-0x20]
    // 0xb92a04: r1 = Null
    //     0xb92a04: mov             x1, NULL
    // 0xb92a08: r2 = 4
    //     0xb92a08: movz            x2, #0x4
    // 0xb92a0c: r0 = AllocateArray()
    //     0xb92a0c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb92a10: mov             x1, x0
    // 0xb92a14: ldur            x0, [fp, #-0x20]
    // 0xb92a18: StoreField: r1->field_f = r0
    //     0xb92a18: stur            w0, [x1, #0xf]
    // 0xb92a1c: r16 = ": "
    //     0xb92a1c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] ": "
    // 0xb92a20: StoreField: r1->field_13 = r16
    //     0xb92a20: stur            w16, [x1, #0x13]
    // 0xb92a24: str             x1, [SP]
    // 0xb92a28: r0 = _interpolate()
    //     0xb92a28: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb92a2c: r1 = Instance_Color
    //     0xb92a2c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb92a30: d0 = 0.700000
    //     0xb92a30: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb92a34: ldr             d0, [x17, #0xf48]
    // 0xb92a38: stur            x0, [fp, #-0x20]
    // 0xb92a3c: r0 = withOpacity()
    //     0xb92a3c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb92a40: r16 = 12.000000
    //     0xb92a40: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb92a44: ldr             x16, [x16, #0x9e8]
    // 0xb92a48: stp             x16, x0, [SP]
    // 0xb92a4c: ldur            x1, [fp, #-0x18]
    // 0xb92a50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb92a50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb92a54: ldr             x4, [x4, #0x9b8]
    // 0xb92a58: r0 = copyWith()
    //     0xb92a58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb92a5c: stur            x0, [fp, #-0x60]
    // 0xb92a60: r0 = TextSpan()
    //     0xb92a60: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb92a64: mov             x2, x0
    // 0xb92a68: ldur            x0, [fp, #-0x20]
    // 0xb92a6c: stur            x2, [fp, #-0x68]
    // 0xb92a70: StoreField: r2->field_b = r0
    //     0xb92a70: stur            w0, [x2, #0xb]
    // 0xb92a74: r0 = Instance__DeferringMouseCursor
    //     0xb92a74: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb92a78: ArrayStore: r2[0] = r0  ; List_4
    //     0xb92a78: stur            w0, [x2, #0x17]
    // 0xb92a7c: ldur            x1, [fp, #-0x60]
    // 0xb92a80: StoreField: r2->field_7 = r1
    //     0xb92a80: stur            w1, [x2, #7]
    // 0xb92a84: ldur            x3, [fp, #-8]
    // 0xb92a88: LoadField: r1 = r3->field_b
    //     0xb92a88: ldur            w1, [x3, #0xb]
    // 0xb92a8c: DecompressPointer r1
    //     0xb92a8c: add             x1, x1, HEAP, lsl #32
    // 0xb92a90: cmp             w1, NULL
    // 0xb92a94: b.eq            #0xb93704
    // 0xb92a98: LoadField: r4 = r1->field_b
    //     0xb92a98: ldur            w4, [x1, #0xb]
    // 0xb92a9c: DecompressPointer r4
    //     0xb92a9c: add             x4, x4, HEAP, lsl #32
    // 0xb92aa0: cmp             w4, NULL
    // 0xb92aa4: b.ne            #0xb92ab0
    // 0xb92aa8: r1 = Null
    //     0xb92aa8: mov             x1, NULL
    // 0xb92aac: b               #0xb92b04
    // 0xb92ab0: LoadField: r1 = r4->field_b
    //     0xb92ab0: ldur            w1, [x4, #0xb]
    // 0xb92ab4: DecompressPointer r1
    //     0xb92ab4: add             x1, x1, HEAP, lsl #32
    // 0xb92ab8: cmp             w1, NULL
    // 0xb92abc: b.ne            #0xb92ac8
    // 0xb92ac0: r1 = Null
    //     0xb92ac0: mov             x1, NULL
    // 0xb92ac4: b               #0xb92b04
    // 0xb92ac8: LoadField: r4 = r1->field_7
    //     0xb92ac8: ldur            w4, [x1, #7]
    // 0xb92acc: DecompressPointer r4
    //     0xb92acc: add             x4, x4, HEAP, lsl #32
    // 0xb92ad0: cmp             w4, NULL
    // 0xb92ad4: b.ne            #0xb92ae0
    // 0xb92ad8: r1 = Null
    //     0xb92ad8: mov             x1, NULL
    // 0xb92adc: b               #0xb92b04
    // 0xb92ae0: LoadField: r1 = r4->field_1f
    //     0xb92ae0: ldur            w1, [x4, #0x1f]
    // 0xb92ae4: DecompressPointer r1
    //     0xb92ae4: add             x1, x1, HEAP, lsl #32
    // 0xb92ae8: cmp             w1, NULL
    // 0xb92aec: b.ne            #0xb92af8
    // 0xb92af0: r1 = Null
    //     0xb92af0: mov             x1, NULL
    // 0xb92af4: b               #0xb92b04
    // 0xb92af8: LoadField: r4 = r1->field_b
    //     0xb92af8: ldur            w4, [x1, #0xb]
    // 0xb92afc: DecompressPointer r4
    //     0xb92afc: add             x4, x4, HEAP, lsl #32
    // 0xb92b00: mov             x1, x4
    // 0xb92b04: cmp             w1, NULL
    // 0xb92b08: b.ne            #0xb92b14
    // 0xb92b0c: r4 = ""
    //     0xb92b0c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb92b10: b               #0xb92b18
    // 0xb92b14: mov             x4, x1
    // 0xb92b18: stur            x4, [fp, #-0x20]
    // 0xb92b1c: r1 = Instance_Color
    //     0xb92b1c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb92b20: d0 = 0.700000
    //     0xb92b20: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb92b24: ldr             d0, [x17, #0xf48]
    // 0xb92b28: r0 = withOpacity()
    //     0xb92b28: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb92b2c: r16 = 12.000000
    //     0xb92b2c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb92b30: ldr             x16, [x16, #0x9e8]
    // 0xb92b34: stp             x16, x0, [SP]
    // 0xb92b38: ldur            x1, [fp, #-0x28]
    // 0xb92b3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb92b3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb92b40: ldr             x4, [x4, #0x9b8]
    // 0xb92b44: r0 = copyWith()
    //     0xb92b44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb92b48: stur            x0, [fp, #-0x28]
    // 0xb92b4c: r0 = TextSpan()
    //     0xb92b4c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb92b50: mov             x3, x0
    // 0xb92b54: ldur            x0, [fp, #-0x20]
    // 0xb92b58: stur            x3, [fp, #-0x60]
    // 0xb92b5c: StoreField: r3->field_b = r0
    //     0xb92b5c: stur            w0, [x3, #0xb]
    // 0xb92b60: r0 = Instance__DeferringMouseCursor
    //     0xb92b60: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb92b64: ArrayStore: r3[0] = r0  ; List_4
    //     0xb92b64: stur            w0, [x3, #0x17]
    // 0xb92b68: ldur            x1, [fp, #-0x28]
    // 0xb92b6c: StoreField: r3->field_7 = r1
    //     0xb92b6c: stur            w1, [x3, #7]
    // 0xb92b70: r1 = Null
    //     0xb92b70: mov             x1, NULL
    // 0xb92b74: r2 = 4
    //     0xb92b74: movz            x2, #0x4
    // 0xb92b78: r0 = AllocateArray()
    //     0xb92b78: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb92b7c: mov             x2, x0
    // 0xb92b80: ldur            x0, [fp, #-0x68]
    // 0xb92b84: stur            x2, [fp, #-0x20]
    // 0xb92b88: StoreField: r2->field_f = r0
    //     0xb92b88: stur            w0, [x2, #0xf]
    // 0xb92b8c: ldur            x0, [fp, #-0x60]
    // 0xb92b90: StoreField: r2->field_13 = r0
    //     0xb92b90: stur            w0, [x2, #0x13]
    // 0xb92b94: r1 = <InlineSpan>
    //     0xb92b94: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb92b98: ldr             x1, [x1, #0xe40]
    // 0xb92b9c: r0 = AllocateGrowableArray()
    //     0xb92b9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb92ba0: mov             x1, x0
    // 0xb92ba4: ldur            x0, [fp, #-0x20]
    // 0xb92ba8: stur            x1, [fp, #-0x28]
    // 0xb92bac: StoreField: r1->field_f = r0
    //     0xb92bac: stur            w0, [x1, #0xf]
    // 0xb92bb0: r0 = 4
    //     0xb92bb0: movz            x0, #0x4
    // 0xb92bb4: StoreField: r1->field_b = r0
    //     0xb92bb4: stur            w0, [x1, #0xb]
    // 0xb92bb8: r0 = TextSpan()
    //     0xb92bb8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb92bbc: mov             x1, x0
    // 0xb92bc0: ldur            x0, [fp, #-0x28]
    // 0xb92bc4: stur            x1, [fp, #-0x20]
    // 0xb92bc8: StoreField: r1->field_f = r0
    //     0xb92bc8: stur            w0, [x1, #0xf]
    // 0xb92bcc: r0 = Instance__DeferringMouseCursor
    //     0xb92bcc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb92bd0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb92bd0: stur            w0, [x1, #0x17]
    // 0xb92bd4: r0 = RichText()
    //     0xb92bd4: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb92bd8: mov             x1, x0
    // 0xb92bdc: ldur            x2, [fp, #-0x20]
    // 0xb92be0: stur            x0, [fp, #-0x20]
    // 0xb92be4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb92be4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb92be8: r0 = RichText()
    //     0xb92be8: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb92bec: r0 = Container()
    //     0xb92bec: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb92bf0: stur            x0, [fp, #-0x28]
    // 0xb92bf4: ldur            x16, [fp, #-0x58]
    // 0xb92bf8: r30 = inf
    //     0xb92bf8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb92bfc: ldr             lr, [lr, #0x9f8]
    // 0xb92c00: stp             lr, x16, [SP, #0x10]
    // 0xb92c04: r16 = Instance_EdgeInsets
    //     0xb92c04: add             x16, PP, #0x56, lsl #12  ; [pp+0x56030] Obj!EdgeInsets@d59481
    //     0xb92c08: ldr             x16, [x16, #0x30]
    // 0xb92c0c: ldur            lr, [fp, #-0x20]
    // 0xb92c10: stp             lr, x16, [SP]
    // 0xb92c14: mov             x1, x0
    // 0xb92c18: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x1, padding, 0x3, width, 0x2, null]
    //     0xb92c18: add             x4, PP, #0x56, lsl #12  ; [pp+0x56038] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xb92c1c: ldr             x4, [x4, #0x38]
    // 0xb92c20: r0 = Container()
    //     0xb92c20: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb92c24: r0 = Padding()
    //     0xb92c24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb92c28: mov             x2, x0
    // 0xb92c2c: r0 = Instance_EdgeInsets
    //     0xb92c2c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe18] Obj!EdgeInsets@d57ef1
    //     0xb92c30: ldr             x0, [x0, #0xe18]
    // 0xb92c34: stur            x2, [fp, #-0x20]
    // 0xb92c38: StoreField: r2->field_f = r0
    //     0xb92c38: stur            w0, [x2, #0xf]
    // 0xb92c3c: ldur            x0, [fp, #-0x28]
    // 0xb92c40: StoreField: r2->field_b = r0
    //     0xb92c40: stur            w0, [x2, #0xb]
    // 0xb92c44: r1 = Instance_Color
    //     0xb92c44: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb92c48: d0 = 0.100000
    //     0xb92c48: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb92c4c: r0 = withOpacity()
    //     0xb92c4c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb92c50: stur            x0, [fp, #-0x28]
    // 0xb92c54: r0 = Divider()
    //     0xb92c54: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xb92c58: mov             x1, x0
    // 0xb92c5c: ldur            x0, [fp, #-0x28]
    // 0xb92c60: stur            x1, [fp, #-0x58]
    // 0xb92c64: StoreField: r1->field_1f = r0
    //     0xb92c64: stur            w0, [x1, #0x1f]
    // 0xb92c68: r0 = Padding()
    //     0xb92c68: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb92c6c: mov             x1, x0
    // 0xb92c70: r0 = Instance_EdgeInsets
    //     0xb92c70: add             x0, PP, #0x56, lsl #12  ; [pp+0x56040] Obj!EdgeInsets@d59451
    //     0xb92c74: ldr             x0, [x0, #0x40]
    // 0xb92c78: stur            x1, [fp, #-0x28]
    // 0xb92c7c: StoreField: r1->field_f = r0
    //     0xb92c7c: stur            w0, [x1, #0xf]
    // 0xb92c80: ldur            x0, [fp, #-0x58]
    // 0xb92c84: StoreField: r1->field_b = r0
    //     0xb92c84: stur            w0, [x1, #0xb]
    // 0xb92c88: r16 = <EdgeInsets>
    //     0xb92c88: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb92c8c: ldr             x16, [x16, #0xda0]
    // 0xb92c90: r30 = Instance_EdgeInsets
    //     0xb92c90: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb92c94: ldr             lr, [lr, #0x1f0]
    // 0xb92c98: stp             lr, x16, [SP]
    // 0xb92c9c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb92c9c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb92ca0: r0 = all()
    //     0xb92ca0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb92ca4: stur            x0, [fp, #-0x58]
    // 0xb92ca8: r16 = <Color>
    //     0xb92ca8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb92cac: ldr             x16, [x16, #0xf80]
    // 0xb92cb0: r30 = Instance_Color
    //     0xb92cb0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb92cb4: stp             lr, x16, [SP]
    // 0xb92cb8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb92cb8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb92cbc: r0 = all()
    //     0xb92cbc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb92cc0: mov             x1, x0
    // 0xb92cc4: ldur            x0, [fp, #-8]
    // 0xb92cc8: stur            x1, [fp, #-0x60]
    // 0xb92ccc: LoadField: r2 = r0->field_b
    //     0xb92ccc: ldur            w2, [x0, #0xb]
    // 0xb92cd0: DecompressPointer r2
    //     0xb92cd0: add             x2, x2, HEAP, lsl #32
    // 0xb92cd4: cmp             w2, NULL
    // 0xb92cd8: b.eq            #0xb93708
    // 0xb92cdc: LoadField: r3 = r2->field_2f
    //     0xb92cdc: ldur            w3, [x2, #0x2f]
    // 0xb92ce0: DecompressPointer r3
    //     0xb92ce0: add             x3, x3, HEAP, lsl #32
    // 0xb92ce4: LoadField: r2 = r3->field_3f
    //     0xb92ce4: ldur            w2, [x3, #0x3f]
    // 0xb92ce8: DecompressPointer r2
    //     0xb92ce8: add             x2, x2, HEAP, lsl #32
    // 0xb92cec: cmp             w2, NULL
    // 0xb92cf0: b.ne            #0xb92cfc
    // 0xb92cf4: r3 = Null
    //     0xb92cf4: mov             x3, NULL
    // 0xb92cf8: b               #0xb92d20
    // 0xb92cfc: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb92cfc: ldur            w3, [x2, #0x17]
    // 0xb92d00: DecompressPointer r3
    //     0xb92d00: add             x3, x3, HEAP, lsl #32
    // 0xb92d04: cmp             w3, NULL
    // 0xb92d08: b.ne            #0xb92d14
    // 0xb92d0c: r3 = Null
    //     0xb92d0c: mov             x3, NULL
    // 0xb92d10: b               #0xb92d20
    // 0xb92d14: LoadField: r4 = r3->field_7
    //     0xb92d14: ldur            w4, [x3, #7]
    // 0xb92d18: DecompressPointer r4
    //     0xb92d18: add             x4, x4, HEAP, lsl #32
    // 0xb92d1c: mov             x3, x4
    // 0xb92d20: cmp             w3, NULL
    // 0xb92d24: b.ne            #0xb92d30
    // 0xb92d28: r3 = 0
    //     0xb92d28: movz            x3, #0
    // 0xb92d2c: b               #0xb92d40
    // 0xb92d30: r4 = LoadInt32Instr(r3)
    //     0xb92d30: sbfx            x4, x3, #1, #0x1f
    //     0xb92d34: tbz             w3, #0, #0xb92d3c
    //     0xb92d38: ldur            x4, [x3, #7]
    // 0xb92d3c: mov             x3, x4
    // 0xb92d40: stur            x3, [fp, #-0x50]
    // 0xb92d44: cmp             w2, NULL
    // 0xb92d48: b.ne            #0xb92d54
    // 0xb92d4c: r4 = Null
    //     0xb92d4c: mov             x4, NULL
    // 0xb92d50: b               #0xb92d78
    // 0xb92d54: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb92d54: ldur            w4, [x2, #0x17]
    // 0xb92d58: DecompressPointer r4
    //     0xb92d58: add             x4, x4, HEAP, lsl #32
    // 0xb92d5c: cmp             w4, NULL
    // 0xb92d60: b.ne            #0xb92d6c
    // 0xb92d64: r4 = Null
    //     0xb92d64: mov             x4, NULL
    // 0xb92d68: b               #0xb92d78
    // 0xb92d6c: LoadField: r5 = r4->field_b
    //     0xb92d6c: ldur            w5, [x4, #0xb]
    // 0xb92d70: DecompressPointer r5
    //     0xb92d70: add             x5, x5, HEAP, lsl #32
    // 0xb92d74: mov             x4, x5
    // 0xb92d78: cmp             w4, NULL
    // 0xb92d7c: b.ne            #0xb92d88
    // 0xb92d80: r4 = 0
    //     0xb92d80: movz            x4, #0
    // 0xb92d84: b               #0xb92d98
    // 0xb92d88: r5 = LoadInt32Instr(r4)
    //     0xb92d88: sbfx            x5, x4, #1, #0x1f
    //     0xb92d8c: tbz             w4, #0, #0xb92d94
    //     0xb92d90: ldur            x5, [x4, #7]
    // 0xb92d94: mov             x4, x5
    // 0xb92d98: stur            x4, [fp, #-0x48]
    // 0xb92d9c: cmp             w2, NULL
    // 0xb92da0: b.ne            #0xb92dac
    // 0xb92da4: r2 = Null
    //     0xb92da4: mov             x2, NULL
    // 0xb92da8: b               #0xb92dcc
    // 0xb92dac: ArrayLoad: r5 = r2[0]  ; List_4
    //     0xb92dac: ldur            w5, [x2, #0x17]
    // 0xb92db0: DecompressPointer r5
    //     0xb92db0: add             x5, x5, HEAP, lsl #32
    // 0xb92db4: cmp             w5, NULL
    // 0xb92db8: b.ne            #0xb92dc4
    // 0xb92dbc: r2 = Null
    //     0xb92dbc: mov             x2, NULL
    // 0xb92dc0: b               #0xb92dcc
    // 0xb92dc4: LoadField: r2 = r5->field_f
    //     0xb92dc4: ldur            w2, [x5, #0xf]
    // 0xb92dc8: DecompressPointer r2
    //     0xb92dc8: add             x2, x2, HEAP, lsl #32
    // 0xb92dcc: cmp             w2, NULL
    // 0xb92dd0: b.ne            #0xb92ddc
    // 0xb92dd4: r5 = 0
    //     0xb92dd4: movz            x5, #0
    // 0xb92dd8: b               #0xb92de8
    // 0xb92ddc: r5 = LoadInt32Instr(r2)
    //     0xb92ddc: sbfx            x5, x2, #1, #0x1f
    //     0xb92de0: tbz             w2, #0, #0xb92de8
    //     0xb92de4: ldur            x5, [x2, #7]
    // 0xb92de8: ldur            x2, [fp, #-0x58]
    // 0xb92dec: stur            x5, [fp, #-0x40]
    // 0xb92df0: r0 = Color()
    //     0xb92df0: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb92df4: mov             x1, x0
    // 0xb92df8: r0 = Instance_ColorSpace
    //     0xb92df8: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb92dfc: stur            x1, [fp, #-0x68]
    // 0xb92e00: StoreField: r1->field_27 = r0
    //     0xb92e00: stur            w0, [x1, #0x27]
    // 0xb92e04: d0 = 0.080000
    //     0xb92e04: add             x17, PP, #0x27, lsl #12  ; [pp+0x27798] IMM: double(0.08) from 0x3fb47ae147ae147b
    //     0xb92e08: ldr             d0, [x17, #0x798]
    // 0xb92e0c: StoreField: r1->field_7 = d0
    //     0xb92e0c: stur            d0, [x1, #7]
    // 0xb92e10: ldur            x2, [fp, #-0x50]
    // 0xb92e14: ubfx            x2, x2, #0, #0x20
    // 0xb92e18: and             w3, w2, #0xff
    // 0xb92e1c: ubfx            x3, x3, #0, #0x20
    // 0xb92e20: scvtf           d0, x3
    // 0xb92e24: d1 = 255.000000
    //     0xb92e24: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb92e28: fdiv            d2, d0, d1
    // 0xb92e2c: StoreField: r1->field_f = d2
    //     0xb92e2c: stur            d2, [x1, #0xf]
    // 0xb92e30: ldur            x2, [fp, #-0x48]
    // 0xb92e34: ubfx            x2, x2, #0, #0x20
    // 0xb92e38: and             w3, w2, #0xff
    // 0xb92e3c: ubfx            x3, x3, #0, #0x20
    // 0xb92e40: scvtf           d0, x3
    // 0xb92e44: fdiv            d2, d0, d1
    // 0xb92e48: ArrayStore: r1[0] = d2  ; List_8
    //     0xb92e48: stur            d2, [x1, #0x17]
    // 0xb92e4c: ldur            x2, [fp, #-0x40]
    // 0xb92e50: ubfx            x2, x2, #0, #0x20
    // 0xb92e54: and             w3, w2, #0xff
    // 0xb92e58: ubfx            x3, x3, #0, #0x20
    // 0xb92e5c: scvtf           d0, x3
    // 0xb92e60: fdiv            d2, d0, d1
    // 0xb92e64: StoreField: r1->field_1f = d2
    //     0xb92e64: stur            d2, [x1, #0x1f]
    // 0xb92e68: r0 = BorderSide()
    //     0xb92e68: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb92e6c: mov             x1, x0
    // 0xb92e70: ldur            x0, [fp, #-0x68]
    // 0xb92e74: stur            x1, [fp, #-0x70]
    // 0xb92e78: StoreField: r1->field_7 = r0
    //     0xb92e78: stur            w0, [x1, #7]
    // 0xb92e7c: d0 = 1.000000
    //     0xb92e7c: fmov            d0, #1.00000000
    // 0xb92e80: StoreField: r1->field_b = d0
    //     0xb92e80: stur            d0, [x1, #0xb]
    // 0xb92e84: r0 = Instance_BorderStyle
    //     0xb92e84: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb92e88: ldr             x0, [x0, #0xf68]
    // 0xb92e8c: StoreField: r1->field_13 = r0
    //     0xb92e8c: stur            w0, [x1, #0x13]
    // 0xb92e90: d1 = -1.000000
    //     0xb92e90: fmov            d1, #-1.00000000
    // 0xb92e94: ArrayStore: r1[0] = d1  ; List_8
    //     0xb92e94: stur            d1, [x1, #0x17]
    // 0xb92e98: r0 = Radius()
    //     0xb92e98: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb92e9c: d0 = 32.000000
    //     0xb92e9c: add             x17, PP, #0x43, lsl #12  ; [pp+0x436c8] IMM: double(32) from 0x4040000000000000
    //     0xb92ea0: ldr             d0, [x17, #0x6c8]
    // 0xb92ea4: stur            x0, [fp, #-0x68]
    // 0xb92ea8: StoreField: r0->field_7 = d0
    //     0xb92ea8: stur            d0, [x0, #7]
    // 0xb92eac: StoreField: r0->field_f = d0
    //     0xb92eac: stur            d0, [x0, #0xf]
    // 0xb92eb0: r0 = BorderRadius()
    //     0xb92eb0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb92eb4: mov             x1, x0
    // 0xb92eb8: ldur            x0, [fp, #-0x68]
    // 0xb92ebc: stur            x1, [fp, #-0x78]
    // 0xb92ec0: StoreField: r1->field_7 = r0
    //     0xb92ec0: stur            w0, [x1, #7]
    // 0xb92ec4: StoreField: r1->field_b = r0
    //     0xb92ec4: stur            w0, [x1, #0xb]
    // 0xb92ec8: StoreField: r1->field_f = r0
    //     0xb92ec8: stur            w0, [x1, #0xf]
    // 0xb92ecc: StoreField: r1->field_13 = r0
    //     0xb92ecc: stur            w0, [x1, #0x13]
    // 0xb92ed0: r0 = RoundedRectangleBorder()
    //     0xb92ed0: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb92ed4: mov             x1, x0
    // 0xb92ed8: ldur            x0, [fp, #-0x78]
    // 0xb92edc: StoreField: r1->field_b = r0
    //     0xb92edc: stur            w0, [x1, #0xb]
    // 0xb92ee0: ldur            x0, [fp, #-0x70]
    // 0xb92ee4: StoreField: r1->field_7 = r0
    //     0xb92ee4: stur            w0, [x1, #7]
    // 0xb92ee8: r16 = <RoundedRectangleBorder>
    //     0xb92ee8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb92eec: ldr             x16, [x16, #0xf78]
    // 0xb92ef0: stp             x1, x16, [SP]
    // 0xb92ef4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb92ef4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb92ef8: r0 = all()
    //     0xb92ef8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb92efc: stur            x0, [fp, #-0x68]
    // 0xb92f00: r0 = ButtonStyle()
    //     0xb92f00: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb92f04: mov             x1, x0
    // 0xb92f08: ldur            x0, [fp, #-0x60]
    // 0xb92f0c: stur            x1, [fp, #-0x70]
    // 0xb92f10: StoreField: r1->field_b = r0
    //     0xb92f10: stur            w0, [x1, #0xb]
    // 0xb92f14: ldur            x0, [fp, #-0x58]
    // 0xb92f18: StoreField: r1->field_23 = r0
    //     0xb92f18: stur            w0, [x1, #0x23]
    // 0xb92f1c: ldur            x0, [fp, #-0x68]
    // 0xb92f20: StoreField: r1->field_43 = r0
    //     0xb92f20: stur            w0, [x1, #0x43]
    // 0xb92f24: r0 = TextButtonThemeData()
    //     0xb92f24: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb92f28: mov             x2, x0
    // 0xb92f2c: ldur            x0, [fp, #-0x70]
    // 0xb92f30: stur            x2, [fp, #-0x58]
    // 0xb92f34: StoreField: r2->field_7 = r0
    //     0xb92f34: stur            w0, [x2, #7]
    // 0xb92f38: r1 = Instance_Color
    //     0xb92f38: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb92f3c: d0 = 0.700000
    //     0xb92f3c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb92f40: ldr             d0, [x17, #0xf48]
    // 0xb92f44: r0 = withOpacity()
    //     0xb92f44: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb92f48: r16 = 14.000000
    //     0xb92f48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb92f4c: ldr             x16, [x16, #0x1d8]
    // 0xb92f50: stp             x0, x16, [SP]
    // 0xb92f54: ldur            x1, [fp, #-0x18]
    // 0xb92f58: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb92f58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb92f5c: ldr             x4, [x4, #0xaa0]
    // 0xb92f60: r0 = copyWith()
    //     0xb92f60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb92f64: stur            x0, [fp, #-0x60]
    // 0xb92f68: r0 = Text()
    //     0xb92f68: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb92f6c: mov             x3, x0
    // 0xb92f70: r0 = "No, Buy Separately"
    //     0xb92f70: add             x0, PP, #0x56, lsl #12  ; [pp+0x56048] "No, Buy Separately"
    //     0xb92f74: ldr             x0, [x0, #0x48]
    // 0xb92f78: stur            x3, [fp, #-0x68]
    // 0xb92f7c: StoreField: r3->field_b = r0
    //     0xb92f7c: stur            w0, [x3, #0xb]
    // 0xb92f80: ldur            x0, [fp, #-0x60]
    // 0xb92f84: StoreField: r3->field_13 = r0
    //     0xb92f84: stur            w0, [x3, #0x13]
    // 0xb92f88: ldur            x2, [fp, #-0x10]
    // 0xb92f8c: r1 = Function '<anonymous closure>':.
    //     0xb92f8c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56050] AnonymousClosure: (0xb937c4), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/single_exchange_product_bottom_sheet.dart] _SingleExchangeProductBottomSheetState::build (0xb91378)
    //     0xb92f90: ldr             x1, [x1, #0x50]
    // 0xb92f94: r0 = AllocateClosure()
    //     0xb92f94: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb92f98: stur            x0, [fp, #-0x60]
    // 0xb92f9c: r0 = TextButton()
    //     0xb92f9c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb92fa0: mov             x1, x0
    // 0xb92fa4: ldur            x0, [fp, #-0x60]
    // 0xb92fa8: stur            x1, [fp, #-0x70]
    // 0xb92fac: StoreField: r1->field_b = r0
    //     0xb92fac: stur            w0, [x1, #0xb]
    // 0xb92fb0: r0 = false
    //     0xb92fb0: add             x0, NULL, #0x30  ; false
    // 0xb92fb4: StoreField: r1->field_27 = r0
    //     0xb92fb4: stur            w0, [x1, #0x27]
    // 0xb92fb8: r2 = true
    //     0xb92fb8: add             x2, NULL, #0x20  ; true
    // 0xb92fbc: StoreField: r1->field_2f = r2
    //     0xb92fbc: stur            w2, [x1, #0x2f]
    // 0xb92fc0: ldur            x3, [fp, #-0x68]
    // 0xb92fc4: StoreField: r1->field_37 = r3
    //     0xb92fc4: stur            w3, [x1, #0x37]
    // 0xb92fc8: r0 = TextButtonTheme()
    //     0xb92fc8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb92fcc: mov             x2, x0
    // 0xb92fd0: ldur            x0, [fp, #-0x58]
    // 0xb92fd4: stur            x2, [fp, #-0x60]
    // 0xb92fd8: StoreField: r2->field_f = r0
    //     0xb92fd8: stur            w0, [x2, #0xf]
    // 0xb92fdc: ldur            x0, [fp, #-0x70]
    // 0xb92fe0: StoreField: r2->field_b = r0
    //     0xb92fe0: stur            w0, [x2, #0xb]
    // 0xb92fe4: r1 = <FlexParentData>
    //     0xb92fe4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb92fe8: ldr             x1, [x1, #0xe00]
    // 0xb92fec: r0 = Expanded()
    //     0xb92fec: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb92ff0: mov             x1, x0
    // 0xb92ff4: r0 = 1
    //     0xb92ff4: movz            x0, #0x1
    // 0xb92ff8: stur            x1, [fp, #-0x58]
    // 0xb92ffc: StoreField: r1->field_13 = r0
    //     0xb92ffc: stur            x0, [x1, #0x13]
    // 0xb93000: r2 = Instance_FlexFit
    //     0xb93000: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb93004: ldr             x2, [x2, #0xe08]
    // 0xb93008: StoreField: r1->field_1b = r2
    //     0xb93008: stur            w2, [x1, #0x1b]
    // 0xb9300c: ldur            x3, [fp, #-0x60]
    // 0xb93010: StoreField: r1->field_b = r3
    //     0xb93010: stur            w3, [x1, #0xb]
    // 0xb93014: r16 = <EdgeInsets>
    //     0xb93014: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb93018: ldr             x16, [x16, #0xda0]
    // 0xb9301c: r30 = Instance_EdgeInsets
    //     0xb9301c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb93020: ldr             lr, [lr, #0x1f0]
    // 0xb93024: stp             lr, x16, [SP]
    // 0xb93028: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb93028: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb9302c: r0 = all()
    //     0xb9302c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb93030: mov             x1, x0
    // 0xb93034: ldur            x0, [fp, #-8]
    // 0xb93038: stur            x1, [fp, #-0x60]
    // 0xb9303c: LoadField: r2 = r0->field_b
    //     0xb9303c: ldur            w2, [x0, #0xb]
    // 0xb93040: DecompressPointer r2
    //     0xb93040: add             x2, x2, HEAP, lsl #32
    // 0xb93044: cmp             w2, NULL
    // 0xb93048: b.eq            #0xb9370c
    // 0xb9304c: LoadField: r3 = r2->field_2f
    //     0xb9304c: ldur            w3, [x2, #0x2f]
    // 0xb93050: DecompressPointer r3
    //     0xb93050: add             x3, x3, HEAP, lsl #32
    // 0xb93054: LoadField: r2 = r3->field_3f
    //     0xb93054: ldur            w2, [x3, #0x3f]
    // 0xb93058: DecompressPointer r2
    //     0xb93058: add             x2, x2, HEAP, lsl #32
    // 0xb9305c: cmp             w2, NULL
    // 0xb93060: b.ne            #0xb9306c
    // 0xb93064: r3 = Null
    //     0xb93064: mov             x3, NULL
    // 0xb93068: b               #0xb93090
    // 0xb9306c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb9306c: ldur            w3, [x2, #0x17]
    // 0xb93070: DecompressPointer r3
    //     0xb93070: add             x3, x3, HEAP, lsl #32
    // 0xb93074: cmp             w3, NULL
    // 0xb93078: b.ne            #0xb93084
    // 0xb9307c: r3 = Null
    //     0xb9307c: mov             x3, NULL
    // 0xb93080: b               #0xb93090
    // 0xb93084: LoadField: r4 = r3->field_7
    //     0xb93084: ldur            w4, [x3, #7]
    // 0xb93088: DecompressPointer r4
    //     0xb93088: add             x4, x4, HEAP, lsl #32
    // 0xb9308c: mov             x3, x4
    // 0xb93090: cmp             w3, NULL
    // 0xb93094: b.ne            #0xb930a0
    // 0xb93098: r3 = 0
    //     0xb93098: movz            x3, #0
    // 0xb9309c: b               #0xb930b0
    // 0xb930a0: r4 = LoadInt32Instr(r3)
    //     0xb930a0: sbfx            x4, x3, #1, #0x1f
    //     0xb930a4: tbz             w3, #0, #0xb930ac
    //     0xb930a8: ldur            x4, [x3, #7]
    // 0xb930ac: mov             x3, x4
    // 0xb930b0: stur            x3, [fp, #-0x50]
    // 0xb930b4: cmp             w2, NULL
    // 0xb930b8: b.ne            #0xb930c4
    // 0xb930bc: r4 = Null
    //     0xb930bc: mov             x4, NULL
    // 0xb930c0: b               #0xb930e8
    // 0xb930c4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb930c4: ldur            w4, [x2, #0x17]
    // 0xb930c8: DecompressPointer r4
    //     0xb930c8: add             x4, x4, HEAP, lsl #32
    // 0xb930cc: cmp             w4, NULL
    // 0xb930d0: b.ne            #0xb930dc
    // 0xb930d4: r4 = Null
    //     0xb930d4: mov             x4, NULL
    // 0xb930d8: b               #0xb930e8
    // 0xb930dc: LoadField: r5 = r4->field_b
    //     0xb930dc: ldur            w5, [x4, #0xb]
    // 0xb930e0: DecompressPointer r5
    //     0xb930e0: add             x5, x5, HEAP, lsl #32
    // 0xb930e4: mov             x4, x5
    // 0xb930e8: cmp             w4, NULL
    // 0xb930ec: b.ne            #0xb930f8
    // 0xb930f0: r4 = 0
    //     0xb930f0: movz            x4, #0
    // 0xb930f4: b               #0xb93108
    // 0xb930f8: r5 = LoadInt32Instr(r4)
    //     0xb930f8: sbfx            x5, x4, #1, #0x1f
    //     0xb930fc: tbz             w4, #0, #0xb93104
    //     0xb93100: ldur            x5, [x4, #7]
    // 0xb93104: mov             x4, x5
    // 0xb93108: stur            x4, [fp, #-0x48]
    // 0xb9310c: cmp             w2, NULL
    // 0xb93110: b.ne            #0xb9311c
    // 0xb93114: r2 = Null
    //     0xb93114: mov             x2, NULL
    // 0xb93118: b               #0xb9313c
    // 0xb9311c: ArrayLoad: r5 = r2[0]  ; List_4
    //     0xb9311c: ldur            w5, [x2, #0x17]
    // 0xb93120: DecompressPointer r5
    //     0xb93120: add             x5, x5, HEAP, lsl #32
    // 0xb93124: cmp             w5, NULL
    // 0xb93128: b.ne            #0xb93134
    // 0xb9312c: r2 = Null
    //     0xb9312c: mov             x2, NULL
    // 0xb93130: b               #0xb9313c
    // 0xb93134: LoadField: r2 = r5->field_f
    //     0xb93134: ldur            w2, [x5, #0xf]
    // 0xb93138: DecompressPointer r2
    //     0xb93138: add             x2, x2, HEAP, lsl #32
    // 0xb9313c: cmp             w2, NULL
    // 0xb93140: b.ne            #0xb9314c
    // 0xb93144: r2 = 0
    //     0xb93144: movz            x2, #0
    // 0xb93148: b               #0xb9315c
    // 0xb9314c: r5 = LoadInt32Instr(r2)
    //     0xb9314c: sbfx            x5, x2, #1, #0x1f
    //     0xb93150: tbz             w2, #0, #0xb93158
    //     0xb93154: ldur            x5, [x2, #7]
    // 0xb93158: mov             x2, x5
    // 0xb9315c: stur            x2, [fp, #-0x40]
    // 0xb93160: r0 = Color()
    //     0xb93160: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb93164: mov             x1, x0
    // 0xb93168: r0 = Instance_ColorSpace
    //     0xb93168: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb9316c: StoreField: r1->field_27 = r0
    //     0xb9316c: stur            w0, [x1, #0x27]
    // 0xb93170: d0 = 1.000000
    //     0xb93170: fmov            d0, #1.00000000
    // 0xb93174: StoreField: r1->field_7 = d0
    //     0xb93174: stur            d0, [x1, #7]
    // 0xb93178: ldur            x2, [fp, #-0x50]
    // 0xb9317c: ubfx            x2, x2, #0, #0x20
    // 0xb93180: and             w3, w2, #0xff
    // 0xb93184: ubfx            x3, x3, #0, #0x20
    // 0xb93188: scvtf           d1, x3
    // 0xb9318c: d2 = 255.000000
    //     0xb9318c: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb93190: fdiv            d3, d1, d2
    // 0xb93194: StoreField: r1->field_f = d3
    //     0xb93194: stur            d3, [x1, #0xf]
    // 0xb93198: ldur            x2, [fp, #-0x48]
    // 0xb9319c: ubfx            x2, x2, #0, #0x20
    // 0xb931a0: and             w3, w2, #0xff
    // 0xb931a4: ubfx            x3, x3, #0, #0x20
    // 0xb931a8: scvtf           d1, x3
    // 0xb931ac: fdiv            d3, d1, d2
    // 0xb931b0: ArrayStore: r1[0] = d3  ; List_8
    //     0xb931b0: stur            d3, [x1, #0x17]
    // 0xb931b4: ldur            x2, [fp, #-0x40]
    // 0xb931b8: ubfx            x2, x2, #0, #0x20
    // 0xb931bc: and             w3, w2, #0xff
    // 0xb931c0: ubfx            x3, x3, #0, #0x20
    // 0xb931c4: scvtf           d1, x3
    // 0xb931c8: fdiv            d3, d1, d2
    // 0xb931cc: StoreField: r1->field_1f = d3
    //     0xb931cc: stur            d3, [x1, #0x1f]
    // 0xb931d0: r16 = <Color>
    //     0xb931d0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb931d4: ldr             x16, [x16, #0xf80]
    // 0xb931d8: stp             x1, x16, [SP]
    // 0xb931dc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb931dc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb931e0: r0 = all()
    //     0xb931e0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb931e4: mov             x1, x0
    // 0xb931e8: ldur            x0, [fp, #-8]
    // 0xb931ec: stur            x1, [fp, #-0x68]
    // 0xb931f0: LoadField: r2 = r0->field_b
    //     0xb931f0: ldur            w2, [x0, #0xb]
    // 0xb931f4: DecompressPointer r2
    //     0xb931f4: add             x2, x2, HEAP, lsl #32
    // 0xb931f8: cmp             w2, NULL
    // 0xb931fc: b.eq            #0xb93710
    // 0xb93200: LoadField: r0 = r2->field_2f
    //     0xb93200: ldur            w0, [x2, #0x2f]
    // 0xb93204: DecompressPointer r0
    //     0xb93204: add             x0, x0, HEAP, lsl #32
    // 0xb93208: LoadField: r2 = r0->field_3f
    //     0xb93208: ldur            w2, [x0, #0x3f]
    // 0xb9320c: DecompressPointer r2
    //     0xb9320c: add             x2, x2, HEAP, lsl #32
    // 0xb93210: cmp             w2, NULL
    // 0xb93214: b.ne            #0xb93220
    // 0xb93218: r0 = Null
    //     0xb93218: mov             x0, NULL
    // 0xb9321c: b               #0xb93244
    // 0xb93220: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb93220: ldur            w0, [x2, #0x17]
    // 0xb93224: DecompressPointer r0
    //     0xb93224: add             x0, x0, HEAP, lsl #32
    // 0xb93228: cmp             w0, NULL
    // 0xb9322c: b.ne            #0xb93238
    // 0xb93230: r0 = Null
    //     0xb93230: mov             x0, NULL
    // 0xb93234: b               #0xb93244
    // 0xb93238: LoadField: r3 = r0->field_7
    //     0xb93238: ldur            w3, [x0, #7]
    // 0xb9323c: DecompressPointer r3
    //     0xb9323c: add             x3, x3, HEAP, lsl #32
    // 0xb93240: mov             x0, x3
    // 0xb93244: cmp             w0, NULL
    // 0xb93248: b.ne            #0xb93254
    // 0xb9324c: r0 = 0
    //     0xb9324c: movz            x0, #0
    // 0xb93250: b               #0xb93264
    // 0xb93254: r3 = LoadInt32Instr(r0)
    //     0xb93254: sbfx            x3, x0, #1, #0x1f
    //     0xb93258: tbz             w0, #0, #0xb93260
    //     0xb9325c: ldur            x3, [x0, #7]
    // 0xb93260: mov             x0, x3
    // 0xb93264: stur            x0, [fp, #-0x50]
    // 0xb93268: cmp             w2, NULL
    // 0xb9326c: b.ne            #0xb93278
    // 0xb93270: r3 = Null
    //     0xb93270: mov             x3, NULL
    // 0xb93274: b               #0xb9329c
    // 0xb93278: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb93278: ldur            w3, [x2, #0x17]
    // 0xb9327c: DecompressPointer r3
    //     0xb9327c: add             x3, x3, HEAP, lsl #32
    // 0xb93280: cmp             w3, NULL
    // 0xb93284: b.ne            #0xb93290
    // 0xb93288: r3 = Null
    //     0xb93288: mov             x3, NULL
    // 0xb9328c: b               #0xb9329c
    // 0xb93290: LoadField: r4 = r3->field_b
    //     0xb93290: ldur            w4, [x3, #0xb]
    // 0xb93294: DecompressPointer r4
    //     0xb93294: add             x4, x4, HEAP, lsl #32
    // 0xb93298: mov             x3, x4
    // 0xb9329c: cmp             w3, NULL
    // 0xb932a0: b.ne            #0xb932ac
    // 0xb932a4: r3 = 0
    //     0xb932a4: movz            x3, #0
    // 0xb932a8: b               #0xb932bc
    // 0xb932ac: r4 = LoadInt32Instr(r3)
    //     0xb932ac: sbfx            x4, x3, #1, #0x1f
    //     0xb932b0: tbz             w3, #0, #0xb932b8
    //     0xb932b4: ldur            x4, [x3, #7]
    // 0xb932b8: mov             x3, x4
    // 0xb932bc: stur            x3, [fp, #-0x48]
    // 0xb932c0: cmp             w2, NULL
    // 0xb932c4: b.ne            #0xb932d0
    // 0xb932c8: r2 = Null
    //     0xb932c8: mov             x2, NULL
    // 0xb932cc: b               #0xb932f0
    // 0xb932d0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb932d0: ldur            w4, [x2, #0x17]
    // 0xb932d4: DecompressPointer r4
    //     0xb932d4: add             x4, x4, HEAP, lsl #32
    // 0xb932d8: cmp             w4, NULL
    // 0xb932dc: b.ne            #0xb932e8
    // 0xb932e0: r2 = Null
    //     0xb932e0: mov             x2, NULL
    // 0xb932e4: b               #0xb932f0
    // 0xb932e8: LoadField: r2 = r4->field_f
    //     0xb932e8: ldur            w2, [x4, #0xf]
    // 0xb932ec: DecompressPointer r2
    //     0xb932ec: add             x2, x2, HEAP, lsl #32
    // 0xb932f0: cmp             w2, NULL
    // 0xb932f4: b.ne            #0xb93300
    // 0xb932f8: r9 = 0
    //     0xb932f8: movz            x9, #0
    // 0xb932fc: b               #0xb93310
    // 0xb93300: r4 = LoadInt32Instr(r2)
    //     0xb93300: sbfx            x4, x2, #1, #0x1f
    //     0xb93304: tbz             w2, #0, #0xb9330c
    //     0xb93308: ldur            x4, [x2, #7]
    // 0xb9330c: mov             x9, x4
    // 0xb93310: ldur            x8, [fp, #-0x30]
    // 0xb93314: ldur            x7, [fp, #-0x38]
    // 0xb93318: ldur            x6, [fp, #-0x20]
    // 0xb9331c: ldur            x5, [fp, #-0x28]
    // 0xb93320: ldur            x4, [fp, #-0x58]
    // 0xb93324: ldur            x2, [fp, #-0x60]
    // 0xb93328: stur            x9, [fp, #-0x40]
    // 0xb9332c: r0 = Color()
    //     0xb9332c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb93330: mov             x1, x0
    // 0xb93334: r0 = Instance_ColorSpace
    //     0xb93334: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb93338: stur            x1, [fp, #-8]
    // 0xb9333c: StoreField: r1->field_27 = r0
    //     0xb9333c: stur            w0, [x1, #0x27]
    // 0xb93340: d0 = 1.000000
    //     0xb93340: fmov            d0, #1.00000000
    // 0xb93344: StoreField: r1->field_7 = d0
    //     0xb93344: stur            d0, [x1, #7]
    // 0xb93348: ldur            x0, [fp, #-0x50]
    // 0xb9334c: ubfx            x0, x0, #0, #0x20
    // 0xb93350: and             w2, w0, #0xff
    // 0xb93354: ubfx            x2, x2, #0, #0x20
    // 0xb93358: scvtf           d1, x2
    // 0xb9335c: d2 = 255.000000
    //     0xb9335c: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb93360: fdiv            d3, d1, d2
    // 0xb93364: StoreField: r1->field_f = d3
    //     0xb93364: stur            d3, [x1, #0xf]
    // 0xb93368: ldur            x0, [fp, #-0x48]
    // 0xb9336c: ubfx            x0, x0, #0, #0x20
    // 0xb93370: and             w2, w0, #0xff
    // 0xb93374: ubfx            x2, x2, #0, #0x20
    // 0xb93378: scvtf           d1, x2
    // 0xb9337c: fdiv            d3, d1, d2
    // 0xb93380: ArrayStore: r1[0] = d3  ; List_8
    //     0xb93380: stur            d3, [x1, #0x17]
    // 0xb93384: ldur            x0, [fp, #-0x40]
    // 0xb93388: ubfx            x0, x0, #0, #0x20
    // 0xb9338c: and             w2, w0, #0xff
    // 0xb93390: ubfx            x2, x2, #0, #0x20
    // 0xb93394: scvtf           d1, x2
    // 0xb93398: fdiv            d3, d1, d2
    // 0xb9339c: StoreField: r1->field_1f = d3
    //     0xb9339c: stur            d3, [x1, #0x1f]
    // 0xb933a0: r0 = BorderSide()
    //     0xb933a0: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb933a4: mov             x1, x0
    // 0xb933a8: ldur            x0, [fp, #-8]
    // 0xb933ac: stur            x1, [fp, #-0x70]
    // 0xb933b0: StoreField: r1->field_7 = r0
    //     0xb933b0: stur            w0, [x1, #7]
    // 0xb933b4: d0 = 1.000000
    //     0xb933b4: fmov            d0, #1.00000000
    // 0xb933b8: StoreField: r1->field_b = d0
    //     0xb933b8: stur            d0, [x1, #0xb]
    // 0xb933bc: r0 = Instance_BorderStyle
    //     0xb933bc: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb933c0: ldr             x0, [x0, #0xf68]
    // 0xb933c4: StoreField: r1->field_13 = r0
    //     0xb933c4: stur            w0, [x1, #0x13]
    // 0xb933c8: d0 = -1.000000
    //     0xb933c8: fmov            d0, #-1.00000000
    // 0xb933cc: ArrayStore: r1[0] = d0  ; List_8
    //     0xb933cc: stur            d0, [x1, #0x17]
    // 0xb933d0: r0 = Radius()
    //     0xb933d0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb933d4: d0 = 32.000000
    //     0xb933d4: add             x17, PP, #0x43, lsl #12  ; [pp+0x436c8] IMM: double(32) from 0x4040000000000000
    //     0xb933d8: ldr             d0, [x17, #0x6c8]
    // 0xb933dc: stur            x0, [fp, #-8]
    // 0xb933e0: StoreField: r0->field_7 = d0
    //     0xb933e0: stur            d0, [x0, #7]
    // 0xb933e4: StoreField: r0->field_f = d0
    //     0xb933e4: stur            d0, [x0, #0xf]
    // 0xb933e8: r0 = BorderRadius()
    //     0xb933e8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb933ec: mov             x1, x0
    // 0xb933f0: ldur            x0, [fp, #-8]
    // 0xb933f4: stur            x1, [fp, #-0x78]
    // 0xb933f8: StoreField: r1->field_7 = r0
    //     0xb933f8: stur            w0, [x1, #7]
    // 0xb933fc: StoreField: r1->field_b = r0
    //     0xb933fc: stur            w0, [x1, #0xb]
    // 0xb93400: StoreField: r1->field_f = r0
    //     0xb93400: stur            w0, [x1, #0xf]
    // 0xb93404: StoreField: r1->field_13 = r0
    //     0xb93404: stur            w0, [x1, #0x13]
    // 0xb93408: r0 = RoundedRectangleBorder()
    //     0xb93408: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb9340c: mov             x1, x0
    // 0xb93410: ldur            x0, [fp, #-0x78]
    // 0xb93414: StoreField: r1->field_b = r0
    //     0xb93414: stur            w0, [x1, #0xb]
    // 0xb93418: ldur            x0, [fp, #-0x70]
    // 0xb9341c: StoreField: r1->field_7 = r0
    //     0xb9341c: stur            w0, [x1, #7]
    // 0xb93420: r16 = <RoundedRectangleBorder>
    //     0xb93420: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb93424: ldr             x16, [x16, #0xf78]
    // 0xb93428: stp             x1, x16, [SP]
    // 0xb9342c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb9342c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb93430: r0 = all()
    //     0xb93430: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb93434: stur            x0, [fp, #-8]
    // 0xb93438: r0 = ButtonStyle()
    //     0xb93438: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb9343c: mov             x1, x0
    // 0xb93440: ldur            x0, [fp, #-0x68]
    // 0xb93444: stur            x1, [fp, #-0x70]
    // 0xb93448: StoreField: r1->field_b = r0
    //     0xb93448: stur            w0, [x1, #0xb]
    // 0xb9344c: ldur            x0, [fp, #-0x60]
    // 0xb93450: StoreField: r1->field_23 = r0
    //     0xb93450: stur            w0, [x1, #0x23]
    // 0xb93454: ldur            x0, [fp, #-8]
    // 0xb93458: StoreField: r1->field_43 = r0
    //     0xb93458: stur            w0, [x1, #0x43]
    // 0xb9345c: r0 = TextButtonThemeData()
    //     0xb9345c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb93460: mov             x2, x0
    // 0xb93464: ldur            x0, [fp, #-0x70]
    // 0xb93468: stur            x2, [fp, #-8]
    // 0xb9346c: StoreField: r2->field_7 = r0
    //     0xb9346c: stur            w0, [x2, #7]
    // 0xb93470: r16 = 14.000000
    //     0xb93470: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb93474: ldr             x16, [x16, #0x1d8]
    // 0xb93478: r30 = Instance_Color
    //     0xb93478: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb9347c: stp             lr, x16, [SP]
    // 0xb93480: ldur            x1, [fp, #-0x18]
    // 0xb93484: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb93484: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb93488: ldr             x4, [x4, #0xaa0]
    // 0xb9348c: r0 = copyWith()
    //     0xb9348c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb93490: stur            x0, [fp, #-0x18]
    // 0xb93494: r0 = Text()
    //     0xb93494: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb93498: mov             x3, x0
    // 0xb9349c: r0 = "Yes, Exchange"
    //     0xb9349c: add             x0, PP, #0x56, lsl #12  ; [pp+0x56058] "Yes, Exchange"
    //     0xb934a0: ldr             x0, [x0, #0x58]
    // 0xb934a4: stur            x3, [fp, #-0x60]
    // 0xb934a8: StoreField: r3->field_b = r0
    //     0xb934a8: stur            w0, [x3, #0xb]
    // 0xb934ac: ldur            x0, [fp, #-0x18]
    // 0xb934b0: StoreField: r3->field_13 = r0
    //     0xb934b0: stur            w0, [x3, #0x13]
    // 0xb934b4: ldur            x2, [fp, #-0x10]
    // 0xb934b8: r1 = Function '<anonymous closure>':.
    //     0xb934b8: add             x1, PP, #0x56, lsl #12  ; [pp+0x56060] AnonymousClosure: (0xb93714), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/single_exchange_product_bottom_sheet.dart] _SingleExchangeProductBottomSheetState::build (0xb91378)
    //     0xb934bc: ldr             x1, [x1, #0x60]
    // 0xb934c0: r0 = AllocateClosure()
    //     0xb934c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb934c4: stur            x0, [fp, #-0x10]
    // 0xb934c8: r0 = TextButton()
    //     0xb934c8: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb934cc: mov             x1, x0
    // 0xb934d0: ldur            x0, [fp, #-0x10]
    // 0xb934d4: stur            x1, [fp, #-0x18]
    // 0xb934d8: StoreField: r1->field_b = r0
    //     0xb934d8: stur            w0, [x1, #0xb]
    // 0xb934dc: r0 = false
    //     0xb934dc: add             x0, NULL, #0x30  ; false
    // 0xb934e0: StoreField: r1->field_27 = r0
    //     0xb934e0: stur            w0, [x1, #0x27]
    // 0xb934e4: r0 = true
    //     0xb934e4: add             x0, NULL, #0x20  ; true
    // 0xb934e8: StoreField: r1->field_2f = r0
    //     0xb934e8: stur            w0, [x1, #0x2f]
    // 0xb934ec: ldur            x0, [fp, #-0x60]
    // 0xb934f0: StoreField: r1->field_37 = r0
    //     0xb934f0: stur            w0, [x1, #0x37]
    // 0xb934f4: r0 = TextButtonTheme()
    //     0xb934f4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb934f8: mov             x2, x0
    // 0xb934fc: ldur            x0, [fp, #-8]
    // 0xb93500: stur            x2, [fp, #-0x10]
    // 0xb93504: StoreField: r2->field_f = r0
    //     0xb93504: stur            w0, [x2, #0xf]
    // 0xb93508: ldur            x0, [fp, #-0x18]
    // 0xb9350c: StoreField: r2->field_b = r0
    //     0xb9350c: stur            w0, [x2, #0xb]
    // 0xb93510: r1 = <FlexParentData>
    //     0xb93510: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb93514: ldr             x1, [x1, #0xe00]
    // 0xb93518: r0 = Expanded()
    //     0xb93518: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb9351c: mov             x3, x0
    // 0xb93520: r0 = 1
    //     0xb93520: movz            x0, #0x1
    // 0xb93524: stur            x3, [fp, #-8]
    // 0xb93528: StoreField: r3->field_13 = r0
    //     0xb93528: stur            x0, [x3, #0x13]
    // 0xb9352c: r0 = Instance_FlexFit
    //     0xb9352c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb93530: ldr             x0, [x0, #0xe08]
    // 0xb93534: StoreField: r3->field_1b = r0
    //     0xb93534: stur            w0, [x3, #0x1b]
    // 0xb93538: ldur            x0, [fp, #-0x10]
    // 0xb9353c: StoreField: r3->field_b = r0
    //     0xb9353c: stur            w0, [x3, #0xb]
    // 0xb93540: r1 = Null
    //     0xb93540: mov             x1, NULL
    // 0xb93544: r2 = 6
    //     0xb93544: movz            x2, #0x6
    // 0xb93548: r0 = AllocateArray()
    //     0xb93548: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9354c: mov             x2, x0
    // 0xb93550: ldur            x0, [fp, #-0x58]
    // 0xb93554: stur            x2, [fp, #-0x10]
    // 0xb93558: StoreField: r2->field_f = r0
    //     0xb93558: stur            w0, [x2, #0xf]
    // 0xb9355c: r16 = Instance_SizedBox
    //     0xb9355c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb93560: ldr             x16, [x16, #0x998]
    // 0xb93564: StoreField: r2->field_13 = r16
    //     0xb93564: stur            w16, [x2, #0x13]
    // 0xb93568: ldur            x0, [fp, #-8]
    // 0xb9356c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9356c: stur            w0, [x2, #0x17]
    // 0xb93570: r1 = <Widget>
    //     0xb93570: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb93574: r0 = AllocateGrowableArray()
    //     0xb93574: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb93578: mov             x1, x0
    // 0xb9357c: ldur            x0, [fp, #-0x10]
    // 0xb93580: stur            x1, [fp, #-8]
    // 0xb93584: StoreField: r1->field_f = r0
    //     0xb93584: stur            w0, [x1, #0xf]
    // 0xb93588: r0 = 6
    //     0xb93588: movz            x0, #0x6
    // 0xb9358c: StoreField: r1->field_b = r0
    //     0xb9358c: stur            w0, [x1, #0xb]
    // 0xb93590: r0 = Row()
    //     0xb93590: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb93594: mov             x1, x0
    // 0xb93598: r0 = Instance_Axis
    //     0xb93598: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb9359c: stur            x1, [fp, #-0x10]
    // 0xb935a0: StoreField: r1->field_f = r0
    //     0xb935a0: stur            w0, [x1, #0xf]
    // 0xb935a4: r0 = Instance_MainAxisAlignment
    //     0xb935a4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb935a8: ldr             x0, [x0, #0xa8]
    // 0xb935ac: StoreField: r1->field_13 = r0
    //     0xb935ac: stur            w0, [x1, #0x13]
    // 0xb935b0: r0 = Instance_MainAxisSize
    //     0xb935b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb935b4: ldr             x0, [x0, #0xa10]
    // 0xb935b8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb935b8: stur            w0, [x1, #0x17]
    // 0xb935bc: r0 = Instance_CrossAxisAlignment
    //     0xb935bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb935c0: ldr             x0, [x0, #0xa18]
    // 0xb935c4: StoreField: r1->field_1b = r0
    //     0xb935c4: stur            w0, [x1, #0x1b]
    // 0xb935c8: r0 = Instance_VerticalDirection
    //     0xb935c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb935cc: ldr             x0, [x0, #0xa20]
    // 0xb935d0: StoreField: r1->field_23 = r0
    //     0xb935d0: stur            w0, [x1, #0x23]
    // 0xb935d4: r2 = Instance_Clip
    //     0xb935d4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb935d8: ldr             x2, [x2, #0x38]
    // 0xb935dc: StoreField: r1->field_2b = r2
    //     0xb935dc: stur            w2, [x1, #0x2b]
    // 0xb935e0: StoreField: r1->field_2f = rZR
    //     0xb935e0: stur            xzr, [x1, #0x2f]
    // 0xb935e4: ldur            x3, [fp, #-8]
    // 0xb935e8: StoreField: r1->field_b = r3
    //     0xb935e8: stur            w3, [x1, #0xb]
    // 0xb935ec: r0 = Padding()
    //     0xb935ec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb935f0: mov             x3, x0
    // 0xb935f4: r0 = Instance_EdgeInsets
    //     0xb935f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb935f8: ldr             x0, [x0, #0x668]
    // 0xb935fc: stur            x3, [fp, #-8]
    // 0xb93600: StoreField: r3->field_f = r0
    //     0xb93600: stur            w0, [x3, #0xf]
    // 0xb93604: ldur            x0, [fp, #-0x10]
    // 0xb93608: StoreField: r3->field_b = r0
    //     0xb93608: stur            w0, [x3, #0xb]
    // 0xb9360c: r1 = Null
    //     0xb9360c: mov             x1, NULL
    // 0xb93610: r2 = 10
    //     0xb93610: movz            x2, #0xa
    // 0xb93614: r0 = AllocateArray()
    //     0xb93614: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb93618: mov             x2, x0
    // 0xb9361c: ldur            x0, [fp, #-0x30]
    // 0xb93620: stur            x2, [fp, #-0x10]
    // 0xb93624: StoreField: r2->field_f = r0
    //     0xb93624: stur            w0, [x2, #0xf]
    // 0xb93628: ldur            x0, [fp, #-0x38]
    // 0xb9362c: StoreField: r2->field_13 = r0
    //     0xb9362c: stur            w0, [x2, #0x13]
    // 0xb93630: ldur            x0, [fp, #-0x20]
    // 0xb93634: ArrayStore: r2[0] = r0  ; List_4
    //     0xb93634: stur            w0, [x2, #0x17]
    // 0xb93638: ldur            x0, [fp, #-0x28]
    // 0xb9363c: StoreField: r2->field_1b = r0
    //     0xb9363c: stur            w0, [x2, #0x1b]
    // 0xb93640: ldur            x0, [fp, #-8]
    // 0xb93644: StoreField: r2->field_1f = r0
    //     0xb93644: stur            w0, [x2, #0x1f]
    // 0xb93648: r1 = <Widget>
    //     0xb93648: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9364c: r0 = AllocateGrowableArray()
    //     0xb9364c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb93650: mov             x1, x0
    // 0xb93654: ldur            x0, [fp, #-0x10]
    // 0xb93658: stur            x1, [fp, #-8]
    // 0xb9365c: StoreField: r1->field_f = r0
    //     0xb9365c: stur            w0, [x1, #0xf]
    // 0xb93660: r0 = 10
    //     0xb93660: movz            x0, #0xa
    // 0xb93664: StoreField: r1->field_b = r0
    //     0xb93664: stur            w0, [x1, #0xb]
    // 0xb93668: r0 = Column()
    //     0xb93668: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb9366c: r1 = Instance_Axis
    //     0xb9366c: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb93670: StoreField: r0->field_f = r1
    //     0xb93670: stur            w1, [x0, #0xf]
    // 0xb93674: r1 = Instance_MainAxisAlignment
    //     0xb93674: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb93678: ldr             x1, [x1, #0xa08]
    // 0xb9367c: StoreField: r0->field_13 = r1
    //     0xb9367c: stur            w1, [x0, #0x13]
    // 0xb93680: r1 = Instance_MainAxisSize
    //     0xb93680: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb93684: ldr             x1, [x1, #0xdd0]
    // 0xb93688: ArrayStore: r0[0] = r1  ; List_4
    //     0xb93688: stur            w1, [x0, #0x17]
    // 0xb9368c: r1 = Instance_CrossAxisAlignment
    //     0xb9368c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb93690: ldr             x1, [x1, #0x890]
    // 0xb93694: StoreField: r0->field_1b = r1
    //     0xb93694: stur            w1, [x0, #0x1b]
    // 0xb93698: r1 = Instance_VerticalDirection
    //     0xb93698: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9369c: ldr             x1, [x1, #0xa20]
    // 0xb936a0: StoreField: r0->field_23 = r1
    //     0xb936a0: stur            w1, [x0, #0x23]
    // 0xb936a4: r1 = Instance_Clip
    //     0xb936a4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb936a8: ldr             x1, [x1, #0x38]
    // 0xb936ac: StoreField: r0->field_2b = r1
    //     0xb936ac: stur            w1, [x0, #0x2b]
    // 0xb936b0: StoreField: r0->field_2f = rZR
    //     0xb936b0: stur            xzr, [x0, #0x2f]
    // 0xb936b4: ldur            x1, [fp, #-8]
    // 0xb936b8: StoreField: r0->field_b = r1
    //     0xb936b8: stur            w1, [x0, #0xb]
    // 0xb936bc: LeaveFrame
    //     0xb936bc: mov             SP, fp
    //     0xb936c0: ldp             fp, lr, [SP], #0x10
    // 0xb936c4: ret
    //     0xb936c4: ret             
    // 0xb936c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb936c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb936cc: b               #0xb91394
    // 0xb936d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb936d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb936d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb936d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb936d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb936d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb936dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb936dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb936e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb936e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb936e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb936e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb936e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb936e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb936ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb936ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb936f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb936f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb936f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb936f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb936f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb936f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb936fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb936fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb93700: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb93700: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb93704: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb93704: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb93708: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb93708: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9370c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9370c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb93710: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb93710: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb93714, size: 0xb0
    // 0xb93714: EnterFrame
    //     0xb93714: stp             fp, lr, [SP, #-0x10]!
    //     0xb93718: mov             fp, SP
    // 0xb9371c: AllocStack(0x10)
    //     0xb9371c: sub             SP, SP, #0x10
    // 0xb93720: SetupParameters()
    //     0xb93720: ldr             x0, [fp, #0x10]
    //     0xb93724: ldur            w2, [x0, #0x17]
    //     0xb93728: add             x2, x2, HEAP, lsl #32
    //     0xb9372c: stur            x2, [fp, #-8]
    // 0xb93730: CheckStackOverflow
    //     0xb93730: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb93734: cmp             SP, x16
    //     0xb93738: b.ls            #0xb937b8
    // 0xb9373c: LoadField: r0 = r2->field_f
    //     0xb9373c: ldur            w0, [x2, #0xf]
    // 0xb93740: DecompressPointer r0
    //     0xb93740: add             x0, x0, HEAP, lsl #32
    // 0xb93744: LoadField: r1 = r0->field_13
    //     0xb93744: ldur            w1, [x0, #0x13]
    // 0xb93748: DecompressPointer r1
    //     0xb93748: add             x1, x1, HEAP, lsl #32
    // 0xb9374c: r0 = exchangeBuyNowProceedPostEvent()
    //     0xb9374c: bl              #0xa9ab50  ; [package:customer_app/app/presentation/controllers/product_detail/product_detail_controller.dart] ProductDetailController::exchangeBuyNowProceedPostEvent
    // 0xb93750: ldur            x0, [fp, #-8]
    // 0xb93754: LoadField: r1 = r0->field_f
    //     0xb93754: ldur            w1, [x0, #0xf]
    // 0xb93758: DecompressPointer r1
    //     0xb93758: add             x1, x1, HEAP, lsl #32
    // 0xb9375c: LoadField: r2 = r1->field_13
    //     0xb9375c: ldur            w2, [x1, #0x13]
    // 0xb93760: DecompressPointer r2
    //     0xb93760: add             x2, x2, HEAP, lsl #32
    // 0xb93764: mov             x1, x2
    // 0xb93768: r0 = ctaExchangeCheckoutInitiatedPostEvent()
    //     0xb93768: bl              #0xa9a804  ; [package:customer_app/app/presentation/controllers/product_detail/product_detail_controller.dart] ProductDetailController::ctaExchangeCheckoutInitiatedPostEvent
    // 0xb9376c: ldur            x0, [fp, #-8]
    // 0xb93770: LoadField: r1 = r0->field_f
    //     0xb93770: ldur            w1, [x0, #0xf]
    // 0xb93774: DecompressPointer r1
    //     0xb93774: add             x1, x1, HEAP, lsl #32
    // 0xb93778: LoadField: r0 = r1->field_b
    //     0xb93778: ldur            w0, [x1, #0xb]
    // 0xb9377c: DecompressPointer r0
    //     0xb9377c: add             x0, x0, HEAP, lsl #32
    // 0xb93780: cmp             w0, NULL
    // 0xb93784: b.eq            #0xb937c0
    // 0xb93788: LoadField: r1 = r0->field_23
    //     0xb93788: ldur            w1, [x0, #0x23]
    // 0xb9378c: DecompressPointer r1
    //     0xb9378c: add             x1, x1, HEAP, lsl #32
    // 0xb93790: str             x1, [SP]
    // 0xb93794: r4 = 0
    //     0xb93794: movz            x4, #0
    // 0xb93798: ldr             x0, [SP]
    // 0xb9379c: r5 = UnlinkedCall_0x613b5c
    //     0xb9379c: add             x16, PP, #0x56, lsl #12  ; [pp+0x56068] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb937a0: ldp             x5, lr, [x16, #0x68]
    // 0xb937a4: blr             lr
    // 0xb937a8: r0 = Null
    //     0xb937a8: mov             x0, NULL
    // 0xb937ac: LeaveFrame
    //     0xb937ac: mov             SP, fp
    //     0xb937b0: ldp             fp, lr, [SP], #0x10
    // 0xb937b4: ret
    //     0xb937b4: ret             
    // 0xb937b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb937b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb937bc: b               #0xb9373c
    // 0xb937c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb937c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb937c4, size: 0xb8
    // 0xb937c4: EnterFrame
    //     0xb937c4: stp             fp, lr, [SP, #-0x10]!
    //     0xb937c8: mov             fp, SP
    // 0xb937cc: AllocStack(0x10)
    //     0xb937cc: sub             SP, SP, #0x10
    // 0xb937d0: SetupParameters()
    //     0xb937d0: ldr             x0, [fp, #0x10]
    //     0xb937d4: ldur            w2, [x0, #0x17]
    //     0xb937d8: add             x2, x2, HEAP, lsl #32
    //     0xb937dc: stur            x2, [fp, #-8]
    // 0xb937e0: CheckStackOverflow
    //     0xb937e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb937e4: cmp             SP, x16
    //     0xb937e8: b.ls            #0xb93870
    // 0xb937ec: LoadField: r0 = r2->field_f
    //     0xb937ec: ldur            w0, [x2, #0xf]
    // 0xb937f0: DecompressPointer r0
    //     0xb937f0: add             x0, x0, HEAP, lsl #32
    // 0xb937f4: LoadField: r1 = r0->field_13
    //     0xb937f4: ldur            w1, [x0, #0x13]
    // 0xb937f8: DecompressPointer r1
    //     0xb937f8: add             x1, x1, HEAP, lsl #32
    // 0xb937fc: r0 = postBuyNowEvent()
    //     0xb937fc: bl              #0xa9af10  ; [package:customer_app/app/presentation/controllers/product_detail/product_detail_controller.dart] ProductDetailController::postBuyNowEvent
    // 0xb93800: ldur            x0, [fp, #-8]
    // 0xb93804: LoadField: r1 = r0->field_f
    //     0xb93804: ldur            w1, [x0, #0xf]
    // 0xb93808: DecompressPointer r1
    //     0xb93808: add             x1, x1, HEAP, lsl #32
    // 0xb9380c: LoadField: r2 = r1->field_13
    //     0xb9380c: ldur            w2, [x1, #0x13]
    // 0xb93810: DecompressPointer r2
    //     0xb93810: add             x2, x2, HEAP, lsl #32
    // 0xb93814: mov             x1, x2
    // 0xb93818: r2 = "buy_now"
    //     0xb93818: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe78] "buy_now"
    //     0xb9381c: ldr             x2, [x2, #0xe78]
    // 0xb93820: r0 = checkoutStartedPostEvent()
    //     0xb93820: bl              #0xa9ad70  ; [package:customer_app/app/presentation/controllers/product_detail/product_detail_controller.dart] ProductDetailController::checkoutStartedPostEvent
    // 0xb93824: ldur            x0, [fp, #-8]
    // 0xb93828: LoadField: r1 = r0->field_f
    //     0xb93828: ldur            w1, [x0, #0xf]
    // 0xb9382c: DecompressPointer r1
    //     0xb9382c: add             x1, x1, HEAP, lsl #32
    // 0xb93830: LoadField: r0 = r1->field_b
    //     0xb93830: ldur            w0, [x1, #0xb]
    // 0xb93834: DecompressPointer r0
    //     0xb93834: add             x0, x0, HEAP, lsl #32
    // 0xb93838: cmp             w0, NULL
    // 0xb9383c: b.eq            #0xb93878
    // 0xb93840: LoadField: r1 = r0->field_1f
    //     0xb93840: ldur            w1, [x0, #0x1f]
    // 0xb93844: DecompressPointer r1
    //     0xb93844: add             x1, x1, HEAP, lsl #32
    // 0xb93848: str             x1, [SP]
    // 0xb9384c: r4 = 0
    //     0xb9384c: movz            x4, #0
    // 0xb93850: ldr             x0, [SP]
    // 0xb93854: r5 = UnlinkedCall_0x613b5c
    //     0xb93854: add             x16, PP, #0x56, lsl #12  ; [pp+0x56078] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb93858: ldp             x5, lr, [x16, #0x78]
    // 0xb9385c: blr             lr
    // 0xb93860: r0 = Null
    //     0xb93860: mov             x0, NULL
    // 0xb93864: LeaveFrame
    //     0xb93864: mov             SP, fp
    //     0xb93868: ldp             fp, lr, [SP], #0x10
    // 0xb9386c: ret
    //     0xb9386c: ret             
    // 0xb93870: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb93870: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb93874: b               #0xb937ec
    // 0xb93878: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb93878: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4047, size: 0x34, field offset: 0xc
//   const constructor, 
class SingleExchangeProductBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7fb14, size: 0x74
    // 0xc7fb14: EnterFrame
    //     0xc7fb14: stp             fp, lr, [SP, #-0x10]!
    //     0xc7fb18: mov             fp, SP
    // 0xc7fb1c: AllocStack(0x10)
    //     0xc7fb1c: sub             SP, SP, #0x10
    // 0xc7fb20: CheckStackOverflow
    //     0xc7fb20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7fb24: cmp             SP, x16
    //     0xc7fb28: b.ls            #0xc7fb80
    // 0xc7fb2c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xc7fb2c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc7fb30: ldr             x0, [x0, #0x1c80]
    //     0xc7fb34: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc7fb38: cmp             w0, w16
    //     0xc7fb3c: b.ne            #0xc7fb48
    //     0xc7fb40: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xc7fb44: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc7fb48: r16 = <ProductDetailController>
    //     0xc7fb48: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ede0] TypeArguments: <ProductDetailController>
    //     0xc7fb4c: ldr             x16, [x16, #0xde0]
    // 0xc7fb50: str             x16, [SP]
    // 0xc7fb54: r4 = const [0x1, 0, 0, 0, null]
    //     0xc7fb54: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xc7fb58: r0 = Inst.find()
    //     0xc7fb58: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xc7fb5c: r1 = <SingleExchangeProductBottomSheet>
    //     0xc7fb5c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48860] TypeArguments: <SingleExchangeProductBottomSheet>
    //     0xc7fb60: ldr             x1, [x1, #0x860]
    // 0xc7fb64: stur            x0, [fp, #-8]
    // 0xc7fb68: r0 = _SingleExchangeProductBottomSheetState()
    //     0xc7fb68: bl              #0xc7fb88  ; Allocate_SingleExchangeProductBottomSheetStateStub -> _SingleExchangeProductBottomSheetState (size=0x18)
    // 0xc7fb6c: ldur            x1, [fp, #-8]
    // 0xc7fb70: StoreField: r0->field_13 = r1
    //     0xc7fb70: stur            w1, [x0, #0x13]
    // 0xc7fb74: LeaveFrame
    //     0xc7fb74: mov             SP, fp
    //     0xc7fb78: ldp             fp, lr, [SP], #0x10
    // 0xc7fb7c: ret
    //     0xc7fb7c: ret             
    // 0xc7fb80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7fb80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7fb84: b               #0xc7fb2c
  }
}
