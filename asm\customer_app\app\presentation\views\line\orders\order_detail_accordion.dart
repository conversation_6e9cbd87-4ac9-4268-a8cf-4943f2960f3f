// lib: , url: package:customer_app/app/presentation/views/line/orders/order_detail_accordion.dart

// class id: 1049536, size: 0x8
class :: {
}

// class id: 3236, size: 0x18, field offset: 0x14
class _OrderDetailAccordionState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x94a660, size: 0x30
    // 0x94a660: LoadField: r2 = r1->field_b
    //     0x94a660: ldur            w2, [x1, #0xb]
    // 0x94a664: DecompressPointer r2
    //     0x94a664: add             x2, x2, HEAP, lsl #32
    // 0x94a668: cmp             w2, NULL
    // 0x94a66c: b.eq            #0x94a684
    // 0x94a670: LoadField: r3 = r2->field_1f
    //     0x94a670: ldur            w3, [x2, #0x1f]
    // 0x94a674: DecompressPointer r3
    //     0x94a674: add             x3, x3, HEAP, lsl #32
    // 0x94a678: StoreField: r1->field_13 = r3
    //     0x94a678: stur            w3, [x1, #0x13]
    // 0x94a67c: r0 = Null
    //     0x94a67c: mov             x0, NULL
    // 0x94a680: ret
    //     0x94a680: ret             
    // 0x94a684: EnterFrame
    //     0x94a684: stp             fp, lr, [SP, #-0x10]!
    //     0x94a688: mov             fp, SP
    // 0x94a68c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94a68c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbf9ae8, size: 0x38c
    // 0xbf9ae8: EnterFrame
    //     0xbf9ae8: stp             fp, lr, [SP, #-0x10]!
    //     0xbf9aec: mov             fp, SP
    // 0xbf9af0: AllocStack(0x48)
    //     0xbf9af0: sub             SP, SP, #0x48
    // 0xbf9af4: SetupParameters(_OrderDetailAccordionState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbf9af4: mov             x0, x1
    //     0xbf9af8: stur            x1, [fp, #-8]
    //     0xbf9afc: mov             x1, x2
    //     0xbf9b00: stur            x2, [fp, #-0x10]
    // 0xbf9b04: CheckStackOverflow
    //     0xbf9b04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf9b08: cmp             SP, x16
    //     0xbf9b0c: b.ls            #0xbf9e64
    // 0xbf9b10: r1 = 1
    //     0xbf9b10: movz            x1, #0x1
    // 0xbf9b14: r0 = AllocateContext()
    //     0xbf9b14: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf9b18: mov             x2, x0
    // 0xbf9b1c: ldur            x0, [fp, #-8]
    // 0xbf9b20: stur            x2, [fp, #-0x28]
    // 0xbf9b24: StoreField: r2->field_f = r0
    //     0xbf9b24: stur            w0, [x2, #0xf]
    // 0xbf9b28: LoadField: r1 = r0->field_b
    //     0xbf9b28: ldur            w1, [x0, #0xb]
    // 0xbf9b2c: DecompressPointer r1
    //     0xbf9b2c: add             x1, x1, HEAP, lsl #32
    // 0xbf9b30: cmp             w1, NULL
    // 0xbf9b34: b.eq            #0xbf9e6c
    // 0xbf9b38: LoadField: r3 = r1->field_b
    //     0xbf9b38: ldur            w3, [x1, #0xb]
    // 0xbf9b3c: DecompressPointer r3
    //     0xbf9b3c: add             x3, x3, HEAP, lsl #32
    // 0xbf9b40: stur            x3, [fp, #-0x20]
    // 0xbf9b44: LoadField: r1 = r0->field_13
    //     0xbf9b44: ldur            w1, [x0, #0x13]
    // 0xbf9b48: DecompressPointer r1
    //     0xbf9b48: add             x1, x1, HEAP, lsl #32
    // 0xbf9b4c: tbnz            w1, #4, #0xbf9b5c
    // 0xbf9b50: r4 = Instance_IconData
    //     0xbf9b50: add             x4, PP, #0x53, lsl #12  ; [pp+0x53440] Obj!IconData@d55621
    //     0xbf9b54: ldr             x4, [x4, #0x440]
    // 0xbf9b58: b               #0xbf9b64
    // 0xbf9b5c: r4 = Instance_IconData
    //     0xbf9b5c: add             x4, PP, #0x53, lsl #12  ; [pp+0x53448] Obj!IconData@d553a1
    //     0xbf9b60: ldr             x4, [x4, #0x448]
    // 0xbf9b64: stur            x4, [fp, #-0x18]
    // 0xbf9b68: tbnz            w1, #4, #0xbf9b84
    // 0xbf9b6c: ldur            x1, [fp, #-0x10]
    // 0xbf9b70: r0 = of()
    //     0xbf9b70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf9b74: LoadField: r1 = r0->field_5b
    //     0xbf9b74: ldur            w1, [x0, #0x5b]
    // 0xbf9b78: DecompressPointer r1
    //     0xbf9b78: add             x1, x1, HEAP, lsl #32
    // 0xbf9b7c: mov             x3, x1
    // 0xbf9b80: b               #0xbf9b98
    // 0xbf9b84: ldur            x1, [fp, #-0x10]
    // 0xbf9b88: r0 = of()
    //     0xbf9b88: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf9b8c: LoadField: r1 = r0->field_5b
    //     0xbf9b8c: ldur            w1, [x0, #0x5b]
    // 0xbf9b90: DecompressPointer r1
    //     0xbf9b90: add             x1, x1, HEAP, lsl #32
    // 0xbf9b94: mov             x3, x1
    // 0xbf9b98: ldur            x0, [fp, #-8]
    // 0xbf9b9c: ldur            x1, [fp, #-0x20]
    // 0xbf9ba0: ldur            x2, [fp, #-0x18]
    // 0xbf9ba4: stur            x3, [fp, #-0x10]
    // 0xbf9ba8: r0 = Icon()
    //     0xbf9ba8: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbf9bac: mov             x1, x0
    // 0xbf9bb0: ldur            x0, [fp, #-0x18]
    // 0xbf9bb4: stur            x1, [fp, #-0x30]
    // 0xbf9bb8: StoreField: r1->field_b = r0
    //     0xbf9bb8: stur            w0, [x1, #0xb]
    // 0xbf9bbc: r0 = 32.000000
    //     0xbf9bbc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xbf9bc0: ldr             x0, [x0, #0x848]
    // 0xbf9bc4: StoreField: r1->field_f = r0
    //     0xbf9bc4: stur            w0, [x1, #0xf]
    // 0xbf9bc8: ldur            x0, [fp, #-0x10]
    // 0xbf9bcc: StoreField: r1->field_23 = r0
    //     0xbf9bcc: stur            w0, [x1, #0x23]
    // 0xbf9bd0: ldur            x0, [fp, #-8]
    // 0xbf9bd4: LoadField: r2 = r0->field_b
    //     0xbf9bd4: ldur            w2, [x0, #0xb]
    // 0xbf9bd8: DecompressPointer r2
    //     0xbf9bd8: add             x2, x2, HEAP, lsl #32
    // 0xbf9bdc: stur            x2, [fp, #-0x10]
    // 0xbf9be0: cmp             w2, NULL
    // 0xbf9be4: b.eq            #0xbf9e70
    // 0xbf9be8: r0 = RichTextIcon()
    //     0xbf9be8: bl              #0xbf9e74  ; AllocateRichTextIconStub -> RichTextIcon (size=0x14)
    // 0xbf9bec: mov             x1, x0
    // 0xbf9bf0: ldur            x0, [fp, #-0x30]
    // 0xbf9bf4: stur            x1, [fp, #-8]
    // 0xbf9bf8: StoreField: r1->field_b = r0
    //     0xbf9bf8: stur            w0, [x1, #0xb]
    // 0xbf9bfc: r0 = ""
    //     0xbf9bfc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf9c00: StoreField: r1->field_f = r0
    //     0xbf9c00: stur            w0, [x1, #0xf]
    // 0xbf9c04: r0 = ListTile()
    //     0xbf9c04: bl              #0x98bcd8  ; AllocateListTileStub -> ListTile (size=0xa0)
    // 0xbf9c08: mov             x3, x0
    // 0xbf9c0c: ldur            x0, [fp, #-0x20]
    // 0xbf9c10: stur            x3, [fp, #-0x18]
    // 0xbf9c14: StoreField: r3->field_f = r0
    //     0xbf9c14: stur            w0, [x3, #0xf]
    // 0xbf9c18: ldur            x0, [fp, #-8]
    // 0xbf9c1c: ArrayStore: r3[0] = r0  ; List_4
    //     0xbf9c1c: stur            w0, [x3, #0x17]
    // 0xbf9c20: r0 = true
    //     0xbf9c20: add             x0, NULL, #0x20  ; true
    // 0xbf9c24: StoreField: r3->field_4b = r0
    //     0xbf9c24: stur            w0, [x3, #0x4b]
    // 0xbf9c28: ldur            x2, [fp, #-0x28]
    // 0xbf9c2c: r1 = Function '<anonymous closure>':.
    //     0xbf9c2c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53450] AnonymousClosure: (0xbf9e80), in [package:customer_app/app/presentation/views/line/orders/order_detail_accordion.dart] _OrderDetailAccordionState::build (0xbf9ae8)
    //     0xbf9c30: ldr             x1, [x1, #0x450]
    // 0xbf9c34: r0 = AllocateClosure()
    //     0xbf9c34: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf9c38: mov             x1, x0
    // 0xbf9c3c: ldur            x0, [fp, #-0x18]
    // 0xbf9c40: StoreField: r0->field_4f = r1
    //     0xbf9c40: stur            w1, [x0, #0x4f]
    // 0xbf9c44: r1 = false
    //     0xbf9c44: add             x1, NULL, #0x30  ; false
    // 0xbf9c48: StoreField: r0->field_5f = r1
    //     0xbf9c48: stur            w1, [x0, #0x5f]
    // 0xbf9c4c: StoreField: r0->field_73 = r1
    //     0xbf9c4c: stur            w1, [x0, #0x73]
    // 0xbf9c50: r1 = true
    //     0xbf9c50: add             x1, NULL, #0x20  ; true
    // 0xbf9c54: StoreField: r0->field_97 = r1
    //     0xbf9c54: stur            w1, [x0, #0x97]
    // 0xbf9c58: r0 = SizedBox()
    //     0xbf9c58: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbf9c5c: mov             x3, x0
    // 0xbf9c60: r0 = inf
    //     0xbf9c60: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbf9c64: ldr             x0, [x0, #0x9f8]
    // 0xbf9c68: stur            x3, [fp, #-8]
    // 0xbf9c6c: StoreField: r3->field_f = r0
    //     0xbf9c6c: stur            w0, [x3, #0xf]
    // 0xbf9c70: ldur            x0, [fp, #-0x18]
    // 0xbf9c74: StoreField: r3->field_b = r0
    //     0xbf9c74: stur            w0, [x3, #0xb]
    // 0xbf9c78: r1 = Null
    //     0xbf9c78: mov             x1, NULL
    // 0xbf9c7c: r2 = 2
    //     0xbf9c7c: movz            x2, #0x2
    // 0xbf9c80: r0 = AllocateArray()
    //     0xbf9c80: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf9c84: mov             x2, x0
    // 0xbf9c88: ldur            x0, [fp, #-8]
    // 0xbf9c8c: stur            x2, [fp, #-0x18]
    // 0xbf9c90: StoreField: r2->field_f = r0
    //     0xbf9c90: stur            w0, [x2, #0xf]
    // 0xbf9c94: r1 = <Widget>
    //     0xbf9c94: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf9c98: r0 = AllocateGrowableArray()
    //     0xbf9c98: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf9c9c: mov             x1, x0
    // 0xbf9ca0: ldur            x0, [fp, #-0x18]
    // 0xbf9ca4: stur            x1, [fp, #-0x20]
    // 0xbf9ca8: StoreField: r1->field_f = r0
    //     0xbf9ca8: stur            w0, [x1, #0xf]
    // 0xbf9cac: r0 = 2
    //     0xbf9cac: movz            x0, #0x2
    // 0xbf9cb0: StoreField: r1->field_b = r0
    //     0xbf9cb0: stur            w0, [x1, #0xb]
    // 0xbf9cb4: ldur            x0, [fp, #-0x10]
    // 0xbf9cb8: LoadField: r2 = r0->field_1f
    //     0xbf9cb8: ldur            w2, [x0, #0x1f]
    // 0xbf9cbc: DecompressPointer r2
    //     0xbf9cbc: add             x2, x2, HEAP, lsl #32
    // 0xbf9cc0: tbnz            w2, #4, #0xbf9d74
    // 0xbf9cc4: LoadField: r2 = r0->field_f
    //     0xbf9cc4: ldur            w2, [x0, #0xf]
    // 0xbf9cc8: DecompressPointer r2
    //     0xbf9cc8: add             x2, x2, HEAP, lsl #32
    // 0xbf9ccc: stur            x2, [fp, #-8]
    // 0xbf9cd0: r0 = Container()
    //     0xbf9cd0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf9cd4: stur            x0, [fp, #-0x10]
    // 0xbf9cd8: r16 = Instance_EdgeInsets
    //     0xbf9cd8: add             x16, PP, #0x34, lsl #12  ; [pp+0x34670] Obj!EdgeInsets@d572c1
    //     0xbf9cdc: ldr             x16, [x16, #0x670]
    // 0xbf9ce0: ldur            lr, [fp, #-8]
    // 0xbf9ce4: stp             lr, x16, [SP]
    // 0xbf9ce8: mov             x1, x0
    // 0xbf9cec: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xbf9cec: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xbf9cf0: ldr             x4, [x4, #0x30]
    // 0xbf9cf4: r0 = Container()
    //     0xbf9cf4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf9cf8: ldur            x0, [fp, #-0x20]
    // 0xbf9cfc: LoadField: r1 = r0->field_b
    //     0xbf9cfc: ldur            w1, [x0, #0xb]
    // 0xbf9d00: LoadField: r2 = r0->field_f
    //     0xbf9d00: ldur            w2, [x0, #0xf]
    // 0xbf9d04: DecompressPointer r2
    //     0xbf9d04: add             x2, x2, HEAP, lsl #32
    // 0xbf9d08: LoadField: r3 = r2->field_b
    //     0xbf9d08: ldur            w3, [x2, #0xb]
    // 0xbf9d0c: r2 = LoadInt32Instr(r1)
    //     0xbf9d0c: sbfx            x2, x1, #1, #0x1f
    // 0xbf9d10: stur            x2, [fp, #-0x38]
    // 0xbf9d14: r1 = LoadInt32Instr(r3)
    //     0xbf9d14: sbfx            x1, x3, #1, #0x1f
    // 0xbf9d18: cmp             x2, x1
    // 0xbf9d1c: b.ne            #0xbf9d28
    // 0xbf9d20: mov             x1, x0
    // 0xbf9d24: r0 = _growToNextCapacity()
    //     0xbf9d24: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbf9d28: ldur            x2, [fp, #-0x20]
    // 0xbf9d2c: ldur            x3, [fp, #-0x38]
    // 0xbf9d30: add             x0, x3, #1
    // 0xbf9d34: lsl             x1, x0, #1
    // 0xbf9d38: StoreField: r2->field_b = r1
    //     0xbf9d38: stur            w1, [x2, #0xb]
    // 0xbf9d3c: LoadField: r1 = r2->field_f
    //     0xbf9d3c: ldur            w1, [x2, #0xf]
    // 0xbf9d40: DecompressPointer r1
    //     0xbf9d40: add             x1, x1, HEAP, lsl #32
    // 0xbf9d44: ldur            x0, [fp, #-0x10]
    // 0xbf9d48: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbf9d48: add             x25, x1, x3, lsl #2
    //     0xbf9d4c: add             x25, x25, #0xf
    //     0xbf9d50: str             w0, [x25]
    //     0xbf9d54: tbz             w0, #0, #0xbf9d70
    //     0xbf9d58: ldurb           w16, [x1, #-1]
    //     0xbf9d5c: ldurb           w17, [x0, #-1]
    //     0xbf9d60: and             x16, x17, x16, lsr #2
    //     0xbf9d64: tst             x16, HEAP, lsr #32
    //     0xbf9d68: b.eq            #0xbf9d70
    //     0xbf9d6c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf9d70: b               #0xbf9e04
    // 0xbf9d74: mov             x2, x1
    // 0xbf9d78: r0 = Container()
    //     0xbf9d78: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf9d7c: mov             x1, x0
    // 0xbf9d80: stur            x0, [fp, #-8]
    // 0xbf9d84: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbf9d84: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbf9d88: r0 = Container()
    //     0xbf9d88: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf9d8c: ldur            x0, [fp, #-0x20]
    // 0xbf9d90: LoadField: r1 = r0->field_b
    //     0xbf9d90: ldur            w1, [x0, #0xb]
    // 0xbf9d94: LoadField: r2 = r0->field_f
    //     0xbf9d94: ldur            w2, [x0, #0xf]
    // 0xbf9d98: DecompressPointer r2
    //     0xbf9d98: add             x2, x2, HEAP, lsl #32
    // 0xbf9d9c: LoadField: r3 = r2->field_b
    //     0xbf9d9c: ldur            w3, [x2, #0xb]
    // 0xbf9da0: r2 = LoadInt32Instr(r1)
    //     0xbf9da0: sbfx            x2, x1, #1, #0x1f
    // 0xbf9da4: stur            x2, [fp, #-0x38]
    // 0xbf9da8: r1 = LoadInt32Instr(r3)
    //     0xbf9da8: sbfx            x1, x3, #1, #0x1f
    // 0xbf9dac: cmp             x2, x1
    // 0xbf9db0: b.ne            #0xbf9dbc
    // 0xbf9db4: mov             x1, x0
    // 0xbf9db8: r0 = _growToNextCapacity()
    //     0xbf9db8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbf9dbc: ldur            x2, [fp, #-0x20]
    // 0xbf9dc0: ldur            x3, [fp, #-0x38]
    // 0xbf9dc4: add             x0, x3, #1
    // 0xbf9dc8: lsl             x1, x0, #1
    // 0xbf9dcc: StoreField: r2->field_b = r1
    //     0xbf9dcc: stur            w1, [x2, #0xb]
    // 0xbf9dd0: LoadField: r1 = r2->field_f
    //     0xbf9dd0: ldur            w1, [x2, #0xf]
    // 0xbf9dd4: DecompressPointer r1
    //     0xbf9dd4: add             x1, x1, HEAP, lsl #32
    // 0xbf9dd8: ldur            x0, [fp, #-8]
    // 0xbf9ddc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbf9ddc: add             x25, x1, x3, lsl #2
    //     0xbf9de0: add             x25, x25, #0xf
    //     0xbf9de4: str             w0, [x25]
    //     0xbf9de8: tbz             w0, #0, #0xbf9e04
    //     0xbf9dec: ldurb           w16, [x1, #-1]
    //     0xbf9df0: ldurb           w17, [x0, #-1]
    //     0xbf9df4: and             x16, x17, x16, lsr #2
    //     0xbf9df8: tst             x16, HEAP, lsr #32
    //     0xbf9dfc: b.eq            #0xbf9e04
    //     0xbf9e00: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf9e04: r0 = Column()
    //     0xbf9e04: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf9e08: r1 = Instance_Axis
    //     0xbf9e08: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf9e0c: StoreField: r0->field_f = r1
    //     0xbf9e0c: stur            w1, [x0, #0xf]
    // 0xbf9e10: r1 = Instance_MainAxisAlignment
    //     0xbf9e10: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf9e14: ldr             x1, [x1, #0xa08]
    // 0xbf9e18: StoreField: r0->field_13 = r1
    //     0xbf9e18: stur            w1, [x0, #0x13]
    // 0xbf9e1c: r1 = Instance_MainAxisSize
    //     0xbf9e1c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf9e20: ldr             x1, [x1, #0xa10]
    // 0xbf9e24: ArrayStore: r0[0] = r1  ; List_4
    //     0xbf9e24: stur            w1, [x0, #0x17]
    // 0xbf9e28: r1 = Instance_CrossAxisAlignment
    //     0xbf9e28: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbf9e2c: ldr             x1, [x1, #0xa18]
    // 0xbf9e30: StoreField: r0->field_1b = r1
    //     0xbf9e30: stur            w1, [x0, #0x1b]
    // 0xbf9e34: r1 = Instance_VerticalDirection
    //     0xbf9e34: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf9e38: ldr             x1, [x1, #0xa20]
    // 0xbf9e3c: StoreField: r0->field_23 = r1
    //     0xbf9e3c: stur            w1, [x0, #0x23]
    // 0xbf9e40: r1 = Instance_Clip
    //     0xbf9e40: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf9e44: ldr             x1, [x1, #0x38]
    // 0xbf9e48: StoreField: r0->field_2b = r1
    //     0xbf9e48: stur            w1, [x0, #0x2b]
    // 0xbf9e4c: StoreField: r0->field_2f = rZR
    //     0xbf9e4c: stur            xzr, [x0, #0x2f]
    // 0xbf9e50: ldur            x1, [fp, #-0x20]
    // 0xbf9e54: StoreField: r0->field_b = r1
    //     0xbf9e54: stur            w1, [x0, #0xb]
    // 0xbf9e58: LeaveFrame
    //     0xbf9e58: mov             SP, fp
    //     0xbf9e5c: ldp             fp, lr, [SP], #0x10
    // 0xbf9e60: ret
    //     0xbf9e60: ret             
    // 0xbf9e64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf9e64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf9e68: b               #0xbf9b10
    // 0xbf9e6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf9e6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf9e70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf9e70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbf9e80, size: 0x60
    // 0xbf9e80: EnterFrame
    //     0xbf9e80: stp             fp, lr, [SP, #-0x10]!
    //     0xbf9e84: mov             fp, SP
    // 0xbf9e88: AllocStack(0x8)
    //     0xbf9e88: sub             SP, SP, #8
    // 0xbf9e8c: SetupParameters()
    //     0xbf9e8c: ldr             x0, [fp, #0x10]
    //     0xbf9e90: ldur            w2, [x0, #0x17]
    //     0xbf9e94: add             x2, x2, HEAP, lsl #32
    // 0xbf9e98: CheckStackOverflow
    //     0xbf9e98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf9e9c: cmp             SP, x16
    //     0xbf9ea0: b.ls            #0xbf9ed8
    // 0xbf9ea4: LoadField: r0 = r2->field_f
    //     0xbf9ea4: ldur            w0, [x2, #0xf]
    // 0xbf9ea8: DecompressPointer r0
    //     0xbf9ea8: add             x0, x0, HEAP, lsl #32
    // 0xbf9eac: stur            x0, [fp, #-8]
    // 0xbf9eb0: r1 = Function '<anonymous closure>':.
    //     0xbf9eb0: add             x1, PP, #0x53, lsl #12  ; [pp+0x53458] AnonymousClosure: (0xbf9ee0), in [package:customer_app/app/presentation/views/line/orders/order_detail_accordion.dart] _OrderDetailAccordionState::build (0xbf9ae8)
    //     0xbf9eb4: ldr             x1, [x1, #0x458]
    // 0xbf9eb8: r0 = AllocateClosure()
    //     0xbf9eb8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf9ebc: ldur            x1, [fp, #-8]
    // 0xbf9ec0: mov             x2, x0
    // 0xbf9ec4: r0 = setState()
    //     0xbf9ec4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbf9ec8: r0 = Null
    //     0xbf9ec8: mov             x0, NULL
    // 0xbf9ecc: LeaveFrame
    //     0xbf9ecc: mov             SP, fp
    //     0xbf9ed0: ldp             fp, lr, [SP], #0x10
    // 0xbf9ed4: ret
    //     0xbf9ed4: ret             
    // 0xbf9ed8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf9ed8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf9edc: b               #0xbf9ea4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbf9ee0, size: 0x94
    // 0xbf9ee0: EnterFrame
    //     0xbf9ee0: stp             fp, lr, [SP, #-0x10]!
    //     0xbf9ee4: mov             fp, SP
    // 0xbf9ee8: AllocStack(0x10)
    //     0xbf9ee8: sub             SP, SP, #0x10
    // 0xbf9eec: SetupParameters()
    //     0xbf9eec: ldr             x0, [fp, #0x10]
    //     0xbf9ef0: ldur            w1, [x0, #0x17]
    //     0xbf9ef4: add             x1, x1, HEAP, lsl #32
    // 0xbf9ef8: CheckStackOverflow
    //     0xbf9ef8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf9efc: cmp             SP, x16
    //     0xbf9f00: b.ls            #0xbf9f68
    // 0xbf9f04: LoadField: r0 = r1->field_f
    //     0xbf9f04: ldur            w0, [x1, #0xf]
    // 0xbf9f08: DecompressPointer r0
    //     0xbf9f08: add             x0, x0, HEAP, lsl #32
    // 0xbf9f0c: LoadField: r1 = r0->field_13
    //     0xbf9f0c: ldur            w1, [x0, #0x13]
    // 0xbf9f10: DecompressPointer r1
    //     0xbf9f10: add             x1, x1, HEAP, lsl #32
    // 0xbf9f14: eor             x2, x1, #0x10
    // 0xbf9f18: StoreField: r0->field_13 = r2
    //     0xbf9f18: stur            w2, [x0, #0x13]
    // 0xbf9f1c: LoadField: r1 = r0->field_b
    //     0xbf9f1c: ldur            w1, [x0, #0xb]
    // 0xbf9f20: DecompressPointer r1
    //     0xbf9f20: add             x1, x1, HEAP, lsl #32
    // 0xbf9f24: cmp             w1, NULL
    // 0xbf9f28: b.eq            #0xbf9f70
    // 0xbf9f2c: LoadField: r0 = r1->field_1f
    //     0xbf9f2c: ldur            w0, [x1, #0x1f]
    // 0xbf9f30: DecompressPointer r0
    //     0xbf9f30: add             x0, x0, HEAP, lsl #32
    // 0xbf9f34: LoadField: r2 = r1->field_33
    //     0xbf9f34: ldur            w2, [x1, #0x33]
    // 0xbf9f38: DecompressPointer r2
    //     0xbf9f38: add             x2, x2, HEAP, lsl #32
    // 0xbf9f3c: stp             x0, x2, [SP]
    // 0xbf9f40: r4 = 0
    //     0xbf9f40: movz            x4, #0
    // 0xbf9f44: ldr             x0, [SP, #8]
    // 0xbf9f48: r16 = UnlinkedCall_0x613b5c
    //     0xbf9f48: add             x16, PP, #0x53, lsl #12  ; [pp+0x53460] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbf9f4c: add             x16, x16, #0x460
    // 0xbf9f50: ldp             x5, lr, [x16]
    // 0xbf9f54: blr             lr
    // 0xbf9f58: r0 = Null
    //     0xbf9f58: mov             x0, NULL
    // 0xbf9f5c: LeaveFrame
    //     0xbf9f5c: mov             SP, fp
    //     0xbf9f60: ldp             fp, lr, [SP], #0x10
    // 0xbf9f64: ret
    //     0xbf9f64: ret             
    // 0xbf9f68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf9f68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf9f6c: b               #0xbf9f04
    // 0xbf9f70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf9f70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3983, size: 0x38, field offset: 0xc
//   const constructor, 
class OrderDetailAccordion extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80d78, size: 0x2c
    // 0xc80d78: EnterFrame
    //     0xc80d78: stp             fp, lr, [SP, #-0x10]!
    //     0xc80d7c: mov             fp, SP
    // 0xc80d80: mov             x0, x1
    // 0xc80d84: r1 = <OrderDetailAccordion>
    //     0xc80d84: add             x1, PP, #0x48, lsl #12  ; [pp+0x483f0] TypeArguments: <OrderDetailAccordion>
    //     0xc80d88: ldr             x1, [x1, #0x3f0]
    // 0xc80d8c: r0 = _OrderDetailAccordionState()
    //     0xc80d8c: bl              #0xc80da4  ; Allocate_OrderDetailAccordionStateStub -> _OrderDetailAccordionState (size=0x18)
    // 0xc80d90: r1 = false
    //     0xc80d90: add             x1, NULL, #0x30  ; false
    // 0xc80d94: StoreField: r0->field_13 = r1
    //     0xc80d94: stur            w1, [x0, #0x13]
    // 0xc80d98: LeaveFrame
    //     0xc80d98: mov             SP, fp
    //     0xc80d9c: ldp             fp, lr, [SP], #0x10
    // 0xc80da0: ret
    //     0xc80da0: ret             
  }
}
