// lib: , url: package:customer_app/app/presentation/custom_widgets/product_detail/product_website_navigator_widget.dart

// class id: 1049084, size: 0x8
class :: {
}

// class id: 3575, size: 0x14, field offset: 0x14
class _ProductWebsiteNavigatorWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0x9ac2a4, size: 0x760
    // 0x9ac2a4: EnterFrame
    //     0x9ac2a4: stp             fp, lr, [SP, #-0x10]!
    //     0x9ac2a8: mov             fp, SP
    // 0x9ac2ac: AllocStack(0x68)
    //     0x9ac2ac: sub             SP, SP, #0x68
    // 0x9ac2b0: SetupParameters(_ProductWebsiteNavigatorWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x9ac2b0: mov             x0, x1
    //     0x9ac2b4: stur            x1, [fp, #-8]
    //     0x9ac2b8: mov             x1, x2
    //     0x9ac2bc: stur            x2, [fp, #-0x10]
    // 0x9ac2c0: CheckStackOverflow
    //     0x9ac2c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ac2c4: cmp             SP, x16
    //     0x9ac2c8: b.ls            #0x9ac9e4
    // 0x9ac2cc: r1 = 1
    //     0x9ac2cc: movz            x1, #0x1
    // 0x9ac2d0: r0 = AllocateContext()
    //     0x9ac2d0: bl              #0x16f6108  ; AllocateContextStub
    // 0x9ac2d4: mov             x3, x0
    // 0x9ac2d8: ldur            x0, [fp, #-8]
    // 0x9ac2dc: stur            x3, [fp, #-0x28]
    // 0x9ac2e0: StoreField: r3->field_f = r0
    //     0x9ac2e0: stur            w0, [x3, #0xf]
    // 0x9ac2e4: LoadField: r1 = r0->field_b
    //     0x9ac2e4: ldur            w1, [x0, #0xb]
    // 0x9ac2e8: DecompressPointer r1
    //     0x9ac2e8: add             x1, x1, HEAP, lsl #32
    // 0x9ac2ec: cmp             w1, NULL
    // 0x9ac2f0: b.eq            #0x9ac9ec
    // 0x9ac2f4: LoadField: r4 = r1->field_2f
    //     0x9ac2f4: ldur            w4, [x1, #0x2f]
    // 0x9ac2f8: DecompressPointer r4
    //     0x9ac2f8: add             x4, x4, HEAP, lsl #32
    // 0x9ac2fc: stur            x4, [fp, #-0x20]
    // 0x9ac300: LoadField: r5 = r1->field_37
    //     0x9ac300: ldur            w5, [x1, #0x37]
    // 0x9ac304: DecompressPointer r5
    //     0x9ac304: add             x5, x5, HEAP, lsl #32
    // 0x9ac308: stur            x5, [fp, #-0x18]
    // 0x9ac30c: LoadField: r2 = r1->field_3b
    //     0x9ac30c: ldur            w2, [x1, #0x3b]
    // 0x9ac310: DecompressPointer r2
    //     0x9ac310: add             x2, x2, HEAP, lsl #32
    // 0x9ac314: r1 = Null
    //     0x9ac314: mov             x1, NULL
    // 0x9ac318: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9ac318: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9ac31c: r0 = Border.all()
    //     0x9ac31c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x9ac320: stur            x0, [fp, #-0x30]
    // 0x9ac324: r0 = BoxDecoration()
    //     0x9ac324: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x9ac328: mov             x2, x0
    // 0x9ac32c: ldur            x0, [fp, #-0x20]
    // 0x9ac330: stur            x2, [fp, #-0x38]
    // 0x9ac334: StoreField: r2->field_7 = r0
    //     0x9ac334: stur            w0, [x2, #7]
    // 0x9ac338: ldur            x0, [fp, #-0x30]
    // 0x9ac33c: StoreField: r2->field_f = r0
    //     0x9ac33c: stur            w0, [x2, #0xf]
    // 0x9ac340: ldur            x0, [fp, #-0x18]
    // 0x9ac344: StoreField: r2->field_13 = r0
    //     0x9ac344: stur            w0, [x2, #0x13]
    // 0x9ac348: r0 = Instance_BoxShape
    //     0x9ac348: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x9ac34c: ldr             x0, [x0, #0x80]
    // 0x9ac350: StoreField: r2->field_23 = r0
    //     0x9ac350: stur            w0, [x2, #0x23]
    // 0x9ac354: ldur            x3, [fp, #-8]
    // 0x9ac358: LoadField: r1 = r3->field_b
    //     0x9ac358: ldur            w1, [x3, #0xb]
    // 0x9ac35c: DecompressPointer r1
    //     0x9ac35c: add             x1, x1, HEAP, lsl #32
    // 0x9ac360: cmp             w1, NULL
    // 0x9ac364: b.eq            #0x9ac9f0
    // 0x9ac368: LoadField: r4 = r1->field_f
    //     0x9ac368: ldur            w4, [x1, #0xf]
    // 0x9ac36c: DecompressPointer r4
    //     0x9ac36c: add             x4, x4, HEAP, lsl #32
    // 0x9ac370: cmp             w4, NULL
    // 0x9ac374: b.ne            #0x9ac380
    // 0x9ac378: r1 = Null
    //     0x9ac378: mov             x1, NULL
    // 0x9ac37c: b               #0x9ac388
    // 0x9ac380: LoadField: r1 = r4->field_7
    //     0x9ac380: ldur            w1, [x4, #7]
    // 0x9ac384: DecompressPointer r1
    //     0x9ac384: add             x1, x1, HEAP, lsl #32
    // 0x9ac388: cmp             w1, NULL
    // 0x9ac38c: b.ne            #0x9ac398
    // 0x9ac390: r4 = ""
    //     0x9ac390: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9ac394: b               #0x9ac39c
    // 0x9ac398: mov             x4, x1
    // 0x9ac39c: ldur            x1, [fp, #-0x10]
    // 0x9ac3a0: stur            x4, [fp, #-0x18]
    // 0x9ac3a4: r0 = of()
    //     0x9ac3a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ac3a8: LoadField: r1 = r0->field_87
    //     0x9ac3a8: ldur            w1, [x0, #0x87]
    // 0x9ac3ac: DecompressPointer r1
    //     0x9ac3ac: add             x1, x1, HEAP, lsl #32
    // 0x9ac3b0: LoadField: r0 = r1->field_27
    //     0x9ac3b0: ldur            w0, [x1, #0x27]
    // 0x9ac3b4: DecompressPointer r0
    //     0x9ac3b4: add             x0, x0, HEAP, lsl #32
    // 0x9ac3b8: stur            x0, [fp, #-0x20]
    // 0x9ac3bc: r1 = Instance_Color
    //     0x9ac3bc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9ac3c0: d0 = 0.700000
    //     0x9ac3c0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x9ac3c4: ldr             d0, [x17, #0xf48]
    // 0x9ac3c8: r0 = withOpacity()
    //     0x9ac3c8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9ac3cc: r16 = 21.000000
    //     0x9ac3cc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x9ac3d0: ldr             x16, [x16, #0x9b0]
    // 0x9ac3d4: stp             x0, x16, [SP]
    // 0x9ac3d8: ldur            x1, [fp, #-0x20]
    // 0x9ac3dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9ac3dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9ac3e0: ldr             x4, [x4, #0xaa0]
    // 0x9ac3e4: r0 = copyWith()
    //     0x9ac3e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9ac3e8: stur            x0, [fp, #-0x20]
    // 0x9ac3ec: r0 = Text()
    //     0x9ac3ec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9ac3f0: mov             x1, x0
    // 0x9ac3f4: ldur            x0, [fp, #-0x18]
    // 0x9ac3f8: stur            x1, [fp, #-0x30]
    // 0x9ac3fc: StoreField: r1->field_b = r0
    //     0x9ac3fc: stur            w0, [x1, #0xb]
    // 0x9ac400: ldur            x0, [fp, #-0x20]
    // 0x9ac404: StoreField: r1->field_13 = r0
    //     0x9ac404: stur            w0, [x1, #0x13]
    // 0x9ac408: r2 = 4
    //     0x9ac408: movz            x2, #0x4
    // 0x9ac40c: StoreField: r1->field_37 = r2
    //     0x9ac40c: stur            w2, [x1, #0x37]
    // 0x9ac410: r0 = SizedBox()
    //     0x9ac410: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x9ac414: mov             x1, x0
    // 0x9ac418: r0 = 220.000000
    //     0x9ac418: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5bb68] 220
    //     0x9ac41c: ldr             x0, [x0, #0xb68]
    // 0x9ac420: stur            x1, [fp, #-0x20]
    // 0x9ac424: StoreField: r1->field_f = r0
    //     0x9ac424: stur            w0, [x1, #0xf]
    // 0x9ac428: ldur            x0, [fp, #-0x30]
    // 0x9ac42c: StoreField: r1->field_b = r0
    //     0x9ac42c: stur            w0, [x1, #0xb]
    // 0x9ac430: ldur            x0, [fp, #-8]
    // 0x9ac434: LoadField: r2 = r0->field_b
    //     0x9ac434: ldur            w2, [x0, #0xb]
    // 0x9ac438: DecompressPointer r2
    //     0x9ac438: add             x2, x2, HEAP, lsl #32
    // 0x9ac43c: cmp             w2, NULL
    // 0x9ac440: b.eq            #0x9ac9f4
    // 0x9ac444: LoadField: r3 = r2->field_f
    //     0x9ac444: ldur            w3, [x2, #0xf]
    // 0x9ac448: DecompressPointer r3
    //     0x9ac448: add             x3, x3, HEAP, lsl #32
    // 0x9ac44c: cmp             w3, NULL
    // 0x9ac450: b.ne            #0x9ac45c
    // 0x9ac454: r2 = Null
    //     0x9ac454: mov             x2, NULL
    // 0x9ac458: b               #0x9ac464
    // 0x9ac45c: LoadField: r2 = r3->field_13
    //     0x9ac45c: ldur            w2, [x3, #0x13]
    // 0x9ac460: DecompressPointer r2
    //     0x9ac460: add             x2, x2, HEAP, lsl #32
    // 0x9ac464: cmp             w2, NULL
    // 0x9ac468: b.ne            #0x9ac470
    // 0x9ac46c: r2 = ""
    //     0x9ac46c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9ac470: stur            x2, [fp, #-0x18]
    // 0x9ac474: r0 = Image()
    //     0x9ac474: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0x9ac478: stur            x0, [fp, #-0x30]
    // 0x9ac47c: r16 = 45.000000
    //     0x9ac47c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0x9ac480: ldr             x16, [x16, #0xc88]
    // 0x9ac484: r30 = 71.000000
    //     0x9ac484: add             lr, PP, #0x5b, lsl #12  ; [pp+0x5bb70] 71
    //     0x9ac488: ldr             lr, [lr, #0xb70]
    // 0x9ac48c: stp             lr, x16, [SP]
    // 0x9ac490: mov             x1, x0
    // 0x9ac494: ldur            x2, [fp, #-0x18]
    // 0x9ac498: r4 = const [0, 0x4, 0x2, 0x2, height, 0x3, width, 0x2, null]
    //     0x9ac498: add             x4, PP, #0x53, lsl #12  ; [pp+0x53ed8] List(9) [0, 0x4, 0x2, 0x2, "height", 0x3, "width", 0x2, Null]
    //     0x9ac49c: ldr             x4, [x4, #0xed8]
    // 0x9ac4a0: r0 = Image.network()
    //     0x9ac4a0: bl              #0x802090  ; [package:flutter/src/widgets/image.dart] Image::Image.network
    // 0x9ac4a4: r1 = Null
    //     0x9ac4a4: mov             x1, NULL
    // 0x9ac4a8: r2 = 4
    //     0x9ac4a8: movz            x2, #0x4
    // 0x9ac4ac: r0 = AllocateArray()
    //     0x9ac4ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9ac4b0: mov             x2, x0
    // 0x9ac4b4: ldur            x0, [fp, #-0x20]
    // 0x9ac4b8: stur            x2, [fp, #-0x18]
    // 0x9ac4bc: StoreField: r2->field_f = r0
    //     0x9ac4bc: stur            w0, [x2, #0xf]
    // 0x9ac4c0: ldur            x0, [fp, #-0x30]
    // 0x9ac4c4: StoreField: r2->field_13 = r0
    //     0x9ac4c4: stur            w0, [x2, #0x13]
    // 0x9ac4c8: r1 = <Widget>
    //     0x9ac4c8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9ac4cc: r0 = AllocateGrowableArray()
    //     0x9ac4cc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9ac4d0: mov             x1, x0
    // 0x9ac4d4: ldur            x0, [fp, #-0x18]
    // 0x9ac4d8: stur            x1, [fp, #-0x20]
    // 0x9ac4dc: StoreField: r1->field_f = r0
    //     0x9ac4dc: stur            w0, [x1, #0xf]
    // 0x9ac4e0: r2 = 4
    //     0x9ac4e0: movz            x2, #0x4
    // 0x9ac4e4: StoreField: r1->field_b = r2
    //     0x9ac4e4: stur            w2, [x1, #0xb]
    // 0x9ac4e8: r0 = Row()
    //     0x9ac4e8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9ac4ec: mov             x1, x0
    // 0x9ac4f0: r0 = Instance_Axis
    //     0x9ac4f0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9ac4f4: stur            x1, [fp, #-0x18]
    // 0x9ac4f8: StoreField: r1->field_f = r0
    //     0x9ac4f8: stur            w0, [x1, #0xf]
    // 0x9ac4fc: r0 = Instance_MainAxisAlignment
    //     0x9ac4fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x9ac500: ldr             x0, [x0, #0xa8]
    // 0x9ac504: StoreField: r1->field_13 = r0
    //     0x9ac504: stur            w0, [x1, #0x13]
    // 0x9ac508: r0 = Instance_MainAxisSize
    //     0x9ac508: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9ac50c: ldr             x0, [x0, #0xa10]
    // 0x9ac510: ArrayStore: r1[0] = r0  ; List_4
    //     0x9ac510: stur            w0, [x1, #0x17]
    // 0x9ac514: r2 = Instance_CrossAxisAlignment
    //     0x9ac514: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9ac518: ldr             x2, [x2, #0xa18]
    // 0x9ac51c: StoreField: r1->field_1b = r2
    //     0x9ac51c: stur            w2, [x1, #0x1b]
    // 0x9ac520: r3 = Instance_VerticalDirection
    //     0x9ac520: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9ac524: ldr             x3, [x3, #0xa20]
    // 0x9ac528: StoreField: r1->field_23 = r3
    //     0x9ac528: stur            w3, [x1, #0x23]
    // 0x9ac52c: r4 = Instance_Clip
    //     0x9ac52c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9ac530: ldr             x4, [x4, #0x38]
    // 0x9ac534: StoreField: r1->field_2b = r4
    //     0x9ac534: stur            w4, [x1, #0x2b]
    // 0x9ac538: StoreField: r1->field_2f = rZR
    //     0x9ac538: stur            xzr, [x1, #0x2f]
    // 0x9ac53c: ldur            x5, [fp, #-0x20]
    // 0x9ac540: StoreField: r1->field_b = r5
    //     0x9ac540: stur            w5, [x1, #0xb]
    // 0x9ac544: r0 = Padding()
    //     0x9ac544: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9ac548: mov             x1, x0
    // 0x9ac54c: r0 = Instance_EdgeInsets
    //     0x9ac54c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0x9ac550: ldr             x0, [x0, #0xb0]
    // 0x9ac554: stur            x1, [fp, #-0x20]
    // 0x9ac558: StoreField: r1->field_f = r0
    //     0x9ac558: stur            w0, [x1, #0xf]
    // 0x9ac55c: ldur            x0, [fp, #-0x18]
    // 0x9ac560: StoreField: r1->field_b = r0
    //     0x9ac560: stur            w0, [x1, #0xb]
    // 0x9ac564: r16 = <EdgeInsets>
    //     0x9ac564: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x9ac568: ldr             x16, [x16, #0xda0]
    // 0x9ac56c: r30 = Instance_EdgeInsets
    //     0x9ac56c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x9ac570: ldr             lr, [lr, #0x1f0]
    // 0x9ac574: stp             lr, x16, [SP]
    // 0x9ac578: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9ac578: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9ac57c: r0 = all()
    //     0x9ac57c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x9ac580: mov             x1, x0
    // 0x9ac584: ldur            x0, [fp, #-8]
    // 0x9ac588: stur            x1, [fp, #-0x18]
    // 0x9ac58c: LoadField: r2 = r0->field_b
    //     0x9ac58c: ldur            w2, [x0, #0xb]
    // 0x9ac590: DecompressPointer r2
    //     0x9ac590: add             x2, x2, HEAP, lsl #32
    // 0x9ac594: cmp             w2, NULL
    // 0x9ac598: b.eq            #0x9ac9f8
    // 0x9ac59c: LoadField: r3 = r2->field_3f
    //     0x9ac59c: ldur            w3, [x2, #0x3f]
    // 0x9ac5a0: DecompressPointer r3
    //     0x9ac5a0: add             x3, x3, HEAP, lsl #32
    // 0x9ac5a4: r16 = <Color>
    //     0x9ac5a4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x9ac5a8: ldr             x16, [x16, #0xf80]
    // 0x9ac5ac: stp             x3, x16, [SP]
    // 0x9ac5b0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9ac5b0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9ac5b4: r0 = all()
    //     0x9ac5b4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x9ac5b8: mov             x1, x0
    // 0x9ac5bc: ldur            x0, [fp, #-8]
    // 0x9ac5c0: stur            x1, [fp, #-0x48]
    // 0x9ac5c4: LoadField: r2 = r0->field_b
    //     0x9ac5c4: ldur            w2, [x0, #0xb]
    // 0x9ac5c8: DecompressPointer r2
    //     0x9ac5c8: add             x2, x2, HEAP, lsl #32
    // 0x9ac5cc: cmp             w2, NULL
    // 0x9ac5d0: b.eq            #0x9ac9fc
    // 0x9ac5d4: LoadField: r3 = r2->field_43
    //     0x9ac5d4: ldur            w3, [x2, #0x43]
    // 0x9ac5d8: DecompressPointer r3
    //     0x9ac5d8: add             x3, x3, HEAP, lsl #32
    // 0x9ac5dc: stur            x3, [fp, #-0x40]
    // 0x9ac5e0: LoadField: r4 = r2->field_33
    //     0x9ac5e0: ldur            w4, [x2, #0x33]
    // 0x9ac5e4: DecompressPointer r4
    //     0x9ac5e4: add             x4, x4, HEAP, lsl #32
    // 0x9ac5e8: stur            x4, [fp, #-0x30]
    // 0x9ac5ec: r0 = BorderSide()
    //     0x9ac5ec: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x9ac5f0: mov             x1, x0
    // 0x9ac5f4: ldur            x0, [fp, #-0x30]
    // 0x9ac5f8: stur            x1, [fp, #-0x50]
    // 0x9ac5fc: StoreField: r1->field_7 = r0
    //     0x9ac5fc: stur            w0, [x1, #7]
    // 0x9ac600: d0 = 1.000000
    //     0x9ac600: fmov            d0, #1.00000000
    // 0x9ac604: StoreField: r1->field_b = d0
    //     0x9ac604: stur            d0, [x1, #0xb]
    // 0x9ac608: r0 = Instance_BorderStyle
    //     0x9ac608: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x9ac60c: ldr             x0, [x0, #0xf68]
    // 0x9ac610: StoreField: r1->field_13 = r0
    //     0x9ac610: stur            w0, [x1, #0x13]
    // 0x9ac614: d0 = -1.000000
    //     0x9ac614: fmov            d0, #-1.00000000
    // 0x9ac618: ArrayStore: r1[0] = d0  ; List_8
    //     0x9ac618: stur            d0, [x1, #0x17]
    // 0x9ac61c: r0 = RoundedRectangleBorder()
    //     0x9ac61c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x9ac620: mov             x1, x0
    // 0x9ac624: ldur            x0, [fp, #-0x40]
    // 0x9ac628: StoreField: r1->field_b = r0
    //     0x9ac628: stur            w0, [x1, #0xb]
    // 0x9ac62c: ldur            x0, [fp, #-0x50]
    // 0x9ac630: StoreField: r1->field_7 = r0
    //     0x9ac630: stur            w0, [x1, #7]
    // 0x9ac634: r16 = <RoundedRectangleBorder>
    //     0x9ac634: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x9ac638: ldr             x16, [x16, #0xf78]
    // 0x9ac63c: stp             x1, x16, [SP]
    // 0x9ac640: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9ac640: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9ac644: r0 = all()
    //     0x9ac644: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x9ac648: stur            x0, [fp, #-0x30]
    // 0x9ac64c: r0 = ButtonStyle()
    //     0x9ac64c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x9ac650: mov             x1, x0
    // 0x9ac654: ldur            x0, [fp, #-0x48]
    // 0x9ac658: stur            x1, [fp, #-0x40]
    // 0x9ac65c: StoreField: r1->field_b = r0
    //     0x9ac65c: stur            w0, [x1, #0xb]
    // 0x9ac660: ldur            x0, [fp, #-0x18]
    // 0x9ac664: StoreField: r1->field_23 = r0
    //     0x9ac664: stur            w0, [x1, #0x23]
    // 0x9ac668: ldur            x0, [fp, #-0x30]
    // 0x9ac66c: StoreField: r1->field_43 = r0
    //     0x9ac66c: stur            w0, [x1, #0x43]
    // 0x9ac670: r0 = TextButtonThemeData()
    //     0x9ac670: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x9ac674: mov             x2, x0
    // 0x9ac678: ldur            x0, [fp, #-0x40]
    // 0x9ac67c: stur            x2, [fp, #-0x18]
    // 0x9ac680: StoreField: r2->field_7 = r0
    //     0x9ac680: stur            w0, [x2, #7]
    // 0x9ac684: ldur            x1, [fp, #-0x10]
    // 0x9ac688: r0 = of()
    //     0x9ac688: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ac68c: LoadField: r1 = r0->field_5b
    //     0x9ac68c: ldur            w1, [x0, #0x5b]
    // 0x9ac690: DecompressPointer r1
    //     0x9ac690: add             x1, x1, HEAP, lsl #32
    // 0x9ac694: r0 = LoadClassIdInstr(r1)
    //     0x9ac694: ldur            x0, [x1, #-1]
    //     0x9ac698: ubfx            x0, x0, #0xc, #0x14
    // 0x9ac69c: d0 = 0.700000
    //     0x9ac69c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x9ac6a0: ldr             d0, [x17, #0xf48]
    // 0x9ac6a4: r0 = GDT[cid_x0 + -0xffa]()
    //     0x9ac6a4: sub             lr, x0, #0xffa
    //     0x9ac6a8: ldr             lr, [x21, lr, lsl #3]
    //     0x9ac6ac: blr             lr
    // 0x9ac6b0: stur            x0, [fp, #-0x30]
    // 0x9ac6b4: r0 = ColorFilter()
    //     0x9ac6b4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x9ac6b8: mov             x1, x0
    // 0x9ac6bc: ldur            x0, [fp, #-0x30]
    // 0x9ac6c0: stur            x1, [fp, #-0x40]
    // 0x9ac6c4: StoreField: r1->field_7 = r0
    //     0x9ac6c4: stur            w0, [x1, #7]
    // 0x9ac6c8: r0 = Instance_BlendMode
    //     0x9ac6c8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x9ac6cc: ldr             x0, [x0, #0xb30]
    // 0x9ac6d0: StoreField: r1->field_b = r0
    //     0x9ac6d0: stur            w0, [x1, #0xb]
    // 0x9ac6d4: r0 = 1
    //     0x9ac6d4: movz            x0, #0x1
    // 0x9ac6d8: StoreField: r1->field_13 = r0
    //     0x9ac6d8: stur            x0, [x1, #0x13]
    // 0x9ac6dc: r0 = SvgPicture()
    //     0x9ac6dc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x9ac6e0: stur            x0, [fp, #-0x30]
    // 0x9ac6e4: ldur            x16, [fp, #-0x40]
    // 0x9ac6e8: str             x16, [SP]
    // 0x9ac6ec: mov             x1, x0
    // 0x9ac6f0: r2 = "assets/images/search.svg"
    //     0x9ac6f0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x9ac6f4: ldr             x2, [x2, #0xa30]
    // 0x9ac6f8: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x9ac6f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x9ac6fc: ldr             x4, [x4, #0xa38]
    // 0x9ac700: r0 = SvgPicture.asset()
    //     0x9ac700: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x9ac704: r0 = Align()
    //     0x9ac704: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x9ac708: mov             x2, x0
    // 0x9ac70c: r0 = Instance_Alignment
    //     0x9ac70c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x9ac710: ldr             x0, [x0, #0xb10]
    // 0x9ac714: stur            x2, [fp, #-0x40]
    // 0x9ac718: StoreField: r2->field_f = r0
    //     0x9ac718: stur            w0, [x2, #0xf]
    // 0x9ac71c: r0 = 1.000000
    //     0x9ac71c: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x9ac720: StoreField: r2->field_13 = r0
    //     0x9ac720: stur            w0, [x2, #0x13]
    // 0x9ac724: ArrayStore: r2[0] = r0  ; List_4
    //     0x9ac724: stur            w0, [x2, #0x17]
    // 0x9ac728: ldur            x0, [fp, #-0x30]
    // 0x9ac72c: StoreField: r2->field_b = r0
    //     0x9ac72c: stur            w0, [x2, #0xb]
    // 0x9ac730: ldur            x0, [fp, #-8]
    // 0x9ac734: LoadField: r1 = r0->field_b
    //     0x9ac734: ldur            w1, [x0, #0xb]
    // 0x9ac738: DecompressPointer r1
    //     0x9ac738: add             x1, x1, HEAP, lsl #32
    // 0x9ac73c: cmp             w1, NULL
    // 0x9ac740: b.eq            #0x9aca00
    // 0x9ac744: LoadField: r0 = r1->field_f
    //     0x9ac744: ldur            w0, [x1, #0xf]
    // 0x9ac748: DecompressPointer r0
    //     0x9ac748: add             x0, x0, HEAP, lsl #32
    // 0x9ac74c: cmp             w0, NULL
    // 0x9ac750: b.ne            #0x9ac75c
    // 0x9ac754: r0 = Null
    //     0x9ac754: mov             x0, NULL
    // 0x9ac758: b               #0x9ac768
    // 0x9ac75c: LoadField: r1 = r0->field_f
    //     0x9ac75c: ldur            w1, [x0, #0xf]
    // 0x9ac760: DecompressPointer r1
    //     0x9ac760: add             x1, x1, HEAP, lsl #32
    // 0x9ac764: mov             x0, x1
    // 0x9ac768: cmp             w0, NULL
    // 0x9ac76c: b.ne            #0x9ac778
    // 0x9ac770: r4 = ""
    //     0x9ac770: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9ac774: b               #0x9ac77c
    // 0x9ac778: mov             x4, x0
    // 0x9ac77c: ldur            x3, [fp, #-0x20]
    // 0x9ac780: ldur            x0, [fp, #-0x18]
    // 0x9ac784: ldur            x1, [fp, #-0x10]
    // 0x9ac788: stur            x4, [fp, #-8]
    // 0x9ac78c: r0 = of()
    //     0x9ac78c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ac790: LoadField: r1 = r0->field_87
    //     0x9ac790: ldur            w1, [x0, #0x87]
    // 0x9ac794: DecompressPointer r1
    //     0x9ac794: add             x1, x1, HEAP, lsl #32
    // 0x9ac798: LoadField: r0 = r1->field_2b
    //     0x9ac798: ldur            w0, [x1, #0x2b]
    // 0x9ac79c: DecompressPointer r0
    //     0x9ac79c: add             x0, x0, HEAP, lsl #32
    // 0x9ac7a0: ldur            x1, [fp, #-0x10]
    // 0x9ac7a4: stur            x0, [fp, #-0x30]
    // 0x9ac7a8: r0 = of()
    //     0x9ac7a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ac7ac: LoadField: r1 = r0->field_5b
    //     0x9ac7ac: ldur            w1, [x0, #0x5b]
    // 0x9ac7b0: DecompressPointer r1
    //     0x9ac7b0: add             x1, x1, HEAP, lsl #32
    // 0x9ac7b4: r0 = LoadClassIdInstr(r1)
    //     0x9ac7b4: ldur            x0, [x1, #-1]
    //     0x9ac7b8: ubfx            x0, x0, #0xc, #0x14
    // 0x9ac7bc: d0 = 0.700000
    //     0x9ac7bc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x9ac7c0: ldr             d0, [x17, #0xf48]
    // 0x9ac7c4: r0 = GDT[cid_x0 + -0xffa]()
    //     0x9ac7c4: sub             lr, x0, #0xffa
    //     0x9ac7c8: ldr             lr, [x21, lr, lsl #3]
    //     0x9ac7cc: blr             lr
    // 0x9ac7d0: r16 = 16.000000
    //     0x9ac7d0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x9ac7d4: ldr             x16, [x16, #0x188]
    // 0x9ac7d8: stp             x0, x16, [SP]
    // 0x9ac7dc: ldur            x1, [fp, #-0x30]
    // 0x9ac7e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9ac7e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9ac7e4: ldr             x4, [x4, #0xaa0]
    // 0x9ac7e8: r0 = copyWith()
    //     0x9ac7e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9ac7ec: stur            x0, [fp, #-0x10]
    // 0x9ac7f0: r0 = Text()
    //     0x9ac7f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9ac7f4: mov             x3, x0
    // 0x9ac7f8: ldur            x0, [fp, #-8]
    // 0x9ac7fc: stur            x3, [fp, #-0x30]
    // 0x9ac800: StoreField: r3->field_b = r0
    //     0x9ac800: stur            w0, [x3, #0xb]
    // 0x9ac804: ldur            x0, [fp, #-0x10]
    // 0x9ac808: StoreField: r3->field_13 = r0
    //     0x9ac808: stur            w0, [x3, #0x13]
    // 0x9ac80c: ldur            x2, [fp, #-0x28]
    // 0x9ac810: r1 = Function '<anonymous closure>':.
    //     0x9ac810: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bb78] AnonymousClosure: (0x9acb10), in [package:customer_app/app/presentation/custom_widgets/product_detail/product_website_navigator_widget.dart] _ProductWebsiteNavigatorWidgetState::build (0x9ac2a4)
    //     0x9ac814: ldr             x1, [x1, #0xb78]
    // 0x9ac818: r0 = AllocateClosure()
    //     0x9ac818: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9ac81c: ldur            x2, [fp, #-0x40]
    // 0x9ac820: ldur            x3, [fp, #-0x30]
    // 0x9ac824: mov             x5, x0
    // 0x9ac828: r1 = Null
    //     0x9ac828: mov             x1, NULL
    // 0x9ac82c: r0 = TextButton.icon()
    //     0x9ac82c: bl              #0x98f21c  ; [package:flutter/src/material/text_button.dart] TextButton::TextButton.icon
    // 0x9ac830: stur            x0, [fp, #-8]
    // 0x9ac834: r0 = TextButtonTheme()
    //     0x9ac834: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x9ac838: mov             x1, x0
    // 0x9ac83c: ldur            x0, [fp, #-0x18]
    // 0x9ac840: stur            x1, [fp, #-0x10]
    // 0x9ac844: StoreField: r1->field_f = r0
    //     0x9ac844: stur            w0, [x1, #0xf]
    // 0x9ac848: ldur            x0, [fp, #-8]
    // 0x9ac84c: StoreField: r1->field_b = r0
    //     0x9ac84c: stur            w0, [x1, #0xb]
    // 0x9ac850: r0 = SizedBox()
    //     0x9ac850: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x9ac854: mov             x3, x0
    // 0x9ac858: r0 = inf
    //     0x9ac858: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x9ac85c: ldr             x0, [x0, #0x9f8]
    // 0x9ac860: stur            x3, [fp, #-8]
    // 0x9ac864: StoreField: r3->field_f = r0
    //     0x9ac864: stur            w0, [x3, #0xf]
    // 0x9ac868: ldur            x0, [fp, #-0x10]
    // 0x9ac86c: StoreField: r3->field_b = r0
    //     0x9ac86c: stur            w0, [x3, #0xb]
    // 0x9ac870: r1 = Null
    //     0x9ac870: mov             x1, NULL
    // 0x9ac874: r2 = 4
    //     0x9ac874: movz            x2, #0x4
    // 0x9ac878: r0 = AllocateArray()
    //     0x9ac878: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9ac87c: mov             x2, x0
    // 0x9ac880: ldur            x0, [fp, #-0x20]
    // 0x9ac884: stur            x2, [fp, #-0x10]
    // 0x9ac888: StoreField: r2->field_f = r0
    //     0x9ac888: stur            w0, [x2, #0xf]
    // 0x9ac88c: ldur            x0, [fp, #-8]
    // 0x9ac890: StoreField: r2->field_13 = r0
    //     0x9ac890: stur            w0, [x2, #0x13]
    // 0x9ac894: r1 = <Widget>
    //     0x9ac894: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9ac898: r0 = AllocateGrowableArray()
    //     0x9ac898: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9ac89c: mov             x1, x0
    // 0x9ac8a0: ldur            x0, [fp, #-0x10]
    // 0x9ac8a4: stur            x1, [fp, #-8]
    // 0x9ac8a8: StoreField: r1->field_f = r0
    //     0x9ac8a8: stur            w0, [x1, #0xf]
    // 0x9ac8ac: r0 = 4
    //     0x9ac8ac: movz            x0, #0x4
    // 0x9ac8b0: StoreField: r1->field_b = r0
    //     0x9ac8b0: stur            w0, [x1, #0xb]
    // 0x9ac8b4: r0 = Column()
    //     0x9ac8b4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9ac8b8: mov             x1, x0
    // 0x9ac8bc: r0 = Instance_Axis
    //     0x9ac8bc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9ac8c0: stur            x1, [fp, #-0x10]
    // 0x9ac8c4: StoreField: r1->field_f = r0
    //     0x9ac8c4: stur            w0, [x1, #0xf]
    // 0x9ac8c8: r0 = Instance_MainAxisAlignment
    //     0x9ac8c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9ac8cc: ldr             x0, [x0, #0xa08]
    // 0x9ac8d0: StoreField: r1->field_13 = r0
    //     0x9ac8d0: stur            w0, [x1, #0x13]
    // 0x9ac8d4: r0 = Instance_MainAxisSize
    //     0x9ac8d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9ac8d8: ldr             x0, [x0, #0xa10]
    // 0x9ac8dc: ArrayStore: r1[0] = r0  ; List_4
    //     0x9ac8dc: stur            w0, [x1, #0x17]
    // 0x9ac8e0: r0 = Instance_CrossAxisAlignment
    //     0x9ac8e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9ac8e4: ldr             x0, [x0, #0xa18]
    // 0x9ac8e8: StoreField: r1->field_1b = r0
    //     0x9ac8e8: stur            w0, [x1, #0x1b]
    // 0x9ac8ec: r0 = Instance_VerticalDirection
    //     0x9ac8ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9ac8f0: ldr             x0, [x0, #0xa20]
    // 0x9ac8f4: StoreField: r1->field_23 = r0
    //     0x9ac8f4: stur            w0, [x1, #0x23]
    // 0x9ac8f8: r0 = Instance_Clip
    //     0x9ac8f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9ac8fc: ldr             x0, [x0, #0x38]
    // 0x9ac900: StoreField: r1->field_2b = r0
    //     0x9ac900: stur            w0, [x1, #0x2b]
    // 0x9ac904: StoreField: r1->field_2f = rZR
    //     0x9ac904: stur            xzr, [x1, #0x2f]
    // 0x9ac908: ldur            x0, [fp, #-8]
    // 0x9ac90c: StoreField: r1->field_b = r0
    //     0x9ac90c: stur            w0, [x1, #0xb]
    // 0x9ac910: r0 = Padding()
    //     0x9ac910: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9ac914: mov             x1, x0
    // 0x9ac918: r0 = Instance_EdgeInsets
    //     0x9ac918: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x9ac91c: ldr             x0, [x0, #0x1f0]
    // 0x9ac920: stur            x1, [fp, #-8]
    // 0x9ac924: StoreField: r1->field_f = r0
    //     0x9ac924: stur            w0, [x1, #0xf]
    // 0x9ac928: ldur            x0, [fp, #-0x10]
    // 0x9ac92c: StoreField: r1->field_b = r0
    //     0x9ac92c: stur            w0, [x1, #0xb]
    // 0x9ac930: r0 = Container()
    //     0x9ac930: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9ac934: stur            x0, [fp, #-0x10]
    // 0x9ac938: ldur            x16, [fp, #-0x38]
    // 0x9ac93c: r30 = Instance_EdgeInsets
    //     0x9ac93c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x9ac940: ldr             lr, [lr, #0x1f0]
    // 0x9ac944: stp             lr, x16, [SP, #8]
    // 0x9ac948: ldur            x16, [fp, #-8]
    // 0x9ac94c: str             x16, [SP]
    // 0x9ac950: mov             x1, x0
    // 0x9ac954: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, margin, 0x2, null]
    //     0x9ac954: add             x4, PP, #0x53, lsl #12  ; [pp+0x534e8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "margin", 0x2, Null]
    //     0x9ac958: ldr             x4, [x4, #0x4e8]
    // 0x9ac95c: r0 = Container()
    //     0x9ac95c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9ac960: r0 = Padding()
    //     0x9ac960: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9ac964: mov             x1, x0
    // 0x9ac968: r0 = Instance_EdgeInsets
    //     0x9ac968: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0x9ac96c: ldr             x0, [x0, #0x240]
    // 0x9ac970: stur            x1, [fp, #-8]
    // 0x9ac974: StoreField: r1->field_f = r0
    //     0x9ac974: stur            w0, [x1, #0xf]
    // 0x9ac978: ldur            x0, [fp, #-0x10]
    // 0x9ac97c: StoreField: r1->field_b = r0
    //     0x9ac97c: stur            w0, [x1, #0xb]
    // 0x9ac980: r0 = InkWell()
    //     0x9ac980: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x9ac984: mov             x3, x0
    // 0x9ac988: ldur            x0, [fp, #-8]
    // 0x9ac98c: stur            x3, [fp, #-0x10]
    // 0x9ac990: StoreField: r3->field_b = r0
    //     0x9ac990: stur            w0, [x3, #0xb]
    // 0x9ac994: ldur            x2, [fp, #-0x28]
    // 0x9ac998: r1 = Function '<anonymous closure>':.
    //     0x9ac998: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bb80] AnonymousClosure: (0x9aca28), in [package:customer_app/app/presentation/custom_widgets/product_detail/product_website_navigator_widget.dart] _ProductWebsiteNavigatorWidgetState::build (0x9ac2a4)
    //     0x9ac99c: ldr             x1, [x1, #0xb80]
    // 0x9ac9a0: r0 = AllocateClosure()
    //     0x9ac9a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9ac9a4: mov             x1, x0
    // 0x9ac9a8: ldur            x0, [fp, #-0x10]
    // 0x9ac9ac: StoreField: r0->field_f = r1
    //     0x9ac9ac: stur            w1, [x0, #0xf]
    // 0x9ac9b0: r1 = true
    //     0x9ac9b0: add             x1, NULL, #0x20  ; true
    // 0x9ac9b4: StoreField: r0->field_43 = r1
    //     0x9ac9b4: stur            w1, [x0, #0x43]
    // 0x9ac9b8: r2 = Instance_BoxShape
    //     0x9ac9b8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x9ac9bc: ldr             x2, [x2, #0x80]
    // 0x9ac9c0: StoreField: r0->field_47 = r2
    //     0x9ac9c0: stur            w2, [x0, #0x47]
    // 0x9ac9c4: StoreField: r0->field_6f = r1
    //     0x9ac9c4: stur            w1, [x0, #0x6f]
    // 0x9ac9c8: r2 = false
    //     0x9ac9c8: add             x2, NULL, #0x30  ; false
    // 0x9ac9cc: StoreField: r0->field_73 = r2
    //     0x9ac9cc: stur            w2, [x0, #0x73]
    // 0x9ac9d0: StoreField: r0->field_83 = r1
    //     0x9ac9d0: stur            w1, [x0, #0x83]
    // 0x9ac9d4: StoreField: r0->field_7b = r2
    //     0x9ac9d4: stur            w2, [x0, #0x7b]
    // 0x9ac9d8: LeaveFrame
    //     0x9ac9d8: mov             SP, fp
    //     0x9ac9dc: ldp             fp, lr, [SP], #0x10
    // 0x9ac9e0: ret
    //     0x9ac9e0: ret             
    // 0x9ac9e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ac9e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ac9e8: b               #0x9ac2cc
    // 0x9ac9ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ac9ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ac9f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ac9f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ac9f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ac9f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ac9f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ac9f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ac9fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ac9fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9aca00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9aca00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9aca28, size: 0xe8
    // 0x9aca28: EnterFrame
    //     0x9aca28: stp             fp, lr, [SP, #-0x10]!
    //     0x9aca2c: mov             fp, SP
    // 0x9aca30: AllocStack(0x38)
    //     0x9aca30: sub             SP, SP, #0x38
    // 0x9aca34: SetupParameters()
    //     0x9aca34: ldr             x0, [fp, #0x10]
    //     0x9aca38: ldur            w1, [x0, #0x17]
    //     0x9aca3c: add             x1, x1, HEAP, lsl #32
    // 0x9aca40: CheckStackOverflow
    //     0x9aca40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9aca44: cmp             SP, x16
    //     0x9aca48: b.ls            #0x9acb04
    // 0x9aca4c: LoadField: r0 = r1->field_f
    //     0x9aca4c: ldur            w0, [x1, #0xf]
    // 0x9aca50: DecompressPointer r0
    //     0x9aca50: add             x0, x0, HEAP, lsl #32
    // 0x9aca54: LoadField: r1 = r0->field_b
    //     0x9aca54: ldur            w1, [x0, #0xb]
    // 0x9aca58: DecompressPointer r1
    //     0x9aca58: add             x1, x1, HEAP, lsl #32
    // 0x9aca5c: cmp             w1, NULL
    // 0x9aca60: b.eq            #0x9acb0c
    // 0x9aca64: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9aca64: ldur            w0, [x1, #0x17]
    // 0x9aca68: DecompressPointer r0
    //     0x9aca68: add             x0, x0, HEAP, lsl #32
    // 0x9aca6c: cmp             w0, NULL
    // 0x9aca70: b.ne            #0x9aca78
    // 0x9aca74: r0 = ""
    //     0x9aca74: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9aca78: LoadField: r2 = r1->field_13
    //     0x9aca78: ldur            w2, [x1, #0x13]
    // 0x9aca7c: DecompressPointer r2
    //     0x9aca7c: add             x2, x2, HEAP, lsl #32
    // 0x9aca80: cmp             w2, NULL
    // 0x9aca84: b.ne            #0x9aca8c
    // 0x9aca88: r2 = ""
    //     0x9aca88: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9aca8c: LoadField: r3 = r1->field_27
    //     0x9aca8c: ldur            w3, [x1, #0x27]
    // 0x9aca90: DecompressPointer r3
    //     0x9aca90: add             x3, x3, HEAP, lsl #32
    // 0x9aca94: cmp             w3, NULL
    // 0x9aca98: b.ne            #0x9acaa0
    // 0x9aca9c: r3 = ""
    //     0x9aca9c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9acaa0: LoadField: r4 = r1->field_1b
    //     0x9acaa0: ldur            w4, [x1, #0x1b]
    // 0x9acaa4: DecompressPointer r4
    //     0x9acaa4: add             x4, x4, HEAP, lsl #32
    // 0x9acaa8: cmp             w4, NULL
    // 0x9acaac: b.ne            #0x9acab4
    // 0x9acab0: r4 = ""
    //     0x9acab0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9acab4: LoadField: r5 = r1->field_23
    //     0x9acab4: ldur            w5, [x1, #0x23]
    // 0x9acab8: DecompressPointer r5
    //     0x9acab8: add             x5, x5, HEAP, lsl #32
    // 0x9acabc: stp             x0, x5, [SP, #0x28]
    // 0x9acac0: stp             x3, x2, [SP, #0x18]
    // 0x9acac4: r16 = "product_page"
    //     0x9acac4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x9acac8: ldr             x16, [x16, #0x480]
    // 0x9acacc: stp             x4, x16, [SP, #8]
    // 0x9acad0: r16 = "product_page"
    //     0x9acad0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x9acad4: ldr             x16, [x16, #0x480]
    // 0x9acad8: str             x16, [SP]
    // 0x9acadc: r4 = 0
    //     0x9acadc: movz            x4, #0
    // 0x9acae0: ldr             x0, [SP, #0x30]
    // 0x9acae4: r16 = UnlinkedCall_0x613b5c
    //     0x9acae4: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5bb88] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9acae8: add             x16, x16, #0xb88
    // 0x9acaec: ldp             x5, lr, [x16]
    // 0x9acaf0: blr             lr
    // 0x9acaf4: r0 = Null
    //     0x9acaf4: mov             x0, NULL
    // 0x9acaf8: LeaveFrame
    //     0x9acaf8: mov             SP, fp
    //     0x9acafc: ldp             fp, lr, [SP], #0x10
    // 0x9acb00: ret
    //     0x9acb00: ret             
    // 0x9acb04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9acb04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9acb08: b               #0x9aca4c
    // 0x9acb0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9acb0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9acb10, size: 0xe8
    // 0x9acb10: EnterFrame
    //     0x9acb10: stp             fp, lr, [SP, #-0x10]!
    //     0x9acb14: mov             fp, SP
    // 0x9acb18: AllocStack(0x38)
    //     0x9acb18: sub             SP, SP, #0x38
    // 0x9acb1c: SetupParameters()
    //     0x9acb1c: ldr             x0, [fp, #0x10]
    //     0x9acb20: ldur            w1, [x0, #0x17]
    //     0x9acb24: add             x1, x1, HEAP, lsl #32
    // 0x9acb28: CheckStackOverflow
    //     0x9acb28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9acb2c: cmp             SP, x16
    //     0x9acb30: b.ls            #0x9acbec
    // 0x9acb34: LoadField: r0 = r1->field_f
    //     0x9acb34: ldur            w0, [x1, #0xf]
    // 0x9acb38: DecompressPointer r0
    //     0x9acb38: add             x0, x0, HEAP, lsl #32
    // 0x9acb3c: LoadField: r1 = r0->field_b
    //     0x9acb3c: ldur            w1, [x0, #0xb]
    // 0x9acb40: DecompressPointer r1
    //     0x9acb40: add             x1, x1, HEAP, lsl #32
    // 0x9acb44: cmp             w1, NULL
    // 0x9acb48: b.eq            #0x9acbf4
    // 0x9acb4c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9acb4c: ldur            w0, [x1, #0x17]
    // 0x9acb50: DecompressPointer r0
    //     0x9acb50: add             x0, x0, HEAP, lsl #32
    // 0x9acb54: cmp             w0, NULL
    // 0x9acb58: b.ne            #0x9acb60
    // 0x9acb5c: r0 = ""
    //     0x9acb5c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9acb60: LoadField: r2 = r1->field_13
    //     0x9acb60: ldur            w2, [x1, #0x13]
    // 0x9acb64: DecompressPointer r2
    //     0x9acb64: add             x2, x2, HEAP, lsl #32
    // 0x9acb68: cmp             w2, NULL
    // 0x9acb6c: b.ne            #0x9acb74
    // 0x9acb70: r2 = ""
    //     0x9acb70: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9acb74: LoadField: r3 = r1->field_27
    //     0x9acb74: ldur            w3, [x1, #0x27]
    // 0x9acb78: DecompressPointer r3
    //     0x9acb78: add             x3, x3, HEAP, lsl #32
    // 0x9acb7c: cmp             w3, NULL
    // 0x9acb80: b.ne            #0x9acb88
    // 0x9acb84: r3 = ""
    //     0x9acb84: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9acb88: LoadField: r4 = r1->field_1b
    //     0x9acb88: ldur            w4, [x1, #0x1b]
    // 0x9acb8c: DecompressPointer r4
    //     0x9acb8c: add             x4, x4, HEAP, lsl #32
    // 0x9acb90: cmp             w4, NULL
    // 0x9acb94: b.ne            #0x9acb9c
    // 0x9acb98: r4 = ""
    //     0x9acb98: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9acb9c: LoadField: r5 = r1->field_23
    //     0x9acb9c: ldur            w5, [x1, #0x23]
    // 0x9acba0: DecompressPointer r5
    //     0x9acba0: add             x5, x5, HEAP, lsl #32
    // 0x9acba4: stp             x0, x5, [SP, #0x28]
    // 0x9acba8: stp             x3, x2, [SP, #0x18]
    // 0x9acbac: r16 = "product_page"
    //     0x9acbac: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x9acbb0: ldr             x16, [x16, #0x480]
    // 0x9acbb4: stp             x4, x16, [SP, #8]
    // 0x9acbb8: r16 = "product_page"
    //     0x9acbb8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x9acbbc: ldr             x16, [x16, #0x480]
    // 0x9acbc0: str             x16, [SP]
    // 0x9acbc4: r4 = 0
    //     0x9acbc4: movz            x4, #0
    // 0x9acbc8: ldr             x0, [SP, #0x30]
    // 0x9acbcc: r16 = UnlinkedCall_0x613b5c
    //     0x9acbcc: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5bb98] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9acbd0: add             x16, x16, #0xb98
    // 0x9acbd4: ldp             x5, lr, [x16]
    // 0x9acbd8: blr             lr
    // 0x9acbdc: r0 = Null
    //     0x9acbdc: mov             x0, NULL
    // 0x9acbe0: LeaveFrame
    //     0x9acbe0: mov             SP, fp
    //     0x9acbe4: ldp             fp, lr, [SP], #0x10
    // 0x9acbe8: ret
    //     0x9acbe8: ret             
    // 0x9acbec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9acbec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9acbf0: b               #0x9acb34
    // 0x9acbf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9acbf4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4302, size: 0x48, field offset: 0xc
//   const constructor, 
class ProductWebsiteNavigatorWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc79e1c, size: 0x24
    // 0xc79e1c: EnterFrame
    //     0xc79e1c: stp             fp, lr, [SP, #-0x10]!
    //     0xc79e20: mov             fp, SP
    // 0xc79e24: mov             x0, x1
    // 0xc79e28: r1 = <ProductWebsiteNavigatorWidget>
    //     0xc79e28: add             x1, PP, #0x49, lsl #12  ; [pp+0x492b8] TypeArguments: <ProductWebsiteNavigatorWidget>
    //     0xc79e2c: ldr             x1, [x1, #0x2b8]
    // 0xc79e30: r0 = _ProductWebsiteNavigatorWidgetState()
    //     0xc79e30: bl              #0xc79e40  ; Allocate_ProductWebsiteNavigatorWidgetStateStub -> _ProductWebsiteNavigatorWidgetState (size=0x14)
    // 0xc79e34: LeaveFrame
    //     0xc79e34: mov             SP, fp
    //     0xc79e38: ldp             fp, lr, [SP], #0x10
    // 0xc79e3c: ret
    //     0xc79e3c: ret             
  }
}
