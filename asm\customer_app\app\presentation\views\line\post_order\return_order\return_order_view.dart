// lib: , url: package:customer_app/app/presentation/views/line/post_order/return_order/return_order_view.dart

// class id: 1049549, size: 0x8
class :: {
}

// class id: 4524, size: 0x14, field offset: 0x14
//   const constructor, 
class ReturnOrderView extends BaseView<dynamic> {

  [closure] String? _validateOther(dynamic, String?) {
    // ** addr: 0xb51164, size: 0x7c
    // 0xb51164: ldr             x1, [SP]
    // 0xb51168: cmp             w1, NULL
    // 0xb5116c: b.ne            #0xb51178
    // 0xb51170: r2 = Null
    //     0xb51170: mov             x2, NULL
    // 0xb51174: b               #0xb5117c
    // 0xb51178: LoadField: r2 = r1->field_7
    //     0xb51178: ldur            w2, [x1, #7]
    // 0xb5117c: cmp             w2, NULL
    // 0xb51180: b.ne            #0xb5118c
    // 0xb51184: r2 = 0
    //     0xb51184: movz            x2, #0
    // 0xb51188: b               #0xb51194
    // 0xb5118c: r3 = LoadInt32Instr(r2)
    //     0xb5118c: sbfx            x3, x2, #1, #0x1f
    // 0xb51190: mov             x2, x3
    // 0xb51194: cmp             x2, #0xa
    // 0xb51198: b.lt            #0xb511cc
    // 0xb5119c: cmp             w1, NULL
    // 0xb511a0: b.ne            #0xb511ac
    // 0xb511a4: r1 = Null
    //     0xb511a4: mov             x1, NULL
    // 0xb511a8: b               #0xb511c0
    // 0xb511ac: LoadField: r2 = r1->field_7
    //     0xb511ac: ldur            w2, [x1, #7]
    // 0xb511b0: cbz             w2, #0xb511bc
    // 0xb511b4: r1 = false
    //     0xb511b4: add             x1, NULL, #0x30  ; false
    // 0xb511b8: b               #0xb511c0
    // 0xb511bc: r1 = true
    //     0xb511bc: add             x1, NULL, #0x20  ; true
    // 0xb511c0: cmp             w1, NULL
    // 0xb511c4: b.eq            #0xb511cc
    // 0xb511c8: tbnz            w1, #4, #0xb511d8
    // 0xb511cc: r0 = "Enter at least 10 characters"
    //     0xb511cc: add             x0, PP, #0x33, lsl #12  ; [pp+0x33850] "Enter at least 10 characters"
    //     0xb511d0: ldr             x0, [x0, #0x850]
    // 0xb511d4: b               #0xb511dc
    // 0xb511d8: r0 = Null
    //     0xb511d8: mov             x0, NULL
    // 0xb511dc: ret
    //     0xb511dc: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x132588c, size: 0x7a0
    // 0x132588c: EnterFrame
    //     0x132588c: stp             fp, lr, [SP, #-0x10]!
    //     0x1325890: mov             fp, SP
    // 0x1325894: AllocStack(0x48)
    //     0x1325894: sub             SP, SP, #0x48
    // 0x1325898: SetupParameters()
    //     0x1325898: ldr             x0, [fp, #0x10]
    //     0x132589c: ldur            w2, [x0, #0x17]
    //     0x13258a0: add             x2, x2, HEAP, lsl #32
    //     0x13258a4: stur            x2, [fp, #-8]
    // 0x13258a8: CheckStackOverflow
    //     0x13258a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13258ac: cmp             SP, x16
    //     0x13258b0: b.ls            #0x1326024
    // 0x13258b4: LoadField: r1 = r2->field_f
    //     0x13258b4: ldur            w1, [x2, #0xf]
    // 0x13258b8: DecompressPointer r1
    //     0x13258b8: add             x1, x1, HEAP, lsl #32
    // 0x13258bc: r0 = controller()
    //     0x13258bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13258c0: mov             x2, x0
    // 0x13258c4: ldur            x0, [fp, #-8]
    // 0x13258c8: stur            x2, [fp, #-0x10]
    // 0x13258cc: LoadField: r1 = r0->field_f
    //     0x13258cc: ldur            w1, [x0, #0xf]
    // 0x13258d0: DecompressPointer r1
    //     0x13258d0: add             x1, x1, HEAP, lsl #32
    // 0x13258d4: r0 = controller()
    //     0x13258d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13258d8: LoadField: r1 = r0->field_57
    //     0x13258d8: ldur            w1, [x0, #0x57]
    // 0x13258dc: DecompressPointer r1
    //     0x13258dc: add             x1, x1, HEAP, lsl #32
    // 0x13258e0: r0 = value()
    //     0x13258e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13258e4: cmp             w0, NULL
    // 0x13258e8: b.ne            #0x13258f4
    // 0x13258ec: r0 = Null
    //     0x13258ec: mov             x0, NULL
    // 0x13258f0: b               #0x1325914
    // 0x13258f4: LoadField: r1 = r0->field_33
    //     0x13258f4: ldur            w1, [x0, #0x33]
    // 0x13258f8: DecompressPointer r1
    //     0x13258f8: add             x1, x1, HEAP, lsl #32
    // 0x13258fc: cmp             w1, NULL
    // 0x1325900: b.ne            #0x132590c
    // 0x1325904: r0 = Null
    //     0x1325904: mov             x0, NULL
    // 0x1325908: b               #0x1325914
    // 0x132590c: LoadField: r0 = r1->field_27
    //     0x132590c: ldur            w0, [x1, #0x27]
    // 0x1325910: DecompressPointer r0
    //     0x1325910: add             x0, x0, HEAP, lsl #32
    // 0x1325914: cmp             w0, NULL
    // 0x1325918: b.ne            #0x1325924
    // 0x132591c: r2 = ""
    //     0x132591c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1325920: b               #0x1325928
    // 0x1325924: mov             x2, x0
    // 0x1325928: ldur            x0, [fp, #-8]
    // 0x132592c: stur            x2, [fp, #-0x18]
    // 0x1325930: LoadField: r1 = r0->field_f
    //     0x1325930: ldur            w1, [x0, #0xf]
    // 0x1325934: DecompressPointer r1
    //     0x1325934: add             x1, x1, HEAP, lsl #32
    // 0x1325938: r0 = controller()
    //     0x1325938: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x132593c: LoadField: r1 = r0->field_bb
    //     0x132593c: ldur            w1, [x0, #0xbb]
    // 0x1325940: DecompressPointer r1
    //     0x1325940: add             x1, x1, HEAP, lsl #32
    // 0x1325944: r0 = LoadClassIdInstr(r1)
    //     0x1325944: ldur            x0, [x1, #-1]
    //     0x1325948: ubfx            x0, x0, #0xc, #0x14
    // 0x132594c: r16 = "return"
    //     0x132594c: add             x16, PP, #0x32, lsl #12  ; [pp+0x329b8] "return"
    //     0x1325950: ldr             x16, [x16, #0x9b8]
    // 0x1325954: stp             x16, x1, [SP]
    // 0x1325958: mov             lr, x0
    // 0x132595c: ldr             lr, [x21, lr, lsl #3]
    // 0x1325960: blr             lr
    // 0x1325964: tbnz            w0, #4, #0x1325974
    // 0x1325968: r3 = "reason_page"
    //     0x1325968: add             x3, PP, #0x33, lsl #12  ; [pp+0x33f48] "reason_page"
    //     0x132596c: ldr             x3, [x3, #0xf48]
    // 0x1325970: b               #0x132597c
    // 0x1325974: r3 = "exchange_reason"
    //     0x1325974: add             x3, PP, #0x33, lsl #12  ; [pp+0x33f50] "exchange_reason"
    //     0x1325978: ldr             x3, [x3, #0xf50]
    // 0x132597c: ldur            x0, [fp, #-8]
    // 0x1325980: ldur            x1, [fp, #-0x10]
    // 0x1325984: ldur            x2, [fp, #-0x18]
    // 0x1325988: r0 = ctaExchangeWithCustomerRemarkPostEvent()
    //     0x1325988: bl              #0x132ceb4  ; [package:customer_app/app/presentation/controllers/post_order/return_order_controller.dart] ReturnOrderController::ctaExchangeWithCustomerRemarkPostEvent
    // 0x132598c: ldur            x0, [fp, #-8]
    // 0x1325990: LoadField: r1 = r0->field_f
    //     0x1325990: ldur            w1, [x0, #0xf]
    // 0x1325994: DecompressPointer r1
    //     0x1325994: add             x1, x1, HEAP, lsl #32
    // 0x1325998: r0 = controller()
    //     0x1325998: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x132599c: LoadField: r1 = r0->field_6b
    //     0x132599c: ldur            w1, [x0, #0x6b]
    // 0x13259a0: DecompressPointer r1
    //     0x13259a0: add             x1, x1, HEAP, lsl #32
    // 0x13259a4: r0 = value()
    //     0x13259a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13259a8: cmp             w0, NULL
    // 0x13259ac: b.eq            #0x13259b4
    // 0x13259b0: tbz             w0, #4, #0x13259d0
    // 0x13259b4: ldur            x0, [fp, #-8]
    // 0x13259b8: LoadField: r1 = r0->field_f
    //     0x13259b8: ldur            w1, [x0, #0xf]
    // 0x13259bc: DecompressPointer r1
    //     0x13259bc: add             x1, x1, HEAP, lsl #32
    // 0x13259c0: r2 = "Please agree terms"
    //     0x13259c0: add             x2, PP, #0x32, lsl #12  ; [pp+0x32c80] "Please agree terms"
    //     0x13259c4: ldr             x2, [x2, #0xc80]
    // 0x13259c8: r0 = showErrorSnackBar()
    //     0x13259c8: bl              #0x9a5fc0  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showErrorSnackBar
    // 0x13259cc: b               #0x1326014
    // 0x13259d0: ldur            x0, [fp, #-8]
    // 0x13259d4: LoadField: r1 = r0->field_f
    //     0x13259d4: ldur            w1, [x0, #0xf]
    // 0x13259d8: DecompressPointer r1
    //     0x13259d8: add             x1, x1, HEAP, lsl #32
    // 0x13259dc: r0 = controller()
    //     0x13259dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13259e0: LoadField: r1 = r0->field_67
    //     0x13259e0: ldur            w1, [x0, #0x67]
    // 0x13259e4: DecompressPointer r1
    //     0x13259e4: add             x1, x1, HEAP, lsl #32
    // 0x13259e8: r0 = value()
    //     0x13259e8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13259ec: r1 = LoadClassIdInstr(r0)
    //     0x13259ec: ldur            x1, [x0, #-1]
    //     0x13259f0: ubfx            x1, x1, #0xc, #0x14
    // 0x13259f4: r16 = ""
    //     0x13259f4: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13259f8: stp             x16, x0, [SP]
    // 0x13259fc: mov             x0, x1
    // 0x1325a00: mov             lr, x0
    // 0x1325a04: ldr             lr, [x21, lr, lsl #3]
    // 0x1325a08: blr             lr
    // 0x1325a0c: tbz             w0, #4, #0x1325a34
    // 0x1325a10: ldur            x0, [fp, #-8]
    // 0x1325a14: LoadField: r1 = r0->field_f
    //     0x1325a14: ldur            w1, [x0, #0xf]
    // 0x1325a18: DecompressPointer r1
    //     0x1325a18: add             x1, x1, HEAP, lsl #32
    // 0x1325a1c: r0 = controller()
    //     0x1325a1c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325a20: LoadField: r1 = r0->field_67
    //     0x1325a20: ldur            w1, [x0, #0x67]
    // 0x1325a24: DecompressPointer r1
    //     0x1325a24: add             x1, x1, HEAP, lsl #32
    // 0x1325a28: r0 = value()
    //     0x1325a28: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1325a2c: cmp             w0, NULL
    // 0x1325a30: b.ne            #0x1325a50
    // 0x1325a34: ldur            x0, [fp, #-8]
    // 0x1325a38: LoadField: r1 = r0->field_f
    //     0x1325a38: ldur            w1, [x0, #0xf]
    // 0x1325a3c: DecompressPointer r1
    //     0x1325a3c: add             x1, x1, HEAP, lsl #32
    // 0x1325a40: r2 = "Please select a Reason !!!"
    //     0x1325a40: add             x2, PP, #0x33, lsl #12  ; [pp+0x33f58] "Please select a Reason !!!"
    //     0x1325a44: ldr             x2, [x2, #0xf58]
    // 0x1325a48: r0 = showErrorSnackBar()
    //     0x1325a48: bl              #0x9a5fc0  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showErrorSnackBar
    // 0x1325a4c: b               #0x1326014
    // 0x1325a50: ldur            x0, [fp, #-8]
    // 0x1325a54: LoadField: r1 = r0->field_f
    //     0x1325a54: ldur            w1, [x0, #0xf]
    // 0x1325a58: DecompressPointer r1
    //     0x1325a58: add             x1, x1, HEAP, lsl #32
    // 0x1325a5c: r0 = controller()
    //     0x1325a5c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325a60: LoadField: r1 = r0->field_db
    //     0x1325a60: ldur            w1, [x0, #0xdb]
    // 0x1325a64: DecompressPointer r1
    //     0x1325a64: add             x1, x1, HEAP, lsl #32
    // 0x1325a68: LoadField: r0 = r1->field_27
    //     0x1325a68: ldur            w0, [x1, #0x27]
    // 0x1325a6c: DecompressPointer r0
    //     0x1325a6c: add             x0, x0, HEAP, lsl #32
    // 0x1325a70: LoadField: r1 = r0->field_7
    //     0x1325a70: ldur            w1, [x0, #7]
    // 0x1325a74: DecompressPointer r1
    //     0x1325a74: add             x1, x1, HEAP, lsl #32
    // 0x1325a78: LoadField: r0 = r1->field_7
    //     0x1325a78: ldur            w0, [x1, #7]
    // 0x1325a7c: cbnz            w0, #0x1325a9c
    // 0x1325a80: ldur            x0, [fp, #-8]
    // 0x1325a84: LoadField: r1 = r0->field_f
    //     0x1325a84: ldur            w1, [x0, #0xf]
    // 0x1325a88: DecompressPointer r1
    //     0x1325a88: add             x1, x1, HEAP, lsl #32
    // 0x1325a8c: r2 = "Please Add Remarks"
    //     0x1325a8c: add             x2, PP, #0x32, lsl #12  ; [pp+0x32c98] "Please Add Remarks"
    //     0x1325a90: ldr             x2, [x2, #0xc98]
    // 0x1325a94: r0 = showErrorSnackBar()
    //     0x1325a94: bl              #0x9a5fc0  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showErrorSnackBar
    // 0x1325a98: b               #0x1326014
    // 0x1325a9c: ldur            x0, [fp, #-8]
    // 0x1325aa0: LoadField: r1 = r0->field_f
    //     0x1325aa0: ldur            w1, [x0, #0xf]
    // 0x1325aa4: DecompressPointer r1
    //     0x1325aa4: add             x1, x1, HEAP, lsl #32
    // 0x1325aa8: r0 = controller()
    //     0x1325aa8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325aac: LoadField: r1 = r0->field_db
    //     0x1325aac: ldur            w1, [x0, #0xdb]
    // 0x1325ab0: DecompressPointer r1
    //     0x1325ab0: add             x1, x1, HEAP, lsl #32
    // 0x1325ab4: LoadField: r0 = r1->field_27
    //     0x1325ab4: ldur            w0, [x1, #0x27]
    // 0x1325ab8: DecompressPointer r0
    //     0x1325ab8: add             x0, x0, HEAP, lsl #32
    // 0x1325abc: LoadField: r1 = r0->field_7
    //     0x1325abc: ldur            w1, [x0, #7]
    // 0x1325ac0: DecompressPointer r1
    //     0x1325ac0: add             x1, x1, HEAP, lsl #32
    // 0x1325ac4: LoadField: r0 = r1->field_7
    //     0x1325ac4: ldur            w0, [x1, #7]
    // 0x1325ac8: r1 = LoadInt32Instr(r0)
    //     0x1325ac8: sbfx            x1, x0, #1, #0x1f
    // 0x1325acc: cmp             x1, #0xa
    // 0x1325ad0: b.ge            #0x1325af0
    // 0x1325ad4: ldur            x0, [fp, #-8]
    // 0x1325ad8: LoadField: r1 = r0->field_f
    //     0x1325ad8: ldur            w1, [x0, #0xf]
    // 0x1325adc: DecompressPointer r1
    //     0x1325adc: add             x1, x1, HEAP, lsl #32
    // 0x1325ae0: r2 = "Enter at least 10 characters in remark field"
    //     0x1325ae0: add             x2, PP, #0x32, lsl #12  ; [pp+0x32ca0] "Enter at least 10 characters in remark field"
    //     0x1325ae4: ldr             x2, [x2, #0xca0]
    // 0x1325ae8: r0 = showErrorSnackBar()
    //     0x1325ae8: bl              #0x9a5fc0  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showErrorSnackBar
    // 0x1325aec: b               #0x1326014
    // 0x1325af0: ldur            x0, [fp, #-8]
    // 0x1325af4: LoadField: r1 = r0->field_f
    //     0x1325af4: ldur            w1, [x0, #0xf]
    // 0x1325af8: DecompressPointer r1
    //     0x1325af8: add             x1, x1, HEAP, lsl #32
    // 0x1325afc: r0 = controller()
    //     0x1325afc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325b00: LoadField: r1 = r0->field_b7
    //     0x1325b00: ldur            w1, [x0, #0xb7]
    // 0x1325b04: DecompressPointer r1
    //     0x1325b04: add             x1, x1, HEAP, lsl #32
    // 0x1325b08: cmp             w1, NULL
    // 0x1325b0c: b.ne            #0x1325b18
    // 0x1325b10: r0 = 0
    //     0x1325b10: movz            x0, #0
    // 0x1325b14: b               #0x1325b24
    // 0x1325b18: r0 = LoadInt32Instr(r1)
    //     0x1325b18: sbfx            x0, x1, #1, #0x1f
    //     0x1325b1c: tbz             w1, #0, #0x1325b24
    //     0x1325b20: ldur            x0, [x1, #7]
    // 0x1325b24: cmp             x0, #0
    // 0x1325b28: b.le            #0x1325ee4
    // 0x1325b2c: ldur            x0, [fp, #-8]
    // 0x1325b30: LoadField: r1 = r0->field_f
    //     0x1325b30: ldur            w1, [x0, #0xf]
    // 0x1325b34: DecompressPointer r1
    //     0x1325b34: add             x1, x1, HEAP, lsl #32
    // 0x1325b38: r0 = controller()
    //     0x1325b38: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325b3c: LoadField: r1 = r0->field_bb
    //     0x1325b3c: ldur            w1, [x0, #0xbb]
    // 0x1325b40: DecompressPointer r1
    //     0x1325b40: add             x1, x1, HEAP, lsl #32
    // 0x1325b44: cmp             w1, NULL
    // 0x1325b48: b.ne            #0x1325b54
    // 0x1325b4c: r0 = ""
    //     0x1325b4c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1325b50: b               #0x1325b58
    // 0x1325b54: mov             x0, x1
    // 0x1325b58: r1 = LoadClassIdInstr(r0)
    //     0x1325b58: ldur            x1, [x0, #-1]
    //     0x1325b5c: ubfx            x1, x1, #0xc, #0x14
    // 0x1325b60: r16 = "replace-same"
    //     0x1325b60: add             x16, PP, #0x32, lsl #12  ; [pp+0x32ca8] "replace-same"
    //     0x1325b64: ldr             x16, [x16, #0xca8]
    // 0x1325b68: stp             x16, x0, [SP]
    // 0x1325b6c: mov             x0, x1
    // 0x1325b70: mov             lr, x0
    // 0x1325b74: ldr             lr, [x21, lr, lsl #3]
    // 0x1325b78: blr             lr
    // 0x1325b7c: tbnz            w0, #4, #0x1325edc
    // 0x1325b80: ldur            x0, [fp, #-8]
    // 0x1325b84: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1325b84: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1325b88: ldr             x0, [x0, #0x1c80]
    //     0x1325b8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1325b90: cmp             w0, w16
    //     0x1325b94: b.ne            #0x1325ba0
    //     0x1325b98: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1325b9c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1325ba0: r1 = Null
    //     0x1325ba0: mov             x1, NULL
    // 0x1325ba4: r2 = 32
    //     0x1325ba4: movz            x2, #0x20
    // 0x1325ba8: r0 = AllocateArray()
    //     0x1325ba8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1325bac: stur            x0, [fp, #-0x10]
    // 0x1325bb0: r16 = "seller_group_id"
    //     0x1325bb0: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb00] "seller_group_id"
    //     0x1325bb4: ldr             x16, [x16, #0xb00]
    // 0x1325bb8: StoreField: r0->field_f = r16
    //     0x1325bb8: stur            w16, [x0, #0xf]
    // 0x1325bbc: ldur            x2, [fp, #-8]
    // 0x1325bc0: LoadField: r1 = r2->field_f
    //     0x1325bc0: ldur            w1, [x2, #0xf]
    // 0x1325bc4: DecompressPointer r1
    //     0x1325bc4: add             x1, x1, HEAP, lsl #32
    // 0x1325bc8: r0 = controller()
    //     0x1325bc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325bcc: LoadField: r1 = r0->field_5f
    //     0x1325bcc: ldur            w1, [x0, #0x5f]
    // 0x1325bd0: DecompressPointer r1
    //     0x1325bd0: add             x1, x1, HEAP, lsl #32
    // 0x1325bd4: r0 = value()
    //     0x1325bd4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1325bd8: cmp             w0, NULL
    // 0x1325bdc: b.ne            #0x1325be4
    // 0x1325be0: r0 = ""
    //     0x1325be0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1325be4: ldur            x3, [fp, #-8]
    // 0x1325be8: ldur            x2, [fp, #-0x10]
    // 0x1325bec: mov             x1, x2
    // 0x1325bf0: ArrayStore: r1[1] = r0  ; List_4
    //     0x1325bf0: add             x25, x1, #0x13
    //     0x1325bf4: str             w0, [x25]
    //     0x1325bf8: tbz             w0, #0, #0x1325c14
    //     0x1325bfc: ldurb           w16, [x1, #-1]
    //     0x1325c00: ldurb           w17, [x0, #-1]
    //     0x1325c04: and             x16, x17, x16, lsr #2
    //     0x1325c08: tst             x16, HEAP, lsr #32
    //     0x1325c0c: b.eq            #0x1325c14
    //     0x1325c10: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1325c14: r16 = "sku_short_id"
    //     0x1325c14: add             x16, PP, #0xb, lsl #12  ; [pp+0xb4a0] "sku_short_id"
    //     0x1325c18: ldr             x16, [x16, #0x4a0]
    // 0x1325c1c: ArrayStore: r2[0] = r16  ; List_4
    //     0x1325c1c: stur            w16, [x2, #0x17]
    // 0x1325c20: LoadField: r1 = r3->field_f
    //     0x1325c20: ldur            w1, [x3, #0xf]
    // 0x1325c24: DecompressPointer r1
    //     0x1325c24: add             x1, x1, HEAP, lsl #32
    // 0x1325c28: r0 = controller()
    //     0x1325c28: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325c2c: LoadField: r1 = r0->field_bf
    //     0x1325c2c: ldur            w1, [x0, #0xbf]
    // 0x1325c30: DecompressPointer r1
    //     0x1325c30: add             x1, x1, HEAP, lsl #32
    // 0x1325c34: r0 = value()
    //     0x1325c34: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1325c38: cmp             w0, NULL
    // 0x1325c3c: b.ne            #0x1325c44
    // 0x1325c40: r0 = ""
    //     0x1325c40: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1325c44: ldur            x3, [fp, #-8]
    // 0x1325c48: ldur            x2, [fp, #-0x10]
    // 0x1325c4c: mov             x1, x2
    // 0x1325c50: ArrayStore: r1[3] = r0  ; List_4
    //     0x1325c50: add             x25, x1, #0x1b
    //     0x1325c54: str             w0, [x25]
    //     0x1325c58: tbz             w0, #0, #0x1325c74
    //     0x1325c5c: ldurb           w16, [x1, #-1]
    //     0x1325c60: ldurb           w17, [x0, #-1]
    //     0x1325c64: and             x16, x17, x16, lsr #2
    //     0x1325c68: tst             x16, HEAP, lsr #32
    //     0x1325c6c: b.eq            #0x1325c74
    //     0x1325c70: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1325c74: r16 = "type"
    //     0x1325c74: ldr             x16, [PP, #0x2e10]  ; [pp+0x2e10] "type"
    // 0x1325c78: StoreField: r2->field_1f = r16
    //     0x1325c78: stur            w16, [x2, #0x1f]
    // 0x1325c7c: LoadField: r1 = r3->field_f
    //     0x1325c7c: ldur            w1, [x3, #0xf]
    // 0x1325c80: DecompressPointer r1
    //     0x1325c80: add             x1, x1, HEAP, lsl #32
    // 0x1325c84: r0 = controller()
    //     0x1325c84: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325c88: LoadField: r1 = r0->field_bb
    //     0x1325c88: ldur            w1, [x0, #0xbb]
    // 0x1325c8c: DecompressPointer r1
    //     0x1325c8c: add             x1, x1, HEAP, lsl #32
    // 0x1325c90: cmp             w1, NULL
    // 0x1325c94: b.ne            #0x1325ca0
    // 0x1325c98: r0 = ""
    //     0x1325c98: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1325c9c: b               #0x1325ca4
    // 0x1325ca0: mov             x0, x1
    // 0x1325ca4: ldur            x3, [fp, #-8]
    // 0x1325ca8: ldur            x2, [fp, #-0x10]
    // 0x1325cac: mov             x1, x2
    // 0x1325cb0: ArrayStore: r1[5] = r0  ; List_4
    //     0x1325cb0: add             x25, x1, #0x23
    //     0x1325cb4: str             w0, [x25]
    //     0x1325cb8: tbz             w0, #0, #0x1325cd4
    //     0x1325cbc: ldurb           w16, [x1, #-1]
    //     0x1325cc0: ldurb           w17, [x0, #-1]
    //     0x1325cc4: and             x16, x17, x16, lsr #2
    //     0x1325cc8: tst             x16, HEAP, lsr #32
    //     0x1325ccc: b.eq            #0x1325cd4
    //     0x1325cd0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1325cd4: r16 = "order_item_id"
    //     0x1325cd4: add             x16, PP, #0x32, lsl #12  ; [pp+0x32cb0] "order_item_id"
    //     0x1325cd8: ldr             x16, [x16, #0xcb0]
    // 0x1325cdc: StoreField: r2->field_27 = r16
    //     0x1325cdc: stur            w16, [x2, #0x27]
    // 0x1325ce0: LoadField: r1 = r3->field_f
    //     0x1325ce0: ldur            w1, [x3, #0xf]
    // 0x1325ce4: DecompressPointer r1
    //     0x1325ce4: add             x1, x1, HEAP, lsl #32
    // 0x1325ce8: r0 = controller()
    //     0x1325ce8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325cec: LoadField: r1 = r0->field_5f
    //     0x1325cec: ldur            w1, [x0, #0x5f]
    // 0x1325cf0: DecompressPointer r1
    //     0x1325cf0: add             x1, x1, HEAP, lsl #32
    // 0x1325cf4: r0 = value()
    //     0x1325cf4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1325cf8: cmp             w0, NULL
    // 0x1325cfc: b.ne            #0x1325d04
    // 0x1325d00: r0 = ""
    //     0x1325d00: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1325d04: ldur            x3, [fp, #-8]
    // 0x1325d08: ldur            x2, [fp, #-0x10]
    // 0x1325d0c: mov             x1, x2
    // 0x1325d10: ArrayStore: r1[7] = r0  ; List_4
    //     0x1325d10: add             x25, x1, #0x2b
    //     0x1325d14: str             w0, [x25]
    //     0x1325d18: tbz             w0, #0, #0x1325d34
    //     0x1325d1c: ldurb           w16, [x1, #-1]
    //     0x1325d20: ldurb           w17, [x0, #-1]
    //     0x1325d24: and             x16, x17, x16, lsr #2
    //     0x1325d28: tst             x16, HEAP, lsr #32
    //     0x1325d2c: b.eq            #0x1325d34
    //     0x1325d30: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1325d34: r16 = "reason"
    //     0x1325d34: add             x16, PP, #0xd, lsl #12  ; [pp+0xd298] "reason"
    //     0x1325d38: ldr             x16, [x16, #0x298]
    // 0x1325d3c: StoreField: r2->field_2f = r16
    //     0x1325d3c: stur            w16, [x2, #0x2f]
    // 0x1325d40: LoadField: r1 = r3->field_f
    //     0x1325d40: ldur            w1, [x3, #0xf]
    // 0x1325d44: DecompressPointer r1
    //     0x1325d44: add             x1, x1, HEAP, lsl #32
    // 0x1325d48: r0 = controller()
    //     0x1325d48: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325d4c: LoadField: r1 = r0->field_53
    //     0x1325d4c: ldur            w1, [x0, #0x53]
    // 0x1325d50: DecompressPointer r1
    //     0x1325d50: add             x1, x1, HEAP, lsl #32
    // 0x1325d54: LoadField: r0 = r1->field_27
    //     0x1325d54: ldur            w0, [x1, #0x27]
    // 0x1325d58: DecompressPointer r0
    //     0x1325d58: add             x0, x0, HEAP, lsl #32
    // 0x1325d5c: LoadField: r1 = r0->field_7
    //     0x1325d5c: ldur            w1, [x0, #7]
    // 0x1325d60: DecompressPointer r1
    //     0x1325d60: add             x1, x1, HEAP, lsl #32
    // 0x1325d64: mov             x0, x1
    // 0x1325d68: ldur            x1, [fp, #-0x10]
    // 0x1325d6c: ArrayStore: r1[9] = r0  ; List_4
    //     0x1325d6c: add             x25, x1, #0x33
    //     0x1325d70: str             w0, [x25]
    //     0x1325d74: tbz             w0, #0, #0x1325d90
    //     0x1325d78: ldurb           w16, [x1, #-1]
    //     0x1325d7c: ldurb           w17, [x0, #-1]
    //     0x1325d80: and             x16, x17, x16, lsr #2
    //     0x1325d84: tst             x16, HEAP, lsr #32
    //     0x1325d88: b.eq            #0x1325d90
    //     0x1325d8c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1325d90: ldur            x0, [fp, #-0x10]
    // 0x1325d94: r16 = "otherReason"
    //     0x1325d94: add             x16, PP, #0x32, lsl #12  ; [pp+0x32cc0] "otherReason"
    //     0x1325d98: ldr             x16, [x16, #0xcc0]
    // 0x1325d9c: StoreField: r0->field_37 = r16
    //     0x1325d9c: stur            w16, [x0, #0x37]
    // 0x1325da0: ldur            x2, [fp, #-8]
    // 0x1325da4: LoadField: r1 = r2->field_f
    //     0x1325da4: ldur            w1, [x2, #0xf]
    // 0x1325da8: DecompressPointer r1
    //     0x1325da8: add             x1, x1, HEAP, lsl #32
    // 0x1325dac: r0 = controller()
    //     0x1325dac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325db0: LoadField: r1 = r0->field_db
    //     0x1325db0: ldur            w1, [x0, #0xdb]
    // 0x1325db4: DecompressPointer r1
    //     0x1325db4: add             x1, x1, HEAP, lsl #32
    // 0x1325db8: LoadField: r0 = r1->field_27
    //     0x1325db8: ldur            w0, [x1, #0x27]
    // 0x1325dbc: DecompressPointer r0
    //     0x1325dbc: add             x0, x0, HEAP, lsl #32
    // 0x1325dc0: LoadField: r1 = r0->field_7
    //     0x1325dc0: ldur            w1, [x0, #7]
    // 0x1325dc4: DecompressPointer r1
    //     0x1325dc4: add             x1, x1, HEAP, lsl #32
    // 0x1325dc8: mov             x0, x1
    // 0x1325dcc: ldur            x1, [fp, #-0x10]
    // 0x1325dd0: ArrayStore: r1[11] = r0  ; List_4
    //     0x1325dd0: add             x25, x1, #0x3b
    //     0x1325dd4: str             w0, [x25]
    //     0x1325dd8: tbz             w0, #0, #0x1325df4
    //     0x1325ddc: ldurb           w16, [x1, #-1]
    //     0x1325de0: ldurb           w17, [x0, #-1]
    //     0x1325de4: and             x16, x17, x16, lsr #2
    //     0x1325de8: tst             x16, HEAP, lsr #32
    //     0x1325dec: b.eq            #0x1325df4
    //     0x1325df0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1325df4: ldur            x0, [fp, #-0x10]
    // 0x1325df8: r16 = "reason_code"
    //     0x1325df8: add             x16, PP, #0x32, lsl #12  ; [pp+0x32cb8] "reason_code"
    //     0x1325dfc: ldr             x16, [x16, #0xcb8]
    // 0x1325e00: StoreField: r0->field_3f = r16
    //     0x1325e00: stur            w16, [x0, #0x3f]
    // 0x1325e04: ldur            x2, [fp, #-8]
    // 0x1325e08: LoadField: r1 = r2->field_f
    //     0x1325e08: ldur            w1, [x2, #0xf]
    // 0x1325e0c: DecompressPointer r1
    //     0x1325e0c: add             x1, x1, HEAP, lsl #32
    // 0x1325e10: r0 = controller()
    //     0x1325e10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325e14: LoadField: r1 = r0->field_67
    //     0x1325e14: ldur            w1, [x0, #0x67]
    // 0x1325e18: DecompressPointer r1
    //     0x1325e18: add             x1, x1, HEAP, lsl #32
    // 0x1325e1c: r0 = value()
    //     0x1325e1c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1325e20: cmp             w0, NULL
    // 0x1325e24: b.ne            #0x1325e2c
    // 0x1325e28: r0 = ""
    //     0x1325e28: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1325e2c: ldur            x3, [fp, #-8]
    // 0x1325e30: ldur            x2, [fp, #-0x10]
    // 0x1325e34: mov             x1, x2
    // 0x1325e38: ArrayStore: r1[13] = r0  ; List_4
    //     0x1325e38: add             x25, x1, #0x43
    //     0x1325e3c: str             w0, [x25]
    //     0x1325e40: tbz             w0, #0, #0x1325e5c
    //     0x1325e44: ldurb           w16, [x1, #-1]
    //     0x1325e48: ldurb           w17, [x0, #-1]
    //     0x1325e4c: and             x16, x17, x16, lsr #2
    //     0x1325e50: tst             x16, HEAP, lsr #32
    //     0x1325e54: b.eq            #0x1325e5c
    //     0x1325e58: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1325e5c: r16 = "customer_product_skus_id"
    //     0x1325e5c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32cd8] "customer_product_skus_id"
    //     0x1325e60: ldr             x16, [x16, #0xcd8]
    // 0x1325e64: StoreField: r2->field_47 = r16
    //     0x1325e64: stur            w16, [x2, #0x47]
    // 0x1325e68: LoadField: r1 = r3->field_f
    //     0x1325e68: ldur            w1, [x3, #0xf]
    // 0x1325e6c: DecompressPointer r1
    //     0x1325e6c: add             x1, x1, HEAP, lsl #32
    // 0x1325e70: r0 = controller()
    //     0x1325e70: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325e74: LoadField: r1 = r0->field_c3
    //     0x1325e74: ldur            w1, [x0, #0xc3]
    // 0x1325e78: DecompressPointer r1
    //     0x1325e78: add             x1, x1, HEAP, lsl #32
    // 0x1325e7c: mov             x0, x1
    // 0x1325e80: ldur            x1, [fp, #-0x10]
    // 0x1325e84: ArrayStore: r1[15] = r0  ; List_4
    //     0x1325e84: add             x25, x1, #0x4b
    //     0x1325e88: str             w0, [x25]
    //     0x1325e8c: tbz             w0, #0, #0x1325ea8
    //     0x1325e90: ldurb           w16, [x1, #-1]
    //     0x1325e94: ldurb           w17, [x0, #-1]
    //     0x1325e98: and             x16, x17, x16, lsr #2
    //     0x1325e9c: tst             x16, HEAP, lsr #32
    //     0x1325ea0: b.eq            #0x1325ea8
    //     0x1325ea4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1325ea8: r16 = <String, String>
    //     0x1325ea8: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x1325eac: ldr             x16, [x16, #0x788]
    // 0x1325eb0: ldur            lr, [fp, #-0x10]
    // 0x1325eb4: stp             lr, x16, [SP]
    // 0x1325eb8: r0 = Map._fromLiteral()
    //     0x1325eb8: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x1325ebc: r16 = "/exchange-checkout"
    //     0x1325ebc: add             x16, PP, #0xd, lsl #12  ; [pp+0xd988] "/exchange-checkout"
    //     0x1325ec0: ldr             x16, [x16, #0x988]
    // 0x1325ec4: stp             x16, NULL, [SP, #8]
    // 0x1325ec8: str             x0, [SP]
    // 0x1325ecc: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x1325ecc: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x1325ed0: ldr             x4, [x4, #0x438]
    // 0x1325ed4: r0 = GetNavigation.toNamed()
    //     0x1325ed4: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x1325ed8: b               #0x1326014
    // 0x1325edc: ldur            x3, [fp, #-8]
    // 0x1325ee0: b               #0x1325ee8
    // 0x1325ee4: ldur            x3, [fp, #-8]
    // 0x1325ee8: LoadField: r1 = r3->field_f
    //     0x1325ee8: ldur            w1, [x3, #0xf]
    // 0x1325eec: DecompressPointer r1
    //     0x1325eec: add             x1, x1, HEAP, lsl #32
    // 0x1325ef0: r0 = controller()
    //     0x1325ef0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325ef4: LoadField: r1 = r0->field_bb
    //     0x1325ef4: ldur            w1, [x0, #0xbb]
    // 0x1325ef8: DecompressPointer r1
    //     0x1325ef8: add             x1, x1, HEAP, lsl #32
    // 0x1325efc: r0 = LoadClassIdInstr(r1)
    //     0x1325efc: ldur            x0, [x1, #-1]
    //     0x1325f00: ubfx            x0, x0, #0xc, #0x14
    // 0x1325f04: r16 = "return"
    //     0x1325f04: add             x16, PP, #0x32, lsl #12  ; [pp+0x329b8] "return"
    //     0x1325f08: ldr             x16, [x16, #0x9b8]
    // 0x1325f0c: stp             x16, x1, [SP]
    // 0x1325f10: mov             lr, x0
    // 0x1325f14: ldr             lr, [x21, lr, lsl #3]
    // 0x1325f18: blr             lr
    // 0x1325f1c: tbnz            w0, #4, #0x1325ffc
    // 0x1325f20: ldur            x0, [fp, #-8]
    // 0x1325f24: LoadField: r1 = r0->field_f
    //     0x1325f24: ldur            w1, [x0, #0xf]
    // 0x1325f28: DecompressPointer r1
    //     0x1325f28: add             x1, x1, HEAP, lsl #32
    // 0x1325f2c: r0 = controller()
    //     0x1325f2c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325f30: LoadField: r1 = r0->field_5f
    //     0x1325f30: ldur            w1, [x0, #0x5f]
    // 0x1325f34: DecompressPointer r1
    //     0x1325f34: add             x1, x1, HEAP, lsl #32
    // 0x1325f38: r0 = value()
    //     0x1325f38: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1325f3c: mov             x2, x0
    // 0x1325f40: ldur            x0, [fp, #-8]
    // 0x1325f44: stur            x2, [fp, #-0x10]
    // 0x1325f48: LoadField: r1 = r0->field_f
    //     0x1325f48: ldur            w1, [x0, #0xf]
    // 0x1325f4c: DecompressPointer r1
    //     0x1325f4c: add             x1, x1, HEAP, lsl #32
    // 0x1325f50: r0 = controller()
    //     0x1325f50: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325f54: LoadField: r1 = r0->field_67
    //     0x1325f54: ldur            w1, [x0, #0x67]
    // 0x1325f58: DecompressPointer r1
    //     0x1325f58: add             x1, x1, HEAP, lsl #32
    // 0x1325f5c: r0 = value()
    //     0x1325f5c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1325f60: mov             x2, x0
    // 0x1325f64: ldur            x0, [fp, #-8]
    // 0x1325f68: stur            x2, [fp, #-0x18]
    // 0x1325f6c: LoadField: r1 = r0->field_f
    //     0x1325f6c: ldur            w1, [x0, #0xf]
    // 0x1325f70: DecompressPointer r1
    //     0x1325f70: add             x1, x1, HEAP, lsl #32
    // 0x1325f74: r0 = controller()
    //     0x1325f74: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325f78: LoadField: r2 = r0->field_bb
    //     0x1325f78: ldur            w2, [x0, #0xbb]
    // 0x1325f7c: DecompressPointer r2
    //     0x1325f7c: add             x2, x2, HEAP, lsl #32
    // 0x1325f80: ldur            x0, [fp, #-8]
    // 0x1325f84: stur            x2, [fp, #-0x20]
    // 0x1325f88: LoadField: r1 = r0->field_f
    //     0x1325f88: ldur            w1, [x0, #0xf]
    // 0x1325f8c: DecompressPointer r1
    //     0x1325f8c: add             x1, x1, HEAP, lsl #32
    // 0x1325f90: r0 = controller()
    //     0x1325f90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325f94: LoadField: r1 = r0->field_db
    //     0x1325f94: ldur            w1, [x0, #0xdb]
    // 0x1325f98: DecompressPointer r1
    //     0x1325f98: add             x1, x1, HEAP, lsl #32
    // 0x1325f9c: LoadField: r0 = r1->field_27
    //     0x1325f9c: ldur            w0, [x1, #0x27]
    // 0x1325fa0: DecompressPointer r0
    //     0x1325fa0: add             x0, x0, HEAP, lsl #32
    // 0x1325fa4: LoadField: r1 = r0->field_7
    //     0x1325fa4: ldur            w1, [x0, #7]
    // 0x1325fa8: DecompressPointer r1
    //     0x1325fa8: add             x1, x1, HEAP, lsl #32
    // 0x1325fac: stur            x1, [fp, #-0x28]
    // 0x1325fb0: r0 = ReturnOrderRequest()
    //     0x1325fb0: bl              #0x132cd5c  ; AllocateReturnOrderRequestStub -> ReturnOrderRequest (size=0x18)
    // 0x1325fb4: mov             x2, x0
    // 0x1325fb8: ldur            x0, [fp, #-0x10]
    // 0x1325fbc: stur            x2, [fp, #-0x30]
    // 0x1325fc0: StoreField: r2->field_7 = r0
    //     0x1325fc0: stur            w0, [x2, #7]
    // 0x1325fc4: ldur            x0, [fp, #-0x18]
    // 0x1325fc8: StoreField: r2->field_b = r0
    //     0x1325fc8: stur            w0, [x2, #0xb]
    // 0x1325fcc: ldur            x0, [fp, #-0x20]
    // 0x1325fd0: StoreField: r2->field_f = r0
    //     0x1325fd0: stur            w0, [x2, #0xf]
    // 0x1325fd4: ldur            x0, [fp, #-0x28]
    // 0x1325fd8: StoreField: r2->field_13 = r0
    //     0x1325fd8: stur            w0, [x2, #0x13]
    // 0x1325fdc: ldur            x0, [fp, #-8]
    // 0x1325fe0: LoadField: r1 = r0->field_f
    //     0x1325fe0: ldur            w1, [x0, #0xf]
    // 0x1325fe4: DecompressPointer r1
    //     0x1325fe4: add             x1, x1, HEAP, lsl #32
    // 0x1325fe8: r0 = controller()
    //     0x1325fe8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1325fec: mov             x1, x0
    // 0x1325ff0: ldur            x2, [fp, #-0x30]
    // 0x1325ff4: r0 = returnOrder()
    //     0x1325ff4: bl              #0x132ad00  ; [package:customer_app/app/presentation/controllers/post_order/return_order_controller.dart] ReturnOrderController::returnOrder
    // 0x1325ff8: b               #0x1326014
    // 0x1325ffc: ldur            x0, [fp, #-8]
    // 0x1326000: LoadField: r1 = r0->field_f
    //     0x1326000: ldur            w1, [x0, #0xf]
    // 0x1326004: DecompressPointer r1
    //     0x1326004: add             x1, x1, HEAP, lsl #32
    // 0x1326008: r0 = controller()
    //     0x1326008: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x132600c: mov             x1, x0
    // 0x1326010: r0 = exchangeReturnOrder()
    //     0x1326010: bl              #0x13275f4  ; [package:customer_app/app/presentation/controllers/post_order/return_order_controller.dart] ReturnOrderController::exchangeReturnOrder
    // 0x1326014: r0 = Null
    //     0x1326014: mov             x0, NULL
    // 0x1326018: LeaveFrame
    //     0x1326018: mov             SP, fp
    //     0x132601c: ldp             fp, lr, [SP], #0x10
    // 0x1326020: ret
    //     0x1326020: ret             
    // 0x1326024: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1326024: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1326028: b               #0x13258b4
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x132602c, size: 0xff0
    // 0x132602c: EnterFrame
    //     0x132602c: stp             fp, lr, [SP, #-0x10]!
    //     0x1326030: mov             fp, SP
    // 0x1326034: AllocStack(0x68)
    //     0x1326034: sub             SP, SP, #0x68
    // 0x1326038: SetupParameters()
    //     0x1326038: ldr             x0, [fp, #0x10]
    //     0x132603c: ldur            w2, [x0, #0x17]
    //     0x1326040: add             x2, x2, HEAP, lsl #32
    //     0x1326044: stur            x2, [fp, #-8]
    // 0x1326048: CheckStackOverflow
    //     0x1326048: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x132604c: cmp             SP, x16
    //     0x1326050: b.ls            #0x1327004
    // 0x1326054: LoadField: r1 = r2->field_f
    //     0x1326054: ldur            w1, [x2, #0xf]
    // 0x1326058: DecompressPointer r1
    //     0x1326058: add             x1, x1, HEAP, lsl #32
    // 0x132605c: r0 = controller()
    //     0x132605c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1326060: LoadField: r1 = r0->field_57
    //     0x1326060: ldur            w1, [x0, #0x57]
    // 0x1326064: DecompressPointer r1
    //     0x1326064: add             x1, x1, HEAP, lsl #32
    // 0x1326068: r0 = value()
    //     0x1326068: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x132606c: cmp             w0, NULL
    // 0x1326070: b.ne            #0x132607c
    // 0x1326074: r0 = Null
    //     0x1326074: mov             x0, NULL
    // 0x1326078: b               #0x1326088
    // 0x132607c: LoadField: r1 = r0->field_23
    //     0x132607c: ldur            w1, [x0, #0x23]
    // 0x1326080: DecompressPointer r1
    //     0x1326080: add             x1, x1, HEAP, lsl #32
    // 0x1326084: mov             x0, x1
    // 0x1326088: cmp             w0, NULL
    // 0x132608c: b.eq            #0x13260cc
    // 0x1326090: tbnz            w0, #4, #0x13260cc
    // 0x1326094: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1326094: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1326098: ldr             x0, [x0, #0x1c80]
    //     0x132609c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13260a0: cmp             w0, w16
    //     0x13260a4: b.ne            #0x13260b0
    //     0x13260a8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13260ac: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13260b0: r0 = GetNavigation.size()
    //     0x13260b0: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x13260b4: LoadField: d0 = r0->field_f
    //     0x13260b4: ldur            d0, [x0, #0xf]
    // 0x13260b8: d1 = 0.080000
    //     0x13260b8: add             x17, PP, #0x27, lsl #12  ; [pp+0x27798] IMM: double(0.08) from 0x3fb47ae147ae147b
    //     0x13260bc: ldr             d1, [x17, #0x798]
    // 0x13260c0: fmul            d2, d0, d1
    // 0x13260c4: mov             v0.16b, v2.16b
    // 0x13260c8: b               #0x1326100
    // 0x13260cc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13260cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13260d0: ldr             x0, [x0, #0x1c80]
    //     0x13260d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13260d8: cmp             w0, w16
    //     0x13260dc: b.ne            #0x13260e8
    //     0x13260e0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13260e4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13260e8: r0 = GetNavigation.size()
    //     0x13260e8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x13260ec: LoadField: d0 = r0->field_f
    //     0x13260ec: ldur            d0, [x0, #0xf]
    // 0x13260f0: d1 = 0.170000
    //     0x13260f0: add             x17, PP, #0x33, lsl #12  ; [pp+0x33f10] IMM: double(0.17) from 0x3fc5c28f5c28f5c3
    //     0x13260f4: ldr             d1, [x17, #0xf10]
    // 0x13260f8: fmul            d2, d0, d1
    // 0x13260fc: mov             v0.16b, v2.16b
    // 0x1326100: ldur            x0, [fp, #-8]
    // 0x1326104: stur            d0, [fp, #-0x50]
    // 0x1326108: r1 = _ConstMap len:11
    //     0x1326108: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c28] Map<int, List<BoxShadow>>(11)
    //     0x132610c: ldr             x1, [x1, #0xc28]
    // 0x1326110: r2 = 8
    //     0x1326110: movz            x2, #0x8
    // 0x1326114: r0 = []()
    //     0x1326114: bl              #0x16a3e7c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x1326118: stur            x0, [fp, #-0x10]
    // 0x132611c: r0 = BoxDecoration()
    //     0x132611c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1326120: mov             x2, x0
    // 0x1326124: r0 = Instance_Color
    //     0x1326124: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1326128: stur            x2, [fp, #-0x18]
    // 0x132612c: StoreField: r2->field_7 = r0
    //     0x132612c: stur            w0, [x2, #7]
    // 0x1326130: ldur            x0, [fp, #-0x10]
    // 0x1326134: ArrayStore: r2[0] = r0  ; List_4
    //     0x1326134: stur            w0, [x2, #0x17]
    // 0x1326138: r0 = Instance_BoxShape
    //     0x1326138: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x132613c: ldr             x0, [x0, #0x80]
    // 0x1326140: StoreField: r2->field_23 = r0
    //     0x1326140: stur            w0, [x2, #0x23]
    // 0x1326144: ldur            x3, [fp, #-8]
    // 0x1326148: LoadField: r1 = r3->field_f
    //     0x1326148: ldur            w1, [x3, #0xf]
    // 0x132614c: DecompressPointer r1
    //     0x132614c: add             x1, x1, HEAP, lsl #32
    // 0x1326150: r0 = controller()
    //     0x1326150: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1326154: LoadField: r1 = r0->field_57
    //     0x1326154: ldur            w1, [x0, #0x57]
    // 0x1326158: DecompressPointer r1
    //     0x1326158: add             x1, x1, HEAP, lsl #32
    // 0x132615c: r0 = value()
    //     0x132615c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1326160: cmp             w0, NULL
    // 0x1326164: b.ne            #0x1326170
    // 0x1326168: r0 = Null
    //     0x1326168: mov             x0, NULL
    // 0x132616c: b               #0x132617c
    // 0x1326170: LoadField: r1 = r0->field_23
    //     0x1326170: ldur            w1, [x0, #0x23]
    // 0x1326174: DecompressPointer r1
    //     0x1326174: add             x1, x1, HEAP, lsl #32
    // 0x1326178: mov             x0, x1
    // 0x132617c: cmp             w0, NULL
    // 0x1326180: b.ne            #0x13261c4
    // 0x1326184: ldur            x0, [fp, #-8]
    // 0x1326188: r4 = true
    //     0x1326188: add             x4, NULL, #0x20  ; true
    // 0x132618c: r3 = false
    //     0x132618c: add             x3, NULL, #0x30  ; false
    // 0x1326190: r7 = Instance_MainAxisSize
    //     0x1326190: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1326194: ldr             x7, [x7, #0xa10]
    // 0x1326198: r5 = Instance_FlexFit
    //     0x1326198: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x132619c: ldr             x5, [x5, #0xe08]
    // 0x13261a0: r6 = Instance_Axis
    //     0x13261a0: ldr             x6, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13261a4: r9 = Instance_VerticalDirection
    //     0x13261a4: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13261a8: ldr             x9, [x9, #0xa20]
    // 0x13261ac: r8 = Instance_CrossAxisAlignment
    //     0x13261ac: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13261b0: ldr             x8, [x8, #0xa18]
    // 0x13261b4: r10 = Instance_Clip
    //     0x13261b4: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13261b8: ldr             x10, [x10, #0x38]
    // 0x13261bc: r2 = 1
    //     0x13261bc: movz            x2, #0x1
    // 0x13261c0: b               #0x13264d4
    // 0x13261c4: tbnz            w0, #4, #0x1326498
    // 0x13261c8: ldur            x2, [fp, #-8]
    // 0x13261cc: LoadField: r1 = r2->field_f
    //     0x13261cc: ldur            w1, [x2, #0xf]
    // 0x13261d0: DecompressPointer r1
    //     0x13261d0: add             x1, x1, HEAP, lsl #32
    // 0x13261d4: r0 = controller()
    //     0x13261d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13261d8: LoadField: r1 = r0->field_8f
    //     0x13261d8: ldur            w1, [x0, #0x8f]
    // 0x13261dc: DecompressPointer r1
    //     0x13261dc: add             x1, x1, HEAP, lsl #32
    // 0x13261e0: r0 = value()
    //     0x13261e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13261e4: tbnz            w0, #4, #0x13261fc
    // 0x13261e8: ldur            x2, [fp, #-8]
    // 0x13261ec: r1 = Function '<anonymous closure>':.
    //     0x13261ec: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f18] AnonymousClosure: (0x13270ec), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_view.dart] ReturnOrderView::bottomNavigationBar (0x1369f9c)
    //     0x13261f0: ldr             x1, [x1, #0xf18]
    // 0x13261f4: r0 = AllocateClosure()
    //     0x13261f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13261f8: b               #0x1326200
    // 0x13261fc: r0 = Null
    //     0x13261fc: mov             x0, NULL
    // 0x1326200: ldur            x2, [fp, #-8]
    // 0x1326204: stur            x0, [fp, #-0x10]
    // 0x1326208: r16 = <EdgeInsets>
    //     0x1326208: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x132620c: ldr             x16, [x16, #0xda0]
    // 0x1326210: r30 = Instance_EdgeInsets
    //     0x1326210: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1326214: ldr             lr, [lr, #0x1f0]
    // 0x1326218: stp             lr, x16, [SP]
    // 0x132621c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x132621c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1326220: r0 = all()
    //     0x1326220: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1326224: ldur            x2, [fp, #-8]
    // 0x1326228: stur            x0, [fp, #-0x20]
    // 0x132622c: LoadField: r1 = r2->field_13
    //     0x132622c: ldur            w1, [x2, #0x13]
    // 0x1326230: DecompressPointer r1
    //     0x1326230: add             x1, x1, HEAP, lsl #32
    // 0x1326234: r0 = of()
    //     0x1326234: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1326238: LoadField: r1 = r0->field_5b
    //     0x1326238: ldur            w1, [x0, #0x5b]
    // 0x132623c: DecompressPointer r1
    //     0x132623c: add             x1, x1, HEAP, lsl #32
    // 0x1326240: r16 = <Color>
    //     0x1326240: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1326244: ldr             x16, [x16, #0xf80]
    // 0x1326248: stp             x1, x16, [SP]
    // 0x132624c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x132624c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1326250: r0 = all()
    //     0x1326250: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1326254: ldur            x2, [fp, #-8]
    // 0x1326258: stur            x0, [fp, #-0x28]
    // 0x132625c: LoadField: r1 = r2->field_f
    //     0x132625c: ldur            w1, [x2, #0xf]
    // 0x1326260: DecompressPointer r1
    //     0x1326260: add             x1, x1, HEAP, lsl #32
    // 0x1326264: r0 = controller()
    //     0x1326264: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1326268: LoadField: r1 = r0->field_8f
    //     0x1326268: ldur            w1, [x0, #0x8f]
    // 0x132626c: DecompressPointer r1
    //     0x132626c: add             x1, x1, HEAP, lsl #32
    // 0x1326270: r0 = value()
    //     0x1326270: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1326274: tbnz            w0, #4, #0x1326298
    // 0x1326278: ldur            x2, [fp, #-8]
    // 0x132627c: LoadField: r1 = r2->field_13
    //     0x132627c: ldur            w1, [x2, #0x13]
    // 0x1326280: DecompressPointer r1
    //     0x1326280: add             x1, x1, HEAP, lsl #32
    // 0x1326284: r0 = of()
    //     0x1326284: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1326288: LoadField: r1 = r0->field_5b
    //     0x1326288: ldur            w1, [x0, #0x5b]
    // 0x132628c: DecompressPointer r1
    //     0x132628c: add             x1, x1, HEAP, lsl #32
    // 0x1326290: mov             x4, x1
    // 0x1326294: b               #0x13262a8
    // 0x1326298: r1 = Instance_Color
    //     0x1326298: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x132629c: d0 = 0.400000
    //     0x132629c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x13262a0: r0 = withOpacity()
    //     0x13262a0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13262a4: mov             x4, x0
    // 0x13262a8: ldur            x2, [fp, #-8]
    // 0x13262ac: ldur            x3, [fp, #-0x10]
    // 0x13262b0: ldur            x1, [fp, #-0x20]
    // 0x13262b4: ldur            x0, [fp, #-0x28]
    // 0x13262b8: r16 = <Color>
    //     0x13262b8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13262bc: ldr             x16, [x16, #0xf80]
    // 0x13262c0: stp             x4, x16, [SP]
    // 0x13262c4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13262c4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13262c8: r0 = all()
    //     0x13262c8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13262cc: stur            x0, [fp, #-0x30]
    // 0x13262d0: r16 = <RoundedRectangleBorder>
    //     0x13262d0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x13262d4: ldr             x16, [x16, #0xf78]
    // 0x13262d8: r30 = Instance_RoundedRectangleBorder
    //     0x13262d8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x13262dc: ldr             lr, [lr, #0xd68]
    // 0x13262e0: stp             lr, x16, [SP]
    // 0x13262e4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13262e4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13262e8: r0 = all()
    //     0x13262e8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13262ec: stur            x0, [fp, #-0x38]
    // 0x13262f0: r0 = ButtonStyle()
    //     0x13262f0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x13262f4: mov             x2, x0
    // 0x13262f8: ldur            x0, [fp, #-0x30]
    // 0x13262fc: stur            x2, [fp, #-0x40]
    // 0x1326300: StoreField: r2->field_b = r0
    //     0x1326300: stur            w0, [x2, #0xb]
    // 0x1326304: ldur            x0, [fp, #-0x28]
    // 0x1326308: StoreField: r2->field_f = r0
    //     0x1326308: stur            w0, [x2, #0xf]
    // 0x132630c: ldur            x0, [fp, #-0x20]
    // 0x1326310: StoreField: r2->field_23 = r0
    //     0x1326310: stur            w0, [x2, #0x23]
    // 0x1326314: ldur            x0, [fp, #-0x38]
    // 0x1326318: StoreField: r2->field_43 = r0
    //     0x1326318: stur            w0, [x2, #0x43]
    // 0x132631c: ldur            x0, [fp, #-8]
    // 0x1326320: LoadField: r1 = r0->field_13
    //     0x1326320: ldur            w1, [x0, #0x13]
    // 0x1326324: DecompressPointer r1
    //     0x1326324: add             x1, x1, HEAP, lsl #32
    // 0x1326328: r0 = of()
    //     0x1326328: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x132632c: LoadField: r1 = r0->field_87
    //     0x132632c: ldur            w1, [x0, #0x87]
    // 0x1326330: DecompressPointer r1
    //     0x1326330: add             x1, x1, HEAP, lsl #32
    // 0x1326334: LoadField: r0 = r1->field_7
    //     0x1326334: ldur            w0, [x1, #7]
    // 0x1326338: DecompressPointer r0
    //     0x1326338: add             x0, x0, HEAP, lsl #32
    // 0x132633c: r16 = 14.000000
    //     0x132633c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1326340: ldr             x16, [x16, #0x1d8]
    // 0x1326344: r30 = Instance_Color
    //     0x1326344: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1326348: stp             lr, x16, [SP]
    // 0x132634c: mov             x1, x0
    // 0x1326350: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1326350: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1326354: ldr             x4, [x4, #0xaa0]
    // 0x1326358: r0 = copyWith()
    //     0x1326358: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x132635c: stur            x0, [fp, #-0x20]
    // 0x1326360: r0 = Text()
    //     0x1326360: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1326364: mov             x1, x0
    // 0x1326368: r0 = "NEXT"
    //     0x1326368: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f20] "NEXT"
    //     0x132636c: ldr             x0, [x0, #0xf20]
    // 0x1326370: stur            x1, [fp, #-0x28]
    // 0x1326374: StoreField: r1->field_b = r0
    //     0x1326374: stur            w0, [x1, #0xb]
    // 0x1326378: ldur            x0, [fp, #-0x20]
    // 0x132637c: StoreField: r1->field_13 = r0
    //     0x132637c: stur            w0, [x1, #0x13]
    // 0x1326380: r0 = TextButton()
    //     0x1326380: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1326384: mov             x2, x0
    // 0x1326388: ldur            x0, [fp, #-0x10]
    // 0x132638c: stur            x2, [fp, #-0x20]
    // 0x1326390: StoreField: r2->field_b = r0
    //     0x1326390: stur            w0, [x2, #0xb]
    // 0x1326394: ldur            x0, [fp, #-0x40]
    // 0x1326398: StoreField: r2->field_1b = r0
    //     0x1326398: stur            w0, [x2, #0x1b]
    // 0x132639c: r3 = false
    //     0x132639c: add             x3, NULL, #0x30  ; false
    // 0x13263a0: StoreField: r2->field_27 = r3
    //     0x13263a0: stur            w3, [x2, #0x27]
    // 0x13263a4: r4 = true
    //     0x13263a4: add             x4, NULL, #0x20  ; true
    // 0x13263a8: StoreField: r2->field_2f = r4
    //     0x13263a8: stur            w4, [x2, #0x2f]
    // 0x13263ac: ldur            x0, [fp, #-0x28]
    // 0x13263b0: StoreField: r2->field_37 = r0
    //     0x13263b0: stur            w0, [x2, #0x37]
    // 0x13263b4: r1 = <FlexParentData>
    //     0x13263b4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x13263b8: ldr             x1, [x1, #0xe00]
    // 0x13263bc: r0 = Expanded()
    //     0x13263bc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x13263c0: r2 = 1
    //     0x13263c0: movz            x2, #0x1
    // 0x13263c4: stur            x0, [fp, #-0x10]
    // 0x13263c8: StoreField: r0->field_13 = r2
    //     0x13263c8: stur            x2, [x0, #0x13]
    // 0x13263cc: r5 = Instance_FlexFit
    //     0x13263cc: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x13263d0: ldr             x5, [x5, #0xe08]
    // 0x13263d4: StoreField: r0->field_1b = r5
    //     0x13263d4: stur            w5, [x0, #0x1b]
    // 0x13263d8: ldur            x1, [fp, #-0x20]
    // 0x13263dc: StoreField: r0->field_b = r1
    //     0x13263dc: stur            w1, [x0, #0xb]
    // 0x13263e0: r1 = Null
    //     0x13263e0: mov             x1, NULL
    // 0x13263e4: r2 = 2
    //     0x13263e4: movz            x2, #0x2
    // 0x13263e8: r0 = AllocateArray()
    //     0x13263e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13263ec: mov             x2, x0
    // 0x13263f0: ldur            x0, [fp, #-0x10]
    // 0x13263f4: stur            x2, [fp, #-0x20]
    // 0x13263f8: StoreField: r2->field_f = r0
    //     0x13263f8: stur            w0, [x2, #0xf]
    // 0x13263fc: r1 = <Widget>
    //     0x13263fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1326400: r0 = AllocateGrowableArray()
    //     0x1326400: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1326404: mov             x1, x0
    // 0x1326408: ldur            x0, [fp, #-0x20]
    // 0x132640c: stur            x1, [fp, #-0x10]
    // 0x1326410: StoreField: r1->field_f = r0
    //     0x1326410: stur            w0, [x1, #0xf]
    // 0x1326414: r0 = 2
    //     0x1326414: movz            x0, #0x2
    // 0x1326418: StoreField: r1->field_b = r0
    //     0x1326418: stur            w0, [x1, #0xb]
    // 0x132641c: r0 = Row()
    //     0x132641c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1326420: r6 = Instance_Axis
    //     0x1326420: ldr             x6, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1326424: stur            x0, [fp, #-0x20]
    // 0x1326428: StoreField: r0->field_f = r6
    //     0x1326428: stur            w6, [x0, #0xf]
    // 0x132642c: r1 = Instance_MainAxisAlignment
    //     0x132642c: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f28] Obj!MainAxisAlignment@d734a1
    //     0x1326430: ldr             x1, [x1, #0xf28]
    // 0x1326434: StoreField: r0->field_13 = r1
    //     0x1326434: stur            w1, [x0, #0x13]
    // 0x1326438: r7 = Instance_MainAxisSize
    //     0x1326438: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x132643c: ldr             x7, [x7, #0xa10]
    // 0x1326440: ArrayStore: r0[0] = r7  ; List_4
    //     0x1326440: stur            w7, [x0, #0x17]
    // 0x1326444: r8 = Instance_CrossAxisAlignment
    //     0x1326444: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1326448: ldr             x8, [x8, #0xa18]
    // 0x132644c: StoreField: r0->field_1b = r8
    //     0x132644c: stur            w8, [x0, #0x1b]
    // 0x1326450: r9 = Instance_VerticalDirection
    //     0x1326450: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1326454: ldr             x9, [x9, #0xa20]
    // 0x1326458: StoreField: r0->field_23 = r9
    //     0x1326458: stur            w9, [x0, #0x23]
    // 0x132645c: r10 = Instance_Clip
    //     0x132645c: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1326460: ldr             x10, [x10, #0x38]
    // 0x1326464: StoreField: r0->field_2b = r10
    //     0x1326464: stur            w10, [x0, #0x2b]
    // 0x1326468: StoreField: r0->field_2f = rZR
    //     0x1326468: stur            xzr, [x0, #0x2f]
    // 0x132646c: ldur            x1, [fp, #-0x10]
    // 0x1326470: StoreField: r0->field_b = r1
    //     0x1326470: stur            w1, [x0, #0xb]
    // 0x1326474: r0 = Padding()
    //     0x1326474: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1326478: mov             x1, x0
    // 0x132647c: r0 = Instance_EdgeInsets
    //     0x132647c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0x1326480: ldr             x0, [x0, #0xf30]
    // 0x1326484: StoreField: r1->field_f = r0
    //     0x1326484: stur            w0, [x1, #0xf]
    // 0x1326488: ldur            x0, [fp, #-0x20]
    // 0x132648c: StoreField: r1->field_b = r0
    //     0x132648c: stur            w0, [x1, #0xb]
    // 0x1326490: mov             x0, x1
    // 0x1326494: b               #0x1326f88
    // 0x1326498: ldur            x0, [fp, #-8]
    // 0x132649c: r4 = true
    //     0x132649c: add             x4, NULL, #0x20  ; true
    // 0x13264a0: r3 = false
    //     0x13264a0: add             x3, NULL, #0x30  ; false
    // 0x13264a4: r7 = Instance_MainAxisSize
    //     0x13264a4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13264a8: ldr             x7, [x7, #0xa10]
    // 0x13264ac: r5 = Instance_FlexFit
    //     0x13264ac: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x13264b0: ldr             x5, [x5, #0xe08]
    // 0x13264b4: r6 = Instance_Axis
    //     0x13264b4: ldr             x6, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13264b8: r9 = Instance_VerticalDirection
    //     0x13264b8: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13264bc: ldr             x9, [x9, #0xa20]
    // 0x13264c0: r8 = Instance_CrossAxisAlignment
    //     0x13264c0: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13264c4: ldr             x8, [x8, #0xa18]
    // 0x13264c8: r10 = Instance_Clip
    //     0x13264c8: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13264cc: ldr             x10, [x10, #0x38]
    // 0x13264d0: r2 = 1
    //     0x13264d0: movz            x2, #0x1
    // 0x13264d4: LoadField: r1 = r0->field_f
    //     0x13264d4: ldur            w1, [x0, #0xf]
    // 0x13264d8: DecompressPointer r1
    //     0x13264d8: add             x1, x1, HEAP, lsl #32
    // 0x13264dc: r0 = controller()
    //     0x13264dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13264e0: LoadField: r1 = r0->field_6b
    //     0x13264e0: ldur            w1, [x0, #0x6b]
    // 0x13264e4: DecompressPointer r1
    //     0x13264e4: add             x1, x1, HEAP, lsl #32
    // 0x13264e8: r0 = value()
    //     0x13264e8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13264ec: cmp             w0, NULL
    // 0x13264f0: b.eq            #0x13264f8
    // 0x13264f4: tbz             w0, #4, #0x1326504
    // 0x13264f8: r0 = Instance_IconData
    //     0x13264f8: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c30] Obj!IconData@d55461
    //     0x13264fc: ldr             x0, [x0, #0xc30]
    // 0x1326500: b               #0x132650c
    // 0x1326504: r0 = Instance_IconData
    //     0x1326504: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c38] Obj!IconData@d55481
    //     0x1326508: ldr             x0, [x0, #0xc38]
    // 0x132650c: ldur            x2, [fp, #-8]
    // 0x1326510: stur            x0, [fp, #-0x10]
    // 0x1326514: r0 = Icon()
    //     0x1326514: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x1326518: mov             x1, x0
    // 0x132651c: ldur            x0, [fp, #-0x10]
    // 0x1326520: stur            x1, [fp, #-0x20]
    // 0x1326524: StoreField: r1->field_b = r0
    //     0x1326524: stur            w0, [x1, #0xb]
    // 0x1326528: r0 = GetNavigation.size()
    //     0x1326528: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x132652c: LoadField: d0 = r0->field_7
    //     0x132652c: ldur            d0, [x0, #7]
    // 0x1326530: d1 = 0.800000
    //     0x1326530: add             x17, PP, #0x32, lsl #12  ; [pp+0x32b28] IMM: double(0.8) from 0x3fe999999999999a
    //     0x1326534: ldr             d1, [x17, #0xb28]
    // 0x1326538: fmul            d2, d0, d1
    // 0x132653c: stur            d2, [fp, #-0x58]
    // 0x1326540: r0 = BoxConstraints()
    //     0x1326540: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x1326544: stur            x0, [fp, #-0x10]
    // 0x1326548: StoreField: r0->field_7 = rZR
    //     0x1326548: stur            xzr, [x0, #7]
    // 0x132654c: ldur            d0, [fp, #-0x58]
    // 0x1326550: StoreField: r0->field_f = d0
    //     0x1326550: stur            d0, [x0, #0xf]
    // 0x1326554: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1326554: stur            xzr, [x0, #0x17]
    // 0x1326558: d0 = inf
    //     0x1326558: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x132655c: StoreField: r0->field_1f = d0
    //     0x132655c: stur            d0, [x0, #0x1f]
    // 0x1326560: ldur            x2, [fp, #-8]
    // 0x1326564: LoadField: r1 = r2->field_f
    //     0x1326564: ldur            w1, [x2, #0xf]
    // 0x1326568: DecompressPointer r1
    //     0x1326568: add             x1, x1, HEAP, lsl #32
    // 0x132656c: r0 = controller()
    //     0x132656c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1326570: LoadField: r1 = r0->field_57
    //     0x1326570: ldur            w1, [x0, #0x57]
    // 0x1326574: DecompressPointer r1
    //     0x1326574: add             x1, x1, HEAP, lsl #32
    // 0x1326578: r0 = value()
    //     0x1326578: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x132657c: cmp             w0, NULL
    // 0x1326580: b.ne            #0x132658c
    // 0x1326584: r0 = Null
    //     0x1326584: mov             x0, NULL
    // 0x1326588: b               #0x1326598
    // 0x132658c: LoadField: r1 = r0->field_1f
    //     0x132658c: ldur            w1, [x0, #0x1f]
    // 0x1326590: DecompressPointer r1
    //     0x1326590: add             x1, x1, HEAP, lsl #32
    // 0x1326594: mov             x0, x1
    // 0x1326598: cmp             w0, NULL
    // 0x132659c: b.ne            #0x13265a8
    // 0x13265a0: r4 = ""
    //     0x13265a0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13265a4: b               #0x13265ac
    // 0x13265a8: mov             x4, x0
    // 0x13265ac: ldur            x2, [fp, #-8]
    // 0x13265b0: ldur            x3, [fp, #-0x20]
    // 0x13265b4: ldur            x0, [fp, #-0x10]
    // 0x13265b8: stur            x4, [fp, #-0x28]
    // 0x13265bc: LoadField: r1 = r2->field_13
    //     0x13265bc: ldur            w1, [x2, #0x13]
    // 0x13265c0: DecompressPointer r1
    //     0x13265c0: add             x1, x1, HEAP, lsl #32
    // 0x13265c4: r0 = of()
    //     0x13265c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13265c8: LoadField: r1 = r0->field_87
    //     0x13265c8: ldur            w1, [x0, #0x87]
    // 0x13265cc: DecompressPointer r1
    //     0x13265cc: add             x1, x1, HEAP, lsl #32
    // 0x13265d0: LoadField: r0 = r1->field_2b
    //     0x13265d0: ldur            w0, [x1, #0x2b]
    // 0x13265d4: DecompressPointer r0
    //     0x13265d4: add             x0, x0, HEAP, lsl #32
    // 0x13265d8: r16 = 10.000000
    //     0x13265d8: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x13265dc: r30 = Instance_Color
    //     0x13265dc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13265e0: stp             lr, x16, [SP]
    // 0x13265e4: mov             x1, x0
    // 0x13265e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13265e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13265ec: ldr             x4, [x4, #0xaa0]
    // 0x13265f0: r0 = copyWith()
    //     0x13265f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13265f4: stur            x0, [fp, #-0x30]
    // 0x13265f8: r0 = Text()
    //     0x13265f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13265fc: mov             x1, x0
    // 0x1326600: ldur            x0, [fp, #-0x28]
    // 0x1326604: stur            x1, [fp, #-0x38]
    // 0x1326608: StoreField: r1->field_b = r0
    //     0x1326608: stur            w0, [x1, #0xb]
    // 0x132660c: ldur            x0, [fp, #-0x30]
    // 0x1326610: StoreField: r1->field_13 = r0
    //     0x1326610: stur            w0, [x1, #0x13]
    // 0x1326614: r0 = ConstrainedBox()
    //     0x1326614: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x1326618: mov             x1, x0
    // 0x132661c: ldur            x0, [fp, #-0x10]
    // 0x1326620: stur            x1, [fp, #-0x28]
    // 0x1326624: StoreField: r1->field_f = r0
    //     0x1326624: stur            w0, [x1, #0xf]
    // 0x1326628: ldur            x0, [fp, #-0x38]
    // 0x132662c: StoreField: r1->field_b = r0
    //     0x132662c: stur            w0, [x1, #0xb]
    // 0x1326630: r0 = Padding()
    //     0x1326630: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1326634: mov             x3, x0
    // 0x1326638: r0 = Instance_EdgeInsets
    //     0x1326638: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0x132663c: ldr             x0, [x0, #0xc40]
    // 0x1326640: stur            x3, [fp, #-0x10]
    // 0x1326644: StoreField: r3->field_f = r0
    //     0x1326644: stur            w0, [x3, #0xf]
    // 0x1326648: ldur            x0, [fp, #-0x28]
    // 0x132664c: StoreField: r3->field_b = r0
    //     0x132664c: stur            w0, [x3, #0xb]
    // 0x1326650: r1 = Null
    //     0x1326650: mov             x1, NULL
    // 0x1326654: r2 = 4
    //     0x1326654: movz            x2, #0x4
    // 0x1326658: r0 = AllocateArray()
    //     0x1326658: bl              #0x16f7198  ; AllocateArrayStub
    // 0x132665c: mov             x2, x0
    // 0x1326660: ldur            x0, [fp, #-0x20]
    // 0x1326664: stur            x2, [fp, #-0x28]
    // 0x1326668: StoreField: r2->field_f = r0
    //     0x1326668: stur            w0, [x2, #0xf]
    // 0x132666c: ldur            x0, [fp, #-0x10]
    // 0x1326670: StoreField: r2->field_13 = r0
    //     0x1326670: stur            w0, [x2, #0x13]
    // 0x1326674: r1 = <Widget>
    //     0x1326674: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1326678: r0 = AllocateGrowableArray()
    //     0x1326678: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x132667c: mov             x1, x0
    // 0x1326680: ldur            x0, [fp, #-0x28]
    // 0x1326684: stur            x1, [fp, #-0x10]
    // 0x1326688: StoreField: r1->field_f = r0
    //     0x1326688: stur            w0, [x1, #0xf]
    // 0x132668c: r2 = 4
    //     0x132668c: movz            x2, #0x4
    // 0x1326690: StoreField: r1->field_b = r2
    //     0x1326690: stur            w2, [x1, #0xb]
    // 0x1326694: r0 = Row()
    //     0x1326694: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1326698: mov             x1, x0
    // 0x132669c: r0 = Instance_Axis
    //     0x132669c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13266a0: stur            x1, [fp, #-0x20]
    // 0x13266a4: StoreField: r1->field_f = r0
    //     0x13266a4: stur            w0, [x1, #0xf]
    // 0x13266a8: r2 = Instance_MainAxisAlignment
    //     0x13266a8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13266ac: ldr             x2, [x2, #0xa08]
    // 0x13266b0: StoreField: r1->field_13 = r2
    //     0x13266b0: stur            w2, [x1, #0x13]
    // 0x13266b4: r3 = Instance_MainAxisSize
    //     0x13266b4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13266b8: ldr             x3, [x3, #0xa10]
    // 0x13266bc: ArrayStore: r1[0] = r3  ; List_4
    //     0x13266bc: stur            w3, [x1, #0x17]
    // 0x13266c0: r4 = Instance_CrossAxisAlignment
    //     0x13266c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13266c4: ldr             x4, [x4, #0xa18]
    // 0x13266c8: StoreField: r1->field_1b = r4
    //     0x13266c8: stur            w4, [x1, #0x1b]
    // 0x13266cc: r5 = Instance_VerticalDirection
    //     0x13266cc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13266d0: ldr             x5, [x5, #0xa20]
    // 0x13266d4: StoreField: r1->field_23 = r5
    //     0x13266d4: stur            w5, [x1, #0x23]
    // 0x13266d8: r6 = Instance_Clip
    //     0x13266d8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13266dc: ldr             x6, [x6, #0x38]
    // 0x13266e0: StoreField: r1->field_2b = r6
    //     0x13266e0: stur            w6, [x1, #0x2b]
    // 0x13266e4: StoreField: r1->field_2f = rZR
    //     0x13266e4: stur            xzr, [x1, #0x2f]
    // 0x13266e8: ldur            x7, [fp, #-0x10]
    // 0x13266ec: StoreField: r1->field_b = r7
    //     0x13266ec: stur            w7, [x1, #0xb]
    // 0x13266f0: r0 = Padding()
    //     0x13266f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13266f4: mov             x1, x0
    // 0x13266f8: r0 = Instance_EdgeInsets
    //     0x13266f8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x13266fc: ldr             x0, [x0, #0x868]
    // 0x1326700: stur            x1, [fp, #-0x10]
    // 0x1326704: StoreField: r1->field_f = r0
    //     0x1326704: stur            w0, [x1, #0xf]
    // 0x1326708: ldur            x0, [fp, #-0x20]
    // 0x132670c: StoreField: r1->field_b = r0
    //     0x132670c: stur            w0, [x1, #0xb]
    // 0x1326710: r0 = InkWell()
    //     0x1326710: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1326714: mov             x3, x0
    // 0x1326718: ldur            x0, [fp, #-0x10]
    // 0x132671c: stur            x3, [fp, #-0x20]
    // 0x1326720: StoreField: r3->field_b = r0
    //     0x1326720: stur            w0, [x3, #0xb]
    // 0x1326724: ldur            x2, [fp, #-8]
    // 0x1326728: r1 = Function '<anonymous closure>':.
    //     0x1326728: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f38] AnonymousClosure: (0x132701c), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_view.dart] ReturnOrderView::bottomNavigationBar (0x1369f9c)
    //     0x132672c: ldr             x1, [x1, #0xf38]
    // 0x1326730: r0 = AllocateClosure()
    //     0x1326730: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1326734: mov             x1, x0
    // 0x1326738: ldur            x0, [fp, #-0x20]
    // 0x132673c: StoreField: r0->field_f = r1
    //     0x132673c: stur            w1, [x0, #0xf]
    // 0x1326740: r2 = true
    //     0x1326740: add             x2, NULL, #0x20  ; true
    // 0x1326744: StoreField: r0->field_43 = r2
    //     0x1326744: stur            w2, [x0, #0x43]
    // 0x1326748: r1 = Instance_BoxShape
    //     0x1326748: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x132674c: ldr             x1, [x1, #0x80]
    // 0x1326750: StoreField: r0->field_47 = r1
    //     0x1326750: stur            w1, [x0, #0x47]
    // 0x1326754: StoreField: r0->field_6f = r2
    //     0x1326754: stur            w2, [x0, #0x6f]
    // 0x1326758: r3 = false
    //     0x1326758: add             x3, NULL, #0x30  ; false
    // 0x132675c: StoreField: r0->field_73 = r3
    //     0x132675c: stur            w3, [x0, #0x73]
    // 0x1326760: StoreField: r0->field_83 = r2
    //     0x1326760: stur            w2, [x0, #0x83]
    // 0x1326764: StoreField: r0->field_7b = r3
    //     0x1326764: stur            w3, [x0, #0x7b]
    // 0x1326768: r1 = <FlexParentData>
    //     0x1326768: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x132676c: ldr             x1, [x1, #0xe00]
    // 0x1326770: r0 = Expanded()
    //     0x1326770: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1326774: mov             x2, x0
    // 0x1326778: r0 = 1
    //     0x1326778: movz            x0, #0x1
    // 0x132677c: stur            x2, [fp, #-0x10]
    // 0x1326780: StoreField: r2->field_13 = r0
    //     0x1326780: stur            x0, [x2, #0x13]
    // 0x1326784: r0 = Instance_FlexFit
    //     0x1326784: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1326788: ldr             x0, [x0, #0xe08]
    // 0x132678c: StoreField: r2->field_1b = r0
    //     0x132678c: stur            w0, [x2, #0x1b]
    // 0x1326790: ldur            x1, [fp, #-0x20]
    // 0x1326794: StoreField: r2->field_b = r1
    //     0x1326794: stur            w1, [x2, #0xb]
    // 0x1326798: ldur            x3, [fp, #-8]
    // 0x132679c: LoadField: r1 = r3->field_f
    //     0x132679c: ldur            w1, [x3, #0xf]
    // 0x13267a0: DecompressPointer r1
    //     0x13267a0: add             x1, x1, HEAP, lsl #32
    // 0x13267a4: r0 = controller()
    //     0x13267a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13267a8: LoadField: r1 = r0->field_57
    //     0x13267a8: ldur            w1, [x0, #0x57]
    // 0x13267ac: DecompressPointer r1
    //     0x13267ac: add             x1, x1, HEAP, lsl #32
    // 0x13267b0: r0 = value()
    //     0x13267b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13267b4: cmp             w0, NULL
    // 0x13267b8: b.ne            #0x13267c4
    // 0x13267bc: r0 = Null
    //     0x13267bc: mov             x0, NULL
    // 0x13267c0: b               #0x13267e4
    // 0x13267c4: LoadField: r1 = r0->field_b
    //     0x13267c4: ldur            w1, [x0, #0xb]
    // 0x13267c8: DecompressPointer r1
    //     0x13267c8: add             x1, x1, HEAP, lsl #32
    // 0x13267cc: cmp             w1, NULL
    // 0x13267d0: b.ne            #0x13267dc
    // 0x13267d4: r0 = Null
    //     0x13267d4: mov             x0, NULL
    // 0x13267d8: b               #0x13267e4
    // 0x13267dc: LoadField: r0 = r1->field_b
    //     0x13267dc: ldur            w0, [x1, #0xb]
    // 0x13267e0: DecompressPointer r0
    //     0x13267e0: add             x0, x0, HEAP, lsl #32
    // 0x13267e4: ldur            x2, [fp, #-8]
    // 0x13267e8: cbnz            w0, #0x13267f4
    // 0x13267ec: r3 = false
    //     0x13267ec: add             x3, NULL, #0x30  ; false
    // 0x13267f0: b               #0x13267f8
    // 0x13267f4: r3 = true
    //     0x13267f4: add             x3, NULL, #0x20  ; true
    // 0x13267f8: stur            x3, [fp, #-0x20]
    // 0x13267fc: LoadField: r1 = r2->field_f
    //     0x13267fc: ldur            w1, [x2, #0xf]
    // 0x1326800: DecompressPointer r1
    //     0x1326800: add             x1, x1, HEAP, lsl #32
    // 0x1326804: r0 = controller()
    //     0x1326804: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1326808: LoadField: r1 = r0->field_57
    //     0x1326808: ldur            w1, [x0, #0x57]
    // 0x132680c: DecompressPointer r1
    //     0x132680c: add             x1, x1, HEAP, lsl #32
    // 0x1326810: r0 = value()
    //     0x1326810: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1326814: cmp             w0, NULL
    // 0x1326818: b.ne            #0x1326824
    // 0x132681c: r0 = Null
    //     0x132681c: mov             x0, NULL
    // 0x1326820: b               #0x1326844
    // 0x1326824: LoadField: r1 = r0->field_33
    //     0x1326824: ldur            w1, [x0, #0x33]
    // 0x1326828: DecompressPointer r1
    //     0x1326828: add             x1, x1, HEAP, lsl #32
    // 0x132682c: cmp             w1, NULL
    // 0x1326830: b.ne            #0x132683c
    // 0x1326834: r0 = Null
    //     0x1326834: mov             x0, NULL
    // 0x1326838: b               #0x1326844
    // 0x132683c: LoadField: r0 = r1->field_2b
    //     0x132683c: ldur            w0, [x1, #0x2b]
    // 0x1326840: DecompressPointer r0
    //     0x1326840: add             x0, x0, HEAP, lsl #32
    // 0x1326844: cmp             w0, NULL
    // 0x1326848: b.ne            #0x1326850
    // 0x132684c: r0 = ""
    //     0x132684c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1326850: ldur            x2, [fp, #-8]
    // 0x1326854: stur            x0, [fp, #-0x28]
    // 0x1326858: LoadField: r1 = r2->field_13
    //     0x1326858: ldur            w1, [x2, #0x13]
    // 0x132685c: DecompressPointer r1
    //     0x132685c: add             x1, x1, HEAP, lsl #32
    // 0x1326860: r0 = of()
    //     0x1326860: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1326864: LoadField: r1 = r0->field_87
    //     0x1326864: ldur            w1, [x0, #0x87]
    // 0x1326868: DecompressPointer r1
    //     0x1326868: add             x1, x1, HEAP, lsl #32
    // 0x132686c: LoadField: r0 = r1->field_2b
    //     0x132686c: ldur            w0, [x1, #0x2b]
    // 0x1326870: DecompressPointer r0
    //     0x1326870: add             x0, x0, HEAP, lsl #32
    // 0x1326874: stur            x0, [fp, #-0x30]
    // 0x1326878: r1 = Instance_Color
    //     0x1326878: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x132687c: d0 = 0.700000
    //     0x132687c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1326880: ldr             d0, [x17, #0xf48]
    // 0x1326884: r0 = withOpacity()
    //     0x1326884: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1326888: r16 = 12.000000
    //     0x1326888: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x132688c: ldr             x16, [x16, #0x9e8]
    // 0x1326890: stp             x0, x16, [SP]
    // 0x1326894: ldur            x1, [fp, #-0x30]
    // 0x1326898: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1326898: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x132689c: ldr             x4, [x4, #0xaa0]
    // 0x13268a0: r0 = copyWith()
    //     0x13268a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13268a4: stur            x0, [fp, #-0x30]
    // 0x13268a8: r0 = Text()
    //     0x13268a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13268ac: mov             x1, x0
    // 0x13268b0: ldur            x0, [fp, #-0x28]
    // 0x13268b4: stur            x1, [fp, #-0x38]
    // 0x13268b8: StoreField: r1->field_b = r0
    //     0x13268b8: stur            w0, [x1, #0xb]
    // 0x13268bc: ldur            x0, [fp, #-0x30]
    // 0x13268c0: StoreField: r1->field_13 = r0
    //     0x13268c0: stur            w0, [x1, #0x13]
    // 0x13268c4: r0 = Padding()
    //     0x13268c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13268c8: mov             x2, x0
    // 0x13268cc: r0 = Instance_EdgeInsets
    //     0x13268cc: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c50] Obj!EdgeInsets@d59541
    //     0x13268d0: ldr             x0, [x0, #0xc50]
    // 0x13268d4: stur            x2, [fp, #-0x28]
    // 0x13268d8: StoreField: r2->field_f = r0
    //     0x13268d8: stur            w0, [x2, #0xf]
    // 0x13268dc: ldur            x0, [fp, #-0x38]
    // 0x13268e0: StoreField: r2->field_b = r0
    //     0x13268e0: stur            w0, [x2, #0xb]
    // 0x13268e4: ldur            x0, [fp, #-8]
    // 0x13268e8: LoadField: r1 = r0->field_f
    //     0x13268e8: ldur            w1, [x0, #0xf]
    // 0x13268ec: DecompressPointer r1
    //     0x13268ec: add             x1, x1, HEAP, lsl #32
    // 0x13268f0: r0 = controller()
    //     0x13268f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13268f4: LoadField: r1 = r0->field_57
    //     0x13268f4: ldur            w1, [x0, #0x57]
    // 0x13268f8: DecompressPointer r1
    //     0x13268f8: add             x1, x1, HEAP, lsl #32
    // 0x13268fc: r0 = value()
    //     0x13268fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1326900: cmp             w0, NULL
    // 0x1326904: b.ne            #0x1326910
    // 0x1326908: r0 = Null
    //     0x1326908: mov             x0, NULL
    // 0x132690c: b               #0x1326930
    // 0x1326910: LoadField: r1 = r0->field_b
    //     0x1326910: ldur            w1, [x0, #0xb]
    // 0x1326914: DecompressPointer r1
    //     0x1326914: add             x1, x1, HEAP, lsl #32
    // 0x1326918: cmp             w1, NULL
    // 0x132691c: b.ne            #0x1326928
    // 0x1326920: r0 = Null
    //     0x1326920: mov             x0, NULL
    // 0x1326924: b               #0x1326930
    // 0x1326928: LoadField: r0 = r1->field_7
    //     0x1326928: ldur            w0, [x1, #7]
    // 0x132692c: DecompressPointer r0
    //     0x132692c: add             x0, x0, HEAP, lsl #32
    // 0x1326930: cmp             w0, NULL
    // 0x1326934: b.ne            #0x1326940
    // 0x1326938: r4 = ""
    //     0x1326938: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x132693c: b               #0x1326944
    // 0x1326940: mov             x4, x0
    // 0x1326944: ldur            x2, [fp, #-8]
    // 0x1326948: ldur            x0, [fp, #-0x28]
    // 0x132694c: ldur            x3, [fp, #-0x20]
    // 0x1326950: stur            x4, [fp, #-0x30]
    // 0x1326954: LoadField: r1 = r2->field_13
    //     0x1326954: ldur            w1, [x2, #0x13]
    // 0x1326958: DecompressPointer r1
    //     0x1326958: add             x1, x1, HEAP, lsl #32
    // 0x132695c: r0 = of()
    //     0x132695c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1326960: LoadField: r1 = r0->field_87
    //     0x1326960: ldur            w1, [x0, #0x87]
    // 0x1326964: DecompressPointer r1
    //     0x1326964: add             x1, x1, HEAP, lsl #32
    // 0x1326968: LoadField: r0 = r1->field_7
    //     0x1326968: ldur            w0, [x1, #7]
    // 0x132696c: DecompressPointer r0
    //     0x132696c: add             x0, x0, HEAP, lsl #32
    // 0x1326970: stur            x0, [fp, #-0x38]
    // 0x1326974: r1 = Instance_Color
    //     0x1326974: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1326978: d0 = 0.700000
    //     0x1326978: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x132697c: ldr             d0, [x17, #0xf48]
    // 0x1326980: r0 = withOpacity()
    //     0x1326980: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1326984: r16 = 16.000000
    //     0x1326984: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1326988: ldr             x16, [x16, #0x188]
    // 0x132698c: stp             x0, x16, [SP]
    // 0x1326990: ldur            x1, [fp, #-0x38]
    // 0x1326994: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1326994: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1326998: ldr             x4, [x4, #0xaa0]
    // 0x132699c: r0 = copyWith()
    //     0x132699c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13269a0: stur            x0, [fp, #-0x38]
    // 0x13269a4: r0 = Text()
    //     0x13269a4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13269a8: mov             x3, x0
    // 0x13269ac: ldur            x0, [fp, #-0x30]
    // 0x13269b0: stur            x3, [fp, #-0x40]
    // 0x13269b4: StoreField: r3->field_b = r0
    //     0x13269b4: stur            w0, [x3, #0xb]
    // 0x13269b8: ldur            x0, [fp, #-0x38]
    // 0x13269bc: StoreField: r3->field_13 = r0
    //     0x13269bc: stur            w0, [x3, #0x13]
    // 0x13269c0: r1 = Null
    //     0x13269c0: mov             x1, NULL
    // 0x13269c4: r2 = 4
    //     0x13269c4: movz            x2, #0x4
    // 0x13269c8: r0 = AllocateArray()
    //     0x13269c8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13269cc: mov             x2, x0
    // 0x13269d0: ldur            x0, [fp, #-0x28]
    // 0x13269d4: stur            x2, [fp, #-0x30]
    // 0x13269d8: StoreField: r2->field_f = r0
    //     0x13269d8: stur            w0, [x2, #0xf]
    // 0x13269dc: ldur            x0, [fp, #-0x40]
    // 0x13269e0: StoreField: r2->field_13 = r0
    //     0x13269e0: stur            w0, [x2, #0x13]
    // 0x13269e4: r1 = <Widget>
    //     0x13269e4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13269e8: r0 = AllocateGrowableArray()
    //     0x13269e8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13269ec: mov             x1, x0
    // 0x13269f0: ldur            x0, [fp, #-0x30]
    // 0x13269f4: stur            x1, [fp, #-0x28]
    // 0x13269f8: StoreField: r1->field_f = r0
    //     0x13269f8: stur            w0, [x1, #0xf]
    // 0x13269fc: r2 = 4
    //     0x13269fc: movz            x2, #0x4
    // 0x1326a00: StoreField: r1->field_b = r2
    //     0x1326a00: stur            w2, [x1, #0xb]
    // 0x1326a04: r0 = Column()
    //     0x1326a04: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1326a08: mov             x1, x0
    // 0x1326a0c: r0 = Instance_Axis
    //     0x1326a0c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1326a10: stur            x1, [fp, #-0x30]
    // 0x1326a14: StoreField: r1->field_f = r0
    //     0x1326a14: stur            w0, [x1, #0xf]
    // 0x1326a18: r2 = Instance_MainAxisAlignment
    //     0x1326a18: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1326a1c: ldr             x2, [x2, #0xa08]
    // 0x1326a20: StoreField: r1->field_13 = r2
    //     0x1326a20: stur            w2, [x1, #0x13]
    // 0x1326a24: r3 = Instance_MainAxisSize
    //     0x1326a24: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1326a28: ldr             x3, [x3, #0xa10]
    // 0x1326a2c: ArrayStore: r1[0] = r3  ; List_4
    //     0x1326a2c: stur            w3, [x1, #0x17]
    // 0x1326a30: r4 = Instance_CrossAxisAlignment
    //     0x1326a30: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1326a34: ldr             x4, [x4, #0x890]
    // 0x1326a38: StoreField: r1->field_1b = r4
    //     0x1326a38: stur            w4, [x1, #0x1b]
    // 0x1326a3c: r4 = Instance_VerticalDirection
    //     0x1326a3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1326a40: ldr             x4, [x4, #0xa20]
    // 0x1326a44: StoreField: r1->field_23 = r4
    //     0x1326a44: stur            w4, [x1, #0x23]
    // 0x1326a48: r5 = Instance_Clip
    //     0x1326a48: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1326a4c: ldr             x5, [x5, #0x38]
    // 0x1326a50: StoreField: r1->field_2b = r5
    //     0x1326a50: stur            w5, [x1, #0x2b]
    // 0x1326a54: StoreField: r1->field_2f = rZR
    //     0x1326a54: stur            xzr, [x1, #0x2f]
    // 0x1326a58: ldur            x6, [fp, #-0x28]
    // 0x1326a5c: StoreField: r1->field_b = r6
    //     0x1326a5c: stur            w6, [x1, #0xb]
    // 0x1326a60: r0 = Visibility()
    //     0x1326a60: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1326a64: mov             x2, x0
    // 0x1326a68: ldur            x0, [fp, #-0x30]
    // 0x1326a6c: stur            x2, [fp, #-0x28]
    // 0x1326a70: StoreField: r2->field_b = r0
    //     0x1326a70: stur            w0, [x2, #0xb]
    // 0x1326a74: r0 = Instance_SizedBox
    //     0x1326a74: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1326a78: StoreField: r2->field_f = r0
    //     0x1326a78: stur            w0, [x2, #0xf]
    // 0x1326a7c: ldur            x0, [fp, #-0x20]
    // 0x1326a80: StoreField: r2->field_13 = r0
    //     0x1326a80: stur            w0, [x2, #0x13]
    // 0x1326a84: r0 = false
    //     0x1326a84: add             x0, NULL, #0x30  ; false
    // 0x1326a88: ArrayStore: r2[0] = r0  ; List_4
    //     0x1326a88: stur            w0, [x2, #0x17]
    // 0x1326a8c: StoreField: r2->field_1b = r0
    //     0x1326a8c: stur            w0, [x2, #0x1b]
    // 0x1326a90: StoreField: r2->field_1f = r0
    //     0x1326a90: stur            w0, [x2, #0x1f]
    // 0x1326a94: StoreField: r2->field_23 = r0
    //     0x1326a94: stur            w0, [x2, #0x23]
    // 0x1326a98: StoreField: r2->field_27 = r0
    //     0x1326a98: stur            w0, [x2, #0x27]
    // 0x1326a9c: StoreField: r2->field_2b = r0
    //     0x1326a9c: stur            w0, [x2, #0x2b]
    // 0x1326aa0: ldur            x3, [fp, #-8]
    // 0x1326aa4: LoadField: r1 = r3->field_f
    //     0x1326aa4: ldur            w1, [x3, #0xf]
    // 0x1326aa8: DecompressPointer r1
    //     0x1326aa8: add             x1, x1, HEAP, lsl #32
    // 0x1326aac: r0 = controller()
    //     0x1326aac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1326ab0: LoadField: r1 = r0->field_57
    //     0x1326ab0: ldur            w1, [x0, #0x57]
    // 0x1326ab4: DecompressPointer r1
    //     0x1326ab4: add             x1, x1, HEAP, lsl #32
    // 0x1326ab8: r0 = value()
    //     0x1326ab8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1326abc: cmp             w0, NULL
    // 0x1326ac0: b.eq            #0x1326ae0
    // 0x1326ac4: LoadField: r1 = r0->field_b
    //     0x1326ac4: ldur            w1, [x0, #0xb]
    // 0x1326ac8: DecompressPointer r1
    //     0x1326ac8: add             x1, x1, HEAP, lsl #32
    // 0x1326acc: cmp             w1, NULL
    // 0x1326ad0: b.eq            #0x1326ae0
    // 0x1326ad4: LoadField: r0 = r1->field_b
    //     0x1326ad4: ldur            w0, [x1, #0xb]
    // 0x1326ad8: DecompressPointer r0
    //     0x1326ad8: add             x0, x0, HEAP, lsl #32
    // 0x1326adc: cbz             w0, #0x1326ae8
    // 0x1326ae0: r0 = 0
    //     0x1326ae0: movz            x0, #0
    // 0x1326ae4: b               #0x1326aec
    // 0x1326ae8: r0 = 1
    //     0x1326ae8: movz            x0, #0x1
    // 0x1326aec: ldur            x2, [fp, #-8]
    // 0x1326af0: stur            x0, [fp, #-0x48]
    // 0x1326af4: r16 = <EdgeInsets>
    //     0x1326af4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1326af8: ldr             x16, [x16, #0xda0]
    // 0x1326afc: r30 = Instance_EdgeInsets
    //     0x1326afc: add             lr, PP, #0x32, lsl #12  ; [pp+0x32c58] Obj!EdgeInsets@d59ab1
    //     0x1326b00: ldr             lr, [lr, #0xc58]
    // 0x1326b04: stp             lr, x16, [SP]
    // 0x1326b08: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1326b08: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1326b0c: r0 = all()
    //     0x1326b0c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1326b10: ldur            x2, [fp, #-8]
    // 0x1326b14: stur            x0, [fp, #-0x20]
    // 0x1326b18: LoadField: r1 = r2->field_f
    //     0x1326b18: ldur            w1, [x2, #0xf]
    // 0x1326b1c: DecompressPointer r1
    //     0x1326b1c: add             x1, x1, HEAP, lsl #32
    // 0x1326b20: r0 = controller()
    //     0x1326b20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1326b24: LoadField: r1 = r0->field_8f
    //     0x1326b24: ldur            w1, [x0, #0x8f]
    // 0x1326b28: DecompressPointer r1
    //     0x1326b28: add             x1, x1, HEAP, lsl #32
    // 0x1326b2c: r0 = value()
    //     0x1326b2c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1326b30: tbnz            w0, #4, #0x1326b78
    // 0x1326b34: ldur            x2, [fp, #-8]
    // 0x1326b38: LoadField: r1 = r2->field_f
    //     0x1326b38: ldur            w1, [x2, #0xf]
    // 0x1326b3c: DecompressPointer r1
    //     0x1326b3c: add             x1, x1, HEAP, lsl #32
    // 0x1326b40: r0 = controller()
    //     0x1326b40: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1326b44: LoadField: r1 = r0->field_6b
    //     0x1326b44: ldur            w1, [x0, #0x6b]
    // 0x1326b48: DecompressPointer r1
    //     0x1326b48: add             x1, x1, HEAP, lsl #32
    // 0x1326b4c: r0 = value()
    //     0x1326b4c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1326b50: cmp             w0, NULL
    // 0x1326b54: b.eq            #0x1326b78
    // 0x1326b58: tbnz            w0, #4, #0x1326b78
    // 0x1326b5c: ldur            x2, [fp, #-8]
    // 0x1326b60: LoadField: r1 = r2->field_13
    //     0x1326b60: ldur            w1, [x2, #0x13]
    // 0x1326b64: DecompressPointer r1
    //     0x1326b64: add             x1, x1, HEAP, lsl #32
    // 0x1326b68: r0 = of()
    //     0x1326b68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1326b6c: LoadField: r1 = r0->field_5b
    //     0x1326b6c: ldur            w1, [x0, #0x5b]
    // 0x1326b70: DecompressPointer r1
    //     0x1326b70: add             x1, x1, HEAP, lsl #32
    // 0x1326b74: b               #0x1326b88
    // 0x1326b78: r1 = Instance_Color
    //     0x1326b78: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1326b7c: d0 = 0.400000
    //     0x1326b7c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1326b80: r0 = withOpacity()
    //     0x1326b80: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1326b84: mov             x1, x0
    // 0x1326b88: ldur            x2, [fp, #-8]
    // 0x1326b8c: ldur            x0, [fp, #-0x20]
    // 0x1326b90: r16 = <Color>
    //     0x1326b90: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1326b94: ldr             x16, [x16, #0xf80]
    // 0x1326b98: stp             x1, x16, [SP]
    // 0x1326b9c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1326b9c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1326ba0: r0 = all()
    //     0x1326ba0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1326ba4: stur            x0, [fp, #-0x30]
    // 0x1326ba8: r16 = <RoundedRectangleBorder>
    //     0x1326ba8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1326bac: ldr             x16, [x16, #0xf78]
    // 0x1326bb0: r30 = Instance_RoundedRectangleBorder
    //     0x1326bb0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x1326bb4: ldr             lr, [lr, #0xd68]
    // 0x1326bb8: stp             lr, x16, [SP]
    // 0x1326bbc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1326bbc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1326bc0: r0 = all()
    //     0x1326bc0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1326bc4: stur            x0, [fp, #-0x38]
    // 0x1326bc8: r0 = ButtonStyle()
    //     0x1326bc8: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1326bcc: mov             x1, x0
    // 0x1326bd0: ldur            x0, [fp, #-0x30]
    // 0x1326bd4: stur            x1, [fp, #-0x40]
    // 0x1326bd8: StoreField: r1->field_b = r0
    //     0x1326bd8: stur            w0, [x1, #0xb]
    // 0x1326bdc: ldur            x0, [fp, #-0x20]
    // 0x1326be0: StoreField: r1->field_23 = r0
    //     0x1326be0: stur            w0, [x1, #0x23]
    // 0x1326be4: ldur            x0, [fp, #-0x38]
    // 0x1326be8: StoreField: r1->field_43 = r0
    //     0x1326be8: stur            w0, [x1, #0x43]
    // 0x1326bec: r0 = TextButtonThemeData()
    //     0x1326bec: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1326bf0: mov             x2, x0
    // 0x1326bf4: ldur            x0, [fp, #-0x40]
    // 0x1326bf8: stur            x2, [fp, #-0x20]
    // 0x1326bfc: StoreField: r2->field_7 = r0
    //     0x1326bfc: stur            w0, [x2, #7]
    // 0x1326c00: ldur            x0, [fp, #-8]
    // 0x1326c04: LoadField: r1 = r0->field_f
    //     0x1326c04: ldur            w1, [x0, #0xf]
    // 0x1326c08: DecompressPointer r1
    //     0x1326c08: add             x1, x1, HEAP, lsl #32
    // 0x1326c0c: r0 = controller()
    //     0x1326c0c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1326c10: LoadField: r1 = r0->field_6b
    //     0x1326c10: ldur            w1, [x0, #0x6b]
    // 0x1326c14: DecompressPointer r1
    //     0x1326c14: add             x1, x1, HEAP, lsl #32
    // 0x1326c18: r0 = value()
    //     0x1326c18: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1326c1c: cmp             w0, NULL
    // 0x1326c20: b.eq            #0x1326cd0
    // 0x1326c24: tbnz            w0, #4, #0x1326cd0
    // 0x1326c28: ldur            x2, [fp, #-8]
    // 0x1326c2c: LoadField: r1 = r2->field_f
    //     0x1326c2c: ldur            w1, [x2, #0xf]
    // 0x1326c30: DecompressPointer r1
    //     0x1326c30: add             x1, x1, HEAP, lsl #32
    // 0x1326c34: r0 = controller()
    //     0x1326c34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1326c38: LoadField: r1 = r0->field_67
    //     0x1326c38: ldur            w1, [x0, #0x67]
    // 0x1326c3c: DecompressPointer r1
    //     0x1326c3c: add             x1, x1, HEAP, lsl #32
    // 0x1326c40: r0 = RxnStringExt.isNotEmpty()
    //     0x1326c40: bl              #0x1325838  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxnStringExt.isNotEmpty
    // 0x1326c44: cmp             w0, NULL
    // 0x1326c48: b.eq            #0x1326cd0
    // 0x1326c4c: tbnz            w0, #4, #0x1326cd0
    // 0x1326c50: ldur            x2, [fp, #-8]
    // 0x1326c54: LoadField: r1 = r2->field_f
    //     0x1326c54: ldur            w1, [x2, #0xf]
    // 0x1326c58: DecompressPointer r1
    //     0x1326c58: add             x1, x1, HEAP, lsl #32
    // 0x1326c5c: r0 = controller()
    //     0x1326c5c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1326c60: LoadField: r1 = r0->field_db
    //     0x1326c60: ldur            w1, [x0, #0xdb]
    // 0x1326c64: DecompressPointer r1
    //     0x1326c64: add             x1, x1, HEAP, lsl #32
    // 0x1326c68: LoadField: r0 = r1->field_27
    //     0x1326c68: ldur            w0, [x1, #0x27]
    // 0x1326c6c: DecompressPointer r0
    //     0x1326c6c: add             x0, x0, HEAP, lsl #32
    // 0x1326c70: LoadField: r1 = r0->field_7
    //     0x1326c70: ldur            w1, [x0, #7]
    // 0x1326c74: DecompressPointer r1
    //     0x1326c74: add             x1, x1, HEAP, lsl #32
    // 0x1326c78: LoadField: r0 = r1->field_7
    //     0x1326c78: ldur            w0, [x1, #7]
    // 0x1326c7c: cbz             w0, #0x1326cd0
    // 0x1326c80: ldur            x2, [fp, #-8]
    // 0x1326c84: LoadField: r1 = r2->field_f
    //     0x1326c84: ldur            w1, [x2, #0xf]
    // 0x1326c88: DecompressPointer r1
    //     0x1326c88: add             x1, x1, HEAP, lsl #32
    // 0x1326c8c: r0 = controller()
    //     0x1326c8c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1326c90: LoadField: r1 = r0->field_db
    //     0x1326c90: ldur            w1, [x0, #0xdb]
    // 0x1326c94: DecompressPointer r1
    //     0x1326c94: add             x1, x1, HEAP, lsl #32
    // 0x1326c98: LoadField: r0 = r1->field_27
    //     0x1326c98: ldur            w0, [x1, #0x27]
    // 0x1326c9c: DecompressPointer r0
    //     0x1326c9c: add             x0, x0, HEAP, lsl #32
    // 0x1326ca0: LoadField: r1 = r0->field_7
    //     0x1326ca0: ldur            w1, [x0, #7]
    // 0x1326ca4: DecompressPointer r1
    //     0x1326ca4: add             x1, x1, HEAP, lsl #32
    // 0x1326ca8: LoadField: r0 = r1->field_7
    //     0x1326ca8: ldur            w0, [x1, #7]
    // 0x1326cac: r1 = LoadInt32Instr(r0)
    //     0x1326cac: sbfx            x1, x0, #1, #0x1f
    // 0x1326cb0: cmp             x1, #9
    // 0x1326cb4: b.le            #0x1326cd0
    // 0x1326cb8: ldur            x2, [fp, #-8]
    // 0x1326cbc: r1 = Function '<anonymous closure>':.
    //     0x1326cbc: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f40] AnonymousClosure: (0x132588c), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_view.dart] ReturnOrderView::bottomNavigationBar (0x1369f9c)
    //     0x1326cc0: ldr             x1, [x1, #0xf40]
    // 0x1326cc4: r0 = AllocateClosure()
    //     0x1326cc4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1326cc8: mov             x2, x0
    // 0x1326ccc: b               #0x1326cd4
    // 0x1326cd0: r2 = Null
    //     0x1326cd0: mov             x2, NULL
    // 0x1326cd4: ldur            x0, [fp, #-8]
    // 0x1326cd8: stur            x2, [fp, #-0x30]
    // 0x1326cdc: LoadField: r1 = r0->field_f
    //     0x1326cdc: ldur            w1, [x0, #0xf]
    // 0x1326ce0: DecompressPointer r1
    //     0x1326ce0: add             x1, x1, HEAP, lsl #32
    // 0x1326ce4: r0 = controller()
    //     0x1326ce4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1326ce8: LoadField: r1 = r0->field_57
    //     0x1326ce8: ldur            w1, [x0, #0x57]
    // 0x1326cec: DecompressPointer r1
    //     0x1326cec: add             x1, x1, HEAP, lsl #32
    // 0x1326cf0: r0 = value()
    //     0x1326cf0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1326cf4: cmp             w0, NULL
    // 0x1326cf8: b.ne            #0x1326d04
    // 0x1326cfc: r0 = Null
    //     0x1326cfc: mov             x0, NULL
    // 0x1326d00: b               #0x1326d24
    // 0x1326d04: LoadField: r1 = r0->field_33
    //     0x1326d04: ldur            w1, [x0, #0x33]
    // 0x1326d08: DecompressPointer r1
    //     0x1326d08: add             x1, x1, HEAP, lsl #32
    // 0x1326d0c: cmp             w1, NULL
    // 0x1326d10: b.ne            #0x1326d1c
    // 0x1326d14: r0 = Null
    //     0x1326d14: mov             x0, NULL
    // 0x1326d18: b               #0x1326d24
    // 0x1326d1c: LoadField: r0 = r1->field_27
    //     0x1326d1c: ldur            w0, [x1, #0x27]
    // 0x1326d20: DecompressPointer r0
    //     0x1326d20: add             x0, x0, HEAP, lsl #32
    // 0x1326d24: cmp             w0, NULL
    // 0x1326d28: b.ne            #0x1326d34
    // 0x1326d2c: r7 = ""
    //     0x1326d2c: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1326d30: b               #0x1326d38
    // 0x1326d34: mov             x7, x0
    // 0x1326d38: ldur            x0, [fp, #-8]
    // 0x1326d3c: ldur            x6, [fp, #-0x10]
    // 0x1326d40: ldur            x5, [fp, #-0x28]
    // 0x1326d44: ldur            x4, [fp, #-0x48]
    // 0x1326d48: ldur            x3, [fp, #-0x20]
    // 0x1326d4c: ldur            x2, [fp, #-0x30]
    // 0x1326d50: stur            x7, [fp, #-0x38]
    // 0x1326d54: LoadField: r1 = r0->field_13
    //     0x1326d54: ldur            w1, [x0, #0x13]
    // 0x1326d58: DecompressPointer r1
    //     0x1326d58: add             x1, x1, HEAP, lsl #32
    // 0x1326d5c: r0 = of()
    //     0x1326d5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1326d60: LoadField: r1 = r0->field_87
    //     0x1326d60: ldur            w1, [x0, #0x87]
    // 0x1326d64: DecompressPointer r1
    //     0x1326d64: add             x1, x1, HEAP, lsl #32
    // 0x1326d68: LoadField: r0 = r1->field_7
    //     0x1326d68: ldur            w0, [x1, #7]
    // 0x1326d6c: DecompressPointer r0
    //     0x1326d6c: add             x0, x0, HEAP, lsl #32
    // 0x1326d70: r16 = 14.000000
    //     0x1326d70: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1326d74: ldr             x16, [x16, #0x1d8]
    // 0x1326d78: r30 = Instance_Color
    //     0x1326d78: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1326d7c: stp             lr, x16, [SP]
    // 0x1326d80: mov             x1, x0
    // 0x1326d84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1326d84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1326d88: ldr             x4, [x4, #0xaa0]
    // 0x1326d8c: r0 = copyWith()
    //     0x1326d8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1326d90: stur            x0, [fp, #-8]
    // 0x1326d94: r0 = Text()
    //     0x1326d94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1326d98: mov             x1, x0
    // 0x1326d9c: ldur            x0, [fp, #-0x38]
    // 0x1326da0: stur            x1, [fp, #-0x40]
    // 0x1326da4: StoreField: r1->field_b = r0
    //     0x1326da4: stur            w0, [x1, #0xb]
    // 0x1326da8: ldur            x0, [fp, #-8]
    // 0x1326dac: StoreField: r1->field_13 = r0
    //     0x1326dac: stur            w0, [x1, #0x13]
    // 0x1326db0: r0 = TextButton()
    //     0x1326db0: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1326db4: mov             x1, x0
    // 0x1326db8: ldur            x0, [fp, #-0x30]
    // 0x1326dbc: stur            x1, [fp, #-8]
    // 0x1326dc0: StoreField: r1->field_b = r0
    //     0x1326dc0: stur            w0, [x1, #0xb]
    // 0x1326dc4: r0 = false
    //     0x1326dc4: add             x0, NULL, #0x30  ; false
    // 0x1326dc8: StoreField: r1->field_27 = r0
    //     0x1326dc8: stur            w0, [x1, #0x27]
    // 0x1326dcc: r0 = true
    //     0x1326dcc: add             x0, NULL, #0x20  ; true
    // 0x1326dd0: StoreField: r1->field_2f = r0
    //     0x1326dd0: stur            w0, [x1, #0x2f]
    // 0x1326dd4: ldur            x0, [fp, #-0x40]
    // 0x1326dd8: StoreField: r1->field_37 = r0
    //     0x1326dd8: stur            w0, [x1, #0x37]
    // 0x1326ddc: r0 = TextButtonTheme()
    //     0x1326ddc: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1326de0: mov             x2, x0
    // 0x1326de4: ldur            x0, [fp, #-0x20]
    // 0x1326de8: stur            x2, [fp, #-0x30]
    // 0x1326dec: StoreField: r2->field_f = r0
    //     0x1326dec: stur            w0, [x2, #0xf]
    // 0x1326df0: ldur            x0, [fp, #-8]
    // 0x1326df4: StoreField: r2->field_b = r0
    //     0x1326df4: stur            w0, [x2, #0xb]
    // 0x1326df8: r1 = <FlexParentData>
    //     0x1326df8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1326dfc: ldr             x1, [x1, #0xe00]
    // 0x1326e00: r0 = Expanded()
    //     0x1326e00: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1326e04: mov             x3, x0
    // 0x1326e08: ldur            x0, [fp, #-0x48]
    // 0x1326e0c: stur            x3, [fp, #-8]
    // 0x1326e10: StoreField: r3->field_13 = r0
    //     0x1326e10: stur            x0, [x3, #0x13]
    // 0x1326e14: r0 = Instance_FlexFit
    //     0x1326e14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1326e18: ldr             x0, [x0, #0xe08]
    // 0x1326e1c: StoreField: r3->field_1b = r0
    //     0x1326e1c: stur            w0, [x3, #0x1b]
    // 0x1326e20: ldur            x0, [fp, #-0x30]
    // 0x1326e24: StoreField: r3->field_b = r0
    //     0x1326e24: stur            w0, [x3, #0xb]
    // 0x1326e28: r1 = Null
    //     0x1326e28: mov             x1, NULL
    // 0x1326e2c: r2 = 4
    //     0x1326e2c: movz            x2, #0x4
    // 0x1326e30: r0 = AllocateArray()
    //     0x1326e30: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1326e34: mov             x2, x0
    // 0x1326e38: ldur            x0, [fp, #-0x28]
    // 0x1326e3c: stur            x2, [fp, #-0x20]
    // 0x1326e40: StoreField: r2->field_f = r0
    //     0x1326e40: stur            w0, [x2, #0xf]
    // 0x1326e44: ldur            x0, [fp, #-8]
    // 0x1326e48: StoreField: r2->field_13 = r0
    //     0x1326e48: stur            w0, [x2, #0x13]
    // 0x1326e4c: r1 = <Widget>
    //     0x1326e4c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1326e50: r0 = AllocateGrowableArray()
    //     0x1326e50: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1326e54: mov             x1, x0
    // 0x1326e58: ldur            x0, [fp, #-0x20]
    // 0x1326e5c: stur            x1, [fp, #-8]
    // 0x1326e60: StoreField: r1->field_f = r0
    //     0x1326e60: stur            w0, [x1, #0xf]
    // 0x1326e64: r2 = 4
    //     0x1326e64: movz            x2, #0x4
    // 0x1326e68: StoreField: r1->field_b = r2
    //     0x1326e68: stur            w2, [x1, #0xb]
    // 0x1326e6c: r0 = Row()
    //     0x1326e6c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1326e70: mov             x3, x0
    // 0x1326e74: r0 = Instance_Axis
    //     0x1326e74: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1326e78: stur            x3, [fp, #-0x20]
    // 0x1326e7c: StoreField: r3->field_f = r0
    //     0x1326e7c: stur            w0, [x3, #0xf]
    // 0x1326e80: r0 = Instance_MainAxisAlignment
    //     0x1326e80: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x1326e84: ldr             x0, [x0, #0xa8]
    // 0x1326e88: StoreField: r3->field_13 = r0
    //     0x1326e88: stur            w0, [x3, #0x13]
    // 0x1326e8c: r0 = Instance_MainAxisSize
    //     0x1326e8c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1326e90: ldr             x0, [x0, #0xa10]
    // 0x1326e94: ArrayStore: r3[0] = r0  ; List_4
    //     0x1326e94: stur            w0, [x3, #0x17]
    // 0x1326e98: r1 = Instance_CrossAxisAlignment
    //     0x1326e98: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0x1326e9c: ldr             x1, [x1, #0xc68]
    // 0x1326ea0: StoreField: r3->field_1b = r1
    //     0x1326ea0: stur            w1, [x3, #0x1b]
    // 0x1326ea4: r4 = Instance_VerticalDirection
    //     0x1326ea4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1326ea8: ldr             x4, [x4, #0xa20]
    // 0x1326eac: StoreField: r3->field_23 = r4
    //     0x1326eac: stur            w4, [x3, #0x23]
    // 0x1326eb0: r5 = Instance_Clip
    //     0x1326eb0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1326eb4: ldr             x5, [x5, #0x38]
    // 0x1326eb8: StoreField: r3->field_2b = r5
    //     0x1326eb8: stur            w5, [x3, #0x2b]
    // 0x1326ebc: StoreField: r3->field_2f = rZR
    //     0x1326ebc: stur            xzr, [x3, #0x2f]
    // 0x1326ec0: ldur            x1, [fp, #-8]
    // 0x1326ec4: StoreField: r3->field_b = r1
    //     0x1326ec4: stur            w1, [x3, #0xb]
    // 0x1326ec8: r1 = Null
    //     0x1326ec8: mov             x1, NULL
    // 0x1326ecc: r2 = 4
    //     0x1326ecc: movz            x2, #0x4
    // 0x1326ed0: r0 = AllocateArray()
    //     0x1326ed0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1326ed4: mov             x2, x0
    // 0x1326ed8: ldur            x0, [fp, #-0x10]
    // 0x1326edc: stur            x2, [fp, #-8]
    // 0x1326ee0: StoreField: r2->field_f = r0
    //     0x1326ee0: stur            w0, [x2, #0xf]
    // 0x1326ee4: ldur            x0, [fp, #-0x20]
    // 0x1326ee8: StoreField: r2->field_13 = r0
    //     0x1326ee8: stur            w0, [x2, #0x13]
    // 0x1326eec: r1 = <Widget>
    //     0x1326eec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1326ef0: r0 = AllocateGrowableArray()
    //     0x1326ef0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1326ef4: mov             x1, x0
    // 0x1326ef8: ldur            x0, [fp, #-8]
    // 0x1326efc: stur            x1, [fp, #-0x10]
    // 0x1326f00: StoreField: r1->field_f = r0
    //     0x1326f00: stur            w0, [x1, #0xf]
    // 0x1326f04: r0 = 4
    //     0x1326f04: movz            x0, #0x4
    // 0x1326f08: StoreField: r1->field_b = r0
    //     0x1326f08: stur            w0, [x1, #0xb]
    // 0x1326f0c: r0 = Column()
    //     0x1326f0c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1326f10: mov             x1, x0
    // 0x1326f14: r0 = Instance_Axis
    //     0x1326f14: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1326f18: stur            x1, [fp, #-8]
    // 0x1326f1c: StoreField: r1->field_f = r0
    //     0x1326f1c: stur            w0, [x1, #0xf]
    // 0x1326f20: r0 = Instance_MainAxisAlignment
    //     0x1326f20: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1326f24: ldr             x0, [x0, #0xa08]
    // 0x1326f28: StoreField: r1->field_13 = r0
    //     0x1326f28: stur            w0, [x1, #0x13]
    // 0x1326f2c: r0 = Instance_MainAxisSize
    //     0x1326f2c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1326f30: ldr             x0, [x0, #0xa10]
    // 0x1326f34: ArrayStore: r1[0] = r0  ; List_4
    //     0x1326f34: stur            w0, [x1, #0x17]
    // 0x1326f38: r0 = Instance_CrossAxisAlignment
    //     0x1326f38: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1326f3c: ldr             x0, [x0, #0xa18]
    // 0x1326f40: StoreField: r1->field_1b = r0
    //     0x1326f40: stur            w0, [x1, #0x1b]
    // 0x1326f44: r0 = Instance_VerticalDirection
    //     0x1326f44: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1326f48: ldr             x0, [x0, #0xa20]
    // 0x1326f4c: StoreField: r1->field_23 = r0
    //     0x1326f4c: stur            w0, [x1, #0x23]
    // 0x1326f50: r0 = Instance_Clip
    //     0x1326f50: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1326f54: ldr             x0, [x0, #0x38]
    // 0x1326f58: StoreField: r1->field_2b = r0
    //     0x1326f58: stur            w0, [x1, #0x2b]
    // 0x1326f5c: StoreField: r1->field_2f = rZR
    //     0x1326f5c: stur            xzr, [x1, #0x2f]
    // 0x1326f60: ldur            x0, [fp, #-0x10]
    // 0x1326f64: StoreField: r1->field_b = r0
    //     0x1326f64: stur            w0, [x1, #0xb]
    // 0x1326f68: r0 = Padding()
    //     0x1326f68: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1326f6c: mov             x1, x0
    // 0x1326f70: r0 = Instance_EdgeInsets
    //     0x1326f70: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1326f74: ldr             x0, [x0, #0x1f0]
    // 0x1326f78: StoreField: r1->field_f = r0
    //     0x1326f78: stur            w0, [x1, #0xf]
    // 0x1326f7c: ldur            x0, [fp, #-8]
    // 0x1326f80: StoreField: r1->field_b = r0
    //     0x1326f80: stur            w0, [x1, #0xb]
    // 0x1326f84: mov             x0, x1
    // 0x1326f88: ldur            d0, [fp, #-0x50]
    // 0x1326f8c: stur            x0, [fp, #-8]
    // 0x1326f90: r0 = Container()
    //     0x1326f90: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1326f94: stur            x0, [fp, #-0x10]
    // 0x1326f98: ldur            x16, [fp, #-0x18]
    // 0x1326f9c: ldur            lr, [fp, #-8]
    // 0x1326fa0: stp             lr, x16, [SP]
    // 0x1326fa4: mov             x1, x0
    // 0x1326fa8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x1326fa8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x1326fac: ldr             x4, [x4, #0x88]
    // 0x1326fb0: r0 = Container()
    //     0x1326fb0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1326fb4: ldur            d0, [fp, #-0x50]
    // 0x1326fb8: r0 = inline_Allocate_Double()
    //     0x1326fb8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x1326fbc: add             x0, x0, #0x10
    //     0x1326fc0: cmp             x1, x0
    //     0x1326fc4: b.ls            #0x132700c
    //     0x1326fc8: str             x0, [THR, #0x50]  ; THR::top
    //     0x1326fcc: sub             x0, x0, #0xf
    //     0x1326fd0: movz            x1, #0xe15c
    //     0x1326fd4: movk            x1, #0x3, lsl #16
    //     0x1326fd8: stur            x1, [x0, #-1]
    // 0x1326fdc: StoreField: r0->field_7 = d0
    //     0x1326fdc: stur            d0, [x0, #7]
    // 0x1326fe0: stur            x0, [fp, #-8]
    // 0x1326fe4: r0 = SizedBox()
    //     0x1326fe4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1326fe8: ldur            x1, [fp, #-8]
    // 0x1326fec: StoreField: r0->field_13 = r1
    //     0x1326fec: stur            w1, [x0, #0x13]
    // 0x1326ff0: ldur            x1, [fp, #-0x10]
    // 0x1326ff4: StoreField: r0->field_b = r1
    //     0x1326ff4: stur            w1, [x0, #0xb]
    // 0x1326ff8: LeaveFrame
    //     0x1326ff8: mov             SP, fp
    //     0x1326ffc: ldp             fp, lr, [SP], #0x10
    // 0x1327000: ret
    //     0x1327000: ret             
    // 0x1327004: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1327004: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1327008: b               #0x1326054
    // 0x132700c: SaveReg d0
    //     0x132700c: str             q0, [SP, #-0x10]!
    // 0x1327010: r0 = AllocateDouble()
    //     0x1327010: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1327014: RestoreReg d0
    //     0x1327014: ldr             q0, [SP], #0x10
    // 0x1327018: b               #0x1326fdc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x132701c, size: 0xd0
    // 0x132701c: EnterFrame
    //     0x132701c: stp             fp, lr, [SP, #-0x10]!
    //     0x1327020: mov             fp, SP
    // 0x1327024: AllocStack(0x8)
    //     0x1327024: sub             SP, SP, #8
    // 0x1327028: SetupParameters()
    //     0x1327028: ldr             x0, [fp, #0x10]
    //     0x132702c: ldur            w2, [x0, #0x17]
    //     0x1327030: add             x2, x2, HEAP, lsl #32
    //     0x1327034: stur            x2, [fp, #-8]
    // 0x1327038: CheckStackOverflow
    //     0x1327038: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x132703c: cmp             SP, x16
    //     0x1327040: b.ls            #0x13270e4
    // 0x1327044: LoadField: r1 = r2->field_f
    //     0x1327044: ldur            w1, [x2, #0xf]
    // 0x1327048: DecompressPointer r1
    //     0x1327048: add             x1, x1, HEAP, lsl #32
    // 0x132704c: r0 = controller()
    //     0x132704c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1327050: LoadField: r1 = r0->field_6b
    //     0x1327050: ldur            w1, [x0, #0x6b]
    // 0x1327054: DecompressPointer r1
    //     0x1327054: add             x1, x1, HEAP, lsl #32
    // 0x1327058: r0 = value()
    //     0x1327058: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x132705c: cmp             w0, NULL
    // 0x1327060: b.ne            #0x132706c
    // 0x1327064: ldur            x0, [fp, #-8]
    // 0x1327068: b               #0x13270a8
    // 0x132706c: tbnz            w0, #4, #0x13270a4
    // 0x1327070: ldur            x0, [fp, #-8]
    // 0x1327074: LoadField: r1 = r0->field_f
    //     0x1327074: ldur            w1, [x0, #0xf]
    // 0x1327078: DecompressPointer r1
    //     0x1327078: add             x1, x1, HEAP, lsl #32
    // 0x132707c: r0 = controller()
    //     0x132707c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1327080: LoadField: r1 = r0->field_6b
    //     0x1327080: ldur            w1, [x0, #0x6b]
    // 0x1327084: DecompressPointer r1
    //     0x1327084: add             x1, x1, HEAP, lsl #32
    // 0x1327088: r2 = false
    //     0x1327088: add             x2, NULL, #0x30  ; false
    // 0x132708c: r0 = value=()
    //     0x132708c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1327090: ldur            x0, [fp, #-8]
    // 0x1327094: LoadField: r1 = r0->field_f
    //     0x1327094: ldur            w1, [x0, #0xf]
    // 0x1327098: DecompressPointer r1
    //     0x1327098: add             x1, x1, HEAP, lsl #32
    // 0x132709c: r0 = isButtonEnabled()
    //     0x132709c: bl              #0xb50f90  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::isButtonEnabled
    // 0x13270a0: b               #0x13270d4
    // 0x13270a4: ldur            x0, [fp, #-8]
    // 0x13270a8: LoadField: r1 = r0->field_f
    //     0x13270a8: ldur            w1, [x0, #0xf]
    // 0x13270ac: DecompressPointer r1
    //     0x13270ac: add             x1, x1, HEAP, lsl #32
    // 0x13270b0: r0 = controller()
    //     0x13270b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13270b4: LoadField: r1 = r0->field_6b
    //     0x13270b4: ldur            w1, [x0, #0x6b]
    // 0x13270b8: DecompressPointer r1
    //     0x13270b8: add             x1, x1, HEAP, lsl #32
    // 0x13270bc: r2 = true
    //     0x13270bc: add             x2, NULL, #0x20  ; true
    // 0x13270c0: r0 = value=()
    //     0x13270c0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13270c4: ldur            x0, [fp, #-8]
    // 0x13270c8: LoadField: r1 = r0->field_f
    //     0x13270c8: ldur            w1, [x0, #0xf]
    // 0x13270cc: DecompressPointer r1
    //     0x13270cc: add             x1, x1, HEAP, lsl #32
    // 0x13270d0: r0 = isButtonEnabled()
    //     0x13270d0: bl              #0xb50f90  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::isButtonEnabled
    // 0x13270d4: r0 = Null
    //     0x13270d4: mov             x0, NULL
    // 0x13270d8: LeaveFrame
    //     0x13270d8: mov             SP, fp
    //     0x13270dc: ldp             fp, lr, [SP], #0x10
    // 0x13270e0: ret
    //     0x13270e0: ret             
    // 0x13270e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13270e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13270e8: b               #0x1327044
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x13270ec, size: 0xec
    // 0x13270ec: EnterFrame
    //     0x13270ec: stp             fp, lr, [SP, #-0x10]!
    //     0x13270f0: mov             fp, SP
    // 0x13270f4: AllocStack(0x20)
    //     0x13270f4: sub             SP, SP, #0x20
    // 0x13270f8: SetupParameters()
    //     0x13270f8: ldr             x0, [fp, #0x10]
    //     0x13270fc: ldur            w2, [x0, #0x17]
    //     0x1327100: add             x2, x2, HEAP, lsl #32
    //     0x1327104: stur            x2, [fp, #-8]
    // 0x1327108: CheckStackOverflow
    //     0x1327108: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x132710c: cmp             SP, x16
    //     0x1327110: b.ls            #0x13271d0
    // 0x1327114: LoadField: r1 = r2->field_f
    //     0x1327114: ldur            w1, [x2, #0xf]
    // 0x1327118: DecompressPointer r1
    //     0x1327118: add             x1, x1, HEAP, lsl #32
    // 0x132711c: r0 = controller()
    //     0x132711c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1327120: mov             x2, x0
    // 0x1327124: ldur            x0, [fp, #-8]
    // 0x1327128: stur            x2, [fp, #-0x10]
    // 0x132712c: LoadField: r1 = r0->field_f
    //     0x132712c: ldur            w1, [x0, #0xf]
    // 0x1327130: DecompressPointer r1
    //     0x1327130: add             x1, x1, HEAP, lsl #32
    // 0x1327134: r0 = controller()
    //     0x1327134: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1327138: LoadField: r1 = r0->field_bb
    //     0x1327138: ldur            w1, [x0, #0xbb]
    // 0x132713c: DecompressPointer r1
    //     0x132713c: add             x1, x1, HEAP, lsl #32
    // 0x1327140: r0 = LoadClassIdInstr(r1)
    //     0x1327140: ldur            x0, [x1, #-1]
    //     0x1327144: ubfx            x0, x0, #0xc, #0x14
    // 0x1327148: r16 = "return"
    //     0x1327148: add             x16, PP, #0x32, lsl #12  ; [pp+0x329b8] "return"
    //     0x132714c: ldr             x16, [x16, #0x9b8]
    // 0x1327150: stp             x16, x1, [SP]
    // 0x1327154: mov             lr, x0
    // 0x1327158: ldr             lr, [x21, lr, lsl #3]
    // 0x132715c: blr             lr
    // 0x1327160: tbnz            w0, #4, #0x1327170
    // 0x1327164: r5 = "reason_page"
    //     0x1327164: add             x5, PP, #0x33, lsl #12  ; [pp+0x33f48] "reason_page"
    //     0x1327168: ldr             x5, [x5, #0xf48]
    // 0x132716c: b               #0x1327178
    // 0x1327170: r5 = "exchange_reason"
    //     0x1327170: add             x5, PP, #0x33, lsl #12  ; [pp+0x33f50] "exchange_reason"
    //     0x1327174: ldr             x5, [x5, #0xf50]
    // 0x1327178: ldur            x1, [fp, #-0x10]
    // 0x132717c: r2 = "NEXT"
    //     0x132717c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33f20] "NEXT"
    //     0x1327180: ldr             x2, [x2, #0xf20]
    // 0x1327184: r3 = "return_exchange_next"
    //     0x1327184: add             x3, PP, #0x33, lsl #12  ; [pp+0x33f60] "return_exchange_next"
    //     0x1327188: ldr             x3, [x3, #0xf60]
    // 0x132718c: r0 = ctaExchangeWithReasonRemarkPostEvent()
    //     0x132718c: bl              #0x13271d8  ; [package:customer_app/app/presentation/controllers/post_order/return_order_controller.dart] ReturnOrderController::ctaExchangeWithReasonRemarkPostEvent
    // 0x1327190: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1327190: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1327194: ldr             x0, [x0, #0x1c80]
    //     0x1327198: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x132719c: cmp             w0, w16
    //     0x13271a0: b.ne            #0x13271ac
    //     0x13271a4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13271a8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13271ac: r16 = "/return-order-confirm"
    //     0x13271ac: add             x16, PP, #0xd, lsl #12  ; [pp+0xd8c8] "/return-order-confirm"
    //     0x13271b0: ldr             x16, [x16, #0x8c8]
    // 0x13271b4: stp             x16, NULL, [SP]
    // 0x13271b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13271b8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13271bc: r0 = GetNavigation.toNamed()
    //     0x13271bc: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x13271c0: r0 = Null
    //     0x13271c0: mov             x0, NULL
    // 0x13271c4: LeaveFrame
    //     0x13271c4: mov             SP, fp
    //     0x13271c8: ldp             fp, lr, [SP], #0x10
    // 0x13271cc: ret
    //     0x13271cc: ret             
    // 0x13271d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13271d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13271d4: b               #0x1327114
  }
  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1369f9c, size: 0x64
    // 0x1369f9c: EnterFrame
    //     0x1369f9c: stp             fp, lr, [SP, #-0x10]!
    //     0x1369fa0: mov             fp, SP
    // 0x1369fa4: AllocStack(0x18)
    //     0x1369fa4: sub             SP, SP, #0x18
    // 0x1369fa8: SetupParameters(ReturnOrderView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1369fa8: stur            x1, [fp, #-8]
    //     0x1369fac: stur            x2, [fp, #-0x10]
    // 0x1369fb0: r1 = 2
    //     0x1369fb0: movz            x1, #0x2
    // 0x1369fb4: r0 = AllocateContext()
    //     0x1369fb4: bl              #0x16f6108  ; AllocateContextStub
    // 0x1369fb8: mov             x1, x0
    // 0x1369fbc: ldur            x0, [fp, #-8]
    // 0x1369fc0: stur            x1, [fp, #-0x18]
    // 0x1369fc4: StoreField: r1->field_f = r0
    //     0x1369fc4: stur            w0, [x1, #0xf]
    // 0x1369fc8: ldur            x0, [fp, #-0x10]
    // 0x1369fcc: StoreField: r1->field_13 = r0
    //     0x1369fcc: stur            w0, [x1, #0x13]
    // 0x1369fd0: r0 = Obx()
    //     0x1369fd0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1369fd4: ldur            x2, [fp, #-0x18]
    // 0x1369fd8: r1 = Function '<anonymous closure>':.
    //     0x1369fd8: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f08] AnonymousClosure: (0x132602c), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_view.dart] ReturnOrderView::bottomNavigationBar (0x1369f9c)
    //     0x1369fdc: ldr             x1, [x1, #0xf08]
    // 0x1369fe0: stur            x0, [fp, #-8]
    // 0x1369fe4: r0 = AllocateClosure()
    //     0x1369fe4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1369fe8: mov             x1, x0
    // 0x1369fec: ldur            x0, [fp, #-8]
    // 0x1369ff0: StoreField: r0->field_b = r1
    //     0x1369ff0: stur            w1, [x0, #0xb]
    // 0x1369ff4: LeaveFrame
    //     0x1369ff4: mov             SP, fp
    //     0x1369ff8: ldp             fp, lr, [SP], #0x10
    // 0x1369ffc: ret
    //     0x1369ffc: ret             
  }
  _ body(/* No info */) {
    // ** addr: 0x1506e38, size: 0x94
    // 0x1506e38: EnterFrame
    //     0x1506e38: stp             fp, lr, [SP, #-0x10]!
    //     0x1506e3c: mov             fp, SP
    // 0x1506e40: AllocStack(0x18)
    //     0x1506e40: sub             SP, SP, #0x18
    // 0x1506e44: SetupParameters(ReturnOrderView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1506e44: mov             x0, x1
    //     0x1506e48: stur            x1, [fp, #-8]
    //     0x1506e4c: stur            x2, [fp, #-0x10]
    // 0x1506e50: r1 = 2
    //     0x1506e50: movz            x1, #0x2
    // 0x1506e54: r0 = AllocateContext()
    //     0x1506e54: bl              #0x16f6108  ; AllocateContextStub
    // 0x1506e58: ldur            x2, [fp, #-8]
    // 0x1506e5c: stur            x0, [fp, #-0x18]
    // 0x1506e60: StoreField: r0->field_f = r2
    //     0x1506e60: stur            w2, [x0, #0xf]
    // 0x1506e64: ldur            x1, [fp, #-0x10]
    // 0x1506e68: StoreField: r0->field_13 = r1
    //     0x1506e68: stur            w1, [x0, #0x13]
    // 0x1506e6c: r0 = Obx()
    //     0x1506e6c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1506e70: ldur            x2, [fp, #-0x18]
    // 0x1506e74: r1 = Function '<anonymous closure>':.
    //     0x1506e74: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f70] AnonymousClosure: (0x1506f04), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x1506e38)
    //     0x1506e78: ldr             x1, [x1, #0xf70]
    // 0x1506e7c: stur            x0, [fp, #-0x10]
    // 0x1506e80: r0 = AllocateClosure()
    //     0x1506e80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1506e84: mov             x1, x0
    // 0x1506e88: ldur            x0, [fp, #-0x10]
    // 0x1506e8c: StoreField: r0->field_b = r1
    //     0x1506e8c: stur            w1, [x0, #0xb]
    // 0x1506e90: r0 = WillPopScope()
    //     0x1506e90: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x1506e94: mov             x3, x0
    // 0x1506e98: ldur            x0, [fp, #-0x10]
    // 0x1506e9c: stur            x3, [fp, #-0x18]
    // 0x1506ea0: StoreField: r3->field_b = r0
    //     0x1506ea0: stur            w0, [x3, #0xb]
    // 0x1506ea4: ldur            x2, [fp, #-8]
    // 0x1506ea8: r1 = Function 'onBackPress':.
    //     0x1506ea8: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f78] AnonymousClosure: (0x1506ecc), in [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::onBackPress (0x1441560)
    //     0x1506eac: ldr             x1, [x1, #0xf78]
    // 0x1506eb0: r0 = AllocateClosure()
    //     0x1506eb0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1506eb4: mov             x1, x0
    // 0x1506eb8: ldur            x0, [fp, #-0x18]
    // 0x1506ebc: StoreField: r0->field_f = r1
    //     0x1506ebc: stur            w1, [x0, #0xf]
    // 0x1506ec0: LeaveFrame
    //     0x1506ec0: mov             SP, fp
    //     0x1506ec4: ldp             fp, lr, [SP], #0x10
    // 0x1506ec8: ret
    //     0x1506ec8: ret             
  }
  [closure] Future<bool> onBackPress(dynamic) {
    // ** addr: 0x1506ecc, size: 0x38
    // 0x1506ecc: EnterFrame
    //     0x1506ecc: stp             fp, lr, [SP, #-0x10]!
    //     0x1506ed0: mov             fp, SP
    // 0x1506ed4: ldr             x0, [fp, #0x10]
    // 0x1506ed8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1506ed8: ldur            w1, [x0, #0x17]
    // 0x1506edc: DecompressPointer r1
    //     0x1506edc: add             x1, x1, HEAP, lsl #32
    // 0x1506ee0: CheckStackOverflow
    //     0x1506ee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1506ee4: cmp             SP, x16
    //     0x1506ee8: b.ls            #0x1506efc
    // 0x1506eec: r0 = onBackPress()
    //     0x1506eec: bl              #0x1441560  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::onBackPress
    // 0x1506ef0: LeaveFrame
    //     0x1506ef0: mov             SP, fp
    //     0x1506ef4: ldp             fp, lr, [SP], #0x10
    // 0x1506ef8: ret
    //     0x1506ef8: ret             
    // 0x1506efc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1506efc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1506f00: b               #0x1506eec
  }
  [closure] StatelessWidget <anonymous closure>(dynamic) {
    // ** addr: 0x1506f04, size: 0x1bec
    // 0x1506f04: EnterFrame
    //     0x1506f04: stp             fp, lr, [SP, #-0x10]!
    //     0x1506f08: mov             fp, SP
    // 0x1506f0c: AllocStack(0xb8)
    //     0x1506f0c: sub             SP, SP, #0xb8
    // 0x1506f10: SetupParameters()
    //     0x1506f10: ldr             x0, [fp, #0x10]
    //     0x1506f14: ldur            w2, [x0, #0x17]
    //     0x1506f18: add             x2, x2, HEAP, lsl #32
    //     0x1506f1c: stur            x2, [fp, #-8]
    // 0x1506f20: CheckStackOverflow
    //     0x1506f20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1506f24: cmp             SP, x16
    //     0x1506f28: b.ls            #0x1508abc
    // 0x1506f2c: LoadField: r1 = r2->field_f
    //     0x1506f2c: ldur            w1, [x2, #0xf]
    // 0x1506f30: DecompressPointer r1
    //     0x1506f30: add             x1, x1, HEAP, lsl #32
    // 0x1506f34: r0 = controller()
    //     0x1506f34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1506f38: LoadField: r1 = r0->field_57
    //     0x1506f38: ldur            w1, [x0, #0x57]
    // 0x1506f3c: DecompressPointer r1
    //     0x1506f3c: add             x1, x1, HEAP, lsl #32
    // 0x1506f40: r0 = value()
    //     0x1506f40: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1506f44: cmp             w0, NULL
    // 0x1506f48: b.eq            #0x1508a98
    // 0x1506f4c: ldur            x2, [fp, #-8]
    // 0x1506f50: LoadField: r1 = r2->field_13
    //     0x1506f50: ldur            w1, [x2, #0x13]
    // 0x1506f54: DecompressPointer r1
    //     0x1506f54: add             x1, x1, HEAP, lsl #32
    // 0x1506f58: r0 = of()
    //     0x1506f58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1506f5c: LoadField: r1 = r0->field_5b
    //     0x1506f5c: ldur            w1, [x0, #0x5b]
    // 0x1506f60: DecompressPointer r1
    //     0x1506f60: add             x1, x1, HEAP, lsl #32
    // 0x1506f64: r0 = LoadClassIdInstr(r1)
    //     0x1506f64: ldur            x0, [x1, #-1]
    //     0x1506f68: ubfx            x0, x0, #0xc, #0x14
    // 0x1506f6c: d0 = 0.100000
    //     0x1506f6c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x1506f70: r0 = GDT[cid_x0 + -0xffa]()
    //     0x1506f70: sub             lr, x0, #0xffa
    //     0x1506f74: ldr             lr, [x21, lr, lsl #3]
    //     0x1506f78: blr             lr
    // 0x1506f7c: mov             x2, x0
    // 0x1506f80: r1 = Null
    //     0x1506f80: mov             x1, NULL
    // 0x1506f84: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x1506f84: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x1506f88: r0 = Border.all()
    //     0x1506f88: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x1506f8c: stur            x0, [fp, #-0x10]
    // 0x1506f90: r0 = BoxDecoration()
    //     0x1506f90: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1506f94: mov             x1, x0
    // 0x1506f98: ldur            x0, [fp, #-0x10]
    // 0x1506f9c: stur            x1, [fp, #-0x18]
    // 0x1506fa0: StoreField: r1->field_f = r0
    //     0x1506fa0: stur            w0, [x1, #0xf]
    // 0x1506fa4: r0 = Instance_BoxShape
    //     0x1506fa4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1506fa8: ldr             x0, [x0, #0x80]
    // 0x1506fac: StoreField: r1->field_23 = r0
    //     0x1506fac: stur            w0, [x1, #0x23]
    // 0x1506fb0: r0 = ImageHeaders.forImages()
    //     0x1506fb0: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x1506fb4: ldur            x2, [fp, #-8]
    // 0x1506fb8: stur            x0, [fp, #-0x10]
    // 0x1506fbc: LoadField: r1 = r2->field_f
    //     0x1506fbc: ldur            w1, [x2, #0xf]
    // 0x1506fc0: DecompressPointer r1
    //     0x1506fc0: add             x1, x1, HEAP, lsl #32
    // 0x1506fc4: r0 = controller()
    //     0x1506fc4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1506fc8: LoadField: r1 = r0->field_57
    //     0x1506fc8: ldur            w1, [x0, #0x57]
    // 0x1506fcc: DecompressPointer r1
    //     0x1506fcc: add             x1, x1, HEAP, lsl #32
    // 0x1506fd0: r0 = value()
    //     0x1506fd0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1506fd4: cmp             w0, NULL
    // 0x1506fd8: b.ne            #0x1506fe4
    // 0x1506fdc: r0 = Null
    //     0x1506fdc: mov             x0, NULL
    // 0x1506fe0: b               #0x1507004
    // 0x1506fe4: LoadField: r1 = r0->field_f
    //     0x1506fe4: ldur            w1, [x0, #0xf]
    // 0x1506fe8: DecompressPointer r1
    //     0x1506fe8: add             x1, x1, HEAP, lsl #32
    // 0x1506fec: cmp             w1, NULL
    // 0x1506ff0: b.ne            #0x1506ffc
    // 0x1506ff4: r0 = Null
    //     0x1506ff4: mov             x0, NULL
    // 0x1506ff8: b               #0x1507004
    // 0x1506ffc: LoadField: r0 = r1->field_7
    //     0x1506ffc: ldur            w0, [x1, #7]
    // 0x1507000: DecompressPointer r0
    //     0x1507000: add             x0, x0, HEAP, lsl #32
    // 0x1507004: cmp             w0, NULL
    // 0x1507008: b.ne            #0x1507014
    // 0x150700c: r3 = ""
    //     0x150700c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1507010: b               #0x1507018
    // 0x1507014: mov             x3, x0
    // 0x1507018: ldur            x0, [fp, #-8]
    // 0x150701c: stur            x3, [fp, #-0x20]
    // 0x1507020: r1 = Function '<anonymous closure>':.
    //     0x1507020: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f80] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x1507024: ldr             x1, [x1, #0xf80]
    // 0x1507028: r2 = Null
    //     0x1507028: mov             x2, NULL
    // 0x150702c: r0 = AllocateClosure()
    //     0x150702c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1507030: r1 = Function '<anonymous closure>':.
    //     0x1507030: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f88] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x1507034: ldr             x1, [x1, #0xf88]
    // 0x1507038: r2 = Null
    //     0x1507038: mov             x2, NULL
    // 0x150703c: stur            x0, [fp, #-0x28]
    // 0x1507040: r0 = AllocateClosure()
    //     0x1507040: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1507044: stur            x0, [fp, #-0x30]
    // 0x1507048: r0 = CachedNetworkImage()
    //     0x1507048: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x150704c: stur            x0, [fp, #-0x38]
    // 0x1507050: ldur            x16, [fp, #-0x10]
    // 0x1507054: r30 = 84.000000
    //     0x1507054: add             lr, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0x1507058: ldr             lr, [lr, #0xf90]
    // 0x150705c: stp             lr, x16, [SP, #0x20]
    // 0x1507060: r16 = 56.000000
    //     0x1507060: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x1507064: ldr             x16, [x16, #0xb78]
    // 0x1507068: r30 = Instance_BoxFit
    //     0x1507068: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x150706c: ldr             lr, [lr, #0x118]
    // 0x1507070: stp             lr, x16, [SP, #0x10]
    // 0x1507074: ldur            x16, [fp, #-0x28]
    // 0x1507078: ldur            lr, [fp, #-0x30]
    // 0x150707c: stp             lr, x16, [SP]
    // 0x1507080: mov             x1, x0
    // 0x1507084: ldur            x2, [fp, #-0x20]
    // 0x1507088: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x5, height, 0x3, httpHeaders, 0x2, progressIndicatorBuilder, 0x6, width, 0x4, null]
    //     0x1507088: add             x4, PP, #0x33, lsl #12  ; [pp+0x33750] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x5, "height", 0x3, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x6, "width", 0x4, Null]
    //     0x150708c: ldr             x4, [x4, #0x750]
    // 0x1507090: r0 = CachedNetworkImage()
    //     0x1507090: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x1507094: ldur            x2, [fp, #-8]
    // 0x1507098: LoadField: r1 = r2->field_13
    //     0x1507098: ldur            w1, [x2, #0x13]
    // 0x150709c: DecompressPointer r1
    //     0x150709c: add             x1, x1, HEAP, lsl #32
    // 0x15070a0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15070a0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15070a4: r0 = _of()
    //     0x15070a4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x15070a8: LoadField: r1 = r0->field_7
    //     0x15070a8: ldur            w1, [x0, #7]
    // 0x15070ac: DecompressPointer r1
    //     0x15070ac: add             x1, x1, HEAP, lsl #32
    // 0x15070b0: LoadField: d0 = r1->field_7
    //     0x15070b0: ldur            d0, [x1, #7]
    // 0x15070b4: d1 = 0.500000
    //     0x15070b4: fmov            d1, #0.50000000
    // 0x15070b8: fmul            d2, d0, d1
    // 0x15070bc: ldur            x2, [fp, #-8]
    // 0x15070c0: stur            d2, [fp, #-0x68]
    // 0x15070c4: LoadField: r1 = r2->field_f
    //     0x15070c4: ldur            w1, [x2, #0xf]
    // 0x15070c8: DecompressPointer r1
    //     0x15070c8: add             x1, x1, HEAP, lsl #32
    // 0x15070cc: r0 = controller()
    //     0x15070cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15070d0: LoadField: r1 = r0->field_57
    //     0x15070d0: ldur            w1, [x0, #0x57]
    // 0x15070d4: DecompressPointer r1
    //     0x15070d4: add             x1, x1, HEAP, lsl #32
    // 0x15070d8: r0 = value()
    //     0x15070d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15070dc: cmp             w0, NULL
    // 0x15070e0: b.ne            #0x15070ec
    // 0x15070e4: r0 = Null
    //     0x15070e4: mov             x0, NULL
    // 0x15070e8: b               #0x150710c
    // 0x15070ec: LoadField: r1 = r0->field_f
    //     0x15070ec: ldur            w1, [x0, #0xf]
    // 0x15070f0: DecompressPointer r1
    //     0x15070f0: add             x1, x1, HEAP, lsl #32
    // 0x15070f4: cmp             w1, NULL
    // 0x15070f8: b.ne            #0x1507104
    // 0x15070fc: r0 = Null
    //     0x15070fc: mov             x0, NULL
    // 0x1507100: b               #0x150710c
    // 0x1507104: LoadField: r0 = r1->field_b
    //     0x1507104: ldur            w0, [x1, #0xb]
    // 0x1507108: DecompressPointer r0
    //     0x1507108: add             x0, x0, HEAP, lsl #32
    // 0x150710c: cmp             w0, NULL
    // 0x1507110: b.ne            #0x150711c
    // 0x1507114: r1 = ""
    //     0x1507114: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1507118: b               #0x1507120
    // 0x150711c: mov             x1, x0
    // 0x1507120: ldur            x2, [fp, #-8]
    // 0x1507124: r0 = capitalizeFirstWord()
    //     0x1507124: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x1507128: ldur            x2, [fp, #-8]
    // 0x150712c: stur            x0, [fp, #-0x10]
    // 0x1507130: LoadField: r1 = r2->field_13
    //     0x1507130: ldur            w1, [x2, #0x13]
    // 0x1507134: DecompressPointer r1
    //     0x1507134: add             x1, x1, HEAP, lsl #32
    // 0x1507138: r0 = of()
    //     0x1507138: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x150713c: LoadField: r1 = r0->field_87
    //     0x150713c: ldur            w1, [x0, #0x87]
    // 0x1507140: DecompressPointer r1
    //     0x1507140: add             x1, x1, HEAP, lsl #32
    // 0x1507144: LoadField: r0 = r1->field_2b
    //     0x1507144: ldur            w0, [x1, #0x2b]
    // 0x1507148: DecompressPointer r0
    //     0x1507148: add             x0, x0, HEAP, lsl #32
    // 0x150714c: r16 = Instance_Color
    //     0x150714c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1507150: r30 = 12.000000
    //     0x1507150: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1507154: ldr             lr, [lr, #0x9e8]
    // 0x1507158: stp             lr, x16, [SP]
    // 0x150715c: mov             x1, x0
    // 0x1507160: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1507160: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1507164: ldr             x4, [x4, #0x9b8]
    // 0x1507168: r0 = copyWith()
    //     0x1507168: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x150716c: stur            x0, [fp, #-0x20]
    // 0x1507170: r0 = Text()
    //     0x1507170: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1507174: mov             x2, x0
    // 0x1507178: ldur            x0, [fp, #-0x10]
    // 0x150717c: stur            x2, [fp, #-0x28]
    // 0x1507180: StoreField: r2->field_b = r0
    //     0x1507180: stur            w0, [x2, #0xb]
    // 0x1507184: ldur            x0, [fp, #-0x20]
    // 0x1507188: StoreField: r2->field_13 = r0
    //     0x1507188: stur            w0, [x2, #0x13]
    // 0x150718c: r0 = Instance_TextOverflow
    //     0x150718c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x1507190: ldr             x0, [x0, #0xe10]
    // 0x1507194: StoreField: r2->field_2b = r0
    //     0x1507194: stur            w0, [x2, #0x2b]
    // 0x1507198: r3 = 4
    //     0x1507198: movz            x3, #0x4
    // 0x150719c: StoreField: r2->field_37 = r3
    //     0x150719c: stur            w3, [x2, #0x37]
    // 0x15071a0: ldur            x4, [fp, #-8]
    // 0x15071a4: LoadField: r1 = r4->field_f
    //     0x15071a4: ldur            w1, [x4, #0xf]
    // 0x15071a8: DecompressPointer r1
    //     0x15071a8: add             x1, x1, HEAP, lsl #32
    // 0x15071ac: r0 = controller()
    //     0x15071ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15071b0: LoadField: r1 = r0->field_57
    //     0x15071b0: ldur            w1, [x0, #0x57]
    // 0x15071b4: DecompressPointer r1
    //     0x15071b4: add             x1, x1, HEAP, lsl #32
    // 0x15071b8: r0 = value()
    //     0x15071b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15071bc: cmp             w0, NULL
    // 0x15071c0: b.ne            #0x15071cc
    // 0x15071c4: r0 = Null
    //     0x15071c4: mov             x0, NULL
    // 0x15071c8: b               #0x15071ec
    // 0x15071cc: LoadField: r1 = r0->field_f
    //     0x15071cc: ldur            w1, [x0, #0xf]
    // 0x15071d0: DecompressPointer r1
    //     0x15071d0: add             x1, x1, HEAP, lsl #32
    // 0x15071d4: cmp             w1, NULL
    // 0x15071d8: b.ne            #0x15071e4
    // 0x15071dc: r0 = Null
    //     0x15071dc: mov             x0, NULL
    // 0x15071e0: b               #0x15071ec
    // 0x15071e4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x15071e4: ldur            w0, [x1, #0x17]
    // 0x15071e8: DecompressPointer r0
    //     0x15071e8: add             x0, x0, HEAP, lsl #32
    // 0x15071ec: r1 = LoadClassIdInstr(r0)
    //     0x15071ec: ldur            x1, [x0, #-1]
    //     0x15071f0: ubfx            x1, x1, #0xc, #0x14
    // 0x15071f4: r16 = "size"
    //     0x15071f4: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0x15071f8: ldr             x16, [x16, #0x9c0]
    // 0x15071fc: stp             x16, x0, [SP]
    // 0x1507200: mov             x0, x1
    // 0x1507204: mov             lr, x0
    // 0x1507208: ldr             lr, [x21, lr, lsl #3]
    // 0x150720c: blr             lr
    // 0x1507210: tbnz            w0, #4, #0x1507374
    // 0x1507214: ldur            x0, [fp, #-8]
    // 0x1507218: r1 = Null
    //     0x1507218: mov             x1, NULL
    // 0x150721c: r2 = 8
    //     0x150721c: movz            x2, #0x8
    // 0x1507220: r0 = AllocateArray()
    //     0x1507220: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1507224: stur            x0, [fp, #-0x10]
    // 0x1507228: r16 = "Size :  "
    //     0x1507228: add             x16, PP, #0x33, lsl #12  ; [pp+0x33758] "Size :  "
    //     0x150722c: ldr             x16, [x16, #0x758]
    // 0x1507230: StoreField: r0->field_f = r16
    //     0x1507230: stur            w16, [x0, #0xf]
    // 0x1507234: ldur            x2, [fp, #-8]
    // 0x1507238: LoadField: r1 = r2->field_f
    //     0x1507238: ldur            w1, [x2, #0xf]
    // 0x150723c: DecompressPointer r1
    //     0x150723c: add             x1, x1, HEAP, lsl #32
    // 0x1507240: r0 = controller()
    //     0x1507240: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1507244: LoadField: r1 = r0->field_57
    //     0x1507244: ldur            w1, [x0, #0x57]
    // 0x1507248: DecompressPointer r1
    //     0x1507248: add             x1, x1, HEAP, lsl #32
    // 0x150724c: r0 = value()
    //     0x150724c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1507250: cmp             w0, NULL
    // 0x1507254: b.ne            #0x1507260
    // 0x1507258: r0 = Null
    //     0x1507258: mov             x0, NULL
    // 0x150725c: b               #0x1507280
    // 0x1507260: LoadField: r1 = r0->field_f
    //     0x1507260: ldur            w1, [x0, #0xf]
    // 0x1507264: DecompressPointer r1
    //     0x1507264: add             x1, x1, HEAP, lsl #32
    // 0x1507268: cmp             w1, NULL
    // 0x150726c: b.ne            #0x1507278
    // 0x1507270: r0 = Null
    //     0x1507270: mov             x0, NULL
    // 0x1507274: b               #0x1507280
    // 0x1507278: LoadField: r0 = r1->field_f
    //     0x1507278: ldur            w0, [x1, #0xf]
    // 0x150727c: DecompressPointer r0
    //     0x150727c: add             x0, x0, HEAP, lsl #32
    // 0x1507280: ldur            x3, [fp, #-8]
    // 0x1507284: ldur            x2, [fp, #-0x10]
    // 0x1507288: mov             x1, x2
    // 0x150728c: ArrayStore: r1[1] = r0  ; List_4
    //     0x150728c: add             x25, x1, #0x13
    //     0x1507290: str             w0, [x25]
    //     0x1507294: tbz             w0, #0, #0x15072b0
    //     0x1507298: ldurb           w16, [x1, #-1]
    //     0x150729c: ldurb           w17, [x0, #-1]
    //     0x15072a0: and             x16, x17, x16, lsr #2
    //     0x15072a4: tst             x16, HEAP, lsr #32
    //     0x15072a8: b.eq            #0x15072b0
    //     0x15072ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15072b0: r16 = " / Qty: "
    //     0x15072b0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x15072b4: ldr             x16, [x16, #0x760]
    // 0x15072b8: ArrayStore: r2[0] = r16  ; List_4
    //     0x15072b8: stur            w16, [x2, #0x17]
    // 0x15072bc: LoadField: r1 = r3->field_f
    //     0x15072bc: ldur            w1, [x3, #0xf]
    // 0x15072c0: DecompressPointer r1
    //     0x15072c0: add             x1, x1, HEAP, lsl #32
    // 0x15072c4: r0 = controller()
    //     0x15072c4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15072c8: LoadField: r1 = r0->field_57
    //     0x15072c8: ldur            w1, [x0, #0x57]
    // 0x15072cc: DecompressPointer r1
    //     0x15072cc: add             x1, x1, HEAP, lsl #32
    // 0x15072d0: r0 = value()
    //     0x15072d0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15072d4: cmp             w0, NULL
    // 0x15072d8: b.ne            #0x15072e4
    // 0x15072dc: r0 = Null
    //     0x15072dc: mov             x0, NULL
    // 0x15072e0: b               #0x1507330
    // 0x15072e4: LoadField: r1 = r0->field_f
    //     0x15072e4: ldur            w1, [x0, #0xf]
    // 0x15072e8: DecompressPointer r1
    //     0x15072e8: add             x1, x1, HEAP, lsl #32
    // 0x15072ec: cmp             w1, NULL
    // 0x15072f0: b.ne            #0x15072fc
    // 0x15072f4: r0 = Null
    //     0x15072f4: mov             x0, NULL
    // 0x15072f8: b               #0x1507330
    // 0x15072fc: LoadField: r0 = r1->field_13
    //     0x15072fc: ldur            w0, [x1, #0x13]
    // 0x1507300: DecompressPointer r0
    //     0x1507300: add             x0, x0, HEAP, lsl #32
    // 0x1507304: r1 = 60
    //     0x1507304: movz            x1, #0x3c
    // 0x1507308: branchIfSmi(r0, 0x1507314)
    //     0x1507308: tbz             w0, #0, #0x1507314
    // 0x150730c: r1 = LoadClassIdInstr(r0)
    //     0x150730c: ldur            x1, [x0, #-1]
    //     0x1507310: ubfx            x1, x1, #0xc, #0x14
    // 0x1507314: str             x0, [SP]
    // 0x1507318: mov             x0, x1
    // 0x150731c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x150731c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x1507320: r0 = GDT[cid_x0 + 0x2700]()
    //     0x1507320: movz            x17, #0x2700
    //     0x1507324: add             lr, x0, x17
    //     0x1507328: ldr             lr, [x21, lr, lsl #3]
    //     0x150732c: blr             lr
    // 0x1507330: cmp             w0, NULL
    // 0x1507334: b.ne            #0x150733c
    // 0x1507338: r0 = ""
    //     0x1507338: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x150733c: ldur            x1, [fp, #-0x10]
    // 0x1507340: ArrayStore: r1[3] = r0  ; List_4
    //     0x1507340: add             x25, x1, #0x1b
    //     0x1507344: str             w0, [x25]
    //     0x1507348: tbz             w0, #0, #0x1507364
    //     0x150734c: ldurb           w16, [x1, #-1]
    //     0x1507350: ldurb           w17, [x0, #-1]
    //     0x1507354: and             x16, x17, x16, lsr #2
    //     0x1507358: tst             x16, HEAP, lsr #32
    //     0x150735c: b.eq            #0x1507364
    //     0x1507360: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1507364: ldur            x16, [fp, #-0x10]
    // 0x1507368: str             x16, [SP]
    // 0x150736c: r0 = _interpolate()
    //     0x150736c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1507370: b               #0x15074d0
    // 0x1507374: ldur            x0, [fp, #-8]
    // 0x1507378: r1 = Null
    //     0x1507378: mov             x1, NULL
    // 0x150737c: r2 = 8
    //     0x150737c: movz            x2, #0x8
    // 0x1507380: r0 = AllocateArray()
    //     0x1507380: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1507384: stur            x0, [fp, #-0x10]
    // 0x1507388: r16 = "Variant : "
    //     0x1507388: add             x16, PP, #0x33, lsl #12  ; [pp+0x33768] "Variant : "
    //     0x150738c: ldr             x16, [x16, #0x768]
    // 0x1507390: StoreField: r0->field_f = r16
    //     0x1507390: stur            w16, [x0, #0xf]
    // 0x1507394: ldur            x2, [fp, #-8]
    // 0x1507398: LoadField: r1 = r2->field_f
    //     0x1507398: ldur            w1, [x2, #0xf]
    // 0x150739c: DecompressPointer r1
    //     0x150739c: add             x1, x1, HEAP, lsl #32
    // 0x15073a0: r0 = controller()
    //     0x15073a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15073a4: LoadField: r1 = r0->field_57
    //     0x15073a4: ldur            w1, [x0, #0x57]
    // 0x15073a8: DecompressPointer r1
    //     0x15073a8: add             x1, x1, HEAP, lsl #32
    // 0x15073ac: r0 = value()
    //     0x15073ac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15073b0: cmp             w0, NULL
    // 0x15073b4: b.ne            #0x15073c0
    // 0x15073b8: r0 = Null
    //     0x15073b8: mov             x0, NULL
    // 0x15073bc: b               #0x15073e0
    // 0x15073c0: LoadField: r1 = r0->field_f
    //     0x15073c0: ldur            w1, [x0, #0xf]
    // 0x15073c4: DecompressPointer r1
    //     0x15073c4: add             x1, x1, HEAP, lsl #32
    // 0x15073c8: cmp             w1, NULL
    // 0x15073cc: b.ne            #0x15073d8
    // 0x15073d0: r0 = Null
    //     0x15073d0: mov             x0, NULL
    // 0x15073d4: b               #0x15073e0
    // 0x15073d8: LoadField: r0 = r1->field_f
    //     0x15073d8: ldur            w0, [x1, #0xf]
    // 0x15073dc: DecompressPointer r0
    //     0x15073dc: add             x0, x0, HEAP, lsl #32
    // 0x15073e0: ldur            x3, [fp, #-8]
    // 0x15073e4: ldur            x2, [fp, #-0x10]
    // 0x15073e8: mov             x1, x2
    // 0x15073ec: ArrayStore: r1[1] = r0  ; List_4
    //     0x15073ec: add             x25, x1, #0x13
    //     0x15073f0: str             w0, [x25]
    //     0x15073f4: tbz             w0, #0, #0x1507410
    //     0x15073f8: ldurb           w16, [x1, #-1]
    //     0x15073fc: ldurb           w17, [x0, #-1]
    //     0x1507400: and             x16, x17, x16, lsr #2
    //     0x1507404: tst             x16, HEAP, lsr #32
    //     0x1507408: b.eq            #0x1507410
    //     0x150740c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1507410: r16 = " / Qty: "
    //     0x1507410: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x1507414: ldr             x16, [x16, #0x760]
    // 0x1507418: ArrayStore: r2[0] = r16  ; List_4
    //     0x1507418: stur            w16, [x2, #0x17]
    // 0x150741c: LoadField: r1 = r3->field_f
    //     0x150741c: ldur            w1, [x3, #0xf]
    // 0x1507420: DecompressPointer r1
    //     0x1507420: add             x1, x1, HEAP, lsl #32
    // 0x1507424: r0 = controller()
    //     0x1507424: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1507428: LoadField: r1 = r0->field_57
    //     0x1507428: ldur            w1, [x0, #0x57]
    // 0x150742c: DecompressPointer r1
    //     0x150742c: add             x1, x1, HEAP, lsl #32
    // 0x1507430: r0 = value()
    //     0x1507430: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1507434: cmp             w0, NULL
    // 0x1507438: b.ne            #0x1507444
    // 0x150743c: r0 = Null
    //     0x150743c: mov             x0, NULL
    // 0x1507440: b               #0x1507490
    // 0x1507444: LoadField: r1 = r0->field_f
    //     0x1507444: ldur            w1, [x0, #0xf]
    // 0x1507448: DecompressPointer r1
    //     0x1507448: add             x1, x1, HEAP, lsl #32
    // 0x150744c: cmp             w1, NULL
    // 0x1507450: b.ne            #0x150745c
    // 0x1507454: r0 = Null
    //     0x1507454: mov             x0, NULL
    // 0x1507458: b               #0x1507490
    // 0x150745c: LoadField: r0 = r1->field_13
    //     0x150745c: ldur            w0, [x1, #0x13]
    // 0x1507460: DecompressPointer r0
    //     0x1507460: add             x0, x0, HEAP, lsl #32
    // 0x1507464: r1 = 60
    //     0x1507464: movz            x1, #0x3c
    // 0x1507468: branchIfSmi(r0, 0x1507474)
    //     0x1507468: tbz             w0, #0, #0x1507474
    // 0x150746c: r1 = LoadClassIdInstr(r0)
    //     0x150746c: ldur            x1, [x0, #-1]
    //     0x1507470: ubfx            x1, x1, #0xc, #0x14
    // 0x1507474: str             x0, [SP]
    // 0x1507478: mov             x0, x1
    // 0x150747c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x150747c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x1507480: r0 = GDT[cid_x0 + 0x2700]()
    //     0x1507480: movz            x17, #0x2700
    //     0x1507484: add             lr, x0, x17
    //     0x1507488: ldr             lr, [x21, lr, lsl #3]
    //     0x150748c: blr             lr
    // 0x1507490: cmp             w0, NULL
    // 0x1507494: b.ne            #0x150749c
    // 0x1507498: r0 = ""
    //     0x1507498: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x150749c: ldur            x1, [fp, #-0x10]
    // 0x15074a0: ArrayStore: r1[3] = r0  ; List_4
    //     0x15074a0: add             x25, x1, #0x1b
    //     0x15074a4: str             w0, [x25]
    //     0x15074a8: tbz             w0, #0, #0x15074c4
    //     0x15074ac: ldurb           w16, [x1, #-1]
    //     0x15074b0: ldurb           w17, [x0, #-1]
    //     0x15074b4: and             x16, x17, x16, lsr #2
    //     0x15074b8: tst             x16, HEAP, lsr #32
    //     0x15074bc: b.eq            #0x15074c4
    //     0x15074c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15074c4: ldur            x16, [fp, #-0x10]
    // 0x15074c8: str             x16, [SP]
    // 0x15074cc: r0 = _interpolate()
    //     0x15074cc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x15074d0: ldur            x2, [fp, #-8]
    // 0x15074d4: stur            x0, [fp, #-0x10]
    // 0x15074d8: LoadField: r1 = r2->field_13
    //     0x15074d8: ldur            w1, [x2, #0x13]
    // 0x15074dc: DecompressPointer r1
    //     0x15074dc: add             x1, x1, HEAP, lsl #32
    // 0x15074e0: r0 = of()
    //     0x15074e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15074e4: LoadField: r1 = r0->field_87
    //     0x15074e4: ldur            w1, [x0, #0x87]
    // 0x15074e8: DecompressPointer r1
    //     0x15074e8: add             x1, x1, HEAP, lsl #32
    // 0x15074ec: LoadField: r0 = r1->field_2b
    //     0x15074ec: ldur            w0, [x1, #0x2b]
    // 0x15074f0: DecompressPointer r0
    //     0x15074f0: add             x0, x0, HEAP, lsl #32
    // 0x15074f4: r16 = 12.000000
    //     0x15074f4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x15074f8: ldr             x16, [x16, #0x9e8]
    // 0x15074fc: r30 = Instance_Color
    //     0x15074fc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1507500: stp             lr, x16, [SP]
    // 0x1507504: mov             x1, x0
    // 0x1507508: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1507508: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x150750c: ldr             x4, [x4, #0xaa0]
    // 0x1507510: r0 = copyWith()
    //     0x1507510: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1507514: stur            x0, [fp, #-0x20]
    // 0x1507518: r0 = Text()
    //     0x1507518: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x150751c: mov             x1, x0
    // 0x1507520: ldur            x0, [fp, #-0x10]
    // 0x1507524: stur            x1, [fp, #-0x30]
    // 0x1507528: StoreField: r1->field_b = r0
    //     0x1507528: stur            w0, [x1, #0xb]
    // 0x150752c: ldur            x0, [fp, #-0x20]
    // 0x1507530: StoreField: r1->field_13 = r0
    //     0x1507530: stur            w0, [x1, #0x13]
    // 0x1507534: r0 = Padding()
    //     0x1507534: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1507538: mov             x2, x0
    // 0x150753c: r0 = Instance_EdgeInsets
    //     0x150753c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x1507540: ldr             x0, [x0, #0x770]
    // 0x1507544: stur            x2, [fp, #-0x10]
    // 0x1507548: StoreField: r2->field_f = r0
    //     0x1507548: stur            w0, [x2, #0xf]
    // 0x150754c: ldur            x1, [fp, #-0x30]
    // 0x1507550: StoreField: r2->field_b = r1
    //     0x1507550: stur            w1, [x2, #0xb]
    // 0x1507554: ldur            x3, [fp, #-8]
    // 0x1507558: LoadField: r1 = r3->field_f
    //     0x1507558: ldur            w1, [x3, #0xf]
    // 0x150755c: DecompressPointer r1
    //     0x150755c: add             x1, x1, HEAP, lsl #32
    // 0x1507560: r0 = controller()
    //     0x1507560: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1507564: LoadField: r1 = r0->field_57
    //     0x1507564: ldur            w1, [x0, #0x57]
    // 0x1507568: DecompressPointer r1
    //     0x1507568: add             x1, x1, HEAP, lsl #32
    // 0x150756c: r0 = value()
    //     0x150756c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1507570: cmp             w0, NULL
    // 0x1507574: b.ne            #0x1507580
    // 0x1507578: r4 = Null
    //     0x1507578: mov             x4, NULL
    // 0x150757c: b               #0x15075c0
    // 0x1507580: LoadField: r1 = r0->field_f
    //     0x1507580: ldur            w1, [x0, #0xf]
    // 0x1507584: DecompressPointer r1
    //     0x1507584: add             x1, x1, HEAP, lsl #32
    // 0x1507588: cmp             w1, NULL
    // 0x150758c: b.ne            #0x1507598
    // 0x1507590: r0 = Null
    //     0x1507590: mov             x0, NULL
    // 0x1507594: b               #0x15075bc
    // 0x1507598: LoadField: r0 = r1->field_1b
    //     0x1507598: ldur            w0, [x1, #0x1b]
    // 0x150759c: DecompressPointer r0
    //     0x150759c: add             x0, x0, HEAP, lsl #32
    // 0x15075a0: cmp             w0, NULL
    // 0x15075a4: b.ne            #0x15075b0
    // 0x15075a8: r0 = Null
    //     0x15075a8: mov             x0, NULL
    // 0x15075ac: b               #0x15075bc
    // 0x15075b0: LoadField: r1 = r0->field_7
    //     0x15075b0: ldur            w1, [x0, #7]
    // 0x15075b4: DecompressPointer r1
    //     0x15075b4: add             x1, x1, HEAP, lsl #32
    // 0x15075b8: mov             x0, x1
    // 0x15075bc: mov             x4, x0
    // 0x15075c0: ldur            x2, [fp, #-8]
    // 0x15075c4: ldur            x3, [fp, #-0x38]
    // 0x15075c8: ldur            d0, [fp, #-0x68]
    // 0x15075cc: ldur            x1, [fp, #-0x28]
    // 0x15075d0: ldur            x0, [fp, #-0x10]
    // 0x15075d4: str             x4, [SP]
    // 0x15075d8: r0 = _interpolateSingle()
    //     0x15075d8: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15075dc: ldur            x2, [fp, #-8]
    // 0x15075e0: stur            x0, [fp, #-0x20]
    // 0x15075e4: LoadField: r1 = r2->field_13
    //     0x15075e4: ldur            w1, [x2, #0x13]
    // 0x15075e8: DecompressPointer r1
    //     0x15075e8: add             x1, x1, HEAP, lsl #32
    // 0x15075ec: r0 = of()
    //     0x15075ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15075f0: LoadField: r1 = r0->field_87
    //     0x15075f0: ldur            w1, [x0, #0x87]
    // 0x15075f4: DecompressPointer r1
    //     0x15075f4: add             x1, x1, HEAP, lsl #32
    // 0x15075f8: LoadField: r0 = r1->field_7
    //     0x15075f8: ldur            w0, [x1, #7]
    // 0x15075fc: DecompressPointer r0
    //     0x15075fc: add             x0, x0, HEAP, lsl #32
    // 0x1507600: stur            x0, [fp, #-0x30]
    // 0x1507604: r1 = Instance_Color
    //     0x1507604: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1507608: d0 = 0.700000
    //     0x1507608: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x150760c: ldr             d0, [x17, #0xf48]
    // 0x1507610: r0 = withOpacity()
    //     0x1507610: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1507614: r16 = 14.000000
    //     0x1507614: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1507618: ldr             x16, [x16, #0x1d8]
    // 0x150761c: stp             x0, x16, [SP]
    // 0x1507620: ldur            x1, [fp, #-0x30]
    // 0x1507624: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1507624: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1507628: ldr             x4, [x4, #0xaa0]
    // 0x150762c: r0 = copyWith()
    //     0x150762c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1507630: stur            x0, [fp, #-0x30]
    // 0x1507634: r0 = Text()
    //     0x1507634: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1507638: mov             x1, x0
    // 0x150763c: ldur            x0, [fp, #-0x20]
    // 0x1507640: stur            x1, [fp, #-0x40]
    // 0x1507644: StoreField: r1->field_b = r0
    //     0x1507644: stur            w0, [x1, #0xb]
    // 0x1507648: ldur            x0, [fp, #-0x30]
    // 0x150764c: StoreField: r1->field_13 = r0
    //     0x150764c: stur            w0, [x1, #0x13]
    // 0x1507650: r0 = Padding()
    //     0x1507650: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1507654: mov             x3, x0
    // 0x1507658: r0 = Instance_EdgeInsets
    //     0x1507658: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x150765c: ldr             x0, [x0, #0x770]
    // 0x1507660: stur            x3, [fp, #-0x20]
    // 0x1507664: StoreField: r3->field_f = r0
    //     0x1507664: stur            w0, [x3, #0xf]
    // 0x1507668: ldur            x0, [fp, #-0x40]
    // 0x150766c: StoreField: r3->field_b = r0
    //     0x150766c: stur            w0, [x3, #0xb]
    // 0x1507670: r1 = Null
    //     0x1507670: mov             x1, NULL
    // 0x1507674: r2 = 6
    //     0x1507674: movz            x2, #0x6
    // 0x1507678: r0 = AllocateArray()
    //     0x1507678: bl              #0x16f7198  ; AllocateArrayStub
    // 0x150767c: mov             x2, x0
    // 0x1507680: ldur            x0, [fp, #-0x28]
    // 0x1507684: stur            x2, [fp, #-0x30]
    // 0x1507688: StoreField: r2->field_f = r0
    //     0x1507688: stur            w0, [x2, #0xf]
    // 0x150768c: ldur            x0, [fp, #-0x10]
    // 0x1507690: StoreField: r2->field_13 = r0
    //     0x1507690: stur            w0, [x2, #0x13]
    // 0x1507694: ldur            x0, [fp, #-0x20]
    // 0x1507698: ArrayStore: r2[0] = r0  ; List_4
    //     0x1507698: stur            w0, [x2, #0x17]
    // 0x150769c: r1 = <Widget>
    //     0x150769c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15076a0: r0 = AllocateGrowableArray()
    //     0x15076a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15076a4: mov             x1, x0
    // 0x15076a8: ldur            x0, [fp, #-0x30]
    // 0x15076ac: stur            x1, [fp, #-0x10]
    // 0x15076b0: StoreField: r1->field_f = r0
    //     0x15076b0: stur            w0, [x1, #0xf]
    // 0x15076b4: r2 = 6
    //     0x15076b4: movz            x2, #0x6
    // 0x15076b8: StoreField: r1->field_b = r2
    //     0x15076b8: stur            w2, [x1, #0xb]
    // 0x15076bc: r0 = Column()
    //     0x15076bc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x15076c0: mov             x1, x0
    // 0x15076c4: r0 = Instance_Axis
    //     0x15076c4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x15076c8: stur            x1, [fp, #-0x20]
    // 0x15076cc: StoreField: r1->field_f = r0
    //     0x15076cc: stur            w0, [x1, #0xf]
    // 0x15076d0: r2 = Instance_MainAxisAlignment
    //     0x15076d0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x15076d4: ldr             x2, [x2, #0xa8]
    // 0x15076d8: StoreField: r1->field_13 = r2
    //     0x15076d8: stur            w2, [x1, #0x13]
    // 0x15076dc: r2 = Instance_MainAxisSize
    //     0x15076dc: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x15076e0: ldr             x2, [x2, #0xdd0]
    // 0x15076e4: ArrayStore: r1[0] = r2  ; List_4
    //     0x15076e4: stur            w2, [x1, #0x17]
    // 0x15076e8: r3 = Instance_CrossAxisAlignment
    //     0x15076e8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x15076ec: ldr             x3, [x3, #0x890]
    // 0x15076f0: StoreField: r1->field_1b = r3
    //     0x15076f0: stur            w3, [x1, #0x1b]
    // 0x15076f4: r4 = Instance_VerticalDirection
    //     0x15076f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15076f8: ldr             x4, [x4, #0xa20]
    // 0x15076fc: StoreField: r1->field_23 = r4
    //     0x15076fc: stur            w4, [x1, #0x23]
    // 0x1507700: r5 = Instance_Clip
    //     0x1507700: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1507704: ldr             x5, [x5, #0x38]
    // 0x1507708: StoreField: r1->field_2b = r5
    //     0x1507708: stur            w5, [x1, #0x2b]
    // 0x150770c: StoreField: r1->field_2f = rZR
    //     0x150770c: stur            xzr, [x1, #0x2f]
    // 0x1507710: ldur            x6, [fp, #-0x10]
    // 0x1507714: StoreField: r1->field_b = r6
    //     0x1507714: stur            w6, [x1, #0xb]
    // 0x1507718: ldur            d0, [fp, #-0x68]
    // 0x150771c: r6 = inline_Allocate_Double()
    //     0x150771c: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0x1507720: add             x6, x6, #0x10
    //     0x1507724: cmp             x7, x6
    //     0x1507728: b.ls            #0x1508ac4
    //     0x150772c: str             x6, [THR, #0x50]  ; THR::top
    //     0x1507730: sub             x6, x6, #0xf
    //     0x1507734: movz            x7, #0xe15c
    //     0x1507738: movk            x7, #0x3, lsl #16
    //     0x150773c: stur            x7, [x6, #-1]
    // 0x1507740: StoreField: r6->field_7 = d0
    //     0x1507740: stur            d0, [x6, #7]
    // 0x1507744: stur            x6, [fp, #-0x10]
    // 0x1507748: r0 = SizedBox()
    //     0x1507748: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x150774c: mov             x1, x0
    // 0x1507750: ldur            x0, [fp, #-0x10]
    // 0x1507754: stur            x1, [fp, #-0x28]
    // 0x1507758: StoreField: r1->field_f = r0
    //     0x1507758: stur            w0, [x1, #0xf]
    // 0x150775c: ldur            x0, [fp, #-0x20]
    // 0x1507760: StoreField: r1->field_b = r0
    //     0x1507760: stur            w0, [x1, #0xb]
    // 0x1507764: r0 = Padding()
    //     0x1507764: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1507768: mov             x3, x0
    // 0x150776c: r0 = Instance_EdgeInsets
    //     0x150776c: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0x1507770: ldr             x0, [x0, #0xc40]
    // 0x1507774: stur            x3, [fp, #-0x10]
    // 0x1507778: StoreField: r3->field_f = r0
    //     0x1507778: stur            w0, [x3, #0xf]
    // 0x150777c: ldur            x0, [fp, #-0x28]
    // 0x1507780: StoreField: r3->field_b = r0
    //     0x1507780: stur            w0, [x3, #0xb]
    // 0x1507784: r1 = Null
    //     0x1507784: mov             x1, NULL
    // 0x1507788: r2 = 4
    //     0x1507788: movz            x2, #0x4
    // 0x150778c: r0 = AllocateArray()
    //     0x150778c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1507790: mov             x2, x0
    // 0x1507794: ldur            x0, [fp, #-0x38]
    // 0x1507798: stur            x2, [fp, #-0x20]
    // 0x150779c: StoreField: r2->field_f = r0
    //     0x150779c: stur            w0, [x2, #0xf]
    // 0x15077a0: ldur            x0, [fp, #-0x10]
    // 0x15077a4: StoreField: r2->field_13 = r0
    //     0x15077a4: stur            w0, [x2, #0x13]
    // 0x15077a8: r1 = <Widget>
    //     0x15077a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15077ac: r0 = AllocateGrowableArray()
    //     0x15077ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15077b0: mov             x1, x0
    // 0x15077b4: ldur            x0, [fp, #-0x20]
    // 0x15077b8: stur            x1, [fp, #-0x10]
    // 0x15077bc: StoreField: r1->field_f = r0
    //     0x15077bc: stur            w0, [x1, #0xf]
    // 0x15077c0: r2 = 4
    //     0x15077c0: movz            x2, #0x4
    // 0x15077c4: StoreField: r1->field_b = r2
    //     0x15077c4: stur            w2, [x1, #0xb]
    // 0x15077c8: r0 = Row()
    //     0x15077c8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15077cc: mov             x3, x0
    // 0x15077d0: r0 = Instance_Axis
    //     0x15077d0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15077d4: stur            x3, [fp, #-0x20]
    // 0x15077d8: StoreField: r3->field_f = r0
    //     0x15077d8: stur            w0, [x3, #0xf]
    // 0x15077dc: r4 = Instance_MainAxisAlignment
    //     0x15077dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x15077e0: ldr             x4, [x4, #0xa08]
    // 0x15077e4: StoreField: r3->field_13 = r4
    //     0x15077e4: stur            w4, [x3, #0x13]
    // 0x15077e8: r5 = Instance_MainAxisSize
    //     0x15077e8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15077ec: ldr             x5, [x5, #0xa10]
    // 0x15077f0: ArrayStore: r3[0] = r5  ; List_4
    //     0x15077f0: stur            w5, [x3, #0x17]
    // 0x15077f4: r6 = Instance_CrossAxisAlignment
    //     0x15077f4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15077f8: ldr             x6, [x6, #0xa18]
    // 0x15077fc: StoreField: r3->field_1b = r6
    //     0x15077fc: stur            w6, [x3, #0x1b]
    // 0x1507800: r7 = Instance_VerticalDirection
    //     0x1507800: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1507804: ldr             x7, [x7, #0xa20]
    // 0x1507808: StoreField: r3->field_23 = r7
    //     0x1507808: stur            w7, [x3, #0x23]
    // 0x150780c: r8 = Instance_Clip
    //     0x150780c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1507810: ldr             x8, [x8, #0x38]
    // 0x1507814: StoreField: r3->field_2b = r8
    //     0x1507814: stur            w8, [x3, #0x2b]
    // 0x1507818: StoreField: r3->field_2f = rZR
    //     0x1507818: stur            xzr, [x3, #0x2f]
    // 0x150781c: ldur            x1, [fp, #-0x10]
    // 0x1507820: StoreField: r3->field_b = r1
    //     0x1507820: stur            w1, [x3, #0xb]
    // 0x1507824: r1 = Null
    //     0x1507824: mov             x1, NULL
    // 0x1507828: r2 = 2
    //     0x1507828: movz            x2, #0x2
    // 0x150782c: r0 = AllocateArray()
    //     0x150782c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1507830: mov             x2, x0
    // 0x1507834: ldur            x0, [fp, #-0x20]
    // 0x1507838: stur            x2, [fp, #-0x10]
    // 0x150783c: StoreField: r2->field_f = r0
    //     0x150783c: stur            w0, [x2, #0xf]
    // 0x1507840: r1 = <Widget>
    //     0x1507840: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1507844: r0 = AllocateGrowableArray()
    //     0x1507844: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1507848: mov             x1, x0
    // 0x150784c: ldur            x0, [fp, #-0x10]
    // 0x1507850: stur            x1, [fp, #-0x20]
    // 0x1507854: StoreField: r1->field_f = r0
    //     0x1507854: stur            w0, [x1, #0xf]
    // 0x1507858: r2 = 2
    //     0x1507858: movz            x2, #0x2
    // 0x150785c: StoreField: r1->field_b = r2
    //     0x150785c: stur            w2, [x1, #0xb]
    // 0x1507860: r0 = Column()
    //     0x1507860: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1507864: mov             x1, x0
    // 0x1507868: r0 = Instance_Axis
    //     0x1507868: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x150786c: stur            x1, [fp, #-0x10]
    // 0x1507870: StoreField: r1->field_f = r0
    //     0x1507870: stur            w0, [x1, #0xf]
    // 0x1507874: r2 = Instance_MainAxisAlignment
    //     0x1507874: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1507878: ldr             x2, [x2, #0xa08]
    // 0x150787c: StoreField: r1->field_13 = r2
    //     0x150787c: stur            w2, [x1, #0x13]
    // 0x1507880: r3 = Instance_MainAxisSize
    //     0x1507880: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1507884: ldr             x3, [x3, #0xa10]
    // 0x1507888: ArrayStore: r1[0] = r3  ; List_4
    //     0x1507888: stur            w3, [x1, #0x17]
    // 0x150788c: r4 = Instance_CrossAxisAlignment
    //     0x150788c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1507890: ldr             x4, [x4, #0xa18]
    // 0x1507894: StoreField: r1->field_1b = r4
    //     0x1507894: stur            w4, [x1, #0x1b]
    // 0x1507898: r5 = Instance_VerticalDirection
    //     0x1507898: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x150789c: ldr             x5, [x5, #0xa20]
    // 0x15078a0: StoreField: r1->field_23 = r5
    //     0x15078a0: stur            w5, [x1, #0x23]
    // 0x15078a4: r6 = Instance_Clip
    //     0x15078a4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15078a8: ldr             x6, [x6, #0x38]
    // 0x15078ac: StoreField: r1->field_2b = r6
    //     0x15078ac: stur            w6, [x1, #0x2b]
    // 0x15078b0: StoreField: r1->field_2f = rZR
    //     0x15078b0: stur            xzr, [x1, #0x2f]
    // 0x15078b4: ldur            x7, [fp, #-0x20]
    // 0x15078b8: StoreField: r1->field_b = r7
    //     0x15078b8: stur            w7, [x1, #0xb]
    // 0x15078bc: r0 = Padding()
    //     0x15078bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15078c0: mov             x1, x0
    // 0x15078c4: r0 = Instance_EdgeInsets
    //     0x15078c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x15078c8: ldr             x0, [x0, #0x1f0]
    // 0x15078cc: stur            x1, [fp, #-0x20]
    // 0x15078d0: StoreField: r1->field_f = r0
    //     0x15078d0: stur            w0, [x1, #0xf]
    // 0x15078d4: ldur            x0, [fp, #-0x10]
    // 0x15078d8: StoreField: r1->field_b = r0
    //     0x15078d8: stur            w0, [x1, #0xb]
    // 0x15078dc: r0 = Container()
    //     0x15078dc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15078e0: stur            x0, [fp, #-0x10]
    // 0x15078e4: ldur            x16, [fp, #-0x18]
    // 0x15078e8: ldur            lr, [fp, #-0x20]
    // 0x15078ec: stp             lr, x16, [SP]
    // 0x15078f0: mov             x1, x0
    // 0x15078f4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x15078f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x15078f8: ldr             x4, [x4, #0x88]
    // 0x15078fc: r0 = Container()
    //     0x15078fc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1507900: r0 = Padding()
    //     0x1507900: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1507904: mov             x2, x0
    // 0x1507908: r0 = Instance_EdgeInsets
    //     0x1507908: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0x150790c: ldr             x0, [x0, #0xf98]
    // 0x1507910: stur            x2, [fp, #-0x18]
    // 0x1507914: StoreField: r2->field_f = r0
    //     0x1507914: stur            w0, [x2, #0xf]
    // 0x1507918: ldur            x0, [fp, #-0x10]
    // 0x150791c: StoreField: r2->field_b = r0
    //     0x150791c: stur            w0, [x2, #0xb]
    // 0x1507920: ldur            x0, [fp, #-8]
    // 0x1507924: LoadField: r1 = r0->field_13
    //     0x1507924: ldur            w1, [x0, #0x13]
    // 0x1507928: DecompressPointer r1
    //     0x1507928: add             x1, x1, HEAP, lsl #32
    // 0x150792c: r0 = of()
    //     0x150792c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1507930: LoadField: r1 = r0->field_87
    //     0x1507930: ldur            w1, [x0, #0x87]
    // 0x1507934: DecompressPointer r1
    //     0x1507934: add             x1, x1, HEAP, lsl #32
    // 0x1507938: LoadField: r0 = r1->field_7
    //     0x1507938: ldur            w0, [x1, #7]
    // 0x150793c: DecompressPointer r0
    //     0x150793c: add             x0, x0, HEAP, lsl #32
    // 0x1507940: stur            x0, [fp, #-0x10]
    // 0x1507944: r1 = Instance_Color
    //     0x1507944: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1507948: d0 = 0.700000
    //     0x1507948: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x150794c: ldr             d0, [x17, #0xf48]
    // 0x1507950: r0 = withOpacity()
    //     0x1507950: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1507954: r16 = 14.000000
    //     0x1507954: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1507958: ldr             x16, [x16, #0x1d8]
    // 0x150795c: stp             x0, x16, [SP]
    // 0x1507960: ldur            x1, [fp, #-0x10]
    // 0x1507964: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1507964: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1507968: ldr             x4, [x4, #0xaa0]
    // 0x150796c: r0 = copyWith()
    //     0x150796c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1507970: stur            x0, [fp, #-0x10]
    // 0x1507974: r0 = Text()
    //     0x1507974: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1507978: mov             x1, x0
    // 0x150797c: r0 = "What is the Issue\?"
    //     0x150797c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33fa0] "What is the Issue\?"
    //     0x1507980: ldr             x0, [x0, #0xfa0]
    // 0x1507984: stur            x1, [fp, #-0x20]
    // 0x1507988: StoreField: r1->field_b = r0
    //     0x1507988: stur            w0, [x1, #0xb]
    // 0x150798c: ldur            x0, [fp, #-0x10]
    // 0x1507990: StoreField: r1->field_13 = r0
    //     0x1507990: stur            w0, [x1, #0x13]
    // 0x1507994: r0 = Padding()
    //     0x1507994: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1507998: mov             x2, x0
    // 0x150799c: r0 = Instance_EdgeInsets
    //     0x150799c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x15079a0: ldr             x0, [x0, #0x980]
    // 0x15079a4: stur            x2, [fp, #-0x10]
    // 0x15079a8: StoreField: r2->field_f = r0
    //     0x15079a8: stur            w0, [x2, #0xf]
    // 0x15079ac: ldur            x1, [fp, #-0x20]
    // 0x15079b0: StoreField: r2->field_b = r1
    //     0x15079b0: stur            w1, [x2, #0xb]
    // 0x15079b4: ldur            x3, [fp, #-8]
    // 0x15079b8: LoadField: r1 = r3->field_13
    //     0x15079b8: ldur            w1, [x3, #0x13]
    // 0x15079bc: DecompressPointer r1
    //     0x15079bc: add             x1, x1, HEAP, lsl #32
    // 0x15079c0: r0 = of()
    //     0x15079c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15079c4: LoadField: r1 = r0->field_87
    //     0x15079c4: ldur            w1, [x0, #0x87]
    // 0x15079c8: DecompressPointer r1
    //     0x15079c8: add             x1, x1, HEAP, lsl #32
    // 0x15079cc: LoadField: r0 = r1->field_2b
    //     0x15079cc: ldur            w0, [x1, #0x2b]
    // 0x15079d0: DecompressPointer r0
    //     0x15079d0: add             x0, x0, HEAP, lsl #32
    // 0x15079d4: stur            x0, [fp, #-0x20]
    // 0x15079d8: r1 = Instance_Color
    //     0x15079d8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15079dc: d0 = 0.400000
    //     0x15079dc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x15079e0: r0 = withOpacity()
    //     0x15079e0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x15079e4: r16 = 12.000000
    //     0x15079e4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x15079e8: ldr             x16, [x16, #0x9e8]
    // 0x15079ec: stp             x0, x16, [SP]
    // 0x15079f0: ldur            x1, [fp, #-0x20]
    // 0x15079f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15079f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15079f8: ldr             x4, [x4, #0xaa0]
    // 0x15079fc: r0 = copyWith()
    //     0x15079fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1507a00: stur            x0, [fp, #-0x20]
    // 0x1507a04: r0 = Text()
    //     0x1507a04: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1507a08: mov             x1, x0
    // 0x1507a0c: r0 = "Please choose the correct issue for return/replace."
    //     0x1507a0c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33fa8] "Please choose the correct issue for return/replace."
    //     0x1507a10: ldr             x0, [x0, #0xfa8]
    // 0x1507a14: stur            x1, [fp, #-0x28]
    // 0x1507a18: StoreField: r1->field_b = r0
    //     0x1507a18: stur            w0, [x1, #0xb]
    // 0x1507a1c: ldur            x0, [fp, #-0x20]
    // 0x1507a20: StoreField: r1->field_13 = r0
    //     0x1507a20: stur            w0, [x1, #0x13]
    // 0x1507a24: r0 = Padding()
    //     0x1507a24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1507a28: mov             x2, x0
    // 0x1507a2c: r0 = Instance_EdgeInsets
    //     0x1507a2c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x1507a30: ldr             x0, [x0, #0x980]
    // 0x1507a34: stur            x2, [fp, #-0x20]
    // 0x1507a38: StoreField: r2->field_f = r0
    //     0x1507a38: stur            w0, [x2, #0xf]
    // 0x1507a3c: ldur            x1, [fp, #-0x28]
    // 0x1507a40: StoreField: r2->field_b = r1
    //     0x1507a40: stur            w1, [x2, #0xb]
    // 0x1507a44: ldur            x3, [fp, #-8]
    // 0x1507a48: LoadField: r1 = r3->field_13
    //     0x1507a48: ldur            w1, [x3, #0x13]
    // 0x1507a4c: DecompressPointer r1
    //     0x1507a4c: add             x1, x1, HEAP, lsl #32
    // 0x1507a50: r0 = of()
    //     0x1507a50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1507a54: LoadField: r1 = r0->field_87
    //     0x1507a54: ldur            w1, [x0, #0x87]
    // 0x1507a58: DecompressPointer r1
    //     0x1507a58: add             x1, x1, HEAP, lsl #32
    // 0x1507a5c: LoadField: r0 = r1->field_7
    //     0x1507a5c: ldur            w0, [x1, #7]
    // 0x1507a60: DecompressPointer r0
    //     0x1507a60: add             x0, x0, HEAP, lsl #32
    // 0x1507a64: r16 = 14.000000
    //     0x1507a64: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1507a68: ldr             x16, [x16, #0x1d8]
    // 0x1507a6c: r30 = Instance_Color
    //     0x1507a6c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1507a70: stp             lr, x16, [SP, #8]
    // 0x1507a74: r16 = const [Instance of 'FontFeature']
    //     0x1507a74: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fb0] List<FontFeature>(1)
    //     0x1507a78: ldr             x16, [x16, #0xfb0]
    // 0x1507a7c: str             x16, [SP]
    // 0x1507a80: mov             x1, x0
    // 0x1507a84: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontFeatures, 0x3, fontSize, 0x1, null]
    //     0x1507a84: add             x4, PP, #0x33, lsl #12  ; [pp+0x33fb8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontFeatures", 0x3, "fontSize", 0x1, Null]
    //     0x1507a88: ldr             x4, [x4, #0xfb8]
    // 0x1507a8c: r0 = copyWith()
    //     0x1507a8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1507a90: stur            x0, [fp, #-0x28]
    // 0x1507a94: r0 = TextSpan()
    //     0x1507a94: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1507a98: mov             x2, x0
    // 0x1507a9c: r0 = "Select Reason"
    //     0x1507a9c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33fc0] "Select Reason"
    //     0x1507aa0: ldr             x0, [x0, #0xfc0]
    // 0x1507aa4: stur            x2, [fp, #-0x30]
    // 0x1507aa8: StoreField: r2->field_b = r0
    //     0x1507aa8: stur            w0, [x2, #0xb]
    // 0x1507aac: r0 = Instance__DeferringMouseCursor
    //     0x1507aac: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1507ab0: ArrayStore: r2[0] = r0  ; List_4
    //     0x1507ab0: stur            w0, [x2, #0x17]
    // 0x1507ab4: ldur            x1, [fp, #-0x28]
    // 0x1507ab8: StoreField: r2->field_7 = r1
    //     0x1507ab8: stur            w1, [x2, #7]
    // 0x1507abc: ldur            x3, [fp, #-8]
    // 0x1507ac0: LoadField: r1 = r3->field_13
    //     0x1507ac0: ldur            w1, [x3, #0x13]
    // 0x1507ac4: DecompressPointer r1
    //     0x1507ac4: add             x1, x1, HEAP, lsl #32
    // 0x1507ac8: r0 = of()
    //     0x1507ac8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1507acc: LoadField: r1 = r0->field_87
    //     0x1507acc: ldur            w1, [x0, #0x87]
    // 0x1507ad0: DecompressPointer r1
    //     0x1507ad0: add             x1, x1, HEAP, lsl #32
    // 0x1507ad4: LoadField: r0 = r1->field_2b
    //     0x1507ad4: ldur            w0, [x1, #0x2b]
    // 0x1507ad8: DecompressPointer r0
    //     0x1507ad8: add             x0, x0, HEAP, lsl #32
    // 0x1507adc: r16 = Instance_MaterialColor
    //     0x1507adc: add             x16, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0x1507ae0: ldr             x16, [x16, #0x180]
    // 0x1507ae4: str             x16, [SP]
    // 0x1507ae8: mov             x1, x0
    // 0x1507aec: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x1507aec: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x1507af0: ldr             x4, [x4, #0xf40]
    // 0x1507af4: r0 = copyWith()
    //     0x1507af4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1507af8: stur            x0, [fp, #-0x28]
    // 0x1507afc: r0 = TextSpan()
    //     0x1507afc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1507b00: mov             x3, x0
    // 0x1507b04: r0 = " *"
    //     0x1507b04: add             x0, PP, #0x33, lsl #12  ; [pp+0x33fc8] " *"
    //     0x1507b08: ldr             x0, [x0, #0xfc8]
    // 0x1507b0c: stur            x3, [fp, #-0x38]
    // 0x1507b10: StoreField: r3->field_b = r0
    //     0x1507b10: stur            w0, [x3, #0xb]
    // 0x1507b14: r0 = Instance__DeferringMouseCursor
    //     0x1507b14: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1507b18: ArrayStore: r3[0] = r0  ; List_4
    //     0x1507b18: stur            w0, [x3, #0x17]
    // 0x1507b1c: ldur            x1, [fp, #-0x28]
    // 0x1507b20: StoreField: r3->field_7 = r1
    //     0x1507b20: stur            w1, [x3, #7]
    // 0x1507b24: r1 = Null
    //     0x1507b24: mov             x1, NULL
    // 0x1507b28: r2 = 4
    //     0x1507b28: movz            x2, #0x4
    // 0x1507b2c: r0 = AllocateArray()
    //     0x1507b2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1507b30: mov             x2, x0
    // 0x1507b34: ldur            x0, [fp, #-0x30]
    // 0x1507b38: stur            x2, [fp, #-0x28]
    // 0x1507b3c: StoreField: r2->field_f = r0
    //     0x1507b3c: stur            w0, [x2, #0xf]
    // 0x1507b40: ldur            x0, [fp, #-0x38]
    // 0x1507b44: StoreField: r2->field_13 = r0
    //     0x1507b44: stur            w0, [x2, #0x13]
    // 0x1507b48: r1 = <InlineSpan>
    //     0x1507b48: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x1507b4c: ldr             x1, [x1, #0xe40]
    // 0x1507b50: r0 = AllocateGrowableArray()
    //     0x1507b50: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1507b54: mov             x1, x0
    // 0x1507b58: ldur            x0, [fp, #-0x28]
    // 0x1507b5c: stur            x1, [fp, #-0x30]
    // 0x1507b60: StoreField: r1->field_f = r0
    //     0x1507b60: stur            w0, [x1, #0xf]
    // 0x1507b64: r0 = 4
    //     0x1507b64: movz            x0, #0x4
    // 0x1507b68: StoreField: r1->field_b = r0
    //     0x1507b68: stur            w0, [x1, #0xb]
    // 0x1507b6c: r0 = TextSpan()
    //     0x1507b6c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1507b70: mov             x1, x0
    // 0x1507b74: ldur            x0, [fp, #-0x30]
    // 0x1507b78: stur            x1, [fp, #-0x28]
    // 0x1507b7c: StoreField: r1->field_f = r0
    //     0x1507b7c: stur            w0, [x1, #0xf]
    // 0x1507b80: r0 = Instance__DeferringMouseCursor
    //     0x1507b80: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1507b84: ArrayStore: r1[0] = r0  ; List_4
    //     0x1507b84: stur            w0, [x1, #0x17]
    // 0x1507b88: r0 = RichText()
    //     0x1507b88: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x1507b8c: mov             x1, x0
    // 0x1507b90: ldur            x2, [fp, #-0x28]
    // 0x1507b94: stur            x0, [fp, #-0x28]
    // 0x1507b98: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x1507b98: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x1507b9c: r0 = RichText()
    //     0x1507b9c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x1507ba0: r0 = Padding()
    //     0x1507ba0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1507ba4: mov             x2, x0
    // 0x1507ba8: r0 = Instance_EdgeInsets
    //     0x1507ba8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x1507bac: ldr             x0, [x0, #0x980]
    // 0x1507bb0: stur            x2, [fp, #-0x30]
    // 0x1507bb4: StoreField: r2->field_f = r0
    //     0x1507bb4: stur            w0, [x2, #0xf]
    // 0x1507bb8: ldur            x1, [fp, #-0x28]
    // 0x1507bbc: StoreField: r2->field_b = r1
    //     0x1507bbc: stur            w1, [x2, #0xb]
    // 0x1507bc0: ldur            x3, [fp, #-8]
    // 0x1507bc4: LoadField: r1 = r3->field_f
    //     0x1507bc4: ldur            w1, [x3, #0xf]
    // 0x1507bc8: DecompressPointer r1
    //     0x1507bc8: add             x1, x1, HEAP, lsl #32
    // 0x1507bcc: r0 = controller()
    //     0x1507bcc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1507bd0: LoadField: r1 = r0->field_57
    //     0x1507bd0: ldur            w1, [x0, #0x57]
    // 0x1507bd4: DecompressPointer r1
    //     0x1507bd4: add             x1, x1, HEAP, lsl #32
    // 0x1507bd8: r0 = value()
    //     0x1507bd8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1507bdc: cmp             w0, NULL
    // 0x1507be0: b.ne            #0x1507bec
    // 0x1507be4: r7 = Null
    //     0x1507be4: mov             x7, NULL
    // 0x1507be8: b               #0x1507bfc
    // 0x1507bec: LoadField: r1 = r0->field_7
    //     0x1507bec: ldur            w1, [x0, #7]
    // 0x1507bf0: DecompressPointer r1
    //     0x1507bf0: add             x1, x1, HEAP, lsl #32
    // 0x1507bf4: LoadField: r0 = r1->field_b
    //     0x1507bf4: ldur            w0, [x1, #0xb]
    // 0x1507bf8: mov             x7, x0
    // 0x1507bfc: ldur            x3, [fp, #-8]
    // 0x1507c00: ldur            x6, [fp, #-0x18]
    // 0x1507c04: ldur            x5, [fp, #-0x10]
    // 0x1507c08: ldur            x4, [fp, #-0x20]
    // 0x1507c0c: ldur            x0, [fp, #-0x30]
    // 0x1507c10: mov             x2, x3
    // 0x1507c14: stur            x7, [fp, #-0x28]
    // 0x1507c18: r1 = Function '<anonymous closure>':.
    //     0x1507c18: add             x1, PP, #0x33, lsl #12  ; [pp+0x33fd0] AnonymousClosure: (0x1508bb4), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x1506e38)
    //     0x1507c1c: ldr             x1, [x1, #0xfd0]
    // 0x1507c20: r0 = AllocateClosure()
    //     0x1507c20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1507c24: stur            x0, [fp, #-0x38]
    // 0x1507c28: r0 = ListView()
    //     0x1507c28: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x1507c2c: stur            x0, [fp, #-0x40]
    // 0x1507c30: r16 = true
    //     0x1507c30: add             x16, NULL, #0x20  ; true
    // 0x1507c34: r30 = false
    //     0x1507c34: add             lr, NULL, #0x30  ; false
    // 0x1507c38: stp             lr, x16, [SP, #8]
    // 0x1507c3c: r16 = Instance_NeverScrollableScrollPhysics
    //     0x1507c3c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x1507c40: ldr             x16, [x16, #0x1c8]
    // 0x1507c44: str             x16, [SP]
    // 0x1507c48: mov             x1, x0
    // 0x1507c4c: ldur            x2, [fp, #-0x38]
    // 0x1507c50: ldur            x3, [fp, #-0x28]
    // 0x1507c54: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x5, primary, 0x4, shrinkWrap, 0x3, null]
    //     0x1507c54: add             x4, PP, #0x33, lsl #12  ; [pp+0x33fd8] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x5, "primary", 0x4, "shrinkWrap", 0x3, Null]
    //     0x1507c58: ldr             x4, [x4, #0xfd8]
    // 0x1507c5c: r0 = ListView.builder()
    //     0x1507c5c: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x1507c60: r0 = Padding()
    //     0x1507c60: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1507c64: mov             x3, x0
    // 0x1507c68: r0 = Instance_EdgeInsets
    //     0x1507c68: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x1507c6c: ldr             x0, [x0, #0x980]
    // 0x1507c70: stur            x3, [fp, #-0x28]
    // 0x1507c74: StoreField: r3->field_f = r0
    //     0x1507c74: stur            w0, [x3, #0xf]
    // 0x1507c78: ldur            x1, [fp, #-0x40]
    // 0x1507c7c: StoreField: r3->field_b = r1
    //     0x1507c7c: stur            w1, [x3, #0xb]
    // 0x1507c80: r1 = Null
    //     0x1507c80: mov             x1, NULL
    // 0x1507c84: r2 = 10
    //     0x1507c84: movz            x2, #0xa
    // 0x1507c88: r0 = AllocateArray()
    //     0x1507c88: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1507c8c: mov             x2, x0
    // 0x1507c90: ldur            x0, [fp, #-0x18]
    // 0x1507c94: stur            x2, [fp, #-0x38]
    // 0x1507c98: StoreField: r2->field_f = r0
    //     0x1507c98: stur            w0, [x2, #0xf]
    // 0x1507c9c: ldur            x0, [fp, #-0x10]
    // 0x1507ca0: StoreField: r2->field_13 = r0
    //     0x1507ca0: stur            w0, [x2, #0x13]
    // 0x1507ca4: ldur            x0, [fp, #-0x20]
    // 0x1507ca8: ArrayStore: r2[0] = r0  ; List_4
    //     0x1507ca8: stur            w0, [x2, #0x17]
    // 0x1507cac: ldur            x0, [fp, #-0x30]
    // 0x1507cb0: StoreField: r2->field_1b = r0
    //     0x1507cb0: stur            w0, [x2, #0x1b]
    // 0x1507cb4: ldur            x0, [fp, #-0x28]
    // 0x1507cb8: StoreField: r2->field_1f = r0
    //     0x1507cb8: stur            w0, [x2, #0x1f]
    // 0x1507cbc: r1 = <Widget>
    //     0x1507cbc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1507cc0: r0 = AllocateGrowableArray()
    //     0x1507cc0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1507cc4: mov             x2, x0
    // 0x1507cc8: ldur            x0, [fp, #-0x38]
    // 0x1507ccc: stur            x2, [fp, #-0x10]
    // 0x1507cd0: StoreField: r2->field_f = r0
    //     0x1507cd0: stur            w0, [x2, #0xf]
    // 0x1507cd4: r0 = 10
    //     0x1507cd4: movz            x0, #0xa
    // 0x1507cd8: StoreField: r2->field_b = r0
    //     0x1507cd8: stur            w0, [x2, #0xb]
    // 0x1507cdc: ldur            x0, [fp, #-8]
    // 0x1507ce0: LoadField: r1 = r0->field_f
    //     0x1507ce0: ldur            w1, [x0, #0xf]
    // 0x1507ce4: DecompressPointer r1
    //     0x1507ce4: add             x1, x1, HEAP, lsl #32
    // 0x1507ce8: r0 = controller()
    //     0x1507ce8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1507cec: LoadField: r1 = r0->field_67
    //     0x1507cec: ldur            w1, [x0, #0x67]
    // 0x1507cf0: DecompressPointer r1
    //     0x1507cf0: add             x1, x1, HEAP, lsl #32
    // 0x1507cf4: r0 = value()
    //     0x1507cf4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1507cf8: r1 = LoadClassIdInstr(r0)
    //     0x1507cf8: ldur            x1, [x0, #-1]
    //     0x1507cfc: ubfx            x1, x1, #0xc, #0x14
    // 0x1507d00: r16 = "OTHER"
    //     0x1507d00: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f68] "OTHER"
    //     0x1507d04: ldr             x16, [x16, #0xf68]
    // 0x1507d08: stp             x16, x0, [SP]
    // 0x1507d0c: mov             x0, x1
    // 0x1507d10: mov             lr, x0
    // 0x1507d14: ldr             lr, [x21, lr, lsl #3]
    // 0x1507d18: blr             lr
    // 0x1507d1c: tbz             w0, #4, #0x1507d6c
    // 0x1507d20: ldur            x2, [fp, #-8]
    // 0x1507d24: LoadField: r1 = r2->field_f
    //     0x1507d24: ldur            w1, [x2, #0xf]
    // 0x1507d28: DecompressPointer r1
    //     0x1507d28: add             x1, x1, HEAP, lsl #32
    // 0x1507d2c: r0 = controller()
    //     0x1507d2c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1507d30: LoadField: r1 = r0->field_57
    //     0x1507d30: ldur            w1, [x0, #0x57]
    // 0x1507d34: DecompressPointer r1
    //     0x1507d34: add             x1, x1, HEAP, lsl #32
    // 0x1507d38: r0 = value()
    //     0x1507d38: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1507d3c: cmp             w0, NULL
    // 0x1507d40: b.ne            #0x1507d4c
    // 0x1507d44: r0 = Null
    //     0x1507d44: mov             x0, NULL
    // 0x1507d48: b               #0x1507d58
    // 0x1507d4c: LoadField: r1 = r0->field_23
    //     0x1507d4c: ldur            w1, [x0, #0x23]
    // 0x1507d50: DecompressPointer r1
    //     0x1507d50: add             x1, x1, HEAP, lsl #32
    // 0x1507d54: mov             x0, x1
    // 0x1507d58: cmp             w0, NULL
    // 0x1507d5c: b.ne            #0x1507d68
    // 0x1507d60: ldur            x2, [fp, #-0x10]
    // 0x1507d64: b               #0x150808c
    // 0x1507d68: tbz             w0, #4, #0x1508088
    // 0x1507d6c: ldur            x2, [fp, #-8]
    // 0x1507d70: ldur            x0, [fp, #-0x10]
    // 0x1507d74: LoadField: r1 = r2->field_f
    //     0x1507d74: ldur            w1, [x2, #0xf]
    // 0x1507d78: DecompressPointer r1
    //     0x1507d78: add             x1, x1, HEAP, lsl #32
    // 0x1507d7c: r0 = controller()
    //     0x1507d7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1507d80: LoadField: r1 = r0->field_d7
    //     0x1507d80: ldur            w1, [x0, #0xd7]
    // 0x1507d84: DecompressPointer r1
    //     0x1507d84: add             x1, x1, HEAP, lsl #32
    // 0x1507d88: ldur            x2, [fp, #-8]
    // 0x1507d8c: stur            x1, [fp, #-0x20]
    // 0x1507d90: LoadField: r0 = r2->field_f
    //     0x1507d90: ldur            w0, [x2, #0xf]
    // 0x1507d94: DecompressPointer r0
    //     0x1507d94: add             x0, x0, HEAP, lsl #32
    // 0x1507d98: stur            x0, [fp, #-0x18]
    // 0x1507d9c: r0 = LengthLimitingTextInputFormatter()
    //     0x1507d9c: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0x1507da0: mov             x3, x0
    // 0x1507da4: r0 = 300
    //     0x1507da4: movz            x0, #0x12c
    // 0x1507da8: stur            x3, [fp, #-0x28]
    // 0x1507dac: StoreField: r3->field_7 = r0
    //     0x1507dac: stur            w0, [x3, #7]
    // 0x1507db0: r1 = Null
    //     0x1507db0: mov             x1, NULL
    // 0x1507db4: r2 = 2
    //     0x1507db4: movz            x2, #0x2
    // 0x1507db8: r0 = AllocateArray()
    //     0x1507db8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1507dbc: mov             x2, x0
    // 0x1507dc0: ldur            x0, [fp, #-0x28]
    // 0x1507dc4: stur            x2, [fp, #-0x30]
    // 0x1507dc8: StoreField: r2->field_f = r0
    //     0x1507dc8: stur            w0, [x2, #0xf]
    // 0x1507dcc: r1 = <TextInputFormatter>
    //     0x1507dcc: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0x1507dd0: ldr             x1, [x1, #0x7b0]
    // 0x1507dd4: r0 = AllocateGrowableArray()
    //     0x1507dd4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1507dd8: mov             x2, x0
    // 0x1507ddc: ldur            x0, [fp, #-0x30]
    // 0x1507de0: stur            x2, [fp, #-0x28]
    // 0x1507de4: StoreField: r2->field_f = r0
    //     0x1507de4: stur            w0, [x2, #0xf]
    // 0x1507de8: r0 = 2
    //     0x1507de8: movz            x0, #0x2
    // 0x1507dec: StoreField: r2->field_b = r0
    //     0x1507dec: stur            w0, [x2, #0xb]
    // 0x1507df0: ldur            x3, [fp, #-8]
    // 0x1507df4: LoadField: r1 = r3->field_13
    //     0x1507df4: ldur            w1, [x3, #0x13]
    // 0x1507df8: DecompressPointer r1
    //     0x1507df8: add             x1, x1, HEAP, lsl #32
    // 0x1507dfc: r0 = of()
    //     0x1507dfc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1507e00: LoadField: r1 = r0->field_87
    //     0x1507e00: ldur            w1, [x0, #0x87]
    // 0x1507e04: DecompressPointer r1
    //     0x1507e04: add             x1, x1, HEAP, lsl #32
    // 0x1507e08: LoadField: r0 = r1->field_2b
    //     0x1507e08: ldur            w0, [x1, #0x2b]
    // 0x1507e0c: DecompressPointer r0
    //     0x1507e0c: add             x0, x0, HEAP, lsl #32
    // 0x1507e10: ldur            x2, [fp, #-8]
    // 0x1507e14: stur            x0, [fp, #-0x30]
    // 0x1507e18: LoadField: r1 = r2->field_13
    //     0x1507e18: ldur            w1, [x2, #0x13]
    // 0x1507e1c: DecompressPointer r1
    //     0x1507e1c: add             x1, x1, HEAP, lsl #32
    // 0x1507e20: r0 = of()
    //     0x1507e20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1507e24: LoadField: r1 = r0->field_5b
    //     0x1507e24: ldur            w1, [x0, #0x5b]
    // 0x1507e28: DecompressPointer r1
    //     0x1507e28: add             x1, x1, HEAP, lsl #32
    // 0x1507e2c: r16 = 12.000000
    //     0x1507e2c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1507e30: ldr             x16, [x16, #0x9e8]
    // 0x1507e34: stp             x16, x1, [SP]
    // 0x1507e38: ldur            x1, [fp, #-0x30]
    // 0x1507e3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1507e3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1507e40: ldr             x4, [x4, #0x9b8]
    // 0x1507e44: r0 = copyWith()
    //     0x1507e44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1507e48: ldur            x2, [fp, #-8]
    // 0x1507e4c: stur            x0, [fp, #-0x30]
    // 0x1507e50: LoadField: r1 = r2->field_f
    //     0x1507e50: ldur            w1, [x2, #0xf]
    // 0x1507e54: DecompressPointer r1
    //     0x1507e54: add             x1, x1, HEAP, lsl #32
    // 0x1507e58: r0 = controller()
    //     0x1507e58: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1507e5c: LoadField: r2 = r0->field_db
    //     0x1507e5c: ldur            w2, [x0, #0xdb]
    // 0x1507e60: DecompressPointer r2
    //     0x1507e60: add             x2, x2, HEAP, lsl #32
    // 0x1507e64: ldur            x0, [fp, #-8]
    // 0x1507e68: stur            x2, [fp, #-0x38]
    // 0x1507e6c: LoadField: r1 = r0->field_13
    //     0x1507e6c: ldur            w1, [x0, #0x13]
    // 0x1507e70: DecompressPointer r1
    //     0x1507e70: add             x1, x1, HEAP, lsl #32
    // 0x1507e74: r0 = getTextFormFieldInputDecoration()
    //     0x1507e74: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0x1507e78: ldur            x2, [fp, #-8]
    // 0x1507e7c: stur            x0, [fp, #-0x40]
    // 0x1507e80: LoadField: r1 = r2->field_13
    //     0x1507e80: ldur            w1, [x2, #0x13]
    // 0x1507e84: DecompressPointer r1
    //     0x1507e84: add             x1, x1, HEAP, lsl #32
    // 0x1507e88: r0 = of()
    //     0x1507e88: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1507e8c: LoadField: r1 = r0->field_87
    //     0x1507e8c: ldur            w1, [x0, #0x87]
    // 0x1507e90: DecompressPointer r1
    //     0x1507e90: add             x1, x1, HEAP, lsl #32
    // 0x1507e94: LoadField: r0 = r1->field_2b
    //     0x1507e94: ldur            w0, [x1, #0x2b]
    // 0x1507e98: DecompressPointer r0
    //     0x1507e98: add             x0, x0, HEAP, lsl #32
    // 0x1507e9c: stur            x0, [fp, #-0x48]
    // 0x1507ea0: r1 = Instance_Color
    //     0x1507ea0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1507ea4: d0 = 0.400000
    //     0x1507ea4: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1507ea8: r0 = withOpacity()
    //     0x1507ea8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1507eac: r16 = 12.000000
    //     0x1507eac: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1507eb0: ldr             x16, [x16, #0x9e8]
    // 0x1507eb4: stp             x16, x0, [SP]
    // 0x1507eb8: ldur            x1, [fp, #-0x48]
    // 0x1507ebc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1507ebc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1507ec0: ldr             x4, [x4, #0x9b8]
    // 0x1507ec4: r0 = copyWith()
    //     0x1507ec4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1507ec8: ldur            x2, [fp, #-8]
    // 0x1507ecc: stur            x0, [fp, #-0x48]
    // 0x1507ed0: LoadField: r1 = r2->field_13
    //     0x1507ed0: ldur            w1, [x2, #0x13]
    // 0x1507ed4: DecompressPointer r1
    //     0x1507ed4: add             x1, x1, HEAP, lsl #32
    // 0x1507ed8: r0 = of()
    //     0x1507ed8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1507edc: LoadField: r1 = r0->field_87
    //     0x1507edc: ldur            w1, [x0, #0x87]
    // 0x1507ee0: DecompressPointer r1
    //     0x1507ee0: add             x1, x1, HEAP, lsl #32
    // 0x1507ee4: LoadField: r0 = r1->field_2b
    //     0x1507ee4: ldur            w0, [x1, #0x2b]
    // 0x1507ee8: DecompressPointer r0
    //     0x1507ee8: add             x0, x0, HEAP, lsl #32
    // 0x1507eec: r16 = Instance_Color
    //     0x1507eec: add             x16, PP, #0x33, lsl #12  ; [pp+0x337c0] Obj!Color@d6b0d1
    //     0x1507ef0: ldr             x16, [x16, #0x7c0]
    // 0x1507ef4: r30 = 12.000000
    //     0x1507ef4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1507ef8: ldr             lr, [lr, #0x9e8]
    // 0x1507efc: stp             lr, x16, [SP]
    // 0x1507f00: mov             x1, x0
    // 0x1507f04: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1507f04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1507f08: ldr             x4, [x4, #0x9b8]
    // 0x1507f0c: r0 = copyWith()
    //     0x1507f0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1507f10: r16 = "Remark: Describe your issue in detail"
    //     0x1507f10: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fe0] "Remark: Describe your issue in detail"
    //     0x1507f14: ldr             x16, [x16, #0xfe0]
    // 0x1507f18: ldur            lr, [fp, #-0x48]
    // 0x1507f1c: stp             lr, x16, [SP, #8]
    // 0x1507f20: str             x0, [SP]
    // 0x1507f24: ldur            x1, [fp, #-0x40]
    // 0x1507f28: r4 = const [0, 0x4, 0x3, 0x1, errorStyle, 0x3, hintStyle, 0x2, hintText, 0x1, null]
    //     0x1507f28: add             x4, PP, #0x33, lsl #12  ; [pp+0x33fe8] List(11) [0, 0x4, 0x3, 0x1, "errorStyle", 0x3, "hintStyle", 0x2, "hintText", 0x1, Null]
    //     0x1507f2c: ldr             x4, [x4, #0xfe8]
    // 0x1507f30: r0 = copyWith()
    //     0x1507f30: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0x1507f34: ldur            x2, [fp, #-0x18]
    // 0x1507f38: r1 = Function '_validateOther@1728293881':.
    //     0x1507f38: add             x1, PP, #0x33, lsl #12  ; [pp+0x33ff0] AnonymousClosure: (0xb51164), of [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_view.dart] ReturnOrderView
    //     0x1507f3c: ldr             x1, [x1, #0xff0]
    // 0x1507f40: stur            x0, [fp, #-0x18]
    // 0x1507f44: r0 = AllocateClosure()
    //     0x1507f44: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1507f48: ldur            x2, [fp, #-8]
    // 0x1507f4c: r1 = Function '<anonymous closure>':.
    //     0x1507f4c: add             x1, PP, #0x33, lsl #12  ; [pp+0x33ff8] AnonymousClosure: (0x1508af0), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x1506e38)
    //     0x1507f50: ldr             x1, [x1, #0xff8]
    // 0x1507f54: stur            x0, [fp, #-0x40]
    // 0x1507f58: r0 = AllocateClosure()
    //     0x1507f58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1507f5c: r1 = <String>
    //     0x1507f5c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x1507f60: stur            x0, [fp, #-0x48]
    // 0x1507f64: r0 = TextFormField()
    //     0x1507f64: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0x1507f68: stur            x0, [fp, #-0x50]
    // 0x1507f6c: ldur            x16, [fp, #-0x40]
    // 0x1507f70: r30 = false
    //     0x1507f70: add             lr, NULL, #0x30  ; false
    // 0x1507f74: stp             lr, x16, [SP, #0x40]
    // 0x1507f78: r16 = Instance_AutovalidateMode
    //     0x1507f78: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0x1507f7c: ldr             x16, [x16, #0x7e8]
    // 0x1507f80: ldur            lr, [fp, #-0x28]
    // 0x1507f84: stp             lr, x16, [SP, #0x30]
    // 0x1507f88: r16 = Instance_TextInputType
    //     0x1507f88: add             x16, PP, #0x33, lsl #12  ; [pp+0x337f0] Obj!TextInputType@d55b61
    //     0x1507f8c: ldr             x16, [x16, #0x7f0]
    // 0x1507f90: r30 = 6
    //     0x1507f90: movz            lr, #0x6
    // 0x1507f94: stp             lr, x16, [SP, #0x20]
    // 0x1507f98: r16 = 10
    //     0x1507f98: movz            x16, #0xa
    // 0x1507f9c: ldur            lr, [fp, #-0x30]
    // 0x1507fa0: stp             lr, x16, [SP, #0x10]
    // 0x1507fa4: ldur            x16, [fp, #-0x38]
    // 0x1507fa8: ldur            lr, [fp, #-0x48]
    // 0x1507fac: stp             lr, x16, [SP]
    // 0x1507fb0: mov             x1, x0
    // 0x1507fb4: ldur            x2, [fp, #-0x18]
    // 0x1507fb8: r4 = const [0, 0xc, 0xa, 0x2, autovalidateMode, 0x4, controller, 0xa, enableSuggestions, 0x3, inputFormatters, 0x5, keyboardType, 0x6, maxLines, 0x8, minLines, 0x7, onChanged, 0xb, style, 0x9, validator, 0x2, null]
    //     0x1507fb8: add             x4, PP, #0x33, lsl #12  ; [pp+0x337f8] List(25) [0, 0xc, 0xa, 0x2, "autovalidateMode", 0x4, "controller", 0xa, "enableSuggestions", 0x3, "inputFormatters", 0x5, "keyboardType", 0x6, "maxLines", 0x8, "minLines", 0x7, "onChanged", 0xb, "style", 0x9, "validator", 0x2, Null]
    //     0x1507fbc: ldr             x4, [x4, #0x7f8]
    // 0x1507fc0: r0 = TextFormField()
    //     0x1507fc0: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0x1507fc4: r0 = Form()
    //     0x1507fc4: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0x1507fc8: mov             x1, x0
    // 0x1507fcc: ldur            x0, [fp, #-0x50]
    // 0x1507fd0: stur            x1, [fp, #-0x18]
    // 0x1507fd4: StoreField: r1->field_b = r0
    //     0x1507fd4: stur            w0, [x1, #0xb]
    // 0x1507fd8: r0 = Instance_AutovalidateMode
    //     0x1507fd8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0x1507fdc: ldr             x0, [x0, #0x800]
    // 0x1507fe0: StoreField: r1->field_23 = r0
    //     0x1507fe0: stur            w0, [x1, #0x23]
    // 0x1507fe4: ldur            x0, [fp, #-0x20]
    // 0x1507fe8: StoreField: r1->field_7 = r0
    //     0x1507fe8: stur            w0, [x1, #7]
    // 0x1507fec: r0 = Padding()
    //     0x1507fec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1507ff0: mov             x2, x0
    // 0x1507ff4: r0 = Instance_EdgeInsets
    //     0x1507ff4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x1507ff8: ldr             x0, [x0, #0x980]
    // 0x1507ffc: stur            x2, [fp, #-0x20]
    // 0x1508000: StoreField: r2->field_f = r0
    //     0x1508000: stur            w0, [x2, #0xf]
    // 0x1508004: ldur            x1, [fp, #-0x18]
    // 0x1508008: StoreField: r2->field_b = r1
    //     0x1508008: stur            w1, [x2, #0xb]
    // 0x150800c: ldur            x3, [fp, #-0x10]
    // 0x1508010: LoadField: r1 = r3->field_b
    //     0x1508010: ldur            w1, [x3, #0xb]
    // 0x1508014: LoadField: r4 = r3->field_f
    //     0x1508014: ldur            w4, [x3, #0xf]
    // 0x1508018: DecompressPointer r4
    //     0x1508018: add             x4, x4, HEAP, lsl #32
    // 0x150801c: LoadField: r5 = r4->field_b
    //     0x150801c: ldur            w5, [x4, #0xb]
    // 0x1508020: r4 = LoadInt32Instr(r1)
    //     0x1508020: sbfx            x4, x1, #1, #0x1f
    // 0x1508024: stur            x4, [fp, #-0x58]
    // 0x1508028: r1 = LoadInt32Instr(r5)
    //     0x1508028: sbfx            x1, x5, #1, #0x1f
    // 0x150802c: cmp             x4, x1
    // 0x1508030: b.ne            #0x150803c
    // 0x1508034: mov             x1, x3
    // 0x1508038: r0 = _growToNextCapacity()
    //     0x1508038: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x150803c: ldur            x2, [fp, #-0x10]
    // 0x1508040: ldur            x3, [fp, #-0x58]
    // 0x1508044: add             x0, x3, #1
    // 0x1508048: lsl             x1, x0, #1
    // 0x150804c: StoreField: r2->field_b = r1
    //     0x150804c: stur            w1, [x2, #0xb]
    // 0x1508050: LoadField: r1 = r2->field_f
    //     0x1508050: ldur            w1, [x2, #0xf]
    // 0x1508054: DecompressPointer r1
    //     0x1508054: add             x1, x1, HEAP, lsl #32
    // 0x1508058: ldur            x0, [fp, #-0x20]
    // 0x150805c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x150805c: add             x25, x1, x3, lsl #2
    //     0x1508060: add             x25, x25, #0xf
    //     0x1508064: str             w0, [x25]
    //     0x1508068: tbz             w0, #0, #0x1508084
    //     0x150806c: ldurb           w16, [x1, #-1]
    //     0x1508070: ldurb           w17, [x0, #-1]
    //     0x1508074: and             x16, x17, x16, lsr #2
    //     0x1508078: tst             x16, HEAP, lsr #32
    //     0x150807c: b.eq            #0x1508084
    //     0x1508080: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1508084: b               #0x1508118
    // 0x1508088: ldur            x2, [fp, #-0x10]
    // 0x150808c: r0 = Container()
    //     0x150808c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1508090: mov             x1, x0
    // 0x1508094: stur            x0, [fp, #-0x18]
    // 0x1508098: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1508098: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x150809c: r0 = Container()
    //     0x150809c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15080a0: ldur            x0, [fp, #-0x10]
    // 0x15080a4: LoadField: r1 = r0->field_b
    //     0x15080a4: ldur            w1, [x0, #0xb]
    // 0x15080a8: LoadField: r2 = r0->field_f
    //     0x15080a8: ldur            w2, [x0, #0xf]
    // 0x15080ac: DecompressPointer r2
    //     0x15080ac: add             x2, x2, HEAP, lsl #32
    // 0x15080b0: LoadField: r3 = r2->field_b
    //     0x15080b0: ldur            w3, [x2, #0xb]
    // 0x15080b4: r2 = LoadInt32Instr(r1)
    //     0x15080b4: sbfx            x2, x1, #1, #0x1f
    // 0x15080b8: stur            x2, [fp, #-0x58]
    // 0x15080bc: r1 = LoadInt32Instr(r3)
    //     0x15080bc: sbfx            x1, x3, #1, #0x1f
    // 0x15080c0: cmp             x2, x1
    // 0x15080c4: b.ne            #0x15080d0
    // 0x15080c8: mov             x1, x0
    // 0x15080cc: r0 = _growToNextCapacity()
    //     0x15080cc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x15080d0: ldur            x2, [fp, #-0x10]
    // 0x15080d4: ldur            x3, [fp, #-0x58]
    // 0x15080d8: add             x0, x3, #1
    // 0x15080dc: lsl             x1, x0, #1
    // 0x15080e0: StoreField: r2->field_b = r1
    //     0x15080e0: stur            w1, [x2, #0xb]
    // 0x15080e4: LoadField: r1 = r2->field_f
    //     0x15080e4: ldur            w1, [x2, #0xf]
    // 0x15080e8: DecompressPointer r1
    //     0x15080e8: add             x1, x1, HEAP, lsl #32
    // 0x15080ec: ldur            x0, [fp, #-0x18]
    // 0x15080f0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x15080f0: add             x25, x1, x3, lsl #2
    //     0x15080f4: add             x25, x25, #0xf
    //     0x15080f8: str             w0, [x25]
    //     0x15080fc: tbz             w0, #0, #0x1508118
    //     0x1508100: ldurb           w16, [x1, #-1]
    //     0x1508104: ldurb           w17, [x0, #-1]
    //     0x1508108: and             x16, x17, x16, lsr #2
    //     0x150810c: tst             x16, HEAP, lsr #32
    //     0x1508110: b.eq            #0x1508118
    //     0x1508114: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1508118: ldur            x0, [fp, #-8]
    // 0x150811c: LoadField: r1 = r0->field_f
    //     0x150811c: ldur            w1, [x0, #0xf]
    // 0x1508120: DecompressPointer r1
    //     0x1508120: add             x1, x1, HEAP, lsl #32
    // 0x1508124: r0 = controller()
    //     0x1508124: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1508128: LoadField: r1 = r0->field_57
    //     0x1508128: ldur            w1, [x0, #0x57]
    // 0x150812c: DecompressPointer r1
    //     0x150812c: add             x1, x1, HEAP, lsl #32
    // 0x1508130: r0 = value()
    //     0x1508130: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1508134: cmp             w0, NULL
    // 0x1508138: b.ne            #0x1508144
    // 0x150813c: r0 = Null
    //     0x150813c: mov             x0, NULL
    // 0x1508140: b               #0x1508150
    // 0x1508144: LoadField: r1 = r0->field_23
    //     0x1508144: ldur            w1, [x0, #0x23]
    // 0x1508148: DecompressPointer r1
    //     0x1508148: add             x1, x1, HEAP, lsl #32
    // 0x150814c: mov             x0, x1
    // 0x1508150: cmp             w0, NULL
    // 0x1508154: b.ne            #0x1508160
    // 0x1508158: r1 = true
    //     0x1508158: add             x1, NULL, #0x20  ; true
    // 0x150815c: b               #0x1508164
    // 0x1508160: mov             x1, x0
    // 0x1508164: ldur            x0, [fp, #-8]
    // 0x1508168: eor             x2, x1, #0x10
    // 0x150816c: stur            x2, [fp, #-0x18]
    // 0x1508170: r1 = Instance_Color
    //     0x1508170: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1508174: d0 = 0.100000
    //     0x1508174: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x1508178: r0 = withOpacity()
    //     0x1508178: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x150817c: r16 = 1.000000
    //     0x150817c: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x1508180: str             x16, [SP]
    // 0x1508184: mov             x2, x0
    // 0x1508188: r1 = Null
    //     0x1508188: mov             x1, NULL
    // 0x150818c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x150818c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x1508190: ldr             x4, [x4, #0x108]
    // 0x1508194: r0 = Border.all()
    //     0x1508194: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x1508198: stur            x0, [fp, #-0x20]
    // 0x150819c: r0 = BoxDecoration()
    //     0x150819c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x15081a0: mov             x2, x0
    // 0x15081a4: ldur            x0, [fp, #-0x20]
    // 0x15081a8: stur            x2, [fp, #-0x28]
    // 0x15081ac: StoreField: r2->field_f = r0
    //     0x15081ac: stur            w0, [x2, #0xf]
    // 0x15081b0: r0 = Instance_BoxShape
    //     0x15081b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15081b4: ldr             x0, [x0, #0x80]
    // 0x15081b8: StoreField: r2->field_23 = r0
    //     0x15081b8: stur            w0, [x2, #0x23]
    // 0x15081bc: ldur            x0, [fp, #-8]
    // 0x15081c0: LoadField: r1 = r0->field_f
    //     0x15081c0: ldur            w1, [x0, #0xf]
    // 0x15081c4: DecompressPointer r1
    //     0x15081c4: add             x1, x1, HEAP, lsl #32
    // 0x15081c8: r0 = controller()
    //     0x15081c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15081cc: LoadField: r1 = r0->field_57
    //     0x15081cc: ldur            w1, [x0, #0x57]
    // 0x15081d0: DecompressPointer r1
    //     0x15081d0: add             x1, x1, HEAP, lsl #32
    // 0x15081d4: r0 = value()
    //     0x15081d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15081d8: cmp             w0, NULL
    // 0x15081dc: b.ne            #0x15081e8
    // 0x15081e0: r0 = Null
    //     0x15081e0: mov             x0, NULL
    // 0x15081e4: b               #0x1508208
    // 0x15081e8: LoadField: r1 = r0->field_13
    //     0x15081e8: ldur            w1, [x0, #0x13]
    // 0x15081ec: DecompressPointer r1
    //     0x15081ec: add             x1, x1, HEAP, lsl #32
    // 0x15081f0: cmp             w1, NULL
    // 0x15081f4: b.ne            #0x1508200
    // 0x15081f8: r0 = Null
    //     0x15081f8: mov             x0, NULL
    // 0x15081fc: b               #0x1508208
    // 0x1508200: LoadField: r0 = r1->field_7
    //     0x1508200: ldur            w0, [x1, #7]
    // 0x1508204: DecompressPointer r0
    //     0x1508204: add             x0, x0, HEAP, lsl #32
    // 0x1508208: cmp             w0, NULL
    // 0x150820c: b.ne            #0x1508218
    // 0x1508210: r2 = ""
    //     0x1508210: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1508214: b               #0x150821c
    // 0x1508218: mov             x2, x0
    // 0x150821c: ldur            x0, [fp, #-8]
    // 0x1508220: stur            x2, [fp, #-0x20]
    // 0x1508224: LoadField: r1 = r0->field_13
    //     0x1508224: ldur            w1, [x0, #0x13]
    // 0x1508228: DecompressPointer r1
    //     0x1508228: add             x1, x1, HEAP, lsl #32
    // 0x150822c: r0 = of()
    //     0x150822c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1508230: LoadField: r1 = r0->field_87
    //     0x1508230: ldur            w1, [x0, #0x87]
    // 0x1508234: DecompressPointer r1
    //     0x1508234: add             x1, x1, HEAP, lsl #32
    // 0x1508238: LoadField: r0 = r1->field_7
    //     0x1508238: ldur            w0, [x1, #7]
    // 0x150823c: DecompressPointer r0
    //     0x150823c: add             x0, x0, HEAP, lsl #32
    // 0x1508240: r16 = 14.000000
    //     0x1508240: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1508244: ldr             x16, [x16, #0x1d8]
    // 0x1508248: r30 = Instance_Color
    //     0x1508248: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x150824c: stp             lr, x16, [SP]
    // 0x1508250: mov             x1, x0
    // 0x1508254: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1508254: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1508258: ldr             x4, [x4, #0xaa0]
    // 0x150825c: r0 = copyWith()
    //     0x150825c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1508260: stur            x0, [fp, #-0x30]
    // 0x1508264: r0 = Text()
    //     0x1508264: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1508268: mov             x1, x0
    // 0x150826c: ldur            x0, [fp, #-0x20]
    // 0x1508270: stur            x1, [fp, #-0x38]
    // 0x1508274: StoreField: r1->field_b = r0
    //     0x1508274: stur            w0, [x1, #0xb]
    // 0x1508278: ldur            x0, [fp, #-0x30]
    // 0x150827c: StoreField: r1->field_13 = r0
    //     0x150827c: stur            w0, [x1, #0x13]
    // 0x1508280: r0 = Padding()
    //     0x1508280: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1508284: mov             x2, x0
    // 0x1508288: r0 = Instance_EdgeInsets
    //     0x1508288: add             x0, PP, #0x33, lsl #12  ; [pp+0x33808] Obj!EdgeInsets@d592d1
    //     0x150828c: ldr             x0, [x0, #0x808]
    // 0x1508290: stur            x2, [fp, #-0x20]
    // 0x1508294: StoreField: r2->field_f = r0
    //     0x1508294: stur            w0, [x2, #0xf]
    // 0x1508298: ldur            x0, [fp, #-0x38]
    // 0x150829c: StoreField: r2->field_b = r0
    //     0x150829c: stur            w0, [x2, #0xb]
    // 0x15082a0: ldur            x0, [fp, #-8]
    // 0x15082a4: LoadField: r1 = r0->field_f
    //     0x15082a4: ldur            w1, [x0, #0xf]
    // 0x15082a8: DecompressPointer r1
    //     0x15082a8: add             x1, x1, HEAP, lsl #32
    // 0x15082ac: r0 = controller()
    //     0x15082ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15082b0: LoadField: r1 = r0->field_57
    //     0x15082b0: ldur            w1, [x0, #0x57]
    // 0x15082b4: DecompressPointer r1
    //     0x15082b4: add             x1, x1, HEAP, lsl #32
    // 0x15082b8: r0 = value()
    //     0x15082b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15082bc: cmp             w0, NULL
    // 0x15082c0: b.ne            #0x15082cc
    // 0x15082c4: r0 = Null
    //     0x15082c4: mov             x0, NULL
    // 0x15082c8: b               #0x15082ec
    // 0x15082cc: LoadField: r1 = r0->field_13
    //     0x15082cc: ldur            w1, [x0, #0x13]
    // 0x15082d0: DecompressPointer r1
    //     0x15082d0: add             x1, x1, HEAP, lsl #32
    // 0x15082d4: cmp             w1, NULL
    // 0x15082d8: b.ne            #0x15082e4
    // 0x15082dc: r0 = Null
    //     0x15082dc: mov             x0, NULL
    // 0x15082e0: b               #0x15082ec
    // 0x15082e4: LoadField: r0 = r1->field_b
    //     0x15082e4: ldur            w0, [x1, #0xb]
    // 0x15082e8: DecompressPointer r0
    //     0x15082e8: add             x0, x0, HEAP, lsl #32
    // 0x15082ec: cmp             w0, NULL
    // 0x15082f0: b.ne            #0x15082fc
    // 0x15082f4: r2 = ""
    //     0x15082f4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15082f8: b               #0x1508300
    // 0x15082fc: mov             x2, x0
    // 0x1508300: ldur            x0, [fp, #-8]
    // 0x1508304: stur            x2, [fp, #-0x30]
    // 0x1508308: LoadField: r1 = r0->field_13
    //     0x1508308: ldur            w1, [x0, #0x13]
    // 0x150830c: DecompressPointer r1
    //     0x150830c: add             x1, x1, HEAP, lsl #32
    // 0x1508310: r0 = of()
    //     0x1508310: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1508314: LoadField: r1 = r0->field_87
    //     0x1508314: ldur            w1, [x0, #0x87]
    // 0x1508318: DecompressPointer r1
    //     0x1508318: add             x1, x1, HEAP, lsl #32
    // 0x150831c: LoadField: r0 = r1->field_2b
    //     0x150831c: ldur            w0, [x1, #0x2b]
    // 0x1508320: DecompressPointer r0
    //     0x1508320: add             x0, x0, HEAP, lsl #32
    // 0x1508324: stur            x0, [fp, #-0x38]
    // 0x1508328: r1 = Instance_Color
    //     0x1508328: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x150832c: d0 = 0.700000
    //     0x150832c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1508330: ldr             d0, [x17, #0xf48]
    // 0x1508334: r0 = withOpacity()
    //     0x1508334: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1508338: r16 = 12.000000
    //     0x1508338: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x150833c: ldr             x16, [x16, #0x9e8]
    // 0x1508340: stp             x0, x16, [SP]
    // 0x1508344: ldur            x1, [fp, #-0x38]
    // 0x1508348: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1508348: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x150834c: ldr             x4, [x4, #0xaa0]
    // 0x1508350: r0 = copyWith()
    //     0x1508350: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1508354: stur            x0, [fp, #-0x38]
    // 0x1508358: r0 = Text()
    //     0x1508358: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x150835c: mov             x1, x0
    // 0x1508360: ldur            x0, [fp, #-0x30]
    // 0x1508364: stur            x1, [fp, #-0x40]
    // 0x1508368: StoreField: r1->field_b = r0
    //     0x1508368: stur            w0, [x1, #0xb]
    // 0x150836c: ldur            x0, [fp, #-0x38]
    // 0x1508370: StoreField: r1->field_13 = r0
    //     0x1508370: stur            w0, [x1, #0x13]
    // 0x1508374: r0 = Padding()
    //     0x1508374: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1508378: mov             x2, x0
    // 0x150837c: r0 = Instance_EdgeInsets
    //     0x150837c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33810] Obj!EdgeInsets@d592a1
    //     0x1508380: ldr             x0, [x0, #0x810]
    // 0x1508384: stur            x2, [fp, #-0x30]
    // 0x1508388: StoreField: r2->field_f = r0
    //     0x1508388: stur            w0, [x2, #0xf]
    // 0x150838c: ldur            x0, [fp, #-0x40]
    // 0x1508390: StoreField: r2->field_b = r0
    //     0x1508390: stur            w0, [x2, #0xb]
    // 0x1508394: ldur            x0, [fp, #-8]
    // 0x1508398: LoadField: r1 = r0->field_13
    //     0x1508398: ldur            w1, [x0, #0x13]
    // 0x150839c: DecompressPointer r1
    //     0x150839c: add             x1, x1, HEAP, lsl #32
    // 0x15083a0: r0 = of()
    //     0x15083a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15083a4: LoadField: r1 = r0->field_5b
    //     0x15083a4: ldur            w1, [x0, #0x5b]
    // 0x15083a8: DecompressPointer r1
    //     0x15083a8: add             x1, x1, HEAP, lsl #32
    // 0x15083ac: r0 = LoadClassIdInstr(r1)
    //     0x15083ac: ldur            x0, [x1, #-1]
    //     0x15083b0: ubfx            x0, x0, #0xc, #0x14
    // 0x15083b4: d0 = 0.030000
    //     0x15083b4: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x15083b8: ldr             d0, [x17, #0x238]
    // 0x15083bc: r0 = GDT[cid_x0 + -0xffa]()
    //     0x15083bc: sub             lr, x0, #0xffa
    //     0x15083c0: ldr             lr, [x21, lr, lsl #3]
    //     0x15083c4: blr             lr
    // 0x15083c8: stur            x0, [fp, #-0x38]
    // 0x15083cc: r0 = SvgPicture()
    //     0x15083cc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15083d0: stur            x0, [fp, #-0x40]
    // 0x15083d4: r16 = Instance_BoxFit
    //     0x15083d4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15083d8: ldr             x16, [x16, #0xb18]
    // 0x15083dc: r30 = Instance_ColorFilter
    //     0x15083dc: add             lr, PP, #0x33, lsl #12  ; [pp+0x33818] Obj!ColorFilter@d69801
    //     0x15083e0: ldr             lr, [lr, #0x818]
    // 0x15083e4: stp             lr, x16, [SP]
    // 0x15083e8: mov             x1, x0
    // 0x15083ec: r2 = "assets/images/shopping_bag.svg"
    //     0x15083ec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15083f0: ldr             x2, [x2, #0xa60]
    // 0x15083f4: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x15083f4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x15083f8: ldr             x4, [x4, #0x820]
    // 0x15083fc: r0 = SvgPicture.asset()
    //     0x15083fc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x1508400: ldur            x0, [fp, #-8]
    // 0x1508404: LoadField: r1 = r0->field_f
    //     0x1508404: ldur            w1, [x0, #0xf]
    // 0x1508408: DecompressPointer r1
    //     0x1508408: add             x1, x1, HEAP, lsl #32
    // 0x150840c: r0 = controller()
    //     0x150840c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1508410: LoadField: r1 = r0->field_57
    //     0x1508410: ldur            w1, [x0, #0x57]
    // 0x1508414: DecompressPointer r1
    //     0x1508414: add             x1, x1, HEAP, lsl #32
    // 0x1508418: r0 = value()
    //     0x1508418: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x150841c: cmp             w0, NULL
    // 0x1508420: b.ne            #0x150842c
    // 0x1508424: r0 = Null
    //     0x1508424: mov             x0, NULL
    // 0x1508428: b               #0x1508438
    // 0x150842c: LoadField: r1 = r0->field_1b
    //     0x150842c: ldur            w1, [x0, #0x1b]
    // 0x1508430: DecompressPointer r1
    //     0x1508430: add             x1, x1, HEAP, lsl #32
    // 0x1508434: mov             x0, x1
    // 0x1508438: cmp             w0, NULL
    // 0x150843c: b.ne            #0x1508448
    // 0x1508440: r5 = ""
    //     0x1508440: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1508444: b               #0x150844c
    // 0x1508448: mov             x5, x0
    // 0x150844c: ldur            x0, [fp, #-8]
    // 0x1508450: ldur            x4, [fp, #-0x20]
    // 0x1508454: ldur            x3, [fp, #-0x30]
    // 0x1508458: ldur            x2, [fp, #-0x40]
    // 0x150845c: stur            x5, [fp, #-0x48]
    // 0x1508460: LoadField: r1 = r0->field_13
    //     0x1508460: ldur            w1, [x0, #0x13]
    // 0x1508464: DecompressPointer r1
    //     0x1508464: add             x1, x1, HEAP, lsl #32
    // 0x1508468: r0 = of()
    //     0x1508468: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x150846c: LoadField: r1 = r0->field_87
    //     0x150846c: ldur            w1, [x0, #0x87]
    // 0x1508470: DecompressPointer r1
    //     0x1508470: add             x1, x1, HEAP, lsl #32
    // 0x1508474: LoadField: r0 = r1->field_2b
    //     0x1508474: ldur            w0, [x1, #0x2b]
    // 0x1508478: DecompressPointer r0
    //     0x1508478: add             x0, x0, HEAP, lsl #32
    // 0x150847c: stur            x0, [fp, #-0x50]
    // 0x1508480: r1 = Instance_Color
    //     0x1508480: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1508484: d0 = 0.700000
    //     0x1508484: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1508488: ldr             d0, [x17, #0xf48]
    // 0x150848c: r0 = withOpacity()
    //     0x150848c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1508490: r16 = 12.000000
    //     0x1508490: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1508494: ldr             x16, [x16, #0x9e8]
    // 0x1508498: stp             x0, x16, [SP]
    // 0x150849c: ldur            x1, [fp, #-0x50]
    // 0x15084a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15084a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15084a4: ldr             x4, [x4, #0xaa0]
    // 0x15084a8: r0 = copyWith()
    //     0x15084a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15084ac: stur            x0, [fp, #-0x50]
    // 0x15084b0: r0 = Text()
    //     0x15084b0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15084b4: mov             x2, x0
    // 0x15084b8: ldur            x0, [fp, #-0x48]
    // 0x15084bc: stur            x2, [fp, #-0x60]
    // 0x15084c0: StoreField: r2->field_b = r0
    //     0x15084c0: stur            w0, [x2, #0xb]
    // 0x15084c4: ldur            x0, [fp, #-0x50]
    // 0x15084c8: StoreField: r2->field_13 = r0
    //     0x15084c8: stur            w0, [x2, #0x13]
    // 0x15084cc: r0 = Instance_TextOverflow
    //     0x15084cc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x15084d0: ldr             x0, [x0, #0xe10]
    // 0x15084d4: StoreField: r2->field_2b = r0
    //     0x15084d4: stur            w0, [x2, #0x2b]
    // 0x15084d8: r0 = 4
    //     0x15084d8: movz            x0, #0x4
    // 0x15084dc: StoreField: r2->field_37 = r0
    //     0x15084dc: stur            w0, [x2, #0x37]
    // 0x15084e0: r1 = <FlexParentData>
    //     0x15084e0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x15084e4: ldr             x1, [x1, #0xe00]
    // 0x15084e8: r0 = Expanded()
    //     0x15084e8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x15084ec: mov             x3, x0
    // 0x15084f0: r0 = 1
    //     0x15084f0: movz            x0, #0x1
    // 0x15084f4: stur            x3, [fp, #-0x48]
    // 0x15084f8: StoreField: r3->field_13 = r0
    //     0x15084f8: stur            x0, [x3, #0x13]
    // 0x15084fc: r0 = Instance_FlexFit
    //     0x15084fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1508500: ldr             x0, [x0, #0xe08]
    // 0x1508504: StoreField: r3->field_1b = r0
    //     0x1508504: stur            w0, [x3, #0x1b]
    // 0x1508508: ldur            x0, [fp, #-0x60]
    // 0x150850c: StoreField: r3->field_b = r0
    //     0x150850c: stur            w0, [x3, #0xb]
    // 0x1508510: r1 = Null
    //     0x1508510: mov             x1, NULL
    // 0x1508514: r2 = 6
    //     0x1508514: movz            x2, #0x6
    // 0x1508518: r0 = AllocateArray()
    //     0x1508518: bl              #0x16f7198  ; AllocateArrayStub
    // 0x150851c: mov             x2, x0
    // 0x1508520: ldur            x0, [fp, #-0x40]
    // 0x1508524: stur            x2, [fp, #-0x50]
    // 0x1508528: StoreField: r2->field_f = r0
    //     0x1508528: stur            w0, [x2, #0xf]
    // 0x150852c: r16 = Instance_SizedBox
    //     0x150852c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x1508530: ldr             x16, [x16, #0xb20]
    // 0x1508534: StoreField: r2->field_13 = r16
    //     0x1508534: stur            w16, [x2, #0x13]
    // 0x1508538: ldur            x0, [fp, #-0x48]
    // 0x150853c: ArrayStore: r2[0] = r0  ; List_4
    //     0x150853c: stur            w0, [x2, #0x17]
    // 0x1508540: r1 = <Widget>
    //     0x1508540: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1508544: r0 = AllocateGrowableArray()
    //     0x1508544: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1508548: mov             x1, x0
    // 0x150854c: ldur            x0, [fp, #-0x50]
    // 0x1508550: stur            x1, [fp, #-0x40]
    // 0x1508554: StoreField: r1->field_f = r0
    //     0x1508554: stur            w0, [x1, #0xf]
    // 0x1508558: r2 = 6
    //     0x1508558: movz            x2, #0x6
    // 0x150855c: StoreField: r1->field_b = r2
    //     0x150855c: stur            w2, [x1, #0xb]
    // 0x1508560: r0 = Row()
    //     0x1508560: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1508564: mov             x1, x0
    // 0x1508568: r0 = Instance_Axis
    //     0x1508568: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x150856c: stur            x1, [fp, #-0x48]
    // 0x1508570: StoreField: r1->field_f = r0
    //     0x1508570: stur            w0, [x1, #0xf]
    // 0x1508574: r0 = Instance_MainAxisAlignment
    //     0x1508574: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1508578: ldr             x0, [x0, #0xa08]
    // 0x150857c: StoreField: r1->field_13 = r0
    //     0x150857c: stur            w0, [x1, #0x13]
    // 0x1508580: r2 = Instance_MainAxisSize
    //     0x1508580: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1508584: ldr             x2, [x2, #0xa10]
    // 0x1508588: ArrayStore: r1[0] = r2  ; List_4
    //     0x1508588: stur            w2, [x1, #0x17]
    // 0x150858c: r3 = Instance_CrossAxisAlignment
    //     0x150858c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1508590: ldr             x3, [x3, #0xa18]
    // 0x1508594: StoreField: r1->field_1b = r3
    //     0x1508594: stur            w3, [x1, #0x1b]
    // 0x1508598: r3 = Instance_VerticalDirection
    //     0x1508598: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x150859c: ldr             x3, [x3, #0xa20]
    // 0x15085a0: StoreField: r1->field_23 = r3
    //     0x15085a0: stur            w3, [x1, #0x23]
    // 0x15085a4: r4 = Instance_Clip
    //     0x15085a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15085a8: ldr             x4, [x4, #0x38]
    // 0x15085ac: StoreField: r1->field_2b = r4
    //     0x15085ac: stur            w4, [x1, #0x2b]
    // 0x15085b0: StoreField: r1->field_2f = rZR
    //     0x15085b0: stur            xzr, [x1, #0x2f]
    // 0x15085b4: ldur            x5, [fp, #-0x40]
    // 0x15085b8: StoreField: r1->field_b = r5
    //     0x15085b8: stur            w5, [x1, #0xb]
    // 0x15085bc: r0 = Padding()
    //     0x15085bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15085c0: mov             x1, x0
    // 0x15085c4: r0 = Instance_EdgeInsets
    //     0x15085c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x15085c8: ldr             x0, [x0, #0x980]
    // 0x15085cc: stur            x1, [fp, #-0x40]
    // 0x15085d0: StoreField: r1->field_f = r0
    //     0x15085d0: stur            w0, [x1, #0xf]
    // 0x15085d4: ldur            x2, [fp, #-0x48]
    // 0x15085d8: StoreField: r1->field_b = r2
    //     0x15085d8: stur            w2, [x1, #0xb]
    // 0x15085dc: r0 = Container()
    //     0x15085dc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15085e0: stur            x0, [fp, #-0x48]
    // 0x15085e4: r16 = inf
    //     0x15085e4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x15085e8: ldr             x16, [x16, #0x9f8]
    // 0x15085ec: ldur            lr, [fp, #-0x38]
    // 0x15085f0: stp             lr, x16, [SP, #8]
    // 0x15085f4: ldur            x16, [fp, #-0x40]
    // 0x15085f8: str             x16, [SP]
    // 0x15085fc: mov             x1, x0
    // 0x1508600: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x2, width, 0x1, null]
    //     0x1508600: add             x4, PP, #0x33, lsl #12  ; [pp+0x33828] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x2, "width", 0x1, Null]
    //     0x1508604: ldr             x4, [x4, #0x828]
    // 0x1508608: r0 = Container()
    //     0x1508608: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x150860c: r1 = Null
    //     0x150860c: mov             x1, NULL
    // 0x1508610: r2 = 6
    //     0x1508610: movz            x2, #0x6
    // 0x1508614: r0 = AllocateArray()
    //     0x1508614: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1508618: mov             x2, x0
    // 0x150861c: ldur            x0, [fp, #-0x20]
    // 0x1508620: stur            x2, [fp, #-0x38]
    // 0x1508624: StoreField: r2->field_f = r0
    //     0x1508624: stur            w0, [x2, #0xf]
    // 0x1508628: ldur            x0, [fp, #-0x30]
    // 0x150862c: StoreField: r2->field_13 = r0
    //     0x150862c: stur            w0, [x2, #0x13]
    // 0x1508630: ldur            x0, [fp, #-0x48]
    // 0x1508634: ArrayStore: r2[0] = r0  ; List_4
    //     0x1508634: stur            w0, [x2, #0x17]
    // 0x1508638: r1 = <Widget>
    //     0x1508638: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x150863c: r0 = AllocateGrowableArray()
    //     0x150863c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1508640: mov             x1, x0
    // 0x1508644: ldur            x0, [fp, #-0x38]
    // 0x1508648: stur            x1, [fp, #-0x20]
    // 0x150864c: StoreField: r1->field_f = r0
    //     0x150864c: stur            w0, [x1, #0xf]
    // 0x1508650: r2 = 6
    //     0x1508650: movz            x2, #0x6
    // 0x1508654: StoreField: r1->field_b = r2
    //     0x1508654: stur            w2, [x1, #0xb]
    // 0x1508658: r0 = Column()
    //     0x1508658: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x150865c: mov             x1, x0
    // 0x1508660: r0 = Instance_Axis
    //     0x1508660: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1508664: stur            x1, [fp, #-0x30]
    // 0x1508668: StoreField: r1->field_f = r0
    //     0x1508668: stur            w0, [x1, #0xf]
    // 0x150866c: r2 = Instance_MainAxisAlignment
    //     0x150866c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1508670: ldr             x2, [x2, #0xa08]
    // 0x1508674: StoreField: r1->field_13 = r2
    //     0x1508674: stur            w2, [x1, #0x13]
    // 0x1508678: r3 = Instance_MainAxisSize
    //     0x1508678: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x150867c: ldr             x3, [x3, #0xa10]
    // 0x1508680: ArrayStore: r1[0] = r3  ; List_4
    //     0x1508680: stur            w3, [x1, #0x17]
    // 0x1508684: r4 = Instance_CrossAxisAlignment
    //     0x1508684: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1508688: ldr             x4, [x4, #0x890]
    // 0x150868c: StoreField: r1->field_1b = r4
    //     0x150868c: stur            w4, [x1, #0x1b]
    // 0x1508690: r5 = Instance_VerticalDirection
    //     0x1508690: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1508694: ldr             x5, [x5, #0xa20]
    // 0x1508698: StoreField: r1->field_23 = r5
    //     0x1508698: stur            w5, [x1, #0x23]
    // 0x150869c: r6 = Instance_Clip
    //     0x150869c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15086a0: ldr             x6, [x6, #0x38]
    // 0x15086a4: StoreField: r1->field_2b = r6
    //     0x15086a4: stur            w6, [x1, #0x2b]
    // 0x15086a8: StoreField: r1->field_2f = rZR
    //     0x15086a8: stur            xzr, [x1, #0x2f]
    // 0x15086ac: ldur            x7, [fp, #-0x20]
    // 0x15086b0: StoreField: r1->field_b = r7
    //     0x15086b0: stur            w7, [x1, #0xb]
    // 0x15086b4: r0 = Container()
    //     0x15086b4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15086b8: stur            x0, [fp, #-0x20]
    // 0x15086bc: r16 = inf
    //     0x15086bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x15086c0: ldr             x16, [x16, #0x9f8]
    // 0x15086c4: r30 = Instance_EdgeInsets
    //     0x15086c4: add             lr, PP, #0x33, lsl #12  ; [pp+0x33808] Obj!EdgeInsets@d592d1
    //     0x15086c8: ldr             lr, [lr, #0x808]
    // 0x15086cc: stp             lr, x16, [SP, #0x10]
    // 0x15086d0: ldur            x16, [fp, #-0x28]
    // 0x15086d4: ldur            lr, [fp, #-0x30]
    // 0x15086d8: stp             lr, x16, [SP]
    // 0x15086dc: mov             x1, x0
    // 0x15086e0: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, margin, 0x2, width, 0x1, null]
    //     0x15086e0: add             x4, PP, #0x34, lsl #12  ; [pp+0x34000] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "margin", 0x2, "width", 0x1, Null]
    //     0x15086e4: ldr             x4, [x4]
    // 0x15086e8: r0 = Container()
    //     0x15086e8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15086ec: ldur            x0, [fp, #-8]
    // 0x15086f0: LoadField: r1 = r0->field_13
    //     0x15086f0: ldur            w1, [x0, #0x13]
    // 0x15086f4: DecompressPointer r1
    //     0x15086f4: add             x1, x1, HEAP, lsl #32
    // 0x15086f8: r0 = of()
    //     0x15086f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15086fc: LoadField: r1 = r0->field_87
    //     0x15086fc: ldur            w1, [x0, #0x87]
    // 0x1508700: DecompressPointer r1
    //     0x1508700: add             x1, x1, HEAP, lsl #32
    // 0x1508704: LoadField: r0 = r1->field_7
    //     0x1508704: ldur            w0, [x1, #7]
    // 0x1508708: DecompressPointer r0
    //     0x1508708: add             x0, x0, HEAP, lsl #32
    // 0x150870c: r16 = 14.000000
    //     0x150870c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1508710: ldr             x16, [x16, #0x1d8]
    // 0x1508714: r30 = Instance_Color
    //     0x1508714: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1508718: stp             lr, x16, [SP]
    // 0x150871c: mov             x1, x0
    // 0x1508720: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1508720: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1508724: ldr             x4, [x4, #0xaa0]
    // 0x1508728: r0 = copyWith()
    //     0x1508728: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x150872c: stur            x0, [fp, #-0x28]
    // 0x1508730: r0 = Text()
    //     0x1508730: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1508734: mov             x1, x0
    // 0x1508738: r0 = "Note:"
    //     0x1508738: add             x0, PP, #0x33, lsl #12  ; [pp+0x33838] "Note:"
    //     0x150873c: ldr             x0, [x0, #0x838]
    // 0x1508740: stur            x1, [fp, #-0x30]
    // 0x1508744: StoreField: r1->field_b = r0
    //     0x1508744: stur            w0, [x1, #0xb]
    // 0x1508748: ldur            x0, [fp, #-0x28]
    // 0x150874c: StoreField: r1->field_13 = r0
    //     0x150874c: stur            w0, [x1, #0x13]
    // 0x1508750: r0 = Padding()
    //     0x1508750: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1508754: mov             x2, x0
    // 0x1508758: r0 = Instance_EdgeInsets
    //     0x1508758: add             x0, PP, #0x34, lsl #12  ; [pp+0x34008] Obj!EdgeInsets@d59f01
    //     0x150875c: ldr             x0, [x0, #8]
    // 0x1508760: stur            x2, [fp, #-0x28]
    // 0x1508764: StoreField: r2->field_f = r0
    //     0x1508764: stur            w0, [x2, #0xf]
    // 0x1508768: ldur            x0, [fp, #-0x30]
    // 0x150876c: StoreField: r2->field_b = r0
    //     0x150876c: stur            w0, [x2, #0xb]
    // 0x1508770: ldur            x0, [fp, #-8]
    // 0x1508774: LoadField: r1 = r0->field_f
    //     0x1508774: ldur            w1, [x0, #0xf]
    // 0x1508778: DecompressPointer r1
    //     0x1508778: add             x1, x1, HEAP, lsl #32
    // 0x150877c: r0 = controller()
    //     0x150877c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1508780: LoadField: r1 = r0->field_57
    //     0x1508780: ldur            w1, [x0, #0x57]
    // 0x1508784: DecompressPointer r1
    //     0x1508784: add             x1, x1, HEAP, lsl #32
    // 0x1508788: r0 = value()
    //     0x1508788: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x150878c: cmp             w0, NULL
    // 0x1508790: b.ne            #0x150879c
    // 0x1508794: r0 = Null
    //     0x1508794: mov             x0, NULL
    // 0x1508798: b               #0x15087a8
    // 0x150879c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x150879c: ldur            w1, [x0, #0x17]
    // 0x15087a0: DecompressPointer r1
    //     0x15087a0: add             x1, x1, HEAP, lsl #32
    // 0x15087a4: mov             x0, x1
    // 0x15087a8: cmp             w0, NULL
    // 0x15087ac: b.ne            #0x15087b8
    // 0x15087b0: r5 = ""
    //     0x15087b0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15087b4: b               #0x15087bc
    // 0x15087b8: mov             x5, x0
    // 0x15087bc: ldur            x1, [fp, #-8]
    // 0x15087c0: ldur            x3, [fp, #-0x18]
    // 0x15087c4: ldur            x2, [fp, #-0x20]
    // 0x15087c8: ldur            x0, [fp, #-0x28]
    // 0x15087cc: ldur            x4, [fp, #-0x10]
    // 0x15087d0: stur            x5, [fp, #-0x30]
    // 0x15087d4: LoadField: r6 = r1->field_13
    //     0x15087d4: ldur            w6, [x1, #0x13]
    // 0x15087d8: DecompressPointer r6
    //     0x15087d8: add             x6, x6, HEAP, lsl #32
    // 0x15087dc: mov             x1, x6
    // 0x15087e0: r0 = of()
    //     0x15087e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15087e4: LoadField: r1 = r0->field_87
    //     0x15087e4: ldur            w1, [x0, #0x87]
    // 0x15087e8: DecompressPointer r1
    //     0x15087e8: add             x1, x1, HEAP, lsl #32
    // 0x15087ec: LoadField: r0 = r1->field_2b
    //     0x15087ec: ldur            w0, [x1, #0x2b]
    // 0x15087f0: DecompressPointer r0
    //     0x15087f0: add             x0, x0, HEAP, lsl #32
    // 0x15087f4: stur            x0, [fp, #-8]
    // 0x15087f8: r1 = Instance_Color
    //     0x15087f8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15087fc: d0 = 0.700000
    //     0x15087fc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1508800: ldr             d0, [x17, #0xf48]
    // 0x1508804: r0 = withOpacity()
    //     0x1508804: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1508808: r16 = 12.000000
    //     0x1508808: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x150880c: ldr             x16, [x16, #0x9e8]
    // 0x1508810: stp             x0, x16, [SP]
    // 0x1508814: ldur            x1, [fp, #-8]
    // 0x1508818: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1508818: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x150881c: ldr             x4, [x4, #0xaa0]
    // 0x1508820: r0 = copyWith()
    //     0x1508820: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1508824: stur            x0, [fp, #-8]
    // 0x1508828: r0 = Text()
    //     0x1508828: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x150882c: mov             x1, x0
    // 0x1508830: ldur            x0, [fp, #-0x30]
    // 0x1508834: stur            x1, [fp, #-0x38]
    // 0x1508838: StoreField: r1->field_b = r0
    //     0x1508838: stur            w0, [x1, #0xb]
    // 0x150883c: ldur            x0, [fp, #-8]
    // 0x1508840: StoreField: r1->field_13 = r0
    //     0x1508840: stur            w0, [x1, #0x13]
    // 0x1508844: r0 = Padding()
    //     0x1508844: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1508848: mov             x3, x0
    // 0x150884c: r0 = Instance_EdgeInsets
    //     0x150884c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x1508850: ldr             x0, [x0, #0x980]
    // 0x1508854: stur            x3, [fp, #-8]
    // 0x1508858: StoreField: r3->field_f = r0
    //     0x1508858: stur            w0, [x3, #0xf]
    // 0x150885c: ldur            x0, [fp, #-0x38]
    // 0x1508860: StoreField: r3->field_b = r0
    //     0x1508860: stur            w0, [x3, #0xb]
    // 0x1508864: r1 = Null
    //     0x1508864: mov             x1, NULL
    // 0x1508868: r2 = 6
    //     0x1508868: movz            x2, #0x6
    // 0x150886c: r0 = AllocateArray()
    //     0x150886c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1508870: mov             x2, x0
    // 0x1508874: ldur            x0, [fp, #-0x20]
    // 0x1508878: stur            x2, [fp, #-0x30]
    // 0x150887c: StoreField: r2->field_f = r0
    //     0x150887c: stur            w0, [x2, #0xf]
    // 0x1508880: ldur            x0, [fp, #-0x28]
    // 0x1508884: StoreField: r2->field_13 = r0
    //     0x1508884: stur            w0, [x2, #0x13]
    // 0x1508888: ldur            x0, [fp, #-8]
    // 0x150888c: ArrayStore: r2[0] = r0  ; List_4
    //     0x150888c: stur            w0, [x2, #0x17]
    // 0x1508890: r1 = <Widget>
    //     0x1508890: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1508894: r0 = AllocateGrowableArray()
    //     0x1508894: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1508898: mov             x1, x0
    // 0x150889c: ldur            x0, [fp, #-0x30]
    // 0x15088a0: stur            x1, [fp, #-8]
    // 0x15088a4: StoreField: r1->field_f = r0
    //     0x15088a4: stur            w0, [x1, #0xf]
    // 0x15088a8: r0 = 6
    //     0x15088a8: movz            x0, #0x6
    // 0x15088ac: StoreField: r1->field_b = r0
    //     0x15088ac: stur            w0, [x1, #0xb]
    // 0x15088b0: r0 = Column()
    //     0x15088b0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x15088b4: mov             x1, x0
    // 0x15088b8: r0 = Instance_Axis
    //     0x15088b8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x15088bc: stur            x1, [fp, #-0x20]
    // 0x15088c0: StoreField: r1->field_f = r0
    //     0x15088c0: stur            w0, [x1, #0xf]
    // 0x15088c4: r2 = Instance_MainAxisAlignment
    //     0x15088c4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x15088c8: ldr             x2, [x2, #0xa08]
    // 0x15088cc: StoreField: r1->field_13 = r2
    //     0x15088cc: stur            w2, [x1, #0x13]
    // 0x15088d0: r3 = Instance_MainAxisSize
    //     0x15088d0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x15088d4: ldr             x3, [x3, #0xdd0]
    // 0x15088d8: ArrayStore: r1[0] = r3  ; List_4
    //     0x15088d8: stur            w3, [x1, #0x17]
    // 0x15088dc: r3 = Instance_CrossAxisAlignment
    //     0x15088dc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x15088e0: ldr             x3, [x3, #0x890]
    // 0x15088e4: StoreField: r1->field_1b = r3
    //     0x15088e4: stur            w3, [x1, #0x1b]
    // 0x15088e8: r4 = Instance_VerticalDirection
    //     0x15088e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15088ec: ldr             x4, [x4, #0xa20]
    // 0x15088f0: StoreField: r1->field_23 = r4
    //     0x15088f0: stur            w4, [x1, #0x23]
    // 0x15088f4: r5 = Instance_Clip
    //     0x15088f4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15088f8: ldr             x5, [x5, #0x38]
    // 0x15088fc: StoreField: r1->field_2b = r5
    //     0x15088fc: stur            w5, [x1, #0x2b]
    // 0x1508900: StoreField: r1->field_2f = rZR
    //     0x1508900: stur            xzr, [x1, #0x2f]
    // 0x1508904: ldur            x6, [fp, #-8]
    // 0x1508908: StoreField: r1->field_b = r6
    //     0x1508908: stur            w6, [x1, #0xb]
    // 0x150890c: r0 = Visibility()
    //     0x150890c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1508910: mov             x2, x0
    // 0x1508914: ldur            x0, [fp, #-0x20]
    // 0x1508918: stur            x2, [fp, #-8]
    // 0x150891c: StoreField: r2->field_b = r0
    //     0x150891c: stur            w0, [x2, #0xb]
    // 0x1508920: r0 = Instance_SizedBox
    //     0x1508920: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1508924: StoreField: r2->field_f = r0
    //     0x1508924: stur            w0, [x2, #0xf]
    // 0x1508928: ldur            x0, [fp, #-0x18]
    // 0x150892c: StoreField: r2->field_13 = r0
    //     0x150892c: stur            w0, [x2, #0x13]
    // 0x1508930: r0 = false
    //     0x1508930: add             x0, NULL, #0x30  ; false
    // 0x1508934: ArrayStore: r2[0] = r0  ; List_4
    //     0x1508934: stur            w0, [x2, #0x17]
    // 0x1508938: StoreField: r2->field_1b = r0
    //     0x1508938: stur            w0, [x2, #0x1b]
    // 0x150893c: StoreField: r2->field_1f = r0
    //     0x150893c: stur            w0, [x2, #0x1f]
    // 0x1508940: StoreField: r2->field_23 = r0
    //     0x1508940: stur            w0, [x2, #0x23]
    // 0x1508944: StoreField: r2->field_27 = r0
    //     0x1508944: stur            w0, [x2, #0x27]
    // 0x1508948: StoreField: r2->field_2b = r0
    //     0x1508948: stur            w0, [x2, #0x2b]
    // 0x150894c: ldur            x0, [fp, #-0x10]
    // 0x1508950: LoadField: r1 = r0->field_b
    //     0x1508950: ldur            w1, [x0, #0xb]
    // 0x1508954: LoadField: r3 = r0->field_f
    //     0x1508954: ldur            w3, [x0, #0xf]
    // 0x1508958: DecompressPointer r3
    //     0x1508958: add             x3, x3, HEAP, lsl #32
    // 0x150895c: LoadField: r4 = r3->field_b
    //     0x150895c: ldur            w4, [x3, #0xb]
    // 0x1508960: r3 = LoadInt32Instr(r1)
    //     0x1508960: sbfx            x3, x1, #1, #0x1f
    // 0x1508964: stur            x3, [fp, #-0x58]
    // 0x1508968: r1 = LoadInt32Instr(r4)
    //     0x1508968: sbfx            x1, x4, #1, #0x1f
    // 0x150896c: cmp             x3, x1
    // 0x1508970: b.ne            #0x150897c
    // 0x1508974: mov             x1, x0
    // 0x1508978: r0 = _growToNextCapacity()
    //     0x1508978: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x150897c: ldur            x2, [fp, #-0x10]
    // 0x1508980: ldur            x3, [fp, #-0x58]
    // 0x1508984: add             x0, x3, #1
    // 0x1508988: lsl             x1, x0, #1
    // 0x150898c: StoreField: r2->field_b = r1
    //     0x150898c: stur            w1, [x2, #0xb]
    // 0x1508990: LoadField: r1 = r2->field_f
    //     0x1508990: ldur            w1, [x2, #0xf]
    // 0x1508994: DecompressPointer r1
    //     0x1508994: add             x1, x1, HEAP, lsl #32
    // 0x1508998: ldur            x0, [fp, #-8]
    // 0x150899c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x150899c: add             x25, x1, x3, lsl #2
    //     0x15089a0: add             x25, x25, #0xf
    //     0x15089a4: str             w0, [x25]
    //     0x15089a8: tbz             w0, #0, #0x15089c4
    //     0x15089ac: ldurb           w16, [x1, #-1]
    //     0x15089b0: ldurb           w17, [x0, #-1]
    //     0x15089b4: and             x16, x17, x16, lsr #2
    //     0x15089b8: tst             x16, HEAP, lsr #32
    //     0x15089bc: b.eq            #0x15089c4
    //     0x15089c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15089c4: r0 = Column()
    //     0x15089c4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x15089c8: mov             x3, x0
    // 0x15089cc: r0 = Instance_Axis
    //     0x15089cc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x15089d0: stur            x3, [fp, #-8]
    // 0x15089d4: StoreField: r3->field_f = r0
    //     0x15089d4: stur            w0, [x3, #0xf]
    // 0x15089d8: r0 = Instance_MainAxisAlignment
    //     0x15089d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x15089dc: ldr             x0, [x0, #0xa08]
    // 0x15089e0: StoreField: r3->field_13 = r0
    //     0x15089e0: stur            w0, [x3, #0x13]
    // 0x15089e4: r0 = Instance_MainAxisSize
    //     0x15089e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15089e8: ldr             x0, [x0, #0xa10]
    // 0x15089ec: ArrayStore: r3[0] = r0  ; List_4
    //     0x15089ec: stur            w0, [x3, #0x17]
    // 0x15089f0: r0 = Instance_CrossAxisAlignment
    //     0x15089f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x15089f4: ldr             x0, [x0, #0x890]
    // 0x15089f8: StoreField: r3->field_1b = r0
    //     0x15089f8: stur            w0, [x3, #0x1b]
    // 0x15089fc: r0 = Instance_VerticalDirection
    //     0x15089fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1508a00: ldr             x0, [x0, #0xa20]
    // 0x1508a04: StoreField: r3->field_23 = r0
    //     0x1508a04: stur            w0, [x3, #0x23]
    // 0x1508a08: r0 = Instance_Clip
    //     0x1508a08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1508a0c: ldr             x0, [x0, #0x38]
    // 0x1508a10: StoreField: r3->field_2b = r0
    //     0x1508a10: stur            w0, [x3, #0x2b]
    // 0x1508a14: StoreField: r3->field_2f = rZR
    //     0x1508a14: stur            xzr, [x3, #0x2f]
    // 0x1508a18: ldur            x0, [fp, #-0x10]
    // 0x1508a1c: StoreField: r3->field_b = r0
    //     0x1508a1c: stur            w0, [x3, #0xb]
    // 0x1508a20: r1 = Null
    //     0x1508a20: mov             x1, NULL
    // 0x1508a24: r2 = 2
    //     0x1508a24: movz            x2, #0x2
    // 0x1508a28: r0 = AllocateArray()
    //     0x1508a28: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1508a2c: mov             x2, x0
    // 0x1508a30: ldur            x0, [fp, #-8]
    // 0x1508a34: stur            x2, [fp, #-0x10]
    // 0x1508a38: StoreField: r2->field_f = r0
    //     0x1508a38: stur            w0, [x2, #0xf]
    // 0x1508a3c: r1 = <Widget>
    //     0x1508a3c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1508a40: r0 = AllocateGrowableArray()
    //     0x1508a40: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1508a44: mov             x1, x0
    // 0x1508a48: ldur            x0, [fp, #-0x10]
    // 0x1508a4c: stur            x1, [fp, #-8]
    // 0x1508a50: StoreField: r1->field_f = r0
    //     0x1508a50: stur            w0, [x1, #0xf]
    // 0x1508a54: r0 = 2
    //     0x1508a54: movz            x0, #0x2
    // 0x1508a58: StoreField: r1->field_b = r0
    //     0x1508a58: stur            w0, [x1, #0xb]
    // 0x1508a5c: r0 = ListView()
    //     0x1508a5c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x1508a60: stur            x0, [fp, #-0x10]
    // 0x1508a64: r16 = true
    //     0x1508a64: add             x16, NULL, #0x20  ; true
    // 0x1508a68: r30 = true
    //     0x1508a68: add             lr, NULL, #0x20  ; true
    // 0x1508a6c: stp             lr, x16, [SP, #8]
    // 0x1508a70: r16 = Instance_ScrollPhysics
    //     0x1508a70: add             x16, PP, #0x34, lsl #12  ; [pp+0x34010] Obj!ScrollPhysics@d558a1
    //     0x1508a74: ldr             x16, [x16, #0x10]
    // 0x1508a78: str             x16, [SP]
    // 0x1508a7c: mov             x1, x0
    // 0x1508a80: ldur            x2, [fp, #-8]
    // 0x1508a84: r4 = const [0, 0x5, 0x3, 0x2, physics, 0x4, primary, 0x2, shrinkWrap, 0x3, null]
    //     0x1508a84: add             x4, PP, #0x34, lsl #12  ; [pp+0x34018] List(11) [0, 0x5, 0x3, 0x2, "physics", 0x4, "primary", 0x2, "shrinkWrap", 0x3, Null]
    //     0x1508a88: ldr             x4, [x4, #0x18]
    // 0x1508a8c: r0 = ListView()
    //     0x1508a8c: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0x1508a90: ldur            x0, [fp, #-0x10]
    // 0x1508a94: b               #0x1508ab0
    // 0x1508a98: r0 = Container()
    //     0x1508a98: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1508a9c: mov             x1, x0
    // 0x1508aa0: stur            x0, [fp, #-8]
    // 0x1508aa4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1508aa4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1508aa8: r0 = Container()
    //     0x1508aa8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1508aac: ldur            x0, [fp, #-8]
    // 0x1508ab0: LeaveFrame
    //     0x1508ab0: mov             SP, fp
    //     0x1508ab4: ldp             fp, lr, [SP], #0x10
    // 0x1508ab8: ret
    //     0x1508ab8: ret             
    // 0x1508abc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1508abc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1508ac0: b               #0x1506f2c
    // 0x1508ac4: SaveReg d0
    //     0x1508ac4: str             q0, [SP, #-0x10]!
    // 0x1508ac8: stp             x4, x5, [SP, #-0x10]!
    // 0x1508acc: stp             x2, x3, [SP, #-0x10]!
    // 0x1508ad0: stp             x0, x1, [SP, #-0x10]!
    // 0x1508ad4: r0 = AllocateDouble()
    //     0x1508ad4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1508ad8: mov             x6, x0
    // 0x1508adc: ldp             x0, x1, [SP], #0x10
    // 0x1508ae0: ldp             x2, x3, [SP], #0x10
    // 0x1508ae4: ldp             x4, x5, [SP], #0x10
    // 0x1508ae8: RestoreReg d0
    //     0x1508ae8: ldr             q0, [SP], #0x10
    // 0x1508aec: b               #0x1507740
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0x1508af0, size: 0xc4
    // 0x1508af0: EnterFrame
    //     0x1508af0: stp             fp, lr, [SP, #-0x10]!
    //     0x1508af4: mov             fp, SP
    // 0x1508af8: ldr             x0, [fp, #0x18]
    // 0x1508afc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1508afc: ldur            w1, [x0, #0x17]
    // 0x1508b00: DecompressPointer r1
    //     0x1508b00: add             x1, x1, HEAP, lsl #32
    // 0x1508b04: CheckStackOverflow
    //     0x1508b04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1508b08: cmp             SP, x16
    //     0x1508b0c: b.ls            #0x1508bac
    // 0x1508b10: ldr             x0, [fp, #0x10]
    // 0x1508b14: cmp             w0, NULL
    // 0x1508b18: b.ne            #0x1508b24
    // 0x1508b1c: r2 = Null
    //     0x1508b1c: mov             x2, NULL
    // 0x1508b20: b               #0x1508b28
    // 0x1508b24: LoadField: r2 = r0->field_7
    //     0x1508b24: ldur            w2, [x0, #7]
    // 0x1508b28: cmp             w2, NULL
    // 0x1508b2c: b.ne            #0x1508b38
    // 0x1508b30: r2 = 0
    //     0x1508b30: movz            x2, #0
    // 0x1508b34: b               #0x1508b40
    // 0x1508b38: r3 = LoadInt32Instr(r2)
    //     0x1508b38: sbfx            x3, x2, #1, #0x1f
    // 0x1508b3c: mov             x2, x3
    // 0x1508b40: cmp             x2, #0xa
    // 0x1508b44: b.gt            #0x1508b78
    // 0x1508b48: cmp             w0, NULL
    // 0x1508b4c: b.ne            #0x1508b58
    // 0x1508b50: r0 = Null
    //     0x1508b50: mov             x0, NULL
    // 0x1508b54: b               #0x1508b6c
    // 0x1508b58: LoadField: r2 = r0->field_7
    //     0x1508b58: ldur            w2, [x0, #7]
    // 0x1508b5c: cbz             w2, #0x1508b68
    // 0x1508b60: r0 = false
    //     0x1508b60: add             x0, NULL, #0x30  ; false
    // 0x1508b64: b               #0x1508b6c
    // 0x1508b68: r0 = true
    //     0x1508b68: add             x0, NULL, #0x20  ; true
    // 0x1508b6c: cmp             w0, NULL
    // 0x1508b70: b.eq            #0x1508b78
    // 0x1508b74: tbnz            w0, #4, #0x1508b8c
    // 0x1508b78: LoadField: r0 = r1->field_f
    //     0x1508b78: ldur            w0, [x1, #0xf]
    // 0x1508b7c: DecompressPointer r0
    //     0x1508b7c: add             x0, x0, HEAP, lsl #32
    // 0x1508b80: mov             x1, x0
    // 0x1508b84: r0 = isButtonEnabled()
    //     0x1508b84: bl              #0xb50f90  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::isButtonEnabled
    // 0x1508b88: b               #0x1508b9c
    // 0x1508b8c: LoadField: r0 = r1->field_f
    //     0x1508b8c: ldur            w0, [x1, #0xf]
    // 0x1508b90: DecompressPointer r0
    //     0x1508b90: add             x0, x0, HEAP, lsl #32
    // 0x1508b94: mov             x1, x0
    // 0x1508b98: r0 = isButtonEnabled()
    //     0x1508b98: bl              #0xb50f90  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::isButtonEnabled
    // 0x1508b9c: r0 = Null
    //     0x1508b9c: mov             x0, NULL
    // 0x1508ba0: LeaveFrame
    //     0x1508ba0: mov             SP, fp
    //     0x1508ba4: ldp             fp, lr, [SP], #0x10
    // 0x1508ba8: ret
    //     0x1508ba8: ret             
    // 0x1508bac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1508bac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1508bb0: b               #0x1508b10
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x1508bb4, size: 0xcc
    // 0x1508bb4: EnterFrame
    //     0x1508bb4: stp             fp, lr, [SP, #-0x10]!
    //     0x1508bb8: mov             fp, SP
    // 0x1508bbc: AllocStack(0x18)
    //     0x1508bbc: sub             SP, SP, #0x18
    // 0x1508bc0: SetupParameters()
    //     0x1508bc0: ldr             x0, [fp, #0x20]
    //     0x1508bc4: ldur            w1, [x0, #0x17]
    //     0x1508bc8: add             x1, x1, HEAP, lsl #32
    //     0x1508bcc: stur            x1, [fp, #-8]
    // 0x1508bd0: r1 = 2
    //     0x1508bd0: movz            x1, #0x2
    // 0x1508bd4: r0 = AllocateContext()
    //     0x1508bd4: bl              #0x16f6108  ; AllocateContextStub
    // 0x1508bd8: mov             x1, x0
    // 0x1508bdc: ldur            x0, [fp, #-8]
    // 0x1508be0: stur            x1, [fp, #-0x10]
    // 0x1508be4: StoreField: r1->field_b = r0
    //     0x1508be4: stur            w0, [x1, #0xb]
    // 0x1508be8: ldr             x0, [fp, #0x18]
    // 0x1508bec: StoreField: r1->field_f = r0
    //     0x1508bec: stur            w0, [x1, #0xf]
    // 0x1508bf0: ldr             x0, [fp, #0x10]
    // 0x1508bf4: StoreField: r1->field_13 = r0
    //     0x1508bf4: stur            w0, [x1, #0x13]
    // 0x1508bf8: r0 = Obx()
    //     0x1508bf8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1508bfc: ldur            x2, [fp, #-0x10]
    // 0x1508c00: r1 = Function '<anonymous closure>':.
    //     0x1508c00: add             x1, PP, #0x34, lsl #12  ; [pp+0x34020] AnonymousClosure: (0x1508da4), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x1506e38)
    //     0x1508c04: ldr             x1, [x1, #0x20]
    // 0x1508c08: stur            x0, [fp, #-8]
    // 0x1508c0c: r0 = AllocateClosure()
    //     0x1508c0c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1508c10: mov             x1, x0
    // 0x1508c14: ldur            x0, [fp, #-8]
    // 0x1508c18: StoreField: r0->field_b = r1
    //     0x1508c18: stur            w1, [x0, #0xb]
    // 0x1508c1c: r0 = InkWell()
    //     0x1508c1c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1508c20: mov             x3, x0
    // 0x1508c24: ldur            x0, [fp, #-8]
    // 0x1508c28: stur            x3, [fp, #-0x18]
    // 0x1508c2c: StoreField: r3->field_b = r0
    //     0x1508c2c: stur            w0, [x3, #0xb]
    // 0x1508c30: ldur            x2, [fp, #-0x10]
    // 0x1508c34: r1 = Function '<anonymous closure>':.
    //     0x1508c34: add             x1, PP, #0x34, lsl #12  ; [pp+0x34028] AnonymousClosure: (0x1508c80), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x1506e38)
    //     0x1508c38: ldr             x1, [x1, #0x28]
    // 0x1508c3c: r0 = AllocateClosure()
    //     0x1508c3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1508c40: mov             x1, x0
    // 0x1508c44: ldur            x0, [fp, #-0x18]
    // 0x1508c48: StoreField: r0->field_f = r1
    //     0x1508c48: stur            w1, [x0, #0xf]
    // 0x1508c4c: r1 = true
    //     0x1508c4c: add             x1, NULL, #0x20  ; true
    // 0x1508c50: StoreField: r0->field_43 = r1
    //     0x1508c50: stur            w1, [x0, #0x43]
    // 0x1508c54: r2 = Instance_BoxShape
    //     0x1508c54: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1508c58: ldr             x2, [x2, #0x80]
    // 0x1508c5c: StoreField: r0->field_47 = r2
    //     0x1508c5c: stur            w2, [x0, #0x47]
    // 0x1508c60: StoreField: r0->field_6f = r1
    //     0x1508c60: stur            w1, [x0, #0x6f]
    // 0x1508c64: r2 = false
    //     0x1508c64: add             x2, NULL, #0x30  ; false
    // 0x1508c68: StoreField: r0->field_73 = r2
    //     0x1508c68: stur            w2, [x0, #0x73]
    // 0x1508c6c: StoreField: r0->field_83 = r1
    //     0x1508c6c: stur            w1, [x0, #0x83]
    // 0x1508c70: StoreField: r0->field_7b = r2
    //     0x1508c70: stur            w2, [x0, #0x7b]
    // 0x1508c74: LeaveFrame
    //     0x1508c74: mov             SP, fp
    //     0x1508c78: ldp             fp, lr, [SP], #0x10
    // 0x1508c7c: ret
    //     0x1508c7c: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1508c80, size: 0x124
    // 0x1508c80: EnterFrame
    //     0x1508c80: stp             fp, lr, [SP, #-0x10]!
    //     0x1508c84: mov             fp, SP
    // 0x1508c88: AllocStack(0x18)
    //     0x1508c88: sub             SP, SP, #0x18
    // 0x1508c8c: SetupParameters()
    //     0x1508c8c: ldr             x0, [fp, #0x10]
    //     0x1508c90: ldur            w2, [x0, #0x17]
    //     0x1508c94: add             x2, x2, HEAP, lsl #32
    //     0x1508c98: stur            x2, [fp, #-0x10]
    // 0x1508c9c: CheckStackOverflow
    //     0x1508c9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1508ca0: cmp             SP, x16
    //     0x1508ca4: b.ls            #0x1508d98
    // 0x1508ca8: LoadField: r0 = r2->field_b
    //     0x1508ca8: ldur            w0, [x2, #0xb]
    // 0x1508cac: DecompressPointer r0
    //     0x1508cac: add             x0, x0, HEAP, lsl #32
    // 0x1508cb0: stur            x0, [fp, #-8]
    // 0x1508cb4: LoadField: r1 = r0->field_f
    //     0x1508cb4: ldur            w1, [x0, #0xf]
    // 0x1508cb8: DecompressPointer r1
    //     0x1508cb8: add             x1, x1, HEAP, lsl #32
    // 0x1508cbc: r0 = controller()
    //     0x1508cbc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1508cc0: LoadField: r1 = r0->field_db
    //     0x1508cc0: ldur            w1, [x0, #0xdb]
    // 0x1508cc4: DecompressPointer r1
    //     0x1508cc4: add             x1, x1, HEAP, lsl #32
    // 0x1508cc8: r2 = ""
    //     0x1508cc8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1508ccc: r0 = text=()
    //     0x1508ccc: bl              #0x80121c  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0x1508cd0: ldur            x0, [fp, #-8]
    // 0x1508cd4: LoadField: r1 = r0->field_f
    //     0x1508cd4: ldur            w1, [x0, #0xf]
    // 0x1508cd8: DecompressPointer r1
    //     0x1508cd8: add             x1, x1, HEAP, lsl #32
    // 0x1508cdc: r0 = controller()
    //     0x1508cdc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1508ce0: LoadField: r2 = r0->field_67
    //     0x1508ce0: ldur            w2, [x0, #0x67]
    // 0x1508ce4: DecompressPointer r2
    //     0x1508ce4: add             x2, x2, HEAP, lsl #32
    // 0x1508ce8: ldur            x0, [fp, #-8]
    // 0x1508cec: stur            x2, [fp, #-0x18]
    // 0x1508cf0: LoadField: r1 = r0->field_f
    //     0x1508cf0: ldur            w1, [x0, #0xf]
    // 0x1508cf4: DecompressPointer r1
    //     0x1508cf4: add             x1, x1, HEAP, lsl #32
    // 0x1508cf8: r0 = controller()
    //     0x1508cf8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1508cfc: LoadField: r1 = r0->field_57
    //     0x1508cfc: ldur            w1, [x0, #0x57]
    // 0x1508d00: DecompressPointer r1
    //     0x1508d00: add             x1, x1, HEAP, lsl #32
    // 0x1508d04: r0 = value()
    //     0x1508d04: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1508d08: cmp             w0, NULL
    // 0x1508d0c: b.ne            #0x1508d18
    // 0x1508d10: r2 = Null
    //     0x1508d10: mov             x2, NULL
    // 0x1508d14: b               #0x1508d6c
    // 0x1508d18: ldur            x1, [fp, #-0x10]
    // 0x1508d1c: LoadField: r2 = r0->field_7
    //     0x1508d1c: ldur            w2, [x0, #7]
    // 0x1508d20: DecompressPointer r2
    //     0x1508d20: add             x2, x2, HEAP, lsl #32
    // 0x1508d24: LoadField: r0 = r1->field_13
    //     0x1508d24: ldur            w0, [x1, #0x13]
    // 0x1508d28: DecompressPointer r0
    //     0x1508d28: add             x0, x0, HEAP, lsl #32
    // 0x1508d2c: LoadField: r1 = r2->field_b
    //     0x1508d2c: ldur            w1, [x2, #0xb]
    // 0x1508d30: r3 = LoadInt32Instr(r0)
    //     0x1508d30: sbfx            x3, x0, #1, #0x1f
    //     0x1508d34: tbz             w0, #0, #0x1508d3c
    //     0x1508d38: ldur            x3, [x0, #7]
    // 0x1508d3c: r0 = LoadInt32Instr(r1)
    //     0x1508d3c: sbfx            x0, x1, #1, #0x1f
    // 0x1508d40: mov             x1, x3
    // 0x1508d44: cmp             x1, x0
    // 0x1508d48: b.hs            #0x1508da0
    // 0x1508d4c: LoadField: r0 = r2->field_f
    //     0x1508d4c: ldur            w0, [x2, #0xf]
    // 0x1508d50: DecompressPointer r0
    //     0x1508d50: add             x0, x0, HEAP, lsl #32
    // 0x1508d54: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x1508d54: add             x16, x0, x3, lsl #2
    //     0x1508d58: ldur            w1, [x16, #0xf]
    // 0x1508d5c: DecompressPointer r1
    //     0x1508d5c: add             x1, x1, HEAP, lsl #32
    // 0x1508d60: LoadField: r0 = r1->field_b
    //     0x1508d60: ldur            w0, [x1, #0xb]
    // 0x1508d64: DecompressPointer r0
    //     0x1508d64: add             x0, x0, HEAP, lsl #32
    // 0x1508d68: mov             x2, x0
    // 0x1508d6c: ldur            x0, [fp, #-8]
    // 0x1508d70: ldur            x1, [fp, #-0x18]
    // 0x1508d74: r0 = value=()
    //     0x1508d74: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1508d78: ldur            x0, [fp, #-8]
    // 0x1508d7c: LoadField: r1 = r0->field_f
    //     0x1508d7c: ldur            w1, [x0, #0xf]
    // 0x1508d80: DecompressPointer r1
    //     0x1508d80: add             x1, x1, HEAP, lsl #32
    // 0x1508d84: r0 = isButtonEnabled()
    //     0x1508d84: bl              #0xb50f90  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::isButtonEnabled
    // 0x1508d88: r0 = Null
    //     0x1508d88: mov             x0, NULL
    // 0x1508d8c: LeaveFrame
    //     0x1508d8c: mov             SP, fp
    //     0x1508d90: ldp             fp, lr, [SP], #0x10
    // 0x1508d94: ret
    //     0x1508d94: ret             
    // 0x1508d98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1508d98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1508d9c: b               #0x1508ca8
    // 0x1508da0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1508da0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Container <anonymous closure>(dynamic) {
    // ** addr: 0x1508da4, size: 0x3f0
    // 0x1508da4: EnterFrame
    //     0x1508da4: stp             fp, lr, [SP, #-0x10]!
    //     0x1508da8: mov             fp, SP
    // 0x1508dac: AllocStack(0x40)
    //     0x1508dac: sub             SP, SP, #0x40
    // 0x1508db0: SetupParameters()
    //     0x1508db0: ldr             x0, [fp, #0x10]
    //     0x1508db4: ldur            w2, [x0, #0x17]
    //     0x1508db8: add             x2, x2, HEAP, lsl #32
    //     0x1508dbc: stur            x2, [fp, #-8]
    // 0x1508dc0: CheckStackOverflow
    //     0x1508dc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1508dc4: cmp             SP, x16
    //     0x1508dc8: b.ls            #0x1509184
    // 0x1508dcc: r1 = Instance_Color
    //     0x1508dcc: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b8] Obj!Color@d6ad11
    //     0x1508dd0: ldr             x1, [x1, #0x7b8]
    // 0x1508dd4: d0 = 0.300000
    //     0x1508dd4: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x1508dd8: ldr             d0, [x17, #0x658]
    // 0x1508ddc: r0 = withOpacity()
    //     0x1508ddc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1508de0: r16 = 1.000000
    //     0x1508de0: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x1508de4: str             x16, [SP]
    // 0x1508de8: mov             x2, x0
    // 0x1508dec: r1 = Null
    //     0x1508dec: mov             x1, NULL
    // 0x1508df0: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x1508df0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x1508df4: ldr             x4, [x4, #0x108]
    // 0x1508df8: r0 = Border.all()
    //     0x1508df8: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x1508dfc: stur            x0, [fp, #-0x10]
    // 0x1508e00: r0 = BoxDecoration()
    //     0x1508e00: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1508e04: mov             x2, x0
    // 0x1508e08: ldur            x0, [fp, #-0x10]
    // 0x1508e0c: stur            x2, [fp, #-0x18]
    // 0x1508e10: StoreField: r2->field_f = r0
    //     0x1508e10: stur            w0, [x2, #0xf]
    // 0x1508e14: r0 = Instance_BoxShape
    //     0x1508e14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1508e18: ldr             x0, [x0, #0x80]
    // 0x1508e1c: StoreField: r2->field_23 = r0
    //     0x1508e1c: stur            w0, [x2, #0x23]
    // 0x1508e20: ldur            x0, [fp, #-8]
    // 0x1508e24: LoadField: r3 = r0->field_b
    //     0x1508e24: ldur            w3, [x0, #0xb]
    // 0x1508e28: DecompressPointer r3
    //     0x1508e28: add             x3, x3, HEAP, lsl #32
    // 0x1508e2c: stur            x3, [fp, #-0x10]
    // 0x1508e30: LoadField: r1 = r3->field_f
    //     0x1508e30: ldur            w1, [x3, #0xf]
    // 0x1508e34: DecompressPointer r1
    //     0x1508e34: add             x1, x1, HEAP, lsl #32
    // 0x1508e38: r0 = controller()
    //     0x1508e38: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1508e3c: LoadField: r1 = r0->field_67
    //     0x1508e3c: ldur            w1, [x0, #0x67]
    // 0x1508e40: DecompressPointer r1
    //     0x1508e40: add             x1, x1, HEAP, lsl #32
    // 0x1508e44: r0 = value()
    //     0x1508e44: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1508e48: mov             x2, x0
    // 0x1508e4c: ldur            x0, [fp, #-0x10]
    // 0x1508e50: stur            x2, [fp, #-0x20]
    // 0x1508e54: LoadField: r1 = r0->field_f
    //     0x1508e54: ldur            w1, [x0, #0xf]
    // 0x1508e58: DecompressPointer r1
    //     0x1508e58: add             x1, x1, HEAP, lsl #32
    // 0x1508e5c: r0 = controller()
    //     0x1508e5c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1508e60: LoadField: r1 = r0->field_57
    //     0x1508e60: ldur            w1, [x0, #0x57]
    // 0x1508e64: DecompressPointer r1
    //     0x1508e64: add             x1, x1, HEAP, lsl #32
    // 0x1508e68: r0 = value()
    //     0x1508e68: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1508e6c: cmp             w0, NULL
    // 0x1508e70: b.ne            #0x1508e80
    // 0x1508e74: ldur            x2, [fp, #-8]
    // 0x1508e78: r1 = Null
    //     0x1508e78: mov             x1, NULL
    // 0x1508e7c: b               #0x1508ed4
    // 0x1508e80: ldur            x2, [fp, #-8]
    // 0x1508e84: LoadField: r3 = r0->field_7
    //     0x1508e84: ldur            w3, [x0, #7]
    // 0x1508e88: DecompressPointer r3
    //     0x1508e88: add             x3, x3, HEAP, lsl #32
    // 0x1508e8c: LoadField: r0 = r2->field_13
    //     0x1508e8c: ldur            w0, [x2, #0x13]
    // 0x1508e90: DecompressPointer r0
    //     0x1508e90: add             x0, x0, HEAP, lsl #32
    // 0x1508e94: LoadField: r1 = r3->field_b
    //     0x1508e94: ldur            w1, [x3, #0xb]
    // 0x1508e98: r4 = LoadInt32Instr(r0)
    //     0x1508e98: sbfx            x4, x0, #1, #0x1f
    //     0x1508e9c: tbz             w0, #0, #0x1508ea4
    //     0x1508ea0: ldur            x4, [x0, #7]
    // 0x1508ea4: r0 = LoadInt32Instr(r1)
    //     0x1508ea4: sbfx            x0, x1, #1, #0x1f
    // 0x1508ea8: mov             x1, x4
    // 0x1508eac: cmp             x1, x0
    // 0x1508eb0: b.hs            #0x150918c
    // 0x1508eb4: LoadField: r0 = r3->field_f
    //     0x1508eb4: ldur            w0, [x3, #0xf]
    // 0x1508eb8: DecompressPointer r0
    //     0x1508eb8: add             x0, x0, HEAP, lsl #32
    // 0x1508ebc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x1508ebc: add             x16, x0, x4, lsl #2
    //     0x1508ec0: ldur            w1, [x16, #0xf]
    // 0x1508ec4: DecompressPointer r1
    //     0x1508ec4: add             x1, x1, HEAP, lsl #32
    // 0x1508ec8: LoadField: r0 = r1->field_b
    //     0x1508ec8: ldur            w0, [x1, #0xb]
    // 0x1508ecc: DecompressPointer r0
    //     0x1508ecc: add             x0, x0, HEAP, lsl #32
    // 0x1508ed0: mov             x1, x0
    // 0x1508ed4: ldur            x0, [fp, #-0x20]
    // 0x1508ed8: r3 = LoadClassIdInstr(r0)
    //     0x1508ed8: ldur            x3, [x0, #-1]
    //     0x1508edc: ubfx            x3, x3, #0xc, #0x14
    // 0x1508ee0: stp             x1, x0, [SP]
    // 0x1508ee4: mov             x0, x3
    // 0x1508ee8: mov             lr, x0
    // 0x1508eec: ldr             lr, [x21, lr, lsl #3]
    // 0x1508ef0: blr             lr
    // 0x1508ef4: tbnz            w0, #4, #0x1508f04
    // 0x1508ef8: r3 = Instance_IconData
    //     0x1508ef8: add             x3, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0x1508efc: ldr             x3, [x3, #0x30]
    // 0x1508f00: b               #0x1508f0c
    // 0x1508f04: r3 = Instance_IconData
    //     0x1508f04: add             x3, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0x1508f08: ldr             x3, [x3, #0x38]
    // 0x1508f0c: ldur            x0, [fp, #-8]
    // 0x1508f10: ldur            x2, [fp, #-0x10]
    // 0x1508f14: stur            x3, [fp, #-0x20]
    // 0x1508f18: LoadField: r1 = r0->field_f
    //     0x1508f18: ldur            w1, [x0, #0xf]
    // 0x1508f1c: DecompressPointer r1
    //     0x1508f1c: add             x1, x1, HEAP, lsl #32
    // 0x1508f20: r0 = of()
    //     0x1508f20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1508f24: LoadField: r1 = r0->field_5b
    //     0x1508f24: ldur            w1, [x0, #0x5b]
    // 0x1508f28: DecompressPointer r1
    //     0x1508f28: add             x1, x1, HEAP, lsl #32
    // 0x1508f2c: stur            x1, [fp, #-0x28]
    // 0x1508f30: r0 = Icon()
    //     0x1508f30: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x1508f34: mov             x2, x0
    // 0x1508f38: ldur            x0, [fp, #-0x20]
    // 0x1508f3c: stur            x2, [fp, #-0x30]
    // 0x1508f40: StoreField: r2->field_b = r0
    //     0x1508f40: stur            w0, [x2, #0xb]
    // 0x1508f44: ldur            x0, [fp, #-0x28]
    // 0x1508f48: StoreField: r2->field_23 = r0
    //     0x1508f48: stur            w0, [x2, #0x23]
    // 0x1508f4c: ldur            x0, [fp, #-0x10]
    // 0x1508f50: LoadField: r1 = r0->field_f
    //     0x1508f50: ldur            w1, [x0, #0xf]
    // 0x1508f54: DecompressPointer r1
    //     0x1508f54: add             x1, x1, HEAP, lsl #32
    // 0x1508f58: r0 = controller()
    //     0x1508f58: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1508f5c: LoadField: r1 = r0->field_57
    //     0x1508f5c: ldur            w1, [x0, #0x57]
    // 0x1508f60: DecompressPointer r1
    //     0x1508f60: add             x1, x1, HEAP, lsl #32
    // 0x1508f64: r0 = value()
    //     0x1508f64: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1508f68: cmp             w0, NULL
    // 0x1508f6c: b.ne            #0x1508f7c
    // 0x1508f70: ldur            x2, [fp, #-8]
    // 0x1508f74: r0 = Null
    //     0x1508f74: mov             x0, NULL
    // 0x1508f78: b               #0x1508fcc
    // 0x1508f7c: ldur            x2, [fp, #-8]
    // 0x1508f80: LoadField: r3 = r0->field_7
    //     0x1508f80: ldur            w3, [x0, #7]
    // 0x1508f84: DecompressPointer r3
    //     0x1508f84: add             x3, x3, HEAP, lsl #32
    // 0x1508f88: LoadField: r0 = r2->field_13
    //     0x1508f88: ldur            w0, [x2, #0x13]
    // 0x1508f8c: DecompressPointer r0
    //     0x1508f8c: add             x0, x0, HEAP, lsl #32
    // 0x1508f90: LoadField: r1 = r3->field_b
    //     0x1508f90: ldur            w1, [x3, #0xb]
    // 0x1508f94: r4 = LoadInt32Instr(r0)
    //     0x1508f94: sbfx            x4, x0, #1, #0x1f
    //     0x1508f98: tbz             w0, #0, #0x1508fa0
    //     0x1508f9c: ldur            x4, [x0, #7]
    // 0x1508fa0: r0 = LoadInt32Instr(r1)
    //     0x1508fa0: sbfx            x0, x1, #1, #0x1f
    // 0x1508fa4: mov             x1, x4
    // 0x1508fa8: cmp             x1, x0
    // 0x1508fac: b.hs            #0x1509190
    // 0x1508fb0: LoadField: r0 = r3->field_f
    //     0x1508fb0: ldur            w0, [x3, #0xf]
    // 0x1508fb4: DecompressPointer r0
    //     0x1508fb4: add             x0, x0, HEAP, lsl #32
    // 0x1508fb8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x1508fb8: add             x16, x0, x4, lsl #2
    //     0x1508fbc: ldur            w1, [x16, #0xf]
    // 0x1508fc0: DecompressPointer r1
    //     0x1508fc0: add             x1, x1, HEAP, lsl #32
    // 0x1508fc4: LoadField: r0 = r1->field_7
    //     0x1508fc4: ldur            w0, [x1, #7]
    // 0x1508fc8: DecompressPointer r0
    //     0x1508fc8: add             x0, x0, HEAP, lsl #32
    // 0x1508fcc: cmp             w0, NULL
    // 0x1508fd0: b.ne            #0x1508fdc
    // 0x1508fd4: r3 = ""
    //     0x1508fd4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1508fd8: b               #0x1508fe0
    // 0x1508fdc: mov             x3, x0
    // 0x1508fe0: ldur            x0, [fp, #-0x30]
    // 0x1508fe4: stur            x3, [fp, #-0x10]
    // 0x1508fe8: LoadField: r1 = r2->field_f
    //     0x1508fe8: ldur            w1, [x2, #0xf]
    // 0x1508fec: DecompressPointer r1
    //     0x1508fec: add             x1, x1, HEAP, lsl #32
    // 0x1508ff0: r0 = of()
    //     0x1508ff0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1508ff4: LoadField: r1 = r0->field_87
    //     0x1508ff4: ldur            w1, [x0, #0x87]
    // 0x1508ff8: DecompressPointer r1
    //     0x1508ff8: add             x1, x1, HEAP, lsl #32
    // 0x1508ffc: LoadField: r0 = r1->field_2b
    //     0x1508ffc: ldur            w0, [x1, #0x2b]
    // 0x1509000: DecompressPointer r0
    //     0x1509000: add             x0, x0, HEAP, lsl #32
    // 0x1509004: stur            x0, [fp, #-8]
    // 0x1509008: r1 = Instance_Color
    //     0x1509008: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x150900c: d0 = 0.700000
    //     0x150900c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1509010: ldr             d0, [x17, #0xf48]
    // 0x1509014: r0 = withOpacity()
    //     0x1509014: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1509018: r16 = 14.000000
    //     0x1509018: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x150901c: ldr             x16, [x16, #0x1d8]
    // 0x1509020: stp             x0, x16, [SP]
    // 0x1509024: ldur            x1, [fp, #-8]
    // 0x1509028: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1509028: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x150902c: ldr             x4, [x4, #0xaa0]
    // 0x1509030: r0 = copyWith()
    //     0x1509030: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1509034: stur            x0, [fp, #-8]
    // 0x1509038: r0 = Text()
    //     0x1509038: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x150903c: mov             x2, x0
    // 0x1509040: ldur            x0, [fp, #-0x10]
    // 0x1509044: stur            x2, [fp, #-0x20]
    // 0x1509048: StoreField: r2->field_b = r0
    //     0x1509048: stur            w0, [x2, #0xb]
    // 0x150904c: ldur            x0, [fp, #-8]
    // 0x1509050: StoreField: r2->field_13 = r0
    //     0x1509050: stur            w0, [x2, #0x13]
    // 0x1509054: r1 = <FlexParentData>
    //     0x1509054: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1509058: ldr             x1, [x1, #0xe00]
    // 0x150905c: r0 = Expanded()
    //     0x150905c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1509060: mov             x3, x0
    // 0x1509064: r0 = 1
    //     0x1509064: movz            x0, #0x1
    // 0x1509068: stur            x3, [fp, #-8]
    // 0x150906c: StoreField: r3->field_13 = r0
    //     0x150906c: stur            x0, [x3, #0x13]
    // 0x1509070: r0 = Instance_FlexFit
    //     0x1509070: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1509074: ldr             x0, [x0, #0xe08]
    // 0x1509078: StoreField: r3->field_1b = r0
    //     0x1509078: stur            w0, [x3, #0x1b]
    // 0x150907c: ldur            x0, [fp, #-0x20]
    // 0x1509080: StoreField: r3->field_b = r0
    //     0x1509080: stur            w0, [x3, #0xb]
    // 0x1509084: r1 = Null
    //     0x1509084: mov             x1, NULL
    // 0x1509088: r2 = 6
    //     0x1509088: movz            x2, #0x6
    // 0x150908c: r0 = AllocateArray()
    //     0x150908c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1509090: mov             x2, x0
    // 0x1509094: ldur            x0, [fp, #-0x30]
    // 0x1509098: stur            x2, [fp, #-0x10]
    // 0x150909c: StoreField: r2->field_f = r0
    //     0x150909c: stur            w0, [x2, #0xf]
    // 0x15090a0: r16 = Instance_SizedBox
    //     0x15090a0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34040] Obj!SizedBox@d680c1
    //     0x15090a4: ldr             x16, [x16, #0x40]
    // 0x15090a8: StoreField: r2->field_13 = r16
    //     0x15090a8: stur            w16, [x2, #0x13]
    // 0x15090ac: ldur            x0, [fp, #-8]
    // 0x15090b0: ArrayStore: r2[0] = r0  ; List_4
    //     0x15090b0: stur            w0, [x2, #0x17]
    // 0x15090b4: r1 = <Widget>
    //     0x15090b4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15090b8: r0 = AllocateGrowableArray()
    //     0x15090b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15090bc: mov             x1, x0
    // 0x15090c0: ldur            x0, [fp, #-0x10]
    // 0x15090c4: stur            x1, [fp, #-8]
    // 0x15090c8: StoreField: r1->field_f = r0
    //     0x15090c8: stur            w0, [x1, #0xf]
    // 0x15090cc: r0 = 6
    //     0x15090cc: movz            x0, #0x6
    // 0x15090d0: StoreField: r1->field_b = r0
    //     0x15090d0: stur            w0, [x1, #0xb]
    // 0x15090d4: r0 = Row()
    //     0x15090d4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15090d8: mov             x1, x0
    // 0x15090dc: r0 = Instance_Axis
    //     0x15090dc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15090e0: stur            x1, [fp, #-0x10]
    // 0x15090e4: StoreField: r1->field_f = r0
    //     0x15090e4: stur            w0, [x1, #0xf]
    // 0x15090e8: r0 = Instance_MainAxisAlignment
    //     0x15090e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x15090ec: ldr             x0, [x0, #0xa08]
    // 0x15090f0: StoreField: r1->field_13 = r0
    //     0x15090f0: stur            w0, [x1, #0x13]
    // 0x15090f4: r0 = Instance_MainAxisSize
    //     0x15090f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x15090f8: ldr             x0, [x0, #0xdd0]
    // 0x15090fc: ArrayStore: r1[0] = r0  ; List_4
    //     0x15090fc: stur            w0, [x1, #0x17]
    // 0x1509100: r0 = Instance_CrossAxisAlignment
    //     0x1509100: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1509104: ldr             x0, [x0, #0xa18]
    // 0x1509108: StoreField: r1->field_1b = r0
    //     0x1509108: stur            w0, [x1, #0x1b]
    // 0x150910c: r0 = Instance_VerticalDirection
    //     0x150910c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1509110: ldr             x0, [x0, #0xa20]
    // 0x1509114: StoreField: r1->field_23 = r0
    //     0x1509114: stur            w0, [x1, #0x23]
    // 0x1509118: r0 = Instance_Clip
    //     0x1509118: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x150911c: ldr             x0, [x0, #0x38]
    // 0x1509120: StoreField: r1->field_2b = r0
    //     0x1509120: stur            w0, [x1, #0x2b]
    // 0x1509124: StoreField: r1->field_2f = rZR
    //     0x1509124: stur            xzr, [x1, #0x2f]
    // 0x1509128: ldur            x0, [fp, #-8]
    // 0x150912c: StoreField: r1->field_b = r0
    //     0x150912c: stur            w0, [x1, #0xb]
    // 0x1509130: r0 = Padding()
    //     0x1509130: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1509134: mov             x1, x0
    // 0x1509138: r0 = Instance_EdgeInsets
    //     0x1509138: add             x0, PP, #0x34, lsl #12  ; [pp+0x34048] Obj!EdgeInsets@d59511
    //     0x150913c: ldr             x0, [x0, #0x48]
    // 0x1509140: stur            x1, [fp, #-8]
    // 0x1509144: StoreField: r1->field_f = r0
    //     0x1509144: stur            w0, [x1, #0xf]
    // 0x1509148: ldur            x0, [fp, #-0x10]
    // 0x150914c: StoreField: r1->field_b = r0
    //     0x150914c: stur            w0, [x1, #0xb]
    // 0x1509150: r0 = Container()
    //     0x1509150: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1509154: stur            x0, [fp, #-0x10]
    // 0x1509158: ldur            x16, [fp, #-0x18]
    // 0x150915c: ldur            lr, [fp, #-8]
    // 0x1509160: stp             lr, x16, [SP]
    // 0x1509164: mov             x1, x0
    // 0x1509168: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x1509168: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x150916c: ldr             x4, [x4, #0x88]
    // 0x1509170: r0 = Container()
    //     0x1509170: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1509174: ldur            x0, [fp, #-0x10]
    // 0x1509178: LeaveFrame
    //     0x1509178: mov             SP, fp
    //     0x150917c: ldp             fp, lr, [SP], #0x10
    // 0x1509180: ret
    //     0x1509180: ret             
    // 0x1509184: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1509184: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1509188: b               #0x1508dcc
    // 0x150918c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x150918c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1509190: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1509190: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15ecca8, size: 0x18c
    // 0x15ecca8: EnterFrame
    //     0x15ecca8: stp             fp, lr, [SP, #-0x10]!
    //     0x15eccac: mov             fp, SP
    // 0x15eccb0: AllocStack(0x28)
    //     0x15eccb0: sub             SP, SP, #0x28
    // 0x15eccb4: SetupParameters(ReturnOrderView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15eccb4: mov             x0, x1
    //     0x15eccb8: stur            x1, [fp, #-8]
    //     0x15eccbc: mov             x1, x2
    //     0x15eccc0: stur            x2, [fp, #-0x10]
    // 0x15eccc4: CheckStackOverflow
    //     0x15eccc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15eccc8: cmp             SP, x16
    //     0x15ecccc: b.ls            #0x15ece2c
    // 0x15eccd0: r1 = 2
    //     0x15eccd0: movz            x1, #0x2
    // 0x15eccd4: r0 = AllocateContext()
    //     0x15eccd4: bl              #0x16f6108  ; AllocateContextStub
    // 0x15eccd8: mov             x1, x0
    // 0x15eccdc: ldur            x0, [fp, #-8]
    // 0x15ecce0: stur            x1, [fp, #-0x18]
    // 0x15ecce4: StoreField: r1->field_f = r0
    //     0x15ecce4: stur            w0, [x1, #0xf]
    // 0x15ecce8: ldur            x0, [fp, #-0x10]
    // 0x15eccec: StoreField: r1->field_13 = r0
    //     0x15eccec: stur            w0, [x1, #0x13]
    // 0x15eccf0: r0 = Obx()
    //     0x15eccf0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15eccf4: ldur            x2, [fp, #-0x18]
    // 0x15eccf8: r1 = Function '<anonymous closure>':.
    //     0x15eccf8: add             x1, PP, #0x34, lsl #12  ; [pp+0x34058] AnonymousClosure: (0x15d5454), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15eccfc: ldr             x1, [x1, #0x58]
    // 0x15ecd00: stur            x0, [fp, #-8]
    // 0x15ecd04: r0 = AllocateClosure()
    //     0x15ecd04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15ecd08: mov             x1, x0
    // 0x15ecd0c: ldur            x0, [fp, #-8]
    // 0x15ecd10: StoreField: r0->field_b = r1
    //     0x15ecd10: stur            w1, [x0, #0xb]
    // 0x15ecd14: ldur            x1, [fp, #-0x10]
    // 0x15ecd18: r0 = of()
    //     0x15ecd18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15ecd1c: LoadField: r1 = r0->field_5b
    //     0x15ecd1c: ldur            w1, [x0, #0x5b]
    // 0x15ecd20: DecompressPointer r1
    //     0x15ecd20: add             x1, x1, HEAP, lsl #32
    // 0x15ecd24: stur            x1, [fp, #-0x10]
    // 0x15ecd28: r0 = ColorFilter()
    //     0x15ecd28: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15ecd2c: mov             x1, x0
    // 0x15ecd30: ldur            x0, [fp, #-0x10]
    // 0x15ecd34: stur            x1, [fp, #-0x20]
    // 0x15ecd38: StoreField: r1->field_7 = r0
    //     0x15ecd38: stur            w0, [x1, #7]
    // 0x15ecd3c: r0 = Instance_BlendMode
    //     0x15ecd3c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15ecd40: ldr             x0, [x0, #0xb30]
    // 0x15ecd44: StoreField: r1->field_b = r0
    //     0x15ecd44: stur            w0, [x1, #0xb]
    // 0x15ecd48: r0 = 1
    //     0x15ecd48: movz            x0, #0x1
    // 0x15ecd4c: StoreField: r1->field_13 = r0
    //     0x15ecd4c: stur            x0, [x1, #0x13]
    // 0x15ecd50: r0 = SvgPicture()
    //     0x15ecd50: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15ecd54: stur            x0, [fp, #-0x10]
    // 0x15ecd58: ldur            x16, [fp, #-0x20]
    // 0x15ecd5c: str             x16, [SP]
    // 0x15ecd60: mov             x1, x0
    // 0x15ecd64: r2 = "assets/images/appbar_arrow.svg"
    //     0x15ecd64: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15ecd68: ldr             x2, [x2, #0xa40]
    // 0x15ecd6c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15ecd6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15ecd70: ldr             x4, [x4, #0xa38]
    // 0x15ecd74: r0 = SvgPicture.asset()
    //     0x15ecd74: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15ecd78: r0 = Align()
    //     0x15ecd78: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15ecd7c: mov             x1, x0
    // 0x15ecd80: r0 = Instance_Alignment
    //     0x15ecd80: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15ecd84: ldr             x0, [x0, #0xb10]
    // 0x15ecd88: stur            x1, [fp, #-0x20]
    // 0x15ecd8c: StoreField: r1->field_f = r0
    //     0x15ecd8c: stur            w0, [x1, #0xf]
    // 0x15ecd90: r0 = 1.000000
    //     0x15ecd90: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15ecd94: StoreField: r1->field_13 = r0
    //     0x15ecd94: stur            w0, [x1, #0x13]
    // 0x15ecd98: ArrayStore: r1[0] = r0  ; List_4
    //     0x15ecd98: stur            w0, [x1, #0x17]
    // 0x15ecd9c: ldur            x0, [fp, #-0x10]
    // 0x15ecda0: StoreField: r1->field_b = r0
    //     0x15ecda0: stur            w0, [x1, #0xb]
    // 0x15ecda4: r0 = InkWell()
    //     0x15ecda4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15ecda8: mov             x3, x0
    // 0x15ecdac: ldur            x0, [fp, #-0x20]
    // 0x15ecdb0: stur            x3, [fp, #-0x10]
    // 0x15ecdb4: StoreField: r3->field_b = r0
    //     0x15ecdb4: stur            w0, [x3, #0xb]
    // 0x15ecdb8: ldur            x2, [fp, #-0x18]
    // 0x15ecdbc: r1 = Function '<anonymous closure>':.
    //     0x15ecdbc: add             x1, PP, #0x34, lsl #12  ; [pp+0x34060] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15ecdc0: ldr             x1, [x1, #0x60]
    // 0x15ecdc4: r0 = AllocateClosure()
    //     0x15ecdc4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15ecdc8: ldur            x2, [fp, #-0x10]
    // 0x15ecdcc: StoreField: r2->field_f = r0
    //     0x15ecdcc: stur            w0, [x2, #0xf]
    // 0x15ecdd0: r0 = true
    //     0x15ecdd0: add             x0, NULL, #0x20  ; true
    // 0x15ecdd4: StoreField: r2->field_43 = r0
    //     0x15ecdd4: stur            w0, [x2, #0x43]
    // 0x15ecdd8: r1 = Instance_BoxShape
    //     0x15ecdd8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15ecddc: ldr             x1, [x1, #0x80]
    // 0x15ecde0: StoreField: r2->field_47 = r1
    //     0x15ecde0: stur            w1, [x2, #0x47]
    // 0x15ecde4: StoreField: r2->field_6f = r0
    //     0x15ecde4: stur            w0, [x2, #0x6f]
    // 0x15ecde8: r1 = false
    //     0x15ecde8: add             x1, NULL, #0x30  ; false
    // 0x15ecdec: StoreField: r2->field_73 = r1
    //     0x15ecdec: stur            w1, [x2, #0x73]
    // 0x15ecdf0: StoreField: r2->field_83 = r0
    //     0x15ecdf0: stur            w0, [x2, #0x83]
    // 0x15ecdf4: StoreField: r2->field_7b = r1
    //     0x15ecdf4: stur            w1, [x2, #0x7b]
    // 0x15ecdf8: r0 = AppBar()
    //     0x15ecdf8: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15ecdfc: stur            x0, [fp, #-0x18]
    // 0x15ece00: ldur            x16, [fp, #-8]
    // 0x15ece04: str             x16, [SP]
    // 0x15ece08: mov             x1, x0
    // 0x15ece0c: ldur            x2, [fp, #-0x10]
    // 0x15ece10: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15ece10: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15ece14: ldr             x4, [x4, #0xf00]
    // 0x15ece18: r0 = AppBar()
    //     0x15ece18: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15ece1c: ldur            x0, [fp, #-0x18]
    // 0x15ece20: LeaveFrame
    //     0x15ece20: mov             SP, fp
    //     0x15ece24: ldp             fp, lr, [SP], #0x10
    // 0x15ece28: ret
    //     0x15ece28: ret             
    // 0x15ece2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15ece2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15ece30: b               #0x15eccd0
  }
}
