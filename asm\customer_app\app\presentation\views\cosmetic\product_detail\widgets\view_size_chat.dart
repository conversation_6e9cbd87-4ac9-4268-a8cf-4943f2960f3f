// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/view_size_chat.dart

// class id: 1049329, size: 0x8
class :: {
}

// class id: 3390, size: 0x14, field offset: 0x14
class _ViewSizeChartState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb20aac, size: 0x3d8
    // 0xb20aac: EnterFrame
    //     0xb20aac: stp             fp, lr, [SP, #-0x10]!
    //     0xb20ab0: mov             fp, SP
    // 0xb20ab4: AllocStack(0x38)
    //     0xb20ab4: sub             SP, SP, #0x38
    // 0xb20ab8: SetupParameters(_ViewSizeChartState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb20ab8: mov             x0, x2
    //     0xb20abc: stur            x2, [fp, #-0x10]
    //     0xb20ac0: mov             x2, x1
    //     0xb20ac4: stur            x1, [fp, #-8]
    // 0xb20ac8: CheckStackOverflow
    //     0xb20ac8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb20acc: cmp             SP, x16
    //     0xb20ad0: b.ls            #0xb20e78
    // 0xb20ad4: mov             x1, x0
    // 0xb20ad8: r0 = of()
    //     0xb20ad8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb20adc: LoadField: r1 = r0->field_87
    //     0xb20adc: ldur            w1, [x0, #0x87]
    // 0xb20ae0: DecompressPointer r1
    //     0xb20ae0: add             x1, x1, HEAP, lsl #32
    // 0xb20ae4: LoadField: r0 = r1->field_27
    //     0xb20ae4: ldur            w0, [x1, #0x27]
    // 0xb20ae8: DecompressPointer r0
    //     0xb20ae8: add             x0, x0, HEAP, lsl #32
    // 0xb20aec: r16 = Instance_FontWeight
    //     0xb20aec: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xb20af0: ldr             x16, [x16, #0x20]
    // 0xb20af4: r30 = 22.000000
    //     0xb20af4: add             lr, PP, #0x51, lsl #12  ; [pp+0x510a0] 22
    //     0xb20af8: ldr             lr, [lr, #0xa0]
    // 0xb20afc: stp             lr, x16, [SP]
    // 0xb20b00: mov             x1, x0
    // 0xb20b04: r4 = const [0, 0x3, 0x2, 0x1, fontSize, 0x2, fontWeight, 0x1, null]
    //     0xb20b04: add             x4, PP, #0x53, lsl #12  ; [pp+0x53dc8] List(9) [0, 0x3, 0x2, 0x1, "fontSize", 0x2, "fontWeight", 0x1, Null]
    //     0xb20b08: ldr             x4, [x4, #0xdc8]
    // 0xb20b0c: r0 = copyWith()
    //     0xb20b0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb20b10: stur            x0, [fp, #-0x18]
    // 0xb20b14: r0 = Text()
    //     0xb20b14: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb20b18: mov             x2, x0
    // 0xb20b1c: r0 = "SIZE GUIDE"
    //     0xb20b1c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53dd0] "SIZE GUIDE"
    //     0xb20b20: ldr             x0, [x0, #0xdd0]
    // 0xb20b24: stur            x2, [fp, #-0x20]
    // 0xb20b28: StoreField: r2->field_b = r0
    //     0xb20b28: stur            w0, [x2, #0xb]
    // 0xb20b2c: ldur            x0, [fp, #-0x18]
    // 0xb20b30: StoreField: r2->field_13 = r0
    //     0xb20b30: stur            w0, [x2, #0x13]
    // 0xb20b34: ldur            x1, [fp, #-0x10]
    // 0xb20b38: r0 = of()
    //     0xb20b38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb20b3c: LoadField: r1 = r0->field_5b
    //     0xb20b3c: ldur            w1, [x0, #0x5b]
    // 0xb20b40: DecompressPointer r1
    //     0xb20b40: add             x1, x1, HEAP, lsl #32
    // 0xb20b44: stur            x1, [fp, #-0x18]
    // 0xb20b48: r0 = Icon()
    //     0xb20b48: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb20b4c: mov             x1, x0
    // 0xb20b50: r0 = Instance_IconData
    //     0xb20b50: add             x0, PP, #0x53, lsl #12  ; [pp+0x53dd8] Obj!IconData@d55661
    //     0xb20b54: ldr             x0, [x0, #0xdd8]
    // 0xb20b58: stur            x1, [fp, #-0x28]
    // 0xb20b5c: StoreField: r1->field_b = r0
    //     0xb20b5c: stur            w0, [x1, #0xb]
    // 0xb20b60: ldur            x0, [fp, #-0x18]
    // 0xb20b64: StoreField: r1->field_23 = r0
    //     0xb20b64: stur            w0, [x1, #0x23]
    // 0xb20b68: r0 = InkWell()
    //     0xb20b68: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb20b6c: mov             x3, x0
    // 0xb20b70: ldur            x0, [fp, #-0x28]
    // 0xb20b74: stur            x3, [fp, #-0x18]
    // 0xb20b78: StoreField: r3->field_b = r0
    //     0xb20b78: stur            w0, [x3, #0xb]
    // 0xb20b7c: r1 = Function '<anonymous closure>':.
    //     0xb20b7c: add             x1, PP, #0x58, lsl #12  ; [pp+0x589d0] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb20b80: ldr             x1, [x1, #0x9d0]
    // 0xb20b84: r2 = Null
    //     0xb20b84: mov             x2, NULL
    // 0xb20b88: r0 = AllocateClosure()
    //     0xb20b88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb20b8c: mov             x1, x0
    // 0xb20b90: ldur            x0, [fp, #-0x18]
    // 0xb20b94: StoreField: r0->field_f = r1
    //     0xb20b94: stur            w1, [x0, #0xf]
    // 0xb20b98: r3 = true
    //     0xb20b98: add             x3, NULL, #0x20  ; true
    // 0xb20b9c: StoreField: r0->field_43 = r3
    //     0xb20b9c: stur            w3, [x0, #0x43]
    // 0xb20ba0: r1 = Instance_BoxShape
    //     0xb20ba0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb20ba4: ldr             x1, [x1, #0x80]
    // 0xb20ba8: StoreField: r0->field_47 = r1
    //     0xb20ba8: stur            w1, [x0, #0x47]
    // 0xb20bac: StoreField: r0->field_6f = r3
    //     0xb20bac: stur            w3, [x0, #0x6f]
    // 0xb20bb0: r4 = false
    //     0xb20bb0: add             x4, NULL, #0x30  ; false
    // 0xb20bb4: StoreField: r0->field_73 = r4
    //     0xb20bb4: stur            w4, [x0, #0x73]
    // 0xb20bb8: StoreField: r0->field_83 = r3
    //     0xb20bb8: stur            w3, [x0, #0x83]
    // 0xb20bbc: StoreField: r0->field_7b = r4
    //     0xb20bbc: stur            w4, [x0, #0x7b]
    // 0xb20bc0: r1 = Null
    //     0xb20bc0: mov             x1, NULL
    // 0xb20bc4: r2 = 4
    //     0xb20bc4: movz            x2, #0x4
    // 0xb20bc8: r0 = AllocateArray()
    //     0xb20bc8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb20bcc: mov             x2, x0
    // 0xb20bd0: ldur            x0, [fp, #-0x20]
    // 0xb20bd4: stur            x2, [fp, #-0x28]
    // 0xb20bd8: StoreField: r2->field_f = r0
    //     0xb20bd8: stur            w0, [x2, #0xf]
    // 0xb20bdc: ldur            x0, [fp, #-0x18]
    // 0xb20be0: StoreField: r2->field_13 = r0
    //     0xb20be0: stur            w0, [x2, #0x13]
    // 0xb20be4: r1 = <Widget>
    //     0xb20be4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb20be8: r0 = AllocateGrowableArray()
    //     0xb20be8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb20bec: mov             x1, x0
    // 0xb20bf0: ldur            x0, [fp, #-0x28]
    // 0xb20bf4: stur            x1, [fp, #-0x18]
    // 0xb20bf8: StoreField: r1->field_f = r0
    //     0xb20bf8: stur            w0, [x1, #0xf]
    // 0xb20bfc: r0 = 4
    //     0xb20bfc: movz            x0, #0x4
    // 0xb20c00: StoreField: r1->field_b = r0
    //     0xb20c00: stur            w0, [x1, #0xb]
    // 0xb20c04: r0 = Row()
    //     0xb20c04: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb20c08: mov             x1, x0
    // 0xb20c0c: r0 = Instance_Axis
    //     0xb20c0c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb20c10: stur            x1, [fp, #-0x20]
    // 0xb20c14: StoreField: r1->field_f = r0
    //     0xb20c14: stur            w0, [x1, #0xf]
    // 0xb20c18: r0 = Instance_MainAxisAlignment
    //     0xb20c18: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb20c1c: ldr             x0, [x0, #0xa8]
    // 0xb20c20: StoreField: r1->field_13 = r0
    //     0xb20c20: stur            w0, [x1, #0x13]
    // 0xb20c24: r0 = Instance_MainAxisSize
    //     0xb20c24: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb20c28: ldr             x0, [x0, #0xa10]
    // 0xb20c2c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb20c2c: stur            w0, [x1, #0x17]
    // 0xb20c30: r0 = Instance_CrossAxisAlignment
    //     0xb20c30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb20c34: ldr             x0, [x0, #0xa18]
    // 0xb20c38: StoreField: r1->field_1b = r0
    //     0xb20c38: stur            w0, [x1, #0x1b]
    // 0xb20c3c: r2 = Instance_VerticalDirection
    //     0xb20c3c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb20c40: ldr             x2, [x2, #0xa20]
    // 0xb20c44: StoreField: r1->field_23 = r2
    //     0xb20c44: stur            w2, [x1, #0x23]
    // 0xb20c48: r3 = Instance_Clip
    //     0xb20c48: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb20c4c: ldr             x3, [x3, #0x38]
    // 0xb20c50: StoreField: r1->field_2b = r3
    //     0xb20c50: stur            w3, [x1, #0x2b]
    // 0xb20c54: StoreField: r1->field_2f = rZR
    //     0xb20c54: stur            xzr, [x1, #0x2f]
    // 0xb20c58: ldur            x4, [fp, #-0x18]
    // 0xb20c5c: StoreField: r1->field_b = r4
    //     0xb20c5c: stur            w4, [x1, #0xb]
    // 0xb20c60: r0 = Padding()
    //     0xb20c60: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb20c64: mov             x2, x0
    // 0xb20c68: r0 = Instance_EdgeInsets
    //     0xb20c68: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb20c6c: ldr             x0, [x0, #0x980]
    // 0xb20c70: stur            x2, [fp, #-0x18]
    // 0xb20c74: StoreField: r2->field_f = r0
    //     0xb20c74: stur            w0, [x2, #0xf]
    // 0xb20c78: ldur            x1, [fp, #-0x20]
    // 0xb20c7c: StoreField: r2->field_b = r1
    //     0xb20c7c: stur            w1, [x2, #0xb]
    // 0xb20c80: ldur            x1, [fp, #-0x10]
    // 0xb20c84: r0 = of()
    //     0xb20c84: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb20c88: LoadField: r1 = r0->field_87
    //     0xb20c88: ldur            w1, [x0, #0x87]
    // 0xb20c8c: DecompressPointer r1
    //     0xb20c8c: add             x1, x1, HEAP, lsl #32
    // 0xb20c90: LoadField: r0 = r1->field_27
    //     0xb20c90: ldur            w0, [x1, #0x27]
    // 0xb20c94: DecompressPointer r0
    //     0xb20c94: add             x0, x0, HEAP, lsl #32
    // 0xb20c98: r16 = Instance_FontWeight
    //     0xb20c98: add             x16, PP, #0x13, lsl #12  ; [pp+0x13010] Obj!FontWeight@d68d61
    //     0xb20c9c: ldr             x16, [x16, #0x10]
    // 0xb20ca0: r30 = 16.000000
    //     0xb20ca0: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb20ca4: ldr             lr, [lr, #0x188]
    // 0xb20ca8: stp             lr, x16, [SP]
    // 0xb20cac: mov             x1, x0
    // 0xb20cb0: r4 = const [0, 0x3, 0x2, 0x1, fontSize, 0x2, fontWeight, 0x1, null]
    //     0xb20cb0: add             x4, PP, #0x53, lsl #12  ; [pp+0x53dc8] List(9) [0, 0x3, 0x2, 0x1, "fontSize", 0x2, "fontWeight", 0x1, Null]
    //     0xb20cb4: ldr             x4, [x4, #0xdc8]
    // 0xb20cb8: r0 = copyWith()
    //     0xb20cb8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb20cbc: stur            x0, [fp, #-0x10]
    // 0xb20cc0: r0 = Text()
    //     0xb20cc0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb20cc4: mov             x1, x0
    // 0xb20cc8: r0 = "We have provided the body measurements to help you decide which size to buy."
    //     0xb20cc8: add             x0, PP, #0x53, lsl #12  ; [pp+0x53de8] "We have provided the body measurements to help you decide which size to buy."
    //     0xb20ccc: ldr             x0, [x0, #0xde8]
    // 0xb20cd0: stur            x1, [fp, #-0x20]
    // 0xb20cd4: StoreField: r1->field_b = r0
    //     0xb20cd4: stur            w0, [x1, #0xb]
    // 0xb20cd8: ldur            x0, [fp, #-0x10]
    // 0xb20cdc: StoreField: r1->field_13 = r0
    //     0xb20cdc: stur            w0, [x1, #0x13]
    // 0xb20ce0: r0 = Padding()
    //     0xb20ce0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb20ce4: mov             x1, x0
    // 0xb20ce8: r0 = Instance_EdgeInsets
    //     0xb20ce8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb20cec: ldr             x0, [x0, #0x980]
    // 0xb20cf0: stur            x1, [fp, #-0x10]
    // 0xb20cf4: StoreField: r1->field_f = r0
    //     0xb20cf4: stur            w0, [x1, #0xf]
    // 0xb20cf8: ldur            x0, [fp, #-0x20]
    // 0xb20cfc: StoreField: r1->field_b = r0
    //     0xb20cfc: stur            w0, [x1, #0xb]
    // 0xb20d00: ldur            x0, [fp, #-8]
    // 0xb20d04: LoadField: r2 = r0->field_b
    //     0xb20d04: ldur            w2, [x0, #0xb]
    // 0xb20d08: DecompressPointer r2
    //     0xb20d08: add             x2, x2, HEAP, lsl #32
    // 0xb20d0c: cmp             w2, NULL
    // 0xb20d10: b.eq            #0xb20e80
    // 0xb20d14: LoadField: r0 = r2->field_b
    //     0xb20d14: ldur            w0, [x2, #0xb]
    // 0xb20d18: DecompressPointer r0
    //     0xb20d18: add             x0, x0, HEAP, lsl #32
    // 0xb20d1c: cmp             w0, NULL
    // 0xb20d20: b.ne            #0xb20d2c
    // 0xb20d24: r2 = ""
    //     0xb20d24: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb20d28: b               #0xb20d30
    // 0xb20d2c: mov             x2, x0
    // 0xb20d30: ldur            x0, [fp, #-0x18]
    // 0xb20d34: stur            x2, [fp, #-8]
    // 0xb20d38: r0 = CachedNetworkImage()
    //     0xb20d38: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb20d3c: stur            x0, [fp, #-0x20]
    // 0xb20d40: r16 = Instance_BoxFit
    //     0xb20d40: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb20d44: ldr             x16, [x16, #0xb18]
    // 0xb20d48: str             x16, [SP]
    // 0xb20d4c: mov             x1, x0
    // 0xb20d50: ldur            x2, [fp, #-8]
    // 0xb20d54: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb20d54: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb20d58: ldr             x4, [x4, #0xb0]
    // 0xb20d5c: r0 = CachedNetworkImage()
    //     0xb20d5c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb20d60: r1 = Null
    //     0xb20d60: mov             x1, NULL
    // 0xb20d64: r2 = 8
    //     0xb20d64: movz            x2, #0x8
    // 0xb20d68: r0 = AllocateArray()
    //     0xb20d68: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb20d6c: mov             x2, x0
    // 0xb20d70: ldur            x0, [fp, #-0x18]
    // 0xb20d74: stur            x2, [fp, #-8]
    // 0xb20d78: StoreField: r2->field_f = r0
    //     0xb20d78: stur            w0, [x2, #0xf]
    // 0xb20d7c: ldur            x0, [fp, #-0x10]
    // 0xb20d80: StoreField: r2->field_13 = r0
    //     0xb20d80: stur            w0, [x2, #0x13]
    // 0xb20d84: r16 = Instance_SizedBox
    //     0xb20d84: add             x16, PP, #0x53, lsl #12  ; [pp+0x53df8] Obj!SizedBox@d68061
    //     0xb20d88: ldr             x16, [x16, #0xdf8]
    // 0xb20d8c: ArrayStore: r2[0] = r16  ; List_4
    //     0xb20d8c: stur            w16, [x2, #0x17]
    // 0xb20d90: ldur            x0, [fp, #-0x20]
    // 0xb20d94: StoreField: r2->field_1b = r0
    //     0xb20d94: stur            w0, [x2, #0x1b]
    // 0xb20d98: r1 = <Widget>
    //     0xb20d98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb20d9c: r0 = AllocateGrowableArray()
    //     0xb20d9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb20da0: mov             x1, x0
    // 0xb20da4: ldur            x0, [fp, #-8]
    // 0xb20da8: stur            x1, [fp, #-0x10]
    // 0xb20dac: StoreField: r1->field_f = r0
    //     0xb20dac: stur            w0, [x1, #0xf]
    // 0xb20db0: r0 = 8
    //     0xb20db0: movz            x0, #0x8
    // 0xb20db4: StoreField: r1->field_b = r0
    //     0xb20db4: stur            w0, [x1, #0xb]
    // 0xb20db8: r0 = Column()
    //     0xb20db8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb20dbc: mov             x1, x0
    // 0xb20dc0: r0 = Instance_Axis
    //     0xb20dc0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb20dc4: stur            x1, [fp, #-8]
    // 0xb20dc8: StoreField: r1->field_f = r0
    //     0xb20dc8: stur            w0, [x1, #0xf]
    // 0xb20dcc: r0 = Instance_MainAxisAlignment
    //     0xb20dcc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb20dd0: ldr             x0, [x0, #0xa08]
    // 0xb20dd4: StoreField: r1->field_13 = r0
    //     0xb20dd4: stur            w0, [x1, #0x13]
    // 0xb20dd8: r0 = Instance_MainAxisSize
    //     0xb20dd8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb20ddc: ldr             x0, [x0, #0xdd0]
    // 0xb20de0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb20de0: stur            w0, [x1, #0x17]
    // 0xb20de4: r0 = Instance_CrossAxisAlignment
    //     0xb20de4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb20de8: ldr             x0, [x0, #0xa18]
    // 0xb20dec: StoreField: r1->field_1b = r0
    //     0xb20dec: stur            w0, [x1, #0x1b]
    // 0xb20df0: r0 = Instance_VerticalDirection
    //     0xb20df0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb20df4: ldr             x0, [x0, #0xa20]
    // 0xb20df8: StoreField: r1->field_23 = r0
    //     0xb20df8: stur            w0, [x1, #0x23]
    // 0xb20dfc: r0 = Instance_Clip
    //     0xb20dfc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb20e00: ldr             x0, [x0, #0x38]
    // 0xb20e04: StoreField: r1->field_2b = r0
    //     0xb20e04: stur            w0, [x1, #0x2b]
    // 0xb20e08: StoreField: r1->field_2f = rZR
    //     0xb20e08: stur            xzr, [x1, #0x2f]
    // 0xb20e0c: ldur            x0, [fp, #-0x10]
    // 0xb20e10: StoreField: r1->field_b = r0
    //     0xb20e10: stur            w0, [x1, #0xb]
    // 0xb20e14: r0 = Scaffold()
    //     0xb20e14: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xb20e18: mov             x1, x0
    // 0xb20e1c: ldur            x0, [fp, #-8]
    // 0xb20e20: stur            x1, [fp, #-0x10]
    // 0xb20e24: ArrayStore: r1[0] = r0  ; List_4
    //     0xb20e24: stur            w0, [x1, #0x17]
    // 0xb20e28: r0 = true
    //     0xb20e28: add             x0, NULL, #0x20  ; true
    // 0xb20e2c: StoreField: r1->field_43 = r0
    //     0xb20e2c: stur            w0, [x1, #0x43]
    // 0xb20e30: r2 = false
    //     0xb20e30: add             x2, NULL, #0x30  ; false
    // 0xb20e34: StoreField: r1->field_b = r2
    //     0xb20e34: stur            w2, [x1, #0xb]
    // 0xb20e38: StoreField: r1->field_f = r2
    //     0xb20e38: stur            w2, [x1, #0xf]
    // 0xb20e3c: r0 = SafeArea()
    //     0xb20e3c: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xb20e40: r1 = true
    //     0xb20e40: add             x1, NULL, #0x20  ; true
    // 0xb20e44: StoreField: r0->field_b = r1
    //     0xb20e44: stur            w1, [x0, #0xb]
    // 0xb20e48: StoreField: r0->field_f = r1
    //     0xb20e48: stur            w1, [x0, #0xf]
    // 0xb20e4c: StoreField: r0->field_13 = r1
    //     0xb20e4c: stur            w1, [x0, #0x13]
    // 0xb20e50: ArrayStore: r0[0] = r1  ; List_4
    //     0xb20e50: stur            w1, [x0, #0x17]
    // 0xb20e54: r1 = Instance_EdgeInsets
    //     0xb20e54: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb20e58: StoreField: r0->field_1b = r1
    //     0xb20e58: stur            w1, [x0, #0x1b]
    // 0xb20e5c: r1 = false
    //     0xb20e5c: add             x1, NULL, #0x30  ; false
    // 0xb20e60: StoreField: r0->field_1f = r1
    //     0xb20e60: stur            w1, [x0, #0x1f]
    // 0xb20e64: ldur            x1, [fp, #-0x10]
    // 0xb20e68: StoreField: r0->field_23 = r1
    //     0xb20e68: stur            w1, [x0, #0x23]
    // 0xb20e6c: LeaveFrame
    //     0xb20e6c: mov             SP, fp
    //     0xb20e70: ldp             fp, lr, [SP], #0x10
    // 0xb20e74: ret
    //     0xb20e74: ret             
    // 0xb20e78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb20e78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb20e7c: b               #0xb20ad4
    // 0xb20e80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb20e80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4128, size: 0x10, field offset: 0xc
//   const constructor, 
class ViewSizeChart extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e3ac, size: 0x24
    // 0xc7e3ac: EnterFrame
    //     0xc7e3ac: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e3b0: mov             fp, SP
    // 0xc7e3b4: mov             x0, x1
    // 0xc7e3b8: r1 = <ViewSizeChart>
    //     0xc7e3b8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48d20] TypeArguments: <ViewSizeChart>
    //     0xc7e3bc: ldr             x1, [x1, #0xd20]
    // 0xc7e3c0: r0 = _ViewSizeChartState()
    //     0xc7e3c0: bl              #0xc7e3d0  ; Allocate_ViewSizeChartStateStub -> _ViewSizeChartState (size=0x14)
    // 0xc7e3c4: LeaveFrame
    //     0xc7e3c4: mov             SP, fp
    //     0xc7e3c8: ldp             fp, lr, [SP], #0x10
    // 0xc7e3cc: ret
    //     0xc7e3cc: ret             
  }
}
