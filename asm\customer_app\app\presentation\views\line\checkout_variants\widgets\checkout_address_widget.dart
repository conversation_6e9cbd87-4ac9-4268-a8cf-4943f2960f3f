// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart

// class id: 1049485, size: 0x8
class :: {
}

// class id: 3280, size: 0x6c, field offset: 0x14
class CheckoutAddressWidgetState extends State<dynamic> {

  [closure] bool <anonymous closure>(dynamic, String) {
    // ** addr: 0x9051f4, size: 0x1c
    // 0x9051f4: ldr             x1, [SP]
    // 0x9051f8: LoadField: r2 = r1->field_7
    //     0x9051f8: ldur            w2, [x1, #7]
    // 0x9051fc: cmp             w2, #2
    // 0x905200: r16 = true
    //     0x905200: add             x16, NULL, #0x20  ; true
    // 0x905204: r17 = false
    //     0x905204: add             x17, NULL, #0x30  ; false
    // 0x905208: csel            x0, x16, x17, eq
    // 0x90520c: ret
    //     0x90520c: ret             
  }
  _ _checkFirstTimeInput(/* No info */) {
    // ** addr: 0x905210, size: 0x1b4
    // 0x905210: EnterFrame
    //     0x905210: stp             fp, lr, [SP, #-0x10]!
    //     0x905214: mov             fp, SP
    // 0x905218: AllocStack(0x48)
    //     0x905218: sub             SP, SP, #0x48
    // 0x90521c: r0 = 12
    //     0x90521c: movz            x0, #0xc
    // 0x905220: mov             x3, x1
    // 0x905224: stur            x1, [fp, #-0x38]
    // 0x905228: CheckStackOverflow
    //     0x905228: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90522c: cmp             SP, x16
    //     0x905230: b.ls            #0x9053b8
    // 0x905234: LoadField: r1 = r3->field_33
    //     0x905234: ldur            w1, [x3, #0x33]
    // 0x905238: DecompressPointer r1
    //     0x905238: add             x1, x1, HEAP, lsl #32
    // 0x90523c: LoadField: r2 = r1->field_27
    //     0x90523c: ldur            w2, [x1, #0x27]
    // 0x905240: DecompressPointer r2
    //     0x905240: add             x2, x2, HEAP, lsl #32
    // 0x905244: LoadField: r4 = r2->field_7
    //     0x905244: ldur            w4, [x2, #7]
    // 0x905248: DecompressPointer r4
    //     0x905248: add             x4, x4, HEAP, lsl #32
    // 0x90524c: stur            x4, [fp, #-0x30]
    // 0x905250: LoadField: r1 = r3->field_3f
    //     0x905250: ldur            w1, [x3, #0x3f]
    // 0x905254: DecompressPointer r1
    //     0x905254: add             x1, x1, HEAP, lsl #32
    // 0x905258: LoadField: r2 = r1->field_27
    //     0x905258: ldur            w2, [x1, #0x27]
    // 0x90525c: DecompressPointer r2
    //     0x90525c: add             x2, x2, HEAP, lsl #32
    // 0x905260: LoadField: r5 = r2->field_7
    //     0x905260: ldur            w5, [x2, #7]
    // 0x905264: DecompressPointer r5
    //     0x905264: add             x5, x5, HEAP, lsl #32
    // 0x905268: stur            x5, [fp, #-0x28]
    // 0x90526c: LoadField: r1 = r3->field_3b
    //     0x90526c: ldur            w1, [x3, #0x3b]
    // 0x905270: DecompressPointer r1
    //     0x905270: add             x1, x1, HEAP, lsl #32
    // 0x905274: LoadField: r2 = r1->field_27
    //     0x905274: ldur            w2, [x1, #0x27]
    // 0x905278: DecompressPointer r2
    //     0x905278: add             x2, x2, HEAP, lsl #32
    // 0x90527c: LoadField: r6 = r2->field_7
    //     0x90527c: ldur            w6, [x2, #7]
    // 0x905280: DecompressPointer r6
    //     0x905280: add             x6, x6, HEAP, lsl #32
    // 0x905284: stur            x6, [fp, #-0x20]
    // 0x905288: LoadField: r1 = r3->field_47
    //     0x905288: ldur            w1, [x3, #0x47]
    // 0x90528c: DecompressPointer r1
    //     0x90528c: add             x1, x1, HEAP, lsl #32
    // 0x905290: LoadField: r2 = r1->field_27
    //     0x905290: ldur            w2, [x1, #0x27]
    // 0x905294: DecompressPointer r2
    //     0x905294: add             x2, x2, HEAP, lsl #32
    // 0x905298: LoadField: r7 = r2->field_7
    //     0x905298: ldur            w7, [x2, #7]
    // 0x90529c: DecompressPointer r7
    //     0x90529c: add             x7, x7, HEAP, lsl #32
    // 0x9052a0: stur            x7, [fp, #-0x18]
    // 0x9052a4: LoadField: r1 = r3->field_43
    //     0x9052a4: ldur            w1, [x3, #0x43]
    // 0x9052a8: DecompressPointer r1
    //     0x9052a8: add             x1, x1, HEAP, lsl #32
    // 0x9052ac: LoadField: r2 = r1->field_27
    //     0x9052ac: ldur            w2, [x1, #0x27]
    // 0x9052b0: DecompressPointer r2
    //     0x9052b0: add             x2, x2, HEAP, lsl #32
    // 0x9052b4: LoadField: r8 = r2->field_7
    //     0x9052b4: ldur            w8, [x2, #7]
    // 0x9052b8: DecompressPointer r8
    //     0x9052b8: add             x8, x8, HEAP, lsl #32
    // 0x9052bc: stur            x8, [fp, #-0x10]
    // 0x9052c0: LoadField: r1 = r3->field_37
    //     0x9052c0: ldur            w1, [x3, #0x37]
    // 0x9052c4: DecompressPointer r1
    //     0x9052c4: add             x1, x1, HEAP, lsl #32
    // 0x9052c8: LoadField: r2 = r1->field_27
    //     0x9052c8: ldur            w2, [x1, #0x27]
    // 0x9052cc: DecompressPointer r2
    //     0x9052cc: add             x2, x2, HEAP, lsl #32
    // 0x9052d0: LoadField: r9 = r2->field_7
    //     0x9052d0: ldur            w9, [x2, #7]
    // 0x9052d4: DecompressPointer r9
    //     0x9052d4: add             x9, x9, HEAP, lsl #32
    // 0x9052d8: mov             x2, x0
    // 0x9052dc: stur            x9, [fp, #-8]
    // 0x9052e0: r1 = Null
    //     0x9052e0: mov             x1, NULL
    // 0x9052e4: r0 = AllocateArray()
    //     0x9052e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9052e8: mov             x2, x0
    // 0x9052ec: ldur            x0, [fp, #-0x30]
    // 0x9052f0: stur            x2, [fp, #-0x40]
    // 0x9052f4: StoreField: r2->field_f = r0
    //     0x9052f4: stur            w0, [x2, #0xf]
    // 0x9052f8: ldur            x0, [fp, #-0x28]
    // 0x9052fc: StoreField: r2->field_13 = r0
    //     0x9052fc: stur            w0, [x2, #0x13]
    // 0x905300: ldur            x0, [fp, #-0x20]
    // 0x905304: ArrayStore: r2[0] = r0  ; List_4
    //     0x905304: stur            w0, [x2, #0x17]
    // 0x905308: ldur            x0, [fp, #-0x18]
    // 0x90530c: StoreField: r2->field_1b = r0
    //     0x90530c: stur            w0, [x2, #0x1b]
    // 0x905310: ldur            x0, [fp, #-0x10]
    // 0x905314: StoreField: r2->field_1f = r0
    //     0x905314: stur            w0, [x2, #0x1f]
    // 0x905318: ldur            x0, [fp, #-8]
    // 0x90531c: StoreField: r2->field_23 = r0
    //     0x90531c: stur            w0, [x2, #0x23]
    // 0x905320: r1 = <String>
    //     0x905320: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x905324: r0 = AllocateGrowableArray()
    //     0x905324: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x905328: mov             x3, x0
    // 0x90532c: ldur            x0, [fp, #-0x40]
    // 0x905330: stur            x3, [fp, #-8]
    // 0x905334: StoreField: r3->field_f = r0
    //     0x905334: stur            w0, [x3, #0xf]
    // 0x905338: r0 = 12
    //     0x905338: movz            x0, #0xc
    // 0x90533c: StoreField: r3->field_b = r0
    //     0x90533c: stur            w0, [x3, #0xb]
    // 0x905340: r1 = Function '<anonymous closure>':.
    //     0x905340: add             x1, PP, #0x54, lsl #12  ; [pp+0x54198] AnonymousClosure: (0x9051f4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_checkFirstTimeInput (0x905210)
    //     0x905344: ldr             x1, [x1, #0x198]
    // 0x905348: r2 = Null
    //     0x905348: mov             x2, NULL
    // 0x90534c: r0 = AllocateClosure()
    //     0x90534c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x905350: ldur            x1, [fp, #-8]
    // 0x905354: mov             x2, x0
    // 0x905358: r0 = any()
    //     0x905358: bl              #0x7d624c  ; [dart:collection] ListBase::any
    // 0x90535c: tbnz            w0, #4, #0x9053a8
    // 0x905360: ldur            x0, [fp, #-0x38]
    // 0x905364: LoadField: r1 = r0->field_4b
    //     0x905364: ldur            x1, [x0, #0x4b]
    // 0x905368: add             x2, x1, #1
    // 0x90536c: StoreField: r0->field_4b = r2
    //     0x90536c: stur            x2, [x0, #0x4b]
    // 0x905370: cmp             x2, #1
    // 0x905374: b.ne            #0x9053a8
    // 0x905378: LoadField: r1 = r0->field_b
    //     0x905378: ldur            w1, [x0, #0xb]
    // 0x90537c: DecompressPointer r1
    //     0x90537c: add             x1, x1, HEAP, lsl #32
    // 0x905380: cmp             w1, NULL
    // 0x905384: b.eq            #0x9053c0
    // 0x905388: LoadField: r0 = r1->field_1f
    //     0x905388: ldur            w0, [x1, #0x1f]
    // 0x90538c: DecompressPointer r0
    //     0x90538c: add             x0, x0, HEAP, lsl #32
    // 0x905390: str             x0, [SP]
    // 0x905394: r4 = 0
    //     0x905394: movz            x4, #0
    // 0x905398: ldr             x0, [SP]
    // 0x90539c: r5 = UnlinkedCall_0x613b5c
    //     0x90539c: add             x16, PP, #0x54, lsl #12  ; [pp+0x541a0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9053a0: ldp             x5, lr, [x16, #0x1a0]
    // 0x9053a4: blr             lr
    // 0x9053a8: r0 = Null
    //     0x9053a8: mov             x0, NULL
    // 0x9053ac: LeaveFrame
    //     0x9053ac: mov             SP, fp
    //     0x9053b0: ldp             fp, lr, [SP], #0x10
    // 0x9053b4: ret
    //     0x9053b4: ret             
    // 0x9053b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9053b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9053bc: b               #0x905234
    // 0x9053c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9053c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x946b50, size: 0x54c
    // 0x946b50: EnterFrame
    //     0x946b50: stp             fp, lr, [SP, #-0x10]!
    //     0x946b54: mov             fp, SP
    // 0x946b58: AllocStack(0x28)
    //     0x946b58: sub             SP, SP, #0x28
    // 0x946b5c: SetupParameters(CheckoutAddressWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x946b5c: stur            x1, [fp, #-8]
    // 0x946b60: CheckStackOverflow
    //     0x946b60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x946b64: cmp             SP, x16
    //     0x946b68: b.ls            #0x94706c
    // 0x946b6c: r1 = 1
    //     0x946b6c: movz            x1, #0x1
    // 0x946b70: r0 = AllocateContext()
    //     0x946b70: bl              #0x16f6108  ; AllocateContextStub
    // 0x946b74: ldur            x3, [fp, #-8]
    // 0x946b78: StoreField: r0->field_f = r3
    //     0x946b78: stur            w3, [x0, #0xf]
    // 0x946b7c: LoadField: r1 = r3->field_b
    //     0x946b7c: ldur            w1, [x3, #0xb]
    // 0x946b80: DecompressPointer r1
    //     0x946b80: add             x1, x1, HEAP, lsl #32
    // 0x946b84: cmp             w1, NULL
    // 0x946b88: b.eq            #0x947074
    // 0x946b8c: LoadField: r2 = r1->field_1b
    //     0x946b8c: ldur            w2, [x1, #0x1b]
    // 0x946b90: DecompressPointer r2
    //     0x946b90: add             x2, x2, HEAP, lsl #32
    // 0x946b94: LoadField: r4 = r2->field_1b
    //     0x946b94: ldur            w4, [x2, #0x1b]
    // 0x946b98: DecompressPointer r4
    //     0x946b98: add             x4, x4, HEAP, lsl #32
    // 0x946b9c: cmp             w4, NULL
    // 0x946ba0: b.ne            #0x946bac
    // 0x946ba4: r1 = Null
    //     0x946ba4: mov             x1, NULL
    // 0x946ba8: b               #0x946bc4
    // 0x946bac: LoadField: r1 = r4->field_b
    //     0x946bac: ldur            w1, [x4, #0xb]
    // 0x946bb0: cbnz            w1, #0x946bbc
    // 0x946bb4: r2 = false
    //     0x946bb4: add             x2, NULL, #0x30  ; false
    // 0x946bb8: b               #0x946bc0
    // 0x946bbc: r2 = true
    //     0x946bbc: add             x2, NULL, #0x20  ; true
    // 0x946bc0: mov             x1, x2
    // 0x946bc4: cmp             w1, NULL
    // 0x946bc8: b.eq            #0x946f58
    // 0x946bcc: tbnz            w1, #4, #0x946f58
    // 0x946bd0: cmp             w4, NULL
    // 0x946bd4: b.ne            #0x946be0
    // 0x946bd8: r2 = Null
    //     0x946bd8: mov             x2, NULL
    // 0x946bdc: b               #0x946c0c
    // 0x946be0: LoadField: r0 = r4->field_b
    //     0x946be0: ldur            w0, [x4, #0xb]
    // 0x946be4: r1 = LoadInt32Instr(r0)
    //     0x946be4: sbfx            x1, x0, #1, #0x1f
    // 0x946be8: mov             x0, x1
    // 0x946bec: r1 = 0
    //     0x946bec: movz            x1, #0
    // 0x946bf0: cmp             x1, x0
    // 0x946bf4: b.hs            #0x947078
    // 0x946bf8: LoadField: r0 = r4->field_f
    //     0x946bf8: ldur            w0, [x4, #0xf]
    // 0x946bfc: DecompressPointer r0
    //     0x946bfc: add             x0, x0, HEAP, lsl #32
    // 0x946c00: LoadField: r1 = r0->field_f
    //     0x946c00: ldur            w1, [x0, #0xf]
    // 0x946c04: DecompressPointer r1
    //     0x946c04: add             x1, x1, HEAP, lsl #32
    // 0x946c08: mov             x2, x1
    // 0x946c0c: mov             x1, x3
    // 0x946c10: r0 = setUpAddress()
    //     0x946c10: bl              #0x94709c  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::setUpAddress
    // 0x946c14: ldur            x0, [fp, #-8]
    // 0x946c18: LoadField: r1 = r0->field_b
    //     0x946c18: ldur            w1, [x0, #0xb]
    // 0x946c1c: DecompressPointer r1
    //     0x946c1c: add             x1, x1, HEAP, lsl #32
    // 0x946c20: cmp             w1, NULL
    // 0x946c24: b.eq            #0x94707c
    // 0x946c28: LoadField: r2 = r1->field_1f
    //     0x946c28: ldur            w2, [x1, #0x1f]
    // 0x946c2c: DecompressPointer r2
    //     0x946c2c: add             x2, x2, HEAP, lsl #32
    // 0x946c30: str             x2, [SP]
    // 0x946c34: r4 = 0
    //     0x946c34: movz            x4, #0
    // 0x946c38: ldr             x0, [SP]
    // 0x946c3c: r16 = UnlinkedCall_0x613b5c
    //     0x946c3c: add             x16, PP, #0x54, lsl #12  ; [pp+0x54240] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x946c40: add             x16, x16, #0x240
    // 0x946c44: ldp             x5, lr, [x16]
    // 0x946c48: blr             lr
    // 0x946c4c: ldur            x3, [fp, #-8]
    // 0x946c50: LoadField: r0 = r3->field_b
    //     0x946c50: ldur            w0, [x3, #0xb]
    // 0x946c54: DecompressPointer r0
    //     0x946c54: add             x0, x0, HEAP, lsl #32
    // 0x946c58: cmp             w0, NULL
    // 0x946c5c: b.eq            #0x947080
    // 0x946c60: LoadField: r1 = r0->field_1b
    //     0x946c60: ldur            w1, [x0, #0x1b]
    // 0x946c64: DecompressPointer r1
    //     0x946c64: add             x1, x1, HEAP, lsl #32
    // 0x946c68: LoadField: r0 = r1->field_b
    //     0x946c68: ldur            w0, [x1, #0xb]
    // 0x946c6c: DecompressPointer r0
    //     0x946c6c: add             x0, x0, HEAP, lsl #32
    // 0x946c70: cmp             w0, NULL
    // 0x946c74: b.ne            #0x946c80
    // 0x946c78: r0 = Null
    //     0x946c78: mov             x0, NULL
    // 0x946c7c: b               #0x946c94
    // 0x946c80: LoadField: r2 = r0->field_7
    //     0x946c80: ldur            w2, [x0, #7]
    // 0x946c84: cbnz            w2, #0x946c90
    // 0x946c88: r0 = false
    //     0x946c88: add             x0, NULL, #0x30  ; false
    // 0x946c8c: b               #0x946c94
    // 0x946c90: r0 = true
    //     0x946c90: add             x0, NULL, #0x20  ; true
    // 0x946c94: cmp             w0, NULL
    // 0x946c98: b.ne            #0x946ca4
    // 0x946c9c: r2 = true
    //     0x946c9c: add             x2, NULL, #0x20  ; true
    // 0x946ca0: b               #0x946cb8
    // 0x946ca4: tbnz            w0, #4, #0x946cb4
    // 0x946ca8: r2 = true
    //     0x946ca8: add             x2, NULL, #0x20  ; true
    // 0x946cac: StoreField: r3->field_53 = r2
    //     0x946cac: stur            w2, [x3, #0x53]
    // 0x946cb0: b               #0x946cb8
    // 0x946cb4: r2 = true
    //     0x946cb4: add             x2, NULL, #0x20  ; true
    // 0x946cb8: LoadField: r4 = r1->field_1b
    //     0x946cb8: ldur            w4, [x1, #0x1b]
    // 0x946cbc: DecompressPointer r4
    //     0x946cbc: add             x4, x4, HEAP, lsl #32
    // 0x946cc0: cmp             w4, NULL
    // 0x946cc4: b.ne            #0x946cd0
    // 0x946cc8: r0 = Null
    //     0x946cc8: mov             x0, NULL
    // 0x946ccc: b               #0x946d34
    // 0x946cd0: LoadField: r0 = r4->field_b
    //     0x946cd0: ldur            w0, [x4, #0xb]
    // 0x946cd4: r1 = LoadInt32Instr(r0)
    //     0x946cd4: sbfx            x1, x0, #1, #0x1f
    // 0x946cd8: mov             x0, x1
    // 0x946cdc: r1 = 0
    //     0x946cdc: movz            x1, #0
    // 0x946ce0: cmp             x1, x0
    // 0x946ce4: b.hs            #0x947084
    // 0x946ce8: LoadField: r0 = r4->field_f
    //     0x946ce8: ldur            w0, [x4, #0xf]
    // 0x946cec: DecompressPointer r0
    //     0x946cec: add             x0, x0, HEAP, lsl #32
    // 0x946cf0: LoadField: r1 = r0->field_f
    //     0x946cf0: ldur            w1, [x0, #0xf]
    // 0x946cf4: DecompressPointer r1
    //     0x946cf4: add             x1, x1, HEAP, lsl #32
    // 0x946cf8: cmp             w1, NULL
    // 0x946cfc: b.ne            #0x946d08
    // 0x946d00: r0 = Null
    //     0x946d00: mov             x0, NULL
    // 0x946d04: b               #0x946d34
    // 0x946d08: LoadField: r0 = r1->field_13
    //     0x946d08: ldur            w0, [x1, #0x13]
    // 0x946d0c: DecompressPointer r0
    //     0x946d0c: add             x0, x0, HEAP, lsl #32
    // 0x946d10: cmp             w0, NULL
    // 0x946d14: b.ne            #0x946d20
    // 0x946d18: r0 = Null
    //     0x946d18: mov             x0, NULL
    // 0x946d1c: b               #0x946d34
    // 0x946d20: LoadField: r1 = r0->field_7
    //     0x946d20: ldur            w1, [x0, #7]
    // 0x946d24: cbnz            w1, #0x946d30
    // 0x946d28: r0 = false
    //     0x946d28: add             x0, NULL, #0x30  ; false
    // 0x946d2c: b               #0x946d34
    // 0x946d30: r0 = true
    //     0x946d30: add             x0, NULL, #0x20  ; true
    // 0x946d34: cmp             w0, NULL
    // 0x946d38: b.eq            #0x946d44
    // 0x946d3c: tbnz            w0, #4, #0x946d44
    // 0x946d40: StoreField: r3->field_5b = r2
    //     0x946d40: stur            w2, [x3, #0x5b]
    // 0x946d44: cmp             w4, NULL
    // 0x946d48: b.ne            #0x946d54
    // 0x946d4c: r0 = Null
    //     0x946d4c: mov             x0, NULL
    // 0x946d50: b               #0x946db8
    // 0x946d54: LoadField: r0 = r4->field_b
    //     0x946d54: ldur            w0, [x4, #0xb]
    // 0x946d58: r1 = LoadInt32Instr(r0)
    //     0x946d58: sbfx            x1, x0, #1, #0x1f
    // 0x946d5c: mov             x0, x1
    // 0x946d60: r1 = 0
    //     0x946d60: movz            x1, #0
    // 0x946d64: cmp             x1, x0
    // 0x946d68: b.hs            #0x947088
    // 0x946d6c: LoadField: r0 = r4->field_f
    //     0x946d6c: ldur            w0, [x4, #0xf]
    // 0x946d70: DecompressPointer r0
    //     0x946d70: add             x0, x0, HEAP, lsl #32
    // 0x946d74: LoadField: r1 = r0->field_f
    //     0x946d74: ldur            w1, [x0, #0xf]
    // 0x946d78: DecompressPointer r1
    //     0x946d78: add             x1, x1, HEAP, lsl #32
    // 0x946d7c: cmp             w1, NULL
    // 0x946d80: b.ne            #0x946d8c
    // 0x946d84: r0 = Null
    //     0x946d84: mov             x0, NULL
    // 0x946d88: b               #0x946db8
    // 0x946d8c: LoadField: r0 = r1->field_2b
    //     0x946d8c: ldur            w0, [x1, #0x2b]
    // 0x946d90: DecompressPointer r0
    //     0x946d90: add             x0, x0, HEAP, lsl #32
    // 0x946d94: cmp             w0, NULL
    // 0x946d98: b.ne            #0x946da4
    // 0x946d9c: r0 = Null
    //     0x946d9c: mov             x0, NULL
    // 0x946da0: b               #0x946db8
    // 0x946da4: LoadField: r1 = r0->field_7
    //     0x946da4: ldur            w1, [x0, #7]
    // 0x946da8: cbnz            w1, #0x946db4
    // 0x946dac: r0 = false
    //     0x946dac: add             x0, NULL, #0x30  ; false
    // 0x946db0: b               #0x946db8
    // 0x946db4: r0 = true
    //     0x946db4: add             x0, NULL, #0x20  ; true
    // 0x946db8: cmp             w0, NULL
    // 0x946dbc: b.eq            #0x946dc8
    // 0x946dc0: tbnz            w0, #4, #0x946dc8
    // 0x946dc4: StoreField: r3->field_57 = r2
    //     0x946dc4: stur            w2, [x3, #0x57]
    // 0x946dc8: cmp             w4, NULL
    // 0x946dcc: b.ne            #0x946dd8
    // 0x946dd0: r0 = Null
    //     0x946dd0: mov             x0, NULL
    // 0x946dd4: b               #0x946e3c
    // 0x946dd8: LoadField: r0 = r4->field_b
    //     0x946dd8: ldur            w0, [x4, #0xb]
    // 0x946ddc: r1 = LoadInt32Instr(r0)
    //     0x946ddc: sbfx            x1, x0, #1, #0x1f
    // 0x946de0: mov             x0, x1
    // 0x946de4: r1 = 0
    //     0x946de4: movz            x1, #0
    // 0x946de8: cmp             x1, x0
    // 0x946dec: b.hs            #0x94708c
    // 0x946df0: LoadField: r0 = r4->field_f
    //     0x946df0: ldur            w0, [x4, #0xf]
    // 0x946df4: DecompressPointer r0
    //     0x946df4: add             x0, x0, HEAP, lsl #32
    // 0x946df8: LoadField: r1 = r0->field_f
    //     0x946df8: ldur            w1, [x0, #0xf]
    // 0x946dfc: DecompressPointer r1
    //     0x946dfc: add             x1, x1, HEAP, lsl #32
    // 0x946e00: cmp             w1, NULL
    // 0x946e04: b.ne            #0x946e10
    // 0x946e08: r0 = Null
    //     0x946e08: mov             x0, NULL
    // 0x946e0c: b               #0x946e3c
    // 0x946e10: LoadField: r0 = r1->field_1b
    //     0x946e10: ldur            w0, [x1, #0x1b]
    // 0x946e14: DecompressPointer r0
    //     0x946e14: add             x0, x0, HEAP, lsl #32
    // 0x946e18: cmp             w0, NULL
    // 0x946e1c: b.ne            #0x946e28
    // 0x946e20: r0 = Null
    //     0x946e20: mov             x0, NULL
    // 0x946e24: b               #0x946e3c
    // 0x946e28: LoadField: r1 = r0->field_7
    //     0x946e28: ldur            w1, [x0, #7]
    // 0x946e2c: cbnz            w1, #0x946e38
    // 0x946e30: r0 = false
    //     0x946e30: add             x0, NULL, #0x30  ; false
    // 0x946e34: b               #0x946e3c
    // 0x946e38: r0 = true
    //     0x946e38: add             x0, NULL, #0x20  ; true
    // 0x946e3c: cmp             w0, NULL
    // 0x946e40: b.eq            #0x946e4c
    // 0x946e44: tbnz            w0, #4, #0x946e4c
    // 0x946e48: StoreField: r3->field_5f = r2
    //     0x946e48: stur            w2, [x3, #0x5f]
    // 0x946e4c: cmp             w4, NULL
    // 0x946e50: b.ne            #0x946e5c
    // 0x946e54: r0 = Null
    //     0x946e54: mov             x0, NULL
    // 0x946e58: b               #0x946ec0
    // 0x946e5c: LoadField: r0 = r4->field_b
    //     0x946e5c: ldur            w0, [x4, #0xb]
    // 0x946e60: r1 = LoadInt32Instr(r0)
    //     0x946e60: sbfx            x1, x0, #1, #0x1f
    // 0x946e64: mov             x0, x1
    // 0x946e68: r1 = 0
    //     0x946e68: movz            x1, #0
    // 0x946e6c: cmp             x1, x0
    // 0x946e70: b.hs            #0x947090
    // 0x946e74: LoadField: r0 = r4->field_f
    //     0x946e74: ldur            w0, [x4, #0xf]
    // 0x946e78: DecompressPointer r0
    //     0x946e78: add             x0, x0, HEAP, lsl #32
    // 0x946e7c: LoadField: r1 = r0->field_f
    //     0x946e7c: ldur            w1, [x0, #0xf]
    // 0x946e80: DecompressPointer r1
    //     0x946e80: add             x1, x1, HEAP, lsl #32
    // 0x946e84: cmp             w1, NULL
    // 0x946e88: b.ne            #0x946e94
    // 0x946e8c: r0 = Null
    //     0x946e8c: mov             x0, NULL
    // 0x946e90: b               #0x946ec0
    // 0x946e94: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x946e94: ldur            w0, [x1, #0x17]
    // 0x946e98: DecompressPointer r0
    //     0x946e98: add             x0, x0, HEAP, lsl #32
    // 0x946e9c: cmp             w0, NULL
    // 0x946ea0: b.ne            #0x946eac
    // 0x946ea4: r0 = Null
    //     0x946ea4: mov             x0, NULL
    // 0x946ea8: b               #0x946ec0
    // 0x946eac: LoadField: r1 = r0->field_7
    //     0x946eac: ldur            w1, [x0, #7]
    // 0x946eb0: cbnz            w1, #0x946ebc
    // 0x946eb4: r0 = false
    //     0x946eb4: add             x0, NULL, #0x30  ; false
    // 0x946eb8: b               #0x946ec0
    // 0x946ebc: r0 = true
    //     0x946ebc: add             x0, NULL, #0x20  ; true
    // 0x946ec0: cmp             w0, NULL
    // 0x946ec4: b.eq            #0x946ed0
    // 0x946ec8: tbnz            w0, #4, #0x946ed0
    // 0x946ecc: StoreField: r3->field_63 = r2
    //     0x946ecc: stur            w2, [x3, #0x63]
    // 0x946ed0: cmp             w4, NULL
    // 0x946ed4: b.ne            #0x946ee0
    // 0x946ed8: r0 = Null
    //     0x946ed8: mov             x0, NULL
    // 0x946edc: b               #0x946f44
    // 0x946ee0: LoadField: r0 = r4->field_b
    //     0x946ee0: ldur            w0, [x4, #0xb]
    // 0x946ee4: r1 = LoadInt32Instr(r0)
    //     0x946ee4: sbfx            x1, x0, #1, #0x1f
    // 0x946ee8: mov             x0, x1
    // 0x946eec: r1 = 0
    //     0x946eec: movz            x1, #0
    // 0x946ef0: cmp             x1, x0
    // 0x946ef4: b.hs            #0x947094
    // 0x946ef8: LoadField: r0 = r4->field_f
    //     0x946ef8: ldur            w0, [x4, #0xf]
    // 0x946efc: DecompressPointer r0
    //     0x946efc: add             x0, x0, HEAP, lsl #32
    // 0x946f00: LoadField: r1 = r0->field_f
    //     0x946f00: ldur            w1, [x0, #0xf]
    // 0x946f04: DecompressPointer r1
    //     0x946f04: add             x1, x1, HEAP, lsl #32
    // 0x946f08: cmp             w1, NULL
    // 0x946f0c: b.ne            #0x946f18
    // 0x946f10: r0 = Null
    //     0x946f10: mov             x0, NULL
    // 0x946f14: b               #0x946f44
    // 0x946f18: LoadField: r0 = r1->field_2f
    //     0x946f18: ldur            w0, [x1, #0x2f]
    // 0x946f1c: DecompressPointer r0
    //     0x946f1c: add             x0, x0, HEAP, lsl #32
    // 0x946f20: cmp             w0, NULL
    // 0x946f24: b.ne            #0x946f30
    // 0x946f28: r0 = Null
    //     0x946f28: mov             x0, NULL
    // 0x946f2c: b               #0x946f44
    // 0x946f30: LoadField: r1 = r0->field_7
    //     0x946f30: ldur            w1, [x0, #7]
    // 0x946f34: cbnz            w1, #0x946f40
    // 0x946f38: r0 = false
    //     0x946f38: add             x0, NULL, #0x30  ; false
    // 0x946f3c: b               #0x946f44
    // 0x946f40: r0 = true
    //     0x946f40: add             x0, NULL, #0x20  ; true
    // 0x946f44: cmp             w0, NULL
    // 0x946f48: b.eq            #0x94705c
    // 0x946f4c: tbnz            w0, #4, #0x94705c
    // 0x946f50: StoreField: r3->field_67 = r2
    //     0x946f50: stur            w2, [x3, #0x67]
    // 0x946f54: b               #0x94705c
    // 0x946f58: r1 = LoadStaticField(0x878)
    //     0x946f58: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x946f5c: ldr             x1, [x1, #0x10f0]
    // 0x946f60: cmp             w1, NULL
    // 0x946f64: b.eq            #0x947098
    // 0x946f68: LoadField: r4 = r1->field_53
    //     0x946f68: ldur            w4, [x1, #0x53]
    // 0x946f6c: DecompressPointer r4
    //     0x946f6c: add             x4, x4, HEAP, lsl #32
    // 0x946f70: stur            x4, [fp, #-0x18]
    // 0x946f74: LoadField: r5 = r4->field_7
    //     0x946f74: ldur            w5, [x4, #7]
    // 0x946f78: DecompressPointer r5
    //     0x946f78: add             x5, x5, HEAP, lsl #32
    // 0x946f7c: mov             x2, x0
    // 0x946f80: stur            x5, [fp, #-0x10]
    // 0x946f84: r1 = Function '<anonymous closure>':.
    //     0x946f84: add             x1, PP, #0x54, lsl #12  ; [pp+0x54250] AnonymousClosure: (0x947b54), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::initState (0x946b50)
    //     0x946f88: ldr             x1, [x1, #0x250]
    // 0x946f8c: r0 = AllocateClosure()
    //     0x946f8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x946f90: ldur            x2, [fp, #-0x10]
    // 0x946f94: mov             x3, x0
    // 0x946f98: r1 = Null
    //     0x946f98: mov             x1, NULL
    // 0x946f9c: stur            x3, [fp, #-0x10]
    // 0x946fa0: cmp             w2, NULL
    // 0x946fa4: b.eq            #0x946fc4
    // 0x946fa8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x946fa8: ldur            w4, [x2, #0x17]
    // 0x946fac: DecompressPointer r4
    //     0x946fac: add             x4, x4, HEAP, lsl #32
    // 0x946fb0: r8 = X0
    //     0x946fb0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x946fb4: LoadField: r9 = r4->field_7
    //     0x946fb4: ldur            x9, [x4, #7]
    // 0x946fb8: r3 = Null
    //     0x946fb8: add             x3, PP, #0x54, lsl #12  ; [pp+0x54258] Null
    //     0x946fbc: ldr             x3, [x3, #0x258]
    // 0x946fc0: blr             x9
    // 0x946fc4: ldur            x0, [fp, #-0x18]
    // 0x946fc8: LoadField: r1 = r0->field_b
    //     0x946fc8: ldur            w1, [x0, #0xb]
    // 0x946fcc: LoadField: r2 = r0->field_f
    //     0x946fcc: ldur            w2, [x0, #0xf]
    // 0x946fd0: DecompressPointer r2
    //     0x946fd0: add             x2, x2, HEAP, lsl #32
    // 0x946fd4: LoadField: r3 = r2->field_b
    //     0x946fd4: ldur            w3, [x2, #0xb]
    // 0x946fd8: r2 = LoadInt32Instr(r1)
    //     0x946fd8: sbfx            x2, x1, #1, #0x1f
    // 0x946fdc: stur            x2, [fp, #-0x20]
    // 0x946fe0: r1 = LoadInt32Instr(r3)
    //     0x946fe0: sbfx            x1, x3, #1, #0x1f
    // 0x946fe4: cmp             x2, x1
    // 0x946fe8: b.ne            #0x946ff4
    // 0x946fec: mov             x1, x0
    // 0x946ff0: r0 = _growToNextCapacity()
    //     0x946ff0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x946ff4: ldur            x4, [fp, #-8]
    // 0x946ff8: ldur            x2, [fp, #-0x18]
    // 0x946ffc: ldur            x3, [fp, #-0x20]
    // 0x947000: r5 = false
    //     0x947000: add             x5, NULL, #0x30  ; false
    // 0x947004: add             x6, x3, #1
    // 0x947008: lsl             x7, x6, #1
    // 0x94700c: StoreField: r2->field_b = r7
    //     0x94700c: stur            w7, [x2, #0xb]
    // 0x947010: LoadField: r1 = r2->field_f
    //     0x947010: ldur            w1, [x2, #0xf]
    // 0x947014: DecompressPointer r1
    //     0x947014: add             x1, x1, HEAP, lsl #32
    // 0x947018: ldur            x0, [fp, #-0x10]
    // 0x94701c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x94701c: add             x25, x1, x3, lsl #2
    //     0x947020: add             x25, x25, #0xf
    //     0x947024: str             w0, [x25]
    //     0x947028: tbz             w0, #0, #0x947044
    //     0x94702c: ldurb           w16, [x1, #-1]
    //     0x947030: ldurb           w17, [x0, #-1]
    //     0x947034: and             x16, x17, x16, lsr #2
    //     0x947038: tst             x16, HEAP, lsr #32
    //     0x94703c: b.eq            #0x947044
    //     0x947040: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x947044: StoreField: r4->field_53 = r5
    //     0x947044: stur            w5, [x4, #0x53]
    // 0x947048: StoreField: r4->field_57 = r5
    //     0x947048: stur            w5, [x4, #0x57]
    // 0x94704c: StoreField: r4->field_5b = r5
    //     0x94704c: stur            w5, [x4, #0x5b]
    // 0x947050: StoreField: r4->field_5f = r5
    //     0x947050: stur            w5, [x4, #0x5f]
    // 0x947054: StoreField: r4->field_63 = r5
    //     0x947054: stur            w5, [x4, #0x63]
    // 0x947058: StoreField: r4->field_67 = r5
    //     0x947058: stur            w5, [x4, #0x67]
    // 0x94705c: r0 = Null
    //     0x94705c: mov             x0, NULL
    // 0x947060: LeaveFrame
    //     0x947060: mov             SP, fp
    //     0x947064: ldp             fp, lr, [SP], #0x10
    // 0x947068: ret
    //     0x947068: ret             
    // 0x94706c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94706c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x947070: b               #0x946b6c
    // 0x947074: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x947074: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x947078: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x947078: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x94707c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94707c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x947080: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x947080: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x947084: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x947084: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x947088: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x947088: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x94708c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x94708c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x947090: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x947090: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x947094: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x947094: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x947098: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x947098: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ setUpAddress(/* No info */) {
    // ** addr: 0x94709c, size: 0x698
    // 0x94709c: EnterFrame
    //     0x94709c: stp             fp, lr, [SP, #-0x10]!
    //     0x9470a0: mov             fp, SP
    // 0x9470a4: AllocStack(0x40)
    //     0x9470a4: sub             SP, SP, #0x40
    // 0x9470a8: SetupParameters(CheckoutAddressWidgetState this /* r1 => r1, fp-0x20 */, dynamic _ /* r2 => r2, fp-0x28 */)
    //     0x9470a8: stur            x1, [fp, #-0x20]
    //     0x9470ac: stur            x2, [fp, #-0x28]
    // 0x9470b0: CheckStackOverflow
    //     0x9470b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9470b4: cmp             SP, x16
    //     0x9470b8: b.ls            #0x947720
    // 0x9470bc: LoadField: r0 = r1->field_3f
    //     0x9470bc: ldur            w0, [x1, #0x3f]
    // 0x9470c0: DecompressPointer r0
    //     0x9470c0: add             x0, x0, HEAP, lsl #32
    // 0x9470c4: stur            x0, [fp, #-0x18]
    // 0x9470c8: cmp             w2, NULL
    // 0x9470cc: b.ne            #0x9470d8
    // 0x9470d0: r3 = Null
    //     0x9470d0: mov             x3, NULL
    // 0x9470d4: b               #0x9470e0
    // 0x9470d8: LoadField: r3 = r2->field_1b
    //     0x9470d8: ldur            w3, [x2, #0x1b]
    // 0x9470dc: DecompressPointer r3
    //     0x9470dc: add             x3, x3, HEAP, lsl #32
    // 0x9470e0: cmp             w3, NULL
    // 0x9470e4: b.ne            #0x9470ec
    // 0x9470e8: r3 = ""
    //     0x9470e8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9470ec: stur            x3, [fp, #-0x10]
    // 0x9470f0: cmp             w2, NULL
    // 0x9470f4: b.ne            #0x947100
    // 0x9470f8: r4 = Null
    //     0x9470f8: mov             x4, NULL
    // 0x9470fc: b               #0x947120
    // 0x947100: LoadField: r4 = r2->field_1b
    //     0x947100: ldur            w4, [x2, #0x1b]
    // 0x947104: DecompressPointer r4
    //     0x947104: add             x4, x4, HEAP, lsl #32
    // 0x947108: cmp             w4, NULL
    // 0x94710c: b.ne            #0x947118
    // 0x947110: r4 = Null
    //     0x947110: mov             x4, NULL
    // 0x947114: b               #0x947120
    // 0x947118: LoadField: r5 = r4->field_7
    //     0x947118: ldur            w5, [x4, #7]
    // 0x94711c: mov             x4, x5
    // 0x947120: cmp             w4, NULL
    // 0x947124: b.ne            #0x947130
    // 0x947128: r4 = 0
    //     0x947128: movz            x4, #0
    // 0x94712c: b               #0x947138
    // 0x947130: r5 = LoadInt32Instr(r4)
    //     0x947130: sbfx            x5, x4, #1, #0x1f
    // 0x947134: mov             x4, x5
    // 0x947138: stur            x4, [fp, #-8]
    // 0x94713c: r0 = TextSelection()
    //     0x94713c: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x947140: mov             x1, x0
    // 0x947144: ldur            x0, [fp, #-8]
    // 0x947148: stur            x1, [fp, #-0x30]
    // 0x94714c: ArrayStore: r1[0] = r0  ; List_8
    //     0x94714c: stur            x0, [x1, #0x17]
    // 0x947150: StoreField: r1->field_1f = r0
    //     0x947150: stur            x0, [x1, #0x1f]
    // 0x947154: r2 = Instance_TextAffinity
    //     0x947154: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x947158: StoreField: r1->field_27 = r2
    //     0x947158: stur            w2, [x1, #0x27]
    // 0x94715c: r3 = false
    //     0x94715c: add             x3, NULL, #0x30  ; false
    // 0x947160: StoreField: r1->field_2b = r3
    //     0x947160: stur            w3, [x1, #0x2b]
    // 0x947164: StoreField: r1->field_7 = r0
    //     0x947164: stur            x0, [x1, #7]
    // 0x947168: StoreField: r1->field_f = r0
    //     0x947168: stur            x0, [x1, #0xf]
    // 0x94716c: r0 = TextEditingValue()
    //     0x94716c: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x947170: mov             x1, x0
    // 0x947174: ldur            x0, [fp, #-0x10]
    // 0x947178: StoreField: r1->field_7 = r0
    //     0x947178: stur            w0, [x1, #7]
    // 0x94717c: ldur            x0, [fp, #-0x30]
    // 0x947180: StoreField: r1->field_b = r0
    //     0x947180: stur            w0, [x1, #0xb]
    // 0x947184: r0 = Instance_TextRange
    //     0x947184: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x947188: StoreField: r1->field_f = r0
    //     0x947188: stur            w0, [x1, #0xf]
    // 0x94718c: mov             x2, x1
    // 0x947190: ldur            x1, [fp, #-0x18]
    // 0x947194: r0 = value=()
    //     0x947194: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x947198: ldur            x1, [fp, #-0x20]
    // 0x94719c: LoadField: r0 = r1->field_3f
    //     0x94719c: ldur            w0, [x1, #0x3f]
    // 0x9471a0: DecompressPointer r0
    //     0x9471a0: add             x0, x0, HEAP, lsl #32
    // 0x9471a4: LoadField: r2 = r0->field_27
    //     0x9471a4: ldur            w2, [x0, #0x27]
    // 0x9471a8: DecompressPointer r2
    //     0x9471a8: add             x2, x2, HEAP, lsl #32
    // 0x9471ac: LoadField: r0 = r2->field_7
    //     0x9471ac: ldur            w0, [x2, #7]
    // 0x9471b0: DecompressPointer r0
    //     0x9471b0: add             x0, x0, HEAP, lsl #32
    // 0x9471b4: LoadField: r2 = r0->field_7
    //     0x9471b4: ldur            w2, [x0, #7]
    // 0x9471b8: cmp             w2, #0xc
    // 0x9471bc: b.ne            #0x9471f4
    // 0x9471c0: LoadField: r2 = r1->field_b
    //     0x9471c0: ldur            w2, [x1, #0xb]
    // 0x9471c4: DecompressPointer r2
    //     0x9471c4: add             x2, x2, HEAP, lsl #32
    // 0x9471c8: cmp             w2, NULL
    // 0x9471cc: b.eq            #0x947728
    // 0x9471d0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x9471d0: ldur            w3, [x2, #0x17]
    // 0x9471d4: DecompressPointer r3
    //     0x9471d4: add             x3, x3, HEAP, lsl #32
    // 0x9471d8: stp             x0, x3, [SP]
    // 0x9471dc: r4 = 0
    //     0x9471dc: movz            x4, #0
    // 0x9471e0: ldr             x0, [SP, #8]
    // 0x9471e4: r16 = UnlinkedCall_0x613b5c
    //     0x9471e4: add             x16, PP, #0x54, lsl #12  ; [pp+0x54278] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9471e8: add             x16, x16, #0x278
    // 0x9471ec: ldp             x5, lr, [x16]
    // 0x9471f0: blr             lr
    // 0x9471f4: ldur            x1, [fp, #-0x20]
    // 0x9471f8: ldur            x0, [fp, #-0x28]
    // 0x9471fc: LoadField: r2 = r1->field_3b
    //     0x9471fc: ldur            w2, [x1, #0x3b]
    // 0x947200: DecompressPointer r2
    //     0x947200: add             x2, x2, HEAP, lsl #32
    // 0x947204: stur            x2, [fp, #-0x18]
    // 0x947208: cmp             w0, NULL
    // 0x94720c: b.ne            #0x947218
    // 0x947210: r3 = Null
    //     0x947210: mov             x3, NULL
    // 0x947214: b               #0x947220
    // 0x947218: LoadField: r3 = r0->field_2b
    //     0x947218: ldur            w3, [x0, #0x2b]
    // 0x94721c: DecompressPointer r3
    //     0x94721c: add             x3, x3, HEAP, lsl #32
    // 0x947220: cmp             w3, NULL
    // 0x947224: b.ne            #0x94722c
    // 0x947228: r3 = ""
    //     0x947228: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x94722c: stur            x3, [fp, #-0x10]
    // 0x947230: cmp             w0, NULL
    // 0x947234: b.ne            #0x947240
    // 0x947238: r4 = Null
    //     0x947238: mov             x4, NULL
    // 0x94723c: b               #0x947260
    // 0x947240: LoadField: r4 = r0->field_2b
    //     0x947240: ldur            w4, [x0, #0x2b]
    // 0x947244: DecompressPointer r4
    //     0x947244: add             x4, x4, HEAP, lsl #32
    // 0x947248: cmp             w4, NULL
    // 0x94724c: b.ne            #0x947258
    // 0x947250: r4 = Null
    //     0x947250: mov             x4, NULL
    // 0x947254: b               #0x947260
    // 0x947258: LoadField: r5 = r4->field_7
    //     0x947258: ldur            w5, [x4, #7]
    // 0x94725c: mov             x4, x5
    // 0x947260: cmp             w4, NULL
    // 0x947264: b.ne            #0x947270
    // 0x947268: r4 = 0
    //     0x947268: movz            x4, #0
    // 0x94726c: b               #0x947278
    // 0x947270: r5 = LoadInt32Instr(r4)
    //     0x947270: sbfx            x5, x4, #1, #0x1f
    // 0x947274: mov             x4, x5
    // 0x947278: stur            x4, [fp, #-8]
    // 0x94727c: r0 = TextSelection()
    //     0x94727c: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x947280: mov             x1, x0
    // 0x947284: ldur            x0, [fp, #-8]
    // 0x947288: stur            x1, [fp, #-0x30]
    // 0x94728c: ArrayStore: r1[0] = r0  ; List_8
    //     0x94728c: stur            x0, [x1, #0x17]
    // 0x947290: StoreField: r1->field_1f = r0
    //     0x947290: stur            x0, [x1, #0x1f]
    // 0x947294: r2 = Instance_TextAffinity
    //     0x947294: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x947298: StoreField: r1->field_27 = r2
    //     0x947298: stur            w2, [x1, #0x27]
    // 0x94729c: r3 = false
    //     0x94729c: add             x3, NULL, #0x30  ; false
    // 0x9472a0: StoreField: r1->field_2b = r3
    //     0x9472a0: stur            w3, [x1, #0x2b]
    // 0x9472a4: StoreField: r1->field_7 = r0
    //     0x9472a4: stur            x0, [x1, #7]
    // 0x9472a8: StoreField: r1->field_f = r0
    //     0x9472a8: stur            x0, [x1, #0xf]
    // 0x9472ac: r0 = TextEditingValue()
    //     0x9472ac: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x9472b0: mov             x1, x0
    // 0x9472b4: ldur            x0, [fp, #-0x10]
    // 0x9472b8: StoreField: r1->field_7 = r0
    //     0x9472b8: stur            w0, [x1, #7]
    // 0x9472bc: ldur            x0, [fp, #-0x30]
    // 0x9472c0: StoreField: r1->field_b = r0
    //     0x9472c0: stur            w0, [x1, #0xb]
    // 0x9472c4: r0 = Instance_TextRange
    //     0x9472c4: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x9472c8: StoreField: r1->field_f = r0
    //     0x9472c8: stur            w0, [x1, #0xf]
    // 0x9472cc: mov             x2, x1
    // 0x9472d0: ldur            x1, [fp, #-0x18]
    // 0x9472d4: r0 = value=()
    //     0x9472d4: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x9472d8: ldur            x1, [fp, #-0x20]
    // 0x9472dc: LoadField: r0 = r1->field_47
    //     0x9472dc: ldur            w0, [x1, #0x47]
    // 0x9472e0: DecompressPointer r0
    //     0x9472e0: add             x0, x0, HEAP, lsl #32
    // 0x9472e4: ldur            x2, [fp, #-0x28]
    // 0x9472e8: stur            x0, [fp, #-0x18]
    // 0x9472ec: cmp             w2, NULL
    // 0x9472f0: b.ne            #0x9472fc
    // 0x9472f4: r3 = Null
    //     0x9472f4: mov             x3, NULL
    // 0x9472f8: b               #0x947304
    // 0x9472fc: LoadField: r3 = r2->field_13
    //     0x9472fc: ldur            w3, [x2, #0x13]
    // 0x947300: DecompressPointer r3
    //     0x947300: add             x3, x3, HEAP, lsl #32
    // 0x947304: cmp             w3, NULL
    // 0x947308: b.ne            #0x947310
    // 0x94730c: r3 = ""
    //     0x94730c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x947310: stur            x3, [fp, #-0x10]
    // 0x947314: cmp             w2, NULL
    // 0x947318: b.ne            #0x947324
    // 0x94731c: r4 = Null
    //     0x94731c: mov             x4, NULL
    // 0x947320: b               #0x947344
    // 0x947324: LoadField: r4 = r2->field_13
    //     0x947324: ldur            w4, [x2, #0x13]
    // 0x947328: DecompressPointer r4
    //     0x947328: add             x4, x4, HEAP, lsl #32
    // 0x94732c: cmp             w4, NULL
    // 0x947330: b.ne            #0x94733c
    // 0x947334: r4 = Null
    //     0x947334: mov             x4, NULL
    // 0x947338: b               #0x947344
    // 0x94733c: LoadField: r5 = r4->field_7
    //     0x94733c: ldur            w5, [x4, #7]
    // 0x947340: mov             x4, x5
    // 0x947344: cmp             w4, NULL
    // 0x947348: b.ne            #0x947354
    // 0x94734c: r4 = 0
    //     0x94734c: movz            x4, #0
    // 0x947350: b               #0x94735c
    // 0x947354: r5 = LoadInt32Instr(r4)
    //     0x947354: sbfx            x5, x4, #1, #0x1f
    // 0x947358: mov             x4, x5
    // 0x94735c: stur            x4, [fp, #-8]
    // 0x947360: r0 = TextSelection()
    //     0x947360: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x947364: mov             x1, x0
    // 0x947368: ldur            x0, [fp, #-8]
    // 0x94736c: stur            x1, [fp, #-0x30]
    // 0x947370: ArrayStore: r1[0] = r0  ; List_8
    //     0x947370: stur            x0, [x1, #0x17]
    // 0x947374: StoreField: r1->field_1f = r0
    //     0x947374: stur            x0, [x1, #0x1f]
    // 0x947378: r2 = Instance_TextAffinity
    //     0x947378: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x94737c: StoreField: r1->field_27 = r2
    //     0x94737c: stur            w2, [x1, #0x27]
    // 0x947380: r3 = false
    //     0x947380: add             x3, NULL, #0x30  ; false
    // 0x947384: StoreField: r1->field_2b = r3
    //     0x947384: stur            w3, [x1, #0x2b]
    // 0x947388: StoreField: r1->field_7 = r0
    //     0x947388: stur            x0, [x1, #7]
    // 0x94738c: StoreField: r1->field_f = r0
    //     0x94738c: stur            x0, [x1, #0xf]
    // 0x947390: r0 = TextEditingValue()
    //     0x947390: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x947394: mov             x1, x0
    // 0x947398: ldur            x0, [fp, #-0x10]
    // 0x94739c: StoreField: r1->field_7 = r0
    //     0x94739c: stur            w0, [x1, #7]
    // 0x9473a0: ldur            x0, [fp, #-0x30]
    // 0x9473a4: StoreField: r1->field_b = r0
    //     0x9473a4: stur            w0, [x1, #0xb]
    // 0x9473a8: r0 = Instance_TextRange
    //     0x9473a8: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x9473ac: StoreField: r1->field_f = r0
    //     0x9473ac: stur            w0, [x1, #0xf]
    // 0x9473b0: mov             x2, x1
    // 0x9473b4: ldur            x1, [fp, #-0x18]
    // 0x9473b8: r0 = value=()
    //     0x9473b8: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x9473bc: ldur            x1, [fp, #-0x20]
    // 0x9473c0: LoadField: r0 = r1->field_37
    //     0x9473c0: ldur            w0, [x1, #0x37]
    // 0x9473c4: DecompressPointer r0
    //     0x9473c4: add             x0, x0, HEAP, lsl #32
    // 0x9473c8: ldur            x2, [fp, #-0x28]
    // 0x9473cc: stur            x0, [fp, #-0x18]
    // 0x9473d0: cmp             w2, NULL
    // 0x9473d4: b.ne            #0x9473e0
    // 0x9473d8: r3 = Null
    //     0x9473d8: mov             x3, NULL
    // 0x9473dc: b               #0x9473e8
    // 0x9473e0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x9473e0: ldur            w3, [x2, #0x17]
    // 0x9473e4: DecompressPointer r3
    //     0x9473e4: add             x3, x3, HEAP, lsl #32
    // 0x9473e8: cmp             w3, NULL
    // 0x9473ec: b.ne            #0x9473f4
    // 0x9473f0: r3 = ""
    //     0x9473f0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9473f4: stur            x3, [fp, #-0x10]
    // 0x9473f8: cmp             w2, NULL
    // 0x9473fc: b.ne            #0x947408
    // 0x947400: r4 = Null
    //     0x947400: mov             x4, NULL
    // 0x947404: b               #0x947428
    // 0x947408: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x947408: ldur            w4, [x2, #0x17]
    // 0x94740c: DecompressPointer r4
    //     0x94740c: add             x4, x4, HEAP, lsl #32
    // 0x947410: cmp             w4, NULL
    // 0x947414: b.ne            #0x947420
    // 0x947418: r4 = Null
    //     0x947418: mov             x4, NULL
    // 0x94741c: b               #0x947428
    // 0x947420: LoadField: r5 = r4->field_7
    //     0x947420: ldur            w5, [x4, #7]
    // 0x947424: mov             x4, x5
    // 0x947428: cmp             w4, NULL
    // 0x94742c: b.ne            #0x947438
    // 0x947430: r4 = 0
    //     0x947430: movz            x4, #0
    // 0x947434: b               #0x947440
    // 0x947438: r5 = LoadInt32Instr(r4)
    //     0x947438: sbfx            x5, x4, #1, #0x1f
    // 0x94743c: mov             x4, x5
    // 0x947440: stur            x4, [fp, #-8]
    // 0x947444: r0 = TextSelection()
    //     0x947444: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x947448: mov             x1, x0
    // 0x94744c: ldur            x0, [fp, #-8]
    // 0x947450: stur            x1, [fp, #-0x30]
    // 0x947454: ArrayStore: r1[0] = r0  ; List_8
    //     0x947454: stur            x0, [x1, #0x17]
    // 0x947458: StoreField: r1->field_1f = r0
    //     0x947458: stur            x0, [x1, #0x1f]
    // 0x94745c: r2 = Instance_TextAffinity
    //     0x94745c: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x947460: StoreField: r1->field_27 = r2
    //     0x947460: stur            w2, [x1, #0x27]
    // 0x947464: r3 = false
    //     0x947464: add             x3, NULL, #0x30  ; false
    // 0x947468: StoreField: r1->field_2b = r3
    //     0x947468: stur            w3, [x1, #0x2b]
    // 0x94746c: StoreField: r1->field_7 = r0
    //     0x94746c: stur            x0, [x1, #7]
    // 0x947470: StoreField: r1->field_f = r0
    //     0x947470: stur            x0, [x1, #0xf]
    // 0x947474: r0 = TextEditingValue()
    //     0x947474: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x947478: mov             x1, x0
    // 0x94747c: ldur            x0, [fp, #-0x10]
    // 0x947480: StoreField: r1->field_7 = r0
    //     0x947480: stur            w0, [x1, #7]
    // 0x947484: ldur            x0, [fp, #-0x30]
    // 0x947488: StoreField: r1->field_b = r0
    //     0x947488: stur            w0, [x1, #0xb]
    // 0x94748c: r0 = Instance_TextRange
    //     0x94748c: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x947490: StoreField: r1->field_f = r0
    //     0x947490: stur            w0, [x1, #0xf]
    // 0x947494: mov             x2, x1
    // 0x947498: ldur            x1, [fp, #-0x18]
    // 0x94749c: r0 = value=()
    //     0x94749c: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x9474a0: ldur            x1, [fp, #-0x20]
    // 0x9474a4: LoadField: r0 = r1->field_43
    //     0x9474a4: ldur            w0, [x1, #0x43]
    // 0x9474a8: DecompressPointer r0
    //     0x9474a8: add             x0, x0, HEAP, lsl #32
    // 0x9474ac: ldur            x2, [fp, #-0x28]
    // 0x9474b0: stur            x0, [fp, #-0x18]
    // 0x9474b4: cmp             w2, NULL
    // 0x9474b8: b.ne            #0x9474c4
    // 0x9474bc: r3 = Null
    //     0x9474bc: mov             x3, NULL
    // 0x9474c0: b               #0x9474cc
    // 0x9474c4: LoadField: r3 = r2->field_2f
    //     0x9474c4: ldur            w3, [x2, #0x2f]
    // 0x9474c8: DecompressPointer r3
    //     0x9474c8: add             x3, x3, HEAP, lsl #32
    // 0x9474cc: cmp             w3, NULL
    // 0x9474d0: b.ne            #0x9474d8
    // 0x9474d4: r3 = ""
    //     0x9474d4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9474d8: stur            x3, [fp, #-0x10]
    // 0x9474dc: cmp             w2, NULL
    // 0x9474e0: b.ne            #0x9474ec
    // 0x9474e4: r2 = Null
    //     0x9474e4: mov             x2, NULL
    // 0x9474e8: b               #0x947508
    // 0x9474ec: LoadField: r4 = r2->field_2f
    //     0x9474ec: ldur            w4, [x2, #0x2f]
    // 0x9474f0: DecompressPointer r4
    //     0x9474f0: add             x4, x4, HEAP, lsl #32
    // 0x9474f4: cmp             w4, NULL
    // 0x9474f8: b.ne            #0x947504
    // 0x9474fc: r2 = Null
    //     0x9474fc: mov             x2, NULL
    // 0x947500: b               #0x947508
    // 0x947504: LoadField: r2 = r4->field_7
    //     0x947504: ldur            w2, [x4, #7]
    // 0x947508: cmp             w2, NULL
    // 0x94750c: b.ne            #0x947518
    // 0x947510: r2 = 0
    //     0x947510: movz            x2, #0
    // 0x947514: b               #0x947520
    // 0x947518: r4 = LoadInt32Instr(r2)
    //     0x947518: sbfx            x4, x2, #1, #0x1f
    // 0x94751c: mov             x2, x4
    // 0x947520: stur            x2, [fp, #-8]
    // 0x947524: r0 = TextSelection()
    //     0x947524: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x947528: mov             x1, x0
    // 0x94752c: ldur            x0, [fp, #-8]
    // 0x947530: stur            x1, [fp, #-0x28]
    // 0x947534: ArrayStore: r1[0] = r0  ; List_8
    //     0x947534: stur            x0, [x1, #0x17]
    // 0x947538: StoreField: r1->field_1f = r0
    //     0x947538: stur            x0, [x1, #0x1f]
    // 0x94753c: r2 = Instance_TextAffinity
    //     0x94753c: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x947540: StoreField: r1->field_27 = r2
    //     0x947540: stur            w2, [x1, #0x27]
    // 0x947544: r3 = false
    //     0x947544: add             x3, NULL, #0x30  ; false
    // 0x947548: StoreField: r1->field_2b = r3
    //     0x947548: stur            w3, [x1, #0x2b]
    // 0x94754c: StoreField: r1->field_7 = r0
    //     0x94754c: stur            x0, [x1, #7]
    // 0x947550: StoreField: r1->field_f = r0
    //     0x947550: stur            x0, [x1, #0xf]
    // 0x947554: r0 = TextEditingValue()
    //     0x947554: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x947558: mov             x1, x0
    // 0x94755c: ldur            x0, [fp, #-0x10]
    // 0x947560: StoreField: r1->field_7 = r0
    //     0x947560: stur            w0, [x1, #7]
    // 0x947564: ldur            x0, [fp, #-0x28]
    // 0x947568: StoreField: r1->field_b = r0
    //     0x947568: stur            w0, [x1, #0xb]
    // 0x94756c: r0 = Instance_TextRange
    //     0x94756c: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x947570: StoreField: r1->field_f = r0
    //     0x947570: stur            w0, [x1, #0xf]
    // 0x947574: mov             x2, x1
    // 0x947578: ldur            x1, [fp, #-0x18]
    // 0x94757c: r0 = value=()
    //     0x94757c: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x947580: ldur            x0, [fp, #-0x20]
    // 0x947584: LoadField: r3 = r0->field_33
    //     0x947584: ldur            w3, [x0, #0x33]
    // 0x947588: DecompressPointer r3
    //     0x947588: add             x3, x3, HEAP, lsl #32
    // 0x94758c: stur            x3, [fp, #-0x28]
    // 0x947590: LoadField: r1 = r0->field_b
    //     0x947590: ldur            w1, [x0, #0xb]
    // 0x947594: DecompressPointer r1
    //     0x947594: add             x1, x1, HEAP, lsl #32
    // 0x947598: cmp             w1, NULL
    // 0x94759c: b.eq            #0x94772c
    // 0x9475a0: LoadField: r4 = r1->field_1b
    //     0x9475a0: ldur            w4, [x1, #0x1b]
    // 0x9475a4: DecompressPointer r4
    //     0x9475a4: add             x4, x4, HEAP, lsl #32
    // 0x9475a8: stur            x4, [fp, #-0x18]
    // 0x9475ac: LoadField: r1 = r4->field_b
    //     0x9475ac: ldur            w1, [x4, #0xb]
    // 0x9475b0: DecompressPointer r1
    //     0x9475b0: add             x1, x1, HEAP, lsl #32
    // 0x9475b4: cmp             w1, NULL
    // 0x9475b8: b.ne            #0x9475c4
    // 0x9475bc: r5 = ""
    //     0x9475bc: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9475c0: b               #0x9475c8
    // 0x9475c4: mov             x5, x1
    // 0x9475c8: stur            x5, [fp, #-0x10]
    // 0x9475cc: r1 = Null
    //     0x9475cc: mov             x1, NULL
    // 0x9475d0: r2 = 6
    //     0x9475d0: movz            x2, #0x6
    // 0x9475d4: r0 = AllocateArray()
    //     0x9475d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9475d8: mov             x1, x0
    // 0x9475dc: ldur            x0, [fp, #-0x10]
    // 0x9475e0: StoreField: r1->field_f = r0
    //     0x9475e0: stur            w0, [x1, #0xf]
    // 0x9475e4: r16 = " "
    //     0x9475e4: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x9475e8: StoreField: r1->field_13 = r16
    //     0x9475e8: stur            w16, [x1, #0x13]
    // 0x9475ec: ldur            x0, [fp, #-0x18]
    // 0x9475f0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x9475f0: ldur            w2, [x0, #0x17]
    // 0x9475f4: DecompressPointer r2
    //     0x9475f4: add             x2, x2, HEAP, lsl #32
    // 0x9475f8: cmp             w2, NULL
    // 0x9475fc: b.ne            #0x947604
    // 0x947600: r2 = ""
    //     0x947600: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x947604: ldur            x0, [fp, #-0x20]
    // 0x947608: ArrayStore: r1[0] = r2  ; List_4
    //     0x947608: stur            w2, [x1, #0x17]
    // 0x94760c: str             x1, [SP]
    // 0x947610: r0 = _interpolate()
    //     0x947610: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x947614: mov             x1, x0
    // 0x947618: r0 = trim()
    //     0x947618: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0x94761c: ldur            x1, [fp, #-0x20]
    // 0x947620: stur            x0, [fp, #-0x10]
    // 0x947624: LoadField: r2 = r1->field_b
    //     0x947624: ldur            w2, [x1, #0xb]
    // 0x947628: DecompressPointer r2
    //     0x947628: add             x2, x2, HEAP, lsl #32
    // 0x94762c: cmp             w2, NULL
    // 0x947630: b.eq            #0x947730
    // 0x947634: LoadField: r3 = r2->field_1b
    //     0x947634: ldur            w3, [x2, #0x1b]
    // 0x947638: DecompressPointer r3
    //     0x947638: add             x3, x3, HEAP, lsl #32
    // 0x94763c: LoadField: r2 = r3->field_b
    //     0x94763c: ldur            w2, [x3, #0xb]
    // 0x947640: DecompressPointer r2
    //     0x947640: add             x2, x2, HEAP, lsl #32
    // 0x947644: cmp             w2, NULL
    // 0x947648: b.ne            #0x947654
    // 0x94764c: r2 = Null
    //     0x94764c: mov             x2, NULL
    // 0x947650: b               #0x94765c
    // 0x947654: LoadField: r4 = r2->field_7
    //     0x947654: ldur            w4, [x2, #7]
    // 0x947658: mov             x2, x4
    // 0x94765c: cmp             w2, NULL
    // 0x947660: b.ne            #0x9476a0
    // 0x947664: ArrayLoad: r2 = r3[0]  ; List_4
    //     0x947664: ldur            w2, [x3, #0x17]
    // 0x947668: DecompressPointer r2
    //     0x947668: add             x2, x2, HEAP, lsl #32
    // 0x94766c: cmp             w2, NULL
    // 0x947670: b.ne            #0x94767c
    // 0x947674: r2 = Null
    //     0x947674: mov             x2, NULL
    // 0x947678: b               #0x947684
    // 0x94767c: LoadField: r3 = r2->field_7
    //     0x94767c: ldur            w3, [x2, #7]
    // 0x947680: mov             x2, x3
    // 0x947684: cmp             w2, NULL
    // 0x947688: b.ne            #0x947694
    // 0x94768c: r2 = 0
    //     0x94768c: movz            x2, #0
    // 0x947690: b               #0x9476a8
    // 0x947694: r3 = LoadInt32Instr(r2)
    //     0x947694: sbfx            x3, x2, #1, #0x1f
    // 0x947698: mov             x2, x3
    // 0x94769c: b               #0x9476a8
    // 0x9476a0: r3 = LoadInt32Instr(r2)
    //     0x9476a0: sbfx            x3, x2, #1, #0x1f
    // 0x9476a4: mov             x2, x3
    // 0x9476a8: stur            x2, [fp, #-8]
    // 0x9476ac: r0 = TextSelection()
    //     0x9476ac: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x9476b0: mov             x1, x0
    // 0x9476b4: ldur            x0, [fp, #-8]
    // 0x9476b8: stur            x1, [fp, #-0x18]
    // 0x9476bc: ArrayStore: r1[0] = r0  ; List_8
    //     0x9476bc: stur            x0, [x1, #0x17]
    // 0x9476c0: StoreField: r1->field_1f = r0
    //     0x9476c0: stur            x0, [x1, #0x1f]
    // 0x9476c4: r2 = Instance_TextAffinity
    //     0x9476c4: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x9476c8: StoreField: r1->field_27 = r2
    //     0x9476c8: stur            w2, [x1, #0x27]
    // 0x9476cc: r2 = false
    //     0x9476cc: add             x2, NULL, #0x30  ; false
    // 0x9476d0: StoreField: r1->field_2b = r2
    //     0x9476d0: stur            w2, [x1, #0x2b]
    // 0x9476d4: StoreField: r1->field_7 = r0
    //     0x9476d4: stur            x0, [x1, #7]
    // 0x9476d8: StoreField: r1->field_f = r0
    //     0x9476d8: stur            x0, [x1, #0xf]
    // 0x9476dc: r0 = TextEditingValue()
    //     0x9476dc: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x9476e0: mov             x1, x0
    // 0x9476e4: ldur            x0, [fp, #-0x10]
    // 0x9476e8: StoreField: r1->field_7 = r0
    //     0x9476e8: stur            w0, [x1, #7]
    // 0x9476ec: ldur            x0, [fp, #-0x18]
    // 0x9476f0: StoreField: r1->field_b = r0
    //     0x9476f0: stur            w0, [x1, #0xb]
    // 0x9476f4: r0 = Instance_TextRange
    //     0x9476f4: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x9476f8: StoreField: r1->field_f = r0
    //     0x9476f8: stur            w0, [x1, #0xf]
    // 0x9476fc: mov             x2, x1
    // 0x947700: ldur            x1, [fp, #-0x28]
    // 0x947704: r0 = value=()
    //     0x947704: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x947708: ldur            x1, [fp, #-0x20]
    // 0x94770c: r0 = _validateAllFields()
    //     0x94770c: bl              #0x947734  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields
    // 0x947710: r0 = Null
    //     0x947710: mov             x0, NULL
    // 0x947714: LeaveFrame
    //     0x947714: mov             SP, fp
    //     0x947718: ldp             fp, lr, [SP], #0x10
    // 0x94771c: ret
    //     0x94771c: ret             
    // 0x947720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x947720: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x947724: b               #0x9470bc
    // 0x947728: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x947728: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94772c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94772c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x947730: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x947730: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _validateAllFields(/* No info */) {
    // ** addr: 0x947734, size: 0x308
    // 0x947734: EnterFrame
    //     0x947734: stp             fp, lr, [SP, #-0x10]!
    //     0x947738: mov             fp, SP
    // 0x94773c: AllocStack(0x38)
    //     0x94773c: sub             SP, SP, #0x38
    // 0x947740: SetupParameters(CheckoutAddressWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x947740: stur            x1, [fp, #-8]
    // 0x947744: CheckStackOverflow
    //     0x947744: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x947748: cmp             SP, x16
    //     0x94774c: b.ls            #0x947a2c
    // 0x947750: r1 = 2
    //     0x947750: movz            x1, #0x2
    // 0x947754: r0 = AllocateContext()
    //     0x947754: bl              #0x16f6108  ; AllocateContextStub
    // 0x947758: mov             x3, x0
    // 0x94775c: ldur            x0, [fp, #-8]
    // 0x947760: stur            x3, [fp, #-0x38]
    // 0x947764: StoreField: r3->field_f = r0
    //     0x947764: stur            w0, [x3, #0xf]
    // 0x947768: LoadField: r1 = r0->field_33
    //     0x947768: ldur            w1, [x0, #0x33]
    // 0x94776c: DecompressPointer r1
    //     0x94776c: add             x1, x1, HEAP, lsl #32
    // 0x947770: LoadField: r2 = r1->field_27
    //     0x947770: ldur            w2, [x1, #0x27]
    // 0x947774: DecompressPointer r2
    //     0x947774: add             x2, x2, HEAP, lsl #32
    // 0x947778: LoadField: r1 = r2->field_7
    //     0x947778: ldur            w1, [x2, #7]
    // 0x94777c: DecompressPointer r1
    //     0x94777c: add             x1, x1, HEAP, lsl #32
    // 0x947780: LoadField: r2 = r1->field_7
    //     0x947780: ldur            w2, [x1, #7]
    // 0x947784: r4 = LoadInt32Instr(r2)
    //     0x947784: sbfx            x4, x2, #1, #0x1f
    // 0x947788: stur            x4, [fp, #-0x30]
    // 0x94778c: LoadField: r1 = r0->field_3f
    //     0x94778c: ldur            w1, [x0, #0x3f]
    // 0x947790: DecompressPointer r1
    //     0x947790: add             x1, x1, HEAP, lsl #32
    // 0x947794: LoadField: r2 = r1->field_27
    //     0x947794: ldur            w2, [x1, #0x27]
    // 0x947798: DecompressPointer r2
    //     0x947798: add             x2, x2, HEAP, lsl #32
    // 0x94779c: LoadField: r1 = r2->field_7
    //     0x94779c: ldur            w1, [x2, #7]
    // 0x9477a0: DecompressPointer r1
    //     0x9477a0: add             x1, x1, HEAP, lsl #32
    // 0x9477a4: LoadField: r2 = r1->field_7
    //     0x9477a4: ldur            w2, [x1, #7]
    // 0x9477a8: cmp             w2, #0xc
    // 0x9477ac: b.ne            #0x947824
    // 0x9477b0: LoadField: r1 = r0->field_b
    //     0x9477b0: ldur            w1, [x0, #0xb]
    // 0x9477b4: DecompressPointer r1
    //     0x9477b4: add             x1, x1, HEAP, lsl #32
    // 0x9477b8: cmp             w1, NULL
    // 0x9477bc: b.eq            #0x947a34
    // 0x9477c0: LoadField: r2 = r1->field_13
    //     0x9477c0: ldur            w2, [x1, #0x13]
    // 0x9477c4: DecompressPointer r2
    //     0x9477c4: add             x2, x2, HEAP, lsl #32
    // 0x9477c8: LoadField: r1 = r2->field_b
    //     0x9477c8: ldur            w1, [x2, #0xb]
    // 0x9477cc: DecompressPointer r1
    //     0x9477cc: add             x1, x1, HEAP, lsl #32
    // 0x9477d0: cmp             w1, NULL
    // 0x9477d4: b.ne            #0x9477e0
    // 0x9477d8: r1 = Null
    //     0x9477d8: mov             x1, NULL
    // 0x9477dc: b               #0x947810
    // 0x9477e0: LoadField: r2 = r1->field_13
    //     0x9477e0: ldur            w2, [x1, #0x13]
    // 0x9477e4: DecompressPointer r2
    //     0x9477e4: add             x2, x2, HEAP, lsl #32
    // 0x9477e8: cmp             w2, NULL
    // 0x9477ec: b.ne            #0x9477f8
    // 0x9477f0: r1 = Null
    //     0x9477f0: mov             x1, NULL
    // 0x9477f4: b               #0x947810
    // 0x9477f8: LoadField: r1 = r2->field_7
    //     0x9477f8: ldur            w1, [x2, #7]
    // 0x9477fc: cbnz            w1, #0x947808
    // 0x947800: r2 = false
    //     0x947800: add             x2, NULL, #0x30  ; false
    // 0x947804: b               #0x94780c
    // 0x947808: r2 = true
    //     0x947808: add             x2, NULL, #0x20  ; true
    // 0x94780c: mov             x1, x2
    // 0x947810: cmp             w1, NULL
    // 0x947814: b.ne            #0x94781c
    // 0x947818: r1 = false
    //     0x947818: add             x1, NULL, #0x30  ; false
    // 0x94781c: mov             x5, x1
    // 0x947820: b               #0x947828
    // 0x947824: r5 = false
    //     0x947824: add             x5, NULL, #0x30  ; false
    // 0x947828: stur            x5, [fp, #-0x28]
    // 0x94782c: LoadField: r1 = r0->field_3b
    //     0x94782c: ldur            w1, [x0, #0x3b]
    // 0x947830: DecompressPointer r1
    //     0x947830: add             x1, x1, HEAP, lsl #32
    // 0x947834: LoadField: r2 = r1->field_27
    //     0x947834: ldur            w2, [x1, #0x27]
    // 0x947838: DecompressPointer r2
    //     0x947838: add             x2, x2, HEAP, lsl #32
    // 0x94783c: LoadField: r1 = r2->field_7
    //     0x94783c: ldur            w1, [x2, #7]
    // 0x947840: DecompressPointer r1
    //     0x947840: add             x1, x1, HEAP, lsl #32
    // 0x947844: LoadField: r6 = r1->field_7
    //     0x947844: ldur            w6, [x1, #7]
    // 0x947848: stur            x6, [fp, #-0x20]
    // 0x94784c: LoadField: r1 = r0->field_47
    //     0x94784c: ldur            w1, [x0, #0x47]
    // 0x947850: DecompressPointer r1
    //     0x947850: add             x1, x1, HEAP, lsl #32
    // 0x947854: LoadField: r2 = r1->field_27
    //     0x947854: ldur            w2, [x1, #0x27]
    // 0x947858: DecompressPointer r2
    //     0x947858: add             x2, x2, HEAP, lsl #32
    // 0x94785c: LoadField: r1 = r2->field_7
    //     0x94785c: ldur            w1, [x2, #7]
    // 0x947860: DecompressPointer r1
    //     0x947860: add             x1, x1, HEAP, lsl #32
    // 0x947864: LoadField: r2 = r1->field_7
    //     0x947864: ldur            w2, [x1, #7]
    // 0x947868: r7 = LoadInt32Instr(r2)
    //     0x947868: sbfx            x7, x2, #1, #0x1f
    // 0x94786c: stur            x7, [fp, #-0x18]
    // 0x947870: LoadField: r1 = r0->field_37
    //     0x947870: ldur            w1, [x0, #0x37]
    // 0x947874: DecompressPointer r1
    //     0x947874: add             x1, x1, HEAP, lsl #32
    // 0x947878: LoadField: r2 = r1->field_27
    //     0x947878: ldur            w2, [x1, #0x27]
    // 0x94787c: DecompressPointer r2
    //     0x94787c: add             x2, x2, HEAP, lsl #32
    // 0x947880: LoadField: r1 = r2->field_7
    //     0x947880: ldur            w1, [x2, #7]
    // 0x947884: DecompressPointer r1
    //     0x947884: add             x1, x1, HEAP, lsl #32
    // 0x947888: LoadField: r2 = r1->field_7
    //     0x947888: ldur            w2, [x1, #7]
    // 0x94788c: cbnz            w2, #0x947898
    // 0x947890: r8 = true
    //     0x947890: add             x8, NULL, #0x20  ; true
    // 0x947894: b               #0x9478b0
    // 0x947898: r1 = LoadInt32Instr(r2)
    //     0x947898: sbfx            x1, x2, #1, #0x1f
    // 0x94789c: cmp             x1, #5
    // 0x9478a0: r16 = true
    //     0x9478a0: add             x16, NULL, #0x20  ; true
    // 0x9478a4: r17 = false
    //     0x9478a4: add             x17, NULL, #0x30  ; false
    // 0x9478a8: csel            x2, x16, x17, ge
    // 0x9478ac: mov             x8, x2
    // 0x9478b0: stur            x8, [fp, #-0x10]
    // 0x9478b4: LoadField: r1 = r0->field_43
    //     0x9478b4: ldur            w1, [x0, #0x43]
    // 0x9478b8: DecompressPointer r1
    //     0x9478b8: add             x1, x1, HEAP, lsl #32
    // 0x9478bc: LoadField: r2 = r1->field_27
    //     0x9478bc: ldur            w2, [x1, #0x27]
    // 0x9478c0: DecompressPointer r2
    //     0x9478c0: add             x2, x2, HEAP, lsl #32
    // 0x9478c4: LoadField: r1 = r2->field_7
    //     0x9478c4: ldur            w1, [x2, #7]
    // 0x9478c8: DecompressPointer r1
    //     0x9478c8: add             x1, x1, HEAP, lsl #32
    // 0x9478cc: LoadField: r2 = r1->field_7
    //     0x9478cc: ldur            w2, [x1, #7]
    // 0x9478d0: cbnz            w2, #0x9478e0
    // 0x9478d4: mov             x0, x4
    // 0x9478d8: r1 = true
    //     0x9478d8: add             x1, NULL, #0x20  ; true
    // 0x9478dc: b               #0x9478f4
    // 0x9478e0: mov             x2, x1
    // 0x9478e4: mov             x1, x0
    // 0x9478e8: r0 = _isValidPhoneNumber()
    //     0x9478e8: bl              #0x801170  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_isValidPhoneNumber
    // 0x9478ec: mov             x1, x0
    // 0x9478f0: ldur            x0, [fp, #-0x30]
    // 0x9478f4: cmp             x0, #1
    // 0x9478f8: b.le            #0x947928
    // 0x9478fc: ldur            x0, [fp, #-0x28]
    // 0x947900: tbnz            w0, #4, #0x947928
    // 0x947904: ldur            x0, [fp, #-0x20]
    // 0x947908: cbz             w0, #0x947928
    // 0x94790c: ldur            x0, [fp, #-0x18]
    // 0x947910: cmp             x0, #0x14
    // 0x947914: b.lt            #0x947928
    // 0x947918: ldur            x0, [fp, #-0x10]
    // 0x94791c: tbnz            w0, #4, #0x947928
    // 0x947920: mov             x0, x1
    // 0x947924: b               #0x94792c
    // 0x947928: r0 = false
    //     0x947928: add             x0, NULL, #0x30  ; false
    // 0x94792c: ldur            x2, [fp, #-0x38]
    // 0x947930: StoreField: r2->field_13 = r0
    //     0x947930: stur            w0, [x2, #0x13]
    // 0x947934: r0 = LoadStaticField(0x878)
    //     0x947934: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x947938: ldr             x0, [x0, #0x10f0]
    // 0x94793c: cmp             w0, NULL
    // 0x947940: b.eq            #0x947a38
    // 0x947944: LoadField: r3 = r0->field_53
    //     0x947944: ldur            w3, [x0, #0x53]
    // 0x947948: DecompressPointer r3
    //     0x947948: add             x3, x3, HEAP, lsl #32
    // 0x94794c: stur            x3, [fp, #-0x20]
    // 0x947950: LoadField: r0 = r3->field_7
    //     0x947950: ldur            w0, [x3, #7]
    // 0x947954: DecompressPointer r0
    //     0x947954: add             x0, x0, HEAP, lsl #32
    // 0x947958: stur            x0, [fp, #-0x10]
    // 0x94795c: r1 = Function '<anonymous closure>':.
    //     0x94795c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54170] AnonymousClosure: (0x947a3c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields (0x947734)
    //     0x947960: ldr             x1, [x1, #0x170]
    // 0x947964: r0 = AllocateClosure()
    //     0x947964: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x947968: ldur            x2, [fp, #-0x10]
    // 0x94796c: mov             x3, x0
    // 0x947970: r1 = Null
    //     0x947970: mov             x1, NULL
    // 0x947974: stur            x3, [fp, #-0x10]
    // 0x947978: cmp             w2, NULL
    // 0x94797c: b.eq            #0x94799c
    // 0x947980: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x947980: ldur            w4, [x2, #0x17]
    // 0x947984: DecompressPointer r4
    //     0x947984: add             x4, x4, HEAP, lsl #32
    // 0x947988: r8 = X0
    //     0x947988: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x94798c: LoadField: r9 = r4->field_7
    //     0x94798c: ldur            x9, [x4, #7]
    // 0x947990: r3 = Null
    //     0x947990: add             x3, PP, #0x54, lsl #12  ; [pp+0x54178] Null
    //     0x947994: ldr             x3, [x3, #0x178]
    // 0x947998: blr             x9
    // 0x94799c: ldur            x0, [fp, #-0x20]
    // 0x9479a0: LoadField: r1 = r0->field_b
    //     0x9479a0: ldur            w1, [x0, #0xb]
    // 0x9479a4: LoadField: r2 = r0->field_f
    //     0x9479a4: ldur            w2, [x0, #0xf]
    // 0x9479a8: DecompressPointer r2
    //     0x9479a8: add             x2, x2, HEAP, lsl #32
    // 0x9479ac: LoadField: r3 = r2->field_b
    //     0x9479ac: ldur            w3, [x2, #0xb]
    // 0x9479b0: r2 = LoadInt32Instr(r1)
    //     0x9479b0: sbfx            x2, x1, #1, #0x1f
    // 0x9479b4: stur            x2, [fp, #-0x18]
    // 0x9479b8: r1 = LoadInt32Instr(r3)
    //     0x9479b8: sbfx            x1, x3, #1, #0x1f
    // 0x9479bc: cmp             x2, x1
    // 0x9479c0: b.ne            #0x9479cc
    // 0x9479c4: mov             x1, x0
    // 0x9479c8: r0 = _growToNextCapacity()
    //     0x9479c8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9479cc: ldur            x0, [fp, #-0x20]
    // 0x9479d0: ldur            x2, [fp, #-0x18]
    // 0x9479d4: add             x1, x2, #1
    // 0x9479d8: lsl             x3, x1, #1
    // 0x9479dc: StoreField: r0->field_b = r3
    //     0x9479dc: stur            w3, [x0, #0xb]
    // 0x9479e0: LoadField: r1 = r0->field_f
    //     0x9479e0: ldur            w1, [x0, #0xf]
    // 0x9479e4: DecompressPointer r1
    //     0x9479e4: add             x1, x1, HEAP, lsl #32
    // 0x9479e8: ldur            x0, [fp, #-0x10]
    // 0x9479ec: ArrayStore: r1[r2] = r0  ; List_4
    //     0x9479ec: add             x25, x1, x2, lsl #2
    //     0x9479f0: add             x25, x25, #0xf
    //     0x9479f4: str             w0, [x25]
    //     0x9479f8: tbz             w0, #0, #0x947a14
    //     0x9479fc: ldurb           w16, [x1, #-1]
    //     0x947a00: ldurb           w17, [x0, #-1]
    //     0x947a04: and             x16, x17, x16, lsr #2
    //     0x947a08: tst             x16, HEAP, lsr #32
    //     0x947a0c: b.eq            #0x947a14
    //     0x947a10: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x947a14: ldur            x1, [fp, #-8]
    // 0x947a18: r0 = _checkFirstTimeInput()
    //     0x947a18: bl              #0x905210  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_checkFirstTimeInput
    // 0x947a1c: r0 = Null
    //     0x947a1c: mov             x0, NULL
    // 0x947a20: LeaveFrame
    //     0x947a20: mov             SP, fp
    //     0x947a24: ldp             fp, lr, [SP], #0x10
    // 0x947a28: ret
    //     0x947a28: ret             
    // 0x947a2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x947a2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x947a30: b               #0x947750
    // 0x947a34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x947a34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x947a38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x947a38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x947a3c, size: 0x118
    // 0x947a3c: EnterFrame
    //     0x947a3c: stp             fp, lr, [SP, #-0x10]!
    //     0x947a40: mov             fp, SP
    // 0x947a44: AllocStack(0x40)
    //     0x947a44: sub             SP, SP, #0x40
    // 0x947a48: SetupParameters()
    //     0x947a48: ldr             x0, [fp, #0x18]
    //     0x947a4c: ldur            w1, [x0, #0x17]
    //     0x947a50: add             x1, x1, HEAP, lsl #32
    // 0x947a54: CheckStackOverflow
    //     0x947a54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x947a58: cmp             SP, x16
    //     0x947a5c: b.ls            #0x947b48
    // 0x947a60: LoadField: r0 = r1->field_f
    //     0x947a60: ldur            w0, [x1, #0xf]
    // 0x947a64: DecompressPointer r0
    //     0x947a64: add             x0, x0, HEAP, lsl #32
    // 0x947a68: LoadField: r2 = r0->field_b
    //     0x947a68: ldur            w2, [x0, #0xb]
    // 0x947a6c: DecompressPointer r2
    //     0x947a6c: add             x2, x2, HEAP, lsl #32
    // 0x947a70: cmp             w2, NULL
    // 0x947a74: b.eq            #0x947b50
    // 0x947a78: LoadField: r3 = r0->field_33
    //     0x947a78: ldur            w3, [x0, #0x33]
    // 0x947a7c: DecompressPointer r3
    //     0x947a7c: add             x3, x3, HEAP, lsl #32
    // 0x947a80: LoadField: r4 = r3->field_27
    //     0x947a80: ldur            w4, [x3, #0x27]
    // 0x947a84: DecompressPointer r4
    //     0x947a84: add             x4, x4, HEAP, lsl #32
    // 0x947a88: LoadField: r3 = r4->field_7
    //     0x947a88: ldur            w3, [x4, #7]
    // 0x947a8c: DecompressPointer r3
    //     0x947a8c: add             x3, x3, HEAP, lsl #32
    // 0x947a90: LoadField: r4 = r0->field_3f
    //     0x947a90: ldur            w4, [x0, #0x3f]
    // 0x947a94: DecompressPointer r4
    //     0x947a94: add             x4, x4, HEAP, lsl #32
    // 0x947a98: LoadField: r5 = r4->field_27
    //     0x947a98: ldur            w5, [x4, #0x27]
    // 0x947a9c: DecompressPointer r5
    //     0x947a9c: add             x5, x5, HEAP, lsl #32
    // 0x947aa0: LoadField: r4 = r5->field_7
    //     0x947aa0: ldur            w4, [x5, #7]
    // 0x947aa4: DecompressPointer r4
    //     0x947aa4: add             x4, x4, HEAP, lsl #32
    // 0x947aa8: LoadField: r5 = r0->field_3b
    //     0x947aa8: ldur            w5, [x0, #0x3b]
    // 0x947aac: DecompressPointer r5
    //     0x947aac: add             x5, x5, HEAP, lsl #32
    // 0x947ab0: LoadField: r6 = r5->field_27
    //     0x947ab0: ldur            w6, [x5, #0x27]
    // 0x947ab4: DecompressPointer r6
    //     0x947ab4: add             x6, x6, HEAP, lsl #32
    // 0x947ab8: LoadField: r5 = r6->field_7
    //     0x947ab8: ldur            w5, [x6, #7]
    // 0x947abc: DecompressPointer r5
    //     0x947abc: add             x5, x5, HEAP, lsl #32
    // 0x947ac0: LoadField: r6 = r0->field_47
    //     0x947ac0: ldur            w6, [x0, #0x47]
    // 0x947ac4: DecompressPointer r6
    //     0x947ac4: add             x6, x6, HEAP, lsl #32
    // 0x947ac8: LoadField: r7 = r6->field_27
    //     0x947ac8: ldur            w7, [x6, #0x27]
    // 0x947acc: DecompressPointer r7
    //     0x947acc: add             x7, x7, HEAP, lsl #32
    // 0x947ad0: LoadField: r6 = r7->field_7
    //     0x947ad0: ldur            w6, [x7, #7]
    // 0x947ad4: DecompressPointer r6
    //     0x947ad4: add             x6, x6, HEAP, lsl #32
    // 0x947ad8: LoadField: r7 = r0->field_37
    //     0x947ad8: ldur            w7, [x0, #0x37]
    // 0x947adc: DecompressPointer r7
    //     0x947adc: add             x7, x7, HEAP, lsl #32
    // 0x947ae0: LoadField: r8 = r7->field_27
    //     0x947ae0: ldur            w8, [x7, #0x27]
    // 0x947ae4: DecompressPointer r8
    //     0x947ae4: add             x8, x8, HEAP, lsl #32
    // 0x947ae8: LoadField: r7 = r8->field_7
    //     0x947ae8: ldur            w7, [x8, #7]
    // 0x947aec: DecompressPointer r7
    //     0x947aec: add             x7, x7, HEAP, lsl #32
    // 0x947af0: LoadField: r8 = r0->field_43
    //     0x947af0: ldur            w8, [x0, #0x43]
    // 0x947af4: DecompressPointer r8
    //     0x947af4: add             x8, x8, HEAP, lsl #32
    // 0x947af8: LoadField: r0 = r8->field_27
    //     0x947af8: ldur            w0, [x8, #0x27]
    // 0x947afc: DecompressPointer r0
    //     0x947afc: add             x0, x0, HEAP, lsl #32
    // 0x947b00: LoadField: r8 = r0->field_7
    //     0x947b00: ldur            w8, [x0, #7]
    // 0x947b04: DecompressPointer r8
    //     0x947b04: add             x8, x8, HEAP, lsl #32
    // 0x947b08: LoadField: r0 = r1->field_13
    //     0x947b08: ldur            w0, [x1, #0x13]
    // 0x947b0c: DecompressPointer r0
    //     0x947b0c: add             x0, x0, HEAP, lsl #32
    // 0x947b10: LoadField: r1 = r2->field_f
    //     0x947b10: ldur            w1, [x2, #0xf]
    // 0x947b14: DecompressPointer r1
    //     0x947b14: add             x1, x1, HEAP, lsl #32
    // 0x947b18: stp             x3, x1, [SP, #0x30]
    // 0x947b1c: stp             x5, x4, [SP, #0x20]
    // 0x947b20: stp             x7, x6, [SP, #0x10]
    // 0x947b24: stp             x0, x8, [SP]
    // 0x947b28: r4 = 0
    //     0x947b28: movz            x4, #0
    // 0x947b2c: ldr             x0, [SP, #0x38]
    // 0x947b30: r5 = UnlinkedCall_0x613b5c
    //     0x947b30: add             x16, PP, #0x54, lsl #12  ; [pp+0x54188] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x947b34: ldp             x5, lr, [x16, #0x188]
    // 0x947b38: blr             lr
    // 0x947b3c: LeaveFrame
    //     0x947b3c: mov             SP, fp
    //     0x947b40: ldp             fp, lr, [SP], #0x10
    // 0x947b44: ret
    //     0x947b44: ret             
    // 0x947b48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x947b48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x947b4c: b               #0x947a60
    // 0x947b50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x947b50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x947b54, size: 0xa0
    // 0x947b54: EnterFrame
    //     0x947b54: stp             fp, lr, [SP, #-0x10]!
    //     0x947b58: mov             fp, SP
    // 0x947b5c: AllocStack(0x40)
    //     0x947b5c: sub             SP, SP, #0x40
    // 0x947b60: SetupParameters()
    //     0x947b60: ldr             x0, [fp, #0x18]
    //     0x947b64: ldur            w1, [x0, #0x17]
    //     0x947b68: add             x1, x1, HEAP, lsl #32
    // 0x947b6c: CheckStackOverflow
    //     0x947b6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x947b70: cmp             SP, x16
    //     0x947b74: b.ls            #0x947be8
    // 0x947b78: LoadField: r0 = r1->field_f
    //     0x947b78: ldur            w0, [x1, #0xf]
    // 0x947b7c: DecompressPointer r0
    //     0x947b7c: add             x0, x0, HEAP, lsl #32
    // 0x947b80: LoadField: r1 = r0->field_b
    //     0x947b80: ldur            w1, [x0, #0xb]
    // 0x947b84: DecompressPointer r1
    //     0x947b84: add             x1, x1, HEAP, lsl #32
    // 0x947b88: cmp             w1, NULL
    // 0x947b8c: b.eq            #0x947bf0
    // 0x947b90: LoadField: r0 = r1->field_f
    //     0x947b90: ldur            w0, [x1, #0xf]
    // 0x947b94: DecompressPointer r0
    //     0x947b94: add             x0, x0, HEAP, lsl #32
    // 0x947b98: r16 = ""
    //     0x947b98: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x947b9c: stp             x16, x0, [SP, #0x30]
    // 0x947ba0: r16 = ""
    //     0x947ba0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x947ba4: r30 = ""
    //     0x947ba4: ldr             lr, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x947ba8: stp             lr, x16, [SP, #0x20]
    // 0x947bac: r16 = ""
    //     0x947bac: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x947bb0: r30 = ""
    //     0x947bb0: ldr             lr, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x947bb4: stp             lr, x16, [SP, #0x10]
    // 0x947bb8: r16 = ""
    //     0x947bb8: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x947bbc: r30 = false
    //     0x947bbc: add             lr, NULL, #0x30  ; false
    // 0x947bc0: stp             lr, x16, [SP]
    // 0x947bc4: r4 = 0
    //     0x947bc4: movz            x4, #0
    // 0x947bc8: ldr             x0, [SP, #0x38]
    // 0x947bcc: r16 = UnlinkedCall_0x613b5c
    //     0x947bcc: add             x16, PP, #0x54, lsl #12  ; [pp+0x54268] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x947bd0: add             x16, x16, #0x268
    // 0x947bd4: ldp             x5, lr, [x16]
    // 0x947bd8: blr             lr
    // 0x947bdc: LeaveFrame
    //     0x947bdc: mov             SP, fp
    //     0x947be0: ldp             fp, lr, [SP], #0x10
    // 0x947be4: ret
    //     0x947be4: ret             
    // 0x947be8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x947be8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x947bec: b               #0x947b78
    // 0x947bf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x947bf0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa01a18, size: 0x24
    // 0xa01a18: r1 = true
    //     0xa01a18: add             x1, NULL, #0x20  ; true
    // 0xa01a1c: ldr             x2, [SP]
    // 0xa01a20: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa01a20: ldur            w3, [x2, #0x17]
    // 0xa01a24: DecompressPointer r3
    //     0xa01a24: add             x3, x3, HEAP, lsl #32
    // 0xa01a28: LoadField: r2 = r3->field_f
    //     0xa01a28: ldur            w2, [x3, #0xf]
    // 0xa01a2c: DecompressPointer r2
    //     0xa01a2c: add             x2, x2, HEAP, lsl #32
    // 0xa01a30: StoreField: r2->field_63 = r1
    //     0xa01a30: stur            w1, [x2, #0x63]
    // 0xa01a34: r0 = Null
    //     0xa01a34: mov             x0, NULL
    // 0xa01a38: ret
    //     0xa01a38: ret             
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xa01a3c, size: 0x78
    // 0xa01a3c: EnterFrame
    //     0xa01a3c: stp             fp, lr, [SP, #-0x10]!
    //     0xa01a40: mov             fp, SP
    // 0xa01a44: AllocStack(0x10)
    //     0xa01a44: sub             SP, SP, #0x10
    // 0xa01a48: SetupParameters()
    //     0xa01a48: ldr             x0, [fp, #0x18]
    //     0xa01a4c: ldur            w3, [x0, #0x17]
    //     0xa01a50: add             x3, x3, HEAP, lsl #32
    //     0xa01a54: stur            x3, [fp, #-0x10]
    // 0xa01a58: CheckStackOverflow
    //     0xa01a58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa01a5c: cmp             SP, x16
    //     0xa01a60: b.ls            #0xa01aac
    // 0xa01a64: LoadField: r0 = r3->field_f
    //     0xa01a64: ldur            w0, [x3, #0xf]
    // 0xa01a68: DecompressPointer r0
    //     0xa01a68: add             x0, x0, HEAP, lsl #32
    // 0xa01a6c: mov             x2, x3
    // 0xa01a70: stur            x0, [fp, #-8]
    // 0xa01a74: r1 = Function '<anonymous closure>':.
    //     0xa01a74: add             x1, PP, #0x54, lsl #12  ; [pp+0x541c0] AnonymousClosure: (0xa01a18), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xa01a78: ldr             x1, [x1, #0x1c0]
    // 0xa01a7c: r0 = AllocateClosure()
    //     0xa01a7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa01a80: ldur            x1, [fp, #-8]
    // 0xa01a84: mov             x2, x0
    // 0xa01a88: r0 = setState()
    //     0xa01a88: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa01a8c: ldur            x0, [fp, #-0x10]
    // 0xa01a90: LoadField: r1 = r0->field_f
    //     0xa01a90: ldur            w1, [x0, #0xf]
    // 0xa01a94: DecompressPointer r1
    //     0xa01a94: add             x1, x1, HEAP, lsl #32
    // 0xa01a98: r0 = _validateAllFields()
    //     0xa01a98: bl              #0x947734  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields
    // 0xa01a9c: r0 = Null
    //     0xa01a9c: mov             x0, NULL
    // 0xa01aa0: LeaveFrame
    //     0xa01aa0: mov             SP, fp
    //     0xa01aa4: ldp             fp, lr, [SP], #0x10
    // 0xa01aa8: ret
    //     0xa01aa8: ret             
    // 0xa01aac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa01aac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa01ab0: b               #0xa01a64
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa01cc4, size: 0x24
    // 0xa01cc4: r1 = true
    //     0xa01cc4: add             x1, NULL, #0x20  ; true
    // 0xa01cc8: ldr             x2, [SP]
    // 0xa01ccc: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa01ccc: ldur            w3, [x2, #0x17]
    // 0xa01cd0: DecompressPointer r3
    //     0xa01cd0: add             x3, x3, HEAP, lsl #32
    // 0xa01cd4: LoadField: r2 = r3->field_f
    //     0xa01cd4: ldur            w2, [x3, #0xf]
    // 0xa01cd8: DecompressPointer r2
    //     0xa01cd8: add             x2, x2, HEAP, lsl #32
    // 0xa01cdc: StoreField: r2->field_5f = r1
    //     0xa01cdc: stur            w1, [x2, #0x5f]
    // 0xa01ce0: r0 = Null
    //     0xa01ce0: mov             x0, NULL
    // 0xa01ce4: ret
    //     0xa01ce4: ret             
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xa01ce8, size: 0xfc
    // 0xa01ce8: EnterFrame
    //     0xa01ce8: stp             fp, lr, [SP, #-0x10]!
    //     0xa01cec: mov             fp, SP
    // 0xa01cf0: AllocStack(0x20)
    //     0xa01cf0: sub             SP, SP, #0x20
    // 0xa01cf4: SetupParameters()
    //     0xa01cf4: ldr             x0, [fp, #0x18]
    //     0xa01cf8: ldur            w3, [x0, #0x17]
    //     0xa01cfc: add             x3, x3, HEAP, lsl #32
    //     0xa01d00: stur            x3, [fp, #-0x10]
    // 0xa01d04: CheckStackOverflow
    //     0xa01d04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa01d08: cmp             SP, x16
    //     0xa01d0c: b.ls            #0xa01dd8
    // 0xa01d10: LoadField: r0 = r3->field_f
    //     0xa01d10: ldur            w0, [x3, #0xf]
    // 0xa01d14: DecompressPointer r0
    //     0xa01d14: add             x0, x0, HEAP, lsl #32
    // 0xa01d18: mov             x2, x3
    // 0xa01d1c: stur            x0, [fp, #-8]
    // 0xa01d20: r1 = Function '<anonymous closure>':.
    //     0xa01d20: add             x1, PP, #0x54, lsl #12  ; [pp+0x541d0] AnonymousClosure: (0xa01cc4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xa01d24: ldr             x1, [x1, #0x1d0]
    // 0xa01d28: r0 = AllocateClosure()
    //     0xa01d28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa01d2c: ldur            x1, [fp, #-8]
    // 0xa01d30: mov             x2, x0
    // 0xa01d34: r0 = setState()
    //     0xa01d34: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa01d38: ldr             x0, [fp, #0x10]
    // 0xa01d3c: cmp             w0, NULL
    // 0xa01d40: b.ne            #0xa01d4c
    // 0xa01d44: r1 = Null
    //     0xa01d44: mov             x1, NULL
    // 0xa01d48: b               #0xa01d50
    // 0xa01d4c: LoadField: r1 = r0->field_7
    //     0xa01d4c: ldur            w1, [x0, #7]
    // 0xa01d50: cmp             w1, NULL
    // 0xa01d54: b.ne            #0xa01d60
    // 0xa01d58: r1 = 0
    //     0xa01d58: movz            x1, #0
    // 0xa01d5c: b               #0xa01d68
    // 0xa01d60: r2 = LoadInt32Instr(r1)
    //     0xa01d60: sbfx            x2, x1, #1, #0x1f
    // 0xa01d64: mov             x1, x2
    // 0xa01d68: cmp             x1, #5
    // 0xa01d6c: b.le            #0xa01db8
    // 0xa01d70: ldur            x1, [fp, #-0x10]
    // 0xa01d74: LoadField: r2 = r1->field_f
    //     0xa01d74: ldur            w2, [x1, #0xf]
    // 0xa01d78: DecompressPointer r2
    //     0xa01d78: add             x2, x2, HEAP, lsl #32
    // 0xa01d7c: LoadField: r3 = r2->field_b
    //     0xa01d7c: ldur            w3, [x2, #0xb]
    // 0xa01d80: DecompressPointer r3
    //     0xa01d80: add             x3, x3, HEAP, lsl #32
    // 0xa01d84: cmp             w3, NULL
    // 0xa01d88: b.eq            #0xa01de0
    // 0xa01d8c: cmp             w0, NULL
    // 0xa01d90: b.ne            #0xa01d98
    // 0xa01d94: r0 = ""
    //     0xa01d94: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa01d98: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xa01d98: ldur            w2, [x3, #0x17]
    // 0xa01d9c: DecompressPointer r2
    //     0xa01d9c: add             x2, x2, HEAP, lsl #32
    // 0xa01da0: stp             x0, x2, [SP]
    // 0xa01da4: r4 = 0
    //     0xa01da4: movz            x4, #0
    // 0xa01da8: ldr             x0, [SP, #8]
    // 0xa01dac: r5 = UnlinkedCall_0x613b5c
    //     0xa01dac: add             x16, PP, #0x54, lsl #12  ; [pp+0x541d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa01db0: ldp             x5, lr, [x16, #0x1d8]
    // 0xa01db4: blr             lr
    // 0xa01db8: ldur            x0, [fp, #-0x10]
    // 0xa01dbc: LoadField: r1 = r0->field_f
    //     0xa01dbc: ldur            w1, [x0, #0xf]
    // 0xa01dc0: DecompressPointer r1
    //     0xa01dc0: add             x1, x1, HEAP, lsl #32
    // 0xa01dc4: r0 = _validateAllFields()
    //     0xa01dc4: bl              #0x947734  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields
    // 0xa01dc8: r0 = Null
    //     0xa01dc8: mov             x0, NULL
    // 0xa01dcc: LeaveFrame
    //     0xa01dcc: mov             SP, fp
    //     0xa01dd0: ldp             fp, lr, [SP], #0x10
    // 0xa01dd4: ret
    //     0xa01dd4: ret             
    // 0xa01dd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa01dd8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa01ddc: b               #0xa01d10
    // 0xa01de0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa01de0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa0202c, size: 0x24
    // 0xa0202c: r1 = true
    //     0xa0202c: add             x1, NULL, #0x20  ; true
    // 0xa02030: ldr             x2, [SP]
    // 0xa02034: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa02034: ldur            w3, [x2, #0x17]
    // 0xa02038: DecompressPointer r3
    //     0xa02038: add             x3, x3, HEAP, lsl #32
    // 0xa0203c: LoadField: r2 = r3->field_f
    //     0xa0203c: ldur            w2, [x3, #0xf]
    // 0xa02040: DecompressPointer r2
    //     0xa02040: add             x2, x2, HEAP, lsl #32
    // 0xa02044: StoreField: r2->field_5b = r1
    //     0xa02044: stur            w1, [x2, #0x5b]
    // 0xa02048: r0 = Null
    //     0xa02048: mov             x0, NULL
    // 0xa0204c: ret
    //     0xa0204c: ret             
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xa02050, size: 0x78
    // 0xa02050: EnterFrame
    //     0xa02050: stp             fp, lr, [SP, #-0x10]!
    //     0xa02054: mov             fp, SP
    // 0xa02058: AllocStack(0x10)
    //     0xa02058: sub             SP, SP, #0x10
    // 0xa0205c: SetupParameters()
    //     0xa0205c: ldr             x0, [fp, #0x18]
    //     0xa02060: ldur            w3, [x0, #0x17]
    //     0xa02064: add             x3, x3, HEAP, lsl #32
    //     0xa02068: stur            x3, [fp, #-0x10]
    // 0xa0206c: CheckStackOverflow
    //     0xa0206c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa02070: cmp             SP, x16
    //     0xa02074: b.ls            #0xa020c0
    // 0xa02078: LoadField: r0 = r3->field_f
    //     0xa02078: ldur            w0, [x3, #0xf]
    // 0xa0207c: DecompressPointer r0
    //     0xa0207c: add             x0, x0, HEAP, lsl #32
    // 0xa02080: mov             x2, x3
    // 0xa02084: stur            x0, [fp, #-8]
    // 0xa02088: r1 = Function '<anonymous closure>':.
    //     0xa02088: add             x1, PP, #0x54, lsl #12  ; [pp+0x54200] AnonymousClosure: (0xa0202c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xa0208c: ldr             x1, [x1, #0x200]
    // 0xa02090: r0 = AllocateClosure()
    //     0xa02090: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa02094: ldur            x1, [fp, #-8]
    // 0xa02098: mov             x2, x0
    // 0xa0209c: r0 = setState()
    //     0xa0209c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa020a0: ldur            x0, [fp, #-0x10]
    // 0xa020a4: LoadField: r1 = r0->field_f
    //     0xa020a4: ldur            w1, [x0, #0xf]
    // 0xa020a8: DecompressPointer r1
    //     0xa020a8: add             x1, x1, HEAP, lsl #32
    // 0xa020ac: r0 = _validateAllFields()
    //     0xa020ac: bl              #0x947734  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields
    // 0xa020b0: r0 = Null
    //     0xa020b0: mov             x0, NULL
    // 0xa020b4: LeaveFrame
    //     0xa020b4: mov             SP, fp
    //     0xa020b8: ldp             fp, lr, [SP], #0x10
    // 0xa020bc: ret
    //     0xa020bc: ret             
    // 0xa020c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa020c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa020c4: b               #0xa02078
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa022d8, size: 0x24
    // 0xa022d8: r1 = true
    //     0xa022d8: add             x1, NULL, #0x20  ; true
    // 0xa022dc: ldr             x2, [SP]
    // 0xa022e0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa022e0: ldur            w3, [x2, #0x17]
    // 0xa022e4: DecompressPointer r3
    //     0xa022e4: add             x3, x3, HEAP, lsl #32
    // 0xa022e8: LoadField: r2 = r3->field_f
    //     0xa022e8: ldur            w2, [x3, #0xf]
    // 0xa022ec: DecompressPointer r2
    //     0xa022ec: add             x2, x2, HEAP, lsl #32
    // 0xa022f0: StoreField: r2->field_57 = r1
    //     0xa022f0: stur            w1, [x2, #0x57]
    // 0xa022f4: r0 = Null
    //     0xa022f4: mov             x0, NULL
    // 0xa022f8: ret
    //     0xa022f8: ret             
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xa022fc, size: 0x78
    // 0xa022fc: EnterFrame
    //     0xa022fc: stp             fp, lr, [SP, #-0x10]!
    //     0xa02300: mov             fp, SP
    // 0xa02304: AllocStack(0x10)
    //     0xa02304: sub             SP, SP, #0x10
    // 0xa02308: SetupParameters()
    //     0xa02308: ldr             x0, [fp, #0x18]
    //     0xa0230c: ldur            w3, [x0, #0x17]
    //     0xa02310: add             x3, x3, HEAP, lsl #32
    //     0xa02314: stur            x3, [fp, #-0x10]
    // 0xa02318: CheckStackOverflow
    //     0xa02318: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0231c: cmp             SP, x16
    //     0xa02320: b.ls            #0xa0236c
    // 0xa02324: LoadField: r0 = r3->field_f
    //     0xa02324: ldur            w0, [x3, #0xf]
    // 0xa02328: DecompressPointer r0
    //     0xa02328: add             x0, x0, HEAP, lsl #32
    // 0xa0232c: mov             x2, x3
    // 0xa02330: stur            x0, [fp, #-8]
    // 0xa02334: r1 = Function '<anonymous closure>':.
    //     0xa02334: add             x1, PP, #0x54, lsl #12  ; [pp+0x54218] AnonymousClosure: (0xa022d8), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xa02338: ldr             x1, [x1, #0x218]
    // 0xa0233c: r0 = AllocateClosure()
    //     0xa0233c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa02340: ldur            x1, [fp, #-8]
    // 0xa02344: mov             x2, x0
    // 0xa02348: r0 = setState()
    //     0xa02348: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa0234c: ldur            x0, [fp, #-0x10]
    // 0xa02350: LoadField: r1 = r0->field_f
    //     0xa02350: ldur            w1, [x0, #0xf]
    // 0xa02354: DecompressPointer r1
    //     0xa02354: add             x1, x1, HEAP, lsl #32
    // 0xa02358: r0 = _validateAllFields()
    //     0xa02358: bl              #0x947734  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields
    // 0xa0235c: r0 = Null
    //     0xa0235c: mov             x0, NULL
    // 0xa02360: LeaveFrame
    //     0xa02360: mov             SP, fp
    //     0xa02364: ldp             fp, lr, [SP], #0x10
    // 0xa02368: ret
    //     0xa02368: ret             
    // 0xa0236c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0236c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa02370: b               #0xa02324
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa024dc, size: 0x24
    // 0xa024dc: r1 = true
    //     0xa024dc: add             x1, NULL, #0x20  ; true
    // 0xa024e0: ldr             x2, [SP]
    // 0xa024e4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa024e4: ldur            w3, [x2, #0x17]
    // 0xa024e8: DecompressPointer r3
    //     0xa024e8: add             x3, x3, HEAP, lsl #32
    // 0xa024ec: LoadField: r2 = r3->field_f
    //     0xa024ec: ldur            w2, [x3, #0xf]
    // 0xa024f0: DecompressPointer r2
    //     0xa024f0: add             x2, x2, HEAP, lsl #32
    // 0xa024f4: StoreField: r2->field_53 = r1
    //     0xa024f4: stur            w1, [x2, #0x53]
    // 0xa024f8: r0 = Null
    //     0xa024f8: mov             x0, NULL
    // 0xa024fc: ret
    //     0xa024fc: ret             
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xa02500, size: 0x78
    // 0xa02500: EnterFrame
    //     0xa02500: stp             fp, lr, [SP, #-0x10]!
    //     0xa02504: mov             fp, SP
    // 0xa02508: AllocStack(0x10)
    //     0xa02508: sub             SP, SP, #0x10
    // 0xa0250c: SetupParameters()
    //     0xa0250c: ldr             x0, [fp, #0x18]
    //     0xa02510: ldur            w3, [x0, #0x17]
    //     0xa02514: add             x3, x3, HEAP, lsl #32
    //     0xa02518: stur            x3, [fp, #-0x10]
    // 0xa0251c: CheckStackOverflow
    //     0xa0251c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa02520: cmp             SP, x16
    //     0xa02524: b.ls            #0xa02570
    // 0xa02528: LoadField: r0 = r3->field_f
    //     0xa02528: ldur            w0, [x3, #0xf]
    // 0xa0252c: DecompressPointer r0
    //     0xa0252c: add             x0, x0, HEAP, lsl #32
    // 0xa02530: mov             x2, x3
    // 0xa02534: stur            x0, [fp, #-8]
    // 0xa02538: r1 = Function '<anonymous closure>':.
    //     0xa02538: add             x1, PP, #0x54, lsl #12  ; [pp+0x54228] AnonymousClosure: (0xa024dc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xa0253c: ldr             x1, [x1, #0x228]
    // 0xa02540: r0 = AllocateClosure()
    //     0xa02540: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa02544: ldur            x1, [fp, #-8]
    // 0xa02548: mov             x2, x0
    // 0xa0254c: r0 = setState()
    //     0xa0254c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa02550: ldur            x0, [fp, #-0x10]
    // 0xa02554: LoadField: r1 = r0->field_f
    //     0xa02554: ldur            w1, [x0, #0xf]
    // 0xa02558: DecompressPointer r1
    //     0xa02558: add             x1, x1, HEAP, lsl #32
    // 0xa0255c: r0 = _validateAllFields()
    //     0xa0255c: bl              #0x947734  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields
    // 0xa02560: r0 = Null
    //     0xa02560: mov             x0, NULL
    // 0xa02564: LeaveFrame
    //     0xa02564: mov             SP, fp
    //     0xa02568: ldp             fp, lr, [SP], #0x10
    // 0xa0256c: ret
    //     0xa0256c: ret             
    // 0xa02570: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa02570: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa02574: b               #0xa02528
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa05258, size: 0x24
    // 0xa05258: r1 = true
    //     0xa05258: add             x1, NULL, #0x20  ; true
    // 0xa0525c: ldr             x2, [SP]
    // 0xa05260: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa05260: ldur            w3, [x2, #0x17]
    // 0xa05264: DecompressPointer r3
    //     0xa05264: add             x3, x3, HEAP, lsl #32
    // 0xa05268: LoadField: r2 = r3->field_f
    //     0xa05268: ldur            w2, [x3, #0xf]
    // 0xa0526c: DecompressPointer r2
    //     0xa0526c: add             x2, x2, HEAP, lsl #32
    // 0xa05270: StoreField: r2->field_67 = r1
    //     0xa05270: stur            w1, [x2, #0x67]
    // 0xa05274: r0 = Null
    //     0xa05274: mov             x0, NULL
    // 0xa05278: ret
    //     0xa05278: ret             
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xa0527c, size: 0x78
    // 0xa0527c: EnterFrame
    //     0xa0527c: stp             fp, lr, [SP, #-0x10]!
    //     0xa05280: mov             fp, SP
    // 0xa05284: AllocStack(0x10)
    //     0xa05284: sub             SP, SP, #0x10
    // 0xa05288: SetupParameters()
    //     0xa05288: ldr             x0, [fp, #0x18]
    //     0xa0528c: ldur            w3, [x0, #0x17]
    //     0xa05290: add             x3, x3, HEAP, lsl #32
    //     0xa05294: stur            x3, [fp, #-0x10]
    // 0xa05298: CheckStackOverflow
    //     0xa05298: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0529c: cmp             SP, x16
    //     0xa052a0: b.ls            #0xa052ec
    // 0xa052a4: LoadField: r0 = r3->field_f
    //     0xa052a4: ldur            w0, [x3, #0xf]
    // 0xa052a8: DecompressPointer r0
    //     0xa052a8: add             x0, x0, HEAP, lsl #32
    // 0xa052ac: mov             x2, x3
    // 0xa052b0: stur            x0, [fp, #-8]
    // 0xa052b4: r1 = Function '<anonymous closure>':.
    //     0xa052b4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54168] AnonymousClosure: (0xa05258), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xa052b8: ldr             x1, [x1, #0x168]
    // 0xa052bc: r0 = AllocateClosure()
    //     0xa052bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa052c0: ldur            x1, [fp, #-8]
    // 0xa052c4: mov             x2, x0
    // 0xa052c8: r0 = setState()
    //     0xa052c8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa052cc: ldur            x0, [fp, #-0x10]
    // 0xa052d0: LoadField: r1 = r0->field_f
    //     0xa052d0: ldur            w1, [x0, #0xf]
    // 0xa052d4: DecompressPointer r1
    //     0xa052d4: add             x1, x1, HEAP, lsl #32
    // 0xa052d8: r0 = _validateAllFields()
    //     0xa052d8: bl              #0x947734  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields
    // 0xa052dc: r0 = Null
    //     0xa052dc: mov             x0, NULL
    // 0xa052e0: LeaveFrame
    //     0xa052e0: mov             SP, fp
    //     0xa052e4: ldp             fp, lr, [SP], #0x10
    // 0xa052e8: ret
    //     0xa052e8: ret             
    // 0xa052ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa052ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa052f0: b               #0xa052a4
  }
  _ build(/* No info */) {
    // ** addr: 0xbb1138, size: 0x28ec
    // 0xbb1138: EnterFrame
    //     0xbb1138: stp             fp, lr, [SP, #-0x10]!
    //     0xbb113c: mov             fp, SP
    // 0xbb1140: AllocStack(0xe8)
    //     0xbb1140: sub             SP, SP, #0xe8
    // 0xbb1144: SetupParameters(CheckoutAddressWidgetState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbb1144: stur            x1, [fp, #-8]
    //     0xbb1148: mov             x16, x2
    //     0xbb114c: mov             x2, x1
    //     0xbb1150: mov             x1, x16
    //     0xbb1154: stur            x1, [fp, #-0x10]
    // 0xbb1158: CheckStackOverflow
    //     0xbb1158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb115c: cmp             SP, x16
    //     0xbb1160: b.ls            #0xbb39d8
    // 0xbb1164: r1 = 1
    //     0xbb1164: movz            x1, #0x1
    // 0xbb1168: r0 = AllocateContext()
    //     0xbb1168: bl              #0x16f6108  ; AllocateContextStub
    // 0xbb116c: ldur            x2, [fp, #-8]
    // 0xbb1170: stur            x0, [fp, #-0x28]
    // 0xbb1174: StoreField: r0->field_f = r2
    //     0xbb1174: stur            w2, [x0, #0xf]
    // 0xbb1178: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xbb1178: ldur            w1, [x2, #0x17]
    // 0xbb117c: DecompressPointer r1
    //     0xbb117c: add             x1, x1, HEAP, lsl #32
    // 0xbb1180: stur            x1, [fp, #-0x20]
    // 0xbb1184: LoadField: r3 = r2->field_b
    //     0xbb1184: ldur            w3, [x2, #0xb]
    // 0xbb1188: DecompressPointer r3
    //     0xbb1188: add             x3, x3, HEAP, lsl #32
    // 0xbb118c: cmp             w3, NULL
    // 0xbb1190: b.eq            #0xbb39e0
    // 0xbb1194: LoadField: r4 = r3->field_1b
    //     0xbb1194: ldur            w4, [x3, #0x1b]
    // 0xbb1198: DecompressPointer r4
    //     0xbb1198: add             x4, x4, HEAP, lsl #32
    // 0xbb119c: LoadField: r3 = r4->field_b
    //     0xbb119c: ldur            w3, [x4, #0xb]
    // 0xbb11a0: DecompressPointer r3
    //     0xbb11a0: add             x3, x3, HEAP, lsl #32
    // 0xbb11a4: cmp             w3, NULL
    // 0xbb11a8: b.ne            #0xbb11b4
    // 0xbb11ac: r3 = Null
    //     0xbb11ac: mov             x3, NULL
    // 0xbb11b0: b               #0xbb11c8
    // 0xbb11b4: LoadField: r4 = r3->field_7
    //     0xbb11b4: ldur            w4, [x3, #7]
    // 0xbb11b8: cbz             w4, #0xbb11c4
    // 0xbb11bc: r3 = false
    //     0xbb11bc: add             x3, NULL, #0x30  ; false
    // 0xbb11c0: b               #0xbb11c8
    // 0xbb11c4: r3 = true
    //     0xbb11c4: add             x3, NULL, #0x20  ; true
    // 0xbb11c8: cmp             w3, NULL
    // 0xbb11cc: b.ne            #0xbb11d4
    // 0xbb11d0: r3 = true
    //     0xbb11d0: add             x3, NULL, #0x20  ; true
    // 0xbb11d4: stur            x3, [fp, #-0x18]
    // 0xbb11d8: r16 = "[a-zA-Z ]"
    //     0xbb11d8: add             x16, PP, #0x54, lsl #12  ; [pp+0x54040] "[a-zA-Z ]"
    //     0xbb11dc: ldr             x16, [x16, #0x40]
    // 0xbb11e0: stp             x16, NULL, [SP, #0x20]
    // 0xbb11e4: r16 = false
    //     0xbb11e4: add             x16, NULL, #0x30  ; false
    // 0xbb11e8: r30 = true
    //     0xbb11e8: add             lr, NULL, #0x20  ; true
    // 0xbb11ec: stp             lr, x16, [SP, #0x10]
    // 0xbb11f0: r16 = false
    //     0xbb11f0: add             x16, NULL, #0x30  ; false
    // 0xbb11f4: r30 = false
    //     0xbb11f4: add             lr, NULL, #0x30  ; false
    // 0xbb11f8: stp             lr, x16, [SP]
    // 0xbb11fc: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xbb11fc: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xbb1200: r0 = _RegExp()
    //     0xbb1200: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xbb1204: stur            x0, [fp, #-0x30]
    // 0xbb1208: r0 = FilteringTextInputFormatter()
    //     0xbb1208: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xbb120c: mov             x1, x0
    // 0xbb1210: ldur            x0, [fp, #-0x30]
    // 0xbb1214: stur            x1, [fp, #-0x38]
    // 0xbb1218: StoreField: r1->field_b = r0
    //     0xbb1218: stur            w0, [x1, #0xb]
    // 0xbb121c: r0 = true
    //     0xbb121c: add             x0, NULL, #0x20  ; true
    // 0xbb1220: StoreField: r1->field_7 = r0
    //     0xbb1220: stur            w0, [x1, #7]
    // 0xbb1224: r2 = ""
    //     0xbb1224: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb1228: StoreField: r1->field_f = r2
    //     0xbb1228: stur            w2, [x1, #0xf]
    // 0xbb122c: r0 = LengthLimitingTextInputFormatter()
    //     0xbb122c: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbb1230: mov             x3, x0
    // 0xbb1234: r0 = 180
    //     0xbb1234: movz            x0, #0xb4
    // 0xbb1238: stur            x3, [fp, #-0x30]
    // 0xbb123c: StoreField: r3->field_7 = r0
    //     0xbb123c: stur            w0, [x3, #7]
    // 0xbb1240: r1 = Null
    //     0xbb1240: mov             x1, NULL
    // 0xbb1244: r2 = 4
    //     0xbb1244: movz            x2, #0x4
    // 0xbb1248: r0 = AllocateArray()
    //     0xbb1248: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb124c: mov             x2, x0
    // 0xbb1250: ldur            x0, [fp, #-0x38]
    // 0xbb1254: stur            x2, [fp, #-0x40]
    // 0xbb1258: StoreField: r2->field_f = r0
    //     0xbb1258: stur            w0, [x2, #0xf]
    // 0xbb125c: ldur            x0, [fp, #-0x30]
    // 0xbb1260: StoreField: r2->field_13 = r0
    //     0xbb1260: stur            w0, [x2, #0x13]
    // 0xbb1264: r1 = <TextInputFormatter>
    //     0xbb1264: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbb1268: ldr             x1, [x1, #0x7b0]
    // 0xbb126c: r0 = AllocateGrowableArray()
    //     0xbb126c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb1270: mov             x2, x0
    // 0xbb1274: ldur            x0, [fp, #-0x40]
    // 0xbb1278: stur            x2, [fp, #-0x30]
    // 0xbb127c: StoreField: r2->field_f = r0
    //     0xbb127c: stur            w0, [x2, #0xf]
    // 0xbb1280: r0 = 4
    //     0xbb1280: movz            x0, #0x4
    // 0xbb1284: StoreField: r2->field_b = r0
    //     0xbb1284: stur            w0, [x2, #0xb]
    // 0xbb1288: ldur            x1, [fp, #-0x10]
    // 0xbb128c: r0 = of()
    //     0xbb128c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb1290: LoadField: r1 = r0->field_87
    //     0xbb1290: ldur            w1, [x0, #0x87]
    // 0xbb1294: DecompressPointer r1
    //     0xbb1294: add             x1, x1, HEAP, lsl #32
    // 0xbb1298: LoadField: r0 = r1->field_2b
    //     0xbb1298: ldur            w0, [x1, #0x2b]
    // 0xbb129c: DecompressPointer r0
    //     0xbb129c: add             x0, x0, HEAP, lsl #32
    // 0xbb12a0: ldur            x1, [fp, #-0x10]
    // 0xbb12a4: stur            x0, [fp, #-0x38]
    // 0xbb12a8: r0 = of()
    //     0xbb12a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb12ac: LoadField: r1 = r0->field_5b
    //     0xbb12ac: ldur            w1, [x0, #0x5b]
    // 0xbb12b0: DecompressPointer r1
    //     0xbb12b0: add             x1, x1, HEAP, lsl #32
    // 0xbb12b4: r16 = 14.000000
    //     0xbb12b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb12b8: ldr             x16, [x16, #0x1d8]
    // 0xbb12bc: stp             x1, x16, [SP]
    // 0xbb12c0: ldur            x1, [fp, #-0x38]
    // 0xbb12c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb12c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb12c8: ldr             x4, [x4, #0xaa0]
    // 0xbb12cc: r0 = copyWith()
    //     0xbb12cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb12d0: ldur            x2, [fp, #-8]
    // 0xbb12d4: stur            x0, [fp, #-0x40]
    // 0xbb12d8: LoadField: r3 = r2->field_33
    //     0xbb12d8: ldur            w3, [x2, #0x33]
    // 0xbb12dc: DecompressPointer r3
    //     0xbb12dc: add             x3, x3, HEAP, lsl #32
    // 0xbb12e0: ldur            x1, [fp, #-0x10]
    // 0xbb12e4: stur            x3, [fp, #-0x38]
    // 0xbb12e8: r0 = getTextFormFieldInputDecoration()
    //     0xbb12e8: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbb12ec: ldur            x1, [fp, #-0x10]
    // 0xbb12f0: stur            x0, [fp, #-0x48]
    // 0xbb12f4: r0 = of()
    //     0xbb12f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb12f8: LoadField: r1 = r0->field_5b
    //     0xbb12f8: ldur            w1, [x0, #0x5b]
    // 0xbb12fc: DecompressPointer r1
    //     0xbb12fc: add             x1, x1, HEAP, lsl #32
    // 0xbb1300: stur            x1, [fp, #-0x50]
    // 0xbb1304: r0 = BorderSide()
    //     0xbb1304: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbb1308: mov             x1, x0
    // 0xbb130c: ldur            x0, [fp, #-0x50]
    // 0xbb1310: stur            x1, [fp, #-0x58]
    // 0xbb1314: StoreField: r1->field_7 = r0
    //     0xbb1314: stur            w0, [x1, #7]
    // 0xbb1318: d0 = 1.000000
    //     0xbb1318: fmov            d0, #1.00000000
    // 0xbb131c: StoreField: r1->field_b = d0
    //     0xbb131c: stur            d0, [x1, #0xb]
    // 0xbb1320: r0 = Instance_BorderStyle
    //     0xbb1320: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbb1324: ldr             x0, [x0, #0xf68]
    // 0xbb1328: StoreField: r1->field_13 = r0
    //     0xbb1328: stur            w0, [x1, #0x13]
    // 0xbb132c: d1 = -1.000000
    //     0xbb132c: fmov            d1, #-1.00000000
    // 0xbb1330: ArrayStore: r1[0] = d1  ; List_8
    //     0xbb1330: stur            d1, [x1, #0x17]
    // 0xbb1334: r0 = OutlineInputBorder()
    //     0xbb1334: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbb1338: mov             x2, x0
    // 0xbb133c: r0 = Instance_BorderRadius
    //     0xbb133c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbb1340: ldr             x0, [x0, #0xf70]
    // 0xbb1344: stur            x2, [fp, #-0x50]
    // 0xbb1348: StoreField: r2->field_13 = r0
    //     0xbb1348: stur            w0, [x2, #0x13]
    // 0xbb134c: d0 = 4.000000
    //     0xbb134c: fmov            d0, #4.00000000
    // 0xbb1350: StoreField: r2->field_b = d0
    //     0xbb1350: stur            d0, [x2, #0xb]
    // 0xbb1354: ldur            x1, [fp, #-0x58]
    // 0xbb1358: StoreField: r2->field_7 = r1
    //     0xbb1358: stur            w1, [x2, #7]
    // 0xbb135c: ldur            x1, [fp, #-0x10]
    // 0xbb1360: r0 = of()
    //     0xbb1360: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb1364: LoadField: r1 = r0->field_87
    //     0xbb1364: ldur            w1, [x0, #0x87]
    // 0xbb1368: DecompressPointer r1
    //     0xbb1368: add             x1, x1, HEAP, lsl #32
    // 0xbb136c: LoadField: r0 = r1->field_2b
    //     0xbb136c: ldur            w0, [x1, #0x2b]
    // 0xbb1370: DecompressPointer r0
    //     0xbb1370: add             x0, x0, HEAP, lsl #32
    // 0xbb1374: stur            x0, [fp, #-0x58]
    // 0xbb1378: r1 = Instance_Color
    //     0xbb1378: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb137c: d0 = 0.400000
    //     0xbb137c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbb1380: r0 = withOpacity()
    //     0xbb1380: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb1384: r16 = 12.000000
    //     0xbb1384: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb1388: ldr             x16, [x16, #0x9e8]
    // 0xbb138c: stp             x0, x16, [SP]
    // 0xbb1390: ldur            x1, [fp, #-0x58]
    // 0xbb1394: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb1394: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb1398: ldr             x4, [x4, #0xaa0]
    // 0xbb139c: r0 = copyWith()
    //     0xbb139c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb13a0: ldur            x1, [fp, #-0x10]
    // 0xbb13a4: stur            x0, [fp, #-0x58]
    // 0xbb13a8: r0 = of()
    //     0xbb13a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb13ac: LoadField: r1 = r0->field_87
    //     0xbb13ac: ldur            w1, [x0, #0x87]
    // 0xbb13b0: DecompressPointer r1
    //     0xbb13b0: add             x1, x1, HEAP, lsl #32
    // 0xbb13b4: LoadField: r0 = r1->field_2b
    //     0xbb13b4: ldur            w0, [x1, #0x2b]
    // 0xbb13b8: DecompressPointer r0
    //     0xbb13b8: add             x0, x0, HEAP, lsl #32
    // 0xbb13bc: r16 = 12.000000
    //     0xbb13bc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb13c0: ldr             x16, [x16, #0x9e8]
    // 0xbb13c4: r30 = Instance_MaterialColor
    //     0xbb13c4: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xbb13c8: ldr             lr, [lr, #0x180]
    // 0xbb13cc: stp             lr, x16, [SP]
    // 0xbb13d0: mov             x1, x0
    // 0xbb13d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb13d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb13d8: ldr             x4, [x4, #0xaa0]
    // 0xbb13dc: r0 = copyWith()
    //     0xbb13dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb13e0: ldur            x2, [fp, #-8]
    // 0xbb13e4: stur            x0, [fp, #-0x70]
    // 0xbb13e8: LoadField: r1 = r2->field_53
    //     0xbb13e8: ldur            w1, [x2, #0x53]
    // 0xbb13ec: DecompressPointer r1
    //     0xbb13ec: add             x1, x1, HEAP, lsl #32
    // 0xbb13f0: tbnz            w1, #4, #0xbb147c
    // 0xbb13f4: LoadField: r1 = r2->field_33
    //     0xbb13f4: ldur            w1, [x2, #0x33]
    // 0xbb13f8: DecompressPointer r1
    //     0xbb13f8: add             x1, x1, HEAP, lsl #32
    // 0xbb13fc: LoadField: r3 = r1->field_27
    //     0xbb13fc: ldur            w3, [x1, #0x27]
    // 0xbb1400: DecompressPointer r3
    //     0xbb1400: add             x3, x3, HEAP, lsl #32
    // 0xbb1404: LoadField: r1 = r3->field_7
    //     0xbb1404: ldur            w1, [x3, #7]
    // 0xbb1408: DecompressPointer r1
    //     0xbb1408: add             x1, x1, HEAP, lsl #32
    // 0xbb140c: LoadField: r3 = r1->field_7
    //     0xbb140c: ldur            w3, [x1, #7]
    // 0xbb1410: cbz             w3, #0xbb142c
    // 0xbb1414: r1 = LoadInt32Instr(r3)
    //     0xbb1414: sbfx            x1, x3, #1, #0x1f
    // 0xbb1418: cmp             x1, #1
    // 0xbb141c: b.le            #0xbb142c
    // 0xbb1420: r1 = Instance_IconData
    //     0xbb1420: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbb1424: ldr             x1, [x1, #0x130]
    // 0xbb1428: b               #0xbb1434
    // 0xbb142c: r1 = Instance_IconData
    //     0xbb142c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbb1430: ldr             x1, [x1, #0x138]
    // 0xbb1434: stur            x1, [fp, #-0x68]
    // 0xbb1438: cbz             w3, #0xbb1454
    // 0xbb143c: r4 = LoadInt32Instr(r3)
    //     0xbb143c: sbfx            x4, x3, #1, #0x1f
    // 0xbb1440: cmp             x4, #1
    // 0xbb1444: b.le            #0xbb1454
    // 0xbb1448: r3 = Instance_Color
    //     0xbb1448: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb144c: ldr             x3, [x3, #0x858]
    // 0xbb1450: b               #0xbb145c
    // 0xbb1454: r3 = Instance_Color
    //     0xbb1454: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbb1458: ldr             x3, [x3, #0x50]
    // 0xbb145c: stur            x3, [fp, #-0x60]
    // 0xbb1460: r0 = Icon()
    //     0xbb1460: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbb1464: mov             x1, x0
    // 0xbb1468: ldur            x0, [fp, #-0x68]
    // 0xbb146c: StoreField: r1->field_b = r0
    //     0xbb146c: stur            w0, [x1, #0xb]
    // 0xbb1470: ldur            x0, [fp, #-0x60]
    // 0xbb1474: StoreField: r1->field_23 = r0
    //     0xbb1474: stur            w0, [x1, #0x23]
    // 0xbb1478: b               #0xbb1480
    // 0xbb147c: r1 = Null
    //     0xbb147c: mov             x1, NULL
    // 0xbb1480: ldur            x2, [fp, #-8]
    // 0xbb1484: ldur            x0, [fp, #-0x20]
    // 0xbb1488: ldur            x16, [fp, #-0x50]
    // 0xbb148c: r30 = Instance_EdgeInsets
    //     0xbb148c: add             lr, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xbb1490: ldr             lr, [lr, #0xc40]
    // 0xbb1494: stp             lr, x16, [SP, #0x20]
    // 0xbb1498: r16 = "Full Name*"
    //     0xbb1498: add             x16, PP, #0x54, lsl #12  ; [pp+0x54048] "Full Name*"
    //     0xbb149c: ldr             x16, [x16, #0x48]
    // 0xbb14a0: ldur            lr, [fp, #-0x58]
    // 0xbb14a4: stp             lr, x16, [SP, #0x10]
    // 0xbb14a8: ldur            x16, [fp, #-0x70]
    // 0xbb14ac: stp             x1, x16, [SP]
    // 0xbb14b0: ldur            x1, [fp, #-0x48]
    // 0xbb14b4: r4 = const [0, 0x7, 0x6, 0x1, contentPadding, 0x2, errorStyle, 0x5, focusedBorder, 0x1, labelStyle, 0x4, labelText, 0x3, suffixIcon, 0x6, null]
    //     0xbb14b4: add             x4, PP, #0x54, lsl #12  ; [pp+0x54050] List(17) [0, 0x7, 0x6, 0x1, "contentPadding", 0x2, "errorStyle", 0x5, "focusedBorder", 0x1, "labelStyle", 0x4, "labelText", 0x3, "suffixIcon", 0x6, Null]
    //     0xbb14b8: ldr             x4, [x4, #0x50]
    // 0xbb14bc: r0 = copyWith()
    //     0xbb14bc: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbb14c0: ldur            x2, [fp, #-8]
    // 0xbb14c4: r1 = Function '_validateCustomerNameNumber@1669306702':.
    //     0xbb14c4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54058] AnonymousClosure: (0xbb3b50), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateCustomerNameNumber (0xa05a54)
    //     0xbb14c8: ldr             x1, [x1, #0x58]
    // 0xbb14cc: stur            x0, [fp, #-0x48]
    // 0xbb14d0: r0 = AllocateClosure()
    //     0xbb14d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb14d4: ldur            x2, [fp, #-0x28]
    // 0xbb14d8: r1 = Function '<anonymous closure>':.
    //     0xbb14d8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54060] AnonymousClosure: (0xa02500), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xbb14dc: ldr             x1, [x1, #0x60]
    // 0xbb14e0: stur            x0, [fp, #-0x50]
    // 0xbb14e4: r0 = AllocateClosure()
    //     0xbb14e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb14e8: r1 = <String>
    //     0xbb14e8: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbb14ec: stur            x0, [fp, #-0x58]
    // 0xbb14f0: r0 = TextFormField()
    //     0xbb14f0: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbb14f4: stur            x0, [fp, #-0x60]
    // 0xbb14f8: ldur            x16, [fp, #-0x50]
    // 0xbb14fc: r30 = true
    //     0xbb14fc: add             lr, NULL, #0x20  ; true
    // 0xbb1500: stp             lr, x16, [SP, #0x40]
    // 0xbb1504: ldur            x16, [fp, #-0x18]
    // 0xbb1508: r30 = Instance_AutovalidateMode
    //     0xbb1508: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbb150c: ldr             lr, [lr, #0x7e8]
    // 0xbb1510: stp             lr, x16, [SP, #0x30]
    // 0xbb1514: ldur            x16, [fp, #-0x30]
    // 0xbb1518: ldur            lr, [fp, #-0x40]
    // 0xbb151c: stp             lr, x16, [SP, #0x20]
    // 0xbb1520: r16 = Instance_TextInputType
    //     0xbb1520: add             x16, PP, #0x54, lsl #12  ; [pp+0x54068] Obj!TextInputType@d55ba1
    //     0xbb1524: ldr             x16, [x16, #0x68]
    // 0xbb1528: r30 = 2
    //     0xbb1528: movz            lr, #0x2
    // 0xbb152c: stp             lr, x16, [SP, #0x10]
    // 0xbb1530: ldur            x16, [fp, #-0x38]
    // 0xbb1534: ldur            lr, [fp, #-0x58]
    // 0xbb1538: stp             lr, x16, [SP]
    // 0xbb153c: mov             x1, x0
    // 0xbb1540: ldur            x2, [fp, #-0x48]
    // 0xbb1544: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x4, autovalidateMode, 0x5, controller, 0xa, enableSuggestions, 0x3, inputFormatters, 0x6, keyboardType, 0x8, maxLines, 0x9, onChanged, 0xb, style, 0x7, validator, 0x2, null]
    //     0xbb1544: add             x4, PP, #0x54, lsl #12  ; [pp+0x54070] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x4, "autovalidateMode", 0x5, "controller", 0xa, "enableSuggestions", 0x3, "inputFormatters", 0x6, "keyboardType", 0x8, "maxLines", 0x9, "onChanged", 0xb, "style", 0x7, "validator", 0x2, Null]
    //     0xbb1548: ldr             x4, [x4, #0x70]
    // 0xbb154c: r0 = TextFormField()
    //     0xbb154c: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbb1550: r0 = Form()
    //     0xbb1550: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbb1554: mov             x1, x0
    // 0xbb1558: ldur            x0, [fp, #-0x60]
    // 0xbb155c: stur            x1, [fp, #-0x18]
    // 0xbb1560: StoreField: r1->field_b = r0
    //     0xbb1560: stur            w0, [x1, #0xb]
    // 0xbb1564: r0 = Instance_AutovalidateMode
    //     0xbb1564: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbb1568: ldr             x0, [x0, #0x800]
    // 0xbb156c: StoreField: r1->field_23 = r0
    //     0xbb156c: stur            w0, [x1, #0x23]
    // 0xbb1570: ldur            x2, [fp, #-0x20]
    // 0xbb1574: StoreField: r1->field_7 = r2
    //     0xbb1574: stur            w2, [x1, #7]
    // 0xbb1578: r0 = Padding()
    //     0xbb1578: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb157c: mov             x3, x0
    // 0xbb1580: r2 = Instance_EdgeInsets
    //     0xbb1580: add             x2, PP, #0x42, lsl #12  ; [pp+0x42620] Obj!EdgeInsets@d57e01
    //     0xbb1584: ldr             x2, [x2, #0x620]
    // 0xbb1588: stur            x3, [fp, #-0x30]
    // 0xbb158c: StoreField: r3->field_f = r2
    //     0xbb158c: stur            w2, [x3, #0xf]
    // 0xbb1590: ldur            x0, [fp, #-0x18]
    // 0xbb1594: StoreField: r3->field_b = r0
    //     0xbb1594: stur            w0, [x3, #0xb]
    // 0xbb1598: ldur            x4, [fp, #-8]
    // 0xbb159c: LoadField: r5 = r4->field_13
    //     0xbb159c: ldur            w5, [x4, #0x13]
    // 0xbb15a0: DecompressPointer r5
    //     0xbb15a0: add             x5, x5, HEAP, lsl #32
    // 0xbb15a4: stur            x5, [fp, #-0x20]
    // 0xbb15a8: LoadField: r0 = r4->field_b
    //     0xbb15a8: ldur            w0, [x4, #0xb]
    // 0xbb15ac: DecompressPointer r0
    //     0xbb15ac: add             x0, x0, HEAP, lsl #32
    // 0xbb15b0: cmp             w0, NULL
    // 0xbb15b4: b.eq            #0xbb39e4
    // 0xbb15b8: LoadField: r1 = r0->field_1b
    //     0xbb15b8: ldur            w1, [x0, #0x1b]
    // 0xbb15bc: DecompressPointer r1
    //     0xbb15bc: add             x1, x1, HEAP, lsl #32
    // 0xbb15c0: LoadField: r6 = r1->field_1b
    //     0xbb15c0: ldur            w6, [x1, #0x1b]
    // 0xbb15c4: DecompressPointer r6
    //     0xbb15c4: add             x6, x6, HEAP, lsl #32
    // 0xbb15c8: cmp             w6, NULL
    // 0xbb15cc: b.ne            #0xbb15d8
    // 0xbb15d0: r0 = Null
    //     0xbb15d0: mov             x0, NULL
    // 0xbb15d4: b               #0xbb15f0
    // 0xbb15d8: LoadField: r0 = r6->field_b
    //     0xbb15d8: ldur            w0, [x6, #0xb]
    // 0xbb15dc: cbz             w0, #0xbb15e8
    // 0xbb15e0: r1 = false
    //     0xbb15e0: add             x1, NULL, #0x30  ; false
    // 0xbb15e4: b               #0xbb15ec
    // 0xbb15e8: r1 = true
    //     0xbb15e8: add             x1, NULL, #0x20  ; true
    // 0xbb15ec: mov             x0, x1
    // 0xbb15f0: cmp             w0, NULL
    // 0xbb15f4: b.eq            #0xbb15fc
    // 0xbb15f8: tbnz            w0, #4, #0xbb1604
    // 0xbb15fc: r0 = true
    //     0xbb15fc: add             x0, NULL, #0x20  ; true
    // 0xbb1600: b               #0xbb1684
    // 0xbb1604: cmp             w6, NULL
    // 0xbb1608: b.ne            #0xbb1614
    // 0xbb160c: r0 = Null
    //     0xbb160c: mov             x0, NULL
    // 0xbb1610: b               #0xbb1678
    // 0xbb1614: LoadField: r0 = r6->field_b
    //     0xbb1614: ldur            w0, [x6, #0xb]
    // 0xbb1618: r1 = LoadInt32Instr(r0)
    //     0xbb1618: sbfx            x1, x0, #1, #0x1f
    // 0xbb161c: mov             x0, x1
    // 0xbb1620: r1 = 0
    //     0xbb1620: movz            x1, #0
    // 0xbb1624: cmp             x1, x0
    // 0xbb1628: b.hs            #0xbb39e8
    // 0xbb162c: LoadField: r0 = r6->field_f
    //     0xbb162c: ldur            w0, [x6, #0xf]
    // 0xbb1630: DecompressPointer r0
    //     0xbb1630: add             x0, x0, HEAP, lsl #32
    // 0xbb1634: LoadField: r1 = r0->field_f
    //     0xbb1634: ldur            w1, [x0, #0xf]
    // 0xbb1638: DecompressPointer r1
    //     0xbb1638: add             x1, x1, HEAP, lsl #32
    // 0xbb163c: cmp             w1, NULL
    // 0xbb1640: b.ne            #0xbb164c
    // 0xbb1644: r0 = Null
    //     0xbb1644: mov             x0, NULL
    // 0xbb1648: b               #0xbb1678
    // 0xbb164c: LoadField: r0 = r1->field_2b
    //     0xbb164c: ldur            w0, [x1, #0x2b]
    // 0xbb1650: DecompressPointer r0
    //     0xbb1650: add             x0, x0, HEAP, lsl #32
    // 0xbb1654: cmp             w0, NULL
    // 0xbb1658: b.ne            #0xbb1664
    // 0xbb165c: r0 = Null
    //     0xbb165c: mov             x0, NULL
    // 0xbb1660: b               #0xbb1678
    // 0xbb1664: LoadField: r1 = r0->field_7
    //     0xbb1664: ldur            w1, [x0, #7]
    // 0xbb1668: cbz             w1, #0xbb1674
    // 0xbb166c: r0 = false
    //     0xbb166c: add             x0, NULL, #0x30  ; false
    // 0xbb1670: b               #0xbb1678
    // 0xbb1674: r0 = true
    //     0xbb1674: add             x0, NULL, #0x20  ; true
    // 0xbb1678: cmp             w0, NULL
    // 0xbb167c: b.ne            #0xbb1684
    // 0xbb1680: r0 = true
    //     0xbb1680: add             x0, NULL, #0x20  ; true
    // 0xbb1684: stur            x0, [fp, #-0x18]
    // 0xbb1688: r0 = InitLateStaticField(0xa94) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::singleLineFormatter
    //     0xbb1688: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbb168c: ldr             x0, [x0, #0x1528]
    //     0xbb1690: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbb1694: cmp             w0, w16
    //     0xbb1698: b.ne            #0xbb16a8
    //     0xbb169c: add             x2, PP, #0x54, lsl #12  ; [pp+0x54078] Field <FilteringTextInputFormatter.singleLineFormatter>: static late final (offset: 0xa94)
    //     0xbb16a0: ldr             x2, [x2, #0x78]
    //     0xbb16a4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbb16a8: stur            x0, [fp, #-0x38]
    // 0xbb16ac: r0 = LengthLimitingTextInputFormatter()
    //     0xbb16ac: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbb16b0: mov             x3, x0
    // 0xbb16b4: r0 = 240
    //     0xbb16b4: movz            x0, #0xf0
    // 0xbb16b8: stur            x3, [fp, #-0x40]
    // 0xbb16bc: StoreField: r3->field_7 = r0
    //     0xbb16bc: stur            w0, [x3, #7]
    // 0xbb16c0: r1 = Null
    //     0xbb16c0: mov             x1, NULL
    // 0xbb16c4: r2 = 4
    //     0xbb16c4: movz            x2, #0x4
    // 0xbb16c8: r0 = AllocateArray()
    //     0xbb16c8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb16cc: mov             x2, x0
    // 0xbb16d0: ldur            x0, [fp, #-0x38]
    // 0xbb16d4: stur            x2, [fp, #-0x48]
    // 0xbb16d8: StoreField: r2->field_f = r0
    //     0xbb16d8: stur            w0, [x2, #0xf]
    // 0xbb16dc: ldur            x0, [fp, #-0x40]
    // 0xbb16e0: StoreField: r2->field_13 = r0
    //     0xbb16e0: stur            w0, [x2, #0x13]
    // 0xbb16e4: r1 = <TextInputFormatter>
    //     0xbb16e4: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbb16e8: ldr             x1, [x1, #0x7b0]
    // 0xbb16ec: r0 = AllocateGrowableArray()
    //     0xbb16ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb16f0: mov             x2, x0
    // 0xbb16f4: ldur            x0, [fp, #-0x48]
    // 0xbb16f8: stur            x2, [fp, #-0x40]
    // 0xbb16fc: StoreField: r2->field_f = r0
    //     0xbb16fc: stur            w0, [x2, #0xf]
    // 0xbb1700: r0 = 4
    //     0xbb1700: movz            x0, #0x4
    // 0xbb1704: StoreField: r2->field_b = r0
    //     0xbb1704: stur            w0, [x2, #0xb]
    // 0xbb1708: ldur            x3, [fp, #-8]
    // 0xbb170c: LoadField: r4 = r3->field_3b
    //     0xbb170c: ldur            w4, [x3, #0x3b]
    // 0xbb1710: DecompressPointer r4
    //     0xbb1710: add             x4, x4, HEAP, lsl #32
    // 0xbb1714: ldur            x1, [fp, #-0x10]
    // 0xbb1718: stur            x4, [fp, #-0x38]
    // 0xbb171c: r0 = of()
    //     0xbb171c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb1720: LoadField: r1 = r0->field_87
    //     0xbb1720: ldur            w1, [x0, #0x87]
    // 0xbb1724: DecompressPointer r1
    //     0xbb1724: add             x1, x1, HEAP, lsl #32
    // 0xbb1728: LoadField: r0 = r1->field_2b
    //     0xbb1728: ldur            w0, [x1, #0x2b]
    // 0xbb172c: DecompressPointer r0
    //     0xbb172c: add             x0, x0, HEAP, lsl #32
    // 0xbb1730: ldur            x1, [fp, #-0x10]
    // 0xbb1734: stur            x0, [fp, #-0x48]
    // 0xbb1738: r0 = of()
    //     0xbb1738: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb173c: LoadField: r1 = r0->field_5b
    //     0xbb173c: ldur            w1, [x0, #0x5b]
    // 0xbb1740: DecompressPointer r1
    //     0xbb1740: add             x1, x1, HEAP, lsl #32
    // 0xbb1744: r16 = 14.000000
    //     0xbb1744: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb1748: ldr             x16, [x16, #0x1d8]
    // 0xbb174c: stp             x1, x16, [SP]
    // 0xbb1750: ldur            x1, [fp, #-0x48]
    // 0xbb1754: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb1754: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb1758: ldr             x4, [x4, #0xaa0]
    // 0xbb175c: r0 = copyWith()
    //     0xbb175c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb1760: ldur            x1, [fp, #-0x10]
    // 0xbb1764: stur            x0, [fp, #-0x48]
    // 0xbb1768: r0 = getTextFormFieldInputDecoration()
    //     0xbb1768: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbb176c: ldur            x1, [fp, #-0x10]
    // 0xbb1770: stur            x0, [fp, #-0x50]
    // 0xbb1774: r0 = of()
    //     0xbb1774: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb1778: LoadField: r1 = r0->field_5b
    //     0xbb1778: ldur            w1, [x0, #0x5b]
    // 0xbb177c: DecompressPointer r1
    //     0xbb177c: add             x1, x1, HEAP, lsl #32
    // 0xbb1780: stur            x1, [fp, #-0x58]
    // 0xbb1784: r0 = BorderSide()
    //     0xbb1784: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbb1788: mov             x1, x0
    // 0xbb178c: ldur            x0, [fp, #-0x58]
    // 0xbb1790: stur            x1, [fp, #-0x60]
    // 0xbb1794: StoreField: r1->field_7 = r0
    //     0xbb1794: stur            w0, [x1, #7]
    // 0xbb1798: d0 = 1.000000
    //     0xbb1798: fmov            d0, #1.00000000
    // 0xbb179c: StoreField: r1->field_b = d0
    //     0xbb179c: stur            d0, [x1, #0xb]
    // 0xbb17a0: r0 = Instance_BorderStyle
    //     0xbb17a0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbb17a4: ldr             x0, [x0, #0xf68]
    // 0xbb17a8: StoreField: r1->field_13 = r0
    //     0xbb17a8: stur            w0, [x1, #0x13]
    // 0xbb17ac: d1 = -1.000000
    //     0xbb17ac: fmov            d1, #-1.00000000
    // 0xbb17b0: ArrayStore: r1[0] = d1  ; List_8
    //     0xbb17b0: stur            d1, [x1, #0x17]
    // 0xbb17b4: r0 = OutlineInputBorder()
    //     0xbb17b4: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbb17b8: mov             x2, x0
    // 0xbb17bc: r0 = Instance_BorderRadius
    //     0xbb17bc: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbb17c0: ldr             x0, [x0, #0xf70]
    // 0xbb17c4: stur            x2, [fp, #-0x58]
    // 0xbb17c8: StoreField: r2->field_13 = r0
    //     0xbb17c8: stur            w0, [x2, #0x13]
    // 0xbb17cc: d0 = 4.000000
    //     0xbb17cc: fmov            d0, #4.00000000
    // 0xbb17d0: StoreField: r2->field_b = d0
    //     0xbb17d0: stur            d0, [x2, #0xb]
    // 0xbb17d4: ldur            x1, [fp, #-0x60]
    // 0xbb17d8: StoreField: r2->field_7 = r1
    //     0xbb17d8: stur            w1, [x2, #7]
    // 0xbb17dc: ldur            x1, [fp, #-0x10]
    // 0xbb17e0: r0 = of()
    //     0xbb17e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb17e4: LoadField: r1 = r0->field_87
    //     0xbb17e4: ldur            w1, [x0, #0x87]
    // 0xbb17e8: DecompressPointer r1
    //     0xbb17e8: add             x1, x1, HEAP, lsl #32
    // 0xbb17ec: LoadField: r0 = r1->field_2b
    //     0xbb17ec: ldur            w0, [x1, #0x2b]
    // 0xbb17f0: DecompressPointer r0
    //     0xbb17f0: add             x0, x0, HEAP, lsl #32
    // 0xbb17f4: stur            x0, [fp, #-0x60]
    // 0xbb17f8: r1 = Instance_Color
    //     0xbb17f8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb17fc: d0 = 0.400000
    //     0xbb17fc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbb1800: r0 = withOpacity()
    //     0xbb1800: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb1804: r16 = 12.000000
    //     0xbb1804: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb1808: ldr             x16, [x16, #0x9e8]
    // 0xbb180c: stp             x0, x16, [SP]
    // 0xbb1810: ldur            x1, [fp, #-0x60]
    // 0xbb1814: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb1814: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb1818: ldr             x4, [x4, #0xaa0]
    // 0xbb181c: r0 = copyWith()
    //     0xbb181c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb1820: ldur            x1, [fp, #-0x10]
    // 0xbb1824: stur            x0, [fp, #-0x60]
    // 0xbb1828: r0 = of()
    //     0xbb1828: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb182c: LoadField: r1 = r0->field_87
    //     0xbb182c: ldur            w1, [x0, #0x87]
    // 0xbb1830: DecompressPointer r1
    //     0xbb1830: add             x1, x1, HEAP, lsl #32
    // 0xbb1834: LoadField: r0 = r1->field_2b
    //     0xbb1834: ldur            w0, [x1, #0x2b]
    // 0xbb1838: DecompressPointer r0
    //     0xbb1838: add             x0, x0, HEAP, lsl #32
    // 0xbb183c: r16 = 12.000000
    //     0xbb183c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb1840: ldr             x16, [x16, #0x9e8]
    // 0xbb1844: r30 = Instance_Color
    //     0xbb1844: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbb1848: ldr             lr, [lr, #0x50]
    // 0xbb184c: stp             lr, x16, [SP]
    // 0xbb1850: mov             x1, x0
    // 0xbb1854: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb1854: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb1858: ldr             x4, [x4, #0xaa0]
    // 0xbb185c: r0 = copyWith()
    //     0xbb185c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb1860: ldur            x2, [fp, #-8]
    // 0xbb1864: stur            x0, [fp, #-0x78]
    // 0xbb1868: LoadField: r1 = r2->field_57
    //     0xbb1868: ldur            w1, [x2, #0x57]
    // 0xbb186c: DecompressPointer r1
    //     0xbb186c: add             x1, x1, HEAP, lsl #32
    // 0xbb1870: tbnz            w1, #4, #0xbb18e4
    // 0xbb1874: LoadField: r1 = r2->field_3b
    //     0xbb1874: ldur            w1, [x2, #0x3b]
    // 0xbb1878: DecompressPointer r1
    //     0xbb1878: add             x1, x1, HEAP, lsl #32
    // 0xbb187c: LoadField: r3 = r1->field_27
    //     0xbb187c: ldur            w3, [x1, #0x27]
    // 0xbb1880: DecompressPointer r3
    //     0xbb1880: add             x3, x3, HEAP, lsl #32
    // 0xbb1884: LoadField: r1 = r3->field_7
    //     0xbb1884: ldur            w1, [x3, #7]
    // 0xbb1888: DecompressPointer r1
    //     0xbb1888: add             x1, x1, HEAP, lsl #32
    // 0xbb188c: LoadField: r3 = r1->field_7
    //     0xbb188c: ldur            w3, [x1, #7]
    // 0xbb1890: cbz             w3, #0xbb18a0
    // 0xbb1894: r1 = Instance_IconData
    //     0xbb1894: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbb1898: ldr             x1, [x1, #0x130]
    // 0xbb189c: b               #0xbb18a8
    // 0xbb18a0: r1 = Instance_IconData
    //     0xbb18a0: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbb18a4: ldr             x1, [x1, #0x138]
    // 0xbb18a8: stur            x1, [fp, #-0x70]
    // 0xbb18ac: cbz             w3, #0xbb18bc
    // 0xbb18b0: r3 = Instance_Color
    //     0xbb18b0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb18b4: ldr             x3, [x3, #0x858]
    // 0xbb18b8: b               #0xbb18c4
    // 0xbb18bc: r3 = Instance_Color
    //     0xbb18bc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbb18c0: ldr             x3, [x3, #0x50]
    // 0xbb18c4: stur            x3, [fp, #-0x68]
    // 0xbb18c8: r0 = Icon()
    //     0xbb18c8: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbb18cc: mov             x1, x0
    // 0xbb18d0: ldur            x0, [fp, #-0x70]
    // 0xbb18d4: StoreField: r1->field_b = r0
    //     0xbb18d4: stur            w0, [x1, #0xb]
    // 0xbb18d8: ldur            x0, [fp, #-0x68]
    // 0xbb18dc: StoreField: r1->field_23 = r0
    //     0xbb18dc: stur            w0, [x1, #0x23]
    // 0xbb18e0: b               #0xbb18e8
    // 0xbb18e4: r1 = Null
    //     0xbb18e4: mov             x1, NULL
    // 0xbb18e8: ldur            x2, [fp, #-8]
    // 0xbb18ec: ldur            x0, [fp, #-0x20]
    // 0xbb18f0: ldur            x16, [fp, #-0x58]
    // 0xbb18f4: r30 = "House No./ Building Name*"
    //     0xbb18f4: add             lr, PP, #0x54, lsl #12  ; [pp+0x54080] "House No./ Building Name*"
    //     0xbb18f8: ldr             lr, [lr, #0x80]
    // 0xbb18fc: stp             lr, x16, [SP, #0x20]
    // 0xbb1900: ldur            x16, [fp, #-0x60]
    // 0xbb1904: r30 = 4
    //     0xbb1904: movz            lr, #0x4
    // 0xbb1908: stp             lr, x16, [SP, #0x10]
    // 0xbb190c: ldur            x16, [fp, #-0x78]
    // 0xbb1910: stp             x1, x16, [SP]
    // 0xbb1914: ldur            x1, [fp, #-0x50]
    // 0xbb1918: r4 = const [0, 0x7, 0x6, 0x1, errorMaxLines, 0x4, errorStyle, 0x5, focusedBorder, 0x1, labelStyle, 0x3, labelText, 0x2, suffixIcon, 0x6, null]
    //     0xbb1918: add             x4, PP, #0x54, lsl #12  ; [pp+0x54088] List(17) [0, 0x7, 0x6, 0x1, "errorMaxLines", 0x4, "errorStyle", 0x5, "focusedBorder", 0x1, "labelStyle", 0x3, "labelText", 0x2, "suffixIcon", 0x6, Null]
    //     0xbb191c: ldr             x4, [x4, #0x88]
    // 0xbb1920: r0 = copyWith()
    //     0xbb1920: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbb1924: ldur            x2, [fp, #-8]
    // 0xbb1928: r1 = Function '_validateHouseNumber@1669306702':.
    //     0xbb1928: add             x1, PP, #0x54, lsl #12  ; [pp+0x54090] AnonymousClosure: (0xbb3b14), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateHouseNumber (0xa0594c)
    //     0xbb192c: ldr             x1, [x1, #0x90]
    // 0xbb1930: stur            x0, [fp, #-0x50]
    // 0xbb1934: r0 = AllocateClosure()
    //     0xbb1934: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb1938: ldur            x2, [fp, #-0x28]
    // 0xbb193c: r1 = Function '<anonymous closure>':.
    //     0xbb193c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54098] AnonymousClosure: (0xa022fc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xbb1940: ldr             x1, [x1, #0x98]
    // 0xbb1944: stur            x0, [fp, #-0x58]
    // 0xbb1948: r0 = AllocateClosure()
    //     0xbb1948: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb194c: r1 = <String>
    //     0xbb194c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbb1950: stur            x0, [fp, #-0x60]
    // 0xbb1954: r0 = TextFormField()
    //     0xbb1954: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbb1958: stur            x0, [fp, #-0x68]
    // 0xbb195c: ldur            x16, [fp, #-0x58]
    // 0xbb1960: r30 = Instance_AutovalidateMode
    //     0xbb1960: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbb1964: ldr             lr, [lr, #0x7e8]
    // 0xbb1968: stp             lr, x16, [SP, #0x40]
    // 0xbb196c: r16 = true
    //     0xbb196c: add             x16, NULL, #0x20  ; true
    // 0xbb1970: ldur            lr, [fp, #-0x18]
    // 0xbb1974: stp             lr, x16, [SP, #0x30]
    // 0xbb1978: ldur            x16, [fp, #-0x40]
    // 0xbb197c: r30 = Instance_TextInputType
    //     0xbb197c: add             lr, PP, #0x33, lsl #12  ; [pp+0x33bb8] Obj!TextInputType@d55b41
    //     0xbb1980: ldr             lr, [lr, #0xbb8]
    // 0xbb1984: stp             lr, x16, [SP, #0x20]
    // 0xbb1988: ldur            x16, [fp, #-0x38]
    // 0xbb198c: r30 = 2
    //     0xbb198c: movz            lr, #0x2
    // 0xbb1990: stp             lr, x16, [SP, #0x10]
    // 0xbb1994: ldur            x16, [fp, #-0x48]
    // 0xbb1998: ldur            lr, [fp, #-0x60]
    // 0xbb199c: stp             lr, x16, [SP]
    // 0xbb19a0: mov             x1, x0
    // 0xbb19a4: ldur            x2, [fp, #-0x50]
    // 0xbb19a8: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x5, autovalidateMode, 0x3, controller, 0x8, enableSuggestions, 0x4, inputFormatters, 0x6, keyboardType, 0x7, maxLines, 0x9, onChanged, 0xb, style, 0xa, validator, 0x2, null]
    //     0xbb19a8: add             x4, PP, #0x54, lsl #12  ; [pp+0x540a0] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x5, "autovalidateMode", 0x3, "controller", 0x8, "enableSuggestions", 0x4, "inputFormatters", 0x6, "keyboardType", 0x7, "maxLines", 0x9, "onChanged", 0xb, "style", 0xa, "validator", 0x2, Null]
    //     0xbb19ac: ldr             x4, [x4, #0xa0]
    // 0xbb19b0: r0 = TextFormField()
    //     0xbb19b0: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbb19b4: r0 = Form()
    //     0xbb19b4: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbb19b8: mov             x1, x0
    // 0xbb19bc: ldur            x0, [fp, #-0x68]
    // 0xbb19c0: stur            x1, [fp, #-0x18]
    // 0xbb19c4: StoreField: r1->field_b = r0
    //     0xbb19c4: stur            w0, [x1, #0xb]
    // 0xbb19c8: r0 = Instance_AutovalidateMode
    //     0xbb19c8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbb19cc: ldr             x0, [x0, #0x800]
    // 0xbb19d0: StoreField: r1->field_23 = r0
    //     0xbb19d0: stur            w0, [x1, #0x23]
    // 0xbb19d4: ldur            x2, [fp, #-0x20]
    // 0xbb19d8: StoreField: r1->field_7 = r2
    //     0xbb19d8: stur            w2, [x1, #7]
    // 0xbb19dc: r0 = Padding()
    //     0xbb19dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb19e0: mov             x4, x0
    // 0xbb19e4: r3 = Instance_EdgeInsets
    //     0xbb19e4: add             x3, PP, #0x42, lsl #12  ; [pp+0x42620] Obj!EdgeInsets@d57e01
    //     0xbb19e8: ldr             x3, [x3, #0x620]
    // 0xbb19ec: stur            x4, [fp, #-0x38]
    // 0xbb19f0: StoreField: r4->field_f = r3
    //     0xbb19f0: stur            w3, [x4, #0xf]
    // 0xbb19f4: ldur            x0, [fp, #-0x18]
    // 0xbb19f8: StoreField: r4->field_b = r0
    //     0xbb19f8: stur            w0, [x4, #0xb]
    // 0xbb19fc: ldur            x5, [fp, #-8]
    // 0xbb1a00: LoadField: r6 = r5->field_2f
    //     0xbb1a00: ldur            w6, [x5, #0x2f]
    // 0xbb1a04: DecompressPointer r6
    //     0xbb1a04: add             x6, x6, HEAP, lsl #32
    // 0xbb1a08: stur            x6, [fp, #-0x20]
    // 0xbb1a0c: LoadField: r2 = r5->field_b
    //     0xbb1a0c: ldur            w2, [x5, #0xb]
    // 0xbb1a10: DecompressPointer r2
    //     0xbb1a10: add             x2, x2, HEAP, lsl #32
    // 0xbb1a14: cmp             w2, NULL
    // 0xbb1a18: b.eq            #0xbb39ec
    // 0xbb1a1c: LoadField: r0 = r2->field_1b
    //     0xbb1a1c: ldur            w0, [x2, #0x1b]
    // 0xbb1a20: DecompressPointer r0
    //     0xbb1a20: add             x0, x0, HEAP, lsl #32
    // 0xbb1a24: LoadField: r7 = r0->field_1b
    //     0xbb1a24: ldur            w7, [x0, #0x1b]
    // 0xbb1a28: DecompressPointer r7
    //     0xbb1a28: add             x7, x7, HEAP, lsl #32
    // 0xbb1a2c: cmp             w7, NULL
    // 0xbb1a30: b.ne            #0xbb1a3c
    // 0xbb1a34: r0 = Null
    //     0xbb1a34: mov             x0, NULL
    // 0xbb1a38: b               #0xbb1a54
    // 0xbb1a3c: LoadField: r0 = r7->field_b
    //     0xbb1a3c: ldur            w0, [x7, #0xb]
    // 0xbb1a40: cbz             w0, #0xbb1a4c
    // 0xbb1a44: r1 = false
    //     0xbb1a44: add             x1, NULL, #0x30  ; false
    // 0xbb1a48: b               #0xbb1a50
    // 0xbb1a4c: r1 = true
    //     0xbb1a4c: add             x1, NULL, #0x20  ; true
    // 0xbb1a50: mov             x0, x1
    // 0xbb1a54: cmp             w0, NULL
    // 0xbb1a58: b.eq            #0xbb1a60
    // 0xbb1a5c: tbnz            w0, #4, #0xbb1a68
    // 0xbb1a60: r7 = true
    //     0xbb1a60: add             x7, NULL, #0x20  ; true
    // 0xbb1a64: b               #0xbb1aec
    // 0xbb1a68: cmp             w7, NULL
    // 0xbb1a6c: b.ne            #0xbb1a78
    // 0xbb1a70: r0 = Null
    //     0xbb1a70: mov             x0, NULL
    // 0xbb1a74: b               #0xbb1adc
    // 0xbb1a78: LoadField: r0 = r7->field_b
    //     0xbb1a78: ldur            w0, [x7, #0xb]
    // 0xbb1a7c: r1 = LoadInt32Instr(r0)
    //     0xbb1a7c: sbfx            x1, x0, #1, #0x1f
    // 0xbb1a80: mov             x0, x1
    // 0xbb1a84: r1 = 0
    //     0xbb1a84: movz            x1, #0
    // 0xbb1a88: cmp             x1, x0
    // 0xbb1a8c: b.hs            #0xbb39f0
    // 0xbb1a90: LoadField: r0 = r7->field_f
    //     0xbb1a90: ldur            w0, [x7, #0xf]
    // 0xbb1a94: DecompressPointer r0
    //     0xbb1a94: add             x0, x0, HEAP, lsl #32
    // 0xbb1a98: LoadField: r1 = r0->field_f
    //     0xbb1a98: ldur            w1, [x0, #0xf]
    // 0xbb1a9c: DecompressPointer r1
    //     0xbb1a9c: add             x1, x1, HEAP, lsl #32
    // 0xbb1aa0: cmp             w1, NULL
    // 0xbb1aa4: b.ne            #0xbb1ab0
    // 0xbb1aa8: r0 = Null
    //     0xbb1aa8: mov             x0, NULL
    // 0xbb1aac: b               #0xbb1adc
    // 0xbb1ab0: LoadField: r0 = r1->field_13
    //     0xbb1ab0: ldur            w0, [x1, #0x13]
    // 0xbb1ab4: DecompressPointer r0
    //     0xbb1ab4: add             x0, x0, HEAP, lsl #32
    // 0xbb1ab8: cmp             w0, NULL
    // 0xbb1abc: b.ne            #0xbb1ac8
    // 0xbb1ac0: r0 = Null
    //     0xbb1ac0: mov             x0, NULL
    // 0xbb1ac4: b               #0xbb1adc
    // 0xbb1ac8: LoadField: r1 = r0->field_7
    //     0xbb1ac8: ldur            w1, [x0, #7]
    // 0xbb1acc: cbz             w1, #0xbb1ad8
    // 0xbb1ad0: r0 = false
    //     0xbb1ad0: add             x0, NULL, #0x30  ; false
    // 0xbb1ad4: b               #0xbb1adc
    // 0xbb1ad8: r0 = true
    //     0xbb1ad8: add             x0, NULL, #0x20  ; true
    // 0xbb1adc: cmp             w0, NULL
    // 0xbb1ae0: b.ne            #0xbb1ae8
    // 0xbb1ae4: r0 = true
    //     0xbb1ae4: add             x0, NULL, #0x20  ; true
    // 0xbb1ae8: mov             x7, x0
    // 0xbb1aec: stur            x7, [fp, #-0x18]
    // 0xbb1af0: LoadField: r0 = r2->field_b
    //     0xbb1af0: ldur            w0, [x2, #0xb]
    // 0xbb1af4: DecompressPointer r0
    //     0xbb1af4: add             x0, x0, HEAP, lsl #32
    // 0xbb1af8: LoadField: r1 = r0->field_f
    //     0xbb1af8: ldur            w1, [x0, #0xf]
    // 0xbb1afc: DecompressPointer r1
    //     0xbb1afc: add             x1, x1, HEAP, lsl #32
    // 0xbb1b00: cmp             w1, NULL
    // 0xbb1b04: b.ne            #0xbb1b10
    // 0xbb1b08: r0 = Null
    //     0xbb1b08: mov             x0, NULL
    // 0xbb1b0c: b               #0xbb1b30
    // 0xbb1b10: r0 = LoadClassIdInstr(r1)
    //     0xbb1b10: ldur            x0, [x1, #-1]
    //     0xbb1b14: ubfx            x0, x0, #0xc, #0x14
    // 0xbb1b18: r2 = "landmark"
    //     0xbb1b18: add             x2, PP, #0x24, lsl #12  ; [pp+0x24930] "landmark"
    //     0xbb1b1c: ldr             x2, [x2, #0x930]
    // 0xbb1b20: r0 = GDT[cid_x0 + 0xe437]()
    //     0xbb1b20: movz            x17, #0xe437
    //     0xbb1b24: add             lr, x0, x17
    //     0xbb1b28: ldr             lr, [x21, lr, lsl #3]
    //     0xbb1b2c: blr             lr
    // 0xbb1b30: cmp             w0, NULL
    // 0xbb1b34: b.eq            #0xbb1b44
    // 0xbb1b38: tbnz            w0, #4, #0xbb1b44
    // 0xbb1b3c: r0 = 250
    //     0xbb1b3c: movz            x0, #0xfa
    // 0xbb1b40: b               #0xbb1b48
    // 0xbb1b44: r0 = 370
    //     0xbb1b44: movz            x0, #0x172
    // 0xbb1b48: ldur            x2, [fp, #-8]
    // 0xbb1b4c: lsl             x1, x0, #1
    // 0xbb1b50: stur            x1, [fp, #-0x40]
    // 0xbb1b54: r0 = LengthLimitingTextInputFormatter()
    //     0xbb1b54: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbb1b58: mov             x1, x0
    // 0xbb1b5c: ldur            x0, [fp, #-0x40]
    // 0xbb1b60: stur            x1, [fp, #-0x48]
    // 0xbb1b64: StoreField: r1->field_7 = r0
    //     0xbb1b64: stur            w0, [x1, #7]
    // 0xbb1b68: r16 = "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xbb1b68: add             x16, PP, #0x54, lsl #12  ; [pp+0x540a8] "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xbb1b6c: ldr             x16, [x16, #0xa8]
    // 0xbb1b70: stp             x16, NULL, [SP, #0x20]
    // 0xbb1b74: r16 = false
    //     0xbb1b74: add             x16, NULL, #0x30  ; false
    // 0xbb1b78: r30 = true
    //     0xbb1b78: add             lr, NULL, #0x20  ; true
    // 0xbb1b7c: stp             lr, x16, [SP, #0x10]
    // 0xbb1b80: r16 = false
    //     0xbb1b80: add             x16, NULL, #0x30  ; false
    // 0xbb1b84: r30 = false
    //     0xbb1b84: add             lr, NULL, #0x30  ; false
    // 0xbb1b88: stp             lr, x16, [SP]
    // 0xbb1b8c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xbb1b8c: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xbb1b90: r0 = _RegExp()
    //     0xbb1b90: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xbb1b94: stur            x0, [fp, #-0x40]
    // 0xbb1b98: r0 = FilteringTextInputFormatter()
    //     0xbb1b98: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xbb1b9c: mov             x3, x0
    // 0xbb1ba0: ldur            x0, [fp, #-0x40]
    // 0xbb1ba4: stur            x3, [fp, #-0x50]
    // 0xbb1ba8: StoreField: r3->field_b = r0
    //     0xbb1ba8: stur            w0, [x3, #0xb]
    // 0xbb1bac: r0 = true
    //     0xbb1bac: add             x0, NULL, #0x20  ; true
    // 0xbb1bb0: StoreField: r3->field_7 = r0
    //     0xbb1bb0: stur            w0, [x3, #7]
    // 0xbb1bb4: r4 = ""
    //     0xbb1bb4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb1bb8: StoreField: r3->field_f = r4
    //     0xbb1bb8: stur            w4, [x3, #0xf]
    // 0xbb1bbc: r1 = Null
    //     0xbb1bbc: mov             x1, NULL
    // 0xbb1bc0: r2 = 4
    //     0xbb1bc0: movz            x2, #0x4
    // 0xbb1bc4: r0 = AllocateArray()
    //     0xbb1bc4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb1bc8: mov             x2, x0
    // 0xbb1bcc: ldur            x0, [fp, #-0x48]
    // 0xbb1bd0: stur            x2, [fp, #-0x40]
    // 0xbb1bd4: StoreField: r2->field_f = r0
    //     0xbb1bd4: stur            w0, [x2, #0xf]
    // 0xbb1bd8: ldur            x0, [fp, #-0x50]
    // 0xbb1bdc: StoreField: r2->field_13 = r0
    //     0xbb1bdc: stur            w0, [x2, #0x13]
    // 0xbb1be0: r1 = <TextInputFormatter>
    //     0xbb1be0: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbb1be4: ldr             x1, [x1, #0x7b0]
    // 0xbb1be8: r0 = AllocateGrowableArray()
    //     0xbb1be8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb1bec: mov             x2, x0
    // 0xbb1bf0: ldur            x0, [fp, #-0x40]
    // 0xbb1bf4: stur            x2, [fp, #-0x48]
    // 0xbb1bf8: StoreField: r2->field_f = r0
    //     0xbb1bf8: stur            w0, [x2, #0xf]
    // 0xbb1bfc: r0 = 4
    //     0xbb1bfc: movz            x0, #0x4
    // 0xbb1c00: StoreField: r2->field_b = r0
    //     0xbb1c00: stur            w0, [x2, #0xb]
    // 0xbb1c04: ldur            x1, [fp, #-0x10]
    // 0xbb1c08: r0 = of()
    //     0xbb1c08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb1c0c: LoadField: r1 = r0->field_87
    //     0xbb1c0c: ldur            w1, [x0, #0x87]
    // 0xbb1c10: DecompressPointer r1
    //     0xbb1c10: add             x1, x1, HEAP, lsl #32
    // 0xbb1c14: LoadField: r0 = r1->field_2b
    //     0xbb1c14: ldur            w0, [x1, #0x2b]
    // 0xbb1c18: DecompressPointer r0
    //     0xbb1c18: add             x0, x0, HEAP, lsl #32
    // 0xbb1c1c: ldur            x1, [fp, #-0x10]
    // 0xbb1c20: stur            x0, [fp, #-0x40]
    // 0xbb1c24: r0 = of()
    //     0xbb1c24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb1c28: LoadField: r1 = r0->field_5b
    //     0xbb1c28: ldur            w1, [x0, #0x5b]
    // 0xbb1c2c: DecompressPointer r1
    //     0xbb1c2c: add             x1, x1, HEAP, lsl #32
    // 0xbb1c30: r16 = 14.000000
    //     0xbb1c30: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb1c34: ldr             x16, [x16, #0x1d8]
    // 0xbb1c38: stp             x1, x16, [SP]
    // 0xbb1c3c: ldur            x1, [fp, #-0x40]
    // 0xbb1c40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb1c40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb1c44: ldr             x4, [x4, #0xaa0]
    // 0xbb1c48: r0 = copyWith()
    //     0xbb1c48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb1c4c: ldur            x2, [fp, #-8]
    // 0xbb1c50: stur            x0, [fp, #-0x50]
    // 0xbb1c54: LoadField: r3 = r2->field_47
    //     0xbb1c54: ldur            w3, [x2, #0x47]
    // 0xbb1c58: DecompressPointer r3
    //     0xbb1c58: add             x3, x3, HEAP, lsl #32
    // 0xbb1c5c: ldur            x1, [fp, #-0x10]
    // 0xbb1c60: stur            x3, [fp, #-0x40]
    // 0xbb1c64: r0 = getTextFormFieldInputDecoration()
    //     0xbb1c64: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbb1c68: ldur            x1, [fp, #-0x10]
    // 0xbb1c6c: stur            x0, [fp, #-0x58]
    // 0xbb1c70: r0 = of()
    //     0xbb1c70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb1c74: LoadField: r1 = r0->field_5b
    //     0xbb1c74: ldur            w1, [x0, #0x5b]
    // 0xbb1c78: DecompressPointer r1
    //     0xbb1c78: add             x1, x1, HEAP, lsl #32
    // 0xbb1c7c: stur            x1, [fp, #-0x60]
    // 0xbb1c80: r0 = BorderSide()
    //     0xbb1c80: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbb1c84: mov             x1, x0
    // 0xbb1c88: ldur            x0, [fp, #-0x60]
    // 0xbb1c8c: stur            x1, [fp, #-0x68]
    // 0xbb1c90: StoreField: r1->field_7 = r0
    //     0xbb1c90: stur            w0, [x1, #7]
    // 0xbb1c94: d0 = 1.000000
    //     0xbb1c94: fmov            d0, #1.00000000
    // 0xbb1c98: StoreField: r1->field_b = d0
    //     0xbb1c98: stur            d0, [x1, #0xb]
    // 0xbb1c9c: r0 = Instance_BorderStyle
    //     0xbb1c9c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbb1ca0: ldr             x0, [x0, #0xf68]
    // 0xbb1ca4: StoreField: r1->field_13 = r0
    //     0xbb1ca4: stur            w0, [x1, #0x13]
    // 0xbb1ca8: d1 = -1.000000
    //     0xbb1ca8: fmov            d1, #-1.00000000
    // 0xbb1cac: ArrayStore: r1[0] = d1  ; List_8
    //     0xbb1cac: stur            d1, [x1, #0x17]
    // 0xbb1cb0: r0 = OutlineInputBorder()
    //     0xbb1cb0: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbb1cb4: mov             x2, x0
    // 0xbb1cb8: r0 = Instance_BorderRadius
    //     0xbb1cb8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbb1cbc: ldr             x0, [x0, #0xf70]
    // 0xbb1cc0: stur            x2, [fp, #-0x60]
    // 0xbb1cc4: StoreField: r2->field_13 = r0
    //     0xbb1cc4: stur            w0, [x2, #0x13]
    // 0xbb1cc8: d0 = 4.000000
    //     0xbb1cc8: fmov            d0, #4.00000000
    // 0xbb1ccc: StoreField: r2->field_b = d0
    //     0xbb1ccc: stur            d0, [x2, #0xb]
    // 0xbb1cd0: ldur            x1, [fp, #-0x68]
    // 0xbb1cd4: StoreField: r2->field_7 = r1
    //     0xbb1cd4: stur            w1, [x2, #7]
    // 0xbb1cd8: ldur            x1, [fp, #-0x10]
    // 0xbb1cdc: r0 = of()
    //     0xbb1cdc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb1ce0: LoadField: r1 = r0->field_87
    //     0xbb1ce0: ldur            w1, [x0, #0x87]
    // 0xbb1ce4: DecompressPointer r1
    //     0xbb1ce4: add             x1, x1, HEAP, lsl #32
    // 0xbb1ce8: LoadField: r0 = r1->field_2b
    //     0xbb1ce8: ldur            w0, [x1, #0x2b]
    // 0xbb1cec: DecompressPointer r0
    //     0xbb1cec: add             x0, x0, HEAP, lsl #32
    // 0xbb1cf0: stur            x0, [fp, #-0x68]
    // 0xbb1cf4: r1 = Instance_Color
    //     0xbb1cf4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb1cf8: d0 = 0.400000
    //     0xbb1cf8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbb1cfc: r0 = withOpacity()
    //     0xbb1cfc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb1d00: r16 = 12.000000
    //     0xbb1d00: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb1d04: ldr             x16, [x16, #0x9e8]
    // 0xbb1d08: stp             x0, x16, [SP]
    // 0xbb1d0c: ldur            x1, [fp, #-0x68]
    // 0xbb1d10: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb1d10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb1d14: ldr             x4, [x4, #0xaa0]
    // 0xbb1d18: r0 = copyWith()
    //     0xbb1d18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb1d1c: ldur            x1, [fp, #-0x10]
    // 0xbb1d20: stur            x0, [fp, #-0x68]
    // 0xbb1d24: r0 = of()
    //     0xbb1d24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb1d28: LoadField: r1 = r0->field_87
    //     0xbb1d28: ldur            w1, [x0, #0x87]
    // 0xbb1d2c: DecompressPointer r1
    //     0xbb1d2c: add             x1, x1, HEAP, lsl #32
    // 0xbb1d30: LoadField: r0 = r1->field_2b
    //     0xbb1d30: ldur            w0, [x1, #0x2b]
    // 0xbb1d34: DecompressPointer r0
    //     0xbb1d34: add             x0, x0, HEAP, lsl #32
    // 0xbb1d38: r16 = 12.000000
    //     0xbb1d38: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb1d3c: ldr             x16, [x16, #0x9e8]
    // 0xbb1d40: r30 = Instance_Color
    //     0xbb1d40: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbb1d44: ldr             lr, [lr, #0x50]
    // 0xbb1d48: stp             lr, x16, [SP]
    // 0xbb1d4c: mov             x1, x0
    // 0xbb1d50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb1d50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb1d54: ldr             x4, [x4, #0xaa0]
    // 0xbb1d58: r0 = copyWith()
    //     0xbb1d58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb1d5c: mov             x1, x0
    // 0xbb1d60: ldur            x2, [fp, #-8]
    // 0xbb1d64: stur            x1, [fp, #-0x70]
    // 0xbb1d68: LoadField: r0 = r2->field_5b
    //     0xbb1d68: ldur            w0, [x2, #0x5b]
    // 0xbb1d6c: DecompressPointer r0
    //     0xbb1d6c: add             x0, x0, HEAP, lsl #32
    // 0xbb1d70: tbnz            w0, #4, #0xbb1e9c
    // 0xbb1d74: LoadField: r0 = r2->field_47
    //     0xbb1d74: ldur            w0, [x2, #0x47]
    // 0xbb1d78: DecompressPointer r0
    //     0xbb1d78: add             x0, x0, HEAP, lsl #32
    // 0xbb1d7c: LoadField: r3 = r0->field_27
    //     0xbb1d7c: ldur            w3, [x0, #0x27]
    // 0xbb1d80: DecompressPointer r3
    //     0xbb1d80: add             x3, x3, HEAP, lsl #32
    // 0xbb1d84: LoadField: r0 = r3->field_7
    //     0xbb1d84: ldur            w0, [x3, #7]
    // 0xbb1d88: DecompressPointer r0
    //     0xbb1d88: add             x0, x0, HEAP, lsl #32
    // 0xbb1d8c: r3 = LoadClassIdInstr(r0)
    //     0xbb1d8c: ldur            x3, [x0, #-1]
    //     0xbb1d90: ubfx            x3, x3, #0xc, #0x14
    // 0xbb1d94: r16 = ""
    //     0xbb1d94: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb1d98: stp             x16, x0, [SP]
    // 0xbb1d9c: mov             x0, x3
    // 0xbb1da0: mov             lr, x0
    // 0xbb1da4: ldr             lr, [x21, lr, lsl #3]
    // 0xbb1da8: blr             lr
    // 0xbb1dac: tbz             w0, #4, #0xbb1de8
    // 0xbb1db0: ldur            x2, [fp, #-8]
    // 0xbb1db4: LoadField: r0 = r2->field_47
    //     0xbb1db4: ldur            w0, [x2, #0x47]
    // 0xbb1db8: DecompressPointer r0
    //     0xbb1db8: add             x0, x0, HEAP, lsl #32
    // 0xbb1dbc: LoadField: r1 = r0->field_27
    //     0xbb1dbc: ldur            w1, [x0, #0x27]
    // 0xbb1dc0: DecompressPointer r1
    //     0xbb1dc0: add             x1, x1, HEAP, lsl #32
    // 0xbb1dc4: LoadField: r0 = r1->field_7
    //     0xbb1dc4: ldur            w0, [x1, #7]
    // 0xbb1dc8: DecompressPointer r0
    //     0xbb1dc8: add             x0, x0, HEAP, lsl #32
    // 0xbb1dcc: LoadField: r1 = r0->field_7
    //     0xbb1dcc: ldur            w1, [x0, #7]
    // 0xbb1dd0: r0 = LoadInt32Instr(r1)
    //     0xbb1dd0: sbfx            x0, x1, #1, #0x1f
    // 0xbb1dd4: cmp             x0, #0x14
    // 0xbb1dd8: b.lt            #0xbb1dec
    // 0xbb1ddc: r1 = Instance_IconData
    //     0xbb1ddc: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbb1de0: ldr             x1, [x1, #0x130]
    // 0xbb1de4: b               #0xbb1df4
    // 0xbb1de8: ldur            x2, [fp, #-8]
    // 0xbb1dec: r1 = Instance_IconData
    //     0xbb1dec: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbb1df0: ldr             x1, [x1, #0x138]
    // 0xbb1df4: stur            x1, [fp, #-0x78]
    // 0xbb1df8: LoadField: r0 = r2->field_47
    //     0xbb1df8: ldur            w0, [x2, #0x47]
    // 0xbb1dfc: DecompressPointer r0
    //     0xbb1dfc: add             x0, x0, HEAP, lsl #32
    // 0xbb1e00: LoadField: r3 = r0->field_27
    //     0xbb1e00: ldur            w3, [x0, #0x27]
    // 0xbb1e04: DecompressPointer r3
    //     0xbb1e04: add             x3, x3, HEAP, lsl #32
    // 0xbb1e08: LoadField: r0 = r3->field_7
    //     0xbb1e08: ldur            w0, [x3, #7]
    // 0xbb1e0c: DecompressPointer r0
    //     0xbb1e0c: add             x0, x0, HEAP, lsl #32
    // 0xbb1e10: r3 = LoadClassIdInstr(r0)
    //     0xbb1e10: ldur            x3, [x0, #-1]
    //     0xbb1e14: ubfx            x3, x3, #0xc, #0x14
    // 0xbb1e18: r16 = ""
    //     0xbb1e18: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb1e1c: stp             x16, x0, [SP]
    // 0xbb1e20: mov             x0, x3
    // 0xbb1e24: mov             lr, x0
    // 0xbb1e28: ldr             lr, [x21, lr, lsl #3]
    // 0xbb1e2c: blr             lr
    // 0xbb1e30: tbz             w0, #4, #0xbb1e6c
    // 0xbb1e34: ldur            x2, [fp, #-8]
    // 0xbb1e38: LoadField: r0 = r2->field_47
    //     0xbb1e38: ldur            w0, [x2, #0x47]
    // 0xbb1e3c: DecompressPointer r0
    //     0xbb1e3c: add             x0, x0, HEAP, lsl #32
    // 0xbb1e40: LoadField: r1 = r0->field_27
    //     0xbb1e40: ldur            w1, [x0, #0x27]
    // 0xbb1e44: DecompressPointer r1
    //     0xbb1e44: add             x1, x1, HEAP, lsl #32
    // 0xbb1e48: LoadField: r0 = r1->field_7
    //     0xbb1e48: ldur            w0, [x1, #7]
    // 0xbb1e4c: DecompressPointer r0
    //     0xbb1e4c: add             x0, x0, HEAP, lsl #32
    // 0xbb1e50: LoadField: r1 = r0->field_7
    //     0xbb1e50: ldur            w1, [x0, #7]
    // 0xbb1e54: r0 = LoadInt32Instr(r1)
    //     0xbb1e54: sbfx            x0, x1, #1, #0x1f
    // 0xbb1e58: cmp             x0, #0x14
    // 0xbb1e5c: b.lt            #0xbb1e70
    // 0xbb1e60: r1 = Instance_Color
    //     0xbb1e60: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb1e64: ldr             x1, [x1, #0x858]
    // 0xbb1e68: b               #0xbb1e78
    // 0xbb1e6c: ldur            x2, [fp, #-8]
    // 0xbb1e70: r1 = Instance_Color
    //     0xbb1e70: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbb1e74: ldr             x1, [x1, #0x50]
    // 0xbb1e78: ldur            x0, [fp, #-0x78]
    // 0xbb1e7c: stur            x1, [fp, #-0x80]
    // 0xbb1e80: r0 = Icon()
    //     0xbb1e80: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbb1e84: mov             x1, x0
    // 0xbb1e88: ldur            x0, [fp, #-0x78]
    // 0xbb1e8c: StoreField: r1->field_b = r0
    //     0xbb1e8c: stur            w0, [x1, #0xb]
    // 0xbb1e90: ldur            x0, [fp, #-0x80]
    // 0xbb1e94: StoreField: r1->field_23 = r0
    //     0xbb1e94: stur            w0, [x1, #0x23]
    // 0xbb1e98: b               #0xbb1ea0
    // 0xbb1e9c: r1 = Null
    //     0xbb1e9c: mov             x1, NULL
    // 0xbb1ea0: ldur            x2, [fp, #-8]
    // 0xbb1ea4: ldur            x0, [fp, #-0x20]
    // 0xbb1ea8: ldur            x16, [fp, #-0x60]
    // 0xbb1eac: r30 = "Road Name / Area / Colony*"
    //     0xbb1eac: add             lr, PP, #0x54, lsl #12  ; [pp+0x540b0] "Road Name / Area / Colony*"
    //     0xbb1eb0: ldr             lr, [lr, #0xb0]
    // 0xbb1eb4: stp             lr, x16, [SP, #0x18]
    // 0xbb1eb8: ldur            x16, [fp, #-0x68]
    // 0xbb1ebc: ldur            lr, [fp, #-0x70]
    // 0xbb1ec0: stp             lr, x16, [SP, #8]
    // 0xbb1ec4: str             x1, [SP]
    // 0xbb1ec8: ldur            x1, [fp, #-0x58]
    // 0xbb1ecc: r4 = const [0, 0x6, 0x5, 0x1, errorStyle, 0x4, focusedBorder, 0x1, labelStyle, 0x3, labelText, 0x2, suffixIcon, 0x5, null]
    //     0xbb1ecc: add             x4, PP, #0x54, lsl #12  ; [pp+0x540b8] List(15) [0, 0x6, 0x5, 0x1, "errorStyle", 0x4, "focusedBorder", 0x1, "labelStyle", 0x3, "labelText", 0x2, "suffixIcon", 0x5, Null]
    //     0xbb1ed0: ldr             x4, [x4, #0xb8]
    // 0xbb1ed4: r0 = copyWith()
    //     0xbb1ed4: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbb1ed8: ldur            x2, [fp, #-8]
    // 0xbb1edc: r1 = Function '_validateAddress@1669306702':.
    //     0xbb1edc: add             x1, PP, #0x54, lsl #12  ; [pp+0x540c0] AnonymousClosure: (0xbb3ad8), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateAddress (0xa023b0)
    //     0xbb1ee0: ldr             x1, [x1, #0xc0]
    // 0xbb1ee4: stur            x0, [fp, #-0x58]
    // 0xbb1ee8: r0 = AllocateClosure()
    //     0xbb1ee8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb1eec: ldur            x2, [fp, #-0x28]
    // 0xbb1ef0: r1 = Function '<anonymous closure>':.
    //     0xbb1ef0: add             x1, PP, #0x54, lsl #12  ; [pp+0x540c8] AnonymousClosure: (0xa02050), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xbb1ef4: ldr             x1, [x1, #0xc8]
    // 0xbb1ef8: stur            x0, [fp, #-0x60]
    // 0xbb1efc: r0 = AllocateClosure()
    //     0xbb1efc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb1f00: r1 = <String>
    //     0xbb1f00: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbb1f04: stur            x0, [fp, #-0x68]
    // 0xbb1f08: r0 = TextFormField()
    //     0xbb1f08: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbb1f0c: stur            x0, [fp, #-0x70]
    // 0xbb1f10: ldur            x16, [fp, #-0x60]
    // 0xbb1f14: r30 = true
    //     0xbb1f14: add             lr, NULL, #0x20  ; true
    // 0xbb1f18: stp             lr, x16, [SP, #0x48]
    // 0xbb1f1c: ldur            x16, [fp, #-0x18]
    // 0xbb1f20: r30 = Instance_AutovalidateMode
    //     0xbb1f20: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbb1f24: ldr             lr, [lr, #0x7e8]
    // 0xbb1f28: stp             lr, x16, [SP, #0x38]
    // 0xbb1f2c: ldur            x16, [fp, #-0x48]
    // 0xbb1f30: r30 = Instance_TextInputType
    //     0xbb1f30: add             lr, PP, #0x33, lsl #12  ; [pp+0x33bb8] Obj!TextInputType@d55b41
    //     0xbb1f34: ldr             lr, [lr, #0xbb8]
    // 0xbb1f38: stp             lr, x16, [SP, #0x28]
    // 0xbb1f3c: r16 = 2
    //     0xbb1f3c: movz            x16, #0x2
    // 0xbb1f40: r30 = 6
    //     0xbb1f40: movz            lr, #0x6
    // 0xbb1f44: stp             lr, x16, [SP, #0x18]
    // 0xbb1f48: ldur            x16, [fp, #-0x50]
    // 0xbb1f4c: ldur            lr, [fp, #-0x40]
    // 0xbb1f50: stp             lr, x16, [SP, #8]
    // 0xbb1f54: ldur            x16, [fp, #-0x68]
    // 0xbb1f58: str             x16, [SP]
    // 0xbb1f5c: mov             x1, x0
    // 0xbb1f60: ldur            x2, [fp, #-0x58]
    // 0xbb1f64: r4 = const [0, 0xd, 0xb, 0x2, autofocus, 0x4, autovalidateMode, 0x5, controller, 0xb, enableSuggestions, 0x3, inputFormatters, 0x6, keyboardType, 0x7, maxLines, 0x9, minLines, 0x8, onChanged, 0xc, style, 0xa, validator, 0x2, null]
    //     0xbb1f64: add             x4, PP, #0x54, lsl #12  ; [pp+0x540d0] List(27) [0, 0xd, 0xb, 0x2, "autofocus", 0x4, "autovalidateMode", 0x5, "controller", 0xb, "enableSuggestions", 0x3, "inputFormatters", 0x6, "keyboardType", 0x7, "maxLines", 0x9, "minLines", 0x8, "onChanged", 0xc, "style", 0xa, "validator", 0x2, Null]
    //     0xbb1f68: ldr             x4, [x4, #0xd0]
    // 0xbb1f6c: r0 = TextFormField()
    //     0xbb1f6c: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbb1f70: r0 = Form()
    //     0xbb1f70: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbb1f74: mov             x1, x0
    // 0xbb1f78: ldur            x0, [fp, #-0x70]
    // 0xbb1f7c: stur            x1, [fp, #-0x18]
    // 0xbb1f80: StoreField: r1->field_b = r0
    //     0xbb1f80: stur            w0, [x1, #0xb]
    // 0xbb1f84: r0 = Instance_AutovalidateMode
    //     0xbb1f84: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbb1f88: ldr             x0, [x0, #0x800]
    // 0xbb1f8c: StoreField: r1->field_23 = r0
    //     0xbb1f8c: stur            w0, [x1, #0x23]
    // 0xbb1f90: ldur            x2, [fp, #-0x20]
    // 0xbb1f94: StoreField: r1->field_7 = r2
    //     0xbb1f94: stur            w2, [x1, #7]
    // 0xbb1f98: r0 = Padding()
    //     0xbb1f98: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb1f9c: mov             x3, x0
    // 0xbb1fa0: r2 = Instance_EdgeInsets
    //     0xbb1fa0: add             x2, PP, #0x42, lsl #12  ; [pp+0x42620] Obj!EdgeInsets@d57e01
    //     0xbb1fa4: ldr             x2, [x2, #0x620]
    // 0xbb1fa8: stur            x3, [fp, #-0x40]
    // 0xbb1fac: StoreField: r3->field_f = r2
    //     0xbb1fac: stur            w2, [x3, #0xf]
    // 0xbb1fb0: ldur            x0, [fp, #-0x18]
    // 0xbb1fb4: StoreField: r3->field_b = r0
    //     0xbb1fb4: stur            w0, [x3, #0xb]
    // 0xbb1fb8: ldur            x4, [fp, #-8]
    // 0xbb1fbc: LoadField: r5 = r4->field_1f
    //     0xbb1fbc: ldur            w5, [x4, #0x1f]
    // 0xbb1fc0: DecompressPointer r5
    //     0xbb1fc0: add             x5, x5, HEAP, lsl #32
    // 0xbb1fc4: stur            x5, [fp, #-0x20]
    // 0xbb1fc8: LoadField: r0 = r4->field_b
    //     0xbb1fc8: ldur            w0, [x4, #0xb]
    // 0xbb1fcc: DecompressPointer r0
    //     0xbb1fcc: add             x0, x0, HEAP, lsl #32
    // 0xbb1fd0: cmp             w0, NULL
    // 0xbb1fd4: b.eq            #0xbb39f4
    // 0xbb1fd8: LoadField: r1 = r0->field_1b
    //     0xbb1fd8: ldur            w1, [x0, #0x1b]
    // 0xbb1fdc: DecompressPointer r1
    //     0xbb1fdc: add             x1, x1, HEAP, lsl #32
    // 0xbb1fe0: LoadField: r6 = r1->field_1b
    //     0xbb1fe0: ldur            w6, [x1, #0x1b]
    // 0xbb1fe4: DecompressPointer r6
    //     0xbb1fe4: add             x6, x6, HEAP, lsl #32
    // 0xbb1fe8: cmp             w6, NULL
    // 0xbb1fec: b.ne            #0xbb1ff8
    // 0xbb1ff0: r0 = Null
    //     0xbb1ff0: mov             x0, NULL
    // 0xbb1ff4: b               #0xbb2010
    // 0xbb1ff8: LoadField: r0 = r6->field_b
    //     0xbb1ff8: ldur            w0, [x6, #0xb]
    // 0xbb1ffc: cbz             w0, #0xbb2008
    // 0xbb2000: r1 = false
    //     0xbb2000: add             x1, NULL, #0x30  ; false
    // 0xbb2004: b               #0xbb200c
    // 0xbb2008: r1 = true
    //     0xbb2008: add             x1, NULL, #0x20  ; true
    // 0xbb200c: mov             x0, x1
    // 0xbb2010: cmp             w0, NULL
    // 0xbb2014: b.eq            #0xbb201c
    // 0xbb2018: tbnz            w0, #4, #0xbb2024
    // 0xbb201c: r0 = true
    //     0xbb201c: add             x0, NULL, #0x20  ; true
    // 0xbb2020: b               #0xbb20a4
    // 0xbb2024: cmp             w6, NULL
    // 0xbb2028: b.ne            #0xbb2034
    // 0xbb202c: r0 = Null
    //     0xbb202c: mov             x0, NULL
    // 0xbb2030: b               #0xbb2098
    // 0xbb2034: LoadField: r0 = r6->field_b
    //     0xbb2034: ldur            w0, [x6, #0xb]
    // 0xbb2038: r1 = LoadInt32Instr(r0)
    //     0xbb2038: sbfx            x1, x0, #1, #0x1f
    // 0xbb203c: mov             x0, x1
    // 0xbb2040: r1 = 0
    //     0xbb2040: movz            x1, #0
    // 0xbb2044: cmp             x1, x0
    // 0xbb2048: b.hs            #0xbb39f8
    // 0xbb204c: LoadField: r0 = r6->field_f
    //     0xbb204c: ldur            w0, [x6, #0xf]
    // 0xbb2050: DecompressPointer r0
    //     0xbb2050: add             x0, x0, HEAP, lsl #32
    // 0xbb2054: LoadField: r1 = r0->field_f
    //     0xbb2054: ldur            w1, [x0, #0xf]
    // 0xbb2058: DecompressPointer r1
    //     0xbb2058: add             x1, x1, HEAP, lsl #32
    // 0xbb205c: cmp             w1, NULL
    // 0xbb2060: b.ne            #0xbb206c
    // 0xbb2064: r0 = Null
    //     0xbb2064: mov             x0, NULL
    // 0xbb2068: b               #0xbb2098
    // 0xbb206c: LoadField: r0 = r1->field_1b
    //     0xbb206c: ldur            w0, [x1, #0x1b]
    // 0xbb2070: DecompressPointer r0
    //     0xbb2070: add             x0, x0, HEAP, lsl #32
    // 0xbb2074: cmp             w0, NULL
    // 0xbb2078: b.ne            #0xbb2084
    // 0xbb207c: r0 = Null
    //     0xbb207c: mov             x0, NULL
    // 0xbb2080: b               #0xbb2098
    // 0xbb2084: LoadField: r1 = r0->field_7
    //     0xbb2084: ldur            w1, [x0, #7]
    // 0xbb2088: cbz             w1, #0xbb2094
    // 0xbb208c: r0 = false
    //     0xbb208c: add             x0, NULL, #0x30  ; false
    // 0xbb2090: b               #0xbb2098
    // 0xbb2094: r0 = true
    //     0xbb2094: add             x0, NULL, #0x20  ; true
    // 0xbb2098: cmp             w0, NULL
    // 0xbb209c: b.ne            #0xbb20a4
    // 0xbb20a0: r0 = true
    //     0xbb20a0: add             x0, NULL, #0x20  ; true
    // 0xbb20a4: stur            x0, [fp, #-0x18]
    // 0xbb20a8: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xbb20a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbb20ac: ldr             x0, [x0, #0x1530]
    //     0xbb20b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbb20b4: cmp             w0, w16
    //     0xbb20b8: b.ne            #0xbb20c8
    //     0xbb20bc: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0xbb20c0: ldr             x2, [x2, #0x120]
    //     0xbb20c4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbb20c8: stur            x0, [fp, #-0x48]
    // 0xbb20cc: r16 = "[0-9]"
    //     0xbb20cc: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0xbb20d0: ldr             x16, [x16, #0x128]
    // 0xbb20d4: stp             x16, NULL, [SP, #0x20]
    // 0xbb20d8: r16 = false
    //     0xbb20d8: add             x16, NULL, #0x30  ; false
    // 0xbb20dc: r30 = true
    //     0xbb20dc: add             lr, NULL, #0x20  ; true
    // 0xbb20e0: stp             lr, x16, [SP, #0x10]
    // 0xbb20e4: r16 = false
    //     0xbb20e4: add             x16, NULL, #0x30  ; false
    // 0xbb20e8: r30 = false
    //     0xbb20e8: add             lr, NULL, #0x30  ; false
    // 0xbb20ec: stp             lr, x16, [SP]
    // 0xbb20f0: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xbb20f0: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xbb20f4: r0 = _RegExp()
    //     0xbb20f4: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xbb20f8: stur            x0, [fp, #-0x50]
    // 0xbb20fc: r0 = FilteringTextInputFormatter()
    //     0xbb20fc: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xbb2100: mov             x1, x0
    // 0xbb2104: ldur            x0, [fp, #-0x50]
    // 0xbb2108: stur            x1, [fp, #-0x58]
    // 0xbb210c: StoreField: r1->field_b = r0
    //     0xbb210c: stur            w0, [x1, #0xb]
    // 0xbb2110: r0 = true
    //     0xbb2110: add             x0, NULL, #0x20  ; true
    // 0xbb2114: StoreField: r1->field_7 = r0
    //     0xbb2114: stur            w0, [x1, #7]
    // 0xbb2118: r2 = ""
    //     0xbb2118: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb211c: StoreField: r1->field_f = r2
    //     0xbb211c: stur            w2, [x1, #0xf]
    // 0xbb2120: r0 = LengthLimitingTextInputFormatter()
    //     0xbb2120: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbb2124: mov             x3, x0
    // 0xbb2128: r0 = 12
    //     0xbb2128: movz            x0, #0xc
    // 0xbb212c: stur            x3, [fp, #-0x50]
    // 0xbb2130: StoreField: r3->field_7 = r0
    //     0xbb2130: stur            w0, [x3, #7]
    // 0xbb2134: r1 = Null
    //     0xbb2134: mov             x1, NULL
    // 0xbb2138: r2 = 6
    //     0xbb2138: movz            x2, #0x6
    // 0xbb213c: r0 = AllocateArray()
    //     0xbb213c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb2140: mov             x2, x0
    // 0xbb2144: ldur            x0, [fp, #-0x48]
    // 0xbb2148: stur            x2, [fp, #-0x60]
    // 0xbb214c: StoreField: r2->field_f = r0
    //     0xbb214c: stur            w0, [x2, #0xf]
    // 0xbb2150: ldur            x1, [fp, #-0x58]
    // 0xbb2154: StoreField: r2->field_13 = r1
    //     0xbb2154: stur            w1, [x2, #0x13]
    // 0xbb2158: ldur            x1, [fp, #-0x50]
    // 0xbb215c: ArrayStore: r2[0] = r1  ; List_4
    //     0xbb215c: stur            w1, [x2, #0x17]
    // 0xbb2160: r1 = <TextInputFormatter>
    //     0xbb2160: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbb2164: ldr             x1, [x1, #0x7b0]
    // 0xbb2168: r0 = AllocateGrowableArray()
    //     0xbb2168: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb216c: mov             x2, x0
    // 0xbb2170: ldur            x0, [fp, #-0x60]
    // 0xbb2174: stur            x2, [fp, #-0x58]
    // 0xbb2178: StoreField: r2->field_f = r0
    //     0xbb2178: stur            w0, [x2, #0xf]
    // 0xbb217c: r0 = 6
    //     0xbb217c: movz            x0, #0x6
    // 0xbb2180: StoreField: r2->field_b = r0
    //     0xbb2180: stur            w0, [x2, #0xb]
    // 0xbb2184: ldur            x3, [fp, #-8]
    // 0xbb2188: LoadField: r4 = r3->field_3f
    //     0xbb2188: ldur            w4, [x3, #0x3f]
    // 0xbb218c: DecompressPointer r4
    //     0xbb218c: add             x4, x4, HEAP, lsl #32
    // 0xbb2190: ldur            x1, [fp, #-0x10]
    // 0xbb2194: stur            x4, [fp, #-0x50]
    // 0xbb2198: r0 = of()
    //     0xbb2198: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb219c: LoadField: r1 = r0->field_87
    //     0xbb219c: ldur            w1, [x0, #0x87]
    // 0xbb21a0: DecompressPointer r1
    //     0xbb21a0: add             x1, x1, HEAP, lsl #32
    // 0xbb21a4: LoadField: r0 = r1->field_2b
    //     0xbb21a4: ldur            w0, [x1, #0x2b]
    // 0xbb21a8: DecompressPointer r0
    //     0xbb21a8: add             x0, x0, HEAP, lsl #32
    // 0xbb21ac: ldur            x1, [fp, #-0x10]
    // 0xbb21b0: stur            x0, [fp, #-0x60]
    // 0xbb21b4: r0 = of()
    //     0xbb21b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb21b8: LoadField: r1 = r0->field_5b
    //     0xbb21b8: ldur            w1, [x0, #0x5b]
    // 0xbb21bc: DecompressPointer r1
    //     0xbb21bc: add             x1, x1, HEAP, lsl #32
    // 0xbb21c0: r16 = 14.000000
    //     0xbb21c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb21c4: ldr             x16, [x16, #0x1d8]
    // 0xbb21c8: stp             x1, x16, [SP]
    // 0xbb21cc: ldur            x1, [fp, #-0x60]
    // 0xbb21d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb21d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb21d4: ldr             x4, [x4, #0xaa0]
    // 0xbb21d8: r0 = copyWith()
    //     0xbb21d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb21dc: ldur            x1, [fp, #-0x10]
    // 0xbb21e0: stur            x0, [fp, #-0x60]
    // 0xbb21e4: r0 = getTextFormFieldInputDecoration()
    //     0xbb21e4: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbb21e8: ldur            x1, [fp, #-0x10]
    // 0xbb21ec: stur            x0, [fp, #-0x68]
    // 0xbb21f0: r0 = of()
    //     0xbb21f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb21f4: LoadField: r1 = r0->field_5b
    //     0xbb21f4: ldur            w1, [x0, #0x5b]
    // 0xbb21f8: DecompressPointer r1
    //     0xbb21f8: add             x1, x1, HEAP, lsl #32
    // 0xbb21fc: stur            x1, [fp, #-0x70]
    // 0xbb2200: r0 = BorderSide()
    //     0xbb2200: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbb2204: mov             x1, x0
    // 0xbb2208: ldur            x0, [fp, #-0x70]
    // 0xbb220c: stur            x1, [fp, #-0x78]
    // 0xbb2210: StoreField: r1->field_7 = r0
    //     0xbb2210: stur            w0, [x1, #7]
    // 0xbb2214: d0 = 1.000000
    //     0xbb2214: fmov            d0, #1.00000000
    // 0xbb2218: StoreField: r1->field_b = d0
    //     0xbb2218: stur            d0, [x1, #0xb]
    // 0xbb221c: r0 = Instance_BorderStyle
    //     0xbb221c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbb2220: ldr             x0, [x0, #0xf68]
    // 0xbb2224: StoreField: r1->field_13 = r0
    //     0xbb2224: stur            w0, [x1, #0x13]
    // 0xbb2228: d1 = -1.000000
    //     0xbb2228: fmov            d1, #-1.00000000
    // 0xbb222c: ArrayStore: r1[0] = d1  ; List_8
    //     0xbb222c: stur            d1, [x1, #0x17]
    // 0xbb2230: r0 = OutlineInputBorder()
    //     0xbb2230: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbb2234: mov             x2, x0
    // 0xbb2238: r0 = Instance_BorderRadius
    //     0xbb2238: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbb223c: ldr             x0, [x0, #0xf70]
    // 0xbb2240: stur            x2, [fp, #-0x70]
    // 0xbb2244: StoreField: r2->field_13 = r0
    //     0xbb2244: stur            w0, [x2, #0x13]
    // 0xbb2248: d0 = 4.000000
    //     0xbb2248: fmov            d0, #4.00000000
    // 0xbb224c: StoreField: r2->field_b = d0
    //     0xbb224c: stur            d0, [x2, #0xb]
    // 0xbb2250: ldur            x1, [fp, #-0x78]
    // 0xbb2254: StoreField: r2->field_7 = r1
    //     0xbb2254: stur            w1, [x2, #7]
    // 0xbb2258: ldur            x1, [fp, #-0x10]
    // 0xbb225c: r0 = of()
    //     0xbb225c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb2260: LoadField: r1 = r0->field_87
    //     0xbb2260: ldur            w1, [x0, #0x87]
    // 0xbb2264: DecompressPointer r1
    //     0xbb2264: add             x1, x1, HEAP, lsl #32
    // 0xbb2268: LoadField: r0 = r1->field_2b
    //     0xbb2268: ldur            w0, [x1, #0x2b]
    // 0xbb226c: DecompressPointer r0
    //     0xbb226c: add             x0, x0, HEAP, lsl #32
    // 0xbb2270: stur            x0, [fp, #-0x78]
    // 0xbb2274: r1 = Instance_Color
    //     0xbb2274: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb2278: d0 = 0.400000
    //     0xbb2278: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbb227c: r0 = withOpacity()
    //     0xbb227c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb2280: r16 = 14.000000
    //     0xbb2280: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb2284: ldr             x16, [x16, #0x1d8]
    // 0xbb2288: stp             x0, x16, [SP]
    // 0xbb228c: ldur            x1, [fp, #-0x78]
    // 0xbb2290: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb2290: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb2294: ldr             x4, [x4, #0xaa0]
    // 0xbb2298: r0 = copyWith()
    //     0xbb2298: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb229c: ldur            x1, [fp, #-0x10]
    // 0xbb22a0: stur            x0, [fp, #-0x78]
    // 0xbb22a4: r0 = of()
    //     0xbb22a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb22a8: LoadField: r1 = r0->field_87
    //     0xbb22a8: ldur            w1, [x0, #0x87]
    // 0xbb22ac: DecompressPointer r1
    //     0xbb22ac: add             x1, x1, HEAP, lsl #32
    // 0xbb22b0: LoadField: r0 = r1->field_2b
    //     0xbb22b0: ldur            w0, [x1, #0x2b]
    // 0xbb22b4: DecompressPointer r0
    //     0xbb22b4: add             x0, x0, HEAP, lsl #32
    // 0xbb22b8: r16 = 12.000000
    //     0xbb22b8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb22bc: ldr             x16, [x16, #0x9e8]
    // 0xbb22c0: r30 = Instance_Color
    //     0xbb22c0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbb22c4: ldr             lr, [lr, #0x50]
    // 0xbb22c8: stp             lr, x16, [SP]
    // 0xbb22cc: mov             x1, x0
    // 0xbb22d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb22d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb22d4: ldr             x4, [x4, #0xaa0]
    // 0xbb22d8: r0 = copyWith()
    //     0xbb22d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb22dc: ldur            x2, [fp, #-8]
    // 0xbb22e0: stur            x0, [fp, #-0x90]
    // 0xbb22e4: LoadField: r1 = r2->field_5f
    //     0xbb22e4: ldur            w1, [x2, #0x5f]
    // 0xbb22e8: DecompressPointer r1
    //     0xbb22e8: add             x1, x1, HEAP, lsl #32
    // 0xbb22ec: tbnz            w1, #4, #0xbb2440
    // 0xbb22f0: LoadField: r1 = r2->field_3f
    //     0xbb22f0: ldur            w1, [x2, #0x3f]
    // 0xbb22f4: DecompressPointer r1
    //     0xbb22f4: add             x1, x1, HEAP, lsl #32
    // 0xbb22f8: LoadField: r3 = r1->field_27
    //     0xbb22f8: ldur            w3, [x1, #0x27]
    // 0xbb22fc: DecompressPointer r3
    //     0xbb22fc: add             x3, x3, HEAP, lsl #32
    // 0xbb2300: LoadField: r1 = r3->field_7
    //     0xbb2300: ldur            w1, [x3, #7]
    // 0xbb2304: DecompressPointer r1
    //     0xbb2304: add             x1, x1, HEAP, lsl #32
    // 0xbb2308: LoadField: r3 = r1->field_7
    //     0xbb2308: ldur            w3, [x1, #7]
    // 0xbb230c: cmp             w3, #0xc
    // 0xbb2310: b.ne            #0xbb238c
    // 0xbb2314: LoadField: r1 = r2->field_b
    //     0xbb2314: ldur            w1, [x2, #0xb]
    // 0xbb2318: DecompressPointer r1
    //     0xbb2318: add             x1, x1, HEAP, lsl #32
    // 0xbb231c: cmp             w1, NULL
    // 0xbb2320: b.eq            #0xbb39fc
    // 0xbb2324: LoadField: r4 = r1->field_13
    //     0xbb2324: ldur            w4, [x1, #0x13]
    // 0xbb2328: DecompressPointer r4
    //     0xbb2328: add             x4, x4, HEAP, lsl #32
    // 0xbb232c: LoadField: r1 = r4->field_b
    //     0xbb232c: ldur            w1, [x4, #0xb]
    // 0xbb2330: DecompressPointer r1
    //     0xbb2330: add             x1, x1, HEAP, lsl #32
    // 0xbb2334: cmp             w1, NULL
    // 0xbb2338: b.ne            #0xbb2344
    // 0xbb233c: r1 = Null
    //     0xbb233c: mov             x1, NULL
    // 0xbb2340: b               #0xbb2374
    // 0xbb2344: LoadField: r4 = r1->field_13
    //     0xbb2344: ldur            w4, [x1, #0x13]
    // 0xbb2348: DecompressPointer r4
    //     0xbb2348: add             x4, x4, HEAP, lsl #32
    // 0xbb234c: cmp             w4, NULL
    // 0xbb2350: b.ne            #0xbb235c
    // 0xbb2354: r1 = Null
    //     0xbb2354: mov             x1, NULL
    // 0xbb2358: b               #0xbb2374
    // 0xbb235c: LoadField: r1 = r4->field_7
    //     0xbb235c: ldur            w1, [x4, #7]
    // 0xbb2360: cbnz            w1, #0xbb236c
    // 0xbb2364: r4 = false
    //     0xbb2364: add             x4, NULL, #0x30  ; false
    // 0xbb2368: b               #0xbb2370
    // 0xbb236c: r4 = true
    //     0xbb236c: add             x4, NULL, #0x20  ; true
    // 0xbb2370: mov             x1, x4
    // 0xbb2374: cmp             w1, NULL
    // 0xbb2378: b.eq            #0xbb238c
    // 0xbb237c: tbnz            w1, #4, #0xbb238c
    // 0xbb2380: r1 = Instance_IconData
    //     0xbb2380: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbb2384: ldr             x1, [x1, #0x130]
    // 0xbb2388: b               #0xbb2394
    // 0xbb238c: r1 = Instance_IconData
    //     0xbb238c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbb2390: ldr             x1, [x1, #0x138]
    // 0xbb2394: stur            x1, [fp, #-0x88]
    // 0xbb2398: cmp             w3, #0xc
    // 0xbb239c: b.ne            #0xbb2418
    // 0xbb23a0: LoadField: r3 = r2->field_b
    //     0xbb23a0: ldur            w3, [x2, #0xb]
    // 0xbb23a4: DecompressPointer r3
    //     0xbb23a4: add             x3, x3, HEAP, lsl #32
    // 0xbb23a8: cmp             w3, NULL
    // 0xbb23ac: b.eq            #0xbb3a00
    // 0xbb23b0: LoadField: r4 = r3->field_13
    //     0xbb23b0: ldur            w4, [x3, #0x13]
    // 0xbb23b4: DecompressPointer r4
    //     0xbb23b4: add             x4, x4, HEAP, lsl #32
    // 0xbb23b8: LoadField: r3 = r4->field_b
    //     0xbb23b8: ldur            w3, [x4, #0xb]
    // 0xbb23bc: DecompressPointer r3
    //     0xbb23bc: add             x3, x3, HEAP, lsl #32
    // 0xbb23c0: cmp             w3, NULL
    // 0xbb23c4: b.ne            #0xbb23d0
    // 0xbb23c8: r3 = Null
    //     0xbb23c8: mov             x3, NULL
    // 0xbb23cc: b               #0xbb2400
    // 0xbb23d0: LoadField: r4 = r3->field_13
    //     0xbb23d0: ldur            w4, [x3, #0x13]
    // 0xbb23d4: DecompressPointer r4
    //     0xbb23d4: add             x4, x4, HEAP, lsl #32
    // 0xbb23d8: cmp             w4, NULL
    // 0xbb23dc: b.ne            #0xbb23e8
    // 0xbb23e0: r3 = Null
    //     0xbb23e0: mov             x3, NULL
    // 0xbb23e4: b               #0xbb2400
    // 0xbb23e8: LoadField: r3 = r4->field_7
    //     0xbb23e8: ldur            w3, [x4, #7]
    // 0xbb23ec: cbnz            w3, #0xbb23f8
    // 0xbb23f0: r4 = false
    //     0xbb23f0: add             x4, NULL, #0x30  ; false
    // 0xbb23f4: b               #0xbb23fc
    // 0xbb23f8: r4 = true
    //     0xbb23f8: add             x4, NULL, #0x20  ; true
    // 0xbb23fc: mov             x3, x4
    // 0xbb2400: cmp             w3, NULL
    // 0xbb2404: b.eq            #0xbb2418
    // 0xbb2408: tbnz            w3, #4, #0xbb2418
    // 0xbb240c: r3 = Instance_Color
    //     0xbb240c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb2410: ldr             x3, [x3, #0x858]
    // 0xbb2414: b               #0xbb2420
    // 0xbb2418: r3 = Instance_Color
    //     0xbb2418: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbb241c: ldr             x3, [x3, #0x50]
    // 0xbb2420: stur            x3, [fp, #-0x80]
    // 0xbb2424: r0 = Icon()
    //     0xbb2424: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbb2428: mov             x1, x0
    // 0xbb242c: ldur            x0, [fp, #-0x88]
    // 0xbb2430: StoreField: r1->field_b = r0
    //     0xbb2430: stur            w0, [x1, #0xb]
    // 0xbb2434: ldur            x0, [fp, #-0x80]
    // 0xbb2438: StoreField: r1->field_23 = r0
    //     0xbb2438: stur            w0, [x1, #0x23]
    // 0xbb243c: b               #0xbb2444
    // 0xbb2440: r1 = Null
    //     0xbb2440: mov             x1, NULL
    // 0xbb2444: ldur            x2, [fp, #-8]
    // 0xbb2448: ldur            x0, [fp, #-0x20]
    // 0xbb244c: ldur            x16, [fp, #-0x70]
    // 0xbb2450: r30 = "Pincode*"
    //     0xbb2450: add             lr, PP, #0x54, lsl #12  ; [pp+0x540d8] "Pincode*"
    //     0xbb2454: ldr             lr, [lr, #0xd8]
    // 0xbb2458: stp             lr, x16, [SP, #0x20]
    // 0xbb245c: ldur            x16, [fp, #-0x78]
    // 0xbb2460: r30 = 4
    //     0xbb2460: movz            lr, #0x4
    // 0xbb2464: stp             lr, x16, [SP, #0x10]
    // 0xbb2468: ldur            x16, [fp, #-0x90]
    // 0xbb246c: stp             x1, x16, [SP]
    // 0xbb2470: ldur            x1, [fp, #-0x68]
    // 0xbb2474: r4 = const [0, 0x7, 0x6, 0x1, errorMaxLines, 0x4, errorStyle, 0x5, focusedBorder, 0x1, labelStyle, 0x3, labelText, 0x2, suffixIcon, 0x6, null]
    //     0xbb2474: add             x4, PP, #0x54, lsl #12  ; [pp+0x54088] List(17) [0, 0x7, 0x6, 0x1, "errorMaxLines", 0x4, "errorStyle", 0x5, "focusedBorder", 0x1, "labelStyle", 0x3, "labelText", 0x2, "suffixIcon", 0x6, Null]
    //     0xbb2478: ldr             x4, [x4, #0x88]
    // 0xbb247c: r0 = copyWith()
    //     0xbb247c: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbb2480: ldur            x2, [fp, #-8]
    // 0xbb2484: r1 = Function '_validatePinCode@1669306702':.
    //     0xbb2484: add             x1, PP, #0x54, lsl #12  ; [pp+0x540e0] AnonymousClosure: (0xbb3a9c), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validatePinCode (0xa056a0)
    //     0xbb2488: ldr             x1, [x1, #0xe0]
    // 0xbb248c: stur            x0, [fp, #-0x68]
    // 0xbb2490: r0 = AllocateClosure()
    //     0xbb2490: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb2494: ldur            x2, [fp, #-0x28]
    // 0xbb2498: r1 = Function '<anonymous closure>':.
    //     0xbb2498: add             x1, PP, #0x54, lsl #12  ; [pp+0x540e8] AnonymousClosure: (0xa01ce8), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xbb249c: ldr             x1, [x1, #0xe8]
    // 0xbb24a0: stur            x0, [fp, #-0x70]
    // 0xbb24a4: r0 = AllocateClosure()
    //     0xbb24a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb24a8: r1 = <String>
    //     0xbb24a8: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbb24ac: stur            x0, [fp, #-0x78]
    // 0xbb24b0: r0 = TextFormField()
    //     0xbb24b0: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbb24b4: stur            x0, [fp, #-0x80]
    // 0xbb24b8: ldur            x16, [fp, #-0x70]
    // 0xbb24bc: r30 = Instance_AutovalidateMode
    //     0xbb24bc: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbb24c0: ldr             lr, [lr, #0x7e8]
    // 0xbb24c4: stp             lr, x16, [SP, #0x40]
    // 0xbb24c8: r16 = true
    //     0xbb24c8: add             x16, NULL, #0x20  ; true
    // 0xbb24cc: ldur            lr, [fp, #-0x18]
    // 0xbb24d0: stp             lr, x16, [SP, #0x30]
    // 0xbb24d4: ldur            x16, [fp, #-0x58]
    // 0xbb24d8: r30 = Instance_TextInputType
    //     0xbb24d8: add             lr, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xbb24dc: ldr             lr, [lr, #0x1a0]
    // 0xbb24e0: stp             lr, x16, [SP, #0x20]
    // 0xbb24e4: ldur            x16, [fp, #-0x50]
    // 0xbb24e8: r30 = 2
    //     0xbb24e8: movz            lr, #0x2
    // 0xbb24ec: stp             lr, x16, [SP, #0x10]
    // 0xbb24f0: ldur            x16, [fp, #-0x60]
    // 0xbb24f4: ldur            lr, [fp, #-0x78]
    // 0xbb24f8: stp             lr, x16, [SP]
    // 0xbb24fc: mov             x1, x0
    // 0xbb2500: ldur            x2, [fp, #-0x68]
    // 0xbb2504: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x5, autovalidateMode, 0x3, controller, 0x8, enableSuggestions, 0x4, inputFormatters, 0x6, keyboardType, 0x7, maxLines, 0x9, onChanged, 0xb, style, 0xa, validator, 0x2, null]
    //     0xbb2504: add             x4, PP, #0x54, lsl #12  ; [pp+0x540a0] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x5, "autovalidateMode", 0x3, "controller", 0x8, "enableSuggestions", 0x4, "inputFormatters", 0x6, "keyboardType", 0x7, "maxLines", 0x9, "onChanged", 0xb, "style", 0xa, "validator", 0x2, Null]
    //     0xbb2508: ldr             x4, [x4, #0xa0]
    // 0xbb250c: r0 = TextFormField()
    //     0xbb250c: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbb2510: r0 = Form()
    //     0xbb2510: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbb2514: mov             x1, x0
    // 0xbb2518: ldur            x0, [fp, #-0x80]
    // 0xbb251c: stur            x1, [fp, #-0x18]
    // 0xbb2520: StoreField: r1->field_b = r0
    //     0xbb2520: stur            w0, [x1, #0xb]
    // 0xbb2524: r0 = Instance_AutovalidateMode
    //     0xbb2524: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbb2528: ldr             x0, [x0, #0x800]
    // 0xbb252c: StoreField: r1->field_23 = r0
    //     0xbb252c: stur            w0, [x1, #0x23]
    // 0xbb2530: ldur            x2, [fp, #-0x20]
    // 0xbb2534: StoreField: r1->field_7 = r2
    //     0xbb2534: stur            w2, [x1, #7]
    // 0xbb2538: r0 = Padding()
    //     0xbb2538: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb253c: mov             x2, x0
    // 0xbb2540: r0 = Instance_EdgeInsets
    //     0xbb2540: add             x0, PP, #0x42, lsl #12  ; [pp+0x42620] Obj!EdgeInsets@d57e01
    //     0xbb2544: ldr             x0, [x0, #0x620]
    // 0xbb2548: stur            x2, [fp, #-0x50]
    // 0xbb254c: StoreField: r2->field_f = r0
    //     0xbb254c: stur            w0, [x2, #0xf]
    // 0xbb2550: ldur            x0, [fp, #-0x18]
    // 0xbb2554: StoreField: r2->field_b = r0
    //     0xbb2554: stur            w0, [x2, #0xb]
    // 0xbb2558: ldur            x0, [fp, #-8]
    // 0xbb255c: LoadField: r3 = r0->field_23
    //     0xbb255c: ldur            w3, [x0, #0x23]
    // 0xbb2560: DecompressPointer r3
    //     0xbb2560: add             x3, x3, HEAP, lsl #32
    // 0xbb2564: stur            x3, [fp, #-0x20]
    // 0xbb2568: LoadField: r1 = r0->field_3f
    //     0xbb2568: ldur            w1, [x0, #0x3f]
    // 0xbb256c: DecompressPointer r1
    //     0xbb256c: add             x1, x1, HEAP, lsl #32
    // 0xbb2570: LoadField: r4 = r1->field_27
    //     0xbb2570: ldur            w4, [x1, #0x27]
    // 0xbb2574: DecompressPointer r4
    //     0xbb2574: add             x4, x4, HEAP, lsl #32
    // 0xbb2578: LoadField: r1 = r4->field_7
    //     0xbb2578: ldur            w1, [x4, #7]
    // 0xbb257c: DecompressPointer r1
    //     0xbb257c: add             x1, x1, HEAP, lsl #32
    // 0xbb2580: LoadField: r4 = r1->field_7
    //     0xbb2580: ldur            w4, [x1, #7]
    // 0xbb2584: cmp             w4, #0xc
    // 0xbb2588: b.ne            #0xbb25d0
    // 0xbb258c: LoadField: r1 = r0->field_b
    //     0xbb258c: ldur            w1, [x0, #0xb]
    // 0xbb2590: DecompressPointer r1
    //     0xbb2590: add             x1, x1, HEAP, lsl #32
    // 0xbb2594: cmp             w1, NULL
    // 0xbb2598: b.eq            #0xbb3a04
    // 0xbb259c: LoadField: r4 = r1->field_13
    //     0xbb259c: ldur            w4, [x1, #0x13]
    // 0xbb25a0: DecompressPointer r4
    //     0xbb25a0: add             x4, x4, HEAP, lsl #32
    // 0xbb25a4: LoadField: r1 = r4->field_b
    //     0xbb25a4: ldur            w1, [x4, #0xb]
    // 0xbb25a8: DecompressPointer r1
    //     0xbb25a8: add             x1, x1, HEAP, lsl #32
    // 0xbb25ac: cmp             w1, NULL
    // 0xbb25b0: b.ne            #0xbb25bc
    // 0xbb25b4: r1 = Null
    //     0xbb25b4: mov             x1, NULL
    // 0xbb25b8: b               #0xbb25c8
    // 0xbb25bc: LoadField: r4 = r1->field_13
    //     0xbb25bc: ldur            w4, [x1, #0x13]
    // 0xbb25c0: DecompressPointer r4
    //     0xbb25c0: add             x4, x4, HEAP, lsl #32
    // 0xbb25c4: mov             x1, x4
    // 0xbb25c8: mov             x4, x1
    // 0xbb25cc: b               #0xbb25d4
    // 0xbb25d0: r4 = ""
    //     0xbb25d0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb25d4: stur            x4, [fp, #-0x18]
    // 0xbb25d8: r1 = <TextEditingValue>
    //     0xbb25d8: ldr             x1, [PP, #0x6c80]  ; [pp+0x6c80] TypeArguments: <TextEditingValue>
    // 0xbb25dc: r0 = TextEditingController()
    //     0xbb25dc: bl              #0x905a14  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0xbb25e0: stur            x0, [fp, #-0x58]
    // 0xbb25e4: ldur            x16, [fp, #-0x18]
    // 0xbb25e8: str             x16, [SP]
    // 0xbb25ec: mov             x1, x0
    // 0xbb25f0: r4 = const [0, 0x2, 0x1, 0x1, text, 0x1, null]
    //     0xbb25f0: add             x4, PP, #0x33, lsl #12  ; [pp+0x33c40] List(7) [0, 0x2, 0x1, 0x1, "text", 0x1, Null]
    //     0xbb25f4: ldr             x4, [x4, #0xc40]
    // 0xbb25f8: r0 = TextEditingController()
    //     0xbb25f8: bl              #0x905904  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0xbb25fc: ldur            x1, [fp, #-0x10]
    // 0xbb2600: r0 = of()
    //     0xbb2600: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb2604: LoadField: r1 = r0->field_87
    //     0xbb2604: ldur            w1, [x0, #0x87]
    // 0xbb2608: DecompressPointer r1
    //     0xbb2608: add             x1, x1, HEAP, lsl #32
    // 0xbb260c: LoadField: r0 = r1->field_2b
    //     0xbb260c: ldur            w0, [x1, #0x2b]
    // 0xbb2610: DecompressPointer r0
    //     0xbb2610: add             x0, x0, HEAP, lsl #32
    // 0xbb2614: ldur            x1, [fp, #-0x10]
    // 0xbb2618: stur            x0, [fp, #-0x18]
    // 0xbb261c: r0 = of()
    //     0xbb261c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb2620: LoadField: r1 = r0->field_5b
    //     0xbb2620: ldur            w1, [x0, #0x5b]
    // 0xbb2624: DecompressPointer r1
    //     0xbb2624: add             x1, x1, HEAP, lsl #32
    // 0xbb2628: r16 = 14.000000
    //     0xbb2628: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb262c: ldr             x16, [x16, #0x1d8]
    // 0xbb2630: stp             x1, x16, [SP]
    // 0xbb2634: ldur            x1, [fp, #-0x18]
    // 0xbb2638: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb2638: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb263c: ldr             x4, [x4, #0xaa0]
    // 0xbb2640: r0 = copyWith()
    //     0xbb2640: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb2644: ldur            x1, [fp, #-0x10]
    // 0xbb2648: stur            x0, [fp, #-0x18]
    // 0xbb264c: r0 = getTextFormFieldInputDecoration()
    //     0xbb264c: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbb2650: ldur            x1, [fp, #-0x10]
    // 0xbb2654: stur            x0, [fp, #-0x60]
    // 0xbb2658: r0 = of()
    //     0xbb2658: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb265c: LoadField: r1 = r0->field_5b
    //     0xbb265c: ldur            w1, [x0, #0x5b]
    // 0xbb2660: DecompressPointer r1
    //     0xbb2660: add             x1, x1, HEAP, lsl #32
    // 0xbb2664: stur            x1, [fp, #-0x68]
    // 0xbb2668: r0 = BorderSide()
    //     0xbb2668: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbb266c: mov             x1, x0
    // 0xbb2670: ldur            x0, [fp, #-0x68]
    // 0xbb2674: stur            x1, [fp, #-0x70]
    // 0xbb2678: StoreField: r1->field_7 = r0
    //     0xbb2678: stur            w0, [x1, #7]
    // 0xbb267c: d0 = 1.000000
    //     0xbb267c: fmov            d0, #1.00000000
    // 0xbb2680: StoreField: r1->field_b = d0
    //     0xbb2680: stur            d0, [x1, #0xb]
    // 0xbb2684: r0 = Instance_BorderStyle
    //     0xbb2684: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbb2688: ldr             x0, [x0, #0xf68]
    // 0xbb268c: StoreField: r1->field_13 = r0
    //     0xbb268c: stur            w0, [x1, #0x13]
    // 0xbb2690: d1 = -1.000000
    //     0xbb2690: fmov            d1, #-1.00000000
    // 0xbb2694: ArrayStore: r1[0] = d1  ; List_8
    //     0xbb2694: stur            d1, [x1, #0x17]
    // 0xbb2698: r0 = OutlineInputBorder()
    //     0xbb2698: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbb269c: mov             x2, x0
    // 0xbb26a0: r0 = Instance_BorderRadius
    //     0xbb26a0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbb26a4: ldr             x0, [x0, #0xf70]
    // 0xbb26a8: stur            x2, [fp, #-0x68]
    // 0xbb26ac: StoreField: r2->field_13 = r0
    //     0xbb26ac: stur            w0, [x2, #0x13]
    // 0xbb26b0: d0 = 4.000000
    //     0xbb26b0: fmov            d0, #4.00000000
    // 0xbb26b4: StoreField: r2->field_b = d0
    //     0xbb26b4: stur            d0, [x2, #0xb]
    // 0xbb26b8: ldur            x1, [fp, #-0x70]
    // 0xbb26bc: StoreField: r2->field_7 = r1
    //     0xbb26bc: stur            w1, [x2, #7]
    // 0xbb26c0: ldur            x1, [fp, #-0x10]
    // 0xbb26c4: r0 = of()
    //     0xbb26c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb26c8: LoadField: r1 = r0->field_5b
    //     0xbb26c8: ldur            w1, [x0, #0x5b]
    // 0xbb26cc: DecompressPointer r1
    //     0xbb26cc: add             x1, x1, HEAP, lsl #32
    // 0xbb26d0: r0 = LoadClassIdInstr(r1)
    //     0xbb26d0: ldur            x0, [x1, #-1]
    //     0xbb26d4: ubfx            x0, x0, #0xc, #0x14
    // 0xbb26d8: d0 = 0.100000
    //     0xbb26d8: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbb26dc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbb26dc: sub             lr, x0, #0xffa
    //     0xbb26e0: ldr             lr, [x21, lr, lsl #3]
    //     0xbb26e4: blr             lr
    // 0xbb26e8: ldur            x1, [fp, #-0x10]
    // 0xbb26ec: stur            x0, [fp, #-0x70]
    // 0xbb26f0: r0 = of()
    //     0xbb26f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb26f4: LoadField: r1 = r0->field_87
    //     0xbb26f4: ldur            w1, [x0, #0x87]
    // 0xbb26f8: DecompressPointer r1
    //     0xbb26f8: add             x1, x1, HEAP, lsl #32
    // 0xbb26fc: LoadField: r0 = r1->field_2b
    //     0xbb26fc: ldur            w0, [x1, #0x2b]
    // 0xbb2700: DecompressPointer r0
    //     0xbb2700: add             x0, x0, HEAP, lsl #32
    // 0xbb2704: stur            x0, [fp, #-0x78]
    // 0xbb2708: r1 = Instance_Color
    //     0xbb2708: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb270c: d0 = 0.400000
    //     0xbb270c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbb2710: r0 = withOpacity()
    //     0xbb2710: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb2714: r16 = 14.000000
    //     0xbb2714: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb2718: ldr             x16, [x16, #0x1d8]
    // 0xbb271c: stp             x0, x16, [SP]
    // 0xbb2720: ldur            x1, [fp, #-0x78]
    // 0xbb2724: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb2724: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb2728: ldr             x4, [x4, #0xaa0]
    // 0xbb272c: r0 = copyWith()
    //     0xbb272c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb2730: ldur            x2, [fp, #-8]
    // 0xbb2734: stur            x0, [fp, #-0x88]
    // 0xbb2738: LoadField: r1 = r2->field_5f
    //     0xbb2738: ldur            w1, [x2, #0x5f]
    // 0xbb273c: DecompressPointer r1
    //     0xbb273c: add             x1, x1, HEAP, lsl #32
    // 0xbb2740: tbnz            w1, #4, #0xbb2894
    // 0xbb2744: LoadField: r1 = r2->field_3f
    //     0xbb2744: ldur            w1, [x2, #0x3f]
    // 0xbb2748: DecompressPointer r1
    //     0xbb2748: add             x1, x1, HEAP, lsl #32
    // 0xbb274c: LoadField: r3 = r1->field_27
    //     0xbb274c: ldur            w3, [x1, #0x27]
    // 0xbb2750: DecompressPointer r3
    //     0xbb2750: add             x3, x3, HEAP, lsl #32
    // 0xbb2754: LoadField: r1 = r3->field_7
    //     0xbb2754: ldur            w1, [x3, #7]
    // 0xbb2758: DecompressPointer r1
    //     0xbb2758: add             x1, x1, HEAP, lsl #32
    // 0xbb275c: LoadField: r3 = r1->field_7
    //     0xbb275c: ldur            w3, [x1, #7]
    // 0xbb2760: cmp             w3, #0xc
    // 0xbb2764: b.ne            #0xbb27e0
    // 0xbb2768: LoadField: r1 = r2->field_b
    //     0xbb2768: ldur            w1, [x2, #0xb]
    // 0xbb276c: DecompressPointer r1
    //     0xbb276c: add             x1, x1, HEAP, lsl #32
    // 0xbb2770: cmp             w1, NULL
    // 0xbb2774: b.eq            #0xbb3a08
    // 0xbb2778: LoadField: r4 = r1->field_13
    //     0xbb2778: ldur            w4, [x1, #0x13]
    // 0xbb277c: DecompressPointer r4
    //     0xbb277c: add             x4, x4, HEAP, lsl #32
    // 0xbb2780: LoadField: r1 = r4->field_b
    //     0xbb2780: ldur            w1, [x4, #0xb]
    // 0xbb2784: DecompressPointer r1
    //     0xbb2784: add             x1, x1, HEAP, lsl #32
    // 0xbb2788: cmp             w1, NULL
    // 0xbb278c: b.ne            #0xbb2798
    // 0xbb2790: r1 = Null
    //     0xbb2790: mov             x1, NULL
    // 0xbb2794: b               #0xbb27c8
    // 0xbb2798: LoadField: r4 = r1->field_13
    //     0xbb2798: ldur            w4, [x1, #0x13]
    // 0xbb279c: DecompressPointer r4
    //     0xbb279c: add             x4, x4, HEAP, lsl #32
    // 0xbb27a0: cmp             w4, NULL
    // 0xbb27a4: b.ne            #0xbb27b0
    // 0xbb27a8: r1 = Null
    //     0xbb27a8: mov             x1, NULL
    // 0xbb27ac: b               #0xbb27c8
    // 0xbb27b0: LoadField: r1 = r4->field_7
    //     0xbb27b0: ldur            w1, [x4, #7]
    // 0xbb27b4: cbnz            w1, #0xbb27c0
    // 0xbb27b8: r4 = false
    //     0xbb27b8: add             x4, NULL, #0x30  ; false
    // 0xbb27bc: b               #0xbb27c4
    // 0xbb27c0: r4 = true
    //     0xbb27c0: add             x4, NULL, #0x20  ; true
    // 0xbb27c4: mov             x1, x4
    // 0xbb27c8: cmp             w1, NULL
    // 0xbb27cc: b.eq            #0xbb27e0
    // 0xbb27d0: tbnz            w1, #4, #0xbb27e0
    // 0xbb27d4: r1 = Instance_IconData
    //     0xbb27d4: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbb27d8: ldr             x1, [x1, #0x130]
    // 0xbb27dc: b               #0xbb27e8
    // 0xbb27e0: r1 = Instance_IconData
    //     0xbb27e0: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbb27e4: ldr             x1, [x1, #0x138]
    // 0xbb27e8: stur            x1, [fp, #-0x80]
    // 0xbb27ec: cmp             w3, #0xc
    // 0xbb27f0: b.ne            #0xbb286c
    // 0xbb27f4: LoadField: r3 = r2->field_b
    //     0xbb27f4: ldur            w3, [x2, #0xb]
    // 0xbb27f8: DecompressPointer r3
    //     0xbb27f8: add             x3, x3, HEAP, lsl #32
    // 0xbb27fc: cmp             w3, NULL
    // 0xbb2800: b.eq            #0xbb3a0c
    // 0xbb2804: LoadField: r4 = r3->field_13
    //     0xbb2804: ldur            w4, [x3, #0x13]
    // 0xbb2808: DecompressPointer r4
    //     0xbb2808: add             x4, x4, HEAP, lsl #32
    // 0xbb280c: LoadField: r3 = r4->field_b
    //     0xbb280c: ldur            w3, [x4, #0xb]
    // 0xbb2810: DecompressPointer r3
    //     0xbb2810: add             x3, x3, HEAP, lsl #32
    // 0xbb2814: cmp             w3, NULL
    // 0xbb2818: b.ne            #0xbb2824
    // 0xbb281c: r3 = Null
    //     0xbb281c: mov             x3, NULL
    // 0xbb2820: b               #0xbb2854
    // 0xbb2824: LoadField: r4 = r3->field_13
    //     0xbb2824: ldur            w4, [x3, #0x13]
    // 0xbb2828: DecompressPointer r4
    //     0xbb2828: add             x4, x4, HEAP, lsl #32
    // 0xbb282c: cmp             w4, NULL
    // 0xbb2830: b.ne            #0xbb283c
    // 0xbb2834: r3 = Null
    //     0xbb2834: mov             x3, NULL
    // 0xbb2838: b               #0xbb2854
    // 0xbb283c: LoadField: r3 = r4->field_7
    //     0xbb283c: ldur            w3, [x4, #7]
    // 0xbb2840: cbnz            w3, #0xbb284c
    // 0xbb2844: r4 = false
    //     0xbb2844: add             x4, NULL, #0x30  ; false
    // 0xbb2848: b               #0xbb2850
    // 0xbb284c: r4 = true
    //     0xbb284c: add             x4, NULL, #0x20  ; true
    // 0xbb2850: mov             x3, x4
    // 0xbb2854: cmp             w3, NULL
    // 0xbb2858: b.eq            #0xbb286c
    // 0xbb285c: tbnz            w3, #4, #0xbb286c
    // 0xbb2860: r3 = Instance_Color
    //     0xbb2860: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb2864: ldr             x3, [x3, #0x858]
    // 0xbb2868: b               #0xbb2874
    // 0xbb286c: r3 = Instance_Color
    //     0xbb286c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbb2870: ldr             x3, [x3, #0x50]
    // 0xbb2874: stur            x3, [fp, #-0x78]
    // 0xbb2878: r0 = Icon()
    //     0xbb2878: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbb287c: mov             x1, x0
    // 0xbb2880: ldur            x0, [fp, #-0x80]
    // 0xbb2884: StoreField: r1->field_b = r0
    //     0xbb2884: stur            w0, [x1, #0xb]
    // 0xbb2888: ldur            x0, [fp, #-0x78]
    // 0xbb288c: StoreField: r1->field_23 = r0
    //     0xbb288c: stur            w0, [x1, #0x23]
    // 0xbb2890: b               #0xbb2898
    // 0xbb2894: r1 = Null
    //     0xbb2894: mov             x1, NULL
    // 0xbb2898: ldur            x2, [fp, #-8]
    // 0xbb289c: ldur            x0, [fp, #-0x20]
    // 0xbb28a0: ldur            x16, [fp, #-0x68]
    // 0xbb28a4: ldur            lr, [fp, #-0x70]
    // 0xbb28a8: stp             lr, x16, [SP, #0x18]
    // 0xbb28ac: r16 = "City*"
    //     0xbb28ac: add             x16, PP, #0x54, lsl #12  ; [pp+0x540f0] "City*"
    //     0xbb28b0: ldr             x16, [x16, #0xf0]
    // 0xbb28b4: ldur            lr, [fp, #-0x88]
    // 0xbb28b8: stp             lr, x16, [SP, #8]
    // 0xbb28bc: str             x1, [SP]
    // 0xbb28c0: ldur            x1, [fp, #-0x60]
    // 0xbb28c4: r4 = const [0, 0x6, 0x5, 0x1, fillColor, 0x2, focusedBorder, 0x1, labelStyle, 0x4, labelText, 0x3, suffixIcon, 0x5, null]
    //     0xbb28c4: add             x4, PP, #0x54, lsl #12  ; [pp+0x540f8] List(15) [0, 0x6, 0x5, 0x1, "fillColor", 0x2, "focusedBorder", 0x1, "labelStyle", 0x4, "labelText", 0x3, "suffixIcon", 0x5, Null]
    //     0xbb28c8: ldr             x4, [x4, #0xf8]
    // 0xbb28cc: r0 = copyWith()
    //     0xbb28cc: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbb28d0: r1 = <String>
    //     0xbb28d0: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbb28d4: stur            x0, [fp, #-0x60]
    // 0xbb28d8: r0 = TextFormField()
    //     0xbb28d8: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbb28dc: stur            x0, [fp, #-0x68]
    // 0xbb28e0: r16 = true
    //     0xbb28e0: add             x16, NULL, #0x20  ; true
    // 0xbb28e4: r30 = true
    //     0xbb28e4: add             lr, NULL, #0x20  ; true
    // 0xbb28e8: stp             lr, x16, [SP, #0x10]
    // 0xbb28ec: ldur            x16, [fp, #-0x58]
    // 0xbb28f0: ldur            lr, [fp, #-0x18]
    // 0xbb28f4: stp             lr, x16, [SP]
    // 0xbb28f8: mov             x1, x0
    // 0xbb28fc: ldur            x2, [fp, #-0x60]
    // 0xbb2900: r4 = const [0, 0x6, 0x4, 0x2, controller, 0x4, ignorePointers, 0x3, readOnly, 0x2, style, 0x5, null]
    //     0xbb2900: add             x4, PP, #0x54, lsl #12  ; [pp+0x54100] List(13) [0, 0x6, 0x4, 0x2, "controller", 0x4, "ignorePointers", 0x3, "readOnly", 0x2, "style", 0x5, Null]
    //     0xbb2904: ldr             x4, [x4, #0x100]
    // 0xbb2908: r0 = TextFormField()
    //     0xbb2908: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbb290c: r0 = Form()
    //     0xbb290c: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbb2910: mov             x2, x0
    // 0xbb2914: ldur            x0, [fp, #-0x68]
    // 0xbb2918: stur            x2, [fp, #-0x18]
    // 0xbb291c: StoreField: r2->field_b = r0
    //     0xbb291c: stur            w0, [x2, #0xb]
    // 0xbb2920: r0 = Instance_AutovalidateMode
    //     0xbb2920: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbb2924: ldr             x0, [x0, #0x800]
    // 0xbb2928: StoreField: r2->field_23 = r0
    //     0xbb2928: stur            w0, [x2, #0x23]
    // 0xbb292c: ldur            x1, [fp, #-0x20]
    // 0xbb2930: StoreField: r2->field_7 = r1
    //     0xbb2930: stur            w1, [x2, #7]
    // 0xbb2934: r1 = <FlexParentData>
    //     0xbb2934: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbb2938: ldr             x1, [x1, #0xe00]
    // 0xbb293c: r0 = Flexible()
    //     0xbb293c: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xbb2940: mov             x2, x0
    // 0xbb2944: r0 = 1
    //     0xbb2944: movz            x0, #0x1
    // 0xbb2948: stur            x2, [fp, #-0x58]
    // 0xbb294c: StoreField: r2->field_13 = r0
    //     0xbb294c: stur            x0, [x2, #0x13]
    // 0xbb2950: r3 = Instance_FlexFit
    //     0xbb2950: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbb2954: ldr             x3, [x3, #0xe08]
    // 0xbb2958: StoreField: r2->field_1b = r3
    //     0xbb2958: stur            w3, [x2, #0x1b]
    // 0xbb295c: ldur            x1, [fp, #-0x18]
    // 0xbb2960: StoreField: r2->field_b = r1
    //     0xbb2960: stur            w1, [x2, #0xb]
    // 0xbb2964: ldur            x4, [fp, #-8]
    // 0xbb2968: LoadField: r5 = r4->field_27
    //     0xbb2968: ldur            w5, [x4, #0x27]
    // 0xbb296c: DecompressPointer r5
    //     0xbb296c: add             x5, x5, HEAP, lsl #32
    // 0xbb2970: stur            x5, [fp, #-0x20]
    // 0xbb2974: LoadField: r1 = r4->field_3f
    //     0xbb2974: ldur            w1, [x4, #0x3f]
    // 0xbb2978: DecompressPointer r1
    //     0xbb2978: add             x1, x1, HEAP, lsl #32
    // 0xbb297c: LoadField: r6 = r1->field_27
    //     0xbb297c: ldur            w6, [x1, #0x27]
    // 0xbb2980: DecompressPointer r6
    //     0xbb2980: add             x6, x6, HEAP, lsl #32
    // 0xbb2984: LoadField: r1 = r6->field_7
    //     0xbb2984: ldur            w1, [x6, #7]
    // 0xbb2988: DecompressPointer r1
    //     0xbb2988: add             x1, x1, HEAP, lsl #32
    // 0xbb298c: LoadField: r6 = r1->field_7
    //     0xbb298c: ldur            w6, [x1, #7]
    // 0xbb2990: cmp             w6, #0xc
    // 0xbb2994: b.ne            #0xbb29dc
    // 0xbb2998: LoadField: r1 = r4->field_b
    //     0xbb2998: ldur            w1, [x4, #0xb]
    // 0xbb299c: DecompressPointer r1
    //     0xbb299c: add             x1, x1, HEAP, lsl #32
    // 0xbb29a0: cmp             w1, NULL
    // 0xbb29a4: b.eq            #0xbb3a10
    // 0xbb29a8: LoadField: r6 = r1->field_13
    //     0xbb29a8: ldur            w6, [x1, #0x13]
    // 0xbb29ac: DecompressPointer r6
    //     0xbb29ac: add             x6, x6, HEAP, lsl #32
    // 0xbb29b0: LoadField: r1 = r6->field_b
    //     0xbb29b0: ldur            w1, [x6, #0xb]
    // 0xbb29b4: DecompressPointer r1
    //     0xbb29b4: add             x1, x1, HEAP, lsl #32
    // 0xbb29b8: cmp             w1, NULL
    // 0xbb29bc: b.ne            #0xbb29c8
    // 0xbb29c0: r1 = Null
    //     0xbb29c0: mov             x1, NULL
    // 0xbb29c4: b               #0xbb29d4
    // 0xbb29c8: LoadField: r6 = r1->field_f
    //     0xbb29c8: ldur            w6, [x1, #0xf]
    // 0xbb29cc: DecompressPointer r6
    //     0xbb29cc: add             x6, x6, HEAP, lsl #32
    // 0xbb29d0: mov             x1, x6
    // 0xbb29d4: mov             x6, x1
    // 0xbb29d8: b               #0xbb29e0
    // 0xbb29dc: r6 = ""
    //     0xbb29dc: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb29e0: stur            x6, [fp, #-0x18]
    // 0xbb29e4: r1 = <TextEditingValue>
    //     0xbb29e4: ldr             x1, [PP, #0x6c80]  ; [pp+0x6c80] TypeArguments: <TextEditingValue>
    // 0xbb29e8: r0 = TextEditingController()
    //     0xbb29e8: bl              #0x905a14  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0xbb29ec: stur            x0, [fp, #-0x60]
    // 0xbb29f0: ldur            x16, [fp, #-0x18]
    // 0xbb29f4: str             x16, [SP]
    // 0xbb29f8: mov             x1, x0
    // 0xbb29fc: r4 = const [0, 0x2, 0x1, 0x1, text, 0x1, null]
    //     0xbb29fc: add             x4, PP, #0x33, lsl #12  ; [pp+0x33c40] List(7) [0, 0x2, 0x1, 0x1, "text", 0x1, Null]
    //     0xbb2a00: ldr             x4, [x4, #0xc40]
    // 0xbb2a04: r0 = TextEditingController()
    //     0xbb2a04: bl              #0x905904  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0xbb2a08: ldur            x1, [fp, #-0x10]
    // 0xbb2a0c: r0 = of()
    //     0xbb2a0c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb2a10: LoadField: r1 = r0->field_87
    //     0xbb2a10: ldur            w1, [x0, #0x87]
    // 0xbb2a14: DecompressPointer r1
    //     0xbb2a14: add             x1, x1, HEAP, lsl #32
    // 0xbb2a18: LoadField: r0 = r1->field_2b
    //     0xbb2a18: ldur            w0, [x1, #0x2b]
    // 0xbb2a1c: DecompressPointer r0
    //     0xbb2a1c: add             x0, x0, HEAP, lsl #32
    // 0xbb2a20: ldur            x1, [fp, #-0x10]
    // 0xbb2a24: stur            x0, [fp, #-0x18]
    // 0xbb2a28: r0 = of()
    //     0xbb2a28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb2a2c: LoadField: r1 = r0->field_5b
    //     0xbb2a2c: ldur            w1, [x0, #0x5b]
    // 0xbb2a30: DecompressPointer r1
    //     0xbb2a30: add             x1, x1, HEAP, lsl #32
    // 0xbb2a34: r16 = 14.000000
    //     0xbb2a34: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb2a38: ldr             x16, [x16, #0x1d8]
    // 0xbb2a3c: stp             x1, x16, [SP]
    // 0xbb2a40: ldur            x1, [fp, #-0x18]
    // 0xbb2a44: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb2a44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb2a48: ldr             x4, [x4, #0xaa0]
    // 0xbb2a4c: r0 = copyWith()
    //     0xbb2a4c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb2a50: ldur            x1, [fp, #-0x10]
    // 0xbb2a54: stur            x0, [fp, #-0x18]
    // 0xbb2a58: r0 = getTextFormFieldInputDecoration()
    //     0xbb2a58: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbb2a5c: ldur            x1, [fp, #-0x10]
    // 0xbb2a60: stur            x0, [fp, #-0x68]
    // 0xbb2a64: r0 = of()
    //     0xbb2a64: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb2a68: LoadField: r1 = r0->field_5b
    //     0xbb2a68: ldur            w1, [x0, #0x5b]
    // 0xbb2a6c: DecompressPointer r1
    //     0xbb2a6c: add             x1, x1, HEAP, lsl #32
    // 0xbb2a70: r0 = LoadClassIdInstr(r1)
    //     0xbb2a70: ldur            x0, [x1, #-1]
    //     0xbb2a74: ubfx            x0, x0, #0xc, #0x14
    // 0xbb2a78: d0 = 0.100000
    //     0xbb2a78: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbb2a7c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbb2a7c: sub             lr, x0, #0xffa
    //     0xbb2a80: ldr             lr, [x21, lr, lsl #3]
    //     0xbb2a84: blr             lr
    // 0xbb2a88: ldur            x1, [fp, #-0x10]
    // 0xbb2a8c: stur            x0, [fp, #-0x70]
    // 0xbb2a90: r0 = of()
    //     0xbb2a90: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb2a94: LoadField: r1 = r0->field_5b
    //     0xbb2a94: ldur            w1, [x0, #0x5b]
    // 0xbb2a98: DecompressPointer r1
    //     0xbb2a98: add             x1, x1, HEAP, lsl #32
    // 0xbb2a9c: stur            x1, [fp, #-0x78]
    // 0xbb2aa0: r0 = BorderSide()
    //     0xbb2aa0: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbb2aa4: mov             x1, x0
    // 0xbb2aa8: ldur            x0, [fp, #-0x78]
    // 0xbb2aac: stur            x1, [fp, #-0x80]
    // 0xbb2ab0: StoreField: r1->field_7 = r0
    //     0xbb2ab0: stur            w0, [x1, #7]
    // 0xbb2ab4: d0 = 1.000000
    //     0xbb2ab4: fmov            d0, #1.00000000
    // 0xbb2ab8: StoreField: r1->field_b = d0
    //     0xbb2ab8: stur            d0, [x1, #0xb]
    // 0xbb2abc: r0 = Instance_BorderStyle
    //     0xbb2abc: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbb2ac0: ldr             x0, [x0, #0xf68]
    // 0xbb2ac4: StoreField: r1->field_13 = r0
    //     0xbb2ac4: stur            w0, [x1, #0x13]
    // 0xbb2ac8: d1 = -1.000000
    //     0xbb2ac8: fmov            d1, #-1.00000000
    // 0xbb2acc: ArrayStore: r1[0] = d1  ; List_8
    //     0xbb2acc: stur            d1, [x1, #0x17]
    // 0xbb2ad0: r0 = OutlineInputBorder()
    //     0xbb2ad0: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbb2ad4: mov             x2, x0
    // 0xbb2ad8: r0 = Instance_BorderRadius
    //     0xbb2ad8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbb2adc: ldr             x0, [x0, #0xf70]
    // 0xbb2ae0: stur            x2, [fp, #-0x78]
    // 0xbb2ae4: StoreField: r2->field_13 = r0
    //     0xbb2ae4: stur            w0, [x2, #0x13]
    // 0xbb2ae8: d0 = 4.000000
    //     0xbb2ae8: fmov            d0, #4.00000000
    // 0xbb2aec: StoreField: r2->field_b = d0
    //     0xbb2aec: stur            d0, [x2, #0xb]
    // 0xbb2af0: ldur            x1, [fp, #-0x80]
    // 0xbb2af4: StoreField: r2->field_7 = r1
    //     0xbb2af4: stur            w1, [x2, #7]
    // 0xbb2af8: ldur            x1, [fp, #-0x10]
    // 0xbb2afc: r0 = of()
    //     0xbb2afc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb2b00: LoadField: r1 = r0->field_87
    //     0xbb2b00: ldur            w1, [x0, #0x87]
    // 0xbb2b04: DecompressPointer r1
    //     0xbb2b04: add             x1, x1, HEAP, lsl #32
    // 0xbb2b08: LoadField: r0 = r1->field_2b
    //     0xbb2b08: ldur            w0, [x1, #0x2b]
    // 0xbb2b0c: DecompressPointer r0
    //     0xbb2b0c: add             x0, x0, HEAP, lsl #32
    // 0xbb2b10: stur            x0, [fp, #-0x80]
    // 0xbb2b14: r1 = Instance_Color
    //     0xbb2b14: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb2b18: d0 = 0.400000
    //     0xbb2b18: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbb2b1c: r0 = withOpacity()
    //     0xbb2b1c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb2b20: r16 = 14.000000
    //     0xbb2b20: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb2b24: ldr             x16, [x16, #0x1d8]
    // 0xbb2b28: stp             x0, x16, [SP]
    // 0xbb2b2c: ldur            x1, [fp, #-0x80]
    // 0xbb2b30: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb2b30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb2b34: ldr             x4, [x4, #0xaa0]
    // 0xbb2b38: r0 = copyWith()
    //     0xbb2b38: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb2b3c: ldur            x2, [fp, #-8]
    // 0xbb2b40: stur            x0, [fp, #-0x90]
    // 0xbb2b44: LoadField: r1 = r2->field_5f
    //     0xbb2b44: ldur            w1, [x2, #0x5f]
    // 0xbb2b48: DecompressPointer r1
    //     0xbb2b48: add             x1, x1, HEAP, lsl #32
    // 0xbb2b4c: tbnz            w1, #4, #0xbb2ca0
    // 0xbb2b50: LoadField: r1 = r2->field_3f
    //     0xbb2b50: ldur            w1, [x2, #0x3f]
    // 0xbb2b54: DecompressPointer r1
    //     0xbb2b54: add             x1, x1, HEAP, lsl #32
    // 0xbb2b58: LoadField: r3 = r1->field_27
    //     0xbb2b58: ldur            w3, [x1, #0x27]
    // 0xbb2b5c: DecompressPointer r3
    //     0xbb2b5c: add             x3, x3, HEAP, lsl #32
    // 0xbb2b60: LoadField: r1 = r3->field_7
    //     0xbb2b60: ldur            w1, [x3, #7]
    // 0xbb2b64: DecompressPointer r1
    //     0xbb2b64: add             x1, x1, HEAP, lsl #32
    // 0xbb2b68: LoadField: r3 = r1->field_7
    //     0xbb2b68: ldur            w3, [x1, #7]
    // 0xbb2b6c: cmp             w3, #0xc
    // 0xbb2b70: b.ne            #0xbb2bec
    // 0xbb2b74: LoadField: r1 = r2->field_b
    //     0xbb2b74: ldur            w1, [x2, #0xb]
    // 0xbb2b78: DecompressPointer r1
    //     0xbb2b78: add             x1, x1, HEAP, lsl #32
    // 0xbb2b7c: cmp             w1, NULL
    // 0xbb2b80: b.eq            #0xbb3a14
    // 0xbb2b84: LoadField: r4 = r1->field_13
    //     0xbb2b84: ldur            w4, [x1, #0x13]
    // 0xbb2b88: DecompressPointer r4
    //     0xbb2b88: add             x4, x4, HEAP, lsl #32
    // 0xbb2b8c: LoadField: r1 = r4->field_b
    //     0xbb2b8c: ldur            w1, [x4, #0xb]
    // 0xbb2b90: DecompressPointer r1
    //     0xbb2b90: add             x1, x1, HEAP, lsl #32
    // 0xbb2b94: cmp             w1, NULL
    // 0xbb2b98: b.ne            #0xbb2ba4
    // 0xbb2b9c: r1 = Null
    //     0xbb2b9c: mov             x1, NULL
    // 0xbb2ba0: b               #0xbb2bd4
    // 0xbb2ba4: LoadField: r4 = r1->field_13
    //     0xbb2ba4: ldur            w4, [x1, #0x13]
    // 0xbb2ba8: DecompressPointer r4
    //     0xbb2ba8: add             x4, x4, HEAP, lsl #32
    // 0xbb2bac: cmp             w4, NULL
    // 0xbb2bb0: b.ne            #0xbb2bbc
    // 0xbb2bb4: r1 = Null
    //     0xbb2bb4: mov             x1, NULL
    // 0xbb2bb8: b               #0xbb2bd4
    // 0xbb2bbc: LoadField: r1 = r4->field_7
    //     0xbb2bbc: ldur            w1, [x4, #7]
    // 0xbb2bc0: cbnz            w1, #0xbb2bcc
    // 0xbb2bc4: r4 = false
    //     0xbb2bc4: add             x4, NULL, #0x30  ; false
    // 0xbb2bc8: b               #0xbb2bd0
    // 0xbb2bcc: r4 = true
    //     0xbb2bcc: add             x4, NULL, #0x20  ; true
    // 0xbb2bd0: mov             x1, x4
    // 0xbb2bd4: cmp             w1, NULL
    // 0xbb2bd8: b.eq            #0xbb2bec
    // 0xbb2bdc: tbnz            w1, #4, #0xbb2bec
    // 0xbb2be0: r1 = Instance_IconData
    //     0xbb2be0: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbb2be4: ldr             x1, [x1, #0x130]
    // 0xbb2be8: b               #0xbb2bf4
    // 0xbb2bec: r1 = Instance_IconData
    //     0xbb2bec: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbb2bf0: ldr             x1, [x1, #0x138]
    // 0xbb2bf4: stur            x1, [fp, #-0x88]
    // 0xbb2bf8: cmp             w3, #0xc
    // 0xbb2bfc: b.ne            #0xbb2c78
    // 0xbb2c00: LoadField: r3 = r2->field_b
    //     0xbb2c00: ldur            w3, [x2, #0xb]
    // 0xbb2c04: DecompressPointer r3
    //     0xbb2c04: add             x3, x3, HEAP, lsl #32
    // 0xbb2c08: cmp             w3, NULL
    // 0xbb2c0c: b.eq            #0xbb3a18
    // 0xbb2c10: LoadField: r4 = r3->field_13
    //     0xbb2c10: ldur            w4, [x3, #0x13]
    // 0xbb2c14: DecompressPointer r4
    //     0xbb2c14: add             x4, x4, HEAP, lsl #32
    // 0xbb2c18: LoadField: r3 = r4->field_b
    //     0xbb2c18: ldur            w3, [x4, #0xb]
    // 0xbb2c1c: DecompressPointer r3
    //     0xbb2c1c: add             x3, x3, HEAP, lsl #32
    // 0xbb2c20: cmp             w3, NULL
    // 0xbb2c24: b.ne            #0xbb2c30
    // 0xbb2c28: r3 = Null
    //     0xbb2c28: mov             x3, NULL
    // 0xbb2c2c: b               #0xbb2c60
    // 0xbb2c30: LoadField: r4 = r3->field_f
    //     0xbb2c30: ldur            w4, [x3, #0xf]
    // 0xbb2c34: DecompressPointer r4
    //     0xbb2c34: add             x4, x4, HEAP, lsl #32
    // 0xbb2c38: cmp             w4, NULL
    // 0xbb2c3c: b.ne            #0xbb2c48
    // 0xbb2c40: r3 = Null
    //     0xbb2c40: mov             x3, NULL
    // 0xbb2c44: b               #0xbb2c60
    // 0xbb2c48: LoadField: r3 = r4->field_7
    //     0xbb2c48: ldur            w3, [x4, #7]
    // 0xbb2c4c: cbnz            w3, #0xbb2c58
    // 0xbb2c50: r4 = false
    //     0xbb2c50: add             x4, NULL, #0x30  ; false
    // 0xbb2c54: b               #0xbb2c5c
    // 0xbb2c58: r4 = true
    //     0xbb2c58: add             x4, NULL, #0x20  ; true
    // 0xbb2c5c: mov             x3, x4
    // 0xbb2c60: cmp             w3, NULL
    // 0xbb2c64: b.eq            #0xbb2c78
    // 0xbb2c68: tbnz            w3, #4, #0xbb2c78
    // 0xbb2c6c: r3 = Instance_Color
    //     0xbb2c6c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb2c70: ldr             x3, [x3, #0x858]
    // 0xbb2c74: b               #0xbb2c80
    // 0xbb2c78: r3 = Instance_Color
    //     0xbb2c78: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbb2c7c: ldr             x3, [x3, #0x50]
    // 0xbb2c80: stur            x3, [fp, #-0x80]
    // 0xbb2c84: r0 = Icon()
    //     0xbb2c84: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbb2c88: mov             x1, x0
    // 0xbb2c8c: ldur            x0, [fp, #-0x88]
    // 0xbb2c90: StoreField: r1->field_b = r0
    //     0xbb2c90: stur            w0, [x1, #0xb]
    // 0xbb2c94: ldur            x0, [fp, #-0x80]
    // 0xbb2c98: StoreField: r1->field_23 = r0
    //     0xbb2c98: stur            w0, [x1, #0x23]
    // 0xbb2c9c: b               #0xbb2ca4
    // 0xbb2ca0: r1 = Null
    //     0xbb2ca0: mov             x1, NULL
    // 0xbb2ca4: ldur            x2, [fp, #-8]
    // 0xbb2ca8: ldur            x7, [fp, #-0x30]
    // 0xbb2cac: ldur            x6, [fp, #-0x38]
    // 0xbb2cb0: ldur            x5, [fp, #-0x40]
    // 0xbb2cb4: ldur            x4, [fp, #-0x50]
    // 0xbb2cb8: ldur            x0, [fp, #-0x58]
    // 0xbb2cbc: ldur            x3, [fp, #-0x20]
    // 0xbb2cc0: ldur            x16, [fp, #-0x70]
    // 0xbb2cc4: ldur            lr, [fp, #-0x78]
    // 0xbb2cc8: stp             lr, x16, [SP, #0x18]
    // 0xbb2ccc: r16 = "State*"
    //     0xbb2ccc: add             x16, PP, #0x54, lsl #12  ; [pp+0x54108] "State*"
    //     0xbb2cd0: ldr             x16, [x16, #0x108]
    // 0xbb2cd4: ldur            lr, [fp, #-0x90]
    // 0xbb2cd8: stp             lr, x16, [SP, #8]
    // 0xbb2cdc: str             x1, [SP]
    // 0xbb2ce0: ldur            x1, [fp, #-0x68]
    // 0xbb2ce4: r4 = const [0, 0x6, 0x5, 0x1, fillColor, 0x1, focusedBorder, 0x2, labelStyle, 0x4, labelText, 0x3, suffixIcon, 0x5, null]
    //     0xbb2ce4: add             x4, PP, #0x54, lsl #12  ; [pp+0x54110] List(15) [0, 0x6, 0x5, 0x1, "fillColor", 0x1, "focusedBorder", 0x2, "labelStyle", 0x4, "labelText", 0x3, "suffixIcon", 0x5, Null]
    //     0xbb2ce8: ldr             x4, [x4, #0x110]
    // 0xbb2cec: r0 = copyWith()
    //     0xbb2cec: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbb2cf0: r1 = <String>
    //     0xbb2cf0: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbb2cf4: stur            x0, [fp, #-0x68]
    // 0xbb2cf8: r0 = TextFormField()
    //     0xbb2cf8: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbb2cfc: stur            x0, [fp, #-0x70]
    // 0xbb2d00: r16 = true
    //     0xbb2d00: add             x16, NULL, #0x20  ; true
    // 0xbb2d04: r30 = true
    //     0xbb2d04: add             lr, NULL, #0x20  ; true
    // 0xbb2d08: stp             lr, x16, [SP, #0x10]
    // 0xbb2d0c: ldur            x16, [fp, #-0x60]
    // 0xbb2d10: ldur            lr, [fp, #-0x18]
    // 0xbb2d14: stp             lr, x16, [SP]
    // 0xbb2d18: mov             x1, x0
    // 0xbb2d1c: ldur            x2, [fp, #-0x68]
    // 0xbb2d20: r4 = const [0, 0x6, 0x4, 0x2, controller, 0x4, ignorePointers, 0x3, readOnly, 0x2, style, 0x5, null]
    //     0xbb2d20: add             x4, PP, #0x54, lsl #12  ; [pp+0x54100] List(13) [0, 0x6, 0x4, 0x2, "controller", 0x4, "ignorePointers", 0x3, "readOnly", 0x2, "style", 0x5, Null]
    //     0xbb2d24: ldr             x4, [x4, #0x100]
    // 0xbb2d28: r0 = TextFormField()
    //     0xbb2d28: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbb2d2c: r0 = Form()
    //     0xbb2d2c: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbb2d30: mov             x2, x0
    // 0xbb2d34: ldur            x0, [fp, #-0x70]
    // 0xbb2d38: stur            x2, [fp, #-0x18]
    // 0xbb2d3c: StoreField: r2->field_b = r0
    //     0xbb2d3c: stur            w0, [x2, #0xb]
    // 0xbb2d40: r0 = Instance_AutovalidateMode
    //     0xbb2d40: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbb2d44: ldr             x0, [x0, #0x800]
    // 0xbb2d48: StoreField: r2->field_23 = r0
    //     0xbb2d48: stur            w0, [x2, #0x23]
    // 0xbb2d4c: ldur            x1, [fp, #-0x20]
    // 0xbb2d50: StoreField: r2->field_7 = r1
    //     0xbb2d50: stur            w1, [x2, #7]
    // 0xbb2d54: r1 = <FlexParentData>
    //     0xbb2d54: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbb2d58: ldr             x1, [x1, #0xe00]
    // 0xbb2d5c: r0 = Flexible()
    //     0xbb2d5c: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xbb2d60: mov             x3, x0
    // 0xbb2d64: r0 = 1
    //     0xbb2d64: movz            x0, #0x1
    // 0xbb2d68: stur            x3, [fp, #-0x20]
    // 0xbb2d6c: StoreField: r3->field_13 = r0
    //     0xbb2d6c: stur            x0, [x3, #0x13]
    // 0xbb2d70: r0 = Instance_FlexFit
    //     0xbb2d70: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbb2d74: ldr             x0, [x0, #0xe08]
    // 0xbb2d78: StoreField: r3->field_1b = r0
    //     0xbb2d78: stur            w0, [x3, #0x1b]
    // 0xbb2d7c: ldur            x0, [fp, #-0x18]
    // 0xbb2d80: StoreField: r3->field_b = r0
    //     0xbb2d80: stur            w0, [x3, #0xb]
    // 0xbb2d84: r1 = Null
    //     0xbb2d84: mov             x1, NULL
    // 0xbb2d88: r2 = 6
    //     0xbb2d88: movz            x2, #0x6
    // 0xbb2d8c: r0 = AllocateArray()
    //     0xbb2d8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb2d90: mov             x2, x0
    // 0xbb2d94: ldur            x0, [fp, #-0x58]
    // 0xbb2d98: stur            x2, [fp, #-0x18]
    // 0xbb2d9c: StoreField: r2->field_f = r0
    //     0xbb2d9c: stur            w0, [x2, #0xf]
    // 0xbb2da0: r16 = Instance_SizedBox
    //     0xbb2da0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xbb2da4: ldr             x16, [x16, #0x998]
    // 0xbb2da8: StoreField: r2->field_13 = r16
    //     0xbb2da8: stur            w16, [x2, #0x13]
    // 0xbb2dac: ldur            x0, [fp, #-0x20]
    // 0xbb2db0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb2db0: stur            w0, [x2, #0x17]
    // 0xbb2db4: r1 = <Widget>
    //     0xbb2db4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb2db8: r0 = AllocateGrowableArray()
    //     0xbb2db8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb2dbc: mov             x1, x0
    // 0xbb2dc0: ldur            x0, [fp, #-0x18]
    // 0xbb2dc4: stur            x1, [fp, #-0x20]
    // 0xbb2dc8: StoreField: r1->field_f = r0
    //     0xbb2dc8: stur            w0, [x1, #0xf]
    // 0xbb2dcc: r2 = 6
    //     0xbb2dcc: movz            x2, #0x6
    // 0xbb2dd0: StoreField: r1->field_b = r2
    //     0xbb2dd0: stur            w2, [x1, #0xb]
    // 0xbb2dd4: r0 = Row()
    //     0xbb2dd4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbb2dd8: mov             x1, x0
    // 0xbb2ddc: r0 = Instance_Axis
    //     0xbb2ddc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb2de0: stur            x1, [fp, #-0x18]
    // 0xbb2de4: StoreField: r1->field_f = r0
    //     0xbb2de4: stur            w0, [x1, #0xf]
    // 0xbb2de8: r0 = Instance_MainAxisAlignment
    //     0xbb2de8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xbb2dec: ldr             x0, [x0, #0xd10]
    // 0xbb2df0: StoreField: r1->field_13 = r0
    //     0xbb2df0: stur            w0, [x1, #0x13]
    // 0xbb2df4: r0 = Instance_MainAxisSize
    //     0xbb2df4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb2df8: ldr             x0, [x0, #0xa10]
    // 0xbb2dfc: ArrayStore: r1[0] = r0  ; List_4
    //     0xbb2dfc: stur            w0, [x1, #0x17]
    // 0xbb2e00: r2 = Instance_CrossAxisAlignment
    //     0xbb2e00: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbb2e04: ldr             x2, [x2, #0x890]
    // 0xbb2e08: StoreField: r1->field_1b = r2
    //     0xbb2e08: stur            w2, [x1, #0x1b]
    // 0xbb2e0c: r3 = Instance_VerticalDirection
    //     0xbb2e0c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb2e10: ldr             x3, [x3, #0xa20]
    // 0xbb2e14: StoreField: r1->field_23 = r3
    //     0xbb2e14: stur            w3, [x1, #0x23]
    // 0xbb2e18: r4 = Instance_Clip
    //     0xbb2e18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb2e1c: ldr             x4, [x4, #0x38]
    // 0xbb2e20: StoreField: r1->field_2b = r4
    //     0xbb2e20: stur            w4, [x1, #0x2b]
    // 0xbb2e24: StoreField: r1->field_2f = rZR
    //     0xbb2e24: stur            xzr, [x1, #0x2f]
    // 0xbb2e28: ldur            x5, [fp, #-0x20]
    // 0xbb2e2c: StoreField: r1->field_b = r5
    //     0xbb2e2c: stur            w5, [x1, #0xb]
    // 0xbb2e30: r0 = Container()
    //     0xbb2e30: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbb2e34: stur            x0, [fp, #-0x20]
    // 0xbb2e38: r16 = Instance_EdgeInsets
    //     0xbb2e38: add             x16, PP, #0x42, lsl #12  ; [pp+0x42620] Obj!EdgeInsets@d57e01
    //     0xbb2e3c: ldr             x16, [x16, #0x620]
    // 0xbb2e40: ldur            lr, [fp, #-0x18]
    // 0xbb2e44: stp             lr, x16, [SP]
    // 0xbb2e48: mov             x1, x0
    // 0xbb2e4c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, margin, 0x1, null]
    //     0xbb2e4c: add             x4, PP, #0x53, lsl #12  ; [pp+0x53400] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "margin", 0x1, Null]
    //     0xbb2e50: ldr             x4, [x4, #0x400]
    // 0xbb2e54: r0 = Container()
    //     0xbb2e54: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbb2e58: r1 = Null
    //     0xbb2e58: mov             x1, NULL
    // 0xbb2e5c: r2 = 10
    //     0xbb2e5c: movz            x2, #0xa
    // 0xbb2e60: r0 = AllocateArray()
    //     0xbb2e60: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb2e64: mov             x2, x0
    // 0xbb2e68: ldur            x0, [fp, #-0x30]
    // 0xbb2e6c: stur            x2, [fp, #-0x18]
    // 0xbb2e70: StoreField: r2->field_f = r0
    //     0xbb2e70: stur            w0, [x2, #0xf]
    // 0xbb2e74: ldur            x0, [fp, #-0x38]
    // 0xbb2e78: StoreField: r2->field_13 = r0
    //     0xbb2e78: stur            w0, [x2, #0x13]
    // 0xbb2e7c: ldur            x0, [fp, #-0x40]
    // 0xbb2e80: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb2e80: stur            w0, [x2, #0x17]
    // 0xbb2e84: ldur            x0, [fp, #-0x50]
    // 0xbb2e88: StoreField: r2->field_1b = r0
    //     0xbb2e88: stur            w0, [x2, #0x1b]
    // 0xbb2e8c: ldur            x0, [fp, #-0x20]
    // 0xbb2e90: StoreField: r2->field_1f = r0
    //     0xbb2e90: stur            w0, [x2, #0x1f]
    // 0xbb2e94: r1 = <Widget>
    //     0xbb2e94: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb2e98: r0 = AllocateGrowableArray()
    //     0xbb2e98: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb2e9c: mov             x1, x0
    // 0xbb2ea0: ldur            x0, [fp, #-0x18]
    // 0xbb2ea4: stur            x1, [fp, #-0x20]
    // 0xbb2ea8: StoreField: r1->field_f = r0
    //     0xbb2ea8: stur            w0, [x1, #0xf]
    // 0xbb2eac: r0 = 10
    //     0xbb2eac: movz            x0, #0xa
    // 0xbb2eb0: StoreField: r1->field_b = r0
    //     0xbb2eb0: stur            w0, [x1, #0xb]
    // 0xbb2eb4: r0 = Column()
    //     0xbb2eb4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb2eb8: mov             x4, x0
    // 0xbb2ebc: r3 = Instance_Axis
    //     0xbb2ebc: ldr             x3, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbb2ec0: stur            x4, [fp, #-0x18]
    // 0xbb2ec4: StoreField: r4->field_f = r3
    //     0xbb2ec4: stur            w3, [x4, #0xf]
    // 0xbb2ec8: r5 = Instance_MainAxisAlignment
    //     0xbb2ec8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb2ecc: ldr             x5, [x5, #0xa08]
    // 0xbb2ed0: StoreField: r4->field_13 = r5
    //     0xbb2ed0: stur            w5, [x4, #0x13]
    // 0xbb2ed4: r6 = Instance_MainAxisSize
    //     0xbb2ed4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb2ed8: ldr             x6, [x6, #0xa10]
    // 0xbb2edc: ArrayStore: r4[0] = r6  ; List_4
    //     0xbb2edc: stur            w6, [x4, #0x17]
    // 0xbb2ee0: r0 = Instance_CrossAxisAlignment
    //     0xbb2ee0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb2ee4: ldr             x0, [x0, #0xa18]
    // 0xbb2ee8: StoreField: r4->field_1b = r0
    //     0xbb2ee8: stur            w0, [x4, #0x1b]
    // 0xbb2eec: r7 = Instance_VerticalDirection
    //     0xbb2eec: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb2ef0: ldr             x7, [x7, #0xa20]
    // 0xbb2ef4: StoreField: r4->field_23 = r7
    //     0xbb2ef4: stur            w7, [x4, #0x23]
    // 0xbb2ef8: r8 = Instance_Clip
    //     0xbb2ef8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb2efc: ldr             x8, [x8, #0x38]
    // 0xbb2f00: StoreField: r4->field_2b = r8
    //     0xbb2f00: stur            w8, [x4, #0x2b]
    // 0xbb2f04: StoreField: r4->field_2f = rZR
    //     0xbb2f04: stur            xzr, [x4, #0x2f]
    // 0xbb2f08: ldur            x0, [fp, #-0x20]
    // 0xbb2f0c: StoreField: r4->field_b = r0
    //     0xbb2f0c: stur            w0, [x4, #0xb]
    // 0xbb2f10: ldur            x9, [fp, #-8]
    // 0xbb2f14: LoadField: r0 = r9->field_b
    //     0xbb2f14: ldur            w0, [x9, #0xb]
    // 0xbb2f18: DecompressPointer r0
    //     0xbb2f18: add             x0, x0, HEAP, lsl #32
    // 0xbb2f1c: cmp             w0, NULL
    // 0xbb2f20: b.eq            #0xbb3a1c
    // 0xbb2f24: LoadField: r1 = r0->field_b
    //     0xbb2f24: ldur            w1, [x0, #0xb]
    // 0xbb2f28: DecompressPointer r1
    //     0xbb2f28: add             x1, x1, HEAP, lsl #32
    // 0xbb2f2c: LoadField: r0 = r1->field_f
    //     0xbb2f2c: ldur            w0, [x1, #0xf]
    // 0xbb2f30: DecompressPointer r0
    //     0xbb2f30: add             x0, x0, HEAP, lsl #32
    // 0xbb2f34: cmp             w0, NULL
    // 0xbb2f38: b.ne            #0xbb2f44
    // 0xbb2f3c: r0 = Null
    //     0xbb2f3c: mov             x0, NULL
    // 0xbb2f40: b               #0xbb2f70
    // 0xbb2f44: r1 = LoadClassIdInstr(r0)
    //     0xbb2f44: ldur            x1, [x0, #-1]
    //     0xbb2f48: ubfx            x1, x1, #0xc, #0x14
    // 0xbb2f4c: mov             x16, x0
    // 0xbb2f50: mov             x0, x1
    // 0xbb2f54: mov             x1, x16
    // 0xbb2f58: r2 = "landmark"
    //     0xbb2f58: add             x2, PP, #0x24, lsl #12  ; [pp+0x24930] "landmark"
    //     0xbb2f5c: ldr             x2, [x2, #0x930]
    // 0xbb2f60: r0 = GDT[cid_x0 + 0xe437]()
    //     0xbb2f60: movz            x17, #0xe437
    //     0xbb2f64: add             lr, x0, x17
    //     0xbb2f68: ldr             lr, [x21, lr, lsl #3]
    //     0xbb2f6c: blr             lr
    // 0xbb2f70: cmp             w0, NULL
    // 0xbb2f74: b.ne            #0xbb2f7c
    // 0xbb2f78: r0 = false
    //     0xbb2f78: add             x0, NULL, #0x30  ; false
    // 0xbb2f7c: ldur            x2, [fp, #-8]
    // 0xbb2f80: stur            x0, [fp, #-0x30]
    // 0xbb2f84: LoadField: r1 = r2->field_1b
    //     0xbb2f84: ldur            w1, [x2, #0x1b]
    // 0xbb2f88: DecompressPointer r1
    //     0xbb2f88: add             x1, x1, HEAP, lsl #32
    // 0xbb2f8c: stur            x1, [fp, #-0x20]
    // 0xbb2f90: r0 = LengthLimitingTextInputFormatter()
    //     0xbb2f90: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbb2f94: mov             x1, x0
    // 0xbb2f98: r0 = 240
    //     0xbb2f98: movz            x0, #0xf0
    // 0xbb2f9c: stur            x1, [fp, #-0x38]
    // 0xbb2fa0: StoreField: r1->field_7 = r0
    //     0xbb2fa0: stur            w0, [x1, #7]
    // 0xbb2fa4: r16 = "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xbb2fa4: add             x16, PP, #0x54, lsl #12  ; [pp+0x540a8] "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xbb2fa8: ldr             x16, [x16, #0xa8]
    // 0xbb2fac: stp             x16, NULL, [SP, #0x20]
    // 0xbb2fb0: r16 = false
    //     0xbb2fb0: add             x16, NULL, #0x30  ; false
    // 0xbb2fb4: r30 = true
    //     0xbb2fb4: add             lr, NULL, #0x20  ; true
    // 0xbb2fb8: stp             lr, x16, [SP, #0x10]
    // 0xbb2fbc: r16 = false
    //     0xbb2fbc: add             x16, NULL, #0x30  ; false
    // 0xbb2fc0: r30 = false
    //     0xbb2fc0: add             lr, NULL, #0x30  ; false
    // 0xbb2fc4: stp             lr, x16, [SP]
    // 0xbb2fc8: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xbb2fc8: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xbb2fcc: r0 = _RegExp()
    //     0xbb2fcc: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xbb2fd0: stur            x0, [fp, #-0x40]
    // 0xbb2fd4: r0 = FilteringTextInputFormatter()
    //     0xbb2fd4: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xbb2fd8: mov             x3, x0
    // 0xbb2fdc: ldur            x0, [fp, #-0x40]
    // 0xbb2fe0: stur            x3, [fp, #-0x50]
    // 0xbb2fe4: StoreField: r3->field_b = r0
    //     0xbb2fe4: stur            w0, [x3, #0xb]
    // 0xbb2fe8: r0 = true
    //     0xbb2fe8: add             x0, NULL, #0x20  ; true
    // 0xbb2fec: StoreField: r3->field_7 = r0
    //     0xbb2fec: stur            w0, [x3, #7]
    // 0xbb2ff0: r4 = ""
    //     0xbb2ff0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb2ff4: StoreField: r3->field_f = r4
    //     0xbb2ff4: stur            w4, [x3, #0xf]
    // 0xbb2ff8: r1 = Null
    //     0xbb2ff8: mov             x1, NULL
    // 0xbb2ffc: r2 = 4
    //     0xbb2ffc: movz            x2, #0x4
    // 0xbb3000: r0 = AllocateArray()
    //     0xbb3000: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb3004: mov             x2, x0
    // 0xbb3008: ldur            x0, [fp, #-0x38]
    // 0xbb300c: stur            x2, [fp, #-0x40]
    // 0xbb3010: StoreField: r2->field_f = r0
    //     0xbb3010: stur            w0, [x2, #0xf]
    // 0xbb3014: ldur            x0, [fp, #-0x50]
    // 0xbb3018: StoreField: r2->field_13 = r0
    //     0xbb3018: stur            w0, [x2, #0x13]
    // 0xbb301c: r1 = <TextInputFormatter>
    //     0xbb301c: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbb3020: ldr             x1, [x1, #0x7b0]
    // 0xbb3024: r0 = AllocateGrowableArray()
    //     0xbb3024: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb3028: mov             x2, x0
    // 0xbb302c: ldur            x0, [fp, #-0x40]
    // 0xbb3030: stur            x2, [fp, #-0x50]
    // 0xbb3034: StoreField: r2->field_f = r0
    //     0xbb3034: stur            w0, [x2, #0xf]
    // 0xbb3038: r0 = 4
    //     0xbb3038: movz            x0, #0x4
    // 0xbb303c: StoreField: r2->field_b = r0
    //     0xbb303c: stur            w0, [x2, #0xb]
    // 0xbb3040: ldur            x0, [fp, #-8]
    // 0xbb3044: LoadField: r3 = r0->field_37
    //     0xbb3044: ldur            w3, [x0, #0x37]
    // 0xbb3048: DecompressPointer r3
    //     0xbb3048: add             x3, x3, HEAP, lsl #32
    // 0xbb304c: ldur            x1, [fp, #-0x10]
    // 0xbb3050: stur            x3, [fp, #-0x38]
    // 0xbb3054: r0 = of()
    //     0xbb3054: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb3058: LoadField: r1 = r0->field_87
    //     0xbb3058: ldur            w1, [x0, #0x87]
    // 0xbb305c: DecompressPointer r1
    //     0xbb305c: add             x1, x1, HEAP, lsl #32
    // 0xbb3060: LoadField: r0 = r1->field_2b
    //     0xbb3060: ldur            w0, [x1, #0x2b]
    // 0xbb3064: DecompressPointer r0
    //     0xbb3064: add             x0, x0, HEAP, lsl #32
    // 0xbb3068: ldur            x1, [fp, #-0x10]
    // 0xbb306c: stur            x0, [fp, #-0x40]
    // 0xbb3070: r0 = of()
    //     0xbb3070: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb3074: LoadField: r1 = r0->field_5b
    //     0xbb3074: ldur            w1, [x0, #0x5b]
    // 0xbb3078: DecompressPointer r1
    //     0xbb3078: add             x1, x1, HEAP, lsl #32
    // 0xbb307c: r16 = 14.000000
    //     0xbb307c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb3080: ldr             x16, [x16, #0x1d8]
    // 0xbb3084: stp             x1, x16, [SP]
    // 0xbb3088: ldur            x1, [fp, #-0x40]
    // 0xbb308c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb308c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb3090: ldr             x4, [x4, #0xaa0]
    // 0xbb3094: r0 = copyWith()
    //     0xbb3094: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb3098: ldur            x1, [fp, #-0x10]
    // 0xbb309c: stur            x0, [fp, #-0x40]
    // 0xbb30a0: r0 = getTextFormFieldInputDecoration()
    //     0xbb30a0: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbb30a4: ldur            x1, [fp, #-0x10]
    // 0xbb30a8: stur            x0, [fp, #-0x58]
    // 0xbb30ac: r0 = of()
    //     0xbb30ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb30b0: LoadField: r1 = r0->field_5b
    //     0xbb30b0: ldur            w1, [x0, #0x5b]
    // 0xbb30b4: DecompressPointer r1
    //     0xbb30b4: add             x1, x1, HEAP, lsl #32
    // 0xbb30b8: stur            x1, [fp, #-0x60]
    // 0xbb30bc: r0 = BorderSide()
    //     0xbb30bc: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbb30c0: mov             x1, x0
    // 0xbb30c4: ldur            x0, [fp, #-0x60]
    // 0xbb30c8: stur            x1, [fp, #-0x68]
    // 0xbb30cc: StoreField: r1->field_7 = r0
    //     0xbb30cc: stur            w0, [x1, #7]
    // 0xbb30d0: d0 = 1.000000
    //     0xbb30d0: fmov            d0, #1.00000000
    // 0xbb30d4: StoreField: r1->field_b = d0
    //     0xbb30d4: stur            d0, [x1, #0xb]
    // 0xbb30d8: r0 = Instance_BorderStyle
    //     0xbb30d8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbb30dc: ldr             x0, [x0, #0xf68]
    // 0xbb30e0: StoreField: r1->field_13 = r0
    //     0xbb30e0: stur            w0, [x1, #0x13]
    // 0xbb30e4: d1 = -1.000000
    //     0xbb30e4: fmov            d1, #-1.00000000
    // 0xbb30e8: ArrayStore: r1[0] = d1  ; List_8
    //     0xbb30e8: stur            d1, [x1, #0x17]
    // 0xbb30ec: r0 = OutlineInputBorder()
    //     0xbb30ec: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbb30f0: mov             x2, x0
    // 0xbb30f4: r0 = Instance_BorderRadius
    //     0xbb30f4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbb30f8: ldr             x0, [x0, #0xf70]
    // 0xbb30fc: stur            x2, [fp, #-0x70]
    // 0xbb3100: StoreField: r2->field_13 = r0
    //     0xbb3100: stur            w0, [x2, #0x13]
    // 0xbb3104: d0 = 4.000000
    //     0xbb3104: fmov            d0, #4.00000000
    // 0xbb3108: StoreField: r2->field_b = d0
    //     0xbb3108: stur            d0, [x2, #0xb]
    // 0xbb310c: ldur            x1, [fp, #-0x68]
    // 0xbb3110: StoreField: r2->field_7 = r1
    //     0xbb3110: stur            w1, [x2, #7]
    // 0xbb3114: ldur            x3, [fp, #-8]
    // 0xbb3118: LoadField: r1 = r3->field_37
    //     0xbb3118: ldur            w1, [x3, #0x37]
    // 0xbb311c: DecompressPointer r1
    //     0xbb311c: add             x1, x1, HEAP, lsl #32
    // 0xbb3120: LoadField: r4 = r1->field_27
    //     0xbb3120: ldur            w4, [x1, #0x27]
    // 0xbb3124: DecompressPointer r4
    //     0xbb3124: add             x4, x4, HEAP, lsl #32
    // 0xbb3128: LoadField: r1 = r4->field_7
    //     0xbb3128: ldur            w1, [x4, #7]
    // 0xbb312c: DecompressPointer r1
    //     0xbb312c: add             x1, x1, HEAP, lsl #32
    // 0xbb3130: LoadField: r4 = r1->field_7
    //     0xbb3130: ldur            w4, [x1, #7]
    // 0xbb3134: cbnz            w4, #0xbb3144
    // 0xbb3138: r4 = "Landmark"
    //     0xbb3138: add             x4, PP, #0x54, lsl #12  ; [pp+0x54118] "Landmark"
    //     0xbb313c: ldr             x4, [x4, #0x118]
    // 0xbb3140: b               #0xbb314c
    // 0xbb3144: r4 = "Nearby Famous Place / Shop / School, etc. (Optional)"
    //     0xbb3144: add             x4, PP, #0x54, lsl #12  ; [pp+0x54120] "Nearby Famous Place / Shop / School, etc. (Optional)"
    //     0xbb3148: ldr             x4, [x4, #0x120]
    // 0xbb314c: ldur            x1, [fp, #-0x10]
    // 0xbb3150: stur            x4, [fp, #-0x60]
    // 0xbb3154: r0 = of()
    //     0xbb3154: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb3158: LoadField: r1 = r0->field_87
    //     0xbb3158: ldur            w1, [x0, #0x87]
    // 0xbb315c: DecompressPointer r1
    //     0xbb315c: add             x1, x1, HEAP, lsl #32
    // 0xbb3160: LoadField: r0 = r1->field_2b
    //     0xbb3160: ldur            w0, [x1, #0x2b]
    // 0xbb3164: DecompressPointer r0
    //     0xbb3164: add             x0, x0, HEAP, lsl #32
    // 0xbb3168: stur            x0, [fp, #-0x68]
    // 0xbb316c: r1 = Instance_Color
    //     0xbb316c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb3170: d0 = 0.400000
    //     0xbb3170: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbb3174: r0 = withOpacity()
    //     0xbb3174: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb3178: r16 = 12.000000
    //     0xbb3178: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb317c: ldr             x16, [x16, #0x9e8]
    // 0xbb3180: stp             x0, x16, [SP]
    // 0xbb3184: ldur            x1, [fp, #-0x68]
    // 0xbb3188: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb3188: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb318c: ldr             x4, [x4, #0xaa0]
    // 0xbb3190: r0 = copyWith()
    //     0xbb3190: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb3194: ldur            x1, [fp, #-0x10]
    // 0xbb3198: stur            x0, [fp, #-0x68]
    // 0xbb319c: r0 = of()
    //     0xbb319c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb31a0: LoadField: r1 = r0->field_87
    //     0xbb31a0: ldur            w1, [x0, #0x87]
    // 0xbb31a4: DecompressPointer r1
    //     0xbb31a4: add             x1, x1, HEAP, lsl #32
    // 0xbb31a8: LoadField: r0 = r1->field_2b
    //     0xbb31a8: ldur            w0, [x1, #0x2b]
    // 0xbb31ac: DecompressPointer r0
    //     0xbb31ac: add             x0, x0, HEAP, lsl #32
    // 0xbb31b0: r16 = 12.000000
    //     0xbb31b0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb31b4: ldr             x16, [x16, #0x9e8]
    // 0xbb31b8: r30 = Instance_MaterialColor
    //     0xbb31b8: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xbb31bc: ldr             lr, [lr, #0x180]
    // 0xbb31c0: stp             lr, x16, [SP]
    // 0xbb31c4: mov             x1, x0
    // 0xbb31c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb31c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb31cc: ldr             x4, [x4, #0xaa0]
    // 0xbb31d0: r0 = copyWith()
    //     0xbb31d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb31d4: ldur            x2, [fp, #-8]
    // 0xbb31d8: stur            x0, [fp, #-0x88]
    // 0xbb31dc: LoadField: r1 = r2->field_63
    //     0xbb31dc: ldur            w1, [x2, #0x63]
    // 0xbb31e0: DecompressPointer r1
    //     0xbb31e0: add             x1, x1, HEAP, lsl #32
    // 0xbb31e4: tbnz            w1, #4, #0xbb327c
    // 0xbb31e8: LoadField: r1 = r2->field_37
    //     0xbb31e8: ldur            w1, [x2, #0x37]
    // 0xbb31ec: DecompressPointer r1
    //     0xbb31ec: add             x1, x1, HEAP, lsl #32
    // 0xbb31f0: LoadField: r3 = r1->field_27
    //     0xbb31f0: ldur            w3, [x1, #0x27]
    // 0xbb31f4: DecompressPointer r3
    //     0xbb31f4: add             x3, x3, HEAP, lsl #32
    // 0xbb31f8: LoadField: r1 = r3->field_7
    //     0xbb31f8: ldur            w1, [x3, #7]
    // 0xbb31fc: DecompressPointer r1
    //     0xbb31fc: add             x1, x1, HEAP, lsl #32
    // 0xbb3200: LoadField: r3 = r1->field_7
    //     0xbb3200: ldur            w3, [x1, #7]
    // 0xbb3204: cbz             w3, #0xbb3220
    // 0xbb3208: r1 = LoadInt32Instr(r3)
    //     0xbb3208: sbfx            x1, x3, #1, #0x1f
    // 0xbb320c: cmp             x1, #5
    // 0xbb3210: b.lt            #0xbb3220
    // 0xbb3214: r1 = Instance_IconData
    //     0xbb3214: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbb3218: ldr             x1, [x1, #0x130]
    // 0xbb321c: b               #0xbb3234
    // 0xbb3220: cbz             w3, #0xbb3230
    // 0xbb3224: r1 = Instance_IconData
    //     0xbb3224: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbb3228: ldr             x1, [x1, #0x138]
    // 0xbb322c: b               #0xbb3234
    // 0xbb3230: r1 = Null
    //     0xbb3230: mov             x1, NULL
    // 0xbb3234: stur            x1, [fp, #-0x80]
    // 0xbb3238: cbz             w3, #0xbb3254
    // 0xbb323c: r4 = LoadInt32Instr(r3)
    //     0xbb323c: sbfx            x4, x3, #1, #0x1f
    // 0xbb3240: cmp             x4, #5
    // 0xbb3244: b.lt            #0xbb3254
    // 0xbb3248: r3 = Instance_Color
    //     0xbb3248: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb324c: ldr             x3, [x3, #0x858]
    // 0xbb3250: b               #0xbb325c
    // 0xbb3254: r3 = Instance_Color
    //     0xbb3254: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbb3258: ldr             x3, [x3, #0x50]
    // 0xbb325c: stur            x3, [fp, #-0x78]
    // 0xbb3260: r0 = Icon()
    //     0xbb3260: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbb3264: mov             x1, x0
    // 0xbb3268: ldur            x0, [fp, #-0x80]
    // 0xbb326c: StoreField: r1->field_b = r0
    //     0xbb326c: stur            w0, [x1, #0xb]
    // 0xbb3270: ldur            x0, [fp, #-0x78]
    // 0xbb3274: StoreField: r1->field_23 = r0
    //     0xbb3274: stur            w0, [x1, #0x23]
    // 0xbb3278: b               #0xbb3280
    // 0xbb327c: r1 = Null
    //     0xbb327c: mov             x1, NULL
    // 0xbb3280: ldur            x2, [fp, #-8]
    // 0xbb3284: ldur            x0, [fp, #-0x30]
    // 0xbb3288: ldur            x3, [fp, #-0x20]
    // 0xbb328c: ldur            x16, [fp, #-0x70]
    // 0xbb3290: r30 = Instance_EdgeInsets
    //     0xbb3290: add             lr, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xbb3294: ldr             lr, [lr, #0xc40]
    // 0xbb3298: stp             lr, x16, [SP, #0x20]
    // 0xbb329c: ldur            x16, [fp, #-0x60]
    // 0xbb32a0: ldur            lr, [fp, #-0x68]
    // 0xbb32a4: stp             lr, x16, [SP, #0x10]
    // 0xbb32a8: ldur            x16, [fp, #-0x88]
    // 0xbb32ac: stp             x1, x16, [SP]
    // 0xbb32b0: ldur            x1, [fp, #-0x58]
    // 0xbb32b4: r4 = const [0, 0x7, 0x6, 0x1, contentPadding, 0x2, errorStyle, 0x5, focusedBorder, 0x1, labelStyle, 0x4, labelText, 0x3, suffixIcon, 0x6, null]
    //     0xbb32b4: add             x4, PP, #0x54, lsl #12  ; [pp+0x54050] List(17) [0, 0x7, 0x6, 0x1, "contentPadding", 0x2, "errorStyle", 0x5, "focusedBorder", 0x1, "labelStyle", 0x4, "labelText", 0x3, "suffixIcon", 0x6, Null]
    //     0xbb32b8: ldr             x4, [x4, #0x50]
    // 0xbb32bc: r0 = copyWith()
    //     0xbb32bc: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbb32c0: ldur            x2, [fp, #-8]
    // 0xbb32c4: r1 = Function '_validateLandmark@1669306702':.
    //     0xbb32c4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54128] AnonymousClosure: (0xbb3a60), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateLandmark (0xa054c0)
    //     0xbb32c8: ldr             x1, [x1, #0x128]
    // 0xbb32cc: stur            x0, [fp, #-0x58]
    // 0xbb32d0: r0 = AllocateClosure()
    //     0xbb32d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb32d4: ldur            x2, [fp, #-0x28]
    // 0xbb32d8: r1 = Function '<anonymous closure>':.
    //     0xbb32d8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54130] AnonymousClosure: (0xa01a3c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xbb32dc: ldr             x1, [x1, #0x130]
    // 0xbb32e0: stur            x0, [fp, #-0x60]
    // 0xbb32e4: r0 = AllocateClosure()
    //     0xbb32e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb32e8: r1 = <String>
    //     0xbb32e8: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbb32ec: stur            x0, [fp, #-0x68]
    // 0xbb32f0: r0 = TextFormField()
    //     0xbb32f0: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbb32f4: stur            x0, [fp, #-0x70]
    // 0xbb32f8: ldur            x16, [fp, #-0x60]
    // 0xbb32fc: r30 = true
    //     0xbb32fc: add             lr, NULL, #0x20  ; true
    // 0xbb3300: stp             lr, x16, [SP, #0x38]
    // 0xbb3304: r16 = Instance_AutovalidateMode
    //     0xbb3304: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbb3308: ldr             x16, [x16, #0x7e8]
    // 0xbb330c: ldur            lr, [fp, #-0x50]
    // 0xbb3310: stp             lr, x16, [SP, #0x28]
    // 0xbb3314: r16 = Instance_TextInputType
    //     0xbb3314: add             x16, PP, #0x54, lsl #12  ; [pp+0x54068] Obj!TextInputType@d55ba1
    //     0xbb3318: ldr             x16, [x16, #0x68]
    // 0xbb331c: r30 = 2
    //     0xbb331c: movz            lr, #0x2
    // 0xbb3320: stp             lr, x16, [SP, #0x18]
    // 0xbb3324: ldur            x16, [fp, #-0x38]
    // 0xbb3328: ldur            lr, [fp, #-0x40]
    // 0xbb332c: stp             lr, x16, [SP, #8]
    // 0xbb3330: ldur            x16, [fp, #-0x68]
    // 0xbb3334: str             x16, [SP]
    // 0xbb3338: mov             x1, x0
    // 0xbb333c: ldur            x2, [fp, #-0x58]
    // 0xbb3340: r4 = const [0, 0xb, 0x9, 0x2, autovalidateMode, 0x4, controller, 0x8, enableSuggestions, 0x3, inputFormatters, 0x5, keyboardType, 0x6, maxLines, 0x7, onChanged, 0xa, style, 0x9, validator, 0x2, null]
    //     0xbb3340: add             x4, PP, #0x54, lsl #12  ; [pp+0x54138] List(23) [0, 0xb, 0x9, 0x2, "autovalidateMode", 0x4, "controller", 0x8, "enableSuggestions", 0x3, "inputFormatters", 0x5, "keyboardType", 0x6, "maxLines", 0x7, "onChanged", 0xa, "style", 0x9, "validator", 0x2, Null]
    //     0xbb3344: ldr             x4, [x4, #0x138]
    // 0xbb3348: r0 = TextFormField()
    //     0xbb3348: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbb334c: r0 = Form()
    //     0xbb334c: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbb3350: mov             x1, x0
    // 0xbb3354: ldur            x0, [fp, #-0x70]
    // 0xbb3358: stur            x1, [fp, #-0x38]
    // 0xbb335c: StoreField: r1->field_b = r0
    //     0xbb335c: stur            w0, [x1, #0xb]
    // 0xbb3360: r0 = Instance_AutovalidateMode
    //     0xbb3360: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbb3364: ldr             x0, [x0, #0x800]
    // 0xbb3368: StoreField: r1->field_23 = r0
    //     0xbb3368: stur            w0, [x1, #0x23]
    // 0xbb336c: ldur            x2, [fp, #-0x20]
    // 0xbb3370: StoreField: r1->field_7 = r2
    //     0xbb3370: stur            w2, [x1, #7]
    // 0xbb3374: r0 = Padding()
    //     0xbb3374: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb3378: mov             x1, x0
    // 0xbb337c: r0 = Instance_EdgeInsets
    //     0xbb337c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xbb3380: ldr             x0, [x0, #0x100]
    // 0xbb3384: stur            x1, [fp, #-0x20]
    // 0xbb3388: StoreField: r1->field_f = r0
    //     0xbb3388: stur            w0, [x1, #0xf]
    // 0xbb338c: ldur            x2, [fp, #-0x38]
    // 0xbb3390: StoreField: r1->field_b = r2
    //     0xbb3390: stur            w2, [x1, #0xb]
    // 0xbb3394: r0 = Visibility()
    //     0xbb3394: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbb3398: mov             x3, x0
    // 0xbb339c: ldur            x0, [fp, #-0x20]
    // 0xbb33a0: stur            x3, [fp, #-0x38]
    // 0xbb33a4: StoreField: r3->field_b = r0
    //     0xbb33a4: stur            w0, [x3, #0xb]
    // 0xbb33a8: r4 = Instance_SizedBox
    //     0xbb33a8: ldr             x4, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbb33ac: StoreField: r3->field_f = r4
    //     0xbb33ac: stur            w4, [x3, #0xf]
    // 0xbb33b0: ldur            x0, [fp, #-0x30]
    // 0xbb33b4: StoreField: r3->field_13 = r0
    //     0xbb33b4: stur            w0, [x3, #0x13]
    // 0xbb33b8: r5 = false
    //     0xbb33b8: add             x5, NULL, #0x30  ; false
    // 0xbb33bc: ArrayStore: r3[0] = r5  ; List_4
    //     0xbb33bc: stur            w5, [x3, #0x17]
    // 0xbb33c0: StoreField: r3->field_1b = r5
    //     0xbb33c0: stur            w5, [x3, #0x1b]
    // 0xbb33c4: StoreField: r3->field_1f = r5
    //     0xbb33c4: stur            w5, [x3, #0x1f]
    // 0xbb33c8: StoreField: r3->field_23 = r5
    //     0xbb33c8: stur            w5, [x3, #0x23]
    // 0xbb33cc: StoreField: r3->field_27 = r5
    //     0xbb33cc: stur            w5, [x3, #0x27]
    // 0xbb33d0: StoreField: r3->field_2b = r5
    //     0xbb33d0: stur            w5, [x3, #0x2b]
    // 0xbb33d4: ldur            x6, [fp, #-8]
    // 0xbb33d8: LoadField: r0 = r6->field_b
    //     0xbb33d8: ldur            w0, [x6, #0xb]
    // 0xbb33dc: DecompressPointer r0
    //     0xbb33dc: add             x0, x0, HEAP, lsl #32
    // 0xbb33e0: cmp             w0, NULL
    // 0xbb33e4: b.eq            #0xbb3a20
    // 0xbb33e8: LoadField: r1 = r0->field_b
    //     0xbb33e8: ldur            w1, [x0, #0xb]
    // 0xbb33ec: DecompressPointer r1
    //     0xbb33ec: add             x1, x1, HEAP, lsl #32
    // 0xbb33f0: LoadField: r0 = r1->field_f
    //     0xbb33f0: ldur            w0, [x1, #0xf]
    // 0xbb33f4: DecompressPointer r0
    //     0xbb33f4: add             x0, x0, HEAP, lsl #32
    // 0xbb33f8: cmp             w0, NULL
    // 0xbb33fc: b.ne            #0xbb3408
    // 0xbb3400: r0 = Null
    //     0xbb3400: mov             x0, NULL
    // 0xbb3404: b               #0xbb3434
    // 0xbb3408: r1 = LoadClassIdInstr(r0)
    //     0xbb3408: ldur            x1, [x0, #-1]
    //     0xbb340c: ubfx            x1, x1, #0xc, #0x14
    // 0xbb3410: mov             x16, x0
    // 0xbb3414: mov             x0, x1
    // 0xbb3418: mov             x1, x16
    // 0xbb341c: r2 = "alternate_contact_number"
    //     0xbb341c: add             x2, PP, #0x54, lsl #12  ; [pp+0x54140] "alternate_contact_number"
    //     0xbb3420: ldr             x2, [x2, #0x140]
    // 0xbb3424: r0 = GDT[cid_x0 + 0xe437]()
    //     0xbb3424: movz            x17, #0xe437
    //     0xbb3428: add             lr, x0, x17
    //     0xbb342c: ldr             lr, [x21, lr, lsl #3]
    //     0xbb3430: blr             lr
    // 0xbb3434: cmp             w0, NULL
    // 0xbb3438: b.ne            #0xbb3444
    // 0xbb343c: r1 = false
    //     0xbb343c: add             x1, NULL, #0x30  ; false
    // 0xbb3440: b               #0xbb3448
    // 0xbb3444: mov             x1, x0
    // 0xbb3448: ldur            x2, [fp, #-8]
    // 0xbb344c: ldur            x0, [fp, #-0x48]
    // 0xbb3450: stur            x1, [fp, #-0x30]
    // 0xbb3454: LoadField: r3 = r2->field_2b
    //     0xbb3454: ldur            w3, [x2, #0x2b]
    // 0xbb3458: DecompressPointer r3
    //     0xbb3458: add             x3, x3, HEAP, lsl #32
    // 0xbb345c: stur            x3, [fp, #-0x20]
    // 0xbb3460: r16 = "[0-9]"
    //     0xbb3460: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0xbb3464: ldr             x16, [x16, #0x128]
    // 0xbb3468: stp             x16, NULL, [SP, #0x20]
    // 0xbb346c: r16 = false
    //     0xbb346c: add             x16, NULL, #0x30  ; false
    // 0xbb3470: r30 = true
    //     0xbb3470: add             lr, NULL, #0x20  ; true
    // 0xbb3474: stp             lr, x16, [SP, #0x10]
    // 0xbb3478: r16 = false
    //     0xbb3478: add             x16, NULL, #0x30  ; false
    // 0xbb347c: r30 = false
    //     0xbb347c: add             lr, NULL, #0x30  ; false
    // 0xbb3480: stp             lr, x16, [SP]
    // 0xbb3484: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xbb3484: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xbb3488: r0 = _RegExp()
    //     0xbb3488: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xbb348c: stur            x0, [fp, #-0x40]
    // 0xbb3490: r0 = FilteringTextInputFormatter()
    //     0xbb3490: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xbb3494: mov             x1, x0
    // 0xbb3498: ldur            x0, [fp, #-0x40]
    // 0xbb349c: stur            x1, [fp, #-0x50]
    // 0xbb34a0: StoreField: r1->field_b = r0
    //     0xbb34a0: stur            w0, [x1, #0xb]
    // 0xbb34a4: r0 = true
    //     0xbb34a4: add             x0, NULL, #0x20  ; true
    // 0xbb34a8: StoreField: r1->field_7 = r0
    //     0xbb34a8: stur            w0, [x1, #7]
    // 0xbb34ac: r0 = ""
    //     0xbb34ac: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb34b0: StoreField: r1->field_f = r0
    //     0xbb34b0: stur            w0, [x1, #0xf]
    // 0xbb34b4: r0 = LengthLimitingTextInputFormatter()
    //     0xbb34b4: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbb34b8: mov             x3, x0
    // 0xbb34bc: r0 = 20
    //     0xbb34bc: movz            x0, #0x14
    // 0xbb34c0: stur            x3, [fp, #-0x40]
    // 0xbb34c4: StoreField: r3->field_7 = r0
    //     0xbb34c4: stur            w0, [x3, #7]
    // 0xbb34c8: r1 = Null
    //     0xbb34c8: mov             x1, NULL
    // 0xbb34cc: r2 = 6
    //     0xbb34cc: movz            x2, #0x6
    // 0xbb34d0: r0 = AllocateArray()
    //     0xbb34d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb34d4: mov             x2, x0
    // 0xbb34d8: ldur            x0, [fp, #-0x48]
    // 0xbb34dc: stur            x2, [fp, #-0x58]
    // 0xbb34e0: StoreField: r2->field_f = r0
    //     0xbb34e0: stur            w0, [x2, #0xf]
    // 0xbb34e4: ldur            x0, [fp, #-0x50]
    // 0xbb34e8: StoreField: r2->field_13 = r0
    //     0xbb34e8: stur            w0, [x2, #0x13]
    // 0xbb34ec: ldur            x0, [fp, #-0x40]
    // 0xbb34f0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb34f0: stur            w0, [x2, #0x17]
    // 0xbb34f4: r1 = <TextInputFormatter>
    //     0xbb34f4: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbb34f8: ldr             x1, [x1, #0x7b0]
    // 0xbb34fc: r0 = AllocateGrowableArray()
    //     0xbb34fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb3500: mov             x2, x0
    // 0xbb3504: ldur            x0, [fp, #-0x58]
    // 0xbb3508: stur            x2, [fp, #-0x48]
    // 0xbb350c: StoreField: r2->field_f = r0
    //     0xbb350c: stur            w0, [x2, #0xf]
    // 0xbb3510: r0 = 6
    //     0xbb3510: movz            x0, #0x6
    // 0xbb3514: StoreField: r2->field_b = r0
    //     0xbb3514: stur            w0, [x2, #0xb]
    // 0xbb3518: ldur            x3, [fp, #-8]
    // 0xbb351c: LoadField: r4 = r3->field_43
    //     0xbb351c: ldur            w4, [x3, #0x43]
    // 0xbb3520: DecompressPointer r4
    //     0xbb3520: add             x4, x4, HEAP, lsl #32
    // 0xbb3524: ldur            x1, [fp, #-0x10]
    // 0xbb3528: stur            x4, [fp, #-0x40]
    // 0xbb352c: r0 = of()
    //     0xbb352c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb3530: LoadField: r1 = r0->field_87
    //     0xbb3530: ldur            w1, [x0, #0x87]
    // 0xbb3534: DecompressPointer r1
    //     0xbb3534: add             x1, x1, HEAP, lsl #32
    // 0xbb3538: LoadField: r0 = r1->field_2b
    //     0xbb3538: ldur            w0, [x1, #0x2b]
    // 0xbb353c: DecompressPointer r0
    //     0xbb353c: add             x0, x0, HEAP, lsl #32
    // 0xbb3540: ldur            x1, [fp, #-0x10]
    // 0xbb3544: stur            x0, [fp, #-0x50]
    // 0xbb3548: r0 = of()
    //     0xbb3548: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb354c: LoadField: r1 = r0->field_5b
    //     0xbb354c: ldur            w1, [x0, #0x5b]
    // 0xbb3550: DecompressPointer r1
    //     0xbb3550: add             x1, x1, HEAP, lsl #32
    // 0xbb3554: r16 = 14.000000
    //     0xbb3554: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb3558: ldr             x16, [x16, #0x1d8]
    // 0xbb355c: stp             x1, x16, [SP]
    // 0xbb3560: ldur            x1, [fp, #-0x50]
    // 0xbb3564: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb3564: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb3568: ldr             x4, [x4, #0xaa0]
    // 0xbb356c: r0 = copyWith()
    //     0xbb356c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb3570: ldur            x1, [fp, #-0x10]
    // 0xbb3574: stur            x0, [fp, #-0x50]
    // 0xbb3578: r0 = getTextFormFieldInputDecoration()
    //     0xbb3578: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbb357c: ldur            x1, [fp, #-0x10]
    // 0xbb3580: stur            x0, [fp, #-0x58]
    // 0xbb3584: r0 = of()
    //     0xbb3584: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb3588: LoadField: r1 = r0->field_5b
    //     0xbb3588: ldur            w1, [x0, #0x5b]
    // 0xbb358c: DecompressPointer r1
    //     0xbb358c: add             x1, x1, HEAP, lsl #32
    // 0xbb3590: stur            x1, [fp, #-0x60]
    // 0xbb3594: r0 = BorderSide()
    //     0xbb3594: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbb3598: mov             x1, x0
    // 0xbb359c: ldur            x0, [fp, #-0x60]
    // 0xbb35a0: stur            x1, [fp, #-0x68]
    // 0xbb35a4: StoreField: r1->field_7 = r0
    //     0xbb35a4: stur            w0, [x1, #7]
    // 0xbb35a8: d0 = 1.000000
    //     0xbb35a8: fmov            d0, #1.00000000
    // 0xbb35ac: StoreField: r1->field_b = d0
    //     0xbb35ac: stur            d0, [x1, #0xb]
    // 0xbb35b0: r0 = Instance_BorderStyle
    //     0xbb35b0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbb35b4: ldr             x0, [x0, #0xf68]
    // 0xbb35b8: StoreField: r1->field_13 = r0
    //     0xbb35b8: stur            w0, [x1, #0x13]
    // 0xbb35bc: d0 = -1.000000
    //     0xbb35bc: fmov            d0, #-1.00000000
    // 0xbb35c0: ArrayStore: r1[0] = d0  ; List_8
    //     0xbb35c0: stur            d0, [x1, #0x17]
    // 0xbb35c4: r0 = OutlineInputBorder()
    //     0xbb35c4: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbb35c8: mov             x2, x0
    // 0xbb35cc: r0 = Instance_BorderRadius
    //     0xbb35cc: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbb35d0: ldr             x0, [x0, #0xf70]
    // 0xbb35d4: stur            x2, [fp, #-0x60]
    // 0xbb35d8: StoreField: r2->field_13 = r0
    //     0xbb35d8: stur            w0, [x2, #0x13]
    // 0xbb35dc: d0 = 4.000000
    //     0xbb35dc: fmov            d0, #4.00000000
    // 0xbb35e0: StoreField: r2->field_b = d0
    //     0xbb35e0: stur            d0, [x2, #0xb]
    // 0xbb35e4: ldur            x0, [fp, #-0x68]
    // 0xbb35e8: StoreField: r2->field_7 = r0
    //     0xbb35e8: stur            w0, [x2, #7]
    // 0xbb35ec: ldur            x1, [fp, #-0x10]
    // 0xbb35f0: r0 = of()
    //     0xbb35f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb35f4: LoadField: r1 = r0->field_87
    //     0xbb35f4: ldur            w1, [x0, #0x87]
    // 0xbb35f8: DecompressPointer r1
    //     0xbb35f8: add             x1, x1, HEAP, lsl #32
    // 0xbb35fc: LoadField: r0 = r1->field_2b
    //     0xbb35fc: ldur            w0, [x1, #0x2b]
    // 0xbb3600: DecompressPointer r0
    //     0xbb3600: add             x0, x0, HEAP, lsl #32
    // 0xbb3604: stur            x0, [fp, #-0x68]
    // 0xbb3608: r1 = Instance_Color
    //     0xbb3608: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb360c: d0 = 0.400000
    //     0xbb360c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbb3610: r0 = withOpacity()
    //     0xbb3610: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb3614: r16 = 12.000000
    //     0xbb3614: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb3618: ldr             x16, [x16, #0x9e8]
    // 0xbb361c: stp             x0, x16, [SP]
    // 0xbb3620: ldur            x1, [fp, #-0x68]
    // 0xbb3624: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb3624: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb3628: ldr             x4, [x4, #0xaa0]
    // 0xbb362c: r0 = copyWith()
    //     0xbb362c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb3630: ldur            x1, [fp, #-0x10]
    // 0xbb3634: stur            x0, [fp, #-0x10]
    // 0xbb3638: r0 = of()
    //     0xbb3638: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb363c: LoadField: r1 = r0->field_87
    //     0xbb363c: ldur            w1, [x0, #0x87]
    // 0xbb3640: DecompressPointer r1
    //     0xbb3640: add             x1, x1, HEAP, lsl #32
    // 0xbb3644: LoadField: r0 = r1->field_2b
    //     0xbb3644: ldur            w0, [x1, #0x2b]
    // 0xbb3648: DecompressPointer r0
    //     0xbb3648: add             x0, x0, HEAP, lsl #32
    // 0xbb364c: r16 = 12.000000
    //     0xbb364c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb3650: ldr             x16, [x16, #0x9e8]
    // 0xbb3654: r30 = Instance_MaterialColor
    //     0xbb3654: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xbb3658: ldr             lr, [lr, #0x180]
    // 0xbb365c: stp             lr, x16, [SP]
    // 0xbb3660: mov             x1, x0
    // 0xbb3664: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb3664: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb3668: ldr             x4, [x4, #0xaa0]
    // 0xbb366c: r0 = copyWith()
    //     0xbb366c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb3670: mov             x3, x0
    // 0xbb3674: ldur            x0, [fp, #-8]
    // 0xbb3678: stur            x3, [fp, #-0x68]
    // 0xbb367c: LoadField: r1 = r0->field_67
    //     0xbb367c: ldur            w1, [x0, #0x67]
    // 0xbb3680: DecompressPointer r1
    //     0xbb3680: add             x1, x1, HEAP, lsl #32
    // 0xbb3684: tbnz            w1, #4, #0xbb37ac
    // 0xbb3688: LoadField: r1 = r0->field_43
    //     0xbb3688: ldur            w1, [x0, #0x43]
    // 0xbb368c: DecompressPointer r1
    //     0xbb368c: add             x1, x1, HEAP, lsl #32
    // 0xbb3690: LoadField: r2 = r1->field_27
    //     0xbb3690: ldur            w2, [x1, #0x27]
    // 0xbb3694: DecompressPointer r2
    //     0xbb3694: add             x2, x2, HEAP, lsl #32
    // 0xbb3698: LoadField: r1 = r2->field_7
    //     0xbb3698: ldur            w1, [x2, #7]
    // 0xbb369c: DecompressPointer r1
    //     0xbb369c: add             x1, x1, HEAP, lsl #32
    // 0xbb36a0: LoadField: r2 = r1->field_7
    //     0xbb36a0: ldur            w2, [x1, #7]
    // 0xbb36a4: cbz             w2, #0xbb36e8
    // 0xbb36a8: cmp             w2, #0x14
    // 0xbb36ac: b.ne            #0xbb36e8
    // 0xbb36b0: r16 = 2
    //     0xbb36b0: movz            x16, #0x2
    // 0xbb36b4: str             x16, [SP]
    // 0xbb36b8: r2 = 0
    //     0xbb36b8: movz            x2, #0
    // 0xbb36bc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbb36bc: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbb36c0: r0 = substring()
    //     0xbb36c0: bl              #0x61f5c0  ; [dart:core] _StringBase::substring
    // 0xbb36c4: mov             x1, x0
    // 0xbb36c8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbb36c8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbb36cc: r0 = parse()
    //     0xbb36cc: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xbb36d0: cmp             x0, #6
    // 0xbb36d4: b.lt            #0xbb36e8
    // 0xbb36d8: ldur            x0, [fp, #-8]
    // 0xbb36dc: r3 = Instance_IconData
    //     0xbb36dc: add             x3, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbb36e0: ldr             x3, [x3, #0x130]
    // 0xbb36e4: b               #0xbb3720
    // 0xbb36e8: ldur            x0, [fp, #-8]
    // 0xbb36ec: LoadField: r1 = r0->field_43
    //     0xbb36ec: ldur            w1, [x0, #0x43]
    // 0xbb36f0: DecompressPointer r1
    //     0xbb36f0: add             x1, x1, HEAP, lsl #32
    // 0xbb36f4: LoadField: r2 = r1->field_27
    //     0xbb36f4: ldur            w2, [x1, #0x27]
    // 0xbb36f8: DecompressPointer r2
    //     0xbb36f8: add             x2, x2, HEAP, lsl #32
    // 0xbb36fc: LoadField: r1 = r2->field_7
    //     0xbb36fc: ldur            w1, [x2, #7]
    // 0xbb3700: DecompressPointer r1
    //     0xbb3700: add             x1, x1, HEAP, lsl #32
    // 0xbb3704: LoadField: r2 = r1->field_7
    //     0xbb3704: ldur            w2, [x1, #7]
    // 0xbb3708: cbz             w2, #0xbb3718
    // 0xbb370c: r1 = Instance_IconData
    //     0xbb370c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbb3710: ldr             x1, [x1, #0x138]
    // 0xbb3714: b               #0xbb371c
    // 0xbb3718: r1 = Null
    //     0xbb3718: mov             x1, NULL
    // 0xbb371c: mov             x3, x1
    // 0xbb3720: stur            x3, [fp, #-0x70]
    // 0xbb3724: LoadField: r1 = r0->field_43
    //     0xbb3724: ldur            w1, [x0, #0x43]
    // 0xbb3728: DecompressPointer r1
    //     0xbb3728: add             x1, x1, HEAP, lsl #32
    // 0xbb372c: LoadField: r2 = r1->field_27
    //     0xbb372c: ldur            w2, [x1, #0x27]
    // 0xbb3730: DecompressPointer r2
    //     0xbb3730: add             x2, x2, HEAP, lsl #32
    // 0xbb3734: LoadField: r1 = r2->field_7
    //     0xbb3734: ldur            w1, [x2, #7]
    // 0xbb3738: DecompressPointer r1
    //     0xbb3738: add             x1, x1, HEAP, lsl #32
    // 0xbb373c: LoadField: r2 = r1->field_7
    //     0xbb373c: ldur            w2, [x1, #7]
    // 0xbb3740: cbz             w2, #0xbb3780
    // 0xbb3744: cmp             w2, #0x14
    // 0xbb3748: b.ne            #0xbb3780
    // 0xbb374c: r16 = 2
    //     0xbb374c: movz            x16, #0x2
    // 0xbb3750: str             x16, [SP]
    // 0xbb3754: r2 = 0
    //     0xbb3754: movz            x2, #0
    // 0xbb3758: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbb3758: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbb375c: r0 = substring()
    //     0xbb375c: bl              #0x61f5c0  ; [dart:core] _StringBase::substring
    // 0xbb3760: mov             x1, x0
    // 0xbb3764: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbb3764: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbb3768: r0 = parse()
    //     0xbb3768: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xbb376c: cmp             x0, #6
    // 0xbb3770: b.lt            #0xbb3780
    // 0xbb3774: r1 = Instance_Color
    //     0xbb3774: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb3778: ldr             x1, [x1, #0x858]
    // 0xbb377c: b               #0xbb3788
    // 0xbb3780: r1 = Instance_Color
    //     0xbb3780: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbb3784: ldr             x1, [x1, #0x50]
    // 0xbb3788: ldur            x0, [fp, #-0x70]
    // 0xbb378c: stur            x1, [fp, #-0x78]
    // 0xbb3790: r0 = Icon()
    //     0xbb3790: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbb3794: mov             x1, x0
    // 0xbb3798: ldur            x0, [fp, #-0x70]
    // 0xbb379c: StoreField: r1->field_b = r0
    //     0xbb379c: stur            w0, [x1, #0xb]
    // 0xbb37a0: ldur            x0, [fp, #-0x78]
    // 0xbb37a4: StoreField: r1->field_23 = r0
    //     0xbb37a4: stur            w0, [x1, #0x23]
    // 0xbb37a8: b               #0xbb37b0
    // 0xbb37ac: r1 = Null
    //     0xbb37ac: mov             x1, NULL
    // 0xbb37b0: ldur            x4, [fp, #-0x18]
    // 0xbb37b4: ldur            x3, [fp, #-0x38]
    // 0xbb37b8: ldur            x0, [fp, #-0x30]
    // 0xbb37bc: ldur            x2, [fp, #-0x20]
    // 0xbb37c0: ldur            x16, [fp, #-0x60]
    // 0xbb37c4: r30 = Instance_EdgeInsets
    //     0xbb37c4: add             lr, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xbb37c8: ldr             lr, [lr, #0xc40]
    // 0xbb37cc: stp             lr, x16, [SP, #0x20]
    // 0xbb37d0: r16 = "Alternate Ph No"
    //     0xbb37d0: add             x16, PP, #0x54, lsl #12  ; [pp+0x54148] "Alternate Ph No"
    //     0xbb37d4: ldr             x16, [x16, #0x148]
    // 0xbb37d8: ldur            lr, [fp, #-0x10]
    // 0xbb37dc: stp             lr, x16, [SP, #0x10]
    // 0xbb37e0: ldur            x16, [fp, #-0x68]
    // 0xbb37e4: stp             x1, x16, [SP]
    // 0xbb37e8: ldur            x1, [fp, #-0x58]
    // 0xbb37ec: r4 = const [0, 0x7, 0x6, 0x1, contentPadding, 0x2, errorStyle, 0x5, focusedBorder, 0x1, labelStyle, 0x4, labelText, 0x3, suffixIcon, 0x6, null]
    //     0xbb37ec: add             x4, PP, #0x54, lsl #12  ; [pp+0x54050] List(17) [0, 0x7, 0x6, 0x1, "contentPadding", 0x2, "errorStyle", 0x5, "focusedBorder", 0x1, "labelStyle", 0x4, "labelText", 0x3, "suffixIcon", 0x6, Null]
    //     0xbb37f0: ldr             x4, [x4, #0x50]
    // 0xbb37f4: r0 = copyWith()
    //     0xbb37f4: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbb37f8: ldur            x2, [fp, #-8]
    // 0xbb37fc: r1 = Function '_validateAlternateNo@1669306702':.
    //     0xbb37fc: add             x1, PP, #0x54, lsl #12  ; [pp+0x54150] AnonymousClosure: (0xbb3a24), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAlternateNo (0xa05330)
    //     0xbb3800: ldr             x1, [x1, #0x150]
    // 0xbb3804: stur            x0, [fp, #-8]
    // 0xbb3808: r0 = AllocateClosure()
    //     0xbb3808: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb380c: ldur            x2, [fp, #-0x28]
    // 0xbb3810: r1 = Function '<anonymous closure>':.
    //     0xbb3810: add             x1, PP, #0x54, lsl #12  ; [pp+0x54158] AnonymousClosure: (0xa0527c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xbb3814: ldr             x1, [x1, #0x158]
    // 0xbb3818: stur            x0, [fp, #-0x10]
    // 0xbb381c: r0 = AllocateClosure()
    //     0xbb381c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb3820: r1 = <String>
    //     0xbb3820: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbb3824: stur            x0, [fp, #-0x28]
    // 0xbb3828: r0 = TextFormField()
    //     0xbb3828: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbb382c: stur            x0, [fp, #-0x58]
    // 0xbb3830: ldur            x16, [fp, #-0x10]
    // 0xbb3834: r30 = true
    //     0xbb3834: add             lr, NULL, #0x20  ; true
    // 0xbb3838: stp             lr, x16, [SP, #0x38]
    // 0xbb383c: r16 = Instance_AutovalidateMode
    //     0xbb383c: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbb3840: ldr             x16, [x16, #0x7e8]
    // 0xbb3844: ldur            lr, [fp, #-0x48]
    // 0xbb3848: stp             lr, x16, [SP, #0x28]
    // 0xbb384c: r16 = Instance_TextInputType
    //     0xbb384c: add             x16, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xbb3850: ldr             x16, [x16, #0x1a0]
    // 0xbb3854: r30 = 2
    //     0xbb3854: movz            lr, #0x2
    // 0xbb3858: stp             lr, x16, [SP, #0x18]
    // 0xbb385c: ldur            x16, [fp, #-0x40]
    // 0xbb3860: ldur            lr, [fp, #-0x50]
    // 0xbb3864: stp             lr, x16, [SP, #8]
    // 0xbb3868: ldur            x16, [fp, #-0x28]
    // 0xbb386c: str             x16, [SP]
    // 0xbb3870: mov             x1, x0
    // 0xbb3874: ldur            x2, [fp, #-8]
    // 0xbb3878: r4 = const [0, 0xb, 0x9, 0x2, autovalidateMode, 0x4, controller, 0x8, enableSuggestions, 0x3, inputFormatters, 0x5, keyboardType, 0x6, maxLines, 0x7, onChanged, 0xa, style, 0x9, validator, 0x2, null]
    //     0xbb3878: add             x4, PP, #0x54, lsl #12  ; [pp+0x54138] List(23) [0, 0xb, 0x9, 0x2, "autovalidateMode", 0x4, "controller", 0x8, "enableSuggestions", 0x3, "inputFormatters", 0x5, "keyboardType", 0x6, "maxLines", 0x7, "onChanged", 0xa, "style", 0x9, "validator", 0x2, Null]
    //     0xbb387c: ldr             x4, [x4, #0x138]
    // 0xbb3880: r0 = TextFormField()
    //     0xbb3880: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbb3884: r0 = Form()
    //     0xbb3884: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbb3888: mov             x1, x0
    // 0xbb388c: ldur            x0, [fp, #-0x58]
    // 0xbb3890: stur            x1, [fp, #-8]
    // 0xbb3894: StoreField: r1->field_b = r0
    //     0xbb3894: stur            w0, [x1, #0xb]
    // 0xbb3898: r0 = Instance_AutovalidateMode
    //     0xbb3898: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbb389c: ldr             x0, [x0, #0x800]
    // 0xbb38a0: StoreField: r1->field_23 = r0
    //     0xbb38a0: stur            w0, [x1, #0x23]
    // 0xbb38a4: ldur            x0, [fp, #-0x20]
    // 0xbb38a8: StoreField: r1->field_7 = r0
    //     0xbb38a8: stur            w0, [x1, #7]
    // 0xbb38ac: r0 = Padding()
    //     0xbb38ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb38b0: mov             x1, x0
    // 0xbb38b4: r0 = Instance_EdgeInsets
    //     0xbb38b4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xbb38b8: ldr             x0, [x0, #0x100]
    // 0xbb38bc: stur            x1, [fp, #-0x10]
    // 0xbb38c0: StoreField: r1->field_f = r0
    //     0xbb38c0: stur            w0, [x1, #0xf]
    // 0xbb38c4: ldur            x0, [fp, #-8]
    // 0xbb38c8: StoreField: r1->field_b = r0
    //     0xbb38c8: stur            w0, [x1, #0xb]
    // 0xbb38cc: r0 = Visibility()
    //     0xbb38cc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbb38d0: mov             x3, x0
    // 0xbb38d4: ldur            x0, [fp, #-0x10]
    // 0xbb38d8: stur            x3, [fp, #-8]
    // 0xbb38dc: StoreField: r3->field_b = r0
    //     0xbb38dc: stur            w0, [x3, #0xb]
    // 0xbb38e0: r0 = Instance_SizedBox
    //     0xbb38e0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbb38e4: StoreField: r3->field_f = r0
    //     0xbb38e4: stur            w0, [x3, #0xf]
    // 0xbb38e8: ldur            x0, [fp, #-0x30]
    // 0xbb38ec: StoreField: r3->field_13 = r0
    //     0xbb38ec: stur            w0, [x3, #0x13]
    // 0xbb38f0: r0 = false
    //     0xbb38f0: add             x0, NULL, #0x30  ; false
    // 0xbb38f4: ArrayStore: r3[0] = r0  ; List_4
    //     0xbb38f4: stur            w0, [x3, #0x17]
    // 0xbb38f8: StoreField: r3->field_1b = r0
    //     0xbb38f8: stur            w0, [x3, #0x1b]
    // 0xbb38fc: StoreField: r3->field_1f = r0
    //     0xbb38fc: stur            w0, [x3, #0x1f]
    // 0xbb3900: StoreField: r3->field_23 = r0
    //     0xbb3900: stur            w0, [x3, #0x23]
    // 0xbb3904: StoreField: r3->field_27 = r0
    //     0xbb3904: stur            w0, [x3, #0x27]
    // 0xbb3908: StoreField: r3->field_2b = r0
    //     0xbb3908: stur            w0, [x3, #0x2b]
    // 0xbb390c: r1 = Null
    //     0xbb390c: mov             x1, NULL
    // 0xbb3910: r2 = 6
    //     0xbb3910: movz            x2, #0x6
    // 0xbb3914: r0 = AllocateArray()
    //     0xbb3914: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb3918: mov             x2, x0
    // 0xbb391c: ldur            x0, [fp, #-0x18]
    // 0xbb3920: stur            x2, [fp, #-0x10]
    // 0xbb3924: StoreField: r2->field_f = r0
    //     0xbb3924: stur            w0, [x2, #0xf]
    // 0xbb3928: ldur            x0, [fp, #-0x38]
    // 0xbb392c: StoreField: r2->field_13 = r0
    //     0xbb392c: stur            w0, [x2, #0x13]
    // 0xbb3930: ldur            x0, [fp, #-8]
    // 0xbb3934: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb3934: stur            w0, [x2, #0x17]
    // 0xbb3938: r1 = <Widget>
    //     0xbb3938: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb393c: r0 = AllocateGrowableArray()
    //     0xbb393c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb3940: mov             x1, x0
    // 0xbb3944: ldur            x0, [fp, #-0x10]
    // 0xbb3948: stur            x1, [fp, #-8]
    // 0xbb394c: StoreField: r1->field_f = r0
    //     0xbb394c: stur            w0, [x1, #0xf]
    // 0xbb3950: r0 = 6
    //     0xbb3950: movz            x0, #0x6
    // 0xbb3954: StoreField: r1->field_b = r0
    //     0xbb3954: stur            w0, [x1, #0xb]
    // 0xbb3958: r0 = Column()
    //     0xbb3958: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb395c: mov             x1, x0
    // 0xbb3960: r0 = Instance_Axis
    //     0xbb3960: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbb3964: stur            x1, [fp, #-0x10]
    // 0xbb3968: StoreField: r1->field_f = r0
    //     0xbb3968: stur            w0, [x1, #0xf]
    // 0xbb396c: r0 = Instance_MainAxisAlignment
    //     0xbb396c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb3970: ldr             x0, [x0, #0xa08]
    // 0xbb3974: StoreField: r1->field_13 = r0
    //     0xbb3974: stur            w0, [x1, #0x13]
    // 0xbb3978: r0 = Instance_MainAxisSize
    //     0xbb3978: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb397c: ldr             x0, [x0, #0xa10]
    // 0xbb3980: ArrayStore: r1[0] = r0  ; List_4
    //     0xbb3980: stur            w0, [x1, #0x17]
    // 0xbb3984: r0 = Instance_CrossAxisAlignment
    //     0xbb3984: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbb3988: ldr             x0, [x0, #0x890]
    // 0xbb398c: StoreField: r1->field_1b = r0
    //     0xbb398c: stur            w0, [x1, #0x1b]
    // 0xbb3990: r0 = Instance_VerticalDirection
    //     0xbb3990: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb3994: ldr             x0, [x0, #0xa20]
    // 0xbb3998: StoreField: r1->field_23 = r0
    //     0xbb3998: stur            w0, [x1, #0x23]
    // 0xbb399c: r0 = Instance_Clip
    //     0xbb399c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb39a0: ldr             x0, [x0, #0x38]
    // 0xbb39a4: StoreField: r1->field_2b = r0
    //     0xbb39a4: stur            w0, [x1, #0x2b]
    // 0xbb39a8: StoreField: r1->field_2f = rZR
    //     0xbb39a8: stur            xzr, [x1, #0x2f]
    // 0xbb39ac: ldur            x0, [fp, #-8]
    // 0xbb39b0: StoreField: r1->field_b = r0
    //     0xbb39b0: stur            w0, [x1, #0xb]
    // 0xbb39b4: r0 = Padding()
    //     0xbb39b4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb39b8: r1 = Instance_EdgeInsets
    //     0xbb39b8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54160] Obj!EdgeInsets@d58d61
    //     0xbb39bc: ldr             x1, [x1, #0x160]
    // 0xbb39c0: StoreField: r0->field_f = r1
    //     0xbb39c0: stur            w1, [x0, #0xf]
    // 0xbb39c4: ldur            x1, [fp, #-0x10]
    // 0xbb39c8: StoreField: r0->field_b = r1
    //     0xbb39c8: stur            w1, [x0, #0xb]
    // 0xbb39cc: LeaveFrame
    //     0xbb39cc: mov             SP, fp
    //     0xbb39d0: ldp             fp, lr, [SP], #0x10
    // 0xbb39d4: ret
    //     0xbb39d4: ret             
    // 0xbb39d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb39d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb39dc: b               #0xbb1164
    // 0xbb39e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb39e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb39e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb39e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb39e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb39e8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbb39ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb39ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb39f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb39f0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbb39f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb39f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb39f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb39f8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbb39fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb39fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb3a00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb3a00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb3a04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb3a04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb3a08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb3a08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb3a0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb3a0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb3a10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb3a10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb3a14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb3a14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb3a18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb3a18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb3a1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb3a1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb3a20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb3a20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] String? _validateAlternateNo(dynamic, String?) {
    // ** addr: 0xbb3a24, size: 0x3c
    // 0xbb3a24: EnterFrame
    //     0xbb3a24: stp             fp, lr, [SP, #-0x10]!
    //     0xbb3a28: mov             fp, SP
    // 0xbb3a2c: ldr             x0, [fp, #0x18]
    // 0xbb3a30: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb3a30: ldur            w1, [x0, #0x17]
    // 0xbb3a34: DecompressPointer r1
    //     0xbb3a34: add             x1, x1, HEAP, lsl #32
    // 0xbb3a38: CheckStackOverflow
    //     0xbb3a38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb3a3c: cmp             SP, x16
    //     0xbb3a40: b.ls            #0xbb3a58
    // 0xbb3a44: ldr             x2, [fp, #0x10]
    // 0xbb3a48: r0 = _validateAlternateNo()
    //     0xbb3a48: bl              #0xa05330  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAlternateNo
    // 0xbb3a4c: LeaveFrame
    //     0xbb3a4c: mov             SP, fp
    //     0xbb3a50: ldp             fp, lr, [SP], #0x10
    // 0xbb3a54: ret
    //     0xbb3a54: ret             
    // 0xbb3a58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb3a58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb3a5c: b               #0xbb3a44
  }
  [closure] String? _validateLandmark(dynamic, String?) {
    // ** addr: 0xbb3a60, size: 0x3c
    // 0xbb3a60: EnterFrame
    //     0xbb3a60: stp             fp, lr, [SP, #-0x10]!
    //     0xbb3a64: mov             fp, SP
    // 0xbb3a68: ldr             x0, [fp, #0x18]
    // 0xbb3a6c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb3a6c: ldur            w1, [x0, #0x17]
    // 0xbb3a70: DecompressPointer r1
    //     0xbb3a70: add             x1, x1, HEAP, lsl #32
    // 0xbb3a74: CheckStackOverflow
    //     0xbb3a74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb3a78: cmp             SP, x16
    //     0xbb3a7c: b.ls            #0xbb3a94
    // 0xbb3a80: ldr             x2, [fp, #0x10]
    // 0xbb3a84: r0 = _validateLandmark()
    //     0xbb3a84: bl              #0xa054c0  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateLandmark
    // 0xbb3a88: LeaveFrame
    //     0xbb3a88: mov             SP, fp
    //     0xbb3a8c: ldp             fp, lr, [SP], #0x10
    // 0xbb3a90: ret
    //     0xbb3a90: ret             
    // 0xbb3a94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb3a94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb3a98: b               #0xbb3a80
  }
  [closure] String? _validatePinCode(dynamic, String?) {
    // ** addr: 0xbb3a9c, size: 0x3c
    // 0xbb3a9c: EnterFrame
    //     0xbb3a9c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb3aa0: mov             fp, SP
    // 0xbb3aa4: ldr             x0, [fp, #0x18]
    // 0xbb3aa8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb3aa8: ldur            w1, [x0, #0x17]
    // 0xbb3aac: DecompressPointer r1
    //     0xbb3aac: add             x1, x1, HEAP, lsl #32
    // 0xbb3ab0: CheckStackOverflow
    //     0xbb3ab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb3ab4: cmp             SP, x16
    //     0xbb3ab8: b.ls            #0xbb3ad0
    // 0xbb3abc: ldr             x2, [fp, #0x10]
    // 0xbb3ac0: r0 = _validatePinCode()
    //     0xbb3ac0: bl              #0xa056a0  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validatePinCode
    // 0xbb3ac4: LeaveFrame
    //     0xbb3ac4: mov             SP, fp
    //     0xbb3ac8: ldp             fp, lr, [SP], #0x10
    // 0xbb3acc: ret
    //     0xbb3acc: ret             
    // 0xbb3ad0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb3ad0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb3ad4: b               #0xbb3abc
  }
  [closure] String? _validateAddress(dynamic, String?) {
    // ** addr: 0xbb3ad8, size: 0x3c
    // 0xbb3ad8: EnterFrame
    //     0xbb3ad8: stp             fp, lr, [SP, #-0x10]!
    //     0xbb3adc: mov             fp, SP
    // 0xbb3ae0: ldr             x0, [fp, #0x18]
    // 0xbb3ae4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb3ae4: ldur            w1, [x0, #0x17]
    // 0xbb3ae8: DecompressPointer r1
    //     0xbb3ae8: add             x1, x1, HEAP, lsl #32
    // 0xbb3aec: CheckStackOverflow
    //     0xbb3aec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb3af0: cmp             SP, x16
    //     0xbb3af4: b.ls            #0xbb3b0c
    // 0xbb3af8: ldr             x2, [fp, #0x10]
    // 0xbb3afc: r0 = _validateAddress()
    //     0xbb3afc: bl              #0xa023b0  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateAddress
    // 0xbb3b00: LeaveFrame
    //     0xbb3b00: mov             SP, fp
    //     0xbb3b04: ldp             fp, lr, [SP], #0x10
    // 0xbb3b08: ret
    //     0xbb3b08: ret             
    // 0xbb3b0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb3b0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb3b10: b               #0xbb3af8
  }
  [closure] String? _validateHouseNumber(dynamic, String?) {
    // ** addr: 0xbb3b14, size: 0x3c
    // 0xbb3b14: EnterFrame
    //     0xbb3b14: stp             fp, lr, [SP, #-0x10]!
    //     0xbb3b18: mov             fp, SP
    // 0xbb3b1c: ldr             x0, [fp, #0x18]
    // 0xbb3b20: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb3b20: ldur            w1, [x0, #0x17]
    // 0xbb3b24: DecompressPointer r1
    //     0xbb3b24: add             x1, x1, HEAP, lsl #32
    // 0xbb3b28: CheckStackOverflow
    //     0xbb3b28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb3b2c: cmp             SP, x16
    //     0xbb3b30: b.ls            #0xbb3b48
    // 0xbb3b34: ldr             x2, [fp, #0x10]
    // 0xbb3b38: r0 = _validateHouseNumber()
    //     0xbb3b38: bl              #0xa0594c  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateHouseNumber
    // 0xbb3b3c: LeaveFrame
    //     0xbb3b3c: mov             SP, fp
    //     0xbb3b40: ldp             fp, lr, [SP], #0x10
    // 0xbb3b44: ret
    //     0xbb3b44: ret             
    // 0xbb3b48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb3b48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb3b4c: b               #0xbb3b34
  }
  [closure] String? _validateCustomerNameNumber(dynamic, String?) {
    // ** addr: 0xbb3b50, size: 0x3c
    // 0xbb3b50: EnterFrame
    //     0xbb3b50: stp             fp, lr, [SP, #-0x10]!
    //     0xbb3b54: mov             fp, SP
    // 0xbb3b58: ldr             x0, [fp, #0x18]
    // 0xbb3b5c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb3b5c: ldur            w1, [x0, #0x17]
    // 0xbb3b60: DecompressPointer r1
    //     0xbb3b60: add             x1, x1, HEAP, lsl #32
    // 0xbb3b64: CheckStackOverflow
    //     0xbb3b64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb3b68: cmp             SP, x16
    //     0xbb3b6c: b.ls            #0xbb3b84
    // 0xbb3b70: ldr             x2, [fp, #0x10]
    // 0xbb3b74: r0 = _validateCustomerNameNumber()
    //     0xbb3b74: bl              #0xa05a54  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateCustomerNameNumber
    // 0xbb3b78: LeaveFrame
    //     0xbb3b78: mov             SP, fp
    //     0xbb3b7c: ldp             fp, lr, [SP], #0x10
    // 0xbb3b80: ret
    //     0xbb3b80: ret             
    // 0xbb3b84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb3b84: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb3b88: b               #0xbb3b70
  }
}

// class id: 4023, size: 0x24, field offset: 0xc
//   const constructor, 
class CheckoutAddressWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80194, size: 0x48
    // 0xc80194: EnterFrame
    //     0xc80194: stp             fp, lr, [SP, #-0x10]!
    //     0xc80198: mov             fp, SP
    // 0xc8019c: AllocStack(0x8)
    //     0xc8019c: sub             SP, SP, #8
    // 0xc801a0: CheckStackOverflow
    //     0xc801a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc801a4: cmp             SP, x16
    //     0xc801a8: b.ls            #0xc801d4
    // 0xc801ac: r1 = <CheckoutAddressWidget>
    //     0xc801ac: add             x1, PP, #0x48, lsl #12  ; [pp+0x48668] TypeArguments: <CheckoutAddressWidget>
    //     0xc801b0: ldr             x1, [x1, #0x668]
    // 0xc801b4: r0 = CheckoutAddressWidgetState()
    //     0xc801b4: bl              #0xc801dc  ; AllocateCheckoutAddressWidgetStateStub -> CheckoutAddressWidgetState (size=0x6c)
    // 0xc801b8: mov             x1, x0
    // 0xc801bc: stur            x0, [fp, #-8]
    // 0xc801c0: r0 = CheckoutAddressWidgetState()
    //     0xc801c0: bl              #0xc7a884  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::CheckoutAddressWidgetState
    // 0xc801c4: ldur            x0, [fp, #-8]
    // 0xc801c8: LeaveFrame
    //     0xc801c8: mov             SP, fp
    //     0xc801cc: ldp             fp, lr, [SP], #0x10
    // 0xc801d0: ret
    //     0xc801d0: ret             
    // 0xc801d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc801d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc801d8: b               #0xc801ac
  }
}
