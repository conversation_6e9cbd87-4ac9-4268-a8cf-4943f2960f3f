// lib: , url: package:customer_app/app/presentation/views/line/bag/customised_strip.dart

// class id: 1049469, size: 0x8
class :: {
}

// class id: 3289, size: 0x14, field offset: 0x14
class _CustomisedStripState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xba44a4, size: 0x3d0
    // 0xba44a4: EnterFrame
    //     0xba44a4: stp             fp, lr, [SP, #-0x10]!
    //     0xba44a8: mov             fp, SP
    // 0xba44ac: AllocStack(0x50)
    //     0xba44ac: sub             SP, SP, #0x50
    // 0xba44b0: SetupParameters(_CustomisedStripState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xba44b0: mov             x0, x1
    //     0xba44b4: stur            x1, [fp, #-8]
    //     0xba44b8: mov             x1, x2
    //     0xba44bc: stur            x2, [fp, #-0x10]
    // 0xba44c0: CheckStackOverflow
    //     0xba44c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba44c4: cmp             SP, x16
    //     0xba44c8: b.ls            #0xba4860
    // 0xba44cc: r1 = 1
    //     0xba44cc: movz            x1, #0x1
    // 0xba44d0: r0 = AllocateContext()
    //     0xba44d0: bl              #0x16f6108  ; AllocateContextStub
    // 0xba44d4: mov             x2, x0
    // 0xba44d8: ldur            x0, [fp, #-8]
    // 0xba44dc: stur            x2, [fp, #-0x18]
    // 0xba44e0: StoreField: r2->field_f = r0
    //     0xba44e0: stur            w0, [x2, #0xf]
    // 0xba44e4: ldur            x1, [fp, #-0x10]
    // 0xba44e8: r0 = of()
    //     0xba44e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba44ec: LoadField: r1 = r0->field_5b
    //     0xba44ec: ldur            w1, [x0, #0x5b]
    // 0xba44f0: DecompressPointer r1
    //     0xba44f0: add             x1, x1, HEAP, lsl #32
    // 0xba44f4: r0 = LoadClassIdInstr(r1)
    //     0xba44f4: ldur            x0, [x1, #-1]
    //     0xba44f8: ubfx            x0, x0, #0xc, #0x14
    // 0xba44fc: d0 = 0.100000
    //     0xba44fc: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xba4500: r0 = GDT[cid_x0 + -0xffa]()
    //     0xba4500: sub             lr, x0, #0xffa
    //     0xba4504: ldr             lr, [x21, lr, lsl #3]
    //     0xba4508: blr             lr
    // 0xba450c: mov             x2, x0
    // 0xba4510: r1 = Null
    //     0xba4510: mov             x1, NULL
    // 0xba4514: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xba4514: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xba4518: r0 = Border.all()
    //     0xba4518: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xba451c: stur            x0, [fp, #-0x20]
    // 0xba4520: r0 = BoxDecoration()
    //     0xba4520: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xba4524: mov             x2, x0
    // 0xba4528: ldur            x0, [fp, #-0x20]
    // 0xba452c: stur            x2, [fp, #-0x28]
    // 0xba4530: StoreField: r2->field_f = r0
    //     0xba4530: stur            w0, [x2, #0xf]
    // 0xba4534: r0 = Instance_BoxShape
    //     0xba4534: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xba4538: ldr             x0, [x0, #0x80]
    // 0xba453c: StoreField: r2->field_23 = r0
    //     0xba453c: stur            w0, [x2, #0x23]
    // 0xba4540: ldur            x1, [fp, #-0x10]
    // 0xba4544: r0 = of()
    //     0xba4544: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba4548: LoadField: r1 = r0->field_87
    //     0xba4548: ldur            w1, [x0, #0x87]
    // 0xba454c: DecompressPointer r1
    //     0xba454c: add             x1, x1, HEAP, lsl #32
    // 0xba4550: LoadField: r0 = r1->field_7
    //     0xba4550: ldur            w0, [x1, #7]
    // 0xba4554: DecompressPointer r0
    //     0xba4554: add             x0, x0, HEAP, lsl #32
    // 0xba4558: stur            x0, [fp, #-0x20]
    // 0xba455c: r1 = Instance_Color
    //     0xba455c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba4560: d0 = 0.700000
    //     0xba4560: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba4564: ldr             d0, [x17, #0xf48]
    // 0xba4568: r0 = withOpacity()
    //     0xba4568: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba456c: r16 = 12.000000
    //     0xba456c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba4570: ldr             x16, [x16, #0x9e8]
    // 0xba4574: stp             x0, x16, [SP]
    // 0xba4578: ldur            x1, [fp, #-0x20]
    // 0xba457c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba457c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba4580: ldr             x4, [x4, #0xaa0]
    // 0xba4584: r0 = copyWith()
    //     0xba4584: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba4588: stur            x0, [fp, #-0x20]
    // 0xba458c: r0 = Text()
    //     0xba458c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba4590: mov             x2, x0
    // 0xba4594: r0 = "Customisation Details"
    //     0xba4594: add             x0, PP, #0x54, lsl #12  ; [pp+0x54bb0] "Customisation Details"
    //     0xba4598: ldr             x0, [x0, #0xbb0]
    // 0xba459c: stur            x2, [fp, #-0x30]
    // 0xba45a0: StoreField: r2->field_b = r0
    //     0xba45a0: stur            w0, [x2, #0xb]
    // 0xba45a4: ldur            x0, [fp, #-0x20]
    // 0xba45a8: StoreField: r2->field_13 = r0
    //     0xba45a8: stur            w0, [x2, #0x13]
    // 0xba45ac: ldur            x3, [fp, #-8]
    // 0xba45b0: LoadField: r0 = r3->field_b
    //     0xba45b0: ldur            w0, [x3, #0xb]
    // 0xba45b4: DecompressPointer r0
    //     0xba45b4: add             x0, x0, HEAP, lsl #32
    // 0xba45b8: cmp             w0, NULL
    // 0xba45bc: b.eq            #0xba4868
    // 0xba45c0: LoadField: r1 = r0->field_b
    //     0xba45c0: ldur            w1, [x0, #0xb]
    // 0xba45c4: DecompressPointer r1
    //     0xba45c4: add             x1, x1, HEAP, lsl #32
    // 0xba45c8: cmp             w1, NULL
    // 0xba45cc: b.ne            #0xba45d8
    // 0xba45d0: r0 = Null
    //     0xba45d0: mov             x0, NULL
    // 0xba45d4: b               #0xba45f0
    // 0xba45d8: r0 = LoadClassIdInstr(r1)
    //     0xba45d8: ldur            x0, [x1, #-1]
    //     0xba45dc: ubfx            x0, x0, #0xc, #0x14
    // 0xba45e0: r0 = GDT[cid_x0 + 0xe517]()
    //     0xba45e0: movz            x17, #0xe517
    //     0xba45e4: add             lr, x0, x17
    //     0xba45e8: ldr             lr, [x21, lr, lsl #3]
    //     0xba45ec: blr             lr
    // 0xba45f0: cmp             w0, NULL
    // 0xba45f4: b.ne            #0xba4600
    // 0xba45f8: ldur            x0, [fp, #-8]
    // 0xba45fc: b               #0xba46cc
    // 0xba4600: tbnz            w0, #4, #0xba46c8
    // 0xba4604: ldur            x0, [fp, #-8]
    // 0xba4608: LoadField: r1 = r0->field_b
    //     0xba4608: ldur            w1, [x0, #0xb]
    // 0xba460c: DecompressPointer r1
    //     0xba460c: add             x1, x1, HEAP, lsl #32
    // 0xba4610: cmp             w1, NULL
    // 0xba4614: b.eq            #0xba486c
    // 0xba4618: LoadField: r0 = r1->field_b
    //     0xba4618: ldur            w0, [x1, #0xb]
    // 0xba461c: DecompressPointer r0
    //     0xba461c: add             x0, x0, HEAP, lsl #32
    // 0xba4620: cmp             w0, NULL
    // 0xba4624: b.ne            #0xba4630
    // 0xba4628: r0 = Null
    //     0xba4628: mov             x0, NULL
    // 0xba462c: b               #0xba4650
    // 0xba4630: r1 = LoadClassIdInstr(r0)
    //     0xba4630: ldur            x1, [x0, #-1]
    //     0xba4634: ubfx            x1, x1, #0xc, #0x14
    // 0xba4638: str             x0, [SP]
    // 0xba463c: mov             x0, x1
    // 0xba4640: r0 = GDT[cid_x0 + 0xc898]()
    //     0xba4640: movz            x17, #0xc898
    //     0xba4644: add             lr, x0, x17
    //     0xba4648: ldr             lr, [x21, lr, lsl #3]
    //     0xba464c: blr             lr
    // 0xba4650: cmp             w0, NULL
    // 0xba4654: b.ne            #0xba4660
    // 0xba4658: r0 = 0
    //     0xba4658: movz            x0, #0
    // 0xba465c: b               #0xba4668
    // 0xba4660: r1 = LoadInt32Instr(r0)
    //     0xba4660: sbfx            x1, x0, #1, #0x1f
    // 0xba4664: mov             x0, x1
    // 0xba4668: add             x2, x0, #1
    // 0xba466c: r0 = BoxInt64Instr(r2)
    //     0xba466c: sbfiz           x0, x2, #1, #0x1f
    //     0xba4670: cmp             x2, x0, asr #1
    //     0xba4674: b.eq            #0xba4680
    //     0xba4678: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xba467c: stur            x2, [x0, #7]
    // 0xba4680: ldur            x2, [fp, #-0x18]
    // 0xba4684: r1 = Function '<anonymous closure>':.
    //     0xba4684: add             x1, PP, #0x54, lsl #12  ; [pp+0x54bb8] AnonymousClosure: (0xba51a8), in [package:customer_app/app/presentation/views/line/bag/customised_strip.dart] _CustomisedStripState::build (0xba44a4)
    //     0xba4688: ldr             x1, [x1, #0xbb8]
    // 0xba468c: stur            x0, [fp, #-0x20]
    // 0xba4690: r0 = AllocateClosure()
    //     0xba4690: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xba4694: stur            x0, [fp, #-0x38]
    // 0xba4698: r0 = ListView()
    //     0xba4698: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xba469c: stur            x0, [fp, #-0x40]
    // 0xba46a0: r16 = true
    //     0xba46a0: add             x16, NULL, #0x20  ; true
    // 0xba46a4: str             x16, [SP]
    // 0xba46a8: mov             x1, x0
    // 0xba46ac: ldur            x2, [fp, #-0x38]
    // 0xba46b0: ldur            x3, [fp, #-0x20]
    // 0xba46b4: r4 = const [0, 0x4, 0x1, 0x3, shrinkWrap, 0x3, null]
    //     0xba46b4: add             x4, PP, #0x49, lsl #12  ; [pp+0x490f0] List(7) [0, 0x4, 0x1, 0x3, "shrinkWrap", 0x3, Null]
    //     0xba46b8: ldr             x4, [x4, #0xf0]
    // 0xba46bc: r0 = ListView.builder()
    //     0xba46bc: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xba46c0: ldur            x2, [fp, #-0x40]
    // 0xba46c4: b               #0xba4784
    // 0xba46c8: ldur            x0, [fp, #-8]
    // 0xba46cc: LoadField: r1 = r0->field_b
    //     0xba46cc: ldur            w1, [x0, #0xb]
    // 0xba46d0: DecompressPointer r1
    //     0xba46d0: add             x1, x1, HEAP, lsl #32
    // 0xba46d4: cmp             w1, NULL
    // 0xba46d8: b.eq            #0xba4870
    // 0xba46dc: LoadField: r0 = r1->field_f
    //     0xba46dc: ldur            w0, [x1, #0xf]
    // 0xba46e0: DecompressPointer r0
    //     0xba46e0: add             x0, x0, HEAP, lsl #32
    // 0xba46e4: cmp             w0, NULL
    // 0xba46e8: b.ne            #0xba46f4
    // 0xba46ec: r0 = Null
    //     0xba46ec: mov             x0, NULL
    // 0xba46f0: b               #0xba4710
    // 0xba46f4: LoadField: r1 = r0->field_13
    //     0xba46f4: ldur            w1, [x0, #0x13]
    // 0xba46f8: DecompressPointer r1
    //     0xba46f8: add             x1, x1, HEAP, lsl #32
    // 0xba46fc: cmp             w1, NULL
    // 0xba4700: b.ne            #0xba470c
    // 0xba4704: r0 = Null
    //     0xba4704: mov             x0, NULL
    // 0xba4708: b               #0xba4710
    // 0xba470c: LoadField: r0 = r1->field_b
    //     0xba470c: ldur            w0, [x1, #0xb]
    // 0xba4710: cmp             w0, NULL
    // 0xba4714: b.ne            #0xba4720
    // 0xba4718: r0 = 0
    //     0xba4718: movz            x0, #0
    // 0xba471c: b               #0xba4728
    // 0xba4720: r1 = LoadInt32Instr(r0)
    //     0xba4720: sbfx            x1, x0, #1, #0x1f
    // 0xba4724: mov             x0, x1
    // 0xba4728: add             x2, x0, #1
    // 0xba472c: r0 = BoxInt64Instr(r2)
    //     0xba472c: sbfiz           x0, x2, #1, #0x1f
    //     0xba4730: cmp             x2, x0, asr #1
    //     0xba4734: b.eq            #0xba4740
    //     0xba4738: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xba473c: stur            x2, [x0, #7]
    // 0xba4740: ldur            x2, [fp, #-0x18]
    // 0xba4744: r1 = Function '<anonymous closure>':.
    //     0xba4744: add             x1, PP, #0x54, lsl #12  ; [pp+0x54bc0] AnonymousClosure: (0xba4880), in [package:customer_app/app/presentation/views/line/bag/customised_strip.dart] _CustomisedStripState::build (0xba44a4)
    //     0xba4748: ldr             x1, [x1, #0xbc0]
    // 0xba474c: stur            x0, [fp, #-8]
    // 0xba4750: r0 = AllocateClosure()
    //     0xba4750: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xba4754: stur            x0, [fp, #-0x18]
    // 0xba4758: r0 = ListView()
    //     0xba4758: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xba475c: stur            x0, [fp, #-0x20]
    // 0xba4760: r16 = true
    //     0xba4760: add             x16, NULL, #0x20  ; true
    // 0xba4764: str             x16, [SP]
    // 0xba4768: mov             x1, x0
    // 0xba476c: ldur            x2, [fp, #-0x18]
    // 0xba4770: ldur            x3, [fp, #-8]
    // 0xba4774: r4 = const [0, 0x4, 0x1, 0x3, shrinkWrap, 0x3, null]
    //     0xba4774: add             x4, PP, #0x49, lsl #12  ; [pp+0x490f0] List(7) [0, 0x4, 0x1, 0x3, "shrinkWrap", 0x3, Null]
    //     0xba4778: ldr             x4, [x4, #0xf0]
    // 0xba477c: r0 = ListView.builder()
    //     0xba477c: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xba4780: ldur            x2, [fp, #-0x20]
    // 0xba4784: ldur            x0, [fp, #-0x30]
    // 0xba4788: ldur            x1, [fp, #-0x10]
    // 0xba478c: stur            x2, [fp, #-8]
    // 0xba4790: r0 = of()
    //     0xba4790: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba4794: LoadField: r1 = r0->field_87
    //     0xba4794: ldur            w1, [x0, #0x87]
    // 0xba4798: DecompressPointer r1
    //     0xba4798: add             x1, x1, HEAP, lsl #32
    // 0xba479c: LoadField: r0 = r1->field_7
    //     0xba479c: ldur            w0, [x1, #7]
    // 0xba47a0: DecompressPointer r0
    //     0xba47a0: add             x0, x0, HEAP, lsl #32
    // 0xba47a4: stur            x0, [fp, #-0x10]
    // 0xba47a8: r1 = Instance_Color
    //     0xba47a8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba47ac: d0 = 0.700000
    //     0xba47ac: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba47b0: ldr             d0, [x17, #0xf48]
    // 0xba47b4: r0 = withOpacity()
    //     0xba47b4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba47b8: r16 = 12.000000
    //     0xba47b8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba47bc: ldr             x16, [x16, #0x9e8]
    // 0xba47c0: stp             x0, x16, [SP]
    // 0xba47c4: ldur            x1, [fp, #-0x10]
    // 0xba47c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba47c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba47cc: ldr             x4, [x4, #0xaa0]
    // 0xba47d0: r0 = copyWith()
    //     0xba47d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba47d4: r0 = Accordion()
    //     0xba47d4: bl              #0xba4874  ; AllocateAccordionStub -> Accordion (size=0x3c)
    // 0xba47d8: mov             x1, x0
    // 0xba47dc: ldur            x0, [fp, #-0x30]
    // 0xba47e0: stur            x1, [fp, #-0x10]
    // 0xba47e4: StoreField: r1->field_b = r0
    //     0xba47e4: stur            w0, [x1, #0xb]
    // 0xba47e8: ldur            x0, [fp, #-8]
    // 0xba47ec: StoreField: r1->field_13 = r0
    //     0xba47ec: stur            w0, [x1, #0x13]
    // 0xba47f0: r0 = false
    //     0xba47f0: add             x0, NULL, #0x30  ; false
    // 0xba47f4: ArrayStore: r1[0] = r0  ; List_4
    //     0xba47f4: stur            w0, [x1, #0x17]
    // 0xba47f8: r0 = 24.000000
    //     0xba47f8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xba47fc: ldr             x0, [x0, #0xba8]
    // 0xba4800: StoreField: r1->field_1b = r0
    //     0xba4800: stur            w0, [x1, #0x1b]
    // 0xba4804: r0 = true
    //     0xba4804: add             x0, NULL, #0x20  ; true
    // 0xba4808: StoreField: r1->field_1f = r0
    //     0xba4808: stur            w0, [x1, #0x1f]
    // 0xba480c: r0 = Padding()
    //     0xba480c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba4810: mov             x1, x0
    // 0xba4814: r0 = Instance_EdgeInsets
    //     0xba4814: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xba4818: ldr             x0, [x0, #0xf30]
    // 0xba481c: stur            x1, [fp, #-8]
    // 0xba4820: StoreField: r1->field_f = r0
    //     0xba4820: stur            w0, [x1, #0xf]
    // 0xba4824: ldur            x0, [fp, #-0x10]
    // 0xba4828: StoreField: r1->field_b = r0
    //     0xba4828: stur            w0, [x1, #0xb]
    // 0xba482c: r0 = Container()
    //     0xba482c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xba4830: stur            x0, [fp, #-0x10]
    // 0xba4834: ldur            x16, [fp, #-0x28]
    // 0xba4838: ldur            lr, [fp, #-8]
    // 0xba483c: stp             lr, x16, [SP]
    // 0xba4840: mov             x1, x0
    // 0xba4844: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xba4844: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xba4848: ldr             x4, [x4, #0x88]
    // 0xba484c: r0 = Container()
    //     0xba484c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xba4850: ldur            x0, [fp, #-0x10]
    // 0xba4854: LeaveFrame
    //     0xba4854: mov             SP, fp
    //     0xba4858: ldp             fp, lr, [SP], #0x10
    // 0xba485c: ret
    //     0xba485c: ret             
    // 0xba4860: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba4860: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba4864: b               #0xba44cc
    // 0xba4868: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba4868: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba486c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba486c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba4870: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba4870: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xba4880, size: 0x8f8
    // 0xba4880: EnterFrame
    //     0xba4880: stp             fp, lr, [SP, #-0x10]!
    //     0xba4884: mov             fp, SP
    // 0xba4888: AllocStack(0x48)
    //     0xba4888: sub             SP, SP, #0x48
    // 0xba488c: SetupParameters()
    //     0xba488c: ldr             x0, [fp, #0x20]
    //     0xba4890: ldur            w1, [x0, #0x17]
    //     0xba4894: add             x1, x1, HEAP, lsl #32
    //     0xba4898: stur            x1, [fp, #-8]
    // 0xba489c: CheckStackOverflow
    //     0xba489c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba48a0: cmp             SP, x16
    //     0xba48a4: b.ls            #0xba513c
    // 0xba48a8: r0 = Container()
    //     0xba48a8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xba48ac: mov             x1, x0
    // 0xba48b0: stur            x0, [fp, #-0x10]
    // 0xba48b4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xba48b4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xba48b8: r0 = Container()
    //     0xba48b8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xba48bc: ldur            x0, [fp, #-8]
    // 0xba48c0: LoadField: r1 = r0->field_f
    //     0xba48c0: ldur            w1, [x0, #0xf]
    // 0xba48c4: DecompressPointer r1
    //     0xba48c4: add             x1, x1, HEAP, lsl #32
    // 0xba48c8: LoadField: r2 = r1->field_b
    //     0xba48c8: ldur            w2, [x1, #0xb]
    // 0xba48cc: DecompressPointer r2
    //     0xba48cc: add             x2, x2, HEAP, lsl #32
    // 0xba48d0: cmp             w2, NULL
    // 0xba48d4: b.eq            #0xba5144
    // 0xba48d8: LoadField: r1 = r2->field_f
    //     0xba48d8: ldur            w1, [x2, #0xf]
    // 0xba48dc: DecompressPointer r1
    //     0xba48dc: add             x1, x1, HEAP, lsl #32
    // 0xba48e0: cmp             w1, NULL
    // 0xba48e4: b.ne            #0xba48f0
    // 0xba48e8: r4 = Null
    //     0xba48e8: mov             x4, NULL
    // 0xba48ec: b               #0xba4914
    // 0xba48f0: LoadField: r3 = r1->field_13
    //     0xba48f0: ldur            w3, [x1, #0x13]
    // 0xba48f4: DecompressPointer r3
    //     0xba48f4: add             x3, x3, HEAP, lsl #32
    // 0xba48f8: cmp             w3, NULL
    // 0xba48fc: b.ne            #0xba4908
    // 0xba4900: r3 = Null
    //     0xba4900: mov             x3, NULL
    // 0xba4904: b               #0xba4910
    // 0xba4908: LoadField: r4 = r3->field_b
    //     0xba4908: ldur            w4, [x3, #0xb]
    // 0xba490c: mov             x3, x4
    // 0xba4910: mov             x4, x3
    // 0xba4914: ldr             x3, [fp, #0x10]
    // 0xba4918: cmp             w4, w3
    // 0xba491c: b.ne            #0xba4bd8
    // 0xba4920: r1 = Instance_Color
    //     0xba4920: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba4924: d0 = 0.100000
    //     0xba4924: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xba4928: r0 = withOpacity()
    //     0xba4928: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba492c: stur            x0, [fp, #-0x18]
    // 0xba4930: r0 = Divider()
    //     0xba4930: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xba4934: mov             x2, x0
    // 0xba4938: ldur            x0, [fp, #-0x18]
    // 0xba493c: stur            x2, [fp, #-0x20]
    // 0xba4940: StoreField: r2->field_1f = r0
    //     0xba4940: stur            w0, [x2, #0x1f]
    // 0xba4944: ldr             x1, [fp, #0x18]
    // 0xba4948: r0 = of()
    //     0xba4948: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba494c: LoadField: r1 = r0->field_87
    //     0xba494c: ldur            w1, [x0, #0x87]
    // 0xba4950: DecompressPointer r1
    //     0xba4950: add             x1, x1, HEAP, lsl #32
    // 0xba4954: LoadField: r0 = r1->field_7
    //     0xba4954: ldur            w0, [x1, #7]
    // 0xba4958: DecompressPointer r0
    //     0xba4958: add             x0, x0, HEAP, lsl #32
    // 0xba495c: stur            x0, [fp, #-0x18]
    // 0xba4960: r1 = Instance_Color
    //     0xba4960: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba4964: d0 = 0.700000
    //     0xba4964: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba4968: ldr             d0, [x17, #0xf48]
    // 0xba496c: r0 = withOpacity()
    //     0xba496c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba4970: r16 = 12.000000
    //     0xba4970: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba4974: ldr             x16, [x16, #0x9e8]
    // 0xba4978: stp             x0, x16, [SP]
    // 0xba497c: ldur            x1, [fp, #-0x18]
    // 0xba4980: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba4980: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba4984: ldr             x4, [x4, #0xaa0]
    // 0xba4988: r0 = copyWith()
    //     0xba4988: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba498c: stur            x0, [fp, #-0x18]
    // 0xba4990: r0 = Text()
    //     0xba4990: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba4994: mov             x1, x0
    // 0xba4998: r0 = "Total"
    //     0xba4998: add             x0, PP, #0x54, lsl #12  ; [pp+0x54bc8] "Total"
    //     0xba499c: ldr             x0, [x0, #0xbc8]
    // 0xba49a0: stur            x1, [fp, #-0x28]
    // 0xba49a4: StoreField: r1->field_b = r0
    //     0xba49a4: stur            w0, [x1, #0xb]
    // 0xba49a8: ldur            x0, [fp, #-0x18]
    // 0xba49ac: StoreField: r1->field_13 = r0
    //     0xba49ac: stur            w0, [x1, #0x13]
    // 0xba49b0: ldur            x4, [fp, #-8]
    // 0xba49b4: LoadField: r0 = r4->field_f
    //     0xba49b4: ldur            w0, [x4, #0xf]
    // 0xba49b8: DecompressPointer r0
    //     0xba49b8: add             x0, x0, HEAP, lsl #32
    // 0xba49bc: LoadField: r2 = r0->field_b
    //     0xba49bc: ldur            w2, [x0, #0xb]
    // 0xba49c0: DecompressPointer r2
    //     0xba49c0: add             x2, x2, HEAP, lsl #32
    // 0xba49c4: cmp             w2, NULL
    // 0xba49c8: b.eq            #0xba5148
    // 0xba49cc: LoadField: r0 = r2->field_13
    //     0xba49cc: ldur            w0, [x2, #0x13]
    // 0xba49d0: DecompressPointer r0
    //     0xba49d0: add             x0, x0, HEAP, lsl #32
    // 0xba49d4: r2 = LoadClassIdInstr(r0)
    //     0xba49d4: ldur            x2, [x0, #-1]
    //     0xba49d8: ubfx            x2, x2, #0xc, #0x14
    // 0xba49dc: str             x0, [SP]
    // 0xba49e0: mov             x0, x2
    // 0xba49e4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xba49e4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xba49e8: r0 = GDT[cid_x0 + 0x2700]()
    //     0xba49e8: movz            x17, #0x2700
    //     0xba49ec: add             lr, x0, x17
    //     0xba49f0: ldr             lr, [x21, lr, lsl #3]
    //     0xba49f4: blr             lr
    // 0xba49f8: ldr             x1, [fp, #0x18]
    // 0xba49fc: stur            x0, [fp, #-0x18]
    // 0xba4a00: r0 = of()
    //     0xba4a00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba4a04: LoadField: r1 = r0->field_87
    //     0xba4a04: ldur            w1, [x0, #0x87]
    // 0xba4a08: DecompressPointer r1
    //     0xba4a08: add             x1, x1, HEAP, lsl #32
    // 0xba4a0c: LoadField: r0 = r1->field_7
    //     0xba4a0c: ldur            w0, [x1, #7]
    // 0xba4a10: DecompressPointer r0
    //     0xba4a10: add             x0, x0, HEAP, lsl #32
    // 0xba4a14: r16 = 12.000000
    //     0xba4a14: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba4a18: ldr             x16, [x16, #0x9e8]
    // 0xba4a1c: r30 = Instance_Color
    //     0xba4a1c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba4a20: stp             lr, x16, [SP]
    // 0xba4a24: mov             x1, x0
    // 0xba4a28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba4a28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba4a2c: ldr             x4, [x4, #0xaa0]
    // 0xba4a30: r0 = copyWith()
    //     0xba4a30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba4a34: stur            x0, [fp, #-0x30]
    // 0xba4a38: r0 = Text()
    //     0xba4a38: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba4a3c: mov             x3, x0
    // 0xba4a40: ldur            x0, [fp, #-0x18]
    // 0xba4a44: stur            x3, [fp, #-0x38]
    // 0xba4a48: StoreField: r3->field_b = r0
    //     0xba4a48: stur            w0, [x3, #0xb]
    // 0xba4a4c: ldur            x0, [fp, #-0x30]
    // 0xba4a50: StoreField: r3->field_13 = r0
    //     0xba4a50: stur            w0, [x3, #0x13]
    // 0xba4a54: r1 = Null
    //     0xba4a54: mov             x1, NULL
    // 0xba4a58: r2 = 6
    //     0xba4a58: movz            x2, #0x6
    // 0xba4a5c: r0 = AllocateArray()
    //     0xba4a5c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba4a60: mov             x2, x0
    // 0xba4a64: ldur            x0, [fp, #-0x28]
    // 0xba4a68: stur            x2, [fp, #-0x18]
    // 0xba4a6c: StoreField: r2->field_f = r0
    //     0xba4a6c: stur            w0, [x2, #0xf]
    // 0xba4a70: r16 = Instance_Spacer
    //     0xba4a70: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xba4a74: ldr             x16, [x16, #0xf0]
    // 0xba4a78: StoreField: r2->field_13 = r16
    //     0xba4a78: stur            w16, [x2, #0x13]
    // 0xba4a7c: ldur            x0, [fp, #-0x38]
    // 0xba4a80: ArrayStore: r2[0] = r0  ; List_4
    //     0xba4a80: stur            w0, [x2, #0x17]
    // 0xba4a84: r1 = <Widget>
    //     0xba4a84: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba4a88: r0 = AllocateGrowableArray()
    //     0xba4a88: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba4a8c: mov             x1, x0
    // 0xba4a90: ldur            x0, [fp, #-0x18]
    // 0xba4a94: stur            x1, [fp, #-0x28]
    // 0xba4a98: StoreField: r1->field_f = r0
    //     0xba4a98: stur            w0, [x1, #0xf]
    // 0xba4a9c: r0 = 6
    //     0xba4a9c: movz            x0, #0x6
    // 0xba4aa0: StoreField: r1->field_b = r0
    //     0xba4aa0: stur            w0, [x1, #0xb]
    // 0xba4aa4: r0 = Row()
    //     0xba4aa4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xba4aa8: mov             x3, x0
    // 0xba4aac: r0 = Instance_Axis
    //     0xba4aac: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba4ab0: stur            x3, [fp, #-0x18]
    // 0xba4ab4: StoreField: r3->field_f = r0
    //     0xba4ab4: stur            w0, [x3, #0xf]
    // 0xba4ab8: r0 = Instance_MainAxisAlignment
    //     0xba4ab8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba4abc: ldr             x0, [x0, #0xa08]
    // 0xba4ac0: StoreField: r3->field_13 = r0
    //     0xba4ac0: stur            w0, [x3, #0x13]
    // 0xba4ac4: r4 = Instance_MainAxisSize
    //     0xba4ac4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba4ac8: ldr             x4, [x4, #0xa10]
    // 0xba4acc: ArrayStore: r3[0] = r4  ; List_4
    //     0xba4acc: stur            w4, [x3, #0x17]
    // 0xba4ad0: r5 = Instance_CrossAxisAlignment
    //     0xba4ad0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba4ad4: ldr             x5, [x5, #0xa18]
    // 0xba4ad8: StoreField: r3->field_1b = r5
    //     0xba4ad8: stur            w5, [x3, #0x1b]
    // 0xba4adc: r6 = Instance_VerticalDirection
    //     0xba4adc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba4ae0: ldr             x6, [x6, #0xa20]
    // 0xba4ae4: StoreField: r3->field_23 = r6
    //     0xba4ae4: stur            w6, [x3, #0x23]
    // 0xba4ae8: r7 = Instance_Clip
    //     0xba4ae8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba4aec: ldr             x7, [x7, #0x38]
    // 0xba4af0: StoreField: r3->field_2b = r7
    //     0xba4af0: stur            w7, [x3, #0x2b]
    // 0xba4af4: StoreField: r3->field_2f = rZR
    //     0xba4af4: stur            xzr, [x3, #0x2f]
    // 0xba4af8: ldur            x1, [fp, #-0x28]
    // 0xba4afc: StoreField: r3->field_b = r1
    //     0xba4afc: stur            w1, [x3, #0xb]
    // 0xba4b00: r1 = Null
    //     0xba4b00: mov             x1, NULL
    // 0xba4b04: r2 = 4
    //     0xba4b04: movz            x2, #0x4
    // 0xba4b08: r0 = AllocateArray()
    //     0xba4b08: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba4b0c: mov             x2, x0
    // 0xba4b10: ldur            x0, [fp, #-0x20]
    // 0xba4b14: stur            x2, [fp, #-0x28]
    // 0xba4b18: StoreField: r2->field_f = r0
    //     0xba4b18: stur            w0, [x2, #0xf]
    // 0xba4b1c: ldur            x0, [fp, #-0x18]
    // 0xba4b20: StoreField: r2->field_13 = r0
    //     0xba4b20: stur            w0, [x2, #0x13]
    // 0xba4b24: r1 = <Widget>
    //     0xba4b24: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba4b28: r0 = AllocateGrowableArray()
    //     0xba4b28: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba4b2c: mov             x1, x0
    // 0xba4b30: ldur            x0, [fp, #-0x28]
    // 0xba4b34: stur            x1, [fp, #-0x18]
    // 0xba4b38: StoreField: r1->field_f = r0
    //     0xba4b38: stur            w0, [x1, #0xf]
    // 0xba4b3c: r0 = 4
    //     0xba4b3c: movz            x0, #0x4
    // 0xba4b40: StoreField: r1->field_b = r0
    //     0xba4b40: stur            w0, [x1, #0xb]
    // 0xba4b44: r0 = Column()
    //     0xba4b44: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xba4b48: mov             x1, x0
    // 0xba4b4c: r0 = Instance_Axis
    //     0xba4b4c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xba4b50: stur            x1, [fp, #-0x20]
    // 0xba4b54: StoreField: r1->field_f = r0
    //     0xba4b54: stur            w0, [x1, #0xf]
    // 0xba4b58: r0 = Instance_MainAxisAlignment
    //     0xba4b58: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba4b5c: ldr             x0, [x0, #0xa08]
    // 0xba4b60: StoreField: r1->field_13 = r0
    //     0xba4b60: stur            w0, [x1, #0x13]
    // 0xba4b64: r0 = Instance_MainAxisSize
    //     0xba4b64: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba4b68: ldr             x0, [x0, #0xa10]
    // 0xba4b6c: ArrayStore: r1[0] = r0  ; List_4
    //     0xba4b6c: stur            w0, [x1, #0x17]
    // 0xba4b70: r0 = Instance_CrossAxisAlignment
    //     0xba4b70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba4b74: ldr             x0, [x0, #0xa18]
    // 0xba4b78: StoreField: r1->field_1b = r0
    //     0xba4b78: stur            w0, [x1, #0x1b]
    // 0xba4b7c: r0 = Instance_VerticalDirection
    //     0xba4b7c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba4b80: ldr             x0, [x0, #0xa20]
    // 0xba4b84: StoreField: r1->field_23 = r0
    //     0xba4b84: stur            w0, [x1, #0x23]
    // 0xba4b88: r0 = Instance_Clip
    //     0xba4b88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba4b8c: ldr             x0, [x0, #0x38]
    // 0xba4b90: StoreField: r1->field_2b = r0
    //     0xba4b90: stur            w0, [x1, #0x2b]
    // 0xba4b94: StoreField: r1->field_2f = rZR
    //     0xba4b94: stur            xzr, [x1, #0x2f]
    // 0xba4b98: ldur            x0, [fp, #-0x18]
    // 0xba4b9c: StoreField: r1->field_b = r0
    //     0xba4b9c: stur            w0, [x1, #0xb]
    // 0xba4ba0: r0 = Padding()
    //     0xba4ba0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba4ba4: mov             x1, x0
    // 0xba4ba8: r0 = Instance_EdgeInsets
    //     0xba4ba8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0xba4bac: ldr             x0, [x0, #0x868]
    // 0xba4bb0: stur            x1, [fp, #-0x18]
    // 0xba4bb4: StoreField: r1->field_f = r0
    //     0xba4bb4: stur            w0, [x1, #0xf]
    // 0xba4bb8: ldur            x0, [fp, #-0x20]
    // 0xba4bbc: StoreField: r1->field_b = r0
    //     0xba4bbc: stur            w0, [x1, #0xb]
    // 0xba4bc0: r0 = IntrinsicHeight()
    //     0xba4bc0: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xba4bc4: mov             x1, x0
    // 0xba4bc8: ldur            x0, [fp, #-0x18]
    // 0xba4bcc: StoreField: r1->field_b = r0
    //     0xba4bcc: stur            w0, [x1, #0xb]
    // 0xba4bd0: mov             x0, x1
    // 0xba4bd4: b               #0xba5130
    // 0xba4bd8: mov             x4, x0
    // 0xba4bdc: cmp             w1, NULL
    // 0xba4be0: b.ne            #0xba4bec
    // 0xba4be4: r0 = Null
    //     0xba4be4: mov             x0, NULL
    // 0xba4be8: b               #0xba4c44
    // 0xba4bec: LoadField: r5 = r1->field_13
    //     0xba4bec: ldur            w5, [x1, #0x13]
    // 0xba4bf0: DecompressPointer r5
    //     0xba4bf0: add             x5, x5, HEAP, lsl #32
    // 0xba4bf4: cmp             w5, NULL
    // 0xba4bf8: b.ne            #0xba4c04
    // 0xba4bfc: r0 = Null
    //     0xba4bfc: mov             x0, NULL
    // 0xba4c00: b               #0xba4c44
    // 0xba4c04: LoadField: r0 = r5->field_b
    //     0xba4c04: ldur            w0, [x5, #0xb]
    // 0xba4c08: r6 = LoadInt32Instr(r3)
    //     0xba4c08: sbfx            x6, x3, #1, #0x1f
    //     0xba4c0c: tbz             w3, #0, #0xba4c14
    //     0xba4c10: ldur            x6, [x3, #7]
    // 0xba4c14: r1 = LoadInt32Instr(r0)
    //     0xba4c14: sbfx            x1, x0, #1, #0x1f
    // 0xba4c18: mov             x0, x1
    // 0xba4c1c: mov             x1, x6
    // 0xba4c20: cmp             x1, x0
    // 0xba4c24: b.hs            #0xba514c
    // 0xba4c28: LoadField: r0 = r5->field_f
    //     0xba4c28: ldur            w0, [x5, #0xf]
    // 0xba4c2c: DecompressPointer r0
    //     0xba4c2c: add             x0, x0, HEAP, lsl #32
    // 0xba4c30: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xba4c30: add             x16, x0, x6, lsl #2
    //     0xba4c34: ldur            w1, [x16, #0xf]
    // 0xba4c38: DecompressPointer r1
    //     0xba4c38: add             x1, x1, HEAP, lsl #32
    // 0xba4c3c: LoadField: r0 = r1->field_f
    //     0xba4c3c: ldur            w0, [x1, #0xf]
    // 0xba4c40: DecompressPointer r0
    //     0xba4c40: add             x0, x0, HEAP, lsl #32
    // 0xba4c44: r16 = Instance_CustomisationType
    //     0xba4c44: add             x16, PP, #0x23, lsl #12  ; [pp+0x23660] Obj!CustomisationType@d756e1
    //     0xba4c48: ldr             x16, [x16, #0x660]
    // 0xba4c4c: cmp             w0, w16
    // 0xba4c50: b.ne            #0xba4d40
    // 0xba4c54: LoadField: r0 = r2->field_b
    //     0xba4c54: ldur            w0, [x2, #0xb]
    // 0xba4c58: DecompressPointer r0
    //     0xba4c58: add             x0, x0, HEAP, lsl #32
    // 0xba4c5c: cmp             w0, NULL
    // 0xba4c60: b.ne            #0xba4c70
    // 0xba4c64: mov             x1, x4
    // 0xba4c68: r2 = Null
    //     0xba4c68: mov             x2, NULL
    // 0xba4c6c: b               #0xba4c94
    // 0xba4c70: r1 = LoadClassIdInstr(r0)
    //     0xba4c70: ldur            x1, [x0, #-1]
    //     0xba4c74: ubfx            x1, x1, #0xc, #0x14
    // 0xba4c78: stp             x3, x0, [SP]
    // 0xba4c7c: mov             x0, x1
    // 0xba4c80: r0 = GDT[cid_x0 + -0xb7]()
    //     0xba4c80: sub             lr, x0, #0xb7
    //     0xba4c84: ldr             lr, [x21, lr, lsl #3]
    //     0xba4c88: blr             lr
    // 0xba4c8c: mov             x2, x0
    // 0xba4c90: ldur            x1, [fp, #-8]
    // 0xba4c94: stur            x2, [fp, #-0x20]
    // 0xba4c98: LoadField: r0 = r1->field_f
    //     0xba4c98: ldur            w0, [x1, #0xf]
    // 0xba4c9c: DecompressPointer r0
    //     0xba4c9c: add             x0, x0, HEAP, lsl #32
    // 0xba4ca0: LoadField: r1 = r0->field_b
    //     0xba4ca0: ldur            w1, [x0, #0xb]
    // 0xba4ca4: DecompressPointer r1
    //     0xba4ca4: add             x1, x1, HEAP, lsl #32
    // 0xba4ca8: cmp             w1, NULL
    // 0xba4cac: b.eq            #0xba5150
    // 0xba4cb0: LoadField: r0 = r1->field_f
    //     0xba4cb0: ldur            w0, [x1, #0xf]
    // 0xba4cb4: DecompressPointer r0
    //     0xba4cb4: add             x0, x0, HEAP, lsl #32
    // 0xba4cb8: cmp             w0, NULL
    // 0xba4cbc: b.ne            #0xba4cc8
    // 0xba4cc0: r0 = Null
    //     0xba4cc0: mov             x0, NULL
    // 0xba4cc4: b               #0xba4d20
    // 0xba4cc8: LoadField: r3 = r0->field_13
    //     0xba4cc8: ldur            w3, [x0, #0x13]
    // 0xba4ccc: DecompressPointer r3
    //     0xba4ccc: add             x3, x3, HEAP, lsl #32
    // 0xba4cd0: cmp             w3, NULL
    // 0xba4cd4: b.ne            #0xba4ce0
    // 0xba4cd8: r0 = Null
    //     0xba4cd8: mov             x0, NULL
    // 0xba4cdc: b               #0xba4d20
    // 0xba4ce0: ldr             x4, [fp, #0x10]
    // 0xba4ce4: LoadField: r0 = r3->field_b
    //     0xba4ce4: ldur            w0, [x3, #0xb]
    // 0xba4ce8: r5 = LoadInt32Instr(r4)
    //     0xba4ce8: sbfx            x5, x4, #1, #0x1f
    //     0xba4cec: tbz             w4, #0, #0xba4cf4
    //     0xba4cf0: ldur            x5, [x4, #7]
    // 0xba4cf4: r1 = LoadInt32Instr(r0)
    //     0xba4cf4: sbfx            x1, x0, #1, #0x1f
    // 0xba4cf8: mov             x0, x1
    // 0xba4cfc: mov             x1, x5
    // 0xba4d00: cmp             x1, x0
    // 0xba4d04: b.hs            #0xba5154
    // 0xba4d08: LoadField: r0 = r3->field_f
    //     0xba4d08: ldur            w0, [x3, #0xf]
    // 0xba4d0c: DecompressPointer r0
    //     0xba4d0c: add             x0, x0, HEAP, lsl #32
    // 0xba4d10: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba4d10: add             x16, x0, x5, lsl #2
    //     0xba4d14: ldur            w1, [x16, #0xf]
    // 0xba4d18: DecompressPointer r1
    //     0xba4d18: add             x1, x1, HEAP, lsl #32
    // 0xba4d1c: mov             x0, x1
    // 0xba4d20: stur            x0, [fp, #-0x18]
    // 0xba4d24: r0 = BagMultiSelect()
    //     0xba4d24: bl              #0xba519c  ; AllocateBagMultiSelectStub -> BagMultiSelect (size=0x14)
    // 0xba4d28: mov             x1, x0
    // 0xba4d2c: ldur            x0, [fp, #-0x20]
    // 0xba4d30: StoreField: r1->field_b = r0
    //     0xba4d30: stur            w0, [x1, #0xb]
    // 0xba4d34: ldur            x0, [fp, #-0x18]
    // 0xba4d38: StoreField: r1->field_f = r0
    //     0xba4d38: stur            w0, [x1, #0xf]
    // 0xba4d3c: b               #0xba512c
    // 0xba4d40: mov             x1, x4
    // 0xba4d44: mov             x4, x3
    // 0xba4d48: r16 = Instance_CustomisationType
    //     0xba4d48: add             x16, PP, #0x23, lsl #12  ; [pp+0x23670] Obj!CustomisationType@d756c1
    //     0xba4d4c: ldr             x16, [x16, #0x670]
    // 0xba4d50: cmp             w0, w16
    // 0xba4d54: b.ne            #0xba4e40
    // 0xba4d58: LoadField: r0 = r2->field_b
    //     0xba4d58: ldur            w0, [x2, #0xb]
    // 0xba4d5c: DecompressPointer r0
    //     0xba4d5c: add             x0, x0, HEAP, lsl #32
    // 0xba4d60: cmp             w0, NULL
    // 0xba4d64: b.ne            #0xba4d70
    // 0xba4d68: r2 = Null
    //     0xba4d68: mov             x2, NULL
    // 0xba4d6c: b               #0xba4d94
    // 0xba4d70: r2 = LoadClassIdInstr(r0)
    //     0xba4d70: ldur            x2, [x0, #-1]
    //     0xba4d74: ubfx            x2, x2, #0xc, #0x14
    // 0xba4d78: stp             x4, x0, [SP]
    // 0xba4d7c: mov             x0, x2
    // 0xba4d80: r0 = GDT[cid_x0 + -0xb7]()
    //     0xba4d80: sub             lr, x0, #0xb7
    //     0xba4d84: ldr             lr, [x21, lr, lsl #3]
    //     0xba4d88: blr             lr
    // 0xba4d8c: mov             x2, x0
    // 0xba4d90: ldur            x1, [fp, #-8]
    // 0xba4d94: stur            x2, [fp, #-0x20]
    // 0xba4d98: LoadField: r0 = r1->field_f
    //     0xba4d98: ldur            w0, [x1, #0xf]
    // 0xba4d9c: DecompressPointer r0
    //     0xba4d9c: add             x0, x0, HEAP, lsl #32
    // 0xba4da0: LoadField: r1 = r0->field_b
    //     0xba4da0: ldur            w1, [x0, #0xb]
    // 0xba4da4: DecompressPointer r1
    //     0xba4da4: add             x1, x1, HEAP, lsl #32
    // 0xba4da8: cmp             w1, NULL
    // 0xba4dac: b.eq            #0xba5158
    // 0xba4db0: LoadField: r0 = r1->field_f
    //     0xba4db0: ldur            w0, [x1, #0xf]
    // 0xba4db4: DecompressPointer r0
    //     0xba4db4: add             x0, x0, HEAP, lsl #32
    // 0xba4db8: cmp             w0, NULL
    // 0xba4dbc: b.ne            #0xba4dc8
    // 0xba4dc0: r0 = Null
    //     0xba4dc0: mov             x0, NULL
    // 0xba4dc4: b               #0xba4e20
    // 0xba4dc8: LoadField: r3 = r0->field_13
    //     0xba4dc8: ldur            w3, [x0, #0x13]
    // 0xba4dcc: DecompressPointer r3
    //     0xba4dcc: add             x3, x3, HEAP, lsl #32
    // 0xba4dd0: cmp             w3, NULL
    // 0xba4dd4: b.ne            #0xba4de0
    // 0xba4dd8: r0 = Null
    //     0xba4dd8: mov             x0, NULL
    // 0xba4ddc: b               #0xba4e20
    // 0xba4de0: ldr             x4, [fp, #0x10]
    // 0xba4de4: LoadField: r0 = r3->field_b
    //     0xba4de4: ldur            w0, [x3, #0xb]
    // 0xba4de8: r5 = LoadInt32Instr(r4)
    //     0xba4de8: sbfx            x5, x4, #1, #0x1f
    //     0xba4dec: tbz             w4, #0, #0xba4df4
    //     0xba4df0: ldur            x5, [x4, #7]
    // 0xba4df4: r1 = LoadInt32Instr(r0)
    //     0xba4df4: sbfx            x1, x0, #1, #0x1f
    // 0xba4df8: mov             x0, x1
    // 0xba4dfc: mov             x1, x5
    // 0xba4e00: cmp             x1, x0
    // 0xba4e04: b.hs            #0xba515c
    // 0xba4e08: LoadField: r0 = r3->field_f
    //     0xba4e08: ldur            w0, [x3, #0xf]
    // 0xba4e0c: DecompressPointer r0
    //     0xba4e0c: add             x0, x0, HEAP, lsl #32
    // 0xba4e10: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba4e10: add             x16, x0, x5, lsl #2
    //     0xba4e14: ldur            w1, [x16, #0xf]
    // 0xba4e18: DecompressPointer r1
    //     0xba4e18: add             x1, x1, HEAP, lsl #32
    // 0xba4e1c: mov             x0, x1
    // 0xba4e20: stur            x0, [fp, #-0x18]
    // 0xba4e24: r0 = BagSingleSelect()
    //     0xba4e24: bl              #0xba5190  ; AllocateBagSingleSelectStub -> BagSingleSelect (size=0x14)
    // 0xba4e28: mov             x1, x0
    // 0xba4e2c: ldur            x0, [fp, #-0x20]
    // 0xba4e30: StoreField: r1->field_b = r0
    //     0xba4e30: stur            w0, [x1, #0xb]
    // 0xba4e34: ldur            x0, [fp, #-0x18]
    // 0xba4e38: StoreField: r1->field_f = r0
    //     0xba4e38: stur            w0, [x1, #0xf]
    // 0xba4e3c: b               #0xba512c
    // 0xba4e40: r16 = Instance_CustomisationType
    //     0xba4e40: add             x16, PP, #0x23, lsl #12  ; [pp+0x23650] Obj!CustomisationType@d75701
    //     0xba4e44: ldr             x16, [x16, #0x650]
    // 0xba4e48: cmp             w0, w16
    // 0xba4e4c: b.ne            #0xba4f38
    // 0xba4e50: LoadField: r0 = r2->field_b
    //     0xba4e50: ldur            w0, [x2, #0xb]
    // 0xba4e54: DecompressPointer r0
    //     0xba4e54: add             x0, x0, HEAP, lsl #32
    // 0xba4e58: cmp             w0, NULL
    // 0xba4e5c: b.ne            #0xba4e68
    // 0xba4e60: r2 = Null
    //     0xba4e60: mov             x2, NULL
    // 0xba4e64: b               #0xba4e8c
    // 0xba4e68: r2 = LoadClassIdInstr(r0)
    //     0xba4e68: ldur            x2, [x0, #-1]
    //     0xba4e6c: ubfx            x2, x2, #0xc, #0x14
    // 0xba4e70: stp             x4, x0, [SP]
    // 0xba4e74: mov             x0, x2
    // 0xba4e78: r0 = GDT[cid_x0 + -0xb7]()
    //     0xba4e78: sub             lr, x0, #0xb7
    //     0xba4e7c: ldr             lr, [x21, lr, lsl #3]
    //     0xba4e80: blr             lr
    // 0xba4e84: mov             x2, x0
    // 0xba4e88: ldur            x1, [fp, #-8]
    // 0xba4e8c: stur            x2, [fp, #-0x20]
    // 0xba4e90: LoadField: r0 = r1->field_f
    //     0xba4e90: ldur            w0, [x1, #0xf]
    // 0xba4e94: DecompressPointer r0
    //     0xba4e94: add             x0, x0, HEAP, lsl #32
    // 0xba4e98: LoadField: r1 = r0->field_b
    //     0xba4e98: ldur            w1, [x0, #0xb]
    // 0xba4e9c: DecompressPointer r1
    //     0xba4e9c: add             x1, x1, HEAP, lsl #32
    // 0xba4ea0: cmp             w1, NULL
    // 0xba4ea4: b.eq            #0xba5160
    // 0xba4ea8: LoadField: r0 = r1->field_f
    //     0xba4ea8: ldur            w0, [x1, #0xf]
    // 0xba4eac: DecompressPointer r0
    //     0xba4eac: add             x0, x0, HEAP, lsl #32
    // 0xba4eb0: cmp             w0, NULL
    // 0xba4eb4: b.ne            #0xba4ec0
    // 0xba4eb8: r0 = Null
    //     0xba4eb8: mov             x0, NULL
    // 0xba4ebc: b               #0xba4f18
    // 0xba4ec0: LoadField: r3 = r0->field_13
    //     0xba4ec0: ldur            w3, [x0, #0x13]
    // 0xba4ec4: DecompressPointer r3
    //     0xba4ec4: add             x3, x3, HEAP, lsl #32
    // 0xba4ec8: cmp             w3, NULL
    // 0xba4ecc: b.ne            #0xba4ed8
    // 0xba4ed0: r0 = Null
    //     0xba4ed0: mov             x0, NULL
    // 0xba4ed4: b               #0xba4f18
    // 0xba4ed8: ldr             x4, [fp, #0x10]
    // 0xba4edc: LoadField: r0 = r3->field_b
    //     0xba4edc: ldur            w0, [x3, #0xb]
    // 0xba4ee0: r5 = LoadInt32Instr(r4)
    //     0xba4ee0: sbfx            x5, x4, #1, #0x1f
    //     0xba4ee4: tbz             w4, #0, #0xba4eec
    //     0xba4ee8: ldur            x5, [x4, #7]
    // 0xba4eec: r1 = LoadInt32Instr(r0)
    //     0xba4eec: sbfx            x1, x0, #1, #0x1f
    // 0xba4ef0: mov             x0, x1
    // 0xba4ef4: mov             x1, x5
    // 0xba4ef8: cmp             x1, x0
    // 0xba4efc: b.hs            #0xba5164
    // 0xba4f00: LoadField: r0 = r3->field_f
    //     0xba4f00: ldur            w0, [x3, #0xf]
    // 0xba4f04: DecompressPointer r0
    //     0xba4f04: add             x0, x0, HEAP, lsl #32
    // 0xba4f08: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba4f08: add             x16, x0, x5, lsl #2
    //     0xba4f0c: ldur            w1, [x16, #0xf]
    // 0xba4f10: DecompressPointer r1
    //     0xba4f10: add             x1, x1, HEAP, lsl #32
    // 0xba4f14: mov             x0, x1
    // 0xba4f18: stur            x0, [fp, #-0x18]
    // 0xba4f1c: r0 = BagImages()
    //     0xba4f1c: bl              #0xba5184  ; AllocateBagImagesStub -> BagImages (size=0x14)
    // 0xba4f20: mov             x1, x0
    // 0xba4f24: ldur            x0, [fp, #-0x20]
    // 0xba4f28: StoreField: r1->field_b = r0
    //     0xba4f28: stur            w0, [x1, #0xb]
    // 0xba4f2c: ldur            x0, [fp, #-0x18]
    // 0xba4f30: StoreField: r1->field_f = r0
    //     0xba4f30: stur            w0, [x1, #0xf]
    // 0xba4f34: b               #0xba512c
    // 0xba4f38: r16 = Instance_CustomisationType
    //     0xba4f38: add             x16, PP, #0x23, lsl #12  ; [pp+0x23680] Obj!CustomisationType@d756a1
    //     0xba4f3c: ldr             x16, [x16, #0x680]
    // 0xba4f40: cmp             w0, w16
    // 0xba4f44: b.ne            #0xba5030
    // 0xba4f48: LoadField: r0 = r2->field_b
    //     0xba4f48: ldur            w0, [x2, #0xb]
    // 0xba4f4c: DecompressPointer r0
    //     0xba4f4c: add             x0, x0, HEAP, lsl #32
    // 0xba4f50: cmp             w0, NULL
    // 0xba4f54: b.ne            #0xba4f60
    // 0xba4f58: r2 = Null
    //     0xba4f58: mov             x2, NULL
    // 0xba4f5c: b               #0xba4f84
    // 0xba4f60: r2 = LoadClassIdInstr(r0)
    //     0xba4f60: ldur            x2, [x0, #-1]
    //     0xba4f64: ubfx            x2, x2, #0xc, #0x14
    // 0xba4f68: stp             x4, x0, [SP]
    // 0xba4f6c: mov             x0, x2
    // 0xba4f70: r0 = GDT[cid_x0 + -0xb7]()
    //     0xba4f70: sub             lr, x0, #0xb7
    //     0xba4f74: ldr             lr, [x21, lr, lsl #3]
    //     0xba4f78: blr             lr
    // 0xba4f7c: mov             x2, x0
    // 0xba4f80: ldur            x1, [fp, #-8]
    // 0xba4f84: stur            x2, [fp, #-0x20]
    // 0xba4f88: LoadField: r0 = r1->field_f
    //     0xba4f88: ldur            w0, [x1, #0xf]
    // 0xba4f8c: DecompressPointer r0
    //     0xba4f8c: add             x0, x0, HEAP, lsl #32
    // 0xba4f90: LoadField: r1 = r0->field_b
    //     0xba4f90: ldur            w1, [x0, #0xb]
    // 0xba4f94: DecompressPointer r1
    //     0xba4f94: add             x1, x1, HEAP, lsl #32
    // 0xba4f98: cmp             w1, NULL
    // 0xba4f9c: b.eq            #0xba5168
    // 0xba4fa0: LoadField: r0 = r1->field_f
    //     0xba4fa0: ldur            w0, [x1, #0xf]
    // 0xba4fa4: DecompressPointer r0
    //     0xba4fa4: add             x0, x0, HEAP, lsl #32
    // 0xba4fa8: cmp             w0, NULL
    // 0xba4fac: b.ne            #0xba4fb8
    // 0xba4fb0: r0 = Null
    //     0xba4fb0: mov             x0, NULL
    // 0xba4fb4: b               #0xba5010
    // 0xba4fb8: LoadField: r3 = r0->field_13
    //     0xba4fb8: ldur            w3, [x0, #0x13]
    // 0xba4fbc: DecompressPointer r3
    //     0xba4fbc: add             x3, x3, HEAP, lsl #32
    // 0xba4fc0: cmp             w3, NULL
    // 0xba4fc4: b.ne            #0xba4fd0
    // 0xba4fc8: r0 = Null
    //     0xba4fc8: mov             x0, NULL
    // 0xba4fcc: b               #0xba5010
    // 0xba4fd0: ldr             x4, [fp, #0x10]
    // 0xba4fd4: LoadField: r0 = r3->field_b
    //     0xba4fd4: ldur            w0, [x3, #0xb]
    // 0xba4fd8: r5 = LoadInt32Instr(r4)
    //     0xba4fd8: sbfx            x5, x4, #1, #0x1f
    //     0xba4fdc: tbz             w4, #0, #0xba4fe4
    //     0xba4fe0: ldur            x5, [x4, #7]
    // 0xba4fe4: r1 = LoadInt32Instr(r0)
    //     0xba4fe4: sbfx            x1, x0, #1, #0x1f
    // 0xba4fe8: mov             x0, x1
    // 0xba4fec: mov             x1, x5
    // 0xba4ff0: cmp             x1, x0
    // 0xba4ff4: b.hs            #0xba516c
    // 0xba4ff8: LoadField: r0 = r3->field_f
    //     0xba4ff8: ldur            w0, [x3, #0xf]
    // 0xba4ffc: DecompressPointer r0
    //     0xba4ffc: add             x0, x0, HEAP, lsl #32
    // 0xba5000: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba5000: add             x16, x0, x5, lsl #2
    //     0xba5004: ldur            w1, [x16, #0xf]
    // 0xba5008: DecompressPointer r1
    //     0xba5008: add             x1, x1, HEAP, lsl #32
    // 0xba500c: mov             x0, x1
    // 0xba5010: stur            x0, [fp, #-0x18]
    // 0xba5014: r0 = BagText()
    //     0xba5014: bl              #0xba5178  ; AllocateBagTextStub -> BagText (size=0x14)
    // 0xba5018: mov             x1, x0
    // 0xba501c: ldur            x0, [fp, #-0x20]
    // 0xba5020: StoreField: r1->field_b = r0
    //     0xba5020: stur            w0, [x1, #0xb]
    // 0xba5024: ldur            x0, [fp, #-0x18]
    // 0xba5028: StoreField: r1->field_f = r0
    //     0xba5028: stur            w0, [x1, #0xf]
    // 0xba502c: b               #0xba512c
    // 0xba5030: r16 = Instance_CustomisationType
    //     0xba5030: add             x16, PP, #0x23, lsl #12  ; [pp+0x23690] Obj!CustomisationType@d75681
    //     0xba5034: ldr             x16, [x16, #0x690]
    // 0xba5038: cmp             w0, w16
    // 0xba503c: b.ne            #0xba5128
    // 0xba5040: LoadField: r0 = r2->field_b
    //     0xba5040: ldur            w0, [x2, #0xb]
    // 0xba5044: DecompressPointer r0
    //     0xba5044: add             x0, x0, HEAP, lsl #32
    // 0xba5048: cmp             w0, NULL
    // 0xba504c: b.ne            #0xba505c
    // 0xba5050: mov             x0, x1
    // 0xba5054: r2 = Null
    //     0xba5054: mov             x2, NULL
    // 0xba5058: b               #0xba5080
    // 0xba505c: r2 = LoadClassIdInstr(r0)
    //     0xba505c: ldur            x2, [x0, #-1]
    //     0xba5060: ubfx            x2, x2, #0xc, #0x14
    // 0xba5064: stp             x4, x0, [SP]
    // 0xba5068: mov             x0, x2
    // 0xba506c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xba506c: sub             lr, x0, #0xb7
    //     0xba5070: ldr             lr, [x21, lr, lsl #3]
    //     0xba5074: blr             lr
    // 0xba5078: mov             x2, x0
    // 0xba507c: ldur            x0, [fp, #-8]
    // 0xba5080: stur            x2, [fp, #-0x18]
    // 0xba5084: LoadField: r1 = r0->field_f
    //     0xba5084: ldur            w1, [x0, #0xf]
    // 0xba5088: DecompressPointer r1
    //     0xba5088: add             x1, x1, HEAP, lsl #32
    // 0xba508c: LoadField: r0 = r1->field_b
    //     0xba508c: ldur            w0, [x1, #0xb]
    // 0xba5090: DecompressPointer r0
    //     0xba5090: add             x0, x0, HEAP, lsl #32
    // 0xba5094: cmp             w0, NULL
    // 0xba5098: b.eq            #0xba5170
    // 0xba509c: LoadField: r1 = r0->field_f
    //     0xba509c: ldur            w1, [x0, #0xf]
    // 0xba50a0: DecompressPointer r1
    //     0xba50a0: add             x1, x1, HEAP, lsl #32
    // 0xba50a4: cmp             w1, NULL
    // 0xba50a8: b.ne            #0xba50b4
    // 0xba50ac: r0 = Null
    //     0xba50ac: mov             x0, NULL
    // 0xba50b0: b               #0xba5108
    // 0xba50b4: LoadField: r3 = r1->field_13
    //     0xba50b4: ldur            w3, [x1, #0x13]
    // 0xba50b8: DecompressPointer r3
    //     0xba50b8: add             x3, x3, HEAP, lsl #32
    // 0xba50bc: cmp             w3, NULL
    // 0xba50c0: b.ne            #0xba50cc
    // 0xba50c4: r0 = Null
    //     0xba50c4: mov             x0, NULL
    // 0xba50c8: b               #0xba5108
    // 0xba50cc: ldr             x0, [fp, #0x10]
    // 0xba50d0: LoadField: r1 = r3->field_b
    //     0xba50d0: ldur            w1, [x3, #0xb]
    // 0xba50d4: r4 = LoadInt32Instr(r0)
    //     0xba50d4: sbfx            x4, x0, #1, #0x1f
    //     0xba50d8: tbz             w0, #0, #0xba50e0
    //     0xba50dc: ldur            x4, [x0, #7]
    // 0xba50e0: r0 = LoadInt32Instr(r1)
    //     0xba50e0: sbfx            x0, x1, #1, #0x1f
    // 0xba50e4: mov             x1, x4
    // 0xba50e8: cmp             x1, x0
    // 0xba50ec: b.hs            #0xba5174
    // 0xba50f0: LoadField: r0 = r3->field_f
    //     0xba50f0: ldur            w0, [x3, #0xf]
    // 0xba50f4: DecompressPointer r0
    //     0xba50f4: add             x0, x0, HEAP, lsl #32
    // 0xba50f8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xba50f8: add             x16, x0, x4, lsl #2
    //     0xba50fc: ldur            w1, [x16, #0xf]
    // 0xba5100: DecompressPointer r1
    //     0xba5100: add             x1, x1, HEAP, lsl #32
    // 0xba5104: mov             x0, x1
    // 0xba5108: stur            x0, [fp, #-8]
    // 0xba510c: r0 = BagText()
    //     0xba510c: bl              #0xba5178  ; AllocateBagTextStub -> BagText (size=0x14)
    // 0xba5110: ldur            x1, [fp, #-0x18]
    // 0xba5114: StoreField: r0->field_b = r1
    //     0xba5114: stur            w1, [x0, #0xb]
    // 0xba5118: ldur            x1, [fp, #-8]
    // 0xba511c: StoreField: r0->field_f = r1
    //     0xba511c: stur            w1, [x0, #0xf]
    // 0xba5120: mov             x1, x0
    // 0xba5124: b               #0xba512c
    // 0xba5128: ldur            x1, [fp, #-0x10]
    // 0xba512c: mov             x0, x1
    // 0xba5130: LeaveFrame
    //     0xba5130: mov             SP, fp
    //     0xba5134: ldp             fp, lr, [SP], #0x10
    // 0xba5138: ret
    //     0xba5138: ret             
    // 0xba513c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba513c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba5140: b               #0xba48a8
    // 0xba5144: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5144: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5148: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5148: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba514c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba514c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba5150: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5150: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5154: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba5154: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba5158: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5158: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba515c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba515c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba5160: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5160: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5164: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba5164: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba5168: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5168: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba516c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba516c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba5170: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5170: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5174: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba5174: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xba51a8, size: 0x968
    // 0xba51a8: EnterFrame
    //     0xba51a8: stp             fp, lr, [SP, #-0x10]!
    //     0xba51ac: mov             fp, SP
    // 0xba51b0: AllocStack(0x48)
    //     0xba51b0: sub             SP, SP, #0x48
    // 0xba51b4: SetupParameters()
    //     0xba51b4: ldr             x0, [fp, #0x20]
    //     0xba51b8: ldur            w1, [x0, #0x17]
    //     0xba51bc: add             x1, x1, HEAP, lsl #32
    //     0xba51c0: stur            x1, [fp, #-8]
    // 0xba51c4: CheckStackOverflow
    //     0xba51c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba51c8: cmp             SP, x16
    //     0xba51cc: b.ls            #0xba5ac0
    // 0xba51d0: r0 = Container()
    //     0xba51d0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xba51d4: mov             x1, x0
    // 0xba51d8: stur            x0, [fp, #-0x10]
    // 0xba51dc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xba51dc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xba51e0: r0 = Container()
    //     0xba51e0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xba51e4: ldur            x1, [fp, #-8]
    // 0xba51e8: LoadField: r0 = r1->field_f
    //     0xba51e8: ldur            w0, [x1, #0xf]
    // 0xba51ec: DecompressPointer r0
    //     0xba51ec: add             x0, x0, HEAP, lsl #32
    // 0xba51f0: LoadField: r2 = r0->field_b
    //     0xba51f0: ldur            w2, [x0, #0xb]
    // 0xba51f4: DecompressPointer r2
    //     0xba51f4: add             x2, x2, HEAP, lsl #32
    // 0xba51f8: cmp             w2, NULL
    // 0xba51fc: b.eq            #0xba5ac8
    // 0xba5200: LoadField: r0 = r2->field_b
    //     0xba5200: ldur            w0, [x2, #0xb]
    // 0xba5204: DecompressPointer r0
    //     0xba5204: add             x0, x0, HEAP, lsl #32
    // 0xba5208: cmp             w0, NULL
    // 0xba520c: b.ne            #0xba5218
    // 0xba5210: r0 = Null
    //     0xba5210: mov             x0, NULL
    // 0xba5214: b               #0xba5238
    // 0xba5218: r2 = LoadClassIdInstr(r0)
    //     0xba5218: ldur            x2, [x0, #-1]
    //     0xba521c: ubfx            x2, x2, #0xc, #0x14
    // 0xba5220: str             x0, [SP]
    // 0xba5224: mov             x0, x2
    // 0xba5228: r0 = GDT[cid_x0 + 0xc898]()
    //     0xba5228: movz            x17, #0xc898
    //     0xba522c: add             lr, x0, x17
    //     0xba5230: ldr             lr, [x21, lr, lsl #3]
    //     0xba5234: blr             lr
    // 0xba5238: ldr             x1, [fp, #0x10]
    // 0xba523c: cmp             w0, w1
    // 0xba5240: b.ne            #0xba54f0
    // 0xba5244: ldur            x0, [fp, #-8]
    // 0xba5248: r1 = Instance_Color
    //     0xba5248: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba524c: d0 = 0.100000
    //     0xba524c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xba5250: r0 = withOpacity()
    //     0xba5250: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba5254: stur            x0, [fp, #-0x18]
    // 0xba5258: r0 = Divider()
    //     0xba5258: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xba525c: mov             x2, x0
    // 0xba5260: ldur            x0, [fp, #-0x18]
    // 0xba5264: stur            x2, [fp, #-0x20]
    // 0xba5268: StoreField: r2->field_1f = r0
    //     0xba5268: stur            w0, [x2, #0x1f]
    // 0xba526c: ldr             x1, [fp, #0x18]
    // 0xba5270: r0 = of()
    //     0xba5270: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba5274: LoadField: r1 = r0->field_87
    //     0xba5274: ldur            w1, [x0, #0x87]
    // 0xba5278: DecompressPointer r1
    //     0xba5278: add             x1, x1, HEAP, lsl #32
    // 0xba527c: LoadField: r0 = r1->field_7
    //     0xba527c: ldur            w0, [x1, #7]
    // 0xba5280: DecompressPointer r0
    //     0xba5280: add             x0, x0, HEAP, lsl #32
    // 0xba5284: r16 = Instance_Color
    //     0xba5284: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba5288: r30 = 16.000000
    //     0xba5288: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xba528c: ldr             lr, [lr, #0x188]
    // 0xba5290: stp             lr, x16, [SP]
    // 0xba5294: mov             x1, x0
    // 0xba5298: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xba5298: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xba529c: ldr             x4, [x4, #0x9b8]
    // 0xba52a0: r0 = copyWith()
    //     0xba52a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba52a4: stur            x0, [fp, #-0x18]
    // 0xba52a8: r0 = Text()
    //     0xba52a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba52ac: mov             x1, x0
    // 0xba52b0: r0 = "Total"
    //     0xba52b0: add             x0, PP, #0x54, lsl #12  ; [pp+0x54bc8] "Total"
    //     0xba52b4: ldr             x0, [x0, #0xbc8]
    // 0xba52b8: stur            x1, [fp, #-0x28]
    // 0xba52bc: StoreField: r1->field_b = r0
    //     0xba52bc: stur            w0, [x1, #0xb]
    // 0xba52c0: ldur            x0, [fp, #-0x18]
    // 0xba52c4: StoreField: r1->field_13 = r0
    //     0xba52c4: stur            w0, [x1, #0x13]
    // 0xba52c8: ldur            x2, [fp, #-8]
    // 0xba52cc: LoadField: r0 = r2->field_f
    //     0xba52cc: ldur            w0, [x2, #0xf]
    // 0xba52d0: DecompressPointer r0
    //     0xba52d0: add             x0, x0, HEAP, lsl #32
    // 0xba52d4: LoadField: r2 = r0->field_b
    //     0xba52d4: ldur            w2, [x0, #0xb]
    // 0xba52d8: DecompressPointer r2
    //     0xba52d8: add             x2, x2, HEAP, lsl #32
    // 0xba52dc: cmp             w2, NULL
    // 0xba52e0: b.eq            #0xba5acc
    // 0xba52e4: LoadField: r0 = r2->field_13
    //     0xba52e4: ldur            w0, [x2, #0x13]
    // 0xba52e8: DecompressPointer r0
    //     0xba52e8: add             x0, x0, HEAP, lsl #32
    // 0xba52ec: r2 = LoadClassIdInstr(r0)
    //     0xba52ec: ldur            x2, [x0, #-1]
    //     0xba52f0: ubfx            x2, x2, #0xc, #0x14
    // 0xba52f4: str             x0, [SP]
    // 0xba52f8: mov             x0, x2
    // 0xba52fc: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xba52fc: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xba5300: r0 = GDT[cid_x0 + 0x2700]()
    //     0xba5300: movz            x17, #0x2700
    //     0xba5304: add             lr, x0, x17
    //     0xba5308: ldr             lr, [x21, lr, lsl #3]
    //     0xba530c: blr             lr
    // 0xba5310: ldr             x1, [fp, #0x18]
    // 0xba5314: stur            x0, [fp, #-0x18]
    // 0xba5318: r0 = of()
    //     0xba5318: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba531c: LoadField: r1 = r0->field_87
    //     0xba531c: ldur            w1, [x0, #0x87]
    // 0xba5320: DecompressPointer r1
    //     0xba5320: add             x1, x1, HEAP, lsl #32
    // 0xba5324: LoadField: r0 = r1->field_7
    //     0xba5324: ldur            w0, [x1, #7]
    // 0xba5328: DecompressPointer r0
    //     0xba5328: add             x0, x0, HEAP, lsl #32
    // 0xba532c: r16 = Instance_Color
    //     0xba532c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba5330: r30 = 16.000000
    //     0xba5330: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xba5334: ldr             lr, [lr, #0x188]
    // 0xba5338: stp             lr, x16, [SP]
    // 0xba533c: mov             x1, x0
    // 0xba5340: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xba5340: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xba5344: ldr             x4, [x4, #0x9b8]
    // 0xba5348: r0 = copyWith()
    //     0xba5348: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba534c: stur            x0, [fp, #-0x30]
    // 0xba5350: r0 = Text()
    //     0xba5350: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba5354: mov             x3, x0
    // 0xba5358: ldur            x0, [fp, #-0x18]
    // 0xba535c: stur            x3, [fp, #-0x38]
    // 0xba5360: StoreField: r3->field_b = r0
    //     0xba5360: stur            w0, [x3, #0xb]
    // 0xba5364: ldur            x0, [fp, #-0x30]
    // 0xba5368: StoreField: r3->field_13 = r0
    //     0xba5368: stur            w0, [x3, #0x13]
    // 0xba536c: r1 = Null
    //     0xba536c: mov             x1, NULL
    // 0xba5370: r2 = 6
    //     0xba5370: movz            x2, #0x6
    // 0xba5374: r0 = AllocateArray()
    //     0xba5374: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba5378: mov             x2, x0
    // 0xba537c: ldur            x0, [fp, #-0x28]
    // 0xba5380: stur            x2, [fp, #-0x18]
    // 0xba5384: StoreField: r2->field_f = r0
    //     0xba5384: stur            w0, [x2, #0xf]
    // 0xba5388: r16 = Instance_Spacer
    //     0xba5388: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xba538c: ldr             x16, [x16, #0xf0]
    // 0xba5390: StoreField: r2->field_13 = r16
    //     0xba5390: stur            w16, [x2, #0x13]
    // 0xba5394: ldur            x0, [fp, #-0x38]
    // 0xba5398: ArrayStore: r2[0] = r0  ; List_4
    //     0xba5398: stur            w0, [x2, #0x17]
    // 0xba539c: r1 = <Widget>
    //     0xba539c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba53a0: r0 = AllocateGrowableArray()
    //     0xba53a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba53a4: mov             x1, x0
    // 0xba53a8: ldur            x0, [fp, #-0x18]
    // 0xba53ac: stur            x1, [fp, #-0x28]
    // 0xba53b0: StoreField: r1->field_f = r0
    //     0xba53b0: stur            w0, [x1, #0xf]
    // 0xba53b4: r0 = 6
    //     0xba53b4: movz            x0, #0x6
    // 0xba53b8: StoreField: r1->field_b = r0
    //     0xba53b8: stur            w0, [x1, #0xb]
    // 0xba53bc: r0 = Row()
    //     0xba53bc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xba53c0: mov             x3, x0
    // 0xba53c4: r0 = Instance_Axis
    //     0xba53c4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba53c8: stur            x3, [fp, #-0x18]
    // 0xba53cc: StoreField: r3->field_f = r0
    //     0xba53cc: stur            w0, [x3, #0xf]
    // 0xba53d0: r0 = Instance_MainAxisAlignment
    //     0xba53d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba53d4: ldr             x0, [x0, #0xa08]
    // 0xba53d8: StoreField: r3->field_13 = r0
    //     0xba53d8: stur            w0, [x3, #0x13]
    // 0xba53dc: r4 = Instance_MainAxisSize
    //     0xba53dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba53e0: ldr             x4, [x4, #0xa10]
    // 0xba53e4: ArrayStore: r3[0] = r4  ; List_4
    //     0xba53e4: stur            w4, [x3, #0x17]
    // 0xba53e8: r5 = Instance_CrossAxisAlignment
    //     0xba53e8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba53ec: ldr             x5, [x5, #0xa18]
    // 0xba53f0: StoreField: r3->field_1b = r5
    //     0xba53f0: stur            w5, [x3, #0x1b]
    // 0xba53f4: r6 = Instance_VerticalDirection
    //     0xba53f4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba53f8: ldr             x6, [x6, #0xa20]
    // 0xba53fc: StoreField: r3->field_23 = r6
    //     0xba53fc: stur            w6, [x3, #0x23]
    // 0xba5400: r7 = Instance_Clip
    //     0xba5400: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba5404: ldr             x7, [x7, #0x38]
    // 0xba5408: StoreField: r3->field_2b = r7
    //     0xba5408: stur            w7, [x3, #0x2b]
    // 0xba540c: StoreField: r3->field_2f = rZR
    //     0xba540c: stur            xzr, [x3, #0x2f]
    // 0xba5410: ldur            x1, [fp, #-0x28]
    // 0xba5414: StoreField: r3->field_b = r1
    //     0xba5414: stur            w1, [x3, #0xb]
    // 0xba5418: r1 = Null
    //     0xba5418: mov             x1, NULL
    // 0xba541c: r2 = 4
    //     0xba541c: movz            x2, #0x4
    // 0xba5420: r0 = AllocateArray()
    //     0xba5420: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba5424: mov             x2, x0
    // 0xba5428: ldur            x0, [fp, #-0x20]
    // 0xba542c: stur            x2, [fp, #-0x28]
    // 0xba5430: StoreField: r2->field_f = r0
    //     0xba5430: stur            w0, [x2, #0xf]
    // 0xba5434: ldur            x0, [fp, #-0x18]
    // 0xba5438: StoreField: r2->field_13 = r0
    //     0xba5438: stur            w0, [x2, #0x13]
    // 0xba543c: r1 = <Widget>
    //     0xba543c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba5440: r0 = AllocateGrowableArray()
    //     0xba5440: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba5444: mov             x1, x0
    // 0xba5448: ldur            x0, [fp, #-0x28]
    // 0xba544c: stur            x1, [fp, #-0x18]
    // 0xba5450: StoreField: r1->field_f = r0
    //     0xba5450: stur            w0, [x1, #0xf]
    // 0xba5454: r0 = 4
    //     0xba5454: movz            x0, #0x4
    // 0xba5458: StoreField: r1->field_b = r0
    //     0xba5458: stur            w0, [x1, #0xb]
    // 0xba545c: r0 = Column()
    //     0xba545c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xba5460: mov             x1, x0
    // 0xba5464: r0 = Instance_Axis
    //     0xba5464: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xba5468: stur            x1, [fp, #-0x20]
    // 0xba546c: StoreField: r1->field_f = r0
    //     0xba546c: stur            w0, [x1, #0xf]
    // 0xba5470: r0 = Instance_MainAxisAlignment
    //     0xba5470: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba5474: ldr             x0, [x0, #0xa08]
    // 0xba5478: StoreField: r1->field_13 = r0
    //     0xba5478: stur            w0, [x1, #0x13]
    // 0xba547c: r0 = Instance_MainAxisSize
    //     0xba547c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba5480: ldr             x0, [x0, #0xa10]
    // 0xba5484: ArrayStore: r1[0] = r0  ; List_4
    //     0xba5484: stur            w0, [x1, #0x17]
    // 0xba5488: r0 = Instance_CrossAxisAlignment
    //     0xba5488: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba548c: ldr             x0, [x0, #0xa18]
    // 0xba5490: StoreField: r1->field_1b = r0
    //     0xba5490: stur            w0, [x1, #0x1b]
    // 0xba5494: r0 = Instance_VerticalDirection
    //     0xba5494: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba5498: ldr             x0, [x0, #0xa20]
    // 0xba549c: StoreField: r1->field_23 = r0
    //     0xba549c: stur            w0, [x1, #0x23]
    // 0xba54a0: r0 = Instance_Clip
    //     0xba54a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba54a4: ldr             x0, [x0, #0x38]
    // 0xba54a8: StoreField: r1->field_2b = r0
    //     0xba54a8: stur            w0, [x1, #0x2b]
    // 0xba54ac: StoreField: r1->field_2f = rZR
    //     0xba54ac: stur            xzr, [x1, #0x2f]
    // 0xba54b0: ldur            x0, [fp, #-0x18]
    // 0xba54b4: StoreField: r1->field_b = r0
    //     0xba54b4: stur            w0, [x1, #0xb]
    // 0xba54b8: r0 = Padding()
    //     0xba54b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba54bc: mov             x1, x0
    // 0xba54c0: r0 = Instance_EdgeInsets
    //     0xba54c0: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0xba54c4: ldr             x0, [x0, #0x868]
    // 0xba54c8: stur            x1, [fp, #-0x18]
    // 0xba54cc: StoreField: r1->field_f = r0
    //     0xba54cc: stur            w0, [x1, #0xf]
    // 0xba54d0: ldur            x0, [fp, #-0x20]
    // 0xba54d4: StoreField: r1->field_b = r0
    //     0xba54d4: stur            w0, [x1, #0xb]
    // 0xba54d8: r0 = IntrinsicHeight()
    //     0xba54d8: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xba54dc: mov             x1, x0
    // 0xba54e0: ldur            x0, [fp, #-0x18]
    // 0xba54e4: StoreField: r1->field_b = r0
    //     0xba54e4: stur            w0, [x1, #0xb]
    // 0xba54e8: mov             x0, x1
    // 0xba54ec: b               #0xba5ab4
    // 0xba54f0: ldur            x2, [fp, #-8]
    // 0xba54f4: LoadField: r0 = r2->field_f
    //     0xba54f4: ldur            w0, [x2, #0xf]
    // 0xba54f8: DecompressPointer r0
    //     0xba54f8: add             x0, x0, HEAP, lsl #32
    // 0xba54fc: LoadField: r3 = r0->field_b
    //     0xba54fc: ldur            w3, [x0, #0xb]
    // 0xba5500: DecompressPointer r3
    //     0xba5500: add             x3, x3, HEAP, lsl #32
    // 0xba5504: cmp             w3, NULL
    // 0xba5508: b.eq            #0xba5ad0
    // 0xba550c: LoadField: r0 = r3->field_b
    //     0xba550c: ldur            w0, [x3, #0xb]
    // 0xba5510: DecompressPointer r0
    //     0xba5510: add             x0, x0, HEAP, lsl #32
    // 0xba5514: cmp             w0, NULL
    // 0xba5518: b.ne            #0xba5524
    // 0xba551c: r0 = Null
    //     0xba551c: mov             x0, NULL
    // 0xba5520: b               #0xba554c
    // 0xba5524: r3 = LoadClassIdInstr(r0)
    //     0xba5524: ldur            x3, [x0, #-1]
    //     0xba5528: ubfx            x3, x3, #0xc, #0x14
    // 0xba552c: stp             x1, x0, [SP]
    // 0xba5530: mov             x0, x3
    // 0xba5534: r0 = GDT[cid_x0 + -0xb7]()
    //     0xba5534: sub             lr, x0, #0xb7
    //     0xba5538: ldr             lr, [x21, lr, lsl #3]
    //     0xba553c: blr             lr
    // 0xba5540: LoadField: r1 = r0->field_f
    //     0xba5540: ldur            w1, [x0, #0xf]
    // 0xba5544: DecompressPointer r1
    //     0xba5544: add             x1, x1, HEAP, lsl #32
    // 0xba5548: mov             x0, x1
    // 0xba554c: r16 = Instance_CustomisationType
    //     0xba554c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23660] Obj!CustomisationType@d756e1
    //     0xba5550: ldr             x16, [x16, #0x660]
    // 0xba5554: cmp             w0, w16
    // 0xba5558: b.ne            #0xba5664
    // 0xba555c: ldur            x1, [fp, #-8]
    // 0xba5560: LoadField: r0 = r1->field_f
    //     0xba5560: ldur            w0, [x1, #0xf]
    // 0xba5564: DecompressPointer r0
    //     0xba5564: add             x0, x0, HEAP, lsl #32
    // 0xba5568: LoadField: r2 = r0->field_b
    //     0xba5568: ldur            w2, [x0, #0xb]
    // 0xba556c: DecompressPointer r2
    //     0xba556c: add             x2, x2, HEAP, lsl #32
    // 0xba5570: cmp             w2, NULL
    // 0xba5574: b.eq            #0xba5ad4
    // 0xba5578: LoadField: r0 = r2->field_b
    //     0xba5578: ldur            w0, [x2, #0xb]
    // 0xba557c: DecompressPointer r0
    //     0xba557c: add             x0, x0, HEAP, lsl #32
    // 0xba5580: cmp             w0, NULL
    // 0xba5584: b.ne            #0xba5590
    // 0xba5588: r2 = Null
    //     0xba5588: mov             x2, NULL
    // 0xba558c: b               #0xba55b8
    // 0xba5590: r2 = LoadClassIdInstr(r0)
    //     0xba5590: ldur            x2, [x0, #-1]
    //     0xba5594: ubfx            x2, x2, #0xc, #0x14
    // 0xba5598: ldr             x16, [fp, #0x10]
    // 0xba559c: stp             x16, x0, [SP]
    // 0xba55a0: mov             x0, x2
    // 0xba55a4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xba55a4: sub             lr, x0, #0xb7
    //     0xba55a8: ldr             lr, [x21, lr, lsl #3]
    //     0xba55ac: blr             lr
    // 0xba55b0: mov             x2, x0
    // 0xba55b4: ldur            x1, [fp, #-8]
    // 0xba55b8: stur            x2, [fp, #-0x20]
    // 0xba55bc: LoadField: r0 = r1->field_f
    //     0xba55bc: ldur            w0, [x1, #0xf]
    // 0xba55c0: DecompressPointer r0
    //     0xba55c0: add             x0, x0, HEAP, lsl #32
    // 0xba55c4: LoadField: r1 = r0->field_b
    //     0xba55c4: ldur            w1, [x0, #0xb]
    // 0xba55c8: DecompressPointer r1
    //     0xba55c8: add             x1, x1, HEAP, lsl #32
    // 0xba55cc: cmp             w1, NULL
    // 0xba55d0: b.eq            #0xba5ad8
    // 0xba55d4: LoadField: r0 = r1->field_f
    //     0xba55d4: ldur            w0, [x1, #0xf]
    // 0xba55d8: DecompressPointer r0
    //     0xba55d8: add             x0, x0, HEAP, lsl #32
    // 0xba55dc: cmp             w0, NULL
    // 0xba55e0: b.ne            #0xba55ec
    // 0xba55e4: r0 = Null
    //     0xba55e4: mov             x0, NULL
    // 0xba55e8: b               #0xba5644
    // 0xba55ec: LoadField: r3 = r0->field_13
    //     0xba55ec: ldur            w3, [x0, #0x13]
    // 0xba55f0: DecompressPointer r3
    //     0xba55f0: add             x3, x3, HEAP, lsl #32
    // 0xba55f4: cmp             w3, NULL
    // 0xba55f8: b.ne            #0xba5604
    // 0xba55fc: r0 = Null
    //     0xba55fc: mov             x0, NULL
    // 0xba5600: b               #0xba5644
    // 0xba5604: ldr             x4, [fp, #0x10]
    // 0xba5608: LoadField: r0 = r3->field_b
    //     0xba5608: ldur            w0, [x3, #0xb]
    // 0xba560c: r5 = LoadInt32Instr(r4)
    //     0xba560c: sbfx            x5, x4, #1, #0x1f
    //     0xba5610: tbz             w4, #0, #0xba5618
    //     0xba5614: ldur            x5, [x4, #7]
    // 0xba5618: r1 = LoadInt32Instr(r0)
    //     0xba5618: sbfx            x1, x0, #1, #0x1f
    // 0xba561c: mov             x0, x1
    // 0xba5620: mov             x1, x5
    // 0xba5624: cmp             x1, x0
    // 0xba5628: b.hs            #0xba5adc
    // 0xba562c: LoadField: r0 = r3->field_f
    //     0xba562c: ldur            w0, [x3, #0xf]
    // 0xba5630: DecompressPointer r0
    //     0xba5630: add             x0, x0, HEAP, lsl #32
    // 0xba5634: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba5634: add             x16, x0, x5, lsl #2
    //     0xba5638: ldur            w1, [x16, #0xf]
    // 0xba563c: DecompressPointer r1
    //     0xba563c: add             x1, x1, HEAP, lsl #32
    // 0xba5640: mov             x0, x1
    // 0xba5644: stur            x0, [fp, #-0x18]
    // 0xba5648: r0 = BagMultiSelect()
    //     0xba5648: bl              #0xba519c  ; AllocateBagMultiSelectStub -> BagMultiSelect (size=0x14)
    // 0xba564c: mov             x1, x0
    // 0xba5650: ldur            x0, [fp, #-0x20]
    // 0xba5654: StoreField: r1->field_b = r0
    //     0xba5654: stur            w0, [x1, #0xb]
    // 0xba5658: ldur            x0, [fp, #-0x18]
    // 0xba565c: StoreField: r1->field_f = r0
    //     0xba565c: stur            w0, [x1, #0xf]
    // 0xba5660: b               #0xba5ab0
    // 0xba5664: ldr             x4, [fp, #0x10]
    // 0xba5668: ldur            x1, [fp, #-8]
    // 0xba566c: r16 = Instance_CustomisationType
    //     0xba566c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23670] Obj!CustomisationType@d756c1
    //     0xba5670: ldr             x16, [x16, #0x670]
    // 0xba5674: cmp             w0, w16
    // 0xba5678: b.ne            #0xba577c
    // 0xba567c: LoadField: r0 = r1->field_f
    //     0xba567c: ldur            w0, [x1, #0xf]
    // 0xba5680: DecompressPointer r0
    //     0xba5680: add             x0, x0, HEAP, lsl #32
    // 0xba5684: LoadField: r2 = r0->field_b
    //     0xba5684: ldur            w2, [x0, #0xb]
    // 0xba5688: DecompressPointer r2
    //     0xba5688: add             x2, x2, HEAP, lsl #32
    // 0xba568c: cmp             w2, NULL
    // 0xba5690: b.eq            #0xba5ae0
    // 0xba5694: LoadField: r0 = r2->field_b
    //     0xba5694: ldur            w0, [x2, #0xb]
    // 0xba5698: DecompressPointer r0
    //     0xba5698: add             x0, x0, HEAP, lsl #32
    // 0xba569c: cmp             w0, NULL
    // 0xba56a0: b.ne            #0xba56ac
    // 0xba56a4: r2 = Null
    //     0xba56a4: mov             x2, NULL
    // 0xba56a8: b               #0xba56d0
    // 0xba56ac: r2 = LoadClassIdInstr(r0)
    //     0xba56ac: ldur            x2, [x0, #-1]
    //     0xba56b0: ubfx            x2, x2, #0xc, #0x14
    // 0xba56b4: stp             x4, x0, [SP]
    // 0xba56b8: mov             x0, x2
    // 0xba56bc: r0 = GDT[cid_x0 + -0xb7]()
    //     0xba56bc: sub             lr, x0, #0xb7
    //     0xba56c0: ldr             lr, [x21, lr, lsl #3]
    //     0xba56c4: blr             lr
    // 0xba56c8: mov             x2, x0
    // 0xba56cc: ldur            x1, [fp, #-8]
    // 0xba56d0: stur            x2, [fp, #-0x20]
    // 0xba56d4: LoadField: r0 = r1->field_f
    //     0xba56d4: ldur            w0, [x1, #0xf]
    // 0xba56d8: DecompressPointer r0
    //     0xba56d8: add             x0, x0, HEAP, lsl #32
    // 0xba56dc: LoadField: r1 = r0->field_b
    //     0xba56dc: ldur            w1, [x0, #0xb]
    // 0xba56e0: DecompressPointer r1
    //     0xba56e0: add             x1, x1, HEAP, lsl #32
    // 0xba56e4: cmp             w1, NULL
    // 0xba56e8: b.eq            #0xba5ae4
    // 0xba56ec: LoadField: r0 = r1->field_f
    //     0xba56ec: ldur            w0, [x1, #0xf]
    // 0xba56f0: DecompressPointer r0
    //     0xba56f0: add             x0, x0, HEAP, lsl #32
    // 0xba56f4: cmp             w0, NULL
    // 0xba56f8: b.ne            #0xba5704
    // 0xba56fc: r0 = Null
    //     0xba56fc: mov             x0, NULL
    // 0xba5700: b               #0xba575c
    // 0xba5704: LoadField: r3 = r0->field_13
    //     0xba5704: ldur            w3, [x0, #0x13]
    // 0xba5708: DecompressPointer r3
    //     0xba5708: add             x3, x3, HEAP, lsl #32
    // 0xba570c: cmp             w3, NULL
    // 0xba5710: b.ne            #0xba571c
    // 0xba5714: r0 = Null
    //     0xba5714: mov             x0, NULL
    // 0xba5718: b               #0xba575c
    // 0xba571c: ldr             x4, [fp, #0x10]
    // 0xba5720: LoadField: r0 = r3->field_b
    //     0xba5720: ldur            w0, [x3, #0xb]
    // 0xba5724: r5 = LoadInt32Instr(r4)
    //     0xba5724: sbfx            x5, x4, #1, #0x1f
    //     0xba5728: tbz             w4, #0, #0xba5730
    //     0xba572c: ldur            x5, [x4, #7]
    // 0xba5730: r1 = LoadInt32Instr(r0)
    //     0xba5730: sbfx            x1, x0, #1, #0x1f
    // 0xba5734: mov             x0, x1
    // 0xba5738: mov             x1, x5
    // 0xba573c: cmp             x1, x0
    // 0xba5740: b.hs            #0xba5ae8
    // 0xba5744: LoadField: r0 = r3->field_f
    //     0xba5744: ldur            w0, [x3, #0xf]
    // 0xba5748: DecompressPointer r0
    //     0xba5748: add             x0, x0, HEAP, lsl #32
    // 0xba574c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba574c: add             x16, x0, x5, lsl #2
    //     0xba5750: ldur            w1, [x16, #0xf]
    // 0xba5754: DecompressPointer r1
    //     0xba5754: add             x1, x1, HEAP, lsl #32
    // 0xba5758: mov             x0, x1
    // 0xba575c: stur            x0, [fp, #-0x18]
    // 0xba5760: r0 = BagSingleSelect()
    //     0xba5760: bl              #0xba5190  ; AllocateBagSingleSelectStub -> BagSingleSelect (size=0x14)
    // 0xba5764: mov             x1, x0
    // 0xba5768: ldur            x0, [fp, #-0x20]
    // 0xba576c: StoreField: r1->field_b = r0
    //     0xba576c: stur            w0, [x1, #0xb]
    // 0xba5770: ldur            x0, [fp, #-0x18]
    // 0xba5774: StoreField: r1->field_f = r0
    //     0xba5774: stur            w0, [x1, #0xf]
    // 0xba5778: b               #0xba5ab0
    // 0xba577c: r16 = Instance_CustomisationType
    //     0xba577c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23650] Obj!CustomisationType@d75701
    //     0xba5780: ldr             x16, [x16, #0x650]
    // 0xba5784: cmp             w0, w16
    // 0xba5788: b.ne            #0xba588c
    // 0xba578c: LoadField: r0 = r1->field_f
    //     0xba578c: ldur            w0, [x1, #0xf]
    // 0xba5790: DecompressPointer r0
    //     0xba5790: add             x0, x0, HEAP, lsl #32
    // 0xba5794: LoadField: r2 = r0->field_b
    //     0xba5794: ldur            w2, [x0, #0xb]
    // 0xba5798: DecompressPointer r2
    //     0xba5798: add             x2, x2, HEAP, lsl #32
    // 0xba579c: cmp             w2, NULL
    // 0xba57a0: b.eq            #0xba5aec
    // 0xba57a4: LoadField: r0 = r2->field_b
    //     0xba57a4: ldur            w0, [x2, #0xb]
    // 0xba57a8: DecompressPointer r0
    //     0xba57a8: add             x0, x0, HEAP, lsl #32
    // 0xba57ac: cmp             w0, NULL
    // 0xba57b0: b.ne            #0xba57bc
    // 0xba57b4: r2 = Null
    //     0xba57b4: mov             x2, NULL
    // 0xba57b8: b               #0xba57e0
    // 0xba57bc: r2 = LoadClassIdInstr(r0)
    //     0xba57bc: ldur            x2, [x0, #-1]
    //     0xba57c0: ubfx            x2, x2, #0xc, #0x14
    // 0xba57c4: stp             x4, x0, [SP]
    // 0xba57c8: mov             x0, x2
    // 0xba57cc: r0 = GDT[cid_x0 + -0xb7]()
    //     0xba57cc: sub             lr, x0, #0xb7
    //     0xba57d0: ldr             lr, [x21, lr, lsl #3]
    //     0xba57d4: blr             lr
    // 0xba57d8: mov             x2, x0
    // 0xba57dc: ldur            x1, [fp, #-8]
    // 0xba57e0: stur            x2, [fp, #-0x20]
    // 0xba57e4: LoadField: r0 = r1->field_f
    //     0xba57e4: ldur            w0, [x1, #0xf]
    // 0xba57e8: DecompressPointer r0
    //     0xba57e8: add             x0, x0, HEAP, lsl #32
    // 0xba57ec: LoadField: r1 = r0->field_b
    //     0xba57ec: ldur            w1, [x0, #0xb]
    // 0xba57f0: DecompressPointer r1
    //     0xba57f0: add             x1, x1, HEAP, lsl #32
    // 0xba57f4: cmp             w1, NULL
    // 0xba57f8: b.eq            #0xba5af0
    // 0xba57fc: LoadField: r0 = r1->field_f
    //     0xba57fc: ldur            w0, [x1, #0xf]
    // 0xba5800: DecompressPointer r0
    //     0xba5800: add             x0, x0, HEAP, lsl #32
    // 0xba5804: cmp             w0, NULL
    // 0xba5808: b.ne            #0xba5814
    // 0xba580c: r0 = Null
    //     0xba580c: mov             x0, NULL
    // 0xba5810: b               #0xba586c
    // 0xba5814: LoadField: r3 = r0->field_13
    //     0xba5814: ldur            w3, [x0, #0x13]
    // 0xba5818: DecompressPointer r3
    //     0xba5818: add             x3, x3, HEAP, lsl #32
    // 0xba581c: cmp             w3, NULL
    // 0xba5820: b.ne            #0xba582c
    // 0xba5824: r0 = Null
    //     0xba5824: mov             x0, NULL
    // 0xba5828: b               #0xba586c
    // 0xba582c: ldr             x4, [fp, #0x10]
    // 0xba5830: LoadField: r0 = r3->field_b
    //     0xba5830: ldur            w0, [x3, #0xb]
    // 0xba5834: r5 = LoadInt32Instr(r4)
    //     0xba5834: sbfx            x5, x4, #1, #0x1f
    //     0xba5838: tbz             w4, #0, #0xba5840
    //     0xba583c: ldur            x5, [x4, #7]
    // 0xba5840: r1 = LoadInt32Instr(r0)
    //     0xba5840: sbfx            x1, x0, #1, #0x1f
    // 0xba5844: mov             x0, x1
    // 0xba5848: mov             x1, x5
    // 0xba584c: cmp             x1, x0
    // 0xba5850: b.hs            #0xba5af4
    // 0xba5854: LoadField: r0 = r3->field_f
    //     0xba5854: ldur            w0, [x3, #0xf]
    // 0xba5858: DecompressPointer r0
    //     0xba5858: add             x0, x0, HEAP, lsl #32
    // 0xba585c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba585c: add             x16, x0, x5, lsl #2
    //     0xba5860: ldur            w1, [x16, #0xf]
    // 0xba5864: DecompressPointer r1
    //     0xba5864: add             x1, x1, HEAP, lsl #32
    // 0xba5868: mov             x0, x1
    // 0xba586c: stur            x0, [fp, #-0x18]
    // 0xba5870: r0 = BagImages()
    //     0xba5870: bl              #0xba5184  ; AllocateBagImagesStub -> BagImages (size=0x14)
    // 0xba5874: mov             x1, x0
    // 0xba5878: ldur            x0, [fp, #-0x20]
    // 0xba587c: StoreField: r1->field_b = r0
    //     0xba587c: stur            w0, [x1, #0xb]
    // 0xba5880: ldur            x0, [fp, #-0x18]
    // 0xba5884: StoreField: r1->field_f = r0
    //     0xba5884: stur            w0, [x1, #0xf]
    // 0xba5888: b               #0xba5ab0
    // 0xba588c: r16 = Instance_CustomisationType
    //     0xba588c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23680] Obj!CustomisationType@d756a1
    //     0xba5890: ldr             x16, [x16, #0x680]
    // 0xba5894: cmp             w0, w16
    // 0xba5898: b.ne            #0xba599c
    // 0xba589c: LoadField: r0 = r1->field_f
    //     0xba589c: ldur            w0, [x1, #0xf]
    // 0xba58a0: DecompressPointer r0
    //     0xba58a0: add             x0, x0, HEAP, lsl #32
    // 0xba58a4: LoadField: r2 = r0->field_b
    //     0xba58a4: ldur            w2, [x0, #0xb]
    // 0xba58a8: DecompressPointer r2
    //     0xba58a8: add             x2, x2, HEAP, lsl #32
    // 0xba58ac: cmp             w2, NULL
    // 0xba58b0: b.eq            #0xba5af8
    // 0xba58b4: LoadField: r0 = r2->field_b
    //     0xba58b4: ldur            w0, [x2, #0xb]
    // 0xba58b8: DecompressPointer r0
    //     0xba58b8: add             x0, x0, HEAP, lsl #32
    // 0xba58bc: cmp             w0, NULL
    // 0xba58c0: b.ne            #0xba58cc
    // 0xba58c4: r2 = Null
    //     0xba58c4: mov             x2, NULL
    // 0xba58c8: b               #0xba58f0
    // 0xba58cc: r2 = LoadClassIdInstr(r0)
    //     0xba58cc: ldur            x2, [x0, #-1]
    //     0xba58d0: ubfx            x2, x2, #0xc, #0x14
    // 0xba58d4: stp             x4, x0, [SP]
    // 0xba58d8: mov             x0, x2
    // 0xba58dc: r0 = GDT[cid_x0 + -0xb7]()
    //     0xba58dc: sub             lr, x0, #0xb7
    //     0xba58e0: ldr             lr, [x21, lr, lsl #3]
    //     0xba58e4: blr             lr
    // 0xba58e8: mov             x2, x0
    // 0xba58ec: ldur            x1, [fp, #-8]
    // 0xba58f0: stur            x2, [fp, #-0x20]
    // 0xba58f4: LoadField: r0 = r1->field_f
    //     0xba58f4: ldur            w0, [x1, #0xf]
    // 0xba58f8: DecompressPointer r0
    //     0xba58f8: add             x0, x0, HEAP, lsl #32
    // 0xba58fc: LoadField: r1 = r0->field_b
    //     0xba58fc: ldur            w1, [x0, #0xb]
    // 0xba5900: DecompressPointer r1
    //     0xba5900: add             x1, x1, HEAP, lsl #32
    // 0xba5904: cmp             w1, NULL
    // 0xba5908: b.eq            #0xba5afc
    // 0xba590c: LoadField: r0 = r1->field_f
    //     0xba590c: ldur            w0, [x1, #0xf]
    // 0xba5910: DecompressPointer r0
    //     0xba5910: add             x0, x0, HEAP, lsl #32
    // 0xba5914: cmp             w0, NULL
    // 0xba5918: b.ne            #0xba5924
    // 0xba591c: r0 = Null
    //     0xba591c: mov             x0, NULL
    // 0xba5920: b               #0xba597c
    // 0xba5924: LoadField: r3 = r0->field_13
    //     0xba5924: ldur            w3, [x0, #0x13]
    // 0xba5928: DecompressPointer r3
    //     0xba5928: add             x3, x3, HEAP, lsl #32
    // 0xba592c: cmp             w3, NULL
    // 0xba5930: b.ne            #0xba593c
    // 0xba5934: r0 = Null
    //     0xba5934: mov             x0, NULL
    // 0xba5938: b               #0xba597c
    // 0xba593c: ldr             x4, [fp, #0x10]
    // 0xba5940: LoadField: r0 = r3->field_b
    //     0xba5940: ldur            w0, [x3, #0xb]
    // 0xba5944: r5 = LoadInt32Instr(r4)
    //     0xba5944: sbfx            x5, x4, #1, #0x1f
    //     0xba5948: tbz             w4, #0, #0xba5950
    //     0xba594c: ldur            x5, [x4, #7]
    // 0xba5950: r1 = LoadInt32Instr(r0)
    //     0xba5950: sbfx            x1, x0, #1, #0x1f
    // 0xba5954: mov             x0, x1
    // 0xba5958: mov             x1, x5
    // 0xba595c: cmp             x1, x0
    // 0xba5960: b.hs            #0xba5b00
    // 0xba5964: LoadField: r0 = r3->field_f
    //     0xba5964: ldur            w0, [x3, #0xf]
    // 0xba5968: DecompressPointer r0
    //     0xba5968: add             x0, x0, HEAP, lsl #32
    // 0xba596c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba596c: add             x16, x0, x5, lsl #2
    //     0xba5970: ldur            w1, [x16, #0xf]
    // 0xba5974: DecompressPointer r1
    //     0xba5974: add             x1, x1, HEAP, lsl #32
    // 0xba5978: mov             x0, x1
    // 0xba597c: stur            x0, [fp, #-0x18]
    // 0xba5980: r0 = BagText()
    //     0xba5980: bl              #0xba5178  ; AllocateBagTextStub -> BagText (size=0x14)
    // 0xba5984: mov             x1, x0
    // 0xba5988: ldur            x0, [fp, #-0x20]
    // 0xba598c: StoreField: r1->field_b = r0
    //     0xba598c: stur            w0, [x1, #0xb]
    // 0xba5990: ldur            x0, [fp, #-0x18]
    // 0xba5994: StoreField: r1->field_f = r0
    //     0xba5994: stur            w0, [x1, #0xf]
    // 0xba5998: b               #0xba5ab0
    // 0xba599c: r16 = Instance_CustomisationType
    //     0xba599c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23690] Obj!CustomisationType@d75681
    //     0xba59a0: ldr             x16, [x16, #0x690]
    // 0xba59a4: cmp             w0, w16
    // 0xba59a8: b.ne            #0xba5aac
    // 0xba59ac: LoadField: r0 = r1->field_f
    //     0xba59ac: ldur            w0, [x1, #0xf]
    // 0xba59b0: DecompressPointer r0
    //     0xba59b0: add             x0, x0, HEAP, lsl #32
    // 0xba59b4: LoadField: r2 = r0->field_b
    //     0xba59b4: ldur            w2, [x0, #0xb]
    // 0xba59b8: DecompressPointer r2
    //     0xba59b8: add             x2, x2, HEAP, lsl #32
    // 0xba59bc: cmp             w2, NULL
    // 0xba59c0: b.eq            #0xba5b04
    // 0xba59c4: LoadField: r0 = r2->field_b
    //     0xba59c4: ldur            w0, [x2, #0xb]
    // 0xba59c8: DecompressPointer r0
    //     0xba59c8: add             x0, x0, HEAP, lsl #32
    // 0xba59cc: cmp             w0, NULL
    // 0xba59d0: b.ne            #0xba59e0
    // 0xba59d4: mov             x0, x1
    // 0xba59d8: r2 = Null
    //     0xba59d8: mov             x2, NULL
    // 0xba59dc: b               #0xba5a04
    // 0xba59e0: r2 = LoadClassIdInstr(r0)
    //     0xba59e0: ldur            x2, [x0, #-1]
    //     0xba59e4: ubfx            x2, x2, #0xc, #0x14
    // 0xba59e8: stp             x4, x0, [SP]
    // 0xba59ec: mov             x0, x2
    // 0xba59f0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xba59f0: sub             lr, x0, #0xb7
    //     0xba59f4: ldr             lr, [x21, lr, lsl #3]
    //     0xba59f8: blr             lr
    // 0xba59fc: mov             x2, x0
    // 0xba5a00: ldur            x0, [fp, #-8]
    // 0xba5a04: stur            x2, [fp, #-0x18]
    // 0xba5a08: LoadField: r1 = r0->field_f
    //     0xba5a08: ldur            w1, [x0, #0xf]
    // 0xba5a0c: DecompressPointer r1
    //     0xba5a0c: add             x1, x1, HEAP, lsl #32
    // 0xba5a10: LoadField: r0 = r1->field_b
    //     0xba5a10: ldur            w0, [x1, #0xb]
    // 0xba5a14: DecompressPointer r0
    //     0xba5a14: add             x0, x0, HEAP, lsl #32
    // 0xba5a18: cmp             w0, NULL
    // 0xba5a1c: b.eq            #0xba5b08
    // 0xba5a20: LoadField: r1 = r0->field_f
    //     0xba5a20: ldur            w1, [x0, #0xf]
    // 0xba5a24: DecompressPointer r1
    //     0xba5a24: add             x1, x1, HEAP, lsl #32
    // 0xba5a28: cmp             w1, NULL
    // 0xba5a2c: b.ne            #0xba5a38
    // 0xba5a30: r0 = Null
    //     0xba5a30: mov             x0, NULL
    // 0xba5a34: b               #0xba5a8c
    // 0xba5a38: LoadField: r3 = r1->field_13
    //     0xba5a38: ldur            w3, [x1, #0x13]
    // 0xba5a3c: DecompressPointer r3
    //     0xba5a3c: add             x3, x3, HEAP, lsl #32
    // 0xba5a40: cmp             w3, NULL
    // 0xba5a44: b.ne            #0xba5a50
    // 0xba5a48: r0 = Null
    //     0xba5a48: mov             x0, NULL
    // 0xba5a4c: b               #0xba5a8c
    // 0xba5a50: ldr             x0, [fp, #0x10]
    // 0xba5a54: LoadField: r1 = r3->field_b
    //     0xba5a54: ldur            w1, [x3, #0xb]
    // 0xba5a58: r4 = LoadInt32Instr(r0)
    //     0xba5a58: sbfx            x4, x0, #1, #0x1f
    //     0xba5a5c: tbz             w0, #0, #0xba5a64
    //     0xba5a60: ldur            x4, [x0, #7]
    // 0xba5a64: r0 = LoadInt32Instr(r1)
    //     0xba5a64: sbfx            x0, x1, #1, #0x1f
    // 0xba5a68: mov             x1, x4
    // 0xba5a6c: cmp             x1, x0
    // 0xba5a70: b.hs            #0xba5b0c
    // 0xba5a74: LoadField: r0 = r3->field_f
    //     0xba5a74: ldur            w0, [x3, #0xf]
    // 0xba5a78: DecompressPointer r0
    //     0xba5a78: add             x0, x0, HEAP, lsl #32
    // 0xba5a7c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xba5a7c: add             x16, x0, x4, lsl #2
    //     0xba5a80: ldur            w1, [x16, #0xf]
    // 0xba5a84: DecompressPointer r1
    //     0xba5a84: add             x1, x1, HEAP, lsl #32
    // 0xba5a88: mov             x0, x1
    // 0xba5a8c: stur            x0, [fp, #-8]
    // 0xba5a90: r0 = BagText()
    //     0xba5a90: bl              #0xba5178  ; AllocateBagTextStub -> BagText (size=0x14)
    // 0xba5a94: ldur            x1, [fp, #-0x18]
    // 0xba5a98: StoreField: r0->field_b = r1
    //     0xba5a98: stur            w1, [x0, #0xb]
    // 0xba5a9c: ldur            x1, [fp, #-8]
    // 0xba5aa0: StoreField: r0->field_f = r1
    //     0xba5aa0: stur            w1, [x0, #0xf]
    // 0xba5aa4: mov             x1, x0
    // 0xba5aa8: b               #0xba5ab0
    // 0xba5aac: ldur            x1, [fp, #-0x10]
    // 0xba5ab0: mov             x0, x1
    // 0xba5ab4: LeaveFrame
    //     0xba5ab4: mov             SP, fp
    //     0xba5ab8: ldp             fp, lr, [SP], #0x10
    // 0xba5abc: ret
    //     0xba5abc: ret             
    // 0xba5ac0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba5ac0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba5ac4: b               #0xba51d0
    // 0xba5ac8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5ac8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5acc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5acc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5ad0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5ad0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5ad4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5ad4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5ad8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5ad8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5adc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba5adc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba5ae0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5ae0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5ae4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5ae4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5ae8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba5ae8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba5aec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5aec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5af0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5af0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5af4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba5af4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba5af8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5af8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5afc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5afc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5b00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba5b00: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba5b04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5b04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5b08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5b08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5b0c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba5b0c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4032, size: 0x18, field offset: 0xc
//   const constructor, 
class CustomisedStrip extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7ffb8, size: 0x24
    // 0xc7ffb8: EnterFrame
    //     0xc7ffb8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ffbc: mov             fp, SP
    // 0xc7ffc0: mov             x0, x1
    // 0xc7ffc4: r1 = <CustomisedStrip>
    //     0xc7ffc4: add             x1, PP, #0x48, lsl #12  ; [pp+0x486e8] TypeArguments: <CustomisedStrip>
    //     0xc7ffc8: ldr             x1, [x1, #0x6e8]
    // 0xc7ffcc: r0 = _CustomisedStripState()
    //     0xc7ffcc: bl              #0xc7ffdc  ; Allocate_CustomisedStripStateStub -> _CustomisedStripState (size=0x14)
    // 0xc7ffd0: LeaveFrame
    //     0xc7ffd0: mov             SP, fp
    //     0xc7ffd4: ldp             fp, lr, [SP], #0x10
    // 0xc7ffd8: ret
    //     0xc7ffd8: ret             
  }
}
