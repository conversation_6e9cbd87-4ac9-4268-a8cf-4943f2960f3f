// lib: , url: package:customer_app/app/presentation/views/cosmetic/home/<USER>/collection_poster_carousel.dart

// class id: 1049278, size: 0x8
class :: {
}

// class id: 3428, size: 0x20, field offset: 0x14
class _CollectionPosterCarouselState extends State<dynamic> {

  late PageController _pageCosmeticController; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x93a578, size: 0x80
    // 0x93a578: EnterFrame
    //     0x93a578: stp             fp, lr, [SP, #-0x10]!
    //     0x93a57c: mov             fp, SP
    // 0x93a580: AllocStack(0x10)
    //     0x93a580: sub             SP, SP, #0x10
    // 0x93a584: SetupParameters(_CollectionPosterCarouselState this /* r1 => r1, fp-0x8 */)
    //     0x93a584: stur            x1, [fp, #-8]
    // 0x93a588: CheckStackOverflow
    //     0x93a588: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93a58c: cmp             SP, x16
    //     0x93a590: b.ls            #0x93a5f0
    // 0x93a594: r0 = PageController()
    //     0x93a594: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x93a598: stur            x0, [fp, #-0x10]
    // 0x93a59c: StoreField: r0->field_3f = rZR
    //     0x93a59c: stur            xzr, [x0, #0x3f]
    // 0x93a5a0: r1 = true
    //     0x93a5a0: add             x1, NULL, #0x20  ; true
    // 0x93a5a4: StoreField: r0->field_47 = r1
    //     0x93a5a4: stur            w1, [x0, #0x47]
    // 0x93a5a8: d0 = 0.500000
    //     0x93a5a8: fmov            d0, #0.50000000
    // 0x93a5ac: StoreField: r0->field_4b = d0
    //     0x93a5ac: stur            d0, [x0, #0x4b]
    // 0x93a5b0: mov             x1, x0
    // 0x93a5b4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x93a5b4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x93a5b8: r0 = ScrollController()
    //     0x93a5b8: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x93a5bc: ldur            x0, [fp, #-0x10]
    // 0x93a5c0: ldur            x1, [fp, #-8]
    // 0x93a5c4: StoreField: r1->field_13 = r0
    //     0x93a5c4: stur            w0, [x1, #0x13]
    //     0x93a5c8: ldurb           w16, [x1, #-1]
    //     0x93a5cc: ldurb           w17, [x0, #-1]
    //     0x93a5d0: and             x16, x17, x16, lsr #2
    //     0x93a5d4: tst             x16, HEAP, lsr #32
    //     0x93a5d8: b.eq            #0x93a5e0
    //     0x93a5dc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x93a5e0: r0 = Null
    //     0x93a5e0: mov             x0, NULL
    // 0x93a5e4: LeaveFrame
    //     0x93a5e4: mov             SP, fp
    //     0x93a5e8: ldp             fp, lr, [SP], #0x10
    // 0x93a5ec: ret
    //     0x93a5ec: ret             
    // 0x93a5f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93a5f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93a5f4: b               #0x93a594
  }
  _ build(/* No info */) {
    // ** addr: 0xae4c08, size: 0x6d0
    // 0xae4c08: EnterFrame
    //     0xae4c08: stp             fp, lr, [SP, #-0x10]!
    //     0xae4c0c: mov             fp, SP
    // 0xae4c10: AllocStack(0x70)
    //     0xae4c10: sub             SP, SP, #0x70
    // 0xae4c14: SetupParameters(_CollectionPosterCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xae4c14: mov             x0, x1
    //     0xae4c18: stur            x1, [fp, #-8]
    //     0xae4c1c: mov             x1, x2
    //     0xae4c20: stur            x2, [fp, #-0x10]
    // 0xae4c24: CheckStackOverflow
    //     0xae4c24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4c28: cmp             SP, x16
    //     0xae4c2c: b.ls            #0xae52ac
    // 0xae4c30: r1 = 1
    //     0xae4c30: movz            x1, #0x1
    // 0xae4c34: r0 = AllocateContext()
    //     0xae4c34: bl              #0x16f6108  ; AllocateContextStub
    // 0xae4c38: mov             x3, x0
    // 0xae4c3c: ldur            x0, [fp, #-8]
    // 0xae4c40: stur            x3, [fp, #-0x20]
    // 0xae4c44: StoreField: r3->field_f = r0
    //     0xae4c44: stur            w0, [x3, #0xf]
    // 0xae4c48: LoadField: r1 = r0->field_b
    //     0xae4c48: ldur            w1, [x0, #0xb]
    // 0xae4c4c: DecompressPointer r1
    //     0xae4c4c: add             x1, x1, HEAP, lsl #32
    // 0xae4c50: cmp             w1, NULL
    // 0xae4c54: b.eq            #0xae52b4
    // 0xae4c58: LoadField: r2 = r1->field_13
    //     0xae4c58: ldur            w2, [x1, #0x13]
    // 0xae4c5c: DecompressPointer r2
    //     0xae4c5c: add             x2, x2, HEAP, lsl #32
    // 0xae4c60: LoadField: r1 = r2->field_7
    //     0xae4c60: ldur            w1, [x2, #7]
    // 0xae4c64: DecompressPointer r1
    //     0xae4c64: add             x1, x1, HEAP, lsl #32
    // 0xae4c68: cmp             w1, NULL
    // 0xae4c6c: b.ne            #0xae4c78
    // 0xae4c70: r1 = Instance_TitleAlignment
    //     0xae4c70: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xae4c74: ldr             x1, [x1, #0x518]
    // 0xae4c78: r16 = Instance_TitleAlignment
    //     0xae4c78: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xae4c7c: ldr             x16, [x16, #0x520]
    // 0xae4c80: cmp             w1, w16
    // 0xae4c84: b.ne            #0xae4c94
    // 0xae4c88: r4 = Instance_CrossAxisAlignment
    //     0xae4c88: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xae4c8c: ldr             x4, [x4, #0xc68]
    // 0xae4c90: b               #0xae4cb8
    // 0xae4c94: r16 = Instance_TitleAlignment
    //     0xae4c94: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xae4c98: ldr             x16, [x16, #0x518]
    // 0xae4c9c: cmp             w1, w16
    // 0xae4ca0: b.ne            #0xae4cb0
    // 0xae4ca4: r4 = Instance_CrossAxisAlignment
    //     0xae4ca4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xae4ca8: ldr             x4, [x4, #0x890]
    // 0xae4cac: b               #0xae4cb8
    // 0xae4cb0: r4 = Instance_CrossAxisAlignment
    //     0xae4cb0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xae4cb4: ldr             x4, [x4, #0xa18]
    // 0xae4cb8: stur            x4, [fp, #-0x18]
    // 0xae4cbc: r1 = <Widget>
    //     0xae4cbc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae4cc0: r2 = 0
    //     0xae4cc0: movz            x2, #0
    // 0xae4cc4: r0 = _GrowableList()
    //     0xae4cc4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xae4cc8: mov             x2, x0
    // 0xae4ccc: ldur            x1, [fp, #-8]
    // 0xae4cd0: stur            x2, [fp, #-0x28]
    // 0xae4cd4: LoadField: r0 = r1->field_b
    //     0xae4cd4: ldur            w0, [x1, #0xb]
    // 0xae4cd8: DecompressPointer r0
    //     0xae4cd8: add             x0, x0, HEAP, lsl #32
    // 0xae4cdc: cmp             w0, NULL
    // 0xae4ce0: b.eq            #0xae52b8
    // 0xae4ce4: LoadField: r3 = r0->field_f
    //     0xae4ce4: ldur            w3, [x0, #0xf]
    // 0xae4ce8: DecompressPointer r3
    //     0xae4ce8: add             x3, x3, HEAP, lsl #32
    // 0xae4cec: LoadField: r0 = r3->field_7
    //     0xae4cec: ldur            w0, [x3, #7]
    // 0xae4cf0: cbz             w0, #0xae4e80
    // 0xae4cf4: r0 = LoadClassIdInstr(r3)
    //     0xae4cf4: ldur            x0, [x3, #-1]
    //     0xae4cf8: ubfx            x0, x0, #0xc, #0x14
    // 0xae4cfc: str             x3, [SP]
    // 0xae4d00: r0 = GDT[cid_x0 + -0x1000]()
    //     0xae4d00: sub             lr, x0, #1, lsl #12
    //     0xae4d04: ldr             lr, [x21, lr, lsl #3]
    //     0xae4d08: blr             lr
    // 0xae4d0c: mov             x2, x0
    // 0xae4d10: ldur            x0, [fp, #-8]
    // 0xae4d14: stur            x2, [fp, #-0x38]
    // 0xae4d18: LoadField: r1 = r0->field_b
    //     0xae4d18: ldur            w1, [x0, #0xb]
    // 0xae4d1c: DecompressPointer r1
    //     0xae4d1c: add             x1, x1, HEAP, lsl #32
    // 0xae4d20: cmp             w1, NULL
    // 0xae4d24: b.eq            #0xae52bc
    // 0xae4d28: LoadField: r3 = r1->field_13
    //     0xae4d28: ldur            w3, [x1, #0x13]
    // 0xae4d2c: DecompressPointer r3
    //     0xae4d2c: add             x3, x3, HEAP, lsl #32
    // 0xae4d30: LoadField: r1 = r3->field_7
    //     0xae4d30: ldur            w1, [x3, #7]
    // 0xae4d34: DecompressPointer r1
    //     0xae4d34: add             x1, x1, HEAP, lsl #32
    // 0xae4d38: cmp             w1, NULL
    // 0xae4d3c: b.ne            #0xae4d48
    // 0xae4d40: r1 = Instance_TitleAlignment
    //     0xae4d40: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xae4d44: ldr             x1, [x1, #0x518]
    // 0xae4d48: r16 = Instance_TitleAlignment
    //     0xae4d48: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xae4d4c: ldr             x16, [x16, #0x520]
    // 0xae4d50: cmp             w1, w16
    // 0xae4d54: b.ne            #0xae4d60
    // 0xae4d58: r4 = Instance_TextAlign
    //     0xae4d58: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xae4d5c: b               #0xae4d7c
    // 0xae4d60: r16 = Instance_TitleAlignment
    //     0xae4d60: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xae4d64: ldr             x16, [x16, #0x518]
    // 0xae4d68: cmp             w1, w16
    // 0xae4d6c: b.ne            #0xae4d78
    // 0xae4d70: r4 = Instance_TextAlign
    //     0xae4d70: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xae4d74: b               #0xae4d7c
    // 0xae4d78: r4 = Instance_TextAlign
    //     0xae4d78: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xae4d7c: ldur            x3, [fp, #-0x28]
    // 0xae4d80: ldur            x1, [fp, #-0x10]
    // 0xae4d84: stur            x4, [fp, #-0x30]
    // 0xae4d88: r0 = of()
    //     0xae4d88: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae4d8c: LoadField: r1 = r0->field_87
    //     0xae4d8c: ldur            w1, [x0, #0x87]
    // 0xae4d90: DecompressPointer r1
    //     0xae4d90: add             x1, x1, HEAP, lsl #32
    // 0xae4d94: LoadField: r0 = r1->field_7
    //     0xae4d94: ldur            w0, [x1, #7]
    // 0xae4d98: DecompressPointer r0
    //     0xae4d98: add             x0, x0, HEAP, lsl #32
    // 0xae4d9c: r16 = 32.000000
    //     0xae4d9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xae4da0: ldr             x16, [x16, #0x848]
    // 0xae4da4: r30 = Instance_Color
    //     0xae4da4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xae4da8: stp             lr, x16, [SP]
    // 0xae4dac: mov             x1, x0
    // 0xae4db0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xae4db0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xae4db4: ldr             x4, [x4, #0xaa0]
    // 0xae4db8: r0 = copyWith()
    //     0xae4db8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae4dbc: stur            x0, [fp, #-0x40]
    // 0xae4dc0: r0 = Text()
    //     0xae4dc0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xae4dc4: mov             x1, x0
    // 0xae4dc8: ldur            x0, [fp, #-0x38]
    // 0xae4dcc: stur            x1, [fp, #-0x48]
    // 0xae4dd0: StoreField: r1->field_b = r0
    //     0xae4dd0: stur            w0, [x1, #0xb]
    // 0xae4dd4: ldur            x0, [fp, #-0x40]
    // 0xae4dd8: StoreField: r1->field_13 = r0
    //     0xae4dd8: stur            w0, [x1, #0x13]
    // 0xae4ddc: ldur            x0, [fp, #-0x30]
    // 0xae4de0: StoreField: r1->field_1b = r0
    //     0xae4de0: stur            w0, [x1, #0x1b]
    // 0xae4de4: r0 = Padding()
    //     0xae4de4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae4de8: mov             x2, x0
    // 0xae4dec: r0 = Instance_EdgeInsets
    //     0xae4dec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xae4df0: ldr             x0, [x0, #0x668]
    // 0xae4df4: stur            x2, [fp, #-0x30]
    // 0xae4df8: StoreField: r2->field_f = r0
    //     0xae4df8: stur            w0, [x2, #0xf]
    // 0xae4dfc: ldur            x0, [fp, #-0x48]
    // 0xae4e00: StoreField: r2->field_b = r0
    //     0xae4e00: stur            w0, [x2, #0xb]
    // 0xae4e04: ldur            x0, [fp, #-0x28]
    // 0xae4e08: LoadField: r1 = r0->field_b
    //     0xae4e08: ldur            w1, [x0, #0xb]
    // 0xae4e0c: LoadField: r3 = r0->field_f
    //     0xae4e0c: ldur            w3, [x0, #0xf]
    // 0xae4e10: DecompressPointer r3
    //     0xae4e10: add             x3, x3, HEAP, lsl #32
    // 0xae4e14: LoadField: r4 = r3->field_b
    //     0xae4e14: ldur            w4, [x3, #0xb]
    // 0xae4e18: r3 = LoadInt32Instr(r1)
    //     0xae4e18: sbfx            x3, x1, #1, #0x1f
    // 0xae4e1c: stur            x3, [fp, #-0x50]
    // 0xae4e20: r1 = LoadInt32Instr(r4)
    //     0xae4e20: sbfx            x1, x4, #1, #0x1f
    // 0xae4e24: cmp             x3, x1
    // 0xae4e28: b.ne            #0xae4e34
    // 0xae4e2c: mov             x1, x0
    // 0xae4e30: r0 = _growToNextCapacity()
    //     0xae4e30: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae4e34: ldur            x3, [fp, #-0x28]
    // 0xae4e38: ldur            x2, [fp, #-0x50]
    // 0xae4e3c: add             x0, x2, #1
    // 0xae4e40: lsl             x1, x0, #1
    // 0xae4e44: StoreField: r3->field_b = r1
    //     0xae4e44: stur            w1, [x3, #0xb]
    // 0xae4e48: LoadField: r1 = r3->field_f
    //     0xae4e48: ldur            w1, [x3, #0xf]
    // 0xae4e4c: DecompressPointer r1
    //     0xae4e4c: add             x1, x1, HEAP, lsl #32
    // 0xae4e50: ldur            x0, [fp, #-0x30]
    // 0xae4e54: ArrayStore: r1[r2] = r0  ; List_4
    //     0xae4e54: add             x25, x1, x2, lsl #2
    //     0xae4e58: add             x25, x25, #0xf
    //     0xae4e5c: str             w0, [x25]
    //     0xae4e60: tbz             w0, #0, #0xae4e7c
    //     0xae4e64: ldurb           w16, [x1, #-1]
    //     0xae4e68: ldurb           w17, [x0, #-1]
    //     0xae4e6c: and             x16, x17, x16, lsr #2
    //     0xae4e70: tst             x16, HEAP, lsr #32
    //     0xae4e74: b.eq            #0xae4e7c
    //     0xae4e78: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae4e7c: b               #0xae4e84
    // 0xae4e80: mov             x3, x2
    // 0xae4e84: ldur            x0, [fp, #-8]
    // 0xae4e88: LoadField: r1 = r0->field_b
    //     0xae4e88: ldur            w1, [x0, #0xb]
    // 0xae4e8c: DecompressPointer r1
    //     0xae4e8c: add             x1, x1, HEAP, lsl #32
    // 0xae4e90: cmp             w1, NULL
    // 0xae4e94: b.eq            #0xae52c0
    // 0xae4e98: LoadField: r2 = r1->field_b
    //     0xae4e98: ldur            w2, [x1, #0xb]
    // 0xae4e9c: DecompressPointer r2
    //     0xae4e9c: add             x2, x2, HEAP, lsl #32
    // 0xae4ea0: cmp             w2, NULL
    // 0xae4ea4: b.ne            #0xae4eb0
    // 0xae4ea8: r4 = Null
    //     0xae4ea8: mov             x4, NULL
    // 0xae4eac: b               #0xae4eb8
    // 0xae4eb0: LoadField: r1 = r2->field_b
    //     0xae4eb0: ldur            w1, [x2, #0xb]
    // 0xae4eb4: mov             x4, x1
    // 0xae4eb8: stur            x4, [fp, #-0x38]
    // 0xae4ebc: LoadField: r5 = r0->field_13
    //     0xae4ebc: ldur            w5, [x0, #0x13]
    // 0xae4ec0: DecompressPointer r5
    //     0xae4ec0: add             x5, x5, HEAP, lsl #32
    // 0xae4ec4: r16 = Sentinel
    //     0xae4ec4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae4ec8: cmp             w5, w16
    // 0xae4ecc: b.eq            #0xae52c4
    // 0xae4ed0: ldur            x2, [fp, #-0x20]
    // 0xae4ed4: stur            x5, [fp, #-0x30]
    // 0xae4ed8: r1 = Function '<anonymous closure>':.
    //     0xae4ed8: add             x1, PP, #0x58, lsl #12  ; [pp+0x582a8] AnonymousClosure: (0xae5b00), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/collection_poster_carousel.dart] _CollectionPosterCarouselState::build (0xae4c08)
    //     0xae4edc: ldr             x1, [x1, #0x2a8]
    // 0xae4ee0: r0 = AllocateClosure()
    //     0xae4ee0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae4ee4: ldur            x2, [fp, #-0x20]
    // 0xae4ee8: r1 = Function '<anonymous closure>':.
    //     0xae4ee8: add             x1, PP, #0x58, lsl #12  ; [pp+0x582b0] AnonymousClosure: (0xae52d8), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/collection_poster_carousel.dart] _CollectionPosterCarouselState::build (0xae4c08)
    //     0xae4eec: ldr             x1, [x1, #0x2b0]
    // 0xae4ef0: stur            x0, [fp, #-0x20]
    // 0xae4ef4: r0 = AllocateClosure()
    //     0xae4ef4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae4ef8: stur            x0, [fp, #-0x40]
    // 0xae4efc: r0 = PageView()
    //     0xae4efc: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xae4f00: stur            x0, [fp, #-0x48]
    // 0xae4f04: r16 = Instance_BouncingScrollPhysics
    //     0xae4f04: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xae4f08: ldr             x16, [x16, #0x890]
    // 0xae4f0c: r30 = false
    //     0xae4f0c: add             lr, NULL, #0x30  ; false
    // 0xae4f10: stp             lr, x16, [SP, #8]
    // 0xae4f14: ldur            x16, [fp, #-0x30]
    // 0xae4f18: str             x16, [SP]
    // 0xae4f1c: mov             x1, x0
    // 0xae4f20: ldur            x2, [fp, #-0x40]
    // 0xae4f24: ldur            x3, [fp, #-0x38]
    // 0xae4f28: ldur            x5, [fp, #-0x20]
    // 0xae4f2c: r4 = const [0, 0x7, 0x3, 0x4, controller, 0x6, padEnds, 0x5, physics, 0x4, null]
    //     0xae4f2c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d20] List(11) [0, 0x7, 0x3, 0x4, "controller", 0x6, "padEnds", 0x5, "physics", 0x4, Null]
    //     0xae4f30: ldr             x4, [x4, #0xd20]
    // 0xae4f34: r0 = PageView.builder()
    //     0xae4f34: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xae4f38: r0 = Container()
    //     0xae4f38: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xae4f3c: stur            x0, [fp, #-0x20]
    // 0xae4f40: r16 = 245.000000
    //     0xae4f40: add             x16, PP, #0x58, lsl #12  ; [pp+0x582b8] 245
    //     0xae4f44: ldr             x16, [x16, #0x2b8]
    // 0xae4f48: r30 = Instance_EdgeInsets
    //     0xae4f48: add             lr, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xae4f4c: ldr             lr, [lr, #0xf30]
    // 0xae4f50: stp             lr, x16, [SP, #8]
    // 0xae4f54: ldur            x16, [fp, #-0x48]
    // 0xae4f58: str             x16, [SP]
    // 0xae4f5c: mov             x1, x0
    // 0xae4f60: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, height, 0x1, padding, 0x2, null]
    //     0xae4f60: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3dcd8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "height", 0x1, "padding", 0x2, Null]
    //     0xae4f64: ldr             x4, [x4, #0xcd8]
    // 0xae4f68: r0 = Container()
    //     0xae4f68: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae4f6c: ldur            x0, [fp, #-0x28]
    // 0xae4f70: LoadField: r1 = r0->field_b
    //     0xae4f70: ldur            w1, [x0, #0xb]
    // 0xae4f74: LoadField: r2 = r0->field_f
    //     0xae4f74: ldur            w2, [x0, #0xf]
    // 0xae4f78: DecompressPointer r2
    //     0xae4f78: add             x2, x2, HEAP, lsl #32
    // 0xae4f7c: LoadField: r3 = r2->field_b
    //     0xae4f7c: ldur            w3, [x2, #0xb]
    // 0xae4f80: r2 = LoadInt32Instr(r1)
    //     0xae4f80: sbfx            x2, x1, #1, #0x1f
    // 0xae4f84: stur            x2, [fp, #-0x50]
    // 0xae4f88: r1 = LoadInt32Instr(r3)
    //     0xae4f88: sbfx            x1, x3, #1, #0x1f
    // 0xae4f8c: cmp             x2, x1
    // 0xae4f90: b.ne            #0xae4f9c
    // 0xae4f94: mov             x1, x0
    // 0xae4f98: r0 = _growToNextCapacity()
    //     0xae4f98: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae4f9c: ldur            x4, [fp, #-8]
    // 0xae4fa0: ldur            x2, [fp, #-0x28]
    // 0xae4fa4: ldur            x3, [fp, #-0x50]
    // 0xae4fa8: add             x0, x3, #1
    // 0xae4fac: lsl             x1, x0, #1
    // 0xae4fb0: StoreField: r2->field_b = r1
    //     0xae4fb0: stur            w1, [x2, #0xb]
    // 0xae4fb4: LoadField: r1 = r2->field_f
    //     0xae4fb4: ldur            w1, [x2, #0xf]
    // 0xae4fb8: DecompressPointer r1
    //     0xae4fb8: add             x1, x1, HEAP, lsl #32
    // 0xae4fbc: ldur            x0, [fp, #-0x20]
    // 0xae4fc0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae4fc0: add             x25, x1, x3, lsl #2
    //     0xae4fc4: add             x25, x25, #0xf
    //     0xae4fc8: str             w0, [x25]
    //     0xae4fcc: tbz             w0, #0, #0xae4fe8
    //     0xae4fd0: ldurb           w16, [x1, #-1]
    //     0xae4fd4: ldurb           w17, [x0, #-1]
    //     0xae4fd8: and             x16, x17, x16, lsr #2
    //     0xae4fdc: tst             x16, HEAP, lsr #32
    //     0xae4fe0: b.eq            #0xae4fe8
    //     0xae4fe4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae4fe8: LoadField: r0 = r4->field_b
    //     0xae4fe8: ldur            w0, [x4, #0xb]
    // 0xae4fec: DecompressPointer r0
    //     0xae4fec: add             x0, x0, HEAP, lsl #32
    // 0xae4ff0: cmp             w0, NULL
    // 0xae4ff4: b.eq            #0xae52d0
    // 0xae4ff8: LoadField: r1 = r0->field_b
    //     0xae4ff8: ldur            w1, [x0, #0xb]
    // 0xae4ffc: DecompressPointer r1
    //     0xae4ffc: add             x1, x1, HEAP, lsl #32
    // 0xae5000: cmp             w1, NULL
    // 0xae5004: b.eq            #0xae52d4
    // 0xae5008: LoadField: r0 = r1->field_b
    //     0xae5008: ldur            w0, [x1, #0xb]
    // 0xae500c: r1 = LoadInt32Instr(r0)
    //     0xae500c: sbfx            x1, x0, #1, #0x1f
    // 0xae5010: cmp             x1, #2
    // 0xae5014: b.le            #0xae51a0
    // 0xae5018: r3 = LoadInt32Instr(r0)
    //     0xae5018: sbfx            x3, x0, #1, #0x1f
    // 0xae501c: stur            x3, [fp, #-0x58]
    // 0xae5020: ArrayLoad: r0 = r4[0]  ; List_8
    //     0xae5020: ldur            x0, [x4, #0x17]
    // 0xae5024: ldur            x1, [fp, #-0x10]
    // 0xae5028: stur            x0, [fp, #-0x50]
    // 0xae502c: r0 = of()
    //     0xae502c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae5030: LoadField: r1 = r0->field_5b
    //     0xae5030: ldur            w1, [x0, #0x5b]
    // 0xae5034: DecompressPointer r1
    //     0xae5034: add             x1, x1, HEAP, lsl #32
    // 0xae5038: stur            x1, [fp, #-8]
    // 0xae503c: r0 = CarouselIndicator()
    //     0xae503c: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xae5040: mov             x3, x0
    // 0xae5044: ldur            x0, [fp, #-0x58]
    // 0xae5048: stur            x3, [fp, #-0x10]
    // 0xae504c: StoreField: r3->field_b = r0
    //     0xae504c: stur            x0, [x3, #0xb]
    // 0xae5050: ldur            x0, [fp, #-0x50]
    // 0xae5054: StoreField: r3->field_13 = r0
    //     0xae5054: stur            x0, [x3, #0x13]
    // 0xae5058: ldur            x0, [fp, #-8]
    // 0xae505c: StoreField: r3->field_1b = r0
    //     0xae505c: stur            w0, [x3, #0x1b]
    // 0xae5060: r0 = Instance_Color
    //     0xae5060: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xae5064: ldr             x0, [x0, #0x90]
    // 0xae5068: StoreField: r3->field_1f = r0
    //     0xae5068: stur            w0, [x3, #0x1f]
    // 0xae506c: r1 = Null
    //     0xae506c: mov             x1, NULL
    // 0xae5070: r2 = 2
    //     0xae5070: movz            x2, #0x2
    // 0xae5074: r0 = AllocateArray()
    //     0xae5074: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae5078: mov             x2, x0
    // 0xae507c: ldur            x0, [fp, #-0x10]
    // 0xae5080: stur            x2, [fp, #-8]
    // 0xae5084: StoreField: r2->field_f = r0
    //     0xae5084: stur            w0, [x2, #0xf]
    // 0xae5088: r1 = <Widget>
    //     0xae5088: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae508c: r0 = AllocateGrowableArray()
    //     0xae508c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xae5090: mov             x1, x0
    // 0xae5094: ldur            x0, [fp, #-8]
    // 0xae5098: stur            x1, [fp, #-0x10]
    // 0xae509c: StoreField: r1->field_f = r0
    //     0xae509c: stur            w0, [x1, #0xf]
    // 0xae50a0: r0 = 2
    //     0xae50a0: movz            x0, #0x2
    // 0xae50a4: StoreField: r1->field_b = r0
    //     0xae50a4: stur            w0, [x1, #0xb]
    // 0xae50a8: r0 = Row()
    //     0xae50a8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xae50ac: mov             x1, x0
    // 0xae50b0: r0 = Instance_Axis
    //     0xae50b0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xae50b4: stur            x1, [fp, #-8]
    // 0xae50b8: StoreField: r1->field_f = r0
    //     0xae50b8: stur            w0, [x1, #0xf]
    // 0xae50bc: r0 = Instance_MainAxisAlignment
    //     0xae50bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xae50c0: ldr             x0, [x0, #0xab0]
    // 0xae50c4: StoreField: r1->field_13 = r0
    //     0xae50c4: stur            w0, [x1, #0x13]
    // 0xae50c8: r0 = Instance_MainAxisSize
    //     0xae50c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xae50cc: ldr             x0, [x0, #0xa10]
    // 0xae50d0: ArrayStore: r1[0] = r0  ; List_4
    //     0xae50d0: stur            w0, [x1, #0x17]
    // 0xae50d4: r0 = Instance_CrossAxisAlignment
    //     0xae50d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xae50d8: ldr             x0, [x0, #0xa18]
    // 0xae50dc: StoreField: r1->field_1b = r0
    //     0xae50dc: stur            w0, [x1, #0x1b]
    // 0xae50e0: r0 = Instance_VerticalDirection
    //     0xae50e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xae50e4: ldr             x0, [x0, #0xa20]
    // 0xae50e8: StoreField: r1->field_23 = r0
    //     0xae50e8: stur            w0, [x1, #0x23]
    // 0xae50ec: r2 = Instance_Clip
    //     0xae50ec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xae50f0: ldr             x2, [x2, #0x38]
    // 0xae50f4: StoreField: r1->field_2b = r2
    //     0xae50f4: stur            w2, [x1, #0x2b]
    // 0xae50f8: StoreField: r1->field_2f = rZR
    //     0xae50f8: stur            xzr, [x1, #0x2f]
    // 0xae50fc: ldur            x3, [fp, #-0x10]
    // 0xae5100: StoreField: r1->field_b = r3
    //     0xae5100: stur            w3, [x1, #0xb]
    // 0xae5104: r0 = Padding()
    //     0xae5104: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae5108: mov             x2, x0
    // 0xae510c: r0 = Instance_EdgeInsets
    //     0xae510c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xae5110: ldr             x0, [x0, #0xa00]
    // 0xae5114: stur            x2, [fp, #-0x10]
    // 0xae5118: StoreField: r2->field_f = r0
    //     0xae5118: stur            w0, [x2, #0xf]
    // 0xae511c: ldur            x0, [fp, #-8]
    // 0xae5120: StoreField: r2->field_b = r0
    //     0xae5120: stur            w0, [x2, #0xb]
    // 0xae5124: ldur            x0, [fp, #-0x28]
    // 0xae5128: LoadField: r1 = r0->field_b
    //     0xae5128: ldur            w1, [x0, #0xb]
    // 0xae512c: LoadField: r3 = r0->field_f
    //     0xae512c: ldur            w3, [x0, #0xf]
    // 0xae5130: DecompressPointer r3
    //     0xae5130: add             x3, x3, HEAP, lsl #32
    // 0xae5134: LoadField: r4 = r3->field_b
    //     0xae5134: ldur            w4, [x3, #0xb]
    // 0xae5138: r3 = LoadInt32Instr(r1)
    //     0xae5138: sbfx            x3, x1, #1, #0x1f
    // 0xae513c: stur            x3, [fp, #-0x50]
    // 0xae5140: r1 = LoadInt32Instr(r4)
    //     0xae5140: sbfx            x1, x4, #1, #0x1f
    // 0xae5144: cmp             x3, x1
    // 0xae5148: b.ne            #0xae5154
    // 0xae514c: mov             x1, x0
    // 0xae5150: r0 = _growToNextCapacity()
    //     0xae5150: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae5154: ldur            x2, [fp, #-0x28]
    // 0xae5158: ldur            x3, [fp, #-0x50]
    // 0xae515c: add             x0, x3, #1
    // 0xae5160: lsl             x1, x0, #1
    // 0xae5164: StoreField: r2->field_b = r1
    //     0xae5164: stur            w1, [x2, #0xb]
    // 0xae5168: LoadField: r1 = r2->field_f
    //     0xae5168: ldur            w1, [x2, #0xf]
    // 0xae516c: DecompressPointer r1
    //     0xae516c: add             x1, x1, HEAP, lsl #32
    // 0xae5170: ldur            x0, [fp, #-0x10]
    // 0xae5174: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae5174: add             x25, x1, x3, lsl #2
    //     0xae5178: add             x25, x25, #0xf
    //     0xae517c: str             w0, [x25]
    //     0xae5180: tbz             w0, #0, #0xae519c
    //     0xae5184: ldurb           w16, [x1, #-1]
    //     0xae5188: ldurb           w17, [x0, #-1]
    //     0xae518c: and             x16, x17, x16, lsr #2
    //     0xae5190: tst             x16, HEAP, lsr #32
    //     0xae5194: b.eq            #0xae519c
    //     0xae5198: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae519c: b               #0xae522c
    // 0xae51a0: r0 = Container()
    //     0xae51a0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xae51a4: mov             x1, x0
    // 0xae51a8: stur            x0, [fp, #-8]
    // 0xae51ac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae51ac: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae51b0: r0 = Container()
    //     0xae51b0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae51b4: ldur            x0, [fp, #-0x28]
    // 0xae51b8: LoadField: r1 = r0->field_b
    //     0xae51b8: ldur            w1, [x0, #0xb]
    // 0xae51bc: LoadField: r2 = r0->field_f
    //     0xae51bc: ldur            w2, [x0, #0xf]
    // 0xae51c0: DecompressPointer r2
    //     0xae51c0: add             x2, x2, HEAP, lsl #32
    // 0xae51c4: LoadField: r3 = r2->field_b
    //     0xae51c4: ldur            w3, [x2, #0xb]
    // 0xae51c8: r2 = LoadInt32Instr(r1)
    //     0xae51c8: sbfx            x2, x1, #1, #0x1f
    // 0xae51cc: stur            x2, [fp, #-0x50]
    // 0xae51d0: r1 = LoadInt32Instr(r3)
    //     0xae51d0: sbfx            x1, x3, #1, #0x1f
    // 0xae51d4: cmp             x2, x1
    // 0xae51d8: b.ne            #0xae51e4
    // 0xae51dc: mov             x1, x0
    // 0xae51e0: r0 = _growToNextCapacity()
    //     0xae51e0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae51e4: ldur            x2, [fp, #-0x28]
    // 0xae51e8: ldur            x3, [fp, #-0x50]
    // 0xae51ec: add             x0, x3, #1
    // 0xae51f0: lsl             x1, x0, #1
    // 0xae51f4: StoreField: r2->field_b = r1
    //     0xae51f4: stur            w1, [x2, #0xb]
    // 0xae51f8: LoadField: r1 = r2->field_f
    //     0xae51f8: ldur            w1, [x2, #0xf]
    // 0xae51fc: DecompressPointer r1
    //     0xae51fc: add             x1, x1, HEAP, lsl #32
    // 0xae5200: ldur            x0, [fp, #-8]
    // 0xae5204: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae5204: add             x25, x1, x3, lsl #2
    //     0xae5208: add             x25, x25, #0xf
    //     0xae520c: str             w0, [x25]
    //     0xae5210: tbz             w0, #0, #0xae522c
    //     0xae5214: ldurb           w16, [x1, #-1]
    //     0xae5218: ldurb           w17, [x0, #-1]
    //     0xae521c: and             x16, x17, x16, lsr #2
    //     0xae5220: tst             x16, HEAP, lsr #32
    //     0xae5224: b.eq            #0xae522c
    //     0xae5228: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae522c: ldur            x0, [fp, #-0x18]
    // 0xae5230: r0 = Column()
    //     0xae5230: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae5234: mov             x1, x0
    // 0xae5238: r0 = Instance_Axis
    //     0xae5238: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xae523c: stur            x1, [fp, #-8]
    // 0xae5240: StoreField: r1->field_f = r0
    //     0xae5240: stur            w0, [x1, #0xf]
    // 0xae5244: r0 = Instance_MainAxisAlignment
    //     0xae5244: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xae5248: ldr             x0, [x0, #0xa08]
    // 0xae524c: StoreField: r1->field_13 = r0
    //     0xae524c: stur            w0, [x1, #0x13]
    // 0xae5250: r0 = Instance_MainAxisSize
    //     0xae5250: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xae5254: ldr             x0, [x0, #0xdd0]
    // 0xae5258: ArrayStore: r1[0] = r0  ; List_4
    //     0xae5258: stur            w0, [x1, #0x17]
    // 0xae525c: ldur            x0, [fp, #-0x18]
    // 0xae5260: StoreField: r1->field_1b = r0
    //     0xae5260: stur            w0, [x1, #0x1b]
    // 0xae5264: r0 = Instance_VerticalDirection
    //     0xae5264: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xae5268: ldr             x0, [x0, #0xa20]
    // 0xae526c: StoreField: r1->field_23 = r0
    //     0xae526c: stur            w0, [x1, #0x23]
    // 0xae5270: r0 = Instance_Clip
    //     0xae5270: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xae5274: ldr             x0, [x0, #0x38]
    // 0xae5278: StoreField: r1->field_2b = r0
    //     0xae5278: stur            w0, [x1, #0x2b]
    // 0xae527c: StoreField: r1->field_2f = rZR
    //     0xae527c: stur            xzr, [x1, #0x2f]
    // 0xae5280: ldur            x0, [fp, #-0x28]
    // 0xae5284: StoreField: r1->field_b = r0
    //     0xae5284: stur            w0, [x1, #0xb]
    // 0xae5288: r0 = Padding()
    //     0xae5288: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae528c: r1 = Instance_EdgeInsets
    //     0xae528c: add             x1, PP, #0x55, lsl #12  ; [pp+0x550a8] Obj!EdgeInsets@d58761
    //     0xae5290: ldr             x1, [x1, #0xa8]
    // 0xae5294: StoreField: r0->field_f = r1
    //     0xae5294: stur            w1, [x0, #0xf]
    // 0xae5298: ldur            x1, [fp, #-8]
    // 0xae529c: StoreField: r0->field_b = r1
    //     0xae529c: stur            w1, [x0, #0xb]
    // 0xae52a0: LeaveFrame
    //     0xae52a0: mov             SP, fp
    //     0xae52a4: ldp             fp, lr, [SP], #0x10
    // 0xae52a8: ret
    //     0xae52a8: ret             
    // 0xae52ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae52ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae52b0: b               #0xae4c30
    // 0xae52b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae52b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae52b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae52b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae52bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae52bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae52c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae52c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae52c4: r9 = _pageCosmeticController
    //     0xae52c4: add             x9, PP, #0x58, lsl #12  ; [pp+0x582c0] Field <_CollectionPosterCarouselState@1465387175._pageCosmeticController@1465387175>: late (offset: 0x14)
    //     0xae52c8: ldr             x9, [x9, #0x2c0]
    // 0xae52cc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xae52cc: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xae52d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae52d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae52d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae52d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] AnimatedContainer <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xae52d8, size: 0x94
    // 0xae52d8: EnterFrame
    //     0xae52d8: stp             fp, lr, [SP, #-0x10]!
    //     0xae52dc: mov             fp, SP
    // 0xae52e0: AllocStack(0x8)
    //     0xae52e0: sub             SP, SP, #8
    // 0xae52e4: SetupParameters()
    //     0xae52e4: ldr             x0, [fp, #0x20]
    //     0xae52e8: ldur            w1, [x0, #0x17]
    //     0xae52ec: add             x1, x1, HEAP, lsl #32
    // 0xae52f0: CheckStackOverflow
    //     0xae52f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae52f4: cmp             SP, x16
    //     0xae52f8: b.ls            #0xae5360
    // 0xae52fc: LoadField: r0 = r1->field_f
    //     0xae52fc: ldur            w0, [x1, #0xf]
    // 0xae5300: DecompressPointer r0
    //     0xae5300: add             x0, x0, HEAP, lsl #32
    // 0xae5304: stur            x0, [fp, #-8]
    // 0xae5308: LoadField: r1 = r0->field_b
    //     0xae5308: ldur            w1, [x0, #0xb]
    // 0xae530c: DecompressPointer r1
    //     0xae530c: add             x1, x1, HEAP, lsl #32
    // 0xae5310: cmp             w1, NULL
    // 0xae5314: b.eq            #0xae5368
    // 0xae5318: LoadField: r2 = r1->field_b
    //     0xae5318: ldur            w2, [x1, #0xb]
    // 0xae531c: DecompressPointer r2
    //     0xae531c: add             x2, x2, HEAP, lsl #32
    // 0xae5320: cmp             w2, NULL
    // 0xae5324: b.ne            #0xae533c
    // 0xae5328: r1 = <Entity>
    //     0xae5328: add             x1, PP, #0x23, lsl #12  ; [pp+0x23b68] TypeArguments: <Entity>
    //     0xae532c: ldr             x1, [x1, #0xb68]
    // 0xae5330: r2 = 0
    //     0xae5330: movz            x2, #0
    // 0xae5334: r0 = AllocateArray()
    //     0xae5334: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae5338: mov             x2, x0
    // 0xae533c: ldr             x0, [fp, #0x10]
    // 0xae5340: r3 = LoadInt32Instr(r0)
    //     0xae5340: sbfx            x3, x0, #1, #0x1f
    //     0xae5344: tbz             w0, #0, #0xae534c
    //     0xae5348: ldur            x3, [x0, #7]
    // 0xae534c: ldur            x1, [fp, #-8]
    // 0xae5350: r0 = cosmeticThemeSlider()
    //     0xae5350: bl              #0xae536c  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/collection_poster_carousel.dart] _CollectionPosterCarouselState::cosmeticThemeSlider
    // 0xae5354: LeaveFrame
    //     0xae5354: mov             SP, fp
    //     0xae5358: ldp             fp, lr, [SP], #0x10
    // 0xae535c: ret
    //     0xae535c: ret             
    // 0xae5360: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae5360: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae5364: b               #0xae52fc
    // 0xae5368: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae5368: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ cosmeticThemeSlider(/* No info */) {
    // ** addr: 0xae536c, size: 0x670
    // 0xae536c: EnterFrame
    //     0xae536c: stp             fp, lr, [SP, #-0x10]!
    //     0xae5370: mov             fp, SP
    // 0xae5374: AllocStack(0x60)
    //     0xae5374: sub             SP, SP, #0x60
    // 0xae5378: SetupParameters(_CollectionPosterCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xae5378: stur            x1, [fp, #-8]
    //     0xae537c: stur            x2, [fp, #-0x10]
    //     0xae5380: stur            x3, [fp, #-0x18]
    // 0xae5384: CheckStackOverflow
    //     0xae5384: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae5388: cmp             SP, x16
    //     0xae538c: b.ls            #0xae59b0
    // 0xae5390: r1 = 3
    //     0xae5390: movz            x1, #0x3
    // 0xae5394: r0 = AllocateContext()
    //     0xae5394: bl              #0x16f6108  ; AllocateContextStub
    // 0xae5398: mov             x3, x0
    // 0xae539c: ldur            x2, [fp, #-8]
    // 0xae53a0: stur            x3, [fp, #-0x20]
    // 0xae53a4: StoreField: r3->field_f = r2
    //     0xae53a4: stur            w2, [x3, #0xf]
    // 0xae53a8: ldur            x0, [fp, #-0x10]
    // 0xae53ac: StoreField: r3->field_13 = r0
    //     0xae53ac: stur            w0, [x3, #0x13]
    // 0xae53b0: ldur            x4, [fp, #-0x18]
    // 0xae53b4: r0 = BoxInt64Instr(r4)
    //     0xae53b4: sbfiz           x0, x4, #1, #0x1f
    //     0xae53b8: cmp             x4, x0, asr #1
    //     0xae53bc: b.eq            #0xae53c8
    //     0xae53c0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae53c4: stur            x4, [x0, #7]
    // 0xae53c8: ArrayStore: r3[0] = r0  ; List_4
    //     0xae53c8: stur            w0, [x3, #0x17]
    // 0xae53cc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae53cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae53d0: ldr             x0, [x0, #0x1c80]
    //     0xae53d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae53d8: cmp             w0, w16
    //     0xae53dc: b.ne            #0xae53e8
    //     0xae53e0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xae53e4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xae53e8: r0 = GetNavigation.size()
    //     0xae53e8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xae53ec: LoadField: d0 = r0->field_7
    //     0xae53ec: ldur            d0, [x0, #7]
    // 0xae53f0: stur            d0, [fp, #-0x48]
    // 0xae53f4: r0 = Radius()
    //     0xae53f4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xae53f8: d0 = 10.000000
    //     0xae53f8: fmov            d0, #10.00000000
    // 0xae53fc: stur            x0, [fp, #-0x10]
    // 0xae5400: StoreField: r0->field_7 = d0
    //     0xae5400: stur            d0, [x0, #7]
    // 0xae5404: StoreField: r0->field_f = d0
    //     0xae5404: stur            d0, [x0, #0xf]
    // 0xae5408: r0 = BorderRadius()
    //     0xae5408: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xae540c: mov             x1, x0
    // 0xae5410: ldur            x0, [fp, #-0x10]
    // 0xae5414: stur            x1, [fp, #-0x28]
    // 0xae5418: StoreField: r1->field_7 = r0
    //     0xae5418: stur            w0, [x1, #7]
    // 0xae541c: StoreField: r1->field_b = r0
    //     0xae541c: stur            w0, [x1, #0xb]
    // 0xae5420: StoreField: r1->field_f = r0
    //     0xae5420: stur            w0, [x1, #0xf]
    // 0xae5424: StoreField: r1->field_13 = r0
    //     0xae5424: stur            w0, [x1, #0x13]
    // 0xae5428: r0 = RoundedRectangleBorder()
    //     0xae5428: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xae542c: mov             x1, x0
    // 0xae5430: ldur            x0, [fp, #-0x28]
    // 0xae5434: stur            x1, [fp, #-0x10]
    // 0xae5438: StoreField: r1->field_b = r0
    //     0xae5438: stur            w0, [x1, #0xb]
    // 0xae543c: r0 = Instance_BorderSide
    //     0xae543c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xae5440: ldr             x0, [x0, #0xe20]
    // 0xae5444: StoreField: r1->field_7 = r0
    //     0xae5444: stur            w0, [x1, #7]
    // 0xae5448: ldur            x2, [fp, #-0x20]
    // 0xae544c: LoadField: r0 = r2->field_13
    //     0xae544c: ldur            w0, [x2, #0x13]
    // 0xae5450: DecompressPointer r0
    //     0xae5450: add             x0, x0, HEAP, lsl #32
    // 0xae5454: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xae5454: ldur            w3, [x2, #0x17]
    // 0xae5458: DecompressPointer r3
    //     0xae5458: add             x3, x3, HEAP, lsl #32
    // 0xae545c: stp             x3, x0, [SP]
    // 0xae5460: r4 = 0
    //     0xae5460: movz            x4, #0
    // 0xae5464: ldr             x0, [SP, #8]
    // 0xae5468: r16 = UnlinkedCall_0x613b5c
    //     0xae5468: add             x16, PP, #0x58, lsl #12  ; [pp+0x582c8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae546c: add             x16, x16, #0x2c8
    // 0xae5470: ldp             x5, lr, [x16]
    // 0xae5474: blr             lr
    // 0xae5478: LoadField: r1 = r0->field_13
    //     0xae5478: ldur            w1, [x0, #0x13]
    // 0xae547c: DecompressPointer r1
    //     0xae547c: add             x1, x1, HEAP, lsl #32
    // 0xae5480: cmp             w1, NULL
    // 0xae5484: b.ne            #0xae5490
    // 0xae5488: r4 = ""
    //     0xae5488: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae548c: b               #0xae5494
    // 0xae5490: mov             x4, x1
    // 0xae5494: ldur            x3, [fp, #-0x20]
    // 0xae5498: ldur            x0, [fp, #-0x10]
    // 0xae549c: ldur            d0, [fp, #-0x48]
    // 0xae54a0: stur            x4, [fp, #-0x28]
    // 0xae54a4: r1 = Function '<anonymous closure>':.
    //     0xae54a4: add             x1, PP, #0x58, lsl #12  ; [pp+0x582d8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xae54a8: ldr             x1, [x1, #0x2d8]
    // 0xae54ac: r2 = Null
    //     0xae54ac: mov             x2, NULL
    // 0xae54b0: r0 = AllocateClosure()
    //     0xae54b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae54b4: r1 = Function '<anonymous closure>':.
    //     0xae54b4: add             x1, PP, #0x58, lsl #12  ; [pp+0x582e0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xae54b8: ldr             x1, [x1, #0x2e0]
    // 0xae54bc: r2 = Null
    //     0xae54bc: mov             x2, NULL
    // 0xae54c0: stur            x0, [fp, #-0x30]
    // 0xae54c4: r0 = AllocateClosure()
    //     0xae54c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae54c8: stur            x0, [fp, #-0x38]
    // 0xae54cc: r0 = CachedNetworkImage()
    //     0xae54cc: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xae54d0: stur            x0, [fp, #-0x40]
    // 0xae54d4: ldur            x16, [fp, #-0x30]
    // 0xae54d8: ldur            lr, [fp, #-0x38]
    // 0xae54dc: stp             lr, x16, [SP, #8]
    // 0xae54e0: r16 = Instance_BoxFit
    //     0xae54e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xae54e4: ldr             x16, [x16, #0x118]
    // 0xae54e8: str             x16, [SP]
    // 0xae54ec: mov             x1, x0
    // 0xae54f0: ldur            x2, [fp, #-0x28]
    // 0xae54f4: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x3, fit, 0x4, progressIndicatorBuilder, 0x2, null]
    //     0xae54f4: add             x4, PP, #0x55, lsl #12  ; [pp+0x55790] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x3, "fit", 0x4, "progressIndicatorBuilder", 0x2, Null]
    //     0xae54f8: ldr             x4, [x4, #0x790]
    // 0xae54fc: r0 = CachedNetworkImage()
    //     0xae54fc: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xae5500: r0 = Card()
    //     0xae5500: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xae5504: mov             x1, x0
    // 0xae5508: r0 = 0.000000
    //     0xae5508: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xae550c: stur            x1, [fp, #-0x28]
    // 0xae5510: ArrayStore: r1[0] = r0  ; List_4
    //     0xae5510: stur            w0, [x1, #0x17]
    // 0xae5514: ldur            x0, [fp, #-0x10]
    // 0xae5518: StoreField: r1->field_1b = r0
    //     0xae5518: stur            w0, [x1, #0x1b]
    // 0xae551c: r0 = true
    //     0xae551c: add             x0, NULL, #0x20  ; true
    // 0xae5520: StoreField: r1->field_1f = r0
    //     0xae5520: stur            w0, [x1, #0x1f]
    // 0xae5524: r2 = Instance_Clip
    //     0xae5524: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xae5528: ldr             x2, [x2, #0xb50]
    // 0xae552c: StoreField: r1->field_23 = r2
    //     0xae552c: stur            w2, [x1, #0x23]
    // 0xae5530: ldur            x2, [fp, #-0x40]
    // 0xae5534: StoreField: r1->field_2f = r2
    //     0xae5534: stur            w2, [x1, #0x2f]
    // 0xae5538: StoreField: r1->field_2b = r0
    //     0xae5538: stur            w0, [x1, #0x2b]
    // 0xae553c: r2 = Instance__CardVariant
    //     0xae553c: add             x2, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xae5540: ldr             x2, [x2, #0xa68]
    // 0xae5544: StoreField: r1->field_33 = r2
    //     0xae5544: stur            w2, [x1, #0x33]
    // 0xae5548: ldur            d0, [fp, #-0x48]
    // 0xae554c: r2 = inline_Allocate_Double()
    //     0xae554c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xae5550: add             x2, x2, #0x10
    //     0xae5554: cmp             x3, x2
    //     0xae5558: b.ls            #0xae59b8
    //     0xae555c: str             x2, [THR, #0x50]  ; THR::top
    //     0xae5560: sub             x2, x2, #0xf
    //     0xae5564: movz            x3, #0xe15c
    //     0xae5568: movk            x3, #0x3, lsl #16
    //     0xae556c: stur            x3, [x2, #-1]
    // 0xae5570: StoreField: r2->field_7 = d0
    //     0xae5570: stur            d0, [x2, #7]
    // 0xae5574: stur            x2, [fp, #-0x10]
    // 0xae5578: r0 = SizedBox()
    //     0xae5578: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xae557c: mov             x3, x0
    // 0xae5580: ldur            x0, [fp, #-0x10]
    // 0xae5584: stur            x3, [fp, #-0x30]
    // 0xae5588: StoreField: r3->field_f = r0
    //     0xae5588: stur            w0, [x3, #0xf]
    // 0xae558c: r0 = 212.000000
    //     0xae558c: add             x0, PP, #0x58, lsl #12  ; [pp+0x582e8] 212
    //     0xae5590: ldr             x0, [x0, #0x2e8]
    // 0xae5594: StoreField: r3->field_13 = r0
    //     0xae5594: stur            w0, [x3, #0x13]
    // 0xae5598: ldur            x0, [fp, #-0x28]
    // 0xae559c: StoreField: r3->field_b = r0
    //     0xae559c: stur            w0, [x3, #0xb]
    // 0xae55a0: r1 = Null
    //     0xae55a0: mov             x1, NULL
    // 0xae55a4: r2 = 2
    //     0xae55a4: movz            x2, #0x2
    // 0xae55a8: r0 = AllocateArray()
    //     0xae55a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae55ac: mov             x2, x0
    // 0xae55b0: ldur            x0, [fp, #-0x30]
    // 0xae55b4: stur            x2, [fp, #-0x10]
    // 0xae55b8: StoreField: r2->field_f = r0
    //     0xae55b8: stur            w0, [x2, #0xf]
    // 0xae55bc: r1 = <Widget>
    //     0xae55bc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae55c0: r0 = AllocateGrowableArray()
    //     0xae55c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xae55c4: mov             x1, x0
    // 0xae55c8: ldur            x0, [fp, #-0x10]
    // 0xae55cc: stur            x1, [fp, #-0x28]
    // 0xae55d0: StoreField: r1->field_f = r0
    //     0xae55d0: stur            w0, [x1, #0xf]
    // 0xae55d4: r2 = 2
    //     0xae55d4: movz            x2, #0x2
    // 0xae55d8: StoreField: r1->field_b = r2
    //     0xae55d8: stur            w2, [x1, #0xb]
    // 0xae55dc: ldur            x0, [fp, #-0x20]
    // 0xae55e0: LoadField: r3 = r0->field_13
    //     0xae55e0: ldur            w3, [x0, #0x13]
    // 0xae55e4: DecompressPointer r3
    //     0xae55e4: add             x3, x3, HEAP, lsl #32
    // 0xae55e8: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xae55e8: ldur            w4, [x0, #0x17]
    // 0xae55ec: DecompressPointer r4
    //     0xae55ec: add             x4, x4, HEAP, lsl #32
    // 0xae55f0: stp             x4, x3, [SP]
    // 0xae55f4: r4 = 0
    //     0xae55f4: movz            x4, #0
    // 0xae55f8: ldr             x0, [SP, #8]
    // 0xae55fc: r16 = UnlinkedCall_0x613b5c
    //     0xae55fc: add             x16, PP, #0x58, lsl #12  ; [pp+0x582f0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae5600: add             x16, x16, #0x2f0
    // 0xae5604: ldp             x5, lr, [x16]
    // 0xae5608: blr             lr
    // 0xae560c: LoadField: r1 = r0->field_7
    //     0xae560c: ldur            w1, [x0, #7]
    // 0xae5610: DecompressPointer r1
    //     0xae5610: add             x1, x1, HEAP, lsl #32
    // 0xae5614: cmp             w1, NULL
    // 0xae5618: b.eq            #0xae59d4
    // 0xae561c: LoadField: r0 = r1->field_7
    //     0xae561c: ldur            w0, [x1, #7]
    // 0xae5620: cbz             w0, #0xae579c
    // 0xae5624: ldur            x0, [fp, #-8]
    // 0xae5628: ldur            x2, [fp, #-0x20]
    // 0xae562c: ldur            x1, [fp, #-0x28]
    // 0xae5630: LoadField: r3 = r2->field_13
    //     0xae5630: ldur            w3, [x2, #0x13]
    // 0xae5634: DecompressPointer r3
    //     0xae5634: add             x3, x3, HEAP, lsl #32
    // 0xae5638: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xae5638: ldur            w4, [x2, #0x17]
    // 0xae563c: DecompressPointer r4
    //     0xae563c: add             x4, x4, HEAP, lsl #32
    // 0xae5640: stp             x4, x3, [SP]
    // 0xae5644: r4 = 0
    //     0xae5644: movz            x4, #0
    // 0xae5648: ldr             x0, [SP, #8]
    // 0xae564c: r16 = UnlinkedCall_0x613b5c
    //     0xae564c: add             x16, PP, #0x58, lsl #12  ; [pp+0x58300] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae5650: add             x16, x16, #0x300
    // 0xae5654: ldp             x5, lr, [x16]
    // 0xae5658: blr             lr
    // 0xae565c: LoadField: r3 = r0->field_7
    //     0xae565c: ldur            w3, [x0, #7]
    // 0xae5660: DecompressPointer r3
    //     0xae5660: add             x3, x3, HEAP, lsl #32
    // 0xae5664: mov             x0, x3
    // 0xae5668: stur            x3, [fp, #-0x10]
    // 0xae566c: r2 = Null
    //     0xae566c: mov             x2, NULL
    // 0xae5670: r1 = Null
    //     0xae5670: mov             x1, NULL
    // 0xae5674: r4 = LoadClassIdInstr(r0)
    //     0xae5674: ldur            x4, [x0, #-1]
    //     0xae5678: ubfx            x4, x4, #0xc, #0x14
    // 0xae567c: sub             x4, x4, #0x5e
    // 0xae5680: cmp             x4, #1
    // 0xae5684: b.ls            #0xae5698
    // 0xae5688: r8 = String
    //     0xae5688: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xae568c: r3 = Null
    //     0xae568c: add             x3, PP, #0x58, lsl #12  ; [pp+0x58310] Null
    //     0xae5690: ldr             x3, [x3, #0x310]
    // 0xae5694: r0 = String()
    //     0xae5694: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xae5698: ldur            x0, [fp, #-8]
    // 0xae569c: LoadField: r1 = r0->field_f
    //     0xae569c: ldur            w1, [x0, #0xf]
    // 0xae56a0: DecompressPointer r1
    //     0xae56a0: add             x1, x1, HEAP, lsl #32
    // 0xae56a4: cmp             w1, NULL
    // 0xae56a8: b.eq            #0xae59d8
    // 0xae56ac: r0 = of()
    //     0xae56ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae56b0: LoadField: r1 = r0->field_87
    //     0xae56b0: ldur            w1, [x0, #0x87]
    // 0xae56b4: DecompressPointer r1
    //     0xae56b4: add             x1, x1, HEAP, lsl #32
    // 0xae56b8: LoadField: r0 = r1->field_7
    //     0xae56b8: ldur            w0, [x1, #7]
    // 0xae56bc: DecompressPointer r0
    //     0xae56bc: add             x0, x0, HEAP, lsl #32
    // 0xae56c0: r16 = Instance_Color
    //     0xae56c0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xae56c4: r30 = 21.000000
    //     0xae56c4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xae56c8: ldr             lr, [lr, #0x9b0]
    // 0xae56cc: stp             lr, x16, [SP]
    // 0xae56d0: mov             x1, x0
    // 0xae56d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xae56d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xae56d8: ldr             x4, [x4, #0x9b8]
    // 0xae56dc: r0 = copyWith()
    //     0xae56dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae56e0: stur            x0, [fp, #-8]
    // 0xae56e4: r0 = Text()
    //     0xae56e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xae56e8: mov             x1, x0
    // 0xae56ec: ldur            x0, [fp, #-0x10]
    // 0xae56f0: stur            x1, [fp, #-0x30]
    // 0xae56f4: StoreField: r1->field_b = r0
    //     0xae56f4: stur            w0, [x1, #0xb]
    // 0xae56f8: ldur            x0, [fp, #-8]
    // 0xae56fc: StoreField: r1->field_13 = r0
    //     0xae56fc: stur            w0, [x1, #0x13]
    // 0xae5700: r0 = Padding()
    //     0xae5700: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae5704: mov             x2, x0
    // 0xae5708: r0 = Instance_EdgeInsets
    //     0xae5708: add             x0, PP, #0x58, lsl #12  ; [pp+0x58320] Obj!EdgeInsets@d58e51
    //     0xae570c: ldr             x0, [x0, #0x320]
    // 0xae5710: stur            x2, [fp, #-8]
    // 0xae5714: StoreField: r2->field_f = r0
    //     0xae5714: stur            w0, [x2, #0xf]
    // 0xae5718: ldur            x0, [fp, #-0x30]
    // 0xae571c: StoreField: r2->field_b = r0
    //     0xae571c: stur            w0, [x2, #0xb]
    // 0xae5720: ldur            x0, [fp, #-0x28]
    // 0xae5724: LoadField: r1 = r0->field_b
    //     0xae5724: ldur            w1, [x0, #0xb]
    // 0xae5728: LoadField: r3 = r0->field_f
    //     0xae5728: ldur            w3, [x0, #0xf]
    // 0xae572c: DecompressPointer r3
    //     0xae572c: add             x3, x3, HEAP, lsl #32
    // 0xae5730: LoadField: r4 = r3->field_b
    //     0xae5730: ldur            w4, [x3, #0xb]
    // 0xae5734: r3 = LoadInt32Instr(r1)
    //     0xae5734: sbfx            x3, x1, #1, #0x1f
    // 0xae5738: stur            x3, [fp, #-0x18]
    // 0xae573c: r1 = LoadInt32Instr(r4)
    //     0xae573c: sbfx            x1, x4, #1, #0x1f
    // 0xae5740: cmp             x3, x1
    // 0xae5744: b.ne            #0xae5750
    // 0xae5748: mov             x1, x0
    // 0xae574c: r0 = _growToNextCapacity()
    //     0xae574c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae5750: ldur            x2, [fp, #-0x28]
    // 0xae5754: ldur            x3, [fp, #-0x18]
    // 0xae5758: add             x0, x3, #1
    // 0xae575c: lsl             x1, x0, #1
    // 0xae5760: StoreField: r2->field_b = r1
    //     0xae5760: stur            w1, [x2, #0xb]
    // 0xae5764: LoadField: r1 = r2->field_f
    //     0xae5764: ldur            w1, [x2, #0xf]
    // 0xae5768: DecompressPointer r1
    //     0xae5768: add             x1, x1, HEAP, lsl #32
    // 0xae576c: ldur            x0, [fp, #-8]
    // 0xae5770: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae5770: add             x25, x1, x3, lsl #2
    //     0xae5774: add             x25, x25, #0xf
    //     0xae5778: str             w0, [x25]
    //     0xae577c: tbz             w0, #0, #0xae5798
    //     0xae5780: ldurb           w16, [x1, #-1]
    //     0xae5784: ldurb           w17, [x0, #-1]
    //     0xae5788: and             x16, x17, x16, lsr #2
    //     0xae578c: tst             x16, HEAP, lsr #32
    //     0xae5790: b.eq            #0xae5798
    //     0xae5794: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae5798: b               #0xae582c
    // 0xae579c: ldur            x2, [fp, #-0x28]
    // 0xae57a0: r0 = Container()
    //     0xae57a0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xae57a4: mov             x1, x0
    // 0xae57a8: stur            x0, [fp, #-8]
    // 0xae57ac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae57ac: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae57b0: r0 = Container()
    //     0xae57b0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae57b4: ldur            x0, [fp, #-0x28]
    // 0xae57b8: LoadField: r1 = r0->field_b
    //     0xae57b8: ldur            w1, [x0, #0xb]
    // 0xae57bc: LoadField: r2 = r0->field_f
    //     0xae57bc: ldur            w2, [x0, #0xf]
    // 0xae57c0: DecompressPointer r2
    //     0xae57c0: add             x2, x2, HEAP, lsl #32
    // 0xae57c4: LoadField: r3 = r2->field_b
    //     0xae57c4: ldur            w3, [x2, #0xb]
    // 0xae57c8: r2 = LoadInt32Instr(r1)
    //     0xae57c8: sbfx            x2, x1, #1, #0x1f
    // 0xae57cc: stur            x2, [fp, #-0x18]
    // 0xae57d0: r1 = LoadInt32Instr(r3)
    //     0xae57d0: sbfx            x1, x3, #1, #0x1f
    // 0xae57d4: cmp             x2, x1
    // 0xae57d8: b.ne            #0xae57e4
    // 0xae57dc: mov             x1, x0
    // 0xae57e0: r0 = _growToNextCapacity()
    //     0xae57e0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae57e4: ldur            x2, [fp, #-0x28]
    // 0xae57e8: ldur            x3, [fp, #-0x18]
    // 0xae57ec: add             x0, x3, #1
    // 0xae57f0: lsl             x1, x0, #1
    // 0xae57f4: StoreField: r2->field_b = r1
    //     0xae57f4: stur            w1, [x2, #0xb]
    // 0xae57f8: LoadField: r1 = r2->field_f
    //     0xae57f8: ldur            w1, [x2, #0xf]
    // 0xae57fc: DecompressPointer r1
    //     0xae57fc: add             x1, x1, HEAP, lsl #32
    // 0xae5800: ldur            x0, [fp, #-8]
    // 0xae5804: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae5804: add             x25, x1, x3, lsl #2
    //     0xae5808: add             x25, x25, #0xf
    //     0xae580c: str             w0, [x25]
    //     0xae5810: tbz             w0, #0, #0xae582c
    //     0xae5814: ldurb           w16, [x1, #-1]
    //     0xae5818: ldurb           w17, [x0, #-1]
    //     0xae581c: and             x16, x17, x16, lsr #2
    //     0xae5820: tst             x16, HEAP, lsr #32
    //     0xae5824: b.eq            #0xae582c
    //     0xae5828: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae582c: r0 = Stack()
    //     0xae582c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xae5830: mov             x1, x0
    // 0xae5834: r0 = Instance_Alignment
    //     0xae5834: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0xae5838: ldr             x0, [x0, #0xa28]
    // 0xae583c: stur            x1, [fp, #-8]
    // 0xae5840: StoreField: r1->field_f = r0
    //     0xae5840: stur            w0, [x1, #0xf]
    // 0xae5844: r0 = Instance_StackFit
    //     0xae5844: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xae5848: ldr             x0, [x0, #0xfa8]
    // 0xae584c: ArrayStore: r1[0] = r0  ; List_4
    //     0xae584c: stur            w0, [x1, #0x17]
    // 0xae5850: r0 = Instance_Clip
    //     0xae5850: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xae5854: ldr             x0, [x0, #0x7e0]
    // 0xae5858: StoreField: r1->field_1b = r0
    //     0xae5858: stur            w0, [x1, #0x1b]
    // 0xae585c: ldur            x0, [fp, #-0x28]
    // 0xae5860: StoreField: r1->field_b = r0
    //     0xae5860: stur            w0, [x1, #0xb]
    // 0xae5864: r0 = InkWell()
    //     0xae5864: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xae5868: mov             x3, x0
    // 0xae586c: ldur            x0, [fp, #-8]
    // 0xae5870: stur            x3, [fp, #-0x10]
    // 0xae5874: StoreField: r3->field_b = r0
    //     0xae5874: stur            w0, [x3, #0xb]
    // 0xae5878: ldur            x2, [fp, #-0x20]
    // 0xae587c: r1 = Function '<anonymous closure>':.
    //     0xae587c: add             x1, PP, #0x58, lsl #12  ; [pp+0x58328] AnonymousClosure: (0xae59dc), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/collection_poster_carousel.dart] _CollectionPosterCarouselState::cosmeticThemeSlider (0xae536c)
    //     0xae5880: ldr             x1, [x1, #0x328]
    // 0xae5884: r0 = AllocateClosure()
    //     0xae5884: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae5888: mov             x1, x0
    // 0xae588c: ldur            x0, [fp, #-0x10]
    // 0xae5890: StoreField: r0->field_f = r1
    //     0xae5890: stur            w1, [x0, #0xf]
    // 0xae5894: r1 = true
    //     0xae5894: add             x1, NULL, #0x20  ; true
    // 0xae5898: StoreField: r0->field_43 = r1
    //     0xae5898: stur            w1, [x0, #0x43]
    // 0xae589c: r2 = Instance_BoxShape
    //     0xae589c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xae58a0: ldr             x2, [x2, #0x80]
    // 0xae58a4: StoreField: r0->field_47 = r2
    //     0xae58a4: stur            w2, [x0, #0x47]
    // 0xae58a8: StoreField: r0->field_6f = r1
    //     0xae58a8: stur            w1, [x0, #0x6f]
    // 0xae58ac: r2 = false
    //     0xae58ac: add             x2, NULL, #0x30  ; false
    // 0xae58b0: StoreField: r0->field_73 = r2
    //     0xae58b0: stur            w2, [x0, #0x73]
    // 0xae58b4: StoreField: r0->field_83 = r1
    //     0xae58b4: stur            w1, [x0, #0x83]
    // 0xae58b8: StoreField: r0->field_7b = r2
    //     0xae58b8: stur            w2, [x0, #0x7b]
    // 0xae58bc: r0 = Padding()
    //     0xae58bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae58c0: mov             x3, x0
    // 0xae58c4: r0 = Instance_EdgeInsets
    //     0xae58c4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xae58c8: ldr             x0, [x0, #0x100]
    // 0xae58cc: stur            x3, [fp, #-8]
    // 0xae58d0: StoreField: r3->field_f = r0
    //     0xae58d0: stur            w0, [x3, #0xf]
    // 0xae58d4: ldur            x0, [fp, #-0x10]
    // 0xae58d8: StoreField: r3->field_b = r0
    //     0xae58d8: stur            w0, [x3, #0xb]
    // 0xae58dc: r1 = Null
    //     0xae58dc: mov             x1, NULL
    // 0xae58e0: r2 = 2
    //     0xae58e0: movz            x2, #0x2
    // 0xae58e4: r0 = AllocateArray()
    //     0xae58e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae58e8: mov             x2, x0
    // 0xae58ec: ldur            x0, [fp, #-8]
    // 0xae58f0: stur            x2, [fp, #-0x10]
    // 0xae58f4: StoreField: r2->field_f = r0
    //     0xae58f4: stur            w0, [x2, #0xf]
    // 0xae58f8: r1 = <Widget>
    //     0xae58f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae58fc: r0 = AllocateGrowableArray()
    //     0xae58fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xae5900: mov             x1, x0
    // 0xae5904: ldur            x0, [fp, #-0x10]
    // 0xae5908: stur            x1, [fp, #-8]
    // 0xae590c: StoreField: r1->field_f = r0
    //     0xae590c: stur            w0, [x1, #0xf]
    // 0xae5910: r0 = 2
    //     0xae5910: movz            x0, #0x2
    // 0xae5914: StoreField: r1->field_b = r0
    //     0xae5914: stur            w0, [x1, #0xb]
    // 0xae5918: r0 = Column()
    //     0xae5918: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae591c: mov             x1, x0
    // 0xae5920: r0 = Instance_Axis
    //     0xae5920: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xae5924: stur            x1, [fp, #-0x10]
    // 0xae5928: StoreField: r1->field_f = r0
    //     0xae5928: stur            w0, [x1, #0xf]
    // 0xae592c: r0 = Instance_MainAxisAlignment
    //     0xae592c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xae5930: ldr             x0, [x0, #0xa08]
    // 0xae5934: StoreField: r1->field_13 = r0
    //     0xae5934: stur            w0, [x1, #0x13]
    // 0xae5938: r0 = Instance_MainAxisSize
    //     0xae5938: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xae593c: ldr             x0, [x0, #0xa10]
    // 0xae5940: ArrayStore: r1[0] = r0  ; List_4
    //     0xae5940: stur            w0, [x1, #0x17]
    // 0xae5944: r0 = Instance_CrossAxisAlignment
    //     0xae5944: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xae5948: ldr             x0, [x0, #0xa18]
    // 0xae594c: StoreField: r1->field_1b = r0
    //     0xae594c: stur            w0, [x1, #0x1b]
    // 0xae5950: r0 = Instance_VerticalDirection
    //     0xae5950: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xae5954: ldr             x0, [x0, #0xa20]
    // 0xae5958: StoreField: r1->field_23 = r0
    //     0xae5958: stur            w0, [x1, #0x23]
    // 0xae595c: r0 = Instance_Clip
    //     0xae595c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xae5960: ldr             x0, [x0, #0x38]
    // 0xae5964: StoreField: r1->field_2b = r0
    //     0xae5964: stur            w0, [x1, #0x2b]
    // 0xae5968: StoreField: r1->field_2f = rZR
    //     0xae5968: stur            xzr, [x1, #0x2f]
    // 0xae596c: ldur            x0, [fp, #-8]
    // 0xae5970: StoreField: r1->field_b = r0
    //     0xae5970: stur            w0, [x1, #0xb]
    // 0xae5974: r0 = AnimatedContainer()
    //     0xae5974: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xae5978: stur            x0, [fp, #-8]
    // 0xae597c: r16 = Instance_Cubic
    //     0xae597c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xae5980: ldr             x16, [x16, #0xaf8]
    // 0xae5984: str             x16, [SP]
    // 0xae5988: mov             x1, x0
    // 0xae598c: ldur            x2, [fp, #-0x10]
    // 0xae5990: r3 = Instance_Duration
    //     0xae5990: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xae5994: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xae5994: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xae5998: ldr             x4, [x4, #0xbc8]
    // 0xae599c: r0 = AnimatedContainer()
    //     0xae599c: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xae59a0: ldur            x0, [fp, #-8]
    // 0xae59a4: LeaveFrame
    //     0xae59a4: mov             SP, fp
    //     0xae59a8: ldp             fp, lr, [SP], #0x10
    // 0xae59ac: ret
    //     0xae59ac: ret             
    // 0xae59b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae59b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae59b4: b               #0xae5390
    // 0xae59b8: SaveReg d0
    //     0xae59b8: str             q0, [SP, #-0x10]!
    // 0xae59bc: stp             x0, x1, [SP, #-0x10]!
    // 0xae59c0: r0 = AllocateDouble()
    //     0xae59c0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xae59c4: mov             x2, x0
    // 0xae59c8: ldp             x0, x1, [SP], #0x10
    // 0xae59cc: RestoreReg d0
    //     0xae59cc: ldr             q0, [SP], #0x10
    // 0xae59d0: b               #0xae5570
    // 0xae59d4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xae59d4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xae59d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae59d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae59dc, size: 0x124
    // 0xae59dc: EnterFrame
    //     0xae59dc: stp             fp, lr, [SP, #-0x10]!
    //     0xae59e0: mov             fp, SP
    // 0xae59e4: AllocStack(0x78)
    //     0xae59e4: sub             SP, SP, #0x78
    // 0xae59e8: SetupParameters()
    //     0xae59e8: ldr             x0, [fp, #0x10]
    //     0xae59ec: ldur            w1, [x0, #0x17]
    //     0xae59f0: add             x1, x1, HEAP, lsl #32
    // 0xae59f4: CheckStackOverflow
    //     0xae59f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae59f8: cmp             SP, x16
    //     0xae59fc: b.ls            #0xae5af4
    // 0xae5a00: LoadField: r0 = r1->field_f
    //     0xae5a00: ldur            w0, [x1, #0xf]
    // 0xae5a04: DecompressPointer r0
    //     0xae5a04: add             x0, x0, HEAP, lsl #32
    // 0xae5a08: LoadField: r2 = r0->field_b
    //     0xae5a08: ldur            w2, [x0, #0xb]
    // 0xae5a0c: DecompressPointer r2
    //     0xae5a0c: add             x2, x2, HEAP, lsl #32
    // 0xae5a10: stur            x2, [fp, #-0x38]
    // 0xae5a14: cmp             w2, NULL
    // 0xae5a18: b.eq            #0xae5afc
    // 0xae5a1c: LoadField: r0 = r2->field_1b
    //     0xae5a1c: ldur            w0, [x2, #0x1b]
    // 0xae5a20: DecompressPointer r0
    //     0xae5a20: add             x0, x0, HEAP, lsl #32
    // 0xae5a24: stur            x0, [fp, #-0x30]
    // 0xae5a28: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xae5a28: ldur            w3, [x2, #0x17]
    // 0xae5a2c: DecompressPointer r3
    //     0xae5a2c: add             x3, x3, HEAP, lsl #32
    // 0xae5a30: stur            x3, [fp, #-0x28]
    // 0xae5a34: LoadField: r4 = r2->field_f
    //     0xae5a34: ldur            w4, [x2, #0xf]
    // 0xae5a38: DecompressPointer r4
    //     0xae5a38: add             x4, x4, HEAP, lsl #32
    // 0xae5a3c: stur            x4, [fp, #-0x20]
    // 0xae5a40: LoadField: r5 = r2->field_23
    //     0xae5a40: ldur            w5, [x2, #0x23]
    // 0xae5a44: DecompressPointer r5
    //     0xae5a44: add             x5, x5, HEAP, lsl #32
    // 0xae5a48: stur            x5, [fp, #-0x18]
    // 0xae5a4c: LoadField: r6 = r2->field_1f
    //     0xae5a4c: ldur            w6, [x2, #0x1f]
    // 0xae5a50: DecompressPointer r6
    //     0xae5a50: add             x6, x6, HEAP, lsl #32
    // 0xae5a54: stur            x6, [fp, #-0x10]
    // 0xae5a58: LoadField: r7 = r2->field_2b
    //     0xae5a58: ldur            w7, [x2, #0x2b]
    // 0xae5a5c: DecompressPointer r7
    //     0xae5a5c: add             x7, x7, HEAP, lsl #32
    // 0xae5a60: stur            x7, [fp, #-8]
    // 0xae5a64: LoadField: r8 = r1->field_13
    //     0xae5a64: ldur            w8, [x1, #0x13]
    // 0xae5a68: DecompressPointer r8
    //     0xae5a68: add             x8, x8, HEAP, lsl #32
    // 0xae5a6c: ArrayLoad: r9 = r1[0]  ; List_4
    //     0xae5a6c: ldur            w9, [x1, #0x17]
    // 0xae5a70: DecompressPointer r9
    //     0xae5a70: add             x9, x9, HEAP, lsl #32
    // 0xae5a74: stp             x9, x8, [SP]
    // 0xae5a78: r4 = 0
    //     0xae5a78: movz            x4, #0
    // 0xae5a7c: ldr             x0, [SP, #8]
    // 0xae5a80: r16 = UnlinkedCall_0x613b5c
    //     0xae5a80: add             x16, PP, #0x58, lsl #12  ; [pp+0x58330] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae5a84: add             x16, x16, #0x330
    // 0xae5a88: ldp             x5, lr, [x16]
    // 0xae5a8c: blr             lr
    // 0xae5a90: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae5a90: ldur            w1, [x0, #0x17]
    // 0xae5a94: DecompressPointer r1
    //     0xae5a94: add             x1, x1, HEAP, lsl #32
    // 0xae5a98: ldur            x0, [fp, #-0x38]
    // 0xae5a9c: LoadField: r2 = r0->field_27
    //     0xae5a9c: ldur            w2, [x0, #0x27]
    // 0xae5aa0: DecompressPointer r2
    //     0xae5aa0: add             x2, x2, HEAP, lsl #32
    // 0xae5aa4: ldur            x16, [fp, #-0x30]
    // 0xae5aa8: stp             x16, x2, [SP, #0x30]
    // 0xae5aac: ldur            x16, [fp, #-0x28]
    // 0xae5ab0: ldur            lr, [fp, #-0x20]
    // 0xae5ab4: stp             lr, x16, [SP, #0x20]
    // 0xae5ab8: ldur            x16, [fp, #-0x18]
    // 0xae5abc: ldur            lr, [fp, #-0x10]
    // 0xae5ac0: stp             lr, x16, [SP, #0x10]
    // 0xae5ac4: ldur            x16, [fp, #-8]
    // 0xae5ac8: stp             x1, x16, [SP]
    // 0xae5acc: r4 = 0
    //     0xae5acc: movz            x4, #0
    // 0xae5ad0: ldr             x0, [SP, #0x38]
    // 0xae5ad4: r16 = UnlinkedCall_0x613b5c
    //     0xae5ad4: add             x16, PP, #0x58, lsl #12  ; [pp+0x58340] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae5ad8: add             x16, x16, #0x340
    // 0xae5adc: ldp             x5, lr, [x16]
    // 0xae5ae0: blr             lr
    // 0xae5ae4: r0 = Null
    //     0xae5ae4: mov             x0, NULL
    // 0xae5ae8: LeaveFrame
    //     0xae5ae8: mov             SP, fp
    //     0xae5aec: ldp             fp, lr, [SP], #0x10
    // 0xae5af0: ret
    //     0xae5af0: ret             
    // 0xae5af4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae5af4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae5af8: b               #0xae5a00
    // 0xae5afc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae5afc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xae5b00, size: 0x84
    // 0xae5b00: EnterFrame
    //     0xae5b00: stp             fp, lr, [SP, #-0x10]!
    //     0xae5b04: mov             fp, SP
    // 0xae5b08: AllocStack(0x10)
    //     0xae5b08: sub             SP, SP, #0x10
    // 0xae5b0c: SetupParameters()
    //     0xae5b0c: ldr             x0, [fp, #0x18]
    //     0xae5b10: ldur            w1, [x0, #0x17]
    //     0xae5b14: add             x1, x1, HEAP, lsl #32
    //     0xae5b18: stur            x1, [fp, #-8]
    // 0xae5b1c: CheckStackOverflow
    //     0xae5b1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae5b20: cmp             SP, x16
    //     0xae5b24: b.ls            #0xae5b7c
    // 0xae5b28: r1 = 1
    //     0xae5b28: movz            x1, #0x1
    // 0xae5b2c: r0 = AllocateContext()
    //     0xae5b2c: bl              #0x16f6108  ; AllocateContextStub
    // 0xae5b30: mov             x1, x0
    // 0xae5b34: ldur            x0, [fp, #-8]
    // 0xae5b38: StoreField: r1->field_b = r0
    //     0xae5b38: stur            w0, [x1, #0xb]
    // 0xae5b3c: ldr             x2, [fp, #0x10]
    // 0xae5b40: StoreField: r1->field_f = r2
    //     0xae5b40: stur            w2, [x1, #0xf]
    // 0xae5b44: LoadField: r3 = r0->field_f
    //     0xae5b44: ldur            w3, [x0, #0xf]
    // 0xae5b48: DecompressPointer r3
    //     0xae5b48: add             x3, x3, HEAP, lsl #32
    // 0xae5b4c: mov             x2, x1
    // 0xae5b50: stur            x3, [fp, #-0x10]
    // 0xae5b54: r1 = Function '<anonymous closure>':.
    //     0xae5b54: add             x1, PP, #0x58, lsl #12  ; [pp+0x58350] AnonymousClosure: (0x98e9d8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0xae5b58: ldr             x1, [x1, #0x350]
    // 0xae5b5c: r0 = AllocateClosure()
    //     0xae5b5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae5b60: ldur            x1, [fp, #-0x10]
    // 0xae5b64: mov             x2, x0
    // 0xae5b68: r0 = setState()
    //     0xae5b68: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xae5b6c: r0 = Null
    //     0xae5b6c: mov             x0, NULL
    // 0xae5b70: LeaveFrame
    //     0xae5b70: mov             SP, fp
    //     0xae5b74: ldp             fp, lr, [SP], #0x10
    // 0xae5b78: ret
    //     0xae5b78: ret             
    // 0xae5b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae5b7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae5b80: b               #0xae5b28
  }
}

// class id: 4163, size: 0x30, field offset: 0xc
//   const constructor, 
class CollectionPosterCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7da1c, size: 0x30
    // 0xc7da1c: EnterFrame
    //     0xc7da1c: stp             fp, lr, [SP, #-0x10]!
    //     0xc7da20: mov             fp, SP
    // 0xc7da24: mov             x0, x1
    // 0xc7da28: r1 = <CollectionPosterCarousel>
    //     0xc7da28: add             x1, PP, #0x48, lsl #12  ; [pp+0x48be0] TypeArguments: <CollectionPosterCarousel>
    //     0xc7da2c: ldr             x1, [x1, #0xbe0]
    // 0xc7da30: r0 = _CollectionPosterCarouselState()
    //     0xc7da30: bl              #0xc7da4c  ; Allocate_CollectionPosterCarouselStateStub -> _CollectionPosterCarouselState (size=0x20)
    // 0xc7da34: r1 = Sentinel
    //     0xc7da34: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7da38: StoreField: r0->field_13 = r1
    //     0xc7da38: stur            w1, [x0, #0x13]
    // 0xc7da3c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc7da3c: stur            xzr, [x0, #0x17]
    // 0xc7da40: LeaveFrame
    //     0xc7da40: mov             SP, fp
    //     0xc7da44: ldp             fp, lr, [SP], #0x10
    // 0xc7da48: ret
    //     0xc7da48: ret             
  }
}
