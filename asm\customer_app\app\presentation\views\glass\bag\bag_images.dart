// lib: , url: package:customer_app/app/presentation/views/glass/bag/bag_images.dart

// class id: 1049340, size: 0x8
class :: {
}

// class id: 3384, size: 0x20, field offset: 0x14
class _BagImagesState extends State<dynamic> {

  late String value; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x93d78c, size: 0x268
    // 0x93d78c: EnterFrame
    //     0x93d78c: stp             fp, lr, [SP, #-0x10]!
    //     0x93d790: mov             fp, SP
    // 0x93d794: AllocStack(0x20)
    //     0x93d794: sub             SP, SP, #0x20
    // 0x93d798: SetupParameters(_BagImagesState this /* r1 => r2, fp-0x10 */)
    //     0x93d798: mov             x2, x1
    //     0x93d79c: stur            x1, [fp, #-0x10]
    // 0x93d7a0: CheckStackOverflow
    //     0x93d7a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93d7a4: cmp             SP, x16
    //     0x93d7a8: b.ls            #0x93d9e0
    // 0x93d7ac: LoadField: r3 = r2->field_b
    //     0x93d7ac: ldur            w3, [x2, #0xb]
    // 0x93d7b0: DecompressPointer r3
    //     0x93d7b0: add             x3, x3, HEAP, lsl #32
    // 0x93d7b4: cmp             w3, NULL
    // 0x93d7b8: b.eq            #0x93d9e8
    // 0x93d7bc: LoadField: r0 = r3->field_b
    //     0x93d7bc: ldur            w0, [x3, #0xb]
    // 0x93d7c0: DecompressPointer r0
    //     0x93d7c0: add             x0, x0, HEAP, lsl #32
    // 0x93d7c4: cmp             w0, NULL
    // 0x93d7c8: b.ne            #0x93d7d4
    // 0x93d7cc: r0 = Null
    //     0x93d7cc: mov             x0, NULL
    // 0x93d7d0: b               #0x93d81c
    // 0x93d7d4: LoadField: r4 = r0->field_23
    //     0x93d7d4: ldur            w4, [x0, #0x23]
    // 0x93d7d8: DecompressPointer r4
    //     0x93d7d8: add             x4, x4, HEAP, lsl #32
    // 0x93d7dc: cmp             w4, NULL
    // 0x93d7e0: b.ne            #0x93d7ec
    // 0x93d7e4: r0 = Null
    //     0x93d7e4: mov             x0, NULL
    // 0x93d7e8: b               #0x93d81c
    // 0x93d7ec: LoadField: r0 = r4->field_b
    //     0x93d7ec: ldur            w0, [x4, #0xb]
    // 0x93d7f0: r1 = LoadInt32Instr(r0)
    //     0x93d7f0: sbfx            x1, x0, #1, #0x1f
    // 0x93d7f4: mov             x0, x1
    // 0x93d7f8: r1 = 0
    //     0x93d7f8: movz            x1, #0
    // 0x93d7fc: cmp             x1, x0
    // 0x93d800: b.hs            #0x93d9ec
    // 0x93d804: LoadField: r0 = r4->field_f
    //     0x93d804: ldur            w0, [x4, #0xf]
    // 0x93d808: DecompressPointer r0
    //     0x93d808: add             x0, x0, HEAP, lsl #32
    // 0x93d80c: LoadField: r1 = r0->field_f
    //     0x93d80c: ldur            w1, [x0, #0xf]
    // 0x93d810: DecompressPointer r1
    //     0x93d810: add             x1, x1, HEAP, lsl #32
    // 0x93d814: LoadField: r0 = r1->field_b
    //     0x93d814: ldur            w0, [x1, #0xb]
    // 0x93d818: DecompressPointer r0
    //     0x93d818: add             x0, x0, HEAP, lsl #32
    // 0x93d81c: cmp             w0, NULL
    // 0x93d820: b.ne            #0x93d828
    // 0x93d824: r0 = ""
    //     0x93d824: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93d828: StoreField: r2->field_13 = r0
    //     0x93d828: stur            w0, [x2, #0x13]
    //     0x93d82c: ldurb           w16, [x2, #-1]
    //     0x93d830: ldurb           w17, [x0, #-1]
    //     0x93d834: and             x16, x17, x16, lsr #2
    //     0x93d838: tst             x16, HEAP, lsr #32
    //     0x93d83c: b.eq            #0x93d844
    //     0x93d840: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x93d844: LoadField: r0 = r3->field_f
    //     0x93d844: ldur            w0, [x3, #0xf]
    // 0x93d848: DecompressPointer r0
    //     0x93d848: add             x0, x0, HEAP, lsl #32
    // 0x93d84c: cmp             w0, NULL
    // 0x93d850: b.ne            #0x93d85c
    // 0x93d854: r0 = Null
    //     0x93d854: mov             x0, NULL
    // 0x93d858: b               #0x93d8a4
    // 0x93d85c: LoadField: r3 = r0->field_23
    //     0x93d85c: ldur            w3, [x0, #0x23]
    // 0x93d860: DecompressPointer r3
    //     0x93d860: add             x3, x3, HEAP, lsl #32
    // 0x93d864: cmp             w3, NULL
    // 0x93d868: b.ne            #0x93d874
    // 0x93d86c: r0 = Null
    //     0x93d86c: mov             x0, NULL
    // 0x93d870: b               #0x93d8a4
    // 0x93d874: LoadField: r0 = r3->field_b
    //     0x93d874: ldur            w0, [x3, #0xb]
    // 0x93d878: r1 = LoadInt32Instr(r0)
    //     0x93d878: sbfx            x1, x0, #1, #0x1f
    // 0x93d87c: mov             x0, x1
    // 0x93d880: r1 = 0
    //     0x93d880: movz            x1, #0
    // 0x93d884: cmp             x1, x0
    // 0x93d888: b.hs            #0x93d9f0
    // 0x93d88c: LoadField: r0 = r3->field_f
    //     0x93d88c: ldur            w0, [x3, #0xf]
    // 0x93d890: DecompressPointer r0
    //     0x93d890: add             x0, x0, HEAP, lsl #32
    // 0x93d894: LoadField: r1 = r0->field_f
    //     0x93d894: ldur            w1, [x0, #0xf]
    // 0x93d898: DecompressPointer r1
    //     0x93d898: add             x1, x1, HEAP, lsl #32
    // 0x93d89c: LoadField: r0 = r1->field_13
    //     0x93d89c: ldur            w0, [x1, #0x13]
    // 0x93d8a0: DecompressPointer r0
    //     0x93d8a0: add             x0, x0, HEAP, lsl #32
    // 0x93d8a4: cmp             w0, NULL
    // 0x93d8a8: b.ne            #0x93d8b4
    // 0x93d8ac: r1 = ""
    //     0x93d8ac: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93d8b0: b               #0x93d8b8
    // 0x93d8b4: mov             x1, x0
    // 0x93d8b8: mov             x0, x1
    // 0x93d8bc: stur            x1, [fp, #-8]
    // 0x93d8c0: ArrayStore: r2[0] = r0  ; List_4
    //     0x93d8c0: stur            w0, [x2, #0x17]
    //     0x93d8c4: ldurb           w16, [x2, #-1]
    //     0x93d8c8: ldurb           w17, [x0, #-1]
    //     0x93d8cc: and             x16, x17, x16, lsr #2
    //     0x93d8d0: tst             x16, HEAP, lsr #32
    //     0x93d8d4: b.eq            #0x93d8dc
    //     0x93d8d8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x93d8dc: r0 = current()
    //     0x93d8dc: bl              #0x62b7cc  ; [dart:io] IOOverrides::current
    // 0x93d8e0: r0 = _File()
    //     0x93d8e0: bl              #0x62f3ac  ; Allocate_FileStub -> _File (size=0x10)
    // 0x93d8e4: ldur            x1, [fp, #-8]
    // 0x93d8e8: stur            x0, [fp, #-0x18]
    // 0x93d8ec: StoreField: r0->field_7 = r1
    //     0x93d8ec: stur            w1, [x0, #7]
    // 0x93d8f0: r0 = _toUtf8Array()
    //     0x93d8f0: bl              #0x62b684  ; [dart:io] FileSystemEntity::_toUtf8Array
    // 0x93d8f4: ldur            x3, [fp, #-0x18]
    // 0x93d8f8: StoreField: r3->field_b = r0
    //     0x93d8f8: stur            w0, [x3, #0xb]
    //     0x93d8fc: ldurb           w16, [x3, #-1]
    //     0x93d900: ldurb           w17, [x0, #-1]
    //     0x93d904: and             x16, x17, x16, lsr #2
    //     0x93d908: tst             x16, HEAP, lsr #32
    //     0x93d90c: b.eq            #0x93d914
    //     0x93d910: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x93d914: ldur            x0, [fp, #-0x10]
    // 0x93d918: LoadField: r4 = r0->field_1b
    //     0x93d918: ldur            w4, [x0, #0x1b]
    // 0x93d91c: DecompressPointer r4
    //     0x93d91c: add             x4, x4, HEAP, lsl #32
    // 0x93d920: stur            x4, [fp, #-8]
    // 0x93d924: LoadField: r2 = r4->field_7
    //     0x93d924: ldur            w2, [x4, #7]
    // 0x93d928: DecompressPointer r2
    //     0x93d928: add             x2, x2, HEAP, lsl #32
    // 0x93d92c: mov             x0, x3
    // 0x93d930: r1 = Null
    //     0x93d930: mov             x1, NULL
    // 0x93d934: cmp             w2, NULL
    // 0x93d938: b.eq            #0x93d958
    // 0x93d93c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93d93c: ldur            w4, [x2, #0x17]
    // 0x93d940: DecompressPointer r4
    //     0x93d940: add             x4, x4, HEAP, lsl #32
    // 0x93d944: r8 = X0
    //     0x93d944: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x93d948: LoadField: r9 = r4->field_7
    //     0x93d948: ldur            x9, [x4, #7]
    // 0x93d94c: r3 = Null
    //     0x93d94c: add             x3, PP, #0x6a, lsl #12  ; [pp+0x6ab98] Null
    //     0x93d950: ldr             x3, [x3, #0xb98]
    // 0x93d954: blr             x9
    // 0x93d958: ldur            x0, [fp, #-8]
    // 0x93d95c: LoadField: r1 = r0->field_b
    //     0x93d95c: ldur            w1, [x0, #0xb]
    // 0x93d960: LoadField: r2 = r0->field_f
    //     0x93d960: ldur            w2, [x0, #0xf]
    // 0x93d964: DecompressPointer r2
    //     0x93d964: add             x2, x2, HEAP, lsl #32
    // 0x93d968: LoadField: r3 = r2->field_b
    //     0x93d968: ldur            w3, [x2, #0xb]
    // 0x93d96c: r2 = LoadInt32Instr(r1)
    //     0x93d96c: sbfx            x2, x1, #1, #0x1f
    // 0x93d970: stur            x2, [fp, #-0x20]
    // 0x93d974: r1 = LoadInt32Instr(r3)
    //     0x93d974: sbfx            x1, x3, #1, #0x1f
    // 0x93d978: cmp             x2, x1
    // 0x93d97c: b.ne            #0x93d988
    // 0x93d980: mov             x1, x0
    // 0x93d984: r0 = _growToNextCapacity()
    //     0x93d984: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93d988: ldur            x2, [fp, #-8]
    // 0x93d98c: ldur            x3, [fp, #-0x20]
    // 0x93d990: add             x4, x3, #1
    // 0x93d994: lsl             x5, x4, #1
    // 0x93d998: StoreField: r2->field_b = r5
    //     0x93d998: stur            w5, [x2, #0xb]
    // 0x93d99c: LoadField: r1 = r2->field_f
    //     0x93d99c: ldur            w1, [x2, #0xf]
    // 0x93d9a0: DecompressPointer r1
    //     0x93d9a0: add             x1, x1, HEAP, lsl #32
    // 0x93d9a4: ldur            x0, [fp, #-0x18]
    // 0x93d9a8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x93d9a8: add             x25, x1, x3, lsl #2
    //     0x93d9ac: add             x25, x25, #0xf
    //     0x93d9b0: str             w0, [x25]
    //     0x93d9b4: tbz             w0, #0, #0x93d9d0
    //     0x93d9b8: ldurb           w16, [x1, #-1]
    //     0x93d9bc: ldurb           w17, [x0, #-1]
    //     0x93d9c0: and             x16, x17, x16, lsr #2
    //     0x93d9c4: tst             x16, HEAP, lsr #32
    //     0x93d9c8: b.eq            #0x93d9d0
    //     0x93d9cc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93d9d0: r0 = Null
    //     0x93d9d0: mov             x0, NULL
    // 0x93d9d4: LeaveFrame
    //     0x93d9d4: mov             SP, fp
    //     0x93d9d8: ldp             fp, lr, [SP], #0x10
    // 0x93d9dc: ret
    //     0x93d9dc: ret             
    // 0x93d9e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93d9e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93d9e4: b               #0x93d7ac
    // 0x93d9e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93d9e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93d9ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93d9ec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93d9f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93d9f0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb2a670, size: 0x878
    // 0xb2a670: EnterFrame
    //     0xb2a670: stp             fp, lr, [SP, #-0x10]!
    //     0xb2a674: mov             fp, SP
    // 0xb2a678: AllocStack(0x68)
    //     0xb2a678: sub             SP, SP, #0x68
    // 0xb2a67c: SetupParameters(_BagImagesState this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xb2a67c: mov             x0, x2
    //     0xb2a680: stur            x2, [fp, #-0x18]
    //     0xb2a684: mov             x2, x1
    //     0xb2a688: stur            x1, [fp, #-0x10]
    // 0xb2a68c: CheckStackOverflow
    //     0xb2a68c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2a690: cmp             SP, x16
    //     0xb2a694: b.ls            #0xb2aec4
    // 0xb2a698: LoadField: r1 = r2->field_b
    //     0xb2a698: ldur            w1, [x2, #0xb]
    // 0xb2a69c: DecompressPointer r1
    //     0xb2a69c: add             x1, x1, HEAP, lsl #32
    // 0xb2a6a0: cmp             w1, NULL
    // 0xb2a6a4: b.eq            #0xb2aecc
    // 0xb2a6a8: LoadField: r3 = r1->field_b
    //     0xb2a6a8: ldur            w3, [x1, #0xb]
    // 0xb2a6ac: DecompressPointer r3
    //     0xb2a6ac: add             x3, x3, HEAP, lsl #32
    // 0xb2a6b0: cmp             w3, NULL
    // 0xb2a6b4: b.ne            #0xb2a6c0
    // 0xb2a6b8: r4 = Null
    //     0xb2a6b8: mov             x4, NULL
    // 0xb2a6bc: b               #0xb2a6ec
    // 0xb2a6c0: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xb2a6c0: ldur            w4, [x3, #0x17]
    // 0xb2a6c4: DecompressPointer r4
    //     0xb2a6c4: add             x4, x4, HEAP, lsl #32
    // 0xb2a6c8: cmp             w4, NULL
    // 0xb2a6cc: b.ne            #0xb2a6d8
    // 0xb2a6d0: r4 = Null
    //     0xb2a6d0: mov             x4, NULL
    // 0xb2a6d4: b               #0xb2a6ec
    // 0xb2a6d8: LoadField: r5 = r4->field_7
    //     0xb2a6d8: ldur            w5, [x4, #7]
    // 0xb2a6dc: cbnz            w5, #0xb2a6e8
    // 0xb2a6e0: r4 = false
    //     0xb2a6e0: add             x4, NULL, #0x30  ; false
    // 0xb2a6e4: b               #0xb2a6ec
    // 0xb2a6e8: r4 = true
    //     0xb2a6e8: add             x4, NULL, #0x20  ; true
    // 0xb2a6ec: cmp             w4, NULL
    // 0xb2a6f0: b.ne            #0xb2a748
    // 0xb2a6f4: mov             x4, x2
    // 0xb2a6f8: r10 = Instance_CrossAxisAlignment
    //     0xb2a6f8: add             x10, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb2a6fc: ldr             x10, [x10, #0x890]
    // 0xb2a700: r8 = Instance_MainAxisAlignment
    //     0xb2a700: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2a704: ldr             x8, [x8, #0xa08]
    // 0xb2a708: r2 = 6
    //     0xb2a708: movz            x2, #0x6
    // 0xb2a70c: r6 = 4
    //     0xb2a70c: movz            x6, #0x4
    // 0xb2a710: r3 = Instance_CrossAxisAlignment
    //     0xb2a710: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2a714: ldr             x3, [x3, #0xa18]
    // 0xb2a718: r9 = Instance_MainAxisSize
    //     0xb2a718: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2a71c: ldr             x9, [x9, #0xa10]
    // 0xb2a720: r0 = Instance_Axis
    //     0xb2a720: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2a724: r11 = Instance_VerticalDirection
    //     0xb2a724: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2a728: ldr             x11, [x11, #0xa20]
    // 0xb2a72c: r5 = Instance_Clip
    //     0xb2a72c: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb2a730: ldr             x5, [x5, #0x138]
    // 0xb2a734: r7 = Instance_Axis
    //     0xb2a734: ldr             x7, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2a738: r12 = Instance_Clip
    //     0xb2a738: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2a73c: ldr             x12, [x12, #0x38]
    // 0xb2a740: d0 = 10.000000
    //     0xb2a740: fmov            d0, #10.00000000
    // 0xb2a744: b               #0xb2ab54
    // 0xb2a748: tbnz            w4, #4, #0xb2ab04
    // 0xb2a74c: cmp             w3, NULL
    // 0xb2a750: b.ne            #0xb2a75c
    // 0xb2a754: r1 = Null
    //     0xb2a754: mov             x1, NULL
    // 0xb2a758: b               #0xb2a764
    // 0xb2a75c: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb2a75c: ldur            w1, [x3, #0x17]
    // 0xb2a760: DecompressPointer r1
    //     0xb2a760: add             x1, x1, HEAP, lsl #32
    // 0xb2a764: cmp             w1, NULL
    // 0xb2a768: b.ne            #0xb2a778
    // 0xb2a76c: r3 = " :"
    //     0xb2a76c: add             x3, PP, #0x40, lsl #12  ; [pp+0x407b8] " :"
    //     0xb2a770: ldr             x3, [x3, #0x7b8]
    // 0xb2a774: b               #0xb2a77c
    // 0xb2a778: mov             x3, x1
    // 0xb2a77c: mov             x1, x0
    // 0xb2a780: stur            x3, [fp, #-8]
    // 0xb2a784: r0 = of()
    //     0xb2a784: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2a788: LoadField: r1 = r0->field_87
    //     0xb2a788: ldur            w1, [x0, #0x87]
    // 0xb2a78c: DecompressPointer r1
    //     0xb2a78c: add             x1, x1, HEAP, lsl #32
    // 0xb2a790: LoadField: r0 = r1->field_2b
    //     0xb2a790: ldur            w0, [x1, #0x2b]
    // 0xb2a794: DecompressPointer r0
    //     0xb2a794: add             x0, x0, HEAP, lsl #32
    // 0xb2a798: stur            x0, [fp, #-0x20]
    // 0xb2a79c: r1 = Instance_Color
    //     0xb2a79c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2a7a0: d0 = 0.400000
    //     0xb2a7a0: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb2a7a4: r0 = withOpacity()
    //     0xb2a7a4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb2a7a8: r16 = 14.000000
    //     0xb2a7a8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2a7ac: ldr             x16, [x16, #0x1d8]
    // 0xb2a7b0: stp             x0, x16, [SP]
    // 0xb2a7b4: ldur            x1, [fp, #-0x20]
    // 0xb2a7b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2a7b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2a7bc: ldr             x4, [x4, #0xaa0]
    // 0xb2a7c0: r0 = copyWith()
    //     0xb2a7c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2a7c4: stur            x0, [fp, #-0x20]
    // 0xb2a7c8: r0 = Text()
    //     0xb2a7c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2a7cc: mov             x1, x0
    // 0xb2a7d0: ldur            x0, [fp, #-8]
    // 0xb2a7d4: stur            x1, [fp, #-0x28]
    // 0xb2a7d8: StoreField: r1->field_b = r0
    //     0xb2a7d8: stur            w0, [x1, #0xb]
    // 0xb2a7dc: ldur            x0, [fp, #-0x20]
    // 0xb2a7e0: StoreField: r1->field_13 = r0
    //     0xb2a7e0: stur            w0, [x1, #0x13]
    // 0xb2a7e4: ldur            x2, [fp, #-0x10]
    // 0xb2a7e8: LoadField: r0 = r2->field_b
    //     0xb2a7e8: ldur            w0, [x2, #0xb]
    // 0xb2a7ec: DecompressPointer r0
    //     0xb2a7ec: add             x0, x0, HEAP, lsl #32
    // 0xb2a7f0: cmp             w0, NULL
    // 0xb2a7f4: b.eq            #0xb2aed0
    // 0xb2a7f8: LoadField: r3 = r0->field_b
    //     0xb2a7f8: ldur            w3, [x0, #0xb]
    // 0xb2a7fc: DecompressPointer r3
    //     0xb2a7fc: add             x3, x3, HEAP, lsl #32
    // 0xb2a800: cmp             w3, NULL
    // 0xb2a804: b.ne            #0xb2a810
    // 0xb2a808: r0 = Null
    //     0xb2a808: mov             x0, NULL
    // 0xb2a80c: b               #0xb2a83c
    // 0xb2a810: LoadField: r0 = r3->field_2b
    //     0xb2a810: ldur            w0, [x3, #0x2b]
    // 0xb2a814: DecompressPointer r0
    //     0xb2a814: add             x0, x0, HEAP, lsl #32
    // 0xb2a818: r3 = LoadClassIdInstr(r0)
    //     0xb2a818: ldur            x3, [x0, #-1]
    //     0xb2a81c: ubfx            x3, x3, #0xc, #0x14
    // 0xb2a820: str             x0, [SP]
    // 0xb2a824: mov             x0, x3
    // 0xb2a828: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb2a828: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb2a82c: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb2a82c: movz            x17, #0x2700
    //     0xb2a830: add             lr, x0, x17
    //     0xb2a834: ldr             lr, [x21, lr, lsl #3]
    //     0xb2a838: blr             lr
    // 0xb2a83c: cmp             w0, NULL
    // 0xb2a840: b.ne            #0xb2a84c
    // 0xb2a844: r3 = " "
    //     0xb2a844: ldr             x3, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb2a848: b               #0xb2a850
    // 0xb2a84c: mov             x3, x0
    // 0xb2a850: ldur            x2, [fp, #-0x10]
    // 0xb2a854: ldur            x0, [fp, #-0x28]
    // 0xb2a858: ldur            x1, [fp, #-0x18]
    // 0xb2a85c: stur            x3, [fp, #-8]
    // 0xb2a860: r0 = of()
    //     0xb2a860: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2a864: LoadField: r1 = r0->field_87
    //     0xb2a864: ldur            w1, [x0, #0x87]
    // 0xb2a868: DecompressPointer r1
    //     0xb2a868: add             x1, x1, HEAP, lsl #32
    // 0xb2a86c: LoadField: r0 = r1->field_2b
    //     0xb2a86c: ldur            w0, [x1, #0x2b]
    // 0xb2a870: DecompressPointer r0
    //     0xb2a870: add             x0, x0, HEAP, lsl #32
    // 0xb2a874: r16 = 14.000000
    //     0xb2a874: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2a878: ldr             x16, [x16, #0x1d8]
    // 0xb2a87c: r30 = Instance_Color
    //     0xb2a87c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2a880: stp             lr, x16, [SP]
    // 0xb2a884: mov             x1, x0
    // 0xb2a888: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2a888: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2a88c: ldr             x4, [x4, #0xaa0]
    // 0xb2a890: r0 = copyWith()
    //     0xb2a890: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2a894: stur            x0, [fp, #-0x20]
    // 0xb2a898: r0 = Text()
    //     0xb2a898: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2a89c: mov             x3, x0
    // 0xb2a8a0: ldur            x0, [fp, #-8]
    // 0xb2a8a4: stur            x3, [fp, #-0x30]
    // 0xb2a8a8: StoreField: r3->field_b = r0
    //     0xb2a8a8: stur            w0, [x3, #0xb]
    // 0xb2a8ac: ldur            x0, [fp, #-0x20]
    // 0xb2a8b0: StoreField: r3->field_13 = r0
    //     0xb2a8b0: stur            w0, [x3, #0x13]
    // 0xb2a8b4: r1 = Null
    //     0xb2a8b4: mov             x1, NULL
    // 0xb2a8b8: r2 = 6
    //     0xb2a8b8: movz            x2, #0x6
    // 0xb2a8bc: r0 = AllocateArray()
    //     0xb2a8bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2a8c0: mov             x2, x0
    // 0xb2a8c4: ldur            x0, [fp, #-0x28]
    // 0xb2a8c8: stur            x2, [fp, #-8]
    // 0xb2a8cc: StoreField: r2->field_f = r0
    //     0xb2a8cc: stur            w0, [x2, #0xf]
    // 0xb2a8d0: r16 = Instance_Spacer
    //     0xb2a8d0: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb2a8d4: ldr             x16, [x16, #0xf0]
    // 0xb2a8d8: StoreField: r2->field_13 = r16
    //     0xb2a8d8: stur            w16, [x2, #0x13]
    // 0xb2a8dc: ldur            x0, [fp, #-0x30]
    // 0xb2a8e0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2a8e0: stur            w0, [x2, #0x17]
    // 0xb2a8e4: r1 = <Widget>
    //     0xb2a8e4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2a8e8: r0 = AllocateGrowableArray()
    //     0xb2a8e8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2a8ec: mov             x1, x0
    // 0xb2a8f0: ldur            x0, [fp, #-8]
    // 0xb2a8f4: stur            x1, [fp, #-0x20]
    // 0xb2a8f8: StoreField: r1->field_f = r0
    //     0xb2a8f8: stur            w0, [x1, #0xf]
    // 0xb2a8fc: r2 = 6
    //     0xb2a8fc: movz            x2, #0x6
    // 0xb2a900: StoreField: r1->field_b = r2
    //     0xb2a900: stur            w2, [x1, #0xb]
    // 0xb2a904: r0 = Row()
    //     0xb2a904: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2a908: mov             x1, x0
    // 0xb2a90c: r0 = Instance_Axis
    //     0xb2a90c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2a910: stur            x1, [fp, #-8]
    // 0xb2a914: StoreField: r1->field_f = r0
    //     0xb2a914: stur            w0, [x1, #0xf]
    // 0xb2a918: r0 = Instance_MainAxisAlignment
    //     0xb2a918: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2a91c: ldr             x0, [x0, #0xa08]
    // 0xb2a920: StoreField: r1->field_13 = r0
    //     0xb2a920: stur            w0, [x1, #0x13]
    // 0xb2a924: r2 = Instance_MainAxisSize
    //     0xb2a924: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2a928: ldr             x2, [x2, #0xa10]
    // 0xb2a92c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb2a92c: stur            w2, [x1, #0x17]
    // 0xb2a930: r3 = Instance_CrossAxisAlignment
    //     0xb2a930: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2a934: ldr             x3, [x3, #0xa18]
    // 0xb2a938: StoreField: r1->field_1b = r3
    //     0xb2a938: stur            w3, [x1, #0x1b]
    // 0xb2a93c: r3 = Instance_VerticalDirection
    //     0xb2a93c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2a940: ldr             x3, [x3, #0xa20]
    // 0xb2a944: StoreField: r1->field_23 = r3
    //     0xb2a944: stur            w3, [x1, #0x23]
    // 0xb2a948: r4 = Instance_Clip
    //     0xb2a948: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2a94c: ldr             x4, [x4, #0x38]
    // 0xb2a950: StoreField: r1->field_2b = r4
    //     0xb2a950: stur            w4, [x1, #0x2b]
    // 0xb2a954: StoreField: r1->field_2f = rZR
    //     0xb2a954: stur            xzr, [x1, #0x2f]
    // 0xb2a958: ldur            x5, [fp, #-0x20]
    // 0xb2a95c: StoreField: r1->field_b = r5
    //     0xb2a95c: stur            w5, [x1, #0xb]
    // 0xb2a960: r0 = Radius()
    //     0xb2a960: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb2a964: d0 = 10.000000
    //     0xb2a964: fmov            d0, #10.00000000
    // 0xb2a968: stur            x0, [fp, #-0x20]
    // 0xb2a96c: StoreField: r0->field_7 = d0
    //     0xb2a96c: stur            d0, [x0, #7]
    // 0xb2a970: StoreField: r0->field_f = d0
    //     0xb2a970: stur            d0, [x0, #0xf]
    // 0xb2a974: r0 = BorderRadius()
    //     0xb2a974: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb2a978: mov             x3, x0
    // 0xb2a97c: ldur            x0, [fp, #-0x20]
    // 0xb2a980: stur            x3, [fp, #-0x28]
    // 0xb2a984: StoreField: r3->field_7 = r0
    //     0xb2a984: stur            w0, [x3, #7]
    // 0xb2a988: StoreField: r3->field_b = r0
    //     0xb2a988: stur            w0, [x3, #0xb]
    // 0xb2a98c: StoreField: r3->field_f = r0
    //     0xb2a98c: stur            w0, [x3, #0xf]
    // 0xb2a990: StoreField: r3->field_13 = r0
    //     0xb2a990: stur            w0, [x3, #0x13]
    // 0xb2a994: ldur            x4, [fp, #-0x10]
    // 0xb2a998: LoadField: r0 = r4->field_13
    //     0xb2a998: ldur            w0, [x4, #0x13]
    // 0xb2a99c: DecompressPointer r0
    //     0xb2a99c: add             x0, x0, HEAP, lsl #32
    // 0xb2a9a0: r16 = Sentinel
    //     0xb2a9a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb2a9a4: cmp             w0, w16
    // 0xb2a9a8: b.eq            #0xb2aed4
    // 0xb2a9ac: stur            x0, [fp, #-0x20]
    // 0xb2a9b0: r1 = Function '<anonymous closure>':.
    //     0xb2a9b0: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ab80] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb2a9b4: ldr             x1, [x1, #0xb80]
    // 0xb2a9b8: r2 = Null
    //     0xb2a9b8: mov             x2, NULL
    // 0xb2a9bc: r0 = AllocateClosure()
    //     0xb2a9bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2a9c0: r1 = Function '<anonymous closure>':.
    //     0xb2a9c0: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ab88] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb2a9c4: ldr             x1, [x1, #0xb88]
    // 0xb2a9c8: r2 = Null
    //     0xb2a9c8: mov             x2, NULL
    // 0xb2a9cc: stur            x0, [fp, #-0x30]
    // 0xb2a9d0: r0 = AllocateClosure()
    //     0xb2a9d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2a9d4: stur            x0, [fp, #-0x38]
    // 0xb2a9d8: r0 = CachedNetworkImage()
    //     0xb2a9d8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb2a9dc: stur            x0, [fp, #-0x40]
    // 0xb2a9e0: r16 = 40.000000
    //     0xb2a9e0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb2a9e4: ldr             x16, [x16, #8]
    // 0xb2a9e8: r30 = 40.000000
    //     0xb2a9e8: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb2a9ec: ldr             lr, [lr, #8]
    // 0xb2a9f0: stp             lr, x16, [SP, #0x18]
    // 0xb2a9f4: r16 = Instance_BoxFit
    //     0xb2a9f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb2a9f8: ldr             x16, [x16, #0x118]
    // 0xb2a9fc: ldur            lr, [fp, #-0x30]
    // 0xb2aa00: stp             lr, x16, [SP, #8]
    // 0xb2aa04: ldur            x16, [fp, #-0x38]
    // 0xb2aa08: str             x16, [SP]
    // 0xb2aa0c: mov             x1, x0
    // 0xb2aa10: ldur            x2, [fp, #-0x20]
    // 0xb2aa14: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xb2aa14: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xb2aa18: ldr             x4, [x4, #0xc28]
    // 0xb2aa1c: r0 = CachedNetworkImage()
    //     0xb2aa1c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb2aa20: r0 = ClipRRect()
    //     0xb2aa20: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb2aa24: mov             x1, x0
    // 0xb2aa28: ldur            x0, [fp, #-0x28]
    // 0xb2aa2c: stur            x1, [fp, #-0x20]
    // 0xb2aa30: StoreField: r1->field_f = r0
    //     0xb2aa30: stur            w0, [x1, #0xf]
    // 0xb2aa34: r5 = Instance_Clip
    //     0xb2aa34: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb2aa38: ldr             x5, [x5, #0x138]
    // 0xb2aa3c: ArrayStore: r1[0] = r5  ; List_4
    //     0xb2aa3c: stur            w5, [x1, #0x17]
    // 0xb2aa40: ldur            x0, [fp, #-0x40]
    // 0xb2aa44: StoreField: r1->field_b = r0
    //     0xb2aa44: stur            w0, [x1, #0xb]
    // 0xb2aa48: r0 = Padding()
    //     0xb2aa48: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2aa4c: mov             x3, x0
    // 0xb2aa50: r0 = Instance_EdgeInsets
    //     0xb2aa50: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb2aa54: ldr             x0, [x0, #0x668]
    // 0xb2aa58: stur            x3, [fp, #-0x28]
    // 0xb2aa5c: StoreField: r3->field_f = r0
    //     0xb2aa5c: stur            w0, [x3, #0xf]
    // 0xb2aa60: ldur            x0, [fp, #-0x20]
    // 0xb2aa64: StoreField: r3->field_b = r0
    //     0xb2aa64: stur            w0, [x3, #0xb]
    // 0xb2aa68: r1 = Null
    //     0xb2aa68: mov             x1, NULL
    // 0xb2aa6c: r2 = 4
    //     0xb2aa6c: movz            x2, #0x4
    // 0xb2aa70: r0 = AllocateArray()
    //     0xb2aa70: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2aa74: mov             x2, x0
    // 0xb2aa78: ldur            x0, [fp, #-8]
    // 0xb2aa7c: stur            x2, [fp, #-0x20]
    // 0xb2aa80: StoreField: r2->field_f = r0
    //     0xb2aa80: stur            w0, [x2, #0xf]
    // 0xb2aa84: ldur            x0, [fp, #-0x28]
    // 0xb2aa88: StoreField: r2->field_13 = r0
    //     0xb2aa88: stur            w0, [x2, #0x13]
    // 0xb2aa8c: r1 = <Widget>
    //     0xb2aa8c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2aa90: r0 = AllocateGrowableArray()
    //     0xb2aa90: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2aa94: mov             x1, x0
    // 0xb2aa98: ldur            x0, [fp, #-0x20]
    // 0xb2aa9c: stur            x1, [fp, #-8]
    // 0xb2aaa0: StoreField: r1->field_f = r0
    //     0xb2aaa0: stur            w0, [x1, #0xf]
    // 0xb2aaa4: r6 = 4
    //     0xb2aaa4: movz            x6, #0x4
    // 0xb2aaa8: StoreField: r1->field_b = r6
    //     0xb2aaa8: stur            w6, [x1, #0xb]
    // 0xb2aaac: r0 = Column()
    //     0xb2aaac: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb2aab0: r7 = Instance_Axis
    //     0xb2aab0: ldr             x7, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2aab4: StoreField: r0->field_f = r7
    //     0xb2aab4: stur            w7, [x0, #0xf]
    // 0xb2aab8: r8 = Instance_MainAxisAlignment
    //     0xb2aab8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2aabc: ldr             x8, [x8, #0xa08]
    // 0xb2aac0: StoreField: r0->field_13 = r8
    //     0xb2aac0: stur            w8, [x0, #0x13]
    // 0xb2aac4: r9 = Instance_MainAxisSize
    //     0xb2aac4: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2aac8: ldr             x9, [x9, #0xa10]
    // 0xb2aacc: ArrayStore: r0[0] = r9  ; List_4
    //     0xb2aacc: stur            w9, [x0, #0x17]
    // 0xb2aad0: r10 = Instance_CrossAxisAlignment
    //     0xb2aad0: add             x10, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb2aad4: ldr             x10, [x10, #0x890]
    // 0xb2aad8: StoreField: r0->field_1b = r10
    //     0xb2aad8: stur            w10, [x0, #0x1b]
    // 0xb2aadc: r11 = Instance_VerticalDirection
    //     0xb2aadc: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2aae0: ldr             x11, [x11, #0xa20]
    // 0xb2aae4: StoreField: r0->field_23 = r11
    //     0xb2aae4: stur            w11, [x0, #0x23]
    // 0xb2aae8: r12 = Instance_Clip
    //     0xb2aae8: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2aaec: ldr             x12, [x12, #0x38]
    // 0xb2aaf0: StoreField: r0->field_2b = r12
    //     0xb2aaf0: stur            w12, [x0, #0x2b]
    // 0xb2aaf4: StoreField: r0->field_2f = rZR
    //     0xb2aaf4: stur            xzr, [x0, #0x2f]
    // 0xb2aaf8: ldur            x1, [fp, #-8]
    // 0xb2aafc: StoreField: r0->field_b = r1
    //     0xb2aafc: stur            w1, [x0, #0xb]
    // 0xb2ab00: b               #0xb2aeb8
    // 0xb2ab04: mov             x4, x2
    // 0xb2ab08: r10 = Instance_CrossAxisAlignment
    //     0xb2ab08: add             x10, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb2ab0c: ldr             x10, [x10, #0x890]
    // 0xb2ab10: r8 = Instance_MainAxisAlignment
    //     0xb2ab10: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2ab14: ldr             x8, [x8, #0xa08]
    // 0xb2ab18: r2 = 6
    //     0xb2ab18: movz            x2, #0x6
    // 0xb2ab1c: r6 = 4
    //     0xb2ab1c: movz            x6, #0x4
    // 0xb2ab20: r3 = Instance_CrossAxisAlignment
    //     0xb2ab20: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2ab24: ldr             x3, [x3, #0xa18]
    // 0xb2ab28: r9 = Instance_MainAxisSize
    //     0xb2ab28: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2ab2c: ldr             x9, [x9, #0xa10]
    // 0xb2ab30: r0 = Instance_Axis
    //     0xb2ab30: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2ab34: r11 = Instance_VerticalDirection
    //     0xb2ab34: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2ab38: ldr             x11, [x11, #0xa20]
    // 0xb2ab3c: r5 = Instance_Clip
    //     0xb2ab3c: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb2ab40: ldr             x5, [x5, #0x138]
    // 0xb2ab44: r7 = Instance_Axis
    //     0xb2ab44: ldr             x7, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2ab48: r12 = Instance_Clip
    //     0xb2ab48: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2ab4c: ldr             x12, [x12, #0x38]
    // 0xb2ab50: d0 = 10.000000
    //     0xb2ab50: fmov            d0, #10.00000000
    // 0xb2ab54: LoadField: r13 = r1->field_f
    //     0xb2ab54: ldur            w13, [x1, #0xf]
    // 0xb2ab58: DecompressPointer r13
    //     0xb2ab58: add             x13, x13, HEAP, lsl #32
    // 0xb2ab5c: cmp             w13, NULL
    // 0xb2ab60: b.ne            #0xb2ab6c
    // 0xb2ab64: r1 = Null
    //     0xb2ab64: mov             x1, NULL
    // 0xb2ab68: b               #0xb2ab74
    // 0xb2ab6c: ArrayLoad: r1 = r13[0]  ; List_4
    //     0xb2ab6c: ldur            w1, [x13, #0x17]
    // 0xb2ab70: DecompressPointer r1
    //     0xb2ab70: add             x1, x1, HEAP, lsl #32
    // 0xb2ab74: cmp             w1, NULL
    // 0xb2ab78: b.ne            #0xb2ab88
    // 0xb2ab7c: r13 = " :"
    //     0xb2ab7c: add             x13, PP, #0x40, lsl #12  ; [pp+0x407b8] " :"
    //     0xb2ab80: ldr             x13, [x13, #0x7b8]
    // 0xb2ab84: b               #0xb2ab8c
    // 0xb2ab88: mov             x13, x1
    // 0xb2ab8c: ldur            x1, [fp, #-0x18]
    // 0xb2ab90: stur            x13, [fp, #-8]
    // 0xb2ab94: r0 = of()
    //     0xb2ab94: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2ab98: LoadField: r1 = r0->field_87
    //     0xb2ab98: ldur            w1, [x0, #0x87]
    // 0xb2ab9c: DecompressPointer r1
    //     0xb2ab9c: add             x1, x1, HEAP, lsl #32
    // 0xb2aba0: LoadField: r0 = r1->field_2b
    //     0xb2aba0: ldur            w0, [x1, #0x2b]
    // 0xb2aba4: DecompressPointer r0
    //     0xb2aba4: add             x0, x0, HEAP, lsl #32
    // 0xb2aba8: stur            x0, [fp, #-0x20]
    // 0xb2abac: r1 = Instance_Color
    //     0xb2abac: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2abb0: d0 = 0.400000
    //     0xb2abb0: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb2abb4: r0 = withOpacity()
    //     0xb2abb4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb2abb8: r16 = 14.000000
    //     0xb2abb8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2abbc: ldr             x16, [x16, #0x1d8]
    // 0xb2abc0: stp             x0, x16, [SP]
    // 0xb2abc4: ldur            x1, [fp, #-0x20]
    // 0xb2abc8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2abc8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2abcc: ldr             x4, [x4, #0xaa0]
    // 0xb2abd0: r0 = copyWith()
    //     0xb2abd0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2abd4: stur            x0, [fp, #-0x20]
    // 0xb2abd8: r0 = Text()
    //     0xb2abd8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2abdc: mov             x1, x0
    // 0xb2abe0: ldur            x0, [fp, #-8]
    // 0xb2abe4: stur            x1, [fp, #-0x28]
    // 0xb2abe8: StoreField: r1->field_b = r0
    //     0xb2abe8: stur            w0, [x1, #0xb]
    // 0xb2abec: ldur            x0, [fp, #-0x20]
    // 0xb2abf0: StoreField: r1->field_13 = r0
    //     0xb2abf0: stur            w0, [x1, #0x13]
    // 0xb2abf4: ldur            x2, [fp, #-0x10]
    // 0xb2abf8: LoadField: r0 = r2->field_b
    //     0xb2abf8: ldur            w0, [x2, #0xb]
    // 0xb2abfc: DecompressPointer r0
    //     0xb2abfc: add             x0, x0, HEAP, lsl #32
    // 0xb2ac00: cmp             w0, NULL
    // 0xb2ac04: b.eq            #0xb2aee0
    // 0xb2ac08: LoadField: r3 = r0->field_f
    //     0xb2ac08: ldur            w3, [x0, #0xf]
    // 0xb2ac0c: DecompressPointer r3
    //     0xb2ac0c: add             x3, x3, HEAP, lsl #32
    // 0xb2ac10: cmp             w3, NULL
    // 0xb2ac14: b.ne            #0xb2ac20
    // 0xb2ac18: r0 = Null
    //     0xb2ac18: mov             x0, NULL
    // 0xb2ac1c: b               #0xb2ac4c
    // 0xb2ac20: LoadField: r0 = r3->field_2b
    //     0xb2ac20: ldur            w0, [x3, #0x2b]
    // 0xb2ac24: DecompressPointer r0
    //     0xb2ac24: add             x0, x0, HEAP, lsl #32
    // 0xb2ac28: r3 = LoadClassIdInstr(r0)
    //     0xb2ac28: ldur            x3, [x0, #-1]
    //     0xb2ac2c: ubfx            x3, x3, #0xc, #0x14
    // 0xb2ac30: str             x0, [SP]
    // 0xb2ac34: mov             x0, x3
    // 0xb2ac38: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb2ac38: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb2ac3c: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb2ac3c: movz            x17, #0x2700
    //     0xb2ac40: add             lr, x0, x17
    //     0xb2ac44: ldr             lr, [x21, lr, lsl #3]
    //     0xb2ac48: blr             lr
    // 0xb2ac4c: cmp             w0, NULL
    // 0xb2ac50: b.ne            #0xb2ac5c
    // 0xb2ac54: r3 = " "
    //     0xb2ac54: ldr             x3, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb2ac58: b               #0xb2ac60
    // 0xb2ac5c: mov             x3, x0
    // 0xb2ac60: ldur            x2, [fp, #-0x10]
    // 0xb2ac64: ldur            x0, [fp, #-0x28]
    // 0xb2ac68: ldur            x1, [fp, #-0x18]
    // 0xb2ac6c: stur            x3, [fp, #-8]
    // 0xb2ac70: r0 = of()
    //     0xb2ac70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2ac74: LoadField: r1 = r0->field_87
    //     0xb2ac74: ldur            w1, [x0, #0x87]
    // 0xb2ac78: DecompressPointer r1
    //     0xb2ac78: add             x1, x1, HEAP, lsl #32
    // 0xb2ac7c: LoadField: r0 = r1->field_2b
    //     0xb2ac7c: ldur            w0, [x1, #0x2b]
    // 0xb2ac80: DecompressPointer r0
    //     0xb2ac80: add             x0, x0, HEAP, lsl #32
    // 0xb2ac84: r16 = 14.000000
    //     0xb2ac84: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2ac88: ldr             x16, [x16, #0x1d8]
    // 0xb2ac8c: r30 = Instance_Color
    //     0xb2ac8c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2ac90: stp             lr, x16, [SP]
    // 0xb2ac94: mov             x1, x0
    // 0xb2ac98: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2ac98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2ac9c: ldr             x4, [x4, #0xaa0]
    // 0xb2aca0: r0 = copyWith()
    //     0xb2aca0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2aca4: stur            x0, [fp, #-0x18]
    // 0xb2aca8: r0 = Text()
    //     0xb2aca8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2acac: mov             x3, x0
    // 0xb2acb0: ldur            x0, [fp, #-8]
    // 0xb2acb4: stur            x3, [fp, #-0x20]
    // 0xb2acb8: StoreField: r3->field_b = r0
    //     0xb2acb8: stur            w0, [x3, #0xb]
    // 0xb2acbc: ldur            x0, [fp, #-0x18]
    // 0xb2acc0: StoreField: r3->field_13 = r0
    //     0xb2acc0: stur            w0, [x3, #0x13]
    // 0xb2acc4: r1 = Null
    //     0xb2acc4: mov             x1, NULL
    // 0xb2acc8: r2 = 6
    //     0xb2acc8: movz            x2, #0x6
    // 0xb2accc: r0 = AllocateArray()
    //     0xb2accc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2acd0: mov             x2, x0
    // 0xb2acd4: ldur            x0, [fp, #-0x28]
    // 0xb2acd8: stur            x2, [fp, #-8]
    // 0xb2acdc: StoreField: r2->field_f = r0
    //     0xb2acdc: stur            w0, [x2, #0xf]
    // 0xb2ace0: r16 = Instance_Spacer
    //     0xb2ace0: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb2ace4: ldr             x16, [x16, #0xf0]
    // 0xb2ace8: StoreField: r2->field_13 = r16
    //     0xb2ace8: stur            w16, [x2, #0x13]
    // 0xb2acec: ldur            x0, [fp, #-0x20]
    // 0xb2acf0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2acf0: stur            w0, [x2, #0x17]
    // 0xb2acf4: r1 = <Widget>
    //     0xb2acf4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2acf8: r0 = AllocateGrowableArray()
    //     0xb2acf8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2acfc: mov             x1, x0
    // 0xb2ad00: ldur            x0, [fp, #-8]
    // 0xb2ad04: stur            x1, [fp, #-0x18]
    // 0xb2ad08: StoreField: r1->field_f = r0
    //     0xb2ad08: stur            w0, [x1, #0xf]
    // 0xb2ad0c: r0 = 6
    //     0xb2ad0c: movz            x0, #0x6
    // 0xb2ad10: StoreField: r1->field_b = r0
    //     0xb2ad10: stur            w0, [x1, #0xb]
    // 0xb2ad14: r0 = Row()
    //     0xb2ad14: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2ad18: mov             x1, x0
    // 0xb2ad1c: r0 = Instance_Axis
    //     0xb2ad1c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2ad20: stur            x1, [fp, #-8]
    // 0xb2ad24: StoreField: r1->field_f = r0
    //     0xb2ad24: stur            w0, [x1, #0xf]
    // 0xb2ad28: r0 = Instance_MainAxisAlignment
    //     0xb2ad28: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2ad2c: ldr             x0, [x0, #0xa08]
    // 0xb2ad30: StoreField: r1->field_13 = r0
    //     0xb2ad30: stur            w0, [x1, #0x13]
    // 0xb2ad34: r2 = Instance_MainAxisSize
    //     0xb2ad34: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2ad38: ldr             x2, [x2, #0xa10]
    // 0xb2ad3c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb2ad3c: stur            w2, [x1, #0x17]
    // 0xb2ad40: r3 = Instance_CrossAxisAlignment
    //     0xb2ad40: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2ad44: ldr             x3, [x3, #0xa18]
    // 0xb2ad48: StoreField: r1->field_1b = r3
    //     0xb2ad48: stur            w3, [x1, #0x1b]
    // 0xb2ad4c: r3 = Instance_VerticalDirection
    //     0xb2ad4c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2ad50: ldr             x3, [x3, #0xa20]
    // 0xb2ad54: StoreField: r1->field_23 = r3
    //     0xb2ad54: stur            w3, [x1, #0x23]
    // 0xb2ad58: r4 = Instance_Clip
    //     0xb2ad58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2ad5c: ldr             x4, [x4, #0x38]
    // 0xb2ad60: StoreField: r1->field_2b = r4
    //     0xb2ad60: stur            w4, [x1, #0x2b]
    // 0xb2ad64: StoreField: r1->field_2f = rZR
    //     0xb2ad64: stur            xzr, [x1, #0x2f]
    // 0xb2ad68: ldur            x5, [fp, #-0x18]
    // 0xb2ad6c: StoreField: r1->field_b = r5
    //     0xb2ad6c: stur            w5, [x1, #0xb]
    // 0xb2ad70: r0 = Radius()
    //     0xb2ad70: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb2ad74: d0 = 10.000000
    //     0xb2ad74: fmov            d0, #10.00000000
    // 0xb2ad78: stur            x0, [fp, #-0x18]
    // 0xb2ad7c: StoreField: r0->field_7 = d0
    //     0xb2ad7c: stur            d0, [x0, #7]
    // 0xb2ad80: StoreField: r0->field_f = d0
    //     0xb2ad80: stur            d0, [x0, #0xf]
    // 0xb2ad84: r0 = BorderRadius()
    //     0xb2ad84: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb2ad88: mov             x2, x0
    // 0xb2ad8c: ldur            x0, [fp, #-0x18]
    // 0xb2ad90: stur            x2, [fp, #-0x20]
    // 0xb2ad94: StoreField: r2->field_7 = r0
    //     0xb2ad94: stur            w0, [x2, #7]
    // 0xb2ad98: StoreField: r2->field_b = r0
    //     0xb2ad98: stur            w0, [x2, #0xb]
    // 0xb2ad9c: StoreField: r2->field_f = r0
    //     0xb2ad9c: stur            w0, [x2, #0xf]
    // 0xb2ada0: StoreField: r2->field_13 = r0
    //     0xb2ada0: stur            w0, [x2, #0x13]
    // 0xb2ada4: ldur            x0, [fp, #-0x10]
    // 0xb2ada8: LoadField: r3 = r0->field_1b
    //     0xb2ada8: ldur            w3, [x0, #0x1b]
    // 0xb2adac: DecompressPointer r3
    //     0xb2adac: add             x3, x3, HEAP, lsl #32
    // 0xb2adb0: LoadField: r0 = r3->field_b
    //     0xb2adb0: ldur            w0, [x3, #0xb]
    // 0xb2adb4: r1 = LoadInt32Instr(r0)
    //     0xb2adb4: sbfx            x1, x0, #1, #0x1f
    // 0xb2adb8: mov             x0, x1
    // 0xb2adbc: r1 = 0
    //     0xb2adbc: movz            x1, #0
    // 0xb2adc0: cmp             x1, x0
    // 0xb2adc4: b.hs            #0xb2aee4
    // 0xb2adc8: LoadField: r0 = r3->field_f
    //     0xb2adc8: ldur            w0, [x3, #0xf]
    // 0xb2adcc: DecompressPointer r0
    //     0xb2adcc: add             x0, x0, HEAP, lsl #32
    // 0xb2add0: LoadField: r1 = r0->field_f
    //     0xb2add0: ldur            w1, [x0, #0xf]
    // 0xb2add4: DecompressPointer r1
    //     0xb2add4: add             x1, x1, HEAP, lsl #32
    // 0xb2add8: stur            x1, [fp, #-0x10]
    // 0xb2addc: r0 = Image()
    //     0xb2addc: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0xb2ade0: mov             x1, x0
    // 0xb2ade4: ldur            x2, [fp, #-0x10]
    // 0xb2ade8: d0 = 40.000000
    //     0xb2ade8: ldr             d0, [PP, #0x5a38]  ; [pp+0x5a38] IMM: double(40) from 0x4044000000000000
    // 0xb2adec: d1 = 40.000000
    //     0xb2adec: ldr             d1, [PP, #0x5a38]  ; [pp+0x5a38] IMM: double(40) from 0x4044000000000000
    // 0xb2adf0: stur            x0, [fp, #-0x10]
    // 0xb2adf4: r0 = Image.file()
    //     0xb2adf4: bl              #0x9d34d0  ; [package:flutter/src/widgets/image.dart] Image::Image.file
    // 0xb2adf8: r0 = ClipRRect()
    //     0xb2adf8: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb2adfc: mov             x3, x0
    // 0xb2ae00: ldur            x0, [fp, #-0x20]
    // 0xb2ae04: stur            x3, [fp, #-0x18]
    // 0xb2ae08: StoreField: r3->field_f = r0
    //     0xb2ae08: stur            w0, [x3, #0xf]
    // 0xb2ae0c: r0 = Instance_Clip
    //     0xb2ae0c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb2ae10: ldr             x0, [x0, #0x138]
    // 0xb2ae14: ArrayStore: r3[0] = r0  ; List_4
    //     0xb2ae14: stur            w0, [x3, #0x17]
    // 0xb2ae18: ldur            x0, [fp, #-0x10]
    // 0xb2ae1c: StoreField: r3->field_b = r0
    //     0xb2ae1c: stur            w0, [x3, #0xb]
    // 0xb2ae20: r1 = Null
    //     0xb2ae20: mov             x1, NULL
    // 0xb2ae24: r2 = 4
    //     0xb2ae24: movz            x2, #0x4
    // 0xb2ae28: r0 = AllocateArray()
    //     0xb2ae28: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2ae2c: mov             x2, x0
    // 0xb2ae30: ldur            x0, [fp, #-8]
    // 0xb2ae34: stur            x2, [fp, #-0x10]
    // 0xb2ae38: StoreField: r2->field_f = r0
    //     0xb2ae38: stur            w0, [x2, #0xf]
    // 0xb2ae3c: ldur            x0, [fp, #-0x18]
    // 0xb2ae40: StoreField: r2->field_13 = r0
    //     0xb2ae40: stur            w0, [x2, #0x13]
    // 0xb2ae44: r1 = <Widget>
    //     0xb2ae44: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2ae48: r0 = AllocateGrowableArray()
    //     0xb2ae48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2ae4c: mov             x1, x0
    // 0xb2ae50: ldur            x0, [fp, #-0x10]
    // 0xb2ae54: stur            x1, [fp, #-8]
    // 0xb2ae58: StoreField: r1->field_f = r0
    //     0xb2ae58: stur            w0, [x1, #0xf]
    // 0xb2ae5c: r0 = 4
    //     0xb2ae5c: movz            x0, #0x4
    // 0xb2ae60: StoreField: r1->field_b = r0
    //     0xb2ae60: stur            w0, [x1, #0xb]
    // 0xb2ae64: r0 = Column()
    //     0xb2ae64: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb2ae68: r1 = Instance_Axis
    //     0xb2ae68: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2ae6c: StoreField: r0->field_f = r1
    //     0xb2ae6c: stur            w1, [x0, #0xf]
    // 0xb2ae70: r1 = Instance_MainAxisAlignment
    //     0xb2ae70: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2ae74: ldr             x1, [x1, #0xa08]
    // 0xb2ae78: StoreField: r0->field_13 = r1
    //     0xb2ae78: stur            w1, [x0, #0x13]
    // 0xb2ae7c: r1 = Instance_MainAxisSize
    //     0xb2ae7c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2ae80: ldr             x1, [x1, #0xa10]
    // 0xb2ae84: ArrayStore: r0[0] = r1  ; List_4
    //     0xb2ae84: stur            w1, [x0, #0x17]
    // 0xb2ae88: r1 = Instance_CrossAxisAlignment
    //     0xb2ae88: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb2ae8c: ldr             x1, [x1, #0x890]
    // 0xb2ae90: StoreField: r0->field_1b = r1
    //     0xb2ae90: stur            w1, [x0, #0x1b]
    // 0xb2ae94: r1 = Instance_VerticalDirection
    //     0xb2ae94: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2ae98: ldr             x1, [x1, #0xa20]
    // 0xb2ae9c: StoreField: r0->field_23 = r1
    //     0xb2ae9c: stur            w1, [x0, #0x23]
    // 0xb2aea0: r1 = Instance_Clip
    //     0xb2aea0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2aea4: ldr             x1, [x1, #0x38]
    // 0xb2aea8: StoreField: r0->field_2b = r1
    //     0xb2aea8: stur            w1, [x0, #0x2b]
    // 0xb2aeac: StoreField: r0->field_2f = rZR
    //     0xb2aeac: stur            xzr, [x0, #0x2f]
    // 0xb2aeb0: ldur            x1, [fp, #-8]
    // 0xb2aeb4: StoreField: r0->field_b = r1
    //     0xb2aeb4: stur            w1, [x0, #0xb]
    // 0xb2aeb8: LeaveFrame
    //     0xb2aeb8: mov             SP, fp
    //     0xb2aebc: ldp             fp, lr, [SP], #0x10
    // 0xb2aec0: ret
    //     0xb2aec0: ret             
    // 0xb2aec4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2aec4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2aec8: b               #0xb2a698
    // 0xb2aecc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2aecc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2aed0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2aed0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2aed4: r9 = value
    //     0xb2aed4: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6ab90] Field <<EMAIL>>: late (offset: 0x14)
    //     0xb2aed8: ldr             x9, [x9, #0xb90]
    // 0xb2aedc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb2aedc: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb2aee0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2aee0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2aee4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb2aee4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4122, size: 0x14, field offset: 0xc
//   const constructor, 
class BagImages extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e540, size: 0x48
    // 0xc7e540: EnterFrame
    //     0xc7e540: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e544: mov             fp, SP
    // 0xc7e548: AllocStack(0x8)
    //     0xc7e548: sub             SP, SP, #8
    // 0xc7e54c: CheckStackOverflow
    //     0xc7e54c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7e550: cmp             SP, x16
    //     0xc7e554: b.ls            #0xc7e580
    // 0xc7e558: r1 = <BagImages>
    //     0xc7e558: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e70] TypeArguments: <BagImages>
    //     0xc7e55c: ldr             x1, [x1, #0xe70]
    // 0xc7e560: r0 = _BagImagesState()
    //     0xc7e560: bl              #0xc7e588  ; Allocate_BagImagesStateStub -> _BagImagesState (size=0x20)
    // 0xc7e564: mov             x1, x0
    // 0xc7e568: stur            x0, [fp, #-8]
    // 0xc7e56c: r0 = _BagImagesState()
    //     0xc7e56c: bl              #0xc7a0e8  ; [package:customer_app/app/presentation/views/basic/bag/bag_images.dart] _BagImagesState::_BagImagesState
    // 0xc7e570: ldur            x0, [fp, #-8]
    // 0xc7e574: LeaveFrame
    //     0xc7e574: mov             SP, fp
    //     0xc7e578: ldp             fp, lr, [SP], #0x10
    // 0xc7e57c: ret
    //     0xc7e57c: ret             
    // 0xc7e580: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e580: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e584: b               #0xc7e558
  }
}
