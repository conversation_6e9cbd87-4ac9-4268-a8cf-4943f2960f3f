// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/product_title_item_view.dart

// class id: 1049440, size: 0x8
class :: {
}

// class id: 3310, size: 0x14, field offset: 0x14
class _ProductTitleItemViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb89fa8, size: 0x7fc
    // 0xb89fa8: EnterFrame
    //     0xb89fa8: stp             fp, lr, [SP, #-0x10]!
    //     0xb89fac: mov             fp, SP
    // 0xb89fb0: AllocStack(0x60)
    //     0xb89fb0: sub             SP, SP, #0x60
    // 0xb89fb4: SetupParameters(_ProductTitleItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb89fb4: mov             x0, x1
    //     0xb89fb8: stur            x1, [fp, #-8]
    //     0xb89fbc: mov             x1, x2
    //     0xb89fc0: stur            x2, [fp, #-0x10]
    // 0xb89fc4: CheckStackOverflow
    //     0xb89fc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89fc8: cmp             SP, x16
    //     0xb89fcc: b.ls            #0xb8a794
    // 0xb89fd0: r1 = 2
    //     0xb89fd0: movz            x1, #0x2
    // 0xb89fd4: r0 = AllocateContext()
    //     0xb89fd4: bl              #0x16f6108  ; AllocateContextStub
    // 0xb89fd8: mov             x1, x0
    // 0xb89fdc: ldur            x0, [fp, #-8]
    // 0xb89fe0: stur            x1, [fp, #-0x30]
    // 0xb89fe4: StoreField: r1->field_f = r0
    //     0xb89fe4: stur            w0, [x1, #0xf]
    // 0xb89fe8: ldur            x2, [fp, #-0x10]
    // 0xb89fec: StoreField: r1->field_13 = r2
    //     0xb89fec: stur            w2, [x1, #0x13]
    // 0xb89ff0: LoadField: r3 = r0->field_b
    //     0xb89ff0: ldur            w3, [x0, #0xb]
    // 0xb89ff4: DecompressPointer r3
    //     0xb89ff4: add             x3, x3, HEAP, lsl #32
    // 0xb89ff8: cmp             w3, NULL
    // 0xb89ffc: b.eq            #0xb8a79c
    // 0xb8a000: LoadField: r4 = r3->field_b
    //     0xb8a000: ldur            w4, [x3, #0xb]
    // 0xb8a004: DecompressPointer r4
    //     0xb8a004: add             x4, x4, HEAP, lsl #32
    // 0xb8a008: stur            x4, [fp, #-0x28]
    // 0xb8a00c: LoadField: r5 = r3->field_f
    //     0xb8a00c: ldur            w5, [x3, #0xf]
    // 0xb8a010: DecompressPointer r5
    //     0xb8a010: add             x5, x5, HEAP, lsl #32
    // 0xb8a014: stur            x5, [fp, #-0x20]
    // 0xb8a018: LoadField: r6 = r3->field_13
    //     0xb8a018: ldur            w6, [x3, #0x13]
    // 0xb8a01c: DecompressPointer r6
    //     0xb8a01c: add             x6, x6, HEAP, lsl #32
    // 0xb8a020: stur            x6, [fp, #-0x18]
    // 0xb8a024: r0 = Radius()
    //     0xb8a024: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8a028: d0 = 8.000000
    //     0xb8a028: fmov            d0, #8.00000000
    // 0xb8a02c: stur            x0, [fp, #-0x38]
    // 0xb8a030: StoreField: r0->field_7 = d0
    //     0xb8a030: stur            d0, [x0, #7]
    // 0xb8a034: StoreField: r0->field_f = d0
    //     0xb8a034: stur            d0, [x0, #0xf]
    // 0xb8a038: r0 = BorderRadius()
    //     0xb8a038: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8a03c: mov             x1, x0
    // 0xb8a040: ldur            x0, [fp, #-0x38]
    // 0xb8a044: stur            x1, [fp, #-0x40]
    // 0xb8a048: StoreField: r1->field_7 = r0
    //     0xb8a048: stur            w0, [x1, #7]
    // 0xb8a04c: StoreField: r1->field_b = r0
    //     0xb8a04c: stur            w0, [x1, #0xb]
    // 0xb8a050: StoreField: r1->field_f = r0
    //     0xb8a050: stur            w0, [x1, #0xf]
    // 0xb8a054: StoreField: r1->field_13 = r0
    //     0xb8a054: stur            w0, [x1, #0x13]
    // 0xb8a058: r0 = ProductTitleWidget()
    //     0xb8a058: bl              #0xa898b0  ; AllocateProductTitleWidgetStub -> ProductTitleWidget (size=0x20)
    // 0xb8a05c: mov             x3, x0
    // 0xb8a060: ldur            x0, [fp, #-0x28]
    // 0xb8a064: stur            x3, [fp, #-0x38]
    // 0xb8a068: StoreField: r3->field_f = r0
    //     0xb8a068: stur            w0, [x3, #0xf]
    // 0xb8a06c: ldur            x1, [fp, #-0x18]
    // 0xb8a070: StoreField: r3->field_b = r1
    //     0xb8a070: stur            w1, [x3, #0xb]
    // 0xb8a074: ldur            x1, [fp, #-0x20]
    // 0xb8a078: StoreField: r3->field_13 = r1
    //     0xb8a078: stur            w1, [x3, #0x13]
    // 0xb8a07c: ldur            x2, [fp, #-0x30]
    // 0xb8a080: r1 = Function '<anonymous closure>':.
    //     0xb8a080: add             x1, PP, #0x55, lsl #12  ; [pp+0x554a8] AnonymousClosure: (0xb8a854), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_title_item_view.dart] _ProductTitleItemViewState::build (0xb89fa8)
    //     0xb8a084: ldr             x1, [x1, #0x4a8]
    // 0xb8a088: r0 = AllocateClosure()
    //     0xb8a088: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8a08c: mov             x1, x0
    // 0xb8a090: ldur            x0, [fp, #-0x38]
    // 0xb8a094: StoreField: r0->field_1b = r1
    //     0xb8a094: stur            w1, [x0, #0x1b]
    // 0xb8a098: ldur            x1, [fp, #-0x40]
    // 0xb8a09c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb8a09c: stur            w1, [x0, #0x17]
    // 0xb8a0a0: ldur            x1, [fp, #-0x28]
    // 0xb8a0a4: LoadField: r2 = r1->field_4b
    //     0xb8a0a4: ldur            w2, [x1, #0x4b]
    // 0xb8a0a8: DecompressPointer r2
    //     0xb8a0a8: add             x2, x2, HEAP, lsl #32
    // 0xb8a0ac: stur            x2, [fp, #-0x20]
    // 0xb8a0b0: cmp             w2, NULL
    // 0xb8a0b4: b.ne            #0xb8a0c0
    // 0xb8a0b8: r1 = Null
    //     0xb8a0b8: mov             x1, NULL
    // 0xb8a0bc: b               #0xb8a0d8
    // 0xb8a0c0: LoadField: r1 = r2->field_7
    //     0xb8a0c0: ldur            w1, [x2, #7]
    // 0xb8a0c4: cbnz            w1, #0xb8a0d0
    // 0xb8a0c8: r3 = false
    //     0xb8a0c8: add             x3, NULL, #0x30  ; false
    // 0xb8a0cc: b               #0xb8a0d4
    // 0xb8a0d0: r3 = true
    //     0xb8a0d0: add             x3, NULL, #0x20  ; true
    // 0xb8a0d4: mov             x1, x3
    // 0xb8a0d8: cmp             w1, NULL
    // 0xb8a0dc: b.ne            #0xb8a0e4
    // 0xb8a0e0: r1 = false
    //     0xb8a0e0: add             x1, NULL, #0x30  ; false
    // 0xb8a0e4: stur            x1, [fp, #-0x18]
    // 0xb8a0e8: r0 = Radius()
    //     0xb8a0e8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8a0ec: d0 = 10.000000
    //     0xb8a0ec: fmov            d0, #10.00000000
    // 0xb8a0f0: stur            x0, [fp, #-0x28]
    // 0xb8a0f4: StoreField: r0->field_7 = d0
    //     0xb8a0f4: stur            d0, [x0, #7]
    // 0xb8a0f8: StoreField: r0->field_f = d0
    //     0xb8a0f8: stur            d0, [x0, #0xf]
    // 0xb8a0fc: r0 = BorderRadius()
    //     0xb8a0fc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8a100: mov             x1, x0
    // 0xb8a104: ldur            x0, [fp, #-0x28]
    // 0xb8a108: stur            x1, [fp, #-0x40]
    // 0xb8a10c: StoreField: r1->field_7 = r0
    //     0xb8a10c: stur            w0, [x1, #7]
    // 0xb8a110: StoreField: r1->field_b = r0
    //     0xb8a110: stur            w0, [x1, #0xb]
    // 0xb8a114: StoreField: r1->field_f = r0
    //     0xb8a114: stur            w0, [x1, #0xf]
    // 0xb8a118: StoreField: r1->field_13 = r0
    //     0xb8a118: stur            w0, [x1, #0x13]
    // 0xb8a11c: r0 = BoxDecoration()
    //     0xb8a11c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8a120: mov             x2, x0
    // 0xb8a124: r0 = Instance_Color
    //     0xb8a124: add             x0, PP, #0x40, lsl #12  ; [pp+0x40860] Obj!Color@d6b0a1
    //     0xb8a128: ldr             x0, [x0, #0x860]
    // 0xb8a12c: stur            x2, [fp, #-0x48]
    // 0xb8a130: StoreField: r2->field_7 = r0
    //     0xb8a130: stur            w0, [x2, #7]
    // 0xb8a134: ldur            x1, [fp, #-0x40]
    // 0xb8a138: StoreField: r2->field_13 = r1
    //     0xb8a138: stur            w1, [x2, #0x13]
    // 0xb8a13c: r3 = Instance_BoxShape
    //     0xb8a13c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8a140: ldr             x3, [x3, #0x80]
    // 0xb8a144: StoreField: r2->field_23 = r3
    //     0xb8a144: stur            w3, [x2, #0x23]
    // 0xb8a148: ldur            x1, [fp, #-0x20]
    // 0xb8a14c: cmp             w1, NULL
    // 0xb8a150: b.ne            #0xb8a15c
    // 0xb8a154: r4 = Null
    //     0xb8a154: mov             x4, NULL
    // 0xb8a158: b               #0xb8a174
    // 0xb8a15c: LoadField: r4 = r1->field_7
    //     0xb8a15c: ldur            w4, [x1, #7]
    // 0xb8a160: cbnz            w4, #0xb8a16c
    // 0xb8a164: r5 = false
    //     0xb8a164: add             x5, NULL, #0x30  ; false
    // 0xb8a168: b               #0xb8a170
    // 0xb8a16c: r5 = true
    //     0xb8a16c: add             x5, NULL, #0x20  ; true
    // 0xb8a170: mov             x4, x5
    // 0xb8a174: cmp             w4, NULL
    // 0xb8a178: b.ne            #0xb8a180
    // 0xb8a17c: r4 = false
    //     0xb8a17c: add             x4, NULL, #0x30  ; false
    // 0xb8a180: stur            x4, [fp, #-0x28]
    // 0xb8a184: cmp             w1, NULL
    // 0xb8a188: b.ne            #0xb8a194
    // 0xb8a18c: r7 = ""
    //     0xb8a18c: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8a190: b               #0xb8a198
    // 0xb8a194: mov             x7, x1
    // 0xb8a198: ldur            x6, [fp, #-8]
    // 0xb8a19c: ldur            x5, [fp, #-0x18]
    // 0xb8a1a0: ldur            x1, [fp, #-0x10]
    // 0xb8a1a4: stur            x7, [fp, #-0x20]
    // 0xb8a1a8: r0 = of()
    //     0xb8a1a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8a1ac: LoadField: r1 = r0->field_87
    //     0xb8a1ac: ldur            w1, [x0, #0x87]
    // 0xb8a1b0: DecompressPointer r1
    //     0xb8a1b0: add             x1, x1, HEAP, lsl #32
    // 0xb8a1b4: LoadField: r0 = r1->field_7
    //     0xb8a1b4: ldur            w0, [x1, #7]
    // 0xb8a1b8: DecompressPointer r0
    //     0xb8a1b8: add             x0, x0, HEAP, lsl #32
    // 0xb8a1bc: r16 = 12.000000
    //     0xb8a1bc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb8a1c0: ldr             x16, [x16, #0x9e8]
    // 0xb8a1c4: r30 = Instance_Color
    //     0xb8a1c4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8a1c8: stp             lr, x16, [SP]
    // 0xb8a1cc: mov             x1, x0
    // 0xb8a1d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8a1d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb8a1d4: ldr             x4, [x4, #0xaa0]
    // 0xb8a1d8: r0 = copyWith()
    //     0xb8a1d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8a1dc: stur            x0, [fp, #-0x10]
    // 0xb8a1e0: r0 = Text()
    //     0xb8a1e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8a1e4: mov             x1, x0
    // 0xb8a1e8: ldur            x0, [fp, #-0x20]
    // 0xb8a1ec: stur            x1, [fp, #-0x40]
    // 0xb8a1f0: StoreField: r1->field_b = r0
    //     0xb8a1f0: stur            w0, [x1, #0xb]
    // 0xb8a1f4: ldur            x0, [fp, #-0x10]
    // 0xb8a1f8: StoreField: r1->field_13 = r0
    //     0xb8a1f8: stur            w0, [x1, #0x13]
    // 0xb8a1fc: r0 = Instance_TextAlign
    //     0xb8a1fc: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb8a200: StoreField: r1->field_1b = r0
    //     0xb8a200: stur            w0, [x1, #0x1b]
    // 0xb8a204: r0 = Center()
    //     0xb8a204: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb8a208: mov             x1, x0
    // 0xb8a20c: r0 = Instance_Alignment
    //     0xb8a20c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb8a210: ldr             x0, [x0, #0xb10]
    // 0xb8a214: stur            x1, [fp, #-0x10]
    // 0xb8a218: StoreField: r1->field_f = r0
    //     0xb8a218: stur            w0, [x1, #0xf]
    // 0xb8a21c: ldur            x2, [fp, #-0x40]
    // 0xb8a220: StoreField: r1->field_b = r2
    //     0xb8a220: stur            w2, [x1, #0xb]
    // 0xb8a224: r0 = Visibility()
    //     0xb8a224: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb8a228: mov             x1, x0
    // 0xb8a22c: ldur            x0, [fp, #-0x10]
    // 0xb8a230: stur            x1, [fp, #-0x20]
    // 0xb8a234: StoreField: r1->field_b = r0
    //     0xb8a234: stur            w0, [x1, #0xb]
    // 0xb8a238: r0 = Instance_SizedBox
    //     0xb8a238: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb8a23c: StoreField: r1->field_f = r0
    //     0xb8a23c: stur            w0, [x1, #0xf]
    // 0xb8a240: ldur            x2, [fp, #-0x28]
    // 0xb8a244: StoreField: r1->field_13 = r2
    //     0xb8a244: stur            w2, [x1, #0x13]
    // 0xb8a248: r2 = false
    //     0xb8a248: add             x2, NULL, #0x30  ; false
    // 0xb8a24c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb8a24c: stur            w2, [x1, #0x17]
    // 0xb8a250: StoreField: r1->field_1b = r2
    //     0xb8a250: stur            w2, [x1, #0x1b]
    // 0xb8a254: StoreField: r1->field_1f = r2
    //     0xb8a254: stur            w2, [x1, #0x1f]
    // 0xb8a258: StoreField: r1->field_23 = r2
    //     0xb8a258: stur            w2, [x1, #0x23]
    // 0xb8a25c: StoreField: r1->field_27 = r2
    //     0xb8a25c: stur            w2, [x1, #0x27]
    // 0xb8a260: StoreField: r1->field_2b = r2
    //     0xb8a260: stur            w2, [x1, #0x2b]
    // 0xb8a264: r0 = Container()
    //     0xb8a264: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8a268: stur            x0, [fp, #-0x10]
    // 0xb8a26c: ldur            x16, [fp, #-0x48]
    // 0xb8a270: r30 = Instance_EdgeInsets
    //     0xb8a270: add             lr, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb8a274: ldr             lr, [lr, #0x878]
    // 0xb8a278: stp             lr, x16, [SP, #8]
    // 0xb8a27c: ldur            x16, [fp, #-0x20]
    // 0xb8a280: str             x16, [SP]
    // 0xb8a284: mov             x1, x0
    // 0xb8a288: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xb8a288: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xb8a28c: ldr             x4, [x4, #0xb40]
    // 0xb8a290: r0 = Container()
    //     0xb8a290: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8a294: r0 = IntrinsicWidth()
    //     0xb8a294: bl              #0xa898a4  ; AllocateIntrinsicWidthStub -> IntrinsicWidth (size=0x18)
    // 0xb8a298: mov             x1, x0
    // 0xb8a29c: ldur            x0, [fp, #-0x10]
    // 0xb8a2a0: stur            x1, [fp, #-0x20]
    // 0xb8a2a4: StoreField: r1->field_b = r0
    //     0xb8a2a4: stur            w0, [x1, #0xb]
    // 0xb8a2a8: r0 = Padding()
    //     0xb8a2a8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8a2ac: mov             x1, x0
    // 0xb8a2b0: r0 = Instance_EdgeInsets
    //     0xb8a2b0: add             x0, PP, #0x55, lsl #12  ; [pp+0x554b0] Obj!EdgeInsets@d59421
    //     0xb8a2b4: ldr             x0, [x0, #0x4b0]
    // 0xb8a2b8: stur            x1, [fp, #-0x10]
    // 0xb8a2bc: StoreField: r1->field_f = r0
    //     0xb8a2bc: stur            w0, [x1, #0xf]
    // 0xb8a2c0: ldur            x0, [fp, #-0x20]
    // 0xb8a2c4: StoreField: r1->field_b = r0
    //     0xb8a2c4: stur            w0, [x1, #0xb]
    // 0xb8a2c8: r0 = Visibility()
    //     0xb8a2c8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb8a2cc: mov             x1, x0
    // 0xb8a2d0: ldur            x0, [fp, #-0x10]
    // 0xb8a2d4: stur            x1, [fp, #-0x20]
    // 0xb8a2d8: StoreField: r1->field_b = r0
    //     0xb8a2d8: stur            w0, [x1, #0xb]
    // 0xb8a2dc: r0 = Instance_SizedBox
    //     0xb8a2dc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb8a2e0: StoreField: r1->field_f = r0
    //     0xb8a2e0: stur            w0, [x1, #0xf]
    // 0xb8a2e4: ldur            x0, [fp, #-0x18]
    // 0xb8a2e8: StoreField: r1->field_13 = r0
    //     0xb8a2e8: stur            w0, [x1, #0x13]
    // 0xb8a2ec: r0 = false
    //     0xb8a2ec: add             x0, NULL, #0x30  ; false
    // 0xb8a2f0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8a2f0: stur            w0, [x1, #0x17]
    // 0xb8a2f4: StoreField: r1->field_1b = r0
    //     0xb8a2f4: stur            w0, [x1, #0x1b]
    // 0xb8a2f8: StoreField: r1->field_1f = r0
    //     0xb8a2f8: stur            w0, [x1, #0x1f]
    // 0xb8a2fc: StoreField: r1->field_23 = r0
    //     0xb8a2fc: stur            w0, [x1, #0x23]
    // 0xb8a300: StoreField: r1->field_27 = r0
    //     0xb8a300: stur            w0, [x1, #0x27]
    // 0xb8a304: StoreField: r1->field_2b = r0
    //     0xb8a304: stur            w0, [x1, #0x2b]
    // 0xb8a308: r0 = Radius()
    //     0xb8a308: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8a30c: d0 = 10.000000
    //     0xb8a30c: fmov            d0, #10.00000000
    // 0xb8a310: stur            x0, [fp, #-0x10]
    // 0xb8a314: StoreField: r0->field_7 = d0
    //     0xb8a314: stur            d0, [x0, #7]
    // 0xb8a318: StoreField: r0->field_f = d0
    //     0xb8a318: stur            d0, [x0, #0xf]
    // 0xb8a31c: r0 = BorderRadius()
    //     0xb8a31c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8a320: mov             x1, x0
    // 0xb8a324: ldur            x0, [fp, #-0x10]
    // 0xb8a328: stur            x1, [fp, #-0x18]
    // 0xb8a32c: StoreField: r1->field_7 = r0
    //     0xb8a32c: stur            w0, [x1, #7]
    // 0xb8a330: StoreField: r1->field_b = r0
    //     0xb8a330: stur            w0, [x1, #0xb]
    // 0xb8a334: StoreField: r1->field_f = r0
    //     0xb8a334: stur            w0, [x1, #0xf]
    // 0xb8a338: StoreField: r1->field_13 = r0
    //     0xb8a338: stur            w0, [x1, #0x13]
    // 0xb8a33c: r0 = BoxDecoration()
    //     0xb8a33c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8a340: mov             x3, x0
    // 0xb8a344: r0 = Instance_Color
    //     0xb8a344: add             x0, PP, #0x40, lsl #12  ; [pp+0x40860] Obj!Color@d6b0a1
    //     0xb8a348: ldr             x0, [x0, #0x860]
    // 0xb8a34c: stur            x3, [fp, #-0x10]
    // 0xb8a350: StoreField: r3->field_7 = r0
    //     0xb8a350: stur            w0, [x3, #7]
    // 0xb8a354: ldur            x0, [fp, #-0x18]
    // 0xb8a358: StoreField: r3->field_13 = r0
    //     0xb8a358: stur            w0, [x3, #0x13]
    // 0xb8a35c: r0 = Instance_BoxShape
    //     0xb8a35c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8a360: ldr             x0, [x0, #0x80]
    // 0xb8a364: StoreField: r3->field_23 = r0
    //     0xb8a364: stur            w0, [x3, #0x23]
    // 0xb8a368: ldur            x1, [fp, #-8]
    // 0xb8a36c: LoadField: r2 = r1->field_b
    //     0xb8a36c: ldur            w2, [x1, #0xb]
    // 0xb8a370: DecompressPointer r2
    //     0xb8a370: add             x2, x2, HEAP, lsl #32
    // 0xb8a374: cmp             w2, NULL
    // 0xb8a378: b.eq            #0xb8a7a0
    // 0xb8a37c: LoadField: r4 = r2->field_27
    //     0xb8a37c: ldur            w4, [x2, #0x27]
    // 0xb8a380: DecompressPointer r4
    //     0xb8a380: add             x4, x4, HEAP, lsl #32
    // 0xb8a384: stur            x4, [fp, #-8]
    // 0xb8a388: cmp             w4, NULL
    // 0xb8a38c: b.ne            #0xb8a398
    // 0xb8a390: r1 = Null
    //     0xb8a390: mov             x1, NULL
    // 0xb8a394: b               #0xb8a3a0
    // 0xb8a398: LoadField: r1 = r4->field_b
    //     0xb8a398: ldur            w1, [x4, #0xb]
    // 0xb8a39c: DecompressPointer r1
    //     0xb8a39c: add             x1, x1, HEAP, lsl #32
    // 0xb8a3a0: cmp             w1, NULL
    // 0xb8a3a4: b.eq            #0xb8a408
    // 0xb8a3a8: r2 = LoadInt32Instr(r1)
    //     0xb8a3a8: sbfx            x2, x1, #1, #0x1f
    //     0xb8a3ac: tbz             w1, #0, #0xb8a3b4
    //     0xb8a3b0: ldur            x2, [x1, #7]
    // 0xb8a3b4: cmp             x2, #0
    // 0xb8a3b8: b.le            #0xb8a408
    // 0xb8a3bc: r1 = Null
    //     0xb8a3bc: mov             x1, NULL
    // 0xb8a3c0: r2 = 4
    //     0xb8a3c0: movz            x2, #0x4
    // 0xb8a3c4: r0 = AllocateArray()
    //     0xb8a3c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8a3c8: r16 = "Get this as low as "
    //     0xb8a3c8: add             x16, PP, #0x52, lsl #12  ; [pp+0x52608] "Get this as low as "
    //     0xb8a3cc: ldr             x16, [x16, #0x608]
    // 0xb8a3d0: StoreField: r0->field_f = r16
    //     0xb8a3d0: stur            w16, [x0, #0xf]
    // 0xb8a3d4: ldur            x1, [fp, #-8]
    // 0xb8a3d8: cmp             w1, NULL
    // 0xb8a3dc: b.ne            #0xb8a3e8
    // 0xb8a3e0: r1 = Null
    //     0xb8a3e0: mov             x1, NULL
    // 0xb8a3e4: b               #0xb8a3f4
    // 0xb8a3e8: LoadField: r2 = r1->field_13
    //     0xb8a3e8: ldur            w2, [x1, #0x13]
    // 0xb8a3ec: DecompressPointer r2
    //     0xb8a3ec: add             x2, x2, HEAP, lsl #32
    // 0xb8a3f0: mov             x1, x2
    // 0xb8a3f4: StoreField: r0->field_13 = r1
    //     0xb8a3f4: stur            w1, [x0, #0x13]
    // 0xb8a3f8: str             x0, [SP]
    // 0xb8a3fc: r0 = _interpolate()
    //     0xb8a3fc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb8a400: mov             x4, x0
    // 0xb8a404: b               #0xb8a410
    // 0xb8a408: r4 = "View available offers"
    //     0xb8a408: add             x4, PP, #0x52, lsl #12  ; [pp+0x52610] "View available offers"
    //     0xb8a40c: ldr             x4, [x4, #0x610]
    // 0xb8a410: ldur            x3, [fp, #-0x30]
    // 0xb8a414: ldur            x2, [fp, #-0x38]
    // 0xb8a418: ldur            x0, [fp, #-0x20]
    // 0xb8a41c: stur            x4, [fp, #-8]
    // 0xb8a420: LoadField: r1 = r3->field_13
    //     0xb8a420: ldur            w1, [x3, #0x13]
    // 0xb8a424: DecompressPointer r1
    //     0xb8a424: add             x1, x1, HEAP, lsl #32
    // 0xb8a428: r0 = of()
    //     0xb8a428: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8a42c: LoadField: r1 = r0->field_87
    //     0xb8a42c: ldur            w1, [x0, #0x87]
    // 0xb8a430: DecompressPointer r1
    //     0xb8a430: add             x1, x1, HEAP, lsl #32
    // 0xb8a434: LoadField: r0 = r1->field_7
    //     0xb8a434: ldur            w0, [x1, #7]
    // 0xb8a438: DecompressPointer r0
    //     0xb8a438: add             x0, x0, HEAP, lsl #32
    // 0xb8a43c: r16 = 12.000000
    //     0xb8a43c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb8a440: ldr             x16, [x16, #0x9e8]
    // 0xb8a444: r30 = Instance_Color
    //     0xb8a444: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8a448: stp             lr, x16, [SP]
    // 0xb8a44c: mov             x1, x0
    // 0xb8a450: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8a450: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb8a454: ldr             x4, [x4, #0xaa0]
    // 0xb8a458: r0 = copyWith()
    //     0xb8a458: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8a45c: stur            x0, [fp, #-0x18]
    // 0xb8a460: r0 = Text()
    //     0xb8a460: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8a464: mov             x3, x0
    // 0xb8a468: ldur            x0, [fp, #-8]
    // 0xb8a46c: stur            x3, [fp, #-0x28]
    // 0xb8a470: StoreField: r3->field_b = r0
    //     0xb8a470: stur            w0, [x3, #0xb]
    // 0xb8a474: ldur            x0, [fp, #-0x18]
    // 0xb8a478: StoreField: r3->field_13 = r0
    //     0xb8a478: stur            w0, [x3, #0x13]
    // 0xb8a47c: r0 = Instance_TextAlign
    //     0xb8a47c: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb8a480: StoreField: r3->field_1b = r0
    //     0xb8a480: stur            w0, [x3, #0x1b]
    // 0xb8a484: r1 = Null
    //     0xb8a484: mov             x1, NULL
    // 0xb8a488: r2 = 4
    //     0xb8a488: movz            x2, #0x4
    // 0xb8a48c: r0 = AllocateArray()
    //     0xb8a48c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8a490: mov             x2, x0
    // 0xb8a494: ldur            x0, [fp, #-0x28]
    // 0xb8a498: stur            x2, [fp, #-8]
    // 0xb8a49c: StoreField: r2->field_f = r0
    //     0xb8a49c: stur            w0, [x2, #0xf]
    // 0xb8a4a0: r16 = Instance_Icon
    //     0xb8a4a0: add             x16, PP, #0x55, lsl #12  ; [pp+0x554b8] Obj!Icon@d666f1
    //     0xb8a4a4: ldr             x16, [x16, #0x4b8]
    // 0xb8a4a8: StoreField: r2->field_13 = r16
    //     0xb8a4a8: stur            w16, [x2, #0x13]
    // 0xb8a4ac: r1 = <Widget>
    //     0xb8a4ac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8a4b0: r0 = AllocateGrowableArray()
    //     0xb8a4b0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8a4b4: mov             x1, x0
    // 0xb8a4b8: ldur            x0, [fp, #-8]
    // 0xb8a4bc: stur            x1, [fp, #-0x18]
    // 0xb8a4c0: StoreField: r1->field_f = r0
    //     0xb8a4c0: stur            w0, [x1, #0xf]
    // 0xb8a4c4: r2 = 4
    //     0xb8a4c4: movz            x2, #0x4
    // 0xb8a4c8: StoreField: r1->field_b = r2
    //     0xb8a4c8: stur            w2, [x1, #0xb]
    // 0xb8a4cc: r0 = Row()
    //     0xb8a4cc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb8a4d0: mov             x1, x0
    // 0xb8a4d4: r0 = Instance_Axis
    //     0xb8a4d4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb8a4d8: stur            x1, [fp, #-8]
    // 0xb8a4dc: StoreField: r1->field_f = r0
    //     0xb8a4dc: stur            w0, [x1, #0xf]
    // 0xb8a4e0: r2 = Instance_MainAxisAlignment
    //     0xb8a4e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb8a4e4: ldr             x2, [x2, #0xab0]
    // 0xb8a4e8: StoreField: r1->field_13 = r2
    //     0xb8a4e8: stur            w2, [x1, #0x13]
    // 0xb8a4ec: r2 = Instance_MainAxisSize
    //     0xb8a4ec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8a4f0: ldr             x2, [x2, #0xa10]
    // 0xb8a4f4: ArrayStore: r1[0] = r2  ; List_4
    //     0xb8a4f4: stur            w2, [x1, #0x17]
    // 0xb8a4f8: r3 = Instance_CrossAxisAlignment
    //     0xb8a4f8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb8a4fc: ldr             x3, [x3, #0xa18]
    // 0xb8a500: StoreField: r1->field_1b = r3
    //     0xb8a500: stur            w3, [x1, #0x1b]
    // 0xb8a504: r4 = Instance_VerticalDirection
    //     0xb8a504: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8a508: ldr             x4, [x4, #0xa20]
    // 0xb8a50c: StoreField: r1->field_23 = r4
    //     0xb8a50c: stur            w4, [x1, #0x23]
    // 0xb8a510: r5 = Instance_Clip
    //     0xb8a510: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8a514: ldr             x5, [x5, #0x38]
    // 0xb8a518: StoreField: r1->field_2b = r5
    //     0xb8a518: stur            w5, [x1, #0x2b]
    // 0xb8a51c: StoreField: r1->field_2f = rZR
    //     0xb8a51c: stur            xzr, [x1, #0x2f]
    // 0xb8a520: ldur            x6, [fp, #-0x18]
    // 0xb8a524: StoreField: r1->field_b = r6
    //     0xb8a524: stur            w6, [x1, #0xb]
    // 0xb8a528: r0 = Center()
    //     0xb8a528: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb8a52c: mov             x1, x0
    // 0xb8a530: r0 = Instance_Alignment
    //     0xb8a530: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb8a534: ldr             x0, [x0, #0xb10]
    // 0xb8a538: stur            x1, [fp, #-0x18]
    // 0xb8a53c: StoreField: r1->field_f = r0
    //     0xb8a53c: stur            w0, [x1, #0xf]
    // 0xb8a540: ldur            x0, [fp, #-8]
    // 0xb8a544: StoreField: r1->field_b = r0
    //     0xb8a544: stur            w0, [x1, #0xb]
    // 0xb8a548: r0 = Container()
    //     0xb8a548: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8a54c: stur            x0, [fp, #-8]
    // 0xb8a550: ldur            x16, [fp, #-0x10]
    // 0xb8a554: r30 = Instance_EdgeInsets
    //     0xb8a554: add             lr, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb8a558: ldr             lr, [lr, #0x878]
    // 0xb8a55c: stp             lr, x16, [SP, #8]
    // 0xb8a560: ldur            x16, [fp, #-0x18]
    // 0xb8a564: str             x16, [SP]
    // 0xb8a568: mov             x1, x0
    // 0xb8a56c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xb8a56c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xb8a570: ldr             x4, [x4, #0xb40]
    // 0xb8a574: r0 = Container()
    //     0xb8a574: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8a578: r0 = IntrinsicWidth()
    //     0xb8a578: bl              #0xa898a4  ; AllocateIntrinsicWidthStub -> IntrinsicWidth (size=0x18)
    // 0xb8a57c: mov             x1, x0
    // 0xb8a580: ldur            x0, [fp, #-8]
    // 0xb8a584: stur            x1, [fp, #-0x10]
    // 0xb8a588: StoreField: r1->field_b = r0
    //     0xb8a588: stur            w0, [x1, #0xb]
    // 0xb8a58c: r0 = InkWell()
    //     0xb8a58c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb8a590: mov             x3, x0
    // 0xb8a594: ldur            x0, [fp, #-0x10]
    // 0xb8a598: stur            x3, [fp, #-8]
    // 0xb8a59c: StoreField: r3->field_b = r0
    //     0xb8a59c: stur            w0, [x3, #0xb]
    // 0xb8a5a0: ldur            x2, [fp, #-0x30]
    // 0xb8a5a4: r1 = Function '<anonymous closure>':.
    //     0xb8a5a4: add             x1, PP, #0x55, lsl #12  ; [pp+0x554c0] AnonymousClosure: (0xb8a7c4), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_title_item_view.dart] _ProductTitleItemViewState::build (0xb89fa8)
    //     0xb8a5a8: ldr             x1, [x1, #0x4c0]
    // 0xb8a5ac: r0 = AllocateClosure()
    //     0xb8a5ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8a5b0: mov             x1, x0
    // 0xb8a5b4: ldur            x0, [fp, #-8]
    // 0xb8a5b8: StoreField: r0->field_f = r1
    //     0xb8a5b8: stur            w1, [x0, #0xf]
    // 0xb8a5bc: r1 = true
    //     0xb8a5bc: add             x1, NULL, #0x20  ; true
    // 0xb8a5c0: StoreField: r0->field_43 = r1
    //     0xb8a5c0: stur            w1, [x0, #0x43]
    // 0xb8a5c4: r2 = Instance_BoxShape
    //     0xb8a5c4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8a5c8: ldr             x2, [x2, #0x80]
    // 0xb8a5cc: StoreField: r0->field_47 = r2
    //     0xb8a5cc: stur            w2, [x0, #0x47]
    // 0xb8a5d0: StoreField: r0->field_6f = r1
    //     0xb8a5d0: stur            w1, [x0, #0x6f]
    // 0xb8a5d4: r2 = false
    //     0xb8a5d4: add             x2, NULL, #0x30  ; false
    // 0xb8a5d8: StoreField: r0->field_73 = r2
    //     0xb8a5d8: stur            w2, [x0, #0x73]
    // 0xb8a5dc: StoreField: r0->field_83 = r1
    //     0xb8a5dc: stur            w1, [x0, #0x83]
    // 0xb8a5e0: StoreField: r0->field_7b = r2
    //     0xb8a5e0: stur            w2, [x0, #0x7b]
    // 0xb8a5e4: r0 = Padding()
    //     0xb8a5e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8a5e8: mov             x3, x0
    // 0xb8a5ec: r0 = Instance_EdgeInsets
    //     0xb8a5ec: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b30] Obj!EdgeInsets@d588e1
    //     0xb8a5f0: ldr             x0, [x0, #0xb30]
    // 0xb8a5f4: stur            x3, [fp, #-0x10]
    // 0xb8a5f8: StoreField: r3->field_f = r0
    //     0xb8a5f8: stur            w0, [x3, #0xf]
    // 0xb8a5fc: ldur            x0, [fp, #-8]
    // 0xb8a600: StoreField: r3->field_b = r0
    //     0xb8a600: stur            w0, [x3, #0xb]
    // 0xb8a604: r1 = Null
    //     0xb8a604: mov             x1, NULL
    // 0xb8a608: r2 = 4
    //     0xb8a608: movz            x2, #0x4
    // 0xb8a60c: r0 = AllocateArray()
    //     0xb8a60c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8a610: mov             x2, x0
    // 0xb8a614: ldur            x0, [fp, #-0x20]
    // 0xb8a618: stur            x2, [fp, #-8]
    // 0xb8a61c: StoreField: r2->field_f = r0
    //     0xb8a61c: stur            w0, [x2, #0xf]
    // 0xb8a620: ldur            x0, [fp, #-0x10]
    // 0xb8a624: StoreField: r2->field_13 = r0
    //     0xb8a624: stur            w0, [x2, #0x13]
    // 0xb8a628: r1 = <Widget>
    //     0xb8a628: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8a62c: r0 = AllocateGrowableArray()
    //     0xb8a62c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8a630: mov             x1, x0
    // 0xb8a634: ldur            x0, [fp, #-8]
    // 0xb8a638: stur            x1, [fp, #-0x10]
    // 0xb8a63c: StoreField: r1->field_f = r0
    //     0xb8a63c: stur            w0, [x1, #0xf]
    // 0xb8a640: r0 = 4
    //     0xb8a640: movz            x0, #0x4
    // 0xb8a644: StoreField: r1->field_b = r0
    //     0xb8a644: stur            w0, [x1, #0xb]
    // 0xb8a648: r0 = Row()
    //     0xb8a648: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb8a64c: mov             x1, x0
    // 0xb8a650: r0 = Instance_Axis
    //     0xb8a650: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb8a654: stur            x1, [fp, #-8]
    // 0xb8a658: StoreField: r1->field_f = r0
    //     0xb8a658: stur            w0, [x1, #0xf]
    // 0xb8a65c: r0 = Instance_MainAxisAlignment
    //     0xb8a65c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8a660: ldr             x0, [x0, #0xa08]
    // 0xb8a664: StoreField: r1->field_13 = r0
    //     0xb8a664: stur            w0, [x1, #0x13]
    // 0xb8a668: r2 = Instance_MainAxisSize
    //     0xb8a668: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8a66c: ldr             x2, [x2, #0xa10]
    // 0xb8a670: ArrayStore: r1[0] = r2  ; List_4
    //     0xb8a670: stur            w2, [x1, #0x17]
    // 0xb8a674: r2 = Instance_CrossAxisAlignment
    //     0xb8a674: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb8a678: ldr             x2, [x2, #0xa18]
    // 0xb8a67c: StoreField: r1->field_1b = r2
    //     0xb8a67c: stur            w2, [x1, #0x1b]
    // 0xb8a680: r2 = Instance_VerticalDirection
    //     0xb8a680: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8a684: ldr             x2, [x2, #0xa20]
    // 0xb8a688: StoreField: r1->field_23 = r2
    //     0xb8a688: stur            w2, [x1, #0x23]
    // 0xb8a68c: r3 = Instance_Clip
    //     0xb8a68c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8a690: ldr             x3, [x3, #0x38]
    // 0xb8a694: StoreField: r1->field_2b = r3
    //     0xb8a694: stur            w3, [x1, #0x2b]
    // 0xb8a698: StoreField: r1->field_2f = rZR
    //     0xb8a698: stur            xzr, [x1, #0x2f]
    // 0xb8a69c: ldur            x4, [fp, #-0x10]
    // 0xb8a6a0: StoreField: r1->field_b = r4
    //     0xb8a6a0: stur            w4, [x1, #0xb]
    // 0xb8a6a4: r0 = Padding()
    //     0xb8a6a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8a6a8: mov             x3, x0
    // 0xb8a6ac: r0 = Instance_EdgeInsets
    //     0xb8a6ac: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb8a6b0: ldr             x0, [x0, #0x668]
    // 0xb8a6b4: stur            x3, [fp, #-0x10]
    // 0xb8a6b8: StoreField: r3->field_f = r0
    //     0xb8a6b8: stur            w0, [x3, #0xf]
    // 0xb8a6bc: ldur            x0, [fp, #-8]
    // 0xb8a6c0: StoreField: r3->field_b = r0
    //     0xb8a6c0: stur            w0, [x3, #0xb]
    // 0xb8a6c4: r1 = Null
    //     0xb8a6c4: mov             x1, NULL
    // 0xb8a6c8: r2 = 6
    //     0xb8a6c8: movz            x2, #0x6
    // 0xb8a6cc: r0 = AllocateArray()
    //     0xb8a6cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8a6d0: mov             x2, x0
    // 0xb8a6d4: ldur            x0, [fp, #-0x38]
    // 0xb8a6d8: stur            x2, [fp, #-8]
    // 0xb8a6dc: StoreField: r2->field_f = r0
    //     0xb8a6dc: stur            w0, [x2, #0xf]
    // 0xb8a6e0: ldur            x0, [fp, #-0x10]
    // 0xb8a6e4: StoreField: r2->field_13 = r0
    //     0xb8a6e4: stur            w0, [x2, #0x13]
    // 0xb8a6e8: r16 = Instance_SizedBox
    //     0xb8a6e8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xb8a6ec: ldr             x16, [x16, #0x8b8]
    // 0xb8a6f0: ArrayStore: r2[0] = r16  ; List_4
    //     0xb8a6f0: stur            w16, [x2, #0x17]
    // 0xb8a6f4: r1 = <Widget>
    //     0xb8a6f4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8a6f8: r0 = AllocateGrowableArray()
    //     0xb8a6f8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8a6fc: mov             x1, x0
    // 0xb8a700: ldur            x0, [fp, #-8]
    // 0xb8a704: stur            x1, [fp, #-0x10]
    // 0xb8a708: StoreField: r1->field_f = r0
    //     0xb8a708: stur            w0, [x1, #0xf]
    // 0xb8a70c: r0 = 6
    //     0xb8a70c: movz            x0, #0x6
    // 0xb8a710: StoreField: r1->field_b = r0
    //     0xb8a710: stur            w0, [x1, #0xb]
    // 0xb8a714: r0 = Column()
    //     0xb8a714: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8a718: mov             x1, x0
    // 0xb8a71c: r0 = Instance_Axis
    //     0xb8a71c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb8a720: stur            x1, [fp, #-8]
    // 0xb8a724: StoreField: r1->field_f = r0
    //     0xb8a724: stur            w0, [x1, #0xf]
    // 0xb8a728: r0 = Instance_MainAxisAlignment
    //     0xb8a728: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8a72c: ldr             x0, [x0, #0xa08]
    // 0xb8a730: StoreField: r1->field_13 = r0
    //     0xb8a730: stur            w0, [x1, #0x13]
    // 0xb8a734: r0 = Instance_MainAxisSize
    //     0xb8a734: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb8a738: ldr             x0, [x0, #0xdd0]
    // 0xb8a73c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8a73c: stur            w0, [x1, #0x17]
    // 0xb8a740: r0 = Instance_CrossAxisAlignment
    //     0xb8a740: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb8a744: ldr             x0, [x0, #0x890]
    // 0xb8a748: StoreField: r1->field_1b = r0
    //     0xb8a748: stur            w0, [x1, #0x1b]
    // 0xb8a74c: r0 = Instance_VerticalDirection
    //     0xb8a74c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8a750: ldr             x0, [x0, #0xa20]
    // 0xb8a754: StoreField: r1->field_23 = r0
    //     0xb8a754: stur            w0, [x1, #0x23]
    // 0xb8a758: r0 = Instance_Clip
    //     0xb8a758: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8a75c: ldr             x0, [x0, #0x38]
    // 0xb8a760: StoreField: r1->field_2b = r0
    //     0xb8a760: stur            w0, [x1, #0x2b]
    // 0xb8a764: StoreField: r1->field_2f = rZR
    //     0xb8a764: stur            xzr, [x1, #0x2f]
    // 0xb8a768: ldur            x0, [fp, #-0x10]
    // 0xb8a76c: StoreField: r1->field_b = r0
    //     0xb8a76c: stur            w0, [x1, #0xb]
    // 0xb8a770: r0 = Padding()
    //     0xb8a770: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8a774: r1 = Instance_EdgeInsets
    //     0xb8a774: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb8a778: ldr             x1, [x1, #0x668]
    // 0xb8a77c: StoreField: r0->field_f = r1
    //     0xb8a77c: stur            w1, [x0, #0xf]
    // 0xb8a780: ldur            x1, [fp, #-8]
    // 0xb8a784: StoreField: r0->field_b = r1
    //     0xb8a784: stur            w1, [x0, #0xb]
    // 0xb8a788: LeaveFrame
    //     0xb8a788: mov             SP, fp
    //     0xb8a78c: ldp             fp, lr, [SP], #0x10
    // 0xb8a790: ret
    //     0xb8a790: ret             
    // 0xb8a794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8a794: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8a798: b               #0xb89fd0
    // 0xb8a79c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8a79c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8a7a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8a7a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8a7c4, size: 0x90
    // 0xb8a7c4: EnterFrame
    //     0xb8a7c4: stp             fp, lr, [SP, #-0x10]!
    //     0xb8a7c8: mov             fp, SP
    // 0xb8a7cc: AllocStack(0x18)
    //     0xb8a7cc: sub             SP, SP, #0x18
    // 0xb8a7d0: SetupParameters()
    //     0xb8a7d0: ldr             x0, [fp, #0x10]
    //     0xb8a7d4: ldur            w1, [x0, #0x17]
    //     0xb8a7d8: add             x1, x1, HEAP, lsl #32
    // 0xb8a7dc: CheckStackOverflow
    //     0xb8a7dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8a7e0: cmp             SP, x16
    //     0xb8a7e4: b.ls            #0xb8a848
    // 0xb8a7e8: LoadField: r0 = r1->field_f
    //     0xb8a7e8: ldur            w0, [x1, #0xf]
    // 0xb8a7ec: DecompressPointer r0
    //     0xb8a7ec: add             x0, x0, HEAP, lsl #32
    // 0xb8a7f0: LoadField: r2 = r0->field_b
    //     0xb8a7f0: ldur            w2, [x0, #0xb]
    // 0xb8a7f4: DecompressPointer r2
    //     0xb8a7f4: add             x2, x2, HEAP, lsl #32
    // 0xb8a7f8: cmp             w2, NULL
    // 0xb8a7fc: b.eq            #0xb8a850
    // 0xb8a800: LoadField: r0 = r1->field_13
    //     0xb8a800: ldur            w0, [x1, #0x13]
    // 0xb8a804: DecompressPointer r0
    //     0xb8a804: add             x0, x0, HEAP, lsl #32
    // 0xb8a808: LoadField: r1 = r2->field_1f
    //     0xb8a808: ldur            w1, [x2, #0x1f]
    // 0xb8a80c: DecompressPointer r1
    //     0xb8a80c: add             x1, x1, HEAP, lsl #32
    // 0xb8a810: r16 = "glasses"
    //     0xb8a810: add             x16, PP, #0xd, lsl #12  ; [pp+0xd808] "glasses"
    //     0xb8a814: ldr             x16, [x16, #0x808]
    // 0xb8a818: stp             x16, x1, [SP, #8]
    // 0xb8a81c: str             x0, [SP]
    // 0xb8a820: r4 = 0
    //     0xb8a820: movz            x4, #0
    // 0xb8a824: ldr             x0, [SP, #0x10]
    // 0xb8a828: r16 = UnlinkedCall_0x613b5c
    //     0xb8a828: add             x16, PP, #0x55, lsl #12  ; [pp+0x554c8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8a82c: add             x16, x16, #0x4c8
    // 0xb8a830: ldp             x5, lr, [x16]
    // 0xb8a834: blr             lr
    // 0xb8a838: r0 = Null
    //     0xb8a838: mov             x0, NULL
    // 0xb8a83c: LeaveFrame
    //     0xb8a83c: mov             SP, fp
    //     0xb8a840: ldp             fp, lr, [SP], #0x10
    // 0xb8a844: ret
    //     0xb8a844: ret             
    // 0xb8a848: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8a848: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8a84c: b               #0xb8a7e8
    // 0xb8a850: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8a850: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, WidgetRatings?) {
    // ** addr: 0xb8a854, size: 0x88
    // 0xb8a854: EnterFrame
    //     0xb8a854: stp             fp, lr, [SP, #-0x10]!
    //     0xb8a858: mov             fp, SP
    // 0xb8a85c: AllocStack(0x18)
    //     0xb8a85c: sub             SP, SP, #0x18
    // 0xb8a860: SetupParameters()
    //     0xb8a860: ldr             x0, [fp, #0x20]
    //     0xb8a864: ldur            w1, [x0, #0x17]
    //     0xb8a868: add             x1, x1, HEAP, lsl #32
    // 0xb8a86c: CheckStackOverflow
    //     0xb8a86c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8a870: cmp             SP, x16
    //     0xb8a874: b.ls            #0xb8a8d0
    // 0xb8a878: LoadField: r0 = r1->field_f
    //     0xb8a878: ldur            w0, [x1, #0xf]
    // 0xb8a87c: DecompressPointer r0
    //     0xb8a87c: add             x0, x0, HEAP, lsl #32
    // 0xb8a880: LoadField: r1 = r0->field_b
    //     0xb8a880: ldur            w1, [x0, #0xb]
    // 0xb8a884: DecompressPointer r1
    //     0xb8a884: add             x1, x1, HEAP, lsl #32
    // 0xb8a888: cmp             w1, NULL
    // 0xb8a88c: b.eq            #0xb8a8d8
    // 0xb8a890: LoadField: r0 = r1->field_1b
    //     0xb8a890: ldur            w0, [x1, #0x1b]
    // 0xb8a894: DecompressPointer r0
    //     0xb8a894: add             x0, x0, HEAP, lsl #32
    // 0xb8a898: ldr             x16, [fp, #0x18]
    // 0xb8a89c: stp             x16, x0, [SP, #8]
    // 0xb8a8a0: ldr             x16, [fp, #0x10]
    // 0xb8a8a4: str             x16, [SP]
    // 0xb8a8a8: r4 = 0
    //     0xb8a8a8: movz            x4, #0
    // 0xb8a8ac: ldr             x0, [SP, #0x10]
    // 0xb8a8b0: r16 = UnlinkedCall_0x613b5c
    //     0xb8a8b0: add             x16, PP, #0x55, lsl #12  ; [pp+0x554d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8a8b4: add             x16, x16, #0x4d8
    // 0xb8a8b8: ldp             x5, lr, [x16]
    // 0xb8a8bc: blr             lr
    // 0xb8a8c0: r0 = Null
    //     0xb8a8c0: mov             x0, NULL
    // 0xb8a8c4: LeaveFrame
    //     0xb8a8c4: mov             SP, fp
    //     0xb8a8c8: ldp             fp, lr, [SP], #0x10
    // 0xb8a8cc: ret
    //     0xb8a8cc: ret             
    // 0xb8a8d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8a8d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8a8d4: b               #0xb8a878
    // 0xb8a8d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8a8d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4053, size: 0x2c, field offset: 0xc
//   const constructor, 
class ProductTitleItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f988, size: 0x24
    // 0xc7f988: EnterFrame
    //     0xc7f988: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f98c: mov             fp, SP
    // 0xc7f990: mov             x0, x1
    // 0xc7f994: r1 = <ProductTitleItemView>
    //     0xc7f994: add             x1, PP, #0x48, lsl #12  ; [pp+0x48740] TypeArguments: <ProductTitleItemView>
    //     0xc7f998: ldr             x1, [x1, #0x740]
    // 0xc7f99c: r0 = _ProductTitleItemViewState()
    //     0xc7f99c: bl              #0xc7f9ac  ; Allocate_ProductTitleItemViewStateStub -> _ProductTitleItemViewState (size=0x14)
    // 0xc7f9a0: LeaveFrame
    //     0xc7f9a0: mov             SP, fp
    //     0xc7f9a4: ldp             fp, lr, [SP], #0x10
    // 0xc7f9a8: ret
    //     0xc7f9a8: ret             
  }
}
