// lib: , url: package:customer_app/app/presentation/views/glass/orders/orders_view.dart

// class id: 1049416, size: 0x8
class :: {
}

// class id: 4563, size: 0x14, field offset: 0x14
//   const constructor, 
class OrdersView extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14ef54c, size: 0xf0
    // 0x14ef54c: EnterFrame
    //     0x14ef54c: stp             fp, lr, [SP, #-0x10]!
    //     0x14ef550: mov             fp, SP
    // 0x14ef554: AllocStack(0x20)
    //     0x14ef554: sub             SP, SP, #0x20
    // 0x14ef558: SetupParameters(OrdersView this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x14ef558: stur            x1, [fp, #-8]
    //     0x14ef55c: mov             x16, x2
    //     0x14ef560: mov             x2, x1
    //     0x14ef564: mov             x1, x16
    //     0x14ef568: stur            x1, [fp, #-0x10]
    // 0x14ef56c: CheckStackOverflow
    //     0x14ef56c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ef570: cmp             SP, x16
    //     0x14ef574: b.ls            #0x14ef634
    // 0x14ef578: r1 = 2
    //     0x14ef578: movz            x1, #0x2
    // 0x14ef57c: r0 = AllocateContext()
    //     0x14ef57c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14ef580: ldur            x2, [fp, #-8]
    // 0x14ef584: stur            x0, [fp, #-0x18]
    // 0x14ef588: StoreField: r0->field_f = r2
    //     0x14ef588: stur            w2, [x0, #0xf]
    // 0x14ef58c: ldur            x1, [fp, #-0x10]
    // 0x14ef590: StoreField: r0->field_13 = r1
    //     0x14ef590: stur            w1, [x0, #0x13]
    // 0x14ef594: r0 = of()
    //     0x14ef594: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ef598: LoadField: r1 = r0->field_5b
    //     0x14ef598: ldur            w1, [x0, #0x5b]
    // 0x14ef59c: DecompressPointer r1
    //     0x14ef59c: add             x1, x1, HEAP, lsl #32
    // 0x14ef5a0: r0 = LoadClassIdInstr(r1)
    //     0x14ef5a0: ldur            x0, [x1, #-1]
    //     0x14ef5a4: ubfx            x0, x0, #0xc, #0x14
    // 0x14ef5a8: d0 = 0.030000
    //     0x14ef5a8: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x14ef5ac: ldr             d0, [x17, #0x238]
    // 0x14ef5b0: r0 = GDT[cid_x0 + -0xffa]()
    //     0x14ef5b0: sub             lr, x0, #0xffa
    //     0x14ef5b4: ldr             lr, [x21, lr, lsl #3]
    //     0x14ef5b8: blr             lr
    // 0x14ef5bc: stur            x0, [fp, #-0x10]
    // 0x14ef5c0: r0 = Obx()
    //     0x14ef5c0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14ef5c4: ldur            x2, [fp, #-0x18]
    // 0x14ef5c8: r1 = Function '<anonymous closure>':.
    //     0x14ef5c8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40158] AnonymousClosure: (0x14ef674), in [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::body (0x14ef54c)
    //     0x14ef5cc: ldr             x1, [x1, #0x158]
    // 0x14ef5d0: stur            x0, [fp, #-0x18]
    // 0x14ef5d4: r0 = AllocateClosure()
    //     0x14ef5d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ef5d8: mov             x1, x0
    // 0x14ef5dc: ldur            x0, [fp, #-0x18]
    // 0x14ef5e0: StoreField: r0->field_b = r1
    //     0x14ef5e0: stur            w1, [x0, #0xb]
    // 0x14ef5e4: r0 = WillPopScope()
    //     0x14ef5e4: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14ef5e8: mov             x3, x0
    // 0x14ef5ec: ldur            x0, [fp, #-0x18]
    // 0x14ef5f0: stur            x3, [fp, #-0x20]
    // 0x14ef5f4: StoreField: r3->field_b = r0
    //     0x14ef5f4: stur            w0, [x3, #0xb]
    // 0x14ef5f8: ldur            x2, [fp, #-8]
    // 0x14ef5fc: r1 = Function '_onBackPress@1596481806':.
    //     0x14ef5fc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40160] AnonymousClosure: (0x14ef63c), in [package:customer_app/app/presentation/views/basic/orders/orders_view.dart] OrdersView::_onBackPress (0x14057f0)
    //     0x14ef600: ldr             x1, [x1, #0x160]
    // 0x14ef604: r0 = AllocateClosure()
    //     0x14ef604: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ef608: mov             x1, x0
    // 0x14ef60c: ldur            x0, [fp, #-0x20]
    // 0x14ef610: StoreField: r0->field_f = r1
    //     0x14ef610: stur            w1, [x0, #0xf]
    // 0x14ef614: r0 = ColoredBox()
    //     0x14ef614: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0x14ef618: ldur            x1, [fp, #-0x10]
    // 0x14ef61c: StoreField: r0->field_f = r1
    //     0x14ef61c: stur            w1, [x0, #0xf]
    // 0x14ef620: ldur            x1, [fp, #-0x20]
    // 0x14ef624: StoreField: r0->field_b = r1
    //     0x14ef624: stur            w1, [x0, #0xb]
    // 0x14ef628: LeaveFrame
    //     0x14ef628: mov             SP, fp
    //     0x14ef62c: ldp             fp, lr, [SP], #0x10
    // 0x14ef630: ret
    //     0x14ef630: ret             
    // 0x14ef634: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ef634: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ef638: b               #0x14ef578
  }
  [closure] Future<bool> _onBackPress(dynamic) {
    // ** addr: 0x14ef63c, size: 0x38
    // 0x14ef63c: EnterFrame
    //     0x14ef63c: stp             fp, lr, [SP, #-0x10]!
    //     0x14ef640: mov             fp, SP
    // 0x14ef644: ldr             x0, [fp, #0x10]
    // 0x14ef648: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14ef648: ldur            w1, [x0, #0x17]
    // 0x14ef64c: DecompressPointer r1
    //     0x14ef64c: add             x1, x1, HEAP, lsl #32
    // 0x14ef650: CheckStackOverflow
    //     0x14ef650: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ef654: cmp             SP, x16
    //     0x14ef658: b.ls            #0x14ef66c
    // 0x14ef65c: r0 = _onBackPress()
    //     0x14ef65c: bl              #0x14057f0  ; [package:customer_app/app/presentation/views/basic/orders/orders_view.dart] OrdersView::_onBackPress
    // 0x14ef660: LeaveFrame
    //     0x14ef660: mov             SP, fp
    //     0x14ef664: ldp             fp, lr, [SP], #0x10
    // 0x14ef668: ret
    //     0x14ef668: ret             
    // 0x14ef66c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ef66c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ef670: b               #0x14ef65c
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x14ef674, size: 0x3ac
    // 0x14ef674: EnterFrame
    //     0x14ef674: stp             fp, lr, [SP, #-0x10]!
    //     0x14ef678: mov             fp, SP
    // 0x14ef67c: AllocStack(0x48)
    //     0x14ef67c: sub             SP, SP, #0x48
    // 0x14ef680: SetupParameters()
    //     0x14ef680: ldr             x0, [fp, #0x10]
    //     0x14ef684: ldur            w2, [x0, #0x17]
    //     0x14ef688: add             x2, x2, HEAP, lsl #32
    //     0x14ef68c: stur            x2, [fp, #-8]
    // 0x14ef690: CheckStackOverflow
    //     0x14ef690: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ef694: cmp             SP, x16
    //     0x14ef698: b.ls            #0x14efa18
    // 0x14ef69c: LoadField: r1 = r2->field_f
    //     0x14ef69c: ldur            w1, [x2, #0xf]
    // 0x14ef6a0: DecompressPointer r1
    //     0x14ef6a0: add             x1, x1, HEAP, lsl #32
    // 0x14ef6a4: r0 = controller()
    //     0x14ef6a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ef6a8: LoadField: r1 = r0->field_53
    //     0x14ef6a8: ldur            w1, [x0, #0x53]
    // 0x14ef6ac: DecompressPointer r1
    //     0x14ef6ac: add             x1, x1, HEAP, lsl #32
    // 0x14ef6b0: r0 = value()
    //     0x14ef6b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ef6b4: LoadField: r1 = r0->field_b
    //     0x14ef6b4: ldur            w1, [x0, #0xb]
    // 0x14ef6b8: DecompressPointer r1
    //     0x14ef6b8: add             x1, x1, HEAP, lsl #32
    // 0x14ef6bc: cmp             w1, NULL
    // 0x14ef6c0: b.eq            #0x14ef6ec
    // 0x14ef6c4: ldur            x2, [fp, #-8]
    // 0x14ef6c8: LoadField: r1 = r2->field_f
    //     0x14ef6c8: ldur            w1, [x2, #0xf]
    // 0x14ef6cc: DecompressPointer r1
    //     0x14ef6cc: add             x1, x1, HEAP, lsl #32
    // 0x14ef6d0: r0 = controller()
    //     0x14ef6d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ef6d4: LoadField: r1 = r0->field_63
    //     0x14ef6d4: ldur            w1, [x0, #0x63]
    // 0x14ef6d8: DecompressPointer r1
    //     0x14ef6d8: add             x1, x1, HEAP, lsl #32
    // 0x14ef6dc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14ef6dc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14ef6e0: r0 = toList()
    //     0x14ef6e0: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14ef6e4: LoadField: r1 = r0->field_b
    //     0x14ef6e4: ldur            w1, [x0, #0xb]
    // 0x14ef6e8: cbnz            w1, #0x14ef764
    // 0x14ef6ec: ldur            x2, [fp, #-8]
    // 0x14ef6f0: LoadField: r1 = r2->field_f
    //     0x14ef6f0: ldur            w1, [x2, #0xf]
    // 0x14ef6f4: DecompressPointer r1
    //     0x14ef6f4: add             x1, x1, HEAP, lsl #32
    // 0x14ef6f8: r0 = controller()
    //     0x14ef6f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ef6fc: LoadField: r1 = r0->field_53
    //     0x14ef6fc: ldur            w1, [x0, #0x53]
    // 0x14ef700: DecompressPointer r1
    //     0x14ef700: add             x1, x1, HEAP, lsl #32
    // 0x14ef704: r0 = value()
    //     0x14ef704: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ef708: LoadField: r1 = r0->field_b
    //     0x14ef708: ldur            w1, [x0, #0xb]
    // 0x14ef70c: DecompressPointer r1
    //     0x14ef70c: add             x1, x1, HEAP, lsl #32
    // 0x14ef710: cmp             w1, NULL
    // 0x14ef714: b.ne            #0x14ef734
    // 0x14ef718: r0 = Container()
    //     0x14ef718: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14ef71c: mov             x1, x0
    // 0x14ef720: stur            x0, [fp, #-0x10]
    // 0x14ef724: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14ef724: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14ef728: r0 = Container()
    //     0x14ef728: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14ef72c: ldur            x0, [fp, #-0x10]
    // 0x14ef730: b               #0x14ef73c
    // 0x14ef734: r0 = Instance_EmptyBagWidget
    //     0x14ef734: add             x0, PP, #0x36, lsl #12  ; [pp+0x36d38] Obj!EmptyBagWidget@d66d71
    //     0x14ef738: ldr             x0, [x0, #0xd38]
    // 0x14ef73c: stur            x0, [fp, #-0x10]
    // 0x14ef740: r0 = Center()
    //     0x14ef740: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14ef744: mov             x1, x0
    // 0x14ef748: r0 = Instance_Alignment
    //     0x14ef748: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14ef74c: ldr             x0, [x0, #0xb10]
    // 0x14ef750: StoreField: r1->field_f = r0
    //     0x14ef750: stur            w0, [x1, #0xf]
    // 0x14ef754: ldur            x0, [fp, #-0x10]
    // 0x14ef758: StoreField: r1->field_b = r0
    //     0x14ef758: stur            w0, [x1, #0xb]
    // 0x14ef75c: mov             x0, x1
    // 0x14ef760: b               #0x14efa0c
    // 0x14ef764: ldur            x2, [fp, #-8]
    // 0x14ef768: LoadField: r1 = r2->field_13
    //     0x14ef768: ldur            w1, [x2, #0x13]
    // 0x14ef76c: DecompressPointer r1
    //     0x14ef76c: add             x1, x1, HEAP, lsl #32
    // 0x14ef770: r0 = of()
    //     0x14ef770: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ef774: LoadField: r1 = r0->field_87
    //     0x14ef774: ldur            w1, [x0, #0x87]
    // 0x14ef778: DecompressPointer r1
    //     0x14ef778: add             x1, x1, HEAP, lsl #32
    // 0x14ef77c: LoadField: r0 = r1->field_7
    //     0x14ef77c: ldur            w0, [x1, #7]
    // 0x14ef780: DecompressPointer r0
    //     0x14ef780: add             x0, x0, HEAP, lsl #32
    // 0x14ef784: r16 = 16.000000
    //     0x14ef784: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x14ef788: ldr             x16, [x16, #0x188]
    // 0x14ef78c: r30 = Instance_Color
    //     0x14ef78c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14ef790: stp             lr, x16, [SP]
    // 0x14ef794: mov             x1, x0
    // 0x14ef798: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14ef798: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14ef79c: ldr             x4, [x4, #0xaa0]
    // 0x14ef7a0: r0 = copyWith()
    //     0x14ef7a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14ef7a4: stur            x0, [fp, #-0x10]
    // 0x14ef7a8: r0 = Text()
    //     0x14ef7a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14ef7ac: mov             x3, x0
    // 0x14ef7b0: r0 = "All Orders"
    //     0x14ef7b0: add             x0, PP, #0x40, lsl #12  ; [pp+0x40168] "All Orders"
    //     0x14ef7b4: ldr             x0, [x0, #0x168]
    // 0x14ef7b8: stur            x3, [fp, #-0x18]
    // 0x14ef7bc: StoreField: r3->field_b = r0
    //     0x14ef7bc: stur            w0, [x3, #0xb]
    // 0x14ef7c0: ldur            x0, [fp, #-0x10]
    // 0x14ef7c4: StoreField: r3->field_13 = r0
    //     0x14ef7c4: stur            w0, [x3, #0x13]
    // 0x14ef7c8: r1 = Null
    //     0x14ef7c8: mov             x1, NULL
    // 0x14ef7cc: r2 = 2
    //     0x14ef7cc: movz            x2, #0x2
    // 0x14ef7d0: r0 = AllocateArray()
    //     0x14ef7d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14ef7d4: mov             x2, x0
    // 0x14ef7d8: ldur            x0, [fp, #-0x18]
    // 0x14ef7dc: stur            x2, [fp, #-0x10]
    // 0x14ef7e0: StoreField: r2->field_f = r0
    //     0x14ef7e0: stur            w0, [x2, #0xf]
    // 0x14ef7e4: r1 = <Widget>
    //     0x14ef7e4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14ef7e8: r0 = AllocateGrowableArray()
    //     0x14ef7e8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14ef7ec: mov             x1, x0
    // 0x14ef7f0: ldur            x0, [fp, #-0x10]
    // 0x14ef7f4: stur            x1, [fp, #-0x18]
    // 0x14ef7f8: StoreField: r1->field_f = r0
    //     0x14ef7f8: stur            w0, [x1, #0xf]
    // 0x14ef7fc: r0 = 2
    //     0x14ef7fc: movz            x0, #0x2
    // 0x14ef800: StoreField: r1->field_b = r0
    //     0x14ef800: stur            w0, [x1, #0xb]
    // 0x14ef804: r0 = Column()
    //     0x14ef804: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14ef808: mov             x1, x0
    // 0x14ef80c: r0 = Instance_Axis
    //     0x14ef80c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14ef810: stur            x1, [fp, #-0x10]
    // 0x14ef814: StoreField: r1->field_f = r0
    //     0x14ef814: stur            w0, [x1, #0xf]
    // 0x14ef818: r2 = Instance_MainAxisAlignment
    //     0x14ef818: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14ef81c: ldr             x2, [x2, #0xa08]
    // 0x14ef820: StoreField: r1->field_13 = r2
    //     0x14ef820: stur            w2, [x1, #0x13]
    // 0x14ef824: r3 = Instance_MainAxisSize
    //     0x14ef824: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14ef828: ldr             x3, [x3, #0xa10]
    // 0x14ef82c: ArrayStore: r1[0] = r3  ; List_4
    //     0x14ef82c: stur            w3, [x1, #0x17]
    // 0x14ef830: r4 = Instance_CrossAxisAlignment
    //     0x14ef830: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14ef834: ldr             x4, [x4, #0x890]
    // 0x14ef838: StoreField: r1->field_1b = r4
    //     0x14ef838: stur            w4, [x1, #0x1b]
    // 0x14ef83c: r4 = Instance_VerticalDirection
    //     0x14ef83c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14ef840: ldr             x4, [x4, #0xa20]
    // 0x14ef844: StoreField: r1->field_23 = r4
    //     0x14ef844: stur            w4, [x1, #0x23]
    // 0x14ef848: r5 = Instance_Clip
    //     0x14ef848: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14ef84c: ldr             x5, [x5, #0x38]
    // 0x14ef850: StoreField: r1->field_2b = r5
    //     0x14ef850: stur            w5, [x1, #0x2b]
    // 0x14ef854: StoreField: r1->field_2f = rZR
    //     0x14ef854: stur            xzr, [x1, #0x2f]
    // 0x14ef858: ldur            x6, [fp, #-0x18]
    // 0x14ef85c: StoreField: r1->field_b = r6
    //     0x14ef85c: stur            w6, [x1, #0xb]
    // 0x14ef860: r0 = Align()
    //     0x14ef860: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14ef864: mov             x2, x0
    // 0x14ef868: r0 = Instance_Alignment
    //     0x14ef868: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f98] Obj!Alignment@d5a7a1
    //     0x14ef86c: ldr             x0, [x0, #0xf98]
    // 0x14ef870: stur            x2, [fp, #-0x18]
    // 0x14ef874: StoreField: r2->field_f = r0
    //     0x14ef874: stur            w0, [x2, #0xf]
    // 0x14ef878: ldur            x0, [fp, #-0x10]
    // 0x14ef87c: StoreField: r2->field_b = r0
    //     0x14ef87c: stur            w0, [x2, #0xb]
    // 0x14ef880: ldur            x0, [fp, #-8]
    // 0x14ef884: LoadField: r1 = r0->field_f
    //     0x14ef884: ldur            w1, [x0, #0xf]
    // 0x14ef888: DecompressPointer r1
    //     0x14ef888: add             x1, x1, HEAP, lsl #32
    // 0x14ef88c: r0 = controller()
    //     0x14ef88c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ef890: LoadField: r1 = r0->field_63
    //     0x14ef890: ldur            w1, [x0, #0x63]
    // 0x14ef894: DecompressPointer r1
    //     0x14ef894: add             x1, x1, HEAP, lsl #32
    // 0x14ef898: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14ef898: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14ef89c: r0 = toList()
    //     0x14ef89c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14ef8a0: LoadField: r3 = r0->field_b
    //     0x14ef8a0: ldur            w3, [x0, #0xb]
    // 0x14ef8a4: ldur            x2, [fp, #-8]
    // 0x14ef8a8: stur            x3, [fp, #-0x10]
    // 0x14ef8ac: r1 = Function '<anonymous closure>':.
    //     0x14ef8ac: add             x1, PP, #0x40, lsl #12  ; [pp+0x40170] AnonymousClosure: (0x14efa20), in [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::body (0x14ef54c)
    //     0x14ef8b0: ldr             x1, [x1, #0x170]
    // 0x14ef8b4: r0 = AllocateClosure()
    //     0x14ef8b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ef8b8: stur            x0, [fp, #-0x20]
    // 0x14ef8bc: r0 = ListView()
    //     0x14ef8bc: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14ef8c0: stur            x0, [fp, #-0x28]
    // 0x14ef8c4: r16 = Instance_Axis
    //     0x14ef8c4: ldr             x16, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14ef8c8: r30 = true
    //     0x14ef8c8: add             lr, NULL, #0x20  ; true
    // 0x14ef8cc: stp             lr, x16, [SP, #0x10]
    // 0x14ef8d0: r16 = Instance_ClampingScrollPhysics
    //     0x14ef8d0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d58] Obj!ClampingScrollPhysics@d558d1
    //     0x14ef8d4: ldr             x16, [x16, #0xd58]
    // 0x14ef8d8: r30 = true
    //     0x14ef8d8: add             lr, NULL, #0x20  ; true
    // 0x14ef8dc: stp             lr, x16, [SP]
    // 0x14ef8e0: mov             x1, x0
    // 0x14ef8e4: ldur            x2, [fp, #-0x20]
    // 0x14ef8e8: ldur            x3, [fp, #-0x10]
    // 0x14ef8ec: r4 = const [0, 0x7, 0x4, 0x3, physics, 0x5, primary, 0x6, scrollDirection, 0x3, shrinkWrap, 0x4, null]
    //     0x14ef8ec: add             x4, PP, #0x40, lsl #12  ; [pp+0x40178] List(13) [0, 0x7, 0x4, 0x3, "physics", 0x5, "primary", 0x6, "scrollDirection", 0x3, "shrinkWrap", 0x4, Null]
    //     0x14ef8f0: ldr             x4, [x4, #0x178]
    // 0x14ef8f4: r0 = ListView.builder()
    //     0x14ef8f4: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x14ef8f8: r1 = Null
    //     0x14ef8f8: mov             x1, NULL
    // 0x14ef8fc: r2 = 6
    //     0x14ef8fc: movz            x2, #0x6
    // 0x14ef900: r0 = AllocateArray()
    //     0x14ef900: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14ef904: mov             x2, x0
    // 0x14ef908: ldur            x0, [fp, #-0x18]
    // 0x14ef90c: stur            x2, [fp, #-0x10]
    // 0x14ef910: StoreField: r2->field_f = r0
    //     0x14ef910: stur            w0, [x2, #0xf]
    // 0x14ef914: r16 = Instance_SizedBox
    //     0x14ef914: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x14ef918: ldr             x16, [x16, #0x8f0]
    // 0x14ef91c: StoreField: r2->field_13 = r16
    //     0x14ef91c: stur            w16, [x2, #0x13]
    // 0x14ef920: ldur            x0, [fp, #-0x28]
    // 0x14ef924: ArrayStore: r2[0] = r0  ; List_4
    //     0x14ef924: stur            w0, [x2, #0x17]
    // 0x14ef928: r1 = <Widget>
    //     0x14ef928: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14ef92c: r0 = AllocateGrowableArray()
    //     0x14ef92c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14ef930: mov             x1, x0
    // 0x14ef934: ldur            x0, [fp, #-0x10]
    // 0x14ef938: stur            x1, [fp, #-0x18]
    // 0x14ef93c: StoreField: r1->field_f = r0
    //     0x14ef93c: stur            w0, [x1, #0xf]
    // 0x14ef940: r0 = 6
    //     0x14ef940: movz            x0, #0x6
    // 0x14ef944: StoreField: r1->field_b = r0
    //     0x14ef944: stur            w0, [x1, #0xb]
    // 0x14ef948: r0 = Column()
    //     0x14ef948: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14ef94c: mov             x1, x0
    // 0x14ef950: r0 = Instance_Axis
    //     0x14ef950: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14ef954: stur            x1, [fp, #-0x10]
    // 0x14ef958: StoreField: r1->field_f = r0
    //     0x14ef958: stur            w0, [x1, #0xf]
    // 0x14ef95c: r0 = Instance_MainAxisAlignment
    //     0x14ef95c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14ef960: ldr             x0, [x0, #0xa08]
    // 0x14ef964: StoreField: r1->field_13 = r0
    //     0x14ef964: stur            w0, [x1, #0x13]
    // 0x14ef968: r0 = Instance_MainAxisSize
    //     0x14ef968: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14ef96c: ldr             x0, [x0, #0xa10]
    // 0x14ef970: ArrayStore: r1[0] = r0  ; List_4
    //     0x14ef970: stur            w0, [x1, #0x17]
    // 0x14ef974: r0 = Instance_CrossAxisAlignment
    //     0x14ef974: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14ef978: ldr             x0, [x0, #0xa18]
    // 0x14ef97c: StoreField: r1->field_1b = r0
    //     0x14ef97c: stur            w0, [x1, #0x1b]
    // 0x14ef980: r0 = Instance_VerticalDirection
    //     0x14ef980: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14ef984: ldr             x0, [x0, #0xa20]
    // 0x14ef988: StoreField: r1->field_23 = r0
    //     0x14ef988: stur            w0, [x1, #0x23]
    // 0x14ef98c: r0 = Instance_Clip
    //     0x14ef98c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14ef990: ldr             x0, [x0, #0x38]
    // 0x14ef994: StoreField: r1->field_2b = r0
    //     0x14ef994: stur            w0, [x1, #0x2b]
    // 0x14ef998: StoreField: r1->field_2f = rZR
    //     0x14ef998: stur            xzr, [x1, #0x2f]
    // 0x14ef99c: ldur            x0, [fp, #-0x18]
    // 0x14ef9a0: StoreField: r1->field_b = r0
    //     0x14ef9a0: stur            w0, [x1, #0xb]
    // 0x14ef9a4: r0 = Padding()
    //     0x14ef9a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14ef9a8: mov             x3, x0
    // 0x14ef9ac: r0 = Instance_EdgeInsets
    //     0x14ef9ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0x14ef9b0: ldr             x0, [x0, #0xd0]
    // 0x14ef9b4: stur            x3, [fp, #-0x18]
    // 0x14ef9b8: StoreField: r3->field_f = r0
    //     0x14ef9b8: stur            w0, [x3, #0xf]
    // 0x14ef9bc: ldur            x0, [fp, #-0x10]
    // 0x14ef9c0: StoreField: r3->field_b = r0
    //     0x14ef9c0: stur            w0, [x3, #0xb]
    // 0x14ef9c4: ldur            x2, [fp, #-8]
    // 0x14ef9c8: r1 = Function '<anonymous closure>':.
    //     0x14ef9c8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40180] AnonymousClosure: (0x8a41fc), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x14ef9cc: ldr             x1, [x1, #0x180]
    // 0x14ef9d0: r0 = AllocateClosure()
    //     0x14ef9d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ef9d4: ldur            x2, [fp, #-8]
    // 0x14ef9d8: r1 = Function '<anonymous closure>':.
    //     0x14ef9d8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40188] AnonymousClosure: (0x8a3de0), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x14ef9dc: ldr             x1, [x1, #0x188]
    // 0x14ef9e0: stur            x0, [fp, #-8]
    // 0x14ef9e4: r0 = AllocateClosure()
    //     0x14ef9e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ef9e8: stur            x0, [fp, #-0x10]
    // 0x14ef9ec: r0 = PagingView()
    //     0x14ef9ec: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x14ef9f0: mov             x1, x0
    // 0x14ef9f4: ldur            x2, [fp, #-0x18]
    // 0x14ef9f8: ldur            x3, [fp, #-0x10]
    // 0x14ef9fc: ldur            x5, [fp, #-8]
    // 0x14efa00: stur            x0, [fp, #-8]
    // 0x14efa04: r0 = PagingView()
    //     0x14efa04: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x14efa08: ldur            x0, [fp, #-8]
    // 0x14efa0c: LeaveFrame
    //     0x14efa0c: mov             SP, fp
    //     0x14efa10: ldp             fp, lr, [SP], #0x10
    // 0x14efa14: ret
    //     0x14efa14: ret             
    // 0x14efa18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14efa18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14efa1c: b               #0x14ef69c
  }
  [closure] OrderItemCard <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14efa20, size: 0x1c4
    // 0x14efa20: EnterFrame
    //     0x14efa20: stp             fp, lr, [SP, #-0x10]!
    //     0x14efa24: mov             fp, SP
    // 0x14efa28: AllocStack(0x28)
    //     0x14efa28: sub             SP, SP, #0x28
    // 0x14efa2c: SetupParameters()
    //     0x14efa2c: ldr             x0, [fp, #0x20]
    //     0x14efa30: ldur            w2, [x0, #0x17]
    //     0x14efa34: add             x2, x2, HEAP, lsl #32
    //     0x14efa38: stur            x2, [fp, #-0x10]
    // 0x14efa3c: CheckStackOverflow
    //     0x14efa3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14efa40: cmp             SP, x16
    //     0x14efa44: b.ls            #0x14efbd8
    // 0x14efa48: LoadField: r0 = r2->field_f
    //     0x14efa48: ldur            w0, [x2, #0xf]
    // 0x14efa4c: DecompressPointer r0
    //     0x14efa4c: add             x0, x0, HEAP, lsl #32
    // 0x14efa50: mov             x1, x0
    // 0x14efa54: stur            x0, [fp, #-8]
    // 0x14efa58: r0 = controller()
    //     0x14efa58: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14efa5c: LoadField: r1 = r0->field_63
    //     0x14efa5c: ldur            w1, [x0, #0x63]
    // 0x14efa60: DecompressPointer r1
    //     0x14efa60: add             x1, x1, HEAP, lsl #32
    // 0x14efa64: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14efa64: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14efa68: r0 = toList()
    //     0x14efa68: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14efa6c: mov             x2, x0
    // 0x14efa70: LoadField: r0 = r2->field_b
    //     0x14efa70: ldur            w0, [x2, #0xb]
    // 0x14efa74: ldr             x1, [fp, #0x10]
    // 0x14efa78: r3 = LoadInt32Instr(r1)
    //     0x14efa78: sbfx            x3, x1, #1, #0x1f
    //     0x14efa7c: tbz             w1, #0, #0x14efa84
    //     0x14efa80: ldur            x3, [x1, #7]
    // 0x14efa84: r1 = LoadInt32Instr(r0)
    //     0x14efa84: sbfx            x1, x0, #1, #0x1f
    // 0x14efa88: mov             x0, x1
    // 0x14efa8c: mov             x1, x3
    // 0x14efa90: cmp             x1, x0
    // 0x14efa94: b.hs            #0x14efbe0
    // 0x14efa98: LoadField: r0 = r2->field_f
    //     0x14efa98: ldur            w0, [x2, #0xf]
    // 0x14efa9c: DecompressPointer r0
    //     0x14efa9c: add             x0, x0, HEAP, lsl #32
    // 0x14efaa0: ArrayLoad: r4 = r0[r3]  ; Unknown_4
    //     0x14efaa0: add             x16, x0, x3, lsl #2
    //     0x14efaa4: ldur            w4, [x16, #0xf]
    // 0x14efaa8: DecompressPointer r4
    //     0x14efaa8: add             x4, x4, HEAP, lsl #32
    // 0x14efaac: mov             x0, x4
    // 0x14efab0: stur            x4, [fp, #-0x18]
    // 0x14efab4: r2 = Null
    //     0x14efab4: mov             x2, NULL
    // 0x14efab8: r1 = Null
    //     0x14efab8: mov             x1, NULL
    // 0x14efabc: r4 = LoadClassIdInstr(r0)
    //     0x14efabc: ldur            x4, [x0, #-1]
    //     0x14efac0: ubfx            x4, x4, #0xc, #0x14
    // 0x14efac4: r17 = 5137
    //     0x14efac4: movz            x17, #0x1411
    // 0x14efac8: cmp             x4, x17
    // 0x14efacc: b.eq            #0x14efae4
    // 0x14efad0: r8 = OrderData
    //     0x14efad0: add             x8, PP, #0x36, lsl #12  ; [pp+0x36d98] Type: OrderData
    //     0x14efad4: ldr             x8, [x8, #0xd98]
    // 0x14efad8: r3 = Null
    //     0x14efad8: add             x3, PP, #0x40, lsl #12  ; [pp+0x40190] Null
    //     0x14efadc: ldr             x3, [x3, #0x190]
    // 0x14efae0: r0 = DefaultTypeTest()
    //     0x14efae0: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x14efae4: ldur            x2, [fp, #-0x10]
    // 0x14efae8: LoadField: r0 = r2->field_f
    //     0x14efae8: ldur            w0, [x2, #0xf]
    // 0x14efaec: DecompressPointer r0
    //     0x14efaec: add             x0, x0, HEAP, lsl #32
    // 0x14efaf0: stur            x0, [fp, #-0x20]
    // 0x14efaf4: r0 = OrderItemCard()
    //     0x14efaf4: bl              #0x14efbe4  ; AllocateOrderItemCardStub -> OrderItemCard (size=0x2c)
    // 0x14efaf8: mov             x3, x0
    // 0x14efafc: ldur            x0, [fp, #-0x18]
    // 0x14efb00: stur            x3, [fp, #-0x28]
    // 0x14efb04: StoreField: r3->field_b = r0
    //     0x14efb04: stur            w0, [x3, #0xb]
    // 0x14efb08: ldur            x2, [fp, #-8]
    // 0x14efb0c: r1 = Function 'cancelOrder':.
    //     0x14efb0c: add             x1, PP, #0x40, lsl #12  ; [pp+0x401a0] AnonymousClosure: (0x14f0ec8), in [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::cancelOrder (0x14f0870)
    //     0x14efb10: ldr             x1, [x1, #0x1a0]
    // 0x14efb14: r0 = AllocateClosure()
    //     0x14efb14: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14efb18: mov             x1, x0
    // 0x14efb1c: ldur            x0, [fp, #-0x28]
    // 0x14efb20: StoreField: r0->field_f = r1
    //     0x14efb20: stur            w1, [x0, #0xf]
    // 0x14efb24: ldur            x2, [fp, #-8]
    // 0x14efb28: r1 = Function 'cancelOrderWithFreeProduct':.
    //     0x14efb28: add             x1, PP, #0x40, lsl #12  ; [pp+0x401a8] AnonymousClosure: (0x14f03a8), in [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::cancelOrderWithFreeProduct (0x14f03ec)
    //     0x14efb2c: ldr             x1, [x1, #0x1a8]
    // 0x14efb30: r0 = AllocateClosure()
    //     0x14efb30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14efb34: mov             x1, x0
    // 0x14efb38: ldur            x0, [fp, #-0x28]
    // 0x14efb3c: StoreField: r0->field_13 = r1
    //     0x14efb3c: stur            w1, [x0, #0x13]
    // 0x14efb40: ldur            x2, [fp, #-8]
    // 0x14efb44: r1 = Function 'openProductDetail':.
    //     0x14efb44: add             x1, PP, #0x40, lsl #12  ; [pp+0x401b0] AnonymousClosure: (0x14f013c), in [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::openProductDetail (0x14f0188)
    //     0x14efb48: ldr             x1, [x1, #0x1b0]
    // 0x14efb4c: r0 = AllocateClosure()
    //     0x14efb4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14efb50: mov             x1, x0
    // 0x14efb54: ldur            x0, [fp, #-0x28]
    // 0x14efb58: StoreField: r0->field_1f = r1
    //     0x14efb58: stur            w1, [x0, #0x1f]
    // 0x14efb5c: ldur            x2, [fp, #-0x20]
    // 0x14efb60: r1 = Function 'cancelReturn':.
    //     0x14efb60: add             x1, PP, #0x40, lsl #12  ; [pp+0x401b8] AnonymousClosure: (0x14effb4), in [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::cancelReturn (0x14efff8)
    //     0x14efb64: ldr             x1, [x1, #0x1b8]
    // 0x14efb68: r0 = AllocateClosure()
    //     0x14efb68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14efb6c: mov             x1, x0
    // 0x14efb70: ldur            x0, [fp, #-0x28]
    // 0x14efb74: ArrayStore: r0[0] = r1  ; List_4
    //     0x14efb74: stur            w1, [x0, #0x17]
    // 0x14efb78: ldur            x2, [fp, #-0x20]
    // 0x14efb7c: r1 = Function 'cancelExchange':.
    //     0x14efb7c: add             x1, PP, #0x40, lsl #12  ; [pp+0x401c0] AnonymousClosure: (0x14efdc0), in [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::cancelExchange (0x14efe28)
    //     0x14efb80: ldr             x1, [x1, #0x1c0]
    // 0x14efb84: r0 = AllocateClosure()
    //     0x14efb84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14efb88: mov             x1, x0
    // 0x14efb8c: ldur            x0, [fp, #-0x28]
    // 0x14efb90: StoreField: r0->field_1b = r1
    //     0x14efb90: stur            w1, [x0, #0x1b]
    // 0x14efb94: ldur            x2, [fp, #-0x10]
    // 0x14efb98: r1 = Function '<anonymous closure>':.
    //     0x14efb98: add             x1, PP, #0x40, lsl #12  ; [pp+0x401c8] AnonymousClosure: (0x8adc64), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x14efb9c: ldr             x1, [x1, #0x1c8]
    // 0x14efba0: r0 = AllocateClosure()
    //     0x14efba0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14efba4: mov             x1, x0
    // 0x14efba8: ldur            x0, [fp, #-0x28]
    // 0x14efbac: StoreField: r0->field_23 = r1
    //     0x14efbac: stur            w1, [x0, #0x23]
    // 0x14efbb0: ldur            x2, [fp, #-0x10]
    // 0x14efbb4: r1 = Function '<anonymous closure>':.
    //     0x14efbb4: add             x1, PP, #0x40, lsl #12  ; [pp+0x401d0] AnonymousClosure: (0x14efbf0), in [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::body (0x14ef54c)
    //     0x14efbb8: ldr             x1, [x1, #0x1d0]
    // 0x14efbbc: r0 = AllocateClosure()
    //     0x14efbbc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14efbc0: mov             x1, x0
    // 0x14efbc4: ldur            x0, [fp, #-0x28]
    // 0x14efbc8: StoreField: r0->field_27 = r1
    //     0x14efbc8: stur            w1, [x0, #0x27]
    // 0x14efbcc: LeaveFrame
    //     0x14efbcc: mov             SP, fp
    //     0x14efbd0: ldp             fp, lr, [SP], #0x10
    // 0x14efbd4: ret
    //     0x14efbd4: ret             
    // 0x14efbd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14efbd8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14efbdc: b               #0x14efa48
    // 0x14efbe0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14efbe0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, double, Items, String) {
    // ** addr: 0x14efbf0, size: 0x1d0
    // 0x14efbf0: EnterFrame
    //     0x14efbf0: stp             fp, lr, [SP, #-0x10]!
    //     0x14efbf4: mov             fp, SP
    // 0x14efbf8: AllocStack(0x28)
    //     0x14efbf8: sub             SP, SP, #0x28
    // 0x14efbfc: SetupParameters()
    //     0x14efbfc: ldr             x0, [fp, #0x28]
    //     0x14efc00: ldur            w2, [x0, #0x17]
    //     0x14efc04: add             x2, x2, HEAP, lsl #32
    //     0x14efc08: stur            x2, [fp, #-8]
    // 0x14efc0c: CheckStackOverflow
    //     0x14efc0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14efc10: cmp             SP, x16
    //     0x14efc14: b.ls            #0x14efdb8
    // 0x14efc18: LoadField: r1 = r2->field_f
    //     0x14efc18: ldur            w1, [x2, #0xf]
    // 0x14efc1c: DecompressPointer r1
    //     0x14efc1c: add             x1, x1, HEAP, lsl #32
    // 0x14efc20: r0 = controller()
    //     0x14efc20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14efc24: mov             x1, x0
    // 0x14efc28: ldr             x0, [fp, #0x10]
    // 0x14efc2c: StoreField: r1->field_77 = r0
    //     0x14efc2c: stur            w0, [x1, #0x77]
    //     0x14efc30: ldurb           w16, [x1, #-1]
    //     0x14efc34: ldurb           w17, [x0, #-1]
    //     0x14efc38: and             x16, x17, x16, lsr #2
    //     0x14efc3c: tst             x16, HEAP, lsr #32
    //     0x14efc40: b.eq            #0x14efc48
    //     0x14efc44: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x14efc48: ldr             x1, [fp, #0x10]
    // 0x14efc4c: r0 = LoadClassIdInstr(r1)
    //     0x14efc4c: ldur            x0, [x1, #-1]
    //     0x14efc50: ubfx            x0, x0, #0xc, #0x14
    // 0x14efc54: r16 = "stars"
    //     0x14efc54: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb28] "stars"
    //     0x14efc58: ldr             x16, [x16, #0xb28]
    // 0x14efc5c: stp             x16, x1, [SP]
    // 0x14efc60: mov             lr, x0
    // 0x14efc64: ldr             lr, [x21, lr, lsl #3]
    // 0x14efc68: blr             lr
    // 0x14efc6c: tbz             w0, #4, #0x14efd34
    // 0x14efc70: ldr             x1, [fp, #0x18]
    // 0x14efc74: ldr             x0, [fp, #0x10]
    // 0x14efc78: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14efc78: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14efc7c: ldr             x0, [x0, #0x1c80]
    //     0x14efc80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14efc84: cmp             w0, w16
    //     0x14efc88: b.ne            #0x14efc94
    //     0x14efc8c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14efc90: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14efc94: r1 = Null
    //     0x14efc94: mov             x1, NULL
    // 0x14efc98: r2 = 8
    //     0x14efc98: movz            x2, #0x8
    // 0x14efc9c: r0 = AllocateArray()
    //     0x14efc9c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14efca0: r16 = "order_id"
    //     0x14efca0: add             x16, PP, #0xe, lsl #12  ; [pp+0xea38] "order_id"
    //     0x14efca4: ldr             x16, [x16, #0xa38]
    // 0x14efca8: StoreField: r0->field_f = r16
    //     0x14efca8: stur            w16, [x0, #0xf]
    // 0x14efcac: ldr             x2, [fp, #0x18]
    // 0x14efcb0: LoadField: r1 = r2->field_7
    //     0x14efcb0: ldur            w1, [x2, #7]
    // 0x14efcb4: DecompressPointer r1
    //     0x14efcb4: add             x1, x1, HEAP, lsl #32
    // 0x14efcb8: StoreField: r0->field_13 = r1
    //     0x14efcb8: stur            w1, [x0, #0x13]
    // 0x14efcbc: r16 = "coming_from"
    //     0x14efcbc: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x14efcc0: ldr             x16, [x16, #0x328]
    // 0x14efcc4: ArrayStore: r0[0] = r16  ; List_4
    //     0x14efcc4: stur            w16, [x0, #0x17]
    // 0x14efcc8: ldr             x1, [fp, #0x10]
    // 0x14efccc: StoreField: r0->field_1b = r1
    //     0x14efccc: stur            w1, [x0, #0x1b]
    // 0x14efcd0: r16 = <String, String?>
    //     0x14efcd0: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0x14efcd4: ldr             x16, [x16, #0x3c8]
    // 0x14efcd8: stp             x0, x16, [SP]
    // 0x14efcdc: r0 = Map._fromLiteral()
    //     0x14efcdc: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x14efce0: r16 = "/rating_review_for_order"
    //     0x14efce0: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9b8] "/rating_review_for_order"
    //     0x14efce4: ldr             x16, [x16, #0x9b8]
    // 0x14efce8: stp             x16, NULL, [SP, #8]
    // 0x14efcec: str             x0, [SP]
    // 0x14efcf0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x14efcf0: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x14efcf4: ldr             x4, [x4, #0x438]
    // 0x14efcf8: r0 = GetNavigation.toNamed()
    //     0x14efcf8: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x14efcfc: stur            x0, [fp, #-0x10]
    // 0x14efd00: cmp             w0, NULL
    // 0x14efd04: b.eq            #0x14efda8
    // 0x14efd08: ldur            x2, [fp, #-8]
    // 0x14efd0c: r1 = Function '<anonymous closure>':.
    //     0x14efd0c: add             x1, PP, #0x40, lsl #12  ; [pp+0x401d8] AnonymousClosure: (0x8adbf4), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x14efd10: ldr             x1, [x1, #0x1d8]
    // 0x14efd14: r0 = AllocateClosure()
    //     0x14efd14: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14efd18: r16 = <Null?>
    //     0x14efd18: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x14efd1c: ldur            lr, [fp, #-0x10]
    // 0x14efd20: stp             lr, x16, [SP, #8]
    // 0x14efd24: str             x0, [SP]
    // 0x14efd28: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14efd28: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14efd2c: r0 = then()
    //     0x14efd2c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x14efd30: b               #0x14efda8
    // 0x14efd34: ldr             x3, [fp, #0x20]
    // 0x14efd38: ldr             x2, [fp, #0x18]
    // 0x14efd3c: ldur            x0, [fp, #-8]
    // 0x14efd40: LoadField: r1 = r0->field_f
    //     0x14efd40: ldur            w1, [x0, #0xf]
    // 0x14efd44: DecompressPointer r1
    //     0x14efd44: add             x1, x1, HEAP, lsl #32
    // 0x14efd48: r0 = controller()
    //     0x14efd48: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14efd4c: mov             x1, x0
    // 0x14efd50: ldr             x0, [fp, #0x18]
    // 0x14efd54: StoreField: r1->field_8b = r0
    //     0x14efd54: stur            w0, [x1, #0x8b]
    //     0x14efd58: ldurb           w16, [x1, #-1]
    //     0x14efd5c: ldurb           w17, [x0, #-1]
    //     0x14efd60: and             x16, x17, x16, lsr #2
    //     0x14efd64: tst             x16, HEAP, lsr #32
    //     0x14efd68: b.eq            #0x14efd70
    //     0x14efd6c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x14efd70: ldur            x0, [fp, #-8]
    // 0x14efd74: LoadField: r1 = r0->field_f
    //     0x14efd74: ldur            w1, [x0, #0xf]
    // 0x14efd78: DecompressPointer r1
    //     0x14efd78: add             x1, x1, HEAP, lsl #32
    // 0x14efd7c: r0 = controller()
    //     0x14efd7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14efd80: mov             x1, x0
    // 0x14efd84: ldr             x0, [fp, #0x20]
    // 0x14efd88: LoadField: d0 = r0->field_7
    //     0x14efd88: ldur            d0, [x0, #7]
    // 0x14efd8c: StoreField: r1->field_8f = d0
    //     0x14efd8c: stur            d0, [x1, #0x8f]
    // 0x14efd90: ldur            x0, [fp, #-8]
    // 0x14efd94: LoadField: r1 = r0->field_f
    //     0x14efd94: ldur            w1, [x0, #0xf]
    // 0x14efd98: DecompressPointer r1
    //     0x14efd98: add             x1, x1, HEAP, lsl #32
    // 0x14efd9c: r0 = controller()
    //     0x14efd9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14efda0: mov             x1, x0
    // 0x14efda4: r0 = createReview()
    //     0x14efda4: bl              #0x8aa48c  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::createReview
    // 0x14efda8: r0 = Null
    //     0x14efda8: mov             x0, NULL
    // 0x14efdac: LeaveFrame
    //     0x14efdac: mov             SP, fp
    //     0x14efdb0: ldp             fp, lr, [SP], #0x10
    // 0x14efdb4: ret
    //     0x14efdb4: ret             
    // 0x14efdb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14efdb8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14efdbc: b               #0x14efc18
  }
  [closure] void cancelExchange(dynamic, BuildContext, CancelExchangeRequestWidget, String, bool, bool, bool, String, String, String) {
    // ** addr: 0x14efdc0, size: 0x68
    // 0x14efdc0: EnterFrame
    //     0x14efdc0: stp             fp, lr, [SP, #-0x10]!
    //     0x14efdc4: mov             fp, SP
    // 0x14efdc8: AllocStack(0x20)
    //     0x14efdc8: sub             SP, SP, #0x20
    // 0x14efdcc: SetupParameters()
    //     0x14efdcc: ldr             x0, [fp, #0x58]
    //     0x14efdd0: ldur            w1, [x0, #0x17]
    //     0x14efdd4: add             x1, x1, HEAP, lsl #32
    // 0x14efdd8: CheckStackOverflow
    //     0x14efdd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14efddc: cmp             SP, x16
    //     0x14efde0: b.ls            #0x14efe20
    // 0x14efde4: ldr             x16, [fp, #0x28]
    // 0x14efde8: ldr             lr, [fp, #0x20]
    // 0x14efdec: stp             lr, x16, [SP, #0x10]
    // 0x14efdf0: ldr             x16, [fp, #0x18]
    // 0x14efdf4: ldr             lr, [fp, #0x10]
    // 0x14efdf8: stp             lr, x16, [SP]
    // 0x14efdfc: ldr             x2, [fp, #0x50]
    // 0x14efe00: ldr             x3, [fp, #0x48]
    // 0x14efe04: ldr             x5, [fp, #0x40]
    // 0x14efe08: ldr             x6, [fp, #0x38]
    // 0x14efe0c: ldr             x7, [fp, #0x30]
    // 0x14efe10: r0 = cancelExchange()
    //     0x14efe10: bl              #0x14efe28  ; [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::cancelExchange
    // 0x14efe14: LeaveFrame
    //     0x14efe14: mov             SP, fp
    //     0x14efe18: ldp             fp, lr, [SP], #0x10
    // 0x14efe1c: ret
    //     0x14efe1c: ret             
    // 0x14efe20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14efe20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14efe24: b               #0x14efde4
  }
  _ cancelExchange(/* No info */) {
    // ** addr: 0x14efe28, size: 0xe4
    // 0x14efe28: EnterFrame
    //     0x14efe28: stp             fp, lr, [SP, #-0x10]!
    //     0x14efe2c: mov             fp, SP
    // 0x14efe30: AllocStack(0x60)
    //     0x14efe30: sub             SP, SP, #0x60
    // 0x14efe34: SetupParameters(OrdersView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r7, fp-0x28 */, dynamic _ /* r7 => r6, fp-0x30 */)
    //     0x14efe34: stur            x6, [fp, #-0x28]
    //     0x14efe38: mov             x16, x7
    //     0x14efe3c: mov             x7, x6
    //     0x14efe40: mov             x6, x16
    //     0x14efe44: stur            x1, [fp, #-8]
    //     0x14efe48: stur            x2, [fp, #-0x10]
    //     0x14efe4c: stur            x3, [fp, #-0x18]
    //     0x14efe50: stur            x5, [fp, #-0x20]
    //     0x14efe54: stur            x6, [fp, #-0x30]
    // 0x14efe58: CheckStackOverflow
    //     0x14efe58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14efe5c: cmp             SP, x16
    //     0x14efe60: b.ls            #0x14eff04
    // 0x14efe64: r1 = 3
    //     0x14efe64: movz            x1, #0x3
    // 0x14efe68: r0 = AllocateContext()
    //     0x14efe68: bl              #0x16f6108  ; AllocateContextStub
    // 0x14efe6c: ldur            x1, [fp, #-8]
    // 0x14efe70: stur            x0, [fp, #-0x38]
    // 0x14efe74: StoreField: r0->field_f = r1
    //     0x14efe74: stur            w1, [x0, #0xf]
    // 0x14efe78: ldur            x2, [fp, #-0x18]
    // 0x14efe7c: StoreField: r0->field_13 = r2
    //     0x14efe7c: stur            w2, [x0, #0x13]
    // 0x14efe80: ldur            x2, [fp, #-0x20]
    // 0x14efe84: ArrayStore: r0[0] = r2  ; List_4
    //     0x14efe84: stur            w2, [x0, #0x17]
    // 0x14efe88: r0 = controller()
    //     0x14efe88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14efe8c: mov             x1, x0
    // 0x14efe90: ldur            x0, [fp, #-0x38]
    // 0x14efe94: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x14efe94: ldur            w2, [x0, #0x17]
    // 0x14efe98: DecompressPointer r2
    //     0x14efe98: add             x2, x2, HEAP, lsl #32
    // 0x14efe9c: ldr             x16, [fp, #0x20]
    // 0x14efea0: stp             x2, x16, [SP]
    // 0x14efea4: ldr             x2, [fp, #0x18]
    // 0x14efea8: ldr             x3, [fp, #0x10]
    // 0x14efeac: ldr             x5, [fp, #0x28]
    // 0x14efeb0: ldur            x6, [fp, #-0x30]
    // 0x14efeb4: ldur            x7, [fp, #-0x28]
    // 0x14efeb8: r0 = exchangeCancelRequestCTAPostEvent()
    //     0x14efeb8: bl              #0x8ae250  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::exchangeCancelRequestCTAPostEvent
    // 0x14efebc: ldur            x2, [fp, #-0x38]
    // 0x14efec0: r1 = Function '<anonymous closure>':.
    //     0x14efec0: add             x1, PP, #0x40, lsl #12  ; [pp+0x401e0] AnonymousClosure: (0x14eff0c), in [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::cancelExchange (0x14efe28)
    //     0x14efec4: ldr             x1, [x1, #0x1e0]
    // 0x14efec8: r0 = AllocateClosure()
    //     0x14efec8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14efecc: stp             x0, NULL, [SP, #0x18]
    // 0x14efed0: ldur            x16, [fp, #-0x10]
    // 0x14efed4: r30 = true
    //     0x14efed4: add             lr, NULL, #0x20  ; true
    // 0x14efed8: stp             lr, x16, [SP, #8]
    // 0x14efedc: r16 = Instance_RoundedRectangleBorder
    //     0x14efedc: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x14efee0: ldr             x16, [x16, #0xc78]
    // 0x14efee4: str             x16, [SP]
    // 0x14efee8: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0x14efee8: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0x14efeec: ldr             x4, [x4, #0xb20]
    // 0x14efef0: r0 = showModalBottomSheet()
    //     0x14efef0: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x14efef4: r0 = Null
    //     0x14efef4: mov             x0, NULL
    // 0x14efef8: LeaveFrame
    //     0x14efef8: mov             SP, fp
    //     0x14efefc: ldp             fp, lr, [SP], #0x10
    // 0x14eff00: ret
    //     0x14eff00: ret             
    // 0x14eff04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14eff04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14eff08: b               #0x14efe64
  }
  [closure] CancelExchangeBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x14eff0c, size: 0x9c
    // 0x14eff0c: EnterFrame
    //     0x14eff0c: stp             fp, lr, [SP, #-0x10]!
    //     0x14eff10: mov             fp, SP
    // 0x14eff14: AllocStack(0x20)
    //     0x14eff14: sub             SP, SP, #0x20
    // 0x14eff18: SetupParameters()
    //     0x14eff18: ldr             x0, [fp, #0x18]
    //     0x14eff1c: ldur            w1, [x0, #0x17]
    //     0x14eff20: add             x1, x1, HEAP, lsl #32
    // 0x14eff24: CheckStackOverflow
    //     0x14eff24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14eff28: cmp             SP, x16
    //     0x14eff2c: b.ls            #0x14effa0
    // 0x14eff30: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14eff30: ldur            w0, [x1, #0x17]
    // 0x14eff34: DecompressPointer r0
    //     0x14eff34: add             x0, x0, HEAP, lsl #32
    // 0x14eff38: stur            x0, [fp, #-0x10]
    // 0x14eff3c: LoadField: r2 = r1->field_13
    //     0x14eff3c: ldur            w2, [x1, #0x13]
    // 0x14eff40: DecompressPointer r2
    //     0x14eff40: add             x2, x2, HEAP, lsl #32
    // 0x14eff44: stur            x2, [fp, #-8]
    // 0x14eff48: LoadField: r3 = r1->field_f
    //     0x14eff48: ldur            w3, [x1, #0xf]
    // 0x14eff4c: DecompressPointer r3
    //     0x14eff4c: add             x3, x3, HEAP, lsl #32
    // 0x14eff50: mov             x1, x3
    // 0x14eff54: r0 = controller()
    //     0x14eff54: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14eff58: stur            x0, [fp, #-0x18]
    // 0x14eff5c: r0 = CancelExchangeBottomSheet()
    //     0x14eff5c: bl              #0x14effa8  ; AllocateCancelExchangeBottomSheetStub -> CancelExchangeBottomSheet (size=0x18)
    // 0x14eff60: mov             x3, x0
    // 0x14eff64: ldur            x0, [fp, #-8]
    // 0x14eff68: stur            x3, [fp, #-0x20]
    // 0x14eff6c: StoreField: r3->field_b = r0
    //     0x14eff6c: stur            w0, [x3, #0xb]
    // 0x14eff70: ldur            x2, [fp, #-0x18]
    // 0x14eff74: r1 = Function 'cancelExchange':.
    //     0x14eff74: add             x1, PP, #0x36, lsl #12  ; [pp+0x36e18] AnonymousClosure: (0x8afdc4), in [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::cancelExchange (0x8afe00)
    //     0x14eff78: ldr             x1, [x1, #0xe18]
    // 0x14eff7c: r0 = AllocateClosure()
    //     0x14eff7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14eff80: mov             x1, x0
    // 0x14eff84: ldur            x0, [fp, #-0x20]
    // 0x14eff88: StoreField: r0->field_f = r1
    //     0x14eff88: stur            w1, [x0, #0xf]
    // 0x14eff8c: ldur            x1, [fp, #-0x10]
    // 0x14eff90: StoreField: r0->field_13 = r1
    //     0x14eff90: stur            w1, [x0, #0x13]
    // 0x14eff94: LeaveFrame
    //     0x14eff94: mov             SP, fp
    //     0x14eff98: ldp             fp, lr, [SP], #0x10
    // 0x14eff9c: ret
    //     0x14eff9c: ret             
    // 0x14effa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14effa0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14effa4: b               #0x14eff30
  }
  [closure] void cancelReturn(dynamic, BuildContext, CancelReturnRequestWidget?, String) {
    // ** addr: 0x14effb4, size: 0x44
    // 0x14effb4: EnterFrame
    //     0x14effb4: stp             fp, lr, [SP, #-0x10]!
    //     0x14effb8: mov             fp, SP
    // 0x14effbc: ldr             x0, [fp, #0x28]
    // 0x14effc0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14effc0: ldur            w1, [x0, #0x17]
    // 0x14effc4: DecompressPointer r1
    //     0x14effc4: add             x1, x1, HEAP, lsl #32
    // 0x14effc8: CheckStackOverflow
    //     0x14effc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14effcc: cmp             SP, x16
    //     0x14effd0: b.ls            #0x14efff0
    // 0x14effd4: ldr             x2, [fp, #0x20]
    // 0x14effd8: ldr             x3, [fp, #0x18]
    // 0x14effdc: ldr             x5, [fp, #0x10]
    // 0x14effe0: r0 = cancelReturn()
    //     0x14effe0: bl              #0x14efff8  ; [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::cancelReturn
    // 0x14effe4: LeaveFrame
    //     0x14effe4: mov             SP, fp
    //     0x14effe8: ldp             fp, lr, [SP], #0x10
    // 0x14effec: ret
    //     0x14effec: ret             
    // 0x14efff0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14efff0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14efff4: b               #0x14effd4
  }
  _ cancelReturn(/* No info */) {
    // ** addr: 0x14efff8, size: 0x9c
    // 0x14efff8: EnterFrame
    //     0x14efff8: stp             fp, lr, [SP, #-0x10]!
    //     0x14efffc: mov             fp, SP
    // 0x14f0000: AllocStack(0x48)
    //     0x14f0000: sub             SP, SP, #0x48
    // 0x14f0004: SetupParameters(OrdersView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x14f0004: stur            x1, [fp, #-8]
    //     0x14f0008: stur            x2, [fp, #-0x10]
    //     0x14f000c: stur            x3, [fp, #-0x18]
    //     0x14f0010: stur            x5, [fp, #-0x20]
    // 0x14f0014: CheckStackOverflow
    //     0x14f0014: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f0018: cmp             SP, x16
    //     0x14f001c: b.ls            #0x14f008c
    // 0x14f0020: r1 = 3
    //     0x14f0020: movz            x1, #0x3
    // 0x14f0024: r0 = AllocateContext()
    //     0x14f0024: bl              #0x16f6108  ; AllocateContextStub
    // 0x14f0028: mov             x1, x0
    // 0x14f002c: ldur            x0, [fp, #-8]
    // 0x14f0030: StoreField: r1->field_f = r0
    //     0x14f0030: stur            w0, [x1, #0xf]
    // 0x14f0034: ldur            x0, [fp, #-0x18]
    // 0x14f0038: StoreField: r1->field_13 = r0
    //     0x14f0038: stur            w0, [x1, #0x13]
    // 0x14f003c: ldur            x0, [fp, #-0x20]
    // 0x14f0040: ArrayStore: r1[0] = r0  ; List_4
    //     0x14f0040: stur            w0, [x1, #0x17]
    // 0x14f0044: mov             x2, x1
    // 0x14f0048: r1 = Function '<anonymous closure>':.
    //     0x14f0048: add             x1, PP, #0x40, lsl #12  ; [pp+0x401e8] AnonymousClosure: (0x14f0094), in [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::cancelReturn (0x14efff8)
    //     0x14f004c: ldr             x1, [x1, #0x1e8]
    // 0x14f0050: r0 = AllocateClosure()
    //     0x14f0050: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f0054: stp             x0, NULL, [SP, #0x18]
    // 0x14f0058: ldur            x16, [fp, #-0x10]
    // 0x14f005c: r30 = true
    //     0x14f005c: add             lr, NULL, #0x20  ; true
    // 0x14f0060: stp             lr, x16, [SP, #8]
    // 0x14f0064: r16 = Instance_RoundedRectangleBorder
    //     0x14f0064: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x14f0068: ldr             x16, [x16, #0xc78]
    // 0x14f006c: str             x16, [SP]
    // 0x14f0070: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0x14f0070: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0x14f0074: ldr             x4, [x4, #0xb20]
    // 0x14f0078: r0 = showModalBottomSheet()
    //     0x14f0078: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x14f007c: r0 = Null
    //     0x14f007c: mov             x0, NULL
    // 0x14f0080: LeaveFrame
    //     0x14f0080: mov             SP, fp
    //     0x14f0084: ldp             fp, lr, [SP], #0x10
    // 0x14f0088: ret
    //     0x14f0088: ret             
    // 0x14f008c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f008c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f0090: b               #0x14f0020
  }
  [closure] CancelReturnOrderBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x14f0094, size: 0x9c
    // 0x14f0094: EnterFrame
    //     0x14f0094: stp             fp, lr, [SP, #-0x10]!
    //     0x14f0098: mov             fp, SP
    // 0x14f009c: AllocStack(0x20)
    //     0x14f009c: sub             SP, SP, #0x20
    // 0x14f00a0: SetupParameters()
    //     0x14f00a0: ldr             x0, [fp, #0x18]
    //     0x14f00a4: ldur            w1, [x0, #0x17]
    //     0x14f00a8: add             x1, x1, HEAP, lsl #32
    // 0x14f00ac: CheckStackOverflow
    //     0x14f00ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f00b0: cmp             SP, x16
    //     0x14f00b4: b.ls            #0x14f0128
    // 0x14f00b8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14f00b8: ldur            w0, [x1, #0x17]
    // 0x14f00bc: DecompressPointer r0
    //     0x14f00bc: add             x0, x0, HEAP, lsl #32
    // 0x14f00c0: stur            x0, [fp, #-0x10]
    // 0x14f00c4: LoadField: r2 = r1->field_13
    //     0x14f00c4: ldur            w2, [x1, #0x13]
    // 0x14f00c8: DecompressPointer r2
    //     0x14f00c8: add             x2, x2, HEAP, lsl #32
    // 0x14f00cc: stur            x2, [fp, #-8]
    // 0x14f00d0: LoadField: r3 = r1->field_f
    //     0x14f00d0: ldur            w3, [x1, #0xf]
    // 0x14f00d4: DecompressPointer r3
    //     0x14f00d4: add             x3, x3, HEAP, lsl #32
    // 0x14f00d8: mov             x1, x3
    // 0x14f00dc: r0 = controller()
    //     0x14f00dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f00e0: stur            x0, [fp, #-0x18]
    // 0x14f00e4: r0 = CancelReturnOrderBottomSheet()
    //     0x14f00e4: bl              #0x14f0130  ; AllocateCancelReturnOrderBottomSheetStub -> CancelReturnOrderBottomSheet (size=0x18)
    // 0x14f00e8: mov             x3, x0
    // 0x14f00ec: ldur            x0, [fp, #-8]
    // 0x14f00f0: stur            x3, [fp, #-0x20]
    // 0x14f00f4: StoreField: r3->field_b = r0
    //     0x14f00f4: stur            w0, [x3, #0xb]
    // 0x14f00f8: ldur            x2, [fp, #-0x18]
    // 0x14f00fc: r1 = Function 'cancelReturn':.
    //     0x14f00fc: add             x1, PP, #0x36, lsl #12  ; [pp+0x36e48] AnonymousClosure: (0x8b07bc), in [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::cancelReturn (0x8b07f8)
    //     0x14f0100: ldr             x1, [x1, #0xe48]
    // 0x14f0104: r0 = AllocateClosure()
    //     0x14f0104: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f0108: mov             x1, x0
    // 0x14f010c: ldur            x0, [fp, #-0x20]
    // 0x14f0110: StoreField: r0->field_f = r1
    //     0x14f0110: stur            w1, [x0, #0xf]
    // 0x14f0114: ldur            x1, [fp, #-0x10]
    // 0x14f0118: StoreField: r0->field_13 = r1
    //     0x14f0118: stur            w1, [x0, #0x13]
    // 0x14f011c: LeaveFrame
    //     0x14f011c: mov             SP, fp
    //     0x14f0120: ldp             fp, lr, [SP], #0x10
    // 0x14f0124: ret
    //     0x14f0124: ret             
    // 0x14f0128: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f0128: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f012c: b               #0x14f00b8
  }
  [closure] void openProductDetail(dynamic, dynamic, dynamic, dynamic, dynamic, dynamic) {
    // ** addr: 0x14f013c, size: 0x4c
    // 0x14f013c: EnterFrame
    //     0x14f013c: stp             fp, lr, [SP, #-0x10]!
    //     0x14f0140: mov             fp, SP
    // 0x14f0144: ldr             x0, [fp, #0x38]
    // 0x14f0148: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14f0148: ldur            w1, [x0, #0x17]
    // 0x14f014c: DecompressPointer r1
    //     0x14f014c: add             x1, x1, HEAP, lsl #32
    // 0x14f0150: CheckStackOverflow
    //     0x14f0150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f0154: cmp             SP, x16
    //     0x14f0158: b.ls            #0x14f0180
    // 0x14f015c: ldr             x2, [fp, #0x30]
    // 0x14f0160: ldr             x3, [fp, #0x28]
    // 0x14f0164: ldr             x5, [fp, #0x20]
    // 0x14f0168: ldr             x6, [fp, #0x18]
    // 0x14f016c: ldr             x7, [fp, #0x10]
    // 0x14f0170: r0 = openProductDetail()
    //     0x14f0170: bl              #0x14f0188  ; [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::openProductDetail
    // 0x14f0174: LeaveFrame
    //     0x14f0174: mov             SP, fp
    //     0x14f0178: ldp             fp, lr, [SP], #0x10
    // 0x14f017c: ret
    //     0x14f017c: ret             
    // 0x14f0180: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f0180: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f0184: b               #0x14f015c
  }
  _ openProductDetail(/* No info */) {
    // ** addr: 0x14f0188, size: 0x220
    // 0x14f0188: EnterFrame
    //     0x14f0188: stp             fp, lr, [SP, #-0x10]!
    //     0x14f018c: mov             fp, SP
    // 0x14f0190: AllocStack(0x48)
    //     0x14f0190: sub             SP, SP, #0x48
    // 0x14f0194: SetupParameters(OrdersView this /* r1 => r8, fp-0x8 */, dynamic _ /* r2 => r7, fp-0x10 */, dynamic _ /* r3 => r6, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r4, fp-0x28 */, dynamic _ /* r7 => r3, fp-0x30 */)
    //     0x14f0194: mov             x8, x1
    //     0x14f0198: mov             x4, x6
    //     0x14f019c: stur            x6, [fp, #-0x28]
    //     0x14f01a0: mov             x6, x3
    //     0x14f01a4: stur            x3, [fp, #-0x18]
    //     0x14f01a8: mov             x3, x7
    //     0x14f01ac: stur            x7, [fp, #-0x30]
    //     0x14f01b0: mov             x7, x2
    //     0x14f01b4: stur            x1, [fp, #-8]
    //     0x14f01b8: stur            x2, [fp, #-0x10]
    //     0x14f01bc: stur            x5, [fp, #-0x20]
    // 0x14f01c0: CheckStackOverflow
    //     0x14f01c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f01c4: cmp             SP, x16
    //     0x14f01c8: b.ls            #0x14f03a0
    // 0x14f01cc: mov             x0, x5
    // 0x14f01d0: r2 = Null
    //     0x14f01d0: mov             x2, NULL
    // 0x14f01d4: r1 = Null
    //     0x14f01d4: mov             x1, NULL
    // 0x14f01d8: r4 = 60
    //     0x14f01d8: movz            x4, #0x3c
    // 0x14f01dc: branchIfSmi(r0, 0x14f01e8)
    //     0x14f01dc: tbz             w0, #0, #0x14f01e8
    // 0x14f01e0: r4 = LoadClassIdInstr(r0)
    //     0x14f01e0: ldur            x4, [x0, #-1]
    //     0x14f01e4: ubfx            x4, x4, #0xc, #0x14
    // 0x14f01e8: cmp             x4, #0x3f
    // 0x14f01ec: b.eq            #0x14f0200
    // 0x14f01f0: r8 = bool
    //     0x14f01f0: ldr             x8, [PP, #0x25f0]  ; [pp+0x25f0] Type: bool
    // 0x14f01f4: r3 = Null
    //     0x14f01f4: add             x3, PP, #0x40, lsl #12  ; [pp+0x401f0] Null
    //     0x14f01f8: ldr             x3, [x3, #0x1f0]
    // 0x14f01fc: r0 = bool()
    //     0x14f01fc: bl              #0x16fbdf8  ; IsType_bool_Stub
    // 0x14f0200: ldur            x0, [fp, #-0x20]
    // 0x14f0204: tbz             w0, #4, #0x14f0248
    // 0x14f0208: ldur            x3, [fp, #-0x28]
    // 0x14f020c: mov             x0, x3
    // 0x14f0210: r2 = Null
    //     0x14f0210: mov             x2, NULL
    // 0x14f0214: r1 = Null
    //     0x14f0214: mov             x1, NULL
    // 0x14f0218: r4 = 60
    //     0x14f0218: movz            x4, #0x3c
    // 0x14f021c: branchIfSmi(r0, 0x14f0228)
    //     0x14f021c: tbz             w0, #0, #0x14f0228
    // 0x14f0220: r4 = LoadClassIdInstr(r0)
    //     0x14f0220: ldur            x4, [x0, #-1]
    //     0x14f0224: ubfx            x4, x4, #0xc, #0x14
    // 0x14f0228: cmp             x4, #0x3f
    // 0x14f022c: b.eq            #0x14f0240
    // 0x14f0230: r8 = bool
    //     0x14f0230: ldr             x8, [PP, #0x25f0]  ; [pp+0x25f0] Type: bool
    // 0x14f0234: r3 = Null
    //     0x14f0234: add             x3, PP, #0x40, lsl #12  ; [pp+0x40200] Null
    //     0x14f0238: ldr             x3, [x3, #0x200]
    // 0x14f023c: r0 = bool()
    //     0x14f023c: bl              #0x16fbdf8  ; IsType_bool_Stub
    // 0x14f0240: ldur            x0, [fp, #-0x28]
    // 0x14f0244: tbnz            w0, #4, #0x14f02e8
    // 0x14f0248: ldur            x0, [fp, #-0x30]
    // 0x14f024c: ldur            x1, [fp, #-8]
    // 0x14f0250: r0 = controller()
    //     0x14f0250: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f0254: mov             x3, x0
    // 0x14f0258: ldur            x0, [fp, #-0x30]
    // 0x14f025c: r2 = Null
    //     0x14f025c: mov             x2, NULL
    // 0x14f0260: r1 = Null
    //     0x14f0260: mov             x1, NULL
    // 0x14f0264: stur            x3, [fp, #-8]
    // 0x14f0268: r4 = 60
    //     0x14f0268: movz            x4, #0x3c
    // 0x14f026c: branchIfSmi(r0, 0x14f0278)
    //     0x14f026c: tbz             w0, #0, #0x14f0278
    // 0x14f0270: r4 = LoadClassIdInstr(r0)
    //     0x14f0270: ldur            x4, [x0, #-1]
    //     0x14f0274: ubfx            x4, x4, #0xc, #0x14
    // 0x14f0278: sub             x4, x4, #0x5e
    // 0x14f027c: cmp             x4, #1
    // 0x14f0280: b.ls            #0x14f0294
    // 0x14f0284: r8 = String?
    //     0x14f0284: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x14f0288: r3 = Null
    //     0x14f0288: add             x3, PP, #0x40, lsl #12  ; [pp+0x40210] Null
    //     0x14f028c: ldr             x3, [x3, #0x210]
    // 0x14f0290: r0 = String?()
    //     0x14f0290: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x14f0294: r0 = EventData()
    //     0x14f0294: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x14f0298: mov             x1, x0
    // 0x14f029c: r0 = "home_page"
    //     0x14f029c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0x14f02a0: ldr             x0, [x0, #0xe60]
    // 0x14f02a4: stur            x1, [fp, #-0x20]
    // 0x14f02a8: StoreField: r1->field_13 = r0
    //     0x14f02a8: stur            w0, [x1, #0x13]
    // 0x14f02ac: ldur            x0, [fp, #-0x30]
    // 0x14f02b0: StoreField: r1->field_77 = r0
    //     0x14f02b0: stur            w0, [x1, #0x77]
    // 0x14f02b4: r0 = "order_list"
    //     0x14f02b4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36e98] "order_list"
    //     0x14f02b8: ldr             x0, [x0, #0xe98]
    // 0x14f02bc: StoreField: r1->field_87 = r0
    //     0x14f02bc: stur            w0, [x1, #0x87]
    // 0x14f02c0: r0 = EventsRequest()
    //     0x14f02c0: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x14f02c4: mov             x1, x0
    // 0x14f02c8: r0 = "return_exchange_order_again"
    //     0x14f02c8: add             x0, PP, #0x36, lsl #12  ; [pp+0x362a0] "return_exchange_order_again"
    //     0x14f02cc: ldr             x0, [x0, #0x2a0]
    // 0x14f02d0: StoreField: r1->field_7 = r0
    //     0x14f02d0: stur            w0, [x1, #7]
    // 0x14f02d4: ldur            x0, [fp, #-0x20]
    // 0x14f02d8: StoreField: r1->field_b = r0
    //     0x14f02d8: stur            w0, [x1, #0xb]
    // 0x14f02dc: mov             x2, x1
    // 0x14f02e0: ldur            x1, [fp, #-8]
    // 0x14f02e4: r0 = postEvents()
    //     0x14f02e4: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x14f02e8: ldur            x1, [fp, #-0x10]
    // 0x14f02ec: ldur            x0, [fp, #-0x18]
    // 0x14f02f0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14f02f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14f02f4: ldr             x0, [x0, #0x1c80]
    //     0x14f02f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14f02fc: cmp             w0, w16
    //     0x14f0300: b.ne            #0x14f030c
    //     0x14f0304: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14f0308: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14f030c: r1 = Null
    //     0x14f030c: mov             x1, NULL
    // 0x14f0310: r2 = 16
    //     0x14f0310: movz            x2, #0x10
    // 0x14f0314: r0 = AllocateArray()
    //     0x14f0314: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14f0318: r16 = "short_id"
    //     0x14f0318: add             x16, PP, #0xb, lsl #12  ; [pp+0xb488] "short_id"
    //     0x14f031c: ldr             x16, [x16, #0x488]
    // 0x14f0320: StoreField: r0->field_f = r16
    //     0x14f0320: stur            w16, [x0, #0xf]
    // 0x14f0324: ldur            x1, [fp, #-0x10]
    // 0x14f0328: StoreField: r0->field_13 = r1
    //     0x14f0328: stur            w1, [x0, #0x13]
    // 0x14f032c: r16 = "sku_id"
    //     0x14f032c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x14f0330: ldr             x16, [x16, #0x498]
    // 0x14f0334: ArrayStore: r0[0] = r16  ; List_4
    //     0x14f0334: stur            w16, [x0, #0x17]
    // 0x14f0338: ldur            x1, [fp, #-0x18]
    // 0x14f033c: StoreField: r0->field_1b = r1
    //     0x14f033c: stur            w1, [x0, #0x1b]
    // 0x14f0340: r16 = "screenSource"
    //     0x14f0340: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0x14f0344: ldr             x16, [x16, #0x450]
    // 0x14f0348: StoreField: r0->field_1f = r16
    //     0x14f0348: stur            w16, [x0, #0x1f]
    // 0x14f034c: StoreField: r0->field_23 = rNULL
    //     0x14f034c: stur            NULL, [x0, #0x23]
    // 0x14f0350: r16 = "previousScreenSource"
    //     0x14f0350: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x14f0354: ldr             x16, [x16, #0x448]
    // 0x14f0358: StoreField: r0->field_27 = r16
    //     0x14f0358: stur            w16, [x0, #0x27]
    // 0x14f035c: r16 = "order_page"
    //     0x14f035c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d710] "order_page"
    //     0x14f0360: ldr             x16, [x16, #0x710]
    // 0x14f0364: StoreField: r0->field_2b = r16
    //     0x14f0364: stur            w16, [x0, #0x2b]
    // 0x14f0368: r16 = <String, dynamic>
    //     0x14f0368: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x14f036c: stp             x0, x16, [SP]
    // 0x14f0370: r0 = Map._fromLiteral()
    //     0x14f0370: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x14f0374: r16 = "/product-detail"
    //     0x14f0374: add             x16, PP, #0xb, lsl #12  ; [pp+0xb4a8] "/product-detail"
    //     0x14f0378: ldr             x16, [x16, #0x4a8]
    // 0x14f037c: stp             x16, NULL, [SP, #8]
    // 0x14f0380: str             x0, [SP]
    // 0x14f0384: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x14f0384: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x14f0388: ldr             x4, [x4, #0x438]
    // 0x14f038c: r0 = GetNavigation.toNamed()
    //     0x14f038c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x14f0390: r0 = Null
    //     0x14f0390: mov             x0, NULL
    // 0x14f0394: LeaveFrame
    //     0x14f0394: mov             SP, fp
    //     0x14f0398: ldp             fp, lr, [SP], #0x10
    // 0x14f039c: ret
    //     0x14f039c: ret             
    // 0x14f03a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f03a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f03a4: b               #0x14f01cc
  }
  [closure] void cancelOrderWithFreeProduct(dynamic, Items?, String, BuildContext) {
    // ** addr: 0x14f03a8, size: 0x44
    // 0x14f03a8: EnterFrame
    //     0x14f03a8: stp             fp, lr, [SP, #-0x10]!
    //     0x14f03ac: mov             fp, SP
    // 0x14f03b0: ldr             x0, [fp, #0x28]
    // 0x14f03b4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14f03b4: ldur            w1, [x0, #0x17]
    // 0x14f03b8: DecompressPointer r1
    //     0x14f03b8: add             x1, x1, HEAP, lsl #32
    // 0x14f03bc: CheckStackOverflow
    //     0x14f03bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f03c0: cmp             SP, x16
    //     0x14f03c4: b.ls            #0x14f03e4
    // 0x14f03c8: ldr             x2, [fp, #0x20]
    // 0x14f03cc: ldr             x3, [fp, #0x18]
    // 0x14f03d0: ldr             x5, [fp, #0x10]
    // 0x14f03d4: r0 = cancelOrderWithFreeProduct()
    //     0x14f03d4: bl              #0x14f03ec  ; [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::cancelOrderWithFreeProduct
    // 0x14f03d8: LeaveFrame
    //     0x14f03d8: mov             SP, fp
    //     0x14f03dc: ldp             fp, lr, [SP], #0x10
    // 0x14f03e0: ret
    //     0x14f03e0: ret             
    // 0x14f03e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f03e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f03e8: b               #0x14f03c8
  }
  _ cancelOrderWithFreeProduct(/* No info */) {
    // ** addr: 0x14f03ec, size: 0x148
    // 0x14f03ec: EnterFrame
    //     0x14f03ec: stp             fp, lr, [SP, #-0x10]!
    //     0x14f03f0: mov             fp, SP
    // 0x14f03f4: AllocStack(0x60)
    //     0x14f03f4: sub             SP, SP, #0x60
    // 0x14f03f8: SetupParameters(OrdersView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x14f03f8: stur            x1, [fp, #-8]
    //     0x14f03fc: stur            x2, [fp, #-0x10]
    //     0x14f0400: stur            x3, [fp, #-0x18]
    //     0x14f0404: stur            x5, [fp, #-0x20]
    // 0x14f0408: CheckStackOverflow
    //     0x14f0408: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f040c: cmp             SP, x16
    //     0x14f0410: b.ls            #0x14f052c
    // 0x14f0414: r1 = 2
    //     0x14f0414: movz            x1, #0x2
    // 0x14f0418: r0 = AllocateContext()
    //     0x14f0418: bl              #0x16f6108  ; AllocateContextStub
    // 0x14f041c: mov             x2, x0
    // 0x14f0420: ldur            x0, [fp, #-8]
    // 0x14f0424: stur            x2, [fp, #-0x28]
    // 0x14f0428: StoreField: r2->field_f = r0
    //     0x14f0428: stur            w0, [x2, #0xf]
    // 0x14f042c: ldur            x1, [fp, #-0x10]
    // 0x14f0430: StoreField: r2->field_13 = r1
    //     0x14f0430: stur            w1, [x2, #0x13]
    // 0x14f0434: mov             x1, x0
    // 0x14f0438: r0 = controller()
    //     0x14f0438: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f043c: stur            x0, [fp, #-0x10]
    // 0x14f0440: r0 = EventData()
    //     0x14f0440: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x14f0444: mov             x1, x0
    // 0x14f0448: r0 = "order_page"
    //     0x14f0448: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d710] "order_page"
    //     0x14f044c: ldr             x0, [x0, #0x710]
    // 0x14f0450: stur            x1, [fp, #-0x30]
    // 0x14f0454: StoreField: r1->field_13 = r0
    //     0x14f0454: stur            w0, [x1, #0x13]
    // 0x14f0458: ldur            x2, [fp, #-0x18]
    // 0x14f045c: StoreField: r1->field_77 = r2
    //     0x14f045c: stur            w2, [x1, #0x77]
    // 0x14f0460: r0 = EventsRequest()
    //     0x14f0460: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x14f0464: mov             x1, x0
    // 0x14f0468: r0 = "cancel_order_clicked"
    //     0x14f0468: add             x0, PP, #0x36, lsl #12  ; [pp+0x360e8] "cancel_order_clicked"
    //     0x14f046c: ldr             x0, [x0, #0xe8]
    // 0x14f0470: StoreField: r1->field_7 = r0
    //     0x14f0470: stur            w0, [x1, #7]
    // 0x14f0474: ldur            x0, [fp, #-0x30]
    // 0x14f0478: StoreField: r1->field_b = r0
    //     0x14f0478: stur            w0, [x1, #0xb]
    // 0x14f047c: mov             x2, x1
    // 0x14f0480: ldur            x1, [fp, #-0x10]
    // 0x14f0484: r0 = postEvents()
    //     0x14f0484: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x14f0488: ldur            x1, [fp, #-8]
    // 0x14f048c: r0 = controller()
    //     0x14f048c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f0490: stur            x0, [fp, #-8]
    // 0x14f0494: r0 = EventData()
    //     0x14f0494: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x14f0498: mov             x1, x0
    // 0x14f049c: r0 = "order_page"
    //     0x14f049c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d710] "order_page"
    //     0x14f04a0: ldr             x0, [x0, #0x710]
    // 0x14f04a4: stur            x1, [fp, #-0x10]
    // 0x14f04a8: StoreField: r1->field_13 = r0
    //     0x14f04a8: stur            w0, [x1, #0x13]
    // 0x14f04ac: ldur            x0, [fp, #-0x18]
    // 0x14f04b0: StoreField: r1->field_77 = r0
    //     0x14f04b0: stur            w0, [x1, #0x77]
    // 0x14f04b4: r0 = EventsRequest()
    //     0x14f04b4: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x14f04b8: mov             x1, x0
    // 0x14f04bc: r0 = "cancel_free_gift_popup_opened"
    //     0x14f04bc: add             x0, PP, #0x36, lsl #12  ; [pp+0x36ea0] "cancel_free_gift_popup_opened"
    //     0x14f04c0: ldr             x0, [x0, #0xea0]
    // 0x14f04c4: StoreField: r1->field_7 = r0
    //     0x14f04c4: stur            w0, [x1, #7]
    // 0x14f04c8: ldur            x0, [fp, #-0x10]
    // 0x14f04cc: StoreField: r1->field_b = r0
    //     0x14f04cc: stur            w0, [x1, #0xb]
    // 0x14f04d0: mov             x2, x1
    // 0x14f04d4: ldur            x1, [fp, #-8]
    // 0x14f04d8: r0 = postEvents()
    //     0x14f04d8: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x14f04dc: ldur            x2, [fp, #-0x28]
    // 0x14f04e0: r1 = Function '<anonymous closure>':.
    //     0x14f04e0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40220] AnonymousClosure: (0x14f0534), in [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::cancelOrderWithFreeProduct (0x14f03ec)
    //     0x14f04e4: ldr             x1, [x1, #0x220]
    // 0x14f04e8: r0 = AllocateClosure()
    //     0x14f04e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f04ec: stp             x0, NULL, [SP, #0x20]
    // 0x14f04f0: ldur            x16, [fp, #-0x20]
    // 0x14f04f4: r30 = Instance_Color
    //     0x14f04f4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x14f04f8: ldr             lr, [lr, #0x90]
    // 0x14f04fc: stp             lr, x16, [SP, #0x10]
    // 0x14f0500: r16 = Instance_RoundedRectangleBorder
    //     0x14f0500: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x14f0504: ldr             x16, [x16, #0xc78]
    // 0x14f0508: r30 = true
    //     0x14f0508: add             lr, NULL, #0x20  ; true
    // 0x14f050c: stp             lr, x16, [SP]
    // 0x14f0510: r4 = const [0x1, 0x5, 0x5, 0x2, backgroundColor, 0x2, isScrollControlled, 0x4, shape, 0x3, null]
    //     0x14f0510: add             x4, PP, #0x34, lsl #12  ; [pp+0x345f0] List(11) [0x1, 0x5, 0x5, 0x2, "backgroundColor", 0x2, "isScrollControlled", 0x4, "shape", 0x3, Null]
    //     0x14f0514: ldr             x4, [x4, #0x5f0]
    // 0x14f0518: r0 = showModalBottomSheet()
    //     0x14f0518: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x14f051c: r0 = Null
    //     0x14f051c: mov             x0, NULL
    // 0x14f0520: LeaveFrame
    //     0x14f0520: mov             SP, fp
    //     0x14f0524: ldp             fp, lr, [SP], #0x10
    // 0x14f0528: ret
    //     0x14f0528: ret             
    // 0x14f052c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f052c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f0530: b               #0x14f0414
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x14f0534, size: 0x1b0
    // 0x14f0534: EnterFrame
    //     0x14f0534: stp             fp, lr, [SP, #-0x10]!
    //     0x14f0538: mov             fp, SP
    // 0x14f053c: AllocStack(0x28)
    //     0x14f053c: sub             SP, SP, #0x28
    // 0x14f0540: SetupParameters()
    //     0x14f0540: ldr             x0, [fp, #0x18]
    //     0x14f0544: ldur            w1, [x0, #0x17]
    //     0x14f0548: add             x1, x1, HEAP, lsl #32
    //     0x14f054c: stur            x1, [fp, #-8]
    // 0x14f0550: CheckStackOverflow
    //     0x14f0550: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f0554: cmp             SP, x16
    //     0x14f0558: b.ls            #0x14f06dc
    // 0x14f055c: r1 = 1
    //     0x14f055c: movz            x1, #0x1
    // 0x14f0560: r0 = AllocateContext()
    //     0x14f0560: bl              #0x16f6108  ; AllocateContextStub
    // 0x14f0564: mov             x1, x0
    // 0x14f0568: ldur            x0, [fp, #-8]
    // 0x14f056c: stur            x1, [fp, #-0x10]
    // 0x14f0570: StoreField: r1->field_b = r0
    //     0x14f0570: stur            w0, [x1, #0xb]
    // 0x14f0574: ldr             x2, [fp, #0x10]
    // 0x14f0578: StoreField: r1->field_f = r2
    //     0x14f0578: stur            w2, [x1, #0xf]
    // 0x14f057c: LoadField: r2 = r0->field_13
    //     0x14f057c: ldur            w2, [x0, #0x13]
    // 0x14f0580: DecompressPointer r2
    //     0x14f0580: add             x2, x2, HEAP, lsl #32
    // 0x14f0584: cmp             w2, NULL
    // 0x14f0588: b.ne            #0x14f0594
    // 0x14f058c: d0 = inf
    //     0x14f058c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x14f0590: b               #0x14f0600
    // 0x14f0594: LoadField: r3 = r2->field_7f
    //     0x14f0594: ldur            w3, [x2, #0x7f]
    // 0x14f0598: DecompressPointer r3
    //     0x14f0598: add             x3, x3, HEAP, lsl #32
    // 0x14f059c: cmp             w3, NULL
    // 0x14f05a0: b.eq            #0x14f05fc
    // 0x14f05a4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14f05a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14f05a8: ldr             x0, [x0, #0x1c80]
    //     0x14f05ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14f05b0: cmp             w0, w16
    //     0x14f05b4: b.ne            #0x14f05c0
    //     0x14f05b8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14f05bc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14f05c0: r0 = GetNavigation.size()
    //     0x14f05c0: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x14f05c4: LoadField: d0 = r0->field_f
    //     0x14f05c4: ldur            d0, [x0, #0xf]
    // 0x14f05c8: d1 = 0.430000
    //     0x14f05c8: add             x17, PP, #0x36, lsl #12  ; [pp+0x360b8] IMM: double(0.43) from 0x3fdb851eb851eb85
    //     0x14f05cc: ldr             d1, [x17, #0xb8]
    // 0x14f05d0: fmul            d2, d0, d1
    // 0x14f05d4: stur            d2, [fp, #-0x28]
    // 0x14f05d8: r0 = BoxConstraints()
    //     0x14f05d8: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x14f05dc: StoreField: r0->field_7 = rZR
    //     0x14f05dc: stur            xzr, [x0, #7]
    // 0x14f05e0: d0 = inf
    //     0x14f05e0: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x14f05e4: StoreField: r0->field_f = d0
    //     0x14f05e4: stur            d0, [x0, #0xf]
    // 0x14f05e8: ArrayStore: r0[0] = rZR  ; List_8
    //     0x14f05e8: stur            xzr, [x0, #0x17]
    // 0x14f05ec: ldur            d0, [fp, #-0x28]
    // 0x14f05f0: StoreField: r0->field_1f = d0
    //     0x14f05f0: stur            d0, [x0, #0x1f]
    // 0x14f05f4: mov             x3, x0
    // 0x14f05f8: b               #0x14f0654
    // 0x14f05fc: d0 = inf
    //     0x14f05fc: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x14f0600: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14f0600: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14f0604: ldr             x0, [x0, #0x1c80]
    //     0x14f0608: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14f060c: cmp             w0, w16
    //     0x14f0610: b.ne            #0x14f061c
    //     0x14f0614: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14f0618: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14f061c: r0 = GetNavigation.size()
    //     0x14f061c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x14f0620: LoadField: d0 = r0->field_f
    //     0x14f0620: ldur            d0, [x0, #0xf]
    // 0x14f0624: d1 = 0.300000
    //     0x14f0624: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x14f0628: ldr             d1, [x17, #0x658]
    // 0x14f062c: fmul            d2, d0, d1
    // 0x14f0630: stur            d2, [fp, #-0x28]
    // 0x14f0634: r0 = BoxConstraints()
    //     0x14f0634: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x14f0638: StoreField: r0->field_7 = rZR
    //     0x14f0638: stur            xzr, [x0, #7]
    // 0x14f063c: d0 = inf
    //     0x14f063c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x14f0640: StoreField: r0->field_f = d0
    //     0x14f0640: stur            d0, [x0, #0xf]
    // 0x14f0644: ArrayStore: r0[0] = rZR  ; List_8
    //     0x14f0644: stur            xzr, [x0, #0x17]
    // 0x14f0648: ldur            d0, [fp, #-0x28]
    // 0x14f064c: StoreField: r0->field_1f = d0
    //     0x14f064c: stur            d0, [x0, #0x1f]
    // 0x14f0650: mov             x3, x0
    // 0x14f0654: ldur            x0, [fp, #-8]
    // 0x14f0658: stur            x3, [fp, #-0x20]
    // 0x14f065c: LoadField: r4 = r0->field_13
    //     0x14f065c: ldur            w4, [x0, #0x13]
    // 0x14f0660: DecompressPointer r4
    //     0x14f0660: add             x4, x4, HEAP, lsl #32
    // 0x14f0664: ldur            x2, [fp, #-0x10]
    // 0x14f0668: stur            x4, [fp, #-0x18]
    // 0x14f066c: r1 = Function '<anonymous closure>':.
    //     0x14f066c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40228] AnonymousClosure: (0x14f06e4), in [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::cancelOrderWithFreeProduct (0x14f03ec)
    //     0x14f0670: ldr             x1, [x1, #0x228]
    // 0x14f0674: r0 = AllocateClosure()
    //     0x14f0674: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f0678: stur            x0, [fp, #-8]
    // 0x14f067c: r0 = CancelReturnOrderWithFreeProductBottomSheet()
    //     0x14f067c: bl              #0x1361098  ; AllocateCancelReturnOrderWithFreeProductBottomSheetStub -> CancelReturnOrderWithFreeProductBottomSheet (size=0x28)
    // 0x14f0680: mov             x1, x0
    // 0x14f0684: ldur            x0, [fp, #-8]
    // 0x14f0688: stur            x1, [fp, #-0x10]
    // 0x14f068c: StoreField: r1->field_b = r0
    //     0x14f068c: stur            w0, [x1, #0xb]
    // 0x14f0690: ldur            x0, [fp, #-0x18]
    // 0x14f0694: StoreField: r1->field_1b = r0
    //     0x14f0694: stur            w0, [x1, #0x1b]
    // 0x14f0698: r0 = "Confirm Cancellation"
    //     0x14f0698: add             x0, PP, #0x36, lsl #12  ; [pp+0x360c8] "Confirm Cancellation"
    //     0x14f069c: ldr             x0, [x0, #0xc8]
    // 0x14f06a0: StoreField: r1->field_13 = r0
    //     0x14f06a0: stur            w0, [x1, #0x13]
    // 0x14f06a4: r0 = "On cancellation of this product the free gift will also be cancelled"
    //     0x14f06a4: add             x0, PP, #0x36, lsl #12  ; [pp+0x360d0] "On cancellation of this product the free gift will also be cancelled"
    //     0x14f06a8: ldr             x0, [x0, #0xd0]
    // 0x14f06ac: ArrayStore: r1[0] = r0  ; List_4
    //     0x14f06ac: stur            w0, [x1, #0x17]
    // 0x14f06b0: r0 = "cancel_order"
    //     0x14f06b0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0x14f06b4: ldr             x0, [x0, #0x98]
    // 0x14f06b8: StoreField: r1->field_f = r0
    //     0x14f06b8: stur            w0, [x1, #0xf]
    // 0x14f06bc: r0 = ConstrainedBox()
    //     0x14f06bc: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x14f06c0: ldur            x1, [fp, #-0x20]
    // 0x14f06c4: StoreField: r0->field_f = r1
    //     0x14f06c4: stur            w1, [x0, #0xf]
    // 0x14f06c8: ldur            x1, [fp, #-0x10]
    // 0x14f06cc: StoreField: r0->field_b = r1
    //     0x14f06cc: stur            w1, [x0, #0xb]
    // 0x14f06d0: LeaveFrame
    //     0x14f06d0: mov             SP, fp
    //     0x14f06d4: ldp             fp, lr, [SP], #0x10
    // 0x14f06d8: ret
    //     0x14f06d8: ret             
    // 0x14f06dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f06dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f06e0: b               #0x14f055c
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x14f06e4, size: 0x18c
    // 0x14f06e4: EnterFrame
    //     0x14f06e4: stp             fp, lr, [SP, #-0x10]!
    //     0x14f06e8: mov             fp, SP
    // 0x14f06ec: AllocStack(0x20)
    //     0x14f06ec: sub             SP, SP, #0x20
    // 0x14f06f0: SetupParameters()
    //     0x14f06f0: ldr             x0, [fp, #0x10]
    //     0x14f06f4: ldur            w1, [x0, #0x17]
    //     0x14f06f8: add             x1, x1, HEAP, lsl #32
    //     0x14f06fc: stur            x1, [fp, #-8]
    // 0x14f0700: CheckStackOverflow
    //     0x14f0700: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f0704: cmp             SP, x16
    //     0x14f0708: b.ls            #0x14f0868
    // 0x14f070c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14f070c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14f0710: ldr             x0, [x0, #0x1c80]
    //     0x14f0714: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14f0718: cmp             w0, w16
    //     0x14f071c: b.ne            #0x14f0728
    //     0x14f0720: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14f0724: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14f0728: str             NULL, [SP]
    // 0x14f072c: r4 = const [0x1, 0, 0, 0, null]
    //     0x14f072c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x14f0730: r0 = GetNavigation.back()
    //     0x14f0730: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x14f0734: ldur            x0, [fp, #-8]
    // 0x14f0738: LoadField: r4 = r0->field_b
    //     0x14f0738: ldur            w4, [x0, #0xb]
    // 0x14f073c: DecompressPointer r4
    //     0x14f073c: add             x4, x4, HEAP, lsl #32
    // 0x14f0740: stur            x4, [fp, #-0x10]
    // 0x14f0744: LoadField: r1 = r4->field_f
    //     0x14f0744: ldur            w1, [x4, #0xf]
    // 0x14f0748: DecompressPointer r1
    //     0x14f0748: add             x1, x1, HEAP, lsl #32
    // 0x14f074c: LoadField: r2 = r4->field_13
    //     0x14f074c: ldur            w2, [x4, #0x13]
    // 0x14f0750: DecompressPointer r2
    //     0x14f0750: add             x2, x2, HEAP, lsl #32
    // 0x14f0754: cmp             w2, NULL
    // 0x14f0758: b.ne            #0x14f0764
    // 0x14f075c: r3 = Null
    //     0x14f075c: mov             x3, NULL
    // 0x14f0760: b               #0x14f076c
    // 0x14f0764: LoadField: r3 = r2->field_37
    //     0x14f0764: ldur            w3, [x2, #0x37]
    // 0x14f0768: DecompressPointer r3
    //     0x14f0768: add             x3, x3, HEAP, lsl #32
    // 0x14f076c: cmp             w2, NULL
    // 0x14f0770: b.ne            #0x14f077c
    // 0x14f0774: r5 = Null
    //     0x14f0774: mov             x5, NULL
    // 0x14f0778: b               #0x14f0784
    // 0x14f077c: LoadField: r5 = r2->field_7
    //     0x14f077c: ldur            w5, [x2, #7]
    // 0x14f0780: DecompressPointer r5
    //     0x14f0780: add             x5, x5, HEAP, lsl #32
    // 0x14f0784: cmp             w5, NULL
    // 0x14f0788: b.ne            #0x14f0790
    // 0x14f078c: r5 = ""
    //     0x14f078c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14f0790: LoadField: r6 = r0->field_f
    //     0x14f0790: ldur            w6, [x0, #0xf]
    // 0x14f0794: DecompressPointer r6
    //     0x14f0794: add             x6, x6, HEAP, lsl #32
    // 0x14f0798: cmp             w2, NULL
    // 0x14f079c: b.ne            #0x14f07a8
    // 0x14f07a0: r0 = Null
    //     0x14f07a0: mov             x0, NULL
    // 0x14f07a4: b               #0x14f07b0
    // 0x14f07a8: LoadField: r0 = r2->field_2f
    //     0x14f07a8: ldur            w0, [x2, #0x2f]
    // 0x14f07ac: DecompressPointer r0
    //     0x14f07ac: add             x0, x0, HEAP, lsl #32
    // 0x14f07b0: cmp             w0, NULL
    // 0x14f07b4: b.ne            #0x14f07bc
    // 0x14f07b8: r0 = ""
    //     0x14f07b8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14f07bc: mov             x2, x3
    // 0x14f07c0: mov             x3, x5
    // 0x14f07c4: mov             x5, x6
    // 0x14f07c8: mov             x6, x0
    // 0x14f07cc: r0 = cancelOrder()
    //     0x14f07cc: bl              #0x14f0870  ; [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::cancelOrder
    // 0x14f07d0: ldur            x0, [fp, #-0x10]
    // 0x14f07d4: LoadField: r1 = r0->field_f
    //     0x14f07d4: ldur            w1, [x0, #0xf]
    // 0x14f07d8: DecompressPointer r1
    //     0x14f07d8: add             x1, x1, HEAP, lsl #32
    // 0x14f07dc: r0 = controller()
    //     0x14f07dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f07e0: mov             x1, x0
    // 0x14f07e4: ldur            x0, [fp, #-0x10]
    // 0x14f07e8: stur            x1, [fp, #-0x18]
    // 0x14f07ec: LoadField: r2 = r0->field_13
    //     0x14f07ec: ldur            w2, [x0, #0x13]
    // 0x14f07f0: DecompressPointer r2
    //     0x14f07f0: add             x2, x2, HEAP, lsl #32
    // 0x14f07f4: cmp             w2, NULL
    // 0x14f07f8: b.ne            #0x14f0804
    // 0x14f07fc: r0 = Null
    //     0x14f07fc: mov             x0, NULL
    // 0x14f0800: b               #0x14f080c
    // 0x14f0804: LoadField: r0 = r2->field_7
    //     0x14f0804: ldur            w0, [x2, #7]
    // 0x14f0808: DecompressPointer r0
    //     0x14f0808: add             x0, x0, HEAP, lsl #32
    // 0x14f080c: cmp             w0, NULL
    // 0x14f0810: b.ne            #0x14f0818
    // 0x14f0814: r0 = ""
    //     0x14f0814: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14f0818: stur            x0, [fp, #-8]
    // 0x14f081c: r0 = EventData()
    //     0x14f081c: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x14f0820: mov             x1, x0
    // 0x14f0824: ldur            x0, [fp, #-8]
    // 0x14f0828: stur            x1, [fp, #-0x10]
    // 0x14f082c: StoreField: r1->field_77 = r0
    //     0x14f082c: stur            w0, [x1, #0x77]
    // 0x14f0830: r0 = EventsRequest()
    //     0x14f0830: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x14f0834: mov             x1, x0
    // 0x14f0838: r0 = "cancel_free_gift_continue_clicked"
    //     0x14f0838: add             x0, PP, #0x36, lsl #12  ; [pp+0x360e0] "cancel_free_gift_continue_clicked"
    //     0x14f083c: ldr             x0, [x0, #0xe0]
    // 0x14f0840: StoreField: r1->field_7 = r0
    //     0x14f0840: stur            w0, [x1, #7]
    // 0x14f0844: ldur            x0, [fp, #-0x10]
    // 0x14f0848: StoreField: r1->field_b = r0
    //     0x14f0848: stur            w0, [x1, #0xb]
    // 0x14f084c: mov             x2, x1
    // 0x14f0850: ldur            x1, [fp, #-0x18]
    // 0x14f0854: r0 = postEvents()
    //     0x14f0854: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x14f0858: r0 = Null
    //     0x14f0858: mov             x0, NULL
    // 0x14f085c: LeaveFrame
    //     0x14f085c: mov             SP, fp
    //     0x14f0860: ldp             fp, lr, [SP], #0x10
    // 0x14f0864: ret
    //     0x14f0864: ret             
    // 0x14f0868: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f0868: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f086c: b               #0x14f070c
  }
  _ cancelOrder(/* No info */) {
    // ** addr: 0x14f0870, size: 0x108
    // 0x14f0870: EnterFrame
    //     0x14f0870: stp             fp, lr, [SP, #-0x10]!
    //     0x14f0874: mov             fp, SP
    // 0x14f0878: AllocStack(0x58)
    //     0x14f0878: sub             SP, SP, #0x58
    // 0x14f087c: SetupParameters(OrdersView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */)
    //     0x14f087c: stur            x1, [fp, #-8]
    //     0x14f0880: stur            x2, [fp, #-0x10]
    //     0x14f0884: stur            x3, [fp, #-0x18]
    //     0x14f0888: stur            x5, [fp, #-0x20]
    //     0x14f088c: stur            x6, [fp, #-0x28]
    // 0x14f0890: CheckStackOverflow
    //     0x14f0890: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f0894: cmp             SP, x16
    //     0x14f0898: b.ls            #0x14f0970
    // 0x14f089c: r1 = 4
    //     0x14f089c: movz            x1, #0x4
    // 0x14f08a0: r0 = AllocateContext()
    //     0x14f08a0: bl              #0x16f6108  ; AllocateContextStub
    // 0x14f08a4: ldur            x1, [fp, #-8]
    // 0x14f08a8: stur            x0, [fp, #-0x30]
    // 0x14f08ac: StoreField: r0->field_f = r1
    //     0x14f08ac: stur            w1, [x0, #0xf]
    // 0x14f08b0: ldur            x2, [fp, #-0x10]
    // 0x14f08b4: StoreField: r0->field_13 = r2
    //     0x14f08b4: stur            w2, [x0, #0x13]
    // 0x14f08b8: ldur            x2, [fp, #-0x18]
    // 0x14f08bc: ArrayStore: r0[0] = r2  ; List_4
    //     0x14f08bc: stur            w2, [x0, #0x17]
    // 0x14f08c0: ldur            x2, [fp, #-0x28]
    // 0x14f08c4: StoreField: r0->field_1b = r2
    //     0x14f08c4: stur            w2, [x0, #0x1b]
    // 0x14f08c8: r0 = controller()
    //     0x14f08c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f08cc: ldur            x2, [fp, #-0x30]
    // 0x14f08d0: stur            x0, [fp, #-0x10]
    // 0x14f08d4: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x14f08d4: ldur            w1, [x2, #0x17]
    // 0x14f08d8: DecompressPointer r1
    //     0x14f08d8: add             x1, x1, HEAP, lsl #32
    // 0x14f08dc: stur            x1, [fp, #-8]
    // 0x14f08e0: r0 = EventData()
    //     0x14f08e0: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x14f08e4: mov             x1, x0
    // 0x14f08e8: r0 = "order_page"
    //     0x14f08e8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d710] "order_page"
    //     0x14f08ec: ldr             x0, [x0, #0x710]
    // 0x14f08f0: stur            x1, [fp, #-0x18]
    // 0x14f08f4: StoreField: r1->field_13 = r0
    //     0x14f08f4: stur            w0, [x1, #0x13]
    // 0x14f08f8: ldur            x0, [fp, #-8]
    // 0x14f08fc: StoreField: r1->field_77 = r0
    //     0x14f08fc: stur            w0, [x1, #0x77]
    // 0x14f0900: r0 = EventsRequest()
    //     0x14f0900: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x14f0904: mov             x1, x0
    // 0x14f0908: r0 = "cancel_order_clicked"
    //     0x14f0908: add             x0, PP, #0x36, lsl #12  ; [pp+0x360e8] "cancel_order_clicked"
    //     0x14f090c: ldr             x0, [x0, #0xe8]
    // 0x14f0910: StoreField: r1->field_7 = r0
    //     0x14f0910: stur            w0, [x1, #7]
    // 0x14f0914: ldur            x0, [fp, #-0x18]
    // 0x14f0918: StoreField: r1->field_b = r0
    //     0x14f0918: stur            w0, [x1, #0xb]
    // 0x14f091c: mov             x2, x1
    // 0x14f0920: ldur            x1, [fp, #-0x10]
    // 0x14f0924: r0 = postEvents()
    //     0x14f0924: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x14f0928: ldur            x2, [fp, #-0x30]
    // 0x14f092c: r1 = Function '<anonymous closure>':.
    //     0x14f092c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40230] AnonymousClosure: (0x14f0978), in [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::cancelOrder (0x14f0870)
    //     0x14f0930: ldr             x1, [x1, #0x230]
    // 0x14f0934: r0 = AllocateClosure()
    //     0x14f0934: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f0938: stp             x0, NULL, [SP, #0x18]
    // 0x14f093c: ldur            x16, [fp, #-0x20]
    // 0x14f0940: r30 = Instance_RoundedRectangleBorder
    //     0x14f0940: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x14f0944: ldr             lr, [lr, #0xc78]
    // 0x14f0948: stp             lr, x16, [SP, #8]
    // 0x14f094c: r16 = true
    //     0x14f094c: add             x16, NULL, #0x20  ; true
    // 0x14f0950: str             x16, [SP]
    // 0x14f0954: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x14f0954: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x14f0958: ldr             x4, [x4, #0xd70]
    // 0x14f095c: r0 = showModalBottomSheet()
    //     0x14f095c: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x14f0960: r0 = Null
    //     0x14f0960: mov             x0, NULL
    // 0x14f0964: LeaveFrame
    //     0x14f0964: mov             SP, fp
    //     0x14f0968: ldp             fp, lr, [SP], #0x10
    // 0x14f096c: ret
    //     0x14f096c: ret             
    // 0x14f0970: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f0970: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f0974: b               #0x14f089c
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x14f0978, size: 0x550
    // 0x14f0978: EnterFrame
    //     0x14f0978: stp             fp, lr, [SP, #-0x10]!
    //     0x14f097c: mov             fp, SP
    // 0x14f0980: AllocStack(0x68)
    //     0x14f0980: sub             SP, SP, #0x68
    // 0x14f0984: SetupParameters()
    //     0x14f0984: ldr             x0, [fp, #0x18]
    //     0x14f0988: ldur            w2, [x0, #0x17]
    //     0x14f098c: add             x2, x2, HEAP, lsl #32
    //     0x14f0990: stur            x2, [fp, #-8]
    // 0x14f0994: CheckStackOverflow
    //     0x14f0994: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f0998: cmp             SP, x16
    //     0x14f099c: b.ls            #0x14f0ec0
    // 0x14f09a0: LoadField: r0 = r2->field_13
    //     0x14f09a0: ldur            w0, [x2, #0x13]
    // 0x14f09a4: DecompressPointer r0
    //     0x14f09a4: add             x0, x0, HEAP, lsl #32
    // 0x14f09a8: cmp             w0, NULL
    // 0x14f09ac: b.eq            #0x14f0abc
    // 0x14f09b0: LoadField: r1 = r0->field_13
    //     0x14f09b0: ldur            w1, [x0, #0x13]
    // 0x14f09b4: DecompressPointer r1
    //     0x14f09b4: add             x1, x1, HEAP, lsl #32
    // 0x14f09b8: cmp             w1, NULL
    // 0x14f09bc: b.ne            #0x14f0a0c
    // 0x14f09c0: LoadField: r1 = r0->field_7
    //     0x14f09c0: ldur            w1, [x0, #7]
    // 0x14f09c4: DecompressPointer r1
    //     0x14f09c4: add             x1, x1, HEAP, lsl #32
    // 0x14f09c8: cmp             w1, NULL
    // 0x14f09cc: b.eq            #0x14f0a04
    // 0x14f09d0: LoadField: r0 = r2->field_1b
    //     0x14f09d0: ldur            w0, [x2, #0x1b]
    // 0x14f09d4: DecompressPointer r0
    //     0x14f09d4: add             x0, x0, HEAP, lsl #32
    // 0x14f09d8: r1 = LoadClassIdInstr(r0)
    //     0x14f09d8: ldur            x1, [x0, #-1]
    //     0x14f09dc: ubfx            x1, x1, #0xc, #0x14
    // 0x14f09e0: r16 = "cod"
    //     0x14f09e0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a28] "cod"
    //     0x14f09e4: ldr             x16, [x16, #0xa28]
    // 0x14f09e8: stp             x16, x0, [SP]
    // 0x14f09ec: mov             x0, x1
    // 0x14f09f0: mov             lr, x0
    // 0x14f09f4: ldr             lr, [x21, lr, lsl #3]
    // 0x14f09f8: blr             lr
    // 0x14f09fc: eor             x1, x0, #0x10
    // 0x14f0a00: tbz             w1, #4, #0x14f0a10
    // 0x14f0a04: d0 = inf
    //     0x14f0a04: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x14f0a08: b               #0x14f0a68
    // 0x14f0a0c: tbnz            w1, #4, #0x14f0a64
    // 0x14f0a10: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14f0a10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14f0a14: ldr             x0, [x0, #0x1c80]
    //     0x14f0a18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14f0a1c: cmp             w0, w16
    //     0x14f0a20: b.ne            #0x14f0a2c
    //     0x14f0a24: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14f0a28: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14f0a2c: r0 = GetNavigation.size()
    //     0x14f0a2c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x14f0a30: LoadField: d0 = r0->field_f
    //     0x14f0a30: ldur            d0, [x0, #0xf]
    // 0x14f0a34: d1 = 0.800000
    //     0x14f0a34: add             x17, PP, #0x32, lsl #12  ; [pp+0x32b28] IMM: double(0.8) from 0x3fe999999999999a
    //     0x14f0a38: ldr             d1, [x17, #0xb28]
    // 0x14f0a3c: fmul            d2, d0, d1
    // 0x14f0a40: stur            d2, [fp, #-0x58]
    // 0x14f0a44: r0 = BoxConstraints()
    //     0x14f0a44: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x14f0a48: StoreField: r0->field_7 = rZR
    //     0x14f0a48: stur            xzr, [x0, #7]
    // 0x14f0a4c: d0 = inf
    //     0x14f0a4c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x14f0a50: StoreField: r0->field_f = d0
    //     0x14f0a50: stur            d0, [x0, #0xf]
    // 0x14f0a54: ArrayStore: r0[0] = rZR  ; List_8
    //     0x14f0a54: stur            xzr, [x0, #0x17]
    // 0x14f0a58: ldur            d0, [fp, #-0x58]
    // 0x14f0a5c: StoreField: r0->field_1f = d0
    //     0x14f0a5c: stur            d0, [x0, #0x1f]
    // 0x14f0a60: b               #0x14f0b10
    // 0x14f0a64: d0 = inf
    //     0x14f0a64: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x14f0a68: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14f0a68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14f0a6c: ldr             x0, [x0, #0x1c80]
    //     0x14f0a70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14f0a74: cmp             w0, w16
    //     0x14f0a78: b.ne            #0x14f0a84
    //     0x14f0a7c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14f0a80: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14f0a84: r0 = GetNavigation.size()
    //     0x14f0a84: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x14f0a88: LoadField: d0 = r0->field_f
    //     0x14f0a88: ldur            d0, [x0, #0xf]
    // 0x14f0a8c: d1 = 0.430000
    //     0x14f0a8c: add             x17, PP, #0x36, lsl #12  ; [pp+0x360b8] IMM: double(0.43) from 0x3fdb851eb851eb85
    //     0x14f0a90: ldr             d1, [x17, #0xb8]
    // 0x14f0a94: fmul            d2, d0, d1
    // 0x14f0a98: stur            d2, [fp, #-0x58]
    // 0x14f0a9c: r0 = BoxConstraints()
    //     0x14f0a9c: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x14f0aa0: StoreField: r0->field_7 = rZR
    //     0x14f0aa0: stur            xzr, [x0, #7]
    // 0x14f0aa4: d0 = inf
    //     0x14f0aa4: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x14f0aa8: StoreField: r0->field_f = d0
    //     0x14f0aa8: stur            d0, [x0, #0xf]
    // 0x14f0aac: ArrayStore: r0[0] = rZR  ; List_8
    //     0x14f0aac: stur            xzr, [x0, #0x17]
    // 0x14f0ab0: ldur            d0, [fp, #-0x58]
    // 0x14f0ab4: StoreField: r0->field_1f = d0
    //     0x14f0ab4: stur            d0, [x0, #0x1f]
    // 0x14f0ab8: b               #0x14f0b10
    // 0x14f0abc: d0 = inf
    //     0x14f0abc: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x14f0ac0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14f0ac0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14f0ac4: ldr             x0, [x0, #0x1c80]
    //     0x14f0ac8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14f0acc: cmp             w0, w16
    //     0x14f0ad0: b.ne            #0x14f0adc
    //     0x14f0ad4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14f0ad8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14f0adc: r0 = GetNavigation.size()
    //     0x14f0adc: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x14f0ae0: LoadField: d0 = r0->field_f
    //     0x14f0ae0: ldur            d0, [x0, #0xf]
    // 0x14f0ae4: d1 = 0.300000
    //     0x14f0ae4: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x14f0ae8: ldr             d1, [x17, #0x658]
    // 0x14f0aec: fmul            d2, d0, d1
    // 0x14f0af0: stur            d2, [fp, #-0x58]
    // 0x14f0af4: r0 = BoxConstraints()
    //     0x14f0af4: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x14f0af8: StoreField: r0->field_7 = rZR
    //     0x14f0af8: stur            xzr, [x0, #7]
    // 0x14f0afc: d0 = inf
    //     0x14f0afc: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x14f0b00: StoreField: r0->field_f = d0
    //     0x14f0b00: stur            d0, [x0, #0xf]
    // 0x14f0b04: ArrayStore: r0[0] = rZR  ; List_8
    //     0x14f0b04: stur            xzr, [x0, #0x17]
    // 0x14f0b08: ldur            d0, [fp, #-0x58]
    // 0x14f0b0c: StoreField: r0->field_1f = d0
    //     0x14f0b0c: stur            d0, [x0, #0x1f]
    // 0x14f0b10: ldur            x2, [fp, #-8]
    // 0x14f0b14: stur            x0, [fp, #-0x10]
    // 0x14f0b18: LoadField: r1 = r2->field_f
    //     0x14f0b18: ldur            w1, [x2, #0xf]
    // 0x14f0b1c: DecompressPointer r1
    //     0x14f0b1c: add             x1, x1, HEAP, lsl #32
    // 0x14f0b20: r0 = controller()
    //     0x14f0b20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f0b24: LoadField: r1 = r0->field_67
    //     0x14f0b24: ldur            w1, [x0, #0x67]
    // 0x14f0b28: DecompressPointer r1
    //     0x14f0b28: add             x1, x1, HEAP, lsl #32
    // 0x14f0b2c: r0 = value()
    //     0x14f0b2c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f0b30: cmp             w0, NULL
    // 0x14f0b34: b.ne            #0x14f0b40
    // 0x14f0b38: r3 = Null
    //     0x14f0b38: mov             x3, NULL
    // 0x14f0b3c: b               #0x14f0b4c
    // 0x14f0b40: LoadField: r1 = r0->field_2b
    //     0x14f0b40: ldur            w1, [x0, #0x2b]
    // 0x14f0b44: DecompressPointer r1
    //     0x14f0b44: add             x1, x1, HEAP, lsl #32
    // 0x14f0b48: mov             x3, x1
    // 0x14f0b4c: ldur            x0, [fp, #-8]
    // 0x14f0b50: stur            x3, [fp, #-0x18]
    // 0x14f0b54: r1 = Null
    //     0x14f0b54: mov             x1, NULL
    // 0x14f0b58: r2 = 4
    //     0x14f0b58: movz            x2, #0x4
    // 0x14f0b5c: r0 = AllocateArray()
    //     0x14f0b5c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14f0b60: mov             x1, x0
    // 0x14f0b64: ldur            x0, [fp, #-0x18]
    // 0x14f0b68: StoreField: r1->field_f = r0
    //     0x14f0b68: stur            w0, [x1, #0xf]
    // 0x14f0b6c: r16 = " has accepted your order, it is already under process"
    //     0x14f0b6c: add             x16, PP, #0x36, lsl #12  ; [pp+0x360f8] " has accepted your order, it is already under process"
    //     0x14f0b70: ldr             x16, [x16, #0xf8]
    // 0x14f0b74: StoreField: r1->field_13 = r16
    //     0x14f0b74: stur            w16, [x1, #0x13]
    // 0x14f0b78: str             x1, [SP]
    // 0x14f0b7c: r0 = _interpolate()
    //     0x14f0b7c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14f0b80: mov             x1, x0
    // 0x14f0b84: ldur            x2, [fp, #-8]
    // 0x14f0b88: stur            x1, [fp, #-0x18]
    // 0x14f0b8c: LoadField: r0 = r2->field_13
    //     0x14f0b8c: ldur            w0, [x2, #0x13]
    // 0x14f0b90: DecompressPointer r0
    //     0x14f0b90: add             x0, x0, HEAP, lsl #32
    // 0x14f0b94: cmp             w0, NULL
    // 0x14f0b98: b.eq            #0x14f0c40
    // 0x14f0b9c: LoadField: r3 = r0->field_b
    //     0x14f0b9c: ldur            w3, [x0, #0xb]
    // 0x14f0ba0: DecompressPointer r3
    //     0x14f0ba0: add             x3, x3, HEAP, lsl #32
    // 0x14f0ba4: cmp             w3, NULL
    // 0x14f0ba8: b.eq            #0x14f0c40
    // 0x14f0bac: r0 = 60
    //     0x14f0bac: movz            x0, #0x3c
    // 0x14f0bb0: branchIfSmi(r3, 0x14f0bbc)
    //     0x14f0bb0: tbz             w3, #0, #0x14f0bbc
    // 0x14f0bb4: r0 = LoadClassIdInstr(r3)
    //     0x14f0bb4: ldur            x0, [x3, #-1]
    //     0x14f0bb8: ubfx            x0, x0, #0xc, #0x14
    // 0x14f0bbc: stp             xzr, x3, [SP]
    // 0x14f0bc0: mov             lr, x0
    // 0x14f0bc4: ldr             lr, [x21, lr, lsl #3]
    // 0x14f0bc8: blr             lr
    // 0x14f0bcc: tbz             w0, #4, #0x14f0c40
    // 0x14f0bd0: ldur            x0, [fp, #-8]
    // 0x14f0bd4: r1 = Null
    //     0x14f0bd4: mov             x1, NULL
    // 0x14f0bd8: r2 = 6
    //     0x14f0bd8: movz            x2, #0x6
    // 0x14f0bdc: r0 = AllocateArray()
    //     0x14f0bdc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14f0be0: r16 = "Cancel ("
    //     0x14f0be0: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3fde8] "Cancel ("
    //     0x14f0be4: ldr             x16, [x16, #0xde8]
    // 0x14f0be8: StoreField: r0->field_f = r16
    //     0x14f0be8: stur            w16, [x0, #0xf]
    // 0x14f0bec: ldur            x2, [fp, #-8]
    // 0x14f0bf0: LoadField: r1 = r2->field_13
    //     0x14f0bf0: ldur            w1, [x2, #0x13]
    // 0x14f0bf4: DecompressPointer r1
    //     0x14f0bf4: add             x1, x1, HEAP, lsl #32
    // 0x14f0bf8: cmp             w1, NULL
    // 0x14f0bfc: b.ne            #0x14f0c08
    // 0x14f0c00: r1 = Null
    //     0x14f0c00: mov             x1, NULL
    // 0x14f0c04: b               #0x14f0c14
    // 0x14f0c08: LoadField: r3 = r1->field_23
    //     0x14f0c08: ldur            w3, [x1, #0x23]
    // 0x14f0c0c: DecompressPointer r3
    //     0x14f0c0c: add             x3, x3, HEAP, lsl #32
    // 0x14f0c10: mov             x1, x3
    // 0x14f0c14: cmp             w1, NULL
    // 0x14f0c18: b.ne            #0x14f0c20
    // 0x14f0c1c: r1 = ""
    //     0x14f0c1c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14f0c20: StoreField: r0->field_13 = r1
    //     0x14f0c20: stur            w1, [x0, #0x13]
    // 0x14f0c24: r16 = " Charge)"
    //     0x14f0c24: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3fdf0] " Charge)"
    //     0x14f0c28: ldr             x16, [x16, #0xdf0]
    // 0x14f0c2c: ArrayStore: r0[0] = r16  ; List_4
    //     0x14f0c2c: stur            w16, [x0, #0x17]
    // 0x14f0c30: str             x0, [SP]
    // 0x14f0c34: r0 = _interpolate()
    //     0x14f0c34: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14f0c38: mov             x4, x0
    // 0x14f0c3c: b               #0x14f0c48
    // 0x14f0c40: r4 = "Cancel Order"
    //     0x14f0c40: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fdf8] "Cancel Order"
    //     0x14f0c44: ldr             x4, [x4, #0xdf8]
    // 0x14f0c48: ldur            x2, [fp, #-8]
    // 0x14f0c4c: ldur            x3, [fp, #-0x10]
    // 0x14f0c50: ldur            x0, [fp, #-0x18]
    // 0x14f0c54: stur            x4, [fp, #-0x30]
    // 0x14f0c58: LoadField: r5 = r2->field_13
    //     0x14f0c58: ldur            w5, [x2, #0x13]
    // 0x14f0c5c: DecompressPointer r5
    //     0x14f0c5c: add             x5, x5, HEAP, lsl #32
    // 0x14f0c60: stur            x5, [fp, #-0x28]
    // 0x14f0c64: LoadField: r6 = r2->field_1b
    //     0x14f0c64: ldur            w6, [x2, #0x1b]
    // 0x14f0c68: DecompressPointer r6
    //     0x14f0c68: add             x6, x6, HEAP, lsl #32
    // 0x14f0c6c: ldr             x1, [fp, #0x10]
    // 0x14f0c70: stur            x6, [fp, #-0x20]
    // 0x14f0c74: r0 = of()
    //     0x14f0c74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f0c78: LoadField: r1 = r0->field_87
    //     0x14f0c78: ldur            w1, [x0, #0x87]
    // 0x14f0c7c: DecompressPointer r1
    //     0x14f0c7c: add             x1, x1, HEAP, lsl #32
    // 0x14f0c80: LoadField: r0 = r1->field_2b
    //     0x14f0c80: ldur            w0, [x1, #0x2b]
    // 0x14f0c84: DecompressPointer r0
    //     0x14f0c84: add             x0, x0, HEAP, lsl #32
    // 0x14f0c88: stur            x0, [fp, #-0x38]
    // 0x14f0c8c: r1 = Instance_Color
    //     0x14f0c8c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14f0c90: d0 = 0.400000
    //     0x14f0c90: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14f0c94: r0 = withOpacity()
    //     0x14f0c94: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14f0c98: r16 = 12.000000
    //     0x14f0c98: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14f0c9c: ldr             x16, [x16, #0x9e8]
    // 0x14f0ca0: stp             x0, x16, [SP]
    // 0x14f0ca4: ldur            x1, [fp, #-0x38]
    // 0x14f0ca8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14f0ca8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14f0cac: ldr             x4, [x4, #0xaa0]
    // 0x14f0cb0: r0 = copyWith()
    //     0x14f0cb0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f0cb4: stur            x0, [fp, #-0x38]
    // 0x14f0cb8: r0 = Radius()
    //     0x14f0cb8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14f0cbc: d0 = 12.000000
    //     0x14f0cbc: fmov            d0, #12.00000000
    // 0x14f0cc0: stur            x0, [fp, #-0x40]
    // 0x14f0cc4: StoreField: r0->field_7 = d0
    //     0x14f0cc4: stur            d0, [x0, #7]
    // 0x14f0cc8: StoreField: r0->field_f = d0
    //     0x14f0cc8: stur            d0, [x0, #0xf]
    // 0x14f0ccc: r0 = BorderRadius()
    //     0x14f0ccc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14f0cd0: mov             x1, x0
    // 0x14f0cd4: ldur            x0, [fp, #-0x40]
    // 0x14f0cd8: stur            x1, [fp, #-0x48]
    // 0x14f0cdc: StoreField: r1->field_7 = r0
    //     0x14f0cdc: stur            w0, [x1, #7]
    // 0x14f0ce0: StoreField: r1->field_b = r0
    //     0x14f0ce0: stur            w0, [x1, #0xb]
    // 0x14f0ce4: StoreField: r1->field_f = r0
    //     0x14f0ce4: stur            w0, [x1, #0xf]
    // 0x14f0ce8: StoreField: r1->field_13 = r0
    //     0x14f0ce8: stur            w0, [x1, #0x13]
    // 0x14f0cec: r0 = OutlineInputBorder()
    //     0x14f0cec: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0x14f0cf0: mov             x1, x0
    // 0x14f0cf4: ldur            x0, [fp, #-0x48]
    // 0x14f0cf8: stur            x1, [fp, #-0x40]
    // 0x14f0cfc: StoreField: r1->field_13 = r0
    //     0x14f0cfc: stur            w0, [x1, #0x13]
    // 0x14f0d00: d0 = 4.000000
    //     0x14f0d00: fmov            d0, #4.00000000
    // 0x14f0d04: StoreField: r1->field_b = d0
    //     0x14f0d04: stur            d0, [x1, #0xb]
    // 0x14f0d08: r0 = Instance_BorderSide
    //     0x14f0d08: add             x0, PP, #0x36, lsl #12  ; [pp+0x36118] Obj!BorderSide@d62ef1
    //     0x14f0d0c: ldr             x0, [x0, #0x118]
    // 0x14f0d10: StoreField: r1->field_7 = r0
    //     0x14f0d10: stur            w0, [x1, #7]
    // 0x14f0d14: r0 = Radius()
    //     0x14f0d14: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14f0d18: d0 = 12.000000
    //     0x14f0d18: fmov            d0, #12.00000000
    // 0x14f0d1c: stur            x0, [fp, #-0x48]
    // 0x14f0d20: StoreField: r0->field_7 = d0
    //     0x14f0d20: stur            d0, [x0, #7]
    // 0x14f0d24: StoreField: r0->field_f = d0
    //     0x14f0d24: stur            d0, [x0, #0xf]
    // 0x14f0d28: r0 = BorderRadius()
    //     0x14f0d28: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14f0d2c: mov             x1, x0
    // 0x14f0d30: ldur            x0, [fp, #-0x48]
    // 0x14f0d34: stur            x1, [fp, #-0x50]
    // 0x14f0d38: StoreField: r1->field_7 = r0
    //     0x14f0d38: stur            w0, [x1, #7]
    // 0x14f0d3c: StoreField: r1->field_b = r0
    //     0x14f0d3c: stur            w0, [x1, #0xb]
    // 0x14f0d40: StoreField: r1->field_f = r0
    //     0x14f0d40: stur            w0, [x1, #0xf]
    // 0x14f0d44: StoreField: r1->field_13 = r0
    //     0x14f0d44: stur            w0, [x1, #0x13]
    // 0x14f0d48: r0 = OutlineInputBorder()
    //     0x14f0d48: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0x14f0d4c: mov             x1, x0
    // 0x14f0d50: ldur            x0, [fp, #-0x50]
    // 0x14f0d54: stur            x1, [fp, #-0x48]
    // 0x14f0d58: StoreField: r1->field_13 = r0
    //     0x14f0d58: stur            w0, [x1, #0x13]
    // 0x14f0d5c: d0 = 4.000000
    //     0x14f0d5c: fmov            d0, #4.00000000
    // 0x14f0d60: StoreField: r1->field_b = d0
    //     0x14f0d60: stur            d0, [x1, #0xb]
    // 0x14f0d64: r0 = Instance_BorderSide
    //     0x14f0d64: add             x0, PP, #0x36, lsl #12  ; [pp+0x36118] Obj!BorderSide@d62ef1
    //     0x14f0d68: ldr             x0, [x0, #0x118]
    // 0x14f0d6c: StoreField: r1->field_7 = r0
    //     0x14f0d6c: stur            w0, [x1, #7]
    // 0x14f0d70: r0 = InputDecoration()
    //     0x14f0d70: bl              #0x81349c  ; AllocateInputDecorationStub -> InputDecoration (size=0xec)
    // 0x14f0d74: mov             x2, x0
    // 0x14f0d78: r0 = "Mention the reason"
    //     0x14f0d78: add             x0, PP, #0x36, lsl #12  ; [pp+0x36120] "Mention the reason"
    //     0x14f0d7c: ldr             x0, [x0, #0x120]
    // 0x14f0d80: stur            x2, [fp, #-0x50]
    // 0x14f0d84: StoreField: r2->field_13 = r0
    //     0x14f0d84: stur            w0, [x2, #0x13]
    // 0x14f0d88: ldur            x0, [fp, #-0x38]
    // 0x14f0d8c: ArrayStore: r2[0] = r0  ; List_4
    //     0x14f0d8c: stur            w0, [x2, #0x17]
    // 0x14f0d90: r0 = true
    //     0x14f0d90: add             x0, NULL, #0x20  ; true
    // 0x14f0d94: StoreField: r2->field_47 = r0
    //     0x14f0d94: stur            w0, [x2, #0x47]
    // 0x14f0d98: StoreField: r2->field_4b = r0
    //     0x14f0d98: stur            w0, [x2, #0x4b]
    // 0x14f0d9c: ldur            x1, [fp, #-0x48]
    // 0x14f0da0: StoreField: r2->field_c3 = r1
    //     0x14f0da0: stur            w1, [x2, #0xc3]
    // 0x14f0da4: ldur            x1, [fp, #-0x40]
    // 0x14f0da8: StoreField: r2->field_cf = r1
    //     0x14f0da8: stur            w1, [x2, #0xcf]
    // 0x14f0dac: StoreField: r2->field_d7 = r0
    //     0x14f0dac: stur            w0, [x2, #0xd7]
    // 0x14f0db0: ldr             x1, [fp, #0x10]
    // 0x14f0db4: r0 = of()
    //     0x14f0db4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f0db8: LoadField: r1 = r0->field_5b
    //     0x14f0db8: ldur            w1, [x0, #0x5b]
    // 0x14f0dbc: DecompressPointer r1
    //     0x14f0dbc: add             x1, x1, HEAP, lsl #32
    // 0x14f0dc0: stur            x1, [fp, #-0x38]
    // 0x14f0dc4: r0 = BorderSide()
    //     0x14f0dc4: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x14f0dc8: mov             x1, x0
    // 0x14f0dcc: ldur            x0, [fp, #-0x38]
    // 0x14f0dd0: stur            x1, [fp, #-0x40]
    // 0x14f0dd4: StoreField: r1->field_7 = r0
    //     0x14f0dd4: stur            w0, [x1, #7]
    // 0x14f0dd8: d0 = 1.000000
    //     0x14f0dd8: fmov            d0, #1.00000000
    // 0x14f0ddc: StoreField: r1->field_b = d0
    //     0x14f0ddc: stur            d0, [x1, #0xb]
    // 0x14f0de0: r0 = Instance_BorderStyle
    //     0x14f0de0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x14f0de4: ldr             x0, [x0, #0xf68]
    // 0x14f0de8: StoreField: r1->field_13 = r0
    //     0x14f0de8: stur            w0, [x1, #0x13]
    // 0x14f0dec: d0 = -1.000000
    //     0x14f0dec: fmov            d0, #-1.00000000
    // 0x14f0df0: ArrayStore: r1[0] = d0  ; List_8
    //     0x14f0df0: stur            d0, [x1, #0x17]
    // 0x14f0df4: r0 = Radius()
    //     0x14f0df4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14f0df8: d0 = 30.000000
    //     0x14f0df8: fmov            d0, #30.00000000
    // 0x14f0dfc: stur            x0, [fp, #-0x38]
    // 0x14f0e00: StoreField: r0->field_7 = d0
    //     0x14f0e00: stur            d0, [x0, #7]
    // 0x14f0e04: StoreField: r0->field_f = d0
    //     0x14f0e04: stur            d0, [x0, #0xf]
    // 0x14f0e08: r0 = BorderRadius()
    //     0x14f0e08: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14f0e0c: mov             x1, x0
    // 0x14f0e10: ldur            x0, [fp, #-0x38]
    // 0x14f0e14: stur            x1, [fp, #-0x48]
    // 0x14f0e18: StoreField: r1->field_7 = r0
    //     0x14f0e18: stur            w0, [x1, #7]
    // 0x14f0e1c: StoreField: r1->field_b = r0
    //     0x14f0e1c: stur            w0, [x1, #0xb]
    // 0x14f0e20: StoreField: r1->field_f = r0
    //     0x14f0e20: stur            w0, [x1, #0xf]
    // 0x14f0e24: StoreField: r1->field_13 = r0
    //     0x14f0e24: stur            w0, [x1, #0x13]
    // 0x14f0e28: r0 = CancelOrderConfirmBottomSheet()
    //     0x14f0e28: bl              #0x8b1964  ; AllocateCancelOrderConfirmBottomSheetStub -> CancelOrderConfirmBottomSheet (size=0x34)
    // 0x14f0e2c: mov             x3, x0
    // 0x14f0e30: ldur            x0, [fp, #-0x28]
    // 0x14f0e34: stur            x3, [fp, #-0x38]
    // 0x14f0e38: StoreField: r3->field_b = r0
    //     0x14f0e38: stur            w0, [x3, #0xb]
    // 0x14f0e3c: ldur            x0, [fp, #-0x18]
    // 0x14f0e40: StoreField: r3->field_f = r0
    //     0x14f0e40: stur            w0, [x3, #0xf]
    // 0x14f0e44: r0 = "Do you want to cancel this order\?"
    //     0x14f0e44: add             x0, PP, #0x36, lsl #12  ; [pp+0x36128] "Do you want to cancel this order\?"
    //     0x14f0e48: ldr             x0, [x0, #0x128]
    // 0x14f0e4c: StoreField: r3->field_13 = r0
    //     0x14f0e4c: stur            w0, [x3, #0x13]
    // 0x14f0e50: ldur            x0, [fp, #-0x30]
    // 0x14f0e54: ArrayStore: r3[0] = r0  ; List_4
    //     0x14f0e54: stur            w0, [x3, #0x17]
    // 0x14f0e58: ldur            x2, [fp, #-8]
    // 0x14f0e5c: r1 = Function '<anonymous closure>':.
    //     0x14f0e5c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40238] AnonymousClosure: (0x8b19a0), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelOrder (0x8b12cc)
    //     0x14f0e60: ldr             x1, [x1, #0x238]
    // 0x14f0e64: r0 = AllocateClosure()
    //     0x14f0e64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f0e68: mov             x1, x0
    // 0x14f0e6c: ldur            x0, [fp, #-0x38]
    // 0x14f0e70: StoreField: r0->field_1b = r1
    //     0x14f0e70: stur            w1, [x0, #0x1b]
    // 0x14f0e74: ldur            x1, [fp, #-0x48]
    // 0x14f0e78: StoreField: r0->field_1f = r1
    //     0x14f0e78: stur            w1, [x0, #0x1f]
    // 0x14f0e7c: ldur            x1, [fp, #-0x50]
    // 0x14f0e80: StoreField: r0->field_23 = r1
    //     0x14f0e80: stur            w1, [x0, #0x23]
    // 0x14f0e84: ldur            x1, [fp, #-0x40]
    // 0x14f0e88: StoreField: r0->field_27 = r1
    //     0x14f0e88: stur            w1, [x0, #0x27]
    // 0x14f0e8c: r1 = "Go, Back"
    //     0x14f0e8c: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fe08] "Go, Back"
    //     0x14f0e90: ldr             x1, [x1, #0xe08]
    // 0x14f0e94: StoreField: r0->field_2b = r1
    //     0x14f0e94: stur            w1, [x0, #0x2b]
    // 0x14f0e98: ldur            x1, [fp, #-0x20]
    // 0x14f0e9c: StoreField: r0->field_2f = r1
    //     0x14f0e9c: stur            w1, [x0, #0x2f]
    // 0x14f0ea0: r0 = ConstrainedBox()
    //     0x14f0ea0: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x14f0ea4: ldur            x1, [fp, #-0x10]
    // 0x14f0ea8: StoreField: r0->field_f = r1
    //     0x14f0ea8: stur            w1, [x0, #0xf]
    // 0x14f0eac: ldur            x1, [fp, #-0x38]
    // 0x14f0eb0: StoreField: r0->field_b = r1
    //     0x14f0eb0: stur            w1, [x0, #0xb]
    // 0x14f0eb4: LeaveFrame
    //     0x14f0eb4: mov             SP, fp
    //     0x14f0eb8: ldp             fp, lr, [SP], #0x10
    // 0x14f0ebc: ret
    //     0x14f0ebc: ret             
    // 0x14f0ec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f0ec0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f0ec4: b               #0x14f09a0
  }
  [closure] void cancelOrder(dynamic, OrderCancellationPopup?, String, BuildContext, String) {
    // ** addr: 0x14f0ec8, size: 0x48
    // 0x14f0ec8: EnterFrame
    //     0x14f0ec8: stp             fp, lr, [SP, #-0x10]!
    //     0x14f0ecc: mov             fp, SP
    // 0x14f0ed0: ldr             x0, [fp, #0x30]
    // 0x14f0ed4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14f0ed4: ldur            w1, [x0, #0x17]
    // 0x14f0ed8: DecompressPointer r1
    //     0x14f0ed8: add             x1, x1, HEAP, lsl #32
    // 0x14f0edc: CheckStackOverflow
    //     0x14f0edc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f0ee0: cmp             SP, x16
    //     0x14f0ee4: b.ls            #0x14f0f08
    // 0x14f0ee8: ldr             x2, [fp, #0x28]
    // 0x14f0eec: ldr             x3, [fp, #0x20]
    // 0x14f0ef0: ldr             x5, [fp, #0x18]
    // 0x14f0ef4: ldr             x6, [fp, #0x10]
    // 0x14f0ef8: r0 = cancelOrder()
    //     0x14f0ef8: bl              #0x14f0870  ; [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::cancelOrder
    // 0x14f0efc: LeaveFrame
    //     0x14f0efc: mov             SP, fp
    //     0x14f0f00: ldp             fp, lr, [SP], #0x10
    // 0x14f0f04: ret
    //     0x14f0f04: ret             
    // 0x14f0f08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f0f08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f0f0c: b               #0x14f0ee8
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e2e00, size: 0x2b4
    // 0x15e2e00: EnterFrame
    //     0x15e2e00: stp             fp, lr, [SP, #-0x10]!
    //     0x15e2e04: mov             fp, SP
    // 0x15e2e08: AllocStack(0x30)
    //     0x15e2e08: sub             SP, SP, #0x30
    // 0x15e2e0c: SetupParameters(OrdersView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e2e0c: stur            x1, [fp, #-8]
    //     0x15e2e10: stur            x2, [fp, #-0x10]
    // 0x15e2e14: CheckStackOverflow
    //     0x15e2e14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e2e18: cmp             SP, x16
    //     0x15e2e1c: b.ls            #0x15e30ac
    // 0x15e2e20: r1 = 2
    //     0x15e2e20: movz            x1, #0x2
    // 0x15e2e24: r0 = AllocateContext()
    //     0x15e2e24: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e2e28: ldur            x1, [fp, #-8]
    // 0x15e2e2c: stur            x0, [fp, #-0x18]
    // 0x15e2e30: StoreField: r0->field_f = r1
    //     0x15e2e30: stur            w1, [x0, #0xf]
    // 0x15e2e34: ldur            x2, [fp, #-0x10]
    // 0x15e2e38: StoreField: r0->field_13 = r2
    //     0x15e2e38: stur            w2, [x0, #0x13]
    // 0x15e2e3c: r0 = Obx()
    //     0x15e2e3c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e2e40: ldur            x2, [fp, #-0x18]
    // 0x15e2e44: r1 = Function '<anonymous closure>':.
    //     0x15e2e44: add             x1, PP, #0x40, lsl #12  ; [pp+0x40240] AnonymousClosure: (0x15d3254), in [package:customer_app/app/presentation/views/glass/profile/profile_view.dart] ProfileView::appBar (0x15e5558)
    //     0x15e2e48: ldr             x1, [x1, #0x240]
    // 0x15e2e4c: stur            x0, [fp, #-0x10]
    // 0x15e2e50: r0 = AllocateClosure()
    //     0x15e2e50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e2e54: mov             x1, x0
    // 0x15e2e58: ldur            x0, [fp, #-0x10]
    // 0x15e2e5c: StoreField: r0->field_b = r1
    //     0x15e2e5c: stur            w1, [x0, #0xb]
    // 0x15e2e60: ldur            x1, [fp, #-8]
    // 0x15e2e64: r0 = controller()
    //     0x15e2e64: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e2e68: LoadField: r1 = r0->field_7f
    //     0x15e2e68: ldur            w1, [x0, #0x7f]
    // 0x15e2e6c: DecompressPointer r1
    //     0x15e2e6c: add             x1, x1, HEAP, lsl #32
    // 0x15e2e70: r0 = value()
    //     0x15e2e70: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e2e74: tbnz            w0, #4, #0x15e2f0c
    // 0x15e2e78: ldur            x2, [fp, #-0x18]
    // 0x15e2e7c: LoadField: r1 = r2->field_13
    //     0x15e2e7c: ldur            w1, [x2, #0x13]
    // 0x15e2e80: DecompressPointer r1
    //     0x15e2e80: add             x1, x1, HEAP, lsl #32
    // 0x15e2e84: r0 = of()
    //     0x15e2e84: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e2e88: LoadField: r1 = r0->field_5b
    //     0x15e2e88: ldur            w1, [x0, #0x5b]
    // 0x15e2e8c: DecompressPointer r1
    //     0x15e2e8c: add             x1, x1, HEAP, lsl #32
    // 0x15e2e90: stur            x1, [fp, #-8]
    // 0x15e2e94: r0 = ColorFilter()
    //     0x15e2e94: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e2e98: mov             x1, x0
    // 0x15e2e9c: ldur            x0, [fp, #-8]
    // 0x15e2ea0: stur            x1, [fp, #-0x20]
    // 0x15e2ea4: StoreField: r1->field_7 = r0
    //     0x15e2ea4: stur            w0, [x1, #7]
    // 0x15e2ea8: r0 = Instance_BlendMode
    //     0x15e2ea8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e2eac: ldr             x0, [x0, #0xb30]
    // 0x15e2eb0: StoreField: r1->field_b = r0
    //     0x15e2eb0: stur            w0, [x1, #0xb]
    // 0x15e2eb4: r2 = 1
    //     0x15e2eb4: movz            x2, #0x1
    // 0x15e2eb8: StoreField: r1->field_13 = r2
    //     0x15e2eb8: stur            x2, [x1, #0x13]
    // 0x15e2ebc: r0 = SvgPicture()
    //     0x15e2ebc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e2ec0: stur            x0, [fp, #-8]
    // 0x15e2ec4: ldur            x16, [fp, #-0x20]
    // 0x15e2ec8: str             x16, [SP]
    // 0x15e2ecc: mov             x1, x0
    // 0x15e2ed0: r2 = "assets/images/search.svg"
    //     0x15e2ed0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e2ed4: ldr             x2, [x2, #0xa30]
    // 0x15e2ed8: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e2ed8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e2edc: ldr             x4, [x4, #0xa38]
    // 0x15e2ee0: r0 = SvgPicture.asset()
    //     0x15e2ee0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e2ee4: r0 = Align()
    //     0x15e2ee4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e2ee8: r3 = Instance_Alignment
    //     0x15e2ee8: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e2eec: ldr             x3, [x3, #0xb10]
    // 0x15e2ef0: StoreField: r0->field_f = r3
    //     0x15e2ef0: stur            w3, [x0, #0xf]
    // 0x15e2ef4: r4 = 1.000000
    //     0x15e2ef4: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e2ef8: StoreField: r0->field_13 = r4
    //     0x15e2ef8: stur            w4, [x0, #0x13]
    // 0x15e2efc: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e2efc: stur            w4, [x0, #0x17]
    // 0x15e2f00: ldur            x1, [fp, #-8]
    // 0x15e2f04: StoreField: r0->field_b = r1
    //     0x15e2f04: stur            w1, [x0, #0xb]
    // 0x15e2f08: b               #0x15e2fbc
    // 0x15e2f0c: ldur            x5, [fp, #-0x18]
    // 0x15e2f10: r4 = 1.000000
    //     0x15e2f10: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e2f14: r0 = Instance_BlendMode
    //     0x15e2f14: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e2f18: ldr             x0, [x0, #0xb30]
    // 0x15e2f1c: r3 = Instance_Alignment
    //     0x15e2f1c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e2f20: ldr             x3, [x3, #0xb10]
    // 0x15e2f24: r2 = 1
    //     0x15e2f24: movz            x2, #0x1
    // 0x15e2f28: LoadField: r1 = r5->field_13
    //     0x15e2f28: ldur            w1, [x5, #0x13]
    // 0x15e2f2c: DecompressPointer r1
    //     0x15e2f2c: add             x1, x1, HEAP, lsl #32
    // 0x15e2f30: r0 = of()
    //     0x15e2f30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e2f34: LoadField: r1 = r0->field_5b
    //     0x15e2f34: ldur            w1, [x0, #0x5b]
    // 0x15e2f38: DecompressPointer r1
    //     0x15e2f38: add             x1, x1, HEAP, lsl #32
    // 0x15e2f3c: stur            x1, [fp, #-8]
    // 0x15e2f40: r0 = ColorFilter()
    //     0x15e2f40: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e2f44: mov             x1, x0
    // 0x15e2f48: ldur            x0, [fp, #-8]
    // 0x15e2f4c: stur            x1, [fp, #-0x20]
    // 0x15e2f50: StoreField: r1->field_7 = r0
    //     0x15e2f50: stur            w0, [x1, #7]
    // 0x15e2f54: r0 = Instance_BlendMode
    //     0x15e2f54: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e2f58: ldr             x0, [x0, #0xb30]
    // 0x15e2f5c: StoreField: r1->field_b = r0
    //     0x15e2f5c: stur            w0, [x1, #0xb]
    // 0x15e2f60: r0 = 1
    //     0x15e2f60: movz            x0, #0x1
    // 0x15e2f64: StoreField: r1->field_13 = r0
    //     0x15e2f64: stur            x0, [x1, #0x13]
    // 0x15e2f68: r0 = SvgPicture()
    //     0x15e2f68: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e2f6c: stur            x0, [fp, #-8]
    // 0x15e2f70: ldur            x16, [fp, #-0x20]
    // 0x15e2f74: str             x16, [SP]
    // 0x15e2f78: mov             x1, x0
    // 0x15e2f7c: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e2f7c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e2f80: ldr             x2, [x2, #0xa40]
    // 0x15e2f84: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e2f84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e2f88: ldr             x4, [x4, #0xa38]
    // 0x15e2f8c: r0 = SvgPicture.asset()
    //     0x15e2f8c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e2f90: r0 = Align()
    //     0x15e2f90: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e2f94: mov             x1, x0
    // 0x15e2f98: r0 = Instance_Alignment
    //     0x15e2f98: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e2f9c: ldr             x0, [x0, #0xb10]
    // 0x15e2fa0: StoreField: r1->field_f = r0
    //     0x15e2fa0: stur            w0, [x1, #0xf]
    // 0x15e2fa4: r0 = 1.000000
    //     0x15e2fa4: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e2fa8: StoreField: r1->field_13 = r0
    //     0x15e2fa8: stur            w0, [x1, #0x13]
    // 0x15e2fac: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e2fac: stur            w0, [x1, #0x17]
    // 0x15e2fb0: ldur            x0, [fp, #-8]
    // 0x15e2fb4: StoreField: r1->field_b = r0
    //     0x15e2fb4: stur            w0, [x1, #0xb]
    // 0x15e2fb8: mov             x0, x1
    // 0x15e2fbc: stur            x0, [fp, #-8]
    // 0x15e2fc0: r0 = InkWell()
    //     0x15e2fc0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e2fc4: mov             x3, x0
    // 0x15e2fc8: ldur            x0, [fp, #-8]
    // 0x15e2fcc: stur            x3, [fp, #-0x20]
    // 0x15e2fd0: StoreField: r3->field_b = r0
    //     0x15e2fd0: stur            w0, [x3, #0xb]
    // 0x15e2fd4: ldur            x2, [fp, #-0x18]
    // 0x15e2fd8: r1 = Function '<anonymous closure>':.
    //     0x15e2fd8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40248] AnonymousClosure: (0x15d3188), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::appBar (0x15eaca0)
    //     0x15e2fdc: ldr             x1, [x1, #0x248]
    // 0x15e2fe0: r0 = AllocateClosure()
    //     0x15e2fe0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e2fe4: ldur            x2, [fp, #-0x20]
    // 0x15e2fe8: StoreField: r2->field_f = r0
    //     0x15e2fe8: stur            w0, [x2, #0xf]
    // 0x15e2fec: r0 = true
    //     0x15e2fec: add             x0, NULL, #0x20  ; true
    // 0x15e2ff0: StoreField: r2->field_43 = r0
    //     0x15e2ff0: stur            w0, [x2, #0x43]
    // 0x15e2ff4: r1 = Instance_BoxShape
    //     0x15e2ff4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e2ff8: ldr             x1, [x1, #0x80]
    // 0x15e2ffc: StoreField: r2->field_47 = r1
    //     0x15e2ffc: stur            w1, [x2, #0x47]
    // 0x15e3000: StoreField: r2->field_6f = r0
    //     0x15e3000: stur            w0, [x2, #0x6f]
    // 0x15e3004: r1 = false
    //     0x15e3004: add             x1, NULL, #0x30  ; false
    // 0x15e3008: StoreField: r2->field_73 = r1
    //     0x15e3008: stur            w1, [x2, #0x73]
    // 0x15e300c: StoreField: r2->field_83 = r0
    //     0x15e300c: stur            w0, [x2, #0x83]
    // 0x15e3010: StoreField: r2->field_7b = r1
    //     0x15e3010: stur            w1, [x2, #0x7b]
    // 0x15e3014: r0 = Obx()
    //     0x15e3014: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e3018: ldur            x2, [fp, #-0x18]
    // 0x15e301c: r1 = Function '<anonymous closure>':.
    //     0x15e301c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40250] AnonymousClosure: (0x15e30b4), in [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::appBar (0x15e2e00)
    //     0x15e3020: ldr             x1, [x1, #0x250]
    // 0x15e3024: stur            x0, [fp, #-8]
    // 0x15e3028: r0 = AllocateClosure()
    //     0x15e3028: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e302c: mov             x1, x0
    // 0x15e3030: ldur            x0, [fp, #-8]
    // 0x15e3034: StoreField: r0->field_b = r1
    //     0x15e3034: stur            w1, [x0, #0xb]
    // 0x15e3038: r1 = Null
    //     0x15e3038: mov             x1, NULL
    // 0x15e303c: r2 = 2
    //     0x15e303c: movz            x2, #0x2
    // 0x15e3040: r0 = AllocateArray()
    //     0x15e3040: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15e3044: mov             x2, x0
    // 0x15e3048: ldur            x0, [fp, #-8]
    // 0x15e304c: stur            x2, [fp, #-0x18]
    // 0x15e3050: StoreField: r2->field_f = r0
    //     0x15e3050: stur            w0, [x2, #0xf]
    // 0x15e3054: r1 = <Widget>
    //     0x15e3054: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15e3058: r0 = AllocateGrowableArray()
    //     0x15e3058: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15e305c: mov             x1, x0
    // 0x15e3060: ldur            x0, [fp, #-0x18]
    // 0x15e3064: stur            x1, [fp, #-8]
    // 0x15e3068: StoreField: r1->field_f = r0
    //     0x15e3068: stur            w0, [x1, #0xf]
    // 0x15e306c: r0 = 2
    //     0x15e306c: movz            x0, #0x2
    // 0x15e3070: StoreField: r1->field_b = r0
    //     0x15e3070: stur            w0, [x1, #0xb]
    // 0x15e3074: r0 = AppBar()
    //     0x15e3074: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e3078: stur            x0, [fp, #-0x18]
    // 0x15e307c: ldur            x16, [fp, #-0x10]
    // 0x15e3080: ldur            lr, [fp, #-8]
    // 0x15e3084: stp             lr, x16, [SP]
    // 0x15e3088: mov             x1, x0
    // 0x15e308c: ldur            x2, [fp, #-0x20]
    // 0x15e3090: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15e3090: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15e3094: ldr             x4, [x4, #0xa58]
    // 0x15e3098: r0 = AppBar()
    //     0x15e3098: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e309c: ldur            x0, [fp, #-0x18]
    // 0x15e30a0: LeaveFrame
    //     0x15e30a0: mov             SP, fp
    //     0x15e30a4: ldp             fp, lr, [SP], #0x10
    // 0x15e30a8: ret
    //     0x15e30a8: ret             
    // 0x15e30ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e30ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e30b0: b               #0x15e2e20
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15e30b4, size: 0x2fc
    // 0x15e30b4: EnterFrame
    //     0x15e30b4: stp             fp, lr, [SP, #-0x10]!
    //     0x15e30b8: mov             fp, SP
    // 0x15e30bc: AllocStack(0x58)
    //     0x15e30bc: sub             SP, SP, #0x58
    // 0x15e30c0: SetupParameters()
    //     0x15e30c0: ldr             x0, [fp, #0x10]
    //     0x15e30c4: ldur            w2, [x0, #0x17]
    //     0x15e30c8: add             x2, x2, HEAP, lsl #32
    //     0x15e30cc: stur            x2, [fp, #-8]
    // 0x15e30d0: CheckStackOverflow
    //     0x15e30d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e30d4: cmp             SP, x16
    //     0x15e30d8: b.ls            #0x15e33a8
    // 0x15e30dc: LoadField: r1 = r2->field_f
    //     0x15e30dc: ldur            w1, [x2, #0xf]
    // 0x15e30e0: DecompressPointer r1
    //     0x15e30e0: add             x1, x1, HEAP, lsl #32
    // 0x15e30e4: r0 = controller()
    //     0x15e30e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e30e8: LoadField: r1 = r0->field_6b
    //     0x15e30e8: ldur            w1, [x0, #0x6b]
    // 0x15e30ec: DecompressPointer r1
    //     0x15e30ec: add             x1, x1, HEAP, lsl #32
    // 0x15e30f0: r0 = value()
    //     0x15e30f0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e30f4: LoadField: r1 = r0->field_1f
    //     0x15e30f4: ldur            w1, [x0, #0x1f]
    // 0x15e30f8: DecompressPointer r1
    //     0x15e30f8: add             x1, x1, HEAP, lsl #32
    // 0x15e30fc: cmp             w1, NULL
    // 0x15e3100: b.ne            #0x15e310c
    // 0x15e3104: r0 = Null
    //     0x15e3104: mov             x0, NULL
    // 0x15e3108: b               #0x15e3114
    // 0x15e310c: LoadField: r0 = r1->field_7
    //     0x15e310c: ldur            w0, [x1, #7]
    // 0x15e3110: DecompressPointer r0
    //     0x15e3110: add             x0, x0, HEAP, lsl #32
    // 0x15e3114: cmp             w0, NULL
    // 0x15e3118: b.ne            #0x15e3124
    // 0x15e311c: r0 = false
    //     0x15e311c: add             x0, NULL, #0x30  ; false
    // 0x15e3120: b               #0x15e3310
    // 0x15e3124: tbnz            w0, #4, #0x15e330c
    // 0x15e3128: ldur            x2, [fp, #-8]
    // 0x15e312c: LoadField: r1 = r2->field_f
    //     0x15e312c: ldur            w1, [x2, #0xf]
    // 0x15e3130: DecompressPointer r1
    //     0x15e3130: add             x1, x1, HEAP, lsl #32
    // 0x15e3134: r0 = controller()
    //     0x15e3134: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e3138: LoadField: r1 = r0->field_83
    //     0x15e3138: ldur            w1, [x0, #0x83]
    // 0x15e313c: DecompressPointer r1
    //     0x15e313c: add             x1, x1, HEAP, lsl #32
    // 0x15e3140: r0 = value()
    //     0x15e3140: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e3144: ldur            x2, [fp, #-8]
    // 0x15e3148: stur            x0, [fp, #-0x10]
    // 0x15e314c: LoadField: r1 = r2->field_13
    //     0x15e314c: ldur            w1, [x2, #0x13]
    // 0x15e3150: DecompressPointer r1
    //     0x15e3150: add             x1, x1, HEAP, lsl #32
    // 0x15e3154: r0 = of()
    //     0x15e3154: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e3158: LoadField: r2 = r0->field_5b
    //     0x15e3158: ldur            w2, [x0, #0x5b]
    // 0x15e315c: DecompressPointer r2
    //     0x15e315c: add             x2, x2, HEAP, lsl #32
    // 0x15e3160: ldur            x0, [fp, #-8]
    // 0x15e3164: stur            x2, [fp, #-0x18]
    // 0x15e3168: LoadField: r1 = r0->field_f
    //     0x15e3168: ldur            w1, [x0, #0xf]
    // 0x15e316c: DecompressPointer r1
    //     0x15e316c: add             x1, x1, HEAP, lsl #32
    // 0x15e3170: r0 = controller()
    //     0x15e3170: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e3174: LoadField: r1 = r0->field_87
    //     0x15e3174: ldur            w1, [x0, #0x87]
    // 0x15e3178: DecompressPointer r1
    //     0x15e3178: add             x1, x1, HEAP, lsl #32
    // 0x15e317c: r0 = value()
    //     0x15e317c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e3180: cmp             w0, NULL
    // 0x15e3184: r16 = true
    //     0x15e3184: add             x16, NULL, #0x20  ; true
    // 0x15e3188: r17 = false
    //     0x15e3188: add             x17, NULL, #0x30  ; false
    // 0x15e318c: csel            x2, x16, x17, ne
    // 0x15e3190: ldur            x0, [fp, #-8]
    // 0x15e3194: stur            x2, [fp, #-0x20]
    // 0x15e3198: LoadField: r1 = r0->field_f
    //     0x15e3198: ldur            w1, [x0, #0xf]
    // 0x15e319c: DecompressPointer r1
    //     0x15e319c: add             x1, x1, HEAP, lsl #32
    // 0x15e31a0: r0 = controller()
    //     0x15e31a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e31a4: LoadField: r1 = r0->field_87
    //     0x15e31a4: ldur            w1, [x0, #0x87]
    // 0x15e31a8: DecompressPointer r1
    //     0x15e31a8: add             x1, x1, HEAP, lsl #32
    // 0x15e31ac: r0 = value()
    //     0x15e31ac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e31b0: str             x0, [SP]
    // 0x15e31b4: r0 = _interpolateSingle()
    //     0x15e31b4: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15e31b8: ldur            x2, [fp, #-8]
    // 0x15e31bc: stur            x0, [fp, #-0x28]
    // 0x15e31c0: LoadField: r1 = r2->field_13
    //     0x15e31c0: ldur            w1, [x2, #0x13]
    // 0x15e31c4: DecompressPointer r1
    //     0x15e31c4: add             x1, x1, HEAP, lsl #32
    // 0x15e31c8: r0 = of()
    //     0x15e31c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e31cc: LoadField: r1 = r0->field_87
    //     0x15e31cc: ldur            w1, [x0, #0x87]
    // 0x15e31d0: DecompressPointer r1
    //     0x15e31d0: add             x1, x1, HEAP, lsl #32
    // 0x15e31d4: LoadField: r0 = r1->field_27
    //     0x15e31d4: ldur            w0, [x1, #0x27]
    // 0x15e31d8: DecompressPointer r0
    //     0x15e31d8: add             x0, x0, HEAP, lsl #32
    // 0x15e31dc: r16 = Instance_Color
    //     0x15e31dc: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15e31e0: str             x16, [SP]
    // 0x15e31e4: mov             x1, x0
    // 0x15e31e8: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15e31e8: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15e31ec: ldr             x4, [x4, #0xf40]
    // 0x15e31f0: r0 = copyWith()
    //     0x15e31f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15e31f4: stur            x0, [fp, #-0x30]
    // 0x15e31f8: r0 = Text()
    //     0x15e31f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15e31fc: mov             x2, x0
    // 0x15e3200: ldur            x0, [fp, #-0x28]
    // 0x15e3204: stur            x2, [fp, #-0x38]
    // 0x15e3208: StoreField: r2->field_b = r0
    //     0x15e3208: stur            w0, [x2, #0xb]
    // 0x15e320c: ldur            x0, [fp, #-0x30]
    // 0x15e3210: StoreField: r2->field_13 = r0
    //     0x15e3210: stur            w0, [x2, #0x13]
    // 0x15e3214: ldur            x0, [fp, #-8]
    // 0x15e3218: LoadField: r1 = r0->field_13
    //     0x15e3218: ldur            w1, [x0, #0x13]
    // 0x15e321c: DecompressPointer r1
    //     0x15e321c: add             x1, x1, HEAP, lsl #32
    // 0x15e3220: r0 = of()
    //     0x15e3220: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e3224: LoadField: r1 = r0->field_5b
    //     0x15e3224: ldur            w1, [x0, #0x5b]
    // 0x15e3228: DecompressPointer r1
    //     0x15e3228: add             x1, x1, HEAP, lsl #32
    // 0x15e322c: stur            x1, [fp, #-0x28]
    // 0x15e3230: r0 = ColorFilter()
    //     0x15e3230: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e3234: mov             x1, x0
    // 0x15e3238: ldur            x0, [fp, #-0x28]
    // 0x15e323c: stur            x1, [fp, #-0x30]
    // 0x15e3240: StoreField: r1->field_7 = r0
    //     0x15e3240: stur            w0, [x1, #7]
    // 0x15e3244: r0 = Instance_BlendMode
    //     0x15e3244: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e3248: ldr             x0, [x0, #0xb30]
    // 0x15e324c: StoreField: r1->field_b = r0
    //     0x15e324c: stur            w0, [x1, #0xb]
    // 0x15e3250: r0 = 1
    //     0x15e3250: movz            x0, #0x1
    // 0x15e3254: StoreField: r1->field_13 = r0
    //     0x15e3254: stur            x0, [x1, #0x13]
    // 0x15e3258: r0 = SvgPicture()
    //     0x15e3258: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e325c: stur            x0, [fp, #-0x28]
    // 0x15e3260: r16 = Instance_BoxFit
    //     0x15e3260: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15e3264: ldr             x16, [x16, #0xb18]
    // 0x15e3268: r30 = 24.000000
    //     0x15e3268: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15e326c: ldr             lr, [lr, #0xba8]
    // 0x15e3270: stp             lr, x16, [SP, #0x10]
    // 0x15e3274: r16 = 24.000000
    //     0x15e3274: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15e3278: ldr             x16, [x16, #0xba8]
    // 0x15e327c: ldur            lr, [fp, #-0x30]
    // 0x15e3280: stp             lr, x16, [SP]
    // 0x15e3284: mov             x1, x0
    // 0x15e3288: r2 = "assets/images/shopping_bag.svg"
    //     0x15e3288: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15e328c: ldr             x2, [x2, #0xa60]
    // 0x15e3290: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15e3290: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15e3294: ldr             x4, [x4, #0xa68]
    // 0x15e3298: r0 = SvgPicture.asset()
    //     0x15e3298: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e329c: r0 = Badge()
    //     0x15e329c: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15e32a0: mov             x1, x0
    // 0x15e32a4: ldur            x0, [fp, #-0x18]
    // 0x15e32a8: stur            x1, [fp, #-0x30]
    // 0x15e32ac: StoreField: r1->field_b = r0
    //     0x15e32ac: stur            w0, [x1, #0xb]
    // 0x15e32b0: ldur            x0, [fp, #-0x38]
    // 0x15e32b4: StoreField: r1->field_27 = r0
    //     0x15e32b4: stur            w0, [x1, #0x27]
    // 0x15e32b8: ldur            x0, [fp, #-0x20]
    // 0x15e32bc: StoreField: r1->field_2b = r0
    //     0x15e32bc: stur            w0, [x1, #0x2b]
    // 0x15e32c0: ldur            x0, [fp, #-0x28]
    // 0x15e32c4: StoreField: r1->field_2f = r0
    //     0x15e32c4: stur            w0, [x1, #0x2f]
    // 0x15e32c8: r0 = Visibility()
    //     0x15e32c8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15e32cc: mov             x1, x0
    // 0x15e32d0: ldur            x0, [fp, #-0x30]
    // 0x15e32d4: StoreField: r1->field_b = r0
    //     0x15e32d4: stur            w0, [x1, #0xb]
    // 0x15e32d8: r0 = Instance_SizedBox
    //     0x15e32d8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15e32dc: StoreField: r1->field_f = r0
    //     0x15e32dc: stur            w0, [x1, #0xf]
    // 0x15e32e0: ldur            x0, [fp, #-0x10]
    // 0x15e32e4: StoreField: r1->field_13 = r0
    //     0x15e32e4: stur            w0, [x1, #0x13]
    // 0x15e32e8: r0 = false
    //     0x15e32e8: add             x0, NULL, #0x30  ; false
    // 0x15e32ec: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e32ec: stur            w0, [x1, #0x17]
    // 0x15e32f0: StoreField: r1->field_1b = r0
    //     0x15e32f0: stur            w0, [x1, #0x1b]
    // 0x15e32f4: StoreField: r1->field_1f = r0
    //     0x15e32f4: stur            w0, [x1, #0x1f]
    // 0x15e32f8: StoreField: r1->field_23 = r0
    //     0x15e32f8: stur            w0, [x1, #0x23]
    // 0x15e32fc: StoreField: r1->field_27 = r0
    //     0x15e32fc: stur            w0, [x1, #0x27]
    // 0x15e3300: StoreField: r1->field_2b = r0
    //     0x15e3300: stur            w0, [x1, #0x2b]
    // 0x15e3304: mov             x0, x1
    // 0x15e3308: b               #0x15e3328
    // 0x15e330c: r0 = false
    //     0x15e330c: add             x0, NULL, #0x30  ; false
    // 0x15e3310: r0 = Container()
    //     0x15e3310: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15e3314: mov             x1, x0
    // 0x15e3318: stur            x0, [fp, #-0x10]
    // 0x15e331c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15e331c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15e3320: r0 = Container()
    //     0x15e3320: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15e3324: ldur            x0, [fp, #-0x10]
    // 0x15e3328: stur            x0, [fp, #-0x10]
    // 0x15e332c: r0 = InkWell()
    //     0x15e332c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e3330: mov             x3, x0
    // 0x15e3334: ldur            x0, [fp, #-0x10]
    // 0x15e3338: stur            x3, [fp, #-0x18]
    // 0x15e333c: StoreField: r3->field_b = r0
    //     0x15e333c: stur            w0, [x3, #0xb]
    // 0x15e3340: ldur            x2, [fp, #-8]
    // 0x15e3344: r1 = Function '<anonymous closure>':.
    //     0x15e3344: add             x1, PP, #0x40, lsl #12  ; [pp+0x40258] AnonymousClosure: (0x15e33b0), in [package:customer_app/app/presentation/views/glass/orders/orders_view.dart] OrdersView::appBar (0x15e2e00)
    //     0x15e3348: ldr             x1, [x1, #0x258]
    // 0x15e334c: r0 = AllocateClosure()
    //     0x15e334c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e3350: mov             x1, x0
    // 0x15e3354: ldur            x0, [fp, #-0x18]
    // 0x15e3358: StoreField: r0->field_f = r1
    //     0x15e3358: stur            w1, [x0, #0xf]
    // 0x15e335c: r1 = true
    //     0x15e335c: add             x1, NULL, #0x20  ; true
    // 0x15e3360: StoreField: r0->field_43 = r1
    //     0x15e3360: stur            w1, [x0, #0x43]
    // 0x15e3364: r2 = Instance_BoxShape
    //     0x15e3364: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e3368: ldr             x2, [x2, #0x80]
    // 0x15e336c: StoreField: r0->field_47 = r2
    //     0x15e336c: stur            w2, [x0, #0x47]
    // 0x15e3370: StoreField: r0->field_6f = r1
    //     0x15e3370: stur            w1, [x0, #0x6f]
    // 0x15e3374: r2 = false
    //     0x15e3374: add             x2, NULL, #0x30  ; false
    // 0x15e3378: StoreField: r0->field_73 = r2
    //     0x15e3378: stur            w2, [x0, #0x73]
    // 0x15e337c: StoreField: r0->field_83 = r1
    //     0x15e337c: stur            w1, [x0, #0x83]
    // 0x15e3380: StoreField: r0->field_7b = r2
    //     0x15e3380: stur            w2, [x0, #0x7b]
    // 0x15e3384: r0 = Padding()
    //     0x15e3384: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15e3388: r1 = Instance_EdgeInsets
    //     0x15e3388: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea78] Obj!EdgeInsets@d5a0e1
    //     0x15e338c: ldr             x1, [x1, #0xa78]
    // 0x15e3390: StoreField: r0->field_f = r1
    //     0x15e3390: stur            w1, [x0, #0xf]
    // 0x15e3394: ldur            x1, [fp, #-0x18]
    // 0x15e3398: StoreField: r0->field_b = r1
    //     0x15e3398: stur            w1, [x0, #0xb]
    // 0x15e339c: LeaveFrame
    //     0x15e339c: mov             SP, fp
    //     0x15e33a0: ldp             fp, lr, [SP], #0x10
    // 0x15e33a4: ret
    //     0x15e33a4: ret             
    // 0x15e33a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e33a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e33ac: b               #0x15e30dc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15e33b0, size: 0xdc
    // 0x15e33b0: EnterFrame
    //     0x15e33b0: stp             fp, lr, [SP, #-0x10]!
    //     0x15e33b4: mov             fp, SP
    // 0x15e33b8: AllocStack(0x28)
    //     0x15e33b8: sub             SP, SP, #0x28
    // 0x15e33bc: SetupParameters()
    //     0x15e33bc: ldr             x0, [fp, #0x10]
    //     0x15e33c0: ldur            w2, [x0, #0x17]
    //     0x15e33c4: add             x2, x2, HEAP, lsl #32
    //     0x15e33c8: stur            x2, [fp, #-8]
    // 0x15e33cc: CheckStackOverflow
    //     0x15e33cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e33d0: cmp             SP, x16
    //     0x15e33d4: b.ls            #0x15e3484
    // 0x15e33d8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15e33d8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15e33dc: ldr             x0, [x0, #0x1c80]
    //     0x15e33e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15e33e4: cmp             w0, w16
    //     0x15e33e8: b.ne            #0x15e33f4
    //     0x15e33ec: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15e33f0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15e33f4: r1 = Null
    //     0x15e33f4: mov             x1, NULL
    // 0x15e33f8: r2 = 4
    //     0x15e33f8: movz            x2, #0x4
    // 0x15e33fc: r0 = AllocateArray()
    //     0x15e33fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15e3400: r16 = "previousScreenSource"
    //     0x15e3400: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x15e3404: ldr             x16, [x16, #0x448]
    // 0x15e3408: StoreField: r0->field_f = r16
    //     0x15e3408: stur            w16, [x0, #0xf]
    // 0x15e340c: r16 = "order_page"
    //     0x15e340c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d710] "order_page"
    //     0x15e3410: ldr             x16, [x16, #0x710]
    // 0x15e3414: StoreField: r0->field_13 = r16
    //     0x15e3414: stur            w16, [x0, #0x13]
    // 0x15e3418: r16 = <String, String>
    //     0x15e3418: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x15e341c: ldr             x16, [x16, #0x788]
    // 0x15e3420: stp             x0, x16, [SP]
    // 0x15e3424: r0 = Map._fromLiteral()
    //     0x15e3424: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x15e3428: r16 = "/bag"
    //     0x15e3428: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x15e342c: ldr             x16, [x16, #0x468]
    // 0x15e3430: stp             x16, NULL, [SP, #8]
    // 0x15e3434: str             x0, [SP]
    // 0x15e3438: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x15e3438: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x15e343c: ldr             x4, [x4, #0x438]
    // 0x15e3440: r0 = GetNavigation.toNamed()
    //     0x15e3440: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15e3444: stur            x0, [fp, #-0x10]
    // 0x15e3448: cmp             w0, NULL
    // 0x15e344c: b.eq            #0x15e3474
    // 0x15e3450: ldur            x2, [fp, #-8]
    // 0x15e3454: r1 = Function '<anonymous closure>':.
    //     0x15e3454: add             x1, PP, #0x40, lsl #12  ; [pp+0x40260] AnonymousClosure: (0x15d2d68), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::appBar (0x15eaca0)
    //     0x15e3458: ldr             x1, [x1, #0x260]
    // 0x15e345c: r0 = AllocateClosure()
    //     0x15e345c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e3460: ldur            x16, [fp, #-0x10]
    // 0x15e3464: stp             x16, NULL, [SP, #8]
    // 0x15e3468: str             x0, [SP]
    // 0x15e346c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15e346c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15e3470: r0 = then()
    //     0x15e3470: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15e3474: r0 = Null
    //     0x15e3474: mov             x0, NULL
    // 0x15e3478: LeaveFrame
    //     0x15e3478: mov             SP, fp
    //     0x15e347c: ldp             fp, lr, [SP], #0x10
    // 0x15e3480: ret
    //     0x15e3480: ret             
    // 0x15e3484: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e3484: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e3488: b               #0x15e33d8
  }
}
