// lib: , url: package:customer_app/app/presentation/views/glass/post_order/replace_order/replace_call_order_view.dart

// class id: 1049425, size: 0x8
class :: {
}

// class id: 4558, size: 0x14, field offset: 0x14
//   const constructor, 
class ReplaceCallOrderView extends BaseView<dynamic> {

  [closure] ReplaceOrderWidget <anonymous closure>(dynamic) {
    // ** addr: 0x14413c0, size: 0x90
    // 0x14413c0: EnterFrame
    //     0x14413c0: stp             fp, lr, [SP, #-0x10]!
    //     0x14413c4: mov             fp, SP
    // 0x14413c8: AllocStack(0x10)
    //     0x14413c8: sub             SP, SP, #0x10
    // 0x14413cc: SetupParameters()
    //     0x14413cc: ldr             x0, [fp, #0x10]
    //     0x14413d0: ldur            w2, [x0, #0x17]
    //     0x14413d4: add             x2, x2, HEAP, lsl #32
    //     0x14413d8: stur            x2, [fp, #-8]
    // 0x14413dc: CheckStackOverflow
    //     0x14413dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14413e0: cmp             SP, x16
    //     0x14413e4: b.ls            #0x1441448
    // 0x14413e8: LoadField: r1 = r2->field_f
    //     0x14413e8: ldur            w1, [x2, #0xf]
    // 0x14413ec: DecompressPointer r1
    //     0x14413ec: add             x1, x1, HEAP, lsl #32
    // 0x14413f0: r0 = controller()
    //     0x14413f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14413f4: LoadField: r1 = r0->field_47
    //     0x14413f4: ldur            w1, [x0, #0x47]
    // 0x14413f8: DecompressPointer r1
    //     0x14413f8: add             x1, x1, HEAP, lsl #32
    // 0x14413fc: r0 = value()
    //     0x14413fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1441400: mov             x2, x0
    // 0x1441404: ldur            x0, [fp, #-8]
    // 0x1441408: stur            x2, [fp, #-0x10]
    // 0x144140c: LoadField: r1 = r0->field_f
    //     0x144140c: ldur            w1, [x0, #0xf]
    // 0x1441410: DecompressPointer r1
    //     0x1441410: add             x1, x1, HEAP, lsl #32
    // 0x1441414: r0 = controller()
    //     0x1441414: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1441418: LoadField: r1 = r0->field_4b
    //     0x1441418: ldur            w1, [x0, #0x4b]
    // 0x144141c: DecompressPointer r1
    //     0x144141c: add             x1, x1, HEAP, lsl #32
    // 0x1441420: r0 = value()
    //     0x1441420: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1441424: stur            x0, [fp, #-8]
    // 0x1441428: r0 = ReplaceOrderWidget()
    //     0x1441428: bl              #0x1441488  ; AllocateReplaceOrderWidgetStub -> ReplaceOrderWidget (size=0x14)
    // 0x144142c: ldur            x1, [fp, #-0x10]
    // 0x1441430: StoreField: r0->field_b = r1
    //     0x1441430: stur            w1, [x0, #0xb]
    // 0x1441434: ldur            x1, [fp, #-8]
    // 0x1441438: StoreField: r0->field_f = r1
    //     0x1441438: stur            w1, [x0, #0xf]
    // 0x144143c: LeaveFrame
    //     0x144143c: mov             SP, fp
    //     0x1441440: ldp             fp, lr, [SP], #0x10
    // 0x1441444: ret
    //     0x1441444: ret             
    // 0x1441448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1441448: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x144144c: b               #0x14413e8
  }
  _ body(/* No info */) {
    // ** addr: 0x14f8384, size: 0x88
    // 0x14f8384: EnterFrame
    //     0x14f8384: stp             fp, lr, [SP, #-0x10]!
    //     0x14f8388: mov             fp, SP
    // 0x14f838c: AllocStack(0x18)
    //     0x14f838c: sub             SP, SP, #0x18
    // 0x14f8390: SetupParameters(ReplaceCallOrderView this /* r1 => r1, fp-0x8 */)
    //     0x14f8390: stur            x1, [fp, #-8]
    // 0x14f8394: r1 = 1
    //     0x14f8394: movz            x1, #0x1
    // 0x14f8398: r0 = AllocateContext()
    //     0x14f8398: bl              #0x16f6108  ; AllocateContextStub
    // 0x14f839c: mov             x1, x0
    // 0x14f83a0: ldur            x0, [fp, #-8]
    // 0x14f83a4: stur            x1, [fp, #-0x10]
    // 0x14f83a8: StoreField: r1->field_f = r0
    //     0x14f83a8: stur            w0, [x1, #0xf]
    // 0x14f83ac: r0 = Obx()
    //     0x14f83ac: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14f83b0: ldur            x2, [fp, #-0x10]
    // 0x14f83b4: r1 = Function '<anonymous closure>':.
    //     0x14f83b4: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f480] AnonymousClosure: (0x14413c0), in [package:customer_app/app/presentation/views/glass/post_order/replace_order/replace_call_order_view.dart] ReplaceCallOrderView::body (0x14f8384)
    //     0x14f83b8: ldr             x1, [x1, #0x480]
    // 0x14f83bc: stur            x0, [fp, #-8]
    // 0x14f83c0: r0 = AllocateClosure()
    //     0x14f83c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f83c4: mov             x1, x0
    // 0x14f83c8: ldur            x0, [fp, #-8]
    // 0x14f83cc: StoreField: r0->field_b = r1
    //     0x14f83cc: stur            w1, [x0, #0xb]
    // 0x14f83d0: r0 = WillPopScope()
    //     0x14f83d0: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14f83d4: mov             x3, x0
    // 0x14f83d8: ldur            x0, [fp, #-8]
    // 0x14f83dc: stur            x3, [fp, #-0x18]
    // 0x14f83e0: StoreField: r3->field_b = r0
    //     0x14f83e0: stur            w0, [x3, #0xb]
    // 0x14f83e4: ldur            x2, [fp, #-0x10]
    // 0x14f83e8: r1 = Function '<anonymous closure>':.
    //     0x14f83e8: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f488] AnonymousClosure: (0x137aadc), in [package:customer_app/app/presentation/views/line/post_order/replace_order/replace_call_order_view.dart] ReplaceCallOrderView::body (0x1506d28)
    //     0x14f83ec: ldr             x1, [x1, #0x488]
    // 0x14f83f0: r0 = AllocateClosure()
    //     0x14f83f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f83f4: mov             x1, x0
    // 0x14f83f8: ldur            x0, [fp, #-0x18]
    // 0x14f83fc: StoreField: r0->field_f = r1
    //     0x14f83fc: stur            w1, [x0, #0xf]
    // 0x14f8400: LeaveFrame
    //     0x14f8400: mov             SP, fp
    //     0x14f8404: ldp             fp, lr, [SP], #0x10
    // 0x14f8408: ret
    //     0x14f8408: ret             
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e44fc, size: 0x1bc
    // 0x15e44fc: EnterFrame
    //     0x15e44fc: stp             fp, lr, [SP, #-0x10]!
    //     0x15e4500: mov             fp, SP
    // 0x15e4504: AllocStack(0x30)
    //     0x15e4504: sub             SP, SP, #0x30
    // 0x15e4508: SetupParameters(ReplaceCallOrderView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15e4508: mov             x0, x1
    //     0x15e450c: stur            x1, [fp, #-8]
    //     0x15e4510: mov             x1, x2
    //     0x15e4514: stur            x2, [fp, #-0x10]
    // 0x15e4518: CheckStackOverflow
    //     0x15e4518: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e451c: cmp             SP, x16
    //     0x15e4520: b.ls            #0x15e46b0
    // 0x15e4524: r1 = 1
    //     0x15e4524: movz            x1, #0x1
    // 0x15e4528: r0 = AllocateContext()
    //     0x15e4528: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e452c: mov             x2, x0
    // 0x15e4530: ldur            x0, [fp, #-8]
    // 0x15e4534: stur            x2, [fp, #-0x18]
    // 0x15e4538: StoreField: r2->field_f = r0
    //     0x15e4538: stur            w0, [x2, #0xf]
    // 0x15e453c: ldur            x1, [fp, #-0x10]
    // 0x15e4540: r0 = of()
    //     0x15e4540: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e4544: LoadField: r1 = r0->field_87
    //     0x15e4544: ldur            w1, [x0, #0x87]
    // 0x15e4548: DecompressPointer r1
    //     0x15e4548: add             x1, x1, HEAP, lsl #32
    // 0x15e454c: LoadField: r0 = r1->field_2b
    //     0x15e454c: ldur            w0, [x1, #0x2b]
    // 0x15e4550: DecompressPointer r0
    //     0x15e4550: add             x0, x0, HEAP, lsl #32
    // 0x15e4554: r16 = 16.000000
    //     0x15e4554: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15e4558: ldr             x16, [x16, #0x188]
    // 0x15e455c: r30 = Instance_Color
    //     0x15e455c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15e4560: stp             lr, x16, [SP]
    // 0x15e4564: mov             x1, x0
    // 0x15e4568: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15e4568: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15e456c: ldr             x4, [x4, #0xaa0]
    // 0x15e4570: r0 = copyWith()
    //     0x15e4570: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15e4574: stur            x0, [fp, #-8]
    // 0x15e4578: r0 = Text()
    //     0x15e4578: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15e457c: mov             x2, x0
    // 0x15e4580: r0 = "Return/Replace Order"
    //     0x15e4580: add             x0, PP, #0x34, lsl #12  ; [pp+0x34078] "Return/Replace Order"
    //     0x15e4584: ldr             x0, [x0, #0x78]
    // 0x15e4588: stur            x2, [fp, #-0x20]
    // 0x15e458c: StoreField: r2->field_b = r0
    //     0x15e458c: stur            w0, [x2, #0xb]
    // 0x15e4590: ldur            x0, [fp, #-8]
    // 0x15e4594: StoreField: r2->field_13 = r0
    //     0x15e4594: stur            w0, [x2, #0x13]
    // 0x15e4598: ldur            x1, [fp, #-0x10]
    // 0x15e459c: r0 = of()
    //     0x15e459c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e45a0: LoadField: r1 = r0->field_5b
    //     0x15e45a0: ldur            w1, [x0, #0x5b]
    // 0x15e45a4: DecompressPointer r1
    //     0x15e45a4: add             x1, x1, HEAP, lsl #32
    // 0x15e45a8: stur            x1, [fp, #-8]
    // 0x15e45ac: r0 = ColorFilter()
    //     0x15e45ac: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e45b0: mov             x1, x0
    // 0x15e45b4: ldur            x0, [fp, #-8]
    // 0x15e45b8: stur            x1, [fp, #-0x10]
    // 0x15e45bc: StoreField: r1->field_7 = r0
    //     0x15e45bc: stur            w0, [x1, #7]
    // 0x15e45c0: r0 = Instance_BlendMode
    //     0x15e45c0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e45c4: ldr             x0, [x0, #0xb30]
    // 0x15e45c8: StoreField: r1->field_b = r0
    //     0x15e45c8: stur            w0, [x1, #0xb]
    // 0x15e45cc: r0 = 1
    //     0x15e45cc: movz            x0, #0x1
    // 0x15e45d0: StoreField: r1->field_13 = r0
    //     0x15e45d0: stur            x0, [x1, #0x13]
    // 0x15e45d4: r0 = SvgPicture()
    //     0x15e45d4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e45d8: stur            x0, [fp, #-8]
    // 0x15e45dc: ldur            x16, [fp, #-0x10]
    // 0x15e45e0: str             x16, [SP]
    // 0x15e45e4: mov             x1, x0
    // 0x15e45e8: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e45e8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e45ec: ldr             x2, [x2, #0xa40]
    // 0x15e45f0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e45f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e45f4: ldr             x4, [x4, #0xa38]
    // 0x15e45f8: r0 = SvgPicture.asset()
    //     0x15e45f8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e45fc: r0 = Align()
    //     0x15e45fc: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e4600: mov             x1, x0
    // 0x15e4604: r0 = Instance_Alignment
    //     0x15e4604: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e4608: ldr             x0, [x0, #0xb10]
    // 0x15e460c: stur            x1, [fp, #-0x10]
    // 0x15e4610: StoreField: r1->field_f = r0
    //     0x15e4610: stur            w0, [x1, #0xf]
    // 0x15e4614: r0 = 1.000000
    //     0x15e4614: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e4618: StoreField: r1->field_13 = r0
    //     0x15e4618: stur            w0, [x1, #0x13]
    // 0x15e461c: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e461c: stur            w0, [x1, #0x17]
    // 0x15e4620: ldur            x0, [fp, #-8]
    // 0x15e4624: StoreField: r1->field_b = r0
    //     0x15e4624: stur            w0, [x1, #0xb]
    // 0x15e4628: r0 = InkWell()
    //     0x15e4628: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e462c: mov             x3, x0
    // 0x15e4630: ldur            x0, [fp, #-0x10]
    // 0x15e4634: stur            x3, [fp, #-8]
    // 0x15e4638: StoreField: r3->field_b = r0
    //     0x15e4638: stur            w0, [x3, #0xb]
    // 0x15e463c: ldur            x2, [fp, #-0x18]
    // 0x15e4640: r1 = Function '<anonymous closure>':.
    //     0x15e4640: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f490] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15e4644: ldr             x1, [x1, #0x490]
    // 0x15e4648: r0 = AllocateClosure()
    //     0x15e4648: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e464c: ldur            x2, [fp, #-8]
    // 0x15e4650: StoreField: r2->field_f = r0
    //     0x15e4650: stur            w0, [x2, #0xf]
    // 0x15e4654: r0 = true
    //     0x15e4654: add             x0, NULL, #0x20  ; true
    // 0x15e4658: StoreField: r2->field_43 = r0
    //     0x15e4658: stur            w0, [x2, #0x43]
    // 0x15e465c: r1 = Instance_BoxShape
    //     0x15e465c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e4660: ldr             x1, [x1, #0x80]
    // 0x15e4664: StoreField: r2->field_47 = r1
    //     0x15e4664: stur            w1, [x2, #0x47]
    // 0x15e4668: StoreField: r2->field_6f = r0
    //     0x15e4668: stur            w0, [x2, #0x6f]
    // 0x15e466c: r1 = false
    //     0x15e466c: add             x1, NULL, #0x30  ; false
    // 0x15e4670: StoreField: r2->field_73 = r1
    //     0x15e4670: stur            w1, [x2, #0x73]
    // 0x15e4674: StoreField: r2->field_83 = r0
    //     0x15e4674: stur            w0, [x2, #0x83]
    // 0x15e4678: StoreField: r2->field_7b = r1
    //     0x15e4678: stur            w1, [x2, #0x7b]
    // 0x15e467c: r0 = AppBar()
    //     0x15e467c: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e4680: stur            x0, [fp, #-0x10]
    // 0x15e4684: ldur            x16, [fp, #-0x20]
    // 0x15e4688: str             x16, [SP]
    // 0x15e468c: mov             x1, x0
    // 0x15e4690: ldur            x2, [fp, #-8]
    // 0x15e4694: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e4694: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e4698: ldr             x4, [x4, #0xf00]
    // 0x15e469c: r0 = AppBar()
    //     0x15e469c: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e46a0: ldur            x0, [fp, #-0x10]
    // 0x15e46a4: LeaveFrame
    //     0x15e46a4: mov             SP, fp
    //     0x15e46a8: ldp             fp, lr, [SP], #0x10
    // 0x15e46ac: ret
    //     0x15e46ac: ret             
    // 0x15e46b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e46b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e46b4: b               #0x15e4524
  }
}
