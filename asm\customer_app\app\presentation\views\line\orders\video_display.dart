// lib: , url: package:customer_app/app/presentation/views/line/orders/video_display.dart

// class id: 1049540, size: 0x8
class :: {
}

// class id: 3234, size: 0x18, field offset: 0x14
class VideoPlayerWidgetState extends State<dynamic> {

  late VideoPlayerController _controller; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x94a6b0, size: 0x14c
    // 0x94a6b0: EnterFrame
    //     0x94a6b0: stp             fp, lr, [SP, #-0x10]!
    //     0x94a6b4: mov             fp, SP
    // 0x94a6b8: AllocStack(0x40)
    //     0x94a6b8: sub             SP, SP, #0x40
    // 0x94a6bc: SetupParameters(VideoPlayerWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x94a6bc: stur            x1, [fp, #-8]
    // 0x94a6c0: CheckStackOverflow
    //     0x94a6c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94a6c4: cmp             SP, x16
    //     0x94a6c8: b.ls            #0x94a7ec
    // 0x94a6cc: r1 = 1
    //     0x94a6cc: movz            x1, #0x1
    // 0x94a6d0: r0 = AllocateContext()
    //     0x94a6d0: bl              #0x16f6108  ; AllocateContextStub
    // 0x94a6d4: mov             x1, x0
    // 0x94a6d8: ldur            x0, [fp, #-8]
    // 0x94a6dc: stur            x1, [fp, #-0x18]
    // 0x94a6e0: StoreField: r1->field_f = r0
    //     0x94a6e0: stur            w0, [x1, #0xf]
    // 0x94a6e4: LoadField: r2 = r0->field_b
    //     0x94a6e4: ldur            w2, [x0, #0xb]
    // 0x94a6e8: DecompressPointer r2
    //     0x94a6e8: add             x2, x2, HEAP, lsl #32
    // 0x94a6ec: cmp             w2, NULL
    // 0x94a6f0: b.eq            #0x94a7f4
    // 0x94a6f4: LoadField: r3 = r2->field_b
    //     0x94a6f4: ldur            w3, [x2, #0xb]
    // 0x94a6f8: DecompressPointer r3
    //     0x94a6f8: add             x3, x3, HEAP, lsl #32
    // 0x94a6fc: stur            x3, [fp, #-0x10]
    // 0x94a700: r0 = VideoPlayerOptions()
    //     0x94a700: bl              #0x93437c  ; AllocateVideoPlayerOptionsStub -> VideoPlayerOptions (size=0x10)
    // 0x94a704: mov             x2, x0
    // 0x94a708: r0 = false
    //     0x94a708: add             x0, NULL, #0x30  ; false
    // 0x94a70c: stur            x2, [fp, #-0x20]
    // 0x94a710: StoreField: r2->field_b = r0
    //     0x94a710: stur            w0, [x2, #0xb]
    // 0x94a714: StoreField: r2->field_7 = r0
    //     0x94a714: stur            w0, [x2, #7]
    // 0x94a718: r1 = <VideoPlayerValue>
    //     0x94a718: add             x1, PP, #0x52, lsl #12  ; [pp+0x52f90] TypeArguments: <VideoPlayerValue>
    //     0x94a71c: ldr             x1, [x1, #0xf90]
    // 0x94a720: r0 = VideoPlayerController()
    //     0x94a720: bl              #0x8f9c94  ; AllocateVideoPlayerControllerStub -> VideoPlayerController (size=0x68)
    // 0x94a724: stur            x0, [fp, #-0x28]
    // 0x94a728: ldur            x16, [fp, #-0x20]
    // 0x94a72c: str             x16, [SP]
    // 0x94a730: mov             x1, x0
    // 0x94a734: ldur            x2, [fp, #-0x10]
    // 0x94a738: r4 = const [0, 0x3, 0x1, 0x2, videoPlayerOptions, 0x2, null]
    //     0x94a738: add             x4, PP, #0x52, lsl #12  ; [pp+0x52f98] List(7) [0, 0x3, 0x1, 0x2, "videoPlayerOptions", 0x2, Null]
    //     0x94a73c: ldr             x4, [x4, #0xf98]
    // 0x94a740: r0 = VideoPlayerController.network()
    //     0x94a740: bl              #0x934250  ; [package:video_player/video_player.dart] VideoPlayerController::VideoPlayerController.network
    // 0x94a744: ldur            x1, [fp, #-0x28]
    // 0x94a748: r0 = initialize()
    //     0x94a748: bl              #0x8f78ec  ; [package:video_player/video_player.dart] VideoPlayerController::initialize
    // 0x94a74c: ldur            x2, [fp, #-0x18]
    // 0x94a750: r1 = Function '<anonymous closure>':.
    //     0x94a750: add             x1, PP, #0x52, lsl #12  ; [pp+0x52fa0] AnonymousClosure: (0x94a7fc), in [package:customer_app/app/presentation/views/line/orders/video_display.dart] VideoPlayerWidgetState::initState (0x94a6b0)
    //     0x94a754: ldr             x1, [x1, #0xfa0]
    // 0x94a758: stur            x0, [fp, #-0x10]
    // 0x94a75c: r0 = AllocateClosure()
    //     0x94a75c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x94a760: r16 = <Null?>
    //     0x94a760: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x94a764: ldur            lr, [fp, #-0x10]
    // 0x94a768: stp             lr, x16, [SP, #8]
    // 0x94a76c: str             x0, [SP]
    // 0x94a770: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x94a770: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x94a774: r0 = then()
    //     0x94a774: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x94a778: ldur            x0, [fp, #-0x28]
    // 0x94a77c: ldur            x3, [fp, #-8]
    // 0x94a780: StoreField: r3->field_13 = r0
    //     0x94a780: stur            w0, [x3, #0x13]
    //     0x94a784: ldurb           w16, [x3, #-1]
    //     0x94a788: ldurb           w17, [x0, #-1]
    //     0x94a78c: and             x16, x17, x16, lsr #2
    //     0x94a790: tst             x16, HEAP, lsr #32
    //     0x94a794: b.eq            #0x94a79c
    //     0x94a798: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x94a79c: LoadField: r0 = r3->field_b
    //     0x94a79c: ldur            w0, [x3, #0xb]
    // 0x94a7a0: DecompressPointer r0
    //     0x94a7a0: add             x0, x0, HEAP, lsl #32
    // 0x94a7a4: cmp             w0, NULL
    // 0x94a7a8: b.eq            #0x94a7f8
    // 0x94a7ac: LoadField: r1 = r0->field_f
    //     0x94a7ac: ldur            w1, [x0, #0xf]
    // 0x94a7b0: DecompressPointer r1
    //     0x94a7b0: add             x1, x1, HEAP, lsl #32
    // 0x94a7b4: cmp             w1, NULL
    // 0x94a7b8: b.eq            #0x94a7dc
    // 0x94a7bc: tbnz            w1, #4, #0x94a7dc
    // 0x94a7c0: ldur            x1, [fp, #-0x28]
    // 0x94a7c4: r2 = true
    //     0x94a7c4: add             x2, NULL, #0x20  ; true
    // 0x94a7c8: r0 = setLooping()
    //     0x94a7c8: bl              #0x8faa24  ; [package:video_player/video_player.dart] VideoPlayerController::setLooping
    // 0x94a7cc: ldur            x0, [fp, #-8]
    // 0x94a7d0: LoadField: r1 = r0->field_13
    //     0x94a7d0: ldur            w1, [x0, #0x13]
    // 0x94a7d4: DecompressPointer r1
    //     0x94a7d4: add             x1, x1, HEAP, lsl #32
    // 0x94a7d8: r0 = play()
    //     0x94a7d8: bl              #0x6ec380  ; [package:video_player/video_player.dart] VideoPlayerController::play
    // 0x94a7dc: r0 = Null
    //     0x94a7dc: mov             x0, NULL
    // 0x94a7e0: LeaveFrame
    //     0x94a7e0: mov             SP, fp
    //     0x94a7e4: ldp             fp, lr, [SP], #0x10
    // 0x94a7e8: ret
    //     0x94a7e8: ret             
    // 0x94a7ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94a7ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94a7f0: b               #0x94a6cc
    // 0x94a7f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94a7f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94a7f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94a7f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, void) {
    // ** addr: 0x94a7fc, size: 0x64
    // 0x94a7fc: EnterFrame
    //     0x94a7fc: stp             fp, lr, [SP, #-0x10]!
    //     0x94a800: mov             fp, SP
    // 0x94a804: AllocStack(0x8)
    //     0x94a804: sub             SP, SP, #8
    // 0x94a808: SetupParameters()
    //     0x94a808: ldr             x0, [fp, #0x18]
    //     0x94a80c: ldur            w1, [x0, #0x17]
    //     0x94a810: add             x1, x1, HEAP, lsl #32
    // 0x94a814: CheckStackOverflow
    //     0x94a814: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94a818: cmp             SP, x16
    //     0x94a81c: b.ls            #0x94a858
    // 0x94a820: LoadField: r0 = r1->field_f
    //     0x94a820: ldur            w0, [x1, #0xf]
    // 0x94a824: DecompressPointer r0
    //     0x94a824: add             x0, x0, HEAP, lsl #32
    // 0x94a828: stur            x0, [fp, #-8]
    // 0x94a82c: r1 = Function '<anonymous closure>':.
    //     0x94a82c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52fa8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x94a830: ldr             x1, [x1, #0xfa8]
    // 0x94a834: r2 = Null
    //     0x94a834: mov             x2, NULL
    // 0x94a838: r0 = AllocateClosure()
    //     0x94a838: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x94a83c: ldur            x1, [fp, #-8]
    // 0x94a840: mov             x2, x0
    // 0x94a844: r0 = setState()
    //     0x94a844: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x94a848: r0 = Null
    //     0x94a848: mov             x0, NULL
    // 0x94a84c: LeaveFrame
    //     0x94a84c: mov             SP, fp
    //     0x94a850: ldp             fp, lr, [SP], #0x10
    // 0x94a854: ret
    //     0x94a854: ret             
    // 0x94a858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94a858: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94a85c: b               #0x94a820
  }
  _ build(/* No info */) {
    // ** addr: 0xbfb6c0, size: 0x318
    // 0xbfb6c0: EnterFrame
    //     0xbfb6c0: stp             fp, lr, [SP, #-0x10]!
    //     0xbfb6c4: mov             fp, SP
    // 0xbfb6c8: AllocStack(0x38)
    //     0xbfb6c8: sub             SP, SP, #0x38
    // 0xbfb6cc: SetupParameters(VideoPlayerWidgetState this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r1 */)
    //     0xbfb6cc: mov             x0, x1
    //     0xbfb6d0: stur            x1, [fp, #-0x10]
    //     0xbfb6d4: mov             x1, x2
    // 0xbfb6d8: CheckStackOverflow
    //     0xbfb6d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfb6dc: cmp             SP, x16
    //     0xbfb6e0: b.ls            #0xbfb9c0
    // 0xbfb6e4: LoadField: r2 = r0->field_13
    //     0xbfb6e4: ldur            w2, [x0, #0x13]
    // 0xbfb6e8: DecompressPointer r2
    //     0xbfb6e8: add             x2, x2, HEAP, lsl #32
    // 0xbfb6ec: r16 = Sentinel
    //     0xbfb6ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbfb6f0: cmp             w2, w16
    // 0xbfb6f4: b.eq            #0xbfb9c8
    // 0xbfb6f8: stur            x2, [fp, #-8]
    // 0xbfb6fc: LoadField: r3 = r2->field_27
    //     0xbfb6fc: ldur            w3, [x2, #0x27]
    // 0xbfb700: DecompressPointer r3
    //     0xbfb700: add             x3, x3, HEAP, lsl #32
    // 0xbfb704: LoadField: r4 = r3->field_4b
    //     0xbfb704: ldur            w4, [x3, #0x4b]
    // 0xbfb708: DecompressPointer r4
    //     0xbfb708: add             x4, x4, HEAP, lsl #32
    // 0xbfb70c: tbnz            w4, #4, #0xbfb900
    // 0xbfb710: r0 = VideoPlayer()
    //     0xbfb710: bl              #0xa6fccc  ; AllocateVideoPlayerStub -> VideoPlayer (size=0x10)
    // 0xbfb714: mov             x3, x0
    // 0xbfb718: ldur            x0, [fp, #-8]
    // 0xbfb71c: stur            x3, [fp, #-0x18]
    // 0xbfb720: StoreField: r3->field_b = r0
    //     0xbfb720: stur            w0, [x3, #0xb]
    // 0xbfb724: r1 = Null
    //     0xbfb724: mov             x1, NULL
    // 0xbfb728: r2 = 2
    //     0xbfb728: movz            x2, #0x2
    // 0xbfb72c: r0 = AllocateArray()
    //     0xbfb72c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfb730: mov             x2, x0
    // 0xbfb734: ldur            x0, [fp, #-0x18]
    // 0xbfb738: stur            x2, [fp, #-8]
    // 0xbfb73c: StoreField: r2->field_f = r0
    //     0xbfb73c: stur            w0, [x2, #0xf]
    // 0xbfb740: r1 = <Widget>
    //     0xbfb740: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfb744: r0 = AllocateGrowableArray()
    //     0xbfb744: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfb748: mov             x2, x0
    // 0xbfb74c: ldur            x0, [fp, #-8]
    // 0xbfb750: stur            x2, [fp, #-0x18]
    // 0xbfb754: StoreField: r2->field_f = r0
    //     0xbfb754: stur            w0, [x2, #0xf]
    // 0xbfb758: r0 = 2
    //     0xbfb758: movz            x0, #0x2
    // 0xbfb75c: StoreField: r2->field_b = r0
    //     0xbfb75c: stur            w0, [x2, #0xb]
    // 0xbfb760: ldur            x0, [fp, #-0x10]
    // 0xbfb764: LoadField: r1 = r0->field_b
    //     0xbfb764: ldur            w1, [x0, #0xb]
    // 0xbfb768: DecompressPointer r1
    //     0xbfb768: add             x1, x1, HEAP, lsl #32
    // 0xbfb76c: cmp             w1, NULL
    // 0xbfb770: b.eq            #0xbfb9d4
    // 0xbfb774: LoadField: r0 = r1->field_f
    //     0xbfb774: ldur            w0, [x1, #0xf]
    // 0xbfb778: DecompressPointer r0
    //     0xbfb778: add             x0, x0, HEAP, lsl #32
    // 0xbfb77c: cmp             w0, NULL
    // 0xbfb780: b.eq            #0xbfb788
    // 0xbfb784: tbz             w0, #4, #0xbfb884
    // 0xbfb788: r1 = Instance_Color
    //     0xbfb788: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfb78c: d0 = 0.300000
    //     0xbfb78c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xbfb790: ldr             d0, [x17, #0x658]
    // 0xbfb794: r0 = withOpacity()
    //     0xbfb794: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbfb798: stur            x0, [fp, #-8]
    // 0xbfb79c: r0 = BoxDecoration()
    //     0xbfb79c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbfb7a0: mov             x1, x0
    // 0xbfb7a4: ldur            x0, [fp, #-8]
    // 0xbfb7a8: stur            x1, [fp, #-0x10]
    // 0xbfb7ac: StoreField: r1->field_7 = r0
    //     0xbfb7ac: stur            w0, [x1, #7]
    // 0xbfb7b0: r0 = Instance_BoxShape
    //     0xbfb7b0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xbfb7b4: ldr             x0, [x0, #0x970]
    // 0xbfb7b8: StoreField: r1->field_23 = r0
    //     0xbfb7b8: stur            w0, [x1, #0x23]
    // 0xbfb7bc: r0 = Container()
    //     0xbfb7bc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbfb7c0: stur            x0, [fp, #-8]
    // 0xbfb7c4: ldur            x16, [fp, #-0x10]
    // 0xbfb7c8: r30 = Instance_EdgeInsets
    //     0xbfb7c8: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b30] Obj!EdgeInsets@d57711
    //     0xbfb7cc: ldr             lr, [lr, #0xb30]
    // 0xbfb7d0: stp             lr, x16, [SP, #8]
    // 0xbfb7d4: r16 = Instance_Icon
    //     0xbfb7d4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36b38] Obj!Icon@d66431
    //     0xbfb7d8: ldr             x16, [x16, #0xb38]
    // 0xbfb7dc: str             x16, [SP]
    // 0xbfb7e0: mov             x1, x0
    // 0xbfb7e4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xbfb7e4: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xbfb7e8: ldr             x4, [x4, #0xb40]
    // 0xbfb7ec: r0 = Container()
    //     0xbfb7ec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbfb7f0: r1 = <StackParentData>
    //     0xbfb7f0: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xbfb7f4: ldr             x1, [x1, #0x8e0]
    // 0xbfb7f8: r0 = Positioned()
    //     0xbfb7f8: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xbfb7fc: mov             x2, x0
    // 0xbfb800: ldur            x0, [fp, #-8]
    // 0xbfb804: stur            x2, [fp, #-0x10]
    // 0xbfb808: StoreField: r2->field_b = r0
    //     0xbfb808: stur            w0, [x2, #0xb]
    // 0xbfb80c: ldur            x0, [fp, #-0x18]
    // 0xbfb810: LoadField: r1 = r0->field_b
    //     0xbfb810: ldur            w1, [x0, #0xb]
    // 0xbfb814: LoadField: r3 = r0->field_f
    //     0xbfb814: ldur            w3, [x0, #0xf]
    // 0xbfb818: DecompressPointer r3
    //     0xbfb818: add             x3, x3, HEAP, lsl #32
    // 0xbfb81c: LoadField: r4 = r3->field_b
    //     0xbfb81c: ldur            w4, [x3, #0xb]
    // 0xbfb820: r3 = LoadInt32Instr(r1)
    //     0xbfb820: sbfx            x3, x1, #1, #0x1f
    // 0xbfb824: stur            x3, [fp, #-0x20]
    // 0xbfb828: r1 = LoadInt32Instr(r4)
    //     0xbfb828: sbfx            x1, x4, #1, #0x1f
    // 0xbfb82c: cmp             x3, x1
    // 0xbfb830: b.ne            #0xbfb83c
    // 0xbfb834: mov             x1, x0
    // 0xbfb838: r0 = _growToNextCapacity()
    //     0xbfb838: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbfb83c: ldur            x2, [fp, #-0x18]
    // 0xbfb840: ldur            x3, [fp, #-0x20]
    // 0xbfb844: add             x0, x3, #1
    // 0xbfb848: lsl             x1, x0, #1
    // 0xbfb84c: StoreField: r2->field_b = r1
    //     0xbfb84c: stur            w1, [x2, #0xb]
    // 0xbfb850: LoadField: r1 = r2->field_f
    //     0xbfb850: ldur            w1, [x2, #0xf]
    // 0xbfb854: DecompressPointer r1
    //     0xbfb854: add             x1, x1, HEAP, lsl #32
    // 0xbfb858: ldur            x0, [fp, #-0x10]
    // 0xbfb85c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbfb85c: add             x25, x1, x3, lsl #2
    //     0xbfb860: add             x25, x25, #0xf
    //     0xbfb864: str             w0, [x25]
    //     0xbfb868: tbz             w0, #0, #0xbfb884
    //     0xbfb86c: ldurb           w16, [x1, #-1]
    //     0xbfb870: ldurb           w17, [x0, #-1]
    //     0xbfb874: and             x16, x17, x16, lsr #2
    //     0xbfb878: tst             x16, HEAP, lsr #32
    //     0xbfb87c: b.eq            #0xbfb884
    //     0xbfb880: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbfb884: r0 = Stack()
    //     0xbfb884: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbfb888: mov             x1, x0
    // 0xbfb88c: r0 = Instance_Alignment
    //     0xbfb88c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbfb890: ldr             x0, [x0, #0xb10]
    // 0xbfb894: stur            x1, [fp, #-8]
    // 0xbfb898: StoreField: r1->field_f = r0
    //     0xbfb898: stur            w0, [x1, #0xf]
    // 0xbfb89c: r0 = Instance_StackFit
    //     0xbfb89c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbfb8a0: ldr             x0, [x0, #0xfa8]
    // 0xbfb8a4: ArrayStore: r1[0] = r0  ; List_4
    //     0xbfb8a4: stur            w0, [x1, #0x17]
    // 0xbfb8a8: r0 = Instance_Clip
    //     0xbfb8a8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbfb8ac: ldr             x0, [x0, #0x7e0]
    // 0xbfb8b0: StoreField: r1->field_1b = r0
    //     0xbfb8b0: stur            w0, [x1, #0x1b]
    // 0xbfb8b4: ldur            x0, [fp, #-0x18]
    // 0xbfb8b8: StoreField: r1->field_b = r0
    //     0xbfb8b8: stur            w0, [x1, #0xb]
    // 0xbfb8bc: r0 = ClipRRect()
    //     0xbfb8bc: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xbfb8c0: mov             x1, x0
    // 0xbfb8c4: r0 = Instance_BorderRadius
    //     0xbfb8c4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbfb8c8: ldr             x0, [x0, #0xf70]
    // 0xbfb8cc: stur            x1, [fp, #-0x10]
    // 0xbfb8d0: StoreField: r1->field_f = r0
    //     0xbfb8d0: stur            w0, [x1, #0xf]
    // 0xbfb8d4: r0 = Instance_Clip
    //     0xbfb8d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xbfb8d8: ldr             x0, [x0, #0x138]
    // 0xbfb8dc: ArrayStore: r1[0] = r0  ; List_4
    //     0xbfb8dc: stur            w0, [x1, #0x17]
    // 0xbfb8e0: ldur            x0, [fp, #-8]
    // 0xbfb8e4: StoreField: r1->field_b = r0
    //     0xbfb8e4: stur            w0, [x1, #0xb]
    // 0xbfb8e8: r0 = AspectRatio()
    //     0xbfb8e8: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xbfb8ec: d0 = 1.000000
    //     0xbfb8ec: fmov            d0, #1.00000000
    // 0xbfb8f0: StoreField: r0->field_f = d0
    //     0xbfb8f0: stur            d0, [x0, #0xf]
    // 0xbfb8f4: ldur            x1, [fp, #-0x10]
    // 0xbfb8f8: StoreField: r0->field_b = r1
    //     0xbfb8f8: stur            w1, [x0, #0xb]
    // 0xbfb8fc: b               #0xbfb9b4
    // 0xbfb900: r0 = Instance_Alignment
    //     0xbfb900: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbfb904: ldr             x0, [x0, #0xb10]
    // 0xbfb908: r0 = of()
    //     0xbfb908: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfb90c: LoadField: r1 = r0->field_5b
    //     0xbfb90c: ldur            w1, [x0, #0x5b]
    // 0xbfb910: DecompressPointer r1
    //     0xbfb910: add             x1, x1, HEAP, lsl #32
    // 0xbfb914: r0 = LoadClassIdInstr(r1)
    //     0xbfb914: ldur            x0, [x1, #-1]
    //     0xbfb918: ubfx            x0, x0, #0xc, #0x14
    // 0xbfb91c: d0 = 0.300000
    //     0xbfb91c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xbfb920: ldr             d0, [x17, #0x658]
    // 0xbfb924: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbfb924: sub             lr, x0, #0xffa
    //     0xbfb928: ldr             lr, [x21, lr, lsl #3]
    //     0xbfb92c: blr             lr
    // 0xbfb930: stur            x0, [fp, #-8]
    // 0xbfb934: r0 = CircularProgressIndicator()
    //     0xbfb934: bl              #0x8596fc  ; AllocateCircularProgressIndicatorStub -> CircularProgressIndicator (size=0x44)
    // 0xbfb938: mov             x1, x0
    // 0xbfb93c: r0 = Instance__ActivityIndicatorType
    //     0xbfb93c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1b0] Obj!_ActivityIndicatorType@d741c1
    //     0xbfb940: ldr             x0, [x0, #0x1b0]
    // 0xbfb944: stur            x1, [fp, #-0x10]
    // 0xbfb948: StoreField: r1->field_23 = r0
    //     0xbfb948: stur            w0, [x1, #0x23]
    // 0xbfb94c: ldur            x0, [fp, #-8]
    // 0xbfb950: StoreField: r1->field_13 = r0
    //     0xbfb950: stur            w0, [x1, #0x13]
    // 0xbfb954: r0 = SizedBox()
    //     0xbfb954: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbfb958: mov             x1, x0
    // 0xbfb95c: r0 = 30.000000
    //     0xbfb95c: add             x0, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xbfb960: ldr             x0, [x0, #0x768]
    // 0xbfb964: stur            x1, [fp, #-8]
    // 0xbfb968: StoreField: r1->field_f = r0
    //     0xbfb968: stur            w0, [x1, #0xf]
    // 0xbfb96c: StoreField: r1->field_13 = r0
    //     0xbfb96c: stur            w0, [x1, #0x13]
    // 0xbfb970: ldur            x0, [fp, #-0x10]
    // 0xbfb974: StoreField: r1->field_b = r0
    //     0xbfb974: stur            w0, [x1, #0xb]
    // 0xbfb978: r0 = Center()
    //     0xbfb978: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbfb97c: mov             x1, x0
    // 0xbfb980: r0 = Instance_Alignment
    //     0xbfb980: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbfb984: ldr             x0, [x0, #0xb10]
    // 0xbfb988: stur            x1, [fp, #-0x10]
    // 0xbfb98c: StoreField: r1->field_f = r0
    //     0xbfb98c: stur            w0, [x1, #0xf]
    // 0xbfb990: ldur            x0, [fp, #-8]
    // 0xbfb994: StoreField: r1->field_b = r0
    //     0xbfb994: stur            w0, [x1, #0xb]
    // 0xbfb998: r0 = SizedBox()
    //     0xbfb998: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbfb99c: r1 = 57.000000
    //     0xbfb99c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b18] 57
    //     0xbfb9a0: ldr             x1, [x1, #0xb18]
    // 0xbfb9a4: StoreField: r0->field_f = r1
    //     0xbfb9a4: stur            w1, [x0, #0xf]
    // 0xbfb9a8: StoreField: r0->field_13 = r1
    //     0xbfb9a8: stur            w1, [x0, #0x13]
    // 0xbfb9ac: ldur            x1, [fp, #-0x10]
    // 0xbfb9b0: StoreField: r0->field_b = r1
    //     0xbfb9b0: stur            w1, [x0, #0xb]
    // 0xbfb9b4: LeaveFrame
    //     0xbfb9b4: mov             SP, fp
    //     0xbfb9b8: ldp             fp, lr, [SP], #0x10
    // 0xbfb9bc: ret
    //     0xbfb9bc: ret             
    // 0xbfb9c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfb9c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfb9c4: b               #0xbfb6e4
    // 0xbfb9c8: r9 = _controller
    //     0xbfb9c8: add             x9, PP, #0x52, lsl #12  ; [pp+0x52ef8] Field <VideoPlayerWidgetState._controller@1720346611>: late (offset: 0x14)
    //     0xbfb9cc: ldr             x9, [x9, #0xef8]
    // 0xbfb9d0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbfb9d0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbfb9d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfb9d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc883e0, size: 0x54
    // 0xc883e0: EnterFrame
    //     0xc883e0: stp             fp, lr, [SP, #-0x10]!
    //     0xc883e4: mov             fp, SP
    // 0xc883e8: CheckStackOverflow
    //     0xc883e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc883ec: cmp             SP, x16
    //     0xc883f0: b.ls            #0xc88420
    // 0xc883f4: LoadField: r0 = r1->field_13
    //     0xc883f4: ldur            w0, [x1, #0x13]
    // 0xc883f8: DecompressPointer r0
    //     0xc883f8: add             x0, x0, HEAP, lsl #32
    // 0xc883fc: r16 = Sentinel
    //     0xc883fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc88400: cmp             w0, w16
    // 0xc88404: b.eq            #0xc88428
    // 0xc88408: mov             x1, x0
    // 0xc8840c: r0 = dispose()
    //     0xc8840c: bl              #0xc75c5c  ; [package:video_player/video_player.dart] VideoPlayerController::dispose
    // 0xc88410: r0 = Null
    //     0xc88410: mov             x0, NULL
    // 0xc88414: LeaveFrame
    //     0xc88414: mov             SP, fp
    //     0xc88418: ldp             fp, lr, [SP], #0x10
    // 0xc8841c: ret
    //     0xc8841c: ret             
    // 0xc88420: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc88420: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc88424: b               #0xc883f4
    // 0xc88428: r9 = _controller
    //     0xc88428: add             x9, PP, #0x52, lsl #12  ; [pp+0x52ef8] Field <VideoPlayerWidgetState._controller@1720346611>: late (offset: 0x14)
    //     0xc8842c: ldr             x9, [x9, #0xef8]
    // 0xc88430: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc88430: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 3981, size: 0x14, field offset: 0xc
class VideoPlayerWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80de0, size: 0x2c
    // 0xc80de0: EnterFrame
    //     0xc80de0: stp             fp, lr, [SP, #-0x10]!
    //     0xc80de4: mov             fp, SP
    // 0xc80de8: mov             x0, x1
    // 0xc80dec: r1 = <VideoPlayerWidget>
    //     0xc80dec: add             x1, PP, #0x48, lsl #12  ; [pp+0x483e0] TypeArguments: <VideoPlayerWidget>
    //     0xc80df0: ldr             x1, [x1, #0x3e0]
    // 0xc80df4: r0 = VideoPlayerWidgetState()
    //     0xc80df4: bl              #0xc80e0c  ; AllocateVideoPlayerWidgetStateStub -> VideoPlayerWidgetState (size=0x18)
    // 0xc80df8: r1 = Sentinel
    //     0xc80df8: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc80dfc: StoreField: r0->field_13 = r1
    //     0xc80dfc: stur            w1, [x0, #0x13]
    // 0xc80e00: LeaveFrame
    //     0xc80e00: mov             SP, fp
    //     0xc80e04: ldp             fp, lr, [SP], #0x10
    // 0xc80e08: ret
    //     0xc80e08: ret             
  }
}
