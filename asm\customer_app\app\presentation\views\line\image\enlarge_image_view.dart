// lib: , url: package:customer_app/app/presentation/views/line/image/enlarge_image_view.dart

// class id: 1049531, size: 0x8
class :: {
}

// class id: 4532, size: 0x14, field offset: 0x14
//   const constructor, 
class EnlargeImageView extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x1505bf4, size: 0x58
    // 0x1505bf4: EnterFrame
    //     0x1505bf4: stp             fp, lr, [SP, #-0x10]!
    //     0x1505bf8: mov             fp, SP
    // 0x1505bfc: AllocStack(0x10)
    //     0x1505bfc: sub             SP, SP, #0x10
    // 0x1505c00: SetupParameters(EnlargeImageView this /* r1 => r1, fp-0x8 */)
    //     0x1505c00: stur            x1, [fp, #-8]
    // 0x1505c04: r1 = 1
    //     0x1505c04: movz            x1, #0x1
    // 0x1505c08: r0 = AllocateContext()
    //     0x1505c08: bl              #0x16f6108  ; AllocateContextStub
    // 0x1505c0c: mov             x1, x0
    // 0x1505c10: ldur            x0, [fp, #-8]
    // 0x1505c14: stur            x1, [fp, #-0x10]
    // 0x1505c18: StoreField: r1->field_f = r0
    //     0x1505c18: stur            w0, [x1, #0xf]
    // 0x1505c1c: r0 = Obx()
    //     0x1505c1c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1505c20: ldur            x2, [fp, #-0x10]
    // 0x1505c24: r1 = Function '<anonymous closure>':.
    //     0x1505c24: add             x1, PP, #0x37, lsl #12  ; [pp+0x372a8] AnonymousClosure: (0x1505c4c), in [package:customer_app/app/presentation/views/line/image/enlarge_image_view.dart] EnlargeImageView::body (0x1505bf4)
    //     0x1505c28: ldr             x1, [x1, #0x2a8]
    // 0x1505c2c: stur            x0, [fp, #-8]
    // 0x1505c30: r0 = AllocateClosure()
    //     0x1505c30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1505c34: mov             x1, x0
    // 0x1505c38: ldur            x0, [fp, #-8]
    // 0x1505c3c: StoreField: r0->field_b = r1
    //     0x1505c3c: stur            w1, [x0, #0xb]
    // 0x1505c40: LeaveFrame
    //     0x1505c40: mov             SP, fp
    //     0x1505c44: ldp             fp, lr, [SP], #0x10
    // 0x1505c48: ret
    //     0x1505c48: ret             
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0x1505c4c, size: 0x34c
    // 0x1505c4c: EnterFrame
    //     0x1505c4c: stp             fp, lr, [SP, #-0x10]!
    //     0x1505c50: mov             fp, SP
    // 0x1505c54: AllocStack(0x48)
    //     0x1505c54: sub             SP, SP, #0x48
    // 0x1505c58: SetupParameters()
    //     0x1505c58: ldr             x0, [fp, #0x10]
    //     0x1505c5c: ldur            w3, [x0, #0x17]
    //     0x1505c60: add             x3, x3, HEAP, lsl #32
    //     0x1505c64: stur            x3, [fp, #-8]
    // 0x1505c68: CheckStackOverflow
    //     0x1505c68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1505c6c: cmp             SP, x16
    //     0x1505c70: b.ls            #0x1505f90
    // 0x1505c74: r16 = 0.500000
    //     0x1505c74: ldr             x16, [PP, #0x47c0]  ; [pp+0x47c0] 0.5
    // 0x1505c78: str             x16, [SP]
    // 0x1505c7c: r1 = Null
    //     0x1505c7c: mov             x1, NULL
    // 0x1505c80: r2 = Instance_Color
    //     0x1505c80: ldr             x2, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1505c84: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x1505c84: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x1505c88: ldr             x4, [x4, #0x108]
    // 0x1505c8c: r0 = Border.all()
    //     0x1505c8c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x1505c90: stur            x0, [fp, #-0x10]
    // 0x1505c94: r0 = BoxDecoration()
    //     0x1505c94: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1505c98: mov             x1, x0
    // 0x1505c9c: r0 = Instance_Color
    //     0x1505c9c: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1505ca0: stur            x1, [fp, #-0x18]
    // 0x1505ca4: StoreField: r1->field_7 = r0
    //     0x1505ca4: stur            w0, [x1, #7]
    // 0x1505ca8: ldur            x0, [fp, #-0x10]
    // 0x1505cac: StoreField: r1->field_f = r0
    //     0x1505cac: stur            w0, [x1, #0xf]
    // 0x1505cb0: r0 = Instance_BoxShape
    //     0x1505cb0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x1505cb4: ldr             x0, [x0, #0x970]
    // 0x1505cb8: StoreField: r1->field_23 = r0
    //     0x1505cb8: stur            w0, [x1, #0x23]
    // 0x1505cbc: r0 = Container()
    //     0x1505cbc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1505cc0: stur            x0, [fp, #-0x10]
    // 0x1505cc4: r16 = 35.000000
    //     0x1505cc4: add             x16, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0x1505cc8: ldr             x16, [x16, #0x2b0]
    // 0x1505ccc: r30 = 35.000000
    //     0x1505ccc: add             lr, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0x1505cd0: ldr             lr, [lr, #0x2b0]
    // 0x1505cd4: stp             lr, x16, [SP, #0x10]
    // 0x1505cd8: ldur            x16, [fp, #-0x18]
    // 0x1505cdc: r30 = Instance_Icon
    //     0x1505cdc: add             lr, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0x1505ce0: ldr             lr, [lr, #0x2b8]
    // 0x1505ce4: stp             lr, x16, [SP]
    // 0x1505ce8: mov             x1, x0
    // 0x1505cec: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x1505cec: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x1505cf0: ldr             x4, [x4, #0x870]
    // 0x1505cf4: r0 = Container()
    //     0x1505cf4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1505cf8: r0 = InkWell()
    //     0x1505cf8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1505cfc: mov             x3, x0
    // 0x1505d00: ldur            x0, [fp, #-0x10]
    // 0x1505d04: stur            x3, [fp, #-0x18]
    // 0x1505d08: StoreField: r3->field_b = r0
    //     0x1505d08: stur            w0, [x3, #0xb]
    // 0x1505d0c: r1 = Function '<anonymous closure>':.
    //     0x1505d0c: add             x1, PP, #0x37, lsl #12  ; [pp+0x372c0] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x1505d10: ldr             x1, [x1, #0x2c0]
    // 0x1505d14: r2 = Null
    //     0x1505d14: mov             x2, NULL
    // 0x1505d18: r0 = AllocateClosure()
    //     0x1505d18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1505d1c: mov             x1, x0
    // 0x1505d20: ldur            x0, [fp, #-0x18]
    // 0x1505d24: StoreField: r0->field_f = r1
    //     0x1505d24: stur            w1, [x0, #0xf]
    // 0x1505d28: r1 = true
    //     0x1505d28: add             x1, NULL, #0x20  ; true
    // 0x1505d2c: StoreField: r0->field_43 = r1
    //     0x1505d2c: stur            w1, [x0, #0x43]
    // 0x1505d30: r2 = Instance_BoxShape
    //     0x1505d30: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1505d34: ldr             x2, [x2, #0x80]
    // 0x1505d38: StoreField: r0->field_47 = r2
    //     0x1505d38: stur            w2, [x0, #0x47]
    // 0x1505d3c: StoreField: r0->field_6f = r1
    //     0x1505d3c: stur            w1, [x0, #0x6f]
    // 0x1505d40: r2 = false
    //     0x1505d40: add             x2, NULL, #0x30  ; false
    // 0x1505d44: StoreField: r0->field_73 = r2
    //     0x1505d44: stur            w2, [x0, #0x73]
    // 0x1505d48: StoreField: r0->field_83 = r1
    //     0x1505d48: stur            w1, [x0, #0x83]
    // 0x1505d4c: StoreField: r0->field_7b = r2
    //     0x1505d4c: stur            w2, [x0, #0x7b]
    // 0x1505d50: r0 = Align()
    //     0x1505d50: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x1505d54: mov             x1, x0
    // 0x1505d58: r0 = Instance_Alignment
    //     0x1505d58: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0x1505d5c: ldr             x0, [x0, #0x950]
    // 0x1505d60: stur            x1, [fp, #-0x10]
    // 0x1505d64: StoreField: r1->field_f = r0
    //     0x1505d64: stur            w0, [x1, #0xf]
    // 0x1505d68: ldur            x0, [fp, #-0x18]
    // 0x1505d6c: StoreField: r1->field_b = r0
    //     0x1505d6c: stur            w0, [x1, #0xb]
    // 0x1505d70: r0 = Padding()
    //     0x1505d70: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1505d74: mov             x2, x0
    // 0x1505d78: r0 = Instance_EdgeInsets
    //     0x1505d78: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x1505d7c: ldr             x0, [x0, #0x980]
    // 0x1505d80: stur            x2, [fp, #-0x18]
    // 0x1505d84: StoreField: r2->field_f = r0
    //     0x1505d84: stur            w0, [x2, #0xf]
    // 0x1505d88: ldur            x0, [fp, #-0x10]
    // 0x1505d8c: StoreField: r2->field_b = r0
    //     0x1505d8c: stur            w0, [x2, #0xb]
    // 0x1505d90: ldur            x0, [fp, #-8]
    // 0x1505d94: LoadField: r1 = r0->field_f
    //     0x1505d94: ldur            w1, [x0, #0xf]
    // 0x1505d98: DecompressPointer r1
    //     0x1505d98: add             x1, x1, HEAP, lsl #32
    // 0x1505d9c: r0 = controller()
    //     0x1505d9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1505da0: mov             x1, x0
    // 0x1505da4: r0 = imageList()
    //     0x1505da4: bl              #0x1401638  ; [package:customer_app/app/presentation/controllers/image/image_controller.dart] ImageController::imageList
    // 0x1505da8: LoadField: r3 = r0->field_b
    //     0x1505da8: ldur            w3, [x0, #0xb]
    // 0x1505dac: ldur            x2, [fp, #-8]
    // 0x1505db0: stur            x3, [fp, #-0x10]
    // 0x1505db4: r1 = Function '<anonymous closure>':.
    //     0x1505db4: add             x1, PP, #0x37, lsl #12  ; [pp+0x372c8] AnonymousClosure: (0x150615c), in [package:customer_app/app/presentation/views/line/image/enlarge_image_view.dart] EnlargeImageView::body (0x1505bf4)
    //     0x1505db8: ldr             x1, [x1, #0x2c8]
    // 0x1505dbc: r0 = AllocateClosure()
    //     0x1505dbc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1505dc0: stur            x0, [fp, #-0x20]
    // 0x1505dc4: r0 = ListView()
    //     0x1505dc4: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x1505dc8: stur            x0, [fp, #-0x28]
    // 0x1505dcc: r16 = true
    //     0x1505dcc: add             x16, NULL, #0x20  ; true
    // 0x1505dd0: r30 = Instance_Axis
    //     0x1505dd0: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1505dd4: stp             lr, x16, [SP]
    // 0x1505dd8: mov             x1, x0
    // 0x1505ddc: ldur            x2, [fp, #-0x20]
    // 0x1505de0: ldur            x3, [fp, #-0x10]
    // 0x1505de4: r4 = const [0, 0x5, 0x2, 0x3, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0x1505de4: add             x4, PP, #0x37, lsl #12  ; [pp+0x372d0] List(9) [0, 0x5, 0x2, 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0x1505de8: ldr             x4, [x4, #0x2d0]
    // 0x1505dec: r0 = ListView.builder()
    //     0x1505dec: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x1505df0: r0 = Center()
    //     0x1505df0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x1505df4: mov             x2, x0
    // 0x1505df8: r0 = Instance_Alignment
    //     0x1505df8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1505dfc: ldr             x0, [x0, #0xb10]
    // 0x1505e00: stur            x2, [fp, #-0x10]
    // 0x1505e04: StoreField: r2->field_f = r0
    //     0x1505e04: stur            w0, [x2, #0xf]
    // 0x1505e08: ldur            x0, [fp, #-0x28]
    // 0x1505e0c: StoreField: r2->field_b = r0
    //     0x1505e0c: stur            w0, [x2, #0xb]
    // 0x1505e10: r1 = <FlexParentData>
    //     0x1505e10: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1505e14: ldr             x1, [x1, #0xe00]
    // 0x1505e18: r0 = Expanded()
    //     0x1505e18: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1505e1c: mov             x2, x0
    // 0x1505e20: r0 = 5
    //     0x1505e20: movz            x0, #0x5
    // 0x1505e24: stur            x2, [fp, #-0x20]
    // 0x1505e28: StoreField: r2->field_13 = r0
    //     0x1505e28: stur            x0, [x2, #0x13]
    // 0x1505e2c: r0 = Instance_FlexFit
    //     0x1505e2c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1505e30: ldr             x0, [x0, #0xe08]
    // 0x1505e34: StoreField: r2->field_1b = r0
    //     0x1505e34: stur            w0, [x2, #0x1b]
    // 0x1505e38: ldur            x0, [fp, #-0x10]
    // 0x1505e3c: StoreField: r2->field_b = r0
    //     0x1505e3c: stur            w0, [x2, #0xb]
    // 0x1505e40: ldur            x0, [fp, #-8]
    // 0x1505e44: LoadField: r1 = r0->field_f
    //     0x1505e44: ldur            w1, [x0, #0xf]
    // 0x1505e48: DecompressPointer r1
    //     0x1505e48: add             x1, x1, HEAP, lsl #32
    // 0x1505e4c: r0 = controller()
    //     0x1505e4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1505e50: LoadField: r1 = r0->field_4b
    //     0x1505e50: ldur            w1, [x0, #0x4b]
    // 0x1505e54: DecompressPointer r1
    //     0x1505e54: add             x1, x1, HEAP, lsl #32
    // 0x1505e58: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1505e58: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1505e5c: r0 = toList()
    //     0x1505e5c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x1505e60: LoadField: r3 = r0->field_b
    //     0x1505e60: ldur            w3, [x0, #0xb]
    // 0x1505e64: ldur            x2, [fp, #-8]
    // 0x1505e68: stur            x3, [fp, #-0x10]
    // 0x1505e6c: r1 = Function '<anonymous closure>':.
    //     0x1505e6c: add             x1, PP, #0x37, lsl #12  ; [pp+0x372d8] AnonymousClosure: (0x1505f98), in [package:customer_app/app/presentation/views/line/image/enlarge_image_view.dart] EnlargeImageView::body (0x1505bf4)
    //     0x1505e70: ldr             x1, [x1, #0x2d8]
    // 0x1505e74: r0 = AllocateClosure()
    //     0x1505e74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1505e78: stur            x0, [fp, #-8]
    // 0x1505e7c: r0 = ListView()
    //     0x1505e7c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x1505e80: stur            x0, [fp, #-0x28]
    // 0x1505e84: r16 = true
    //     0x1505e84: add             x16, NULL, #0x20  ; true
    // 0x1505e88: r30 = Instance_Axis
    //     0x1505e88: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1505e8c: stp             lr, x16, [SP]
    // 0x1505e90: mov             x1, x0
    // 0x1505e94: ldur            x2, [fp, #-8]
    // 0x1505e98: ldur            x3, [fp, #-0x10]
    // 0x1505e9c: r4 = const [0, 0x5, 0x2, 0x3, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0x1505e9c: add             x4, PP, #0x37, lsl #12  ; [pp+0x372d0] List(9) [0, 0x5, 0x2, 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0x1505ea0: ldr             x4, [x4, #0x2d0]
    // 0x1505ea4: r0 = ListView.builder()
    //     0x1505ea4: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x1505ea8: r1 = <FlexParentData>
    //     0x1505ea8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1505eac: ldr             x1, [x1, #0xe00]
    // 0x1505eb0: r0 = Flexible()
    //     0x1505eb0: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x1505eb4: mov             x3, x0
    // 0x1505eb8: r0 = 1
    //     0x1505eb8: movz            x0, #0x1
    // 0x1505ebc: stur            x3, [fp, #-8]
    // 0x1505ec0: StoreField: r3->field_13 = r0
    //     0x1505ec0: stur            x0, [x3, #0x13]
    // 0x1505ec4: r0 = Instance_FlexFit
    //     0x1505ec4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x1505ec8: ldr             x0, [x0, #0xe20]
    // 0x1505ecc: StoreField: r3->field_1b = r0
    //     0x1505ecc: stur            w0, [x3, #0x1b]
    // 0x1505ed0: ldur            x0, [fp, #-0x28]
    // 0x1505ed4: StoreField: r3->field_b = r0
    //     0x1505ed4: stur            w0, [x3, #0xb]
    // 0x1505ed8: r1 = Null
    //     0x1505ed8: mov             x1, NULL
    // 0x1505edc: r2 = 8
    //     0x1505edc: movz            x2, #0x8
    // 0x1505ee0: r0 = AllocateArray()
    //     0x1505ee0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1505ee4: mov             x2, x0
    // 0x1505ee8: ldur            x0, [fp, #-0x18]
    // 0x1505eec: stur            x2, [fp, #-0x10]
    // 0x1505ef0: StoreField: r2->field_f = r0
    //     0x1505ef0: stur            w0, [x2, #0xf]
    // 0x1505ef4: ldur            x0, [fp, #-0x20]
    // 0x1505ef8: StoreField: r2->field_13 = r0
    //     0x1505ef8: stur            w0, [x2, #0x13]
    // 0x1505efc: r16 = Instance_Divider
    //     0x1505efc: add             x16, PP, #0x37, lsl #12  ; [pp+0x372e0] Obj!Divider@d66be1
    //     0x1505f00: ldr             x16, [x16, #0x2e0]
    // 0x1505f04: ArrayStore: r2[0] = r16  ; List_4
    //     0x1505f04: stur            w16, [x2, #0x17]
    // 0x1505f08: ldur            x0, [fp, #-8]
    // 0x1505f0c: StoreField: r2->field_1b = r0
    //     0x1505f0c: stur            w0, [x2, #0x1b]
    // 0x1505f10: r1 = <Widget>
    //     0x1505f10: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1505f14: r0 = AllocateGrowableArray()
    //     0x1505f14: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1505f18: mov             x1, x0
    // 0x1505f1c: ldur            x0, [fp, #-0x10]
    // 0x1505f20: stur            x1, [fp, #-8]
    // 0x1505f24: StoreField: r1->field_f = r0
    //     0x1505f24: stur            w0, [x1, #0xf]
    // 0x1505f28: r0 = 8
    //     0x1505f28: movz            x0, #0x8
    // 0x1505f2c: StoreField: r1->field_b = r0
    //     0x1505f2c: stur            w0, [x1, #0xb]
    // 0x1505f30: r0 = Column()
    //     0x1505f30: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1505f34: r1 = Instance_Axis
    //     0x1505f34: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1505f38: StoreField: r0->field_f = r1
    //     0x1505f38: stur            w1, [x0, #0xf]
    // 0x1505f3c: r1 = Instance_MainAxisAlignment
    //     0x1505f3c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1505f40: ldr             x1, [x1, #0xa08]
    // 0x1505f44: StoreField: r0->field_13 = r1
    //     0x1505f44: stur            w1, [x0, #0x13]
    // 0x1505f48: r1 = Instance_MainAxisSize
    //     0x1505f48: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1505f4c: ldr             x1, [x1, #0xa10]
    // 0x1505f50: ArrayStore: r0[0] = r1  ; List_4
    //     0x1505f50: stur            w1, [x0, #0x17]
    // 0x1505f54: r1 = Instance_CrossAxisAlignment
    //     0x1505f54: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1505f58: ldr             x1, [x1, #0x890]
    // 0x1505f5c: StoreField: r0->field_1b = r1
    //     0x1505f5c: stur            w1, [x0, #0x1b]
    // 0x1505f60: r1 = Instance_VerticalDirection
    //     0x1505f60: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1505f64: ldr             x1, [x1, #0xa20]
    // 0x1505f68: StoreField: r0->field_23 = r1
    //     0x1505f68: stur            w1, [x0, #0x23]
    // 0x1505f6c: r1 = Instance_Clip
    //     0x1505f6c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1505f70: ldr             x1, [x1, #0x38]
    // 0x1505f74: StoreField: r0->field_2b = r1
    //     0x1505f74: stur            w1, [x0, #0x2b]
    // 0x1505f78: StoreField: r0->field_2f = rZR
    //     0x1505f78: stur            xzr, [x0, #0x2f]
    // 0x1505f7c: ldur            x1, [fp, #-8]
    // 0x1505f80: StoreField: r0->field_b = r1
    //     0x1505f80: stur            w1, [x0, #0xb]
    // 0x1505f84: LeaveFrame
    //     0x1505f84: mov             SP, fp
    //     0x1505f88: ldp             fp, lr, [SP], #0x10
    // 0x1505f8c: ret
    //     0x1505f8c: ret             
    // 0x1505f90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1505f90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1505f94: b               #0x1505c74
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x1505f98, size: 0x1c4
    // 0x1505f98: EnterFrame
    //     0x1505f98: stp             fp, lr, [SP, #-0x10]!
    //     0x1505f9c: mov             fp, SP
    // 0x1505fa0: AllocStack(0x50)
    //     0x1505fa0: sub             SP, SP, #0x50
    // 0x1505fa4: SetupParameters()
    //     0x1505fa4: ldr             x0, [fp, #0x20]
    //     0x1505fa8: ldur            w1, [x0, #0x17]
    //     0x1505fac: add             x1, x1, HEAP, lsl #32
    //     0x1505fb0: stur            x1, [fp, #-8]
    // 0x1505fb4: CheckStackOverflow
    //     0x1505fb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1505fb8: cmp             SP, x16
    //     0x1505fbc: b.ls            #0x150614c
    // 0x1505fc0: r0 = ImageHeaders.forImages()
    //     0x1505fc0: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x1505fc4: mov             x2, x0
    // 0x1505fc8: ldur            x0, [fp, #-8]
    // 0x1505fcc: stur            x2, [fp, #-0x10]
    // 0x1505fd0: LoadField: r1 = r0->field_f
    //     0x1505fd0: ldur            w1, [x0, #0xf]
    // 0x1505fd4: DecompressPointer r1
    //     0x1505fd4: add             x1, x1, HEAP, lsl #32
    // 0x1505fd8: r0 = controller()
    //     0x1505fd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1505fdc: LoadField: r1 = r0->field_4b
    //     0x1505fdc: ldur            w1, [x0, #0x4b]
    // 0x1505fe0: DecompressPointer r1
    //     0x1505fe0: add             x1, x1, HEAP, lsl #32
    // 0x1505fe4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1505fe4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1505fe8: r0 = toList()
    //     0x1505fe8: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x1505fec: mov             x2, x0
    // 0x1505ff0: LoadField: r0 = r2->field_b
    //     0x1505ff0: ldur            w0, [x2, #0xb]
    // 0x1505ff4: ldr             x1, [fp, #0x10]
    // 0x1505ff8: r3 = LoadInt32Instr(r1)
    //     0x1505ff8: sbfx            x3, x1, #1, #0x1f
    //     0x1505ffc: tbz             w1, #0, #0x1506004
    //     0x1506000: ldur            x3, [x1, #7]
    // 0x1506004: r1 = LoadInt32Instr(r0)
    //     0x1506004: sbfx            x1, x0, #1, #0x1f
    // 0x1506008: mov             x0, x1
    // 0x150600c: mov             x1, x3
    // 0x1506010: cmp             x1, x0
    // 0x1506014: b.hs            #0x1506154
    // 0x1506018: LoadField: r0 = r2->field_f
    //     0x1506018: ldur            w0, [x2, #0xf]
    // 0x150601c: DecompressPointer r0
    //     0x150601c: add             x0, x0, HEAP, lsl #32
    // 0x1506020: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x1506020: add             x16, x0, x3, lsl #2
    //     0x1506024: ldur            w1, [x16, #0xf]
    // 0x1506028: DecompressPointer r1
    //     0x1506028: add             x1, x1, HEAP, lsl #32
    // 0x150602c: cmp             w1, NULL
    // 0x1506030: b.eq            #0x1506158
    // 0x1506034: LoadField: r0 = r1->field_2b
    //     0x1506034: ldur            w0, [x1, #0x2b]
    // 0x1506038: DecompressPointer r0
    //     0x1506038: add             x0, x0, HEAP, lsl #32
    // 0x150603c: cmp             w0, NULL
    // 0x1506040: b.ne            #0x150604c
    // 0x1506044: r0 = Null
    //     0x1506044: mov             x0, NULL
    // 0x1506048: b               #0x1506058
    // 0x150604c: LoadField: r1 = r0->field_b
    //     0x150604c: ldur            w1, [x0, #0xb]
    // 0x1506050: DecompressPointer r1
    //     0x1506050: add             x1, x1, HEAP, lsl #32
    // 0x1506054: mov             x0, x1
    // 0x1506058: cmp             w0, NULL
    // 0x150605c: b.ne            #0x1506064
    // 0x1506060: r0 = ""
    //     0x1506060: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1506064: stur            x0, [fp, #-8]
    // 0x1506068: r1 = Function '<anonymous closure>':.
    //     0x1506068: add             x1, PP, #0x37, lsl #12  ; [pp+0x372e8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x150606c: ldr             x1, [x1, #0x2e8]
    // 0x1506070: r2 = Null
    //     0x1506070: mov             x2, NULL
    // 0x1506074: r0 = AllocateClosure()
    //     0x1506074: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1506078: r1 = Function '<anonymous closure>':.
    //     0x1506078: add             x1, PP, #0x37, lsl #12  ; [pp+0x372f0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x150607c: ldr             x1, [x1, #0x2f0]
    // 0x1506080: r2 = Null
    //     0x1506080: mov             x2, NULL
    // 0x1506084: stur            x0, [fp, #-0x18]
    // 0x1506088: r0 = AllocateClosure()
    //     0x1506088: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x150608c: stur            x0, [fp, #-0x20]
    // 0x1506090: r0 = CachedNetworkImage()
    //     0x1506090: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x1506094: stur            x0, [fp, #-0x28]
    // 0x1506098: r16 = 100.000000
    //     0x1506098: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0x150609c: r30 = 80.000000
    //     0x150609c: add             lr, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0x15060a0: ldr             lr, [lr, #0x2f8]
    // 0x15060a4: stp             lr, x16, [SP, #0x18]
    // 0x15060a8: ldur            x16, [fp, #-0x10]
    // 0x15060ac: ldur            lr, [fp, #-0x18]
    // 0x15060b0: stp             lr, x16, [SP, #8]
    // 0x15060b4: ldur            x16, [fp, #-0x20]
    // 0x15060b8: str             x16, [SP]
    // 0x15060bc: mov             x1, x0
    // 0x15060c0: ldur            x2, [fp, #-8]
    // 0x15060c4: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, height, 0x2, httpHeaders, 0x4, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x15060c4: add             x4, PP, #0x37, lsl #12  ; [pp+0x37300] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "height", 0x2, "httpHeaders", 0x4, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x15060c8: ldr             x4, [x4, #0x300]
    // 0x15060cc: r0 = CachedNetworkImage()
    //     0x15060cc: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15060d0: r0 = InkWell()
    //     0x15060d0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15060d4: mov             x3, x0
    // 0x15060d8: ldur            x0, [fp, #-0x28]
    // 0x15060dc: stur            x3, [fp, #-8]
    // 0x15060e0: StoreField: r3->field_b = r0
    //     0x15060e0: stur            w0, [x3, #0xb]
    // 0x15060e4: r1 = Function '<anonymous closure>':.
    //     0x15060e4: add             x1, PP, #0x37, lsl #12  ; [pp+0x37308] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x15060e8: ldr             x1, [x1, #0x308]
    // 0x15060ec: r2 = Null
    //     0x15060ec: mov             x2, NULL
    // 0x15060f0: r0 = AllocateClosure()
    //     0x15060f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15060f4: mov             x1, x0
    // 0x15060f8: ldur            x0, [fp, #-8]
    // 0x15060fc: StoreField: r0->field_f = r1
    //     0x15060fc: stur            w1, [x0, #0xf]
    // 0x1506100: r1 = true
    //     0x1506100: add             x1, NULL, #0x20  ; true
    // 0x1506104: StoreField: r0->field_43 = r1
    //     0x1506104: stur            w1, [x0, #0x43]
    // 0x1506108: r2 = Instance_BoxShape
    //     0x1506108: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x150610c: ldr             x2, [x2, #0x80]
    // 0x1506110: StoreField: r0->field_47 = r2
    //     0x1506110: stur            w2, [x0, #0x47]
    // 0x1506114: StoreField: r0->field_6f = r1
    //     0x1506114: stur            w1, [x0, #0x6f]
    // 0x1506118: r2 = false
    //     0x1506118: add             x2, NULL, #0x30  ; false
    // 0x150611c: StoreField: r0->field_73 = r2
    //     0x150611c: stur            w2, [x0, #0x73]
    // 0x1506120: StoreField: r0->field_83 = r1
    //     0x1506120: stur            w1, [x0, #0x83]
    // 0x1506124: StoreField: r0->field_7b = r2
    //     0x1506124: stur            w2, [x0, #0x7b]
    // 0x1506128: r0 = Padding()
    //     0x1506128: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x150612c: r1 = Instance_EdgeInsets
    //     0x150612c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x1506130: ldr             x1, [x1, #0x980]
    // 0x1506134: StoreField: r0->field_f = r1
    //     0x1506134: stur            w1, [x0, #0xf]
    // 0x1506138: ldur            x1, [fp, #-8]
    // 0x150613c: StoreField: r0->field_b = r1
    //     0x150613c: stur            w1, [x0, #0xb]
    // 0x1506140: LeaveFrame
    //     0x1506140: mov             SP, fp
    //     0x1506144: ldp             fp, lr, [SP], #0x10
    // 0x1506148: ret
    //     0x1506148: ret             
    // 0x150614c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x150614c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1506150: b               #0x1505fc0
    // 0x1506154: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1506154: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1506158: r0 = NullErrorSharedWithoutFPURegs()
    //     0x1506158: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] InteractiveViewer <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x150615c, size: 0x24c
    // 0x150615c: EnterFrame
    //     0x150615c: stp             fp, lr, [SP, #-0x10]!
    //     0x1506160: mov             fp, SP
    // 0x1506164: AllocStack(0x70)
    //     0x1506164: sub             SP, SP, #0x70
    // 0x1506168: SetupParameters()
    //     0x1506168: ldr             x0, [fp, #0x20]
    //     0x150616c: ldur            w1, [x0, #0x17]
    //     0x1506170: add             x1, x1, HEAP, lsl #32
    //     0x1506174: stur            x1, [fp, #-8]
    // 0x1506178: CheckStackOverflow
    //     0x1506178: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x150617c: cmp             SP, x16
    //     0x1506180: b.ls            #0x1506360
    // 0x1506184: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1506184: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1506188: ldr             x0, [x0, #0x1c80]
    //     0x150618c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1506190: cmp             w0, w16
    //     0x1506194: b.ne            #0x15061a0
    //     0x1506198: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x150619c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15061a0: r0 = GetNavigation.size()
    //     0x15061a0: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x15061a4: LoadField: d0 = r0->field_f
    //     0x15061a4: ldur            d0, [x0, #0xf]
    // 0x15061a8: stur            d0, [fp, #-0x40]
    // 0x15061ac: r0 = GetNavigation.size()
    //     0x15061ac: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x15061b0: LoadField: d0 = r0->field_7
    //     0x15061b0: ldur            d0, [x0, #7]
    // 0x15061b4: stur            d0, [fp, #-0x48]
    // 0x15061b8: r0 = ImageHeaders.forImages()
    //     0x15061b8: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x15061bc: mov             x2, x0
    // 0x15061c0: ldur            x0, [fp, #-8]
    // 0x15061c4: stur            x2, [fp, #-0x10]
    // 0x15061c8: LoadField: r1 = r0->field_f
    //     0x15061c8: ldur            w1, [x0, #0xf]
    // 0x15061cc: DecompressPointer r1
    //     0x15061cc: add             x1, x1, HEAP, lsl #32
    // 0x15061d0: r0 = controller()
    //     0x15061d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15061d4: LoadField: r1 = r0->field_4b
    //     0x15061d4: ldur            w1, [x0, #0x4b]
    // 0x15061d8: DecompressPointer r1
    //     0x15061d8: add             x1, x1, HEAP, lsl #32
    // 0x15061dc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15061dc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15061e0: r0 = toList()
    //     0x15061e0: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x15061e4: mov             x2, x0
    // 0x15061e8: LoadField: r0 = r2->field_b
    //     0x15061e8: ldur            w0, [x2, #0xb]
    // 0x15061ec: ldr             x1, [fp, #0x10]
    // 0x15061f0: r3 = LoadInt32Instr(r1)
    //     0x15061f0: sbfx            x3, x1, #1, #0x1f
    //     0x15061f4: tbz             w1, #0, #0x15061fc
    //     0x15061f8: ldur            x3, [x1, #7]
    // 0x15061fc: r1 = LoadInt32Instr(r0)
    //     0x15061fc: sbfx            x1, x0, #1, #0x1f
    // 0x1506200: mov             x0, x1
    // 0x1506204: mov             x1, x3
    // 0x1506208: cmp             x1, x0
    // 0x150620c: b.hs            #0x1506368
    // 0x1506210: LoadField: r0 = r2->field_f
    //     0x1506210: ldur            w0, [x2, #0xf]
    // 0x1506214: DecompressPointer r0
    //     0x1506214: add             x0, x0, HEAP, lsl #32
    // 0x1506218: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x1506218: add             x16, x0, x3, lsl #2
    //     0x150621c: ldur            w1, [x16, #0xf]
    // 0x1506220: DecompressPointer r1
    //     0x1506220: add             x1, x1, HEAP, lsl #32
    // 0x1506224: cmp             w1, NULL
    // 0x1506228: b.eq            #0x150636c
    // 0x150622c: LoadField: r0 = r1->field_2b
    //     0x150622c: ldur            w0, [x1, #0x2b]
    // 0x1506230: DecompressPointer r0
    //     0x1506230: add             x0, x0, HEAP, lsl #32
    // 0x1506234: cmp             w0, NULL
    // 0x1506238: b.ne            #0x1506244
    // 0x150623c: r0 = Null
    //     0x150623c: mov             x0, NULL
    // 0x1506240: b               #0x1506250
    // 0x1506244: LoadField: r1 = r0->field_b
    //     0x1506244: ldur            w1, [x0, #0xb]
    // 0x1506248: DecompressPointer r1
    //     0x1506248: add             x1, x1, HEAP, lsl #32
    // 0x150624c: mov             x0, x1
    // 0x1506250: cmp             w0, NULL
    // 0x1506254: b.ne            #0x150625c
    // 0x1506258: r0 = ""
    //     0x1506258: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x150625c: ldur            d1, [fp, #-0x40]
    // 0x1506260: ldur            d0, [fp, #-0x48]
    // 0x1506264: stur            x0, [fp, #-0x20]
    // 0x1506268: r3 = inline_Allocate_Double()
    //     0x1506268: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0x150626c: add             x3, x3, #0x10
    //     0x1506270: cmp             x1, x3
    //     0x1506274: b.ls            #0x1506370
    //     0x1506278: str             x3, [THR, #0x50]  ; THR::top
    //     0x150627c: sub             x3, x3, #0xf
    //     0x1506280: movz            x1, #0xe15c
    //     0x1506284: movk            x1, #0x3, lsl #16
    //     0x1506288: stur            x1, [x3, #-1]
    // 0x150628c: StoreField: r3->field_7 = d1
    //     0x150628c: stur            d1, [x3, #7]
    // 0x1506290: stur            x3, [fp, #-0x18]
    // 0x1506294: r4 = inline_Allocate_Double()
    //     0x1506294: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0x1506298: add             x4, x4, #0x10
    //     0x150629c: cmp             x1, x4
    //     0x15062a0: b.ls            #0x150638c
    //     0x15062a4: str             x4, [THR, #0x50]  ; THR::top
    //     0x15062a8: sub             x4, x4, #0xf
    //     0x15062ac: movz            x1, #0xe15c
    //     0x15062b0: movk            x1, #0x3, lsl #16
    //     0x15062b4: stur            x1, [x4, #-1]
    // 0x15062b8: StoreField: r4->field_7 = d0
    //     0x15062b8: stur            d0, [x4, #7]
    // 0x15062bc: stur            x4, [fp, #-8]
    // 0x15062c0: r1 = Function '<anonymous closure>':.
    //     0x15062c0: add             x1, PP, #0x37, lsl #12  ; [pp+0x37318] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x15062c4: ldr             x1, [x1, #0x318]
    // 0x15062c8: r2 = Null
    //     0x15062c8: mov             x2, NULL
    // 0x15062cc: r0 = AllocateClosure()
    //     0x15062cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15062d0: r1 = Function '<anonymous closure>':.
    //     0x15062d0: add             x1, PP, #0x37, lsl #12  ; [pp+0x37320] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x15062d4: ldr             x1, [x1, #0x320]
    // 0x15062d8: r2 = Null
    //     0x15062d8: mov             x2, NULL
    // 0x15062dc: stur            x0, [fp, #-0x28]
    // 0x15062e0: r0 = AllocateClosure()
    //     0x15062e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15062e4: stur            x0, [fp, #-0x30]
    // 0x15062e8: r0 = CachedNetworkImage()
    //     0x15062e8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15062ec: stur            x0, [fp, #-0x38]
    // 0x15062f0: ldur            x16, [fp, #-0x18]
    // 0x15062f4: ldur            lr, [fp, #-8]
    // 0x15062f8: stp             lr, x16, [SP, #0x18]
    // 0x15062fc: ldur            x16, [fp, #-0x10]
    // 0x1506300: ldur            lr, [fp, #-0x28]
    // 0x1506304: stp             lr, x16, [SP, #8]
    // 0x1506308: ldur            x16, [fp, #-0x30]
    // 0x150630c: str             x16, [SP]
    // 0x1506310: mov             x1, x0
    // 0x1506314: ldur            x2, [fp, #-0x20]
    // 0x1506318: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, height, 0x2, httpHeaders, 0x4, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x1506318: add             x4, PP, #0x37, lsl #12  ; [pp+0x37300] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "height", 0x2, "httpHeaders", 0x4, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x150631c: ldr             x4, [x4, #0x300]
    // 0x1506320: r0 = CachedNetworkImage()
    //     0x1506320: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x1506324: r0 = InteractiveViewer()
    //     0x1506324: bl              #0xc92d1c  ; AllocateInteractiveViewerStub -> InteractiveViewer (size=0x64)
    // 0x1506328: stur            x0, [fp, #-8]
    // 0x150632c: r16 = Instance_EdgeInsets
    //     0x150632c: ldr             x16, [PP, #0x6d10]  ; [pp+0x6d10] Obj!EdgeInsets@d56cf1
    // 0x1506330: r30 = 0.500000
    //     0x1506330: ldr             lr, [PP, #0x47c0]  ; [pp+0x47c0] 0.5
    // 0x1506334: stp             lr, x16, [SP]
    // 0x1506338: mov             x1, x0
    // 0x150633c: ldur            x2, [fp, #-0x38]
    // 0x1506340: d0 = 2.000000
    //     0x1506340: fmov            d0, #2.00000000
    // 0x1506344: r4 = const [0, 0x5, 0x2, 0x3, boundaryMargin, 0x3, minScale, 0x4, null]
    //     0x1506344: add             x4, PP, #0x37, lsl #12  ; [pp+0x37328] List(9) [0, 0x5, 0x2, 0x3, "boundaryMargin", 0x3, "minScale", 0x4, Null]
    //     0x1506348: ldr             x4, [x4, #0x328]
    // 0x150634c: r0 = InteractiveViewer()
    //     0x150634c: bl              #0xc92b1c  ; [package:flutter/src/widgets/interactive_viewer.dart] InteractiveViewer::InteractiveViewer
    // 0x1506350: ldur            x0, [fp, #-8]
    // 0x1506354: LeaveFrame
    //     0x1506354: mov             SP, fp
    //     0x1506358: ldp             fp, lr, [SP], #0x10
    // 0x150635c: ret
    //     0x150635c: ret             
    // 0x1506360: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1506360: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1506364: b               #0x1506184
    // 0x1506368: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1506368: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x150636c: r0 = NullErrorSharedWithoutFPURegs()
    //     0x150636c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0x1506370: stp             q0, q1, [SP, #-0x20]!
    // 0x1506374: SaveReg r0
    //     0x1506374: str             x0, [SP, #-8]!
    // 0x1506378: r0 = AllocateDouble()
    //     0x1506378: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x150637c: mov             x3, x0
    // 0x1506380: RestoreReg r0
    //     0x1506380: ldr             x0, [SP], #8
    // 0x1506384: ldp             q0, q1, [SP], #0x20
    // 0x1506388: b               #0x150628c
    // 0x150638c: SaveReg d0
    //     0x150638c: str             q0, [SP, #-0x10]!
    // 0x1506390: stp             x0, x3, [SP, #-0x10]!
    // 0x1506394: r0 = AllocateDouble()
    //     0x1506394: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1506398: mov             x4, x0
    // 0x150639c: ldp             x0, x3, [SP], #0x10
    // 0x15063a0: RestoreReg d0
    //     0x15063a0: ldr             q0, [SP], #0x10
    // 0x15063a4: b               #0x15062b8
  }
}
