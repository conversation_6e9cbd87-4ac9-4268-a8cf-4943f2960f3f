// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/offer_section_widget.dart

// class id: 1049496, size: 0x8
class :: {
}

// class id: 3267, size: 0x14, field offset: 0x14
class _OfferSectionWidgetState extends State<dynamic> {

  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x906d90, size: 0x70
    // 0x906d90: EnterFrame
    //     0x906d90: stp             fp, lr, [SP, #-0x10]!
    //     0x906d94: mov             fp, SP
    // 0x906d98: AllocStack(0x10)
    //     0x906d98: sub             SP, SP, #0x10
    // 0x906d9c: SetupParameters()
    //     0x906d9c: ldr             x0, [fp, #0x18]
    //     0x906da0: ldur            w1, [x0, #0x17]
    //     0x906da4: add             x1, x1, HEA<PERSON>, lsl #32
    // 0x906da8: CheckStackOverflow
    //     0x906da8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x906dac: cmp             SP, x16
    //     0x906db0: b.ls            #0x906df4
    // 0x906db4: LoadField: r0 = r1->field_f
    //     0x906db4: ldur            w0, [x1, #0xf]
    // 0x906db8: DecompressPointer r0
    //     0x906db8: add             x0, x0, HEAP, lsl #32
    // 0x906dbc: LoadField: r1 = r0->field_b
    //     0x906dbc: ldur            w1, [x0, #0xb]
    // 0x906dc0: DecompressPointer r1
    //     0x906dc0: add             x1, x1, HEAP, lsl #32
    // 0x906dc4: cmp             w1, NULL
    // 0x906dc8: b.eq            #0x906dfc
    // 0x906dcc: LoadField: r0 = r1->field_2b
    //     0x906dcc: ldur            w0, [x1, #0x2b]
    // 0x906dd0: DecompressPointer r0
    //     0x906dd0: add             x0, x0, HEAP, lsl #32
    // 0x906dd4: r16 = true
    //     0x906dd4: add             x16, NULL, #0x20  ; true
    // 0x906dd8: stp             x16, x0, [SP]
    // 0x906ddc: ClosureCall
    //     0x906ddc: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x906de0: ldur            x2, [x0, #0x1f]
    //     0x906de4: blr             x2
    // 0x906de8: LeaveFrame
    //     0x906de8: mov             SP, fp
    //     0x906dec: ldp             fp, lr, [SP], #0x10
    // 0x906df0: ret
    //     0x906df0: ret             
    // 0x906df4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x906df4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x906df8: b               #0x906db4
    // 0x906dfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x906dfc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x948564, size: 0x130
    // 0x948564: EnterFrame
    //     0x948564: stp             fp, lr, [SP, #-0x10]!
    //     0x948568: mov             fp, SP
    // 0x94856c: AllocStack(0x18)
    //     0x94856c: sub             SP, SP, #0x18
    // 0x948570: SetupParameters(_OfferSectionWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x948570: stur            x1, [fp, #-8]
    // 0x948574: CheckStackOverflow
    //     0x948574: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x948578: cmp             SP, x16
    //     0x94857c: b.ls            #0x948688
    // 0x948580: r1 = 1
    //     0x948580: movz            x1, #0x1
    // 0x948584: r0 = AllocateContext()
    //     0x948584: bl              #0x16f6108  ; AllocateContextStub
    // 0x948588: mov             x1, x0
    // 0x94858c: ldur            x0, [fp, #-8]
    // 0x948590: StoreField: r1->field_f = r0
    //     0x948590: stur            w0, [x1, #0xf]
    // 0x948594: r0 = LoadStaticField(0x878)
    //     0x948594: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x948598: ldr             x0, [x0, #0x10f0]
    // 0x94859c: cmp             w0, NULL
    // 0x9485a0: b.eq            #0x948690
    // 0x9485a4: LoadField: r3 = r0->field_53
    //     0x9485a4: ldur            w3, [x0, #0x53]
    // 0x9485a8: DecompressPointer r3
    //     0x9485a8: add             x3, x3, HEAP, lsl #32
    // 0x9485ac: stur            x3, [fp, #-0x10]
    // 0x9485b0: LoadField: r0 = r3->field_7
    //     0x9485b0: ldur            w0, [x3, #7]
    // 0x9485b4: DecompressPointer r0
    //     0x9485b4: add             x0, x0, HEAP, lsl #32
    // 0x9485b8: mov             x2, x1
    // 0x9485bc: stur            x0, [fp, #-8]
    // 0x9485c0: r1 = Function '<anonymous closure>':.
    //     0x9485c0: add             x1, PP, #0x54, lsl #12  ; [pp+0x54498] AnonymousClosure: (0x906d90), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::initState (0x948564)
    //     0x9485c4: ldr             x1, [x1, #0x498]
    // 0x9485c8: r0 = AllocateClosure()
    //     0x9485c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9485cc: ldur            x2, [fp, #-8]
    // 0x9485d0: mov             x3, x0
    // 0x9485d4: r1 = Null
    //     0x9485d4: mov             x1, NULL
    // 0x9485d8: stur            x3, [fp, #-8]
    // 0x9485dc: cmp             w2, NULL
    // 0x9485e0: b.eq            #0x948600
    // 0x9485e4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9485e4: ldur            w4, [x2, #0x17]
    // 0x9485e8: DecompressPointer r4
    //     0x9485e8: add             x4, x4, HEAP, lsl #32
    // 0x9485ec: r8 = X0
    //     0x9485ec: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x9485f0: LoadField: r9 = r4->field_7
    //     0x9485f0: ldur            x9, [x4, #7]
    // 0x9485f4: r3 = Null
    //     0x9485f4: add             x3, PP, #0x54, lsl #12  ; [pp+0x544a0] Null
    //     0x9485f8: ldr             x3, [x3, #0x4a0]
    // 0x9485fc: blr             x9
    // 0x948600: ldur            x0, [fp, #-0x10]
    // 0x948604: LoadField: r1 = r0->field_b
    //     0x948604: ldur            w1, [x0, #0xb]
    // 0x948608: LoadField: r2 = r0->field_f
    //     0x948608: ldur            w2, [x0, #0xf]
    // 0x94860c: DecompressPointer r2
    //     0x94860c: add             x2, x2, HEAP, lsl #32
    // 0x948610: LoadField: r3 = r2->field_b
    //     0x948610: ldur            w3, [x2, #0xb]
    // 0x948614: r2 = LoadInt32Instr(r1)
    //     0x948614: sbfx            x2, x1, #1, #0x1f
    // 0x948618: stur            x2, [fp, #-0x18]
    // 0x94861c: r1 = LoadInt32Instr(r3)
    //     0x94861c: sbfx            x1, x3, #1, #0x1f
    // 0x948620: cmp             x2, x1
    // 0x948624: b.ne            #0x948630
    // 0x948628: mov             x1, x0
    // 0x94862c: r0 = _growToNextCapacity()
    //     0x94862c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x948630: ldur            x2, [fp, #-0x10]
    // 0x948634: ldur            x3, [fp, #-0x18]
    // 0x948638: add             x4, x3, #1
    // 0x94863c: lsl             x5, x4, #1
    // 0x948640: StoreField: r2->field_b = r5
    //     0x948640: stur            w5, [x2, #0xb]
    // 0x948644: LoadField: r1 = r2->field_f
    //     0x948644: ldur            w1, [x2, #0xf]
    // 0x948648: DecompressPointer r1
    //     0x948648: add             x1, x1, HEAP, lsl #32
    // 0x94864c: ldur            x0, [fp, #-8]
    // 0x948650: ArrayStore: r1[r3] = r0  ; List_4
    //     0x948650: add             x25, x1, x3, lsl #2
    //     0x948654: add             x25, x25, #0xf
    //     0x948658: str             w0, [x25]
    //     0x94865c: tbz             w0, #0, #0x948678
    //     0x948660: ldurb           w16, [x1, #-1]
    //     0x948664: ldurb           w17, [x0, #-1]
    //     0x948668: and             x16, x17, x16, lsr #2
    //     0x94866c: tst             x16, HEAP, lsr #32
    //     0x948670: b.eq            #0x948678
    //     0x948674: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x948678: r0 = Null
    //     0x948678: mov             x0, NULL
    // 0x94867c: LeaveFrame
    //     0x94867c: mov             SP, fp
    //     0x948680: ldp             fp, lr, [SP], #0x10
    // 0x948684: ret
    //     0x948684: ret             
    // 0x948688: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x948688: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94868c: b               #0x948580
    // 0x948690: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x948690: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbbf99c, size: 0x168c
    // 0xbbf99c: EnterFrame
    //     0xbbf99c: stp             fp, lr, [SP, #-0x10]!
    //     0xbbf9a0: mov             fp, SP
    // 0xbbf9a4: AllocStack(0x70)
    //     0xbbf9a4: sub             SP, SP, #0x70
    // 0xbbf9a8: SetupParameters(_OfferSectionWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbbf9a8: stur            x1, [fp, #-8]
    //     0xbbf9ac: stur            x2, [fp, #-0x10]
    // 0xbbf9b0: CheckStackOverflow
    //     0xbbf9b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbf9b4: cmp             SP, x16
    //     0xbbf9b8: b.ls            #0xbc0ffc
    // 0xbbf9bc: r1 = 2
    //     0xbbf9bc: movz            x1, #0x2
    // 0xbbf9c0: r0 = AllocateContext()
    //     0xbbf9c0: bl              #0x16f6108  ; AllocateContextStub
    // 0xbbf9c4: mov             x2, x0
    // 0xbbf9c8: ldur            x1, [fp, #-8]
    // 0xbbf9cc: stur            x2, [fp, #-0x18]
    // 0xbbf9d0: StoreField: r2->field_f = r1
    //     0xbbf9d0: stur            w1, [x2, #0xf]
    // 0xbbf9d4: ldur            x0, [fp, #-0x10]
    // 0xbbf9d8: StoreField: r2->field_13 = r0
    //     0xbbf9d8: stur            w0, [x2, #0x13]
    // 0xbbf9dc: LoadField: r0 = r1->field_b
    //     0xbbf9dc: ldur            w0, [x1, #0xb]
    // 0xbbf9e0: DecompressPointer r0
    //     0xbbf9e0: add             x0, x0, HEAP, lsl #32
    // 0xbbf9e4: cmp             w0, NULL
    // 0xbbf9e8: b.eq            #0xbc1004
    // 0xbbf9ec: LoadField: r3 = r0->field_f
    //     0xbbf9ec: ldur            w3, [x0, #0xf]
    // 0xbbf9f0: DecompressPointer r3
    //     0xbbf9f0: add             x3, x3, HEAP, lsl #32
    // 0xbbf9f4: LoadField: r4 = r3->field_b
    //     0xbbf9f4: ldur            w4, [x3, #0xb]
    // 0xbbf9f8: DecompressPointer r4
    //     0xbbf9f8: add             x4, x4, HEAP, lsl #32
    // 0xbbf9fc: cmp             w4, NULL
    // 0xbbfa00: b.ne            #0xbbfa0c
    // 0xbbfa04: r3 = Null
    //     0xbbfa04: mov             x3, NULL
    // 0xbbfa08: b               #0xbbfa20
    // 0xbbfa0c: LoadField: r3 = r4->field_13
    //     0xbbfa0c: ldur            w3, [x4, #0x13]
    // 0xbbfa10: DecompressPointer r3
    //     0xbbfa10: add             x3, x3, HEAP, lsl #32
    // 0xbbfa14: LoadField: r4 = r3->field_7
    //     0xbbfa14: ldur            w4, [x3, #7]
    // 0xbbfa18: DecompressPointer r4
    //     0xbbfa18: add             x4, x4, HEAP, lsl #32
    // 0xbbfa1c: mov             x3, x4
    // 0xbbfa20: cmp             w3, NULL
    // 0xbbfa24: b.ne            #0xbbfa30
    // 0xbbfa28: r3 = 0
    //     0xbbfa28: movz            x3, #0
    // 0xbbfa2c: b               #0xbbfa40
    // 0xbbfa30: r4 = LoadInt32Instr(r3)
    //     0xbbfa30: sbfx            x4, x3, #1, #0x1f
    //     0xbbfa34: tbz             w3, #0, #0xbbfa3c
    //     0xbbfa38: ldur            x4, [x3, #7]
    // 0xbbfa3c: mov             x3, x4
    // 0xbbfa40: cmp             x3, #0
    // 0xbbfa44: r16 = true
    //     0xbbfa44: add             x16, NULL, #0x20  ; true
    // 0xbbfa48: r17 = false
    //     0xbbfa48: add             x17, NULL, #0x30  ; false
    // 0xbbfa4c: csel            x4, x16, x17, gt
    // 0xbbfa50: stur            x4, [fp, #-0x10]
    // 0xbbfa54: LoadField: r3 = r0->field_13
    //     0xbbfa54: ldur            w3, [x0, #0x13]
    // 0xbbfa58: DecompressPointer r3
    //     0xbbfa58: add             x3, x3, HEAP, lsl #32
    // 0xbbfa5c: r0 = LoadClassIdInstr(r3)
    //     0xbbfa5c: ldur            x0, [x3, #-1]
    //     0xbbfa60: ubfx            x0, x0, #0xc, #0x14
    // 0xbbfa64: r16 = ""
    //     0xbbfa64: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbfa68: stp             x16, x3, [SP]
    // 0xbbfa6c: mov             lr, x0
    // 0xbbfa70: ldr             lr, [x21, lr, lsl #3]
    // 0xbbfa74: blr             lr
    // 0xbbfa78: tbnz            w0, #4, #0xbc0190
    // 0xbbfa7c: ldur            x0, [fp, #-8]
    // 0xbbfa80: LoadField: r1 = r0->field_b
    //     0xbbfa80: ldur            w1, [x0, #0xb]
    // 0xbbfa84: DecompressPointer r1
    //     0xbbfa84: add             x1, x1, HEAP, lsl #32
    // 0xbbfa88: cmp             w1, NULL
    // 0xbbfa8c: b.eq            #0xbc1008
    // 0xbbfa90: LoadField: r2 = r1->field_23
    //     0xbbfa90: ldur            w2, [x1, #0x23]
    // 0xbbfa94: DecompressPointer r2
    //     0xbbfa94: add             x2, x2, HEAP, lsl #32
    // 0xbbfa98: LoadField: r1 = r2->field_b
    //     0xbbfa98: ldur            w1, [x2, #0xb]
    // 0xbbfa9c: DecompressPointer r1
    //     0xbbfa9c: add             x1, x1, HEAP, lsl #32
    // 0xbbfaa0: cmp             w1, NULL
    // 0xbbfaa4: b.ne            #0xbbfab0
    // 0xbbfaa8: r1 = Null
    //     0xbbfaa8: mov             x1, NULL
    // 0xbbfaac: b               #0xbbfad0
    // 0xbbfab0: LoadField: r2 = r1->field_33
    //     0xbbfab0: ldur            w2, [x1, #0x33]
    // 0xbbfab4: DecompressPointer r2
    //     0xbbfab4: add             x2, x2, HEAP, lsl #32
    // 0xbbfab8: cmp             w2, NULL
    // 0xbbfabc: b.ne            #0xbbfac8
    // 0xbbfac0: r1 = Null
    //     0xbbfac0: mov             x1, NULL
    // 0xbbfac4: b               #0xbbfad0
    // 0xbbfac8: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xbbfac8: ldur            w1, [x2, #0x17]
    // 0xbbfacc: DecompressPointer r1
    //     0xbbfacc: add             x1, x1, HEAP, lsl #32
    // 0xbbfad0: cmp             w1, NULL
    // 0xbbfad4: b.eq            #0xbbfb10
    // 0xbbfad8: tbnz            w1, #4, #0xbbfb10
    // 0xbbfadc: r0 = SvgPicture()
    //     0xbbfadc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbbfae0: stur            x0, [fp, #-0x20]
    // 0xbbfae4: r16 = 30.000000
    //     0xbbfae4: add             x16, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xbbfae8: ldr             x16, [x16, #0x768]
    // 0xbbfaec: str             x16, [SP]
    // 0xbbfaf0: mov             x1, x0
    // 0xbbfaf4: r2 = "assets/images/gift-icon-popup.svg"
    //     0xbbfaf4: add             x2, PP, #0x52, lsl #12  ; [pp+0x528e8] "assets/images/gift-icon-popup.svg"
    //     0xbbfaf8: ldr             x2, [x2, #0x8e8]
    // 0xbbfafc: r4 = const [0, 0x3, 0x1, 0x2, height, 0x2, null]
    //     0xbbfafc: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c760] List(7) [0, 0x3, 0x1, 0x2, "height", 0x2, Null]
    //     0xbbfb00: ldr             x4, [x4, #0x760]
    // 0xbbfb04: r0 = SvgPicture.asset()
    //     0xbbfb04: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbbfb08: ldur            x1, [fp, #-0x20]
    // 0xbbfb0c: b               #0xbbfb54
    // 0xbbfb10: r0 = SvgPicture()
    //     0xbbfb10: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbbfb14: stur            x0, [fp, #-0x20]
    // 0xbbfb18: r16 = Instance_BoxFit
    //     0xbbfb18: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbbfb1c: ldr             x16, [x16, #0xb18]
    // 0xbbfb20: r30 = 40.000000
    //     0xbbfb20: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xbbfb24: ldr             lr, [lr, #8]
    // 0xbbfb28: stp             lr, x16, [SP, #8]
    // 0xbbfb2c: r16 = 40.000000
    //     0xbbfb2c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xbbfb30: ldr             x16, [x16, #8]
    // 0xbbfb34: str             x16, [SP]
    // 0xbbfb38: mov             x1, x0
    // 0xbbfb3c: r2 = "assets/images/offer_icon.svg"
    //     0xbbfb3c: add             x2, PP, #0x43, lsl #12  ; [pp+0x43758] "assets/images/offer_icon.svg"
    //     0xbbfb40: ldr             x2, [x2, #0x758]
    // 0xbbfb44: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0xbbfb44: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d8e0] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0xbbfb48: ldr             x4, [x4, #0x8e0]
    // 0xbbfb4c: r0 = SvgPicture.asset()
    //     0xbbfb4c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbbfb50: ldur            x1, [fp, #-0x20]
    // 0xbbfb54: ldur            x0, [fp, #-8]
    // 0xbbfb58: stur            x1, [fp, #-0x20]
    // 0xbbfb5c: r0 = Padding()
    //     0xbbfb5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbfb60: mov             x3, x0
    // 0xbbfb64: r0 = Instance_EdgeInsets
    //     0xbbfb64: add             x0, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xbbfb68: ldr             x0, [x0, #0x878]
    // 0xbbfb6c: stur            x3, [fp, #-0x28]
    // 0xbbfb70: StoreField: r3->field_f = r0
    //     0xbbfb70: stur            w0, [x3, #0xf]
    // 0xbbfb74: ldur            x0, [fp, #-0x20]
    // 0xbbfb78: StoreField: r3->field_b = r0
    //     0xbbfb78: stur            w0, [x3, #0xb]
    // 0xbbfb7c: ldur            x0, [fp, #-8]
    // 0xbbfb80: LoadField: r4 = r0->field_b
    //     0xbbfb80: ldur            w4, [x0, #0xb]
    // 0xbbfb84: DecompressPointer r4
    //     0xbbfb84: add             x4, x4, HEAP, lsl #32
    // 0xbbfb88: stur            x4, [fp, #-0x20]
    // 0xbbfb8c: cmp             w4, NULL
    // 0xbbfb90: b.eq            #0xbc100c
    // 0xbbfb94: LoadField: r1 = r4->field_23
    //     0xbbfb94: ldur            w1, [x4, #0x23]
    // 0xbbfb98: DecompressPointer r1
    //     0xbbfb98: add             x1, x1, HEAP, lsl #32
    // 0xbbfb9c: LoadField: r2 = r1->field_b
    //     0xbbfb9c: ldur            w2, [x1, #0xb]
    // 0xbbfba0: DecompressPointer r2
    //     0xbbfba0: add             x2, x2, HEAP, lsl #32
    // 0xbbfba4: cmp             w2, NULL
    // 0xbbfba8: b.ne            #0xbbfbb4
    // 0xbbfbac: r1 = Null
    //     0xbbfbac: mov             x1, NULL
    // 0xbbfbb0: b               #0xbbfbd8
    // 0xbbfbb4: LoadField: r1 = r2->field_33
    //     0xbbfbb4: ldur            w1, [x2, #0x33]
    // 0xbbfbb8: DecompressPointer r1
    //     0xbbfbb8: add             x1, x1, HEAP, lsl #32
    // 0xbbfbbc: cmp             w1, NULL
    // 0xbbfbc0: b.ne            #0xbbfbcc
    // 0xbbfbc4: r1 = Null
    //     0xbbfbc4: mov             x1, NULL
    // 0xbbfbc8: b               #0xbbfbd8
    // 0xbbfbcc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbbfbcc: ldur            w2, [x1, #0x17]
    // 0xbbfbd0: DecompressPointer r2
    //     0xbbfbd0: add             x2, x2, HEAP, lsl #32
    // 0xbbfbd4: mov             x1, x2
    // 0xbbfbd8: cmp             w1, NULL
    // 0xbbfbdc: b.eq            #0xbbfbf0
    // 0xbbfbe0: tbnz            w1, #4, #0xbbfbf0
    // 0xbbfbe4: r3 = "Free Gift Added!"
    //     0xbbfbe4: add             x3, PP, #0x54, lsl #12  ; [pp+0x54428] "Free Gift Added!"
    //     0xbbfbe8: ldr             x3, [x3, #0x428]
    // 0xbbfbec: b               #0xbbfc4c
    // 0xbbfbf0: r1 = Null
    //     0xbbfbf0: mov             x1, NULL
    // 0xbbfbf4: r2 = 4
    //     0xbbfbf4: movz            x2, #0x4
    // 0xbbfbf8: r0 = AllocateArray()
    //     0xbbfbf8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbfbfc: r16 = "Save upto "
    //     0xbbfbfc: add             x16, PP, #0x38, lsl #12  ; [pp+0x38ca0] "Save upto "
    //     0xbbfc00: ldr             x16, [x16, #0xca0]
    // 0xbbfc04: StoreField: r0->field_f = r16
    //     0xbbfc04: stur            w16, [x0, #0xf]
    // 0xbbfc08: ldur            x1, [fp, #-0x20]
    // 0xbbfc0c: LoadField: r2 = r1->field_f
    //     0xbbfc0c: ldur            w2, [x1, #0xf]
    // 0xbbfc10: DecompressPointer r2
    //     0xbbfc10: add             x2, x2, HEAP, lsl #32
    // 0xbbfc14: LoadField: r1 = r2->field_b
    //     0xbbfc14: ldur            w1, [x2, #0xb]
    // 0xbbfc18: DecompressPointer r1
    //     0xbbfc18: add             x1, x1, HEAP, lsl #32
    // 0xbbfc1c: cmp             w1, NULL
    // 0xbbfc20: b.ne            #0xbbfc2c
    // 0xbbfc24: r1 = Null
    //     0xbbfc24: mov             x1, NULL
    // 0xbbfc28: b               #0xbbfc38
    // 0xbbfc2c: LoadField: r2 = r1->field_b
    //     0xbbfc2c: ldur            w2, [x1, #0xb]
    // 0xbbfc30: DecompressPointer r2
    //     0xbbfc30: add             x2, x2, HEAP, lsl #32
    // 0xbbfc34: mov             x1, x2
    // 0xbbfc38: StoreField: r0->field_13 = r1
    //     0xbbfc38: stur            w1, [x0, #0x13]
    // 0xbbfc3c: str             x0, [SP]
    // 0xbbfc40: r0 = _interpolate()
    //     0xbbfc40: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbbfc44: mov             x3, x0
    // 0xbbfc48: ldur            x0, [fp, #-8]
    // 0xbbfc4c: ldur            x2, [fp, #-0x18]
    // 0xbbfc50: stur            x3, [fp, #-0x20]
    // 0xbbfc54: LoadField: r1 = r2->field_13
    //     0xbbfc54: ldur            w1, [x2, #0x13]
    // 0xbbfc58: DecompressPointer r1
    //     0xbbfc58: add             x1, x1, HEAP, lsl #32
    // 0xbbfc5c: r0 = of()
    //     0xbbfc5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbfc60: LoadField: r1 = r0->field_87
    //     0xbbfc60: ldur            w1, [x0, #0x87]
    // 0xbbfc64: DecompressPointer r1
    //     0xbbfc64: add             x1, x1, HEAP, lsl #32
    // 0xbbfc68: LoadField: r0 = r1->field_7
    //     0xbbfc68: ldur            w0, [x1, #7]
    // 0xbbfc6c: DecompressPointer r0
    //     0xbbfc6c: add             x0, x0, HEAP, lsl #32
    // 0xbbfc70: r16 = Instance_Color
    //     0xbbfc70: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbbfc74: r30 = 12.000000
    //     0xbbfc74: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbbfc78: ldr             lr, [lr, #0x9e8]
    // 0xbbfc7c: stp             lr, x16, [SP]
    // 0xbbfc80: mov             x1, x0
    // 0xbbfc84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbbfc84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbbfc88: ldr             x4, [x4, #0x9b8]
    // 0xbbfc8c: r0 = copyWith()
    //     0xbbfc8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbfc90: stur            x0, [fp, #-0x30]
    // 0xbbfc94: r0 = Text()
    //     0xbbfc94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbfc98: mov             x3, x0
    // 0xbbfc9c: ldur            x0, [fp, #-0x20]
    // 0xbbfca0: stur            x3, [fp, #-0x38]
    // 0xbbfca4: StoreField: r3->field_b = r0
    //     0xbbfca4: stur            w0, [x3, #0xb]
    // 0xbbfca8: ldur            x0, [fp, #-0x30]
    // 0xbbfcac: StoreField: r3->field_13 = r0
    //     0xbbfcac: stur            w0, [x3, #0x13]
    // 0xbbfcb0: ldur            x0, [fp, #-8]
    // 0xbbfcb4: LoadField: r1 = r0->field_b
    //     0xbbfcb4: ldur            w1, [x0, #0xb]
    // 0xbbfcb8: DecompressPointer r1
    //     0xbbfcb8: add             x1, x1, HEAP, lsl #32
    // 0xbbfcbc: cmp             w1, NULL
    // 0xbbfcc0: b.eq            #0xbc1010
    // 0xbbfcc4: LoadField: r2 = r1->field_23
    //     0xbbfcc4: ldur            w2, [x1, #0x23]
    // 0xbbfcc8: DecompressPointer r2
    //     0xbbfcc8: add             x2, x2, HEAP, lsl #32
    // 0xbbfccc: LoadField: r4 = r2->field_b
    //     0xbbfccc: ldur            w4, [x2, #0xb]
    // 0xbbfcd0: DecompressPointer r4
    //     0xbbfcd0: add             x4, x4, HEAP, lsl #32
    // 0xbbfcd4: cmp             w4, NULL
    // 0xbbfcd8: b.ne            #0xbbfce4
    // 0xbbfcdc: r2 = Null
    //     0xbbfcdc: mov             x2, NULL
    // 0xbbfce0: b               #0xbbfd08
    // 0xbbfce4: LoadField: r2 = r4->field_33
    //     0xbbfce4: ldur            w2, [x4, #0x33]
    // 0xbbfce8: DecompressPointer r2
    //     0xbbfce8: add             x2, x2, HEAP, lsl #32
    // 0xbbfcec: cmp             w2, NULL
    // 0xbbfcf0: b.ne            #0xbbfcfc
    // 0xbbfcf4: r2 = Null
    //     0xbbfcf4: mov             x2, NULL
    // 0xbbfcf8: b               #0xbbfd08
    // 0xbbfcfc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbfcfc: ldur            w4, [x2, #0x17]
    // 0xbbfd00: DecompressPointer r4
    //     0xbbfd00: add             x4, x4, HEAP, lsl #32
    // 0xbbfd04: mov             x2, x4
    // 0xbbfd08: cmp             w2, NULL
    // 0xbbfd0c: b.eq            #0xbbfd28
    // 0xbbfd10: tbnz            w2, #4, #0xbbfd28
    // 0xbbfd14: mov             x2, x0
    // 0xbbfd18: mov             x0, x3
    // 0xbbfd1c: r4 = "Offer Applied!"
    //     0xbbfd1c: add             x4, PP, #0x38, lsl #12  ; [pp+0x38d38] "Offer Applied!"
    //     0xbbfd20: ldr             x4, [x4, #0xd38]
    // 0xbbfd24: b               #0xbbfda8
    // 0xbbfd28: LoadField: r2 = r1->field_f
    //     0xbbfd28: ldur            w2, [x1, #0xf]
    // 0xbbfd2c: DecompressPointer r2
    //     0xbbfd2c: add             x2, x2, HEAP, lsl #32
    // 0xbbfd30: LoadField: r1 = r2->field_b
    //     0xbbfd30: ldur            w1, [x2, #0xb]
    // 0xbbfd34: DecompressPointer r1
    //     0xbbfd34: add             x1, x1, HEAP, lsl #32
    // 0xbbfd38: cmp             w1, NULL
    // 0xbbfd3c: b.ne            #0xbbfd48
    // 0xbbfd40: r1 = Null
    //     0xbbfd40: mov             x1, NULL
    // 0xbbfd44: b               #0xbbfd58
    // 0xbbfd48: LoadField: r2 = r1->field_13
    //     0xbbfd48: ldur            w2, [x1, #0x13]
    // 0xbbfd4c: DecompressPointer r2
    //     0xbbfd4c: add             x2, x2, HEAP, lsl #32
    // 0xbbfd50: LoadField: r1 = r2->field_7
    //     0xbbfd50: ldur            w1, [x2, #7]
    // 0xbbfd54: DecompressPointer r1
    //     0xbbfd54: add             x1, x1, HEAP, lsl #32
    // 0xbbfd58: cmp             w1, NULL
    // 0xbbfd5c: b.ne            #0xbbfd68
    // 0xbbfd60: r4 = ""
    //     0xbbfd60: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbfd64: b               #0xbbfd6c
    // 0xbbfd68: mov             x4, x1
    // 0xbbfd6c: stur            x4, [fp, #-0x20]
    // 0xbbfd70: r1 = Null
    //     0xbbfd70: mov             x1, NULL
    // 0xbbfd74: r2 = 4
    //     0xbbfd74: movz            x2, #0x4
    // 0xbbfd78: r0 = AllocateArray()
    //     0xbbfd78: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbfd7c: mov             x1, x0
    // 0xbbfd80: ldur            x0, [fp, #-0x20]
    // 0xbbfd84: StoreField: r1->field_f = r0
    //     0xbbfd84: stur            w0, [x1, #0xf]
    // 0xbbfd88: r16 = " offers available"
    //     0xbbfd88: add             x16, PP, #0x38, lsl #12  ; [pp+0x38ca8] " offers available"
    //     0xbbfd8c: ldr             x16, [x16, #0xca8]
    // 0xbbfd90: StoreField: r1->field_13 = r16
    //     0xbbfd90: stur            w16, [x1, #0x13]
    // 0xbbfd94: str             x1, [SP]
    // 0xbbfd98: r0 = _interpolate()
    //     0xbbfd98: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbbfd9c: mov             x4, x0
    // 0xbbfda0: ldur            x2, [fp, #-8]
    // 0xbbfda4: ldur            x0, [fp, #-0x38]
    // 0xbbfda8: ldur            x3, [fp, #-0x18]
    // 0xbbfdac: stur            x4, [fp, #-0x20]
    // 0xbbfdb0: LoadField: r1 = r3->field_13
    //     0xbbfdb0: ldur            w1, [x3, #0x13]
    // 0xbbfdb4: DecompressPointer r1
    //     0xbbfdb4: add             x1, x1, HEAP, lsl #32
    // 0xbbfdb8: r0 = of()
    //     0xbbfdb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbfdbc: LoadField: r1 = r0->field_87
    //     0xbbfdbc: ldur            w1, [x0, #0x87]
    // 0xbbfdc0: DecompressPointer r1
    //     0xbbfdc0: add             x1, x1, HEAP, lsl #32
    // 0xbbfdc4: LoadField: r0 = r1->field_2b
    //     0xbbfdc4: ldur            w0, [x1, #0x2b]
    // 0xbbfdc8: DecompressPointer r0
    //     0xbbfdc8: add             x0, x0, HEAP, lsl #32
    // 0xbbfdcc: r16 = Instance_Color
    //     0xbbfdcc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbbfdd0: ldr             x16, [x16, #0x858]
    // 0xbbfdd4: r30 = 12.000000
    //     0xbbfdd4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbbfdd8: ldr             lr, [lr, #0x9e8]
    // 0xbbfddc: stp             lr, x16, [SP]
    // 0xbbfde0: mov             x1, x0
    // 0xbbfde4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbbfde4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbbfde8: ldr             x4, [x4, #0x9b8]
    // 0xbbfdec: r0 = copyWith()
    //     0xbbfdec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbfdf0: stur            x0, [fp, #-0x30]
    // 0xbbfdf4: r0 = Text()
    //     0xbbfdf4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbfdf8: mov             x3, x0
    // 0xbbfdfc: ldur            x0, [fp, #-0x20]
    // 0xbbfe00: stur            x3, [fp, #-0x40]
    // 0xbbfe04: StoreField: r3->field_b = r0
    //     0xbbfe04: stur            w0, [x3, #0xb]
    // 0xbbfe08: ldur            x0, [fp, #-0x30]
    // 0xbbfe0c: StoreField: r3->field_13 = r0
    //     0xbbfe0c: stur            w0, [x3, #0x13]
    // 0xbbfe10: r1 = Null
    //     0xbbfe10: mov             x1, NULL
    // 0xbbfe14: r2 = 4
    //     0xbbfe14: movz            x2, #0x4
    // 0xbbfe18: r0 = AllocateArray()
    //     0xbbfe18: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbfe1c: mov             x2, x0
    // 0xbbfe20: ldur            x0, [fp, #-0x38]
    // 0xbbfe24: stur            x2, [fp, #-0x20]
    // 0xbbfe28: StoreField: r2->field_f = r0
    //     0xbbfe28: stur            w0, [x2, #0xf]
    // 0xbbfe2c: ldur            x0, [fp, #-0x40]
    // 0xbbfe30: StoreField: r2->field_13 = r0
    //     0xbbfe30: stur            w0, [x2, #0x13]
    // 0xbbfe34: r1 = <Widget>
    //     0xbbfe34: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbfe38: r0 = AllocateGrowableArray()
    //     0xbbfe38: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbfe3c: mov             x1, x0
    // 0xbbfe40: ldur            x0, [fp, #-0x20]
    // 0xbbfe44: stur            x1, [fp, #-0x30]
    // 0xbbfe48: StoreField: r1->field_f = r0
    //     0xbbfe48: stur            w0, [x1, #0xf]
    // 0xbbfe4c: r2 = 4
    //     0xbbfe4c: movz            x2, #0x4
    // 0xbbfe50: StoreField: r1->field_b = r2
    //     0xbbfe50: stur            w2, [x1, #0xb]
    // 0xbbfe54: r0 = Column()
    //     0xbbfe54: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbbfe58: r1 = Instance_Axis
    //     0xbbfe58: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbbfe5c: stur            x0, [fp, #-0x20]
    // 0xbbfe60: StoreField: r0->field_f = r1
    //     0xbbfe60: stur            w1, [x0, #0xf]
    // 0xbbfe64: r3 = Instance_MainAxisAlignment
    //     0xbbfe64: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbbfe68: ldr             x3, [x3, #0xab0]
    // 0xbbfe6c: StoreField: r0->field_13 = r3
    //     0xbbfe6c: stur            w3, [x0, #0x13]
    // 0xbbfe70: r2 = Instance_MainAxisSize
    //     0xbbfe70: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbfe74: ldr             x2, [x2, #0xa10]
    // 0xbbfe78: ArrayStore: r0[0] = r2  ; List_4
    //     0xbbfe78: stur            w2, [x0, #0x17]
    // 0xbbfe7c: r4 = Instance_CrossAxisAlignment
    //     0xbbfe7c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbbfe80: ldr             x4, [x4, #0x890]
    // 0xbbfe84: StoreField: r0->field_1b = r4
    //     0xbbfe84: stur            w4, [x0, #0x1b]
    // 0xbbfe88: r3 = Instance_VerticalDirection
    //     0xbbfe88: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbfe8c: ldr             x3, [x3, #0xa20]
    // 0xbbfe90: StoreField: r0->field_23 = r3
    //     0xbbfe90: stur            w3, [x0, #0x23]
    // 0xbbfe94: r4 = Instance_Clip
    //     0xbbfe94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbfe98: ldr             x4, [x4, #0x38]
    // 0xbbfe9c: StoreField: r0->field_2b = r4
    //     0xbbfe9c: stur            w4, [x0, #0x2b]
    // 0xbbfea0: StoreField: r0->field_2f = rZR
    //     0xbbfea0: stur            xzr, [x0, #0x2f]
    // 0xbbfea4: ldur            x1, [fp, #-0x30]
    // 0xbbfea8: StoreField: r0->field_b = r1
    //     0xbbfea8: stur            w1, [x0, #0xb]
    // 0xbbfeac: r1 = <FlexParentData>
    //     0xbbfeac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbbfeb0: ldr             x1, [x1, #0xe00]
    // 0xbbfeb4: r0 = Expanded()
    //     0xbbfeb4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbbfeb8: stur            x0, [fp, #-0x30]
    // 0xbbfebc: StoreField: r0->field_13 = rZR
    //     0xbbfebc: stur            xzr, [x0, #0x13]
    // 0xbbfec0: r5 = Instance_FlexFit
    //     0xbbfec0: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbbfec4: ldr             x5, [x5, #0xe08]
    // 0xbbfec8: StoreField: r0->field_1b = r5
    //     0xbbfec8: stur            w5, [x0, #0x1b]
    // 0xbbfecc: ldur            x1, [fp, #-0x20]
    // 0xbbfed0: StoreField: r0->field_b = r1
    //     0xbbfed0: stur            w1, [x0, #0xb]
    // 0xbbfed4: ldur            x6, [fp, #-8]
    // 0xbbfed8: LoadField: r1 = r6->field_b
    //     0xbbfed8: ldur            w1, [x6, #0xb]
    // 0xbbfedc: DecompressPointer r1
    //     0xbbfedc: add             x1, x1, HEAP, lsl #32
    // 0xbbfee0: cmp             w1, NULL
    // 0xbbfee4: b.eq            #0xbc1014
    // 0xbbfee8: LoadField: r2 = r1->field_23
    //     0xbbfee8: ldur            w2, [x1, #0x23]
    // 0xbbfeec: DecompressPointer r2
    //     0xbbfeec: add             x2, x2, HEAP, lsl #32
    // 0xbbfef0: LoadField: r1 = r2->field_b
    //     0xbbfef0: ldur            w1, [x2, #0xb]
    // 0xbbfef4: DecompressPointer r1
    //     0xbbfef4: add             x1, x1, HEAP, lsl #32
    // 0xbbfef8: cmp             w1, NULL
    // 0xbbfefc: b.ne            #0xbbff08
    // 0xbbff00: r1 = Null
    //     0xbbff00: mov             x1, NULL
    // 0xbbff04: b               #0xbbff28
    // 0xbbff08: LoadField: r2 = r1->field_33
    //     0xbbff08: ldur            w2, [x1, #0x33]
    // 0xbbff0c: DecompressPointer r2
    //     0xbbff0c: add             x2, x2, HEAP, lsl #32
    // 0xbbff10: cmp             w2, NULL
    // 0xbbff14: b.ne            #0xbbff20
    // 0xbbff18: r1 = Null
    //     0xbbff18: mov             x1, NULL
    // 0xbbff1c: b               #0xbbff28
    // 0xbbff20: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xbbff20: ldur            w1, [x2, #0x17]
    // 0xbbff24: DecompressPointer r1
    //     0xbbff24: add             x1, x1, HEAP, lsl #32
    // 0xbbff28: cmp             w1, NULL
    // 0xbbff2c: b.eq            #0xbbff40
    // 0xbbff30: tbnz            w1, #4, #0xbbff40
    // 0xbbff34: r4 = "CHANGE"
    //     0xbbff34: add             x4, PP, #0x38, lsl #12  ; [pp+0x38d10] "CHANGE"
    //     0xbbff38: ldr             x4, [x4, #0xd10]
    // 0xbbff3c: b               #0xbbff48
    // 0xbbff40: r4 = "OFFERS"
    //     0xbbff40: add             x4, PP, #0x38, lsl #12  ; [pp+0x38cb8] "OFFERS"
    //     0xbbff44: ldr             x4, [x4, #0xcb8]
    // 0xbbff48: ldur            x2, [fp, #-0x18]
    // 0xbbff4c: ldur            x3, [fp, #-0x28]
    // 0xbbff50: stur            x4, [fp, #-0x20]
    // 0xbbff54: LoadField: r1 = r2->field_13
    //     0xbbff54: ldur            w1, [x2, #0x13]
    // 0xbbff58: DecompressPointer r1
    //     0xbbff58: add             x1, x1, HEAP, lsl #32
    // 0xbbff5c: r0 = of()
    //     0xbbff5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbff60: LoadField: r1 = r0->field_87
    //     0xbbff60: ldur            w1, [x0, #0x87]
    // 0xbbff64: DecompressPointer r1
    //     0xbbff64: add             x1, x1, HEAP, lsl #32
    // 0xbbff68: LoadField: r0 = r1->field_7
    //     0xbbff68: ldur            w0, [x1, #7]
    // 0xbbff6c: DecompressPointer r0
    //     0xbbff6c: add             x0, x0, HEAP, lsl #32
    // 0xbbff70: r16 = 14.000000
    //     0xbbff70: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbbff74: ldr             x16, [x16, #0x1d8]
    // 0xbbff78: r30 = Instance_Color
    //     0xbbff78: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbbff7c: ldr             lr, [lr, #0x858]
    // 0xbbff80: stp             lr, x16, [SP]
    // 0xbbff84: mov             x1, x0
    // 0xbbff88: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbbff88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbbff8c: ldr             x4, [x4, #0xaa0]
    // 0xbbff90: r0 = copyWith()
    //     0xbbff90: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbff94: stur            x0, [fp, #-0x38]
    // 0xbbff98: r0 = Text()
    //     0xbbff98: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbff9c: mov             x1, x0
    // 0xbbffa0: ldur            x0, [fp, #-0x20]
    // 0xbbffa4: stur            x1, [fp, #-0x40]
    // 0xbbffa8: StoreField: r1->field_b = r0
    //     0xbbffa8: stur            w0, [x1, #0xb]
    // 0xbbffac: ldur            x0, [fp, #-0x38]
    // 0xbbffb0: StoreField: r1->field_13 = r0
    //     0xbbffb0: stur            w0, [x1, #0x13]
    // 0xbbffb4: r0 = InkWell()
    //     0xbbffb4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbbffb8: mov             x3, x0
    // 0xbbffbc: ldur            x0, [fp, #-0x40]
    // 0xbbffc0: stur            x3, [fp, #-0x20]
    // 0xbbffc4: StoreField: r3->field_b = r0
    //     0xbbffc4: stur            w0, [x3, #0xb]
    // 0xbbffc8: ldur            x2, [fp, #-0x18]
    // 0xbbffcc: r1 = Function '<anonymous closure>':.
    //     0xbbffcc: add             x1, PP, #0x54, lsl #12  ; [pp+0x54430] AnonymousClosure: (0xbc15f4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xbbf99c)
    //     0xbbffd0: ldr             x1, [x1, #0x430]
    // 0xbbffd4: r0 = AllocateClosure()
    //     0xbbffd4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbffd8: mov             x1, x0
    // 0xbbffdc: ldur            x0, [fp, #-0x20]
    // 0xbbffe0: StoreField: r0->field_f = r1
    //     0xbbffe0: stur            w1, [x0, #0xf]
    // 0xbbffe4: r1 = true
    //     0xbbffe4: add             x1, NULL, #0x20  ; true
    // 0xbbffe8: StoreField: r0->field_43 = r1
    //     0xbbffe8: stur            w1, [x0, #0x43]
    // 0xbbffec: r2 = Instance_BoxShape
    //     0xbbffec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbfff0: ldr             x2, [x2, #0x80]
    // 0xbbfff4: StoreField: r0->field_47 = r2
    //     0xbbfff4: stur            w2, [x0, #0x47]
    // 0xbbfff8: StoreField: r0->field_6f = r1
    //     0xbbfff8: stur            w1, [x0, #0x6f]
    // 0xbbfffc: r3 = false
    //     0xbbfffc: add             x3, NULL, #0x30  ; false
    // 0xbc0000: StoreField: r0->field_73 = r3
    //     0xbc0000: stur            w3, [x0, #0x73]
    // 0xbc0004: StoreField: r0->field_83 = r1
    //     0xbc0004: stur            w1, [x0, #0x83]
    // 0xbc0008: StoreField: r0->field_7b = r3
    //     0xbc0008: stur            w3, [x0, #0x7b]
    // 0xbc000c: r0 = Padding()
    //     0xbc000c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc0010: r7 = Instance_EdgeInsets
    //     0xbc0010: add             x7, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0xbc0014: ldr             x7, [x7, #0xf98]
    // 0xbc0018: stur            x0, [fp, #-0x38]
    // 0xbc001c: StoreField: r0->field_f = r7
    //     0xbc001c: stur            w7, [x0, #0xf]
    // 0xbc0020: ldur            x1, [fp, #-0x20]
    // 0xbc0024: StoreField: r0->field_b = r1
    //     0xbc0024: stur            w1, [x0, #0xb]
    // 0xbc0028: r1 = Null
    //     0xbc0028: mov             x1, NULL
    // 0xbc002c: r2 = 8
    //     0xbc002c: movz            x2, #0x8
    // 0xbc0030: r0 = AllocateArray()
    //     0xbc0030: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc0034: mov             x2, x0
    // 0xbc0038: ldur            x0, [fp, #-0x28]
    // 0xbc003c: stur            x2, [fp, #-0x20]
    // 0xbc0040: StoreField: r2->field_f = r0
    //     0xbc0040: stur            w0, [x2, #0xf]
    // 0xbc0044: ldur            x0, [fp, #-0x30]
    // 0xbc0048: StoreField: r2->field_13 = r0
    //     0xbc0048: stur            w0, [x2, #0x13]
    // 0xbc004c: r16 = Instance_Spacer
    //     0xbc004c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbc0050: ldr             x16, [x16, #0xf0]
    // 0xbc0054: ArrayStore: r2[0] = r16  ; List_4
    //     0xbc0054: stur            w16, [x2, #0x17]
    // 0xbc0058: ldur            x0, [fp, #-0x38]
    // 0xbc005c: StoreField: r2->field_1b = r0
    //     0xbc005c: stur            w0, [x2, #0x1b]
    // 0xbc0060: r1 = <Widget>
    //     0xbc0060: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc0064: r0 = AllocateGrowableArray()
    //     0xbc0064: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc0068: mov             x1, x0
    // 0xbc006c: ldur            x0, [fp, #-0x20]
    // 0xbc0070: stur            x1, [fp, #-0x28]
    // 0xbc0074: StoreField: r1->field_f = r0
    //     0xbc0074: stur            w0, [x1, #0xf]
    // 0xbc0078: r8 = 8
    //     0xbc0078: movz            x8, #0x8
    // 0xbc007c: StoreField: r1->field_b = r8
    //     0xbc007c: stur            w8, [x1, #0xb]
    // 0xbc0080: r0 = Row()
    //     0xbc0080: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbc0084: r9 = Instance_Axis
    //     0xbc0084: ldr             x9, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc0088: stur            x0, [fp, #-0x20]
    // 0xbc008c: StoreField: r0->field_f = r9
    //     0xbc008c: stur            w9, [x0, #0xf]
    // 0xbc0090: r10 = Instance_MainAxisAlignment
    //     0xbc0090: add             x10, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xbc0094: ldr             x10, [x10, #0xd10]
    // 0xbc0098: StoreField: r0->field_13 = r10
    //     0xbc0098: stur            w10, [x0, #0x13]
    // 0xbc009c: r11 = Instance_MainAxisSize
    //     0xbc009c: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc00a0: ldr             x11, [x11, #0xa10]
    // 0xbc00a4: ArrayStore: r0[0] = r11  ; List_4
    //     0xbc00a4: stur            w11, [x0, #0x17]
    // 0xbc00a8: r12 = Instance_CrossAxisAlignment
    //     0xbc00a8: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc00ac: ldr             x12, [x12, #0xa18]
    // 0xbc00b0: StoreField: r0->field_1b = r12
    //     0xbc00b0: stur            w12, [x0, #0x1b]
    // 0xbc00b4: r13 = Instance_VerticalDirection
    //     0xbc00b4: add             x13, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc00b8: ldr             x13, [x13, #0xa20]
    // 0xbc00bc: StoreField: r0->field_23 = r13
    //     0xbc00bc: stur            w13, [x0, #0x23]
    // 0xbc00c0: r14 = Instance_Clip
    //     0xbc00c0: add             x14, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc00c4: ldr             x14, [x14, #0x38]
    // 0xbc00c8: StoreField: r0->field_2b = r14
    //     0xbc00c8: stur            w14, [x0, #0x2b]
    // 0xbc00cc: StoreField: r0->field_2f = rZR
    //     0xbc00cc: stur            xzr, [x0, #0x2f]
    // 0xbc00d0: ldur            x1, [fp, #-0x28]
    // 0xbc00d4: StoreField: r0->field_b = r1
    //     0xbc00d4: stur            w1, [x0, #0xb]
    // 0xbc00d8: r0 = Container()
    //     0xbc00d8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbc00dc: stur            x0, [fp, #-0x28]
    // 0xbc00e0: r16 = 60.000000
    //     0xbc00e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xbc00e4: ldr             x16, [x16, #0x110]
    // 0xbc00e8: r30 = Instance_BoxDecoration
    //     0xbc00e8: add             lr, PP, #0x54, lsl #12  ; [pp+0x54438] Obj!BoxDecoration@d64bc1
    //     0xbc00ec: ldr             lr, [lr, #0x438]
    // 0xbc00f0: stp             lr, x16, [SP, #8]
    // 0xbc00f4: ldur            x16, [fp, #-0x20]
    // 0xbc00f8: str             x16, [SP]
    // 0xbc00fc: mov             x1, x0
    // 0xbc0100: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xbc0100: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xbc0104: ldr             x4, [x4, #0xc78]
    // 0xbc0108: r0 = Container()
    //     0xbc0108: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbc010c: r0 = Padding()
    //     0xbc010c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc0110: mov             x1, x0
    // 0xbc0114: r0 = Instance_EdgeInsets
    //     0xbc0114: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe18] Obj!EdgeInsets@d57ef1
    //     0xbc0118: ldr             x0, [x0, #0xe18]
    // 0xbc011c: stur            x1, [fp, #-0x20]
    // 0xbc0120: StoreField: r1->field_f = r0
    //     0xbc0120: stur            w0, [x1, #0xf]
    // 0xbc0124: ldur            x0, [fp, #-0x28]
    // 0xbc0128: StoreField: r1->field_b = r0
    //     0xbc0128: stur            w0, [x1, #0xb]
    // 0xbc012c: r0 = InkWell()
    //     0xbc012c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbc0130: mov             x3, x0
    // 0xbc0134: ldur            x0, [fp, #-0x20]
    // 0xbc0138: stur            x3, [fp, #-0x28]
    // 0xbc013c: StoreField: r3->field_b = r0
    //     0xbc013c: stur            w0, [x3, #0xb]
    // 0xbc0140: ldur            x2, [fp, #-0x18]
    // 0xbc0144: r1 = Function '<anonymous closure>':.
    //     0xbc0144: add             x1, PP, #0x54, lsl #12  ; [pp+0x54440] AnonymousClosure: (0xbc1578), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xbbf99c)
    //     0xbc0148: ldr             x1, [x1, #0x440]
    // 0xbc014c: r0 = AllocateClosure()
    //     0xbc014c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc0150: mov             x1, x0
    // 0xbc0154: ldur            x0, [fp, #-0x28]
    // 0xbc0158: StoreField: r0->field_f = r1
    //     0xbc0158: stur            w1, [x0, #0xf]
    // 0xbc015c: r19 = true
    //     0xbc015c: add             x19, NULL, #0x20  ; true
    // 0xbc0160: StoreField: r0->field_43 = r19
    //     0xbc0160: stur            w19, [x0, #0x43]
    // 0xbc0164: r20 = Instance_BoxShape
    //     0xbc0164: add             x20, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbc0168: ldr             x20, [x20, #0x80]
    // 0xbc016c: StoreField: r0->field_47 = r20
    //     0xbc016c: stur            w20, [x0, #0x47]
    // 0xbc0170: StoreField: r0->field_6f = r19
    //     0xbc0170: stur            w19, [x0, #0x6f]
    // 0xbc0174: r23 = false
    //     0xbc0174: add             x23, NULL, #0x30  ; false
    // 0xbc0178: StoreField: r0->field_73 = r23
    //     0xbc0178: stur            w23, [x0, #0x73]
    // 0xbc017c: StoreField: r0->field_83 = r19
    //     0xbc017c: stur            w19, [x0, #0x83]
    // 0xbc0180: StoreField: r0->field_7b = r23
    //     0xbc0180: stur            w23, [x0, #0x7b]
    // 0xbc0184: mov             x1, x0
    // 0xbc0188: mov             x2, x23
    // 0xbc018c: b               #0xbc0fb0
    // 0xbc0190: ldur            x6, [fp, #-8]
    // 0xbc0194: r19 = true
    //     0xbc0194: add             x19, NULL, #0x20  ; true
    // 0xbc0198: r2 = 4
    //     0xbc0198: movz            x2, #0x4
    // 0xbc019c: r3 = Instance_MainAxisAlignment
    //     0xbc019c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbc01a0: ldr             x3, [x3, #0xab0]
    // 0xbc01a4: r4 = Instance_CrossAxisAlignment
    //     0xbc01a4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbc01a8: ldr             x4, [x4, #0x890]
    // 0xbc01ac: r23 = false
    //     0xbc01ac: add             x23, NULL, #0x30  ; false
    // 0xbc01b0: r7 = Instance_EdgeInsets
    //     0xbc01b0: add             x7, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0xbc01b4: ldr             x7, [x7, #0xf98]
    // 0xbc01b8: r10 = Instance_MainAxisAlignment
    //     0xbc01b8: add             x10, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xbc01bc: ldr             x10, [x10, #0xd10]
    // 0xbc01c0: r20 = Instance_BoxShape
    //     0xbc01c0: add             x20, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbc01c4: ldr             x20, [x20, #0x80]
    // 0xbc01c8: r11 = Instance_MainAxisSize
    //     0xbc01c8: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc01cc: ldr             x11, [x11, #0xa10]
    // 0xbc01d0: r13 = Instance_VerticalDirection
    //     0xbc01d0: add             x13, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc01d4: ldr             x13, [x13, #0xa20]
    // 0xbc01d8: r1 = Instance_Axis
    //     0xbc01d8: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbc01dc: r5 = Instance_FlexFit
    //     0xbc01dc: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbc01e0: ldr             x5, [x5, #0xe08]
    // 0xbc01e4: r8 = 8
    //     0xbc01e4: movz            x8, #0x8
    // 0xbc01e8: r12 = Instance_CrossAxisAlignment
    //     0xbc01e8: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc01ec: ldr             x12, [x12, #0xa18]
    // 0xbc01f0: r9 = Instance_Axis
    //     0xbc01f0: ldr             x9, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc01f4: r14 = Instance_Clip
    //     0xbc01f4: add             x14, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc01f8: ldr             x14, [x14, #0x38]
    // 0xbc01fc: LoadField: r0 = r6->field_b
    //     0xbc01fc: ldur            w0, [x6, #0xb]
    // 0xbc0200: DecompressPointer r0
    //     0xbc0200: add             x0, x0, HEAP, lsl #32
    // 0xbc0204: cmp             w0, NULL
    // 0xbc0208: b.eq            #0xbc1018
    // 0xbc020c: LoadField: r24 = r0->field_27
    //     0xbc020c: ldur            w24, [x0, #0x27]
    // 0xbc0210: DecompressPointer r24
    //     0xbc0210: add             x24, x24, HEAP, lsl #32
    // 0xbc0214: r0 = LoadClassIdInstr(r24)
    //     0xbc0214: ldur            x0, [x24, #-1]
    //     0xbc0218: ubfx            x0, x0, #0xc, #0x14
    // 0xbc021c: r16 = "bumper_coupon"
    //     0xbc021c: add             x16, PP, #0x12, lsl #12  ; [pp+0x128d8] "bumper_coupon"
    //     0xbc0220: ldr             x16, [x16, #0x8d8]
    // 0xbc0224: stp             x16, x24, [SP]
    // 0xbc0228: mov             lr, x0
    // 0xbc022c: ldr             lr, [x21, lr, lsl #3]
    // 0xbc0230: blr             lr
    // 0xbc0234: tbnz            w0, #4, #0xbc09b8
    // 0xbc0238: ldur            x0, [fp, #-8]
    // 0xbc023c: LoadField: r1 = r0->field_b
    //     0xbc023c: ldur            w1, [x0, #0xb]
    // 0xbc0240: DecompressPointer r1
    //     0xbc0240: add             x1, x1, HEAP, lsl #32
    // 0xbc0244: stur            x1, [fp, #-0x28]
    // 0xbc0248: cmp             w1, NULL
    // 0xbc024c: b.eq            #0xbc101c
    // 0xbc0250: LoadField: r0 = r1->field_b
    //     0xbc0250: ldur            w0, [x1, #0xb]
    // 0xbc0254: DecompressPointer r0
    //     0xbc0254: add             x0, x0, HEAP, lsl #32
    // 0xbc0258: LoadField: r2 = r0->field_13
    //     0xbc0258: ldur            w2, [x0, #0x13]
    // 0xbc025c: DecompressPointer r2
    //     0xbc025c: add             x2, x2, HEAP, lsl #32
    // 0xbc0260: stur            x2, [fp, #-0x20]
    // 0xbc0264: cmp             w2, NULL
    // 0xbc0268: b.ne            #0xbc0274
    // 0xbc026c: r0 = Null
    //     0xbc026c: mov             x0, NULL
    // 0xbc0270: b               #0xbc027c
    // 0xbc0274: LoadField: r0 = r2->field_7
    //     0xbc0274: ldur            w0, [x2, #7]
    // 0xbc0278: DecompressPointer r0
    //     0xbc0278: add             x0, x0, HEAP, lsl #32
    // 0xbc027c: cmp             w0, NULL
    // 0xbc0280: b.ne            #0xbc028c
    // 0xbc0284: r0 = 0
    //     0xbc0284: movz            x0, #0
    // 0xbc0288: b               #0xbc029c
    // 0xbc028c: r3 = LoadInt32Instr(r0)
    //     0xbc028c: sbfx            x3, x0, #1, #0x1f
    //     0xbc0290: tbz             w0, #0, #0xbc0298
    //     0xbc0294: ldur            x3, [x0, #7]
    // 0xbc0298: mov             x0, x3
    // 0xbc029c: stur            x0, [fp, #-0x58]
    // 0xbc02a0: cmp             w2, NULL
    // 0xbc02a4: b.ne            #0xbc02b0
    // 0xbc02a8: r3 = Null
    //     0xbc02a8: mov             x3, NULL
    // 0xbc02ac: b               #0xbc02b8
    // 0xbc02b0: LoadField: r3 = r2->field_b
    //     0xbc02b0: ldur            w3, [x2, #0xb]
    // 0xbc02b4: DecompressPointer r3
    //     0xbc02b4: add             x3, x3, HEAP, lsl #32
    // 0xbc02b8: cmp             w3, NULL
    // 0xbc02bc: b.ne            #0xbc02c8
    // 0xbc02c0: r3 = 0
    //     0xbc02c0: movz            x3, #0
    // 0xbc02c4: b               #0xbc02d8
    // 0xbc02c8: r4 = LoadInt32Instr(r3)
    //     0xbc02c8: sbfx            x4, x3, #1, #0x1f
    //     0xbc02cc: tbz             w3, #0, #0xbc02d4
    //     0xbc02d0: ldur            x4, [x3, #7]
    // 0xbc02d4: mov             x3, x4
    // 0xbc02d8: stur            x3, [fp, #-0x50]
    // 0xbc02dc: cmp             w2, NULL
    // 0xbc02e0: b.ne            #0xbc02ec
    // 0xbc02e4: r4 = Null
    //     0xbc02e4: mov             x4, NULL
    // 0xbc02e8: b               #0xbc02f4
    // 0xbc02ec: LoadField: r4 = r2->field_f
    //     0xbc02ec: ldur            w4, [x2, #0xf]
    // 0xbc02f0: DecompressPointer r4
    //     0xbc02f0: add             x4, x4, HEAP, lsl #32
    // 0xbc02f4: cmp             w4, NULL
    // 0xbc02f8: b.ne            #0xbc0304
    // 0xbc02fc: r4 = 0
    //     0xbc02fc: movz            x4, #0
    // 0xbc0300: b               #0xbc0314
    // 0xbc0304: r5 = LoadInt32Instr(r4)
    //     0xbc0304: sbfx            x5, x4, #1, #0x1f
    //     0xbc0308: tbz             w4, #0, #0xbc0310
    //     0xbc030c: ldur            x5, [x4, #7]
    // 0xbc0310: mov             x4, x5
    // 0xbc0314: stur            x4, [fp, #-0x48]
    // 0xbc0318: r0 = Color()
    //     0xbc0318: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbc031c: mov             x1, x0
    // 0xbc0320: r0 = Instance_ColorSpace
    //     0xbc0320: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbc0324: stur            x1, [fp, #-0x30]
    // 0xbc0328: StoreField: r1->field_27 = r0
    //     0xbc0328: stur            w0, [x1, #0x27]
    // 0xbc032c: d0 = 1.000000
    //     0xbc032c: fmov            d0, #1.00000000
    // 0xbc0330: StoreField: r1->field_7 = d0
    //     0xbc0330: stur            d0, [x1, #7]
    // 0xbc0334: ldur            x2, [fp, #-0x58]
    // 0xbc0338: ubfx            x2, x2, #0, #0x20
    // 0xbc033c: and             w3, w2, #0xff
    // 0xbc0340: ubfx            x3, x3, #0, #0x20
    // 0xbc0344: scvtf           d0, x3
    // 0xbc0348: d1 = 255.000000
    //     0xbc0348: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbc034c: fdiv            d2, d0, d1
    // 0xbc0350: StoreField: r1->field_f = d2
    //     0xbc0350: stur            d2, [x1, #0xf]
    // 0xbc0354: ldur            x2, [fp, #-0x50]
    // 0xbc0358: ubfx            x2, x2, #0, #0x20
    // 0xbc035c: and             w3, w2, #0xff
    // 0xbc0360: ubfx            x3, x3, #0, #0x20
    // 0xbc0364: scvtf           d0, x3
    // 0xbc0368: fdiv            d2, d0, d1
    // 0xbc036c: ArrayStore: r1[0] = d2  ; List_8
    //     0xbc036c: stur            d2, [x1, #0x17]
    // 0xbc0370: ldur            x2, [fp, #-0x48]
    // 0xbc0374: ubfx            x2, x2, #0, #0x20
    // 0xbc0378: and             w3, w2, #0xff
    // 0xbc037c: ubfx            x3, x3, #0, #0x20
    // 0xbc0380: scvtf           d0, x3
    // 0xbc0384: fdiv            d2, d0, d1
    // 0xbc0388: StoreField: r1->field_1f = d2
    //     0xbc0388: stur            d2, [x1, #0x1f]
    // 0xbc038c: ldur            x2, [fp, #-0x20]
    // 0xbc0390: cmp             w2, NULL
    // 0xbc0394: b.ne            #0xbc03a0
    // 0xbc0398: r3 = Null
    //     0xbc0398: mov             x3, NULL
    // 0xbc039c: b               #0xbc03a8
    // 0xbc03a0: LoadField: r3 = r2->field_7
    //     0xbc03a0: ldur            w3, [x2, #7]
    // 0xbc03a4: DecompressPointer r3
    //     0xbc03a4: add             x3, x3, HEAP, lsl #32
    // 0xbc03a8: cmp             w3, NULL
    // 0xbc03ac: b.ne            #0xbc03b8
    // 0xbc03b0: r3 = 0
    //     0xbc03b0: movz            x3, #0
    // 0xbc03b4: b               #0xbc03c8
    // 0xbc03b8: r4 = LoadInt32Instr(r3)
    //     0xbc03b8: sbfx            x4, x3, #1, #0x1f
    //     0xbc03bc: tbz             w3, #0, #0xbc03c4
    //     0xbc03c0: ldur            x4, [x3, #7]
    // 0xbc03c4: mov             x3, x4
    // 0xbc03c8: stur            x3, [fp, #-0x58]
    // 0xbc03cc: cmp             w2, NULL
    // 0xbc03d0: b.ne            #0xbc03dc
    // 0xbc03d4: r4 = Null
    //     0xbc03d4: mov             x4, NULL
    // 0xbc03d8: b               #0xbc03e4
    // 0xbc03dc: LoadField: r4 = r2->field_b
    //     0xbc03dc: ldur            w4, [x2, #0xb]
    // 0xbc03e0: DecompressPointer r4
    //     0xbc03e0: add             x4, x4, HEAP, lsl #32
    // 0xbc03e4: cmp             w4, NULL
    // 0xbc03e8: b.ne            #0xbc03f4
    // 0xbc03ec: r4 = 0
    //     0xbc03ec: movz            x4, #0
    // 0xbc03f0: b               #0xbc0404
    // 0xbc03f4: r5 = LoadInt32Instr(r4)
    //     0xbc03f4: sbfx            x5, x4, #1, #0x1f
    //     0xbc03f8: tbz             w4, #0, #0xbc0400
    //     0xbc03fc: ldur            x5, [x4, #7]
    // 0xbc0400: mov             x4, x5
    // 0xbc0404: stur            x4, [fp, #-0x50]
    // 0xbc0408: cmp             w2, NULL
    // 0xbc040c: b.ne            #0xbc0418
    // 0xbc0410: r2 = Null
    //     0xbc0410: mov             x2, NULL
    // 0xbc0414: b               #0xbc0424
    // 0xbc0418: LoadField: r5 = r2->field_f
    //     0xbc0418: ldur            w5, [x2, #0xf]
    // 0xbc041c: DecompressPointer r5
    //     0xbc041c: add             x5, x5, HEAP, lsl #32
    // 0xbc0420: mov             x2, x5
    // 0xbc0424: cmp             w2, NULL
    // 0xbc0428: b.ne            #0xbc0434
    // 0xbc042c: r5 = 0
    //     0xbc042c: movz            x5, #0
    // 0xbc0430: b               #0xbc0440
    // 0xbc0434: r5 = LoadInt32Instr(r2)
    //     0xbc0434: sbfx            x5, x2, #1, #0x1f
    //     0xbc0438: tbz             w2, #0, #0xbc0440
    //     0xbc043c: ldur            x5, [x2, #7]
    // 0xbc0440: ldur            x2, [fp, #-0x28]
    // 0xbc0444: stur            x5, [fp, #-0x48]
    // 0xbc0448: r0 = Color()
    //     0xbc0448: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbc044c: mov             x3, x0
    // 0xbc0450: r0 = Instance_ColorSpace
    //     0xbc0450: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbc0454: stur            x3, [fp, #-0x20]
    // 0xbc0458: StoreField: r3->field_27 = r0
    //     0xbc0458: stur            w0, [x3, #0x27]
    // 0xbc045c: d0 = 0.700000
    //     0xbc045c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbc0460: ldr             d0, [x17, #0xf48]
    // 0xbc0464: StoreField: r3->field_7 = d0
    //     0xbc0464: stur            d0, [x3, #7]
    // 0xbc0468: ldur            x0, [fp, #-0x58]
    // 0xbc046c: ubfx            x0, x0, #0, #0x20
    // 0xbc0470: and             w1, w0, #0xff
    // 0xbc0474: ubfx            x1, x1, #0, #0x20
    // 0xbc0478: scvtf           d0, x1
    // 0xbc047c: d1 = 255.000000
    //     0xbc047c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbc0480: fdiv            d2, d0, d1
    // 0xbc0484: StoreField: r3->field_f = d2
    //     0xbc0484: stur            d2, [x3, #0xf]
    // 0xbc0488: ldur            x0, [fp, #-0x50]
    // 0xbc048c: ubfx            x0, x0, #0, #0x20
    // 0xbc0490: and             w1, w0, #0xff
    // 0xbc0494: ubfx            x1, x1, #0, #0x20
    // 0xbc0498: scvtf           d0, x1
    // 0xbc049c: fdiv            d2, d0, d1
    // 0xbc04a0: ArrayStore: r3[0] = d2  ; List_8
    //     0xbc04a0: stur            d2, [x3, #0x17]
    // 0xbc04a4: ldur            x0, [fp, #-0x48]
    // 0xbc04a8: ubfx            x0, x0, #0, #0x20
    // 0xbc04ac: and             w1, w0, #0xff
    // 0xbc04b0: ubfx            x1, x1, #0, #0x20
    // 0xbc04b4: scvtf           d0, x1
    // 0xbc04b8: fdiv            d2, d0, d1
    // 0xbc04bc: StoreField: r3->field_1f = d2
    //     0xbc04bc: stur            d2, [x3, #0x1f]
    // 0xbc04c0: r1 = Null
    //     0xbc04c0: mov             x1, NULL
    // 0xbc04c4: r2 = 4
    //     0xbc04c4: movz            x2, #0x4
    // 0xbc04c8: r0 = AllocateArray()
    //     0xbc04c8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc04cc: mov             x2, x0
    // 0xbc04d0: ldur            x0, [fp, #-0x30]
    // 0xbc04d4: stur            x2, [fp, #-0x38]
    // 0xbc04d8: StoreField: r2->field_f = r0
    //     0xbc04d8: stur            w0, [x2, #0xf]
    // 0xbc04dc: ldur            x0, [fp, #-0x20]
    // 0xbc04e0: StoreField: r2->field_13 = r0
    //     0xbc04e0: stur            w0, [x2, #0x13]
    // 0xbc04e4: r1 = <Color>
    //     0xbc04e4: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbc04e8: ldr             x1, [x1, #0xf80]
    // 0xbc04ec: r0 = AllocateGrowableArray()
    //     0xbc04ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc04f0: mov             x1, x0
    // 0xbc04f4: ldur            x0, [fp, #-0x38]
    // 0xbc04f8: stur            x1, [fp, #-0x20]
    // 0xbc04fc: StoreField: r1->field_f = r0
    //     0xbc04fc: stur            w0, [x1, #0xf]
    // 0xbc0500: r2 = 4
    //     0xbc0500: movz            x2, #0x4
    // 0xbc0504: StoreField: r1->field_b = r2
    //     0xbc0504: stur            w2, [x1, #0xb]
    // 0xbc0508: r0 = LinearGradient()
    //     0xbc0508: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xbc050c: mov             x1, x0
    // 0xbc0510: r0 = Instance_Alignment
    //     0xbc0510: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xbc0514: ldr             x0, [x0, #0xce0]
    // 0xbc0518: stur            x1, [fp, #-0x30]
    // 0xbc051c: StoreField: r1->field_13 = r0
    //     0xbc051c: stur            w0, [x1, #0x13]
    // 0xbc0520: r0 = Instance_Alignment
    //     0xbc0520: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xbc0524: ldr             x0, [x0, #0xce8]
    // 0xbc0528: ArrayStore: r1[0] = r0  ; List_4
    //     0xbc0528: stur            w0, [x1, #0x17]
    // 0xbc052c: r0 = Instance_TileMode
    //     0xbc052c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xbc0530: ldr             x0, [x0, #0xcf0]
    // 0xbc0534: StoreField: r1->field_1b = r0
    //     0xbc0534: stur            w0, [x1, #0x1b]
    // 0xbc0538: ldur            x0, [fp, #-0x20]
    // 0xbc053c: StoreField: r1->field_7 = r0
    //     0xbc053c: stur            w0, [x1, #7]
    // 0xbc0540: r0 = BoxDecoration()
    //     0xbc0540: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbc0544: mov             x3, x0
    // 0xbc0548: ldur            x0, [fp, #-0x30]
    // 0xbc054c: stur            x3, [fp, #-0x20]
    // 0xbc0550: StoreField: r3->field_1b = r0
    //     0xbc0550: stur            w0, [x3, #0x1b]
    // 0xbc0554: r0 = Instance_BoxShape
    //     0xbc0554: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbc0558: ldr             x0, [x0, #0x80]
    // 0xbc055c: StoreField: r3->field_23 = r0
    //     0xbc055c: stur            w0, [x3, #0x23]
    // 0xbc0560: r1 = Null
    //     0xbc0560: mov             x1, NULL
    // 0xbc0564: r2 = 6
    //     0xbc0564: movz            x2, #0x6
    // 0xbc0568: r0 = AllocateArray()
    //     0xbc0568: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc056c: r16 = "You\'ve saved "
    //     0xbc056c: add             x16, PP, #0x38, lsl #12  ; [pp+0x38cf8] "You\'ve saved "
    //     0xbc0570: ldr             x16, [x16, #0xcf8]
    // 0xbc0574: StoreField: r0->field_f = r16
    //     0xbc0574: stur            w16, [x0, #0xf]
    // 0xbc0578: ldur            x1, [fp, #-0x28]
    // 0xbc057c: LoadField: r2 = r1->field_23
    //     0xbc057c: ldur            w2, [x1, #0x23]
    // 0xbc0580: DecompressPointer r2
    //     0xbc0580: add             x2, x2, HEAP, lsl #32
    // 0xbc0584: LoadField: r1 = r2->field_b
    //     0xbc0584: ldur            w1, [x2, #0xb]
    // 0xbc0588: DecompressPointer r1
    //     0xbc0588: add             x1, x1, HEAP, lsl #32
    // 0xbc058c: cmp             w1, NULL
    // 0xbc0590: b.ne            #0xbc059c
    // 0xbc0594: r1 = Null
    //     0xbc0594: mov             x1, NULL
    // 0xbc0598: b               #0xbc05bc
    // 0xbc059c: LoadField: r2 = r1->field_33
    //     0xbc059c: ldur            w2, [x1, #0x33]
    // 0xbc05a0: DecompressPointer r2
    //     0xbc05a0: add             x2, x2, HEAP, lsl #32
    // 0xbc05a4: cmp             w2, NULL
    // 0xbc05a8: b.ne            #0xbc05b4
    // 0xbc05ac: r1 = Null
    //     0xbc05ac: mov             x1, NULL
    // 0xbc05b0: b               #0xbc05bc
    // 0xbc05b4: LoadField: r1 = r2->field_f
    //     0xbc05b4: ldur            w1, [x2, #0xf]
    // 0xbc05b8: DecompressPointer r1
    //     0xbc05b8: add             x1, x1, HEAP, lsl #32
    // 0xbc05bc: cmp             w1, NULL
    // 0xbc05c0: b.ne            #0xbc05c8
    // 0xbc05c4: r1 = ""
    //     0xbc05c4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbc05c8: ldur            x2, [fp, #-0x18]
    // 0xbc05cc: StoreField: r0->field_13 = r1
    //     0xbc05cc: stur            w1, [x0, #0x13]
    // 0xbc05d0: r16 = "!"
    //     0xbc05d0: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d00] "!"
    //     0xbc05d4: ldr             x16, [x16, #0xd00]
    // 0xbc05d8: ArrayStore: r0[0] = r16  ; List_4
    //     0xbc05d8: stur            w16, [x0, #0x17]
    // 0xbc05dc: str             x0, [SP]
    // 0xbc05e0: r0 = _interpolate()
    //     0xbc05e0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbc05e4: ldur            x2, [fp, #-0x18]
    // 0xbc05e8: stur            x0, [fp, #-0x28]
    // 0xbc05ec: LoadField: r1 = r2->field_13
    //     0xbc05ec: ldur            w1, [x2, #0x13]
    // 0xbc05f0: DecompressPointer r1
    //     0xbc05f0: add             x1, x1, HEAP, lsl #32
    // 0xbc05f4: r0 = of()
    //     0xbc05f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc05f8: LoadField: r1 = r0->field_87
    //     0xbc05f8: ldur            w1, [x0, #0x87]
    // 0xbc05fc: DecompressPointer r1
    //     0xbc05fc: add             x1, x1, HEAP, lsl #32
    // 0xbc0600: LoadField: r0 = r1->field_7
    //     0xbc0600: ldur            w0, [x1, #7]
    // 0xbc0604: DecompressPointer r0
    //     0xbc0604: add             x0, x0, HEAP, lsl #32
    // 0xbc0608: r16 = 16.000000
    //     0xbc0608: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbc060c: ldr             x16, [x16, #0x188]
    // 0xbc0610: r30 = Instance_Color
    //     0xbc0610: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbc0614: stp             lr, x16, [SP]
    // 0xbc0618: mov             x1, x0
    // 0xbc061c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbc061c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbc0620: ldr             x4, [x4, #0xaa0]
    // 0xbc0624: r0 = copyWith()
    //     0xbc0624: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc0628: stur            x0, [fp, #-0x30]
    // 0xbc062c: r0 = Text()
    //     0xbc062c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc0630: mov             x2, x0
    // 0xbc0634: ldur            x0, [fp, #-0x28]
    // 0xbc0638: stur            x2, [fp, #-0x38]
    // 0xbc063c: StoreField: r2->field_b = r0
    //     0xbc063c: stur            w0, [x2, #0xb]
    // 0xbc0640: ldur            x0, [fp, #-0x30]
    // 0xbc0644: StoreField: r2->field_13 = r0
    //     0xbc0644: stur            w0, [x2, #0x13]
    // 0xbc0648: ldur            x0, [fp, #-0x18]
    // 0xbc064c: LoadField: r1 = r0->field_13
    //     0xbc064c: ldur            w1, [x0, #0x13]
    // 0xbc0650: DecompressPointer r1
    //     0xbc0650: add             x1, x1, HEAP, lsl #32
    // 0xbc0654: r0 = of()
    //     0xbc0654: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc0658: LoadField: r1 = r0->field_87
    //     0xbc0658: ldur            w1, [x0, #0x87]
    // 0xbc065c: DecompressPointer r1
    //     0xbc065c: add             x1, x1, HEAP, lsl #32
    // 0xbc0660: LoadField: r0 = r1->field_2b
    //     0xbc0660: ldur            w0, [x1, #0x2b]
    // 0xbc0664: DecompressPointer r0
    //     0xbc0664: add             x0, x0, HEAP, lsl #32
    // 0xbc0668: r16 = 12.000000
    //     0xbc0668: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbc066c: ldr             x16, [x16, #0x9e8]
    // 0xbc0670: r30 = Instance_Color
    //     0xbc0670: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbc0674: stp             lr, x16, [SP]
    // 0xbc0678: mov             x1, x0
    // 0xbc067c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbc067c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbc0680: ldr             x4, [x4, #0xaa0]
    // 0xbc0684: r0 = copyWith()
    //     0xbc0684: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc0688: stur            x0, [fp, #-0x28]
    // 0xbc068c: r0 = Text()
    //     0xbc068c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc0690: mov             x1, x0
    // 0xbc0694: r0 = "Bumper Offer Applied!"
    //     0xbc0694: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d08] "Bumper Offer Applied!"
    //     0xbc0698: ldr             x0, [x0, #0xd08]
    // 0xbc069c: stur            x1, [fp, #-0x30]
    // 0xbc06a0: StoreField: r1->field_b = r0
    //     0xbc06a0: stur            w0, [x1, #0xb]
    // 0xbc06a4: ldur            x0, [fp, #-0x28]
    // 0xbc06a8: StoreField: r1->field_13 = r0
    //     0xbc06a8: stur            w0, [x1, #0x13]
    // 0xbc06ac: r0 = Padding()
    //     0xbc06ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc06b0: r1 = Instance_EdgeInsets
    //     0xbc06b0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xbc06b4: ldr             x1, [x1, #0x990]
    // 0xbc06b8: stur            x0, [fp, #-0x28]
    // 0xbc06bc: StoreField: r0->field_f = r1
    //     0xbc06bc: stur            w1, [x0, #0xf]
    // 0xbc06c0: ldur            x1, [fp, #-0x30]
    // 0xbc06c4: StoreField: r0->field_b = r1
    //     0xbc06c4: stur            w1, [x0, #0xb]
    // 0xbc06c8: r1 = Null
    //     0xbc06c8: mov             x1, NULL
    // 0xbc06cc: r2 = 4
    //     0xbc06cc: movz            x2, #0x4
    // 0xbc06d0: r0 = AllocateArray()
    //     0xbc06d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc06d4: mov             x2, x0
    // 0xbc06d8: ldur            x0, [fp, #-0x38]
    // 0xbc06dc: stur            x2, [fp, #-0x30]
    // 0xbc06e0: StoreField: r2->field_f = r0
    //     0xbc06e0: stur            w0, [x2, #0xf]
    // 0xbc06e4: ldur            x0, [fp, #-0x28]
    // 0xbc06e8: StoreField: r2->field_13 = r0
    //     0xbc06e8: stur            w0, [x2, #0x13]
    // 0xbc06ec: r1 = <Widget>
    //     0xbc06ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc06f0: r0 = AllocateGrowableArray()
    //     0xbc06f0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc06f4: mov             x1, x0
    // 0xbc06f8: ldur            x0, [fp, #-0x30]
    // 0xbc06fc: stur            x1, [fp, #-0x28]
    // 0xbc0700: StoreField: r1->field_f = r0
    //     0xbc0700: stur            w0, [x1, #0xf]
    // 0xbc0704: r2 = 4
    //     0xbc0704: movz            x2, #0x4
    // 0xbc0708: StoreField: r1->field_b = r2
    //     0xbc0708: stur            w2, [x1, #0xb]
    // 0xbc070c: r0 = Column()
    //     0xbc070c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbc0710: r3 = Instance_Axis
    //     0xbc0710: ldr             x3, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbc0714: stur            x0, [fp, #-0x30]
    // 0xbc0718: StoreField: r0->field_f = r3
    //     0xbc0718: stur            w3, [x0, #0xf]
    // 0xbc071c: r4 = Instance_MainAxisAlignment
    //     0xbc071c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbc0720: ldr             x4, [x4, #0xab0]
    // 0xbc0724: StoreField: r0->field_13 = r4
    //     0xbc0724: stur            w4, [x0, #0x13]
    // 0xbc0728: r1 = Instance_MainAxisSize
    //     0xbc0728: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc072c: ldr             x1, [x1, #0xa10]
    // 0xbc0730: ArrayStore: r0[0] = r1  ; List_4
    //     0xbc0730: stur            w1, [x0, #0x17]
    // 0xbc0734: r5 = Instance_CrossAxisAlignment
    //     0xbc0734: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbc0738: ldr             x5, [x5, #0x890]
    // 0xbc073c: StoreField: r0->field_1b = r5
    //     0xbc073c: stur            w5, [x0, #0x1b]
    // 0xbc0740: r2 = Instance_VerticalDirection
    //     0xbc0740: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc0744: ldr             x2, [x2, #0xa20]
    // 0xbc0748: StoreField: r0->field_23 = r2
    //     0xbc0748: stur            w2, [x0, #0x23]
    // 0xbc074c: r3 = Instance_Clip
    //     0xbc074c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc0750: ldr             x3, [x3, #0x38]
    // 0xbc0754: StoreField: r0->field_2b = r3
    //     0xbc0754: stur            w3, [x0, #0x2b]
    // 0xbc0758: StoreField: r0->field_2f = rZR
    //     0xbc0758: stur            xzr, [x0, #0x2f]
    // 0xbc075c: ldur            x4, [fp, #-0x28]
    // 0xbc0760: StoreField: r0->field_b = r4
    //     0xbc0760: stur            w4, [x0, #0xb]
    // 0xbc0764: r0 = Padding()
    //     0xbc0764: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc0768: mov             x2, x0
    // 0xbc076c: r0 = Instance_EdgeInsets
    //     0xbc076c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cb0] Obj!EdgeInsets@d57bf1
    //     0xbc0770: ldr             x0, [x0, #0xcb0]
    // 0xbc0774: stur            x2, [fp, #-0x28]
    // 0xbc0778: StoreField: r2->field_f = r0
    //     0xbc0778: stur            w0, [x2, #0xf]
    // 0xbc077c: ldur            x0, [fp, #-0x30]
    // 0xbc0780: StoreField: r2->field_b = r0
    //     0xbc0780: stur            w0, [x2, #0xb]
    // 0xbc0784: r1 = <FlexParentData>
    //     0xbc0784: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbc0788: ldr             x1, [x1, #0xe00]
    // 0xbc078c: r0 = Expanded()
    //     0xbc078c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbc0790: stur            x0, [fp, #-0x30]
    // 0xbc0794: StoreField: r0->field_13 = rZR
    //     0xbc0794: stur            xzr, [x0, #0x13]
    // 0xbc0798: r6 = Instance_FlexFit
    //     0xbc0798: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbc079c: ldr             x6, [x6, #0xe08]
    // 0xbc07a0: StoreField: r0->field_1b = r6
    //     0xbc07a0: stur            w6, [x0, #0x1b]
    // 0xbc07a4: ldur            x1, [fp, #-0x28]
    // 0xbc07a8: StoreField: r0->field_b = r1
    //     0xbc07a8: stur            w1, [x0, #0xb]
    // 0xbc07ac: ldur            x2, [fp, #-0x18]
    // 0xbc07b0: LoadField: r1 = r2->field_13
    //     0xbc07b0: ldur            w1, [x2, #0x13]
    // 0xbc07b4: DecompressPointer r1
    //     0xbc07b4: add             x1, x1, HEAP, lsl #32
    // 0xbc07b8: r0 = of()
    //     0xbc07b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc07bc: LoadField: r1 = r0->field_87
    //     0xbc07bc: ldur            w1, [x0, #0x87]
    // 0xbc07c0: DecompressPointer r1
    //     0xbc07c0: add             x1, x1, HEAP, lsl #32
    // 0xbc07c4: LoadField: r0 = r1->field_7
    //     0xbc07c4: ldur            w0, [x1, #7]
    // 0xbc07c8: DecompressPointer r0
    //     0xbc07c8: add             x0, x0, HEAP, lsl #32
    // 0xbc07cc: r16 = 14.000000
    //     0xbc07cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbc07d0: ldr             x16, [x16, #0x1d8]
    // 0xbc07d4: r30 = Instance_Color
    //     0xbc07d4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbc07d8: stp             lr, x16, [SP]
    // 0xbc07dc: mov             x1, x0
    // 0xbc07e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbc07e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbc07e4: ldr             x4, [x4, #0xaa0]
    // 0xbc07e8: r0 = copyWith()
    //     0xbc07e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc07ec: stur            x0, [fp, #-0x28]
    // 0xbc07f0: r0 = Text()
    //     0xbc07f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc07f4: r7 = "CHANGE"
    //     0xbc07f4: add             x7, PP, #0x38, lsl #12  ; [pp+0x38d10] "CHANGE"
    //     0xbc07f8: ldr             x7, [x7, #0xd10]
    // 0xbc07fc: stur            x0, [fp, #-0x38]
    // 0xbc0800: StoreField: r0->field_b = r7
    //     0xbc0800: stur            w7, [x0, #0xb]
    // 0xbc0804: ldur            x1, [fp, #-0x28]
    // 0xbc0808: StoreField: r0->field_13 = r1
    //     0xbc0808: stur            w1, [x0, #0x13]
    // 0xbc080c: r0 = Padding()
    //     0xbc080c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc0810: mov             x1, x0
    // 0xbc0814: r0 = Instance_EdgeInsets
    //     0xbc0814: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0xbc0818: ldr             x0, [x0, #0xf98]
    // 0xbc081c: stur            x1, [fp, #-0x28]
    // 0xbc0820: StoreField: r1->field_f = r0
    //     0xbc0820: stur            w0, [x1, #0xf]
    // 0xbc0824: ldur            x0, [fp, #-0x38]
    // 0xbc0828: StoreField: r1->field_b = r0
    //     0xbc0828: stur            w0, [x1, #0xb]
    // 0xbc082c: r0 = InkWell()
    //     0xbc082c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbc0830: mov             x3, x0
    // 0xbc0834: ldur            x0, [fp, #-0x28]
    // 0xbc0838: stur            x3, [fp, #-0x38]
    // 0xbc083c: StoreField: r3->field_b = r0
    //     0xbc083c: stur            w0, [x3, #0xb]
    // 0xbc0840: ldur            x2, [fp, #-0x18]
    // 0xbc0844: r1 = Function '<anonymous closure>':.
    //     0xbc0844: add             x1, PP, #0x54, lsl #12  ; [pp+0x54448] AnonymousClosure: (0xbc14fc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xbbf99c)
    //     0xbc0848: ldr             x1, [x1, #0x448]
    // 0xbc084c: r0 = AllocateClosure()
    //     0xbc084c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc0850: mov             x1, x0
    // 0xbc0854: ldur            x0, [fp, #-0x38]
    // 0xbc0858: StoreField: r0->field_f = r1
    //     0xbc0858: stur            w1, [x0, #0xf]
    // 0xbc085c: r3 = true
    //     0xbc085c: add             x3, NULL, #0x20  ; true
    // 0xbc0860: StoreField: r0->field_43 = r3
    //     0xbc0860: stur            w3, [x0, #0x43]
    // 0xbc0864: r4 = Instance_BoxShape
    //     0xbc0864: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbc0868: ldr             x4, [x4, #0x80]
    // 0xbc086c: StoreField: r0->field_47 = r4
    //     0xbc086c: stur            w4, [x0, #0x47]
    // 0xbc0870: StoreField: r0->field_6f = r3
    //     0xbc0870: stur            w3, [x0, #0x6f]
    // 0xbc0874: r5 = false
    //     0xbc0874: add             x5, NULL, #0x30  ; false
    // 0xbc0878: StoreField: r0->field_73 = r5
    //     0xbc0878: stur            w5, [x0, #0x73]
    // 0xbc087c: StoreField: r0->field_83 = r3
    //     0xbc087c: stur            w3, [x0, #0x83]
    // 0xbc0880: StoreField: r0->field_7b = r5
    //     0xbc0880: stur            w5, [x0, #0x7b]
    // 0xbc0884: r1 = Null
    //     0xbc0884: mov             x1, NULL
    // 0xbc0888: r2 = 8
    //     0xbc0888: movz            x2, #0x8
    // 0xbc088c: r0 = AllocateArray()
    //     0xbc088c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc0890: stur            x0, [fp, #-0x28]
    // 0xbc0894: r16 = Instance_Padding
    //     0xbc0894: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d20] Obj!Padding@d68521
    //     0xbc0898: ldr             x16, [x16, #0xd20]
    // 0xbc089c: StoreField: r0->field_f = r16
    //     0xbc089c: stur            w16, [x0, #0xf]
    // 0xbc08a0: ldur            x1, [fp, #-0x30]
    // 0xbc08a4: StoreField: r0->field_13 = r1
    //     0xbc08a4: stur            w1, [x0, #0x13]
    // 0xbc08a8: r16 = Instance_Spacer
    //     0xbc08a8: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbc08ac: ldr             x16, [x16, #0xf0]
    // 0xbc08b0: ArrayStore: r0[0] = r16  ; List_4
    //     0xbc08b0: stur            w16, [x0, #0x17]
    // 0xbc08b4: ldur            x1, [fp, #-0x38]
    // 0xbc08b8: StoreField: r0->field_1b = r1
    //     0xbc08b8: stur            w1, [x0, #0x1b]
    // 0xbc08bc: r1 = <Widget>
    //     0xbc08bc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc08c0: r0 = AllocateGrowableArray()
    //     0xbc08c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc08c4: mov             x1, x0
    // 0xbc08c8: ldur            x0, [fp, #-0x28]
    // 0xbc08cc: stur            x1, [fp, #-0x30]
    // 0xbc08d0: StoreField: r1->field_f = r0
    //     0xbc08d0: stur            w0, [x1, #0xf]
    // 0xbc08d4: r0 = 8
    //     0xbc08d4: movz            x0, #0x8
    // 0xbc08d8: StoreField: r1->field_b = r0
    //     0xbc08d8: stur            w0, [x1, #0xb]
    // 0xbc08dc: r0 = Row()
    //     0xbc08dc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbc08e0: r8 = Instance_Axis
    //     0xbc08e0: ldr             x8, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc08e4: stur            x0, [fp, #-0x28]
    // 0xbc08e8: StoreField: r0->field_f = r8
    //     0xbc08e8: stur            w8, [x0, #0xf]
    // 0xbc08ec: r1 = Instance_MainAxisAlignment
    //     0xbc08ec: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xbc08f0: ldr             x1, [x1, #0xd10]
    // 0xbc08f4: StoreField: r0->field_13 = r1
    //     0xbc08f4: stur            w1, [x0, #0x13]
    // 0xbc08f8: r9 = Instance_MainAxisSize
    //     0xbc08f8: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc08fc: ldr             x9, [x9, #0xa10]
    // 0xbc0900: ArrayStore: r0[0] = r9  ; List_4
    //     0xbc0900: stur            w9, [x0, #0x17]
    // 0xbc0904: r10 = Instance_CrossAxisAlignment
    //     0xbc0904: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc0908: ldr             x10, [x10, #0xa18]
    // 0xbc090c: StoreField: r0->field_1b = r10
    //     0xbc090c: stur            w10, [x0, #0x1b]
    // 0xbc0910: r11 = Instance_VerticalDirection
    //     0xbc0910: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc0914: ldr             x11, [x11, #0xa20]
    // 0xbc0918: StoreField: r0->field_23 = r11
    //     0xbc0918: stur            w11, [x0, #0x23]
    // 0xbc091c: r12 = Instance_Clip
    //     0xbc091c: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc0920: ldr             x12, [x12, #0x38]
    // 0xbc0924: StoreField: r0->field_2b = r12
    //     0xbc0924: stur            w12, [x0, #0x2b]
    // 0xbc0928: StoreField: r0->field_2f = rZR
    //     0xbc0928: stur            xzr, [x0, #0x2f]
    // 0xbc092c: ldur            x1, [fp, #-0x30]
    // 0xbc0930: StoreField: r0->field_b = r1
    //     0xbc0930: stur            w1, [x0, #0xb]
    // 0xbc0934: r0 = Container()
    //     0xbc0934: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbc0938: stur            x0, [fp, #-0x30]
    // 0xbc093c: r16 = 65.000000
    //     0xbc093c: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d28] 65
    //     0xbc0940: ldr             x16, [x16, #0xd28]
    // 0xbc0944: ldur            lr, [fp, #-0x20]
    // 0xbc0948: stp             lr, x16, [SP, #8]
    // 0xbc094c: ldur            x16, [fp, #-0x28]
    // 0xbc0950: str             x16, [SP]
    // 0xbc0954: mov             x1, x0
    // 0xbc0958: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xbc0958: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xbc095c: ldr             x4, [x4, #0xc78]
    // 0xbc0960: r0 = Container()
    //     0xbc0960: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbc0964: r1 = <Path>
    //     0xbc0964: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xbc0968: ldr             x1, [x1, #0xd30]
    // 0xbc096c: r0 = MovieTicketClipper()
    //     0xbc096c: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0xbc0970: stur            x0, [fp, #-0x20]
    // 0xbc0974: r0 = ClipPath()
    //     0xbc0974: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xbc0978: mov             x1, x0
    // 0xbc097c: ldur            x0, [fp, #-0x20]
    // 0xbc0980: stur            x1, [fp, #-0x28]
    // 0xbc0984: StoreField: r1->field_f = r0
    //     0xbc0984: stur            w0, [x1, #0xf]
    // 0xbc0988: r0 = Instance_Clip
    //     0xbc0988: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xbc098c: ldr             x0, [x0, #0x138]
    // 0xbc0990: StoreField: r1->field_13 = r0
    //     0xbc0990: stur            w0, [x1, #0x13]
    // 0xbc0994: ldur            x0, [fp, #-0x30]
    // 0xbc0998: StoreField: r1->field_b = r0
    //     0xbc0998: stur            w0, [x1, #0xb]
    // 0xbc099c: r0 = Padding()
    //     0xbc099c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc09a0: r13 = Instance_EdgeInsets
    //     0xbc09a0: add             x13, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0xbc09a4: ldr             x13, [x13, #0xa98]
    // 0xbc09a8: StoreField: r0->field_f = r13
    //     0xbc09a8: stur            w13, [x0, #0xf]
    // 0xbc09ac: ldur            x1, [fp, #-0x28]
    // 0xbc09b0: StoreField: r0->field_b = r1
    //     0xbc09b0: stur            w1, [x0, #0xb]
    // 0xbc09b4: b               #0xbc0f50
    // 0xbc09b8: ldur            x0, [fp, #-8]
    // 0xbc09bc: r2 = 4
    //     0xbc09bc: movz            x2, #0x4
    // 0xbc09c0: r1 = Instance_EdgeInsets
    //     0xbc09c0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xbc09c4: ldr             x1, [x1, #0x990]
    // 0xbc09c8: r4 = Instance_MainAxisAlignment
    //     0xbc09c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbc09cc: ldr             x4, [x4, #0xab0]
    // 0xbc09d0: r5 = Instance_CrossAxisAlignment
    //     0xbc09d0: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbc09d4: ldr             x5, [x5, #0x890]
    // 0xbc09d8: r7 = "CHANGE"
    //     0xbc09d8: add             x7, PP, #0x38, lsl #12  ; [pp+0x38d10] "CHANGE"
    //     0xbc09dc: ldr             x7, [x7, #0xd10]
    // 0xbc09e0: r13 = Instance_EdgeInsets
    //     0xbc09e0: add             x13, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0xbc09e4: ldr             x13, [x13, #0xa98]
    // 0xbc09e8: r9 = Instance_MainAxisSize
    //     0xbc09e8: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc09ec: ldr             x9, [x9, #0xa10]
    // 0xbc09f0: r11 = Instance_VerticalDirection
    //     0xbc09f0: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc09f4: ldr             x11, [x11, #0xa20]
    // 0xbc09f8: r3 = Instance_Axis
    //     0xbc09f8: ldr             x3, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbc09fc: r6 = Instance_FlexFit
    //     0xbc09fc: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbc0a00: ldr             x6, [x6, #0xe08]
    // 0xbc0a04: r10 = Instance_CrossAxisAlignment
    //     0xbc0a04: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc0a08: ldr             x10, [x10, #0xa18]
    // 0xbc0a0c: r8 = Instance_Axis
    //     0xbc0a0c: ldr             x8, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc0a10: r12 = Instance_Clip
    //     0xbc0a10: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc0a14: ldr             x12, [x12, #0x38]
    // 0xbc0a18: LoadField: r14 = r0->field_b
    //     0xbc0a18: ldur            w14, [x0, #0xb]
    // 0xbc0a1c: DecompressPointer r14
    //     0xbc0a1c: add             x14, x14, HEAP, lsl #32
    // 0xbc0a20: cmp             w14, NULL
    // 0xbc0a24: b.eq            #0xbc1020
    // 0xbc0a28: LoadField: r19 = r14->field_23
    //     0xbc0a28: ldur            w19, [x14, #0x23]
    // 0xbc0a2c: DecompressPointer r19
    //     0xbc0a2c: add             x19, x19, HEAP, lsl #32
    // 0xbc0a30: LoadField: r14 = r19->field_b
    //     0xbc0a30: ldur            w14, [x19, #0xb]
    // 0xbc0a34: DecompressPointer r14
    //     0xbc0a34: add             x14, x14, HEAP, lsl #32
    // 0xbc0a38: cmp             w14, NULL
    // 0xbc0a3c: b.ne            #0xbc0a48
    // 0xbc0a40: r14 = Null
    //     0xbc0a40: mov             x14, NULL
    // 0xbc0a44: b               #0xbc0a68
    // 0xbc0a48: LoadField: r19 = r14->field_33
    //     0xbc0a48: ldur            w19, [x14, #0x33]
    // 0xbc0a4c: DecompressPointer r19
    //     0xbc0a4c: add             x19, x19, HEAP, lsl #32
    // 0xbc0a50: cmp             w19, NULL
    // 0xbc0a54: b.ne            #0xbc0a60
    // 0xbc0a58: r14 = Null
    //     0xbc0a58: mov             x14, NULL
    // 0xbc0a5c: b               #0xbc0a68
    // 0xbc0a60: ArrayLoad: r14 = r19[0]  ; List_4
    //     0xbc0a60: ldur            w14, [x19, #0x17]
    // 0xbc0a64: DecompressPointer r14
    //     0xbc0a64: add             x14, x14, HEAP, lsl #32
    // 0xbc0a68: cmp             w14, NULL
    // 0xbc0a6c: b.eq            #0xbc0aa8
    // 0xbc0a70: tbnz            w14, #4, #0xbc0aa8
    // 0xbc0a74: r0 = SvgPicture()
    //     0xbc0a74: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbc0a78: stur            x0, [fp, #-0x20]
    // 0xbc0a7c: r16 = 30.000000
    //     0xbc0a7c: add             x16, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xbc0a80: ldr             x16, [x16, #0x768]
    // 0xbc0a84: str             x16, [SP]
    // 0xbc0a88: mov             x1, x0
    // 0xbc0a8c: r2 = "assets/images/gift-icon-popup.svg"
    //     0xbc0a8c: add             x2, PP, #0x52, lsl #12  ; [pp+0x528e8] "assets/images/gift-icon-popup.svg"
    //     0xbc0a90: ldr             x2, [x2, #0x8e8]
    // 0xbc0a94: r4 = const [0, 0x3, 0x1, 0x2, height, 0x2, null]
    //     0xbc0a94: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c760] List(7) [0, 0x3, 0x1, 0x2, "height", 0x2, Null]
    //     0xbc0a98: ldr             x4, [x4, #0x760]
    // 0xbc0a9c: r0 = SvgPicture.asset()
    //     0xbc0a9c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbc0aa0: ldur            x1, [fp, #-0x20]
    // 0xbc0aa4: b               #0xbc0ab0
    // 0xbc0aa8: r1 = Instance_Icon
    //     0xbc0aa8: add             x1, PP, #0x46, lsl #12  ; [pp+0x46568] Obj!Icon@d662f1
    //     0xbc0aac: ldr             x1, [x1, #0x568]
    // 0xbc0ab0: ldur            x0, [fp, #-8]
    // 0xbc0ab4: stur            x1, [fp, #-0x20]
    // 0xbc0ab8: r0 = Padding()
    //     0xbc0ab8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc0abc: mov             x3, x0
    // 0xbc0ac0: r0 = Instance_EdgeInsets
    //     0xbc0ac0: add             x0, PP, #0x54, lsl #12  ; [pp+0x54450] Obj!EdgeInsets@d595a1
    //     0xbc0ac4: ldr             x0, [x0, #0x450]
    // 0xbc0ac8: stur            x3, [fp, #-0x28]
    // 0xbc0acc: StoreField: r3->field_f = r0
    //     0xbc0acc: stur            w0, [x3, #0xf]
    // 0xbc0ad0: ldur            x0, [fp, #-0x20]
    // 0xbc0ad4: StoreField: r3->field_b = r0
    //     0xbc0ad4: stur            w0, [x3, #0xb]
    // 0xbc0ad8: ldur            x0, [fp, #-8]
    // 0xbc0adc: LoadField: r1 = r0->field_b
    //     0xbc0adc: ldur            w1, [x0, #0xb]
    // 0xbc0ae0: DecompressPointer r1
    //     0xbc0ae0: add             x1, x1, HEAP, lsl #32
    // 0xbc0ae4: cmp             w1, NULL
    // 0xbc0ae8: b.eq            #0xbc1024
    // 0xbc0aec: LoadField: r0 = r1->field_23
    //     0xbc0aec: ldur            w0, [x1, #0x23]
    // 0xbc0af0: DecompressPointer r0
    //     0xbc0af0: add             x0, x0, HEAP, lsl #32
    // 0xbc0af4: LoadField: r4 = r0->field_b
    //     0xbc0af4: ldur            w4, [x0, #0xb]
    // 0xbc0af8: DecompressPointer r4
    //     0xbc0af8: add             x4, x4, HEAP, lsl #32
    // 0xbc0afc: stur            x4, [fp, #-8]
    // 0xbc0b00: cmp             w4, NULL
    // 0xbc0b04: b.ne            #0xbc0b10
    // 0xbc0b08: r0 = Null
    //     0xbc0b08: mov             x0, NULL
    // 0xbc0b0c: b               #0xbc0b34
    // 0xbc0b10: LoadField: r0 = r4->field_33
    //     0xbc0b10: ldur            w0, [x4, #0x33]
    // 0xbc0b14: DecompressPointer r0
    //     0xbc0b14: add             x0, x0, HEAP, lsl #32
    // 0xbc0b18: cmp             w0, NULL
    // 0xbc0b1c: b.ne            #0xbc0b28
    // 0xbc0b20: r0 = Null
    //     0xbc0b20: mov             x0, NULL
    // 0xbc0b24: b               #0xbc0b34
    // 0xbc0b28: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbc0b28: ldur            w1, [x0, #0x17]
    // 0xbc0b2c: DecompressPointer r1
    //     0xbc0b2c: add             x1, x1, HEAP, lsl #32
    // 0xbc0b30: mov             x0, x1
    // 0xbc0b34: cmp             w0, NULL
    // 0xbc0b38: b.eq            #0xbc0b50
    // 0xbc0b3c: tbnz            w0, #4, #0xbc0b50
    // 0xbc0b40: mov             x0, x3
    // 0xbc0b44: r3 = "Free Gift Added!"
    //     0xbc0b44: add             x3, PP, #0x54, lsl #12  ; [pp+0x54428] "Free Gift Added!"
    //     0xbc0b48: ldr             x3, [x3, #0x428]
    // 0xbc0b4c: b               #0xbc0bc8
    // 0xbc0b50: r1 = Null
    //     0xbc0b50: mov             x1, NULL
    // 0xbc0b54: r2 = 6
    //     0xbc0b54: movz            x2, #0x6
    // 0xbc0b58: r0 = AllocateArray()
    //     0xbc0b58: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc0b5c: r16 = "You\'ve saved "
    //     0xbc0b5c: add             x16, PP, #0x38, lsl #12  ; [pp+0x38cf8] "You\'ve saved "
    //     0xbc0b60: ldr             x16, [x16, #0xcf8]
    // 0xbc0b64: StoreField: r0->field_f = r16
    //     0xbc0b64: stur            w16, [x0, #0xf]
    // 0xbc0b68: ldur            x1, [fp, #-8]
    // 0xbc0b6c: cmp             w1, NULL
    // 0xbc0b70: b.ne            #0xbc0b7c
    // 0xbc0b74: r1 = Null
    //     0xbc0b74: mov             x1, NULL
    // 0xbc0b78: b               #0xbc0b9c
    // 0xbc0b7c: LoadField: r2 = r1->field_33
    //     0xbc0b7c: ldur            w2, [x1, #0x33]
    // 0xbc0b80: DecompressPointer r2
    //     0xbc0b80: add             x2, x2, HEAP, lsl #32
    // 0xbc0b84: cmp             w2, NULL
    // 0xbc0b88: b.ne            #0xbc0b94
    // 0xbc0b8c: r1 = Null
    //     0xbc0b8c: mov             x1, NULL
    // 0xbc0b90: b               #0xbc0b9c
    // 0xbc0b94: LoadField: r1 = r2->field_f
    //     0xbc0b94: ldur            w1, [x2, #0xf]
    // 0xbc0b98: DecompressPointer r1
    //     0xbc0b98: add             x1, x1, HEAP, lsl #32
    // 0xbc0b9c: cmp             w1, NULL
    // 0xbc0ba0: b.ne            #0xbc0ba8
    // 0xbc0ba4: r1 = ""
    //     0xbc0ba4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbc0ba8: StoreField: r0->field_13 = r1
    //     0xbc0ba8: stur            w1, [x0, #0x13]
    // 0xbc0bac: r16 = "!"
    //     0xbc0bac: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d00] "!"
    //     0xbc0bb0: ldr             x16, [x16, #0xd00]
    // 0xbc0bb4: ArrayStore: r0[0] = r16  ; List_4
    //     0xbc0bb4: stur            w16, [x0, #0x17]
    // 0xbc0bb8: str             x0, [SP]
    // 0xbc0bbc: r0 = _interpolate()
    //     0xbc0bbc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbc0bc0: mov             x3, x0
    // 0xbc0bc4: ldur            x0, [fp, #-0x28]
    // 0xbc0bc8: ldur            x2, [fp, #-0x18]
    // 0xbc0bcc: stur            x3, [fp, #-8]
    // 0xbc0bd0: LoadField: r1 = r2->field_13
    //     0xbc0bd0: ldur            w1, [x2, #0x13]
    // 0xbc0bd4: DecompressPointer r1
    //     0xbc0bd4: add             x1, x1, HEAP, lsl #32
    // 0xbc0bd8: r0 = of()
    //     0xbc0bd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc0bdc: LoadField: r1 = r0->field_87
    //     0xbc0bdc: ldur            w1, [x0, #0x87]
    // 0xbc0be0: DecompressPointer r1
    //     0xbc0be0: add             x1, x1, HEAP, lsl #32
    // 0xbc0be4: LoadField: r0 = r1->field_7
    //     0xbc0be4: ldur            w0, [x1, #7]
    // 0xbc0be8: DecompressPointer r0
    //     0xbc0be8: add             x0, x0, HEAP, lsl #32
    // 0xbc0bec: r16 = Instance_Color
    //     0xbc0bec: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc0bf0: r30 = 12.000000
    //     0xbc0bf0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbc0bf4: ldr             lr, [lr, #0x9e8]
    // 0xbc0bf8: stp             lr, x16, [SP]
    // 0xbc0bfc: mov             x1, x0
    // 0xbc0c00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc0c00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc0c04: ldr             x4, [x4, #0x9b8]
    // 0xbc0c08: r0 = copyWith()
    //     0xbc0c08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc0c0c: stur            x0, [fp, #-0x20]
    // 0xbc0c10: r0 = Text()
    //     0xbc0c10: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc0c14: mov             x2, x0
    // 0xbc0c18: ldur            x0, [fp, #-8]
    // 0xbc0c1c: stur            x2, [fp, #-0x30]
    // 0xbc0c20: StoreField: r2->field_b = r0
    //     0xbc0c20: stur            w0, [x2, #0xb]
    // 0xbc0c24: ldur            x0, [fp, #-0x20]
    // 0xbc0c28: StoreField: r2->field_13 = r0
    //     0xbc0c28: stur            w0, [x2, #0x13]
    // 0xbc0c2c: ldur            x0, [fp, #-0x18]
    // 0xbc0c30: LoadField: r1 = r0->field_13
    //     0xbc0c30: ldur            w1, [x0, #0x13]
    // 0xbc0c34: DecompressPointer r1
    //     0xbc0c34: add             x1, x1, HEAP, lsl #32
    // 0xbc0c38: r0 = of()
    //     0xbc0c38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc0c3c: LoadField: r1 = r0->field_87
    //     0xbc0c3c: ldur            w1, [x0, #0x87]
    // 0xbc0c40: DecompressPointer r1
    //     0xbc0c40: add             x1, x1, HEAP, lsl #32
    // 0xbc0c44: LoadField: r0 = r1->field_2b
    //     0xbc0c44: ldur            w0, [x1, #0x2b]
    // 0xbc0c48: DecompressPointer r0
    //     0xbc0c48: add             x0, x0, HEAP, lsl #32
    // 0xbc0c4c: r16 = 12.000000
    //     0xbc0c4c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbc0c50: ldr             x16, [x16, #0x9e8]
    // 0xbc0c54: r30 = Instance_Color
    //     0xbc0c54: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbc0c58: ldr             lr, [lr, #0x858]
    // 0xbc0c5c: stp             lr, x16, [SP]
    // 0xbc0c60: mov             x1, x0
    // 0xbc0c64: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbc0c64: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbc0c68: ldr             x4, [x4, #0xaa0]
    // 0xbc0c6c: r0 = copyWith()
    //     0xbc0c6c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc0c70: stur            x0, [fp, #-8]
    // 0xbc0c74: r0 = Text()
    //     0xbc0c74: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc0c78: mov             x1, x0
    // 0xbc0c7c: r0 = "Offer Applied!"
    //     0xbc0c7c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d38] "Offer Applied!"
    //     0xbc0c80: ldr             x0, [x0, #0xd38]
    // 0xbc0c84: stur            x1, [fp, #-0x20]
    // 0xbc0c88: StoreField: r1->field_b = r0
    //     0xbc0c88: stur            w0, [x1, #0xb]
    // 0xbc0c8c: ldur            x0, [fp, #-8]
    // 0xbc0c90: StoreField: r1->field_13 = r0
    //     0xbc0c90: stur            w0, [x1, #0x13]
    // 0xbc0c94: r0 = Padding()
    //     0xbc0c94: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc0c98: mov             x3, x0
    // 0xbc0c9c: r0 = Instance_EdgeInsets
    //     0xbc0c9c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xbc0ca0: ldr             x0, [x0, #0x990]
    // 0xbc0ca4: stur            x3, [fp, #-8]
    // 0xbc0ca8: StoreField: r3->field_f = r0
    //     0xbc0ca8: stur            w0, [x3, #0xf]
    // 0xbc0cac: ldur            x0, [fp, #-0x20]
    // 0xbc0cb0: StoreField: r3->field_b = r0
    //     0xbc0cb0: stur            w0, [x3, #0xb]
    // 0xbc0cb4: r1 = Null
    //     0xbc0cb4: mov             x1, NULL
    // 0xbc0cb8: r2 = 4
    //     0xbc0cb8: movz            x2, #0x4
    // 0xbc0cbc: r0 = AllocateArray()
    //     0xbc0cbc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc0cc0: mov             x2, x0
    // 0xbc0cc4: ldur            x0, [fp, #-0x30]
    // 0xbc0cc8: stur            x2, [fp, #-0x20]
    // 0xbc0ccc: StoreField: r2->field_f = r0
    //     0xbc0ccc: stur            w0, [x2, #0xf]
    // 0xbc0cd0: ldur            x0, [fp, #-8]
    // 0xbc0cd4: StoreField: r2->field_13 = r0
    //     0xbc0cd4: stur            w0, [x2, #0x13]
    // 0xbc0cd8: r1 = <Widget>
    //     0xbc0cd8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc0cdc: r0 = AllocateGrowableArray()
    //     0xbc0cdc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc0ce0: mov             x1, x0
    // 0xbc0ce4: ldur            x0, [fp, #-0x20]
    // 0xbc0ce8: stur            x1, [fp, #-8]
    // 0xbc0cec: StoreField: r1->field_f = r0
    //     0xbc0cec: stur            w0, [x1, #0xf]
    // 0xbc0cf0: r0 = 4
    //     0xbc0cf0: movz            x0, #0x4
    // 0xbc0cf4: StoreField: r1->field_b = r0
    //     0xbc0cf4: stur            w0, [x1, #0xb]
    // 0xbc0cf8: r0 = Column()
    //     0xbc0cf8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbc0cfc: mov             x2, x0
    // 0xbc0d00: r0 = Instance_Axis
    //     0xbc0d00: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbc0d04: stur            x2, [fp, #-0x20]
    // 0xbc0d08: StoreField: r2->field_f = r0
    //     0xbc0d08: stur            w0, [x2, #0xf]
    // 0xbc0d0c: r0 = Instance_MainAxisAlignment
    //     0xbc0d0c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbc0d10: ldr             x0, [x0, #0xab0]
    // 0xbc0d14: StoreField: r2->field_13 = r0
    //     0xbc0d14: stur            w0, [x2, #0x13]
    // 0xbc0d18: r0 = Instance_MainAxisSize
    //     0xbc0d18: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc0d1c: ldr             x0, [x0, #0xa10]
    // 0xbc0d20: ArrayStore: r2[0] = r0  ; List_4
    //     0xbc0d20: stur            w0, [x2, #0x17]
    // 0xbc0d24: r1 = Instance_CrossAxisAlignment
    //     0xbc0d24: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbc0d28: ldr             x1, [x1, #0x890]
    // 0xbc0d2c: StoreField: r2->field_1b = r1
    //     0xbc0d2c: stur            w1, [x2, #0x1b]
    // 0xbc0d30: r3 = Instance_VerticalDirection
    //     0xbc0d30: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc0d34: ldr             x3, [x3, #0xa20]
    // 0xbc0d38: StoreField: r2->field_23 = r3
    //     0xbc0d38: stur            w3, [x2, #0x23]
    // 0xbc0d3c: r4 = Instance_Clip
    //     0xbc0d3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc0d40: ldr             x4, [x4, #0x38]
    // 0xbc0d44: StoreField: r2->field_2b = r4
    //     0xbc0d44: stur            w4, [x2, #0x2b]
    // 0xbc0d48: StoreField: r2->field_2f = rZR
    //     0xbc0d48: stur            xzr, [x2, #0x2f]
    // 0xbc0d4c: ldur            x1, [fp, #-8]
    // 0xbc0d50: StoreField: r2->field_b = r1
    //     0xbc0d50: stur            w1, [x2, #0xb]
    // 0xbc0d54: r1 = <FlexParentData>
    //     0xbc0d54: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbc0d58: ldr             x1, [x1, #0xe00]
    // 0xbc0d5c: r0 = Expanded()
    //     0xbc0d5c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbc0d60: stur            x0, [fp, #-8]
    // 0xbc0d64: StoreField: r0->field_13 = rZR
    //     0xbc0d64: stur            xzr, [x0, #0x13]
    // 0xbc0d68: r1 = Instance_FlexFit
    //     0xbc0d68: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbc0d6c: ldr             x1, [x1, #0xe08]
    // 0xbc0d70: StoreField: r0->field_1b = r1
    //     0xbc0d70: stur            w1, [x0, #0x1b]
    // 0xbc0d74: ldur            x1, [fp, #-0x20]
    // 0xbc0d78: StoreField: r0->field_b = r1
    //     0xbc0d78: stur            w1, [x0, #0xb]
    // 0xbc0d7c: ldur            x2, [fp, #-0x18]
    // 0xbc0d80: LoadField: r1 = r2->field_13
    //     0xbc0d80: ldur            w1, [x2, #0x13]
    // 0xbc0d84: DecompressPointer r1
    //     0xbc0d84: add             x1, x1, HEAP, lsl #32
    // 0xbc0d88: r0 = of()
    //     0xbc0d88: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc0d8c: LoadField: r1 = r0->field_87
    //     0xbc0d8c: ldur            w1, [x0, #0x87]
    // 0xbc0d90: DecompressPointer r1
    //     0xbc0d90: add             x1, x1, HEAP, lsl #32
    // 0xbc0d94: LoadField: r0 = r1->field_7
    //     0xbc0d94: ldur            w0, [x1, #7]
    // 0xbc0d98: DecompressPointer r0
    //     0xbc0d98: add             x0, x0, HEAP, lsl #32
    // 0xbc0d9c: r16 = 14.000000
    //     0xbc0d9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbc0da0: ldr             x16, [x16, #0x1d8]
    // 0xbc0da4: r30 = Instance_Color
    //     0xbc0da4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbc0da8: ldr             lr, [lr, #0x858]
    // 0xbc0dac: stp             lr, x16, [SP]
    // 0xbc0db0: mov             x1, x0
    // 0xbc0db4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbc0db4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbc0db8: ldr             x4, [x4, #0xaa0]
    // 0xbc0dbc: r0 = copyWith()
    //     0xbc0dbc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc0dc0: stur            x0, [fp, #-0x20]
    // 0xbc0dc4: r0 = Text()
    //     0xbc0dc4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc0dc8: mov             x1, x0
    // 0xbc0dcc: r0 = "CHANGE"
    //     0xbc0dcc: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d10] "CHANGE"
    //     0xbc0dd0: ldr             x0, [x0, #0xd10]
    // 0xbc0dd4: stur            x1, [fp, #-0x30]
    // 0xbc0dd8: StoreField: r1->field_b = r0
    //     0xbc0dd8: stur            w0, [x1, #0xb]
    // 0xbc0ddc: ldur            x0, [fp, #-0x20]
    // 0xbc0de0: StoreField: r1->field_13 = r0
    //     0xbc0de0: stur            w0, [x1, #0x13]
    // 0xbc0de4: r0 = InkWell()
    //     0xbc0de4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbc0de8: mov             x3, x0
    // 0xbc0dec: ldur            x0, [fp, #-0x30]
    // 0xbc0df0: stur            x3, [fp, #-0x20]
    // 0xbc0df4: StoreField: r3->field_b = r0
    //     0xbc0df4: stur            w0, [x3, #0xb]
    // 0xbc0df8: ldur            x2, [fp, #-0x18]
    // 0xbc0dfc: r1 = Function '<anonymous closure>':.
    //     0xbc0dfc: add             x1, PP, #0x54, lsl #12  ; [pp+0x54458] AnonymousClosure: (0xbc1298), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xbbf99c)
    //     0xbc0e00: ldr             x1, [x1, #0x458]
    // 0xbc0e04: r0 = AllocateClosure()
    //     0xbc0e04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc0e08: mov             x1, x0
    // 0xbc0e0c: ldur            x0, [fp, #-0x20]
    // 0xbc0e10: StoreField: r0->field_f = r1
    //     0xbc0e10: stur            w1, [x0, #0xf]
    // 0xbc0e14: r3 = true
    //     0xbc0e14: add             x3, NULL, #0x20  ; true
    // 0xbc0e18: StoreField: r0->field_43 = r3
    //     0xbc0e18: stur            w3, [x0, #0x43]
    // 0xbc0e1c: r4 = Instance_BoxShape
    //     0xbc0e1c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbc0e20: ldr             x4, [x4, #0x80]
    // 0xbc0e24: StoreField: r0->field_47 = r4
    //     0xbc0e24: stur            w4, [x0, #0x47]
    // 0xbc0e28: StoreField: r0->field_6f = r3
    //     0xbc0e28: stur            w3, [x0, #0x6f]
    // 0xbc0e2c: r5 = false
    //     0xbc0e2c: add             x5, NULL, #0x30  ; false
    // 0xbc0e30: StoreField: r0->field_73 = r5
    //     0xbc0e30: stur            w5, [x0, #0x73]
    // 0xbc0e34: StoreField: r0->field_83 = r3
    //     0xbc0e34: stur            w3, [x0, #0x83]
    // 0xbc0e38: StoreField: r0->field_7b = r5
    //     0xbc0e38: stur            w5, [x0, #0x7b]
    // 0xbc0e3c: r1 = Null
    //     0xbc0e3c: mov             x1, NULL
    // 0xbc0e40: r2 = 10
    //     0xbc0e40: movz            x2, #0xa
    // 0xbc0e44: r0 = AllocateArray()
    //     0xbc0e44: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc0e48: mov             x2, x0
    // 0xbc0e4c: ldur            x0, [fp, #-0x28]
    // 0xbc0e50: stur            x2, [fp, #-0x30]
    // 0xbc0e54: StoreField: r2->field_f = r0
    //     0xbc0e54: stur            w0, [x2, #0xf]
    // 0xbc0e58: r16 = Instance_SizedBox
    //     0xbc0e58: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xbc0e5c: ldr             x16, [x16, #0xb20]
    // 0xbc0e60: StoreField: r2->field_13 = r16
    //     0xbc0e60: stur            w16, [x2, #0x13]
    // 0xbc0e64: ldur            x0, [fp, #-8]
    // 0xbc0e68: ArrayStore: r2[0] = r0  ; List_4
    //     0xbc0e68: stur            w0, [x2, #0x17]
    // 0xbc0e6c: r16 = Instance_Spacer
    //     0xbc0e6c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbc0e70: ldr             x16, [x16, #0xf0]
    // 0xbc0e74: StoreField: r2->field_1b = r16
    //     0xbc0e74: stur            w16, [x2, #0x1b]
    // 0xbc0e78: ldur            x0, [fp, #-0x20]
    // 0xbc0e7c: StoreField: r2->field_1f = r0
    //     0xbc0e7c: stur            w0, [x2, #0x1f]
    // 0xbc0e80: r1 = <Widget>
    //     0xbc0e80: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc0e84: r0 = AllocateGrowableArray()
    //     0xbc0e84: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc0e88: mov             x1, x0
    // 0xbc0e8c: ldur            x0, [fp, #-0x30]
    // 0xbc0e90: stur            x1, [fp, #-8]
    // 0xbc0e94: StoreField: r1->field_f = r0
    //     0xbc0e94: stur            w0, [x1, #0xf]
    // 0xbc0e98: r0 = 10
    //     0xbc0e98: movz            x0, #0xa
    // 0xbc0e9c: StoreField: r1->field_b = r0
    //     0xbc0e9c: stur            w0, [x1, #0xb]
    // 0xbc0ea0: r0 = Row()
    //     0xbc0ea0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbc0ea4: mov             x1, x0
    // 0xbc0ea8: r0 = Instance_Axis
    //     0xbc0ea8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc0eac: stur            x1, [fp, #-0x20]
    // 0xbc0eb0: StoreField: r1->field_f = r0
    //     0xbc0eb0: stur            w0, [x1, #0xf]
    // 0xbc0eb4: r0 = Instance_MainAxisAlignment
    //     0xbc0eb4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbc0eb8: ldr             x0, [x0, #0xa8]
    // 0xbc0ebc: StoreField: r1->field_13 = r0
    //     0xbc0ebc: stur            w0, [x1, #0x13]
    // 0xbc0ec0: r0 = Instance_MainAxisSize
    //     0xbc0ec0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc0ec4: ldr             x0, [x0, #0xa10]
    // 0xbc0ec8: ArrayStore: r1[0] = r0  ; List_4
    //     0xbc0ec8: stur            w0, [x1, #0x17]
    // 0xbc0ecc: r0 = Instance_CrossAxisAlignment
    //     0xbc0ecc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc0ed0: ldr             x0, [x0, #0xa18]
    // 0xbc0ed4: StoreField: r1->field_1b = r0
    //     0xbc0ed4: stur            w0, [x1, #0x1b]
    // 0xbc0ed8: r0 = Instance_VerticalDirection
    //     0xbc0ed8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc0edc: ldr             x0, [x0, #0xa20]
    // 0xbc0ee0: StoreField: r1->field_23 = r0
    //     0xbc0ee0: stur            w0, [x1, #0x23]
    // 0xbc0ee4: r0 = Instance_Clip
    //     0xbc0ee4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc0ee8: ldr             x0, [x0, #0x38]
    // 0xbc0eec: StoreField: r1->field_2b = r0
    //     0xbc0eec: stur            w0, [x1, #0x2b]
    // 0xbc0ef0: StoreField: r1->field_2f = rZR
    //     0xbc0ef0: stur            xzr, [x1, #0x2f]
    // 0xbc0ef4: ldur            x0, [fp, #-8]
    // 0xbc0ef8: StoreField: r1->field_b = r0
    //     0xbc0ef8: stur            w0, [x1, #0xb]
    // 0xbc0efc: r0 = Container()
    //     0xbc0efc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbc0f00: stur            x0, [fp, #-8]
    // 0xbc0f04: r16 = Instance_EdgeInsets
    //     0xbc0f04: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbc0f08: ldr             x16, [x16, #0x668]
    // 0xbc0f0c: r30 = Instance_BoxDecoration
    //     0xbc0f0c: add             lr, PP, #0x54, lsl #12  ; [pp+0x54438] Obj!BoxDecoration@d64bc1
    //     0xbc0f10: ldr             lr, [lr, #0x438]
    // 0xbc0f14: stp             lr, x16, [SP, #8]
    // 0xbc0f18: ldur            x16, [fp, #-0x20]
    // 0xbc0f1c: str             x16, [SP]
    // 0xbc0f20: mov             x1, x0
    // 0xbc0f24: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xbc0f24: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xbc0f28: ldr             x4, [x4, #0x610]
    // 0xbc0f2c: r0 = Container()
    //     0xbc0f2c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbc0f30: r0 = Padding()
    //     0xbc0f30: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc0f34: mov             x1, x0
    // 0xbc0f38: r0 = Instance_EdgeInsets
    //     0xbc0f38: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0xbc0f3c: ldr             x0, [x0, #0xa98]
    // 0xbc0f40: StoreField: r1->field_f = r0
    //     0xbc0f40: stur            w0, [x1, #0xf]
    // 0xbc0f44: ldur            x0, [fp, #-8]
    // 0xbc0f48: StoreField: r1->field_b = r0
    //     0xbc0f48: stur            w0, [x1, #0xb]
    // 0xbc0f4c: mov             x0, x1
    // 0xbc0f50: stur            x0, [fp, #-8]
    // 0xbc0f54: r0 = InkWell()
    //     0xbc0f54: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbc0f58: mov             x3, x0
    // 0xbc0f5c: ldur            x0, [fp, #-8]
    // 0xbc0f60: stur            x3, [fp, #-0x20]
    // 0xbc0f64: StoreField: r3->field_b = r0
    //     0xbc0f64: stur            w0, [x3, #0xb]
    // 0xbc0f68: ldur            x2, [fp, #-0x18]
    // 0xbc0f6c: r1 = Function '<anonymous closure>':.
    //     0xbc0f6c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54460] AnonymousClosure: (0xbc1028), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xbbf99c)
    //     0xbc0f70: ldr             x1, [x1, #0x460]
    // 0xbc0f74: r0 = AllocateClosure()
    //     0xbc0f74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc0f78: mov             x1, x0
    // 0xbc0f7c: ldur            x0, [fp, #-0x20]
    // 0xbc0f80: StoreField: r0->field_f = r1
    //     0xbc0f80: stur            w1, [x0, #0xf]
    // 0xbc0f84: r1 = true
    //     0xbc0f84: add             x1, NULL, #0x20  ; true
    // 0xbc0f88: StoreField: r0->field_43 = r1
    //     0xbc0f88: stur            w1, [x0, #0x43]
    // 0xbc0f8c: r2 = Instance_BoxShape
    //     0xbc0f8c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbc0f90: ldr             x2, [x2, #0x80]
    // 0xbc0f94: StoreField: r0->field_47 = r2
    //     0xbc0f94: stur            w2, [x0, #0x47]
    // 0xbc0f98: StoreField: r0->field_6f = r1
    //     0xbc0f98: stur            w1, [x0, #0x6f]
    // 0xbc0f9c: r2 = false
    //     0xbc0f9c: add             x2, NULL, #0x30  ; false
    // 0xbc0fa0: StoreField: r0->field_73 = r2
    //     0xbc0fa0: stur            w2, [x0, #0x73]
    // 0xbc0fa4: StoreField: r0->field_83 = r1
    //     0xbc0fa4: stur            w1, [x0, #0x83]
    // 0xbc0fa8: StoreField: r0->field_7b = r2
    //     0xbc0fa8: stur            w2, [x0, #0x7b]
    // 0xbc0fac: mov             x1, x0
    // 0xbc0fb0: ldur            x0, [fp, #-0x10]
    // 0xbc0fb4: stur            x1, [fp, #-8]
    // 0xbc0fb8: r0 = Visibility()
    //     0xbc0fb8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbc0fbc: ldur            x1, [fp, #-8]
    // 0xbc0fc0: StoreField: r0->field_b = r1
    //     0xbc0fc0: stur            w1, [x0, #0xb]
    // 0xbc0fc4: r1 = Instance_SizedBox
    //     0xbc0fc4: ldr             x1, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbc0fc8: StoreField: r0->field_f = r1
    //     0xbc0fc8: stur            w1, [x0, #0xf]
    // 0xbc0fcc: ldur            x1, [fp, #-0x10]
    // 0xbc0fd0: StoreField: r0->field_13 = r1
    //     0xbc0fd0: stur            w1, [x0, #0x13]
    // 0xbc0fd4: r1 = false
    //     0xbc0fd4: add             x1, NULL, #0x30  ; false
    // 0xbc0fd8: ArrayStore: r0[0] = r1  ; List_4
    //     0xbc0fd8: stur            w1, [x0, #0x17]
    // 0xbc0fdc: StoreField: r0->field_1b = r1
    //     0xbc0fdc: stur            w1, [x0, #0x1b]
    // 0xbc0fe0: StoreField: r0->field_1f = r1
    //     0xbc0fe0: stur            w1, [x0, #0x1f]
    // 0xbc0fe4: StoreField: r0->field_23 = r1
    //     0xbc0fe4: stur            w1, [x0, #0x23]
    // 0xbc0fe8: StoreField: r0->field_27 = r1
    //     0xbc0fe8: stur            w1, [x0, #0x27]
    // 0xbc0fec: StoreField: r0->field_2b = r1
    //     0xbc0fec: stur            w1, [x0, #0x2b]
    // 0xbc0ff0: LeaveFrame
    //     0xbc0ff0: mov             SP, fp
    //     0xbc0ff4: ldp             fp, lr, [SP], #0x10
    // 0xbc0ff8: ret
    //     0xbc0ff8: ret             
    // 0xbc0ffc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc0ffc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc1000: b               #0xbbf9bc
    // 0xbc1004: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc1004: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc1008: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc1008: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc100c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc100c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc1010: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc1010: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc1014: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc1014: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc1018: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc1018: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc101c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc101c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc1020: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc1020: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc1024: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc1024: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbc1028, size: 0x7c
    // 0xbc1028: EnterFrame
    //     0xbc1028: stp             fp, lr, [SP, #-0x10]!
    //     0xbc102c: mov             fp, SP
    // 0xbc1030: AllocStack(0x30)
    //     0xbc1030: sub             SP, SP, #0x30
    // 0xbc1034: SetupParameters()
    //     0xbc1034: ldr             x0, [fp, #0x10]
    //     0xbc1038: ldur            w2, [x0, #0x17]
    //     0xbc103c: add             x2, x2, HEAP, lsl #32
    // 0xbc1040: CheckStackOverflow
    //     0xbc1040: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc1044: cmp             SP, x16
    //     0xbc1048: b.ls            #0xbc109c
    // 0xbc104c: LoadField: r0 = r2->field_13
    //     0xbc104c: ldur            w0, [x2, #0x13]
    // 0xbc1050: DecompressPointer r0
    //     0xbc1050: add             x0, x0, HEAP, lsl #32
    // 0xbc1054: stur            x0, [fp, #-8]
    // 0xbc1058: r1 = Function '<anonymous closure>':.
    //     0xbc1058: add             x1, PP, #0x54, lsl #12  ; [pp+0x54468] AnonymousClosure: (0xbc10a4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xbbf99c)
    //     0xbc105c: ldr             x1, [x1, #0x468]
    // 0xbc1060: r0 = AllocateClosure()
    //     0xbc1060: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc1064: stp             x0, NULL, [SP, #0x18]
    // 0xbc1068: ldur            x16, [fp, #-8]
    // 0xbc106c: r30 = true
    //     0xbc106c: add             lr, NULL, #0x20  ; true
    // 0xbc1070: stp             lr, x16, [SP, #8]
    // 0xbc1074: r16 = Instance_RoundedRectangleBorder
    //     0xbc1074: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbc1078: ldr             x16, [x16, #0xd68]
    // 0xbc107c: str             x16, [SP]
    // 0xbc1080: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xbc1080: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xbc1084: ldr             x4, [x4, #0xb20]
    // 0xbc1088: r0 = showModalBottomSheet()
    //     0xbc1088: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xbc108c: r0 = Null
    //     0xbc108c: mov             x0, NULL
    // 0xbc1090: LeaveFrame
    //     0xbc1090: mov             SP, fp
    //     0xbc1094: ldp             fp, lr, [SP], #0x10
    // 0xbc1098: ret
    //     0xbc1098: ret             
    // 0xbc109c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc109c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc10a0: b               #0xbc104c
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xbc10a4, size: 0x1e8
    // 0xbc10a4: EnterFrame
    //     0xbc10a4: stp             fp, lr, [SP, #-0x10]!
    //     0xbc10a8: mov             fp, SP
    // 0xbc10ac: AllocStack(0x58)
    //     0xbc10ac: sub             SP, SP, #0x58
    // 0xbc10b0: SetupParameters()
    //     0xbc10b0: ldr             x0, [fp, #0x18]
    //     0xbc10b4: ldur            w1, [x0, #0x17]
    //     0xbc10b8: add             x1, x1, HEAP, lsl #32
    //     0xbc10bc: stur            x1, [fp, #-8]
    // 0xbc10c0: CheckStackOverflow
    //     0xbc10c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc10c4: cmp             SP, x16
    //     0xbc10c8: b.ls            #0xbc1280
    // 0xbc10cc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbc10cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc10d0: ldr             x0, [x0, #0x1c80]
    //     0xbc10d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc10d8: cmp             w0, w16
    //     0xbc10dc: b.ne            #0xbc10e8
    //     0xbc10e0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbc10e4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbc10e8: r0 = GetNavigation.size()
    //     0xbc10e8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbc10ec: LoadField: d0 = r0->field_f
    //     0xbc10ec: ldur            d0, [x0, #0xf]
    // 0xbc10f0: d1 = 0.750000
    //     0xbc10f0: fmov            d1, #0.75000000
    // 0xbc10f4: fmul            d2, d0, d1
    // 0xbc10f8: stur            d2, [fp, #-0x50]
    // 0xbc10fc: r0 = GetNavigation.size()
    //     0xbc10fc: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbc1100: LoadField: d0 = r0->field_f
    //     0xbc1100: ldur            d0, [x0, #0xf]
    // 0xbc1104: d1 = 0.550000
    //     0xbc1104: add             x17, PP, #0x54, lsl #12  ; [pp+0x54470] IMM: double(0.55) from 0x3fe199999999999a
    //     0xbc1108: ldr             d1, [x17, #0x470]
    // 0xbc110c: fmul            d2, d0, d1
    // 0xbc1110: stur            d2, [fp, #-0x58]
    // 0xbc1114: r0 = BoxConstraints()
    //     0xbc1114: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xbc1118: stur            x0, [fp, #-0x40]
    // 0xbc111c: StoreField: r0->field_7 = rZR
    //     0xbc111c: stur            xzr, [x0, #7]
    // 0xbc1120: d0 = inf
    //     0xbc1120: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xbc1124: StoreField: r0->field_f = d0
    //     0xbc1124: stur            d0, [x0, #0xf]
    // 0xbc1128: ldur            d0, [fp, #-0x58]
    // 0xbc112c: ArrayStore: r0[0] = d0  ; List_8
    //     0xbc112c: stur            d0, [x0, #0x17]
    // 0xbc1130: ldur            d0, [fp, #-0x50]
    // 0xbc1134: StoreField: r0->field_1f = d0
    //     0xbc1134: stur            d0, [x0, #0x1f]
    // 0xbc1138: ldur            x1, [fp, #-8]
    // 0xbc113c: LoadField: r2 = r1->field_f
    //     0xbc113c: ldur            w2, [x1, #0xf]
    // 0xbc1140: DecompressPointer r2
    //     0xbc1140: add             x2, x2, HEAP, lsl #32
    // 0xbc1144: LoadField: r1 = r2->field_b
    //     0xbc1144: ldur            w1, [x2, #0xb]
    // 0xbc1148: DecompressPointer r1
    //     0xbc1148: add             x1, x1, HEAP, lsl #32
    // 0xbc114c: cmp             w1, NULL
    // 0xbc1150: b.eq            #0xbc1288
    // 0xbc1154: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbc1154: ldur            w2, [x1, #0x17]
    // 0xbc1158: DecompressPointer r2
    //     0xbc1158: add             x2, x2, HEAP, lsl #32
    // 0xbc115c: stur            x2, [fp, #-0x38]
    // 0xbc1160: LoadField: r3 = r1->field_1b
    //     0xbc1160: ldur            w3, [x1, #0x1b]
    // 0xbc1164: DecompressPointer r3
    //     0xbc1164: add             x3, x3, HEAP, lsl #32
    // 0xbc1168: stur            x3, [fp, #-0x30]
    // 0xbc116c: LoadField: r4 = r1->field_f
    //     0xbc116c: ldur            w4, [x1, #0xf]
    // 0xbc1170: DecompressPointer r4
    //     0xbc1170: add             x4, x4, HEAP, lsl #32
    // 0xbc1174: LoadField: r5 = r4->field_b
    //     0xbc1174: ldur            w5, [x4, #0xb]
    // 0xbc1178: DecompressPointer r5
    //     0xbc1178: add             x5, x5, HEAP, lsl #32
    // 0xbc117c: stur            x5, [fp, #-0x28]
    // 0xbc1180: LoadField: r4 = r1->field_23
    //     0xbc1180: ldur            w4, [x1, #0x23]
    // 0xbc1184: DecompressPointer r4
    //     0xbc1184: add             x4, x4, HEAP, lsl #32
    // 0xbc1188: LoadField: r6 = r4->field_b
    //     0xbc1188: ldur            w6, [x4, #0xb]
    // 0xbc118c: DecompressPointer r6
    //     0xbc118c: add             x6, x6, HEAP, lsl #32
    // 0xbc1190: cmp             w6, NULL
    // 0xbc1194: b.ne            #0xbc11a0
    // 0xbc1198: r4 = Null
    //     0xbc1198: mov             x4, NULL
    // 0xbc119c: b               #0xbc11c4
    // 0xbc11a0: LoadField: r4 = r6->field_33
    //     0xbc11a0: ldur            w4, [x6, #0x33]
    // 0xbc11a4: DecompressPointer r4
    //     0xbc11a4: add             x4, x4, HEAP, lsl #32
    // 0xbc11a8: cmp             w4, NULL
    // 0xbc11ac: b.ne            #0xbc11b8
    // 0xbc11b0: r4 = Null
    //     0xbc11b0: mov             x4, NULL
    // 0xbc11b4: b               #0xbc11c4
    // 0xbc11b8: LoadField: r7 = r4->field_7
    //     0xbc11b8: ldur            w7, [x4, #7]
    // 0xbc11bc: DecompressPointer r7
    //     0xbc11bc: add             x7, x7, HEAP, lsl #32
    // 0xbc11c0: mov             x4, x7
    // 0xbc11c4: stur            x4, [fp, #-0x20]
    // 0xbc11c8: LoadField: r7 = r1->field_1f
    //     0xbc11c8: ldur            w7, [x1, #0x1f]
    // 0xbc11cc: DecompressPointer r7
    //     0xbc11cc: add             x7, x7, HEAP, lsl #32
    // 0xbc11d0: stur            x7, [fp, #-0x18]
    // 0xbc11d4: LoadField: r8 = r1->field_b
    //     0xbc11d4: ldur            w8, [x1, #0xb]
    // 0xbc11d8: DecompressPointer r8
    //     0xbc11d8: add             x8, x8, HEAP, lsl #32
    // 0xbc11dc: stur            x8, [fp, #-0x10]
    // 0xbc11e0: cmp             w6, NULL
    // 0xbc11e4: b.ne            #0xbc11f0
    // 0xbc11e8: r1 = Null
    //     0xbc11e8: mov             x1, NULL
    // 0xbc11ec: b               #0xbc11f8
    // 0xbc11f0: LoadField: r1 = r6->field_33
    //     0xbc11f0: ldur            w1, [x6, #0x33]
    // 0xbc11f4: DecompressPointer r1
    //     0xbc11f4: add             x1, x1, HEAP, lsl #32
    // 0xbc11f8: stur            x1, [fp, #-8]
    // 0xbc11fc: r0 = OffersListWidget()
    //     0xbc11fc: bl              #0xbc128c  ; AllocateOffersListWidgetStub -> OffersListWidget (size=0x34)
    // 0xbc1200: mov             x1, x0
    // 0xbc1204: ldur            x0, [fp, #-0x28]
    // 0xbc1208: stur            x1, [fp, #-0x48]
    // 0xbc120c: StoreField: r1->field_b = r0
    //     0xbc120c: stur            w0, [x1, #0xb]
    // 0xbc1210: ldur            x0, [fp, #-0x20]
    // 0xbc1214: StoreField: r1->field_f = r0
    //     0xbc1214: stur            w0, [x1, #0xf]
    // 0xbc1218: ldur            x0, [fp, #-0x38]
    // 0xbc121c: StoreField: r1->field_13 = r0
    //     0xbc121c: stur            w0, [x1, #0x13]
    // 0xbc1220: ldur            x0, [fp, #-0x30]
    // 0xbc1224: ArrayStore: r1[0] = r0  ; List_4
    //     0xbc1224: stur            w0, [x1, #0x17]
    // 0xbc1228: r0 = true
    //     0xbc1228: add             x0, NULL, #0x20  ; true
    // 0xbc122c: StoreField: r1->field_1b = r0
    //     0xbc122c: stur            w0, [x1, #0x1b]
    // 0xbc1230: r0 = "payment_screen"
    //     0xbc1230: add             x0, PP, #0x38, lsl #12  ; [pp+0x38de8] "payment_screen"
    //     0xbc1234: ldr             x0, [x0, #0xde8]
    // 0xbc1238: StoreField: r1->field_23 = r0
    //     0xbc1238: stur            w0, [x1, #0x23]
    // 0xbc123c: ldur            x0, [fp, #-0x18]
    // 0xbc1240: StoreField: r1->field_1f = r0
    //     0xbc1240: stur            w0, [x1, #0x1f]
    // 0xbc1244: r0 = "false"
    //     0xbc1244: add             x0, PP, #8, lsl #12  ; [pp+0x8ed8] "false"
    //     0xbc1248: ldr             x0, [x0, #0xed8]
    // 0xbc124c: StoreField: r1->field_27 = r0
    //     0xbc124c: stur            w0, [x1, #0x27]
    // 0xbc1250: ldur            x0, [fp, #-0x10]
    // 0xbc1254: StoreField: r1->field_2b = r0
    //     0xbc1254: stur            w0, [x1, #0x2b]
    // 0xbc1258: ldur            x0, [fp, #-8]
    // 0xbc125c: StoreField: r1->field_2f = r0
    //     0xbc125c: stur            w0, [x1, #0x2f]
    // 0xbc1260: r0 = ConstrainedBox()
    //     0xbc1260: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xbc1264: ldur            x1, [fp, #-0x40]
    // 0xbc1268: StoreField: r0->field_f = r1
    //     0xbc1268: stur            w1, [x0, #0xf]
    // 0xbc126c: ldur            x1, [fp, #-0x48]
    // 0xbc1270: StoreField: r0->field_b = r1
    //     0xbc1270: stur            w1, [x0, #0xb]
    // 0xbc1274: LeaveFrame
    //     0xbc1274: mov             SP, fp
    //     0xbc1278: ldp             fp, lr, [SP], #0x10
    // 0xbc127c: ret
    //     0xbc127c: ret             
    // 0xbc1280: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc1280: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc1284: b               #0xbc10cc
    // 0xbc1288: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc1288: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbc1298, size: 0x7c
    // 0xbc1298: EnterFrame
    //     0xbc1298: stp             fp, lr, [SP, #-0x10]!
    //     0xbc129c: mov             fp, SP
    // 0xbc12a0: AllocStack(0x30)
    //     0xbc12a0: sub             SP, SP, #0x30
    // 0xbc12a4: SetupParameters()
    //     0xbc12a4: ldr             x0, [fp, #0x10]
    //     0xbc12a8: ldur            w2, [x0, #0x17]
    //     0xbc12ac: add             x2, x2, HEAP, lsl #32
    // 0xbc12b0: CheckStackOverflow
    //     0xbc12b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc12b4: cmp             SP, x16
    //     0xbc12b8: b.ls            #0xbc130c
    // 0xbc12bc: LoadField: r0 = r2->field_13
    //     0xbc12bc: ldur            w0, [x2, #0x13]
    // 0xbc12c0: DecompressPointer r0
    //     0xbc12c0: add             x0, x0, HEAP, lsl #32
    // 0xbc12c4: stur            x0, [fp, #-8]
    // 0xbc12c8: r1 = Function '<anonymous closure>':.
    //     0xbc12c8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54478] AnonymousClosure: (0xbc1314), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xbbf99c)
    //     0xbc12cc: ldr             x1, [x1, #0x478]
    // 0xbc12d0: r0 = AllocateClosure()
    //     0xbc12d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc12d4: stp             x0, NULL, [SP, #0x18]
    // 0xbc12d8: ldur            x16, [fp, #-8]
    // 0xbc12dc: r30 = true
    //     0xbc12dc: add             lr, NULL, #0x20  ; true
    // 0xbc12e0: stp             lr, x16, [SP, #8]
    // 0xbc12e4: r16 = Instance_RoundedRectangleBorder
    //     0xbc12e4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbc12e8: ldr             x16, [x16, #0xd68]
    // 0xbc12ec: str             x16, [SP]
    // 0xbc12f0: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xbc12f0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xbc12f4: ldr             x4, [x4, #0xb20]
    // 0xbc12f8: r0 = showModalBottomSheet()
    //     0xbc12f8: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xbc12fc: r0 = Null
    //     0xbc12fc: mov             x0, NULL
    // 0xbc1300: LeaveFrame
    //     0xbc1300: mov             SP, fp
    //     0xbc1304: ldp             fp, lr, [SP], #0x10
    // 0xbc1308: ret
    //     0xbc1308: ret             
    // 0xbc130c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc130c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc1310: b               #0xbc12bc
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xbc1314, size: 0x1e8
    // 0xbc1314: EnterFrame
    //     0xbc1314: stp             fp, lr, [SP, #-0x10]!
    //     0xbc1318: mov             fp, SP
    // 0xbc131c: AllocStack(0x58)
    //     0xbc131c: sub             SP, SP, #0x58
    // 0xbc1320: SetupParameters()
    //     0xbc1320: ldr             x0, [fp, #0x18]
    //     0xbc1324: ldur            w1, [x0, #0x17]
    //     0xbc1328: add             x1, x1, HEAP, lsl #32
    //     0xbc132c: stur            x1, [fp, #-8]
    // 0xbc1330: CheckStackOverflow
    //     0xbc1330: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc1334: cmp             SP, x16
    //     0xbc1338: b.ls            #0xbc14f0
    // 0xbc133c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbc133c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc1340: ldr             x0, [x0, #0x1c80]
    //     0xbc1344: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc1348: cmp             w0, w16
    //     0xbc134c: b.ne            #0xbc1358
    //     0xbc1350: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbc1354: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbc1358: r0 = GetNavigation.size()
    //     0xbc1358: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbc135c: LoadField: d0 = r0->field_f
    //     0xbc135c: ldur            d0, [x0, #0xf]
    // 0xbc1360: d1 = 0.750000
    //     0xbc1360: fmov            d1, #0.75000000
    // 0xbc1364: fmul            d2, d0, d1
    // 0xbc1368: stur            d2, [fp, #-0x50]
    // 0xbc136c: r0 = GetNavigation.size()
    //     0xbc136c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbc1370: LoadField: d0 = r0->field_f
    //     0xbc1370: ldur            d0, [x0, #0xf]
    // 0xbc1374: d1 = 0.550000
    //     0xbc1374: add             x17, PP, #0x54, lsl #12  ; [pp+0x54470] IMM: double(0.55) from 0x3fe199999999999a
    //     0xbc1378: ldr             d1, [x17, #0x470]
    // 0xbc137c: fmul            d2, d0, d1
    // 0xbc1380: stur            d2, [fp, #-0x58]
    // 0xbc1384: r0 = BoxConstraints()
    //     0xbc1384: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xbc1388: stur            x0, [fp, #-0x40]
    // 0xbc138c: StoreField: r0->field_7 = rZR
    //     0xbc138c: stur            xzr, [x0, #7]
    // 0xbc1390: d0 = inf
    //     0xbc1390: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xbc1394: StoreField: r0->field_f = d0
    //     0xbc1394: stur            d0, [x0, #0xf]
    // 0xbc1398: ldur            d0, [fp, #-0x58]
    // 0xbc139c: ArrayStore: r0[0] = d0  ; List_8
    //     0xbc139c: stur            d0, [x0, #0x17]
    // 0xbc13a0: ldur            d0, [fp, #-0x50]
    // 0xbc13a4: StoreField: r0->field_1f = d0
    //     0xbc13a4: stur            d0, [x0, #0x1f]
    // 0xbc13a8: ldur            x1, [fp, #-8]
    // 0xbc13ac: LoadField: r2 = r1->field_f
    //     0xbc13ac: ldur            w2, [x1, #0xf]
    // 0xbc13b0: DecompressPointer r2
    //     0xbc13b0: add             x2, x2, HEAP, lsl #32
    // 0xbc13b4: LoadField: r1 = r2->field_b
    //     0xbc13b4: ldur            w1, [x2, #0xb]
    // 0xbc13b8: DecompressPointer r1
    //     0xbc13b8: add             x1, x1, HEAP, lsl #32
    // 0xbc13bc: cmp             w1, NULL
    // 0xbc13c0: b.eq            #0xbc14f8
    // 0xbc13c4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbc13c4: ldur            w2, [x1, #0x17]
    // 0xbc13c8: DecompressPointer r2
    //     0xbc13c8: add             x2, x2, HEAP, lsl #32
    // 0xbc13cc: stur            x2, [fp, #-0x38]
    // 0xbc13d0: LoadField: r3 = r1->field_1b
    //     0xbc13d0: ldur            w3, [x1, #0x1b]
    // 0xbc13d4: DecompressPointer r3
    //     0xbc13d4: add             x3, x3, HEAP, lsl #32
    // 0xbc13d8: stur            x3, [fp, #-0x30]
    // 0xbc13dc: LoadField: r4 = r1->field_f
    //     0xbc13dc: ldur            w4, [x1, #0xf]
    // 0xbc13e0: DecompressPointer r4
    //     0xbc13e0: add             x4, x4, HEAP, lsl #32
    // 0xbc13e4: LoadField: r5 = r4->field_b
    //     0xbc13e4: ldur            w5, [x4, #0xb]
    // 0xbc13e8: DecompressPointer r5
    //     0xbc13e8: add             x5, x5, HEAP, lsl #32
    // 0xbc13ec: stur            x5, [fp, #-0x28]
    // 0xbc13f0: LoadField: r4 = r1->field_23
    //     0xbc13f0: ldur            w4, [x1, #0x23]
    // 0xbc13f4: DecompressPointer r4
    //     0xbc13f4: add             x4, x4, HEAP, lsl #32
    // 0xbc13f8: LoadField: r6 = r4->field_b
    //     0xbc13f8: ldur            w6, [x4, #0xb]
    // 0xbc13fc: DecompressPointer r6
    //     0xbc13fc: add             x6, x6, HEAP, lsl #32
    // 0xbc1400: cmp             w6, NULL
    // 0xbc1404: b.ne            #0xbc1410
    // 0xbc1408: r4 = Null
    //     0xbc1408: mov             x4, NULL
    // 0xbc140c: b               #0xbc1434
    // 0xbc1410: LoadField: r4 = r6->field_33
    //     0xbc1410: ldur            w4, [x6, #0x33]
    // 0xbc1414: DecompressPointer r4
    //     0xbc1414: add             x4, x4, HEAP, lsl #32
    // 0xbc1418: cmp             w4, NULL
    // 0xbc141c: b.ne            #0xbc1428
    // 0xbc1420: r4 = Null
    //     0xbc1420: mov             x4, NULL
    // 0xbc1424: b               #0xbc1434
    // 0xbc1428: LoadField: r7 = r4->field_7
    //     0xbc1428: ldur            w7, [x4, #7]
    // 0xbc142c: DecompressPointer r7
    //     0xbc142c: add             x7, x7, HEAP, lsl #32
    // 0xbc1430: mov             x4, x7
    // 0xbc1434: stur            x4, [fp, #-0x20]
    // 0xbc1438: LoadField: r7 = r1->field_1f
    //     0xbc1438: ldur            w7, [x1, #0x1f]
    // 0xbc143c: DecompressPointer r7
    //     0xbc143c: add             x7, x7, HEAP, lsl #32
    // 0xbc1440: stur            x7, [fp, #-0x18]
    // 0xbc1444: LoadField: r8 = r1->field_b
    //     0xbc1444: ldur            w8, [x1, #0xb]
    // 0xbc1448: DecompressPointer r8
    //     0xbc1448: add             x8, x8, HEAP, lsl #32
    // 0xbc144c: stur            x8, [fp, #-0x10]
    // 0xbc1450: cmp             w6, NULL
    // 0xbc1454: b.ne            #0xbc1460
    // 0xbc1458: r1 = Null
    //     0xbc1458: mov             x1, NULL
    // 0xbc145c: b               #0xbc1468
    // 0xbc1460: LoadField: r1 = r6->field_33
    //     0xbc1460: ldur            w1, [x6, #0x33]
    // 0xbc1464: DecompressPointer r1
    //     0xbc1464: add             x1, x1, HEAP, lsl #32
    // 0xbc1468: stur            x1, [fp, #-8]
    // 0xbc146c: r0 = OffersListWidget()
    //     0xbc146c: bl              #0xbc128c  ; AllocateOffersListWidgetStub -> OffersListWidget (size=0x34)
    // 0xbc1470: mov             x1, x0
    // 0xbc1474: ldur            x0, [fp, #-0x28]
    // 0xbc1478: stur            x1, [fp, #-0x48]
    // 0xbc147c: StoreField: r1->field_b = r0
    //     0xbc147c: stur            w0, [x1, #0xb]
    // 0xbc1480: ldur            x0, [fp, #-0x20]
    // 0xbc1484: StoreField: r1->field_f = r0
    //     0xbc1484: stur            w0, [x1, #0xf]
    // 0xbc1488: ldur            x0, [fp, #-0x38]
    // 0xbc148c: StoreField: r1->field_13 = r0
    //     0xbc148c: stur            w0, [x1, #0x13]
    // 0xbc1490: ldur            x0, [fp, #-0x30]
    // 0xbc1494: ArrayStore: r1[0] = r0  ; List_4
    //     0xbc1494: stur            w0, [x1, #0x17]
    // 0xbc1498: r0 = true
    //     0xbc1498: add             x0, NULL, #0x20  ; true
    // 0xbc149c: StoreField: r1->field_1b = r0
    //     0xbc149c: stur            w0, [x1, #0x1b]
    // 0xbc14a0: r0 = "payment_screen"
    //     0xbc14a0: add             x0, PP, #0x38, lsl #12  ; [pp+0x38de8] "payment_screen"
    //     0xbc14a4: ldr             x0, [x0, #0xde8]
    // 0xbc14a8: StoreField: r1->field_23 = r0
    //     0xbc14a8: stur            w0, [x1, #0x23]
    // 0xbc14ac: ldur            x0, [fp, #-0x18]
    // 0xbc14b0: StoreField: r1->field_1f = r0
    //     0xbc14b0: stur            w0, [x1, #0x1f]
    // 0xbc14b4: r0 = "true"
    //     0xbc14b4: add             x0, PP, #8, lsl #12  ; [pp+0x8ed0] "true"
    //     0xbc14b8: ldr             x0, [x0, #0xed0]
    // 0xbc14bc: StoreField: r1->field_27 = r0
    //     0xbc14bc: stur            w0, [x1, #0x27]
    // 0xbc14c0: ldur            x0, [fp, #-0x10]
    // 0xbc14c4: StoreField: r1->field_2b = r0
    //     0xbc14c4: stur            w0, [x1, #0x2b]
    // 0xbc14c8: ldur            x0, [fp, #-8]
    // 0xbc14cc: StoreField: r1->field_2f = r0
    //     0xbc14cc: stur            w0, [x1, #0x2f]
    // 0xbc14d0: r0 = ConstrainedBox()
    //     0xbc14d0: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xbc14d4: ldur            x1, [fp, #-0x40]
    // 0xbc14d8: StoreField: r0->field_f = r1
    //     0xbc14d8: stur            w1, [x0, #0xf]
    // 0xbc14dc: ldur            x1, [fp, #-0x48]
    // 0xbc14e0: StoreField: r0->field_b = r1
    //     0xbc14e0: stur            w1, [x0, #0xb]
    // 0xbc14e4: LeaveFrame
    //     0xbc14e4: mov             SP, fp
    //     0xbc14e8: ldp             fp, lr, [SP], #0x10
    // 0xbc14ec: ret
    //     0xbc14ec: ret             
    // 0xbc14f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc14f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc14f4: b               #0xbc133c
    // 0xbc14f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc14f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbc14fc, size: 0x7c
    // 0xbc14fc: EnterFrame
    //     0xbc14fc: stp             fp, lr, [SP, #-0x10]!
    //     0xbc1500: mov             fp, SP
    // 0xbc1504: AllocStack(0x30)
    //     0xbc1504: sub             SP, SP, #0x30
    // 0xbc1508: SetupParameters()
    //     0xbc1508: ldr             x0, [fp, #0x10]
    //     0xbc150c: ldur            w2, [x0, #0x17]
    //     0xbc1510: add             x2, x2, HEAP, lsl #32
    // 0xbc1514: CheckStackOverflow
    //     0xbc1514: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc1518: cmp             SP, x16
    //     0xbc151c: b.ls            #0xbc1570
    // 0xbc1520: LoadField: r0 = r2->field_13
    //     0xbc1520: ldur            w0, [x2, #0x13]
    // 0xbc1524: DecompressPointer r0
    //     0xbc1524: add             x0, x0, HEAP, lsl #32
    // 0xbc1528: stur            x0, [fp, #-8]
    // 0xbc152c: r1 = Function '<anonymous closure>':.
    //     0xbc152c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54480] AnonymousClosure: (0xbc1314), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xbbf99c)
    //     0xbc1530: ldr             x1, [x1, #0x480]
    // 0xbc1534: r0 = AllocateClosure()
    //     0xbc1534: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc1538: stp             x0, NULL, [SP, #0x18]
    // 0xbc153c: ldur            x16, [fp, #-8]
    // 0xbc1540: r30 = true
    //     0xbc1540: add             lr, NULL, #0x20  ; true
    // 0xbc1544: stp             lr, x16, [SP, #8]
    // 0xbc1548: r16 = Instance_RoundedRectangleBorder
    //     0xbc1548: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbc154c: ldr             x16, [x16, #0xd68]
    // 0xbc1550: str             x16, [SP]
    // 0xbc1554: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xbc1554: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xbc1558: ldr             x4, [x4, #0xb20]
    // 0xbc155c: r0 = showModalBottomSheet()
    //     0xbc155c: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xbc1560: r0 = Null
    //     0xbc1560: mov             x0, NULL
    // 0xbc1564: LeaveFrame
    //     0xbc1564: mov             SP, fp
    //     0xbc1568: ldp             fp, lr, [SP], #0x10
    // 0xbc156c: ret
    //     0xbc156c: ret             
    // 0xbc1570: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc1570: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc1574: b               #0xbc1520
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbc1578, size: 0x7c
    // 0xbc1578: EnterFrame
    //     0xbc1578: stp             fp, lr, [SP, #-0x10]!
    //     0xbc157c: mov             fp, SP
    // 0xbc1580: AllocStack(0x30)
    //     0xbc1580: sub             SP, SP, #0x30
    // 0xbc1584: SetupParameters()
    //     0xbc1584: ldr             x0, [fp, #0x10]
    //     0xbc1588: ldur            w2, [x0, #0x17]
    //     0xbc158c: add             x2, x2, HEAP, lsl #32
    // 0xbc1590: CheckStackOverflow
    //     0xbc1590: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc1594: cmp             SP, x16
    //     0xbc1598: b.ls            #0xbc15ec
    // 0xbc159c: LoadField: r0 = r2->field_13
    //     0xbc159c: ldur            w0, [x2, #0x13]
    // 0xbc15a0: DecompressPointer r0
    //     0xbc15a0: add             x0, x0, HEAP, lsl #32
    // 0xbc15a4: stur            x0, [fp, #-8]
    // 0xbc15a8: r1 = Function '<anonymous closure>':.
    //     0xbc15a8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54488] AnonymousClosure: (0xbc10a4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xbbf99c)
    //     0xbc15ac: ldr             x1, [x1, #0x488]
    // 0xbc15b0: r0 = AllocateClosure()
    //     0xbc15b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc15b4: stp             x0, NULL, [SP, #0x18]
    // 0xbc15b8: ldur            x16, [fp, #-8]
    // 0xbc15bc: r30 = true
    //     0xbc15bc: add             lr, NULL, #0x20  ; true
    // 0xbc15c0: stp             lr, x16, [SP, #8]
    // 0xbc15c4: r16 = Instance_RoundedRectangleBorder
    //     0xbc15c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbc15c8: ldr             x16, [x16, #0xd68]
    // 0xbc15cc: str             x16, [SP]
    // 0xbc15d0: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xbc15d0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xbc15d4: ldr             x4, [x4, #0xb20]
    // 0xbc15d8: r0 = showModalBottomSheet()
    //     0xbc15d8: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xbc15dc: r0 = Null
    //     0xbc15dc: mov             x0, NULL
    // 0xbc15e0: LeaveFrame
    //     0xbc15e0: mov             SP, fp
    //     0xbc15e4: ldp             fp, lr, [SP], #0x10
    // 0xbc15e8: ret
    //     0xbc15e8: ret             
    // 0xbc15ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc15ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc15f0: b               #0xbc159c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbc15f4, size: 0x7c
    // 0xbc15f4: EnterFrame
    //     0xbc15f4: stp             fp, lr, [SP, #-0x10]!
    //     0xbc15f8: mov             fp, SP
    // 0xbc15fc: AllocStack(0x30)
    //     0xbc15fc: sub             SP, SP, #0x30
    // 0xbc1600: SetupParameters()
    //     0xbc1600: ldr             x0, [fp, #0x10]
    //     0xbc1604: ldur            w2, [x0, #0x17]
    //     0xbc1608: add             x2, x2, HEAP, lsl #32
    // 0xbc160c: CheckStackOverflow
    //     0xbc160c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc1610: cmp             SP, x16
    //     0xbc1614: b.ls            #0xbc1668
    // 0xbc1618: LoadField: r0 = r2->field_13
    //     0xbc1618: ldur            w0, [x2, #0x13]
    // 0xbc161c: DecompressPointer r0
    //     0xbc161c: add             x0, x0, HEAP, lsl #32
    // 0xbc1620: stur            x0, [fp, #-8]
    // 0xbc1624: r1 = Function '<anonymous closure>':.
    //     0xbc1624: add             x1, PP, #0x54, lsl #12  ; [pp+0x54490] AnonymousClosure: (0xbc10a4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/offer_section_widget.dart] _OfferSectionWidgetState::build (0xbbf99c)
    //     0xbc1628: ldr             x1, [x1, #0x490]
    // 0xbc162c: r0 = AllocateClosure()
    //     0xbc162c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc1630: stp             x0, NULL, [SP, #0x18]
    // 0xbc1634: ldur            x16, [fp, #-8]
    // 0xbc1638: r30 = true
    //     0xbc1638: add             lr, NULL, #0x20  ; true
    // 0xbc163c: stp             lr, x16, [SP, #8]
    // 0xbc1640: r16 = Instance_RoundedRectangleBorder
    //     0xbc1640: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbc1644: ldr             x16, [x16, #0xd68]
    // 0xbc1648: str             x16, [SP]
    // 0xbc164c: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xbc164c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xbc1650: ldr             x4, [x4, #0xb20]
    // 0xbc1654: r0 = showModalBottomSheet()
    //     0xbc1654: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xbc1658: r0 = Null
    //     0xbc1658: mov             x0, NULL
    // 0xbc165c: LeaveFrame
    //     0xbc165c: mov             SP, fp
    //     0xbc1660: ldp             fp, lr, [SP], #0x10
    // 0xbc1664: ret
    //     0xbc1664: ret             
    // 0xbc1668: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc1668: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc166c: b               #0xbc1618
  }
}

// class id: 4012, size: 0x30, field offset: 0xc
//   const constructor, 
class OfferSectionWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80550, size: 0x24
    // 0xc80550: EnterFrame
    //     0xc80550: stp             fp, lr, [SP, #-0x10]!
    //     0xc80554: mov             fp, SP
    // 0xc80558: mov             x0, x1
    // 0xc8055c: r1 = <OfferSectionWidget>
    //     0xc8055c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48688] TypeArguments: <OfferSectionWidget>
    //     0xc80560: ldr             x1, [x1, #0x688]
    // 0xc80564: r0 = _OfferSectionWidgetState()
    //     0xc80564: bl              #0xc80574  ; Allocate_OfferSectionWidgetStateStub -> _OfferSectionWidgetState (size=0x14)
    // 0xc80568: LeaveFrame
    //     0xc80568: mov             SP, fp
    //     0xc8056c: ldp             fp, lr, [SP], #0x10
    // 0xc80570: ret
    //     0xc80570: ret             
  }
}
