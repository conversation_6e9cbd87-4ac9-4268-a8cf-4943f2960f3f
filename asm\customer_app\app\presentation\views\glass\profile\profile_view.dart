// lib: , url: package:customer_app/app/presentation/views/glass/profile/profile_view.dart

// class id: 1049453, size: 0x8
class :: {
}

// class id: 4553, size: 0x14, field offset: 0x14
//   const constructor, 
class ProfileView extends BaseView<dynamic> {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14778c4, size: 0xe8
    // 0x14778c4: EnterFrame
    //     0x14778c4: stp             fp, lr, [SP, #-0x10]!
    //     0x14778c8: mov             fp, SP
    // 0x14778cc: AllocStack(0x20)
    //     0x14778cc: sub             SP, SP, #0x20
    // 0x14778d0: SetupParameters()
    //     0x14778d0: ldr             x0, [fp, #0x10]
    //     0x14778d4: ldur            w2, [x0, #0x17]
    //     0x14778d8: add             x2, x2, HEAP, lsl #32
    //     0x14778dc: stur            x2, [fp, #-8]
    // 0x14778e0: CheckStackOverflow
    //     0x14778e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14778e4: cmp             SP, x16
    //     0x14778e8: b.ls            #0x14779a4
    // 0x14778ec: LoadField: r1 = r2->field_f
    //     0x14778ec: ldur            w1, [x2, #0xf]
    // 0x14778f0: DecompressPointer r1
    //     0x14778f0: add             x1, x1, HEAP, lsl #32
    // 0x14778f4: r0 = controller()
    //     0x14778f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14778f8: LoadField: r1 = r0->field_5b
    //     0x14778f8: ldur            w1, [x0, #0x5b]
    // 0x14778fc: DecompressPointer r1
    //     0x14778fc: add             x1, x1, HEAP, lsl #32
    // 0x1477900: r0 = value()
    //     0x1477900: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1477904: tbz             w0, #4, #0x1477994
    // 0x1477908: ldur            x0, [fp, #-8]
    // 0x147790c: LoadField: r1 = r0->field_f
    //     0x147790c: ldur            w1, [x0, #0xf]
    // 0x1477910: DecompressPointer r1
    //     0x1477910: add             x1, x1, HEAP, lsl #32
    // 0x1477914: r0 = controller()
    //     0x1477914: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1477918: mov             x1, x0
    // 0x147791c: r0 = "profile_page"
    //     0x147791c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36cf8] "profile_page"
    //     0x1477920: ldr             x0, [x0, #0xcf8]
    // 0x1477924: StoreField: r1->field_53 = r0
    //     0x1477924: stur            w0, [x1, #0x53]
    // 0x1477928: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1477928: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x147792c: ldr             x0, [x0, #0x1c80]
    //     0x1477930: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1477934: cmp             w0, w16
    //     0x1477938: b.ne            #0x1477944
    //     0x147793c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1477940: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1477944: r1 = Null
    //     0x1477944: mov             x1, NULL
    // 0x1477948: r2 = 4
    //     0x1477948: movz            x2, #0x4
    // 0x147794c: r0 = AllocateArray()
    //     0x147794c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1477950: r16 = "previousScreenSource"
    //     0x1477950: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x1477954: ldr             x16, [x16, #0x448]
    // 0x1477958: StoreField: r0->field_f = r16
    //     0x1477958: stur            w16, [x0, #0xf]
    // 0x147795c: r16 = "profile_page"
    //     0x147795c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36cf8] "profile_page"
    //     0x1477960: ldr             x16, [x16, #0xcf8]
    // 0x1477964: StoreField: r0->field_13 = r16
    //     0x1477964: stur            w16, [x0, #0x13]
    // 0x1477968: r16 = <String, String>
    //     0x1477968: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x147796c: ldr             x16, [x16, #0x788]
    // 0x1477970: stp             x0, x16, [SP]
    // 0x1477974: r0 = Map._fromLiteral()
    //     0x1477974: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x1477978: r16 = "/login"
    //     0x1477978: add             x16, PP, #0xd, lsl #12  ; [pp+0xd880] "/login"
    //     0x147797c: ldr             x16, [x16, #0x880]
    // 0x1477980: stp             x16, NULL, [SP, #8]
    // 0x1477984: str             x0, [SP]
    // 0x1477988: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x1477988: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x147798c: ldr             x4, [x4, #0x438]
    // 0x1477990: r0 = GetNavigation.toNamed()
    //     0x1477990: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x1477994: r0 = Null
    //     0x1477994: mov             x0, NULL
    // 0x1477998: LeaveFrame
    //     0x1477998: mov             SP, fp
    //     0x147799c: ldp             fp, lr, [SP], #0x10
    // 0x14779a0: ret
    //     0x14779a0: ret             
    // 0x14779a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14779a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14779a8: b               #0x14778ec
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x14779ac, size: 0xdfc
    // 0x14779ac: EnterFrame
    //     0x14779ac: stp             fp, lr, [SP, #-0x10]!
    //     0x14779b0: mov             fp, SP
    // 0x14779b4: AllocStack(0x30)
    //     0x14779b4: sub             SP, SP, #0x30
    // 0x14779b8: SetupParameters()
    //     0x14779b8: ldr             x0, [fp, #0x10]
    //     0x14779bc: ldur            w2, [x0, #0x17]
    //     0x14779c0: add             x2, x2, HEAP, lsl #32
    //     0x14779c4: stur            x2, [fp, #-8]
    // 0x14779c8: CheckStackOverflow
    //     0x14779c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14779cc: cmp             SP, x16
    //     0x14779d0: b.ls            #0x14787a0
    // 0x14779d4: LoadField: r1 = r2->field_f
    //     0x14779d4: ldur            w1, [x2, #0xf]
    // 0x14779d8: DecompressPointer r1
    //     0x14779d8: add             x1, x1, HEAP, lsl #32
    // 0x14779dc: r0 = controller()
    //     0x14779dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14779e0: LoadField: r1 = r0->field_5b
    //     0x14779e0: ldur            w1, [x0, #0x5b]
    // 0x14779e4: DecompressPointer r1
    //     0x14779e4: add             x1, x1, HEAP, lsl #32
    // 0x14779e8: r0 = value()
    //     0x14779e8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14779ec: eor             x2, x0, #0x10
    // 0x14779f0: ldur            x0, [fp, #-8]
    // 0x14779f4: stur            x2, [fp, #-0x10]
    // 0x14779f8: LoadField: r1 = r0->field_13
    //     0x14779f8: ldur            w1, [x0, #0x13]
    // 0x14779fc: DecompressPointer r1
    //     0x14779fc: add             x1, x1, HEAP, lsl #32
    // 0x1477a00: r0 = of()
    //     0x1477a00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1477a04: LoadField: r1 = r0->field_87
    //     0x1477a04: ldur            w1, [x0, #0x87]
    // 0x1477a08: DecompressPointer r1
    //     0x1477a08: add             x1, x1, HEAP, lsl #32
    // 0x1477a0c: LoadField: r0 = r1->field_7
    //     0x1477a0c: ldur            w0, [x1, #7]
    // 0x1477a10: DecompressPointer r0
    //     0x1477a10: add             x0, x0, HEAP, lsl #32
    // 0x1477a14: r16 = 14.000000
    //     0x1477a14: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1477a18: ldr             x16, [x16, #0x1d8]
    // 0x1477a1c: str             x16, [SP]
    // 0x1477a20: mov             x1, x0
    // 0x1477a24: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x1477a24: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x1477a28: ldr             x4, [x4, #0x798]
    // 0x1477a2c: r0 = copyWith()
    //     0x1477a2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1477a30: stur            x0, [fp, #-0x18]
    // 0x1477a34: r0 = Text()
    //     0x1477a34: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1477a38: mov             x3, x0
    // 0x1477a3c: r0 = "Login"
    //     0x1477a3c: add             x0, PP, #0x23, lsl #12  ; [pp+0x23770] "Login"
    //     0x1477a40: ldr             x0, [x0, #0x770]
    // 0x1477a44: stur            x3, [fp, #-0x20]
    // 0x1477a48: StoreField: r3->field_b = r0
    //     0x1477a48: stur            w0, [x3, #0xb]
    // 0x1477a4c: ldur            x0, [fp, #-0x18]
    // 0x1477a50: StoreField: r3->field_13 = r0
    //     0x1477a50: stur            w0, [x3, #0x13]
    // 0x1477a54: r1 = Null
    //     0x1477a54: mov             x1, NULL
    // 0x1477a58: r2 = 6
    //     0x1477a58: movz            x2, #0x6
    // 0x1477a5c: r0 = AllocateArray()
    //     0x1477a5c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1477a60: stur            x0, [fp, #-0x18]
    // 0x1477a64: r16 = Instance_Icon
    //     0x1477a64: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c38] Obj!Icon@d66a71
    //     0x1477a68: ldr             x16, [x16, #0xc38]
    // 0x1477a6c: StoreField: r0->field_f = r16
    //     0x1477a6c: stur            w16, [x0, #0xf]
    // 0x1477a70: r16 = Instance_SizedBox
    //     0x1477a70: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x1477a74: ldr             x16, [x16, #0xaa8]
    // 0x1477a78: StoreField: r0->field_13 = r16
    //     0x1477a78: stur            w16, [x0, #0x13]
    // 0x1477a7c: ldur            x1, [fp, #-0x20]
    // 0x1477a80: ArrayStore: r0[0] = r1  ; List_4
    //     0x1477a80: stur            w1, [x0, #0x17]
    // 0x1477a84: r1 = <Widget>
    //     0x1477a84: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1477a88: r0 = AllocateGrowableArray()
    //     0x1477a88: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1477a8c: mov             x1, x0
    // 0x1477a90: ldur            x0, [fp, #-0x18]
    // 0x1477a94: stur            x1, [fp, #-0x20]
    // 0x1477a98: StoreField: r1->field_f = r0
    //     0x1477a98: stur            w0, [x1, #0xf]
    // 0x1477a9c: r2 = 6
    //     0x1477a9c: movz            x2, #0x6
    // 0x1477aa0: StoreField: r1->field_b = r2
    //     0x1477aa0: stur            w2, [x1, #0xb]
    // 0x1477aa4: r0 = Row()
    //     0x1477aa4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1477aa8: mov             x1, x0
    // 0x1477aac: r0 = Instance_Axis
    //     0x1477aac: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1477ab0: stur            x1, [fp, #-0x18]
    // 0x1477ab4: StoreField: r1->field_f = r0
    //     0x1477ab4: stur            w0, [x1, #0xf]
    // 0x1477ab8: r2 = Instance_MainAxisAlignment
    //     0x1477ab8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1477abc: ldr             x2, [x2, #0xa08]
    // 0x1477ac0: StoreField: r1->field_13 = r2
    //     0x1477ac0: stur            w2, [x1, #0x13]
    // 0x1477ac4: r3 = Instance_MainAxisSize
    //     0x1477ac4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1477ac8: ldr             x3, [x3, #0xa10]
    // 0x1477acc: ArrayStore: r1[0] = r3  ; List_4
    //     0x1477acc: stur            w3, [x1, #0x17]
    // 0x1477ad0: r4 = Instance_CrossAxisAlignment
    //     0x1477ad0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1477ad4: ldr             x4, [x4, #0xa18]
    // 0x1477ad8: StoreField: r1->field_1b = r4
    //     0x1477ad8: stur            w4, [x1, #0x1b]
    // 0x1477adc: r5 = Instance_VerticalDirection
    //     0x1477adc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1477ae0: ldr             x5, [x5, #0xa20]
    // 0x1477ae4: StoreField: r1->field_23 = r5
    //     0x1477ae4: stur            w5, [x1, #0x23]
    // 0x1477ae8: r6 = Instance_Clip
    //     0x1477ae8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1477aec: ldr             x6, [x6, #0x38]
    // 0x1477af0: StoreField: r1->field_2b = r6
    //     0x1477af0: stur            w6, [x1, #0x2b]
    // 0x1477af4: StoreField: r1->field_2f = rZR
    //     0x1477af4: stur            xzr, [x1, #0x2f]
    // 0x1477af8: ldur            x7, [fp, #-0x20]
    // 0x1477afc: StoreField: r1->field_b = r7
    //     0x1477afc: stur            w7, [x1, #0xb]
    // 0x1477b00: r0 = InkWell()
    //     0x1477b00: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1477b04: mov             x3, x0
    // 0x1477b08: ldur            x0, [fp, #-0x18]
    // 0x1477b0c: stur            x3, [fp, #-0x20]
    // 0x1477b10: StoreField: r3->field_b = r0
    //     0x1477b10: stur            w0, [x3, #0xb]
    // 0x1477b14: ldur            x2, [fp, #-8]
    // 0x1477b18: r1 = Function '<anonymous closure>':.
    //     0x1477b18: add             x1, PP, #0x40, lsl #12  ; [pp+0x400f8] AnonymousClosure: (0x14778c4), in [package:customer_app/app/presentation/views/glass/profile/profile_view.dart] ProfileView::body (0x14fc37c)
    //     0x1477b1c: ldr             x1, [x1, #0xf8]
    // 0x1477b20: r0 = AllocateClosure()
    //     0x1477b20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1477b24: mov             x1, x0
    // 0x1477b28: ldur            x0, [fp, #-0x20]
    // 0x1477b2c: StoreField: r0->field_f = r1
    //     0x1477b2c: stur            w1, [x0, #0xf]
    // 0x1477b30: r1 = true
    //     0x1477b30: add             x1, NULL, #0x20  ; true
    // 0x1477b34: StoreField: r0->field_43 = r1
    //     0x1477b34: stur            w1, [x0, #0x43]
    // 0x1477b38: r2 = Instance_BoxShape
    //     0x1477b38: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1477b3c: ldr             x2, [x2, #0x80]
    // 0x1477b40: StoreField: r0->field_47 = r2
    //     0x1477b40: stur            w2, [x0, #0x47]
    // 0x1477b44: r3 = Instance_Color
    //     0x1477b44: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x1477b48: ldr             x3, [x3, #0xf88]
    // 0x1477b4c: StoreField: r0->field_5f = r3
    //     0x1477b4c: stur            w3, [x0, #0x5f]
    // 0x1477b50: r4 = Instance__NoSplashFactory
    //     0x1477b50: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x1477b54: ldr             x4, [x4, #0xc48]
    // 0x1477b58: StoreField: r0->field_6b = r4
    //     0x1477b58: stur            w4, [x0, #0x6b]
    // 0x1477b5c: StoreField: r0->field_6f = r1
    //     0x1477b5c: stur            w1, [x0, #0x6f]
    // 0x1477b60: r5 = false
    //     0x1477b60: add             x5, NULL, #0x30  ; false
    // 0x1477b64: StoreField: r0->field_73 = r5
    //     0x1477b64: stur            w5, [x0, #0x73]
    // 0x1477b68: StoreField: r0->field_83 = r1
    //     0x1477b68: stur            w1, [x0, #0x83]
    // 0x1477b6c: StoreField: r0->field_7b = r5
    //     0x1477b6c: stur            w5, [x0, #0x7b]
    // 0x1477b70: r0 = Visibility()
    //     0x1477b70: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1477b74: mov             x3, x0
    // 0x1477b78: ldur            x0, [fp, #-0x20]
    // 0x1477b7c: stur            x3, [fp, #-0x18]
    // 0x1477b80: StoreField: r3->field_b = r0
    //     0x1477b80: stur            w0, [x3, #0xb]
    // 0x1477b84: r0 = Instance_SizedBox
    //     0x1477b84: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1477b88: StoreField: r3->field_f = r0
    //     0x1477b88: stur            w0, [x3, #0xf]
    // 0x1477b8c: ldur            x1, [fp, #-0x10]
    // 0x1477b90: StoreField: r3->field_13 = r1
    //     0x1477b90: stur            w1, [x3, #0x13]
    // 0x1477b94: r4 = false
    //     0x1477b94: add             x4, NULL, #0x30  ; false
    // 0x1477b98: ArrayStore: r3[0] = r4  ; List_4
    //     0x1477b98: stur            w4, [x3, #0x17]
    // 0x1477b9c: StoreField: r3->field_1b = r4
    //     0x1477b9c: stur            w4, [x3, #0x1b]
    // 0x1477ba0: StoreField: r3->field_1f = r4
    //     0x1477ba0: stur            w4, [x3, #0x1f]
    // 0x1477ba4: StoreField: r3->field_23 = r4
    //     0x1477ba4: stur            w4, [x3, #0x23]
    // 0x1477ba8: StoreField: r3->field_27 = r4
    //     0x1477ba8: stur            w4, [x3, #0x27]
    // 0x1477bac: StoreField: r3->field_2b = r4
    //     0x1477bac: stur            w4, [x3, #0x2b]
    // 0x1477bb0: r1 = <Widget>
    //     0x1477bb0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1477bb4: r2 = 26
    //     0x1477bb4: movz            x2, #0x1a
    // 0x1477bb8: r0 = AllocateArray()
    //     0x1477bb8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1477bbc: mov             x2, x0
    // 0x1477bc0: ldur            x0, [fp, #-0x18]
    // 0x1477bc4: stur            x2, [fp, #-0x10]
    // 0x1477bc8: StoreField: r2->field_f = r0
    //     0x1477bc8: stur            w0, [x2, #0xf]
    // 0x1477bcc: ldur            x0, [fp, #-8]
    // 0x1477bd0: LoadField: r1 = r0->field_f
    //     0x1477bd0: ldur            w1, [x0, #0xf]
    // 0x1477bd4: DecompressPointer r1
    //     0x1477bd4: add             x1, x1, HEAP, lsl #32
    // 0x1477bd8: r0 = controller()
    //     0x1477bd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1477bdc: LoadField: r1 = r0->field_5b
    //     0x1477bdc: ldur            w1, [x0, #0x5b]
    // 0x1477be0: DecompressPointer r1
    //     0x1477be0: add             x1, x1, HEAP, lsl #32
    // 0x1477be4: r0 = value()
    //     0x1477be4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1477be8: eor             x1, x0, #0x10
    // 0x1477bec: stur            x1, [fp, #-0x18]
    // 0x1477bf0: r0 = Visibility()
    //     0x1477bf0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1477bf4: mov             x1, x0
    // 0x1477bf8: r0 = Instance_SizedBox
    //     0x1477bf8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x1477bfc: ldr             x0, [x0, #0xc50]
    // 0x1477c00: StoreField: r1->field_b = r0
    //     0x1477c00: stur            w0, [x1, #0xb]
    // 0x1477c04: r2 = Instance_SizedBox
    //     0x1477c04: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1477c08: StoreField: r1->field_f = r2
    //     0x1477c08: stur            w2, [x1, #0xf]
    // 0x1477c0c: ldur            x0, [fp, #-0x18]
    // 0x1477c10: StoreField: r1->field_13 = r0
    //     0x1477c10: stur            w0, [x1, #0x13]
    // 0x1477c14: r3 = false
    //     0x1477c14: add             x3, NULL, #0x30  ; false
    // 0x1477c18: ArrayStore: r1[0] = r3  ; List_4
    //     0x1477c18: stur            w3, [x1, #0x17]
    // 0x1477c1c: StoreField: r1->field_1b = r3
    //     0x1477c1c: stur            w3, [x1, #0x1b]
    // 0x1477c20: StoreField: r1->field_1f = r3
    //     0x1477c20: stur            w3, [x1, #0x1f]
    // 0x1477c24: StoreField: r1->field_23 = r3
    //     0x1477c24: stur            w3, [x1, #0x23]
    // 0x1477c28: StoreField: r1->field_27 = r3
    //     0x1477c28: stur            w3, [x1, #0x27]
    // 0x1477c2c: StoreField: r1->field_2b = r3
    //     0x1477c2c: stur            w3, [x1, #0x2b]
    // 0x1477c30: mov             x0, x1
    // 0x1477c34: ldur            x1, [fp, #-0x10]
    // 0x1477c38: ArrayStore: r1[1] = r0  ; List_4
    //     0x1477c38: add             x25, x1, #0x13
    //     0x1477c3c: str             w0, [x25]
    //     0x1477c40: tbz             w0, #0, #0x1477c5c
    //     0x1477c44: ldurb           w16, [x1, #-1]
    //     0x1477c48: ldurb           w17, [x0, #-1]
    //     0x1477c4c: and             x16, x17, x16, lsr #2
    //     0x1477c50: tst             x16, HEAP, lsr #32
    //     0x1477c54: b.eq            #0x1477c5c
    //     0x1477c58: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1477c5c: ldur            x0, [fp, #-8]
    // 0x1477c60: LoadField: r1 = r0->field_13
    //     0x1477c60: ldur            w1, [x0, #0x13]
    // 0x1477c64: DecompressPointer r1
    //     0x1477c64: add             x1, x1, HEAP, lsl #32
    // 0x1477c68: r0 = of()
    //     0x1477c68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1477c6c: LoadField: r1 = r0->field_87
    //     0x1477c6c: ldur            w1, [x0, #0x87]
    // 0x1477c70: DecompressPointer r1
    //     0x1477c70: add             x1, x1, HEAP, lsl #32
    // 0x1477c74: LoadField: r0 = r1->field_7
    //     0x1477c74: ldur            w0, [x1, #7]
    // 0x1477c78: DecompressPointer r0
    //     0x1477c78: add             x0, x0, HEAP, lsl #32
    // 0x1477c7c: r16 = 14.000000
    //     0x1477c7c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1477c80: ldr             x16, [x16, #0x1d8]
    // 0x1477c84: str             x16, [SP]
    // 0x1477c88: mov             x1, x0
    // 0x1477c8c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x1477c8c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x1477c90: ldr             x4, [x4, #0x798]
    // 0x1477c94: r0 = copyWith()
    //     0x1477c94: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1477c98: stur            x0, [fp, #-0x18]
    // 0x1477c9c: r0 = Text()
    //     0x1477c9c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1477ca0: mov             x3, x0
    // 0x1477ca4: r0 = "About Us"
    //     0x1477ca4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c58] "About Us"
    //     0x1477ca8: ldr             x0, [x0, #0xc58]
    // 0x1477cac: stur            x3, [fp, #-0x20]
    // 0x1477cb0: StoreField: r3->field_b = r0
    //     0x1477cb0: stur            w0, [x3, #0xb]
    // 0x1477cb4: ldur            x0, [fp, #-0x18]
    // 0x1477cb8: StoreField: r3->field_13 = r0
    //     0x1477cb8: stur            w0, [x3, #0x13]
    // 0x1477cbc: r1 = Null
    //     0x1477cbc: mov             x1, NULL
    // 0x1477cc0: r2 = 2
    //     0x1477cc0: movz            x2, #0x2
    // 0x1477cc4: r0 = AllocateArray()
    //     0x1477cc4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1477cc8: mov             x2, x0
    // 0x1477ccc: ldur            x0, [fp, #-0x20]
    // 0x1477cd0: stur            x2, [fp, #-0x18]
    // 0x1477cd4: StoreField: r2->field_f = r0
    //     0x1477cd4: stur            w0, [x2, #0xf]
    // 0x1477cd8: r1 = <Widget>
    //     0x1477cd8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1477cdc: r0 = AllocateGrowableArray()
    //     0x1477cdc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1477ce0: mov             x1, x0
    // 0x1477ce4: ldur            x0, [fp, #-0x18]
    // 0x1477ce8: stur            x1, [fp, #-0x20]
    // 0x1477cec: StoreField: r1->field_f = r0
    //     0x1477cec: stur            w0, [x1, #0xf]
    // 0x1477cf0: r2 = 2
    //     0x1477cf0: movz            x2, #0x2
    // 0x1477cf4: StoreField: r1->field_b = r2
    //     0x1477cf4: stur            w2, [x1, #0xb]
    // 0x1477cf8: r0 = Row()
    //     0x1477cf8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1477cfc: mov             x1, x0
    // 0x1477d00: r0 = Instance_Axis
    //     0x1477d00: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1477d04: stur            x1, [fp, #-0x18]
    // 0x1477d08: StoreField: r1->field_f = r0
    //     0x1477d08: stur            w0, [x1, #0xf]
    // 0x1477d0c: r2 = Instance_MainAxisAlignment
    //     0x1477d0c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1477d10: ldr             x2, [x2, #0xa08]
    // 0x1477d14: StoreField: r1->field_13 = r2
    //     0x1477d14: stur            w2, [x1, #0x13]
    // 0x1477d18: r3 = Instance_MainAxisSize
    //     0x1477d18: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1477d1c: ldr             x3, [x3, #0xa10]
    // 0x1477d20: ArrayStore: r1[0] = r3  ; List_4
    //     0x1477d20: stur            w3, [x1, #0x17]
    // 0x1477d24: r4 = Instance_CrossAxisAlignment
    //     0x1477d24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1477d28: ldr             x4, [x4, #0xa18]
    // 0x1477d2c: StoreField: r1->field_1b = r4
    //     0x1477d2c: stur            w4, [x1, #0x1b]
    // 0x1477d30: r5 = Instance_VerticalDirection
    //     0x1477d30: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1477d34: ldr             x5, [x5, #0xa20]
    // 0x1477d38: StoreField: r1->field_23 = r5
    //     0x1477d38: stur            w5, [x1, #0x23]
    // 0x1477d3c: r6 = Instance_Clip
    //     0x1477d3c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1477d40: ldr             x6, [x6, #0x38]
    // 0x1477d44: StoreField: r1->field_2b = r6
    //     0x1477d44: stur            w6, [x1, #0x2b]
    // 0x1477d48: StoreField: r1->field_2f = rZR
    //     0x1477d48: stur            xzr, [x1, #0x2f]
    // 0x1477d4c: ldur            x7, [fp, #-0x20]
    // 0x1477d50: StoreField: r1->field_b = r7
    //     0x1477d50: stur            w7, [x1, #0xb]
    // 0x1477d54: r0 = InkWell()
    //     0x1477d54: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1477d58: mov             x3, x0
    // 0x1477d5c: ldur            x0, [fp, #-0x18]
    // 0x1477d60: stur            x3, [fp, #-0x20]
    // 0x1477d64: StoreField: r3->field_b = r0
    //     0x1477d64: stur            w0, [x3, #0xb]
    // 0x1477d68: ldur            x2, [fp, #-8]
    // 0x1477d6c: r1 = Function '<anonymous closure>':.
    //     0x1477d6c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40100] AnonymousClosure: (0x14788e8), in [package:customer_app/app/presentation/views/glass/profile/profile_view.dart] ProfileView::body (0x14fc37c)
    //     0x1477d70: ldr             x1, [x1, #0x100]
    // 0x1477d74: r0 = AllocateClosure()
    //     0x1477d74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1477d78: mov             x1, x0
    // 0x1477d7c: ldur            x0, [fp, #-0x20]
    // 0x1477d80: StoreField: r0->field_f = r1
    //     0x1477d80: stur            w1, [x0, #0xf]
    // 0x1477d84: r1 = true
    //     0x1477d84: add             x1, NULL, #0x20  ; true
    // 0x1477d88: StoreField: r0->field_43 = r1
    //     0x1477d88: stur            w1, [x0, #0x43]
    // 0x1477d8c: r2 = Instance_BoxShape
    //     0x1477d8c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1477d90: ldr             x2, [x2, #0x80]
    // 0x1477d94: StoreField: r0->field_47 = r2
    //     0x1477d94: stur            w2, [x0, #0x47]
    // 0x1477d98: r3 = Instance_Color
    //     0x1477d98: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x1477d9c: ldr             x3, [x3, #0xf88]
    // 0x1477da0: StoreField: r0->field_5f = r3
    //     0x1477da0: stur            w3, [x0, #0x5f]
    // 0x1477da4: r4 = Instance__NoSplashFactory
    //     0x1477da4: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x1477da8: ldr             x4, [x4, #0xc48]
    // 0x1477dac: StoreField: r0->field_6b = r4
    //     0x1477dac: stur            w4, [x0, #0x6b]
    // 0x1477db0: StoreField: r0->field_6f = r1
    //     0x1477db0: stur            w1, [x0, #0x6f]
    // 0x1477db4: r5 = false
    //     0x1477db4: add             x5, NULL, #0x30  ; false
    // 0x1477db8: StoreField: r0->field_73 = r5
    //     0x1477db8: stur            w5, [x0, #0x73]
    // 0x1477dbc: StoreField: r0->field_83 = r1
    //     0x1477dbc: stur            w1, [x0, #0x83]
    // 0x1477dc0: StoreField: r0->field_7b = r5
    //     0x1477dc0: stur            w5, [x0, #0x7b]
    // 0x1477dc4: r0 = Align()
    //     0x1477dc4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x1477dc8: r2 = Instance_Alignment
    //     0x1477dc8: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x1477dcc: ldr             x2, [x2, #0xfa0]
    // 0x1477dd0: StoreField: r0->field_f = r2
    //     0x1477dd0: stur            w2, [x0, #0xf]
    // 0x1477dd4: ldur            x1, [fp, #-0x20]
    // 0x1477dd8: StoreField: r0->field_b = r1
    //     0x1477dd8: stur            w1, [x0, #0xb]
    // 0x1477ddc: ldur            x1, [fp, #-0x10]
    // 0x1477de0: ArrayStore: r1[2] = r0  ; List_4
    //     0x1477de0: add             x25, x1, #0x17
    //     0x1477de4: str             w0, [x25]
    //     0x1477de8: tbz             w0, #0, #0x1477e04
    //     0x1477dec: ldurb           w16, [x1, #-1]
    //     0x1477df0: ldurb           w17, [x0, #-1]
    //     0x1477df4: and             x16, x17, x16, lsr #2
    //     0x1477df8: tst             x16, HEAP, lsr #32
    //     0x1477dfc: b.eq            #0x1477e04
    //     0x1477e00: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1477e04: ldur            x0, [fp, #-0x10]
    // 0x1477e08: r16 = Instance_SizedBox
    //     0x1477e08: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x1477e0c: ldr             x16, [x16, #0xc50]
    // 0x1477e10: StoreField: r0->field_1b = r16
    //     0x1477e10: stur            w16, [x0, #0x1b]
    // 0x1477e14: ldur            x3, [fp, #-8]
    // 0x1477e18: LoadField: r1 = r3->field_13
    //     0x1477e18: ldur            w1, [x3, #0x13]
    // 0x1477e1c: DecompressPointer r1
    //     0x1477e1c: add             x1, x1, HEAP, lsl #32
    // 0x1477e20: r0 = of()
    //     0x1477e20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1477e24: LoadField: r1 = r0->field_87
    //     0x1477e24: ldur            w1, [x0, #0x87]
    // 0x1477e28: DecompressPointer r1
    //     0x1477e28: add             x1, x1, HEAP, lsl #32
    // 0x1477e2c: LoadField: r0 = r1->field_7
    //     0x1477e2c: ldur            w0, [x1, #7]
    // 0x1477e30: DecompressPointer r0
    //     0x1477e30: add             x0, x0, HEAP, lsl #32
    // 0x1477e34: r16 = 14.000000
    //     0x1477e34: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1477e38: ldr             x16, [x16, #0x1d8]
    // 0x1477e3c: str             x16, [SP]
    // 0x1477e40: mov             x1, x0
    // 0x1477e44: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x1477e44: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x1477e48: ldr             x4, [x4, #0x798]
    // 0x1477e4c: r0 = copyWith()
    //     0x1477e4c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1477e50: stur            x0, [fp, #-0x18]
    // 0x1477e54: r0 = Text()
    //     0x1477e54: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1477e58: mov             x3, x0
    // 0x1477e5c: r0 = "Privacy Policy"
    //     0x1477e5c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c68] "Privacy Policy"
    //     0x1477e60: ldr             x0, [x0, #0xc68]
    // 0x1477e64: stur            x3, [fp, #-0x20]
    // 0x1477e68: StoreField: r3->field_b = r0
    //     0x1477e68: stur            w0, [x3, #0xb]
    // 0x1477e6c: ldur            x0, [fp, #-0x18]
    // 0x1477e70: StoreField: r3->field_13 = r0
    //     0x1477e70: stur            w0, [x3, #0x13]
    // 0x1477e74: r1 = Null
    //     0x1477e74: mov             x1, NULL
    // 0x1477e78: r2 = 2
    //     0x1477e78: movz            x2, #0x2
    // 0x1477e7c: r0 = AllocateArray()
    //     0x1477e7c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1477e80: mov             x2, x0
    // 0x1477e84: ldur            x0, [fp, #-0x20]
    // 0x1477e88: stur            x2, [fp, #-0x18]
    // 0x1477e8c: StoreField: r2->field_f = r0
    //     0x1477e8c: stur            w0, [x2, #0xf]
    // 0x1477e90: r1 = <Widget>
    //     0x1477e90: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1477e94: r0 = AllocateGrowableArray()
    //     0x1477e94: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1477e98: mov             x1, x0
    // 0x1477e9c: ldur            x0, [fp, #-0x18]
    // 0x1477ea0: stur            x1, [fp, #-0x20]
    // 0x1477ea4: StoreField: r1->field_f = r0
    //     0x1477ea4: stur            w0, [x1, #0xf]
    // 0x1477ea8: r2 = 2
    //     0x1477ea8: movz            x2, #0x2
    // 0x1477eac: StoreField: r1->field_b = r2
    //     0x1477eac: stur            w2, [x1, #0xb]
    // 0x1477eb0: r0 = Row()
    //     0x1477eb0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1477eb4: mov             x1, x0
    // 0x1477eb8: r0 = Instance_Axis
    //     0x1477eb8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1477ebc: stur            x1, [fp, #-0x18]
    // 0x1477ec0: StoreField: r1->field_f = r0
    //     0x1477ec0: stur            w0, [x1, #0xf]
    // 0x1477ec4: r2 = Instance_MainAxisAlignment
    //     0x1477ec4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1477ec8: ldr             x2, [x2, #0xa08]
    // 0x1477ecc: StoreField: r1->field_13 = r2
    //     0x1477ecc: stur            w2, [x1, #0x13]
    // 0x1477ed0: r3 = Instance_MainAxisSize
    //     0x1477ed0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1477ed4: ldr             x3, [x3, #0xa10]
    // 0x1477ed8: ArrayStore: r1[0] = r3  ; List_4
    //     0x1477ed8: stur            w3, [x1, #0x17]
    // 0x1477edc: r4 = Instance_CrossAxisAlignment
    //     0x1477edc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1477ee0: ldr             x4, [x4, #0xa18]
    // 0x1477ee4: StoreField: r1->field_1b = r4
    //     0x1477ee4: stur            w4, [x1, #0x1b]
    // 0x1477ee8: r5 = Instance_VerticalDirection
    //     0x1477ee8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1477eec: ldr             x5, [x5, #0xa20]
    // 0x1477ef0: StoreField: r1->field_23 = r5
    //     0x1477ef0: stur            w5, [x1, #0x23]
    // 0x1477ef4: r6 = Instance_Clip
    //     0x1477ef4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1477ef8: ldr             x6, [x6, #0x38]
    // 0x1477efc: StoreField: r1->field_2b = r6
    //     0x1477efc: stur            w6, [x1, #0x2b]
    // 0x1477f00: StoreField: r1->field_2f = rZR
    //     0x1477f00: stur            xzr, [x1, #0x2f]
    // 0x1477f04: ldur            x7, [fp, #-0x20]
    // 0x1477f08: StoreField: r1->field_b = r7
    //     0x1477f08: stur            w7, [x1, #0xb]
    // 0x1477f0c: r0 = InkWell()
    //     0x1477f0c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1477f10: mov             x3, x0
    // 0x1477f14: ldur            x0, [fp, #-0x18]
    // 0x1477f18: stur            x3, [fp, #-0x20]
    // 0x1477f1c: StoreField: r3->field_b = r0
    //     0x1477f1c: stur            w0, [x3, #0xb]
    // 0x1477f20: ldur            x2, [fp, #-8]
    // 0x1477f24: r1 = Function '<anonymous closure>':.
    //     0x1477f24: add             x1, PP, #0x40, lsl #12  ; [pp+0x40108] AnonymousClosure: (0x1478898), in [package:customer_app/app/presentation/views/glass/profile/profile_view.dart] ProfileView::body (0x14fc37c)
    //     0x1477f28: ldr             x1, [x1, #0x108]
    // 0x1477f2c: r0 = AllocateClosure()
    //     0x1477f2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1477f30: mov             x1, x0
    // 0x1477f34: ldur            x0, [fp, #-0x20]
    // 0x1477f38: StoreField: r0->field_f = r1
    //     0x1477f38: stur            w1, [x0, #0xf]
    // 0x1477f3c: r1 = true
    //     0x1477f3c: add             x1, NULL, #0x20  ; true
    // 0x1477f40: StoreField: r0->field_43 = r1
    //     0x1477f40: stur            w1, [x0, #0x43]
    // 0x1477f44: r2 = Instance_BoxShape
    //     0x1477f44: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1477f48: ldr             x2, [x2, #0x80]
    // 0x1477f4c: StoreField: r0->field_47 = r2
    //     0x1477f4c: stur            w2, [x0, #0x47]
    // 0x1477f50: r3 = Instance_Color
    //     0x1477f50: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x1477f54: ldr             x3, [x3, #0xf88]
    // 0x1477f58: StoreField: r0->field_5f = r3
    //     0x1477f58: stur            w3, [x0, #0x5f]
    // 0x1477f5c: r4 = Instance__NoSplashFactory
    //     0x1477f5c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x1477f60: ldr             x4, [x4, #0xc48]
    // 0x1477f64: StoreField: r0->field_6b = r4
    //     0x1477f64: stur            w4, [x0, #0x6b]
    // 0x1477f68: StoreField: r0->field_6f = r1
    //     0x1477f68: stur            w1, [x0, #0x6f]
    // 0x1477f6c: r5 = false
    //     0x1477f6c: add             x5, NULL, #0x30  ; false
    // 0x1477f70: StoreField: r0->field_73 = r5
    //     0x1477f70: stur            w5, [x0, #0x73]
    // 0x1477f74: StoreField: r0->field_83 = r1
    //     0x1477f74: stur            w1, [x0, #0x83]
    // 0x1477f78: StoreField: r0->field_7b = r5
    //     0x1477f78: stur            w5, [x0, #0x7b]
    // 0x1477f7c: r0 = Align()
    //     0x1477f7c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x1477f80: r2 = Instance_Alignment
    //     0x1477f80: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x1477f84: ldr             x2, [x2, #0xfa0]
    // 0x1477f88: StoreField: r0->field_f = r2
    //     0x1477f88: stur            w2, [x0, #0xf]
    // 0x1477f8c: ldur            x1, [fp, #-0x20]
    // 0x1477f90: StoreField: r0->field_b = r1
    //     0x1477f90: stur            w1, [x0, #0xb]
    // 0x1477f94: ldur            x1, [fp, #-0x10]
    // 0x1477f98: ArrayStore: r1[4] = r0  ; List_4
    //     0x1477f98: add             x25, x1, #0x1f
    //     0x1477f9c: str             w0, [x25]
    //     0x1477fa0: tbz             w0, #0, #0x1477fbc
    //     0x1477fa4: ldurb           w16, [x1, #-1]
    //     0x1477fa8: ldurb           w17, [x0, #-1]
    //     0x1477fac: and             x16, x17, x16, lsr #2
    //     0x1477fb0: tst             x16, HEAP, lsr #32
    //     0x1477fb4: b.eq            #0x1477fbc
    //     0x1477fb8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1477fbc: ldur            x0, [fp, #-0x10]
    // 0x1477fc0: r16 = Instance_SizedBox
    //     0x1477fc0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x1477fc4: ldr             x16, [x16, #0xc50]
    // 0x1477fc8: StoreField: r0->field_23 = r16
    //     0x1477fc8: stur            w16, [x0, #0x23]
    // 0x1477fcc: ldur            x3, [fp, #-8]
    // 0x1477fd0: LoadField: r1 = r3->field_13
    //     0x1477fd0: ldur            w1, [x3, #0x13]
    // 0x1477fd4: DecompressPointer r1
    //     0x1477fd4: add             x1, x1, HEAP, lsl #32
    // 0x1477fd8: r0 = of()
    //     0x1477fd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1477fdc: LoadField: r1 = r0->field_87
    //     0x1477fdc: ldur            w1, [x0, #0x87]
    // 0x1477fe0: DecompressPointer r1
    //     0x1477fe0: add             x1, x1, HEAP, lsl #32
    // 0x1477fe4: LoadField: r0 = r1->field_7
    //     0x1477fe4: ldur            w0, [x1, #7]
    // 0x1477fe8: DecompressPointer r0
    //     0x1477fe8: add             x0, x0, HEAP, lsl #32
    // 0x1477fec: r16 = 14.000000
    //     0x1477fec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1477ff0: ldr             x16, [x16, #0x1d8]
    // 0x1477ff4: str             x16, [SP]
    // 0x1477ff8: mov             x1, x0
    // 0x1477ffc: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x1477ffc: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x1478000: ldr             x4, [x4, #0x798]
    // 0x1478004: r0 = copyWith()
    //     0x1478004: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1478008: stur            x0, [fp, #-0x18]
    // 0x147800c: r0 = Text()
    //     0x147800c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1478010: mov             x3, x0
    // 0x1478014: r0 = "Return Policy"
    //     0x1478014: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c78] "Return Policy"
    //     0x1478018: ldr             x0, [x0, #0xc78]
    // 0x147801c: stur            x3, [fp, #-0x20]
    // 0x1478020: StoreField: r3->field_b = r0
    //     0x1478020: stur            w0, [x3, #0xb]
    // 0x1478024: ldur            x0, [fp, #-0x18]
    // 0x1478028: StoreField: r3->field_13 = r0
    //     0x1478028: stur            w0, [x3, #0x13]
    // 0x147802c: r1 = Null
    //     0x147802c: mov             x1, NULL
    // 0x1478030: r2 = 2
    //     0x1478030: movz            x2, #0x2
    // 0x1478034: r0 = AllocateArray()
    //     0x1478034: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1478038: mov             x2, x0
    // 0x147803c: ldur            x0, [fp, #-0x20]
    // 0x1478040: stur            x2, [fp, #-0x18]
    // 0x1478044: StoreField: r2->field_f = r0
    //     0x1478044: stur            w0, [x2, #0xf]
    // 0x1478048: r1 = <Widget>
    //     0x1478048: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x147804c: r0 = AllocateGrowableArray()
    //     0x147804c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1478050: mov             x1, x0
    // 0x1478054: ldur            x0, [fp, #-0x18]
    // 0x1478058: stur            x1, [fp, #-0x20]
    // 0x147805c: StoreField: r1->field_f = r0
    //     0x147805c: stur            w0, [x1, #0xf]
    // 0x1478060: r2 = 2
    //     0x1478060: movz            x2, #0x2
    // 0x1478064: StoreField: r1->field_b = r2
    //     0x1478064: stur            w2, [x1, #0xb]
    // 0x1478068: r0 = Row()
    //     0x1478068: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x147806c: mov             x1, x0
    // 0x1478070: r0 = Instance_Axis
    //     0x1478070: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1478074: stur            x1, [fp, #-0x18]
    // 0x1478078: StoreField: r1->field_f = r0
    //     0x1478078: stur            w0, [x1, #0xf]
    // 0x147807c: r2 = Instance_MainAxisAlignment
    //     0x147807c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1478080: ldr             x2, [x2, #0xa08]
    // 0x1478084: StoreField: r1->field_13 = r2
    //     0x1478084: stur            w2, [x1, #0x13]
    // 0x1478088: r3 = Instance_MainAxisSize
    //     0x1478088: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x147808c: ldr             x3, [x3, #0xa10]
    // 0x1478090: ArrayStore: r1[0] = r3  ; List_4
    //     0x1478090: stur            w3, [x1, #0x17]
    // 0x1478094: r4 = Instance_CrossAxisAlignment
    //     0x1478094: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1478098: ldr             x4, [x4, #0xa18]
    // 0x147809c: StoreField: r1->field_1b = r4
    //     0x147809c: stur            w4, [x1, #0x1b]
    // 0x14780a0: r5 = Instance_VerticalDirection
    //     0x14780a0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14780a4: ldr             x5, [x5, #0xa20]
    // 0x14780a8: StoreField: r1->field_23 = r5
    //     0x14780a8: stur            w5, [x1, #0x23]
    // 0x14780ac: r6 = Instance_Clip
    //     0x14780ac: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14780b0: ldr             x6, [x6, #0x38]
    // 0x14780b4: StoreField: r1->field_2b = r6
    //     0x14780b4: stur            w6, [x1, #0x2b]
    // 0x14780b8: StoreField: r1->field_2f = rZR
    //     0x14780b8: stur            xzr, [x1, #0x2f]
    // 0x14780bc: ldur            x7, [fp, #-0x20]
    // 0x14780c0: StoreField: r1->field_b = r7
    //     0x14780c0: stur            w7, [x1, #0xb]
    // 0x14780c4: r0 = InkWell()
    //     0x14780c4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14780c8: mov             x3, x0
    // 0x14780cc: ldur            x0, [fp, #-0x18]
    // 0x14780d0: stur            x3, [fp, #-0x20]
    // 0x14780d4: StoreField: r3->field_b = r0
    //     0x14780d4: stur            w0, [x3, #0xb]
    // 0x14780d8: ldur            x2, [fp, #-8]
    // 0x14780dc: r1 = Function '<anonymous closure>':.
    //     0x14780dc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40110] AnonymousClosure: (0x1478848), in [package:customer_app/app/presentation/views/glass/profile/profile_view.dart] ProfileView::body (0x14fc37c)
    //     0x14780e0: ldr             x1, [x1, #0x110]
    // 0x14780e4: r0 = AllocateClosure()
    //     0x14780e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14780e8: mov             x1, x0
    // 0x14780ec: ldur            x0, [fp, #-0x20]
    // 0x14780f0: StoreField: r0->field_f = r1
    //     0x14780f0: stur            w1, [x0, #0xf]
    // 0x14780f4: r1 = true
    //     0x14780f4: add             x1, NULL, #0x20  ; true
    // 0x14780f8: StoreField: r0->field_43 = r1
    //     0x14780f8: stur            w1, [x0, #0x43]
    // 0x14780fc: r2 = Instance_BoxShape
    //     0x14780fc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1478100: ldr             x2, [x2, #0x80]
    // 0x1478104: StoreField: r0->field_47 = r2
    //     0x1478104: stur            w2, [x0, #0x47]
    // 0x1478108: r3 = Instance_Color
    //     0x1478108: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x147810c: ldr             x3, [x3, #0xf88]
    // 0x1478110: StoreField: r0->field_5f = r3
    //     0x1478110: stur            w3, [x0, #0x5f]
    // 0x1478114: r4 = Instance__NoSplashFactory
    //     0x1478114: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x1478118: ldr             x4, [x4, #0xc48]
    // 0x147811c: StoreField: r0->field_6b = r4
    //     0x147811c: stur            w4, [x0, #0x6b]
    // 0x1478120: StoreField: r0->field_6f = r1
    //     0x1478120: stur            w1, [x0, #0x6f]
    // 0x1478124: r5 = false
    //     0x1478124: add             x5, NULL, #0x30  ; false
    // 0x1478128: StoreField: r0->field_73 = r5
    //     0x1478128: stur            w5, [x0, #0x73]
    // 0x147812c: StoreField: r0->field_83 = r1
    //     0x147812c: stur            w1, [x0, #0x83]
    // 0x1478130: StoreField: r0->field_7b = r5
    //     0x1478130: stur            w5, [x0, #0x7b]
    // 0x1478134: r0 = Align()
    //     0x1478134: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x1478138: r2 = Instance_Alignment
    //     0x1478138: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x147813c: ldr             x2, [x2, #0xfa0]
    // 0x1478140: StoreField: r0->field_f = r2
    //     0x1478140: stur            w2, [x0, #0xf]
    // 0x1478144: ldur            x1, [fp, #-0x20]
    // 0x1478148: StoreField: r0->field_b = r1
    //     0x1478148: stur            w1, [x0, #0xb]
    // 0x147814c: ldur            x1, [fp, #-0x10]
    // 0x1478150: ArrayStore: r1[6] = r0  ; List_4
    //     0x1478150: add             x25, x1, #0x27
    //     0x1478154: str             w0, [x25]
    //     0x1478158: tbz             w0, #0, #0x1478174
    //     0x147815c: ldurb           w16, [x1, #-1]
    //     0x1478160: ldurb           w17, [x0, #-1]
    //     0x1478164: and             x16, x17, x16, lsr #2
    //     0x1478168: tst             x16, HEAP, lsr #32
    //     0x147816c: b.eq            #0x1478174
    //     0x1478170: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1478174: ldur            x0, [fp, #-0x10]
    // 0x1478178: r16 = Instance_SizedBox
    //     0x1478178: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x147817c: ldr             x16, [x16, #0xc50]
    // 0x1478180: StoreField: r0->field_2b = r16
    //     0x1478180: stur            w16, [x0, #0x2b]
    // 0x1478184: ldur            x3, [fp, #-8]
    // 0x1478188: LoadField: r1 = r3->field_13
    //     0x1478188: ldur            w1, [x3, #0x13]
    // 0x147818c: DecompressPointer r1
    //     0x147818c: add             x1, x1, HEAP, lsl #32
    // 0x1478190: r0 = of()
    //     0x1478190: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1478194: LoadField: r1 = r0->field_87
    //     0x1478194: ldur            w1, [x0, #0x87]
    // 0x1478198: DecompressPointer r1
    //     0x1478198: add             x1, x1, HEAP, lsl #32
    // 0x147819c: LoadField: r0 = r1->field_7
    //     0x147819c: ldur            w0, [x1, #7]
    // 0x14781a0: DecompressPointer r0
    //     0x14781a0: add             x0, x0, HEAP, lsl #32
    // 0x14781a4: r16 = 14.000000
    //     0x14781a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14781a8: ldr             x16, [x16, #0x1d8]
    // 0x14781ac: str             x16, [SP]
    // 0x14781b0: mov             x1, x0
    // 0x14781b4: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x14781b4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x14781b8: ldr             x4, [x4, #0x798]
    // 0x14781bc: r0 = copyWith()
    //     0x14781bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14781c0: stur            x0, [fp, #-0x18]
    // 0x14781c4: r0 = Text()
    //     0x14781c4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14781c8: mov             x3, x0
    // 0x14781cc: r0 = "Shipping Policy"
    //     0x14781cc: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c88] "Shipping Policy"
    //     0x14781d0: ldr             x0, [x0, #0xc88]
    // 0x14781d4: stur            x3, [fp, #-0x20]
    // 0x14781d8: StoreField: r3->field_b = r0
    //     0x14781d8: stur            w0, [x3, #0xb]
    // 0x14781dc: ldur            x0, [fp, #-0x18]
    // 0x14781e0: StoreField: r3->field_13 = r0
    //     0x14781e0: stur            w0, [x3, #0x13]
    // 0x14781e4: r1 = Null
    //     0x14781e4: mov             x1, NULL
    // 0x14781e8: r2 = 2
    //     0x14781e8: movz            x2, #0x2
    // 0x14781ec: r0 = AllocateArray()
    //     0x14781ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14781f0: mov             x2, x0
    // 0x14781f4: ldur            x0, [fp, #-0x20]
    // 0x14781f8: stur            x2, [fp, #-0x18]
    // 0x14781fc: StoreField: r2->field_f = r0
    //     0x14781fc: stur            w0, [x2, #0xf]
    // 0x1478200: r1 = <Widget>
    //     0x1478200: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1478204: r0 = AllocateGrowableArray()
    //     0x1478204: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1478208: mov             x1, x0
    // 0x147820c: ldur            x0, [fp, #-0x18]
    // 0x1478210: stur            x1, [fp, #-0x20]
    // 0x1478214: StoreField: r1->field_f = r0
    //     0x1478214: stur            w0, [x1, #0xf]
    // 0x1478218: r2 = 2
    //     0x1478218: movz            x2, #0x2
    // 0x147821c: StoreField: r1->field_b = r2
    //     0x147821c: stur            w2, [x1, #0xb]
    // 0x1478220: r0 = Row()
    //     0x1478220: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1478224: mov             x1, x0
    // 0x1478228: r0 = Instance_Axis
    //     0x1478228: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x147822c: stur            x1, [fp, #-0x18]
    // 0x1478230: StoreField: r1->field_f = r0
    //     0x1478230: stur            w0, [x1, #0xf]
    // 0x1478234: r2 = Instance_MainAxisAlignment
    //     0x1478234: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1478238: ldr             x2, [x2, #0xa08]
    // 0x147823c: StoreField: r1->field_13 = r2
    //     0x147823c: stur            w2, [x1, #0x13]
    // 0x1478240: r3 = Instance_MainAxisSize
    //     0x1478240: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1478244: ldr             x3, [x3, #0xa10]
    // 0x1478248: ArrayStore: r1[0] = r3  ; List_4
    //     0x1478248: stur            w3, [x1, #0x17]
    // 0x147824c: r4 = Instance_CrossAxisAlignment
    //     0x147824c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1478250: ldr             x4, [x4, #0xa18]
    // 0x1478254: StoreField: r1->field_1b = r4
    //     0x1478254: stur            w4, [x1, #0x1b]
    // 0x1478258: r5 = Instance_VerticalDirection
    //     0x1478258: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x147825c: ldr             x5, [x5, #0xa20]
    // 0x1478260: StoreField: r1->field_23 = r5
    //     0x1478260: stur            w5, [x1, #0x23]
    // 0x1478264: r6 = Instance_Clip
    //     0x1478264: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1478268: ldr             x6, [x6, #0x38]
    // 0x147826c: StoreField: r1->field_2b = r6
    //     0x147826c: stur            w6, [x1, #0x2b]
    // 0x1478270: StoreField: r1->field_2f = rZR
    //     0x1478270: stur            xzr, [x1, #0x2f]
    // 0x1478274: ldur            x7, [fp, #-0x20]
    // 0x1478278: StoreField: r1->field_b = r7
    //     0x1478278: stur            w7, [x1, #0xb]
    // 0x147827c: r0 = InkWell()
    //     0x147827c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1478280: mov             x3, x0
    // 0x1478284: ldur            x0, [fp, #-0x18]
    // 0x1478288: stur            x3, [fp, #-0x20]
    // 0x147828c: StoreField: r3->field_b = r0
    //     0x147828c: stur            w0, [x3, #0xb]
    // 0x1478290: ldur            x2, [fp, #-8]
    // 0x1478294: r1 = Function '<anonymous closure>':.
    //     0x1478294: add             x1, PP, #0x40, lsl #12  ; [pp+0x40118] AnonymousClosure: (0x14787f8), in [package:customer_app/app/presentation/views/glass/profile/profile_view.dart] ProfileView::body (0x14fc37c)
    //     0x1478298: ldr             x1, [x1, #0x118]
    // 0x147829c: r0 = AllocateClosure()
    //     0x147829c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14782a0: mov             x1, x0
    // 0x14782a4: ldur            x0, [fp, #-0x20]
    // 0x14782a8: StoreField: r0->field_f = r1
    //     0x14782a8: stur            w1, [x0, #0xf]
    // 0x14782ac: r1 = true
    //     0x14782ac: add             x1, NULL, #0x20  ; true
    // 0x14782b0: StoreField: r0->field_43 = r1
    //     0x14782b0: stur            w1, [x0, #0x43]
    // 0x14782b4: r2 = Instance_BoxShape
    //     0x14782b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14782b8: ldr             x2, [x2, #0x80]
    // 0x14782bc: StoreField: r0->field_47 = r2
    //     0x14782bc: stur            w2, [x0, #0x47]
    // 0x14782c0: r3 = Instance_Color
    //     0x14782c0: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x14782c4: ldr             x3, [x3, #0xf88]
    // 0x14782c8: StoreField: r0->field_5f = r3
    //     0x14782c8: stur            w3, [x0, #0x5f]
    // 0x14782cc: r4 = Instance__NoSplashFactory
    //     0x14782cc: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x14782d0: ldr             x4, [x4, #0xc48]
    // 0x14782d4: StoreField: r0->field_6b = r4
    //     0x14782d4: stur            w4, [x0, #0x6b]
    // 0x14782d8: StoreField: r0->field_6f = r1
    //     0x14782d8: stur            w1, [x0, #0x6f]
    // 0x14782dc: r5 = false
    //     0x14782dc: add             x5, NULL, #0x30  ; false
    // 0x14782e0: StoreField: r0->field_73 = r5
    //     0x14782e0: stur            w5, [x0, #0x73]
    // 0x14782e4: StoreField: r0->field_83 = r1
    //     0x14782e4: stur            w1, [x0, #0x83]
    // 0x14782e8: StoreField: r0->field_7b = r5
    //     0x14782e8: stur            w5, [x0, #0x7b]
    // 0x14782ec: r0 = Align()
    //     0x14782ec: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14782f0: r2 = Instance_Alignment
    //     0x14782f0: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x14782f4: ldr             x2, [x2, #0xfa0]
    // 0x14782f8: StoreField: r0->field_f = r2
    //     0x14782f8: stur            w2, [x0, #0xf]
    // 0x14782fc: ldur            x1, [fp, #-0x20]
    // 0x1478300: StoreField: r0->field_b = r1
    //     0x1478300: stur            w1, [x0, #0xb]
    // 0x1478304: ldur            x1, [fp, #-0x10]
    // 0x1478308: ArrayStore: r1[8] = r0  ; List_4
    //     0x1478308: add             x25, x1, #0x2f
    //     0x147830c: str             w0, [x25]
    //     0x1478310: tbz             w0, #0, #0x147832c
    //     0x1478314: ldurb           w16, [x1, #-1]
    //     0x1478318: ldurb           w17, [x0, #-1]
    //     0x147831c: and             x16, x17, x16, lsr #2
    //     0x1478320: tst             x16, HEAP, lsr #32
    //     0x1478324: b.eq            #0x147832c
    //     0x1478328: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x147832c: ldur            x0, [fp, #-0x10]
    // 0x1478330: r16 = Instance_SizedBox
    //     0x1478330: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x1478334: ldr             x16, [x16, #0xc50]
    // 0x1478338: StoreField: r0->field_33 = r16
    //     0x1478338: stur            w16, [x0, #0x33]
    // 0x147833c: ldur            x3, [fp, #-8]
    // 0x1478340: LoadField: r1 = r3->field_13
    //     0x1478340: ldur            w1, [x3, #0x13]
    // 0x1478344: DecompressPointer r1
    //     0x1478344: add             x1, x1, HEAP, lsl #32
    // 0x1478348: r0 = of()
    //     0x1478348: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x147834c: LoadField: r1 = r0->field_87
    //     0x147834c: ldur            w1, [x0, #0x87]
    // 0x1478350: DecompressPointer r1
    //     0x1478350: add             x1, x1, HEAP, lsl #32
    // 0x1478354: LoadField: r0 = r1->field_7
    //     0x1478354: ldur            w0, [x1, #7]
    // 0x1478358: DecompressPointer r0
    //     0x1478358: add             x0, x0, HEAP, lsl #32
    // 0x147835c: r16 = 14.000000
    //     0x147835c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1478360: ldr             x16, [x16, #0x1d8]
    // 0x1478364: str             x16, [SP]
    // 0x1478368: mov             x1, x0
    // 0x147836c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x147836c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x1478370: ldr             x4, [x4, #0x798]
    // 0x1478374: r0 = copyWith()
    //     0x1478374: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1478378: stur            x0, [fp, #-0x18]
    // 0x147837c: r0 = Text()
    //     0x147837c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1478380: mov             x3, x0
    // 0x1478384: r0 = "Terms & Condition"
    //     0x1478384: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c98] "Terms & Condition"
    //     0x1478388: ldr             x0, [x0, #0xc98]
    // 0x147838c: stur            x3, [fp, #-0x20]
    // 0x1478390: StoreField: r3->field_b = r0
    //     0x1478390: stur            w0, [x3, #0xb]
    // 0x1478394: ldur            x0, [fp, #-0x18]
    // 0x1478398: StoreField: r3->field_13 = r0
    //     0x1478398: stur            w0, [x3, #0x13]
    // 0x147839c: r1 = Null
    //     0x147839c: mov             x1, NULL
    // 0x14783a0: r2 = 2
    //     0x14783a0: movz            x2, #0x2
    // 0x14783a4: r0 = AllocateArray()
    //     0x14783a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14783a8: mov             x2, x0
    // 0x14783ac: ldur            x0, [fp, #-0x20]
    // 0x14783b0: stur            x2, [fp, #-0x18]
    // 0x14783b4: StoreField: r2->field_f = r0
    //     0x14783b4: stur            w0, [x2, #0xf]
    // 0x14783b8: r1 = <Widget>
    //     0x14783b8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14783bc: r0 = AllocateGrowableArray()
    //     0x14783bc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14783c0: mov             x1, x0
    // 0x14783c4: ldur            x0, [fp, #-0x18]
    // 0x14783c8: stur            x1, [fp, #-0x20]
    // 0x14783cc: StoreField: r1->field_f = r0
    //     0x14783cc: stur            w0, [x1, #0xf]
    // 0x14783d0: r0 = 2
    //     0x14783d0: movz            x0, #0x2
    // 0x14783d4: StoreField: r1->field_b = r0
    //     0x14783d4: stur            w0, [x1, #0xb]
    // 0x14783d8: r0 = Row()
    //     0x14783d8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14783dc: mov             x1, x0
    // 0x14783e0: r0 = Instance_Axis
    //     0x14783e0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14783e4: stur            x1, [fp, #-0x18]
    // 0x14783e8: StoreField: r1->field_f = r0
    //     0x14783e8: stur            w0, [x1, #0xf]
    // 0x14783ec: r2 = Instance_MainAxisAlignment
    //     0x14783ec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14783f0: ldr             x2, [x2, #0xa08]
    // 0x14783f4: StoreField: r1->field_13 = r2
    //     0x14783f4: stur            w2, [x1, #0x13]
    // 0x14783f8: r3 = Instance_MainAxisSize
    //     0x14783f8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14783fc: ldr             x3, [x3, #0xa10]
    // 0x1478400: ArrayStore: r1[0] = r3  ; List_4
    //     0x1478400: stur            w3, [x1, #0x17]
    // 0x1478404: r4 = Instance_CrossAxisAlignment
    //     0x1478404: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1478408: ldr             x4, [x4, #0xa18]
    // 0x147840c: StoreField: r1->field_1b = r4
    //     0x147840c: stur            w4, [x1, #0x1b]
    // 0x1478410: r5 = Instance_VerticalDirection
    //     0x1478410: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1478414: ldr             x5, [x5, #0xa20]
    // 0x1478418: StoreField: r1->field_23 = r5
    //     0x1478418: stur            w5, [x1, #0x23]
    // 0x147841c: r6 = Instance_Clip
    //     0x147841c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1478420: ldr             x6, [x6, #0x38]
    // 0x1478424: StoreField: r1->field_2b = r6
    //     0x1478424: stur            w6, [x1, #0x2b]
    // 0x1478428: StoreField: r1->field_2f = rZR
    //     0x1478428: stur            xzr, [x1, #0x2f]
    // 0x147842c: ldur            x7, [fp, #-0x20]
    // 0x1478430: StoreField: r1->field_b = r7
    //     0x1478430: stur            w7, [x1, #0xb]
    // 0x1478434: r0 = InkWell()
    //     0x1478434: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1478438: mov             x3, x0
    // 0x147843c: ldur            x0, [fp, #-0x18]
    // 0x1478440: stur            x3, [fp, #-0x20]
    // 0x1478444: StoreField: r3->field_b = r0
    //     0x1478444: stur            w0, [x3, #0xb]
    // 0x1478448: ldur            x2, [fp, #-8]
    // 0x147844c: r1 = Function '<anonymous closure>':.
    //     0x147844c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40120] AnonymousClosure: (0x14787a8), in [package:customer_app/app/presentation/views/glass/profile/profile_view.dart] ProfileView::body (0x14fc37c)
    //     0x1478450: ldr             x1, [x1, #0x120]
    // 0x1478454: r0 = AllocateClosure()
    //     0x1478454: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1478458: mov             x1, x0
    // 0x147845c: ldur            x0, [fp, #-0x20]
    // 0x1478460: StoreField: r0->field_f = r1
    //     0x1478460: stur            w1, [x0, #0xf]
    // 0x1478464: r1 = true
    //     0x1478464: add             x1, NULL, #0x20  ; true
    // 0x1478468: StoreField: r0->field_43 = r1
    //     0x1478468: stur            w1, [x0, #0x43]
    // 0x147846c: r2 = Instance_BoxShape
    //     0x147846c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1478470: ldr             x2, [x2, #0x80]
    // 0x1478474: StoreField: r0->field_47 = r2
    //     0x1478474: stur            w2, [x0, #0x47]
    // 0x1478478: r3 = Instance_Color
    //     0x1478478: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x147847c: ldr             x3, [x3, #0xf88]
    // 0x1478480: StoreField: r0->field_5f = r3
    //     0x1478480: stur            w3, [x0, #0x5f]
    // 0x1478484: r4 = Instance__NoSplashFactory
    //     0x1478484: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x1478488: ldr             x4, [x4, #0xc48]
    // 0x147848c: StoreField: r0->field_6b = r4
    //     0x147848c: stur            w4, [x0, #0x6b]
    // 0x1478490: StoreField: r0->field_6f = r1
    //     0x1478490: stur            w1, [x0, #0x6f]
    // 0x1478494: r5 = false
    //     0x1478494: add             x5, NULL, #0x30  ; false
    // 0x1478498: StoreField: r0->field_73 = r5
    //     0x1478498: stur            w5, [x0, #0x73]
    // 0x147849c: StoreField: r0->field_83 = r1
    //     0x147849c: stur            w1, [x0, #0x83]
    // 0x14784a0: StoreField: r0->field_7b = r5
    //     0x14784a0: stur            w5, [x0, #0x7b]
    // 0x14784a4: r0 = Align()
    //     0x14784a4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14784a8: mov             x1, x0
    // 0x14784ac: r0 = Instance_Alignment
    //     0x14784ac: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x14784b0: ldr             x0, [x0, #0xfa0]
    // 0x14784b4: StoreField: r1->field_f = r0
    //     0x14784b4: stur            w0, [x1, #0xf]
    // 0x14784b8: ldur            x0, [fp, #-0x20]
    // 0x14784bc: StoreField: r1->field_b = r0
    //     0x14784bc: stur            w0, [x1, #0xb]
    // 0x14784c0: mov             x0, x1
    // 0x14784c4: ldur            x1, [fp, #-0x10]
    // 0x14784c8: ArrayStore: r1[10] = r0  ; List_4
    //     0x14784c8: add             x25, x1, #0x37
    //     0x14784cc: str             w0, [x25]
    //     0x14784d0: tbz             w0, #0, #0x14784ec
    //     0x14784d4: ldurb           w16, [x1, #-1]
    //     0x14784d8: ldurb           w17, [x0, #-1]
    //     0x14784dc: and             x16, x17, x16, lsr #2
    //     0x14784e0: tst             x16, HEAP, lsr #32
    //     0x14784e4: b.eq            #0x14784ec
    //     0x14784e8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14784ec: ldur            x0, [fp, #-0x10]
    // 0x14784f0: r16 = Instance_SizedBox
    //     0x14784f0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x14784f4: ldr             x16, [x16, #0xc50]
    // 0x14784f8: StoreField: r0->field_3b = r16
    //     0x14784f8: stur            w16, [x0, #0x3b]
    // 0x14784fc: ldur            x2, [fp, #-8]
    // 0x1478500: LoadField: r1 = r2->field_f
    //     0x1478500: ldur            w1, [x2, #0xf]
    // 0x1478504: DecompressPointer r1
    //     0x1478504: add             x1, x1, HEAP, lsl #32
    // 0x1478508: r0 = controller()
    //     0x1478508: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147850c: LoadField: r1 = r0->field_5b
    //     0x147850c: ldur            w1, [x0, #0x5b]
    // 0x1478510: DecompressPointer r1
    //     0x1478510: add             x1, x1, HEAP, lsl #32
    // 0x1478514: r0 = value()
    //     0x1478514: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1478518: ldur            x2, [fp, #-8]
    // 0x147851c: stur            x0, [fp, #-0x18]
    // 0x1478520: LoadField: r1 = r2->field_13
    //     0x1478520: ldur            w1, [x2, #0x13]
    // 0x1478524: DecompressPointer r1
    //     0x1478524: add             x1, x1, HEAP, lsl #32
    // 0x1478528: r0 = of()
    //     0x1478528: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x147852c: LoadField: r1 = r0->field_87
    //     0x147852c: ldur            w1, [x0, #0x87]
    // 0x1478530: DecompressPointer r1
    //     0x1478530: add             x1, x1, HEAP, lsl #32
    // 0x1478534: LoadField: r0 = r1->field_7
    //     0x1478534: ldur            w0, [x1, #7]
    // 0x1478538: DecompressPointer r0
    //     0x1478538: add             x0, x0, HEAP, lsl #32
    // 0x147853c: r16 = 14.000000
    //     0x147853c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1478540: ldr             x16, [x16, #0x1d8]
    // 0x1478544: str             x16, [SP]
    // 0x1478548: mov             x1, x0
    // 0x147854c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x147854c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x1478550: ldr             x4, [x4, #0x798]
    // 0x1478554: r0 = copyWith()
    //     0x1478554: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1478558: stur            x0, [fp, #-0x20]
    // 0x147855c: r0 = Text()
    //     0x147855c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1478560: mov             x3, x0
    // 0x1478564: r0 = "Logout"
    //     0x1478564: add             x0, PP, #0x36, lsl #12  ; [pp+0x36cb8] "Logout"
    //     0x1478568: ldr             x0, [x0, #0xcb8]
    // 0x147856c: stur            x3, [fp, #-0x28]
    // 0x1478570: StoreField: r3->field_b = r0
    //     0x1478570: stur            w0, [x3, #0xb]
    // 0x1478574: ldur            x0, [fp, #-0x20]
    // 0x1478578: StoreField: r3->field_13 = r0
    //     0x1478578: stur            w0, [x3, #0x13]
    // 0x147857c: r1 = Null
    //     0x147857c: mov             x1, NULL
    // 0x1478580: r2 = 6
    //     0x1478580: movz            x2, #0x6
    // 0x1478584: r0 = AllocateArray()
    //     0x1478584: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1478588: stur            x0, [fp, #-0x20]
    // 0x147858c: r16 = Instance_Icon
    //     0x147858c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36cc0] Obj!Icon@d66a31
    //     0x1478590: ldr             x16, [x16, #0xcc0]
    // 0x1478594: StoreField: r0->field_f = r16
    //     0x1478594: stur            w16, [x0, #0xf]
    // 0x1478598: r16 = Instance_SizedBox
    //     0x1478598: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x147859c: ldr             x16, [x16, #0xaa8]
    // 0x14785a0: StoreField: r0->field_13 = r16
    //     0x14785a0: stur            w16, [x0, #0x13]
    // 0x14785a4: ldur            x1, [fp, #-0x28]
    // 0x14785a8: ArrayStore: r0[0] = r1  ; List_4
    //     0x14785a8: stur            w1, [x0, #0x17]
    // 0x14785ac: r1 = <Widget>
    //     0x14785ac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14785b0: r0 = AllocateGrowableArray()
    //     0x14785b0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14785b4: mov             x1, x0
    // 0x14785b8: ldur            x0, [fp, #-0x20]
    // 0x14785bc: stur            x1, [fp, #-0x28]
    // 0x14785c0: StoreField: r1->field_f = r0
    //     0x14785c0: stur            w0, [x1, #0xf]
    // 0x14785c4: r0 = 6
    //     0x14785c4: movz            x0, #0x6
    // 0x14785c8: StoreField: r1->field_b = r0
    //     0x14785c8: stur            w0, [x1, #0xb]
    // 0x14785cc: r0 = Row()
    //     0x14785cc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14785d0: mov             x1, x0
    // 0x14785d4: r0 = Instance_Axis
    //     0x14785d4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14785d8: stur            x1, [fp, #-0x20]
    // 0x14785dc: StoreField: r1->field_f = r0
    //     0x14785dc: stur            w0, [x1, #0xf]
    // 0x14785e0: r0 = Instance_MainAxisAlignment
    //     0x14785e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14785e4: ldr             x0, [x0, #0xa08]
    // 0x14785e8: StoreField: r1->field_13 = r0
    //     0x14785e8: stur            w0, [x1, #0x13]
    // 0x14785ec: r2 = Instance_MainAxisSize
    //     0x14785ec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14785f0: ldr             x2, [x2, #0xa10]
    // 0x14785f4: ArrayStore: r1[0] = r2  ; List_4
    //     0x14785f4: stur            w2, [x1, #0x17]
    // 0x14785f8: r3 = Instance_CrossAxisAlignment
    //     0x14785f8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14785fc: ldr             x3, [x3, #0xa18]
    // 0x1478600: StoreField: r1->field_1b = r3
    //     0x1478600: stur            w3, [x1, #0x1b]
    // 0x1478604: r4 = Instance_VerticalDirection
    //     0x1478604: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1478608: ldr             x4, [x4, #0xa20]
    // 0x147860c: StoreField: r1->field_23 = r4
    //     0x147860c: stur            w4, [x1, #0x23]
    // 0x1478610: r5 = Instance_Clip
    //     0x1478610: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1478614: ldr             x5, [x5, #0x38]
    // 0x1478618: StoreField: r1->field_2b = r5
    //     0x1478618: stur            w5, [x1, #0x2b]
    // 0x147861c: StoreField: r1->field_2f = rZR
    //     0x147861c: stur            xzr, [x1, #0x2f]
    // 0x1478620: ldur            x6, [fp, #-0x28]
    // 0x1478624: StoreField: r1->field_b = r6
    //     0x1478624: stur            w6, [x1, #0xb]
    // 0x1478628: r0 = InkWell()
    //     0x1478628: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x147862c: mov             x3, x0
    // 0x1478630: ldur            x0, [fp, #-0x20]
    // 0x1478634: stur            x3, [fp, #-0x28]
    // 0x1478638: StoreField: r3->field_b = r0
    //     0x1478638: stur            w0, [x3, #0xb]
    // 0x147863c: ldur            x2, [fp, #-8]
    // 0x1478640: r1 = Function '<anonymous closure>':.
    //     0x1478640: add             x1, PP, #0x40, lsl #12  ; [pp+0x40128] AnonymousClosure: (0x14762a4), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::body (0x1509484)
    //     0x1478644: ldr             x1, [x1, #0x128]
    // 0x1478648: r0 = AllocateClosure()
    //     0x1478648: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147864c: mov             x1, x0
    // 0x1478650: ldur            x0, [fp, #-0x28]
    // 0x1478654: StoreField: r0->field_f = r1
    //     0x1478654: stur            w1, [x0, #0xf]
    // 0x1478658: r1 = true
    //     0x1478658: add             x1, NULL, #0x20  ; true
    // 0x147865c: StoreField: r0->field_43 = r1
    //     0x147865c: stur            w1, [x0, #0x43]
    // 0x1478660: r2 = Instance_BoxShape
    //     0x1478660: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1478664: ldr             x2, [x2, #0x80]
    // 0x1478668: StoreField: r0->field_47 = r2
    //     0x1478668: stur            w2, [x0, #0x47]
    // 0x147866c: r2 = Instance_Color
    //     0x147866c: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x1478670: ldr             x2, [x2, #0xf88]
    // 0x1478674: StoreField: r0->field_5f = r2
    //     0x1478674: stur            w2, [x0, #0x5f]
    // 0x1478678: r2 = Instance__NoSplashFactory
    //     0x1478678: add             x2, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x147867c: ldr             x2, [x2, #0xc48]
    // 0x1478680: StoreField: r0->field_6b = r2
    //     0x1478680: stur            w2, [x0, #0x6b]
    // 0x1478684: StoreField: r0->field_6f = r1
    //     0x1478684: stur            w1, [x0, #0x6f]
    // 0x1478688: r2 = false
    //     0x1478688: add             x2, NULL, #0x30  ; false
    // 0x147868c: StoreField: r0->field_73 = r2
    //     0x147868c: stur            w2, [x0, #0x73]
    // 0x1478690: StoreField: r0->field_83 = r1
    //     0x1478690: stur            w1, [x0, #0x83]
    // 0x1478694: StoreField: r0->field_7b = r2
    //     0x1478694: stur            w2, [x0, #0x7b]
    // 0x1478698: r0 = Visibility()
    //     0x1478698: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x147869c: mov             x1, x0
    // 0x14786a0: ldur            x0, [fp, #-0x28]
    // 0x14786a4: StoreField: r1->field_b = r0
    //     0x14786a4: stur            w0, [x1, #0xb]
    // 0x14786a8: r0 = Instance_SizedBox
    //     0x14786a8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14786ac: StoreField: r1->field_f = r0
    //     0x14786ac: stur            w0, [x1, #0xf]
    // 0x14786b0: ldur            x0, [fp, #-0x18]
    // 0x14786b4: StoreField: r1->field_13 = r0
    //     0x14786b4: stur            w0, [x1, #0x13]
    // 0x14786b8: r0 = false
    //     0x14786b8: add             x0, NULL, #0x30  ; false
    // 0x14786bc: ArrayStore: r1[0] = r0  ; List_4
    //     0x14786bc: stur            w0, [x1, #0x17]
    // 0x14786c0: StoreField: r1->field_1b = r0
    //     0x14786c0: stur            w0, [x1, #0x1b]
    // 0x14786c4: StoreField: r1->field_1f = r0
    //     0x14786c4: stur            w0, [x1, #0x1f]
    // 0x14786c8: StoreField: r1->field_23 = r0
    //     0x14786c8: stur            w0, [x1, #0x23]
    // 0x14786cc: StoreField: r1->field_27 = r0
    //     0x14786cc: stur            w0, [x1, #0x27]
    // 0x14786d0: StoreField: r1->field_2b = r0
    //     0x14786d0: stur            w0, [x1, #0x2b]
    // 0x14786d4: mov             x0, x1
    // 0x14786d8: ldur            x1, [fp, #-0x10]
    // 0x14786dc: ArrayStore: r1[12] = r0  ; List_4
    //     0x14786dc: add             x25, x1, #0x3f
    //     0x14786e0: str             w0, [x25]
    //     0x14786e4: tbz             w0, #0, #0x1478700
    //     0x14786e8: ldurb           w16, [x1, #-1]
    //     0x14786ec: ldurb           w17, [x0, #-1]
    //     0x14786f0: and             x16, x17, x16, lsr #2
    //     0x14786f4: tst             x16, HEAP, lsr #32
    //     0x14786f8: b.eq            #0x1478700
    //     0x14786fc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1478700: r1 = <Widget>
    //     0x1478700: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1478704: r0 = AllocateGrowableArray()
    //     0x1478704: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1478708: mov             x1, x0
    // 0x147870c: ldur            x0, [fp, #-0x10]
    // 0x1478710: stur            x1, [fp, #-8]
    // 0x1478714: StoreField: r1->field_f = r0
    //     0x1478714: stur            w0, [x1, #0xf]
    // 0x1478718: r0 = 26
    //     0x1478718: movz            x0, #0x1a
    // 0x147871c: StoreField: r1->field_b = r0
    //     0x147871c: stur            w0, [x1, #0xb]
    // 0x1478720: r0 = Column()
    //     0x1478720: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1478724: mov             x1, x0
    // 0x1478728: r0 = Instance_Axis
    //     0x1478728: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x147872c: stur            x1, [fp, #-0x10]
    // 0x1478730: StoreField: r1->field_f = r0
    //     0x1478730: stur            w0, [x1, #0xf]
    // 0x1478734: r0 = Instance_MainAxisAlignment
    //     0x1478734: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1478738: ldr             x0, [x0, #0xa08]
    // 0x147873c: StoreField: r1->field_13 = r0
    //     0x147873c: stur            w0, [x1, #0x13]
    // 0x1478740: r0 = Instance_MainAxisSize
    //     0x1478740: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1478744: ldr             x0, [x0, #0xa10]
    // 0x1478748: ArrayStore: r1[0] = r0  ; List_4
    //     0x1478748: stur            w0, [x1, #0x17]
    // 0x147874c: r0 = Instance_CrossAxisAlignment
    //     0x147874c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1478750: ldr             x0, [x0, #0xa18]
    // 0x1478754: StoreField: r1->field_1b = r0
    //     0x1478754: stur            w0, [x1, #0x1b]
    // 0x1478758: r0 = Instance_VerticalDirection
    //     0x1478758: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x147875c: ldr             x0, [x0, #0xa20]
    // 0x1478760: StoreField: r1->field_23 = r0
    //     0x1478760: stur            w0, [x1, #0x23]
    // 0x1478764: r0 = Instance_Clip
    //     0x1478764: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1478768: ldr             x0, [x0, #0x38]
    // 0x147876c: StoreField: r1->field_2b = r0
    //     0x147876c: stur            w0, [x1, #0x2b]
    // 0x1478770: StoreField: r1->field_2f = rZR
    //     0x1478770: stur            xzr, [x1, #0x2f]
    // 0x1478774: ldur            x0, [fp, #-8]
    // 0x1478778: StoreField: r1->field_b = r0
    //     0x1478778: stur            w0, [x1, #0xb]
    // 0x147877c: r0 = Padding()
    //     0x147877c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1478780: r1 = Instance_EdgeInsets
    //     0x1478780: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1478784: ldr             x1, [x1, #0x1f0]
    // 0x1478788: StoreField: r0->field_f = r1
    //     0x1478788: stur            w1, [x0, #0xf]
    // 0x147878c: ldur            x1, [fp, #-0x10]
    // 0x1478790: StoreField: r0->field_b = r1
    //     0x1478790: stur            w1, [x0, #0xb]
    // 0x1478794: LeaveFrame
    //     0x1478794: mov             SP, fp
    //     0x1478798: ldp             fp, lr, [SP], #0x10
    // 0x147879c: ret
    //     0x147879c: ret             
    // 0x14787a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14787a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14787a4: b               #0x14779d4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14787a8, size: 0x50
    // 0x14787a8: EnterFrame
    //     0x14787a8: stp             fp, lr, [SP, #-0x10]!
    //     0x14787ac: mov             fp, SP
    // 0x14787b0: ldr             x0, [fp, #0x10]
    // 0x14787b4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14787b4: ldur            w1, [x0, #0x17]
    // 0x14787b8: DecompressPointer r1
    //     0x14787b8: add             x1, x1, HEAP, lsl #32
    // 0x14787bc: CheckStackOverflow
    //     0x14787bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14787c0: cmp             SP, x16
    //     0x14787c4: b.ls            #0x14787f0
    // 0x14787c8: LoadField: r0 = r1->field_f
    //     0x14787c8: ldur            w0, [x1, #0xf]
    // 0x14787cc: DecompressPointer r0
    //     0x14787cc: add             x0, x0, HEAP, lsl #32
    // 0x14787d0: mov             x1, x0
    // 0x14787d4: r2 = "terms_and_conditions"
    //     0x14787d4: add             x2, PP, #0x36, lsl #12  ; [pp+0x36cd0] "terms_and_conditions"
    //     0x14787d8: ldr             x2, [x2, #0xcd0]
    // 0x14787dc: r0 = openPolicyPage()
    //     0x14787dc: bl              #0x1477424  ; [package:customer_app/app/presentation/views/basic/profile/profile_view.dart] ProfileView::openPolicyPage
    // 0x14787e0: r0 = Null
    //     0x14787e0: mov             x0, NULL
    // 0x14787e4: LeaveFrame
    //     0x14787e4: mov             SP, fp
    //     0x14787e8: ldp             fp, lr, [SP], #0x10
    // 0x14787ec: ret
    //     0x14787ec: ret             
    // 0x14787f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14787f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14787f4: b               #0x14787c8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14787f8, size: 0x50
    // 0x14787f8: EnterFrame
    //     0x14787f8: stp             fp, lr, [SP, #-0x10]!
    //     0x14787fc: mov             fp, SP
    // 0x1478800: ldr             x0, [fp, #0x10]
    // 0x1478804: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1478804: ldur            w1, [x0, #0x17]
    // 0x1478808: DecompressPointer r1
    //     0x1478808: add             x1, x1, HEAP, lsl #32
    // 0x147880c: CheckStackOverflow
    //     0x147880c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1478810: cmp             SP, x16
    //     0x1478814: b.ls            #0x1478840
    // 0x1478818: LoadField: r0 = r1->field_f
    //     0x1478818: ldur            w0, [x1, #0xf]
    // 0x147881c: DecompressPointer r0
    //     0x147881c: add             x0, x0, HEAP, lsl #32
    // 0x1478820: mov             x1, x0
    // 0x1478824: r2 = "shipping_policy"
    //     0x1478824: add             x2, PP, #0x36, lsl #12  ; [pp+0x36cd8] "shipping_policy"
    //     0x1478828: ldr             x2, [x2, #0xcd8]
    // 0x147882c: r0 = openPolicyPage()
    //     0x147882c: bl              #0x1477424  ; [package:customer_app/app/presentation/views/basic/profile/profile_view.dart] ProfileView::openPolicyPage
    // 0x1478830: r0 = Null
    //     0x1478830: mov             x0, NULL
    // 0x1478834: LeaveFrame
    //     0x1478834: mov             SP, fp
    //     0x1478838: ldp             fp, lr, [SP], #0x10
    // 0x147883c: ret
    //     0x147883c: ret             
    // 0x1478840: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1478840: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1478844: b               #0x1478818
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1478848, size: 0x50
    // 0x1478848: EnterFrame
    //     0x1478848: stp             fp, lr, [SP, #-0x10]!
    //     0x147884c: mov             fp, SP
    // 0x1478850: ldr             x0, [fp, #0x10]
    // 0x1478854: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1478854: ldur            w1, [x0, #0x17]
    // 0x1478858: DecompressPointer r1
    //     0x1478858: add             x1, x1, HEAP, lsl #32
    // 0x147885c: CheckStackOverflow
    //     0x147885c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1478860: cmp             SP, x16
    //     0x1478864: b.ls            #0x1478890
    // 0x1478868: LoadField: r0 = r1->field_f
    //     0x1478868: ldur            w0, [x1, #0xf]
    // 0x147886c: DecompressPointer r0
    //     0x147886c: add             x0, x0, HEAP, lsl #32
    // 0x1478870: mov             x1, x0
    // 0x1478874: r2 = "return_policy"
    //     0x1478874: add             x2, PP, #0x36, lsl #12  ; [pp+0x36ce0] "return_policy"
    //     0x1478878: ldr             x2, [x2, #0xce0]
    // 0x147887c: r0 = openPolicyPage()
    //     0x147887c: bl              #0x1477424  ; [package:customer_app/app/presentation/views/basic/profile/profile_view.dart] ProfileView::openPolicyPage
    // 0x1478880: r0 = Null
    //     0x1478880: mov             x0, NULL
    // 0x1478884: LeaveFrame
    //     0x1478884: mov             SP, fp
    //     0x1478888: ldp             fp, lr, [SP], #0x10
    // 0x147888c: ret
    //     0x147888c: ret             
    // 0x1478890: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1478890: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1478894: b               #0x1478868
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1478898, size: 0x50
    // 0x1478898: EnterFrame
    //     0x1478898: stp             fp, lr, [SP, #-0x10]!
    //     0x147889c: mov             fp, SP
    // 0x14788a0: ldr             x0, [fp, #0x10]
    // 0x14788a4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14788a4: ldur            w1, [x0, #0x17]
    // 0x14788a8: DecompressPointer r1
    //     0x14788a8: add             x1, x1, HEAP, lsl #32
    // 0x14788ac: CheckStackOverflow
    //     0x14788ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14788b0: cmp             SP, x16
    //     0x14788b4: b.ls            #0x14788e0
    // 0x14788b8: LoadField: r0 = r1->field_f
    //     0x14788b8: ldur            w0, [x1, #0xf]
    // 0x14788bc: DecompressPointer r0
    //     0x14788bc: add             x0, x0, HEAP, lsl #32
    // 0x14788c0: mov             x1, x0
    // 0x14788c4: r2 = "privacy_policy"
    //     0x14788c4: add             x2, PP, #0x36, lsl #12  ; [pp+0x36ce8] "privacy_policy"
    //     0x14788c8: ldr             x2, [x2, #0xce8]
    // 0x14788cc: r0 = openPolicyPage()
    //     0x14788cc: bl              #0x1477424  ; [package:customer_app/app/presentation/views/basic/profile/profile_view.dart] ProfileView::openPolicyPage
    // 0x14788d0: r0 = Null
    //     0x14788d0: mov             x0, NULL
    // 0x14788d4: LeaveFrame
    //     0x14788d4: mov             SP, fp
    //     0x14788d8: ldp             fp, lr, [SP], #0x10
    // 0x14788dc: ret
    //     0x14788dc: ret             
    // 0x14788e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14788e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14788e4: b               #0x14788b8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14788e8, size: 0x50
    // 0x14788e8: EnterFrame
    //     0x14788e8: stp             fp, lr, [SP, #-0x10]!
    //     0x14788ec: mov             fp, SP
    // 0x14788f0: ldr             x0, [fp, #0x10]
    // 0x14788f4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14788f4: ldur            w1, [x0, #0x17]
    // 0x14788f8: DecompressPointer r1
    //     0x14788f8: add             x1, x1, HEAP, lsl #32
    // 0x14788fc: CheckStackOverflow
    //     0x14788fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1478900: cmp             SP, x16
    //     0x1478904: b.ls            #0x1478930
    // 0x1478908: LoadField: r0 = r1->field_f
    //     0x1478908: ldur            w0, [x1, #0xf]
    // 0x147890c: DecompressPointer r0
    //     0x147890c: add             x0, x0, HEAP, lsl #32
    // 0x1478910: mov             x1, x0
    // 0x1478914: r2 = "about_us"
    //     0x1478914: add             x2, PP, #0x36, lsl #12  ; [pp+0x36cf0] "about_us"
    //     0x1478918: ldr             x2, [x2, #0xcf0]
    // 0x147891c: r0 = openPolicyPage()
    //     0x147891c: bl              #0x1477424  ; [package:customer_app/app/presentation/views/basic/profile/profile_view.dart] ProfileView::openPolicyPage
    // 0x1478920: r0 = Null
    //     0x1478920: mov             x0, NULL
    // 0x1478924: LeaveFrame
    //     0x1478924: mov             SP, fp
    //     0x1478928: ldp             fp, lr, [SP], #0x10
    // 0x147892c: ret
    //     0x147892c: ret             
    // 0x1478930: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1478930: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1478934: b               #0x1478908
  }
  _ body(/* No info */) {
    // ** addr: 0x14fc37c, size: 0x64
    // 0x14fc37c: EnterFrame
    //     0x14fc37c: stp             fp, lr, [SP, #-0x10]!
    //     0x14fc380: mov             fp, SP
    // 0x14fc384: AllocStack(0x18)
    //     0x14fc384: sub             SP, SP, #0x18
    // 0x14fc388: SetupParameters(ProfileView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14fc388: stur            x1, [fp, #-8]
    //     0x14fc38c: stur            x2, [fp, #-0x10]
    // 0x14fc390: r1 = 2
    //     0x14fc390: movz            x1, #0x2
    // 0x14fc394: r0 = AllocateContext()
    //     0x14fc394: bl              #0x16f6108  ; AllocateContextStub
    // 0x14fc398: mov             x1, x0
    // 0x14fc39c: ldur            x0, [fp, #-8]
    // 0x14fc3a0: stur            x1, [fp, #-0x18]
    // 0x14fc3a4: StoreField: r1->field_f = r0
    //     0x14fc3a4: stur            w0, [x1, #0xf]
    // 0x14fc3a8: ldur            x0, [fp, #-0x10]
    // 0x14fc3ac: StoreField: r1->field_13 = r0
    //     0x14fc3ac: stur            w0, [x1, #0x13]
    // 0x14fc3b0: r0 = Obx()
    //     0x14fc3b0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14fc3b4: ldur            x2, [fp, #-0x18]
    // 0x14fc3b8: r1 = Function '<anonymous closure>':.
    //     0x14fc3b8: add             x1, PP, #0x40, lsl #12  ; [pp+0x400f0] AnonymousClosure: (0x14779ac), in [package:customer_app/app/presentation/views/glass/profile/profile_view.dart] ProfileView::body (0x14fc37c)
    //     0x14fc3bc: ldr             x1, [x1, #0xf0]
    // 0x14fc3c0: stur            x0, [fp, #-8]
    // 0x14fc3c4: r0 = AllocateClosure()
    //     0x14fc3c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fc3c8: mov             x1, x0
    // 0x14fc3cc: ldur            x0, [fp, #-8]
    // 0x14fc3d0: StoreField: r0->field_b = r1
    //     0x14fc3d0: stur            w1, [x0, #0xb]
    // 0x14fc3d4: LeaveFrame
    //     0x14fc3d4: mov             SP, fp
    //     0x14fc3d8: ldp             fp, lr, [SP], #0x10
    // 0x14fc3dc: ret
    //     0x14fc3dc: ret             
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0x15d3254, size: 0x420
    // 0x15d3254: EnterFrame
    //     0x15d3254: stp             fp, lr, [SP, #-0x10]!
    //     0x15d3258: mov             fp, SP
    // 0x15d325c: AllocStack(0x40)
    //     0x15d325c: sub             SP, SP, #0x40
    // 0x15d3260: SetupParameters()
    //     0x15d3260: ldr             x0, [fp, #0x10]
    //     0x15d3264: ldur            w2, [x0, #0x17]
    //     0x15d3268: add             x2, x2, HEAP, lsl #32
    //     0x15d326c: stur            x2, [fp, #-8]
    // 0x15d3270: CheckStackOverflow
    //     0x15d3270: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d3274: cmp             SP, x16
    //     0x15d3278: b.ls            #0x15d366c
    // 0x15d327c: LoadField: r1 = r2->field_f
    //     0x15d327c: ldur            w1, [x2, #0xf]
    // 0x15d3280: DecompressPointer r1
    //     0x15d3280: add             x1, x1, HEAP, lsl #32
    // 0x15d3284: r0 = controller()
    //     0x15d3284: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d3288: LoadField: r1 = r0->field_6b
    //     0x15d3288: ldur            w1, [x0, #0x6b]
    // 0x15d328c: DecompressPointer r1
    //     0x15d328c: add             x1, x1, HEAP, lsl #32
    // 0x15d3290: r0 = value()
    //     0x15d3290: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d3294: LoadField: r1 = r0->field_3f
    //     0x15d3294: ldur            w1, [x0, #0x3f]
    // 0x15d3298: DecompressPointer r1
    //     0x15d3298: add             x1, x1, HEAP, lsl #32
    // 0x15d329c: cmp             w1, NULL
    // 0x15d32a0: b.ne            #0x15d32ac
    // 0x15d32a4: r0 = Null
    //     0x15d32a4: mov             x0, NULL
    // 0x15d32a8: b               #0x15d32b4
    // 0x15d32ac: LoadField: r0 = r1->field_f
    //     0x15d32ac: ldur            w0, [x1, #0xf]
    // 0x15d32b0: DecompressPointer r0
    //     0x15d32b0: add             x0, x0, HEAP, lsl #32
    // 0x15d32b4: r1 = LoadClassIdInstr(r0)
    //     0x15d32b4: ldur            x1, [x0, #-1]
    //     0x15d32b8: ubfx            x1, x1, #0xc, #0x14
    // 0x15d32bc: r16 = "image_text"
    //     0x15d32bc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15d32c0: ldr             x16, [x16, #0xa88]
    // 0x15d32c4: stp             x16, x0, [SP]
    // 0x15d32c8: mov             x0, x1
    // 0x15d32cc: mov             lr, x0
    // 0x15d32d0: ldr             lr, [x21, lr, lsl #3]
    // 0x15d32d4: blr             lr
    // 0x15d32d8: tbnz            w0, #4, #0x15d32e4
    // 0x15d32dc: r2 = true
    //     0x15d32dc: add             x2, NULL, #0x20  ; true
    // 0x15d32e0: b               #0x15d3344
    // 0x15d32e4: ldur            x0, [fp, #-8]
    // 0x15d32e8: LoadField: r1 = r0->field_f
    //     0x15d32e8: ldur            w1, [x0, #0xf]
    // 0x15d32ec: DecompressPointer r1
    //     0x15d32ec: add             x1, x1, HEAP, lsl #32
    // 0x15d32f0: r0 = controller()
    //     0x15d32f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d32f4: LoadField: r1 = r0->field_6b
    //     0x15d32f4: ldur            w1, [x0, #0x6b]
    // 0x15d32f8: DecompressPointer r1
    //     0x15d32f8: add             x1, x1, HEAP, lsl #32
    // 0x15d32fc: r0 = value()
    //     0x15d32fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d3300: LoadField: r1 = r0->field_3f
    //     0x15d3300: ldur            w1, [x0, #0x3f]
    // 0x15d3304: DecompressPointer r1
    //     0x15d3304: add             x1, x1, HEAP, lsl #32
    // 0x15d3308: cmp             w1, NULL
    // 0x15d330c: b.ne            #0x15d3318
    // 0x15d3310: r0 = Null
    //     0x15d3310: mov             x0, NULL
    // 0x15d3314: b               #0x15d3320
    // 0x15d3318: LoadField: r0 = r1->field_f
    //     0x15d3318: ldur            w0, [x1, #0xf]
    // 0x15d331c: DecompressPointer r0
    //     0x15d331c: add             x0, x0, HEAP, lsl #32
    // 0x15d3320: r1 = LoadClassIdInstr(r0)
    //     0x15d3320: ldur            x1, [x0, #-1]
    //     0x15d3324: ubfx            x1, x1, #0xc, #0x14
    // 0x15d3328: r16 = "image"
    //     0x15d3328: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x15d332c: stp             x16, x0, [SP]
    // 0x15d3330: mov             x0, x1
    // 0x15d3334: mov             lr, x0
    // 0x15d3338: ldr             lr, [x21, lr, lsl #3]
    // 0x15d333c: blr             lr
    // 0x15d3340: mov             x2, x0
    // 0x15d3344: ldur            x0, [fp, #-8]
    // 0x15d3348: stur            x2, [fp, #-0x10]
    // 0x15d334c: LoadField: r1 = r0->field_f
    //     0x15d334c: ldur            w1, [x0, #0xf]
    // 0x15d3350: DecompressPointer r1
    //     0x15d3350: add             x1, x1, HEAP, lsl #32
    // 0x15d3354: r0 = controller()
    //     0x15d3354: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d3358: LoadField: r1 = r0->field_6b
    //     0x15d3358: ldur            w1, [x0, #0x6b]
    // 0x15d335c: DecompressPointer r1
    //     0x15d335c: add             x1, x1, HEAP, lsl #32
    // 0x15d3360: r0 = value()
    //     0x15d3360: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d3364: LoadField: r1 = r0->field_27
    //     0x15d3364: ldur            w1, [x0, #0x27]
    // 0x15d3368: DecompressPointer r1
    //     0x15d3368: add             x1, x1, HEAP, lsl #32
    // 0x15d336c: cmp             w1, NULL
    // 0x15d3370: b.ne            #0x15d337c
    // 0x15d3374: r2 = ""
    //     0x15d3374: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15d3378: b               #0x15d3380
    // 0x15d337c: mov             x2, x1
    // 0x15d3380: ldur            x0, [fp, #-8]
    // 0x15d3384: ldur            x1, [fp, #-0x10]
    // 0x15d3388: stur            x2, [fp, #-0x18]
    // 0x15d338c: r0 = CachedNetworkImage()
    //     0x15d338c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15d3390: stur            x0, [fp, #-0x20]
    // 0x15d3394: r16 = Instance_BoxFit
    //     0x15d3394: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15d3398: ldr             x16, [x16, #0xb18]
    // 0x15d339c: r30 = 50.000000
    //     0x15d339c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15d33a0: ldr             lr, [lr, #0xa90]
    // 0x15d33a4: stp             lr, x16, [SP, #8]
    // 0x15d33a8: r16 = 50.000000
    //     0x15d33a8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15d33ac: ldr             x16, [x16, #0xa90]
    // 0x15d33b0: str             x16, [SP]
    // 0x15d33b4: mov             x1, x0
    // 0x15d33b8: ldur            x2, [fp, #-0x18]
    // 0x15d33bc: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15d33bc: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d8e0] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15d33c0: ldr             x4, [x4, #0x8e0]
    // 0x15d33c4: r0 = CachedNetworkImage()
    //     0x15d33c4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15d33c8: r0 = Visibility()
    //     0x15d33c8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15d33cc: mov             x2, x0
    // 0x15d33d0: ldur            x0, [fp, #-0x20]
    // 0x15d33d4: stur            x2, [fp, #-0x18]
    // 0x15d33d8: StoreField: r2->field_b = r0
    //     0x15d33d8: stur            w0, [x2, #0xb]
    // 0x15d33dc: r0 = Instance_SizedBox
    //     0x15d33dc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15d33e0: StoreField: r2->field_f = r0
    //     0x15d33e0: stur            w0, [x2, #0xf]
    // 0x15d33e4: ldur            x1, [fp, #-0x10]
    // 0x15d33e8: StoreField: r2->field_13 = r1
    //     0x15d33e8: stur            w1, [x2, #0x13]
    // 0x15d33ec: r3 = false
    //     0x15d33ec: add             x3, NULL, #0x30  ; false
    // 0x15d33f0: ArrayStore: r2[0] = r3  ; List_4
    //     0x15d33f0: stur            w3, [x2, #0x17]
    // 0x15d33f4: StoreField: r2->field_1b = r3
    //     0x15d33f4: stur            w3, [x2, #0x1b]
    // 0x15d33f8: StoreField: r2->field_1f = r3
    //     0x15d33f8: stur            w3, [x2, #0x1f]
    // 0x15d33fc: StoreField: r2->field_23 = r3
    //     0x15d33fc: stur            w3, [x2, #0x23]
    // 0x15d3400: StoreField: r2->field_27 = r3
    //     0x15d3400: stur            w3, [x2, #0x27]
    // 0x15d3404: StoreField: r2->field_2b = r3
    //     0x15d3404: stur            w3, [x2, #0x2b]
    // 0x15d3408: ldur            x4, [fp, #-8]
    // 0x15d340c: LoadField: r1 = r4->field_f
    //     0x15d340c: ldur            w1, [x4, #0xf]
    // 0x15d3410: DecompressPointer r1
    //     0x15d3410: add             x1, x1, HEAP, lsl #32
    // 0x15d3414: r0 = controller()
    //     0x15d3414: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d3418: LoadField: r1 = r0->field_6b
    //     0x15d3418: ldur            w1, [x0, #0x6b]
    // 0x15d341c: DecompressPointer r1
    //     0x15d341c: add             x1, x1, HEAP, lsl #32
    // 0x15d3420: r0 = value()
    //     0x15d3420: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d3424: LoadField: r1 = r0->field_3f
    //     0x15d3424: ldur            w1, [x0, #0x3f]
    // 0x15d3428: DecompressPointer r1
    //     0x15d3428: add             x1, x1, HEAP, lsl #32
    // 0x15d342c: cmp             w1, NULL
    // 0x15d3430: b.ne            #0x15d343c
    // 0x15d3434: r0 = Null
    //     0x15d3434: mov             x0, NULL
    // 0x15d3438: b               #0x15d3444
    // 0x15d343c: LoadField: r0 = r1->field_f
    //     0x15d343c: ldur            w0, [x1, #0xf]
    // 0x15d3440: DecompressPointer r0
    //     0x15d3440: add             x0, x0, HEAP, lsl #32
    // 0x15d3444: r1 = LoadClassIdInstr(r0)
    //     0x15d3444: ldur            x1, [x0, #-1]
    //     0x15d3448: ubfx            x1, x1, #0xc, #0x14
    // 0x15d344c: r16 = "image_text"
    //     0x15d344c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15d3450: ldr             x16, [x16, #0xa88]
    // 0x15d3454: stp             x16, x0, [SP]
    // 0x15d3458: mov             x0, x1
    // 0x15d345c: mov             lr, x0
    // 0x15d3460: ldr             lr, [x21, lr, lsl #3]
    // 0x15d3464: blr             lr
    // 0x15d3468: tbnz            w0, #4, #0x15d3474
    // 0x15d346c: r2 = true
    //     0x15d346c: add             x2, NULL, #0x20  ; true
    // 0x15d3470: b               #0x15d34d4
    // 0x15d3474: ldur            x0, [fp, #-8]
    // 0x15d3478: LoadField: r1 = r0->field_f
    //     0x15d3478: ldur            w1, [x0, #0xf]
    // 0x15d347c: DecompressPointer r1
    //     0x15d347c: add             x1, x1, HEAP, lsl #32
    // 0x15d3480: r0 = controller()
    //     0x15d3480: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d3484: LoadField: r1 = r0->field_6b
    //     0x15d3484: ldur            w1, [x0, #0x6b]
    // 0x15d3488: DecompressPointer r1
    //     0x15d3488: add             x1, x1, HEAP, lsl #32
    // 0x15d348c: r0 = value()
    //     0x15d348c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d3490: LoadField: r1 = r0->field_3f
    //     0x15d3490: ldur            w1, [x0, #0x3f]
    // 0x15d3494: DecompressPointer r1
    //     0x15d3494: add             x1, x1, HEAP, lsl #32
    // 0x15d3498: cmp             w1, NULL
    // 0x15d349c: b.ne            #0x15d34a8
    // 0x15d34a0: r0 = Null
    //     0x15d34a0: mov             x0, NULL
    // 0x15d34a4: b               #0x15d34b0
    // 0x15d34a8: LoadField: r0 = r1->field_f
    //     0x15d34a8: ldur            w0, [x1, #0xf]
    // 0x15d34ac: DecompressPointer r0
    //     0x15d34ac: add             x0, x0, HEAP, lsl #32
    // 0x15d34b0: r1 = LoadClassIdInstr(r0)
    //     0x15d34b0: ldur            x1, [x0, #-1]
    //     0x15d34b4: ubfx            x1, x1, #0xc, #0x14
    // 0x15d34b8: r16 = "text"
    //     0x15d34b8: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15d34bc: stp             x16, x0, [SP]
    // 0x15d34c0: mov             x0, x1
    // 0x15d34c4: mov             lr, x0
    // 0x15d34c8: ldr             lr, [x21, lr, lsl #3]
    // 0x15d34cc: blr             lr
    // 0x15d34d0: mov             x2, x0
    // 0x15d34d4: ldur            x0, [fp, #-8]
    // 0x15d34d8: stur            x2, [fp, #-0x10]
    // 0x15d34dc: LoadField: r1 = r0->field_f
    //     0x15d34dc: ldur            w1, [x0, #0xf]
    // 0x15d34e0: DecompressPointer r1
    //     0x15d34e0: add             x1, x1, HEAP, lsl #32
    // 0x15d34e4: r0 = controller()
    //     0x15d34e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d34e8: LoadField: r1 = r0->field_6b
    //     0x15d34e8: ldur            w1, [x0, #0x6b]
    // 0x15d34ec: DecompressPointer r1
    //     0x15d34ec: add             x1, x1, HEAP, lsl #32
    // 0x15d34f0: r0 = value()
    //     0x15d34f0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d34f4: LoadField: r1 = r0->field_2b
    //     0x15d34f4: ldur            w1, [x0, #0x2b]
    // 0x15d34f8: DecompressPointer r1
    //     0x15d34f8: add             x1, x1, HEAP, lsl #32
    // 0x15d34fc: cmp             w1, NULL
    // 0x15d3500: b.ne            #0x15d350c
    // 0x15d3504: r4 = ""
    //     0x15d3504: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15d3508: b               #0x15d3510
    // 0x15d350c: mov             x4, x1
    // 0x15d3510: ldur            x0, [fp, #-8]
    // 0x15d3514: ldur            x3, [fp, #-0x18]
    // 0x15d3518: ldur            x2, [fp, #-0x10]
    // 0x15d351c: stur            x4, [fp, #-0x20]
    // 0x15d3520: LoadField: r1 = r0->field_13
    //     0x15d3520: ldur            w1, [x0, #0x13]
    // 0x15d3524: DecompressPointer r1
    //     0x15d3524: add             x1, x1, HEAP, lsl #32
    // 0x15d3528: r0 = of()
    //     0x15d3528: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d352c: LoadField: r1 = r0->field_87
    //     0x15d352c: ldur            w1, [x0, #0x87]
    // 0x15d3530: DecompressPointer r1
    //     0x15d3530: add             x1, x1, HEAP, lsl #32
    // 0x15d3534: LoadField: r0 = r1->field_2b
    //     0x15d3534: ldur            w0, [x1, #0x2b]
    // 0x15d3538: DecompressPointer r0
    //     0x15d3538: add             x0, x0, HEAP, lsl #32
    // 0x15d353c: r16 = 16.000000
    //     0x15d353c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15d3540: ldr             x16, [x16, #0x188]
    // 0x15d3544: r30 = Instance_Color
    //     0x15d3544: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15d3548: stp             lr, x16, [SP]
    // 0x15d354c: mov             x1, x0
    // 0x15d3550: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15d3550: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15d3554: ldr             x4, [x4, #0xaa0]
    // 0x15d3558: r0 = copyWith()
    //     0x15d3558: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15d355c: stur            x0, [fp, #-8]
    // 0x15d3560: r0 = Text()
    //     0x15d3560: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15d3564: mov             x1, x0
    // 0x15d3568: ldur            x0, [fp, #-0x20]
    // 0x15d356c: stur            x1, [fp, #-0x28]
    // 0x15d3570: StoreField: r1->field_b = r0
    //     0x15d3570: stur            w0, [x1, #0xb]
    // 0x15d3574: ldur            x0, [fp, #-8]
    // 0x15d3578: StoreField: r1->field_13 = r0
    //     0x15d3578: stur            w0, [x1, #0x13]
    // 0x15d357c: r0 = Visibility()
    //     0x15d357c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15d3580: mov             x3, x0
    // 0x15d3584: ldur            x0, [fp, #-0x28]
    // 0x15d3588: stur            x3, [fp, #-8]
    // 0x15d358c: StoreField: r3->field_b = r0
    //     0x15d358c: stur            w0, [x3, #0xb]
    // 0x15d3590: r0 = Instance_SizedBox
    //     0x15d3590: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15d3594: StoreField: r3->field_f = r0
    //     0x15d3594: stur            w0, [x3, #0xf]
    // 0x15d3598: ldur            x0, [fp, #-0x10]
    // 0x15d359c: StoreField: r3->field_13 = r0
    //     0x15d359c: stur            w0, [x3, #0x13]
    // 0x15d35a0: r0 = false
    //     0x15d35a0: add             x0, NULL, #0x30  ; false
    // 0x15d35a4: ArrayStore: r3[0] = r0  ; List_4
    //     0x15d35a4: stur            w0, [x3, #0x17]
    // 0x15d35a8: StoreField: r3->field_1b = r0
    //     0x15d35a8: stur            w0, [x3, #0x1b]
    // 0x15d35ac: StoreField: r3->field_1f = r0
    //     0x15d35ac: stur            w0, [x3, #0x1f]
    // 0x15d35b0: StoreField: r3->field_23 = r0
    //     0x15d35b0: stur            w0, [x3, #0x23]
    // 0x15d35b4: StoreField: r3->field_27 = r0
    //     0x15d35b4: stur            w0, [x3, #0x27]
    // 0x15d35b8: StoreField: r3->field_2b = r0
    //     0x15d35b8: stur            w0, [x3, #0x2b]
    // 0x15d35bc: r1 = Null
    //     0x15d35bc: mov             x1, NULL
    // 0x15d35c0: r2 = 6
    //     0x15d35c0: movz            x2, #0x6
    // 0x15d35c4: r0 = AllocateArray()
    //     0x15d35c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15d35c8: mov             x2, x0
    // 0x15d35cc: ldur            x0, [fp, #-0x18]
    // 0x15d35d0: stur            x2, [fp, #-0x10]
    // 0x15d35d4: StoreField: r2->field_f = r0
    //     0x15d35d4: stur            w0, [x2, #0xf]
    // 0x15d35d8: r16 = Instance_SizedBox
    //     0x15d35d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x15d35dc: ldr             x16, [x16, #0xaa8]
    // 0x15d35e0: StoreField: r2->field_13 = r16
    //     0x15d35e0: stur            w16, [x2, #0x13]
    // 0x15d35e4: ldur            x0, [fp, #-8]
    // 0x15d35e8: ArrayStore: r2[0] = r0  ; List_4
    //     0x15d35e8: stur            w0, [x2, #0x17]
    // 0x15d35ec: r1 = <Widget>
    //     0x15d35ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15d35f0: r0 = AllocateGrowableArray()
    //     0x15d35f0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15d35f4: mov             x1, x0
    // 0x15d35f8: ldur            x0, [fp, #-0x10]
    // 0x15d35fc: stur            x1, [fp, #-8]
    // 0x15d3600: StoreField: r1->field_f = r0
    //     0x15d3600: stur            w0, [x1, #0xf]
    // 0x15d3604: r0 = 6
    //     0x15d3604: movz            x0, #0x6
    // 0x15d3608: StoreField: r1->field_b = r0
    //     0x15d3608: stur            w0, [x1, #0xb]
    // 0x15d360c: r0 = Row()
    //     0x15d360c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15d3610: r1 = Instance_Axis
    //     0x15d3610: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15d3614: StoreField: r0->field_f = r1
    //     0x15d3614: stur            w1, [x0, #0xf]
    // 0x15d3618: r1 = Instance_MainAxisAlignment
    //     0x15d3618: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x15d361c: ldr             x1, [x1, #0xab0]
    // 0x15d3620: StoreField: r0->field_13 = r1
    //     0x15d3620: stur            w1, [x0, #0x13]
    // 0x15d3624: r1 = Instance_MainAxisSize
    //     0x15d3624: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15d3628: ldr             x1, [x1, #0xa10]
    // 0x15d362c: ArrayStore: r0[0] = r1  ; List_4
    //     0x15d362c: stur            w1, [x0, #0x17]
    // 0x15d3630: r1 = Instance_CrossAxisAlignment
    //     0x15d3630: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15d3634: ldr             x1, [x1, #0xa18]
    // 0x15d3638: StoreField: r0->field_1b = r1
    //     0x15d3638: stur            w1, [x0, #0x1b]
    // 0x15d363c: r1 = Instance_VerticalDirection
    //     0x15d363c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15d3640: ldr             x1, [x1, #0xa20]
    // 0x15d3644: StoreField: r0->field_23 = r1
    //     0x15d3644: stur            w1, [x0, #0x23]
    // 0x15d3648: r1 = Instance_Clip
    //     0x15d3648: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15d364c: ldr             x1, [x1, #0x38]
    // 0x15d3650: StoreField: r0->field_2b = r1
    //     0x15d3650: stur            w1, [x0, #0x2b]
    // 0x15d3654: StoreField: r0->field_2f = rZR
    //     0x15d3654: stur            xzr, [x0, #0x2f]
    // 0x15d3658: ldur            x1, [fp, #-8]
    // 0x15d365c: StoreField: r0->field_b = r1
    //     0x15d365c: stur            w1, [x0, #0xb]
    // 0x15d3660: LeaveFrame
    //     0x15d3660: mov             SP, fp
    //     0x15d3664: ldp             fp, lr, [SP], #0x10
    // 0x15d3668: ret
    //     0x15d3668: ret             
    // 0x15d366c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d366c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d3670: b               #0x15d327c
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e5558, size: 0x2b4
    // 0x15e5558: EnterFrame
    //     0x15e5558: stp             fp, lr, [SP, #-0x10]!
    //     0x15e555c: mov             fp, SP
    // 0x15e5560: AllocStack(0x30)
    //     0x15e5560: sub             SP, SP, #0x30
    // 0x15e5564: SetupParameters(ProfileView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e5564: stur            x1, [fp, #-8]
    //     0x15e5568: stur            x2, [fp, #-0x10]
    // 0x15e556c: CheckStackOverflow
    //     0x15e556c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e5570: cmp             SP, x16
    //     0x15e5574: b.ls            #0x15e5804
    // 0x15e5578: r1 = 2
    //     0x15e5578: movz            x1, #0x2
    // 0x15e557c: r0 = AllocateContext()
    //     0x15e557c: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e5580: ldur            x1, [fp, #-8]
    // 0x15e5584: stur            x0, [fp, #-0x18]
    // 0x15e5588: StoreField: r0->field_f = r1
    //     0x15e5588: stur            w1, [x0, #0xf]
    // 0x15e558c: ldur            x2, [fp, #-0x10]
    // 0x15e5590: StoreField: r0->field_13 = r2
    //     0x15e5590: stur            w2, [x0, #0x13]
    // 0x15e5594: r0 = Obx()
    //     0x15e5594: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e5598: ldur            x2, [fp, #-0x18]
    // 0x15e559c: r1 = Function '<anonymous closure>':.
    //     0x15e559c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40130] AnonymousClosure: (0x15d3254), in [package:customer_app/app/presentation/views/glass/profile/profile_view.dart] ProfileView::appBar (0x15e5558)
    //     0x15e55a0: ldr             x1, [x1, #0x130]
    // 0x15e55a4: stur            x0, [fp, #-0x10]
    // 0x15e55a8: r0 = AllocateClosure()
    //     0x15e55a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e55ac: mov             x1, x0
    // 0x15e55b0: ldur            x0, [fp, #-0x10]
    // 0x15e55b4: StoreField: r0->field_b = r1
    //     0x15e55b4: stur            w1, [x0, #0xb]
    // 0x15e55b8: ldur            x1, [fp, #-8]
    // 0x15e55bc: r0 = controller()
    //     0x15e55bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e55c0: LoadField: r1 = r0->field_7b
    //     0x15e55c0: ldur            w1, [x0, #0x7b]
    // 0x15e55c4: DecompressPointer r1
    //     0x15e55c4: add             x1, x1, HEAP, lsl #32
    // 0x15e55c8: r0 = value()
    //     0x15e55c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e55cc: tbnz            w0, #4, #0x15e5664
    // 0x15e55d0: ldur            x2, [fp, #-0x18]
    // 0x15e55d4: LoadField: r1 = r2->field_13
    //     0x15e55d4: ldur            w1, [x2, #0x13]
    // 0x15e55d8: DecompressPointer r1
    //     0x15e55d8: add             x1, x1, HEAP, lsl #32
    // 0x15e55dc: r0 = of()
    //     0x15e55dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e55e0: LoadField: r1 = r0->field_5b
    //     0x15e55e0: ldur            w1, [x0, #0x5b]
    // 0x15e55e4: DecompressPointer r1
    //     0x15e55e4: add             x1, x1, HEAP, lsl #32
    // 0x15e55e8: stur            x1, [fp, #-8]
    // 0x15e55ec: r0 = ColorFilter()
    //     0x15e55ec: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e55f0: mov             x1, x0
    // 0x15e55f4: ldur            x0, [fp, #-8]
    // 0x15e55f8: stur            x1, [fp, #-0x20]
    // 0x15e55fc: StoreField: r1->field_7 = r0
    //     0x15e55fc: stur            w0, [x1, #7]
    // 0x15e5600: r0 = Instance_BlendMode
    //     0x15e5600: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e5604: ldr             x0, [x0, #0xb30]
    // 0x15e5608: StoreField: r1->field_b = r0
    //     0x15e5608: stur            w0, [x1, #0xb]
    // 0x15e560c: r2 = 1
    //     0x15e560c: movz            x2, #0x1
    // 0x15e5610: StoreField: r1->field_13 = r2
    //     0x15e5610: stur            x2, [x1, #0x13]
    // 0x15e5614: r0 = SvgPicture()
    //     0x15e5614: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e5618: stur            x0, [fp, #-8]
    // 0x15e561c: ldur            x16, [fp, #-0x20]
    // 0x15e5620: str             x16, [SP]
    // 0x15e5624: mov             x1, x0
    // 0x15e5628: r2 = "assets/images/search.svg"
    //     0x15e5628: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e562c: ldr             x2, [x2, #0xa30]
    // 0x15e5630: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e5630: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e5634: ldr             x4, [x4, #0xa38]
    // 0x15e5638: r0 = SvgPicture.asset()
    //     0x15e5638: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e563c: r0 = Align()
    //     0x15e563c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e5640: r3 = Instance_Alignment
    //     0x15e5640: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e5644: ldr             x3, [x3, #0xb10]
    // 0x15e5648: StoreField: r0->field_f = r3
    //     0x15e5648: stur            w3, [x0, #0xf]
    // 0x15e564c: r4 = 1.000000
    //     0x15e564c: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e5650: StoreField: r0->field_13 = r4
    //     0x15e5650: stur            w4, [x0, #0x13]
    // 0x15e5654: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e5654: stur            w4, [x0, #0x17]
    // 0x15e5658: ldur            x1, [fp, #-8]
    // 0x15e565c: StoreField: r0->field_b = r1
    //     0x15e565c: stur            w1, [x0, #0xb]
    // 0x15e5660: b               #0x15e5714
    // 0x15e5664: ldur            x5, [fp, #-0x18]
    // 0x15e5668: r4 = 1.000000
    //     0x15e5668: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e566c: r0 = Instance_BlendMode
    //     0x15e566c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e5670: ldr             x0, [x0, #0xb30]
    // 0x15e5674: r3 = Instance_Alignment
    //     0x15e5674: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e5678: ldr             x3, [x3, #0xb10]
    // 0x15e567c: r2 = 1
    //     0x15e567c: movz            x2, #0x1
    // 0x15e5680: LoadField: r1 = r5->field_13
    //     0x15e5680: ldur            w1, [x5, #0x13]
    // 0x15e5684: DecompressPointer r1
    //     0x15e5684: add             x1, x1, HEAP, lsl #32
    // 0x15e5688: r0 = of()
    //     0x15e5688: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e568c: LoadField: r1 = r0->field_5b
    //     0x15e568c: ldur            w1, [x0, #0x5b]
    // 0x15e5690: DecompressPointer r1
    //     0x15e5690: add             x1, x1, HEAP, lsl #32
    // 0x15e5694: stur            x1, [fp, #-8]
    // 0x15e5698: r0 = ColorFilter()
    //     0x15e5698: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e569c: mov             x1, x0
    // 0x15e56a0: ldur            x0, [fp, #-8]
    // 0x15e56a4: stur            x1, [fp, #-0x20]
    // 0x15e56a8: StoreField: r1->field_7 = r0
    //     0x15e56a8: stur            w0, [x1, #7]
    // 0x15e56ac: r0 = Instance_BlendMode
    //     0x15e56ac: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e56b0: ldr             x0, [x0, #0xb30]
    // 0x15e56b4: StoreField: r1->field_b = r0
    //     0x15e56b4: stur            w0, [x1, #0xb]
    // 0x15e56b8: r0 = 1
    //     0x15e56b8: movz            x0, #0x1
    // 0x15e56bc: StoreField: r1->field_13 = r0
    //     0x15e56bc: stur            x0, [x1, #0x13]
    // 0x15e56c0: r0 = SvgPicture()
    //     0x15e56c0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e56c4: stur            x0, [fp, #-8]
    // 0x15e56c8: ldur            x16, [fp, #-0x20]
    // 0x15e56cc: str             x16, [SP]
    // 0x15e56d0: mov             x1, x0
    // 0x15e56d4: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e56d4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e56d8: ldr             x2, [x2, #0xa40]
    // 0x15e56dc: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e56dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e56e0: ldr             x4, [x4, #0xa38]
    // 0x15e56e4: r0 = SvgPicture.asset()
    //     0x15e56e4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e56e8: r0 = Align()
    //     0x15e56e8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e56ec: mov             x1, x0
    // 0x15e56f0: r0 = Instance_Alignment
    //     0x15e56f0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e56f4: ldr             x0, [x0, #0xb10]
    // 0x15e56f8: StoreField: r1->field_f = r0
    //     0x15e56f8: stur            w0, [x1, #0xf]
    // 0x15e56fc: r0 = 1.000000
    //     0x15e56fc: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e5700: StoreField: r1->field_13 = r0
    //     0x15e5700: stur            w0, [x1, #0x13]
    // 0x15e5704: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e5704: stur            w0, [x1, #0x17]
    // 0x15e5708: ldur            x0, [fp, #-8]
    // 0x15e570c: StoreField: r1->field_b = r0
    //     0x15e570c: stur            w0, [x1, #0xb]
    // 0x15e5710: mov             x0, x1
    // 0x15e5714: stur            x0, [fp, #-8]
    // 0x15e5718: r0 = InkWell()
    //     0x15e5718: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e571c: mov             x3, x0
    // 0x15e5720: ldur            x0, [fp, #-8]
    // 0x15e5724: stur            x3, [fp, #-0x20]
    // 0x15e5728: StoreField: r3->field_b = r0
    //     0x15e5728: stur            w0, [x3, #0xb]
    // 0x15e572c: ldur            x2, [fp, #-0x18]
    // 0x15e5730: r1 = Function '<anonymous closure>':.
    //     0x15e5730: add             x1, PP, #0x40, lsl #12  ; [pp+0x40138] AnonymousClosure: (0x15d71a8), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::appBar (0x15edec4)
    //     0x15e5734: ldr             x1, [x1, #0x138]
    // 0x15e5738: r0 = AllocateClosure()
    //     0x15e5738: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e573c: ldur            x2, [fp, #-0x20]
    // 0x15e5740: StoreField: r2->field_f = r0
    //     0x15e5740: stur            w0, [x2, #0xf]
    // 0x15e5744: r0 = true
    //     0x15e5744: add             x0, NULL, #0x20  ; true
    // 0x15e5748: StoreField: r2->field_43 = r0
    //     0x15e5748: stur            w0, [x2, #0x43]
    // 0x15e574c: r1 = Instance_BoxShape
    //     0x15e574c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e5750: ldr             x1, [x1, #0x80]
    // 0x15e5754: StoreField: r2->field_47 = r1
    //     0x15e5754: stur            w1, [x2, #0x47]
    // 0x15e5758: StoreField: r2->field_6f = r0
    //     0x15e5758: stur            w0, [x2, #0x6f]
    // 0x15e575c: r1 = false
    //     0x15e575c: add             x1, NULL, #0x30  ; false
    // 0x15e5760: StoreField: r2->field_73 = r1
    //     0x15e5760: stur            w1, [x2, #0x73]
    // 0x15e5764: StoreField: r2->field_83 = r0
    //     0x15e5764: stur            w0, [x2, #0x83]
    // 0x15e5768: StoreField: r2->field_7b = r1
    //     0x15e5768: stur            w1, [x2, #0x7b]
    // 0x15e576c: r0 = Obx()
    //     0x15e576c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e5770: ldur            x2, [fp, #-0x18]
    // 0x15e5774: r1 = Function '<anonymous closure>':.
    //     0x15e5774: add             x1, PP, #0x40, lsl #12  ; [pp+0x40140] AnonymousClosure: (0x15e580c), in [package:customer_app/app/presentation/views/glass/profile/profile_view.dart] ProfileView::appBar (0x15e5558)
    //     0x15e5778: ldr             x1, [x1, #0x140]
    // 0x15e577c: stur            x0, [fp, #-8]
    // 0x15e5780: r0 = AllocateClosure()
    //     0x15e5780: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e5784: mov             x1, x0
    // 0x15e5788: ldur            x0, [fp, #-8]
    // 0x15e578c: StoreField: r0->field_b = r1
    //     0x15e578c: stur            w1, [x0, #0xb]
    // 0x15e5790: r1 = Null
    //     0x15e5790: mov             x1, NULL
    // 0x15e5794: r2 = 2
    //     0x15e5794: movz            x2, #0x2
    // 0x15e5798: r0 = AllocateArray()
    //     0x15e5798: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15e579c: mov             x2, x0
    // 0x15e57a0: ldur            x0, [fp, #-8]
    // 0x15e57a4: stur            x2, [fp, #-0x18]
    // 0x15e57a8: StoreField: r2->field_f = r0
    //     0x15e57a8: stur            w0, [x2, #0xf]
    // 0x15e57ac: r1 = <Widget>
    //     0x15e57ac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15e57b0: r0 = AllocateGrowableArray()
    //     0x15e57b0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15e57b4: mov             x1, x0
    // 0x15e57b8: ldur            x0, [fp, #-0x18]
    // 0x15e57bc: stur            x1, [fp, #-8]
    // 0x15e57c0: StoreField: r1->field_f = r0
    //     0x15e57c0: stur            w0, [x1, #0xf]
    // 0x15e57c4: r0 = 2
    //     0x15e57c4: movz            x0, #0x2
    // 0x15e57c8: StoreField: r1->field_b = r0
    //     0x15e57c8: stur            w0, [x1, #0xb]
    // 0x15e57cc: r0 = AppBar()
    //     0x15e57cc: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e57d0: stur            x0, [fp, #-0x18]
    // 0x15e57d4: ldur            x16, [fp, #-0x10]
    // 0x15e57d8: ldur            lr, [fp, #-8]
    // 0x15e57dc: stp             lr, x16, [SP]
    // 0x15e57e0: mov             x1, x0
    // 0x15e57e4: ldur            x2, [fp, #-0x20]
    // 0x15e57e8: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15e57e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15e57ec: ldr             x4, [x4, #0xa58]
    // 0x15e57f0: r0 = AppBar()
    //     0x15e57f0: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e57f4: ldur            x0, [fp, #-0x18]
    // 0x15e57f8: LeaveFrame
    //     0x15e57f8: mov             SP, fp
    //     0x15e57fc: ldp             fp, lr, [SP], #0x10
    // 0x15e5800: ret
    //     0x15e5800: ret             
    // 0x15e5804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e5804: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e5808: b               #0x15e5578
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15e580c, size: 0x2fc
    // 0x15e580c: EnterFrame
    //     0x15e580c: stp             fp, lr, [SP, #-0x10]!
    //     0x15e5810: mov             fp, SP
    // 0x15e5814: AllocStack(0x58)
    //     0x15e5814: sub             SP, SP, #0x58
    // 0x15e5818: SetupParameters()
    //     0x15e5818: ldr             x0, [fp, #0x10]
    //     0x15e581c: ldur            w2, [x0, #0x17]
    //     0x15e5820: add             x2, x2, HEAP, lsl #32
    //     0x15e5824: stur            x2, [fp, #-8]
    // 0x15e5828: CheckStackOverflow
    //     0x15e5828: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e582c: cmp             SP, x16
    //     0x15e5830: b.ls            #0x15e5b00
    // 0x15e5834: LoadField: r1 = r2->field_f
    //     0x15e5834: ldur            w1, [x2, #0xf]
    // 0x15e5838: DecompressPointer r1
    //     0x15e5838: add             x1, x1, HEAP, lsl #32
    // 0x15e583c: r0 = controller()
    //     0x15e583c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e5840: LoadField: r1 = r0->field_6b
    //     0x15e5840: ldur            w1, [x0, #0x6b]
    // 0x15e5844: DecompressPointer r1
    //     0x15e5844: add             x1, x1, HEAP, lsl #32
    // 0x15e5848: r0 = value()
    //     0x15e5848: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e584c: LoadField: r1 = r0->field_1f
    //     0x15e584c: ldur            w1, [x0, #0x1f]
    // 0x15e5850: DecompressPointer r1
    //     0x15e5850: add             x1, x1, HEAP, lsl #32
    // 0x15e5854: cmp             w1, NULL
    // 0x15e5858: b.ne            #0x15e5864
    // 0x15e585c: r0 = Null
    //     0x15e585c: mov             x0, NULL
    // 0x15e5860: b               #0x15e586c
    // 0x15e5864: LoadField: r0 = r1->field_7
    //     0x15e5864: ldur            w0, [x1, #7]
    // 0x15e5868: DecompressPointer r0
    //     0x15e5868: add             x0, x0, HEAP, lsl #32
    // 0x15e586c: cmp             w0, NULL
    // 0x15e5870: b.ne            #0x15e587c
    // 0x15e5874: r0 = false
    //     0x15e5874: add             x0, NULL, #0x30  ; false
    // 0x15e5878: b               #0x15e5a68
    // 0x15e587c: tbnz            w0, #4, #0x15e5a64
    // 0x15e5880: ldur            x2, [fp, #-8]
    // 0x15e5884: LoadField: r1 = r2->field_f
    //     0x15e5884: ldur            w1, [x2, #0xf]
    // 0x15e5888: DecompressPointer r1
    //     0x15e5888: add             x1, x1, HEAP, lsl #32
    // 0x15e588c: r0 = controller()
    //     0x15e588c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e5890: LoadField: r1 = r0->field_7f
    //     0x15e5890: ldur            w1, [x0, #0x7f]
    // 0x15e5894: DecompressPointer r1
    //     0x15e5894: add             x1, x1, HEAP, lsl #32
    // 0x15e5898: r0 = value()
    //     0x15e5898: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e589c: ldur            x2, [fp, #-8]
    // 0x15e58a0: stur            x0, [fp, #-0x10]
    // 0x15e58a4: LoadField: r1 = r2->field_13
    //     0x15e58a4: ldur            w1, [x2, #0x13]
    // 0x15e58a8: DecompressPointer r1
    //     0x15e58a8: add             x1, x1, HEAP, lsl #32
    // 0x15e58ac: r0 = of()
    //     0x15e58ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e58b0: LoadField: r2 = r0->field_5b
    //     0x15e58b0: ldur            w2, [x0, #0x5b]
    // 0x15e58b4: DecompressPointer r2
    //     0x15e58b4: add             x2, x2, HEAP, lsl #32
    // 0x15e58b8: ldur            x0, [fp, #-8]
    // 0x15e58bc: stur            x2, [fp, #-0x18]
    // 0x15e58c0: LoadField: r1 = r0->field_f
    //     0x15e58c0: ldur            w1, [x0, #0xf]
    // 0x15e58c4: DecompressPointer r1
    //     0x15e58c4: add             x1, x1, HEAP, lsl #32
    // 0x15e58c8: r0 = controller()
    //     0x15e58c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e58cc: LoadField: r1 = r0->field_83
    //     0x15e58cc: ldur            w1, [x0, #0x83]
    // 0x15e58d0: DecompressPointer r1
    //     0x15e58d0: add             x1, x1, HEAP, lsl #32
    // 0x15e58d4: r0 = value()
    //     0x15e58d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e58d8: cmp             w0, NULL
    // 0x15e58dc: r16 = true
    //     0x15e58dc: add             x16, NULL, #0x20  ; true
    // 0x15e58e0: r17 = false
    //     0x15e58e0: add             x17, NULL, #0x30  ; false
    // 0x15e58e4: csel            x2, x16, x17, ne
    // 0x15e58e8: ldur            x0, [fp, #-8]
    // 0x15e58ec: stur            x2, [fp, #-0x20]
    // 0x15e58f0: LoadField: r1 = r0->field_f
    //     0x15e58f0: ldur            w1, [x0, #0xf]
    // 0x15e58f4: DecompressPointer r1
    //     0x15e58f4: add             x1, x1, HEAP, lsl #32
    // 0x15e58f8: r0 = controller()
    //     0x15e58f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e58fc: LoadField: r1 = r0->field_83
    //     0x15e58fc: ldur            w1, [x0, #0x83]
    // 0x15e5900: DecompressPointer r1
    //     0x15e5900: add             x1, x1, HEAP, lsl #32
    // 0x15e5904: r0 = value()
    //     0x15e5904: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e5908: str             x0, [SP]
    // 0x15e590c: r0 = _interpolateSingle()
    //     0x15e590c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15e5910: ldur            x2, [fp, #-8]
    // 0x15e5914: stur            x0, [fp, #-0x28]
    // 0x15e5918: LoadField: r1 = r2->field_13
    //     0x15e5918: ldur            w1, [x2, #0x13]
    // 0x15e591c: DecompressPointer r1
    //     0x15e591c: add             x1, x1, HEAP, lsl #32
    // 0x15e5920: r0 = of()
    //     0x15e5920: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e5924: LoadField: r1 = r0->field_87
    //     0x15e5924: ldur            w1, [x0, #0x87]
    // 0x15e5928: DecompressPointer r1
    //     0x15e5928: add             x1, x1, HEAP, lsl #32
    // 0x15e592c: LoadField: r0 = r1->field_27
    //     0x15e592c: ldur            w0, [x1, #0x27]
    // 0x15e5930: DecompressPointer r0
    //     0x15e5930: add             x0, x0, HEAP, lsl #32
    // 0x15e5934: r16 = Instance_Color
    //     0x15e5934: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15e5938: str             x16, [SP]
    // 0x15e593c: mov             x1, x0
    // 0x15e5940: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15e5940: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15e5944: ldr             x4, [x4, #0xf40]
    // 0x15e5948: r0 = copyWith()
    //     0x15e5948: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15e594c: stur            x0, [fp, #-0x30]
    // 0x15e5950: r0 = Text()
    //     0x15e5950: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15e5954: mov             x2, x0
    // 0x15e5958: ldur            x0, [fp, #-0x28]
    // 0x15e595c: stur            x2, [fp, #-0x38]
    // 0x15e5960: StoreField: r2->field_b = r0
    //     0x15e5960: stur            w0, [x2, #0xb]
    // 0x15e5964: ldur            x0, [fp, #-0x30]
    // 0x15e5968: StoreField: r2->field_13 = r0
    //     0x15e5968: stur            w0, [x2, #0x13]
    // 0x15e596c: ldur            x0, [fp, #-8]
    // 0x15e5970: LoadField: r1 = r0->field_13
    //     0x15e5970: ldur            w1, [x0, #0x13]
    // 0x15e5974: DecompressPointer r1
    //     0x15e5974: add             x1, x1, HEAP, lsl #32
    // 0x15e5978: r0 = of()
    //     0x15e5978: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e597c: LoadField: r1 = r0->field_5b
    //     0x15e597c: ldur            w1, [x0, #0x5b]
    // 0x15e5980: DecompressPointer r1
    //     0x15e5980: add             x1, x1, HEAP, lsl #32
    // 0x15e5984: stur            x1, [fp, #-0x28]
    // 0x15e5988: r0 = ColorFilter()
    //     0x15e5988: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e598c: mov             x1, x0
    // 0x15e5990: ldur            x0, [fp, #-0x28]
    // 0x15e5994: stur            x1, [fp, #-0x30]
    // 0x15e5998: StoreField: r1->field_7 = r0
    //     0x15e5998: stur            w0, [x1, #7]
    // 0x15e599c: r0 = Instance_BlendMode
    //     0x15e599c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e59a0: ldr             x0, [x0, #0xb30]
    // 0x15e59a4: StoreField: r1->field_b = r0
    //     0x15e59a4: stur            w0, [x1, #0xb]
    // 0x15e59a8: r0 = 1
    //     0x15e59a8: movz            x0, #0x1
    // 0x15e59ac: StoreField: r1->field_13 = r0
    //     0x15e59ac: stur            x0, [x1, #0x13]
    // 0x15e59b0: r0 = SvgPicture()
    //     0x15e59b0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e59b4: stur            x0, [fp, #-0x28]
    // 0x15e59b8: r16 = Instance_BoxFit
    //     0x15e59b8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15e59bc: ldr             x16, [x16, #0xb18]
    // 0x15e59c0: r30 = 24.000000
    //     0x15e59c0: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15e59c4: ldr             lr, [lr, #0xba8]
    // 0x15e59c8: stp             lr, x16, [SP, #0x10]
    // 0x15e59cc: r16 = 24.000000
    //     0x15e59cc: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15e59d0: ldr             x16, [x16, #0xba8]
    // 0x15e59d4: ldur            lr, [fp, #-0x30]
    // 0x15e59d8: stp             lr, x16, [SP]
    // 0x15e59dc: mov             x1, x0
    // 0x15e59e0: r2 = "assets/images/shopping_bag.svg"
    //     0x15e59e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15e59e4: ldr             x2, [x2, #0xa60]
    // 0x15e59e8: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15e59e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15e59ec: ldr             x4, [x4, #0xa68]
    // 0x15e59f0: r0 = SvgPicture.asset()
    //     0x15e59f0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e59f4: r0 = Badge()
    //     0x15e59f4: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15e59f8: mov             x1, x0
    // 0x15e59fc: ldur            x0, [fp, #-0x18]
    // 0x15e5a00: stur            x1, [fp, #-0x30]
    // 0x15e5a04: StoreField: r1->field_b = r0
    //     0x15e5a04: stur            w0, [x1, #0xb]
    // 0x15e5a08: ldur            x0, [fp, #-0x38]
    // 0x15e5a0c: StoreField: r1->field_27 = r0
    //     0x15e5a0c: stur            w0, [x1, #0x27]
    // 0x15e5a10: ldur            x0, [fp, #-0x20]
    // 0x15e5a14: StoreField: r1->field_2b = r0
    //     0x15e5a14: stur            w0, [x1, #0x2b]
    // 0x15e5a18: ldur            x0, [fp, #-0x28]
    // 0x15e5a1c: StoreField: r1->field_2f = r0
    //     0x15e5a1c: stur            w0, [x1, #0x2f]
    // 0x15e5a20: r0 = Visibility()
    //     0x15e5a20: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15e5a24: mov             x1, x0
    // 0x15e5a28: ldur            x0, [fp, #-0x30]
    // 0x15e5a2c: StoreField: r1->field_b = r0
    //     0x15e5a2c: stur            w0, [x1, #0xb]
    // 0x15e5a30: r0 = Instance_SizedBox
    //     0x15e5a30: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15e5a34: StoreField: r1->field_f = r0
    //     0x15e5a34: stur            w0, [x1, #0xf]
    // 0x15e5a38: ldur            x0, [fp, #-0x10]
    // 0x15e5a3c: StoreField: r1->field_13 = r0
    //     0x15e5a3c: stur            w0, [x1, #0x13]
    // 0x15e5a40: r0 = false
    //     0x15e5a40: add             x0, NULL, #0x30  ; false
    // 0x15e5a44: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e5a44: stur            w0, [x1, #0x17]
    // 0x15e5a48: StoreField: r1->field_1b = r0
    //     0x15e5a48: stur            w0, [x1, #0x1b]
    // 0x15e5a4c: StoreField: r1->field_1f = r0
    //     0x15e5a4c: stur            w0, [x1, #0x1f]
    // 0x15e5a50: StoreField: r1->field_23 = r0
    //     0x15e5a50: stur            w0, [x1, #0x23]
    // 0x15e5a54: StoreField: r1->field_27 = r0
    //     0x15e5a54: stur            w0, [x1, #0x27]
    // 0x15e5a58: StoreField: r1->field_2b = r0
    //     0x15e5a58: stur            w0, [x1, #0x2b]
    // 0x15e5a5c: mov             x0, x1
    // 0x15e5a60: b               #0x15e5a80
    // 0x15e5a64: r0 = false
    //     0x15e5a64: add             x0, NULL, #0x30  ; false
    // 0x15e5a68: r0 = Container()
    //     0x15e5a68: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15e5a6c: mov             x1, x0
    // 0x15e5a70: stur            x0, [fp, #-0x10]
    // 0x15e5a74: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15e5a74: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15e5a78: r0 = Container()
    //     0x15e5a78: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15e5a7c: ldur            x0, [fp, #-0x10]
    // 0x15e5a80: stur            x0, [fp, #-0x10]
    // 0x15e5a84: r0 = InkWell()
    //     0x15e5a84: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e5a88: mov             x3, x0
    // 0x15e5a8c: ldur            x0, [fp, #-0x10]
    // 0x15e5a90: stur            x3, [fp, #-0x18]
    // 0x15e5a94: StoreField: r3->field_b = r0
    //     0x15e5a94: stur            w0, [x3, #0xb]
    // 0x15e5a98: ldur            x2, [fp, #-8]
    // 0x15e5a9c: r1 = Function '<anonymous closure>':.
    //     0x15e5a9c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40148] AnonymousClosure: (0x15e5b08), in [package:customer_app/app/presentation/views/glass/profile/profile_view.dart] ProfileView::appBar (0x15e5558)
    //     0x15e5aa0: ldr             x1, [x1, #0x148]
    // 0x15e5aa4: r0 = AllocateClosure()
    //     0x15e5aa4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e5aa8: mov             x1, x0
    // 0x15e5aac: ldur            x0, [fp, #-0x18]
    // 0x15e5ab0: StoreField: r0->field_f = r1
    //     0x15e5ab0: stur            w1, [x0, #0xf]
    // 0x15e5ab4: r1 = true
    //     0x15e5ab4: add             x1, NULL, #0x20  ; true
    // 0x15e5ab8: StoreField: r0->field_43 = r1
    //     0x15e5ab8: stur            w1, [x0, #0x43]
    // 0x15e5abc: r2 = Instance_BoxShape
    //     0x15e5abc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e5ac0: ldr             x2, [x2, #0x80]
    // 0x15e5ac4: StoreField: r0->field_47 = r2
    //     0x15e5ac4: stur            w2, [x0, #0x47]
    // 0x15e5ac8: StoreField: r0->field_6f = r1
    //     0x15e5ac8: stur            w1, [x0, #0x6f]
    // 0x15e5acc: r2 = false
    //     0x15e5acc: add             x2, NULL, #0x30  ; false
    // 0x15e5ad0: StoreField: r0->field_73 = r2
    //     0x15e5ad0: stur            w2, [x0, #0x73]
    // 0x15e5ad4: StoreField: r0->field_83 = r1
    //     0x15e5ad4: stur            w1, [x0, #0x83]
    // 0x15e5ad8: StoreField: r0->field_7b = r2
    //     0x15e5ad8: stur            w2, [x0, #0x7b]
    // 0x15e5adc: r0 = Padding()
    //     0x15e5adc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15e5ae0: r1 = Instance_EdgeInsets
    //     0x15e5ae0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea78] Obj!EdgeInsets@d5a0e1
    //     0x15e5ae4: ldr             x1, [x1, #0xa78]
    // 0x15e5ae8: StoreField: r0->field_f = r1
    //     0x15e5ae8: stur            w1, [x0, #0xf]
    // 0x15e5aec: ldur            x1, [fp, #-0x18]
    // 0x15e5af0: StoreField: r0->field_b = r1
    //     0x15e5af0: stur            w1, [x0, #0xb]
    // 0x15e5af4: LeaveFrame
    //     0x15e5af4: mov             SP, fp
    //     0x15e5af8: ldp             fp, lr, [SP], #0x10
    // 0x15e5afc: ret
    //     0x15e5afc: ret             
    // 0x15e5b00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e5b00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e5b04: b               #0x15e5834
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15e5b08, size: 0xdc
    // 0x15e5b08: EnterFrame
    //     0x15e5b08: stp             fp, lr, [SP, #-0x10]!
    //     0x15e5b0c: mov             fp, SP
    // 0x15e5b10: AllocStack(0x28)
    //     0x15e5b10: sub             SP, SP, #0x28
    // 0x15e5b14: SetupParameters()
    //     0x15e5b14: ldr             x0, [fp, #0x10]
    //     0x15e5b18: ldur            w2, [x0, #0x17]
    //     0x15e5b1c: add             x2, x2, HEAP, lsl #32
    //     0x15e5b20: stur            x2, [fp, #-8]
    // 0x15e5b24: CheckStackOverflow
    //     0x15e5b24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e5b28: cmp             SP, x16
    //     0x15e5b2c: b.ls            #0x15e5bdc
    // 0x15e5b30: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15e5b30: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15e5b34: ldr             x0, [x0, #0x1c80]
    //     0x15e5b38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15e5b3c: cmp             w0, w16
    //     0x15e5b40: b.ne            #0x15e5b4c
    //     0x15e5b44: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15e5b48: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15e5b4c: r1 = Null
    //     0x15e5b4c: mov             x1, NULL
    // 0x15e5b50: r2 = 4
    //     0x15e5b50: movz            x2, #0x4
    // 0x15e5b54: r0 = AllocateArray()
    //     0x15e5b54: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15e5b58: r16 = "previousScreenSource"
    //     0x15e5b58: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x15e5b5c: ldr             x16, [x16, #0x448]
    // 0x15e5b60: StoreField: r0->field_f = r16
    //     0x15e5b60: stur            w16, [x0, #0xf]
    // 0x15e5b64: r16 = "profile_page"
    //     0x15e5b64: add             x16, PP, #0x36, lsl #12  ; [pp+0x36cf8] "profile_page"
    //     0x15e5b68: ldr             x16, [x16, #0xcf8]
    // 0x15e5b6c: StoreField: r0->field_13 = r16
    //     0x15e5b6c: stur            w16, [x0, #0x13]
    // 0x15e5b70: r16 = <String, String>
    //     0x15e5b70: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x15e5b74: ldr             x16, [x16, #0x788]
    // 0x15e5b78: stp             x0, x16, [SP]
    // 0x15e5b7c: r0 = Map._fromLiteral()
    //     0x15e5b7c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x15e5b80: r16 = "/bag"
    //     0x15e5b80: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x15e5b84: ldr             x16, [x16, #0x468]
    // 0x15e5b88: stp             x16, NULL, [SP, #8]
    // 0x15e5b8c: str             x0, [SP]
    // 0x15e5b90: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x15e5b90: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x15e5b94: ldr             x4, [x4, #0x438]
    // 0x15e5b98: r0 = GetNavigation.toNamed()
    //     0x15e5b98: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15e5b9c: stur            x0, [fp, #-0x10]
    // 0x15e5ba0: cmp             w0, NULL
    // 0x15e5ba4: b.eq            #0x15e5bcc
    // 0x15e5ba8: ldur            x2, [fp, #-8]
    // 0x15e5bac: r1 = Function '<anonymous closure>':.
    //     0x15e5bac: add             x1, PP, #0x40, lsl #12  ; [pp+0x40150] AnonymousClosure: (0x15d6d84), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::appBar (0x15edec4)
    //     0x15e5bb0: ldr             x1, [x1, #0x150]
    // 0x15e5bb4: r0 = AllocateClosure()
    //     0x15e5bb4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e5bb8: ldur            x16, [fp, #-0x10]
    // 0x15e5bbc: stp             x16, NULL, [SP, #8]
    // 0x15e5bc0: str             x0, [SP]
    // 0x15e5bc4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15e5bc4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15e5bc8: r0 = then()
    //     0x15e5bc8: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15e5bcc: r0 = Null
    //     0x15e5bcc: mov             x0, NULL
    // 0x15e5bd0: LeaveFrame
    //     0x15e5bd0: mov             SP, fp
    //     0x15e5bd4: ldp             fp, lr, [SP], #0x10
    // 0x15e5bd8: ret
    //     0x15e5bd8: ret             
    // 0x15e5bdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e5bdc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e5be0: b               #0x15e5b30
  }
}
