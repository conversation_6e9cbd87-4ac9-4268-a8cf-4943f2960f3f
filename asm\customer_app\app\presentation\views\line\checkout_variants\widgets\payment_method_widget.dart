// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/payment_method_widget.dart

// class id: 1049499, size: 0x8
class :: {
}

// class id: 3263, size: 0x1c, field offset: 0x14
class _PaymentMethodWidgetState extends State<dynamic> {

  late String _currentSelectedPaymentMode; // offset: 0x14

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x801620, size: 0x64
    // 0x801620: ldr             x1, [SP]
    // 0x801624: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x801624: ldur            w2, [x1, #0x17]
    // 0x801628: DecompressPointer r2
    //     0x801628: add             x2, x2, HEAP, lsl #32
    // 0x80162c: LoadField: r1 = r2->field_f
    //     0x80162c: ldur            w1, [x2, #0xf]
    // 0x801630: DecompressPointer r1
    //     0x801630: add             x1, x1, HEAP, lsl #32
    // 0x801634: LoadField: r2 = r1->field_b
    //     0x801634: ldur            w2, [x1, #0xb]
    // 0x801638: DecompressPointer r2
    //     0x801638: add             x2, x2, HEAP, lsl #32
    // 0x80163c: cmp             w2, NULL
    // 0x801640: b.eq            #0x801678
    // 0x801644: LoadField: r0 = r2->field_1b
    //     0x801644: ldur            w0, [x2, #0x1b]
    // 0x801648: DecompressPointer r0
    //     0x801648: add             x0, x0, HEAP, lsl #32
    // 0x80164c: StoreField: r1->field_13 = r0
    //     0x80164c: stur            w0, [x1, #0x13]
    //     0x801650: ldurb           w16, [x1, #-1]
    //     0x801654: ldurb           w17, [x0, #-1]
    //     0x801658: and             x16, x17, x16, lsr #2
    //     0x80165c: tst             x16, HEAP, lsr #32
    //     0x801660: b.eq            #0x801670
    //     0x801664: str             lr, [SP, #-8]!
    //     0x801668: bl              #0x16f5888  ; WriteBarrierWrappersStub
    //     0x80166c: ldr             lr, [SP], #8
    // 0x801670: r0 = Null
    //     0x801670: mov             x0, NULL
    // 0x801674: ret
    //     0x801674: ret             
    // 0x801678: EnterFrame
    //     0x801678: stp             fp, lr, [SP, #-0x10]!
    //     0x80167c: mov             fp, SP
    // 0x801680: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x801680: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x807038, size: 0x184
    // 0x807038: EnterFrame
    //     0x807038: stp             fp, lr, [SP, #-0x10]!
    //     0x80703c: mov             fp, SP
    // 0x807040: AllocStack(0x28)
    //     0x807040: sub             SP, SP, #0x28
    // 0x807044: SetupParameters(_PaymentMethodWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x807044: mov             x0, x2
    //     0x807048: stur            x1, [fp, #-8]
    //     0x80704c: stur            x2, [fp, #-0x10]
    // 0x807050: CheckStackOverflow
    //     0x807050: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x807054: cmp             SP, x16
    //     0x807058: b.ls            #0x8071ac
    // 0x80705c: r1 = 1
    //     0x80705c: movz            x1, #0x1
    // 0x807060: r0 = AllocateContext()
    //     0x807060: bl              #0x16f6108  ; AllocateContextStub
    // 0x807064: mov             x4, x0
    // 0x807068: ldur            x3, [fp, #-8]
    // 0x80706c: stur            x4, [fp, #-0x18]
    // 0x807070: StoreField: r4->field_f = r3
    //     0x807070: stur            w3, [x4, #0xf]
    // 0x807074: ldur            x0, [fp, #-0x10]
    // 0x807078: r2 = Null
    //     0x807078: mov             x2, NULL
    // 0x80707c: r1 = Null
    //     0x80707c: mov             x1, NULL
    // 0x807080: r4 = 60
    //     0x807080: movz            x4, #0x3c
    // 0x807084: branchIfSmi(r0, 0x807090)
    //     0x807084: tbz             w0, #0, #0x807090
    // 0x807088: r4 = LoadClassIdInstr(r0)
    //     0x807088: ldur            x4, [x0, #-1]
    //     0x80708c: ubfx            x4, x4, #0xc, #0x14
    // 0x807090: cmp             x4, #0xfa9
    // 0x807094: b.eq            #0x8070ac
    // 0x807098: r8 = PaymentMethodWidget
    //     0x807098: add             x8, PP, #0x54, lsl #12  ; [pp+0x543b8] Type: PaymentMethodWidget
    //     0x80709c: ldr             x8, [x8, #0x3b8]
    // 0x8070a0: r3 = Null
    //     0x8070a0: add             x3, PP, #0x54, lsl #12  ; [pp+0x543c0] Null
    //     0x8070a4: ldr             x3, [x3, #0x3c0]
    // 0x8070a8: r0 = PaymentMethodWidget()
    //     0x8070a8: bl              #0x801684  ; IsType_PaymentMethodWidget_Stub
    // 0x8070ac: ldur            x3, [fp, #-8]
    // 0x8070b0: LoadField: r2 = r3->field_7
    //     0x8070b0: ldur            w2, [x3, #7]
    // 0x8070b4: DecompressPointer r2
    //     0x8070b4: add             x2, x2, HEAP, lsl #32
    // 0x8070b8: ldur            x0, [fp, #-0x10]
    // 0x8070bc: r1 = Null
    //     0x8070bc: mov             x1, NULL
    // 0x8070c0: cmp             w2, NULL
    // 0x8070c4: b.eq            #0x8070e8
    // 0x8070c8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8070c8: ldur            w4, [x2, #0x17]
    // 0x8070cc: DecompressPointer r4
    //     0x8070cc: add             x4, x4, HEAP, lsl #32
    // 0x8070d0: r8 = X0 bound StatefulWidget
    //     0x8070d0: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c7a0] TypeParameter: X0 bound StatefulWidget
    //     0x8070d4: ldr             x8, [x8, #0x7a0]
    // 0x8070d8: LoadField: r9 = r4->field_7
    //     0x8070d8: ldur            x9, [x4, #7]
    // 0x8070dc: r3 = Null
    //     0x8070dc: add             x3, PP, #0x54, lsl #12  ; [pp+0x543d0] Null
    //     0x8070e0: ldr             x3, [x3, #0x3d0]
    // 0x8070e4: blr             x9
    // 0x8070e8: ldur            x0, [fp, #-0x10]
    // 0x8070ec: LoadField: r1 = r0->field_1b
    //     0x8070ec: ldur            w1, [x0, #0x1b]
    // 0x8070f0: DecompressPointer r1
    //     0x8070f0: add             x1, x1, HEAP, lsl #32
    // 0x8070f4: ldur            x2, [fp, #-8]
    // 0x8070f8: LoadField: r0 = r2->field_b
    //     0x8070f8: ldur            w0, [x2, #0xb]
    // 0x8070fc: DecompressPointer r0
    //     0x8070fc: add             x0, x0, HEAP, lsl #32
    // 0x807100: cmp             w0, NULL
    // 0x807104: b.eq            #0x8071b4
    // 0x807108: LoadField: r3 = r0->field_1b
    //     0x807108: ldur            w3, [x0, #0x1b]
    // 0x80710c: DecompressPointer r3
    //     0x80710c: add             x3, x3, HEAP, lsl #32
    // 0x807110: r0 = LoadClassIdInstr(r1)
    //     0x807110: ldur            x0, [x1, #-1]
    //     0x807114: ubfx            x0, x0, #0xc, #0x14
    // 0x807118: stp             x3, x1, [SP]
    // 0x80711c: mov             lr, x0
    // 0x807120: ldr             lr, [x21, lr, lsl #3]
    // 0x807124: blr             lr
    // 0x807128: tbz             w0, #4, #0x80719c
    // 0x80712c: ldur            x0, [fp, #-8]
    // 0x807130: ldur            x2, [fp, #-0x18]
    // 0x807134: r1 = Function '<anonymous closure>':.
    //     0x807134: add             x1, PP, #0x54, lsl #12  ; [pp+0x543e0] AnonymousClosure: (0x801620), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::didUpdateWidget (0x807038)
    //     0x807138: ldr             x1, [x1, #0x3e0]
    // 0x80713c: r0 = AllocateClosure()
    //     0x80713c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x807140: ldur            x1, [fp, #-8]
    // 0x807144: mov             x2, x0
    // 0x807148: r0 = setState()
    //     0x807148: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x80714c: ldur            x0, [fp, #-8]
    // 0x807150: LoadField: r1 = r0->field_b
    //     0x807150: ldur            w1, [x0, #0xb]
    // 0x807154: DecompressPointer r1
    //     0x807154: add             x1, x1, HEAP, lsl #32
    // 0x807158: cmp             w1, NULL
    // 0x80715c: b.eq            #0x8071b8
    // 0x807160: LoadField: r2 = r1->field_1b
    //     0x807160: ldur            w2, [x1, #0x1b]
    // 0x807164: DecompressPointer r2
    //     0x807164: add             x2, x2, HEAP, lsl #32
    // 0x807168: LoadField: r1 = r2->field_7
    //     0x807168: ldur            w1, [x2, #7]
    // 0x80716c: cbz             w1, #0x80719c
    // 0x807170: LoadField: r1 = r0->field_f
    //     0x807170: ldur            w1, [x0, #0xf]
    // 0x807174: DecompressPointer r1
    //     0x807174: add             x1, x1, HEAP, lsl #32
    // 0x807178: cmp             w1, NULL
    // 0x80717c: b.eq            #0x80719c
    // 0x807180: ldur            x2, [fp, #-0x18]
    // 0x807184: r1 = Function '<anonymous closure>':.
    //     0x807184: add             x1, PP, #0x54, lsl #12  ; [pp+0x543e8] AnonymousClosure: (0x8071bc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::didUpdateWidget (0x807038)
    //     0x807188: ldr             x1, [x1, #0x3e8]
    // 0x80718c: r0 = AllocateClosure()
    //     0x80718c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x807190: mov             x2, x0
    // 0x807194: r1 = <Null?>
    //     0x807194: ldr             x1, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x807198: r0 = Future.microtask()
    //     0x807198: bl              #0x801434  ; [dart:async] Future::Future.microtask
    // 0x80719c: r0 = Null
    //     0x80719c: mov             x0, NULL
    // 0x8071a0: LeaveFrame
    //     0x8071a0: mov             SP, fp
    //     0x8071a4: ldp             fp, lr, [SP], #0x10
    // 0x8071a8: ret
    //     0x8071a8: ret             
    // 0x8071ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8071ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8071b0: b               #0x80705c
    // 0x8071b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8071b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8071b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8071b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x8071bc, size: 0xa0
    // 0x8071bc: EnterFrame
    //     0x8071bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8071c0: mov             fp, SP
    // 0x8071c4: AllocStack(0x20)
    //     0x8071c4: sub             SP, SP, #0x20
    // 0x8071c8: SetupParameters()
    //     0x8071c8: ldr             x0, [fp, #0x10]
    //     0x8071cc: ldur            w1, [x0, #0x17]
    //     0x8071d0: add             x1, x1, HEAP, lsl #32
    // 0x8071d4: CheckStackOverflow
    //     0x8071d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8071d8: cmp             SP, x16
    //     0x8071dc: b.ls            #0x807250
    // 0x8071e0: LoadField: r0 = r1->field_f
    //     0x8071e0: ldur            w0, [x1, #0xf]
    // 0x8071e4: DecompressPointer r0
    //     0x8071e4: add             x0, x0, HEAP, lsl #32
    // 0x8071e8: LoadField: r1 = r0->field_f
    //     0x8071e8: ldur            w1, [x0, #0xf]
    // 0x8071ec: DecompressPointer r1
    //     0x8071ec: add             x1, x1, HEAP, lsl #32
    // 0x8071f0: cmp             w1, NULL
    // 0x8071f4: b.eq            #0x807240
    // 0x8071f8: LoadField: r1 = r0->field_b
    //     0x8071f8: ldur            w1, [x0, #0xb]
    // 0x8071fc: DecompressPointer r1
    //     0x8071fc: add             x1, x1, HEAP, lsl #32
    // 0x807200: cmp             w1, NULL
    // 0x807204: b.eq            #0x807258
    // 0x807208: LoadField: r0 = r1->field_1b
    //     0x807208: ldur            w0, [x1, #0x1b]
    // 0x80720c: DecompressPointer r0
    //     0x80720c: add             x0, x0, HEAP, lsl #32
    // 0x807210: LoadField: r2 = r1->field_f
    //     0x807210: ldur            w2, [x1, #0xf]
    // 0x807214: DecompressPointer r2
    //     0x807214: add             x2, x2, HEAP, lsl #32
    // 0x807218: stp             x0, x2, [SP, #0x10]
    // 0x80721c: r16 = true
    //     0x80721c: add             x16, NULL, #0x20  ; true
    // 0x807220: r30 = false
    //     0x807220: add             lr, NULL, #0x30  ; false
    // 0x807224: stp             lr, x16, [SP]
    // 0x807228: r4 = 0
    //     0x807228: movz            x4, #0
    // 0x80722c: ldr             x0, [SP, #0x18]
    // 0x807230: r16 = UnlinkedCall_0x613b5c
    //     0x807230: add             x16, PP, #0x54, lsl #12  ; [pp+0x543f0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x807234: add             x16, x16, #0x3f0
    // 0x807238: ldp             x5, lr, [x16]
    // 0x80723c: blr             lr
    // 0x807240: r0 = Null
    //     0x807240: mov             x0, NULL
    // 0x807244: LeaveFrame
    //     0x807244: mov             SP, fp
    //     0x807248: ldp             fp, lr, [SP], #0x10
    // 0x80724c: ret
    //     0x80724c: ret             
    // 0x807250: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x807250: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x807254: b               #0x8071e0
    // 0x807258: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x807258: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x9486c4, size: 0x170
    // 0x9486c4: EnterFrame
    //     0x9486c4: stp             fp, lr, [SP, #-0x10]!
    //     0x9486c8: mov             fp, SP
    // 0x9486cc: AllocStack(0x18)
    //     0x9486cc: sub             SP, SP, #0x18
    // 0x9486d0: SetupParameters(_PaymentMethodWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x9486d0: stur            x1, [fp, #-8]
    // 0x9486d4: CheckStackOverflow
    //     0x9486d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9486d8: cmp             SP, x16
    //     0x9486dc: b.ls            #0x948824
    // 0x9486e0: r1 = 1
    //     0x9486e0: movz            x1, #0x1
    // 0x9486e4: r0 = AllocateContext()
    //     0x9486e4: bl              #0x16f6108  ; AllocateContextStub
    // 0x9486e8: mov             x2, x0
    // 0x9486ec: ldur            x1, [fp, #-8]
    // 0x9486f0: StoreField: r2->field_f = r1
    //     0x9486f0: stur            w1, [x2, #0xf]
    // 0x9486f4: LoadField: r0 = r1->field_b
    //     0x9486f4: ldur            w0, [x1, #0xb]
    // 0x9486f8: DecompressPointer r0
    //     0x9486f8: add             x0, x0, HEAP, lsl #32
    // 0x9486fc: cmp             w0, NULL
    // 0x948700: b.eq            #0x94882c
    // 0x948704: LoadField: r3 = r0->field_1b
    //     0x948704: ldur            w3, [x0, #0x1b]
    // 0x948708: DecompressPointer r3
    //     0x948708: add             x3, x3, HEAP, lsl #32
    // 0x94870c: mov             x0, x3
    // 0x948710: StoreField: r1->field_13 = r0
    //     0x948710: stur            w0, [x1, #0x13]
    //     0x948714: ldurb           w16, [x1, #-1]
    //     0x948718: ldurb           w17, [x0, #-1]
    //     0x94871c: and             x16, x17, x16, lsr #2
    //     0x948720: tst             x16, HEAP, lsr #32
    //     0x948724: b.eq            #0x94872c
    //     0x948728: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x94872c: LoadField: r0 = r3->field_7
    //     0x94872c: ldur            w0, [x3, #7]
    // 0x948730: cbz             w0, #0x948814
    // 0x948734: r0 = LoadStaticField(0x878)
    //     0x948734: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x948738: ldr             x0, [x0, #0x10f0]
    // 0x94873c: cmp             w0, NULL
    // 0x948740: b.eq            #0x948830
    // 0x948744: LoadField: r3 = r0->field_53
    //     0x948744: ldur            w3, [x0, #0x53]
    // 0x948748: DecompressPointer r3
    //     0x948748: add             x3, x3, HEAP, lsl #32
    // 0x94874c: stur            x3, [fp, #-0x10]
    // 0x948750: LoadField: r0 = r3->field_7
    //     0x948750: ldur            w0, [x3, #7]
    // 0x948754: DecompressPointer r0
    //     0x948754: add             x0, x0, HEAP, lsl #32
    // 0x948758: stur            x0, [fp, #-8]
    // 0x94875c: r1 = Function '<anonymous closure>':.
    //     0x94875c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54400] AnonymousClosure: (0x948834), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::initState (0x9486c4)
    //     0x948760: ldr             x1, [x1, #0x400]
    // 0x948764: r0 = AllocateClosure()
    //     0x948764: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x948768: ldur            x2, [fp, #-8]
    // 0x94876c: mov             x3, x0
    // 0x948770: r1 = Null
    //     0x948770: mov             x1, NULL
    // 0x948774: stur            x3, [fp, #-8]
    // 0x948778: cmp             w2, NULL
    // 0x94877c: b.eq            #0x94879c
    // 0x948780: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x948780: ldur            w4, [x2, #0x17]
    // 0x948784: DecompressPointer r4
    //     0x948784: add             x4, x4, HEAP, lsl #32
    // 0x948788: r8 = X0
    //     0x948788: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x94878c: LoadField: r9 = r4->field_7
    //     0x94878c: ldur            x9, [x4, #7]
    // 0x948790: r3 = Null
    //     0x948790: add             x3, PP, #0x54, lsl #12  ; [pp+0x54408] Null
    //     0x948794: ldr             x3, [x3, #0x408]
    // 0x948798: blr             x9
    // 0x94879c: ldur            x0, [fp, #-0x10]
    // 0x9487a0: LoadField: r1 = r0->field_b
    //     0x9487a0: ldur            w1, [x0, #0xb]
    // 0x9487a4: LoadField: r2 = r0->field_f
    //     0x9487a4: ldur            w2, [x0, #0xf]
    // 0x9487a8: DecompressPointer r2
    //     0x9487a8: add             x2, x2, HEAP, lsl #32
    // 0x9487ac: LoadField: r3 = r2->field_b
    //     0x9487ac: ldur            w3, [x2, #0xb]
    // 0x9487b0: r2 = LoadInt32Instr(r1)
    //     0x9487b0: sbfx            x2, x1, #1, #0x1f
    // 0x9487b4: stur            x2, [fp, #-0x18]
    // 0x9487b8: r1 = LoadInt32Instr(r3)
    //     0x9487b8: sbfx            x1, x3, #1, #0x1f
    // 0x9487bc: cmp             x2, x1
    // 0x9487c0: b.ne            #0x9487cc
    // 0x9487c4: mov             x1, x0
    // 0x9487c8: r0 = _growToNextCapacity()
    //     0x9487c8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9487cc: ldur            x2, [fp, #-0x10]
    // 0x9487d0: ldur            x3, [fp, #-0x18]
    // 0x9487d4: add             x4, x3, #1
    // 0x9487d8: lsl             x5, x4, #1
    // 0x9487dc: StoreField: r2->field_b = r5
    //     0x9487dc: stur            w5, [x2, #0xb]
    // 0x9487e0: LoadField: r1 = r2->field_f
    //     0x9487e0: ldur            w1, [x2, #0xf]
    // 0x9487e4: DecompressPointer r1
    //     0x9487e4: add             x1, x1, HEAP, lsl #32
    // 0x9487e8: ldur            x0, [fp, #-8]
    // 0x9487ec: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9487ec: add             x25, x1, x3, lsl #2
    //     0x9487f0: add             x25, x25, #0xf
    //     0x9487f4: str             w0, [x25]
    //     0x9487f8: tbz             w0, #0, #0x948814
    //     0x9487fc: ldurb           w16, [x1, #-1]
    //     0x948800: ldurb           w17, [x0, #-1]
    //     0x948804: and             x16, x17, x16, lsr #2
    //     0x948808: tst             x16, HEAP, lsr #32
    //     0x94880c: b.eq            #0x948814
    //     0x948810: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x948814: r0 = Null
    //     0x948814: mov             x0, NULL
    // 0x948818: LeaveFrame
    //     0x948818: mov             SP, fp
    //     0x94881c: ldp             fp, lr, [SP], #0x10
    // 0x948820: ret
    //     0x948820: ret             
    // 0x948824: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x948824: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x948828: b               #0x9486e0
    // 0x94882c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94882c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x948830: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x948830: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x948834, size: 0xb4
    // 0x948834: EnterFrame
    //     0x948834: stp             fp, lr, [SP, #-0x10]!
    //     0x948838: mov             fp, SP
    // 0x94883c: AllocStack(0x20)
    //     0x94883c: sub             SP, SP, #0x20
    // 0x948840: SetupParameters()
    //     0x948840: ldr             x0, [fp, #0x18]
    //     0x948844: ldur            w1, [x0, #0x17]
    //     0x948848: add             x1, x1, HEAP, lsl #32
    // 0x94884c: CheckStackOverflow
    //     0x94884c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x948850: cmp             SP, x16
    //     0x948854: b.ls            #0x9488dc
    // 0x948858: LoadField: r0 = r1->field_f
    //     0x948858: ldur            w0, [x1, #0xf]
    // 0x94885c: DecompressPointer r0
    //     0x94885c: add             x0, x0, HEAP, lsl #32
    // 0x948860: LoadField: r1 = r0->field_f
    //     0x948860: ldur            w1, [x0, #0xf]
    // 0x948864: DecompressPointer r1
    //     0x948864: add             x1, x1, HEAP, lsl #32
    // 0x948868: cmp             w1, NULL
    // 0x94886c: b.eq            #0x9488cc
    // 0x948870: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x948870: ldur            w1, [x0, #0x17]
    // 0x948874: DecompressPointer r1
    //     0x948874: add             x1, x1, HEAP, lsl #32
    // 0x948878: tbz             w1, #4, #0x9488cc
    // 0x94887c: r1 = true
    //     0x94887c: add             x1, NULL, #0x20  ; true
    // 0x948880: ArrayStore: r0[0] = r1  ; List_4
    //     0x948880: stur            w1, [x0, #0x17]
    // 0x948884: LoadField: r1 = r0->field_b
    //     0x948884: ldur            w1, [x0, #0xb]
    // 0x948888: DecompressPointer r1
    //     0x948888: add             x1, x1, HEAP, lsl #32
    // 0x94888c: cmp             w1, NULL
    // 0x948890: b.eq            #0x9488e4
    // 0x948894: LoadField: r0 = r1->field_1b
    //     0x948894: ldur            w0, [x1, #0x1b]
    // 0x948898: DecompressPointer r0
    //     0x948898: add             x0, x0, HEAP, lsl #32
    // 0x94889c: LoadField: r2 = r1->field_f
    //     0x94889c: ldur            w2, [x1, #0xf]
    // 0x9488a0: DecompressPointer r2
    //     0x9488a0: add             x2, x2, HEAP, lsl #32
    // 0x9488a4: stp             x0, x2, [SP, #0x10]
    // 0x9488a8: r16 = true
    //     0x9488a8: add             x16, NULL, #0x20  ; true
    // 0x9488ac: r30 = false
    //     0x9488ac: add             lr, NULL, #0x30  ; false
    // 0x9488b0: stp             lr, x16, [SP]
    // 0x9488b4: r4 = 0
    //     0x9488b4: movz            x4, #0
    // 0x9488b8: ldr             x0, [SP, #0x18]
    // 0x9488bc: r16 = UnlinkedCall_0x613b5c
    //     0x9488bc: add             x16, PP, #0x54, lsl #12  ; [pp+0x54418] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9488c0: add             x16, x16, #0x418
    // 0x9488c4: ldp             x5, lr, [x16]
    // 0x9488c8: blr             lr
    // 0x9488cc: r0 = Null
    //     0x9488cc: mov             x0, NULL
    // 0x9488d0: LeaveFrame
    //     0x9488d0: mov             SP, fp
    //     0x9488d4: ldp             fp, lr, [SP], #0x10
    // 0x9488d8: ret
    //     0x9488d8: ret             
    // 0x9488dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9488dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9488e0: b               #0x948858
    // 0x9488e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9488e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbc3af8, size: 0x358
    // 0xbc3af8: EnterFrame
    //     0xbc3af8: stp             fp, lr, [SP, #-0x10]!
    //     0xbc3afc: mov             fp, SP
    // 0xbc3b00: AllocStack(0x48)
    //     0xbc3b00: sub             SP, SP, #0x48
    // 0xbc3b04: SetupParameters(_PaymentMethodWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbc3b04: mov             x0, x1
    //     0xbc3b08: stur            x1, [fp, #-8]
    //     0xbc3b0c: mov             x1, x2
    //     0xbc3b10: stur            x2, [fp, #-0x10]
    // 0xbc3b14: CheckStackOverflow
    //     0xbc3b14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc3b18: cmp             SP, x16
    //     0xbc3b1c: b.ls            #0xbc3e44
    // 0xbc3b20: r1 = 1
    //     0xbc3b20: movz            x1, #0x1
    // 0xbc3b24: r0 = AllocateContext()
    //     0xbc3b24: bl              #0x16f6108  ; AllocateContextStub
    // 0xbc3b28: mov             x2, x0
    // 0xbc3b2c: ldur            x0, [fp, #-8]
    // 0xbc3b30: stur            x2, [fp, #-0x18]
    // 0xbc3b34: StoreField: r2->field_f = r0
    //     0xbc3b34: stur            w0, [x2, #0xf]
    // 0xbc3b38: ldur            x1, [fp, #-0x10]
    // 0xbc3b3c: r0 = of()
    //     0xbc3b3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc3b40: LoadField: r1 = r0->field_87
    //     0xbc3b40: ldur            w1, [x0, #0x87]
    // 0xbc3b44: DecompressPointer r1
    //     0xbc3b44: add             x1, x1, HEAP, lsl #32
    // 0xbc3b48: LoadField: r0 = r1->field_7
    //     0xbc3b48: ldur            w0, [x1, #7]
    // 0xbc3b4c: DecompressPointer r0
    //     0xbc3b4c: add             x0, x0, HEAP, lsl #32
    // 0xbc3b50: stur            x0, [fp, #-0x10]
    // 0xbc3b54: r1 = Instance_Color
    //     0xbc3b54: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc3b58: d0 = 0.700000
    //     0xbc3b58: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbc3b5c: ldr             d0, [x17, #0xf48]
    // 0xbc3b60: r0 = withOpacity()
    //     0xbc3b60: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbc3b64: r16 = 14.000000
    //     0xbc3b64: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbc3b68: ldr             x16, [x16, #0x1d8]
    // 0xbc3b6c: stp             x0, x16, [SP]
    // 0xbc3b70: ldur            x1, [fp, #-0x10]
    // 0xbc3b74: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbc3b74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbc3b78: ldr             x4, [x4, #0xaa0]
    // 0xbc3b7c: r0 = copyWith()
    //     0xbc3b7c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc3b80: stur            x0, [fp, #-0x10]
    // 0xbc3b84: r0 = Text()
    //     0xbc3b84: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc3b88: mov             x1, x0
    // 0xbc3b8c: r0 = "Payment Method"
    //     0xbc3b8c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34110] "Payment Method"
    //     0xbc3b90: ldr             x0, [x0, #0x110]
    // 0xbc3b94: stur            x1, [fp, #-0x20]
    // 0xbc3b98: StoreField: r1->field_b = r0
    //     0xbc3b98: stur            w0, [x1, #0xb]
    // 0xbc3b9c: ldur            x0, [fp, #-0x10]
    // 0xbc3ba0: StoreField: r1->field_13 = r0
    //     0xbc3ba0: stur            w0, [x1, #0x13]
    // 0xbc3ba4: r0 = SvgPicture()
    //     0xbc3ba4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbc3ba8: stur            x0, [fp, #-0x10]
    // 0xbc3bac: r16 = "return order"
    //     0xbc3bac: add             x16, PP, #0x38, lsl #12  ; [pp+0x38c78] "return order"
    //     0xbc3bb0: ldr             x16, [x16, #0xc78]
    // 0xbc3bb4: r30 = Instance_BoxFit
    //     0xbc3bb4: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbc3bb8: ldr             lr, [lr, #0xb18]
    // 0xbc3bbc: stp             lr, x16, [SP]
    // 0xbc3bc0: mov             x1, x0
    // 0xbc3bc4: r2 = "assets/images/secure_icon.svg"
    //     0xbc3bc4: add             x2, PP, #0x38, lsl #12  ; [pp+0x38c80] "assets/images/secure_icon.svg"
    //     0xbc3bc8: ldr             x2, [x2, #0xc80]
    // 0xbc3bcc: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xbc3bcc: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xbc3bd0: ldr             x4, [x4, #0xb28]
    // 0xbc3bd4: r0 = SvgPicture.asset()
    //     0xbc3bd4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbc3bd8: r1 = Null
    //     0xbc3bd8: mov             x1, NULL
    // 0xbc3bdc: r2 = 4
    //     0xbc3bdc: movz            x2, #0x4
    // 0xbc3be0: r0 = AllocateArray()
    //     0xbc3be0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc3be4: mov             x2, x0
    // 0xbc3be8: ldur            x0, [fp, #-0x20]
    // 0xbc3bec: stur            x2, [fp, #-0x28]
    // 0xbc3bf0: StoreField: r2->field_f = r0
    //     0xbc3bf0: stur            w0, [x2, #0xf]
    // 0xbc3bf4: ldur            x0, [fp, #-0x10]
    // 0xbc3bf8: StoreField: r2->field_13 = r0
    //     0xbc3bf8: stur            w0, [x2, #0x13]
    // 0xbc3bfc: r1 = <Widget>
    //     0xbc3bfc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc3c00: r0 = AllocateGrowableArray()
    //     0xbc3c00: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc3c04: mov             x1, x0
    // 0xbc3c08: ldur            x0, [fp, #-0x28]
    // 0xbc3c0c: stur            x1, [fp, #-0x10]
    // 0xbc3c10: StoreField: r1->field_f = r0
    //     0xbc3c10: stur            w0, [x1, #0xf]
    // 0xbc3c14: r2 = 4
    //     0xbc3c14: movz            x2, #0x4
    // 0xbc3c18: StoreField: r1->field_b = r2
    //     0xbc3c18: stur            w2, [x1, #0xb]
    // 0xbc3c1c: r0 = Row()
    //     0xbc3c1c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbc3c20: mov             x1, x0
    // 0xbc3c24: r0 = Instance_Axis
    //     0xbc3c24: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc3c28: stur            x1, [fp, #-0x20]
    // 0xbc3c2c: StoreField: r1->field_f = r0
    //     0xbc3c2c: stur            w0, [x1, #0xf]
    // 0xbc3c30: r0 = Instance_MainAxisAlignment
    //     0xbc3c30: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbc3c34: ldr             x0, [x0, #0xa8]
    // 0xbc3c38: StoreField: r1->field_13 = r0
    //     0xbc3c38: stur            w0, [x1, #0x13]
    // 0xbc3c3c: r0 = Instance_MainAxisSize
    //     0xbc3c3c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc3c40: ldr             x0, [x0, #0xa10]
    // 0xbc3c44: ArrayStore: r1[0] = r0  ; List_4
    //     0xbc3c44: stur            w0, [x1, #0x17]
    // 0xbc3c48: r2 = Instance_CrossAxisAlignment
    //     0xbc3c48: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc3c4c: ldr             x2, [x2, #0xa18]
    // 0xbc3c50: StoreField: r1->field_1b = r2
    //     0xbc3c50: stur            w2, [x1, #0x1b]
    // 0xbc3c54: r3 = Instance_VerticalDirection
    //     0xbc3c54: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc3c58: ldr             x3, [x3, #0xa20]
    // 0xbc3c5c: StoreField: r1->field_23 = r3
    //     0xbc3c5c: stur            w3, [x1, #0x23]
    // 0xbc3c60: r4 = Instance_Clip
    //     0xbc3c60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc3c64: ldr             x4, [x4, #0x38]
    // 0xbc3c68: StoreField: r1->field_2b = r4
    //     0xbc3c68: stur            w4, [x1, #0x2b]
    // 0xbc3c6c: StoreField: r1->field_2f = rZR
    //     0xbc3c6c: stur            xzr, [x1, #0x2f]
    // 0xbc3c70: ldur            x5, [fp, #-0x10]
    // 0xbc3c74: StoreField: r1->field_b = r5
    //     0xbc3c74: stur            w5, [x1, #0xb]
    // 0xbc3c78: r0 = Padding()
    //     0xbc3c78: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc3c7c: mov             x3, x0
    // 0xbc3c80: r0 = Instance_EdgeInsets
    //     0xbc3c80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbc3c84: ldr             x0, [x0, #0x668]
    // 0xbc3c88: stur            x3, [fp, #-0x10]
    // 0xbc3c8c: StoreField: r3->field_f = r0
    //     0xbc3c8c: stur            w0, [x3, #0xf]
    // 0xbc3c90: ldur            x0, [fp, #-0x20]
    // 0xbc3c94: StoreField: r3->field_b = r0
    //     0xbc3c94: stur            w0, [x3, #0xb]
    // 0xbc3c98: ldur            x0, [fp, #-8]
    // 0xbc3c9c: LoadField: r1 = r0->field_b
    //     0xbc3c9c: ldur            w1, [x0, #0xb]
    // 0xbc3ca0: DecompressPointer r1
    //     0xbc3ca0: add             x1, x1, HEAP, lsl #32
    // 0xbc3ca4: cmp             w1, NULL
    // 0xbc3ca8: b.eq            #0xbc3e4c
    // 0xbc3cac: LoadField: r0 = r1->field_b
    //     0xbc3cac: ldur            w0, [x1, #0xb]
    // 0xbc3cb0: DecompressPointer r0
    //     0xbc3cb0: add             x0, x0, HEAP, lsl #32
    // 0xbc3cb4: LoadField: r1 = r0->field_b
    //     0xbc3cb4: ldur            w1, [x0, #0xb]
    // 0xbc3cb8: DecompressPointer r1
    //     0xbc3cb8: add             x1, x1, HEAP, lsl #32
    // 0xbc3cbc: cmp             w1, NULL
    // 0xbc3cc0: b.ne            #0xbc3ccc
    // 0xbc3cc4: r0 = Null
    //     0xbc3cc4: mov             x0, NULL
    // 0xbc3cc8: b               #0xbc3d00
    // 0xbc3ccc: LoadField: r0 = r1->field_1f
    //     0xbc3ccc: ldur            w0, [x1, #0x1f]
    // 0xbc3cd0: DecompressPointer r0
    //     0xbc3cd0: add             x0, x0, HEAP, lsl #32
    // 0xbc3cd4: cmp             w0, NULL
    // 0xbc3cd8: b.ne            #0xbc3ce4
    // 0xbc3cdc: r0 = Null
    //     0xbc3cdc: mov             x0, NULL
    // 0xbc3ce0: b               #0xbc3d00
    // 0xbc3ce4: LoadField: r1 = r0->field_7
    //     0xbc3ce4: ldur            w1, [x0, #7]
    // 0xbc3ce8: DecompressPointer r1
    //     0xbc3ce8: add             x1, x1, HEAP, lsl #32
    // 0xbc3cec: cmp             w1, NULL
    // 0xbc3cf0: b.ne            #0xbc3cfc
    // 0xbc3cf4: r0 = Null
    //     0xbc3cf4: mov             x0, NULL
    // 0xbc3cf8: b               #0xbc3d00
    // 0xbc3cfc: LoadField: r0 = r1->field_b
    //     0xbc3cfc: ldur            w0, [x1, #0xb]
    // 0xbc3d00: cmp             w0, NULL
    // 0xbc3d04: b.ne            #0xbc3d10
    // 0xbc3d08: r0 = 0
    //     0xbc3d08: movz            x0, #0
    // 0xbc3d0c: b               #0xbc3d18
    // 0xbc3d10: r1 = LoadInt32Instr(r0)
    //     0xbc3d10: sbfx            x1, x0, #1, #0x1f
    // 0xbc3d14: mov             x0, x1
    // 0xbc3d18: ldur            x2, [fp, #-0x18]
    // 0xbc3d1c: stur            x0, [fp, #-0x30]
    // 0xbc3d20: r1 = Function '<anonymous closure>':.
    //     0xbc3d20: add             x1, PP, #0x54, lsl #12  ; [pp+0x54338] AnonymousClosure: (0xbd2580), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::build (0xbc3af8)
    //     0xbc3d24: ldr             x1, [x1, #0x338]
    // 0xbc3d28: r0 = AllocateClosure()
    //     0xbc3d28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc3d2c: r1 = Function '<anonymous closure>':.
    //     0xbc3d2c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54340] AnonymousClosure: (0xbc3e50), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_screen.dart] ExchangeCheckoutScreen::body (0x15059dc)
    //     0xbc3d30: ldr             x1, [x1, #0x340]
    // 0xbc3d34: r2 = Null
    //     0xbc3d34: mov             x2, NULL
    // 0xbc3d38: stur            x0, [fp, #-8]
    // 0xbc3d3c: r0 = AllocateClosure()
    //     0xbc3d3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc3d40: stur            x0, [fp, #-0x18]
    // 0xbc3d44: r0 = ListView()
    //     0xbc3d44: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbc3d48: stur            x0, [fp, #-0x20]
    // 0xbc3d4c: r16 = true
    //     0xbc3d4c: add             x16, NULL, #0x20  ; true
    // 0xbc3d50: r30 = false
    //     0xbc3d50: add             lr, NULL, #0x30  ; false
    // 0xbc3d54: stp             lr, x16, [SP, #8]
    // 0xbc3d58: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbc3d58: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbc3d5c: ldr             x16, [x16, #0x1c8]
    // 0xbc3d60: str             x16, [SP]
    // 0xbc3d64: mov             x1, x0
    // 0xbc3d68: ldur            x2, [fp, #-8]
    // 0xbc3d6c: ldur            x3, [fp, #-0x30]
    // 0xbc3d70: ldur            x5, [fp, #-0x18]
    // 0xbc3d74: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x6, primary, 0x5, shrinkWrap, 0x4, null]
    //     0xbc3d74: add             x4, PP, #0x34, lsl #12  ; [pp+0x34138] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x6, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0xbc3d78: ldr             x4, [x4, #0x138]
    // 0xbc3d7c: r0 = ListView.separated()
    //     0xbc3d7c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbc3d80: r0 = Padding()
    //     0xbc3d80: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc3d84: mov             x3, x0
    // 0xbc3d88: r0 = Instance_EdgeInsets
    //     0xbc3d88: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbc3d8c: ldr             x0, [x0, #0x770]
    // 0xbc3d90: stur            x3, [fp, #-8]
    // 0xbc3d94: StoreField: r3->field_f = r0
    //     0xbc3d94: stur            w0, [x3, #0xf]
    // 0xbc3d98: ldur            x0, [fp, #-0x20]
    // 0xbc3d9c: StoreField: r3->field_b = r0
    //     0xbc3d9c: stur            w0, [x3, #0xb]
    // 0xbc3da0: r1 = Null
    //     0xbc3da0: mov             x1, NULL
    // 0xbc3da4: r2 = 4
    //     0xbc3da4: movz            x2, #0x4
    // 0xbc3da8: r0 = AllocateArray()
    //     0xbc3da8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc3dac: mov             x2, x0
    // 0xbc3db0: ldur            x0, [fp, #-0x10]
    // 0xbc3db4: stur            x2, [fp, #-0x18]
    // 0xbc3db8: StoreField: r2->field_f = r0
    //     0xbc3db8: stur            w0, [x2, #0xf]
    // 0xbc3dbc: ldur            x0, [fp, #-8]
    // 0xbc3dc0: StoreField: r2->field_13 = r0
    //     0xbc3dc0: stur            w0, [x2, #0x13]
    // 0xbc3dc4: r1 = <Widget>
    //     0xbc3dc4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc3dc8: r0 = AllocateGrowableArray()
    //     0xbc3dc8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc3dcc: mov             x1, x0
    // 0xbc3dd0: ldur            x0, [fp, #-0x18]
    // 0xbc3dd4: stur            x1, [fp, #-8]
    // 0xbc3dd8: StoreField: r1->field_f = r0
    //     0xbc3dd8: stur            w0, [x1, #0xf]
    // 0xbc3ddc: r0 = 4
    //     0xbc3ddc: movz            x0, #0x4
    // 0xbc3de0: StoreField: r1->field_b = r0
    //     0xbc3de0: stur            w0, [x1, #0xb]
    // 0xbc3de4: r0 = Column()
    //     0xbc3de4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbc3de8: r1 = Instance_Axis
    //     0xbc3de8: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbc3dec: StoreField: r0->field_f = r1
    //     0xbc3dec: stur            w1, [x0, #0xf]
    // 0xbc3df0: r1 = Instance_MainAxisAlignment
    //     0xbc3df0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbc3df4: ldr             x1, [x1, #0xa08]
    // 0xbc3df8: StoreField: r0->field_13 = r1
    //     0xbc3df8: stur            w1, [x0, #0x13]
    // 0xbc3dfc: r1 = Instance_MainAxisSize
    //     0xbc3dfc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc3e00: ldr             x1, [x1, #0xa10]
    // 0xbc3e04: ArrayStore: r0[0] = r1  ; List_4
    //     0xbc3e04: stur            w1, [x0, #0x17]
    // 0xbc3e08: r1 = Instance_CrossAxisAlignment
    //     0xbc3e08: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc3e0c: ldr             x1, [x1, #0xa18]
    // 0xbc3e10: StoreField: r0->field_1b = r1
    //     0xbc3e10: stur            w1, [x0, #0x1b]
    // 0xbc3e14: r1 = Instance_VerticalDirection
    //     0xbc3e14: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc3e18: ldr             x1, [x1, #0xa20]
    // 0xbc3e1c: StoreField: r0->field_23 = r1
    //     0xbc3e1c: stur            w1, [x0, #0x23]
    // 0xbc3e20: r1 = Instance_Clip
    //     0xbc3e20: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc3e24: ldr             x1, [x1, #0x38]
    // 0xbc3e28: StoreField: r0->field_2b = r1
    //     0xbc3e28: stur            w1, [x0, #0x2b]
    // 0xbc3e2c: StoreField: r0->field_2f = rZR
    //     0xbc3e2c: stur            xzr, [x0, #0x2f]
    // 0xbc3e30: ldur            x1, [fp, #-8]
    // 0xbc3e34: StoreField: r0->field_b = r1
    //     0xbc3e34: stur            w1, [x0, #0xb]
    // 0xbc3e38: LeaveFrame
    //     0xbc3e38: mov             SP, fp
    //     0xbc3e3c: ldp             fp, lr, [SP], #0x10
    // 0xbc3e40: ret
    //     0xbc3e40: ret             
    // 0xbc3e44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc3e44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc3e48: b               #0xbc3b20
    // 0xbc3e4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc3e4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget? <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbd2580, size: 0xf0
    // 0xbd2580: EnterFrame
    //     0xbd2580: stp             fp, lr, [SP, #-0x10]!
    //     0xbd2584: mov             fp, SP
    // 0xbd2588: ldr             x0, [fp, #0x20]
    // 0xbd258c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbd258c: ldur            w1, [x0, #0x17]
    // 0xbd2590: DecompressPointer r1
    //     0xbd2590: add             x1, x1, HEAP, lsl #32
    // 0xbd2594: CheckStackOverflow
    //     0xbd2594: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd2598: cmp             SP, x16
    //     0xbd259c: b.ls            #0xbd2660
    // 0xbd25a0: LoadField: r2 = r1->field_f
    //     0xbd25a0: ldur            w2, [x1, #0xf]
    // 0xbd25a4: DecompressPointer r2
    //     0xbd25a4: add             x2, x2, HEAP, lsl #32
    // 0xbd25a8: LoadField: r0 = r2->field_b
    //     0xbd25a8: ldur            w0, [x2, #0xb]
    // 0xbd25ac: DecompressPointer r0
    //     0xbd25ac: add             x0, x0, HEAP, lsl #32
    // 0xbd25b0: cmp             w0, NULL
    // 0xbd25b4: b.eq            #0xbd2668
    // 0xbd25b8: LoadField: r1 = r0->field_b
    //     0xbd25b8: ldur            w1, [x0, #0xb]
    // 0xbd25bc: DecompressPointer r1
    //     0xbd25bc: add             x1, x1, HEAP, lsl #32
    // 0xbd25c0: LoadField: r0 = r1->field_b
    //     0xbd25c0: ldur            w0, [x1, #0xb]
    // 0xbd25c4: DecompressPointer r0
    //     0xbd25c4: add             x0, x0, HEAP, lsl #32
    // 0xbd25c8: cmp             w0, NULL
    // 0xbd25cc: b.ne            #0xbd25d8
    // 0xbd25d0: r0 = Null
    //     0xbd25d0: mov             x0, NULL
    // 0xbd25d4: b               #0xbd2644
    // 0xbd25d8: LoadField: r1 = r0->field_1f
    //     0xbd25d8: ldur            w1, [x0, #0x1f]
    // 0xbd25dc: DecompressPointer r1
    //     0xbd25dc: add             x1, x1, HEAP, lsl #32
    // 0xbd25e0: cmp             w1, NULL
    // 0xbd25e4: b.ne            #0xbd25f0
    // 0xbd25e8: r0 = Null
    //     0xbd25e8: mov             x0, NULL
    // 0xbd25ec: b               #0xbd2644
    // 0xbd25f0: LoadField: r3 = r1->field_7
    //     0xbd25f0: ldur            w3, [x1, #7]
    // 0xbd25f4: DecompressPointer r3
    //     0xbd25f4: add             x3, x3, HEAP, lsl #32
    // 0xbd25f8: cmp             w3, NULL
    // 0xbd25fc: b.ne            #0xbd2608
    // 0xbd2600: r0 = Null
    //     0xbd2600: mov             x0, NULL
    // 0xbd2604: b               #0xbd2644
    // 0xbd2608: ldr             x0, [fp, #0x10]
    // 0xbd260c: LoadField: r1 = r3->field_b
    //     0xbd260c: ldur            w1, [x3, #0xb]
    // 0xbd2610: r4 = LoadInt32Instr(r0)
    //     0xbd2610: sbfx            x4, x0, #1, #0x1f
    //     0xbd2614: tbz             w0, #0, #0xbd261c
    //     0xbd2618: ldur            x4, [x0, #7]
    // 0xbd261c: r0 = LoadInt32Instr(r1)
    //     0xbd261c: sbfx            x0, x1, #1, #0x1f
    // 0xbd2620: mov             x1, x4
    // 0xbd2624: cmp             x1, x0
    // 0xbd2628: b.hs            #0xbd266c
    // 0xbd262c: LoadField: r0 = r3->field_f
    //     0xbd262c: ldur            w0, [x3, #0xf]
    // 0xbd2630: DecompressPointer r0
    //     0xbd2630: add             x0, x0, HEAP, lsl #32
    // 0xbd2634: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbd2634: add             x16, x0, x4, lsl #2
    //     0xbd2638: ldur            w1, [x16, #0xf]
    // 0xbd263c: DecompressPointer r1
    //     0xbd263c: add             x1, x1, HEAP, lsl #32
    // 0xbd2640: mov             x0, x1
    // 0xbd2644: mov             x1, x2
    // 0xbd2648: mov             x2, x0
    // 0xbd264c: ldr             x3, [fp, #0x18]
    // 0xbd2650: r0 = paymentMethodCardLineTheme()
    //     0xbd2650: bl              #0xbd2670  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::paymentMethodCardLineTheme
    // 0xbd2654: LeaveFrame
    //     0xbd2654: mov             SP, fp
    //     0xbd2658: ldp             fp, lr, [SP], #0x10
    // 0xbd265c: ret
    //     0xbd265c: ret             
    // 0xbd2660: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd2660: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd2664: b               #0xbd25a0
    // 0xbd2668: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd2668: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd266c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd266c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ paymentMethodCardLineTheme(/* No info */) {
    // ** addr: 0xbd2670, size: 0xc74
    // 0xbd2670: EnterFrame
    //     0xbd2670: stp             fp, lr, [SP, #-0x10]!
    //     0xbd2674: mov             fp, SP
    // 0xbd2678: AllocStack(0x60)
    //     0xbd2678: sub             SP, SP, #0x60
    // 0xbd267c: SetupParameters(_PaymentMethodWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xbd267c: mov             x0, x1
    //     0xbd2680: stur            x1, [fp, #-8]
    //     0xbd2684: mov             x1, x3
    //     0xbd2688: stur            x2, [fp, #-0x10]
    //     0xbd268c: stur            x3, [fp, #-0x18]
    // 0xbd2690: CheckStackOverflow
    //     0xbd2690: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd2694: cmp             SP, x16
    //     0xbd2698: b.ls            #0xbd32d0
    // 0xbd269c: r1 = 2
    //     0xbd269c: movz            x1, #0x2
    // 0xbd26a0: r0 = AllocateContext()
    //     0xbd26a0: bl              #0x16f6108  ; AllocateContextStub
    // 0xbd26a4: mov             x1, x0
    // 0xbd26a8: ldur            x0, [fp, #-8]
    // 0xbd26ac: stur            x1, [fp, #-0x20]
    // 0xbd26b0: StoreField: r1->field_f = r0
    //     0xbd26b0: stur            w0, [x1, #0xf]
    // 0xbd26b4: ldur            x2, [fp, #-0x10]
    // 0xbd26b8: StoreField: r1->field_13 = r2
    //     0xbd26b8: stur            w2, [x1, #0x13]
    // 0xbd26bc: LoadField: r3 = r0->field_13
    //     0xbd26bc: ldur            w3, [x0, #0x13]
    // 0xbd26c0: DecompressPointer r3
    //     0xbd26c0: add             x3, x3, HEAP, lsl #32
    // 0xbd26c4: r16 = Sentinel
    //     0xbd26c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbd26c8: cmp             w3, w16
    // 0xbd26cc: b.eq            #0xbd32d8
    // 0xbd26d0: cmp             w2, NULL
    // 0xbd26d4: b.ne            #0xbd26e0
    // 0xbd26d8: r0 = Null
    //     0xbd26d8: mov             x0, NULL
    // 0xbd26dc: b               #0xbd26e8
    // 0xbd26e0: LoadField: r0 = r2->field_7
    //     0xbd26e0: ldur            w0, [x2, #7]
    // 0xbd26e4: DecompressPointer r0
    //     0xbd26e4: add             x0, x0, HEAP, lsl #32
    // 0xbd26e8: r2 = LoadClassIdInstr(r3)
    //     0xbd26e8: ldur            x2, [x3, #-1]
    //     0xbd26ec: ubfx            x2, x2, #0xc, #0x14
    // 0xbd26f0: stp             x0, x3, [SP]
    // 0xbd26f4: mov             x0, x2
    // 0xbd26f8: mov             lr, x0
    // 0xbd26fc: ldr             lr, [x21, lr, lsl #3]
    // 0xbd2700: blr             lr
    // 0xbd2704: tbnz            w0, #4, #0xbd2714
    // 0xbd2708: r0 = Instance_IconData
    //     0xbd2708: add             x0, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0xbd270c: ldr             x0, [x0, #0x30]
    // 0xbd2710: b               #0xbd271c
    // 0xbd2714: r0 = Instance_IconData
    //     0xbd2714: add             x0, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0xbd2718: ldr             x0, [x0, #0x38]
    // 0xbd271c: ldur            x2, [fp, #-0x20]
    // 0xbd2720: ldur            x1, [fp, #-0x18]
    // 0xbd2724: stur            x0, [fp, #-8]
    // 0xbd2728: r0 = of()
    //     0xbd2728: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd272c: LoadField: r1 = r0->field_5b
    //     0xbd272c: ldur            w1, [x0, #0x5b]
    // 0xbd2730: DecompressPointer r1
    //     0xbd2730: add             x1, x1, HEAP, lsl #32
    // 0xbd2734: stur            x1, [fp, #-0x10]
    // 0xbd2738: r0 = Icon()
    //     0xbd2738: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbd273c: mov             x2, x0
    // 0xbd2740: ldur            x0, [fp, #-8]
    // 0xbd2744: stur            x2, [fp, #-0x28]
    // 0xbd2748: StoreField: r2->field_b = r0
    //     0xbd2748: stur            w0, [x2, #0xb]
    // 0xbd274c: ldur            x0, [fp, #-0x10]
    // 0xbd2750: StoreField: r2->field_23 = r0
    //     0xbd2750: stur            w0, [x2, #0x23]
    // 0xbd2754: ldur            x0, [fp, #-0x20]
    // 0xbd2758: LoadField: r1 = r0->field_13
    //     0xbd2758: ldur            w1, [x0, #0x13]
    // 0xbd275c: DecompressPointer r1
    //     0xbd275c: add             x1, x1, HEAP, lsl #32
    // 0xbd2760: cmp             w1, NULL
    // 0xbd2764: b.ne            #0xbd2770
    // 0xbd2768: r1 = Null
    //     0xbd2768: mov             x1, NULL
    // 0xbd276c: b               #0xbd277c
    // 0xbd2770: LoadField: r3 = r1->field_b
    //     0xbd2770: ldur            w3, [x1, #0xb]
    // 0xbd2774: DecompressPointer r3
    //     0xbd2774: add             x3, x3, HEAP, lsl #32
    // 0xbd2778: mov             x1, x3
    // 0xbd277c: cmp             w1, NULL
    // 0xbd2780: b.ne            #0xbd278c
    // 0xbd2784: r3 = ""
    //     0xbd2784: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd2788: b               #0xbd2790
    // 0xbd278c: mov             x3, x1
    // 0xbd2790: ldur            x1, [fp, #-0x18]
    // 0xbd2794: stur            x3, [fp, #-8]
    // 0xbd2798: r0 = of()
    //     0xbd2798: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd279c: LoadField: r1 = r0->field_87
    //     0xbd279c: ldur            w1, [x0, #0x87]
    // 0xbd27a0: DecompressPointer r1
    //     0xbd27a0: add             x1, x1, HEAP, lsl #32
    // 0xbd27a4: LoadField: r0 = r1->field_2b
    //     0xbd27a4: ldur            w0, [x1, #0x2b]
    // 0xbd27a8: DecompressPointer r0
    //     0xbd27a8: add             x0, x0, HEAP, lsl #32
    // 0xbd27ac: r16 = 14.000000
    //     0xbd27ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd27b0: ldr             x16, [x16, #0x1d8]
    // 0xbd27b4: r30 = Instance_Color
    //     0xbd27b4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd27b8: stp             lr, x16, [SP]
    // 0xbd27bc: mov             x1, x0
    // 0xbd27c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd27c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd27c4: ldr             x4, [x4, #0xaa0]
    // 0xbd27c8: r0 = copyWith()
    //     0xbd27c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd27cc: stur            x0, [fp, #-0x10]
    // 0xbd27d0: r0 = Text()
    //     0xbd27d0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd27d4: mov             x1, x0
    // 0xbd27d8: ldur            x0, [fp, #-8]
    // 0xbd27dc: stur            x1, [fp, #-0x30]
    // 0xbd27e0: StoreField: r1->field_b = r0
    //     0xbd27e0: stur            w0, [x1, #0xb]
    // 0xbd27e4: ldur            x0, [fp, #-0x10]
    // 0xbd27e8: StoreField: r1->field_13 = r0
    //     0xbd27e8: stur            w0, [x1, #0x13]
    // 0xbd27ec: ldur            x2, [fp, #-0x20]
    // 0xbd27f0: LoadField: r0 = r2->field_13
    //     0xbd27f0: ldur            w0, [x2, #0x13]
    // 0xbd27f4: DecompressPointer r0
    //     0xbd27f4: add             x0, x0, HEAP, lsl #32
    // 0xbd27f8: cmp             w0, NULL
    // 0xbd27fc: b.ne            #0xbd2808
    // 0xbd2800: r0 = Null
    //     0xbd2800: mov             x0, NULL
    // 0xbd2804: b               #0xbd2814
    // 0xbd2808: LoadField: r3 = r0->field_7
    //     0xbd2808: ldur            w3, [x0, #7]
    // 0xbd280c: DecompressPointer r3
    //     0xbd280c: add             x3, x3, HEAP, lsl #32
    // 0xbd2810: mov             x0, x3
    // 0xbd2814: r3 = LoadClassIdInstr(r0)
    //     0xbd2814: ldur            x3, [x0, #-1]
    //     0xbd2818: ubfx            x3, x3, #0xc, #0x14
    // 0xbd281c: r16 = "partial-cod"
    //     0xbd281c: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c830] "partial-cod"
    //     0xbd2820: ldr             x16, [x16, #0x830]
    // 0xbd2824: stp             x16, x0, [SP]
    // 0xbd2828: mov             x0, x3
    // 0xbd282c: mov             lr, x0
    // 0xbd2830: ldr             lr, [x21, lr, lsl #3]
    // 0xbd2834: blr             lr
    // 0xbd2838: r1 = Instance_Color
    //     0xbd2838: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb18] Obj!Color@d6ad71
    //     0xbd283c: ldr             x1, [x1, #0xb18]
    // 0xbd2840: d0 = 0.080000
    //     0xbd2840: add             x17, PP, #0x27, lsl #12  ; [pp+0x27798] IMM: double(0.08) from 0x3fb47ae147ae147b
    //     0xbd2844: ldr             d0, [x17, #0x798]
    // 0xbd2848: stur            x0, [fp, #-8]
    // 0xbd284c: r0 = withOpacity()
    //     0xbd284c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd2850: stur            x0, [fp, #-0x10]
    // 0xbd2854: r0 = BoxDecoration()
    //     0xbd2854: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbd2858: mov             x2, x0
    // 0xbd285c: ldur            x0, [fp, #-0x10]
    // 0xbd2860: stur            x2, [fp, #-0x38]
    // 0xbd2864: StoreField: r2->field_7 = r0
    //     0xbd2864: stur            w0, [x2, #7]
    // 0xbd2868: r0 = Instance_BorderRadius
    //     0xbd2868: add             x0, PP, #0x39, lsl #12  ; [pp+0x39798] Obj!BorderRadius@d5a341
    //     0xbd286c: ldr             x0, [x0, #0x798]
    // 0xbd2870: StoreField: r2->field_13 = r0
    //     0xbd2870: stur            w0, [x2, #0x13]
    // 0xbd2874: r3 = Instance_BoxShape
    //     0xbd2874: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbd2878: ldr             x3, [x3, #0x80]
    // 0xbd287c: StoreField: r2->field_23 = r3
    //     0xbd287c: stur            w3, [x2, #0x23]
    // 0xbd2880: ldur            x1, [fp, #-0x18]
    // 0xbd2884: r0 = of()
    //     0xbd2884: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd2888: LoadField: r1 = r0->field_87
    //     0xbd2888: ldur            w1, [x0, #0x87]
    // 0xbd288c: DecompressPointer r1
    //     0xbd288c: add             x1, x1, HEAP, lsl #32
    // 0xbd2890: LoadField: r0 = r1->field_7
    //     0xbd2890: ldur            w0, [x1, #7]
    // 0xbd2894: DecompressPointer r0
    //     0xbd2894: add             x0, x0, HEAP, lsl #32
    // 0xbd2898: r16 = 12.000000
    //     0xbd2898: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbd289c: ldr             x16, [x16, #0x9e8]
    // 0xbd28a0: r30 = Instance_Color
    //     0xbd28a0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbd28a4: ldr             lr, [lr, #0x858]
    // 0xbd28a8: stp             lr, x16, [SP]
    // 0xbd28ac: mov             x1, x0
    // 0xbd28b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd28b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd28b4: ldr             x4, [x4, #0xaa0]
    // 0xbd28b8: r0 = copyWith()
    //     0xbd28b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd28bc: stur            x0, [fp, #-0x10]
    // 0xbd28c0: r0 = Text()
    //     0xbd28c0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd28c4: mov             x1, x0
    // 0xbd28c8: r0 = "NEW"
    //     0xbd28c8: add             x0, PP, #0x54, lsl #12  ; [pp+0x54348] "NEW"
    //     0xbd28cc: ldr             x0, [x0, #0x348]
    // 0xbd28d0: stur            x1, [fp, #-0x40]
    // 0xbd28d4: StoreField: r1->field_b = r0
    //     0xbd28d4: stur            w0, [x1, #0xb]
    // 0xbd28d8: ldur            x0, [fp, #-0x10]
    // 0xbd28dc: StoreField: r1->field_13 = r0
    //     0xbd28dc: stur            w0, [x1, #0x13]
    // 0xbd28e0: r0 = Padding()
    //     0xbd28e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd28e4: mov             x1, x0
    // 0xbd28e8: r0 = Instance_EdgeInsets
    //     0xbd28e8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38db0] Obj!EdgeInsets@d58101
    //     0xbd28ec: ldr             x0, [x0, #0xdb0]
    // 0xbd28f0: stur            x1, [fp, #-0x10]
    // 0xbd28f4: StoreField: r1->field_f = r0
    //     0xbd28f4: stur            w0, [x1, #0xf]
    // 0xbd28f8: ldur            x2, [fp, #-0x40]
    // 0xbd28fc: StoreField: r1->field_b = r2
    //     0xbd28fc: stur            w2, [x1, #0xb]
    // 0xbd2900: r0 = Container()
    //     0xbd2900: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbd2904: stur            x0, [fp, #-0x40]
    // 0xbd2908: ldur            x16, [fp, #-0x38]
    // 0xbd290c: ldur            lr, [fp, #-0x10]
    // 0xbd2910: stp             lr, x16, [SP]
    // 0xbd2914: mov             x1, x0
    // 0xbd2918: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbd2918: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbd291c: ldr             x4, [x4, #0x88]
    // 0xbd2920: r0 = Container()
    //     0xbd2920: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbd2924: r0 = Padding()
    //     0xbd2924: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd2928: mov             x1, x0
    // 0xbd292c: r0 = Instance_EdgeInsets
    //     0xbd292c: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xbd2930: ldr             x0, [x0, #0xc40]
    // 0xbd2934: stur            x1, [fp, #-0x10]
    // 0xbd2938: StoreField: r1->field_f = r0
    //     0xbd2938: stur            w0, [x1, #0xf]
    // 0xbd293c: ldur            x0, [fp, #-0x40]
    // 0xbd2940: StoreField: r1->field_b = r0
    //     0xbd2940: stur            w0, [x1, #0xb]
    // 0xbd2944: r0 = Visibility()
    //     0xbd2944: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbd2948: mov             x3, x0
    // 0xbd294c: ldur            x0, [fp, #-0x10]
    // 0xbd2950: stur            x3, [fp, #-0x38]
    // 0xbd2954: StoreField: r3->field_b = r0
    //     0xbd2954: stur            w0, [x3, #0xb]
    // 0xbd2958: r0 = Instance_SizedBox
    //     0xbd2958: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbd295c: StoreField: r3->field_f = r0
    //     0xbd295c: stur            w0, [x3, #0xf]
    // 0xbd2960: ldur            x1, [fp, #-8]
    // 0xbd2964: StoreField: r3->field_13 = r1
    //     0xbd2964: stur            w1, [x3, #0x13]
    // 0xbd2968: r4 = false
    //     0xbd2968: add             x4, NULL, #0x30  ; false
    // 0xbd296c: ArrayStore: r3[0] = r4  ; List_4
    //     0xbd296c: stur            w4, [x3, #0x17]
    // 0xbd2970: StoreField: r3->field_1b = r4
    //     0xbd2970: stur            w4, [x3, #0x1b]
    // 0xbd2974: StoreField: r3->field_1f = r4
    //     0xbd2974: stur            w4, [x3, #0x1f]
    // 0xbd2978: StoreField: r3->field_23 = r4
    //     0xbd2978: stur            w4, [x3, #0x23]
    // 0xbd297c: StoreField: r3->field_27 = r4
    //     0xbd297c: stur            w4, [x3, #0x27]
    // 0xbd2980: StoreField: r3->field_2b = r4
    //     0xbd2980: stur            w4, [x3, #0x2b]
    // 0xbd2984: r1 = Null
    //     0xbd2984: mov             x1, NULL
    // 0xbd2988: r2 = 4
    //     0xbd2988: movz            x2, #0x4
    // 0xbd298c: r0 = AllocateArray()
    //     0xbd298c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd2990: mov             x2, x0
    // 0xbd2994: ldur            x0, [fp, #-0x30]
    // 0xbd2998: stur            x2, [fp, #-8]
    // 0xbd299c: StoreField: r2->field_f = r0
    //     0xbd299c: stur            w0, [x2, #0xf]
    // 0xbd29a0: ldur            x0, [fp, #-0x38]
    // 0xbd29a4: StoreField: r2->field_13 = r0
    //     0xbd29a4: stur            w0, [x2, #0x13]
    // 0xbd29a8: r1 = <Widget>
    //     0xbd29a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd29ac: r0 = AllocateGrowableArray()
    //     0xbd29ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd29b0: mov             x1, x0
    // 0xbd29b4: ldur            x0, [fp, #-8]
    // 0xbd29b8: stur            x1, [fp, #-0x10]
    // 0xbd29bc: StoreField: r1->field_f = r0
    //     0xbd29bc: stur            w0, [x1, #0xf]
    // 0xbd29c0: r2 = 4
    //     0xbd29c0: movz            x2, #0x4
    // 0xbd29c4: StoreField: r1->field_b = r2
    //     0xbd29c4: stur            w2, [x1, #0xb]
    // 0xbd29c8: r0 = Row()
    //     0xbd29c8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbd29cc: mov             x2, x0
    // 0xbd29d0: r1 = Instance_Axis
    //     0xbd29d0: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd29d4: stur            x2, [fp, #-0x30]
    // 0xbd29d8: StoreField: r2->field_f = r1
    //     0xbd29d8: stur            w1, [x2, #0xf]
    // 0xbd29dc: r3 = Instance_MainAxisAlignment
    //     0xbd29dc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd29e0: ldr             x3, [x3, #0xa08]
    // 0xbd29e4: StoreField: r2->field_13 = r3
    //     0xbd29e4: stur            w3, [x2, #0x13]
    // 0xbd29e8: r4 = Instance_MainAxisSize
    //     0xbd29e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd29ec: ldr             x4, [x4, #0xa10]
    // 0xbd29f0: ArrayStore: r2[0] = r4  ; List_4
    //     0xbd29f0: stur            w4, [x2, #0x17]
    // 0xbd29f4: r5 = Instance_CrossAxisAlignment
    //     0xbd29f4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd29f8: ldr             x5, [x5, #0xa18]
    // 0xbd29fc: StoreField: r2->field_1b = r5
    //     0xbd29fc: stur            w5, [x2, #0x1b]
    // 0xbd2a00: r6 = Instance_VerticalDirection
    //     0xbd2a00: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd2a04: ldr             x6, [x6, #0xa20]
    // 0xbd2a08: StoreField: r2->field_23 = r6
    //     0xbd2a08: stur            w6, [x2, #0x23]
    // 0xbd2a0c: r7 = Instance_Clip
    //     0xbd2a0c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd2a10: ldr             x7, [x7, #0x38]
    // 0xbd2a14: StoreField: r2->field_2b = r7
    //     0xbd2a14: stur            w7, [x2, #0x2b]
    // 0xbd2a18: StoreField: r2->field_2f = rZR
    //     0xbd2a18: stur            xzr, [x2, #0x2f]
    // 0xbd2a1c: ldur            x0, [fp, #-0x10]
    // 0xbd2a20: StoreField: r2->field_b = r0
    //     0xbd2a20: stur            w0, [x2, #0xb]
    // 0xbd2a24: ldur            x8, [fp, #-0x20]
    // 0xbd2a28: LoadField: r0 = r8->field_13
    //     0xbd2a28: ldur            w0, [x8, #0x13]
    // 0xbd2a2c: DecompressPointer r0
    //     0xbd2a2c: add             x0, x0, HEAP, lsl #32
    // 0xbd2a30: cmp             w0, NULL
    // 0xbd2a34: b.ne            #0xbd2a40
    // 0xbd2a38: r9 = Null
    //     0xbd2a38: mov             x9, NULL
    // 0xbd2a3c: b               #0xbd2a6c
    // 0xbd2a40: LoadField: r9 = r0->field_f
    //     0xbd2a40: ldur            w9, [x0, #0xf]
    // 0xbd2a44: DecompressPointer r9
    //     0xbd2a44: add             x9, x9, HEAP, lsl #32
    // 0xbd2a48: cmp             w9, NULL
    // 0xbd2a4c: b.ne            #0xbd2a58
    // 0xbd2a50: r9 = Null
    //     0xbd2a50: mov             x9, NULL
    // 0xbd2a54: b               #0xbd2a6c
    // 0xbd2a58: LoadField: r10 = r9->field_7
    //     0xbd2a58: ldur            w10, [x9, #7]
    // 0xbd2a5c: cbnz            w10, #0xbd2a68
    // 0xbd2a60: r9 = false
    //     0xbd2a60: add             x9, NULL, #0x30  ; false
    // 0xbd2a64: b               #0xbd2a6c
    // 0xbd2a68: r9 = true
    //     0xbd2a68: add             x9, NULL, #0x20  ; true
    // 0xbd2a6c: cmp             w9, NULL
    // 0xbd2a70: b.ne            #0xbd2a78
    // 0xbd2a74: r9 = false
    //     0xbd2a74: add             x9, NULL, #0x30  ; false
    // 0xbd2a78: stur            x9, [fp, #-8]
    // 0xbd2a7c: cmp             w0, NULL
    // 0xbd2a80: b.ne            #0xbd2a8c
    // 0xbd2a84: r0 = Null
    //     0xbd2a84: mov             x0, NULL
    // 0xbd2a88: b               #0xbd2a98
    // 0xbd2a8c: LoadField: r10 = r0->field_7
    //     0xbd2a8c: ldur            w10, [x0, #7]
    // 0xbd2a90: DecompressPointer r10
    //     0xbd2a90: add             x10, x10, HEAP, lsl #32
    // 0xbd2a94: mov             x0, x10
    // 0xbd2a98: r10 = LoadClassIdInstr(r0)
    //     0xbd2a98: ldur            x10, [x0, #-1]
    //     0xbd2a9c: ubfx            x10, x10, #0xc, #0x14
    // 0xbd2aa0: r16 = "cod"
    //     0xbd2aa0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a28] "cod"
    //     0xbd2aa4: ldr             x16, [x16, #0xa28]
    // 0xbd2aa8: stp             x16, x0, [SP]
    // 0xbd2aac: mov             x0, x10
    // 0xbd2ab0: mov             lr, x0
    // 0xbd2ab4: ldr             lr, [x21, lr, lsl #3]
    // 0xbd2ab8: blr             lr
    // 0xbd2abc: tbnz            w0, #4, #0xbd2aec
    // 0xbd2ac0: ldur            x1, [fp, #-0x18]
    // 0xbd2ac4: r0 = of()
    //     0xbd2ac4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd2ac8: LoadField: r1 = r0->field_5b
    //     0xbd2ac8: ldur            w1, [x0, #0x5b]
    // 0xbd2acc: DecompressPointer r1
    //     0xbd2acc: add             x1, x1, HEAP, lsl #32
    // 0xbd2ad0: r0 = LoadClassIdInstr(r1)
    //     0xbd2ad0: ldur            x0, [x1, #-1]
    //     0xbd2ad4: ubfx            x0, x0, #0xc, #0x14
    // 0xbd2ad8: d0 = 0.100000
    //     0xbd2ad8: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbd2adc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbd2adc: sub             lr, x0, #0xffa
    //     0xbd2ae0: ldr             lr, [x21, lr, lsl #3]
    //     0xbd2ae4: blr             lr
    // 0xbd2ae8: b               #0xbd2af4
    // 0xbd2aec: r0 = Instance_Color
    //     0xbd2aec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbd2af0: ldr             x0, [x0, #0x858]
    // 0xbd2af4: ldur            x2, [fp, #-0x20]
    // 0xbd2af8: stur            x0, [fp, #-0x10]
    // 0xbd2afc: r0 = BoxDecoration()
    //     0xbd2afc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbd2b00: mov             x2, x0
    // 0xbd2b04: ldur            x0, [fp, #-0x10]
    // 0xbd2b08: stur            x2, [fp, #-0x38]
    // 0xbd2b0c: StoreField: r2->field_7 = r0
    //     0xbd2b0c: stur            w0, [x2, #7]
    // 0xbd2b10: r0 = Instance_BorderRadius
    //     0xbd2b10: add             x0, PP, #0x39, lsl #12  ; [pp+0x39798] Obj!BorderRadius@d5a341
    //     0xbd2b14: ldr             x0, [x0, #0x798]
    // 0xbd2b18: StoreField: r2->field_13 = r0
    //     0xbd2b18: stur            w0, [x2, #0x13]
    // 0xbd2b1c: r0 = Instance_BoxShape
    //     0xbd2b1c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbd2b20: ldr             x0, [x0, #0x80]
    // 0xbd2b24: StoreField: r2->field_23 = r0
    //     0xbd2b24: stur            w0, [x2, #0x23]
    // 0xbd2b28: ldur            x3, [fp, #-0x20]
    // 0xbd2b2c: LoadField: r1 = r3->field_13
    //     0xbd2b2c: ldur            w1, [x3, #0x13]
    // 0xbd2b30: DecompressPointer r1
    //     0xbd2b30: add             x1, x1, HEAP, lsl #32
    // 0xbd2b34: cmp             w1, NULL
    // 0xbd2b38: b.ne            #0xbd2b44
    // 0xbd2b3c: r1 = Null
    //     0xbd2b3c: mov             x1, NULL
    // 0xbd2b40: b               #0xbd2b50
    // 0xbd2b44: LoadField: r4 = r1->field_f
    //     0xbd2b44: ldur            w4, [x1, #0xf]
    // 0xbd2b48: DecompressPointer r4
    //     0xbd2b48: add             x4, x4, HEAP, lsl #32
    // 0xbd2b4c: mov             x1, x4
    // 0xbd2b50: cmp             w1, NULL
    // 0xbd2b54: b.ne            #0xbd2b60
    // 0xbd2b58: r4 = ""
    //     0xbd2b58: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd2b5c: b               #0xbd2b64
    // 0xbd2b60: mov             x4, x1
    // 0xbd2b64: ldur            x1, [fp, #-0x18]
    // 0xbd2b68: stur            x4, [fp, #-0x10]
    // 0xbd2b6c: r0 = of()
    //     0xbd2b6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd2b70: LoadField: r1 = r0->field_87
    //     0xbd2b70: ldur            w1, [x0, #0x87]
    // 0xbd2b74: DecompressPointer r1
    //     0xbd2b74: add             x1, x1, HEAP, lsl #32
    // 0xbd2b78: LoadField: r2 = r1->field_7
    //     0xbd2b78: ldur            w2, [x1, #7]
    // 0xbd2b7c: DecompressPointer r2
    //     0xbd2b7c: add             x2, x2, HEAP, lsl #32
    // 0xbd2b80: ldur            x1, [fp, #-0x20]
    // 0xbd2b84: stur            x2, [fp, #-0x40]
    // 0xbd2b88: LoadField: r0 = r1->field_13
    //     0xbd2b88: ldur            w0, [x1, #0x13]
    // 0xbd2b8c: DecompressPointer r0
    //     0xbd2b8c: add             x0, x0, HEAP, lsl #32
    // 0xbd2b90: cmp             w0, NULL
    // 0xbd2b94: b.ne            #0xbd2ba0
    // 0xbd2b98: r0 = Null
    //     0xbd2b98: mov             x0, NULL
    // 0xbd2b9c: b               #0xbd2bac
    // 0xbd2ba0: LoadField: r3 = r0->field_7
    //     0xbd2ba0: ldur            w3, [x0, #7]
    // 0xbd2ba4: DecompressPointer r3
    //     0xbd2ba4: add             x3, x3, HEAP, lsl #32
    // 0xbd2ba8: mov             x0, x3
    // 0xbd2bac: r3 = LoadClassIdInstr(r0)
    //     0xbd2bac: ldur            x3, [x0, #-1]
    //     0xbd2bb0: ubfx            x3, x3, #0xc, #0x14
    // 0xbd2bb4: r16 = "cod"
    //     0xbd2bb4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a28] "cod"
    //     0xbd2bb8: ldr             x16, [x16, #0xa28]
    // 0xbd2bbc: stp             x16, x0, [SP]
    // 0xbd2bc0: mov             x0, x3
    // 0xbd2bc4: mov             lr, x0
    // 0xbd2bc8: ldr             lr, [x21, lr, lsl #3]
    // 0xbd2bcc: blr             lr
    // 0xbd2bd0: tbnz            w0, #4, #0xbd2bec
    // 0xbd2bd4: r1 = Instance_Color
    //     0xbd2bd4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd2bd8: d0 = 0.700000
    //     0xbd2bd8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd2bdc: ldr             d0, [x17, #0xf48]
    // 0xbd2be0: r0 = withOpacity()
    //     0xbd2be0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd2be4: mov             x1, x0
    // 0xbd2be8: b               #0xbd2bf0
    // 0xbd2bec: r1 = Instance_Color
    //     0xbd2bec: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbd2bf0: ldur            x2, [fp, #-0x20]
    // 0xbd2bf4: ldur            x3, [fp, #-0x30]
    // 0xbd2bf8: ldur            x4, [fp, #-8]
    // 0xbd2bfc: ldur            x0, [fp, #-0x10]
    // 0xbd2c00: r16 = 12.000000
    //     0xbd2c00: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbd2c04: ldr             x16, [x16, #0x9e8]
    // 0xbd2c08: stp             x1, x16, [SP]
    // 0xbd2c0c: ldur            x1, [fp, #-0x40]
    // 0xbd2c10: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd2c10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd2c14: ldr             x4, [x4, #0xaa0]
    // 0xbd2c18: r0 = copyWith()
    //     0xbd2c18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd2c1c: stur            x0, [fp, #-0x40]
    // 0xbd2c20: r0 = Text()
    //     0xbd2c20: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd2c24: mov             x1, x0
    // 0xbd2c28: ldur            x0, [fp, #-0x10]
    // 0xbd2c2c: stur            x1, [fp, #-0x48]
    // 0xbd2c30: StoreField: r1->field_b = r0
    //     0xbd2c30: stur            w0, [x1, #0xb]
    // 0xbd2c34: ldur            x0, [fp, #-0x40]
    // 0xbd2c38: StoreField: r1->field_13 = r0
    //     0xbd2c38: stur            w0, [x1, #0x13]
    // 0xbd2c3c: r0 = Padding()
    //     0xbd2c3c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd2c40: mov             x1, x0
    // 0xbd2c44: r0 = Instance_EdgeInsets
    //     0xbd2c44: add             x0, PP, #0x38, lsl #12  ; [pp+0x38db0] Obj!EdgeInsets@d58101
    //     0xbd2c48: ldr             x0, [x0, #0xdb0]
    // 0xbd2c4c: stur            x1, [fp, #-0x10]
    // 0xbd2c50: StoreField: r1->field_f = r0
    //     0xbd2c50: stur            w0, [x1, #0xf]
    // 0xbd2c54: ldur            x0, [fp, #-0x48]
    // 0xbd2c58: StoreField: r1->field_b = r0
    //     0xbd2c58: stur            w0, [x1, #0xb]
    // 0xbd2c5c: r0 = Container()
    //     0xbd2c5c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbd2c60: stur            x0, [fp, #-0x40]
    // 0xbd2c64: ldur            x16, [fp, #-0x38]
    // 0xbd2c68: ldur            lr, [fp, #-0x10]
    // 0xbd2c6c: stp             lr, x16, [SP]
    // 0xbd2c70: mov             x1, x0
    // 0xbd2c74: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbd2c74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbd2c78: ldr             x4, [x4, #0x88]
    // 0xbd2c7c: r0 = Container()
    //     0xbd2c7c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbd2c80: r0 = Visibility()
    //     0xbd2c80: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbd2c84: mov             x3, x0
    // 0xbd2c88: ldur            x0, [fp, #-0x40]
    // 0xbd2c8c: stur            x3, [fp, #-0x10]
    // 0xbd2c90: StoreField: r3->field_b = r0
    //     0xbd2c90: stur            w0, [x3, #0xb]
    // 0xbd2c94: r0 = Instance_SizedBox
    //     0xbd2c94: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbd2c98: StoreField: r3->field_f = r0
    //     0xbd2c98: stur            w0, [x3, #0xf]
    // 0xbd2c9c: ldur            x1, [fp, #-8]
    // 0xbd2ca0: StoreField: r3->field_13 = r1
    //     0xbd2ca0: stur            w1, [x3, #0x13]
    // 0xbd2ca4: r4 = false
    //     0xbd2ca4: add             x4, NULL, #0x30  ; false
    // 0xbd2ca8: ArrayStore: r3[0] = r4  ; List_4
    //     0xbd2ca8: stur            w4, [x3, #0x17]
    // 0xbd2cac: StoreField: r3->field_1b = r4
    //     0xbd2cac: stur            w4, [x3, #0x1b]
    // 0xbd2cb0: StoreField: r3->field_1f = r4
    //     0xbd2cb0: stur            w4, [x3, #0x1f]
    // 0xbd2cb4: StoreField: r3->field_23 = r4
    //     0xbd2cb4: stur            w4, [x3, #0x23]
    // 0xbd2cb8: StoreField: r3->field_27 = r4
    //     0xbd2cb8: stur            w4, [x3, #0x27]
    // 0xbd2cbc: StoreField: r3->field_2b = r4
    //     0xbd2cbc: stur            w4, [x3, #0x2b]
    // 0xbd2cc0: r1 = Null
    //     0xbd2cc0: mov             x1, NULL
    // 0xbd2cc4: r2 = 4
    //     0xbd2cc4: movz            x2, #0x4
    // 0xbd2cc8: r0 = AllocateArray()
    //     0xbd2cc8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd2ccc: mov             x2, x0
    // 0xbd2cd0: ldur            x0, [fp, #-0x30]
    // 0xbd2cd4: stur            x2, [fp, #-8]
    // 0xbd2cd8: StoreField: r2->field_f = r0
    //     0xbd2cd8: stur            w0, [x2, #0xf]
    // 0xbd2cdc: ldur            x0, [fp, #-0x10]
    // 0xbd2ce0: StoreField: r2->field_13 = r0
    //     0xbd2ce0: stur            w0, [x2, #0x13]
    // 0xbd2ce4: r1 = <Widget>
    //     0xbd2ce4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd2ce8: r0 = AllocateGrowableArray()
    //     0xbd2ce8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd2cec: mov             x1, x0
    // 0xbd2cf0: ldur            x0, [fp, #-8]
    // 0xbd2cf4: stur            x1, [fp, #-0x10]
    // 0xbd2cf8: StoreField: r1->field_f = r0
    //     0xbd2cf8: stur            w0, [x1, #0xf]
    // 0xbd2cfc: r0 = 4
    //     0xbd2cfc: movz            x0, #0x4
    // 0xbd2d00: StoreField: r1->field_b = r0
    //     0xbd2d00: stur            w0, [x1, #0xb]
    // 0xbd2d04: r0 = Row()
    //     0xbd2d04: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbd2d08: mov             x2, x0
    // 0xbd2d0c: r0 = Instance_Axis
    //     0xbd2d0c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd2d10: stur            x2, [fp, #-0x30]
    // 0xbd2d14: StoreField: r2->field_f = r0
    //     0xbd2d14: stur            w0, [x2, #0xf]
    // 0xbd2d18: r0 = Instance_MainAxisAlignment
    //     0xbd2d18: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbd2d1c: ldr             x0, [x0, #0xa8]
    // 0xbd2d20: StoreField: r2->field_13 = r0
    //     0xbd2d20: stur            w0, [x2, #0x13]
    // 0xbd2d24: r0 = Instance_MainAxisSize
    //     0xbd2d24: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd2d28: ldr             x0, [x0, #0xa10]
    // 0xbd2d2c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd2d2c: stur            w0, [x2, #0x17]
    // 0xbd2d30: r1 = Instance_CrossAxisAlignment
    //     0xbd2d30: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd2d34: ldr             x1, [x1, #0xa18]
    // 0xbd2d38: StoreField: r2->field_1b = r1
    //     0xbd2d38: stur            w1, [x2, #0x1b]
    // 0xbd2d3c: r3 = Instance_VerticalDirection
    //     0xbd2d3c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd2d40: ldr             x3, [x3, #0xa20]
    // 0xbd2d44: StoreField: r2->field_23 = r3
    //     0xbd2d44: stur            w3, [x2, #0x23]
    // 0xbd2d48: r4 = Instance_Clip
    //     0xbd2d48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd2d4c: ldr             x4, [x4, #0x38]
    // 0xbd2d50: StoreField: r2->field_2b = r4
    //     0xbd2d50: stur            w4, [x2, #0x2b]
    // 0xbd2d54: StoreField: r2->field_2f = rZR
    //     0xbd2d54: stur            xzr, [x2, #0x2f]
    // 0xbd2d58: ldur            x1, [fp, #-0x10]
    // 0xbd2d5c: StoreField: r2->field_b = r1
    //     0xbd2d5c: stur            w1, [x2, #0xb]
    // 0xbd2d60: ldur            x5, [fp, #-0x20]
    // 0xbd2d64: LoadField: r1 = r5->field_13
    //     0xbd2d64: ldur            w1, [x5, #0x13]
    // 0xbd2d68: DecompressPointer r1
    //     0xbd2d68: add             x1, x1, HEAP, lsl #32
    // 0xbd2d6c: cmp             w1, NULL
    // 0xbd2d70: b.ne            #0xbd2d7c
    // 0xbd2d74: r6 = Null
    //     0xbd2d74: mov             x6, NULL
    // 0xbd2d78: b               #0xbd2da8
    // 0xbd2d7c: LoadField: r6 = r1->field_13
    //     0xbd2d7c: ldur            w6, [x1, #0x13]
    // 0xbd2d80: DecompressPointer r6
    //     0xbd2d80: add             x6, x6, HEAP, lsl #32
    // 0xbd2d84: cmp             w6, NULL
    // 0xbd2d88: b.ne            #0xbd2d94
    // 0xbd2d8c: r6 = Null
    //     0xbd2d8c: mov             x6, NULL
    // 0xbd2d90: b               #0xbd2da8
    // 0xbd2d94: LoadField: r7 = r6->field_7
    //     0xbd2d94: ldur            w7, [x6, #7]
    // 0xbd2d98: cbnz            w7, #0xbd2da4
    // 0xbd2d9c: r6 = false
    //     0xbd2d9c: add             x6, NULL, #0x30  ; false
    // 0xbd2da0: b               #0xbd2da8
    // 0xbd2da4: r6 = true
    //     0xbd2da4: add             x6, NULL, #0x20  ; true
    // 0xbd2da8: cmp             w6, NULL
    // 0xbd2dac: b.ne            #0xbd2db4
    // 0xbd2db0: r6 = false
    //     0xbd2db0: add             x6, NULL, #0x30  ; false
    // 0xbd2db4: stur            x6, [fp, #-0x10]
    // 0xbd2db8: cmp             w1, NULL
    // 0xbd2dbc: b.ne            #0xbd2dc8
    // 0xbd2dc0: r1 = Null
    //     0xbd2dc0: mov             x1, NULL
    // 0xbd2dc4: b               #0xbd2dd4
    // 0xbd2dc8: LoadField: r7 = r1->field_13
    //     0xbd2dc8: ldur            w7, [x1, #0x13]
    // 0xbd2dcc: DecompressPointer r7
    //     0xbd2dcc: add             x7, x7, HEAP, lsl #32
    // 0xbd2dd0: mov             x1, x7
    // 0xbd2dd4: cmp             w1, NULL
    // 0xbd2dd8: b.ne            #0xbd2de4
    // 0xbd2ddc: r7 = ""
    //     0xbd2ddc: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd2de0: b               #0xbd2de8
    // 0xbd2de4: mov             x7, x1
    // 0xbd2de8: ldur            x1, [fp, #-0x18]
    // 0xbd2dec: stur            x7, [fp, #-8]
    // 0xbd2df0: r0 = of()
    //     0xbd2df0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd2df4: LoadField: r1 = r0->field_87
    //     0xbd2df4: ldur            w1, [x0, #0x87]
    // 0xbd2df8: DecompressPointer r1
    //     0xbd2df8: add             x1, x1, HEAP, lsl #32
    // 0xbd2dfc: LoadField: r0 = r1->field_2b
    //     0xbd2dfc: ldur            w0, [x1, #0x2b]
    // 0xbd2e00: DecompressPointer r0
    //     0xbd2e00: add             x0, x0, HEAP, lsl #32
    // 0xbd2e04: stur            x0, [fp, #-0x38]
    // 0xbd2e08: r1 = Instance_Color
    //     0xbd2e08: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd2e0c: d0 = 0.400000
    //     0xbd2e0c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbd2e10: r0 = withOpacity()
    //     0xbd2e10: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd2e14: r16 = 12.000000
    //     0xbd2e14: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbd2e18: ldr             x16, [x16, #0x9e8]
    // 0xbd2e1c: stp             x16, x0, [SP]
    // 0xbd2e20: ldur            x1, [fp, #-0x38]
    // 0xbd2e24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbd2e24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbd2e28: ldr             x4, [x4, #0x9b8]
    // 0xbd2e2c: r0 = copyWith()
    //     0xbd2e2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd2e30: stur            x0, [fp, #-0x38]
    // 0xbd2e34: r0 = TextSpan()
    //     0xbd2e34: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbd2e38: mov             x1, x0
    // 0xbd2e3c: ldur            x0, [fp, #-8]
    // 0xbd2e40: stur            x1, [fp, #-0x40]
    // 0xbd2e44: StoreField: r1->field_b = r0
    //     0xbd2e44: stur            w0, [x1, #0xb]
    // 0xbd2e48: r0 = Instance__DeferringMouseCursor
    //     0xbd2e48: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbd2e4c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbd2e4c: stur            w0, [x1, #0x17]
    // 0xbd2e50: ldur            x2, [fp, #-0x38]
    // 0xbd2e54: StoreField: r1->field_7 = r2
    //     0xbd2e54: stur            w2, [x1, #7]
    // 0xbd2e58: r0 = TapGestureRecognizer()
    //     0xbd2e58: bl              #0x7ce314  ; AllocateTapGestureRecognizerStub -> TapGestureRecognizer (size=0x88)
    // 0xbd2e5c: stur            x0, [fp, #-8]
    // 0xbd2e60: r16 = -1.000000
    //     0xbd2e60: ldr             x16, [PP, #0x5bc0]  ; [pp+0x5bc0] -1
    // 0xbd2e64: stp             x16, NULL, [SP]
    // 0xbd2e68: mov             x1, x0
    // 0xbd2e6c: r4 = const [0, 0x3, 0x2, 0x1, postAcceptSlopTolerance, 0x2, supportedDevices, 0x1, null]
    //     0xbd2e6c: add             x4, PP, #0x47, lsl #12  ; [pp+0x47c80] List(9) [0, 0x3, 0x2, 0x1, "postAcceptSlopTolerance", 0x2, "supportedDevices", 0x1, Null]
    //     0xbd2e70: ldr             x4, [x4, #0xc80]
    // 0xbd2e74: r0 = BaseTapGestureRecognizer()
    //     0xbd2e74: bl              #0x7ce238  ; [package:flutter/src/gestures/tap.dart] BaseTapGestureRecognizer::BaseTapGestureRecognizer
    // 0xbd2e78: ldur            x2, [fp, #-0x20]
    // 0xbd2e7c: r1 = Function '<anonymous closure>':.
    //     0xbd2e7c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54350] AnonymousClosure: (0xbd34e0), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::paymentMethodCardLineTheme (0xbd2670)
    //     0xbd2e80: ldr             x1, [x1, #0x350]
    // 0xbd2e84: r0 = AllocateClosure()
    //     0xbd2e84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd2e88: ldur            x1, [fp, #-8]
    // 0xbd2e8c: StoreField: r1->field_5f = r0
    //     0xbd2e8c: stur            w0, [x1, #0x5f]
    //     0xbd2e90: ldurb           w16, [x1, #-1]
    //     0xbd2e94: ldurb           w17, [x0, #-1]
    //     0xbd2e98: and             x16, x17, x16, lsr #2
    //     0xbd2e9c: tst             x16, HEAP, lsr #32
    //     0xbd2ea0: b.eq            #0xbd2ea8
    //     0xbd2ea4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xbd2ea8: ldur            x2, [fp, #-0x20]
    // 0xbd2eac: LoadField: r0 = r2->field_13
    //     0xbd2eac: ldur            w0, [x2, #0x13]
    // 0xbd2eb0: DecompressPointer r0
    //     0xbd2eb0: add             x0, x0, HEAP, lsl #32
    // 0xbd2eb4: cmp             w0, NULL
    // 0xbd2eb8: b.ne            #0xbd2ec4
    // 0xbd2ebc: r0 = Null
    //     0xbd2ebc: mov             x0, NULL
    // 0xbd2ec0: b               #0xbd2ed0
    // 0xbd2ec4: LoadField: r3 = r0->field_7
    //     0xbd2ec4: ldur            w3, [x0, #7]
    // 0xbd2ec8: DecompressPointer r3
    //     0xbd2ec8: add             x3, x3, HEAP, lsl #32
    // 0xbd2ecc: mov             x0, x3
    // 0xbd2ed0: r3 = LoadClassIdInstr(r0)
    //     0xbd2ed0: ldur            x3, [x0, #-1]
    //     0xbd2ed4: ubfx            x3, x3, #0xc, #0x14
    // 0xbd2ed8: r16 = "partial-cod"
    //     0xbd2ed8: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c830] "partial-cod"
    //     0xbd2edc: ldr             x16, [x16, #0x830]
    // 0xbd2ee0: stp             x16, x0, [SP]
    // 0xbd2ee4: mov             x0, x3
    // 0xbd2ee8: mov             lr, x0
    // 0xbd2eec: ldr             lr, [x21, lr, lsl #3]
    // 0xbd2ef0: blr             lr
    // 0xbd2ef4: tbnz            w0, #4, #0xbd2f04
    // 0xbd2ef8: r5 = "Know More"
    //     0xbd2ef8: add             x5, PP, #0x42, lsl #12  ; [pp+0x42f00] "Know More"
    //     0xbd2efc: ldr             x5, [x5, #0xf00]
    // 0xbd2f00: b               #0xbd2f08
    // 0xbd2f04: r5 = ""
    //     0xbd2f04: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd2f08: ldur            x2, [fp, #-0x20]
    // 0xbd2f0c: ldur            x4, [fp, #-0x10]
    // 0xbd2f10: ldur            x3, [fp, #-0x40]
    // 0xbd2f14: ldur            x0, [fp, #-8]
    // 0xbd2f18: ldur            x1, [fp, #-0x18]
    // 0xbd2f1c: stur            x5, [fp, #-0x38]
    // 0xbd2f20: r0 = of()
    //     0xbd2f20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd2f24: LoadField: r1 = r0->field_87
    //     0xbd2f24: ldur            w1, [x0, #0x87]
    // 0xbd2f28: DecompressPointer r1
    //     0xbd2f28: add             x1, x1, HEAP, lsl #32
    // 0xbd2f2c: LoadField: r0 = r1->field_7
    //     0xbd2f2c: ldur            w0, [x1, #7]
    // 0xbd2f30: DecompressPointer r0
    //     0xbd2f30: add             x0, x0, HEAP, lsl #32
    // 0xbd2f34: ldur            x1, [fp, #-0x18]
    // 0xbd2f38: stur            x0, [fp, #-0x48]
    // 0xbd2f3c: r0 = of()
    //     0xbd2f3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd2f40: LoadField: r1 = r0->field_5b
    //     0xbd2f40: ldur            w1, [x0, #0x5b]
    // 0xbd2f44: DecompressPointer r1
    //     0xbd2f44: add             x1, x1, HEAP, lsl #32
    // 0xbd2f48: r16 = 12.000000
    //     0xbd2f48: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbd2f4c: ldr             x16, [x16, #0x9e8]
    // 0xbd2f50: stp             x1, x16, [SP, #8]
    // 0xbd2f54: r16 = Instance_TextDecoration
    //     0xbd2f54: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xbd2f58: ldr             x16, [x16, #0x10]
    // 0xbd2f5c: str             x16, [SP]
    // 0xbd2f60: ldur            x1, [fp, #-0x48]
    // 0xbd2f64: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xbd2f64: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xbd2f68: ldr             x4, [x4, #0xe38]
    // 0xbd2f6c: r0 = copyWith()
    //     0xbd2f6c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd2f70: stur            x0, [fp, #-0x18]
    // 0xbd2f74: r0 = TextSpan()
    //     0xbd2f74: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbd2f78: mov             x3, x0
    // 0xbd2f7c: ldur            x0, [fp, #-0x38]
    // 0xbd2f80: stur            x3, [fp, #-0x48]
    // 0xbd2f84: StoreField: r3->field_b = r0
    //     0xbd2f84: stur            w0, [x3, #0xb]
    // 0xbd2f88: ldur            x0, [fp, #-8]
    // 0xbd2f8c: StoreField: r3->field_13 = r0
    //     0xbd2f8c: stur            w0, [x3, #0x13]
    // 0xbd2f90: r0 = Instance_SystemMouseCursor
    //     0xbd2f90: add             x0, PP, #0x4b, lsl #12  ; [pp+0x4bfe0] Obj!SystemMouseCursor@d645c1
    //     0xbd2f94: ldr             x0, [x0, #0xfe0]
    // 0xbd2f98: ArrayStore: r3[0] = r0  ; List_4
    //     0xbd2f98: stur            w0, [x3, #0x17]
    // 0xbd2f9c: ldur            x0, [fp, #-0x18]
    // 0xbd2fa0: StoreField: r3->field_7 = r0
    //     0xbd2fa0: stur            w0, [x3, #7]
    // 0xbd2fa4: r1 = Null
    //     0xbd2fa4: mov             x1, NULL
    // 0xbd2fa8: r2 = 6
    //     0xbd2fa8: movz            x2, #0x6
    // 0xbd2fac: r0 = AllocateArray()
    //     0xbd2fac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd2fb0: mov             x2, x0
    // 0xbd2fb4: ldur            x0, [fp, #-0x40]
    // 0xbd2fb8: stur            x2, [fp, #-8]
    // 0xbd2fbc: StoreField: r2->field_f = r0
    //     0xbd2fbc: stur            w0, [x2, #0xf]
    // 0xbd2fc0: r16 = Instance_TextSpan
    //     0xbd2fc0: add             x16, PP, #0x54, lsl #12  ; [pp+0x54358] Obj!TextSpan@d65581
    //     0xbd2fc4: ldr             x16, [x16, #0x358]
    // 0xbd2fc8: StoreField: r2->field_13 = r16
    //     0xbd2fc8: stur            w16, [x2, #0x13]
    // 0xbd2fcc: ldur            x0, [fp, #-0x48]
    // 0xbd2fd0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd2fd0: stur            w0, [x2, #0x17]
    // 0xbd2fd4: r1 = <InlineSpan>
    //     0xbd2fd4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xbd2fd8: ldr             x1, [x1, #0xe40]
    // 0xbd2fdc: r0 = AllocateGrowableArray()
    //     0xbd2fdc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd2fe0: mov             x1, x0
    // 0xbd2fe4: ldur            x0, [fp, #-8]
    // 0xbd2fe8: stur            x1, [fp, #-0x18]
    // 0xbd2fec: StoreField: r1->field_f = r0
    //     0xbd2fec: stur            w0, [x1, #0xf]
    // 0xbd2ff0: r2 = 6
    //     0xbd2ff0: movz            x2, #0x6
    // 0xbd2ff4: StoreField: r1->field_b = r2
    //     0xbd2ff4: stur            w2, [x1, #0xb]
    // 0xbd2ff8: r0 = TextSpan()
    //     0xbd2ff8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbd2ffc: mov             x1, x0
    // 0xbd3000: ldur            x0, [fp, #-0x18]
    // 0xbd3004: stur            x1, [fp, #-8]
    // 0xbd3008: StoreField: r1->field_f = r0
    //     0xbd3008: stur            w0, [x1, #0xf]
    // 0xbd300c: r0 = Instance__DeferringMouseCursor
    //     0xbd300c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbd3010: ArrayStore: r1[0] = r0  ; List_4
    //     0xbd3010: stur            w0, [x1, #0x17]
    // 0xbd3014: r0 = RichText()
    //     0xbd3014: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xbd3018: mov             x1, x0
    // 0xbd301c: ldur            x2, [fp, #-8]
    // 0xbd3020: stur            x0, [fp, #-8]
    // 0xbd3024: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbd3024: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbd3028: r0 = RichText()
    //     0xbd3028: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xbd302c: r0 = Padding()
    //     0xbd302c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd3030: mov             x1, x0
    // 0xbd3034: r0 = Instance_EdgeInsets
    //     0xbd3034: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbd3038: ldr             x0, [x0, #0x770]
    // 0xbd303c: stur            x1, [fp, #-0x18]
    // 0xbd3040: StoreField: r1->field_f = r0
    //     0xbd3040: stur            w0, [x1, #0xf]
    // 0xbd3044: ldur            x0, [fp, #-8]
    // 0xbd3048: StoreField: r1->field_b = r0
    //     0xbd3048: stur            w0, [x1, #0xb]
    // 0xbd304c: r0 = Visibility()
    //     0xbd304c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbd3050: mov             x1, x0
    // 0xbd3054: ldur            x0, [fp, #-0x18]
    // 0xbd3058: stur            x1, [fp, #-8]
    // 0xbd305c: StoreField: r1->field_b = r0
    //     0xbd305c: stur            w0, [x1, #0xb]
    // 0xbd3060: r2 = Instance_SizedBox
    //     0xbd3060: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbd3064: StoreField: r1->field_f = r2
    //     0xbd3064: stur            w2, [x1, #0xf]
    // 0xbd3068: ldur            x0, [fp, #-0x10]
    // 0xbd306c: StoreField: r1->field_13 = r0
    //     0xbd306c: stur            w0, [x1, #0x13]
    // 0xbd3070: r3 = false
    //     0xbd3070: add             x3, NULL, #0x30  ; false
    // 0xbd3074: ArrayStore: r1[0] = r3  ; List_4
    //     0xbd3074: stur            w3, [x1, #0x17]
    // 0xbd3078: StoreField: r1->field_1b = r3
    //     0xbd3078: stur            w3, [x1, #0x1b]
    // 0xbd307c: StoreField: r1->field_1f = r3
    //     0xbd307c: stur            w3, [x1, #0x1f]
    // 0xbd3080: StoreField: r1->field_23 = r3
    //     0xbd3080: stur            w3, [x1, #0x23]
    // 0xbd3084: StoreField: r1->field_27 = r3
    //     0xbd3084: stur            w3, [x1, #0x27]
    // 0xbd3088: StoreField: r1->field_2b = r3
    //     0xbd3088: stur            w3, [x1, #0x2b]
    // 0xbd308c: ldur            x4, [fp, #-0x20]
    // 0xbd3090: LoadField: r0 = r4->field_13
    //     0xbd3090: ldur            w0, [x4, #0x13]
    // 0xbd3094: DecompressPointer r0
    //     0xbd3094: add             x0, x0, HEAP, lsl #32
    // 0xbd3098: cmp             w0, NULL
    // 0xbd309c: b.ne            #0xbd30a8
    // 0xbd30a0: r0 = Null
    //     0xbd30a0: mov             x0, NULL
    // 0xbd30a4: b               #0xbd30b4
    // 0xbd30a8: LoadField: r5 = r0->field_7
    //     0xbd30a8: ldur            w5, [x0, #7]
    // 0xbd30ac: DecompressPointer r5
    //     0xbd30ac: add             x5, x5, HEAP, lsl #32
    // 0xbd30b0: mov             x0, x5
    // 0xbd30b4: ldur            x6, [fp, #-0x28]
    // 0xbd30b8: ldur            x5, [fp, #-0x30]
    // 0xbd30bc: r7 = LoadClassIdInstr(r0)
    //     0xbd30bc: ldur            x7, [x0, #-1]
    //     0xbd30c0: ubfx            x7, x7, #0xc, #0x14
    // 0xbd30c4: r16 = "online"
    //     0xbd30c4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a50] "online"
    //     0xbd30c8: ldr             x16, [x16, #0xa50]
    // 0xbd30cc: stp             x16, x0, [SP]
    // 0xbd30d0: mov             x0, x7
    // 0xbd30d4: mov             lr, x0
    // 0xbd30d8: ldr             lr, [x21, lr, lsl #3]
    // 0xbd30dc: blr             lr
    // 0xbd30e0: stur            x0, [fp, #-0x10]
    // 0xbd30e4: r0 = Image()
    //     0xbd30e4: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0xbd30e8: stur            x0, [fp, #-0x18]
    // 0xbd30ec: r16 = 20.000000
    //     0xbd30ec: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xbd30f0: ldr             x16, [x16, #0xac8]
    // 0xbd30f4: r30 = 20.000000
    //     0xbd30f4: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xbd30f8: ldr             lr, [lr, #0xac8]
    // 0xbd30fc: stp             lr, x16, [SP]
    // 0xbd3100: mov             x1, x0
    // 0xbd3104: r2 = "assets/images/payment_gif.gif"
    //     0xbd3104: add             x2, PP, #0x54, lsl #12  ; [pp+0x54360] "assets/images/payment_gif.gif"
    //     0xbd3108: ldr             x2, [x2, #0x360]
    // 0xbd310c: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0xbd310c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0xbd3110: ldr             x4, [x4, #0x900]
    // 0xbd3114: r0 = Image.asset()
    //     0xbd3114: bl              #0xa20f60  ; [package:flutter/src/widgets/image.dart] Image::Image.asset
    // 0xbd3118: r0 = Padding()
    //     0xbd3118: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd311c: mov             x1, x0
    // 0xbd3120: r0 = Instance_EdgeInsets
    //     0xbd3120: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xbd3124: ldr             x0, [x0, #0x990]
    // 0xbd3128: stur            x1, [fp, #-0x38]
    // 0xbd312c: StoreField: r1->field_f = r0
    //     0xbd312c: stur            w0, [x1, #0xf]
    // 0xbd3130: ldur            x0, [fp, #-0x18]
    // 0xbd3134: StoreField: r1->field_b = r0
    //     0xbd3134: stur            w0, [x1, #0xb]
    // 0xbd3138: r0 = Visibility()
    //     0xbd3138: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbd313c: mov             x3, x0
    // 0xbd3140: ldur            x0, [fp, #-0x38]
    // 0xbd3144: stur            x3, [fp, #-0x18]
    // 0xbd3148: StoreField: r3->field_b = r0
    //     0xbd3148: stur            w0, [x3, #0xb]
    // 0xbd314c: r0 = Instance_SizedBox
    //     0xbd314c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbd3150: StoreField: r3->field_f = r0
    //     0xbd3150: stur            w0, [x3, #0xf]
    // 0xbd3154: ldur            x0, [fp, #-0x10]
    // 0xbd3158: StoreField: r3->field_13 = r0
    //     0xbd3158: stur            w0, [x3, #0x13]
    // 0xbd315c: r0 = false
    //     0xbd315c: add             x0, NULL, #0x30  ; false
    // 0xbd3160: ArrayStore: r3[0] = r0  ; List_4
    //     0xbd3160: stur            w0, [x3, #0x17]
    // 0xbd3164: StoreField: r3->field_1b = r0
    //     0xbd3164: stur            w0, [x3, #0x1b]
    // 0xbd3168: StoreField: r3->field_1f = r0
    //     0xbd3168: stur            w0, [x3, #0x1f]
    // 0xbd316c: StoreField: r3->field_23 = r0
    //     0xbd316c: stur            w0, [x3, #0x23]
    // 0xbd3170: StoreField: r3->field_27 = r0
    //     0xbd3170: stur            w0, [x3, #0x27]
    // 0xbd3174: StoreField: r3->field_2b = r0
    //     0xbd3174: stur            w0, [x3, #0x2b]
    // 0xbd3178: r1 = Null
    //     0xbd3178: mov             x1, NULL
    // 0xbd317c: r2 = 6
    //     0xbd317c: movz            x2, #0x6
    // 0xbd3180: r0 = AllocateArray()
    //     0xbd3180: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd3184: mov             x2, x0
    // 0xbd3188: ldur            x0, [fp, #-0x30]
    // 0xbd318c: stur            x2, [fp, #-0x10]
    // 0xbd3190: StoreField: r2->field_f = r0
    //     0xbd3190: stur            w0, [x2, #0xf]
    // 0xbd3194: ldur            x0, [fp, #-8]
    // 0xbd3198: StoreField: r2->field_13 = r0
    //     0xbd3198: stur            w0, [x2, #0x13]
    // 0xbd319c: ldur            x0, [fp, #-0x18]
    // 0xbd31a0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd31a0: stur            w0, [x2, #0x17]
    // 0xbd31a4: r1 = <Widget>
    //     0xbd31a4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd31a8: r0 = AllocateGrowableArray()
    //     0xbd31a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd31ac: mov             x1, x0
    // 0xbd31b0: ldur            x0, [fp, #-0x10]
    // 0xbd31b4: stur            x1, [fp, #-8]
    // 0xbd31b8: StoreField: r1->field_f = r0
    //     0xbd31b8: stur            w0, [x1, #0xf]
    // 0xbd31bc: r0 = 6
    //     0xbd31bc: movz            x0, #0x6
    // 0xbd31c0: StoreField: r1->field_b = r0
    //     0xbd31c0: stur            w0, [x1, #0xb]
    // 0xbd31c4: r0 = Column()
    //     0xbd31c4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbd31c8: mov             x1, x0
    // 0xbd31cc: r0 = Instance_Axis
    //     0xbd31cc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbd31d0: stur            x1, [fp, #-0x10]
    // 0xbd31d4: StoreField: r1->field_f = r0
    //     0xbd31d4: stur            w0, [x1, #0xf]
    // 0xbd31d8: r0 = Instance_MainAxisAlignment
    //     0xbd31d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd31dc: ldr             x0, [x0, #0xa08]
    // 0xbd31e0: StoreField: r1->field_13 = r0
    //     0xbd31e0: stur            w0, [x1, #0x13]
    // 0xbd31e4: r0 = Instance_MainAxisSize
    //     0xbd31e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd31e8: ldr             x0, [x0, #0xa10]
    // 0xbd31ec: ArrayStore: r1[0] = r0  ; List_4
    //     0xbd31ec: stur            w0, [x1, #0x17]
    // 0xbd31f0: r0 = Instance_CrossAxisAlignment
    //     0xbd31f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbd31f4: ldr             x0, [x0, #0x890]
    // 0xbd31f8: StoreField: r1->field_1b = r0
    //     0xbd31f8: stur            w0, [x1, #0x1b]
    // 0xbd31fc: r0 = Instance_VerticalDirection
    //     0xbd31fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd3200: ldr             x0, [x0, #0xa20]
    // 0xbd3204: StoreField: r1->field_23 = r0
    //     0xbd3204: stur            w0, [x1, #0x23]
    // 0xbd3208: r0 = Instance_Clip
    //     0xbd3208: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd320c: ldr             x0, [x0, #0x38]
    // 0xbd3210: StoreField: r1->field_2b = r0
    //     0xbd3210: stur            w0, [x1, #0x2b]
    // 0xbd3214: StoreField: r1->field_2f = rZR
    //     0xbd3214: stur            xzr, [x1, #0x2f]
    // 0xbd3218: ldur            x0, [fp, #-8]
    // 0xbd321c: StoreField: r1->field_b = r0
    //     0xbd321c: stur            w0, [x1, #0xb]
    // 0xbd3220: r0 = ListTile()
    //     0xbd3220: bl              #0x98bcd8  ; AllocateListTileStub -> ListTile (size=0xa0)
    // 0xbd3224: mov             x1, x0
    // 0xbd3228: ldur            x0, [fp, #-0x28]
    // 0xbd322c: stur            x1, [fp, #-8]
    // 0xbd3230: StoreField: r1->field_b = r0
    //     0xbd3230: stur            w0, [x1, #0xb]
    // 0xbd3234: ldur            x0, [fp, #-0x10]
    // 0xbd3238: StoreField: r1->field_f = r0
    //     0xbd3238: stur            w0, [x1, #0xf]
    // 0xbd323c: r0 = true
    //     0xbd323c: add             x0, NULL, #0x20  ; true
    // 0xbd3240: StoreField: r1->field_4b = r0
    //     0xbd3240: stur            w0, [x1, #0x4b]
    // 0xbd3244: r2 = false
    //     0xbd3244: add             x2, NULL, #0x30  ; false
    // 0xbd3248: StoreField: r1->field_5f = r2
    //     0xbd3248: stur            w2, [x1, #0x5f]
    // 0xbd324c: StoreField: r1->field_73 = r2
    //     0xbd324c: stur            w2, [x1, #0x73]
    // 0xbd3250: r3 = 8.000000
    //     0xbd3250: add             x3, PP, #0x36, lsl #12  ; [pp+0x36608] 8
    //     0xbd3254: ldr             x3, [x3, #0x608]
    // 0xbd3258: StoreField: r1->field_8b = r3
    //     0xbd3258: stur            w3, [x1, #0x8b]
    // 0xbd325c: r3 = Instance_ListTileTitleAlignment
    //     0xbd325c: add             x3, PP, #0x54, lsl #12  ; [pp+0x54368] Obj!ListTileTitleAlignment@d74361
    //     0xbd3260: ldr             x3, [x3, #0x368]
    // 0xbd3264: StoreField: r1->field_93 = r3
    //     0xbd3264: stur            w3, [x1, #0x93]
    // 0xbd3268: StoreField: r1->field_97 = r0
    //     0xbd3268: stur            w0, [x1, #0x97]
    // 0xbd326c: r0 = InkWell()
    //     0xbd326c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbd3270: mov             x3, x0
    // 0xbd3274: ldur            x0, [fp, #-8]
    // 0xbd3278: stur            x3, [fp, #-0x10]
    // 0xbd327c: StoreField: r3->field_b = r0
    //     0xbd327c: stur            w0, [x3, #0xb]
    // 0xbd3280: ldur            x2, [fp, #-0x20]
    // 0xbd3284: r1 = Function '<anonymous closure>':.
    //     0xbd3284: add             x1, PP, #0x54, lsl #12  ; [pp+0x54370] AnonymousClosure: (0xbd32e4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::paymentMethodCardLineTheme (0xbd2670)
    //     0xbd3288: ldr             x1, [x1, #0x370]
    // 0xbd328c: r0 = AllocateClosure()
    //     0xbd328c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd3290: mov             x1, x0
    // 0xbd3294: ldur            x0, [fp, #-0x10]
    // 0xbd3298: StoreField: r0->field_f = r1
    //     0xbd3298: stur            w1, [x0, #0xf]
    // 0xbd329c: r1 = true
    //     0xbd329c: add             x1, NULL, #0x20  ; true
    // 0xbd32a0: StoreField: r0->field_43 = r1
    //     0xbd32a0: stur            w1, [x0, #0x43]
    // 0xbd32a4: r2 = Instance_BoxShape
    //     0xbd32a4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbd32a8: ldr             x2, [x2, #0x80]
    // 0xbd32ac: StoreField: r0->field_47 = r2
    //     0xbd32ac: stur            w2, [x0, #0x47]
    // 0xbd32b0: StoreField: r0->field_6f = r1
    //     0xbd32b0: stur            w1, [x0, #0x6f]
    // 0xbd32b4: r2 = false
    //     0xbd32b4: add             x2, NULL, #0x30  ; false
    // 0xbd32b8: StoreField: r0->field_73 = r2
    //     0xbd32b8: stur            w2, [x0, #0x73]
    // 0xbd32bc: StoreField: r0->field_83 = r1
    //     0xbd32bc: stur            w1, [x0, #0x83]
    // 0xbd32c0: StoreField: r0->field_7b = r2
    //     0xbd32c0: stur            w2, [x0, #0x7b]
    // 0xbd32c4: LeaveFrame
    //     0xbd32c4: mov             SP, fp
    //     0xbd32c8: ldp             fp, lr, [SP], #0x10
    // 0xbd32cc: ret
    //     0xbd32cc: ret             
    // 0xbd32d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd32d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd32d4: b               #0xbd269c
    // 0xbd32d8: r9 = _currentSelectedPaymentMode
    //     0xbd32d8: add             x9, PP, #0x54, lsl #12  ; [pp+0x54378] Field <_PaymentMethodWidgetState@1665258561._currentSelectedPaymentMode@1665258561>: late (offset: 0x14)
    //     0xbd32dc: ldr             x9, [x9, #0x378]
    // 0xbd32e0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbd32e0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbd32e4, size: 0x60
    // 0xbd32e4: EnterFrame
    //     0xbd32e4: stp             fp, lr, [SP, #-0x10]!
    //     0xbd32e8: mov             fp, SP
    // 0xbd32ec: AllocStack(0x8)
    //     0xbd32ec: sub             SP, SP, #8
    // 0xbd32f0: SetupParameters()
    //     0xbd32f0: ldr             x0, [fp, #0x10]
    //     0xbd32f4: ldur            w2, [x0, #0x17]
    //     0xbd32f8: add             x2, x2, HEAP, lsl #32
    // 0xbd32fc: CheckStackOverflow
    //     0xbd32fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd3300: cmp             SP, x16
    //     0xbd3304: b.ls            #0xbd333c
    // 0xbd3308: LoadField: r0 = r2->field_f
    //     0xbd3308: ldur            w0, [x2, #0xf]
    // 0xbd330c: DecompressPointer r0
    //     0xbd330c: add             x0, x0, HEAP, lsl #32
    // 0xbd3310: stur            x0, [fp, #-8]
    // 0xbd3314: r1 = Function '<anonymous closure>':.
    //     0xbd3314: add             x1, PP, #0x54, lsl #12  ; [pp+0x54380] AnonymousClosure: (0xbd3344), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::paymentMethodCardLineTheme (0xbd2670)
    //     0xbd3318: ldr             x1, [x1, #0x380]
    // 0xbd331c: r0 = AllocateClosure()
    //     0xbd331c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd3320: ldur            x1, [fp, #-8]
    // 0xbd3324: mov             x2, x0
    // 0xbd3328: r0 = setState()
    //     0xbd3328: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbd332c: r0 = Null
    //     0xbd332c: mov             x0, NULL
    // 0xbd3330: LeaveFrame
    //     0xbd3330: mov             SP, fp
    //     0xbd3334: ldp             fp, lr, [SP], #0x10
    // 0xbd3338: ret
    //     0xbd3338: ret             
    // 0xbd333c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd333c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd3340: b               #0xbd3308
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbd3344, size: 0x19c
    // 0xbd3344: EnterFrame
    //     0xbd3344: stp             fp, lr, [SP, #-0x10]!
    //     0xbd3348: mov             fp, SP
    // 0xbd334c: AllocStack(0x28)
    //     0xbd334c: sub             SP, SP, #0x28
    // 0xbd3350: SetupParameters()
    //     0xbd3350: ldr             x0, [fp, #0x10]
    //     0xbd3354: ldur            w1, [x0, #0x17]
    //     0xbd3358: add             x1, x1, HEAP, lsl #32
    //     0xbd335c: stur            x1, [fp, #-8]
    // 0xbd3360: CheckStackOverflow
    //     0xbd3360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd3364: cmp             SP, x16
    //     0xbd3368: b.ls            #0xbd34d0
    // 0xbd336c: LoadField: r2 = r1->field_f
    //     0xbd336c: ldur            w2, [x1, #0xf]
    // 0xbd3370: DecompressPointer r2
    //     0xbd3370: add             x2, x2, HEAP, lsl #32
    // 0xbd3374: LoadField: r3 = r2->field_b
    //     0xbd3374: ldur            w3, [x2, #0xb]
    // 0xbd3378: DecompressPointer r3
    //     0xbd3378: add             x3, x3, HEAP, lsl #32
    // 0xbd337c: cmp             w3, NULL
    // 0xbd3380: b.eq            #0xbd34d8
    // 0xbd3384: LoadField: r4 = r1->field_13
    //     0xbd3384: ldur            w4, [x1, #0x13]
    // 0xbd3388: DecompressPointer r4
    //     0xbd3388: add             x4, x4, HEAP, lsl #32
    // 0xbd338c: cmp             w4, NULL
    // 0xbd3390: b.ne            #0xbd339c
    // 0xbd3394: r0 = Null
    //     0xbd3394: mov             x0, NULL
    // 0xbd3398: b               #0xbd33a4
    // 0xbd339c: LoadField: r0 = r4->field_7
    //     0xbd339c: ldur            w0, [x4, #7]
    // 0xbd33a0: DecompressPointer r0
    //     0xbd33a0: add             x0, x0, HEAP, lsl #32
    // 0xbd33a4: cmp             w0, NULL
    // 0xbd33a8: b.ne            #0xbd33b4
    // 0xbd33ac: r5 = ""
    //     0xbd33ac: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd33b0: b               #0xbd33b8
    // 0xbd33b4: mov             x5, x0
    // 0xbd33b8: mov             x0, x5
    // 0xbd33bc: StoreField: r3->field_1b = r0
    //     0xbd33bc: stur            w0, [x3, #0x1b]
    //     0xbd33c0: ldurb           w16, [x3, #-1]
    //     0xbd33c4: ldurb           w17, [x0, #-1]
    //     0xbd33c8: and             x16, x17, x16, lsr #2
    //     0xbd33cc: tst             x16, HEAP, lsr #32
    //     0xbd33d0: b.eq            #0xbd33d8
    //     0xbd33d4: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xbd33d8: mov             x0, x5
    // 0xbd33dc: StoreField: r2->field_13 = r0
    //     0xbd33dc: stur            w0, [x2, #0x13]
    //     0xbd33e0: ldurb           w16, [x2, #-1]
    //     0xbd33e4: ldurb           w17, [x0, #-1]
    //     0xbd33e8: and             x16, x17, x16, lsr #2
    //     0xbd33ec: tst             x16, HEAP, lsr #32
    //     0xbd33f0: b.eq            #0xbd33f8
    //     0xbd33f4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbd33f8: cmp             w4, NULL
    // 0xbd33fc: b.ne            #0xbd3408
    // 0xbd3400: r0 = Null
    //     0xbd3400: mov             x0, NULL
    // 0xbd3404: b               #0xbd3410
    // 0xbd3408: LoadField: r0 = r4->field_7
    //     0xbd3408: ldur            w0, [x4, #7]
    // 0xbd340c: DecompressPointer r0
    //     0xbd340c: add             x0, x0, HEAP, lsl #32
    // 0xbd3410: LoadField: r2 = r3->field_f
    //     0xbd3410: ldur            w2, [x3, #0xf]
    // 0xbd3414: DecompressPointer r2
    //     0xbd3414: add             x2, x2, HEAP, lsl #32
    // 0xbd3418: stp             x0, x2, [SP, #0x10]
    // 0xbd341c: r16 = true
    //     0xbd341c: add             x16, NULL, #0x20  ; true
    // 0xbd3420: r30 = true
    //     0xbd3420: add             lr, NULL, #0x20  ; true
    // 0xbd3424: stp             lr, x16, [SP]
    // 0xbd3428: r4 = 0
    //     0xbd3428: movz            x4, #0
    // 0xbd342c: ldr             x0, [SP, #0x18]
    // 0xbd3430: r16 = UnlinkedCall_0x613b5c
    //     0xbd3430: add             x16, PP, #0x54, lsl #12  ; [pp+0x54388] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbd3434: add             x16, x16, #0x388
    // 0xbd3438: ldp             x5, lr, [x16]
    // 0xbd343c: blr             lr
    // 0xbd3440: ldur            x0, [fp, #-8]
    // 0xbd3444: LoadField: r1 = r0->field_f
    //     0xbd3444: ldur            w1, [x0, #0xf]
    // 0xbd3448: DecompressPointer r1
    //     0xbd3448: add             x1, x1, HEAP, lsl #32
    // 0xbd344c: LoadField: r2 = r1->field_b
    //     0xbd344c: ldur            w2, [x1, #0xb]
    // 0xbd3450: DecompressPointer r2
    //     0xbd3450: add             x2, x2, HEAP, lsl #32
    // 0xbd3454: cmp             w2, NULL
    // 0xbd3458: b.eq            #0xbd34dc
    // 0xbd345c: LoadField: r1 = r0->field_13
    //     0xbd345c: ldur            w1, [x0, #0x13]
    // 0xbd3460: DecompressPointer r1
    //     0xbd3460: add             x1, x1, HEAP, lsl #32
    // 0xbd3464: cmp             w1, NULL
    // 0xbd3468: b.ne            #0xbd3474
    // 0xbd346c: r0 = Null
    //     0xbd346c: mov             x0, NULL
    // 0xbd3470: b               #0xbd347c
    // 0xbd3474: LoadField: r0 = r1->field_7
    //     0xbd3474: ldur            w0, [x1, #7]
    // 0xbd3478: DecompressPointer r0
    //     0xbd3478: add             x0, x0, HEAP, lsl #32
    // 0xbd347c: cmp             w1, NULL
    // 0xbd3480: b.ne            #0xbd348c
    // 0xbd3484: r1 = Null
    //     0xbd3484: mov             x1, NULL
    // 0xbd3488: b               #0xbd3498
    // 0xbd348c: LoadField: r3 = r1->field_13
    //     0xbd348c: ldur            w3, [x1, #0x13]
    // 0xbd3490: DecompressPointer r3
    //     0xbd3490: add             x3, x3, HEAP, lsl #32
    // 0xbd3494: mov             x1, x3
    // 0xbd3498: LoadField: r3 = r2->field_13
    //     0xbd3498: ldur            w3, [x2, #0x13]
    // 0xbd349c: DecompressPointer r3
    //     0xbd349c: add             x3, x3, HEAP, lsl #32
    // 0xbd34a0: stp             x0, x3, [SP, #8]
    // 0xbd34a4: str             x1, [SP]
    // 0xbd34a8: r4 = 0
    //     0xbd34a8: movz            x4, #0
    // 0xbd34ac: ldr             x0, [SP, #0x10]
    // 0xbd34b0: r16 = UnlinkedCall_0x613b5c
    //     0xbd34b0: add             x16, PP, #0x54, lsl #12  ; [pp+0x54398] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbd34b4: add             x16, x16, #0x398
    // 0xbd34b8: ldp             x5, lr, [x16]
    // 0xbd34bc: blr             lr
    // 0xbd34c0: r0 = Null
    //     0xbd34c0: mov             x0, NULL
    // 0xbd34c4: LeaveFrame
    //     0xbd34c4: mov             SP, fp
    //     0xbd34c8: ldp             fp, lr, [SP], #0x10
    // 0xbd34cc: ret
    //     0xbd34cc: ret             
    // 0xbd34d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd34d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd34d4: b               #0xbd336c
    // 0xbd34d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd34d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd34dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd34dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbd34e0, size: 0xd0
    // 0xbd34e0: EnterFrame
    //     0xbd34e0: stp             fp, lr, [SP, #-0x10]!
    //     0xbd34e4: mov             fp, SP
    // 0xbd34e8: AllocStack(0x18)
    //     0xbd34e8: sub             SP, SP, #0x18
    // 0xbd34ec: SetupParameters()
    //     0xbd34ec: ldr             x0, [fp, #0x10]
    //     0xbd34f0: ldur            w1, [x0, #0x17]
    //     0xbd34f4: add             x1, x1, HEAP, lsl #32
    //     0xbd34f8: stur            x1, [fp, #-8]
    // 0xbd34fc: CheckStackOverflow
    //     0xbd34fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd3500: cmp             SP, x16
    //     0xbd3504: b.ls            #0xbd35a4
    // 0xbd3508: LoadField: r0 = r1->field_13
    //     0xbd3508: ldur            w0, [x1, #0x13]
    // 0xbd350c: DecompressPointer r0
    //     0xbd350c: add             x0, x0, HEAP, lsl #32
    // 0xbd3510: cmp             w0, NULL
    // 0xbd3514: b.ne            #0xbd3520
    // 0xbd3518: r0 = Null
    //     0xbd3518: mov             x0, NULL
    // 0xbd351c: b               #0xbd352c
    // 0xbd3520: LoadField: r2 = r0->field_7
    //     0xbd3520: ldur            w2, [x0, #7]
    // 0xbd3524: DecompressPointer r2
    //     0xbd3524: add             x2, x2, HEAP, lsl #32
    // 0xbd3528: mov             x0, x2
    // 0xbd352c: r2 = LoadClassIdInstr(r0)
    //     0xbd352c: ldur            x2, [x0, #-1]
    //     0xbd3530: ubfx            x2, x2, #0xc, #0x14
    // 0xbd3534: r16 = "partial-cod"
    //     0xbd3534: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c830] "partial-cod"
    //     0xbd3538: ldr             x16, [x16, #0x830]
    // 0xbd353c: stp             x16, x0, [SP]
    // 0xbd3540: mov             x0, x2
    // 0xbd3544: mov             lr, x0
    // 0xbd3548: ldr             lr, [x21, lr, lsl #3]
    // 0xbd354c: blr             lr
    // 0xbd3550: tbnz            w0, #4, #0xbd3594
    // 0xbd3554: ldur            x0, [fp, #-8]
    // 0xbd3558: LoadField: r1 = r0->field_f
    //     0xbd3558: ldur            w1, [x0, #0xf]
    // 0xbd355c: DecompressPointer r1
    //     0xbd355c: add             x1, x1, HEAP, lsl #32
    // 0xbd3560: LoadField: r0 = r1->field_b
    //     0xbd3560: ldur            w0, [x1, #0xb]
    // 0xbd3564: DecompressPointer r0
    //     0xbd3564: add             x0, x0, HEAP, lsl #32
    // 0xbd3568: cmp             w0, NULL
    // 0xbd356c: b.eq            #0xbd35ac
    // 0xbd3570: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbd3570: ldur            w1, [x0, #0x17]
    // 0xbd3574: DecompressPointer r1
    //     0xbd3574: add             x1, x1, HEAP, lsl #32
    // 0xbd3578: str             x1, [SP]
    // 0xbd357c: r4 = 0
    //     0xbd357c: movz            x4, #0
    // 0xbd3580: ldr             x0, [SP]
    // 0xbd3584: r16 = UnlinkedCall_0x613b5c
    //     0xbd3584: add             x16, PP, #0x54, lsl #12  ; [pp+0x543a8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbd3588: add             x16, x16, #0x3a8
    // 0xbd358c: ldp             x5, lr, [x16]
    // 0xbd3590: blr             lr
    // 0xbd3594: r0 = Null
    //     0xbd3594: mov             x0, NULL
    // 0xbd3598: LeaveFrame
    //     0xbd3598: mov             SP, fp
    //     0xbd359c: ldp             fp, lr, [SP], #0x10
    // 0xbd35a0: ret
    //     0xbd35a0: ret             
    // 0xbd35a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd35a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd35a8: b               #0xbd3508
    // 0xbd35ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd35ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4009, size: 0x20, field offset: 0xc
class PaymentMethodWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc806a0, size: 0x34
    // 0xc806a0: EnterFrame
    //     0xc806a0: stp             fp, lr, [SP, #-0x10]!
    //     0xc806a4: mov             fp, SP
    // 0xc806a8: mov             x0, x1
    // 0xc806ac: r1 = <PaymentMethodWidget>
    //     0xc806ac: add             x1, PP, #0x48, lsl #12  ; [pp+0x48680] TypeArguments: <PaymentMethodWidget>
    //     0xc806b0: ldr             x1, [x1, #0x680]
    // 0xc806b4: r0 = _PaymentMethodWidgetState()
    //     0xc806b4: bl              #0xc806d4  ; Allocate_PaymentMethodWidgetStateStub -> _PaymentMethodWidgetState (size=0x1c)
    // 0xc806b8: r1 = Sentinel
    //     0xc806b8: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc806bc: StoreField: r0->field_13 = r1
    //     0xc806bc: stur            w1, [x0, #0x13]
    // 0xc806c0: r1 = false
    //     0xc806c0: add             x1, NULL, #0x30  ; false
    // 0xc806c4: ArrayStore: r0[0] = r1  ; List_4
    //     0xc806c4: stur            w1, [x0, #0x17]
    // 0xc806c8: LeaveFrame
    //     0xc806c8: mov             SP, fp
    //     0xc806cc: ldp             fp, lr, [SP], #0x10
    // 0xc806d0: ret
    //     0xc806d0: ret             
  }
}
