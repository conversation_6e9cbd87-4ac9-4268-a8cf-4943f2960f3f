// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/group_carousel_item_view.dart

// class id: 1049309, size: 0x8
class :: {
}

// class id: 3409, size: 0x24, field offset: 0x14
class _GroupCarouselItemViewState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14
  late PageController _imagePageController; // offset: 0x18

  _ build(/* No info */) {
    // ** addr: 0xb00118, size: 0x1414
    // 0xb00118: EnterFrame
    //     0xb00118: stp             fp, lr, [SP, #-0x10]!
    //     0xb0011c: mov             fp, SP
    // 0xb00120: AllocStack(0xa8)
    //     0xb00120: sub             SP, SP, #0xa8
    // 0xb00124: SetupParameters(_GroupCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb00124: mov             x0, x1
    //     0xb00128: stur            x1, [fp, #-8]
    //     0xb0012c: mov             x1, x2
    //     0xb00130: stur            x2, [fp, #-0x10]
    // 0xb00134: CheckStackOverflow
    //     0xb00134: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb00138: cmp             SP, x16
    //     0xb0013c: b.ls            #0xb014c8
    // 0xb00140: r1 = 1
    //     0xb00140: movz            x1, #0x1
    // 0xb00144: r0 = AllocateContext()
    //     0xb00144: bl              #0x16f6108  ; AllocateContextStub
    // 0xb00148: mov             x3, x0
    // 0xb0014c: ldur            x0, [fp, #-8]
    // 0xb00150: stur            x3, [fp, #-0x20]
    // 0xb00154: StoreField: r3->field_f = r0
    //     0xb00154: stur            w0, [x3, #0xf]
    // 0xb00158: LoadField: r1 = r0->field_b
    //     0xb00158: ldur            w1, [x0, #0xb]
    // 0xb0015c: DecompressPointer r1
    //     0xb0015c: add             x1, x1, HEAP, lsl #32
    // 0xb00160: cmp             w1, NULL
    // 0xb00164: b.eq            #0xb014d0
    // 0xb00168: LoadField: r2 = r1->field_1b
    //     0xb00168: ldur            w2, [x1, #0x1b]
    // 0xb0016c: DecompressPointer r2
    //     0xb0016c: add             x2, x2, HEAP, lsl #32
    // 0xb00170: LoadField: r1 = r2->field_7
    //     0xb00170: ldur            w1, [x2, #7]
    // 0xb00174: DecompressPointer r1
    //     0xb00174: add             x1, x1, HEAP, lsl #32
    // 0xb00178: cmp             w1, NULL
    // 0xb0017c: b.ne            #0xb00188
    // 0xb00180: r1 = Instance_TitleAlignment
    //     0xb00180: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb00184: ldr             x1, [x1, #0x518]
    // 0xb00188: r16 = Instance_TitleAlignment
    //     0xb00188: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb0018c: ldr             x16, [x16, #0x520]
    // 0xb00190: cmp             w1, w16
    // 0xb00194: b.ne            #0xb001a4
    // 0xb00198: r4 = Instance_CrossAxisAlignment
    //     0xb00198: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb0019c: ldr             x4, [x4, #0xc68]
    // 0xb001a0: b               #0xb001c8
    // 0xb001a4: r16 = Instance_TitleAlignment
    //     0xb001a4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb001a8: ldr             x16, [x16, #0x518]
    // 0xb001ac: cmp             w1, w16
    // 0xb001b0: b.ne            #0xb001c0
    // 0xb001b4: r4 = Instance_CrossAxisAlignment
    //     0xb001b4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb001b8: ldr             x4, [x4, #0x890]
    // 0xb001bc: b               #0xb001c8
    // 0xb001c0: r4 = Instance_CrossAxisAlignment
    //     0xb001c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb001c4: ldr             x4, [x4, #0xa18]
    // 0xb001c8: stur            x4, [fp, #-0x18]
    // 0xb001cc: r1 = <Widget>
    //     0xb001cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb001d0: r2 = 0
    //     0xb001d0: movz            x2, #0
    // 0xb001d4: r0 = _GrowableList()
    //     0xb001d4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb001d8: mov             x2, x0
    // 0xb001dc: ldur            x1, [fp, #-8]
    // 0xb001e0: stur            x2, [fp, #-0x28]
    // 0xb001e4: LoadField: r0 = r1->field_b
    //     0xb001e4: ldur            w0, [x1, #0xb]
    // 0xb001e8: DecompressPointer r0
    //     0xb001e8: add             x0, x0, HEAP, lsl #32
    // 0xb001ec: cmp             w0, NULL
    // 0xb001f0: b.eq            #0xb014d4
    // 0xb001f4: LoadField: r3 = r0->field_f
    //     0xb001f4: ldur            w3, [x0, #0xf]
    // 0xb001f8: DecompressPointer r3
    //     0xb001f8: add             x3, x3, HEAP, lsl #32
    // 0xb001fc: cmp             w3, NULL
    // 0xb00200: b.ne            #0xb0020c
    // 0xb00204: r0 = Null
    //     0xb00204: mov             x0, NULL
    // 0xb00208: b               #0xb00224
    // 0xb0020c: LoadField: r0 = r3->field_7
    //     0xb0020c: ldur            w0, [x3, #7]
    // 0xb00210: cbnz            w0, #0xb0021c
    // 0xb00214: r4 = false
    //     0xb00214: add             x4, NULL, #0x30  ; false
    // 0xb00218: b               #0xb00220
    // 0xb0021c: r4 = true
    //     0xb0021c: add             x4, NULL, #0x20  ; true
    // 0xb00220: mov             x0, x4
    // 0xb00224: cmp             w0, NULL
    // 0xb00228: b.eq            #0xb0047c
    // 0xb0022c: tbnz            w0, #4, #0xb0047c
    // 0xb00230: cmp             w3, NULL
    // 0xb00234: b.ne            #0xb00240
    // 0xb00238: r0 = Null
    //     0xb00238: mov             x0, NULL
    // 0xb0023c: b               #0xb00258
    // 0xb00240: r0 = LoadClassIdInstr(r3)
    //     0xb00240: ldur            x0, [x3, #-1]
    //     0xb00244: ubfx            x0, x0, #0xc, #0x14
    // 0xb00248: str             x3, [SP]
    // 0xb0024c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb0024c: sub             lr, x0, #1, lsl #12
    //     0xb00250: ldr             lr, [x21, lr, lsl #3]
    //     0xb00254: blr             lr
    // 0xb00258: cmp             w0, NULL
    // 0xb0025c: b.ne            #0xb00268
    // 0xb00260: r2 = ""
    //     0xb00260: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb00264: b               #0xb0026c
    // 0xb00268: mov             x2, x0
    // 0xb0026c: ldur            x0, [fp, #-8]
    // 0xb00270: stur            x2, [fp, #-0x38]
    // 0xb00274: LoadField: r1 = r0->field_b
    //     0xb00274: ldur            w1, [x0, #0xb]
    // 0xb00278: DecompressPointer r1
    //     0xb00278: add             x1, x1, HEAP, lsl #32
    // 0xb0027c: cmp             w1, NULL
    // 0xb00280: b.eq            #0xb014d8
    // 0xb00284: LoadField: r3 = r1->field_1b
    //     0xb00284: ldur            w3, [x1, #0x1b]
    // 0xb00288: DecompressPointer r3
    //     0xb00288: add             x3, x3, HEAP, lsl #32
    // 0xb0028c: LoadField: r1 = r3->field_7
    //     0xb0028c: ldur            w1, [x3, #7]
    // 0xb00290: DecompressPointer r1
    //     0xb00290: add             x1, x1, HEAP, lsl #32
    // 0xb00294: cmp             w1, NULL
    // 0xb00298: b.ne            #0xb002a4
    // 0xb0029c: r1 = Instance_TitleAlignment
    //     0xb0029c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb002a0: ldr             x1, [x1, #0x518]
    // 0xb002a4: r16 = Instance_TitleAlignment
    //     0xb002a4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb002a8: ldr             x16, [x16, #0x520]
    // 0xb002ac: cmp             w1, w16
    // 0xb002b0: b.ne            #0xb002bc
    // 0xb002b4: r4 = Instance_TextAlign
    //     0xb002b4: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xb002b8: b               #0xb002d8
    // 0xb002bc: r16 = Instance_TitleAlignment
    //     0xb002bc: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb002c0: ldr             x16, [x16, #0x518]
    // 0xb002c4: cmp             w1, w16
    // 0xb002c8: b.ne            #0xb002d4
    // 0xb002cc: r4 = Instance_TextAlign
    //     0xb002cc: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb002d0: b               #0xb002d8
    // 0xb002d4: r4 = Instance_TextAlign
    //     0xb002d4: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb002d8: ldur            x3, [fp, #-0x28]
    // 0xb002dc: ldur            x1, [fp, #-0x10]
    // 0xb002e0: stur            x4, [fp, #-0x30]
    // 0xb002e4: r0 = of()
    //     0xb002e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb002e8: LoadField: r1 = r0->field_87
    //     0xb002e8: ldur            w1, [x0, #0x87]
    // 0xb002ec: DecompressPointer r1
    //     0xb002ec: add             x1, x1, HEAP, lsl #32
    // 0xb002f0: LoadField: r0 = r1->field_23
    //     0xb002f0: ldur            w0, [x1, #0x23]
    // 0xb002f4: DecompressPointer r0
    //     0xb002f4: add             x0, x0, HEAP, lsl #32
    // 0xb002f8: r16 = Instance_Color
    //     0xb002f8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb002fc: r30 = 32.000000
    //     0xb002fc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb00300: ldr             lr, [lr, #0x848]
    // 0xb00304: stp             lr, x16, [SP]
    // 0xb00308: mov             x1, x0
    // 0xb0030c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb0030c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb00310: ldr             x4, [x4, #0x9b8]
    // 0xb00314: r0 = copyWith()
    //     0xb00314: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb00318: stur            x0, [fp, #-0x40]
    // 0xb0031c: r0 = Text()
    //     0xb0031c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb00320: mov             x3, x0
    // 0xb00324: ldur            x0, [fp, #-0x38]
    // 0xb00328: stur            x3, [fp, #-0x48]
    // 0xb0032c: StoreField: r3->field_b = r0
    //     0xb0032c: stur            w0, [x3, #0xb]
    // 0xb00330: ldur            x0, [fp, #-0x40]
    // 0xb00334: StoreField: r3->field_13 = r0
    //     0xb00334: stur            w0, [x3, #0x13]
    // 0xb00338: ldur            x0, [fp, #-0x30]
    // 0xb0033c: StoreField: r3->field_1b = r0
    //     0xb0033c: stur            w0, [x3, #0x1b]
    // 0xb00340: r1 = Null
    //     0xb00340: mov             x1, NULL
    // 0xb00344: r2 = 4
    //     0xb00344: movz            x2, #0x4
    // 0xb00348: r0 = AllocateArray()
    //     0xb00348: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0034c: mov             x2, x0
    // 0xb00350: ldur            x0, [fp, #-0x48]
    // 0xb00354: stur            x2, [fp, #-0x30]
    // 0xb00358: StoreField: r2->field_f = r0
    //     0xb00358: stur            w0, [x2, #0xf]
    // 0xb0035c: r16 = Instance_SizedBox
    //     0xb0035c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb00360: ldr             x16, [x16, #0xc70]
    // 0xb00364: StoreField: r2->field_13 = r16
    //     0xb00364: stur            w16, [x2, #0x13]
    // 0xb00368: r1 = <Widget>
    //     0xb00368: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0036c: r0 = AllocateGrowableArray()
    //     0xb0036c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb00370: mov             x1, x0
    // 0xb00374: ldur            x0, [fp, #-0x30]
    // 0xb00378: stur            x1, [fp, #-0x38]
    // 0xb0037c: StoreField: r1->field_f = r0
    //     0xb0037c: stur            w0, [x1, #0xf]
    // 0xb00380: r2 = 4
    //     0xb00380: movz            x2, #0x4
    // 0xb00384: StoreField: r1->field_b = r2
    //     0xb00384: stur            w2, [x1, #0xb]
    // 0xb00388: r0 = Column()
    //     0xb00388: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb0038c: mov             x1, x0
    // 0xb00390: r0 = Instance_Axis
    //     0xb00390: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb00394: stur            x1, [fp, #-0x30]
    // 0xb00398: StoreField: r1->field_f = r0
    //     0xb00398: stur            w0, [x1, #0xf]
    // 0xb0039c: r2 = Instance_MainAxisAlignment
    //     0xb0039c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb003a0: ldr             x2, [x2, #0xa08]
    // 0xb003a4: StoreField: r1->field_13 = r2
    //     0xb003a4: stur            w2, [x1, #0x13]
    // 0xb003a8: r3 = Instance_MainAxisSize
    //     0xb003a8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb003ac: ldr             x3, [x3, #0xa10]
    // 0xb003b0: ArrayStore: r1[0] = r3  ; List_4
    //     0xb003b0: stur            w3, [x1, #0x17]
    // 0xb003b4: r4 = Instance_CrossAxisAlignment
    //     0xb003b4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb003b8: ldr             x4, [x4, #0x890]
    // 0xb003bc: StoreField: r1->field_1b = r4
    //     0xb003bc: stur            w4, [x1, #0x1b]
    // 0xb003c0: r4 = Instance_VerticalDirection
    //     0xb003c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb003c4: ldr             x4, [x4, #0xa20]
    // 0xb003c8: StoreField: r1->field_23 = r4
    //     0xb003c8: stur            w4, [x1, #0x23]
    // 0xb003cc: r5 = Instance_Clip
    //     0xb003cc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb003d0: ldr             x5, [x5, #0x38]
    // 0xb003d4: StoreField: r1->field_2b = r5
    //     0xb003d4: stur            w5, [x1, #0x2b]
    // 0xb003d8: StoreField: r1->field_2f = rZR
    //     0xb003d8: stur            xzr, [x1, #0x2f]
    // 0xb003dc: ldur            x6, [fp, #-0x38]
    // 0xb003e0: StoreField: r1->field_b = r6
    //     0xb003e0: stur            w6, [x1, #0xb]
    // 0xb003e4: r0 = Padding()
    //     0xb003e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb003e8: mov             x2, x0
    // 0xb003ec: r0 = Instance_EdgeInsets
    //     0xb003ec: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xb003f0: ldr             x0, [x0, #0x100]
    // 0xb003f4: stur            x2, [fp, #-0x38]
    // 0xb003f8: StoreField: r2->field_f = r0
    //     0xb003f8: stur            w0, [x2, #0xf]
    // 0xb003fc: ldur            x0, [fp, #-0x30]
    // 0xb00400: StoreField: r2->field_b = r0
    //     0xb00400: stur            w0, [x2, #0xb]
    // 0xb00404: ldur            x0, [fp, #-0x28]
    // 0xb00408: LoadField: r1 = r0->field_b
    //     0xb00408: ldur            w1, [x0, #0xb]
    // 0xb0040c: LoadField: r3 = r0->field_f
    //     0xb0040c: ldur            w3, [x0, #0xf]
    // 0xb00410: DecompressPointer r3
    //     0xb00410: add             x3, x3, HEAP, lsl #32
    // 0xb00414: LoadField: r4 = r3->field_b
    //     0xb00414: ldur            w4, [x3, #0xb]
    // 0xb00418: r3 = LoadInt32Instr(r1)
    //     0xb00418: sbfx            x3, x1, #1, #0x1f
    // 0xb0041c: stur            x3, [fp, #-0x50]
    // 0xb00420: r1 = LoadInt32Instr(r4)
    //     0xb00420: sbfx            x1, x4, #1, #0x1f
    // 0xb00424: cmp             x3, x1
    // 0xb00428: b.ne            #0xb00434
    // 0xb0042c: mov             x1, x0
    // 0xb00430: r0 = _growToNextCapacity()
    //     0xb00430: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb00434: ldur            x2, [fp, #-0x28]
    // 0xb00438: ldur            x3, [fp, #-0x50]
    // 0xb0043c: add             x0, x3, #1
    // 0xb00440: lsl             x1, x0, #1
    // 0xb00444: StoreField: r2->field_b = r1
    //     0xb00444: stur            w1, [x2, #0xb]
    // 0xb00448: LoadField: r1 = r2->field_f
    //     0xb00448: ldur            w1, [x2, #0xf]
    // 0xb0044c: DecompressPointer r1
    //     0xb0044c: add             x1, x1, HEAP, lsl #32
    // 0xb00450: ldur            x0, [fp, #-0x38]
    // 0xb00454: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb00454: add             x25, x1, x3, lsl #2
    //     0xb00458: add             x25, x25, #0xf
    //     0xb0045c: str             w0, [x25]
    //     0xb00460: tbz             w0, #0, #0xb0047c
    //     0xb00464: ldurb           w16, [x1, #-1]
    //     0xb00468: ldurb           w17, [x0, #-1]
    //     0xb0046c: and             x16, x17, x16, lsr #2
    //     0xb00470: tst             x16, HEAP, lsr #32
    //     0xb00474: b.eq            #0xb0047c
    //     0xb00478: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0047c: ldur            x0, [fp, #-8]
    // 0xb00480: LoadField: r1 = r0->field_b
    //     0xb00480: ldur            w1, [x0, #0xb]
    // 0xb00484: DecompressPointer r1
    //     0xb00484: add             x1, x1, HEAP, lsl #32
    // 0xb00488: cmp             w1, NULL
    // 0xb0048c: b.eq            #0xb014dc
    // 0xb00490: LoadField: r3 = r1->field_13
    //     0xb00490: ldur            w3, [x1, #0x13]
    // 0xb00494: DecompressPointer r3
    //     0xb00494: add             x3, x3, HEAP, lsl #32
    // 0xb00498: cmp             w3, NULL
    // 0xb0049c: b.ne            #0xb004a8
    // 0xb004a0: r1 = Null
    //     0xb004a0: mov             x1, NULL
    // 0xb004a4: b               #0xb004d4
    // 0xb004a8: LoadField: r1 = r3->field_7
    //     0xb004a8: ldur            w1, [x3, #7]
    // 0xb004ac: DecompressPointer r1
    //     0xb004ac: add             x1, x1, HEAP, lsl #32
    // 0xb004b0: cmp             w1, NULL
    // 0xb004b4: b.ne            #0xb004c0
    // 0xb004b8: r1 = Null
    //     0xb004b8: mov             x1, NULL
    // 0xb004bc: b               #0xb004d4
    // 0xb004c0: LoadField: r3 = r1->field_7
    //     0xb004c0: ldur            w3, [x1, #7]
    // 0xb004c4: cbnz            w3, #0xb004d0
    // 0xb004c8: r1 = false
    //     0xb004c8: add             x1, NULL, #0x30  ; false
    // 0xb004cc: b               #0xb004d4
    // 0xb004d0: r1 = true
    //     0xb004d0: add             x1, NULL, #0x20  ; true
    // 0xb004d4: cmp             w1, NULL
    // 0xb004d8: b.ne            #0xb004e4
    // 0xb004dc: r3 = false
    //     0xb004dc: add             x3, NULL, #0x30  ; false
    // 0xb004e0: b               #0xb004e8
    // 0xb004e4: mov             x3, x1
    // 0xb004e8: ldur            x1, [fp, #-0x10]
    // 0xb004ec: stur            x3, [fp, #-0x30]
    // 0xb004f0: r0 = of()
    //     0xb004f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb004f4: LoadField: r1 = r0->field_5b
    //     0xb004f4: ldur            w1, [x0, #0x5b]
    // 0xb004f8: DecompressPointer r1
    //     0xb004f8: add             x1, x1, HEAP, lsl #32
    // 0xb004fc: stur            x1, [fp, #-0x38]
    // 0xb00500: r0 = BoxDecoration()
    //     0xb00500: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb00504: mov             x2, x0
    // 0xb00508: ldur            x0, [fp, #-0x38]
    // 0xb0050c: stur            x2, [fp, #-0x40]
    // 0xb00510: StoreField: r2->field_7 = r0
    //     0xb00510: stur            w0, [x2, #7]
    // 0xb00514: r0 = Instance_BorderRadius
    //     0xb00514: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb00518: ldr             x0, [x0, #0x460]
    // 0xb0051c: StoreField: r2->field_13 = r0
    //     0xb0051c: stur            w0, [x2, #0x13]
    // 0xb00520: r0 = Instance_BoxShape
    //     0xb00520: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb00524: ldr             x0, [x0, #0x80]
    // 0xb00528: StoreField: r2->field_23 = r0
    //     0xb00528: stur            w0, [x2, #0x23]
    // 0xb0052c: ldur            x3, [fp, #-8]
    // 0xb00530: LoadField: r1 = r3->field_b
    //     0xb00530: ldur            w1, [x3, #0xb]
    // 0xb00534: DecompressPointer r1
    //     0xb00534: add             x1, x1, HEAP, lsl #32
    // 0xb00538: cmp             w1, NULL
    // 0xb0053c: b.eq            #0xb014e0
    // 0xb00540: LoadField: r4 = r1->field_13
    //     0xb00540: ldur            w4, [x1, #0x13]
    // 0xb00544: DecompressPointer r4
    //     0xb00544: add             x4, x4, HEAP, lsl #32
    // 0xb00548: cmp             w4, NULL
    // 0xb0054c: b.ne            #0xb00558
    // 0xb00550: r1 = Null
    //     0xb00550: mov             x1, NULL
    // 0xb00554: b               #0xb00560
    // 0xb00558: LoadField: r1 = r4->field_7
    //     0xb00558: ldur            w1, [x4, #7]
    // 0xb0055c: DecompressPointer r1
    //     0xb0055c: add             x1, x1, HEAP, lsl #32
    // 0xb00560: cmp             w1, NULL
    // 0xb00564: b.ne            #0xb00570
    // 0xb00568: r6 = ""
    //     0xb00568: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0056c: b               #0xb00574
    // 0xb00570: mov             x6, x1
    // 0xb00574: ldur            x4, [fp, #-0x28]
    // 0xb00578: ldur            x5, [fp, #-0x30]
    // 0xb0057c: ldur            x1, [fp, #-0x10]
    // 0xb00580: stur            x6, [fp, #-0x38]
    // 0xb00584: r0 = of()
    //     0xb00584: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb00588: LoadField: r1 = r0->field_87
    //     0xb00588: ldur            w1, [x0, #0x87]
    // 0xb0058c: DecompressPointer r1
    //     0xb0058c: add             x1, x1, HEAP, lsl #32
    // 0xb00590: LoadField: r0 = r1->field_2b
    //     0xb00590: ldur            w0, [x1, #0x2b]
    // 0xb00594: DecompressPointer r0
    //     0xb00594: add             x0, x0, HEAP, lsl #32
    // 0xb00598: r16 = 16.000000
    //     0xb00598: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb0059c: ldr             x16, [x16, #0x188]
    // 0xb005a0: r30 = Instance_Color
    //     0xb005a0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb005a4: stp             lr, x16, [SP]
    // 0xb005a8: mov             x1, x0
    // 0xb005ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb005ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb005b0: ldr             x4, [x4, #0xaa0]
    // 0xb005b4: r0 = copyWith()
    //     0xb005b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb005b8: stur            x0, [fp, #-0x48]
    // 0xb005bc: r0 = Text()
    //     0xb005bc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb005c0: mov             x1, x0
    // 0xb005c4: ldur            x0, [fp, #-0x38]
    // 0xb005c8: stur            x1, [fp, #-0x58]
    // 0xb005cc: StoreField: r1->field_b = r0
    //     0xb005cc: stur            w0, [x1, #0xb]
    // 0xb005d0: ldur            x0, [fp, #-0x48]
    // 0xb005d4: StoreField: r1->field_13 = r0
    //     0xb005d4: stur            w0, [x1, #0x13]
    // 0xb005d8: r0 = Center()
    //     0xb005d8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb005dc: mov             x1, x0
    // 0xb005e0: r0 = Instance_Alignment
    //     0xb005e0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb005e4: ldr             x0, [x0, #0xb10]
    // 0xb005e8: stur            x1, [fp, #-0x38]
    // 0xb005ec: StoreField: r1->field_f = r0
    //     0xb005ec: stur            w0, [x1, #0xf]
    // 0xb005f0: ldur            x0, [fp, #-0x58]
    // 0xb005f4: StoreField: r1->field_b = r0
    //     0xb005f4: stur            w0, [x1, #0xb]
    // 0xb005f8: r0 = Container()
    //     0xb005f8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb005fc: stur            x0, [fp, #-0x48]
    // 0xb00600: r16 = 40.000000
    //     0xb00600: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb00604: ldr             x16, [x16, #8]
    // 0xb00608: r30 = 110.000000
    //     0xb00608: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xb0060c: ldr             lr, [lr, #0x770]
    // 0xb00610: stp             lr, x16, [SP, #0x10]
    // 0xb00614: ldur            x16, [fp, #-0x40]
    // 0xb00618: ldur            lr, [fp, #-0x38]
    // 0xb0061c: stp             lr, x16, [SP]
    // 0xb00620: mov             x1, x0
    // 0xb00624: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb00624: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb00628: ldr             x4, [x4, #0x8c0]
    // 0xb0062c: r0 = Container()
    //     0xb0062c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb00630: r0 = InkWell()
    //     0xb00630: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb00634: mov             x3, x0
    // 0xb00638: ldur            x0, [fp, #-0x48]
    // 0xb0063c: stur            x3, [fp, #-0x38]
    // 0xb00640: StoreField: r3->field_b = r0
    //     0xb00640: stur            w0, [x3, #0xb]
    // 0xb00644: ldur            x2, [fp, #-0x20]
    // 0xb00648: r1 = Function '<anonymous closure>':.
    //     0xb00648: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e90] AnonymousClosure: (0xb04478), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::build (0xb00118)
    //     0xb0064c: ldr             x1, [x1, #0xe90]
    // 0xb00650: r0 = AllocateClosure()
    //     0xb00650: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb00654: mov             x1, x0
    // 0xb00658: ldur            x0, [fp, #-0x38]
    // 0xb0065c: StoreField: r0->field_f = r1
    //     0xb0065c: stur            w1, [x0, #0xf]
    // 0xb00660: r1 = true
    //     0xb00660: add             x1, NULL, #0x20  ; true
    // 0xb00664: StoreField: r0->field_43 = r1
    //     0xb00664: stur            w1, [x0, #0x43]
    // 0xb00668: r2 = Instance_BoxShape
    //     0xb00668: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb0066c: ldr             x2, [x2, #0x80]
    // 0xb00670: StoreField: r0->field_47 = r2
    //     0xb00670: stur            w2, [x0, #0x47]
    // 0xb00674: StoreField: r0->field_6f = r1
    //     0xb00674: stur            w1, [x0, #0x6f]
    // 0xb00678: r3 = false
    //     0xb00678: add             x3, NULL, #0x30  ; false
    // 0xb0067c: StoreField: r0->field_73 = r3
    //     0xb0067c: stur            w3, [x0, #0x73]
    // 0xb00680: StoreField: r0->field_83 = r1
    //     0xb00680: stur            w1, [x0, #0x83]
    // 0xb00684: StoreField: r0->field_7b = r3
    //     0xb00684: stur            w3, [x0, #0x7b]
    // 0xb00688: r0 = Visibility()
    //     0xb00688: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb0068c: mov             x2, x0
    // 0xb00690: ldur            x0, [fp, #-0x38]
    // 0xb00694: stur            x2, [fp, #-0x40]
    // 0xb00698: StoreField: r2->field_b = r0
    //     0xb00698: stur            w0, [x2, #0xb]
    // 0xb0069c: r0 = Instance_SizedBox
    //     0xb0069c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb006a0: StoreField: r2->field_f = r0
    //     0xb006a0: stur            w0, [x2, #0xf]
    // 0xb006a4: ldur            x1, [fp, #-0x30]
    // 0xb006a8: StoreField: r2->field_13 = r1
    //     0xb006a8: stur            w1, [x2, #0x13]
    // 0xb006ac: r3 = false
    //     0xb006ac: add             x3, NULL, #0x30  ; false
    // 0xb006b0: ArrayStore: r2[0] = r3  ; List_4
    //     0xb006b0: stur            w3, [x2, #0x17]
    // 0xb006b4: StoreField: r2->field_1b = r3
    //     0xb006b4: stur            w3, [x2, #0x1b]
    // 0xb006b8: StoreField: r2->field_1f = r3
    //     0xb006b8: stur            w3, [x2, #0x1f]
    // 0xb006bc: StoreField: r2->field_23 = r3
    //     0xb006bc: stur            w3, [x2, #0x23]
    // 0xb006c0: StoreField: r2->field_27 = r3
    //     0xb006c0: stur            w3, [x2, #0x27]
    // 0xb006c4: StoreField: r2->field_2b = r3
    //     0xb006c4: stur            w3, [x2, #0x2b]
    // 0xb006c8: ldur            x4, [fp, #-0x28]
    // 0xb006cc: LoadField: r1 = r4->field_b
    //     0xb006cc: ldur            w1, [x4, #0xb]
    // 0xb006d0: LoadField: r5 = r4->field_f
    //     0xb006d0: ldur            w5, [x4, #0xf]
    // 0xb006d4: DecompressPointer r5
    //     0xb006d4: add             x5, x5, HEAP, lsl #32
    // 0xb006d8: LoadField: r6 = r5->field_b
    //     0xb006d8: ldur            w6, [x5, #0xb]
    // 0xb006dc: r5 = LoadInt32Instr(r1)
    //     0xb006dc: sbfx            x5, x1, #1, #0x1f
    // 0xb006e0: stur            x5, [fp, #-0x50]
    // 0xb006e4: r1 = LoadInt32Instr(r6)
    //     0xb006e4: sbfx            x1, x6, #1, #0x1f
    // 0xb006e8: cmp             x5, x1
    // 0xb006ec: b.ne            #0xb006f8
    // 0xb006f0: mov             x1, x4
    // 0xb006f4: r0 = _growToNextCapacity()
    //     0xb006f4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb006f8: ldur            x2, [fp, #-0x28]
    // 0xb006fc: ldur            x3, [fp, #-0x50]
    // 0xb00700: add             x4, x3, #1
    // 0xb00704: stur            x4, [fp, #-0x60]
    // 0xb00708: lsl             x0, x4, #1
    // 0xb0070c: StoreField: r2->field_b = r0
    //     0xb0070c: stur            w0, [x2, #0xb]
    // 0xb00710: LoadField: r5 = r2->field_f
    //     0xb00710: ldur            w5, [x2, #0xf]
    // 0xb00714: DecompressPointer r5
    //     0xb00714: add             x5, x5, HEAP, lsl #32
    // 0xb00718: mov             x1, x5
    // 0xb0071c: ldur            x0, [fp, #-0x40]
    // 0xb00720: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb00720: add             x25, x1, x3, lsl #2
    //     0xb00724: add             x25, x25, #0xf
    //     0xb00728: str             w0, [x25]
    //     0xb0072c: tbz             w0, #0, #0xb00748
    //     0xb00730: ldurb           w16, [x1, #-1]
    //     0xb00734: ldurb           w17, [x0, #-1]
    //     0xb00738: and             x16, x17, x16, lsr #2
    //     0xb0073c: tst             x16, HEAP, lsr #32
    //     0xb00740: b.eq            #0xb00748
    //     0xb00744: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb00748: LoadField: r0 = r5->field_b
    //     0xb00748: ldur            w0, [x5, #0xb]
    // 0xb0074c: r1 = LoadInt32Instr(r0)
    //     0xb0074c: sbfx            x1, x0, #1, #0x1f
    // 0xb00750: cmp             x4, x1
    // 0xb00754: b.ne            #0xb00760
    // 0xb00758: mov             x1, x2
    // 0xb0075c: r0 = _growToNextCapacity()
    //     0xb0075c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb00760: ldur            x2, [fp, #-8]
    // 0xb00764: ldur            x1, [fp, #-0x28]
    // 0xb00768: ldur            x0, [fp, #-0x60]
    // 0xb0076c: add             x3, x0, #1
    // 0xb00770: lsl             x4, x3, #1
    // 0xb00774: StoreField: r1->field_b = r4
    //     0xb00774: stur            w4, [x1, #0xb]
    // 0xb00778: LoadField: r3 = r1->field_f
    //     0xb00778: ldur            w3, [x1, #0xf]
    // 0xb0077c: DecompressPointer r3
    //     0xb0077c: add             x3, x3, HEAP, lsl #32
    // 0xb00780: add             x4, x3, x0, lsl #2
    // 0xb00784: r16 = Instance_SizedBox
    //     0xb00784: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xb00788: ldr             x16, [x16, #0x8f0]
    // 0xb0078c: StoreField: r4->field_f = r16
    //     0xb0078c: stur            w16, [x4, #0xf]
    // 0xb00790: LoadField: r0 = r2->field_b
    //     0xb00790: ldur            w0, [x2, #0xb]
    // 0xb00794: DecompressPointer r0
    //     0xb00794: add             x0, x0, HEAP, lsl #32
    // 0xb00798: cmp             w0, NULL
    // 0xb0079c: b.eq            #0xb014e4
    // 0xb007a0: LoadField: r3 = r0->field_2f
    //     0xb007a0: ldur            w3, [x0, #0x2f]
    // 0xb007a4: DecompressPointer r3
    //     0xb007a4: add             x3, x3, HEAP, lsl #32
    // 0xb007a8: LoadField: r0 = r3->field_f
    //     0xb007a8: ldur            w0, [x3, #0xf]
    // 0xb007ac: DecompressPointer r0
    //     0xb007ac: add             x0, x0, HEAP, lsl #32
    // 0xb007b0: cmp             w0, NULL
    // 0xb007b4: r16 = true
    //     0xb007b4: add             x16, NULL, #0x20  ; true
    // 0xb007b8: r17 = false
    //     0xb007b8: add             x17, NULL, #0x30  ; false
    // 0xb007bc: csel            x4, x16, x17, ne
    // 0xb007c0: stur            x4, [fp, #-0x38]
    // 0xb007c4: LoadField: r0 = r3->field_13
    //     0xb007c4: ldur            w0, [x3, #0x13]
    // 0xb007c8: DecompressPointer r0
    //     0xb007c8: add             x0, x0, HEAP, lsl #32
    // 0xb007cc: stur            x0, [fp, #-0x30]
    // 0xb007d0: cmp             w0, NULL
    // 0xb007d4: b.ne            #0xb007e0
    // 0xb007d8: r3 = Null
    //     0xb007d8: mov             x3, NULL
    // 0xb007dc: b               #0xb007e8
    // 0xb007e0: LoadField: r3 = r0->field_7
    //     0xb007e0: ldur            w3, [x0, #7]
    // 0xb007e4: DecompressPointer r3
    //     0xb007e4: add             x3, x3, HEAP, lsl #32
    // 0xb007e8: cmp             w3, NULL
    // 0xb007ec: b.ne            #0xb007f8
    // 0xb007f0: r3 = 0
    //     0xb007f0: movz            x3, #0
    // 0xb007f4: b               #0xb00808
    // 0xb007f8: r5 = LoadInt32Instr(r3)
    //     0xb007f8: sbfx            x5, x3, #1, #0x1f
    //     0xb007fc: tbz             w3, #0, #0xb00804
    //     0xb00800: ldur            x5, [x3, #7]
    // 0xb00804: mov             x3, x5
    // 0xb00808: stur            x3, [fp, #-0x68]
    // 0xb0080c: cmp             w0, NULL
    // 0xb00810: b.ne            #0xb0081c
    // 0xb00814: r5 = Null
    //     0xb00814: mov             x5, NULL
    // 0xb00818: b               #0xb00824
    // 0xb0081c: LoadField: r5 = r0->field_b
    //     0xb0081c: ldur            w5, [x0, #0xb]
    // 0xb00820: DecompressPointer r5
    //     0xb00820: add             x5, x5, HEAP, lsl #32
    // 0xb00824: cmp             w5, NULL
    // 0xb00828: b.ne            #0xb00834
    // 0xb0082c: r5 = 0
    //     0xb0082c: movz            x5, #0
    // 0xb00830: b               #0xb00844
    // 0xb00834: r6 = LoadInt32Instr(r5)
    //     0xb00834: sbfx            x6, x5, #1, #0x1f
    //     0xb00838: tbz             w5, #0, #0xb00840
    //     0xb0083c: ldur            x6, [x5, #7]
    // 0xb00840: mov             x5, x6
    // 0xb00844: stur            x5, [fp, #-0x60]
    // 0xb00848: cmp             w0, NULL
    // 0xb0084c: b.ne            #0xb00858
    // 0xb00850: r6 = Null
    //     0xb00850: mov             x6, NULL
    // 0xb00854: b               #0xb00860
    // 0xb00858: LoadField: r6 = r0->field_f
    //     0xb00858: ldur            w6, [x0, #0xf]
    // 0xb0085c: DecompressPointer r6
    //     0xb0085c: add             x6, x6, HEAP, lsl #32
    // 0xb00860: cmp             w6, NULL
    // 0xb00864: b.ne            #0xb00870
    // 0xb00868: r6 = 0
    //     0xb00868: movz            x6, #0
    // 0xb0086c: b               #0xb00880
    // 0xb00870: r7 = LoadInt32Instr(r6)
    //     0xb00870: sbfx            x7, x6, #1, #0x1f
    //     0xb00874: tbz             w6, #0, #0xb0087c
    //     0xb00878: ldur            x7, [x6, #7]
    // 0xb0087c: mov             x6, x7
    // 0xb00880: stur            x6, [fp, #-0x50]
    // 0xb00884: r0 = Color()
    //     0xb00884: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb00888: mov             x1, x0
    // 0xb0088c: r0 = Instance_ColorSpace
    //     0xb0088c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb00890: stur            x1, [fp, #-0x40]
    // 0xb00894: StoreField: r1->field_27 = r0
    //     0xb00894: stur            w0, [x1, #0x27]
    // 0xb00898: d0 = 1.000000
    //     0xb00898: fmov            d0, #1.00000000
    // 0xb0089c: StoreField: r1->field_7 = d0
    //     0xb0089c: stur            d0, [x1, #7]
    // 0xb008a0: ldur            x2, [fp, #-0x68]
    // 0xb008a4: ubfx            x2, x2, #0, #0x20
    // 0xb008a8: and             w3, w2, #0xff
    // 0xb008ac: ubfx            x3, x3, #0, #0x20
    // 0xb008b0: scvtf           d0, x3
    // 0xb008b4: d1 = 255.000000
    //     0xb008b4: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb008b8: fdiv            d2, d0, d1
    // 0xb008bc: StoreField: r1->field_f = d2
    //     0xb008bc: stur            d2, [x1, #0xf]
    // 0xb008c0: ldur            x2, [fp, #-0x60]
    // 0xb008c4: ubfx            x2, x2, #0, #0x20
    // 0xb008c8: and             w3, w2, #0xff
    // 0xb008cc: ubfx            x3, x3, #0, #0x20
    // 0xb008d0: scvtf           d0, x3
    // 0xb008d4: fdiv            d2, d0, d1
    // 0xb008d8: ArrayStore: r1[0] = d2  ; List_8
    //     0xb008d8: stur            d2, [x1, #0x17]
    // 0xb008dc: ldur            x2, [fp, #-0x50]
    // 0xb008e0: ubfx            x2, x2, #0, #0x20
    // 0xb008e4: and             w3, w2, #0xff
    // 0xb008e8: ubfx            x3, x3, #0, #0x20
    // 0xb008ec: scvtf           d0, x3
    // 0xb008f0: fdiv            d2, d0, d1
    // 0xb008f4: StoreField: r1->field_1f = d2
    //     0xb008f4: stur            d2, [x1, #0x1f]
    // 0xb008f8: ldur            x2, [fp, #-0x30]
    // 0xb008fc: cmp             w2, NULL
    // 0xb00900: b.ne            #0xb0090c
    // 0xb00904: r3 = Null
    //     0xb00904: mov             x3, NULL
    // 0xb00908: b               #0xb00914
    // 0xb0090c: LoadField: r3 = r2->field_7
    //     0xb0090c: ldur            w3, [x2, #7]
    // 0xb00910: DecompressPointer r3
    //     0xb00910: add             x3, x3, HEAP, lsl #32
    // 0xb00914: cmp             w3, NULL
    // 0xb00918: b.ne            #0xb00924
    // 0xb0091c: r3 = 0
    //     0xb0091c: movz            x3, #0
    // 0xb00920: b               #0xb00934
    // 0xb00924: r4 = LoadInt32Instr(r3)
    //     0xb00924: sbfx            x4, x3, #1, #0x1f
    //     0xb00928: tbz             w3, #0, #0xb00930
    //     0xb0092c: ldur            x4, [x3, #7]
    // 0xb00930: mov             x3, x4
    // 0xb00934: stur            x3, [fp, #-0x68]
    // 0xb00938: cmp             w2, NULL
    // 0xb0093c: b.ne            #0xb00948
    // 0xb00940: r4 = Null
    //     0xb00940: mov             x4, NULL
    // 0xb00944: b               #0xb00950
    // 0xb00948: LoadField: r4 = r2->field_b
    //     0xb00948: ldur            w4, [x2, #0xb]
    // 0xb0094c: DecompressPointer r4
    //     0xb0094c: add             x4, x4, HEAP, lsl #32
    // 0xb00950: cmp             w4, NULL
    // 0xb00954: b.ne            #0xb00960
    // 0xb00958: r4 = 0
    //     0xb00958: movz            x4, #0
    // 0xb0095c: b               #0xb00970
    // 0xb00960: r5 = LoadInt32Instr(r4)
    //     0xb00960: sbfx            x5, x4, #1, #0x1f
    //     0xb00964: tbz             w4, #0, #0xb0096c
    //     0xb00968: ldur            x5, [x4, #7]
    // 0xb0096c: mov             x4, x5
    // 0xb00970: stur            x4, [fp, #-0x60]
    // 0xb00974: cmp             w2, NULL
    // 0xb00978: b.ne            #0xb00984
    // 0xb0097c: r2 = Null
    //     0xb0097c: mov             x2, NULL
    // 0xb00980: b               #0xb00990
    // 0xb00984: LoadField: r5 = r2->field_f
    //     0xb00984: ldur            w5, [x2, #0xf]
    // 0xb00988: DecompressPointer r5
    //     0xb00988: add             x5, x5, HEAP, lsl #32
    // 0xb0098c: mov             x2, x5
    // 0xb00990: cmp             w2, NULL
    // 0xb00994: b.ne            #0xb009a0
    // 0xb00998: r7 = 0
    //     0xb00998: movz            x7, #0
    // 0xb0099c: b               #0xb009b0
    // 0xb009a0: r5 = LoadInt32Instr(r2)
    //     0xb009a0: sbfx            x5, x2, #1, #0x1f
    //     0xb009a4: tbz             w2, #0, #0xb009ac
    //     0xb009a8: ldur            x5, [x2, #7]
    // 0xb009ac: mov             x7, x5
    // 0xb009b0: ldur            x5, [fp, #-8]
    // 0xb009b4: ldur            x2, [fp, #-0x28]
    // 0xb009b8: ldur            x6, [fp, #-0x38]
    // 0xb009bc: stur            x7, [fp, #-0x50]
    // 0xb009c0: r0 = Color()
    //     0xb009c0: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb009c4: mov             x3, x0
    // 0xb009c8: r0 = Instance_ColorSpace
    //     0xb009c8: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb009cc: stur            x3, [fp, #-0x30]
    // 0xb009d0: StoreField: r3->field_27 = r0
    //     0xb009d0: stur            w0, [x3, #0x27]
    // 0xb009d4: d0 = 0.700000
    //     0xb009d4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb009d8: ldr             d0, [x17, #0xf48]
    // 0xb009dc: StoreField: r3->field_7 = d0
    //     0xb009dc: stur            d0, [x3, #7]
    // 0xb009e0: ldur            x0, [fp, #-0x68]
    // 0xb009e4: ubfx            x0, x0, #0, #0x20
    // 0xb009e8: and             w1, w0, #0xff
    // 0xb009ec: ubfx            x1, x1, #0, #0x20
    // 0xb009f0: scvtf           d0, x1
    // 0xb009f4: d1 = 255.000000
    //     0xb009f4: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb009f8: fdiv            d2, d0, d1
    // 0xb009fc: StoreField: r3->field_f = d2
    //     0xb009fc: stur            d2, [x3, #0xf]
    // 0xb00a00: ldur            x0, [fp, #-0x60]
    // 0xb00a04: ubfx            x0, x0, #0, #0x20
    // 0xb00a08: and             w1, w0, #0xff
    // 0xb00a0c: ubfx            x1, x1, #0, #0x20
    // 0xb00a10: scvtf           d0, x1
    // 0xb00a14: fdiv            d2, d0, d1
    // 0xb00a18: ArrayStore: r3[0] = d2  ; List_8
    //     0xb00a18: stur            d2, [x3, #0x17]
    // 0xb00a1c: ldur            x0, [fp, #-0x50]
    // 0xb00a20: ubfx            x0, x0, #0, #0x20
    // 0xb00a24: and             w1, w0, #0xff
    // 0xb00a28: ubfx            x1, x1, #0, #0x20
    // 0xb00a2c: scvtf           d0, x1
    // 0xb00a30: fdiv            d2, d0, d1
    // 0xb00a34: StoreField: r3->field_1f = d2
    //     0xb00a34: stur            d2, [x3, #0x1f]
    // 0xb00a38: r1 = Null
    //     0xb00a38: mov             x1, NULL
    // 0xb00a3c: r2 = 4
    //     0xb00a3c: movz            x2, #0x4
    // 0xb00a40: r0 = AllocateArray()
    //     0xb00a40: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb00a44: mov             x2, x0
    // 0xb00a48: ldur            x0, [fp, #-0x40]
    // 0xb00a4c: stur            x2, [fp, #-0x48]
    // 0xb00a50: StoreField: r2->field_f = r0
    //     0xb00a50: stur            w0, [x2, #0xf]
    // 0xb00a54: ldur            x0, [fp, #-0x30]
    // 0xb00a58: StoreField: r2->field_13 = r0
    //     0xb00a58: stur            w0, [x2, #0x13]
    // 0xb00a5c: r1 = <Color>
    //     0xb00a5c: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb00a60: ldr             x1, [x1, #0xf80]
    // 0xb00a64: r0 = AllocateGrowableArray()
    //     0xb00a64: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb00a68: mov             x1, x0
    // 0xb00a6c: ldur            x0, [fp, #-0x48]
    // 0xb00a70: stur            x1, [fp, #-0x30]
    // 0xb00a74: StoreField: r1->field_f = r0
    //     0xb00a74: stur            w0, [x1, #0xf]
    // 0xb00a78: r2 = 4
    //     0xb00a78: movz            x2, #0x4
    // 0xb00a7c: StoreField: r1->field_b = r2
    //     0xb00a7c: stur            w2, [x1, #0xb]
    // 0xb00a80: r0 = LinearGradient()
    //     0xb00a80: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xb00a84: mov             x1, x0
    // 0xb00a88: r0 = Instance_Alignment
    //     0xb00a88: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb00a8c: ldr             x0, [x0, #0xce0]
    // 0xb00a90: stur            x1, [fp, #-0x40]
    // 0xb00a94: StoreField: r1->field_13 = r0
    //     0xb00a94: stur            w0, [x1, #0x13]
    // 0xb00a98: r0 = Instance_Alignment
    //     0xb00a98: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xb00a9c: ldr             x0, [x0, #0xce8]
    // 0xb00aa0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb00aa0: stur            w0, [x1, #0x17]
    // 0xb00aa4: r0 = Instance_TileMode
    //     0xb00aa4: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xb00aa8: ldr             x0, [x0, #0xcf0]
    // 0xb00aac: StoreField: r1->field_1b = r0
    //     0xb00aac: stur            w0, [x1, #0x1b]
    // 0xb00ab0: ldur            x0, [fp, #-0x30]
    // 0xb00ab4: StoreField: r1->field_7 = r0
    //     0xb00ab4: stur            w0, [x1, #7]
    // 0xb00ab8: r0 = BoxDecoration()
    //     0xb00ab8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb00abc: mov             x2, x0
    // 0xb00ac0: r0 = Instance_BorderRadius
    //     0xb00ac0: add             x0, PP, #0x48, lsl #12  ; [pp+0x48b20] Obj!BorderRadius@d5a2e1
    //     0xb00ac4: ldr             x0, [x0, #0xb20]
    // 0xb00ac8: stur            x2, [fp, #-0x30]
    // 0xb00acc: StoreField: r2->field_13 = r0
    //     0xb00acc: stur            w0, [x2, #0x13]
    // 0xb00ad0: ldur            x0, [fp, #-0x40]
    // 0xb00ad4: StoreField: r2->field_1b = r0
    //     0xb00ad4: stur            w0, [x2, #0x1b]
    // 0xb00ad8: r0 = Instance_BoxShape
    //     0xb00ad8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb00adc: ldr             x0, [x0, #0x80]
    // 0xb00ae0: StoreField: r2->field_23 = r0
    //     0xb00ae0: stur            w0, [x2, #0x23]
    // 0xb00ae4: ldur            x1, [fp, #-0x10]
    // 0xb00ae8: r0 = of()
    //     0xb00ae8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb00aec: LoadField: r1 = r0->field_87
    //     0xb00aec: ldur            w1, [x0, #0x87]
    // 0xb00af0: DecompressPointer r1
    //     0xb00af0: add             x1, x1, HEAP, lsl #32
    // 0xb00af4: LoadField: r0 = r1->field_7
    //     0xb00af4: ldur            w0, [x1, #7]
    // 0xb00af8: DecompressPointer r0
    //     0xb00af8: add             x0, x0, HEAP, lsl #32
    // 0xb00afc: r16 = 16.000000
    //     0xb00afc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb00b00: ldr             x16, [x16, #0x188]
    // 0xb00b04: r30 = Instance_Color
    //     0xb00b04: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb00b08: stp             lr, x16, [SP]
    // 0xb00b0c: mov             x1, x0
    // 0xb00b10: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb00b10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb00b14: ldr             x4, [x4, #0xaa0]
    // 0xb00b18: r0 = copyWith()
    //     0xb00b18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb00b1c: stur            x0, [fp, #-0x40]
    // 0xb00b20: r0 = TextSpan()
    //     0xb00b20: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb00b24: mov             x2, x0
    // 0xb00b28: r0 = "BUMPER OFFER\n"
    //     0xb00b28: add             x0, PP, #0x48, lsl #12  ; [pp+0x48338] "BUMPER OFFER\n"
    //     0xb00b2c: ldr             x0, [x0, #0x338]
    // 0xb00b30: stur            x2, [fp, #-0x48]
    // 0xb00b34: StoreField: r2->field_b = r0
    //     0xb00b34: stur            w0, [x2, #0xb]
    // 0xb00b38: r0 = Instance__DeferringMouseCursor
    //     0xb00b38: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb00b3c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb00b3c: stur            w0, [x2, #0x17]
    // 0xb00b40: ldur            x1, [fp, #-0x40]
    // 0xb00b44: StoreField: r2->field_7 = r1
    //     0xb00b44: stur            w1, [x2, #7]
    // 0xb00b48: ldur            x1, [fp, #-0x10]
    // 0xb00b4c: r0 = of()
    //     0xb00b4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb00b50: LoadField: r1 = r0->field_87
    //     0xb00b50: ldur            w1, [x0, #0x87]
    // 0xb00b54: DecompressPointer r1
    //     0xb00b54: add             x1, x1, HEAP, lsl #32
    // 0xb00b58: LoadField: r0 = r1->field_2b
    //     0xb00b58: ldur            w0, [x1, #0x2b]
    // 0xb00b5c: DecompressPointer r0
    //     0xb00b5c: add             x0, x0, HEAP, lsl #32
    // 0xb00b60: r16 = 12.000000
    //     0xb00b60: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb00b64: ldr             x16, [x16, #0x9e8]
    // 0xb00b68: r30 = Instance_Color
    //     0xb00b68: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb00b6c: stp             lr, x16, [SP]
    // 0xb00b70: mov             x1, x0
    // 0xb00b74: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb00b74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb00b78: ldr             x4, [x4, #0xaa0]
    // 0xb00b7c: r0 = copyWith()
    //     0xb00b7c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb00b80: stur            x0, [fp, #-0x40]
    // 0xb00b84: r0 = TextSpan()
    //     0xb00b84: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb00b88: mov             x3, x0
    // 0xb00b8c: r0 = "Unlocked from your last order"
    //     0xb00b8c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48340] "Unlocked from your last order"
    //     0xb00b90: ldr             x0, [x0, #0x340]
    // 0xb00b94: stur            x3, [fp, #-0x58]
    // 0xb00b98: StoreField: r3->field_b = r0
    //     0xb00b98: stur            w0, [x3, #0xb]
    // 0xb00b9c: r0 = Instance__DeferringMouseCursor
    //     0xb00b9c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb00ba0: ArrayStore: r3[0] = r0  ; List_4
    //     0xb00ba0: stur            w0, [x3, #0x17]
    // 0xb00ba4: ldur            x1, [fp, #-0x40]
    // 0xb00ba8: StoreField: r3->field_7 = r1
    //     0xb00ba8: stur            w1, [x3, #7]
    // 0xb00bac: r1 = Null
    //     0xb00bac: mov             x1, NULL
    // 0xb00bb0: r2 = 4
    //     0xb00bb0: movz            x2, #0x4
    // 0xb00bb4: r0 = AllocateArray()
    //     0xb00bb4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb00bb8: mov             x2, x0
    // 0xb00bbc: ldur            x0, [fp, #-0x48]
    // 0xb00bc0: stur            x2, [fp, #-0x40]
    // 0xb00bc4: StoreField: r2->field_f = r0
    //     0xb00bc4: stur            w0, [x2, #0xf]
    // 0xb00bc8: ldur            x0, [fp, #-0x58]
    // 0xb00bcc: StoreField: r2->field_13 = r0
    //     0xb00bcc: stur            w0, [x2, #0x13]
    // 0xb00bd0: r1 = <InlineSpan>
    //     0xb00bd0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb00bd4: ldr             x1, [x1, #0xe40]
    // 0xb00bd8: r0 = AllocateGrowableArray()
    //     0xb00bd8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb00bdc: mov             x1, x0
    // 0xb00be0: ldur            x0, [fp, #-0x40]
    // 0xb00be4: stur            x1, [fp, #-0x48]
    // 0xb00be8: StoreField: r1->field_f = r0
    //     0xb00be8: stur            w0, [x1, #0xf]
    // 0xb00bec: r2 = 4
    //     0xb00bec: movz            x2, #0x4
    // 0xb00bf0: StoreField: r1->field_b = r2
    //     0xb00bf0: stur            w2, [x1, #0xb]
    // 0xb00bf4: r0 = TextSpan()
    //     0xb00bf4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb00bf8: mov             x1, x0
    // 0xb00bfc: ldur            x0, [fp, #-0x48]
    // 0xb00c00: stur            x1, [fp, #-0x40]
    // 0xb00c04: StoreField: r1->field_f = r0
    //     0xb00c04: stur            w0, [x1, #0xf]
    // 0xb00c08: r0 = Instance__DeferringMouseCursor
    //     0xb00c08: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb00c0c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb00c0c: stur            w0, [x1, #0x17]
    // 0xb00c10: r0 = RichText()
    //     0xb00c10: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb00c14: mov             x1, x0
    // 0xb00c18: ldur            x2, [fp, #-0x40]
    // 0xb00c1c: stur            x0, [fp, #-0x40]
    // 0xb00c20: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb00c20: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb00c24: r0 = RichText()
    //     0xb00c24: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb00c28: ldur            x0, [fp, #-8]
    // 0xb00c2c: LoadField: r1 = r0->field_b
    //     0xb00c2c: ldur            w1, [x0, #0xb]
    // 0xb00c30: DecompressPointer r1
    //     0xb00c30: add             x1, x1, HEAP, lsl #32
    // 0xb00c34: cmp             w1, NULL
    // 0xb00c38: b.eq            #0xb014e8
    // 0xb00c3c: LoadField: r2 = r1->field_2f
    //     0xb00c3c: ldur            w2, [x1, #0x2f]
    // 0xb00c40: DecompressPointer r2
    //     0xb00c40: add             x2, x2, HEAP, lsl #32
    // 0xb00c44: LoadField: r3 = r2->field_7
    //     0xb00c44: ldur            w3, [x2, #7]
    // 0xb00c48: DecompressPointer r3
    //     0xb00c48: add             x3, x3, HEAP, lsl #32
    // 0xb00c4c: stur            x3, [fp, #-0x48]
    // 0xb00c50: r1 = Null
    //     0xb00c50: mov             x1, NULL
    // 0xb00c54: r2 = 4
    //     0xb00c54: movz            x2, #0x4
    // 0xb00c58: r0 = AllocateArray()
    //     0xb00c58: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb00c5c: mov             x1, x0
    // 0xb00c60: ldur            x0, [fp, #-0x48]
    // 0xb00c64: StoreField: r1->field_f = r0
    //     0xb00c64: stur            w0, [x1, #0xf]
    // 0xb00c68: r16 = "\n"
    //     0xb00c68: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0xb00c6c: StoreField: r1->field_13 = r16
    //     0xb00c6c: stur            w16, [x1, #0x13]
    // 0xb00c70: str             x1, [SP]
    // 0xb00c74: r0 = _interpolate()
    //     0xb00c74: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb00c78: ldur            x1, [fp, #-0x10]
    // 0xb00c7c: stur            x0, [fp, #-0x48]
    // 0xb00c80: r0 = of()
    //     0xb00c80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb00c84: LoadField: r1 = r0->field_87
    //     0xb00c84: ldur            w1, [x0, #0x87]
    // 0xb00c88: DecompressPointer r1
    //     0xb00c88: add             x1, x1, HEAP, lsl #32
    // 0xb00c8c: LoadField: r0 = r1->field_23
    //     0xb00c8c: ldur            w0, [x1, #0x23]
    // 0xb00c90: DecompressPointer r0
    //     0xb00c90: add             x0, x0, HEAP, lsl #32
    // 0xb00c94: r16 = 32.000000
    //     0xb00c94: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb00c98: ldr             x16, [x16, #0x848]
    // 0xb00c9c: r30 = Instance_Color
    //     0xb00c9c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb00ca0: stp             lr, x16, [SP]
    // 0xb00ca4: mov             x1, x0
    // 0xb00ca8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb00ca8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb00cac: ldr             x4, [x4, #0xaa0]
    // 0xb00cb0: r0 = copyWith()
    //     0xb00cb0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb00cb4: ldur            x1, [fp, #-0x10]
    // 0xb00cb8: stur            x0, [fp, #-0x58]
    // 0xb00cbc: r0 = of()
    //     0xb00cbc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb00cc0: LoadField: r1 = r0->field_87
    //     0xb00cc0: ldur            w1, [x0, #0x87]
    // 0xb00cc4: DecompressPointer r1
    //     0xb00cc4: add             x1, x1, HEAP, lsl #32
    // 0xb00cc8: LoadField: r0 = r1->field_2b
    //     0xb00cc8: ldur            w0, [x1, #0x2b]
    // 0xb00ccc: DecompressPointer r0
    //     0xb00ccc: add             x0, x0, HEAP, lsl #32
    // 0xb00cd0: r16 = Instance_Color
    //     0xb00cd0: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb00cd4: r30 = 16.000000
    //     0xb00cd4: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb00cd8: ldr             lr, [lr, #0x188]
    // 0xb00cdc: stp             lr, x16, [SP]
    // 0xb00ce0: mov             x1, x0
    // 0xb00ce4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb00ce4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb00ce8: ldr             x4, [x4, #0x9b8]
    // 0xb00cec: r0 = copyWith()
    //     0xb00cec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb00cf0: stur            x0, [fp, #-0x70]
    // 0xb00cf4: r0 = TextSpan()
    //     0xb00cf4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb00cf8: mov             x3, x0
    // 0xb00cfc: r0 = "OFF"
    //     0xb00cfc: add             x0, PP, #0x48, lsl #12  ; [pp+0x48348] "OFF"
    //     0xb00d00: ldr             x0, [x0, #0x348]
    // 0xb00d04: stur            x3, [fp, #-0x78]
    // 0xb00d08: StoreField: r3->field_b = r0
    //     0xb00d08: stur            w0, [x3, #0xb]
    // 0xb00d0c: r0 = Instance__DeferringMouseCursor
    //     0xb00d0c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb00d10: ArrayStore: r3[0] = r0  ; List_4
    //     0xb00d10: stur            w0, [x3, #0x17]
    // 0xb00d14: ldur            x1, [fp, #-0x70]
    // 0xb00d18: StoreField: r3->field_7 = r1
    //     0xb00d18: stur            w1, [x3, #7]
    // 0xb00d1c: r1 = Null
    //     0xb00d1c: mov             x1, NULL
    // 0xb00d20: r2 = 2
    //     0xb00d20: movz            x2, #0x2
    // 0xb00d24: r0 = AllocateArray()
    //     0xb00d24: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb00d28: mov             x2, x0
    // 0xb00d2c: ldur            x0, [fp, #-0x78]
    // 0xb00d30: stur            x2, [fp, #-0x70]
    // 0xb00d34: StoreField: r2->field_f = r0
    //     0xb00d34: stur            w0, [x2, #0xf]
    // 0xb00d38: r1 = <InlineSpan>
    //     0xb00d38: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb00d3c: ldr             x1, [x1, #0xe40]
    // 0xb00d40: r0 = AllocateGrowableArray()
    //     0xb00d40: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb00d44: mov             x1, x0
    // 0xb00d48: ldur            x0, [fp, #-0x70]
    // 0xb00d4c: stur            x1, [fp, #-0x78]
    // 0xb00d50: StoreField: r1->field_f = r0
    //     0xb00d50: stur            w0, [x1, #0xf]
    // 0xb00d54: r2 = 2
    //     0xb00d54: movz            x2, #0x2
    // 0xb00d58: StoreField: r1->field_b = r2
    //     0xb00d58: stur            w2, [x1, #0xb]
    // 0xb00d5c: r0 = TextSpan()
    //     0xb00d5c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb00d60: mov             x1, x0
    // 0xb00d64: ldur            x0, [fp, #-0x48]
    // 0xb00d68: stur            x1, [fp, #-0x70]
    // 0xb00d6c: StoreField: r1->field_b = r0
    //     0xb00d6c: stur            w0, [x1, #0xb]
    // 0xb00d70: ldur            x0, [fp, #-0x78]
    // 0xb00d74: StoreField: r1->field_f = r0
    //     0xb00d74: stur            w0, [x1, #0xf]
    // 0xb00d78: r0 = Instance__DeferringMouseCursor
    //     0xb00d78: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb00d7c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb00d7c: stur            w0, [x1, #0x17]
    // 0xb00d80: ldur            x0, [fp, #-0x58]
    // 0xb00d84: StoreField: r1->field_7 = r0
    //     0xb00d84: stur            w0, [x1, #7]
    // 0xb00d88: r0 = RichText()
    //     0xb00d88: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb00d8c: stur            x0, [fp, #-0x48]
    // 0xb00d90: r16 = Instance_TextAlign
    //     0xb00d90: ldr             x16, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb00d94: str             x16, [SP]
    // 0xb00d98: mov             x1, x0
    // 0xb00d9c: ldur            x2, [fp, #-0x70]
    // 0xb00da0: r4 = const [0, 0x3, 0x1, 0x2, textAlign, 0x2, null]
    //     0xb00da0: add             x4, PP, #0x48, lsl #12  ; [pp+0x48350] List(7) [0, 0x3, 0x1, 0x2, "textAlign", 0x2, Null]
    //     0xb00da4: ldr             x4, [x4, #0x350]
    // 0xb00da8: r0 = RichText()
    //     0xb00da8: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb00dac: r1 = Null
    //     0xb00dac: mov             x1, NULL
    // 0xb00db0: r2 = 6
    //     0xb00db0: movz            x2, #0x6
    // 0xb00db4: r0 = AllocateArray()
    //     0xb00db4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb00db8: mov             x2, x0
    // 0xb00dbc: ldur            x0, [fp, #-0x40]
    // 0xb00dc0: stur            x2, [fp, #-0x58]
    // 0xb00dc4: StoreField: r2->field_f = r0
    //     0xb00dc4: stur            w0, [x2, #0xf]
    // 0xb00dc8: r16 = Instance_VerticalDivider
    //     0xb00dc8: add             x16, PP, #0x49, lsl #12  ; [pp+0x49750] Obj!VerticalDivider@d66bb1
    //     0xb00dcc: ldr             x16, [x16, #0x750]
    // 0xb00dd0: StoreField: r2->field_13 = r16
    //     0xb00dd0: stur            w16, [x2, #0x13]
    // 0xb00dd4: ldur            x0, [fp, #-0x48]
    // 0xb00dd8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb00dd8: stur            w0, [x2, #0x17]
    // 0xb00ddc: r1 = <Widget>
    //     0xb00ddc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb00de0: r0 = AllocateGrowableArray()
    //     0xb00de0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb00de4: mov             x1, x0
    // 0xb00de8: ldur            x0, [fp, #-0x58]
    // 0xb00dec: stur            x1, [fp, #-0x40]
    // 0xb00df0: StoreField: r1->field_f = r0
    //     0xb00df0: stur            w0, [x1, #0xf]
    // 0xb00df4: r0 = 6
    //     0xb00df4: movz            x0, #0x6
    // 0xb00df8: StoreField: r1->field_b = r0
    //     0xb00df8: stur            w0, [x1, #0xb]
    // 0xb00dfc: r0 = Row()
    //     0xb00dfc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb00e00: mov             x1, x0
    // 0xb00e04: r0 = Instance_Axis
    //     0xb00e04: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb00e08: stur            x1, [fp, #-0x48]
    // 0xb00e0c: StoreField: r1->field_f = r0
    //     0xb00e0c: stur            w0, [x1, #0xf]
    // 0xb00e10: r2 = Instance_MainAxisAlignment
    //     0xb00e10: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb00e14: ldr             x2, [x2, #0xa8]
    // 0xb00e18: StoreField: r1->field_13 = r2
    //     0xb00e18: stur            w2, [x1, #0x13]
    // 0xb00e1c: r2 = Instance_MainAxisSize
    //     0xb00e1c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb00e20: ldr             x2, [x2, #0xa10]
    // 0xb00e24: ArrayStore: r1[0] = r2  ; List_4
    //     0xb00e24: stur            w2, [x1, #0x17]
    // 0xb00e28: r3 = Instance_CrossAxisAlignment
    //     0xb00e28: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb00e2c: ldr             x3, [x3, #0xa18]
    // 0xb00e30: StoreField: r1->field_1b = r3
    //     0xb00e30: stur            w3, [x1, #0x1b]
    // 0xb00e34: r4 = Instance_VerticalDirection
    //     0xb00e34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb00e38: ldr             x4, [x4, #0xa20]
    // 0xb00e3c: StoreField: r1->field_23 = r4
    //     0xb00e3c: stur            w4, [x1, #0x23]
    // 0xb00e40: r5 = Instance_Clip
    //     0xb00e40: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb00e44: ldr             x5, [x5, #0x38]
    // 0xb00e48: StoreField: r1->field_2b = r5
    //     0xb00e48: stur            w5, [x1, #0x2b]
    // 0xb00e4c: StoreField: r1->field_2f = rZR
    //     0xb00e4c: stur            xzr, [x1, #0x2f]
    // 0xb00e50: ldur            x6, [fp, #-0x40]
    // 0xb00e54: StoreField: r1->field_b = r6
    //     0xb00e54: stur            w6, [x1, #0xb]
    // 0xb00e58: r0 = Padding()
    //     0xb00e58: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb00e5c: mov             x1, x0
    // 0xb00e60: r0 = Instance_EdgeInsets
    //     0xb00e60: add             x0, PP, #0x48, lsl #12  ; [pp+0x48358] Obj!EdgeInsets@d57411
    //     0xb00e64: ldr             x0, [x0, #0x358]
    // 0xb00e68: stur            x1, [fp, #-0x40]
    // 0xb00e6c: StoreField: r1->field_f = r0
    //     0xb00e6c: stur            w0, [x1, #0xf]
    // 0xb00e70: ldur            x0, [fp, #-0x48]
    // 0xb00e74: StoreField: r1->field_b = r0
    //     0xb00e74: stur            w0, [x1, #0xb]
    // 0xb00e78: r0 = IntrinsicHeight()
    //     0xb00e78: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xb00e7c: mov             x1, x0
    // 0xb00e80: ldur            x0, [fp, #-0x40]
    // 0xb00e84: stur            x1, [fp, #-0x48]
    // 0xb00e88: StoreField: r1->field_b = r0
    //     0xb00e88: stur            w0, [x1, #0xb]
    // 0xb00e8c: r0 = Container()
    //     0xb00e8c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb00e90: stur            x0, [fp, #-0x40]
    // 0xb00e94: r16 = 100.000000
    //     0xb00e94: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xb00e98: ldur            lr, [fp, #-0x30]
    // 0xb00e9c: stp             lr, x16, [SP, #8]
    // 0xb00ea0: ldur            x16, [fp, #-0x48]
    // 0xb00ea4: str             x16, [SP]
    // 0xb00ea8: mov             x1, x0
    // 0xb00eac: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xb00eac: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xb00eb0: ldr             x4, [x4, #0xc78]
    // 0xb00eb4: r0 = Container()
    //     0xb00eb4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb00eb8: r1 = <Path>
    //     0xb00eb8: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xb00ebc: ldr             x1, [x1, #0xd30]
    // 0xb00ec0: r0 = MovieTicketClipper()
    //     0xb00ec0: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0xb00ec4: stur            x0, [fp, #-0x30]
    // 0xb00ec8: r0 = ClipPath()
    //     0xb00ec8: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xb00ecc: mov             x1, x0
    // 0xb00ed0: ldur            x0, [fp, #-0x30]
    // 0xb00ed4: stur            x1, [fp, #-0x48]
    // 0xb00ed8: StoreField: r1->field_f = r0
    //     0xb00ed8: stur            w0, [x1, #0xf]
    // 0xb00edc: r0 = Instance_Clip
    //     0xb00edc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb00ee0: ldr             x0, [x0, #0x138]
    // 0xb00ee4: StoreField: r1->field_13 = r0
    //     0xb00ee4: stur            w0, [x1, #0x13]
    // 0xb00ee8: ldur            x0, [fp, #-0x40]
    // 0xb00eec: StoreField: r1->field_b = r0
    //     0xb00eec: stur            w0, [x1, #0xb]
    // 0xb00ef0: r0 = Visibility()
    //     0xb00ef0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb00ef4: mov             x2, x0
    // 0xb00ef8: ldur            x0, [fp, #-0x48]
    // 0xb00efc: stur            x2, [fp, #-0x30]
    // 0xb00f00: StoreField: r2->field_b = r0
    //     0xb00f00: stur            w0, [x2, #0xb]
    // 0xb00f04: r0 = Instance_SizedBox
    //     0xb00f04: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb00f08: StoreField: r2->field_f = r0
    //     0xb00f08: stur            w0, [x2, #0xf]
    // 0xb00f0c: ldur            x0, [fp, #-0x38]
    // 0xb00f10: StoreField: r2->field_13 = r0
    //     0xb00f10: stur            w0, [x2, #0x13]
    // 0xb00f14: r0 = false
    //     0xb00f14: add             x0, NULL, #0x30  ; false
    // 0xb00f18: ArrayStore: r2[0] = r0  ; List_4
    //     0xb00f18: stur            w0, [x2, #0x17]
    // 0xb00f1c: StoreField: r2->field_1b = r0
    //     0xb00f1c: stur            w0, [x2, #0x1b]
    // 0xb00f20: StoreField: r2->field_1f = r0
    //     0xb00f20: stur            w0, [x2, #0x1f]
    // 0xb00f24: StoreField: r2->field_23 = r0
    //     0xb00f24: stur            w0, [x2, #0x23]
    // 0xb00f28: StoreField: r2->field_27 = r0
    //     0xb00f28: stur            w0, [x2, #0x27]
    // 0xb00f2c: StoreField: r2->field_2b = r0
    //     0xb00f2c: stur            w0, [x2, #0x2b]
    // 0xb00f30: ldur            x0, [fp, #-0x28]
    // 0xb00f34: LoadField: r1 = r0->field_b
    //     0xb00f34: ldur            w1, [x0, #0xb]
    // 0xb00f38: LoadField: r3 = r0->field_f
    //     0xb00f38: ldur            w3, [x0, #0xf]
    // 0xb00f3c: DecompressPointer r3
    //     0xb00f3c: add             x3, x3, HEAP, lsl #32
    // 0xb00f40: LoadField: r4 = r3->field_b
    //     0xb00f40: ldur            w4, [x3, #0xb]
    // 0xb00f44: r3 = LoadInt32Instr(r1)
    //     0xb00f44: sbfx            x3, x1, #1, #0x1f
    // 0xb00f48: stur            x3, [fp, #-0x50]
    // 0xb00f4c: r1 = LoadInt32Instr(r4)
    //     0xb00f4c: sbfx            x1, x4, #1, #0x1f
    // 0xb00f50: cmp             x3, x1
    // 0xb00f54: b.ne            #0xb00f60
    // 0xb00f58: mov             x1, x0
    // 0xb00f5c: r0 = _growToNextCapacity()
    //     0xb00f5c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb00f60: ldur            x4, [fp, #-8]
    // 0xb00f64: ldur            x2, [fp, #-0x28]
    // 0xb00f68: ldur            x3, [fp, #-0x50]
    // 0xb00f6c: add             x0, x3, #1
    // 0xb00f70: lsl             x1, x0, #1
    // 0xb00f74: StoreField: r2->field_b = r1
    //     0xb00f74: stur            w1, [x2, #0xb]
    // 0xb00f78: LoadField: r1 = r2->field_f
    //     0xb00f78: ldur            w1, [x2, #0xf]
    // 0xb00f7c: DecompressPointer r1
    //     0xb00f7c: add             x1, x1, HEAP, lsl #32
    // 0xb00f80: ldur            x0, [fp, #-0x30]
    // 0xb00f84: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb00f84: add             x25, x1, x3, lsl #2
    //     0xb00f88: add             x25, x25, #0xf
    //     0xb00f8c: str             w0, [x25]
    //     0xb00f90: tbz             w0, #0, #0xb00fac
    //     0xb00f94: ldurb           w16, [x1, #-1]
    //     0xb00f98: ldurb           w17, [x0, #-1]
    //     0xb00f9c: and             x16, x17, x16, lsr #2
    //     0xb00fa0: tst             x16, HEAP, lsr #32
    //     0xb00fa4: b.eq            #0xb00fac
    //     0xb00fa8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb00fac: ldur            x1, [fp, #-0x10]
    // 0xb00fb0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb00fb0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb00fb4: r0 = _of()
    //     0xb00fb4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb00fb8: LoadField: r1 = r0->field_7
    //     0xb00fb8: ldur            w1, [x0, #7]
    // 0xb00fbc: DecompressPointer r1
    //     0xb00fbc: add             x1, x1, HEAP, lsl #32
    // 0xb00fc0: LoadField: d0 = r1->field_f
    //     0xb00fc0: ldur            d0, [x1, #0xf]
    // 0xb00fc4: d1 = 0.375000
    //     0xb00fc4: fmov            d1, #0.37500000
    // 0xb00fc8: fmul            d2, d0, d1
    // 0xb00fcc: ldur            x1, [fp, #-0x10]
    // 0xb00fd0: stur            d2, [fp, #-0x80]
    // 0xb00fd4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb00fd4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb00fd8: r0 = _of()
    //     0xb00fd8: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb00fdc: LoadField: r1 = r0->field_7
    //     0xb00fdc: ldur            w1, [x0, #7]
    // 0xb00fe0: DecompressPointer r1
    //     0xb00fe0: add             x1, x1, HEAP, lsl #32
    // 0xb00fe4: LoadField: d0 = r1->field_7
    //     0xb00fe4: ldur            d0, [x1, #7]
    // 0xb00fe8: ldur            x1, [fp, #-8]
    // 0xb00fec: stur            d0, [fp, #-0x88]
    // 0xb00ff0: LoadField: r0 = r1->field_b
    //     0xb00ff0: ldur            w0, [x1, #0xb]
    // 0xb00ff4: DecompressPointer r0
    //     0xb00ff4: add             x0, x0, HEAP, lsl #32
    // 0xb00ff8: cmp             w0, NULL
    // 0xb00ffc: b.eq            #0xb014ec
    // 0xb01000: LoadField: r2 = r0->field_b
    //     0xb01000: ldur            w2, [x0, #0xb]
    // 0xb01004: DecompressPointer r2
    //     0xb01004: add             x2, x2, HEAP, lsl #32
    // 0xb01008: r0 = LoadClassIdInstr(r2)
    //     0xb01008: ldur            x0, [x2, #-1]
    //     0xb0100c: ubfx            x0, x0, #0xc, #0x14
    // 0xb01010: str             x2, [SP]
    // 0xb01014: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb01014: movz            x17, #0xc898
    //     0xb01018: add             lr, x0, x17
    //     0xb0101c: ldr             lr, [x21, lr, lsl #3]
    //     0xb01020: blr             lr
    // 0xb01024: mov             x3, x0
    // 0xb01028: ldur            x0, [fp, #-8]
    // 0xb0102c: stur            x3, [fp, #-0x38]
    // 0xb01030: LoadField: r4 = r0->field_13
    //     0xb01030: ldur            w4, [x0, #0x13]
    // 0xb01034: DecompressPointer r4
    //     0xb01034: add             x4, x4, HEAP, lsl #32
    // 0xb01038: r16 = Sentinel
    //     0xb01038: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb0103c: cmp             w4, w16
    // 0xb01040: b.eq            #0xb014f0
    // 0xb01044: ldur            x2, [fp, #-0x20]
    // 0xb01048: stur            x4, [fp, #-0x30]
    // 0xb0104c: r1 = Function '<anonymous closure>':.
    //     0xb0104c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e98] AnonymousClosure: (0xb043f4), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::build (0xb00118)
    //     0xb01050: ldr             x1, [x1, #0xe98]
    // 0xb01054: r0 = AllocateClosure()
    //     0xb01054: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb01058: ldur            x2, [fp, #-0x20]
    // 0xb0105c: r1 = Function '<anonymous closure>':.
    //     0xb0105c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57ea0] AnonymousClosure: (0xb01550), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::build (0xb00118)
    //     0xb01060: ldr             x1, [x1, #0xea0]
    // 0xb01064: stur            x0, [fp, #-0x20]
    // 0xb01068: r0 = AllocateClosure()
    //     0xb01068: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0106c: stur            x0, [fp, #-0x40]
    // 0xb01070: r0 = PageView()
    //     0xb01070: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb01074: stur            x0, [fp, #-0x48]
    // 0xb01078: r16 = Instance_BouncingScrollPhysics
    //     0xb01078: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb0107c: ldr             x16, [x16, #0x890]
    // 0xb01080: r30 = false
    //     0xb01080: add             lr, NULL, #0x30  ; false
    // 0xb01084: stp             lr, x16, [SP, #8]
    // 0xb01088: ldur            x16, [fp, #-0x30]
    // 0xb0108c: str             x16, [SP]
    // 0xb01090: mov             x1, x0
    // 0xb01094: ldur            x2, [fp, #-0x40]
    // 0xb01098: ldur            x3, [fp, #-0x38]
    // 0xb0109c: ldur            x5, [fp, #-0x20]
    // 0xb010a0: r4 = const [0, 0x7, 0x3, 0x4, controller, 0x6, padEnds, 0x5, physics, 0x4, null]
    //     0xb010a0: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d20] List(11) [0, 0x7, 0x3, 0x4, "controller", 0x6, "padEnds", 0x5, "physics", 0x4, Null]
    //     0xb010a4: ldr             x4, [x4, #0xd20]
    // 0xb010a8: r0 = PageView.builder()
    //     0xb010a8: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb010ac: ldur            d0, [fp, #-0x88]
    // 0xb010b0: r0 = inline_Allocate_Double()
    //     0xb010b0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb010b4: add             x0, x0, #0x10
    //     0xb010b8: cmp             x1, x0
    //     0xb010bc: b.ls            #0xb014fc
    //     0xb010c0: str             x0, [THR, #0x50]  ; THR::top
    //     0xb010c4: sub             x0, x0, #0xf
    //     0xb010c8: movz            x1, #0xe15c
    //     0xb010cc: movk            x1, #0x3, lsl #16
    //     0xb010d0: stur            x1, [x0, #-1]
    // 0xb010d4: StoreField: r0->field_7 = d0
    //     0xb010d4: stur            d0, [x0, #7]
    // 0xb010d8: stur            x0, [fp, #-0x20]
    // 0xb010dc: r0 = SizedBox()
    //     0xb010dc: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb010e0: mov             x2, x0
    // 0xb010e4: ldur            x0, [fp, #-0x20]
    // 0xb010e8: stur            x2, [fp, #-0x30]
    // 0xb010ec: StoreField: r2->field_f = r0
    //     0xb010ec: stur            w0, [x2, #0xf]
    // 0xb010f0: ldur            d0, [fp, #-0x80]
    // 0xb010f4: r0 = inline_Allocate_Double()
    //     0xb010f4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb010f8: add             x0, x0, #0x10
    //     0xb010fc: cmp             x1, x0
    //     0xb01100: b.ls            #0xb0150c
    //     0xb01104: str             x0, [THR, #0x50]  ; THR::top
    //     0xb01108: sub             x0, x0, #0xf
    //     0xb0110c: movz            x1, #0xe15c
    //     0xb01110: movk            x1, #0x3, lsl #16
    //     0xb01114: stur            x1, [x0, #-1]
    // 0xb01118: StoreField: r0->field_7 = d0
    //     0xb01118: stur            d0, [x0, #7]
    // 0xb0111c: StoreField: r2->field_13 = r0
    //     0xb0111c: stur            w0, [x2, #0x13]
    // 0xb01120: ldur            x0, [fp, #-0x48]
    // 0xb01124: StoreField: r2->field_b = r0
    //     0xb01124: stur            w0, [x2, #0xb]
    // 0xb01128: ldur            x0, [fp, #-0x28]
    // 0xb0112c: LoadField: r1 = r0->field_b
    //     0xb0112c: ldur            w1, [x0, #0xb]
    // 0xb01130: LoadField: r3 = r0->field_f
    //     0xb01130: ldur            w3, [x0, #0xf]
    // 0xb01134: DecompressPointer r3
    //     0xb01134: add             x3, x3, HEAP, lsl #32
    // 0xb01138: LoadField: r4 = r3->field_b
    //     0xb01138: ldur            w4, [x3, #0xb]
    // 0xb0113c: r3 = LoadInt32Instr(r1)
    //     0xb0113c: sbfx            x3, x1, #1, #0x1f
    // 0xb01140: stur            x3, [fp, #-0x50]
    // 0xb01144: r1 = LoadInt32Instr(r4)
    //     0xb01144: sbfx            x1, x4, #1, #0x1f
    // 0xb01148: cmp             x3, x1
    // 0xb0114c: b.ne            #0xb01158
    // 0xb01150: mov             x1, x0
    // 0xb01154: r0 = _growToNextCapacity()
    //     0xb01154: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb01158: ldur            x4, [fp, #-8]
    // 0xb0115c: ldur            x2, [fp, #-0x28]
    // 0xb01160: ldur            x3, [fp, #-0x50]
    // 0xb01164: add             x0, x3, #1
    // 0xb01168: lsl             x1, x0, #1
    // 0xb0116c: StoreField: r2->field_b = r1
    //     0xb0116c: stur            w1, [x2, #0xb]
    // 0xb01170: LoadField: r1 = r2->field_f
    //     0xb01170: ldur            w1, [x2, #0xf]
    // 0xb01174: DecompressPointer r1
    //     0xb01174: add             x1, x1, HEAP, lsl #32
    // 0xb01178: ldur            x0, [fp, #-0x30]
    // 0xb0117c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0117c: add             x25, x1, x3, lsl #2
    //     0xb01180: add             x25, x25, #0xf
    //     0xb01184: str             w0, [x25]
    //     0xb01188: tbz             w0, #0, #0xb011a4
    //     0xb0118c: ldurb           w16, [x1, #-1]
    //     0xb01190: ldurb           w17, [x0, #-1]
    //     0xb01194: and             x16, x17, x16, lsr #2
    //     0xb01198: tst             x16, HEAP, lsr #32
    //     0xb0119c: b.eq            #0xb011a4
    //     0xb011a0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb011a4: LoadField: r0 = r4->field_b
    //     0xb011a4: ldur            w0, [x4, #0xb]
    // 0xb011a8: DecompressPointer r0
    //     0xb011a8: add             x0, x0, HEAP, lsl #32
    // 0xb011ac: cmp             w0, NULL
    // 0xb011b0: b.eq            #0xb01524
    // 0xb011b4: LoadField: r1 = r0->field_b
    //     0xb011b4: ldur            w1, [x0, #0xb]
    // 0xb011b8: DecompressPointer r1
    //     0xb011b8: add             x1, x1, HEAP, lsl #32
    // 0xb011bc: r0 = LoadClassIdInstr(r1)
    //     0xb011bc: ldur            x0, [x1, #-1]
    //     0xb011c0: ubfx            x0, x0, #0xc, #0x14
    // 0xb011c4: str             x1, [SP]
    // 0xb011c8: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb011c8: movz            x17, #0xc898
    //     0xb011cc: add             lr, x0, x17
    //     0xb011d0: ldr             lr, [x21, lr, lsl #3]
    //     0xb011d4: blr             lr
    // 0xb011d8: r1 = LoadInt32Instr(r0)
    //     0xb011d8: sbfx            x1, x0, #1, #0x1f
    // 0xb011dc: cmp             x1, #2
    // 0xb011e0: b.le            #0xb013b8
    // 0xb011e4: ldur            x2, [fp, #-8]
    // 0xb011e8: ldur            x1, [fp, #-0x28]
    // 0xb011ec: LoadField: r0 = r2->field_b
    //     0xb011ec: ldur            w0, [x2, #0xb]
    // 0xb011f0: DecompressPointer r0
    //     0xb011f0: add             x0, x0, HEAP, lsl #32
    // 0xb011f4: cmp             w0, NULL
    // 0xb011f8: b.eq            #0xb01528
    // 0xb011fc: LoadField: r3 = r0->field_b
    //     0xb011fc: ldur            w3, [x0, #0xb]
    // 0xb01200: DecompressPointer r3
    //     0xb01200: add             x3, x3, HEAP, lsl #32
    // 0xb01204: r0 = LoadClassIdInstr(r3)
    //     0xb01204: ldur            x0, [x3, #-1]
    //     0xb01208: ubfx            x0, x0, #0xc, #0x14
    // 0xb0120c: str             x3, [SP]
    // 0xb01210: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb01210: movz            x17, #0xc898
    //     0xb01214: add             lr, x0, x17
    //     0xb01218: ldr             lr, [x21, lr, lsl #3]
    //     0xb0121c: blr             lr
    // 0xb01220: mov             x2, x0
    // 0xb01224: ldur            x0, [fp, #-8]
    // 0xb01228: stur            x2, [fp, #-0x20]
    // 0xb0122c: LoadField: r3 = r0->field_1b
    //     0xb0122c: ldur            x3, [x0, #0x1b]
    // 0xb01230: ldur            x1, [fp, #-0x10]
    // 0xb01234: stur            x3, [fp, #-0x50]
    // 0xb01238: r0 = of()
    //     0xb01238: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0123c: LoadField: r1 = r0->field_5b
    //     0xb0123c: ldur            w1, [x0, #0x5b]
    // 0xb01240: DecompressPointer r1
    //     0xb01240: add             x1, x1, HEAP, lsl #32
    // 0xb01244: ldur            x0, [fp, #-0x20]
    // 0xb01248: stur            x1, [fp, #-8]
    // 0xb0124c: r2 = LoadInt32Instr(r0)
    //     0xb0124c: sbfx            x2, x0, #1, #0x1f
    // 0xb01250: stur            x2, [fp, #-0x60]
    // 0xb01254: r0 = CarouselIndicator()
    //     0xb01254: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xb01258: mov             x3, x0
    // 0xb0125c: ldur            x0, [fp, #-0x60]
    // 0xb01260: stur            x3, [fp, #-0x10]
    // 0xb01264: StoreField: r3->field_b = r0
    //     0xb01264: stur            x0, [x3, #0xb]
    // 0xb01268: ldur            x0, [fp, #-0x50]
    // 0xb0126c: StoreField: r3->field_13 = r0
    //     0xb0126c: stur            x0, [x3, #0x13]
    // 0xb01270: ldur            x0, [fp, #-8]
    // 0xb01274: StoreField: r3->field_1b = r0
    //     0xb01274: stur            w0, [x3, #0x1b]
    // 0xb01278: r0 = Instance_Color
    //     0xb01278: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb0127c: ldr             x0, [x0, #0x90]
    // 0xb01280: StoreField: r3->field_1f = r0
    //     0xb01280: stur            w0, [x3, #0x1f]
    // 0xb01284: r1 = Null
    //     0xb01284: mov             x1, NULL
    // 0xb01288: r2 = 2
    //     0xb01288: movz            x2, #0x2
    // 0xb0128c: r0 = AllocateArray()
    //     0xb0128c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb01290: mov             x2, x0
    // 0xb01294: ldur            x0, [fp, #-0x10]
    // 0xb01298: stur            x2, [fp, #-8]
    // 0xb0129c: StoreField: r2->field_f = r0
    //     0xb0129c: stur            w0, [x2, #0xf]
    // 0xb012a0: r1 = <Widget>
    //     0xb012a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb012a4: r0 = AllocateGrowableArray()
    //     0xb012a4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb012a8: mov             x1, x0
    // 0xb012ac: ldur            x0, [fp, #-8]
    // 0xb012b0: stur            x1, [fp, #-0x10]
    // 0xb012b4: StoreField: r1->field_f = r0
    //     0xb012b4: stur            w0, [x1, #0xf]
    // 0xb012b8: r0 = 2
    //     0xb012b8: movz            x0, #0x2
    // 0xb012bc: StoreField: r1->field_b = r0
    //     0xb012bc: stur            w0, [x1, #0xb]
    // 0xb012c0: r0 = Row()
    //     0xb012c0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb012c4: mov             x1, x0
    // 0xb012c8: r0 = Instance_Axis
    //     0xb012c8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb012cc: stur            x1, [fp, #-8]
    // 0xb012d0: StoreField: r1->field_f = r0
    //     0xb012d0: stur            w0, [x1, #0xf]
    // 0xb012d4: r0 = Instance_MainAxisAlignment
    //     0xb012d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb012d8: ldr             x0, [x0, #0xab0]
    // 0xb012dc: StoreField: r1->field_13 = r0
    //     0xb012dc: stur            w0, [x1, #0x13]
    // 0xb012e0: r0 = Instance_MainAxisSize
    //     0xb012e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb012e4: ldr             x0, [x0, #0xa10]
    // 0xb012e8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb012e8: stur            w0, [x1, #0x17]
    // 0xb012ec: r0 = Instance_CrossAxisAlignment
    //     0xb012ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb012f0: ldr             x0, [x0, #0xa18]
    // 0xb012f4: StoreField: r1->field_1b = r0
    //     0xb012f4: stur            w0, [x1, #0x1b]
    // 0xb012f8: r0 = Instance_VerticalDirection
    //     0xb012f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb012fc: ldr             x0, [x0, #0xa20]
    // 0xb01300: StoreField: r1->field_23 = r0
    //     0xb01300: stur            w0, [x1, #0x23]
    // 0xb01304: r2 = Instance_Clip
    //     0xb01304: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb01308: ldr             x2, [x2, #0x38]
    // 0xb0130c: StoreField: r1->field_2b = r2
    //     0xb0130c: stur            w2, [x1, #0x2b]
    // 0xb01310: StoreField: r1->field_2f = rZR
    //     0xb01310: stur            xzr, [x1, #0x2f]
    // 0xb01314: ldur            x3, [fp, #-0x10]
    // 0xb01318: StoreField: r1->field_b = r3
    //     0xb01318: stur            w3, [x1, #0xb]
    // 0xb0131c: r0 = Padding()
    //     0xb0131c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb01320: mov             x2, x0
    // 0xb01324: r0 = Instance_EdgeInsets
    //     0xb01324: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb01328: ldr             x0, [x0, #0xa00]
    // 0xb0132c: stur            x2, [fp, #-0x10]
    // 0xb01330: StoreField: r2->field_f = r0
    //     0xb01330: stur            w0, [x2, #0xf]
    // 0xb01334: ldur            x0, [fp, #-8]
    // 0xb01338: StoreField: r2->field_b = r0
    //     0xb01338: stur            w0, [x2, #0xb]
    // 0xb0133c: ldur            x0, [fp, #-0x28]
    // 0xb01340: LoadField: r1 = r0->field_b
    //     0xb01340: ldur            w1, [x0, #0xb]
    // 0xb01344: LoadField: r3 = r0->field_f
    //     0xb01344: ldur            w3, [x0, #0xf]
    // 0xb01348: DecompressPointer r3
    //     0xb01348: add             x3, x3, HEAP, lsl #32
    // 0xb0134c: LoadField: r4 = r3->field_b
    //     0xb0134c: ldur            w4, [x3, #0xb]
    // 0xb01350: r3 = LoadInt32Instr(r1)
    //     0xb01350: sbfx            x3, x1, #1, #0x1f
    // 0xb01354: stur            x3, [fp, #-0x50]
    // 0xb01358: r1 = LoadInt32Instr(r4)
    //     0xb01358: sbfx            x1, x4, #1, #0x1f
    // 0xb0135c: cmp             x3, x1
    // 0xb01360: b.ne            #0xb0136c
    // 0xb01364: mov             x1, x0
    // 0xb01368: r0 = _growToNextCapacity()
    //     0xb01368: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0136c: ldur            x2, [fp, #-0x28]
    // 0xb01370: ldur            x3, [fp, #-0x50]
    // 0xb01374: add             x0, x3, #1
    // 0xb01378: lsl             x1, x0, #1
    // 0xb0137c: StoreField: r2->field_b = r1
    //     0xb0137c: stur            w1, [x2, #0xb]
    // 0xb01380: LoadField: r1 = r2->field_f
    //     0xb01380: ldur            w1, [x2, #0xf]
    // 0xb01384: DecompressPointer r1
    //     0xb01384: add             x1, x1, HEAP, lsl #32
    // 0xb01388: ldur            x0, [fp, #-0x10]
    // 0xb0138c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0138c: add             x25, x1, x3, lsl #2
    //     0xb01390: add             x25, x25, #0xf
    //     0xb01394: str             w0, [x25]
    //     0xb01398: tbz             w0, #0, #0xb013b4
    //     0xb0139c: ldurb           w16, [x1, #-1]
    //     0xb013a0: ldurb           w17, [x0, #-1]
    //     0xb013a4: and             x16, x17, x16, lsr #2
    //     0xb013a8: tst             x16, HEAP, lsr #32
    //     0xb013ac: b.eq            #0xb013b4
    //     0xb013b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb013b4: b               #0xb01448
    // 0xb013b8: ldur            x2, [fp, #-0x28]
    // 0xb013bc: r0 = Container()
    //     0xb013bc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb013c0: mov             x1, x0
    // 0xb013c4: stur            x0, [fp, #-8]
    // 0xb013c8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb013c8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb013cc: r0 = Container()
    //     0xb013cc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb013d0: ldur            x0, [fp, #-0x28]
    // 0xb013d4: LoadField: r1 = r0->field_b
    //     0xb013d4: ldur            w1, [x0, #0xb]
    // 0xb013d8: LoadField: r2 = r0->field_f
    //     0xb013d8: ldur            w2, [x0, #0xf]
    // 0xb013dc: DecompressPointer r2
    //     0xb013dc: add             x2, x2, HEAP, lsl #32
    // 0xb013e0: LoadField: r3 = r2->field_b
    //     0xb013e0: ldur            w3, [x2, #0xb]
    // 0xb013e4: r2 = LoadInt32Instr(r1)
    //     0xb013e4: sbfx            x2, x1, #1, #0x1f
    // 0xb013e8: stur            x2, [fp, #-0x50]
    // 0xb013ec: r1 = LoadInt32Instr(r3)
    //     0xb013ec: sbfx            x1, x3, #1, #0x1f
    // 0xb013f0: cmp             x2, x1
    // 0xb013f4: b.ne            #0xb01400
    // 0xb013f8: mov             x1, x0
    // 0xb013fc: r0 = _growToNextCapacity()
    //     0xb013fc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb01400: ldur            x2, [fp, #-0x28]
    // 0xb01404: ldur            x3, [fp, #-0x50]
    // 0xb01408: add             x0, x3, #1
    // 0xb0140c: lsl             x1, x0, #1
    // 0xb01410: StoreField: r2->field_b = r1
    //     0xb01410: stur            w1, [x2, #0xb]
    // 0xb01414: LoadField: r1 = r2->field_f
    //     0xb01414: ldur            w1, [x2, #0xf]
    // 0xb01418: DecompressPointer r1
    //     0xb01418: add             x1, x1, HEAP, lsl #32
    // 0xb0141c: ldur            x0, [fp, #-8]
    // 0xb01420: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb01420: add             x25, x1, x3, lsl #2
    //     0xb01424: add             x25, x25, #0xf
    //     0xb01428: str             w0, [x25]
    //     0xb0142c: tbz             w0, #0, #0xb01448
    //     0xb01430: ldurb           w16, [x1, #-1]
    //     0xb01434: ldurb           w17, [x0, #-1]
    //     0xb01438: and             x16, x17, x16, lsr #2
    //     0xb0143c: tst             x16, HEAP, lsr #32
    //     0xb01440: b.eq            #0xb01448
    //     0xb01444: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb01448: ldur            x0, [fp, #-0x18]
    // 0xb0144c: r0 = Column()
    //     0xb0144c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb01450: mov             x1, x0
    // 0xb01454: r0 = Instance_Axis
    //     0xb01454: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb01458: stur            x1, [fp, #-8]
    // 0xb0145c: StoreField: r1->field_f = r0
    //     0xb0145c: stur            w0, [x1, #0xf]
    // 0xb01460: r0 = Instance_MainAxisAlignment
    //     0xb01460: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb01464: ldr             x0, [x0, #0xa08]
    // 0xb01468: StoreField: r1->field_13 = r0
    //     0xb01468: stur            w0, [x1, #0x13]
    // 0xb0146c: r0 = Instance_MainAxisSize
    //     0xb0146c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb01470: ldr             x0, [x0, #0xdd0]
    // 0xb01474: ArrayStore: r1[0] = r0  ; List_4
    //     0xb01474: stur            w0, [x1, #0x17]
    // 0xb01478: ldur            x0, [fp, #-0x18]
    // 0xb0147c: StoreField: r1->field_1b = r0
    //     0xb0147c: stur            w0, [x1, #0x1b]
    // 0xb01480: r0 = Instance_VerticalDirection
    //     0xb01480: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb01484: ldr             x0, [x0, #0xa20]
    // 0xb01488: StoreField: r1->field_23 = r0
    //     0xb01488: stur            w0, [x1, #0x23]
    // 0xb0148c: r0 = Instance_Clip
    //     0xb0148c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb01490: ldr             x0, [x0, #0x38]
    // 0xb01494: StoreField: r1->field_2b = r0
    //     0xb01494: stur            w0, [x1, #0x2b]
    // 0xb01498: StoreField: r1->field_2f = rZR
    //     0xb01498: stur            xzr, [x1, #0x2f]
    // 0xb0149c: ldur            x0, [fp, #-0x28]
    // 0xb014a0: StoreField: r1->field_b = r0
    //     0xb014a0: stur            w0, [x1, #0xb]
    // 0xb014a4: r0 = Padding()
    //     0xb014a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb014a8: r1 = Instance_EdgeInsets
    //     0xb014a8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3edd8] Obj!EdgeInsets@d58f41
    //     0xb014ac: ldr             x1, [x1, #0xdd8]
    // 0xb014b0: StoreField: r0->field_f = r1
    //     0xb014b0: stur            w1, [x0, #0xf]
    // 0xb014b4: ldur            x1, [fp, #-8]
    // 0xb014b8: StoreField: r0->field_b = r1
    //     0xb014b8: stur            w1, [x0, #0xb]
    // 0xb014bc: LeaveFrame
    //     0xb014bc: mov             SP, fp
    //     0xb014c0: ldp             fp, lr, [SP], #0x10
    // 0xb014c4: ret
    //     0xb014c4: ret             
    // 0xb014c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb014c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb014cc: b               #0xb00140
    // 0xb014d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb014d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb014d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb014d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb014d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb014d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb014dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb014dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb014e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb014e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb014e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb014e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb014e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb014e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb014ec: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb014ec: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb014f0: r9 = _pageController
    //     0xb014f0: add             x9, PP, #0x57, lsl #12  ; [pp+0x57ea8] Field <_GroupCarouselItemViewState@1494318134._pageController@1494318134>: late (offset: 0x14)
    //     0xb014f4: ldr             x9, [x9, #0xea8]
    // 0xb014f8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb014f8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb014fc: SaveReg d0
    //     0xb014fc: str             q0, [SP, #-0x10]!
    // 0xb01500: r0 = AllocateDouble()
    //     0xb01500: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb01504: RestoreReg d0
    //     0xb01504: ldr             q0, [SP], #0x10
    // 0xb01508: b               #0xb010d4
    // 0xb0150c: SaveReg d0
    //     0xb0150c: str             q0, [SP, #-0x10]!
    // 0xb01510: SaveReg r2
    //     0xb01510: str             x2, [SP, #-8]!
    // 0xb01514: r0 = AllocateDouble()
    //     0xb01514: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb01518: RestoreReg r2
    //     0xb01518: ldr             x2, [SP], #8
    // 0xb0151c: RestoreReg d0
    //     0xb0151c: ldr             q0, [SP], #0x10
    // 0xb01520: b               #0xb01118
    // 0xb01524: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb01524: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb01528: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb01528: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Stack <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb01550, size: 0x4b0
    // 0xb01550: EnterFrame
    //     0xb01550: stp             fp, lr, [SP, #-0x10]!
    //     0xb01554: mov             fp, SP
    // 0xb01558: AllocStack(0x68)
    //     0xb01558: sub             SP, SP, #0x68
    // 0xb0155c: SetupParameters()
    //     0xb0155c: ldr             x0, [fp, #0x20]
    //     0xb01560: ldur            w1, [x0, #0x17]
    //     0xb01564: add             x1, x1, HEAP, lsl #32
    //     0xb01568: stur            x1, [fp, #-0x18]
    // 0xb0156c: CheckStackOverflow
    //     0xb0156c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb01570: cmp             SP, x16
    //     0xb01574: b.ls            #0xb019dc
    // 0xb01578: LoadField: r0 = r1->field_f
    //     0xb01578: ldur            w0, [x1, #0xf]
    // 0xb0157c: DecompressPointer r0
    //     0xb0157c: add             x0, x0, HEAP, lsl #32
    // 0xb01580: LoadField: r2 = r0->field_1b
    //     0xb01580: ldur            x2, [x0, #0x1b]
    // 0xb01584: ldr             x3, [fp, #0x10]
    // 0xb01588: r4 = LoadInt32Instr(r3)
    //     0xb01588: sbfx            x4, x3, #1, #0x1f
    //     0xb0158c: tbz             w3, #0, #0xb01594
    //     0xb01590: ldur            x4, [x3, #7]
    // 0xb01594: stur            x4, [fp, #-0x10]
    // 0xb01598: cmp             x4, x2
    // 0xb0159c: b.ne            #0xb01808
    // 0xb015a0: LoadField: r2 = r0->field_b
    //     0xb015a0: ldur            w2, [x0, #0xb]
    // 0xb015a4: DecompressPointer r2
    //     0xb015a4: add             x2, x2, HEAP, lsl #32
    // 0xb015a8: stur            x2, [fp, #-8]
    // 0xb015ac: cmp             w2, NULL
    // 0xb015b0: b.eq            #0xb019e4
    // 0xb015b4: LoadField: r0 = r2->field_b
    //     0xb015b4: ldur            w0, [x2, #0xb]
    // 0xb015b8: DecompressPointer r0
    //     0xb015b8: add             x0, x0, HEAP, lsl #32
    // 0xb015bc: r5 = LoadClassIdInstr(r0)
    //     0xb015bc: ldur            x5, [x0, #-1]
    //     0xb015c0: ubfx            x5, x5, #0xc, #0x14
    // 0xb015c4: stp             x3, x0, [SP]
    // 0xb015c8: mov             x0, x5
    // 0xb015cc: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb015cc: sub             lr, x0, #0xb7
    //     0xb015d0: ldr             lr, [x21, lr, lsl #3]
    //     0xb015d4: blr             lr
    // 0xb015d8: cmp             w0, NULL
    // 0xb015dc: b.ne            #0xb015e8
    // 0xb015e0: r2 = Null
    //     0xb015e0: mov             x2, NULL
    // 0xb015e4: b               #0xb0160c
    // 0xb015e8: LoadField: r1 = r0->field_eb
    //     0xb015e8: ldur            w1, [x0, #0xeb]
    // 0xb015ec: DecompressPointer r1
    //     0xb015ec: add             x1, x1, HEAP, lsl #32
    // 0xb015f0: cmp             w1, NULL
    // 0xb015f4: b.ne            #0xb01600
    // 0xb015f8: r0 = Null
    //     0xb015f8: mov             x0, NULL
    // 0xb015fc: b               #0xb01608
    // 0xb01600: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb01600: ldur            w0, [x1, #0x17]
    // 0xb01604: DecompressPointer r0
    //     0xb01604: add             x0, x0, HEAP, lsl #32
    // 0xb01608: mov             x2, x0
    // 0xb0160c: ldur            x1, [fp, #-0x18]
    // 0xb01610: stur            x2, [fp, #-0x20]
    // 0xb01614: LoadField: r0 = r1->field_f
    //     0xb01614: ldur            w0, [x1, #0xf]
    // 0xb01618: DecompressPointer r0
    //     0xb01618: add             x0, x0, HEAP, lsl #32
    // 0xb0161c: LoadField: r3 = r0->field_b
    //     0xb0161c: ldur            w3, [x0, #0xb]
    // 0xb01620: DecompressPointer r3
    //     0xb01620: add             x3, x3, HEAP, lsl #32
    // 0xb01624: cmp             w3, NULL
    // 0xb01628: b.eq            #0xb019e8
    // 0xb0162c: LoadField: r0 = r3->field_b
    //     0xb0162c: ldur            w0, [x3, #0xb]
    // 0xb01630: DecompressPointer r0
    //     0xb01630: add             x0, x0, HEAP, lsl #32
    // 0xb01634: r3 = LoadClassIdInstr(r0)
    //     0xb01634: ldur            x3, [x0, #-1]
    //     0xb01638: ubfx            x3, x3, #0xc, #0x14
    // 0xb0163c: ldr             x16, [fp, #0x10]
    // 0xb01640: stp             x16, x0, [SP]
    // 0xb01644: mov             x0, x3
    // 0xb01648: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb01648: sub             lr, x0, #0xb7
    //     0xb0164c: ldr             lr, [x21, lr, lsl #3]
    //     0xb01650: blr             lr
    // 0xb01654: cmp             w0, NULL
    // 0xb01658: b.ne            #0xb01664
    // 0xb0165c: r2 = Null
    //     0xb0165c: mov             x2, NULL
    // 0xb01660: b               #0xb01688
    // 0xb01664: LoadField: r1 = r0->field_eb
    //     0xb01664: ldur            w1, [x0, #0xeb]
    // 0xb01668: DecompressPointer r1
    //     0xb01668: add             x1, x1, HEAP, lsl #32
    // 0xb0166c: cmp             w1, NULL
    // 0xb01670: b.ne            #0xb0167c
    // 0xb01674: r0 = Null
    //     0xb01674: mov             x0, NULL
    // 0xb01678: b               #0xb01684
    // 0xb0167c: LoadField: r0 = r1->field_23
    //     0xb0167c: ldur            w0, [x1, #0x23]
    // 0xb01680: DecompressPointer r0
    //     0xb01680: add             x0, x0, HEAP, lsl #32
    // 0xb01684: mov             x2, x0
    // 0xb01688: ldur            x1, [fp, #-0x18]
    // 0xb0168c: stur            x2, [fp, #-0x28]
    // 0xb01690: LoadField: r0 = r1->field_f
    //     0xb01690: ldur            w0, [x1, #0xf]
    // 0xb01694: DecompressPointer r0
    //     0xb01694: add             x0, x0, HEAP, lsl #32
    // 0xb01698: LoadField: r3 = r0->field_b
    //     0xb01698: ldur            w3, [x0, #0xb]
    // 0xb0169c: DecompressPointer r3
    //     0xb0169c: add             x3, x3, HEAP, lsl #32
    // 0xb016a0: cmp             w3, NULL
    // 0xb016a4: b.eq            #0xb019ec
    // 0xb016a8: LoadField: r0 = r3->field_b
    //     0xb016a8: ldur            w0, [x3, #0xb]
    // 0xb016ac: DecompressPointer r0
    //     0xb016ac: add             x0, x0, HEAP, lsl #32
    // 0xb016b0: r3 = LoadClassIdInstr(r0)
    //     0xb016b0: ldur            x3, [x0, #-1]
    //     0xb016b4: ubfx            x3, x3, #0xc, #0x14
    // 0xb016b8: ldr             x16, [fp, #0x10]
    // 0xb016bc: stp             x16, x0, [SP]
    // 0xb016c0: mov             x0, x3
    // 0xb016c4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb016c4: sub             lr, x0, #0xb7
    //     0xb016c8: ldr             lr, [x21, lr, lsl #3]
    //     0xb016cc: blr             lr
    // 0xb016d0: cmp             w0, NULL
    // 0xb016d4: b.ne            #0xb016e0
    // 0xb016d8: r2 = Null
    //     0xb016d8: mov             x2, NULL
    // 0xb016dc: b               #0xb016ec
    // 0xb016e0: LoadField: r1 = r0->field_47
    //     0xb016e0: ldur            w1, [x0, #0x47]
    // 0xb016e4: DecompressPointer r1
    //     0xb016e4: add             x1, x1, HEAP, lsl #32
    // 0xb016e8: mov             x2, x1
    // 0xb016ec: ldur            x1, [fp, #-0x18]
    // 0xb016f0: stur            x2, [fp, #-0x30]
    // 0xb016f4: LoadField: r0 = r1->field_f
    //     0xb016f4: ldur            w0, [x1, #0xf]
    // 0xb016f8: DecompressPointer r0
    //     0xb016f8: add             x0, x0, HEAP, lsl #32
    // 0xb016fc: LoadField: r3 = r0->field_b
    //     0xb016fc: ldur            w3, [x0, #0xb]
    // 0xb01700: DecompressPointer r3
    //     0xb01700: add             x3, x3, HEAP, lsl #32
    // 0xb01704: cmp             w3, NULL
    // 0xb01708: b.eq            #0xb019f0
    // 0xb0170c: LoadField: r0 = r3->field_b
    //     0xb0170c: ldur            w0, [x3, #0xb]
    // 0xb01710: DecompressPointer r0
    //     0xb01710: add             x0, x0, HEAP, lsl #32
    // 0xb01714: r3 = LoadClassIdInstr(r0)
    //     0xb01714: ldur            x3, [x0, #-1]
    //     0xb01718: ubfx            x3, x3, #0xc, #0x14
    // 0xb0171c: ldr             x16, [fp, #0x10]
    // 0xb01720: stp             x16, x0, [SP]
    // 0xb01724: mov             x0, x3
    // 0xb01728: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb01728: sub             lr, x0, #0xb7
    //     0xb0172c: ldr             lr, [x21, lr, lsl #3]
    //     0xb01730: blr             lr
    // 0xb01734: cmp             w0, NULL
    // 0xb01738: b.ne            #0xb01744
    // 0xb0173c: r2 = Null
    //     0xb0173c: mov             x2, NULL
    // 0xb01740: b               #0xb01768
    // 0xb01744: LoadField: r1 = r0->field_eb
    //     0xb01744: ldur            w1, [x0, #0xeb]
    // 0xb01748: DecompressPointer r1
    //     0xb01748: add             x1, x1, HEAP, lsl #32
    // 0xb0174c: cmp             w1, NULL
    // 0xb01750: b.ne            #0xb0175c
    // 0xb01754: r0 = Null
    //     0xb01754: mov             x0, NULL
    // 0xb01758: b               #0xb01764
    // 0xb0175c: LoadField: r0 = r1->field_13
    //     0xb0175c: ldur            w0, [x1, #0x13]
    // 0xb01760: DecompressPointer r0
    //     0xb01760: add             x0, x0, HEAP, lsl #32
    // 0xb01764: mov             x2, x0
    // 0xb01768: ldur            x1, [fp, #-0x18]
    // 0xb0176c: stur            x2, [fp, #-0x38]
    // 0xb01770: LoadField: r0 = r1->field_f
    //     0xb01770: ldur            w0, [x1, #0xf]
    // 0xb01774: DecompressPointer r0
    //     0xb01774: add             x0, x0, HEAP, lsl #32
    // 0xb01778: LoadField: r3 = r0->field_b
    //     0xb01778: ldur            w3, [x0, #0xb]
    // 0xb0177c: DecompressPointer r3
    //     0xb0177c: add             x3, x3, HEAP, lsl #32
    // 0xb01780: cmp             w3, NULL
    // 0xb01784: b.eq            #0xb019f4
    // 0xb01788: LoadField: r0 = r3->field_b
    //     0xb01788: ldur            w0, [x3, #0xb]
    // 0xb0178c: DecompressPointer r0
    //     0xb0178c: add             x0, x0, HEAP, lsl #32
    // 0xb01790: r3 = LoadClassIdInstr(r0)
    //     0xb01790: ldur            x3, [x0, #-1]
    //     0xb01794: ubfx            x3, x3, #0xc, #0x14
    // 0xb01798: ldr             x16, [fp, #0x10]
    // 0xb0179c: stp             x16, x0, [SP]
    // 0xb017a0: mov             x0, x3
    // 0xb017a4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb017a4: sub             lr, x0, #0xb7
    //     0xb017a8: ldr             lr, [x21, lr, lsl #3]
    //     0xb017ac: blr             lr
    // 0xb017b0: cmp             w0, NULL
    // 0xb017b4: b.ne            #0xb017c0
    // 0xb017b8: r1 = Null
    //     0xb017b8: mov             x1, NULL
    // 0xb017bc: b               #0xb017c8
    // 0xb017c0: LoadField: r1 = r0->field_e3
    //     0xb017c0: ldur            w1, [x0, #0xe3]
    // 0xb017c4: DecompressPointer r1
    //     0xb017c4: add             x1, x1, HEAP, lsl #32
    // 0xb017c8: ldur            x0, [fp, #-8]
    // 0xb017cc: LoadField: r2 = r0->field_33
    //     0xb017cc: ldur            w2, [x0, #0x33]
    // 0xb017d0: DecompressPointer r2
    //     0xb017d0: add             x2, x2, HEAP, lsl #32
    // 0xb017d4: ldur            x16, [fp, #-0x20]
    // 0xb017d8: stp             x16, x2, [SP, #0x20]
    // 0xb017dc: ldur            x16, [fp, #-0x28]
    // 0xb017e0: ldur            lr, [fp, #-0x30]
    // 0xb017e4: stp             lr, x16, [SP, #0x10]
    // 0xb017e8: ldur            x16, [fp, #-0x38]
    // 0xb017ec: stp             x1, x16, [SP]
    // 0xb017f0: r4 = 0
    //     0xb017f0: movz            x4, #0
    // 0xb017f4: ldr             x0, [SP, #0x28]
    // 0xb017f8: r16 = UnlinkedCall_0x613b5c
    //     0xb017f8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57eb0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb017fc: add             x16, x16, #0xeb0
    // 0xb01800: ldp             x5, lr, [x16]
    // 0xb01804: blr             lr
    // 0xb01808: ldur            x1, [fp, #-0x18]
    // 0xb0180c: LoadField: r2 = r1->field_f
    //     0xb0180c: ldur            w2, [x1, #0xf]
    // 0xb01810: DecompressPointer r2
    //     0xb01810: add             x2, x2, HEAP, lsl #32
    // 0xb01814: stur            x2, [fp, #-8]
    // 0xb01818: LoadField: r0 = r2->field_b
    //     0xb01818: ldur            w0, [x2, #0xb]
    // 0xb0181c: DecompressPointer r0
    //     0xb0181c: add             x0, x0, HEAP, lsl #32
    // 0xb01820: cmp             w0, NULL
    // 0xb01824: b.eq            #0xb019f8
    // 0xb01828: LoadField: r3 = r0->field_b
    //     0xb01828: ldur            w3, [x0, #0xb]
    // 0xb0182c: DecompressPointer r3
    //     0xb0182c: add             x3, x3, HEAP, lsl #32
    // 0xb01830: r0 = LoadClassIdInstr(r3)
    //     0xb01830: ldur            x0, [x3, #-1]
    //     0xb01834: ubfx            x0, x0, #0xc, #0x14
    // 0xb01838: ldr             x16, [fp, #0x10]
    // 0xb0183c: stp             x16, x3, [SP]
    // 0xb01840: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb01840: sub             lr, x0, #0xb7
    //     0xb01844: ldr             lr, [x21, lr, lsl #3]
    //     0xb01848: blr             lr
    // 0xb0184c: ldur            x1, [fp, #-8]
    // 0xb01850: mov             x2, x0
    // 0xb01854: ldur            x3, [fp, #-0x10]
    // 0xb01858: r0 = cosmeticThemeSlider()
    //     0xb01858: bl              #0xb01a00  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::cosmeticThemeSlider
    // 0xb0185c: mov             x1, x0
    // 0xb01860: ldur            x0, [fp, #-0x18]
    // 0xb01864: stur            x1, [fp, #-8]
    // 0xb01868: LoadField: r2 = r0->field_f
    //     0xb01868: ldur            w2, [x0, #0xf]
    // 0xb0186c: DecompressPointer r2
    //     0xb0186c: add             x2, x2, HEAP, lsl #32
    // 0xb01870: LoadField: r0 = r2->field_b
    //     0xb01870: ldur            w0, [x2, #0xb]
    // 0xb01874: DecompressPointer r0
    //     0xb01874: add             x0, x0, HEAP, lsl #32
    // 0xb01878: cmp             w0, NULL
    // 0xb0187c: b.eq            #0xb019fc
    // 0xb01880: LoadField: r2 = r0->field_b
    //     0xb01880: ldur            w2, [x0, #0xb]
    // 0xb01884: DecompressPointer r2
    //     0xb01884: add             x2, x2, HEAP, lsl #32
    // 0xb01888: r0 = LoadClassIdInstr(r2)
    //     0xb01888: ldur            x0, [x2, #-1]
    //     0xb0188c: ubfx            x0, x0, #0xc, #0x14
    // 0xb01890: ldr             x16, [fp, #0x10]
    // 0xb01894: stp             x16, x2, [SP]
    // 0xb01898: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb01898: sub             lr, x0, #0xb7
    //     0xb0189c: ldr             lr, [x21, lr, lsl #3]
    //     0xb018a0: blr             lr
    // 0xb018a4: cmp             w0, NULL
    // 0xb018a8: b.ne            #0xb018b4
    // 0xb018ac: r0 = Null
    //     0xb018ac: mov             x0, NULL
    // 0xb018b0: b               #0xb018c4
    // 0xb018b4: r17 = 315
    //     0xb018b4: movz            x17, #0x13b
    // 0xb018b8: ldr             w1, [x0, x17]
    // 0xb018bc: DecompressPointer r1
    //     0xb018bc: add             x1, x1, HEAP, lsl #32
    // 0xb018c0: mov             x0, x1
    // 0xb018c4: cmp             w0, NULL
    // 0xb018c8: b.ne            #0xb018d4
    // 0xb018cc: r1 = false
    //     0xb018cc: add             x1, NULL, #0x30  ; false
    // 0xb018d0: b               #0xb018d8
    // 0xb018d4: mov             x1, x0
    // 0xb018d8: ldur            x0, [fp, #-8]
    // 0xb018dc: stur            x1, [fp, #-0x18]
    // 0xb018e0: r0 = SvgPicture()
    //     0xb018e0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb018e4: mov             x1, x0
    // 0xb018e8: r2 = "assets/images/free-gift-icon.svg"
    //     0xb018e8: add             x2, PP, #0x52, lsl #12  ; [pp+0x52d40] "assets/images/free-gift-icon.svg"
    //     0xb018ec: ldr             x2, [x2, #0xd40]
    // 0xb018f0: stur            x0, [fp, #-0x20]
    // 0xb018f4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb018f4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb018f8: r0 = SvgPicture.asset()
    //     0xb018f8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb018fc: r0 = Padding()
    //     0xb018fc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb01900: mov             x1, x0
    // 0xb01904: r0 = Instance_EdgeInsets
    //     0xb01904: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d48] Obj!EdgeInsets@d584c1
    //     0xb01908: ldr             x0, [x0, #0xd48]
    // 0xb0190c: stur            x1, [fp, #-0x28]
    // 0xb01910: StoreField: r1->field_f = r0
    //     0xb01910: stur            w0, [x1, #0xf]
    // 0xb01914: ldur            x0, [fp, #-0x20]
    // 0xb01918: StoreField: r1->field_b = r0
    //     0xb01918: stur            w0, [x1, #0xb]
    // 0xb0191c: r0 = Visibility()
    //     0xb0191c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb01920: mov             x3, x0
    // 0xb01924: ldur            x0, [fp, #-0x28]
    // 0xb01928: stur            x3, [fp, #-0x20]
    // 0xb0192c: StoreField: r3->field_b = r0
    //     0xb0192c: stur            w0, [x3, #0xb]
    // 0xb01930: r0 = Instance_SizedBox
    //     0xb01930: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb01934: StoreField: r3->field_f = r0
    //     0xb01934: stur            w0, [x3, #0xf]
    // 0xb01938: ldur            x0, [fp, #-0x18]
    // 0xb0193c: StoreField: r3->field_13 = r0
    //     0xb0193c: stur            w0, [x3, #0x13]
    // 0xb01940: r0 = false
    //     0xb01940: add             x0, NULL, #0x30  ; false
    // 0xb01944: ArrayStore: r3[0] = r0  ; List_4
    //     0xb01944: stur            w0, [x3, #0x17]
    // 0xb01948: StoreField: r3->field_1b = r0
    //     0xb01948: stur            w0, [x3, #0x1b]
    // 0xb0194c: StoreField: r3->field_1f = r0
    //     0xb0194c: stur            w0, [x3, #0x1f]
    // 0xb01950: StoreField: r3->field_23 = r0
    //     0xb01950: stur            w0, [x3, #0x23]
    // 0xb01954: StoreField: r3->field_27 = r0
    //     0xb01954: stur            w0, [x3, #0x27]
    // 0xb01958: StoreField: r3->field_2b = r0
    //     0xb01958: stur            w0, [x3, #0x2b]
    // 0xb0195c: r1 = Null
    //     0xb0195c: mov             x1, NULL
    // 0xb01960: r2 = 4
    //     0xb01960: movz            x2, #0x4
    // 0xb01964: r0 = AllocateArray()
    //     0xb01964: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb01968: mov             x2, x0
    // 0xb0196c: ldur            x0, [fp, #-8]
    // 0xb01970: stur            x2, [fp, #-0x18]
    // 0xb01974: StoreField: r2->field_f = r0
    //     0xb01974: stur            w0, [x2, #0xf]
    // 0xb01978: ldur            x0, [fp, #-0x20]
    // 0xb0197c: StoreField: r2->field_13 = r0
    //     0xb0197c: stur            w0, [x2, #0x13]
    // 0xb01980: r1 = <Widget>
    //     0xb01980: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb01984: r0 = AllocateGrowableArray()
    //     0xb01984: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb01988: mov             x1, x0
    // 0xb0198c: ldur            x0, [fp, #-0x18]
    // 0xb01990: stur            x1, [fp, #-8]
    // 0xb01994: StoreField: r1->field_f = r0
    //     0xb01994: stur            w0, [x1, #0xf]
    // 0xb01998: r0 = 4
    //     0xb01998: movz            x0, #0x4
    // 0xb0199c: StoreField: r1->field_b = r0
    //     0xb0199c: stur            w0, [x1, #0xb]
    // 0xb019a0: r0 = Stack()
    //     0xb019a0: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb019a4: r1 = Instance_Alignment
    //     0xb019a4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xb019a8: ldr             x1, [x1, #0x950]
    // 0xb019ac: StoreField: r0->field_f = r1
    //     0xb019ac: stur            w1, [x0, #0xf]
    // 0xb019b0: r1 = Instance_StackFit
    //     0xb019b0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb019b4: ldr             x1, [x1, #0xfa8]
    // 0xb019b8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb019b8: stur            w1, [x0, #0x17]
    // 0xb019bc: r1 = Instance_Clip
    //     0xb019bc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb019c0: ldr             x1, [x1, #0x7e0]
    // 0xb019c4: StoreField: r0->field_1b = r1
    //     0xb019c4: stur            w1, [x0, #0x1b]
    // 0xb019c8: ldur            x1, [fp, #-8]
    // 0xb019cc: StoreField: r0->field_b = r1
    //     0xb019cc: stur            w1, [x0, #0xb]
    // 0xb019d0: LeaveFrame
    //     0xb019d0: mov             SP, fp
    //     0xb019d4: ldp             fp, lr, [SP], #0x10
    // 0xb019d8: ret
    //     0xb019d8: ret             
    // 0xb019dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb019dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb019e0: b               #0xb01578
    // 0xb019e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb019e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb019e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb019e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb019ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb019ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb019f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb019f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb019f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb019f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb019f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb019f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb019fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb019fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ cosmeticThemeSlider(/* No info */) {
    // ** addr: 0xb01a00, size: 0x1614
    // 0xb01a00: EnterFrame
    //     0xb01a00: stp             fp, lr, [SP, #-0x10]!
    //     0xb01a04: mov             fp, SP
    // 0xb01a08: AllocStack(0x80)
    //     0xb01a08: sub             SP, SP, #0x80
    // 0xb01a0c: SetupParameters(_GroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb01a0c: stur            x1, [fp, #-8]
    //     0xb01a10: stur            x2, [fp, #-0x10]
    //     0xb01a14: stur            x3, [fp, #-0x18]
    // 0xb01a18: CheckStackOverflow
    //     0xb01a18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb01a1c: cmp             SP, x16
    //     0xb01a20: b.ls            #0xb02f54
    // 0xb01a24: r1 = 3
    //     0xb01a24: movz            x1, #0x3
    // 0xb01a28: r0 = AllocateContext()
    //     0xb01a28: bl              #0x16f6108  ; AllocateContextStub
    // 0xb01a2c: mov             x3, x0
    // 0xb01a30: ldur            x2, [fp, #-8]
    // 0xb01a34: stur            x3, [fp, #-0x20]
    // 0xb01a38: StoreField: r3->field_f = r2
    //     0xb01a38: stur            w2, [x3, #0xf]
    // 0xb01a3c: ldur            x4, [fp, #-0x10]
    // 0xb01a40: StoreField: r3->field_13 = r4
    //     0xb01a40: stur            w4, [x3, #0x13]
    // 0xb01a44: ldur            x5, [fp, #-0x18]
    // 0xb01a48: r0 = BoxInt64Instr(r5)
    //     0xb01a48: sbfiz           x0, x5, #1, #0x1f
    //     0xb01a4c: cmp             x5, x0, asr #1
    //     0xb01a50: b.eq            #0xb01a5c
    //     0xb01a54: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb01a58: stur            x5, [x0, #7]
    // 0xb01a5c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb01a5c: stur            w0, [x3, #0x17]
    // 0xb01a60: r0 = Radius()
    //     0xb01a60: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb01a64: d0 = 12.000000
    //     0xb01a64: fmov            d0, #12.00000000
    // 0xb01a68: stur            x0, [fp, #-0x28]
    // 0xb01a6c: StoreField: r0->field_7 = d0
    //     0xb01a6c: stur            d0, [x0, #7]
    // 0xb01a70: StoreField: r0->field_f = d0
    //     0xb01a70: stur            d0, [x0, #0xf]
    // 0xb01a74: r0 = BorderRadius()
    //     0xb01a74: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb01a78: mov             x1, x0
    // 0xb01a7c: ldur            x0, [fp, #-0x28]
    // 0xb01a80: stur            x1, [fp, #-0x30]
    // 0xb01a84: StoreField: r1->field_7 = r0
    //     0xb01a84: stur            w0, [x1, #7]
    // 0xb01a88: StoreField: r1->field_b = r0
    //     0xb01a88: stur            w0, [x1, #0xb]
    // 0xb01a8c: StoreField: r1->field_f = r0
    //     0xb01a8c: stur            w0, [x1, #0xf]
    // 0xb01a90: StoreField: r1->field_13 = r0
    //     0xb01a90: stur            w0, [x1, #0x13]
    // 0xb01a94: r0 = RoundedRectangleBorder()
    //     0xb01a94: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb01a98: mov             x3, x0
    // 0xb01a9c: ldur            x0, [fp, #-0x30]
    // 0xb01aa0: stur            x3, [fp, #-0x38]
    // 0xb01aa4: StoreField: r3->field_b = r0
    //     0xb01aa4: stur            w0, [x3, #0xb]
    // 0xb01aa8: r0 = Instance_BorderSide
    //     0xb01aa8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb01aac: ldr             x0, [x0, #0xe20]
    // 0xb01ab0: StoreField: r3->field_7 = r0
    //     0xb01ab0: stur            w0, [x3, #7]
    // 0xb01ab4: ldur            x0, [fp, #-0x10]
    // 0xb01ab8: cmp             w0, NULL
    // 0xb01abc: b.eq            #0xb02f5c
    // 0xb01ac0: LoadField: r1 = r0->field_e7
    //     0xb01ac0: ldur            w1, [x0, #0xe7]
    // 0xb01ac4: DecompressPointer r1
    //     0xb01ac4: add             x1, x1, HEAP, lsl #32
    // 0xb01ac8: cmp             w1, NULL
    // 0xb01acc: b.eq            #0xb02f60
    // 0xb01ad0: LoadField: r0 = r1->field_b
    //     0xb01ad0: ldur            w0, [x1, #0xb]
    // 0xb01ad4: ldur            x4, [fp, #-8]
    // 0xb01ad8: stur            x0, [fp, #-0x28]
    // 0xb01adc: ArrayLoad: r5 = r4[0]  ; List_4
    //     0xb01adc: ldur            w5, [x4, #0x17]
    // 0xb01ae0: DecompressPointer r5
    //     0xb01ae0: add             x5, x5, HEAP, lsl #32
    // 0xb01ae4: r16 = Sentinel
    //     0xb01ae4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb01ae8: cmp             w5, w16
    // 0xb01aec: b.eq            #0xb02f64
    // 0xb01af0: ldur            x2, [fp, #-0x20]
    // 0xb01af4: stur            x5, [fp, #-0x10]
    // 0xb01af8: r1 = Function '<anonymous closure>':.
    //     0xb01af8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57ec0] AnonymousClosure: (0xa75c08), in [package:customer_app/app/presentation/views/line/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::lineThemeSlider (0xa75c38)
    //     0xb01afc: ldr             x1, [x1, #0xec0]
    // 0xb01b00: r0 = AllocateClosure()
    //     0xb01b00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb01b04: ldur            x2, [fp, #-0x20]
    // 0xb01b08: r1 = Function '<anonymous closure>':.
    //     0xb01b08: add             x1, PP, #0x57, lsl #12  ; [pp+0x57ec8] AnonymousClosure: (0xb03124), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::cosmeticThemeSlider (0xb01a00)
    //     0xb01b0c: ldr             x1, [x1, #0xec8]
    // 0xb01b10: stur            x0, [fp, #-0x30]
    // 0xb01b14: r0 = AllocateClosure()
    //     0xb01b14: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb01b18: stur            x0, [fp, #-0x40]
    // 0xb01b1c: r0 = PageView()
    //     0xb01b1c: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb01b20: stur            x0, [fp, #-0x48]
    // 0xb01b24: ldur            x16, [fp, #-0x10]
    // 0xb01b28: str             x16, [SP]
    // 0xb01b2c: mov             x1, x0
    // 0xb01b30: ldur            x2, [fp, #-0x40]
    // 0xb01b34: ldur            x3, [fp, #-0x28]
    // 0xb01b38: ldur            x5, [fp, #-0x30]
    // 0xb01b3c: r4 = const [0, 0x5, 0x1, 0x4, controller, 0x4, null]
    //     0xb01b3c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d60] List(7) [0, 0x5, 0x1, 0x4, "controller", 0x4, Null]
    //     0xb01b40: ldr             x4, [x4, #0xd60]
    // 0xb01b44: r0 = PageView.builder()
    //     0xb01b44: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb01b48: r0 = AspectRatio()
    //     0xb01b48: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb01b4c: d0 = 1.000000
    //     0xb01b4c: fmov            d0, #1.00000000
    // 0xb01b50: stur            x0, [fp, #-0x10]
    // 0xb01b54: StoreField: r0->field_f = d0
    //     0xb01b54: stur            d0, [x0, #0xf]
    // 0xb01b58: ldur            x1, [fp, #-0x48]
    // 0xb01b5c: StoreField: r0->field_b = r1
    //     0xb01b5c: stur            w1, [x0, #0xb]
    // 0xb01b60: r0 = Card()
    //     0xb01b60: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb01b64: mov             x2, x0
    // 0xb01b68: r0 = 0.000000
    //     0xb01b68: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb01b6c: stur            x2, [fp, #-0x28]
    // 0xb01b70: ArrayStore: r2[0] = r0  ; List_4
    //     0xb01b70: stur            w0, [x2, #0x17]
    // 0xb01b74: ldur            x0, [fp, #-0x38]
    // 0xb01b78: StoreField: r2->field_1b = r0
    //     0xb01b78: stur            w0, [x2, #0x1b]
    // 0xb01b7c: r0 = true
    //     0xb01b7c: add             x0, NULL, #0x20  ; true
    // 0xb01b80: StoreField: r2->field_1f = r0
    //     0xb01b80: stur            w0, [x2, #0x1f]
    // 0xb01b84: ldur            x1, [fp, #-0x10]
    // 0xb01b88: StoreField: r2->field_2f = r1
    //     0xb01b88: stur            w1, [x2, #0x2f]
    // 0xb01b8c: StoreField: r2->field_2b = r0
    //     0xb01b8c: stur            w0, [x2, #0x2b]
    // 0xb01b90: r1 = Instance__CardVariant
    //     0xb01b90: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb01b94: ldr             x1, [x1, #0xa68]
    // 0xb01b98: StoreField: r2->field_33 = r1
    //     0xb01b98: stur            w1, [x2, #0x33]
    // 0xb01b9c: ldur            x3, [fp, #-0x20]
    // 0xb01ba0: LoadField: r1 = r3->field_13
    //     0xb01ba0: ldur            w1, [x3, #0x13]
    // 0xb01ba4: DecompressPointer r1
    //     0xb01ba4: add             x1, x1, HEAP, lsl #32
    // 0xb01ba8: cmp             w1, NULL
    // 0xb01bac: b.eq            #0xb02f70
    // 0xb01bb0: LoadField: r4 = r1->field_53
    //     0xb01bb0: ldur            w4, [x1, #0x53]
    // 0xb01bb4: DecompressPointer r4
    //     0xb01bb4: add             x4, x4, HEAP, lsl #32
    // 0xb01bb8: cmp             w4, NULL
    // 0xb01bbc: b.ne            #0xb01bc8
    // 0xb01bc0: r1 = Null
    //     0xb01bc0: mov             x1, NULL
    // 0xb01bc4: b               #0xb01bd0
    // 0xb01bc8: LoadField: r1 = r4->field_b
    //     0xb01bc8: ldur            w1, [x4, #0xb]
    // 0xb01bcc: DecompressPointer r1
    //     0xb01bcc: add             x1, x1, HEAP, lsl #32
    // 0xb01bd0: cmp             w1, NULL
    // 0xb01bd4: b.ne            #0xb01bdc
    // 0xb01bd8: r1 = ""
    //     0xb01bd8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb01bdc: ldur            x4, [fp, #-8]
    // 0xb01be0: r0 = capitalizeFirstWord()
    //     0xb01be0: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb01be4: mov             x2, x0
    // 0xb01be8: ldur            x0, [fp, #-8]
    // 0xb01bec: stur            x2, [fp, #-0x10]
    // 0xb01bf0: LoadField: r1 = r0->field_f
    //     0xb01bf0: ldur            w1, [x0, #0xf]
    // 0xb01bf4: DecompressPointer r1
    //     0xb01bf4: add             x1, x1, HEAP, lsl #32
    // 0xb01bf8: cmp             w1, NULL
    // 0xb01bfc: b.eq            #0xb02f74
    // 0xb01c00: r0 = of()
    //     0xb01c00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb01c04: LoadField: r1 = r0->field_87
    //     0xb01c04: ldur            w1, [x0, #0x87]
    // 0xb01c08: DecompressPointer r1
    //     0xb01c08: add             x1, x1, HEAP, lsl #32
    // 0xb01c0c: LoadField: r0 = r1->field_2b
    //     0xb01c0c: ldur            w0, [x1, #0x2b]
    // 0xb01c10: DecompressPointer r0
    //     0xb01c10: add             x0, x0, HEAP, lsl #32
    // 0xb01c14: r16 = 14.000000
    //     0xb01c14: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb01c18: ldr             x16, [x16, #0x1d8]
    // 0xb01c1c: r30 = Instance_Color
    //     0xb01c1c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb01c20: stp             lr, x16, [SP]
    // 0xb01c24: mov             x1, x0
    // 0xb01c28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb01c28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb01c2c: ldr             x4, [x4, #0xaa0]
    // 0xb01c30: r0 = copyWith()
    //     0xb01c30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb01c34: stur            x0, [fp, #-0x30]
    // 0xb01c38: r0 = Text()
    //     0xb01c38: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb01c3c: mov             x1, x0
    // 0xb01c40: ldur            x0, [fp, #-0x10]
    // 0xb01c44: stur            x1, [fp, #-0x38]
    // 0xb01c48: StoreField: r1->field_b = r0
    //     0xb01c48: stur            w0, [x1, #0xb]
    // 0xb01c4c: ldur            x0, [fp, #-0x30]
    // 0xb01c50: StoreField: r1->field_13 = r0
    //     0xb01c50: stur            w0, [x1, #0x13]
    // 0xb01c54: r0 = Instance_TextOverflow
    //     0xb01c54: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb01c58: ldr             x0, [x0, #0xe10]
    // 0xb01c5c: StoreField: r1->field_2b = r0
    //     0xb01c5c: stur            w0, [x1, #0x2b]
    // 0xb01c60: r0 = 2
    //     0xb01c60: movz            x0, #0x2
    // 0xb01c64: StoreField: r1->field_37 = r0
    //     0xb01c64: stur            w0, [x1, #0x37]
    // 0xb01c68: r0 = Padding()
    //     0xb01c68: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb01c6c: mov             x2, x0
    // 0xb01c70: r1 = Instance_EdgeInsets
    //     0xb01c70: add             x1, PP, #0x55, lsl #12  ; [pp+0x55930] Obj!EdgeInsets@d586a1
    //     0xb01c74: ldr             x1, [x1, #0x930]
    // 0xb01c78: stur            x2, [fp, #-0x10]
    // 0xb01c7c: StoreField: r2->field_f = r1
    //     0xb01c7c: stur            w1, [x2, #0xf]
    // 0xb01c80: ldur            x0, [fp, #-0x38]
    // 0xb01c84: StoreField: r2->field_b = r0
    //     0xb01c84: stur            w0, [x2, #0xb]
    // 0xb01c88: ldur            x3, [fp, #-0x20]
    // 0xb01c8c: LoadField: r0 = r3->field_13
    //     0xb01c8c: ldur            w0, [x3, #0x13]
    // 0xb01c90: DecompressPointer r0
    //     0xb01c90: add             x0, x0, HEAP, lsl #32
    // 0xb01c94: cmp             w0, NULL
    // 0xb01c98: b.eq            #0xb02f78
    // 0xb01c9c: LoadField: r4 = r0->field_e3
    //     0xb01c9c: ldur            w4, [x0, #0xe3]
    // 0xb01ca0: DecompressPointer r4
    //     0xb01ca0: add             x4, x4, HEAP, lsl #32
    // 0xb01ca4: cmp             w4, NULL
    // 0xb01ca8: b.ne            #0xb01cb4
    // 0xb01cac: r0 = Null
    //     0xb01cac: mov             x0, NULL
    // 0xb01cb0: b               #0xb01cbc
    // 0xb01cb4: LoadField: r0 = r4->field_7
    //     0xb01cb4: ldur            w0, [x4, #7]
    // 0xb01cb8: DecompressPointer r0
    //     0xb01cb8: add             x0, x0, HEAP, lsl #32
    // 0xb01cbc: r4 = LoadClassIdInstr(r0)
    //     0xb01cbc: ldur            x4, [x0, #-1]
    //     0xb01cc0: ubfx            x4, x4, #0xc, #0x14
    // 0xb01cc4: r16 = 0.000000
    //     0xb01cc4: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb01cc8: stp             x16, x0, [SP]
    // 0xb01ccc: mov             x0, x4
    // 0xb01cd0: mov             lr, x0
    // 0xb01cd4: ldr             lr, [x21, lr, lsl #3]
    // 0xb01cd8: blr             lr
    // 0xb01cdc: eor             x1, x0, #0x10
    // 0xb01ce0: ldur            x2, [fp, #-0x20]
    // 0xb01ce4: stur            x1, [fp, #-0x30]
    // 0xb01ce8: LoadField: r0 = r2->field_13
    //     0xb01ce8: ldur            w0, [x2, #0x13]
    // 0xb01cec: DecompressPointer r0
    //     0xb01cec: add             x0, x0, HEAP, lsl #32
    // 0xb01cf0: cmp             w0, NULL
    // 0xb01cf4: b.eq            #0xb02f7c
    // 0xb01cf8: LoadField: r3 = r0->field_e3
    //     0xb01cf8: ldur            w3, [x0, #0xe3]
    // 0xb01cfc: DecompressPointer r3
    //     0xb01cfc: add             x3, x3, HEAP, lsl #32
    // 0xb01d00: cmp             w3, NULL
    // 0xb01d04: b.ne            #0xb01d10
    // 0xb01d08: r0 = Null
    //     0xb01d08: mov             x0, NULL
    // 0xb01d0c: b               #0xb01d18
    // 0xb01d10: LoadField: r0 = r3->field_f
    //     0xb01d10: ldur            w0, [x3, #0xf]
    // 0xb01d14: DecompressPointer r0
    //     0xb01d14: add             x0, x0, HEAP, lsl #32
    // 0xb01d18: r3 = LoadClassIdInstr(r0)
    //     0xb01d18: ldur            x3, [x0, #-1]
    //     0xb01d1c: ubfx            x3, x3, #0xc, #0x14
    // 0xb01d20: r16 = "product_rating"
    //     0xb01d20: add             x16, PP, #0x23, lsl #12  ; [pp+0x23f20] "product_rating"
    //     0xb01d24: ldr             x16, [x16, #0xf20]
    // 0xb01d28: stp             x16, x0, [SP]
    // 0xb01d2c: mov             x0, x3
    // 0xb01d30: mov             lr, x0
    // 0xb01d34: ldr             lr, [x21, lr, lsl #3]
    // 0xb01d38: blr             lr
    // 0xb01d3c: tbnz            w0, #4, #0xb02228
    // 0xb01d40: ldur            x2, [fp, #-0x20]
    // 0xb01d44: LoadField: r0 = r2->field_13
    //     0xb01d44: ldur            w0, [x2, #0x13]
    // 0xb01d48: DecompressPointer r0
    //     0xb01d48: add             x0, x0, HEAP, lsl #32
    // 0xb01d4c: cmp             w0, NULL
    // 0xb01d50: b.eq            #0xb02f80
    // 0xb01d54: LoadField: r1 = r0->field_e3
    //     0xb01d54: ldur            w1, [x0, #0xe3]
    // 0xb01d58: DecompressPointer r1
    //     0xb01d58: add             x1, x1, HEAP, lsl #32
    // 0xb01d5c: cmp             w1, NULL
    // 0xb01d60: b.ne            #0xb01d6c
    // 0xb01d64: r0 = Null
    //     0xb01d64: mov             x0, NULL
    // 0xb01d68: b               #0xb01d74
    // 0xb01d6c: LoadField: r0 = r1->field_7
    //     0xb01d6c: ldur            w0, [x1, #7]
    // 0xb01d70: DecompressPointer r0
    //     0xb01d70: add             x0, x0, HEAP, lsl #32
    // 0xb01d74: cmp             w0, NULL
    // 0xb01d78: r16 = true
    //     0xb01d78: add             x16, NULL, #0x20  ; true
    // 0xb01d7c: r17 = false
    //     0xb01d7c: add             x17, NULL, #0x30  ; false
    // 0xb01d80: csel            x3, x16, x17, ne
    // 0xb01d84: stur            x3, [fp, #-0x38]
    // 0xb01d88: cmp             w1, NULL
    // 0xb01d8c: b.ne            #0xb01d98
    // 0xb01d90: r0 = Null
    //     0xb01d90: mov             x0, NULL
    // 0xb01d94: b               #0xb01da0
    // 0xb01d98: LoadField: r0 = r1->field_7
    //     0xb01d98: ldur            w0, [x1, #7]
    // 0xb01d9c: DecompressPointer r0
    //     0xb01d9c: add             x0, x0, HEAP, lsl #32
    // 0xb01da0: cmp             w0, NULL
    // 0xb01da4: b.ne            #0xb01db0
    // 0xb01da8: d1 = 0.000000
    //     0xb01da8: eor             v1.16b, v1.16b, v1.16b
    // 0xb01dac: b               #0xb01db8
    // 0xb01db0: LoadField: d0 = r0->field_7
    //     0xb01db0: ldur            d0, [x0, #7]
    // 0xb01db4: mov             v1.16b, v0.16b
    // 0xb01db8: d0 = 4.000000
    //     0xb01db8: fmov            d0, #4.00000000
    // 0xb01dbc: fcmp            d1, d0
    // 0xb01dc0: b.lt            #0xb01dd0
    // 0xb01dc4: r0 = Instance_Color
    //     0xb01dc4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb01dc8: ldr             x0, [x0, #0x858]
    // 0xb01dcc: b               #0xb01e78
    // 0xb01dd0: cmp             w1, NULL
    // 0xb01dd4: b.ne            #0xb01de0
    // 0xb01dd8: r0 = Null
    //     0xb01dd8: mov             x0, NULL
    // 0xb01ddc: b               #0xb01de8
    // 0xb01de0: LoadField: r0 = r1->field_7
    //     0xb01de0: ldur            w0, [x1, #7]
    // 0xb01de4: DecompressPointer r0
    //     0xb01de4: add             x0, x0, HEAP, lsl #32
    // 0xb01de8: cmp             w0, NULL
    // 0xb01dec: b.ne            #0xb01df8
    // 0xb01df0: d1 = 0.000000
    //     0xb01df0: eor             v1.16b, v1.16b, v1.16b
    // 0xb01df4: b               #0xb01e00
    // 0xb01df8: LoadField: d0 = r0->field_7
    //     0xb01df8: ldur            d0, [x0, #7]
    // 0xb01dfc: mov             v1.16b, v0.16b
    // 0xb01e00: d0 = 3.500000
    //     0xb01e00: fmov            d0, #3.50000000
    // 0xb01e04: fcmp            d1, d0
    // 0xb01e08: b.lt            #0xb01e24
    // 0xb01e0c: r1 = Instance_Color
    //     0xb01e0c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb01e10: ldr             x1, [x1, #0x858]
    // 0xb01e14: d0 = 0.700000
    //     0xb01e14: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb01e18: ldr             d0, [x17, #0xf48]
    // 0xb01e1c: r0 = withOpacity()
    //     0xb01e1c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb01e20: b               #0xb01e74
    // 0xb01e24: cmp             w1, NULL
    // 0xb01e28: b.ne            #0xb01e34
    // 0xb01e2c: r0 = Null
    //     0xb01e2c: mov             x0, NULL
    // 0xb01e30: b               #0xb01e3c
    // 0xb01e34: LoadField: r0 = r1->field_7
    //     0xb01e34: ldur            w0, [x1, #7]
    // 0xb01e38: DecompressPointer r0
    //     0xb01e38: add             x0, x0, HEAP, lsl #32
    // 0xb01e3c: cmp             w0, NULL
    // 0xb01e40: b.ne            #0xb01e4c
    // 0xb01e44: d1 = 0.000000
    //     0xb01e44: eor             v1.16b, v1.16b, v1.16b
    // 0xb01e48: b               #0xb01e54
    // 0xb01e4c: LoadField: d0 = r0->field_7
    //     0xb01e4c: ldur            d0, [x0, #7]
    // 0xb01e50: mov             v1.16b, v0.16b
    // 0xb01e54: d0 = 2.000000
    //     0xb01e54: fmov            d0, #2.00000000
    // 0xb01e58: fcmp            d1, d0
    // 0xb01e5c: b.lt            #0xb01e6c
    // 0xb01e60: r0 = Instance_Color
    //     0xb01e60: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb01e64: ldr             x0, [x0, #0x860]
    // 0xb01e68: b               #0xb01e74
    // 0xb01e6c: r0 = Instance_Color
    //     0xb01e6c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb01e70: ldr             x0, [x0, #0x50]
    // 0xb01e74: ldur            x2, [fp, #-0x20]
    // 0xb01e78: stur            x0, [fp, #-0x40]
    // 0xb01e7c: r0 = ColorFilter()
    //     0xb01e7c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb01e80: mov             x1, x0
    // 0xb01e84: ldur            x0, [fp, #-0x40]
    // 0xb01e88: stur            x1, [fp, #-0x48]
    // 0xb01e8c: StoreField: r1->field_7 = r0
    //     0xb01e8c: stur            w0, [x1, #7]
    // 0xb01e90: r0 = Instance_BlendMode
    //     0xb01e90: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb01e94: ldr             x0, [x0, #0xb30]
    // 0xb01e98: StoreField: r1->field_b = r0
    //     0xb01e98: stur            w0, [x1, #0xb]
    // 0xb01e9c: r2 = 1
    //     0xb01e9c: movz            x2, #0x1
    // 0xb01ea0: StoreField: r1->field_13 = r2
    //     0xb01ea0: stur            x2, [x1, #0x13]
    // 0xb01ea4: r0 = SvgPicture()
    //     0xb01ea4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb01ea8: stur            x0, [fp, #-0x40]
    // 0xb01eac: ldur            x16, [fp, #-0x48]
    // 0xb01eb0: str             x16, [SP]
    // 0xb01eb4: mov             x1, x0
    // 0xb01eb8: r2 = "assets/images/green_star.svg"
    //     0xb01eb8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb01ebc: ldr             x2, [x2, #0x9a0]
    // 0xb01ec0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb01ec0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb01ec4: ldr             x4, [x4, #0xa38]
    // 0xb01ec8: r0 = SvgPicture.asset()
    //     0xb01ec8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb01ecc: ldur            x0, [fp, #-0x20]
    // 0xb01ed0: LoadField: r1 = r0->field_13
    //     0xb01ed0: ldur            w1, [x0, #0x13]
    // 0xb01ed4: DecompressPointer r1
    //     0xb01ed4: add             x1, x1, HEAP, lsl #32
    // 0xb01ed8: cmp             w1, NULL
    // 0xb01edc: b.eq            #0xb02f84
    // 0xb01ee0: LoadField: r2 = r1->field_e3
    //     0xb01ee0: ldur            w2, [x1, #0xe3]
    // 0xb01ee4: DecompressPointer r2
    //     0xb01ee4: add             x2, x2, HEAP, lsl #32
    // 0xb01ee8: cmp             w2, NULL
    // 0xb01eec: b.ne            #0xb01ef8
    // 0xb01ef0: r0 = Null
    //     0xb01ef0: mov             x0, NULL
    // 0xb01ef4: b               #0xb01f18
    // 0xb01ef8: LoadField: r1 = r2->field_7
    //     0xb01ef8: ldur            w1, [x2, #7]
    // 0xb01efc: DecompressPointer r1
    //     0xb01efc: add             x1, x1, HEAP, lsl #32
    // 0xb01f00: cmp             w1, NULL
    // 0xb01f04: b.ne            #0xb01f10
    // 0xb01f08: r0 = Null
    //     0xb01f08: mov             x0, NULL
    // 0xb01f0c: b               #0xb01f18
    // 0xb01f10: r2 = 1
    //     0xb01f10: movz            x2, #0x1
    // 0xb01f14: r0 = toStringAsFixed()
    //     0xb01f14: bl              #0x7c1ef8  ; [dart:core] _Double::toStringAsFixed
    // 0xb01f18: cmp             w0, NULL
    // 0xb01f1c: b.ne            #0xb01f28
    // 0xb01f20: r4 = ""
    //     0xb01f20: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb01f24: b               #0xb01f2c
    // 0xb01f28: mov             x4, x0
    // 0xb01f2c: ldur            x3, [fp, #-8]
    // 0xb01f30: ldur            x2, [fp, #-0x20]
    // 0xb01f34: ldur            x0, [fp, #-0x40]
    // 0xb01f38: stur            x4, [fp, #-0x48]
    // 0xb01f3c: LoadField: r1 = r3->field_f
    //     0xb01f3c: ldur            w1, [x3, #0xf]
    // 0xb01f40: DecompressPointer r1
    //     0xb01f40: add             x1, x1, HEAP, lsl #32
    // 0xb01f44: cmp             w1, NULL
    // 0xb01f48: b.eq            #0xb02f88
    // 0xb01f4c: r0 = of()
    //     0xb01f4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb01f50: LoadField: r1 = r0->field_87
    //     0xb01f50: ldur            w1, [x0, #0x87]
    // 0xb01f54: DecompressPointer r1
    //     0xb01f54: add             x1, x1, HEAP, lsl #32
    // 0xb01f58: LoadField: r0 = r1->field_7
    //     0xb01f58: ldur            w0, [x1, #7]
    // 0xb01f5c: DecompressPointer r0
    //     0xb01f5c: add             x0, x0, HEAP, lsl #32
    // 0xb01f60: r16 = 12.000000
    //     0xb01f60: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb01f64: ldr             x16, [x16, #0x9e8]
    // 0xb01f68: r30 = Instance_Color
    //     0xb01f68: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb01f6c: stp             lr, x16, [SP]
    // 0xb01f70: mov             x1, x0
    // 0xb01f74: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb01f74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb01f78: ldr             x4, [x4, #0xaa0]
    // 0xb01f7c: r0 = copyWith()
    //     0xb01f7c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb01f80: stur            x0, [fp, #-0x50]
    // 0xb01f84: r0 = Text()
    //     0xb01f84: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb01f88: mov             x3, x0
    // 0xb01f8c: ldur            x0, [fp, #-0x48]
    // 0xb01f90: stur            x3, [fp, #-0x58]
    // 0xb01f94: StoreField: r3->field_b = r0
    //     0xb01f94: stur            w0, [x3, #0xb]
    // 0xb01f98: ldur            x0, [fp, #-0x50]
    // 0xb01f9c: StoreField: r3->field_13 = r0
    //     0xb01f9c: stur            w0, [x3, #0x13]
    // 0xb01fa0: r1 = Null
    //     0xb01fa0: mov             x1, NULL
    // 0xb01fa4: r2 = 4
    //     0xb01fa4: movz            x2, #0x4
    // 0xb01fa8: r0 = AllocateArray()
    //     0xb01fa8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb01fac: mov             x2, x0
    // 0xb01fb0: ldur            x0, [fp, #-0x40]
    // 0xb01fb4: stur            x2, [fp, #-0x48]
    // 0xb01fb8: StoreField: r2->field_f = r0
    //     0xb01fb8: stur            w0, [x2, #0xf]
    // 0xb01fbc: ldur            x0, [fp, #-0x58]
    // 0xb01fc0: StoreField: r2->field_13 = r0
    //     0xb01fc0: stur            w0, [x2, #0x13]
    // 0xb01fc4: r1 = <Widget>
    //     0xb01fc4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb01fc8: r0 = AllocateGrowableArray()
    //     0xb01fc8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb01fcc: mov             x3, x0
    // 0xb01fd0: ldur            x0, [fp, #-0x48]
    // 0xb01fd4: stur            x3, [fp, #-0x50]
    // 0xb01fd8: StoreField: r3->field_f = r0
    //     0xb01fd8: stur            w0, [x3, #0xf]
    // 0xb01fdc: r0 = 4
    //     0xb01fdc: movz            x0, #0x4
    // 0xb01fe0: StoreField: r3->field_b = r0
    //     0xb01fe0: stur            w0, [x3, #0xb]
    // 0xb01fe4: ldur            x4, [fp, #-0x20]
    // 0xb01fe8: LoadField: r1 = r4->field_13
    //     0xb01fe8: ldur            w1, [x4, #0x13]
    // 0xb01fec: DecompressPointer r1
    //     0xb01fec: add             x1, x1, HEAP, lsl #32
    // 0xb01ff0: cmp             w1, NULL
    // 0xb01ff4: b.eq            #0xb02f8c
    // 0xb01ff8: LoadField: r2 = r1->field_e3
    //     0xb01ff8: ldur            w2, [x1, #0xe3]
    // 0xb01ffc: DecompressPointer r2
    //     0xb01ffc: add             x2, x2, HEAP, lsl #32
    // 0xb02000: cmp             w2, NULL
    // 0xb02004: b.ne            #0xb02010
    // 0xb02008: mov             x2, x3
    // 0xb0200c: b               #0xb02180
    // 0xb02010: LoadField: r5 = r2->field_b
    //     0xb02010: ldur            w5, [x2, #0xb]
    // 0xb02014: DecompressPointer r5
    //     0xb02014: add             x5, x5, HEAP, lsl #32
    // 0xb02018: stur            x5, [fp, #-0x40]
    // 0xb0201c: cmp             w5, NULL
    // 0xb02020: b.eq            #0xb0217c
    // 0xb02024: r1 = Null
    //     0xb02024: mov             x1, NULL
    // 0xb02028: r2 = 6
    //     0xb02028: movz            x2, #0x6
    // 0xb0202c: r0 = AllocateArray()
    //     0xb0202c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb02030: r16 = " | ("
    //     0xb02030: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d70] " | ("
    //     0xb02034: ldr             x16, [x16, #0xd70]
    // 0xb02038: StoreField: r0->field_f = r16
    //     0xb02038: stur            w16, [x0, #0xf]
    // 0xb0203c: ldur            x1, [fp, #-0x40]
    // 0xb02040: cmp             w1, NULL
    // 0xb02044: b.ne            #0xb02050
    // 0xb02048: r3 = Null
    //     0xb02048: mov             x3, NULL
    // 0xb0204c: b               #0xb02074
    // 0xb02050: LoadField: d0 = r1->field_7
    //     0xb02050: ldur            d0, [x1, #7]
    // 0xb02054: fcmp            d0, d0
    // 0xb02058: b.vs            #0xb02f90
    // 0xb0205c: fcvtzs          x1, d0
    // 0xb02060: asr             x16, x1, #0x1e
    // 0xb02064: cmp             x16, x1, asr #63
    // 0xb02068: b.ne            #0xb02f90
    // 0xb0206c: lsl             x1, x1, #1
    // 0xb02070: mov             x3, x1
    // 0xb02074: ldur            x2, [fp, #-8]
    // 0xb02078: ldur            x1, [fp, #-0x50]
    // 0xb0207c: StoreField: r0->field_13 = r3
    //     0xb0207c: stur            w3, [x0, #0x13]
    // 0xb02080: r16 = ")"
    //     0xb02080: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xb02084: ArrayStore: r0[0] = r16  ; List_4
    //     0xb02084: stur            w16, [x0, #0x17]
    // 0xb02088: str             x0, [SP]
    // 0xb0208c: r0 = _interpolate()
    //     0xb0208c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb02090: mov             x2, x0
    // 0xb02094: ldur            x0, [fp, #-8]
    // 0xb02098: stur            x2, [fp, #-0x40]
    // 0xb0209c: LoadField: r1 = r0->field_f
    //     0xb0209c: ldur            w1, [x0, #0xf]
    // 0xb020a0: DecompressPointer r1
    //     0xb020a0: add             x1, x1, HEAP, lsl #32
    // 0xb020a4: cmp             w1, NULL
    // 0xb020a8: b.eq            #0xb02fb8
    // 0xb020ac: r0 = of()
    //     0xb020ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb020b0: LoadField: r1 = r0->field_87
    //     0xb020b0: ldur            w1, [x0, #0x87]
    // 0xb020b4: DecompressPointer r1
    //     0xb020b4: add             x1, x1, HEAP, lsl #32
    // 0xb020b8: LoadField: r0 = r1->field_2b
    //     0xb020b8: ldur            w0, [x1, #0x2b]
    // 0xb020bc: DecompressPointer r0
    //     0xb020bc: add             x0, x0, HEAP, lsl #32
    // 0xb020c0: r16 = 12.000000
    //     0xb020c0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb020c4: ldr             x16, [x16, #0x9e8]
    // 0xb020c8: r30 = Instance_Color
    //     0xb020c8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb020cc: stp             lr, x16, [SP]
    // 0xb020d0: mov             x1, x0
    // 0xb020d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb020d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb020d8: ldr             x4, [x4, #0xaa0]
    // 0xb020dc: r0 = copyWith()
    //     0xb020dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb020e0: stur            x0, [fp, #-0x48]
    // 0xb020e4: r0 = Text()
    //     0xb020e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb020e8: mov             x2, x0
    // 0xb020ec: ldur            x0, [fp, #-0x40]
    // 0xb020f0: stur            x2, [fp, #-0x58]
    // 0xb020f4: StoreField: r2->field_b = r0
    //     0xb020f4: stur            w0, [x2, #0xb]
    // 0xb020f8: ldur            x0, [fp, #-0x48]
    // 0xb020fc: StoreField: r2->field_13 = r0
    //     0xb020fc: stur            w0, [x2, #0x13]
    // 0xb02100: ldur            x0, [fp, #-0x50]
    // 0xb02104: LoadField: r1 = r0->field_b
    //     0xb02104: ldur            w1, [x0, #0xb]
    // 0xb02108: LoadField: r3 = r0->field_f
    //     0xb02108: ldur            w3, [x0, #0xf]
    // 0xb0210c: DecompressPointer r3
    //     0xb0210c: add             x3, x3, HEAP, lsl #32
    // 0xb02110: LoadField: r4 = r3->field_b
    //     0xb02110: ldur            w4, [x3, #0xb]
    // 0xb02114: r3 = LoadInt32Instr(r1)
    //     0xb02114: sbfx            x3, x1, #1, #0x1f
    // 0xb02118: stur            x3, [fp, #-0x18]
    // 0xb0211c: r1 = LoadInt32Instr(r4)
    //     0xb0211c: sbfx            x1, x4, #1, #0x1f
    // 0xb02120: cmp             x3, x1
    // 0xb02124: b.ne            #0xb02130
    // 0xb02128: mov             x1, x0
    // 0xb0212c: r0 = _growToNextCapacity()
    //     0xb0212c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb02130: ldur            x2, [fp, #-0x50]
    // 0xb02134: ldur            x3, [fp, #-0x18]
    // 0xb02138: add             x0, x3, #1
    // 0xb0213c: lsl             x1, x0, #1
    // 0xb02140: StoreField: r2->field_b = r1
    //     0xb02140: stur            w1, [x2, #0xb]
    // 0xb02144: LoadField: r1 = r2->field_f
    //     0xb02144: ldur            w1, [x2, #0xf]
    // 0xb02148: DecompressPointer r1
    //     0xb02148: add             x1, x1, HEAP, lsl #32
    // 0xb0214c: ldur            x0, [fp, #-0x58]
    // 0xb02150: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb02150: add             x25, x1, x3, lsl #2
    //     0xb02154: add             x25, x25, #0xf
    //     0xb02158: str             w0, [x25]
    //     0xb0215c: tbz             w0, #0, #0xb02178
    //     0xb02160: ldurb           w16, [x1, #-1]
    //     0xb02164: ldurb           w17, [x0, #-1]
    //     0xb02168: and             x16, x17, x16, lsr #2
    //     0xb0216c: tst             x16, HEAP, lsr #32
    //     0xb02170: b.eq            #0xb02178
    //     0xb02174: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb02178: b               #0xb02180
    // 0xb0217c: mov             x2, x3
    // 0xb02180: ldur            x0, [fp, #-0x38]
    // 0xb02184: r0 = Row()
    //     0xb02184: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb02188: r2 = Instance_Axis
    //     0xb02188: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb0218c: stur            x0, [fp, #-0x40]
    // 0xb02190: StoreField: r0->field_f = r2
    //     0xb02190: stur            w2, [x0, #0xf]
    // 0xb02194: r1 = Instance_MainAxisAlignment
    //     0xb02194: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb02198: ldr             x1, [x1, #0xa08]
    // 0xb0219c: StoreField: r0->field_13 = r1
    //     0xb0219c: stur            w1, [x0, #0x13]
    // 0xb021a0: r3 = Instance_MainAxisSize
    //     0xb021a0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb021a4: ldr             x3, [x3, #0xa10]
    // 0xb021a8: ArrayStore: r0[0] = r3  ; List_4
    //     0xb021a8: stur            w3, [x0, #0x17]
    // 0xb021ac: r4 = Instance_CrossAxisAlignment
    //     0xb021ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb021b0: ldr             x4, [x4, #0xa18]
    // 0xb021b4: StoreField: r0->field_1b = r4
    //     0xb021b4: stur            w4, [x0, #0x1b]
    // 0xb021b8: r2 = Instance_VerticalDirection
    //     0xb021b8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb021bc: ldr             x2, [x2, #0xa20]
    // 0xb021c0: StoreField: r0->field_23 = r2
    //     0xb021c0: stur            w2, [x0, #0x23]
    // 0xb021c4: r3 = Instance_Clip
    //     0xb021c4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb021c8: ldr             x3, [x3, #0x38]
    // 0xb021cc: StoreField: r0->field_2b = r3
    //     0xb021cc: stur            w3, [x0, #0x2b]
    // 0xb021d0: StoreField: r0->field_2f = rZR
    //     0xb021d0: stur            xzr, [x0, #0x2f]
    // 0xb021d4: ldur            x4, [fp, #-0x50]
    // 0xb021d8: StoreField: r0->field_b = r4
    //     0xb021d8: stur            w4, [x0, #0xb]
    // 0xb021dc: r0 = Visibility()
    //     0xb021dc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb021e0: mov             x1, x0
    // 0xb021e4: ldur            x0, [fp, #-0x40]
    // 0xb021e8: StoreField: r1->field_b = r0
    //     0xb021e8: stur            w0, [x1, #0xb]
    // 0xb021ec: r5 = Instance_SizedBox
    //     0xb021ec: ldr             x5, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb021f0: StoreField: r1->field_f = r5
    //     0xb021f0: stur            w5, [x1, #0xf]
    // 0xb021f4: ldur            x0, [fp, #-0x38]
    // 0xb021f8: StoreField: r1->field_13 = r0
    //     0xb021f8: stur            w0, [x1, #0x13]
    // 0xb021fc: r6 = false
    //     0xb021fc: add             x6, NULL, #0x30  ; false
    // 0xb02200: ArrayStore: r1[0] = r6  ; List_4
    //     0xb02200: stur            w6, [x1, #0x17]
    // 0xb02204: StoreField: r1->field_1b = r6
    //     0xb02204: stur            w6, [x1, #0x1b]
    // 0xb02208: StoreField: r1->field_1f = r6
    //     0xb02208: stur            w6, [x1, #0x1f]
    // 0xb0220c: StoreField: r1->field_23 = r6
    //     0xb0220c: stur            w6, [x1, #0x23]
    // 0xb02210: StoreField: r1->field_27 = r6
    //     0xb02210: stur            w6, [x1, #0x27]
    // 0xb02214: StoreField: r1->field_2b = r6
    //     0xb02214: stur            w6, [x1, #0x2b]
    // 0xb02218: mov             x0, x5
    // 0xb0221c: mov             x5, x1
    // 0xb02220: mov             x2, x6
    // 0xb02224: b               #0xb0254c
    // 0xb02228: ldur            x7, [fp, #-0x20]
    // 0xb0222c: r6 = false
    //     0xb0222c: add             x6, NULL, #0x30  ; false
    // 0xb02230: r5 = Instance_SizedBox
    //     0xb02230: ldr             x5, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb02234: r4 = Instance_CrossAxisAlignment
    //     0xb02234: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb02238: ldr             x4, [x4, #0xa18]
    // 0xb0223c: r3 = Instance_MainAxisSize
    //     0xb0223c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb02240: ldr             x3, [x3, #0xa10]
    // 0xb02244: r2 = Instance_Axis
    //     0xb02244: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb02248: r0 = Instance_BlendMode
    //     0xb02248: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb0224c: ldr             x0, [x0, #0xb30]
    // 0xb02250: LoadField: r1 = r7->field_13
    //     0xb02250: ldur            w1, [x7, #0x13]
    // 0xb02254: DecompressPointer r1
    //     0xb02254: add             x1, x1, HEAP, lsl #32
    // 0xb02258: cmp             w1, NULL
    // 0xb0225c: b.eq            #0xb02fbc
    // 0xb02260: LoadField: r8 = r1->field_e3
    //     0xb02260: ldur            w8, [x1, #0xe3]
    // 0xb02264: DecompressPointer r8
    //     0xb02264: add             x8, x8, HEAP, lsl #32
    // 0xb02268: cmp             w8, NULL
    // 0xb0226c: b.ne            #0xb02278
    // 0xb02270: r1 = Null
    //     0xb02270: mov             x1, NULL
    // 0xb02274: b               #0xb02280
    // 0xb02278: LoadField: r1 = r8->field_7
    //     0xb02278: ldur            w1, [x8, #7]
    // 0xb0227c: DecompressPointer r1
    //     0xb0227c: add             x1, x1, HEAP, lsl #32
    // 0xb02280: ldur            x8, [fp, #-8]
    // 0xb02284: cmp             w1, NULL
    // 0xb02288: r16 = true
    //     0xb02288: add             x16, NULL, #0x20  ; true
    // 0xb0228c: r17 = false
    //     0xb0228c: add             x17, NULL, #0x30  ; false
    // 0xb02290: csel            x9, x16, x17, ne
    // 0xb02294: stur            x9, [fp, #-0x38]
    // 0xb02298: LoadField: r1 = r8->field_f
    //     0xb02298: ldur            w1, [x8, #0xf]
    // 0xb0229c: DecompressPointer r1
    //     0xb0229c: add             x1, x1, HEAP, lsl #32
    // 0xb022a0: cmp             w1, NULL
    // 0xb022a4: b.eq            #0xb02fc0
    // 0xb022a8: r0 = of()
    //     0xb022a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb022ac: LoadField: r1 = r0->field_5b
    //     0xb022ac: ldur            w1, [x0, #0x5b]
    // 0xb022b0: DecompressPointer r1
    //     0xb022b0: add             x1, x1, HEAP, lsl #32
    // 0xb022b4: stur            x1, [fp, #-0x40]
    // 0xb022b8: r0 = ColorFilter()
    //     0xb022b8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb022bc: mov             x1, x0
    // 0xb022c0: ldur            x0, [fp, #-0x40]
    // 0xb022c4: stur            x1, [fp, #-0x48]
    // 0xb022c8: StoreField: r1->field_7 = r0
    //     0xb022c8: stur            w0, [x1, #7]
    // 0xb022cc: r0 = Instance_BlendMode
    //     0xb022cc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb022d0: ldr             x0, [x0, #0xb30]
    // 0xb022d4: StoreField: r1->field_b = r0
    //     0xb022d4: stur            w0, [x1, #0xb]
    // 0xb022d8: r2 = 1
    //     0xb022d8: movz            x2, #0x1
    // 0xb022dc: StoreField: r1->field_13 = r2
    //     0xb022dc: stur            x2, [x1, #0x13]
    // 0xb022e0: r0 = SvgPicture()
    //     0xb022e0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb022e4: stur            x0, [fp, #-0x40]
    // 0xb022e8: ldur            x16, [fp, #-0x48]
    // 0xb022ec: str             x16, [SP]
    // 0xb022f0: mov             x1, x0
    // 0xb022f4: r2 = "assets/images/green_star.svg"
    //     0xb022f4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb022f8: ldr             x2, [x2, #0x9a0]
    // 0xb022fc: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb022fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb02300: ldr             x4, [x4, #0xa38]
    // 0xb02304: r0 = SvgPicture.asset()
    //     0xb02304: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb02308: ldur            x0, [fp, #-0x20]
    // 0xb0230c: LoadField: r1 = r0->field_13
    //     0xb0230c: ldur            w1, [x0, #0x13]
    // 0xb02310: DecompressPointer r1
    //     0xb02310: add             x1, x1, HEAP, lsl #32
    // 0xb02314: cmp             w1, NULL
    // 0xb02318: b.eq            #0xb02fc4
    // 0xb0231c: LoadField: r2 = r1->field_e3
    //     0xb0231c: ldur            w2, [x1, #0xe3]
    // 0xb02320: DecompressPointer r2
    //     0xb02320: add             x2, x2, HEAP, lsl #32
    // 0xb02324: cmp             w2, NULL
    // 0xb02328: b.ne            #0xb02334
    // 0xb0232c: r0 = Null
    //     0xb0232c: mov             x0, NULL
    // 0xb02330: b               #0xb02354
    // 0xb02334: LoadField: r1 = r2->field_7
    //     0xb02334: ldur            w1, [x2, #7]
    // 0xb02338: DecompressPointer r1
    //     0xb02338: add             x1, x1, HEAP, lsl #32
    // 0xb0233c: cmp             w1, NULL
    // 0xb02340: b.ne            #0xb0234c
    // 0xb02344: r0 = Null
    //     0xb02344: mov             x0, NULL
    // 0xb02348: b               #0xb02354
    // 0xb0234c: r2 = 1
    //     0xb0234c: movz            x2, #0x1
    // 0xb02350: r0 = toStringAsFixed()
    //     0xb02350: bl              #0x7c1ef8  ; [dart:core] _Double::toStringAsFixed
    // 0xb02354: cmp             w0, NULL
    // 0xb02358: b.ne            #0xb02364
    // 0xb0235c: r4 = ""
    //     0xb0235c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb02360: b               #0xb02368
    // 0xb02364: mov             x4, x0
    // 0xb02368: ldur            x2, [fp, #-8]
    // 0xb0236c: ldur            x3, [fp, #-0x38]
    // 0xb02370: ldur            x0, [fp, #-0x40]
    // 0xb02374: stur            x4, [fp, #-0x48]
    // 0xb02378: LoadField: r1 = r2->field_f
    //     0xb02378: ldur            w1, [x2, #0xf]
    // 0xb0237c: DecompressPointer r1
    //     0xb0237c: add             x1, x1, HEAP, lsl #32
    // 0xb02380: cmp             w1, NULL
    // 0xb02384: b.eq            #0xb02fc8
    // 0xb02388: r0 = of()
    //     0xb02388: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0238c: LoadField: r1 = r0->field_87
    //     0xb0238c: ldur            w1, [x0, #0x87]
    // 0xb02390: DecompressPointer r1
    //     0xb02390: add             x1, x1, HEAP, lsl #32
    // 0xb02394: LoadField: r0 = r1->field_7
    //     0xb02394: ldur            w0, [x1, #7]
    // 0xb02398: DecompressPointer r0
    //     0xb02398: add             x0, x0, HEAP, lsl #32
    // 0xb0239c: r16 = 12.000000
    //     0xb0239c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb023a0: ldr             x16, [x16, #0x9e8]
    // 0xb023a4: r30 = Instance_Color
    //     0xb023a4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb023a8: stp             lr, x16, [SP]
    // 0xb023ac: mov             x1, x0
    // 0xb023b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb023b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb023b4: ldr             x4, [x4, #0xaa0]
    // 0xb023b8: r0 = copyWith()
    //     0xb023b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb023bc: stur            x0, [fp, #-0x50]
    // 0xb023c0: r0 = Text()
    //     0xb023c0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb023c4: mov             x2, x0
    // 0xb023c8: ldur            x0, [fp, #-0x48]
    // 0xb023cc: stur            x2, [fp, #-0x58]
    // 0xb023d0: StoreField: r2->field_b = r0
    //     0xb023d0: stur            w0, [x2, #0xb]
    // 0xb023d4: ldur            x0, [fp, #-0x50]
    // 0xb023d8: StoreField: r2->field_13 = r0
    //     0xb023d8: stur            w0, [x2, #0x13]
    // 0xb023dc: ldur            x0, [fp, #-8]
    // 0xb023e0: LoadField: r1 = r0->field_f
    //     0xb023e0: ldur            w1, [x0, #0xf]
    // 0xb023e4: DecompressPointer r1
    //     0xb023e4: add             x1, x1, HEAP, lsl #32
    // 0xb023e8: cmp             w1, NULL
    // 0xb023ec: b.eq            #0xb02fcc
    // 0xb023f0: r0 = of()
    //     0xb023f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb023f4: LoadField: r1 = r0->field_87
    //     0xb023f4: ldur            w1, [x0, #0x87]
    // 0xb023f8: DecompressPointer r1
    //     0xb023f8: add             x1, x1, HEAP, lsl #32
    // 0xb023fc: LoadField: r0 = r1->field_2b
    //     0xb023fc: ldur            w0, [x1, #0x2b]
    // 0xb02400: DecompressPointer r0
    //     0xb02400: add             x0, x0, HEAP, lsl #32
    // 0xb02404: ldur            x2, [fp, #-8]
    // 0xb02408: stur            x0, [fp, #-0x48]
    // 0xb0240c: LoadField: r1 = r2->field_f
    //     0xb0240c: ldur            w1, [x2, #0xf]
    // 0xb02410: DecompressPointer r1
    //     0xb02410: add             x1, x1, HEAP, lsl #32
    // 0xb02414: cmp             w1, NULL
    // 0xb02418: b.eq            #0xb02fd0
    // 0xb0241c: r0 = of()
    //     0xb0241c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb02420: LoadField: r1 = r0->field_5b
    //     0xb02420: ldur            w1, [x0, #0x5b]
    // 0xb02424: DecompressPointer r1
    //     0xb02424: add             x1, x1, HEAP, lsl #32
    // 0xb02428: r16 = 10.000000
    //     0xb02428: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb0242c: stp             x1, x16, [SP]
    // 0xb02430: ldur            x1, [fp, #-0x48]
    // 0xb02434: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb02434: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb02438: ldr             x4, [x4, #0xaa0]
    // 0xb0243c: r0 = copyWith()
    //     0xb0243c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb02440: stur            x0, [fp, #-0x48]
    // 0xb02444: r0 = Text()
    //     0xb02444: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb02448: mov             x3, x0
    // 0xb0244c: r0 = " Brand Rating"
    //     0xb0244c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d78] " Brand Rating"
    //     0xb02450: ldr             x0, [x0, #0xd78]
    // 0xb02454: stur            x3, [fp, #-0x50]
    // 0xb02458: StoreField: r3->field_b = r0
    //     0xb02458: stur            w0, [x3, #0xb]
    // 0xb0245c: ldur            x0, [fp, #-0x48]
    // 0xb02460: StoreField: r3->field_13 = r0
    //     0xb02460: stur            w0, [x3, #0x13]
    // 0xb02464: r1 = Null
    //     0xb02464: mov             x1, NULL
    // 0xb02468: r2 = 6
    //     0xb02468: movz            x2, #0x6
    // 0xb0246c: r0 = AllocateArray()
    //     0xb0246c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb02470: mov             x2, x0
    // 0xb02474: ldur            x0, [fp, #-0x40]
    // 0xb02478: stur            x2, [fp, #-0x48]
    // 0xb0247c: StoreField: r2->field_f = r0
    //     0xb0247c: stur            w0, [x2, #0xf]
    // 0xb02480: ldur            x0, [fp, #-0x58]
    // 0xb02484: StoreField: r2->field_13 = r0
    //     0xb02484: stur            w0, [x2, #0x13]
    // 0xb02488: ldur            x0, [fp, #-0x50]
    // 0xb0248c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb0248c: stur            w0, [x2, #0x17]
    // 0xb02490: r1 = <Widget>
    //     0xb02490: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb02494: r0 = AllocateGrowableArray()
    //     0xb02494: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb02498: mov             x1, x0
    // 0xb0249c: ldur            x0, [fp, #-0x48]
    // 0xb024a0: stur            x1, [fp, #-0x40]
    // 0xb024a4: StoreField: r1->field_f = r0
    //     0xb024a4: stur            w0, [x1, #0xf]
    // 0xb024a8: r2 = 6
    //     0xb024a8: movz            x2, #0x6
    // 0xb024ac: StoreField: r1->field_b = r2
    //     0xb024ac: stur            w2, [x1, #0xb]
    // 0xb024b0: r0 = Row()
    //     0xb024b0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb024b4: mov             x1, x0
    // 0xb024b8: r0 = Instance_Axis
    //     0xb024b8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb024bc: stur            x1, [fp, #-0x48]
    // 0xb024c0: StoreField: r1->field_f = r0
    //     0xb024c0: stur            w0, [x1, #0xf]
    // 0xb024c4: r0 = Instance_MainAxisAlignment
    //     0xb024c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb024c8: ldr             x0, [x0, #0xa08]
    // 0xb024cc: StoreField: r1->field_13 = r0
    //     0xb024cc: stur            w0, [x1, #0x13]
    // 0xb024d0: r2 = Instance_MainAxisSize
    //     0xb024d0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb024d4: ldr             x2, [x2, #0xa10]
    // 0xb024d8: ArrayStore: r1[0] = r2  ; List_4
    //     0xb024d8: stur            w2, [x1, #0x17]
    // 0xb024dc: r2 = Instance_CrossAxisAlignment
    //     0xb024dc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb024e0: ldr             x2, [x2, #0xa18]
    // 0xb024e4: StoreField: r1->field_1b = r2
    //     0xb024e4: stur            w2, [x1, #0x1b]
    // 0xb024e8: r2 = Instance_VerticalDirection
    //     0xb024e8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb024ec: ldr             x2, [x2, #0xa20]
    // 0xb024f0: StoreField: r1->field_23 = r2
    //     0xb024f0: stur            w2, [x1, #0x23]
    // 0xb024f4: r3 = Instance_Clip
    //     0xb024f4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb024f8: ldr             x3, [x3, #0x38]
    // 0xb024fc: StoreField: r1->field_2b = r3
    //     0xb024fc: stur            w3, [x1, #0x2b]
    // 0xb02500: StoreField: r1->field_2f = rZR
    //     0xb02500: stur            xzr, [x1, #0x2f]
    // 0xb02504: ldur            x4, [fp, #-0x40]
    // 0xb02508: StoreField: r1->field_b = r4
    //     0xb02508: stur            w4, [x1, #0xb]
    // 0xb0250c: r0 = Visibility()
    //     0xb0250c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb02510: mov             x1, x0
    // 0xb02514: ldur            x0, [fp, #-0x48]
    // 0xb02518: StoreField: r1->field_b = r0
    //     0xb02518: stur            w0, [x1, #0xb]
    // 0xb0251c: r0 = Instance_SizedBox
    //     0xb0251c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb02520: StoreField: r1->field_f = r0
    //     0xb02520: stur            w0, [x1, #0xf]
    // 0xb02524: ldur            x2, [fp, #-0x38]
    // 0xb02528: StoreField: r1->field_13 = r2
    //     0xb02528: stur            w2, [x1, #0x13]
    // 0xb0252c: r2 = false
    //     0xb0252c: add             x2, NULL, #0x30  ; false
    // 0xb02530: ArrayStore: r1[0] = r2  ; List_4
    //     0xb02530: stur            w2, [x1, #0x17]
    // 0xb02534: StoreField: r1->field_1b = r2
    //     0xb02534: stur            w2, [x1, #0x1b]
    // 0xb02538: StoreField: r1->field_1f = r2
    //     0xb02538: stur            w2, [x1, #0x1f]
    // 0xb0253c: StoreField: r1->field_23 = r2
    //     0xb0253c: stur            w2, [x1, #0x23]
    // 0xb02540: StoreField: r1->field_27 = r2
    //     0xb02540: stur            w2, [x1, #0x27]
    // 0xb02544: StoreField: r1->field_2b = r2
    //     0xb02544: stur            w2, [x1, #0x2b]
    // 0xb02548: mov             x5, x1
    // 0xb0254c: ldur            x1, [fp, #-8]
    // 0xb02550: ldur            x3, [fp, #-0x20]
    // 0xb02554: ldur            x4, [fp, #-0x30]
    // 0xb02558: stur            x5, [fp, #-0x38]
    // 0xb0255c: r0 = Visibility()
    //     0xb0255c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb02560: mov             x3, x0
    // 0xb02564: ldur            x0, [fp, #-0x38]
    // 0xb02568: stur            x3, [fp, #-0x40]
    // 0xb0256c: StoreField: r3->field_b = r0
    //     0xb0256c: stur            w0, [x3, #0xb]
    // 0xb02570: r0 = Instance_SizedBox
    //     0xb02570: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb02574: StoreField: r3->field_f = r0
    //     0xb02574: stur            w0, [x3, #0xf]
    // 0xb02578: ldur            x1, [fp, #-0x30]
    // 0xb0257c: StoreField: r3->field_13 = r1
    //     0xb0257c: stur            w1, [x3, #0x13]
    // 0xb02580: r4 = false
    //     0xb02580: add             x4, NULL, #0x30  ; false
    // 0xb02584: ArrayStore: r3[0] = r4  ; List_4
    //     0xb02584: stur            w4, [x3, #0x17]
    // 0xb02588: StoreField: r3->field_1b = r4
    //     0xb02588: stur            w4, [x3, #0x1b]
    // 0xb0258c: StoreField: r3->field_1f = r4
    //     0xb0258c: stur            w4, [x3, #0x1f]
    // 0xb02590: StoreField: r3->field_23 = r4
    //     0xb02590: stur            w4, [x3, #0x23]
    // 0xb02594: StoreField: r3->field_27 = r4
    //     0xb02594: stur            w4, [x3, #0x27]
    // 0xb02598: StoreField: r3->field_2b = r4
    //     0xb02598: stur            w4, [x3, #0x2b]
    // 0xb0259c: ldur            x5, [fp, #-0x20]
    // 0xb025a0: LoadField: r1 = r5->field_13
    //     0xb025a0: ldur            w1, [x5, #0x13]
    // 0xb025a4: DecompressPointer r1
    //     0xb025a4: add             x1, x1, HEAP, lsl #32
    // 0xb025a8: cmp             w1, NULL
    // 0xb025ac: b.eq            #0xb02fd4
    // 0xb025b0: LoadField: r6 = r1->field_f3
    //     0xb025b0: ldur            w6, [x1, #0xf3]
    // 0xb025b4: DecompressPointer r6
    //     0xb025b4: add             x6, x6, HEAP, lsl #32
    // 0xb025b8: stur            x6, [fp, #-0x30]
    // 0xb025bc: r1 = Null
    //     0xb025bc: mov             x1, NULL
    // 0xb025c0: r2 = 4
    //     0xb025c0: movz            x2, #0x4
    // 0xb025c4: r0 = AllocateArray()
    //     0xb025c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb025c8: mov             x1, x0
    // 0xb025cc: ldur            x0, [fp, #-0x30]
    // 0xb025d0: StoreField: r1->field_f = r0
    //     0xb025d0: stur            w0, [x1, #0xf]
    // 0xb025d4: r16 = " "
    //     0xb025d4: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb025d8: StoreField: r1->field_13 = r16
    //     0xb025d8: stur            w16, [x1, #0x13]
    // 0xb025dc: str             x1, [SP]
    // 0xb025e0: r0 = _interpolate()
    //     0xb025e0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb025e4: mov             x2, x0
    // 0xb025e8: ldur            x0, [fp, #-8]
    // 0xb025ec: stur            x2, [fp, #-0x30]
    // 0xb025f0: LoadField: r1 = r0->field_f
    //     0xb025f0: ldur            w1, [x0, #0xf]
    // 0xb025f4: DecompressPointer r1
    //     0xb025f4: add             x1, x1, HEAP, lsl #32
    // 0xb025f8: cmp             w1, NULL
    // 0xb025fc: b.eq            #0xb02fd8
    // 0xb02600: r0 = of()
    //     0xb02600: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb02604: LoadField: r1 = r0->field_87
    //     0xb02604: ldur            w1, [x0, #0x87]
    // 0xb02608: DecompressPointer r1
    //     0xb02608: add             x1, x1, HEAP, lsl #32
    // 0xb0260c: LoadField: r0 = r1->field_27
    //     0xb0260c: ldur            w0, [x1, #0x27]
    // 0xb02610: DecompressPointer r0
    //     0xb02610: add             x0, x0, HEAP, lsl #32
    // 0xb02614: r16 = 15.000000
    //     0xb02614: add             x16, PP, #0x41, lsl #12  ; [pp+0x41480] 15
    //     0xb02618: ldr             x16, [x16, #0x480]
    // 0xb0261c: r30 = Instance_Color
    //     0xb0261c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb02620: stp             lr, x16, [SP]
    // 0xb02624: mov             x1, x0
    // 0xb02628: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb02628: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0262c: ldr             x4, [x4, #0xaa0]
    // 0xb02630: r0 = copyWith()
    //     0xb02630: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb02634: stur            x0, [fp, #-0x38]
    // 0xb02638: r0 = Text()
    //     0xb02638: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0263c: mov             x1, x0
    // 0xb02640: ldur            x0, [fp, #-0x30]
    // 0xb02644: stur            x1, [fp, #-0x48]
    // 0xb02648: StoreField: r1->field_b = r0
    //     0xb02648: stur            w0, [x1, #0xb]
    // 0xb0264c: ldur            x0, [fp, #-0x38]
    // 0xb02650: StoreField: r1->field_13 = r0
    //     0xb02650: stur            w0, [x1, #0x13]
    // 0xb02654: r0 = WidgetSpan()
    //     0xb02654: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xb02658: mov             x3, x0
    // 0xb0265c: ldur            x0, [fp, #-0x48]
    // 0xb02660: stur            x3, [fp, #-0x38]
    // 0xb02664: StoreField: r3->field_13 = r0
    //     0xb02664: stur            w0, [x3, #0x13]
    // 0xb02668: r0 = Instance_PlaceholderAlignment
    //     0xb02668: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] Obj!PlaceholderAlignment@d76401
    //     0xb0266c: ldr             x0, [x0, #0xa0]
    // 0xb02670: StoreField: r3->field_b = r0
    //     0xb02670: stur            w0, [x3, #0xb]
    // 0xb02674: ldur            x0, [fp, #-0x20]
    // 0xb02678: LoadField: r1 = r0->field_13
    //     0xb02678: ldur            w1, [x0, #0x13]
    // 0xb0267c: DecompressPointer r1
    //     0xb0267c: add             x1, x1, HEAP, lsl #32
    // 0xb02680: cmp             w1, NULL
    // 0xb02684: b.eq            #0xb02fdc
    // 0xb02688: LoadField: r4 = r1->field_fb
    //     0xb02688: ldur            w4, [x1, #0xfb]
    // 0xb0268c: DecompressPointer r4
    //     0xb0268c: add             x4, x4, HEAP, lsl #32
    // 0xb02690: stur            x4, [fp, #-0x30]
    // 0xb02694: r1 = Null
    //     0xb02694: mov             x1, NULL
    // 0xb02698: r2 = 4
    //     0xb02698: movz            x2, #0x4
    // 0xb0269c: r0 = AllocateArray()
    //     0xb0269c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb026a0: mov             x1, x0
    // 0xb026a4: ldur            x0, [fp, #-0x30]
    // 0xb026a8: StoreField: r1->field_f = r0
    //     0xb026a8: stur            w0, [x1, #0xf]
    // 0xb026ac: r16 = " "
    //     0xb026ac: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb026b0: StoreField: r1->field_13 = r16
    //     0xb026b0: stur            w16, [x1, #0x13]
    // 0xb026b4: str             x1, [SP]
    // 0xb026b8: r0 = _interpolate()
    //     0xb026b8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb026bc: mov             x2, x0
    // 0xb026c0: ldur            x0, [fp, #-8]
    // 0xb026c4: stur            x2, [fp, #-0x30]
    // 0xb026c8: LoadField: r1 = r0->field_f
    //     0xb026c8: ldur            w1, [x0, #0xf]
    // 0xb026cc: DecompressPointer r1
    //     0xb026cc: add             x1, x1, HEAP, lsl #32
    // 0xb026d0: cmp             w1, NULL
    // 0xb026d4: b.eq            #0xb02fe0
    // 0xb026d8: r0 = of()
    //     0xb026d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb026dc: LoadField: r1 = r0->field_87
    //     0xb026dc: ldur            w1, [x0, #0x87]
    // 0xb026e0: DecompressPointer r1
    //     0xb026e0: add             x1, x1, HEAP, lsl #32
    // 0xb026e4: LoadField: r0 = r1->field_2b
    //     0xb026e4: ldur            w0, [x1, #0x2b]
    // 0xb026e8: DecompressPointer r0
    //     0xb026e8: add             x0, x0, HEAP, lsl #32
    // 0xb026ec: stur            x0, [fp, #-0x48]
    // 0xb026f0: r1 = Instance_Color
    //     0xb026f0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb026f4: d0 = 0.400000
    //     0xb026f4: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb026f8: r0 = withOpacity()
    //     0xb026f8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb026fc: r16 = Instance_TextDecoration
    //     0xb026fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb02700: ldr             x16, [x16, #0xe30]
    // 0xb02704: r30 = 10.000000
    //     0xb02704: ldr             lr, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb02708: stp             lr, x16, [SP, #8]
    // 0xb0270c: str             x0, [SP]
    // 0xb02710: ldur            x1, [fp, #-0x48]
    // 0xb02714: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, decoration, 0x1, fontSize, 0x2, null]
    //     0xb02714: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb60] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "decoration", 0x1, "fontSize", 0x2, Null]
    //     0xb02718: ldr             x4, [x4, #0xb60]
    // 0xb0271c: r0 = copyWith()
    //     0xb0271c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb02720: stur            x0, [fp, #-0x48]
    // 0xb02724: r0 = TextSpan()
    //     0xb02724: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb02728: mov             x3, x0
    // 0xb0272c: ldur            x0, [fp, #-0x30]
    // 0xb02730: stur            x3, [fp, #-0x50]
    // 0xb02734: StoreField: r3->field_b = r0
    //     0xb02734: stur            w0, [x3, #0xb]
    // 0xb02738: r0 = Instance__DeferringMouseCursor
    //     0xb02738: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb0273c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb0273c: stur            w0, [x3, #0x17]
    // 0xb02740: ldur            x1, [fp, #-0x48]
    // 0xb02744: StoreField: r3->field_7 = r1
    //     0xb02744: stur            w1, [x3, #7]
    // 0xb02748: r1 = Null
    //     0xb02748: mov             x1, NULL
    // 0xb0274c: r2 = 6
    //     0xb0274c: movz            x2, #0x6
    // 0xb02750: r0 = AllocateArray()
    //     0xb02750: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb02754: stur            x0, [fp, #-0x30]
    // 0xb02758: r16 = "| "
    //     0xb02758: add             x16, PP, #0x55, lsl #12  ; [pp+0x55938] "| "
    //     0xb0275c: ldr             x16, [x16, #0x938]
    // 0xb02760: StoreField: r0->field_f = r16
    //     0xb02760: stur            w16, [x0, #0xf]
    // 0xb02764: ldur            x2, [fp, #-0x20]
    // 0xb02768: LoadField: r1 = r2->field_13
    //     0xb02768: ldur            w1, [x2, #0x13]
    // 0xb0276c: DecompressPointer r1
    //     0xb0276c: add             x1, x1, HEAP, lsl #32
    // 0xb02770: cmp             w1, NULL
    // 0xb02774: b.eq            #0xb02fe4
    // 0xb02778: LoadField: r3 = r1->field_5f
    //     0xb02778: ldur            w3, [x1, #0x5f]
    // 0xb0277c: DecompressPointer r3
    //     0xb0277c: add             x3, x3, HEAP, lsl #32
    // 0xb02780: stp             xzr, x3, [SP]
    // 0xb02784: r4 = 0
    //     0xb02784: movz            x4, #0
    // 0xb02788: ldr             x0, [SP, #8]
    // 0xb0278c: r16 = UnlinkedCall_0x613b5c
    //     0xb0278c: add             x16, PP, #0x57, lsl #12  ; [pp+0x57ed0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb02790: add             x16, x16, #0xed0
    // 0xb02794: ldp             x5, lr, [x16]
    // 0xb02798: blr             lr
    // 0xb0279c: ldur            x1, [fp, #-0x30]
    // 0xb027a0: ArrayStore: r1[1] = r0  ; List_4
    //     0xb027a0: add             x25, x1, #0x13
    //     0xb027a4: str             w0, [x25]
    //     0xb027a8: tbz             w0, #0, #0xb027c4
    //     0xb027ac: ldurb           w16, [x1, #-1]
    //     0xb027b0: ldurb           w17, [x0, #-1]
    //     0xb027b4: and             x16, x17, x16, lsr #2
    //     0xb027b8: tst             x16, HEAP, lsr #32
    //     0xb027bc: b.eq            #0xb027c4
    //     0xb027c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb027c4: ldur            x0, [fp, #-0x30]
    // 0xb027c8: r16 = "% OFF"
    //     0xb027c8: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d98] "% OFF"
    //     0xb027cc: ldr             x16, [x16, #0xd98]
    // 0xb027d0: ArrayStore: r0[0] = r16  ; List_4
    //     0xb027d0: stur            w16, [x0, #0x17]
    // 0xb027d4: str             x0, [SP]
    // 0xb027d8: r0 = _interpolate()
    //     0xb027d8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb027dc: mov             x2, x0
    // 0xb027e0: ldur            x0, [fp, #-8]
    // 0xb027e4: stur            x2, [fp, #-0x30]
    // 0xb027e8: LoadField: r1 = r0->field_f
    //     0xb027e8: ldur            w1, [x0, #0xf]
    // 0xb027ec: DecompressPointer r1
    //     0xb027ec: add             x1, x1, HEAP, lsl #32
    // 0xb027f0: cmp             w1, NULL
    // 0xb027f4: b.eq            #0xb02fe8
    // 0xb027f8: r0 = of()
    //     0xb027f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb027fc: LoadField: r1 = r0->field_87
    //     0xb027fc: ldur            w1, [x0, #0x87]
    // 0xb02800: DecompressPointer r1
    //     0xb02800: add             x1, x1, HEAP, lsl #32
    // 0xb02804: LoadField: r0 = r1->field_2b
    //     0xb02804: ldur            w0, [x1, #0x2b]
    // 0xb02808: DecompressPointer r0
    //     0xb02808: add             x0, x0, HEAP, lsl #32
    // 0xb0280c: r16 = Instance_Color
    //     0xb0280c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb02810: ldr             x16, [x16, #0x858]
    // 0xb02814: r30 = 12.000000
    //     0xb02814: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb02818: ldr             lr, [lr, #0x9e8]
    // 0xb0281c: stp             lr, x16, [SP]
    // 0xb02820: mov             x1, x0
    // 0xb02824: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb02824: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb02828: ldr             x4, [x4, #0x9b8]
    // 0xb0282c: r0 = copyWith()
    //     0xb0282c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb02830: stur            x0, [fp, #-0x48]
    // 0xb02834: r0 = TextSpan()
    //     0xb02834: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb02838: mov             x3, x0
    // 0xb0283c: ldur            x0, [fp, #-0x30]
    // 0xb02840: stur            x3, [fp, #-0x58]
    // 0xb02844: StoreField: r3->field_b = r0
    //     0xb02844: stur            w0, [x3, #0xb]
    // 0xb02848: r0 = Instance__DeferringMouseCursor
    //     0xb02848: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb0284c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb0284c: stur            w0, [x3, #0x17]
    // 0xb02850: ldur            x1, [fp, #-0x48]
    // 0xb02854: StoreField: r3->field_7 = r1
    //     0xb02854: stur            w1, [x3, #7]
    // 0xb02858: r1 = Null
    //     0xb02858: mov             x1, NULL
    // 0xb0285c: r2 = 6
    //     0xb0285c: movz            x2, #0x6
    // 0xb02860: r0 = AllocateArray()
    //     0xb02860: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb02864: mov             x2, x0
    // 0xb02868: ldur            x0, [fp, #-0x38]
    // 0xb0286c: stur            x2, [fp, #-0x30]
    // 0xb02870: StoreField: r2->field_f = r0
    //     0xb02870: stur            w0, [x2, #0xf]
    // 0xb02874: ldur            x0, [fp, #-0x50]
    // 0xb02878: StoreField: r2->field_13 = r0
    //     0xb02878: stur            w0, [x2, #0x13]
    // 0xb0287c: ldur            x0, [fp, #-0x58]
    // 0xb02880: ArrayStore: r2[0] = r0  ; List_4
    //     0xb02880: stur            w0, [x2, #0x17]
    // 0xb02884: r1 = <InlineSpan>
    //     0xb02884: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb02888: ldr             x1, [x1, #0xe40]
    // 0xb0288c: r0 = AllocateGrowableArray()
    //     0xb0288c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb02890: mov             x1, x0
    // 0xb02894: ldur            x0, [fp, #-0x30]
    // 0xb02898: stur            x1, [fp, #-0x38]
    // 0xb0289c: StoreField: r1->field_f = r0
    //     0xb0289c: stur            w0, [x1, #0xf]
    // 0xb028a0: r0 = 6
    //     0xb028a0: movz            x0, #0x6
    // 0xb028a4: StoreField: r1->field_b = r0
    //     0xb028a4: stur            w0, [x1, #0xb]
    // 0xb028a8: r0 = TextSpan()
    //     0xb028a8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb028ac: mov             x1, x0
    // 0xb028b0: ldur            x0, [fp, #-0x38]
    // 0xb028b4: stur            x1, [fp, #-0x30]
    // 0xb028b8: StoreField: r1->field_f = r0
    //     0xb028b8: stur            w0, [x1, #0xf]
    // 0xb028bc: r0 = Instance__DeferringMouseCursor
    //     0xb028bc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb028c0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb028c0: stur            w0, [x1, #0x17]
    // 0xb028c4: r0 = RichText()
    //     0xb028c4: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb028c8: mov             x1, x0
    // 0xb028cc: ldur            x2, [fp, #-0x30]
    // 0xb028d0: stur            x0, [fp, #-0x30]
    // 0xb028d4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb028d4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb028d8: r0 = RichText()
    //     0xb028d8: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb028dc: r0 = Padding()
    //     0xb028dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb028e0: mov             x1, x0
    // 0xb028e4: r0 = Instance_EdgeInsets
    //     0xb028e4: add             x0, PP, #0x55, lsl #12  ; [pp+0x55930] Obj!EdgeInsets@d586a1
    //     0xb028e8: ldr             x0, [x0, #0x930]
    // 0xb028ec: stur            x1, [fp, #-0x38]
    // 0xb028f0: StoreField: r1->field_f = r0
    //     0xb028f0: stur            w0, [x1, #0xf]
    // 0xb028f4: ldur            x0, [fp, #-0x30]
    // 0xb028f8: StoreField: r1->field_b = r0
    //     0xb028f8: stur            w0, [x1, #0xb]
    // 0xb028fc: ldur            x0, [fp, #-8]
    // 0xb02900: LoadField: r2 = r0->field_b
    //     0xb02900: ldur            w2, [x0, #0xb]
    // 0xb02904: DecompressPointer r2
    //     0xb02904: add             x2, x2, HEAP, lsl #32
    // 0xb02908: cmp             w2, NULL
    // 0xb0290c: b.eq            #0xb02fec
    // 0xb02910: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb02910: ldur            w3, [x2, #0x17]
    // 0xb02914: DecompressPointer r3
    //     0xb02914: add             x3, x3, HEAP, lsl #32
    // 0xb02918: LoadField: r2 = r3->field_1f
    //     0xb02918: ldur            w2, [x3, #0x1f]
    // 0xb0291c: DecompressPointer r2
    //     0xb0291c: add             x2, x2, HEAP, lsl #32
    // 0xb02920: cmp             w2, NULL
    // 0xb02924: b.ne            #0xb02930
    // 0xb02928: r2 = Null
    //     0xb02928: mov             x2, NULL
    // 0xb0292c: b               #0xb0293c
    // 0xb02930: LoadField: r4 = r2->field_7
    //     0xb02930: ldur            w4, [x2, #7]
    // 0xb02934: DecompressPointer r4
    //     0xb02934: add             x4, x4, HEAP, lsl #32
    // 0xb02938: mov             x2, x4
    // 0xb0293c: cmp             w2, NULL
    // 0xb02940: b.ne            #0xb02948
    // 0xb02944: r2 = false
    //     0xb02944: add             x2, NULL, #0x30  ; false
    // 0xb02948: stur            x2, [fp, #-0x30]
    // 0xb0294c: LoadField: r4 = r3->field_3f
    //     0xb0294c: ldur            w4, [x3, #0x3f]
    // 0xb02950: DecompressPointer r4
    //     0xb02950: add             x4, x4, HEAP, lsl #32
    // 0xb02954: cmp             w4, NULL
    // 0xb02958: b.ne            #0xb02964
    // 0xb0295c: r3 = Null
    //     0xb0295c: mov             x3, NULL
    // 0xb02960: b               #0xb0296c
    // 0xb02964: LoadField: r3 = r4->field_23
    //     0xb02964: ldur            w3, [x4, #0x23]
    // 0xb02968: DecompressPointer r3
    //     0xb02968: add             x3, x3, HEAP, lsl #32
    // 0xb0296c: cmp             w3, NULL
    // 0xb02970: b.eq            #0xb02d8c
    // 0xb02974: tbnz            w3, #4, #0xb02d8c
    // 0xb02978: ldur            x3, [fp, #-0x20]
    // 0xb0297c: r16 = <Size?>
    //     0xb0297c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27768] TypeArguments: <Size?>
    //     0xb02980: ldr             x16, [x16, #0x768]
    // 0xb02984: r30 = Instance_Size
    //     0xb02984: add             lr, PP, #0x55, lsl #12  ; [pp+0x55950] Obj!Size@d6c241
    //     0xb02988: ldr             lr, [lr, #0x950]
    // 0xb0298c: stp             lr, x16, [SP]
    // 0xb02990: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb02990: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb02994: r0 = all()
    //     0xb02994: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb02998: ldur            x2, [fp, #-0x20]
    // 0xb0299c: stur            x0, [fp, #-0x48]
    // 0xb029a0: LoadField: r1 = r2->field_13
    //     0xb029a0: ldur            w1, [x2, #0x13]
    // 0xb029a4: DecompressPointer r1
    //     0xb029a4: add             x1, x1, HEAP, lsl #32
    // 0xb029a8: cmp             w1, NULL
    // 0xb029ac: b.eq            #0xb02ff0
    // 0xb029b0: r17 = 275
    //     0xb029b0: movz            x17, #0x113
    // 0xb029b4: ldr             w3, [x1, x17]
    // 0xb029b8: DecompressPointer r3
    //     0xb029b8: add             x3, x3, HEAP, lsl #32
    // 0xb029bc: cmp             w3, NULL
    // 0xb029c0: b.eq            #0xb029c8
    // 0xb029c4: tbnz            w3, #4, #0xb029e8
    // 0xb029c8: r16 = <Color>
    //     0xb029c8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb029cc: ldr             x16, [x16, #0xf80]
    // 0xb029d0: r30 = Instance_MaterialColor
    //     0xb029d0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xb029d4: ldr             lr, [lr, #0xdc0]
    // 0xb029d8: stp             lr, x16, [SP]
    // 0xb029dc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb029dc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb029e0: r0 = all()
    //     0xb029e0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb029e4: b               #0xb02a00
    // 0xb029e8: r16 = <Color>
    //     0xb029e8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb029ec: ldr             x16, [x16, #0xf80]
    // 0xb029f0: r30 = Instance_Color
    //     0xb029f0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb029f4: stp             lr, x16, [SP]
    // 0xb029f8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb029f8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb029fc: r0 = all()
    //     0xb029fc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb02a00: ldur            x2, [fp, #-0x20]
    // 0xb02a04: stur            x0, [fp, #-0x50]
    // 0xb02a08: LoadField: r1 = r2->field_13
    //     0xb02a08: ldur            w1, [x2, #0x13]
    // 0xb02a0c: DecompressPointer r1
    //     0xb02a0c: add             x1, x1, HEAP, lsl #32
    // 0xb02a10: cmp             w1, NULL
    // 0xb02a14: b.eq            #0xb02ff4
    // 0xb02a18: r17 = 275
    //     0xb02a18: movz            x17, #0x113
    // 0xb02a1c: ldr             w3, [x1, x17]
    // 0xb02a20: DecompressPointer r3
    //     0xb02a20: add             x3, x3, HEAP, lsl #32
    // 0xb02a24: cmp             w3, NULL
    // 0xb02a28: b.eq            #0xb02a30
    // 0xb02a2c: tbnz            w3, #4, #0xb02a58
    // 0xb02a30: r1 = Instance_MaterialColor
    //     0xb02a30: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xb02a34: ldr             x1, [x1, #0xdc0]
    // 0xb02a38: d0 = 0.200000
    //     0xb02a38: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb02a3c: r0 = withOpacity()
    //     0xb02a3c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb02a40: r16 = <Color>
    //     0xb02a40: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb02a44: ldr             x16, [x16, #0xf80]
    // 0xb02a48: stp             x0, x16, [SP]
    // 0xb02a4c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb02a4c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb02a50: r0 = all()
    //     0xb02a50: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb02a54: b               #0xb02a70
    // 0xb02a58: r16 = <Color>
    //     0xb02a58: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb02a5c: ldr             x16, [x16, #0xf80]
    // 0xb02a60: r30 = Instance_Color
    //     0xb02a60: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb02a64: stp             lr, x16, [SP]
    // 0xb02a68: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb02a68: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb02a6c: r0 = all()
    //     0xb02a6c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb02a70: ldur            x2, [fp, #-0x20]
    // 0xb02a74: stur            x0, [fp, #-0x58]
    // 0xb02a78: LoadField: r1 = r2->field_13
    //     0xb02a78: ldur            w1, [x2, #0x13]
    // 0xb02a7c: DecompressPointer r1
    //     0xb02a7c: add             x1, x1, HEAP, lsl #32
    // 0xb02a80: cmp             w1, NULL
    // 0xb02a84: b.eq            #0xb02ff8
    // 0xb02a88: r17 = 275
    //     0xb02a88: movz            x17, #0x113
    // 0xb02a8c: ldr             w3, [x1, x17]
    // 0xb02a90: DecompressPointer r3
    //     0xb02a90: add             x3, x3, HEAP, lsl #32
    // 0xb02a94: cmp             w3, NULL
    // 0xb02a98: b.eq            #0xb02aa0
    // 0xb02a9c: tbnz            w3, #4, #0xb02af8
    // 0xb02aa0: ldur            x3, [fp, #-8]
    // 0xb02aa4: LoadField: r1 = r3->field_f
    //     0xb02aa4: ldur            w1, [x3, #0xf]
    // 0xb02aa8: DecompressPointer r1
    //     0xb02aa8: add             x1, x1, HEAP, lsl #32
    // 0xb02aac: cmp             w1, NULL
    // 0xb02ab0: b.eq            #0xb02ffc
    // 0xb02ab4: r0 = of()
    //     0xb02ab4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb02ab8: LoadField: r1 = r0->field_5b
    //     0xb02ab8: ldur            w1, [x0, #0x5b]
    // 0xb02abc: DecompressPointer r1
    //     0xb02abc: add             x1, x1, HEAP, lsl #32
    // 0xb02ac0: stur            x1, [fp, #-0x60]
    // 0xb02ac4: r0 = BorderSide()
    //     0xb02ac4: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb02ac8: mov             x1, x0
    // 0xb02acc: ldur            x0, [fp, #-0x60]
    // 0xb02ad0: StoreField: r1->field_7 = r0
    //     0xb02ad0: stur            w0, [x1, #7]
    // 0xb02ad4: d0 = 1.000000
    //     0xb02ad4: fmov            d0, #1.00000000
    // 0xb02ad8: StoreField: r1->field_b = d0
    //     0xb02ad8: stur            d0, [x1, #0xb]
    // 0xb02adc: r0 = Instance_BorderStyle
    //     0xb02adc: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb02ae0: ldr             x0, [x0, #0xf68]
    // 0xb02ae4: StoreField: r1->field_13 = r0
    //     0xb02ae4: stur            w0, [x1, #0x13]
    // 0xb02ae8: d0 = -1.000000
    //     0xb02ae8: fmov            d0, #-1.00000000
    // 0xb02aec: ArrayStore: r1[0] = d0  ; List_8
    //     0xb02aec: stur            d0, [x1, #0x17]
    // 0xb02af0: mov             x4, x1
    // 0xb02af4: b               #0xb02b00
    // 0xb02af8: r4 = Instance_BorderSide
    //     0xb02af8: add             x4, PP, #0x55, lsl #12  ; [pp+0x55958] Obj!BorderSide@d62f31
    //     0xb02afc: ldr             x4, [x4, #0x958]
    // 0xb02b00: ldur            x2, [fp, #-0x20]
    // 0xb02b04: ldur            x3, [fp, #-0x48]
    // 0xb02b08: ldur            x1, [fp, #-0x50]
    // 0xb02b0c: ldur            x0, [fp, #-0x58]
    // 0xb02b10: stur            x4, [fp, #-0x60]
    // 0xb02b14: r0 = RoundedRectangleBorder()
    //     0xb02b14: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb02b18: mov             x1, x0
    // 0xb02b1c: r0 = Instance_BorderRadius
    //     0xb02b1c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb02b20: ldr             x0, [x0, #0x460]
    // 0xb02b24: StoreField: r1->field_b = r0
    //     0xb02b24: stur            w0, [x1, #0xb]
    // 0xb02b28: ldur            x0, [fp, #-0x60]
    // 0xb02b2c: StoreField: r1->field_7 = r0
    //     0xb02b2c: stur            w0, [x1, #7]
    // 0xb02b30: r16 = <RoundedRectangleBorder>
    //     0xb02b30: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb02b34: ldr             x16, [x16, #0xf78]
    // 0xb02b38: stp             x1, x16, [SP]
    // 0xb02b3c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb02b3c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb02b40: r0 = all()
    //     0xb02b40: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb02b44: stur            x0, [fp, #-0x60]
    // 0xb02b48: r0 = ButtonStyle()
    //     0xb02b48: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb02b4c: mov             x1, x0
    // 0xb02b50: ldur            x0, [fp, #-0x58]
    // 0xb02b54: stur            x1, [fp, #-0x68]
    // 0xb02b58: StoreField: r1->field_b = r0
    //     0xb02b58: stur            w0, [x1, #0xb]
    // 0xb02b5c: ldur            x0, [fp, #-0x50]
    // 0xb02b60: StoreField: r1->field_f = r0
    //     0xb02b60: stur            w0, [x1, #0xf]
    // 0xb02b64: ldur            x0, [fp, #-0x48]
    // 0xb02b68: StoreField: r1->field_27 = r0
    //     0xb02b68: stur            w0, [x1, #0x27]
    // 0xb02b6c: ldur            x0, [fp, #-0x60]
    // 0xb02b70: StoreField: r1->field_43 = r0
    //     0xb02b70: stur            w0, [x1, #0x43]
    // 0xb02b74: r0 = TextButtonThemeData()
    //     0xb02b74: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb02b78: mov             x2, x0
    // 0xb02b7c: ldur            x0, [fp, #-0x68]
    // 0xb02b80: stur            x2, [fp, #-0x48]
    // 0xb02b84: StoreField: r2->field_7 = r0
    //     0xb02b84: stur            w0, [x2, #7]
    // 0xb02b88: ldur            x0, [fp, #-0x20]
    // 0xb02b8c: LoadField: r1 = r0->field_13
    //     0xb02b8c: ldur            w1, [x0, #0x13]
    // 0xb02b90: DecompressPointer r1
    //     0xb02b90: add             x1, x1, HEAP, lsl #32
    // 0xb02b94: cmp             w1, NULL
    // 0xb02b98: b.eq            #0xb03000
    // 0xb02b9c: r17 = 275
    //     0xb02b9c: movz            x17, #0x113
    // 0xb02ba0: ldr             w3, [x1, x17]
    // 0xb02ba4: DecompressPointer r3
    //     0xb02ba4: add             x3, x3, HEAP, lsl #32
    // 0xb02ba8: cmp             w3, NULL
    // 0xb02bac: b.eq            #0xb02bb4
    // 0xb02bb0: tbnz            w3, #4, #0xb02c6c
    // 0xb02bb4: LoadField: r3 = r1->field_f
    //     0xb02bb4: ldur            w3, [x1, #0xf]
    // 0xb02bb8: DecompressPointer r3
    //     0xb02bb8: add             x3, x3, HEAP, lsl #32
    // 0xb02bbc: cmp             w3, NULL
    // 0xb02bc0: b.ne            #0xb02bcc
    // 0xb02bc4: r1 = ""
    //     0xb02bc4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb02bc8: b               #0xb02bd0
    // 0xb02bcc: mov             x1, x3
    // 0xb02bd0: ldur            x3, [fp, #-8]
    // 0xb02bd4: r0 = capitalizeFirstWord()
    //     0xb02bd4: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb02bd8: mov             x2, x0
    // 0xb02bdc: ldur            x0, [fp, #-8]
    // 0xb02be0: stur            x2, [fp, #-0x50]
    // 0xb02be4: LoadField: r1 = r0->field_f
    //     0xb02be4: ldur            w1, [x0, #0xf]
    // 0xb02be8: DecompressPointer r1
    //     0xb02be8: add             x1, x1, HEAP, lsl #32
    // 0xb02bec: cmp             w1, NULL
    // 0xb02bf0: b.eq            #0xb03004
    // 0xb02bf4: r0 = of()
    //     0xb02bf4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb02bf8: LoadField: r1 = r0->field_87
    //     0xb02bf8: ldur            w1, [x0, #0x87]
    // 0xb02bfc: DecompressPointer r1
    //     0xb02bfc: add             x1, x1, HEAP, lsl #32
    // 0xb02c00: LoadField: r0 = r1->field_7
    //     0xb02c00: ldur            w0, [x1, #7]
    // 0xb02c04: DecompressPointer r0
    //     0xb02c04: add             x0, x0, HEAP, lsl #32
    // 0xb02c08: ldur            x2, [fp, #-8]
    // 0xb02c0c: stur            x0, [fp, #-0x58]
    // 0xb02c10: LoadField: r1 = r2->field_f
    //     0xb02c10: ldur            w1, [x2, #0xf]
    // 0xb02c14: DecompressPointer r1
    //     0xb02c14: add             x1, x1, HEAP, lsl #32
    // 0xb02c18: cmp             w1, NULL
    // 0xb02c1c: b.eq            #0xb03008
    // 0xb02c20: r0 = of()
    //     0xb02c20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb02c24: LoadField: r1 = r0->field_5b
    //     0xb02c24: ldur            w1, [x0, #0x5b]
    // 0xb02c28: DecompressPointer r1
    //     0xb02c28: add             x1, x1, HEAP, lsl #32
    // 0xb02c2c: r16 = 16.000000
    //     0xb02c2c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb02c30: ldr             x16, [x16, #0x188]
    // 0xb02c34: stp             x1, x16, [SP]
    // 0xb02c38: ldur            x1, [fp, #-0x58]
    // 0xb02c3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb02c3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb02c40: ldr             x4, [x4, #0xaa0]
    // 0xb02c44: r0 = copyWith()
    //     0xb02c44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb02c48: stur            x0, [fp, #-0x58]
    // 0xb02c4c: r0 = Text()
    //     0xb02c4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb02c50: mov             x1, x0
    // 0xb02c54: ldur            x0, [fp, #-0x50]
    // 0xb02c58: StoreField: r1->field_b = r0
    //     0xb02c58: stur            w0, [x1, #0xb]
    // 0xb02c5c: ldur            x0, [fp, #-0x58]
    // 0xb02c60: StoreField: r1->field_13 = r0
    //     0xb02c60: stur            w0, [x1, #0x13]
    // 0xb02c64: mov             x3, x1
    // 0xb02c68: b               #0xb02d24
    // 0xb02c6c: ldur            x2, [fp, #-8]
    // 0xb02c70: LoadField: r0 = r1->field_f
    //     0xb02c70: ldur            w0, [x1, #0xf]
    // 0xb02c74: DecompressPointer r0
    //     0xb02c74: add             x0, x0, HEAP, lsl #32
    // 0xb02c78: cmp             w0, NULL
    // 0xb02c7c: b.ne            #0xb02c88
    // 0xb02c80: r1 = ""
    //     0xb02c80: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb02c84: b               #0xb02c8c
    // 0xb02c88: mov             x1, x0
    // 0xb02c8c: r0 = capitalizeFirstWord()
    //     0xb02c8c: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb02c90: mov             x2, x0
    // 0xb02c94: ldur            x0, [fp, #-8]
    // 0xb02c98: stur            x2, [fp, #-0x50]
    // 0xb02c9c: LoadField: r1 = r0->field_f
    //     0xb02c9c: ldur            w1, [x0, #0xf]
    // 0xb02ca0: DecompressPointer r1
    //     0xb02ca0: add             x1, x1, HEAP, lsl #32
    // 0xb02ca4: cmp             w1, NULL
    // 0xb02ca8: b.eq            #0xb0300c
    // 0xb02cac: r0 = of()
    //     0xb02cac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb02cb0: LoadField: r1 = r0->field_87
    //     0xb02cb0: ldur            w1, [x0, #0x87]
    // 0xb02cb4: DecompressPointer r1
    //     0xb02cb4: add             x1, x1, HEAP, lsl #32
    // 0xb02cb8: LoadField: r0 = r1->field_7
    //     0xb02cb8: ldur            w0, [x1, #7]
    // 0xb02cbc: DecompressPointer r0
    //     0xb02cbc: add             x0, x0, HEAP, lsl #32
    // 0xb02cc0: ldur            x1, [fp, #-8]
    // 0xb02cc4: stur            x0, [fp, #-0x58]
    // 0xb02cc8: LoadField: r2 = r1->field_f
    //     0xb02cc8: ldur            w2, [x1, #0xf]
    // 0xb02ccc: DecompressPointer r2
    //     0xb02ccc: add             x2, x2, HEAP, lsl #32
    // 0xb02cd0: cmp             w2, NULL
    // 0xb02cd4: b.eq            #0xb03010
    // 0xb02cd8: mov             x1, x2
    // 0xb02cdc: r0 = of()
    //     0xb02cdc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb02ce0: LoadField: r1 = r0->field_5b
    //     0xb02ce0: ldur            w1, [x0, #0x5b]
    // 0xb02ce4: DecompressPointer r1
    //     0xb02ce4: add             x1, x1, HEAP, lsl #32
    // 0xb02ce8: r16 = 16.000000
    //     0xb02ce8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb02cec: ldr             x16, [x16, #0x188]
    // 0xb02cf0: stp             x1, x16, [SP]
    // 0xb02cf4: ldur            x1, [fp, #-0x58]
    // 0xb02cf8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb02cf8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb02cfc: ldr             x4, [x4, #0xaa0]
    // 0xb02d00: r0 = copyWith()
    //     0xb02d00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb02d04: stur            x0, [fp, #-8]
    // 0xb02d08: r0 = Text()
    //     0xb02d08: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb02d0c: mov             x1, x0
    // 0xb02d10: ldur            x0, [fp, #-0x50]
    // 0xb02d14: StoreField: r1->field_b = r0
    //     0xb02d14: stur            w0, [x1, #0xb]
    // 0xb02d18: ldur            x0, [fp, #-8]
    // 0xb02d1c: StoreField: r1->field_13 = r0
    //     0xb02d1c: stur            w0, [x1, #0x13]
    // 0xb02d20: mov             x3, x1
    // 0xb02d24: ldur            x0, [fp, #-0x48]
    // 0xb02d28: ldur            x2, [fp, #-0x20]
    // 0xb02d2c: stur            x3, [fp, #-8]
    // 0xb02d30: r1 = Function '<anonymous closure>':.
    //     0xb02d30: add             x1, PP, #0x57, lsl #12  ; [pp+0x57ee0] AnonymousClosure: (0xb03014), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::cosmeticThemeSlider (0xb01a00)
    //     0xb02d34: ldr             x1, [x1, #0xee0]
    // 0xb02d38: r0 = AllocateClosure()
    //     0xb02d38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb02d3c: stur            x0, [fp, #-0x20]
    // 0xb02d40: r0 = TextButton()
    //     0xb02d40: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb02d44: mov             x1, x0
    // 0xb02d48: ldur            x0, [fp, #-0x20]
    // 0xb02d4c: stur            x1, [fp, #-0x50]
    // 0xb02d50: StoreField: r1->field_b = r0
    //     0xb02d50: stur            w0, [x1, #0xb]
    // 0xb02d54: r0 = false
    //     0xb02d54: add             x0, NULL, #0x30  ; false
    // 0xb02d58: StoreField: r1->field_27 = r0
    //     0xb02d58: stur            w0, [x1, #0x27]
    // 0xb02d5c: r2 = true
    //     0xb02d5c: add             x2, NULL, #0x20  ; true
    // 0xb02d60: StoreField: r1->field_2f = r2
    //     0xb02d60: stur            w2, [x1, #0x2f]
    // 0xb02d64: ldur            x2, [fp, #-8]
    // 0xb02d68: StoreField: r1->field_37 = r2
    //     0xb02d68: stur            w2, [x1, #0x37]
    // 0xb02d6c: r0 = TextButtonTheme()
    //     0xb02d6c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb02d70: mov             x1, x0
    // 0xb02d74: ldur            x0, [fp, #-0x48]
    // 0xb02d78: StoreField: r1->field_f = r0
    //     0xb02d78: stur            w0, [x1, #0xf]
    // 0xb02d7c: ldur            x0, [fp, #-0x50]
    // 0xb02d80: StoreField: r1->field_b = r0
    //     0xb02d80: stur            w0, [x1, #0xb]
    // 0xb02d84: mov             x5, x1
    // 0xb02d88: b               #0xb02da4
    // 0xb02d8c: r0 = Container()
    //     0xb02d8c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb02d90: mov             x1, x0
    // 0xb02d94: stur            x0, [fp, #-8]
    // 0xb02d98: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb02d98: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb02d9c: r0 = Container()
    //     0xb02d9c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb02da0: ldur            x5, [fp, #-8]
    // 0xb02da4: ldur            x4, [fp, #-0x28]
    // 0xb02da8: ldur            x3, [fp, #-0x10]
    // 0xb02dac: ldur            x2, [fp, #-0x40]
    // 0xb02db0: ldur            x0, [fp, #-0x38]
    // 0xb02db4: ldur            x1, [fp, #-0x30]
    // 0xb02db8: stur            x5, [fp, #-8]
    // 0xb02dbc: r0 = Container()
    //     0xb02dbc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb02dc0: stur            x0, [fp, #-0x20]
    // 0xb02dc4: r16 = Instance_Alignment
    //     0xb02dc4: add             x16, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xb02dc8: ldr             x16, [x16, #0xcb0]
    // 0xb02dcc: r30 = Instance_EdgeInsets
    //     0xb02dcc: add             lr, PP, #0x55, lsl #12  ; [pp+0x55968] Obj!EdgeInsets@d58581
    //     0xb02dd0: ldr             lr, [lr, #0x968]
    // 0xb02dd4: stp             lr, x16, [SP, #8]
    // 0xb02dd8: ldur            x16, [fp, #-8]
    // 0xb02ddc: str             x16, [SP]
    // 0xb02de0: mov             x1, x0
    // 0xb02de4: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x1, child, 0x3, margin, 0x2, null]
    //     0xb02de4: add             x4, PP, #0x55, lsl #12  ; [pp+0x55970] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x1, "child", 0x3, "margin", 0x2, Null]
    //     0xb02de8: ldr             x4, [x4, #0x970]
    // 0xb02dec: r0 = Container()
    //     0xb02dec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb02df0: r0 = Visibility()
    //     0xb02df0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb02df4: mov             x2, x0
    // 0xb02df8: ldur            x0, [fp, #-0x20]
    // 0xb02dfc: stur            x2, [fp, #-8]
    // 0xb02e00: StoreField: r2->field_b = r0
    //     0xb02e00: stur            w0, [x2, #0xb]
    // 0xb02e04: r0 = Instance_SizedBox
    //     0xb02e04: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb02e08: StoreField: r2->field_f = r0
    //     0xb02e08: stur            w0, [x2, #0xf]
    // 0xb02e0c: ldur            x0, [fp, #-0x30]
    // 0xb02e10: StoreField: r2->field_13 = r0
    //     0xb02e10: stur            w0, [x2, #0x13]
    // 0xb02e14: r0 = false
    //     0xb02e14: add             x0, NULL, #0x30  ; false
    // 0xb02e18: ArrayStore: r2[0] = r0  ; List_4
    //     0xb02e18: stur            w0, [x2, #0x17]
    // 0xb02e1c: StoreField: r2->field_1b = r0
    //     0xb02e1c: stur            w0, [x2, #0x1b]
    // 0xb02e20: StoreField: r2->field_1f = r0
    //     0xb02e20: stur            w0, [x2, #0x1f]
    // 0xb02e24: StoreField: r2->field_23 = r0
    //     0xb02e24: stur            w0, [x2, #0x23]
    // 0xb02e28: StoreField: r2->field_27 = r0
    //     0xb02e28: stur            w0, [x2, #0x27]
    // 0xb02e2c: StoreField: r2->field_2b = r0
    //     0xb02e2c: stur            w0, [x2, #0x2b]
    // 0xb02e30: r1 = <FlexParentData>
    //     0xb02e30: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb02e34: ldr             x1, [x1, #0xe00]
    // 0xb02e38: r0 = Expanded()
    //     0xb02e38: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb02e3c: mov             x3, x0
    // 0xb02e40: r0 = 1
    //     0xb02e40: movz            x0, #0x1
    // 0xb02e44: stur            x3, [fp, #-0x20]
    // 0xb02e48: StoreField: r3->field_13 = r0
    //     0xb02e48: stur            x0, [x3, #0x13]
    // 0xb02e4c: r0 = Instance_FlexFit
    //     0xb02e4c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb02e50: ldr             x0, [x0, #0xe08]
    // 0xb02e54: StoreField: r3->field_1b = r0
    //     0xb02e54: stur            w0, [x3, #0x1b]
    // 0xb02e58: ldur            x0, [fp, #-8]
    // 0xb02e5c: StoreField: r3->field_b = r0
    //     0xb02e5c: stur            w0, [x3, #0xb]
    // 0xb02e60: r1 = Null
    //     0xb02e60: mov             x1, NULL
    // 0xb02e64: r2 = 10
    //     0xb02e64: movz            x2, #0xa
    // 0xb02e68: r0 = AllocateArray()
    //     0xb02e68: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb02e6c: mov             x2, x0
    // 0xb02e70: ldur            x0, [fp, #-0x28]
    // 0xb02e74: stur            x2, [fp, #-8]
    // 0xb02e78: StoreField: r2->field_f = r0
    //     0xb02e78: stur            w0, [x2, #0xf]
    // 0xb02e7c: ldur            x0, [fp, #-0x10]
    // 0xb02e80: StoreField: r2->field_13 = r0
    //     0xb02e80: stur            w0, [x2, #0x13]
    // 0xb02e84: ldur            x0, [fp, #-0x40]
    // 0xb02e88: ArrayStore: r2[0] = r0  ; List_4
    //     0xb02e88: stur            w0, [x2, #0x17]
    // 0xb02e8c: ldur            x0, [fp, #-0x38]
    // 0xb02e90: StoreField: r2->field_1b = r0
    //     0xb02e90: stur            w0, [x2, #0x1b]
    // 0xb02e94: ldur            x0, [fp, #-0x20]
    // 0xb02e98: StoreField: r2->field_1f = r0
    //     0xb02e98: stur            w0, [x2, #0x1f]
    // 0xb02e9c: r1 = <Widget>
    //     0xb02e9c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb02ea0: r0 = AllocateGrowableArray()
    //     0xb02ea0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb02ea4: mov             x1, x0
    // 0xb02ea8: ldur            x0, [fp, #-8]
    // 0xb02eac: stur            x1, [fp, #-0x10]
    // 0xb02eb0: StoreField: r1->field_f = r0
    //     0xb02eb0: stur            w0, [x1, #0xf]
    // 0xb02eb4: r0 = 10
    //     0xb02eb4: movz            x0, #0xa
    // 0xb02eb8: StoreField: r1->field_b = r0
    //     0xb02eb8: stur            w0, [x1, #0xb]
    // 0xb02ebc: r0 = Column()
    //     0xb02ebc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb02ec0: mov             x1, x0
    // 0xb02ec4: r0 = Instance_Axis
    //     0xb02ec4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb02ec8: stur            x1, [fp, #-8]
    // 0xb02ecc: StoreField: r1->field_f = r0
    //     0xb02ecc: stur            w0, [x1, #0xf]
    // 0xb02ed0: r0 = Instance_MainAxisAlignment
    //     0xb02ed0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb02ed4: ldr             x0, [x0, #0xa08]
    // 0xb02ed8: StoreField: r1->field_13 = r0
    //     0xb02ed8: stur            w0, [x1, #0x13]
    // 0xb02edc: r0 = Instance_MainAxisSize
    //     0xb02edc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb02ee0: ldr             x0, [x0, #0xdd0]
    // 0xb02ee4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb02ee4: stur            w0, [x1, #0x17]
    // 0xb02ee8: r0 = Instance_CrossAxisAlignment
    //     0xb02ee8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb02eec: ldr             x0, [x0, #0x890]
    // 0xb02ef0: StoreField: r1->field_1b = r0
    //     0xb02ef0: stur            w0, [x1, #0x1b]
    // 0xb02ef4: r0 = Instance_VerticalDirection
    //     0xb02ef4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb02ef8: ldr             x0, [x0, #0xa20]
    // 0xb02efc: StoreField: r1->field_23 = r0
    //     0xb02efc: stur            w0, [x1, #0x23]
    // 0xb02f00: r0 = Instance_Clip
    //     0xb02f00: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb02f04: ldr             x0, [x0, #0x38]
    // 0xb02f08: StoreField: r1->field_2b = r0
    //     0xb02f08: stur            w0, [x1, #0x2b]
    // 0xb02f0c: StoreField: r1->field_2f = rZR
    //     0xb02f0c: stur            xzr, [x1, #0x2f]
    // 0xb02f10: ldur            x0, [fp, #-0x10]
    // 0xb02f14: StoreField: r1->field_b = r0
    //     0xb02f14: stur            w0, [x1, #0xb]
    // 0xb02f18: r0 = AnimatedContainer()
    //     0xb02f18: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xb02f1c: stur            x0, [fp, #-0x10]
    // 0xb02f20: r16 = Instance_Cubic
    //     0xb02f20: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xb02f24: ldr             x16, [x16, #0xaf8]
    // 0xb02f28: str             x16, [SP]
    // 0xb02f2c: mov             x1, x0
    // 0xb02f30: ldur            x2, [fp, #-8]
    // 0xb02f34: r3 = Instance_Duration
    //     0xb02f34: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb02f38: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xb02f38: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xb02f3c: ldr             x4, [x4, #0xbc8]
    // 0xb02f40: r0 = AnimatedContainer()
    //     0xb02f40: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xb02f44: ldur            x0, [fp, #-0x10]
    // 0xb02f48: LeaveFrame
    //     0xb02f48: mov             SP, fp
    //     0xb02f4c: ldp             fp, lr, [SP], #0x10
    // 0xb02f50: ret
    //     0xb02f50: ret             
    // 0xb02f54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb02f54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb02f58: b               #0xb01a24
    // 0xb02f5c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb02f5c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb02f60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb02f60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb02f64: r9 = _imagePageController
    //     0xb02f64: add             x9, PP, #0x57, lsl #12  ; [pp+0x57ee8] Field <_GroupCarouselItemViewState@1494318134._imagePageController@1494318134>: late (offset: 0x18)
    //     0xb02f68: ldr             x9, [x9, #0xee8]
    // 0xb02f6c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb02f6c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb02f70: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb02f70: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb02f74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb02f74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb02f78: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb02f78: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb02f7c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb02f7c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb02f80: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb02f80: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb02f84: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb02f84: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb02f88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb02f88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb02f8c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb02f8c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb02f90: SaveReg d0
    //     0xb02f90: str             q0, [SP, #-0x10]!
    // 0xb02f94: SaveReg r0
    //     0xb02f94: str             x0, [SP, #-8]!
    // 0xb02f98: r0 = 74
    //     0xb02f98: movz            x0, #0x4a
    // 0xb02f9c: r30 = DoubleToIntegerStub
    //     0xb02f9c: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xb02fa0: LoadField: r30 = r30->field_7
    //     0xb02fa0: ldur            lr, [lr, #7]
    // 0xb02fa4: blr             lr
    // 0xb02fa8: mov             x1, x0
    // 0xb02fac: RestoreReg r0
    //     0xb02fac: ldr             x0, [SP], #8
    // 0xb02fb0: RestoreReg d0
    //     0xb02fb0: ldr             q0, [SP], #0x10
    // 0xb02fb4: b               #0xb02070
    // 0xb02fb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb02fb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb02fbc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb02fbc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb02fc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb02fc0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb02fc4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb02fc4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb02fc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb02fc8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb02fcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb02fcc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb02fd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb02fd0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb02fd4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb02fd4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb02fd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb02fd8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb02fdc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb02fdc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb02fe0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb02fe0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb02fe4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb02fe4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb02fe8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb02fe8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb02fec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb02fec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb02ff0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb02ff0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb02ff4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb02ff4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb02ff8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb02ff8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb02ffc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb02ffc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb03000: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb03000: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb03004: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb03004: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb03008: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb03008: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0300c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0300c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb03010: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb03010: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb03014, size: 0x110
    // 0xb03014: EnterFrame
    //     0xb03014: stp             fp, lr, [SP, #-0x10]!
    //     0xb03018: mov             fp, SP
    // 0xb0301c: AllocStack(0x20)
    //     0xb0301c: sub             SP, SP, #0x20
    // 0xb03020: SetupParameters()
    //     0xb03020: ldr             x0, [fp, #0x10]
    //     0xb03024: ldur            w1, [x0, #0x17]
    //     0xb03028: add             x1, x1, HEAP, lsl #32
    //     0xb0302c: stur            x1, [fp, #-8]
    // 0xb03030: CheckStackOverflow
    //     0xb03030: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb03034: cmp             SP, x16
    //     0xb03038: b.ls            #0xb03110
    // 0xb0303c: LoadField: r0 = r1->field_13
    //     0xb0303c: ldur            w0, [x1, #0x13]
    // 0xb03040: DecompressPointer r0
    //     0xb03040: add             x0, x0, HEAP, lsl #32
    // 0xb03044: cmp             w0, NULL
    // 0xb03048: b.eq            #0xb03118
    // 0xb0304c: r17 = 275
    //     0xb0304c: movz            x17, #0x113
    // 0xb03050: ldr             w2, [x0, x17]
    // 0xb03054: DecompressPointer r2
    //     0xb03054: add             x2, x2, HEAP, lsl #32
    // 0xb03058: cmp             w2, NULL
    // 0xb0305c: b.eq            #0xb03064
    // 0xb03060: tbz             w2, #4, #0xb03100
    // 0xb03064: LoadField: r0 = r1->field_f
    //     0xb03064: ldur            w0, [x1, #0xf]
    // 0xb03068: DecompressPointer r0
    //     0xb03068: add             x0, x0, HEAP, lsl #32
    // 0xb0306c: LoadField: r2 = r0->field_b
    //     0xb0306c: ldur            w2, [x0, #0xb]
    // 0xb03070: DecompressPointer r2
    //     0xb03070: add             x2, x2, HEAP, lsl #32
    // 0xb03074: cmp             w2, NULL
    // 0xb03078: b.eq            #0xb0311c
    // 0xb0307c: LoadField: r0 = r2->field_37
    //     0xb0307c: ldur            w0, [x2, #0x37]
    // 0xb03080: DecompressPointer r0
    //     0xb03080: add             x0, x0, HEAP, lsl #32
    // 0xb03084: r16 = "add_to_bag"
    //     0xb03084: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xb03088: ldr             x16, [x16, #0xa38]
    // 0xb0308c: stp             x16, x0, [SP]
    // 0xb03090: r4 = 0
    //     0xb03090: movz            x4, #0
    // 0xb03094: ldr             x0, [SP, #8]
    // 0xb03098: r16 = UnlinkedCall_0x613b5c
    //     0xb03098: add             x16, PP, #0x57, lsl #12  ; [pp+0x57ef0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb0309c: add             x16, x16, #0xef0
    // 0xb030a0: ldp             x5, lr, [x16]
    // 0xb030a4: blr             lr
    // 0xb030a8: ldur            x0, [fp, #-8]
    // 0xb030ac: LoadField: r1 = r0->field_f
    //     0xb030ac: ldur            w1, [x0, #0xf]
    // 0xb030b0: DecompressPointer r1
    //     0xb030b0: add             x1, x1, HEAP, lsl #32
    // 0xb030b4: LoadField: r2 = r1->field_b
    //     0xb030b4: ldur            w2, [x1, #0xb]
    // 0xb030b8: DecompressPointer r2
    //     0xb030b8: add             x2, x2, HEAP, lsl #32
    // 0xb030bc: stur            x2, [fp, #-0x10]
    // 0xb030c0: cmp             w2, NULL
    // 0xb030c4: b.eq            #0xb03120
    // 0xb030c8: LoadField: r1 = r0->field_13
    //     0xb030c8: ldur            w1, [x0, #0x13]
    // 0xb030cc: DecompressPointer r1
    //     0xb030cc: add             x1, x1, HEAP, lsl #32
    // 0xb030d0: cmp             w1, NULL
    // 0xb030d4: b.ne            #0xb030e0
    // 0xb030d8: r0 = WidgetEntity()
    //     0xb030d8: bl              #0xa749ec  ; AllocateWidgetEntityStub -> WidgetEntity (size=0x140)
    // 0xb030dc: mov             x1, x0
    // 0xb030e0: ldur            x0, [fp, #-0x10]
    // 0xb030e4: LoadField: r2 = r0->field_43
    //     0xb030e4: ldur            w2, [x0, #0x43]
    // 0xb030e8: DecompressPointer r2
    //     0xb030e8: add             x2, x2, HEAP, lsl #32
    // 0xb030ec: stp             x1, x2, [SP]
    // 0xb030f0: mov             x0, x2
    // 0xb030f4: ClosureCall
    //     0xb030f4: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb030f8: ldur            x2, [x0, #0x1f]
    //     0xb030fc: blr             x2
    // 0xb03100: r0 = Null
    //     0xb03100: mov             x0, NULL
    // 0xb03104: LeaveFrame
    //     0xb03104: mov             SP, fp
    //     0xb03108: ldp             fp, lr, [SP], #0x10
    // 0xb0310c: ret
    //     0xb0310c: ret             
    // 0xb03110: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb03110: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb03114: b               #0xb0303c
    // 0xb03118: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb03118: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb0311c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0311c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb03120: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb03120: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] AnimatedContainer <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb03124, size: 0xc8
    // 0xb03124: EnterFrame
    //     0xb03124: stp             fp, lr, [SP, #-0x10]!
    //     0xb03128: mov             fp, SP
    // 0xb0312c: AllocStack(0x20)
    //     0xb0312c: sub             SP, SP, #0x20
    // 0xb03130: SetupParameters()
    //     0xb03130: ldr             x0, [fp, #0x20]
    //     0xb03134: ldur            w1, [x0, #0x17]
    //     0xb03138: add             x1, x1, HEAP, lsl #32
    // 0xb0313c: CheckStackOverflow
    //     0xb0313c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb03140: cmp             SP, x16
    //     0xb03144: b.ls            #0xb031dc
    // 0xb03148: LoadField: r2 = r1->field_f
    //     0xb03148: ldur            w2, [x1, #0xf]
    // 0xb0314c: DecompressPointer r2
    //     0xb0314c: add             x2, x2, HEAP, lsl #32
    // 0xb03150: stur            x2, [fp, #-0x10]
    // 0xb03154: LoadField: r0 = r1->field_13
    //     0xb03154: ldur            w0, [x1, #0x13]
    // 0xb03158: DecompressPointer r0
    //     0xb03158: add             x0, x0, HEAP, lsl #32
    // 0xb0315c: cmp             w0, NULL
    // 0xb03160: b.eq            #0xb031e4
    // 0xb03164: LoadField: r3 = r0->field_e7
    //     0xb03164: ldur            w3, [x0, #0xe7]
    // 0xb03168: DecompressPointer r3
    //     0xb03168: add             x3, x3, HEAP, lsl #32
    // 0xb0316c: stur            x3, [fp, #-8]
    // 0xb03170: LoadField: r0 = r2->field_b
    //     0xb03170: ldur            w0, [x2, #0xb]
    // 0xb03174: DecompressPointer r0
    //     0xb03174: add             x0, x0, HEAP, lsl #32
    // 0xb03178: cmp             w0, NULL
    // 0xb0317c: b.eq            #0xb031e8
    // 0xb03180: LoadField: r4 = r0->field_b
    //     0xb03180: ldur            w4, [x0, #0xb]
    // 0xb03184: DecompressPointer r4
    //     0xb03184: add             x4, x4, HEAP, lsl #32
    // 0xb03188: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb03188: ldur            w0, [x1, #0x17]
    // 0xb0318c: DecompressPointer r0
    //     0xb0318c: add             x0, x0, HEAP, lsl #32
    // 0xb03190: r1 = LoadClassIdInstr(r4)
    //     0xb03190: ldur            x1, [x4, #-1]
    //     0xb03194: ubfx            x1, x1, #0xc, #0x14
    // 0xb03198: stp             x0, x4, [SP]
    // 0xb0319c: mov             x0, x1
    // 0xb031a0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb031a0: sub             lr, x0, #0xb7
    //     0xb031a4: ldr             lr, [x21, lr, lsl #3]
    //     0xb031a8: blr             lr
    // 0xb031ac: mov             x1, x0
    // 0xb031b0: ldr             x0, [fp, #0x10]
    // 0xb031b4: r5 = LoadInt32Instr(r0)
    //     0xb031b4: sbfx            x5, x0, #1, #0x1f
    //     0xb031b8: tbz             w0, #0, #0xb031c0
    //     0xb031bc: ldur            x5, [x0, #7]
    // 0xb031c0: mov             x3, x1
    // 0xb031c4: ldur            x1, [fp, #-0x10]
    // 0xb031c8: ldur            x2, [fp, #-8]
    // 0xb031cc: r0 = imageSlider()
    //     0xb031cc: bl              #0xb031ec  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::imageSlider
    // 0xb031d0: LeaveFrame
    //     0xb031d0: mov             SP, fp
    //     0xb031d4: ldp             fp, lr, [SP], #0x10
    // 0xb031d8: ret
    //     0xb031d8: ret             
    // 0xb031dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb031dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb031e0: b               #0xb03148
    // 0xb031e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb031e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb031e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb031e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ imageSlider(/* No info */) {
    // ** addr: 0xb031ec, size: 0x1120
    // 0xb031ec: EnterFrame
    //     0xb031ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb031f0: mov             fp, SP
    // 0xb031f4: AllocStack(0x90)
    //     0xb031f4: sub             SP, SP, #0x90
    // 0xb031f8: SetupParameters(_GroupCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */)
    //     0xb031f8: mov             x0, x1
    //     0xb031fc: stur            x1, [fp, #-8]
    //     0xb03200: mov             x1, x5
    //     0xb03204: stur            x2, [fp, #-0x10]
    //     0xb03208: stur            x3, [fp, #-0x18]
    //     0xb0320c: stur            x5, [fp, #-0x20]
    // 0xb03210: CheckStackOverflow
    //     0xb03210: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb03214: cmp             SP, x16
    //     0xb03218: b.ls            #0xb042c0
    // 0xb0321c: r1 = 2
    //     0xb0321c: movz            x1, #0x2
    // 0xb03220: r0 = AllocateContext()
    //     0xb03220: bl              #0x16f6108  ; AllocateContextStub
    // 0xb03224: mov             x1, x0
    // 0xb03228: ldur            x0, [fp, #-8]
    // 0xb0322c: stur            x1, [fp, #-0x28]
    // 0xb03230: StoreField: r1->field_f = r0
    //     0xb03230: stur            w0, [x1, #0xf]
    // 0xb03234: ldur            x2, [fp, #-0x18]
    // 0xb03238: StoreField: r1->field_13 = r2
    //     0xb03238: stur            w2, [x1, #0x13]
    // 0xb0323c: r0 = Radius()
    //     0xb0323c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb03240: d0 = 10.000000
    //     0xb03240: fmov            d0, #10.00000000
    // 0xb03244: stur            x0, [fp, #-0x18]
    // 0xb03248: StoreField: r0->field_7 = d0
    //     0xb03248: stur            d0, [x0, #7]
    // 0xb0324c: StoreField: r0->field_f = d0
    //     0xb0324c: stur            d0, [x0, #0xf]
    // 0xb03250: r0 = BorderRadius()
    //     0xb03250: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb03254: mov             x1, x0
    // 0xb03258: ldur            x0, [fp, #-0x18]
    // 0xb0325c: stur            x1, [fp, #-0x30]
    // 0xb03260: StoreField: r1->field_7 = r0
    //     0xb03260: stur            w0, [x1, #7]
    // 0xb03264: StoreField: r1->field_b = r0
    //     0xb03264: stur            w0, [x1, #0xb]
    // 0xb03268: StoreField: r1->field_f = r0
    //     0xb03268: stur            w0, [x1, #0xf]
    // 0xb0326c: StoreField: r1->field_13 = r0
    //     0xb0326c: stur            w0, [x1, #0x13]
    // 0xb03270: r0 = RoundedRectangleBorder()
    //     0xb03270: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb03274: mov             x3, x0
    // 0xb03278: ldur            x0, [fp, #-0x30]
    // 0xb0327c: stur            x3, [fp, #-0x18]
    // 0xb03280: StoreField: r3->field_b = r0
    //     0xb03280: stur            w0, [x3, #0xb]
    // 0xb03284: r4 = Instance_BorderSide
    //     0xb03284: add             x4, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb03288: ldr             x4, [x4, #0xe20]
    // 0xb0328c: StoreField: r3->field_7 = r4
    //     0xb0328c: stur            w4, [x3, #7]
    // 0xb03290: ldur            x2, [fp, #-0x10]
    // 0xb03294: cmp             w2, NULL
    // 0xb03298: b.eq            #0xb042c8
    // 0xb0329c: LoadField: r0 = r2->field_b
    //     0xb0329c: ldur            w0, [x2, #0xb]
    // 0xb032a0: r1 = LoadInt32Instr(r0)
    //     0xb032a0: sbfx            x1, x0, #1, #0x1f
    // 0xb032a4: mov             x0, x1
    // 0xb032a8: ldur            x1, [fp, #-0x20]
    // 0xb032ac: cmp             x1, x0
    // 0xb032b0: b.hs            #0xb042cc
    // 0xb032b4: LoadField: r0 = r2->field_f
    //     0xb032b4: ldur            w0, [x2, #0xf]
    // 0xb032b8: DecompressPointer r0
    //     0xb032b8: add             x0, x0, HEAP, lsl #32
    // 0xb032bc: ldur            x1, [fp, #-0x20]
    // 0xb032c0: ArrayLoad: r2 = r0[r1]  ; Unknown_4
    //     0xb032c0: add             x16, x0, x1, lsl #2
    //     0xb032c4: ldur            w2, [x16, #0xf]
    // 0xb032c8: DecompressPointer r2
    //     0xb032c8: add             x2, x2, HEAP, lsl #32
    // 0xb032cc: LoadField: r0 = r2->field_b
    //     0xb032cc: ldur            w0, [x2, #0xb]
    // 0xb032d0: DecompressPointer r0
    //     0xb032d0: add             x0, x0, HEAP, lsl #32
    // 0xb032d4: stur            x0, [fp, #-0x10]
    // 0xb032d8: cmp             w0, NULL
    // 0xb032dc: b.eq            #0xb042d0
    // 0xb032e0: r1 = Function '<anonymous closure>':.
    //     0xb032e0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57f00] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb032e4: ldr             x1, [x1, #0xf00]
    // 0xb032e8: r2 = Null
    //     0xb032e8: mov             x2, NULL
    // 0xb032ec: r0 = AllocateClosure()
    //     0xb032ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb032f0: r1 = Function '<anonymous closure>':.
    //     0xb032f0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57f08] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb032f4: ldr             x1, [x1, #0xf08]
    // 0xb032f8: r2 = Null
    //     0xb032f8: mov             x2, NULL
    // 0xb032fc: stur            x0, [fp, #-0x30]
    // 0xb03300: r0 = AllocateClosure()
    //     0xb03300: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb03304: stur            x0, [fp, #-0x38]
    // 0xb03308: r0 = CachedNetworkImage()
    //     0xb03308: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb0330c: stur            x0, [fp, #-0x40]
    // 0xb03310: r16 = Instance_BoxFit
    //     0xb03310: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb03314: ldr             x16, [x16, #0xb18]
    // 0xb03318: ldur            lr, [fp, #-0x30]
    // 0xb0331c: stp             lr, x16, [SP, #8]
    // 0xb03320: ldur            x16, [fp, #-0x38]
    // 0xb03324: str             x16, [SP]
    // 0xb03328: mov             x1, x0
    // 0xb0332c: ldur            x2, [fp, #-0x10]
    // 0xb03330: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xb03330: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xb03334: ldr             x4, [x4, #0x638]
    // 0xb03338: r0 = CachedNetworkImage()
    //     0xb03338: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb0333c: r0 = Card()
    //     0xb0333c: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb03340: mov             x1, x0
    // 0xb03344: r0 = Instance_Color
    //     0xb03344: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb03348: ldr             x0, [x0, #0xf88]
    // 0xb0334c: stur            x1, [fp, #-0x10]
    // 0xb03350: StoreField: r1->field_b = r0
    //     0xb03350: stur            w0, [x1, #0xb]
    // 0xb03354: r0 = 0.000000
    //     0xb03354: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb03358: ArrayStore: r1[0] = r0  ; List_4
    //     0xb03358: stur            w0, [x1, #0x17]
    // 0xb0335c: ldur            x0, [fp, #-0x18]
    // 0xb03360: StoreField: r1->field_1b = r0
    //     0xb03360: stur            w0, [x1, #0x1b]
    // 0xb03364: r0 = true
    //     0xb03364: add             x0, NULL, #0x20  ; true
    // 0xb03368: StoreField: r1->field_1f = r0
    //     0xb03368: stur            w0, [x1, #0x1f]
    // 0xb0336c: r2 = Instance_EdgeInsets
    //     0xb0336c: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb03370: StoreField: r1->field_27 = r2
    //     0xb03370: stur            w2, [x1, #0x27]
    // 0xb03374: r2 = Instance_Clip
    //     0xb03374: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xb03378: ldr             x2, [x2, #0xb50]
    // 0xb0337c: StoreField: r1->field_23 = r2
    //     0xb0337c: stur            w2, [x1, #0x23]
    // 0xb03380: ldur            x2, [fp, #-0x40]
    // 0xb03384: StoreField: r1->field_2f = r2
    //     0xb03384: stur            w2, [x1, #0x2f]
    // 0xb03388: StoreField: r1->field_2b = r0
    //     0xb03388: stur            w0, [x1, #0x2b]
    // 0xb0338c: r2 = Instance__CardVariant
    //     0xb0338c: add             x2, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb03390: ldr             x2, [x2, #0xa68]
    // 0xb03394: StoreField: r1->field_33 = r2
    //     0xb03394: stur            w2, [x1, #0x33]
    // 0xb03398: r0 = Center()
    //     0xb03398: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb0339c: mov             x1, x0
    // 0xb033a0: r0 = Instance_Alignment
    //     0xb033a0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb033a4: ldr             x0, [x0, #0xb10]
    // 0xb033a8: stur            x1, [fp, #-0x30]
    // 0xb033ac: StoreField: r1->field_f = r0
    //     0xb033ac: stur            w0, [x1, #0xf]
    // 0xb033b0: ldur            x0, [fp, #-0x10]
    // 0xb033b4: StoreField: r1->field_b = r0
    //     0xb033b4: stur            w0, [x1, #0xb]
    // 0xb033b8: ldur            x2, [fp, #-0x28]
    // 0xb033bc: LoadField: r0 = r2->field_13
    //     0xb033bc: ldur            w0, [x2, #0x13]
    // 0xb033c0: DecompressPointer r0
    //     0xb033c0: add             x0, x0, HEAP, lsl #32
    // 0xb033c4: stur            x0, [fp, #-0x18]
    // 0xb033c8: cmp             w0, NULL
    // 0xb033cc: b.ne            #0xb033d8
    // 0xb033d0: r3 = Null
    //     0xb033d0: mov             x3, NULL
    // 0xb033d4: b               #0xb03408
    // 0xb033d8: r17 = 295
    //     0xb033d8: movz            x17, #0x127
    // 0xb033dc: ldr             w3, [x0, x17]
    // 0xb033e0: DecompressPointer r3
    //     0xb033e0: add             x3, x3, HEAP, lsl #32
    // 0xb033e4: cmp             w3, NULL
    // 0xb033e8: b.ne            #0xb033f4
    // 0xb033ec: r3 = Null
    //     0xb033ec: mov             x3, NULL
    // 0xb033f0: b               #0xb03408
    // 0xb033f4: LoadField: r4 = r3->field_7
    //     0xb033f4: ldur            w4, [x3, #7]
    // 0xb033f8: cbnz            w4, #0xb03404
    // 0xb033fc: r3 = false
    //     0xb033fc: add             x3, NULL, #0x30  ; false
    // 0xb03400: b               #0xb03408
    // 0xb03404: r3 = true
    //     0xb03404: add             x3, NULL, #0x20  ; true
    // 0xb03408: cmp             w3, NULL
    // 0xb0340c: b.ne            #0xb03414
    // 0xb03410: r3 = false
    //     0xb03410: add             x3, NULL, #0x30  ; false
    // 0xb03414: stur            x3, [fp, #-0x10]
    // 0xb03418: cmp             w0, NULL
    // 0xb0341c: b.ne            #0xb03428
    // 0xb03420: r4 = Null
    //     0xb03420: mov             x4, NULL
    // 0xb03424: b               #0xb03434
    // 0xb03428: r17 = 271
    //     0xb03428: movz            x17, #0x10f
    // 0xb0342c: ldr             w4, [x0, x17]
    // 0xb03430: DecompressPointer r4
    //     0xb03430: add             x4, x4, HEAP, lsl #32
    // 0xb03434: cmp             w4, NULL
    // 0xb03438: b.ne            #0xb03450
    // 0xb0343c: mov             x4, x2
    // 0xb03440: r0 = Instance_Alignment
    //     0xb03440: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb03444: ldr             x0, [x0, #0xfa0]
    // 0xb03448: r2 = 4
    //     0xb03448: movz            x2, #0x4
    // 0xb0344c: b               #0xb03914
    // 0xb03450: tbnz            w4, #4, #0xb03904
    // 0xb03454: ldur            x4, [fp, #-8]
    // 0xb03458: r0 = Radius()
    //     0xb03458: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb0345c: d0 = 8.000000
    //     0xb0345c: fmov            d0, #8.00000000
    // 0xb03460: stur            x0, [fp, #-0x38]
    // 0xb03464: StoreField: r0->field_7 = d0
    //     0xb03464: stur            d0, [x0, #7]
    // 0xb03468: StoreField: r0->field_f = d0
    //     0xb03468: stur            d0, [x0, #0xf]
    // 0xb0346c: r0 = BorderRadius()
    //     0xb0346c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb03470: mov             x1, x0
    // 0xb03474: ldur            x0, [fp, #-0x38]
    // 0xb03478: stur            x1, [fp, #-0x40]
    // 0xb0347c: StoreField: r1->field_7 = r0
    //     0xb0347c: stur            w0, [x1, #7]
    // 0xb03480: StoreField: r1->field_b = r0
    //     0xb03480: stur            w0, [x1, #0xb]
    // 0xb03484: StoreField: r1->field_f = r0
    //     0xb03484: stur            w0, [x1, #0xf]
    // 0xb03488: StoreField: r1->field_13 = r0
    //     0xb03488: stur            w0, [x1, #0x13]
    // 0xb0348c: ldur            x0, [fp, #-8]
    // 0xb03490: LoadField: r2 = r0->field_b
    //     0xb03490: ldur            w2, [x0, #0xb]
    // 0xb03494: DecompressPointer r2
    //     0xb03494: add             x2, x2, HEAP, lsl #32
    // 0xb03498: cmp             w2, NULL
    // 0xb0349c: b.eq            #0xb042d4
    // 0xb034a0: LoadField: r3 = r2->field_2f
    //     0xb034a0: ldur            w3, [x2, #0x2f]
    // 0xb034a4: DecompressPointer r3
    //     0xb034a4: add             x3, x3, HEAP, lsl #32
    // 0xb034a8: LoadField: r2 = r3->field_13
    //     0xb034a8: ldur            w2, [x3, #0x13]
    // 0xb034ac: DecompressPointer r2
    //     0xb034ac: add             x2, x2, HEAP, lsl #32
    // 0xb034b0: stur            x2, [fp, #-0x38]
    // 0xb034b4: cmp             w2, NULL
    // 0xb034b8: b.ne            #0xb034c4
    // 0xb034bc: r3 = Null
    //     0xb034bc: mov             x3, NULL
    // 0xb034c0: b               #0xb034cc
    // 0xb034c4: LoadField: r3 = r2->field_7
    //     0xb034c4: ldur            w3, [x2, #7]
    // 0xb034c8: DecompressPointer r3
    //     0xb034c8: add             x3, x3, HEAP, lsl #32
    // 0xb034cc: cmp             w3, NULL
    // 0xb034d0: b.ne            #0xb034dc
    // 0xb034d4: r3 = 0
    //     0xb034d4: movz            x3, #0
    // 0xb034d8: b               #0xb034ec
    // 0xb034dc: r4 = LoadInt32Instr(r3)
    //     0xb034dc: sbfx            x4, x3, #1, #0x1f
    //     0xb034e0: tbz             w3, #0, #0xb034e8
    //     0xb034e4: ldur            x4, [x3, #7]
    // 0xb034e8: mov             x3, x4
    // 0xb034ec: stur            x3, [fp, #-0x50]
    // 0xb034f0: cmp             w2, NULL
    // 0xb034f4: b.ne            #0xb03500
    // 0xb034f8: r4 = Null
    //     0xb034f8: mov             x4, NULL
    // 0xb034fc: b               #0xb03508
    // 0xb03500: LoadField: r4 = r2->field_b
    //     0xb03500: ldur            w4, [x2, #0xb]
    // 0xb03504: DecompressPointer r4
    //     0xb03504: add             x4, x4, HEAP, lsl #32
    // 0xb03508: cmp             w4, NULL
    // 0xb0350c: b.ne            #0xb03518
    // 0xb03510: r4 = 0
    //     0xb03510: movz            x4, #0
    // 0xb03514: b               #0xb03528
    // 0xb03518: r5 = LoadInt32Instr(r4)
    //     0xb03518: sbfx            x5, x4, #1, #0x1f
    //     0xb0351c: tbz             w4, #0, #0xb03524
    //     0xb03520: ldur            x5, [x4, #7]
    // 0xb03524: mov             x4, x5
    // 0xb03528: stur            x4, [fp, #-0x48]
    // 0xb0352c: cmp             w2, NULL
    // 0xb03530: b.ne            #0xb0353c
    // 0xb03534: r5 = Null
    //     0xb03534: mov             x5, NULL
    // 0xb03538: b               #0xb03544
    // 0xb0353c: LoadField: r5 = r2->field_f
    //     0xb0353c: ldur            w5, [x2, #0xf]
    // 0xb03540: DecompressPointer r5
    //     0xb03540: add             x5, x5, HEAP, lsl #32
    // 0xb03544: cmp             w5, NULL
    // 0xb03548: b.ne            #0xb03554
    // 0xb0354c: r5 = 0
    //     0xb0354c: movz            x5, #0
    // 0xb03550: b               #0xb03564
    // 0xb03554: r6 = LoadInt32Instr(r5)
    //     0xb03554: sbfx            x6, x5, #1, #0x1f
    //     0xb03558: tbz             w5, #0, #0xb03560
    //     0xb0355c: ldur            x6, [x5, #7]
    // 0xb03560: mov             x5, x6
    // 0xb03564: stur            x5, [fp, #-0x20]
    // 0xb03568: r0 = Color()
    //     0xb03568: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb0356c: mov             x1, x0
    // 0xb03570: r0 = Instance_ColorSpace
    //     0xb03570: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb03574: stur            x1, [fp, #-0x58]
    // 0xb03578: StoreField: r1->field_27 = r0
    //     0xb03578: stur            w0, [x1, #0x27]
    // 0xb0357c: d0 = 1.000000
    //     0xb0357c: fmov            d0, #1.00000000
    // 0xb03580: StoreField: r1->field_7 = d0
    //     0xb03580: stur            d0, [x1, #7]
    // 0xb03584: ldur            x2, [fp, #-0x50]
    // 0xb03588: ubfx            x2, x2, #0, #0x20
    // 0xb0358c: and             w3, w2, #0xff
    // 0xb03590: ubfx            x3, x3, #0, #0x20
    // 0xb03594: scvtf           d0, x3
    // 0xb03598: d1 = 255.000000
    //     0xb03598: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb0359c: fdiv            d2, d0, d1
    // 0xb035a0: StoreField: r1->field_f = d2
    //     0xb035a0: stur            d2, [x1, #0xf]
    // 0xb035a4: ldur            x2, [fp, #-0x48]
    // 0xb035a8: ubfx            x2, x2, #0, #0x20
    // 0xb035ac: and             w3, w2, #0xff
    // 0xb035b0: ubfx            x3, x3, #0, #0x20
    // 0xb035b4: scvtf           d0, x3
    // 0xb035b8: fdiv            d2, d0, d1
    // 0xb035bc: ArrayStore: r1[0] = d2  ; List_8
    //     0xb035bc: stur            d2, [x1, #0x17]
    // 0xb035c0: ldur            x2, [fp, #-0x20]
    // 0xb035c4: ubfx            x2, x2, #0, #0x20
    // 0xb035c8: and             w3, w2, #0xff
    // 0xb035cc: ubfx            x3, x3, #0, #0x20
    // 0xb035d0: scvtf           d0, x3
    // 0xb035d4: fdiv            d2, d0, d1
    // 0xb035d8: StoreField: r1->field_1f = d2
    //     0xb035d8: stur            d2, [x1, #0x1f]
    // 0xb035dc: ldur            x2, [fp, #-0x38]
    // 0xb035e0: cmp             w2, NULL
    // 0xb035e4: b.ne            #0xb035f0
    // 0xb035e8: r3 = Null
    //     0xb035e8: mov             x3, NULL
    // 0xb035ec: b               #0xb035f8
    // 0xb035f0: LoadField: r3 = r2->field_7
    //     0xb035f0: ldur            w3, [x2, #7]
    // 0xb035f4: DecompressPointer r3
    //     0xb035f4: add             x3, x3, HEAP, lsl #32
    // 0xb035f8: cmp             w3, NULL
    // 0xb035fc: b.ne            #0xb03608
    // 0xb03600: r3 = 0
    //     0xb03600: movz            x3, #0
    // 0xb03604: b               #0xb03618
    // 0xb03608: r4 = LoadInt32Instr(r3)
    //     0xb03608: sbfx            x4, x3, #1, #0x1f
    //     0xb0360c: tbz             w3, #0, #0xb03614
    //     0xb03610: ldur            x4, [x3, #7]
    // 0xb03614: mov             x3, x4
    // 0xb03618: stur            x3, [fp, #-0x50]
    // 0xb0361c: cmp             w2, NULL
    // 0xb03620: b.ne            #0xb0362c
    // 0xb03624: r4 = Null
    //     0xb03624: mov             x4, NULL
    // 0xb03628: b               #0xb03634
    // 0xb0362c: LoadField: r4 = r2->field_b
    //     0xb0362c: ldur            w4, [x2, #0xb]
    // 0xb03630: DecompressPointer r4
    //     0xb03630: add             x4, x4, HEAP, lsl #32
    // 0xb03634: cmp             w4, NULL
    // 0xb03638: b.ne            #0xb03644
    // 0xb0363c: r4 = 0
    //     0xb0363c: movz            x4, #0
    // 0xb03640: b               #0xb03654
    // 0xb03644: r5 = LoadInt32Instr(r4)
    //     0xb03644: sbfx            x5, x4, #1, #0x1f
    //     0xb03648: tbz             w4, #0, #0xb03650
    //     0xb0364c: ldur            x5, [x4, #7]
    // 0xb03650: mov             x4, x5
    // 0xb03654: stur            x4, [fp, #-0x48]
    // 0xb03658: cmp             w2, NULL
    // 0xb0365c: b.ne            #0xb03668
    // 0xb03660: r2 = Null
    //     0xb03660: mov             x2, NULL
    // 0xb03664: b               #0xb03674
    // 0xb03668: LoadField: r5 = r2->field_f
    //     0xb03668: ldur            w5, [x2, #0xf]
    // 0xb0366c: DecompressPointer r5
    //     0xb0366c: add             x5, x5, HEAP, lsl #32
    // 0xb03670: mov             x2, x5
    // 0xb03674: cmp             w2, NULL
    // 0xb03678: b.ne            #0xb03684
    // 0xb0367c: r6 = 0
    //     0xb0367c: movz            x6, #0
    // 0xb03680: b               #0xb03694
    // 0xb03684: r5 = LoadInt32Instr(r2)
    //     0xb03684: sbfx            x5, x2, #1, #0x1f
    //     0xb03688: tbz             w2, #0, #0xb03690
    //     0xb0368c: ldur            x5, [x2, #7]
    // 0xb03690: mov             x6, x5
    // 0xb03694: ldur            x5, [fp, #-0x18]
    // 0xb03698: ldur            x2, [fp, #-0x40]
    // 0xb0369c: stur            x6, [fp, #-0x20]
    // 0xb036a0: r0 = Color()
    //     0xb036a0: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb036a4: mov             x3, x0
    // 0xb036a8: r0 = Instance_ColorSpace
    //     0xb036a8: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb036ac: stur            x3, [fp, #-0x38]
    // 0xb036b0: StoreField: r3->field_27 = r0
    //     0xb036b0: stur            w0, [x3, #0x27]
    // 0xb036b4: d0 = 0.700000
    //     0xb036b4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb036b8: ldr             d0, [x17, #0xf48]
    // 0xb036bc: StoreField: r3->field_7 = d0
    //     0xb036bc: stur            d0, [x3, #7]
    // 0xb036c0: ldur            x0, [fp, #-0x50]
    // 0xb036c4: ubfx            x0, x0, #0, #0x20
    // 0xb036c8: and             w1, w0, #0xff
    // 0xb036cc: ubfx            x1, x1, #0, #0x20
    // 0xb036d0: scvtf           d0, x1
    // 0xb036d4: d1 = 255.000000
    //     0xb036d4: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb036d8: fdiv            d2, d0, d1
    // 0xb036dc: StoreField: r3->field_f = d2
    //     0xb036dc: stur            d2, [x3, #0xf]
    // 0xb036e0: ldur            x0, [fp, #-0x48]
    // 0xb036e4: ubfx            x0, x0, #0, #0x20
    // 0xb036e8: and             w1, w0, #0xff
    // 0xb036ec: ubfx            x1, x1, #0, #0x20
    // 0xb036f0: scvtf           d0, x1
    // 0xb036f4: fdiv            d2, d0, d1
    // 0xb036f8: ArrayStore: r3[0] = d2  ; List_8
    //     0xb036f8: stur            d2, [x3, #0x17]
    // 0xb036fc: ldur            x0, [fp, #-0x20]
    // 0xb03700: ubfx            x0, x0, #0, #0x20
    // 0xb03704: and             w1, w0, #0xff
    // 0xb03708: ubfx            x1, x1, #0, #0x20
    // 0xb0370c: scvtf           d0, x1
    // 0xb03710: fdiv            d2, d0, d1
    // 0xb03714: StoreField: r3->field_1f = d2
    //     0xb03714: stur            d2, [x3, #0x1f]
    // 0xb03718: r1 = Null
    //     0xb03718: mov             x1, NULL
    // 0xb0371c: r2 = 4
    //     0xb0371c: movz            x2, #0x4
    // 0xb03720: r0 = AllocateArray()
    //     0xb03720: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb03724: mov             x2, x0
    // 0xb03728: ldur            x0, [fp, #-0x58]
    // 0xb0372c: stur            x2, [fp, #-0x60]
    // 0xb03730: StoreField: r2->field_f = r0
    //     0xb03730: stur            w0, [x2, #0xf]
    // 0xb03734: ldur            x0, [fp, #-0x38]
    // 0xb03738: StoreField: r2->field_13 = r0
    //     0xb03738: stur            w0, [x2, #0x13]
    // 0xb0373c: r1 = <Color>
    //     0xb0373c: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb03740: ldr             x1, [x1, #0xf80]
    // 0xb03744: r0 = AllocateGrowableArray()
    //     0xb03744: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb03748: mov             x1, x0
    // 0xb0374c: ldur            x0, [fp, #-0x60]
    // 0xb03750: stur            x1, [fp, #-0x38]
    // 0xb03754: StoreField: r1->field_f = r0
    //     0xb03754: stur            w0, [x1, #0xf]
    // 0xb03758: r2 = 4
    //     0xb03758: movz            x2, #0x4
    // 0xb0375c: StoreField: r1->field_b = r2
    //     0xb0375c: stur            w2, [x1, #0xb]
    // 0xb03760: r0 = LinearGradient()
    //     0xb03760: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xb03764: mov             x1, x0
    // 0xb03768: r0 = Instance_Alignment
    //     0xb03768: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb0376c: ldr             x0, [x0, #0xce0]
    // 0xb03770: stur            x1, [fp, #-0x58]
    // 0xb03774: StoreField: r1->field_13 = r0
    //     0xb03774: stur            w0, [x1, #0x13]
    // 0xb03778: r0 = Instance_Alignment
    //     0xb03778: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xb0377c: ldr             x0, [x0, #0xce8]
    // 0xb03780: ArrayStore: r1[0] = r0  ; List_4
    //     0xb03780: stur            w0, [x1, #0x17]
    // 0xb03784: r0 = Instance_TileMode
    //     0xb03784: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xb03788: ldr             x0, [x0, #0xcf0]
    // 0xb0378c: StoreField: r1->field_1b = r0
    //     0xb0378c: stur            w0, [x1, #0x1b]
    // 0xb03790: ldur            x0, [fp, #-0x38]
    // 0xb03794: StoreField: r1->field_7 = r0
    //     0xb03794: stur            w0, [x1, #7]
    // 0xb03798: r0 = BoxDecoration()
    //     0xb03798: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb0379c: mov             x2, x0
    // 0xb037a0: ldur            x0, [fp, #-0x40]
    // 0xb037a4: stur            x2, [fp, #-0x38]
    // 0xb037a8: StoreField: r2->field_13 = r0
    //     0xb037a8: stur            w0, [x2, #0x13]
    // 0xb037ac: ldur            x0, [fp, #-0x58]
    // 0xb037b0: StoreField: r2->field_1b = r0
    //     0xb037b0: stur            w0, [x2, #0x1b]
    // 0xb037b4: r0 = Instance_BoxShape
    //     0xb037b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb037b8: ldr             x0, [x0, #0x80]
    // 0xb037bc: StoreField: r2->field_23 = r0
    //     0xb037bc: stur            w0, [x2, #0x23]
    // 0xb037c0: ldur            x1, [fp, #-0x18]
    // 0xb037c4: cmp             w1, NULL
    // 0xb037c8: b.ne            #0xb037d4
    // 0xb037cc: r1 = Null
    //     0xb037cc: mov             x1, NULL
    // 0xb037d0: b               #0xb037e4
    // 0xb037d4: r17 = 295
    //     0xb037d4: movz            x17, #0x127
    // 0xb037d8: ldr             w3, [x1, x17]
    // 0xb037dc: DecompressPointer r3
    //     0xb037dc: add             x3, x3, HEAP, lsl #32
    // 0xb037e0: mov             x1, x3
    // 0xb037e4: cmp             w1, NULL
    // 0xb037e8: b.ne            #0xb037f4
    // 0xb037ec: r4 = ""
    //     0xb037ec: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb037f0: b               #0xb037f8
    // 0xb037f4: mov             x4, x1
    // 0xb037f8: ldur            x3, [fp, #-8]
    // 0xb037fc: stur            x4, [fp, #-0x18]
    // 0xb03800: LoadField: r1 = r3->field_f
    //     0xb03800: ldur            w1, [x3, #0xf]
    // 0xb03804: DecompressPointer r1
    //     0xb03804: add             x1, x1, HEAP, lsl #32
    // 0xb03808: cmp             w1, NULL
    // 0xb0380c: b.eq            #0xb042d8
    // 0xb03810: r0 = of()
    //     0xb03810: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb03814: LoadField: r1 = r0->field_87
    //     0xb03814: ldur            w1, [x0, #0x87]
    // 0xb03818: DecompressPointer r1
    //     0xb03818: add             x1, x1, HEAP, lsl #32
    // 0xb0381c: LoadField: r0 = r1->field_7
    //     0xb0381c: ldur            w0, [x1, #7]
    // 0xb03820: DecompressPointer r0
    //     0xb03820: add             x0, x0, HEAP, lsl #32
    // 0xb03824: r16 = 12.000000
    //     0xb03824: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb03828: ldr             x16, [x16, #0x9e8]
    // 0xb0382c: r30 = Instance_Color
    //     0xb0382c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb03830: stp             lr, x16, [SP]
    // 0xb03834: mov             x1, x0
    // 0xb03838: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb03838: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0383c: ldr             x4, [x4, #0xaa0]
    // 0xb03840: r0 = copyWith()
    //     0xb03840: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb03844: stur            x0, [fp, #-0x40]
    // 0xb03848: r0 = Text()
    //     0xb03848: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0384c: mov             x1, x0
    // 0xb03850: ldur            x0, [fp, #-0x18]
    // 0xb03854: stur            x1, [fp, #-0x58]
    // 0xb03858: StoreField: r1->field_b = r0
    //     0xb03858: stur            w0, [x1, #0xb]
    // 0xb0385c: ldur            x0, [fp, #-0x40]
    // 0xb03860: StoreField: r1->field_13 = r0
    //     0xb03860: stur            w0, [x1, #0x13]
    // 0xb03864: r0 = Instance_TextAlign
    //     0xb03864: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb03868: StoreField: r1->field_1b = r0
    //     0xb03868: stur            w0, [x1, #0x1b]
    // 0xb0386c: r0 = Padding()
    //     0xb0386c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb03870: mov             x1, x0
    // 0xb03874: r0 = Instance_EdgeInsets
    //     0xb03874: add             x0, PP, #0x27, lsl #12  ; [pp+0x27850] Obj!EdgeInsets@d57a71
    //     0xb03878: ldr             x0, [x0, #0x850]
    // 0xb0387c: stur            x1, [fp, #-0x18]
    // 0xb03880: StoreField: r1->field_f = r0
    //     0xb03880: stur            w0, [x1, #0xf]
    // 0xb03884: ldur            x0, [fp, #-0x58]
    // 0xb03888: StoreField: r1->field_b = r0
    //     0xb03888: stur            w0, [x1, #0xb]
    // 0xb0388c: r0 = Container()
    //     0xb0388c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb03890: stur            x0, [fp, #-0x40]
    // 0xb03894: r16 = 20.000000
    //     0xb03894: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb03898: ldr             x16, [x16, #0xac8]
    // 0xb0389c: r30 = 120.000000
    //     0xb0389c: add             lr, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0xb038a0: ldr             lr, [lr, #0x3a0]
    // 0xb038a4: stp             lr, x16, [SP, #0x10]
    // 0xb038a8: ldur            x16, [fp, #-0x38]
    // 0xb038ac: ldur            lr, [fp, #-0x18]
    // 0xb038b0: stp             lr, x16, [SP]
    // 0xb038b4: mov             x1, x0
    // 0xb038b8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb038b8: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb038bc: ldr             x4, [x4, #0x8c0]
    // 0xb038c0: r0 = Container()
    //     0xb038c0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb038c4: r0 = Align()
    //     0xb038c4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb038c8: mov             x1, x0
    // 0xb038cc: r0 = Instance_Alignment
    //     0xb038cc: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb038d0: ldr             x0, [x0, #0xfa0]
    // 0xb038d4: stur            x1, [fp, #-0x18]
    // 0xb038d8: StoreField: r1->field_f = r0
    //     0xb038d8: stur            w0, [x1, #0xf]
    // 0xb038dc: ldur            x0, [fp, #-0x40]
    // 0xb038e0: StoreField: r1->field_b = r0
    //     0xb038e0: stur            w0, [x1, #0xb]
    // 0xb038e4: r0 = Padding()
    //     0xb038e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb038e8: mov             x1, x0
    // 0xb038ec: r0 = Instance_EdgeInsets
    //     0xb038ec: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xb038f0: ldr             x0, [x0, #0xe50]
    // 0xb038f4: StoreField: r1->field_f = r0
    //     0xb038f4: stur            w0, [x1, #0xf]
    // 0xb038f8: ldur            x0, [fp, #-0x18]
    // 0xb038fc: StoreField: r1->field_b = r0
    //     0xb038fc: stur            w0, [x1, #0xb]
    // 0xb03900: b               #0xb03c40
    // 0xb03904: r0 = Instance_Alignment
    //     0xb03904: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb03908: ldr             x0, [x0, #0xfa0]
    // 0xb0390c: r2 = 4
    //     0xb0390c: movz            x2, #0x4
    // 0xb03910: ldur            x4, [fp, #-0x28]
    // 0xb03914: ldur            x3, [fp, #-8]
    // 0xb03918: LoadField: r1 = r3->field_f
    //     0xb03918: ldur            w1, [x3, #0xf]
    // 0xb0391c: DecompressPointer r1
    //     0xb0391c: add             x1, x1, HEAP, lsl #32
    // 0xb03920: cmp             w1, NULL
    // 0xb03924: b.eq            #0xb042dc
    // 0xb03928: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb03928: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0392c: r0 = _of()
    //     0xb0392c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb03930: LoadField: r1 = r0->field_7
    //     0xb03930: ldur            w1, [x0, #7]
    // 0xb03934: DecompressPointer r1
    //     0xb03934: add             x1, x1, HEAP, lsl #32
    // 0xb03938: LoadField: d0 = r1->field_7
    //     0xb03938: ldur            d0, [x1, #7]
    // 0xb0393c: d1 = 0.370000
    //     0xb0393c: add             x17, PP, #0x52, lsl #12  ; [pp+0x52e40] IMM: double(0.37) from 0x3fd7ae147ae147ae
    //     0xb03940: ldr             d1, [x17, #0xe40]
    // 0xb03944: fmul            d2, d0, d1
    // 0xb03948: stur            d2, [fp, #-0x70]
    // 0xb0394c: r1 = Instance_Color
    //     0xb0394c: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb03950: d0 = 0.900000
    //     0xb03950: ldr             d0, [PP, #0x5a78]  ; [pp+0x5a78] IMM: double(0.9) from 0x3feccccccccccccd
    // 0xb03954: r0 = withOpacity()
    //     0xb03954: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb03958: stur            x0, [fp, #-0x18]
    // 0xb0395c: r0 = Radius()
    //     0xb0395c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb03960: d0 = 4.000000
    //     0xb03960: fmov            d0, #4.00000000
    // 0xb03964: stur            x0, [fp, #-0x38]
    // 0xb03968: StoreField: r0->field_7 = d0
    //     0xb03968: stur            d0, [x0, #7]
    // 0xb0396c: StoreField: r0->field_f = d0
    //     0xb0396c: stur            d0, [x0, #0xf]
    // 0xb03970: r0 = BorderRadius()
    //     0xb03970: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb03974: mov             x1, x0
    // 0xb03978: ldur            x0, [fp, #-0x38]
    // 0xb0397c: stur            x1, [fp, #-0x40]
    // 0xb03980: StoreField: r1->field_7 = r0
    //     0xb03980: stur            w0, [x1, #7]
    // 0xb03984: StoreField: r1->field_b = r0
    //     0xb03984: stur            w0, [x1, #0xb]
    // 0xb03988: StoreField: r1->field_f = r0
    //     0xb03988: stur            w0, [x1, #0xf]
    // 0xb0398c: StoreField: r1->field_13 = r0
    //     0xb0398c: stur            w0, [x1, #0x13]
    // 0xb03990: r0 = BoxDecoration()
    //     0xb03990: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb03994: mov             x2, x0
    // 0xb03998: ldur            x0, [fp, #-0x18]
    // 0xb0399c: stur            x2, [fp, #-0x38]
    // 0xb039a0: StoreField: r2->field_7 = r0
    //     0xb039a0: stur            w0, [x2, #7]
    // 0xb039a4: ldur            x0, [fp, #-0x40]
    // 0xb039a8: StoreField: r2->field_13 = r0
    //     0xb039a8: stur            w0, [x2, #0x13]
    // 0xb039ac: r0 = Instance_BoxShape
    //     0xb039ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb039b0: ldr             x0, [x0, #0x80]
    // 0xb039b4: StoreField: r2->field_23 = r0
    //     0xb039b4: stur            w0, [x2, #0x23]
    // 0xb039b8: ldur            x3, [fp, #-8]
    // 0xb039bc: LoadField: r1 = r3->field_f
    //     0xb039bc: ldur            w1, [x3, #0xf]
    // 0xb039c0: DecompressPointer r1
    //     0xb039c0: add             x1, x1, HEAP, lsl #32
    // 0xb039c4: cmp             w1, NULL
    // 0xb039c8: b.eq            #0xb042e0
    // 0xb039cc: r0 = of()
    //     0xb039cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb039d0: LoadField: r1 = r0->field_5b
    //     0xb039d0: ldur            w1, [x0, #0x5b]
    // 0xb039d4: DecompressPointer r1
    //     0xb039d4: add             x1, x1, HEAP, lsl #32
    // 0xb039d8: stur            x1, [fp, #-0x18]
    // 0xb039dc: r0 = ColorFilter()
    //     0xb039dc: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb039e0: mov             x1, x0
    // 0xb039e4: ldur            x0, [fp, #-0x18]
    // 0xb039e8: stur            x1, [fp, #-0x40]
    // 0xb039ec: StoreField: r1->field_7 = r0
    //     0xb039ec: stur            w0, [x1, #7]
    // 0xb039f0: r0 = Instance_BlendMode
    //     0xb039f0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb039f4: ldr             x0, [x0, #0xb30]
    // 0xb039f8: StoreField: r1->field_b = r0
    //     0xb039f8: stur            w0, [x1, #0xb]
    // 0xb039fc: r0 = 1
    //     0xb039fc: movz            x0, #0x1
    // 0xb03a00: StoreField: r1->field_13 = r0
    //     0xb03a00: stur            x0, [x1, #0x13]
    // 0xb03a04: r0 = SvgPicture()
    //     0xb03a04: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb03a08: stur            x0, [fp, #-0x18]
    // 0xb03a0c: ldur            x16, [fp, #-0x40]
    // 0xb03a10: str             x16, [SP]
    // 0xb03a14: mov             x1, x0
    // 0xb03a18: r2 = "assets/images/bumper_coupon.svg"
    //     0xb03a18: add             x2, PP, #0x52, lsl #12  ; [pp+0x52e48] "assets/images/bumper_coupon.svg"
    //     0xb03a1c: ldr             x2, [x2, #0xe48]
    // 0xb03a20: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb03a20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb03a24: ldr             x4, [x4, #0xa38]
    // 0xb03a28: r0 = SvgPicture.asset()
    //     0xb03a28: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb03a2c: ldur            x2, [fp, #-0x28]
    // 0xb03a30: LoadField: r0 = r2->field_13
    //     0xb03a30: ldur            w0, [x2, #0x13]
    // 0xb03a34: DecompressPointer r0
    //     0xb03a34: add             x0, x0, HEAP, lsl #32
    // 0xb03a38: cmp             w0, NULL
    // 0xb03a3c: b.ne            #0xb03a48
    // 0xb03a40: r0 = Null
    //     0xb03a40: mov             x0, NULL
    // 0xb03a44: b               #0xb03a58
    // 0xb03a48: r17 = 295
    //     0xb03a48: movz            x17, #0x127
    // 0xb03a4c: ldr             w1, [x0, x17]
    // 0xb03a50: DecompressPointer r1
    //     0xb03a50: add             x1, x1, HEAP, lsl #32
    // 0xb03a54: mov             x0, x1
    // 0xb03a58: cmp             w0, NULL
    // 0xb03a5c: b.ne            #0xb03a68
    // 0xb03a60: r4 = ""
    //     0xb03a60: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb03a64: b               #0xb03a6c
    // 0xb03a68: mov             x4, x0
    // 0xb03a6c: ldur            x3, [fp, #-8]
    // 0xb03a70: ldur            d0, [fp, #-0x70]
    // 0xb03a74: ldur            x0, [fp, #-0x18]
    // 0xb03a78: stur            x4, [fp, #-0x40]
    // 0xb03a7c: LoadField: r1 = r3->field_f
    //     0xb03a7c: ldur            w1, [x3, #0xf]
    // 0xb03a80: DecompressPointer r1
    //     0xb03a80: add             x1, x1, HEAP, lsl #32
    // 0xb03a84: cmp             w1, NULL
    // 0xb03a88: b.eq            #0xb042e4
    // 0xb03a8c: r0 = of()
    //     0xb03a8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb03a90: LoadField: r1 = r0->field_87
    //     0xb03a90: ldur            w1, [x0, #0x87]
    // 0xb03a94: DecompressPointer r1
    //     0xb03a94: add             x1, x1, HEAP, lsl #32
    // 0xb03a98: LoadField: r0 = r1->field_2b
    //     0xb03a98: ldur            w0, [x1, #0x2b]
    // 0xb03a9c: DecompressPointer r0
    //     0xb03a9c: add             x0, x0, HEAP, lsl #32
    // 0xb03aa0: r16 = 12.000000
    //     0xb03aa0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb03aa4: ldr             x16, [x16, #0x9e8]
    // 0xb03aa8: r30 = Instance_Color
    //     0xb03aa8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb03aac: stp             lr, x16, [SP]
    // 0xb03ab0: mov             x1, x0
    // 0xb03ab4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb03ab4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb03ab8: ldr             x4, [x4, #0xaa0]
    // 0xb03abc: r0 = copyWith()
    //     0xb03abc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb03ac0: stur            x0, [fp, #-0x58]
    // 0xb03ac4: r0 = Text()
    //     0xb03ac4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb03ac8: mov             x1, x0
    // 0xb03acc: ldur            x0, [fp, #-0x40]
    // 0xb03ad0: stur            x1, [fp, #-0x60]
    // 0xb03ad4: StoreField: r1->field_b = r0
    //     0xb03ad4: stur            w0, [x1, #0xb]
    // 0xb03ad8: ldur            x0, [fp, #-0x58]
    // 0xb03adc: StoreField: r1->field_13 = r0
    //     0xb03adc: stur            w0, [x1, #0x13]
    // 0xb03ae0: r0 = Padding()
    //     0xb03ae0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb03ae4: mov             x3, x0
    // 0xb03ae8: r0 = Instance_EdgeInsets
    //     0xb03ae8: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xb03aec: ldr             x0, [x0, #0xc40]
    // 0xb03af0: stur            x3, [fp, #-0x40]
    // 0xb03af4: StoreField: r3->field_f = r0
    //     0xb03af4: stur            w0, [x3, #0xf]
    // 0xb03af8: ldur            x0, [fp, #-0x60]
    // 0xb03afc: StoreField: r3->field_b = r0
    //     0xb03afc: stur            w0, [x3, #0xb]
    // 0xb03b00: r1 = Null
    //     0xb03b00: mov             x1, NULL
    // 0xb03b04: r2 = 4
    //     0xb03b04: movz            x2, #0x4
    // 0xb03b08: r0 = AllocateArray()
    //     0xb03b08: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb03b0c: mov             x2, x0
    // 0xb03b10: ldur            x0, [fp, #-0x18]
    // 0xb03b14: stur            x2, [fp, #-0x58]
    // 0xb03b18: StoreField: r2->field_f = r0
    //     0xb03b18: stur            w0, [x2, #0xf]
    // 0xb03b1c: ldur            x0, [fp, #-0x40]
    // 0xb03b20: StoreField: r2->field_13 = r0
    //     0xb03b20: stur            w0, [x2, #0x13]
    // 0xb03b24: r1 = <Widget>
    //     0xb03b24: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb03b28: r0 = AllocateGrowableArray()
    //     0xb03b28: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb03b2c: mov             x1, x0
    // 0xb03b30: ldur            x0, [fp, #-0x58]
    // 0xb03b34: stur            x1, [fp, #-0x18]
    // 0xb03b38: StoreField: r1->field_f = r0
    //     0xb03b38: stur            w0, [x1, #0xf]
    // 0xb03b3c: r0 = 4
    //     0xb03b3c: movz            x0, #0x4
    // 0xb03b40: StoreField: r1->field_b = r0
    //     0xb03b40: stur            w0, [x1, #0xb]
    // 0xb03b44: r0 = Row()
    //     0xb03b44: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb03b48: mov             x1, x0
    // 0xb03b4c: r0 = Instance_Axis
    //     0xb03b4c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb03b50: stur            x1, [fp, #-0x40]
    // 0xb03b54: StoreField: r1->field_f = r0
    //     0xb03b54: stur            w0, [x1, #0xf]
    // 0xb03b58: r0 = Instance_MainAxisAlignment
    //     0xb03b58: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb03b5c: ldr             x0, [x0, #0xa08]
    // 0xb03b60: StoreField: r1->field_13 = r0
    //     0xb03b60: stur            w0, [x1, #0x13]
    // 0xb03b64: r0 = Instance_MainAxisSize
    //     0xb03b64: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb03b68: ldr             x0, [x0, #0xa10]
    // 0xb03b6c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb03b6c: stur            w0, [x1, #0x17]
    // 0xb03b70: r0 = Instance_CrossAxisAlignment
    //     0xb03b70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb03b74: ldr             x0, [x0, #0xa18]
    // 0xb03b78: StoreField: r1->field_1b = r0
    //     0xb03b78: stur            w0, [x1, #0x1b]
    // 0xb03b7c: r0 = Instance_VerticalDirection
    //     0xb03b7c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb03b80: ldr             x0, [x0, #0xa20]
    // 0xb03b84: StoreField: r1->field_23 = r0
    //     0xb03b84: stur            w0, [x1, #0x23]
    // 0xb03b88: r0 = Instance_Clip
    //     0xb03b88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb03b8c: ldr             x0, [x0, #0x38]
    // 0xb03b90: StoreField: r1->field_2b = r0
    //     0xb03b90: stur            w0, [x1, #0x2b]
    // 0xb03b94: StoreField: r1->field_2f = rZR
    //     0xb03b94: stur            xzr, [x1, #0x2f]
    // 0xb03b98: ldur            x0, [fp, #-0x18]
    // 0xb03b9c: StoreField: r1->field_b = r0
    //     0xb03b9c: stur            w0, [x1, #0xb]
    // 0xb03ba0: ldur            d0, [fp, #-0x70]
    // 0xb03ba4: r0 = inline_Allocate_Double()
    //     0xb03ba4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb03ba8: add             x0, x0, #0x10
    //     0xb03bac: cmp             x2, x0
    //     0xb03bb0: b.ls            #0xb042e8
    //     0xb03bb4: str             x0, [THR, #0x50]  ; THR::top
    //     0xb03bb8: sub             x0, x0, #0xf
    //     0xb03bbc: movz            x2, #0xe15c
    //     0xb03bc0: movk            x2, #0x3, lsl #16
    //     0xb03bc4: stur            x2, [x0, #-1]
    // 0xb03bc8: StoreField: r0->field_7 = d0
    //     0xb03bc8: stur            d0, [x0, #7]
    // 0xb03bcc: stur            x0, [fp, #-0x18]
    // 0xb03bd0: r0 = Container()
    //     0xb03bd0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb03bd4: stur            x0, [fp, #-0x58]
    // 0xb03bd8: r16 = 20.000000
    //     0xb03bd8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb03bdc: ldr             x16, [x16, #0xac8]
    // 0xb03be0: ldur            lr, [fp, #-0x18]
    // 0xb03be4: stp             lr, x16, [SP, #0x10]
    // 0xb03be8: ldur            x16, [fp, #-0x38]
    // 0xb03bec: ldur            lr, [fp, #-0x40]
    // 0xb03bf0: stp             lr, x16, [SP]
    // 0xb03bf4: mov             x1, x0
    // 0xb03bf8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb03bf8: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb03bfc: ldr             x4, [x4, #0x8c0]
    // 0xb03c00: r0 = Container()
    //     0xb03c00: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb03c04: r0 = Padding()
    //     0xb03c04: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb03c08: mov             x1, x0
    // 0xb03c0c: r0 = Instance_EdgeInsets
    //     0xb03c0c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xb03c10: ldr             x0, [x0, #0xf70]
    // 0xb03c14: stur            x1, [fp, #-0x18]
    // 0xb03c18: StoreField: r1->field_f = r0
    //     0xb03c18: stur            w0, [x1, #0xf]
    // 0xb03c1c: ldur            x0, [fp, #-0x58]
    // 0xb03c20: StoreField: r1->field_b = r0
    //     0xb03c20: stur            w0, [x1, #0xb]
    // 0xb03c24: r0 = Align()
    //     0xb03c24: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb03c28: mov             x1, x0
    // 0xb03c2c: r0 = Instance_Alignment
    //     0xb03c2c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb03c30: ldr             x0, [x0, #0xfa0]
    // 0xb03c34: StoreField: r1->field_f = r0
    //     0xb03c34: stur            w0, [x1, #0xf]
    // 0xb03c38: ldur            x0, [fp, #-0x18]
    // 0xb03c3c: StoreField: r1->field_b = r0
    //     0xb03c3c: stur            w0, [x1, #0xb]
    // 0xb03c40: ldur            x2, [fp, #-0x28]
    // 0xb03c44: ldur            x0, [fp, #-0x10]
    // 0xb03c48: stur            x1, [fp, #-0x18]
    // 0xb03c4c: r0 = Visibility()
    //     0xb03c4c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb03c50: mov             x1, x0
    // 0xb03c54: ldur            x0, [fp, #-0x18]
    // 0xb03c58: stur            x1, [fp, #-0x38]
    // 0xb03c5c: StoreField: r1->field_b = r0
    //     0xb03c5c: stur            w0, [x1, #0xb]
    // 0xb03c60: r0 = Instance_SizedBox
    //     0xb03c60: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb03c64: StoreField: r1->field_f = r0
    //     0xb03c64: stur            w0, [x1, #0xf]
    // 0xb03c68: ldur            x2, [fp, #-0x10]
    // 0xb03c6c: StoreField: r1->field_13 = r2
    //     0xb03c6c: stur            w2, [x1, #0x13]
    // 0xb03c70: r2 = false
    //     0xb03c70: add             x2, NULL, #0x30  ; false
    // 0xb03c74: ArrayStore: r1[0] = r2  ; List_4
    //     0xb03c74: stur            w2, [x1, #0x17]
    // 0xb03c78: StoreField: r1->field_1b = r2
    //     0xb03c78: stur            w2, [x1, #0x1b]
    // 0xb03c7c: StoreField: r1->field_1f = r2
    //     0xb03c7c: stur            w2, [x1, #0x1f]
    // 0xb03c80: StoreField: r1->field_23 = r2
    //     0xb03c80: stur            w2, [x1, #0x23]
    // 0xb03c84: StoreField: r1->field_27 = r2
    //     0xb03c84: stur            w2, [x1, #0x27]
    // 0xb03c88: StoreField: r1->field_2b = r2
    //     0xb03c88: stur            w2, [x1, #0x2b]
    // 0xb03c8c: ldur            x3, [fp, #-0x28]
    // 0xb03c90: LoadField: r4 = r3->field_13
    //     0xb03c90: ldur            w4, [x3, #0x13]
    // 0xb03c94: DecompressPointer r4
    //     0xb03c94: add             x4, x4, HEAP, lsl #32
    // 0xb03c98: cmp             w4, NULL
    // 0xb03c9c: b.ne            #0xb03ca8
    // 0xb03ca0: r5 = Null
    //     0xb03ca0: mov             x5, NULL
    // 0xb03ca4: b               #0xb03cd8
    // 0xb03ca8: r17 = 311
    //     0xb03ca8: movz            x17, #0x137
    // 0xb03cac: ldr             w5, [x4, x17]
    // 0xb03cb0: DecompressPointer r5
    //     0xb03cb0: add             x5, x5, HEAP, lsl #32
    // 0xb03cb4: cmp             w5, NULL
    // 0xb03cb8: b.ne            #0xb03cc4
    // 0xb03cbc: r5 = Null
    //     0xb03cbc: mov             x5, NULL
    // 0xb03cc0: b               #0xb03cd8
    // 0xb03cc4: LoadField: r6 = r5->field_7
    //     0xb03cc4: ldur            w6, [x5, #7]
    // 0xb03cc8: cbnz            w6, #0xb03cd4
    // 0xb03ccc: r5 = false
    //     0xb03ccc: add             x5, NULL, #0x30  ; false
    // 0xb03cd0: b               #0xb03cd8
    // 0xb03cd4: r5 = true
    //     0xb03cd4: add             x5, NULL, #0x20  ; true
    // 0xb03cd8: cmp             w5, NULL
    // 0xb03cdc: b.ne            #0xb03ce4
    // 0xb03ce0: r5 = false
    //     0xb03ce0: add             x5, NULL, #0x30  ; false
    // 0xb03ce4: stur            x5, [fp, #-0x10]
    // 0xb03ce8: cmp             w4, NULL
    // 0xb03cec: b.ne            #0xb03cf8
    // 0xb03cf0: r4 = Null
    //     0xb03cf0: mov             x4, NULL
    // 0xb03cf4: b               #0xb03d08
    // 0xb03cf8: r17 = 263
    //     0xb03cf8: movz            x17, #0x107
    // 0xb03cfc: ldr             w6, [x4, x17]
    // 0xb03d00: DecompressPointer r6
    //     0xb03d00: add             x6, x6, HEAP, lsl #32
    // 0xb03d04: mov             x4, x6
    // 0xb03d08: cmp             w4, NULL
    // 0xb03d0c: b.eq            #0xb03d20
    // 0xb03d10: tbnz            w4, #4, #0xb03d20
    // 0xb03d14: d0 = 38.000000
    //     0xb03d14: add             x17, PP, #0x50, lsl #12  ; [pp+0x50d10] IMM: double(38) from 0x4043000000000000
    //     0xb03d18: ldr             d0, [x17, #0xd10]
    // 0xb03d1c: b               #0xb03d24
    // 0xb03d20: d0 = 4.000000
    //     0xb03d20: fmov            d0, #4.00000000
    // 0xb03d24: stur            d0, [fp, #-0x70]
    // 0xb03d28: r0 = EdgeInsets()
    //     0xb03d28: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xb03d2c: d0 = 8.000000
    //     0xb03d2c: fmov            d0, #8.00000000
    // 0xb03d30: stur            x0, [fp, #-0x18]
    // 0xb03d34: StoreField: r0->field_7 = d0
    //     0xb03d34: stur            d0, [x0, #7]
    // 0xb03d38: StoreField: r0->field_f = rZR
    //     0xb03d38: stur            xzr, [x0, #0xf]
    // 0xb03d3c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb03d3c: stur            xzr, [x0, #0x17]
    // 0xb03d40: ldur            d0, [fp, #-0x70]
    // 0xb03d44: StoreField: r0->field_1f = d0
    //     0xb03d44: stur            d0, [x0, #0x1f]
    // 0xb03d48: r16 = <EdgeInsets>
    //     0xb03d48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb03d4c: ldr             x16, [x16, #0xda0]
    // 0xb03d50: r30 = Instance_EdgeInsets
    //     0xb03d50: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb03d54: ldr             lr, [lr, #0x668]
    // 0xb03d58: stp             lr, x16, [SP]
    // 0xb03d5c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb03d5c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb03d60: r0 = all()
    //     0xb03d60: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb03d64: stur            x0, [fp, #-0x40]
    // 0xb03d68: r16 = <Color>
    //     0xb03d68: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb03d6c: ldr             x16, [x16, #0xf80]
    // 0xb03d70: r30 = Instance_Color
    //     0xb03d70: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb03d74: stp             lr, x16, [SP]
    // 0xb03d78: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb03d78: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb03d7c: r0 = all()
    //     0xb03d7c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb03d80: stur            x0, [fp, #-0x58]
    // 0xb03d84: r0 = Radius()
    //     0xb03d84: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb03d88: d0 = 5.000000
    //     0xb03d88: fmov            d0, #5.00000000
    // 0xb03d8c: stur            x0, [fp, #-0x60]
    // 0xb03d90: StoreField: r0->field_7 = d0
    //     0xb03d90: stur            d0, [x0, #7]
    // 0xb03d94: StoreField: r0->field_f = d0
    //     0xb03d94: stur            d0, [x0, #0xf]
    // 0xb03d98: r0 = BorderRadius()
    //     0xb03d98: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb03d9c: mov             x1, x0
    // 0xb03da0: ldur            x0, [fp, #-0x60]
    // 0xb03da4: stur            x1, [fp, #-0x68]
    // 0xb03da8: StoreField: r1->field_7 = r0
    //     0xb03da8: stur            w0, [x1, #7]
    // 0xb03dac: StoreField: r1->field_b = r0
    //     0xb03dac: stur            w0, [x1, #0xb]
    // 0xb03db0: StoreField: r1->field_f = r0
    //     0xb03db0: stur            w0, [x1, #0xf]
    // 0xb03db4: StoreField: r1->field_13 = r0
    //     0xb03db4: stur            w0, [x1, #0x13]
    // 0xb03db8: r0 = RoundedRectangleBorder()
    //     0xb03db8: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb03dbc: mov             x1, x0
    // 0xb03dc0: ldur            x0, [fp, #-0x68]
    // 0xb03dc4: StoreField: r1->field_b = r0
    //     0xb03dc4: stur            w0, [x1, #0xb]
    // 0xb03dc8: r0 = Instance_BorderSide
    //     0xb03dc8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb03dcc: ldr             x0, [x0, #0xe20]
    // 0xb03dd0: StoreField: r1->field_7 = r0
    //     0xb03dd0: stur            w0, [x1, #7]
    // 0xb03dd4: r16 = <RoundedRectangleBorder>
    //     0xb03dd4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb03dd8: ldr             x16, [x16, #0xf78]
    // 0xb03ddc: stp             x1, x16, [SP]
    // 0xb03de0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb03de0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb03de4: r0 = all()
    //     0xb03de4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb03de8: stur            x0, [fp, #-0x60]
    // 0xb03dec: r0 = ButtonStyle()
    //     0xb03dec: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb03df0: mov             x1, x0
    // 0xb03df4: ldur            x0, [fp, #-0x58]
    // 0xb03df8: stur            x1, [fp, #-0x68]
    // 0xb03dfc: StoreField: r1->field_b = r0
    //     0xb03dfc: stur            w0, [x1, #0xb]
    // 0xb03e00: ldur            x0, [fp, #-0x40]
    // 0xb03e04: StoreField: r1->field_23 = r0
    //     0xb03e04: stur            w0, [x1, #0x23]
    // 0xb03e08: ldur            x0, [fp, #-0x60]
    // 0xb03e0c: StoreField: r1->field_43 = r0
    //     0xb03e0c: stur            w0, [x1, #0x43]
    // 0xb03e10: r0 = TextButtonThemeData()
    //     0xb03e10: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb03e14: mov             x1, x0
    // 0xb03e18: ldur            x0, [fp, #-0x68]
    // 0xb03e1c: stur            x1, [fp, #-0x40]
    // 0xb03e20: StoreField: r1->field_7 = r0
    //     0xb03e20: stur            w0, [x1, #7]
    // 0xb03e24: ldur            x2, [fp, #-0x28]
    // 0xb03e28: LoadField: r0 = r2->field_13
    //     0xb03e28: ldur            w0, [x2, #0x13]
    // 0xb03e2c: DecompressPointer r0
    //     0xb03e2c: add             x0, x0, HEAP, lsl #32
    // 0xb03e30: cmp             w0, NULL
    // 0xb03e34: b.ne            #0xb03e40
    // 0xb03e38: r5 = Null
    //     0xb03e38: mov             x5, NULL
    // 0xb03e3c: b               #0xb03e50
    // 0xb03e40: r17 = 311
    //     0xb03e40: movz            x17, #0x137
    // 0xb03e44: ldr             w3, [x0, x17]
    // 0xb03e48: DecompressPointer r3
    //     0xb03e48: add             x3, x3, HEAP, lsl #32
    // 0xb03e4c: mov             x5, x3
    // 0xb03e50: ldur            x4, [fp, #-8]
    // 0xb03e54: ldur            x3, [fp, #-0x10]
    // 0xb03e58: ldur            x0, [fp, #-0x18]
    // 0xb03e5c: str             x5, [SP]
    // 0xb03e60: r0 = _interpolateSingle()
    //     0xb03e60: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb03e64: mov             x2, x0
    // 0xb03e68: ldur            x0, [fp, #-8]
    // 0xb03e6c: stur            x2, [fp, #-0x58]
    // 0xb03e70: LoadField: r1 = r0->field_f
    //     0xb03e70: ldur            w1, [x0, #0xf]
    // 0xb03e74: DecompressPointer r1
    //     0xb03e74: add             x1, x1, HEAP, lsl #32
    // 0xb03e78: cmp             w1, NULL
    // 0xb03e7c: b.eq            #0xb04300
    // 0xb03e80: r0 = of()
    //     0xb03e80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb03e84: LoadField: r1 = r0->field_87
    //     0xb03e84: ldur            w1, [x0, #0x87]
    // 0xb03e88: DecompressPointer r1
    //     0xb03e88: add             x1, x1, HEAP, lsl #32
    // 0xb03e8c: LoadField: r0 = r1->field_2b
    //     0xb03e8c: ldur            w0, [x1, #0x2b]
    // 0xb03e90: DecompressPointer r0
    //     0xb03e90: add             x0, x0, HEAP, lsl #32
    // 0xb03e94: r16 = 12.000000
    //     0xb03e94: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb03e98: ldr             x16, [x16, #0x9e8]
    // 0xb03e9c: r30 = Instance_Color
    //     0xb03e9c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb03ea0: stp             lr, x16, [SP]
    // 0xb03ea4: mov             x1, x0
    // 0xb03ea8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb03ea8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb03eac: ldr             x4, [x4, #0xaa0]
    // 0xb03eb0: r0 = copyWith()
    //     0xb03eb0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb03eb4: stur            x0, [fp, #-0x60]
    // 0xb03eb8: r0 = Text()
    //     0xb03eb8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb03ebc: mov             x3, x0
    // 0xb03ec0: ldur            x0, [fp, #-0x58]
    // 0xb03ec4: stur            x3, [fp, #-0x68]
    // 0xb03ec8: StoreField: r3->field_b = r0
    //     0xb03ec8: stur            w0, [x3, #0xb]
    // 0xb03ecc: ldur            x0, [fp, #-0x60]
    // 0xb03ed0: StoreField: r3->field_13 = r0
    //     0xb03ed0: stur            w0, [x3, #0x13]
    // 0xb03ed4: r1 = Function '<anonymous closure>':.
    //     0xb03ed4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57f10] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb03ed8: ldr             x1, [x1, #0xf10]
    // 0xb03edc: r2 = Null
    //     0xb03edc: mov             x2, NULL
    // 0xb03ee0: r0 = AllocateClosure()
    //     0xb03ee0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb03ee4: stur            x0, [fp, #-0x58]
    // 0xb03ee8: r0 = TextButton()
    //     0xb03ee8: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb03eec: mov             x1, x0
    // 0xb03ef0: ldur            x0, [fp, #-0x58]
    // 0xb03ef4: stur            x1, [fp, #-0x60]
    // 0xb03ef8: StoreField: r1->field_b = r0
    //     0xb03ef8: stur            w0, [x1, #0xb]
    // 0xb03efc: r0 = false
    //     0xb03efc: add             x0, NULL, #0x30  ; false
    // 0xb03f00: StoreField: r1->field_27 = r0
    //     0xb03f00: stur            w0, [x1, #0x27]
    // 0xb03f04: r2 = true
    //     0xb03f04: add             x2, NULL, #0x20  ; true
    // 0xb03f08: StoreField: r1->field_2f = r2
    //     0xb03f08: stur            w2, [x1, #0x2f]
    // 0xb03f0c: ldur            x3, [fp, #-0x68]
    // 0xb03f10: StoreField: r1->field_37 = r3
    //     0xb03f10: stur            w3, [x1, #0x37]
    // 0xb03f14: r0 = TextButtonTheme()
    //     0xb03f14: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb03f18: mov             x1, x0
    // 0xb03f1c: ldur            x0, [fp, #-0x40]
    // 0xb03f20: stur            x1, [fp, #-0x58]
    // 0xb03f24: StoreField: r1->field_f = r0
    //     0xb03f24: stur            w0, [x1, #0xf]
    // 0xb03f28: ldur            x0, [fp, #-0x60]
    // 0xb03f2c: StoreField: r1->field_b = r0
    //     0xb03f2c: stur            w0, [x1, #0xb]
    // 0xb03f30: r0 = Padding()
    //     0xb03f30: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb03f34: mov             x1, x0
    // 0xb03f38: ldur            x0, [fp, #-0x18]
    // 0xb03f3c: stur            x1, [fp, #-0x40]
    // 0xb03f40: StoreField: r1->field_f = r0
    //     0xb03f40: stur            w0, [x1, #0xf]
    // 0xb03f44: ldur            x0, [fp, #-0x58]
    // 0xb03f48: StoreField: r1->field_b = r0
    //     0xb03f48: stur            w0, [x1, #0xb]
    // 0xb03f4c: r0 = Visibility()
    //     0xb03f4c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb03f50: mov             x2, x0
    // 0xb03f54: ldur            x0, [fp, #-0x40]
    // 0xb03f58: stur            x2, [fp, #-0x18]
    // 0xb03f5c: StoreField: r2->field_b = r0
    //     0xb03f5c: stur            w0, [x2, #0xb]
    // 0xb03f60: r0 = Instance_SizedBox
    //     0xb03f60: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb03f64: StoreField: r2->field_f = r0
    //     0xb03f64: stur            w0, [x2, #0xf]
    // 0xb03f68: ldur            x0, [fp, #-0x10]
    // 0xb03f6c: StoreField: r2->field_13 = r0
    //     0xb03f6c: stur            w0, [x2, #0x13]
    // 0xb03f70: r0 = false
    //     0xb03f70: add             x0, NULL, #0x30  ; false
    // 0xb03f74: ArrayStore: r2[0] = r0  ; List_4
    //     0xb03f74: stur            w0, [x2, #0x17]
    // 0xb03f78: StoreField: r2->field_1b = r0
    //     0xb03f78: stur            w0, [x2, #0x1b]
    // 0xb03f7c: StoreField: r2->field_1f = r0
    //     0xb03f7c: stur            w0, [x2, #0x1f]
    // 0xb03f80: StoreField: r2->field_23 = r0
    //     0xb03f80: stur            w0, [x2, #0x23]
    // 0xb03f84: StoreField: r2->field_27 = r0
    //     0xb03f84: stur            w0, [x2, #0x27]
    // 0xb03f88: StoreField: r2->field_2b = r0
    //     0xb03f88: stur            w0, [x2, #0x2b]
    // 0xb03f8c: ldur            x3, [fp, #-0x28]
    // 0xb03f90: LoadField: r1 = r3->field_13
    //     0xb03f90: ldur            w1, [x3, #0x13]
    // 0xb03f94: DecompressPointer r1
    //     0xb03f94: add             x1, x1, HEAP, lsl #32
    // 0xb03f98: cmp             w1, NULL
    // 0xb03f9c: b.ne            #0xb03fa8
    // 0xb03fa0: r1 = Null
    //     0xb03fa0: mov             x1, NULL
    // 0xb03fa4: b               #0xb03fb8
    // 0xb03fa8: r17 = 263
    //     0xb03fa8: movz            x17, #0x107
    // 0xb03fac: ldr             w4, [x1, x17]
    // 0xb03fb0: DecompressPointer r4
    //     0xb03fb0: add             x4, x4, HEAP, lsl #32
    // 0xb03fb4: mov             x1, x4
    // 0xb03fb8: cmp             w1, NULL
    // 0xb03fbc: b.eq            #0xb0415c
    // 0xb03fc0: tbnz            w1, #4, #0xb0415c
    // 0xb03fc4: ldur            x4, [fp, #-8]
    // 0xb03fc8: LoadField: r1 = r4->field_f
    //     0xb03fc8: ldur            w1, [x4, #0xf]
    // 0xb03fcc: DecompressPointer r1
    //     0xb03fcc: add             x1, x1, HEAP, lsl #32
    // 0xb03fd0: cmp             w1, NULL
    // 0xb03fd4: b.eq            #0xb04304
    // 0xb03fd8: r0 = of()
    //     0xb03fd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb03fdc: r17 = 307
    //     0xb03fdc: movz            x17, #0x133
    // 0xb03fe0: ldr             w1, [x0, x17]
    // 0xb03fe4: DecompressPointer r1
    //     0xb03fe4: add             x1, x1, HEAP, lsl #32
    // 0xb03fe8: stur            x1, [fp, #-0x10]
    // 0xb03fec: r0 = Radius()
    //     0xb03fec: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb03ff0: d0 = 5.000000
    //     0xb03ff0: fmov            d0, #5.00000000
    // 0xb03ff4: stur            x0, [fp, #-0x40]
    // 0xb03ff8: StoreField: r0->field_7 = d0
    //     0xb03ff8: stur            d0, [x0, #7]
    // 0xb03ffc: StoreField: r0->field_f = d0
    //     0xb03ffc: stur            d0, [x0, #0xf]
    // 0xb04000: r0 = BorderRadius()
    //     0xb04000: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb04004: mov             x1, x0
    // 0xb04008: ldur            x0, [fp, #-0x40]
    // 0xb0400c: stur            x1, [fp, #-0x58]
    // 0xb04010: StoreField: r1->field_7 = r0
    //     0xb04010: stur            w0, [x1, #7]
    // 0xb04014: StoreField: r1->field_b = r0
    //     0xb04014: stur            w0, [x1, #0xb]
    // 0xb04018: StoreField: r1->field_f = r0
    //     0xb04018: stur            w0, [x1, #0xf]
    // 0xb0401c: StoreField: r1->field_13 = r0
    //     0xb0401c: stur            w0, [x1, #0x13]
    // 0xb04020: r0 = RoundedRectangleBorder()
    //     0xb04020: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb04024: mov             x1, x0
    // 0xb04028: ldur            x0, [fp, #-0x58]
    // 0xb0402c: StoreField: r1->field_b = r0
    //     0xb0402c: stur            w0, [x1, #0xb]
    // 0xb04030: r0 = Instance_BorderSide
    //     0xb04030: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb04034: ldr             x0, [x0, #0xe20]
    // 0xb04038: StoreField: r1->field_7 = r0
    //     0xb04038: stur            w0, [x1, #7]
    // 0xb0403c: r16 = <RoundedRectangleBorder>
    //     0xb0403c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb04040: ldr             x16, [x16, #0xf78]
    // 0xb04044: stp             x1, x16, [SP]
    // 0xb04048: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb04048: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb0404c: r0 = all()
    //     0xb0404c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb04050: stur            x0, [fp, #-0x40]
    // 0xb04054: r0 = ButtonStyle()
    //     0xb04054: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb04058: mov             x2, x0
    // 0xb0405c: ldur            x0, [fp, #-0x40]
    // 0xb04060: stur            x2, [fp, #-0x58]
    // 0xb04064: StoreField: r2->field_43 = r0
    //     0xb04064: stur            w0, [x2, #0x43]
    // 0xb04068: ldur            x0, [fp, #-8]
    // 0xb0406c: LoadField: r1 = r0->field_f
    //     0xb0406c: ldur            w1, [x0, #0xf]
    // 0xb04070: DecompressPointer r1
    //     0xb04070: add             x1, x1, HEAP, lsl #32
    // 0xb04074: cmp             w1, NULL
    // 0xb04078: b.eq            #0xb04308
    // 0xb0407c: r0 = of()
    //     0xb0407c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb04080: LoadField: r1 = r0->field_87
    //     0xb04080: ldur            w1, [x0, #0x87]
    // 0xb04084: DecompressPointer r1
    //     0xb04084: add             x1, x1, HEAP, lsl #32
    // 0xb04088: LoadField: r0 = r1->field_2b
    //     0xb04088: ldur            w0, [x1, #0x2b]
    // 0xb0408c: DecompressPointer r0
    //     0xb0408c: add             x0, x0, HEAP, lsl #32
    // 0xb04090: r16 = 12.000000
    //     0xb04090: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb04094: ldr             x16, [x16, #0x9e8]
    // 0xb04098: r30 = Instance_Color
    //     0xb04098: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb0409c: stp             lr, x16, [SP]
    // 0xb040a0: mov             x1, x0
    // 0xb040a4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb040a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb040a8: ldr             x4, [x4, #0xaa0]
    // 0xb040ac: r0 = copyWith()
    //     0xb040ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb040b0: stur            x0, [fp, #-8]
    // 0xb040b4: r0 = Text()
    //     0xb040b4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb040b8: mov             x3, x0
    // 0xb040bc: r0 = "Customisable"
    //     0xb040bc: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xb040c0: ldr             x0, [x0, #0x970]
    // 0xb040c4: stur            x3, [fp, #-0x40]
    // 0xb040c8: StoreField: r3->field_b = r0
    //     0xb040c8: stur            w0, [x3, #0xb]
    // 0xb040cc: ldur            x0, [fp, #-8]
    // 0xb040d0: StoreField: r3->field_13 = r0
    //     0xb040d0: stur            w0, [x3, #0x13]
    // 0xb040d4: r1 = Function '<anonymous closure>':.
    //     0xb040d4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57f18] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb040d8: ldr             x1, [x1, #0xf18]
    // 0xb040dc: r2 = Null
    //     0xb040dc: mov             x2, NULL
    // 0xb040e0: r0 = AllocateClosure()
    //     0xb040e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb040e4: stur            x0, [fp, #-8]
    // 0xb040e8: r0 = TextButton()
    //     0xb040e8: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb040ec: mov             x1, x0
    // 0xb040f0: ldur            x0, [fp, #-8]
    // 0xb040f4: stur            x1, [fp, #-0x60]
    // 0xb040f8: StoreField: r1->field_b = r0
    //     0xb040f8: stur            w0, [x1, #0xb]
    // 0xb040fc: ldur            x0, [fp, #-0x58]
    // 0xb04100: StoreField: r1->field_1b = r0
    //     0xb04100: stur            w0, [x1, #0x1b]
    // 0xb04104: r0 = false
    //     0xb04104: add             x0, NULL, #0x30  ; false
    // 0xb04108: StoreField: r1->field_27 = r0
    //     0xb04108: stur            w0, [x1, #0x27]
    // 0xb0410c: r2 = true
    //     0xb0410c: add             x2, NULL, #0x20  ; true
    // 0xb04110: StoreField: r1->field_2f = r2
    //     0xb04110: stur            w2, [x1, #0x2f]
    // 0xb04114: ldur            x3, [fp, #-0x40]
    // 0xb04118: StoreField: r1->field_37 = r3
    //     0xb04118: stur            w3, [x1, #0x37]
    // 0xb0411c: r0 = TextButtonTheme()
    //     0xb0411c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb04120: mov             x1, x0
    // 0xb04124: ldur            x0, [fp, #-0x10]
    // 0xb04128: stur            x1, [fp, #-8]
    // 0xb0412c: StoreField: r1->field_f = r0
    //     0xb0412c: stur            w0, [x1, #0xf]
    // 0xb04130: ldur            x0, [fp, #-0x60]
    // 0xb04134: StoreField: r1->field_b = r0
    //     0xb04134: stur            w0, [x1, #0xb]
    // 0xb04138: r0 = SizedBox()
    //     0xb04138: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb0413c: mov             x1, x0
    // 0xb04140: r0 = 30.000000
    //     0xb04140: add             x0, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xb04144: ldr             x0, [x0, #0x768]
    // 0xb04148: StoreField: r1->field_13 = r0
    //     0xb04148: stur            w0, [x1, #0x13]
    // 0xb0414c: ldur            x0, [fp, #-8]
    // 0xb04150: StoreField: r1->field_b = r0
    //     0xb04150: stur            w0, [x1, #0xb]
    // 0xb04154: mov             x3, x1
    // 0xb04158: b               #0xb04174
    // 0xb0415c: r0 = Container()
    //     0xb0415c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb04160: mov             x1, x0
    // 0xb04164: stur            x0, [fp, #-8]
    // 0xb04168: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb04168: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0416c: r0 = Container()
    //     0xb0416c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb04170: ldur            x3, [fp, #-8]
    // 0xb04174: ldur            x2, [fp, #-0x30]
    // 0xb04178: ldur            x1, [fp, #-0x38]
    // 0xb0417c: ldur            x0, [fp, #-0x18]
    // 0xb04180: stur            x3, [fp, #-8]
    // 0xb04184: r0 = Padding()
    //     0xb04184: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb04188: mov             x3, x0
    // 0xb0418c: r0 = Instance_EdgeInsets
    //     0xb0418c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e68] Obj!EdgeInsets@d58461
    //     0xb04190: ldr             x0, [x0, #0xe68]
    // 0xb04194: stur            x3, [fp, #-0x10]
    // 0xb04198: StoreField: r3->field_f = r0
    //     0xb04198: stur            w0, [x3, #0xf]
    // 0xb0419c: ldur            x0, [fp, #-8]
    // 0xb041a0: StoreField: r3->field_b = r0
    //     0xb041a0: stur            w0, [x3, #0xb]
    // 0xb041a4: r1 = Null
    //     0xb041a4: mov             x1, NULL
    // 0xb041a8: r2 = 8
    //     0xb041a8: movz            x2, #0x8
    // 0xb041ac: r0 = AllocateArray()
    //     0xb041ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb041b0: mov             x2, x0
    // 0xb041b4: ldur            x0, [fp, #-0x30]
    // 0xb041b8: stur            x2, [fp, #-8]
    // 0xb041bc: StoreField: r2->field_f = r0
    //     0xb041bc: stur            w0, [x2, #0xf]
    // 0xb041c0: ldur            x0, [fp, #-0x38]
    // 0xb041c4: StoreField: r2->field_13 = r0
    //     0xb041c4: stur            w0, [x2, #0x13]
    // 0xb041c8: ldur            x0, [fp, #-0x18]
    // 0xb041cc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb041cc: stur            w0, [x2, #0x17]
    // 0xb041d0: ldur            x0, [fp, #-0x10]
    // 0xb041d4: StoreField: r2->field_1b = r0
    //     0xb041d4: stur            w0, [x2, #0x1b]
    // 0xb041d8: r1 = <Widget>
    //     0xb041d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb041dc: r0 = AllocateGrowableArray()
    //     0xb041dc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb041e0: mov             x1, x0
    // 0xb041e4: ldur            x0, [fp, #-8]
    // 0xb041e8: stur            x1, [fp, #-0x10]
    // 0xb041ec: StoreField: r1->field_f = r0
    //     0xb041ec: stur            w0, [x1, #0xf]
    // 0xb041f0: r0 = 8
    //     0xb041f0: movz            x0, #0x8
    // 0xb041f4: StoreField: r1->field_b = r0
    //     0xb041f4: stur            w0, [x1, #0xb]
    // 0xb041f8: r0 = Stack()
    //     0xb041f8: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb041fc: mov             x1, x0
    // 0xb04200: r0 = Instance_Alignment
    //     0xb04200: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xb04204: ldr             x0, [x0, #0x5b8]
    // 0xb04208: stur            x1, [fp, #-8]
    // 0xb0420c: StoreField: r1->field_f = r0
    //     0xb0420c: stur            w0, [x1, #0xf]
    // 0xb04210: r0 = Instance_StackFit
    //     0xb04210: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb04214: ldr             x0, [x0, #0xfa8]
    // 0xb04218: ArrayStore: r1[0] = r0  ; List_4
    //     0xb04218: stur            w0, [x1, #0x17]
    // 0xb0421c: r0 = Instance_Clip
    //     0xb0421c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb04220: ldr             x0, [x0, #0x7e0]
    // 0xb04224: StoreField: r1->field_1b = r0
    //     0xb04224: stur            w0, [x1, #0x1b]
    // 0xb04228: ldur            x0, [fp, #-0x10]
    // 0xb0422c: StoreField: r1->field_b = r0
    //     0xb0422c: stur            w0, [x1, #0xb]
    // 0xb04230: r0 = InkWell()
    //     0xb04230: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb04234: mov             x3, x0
    // 0xb04238: ldur            x0, [fp, #-8]
    // 0xb0423c: stur            x3, [fp, #-0x10]
    // 0xb04240: StoreField: r3->field_b = r0
    //     0xb04240: stur            w0, [x3, #0xb]
    // 0xb04244: ldur            x2, [fp, #-0x28]
    // 0xb04248: r1 = Function '<anonymous closure>':.
    //     0xb04248: add             x1, PP, #0x57, lsl #12  ; [pp+0x57f20] AnonymousClosure: (0xb0430c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::imageSlider (0xb031ec)
    //     0xb0424c: ldr             x1, [x1, #0xf20]
    // 0xb04250: r0 = AllocateClosure()
    //     0xb04250: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb04254: ldur            x2, [fp, #-0x10]
    // 0xb04258: StoreField: r2->field_f = r0
    //     0xb04258: stur            w0, [x2, #0xf]
    // 0xb0425c: r0 = true
    //     0xb0425c: add             x0, NULL, #0x20  ; true
    // 0xb04260: StoreField: r2->field_43 = r0
    //     0xb04260: stur            w0, [x2, #0x43]
    // 0xb04264: r1 = Instance_BoxShape
    //     0xb04264: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb04268: ldr             x1, [x1, #0x80]
    // 0xb0426c: StoreField: r2->field_47 = r1
    //     0xb0426c: stur            w1, [x2, #0x47]
    // 0xb04270: StoreField: r2->field_6f = r0
    //     0xb04270: stur            w0, [x2, #0x6f]
    // 0xb04274: r1 = false
    //     0xb04274: add             x1, NULL, #0x30  ; false
    // 0xb04278: StoreField: r2->field_73 = r1
    //     0xb04278: stur            w1, [x2, #0x73]
    // 0xb0427c: StoreField: r2->field_83 = r0
    //     0xb0427c: stur            w0, [x2, #0x83]
    // 0xb04280: StoreField: r2->field_7b = r1
    //     0xb04280: stur            w1, [x2, #0x7b]
    // 0xb04284: r0 = AnimatedContainer()
    //     0xb04284: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xb04288: stur            x0, [fp, #-8]
    // 0xb0428c: r16 = Instance_Cubic
    //     0xb0428c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xb04290: ldr             x16, [x16, #0xaf8]
    // 0xb04294: str             x16, [SP]
    // 0xb04298: mov             x1, x0
    // 0xb0429c: ldur            x2, [fp, #-0x10]
    // 0xb042a0: r3 = Instance_Duration
    //     0xb042a0: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb042a4: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xb042a4: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xb042a8: ldr             x4, [x4, #0xbc8]
    // 0xb042ac: r0 = AnimatedContainer()
    //     0xb042ac: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xb042b0: ldur            x0, [fp, #-8]
    // 0xb042b4: LeaveFrame
    //     0xb042b4: mov             SP, fp
    //     0xb042b8: ldp             fp, lr, [SP], #0x10
    // 0xb042bc: ret
    //     0xb042bc: ret             
    // 0xb042c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb042c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb042c4: b               #0xb0321c
    // 0xb042c8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb042c8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb042cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb042cc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb042d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb042d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb042d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb042d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb042d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb042d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb042dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb042dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb042e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb042e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb042e4: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb042e4: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb042e8: SaveReg d0
    //     0xb042e8: str             q0, [SP, #-0x10]!
    // 0xb042ec: SaveReg r1
    //     0xb042ec: str             x1, [SP, #-8]!
    // 0xb042f0: r0 = AllocateDouble()
    //     0xb042f0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb042f4: RestoreReg r1
    //     0xb042f4: ldr             x1, [SP], #8
    // 0xb042f8: RestoreReg d0
    //     0xb042f8: ldr             q0, [SP], #0x10
    // 0xb042fc: b               #0xb03bc8
    // 0xb04300: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb04300: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb04304: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb04304: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb04308: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb04308: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb0430c, size: 0xe8
    // 0xb0430c: EnterFrame
    //     0xb0430c: stp             fp, lr, [SP, #-0x10]!
    //     0xb04310: mov             fp, SP
    // 0xb04314: AllocStack(0x38)
    //     0xb04314: sub             SP, SP, #0x38
    // 0xb04318: SetupParameters()
    //     0xb04318: ldr             x0, [fp, #0x10]
    //     0xb0431c: ldur            w1, [x0, #0x17]
    //     0xb04320: add             x1, x1, HEAP, lsl #32
    // 0xb04324: CheckStackOverflow
    //     0xb04324: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb04328: cmp             SP, x16
    //     0xb0432c: b.ls            #0xb043e8
    // 0xb04330: LoadField: r0 = r1->field_f
    //     0xb04330: ldur            w0, [x1, #0xf]
    // 0xb04334: DecompressPointer r0
    //     0xb04334: add             x0, x0, HEAP, lsl #32
    // 0xb04338: LoadField: r2 = r0->field_b
    //     0xb04338: ldur            w2, [x0, #0xb]
    // 0xb0433c: DecompressPointer r2
    //     0xb0433c: add             x2, x2, HEAP, lsl #32
    // 0xb04340: cmp             w2, NULL
    // 0xb04344: b.eq            #0xb043f0
    // 0xb04348: LoadField: r0 = r2->field_23
    //     0xb04348: ldur            w0, [x2, #0x23]
    // 0xb0434c: DecompressPointer r0
    //     0xb0434c: add             x0, x0, HEAP, lsl #32
    // 0xb04350: cmp             w0, NULL
    // 0xb04354: b.ne            #0xb0435c
    // 0xb04358: r0 = ""
    //     0xb04358: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0435c: LoadField: r3 = r2->field_1f
    //     0xb0435c: ldur            w3, [x2, #0x1f]
    // 0xb04360: DecompressPointer r3
    //     0xb04360: add             x3, x3, HEAP, lsl #32
    // 0xb04364: cmp             w3, NULL
    // 0xb04368: b.ne            #0xb04370
    // 0xb0436c: r3 = ""
    //     0xb0436c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb04370: LoadField: r4 = r2->field_27
    //     0xb04370: ldur            w4, [x2, #0x27]
    // 0xb04374: DecompressPointer r4
    //     0xb04374: add             x4, x4, HEAP, lsl #32
    // 0xb04378: cmp             w4, NULL
    // 0xb0437c: b.ne            #0xb04384
    // 0xb04380: r4 = ""
    //     0xb04380: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb04384: LoadField: r5 = r2->field_f
    //     0xb04384: ldur            w5, [x2, #0xf]
    // 0xb04388: DecompressPointer r5
    //     0xb04388: add             x5, x5, HEAP, lsl #32
    // 0xb0438c: cmp             w5, NULL
    // 0xb04390: b.ne            #0xb04398
    // 0xb04394: r5 = ""
    //     0xb04394: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb04398: LoadField: r6 = r1->field_13
    //     0xb04398: ldur            w6, [x1, #0x13]
    // 0xb0439c: DecompressPointer r6
    //     0xb0439c: add             x6, x6, HEAP, lsl #32
    // 0xb043a0: LoadField: r1 = r2->field_3f
    //     0xb043a0: ldur            w1, [x2, #0x3f]
    // 0xb043a4: DecompressPointer r1
    //     0xb043a4: add             x1, x1, HEAP, lsl #32
    // 0xb043a8: stp             x0, x1, [SP, #0x28]
    // 0xb043ac: r16 = "product_page"
    //     0xb043ac: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xb043b0: ldr             x16, [x16, #0x480]
    // 0xb043b4: stp             x16, x3, [SP, #0x18]
    // 0xb043b8: stp             x5, x4, [SP, #8]
    // 0xb043bc: str             x6, [SP]
    // 0xb043c0: r4 = 0
    //     0xb043c0: movz            x4, #0
    // 0xb043c4: ldr             x0, [SP, #0x30]
    // 0xb043c8: r16 = UnlinkedCall_0x613b5c
    //     0xb043c8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57f28] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb043cc: add             x16, x16, #0xf28
    // 0xb043d0: ldp             x5, lr, [x16]
    // 0xb043d4: blr             lr
    // 0xb043d8: r0 = Null
    //     0xb043d8: mov             x0, NULL
    // 0xb043dc: LeaveFrame
    //     0xb043dc: mov             SP, fp
    //     0xb043e0: ldp             fp, lr, [SP], #0x10
    // 0xb043e4: ret
    //     0xb043e4: ret             
    // 0xb043e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb043e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb043ec: b               #0xb04330
    // 0xb043f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb043f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb043f4, size: 0x84
    // 0xb043f4: EnterFrame
    //     0xb043f4: stp             fp, lr, [SP, #-0x10]!
    //     0xb043f8: mov             fp, SP
    // 0xb043fc: AllocStack(0x10)
    //     0xb043fc: sub             SP, SP, #0x10
    // 0xb04400: SetupParameters()
    //     0xb04400: ldr             x0, [fp, #0x18]
    //     0xb04404: ldur            w1, [x0, #0x17]
    //     0xb04408: add             x1, x1, HEAP, lsl #32
    //     0xb0440c: stur            x1, [fp, #-8]
    // 0xb04410: CheckStackOverflow
    //     0xb04410: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb04414: cmp             SP, x16
    //     0xb04418: b.ls            #0xb04470
    // 0xb0441c: r1 = 1
    //     0xb0441c: movz            x1, #0x1
    // 0xb04420: r0 = AllocateContext()
    //     0xb04420: bl              #0x16f6108  ; AllocateContextStub
    // 0xb04424: mov             x1, x0
    // 0xb04428: ldur            x0, [fp, #-8]
    // 0xb0442c: StoreField: r1->field_b = r0
    //     0xb0442c: stur            w0, [x1, #0xb]
    // 0xb04430: ldr             x2, [fp, #0x10]
    // 0xb04434: StoreField: r1->field_f = r2
    //     0xb04434: stur            w2, [x1, #0xf]
    // 0xb04438: LoadField: r3 = r0->field_f
    //     0xb04438: ldur            w3, [x0, #0xf]
    // 0xb0443c: DecompressPointer r3
    //     0xb0443c: add             x3, x3, HEAP, lsl #32
    // 0xb04440: mov             x2, x1
    // 0xb04444: stur            x3, [fp, #-0x10]
    // 0xb04448: r1 = Function '<anonymous closure>':.
    //     0xb04448: add             x1, PP, #0x57, lsl #12  ; [pp+0x57f38] AnonymousClosure: (0xa59828), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildMediaCarousel (0xa598e8)
    //     0xb0444c: ldr             x1, [x1, #0xf38]
    // 0xb04450: r0 = AllocateClosure()
    //     0xb04450: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb04454: ldur            x1, [fp, #-0x10]
    // 0xb04458: mov             x2, x0
    // 0xb0445c: r0 = setState()
    //     0xb0445c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb04460: r0 = Null
    //     0xb04460: mov             x0, NULL
    // 0xb04464: LeaveFrame
    //     0xb04464: mov             SP, fp
    //     0xb04468: ldp             fp, lr, [SP], #0x10
    // 0xb0446c: ret
    //     0xb0446c: ret             
    // 0xb04470: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb04470: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb04474: b               #0xb0441c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb04478, size: 0xd4
    // 0xb04478: EnterFrame
    //     0xb04478: stp             fp, lr, [SP, #-0x10]!
    //     0xb0447c: mov             fp, SP
    // 0xb04480: AllocStack(0x38)
    //     0xb04480: sub             SP, SP, #0x38
    // 0xb04484: SetupParameters()
    //     0xb04484: ldr             x0, [fp, #0x10]
    //     0xb04488: ldur            w1, [x0, #0x17]
    //     0xb0448c: add             x1, x1, HEAP, lsl #32
    // 0xb04490: CheckStackOverflow
    //     0xb04490: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb04494: cmp             SP, x16
    //     0xb04498: b.ls            #0xb04540
    // 0xb0449c: LoadField: r0 = r1->field_f
    //     0xb0449c: ldur            w0, [x1, #0xf]
    // 0xb044a0: DecompressPointer r0
    //     0xb044a0: add             x0, x0, HEAP, lsl #32
    // 0xb044a4: LoadField: r1 = r0->field_b
    //     0xb044a4: ldur            w1, [x0, #0xb]
    // 0xb044a8: DecompressPointer r1
    //     0xb044a8: add             x1, x1, HEAP, lsl #32
    // 0xb044ac: cmp             w1, NULL
    // 0xb044b0: b.eq            #0xb04548
    // 0xb044b4: LoadField: r0 = r1->field_23
    //     0xb044b4: ldur            w0, [x1, #0x23]
    // 0xb044b8: DecompressPointer r0
    //     0xb044b8: add             x0, x0, HEAP, lsl #32
    // 0xb044bc: LoadField: r2 = r1->field_1f
    //     0xb044bc: ldur            w2, [x1, #0x1f]
    // 0xb044c0: DecompressPointer r2
    //     0xb044c0: add             x2, x2, HEAP, lsl #32
    // 0xb044c4: LoadField: r3 = r1->field_27
    //     0xb044c4: ldur            w3, [x1, #0x27]
    // 0xb044c8: DecompressPointer r3
    //     0xb044c8: add             x3, x3, HEAP, lsl #32
    // 0xb044cc: LoadField: r4 = r1->field_13
    //     0xb044cc: ldur            w4, [x1, #0x13]
    // 0xb044d0: DecompressPointer r4
    //     0xb044d0: add             x4, x4, HEAP, lsl #32
    // 0xb044d4: cmp             w4, NULL
    // 0xb044d8: b.ne            #0xb044e4
    // 0xb044dc: r4 = Null
    //     0xb044dc: mov             x4, NULL
    // 0xb044e0: b               #0xb044f0
    // 0xb044e4: LoadField: r5 = r4->field_b
    //     0xb044e4: ldur            w5, [x4, #0xb]
    // 0xb044e8: DecompressPointer r5
    //     0xb044e8: add             x5, x5, HEAP, lsl #32
    // 0xb044ec: mov             x4, x5
    // 0xb044f0: LoadField: r5 = r1->field_f
    //     0xb044f0: ldur            w5, [x1, #0xf]
    // 0xb044f4: DecompressPointer r5
    //     0xb044f4: add             x5, x5, HEAP, lsl #32
    // 0xb044f8: LoadField: r6 = r1->field_3b
    //     0xb044f8: ldur            w6, [x1, #0x3b]
    // 0xb044fc: DecompressPointer r6
    //     0xb044fc: add             x6, x6, HEAP, lsl #32
    // 0xb04500: stp             x0, x6, [SP, #0x28]
    // 0xb04504: r16 = "product_page"
    //     0xb04504: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xb04508: ldr             x16, [x16, #0x480]
    // 0xb0450c: stp             x16, x2, [SP, #0x18]
    // 0xb04510: stp             x4, x3, [SP, #8]
    // 0xb04514: str             x5, [SP]
    // 0xb04518: r4 = 0
    //     0xb04518: movz            x4, #0
    // 0xb0451c: ldr             x0, [SP, #0x30]
    // 0xb04520: r16 = UnlinkedCall_0x613b5c
    //     0xb04520: add             x16, PP, #0x57, lsl #12  ; [pp+0x57f40] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb04524: add             x16, x16, #0xf40
    // 0xb04528: ldp             x5, lr, [x16]
    // 0xb0452c: blr             lr
    // 0xb04530: r0 = Null
    //     0xb04530: mov             x0, NULL
    // 0xb04534: LeaveFrame
    //     0xb04534: mov             SP, fp
    //     0xb04538: ldp             fp, lr, [SP], #0x10
    // 0xb0453c: ret
    //     0xb0453c: ret             
    // 0xb04540: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb04540: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb04544: b               #0xb0449c
    // 0xb04548: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb04548: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc8768c, size: 0x84
    // 0xc8768c: EnterFrame
    //     0xc8768c: stp             fp, lr, [SP, #-0x10]!
    //     0xc87690: mov             fp, SP
    // 0xc87694: AllocStack(0x8)
    //     0xc87694: sub             SP, SP, #8
    // 0xc87698: SetupParameters(_GroupCarouselItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xc87698: mov             x0, x1
    //     0xc8769c: stur            x1, [fp, #-8]
    // 0xc876a0: CheckStackOverflow
    //     0xc876a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc876a4: cmp             SP, x16
    //     0xc876a8: b.ls            #0xc876f0
    // 0xc876ac: LoadField: r1 = r0->field_13
    //     0xc876ac: ldur            w1, [x0, #0x13]
    // 0xc876b0: DecompressPointer r1
    //     0xc876b0: add             x1, x1, HEAP, lsl #32
    // 0xc876b4: r16 = Sentinel
    //     0xc876b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc876b8: cmp             w1, w16
    // 0xc876bc: b.eq            #0xc876f8
    // 0xc876c0: r0 = dispose()
    //     0xc876c0: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc876c4: ldur            x0, [fp, #-8]
    // 0xc876c8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc876c8: ldur            w1, [x0, #0x17]
    // 0xc876cc: DecompressPointer r1
    //     0xc876cc: add             x1, x1, HEAP, lsl #32
    // 0xc876d0: r16 = Sentinel
    //     0xc876d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc876d4: cmp             w1, w16
    // 0xc876d8: b.eq            #0xc87704
    // 0xc876dc: r0 = dispose()
    //     0xc876dc: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc876e0: r0 = Null
    //     0xc876e0: mov             x0, NULL
    // 0xc876e4: LeaveFrame
    //     0xc876e4: mov             SP, fp
    //     0xc876e8: ldp             fp, lr, [SP], #0x10
    // 0xc876ec: ret
    //     0xc876ec: ret             
    // 0xc876f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc876f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc876f4: b               #0xc876ac
    // 0xc876f8: r9 = _pageController
    //     0xc876f8: add             x9, PP, #0x57, lsl #12  ; [pp+0x57ea8] Field <_GroupCarouselItemViewState@1494318134._pageController@1494318134>: late (offset: 0x14)
    //     0xc876fc: ldr             x9, [x9, #0xea8]
    // 0xc87700: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87700: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc87704: r9 = _imagePageController
    //     0xc87704: add             x9, PP, #0x57, lsl #12  ; [pp+0x57ee8] Field <_GroupCarouselItemViewState@1494318134._imagePageController@1494318134>: late (offset: 0x18)
    //     0xc87708: ldr             x9, [x9, #0xee8]
    // 0xc8770c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc8770c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4146, size: 0x48, field offset: 0xc
//   const constructor, 
class GroupCarouselItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7dea4, size: 0x34
    // 0xc7dea4: EnterFrame
    //     0xc7dea4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7dea8: mov             fp, SP
    // 0xc7deac: mov             x0, x1
    // 0xc7deb0: r1 = <GroupCarouselItemView>
    //     0xc7deb0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48b58] TypeArguments: <GroupCarouselItemView>
    //     0xc7deb4: ldr             x1, [x1, #0xb58]
    // 0xc7deb8: r0 = _GroupCarouselItemViewState()
    //     0xc7deb8: bl              #0xc7ded8  ; Allocate_GroupCarouselItemViewStateStub -> _GroupCarouselItemViewState (size=0x24)
    // 0xc7debc: r1 = Sentinel
    //     0xc7debc: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7dec0: StoreField: r0->field_13 = r1
    //     0xc7dec0: stur            w1, [x0, #0x13]
    // 0xc7dec4: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7dec4: stur            w1, [x0, #0x17]
    // 0xc7dec8: StoreField: r0->field_1b = rZR
    //     0xc7dec8: stur            xzr, [x0, #0x1b]
    // 0xc7decc: LeaveFrame
    //     0xc7decc: mov             SP, fp
    //     0xc7ded0: ldp             fp, lr, [SP], #0x10
    // 0xc7ded4: ret
    //     0xc7ded4: ret             
  }
}
