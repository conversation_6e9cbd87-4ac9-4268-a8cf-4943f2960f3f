// lib: , url: package:customer_app/app/presentation/views/line/post_order/order_detail/know_more_bottom_sheet.dart

// class id: 1049542, size: 0x8
class :: {
}

// class id: 3232, size: 0x14, field offset: 0x14
class _KnowMoreBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbfc304, size: 0xe10
    // 0xbfc304: EnterFrame
    //     0xbfc304: stp             fp, lr, [SP, #-0x10]!
    //     0xbfc308: mov             fp, SP
    // 0xbfc30c: AllocStack(0x68)
    //     0xbfc30c: sub             SP, SP, #0x68
    // 0xbfc310: SetupParameters(_KnowMoreBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbfc310: mov             x0, x1
    //     0xbfc314: stur            x1, [fp, #-8]
    //     0xbfc318: mov             x1, x2
    //     0xbfc31c: stur            x2, [fp, #-0x10]
    // 0xbfc320: CheckStackOverflow
    //     0xbfc320: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfc324: cmp             SP, x16
    //     0xbfc328: b.ls            #0xbfd0f4
    // 0xbfc32c: r1 = 1
    //     0xbfc32c: movz            x1, #0x1
    // 0xbfc330: r0 = AllocateContext()
    //     0xbfc330: bl              #0x16f6108  ; AllocateContextStub
    // 0xbfc334: mov             x2, x0
    // 0xbfc338: ldur            x0, [fp, #-8]
    // 0xbfc33c: stur            x2, [fp, #-0x18]
    // 0xbfc340: StoreField: r2->field_f = r0
    //     0xbfc340: stur            w0, [x2, #0xf]
    // 0xbfc344: ldur            x1, [fp, #-0x10]
    // 0xbfc348: r0 = of()
    //     0xbfc348: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfc34c: LoadField: r1 = r0->field_5b
    //     0xbfc34c: ldur            w1, [x0, #0x5b]
    // 0xbfc350: DecompressPointer r1
    //     0xbfc350: add             x1, x1, HEAP, lsl #32
    // 0xbfc354: stur            x1, [fp, #-0x20]
    // 0xbfc358: r0 = BoxDecoration()
    //     0xbfc358: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbfc35c: mov             x1, x0
    // 0xbfc360: ldur            x0, [fp, #-0x20]
    // 0xbfc364: stur            x1, [fp, #-0x28]
    // 0xbfc368: StoreField: r1->field_7 = r0
    //     0xbfc368: stur            w0, [x1, #7]
    // 0xbfc36c: r0 = Instance_BoxShape
    //     0xbfc36c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xbfc370: ldr             x0, [x0, #0x970]
    // 0xbfc374: StoreField: r1->field_23 = r0
    //     0xbfc374: stur            w0, [x1, #0x23]
    // 0xbfc378: r0 = SvgPicture()
    //     0xbfc378: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbfc37c: stur            x0, [fp, #-0x20]
    // 0xbfc380: r16 = Instance_BoxFit
    //     0xbfc380: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbfc384: ldr             x16, [x16, #0xb18]
    // 0xbfc388: str             x16, [SP]
    // 0xbfc38c: mov             x1, x0
    // 0xbfc390: r2 = "assets/images/replacement.svg"
    //     0xbfc390: add             x2, PP, #0x5c, lsl #12  ; [pp+0x5c0f0] "assets/images/replacement.svg"
    //     0xbfc394: ldr             x2, [x2, #0xf0]
    // 0xbfc398: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbfc398: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbfc39c: ldr             x4, [x4, #0xb0]
    // 0xbfc3a0: r0 = SvgPicture.asset()
    //     0xbfc3a0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbfc3a4: r0 = Container()
    //     0xbfc3a4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbfc3a8: stur            x0, [fp, #-0x30]
    // 0xbfc3ac: r16 = 40.000000
    //     0xbfc3ac: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xbfc3b0: ldr             x16, [x16, #8]
    // 0xbfc3b4: r30 = 40.000000
    //     0xbfc3b4: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xbfc3b8: ldr             lr, [lr, #8]
    // 0xbfc3bc: stp             lr, x16, [SP, #0x10]
    // 0xbfc3c0: ldur            x16, [fp, #-0x28]
    // 0xbfc3c4: ldur            lr, [fp, #-0x20]
    // 0xbfc3c8: stp             lr, x16, [SP]
    // 0xbfc3cc: mov             x1, x0
    // 0xbfc3d0: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xbfc3d0: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xbfc3d4: ldr             x4, [x4, #0x870]
    // 0xbfc3d8: r0 = Container()
    //     0xbfc3d8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbfc3dc: r0 = SvgPicture()
    //     0xbfc3dc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbfc3e0: stur            x0, [fp, #-0x20]
    // 0xbfc3e4: r16 = Instance_BoxFit
    //     0xbfc3e4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbfc3e8: ldr             x16, [x16, #0xb18]
    // 0xbfc3ec: str             x16, [SP]
    // 0xbfc3f0: mov             x1, x0
    // 0xbfc3f4: r2 = "assets/images/replacement_check.svg"
    //     0xbfc3f4: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c90] "assets/images/replacement_check.svg"
    //     0xbfc3f8: ldr             x2, [x2, #0xc90]
    // 0xbfc3fc: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbfc3fc: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbfc400: ldr             x4, [x4, #0xb0]
    // 0xbfc404: r0 = SvgPicture.asset()
    //     0xbfc404: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbfc408: r1 = Null
    //     0xbfc408: mov             x1, NULL
    // 0xbfc40c: r2 = 4
    //     0xbfc40c: movz            x2, #0x4
    // 0xbfc410: r0 = AllocateArray()
    //     0xbfc410: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfc414: mov             x2, x0
    // 0xbfc418: ldur            x0, [fp, #-0x30]
    // 0xbfc41c: stur            x2, [fp, #-0x28]
    // 0xbfc420: StoreField: r2->field_f = r0
    //     0xbfc420: stur            w0, [x2, #0xf]
    // 0xbfc424: ldur            x0, [fp, #-0x20]
    // 0xbfc428: StoreField: r2->field_13 = r0
    //     0xbfc428: stur            w0, [x2, #0x13]
    // 0xbfc42c: r1 = <Widget>
    //     0xbfc42c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfc430: r0 = AllocateGrowableArray()
    //     0xbfc430: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfc434: mov             x1, x0
    // 0xbfc438: ldur            x0, [fp, #-0x28]
    // 0xbfc43c: stur            x1, [fp, #-0x20]
    // 0xbfc440: StoreField: r1->field_f = r0
    //     0xbfc440: stur            w0, [x1, #0xf]
    // 0xbfc444: r2 = 4
    //     0xbfc444: movz            x2, #0x4
    // 0xbfc448: StoreField: r1->field_b = r2
    //     0xbfc448: stur            w2, [x1, #0xb]
    // 0xbfc44c: r0 = Stack()
    //     0xbfc44c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbfc450: mov             x1, x0
    // 0xbfc454: r0 = Instance_Alignment
    //     0xbfc454: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xbfc458: ldr             x0, [x0, #0x950]
    // 0xbfc45c: stur            x1, [fp, #-0x28]
    // 0xbfc460: StoreField: r1->field_f = r0
    //     0xbfc460: stur            w0, [x1, #0xf]
    // 0xbfc464: r2 = Instance_StackFit
    //     0xbfc464: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbfc468: ldr             x2, [x2, #0xfa8]
    // 0xbfc46c: ArrayStore: r1[0] = r2  ; List_4
    //     0xbfc46c: stur            w2, [x1, #0x17]
    // 0xbfc470: r3 = Instance_Clip
    //     0xbfc470: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbfc474: ldr             x3, [x3, #0x7e0]
    // 0xbfc478: StoreField: r1->field_1b = r3
    //     0xbfc478: stur            w3, [x1, #0x1b]
    // 0xbfc47c: ldur            x4, [fp, #-0x20]
    // 0xbfc480: StoreField: r1->field_b = r4
    //     0xbfc480: stur            w4, [x1, #0xb]
    // 0xbfc484: ldur            x4, [fp, #-8]
    // 0xbfc488: LoadField: r5 = r4->field_b
    //     0xbfc488: ldur            w5, [x4, #0xb]
    // 0xbfc48c: DecompressPointer r5
    //     0xbfc48c: add             x5, x5, HEAP, lsl #32
    // 0xbfc490: cmp             w5, NULL
    // 0xbfc494: b.eq            #0xbfd0fc
    // 0xbfc498: LoadField: r6 = r5->field_b
    //     0xbfc498: ldur            w6, [x5, #0xb]
    // 0xbfc49c: DecompressPointer r6
    //     0xbfc49c: add             x6, x6, HEAP, lsl #32
    // 0xbfc4a0: cmp             w6, NULL
    // 0xbfc4a4: b.ne            #0xbfc4b0
    // 0xbfc4a8: r5 = Null
    //     0xbfc4a8: mov             x5, NULL
    // 0xbfc4ac: b               #0xbfc4e4
    // 0xbfc4b0: LoadField: r5 = r6->field_b
    //     0xbfc4b0: ldur            w5, [x6, #0xb]
    // 0xbfc4b4: DecompressPointer r5
    //     0xbfc4b4: add             x5, x5, HEAP, lsl #32
    // 0xbfc4b8: cmp             w5, NULL
    // 0xbfc4bc: b.ne            #0xbfc4c8
    // 0xbfc4c0: r5 = Null
    //     0xbfc4c0: mov             x5, NULL
    // 0xbfc4c4: b               #0xbfc4e4
    // 0xbfc4c8: LoadField: r6 = r5->field_7
    //     0xbfc4c8: ldur            w6, [x5, #7]
    // 0xbfc4cc: DecompressPointer r6
    //     0xbfc4cc: add             x6, x6, HEAP, lsl #32
    // 0xbfc4d0: LoadField: r5 = r6->field_7
    //     0xbfc4d0: ldur            w5, [x6, #7]
    // 0xbfc4d4: DecompressPointer r5
    //     0xbfc4d4: add             x5, x5, HEAP, lsl #32
    // 0xbfc4d8: LoadField: r6 = r5->field_b
    //     0xbfc4d8: ldur            w6, [x5, #0xb]
    // 0xbfc4dc: DecompressPointer r6
    //     0xbfc4dc: add             x6, x6, HEAP, lsl #32
    // 0xbfc4e0: mov             x5, x6
    // 0xbfc4e4: str             x5, [SP]
    // 0xbfc4e8: r0 = _interpolateSingle()
    //     0xbfc4e8: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbfc4ec: ldur            x1, [fp, #-0x10]
    // 0xbfc4f0: stur            x0, [fp, #-0x20]
    // 0xbfc4f4: r0 = of()
    //     0xbfc4f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfc4f8: LoadField: r1 = r0->field_87
    //     0xbfc4f8: ldur            w1, [x0, #0x87]
    // 0xbfc4fc: DecompressPointer r1
    //     0xbfc4fc: add             x1, x1, HEAP, lsl #32
    // 0xbfc500: LoadField: r0 = r1->field_7
    //     0xbfc500: ldur            w0, [x1, #7]
    // 0xbfc504: DecompressPointer r0
    //     0xbfc504: add             x0, x0, HEAP, lsl #32
    // 0xbfc508: r16 = Instance_Color
    //     0xbfc508: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfc50c: r30 = 12.000000
    //     0xbfc50c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfc510: ldr             lr, [lr, #0x9e8]
    // 0xbfc514: stp             lr, x16, [SP]
    // 0xbfc518: mov             x1, x0
    // 0xbfc51c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbfc51c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbfc520: ldr             x4, [x4, #0x9b8]
    // 0xbfc524: r0 = copyWith()
    //     0xbfc524: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfc528: stur            x0, [fp, #-0x30]
    // 0xbfc52c: r0 = Text()
    //     0xbfc52c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfc530: mov             x3, x0
    // 0xbfc534: ldur            x0, [fp, #-0x20]
    // 0xbfc538: stur            x3, [fp, #-0x38]
    // 0xbfc53c: StoreField: r3->field_b = r0
    //     0xbfc53c: stur            w0, [x3, #0xb]
    // 0xbfc540: ldur            x0, [fp, #-0x30]
    // 0xbfc544: StoreField: r3->field_13 = r0
    //     0xbfc544: stur            w0, [x3, #0x13]
    // 0xbfc548: r1 = Null
    //     0xbfc548: mov             x1, NULL
    // 0xbfc54c: r2 = 6
    //     0xbfc54c: movz            x2, #0x6
    // 0xbfc550: r0 = AllocateArray()
    //     0xbfc550: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfc554: mov             x2, x0
    // 0xbfc558: ldur            x0, [fp, #-0x28]
    // 0xbfc55c: stur            x2, [fp, #-0x20]
    // 0xbfc560: StoreField: r2->field_f = r0
    //     0xbfc560: stur            w0, [x2, #0xf]
    // 0xbfc564: r16 = Instance_SizedBox
    //     0xbfc564: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xbfc568: ldr             x16, [x16, #0x328]
    // 0xbfc56c: StoreField: r2->field_13 = r16
    //     0xbfc56c: stur            w16, [x2, #0x13]
    // 0xbfc570: ldur            x0, [fp, #-0x38]
    // 0xbfc574: ArrayStore: r2[0] = r0  ; List_4
    //     0xbfc574: stur            w0, [x2, #0x17]
    // 0xbfc578: r1 = <Widget>
    //     0xbfc578: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfc57c: r0 = AllocateGrowableArray()
    //     0xbfc57c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfc580: mov             x1, x0
    // 0xbfc584: ldur            x0, [fp, #-0x20]
    // 0xbfc588: stur            x1, [fp, #-0x28]
    // 0xbfc58c: StoreField: r1->field_f = r0
    //     0xbfc58c: stur            w0, [x1, #0xf]
    // 0xbfc590: r2 = 6
    //     0xbfc590: movz            x2, #0x6
    // 0xbfc594: StoreField: r1->field_b = r2
    //     0xbfc594: stur            w2, [x1, #0xb]
    // 0xbfc598: r0 = Column()
    //     0xbfc598: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbfc59c: mov             x2, x0
    // 0xbfc5a0: r0 = Instance_Axis
    //     0xbfc5a0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbfc5a4: stur            x2, [fp, #-0x20]
    // 0xbfc5a8: StoreField: r2->field_f = r0
    //     0xbfc5a8: stur            w0, [x2, #0xf]
    // 0xbfc5ac: r3 = Instance_MainAxisAlignment
    //     0xbfc5ac: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfc5b0: ldr             x3, [x3, #0xa08]
    // 0xbfc5b4: StoreField: r2->field_13 = r3
    //     0xbfc5b4: stur            w3, [x2, #0x13]
    // 0xbfc5b8: r4 = Instance_MainAxisSize
    //     0xbfc5b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfc5bc: ldr             x4, [x4, #0xa10]
    // 0xbfc5c0: ArrayStore: r2[0] = r4  ; List_4
    //     0xbfc5c0: stur            w4, [x2, #0x17]
    // 0xbfc5c4: r5 = Instance_CrossAxisAlignment
    //     0xbfc5c4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbfc5c8: ldr             x5, [x5, #0xa18]
    // 0xbfc5cc: StoreField: r2->field_1b = r5
    //     0xbfc5cc: stur            w5, [x2, #0x1b]
    // 0xbfc5d0: r6 = Instance_VerticalDirection
    //     0xbfc5d0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfc5d4: ldr             x6, [x6, #0xa20]
    // 0xbfc5d8: StoreField: r2->field_23 = r6
    //     0xbfc5d8: stur            w6, [x2, #0x23]
    // 0xbfc5dc: r7 = Instance_Clip
    //     0xbfc5dc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfc5e0: ldr             x7, [x7, #0x38]
    // 0xbfc5e4: StoreField: r2->field_2b = r7
    //     0xbfc5e4: stur            w7, [x2, #0x2b]
    // 0xbfc5e8: StoreField: r2->field_2f = rZR
    //     0xbfc5e8: stur            xzr, [x2, #0x2f]
    // 0xbfc5ec: ldur            x1, [fp, #-0x28]
    // 0xbfc5f0: StoreField: r2->field_b = r1
    //     0xbfc5f0: stur            w1, [x2, #0xb]
    // 0xbfc5f4: ldur            x1, [fp, #-0x10]
    // 0xbfc5f8: r0 = of()
    //     0xbfc5f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfc5fc: LoadField: r1 = r0->field_5b
    //     0xbfc5fc: ldur            w1, [x0, #0x5b]
    // 0xbfc600: DecompressPointer r1
    //     0xbfc600: add             x1, x1, HEAP, lsl #32
    // 0xbfc604: stur            x1, [fp, #-0x28]
    // 0xbfc608: r0 = BoxDecoration()
    //     0xbfc608: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbfc60c: mov             x1, x0
    // 0xbfc610: ldur            x0, [fp, #-0x28]
    // 0xbfc614: stur            x1, [fp, #-0x30]
    // 0xbfc618: StoreField: r1->field_7 = r0
    //     0xbfc618: stur            w0, [x1, #7]
    // 0xbfc61c: r0 = Instance_BoxShape
    //     0xbfc61c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xbfc620: ldr             x0, [x0, #0x970]
    // 0xbfc624: StoreField: r1->field_23 = r0
    //     0xbfc624: stur            w0, [x1, #0x23]
    // 0xbfc628: r0 = SvgPicture()
    //     0xbfc628: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbfc62c: stur            x0, [fp, #-0x28]
    // 0xbfc630: r16 = Instance_BoxFit
    //     0xbfc630: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbfc634: ldr             x16, [x16, #0xb18]
    // 0xbfc638: str             x16, [SP]
    // 0xbfc63c: mov             x1, x0
    // 0xbfc640: r2 = "assets/images/exchange.svg"
    //     0xbfc640: add             x2, PP, #0x52, lsl #12  ; [pp+0x52ca0] "assets/images/exchange.svg"
    //     0xbfc644: ldr             x2, [x2, #0xca0]
    // 0xbfc648: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbfc648: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbfc64c: ldr             x4, [x4, #0xb0]
    // 0xbfc650: r0 = SvgPicture.asset()
    //     0xbfc650: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbfc654: r0 = Container()
    //     0xbfc654: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbfc658: stur            x0, [fp, #-0x38]
    // 0xbfc65c: r16 = 40.000000
    //     0xbfc65c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xbfc660: ldr             x16, [x16, #8]
    // 0xbfc664: r30 = 40.000000
    //     0xbfc664: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xbfc668: ldr             lr, [lr, #8]
    // 0xbfc66c: stp             lr, x16, [SP, #0x10]
    // 0xbfc670: ldur            x16, [fp, #-0x30]
    // 0xbfc674: ldur            lr, [fp, #-0x28]
    // 0xbfc678: stp             lr, x16, [SP]
    // 0xbfc67c: mov             x1, x0
    // 0xbfc680: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xbfc680: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xbfc684: ldr             x4, [x4, #0x870]
    // 0xbfc688: r0 = Container()
    //     0xbfc688: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbfc68c: r0 = SvgPicture()
    //     0xbfc68c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbfc690: stur            x0, [fp, #-0x28]
    // 0xbfc694: r16 = Instance_BoxFit
    //     0xbfc694: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbfc698: ldr             x16, [x16, #0xb18]
    // 0xbfc69c: str             x16, [SP]
    // 0xbfc6a0: mov             x1, x0
    // 0xbfc6a4: r2 = "assets/images/exchange_check.svg"
    //     0xbfc6a4: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c98] "assets/images/exchange_check.svg"
    //     0xbfc6a8: ldr             x2, [x2, #0xc98]
    // 0xbfc6ac: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbfc6ac: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbfc6b0: ldr             x4, [x4, #0xb0]
    // 0xbfc6b4: r0 = SvgPicture.asset()
    //     0xbfc6b4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbfc6b8: r1 = Null
    //     0xbfc6b8: mov             x1, NULL
    // 0xbfc6bc: r2 = 4
    //     0xbfc6bc: movz            x2, #0x4
    // 0xbfc6c0: r0 = AllocateArray()
    //     0xbfc6c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfc6c4: mov             x2, x0
    // 0xbfc6c8: ldur            x0, [fp, #-0x38]
    // 0xbfc6cc: stur            x2, [fp, #-0x30]
    // 0xbfc6d0: StoreField: r2->field_f = r0
    //     0xbfc6d0: stur            w0, [x2, #0xf]
    // 0xbfc6d4: ldur            x0, [fp, #-0x28]
    // 0xbfc6d8: StoreField: r2->field_13 = r0
    //     0xbfc6d8: stur            w0, [x2, #0x13]
    // 0xbfc6dc: r1 = <Widget>
    //     0xbfc6dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfc6e0: r0 = AllocateGrowableArray()
    //     0xbfc6e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfc6e4: mov             x1, x0
    // 0xbfc6e8: ldur            x0, [fp, #-0x30]
    // 0xbfc6ec: stur            x1, [fp, #-0x28]
    // 0xbfc6f0: StoreField: r1->field_f = r0
    //     0xbfc6f0: stur            w0, [x1, #0xf]
    // 0xbfc6f4: r2 = 4
    //     0xbfc6f4: movz            x2, #0x4
    // 0xbfc6f8: StoreField: r1->field_b = r2
    //     0xbfc6f8: stur            w2, [x1, #0xb]
    // 0xbfc6fc: r0 = Stack()
    //     0xbfc6fc: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbfc700: mov             x1, x0
    // 0xbfc704: r0 = Instance_Alignment
    //     0xbfc704: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xbfc708: ldr             x0, [x0, #0x950]
    // 0xbfc70c: stur            x1, [fp, #-0x30]
    // 0xbfc710: StoreField: r1->field_f = r0
    //     0xbfc710: stur            w0, [x1, #0xf]
    // 0xbfc714: r0 = Instance_StackFit
    //     0xbfc714: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbfc718: ldr             x0, [x0, #0xfa8]
    // 0xbfc71c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbfc71c: stur            w0, [x1, #0x17]
    // 0xbfc720: r0 = Instance_Clip
    //     0xbfc720: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbfc724: ldr             x0, [x0, #0x7e0]
    // 0xbfc728: StoreField: r1->field_1b = r0
    //     0xbfc728: stur            w0, [x1, #0x1b]
    // 0xbfc72c: ldur            x2, [fp, #-0x28]
    // 0xbfc730: StoreField: r1->field_b = r2
    //     0xbfc730: stur            w2, [x1, #0xb]
    // 0xbfc734: ldur            x2, [fp, #-8]
    // 0xbfc738: LoadField: r3 = r2->field_b
    //     0xbfc738: ldur            w3, [x2, #0xb]
    // 0xbfc73c: DecompressPointer r3
    //     0xbfc73c: add             x3, x3, HEAP, lsl #32
    // 0xbfc740: cmp             w3, NULL
    // 0xbfc744: b.eq            #0xbfd100
    // 0xbfc748: LoadField: r4 = r3->field_b
    //     0xbfc748: ldur            w4, [x3, #0xb]
    // 0xbfc74c: DecompressPointer r4
    //     0xbfc74c: add             x4, x4, HEAP, lsl #32
    // 0xbfc750: cmp             w4, NULL
    // 0xbfc754: b.ne            #0xbfc760
    // 0xbfc758: r4 = Null
    //     0xbfc758: mov             x4, NULL
    // 0xbfc75c: b               #0xbfc798
    // 0xbfc760: LoadField: r3 = r4->field_b
    //     0xbfc760: ldur            w3, [x4, #0xb]
    // 0xbfc764: DecompressPointer r3
    //     0xbfc764: add             x3, x3, HEAP, lsl #32
    // 0xbfc768: cmp             w3, NULL
    // 0xbfc76c: b.ne            #0xbfc778
    // 0xbfc770: r3 = Null
    //     0xbfc770: mov             x3, NULL
    // 0xbfc774: b               #0xbfc794
    // 0xbfc778: LoadField: r4 = r3->field_7
    //     0xbfc778: ldur            w4, [x3, #7]
    // 0xbfc77c: DecompressPointer r4
    //     0xbfc77c: add             x4, x4, HEAP, lsl #32
    // 0xbfc780: LoadField: r3 = r4->field_b
    //     0xbfc780: ldur            w3, [x4, #0xb]
    // 0xbfc784: DecompressPointer r3
    //     0xbfc784: add             x3, x3, HEAP, lsl #32
    // 0xbfc788: LoadField: r4 = r3->field_b
    //     0xbfc788: ldur            w4, [x3, #0xb]
    // 0xbfc78c: DecompressPointer r4
    //     0xbfc78c: add             x4, x4, HEAP, lsl #32
    // 0xbfc790: mov             x3, x4
    // 0xbfc794: mov             x4, x3
    // 0xbfc798: ldur            x3, [fp, #-0x20]
    // 0xbfc79c: str             x4, [SP]
    // 0xbfc7a0: r0 = _interpolateSingle()
    //     0xbfc7a0: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbfc7a4: ldur            x1, [fp, #-0x10]
    // 0xbfc7a8: stur            x0, [fp, #-0x28]
    // 0xbfc7ac: r0 = of()
    //     0xbfc7ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfc7b0: LoadField: r1 = r0->field_87
    //     0xbfc7b0: ldur            w1, [x0, #0x87]
    // 0xbfc7b4: DecompressPointer r1
    //     0xbfc7b4: add             x1, x1, HEAP, lsl #32
    // 0xbfc7b8: LoadField: r0 = r1->field_7
    //     0xbfc7b8: ldur            w0, [x1, #7]
    // 0xbfc7bc: DecompressPointer r0
    //     0xbfc7bc: add             x0, x0, HEAP, lsl #32
    // 0xbfc7c0: r16 = Instance_Color
    //     0xbfc7c0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfc7c4: r30 = 12.000000
    //     0xbfc7c4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfc7c8: ldr             lr, [lr, #0x9e8]
    // 0xbfc7cc: stp             lr, x16, [SP]
    // 0xbfc7d0: mov             x1, x0
    // 0xbfc7d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbfc7d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbfc7d8: ldr             x4, [x4, #0x9b8]
    // 0xbfc7dc: r0 = copyWith()
    //     0xbfc7dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfc7e0: stur            x0, [fp, #-0x38]
    // 0xbfc7e4: r0 = Text()
    //     0xbfc7e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfc7e8: mov             x3, x0
    // 0xbfc7ec: ldur            x0, [fp, #-0x28]
    // 0xbfc7f0: stur            x3, [fp, #-0x40]
    // 0xbfc7f4: StoreField: r3->field_b = r0
    //     0xbfc7f4: stur            w0, [x3, #0xb]
    // 0xbfc7f8: ldur            x0, [fp, #-0x38]
    // 0xbfc7fc: StoreField: r3->field_13 = r0
    //     0xbfc7fc: stur            w0, [x3, #0x13]
    // 0xbfc800: r1 = Null
    //     0xbfc800: mov             x1, NULL
    // 0xbfc804: r2 = 6
    //     0xbfc804: movz            x2, #0x6
    // 0xbfc808: r0 = AllocateArray()
    //     0xbfc808: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfc80c: mov             x2, x0
    // 0xbfc810: ldur            x0, [fp, #-0x30]
    // 0xbfc814: stur            x2, [fp, #-0x28]
    // 0xbfc818: StoreField: r2->field_f = r0
    //     0xbfc818: stur            w0, [x2, #0xf]
    // 0xbfc81c: r16 = Instance_SizedBox
    //     0xbfc81c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xbfc820: ldr             x16, [x16, #0x328]
    // 0xbfc824: StoreField: r2->field_13 = r16
    //     0xbfc824: stur            w16, [x2, #0x13]
    // 0xbfc828: ldur            x0, [fp, #-0x40]
    // 0xbfc82c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbfc82c: stur            w0, [x2, #0x17]
    // 0xbfc830: r1 = <Widget>
    //     0xbfc830: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfc834: r0 = AllocateGrowableArray()
    //     0xbfc834: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfc838: mov             x1, x0
    // 0xbfc83c: ldur            x0, [fp, #-0x28]
    // 0xbfc840: stur            x1, [fp, #-0x30]
    // 0xbfc844: StoreField: r1->field_f = r0
    //     0xbfc844: stur            w0, [x1, #0xf]
    // 0xbfc848: r0 = 6
    //     0xbfc848: movz            x0, #0x6
    // 0xbfc84c: StoreField: r1->field_b = r0
    //     0xbfc84c: stur            w0, [x1, #0xb]
    // 0xbfc850: r0 = Column()
    //     0xbfc850: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbfc854: mov             x3, x0
    // 0xbfc858: r0 = Instance_Axis
    //     0xbfc858: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbfc85c: stur            x3, [fp, #-0x28]
    // 0xbfc860: StoreField: r3->field_f = r0
    //     0xbfc860: stur            w0, [x3, #0xf]
    // 0xbfc864: r4 = Instance_MainAxisAlignment
    //     0xbfc864: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfc868: ldr             x4, [x4, #0xa08]
    // 0xbfc86c: StoreField: r3->field_13 = r4
    //     0xbfc86c: stur            w4, [x3, #0x13]
    // 0xbfc870: r5 = Instance_MainAxisSize
    //     0xbfc870: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfc874: ldr             x5, [x5, #0xa10]
    // 0xbfc878: ArrayStore: r3[0] = r5  ; List_4
    //     0xbfc878: stur            w5, [x3, #0x17]
    // 0xbfc87c: r6 = Instance_CrossAxisAlignment
    //     0xbfc87c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbfc880: ldr             x6, [x6, #0xa18]
    // 0xbfc884: StoreField: r3->field_1b = r6
    //     0xbfc884: stur            w6, [x3, #0x1b]
    // 0xbfc888: r7 = Instance_VerticalDirection
    //     0xbfc888: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfc88c: ldr             x7, [x7, #0xa20]
    // 0xbfc890: StoreField: r3->field_23 = r7
    //     0xbfc890: stur            w7, [x3, #0x23]
    // 0xbfc894: r8 = Instance_Clip
    //     0xbfc894: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfc898: ldr             x8, [x8, #0x38]
    // 0xbfc89c: StoreField: r3->field_2b = r8
    //     0xbfc89c: stur            w8, [x3, #0x2b]
    // 0xbfc8a0: StoreField: r3->field_2f = rZR
    //     0xbfc8a0: stur            xzr, [x3, #0x2f]
    // 0xbfc8a4: ldur            x1, [fp, #-0x30]
    // 0xbfc8a8: StoreField: r3->field_b = r1
    //     0xbfc8a8: stur            w1, [x3, #0xb]
    // 0xbfc8ac: r1 = Null
    //     0xbfc8ac: mov             x1, NULL
    // 0xbfc8b0: r2 = 4
    //     0xbfc8b0: movz            x2, #0x4
    // 0xbfc8b4: r0 = AllocateArray()
    //     0xbfc8b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfc8b8: mov             x2, x0
    // 0xbfc8bc: ldur            x0, [fp, #-0x20]
    // 0xbfc8c0: stur            x2, [fp, #-0x30]
    // 0xbfc8c4: StoreField: r2->field_f = r0
    //     0xbfc8c4: stur            w0, [x2, #0xf]
    // 0xbfc8c8: ldur            x0, [fp, #-0x28]
    // 0xbfc8cc: StoreField: r2->field_13 = r0
    //     0xbfc8cc: stur            w0, [x2, #0x13]
    // 0xbfc8d0: r1 = <Widget>
    //     0xbfc8d0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfc8d4: r0 = AllocateGrowableArray()
    //     0xbfc8d4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfc8d8: mov             x1, x0
    // 0xbfc8dc: ldur            x0, [fp, #-0x30]
    // 0xbfc8e0: stur            x1, [fp, #-0x20]
    // 0xbfc8e4: StoreField: r1->field_f = r0
    //     0xbfc8e4: stur            w0, [x1, #0xf]
    // 0xbfc8e8: r0 = 4
    //     0xbfc8e8: movz            x0, #0x4
    // 0xbfc8ec: StoreField: r1->field_b = r0
    //     0xbfc8ec: stur            w0, [x1, #0xb]
    // 0xbfc8f0: r0 = Row()
    //     0xbfc8f0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbfc8f4: mov             x3, x0
    // 0xbfc8f8: r0 = Instance_Axis
    //     0xbfc8f8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbfc8fc: stur            x3, [fp, #-0x28]
    // 0xbfc900: StoreField: r3->field_f = r0
    //     0xbfc900: stur            w0, [x3, #0xf]
    // 0xbfc904: r0 = Instance_MainAxisAlignment
    //     0xbfc904: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xbfc908: ldr             x0, [x0, #0xd10]
    // 0xbfc90c: StoreField: r3->field_13 = r0
    //     0xbfc90c: stur            w0, [x3, #0x13]
    // 0xbfc910: r0 = Instance_MainAxisSize
    //     0xbfc910: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfc914: ldr             x0, [x0, #0xa10]
    // 0xbfc918: ArrayStore: r3[0] = r0  ; List_4
    //     0xbfc918: stur            w0, [x3, #0x17]
    // 0xbfc91c: r1 = Instance_CrossAxisAlignment
    //     0xbfc91c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbfc920: ldr             x1, [x1, #0xa18]
    // 0xbfc924: StoreField: r3->field_1b = r1
    //     0xbfc924: stur            w1, [x3, #0x1b]
    // 0xbfc928: r4 = Instance_VerticalDirection
    //     0xbfc928: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfc92c: ldr             x4, [x4, #0xa20]
    // 0xbfc930: StoreField: r3->field_23 = r4
    //     0xbfc930: stur            w4, [x3, #0x23]
    // 0xbfc934: r5 = Instance_Clip
    //     0xbfc934: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfc938: ldr             x5, [x5, #0x38]
    // 0xbfc93c: StoreField: r3->field_2b = r5
    //     0xbfc93c: stur            w5, [x3, #0x2b]
    // 0xbfc940: StoreField: r3->field_2f = rZR
    //     0xbfc940: stur            xzr, [x3, #0x2f]
    // 0xbfc944: ldur            x1, [fp, #-0x20]
    // 0xbfc948: StoreField: r3->field_b = r1
    //     0xbfc948: stur            w1, [x3, #0xb]
    // 0xbfc94c: r1 = <Widget>
    //     0xbfc94c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfc950: r2 = 26
    //     0xbfc950: movz            x2, #0x1a
    // 0xbfc954: r0 = AllocateArray()
    //     0xbfc954: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfc958: mov             x1, x0
    // 0xbfc95c: ldur            x0, [fp, #-0x28]
    // 0xbfc960: stur            x1, [fp, #-0x20]
    // 0xbfc964: StoreField: r1->field_f = r0
    //     0xbfc964: stur            w0, [x1, #0xf]
    // 0xbfc968: r16 = Instance_SizedBox
    //     0xbfc968: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xbfc96c: ldr             x16, [x16, #0x578]
    // 0xbfc970: StoreField: r1->field_13 = r16
    //     0xbfc970: stur            w16, [x1, #0x13]
    // 0xbfc974: ldur            x0, [fp, #-8]
    // 0xbfc978: LoadField: r2 = r0->field_b
    //     0xbfc978: ldur            w2, [x0, #0xb]
    // 0xbfc97c: DecompressPointer r2
    //     0xbfc97c: add             x2, x2, HEAP, lsl #32
    // 0xbfc980: cmp             w2, NULL
    // 0xbfc984: b.eq            #0xbfd104
    // 0xbfc988: LoadField: r3 = r2->field_b
    //     0xbfc988: ldur            w3, [x2, #0xb]
    // 0xbfc98c: DecompressPointer r3
    //     0xbfc98c: add             x3, x3, HEAP, lsl #32
    // 0xbfc990: cmp             w3, NULL
    // 0xbfc994: b.ne            #0xbfc9a0
    // 0xbfc998: r2 = Null
    //     0xbfc998: mov             x2, NULL
    // 0xbfc99c: b               #0xbfc9c8
    // 0xbfc9a0: LoadField: r2 = r3->field_b
    //     0xbfc9a0: ldur            w2, [x3, #0xb]
    // 0xbfc9a4: DecompressPointer r2
    //     0xbfc9a4: add             x2, x2, HEAP, lsl #32
    // 0xbfc9a8: cmp             w2, NULL
    // 0xbfc9ac: b.ne            #0xbfc9b8
    // 0xbfc9b0: r2 = Null
    //     0xbfc9b0: mov             x2, NULL
    // 0xbfc9b4: b               #0xbfc9c8
    // 0xbfc9b8: LoadField: r3 = r2->field_b
    //     0xbfc9b8: ldur            w3, [x2, #0xb]
    // 0xbfc9bc: DecompressPointer r3
    //     0xbfc9bc: add             x3, x3, HEAP, lsl #32
    // 0xbfc9c0: LoadField: r2 = r3->field_b
    //     0xbfc9c0: ldur            w2, [x3, #0xb]
    // 0xbfc9c4: DecompressPointer r2
    //     0xbfc9c4: add             x2, x2, HEAP, lsl #32
    // 0xbfc9c8: str             x2, [SP]
    // 0xbfc9cc: r0 = _interpolateSingle()
    //     0xbfc9cc: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbfc9d0: ldur            x1, [fp, #-0x10]
    // 0xbfc9d4: stur            x0, [fp, #-0x28]
    // 0xbfc9d8: r0 = of()
    //     0xbfc9d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfc9dc: LoadField: r1 = r0->field_87
    //     0xbfc9dc: ldur            w1, [x0, #0x87]
    // 0xbfc9e0: DecompressPointer r1
    //     0xbfc9e0: add             x1, x1, HEAP, lsl #32
    // 0xbfc9e4: LoadField: r0 = r1->field_7
    //     0xbfc9e4: ldur            w0, [x1, #7]
    // 0xbfc9e8: DecompressPointer r0
    //     0xbfc9e8: add             x0, x0, HEAP, lsl #32
    // 0xbfc9ec: r16 = Instance_Color
    //     0xbfc9ec: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfc9f0: r30 = 16.000000
    //     0xbfc9f0: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbfc9f4: ldr             lr, [lr, #0x188]
    // 0xbfc9f8: stp             lr, x16, [SP]
    // 0xbfc9fc: mov             x1, x0
    // 0xbfca00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbfca00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbfca04: ldr             x4, [x4, #0x9b8]
    // 0xbfca08: r0 = copyWith()
    //     0xbfca08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfca0c: stur            x0, [fp, #-0x30]
    // 0xbfca10: r0 = Text()
    //     0xbfca10: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfca14: mov             x1, x0
    // 0xbfca18: ldur            x0, [fp, #-0x28]
    // 0xbfca1c: stur            x1, [fp, #-0x38]
    // 0xbfca20: StoreField: r1->field_b = r0
    //     0xbfca20: stur            w0, [x1, #0xb]
    // 0xbfca24: ldur            x0, [fp, #-0x30]
    // 0xbfca28: StoreField: r1->field_13 = r0
    //     0xbfca28: stur            w0, [x1, #0x13]
    // 0xbfca2c: r0 = SizedBox()
    //     0xbfca2c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbfca30: r3 = inf
    //     0xbfca30: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbfca34: ldr             x3, [x3, #0x9f8]
    // 0xbfca38: StoreField: r0->field_f = r3
    //     0xbfca38: stur            w3, [x0, #0xf]
    // 0xbfca3c: ldur            x1, [fp, #-0x38]
    // 0xbfca40: StoreField: r0->field_b = r1
    //     0xbfca40: stur            w1, [x0, #0xb]
    // 0xbfca44: ldur            x1, [fp, #-0x20]
    // 0xbfca48: ArrayStore: r1[2] = r0  ; List_4
    //     0xbfca48: add             x25, x1, #0x17
    //     0xbfca4c: str             w0, [x25]
    //     0xbfca50: tbz             w0, #0, #0xbfca6c
    //     0xbfca54: ldurb           w16, [x1, #-1]
    //     0xbfca58: ldurb           w17, [x0, #-1]
    //     0xbfca5c: and             x16, x17, x16, lsr #2
    //     0xbfca60: tst             x16, HEAP, lsr #32
    //     0xbfca64: b.eq            #0xbfca6c
    //     0xbfca68: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbfca6c: ldur            x0, [fp, #-0x20]
    // 0xbfca70: r16 = Instance_SizedBox
    //     0xbfca70: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xbfca74: ldr             x16, [x16, #0x8f0]
    // 0xbfca78: StoreField: r0->field_1b = r16
    //     0xbfca78: stur            w16, [x0, #0x1b]
    // 0xbfca7c: ldur            x4, [fp, #-8]
    // 0xbfca80: LoadField: r1 = r4->field_b
    //     0xbfca80: ldur            w1, [x4, #0xb]
    // 0xbfca84: DecompressPointer r1
    //     0xbfca84: add             x1, x1, HEAP, lsl #32
    // 0xbfca88: cmp             w1, NULL
    // 0xbfca8c: b.eq            #0xbfd108
    // 0xbfca90: LoadField: r2 = r1->field_b
    //     0xbfca90: ldur            w2, [x1, #0xb]
    // 0xbfca94: DecompressPointer r2
    //     0xbfca94: add             x2, x2, HEAP, lsl #32
    // 0xbfca98: cmp             w2, NULL
    // 0xbfca9c: b.ne            #0xbfcaa8
    // 0xbfcaa0: r1 = Null
    //     0xbfcaa0: mov             x1, NULL
    // 0xbfcaa4: b               #0xbfcad8
    // 0xbfcaa8: LoadField: r1 = r2->field_b
    //     0xbfcaa8: ldur            w1, [x2, #0xb]
    // 0xbfcaac: DecompressPointer r1
    //     0xbfcaac: add             x1, x1, HEAP, lsl #32
    // 0xbfcab0: cmp             w1, NULL
    // 0xbfcab4: b.ne            #0xbfcac0
    // 0xbfcab8: r1 = Null
    //     0xbfcab8: mov             x1, NULL
    // 0xbfcabc: b               #0xbfcad8
    // 0xbfcac0: LoadField: r2 = r1->field_b
    //     0xbfcac0: ldur            w2, [x1, #0xb]
    // 0xbfcac4: DecompressPointer r2
    //     0xbfcac4: add             x2, x2, HEAP, lsl #32
    // 0xbfcac8: LoadField: r1 = r2->field_7
    //     0xbfcac8: ldur            w1, [x2, #7]
    // 0xbfcacc: DecompressPointer r1
    //     0xbfcacc: add             x1, x1, HEAP, lsl #32
    // 0xbfcad0: LoadField: r2 = r1->field_b
    //     0xbfcad0: ldur            w2, [x1, #0xb]
    // 0xbfcad4: mov             x1, x2
    // 0xbfcad8: cmp             w1, NULL
    // 0xbfcadc: b.ne            #0xbfcae8
    // 0xbfcae0: r1 = 0
    //     0xbfcae0: movz            x1, #0
    // 0xbfcae4: b               #0xbfcaf0
    // 0xbfcae8: r2 = LoadInt32Instr(r1)
    //     0xbfcae8: sbfx            x2, x1, #1, #0x1f
    // 0xbfcaec: mov             x1, x2
    // 0xbfcaf0: lsl             x5, x1, #1
    // 0xbfcaf4: ldur            x2, [fp, #-0x18]
    // 0xbfcaf8: stur            x5, [fp, #-0x28]
    // 0xbfcafc: r1 = Function '<anonymous closure>':.
    //     0xbfcafc: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c0f8] AnonymousClosure: (0xbfd45c), in [package:customer_app/app/presentation/views/line/post_order/order_detail/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xbfc304)
    //     0xbfcb00: ldr             x1, [x1, #0xf8]
    // 0xbfcb04: r0 = AllocateClosure()
    //     0xbfcb04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfcb08: stur            x0, [fp, #-0x30]
    // 0xbfcb0c: r0 = ListView()
    //     0xbfcb0c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbfcb10: stur            x0, [fp, #-0x38]
    // 0xbfcb14: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbfcb14: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbfcb18: ldr             x16, [x16, #0x1c8]
    // 0xbfcb1c: r30 = true
    //     0xbfcb1c: add             lr, NULL, #0x20  ; true
    // 0xbfcb20: stp             lr, x16, [SP]
    // 0xbfcb24: mov             x1, x0
    // 0xbfcb28: ldur            x2, [fp, #-0x30]
    // 0xbfcb2c: ldur            x3, [fp, #-0x28]
    // 0xbfcb30: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xbfcb30: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xbfcb34: ldr             x4, [x4, #0xd18]
    // 0xbfcb38: r0 = ListView.builder()
    //     0xbfcb38: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbfcb3c: ldur            x1, [fp, #-0x20]
    // 0xbfcb40: ldur            x0, [fp, #-0x38]
    // 0xbfcb44: ArrayStore: r1[4] = r0  ; List_4
    //     0xbfcb44: add             x25, x1, #0x1f
    //     0xbfcb48: str             w0, [x25]
    //     0xbfcb4c: tbz             w0, #0, #0xbfcb68
    //     0xbfcb50: ldurb           w16, [x1, #-1]
    //     0xbfcb54: ldurb           w17, [x0, #-1]
    //     0xbfcb58: and             x16, x17, x16, lsr #2
    //     0xbfcb5c: tst             x16, HEAP, lsr #32
    //     0xbfcb60: b.eq            #0xbfcb68
    //     0xbfcb64: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbfcb68: ldur            x1, [fp, #-0x20]
    // 0xbfcb6c: r16 = Instance_SizedBox
    //     0xbfcb6c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xbfcb70: ldr             x16, [x16, #0x578]
    // 0xbfcb74: StoreField: r1->field_23 = r16
    //     0xbfcb74: stur            w16, [x1, #0x23]
    // 0xbfcb78: ldur            x0, [fp, #-8]
    // 0xbfcb7c: LoadField: r2 = r0->field_b
    //     0xbfcb7c: ldur            w2, [x0, #0xb]
    // 0xbfcb80: DecompressPointer r2
    //     0xbfcb80: add             x2, x2, HEAP, lsl #32
    // 0xbfcb84: cmp             w2, NULL
    // 0xbfcb88: b.eq            #0xbfd10c
    // 0xbfcb8c: LoadField: r3 = r2->field_b
    //     0xbfcb8c: ldur            w3, [x2, #0xb]
    // 0xbfcb90: DecompressPointer r3
    //     0xbfcb90: add             x3, x3, HEAP, lsl #32
    // 0xbfcb94: cmp             w3, NULL
    // 0xbfcb98: b.ne            #0xbfcba4
    // 0xbfcb9c: r2 = Null
    //     0xbfcb9c: mov             x2, NULL
    // 0xbfcba0: b               #0xbfcbcc
    // 0xbfcba4: LoadField: r2 = r3->field_b
    //     0xbfcba4: ldur            w2, [x3, #0xb]
    // 0xbfcba8: DecompressPointer r2
    //     0xbfcba8: add             x2, x2, HEAP, lsl #32
    // 0xbfcbac: cmp             w2, NULL
    // 0xbfcbb0: b.ne            #0xbfcbbc
    // 0xbfcbb4: r2 = Null
    //     0xbfcbb4: mov             x2, NULL
    // 0xbfcbb8: b               #0xbfcbcc
    // 0xbfcbbc: LoadField: r3 = r2->field_f
    //     0xbfcbbc: ldur            w3, [x2, #0xf]
    // 0xbfcbc0: DecompressPointer r3
    //     0xbfcbc0: add             x3, x3, HEAP, lsl #32
    // 0xbfcbc4: LoadField: r2 = r3->field_7
    //     0xbfcbc4: ldur            w2, [x3, #7]
    // 0xbfcbc8: DecompressPointer r2
    //     0xbfcbc8: add             x2, x2, HEAP, lsl #32
    // 0xbfcbcc: str             x2, [SP]
    // 0xbfcbd0: r0 = _interpolateSingle()
    //     0xbfcbd0: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbfcbd4: ldur            x1, [fp, #-0x10]
    // 0xbfcbd8: stur            x0, [fp, #-0x28]
    // 0xbfcbdc: r0 = of()
    //     0xbfcbdc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfcbe0: LoadField: r1 = r0->field_87
    //     0xbfcbe0: ldur            w1, [x0, #0x87]
    // 0xbfcbe4: DecompressPointer r1
    //     0xbfcbe4: add             x1, x1, HEAP, lsl #32
    // 0xbfcbe8: LoadField: r0 = r1->field_7
    //     0xbfcbe8: ldur            w0, [x1, #7]
    // 0xbfcbec: DecompressPointer r0
    //     0xbfcbec: add             x0, x0, HEAP, lsl #32
    // 0xbfcbf0: r16 = Instance_Color
    //     0xbfcbf0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfcbf4: r30 = 16.000000
    //     0xbfcbf4: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbfcbf8: ldr             lr, [lr, #0x188]
    // 0xbfcbfc: stp             lr, x16, [SP]
    // 0xbfcc00: mov             x1, x0
    // 0xbfcc04: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbfcc04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbfcc08: ldr             x4, [x4, #0x9b8]
    // 0xbfcc0c: r0 = copyWith()
    //     0xbfcc0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfcc10: stur            x0, [fp, #-0x30]
    // 0xbfcc14: r0 = Text()
    //     0xbfcc14: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfcc18: mov             x1, x0
    // 0xbfcc1c: ldur            x0, [fp, #-0x28]
    // 0xbfcc20: StoreField: r1->field_b = r0
    //     0xbfcc20: stur            w0, [x1, #0xb]
    // 0xbfcc24: ldur            x0, [fp, #-0x30]
    // 0xbfcc28: StoreField: r1->field_13 = r0
    //     0xbfcc28: stur            w0, [x1, #0x13]
    // 0xbfcc2c: mov             x0, x1
    // 0xbfcc30: ldur            x1, [fp, #-0x20]
    // 0xbfcc34: ArrayStore: r1[6] = r0  ; List_4
    //     0xbfcc34: add             x25, x1, #0x27
    //     0xbfcc38: str             w0, [x25]
    //     0xbfcc3c: tbz             w0, #0, #0xbfcc58
    //     0xbfcc40: ldurb           w16, [x1, #-1]
    //     0xbfcc44: ldurb           w17, [x0, #-1]
    //     0xbfcc48: and             x16, x17, x16, lsr #2
    //     0xbfcc4c: tst             x16, HEAP, lsr #32
    //     0xbfcc50: b.eq            #0xbfcc58
    //     0xbfcc54: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbfcc58: ldur            x0, [fp, #-0x20]
    // 0xbfcc5c: r16 = Instance_SizedBox
    //     0xbfcc5c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xbfcc60: ldr             x16, [x16, #0x8b8]
    // 0xbfcc64: StoreField: r0->field_2b = r16
    //     0xbfcc64: stur            w16, [x0, #0x2b]
    // 0xbfcc68: r16 = 0.000000
    //     0xbfcc68: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xbfcc6c: r30 = Instance_ConnectorThemeData
    //     0xbfcc6c: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3fe88] Obj!ConnectorThemeData@d5bb71
    //     0xbfcc70: ldr             lr, [lr, #0xe88]
    // 0xbfcc74: stp             lr, x16, [SP]
    // 0xbfcc78: r1 = Null
    //     0xbfcc78: mov             x1, NULL
    // 0xbfcc7c: r4 = const [0, 0x3, 0x2, 0x1, connectorTheme, 0x2, nodePosition, 0x1, null]
    //     0xbfcc7c: add             x4, PP, #0x36, lsl #12  ; [pp+0x365b8] List(9) [0, 0x3, 0x2, 0x1, "connectorTheme", 0x2, "nodePosition", 0x1, Null]
    //     0xbfcc80: ldr             x4, [x4, #0x5b8]
    // 0xbfcc84: r0 = TimelineThemeData()
    //     0xbfcc84: bl              #0x9dffe8  ; [package:timelines_plus/src/timeline_theme.dart] TimelineThemeData::TimelineThemeData
    // 0xbfcc88: mov             x3, x0
    // 0xbfcc8c: ldur            x0, [fp, #-8]
    // 0xbfcc90: stur            x3, [fp, #-0x28]
    // 0xbfcc94: LoadField: r1 = r0->field_b
    //     0xbfcc94: ldur            w1, [x0, #0xb]
    // 0xbfcc98: DecompressPointer r1
    //     0xbfcc98: add             x1, x1, HEAP, lsl #32
    // 0xbfcc9c: cmp             w1, NULL
    // 0xbfcca0: b.eq            #0xbfd110
    // 0xbfcca4: LoadField: r0 = r1->field_b
    //     0xbfcca4: ldur            w0, [x1, #0xb]
    // 0xbfcca8: DecompressPointer r0
    //     0xbfcca8: add             x0, x0, HEAP, lsl #32
    // 0xbfccac: cmp             w0, NULL
    // 0xbfccb0: b.ne            #0xbfccbc
    // 0xbfccb4: r0 = Null
    //     0xbfccb4: mov             x0, NULL
    // 0xbfccb8: b               #0xbfcce8
    // 0xbfccbc: LoadField: r1 = r0->field_b
    //     0xbfccbc: ldur            w1, [x0, #0xb]
    // 0xbfccc0: DecompressPointer r1
    //     0xbfccc0: add             x1, x1, HEAP, lsl #32
    // 0xbfccc4: cmp             w1, NULL
    // 0xbfccc8: b.ne            #0xbfccd4
    // 0xbfcccc: r0 = Null
    //     0xbfcccc: mov             x0, NULL
    // 0xbfccd0: b               #0xbfcce8
    // 0xbfccd4: LoadField: r0 = r1->field_f
    //     0xbfccd4: ldur            w0, [x1, #0xf]
    // 0xbfccd8: DecompressPointer r0
    //     0xbfccd8: add             x0, x0, HEAP, lsl #32
    // 0xbfccdc: LoadField: r1 = r0->field_b
    //     0xbfccdc: ldur            w1, [x0, #0xb]
    // 0xbfcce0: DecompressPointer r1
    //     0xbfcce0: add             x1, x1, HEAP, lsl #32
    // 0xbfcce4: LoadField: r0 = r1->field_b
    //     0xbfcce4: ldur            w0, [x1, #0xb]
    // 0xbfcce8: cmp             w0, NULL
    // 0xbfccec: b.ne            #0xbfccf8
    // 0xbfccf0: r6 = 0
    //     0xbfccf0: movz            x6, #0
    // 0xbfccf4: b               #0xbfcd00
    // 0xbfccf8: r1 = LoadInt32Instr(r0)
    //     0xbfccf8: sbfx            x1, x0, #1, #0x1f
    // 0xbfccfc: mov             x6, x1
    // 0xbfcd00: ldur            x0, [fp, #-0x20]
    // 0xbfcd04: ldur            x2, [fp, #-0x18]
    // 0xbfcd08: stur            x6, [fp, #-0x48]
    // 0xbfcd0c: r1 = Function '<anonymous closure>':.
    //     0xbfcd0c: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c100] AnonymousClosure: (0xbfd114), in [package:customer_app/app/presentation/views/line/post_order/order_detail/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xbfc304)
    //     0xbfcd10: ldr             x1, [x1, #0x100]
    // 0xbfcd14: r0 = AllocateClosure()
    //     0xbfcd14: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfcd18: r1 = Function '<anonymous closure>':.
    //     0xbfcd18: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c108] AnonymousClosure: (0xa79710), in [package:customer_app/app/presentation/views/line/product_detail/widgets/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xbff404)
    //     0xbfcd1c: ldr             x1, [x1, #0x108]
    // 0xbfcd20: r2 = Null
    //     0xbfcd20: mov             x2, NULL
    // 0xbfcd24: stur            x0, [fp, #-8]
    // 0xbfcd28: r0 = AllocateClosure()
    //     0xbfcd28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfcd2c: r1 = Function '<anonymous closure>':.
    //     0xbfcd2c: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c110] AnonymousClosure: (0xa796c4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xbff404)
    //     0xbfcd30: ldr             x1, [x1, #0x110]
    // 0xbfcd34: r2 = Null
    //     0xbfcd34: mov             x2, NULL
    // 0xbfcd38: stur            x0, [fp, #-0x18]
    // 0xbfcd3c: r0 = AllocateClosure()
    //     0xbfcd3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfcd40: mov             x2, x0
    // 0xbfcd44: ldur            x3, [fp, #-8]
    // 0xbfcd48: ldur            x5, [fp, #-0x18]
    // 0xbfcd4c: ldur            x6, [fp, #-0x48]
    // 0xbfcd50: r1 = Null
    //     0xbfcd50: mov             x1, NULL
    // 0xbfcd54: r0 = TimelineTileBuilder.connected()
    //     0xbfcd54: bl              #0x9dfab0  ; [package:timelines_plus/src/timeline_tile_builder.dart] TimelineTileBuilder::TimelineTileBuilder.connected
    // 0xbfcd58: mov             x2, x0
    // 0xbfcd5c: ldur            x3, [fp, #-0x28]
    // 0xbfcd60: r1 = Null
    //     0xbfcd60: mov             x1, NULL
    // 0xbfcd64: r0 = Timeline.tileBuilder()
    //     0xbfcd64: bl              #0x9df894  ; [package:timelines_plus/src/timelines.dart] Timeline::Timeline.tileBuilder
    // 0xbfcd68: ldur            x1, [fp, #-0x20]
    // 0xbfcd6c: ArrayStore: r1[8] = r0  ; List_4
    //     0xbfcd6c: add             x25, x1, #0x2f
    //     0xbfcd70: str             w0, [x25]
    //     0xbfcd74: tbz             w0, #0, #0xbfcd90
    //     0xbfcd78: ldurb           w16, [x1, #-1]
    //     0xbfcd7c: ldurb           w17, [x0, #-1]
    //     0xbfcd80: and             x16, x17, x16, lsr #2
    //     0xbfcd84: tst             x16, HEAP, lsr #32
    //     0xbfcd88: b.eq            #0xbfcd90
    //     0xbfcd8c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbfcd90: ldur            x0, [fp, #-0x20]
    // 0xbfcd94: r16 = Instance_SizedBox
    //     0xbfcd94: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xbfcd98: ldr             x16, [x16, #0x328]
    // 0xbfcd9c: StoreField: r0->field_33 = r16
    //     0xbfcd9c: stur            w16, [x0, #0x33]
    // 0xbfcda0: r1 = Instance_Color
    //     0xbfcda0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfcda4: d0 = 0.100000
    //     0xbfcda4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbfcda8: r0 = withOpacity()
    //     0xbfcda8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbfcdac: stur            x0, [fp, #-8]
    // 0xbfcdb0: r0 = Divider()
    //     0xbfcdb0: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xbfcdb4: mov             x1, x0
    // 0xbfcdb8: ldur            x0, [fp, #-8]
    // 0xbfcdbc: StoreField: r1->field_1f = r0
    //     0xbfcdbc: stur            w0, [x1, #0x1f]
    // 0xbfcdc0: mov             x0, x1
    // 0xbfcdc4: ldur            x1, [fp, #-0x20]
    // 0xbfcdc8: ArrayStore: r1[10] = r0  ; List_4
    //     0xbfcdc8: add             x25, x1, #0x37
    //     0xbfcdcc: str             w0, [x25]
    //     0xbfcdd0: tbz             w0, #0, #0xbfcdec
    //     0xbfcdd4: ldurb           w16, [x1, #-1]
    //     0xbfcdd8: ldurb           w17, [x0, #-1]
    //     0xbfcddc: and             x16, x17, x16, lsr #2
    //     0xbfcde0: tst             x16, HEAP, lsr #32
    //     0xbfcde4: b.eq            #0xbfcdec
    //     0xbfcde8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbfcdec: ldur            x1, [fp, #-0x20]
    // 0xbfcdf0: r16 = Instance_SizedBox
    //     0xbfcdf0: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xbfcdf4: ldr             x16, [x16, #0x328]
    // 0xbfcdf8: StoreField: r1->field_3b = r16
    //     0xbfcdf8: stur            w16, [x1, #0x3b]
    // 0xbfcdfc: r16 = <EdgeInsets>
    //     0xbfcdfc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbfce00: ldr             x16, [x16, #0xda0]
    // 0xbfce04: r30 = Instance_EdgeInsets
    //     0xbfce04: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbfce08: ldr             lr, [lr, #0x1f0]
    // 0xbfce0c: stp             lr, x16, [SP]
    // 0xbfce10: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbfce10: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbfce14: r0 = all()
    //     0xbfce14: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbfce18: ldur            x1, [fp, #-0x10]
    // 0xbfce1c: stur            x0, [fp, #-8]
    // 0xbfce20: r0 = of()
    //     0xbfce20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfce24: LoadField: r1 = r0->field_5b
    //     0xbfce24: ldur            w1, [x0, #0x5b]
    // 0xbfce28: DecompressPointer r1
    //     0xbfce28: add             x1, x1, HEAP, lsl #32
    // 0xbfce2c: r16 = <Color>
    //     0xbfce2c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbfce30: ldr             x16, [x16, #0xf80]
    // 0xbfce34: stp             x1, x16, [SP]
    // 0xbfce38: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbfce38: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbfce3c: r0 = all()
    //     0xbfce3c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbfce40: ldur            x1, [fp, #-0x10]
    // 0xbfce44: stur            x0, [fp, #-0x18]
    // 0xbfce48: r0 = of()
    //     0xbfce48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfce4c: LoadField: r1 = r0->field_5b
    //     0xbfce4c: ldur            w1, [x0, #0x5b]
    // 0xbfce50: DecompressPointer r1
    //     0xbfce50: add             x1, x1, HEAP, lsl #32
    // 0xbfce54: r16 = <Color>
    //     0xbfce54: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbfce58: ldr             x16, [x16, #0xf80]
    // 0xbfce5c: stp             x1, x16, [SP]
    // 0xbfce60: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbfce60: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbfce64: r0 = all()
    //     0xbfce64: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbfce68: ldur            x1, [fp, #-0x10]
    // 0xbfce6c: stur            x0, [fp, #-0x28]
    // 0xbfce70: r0 = of()
    //     0xbfce70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfce74: LoadField: r1 = r0->field_5b
    //     0xbfce74: ldur            w1, [x0, #0x5b]
    // 0xbfce78: DecompressPointer r1
    //     0xbfce78: add             x1, x1, HEAP, lsl #32
    // 0xbfce7c: stur            x1, [fp, #-0x30]
    // 0xbfce80: r0 = BorderSide()
    //     0xbfce80: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbfce84: mov             x1, x0
    // 0xbfce88: ldur            x0, [fp, #-0x30]
    // 0xbfce8c: stur            x1, [fp, #-0x38]
    // 0xbfce90: StoreField: r1->field_7 = r0
    //     0xbfce90: stur            w0, [x1, #7]
    // 0xbfce94: d0 = 1.000000
    //     0xbfce94: fmov            d0, #1.00000000
    // 0xbfce98: StoreField: r1->field_b = d0
    //     0xbfce98: stur            d0, [x1, #0xb]
    // 0xbfce9c: r0 = Instance_BorderStyle
    //     0xbfce9c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbfcea0: ldr             x0, [x0, #0xf68]
    // 0xbfcea4: StoreField: r1->field_13 = r0
    //     0xbfcea4: stur            w0, [x1, #0x13]
    // 0xbfcea8: d0 = -1.000000
    //     0xbfcea8: fmov            d0, #-1.00000000
    // 0xbfceac: ArrayStore: r1[0] = d0  ; List_8
    //     0xbfceac: stur            d0, [x1, #0x17]
    // 0xbfceb0: r0 = RoundedRectangleBorder()
    //     0xbfceb0: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xbfceb4: mov             x1, x0
    // 0xbfceb8: r0 = Instance_BorderRadius
    //     0xbfceb8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbfcebc: ldr             x0, [x0, #0xf70]
    // 0xbfcec0: StoreField: r1->field_b = r0
    //     0xbfcec0: stur            w0, [x1, #0xb]
    // 0xbfcec4: ldur            x0, [fp, #-0x38]
    // 0xbfcec8: StoreField: r1->field_7 = r0
    //     0xbfcec8: stur            w0, [x1, #7]
    // 0xbfcecc: r16 = <RoundedRectangleBorder>
    //     0xbfcecc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbfced0: ldr             x16, [x16, #0xf78]
    // 0xbfced4: stp             x1, x16, [SP]
    // 0xbfced8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbfced8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbfcedc: r0 = all()
    //     0xbfcedc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbfcee0: stur            x0, [fp, #-0x30]
    // 0xbfcee4: r0 = ButtonStyle()
    //     0xbfcee4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbfcee8: mov             x1, x0
    // 0xbfceec: ldur            x0, [fp, #-0x28]
    // 0xbfcef0: stur            x1, [fp, #-0x38]
    // 0xbfcef4: StoreField: r1->field_b = r0
    //     0xbfcef4: stur            w0, [x1, #0xb]
    // 0xbfcef8: ldur            x0, [fp, #-0x18]
    // 0xbfcefc: StoreField: r1->field_f = r0
    //     0xbfcefc: stur            w0, [x1, #0xf]
    // 0xbfcf00: ldur            x0, [fp, #-8]
    // 0xbfcf04: StoreField: r1->field_23 = r0
    //     0xbfcf04: stur            w0, [x1, #0x23]
    // 0xbfcf08: ldur            x0, [fp, #-0x30]
    // 0xbfcf0c: StoreField: r1->field_43 = r0
    //     0xbfcf0c: stur            w0, [x1, #0x43]
    // 0xbfcf10: r0 = TextButtonThemeData()
    //     0xbfcf10: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbfcf14: mov             x2, x0
    // 0xbfcf18: ldur            x0, [fp, #-0x38]
    // 0xbfcf1c: stur            x2, [fp, #-8]
    // 0xbfcf20: StoreField: r2->field_7 = r0
    //     0xbfcf20: stur            w0, [x2, #7]
    // 0xbfcf24: ldur            x1, [fp, #-0x10]
    // 0xbfcf28: r0 = of()
    //     0xbfcf28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfcf2c: LoadField: r1 = r0->field_87
    //     0xbfcf2c: ldur            w1, [x0, #0x87]
    // 0xbfcf30: DecompressPointer r1
    //     0xbfcf30: add             x1, x1, HEAP, lsl #32
    // 0xbfcf34: LoadField: r0 = r1->field_7
    //     0xbfcf34: ldur            w0, [x1, #7]
    // 0xbfcf38: DecompressPointer r0
    //     0xbfcf38: add             x0, x0, HEAP, lsl #32
    // 0xbfcf3c: r16 = 16.000000
    //     0xbfcf3c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbfcf40: ldr             x16, [x16, #0x188]
    // 0xbfcf44: r30 = Instance_Color
    //     0xbfcf44: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbfcf48: stp             lr, x16, [SP]
    // 0xbfcf4c: mov             x1, x0
    // 0xbfcf50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbfcf50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbfcf54: ldr             x4, [x4, #0xaa0]
    // 0xbfcf58: r0 = copyWith()
    //     0xbfcf58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfcf5c: stur            x0, [fp, #-0x10]
    // 0xbfcf60: r0 = Text()
    //     0xbfcf60: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfcf64: mov             x3, x0
    // 0xbfcf68: r0 = "Okay, Go back"
    //     0xbfcf68: add             x0, PP, #0x5c, lsl #12  ; [pp+0x5c118] "Okay, Go back"
    //     0xbfcf6c: ldr             x0, [x0, #0x118]
    // 0xbfcf70: stur            x3, [fp, #-0x18]
    // 0xbfcf74: StoreField: r3->field_b = r0
    //     0xbfcf74: stur            w0, [x3, #0xb]
    // 0xbfcf78: ldur            x0, [fp, #-0x10]
    // 0xbfcf7c: StoreField: r3->field_13 = r0
    //     0xbfcf7c: stur            w0, [x3, #0x13]
    // 0xbfcf80: r1 = Function '<anonymous closure>':.
    //     0xbfcf80: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c120] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbfcf84: ldr             x1, [x1, #0x120]
    // 0xbfcf88: r2 = Null
    //     0xbfcf88: mov             x2, NULL
    // 0xbfcf8c: r0 = AllocateClosure()
    //     0xbfcf8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfcf90: stur            x0, [fp, #-0x10]
    // 0xbfcf94: r0 = TextButton()
    //     0xbfcf94: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbfcf98: mov             x1, x0
    // 0xbfcf9c: ldur            x0, [fp, #-0x10]
    // 0xbfcfa0: stur            x1, [fp, #-0x28]
    // 0xbfcfa4: StoreField: r1->field_b = r0
    //     0xbfcfa4: stur            w0, [x1, #0xb]
    // 0xbfcfa8: r0 = false
    //     0xbfcfa8: add             x0, NULL, #0x30  ; false
    // 0xbfcfac: StoreField: r1->field_27 = r0
    //     0xbfcfac: stur            w0, [x1, #0x27]
    // 0xbfcfb0: r2 = true
    //     0xbfcfb0: add             x2, NULL, #0x20  ; true
    // 0xbfcfb4: StoreField: r1->field_2f = r2
    //     0xbfcfb4: stur            w2, [x1, #0x2f]
    // 0xbfcfb8: ldur            x2, [fp, #-0x18]
    // 0xbfcfbc: StoreField: r1->field_37 = r2
    //     0xbfcfbc: stur            w2, [x1, #0x37]
    // 0xbfcfc0: r0 = TextButtonTheme()
    //     0xbfcfc0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbfcfc4: mov             x1, x0
    // 0xbfcfc8: ldur            x0, [fp, #-8]
    // 0xbfcfcc: stur            x1, [fp, #-0x10]
    // 0xbfcfd0: StoreField: r1->field_f = r0
    //     0xbfcfd0: stur            w0, [x1, #0xf]
    // 0xbfcfd4: ldur            x0, [fp, #-0x28]
    // 0xbfcfd8: StoreField: r1->field_b = r0
    //     0xbfcfd8: stur            w0, [x1, #0xb]
    // 0xbfcfdc: r0 = SizedBox()
    //     0xbfcfdc: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbfcfe0: mov             x1, x0
    // 0xbfcfe4: r0 = inf
    //     0xbfcfe4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbfcfe8: ldr             x0, [x0, #0x9f8]
    // 0xbfcfec: StoreField: r1->field_f = r0
    //     0xbfcfec: stur            w0, [x1, #0xf]
    // 0xbfcff0: ldur            x0, [fp, #-0x10]
    // 0xbfcff4: StoreField: r1->field_b = r0
    //     0xbfcff4: stur            w0, [x1, #0xb]
    // 0xbfcff8: mov             x0, x1
    // 0xbfcffc: ldur            x1, [fp, #-0x20]
    // 0xbfd000: ArrayStore: r1[12] = r0  ; List_4
    //     0xbfd000: add             x25, x1, #0x3f
    //     0xbfd004: str             w0, [x25]
    //     0xbfd008: tbz             w0, #0, #0xbfd024
    //     0xbfd00c: ldurb           w16, [x1, #-1]
    //     0xbfd010: ldurb           w17, [x0, #-1]
    //     0xbfd014: and             x16, x17, x16, lsr #2
    //     0xbfd018: tst             x16, HEAP, lsr #32
    //     0xbfd01c: b.eq            #0xbfd024
    //     0xbfd020: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbfd024: r1 = <Widget>
    //     0xbfd024: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfd028: r0 = AllocateGrowableArray()
    //     0xbfd028: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfd02c: mov             x1, x0
    // 0xbfd030: ldur            x0, [fp, #-0x20]
    // 0xbfd034: stur            x1, [fp, #-8]
    // 0xbfd038: StoreField: r1->field_f = r0
    //     0xbfd038: stur            w0, [x1, #0xf]
    // 0xbfd03c: r0 = 26
    //     0xbfd03c: movz            x0, #0x1a
    // 0xbfd040: StoreField: r1->field_b = r0
    //     0xbfd040: stur            w0, [x1, #0xb]
    // 0xbfd044: r0 = Column()
    //     0xbfd044: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbfd048: mov             x1, x0
    // 0xbfd04c: r0 = Instance_Axis
    //     0xbfd04c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbfd050: stur            x1, [fp, #-0x10]
    // 0xbfd054: StoreField: r1->field_f = r0
    //     0xbfd054: stur            w0, [x1, #0xf]
    // 0xbfd058: r2 = Instance_MainAxisAlignment
    //     0xbfd058: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfd05c: ldr             x2, [x2, #0xa08]
    // 0xbfd060: StoreField: r1->field_13 = r2
    //     0xbfd060: stur            w2, [x1, #0x13]
    // 0xbfd064: r2 = Instance_MainAxisSize
    //     0xbfd064: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfd068: ldr             x2, [x2, #0xa10]
    // 0xbfd06c: ArrayStore: r1[0] = r2  ; List_4
    //     0xbfd06c: stur            w2, [x1, #0x17]
    // 0xbfd070: r2 = Instance_CrossAxisAlignment
    //     0xbfd070: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbfd074: ldr             x2, [x2, #0x890]
    // 0xbfd078: StoreField: r1->field_1b = r2
    //     0xbfd078: stur            w2, [x1, #0x1b]
    // 0xbfd07c: r2 = Instance_VerticalDirection
    //     0xbfd07c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfd080: ldr             x2, [x2, #0xa20]
    // 0xbfd084: StoreField: r1->field_23 = r2
    //     0xbfd084: stur            w2, [x1, #0x23]
    // 0xbfd088: r2 = Instance_Clip
    //     0xbfd088: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfd08c: ldr             x2, [x2, #0x38]
    // 0xbfd090: StoreField: r1->field_2b = r2
    //     0xbfd090: stur            w2, [x1, #0x2b]
    // 0xbfd094: StoreField: r1->field_2f = rZR
    //     0xbfd094: stur            xzr, [x1, #0x2f]
    // 0xbfd098: ldur            x2, [fp, #-8]
    // 0xbfd09c: StoreField: r1->field_b = r2
    //     0xbfd09c: stur            w2, [x1, #0xb]
    // 0xbfd0a0: r0 = SingleChildScrollView()
    //     0xbfd0a0: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xbfd0a4: r1 = Instance_Axis
    //     0xbfd0a4: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbfd0a8: StoreField: r0->field_b = r1
    //     0xbfd0a8: stur            w1, [x0, #0xb]
    // 0xbfd0ac: r1 = false
    //     0xbfd0ac: add             x1, NULL, #0x30  ; false
    // 0xbfd0b0: StoreField: r0->field_f = r1
    //     0xbfd0b0: stur            w1, [x0, #0xf]
    // 0xbfd0b4: r1 = Instance_EdgeInsets
    //     0xbfd0b4: add             x1, PP, #0x55, lsl #12  ; [pp+0x558e8] Obj!EdgeInsets@d58fa1
    //     0xbfd0b8: ldr             x1, [x1, #0x8e8]
    // 0xbfd0bc: StoreField: r0->field_13 = r1
    //     0xbfd0bc: stur            w1, [x0, #0x13]
    // 0xbfd0c0: ldur            x1, [fp, #-0x10]
    // 0xbfd0c4: StoreField: r0->field_23 = r1
    //     0xbfd0c4: stur            w1, [x0, #0x23]
    // 0xbfd0c8: r1 = Instance_DragStartBehavior
    //     0xbfd0c8: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xbfd0cc: StoreField: r0->field_27 = r1
    //     0xbfd0cc: stur            w1, [x0, #0x27]
    // 0xbfd0d0: r1 = Instance_Clip
    //     0xbfd0d0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbfd0d4: ldr             x1, [x1, #0x7e0]
    // 0xbfd0d8: StoreField: r0->field_2b = r1
    //     0xbfd0d8: stur            w1, [x0, #0x2b]
    // 0xbfd0dc: r1 = Instance_HitTestBehavior
    //     0xbfd0dc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xbfd0e0: ldr             x1, [x1, #0x288]
    // 0xbfd0e4: StoreField: r0->field_2f = r1
    //     0xbfd0e4: stur            w1, [x0, #0x2f]
    // 0xbfd0e8: LeaveFrame
    //     0xbfd0e8: mov             SP, fp
    //     0xbfd0ec: ldp             fp, lr, [SP], #0x10
    // 0xbfd0f0: ret
    //     0xbfd0f0: ret             
    // 0xbfd0f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfd0f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfd0f8: b               #0xbfc32c
    // 0xbfd0fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfd0fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfd100: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfd100: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfd104: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfd104: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfd108: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfd108: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfd10c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfd10c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfd110: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfd110: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbfd114, size: 0x348
    // 0xbfd114: EnterFrame
    //     0xbfd114: stp             fp, lr, [SP, #-0x10]!
    //     0xbfd118: mov             fp, SP
    // 0xbfd11c: AllocStack(0x30)
    //     0xbfd11c: sub             SP, SP, #0x30
    // 0xbfd120: SetupParameters()
    //     0xbfd120: ldr             x0, [fp, #0x20]
    //     0xbfd124: ldur            w2, [x0, #0x17]
    //     0xbfd128: add             x2, x2, HEAP, lsl #32
    //     0xbfd12c: stur            x2, [fp, #-8]
    // 0xbfd130: CheckStackOverflow
    //     0xbfd130: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfd134: cmp             SP, x16
    //     0xbfd138: b.ls            #0xbfd444
    // 0xbfd13c: LoadField: r0 = r2->field_f
    //     0xbfd13c: ldur            w0, [x2, #0xf]
    // 0xbfd140: DecompressPointer r0
    //     0xbfd140: add             x0, x0, HEAP, lsl #32
    // 0xbfd144: LoadField: r1 = r0->field_b
    //     0xbfd144: ldur            w1, [x0, #0xb]
    // 0xbfd148: DecompressPointer r1
    //     0xbfd148: add             x1, x1, HEAP, lsl #32
    // 0xbfd14c: cmp             w1, NULL
    // 0xbfd150: b.eq            #0xbfd44c
    // 0xbfd154: LoadField: r0 = r1->field_b
    //     0xbfd154: ldur            w0, [x1, #0xb]
    // 0xbfd158: DecompressPointer r0
    //     0xbfd158: add             x0, x0, HEAP, lsl #32
    // 0xbfd15c: cmp             w0, NULL
    // 0xbfd160: b.ne            #0xbfd170
    // 0xbfd164: ldr             x3, [fp, #0x10]
    // 0xbfd168: r0 = Null
    //     0xbfd168: mov             x0, NULL
    // 0xbfd16c: b               #0xbfd1e0
    // 0xbfd170: LoadField: r1 = r0->field_b
    //     0xbfd170: ldur            w1, [x0, #0xb]
    // 0xbfd174: DecompressPointer r1
    //     0xbfd174: add             x1, x1, HEAP, lsl #32
    // 0xbfd178: cmp             w1, NULL
    // 0xbfd17c: b.ne            #0xbfd18c
    // 0xbfd180: ldr             x3, [fp, #0x10]
    // 0xbfd184: r0 = Null
    //     0xbfd184: mov             x0, NULL
    // 0xbfd188: b               #0xbfd1e0
    // 0xbfd18c: ldr             x3, [fp, #0x10]
    // 0xbfd190: LoadField: r0 = r1->field_f
    //     0xbfd190: ldur            w0, [x1, #0xf]
    // 0xbfd194: DecompressPointer r0
    //     0xbfd194: add             x0, x0, HEAP, lsl #32
    // 0xbfd198: LoadField: r4 = r0->field_b
    //     0xbfd198: ldur            w4, [x0, #0xb]
    // 0xbfd19c: DecompressPointer r4
    //     0xbfd19c: add             x4, x4, HEAP, lsl #32
    // 0xbfd1a0: LoadField: r0 = r4->field_b
    //     0xbfd1a0: ldur            w0, [x4, #0xb]
    // 0xbfd1a4: r5 = LoadInt32Instr(r3)
    //     0xbfd1a4: sbfx            x5, x3, #1, #0x1f
    //     0xbfd1a8: tbz             w3, #0, #0xbfd1b0
    //     0xbfd1ac: ldur            x5, [x3, #7]
    // 0xbfd1b0: r1 = LoadInt32Instr(r0)
    //     0xbfd1b0: sbfx            x1, x0, #1, #0x1f
    // 0xbfd1b4: mov             x0, x1
    // 0xbfd1b8: mov             x1, x5
    // 0xbfd1bc: cmp             x1, x0
    // 0xbfd1c0: b.hs            #0xbfd450
    // 0xbfd1c4: LoadField: r0 = r4->field_f
    //     0xbfd1c4: ldur            w0, [x4, #0xf]
    // 0xbfd1c8: DecompressPointer r0
    //     0xbfd1c8: add             x0, x0, HEAP, lsl #32
    // 0xbfd1cc: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbfd1cc: add             x16, x0, x5, lsl #2
    //     0xbfd1d0: ldur            w1, [x16, #0xf]
    // 0xbfd1d4: DecompressPointer r1
    //     0xbfd1d4: add             x1, x1, HEAP, lsl #32
    // 0xbfd1d8: LoadField: r0 = r1->field_7
    //     0xbfd1d8: ldur            w0, [x1, #7]
    // 0xbfd1dc: DecompressPointer r0
    //     0xbfd1dc: add             x0, x0, HEAP, lsl #32
    // 0xbfd1e0: str             x0, [SP]
    // 0xbfd1e4: r0 = _interpolateSingle()
    //     0xbfd1e4: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbfd1e8: ldr             x1, [fp, #0x18]
    // 0xbfd1ec: stur            x0, [fp, #-0x10]
    // 0xbfd1f0: r0 = of()
    //     0xbfd1f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfd1f4: LoadField: r1 = r0->field_87
    //     0xbfd1f4: ldur            w1, [x0, #0x87]
    // 0xbfd1f8: DecompressPointer r1
    //     0xbfd1f8: add             x1, x1, HEAP, lsl #32
    // 0xbfd1fc: LoadField: r0 = r1->field_7
    //     0xbfd1fc: ldur            w0, [x1, #7]
    // 0xbfd200: DecompressPointer r0
    //     0xbfd200: add             x0, x0, HEAP, lsl #32
    // 0xbfd204: r16 = Instance_Color
    //     0xbfd204: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfd208: r30 = 12.000000
    //     0xbfd208: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfd20c: ldr             lr, [lr, #0x9e8]
    // 0xbfd210: stp             lr, x16, [SP]
    // 0xbfd214: mov             x1, x0
    // 0xbfd218: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbfd218: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbfd21c: ldr             x4, [x4, #0x9b8]
    // 0xbfd220: r0 = copyWith()
    //     0xbfd220: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfd224: stur            x0, [fp, #-0x18]
    // 0xbfd228: r0 = Text()
    //     0xbfd228: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfd22c: mov             x2, x0
    // 0xbfd230: ldur            x0, [fp, #-0x10]
    // 0xbfd234: stur            x2, [fp, #-0x20]
    // 0xbfd238: StoreField: r2->field_b = r0
    //     0xbfd238: stur            w0, [x2, #0xb]
    // 0xbfd23c: ldur            x0, [fp, #-0x18]
    // 0xbfd240: StoreField: r2->field_13 = r0
    //     0xbfd240: stur            w0, [x2, #0x13]
    // 0xbfd244: ldur            x0, [fp, #-8]
    // 0xbfd248: LoadField: r1 = r0->field_f
    //     0xbfd248: ldur            w1, [x0, #0xf]
    // 0xbfd24c: DecompressPointer r1
    //     0xbfd24c: add             x1, x1, HEAP, lsl #32
    // 0xbfd250: LoadField: r0 = r1->field_b
    //     0xbfd250: ldur            w0, [x1, #0xb]
    // 0xbfd254: DecompressPointer r0
    //     0xbfd254: add             x0, x0, HEAP, lsl #32
    // 0xbfd258: cmp             w0, NULL
    // 0xbfd25c: b.eq            #0xbfd454
    // 0xbfd260: LoadField: r1 = r0->field_b
    //     0xbfd260: ldur            w1, [x0, #0xb]
    // 0xbfd264: DecompressPointer r1
    //     0xbfd264: add             x1, x1, HEAP, lsl #32
    // 0xbfd268: cmp             w1, NULL
    // 0xbfd26c: b.ne            #0xbfd278
    // 0xbfd270: r0 = Null
    //     0xbfd270: mov             x0, NULL
    // 0xbfd274: b               #0xbfd2e4
    // 0xbfd278: LoadField: r0 = r1->field_b
    //     0xbfd278: ldur            w0, [x1, #0xb]
    // 0xbfd27c: DecompressPointer r0
    //     0xbfd27c: add             x0, x0, HEAP, lsl #32
    // 0xbfd280: cmp             w0, NULL
    // 0xbfd284: b.ne            #0xbfd290
    // 0xbfd288: r0 = Null
    //     0xbfd288: mov             x0, NULL
    // 0xbfd28c: b               #0xbfd2e4
    // 0xbfd290: ldr             x1, [fp, #0x10]
    // 0xbfd294: LoadField: r3 = r0->field_f
    //     0xbfd294: ldur            w3, [x0, #0xf]
    // 0xbfd298: DecompressPointer r3
    //     0xbfd298: add             x3, x3, HEAP, lsl #32
    // 0xbfd29c: LoadField: r4 = r3->field_b
    //     0xbfd29c: ldur            w4, [x3, #0xb]
    // 0xbfd2a0: DecompressPointer r4
    //     0xbfd2a0: add             x4, x4, HEAP, lsl #32
    // 0xbfd2a4: LoadField: r0 = r4->field_b
    //     0xbfd2a4: ldur            w0, [x4, #0xb]
    // 0xbfd2a8: r3 = LoadInt32Instr(r1)
    //     0xbfd2a8: sbfx            x3, x1, #1, #0x1f
    //     0xbfd2ac: tbz             w1, #0, #0xbfd2b4
    //     0xbfd2b0: ldur            x3, [x1, #7]
    // 0xbfd2b4: r1 = LoadInt32Instr(r0)
    //     0xbfd2b4: sbfx            x1, x0, #1, #0x1f
    // 0xbfd2b8: mov             x0, x1
    // 0xbfd2bc: mov             x1, x3
    // 0xbfd2c0: cmp             x1, x0
    // 0xbfd2c4: b.hs            #0xbfd458
    // 0xbfd2c8: LoadField: r0 = r4->field_f
    //     0xbfd2c8: ldur            w0, [x4, #0xf]
    // 0xbfd2cc: DecompressPointer r0
    //     0xbfd2cc: add             x0, x0, HEAP, lsl #32
    // 0xbfd2d0: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xbfd2d0: add             x16, x0, x3, lsl #2
    //     0xbfd2d4: ldur            w1, [x16, #0xf]
    // 0xbfd2d8: DecompressPointer r1
    //     0xbfd2d8: add             x1, x1, HEAP, lsl #32
    // 0xbfd2dc: LoadField: r0 = r1->field_b
    //     0xbfd2dc: ldur            w0, [x1, #0xb]
    // 0xbfd2e0: DecompressPointer r0
    //     0xbfd2e0: add             x0, x0, HEAP, lsl #32
    // 0xbfd2e4: cmp             w0, NULL
    // 0xbfd2e8: b.ne            #0xbfd2f0
    // 0xbfd2ec: r0 = ""
    //     0xbfd2ec: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbfd2f0: ldr             x1, [fp, #0x18]
    // 0xbfd2f4: stur            x0, [fp, #-8]
    // 0xbfd2f8: r0 = of()
    //     0xbfd2f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfd2fc: LoadField: r1 = r0->field_87
    //     0xbfd2fc: ldur            w1, [x0, #0x87]
    // 0xbfd300: DecompressPointer r1
    //     0xbfd300: add             x1, x1, HEAP, lsl #32
    // 0xbfd304: LoadField: r0 = r1->field_2b
    //     0xbfd304: ldur            w0, [x1, #0x2b]
    // 0xbfd308: DecompressPointer r0
    //     0xbfd308: add             x0, x0, HEAP, lsl #32
    // 0xbfd30c: stur            x0, [fp, #-0x10]
    // 0xbfd310: r1 = Instance_Color
    //     0xbfd310: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfd314: d0 = 0.700000
    //     0xbfd314: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbfd318: ldr             d0, [x17, #0xf48]
    // 0xbfd31c: r0 = withOpacity()
    //     0xbfd31c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbfd320: r16 = 12.000000
    //     0xbfd320: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfd324: ldr             x16, [x16, #0x9e8]
    // 0xbfd328: stp             x16, x0, [SP]
    // 0xbfd32c: ldur            x1, [fp, #-0x10]
    // 0xbfd330: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbfd330: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbfd334: ldr             x4, [x4, #0x9b8]
    // 0xbfd338: r0 = copyWith()
    //     0xbfd338: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfd33c: stur            x0, [fp, #-0x10]
    // 0xbfd340: r0 = HtmlWidget()
    //     0xbfd340: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xbfd344: mov             x3, x0
    // 0xbfd348: ldur            x0, [fp, #-8]
    // 0xbfd34c: stur            x3, [fp, #-0x18]
    // 0xbfd350: StoreField: r3->field_1f = r0
    //     0xbfd350: stur            w0, [x3, #0x1f]
    // 0xbfd354: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xbfd354: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xbfd358: ldr             x0, [x0, #0x1e0]
    // 0xbfd35c: StoreField: r3->field_23 = r0
    //     0xbfd35c: stur            w0, [x3, #0x23]
    // 0xbfd360: r0 = Instance_ColumnMode
    //     0xbfd360: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xbfd364: ldr             x0, [x0, #0x1e8]
    // 0xbfd368: StoreField: r3->field_3b = r0
    //     0xbfd368: stur            w0, [x3, #0x3b]
    // 0xbfd36c: ldur            x0, [fp, #-0x10]
    // 0xbfd370: StoreField: r3->field_3f = r0
    //     0xbfd370: stur            w0, [x3, #0x3f]
    // 0xbfd374: r1 = Null
    //     0xbfd374: mov             x1, NULL
    // 0xbfd378: r2 = 6
    //     0xbfd378: movz            x2, #0x6
    // 0xbfd37c: r0 = AllocateArray()
    //     0xbfd37c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfd380: mov             x2, x0
    // 0xbfd384: ldur            x0, [fp, #-0x20]
    // 0xbfd388: stur            x2, [fp, #-8]
    // 0xbfd38c: StoreField: r2->field_f = r0
    //     0xbfd38c: stur            w0, [x2, #0xf]
    // 0xbfd390: r16 = Instance_SizedBox
    //     0xbfd390: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xbfd394: ldr             x16, [x16, #0xc70]
    // 0xbfd398: StoreField: r2->field_13 = r16
    //     0xbfd398: stur            w16, [x2, #0x13]
    // 0xbfd39c: ldur            x0, [fp, #-0x18]
    // 0xbfd3a0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbfd3a0: stur            w0, [x2, #0x17]
    // 0xbfd3a4: r1 = <Widget>
    //     0xbfd3a4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfd3a8: r0 = AllocateGrowableArray()
    //     0xbfd3a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfd3ac: mov             x1, x0
    // 0xbfd3b0: ldur            x0, [fp, #-8]
    // 0xbfd3b4: stur            x1, [fp, #-0x10]
    // 0xbfd3b8: StoreField: r1->field_f = r0
    //     0xbfd3b8: stur            w0, [x1, #0xf]
    // 0xbfd3bc: r0 = 6
    //     0xbfd3bc: movz            x0, #0x6
    // 0xbfd3c0: StoreField: r1->field_b = r0
    //     0xbfd3c0: stur            w0, [x1, #0xb]
    // 0xbfd3c4: r0 = Column()
    //     0xbfd3c4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbfd3c8: mov             x1, x0
    // 0xbfd3cc: r0 = Instance_Axis
    //     0xbfd3cc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbfd3d0: stur            x1, [fp, #-8]
    // 0xbfd3d4: StoreField: r1->field_f = r0
    //     0xbfd3d4: stur            w0, [x1, #0xf]
    // 0xbfd3d8: r0 = Instance_MainAxisAlignment
    //     0xbfd3d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfd3dc: ldr             x0, [x0, #0xa08]
    // 0xbfd3e0: StoreField: r1->field_13 = r0
    //     0xbfd3e0: stur            w0, [x1, #0x13]
    // 0xbfd3e4: r0 = Instance_MainAxisSize
    //     0xbfd3e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfd3e8: ldr             x0, [x0, #0xa10]
    // 0xbfd3ec: ArrayStore: r1[0] = r0  ; List_4
    //     0xbfd3ec: stur            w0, [x1, #0x17]
    // 0xbfd3f0: r0 = Instance_CrossAxisAlignment
    //     0xbfd3f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbfd3f4: ldr             x0, [x0, #0x890]
    // 0xbfd3f8: StoreField: r1->field_1b = r0
    //     0xbfd3f8: stur            w0, [x1, #0x1b]
    // 0xbfd3fc: r0 = Instance_VerticalDirection
    //     0xbfd3fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfd400: ldr             x0, [x0, #0xa20]
    // 0xbfd404: StoreField: r1->field_23 = r0
    //     0xbfd404: stur            w0, [x1, #0x23]
    // 0xbfd408: r0 = Instance_Clip
    //     0xbfd408: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfd40c: ldr             x0, [x0, #0x38]
    // 0xbfd410: StoreField: r1->field_2b = r0
    //     0xbfd410: stur            w0, [x1, #0x2b]
    // 0xbfd414: StoreField: r1->field_2f = rZR
    //     0xbfd414: stur            xzr, [x1, #0x2f]
    // 0xbfd418: ldur            x0, [fp, #-0x10]
    // 0xbfd41c: StoreField: r1->field_b = r0
    //     0xbfd41c: stur            w0, [x1, #0xb]
    // 0xbfd420: r0 = Padding()
    //     0xbfd420: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbfd424: r1 = Instance_EdgeInsets
    //     0xbfd424: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0xbfd428: ldr             x1, [x1, #0xf98]
    // 0xbfd42c: StoreField: r0->field_f = r1
    //     0xbfd42c: stur            w1, [x0, #0xf]
    // 0xbfd430: ldur            x1, [fp, #-8]
    // 0xbfd434: StoreField: r0->field_b = r1
    //     0xbfd434: stur            w1, [x0, #0xb]
    // 0xbfd438: LeaveFrame
    //     0xbfd438: mov             SP, fp
    //     0xbfd43c: ldp             fp, lr, [SP], #0x10
    // 0xbfd440: ret
    //     0xbfd440: ret             
    // 0xbfd444: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfd444: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfd448: b               #0xbfd13c
    // 0xbfd44c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfd44c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfd450: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbfd450: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbfd454: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfd454: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfd458: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbfd458: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbfd45c, size: 0x1fc
    // 0xbfd45c: EnterFrame
    //     0xbfd45c: stp             fp, lr, [SP, #-0x10]!
    //     0xbfd460: mov             fp, SP
    // 0xbfd464: AllocStack(0x20)
    //     0xbfd464: sub             SP, SP, #0x20
    // 0xbfd468: SetupParameters()
    //     0xbfd468: ldr             x0, [fp, #0x20]
    //     0xbfd46c: ldur            w1, [x0, #0x17]
    //     0xbfd470: add             x1, x1, HEAP, lsl #32
    // 0xbfd474: CheckStackOverflow
    //     0xbfd474: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfd478: cmp             SP, x16
    //     0xbfd47c: b.ls            #0xbfd648
    // 0xbfd480: LoadField: r0 = r1->field_f
    //     0xbfd480: ldur            w0, [x1, #0xf]
    // 0xbfd484: DecompressPointer r0
    //     0xbfd484: add             x0, x0, HEAP, lsl #32
    // 0xbfd488: LoadField: r1 = r0->field_b
    //     0xbfd488: ldur            w1, [x0, #0xb]
    // 0xbfd48c: DecompressPointer r1
    //     0xbfd48c: add             x1, x1, HEAP, lsl #32
    // 0xbfd490: cmp             w1, NULL
    // 0xbfd494: b.eq            #0xbfd650
    // 0xbfd498: LoadField: r0 = r1->field_b
    //     0xbfd498: ldur            w0, [x1, #0xb]
    // 0xbfd49c: DecompressPointer r0
    //     0xbfd49c: add             x0, x0, HEAP, lsl #32
    // 0xbfd4a0: cmp             w0, NULL
    // 0xbfd4a4: b.ne            #0xbfd4b0
    // 0xbfd4a8: r0 = Null
    //     0xbfd4a8: mov             x0, NULL
    // 0xbfd4ac: b               #0xbfd518
    // 0xbfd4b0: LoadField: r1 = r0->field_b
    //     0xbfd4b0: ldur            w1, [x0, #0xb]
    // 0xbfd4b4: DecompressPointer r1
    //     0xbfd4b4: add             x1, x1, HEAP, lsl #32
    // 0xbfd4b8: cmp             w1, NULL
    // 0xbfd4bc: b.ne            #0xbfd4c8
    // 0xbfd4c0: r0 = Null
    //     0xbfd4c0: mov             x0, NULL
    // 0xbfd4c4: b               #0xbfd518
    // 0xbfd4c8: ldr             x0, [fp, #0x10]
    // 0xbfd4cc: LoadField: r2 = r1->field_b
    //     0xbfd4cc: ldur            w2, [x1, #0xb]
    // 0xbfd4d0: DecompressPointer r2
    //     0xbfd4d0: add             x2, x2, HEAP, lsl #32
    // 0xbfd4d4: LoadField: r3 = r2->field_7
    //     0xbfd4d4: ldur            w3, [x2, #7]
    // 0xbfd4d8: DecompressPointer r3
    //     0xbfd4d8: add             x3, x3, HEAP, lsl #32
    // 0xbfd4dc: LoadField: r1 = r3->field_b
    //     0xbfd4dc: ldur            w1, [x3, #0xb]
    // 0xbfd4e0: r2 = LoadInt32Instr(r0)
    //     0xbfd4e0: sbfx            x2, x0, #1, #0x1f
    //     0xbfd4e4: tbz             w0, #0, #0xbfd4ec
    //     0xbfd4e8: ldur            x2, [x0, #7]
    // 0xbfd4ec: r0 = LoadInt32Instr(r1)
    //     0xbfd4ec: sbfx            x0, x1, #1, #0x1f
    // 0xbfd4f0: mov             x1, x2
    // 0xbfd4f4: cmp             x1, x0
    // 0xbfd4f8: b.hs            #0xbfd654
    // 0xbfd4fc: LoadField: r0 = r3->field_f
    //     0xbfd4fc: ldur            w0, [x3, #0xf]
    // 0xbfd500: DecompressPointer r0
    //     0xbfd500: add             x0, x0, HEAP, lsl #32
    // 0xbfd504: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xbfd504: add             x16, x0, x2, lsl #2
    //     0xbfd508: ldur            w1, [x16, #0xf]
    // 0xbfd50c: DecompressPointer r1
    //     0xbfd50c: add             x1, x1, HEAP, lsl #32
    // 0xbfd510: LoadField: r0 = r1->field_7
    //     0xbfd510: ldur            w0, [x1, #7]
    // 0xbfd514: DecompressPointer r0
    //     0xbfd514: add             x0, x0, HEAP, lsl #32
    // 0xbfd518: str             x0, [SP]
    // 0xbfd51c: r0 = _interpolateSingle()
    //     0xbfd51c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbfd520: ldr             x1, [fp, #0x18]
    // 0xbfd524: stur            x0, [fp, #-8]
    // 0xbfd528: r0 = of()
    //     0xbfd528: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfd52c: LoadField: r1 = r0->field_87
    //     0xbfd52c: ldur            w1, [x0, #0x87]
    // 0xbfd530: DecompressPointer r1
    //     0xbfd530: add             x1, x1, HEAP, lsl #32
    // 0xbfd534: LoadField: r0 = r1->field_2b
    //     0xbfd534: ldur            w0, [x1, #0x2b]
    // 0xbfd538: DecompressPointer r0
    //     0xbfd538: add             x0, x0, HEAP, lsl #32
    // 0xbfd53c: r16 = 12.000000
    //     0xbfd53c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfd540: ldr             x16, [x16, #0x9e8]
    // 0xbfd544: str             x16, [SP]
    // 0xbfd548: mov             x1, x0
    // 0xbfd54c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xbfd54c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xbfd550: ldr             x4, [x4, #0x798]
    // 0xbfd554: r0 = copyWith()
    //     0xbfd554: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfd558: stur            x0, [fp, #-0x10]
    // 0xbfd55c: r0 = Text()
    //     0xbfd55c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfd560: mov             x3, x0
    // 0xbfd564: ldur            x0, [fp, #-8]
    // 0xbfd568: stur            x3, [fp, #-0x18]
    // 0xbfd56c: StoreField: r3->field_b = r0
    //     0xbfd56c: stur            w0, [x3, #0xb]
    // 0xbfd570: ldur            x0, [fp, #-0x10]
    // 0xbfd574: StoreField: r3->field_13 = r0
    //     0xbfd574: stur            w0, [x3, #0x13]
    // 0xbfd578: r1 = Null
    //     0xbfd578: mov             x1, NULL
    // 0xbfd57c: r2 = 6
    //     0xbfd57c: movz            x2, #0x6
    // 0xbfd580: r0 = AllocateArray()
    //     0xbfd580: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfd584: stur            x0, [fp, #-8]
    // 0xbfd588: r16 = Instance_Icon
    //     0xbfd588: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c128] Obj!Icon@d66831
    //     0xbfd58c: ldr             x16, [x16, #0x128]
    // 0xbfd590: StoreField: r0->field_f = r16
    //     0xbfd590: stur            w16, [x0, #0xf]
    // 0xbfd594: r16 = Instance_SizedBox
    //     0xbfd594: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xbfd598: ldr             x16, [x16, #0x998]
    // 0xbfd59c: StoreField: r0->field_13 = r16
    //     0xbfd59c: stur            w16, [x0, #0x13]
    // 0xbfd5a0: ldur            x1, [fp, #-0x18]
    // 0xbfd5a4: ArrayStore: r0[0] = r1  ; List_4
    //     0xbfd5a4: stur            w1, [x0, #0x17]
    // 0xbfd5a8: r1 = <Widget>
    //     0xbfd5a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfd5ac: r0 = AllocateGrowableArray()
    //     0xbfd5ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfd5b0: mov             x1, x0
    // 0xbfd5b4: ldur            x0, [fp, #-8]
    // 0xbfd5b8: stur            x1, [fp, #-0x10]
    // 0xbfd5bc: StoreField: r1->field_f = r0
    //     0xbfd5bc: stur            w0, [x1, #0xf]
    // 0xbfd5c0: r0 = 6
    //     0xbfd5c0: movz            x0, #0x6
    // 0xbfd5c4: StoreField: r1->field_b = r0
    //     0xbfd5c4: stur            w0, [x1, #0xb]
    // 0xbfd5c8: r0 = Row()
    //     0xbfd5c8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbfd5cc: mov             x1, x0
    // 0xbfd5d0: r0 = Instance_Axis
    //     0xbfd5d0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbfd5d4: stur            x1, [fp, #-8]
    // 0xbfd5d8: StoreField: r1->field_f = r0
    //     0xbfd5d8: stur            w0, [x1, #0xf]
    // 0xbfd5dc: r0 = Instance_MainAxisAlignment
    //     0xbfd5dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfd5e0: ldr             x0, [x0, #0xa08]
    // 0xbfd5e4: StoreField: r1->field_13 = r0
    //     0xbfd5e4: stur            w0, [x1, #0x13]
    // 0xbfd5e8: r0 = Instance_MainAxisSize
    //     0xbfd5e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfd5ec: ldr             x0, [x0, #0xa10]
    // 0xbfd5f0: ArrayStore: r1[0] = r0  ; List_4
    //     0xbfd5f0: stur            w0, [x1, #0x17]
    // 0xbfd5f4: r0 = Instance_CrossAxisAlignment
    //     0xbfd5f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbfd5f8: ldr             x0, [x0, #0xa18]
    // 0xbfd5fc: StoreField: r1->field_1b = r0
    //     0xbfd5fc: stur            w0, [x1, #0x1b]
    // 0xbfd600: r0 = Instance_VerticalDirection
    //     0xbfd600: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfd604: ldr             x0, [x0, #0xa20]
    // 0xbfd608: StoreField: r1->field_23 = r0
    //     0xbfd608: stur            w0, [x1, #0x23]
    // 0xbfd60c: r0 = Instance_Clip
    //     0xbfd60c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfd610: ldr             x0, [x0, #0x38]
    // 0xbfd614: StoreField: r1->field_2b = r0
    //     0xbfd614: stur            w0, [x1, #0x2b]
    // 0xbfd618: StoreField: r1->field_2f = rZR
    //     0xbfd618: stur            xzr, [x1, #0x2f]
    // 0xbfd61c: ldur            x0, [fp, #-0x10]
    // 0xbfd620: StoreField: r1->field_b = r0
    //     0xbfd620: stur            w0, [x1, #0xb]
    // 0xbfd624: r0 = Padding()
    //     0xbfd624: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbfd628: r1 = Instance_EdgeInsets
    //     0xbfd628: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!EdgeInsets@d577d1
    //     0xbfd62c: ldr             x1, [x1, #0xb28]
    // 0xbfd630: StoreField: r0->field_f = r1
    //     0xbfd630: stur            w1, [x0, #0xf]
    // 0xbfd634: ldur            x1, [fp, #-8]
    // 0xbfd638: StoreField: r0->field_b = r1
    //     0xbfd638: stur            w1, [x0, #0xb]
    // 0xbfd63c: LeaveFrame
    //     0xbfd63c: mov             SP, fp
    //     0xbfd640: ldp             fp, lr, [SP], #0x10
    // 0xbfd644: ret
    //     0xbfd644: ret             
    // 0xbfd648: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfd648: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfd64c: b               #0xbfd480
    // 0xbfd650: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfd650: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfd654: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbfd654: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 3979, size: 0x10, field offset: 0xc
//   const constructor, 
class KnowMoreBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80e48, size: 0x24
    // 0xc80e48: EnterFrame
    //     0xc80e48: stp             fp, lr, [SP, #-0x10]!
    //     0xc80e4c: mov             fp, SP
    // 0xc80e50: mov             x0, x1
    // 0xc80e54: r1 = <KnowMoreBottomSheet>
    //     0xc80e54: add             x1, PP, #0x49, lsl #12  ; [pp+0x493b0] TypeArguments: <KnowMoreBottomSheet>
    //     0xc80e58: ldr             x1, [x1, #0x3b0]
    // 0xc80e5c: r0 = _KnowMoreBottomSheetState()
    //     0xc80e5c: bl              #0xc80e6c  ; Allocate_KnowMoreBottomSheetStateStub -> _KnowMoreBottomSheetState (size=0x14)
    // 0xc80e60: LeaveFrame
    //     0xc80e60: mov             SP, fp
    //     0xc80e64: ldp             fp, lr, [SP], #0x10
    // 0xc80e68: ret
    //     0xc80e68: ret             
  }
}
