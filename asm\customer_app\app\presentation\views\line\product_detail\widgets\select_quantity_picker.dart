// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/select_quantity_picker.dart

// class id: 1049570, size: 0x8
class :: {
}

// class id: 3215, size: 0x14, field offset: 0x14
class _SelectQuantityPickerState extends State<dynamic> {

  [closure] DropdownMenuItem<int> <anonymous closure>(dynamic, int) {
    // ** addr: 0xb911ec, size: 0x16c
    // 0xb911ec: EnterFrame
    //     0xb911ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb911f0: mov             fp, SP
    // 0xb911f4: AllocStack(0x28)
    //     0xb911f4: sub             SP, SP, #0x28
    // 0xb911f8: SetupParameters()
    //     0xb911f8: ldr             x0, [fp, #0x18]
    //     0xb911fc: ldur            w1, [x0, #0x17]
    //     0xb91200: add             x1, x1, HEAP, lsl #32
    //     0xb91204: stur            x1, [fp, #-8]
    // 0xb91208: CheckStackOverflow
    //     0xb91208: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9120c: cmp             SP, x16
    //     0xb91210: b.ls            #0xb91350
    // 0xb91214: ldr             x2, [fp, #0x10]
    // 0xb91218: r0 = 60
    //     0xb91218: movz            x0, #0x3c
    // 0xb9121c: branchIfSmi(r2, 0xb91228)
    //     0xb9121c: tbz             w2, #0, #0xb91228
    // 0xb91220: r0 = LoadClassIdInstr(r2)
    //     0xb91220: ldur            x0, [x2, #-1]
    //     0xb91224: ubfx            x0, x0, #0xc, #0x14
    // 0xb91228: str             x2, [SP]
    // 0xb9122c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb9122c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb91230: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb91230: movz            x17, #0x2700
    //     0xb91234: add             lr, x0, x17
    //     0xb91238: ldr             lr, [x21, lr, lsl #3]
    //     0xb9123c: blr             lr
    // 0xb91240: mov             x2, x0
    // 0xb91244: ldur            x0, [fp, #-8]
    // 0xb91248: stur            x2, [fp, #-0x10]
    // 0xb9124c: LoadField: r1 = r0->field_13
    //     0xb9124c: ldur            w1, [x0, #0x13]
    // 0xb91250: DecompressPointer r1
    //     0xb91250: add             x1, x1, HEAP, lsl #32
    // 0xb91254: r0 = of()
    //     0xb91254: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb91258: LoadField: r1 = r0->field_87
    //     0xb91258: ldur            w1, [x0, #0x87]
    // 0xb9125c: DecompressPointer r1
    //     0xb9125c: add             x1, x1, HEAP, lsl #32
    // 0xb91260: LoadField: r0 = r1->field_7
    //     0xb91260: ldur            w0, [x1, #7]
    // 0xb91264: DecompressPointer r0
    //     0xb91264: add             x0, x0, HEAP, lsl #32
    // 0xb91268: stur            x0, [fp, #-8]
    // 0xb9126c: r1 = Instance_Color
    //     0xb9126c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb91270: d0 = 0.700000
    //     0xb91270: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb91274: ldr             d0, [x17, #0xf48]
    // 0xb91278: r0 = withOpacity()
    //     0xb91278: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb9127c: r16 = 14.000000
    //     0xb9127c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb91280: ldr             x16, [x16, #0x1d8]
    // 0xb91284: stp             x0, x16, [SP]
    // 0xb91288: ldur            x1, [fp, #-8]
    // 0xb9128c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb9128c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb91290: ldr             x4, [x4, #0xaa0]
    // 0xb91294: r0 = copyWith()
    //     0xb91294: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb91298: stur            x0, [fp, #-8]
    // 0xb9129c: r0 = Text()
    //     0xb9129c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb912a0: mov             x1, x0
    // 0xb912a4: ldur            x0, [fp, #-0x10]
    // 0xb912a8: stur            x1, [fp, #-0x18]
    // 0xb912ac: StoreField: r1->field_b = r0
    //     0xb912ac: stur            w0, [x1, #0xb]
    // 0xb912b0: ldur            x0, [fp, #-8]
    // 0xb912b4: StoreField: r1->field_13 = r0
    //     0xb912b4: stur            w0, [x1, #0x13]
    // 0xb912b8: r0 = Instance_TextAlign
    //     0xb912b8: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb912bc: StoreField: r1->field_1b = r0
    //     0xb912bc: stur            w0, [x1, #0x1b]
    // 0xb912c0: r0 = Instance_TextOverflow
    //     0xb912c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb912c4: ldr             x0, [x0, #0xe10]
    // 0xb912c8: StoreField: r1->field_2b = r0
    //     0xb912c8: stur            w0, [x1, #0x2b]
    // 0xb912cc: r0 = Center()
    //     0xb912cc: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb912d0: mov             x1, x0
    // 0xb912d4: r0 = Instance_Alignment
    //     0xb912d4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb912d8: ldr             x0, [x0, #0xb10]
    // 0xb912dc: stur            x1, [fp, #-8]
    // 0xb912e0: StoreField: r1->field_f = r0
    //     0xb912e0: stur            w0, [x1, #0xf]
    // 0xb912e4: ldur            x0, [fp, #-0x18]
    // 0xb912e8: StoreField: r1->field_b = r0
    //     0xb912e8: stur            w0, [x1, #0xb]
    // 0xb912ec: r0 = SizedBox()
    //     0xb912ec: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb912f0: mov             x2, x0
    // 0xb912f4: r0 = 140.000000
    //     0xb912f4: add             x0, PP, #0x52, lsl #12  ; [pp+0x523c8] 140
    //     0xb912f8: ldr             x0, [x0, #0x3c8]
    // 0xb912fc: stur            x2, [fp, #-0x10]
    // 0xb91300: StoreField: r2->field_f = r0
    //     0xb91300: stur            w0, [x2, #0xf]
    // 0xb91304: r0 = 44.000000
    //     0xb91304: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xb91308: ldr             x0, [x0, #0xad8]
    // 0xb9130c: StoreField: r2->field_13 = r0
    //     0xb9130c: stur            w0, [x2, #0x13]
    // 0xb91310: ldur            x0, [fp, #-8]
    // 0xb91314: StoreField: r2->field_b = r0
    //     0xb91314: stur            w0, [x2, #0xb]
    // 0xb91318: r1 = <int>
    //     0xb91318: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xb9131c: r0 = DropdownMenuItem()
    //     0xb9131c: bl              #0x9ba75c  ; AllocateDropdownMenuItemStub -> DropdownMenuItem<X0> (size=0x24)
    // 0xb91320: ldr             x1, [fp, #0x10]
    // 0xb91324: StoreField: r0->field_1b = r1
    //     0xb91324: stur            w1, [x0, #0x1b]
    // 0xb91328: r1 = true
    //     0xb91328: add             x1, NULL, #0x20  ; true
    // 0xb9132c: StoreField: r0->field_1f = r1
    //     0xb9132c: stur            w1, [x0, #0x1f]
    // 0xb91330: r1 = Instance_AlignmentDirectional
    //     0xb91330: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb70] Obj!AlignmentDirectional@d5a601
    //     0xb91334: ldr             x1, [x1, #0xb70]
    // 0xb91338: StoreField: r0->field_f = r1
    //     0xb91338: stur            w1, [x0, #0xf]
    // 0xb9133c: ldur            x1, [fp, #-0x10]
    // 0xb91340: StoreField: r0->field_b = r1
    //     0xb91340: stur            w1, [x0, #0xb]
    // 0xb91344: LeaveFrame
    //     0xb91344: mov             SP, fp
    //     0xb91348: ldp             fp, lr, [SP], #0x10
    // 0xb9134c: ret
    //     0xb9134c: ret             
    // 0xb91350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb91350: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb91354: b               #0xb91214
  }
  _ build(/* No info */) {
    // ** addr: 0xc0c320, size: 0x678
    // 0xc0c320: EnterFrame
    //     0xc0c320: stp             fp, lr, [SP, #-0x10]!
    //     0xc0c324: mov             fp, SP
    // 0xc0c328: AllocStack(0x50)
    //     0xc0c328: sub             SP, SP, #0x50
    // 0xc0c32c: SetupParameters(_SelectQuantityPickerState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xc0c32c: stur            x1, [fp, #-8]
    //     0xc0c330: stur            x2, [fp, #-0x10]
    // 0xc0c334: CheckStackOverflow
    //     0xc0c334: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0c338: cmp             SP, x16
    //     0xc0c33c: b.ls            #0xc0c964
    // 0xc0c340: r1 = 4
    //     0xc0c340: movz            x1, #0x4
    // 0xc0c344: r0 = AllocateContext()
    //     0xc0c344: bl              #0x16f6108  ; AllocateContextStub
    // 0xc0c348: mov             x1, x0
    // 0xc0c34c: ldur            x0, [fp, #-8]
    // 0xc0c350: stur            x1, [fp, #-0x18]
    // 0xc0c354: StoreField: r1->field_f = r0
    //     0xc0c354: stur            w0, [x1, #0xf]
    // 0xc0c358: ldur            x2, [fp, #-0x10]
    // 0xc0c35c: StoreField: r1->field_13 = r2
    //     0xc0c35c: stur            w2, [x1, #0x13]
    // 0xc0c360: r2 = Sentinel
    //     0xc0c360: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc0c364: ArrayStore: r1[0] = r2  ; List_4
    //     0xc0c364: stur            w2, [x1, #0x17]
    // 0xc0c368: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xc0c368: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc0c36c: ldr             x0, [x0]
    //     0xc0c370: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc0c374: cmp             w0, w16
    //     0xc0c378: b.ne            #0xc0c384
    //     0xc0c37c: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xc0c380: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc0c384: r1 = <int>
    //     0xc0c384: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xc0c388: stur            x0, [fp, #-0x10]
    // 0xc0c38c: r0 = AllocateGrowableArray()
    //     0xc0c38c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0c390: mov             x1, x0
    // 0xc0c394: ldur            x0, [fp, #-0x10]
    // 0xc0c398: StoreField: r1->field_f = r0
    //     0xc0c398: stur            w0, [x1, #0xf]
    // 0xc0c39c: StoreField: r1->field_b = rZR
    //     0xc0c39c: stur            wzr, [x1, #0xb]
    // 0xc0c3a0: ldur            x3, [fp, #-0x18]
    // 0xc0c3a4: StoreField: r3->field_1b = r1
    //     0xc0c3a4: stur            w1, [x3, #0x1b]
    // 0xc0c3a8: ldur            x4, [fp, #-8]
    // 0xc0c3ac: LoadField: r0 = r4->field_b
    //     0xc0c3ac: ldur            w0, [x4, #0xb]
    // 0xc0c3b0: DecompressPointer r0
    //     0xc0c3b0: add             x0, x0, HEAP, lsl #32
    // 0xc0c3b4: cmp             w0, NULL
    // 0xc0c3b8: b.eq            #0xc0c96c
    // 0xc0c3bc: LoadField: r1 = r0->field_b
    //     0xc0c3bc: ldur            w1, [x0, #0xb]
    // 0xc0c3c0: DecompressPointer r1
    //     0xc0c3c0: add             x1, x1, HEAP, lsl #32
    // 0xc0c3c4: LoadField: r2 = r1->field_7b
    //     0xc0c3c4: ldur            w2, [x1, #0x7b]
    // 0xc0c3c8: DecompressPointer r2
    //     0xc0c3c8: add             x2, x2, HEAP, lsl #32
    // 0xc0c3cc: cmp             w2, NULL
    // 0xc0c3d0: b.ne            #0xc0c3dc
    // 0xc0c3d4: r5 = 1
    //     0xc0c3d4: movz            x5, #0x1
    // 0xc0c3d8: b               #0xc0c3ec
    // 0xc0c3dc: r0 = LoadInt32Instr(r2)
    //     0xc0c3dc: sbfx            x0, x2, #1, #0x1f
    //     0xc0c3e0: tbz             w2, #0, #0xc0c3e8
    //     0xc0c3e4: ldur            x0, [x2, #7]
    // 0xc0c3e8: mov             x5, x0
    // 0xc0c3ec: r0 = BoxInt64Instr(r5)
    //     0xc0c3ec: sbfiz           x0, x5, #1, #0x1f
    //     0xc0c3f0: cmp             x5, x0, asr #1
    //     0xc0c3f4: b.eq            #0xc0c400
    //     0xc0c3f8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc0c3fc: stur            x5, [x0, #7]
    // 0xc0c400: stur            x0, [fp, #-0x10]
    // 0xc0c404: cmp             x5, #5
    // 0xc0c408: b.le            #0xc0c4d0
    // 0xc0c40c: r1 = <int>
    //     0xc0c40c: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xc0c410: r2 = 5
    //     0xc0c410: movz            x2, #0x5
    // 0xc0c414: r0 = _GrowableList()
    //     0xc0c414: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc0c418: LoadField: r1 = r0->field_b
    //     0xc0c418: ldur            w1, [x0, #0xb]
    // 0xc0c41c: r2 = LoadInt32Instr(r1)
    //     0xc0c41c: sbfx            x2, x1, #1, #0x1f
    // 0xc0c420: LoadField: r3 = r0->field_f
    //     0xc0c420: ldur            w3, [x0, #0xf]
    // 0xc0c424: DecompressPointer r3
    //     0xc0c424: add             x3, x3, HEAP, lsl #32
    // 0xc0c428: r1 = 0
    //     0xc0c428: movz            x1, #0
    // 0xc0c42c: CheckStackOverflow
    //     0xc0c42c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0c430: cmp             SP, x16
    //     0xc0c434: b.ls            #0xc0c970
    // 0xc0c438: cmp             x1, x2
    // 0xc0c43c: b.ge            #0xc0c458
    // 0xc0c440: add             x4, x1, #1
    // 0xc0c444: lsl             x5, x4, #1
    // 0xc0c448: ArrayStore: r3[r1] = r5  ; Unknown_4
    //     0xc0c448: add             x6, x3, x1, lsl #2
    //     0xc0c44c: stur            w5, [x6, #0xf]
    // 0xc0c450: mov             x1, x4
    // 0xc0c454: b               #0xc0c42c
    // 0xc0c458: ldur            x5, [fp, #-8]
    // 0xc0c45c: ldur            x4, [fp, #-0x18]
    // 0xc0c460: StoreField: r4->field_1b = r0
    //     0xc0c460: stur            w0, [x4, #0x1b]
    //     0xc0c464: ldurb           w16, [x4, #-1]
    //     0xc0c468: ldurb           w17, [x0, #-1]
    //     0xc0c46c: and             x16, x17, x16, lsr #2
    //     0xc0c470: tst             x16, HEAP, lsr #32
    //     0xc0c474: b.eq            #0xc0c47c
    //     0xc0c478: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0xc0c47c: LoadField: r0 = r5->field_b
    //     0xc0c47c: ldur            w0, [x5, #0xb]
    // 0xc0c480: DecompressPointer r0
    //     0xc0c480: add             x0, x0, HEAP, lsl #32
    // 0xc0c484: cmp             w0, NULL
    // 0xc0c488: b.eq            #0xc0c978
    // 0xc0c48c: LoadField: r5 = r0->field_f
    //     0xc0c48c: ldur            x5, [x0, #0xf]
    // 0xc0c490: mov             x0, x2
    // 0xc0c494: mov             x1, x5
    // 0xc0c498: cmp             x1, x0
    // 0xc0c49c: b.hs            #0xc0c97c
    // 0xc0c4a0: ArrayLoad: r0 = r3[r5]  ; Unknown_4
    //     0xc0c4a0: add             x16, x3, x5, lsl #2
    //     0xc0c4a4: ldur            w0, [x16, #0xf]
    // 0xc0c4a8: DecompressPointer r0
    //     0xc0c4a8: add             x0, x0, HEAP, lsl #32
    // 0xc0c4ac: ArrayStore: r4[0] = r0  ; List_4
    //     0xc0c4ac: stur            w0, [x4, #0x17]
    //     0xc0c4b0: tbz             w0, #0, #0xc0c4cc
    //     0xc0c4b4: ldurb           w16, [x4, #-1]
    //     0xc0c4b8: ldurb           w17, [x0, #-1]
    //     0xc0c4bc: and             x16, x17, x16, lsr #2
    //     0xc0c4c0: tst             x16, HEAP, lsr #32
    //     0xc0c4c4: b.eq            #0xc0c4cc
    //     0xc0c4c8: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0xc0c4cc: b               #0xc0c634
    // 0xc0c4d0: mov             x5, x4
    // 0xc0c4d4: mov             x4, x3
    // 0xc0c4d8: cbnz            w0, #0xc0c554
    // 0xc0c4dc: ArrayStore: r4[0] = rZR  ; List_4
    //     0xc0c4dc: stur            wzr, [x4, #0x17]
    // 0xc0c4e0: r1 = <int>
    //     0xc0c4e0: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xc0c4e4: r2 = 1
    //     0xc0c4e4: movz            x2, #0x1
    // 0xc0c4e8: r0 = _GrowableList()
    //     0xc0c4e8: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc0c4ec: LoadField: r1 = r0->field_b
    //     0xc0c4ec: ldur            w1, [x0, #0xb]
    // 0xc0c4f0: r2 = LoadInt32Instr(r1)
    //     0xc0c4f0: sbfx            x2, x1, #1, #0x1f
    // 0xc0c4f4: LoadField: r1 = r0->field_f
    //     0xc0c4f4: ldur            w1, [x0, #0xf]
    // 0xc0c4f8: DecompressPointer r1
    //     0xc0c4f8: add             x1, x1, HEAP, lsl #32
    // 0xc0c4fc: r3 = 0
    //     0xc0c4fc: movz            x3, #0
    // 0xc0c500: CheckStackOverflow
    //     0xc0c500: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0c504: cmp             SP, x16
    //     0xc0c508: b.ls            #0xc0c980
    // 0xc0c50c: cmp             x3, x2
    // 0xc0c510: b.ge            #0xc0c52c
    // 0xc0c514: lsl             x4, x3, #1
    // 0xc0c518: ArrayStore: r1[r3] = r4  ; Unknown_4
    //     0xc0c518: add             x5, x1, x3, lsl #2
    //     0xc0c51c: stur            w4, [x5, #0xf]
    // 0xc0c520: add             x4, x3, #1
    // 0xc0c524: mov             x3, x4
    // 0xc0c528: b               #0xc0c500
    // 0xc0c52c: ldur            x3, [fp, #-0x18]
    // 0xc0c530: StoreField: r3->field_1b = r0
    //     0xc0c530: stur            w0, [x3, #0x1b]
    //     0xc0c534: ldurb           w16, [x3, #-1]
    //     0xc0c538: ldurb           w17, [x0, #-1]
    //     0xc0c53c: and             x16, x17, x16, lsr #2
    //     0xc0c540: tst             x16, HEAP, lsr #32
    //     0xc0c544: b.eq            #0xc0c54c
    //     0xc0c548: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xc0c54c: mov             x4, x3
    // 0xc0c550: b               #0xc0c634
    // 0xc0c554: mov             x3, x4
    // 0xc0c558: cmp             w2, NULL
    // 0xc0c55c: b.ne            #0xc0c568
    // 0xc0c560: r2 = 1
    //     0xc0c560: movz            x2, #0x1
    // 0xc0c564: b               #0xc0c578
    // 0xc0c568: r0 = LoadInt32Instr(r2)
    //     0xc0c568: sbfx            x0, x2, #1, #0x1f
    //     0xc0c56c: tbz             w2, #0, #0xc0c574
    //     0xc0c570: ldur            x0, [x2, #7]
    // 0xc0c574: mov             x2, x0
    // 0xc0c578: r1 = <int>
    //     0xc0c578: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xc0c57c: r0 = _GrowableList()
    //     0xc0c57c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc0c580: LoadField: r1 = r0->field_b
    //     0xc0c580: ldur            w1, [x0, #0xb]
    // 0xc0c584: r2 = LoadInt32Instr(r1)
    //     0xc0c584: sbfx            x2, x1, #1, #0x1f
    // 0xc0c588: LoadField: r3 = r0->field_f
    //     0xc0c588: ldur            w3, [x0, #0xf]
    // 0xc0c58c: DecompressPointer r3
    //     0xc0c58c: add             x3, x3, HEAP, lsl #32
    // 0xc0c590: r1 = 0
    //     0xc0c590: movz            x1, #0
    // 0xc0c594: CheckStackOverflow
    //     0xc0c594: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0c598: cmp             SP, x16
    //     0xc0c59c: b.ls            #0xc0c988
    // 0xc0c5a0: cmp             x1, x2
    // 0xc0c5a4: b.ge            #0xc0c5c0
    // 0xc0c5a8: add             x4, x1, #1
    // 0xc0c5ac: lsl             x5, x4, #1
    // 0xc0c5b0: ArrayStore: r3[r1] = r5  ; Unknown_4
    //     0xc0c5b0: add             x6, x3, x1, lsl #2
    //     0xc0c5b4: stur            w5, [x6, #0xf]
    // 0xc0c5b8: mov             x1, x4
    // 0xc0c5bc: b               #0xc0c594
    // 0xc0c5c0: ldur            x1, [fp, #-8]
    // 0xc0c5c4: ldur            x4, [fp, #-0x18]
    // 0xc0c5c8: StoreField: r4->field_1b = r0
    //     0xc0c5c8: stur            w0, [x4, #0x1b]
    //     0xc0c5cc: ldurb           w16, [x4, #-1]
    //     0xc0c5d0: ldurb           w17, [x0, #-1]
    //     0xc0c5d4: and             x16, x17, x16, lsr #2
    //     0xc0c5d8: tst             x16, HEAP, lsr #32
    //     0xc0c5dc: b.eq            #0xc0c5e4
    //     0xc0c5e0: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0xc0c5e4: LoadField: r0 = r1->field_b
    //     0xc0c5e4: ldur            w0, [x1, #0xb]
    // 0xc0c5e8: DecompressPointer r0
    //     0xc0c5e8: add             x0, x0, HEAP, lsl #32
    // 0xc0c5ec: cmp             w0, NULL
    // 0xc0c5f0: b.eq            #0xc0c990
    // 0xc0c5f4: LoadField: r5 = r0->field_f
    //     0xc0c5f4: ldur            x5, [x0, #0xf]
    // 0xc0c5f8: mov             x0, x2
    // 0xc0c5fc: mov             x1, x5
    // 0xc0c600: cmp             x1, x0
    // 0xc0c604: b.hs            #0xc0c994
    // 0xc0c608: ArrayLoad: r0 = r3[r5]  ; Unknown_4
    //     0xc0c608: add             x16, x3, x5, lsl #2
    //     0xc0c60c: ldur            w0, [x16, #0xf]
    // 0xc0c610: DecompressPointer r0
    //     0xc0c610: add             x0, x0, HEAP, lsl #32
    // 0xc0c614: ArrayStore: r4[0] = r0  ; List_4
    //     0xc0c614: stur            w0, [x4, #0x17]
    //     0xc0c618: tbz             w0, #0, #0xc0c634
    //     0xc0c61c: ldurb           w16, [x4, #-1]
    //     0xc0c620: ldurb           w17, [x0, #-1]
    //     0xc0c624: and             x16, x17, x16, lsr #2
    //     0xc0c628: tst             x16, HEAP, lsr #32
    //     0xc0c62c: b.eq            #0xc0c634
    //     0xc0c630: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0xc0c634: LoadField: r1 = r4->field_13
    //     0xc0c634: ldur            w1, [x4, #0x13]
    // 0xc0c638: DecompressPointer r1
    //     0xc0c638: add             x1, x1, HEAP, lsl #32
    // 0xc0c63c: r0 = of()
    //     0xc0c63c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0c640: LoadField: r1 = r0->field_87
    //     0xc0c640: ldur            w1, [x0, #0x87]
    // 0xc0c644: DecompressPointer r1
    //     0xc0c644: add             x1, x1, HEAP, lsl #32
    // 0xc0c648: LoadField: r0 = r1->field_7
    //     0xc0c648: ldur            w0, [x1, #7]
    // 0xc0c64c: DecompressPointer r0
    //     0xc0c64c: add             x0, x0, HEAP, lsl #32
    // 0xc0c650: r16 = 12.000000
    //     0xc0c650: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0c654: ldr             x16, [x16, #0x9e8]
    // 0xc0c658: r30 = Instance_Color
    //     0xc0c658: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0c65c: stp             lr, x16, [SP]
    // 0xc0c660: mov             x1, x0
    // 0xc0c664: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc0c664: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc0c668: ldr             x4, [x4, #0xaa0]
    // 0xc0c66c: r0 = copyWith()
    //     0xc0c66c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0c670: stur            x0, [fp, #-8]
    // 0xc0c674: r0 = Text()
    //     0xc0c674: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0c678: mov             x2, x0
    // 0xc0c67c: r0 = "Quantity"
    //     0xc0c67c: add             x0, PP, #0x52, lsl #12  ; [pp+0x523f8] "Quantity"
    //     0xc0c680: ldr             x0, [x0, #0x3f8]
    // 0xc0c684: stur            x2, [fp, #-0x20]
    // 0xc0c688: StoreField: r2->field_b = r0
    //     0xc0c688: stur            w0, [x2, #0xb]
    // 0xc0c68c: ldur            x0, [fp, #-8]
    // 0xc0c690: StoreField: r2->field_13 = r0
    //     0xc0c690: stur            w0, [x2, #0x13]
    // 0xc0c694: ldur            x0, [fp, #-0x18]
    // 0xc0c698: LoadField: r1 = r0->field_13
    //     0xc0c698: ldur            w1, [x0, #0x13]
    // 0xc0c69c: DecompressPointer r1
    //     0xc0c69c: add             x1, x1, HEAP, lsl #32
    // 0xc0c6a0: r0 = of()
    //     0xc0c6a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0c6a4: LoadField: r1 = r0->field_5b
    //     0xc0c6a4: ldur            w1, [x0, #0x5b]
    // 0xc0c6a8: DecompressPointer r1
    //     0xc0c6a8: add             x1, x1, HEAP, lsl #32
    // 0xc0c6ac: r0 = LoadClassIdInstr(r1)
    //     0xc0c6ac: ldur            x0, [x1, #-1]
    //     0xc0c6b0: ubfx            x0, x0, #0xc, #0x14
    // 0xc0c6b4: d0 = 0.100000
    //     0xc0c6b4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc0c6b8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xc0c6b8: sub             lr, x0, #0xffa
    //     0xc0c6bc: ldr             lr, [x21, lr, lsl #3]
    //     0xc0c6c0: blr             lr
    // 0xc0c6c4: r16 = 1.000000
    //     0xc0c6c4: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xc0c6c8: str             x16, [SP]
    // 0xc0c6cc: mov             x2, x0
    // 0xc0c6d0: r1 = Null
    //     0xc0c6d0: mov             x1, NULL
    // 0xc0c6d4: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xc0c6d4: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xc0c6d8: ldr             x4, [x4, #0x108]
    // 0xc0c6dc: r0 = Border.all()
    //     0xc0c6dc: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xc0c6e0: stur            x0, [fp, #-8]
    // 0xc0c6e4: r0 = BoxDecoration()
    //     0xc0c6e4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc0c6e8: mov             x2, x0
    // 0xc0c6ec: r0 = Instance_Color
    //     0xc0c6ec: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc0c6f0: stur            x2, [fp, #-0x28]
    // 0xc0c6f4: StoreField: r2->field_7 = r0
    //     0xc0c6f4: stur            w0, [x2, #7]
    // 0xc0c6f8: ldur            x0, [fp, #-8]
    // 0xc0c6fc: StoreField: r2->field_f = r0
    //     0xc0c6fc: stur            w0, [x2, #0xf]
    // 0xc0c700: r0 = Instance_BoxShape
    //     0xc0c700: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0c704: ldr             x0, [x0, #0x80]
    // 0xc0c708: StoreField: r2->field_23 = r0
    //     0xc0c708: stur            w0, [x2, #0x23]
    // 0xc0c70c: ldur            x0, [fp, #-0x18]
    // 0xc0c710: ArrayLoad: r5 = r0[0]  ; List_4
    //     0xc0c710: ldur            w5, [x0, #0x17]
    // 0xc0c714: DecompressPointer r5
    //     0xc0c714: add             x5, x5, HEAP, lsl #32
    // 0xc0c718: stur            x5, [fp, #-8]
    // 0xc0c71c: r16 = Sentinel
    //     0xc0c71c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc0c720: cmp             w5, w16
    // 0xc0c724: b.eq            #0xc0c950
    // 0xc0c728: ldur            x3, [fp, #-0x10]
    // 0xc0c72c: LoadField: r1 = r0->field_13
    //     0xc0c72c: ldur            w1, [x0, #0x13]
    // 0xc0c730: DecompressPointer r1
    //     0xc0c730: add             x1, x1, HEAP, lsl #32
    // 0xc0c734: r0 = of()
    //     0xc0c734: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0c738: LoadField: r3 = r0->field_5b
    //     0xc0c738: ldur            w3, [x0, #0x5b]
    // 0xc0c73c: DecompressPointer r3
    //     0xc0c73c: add             x3, x3, HEAP, lsl #32
    // 0xc0c740: ldur            x0, [fp, #-0x18]
    // 0xc0c744: stur            x3, [fp, #-0x38]
    // 0xc0c748: LoadField: r4 = r0->field_1b
    //     0xc0c748: ldur            w4, [x0, #0x1b]
    // 0xc0c74c: DecompressPointer r4
    //     0xc0c74c: add             x4, x4, HEAP, lsl #32
    // 0xc0c750: mov             x2, x0
    // 0xc0c754: stur            x4, [fp, #-0x30]
    // 0xc0c758: r1 = Function '<anonymous closure>':.
    //     0xc0c758: add             x1, PP, #0x52, lsl #12  ; [pp+0x52400] AnonymousClosure: (0xb911ec), in [package:customer_app/app/presentation/views/line/product_detail/widgets/select_quantity_picker.dart] _SelectQuantityPickerState::build (0xc0c320)
    //     0xc0c75c: ldr             x1, [x1, #0x400]
    // 0xc0c760: r0 = AllocateClosure()
    //     0xc0c760: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0c764: r16 = <DropdownMenuItem<int>>
    //     0xc0c764: add             x16, PP, #0x52, lsl #12  ; [pp+0x52408] TypeArguments: <DropdownMenuItem<int>>
    //     0xc0c768: ldr             x16, [x16, #0x408]
    // 0xc0c76c: ldur            lr, [fp, #-0x30]
    // 0xc0c770: stp             lr, x16, [SP, #8]
    // 0xc0c774: str             x0, [SP]
    // 0xc0c778: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc0c778: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc0c77c: r0 = map()
    //     0xc0c77c: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0xc0c780: mov             x1, x0
    // 0xc0c784: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc0c784: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc0c788: r0 = toList()
    //     0xc0c788: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xc0c78c: mov             x3, x0
    // 0xc0c790: ldur            x0, [fp, #-0x10]
    // 0xc0c794: stur            x3, [fp, #-0x30]
    // 0xc0c798: cbz             w0, #0xc0c7b4
    // 0xc0c79c: ldur            x2, [fp, #-0x18]
    // 0xc0c7a0: r1 = Function '<anonymous closure>':.
    //     0xc0c7a0: add             x1, PP, #0x52, lsl #12  ; [pp+0x52410] AnonymousClosure: (0xc0c998), in [package:customer_app/app/presentation/views/line/product_detail/widgets/select_quantity_picker.dart] _SelectQuantityPickerState::build (0xc0c320)
    //     0xc0c7a4: ldr             x1, [x1, #0x410]
    // 0xc0c7a8: r0 = AllocateClosure()
    //     0xc0c7a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0c7ac: mov             x3, x0
    // 0xc0c7b0: b               #0xc0c7b8
    // 0xc0c7b4: r3 = Null
    //     0xc0c7b4: mov             x3, NULL
    // 0xc0c7b8: ldur            x0, [fp, #-0x20]
    // 0xc0c7bc: stur            x3, [fp, #-0x10]
    // 0xc0c7c0: r1 = <int>
    //     0xc0c7c0: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xc0c7c4: r0 = DropdownButton()
    //     0xc0c7c4: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0xc0c7c8: stur            x0, [fp, #-0x18]
    // 0xc0c7cc: r16 = Instance_Color
    //     0xc0c7cc: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc0c7d0: r30 = Instance_Icon
    //     0xc0c7d0: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3e530] Obj!Icon@d66771
    //     0xc0c7d4: ldr             lr, [lr, #0x530]
    // 0xc0c7d8: stp             lr, x16, [SP, #8]
    // 0xc0c7dc: ldur            x16, [fp, #-0x38]
    // 0xc0c7e0: str             x16, [SP]
    // 0xc0c7e4: mov             x1, x0
    // 0xc0c7e8: ldur            x2, [fp, #-0x30]
    // 0xc0c7ec: ldur            x3, [fp, #-0x10]
    // 0xc0c7f0: ldur            x5, [fp, #-8]
    // 0xc0c7f4: r4 = const [0, 0x7, 0x3, 0x4, dropdownColor, 0x4, icon, 0x5, iconEnabledColor, 0x6, null]
    //     0xc0c7f4: add             x4, PP, #0x52, lsl #12  ; [pp+0x52398] List(11) [0, 0x7, 0x3, 0x4, "dropdownColor", 0x4, "icon", 0x5, "iconEnabledColor", 0x6, Null]
    //     0xc0c7f8: ldr             x4, [x4, #0x398]
    // 0xc0c7fc: r0 = DropdownButton()
    //     0xc0c7fc: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0xc0c800: r0 = DropdownButtonHideUnderline()
    //     0xc0c800: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0xc0c804: mov             x1, x0
    // 0xc0c808: ldur            x0, [fp, #-0x18]
    // 0xc0c80c: stur            x1, [fp, #-8]
    // 0xc0c810: StoreField: r1->field_b = r0
    //     0xc0c810: stur            w0, [x1, #0xb]
    // 0xc0c814: r0 = Container()
    //     0xc0c814: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc0c818: stur            x0, [fp, #-0x10]
    // 0xc0c81c: r16 = Instance_Alignment
    //     0xc0c81c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc0c820: ldr             x16, [x16, #0xb10]
    // 0xc0c824: r30 = 44.000000
    //     0xc0c824: add             lr, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xc0c828: ldr             lr, [lr, #0xad8]
    // 0xc0c82c: stp             lr, x16, [SP, #8]
    // 0xc0c830: ldur            x16, [fp, #-8]
    // 0xc0c834: str             x16, [SP]
    // 0xc0c838: mov             x1, x0
    // 0xc0c83c: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x1, child, 0x3, height, 0x2, null]
    //     0xc0c83c: add             x4, PP, #0x52, lsl #12  ; [pp+0x523a0] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x1, "child", 0x3, "height", 0x2, Null]
    //     0xc0c840: ldr             x4, [x4, #0x3a0]
    // 0xc0c844: r0 = Container()
    //     0xc0c844: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc0c848: r0 = Container()
    //     0xc0c848: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc0c84c: stur            x0, [fp, #-8]
    // 0xc0c850: r16 = Instance_AlignmentDirectional
    //     0xc0c850: add             x16, PP, #0x52, lsl #12  ; [pp+0x523a8] Obj!AlignmentDirectional@d5a621
    //     0xc0c854: ldr             x16, [x16, #0x3a8]
    // 0xc0c858: ldur            lr, [fp, #-0x28]
    // 0xc0c85c: stp             lr, x16, [SP, #8]
    // 0xc0c860: ldur            x16, [fp, #-0x10]
    // 0xc0c864: str             x16, [SP]
    // 0xc0c868: mov             x1, x0
    // 0xc0c86c: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x1, child, 0x3, decoration, 0x2, null]
    //     0xc0c86c: add             x4, PP, #0x52, lsl #12  ; [pp+0x523b0] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x1, "child", 0x3, "decoration", 0x2, Null]
    //     0xc0c870: ldr             x4, [x4, #0x3b0]
    // 0xc0c874: r0 = Container()
    //     0xc0c874: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc0c878: r1 = Null
    //     0xc0c878: mov             x1, NULL
    // 0xc0c87c: r2 = 6
    //     0xc0c87c: movz            x2, #0x6
    // 0xc0c880: r0 = AllocateArray()
    //     0xc0c880: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0c884: mov             x2, x0
    // 0xc0c888: ldur            x0, [fp, #-0x20]
    // 0xc0c88c: stur            x2, [fp, #-0x10]
    // 0xc0c890: StoreField: r2->field_f = r0
    //     0xc0c890: stur            w0, [x2, #0xf]
    // 0xc0c894: r16 = Instance_SizedBox
    //     0xc0c894: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xc0c898: ldr             x16, [x16, #0x328]
    // 0xc0c89c: StoreField: r2->field_13 = r16
    //     0xc0c89c: stur            w16, [x2, #0x13]
    // 0xc0c8a0: ldur            x0, [fp, #-8]
    // 0xc0c8a4: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0c8a4: stur            w0, [x2, #0x17]
    // 0xc0c8a8: r1 = <Widget>
    //     0xc0c8a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0c8ac: r0 = AllocateGrowableArray()
    //     0xc0c8ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0c8b0: mov             x1, x0
    // 0xc0c8b4: ldur            x0, [fp, #-0x10]
    // 0xc0c8b8: stur            x1, [fp, #-8]
    // 0xc0c8bc: StoreField: r1->field_f = r0
    //     0xc0c8bc: stur            w0, [x1, #0xf]
    // 0xc0c8c0: r0 = 6
    //     0xc0c8c0: movz            x0, #0x6
    // 0xc0c8c4: StoreField: r1->field_b = r0
    //     0xc0c8c4: stur            w0, [x1, #0xb]
    // 0xc0c8c8: r0 = Column()
    //     0xc0c8c8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0c8cc: mov             x1, x0
    // 0xc0c8d0: r0 = Instance_Axis
    //     0xc0c8d0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0c8d4: stur            x1, [fp, #-0x10]
    // 0xc0c8d8: StoreField: r1->field_f = r0
    //     0xc0c8d8: stur            w0, [x1, #0xf]
    // 0xc0c8dc: r0 = Instance_MainAxisAlignment
    //     0xc0c8dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0c8e0: ldr             x0, [x0, #0xa08]
    // 0xc0c8e4: StoreField: r1->field_13 = r0
    //     0xc0c8e4: stur            w0, [x1, #0x13]
    // 0xc0c8e8: r0 = Instance_MainAxisSize
    //     0xc0c8e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0c8ec: ldr             x0, [x0, #0xa10]
    // 0xc0c8f0: ArrayStore: r1[0] = r0  ; List_4
    //     0xc0c8f0: stur            w0, [x1, #0x17]
    // 0xc0c8f4: r0 = Instance_CrossAxisAlignment
    //     0xc0c8f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc0c8f8: ldr             x0, [x0, #0x890]
    // 0xc0c8fc: StoreField: r1->field_1b = r0
    //     0xc0c8fc: stur            w0, [x1, #0x1b]
    // 0xc0c900: r0 = Instance_VerticalDirection
    //     0xc0c900: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0c904: ldr             x0, [x0, #0xa20]
    // 0xc0c908: StoreField: r1->field_23 = r0
    //     0xc0c908: stur            w0, [x1, #0x23]
    // 0xc0c90c: r0 = Instance_Clip
    //     0xc0c90c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0c910: ldr             x0, [x0, #0x38]
    // 0xc0c914: StoreField: r1->field_2b = r0
    //     0xc0c914: stur            w0, [x1, #0x2b]
    // 0xc0c918: StoreField: r1->field_2f = rZR
    //     0xc0c918: stur            xzr, [x1, #0x2f]
    // 0xc0c91c: ldur            x0, [fp, #-8]
    // 0xc0c920: StoreField: r1->field_b = r0
    //     0xc0c920: stur            w0, [x1, #0xb]
    // 0xc0c924: r0 = Padding()
    //     0xc0c924: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0c928: mov             x1, x0
    // 0xc0c92c: r0 = Instance_EdgeInsets
    //     0xc0c92c: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xc0c930: ldr             x0, [x0, #0x240]
    // 0xc0c934: StoreField: r1->field_f = r0
    //     0xc0c934: stur            w0, [x1, #0xf]
    // 0xc0c938: ldur            x0, [fp, #-0x10]
    // 0xc0c93c: StoreField: r1->field_b = r0
    //     0xc0c93c: stur            w0, [x1, #0xb]
    // 0xc0c940: mov             x0, x1
    // 0xc0c944: LeaveFrame
    //     0xc0c944: mov             SP, fp
    //     0xc0c948: ldp             fp, lr, [SP], #0x10
    // 0xc0c94c: ret
    //     0xc0c94c: ret             
    // 0xc0c950: r16 = "dropdownValue"
    //     0xc0c950: add             x16, PP, #0x52, lsl #12  ; [pp+0x52418] "dropdownValue"
    //     0xc0c954: ldr             x16, [x16, #0x418]
    // 0xc0c958: str             x16, [SP]
    // 0xc0c95c: r0 = _throwLocalNotInitialized()
    //     0xc0c95c: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xc0c960: brk             #0
    // 0xc0c964: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0c964: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0c968: b               #0xc0c340
    // 0xc0c96c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0c96c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0c970: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0c970: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0c974: b               #0xc0c438
    // 0xc0c978: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0c978: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0c97c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc0c97c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc0c980: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0c980: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0c984: b               #0xc0c50c
    // 0xc0c988: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0c988: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0c98c: b               #0xc0c5a0
    // 0xc0c990: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0c990: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0c994: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc0c994: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int?) {
    // ** addr: 0xc0c998, size: 0x84
    // 0xc0c998: EnterFrame
    //     0xc0c998: stp             fp, lr, [SP, #-0x10]!
    //     0xc0c99c: mov             fp, SP
    // 0xc0c9a0: AllocStack(0x10)
    //     0xc0c9a0: sub             SP, SP, #0x10
    // 0xc0c9a4: SetupParameters()
    //     0xc0c9a4: ldr             x0, [fp, #0x18]
    //     0xc0c9a8: ldur            w1, [x0, #0x17]
    //     0xc0c9ac: add             x1, x1, HEAP, lsl #32
    //     0xc0c9b0: stur            x1, [fp, #-8]
    // 0xc0c9b4: CheckStackOverflow
    //     0xc0c9b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0c9b8: cmp             SP, x16
    //     0xc0c9bc: b.ls            #0xc0ca14
    // 0xc0c9c0: r1 = 1
    //     0xc0c9c0: movz            x1, #0x1
    // 0xc0c9c4: r0 = AllocateContext()
    //     0xc0c9c4: bl              #0x16f6108  ; AllocateContextStub
    // 0xc0c9c8: mov             x1, x0
    // 0xc0c9cc: ldur            x0, [fp, #-8]
    // 0xc0c9d0: StoreField: r1->field_b = r0
    //     0xc0c9d0: stur            w0, [x1, #0xb]
    // 0xc0c9d4: ldr             x2, [fp, #0x10]
    // 0xc0c9d8: StoreField: r1->field_f = r2
    //     0xc0c9d8: stur            w2, [x1, #0xf]
    // 0xc0c9dc: LoadField: r3 = r0->field_f
    //     0xc0c9dc: ldur            w3, [x0, #0xf]
    // 0xc0c9e0: DecompressPointer r3
    //     0xc0c9e0: add             x3, x3, HEAP, lsl #32
    // 0xc0c9e4: mov             x2, x1
    // 0xc0c9e8: stur            x3, [fp, #-0x10]
    // 0xc0c9ec: r1 = Function '<anonymous closure>':.
    //     0xc0c9ec: add             x1, PP, #0x52, lsl #12  ; [pp+0x52420] AnonymousClosure: (0xc0ca1c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/select_quantity_picker.dart] _SelectQuantityPickerState::build (0xc0c320)
    //     0xc0c9f0: ldr             x1, [x1, #0x420]
    // 0xc0c9f4: r0 = AllocateClosure()
    //     0xc0c9f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0c9f8: ldur            x1, [fp, #-0x10]
    // 0xc0c9fc: mov             x2, x0
    // 0xc0ca00: r0 = setState()
    //     0xc0ca00: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xc0ca04: r0 = Null
    //     0xc0ca04: mov             x0, NULL
    // 0xc0ca08: LeaveFrame
    //     0xc0ca08: mov             SP, fp
    //     0xc0ca0c: ldp             fp, lr, [SP], #0x10
    // 0xc0ca10: ret
    //     0xc0ca10: ret             
    // 0xc0ca14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0ca14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0ca18: b               #0xc0c9c0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc0ca1c, size: 0x138
    // 0xc0ca1c: EnterFrame
    //     0xc0ca1c: stp             fp, lr, [SP, #-0x10]!
    //     0xc0ca20: mov             fp, SP
    // 0xc0ca24: AllocStack(0x18)
    //     0xc0ca24: sub             SP, SP, #0x18
    // 0xc0ca28: SetupParameters()
    //     0xc0ca28: ldr             x0, [fp, #0x10]
    //     0xc0ca2c: ldur            w1, [x0, #0x17]
    //     0xc0ca30: add             x1, x1, HEAP, lsl #32
    // 0xc0ca34: CheckStackOverflow
    //     0xc0ca34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0ca38: cmp             SP, x16
    //     0xc0ca3c: b.ls            #0xc0cb3c
    // 0xc0ca40: LoadField: r2 = r1->field_f
    //     0xc0ca40: ldur            w2, [x1, #0xf]
    // 0xc0ca44: DecompressPointer r2
    //     0xc0ca44: add             x2, x2, HEAP, lsl #32
    // 0xc0ca48: cmp             w2, NULL
    // 0xc0ca4c: b.eq            #0xc0cb44
    // 0xc0ca50: LoadField: r3 = r1->field_b
    //     0xc0ca50: ldur            w3, [x1, #0xb]
    // 0xc0ca54: DecompressPointer r3
    //     0xc0ca54: add             x3, x3, HEAP, lsl #32
    // 0xc0ca58: mov             x0, x2
    // 0xc0ca5c: ArrayStore: r3[0] = r0  ; List_4
    //     0xc0ca5c: stur            w0, [x3, #0x17]
    //     0xc0ca60: tbz             w0, #0, #0xc0ca7c
    //     0xc0ca64: ldurb           w16, [x3, #-1]
    //     0xc0ca68: ldurb           w17, [x0, #-1]
    //     0xc0ca6c: and             x16, x17, x16, lsr #2
    //     0xc0ca70: tst             x16, HEAP, lsr #32
    //     0xc0ca74: b.eq            #0xc0ca7c
    //     0xc0ca78: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xc0ca7c: LoadField: r0 = r3->field_f
    //     0xc0ca7c: ldur            w0, [x3, #0xf]
    // 0xc0ca80: DecompressPointer r0
    //     0xc0ca80: add             x0, x0, HEAP, lsl #32
    // 0xc0ca84: LoadField: r1 = r0->field_b
    //     0xc0ca84: ldur            w1, [x0, #0xb]
    // 0xc0ca88: DecompressPointer r1
    //     0xc0ca88: add             x1, x1, HEAP, lsl #32
    // 0xc0ca8c: cmp             w1, NULL
    // 0xc0ca90: b.eq            #0xc0cb48
    // 0xc0ca94: LoadField: r0 = r3->field_1b
    //     0xc0ca94: ldur            w0, [x3, #0x1b]
    // 0xc0ca98: DecompressPointer r0
    //     0xc0ca98: add             x0, x0, HEAP, lsl #32
    // 0xc0ca9c: LoadField: r3 = r0->field_b
    //     0xc0ca9c: ldur            w3, [x0, #0xb]
    // 0xc0caa0: r4 = LoadInt32Instr(r3)
    //     0xc0caa0: sbfx            x4, x3, #1, #0x1f
    // 0xc0caa4: LoadField: r3 = r0->field_f
    //     0xc0caa4: ldur            w3, [x0, #0xf]
    // 0xc0caa8: DecompressPointer r3
    //     0xc0caa8: add             x3, x3, HEAP, lsl #32
    // 0xc0caac: r0 = 0
    //     0xc0caac: movz            x0, #0
    // 0xc0cab0: CheckStackOverflow
    //     0xc0cab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0cab4: cmp             SP, x16
    //     0xc0cab8: b.ls            #0xc0cb4c
    // 0xc0cabc: cmp             x0, x4
    // 0xc0cac0: b.ge            #0xc0cafc
    // 0xc0cac4: ArrayLoad: r5 = r3[r0]  ; Unknown_4
    //     0xc0cac4: add             x16, x3, x0, lsl #2
    //     0xc0cac8: ldur            w5, [x16, #0xf]
    // 0xc0cacc: DecompressPointer r5
    //     0xc0cacc: add             x5, x5, HEAP, lsl #32
    // 0xc0cad0: r6 = LoadInt32Instr(r2)
    //     0xc0cad0: sbfx            x6, x2, #1, #0x1f
    //     0xc0cad4: tbz             w2, #0, #0xc0cadc
    //     0xc0cad8: ldur            x6, [x2, #7]
    // 0xc0cadc: r7 = LoadInt32Instr(r5)
    //     0xc0cadc: sbfx            x7, x5, #1, #0x1f
    //     0xc0cae0: tbz             w5, #0, #0xc0cae8
    //     0xc0cae4: ldur            x7, [x5, #7]
    // 0xc0cae8: cmp             x7, x6
    // 0xc0caec: b.eq            #0xc0cb00
    // 0xc0caf0: add             x5, x0, #1
    // 0xc0caf4: mov             x0, x5
    // 0xc0caf8: b               #0xc0cab0
    // 0xc0cafc: r0 = -1
    //     0xc0cafc: movn            x0, #0
    // 0xc0cb00: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xc0cb00: ldur            w3, [x1, #0x17]
    // 0xc0cb04: DecompressPointer r3
    //     0xc0cb04: add             x3, x3, HEAP, lsl #32
    // 0xc0cb08: lsl             x1, x0, #1
    // 0xc0cb0c: stp             x2, x3, [SP, #8]
    // 0xc0cb10: str             x1, [SP]
    // 0xc0cb14: r4 = 0
    //     0xc0cb14: movz            x4, #0
    // 0xc0cb18: ldr             x0, [SP, #0x10]
    // 0xc0cb1c: r16 = UnlinkedCall_0x613b5c
    //     0xc0cb1c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52428] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc0cb20: add             x16, x16, #0x428
    // 0xc0cb24: ldp             x5, lr, [x16]
    // 0xc0cb28: blr             lr
    // 0xc0cb2c: r0 = Null
    //     0xc0cb2c: mov             x0, NULL
    // 0xc0cb30: LeaveFrame
    //     0xc0cb30: mov             SP, fp
    //     0xc0cb34: ldp             fp, lr, [SP], #0x10
    // 0xc0cb38: ret
    //     0xc0cb38: ret             
    // 0xc0cb3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0cb3c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0cb40: b               #0xc0ca40
    // 0xc0cb44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0cb44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0cb48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0cb48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0cb4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0cb4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0cb50: b               #0xc0cabc
  }
}

// class id: 3963, size: 0x1c, field offset: 0xc
class SelectQuantityPicker extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc8124c, size: 0x50
    // 0xc8124c: EnterFrame
    //     0xc8124c: stp             fp, lr, [SP, #-0x10]!
    //     0xc81250: mov             fp, SP
    // 0xc81254: CheckStackOverflow
    //     0xc81254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc81258: cmp             SP, x16
    //     0xc8125c: b.ls            #0xc81294
    // 0xc81260: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xc81260: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc81264: ldr             x0, [x0]
    //     0xc81268: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc8126c: cmp             w0, w16
    //     0xc81270: b.ne            #0xc8127c
    //     0xc81274: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xc81278: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc8127c: r1 = <SelectQuantityPicker>
    //     0xc8127c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48278] TypeArguments: <SelectQuantityPicker>
    //     0xc81280: ldr             x1, [x1, #0x278]
    // 0xc81284: r0 = _SelectQuantityPickerState()
    //     0xc81284: bl              #0xc8129c  ; Allocate_SelectQuantityPickerStateStub -> _SelectQuantityPickerState (size=0x14)
    // 0xc81288: LeaveFrame
    //     0xc81288: mov             SP, fp
    //     0xc8128c: ldp             fp, lr, [SP], #0x10
    // 0xc81290: ret
    //     0xc81290: ret             
    // 0xc81294: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc81294: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc81298: b               #0xc81260
  }
}
