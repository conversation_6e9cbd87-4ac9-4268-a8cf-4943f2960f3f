// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart

// class id: 1049445, size: 0x8
class :: {
}

// class id: 4554, size: 0x14, field offset: 0x14
//   const constructor, 
class ReviewListWidget extends BaseView<dynamic> {

  [closure] SizedBox <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa928e0, size: 0xc
    // 0xa928e0: r0 = Instance_SizedBox
    //     0xa928e0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xa928e4: ldr             x0, [x0, #0x8f0]
    // 0xa928e8: ret
    //     0xa928e8: ret             
  }
  [closure] StatelessWidget <anonymous closure>(dynamic) {
    // ** addr: 0xa928ec, size: 0x2a3c
    // 0xa928ec: EnterFrame
    //     0xa928ec: stp             fp, lr, [SP, #-0x10]!
    //     0xa928f0: mov             fp, SP
    // 0xa928f4: AllocStack(0x88)
    //     0xa928f4: sub             SP, SP, #0x88
    // 0xa928f8: SetupParameters()
    //     0xa928f8: ldr             x0, [fp, #0x10]
    //     0xa928fc: ldur            w2, [x0, #0x17]
    //     0xa92900: add             x2, x2, HEAP, lsl #32
    //     0xa92904: stur            x2, [fp, #-8]
    // 0xa92908: CheckStackOverflow
    //     0xa92908: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9290c: cmp             SP, x16
    //     0xa92910: b.ls            #0xa95320
    // 0xa92914: LoadField: r1 = r2->field_f
    //     0xa92914: ldur            w1, [x2, #0xf]
    // 0xa92918: DecompressPointer r1
    //     0xa92918: add             x1, x1, HEAP, lsl #32
    // 0xa9291c: r0 = controller()
    //     0xa9291c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa92920: LoadField: r1 = r0->field_7b
    //     0xa92920: ldur            w1, [x0, #0x7b]
    // 0xa92924: DecompressPointer r1
    //     0xa92924: add             x1, x1, HEAP, lsl #32
    // 0xa92928: r0 = value()
    //     0xa92928: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa9292c: LoadField: r1 = r0->field_b
    //     0xa9292c: ldur            w1, [x0, #0xb]
    // 0xa92930: DecompressPointer r1
    //     0xa92930: add             x1, x1, HEAP, lsl #32
    // 0xa92934: cmp             w1, NULL
    // 0xa92938: b.ne            #0xa92958
    // 0xa9293c: r0 = Container()
    //     0xa9293c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa92940: mov             x1, x0
    // 0xa92944: stur            x0, [fp, #-0x10]
    // 0xa92948: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa92948: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa9294c: r0 = Container()
    //     0xa9294c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa92950: ldur            x0, [fp, #-0x10]
    // 0xa92954: b               #0xa95314
    // 0xa92958: ldur            x2, [fp, #-8]
    // 0xa9295c: LoadField: r1 = r2->field_f
    //     0xa9295c: ldur            w1, [x2, #0xf]
    // 0xa92960: DecompressPointer r1
    //     0xa92960: add             x1, x1, HEAP, lsl #32
    // 0xa92964: r0 = controller()
    //     0xa92964: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa92968: LoadField: r1 = r0->field_7b
    //     0xa92968: ldur            w1, [x0, #0x7b]
    // 0xa9296c: DecompressPointer r1
    //     0xa9296c: add             x1, x1, HEAP, lsl #32
    // 0xa92970: r0 = value()
    //     0xa92970: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa92974: LoadField: r1 = r0->field_b
    //     0xa92974: ldur            w1, [x0, #0xb]
    // 0xa92978: DecompressPointer r1
    //     0xa92978: add             x1, x1, HEAP, lsl #32
    // 0xa9297c: cmp             w1, NULL
    // 0xa92980: b.ne            #0xa9298c
    // 0xa92984: r0 = Null
    //     0xa92984: mov             x0, NULL
    // 0xa92988: b               #0xa929b0
    // 0xa9298c: LoadField: r0 = r1->field_f
    //     0xa9298c: ldur            w0, [x1, #0xf]
    // 0xa92990: DecompressPointer r0
    //     0xa92990: add             x0, x0, HEAP, lsl #32
    // 0xa92994: cmp             w0, NULL
    // 0xa92998: b.ne            #0xa929a4
    // 0xa9299c: r0 = Null
    //     0xa9299c: mov             x0, NULL
    // 0xa929a0: b               #0xa929b0
    // 0xa929a4: LoadField: r1 = r0->field_f
    //     0xa929a4: ldur            w1, [x0, #0xf]
    // 0xa929a8: DecompressPointer r1
    //     0xa929a8: add             x1, x1, HEAP, lsl #32
    // 0xa929ac: mov             x0, x1
    // 0xa929b0: ldur            x2, [fp, #-8]
    // 0xa929b4: r1 = LoadClassIdInstr(r0)
    //     0xa929b4: ldur            x1, [x0, #-1]
    //     0xa929b8: ubfx            x1, x1, #0xc, #0x14
    // 0xa929bc: stp             xzr, x0, [SP]
    // 0xa929c0: mov             x0, x1
    // 0xa929c4: mov             lr, x0
    // 0xa929c8: ldr             lr, [x21, lr, lsl #3]
    // 0xa929cc: blr             lr
    // 0xa929d0: eor             x2, x0, #0x10
    // 0xa929d4: ldur            x0, [fp, #-8]
    // 0xa929d8: stur            x2, [fp, #-0x10]
    // 0xa929dc: LoadField: r1 = r0->field_13
    //     0xa929dc: ldur            w1, [x0, #0x13]
    // 0xa929e0: DecompressPointer r1
    //     0xa929e0: add             x1, x1, HEAP, lsl #32
    // 0xa929e4: r0 = of()
    //     0xa929e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa929e8: LoadField: r1 = r0->field_5b
    //     0xa929e8: ldur            w1, [x0, #0x5b]
    // 0xa929ec: DecompressPointer r1
    //     0xa929ec: add             x1, x1, HEAP, lsl #32
    // 0xa929f0: r0 = LoadClassIdInstr(r1)
    //     0xa929f0: ldur            x0, [x1, #-1]
    //     0xa929f4: ubfx            x0, x0, #0xc, #0x14
    // 0xa929f8: d0 = 0.200000
    //     0xa929f8: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xa929fc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa929fc: sub             lr, x0, #0xffa
    //     0xa92a00: ldr             lr, [x21, lr, lsl #3]
    //     0xa92a04: blr             lr
    // 0xa92a08: mov             x2, x0
    // 0xa92a0c: r1 = Null
    //     0xa92a0c: mov             x1, NULL
    // 0xa92a10: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa92a10: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa92a14: r0 = Border.all()
    //     0xa92a14: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa92a18: stur            x0, [fp, #-0x18]
    // 0xa92a1c: r0 = Radius()
    //     0xa92a1c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa92a20: d0 = 15.000000
    //     0xa92a20: fmov            d0, #15.00000000
    // 0xa92a24: stur            x0, [fp, #-0x20]
    // 0xa92a28: StoreField: r0->field_7 = d0
    //     0xa92a28: stur            d0, [x0, #7]
    // 0xa92a2c: StoreField: r0->field_f = d0
    //     0xa92a2c: stur            d0, [x0, #0xf]
    // 0xa92a30: r0 = BorderRadius()
    //     0xa92a30: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa92a34: mov             x1, x0
    // 0xa92a38: ldur            x0, [fp, #-0x20]
    // 0xa92a3c: stur            x1, [fp, #-0x28]
    // 0xa92a40: StoreField: r1->field_7 = r0
    //     0xa92a40: stur            w0, [x1, #7]
    // 0xa92a44: StoreField: r1->field_b = r0
    //     0xa92a44: stur            w0, [x1, #0xb]
    // 0xa92a48: StoreField: r1->field_f = r0
    //     0xa92a48: stur            w0, [x1, #0xf]
    // 0xa92a4c: StoreField: r1->field_13 = r0
    //     0xa92a4c: stur            w0, [x1, #0x13]
    // 0xa92a50: r0 = BoxDecoration()
    //     0xa92a50: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa92a54: mov             x2, x0
    // 0xa92a58: ldur            x0, [fp, #-0x18]
    // 0xa92a5c: stur            x2, [fp, #-0x20]
    // 0xa92a60: StoreField: r2->field_f = r0
    //     0xa92a60: stur            w0, [x2, #0xf]
    // 0xa92a64: ldur            x0, [fp, #-0x28]
    // 0xa92a68: StoreField: r2->field_13 = r0
    //     0xa92a68: stur            w0, [x2, #0x13]
    // 0xa92a6c: r0 = Instance_BoxShape
    //     0xa92a6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa92a70: ldr             x0, [x0, #0x80]
    // 0xa92a74: StoreField: r2->field_23 = r0
    //     0xa92a74: stur            w0, [x2, #0x23]
    // 0xa92a78: ldur            x0, [fp, #-8]
    // 0xa92a7c: LoadField: r1 = r0->field_f
    //     0xa92a7c: ldur            w1, [x0, #0xf]
    // 0xa92a80: DecompressPointer r1
    //     0xa92a80: add             x1, x1, HEAP, lsl #32
    // 0xa92a84: r0 = controller()
    //     0xa92a84: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa92a88: LoadField: r1 = r0->field_7b
    //     0xa92a88: ldur            w1, [x0, #0x7b]
    // 0xa92a8c: DecompressPointer r1
    //     0xa92a8c: add             x1, x1, HEAP, lsl #32
    // 0xa92a90: r0 = value()
    //     0xa92a90: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa92a94: LoadField: r1 = r0->field_b
    //     0xa92a94: ldur            w1, [x0, #0xb]
    // 0xa92a98: DecompressPointer r1
    //     0xa92a98: add             x1, x1, HEAP, lsl #32
    // 0xa92a9c: cmp             w1, NULL
    // 0xa92aa0: b.ne            #0xa92aac
    // 0xa92aa4: r0 = Null
    //     0xa92aa4: mov             x0, NULL
    // 0xa92aa8: b               #0xa92aec
    // 0xa92aac: LoadField: r0 = r1->field_f
    //     0xa92aac: ldur            w0, [x1, #0xf]
    // 0xa92ab0: DecompressPointer r0
    //     0xa92ab0: add             x0, x0, HEAP, lsl #32
    // 0xa92ab4: cmp             w0, NULL
    // 0xa92ab8: b.ne            #0xa92ac4
    // 0xa92abc: r0 = Null
    //     0xa92abc: mov             x0, NULL
    // 0xa92ac0: b               #0xa92aec
    // 0xa92ac4: LoadField: r1 = r0->field_f
    //     0xa92ac4: ldur            w1, [x0, #0xf]
    // 0xa92ac8: DecompressPointer r1
    //     0xa92ac8: add             x1, x1, HEAP, lsl #32
    // 0xa92acc: r0 = LoadClassIdInstr(r1)
    //     0xa92acc: ldur            x0, [x1, #-1]
    //     0xa92ad0: ubfx            x0, x0, #0xc, #0x14
    // 0xa92ad4: str             x1, [SP]
    // 0xa92ad8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xa92ad8: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xa92adc: r0 = GDT[cid_x0 + 0x2700]()
    //     0xa92adc: movz            x17, #0x2700
    //     0xa92ae0: add             lr, x0, x17
    //     0xa92ae4: ldr             lr, [x21, lr, lsl #3]
    //     0xa92ae8: blr             lr
    // 0xa92aec: cmp             w0, NULL
    // 0xa92af0: b.ne            #0xa92af8
    // 0xa92af4: r0 = ""
    //     0xa92af4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa92af8: ldur            x2, [fp, #-8]
    // 0xa92afc: stur            x0, [fp, #-0x18]
    // 0xa92b00: LoadField: r1 = r2->field_13
    //     0xa92b00: ldur            w1, [x2, #0x13]
    // 0xa92b04: DecompressPointer r1
    //     0xa92b04: add             x1, x1, HEAP, lsl #32
    // 0xa92b08: r0 = of()
    //     0xa92b08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa92b0c: LoadField: r1 = r0->field_87
    //     0xa92b0c: ldur            w1, [x0, #0x87]
    // 0xa92b10: DecompressPointer r1
    //     0xa92b10: add             x1, x1, HEAP, lsl #32
    // 0xa92b14: LoadField: r0 = r1->field_23
    //     0xa92b14: ldur            w0, [x1, #0x23]
    // 0xa92b18: DecompressPointer r0
    //     0xa92b18: add             x0, x0, HEAP, lsl #32
    // 0xa92b1c: r16 = Instance_Color
    //     0xa92b1c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa92b20: r30 = 32.000000
    //     0xa92b20: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xa92b24: ldr             lr, [lr, #0x848]
    // 0xa92b28: stp             lr, x16, [SP]
    // 0xa92b2c: mov             x1, x0
    // 0xa92b30: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa92b30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa92b34: ldr             x4, [x4, #0x9b8]
    // 0xa92b38: r0 = copyWith()
    //     0xa92b38: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa92b3c: stur            x0, [fp, #-0x28]
    // 0xa92b40: r0 = Text()
    //     0xa92b40: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa92b44: mov             x1, x0
    // 0xa92b48: ldur            x0, [fp, #-0x18]
    // 0xa92b4c: stur            x1, [fp, #-0x30]
    // 0xa92b50: StoreField: r1->field_b = r0
    //     0xa92b50: stur            w0, [x1, #0xb]
    // 0xa92b54: ldur            x0, [fp, #-0x28]
    // 0xa92b58: StoreField: r1->field_13 = r0
    //     0xa92b58: stur            w0, [x1, #0x13]
    // 0xa92b5c: r0 = Padding()
    //     0xa92b5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa92b60: mov             x2, x0
    // 0xa92b64: r0 = Instance_EdgeInsets
    //     0xa92b64: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f850] Obj!EdgeInsets@d57801
    //     0xa92b68: ldr             x0, [x0, #0x850]
    // 0xa92b6c: stur            x2, [fp, #-0x18]
    // 0xa92b70: StoreField: r2->field_f = r0
    //     0xa92b70: stur            w0, [x2, #0xf]
    // 0xa92b74: ldur            x0, [fp, #-0x30]
    // 0xa92b78: StoreField: r2->field_b = r0
    //     0xa92b78: stur            w0, [x2, #0xb]
    // 0xa92b7c: ldur            x0, [fp, #-8]
    // 0xa92b80: LoadField: r1 = r0->field_f
    //     0xa92b80: ldur            w1, [x0, #0xf]
    // 0xa92b84: DecompressPointer r1
    //     0xa92b84: add             x1, x1, HEAP, lsl #32
    // 0xa92b88: r0 = controller()
    //     0xa92b88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa92b8c: LoadField: r1 = r0->field_7b
    //     0xa92b8c: ldur            w1, [x0, #0x7b]
    // 0xa92b90: DecompressPointer r1
    //     0xa92b90: add             x1, x1, HEAP, lsl #32
    // 0xa92b94: r0 = value()
    //     0xa92b94: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa92b98: LoadField: r1 = r0->field_b
    //     0xa92b98: ldur            w1, [x0, #0xb]
    // 0xa92b9c: DecompressPointer r1
    //     0xa92b9c: add             x1, x1, HEAP, lsl #32
    // 0xa92ba0: cmp             w1, NULL
    // 0xa92ba4: b.ne            #0xa92bb0
    // 0xa92ba8: r0 = Null
    //     0xa92ba8: mov             x0, NULL
    // 0xa92bac: b               #0xa92bd4
    // 0xa92bb0: LoadField: r0 = r1->field_f
    //     0xa92bb0: ldur            w0, [x1, #0xf]
    // 0xa92bb4: DecompressPointer r0
    //     0xa92bb4: add             x0, x0, HEAP, lsl #32
    // 0xa92bb8: cmp             w0, NULL
    // 0xa92bbc: b.ne            #0xa92bc8
    // 0xa92bc0: r0 = Null
    //     0xa92bc0: mov             x0, NULL
    // 0xa92bc4: b               #0xa92bd4
    // 0xa92bc8: LoadField: r1 = r0->field_f
    //     0xa92bc8: ldur            w1, [x0, #0xf]
    // 0xa92bcc: DecompressPointer r1
    //     0xa92bcc: add             x1, x1, HEAP, lsl #32
    // 0xa92bd0: mov             x0, x1
    // 0xa92bd4: cmp             w0, NULL
    // 0xa92bd8: b.ne            #0xa92be4
    // 0xa92bdc: d1 = 0.000000
    //     0xa92bdc: eor             v1.16b, v1.16b, v1.16b
    // 0xa92be0: b               #0xa92bec
    // 0xa92be4: LoadField: d0 = r0->field_7
    //     0xa92be4: ldur            d0, [x0, #7]
    // 0xa92be8: mov             v1.16b, v0.16b
    // 0xa92bec: d0 = 4.000000
    //     0xa92bec: fmov            d0, #4.00000000
    // 0xa92bf0: fcmp            d1, d0
    // 0xa92bf4: b.lt            #0xa92c04
    // 0xa92bf8: r1 = Instance_Color
    //     0xa92bf8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa92bfc: ldr             x1, [x1, #0x858]
    // 0xa92c00: b               #0xa92d2c
    // 0xa92c04: ldur            x2, [fp, #-8]
    // 0xa92c08: LoadField: r1 = r2->field_f
    //     0xa92c08: ldur            w1, [x2, #0xf]
    // 0xa92c0c: DecompressPointer r1
    //     0xa92c0c: add             x1, x1, HEAP, lsl #32
    // 0xa92c10: r0 = controller()
    //     0xa92c10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa92c14: LoadField: r1 = r0->field_7b
    //     0xa92c14: ldur            w1, [x0, #0x7b]
    // 0xa92c18: DecompressPointer r1
    //     0xa92c18: add             x1, x1, HEAP, lsl #32
    // 0xa92c1c: r0 = value()
    //     0xa92c1c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa92c20: LoadField: r1 = r0->field_b
    //     0xa92c20: ldur            w1, [x0, #0xb]
    // 0xa92c24: DecompressPointer r1
    //     0xa92c24: add             x1, x1, HEAP, lsl #32
    // 0xa92c28: cmp             w1, NULL
    // 0xa92c2c: b.ne            #0xa92c38
    // 0xa92c30: r0 = Null
    //     0xa92c30: mov             x0, NULL
    // 0xa92c34: b               #0xa92c5c
    // 0xa92c38: LoadField: r0 = r1->field_f
    //     0xa92c38: ldur            w0, [x1, #0xf]
    // 0xa92c3c: DecompressPointer r0
    //     0xa92c3c: add             x0, x0, HEAP, lsl #32
    // 0xa92c40: cmp             w0, NULL
    // 0xa92c44: b.ne            #0xa92c50
    // 0xa92c48: r0 = Null
    //     0xa92c48: mov             x0, NULL
    // 0xa92c4c: b               #0xa92c5c
    // 0xa92c50: LoadField: r1 = r0->field_f
    //     0xa92c50: ldur            w1, [x0, #0xf]
    // 0xa92c54: DecompressPointer r1
    //     0xa92c54: add             x1, x1, HEAP, lsl #32
    // 0xa92c58: mov             x0, x1
    // 0xa92c5c: cmp             w0, NULL
    // 0xa92c60: b.ne            #0xa92c6c
    // 0xa92c64: d1 = 0.000000
    //     0xa92c64: eor             v1.16b, v1.16b, v1.16b
    // 0xa92c68: b               #0xa92c74
    // 0xa92c6c: LoadField: d0 = r0->field_7
    //     0xa92c6c: ldur            d0, [x0, #7]
    // 0xa92c70: mov             v1.16b, v0.16b
    // 0xa92c74: d0 = 3.500000
    //     0xa92c74: fmov            d0, #3.50000000
    // 0xa92c78: fcmp            d1, d0
    // 0xa92c7c: b.lt            #0xa92c98
    // 0xa92c80: r1 = Instance_Color
    //     0xa92c80: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa92c84: ldr             x1, [x1, #0x858]
    // 0xa92c88: d0 = 0.700000
    //     0xa92c88: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa92c8c: ldr             d0, [x17, #0xf48]
    // 0xa92c90: r0 = withOpacity()
    //     0xa92c90: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa92c94: b               #0xa92d28
    // 0xa92c98: ldur            x2, [fp, #-8]
    // 0xa92c9c: LoadField: r1 = r2->field_f
    //     0xa92c9c: ldur            w1, [x2, #0xf]
    // 0xa92ca0: DecompressPointer r1
    //     0xa92ca0: add             x1, x1, HEAP, lsl #32
    // 0xa92ca4: r0 = controller()
    //     0xa92ca4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa92ca8: LoadField: r1 = r0->field_7b
    //     0xa92ca8: ldur            w1, [x0, #0x7b]
    // 0xa92cac: DecompressPointer r1
    //     0xa92cac: add             x1, x1, HEAP, lsl #32
    // 0xa92cb0: r0 = value()
    //     0xa92cb0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa92cb4: LoadField: r1 = r0->field_b
    //     0xa92cb4: ldur            w1, [x0, #0xb]
    // 0xa92cb8: DecompressPointer r1
    //     0xa92cb8: add             x1, x1, HEAP, lsl #32
    // 0xa92cbc: cmp             w1, NULL
    // 0xa92cc0: b.ne            #0xa92ccc
    // 0xa92cc4: r0 = Null
    //     0xa92cc4: mov             x0, NULL
    // 0xa92cc8: b               #0xa92cf0
    // 0xa92ccc: LoadField: r0 = r1->field_f
    //     0xa92ccc: ldur            w0, [x1, #0xf]
    // 0xa92cd0: DecompressPointer r0
    //     0xa92cd0: add             x0, x0, HEAP, lsl #32
    // 0xa92cd4: cmp             w0, NULL
    // 0xa92cd8: b.ne            #0xa92ce4
    // 0xa92cdc: r0 = Null
    //     0xa92cdc: mov             x0, NULL
    // 0xa92ce0: b               #0xa92cf0
    // 0xa92ce4: LoadField: r1 = r0->field_f
    //     0xa92ce4: ldur            w1, [x0, #0xf]
    // 0xa92ce8: DecompressPointer r1
    //     0xa92ce8: add             x1, x1, HEAP, lsl #32
    // 0xa92cec: mov             x0, x1
    // 0xa92cf0: cmp             w0, NULL
    // 0xa92cf4: b.ne            #0xa92d00
    // 0xa92cf8: d1 = 0.000000
    //     0xa92cf8: eor             v1.16b, v1.16b, v1.16b
    // 0xa92cfc: b               #0xa92d08
    // 0xa92d00: LoadField: d0 = r0->field_7
    //     0xa92d00: ldur            d0, [x0, #7]
    // 0xa92d04: mov             v1.16b, v0.16b
    // 0xa92d08: d0 = 2.000000
    //     0xa92d08: fmov            d0, #2.00000000
    // 0xa92d0c: fcmp            d1, d0
    // 0xa92d10: b.lt            #0xa92d20
    // 0xa92d14: r0 = Instance_Color
    //     0xa92d14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xa92d18: ldr             x0, [x0, #0x860]
    // 0xa92d1c: b               #0xa92d28
    // 0xa92d20: r0 = Instance_Color
    //     0xa92d20: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xa92d24: ldr             x0, [x0, #0x50]
    // 0xa92d28: mov             x1, x0
    // 0xa92d2c: ldur            x2, [fp, #-8]
    // 0xa92d30: ldur            x0, [fp, #-0x18]
    // 0xa92d34: stur            x1, [fp, #-0x28]
    // 0xa92d38: r0 = ColorFilter()
    //     0xa92d38: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xa92d3c: mov             x1, x0
    // 0xa92d40: ldur            x0, [fp, #-0x28]
    // 0xa92d44: stur            x1, [fp, #-0x30]
    // 0xa92d48: StoreField: r1->field_7 = r0
    //     0xa92d48: stur            w0, [x1, #7]
    // 0xa92d4c: r0 = Instance_BlendMode
    //     0xa92d4c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa92d50: ldr             x0, [x0, #0xb30]
    // 0xa92d54: StoreField: r1->field_b = r0
    //     0xa92d54: stur            w0, [x1, #0xb]
    // 0xa92d58: r0 = 1
    //     0xa92d58: movz            x0, #0x1
    // 0xa92d5c: StoreField: r1->field_13 = r0
    //     0xa92d5c: stur            x0, [x1, #0x13]
    // 0xa92d60: r0 = SvgPicture()
    //     0xa92d60: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa92d64: stur            x0, [fp, #-0x28]
    // 0xa92d68: ldur            x16, [fp, #-0x30]
    // 0xa92d6c: str             x16, [SP]
    // 0xa92d70: mov             x1, x0
    // 0xa92d74: r2 = "assets/images/big_green_star.svg"
    //     0xa92d74: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f868] "assets/images/big_green_star.svg"
    //     0xa92d78: ldr             x2, [x2, #0x868]
    // 0xa92d7c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xa92d7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xa92d80: ldr             x4, [x4, #0xa38]
    // 0xa92d84: r0 = SvgPicture.asset()
    //     0xa92d84: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa92d88: r1 = Null
    //     0xa92d88: mov             x1, NULL
    // 0xa92d8c: r2 = 4
    //     0xa92d8c: movz            x2, #0x4
    // 0xa92d90: r0 = AllocateArray()
    //     0xa92d90: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa92d94: mov             x2, x0
    // 0xa92d98: ldur            x0, [fp, #-0x18]
    // 0xa92d9c: stur            x2, [fp, #-0x30]
    // 0xa92da0: StoreField: r2->field_f = r0
    //     0xa92da0: stur            w0, [x2, #0xf]
    // 0xa92da4: ldur            x0, [fp, #-0x28]
    // 0xa92da8: StoreField: r2->field_13 = r0
    //     0xa92da8: stur            w0, [x2, #0x13]
    // 0xa92dac: r1 = <Widget>
    //     0xa92dac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa92db0: r0 = AllocateGrowableArray()
    //     0xa92db0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa92db4: mov             x1, x0
    // 0xa92db8: ldur            x0, [fp, #-0x30]
    // 0xa92dbc: stur            x1, [fp, #-0x18]
    // 0xa92dc0: StoreField: r1->field_f = r0
    //     0xa92dc0: stur            w0, [x1, #0xf]
    // 0xa92dc4: r2 = 4
    //     0xa92dc4: movz            x2, #0x4
    // 0xa92dc8: StoreField: r1->field_b = r2
    //     0xa92dc8: stur            w2, [x1, #0xb]
    // 0xa92dcc: r0 = Row()
    //     0xa92dcc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa92dd0: mov             x3, x0
    // 0xa92dd4: r0 = Instance_Axis
    //     0xa92dd4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa92dd8: stur            x3, [fp, #-0x28]
    // 0xa92ddc: StoreField: r3->field_f = r0
    //     0xa92ddc: stur            w0, [x3, #0xf]
    // 0xa92de0: r4 = Instance_MainAxisAlignment
    //     0xa92de0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa92de4: ldr             x4, [x4, #0xa08]
    // 0xa92de8: StoreField: r3->field_13 = r4
    //     0xa92de8: stur            w4, [x3, #0x13]
    // 0xa92dec: r5 = Instance_MainAxisSize
    //     0xa92dec: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa92df0: ldr             x5, [x5, #0xa10]
    // 0xa92df4: ArrayStore: r3[0] = r5  ; List_4
    //     0xa92df4: stur            w5, [x3, #0x17]
    // 0xa92df8: r6 = Instance_CrossAxisAlignment
    //     0xa92df8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa92dfc: ldr             x6, [x6, #0xa18]
    // 0xa92e00: StoreField: r3->field_1b = r6
    //     0xa92e00: stur            w6, [x3, #0x1b]
    // 0xa92e04: r7 = Instance_VerticalDirection
    //     0xa92e04: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa92e08: ldr             x7, [x7, #0xa20]
    // 0xa92e0c: StoreField: r3->field_23 = r7
    //     0xa92e0c: stur            w7, [x3, #0x23]
    // 0xa92e10: r8 = Instance_Clip
    //     0xa92e10: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa92e14: ldr             x8, [x8, #0x38]
    // 0xa92e18: StoreField: r3->field_2b = r8
    //     0xa92e18: stur            w8, [x3, #0x2b]
    // 0xa92e1c: StoreField: r3->field_2f = rZR
    //     0xa92e1c: stur            xzr, [x3, #0x2f]
    // 0xa92e20: ldur            x1, [fp, #-0x18]
    // 0xa92e24: StoreField: r3->field_b = r1
    //     0xa92e24: stur            w1, [x3, #0xb]
    // 0xa92e28: r1 = Null
    //     0xa92e28: mov             x1, NULL
    // 0xa92e2c: r2 = 2
    //     0xa92e2c: movz            x2, #0x2
    // 0xa92e30: r0 = AllocateArray()
    //     0xa92e30: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa92e34: mov             x2, x0
    // 0xa92e38: ldur            x0, [fp, #-0x28]
    // 0xa92e3c: stur            x2, [fp, #-0x18]
    // 0xa92e40: StoreField: r2->field_f = r0
    //     0xa92e40: stur            w0, [x2, #0xf]
    // 0xa92e44: r1 = <Widget>
    //     0xa92e44: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa92e48: r0 = AllocateGrowableArray()
    //     0xa92e48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa92e4c: mov             x2, x0
    // 0xa92e50: ldur            x0, [fp, #-0x18]
    // 0xa92e54: stur            x2, [fp, #-0x28]
    // 0xa92e58: StoreField: r2->field_f = r0
    //     0xa92e58: stur            w0, [x2, #0xf]
    // 0xa92e5c: r0 = 2
    //     0xa92e5c: movz            x0, #0x2
    // 0xa92e60: StoreField: r2->field_b = r0
    //     0xa92e60: stur            w0, [x2, #0xb]
    // 0xa92e64: ldur            x3, [fp, #-8]
    // 0xa92e68: LoadField: r1 = r3->field_f
    //     0xa92e68: ldur            w1, [x3, #0xf]
    // 0xa92e6c: DecompressPointer r1
    //     0xa92e6c: add             x1, x1, HEAP, lsl #32
    // 0xa92e70: r0 = controller()
    //     0xa92e70: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa92e74: LoadField: r1 = r0->field_7b
    //     0xa92e74: ldur            w1, [x0, #0x7b]
    // 0xa92e78: DecompressPointer r1
    //     0xa92e78: add             x1, x1, HEAP, lsl #32
    // 0xa92e7c: r0 = value()
    //     0xa92e7c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa92e80: LoadField: r1 = r0->field_b
    //     0xa92e80: ldur            w1, [x0, #0xb]
    // 0xa92e84: DecompressPointer r1
    //     0xa92e84: add             x1, x1, HEAP, lsl #32
    // 0xa92e88: cmp             w1, NULL
    // 0xa92e8c: b.eq            #0xa92eac
    // 0xa92e90: LoadField: r0 = r1->field_f
    //     0xa92e90: ldur            w0, [x1, #0xf]
    // 0xa92e94: DecompressPointer r0
    //     0xa92e94: add             x0, x0, HEAP, lsl #32
    // 0xa92e98: cmp             w0, NULL
    // 0xa92e9c: b.eq            #0xa92eac
    // 0xa92ea0: LoadField: r1 = r0->field_13
    //     0xa92ea0: ldur            w1, [x0, #0x13]
    // 0xa92ea4: DecompressPointer r1
    //     0xa92ea4: add             x1, x1, HEAP, lsl #32
    // 0xa92ea8: cbz             w1, #0xa9303c
    // 0xa92eac: ldur            x2, [fp, #-8]
    // 0xa92eb0: LoadField: r1 = r2->field_f
    //     0xa92eb0: ldur            w1, [x2, #0xf]
    // 0xa92eb4: DecompressPointer r1
    //     0xa92eb4: add             x1, x1, HEAP, lsl #32
    // 0xa92eb8: r0 = controller()
    //     0xa92eb8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa92ebc: LoadField: r1 = r0->field_7b
    //     0xa92ebc: ldur            w1, [x0, #0x7b]
    // 0xa92ec0: DecompressPointer r1
    //     0xa92ec0: add             x1, x1, HEAP, lsl #32
    // 0xa92ec4: r0 = value()
    //     0xa92ec4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa92ec8: LoadField: r1 = r0->field_b
    //     0xa92ec8: ldur            w1, [x0, #0xb]
    // 0xa92ecc: DecompressPointer r1
    //     0xa92ecc: add             x1, x1, HEAP, lsl #32
    // 0xa92ed0: cmp             w1, NULL
    // 0xa92ed4: b.ne            #0xa92ee0
    // 0xa92ed8: r0 = Null
    //     0xa92ed8: mov             x0, NULL
    // 0xa92edc: b               #0xa92f04
    // 0xa92ee0: LoadField: r0 = r1->field_f
    //     0xa92ee0: ldur            w0, [x1, #0xf]
    // 0xa92ee4: DecompressPointer r0
    //     0xa92ee4: add             x0, x0, HEAP, lsl #32
    // 0xa92ee8: cmp             w0, NULL
    // 0xa92eec: b.ne            #0xa92ef8
    // 0xa92ef0: r0 = Null
    //     0xa92ef0: mov             x0, NULL
    // 0xa92ef4: b               #0xa92f04
    // 0xa92ef8: LoadField: r1 = r0->field_13
    //     0xa92ef8: ldur            w1, [x0, #0x13]
    // 0xa92efc: DecompressPointer r1
    //     0xa92efc: add             x1, x1, HEAP, lsl #32
    // 0xa92f00: mov             x0, x1
    // 0xa92f04: cmp             w0, NULL
    // 0xa92f08: b.ne            #0xa92f14
    // 0xa92f0c: r4 = ""
    //     0xa92f0c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa92f10: b               #0xa92f18
    // 0xa92f14: mov             x4, x0
    // 0xa92f18: ldur            x0, [fp, #-8]
    // 0xa92f1c: ldur            x3, [fp, #-0x28]
    // 0xa92f20: stur            x4, [fp, #-0x18]
    // 0xa92f24: r1 = Null
    //     0xa92f24: mov             x1, NULL
    // 0xa92f28: r2 = 4
    //     0xa92f28: movz            x2, #0x4
    // 0xa92f2c: r0 = AllocateArray()
    //     0xa92f2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa92f30: mov             x1, x0
    // 0xa92f34: ldur            x0, [fp, #-0x18]
    // 0xa92f38: StoreField: r1->field_f = r0
    //     0xa92f38: stur            w0, [x1, #0xf]
    // 0xa92f3c: r16 = " Ratings"
    //     0xa92f3c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f870] " Ratings"
    //     0xa92f40: ldr             x16, [x16, #0x870]
    // 0xa92f44: StoreField: r1->field_13 = r16
    //     0xa92f44: stur            w16, [x1, #0x13]
    // 0xa92f48: str             x1, [SP]
    // 0xa92f4c: r0 = _interpolate()
    //     0xa92f4c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa92f50: ldur            x2, [fp, #-8]
    // 0xa92f54: stur            x0, [fp, #-0x18]
    // 0xa92f58: LoadField: r1 = r2->field_13
    //     0xa92f58: ldur            w1, [x2, #0x13]
    // 0xa92f5c: DecompressPointer r1
    //     0xa92f5c: add             x1, x1, HEAP, lsl #32
    // 0xa92f60: r0 = of()
    //     0xa92f60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa92f64: LoadField: r1 = r0->field_87
    //     0xa92f64: ldur            w1, [x0, #0x87]
    // 0xa92f68: DecompressPointer r1
    //     0xa92f68: add             x1, x1, HEAP, lsl #32
    // 0xa92f6c: LoadField: r0 = r1->field_2b
    //     0xa92f6c: ldur            w0, [x1, #0x2b]
    // 0xa92f70: DecompressPointer r0
    //     0xa92f70: add             x0, x0, HEAP, lsl #32
    // 0xa92f74: stur            x0, [fp, #-0x30]
    // 0xa92f78: r1 = Instance_Color
    //     0xa92f78: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa92f7c: d0 = 0.850000
    //     0xa92f7c: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2f878] IMM: double(0.85) from 0x3feb333333333333
    //     0xa92f80: ldr             d0, [x17, #0x878]
    // 0xa92f84: r0 = withOpacity()
    //     0xa92f84: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa92f88: r16 = 10.000000
    //     0xa92f88: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xa92f8c: stp             x0, x16, [SP]
    // 0xa92f90: ldur            x1, [fp, #-0x30]
    // 0xa92f94: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa92f94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa92f98: ldr             x4, [x4, #0xaa0]
    // 0xa92f9c: r0 = copyWith()
    //     0xa92f9c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa92fa0: stur            x0, [fp, #-0x30]
    // 0xa92fa4: r0 = Text()
    //     0xa92fa4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa92fa8: mov             x2, x0
    // 0xa92fac: ldur            x0, [fp, #-0x18]
    // 0xa92fb0: stur            x2, [fp, #-0x40]
    // 0xa92fb4: StoreField: r2->field_b = r0
    //     0xa92fb4: stur            w0, [x2, #0xb]
    // 0xa92fb8: ldur            x0, [fp, #-0x30]
    // 0xa92fbc: StoreField: r2->field_13 = r0
    //     0xa92fbc: stur            w0, [x2, #0x13]
    // 0xa92fc0: ldur            x0, [fp, #-0x28]
    // 0xa92fc4: LoadField: r1 = r0->field_b
    //     0xa92fc4: ldur            w1, [x0, #0xb]
    // 0xa92fc8: LoadField: r3 = r0->field_f
    //     0xa92fc8: ldur            w3, [x0, #0xf]
    // 0xa92fcc: DecompressPointer r3
    //     0xa92fcc: add             x3, x3, HEAP, lsl #32
    // 0xa92fd0: LoadField: r4 = r3->field_b
    //     0xa92fd0: ldur            w4, [x3, #0xb]
    // 0xa92fd4: r3 = LoadInt32Instr(r1)
    //     0xa92fd4: sbfx            x3, x1, #1, #0x1f
    // 0xa92fd8: stur            x3, [fp, #-0x38]
    // 0xa92fdc: r1 = LoadInt32Instr(r4)
    //     0xa92fdc: sbfx            x1, x4, #1, #0x1f
    // 0xa92fe0: cmp             x3, x1
    // 0xa92fe4: b.ne            #0xa92ff0
    // 0xa92fe8: mov             x1, x0
    // 0xa92fec: r0 = _growToNextCapacity()
    //     0xa92fec: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa92ff0: ldur            x2, [fp, #-0x28]
    // 0xa92ff4: ldur            x3, [fp, #-0x38]
    // 0xa92ff8: add             x0, x3, #1
    // 0xa92ffc: lsl             x1, x0, #1
    // 0xa93000: StoreField: r2->field_b = r1
    //     0xa93000: stur            w1, [x2, #0xb]
    // 0xa93004: LoadField: r1 = r2->field_f
    //     0xa93004: ldur            w1, [x2, #0xf]
    // 0xa93008: DecompressPointer r1
    //     0xa93008: add             x1, x1, HEAP, lsl #32
    // 0xa9300c: ldur            x0, [fp, #-0x40]
    // 0xa93010: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa93010: add             x25, x1, x3, lsl #2
    //     0xa93014: add             x25, x25, #0xf
    //     0xa93018: str             w0, [x25]
    //     0xa9301c: tbz             w0, #0, #0xa93038
    //     0xa93020: ldurb           w16, [x1, #-1]
    //     0xa93024: ldurb           w17, [x0, #-1]
    //     0xa93028: and             x16, x17, x16, lsr #2
    //     0xa9302c: tst             x16, HEAP, lsr #32
    //     0xa93030: b.eq            #0xa93038
    //     0xa93034: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa93038: b               #0xa93040
    // 0xa9303c: ldur            x2, [fp, #-0x28]
    // 0xa93040: ldur            x0, [fp, #-8]
    // 0xa93044: LoadField: r1 = r0->field_f
    //     0xa93044: ldur            w1, [x0, #0xf]
    // 0xa93048: DecompressPointer r1
    //     0xa93048: add             x1, x1, HEAP, lsl #32
    // 0xa9304c: r0 = controller()
    //     0xa9304c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa93050: LoadField: r1 = r0->field_7b
    //     0xa93050: ldur            w1, [x0, #0x7b]
    // 0xa93054: DecompressPointer r1
    //     0xa93054: add             x1, x1, HEAP, lsl #32
    // 0xa93058: r0 = value()
    //     0xa93058: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa9305c: LoadField: r1 = r0->field_b
    //     0xa9305c: ldur            w1, [x0, #0xb]
    // 0xa93060: DecompressPointer r1
    //     0xa93060: add             x1, x1, HEAP, lsl #32
    // 0xa93064: cmp             w1, NULL
    // 0xa93068: b.eq            #0xa93088
    // 0xa9306c: LoadField: r0 = r1->field_f
    //     0xa9306c: ldur            w0, [x1, #0xf]
    // 0xa93070: DecompressPointer r0
    //     0xa93070: add             x0, x0, HEAP, lsl #32
    // 0xa93074: cmp             w0, NULL
    // 0xa93078: b.eq            #0xa93088
    // 0xa9307c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa9307c: ldur            w1, [x0, #0x17]
    // 0xa93080: DecompressPointer r1
    //     0xa93080: add             x1, x1, HEAP, lsl #32
    // 0xa93084: cbz             w1, #0xa93240
    // 0xa93088: ldur            x0, [fp, #-8]
    // 0xa9308c: r1 = Null
    //     0xa9308c: mov             x1, NULL
    // 0xa93090: r2 = 6
    //     0xa93090: movz            x2, #0x6
    // 0xa93094: r0 = AllocateArray()
    //     0xa93094: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa93098: stur            x0, [fp, #-0x18]
    // 0xa9309c: r16 = "& "
    //     0xa9309c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f880] "& "
    //     0xa930a0: ldr             x16, [x16, #0x880]
    // 0xa930a4: StoreField: r0->field_f = r16
    //     0xa930a4: stur            w16, [x0, #0xf]
    // 0xa930a8: ldur            x2, [fp, #-8]
    // 0xa930ac: LoadField: r1 = r2->field_f
    //     0xa930ac: ldur            w1, [x2, #0xf]
    // 0xa930b0: DecompressPointer r1
    //     0xa930b0: add             x1, x1, HEAP, lsl #32
    // 0xa930b4: r0 = controller()
    //     0xa930b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa930b8: LoadField: r1 = r0->field_7b
    //     0xa930b8: ldur            w1, [x0, #0x7b]
    // 0xa930bc: DecompressPointer r1
    //     0xa930bc: add             x1, x1, HEAP, lsl #32
    // 0xa930c0: r0 = value()
    //     0xa930c0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa930c4: LoadField: r1 = r0->field_b
    //     0xa930c4: ldur            w1, [x0, #0xb]
    // 0xa930c8: DecompressPointer r1
    //     0xa930c8: add             x1, x1, HEAP, lsl #32
    // 0xa930cc: cmp             w1, NULL
    // 0xa930d0: b.ne            #0xa930dc
    // 0xa930d4: r0 = Null
    //     0xa930d4: mov             x0, NULL
    // 0xa930d8: b               #0xa93100
    // 0xa930dc: LoadField: r0 = r1->field_f
    //     0xa930dc: ldur            w0, [x1, #0xf]
    // 0xa930e0: DecompressPointer r0
    //     0xa930e0: add             x0, x0, HEAP, lsl #32
    // 0xa930e4: cmp             w0, NULL
    // 0xa930e8: b.ne            #0xa930f4
    // 0xa930ec: r0 = Null
    //     0xa930ec: mov             x0, NULL
    // 0xa930f0: b               #0xa93100
    // 0xa930f4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa930f4: ldur            w1, [x0, #0x17]
    // 0xa930f8: DecompressPointer r1
    //     0xa930f8: add             x1, x1, HEAP, lsl #32
    // 0xa930fc: mov             x0, x1
    // 0xa93100: cmp             w0, NULL
    // 0xa93104: b.ne            #0xa9310c
    // 0xa93108: r0 = ""
    //     0xa93108: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa9310c: ldur            x3, [fp, #-8]
    // 0xa93110: ldur            x2, [fp, #-0x18]
    // 0xa93114: ldur            x4, [fp, #-0x28]
    // 0xa93118: mov             x1, x2
    // 0xa9311c: ArrayStore: r1[1] = r0  ; List_4
    //     0xa9311c: add             x25, x1, #0x13
    //     0xa93120: str             w0, [x25]
    //     0xa93124: tbz             w0, #0, #0xa93140
    //     0xa93128: ldurb           w16, [x1, #-1]
    //     0xa9312c: ldurb           w17, [x0, #-1]
    //     0xa93130: and             x16, x17, x16, lsr #2
    //     0xa93134: tst             x16, HEAP, lsr #32
    //     0xa93138: b.eq            #0xa93140
    //     0xa9313c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa93140: r16 = " Reviews"
    //     0xa93140: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f888] " Reviews"
    //     0xa93144: ldr             x16, [x16, #0x888]
    // 0xa93148: ArrayStore: r2[0] = r16  ; List_4
    //     0xa93148: stur            w16, [x2, #0x17]
    // 0xa9314c: str             x2, [SP]
    // 0xa93150: r0 = _interpolate()
    //     0xa93150: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa93154: ldur            x2, [fp, #-8]
    // 0xa93158: stur            x0, [fp, #-0x18]
    // 0xa9315c: LoadField: r1 = r2->field_13
    //     0xa9315c: ldur            w1, [x2, #0x13]
    // 0xa93160: DecompressPointer r1
    //     0xa93160: add             x1, x1, HEAP, lsl #32
    // 0xa93164: r0 = of()
    //     0xa93164: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa93168: LoadField: r1 = r0->field_87
    //     0xa93168: ldur            w1, [x0, #0x87]
    // 0xa9316c: DecompressPointer r1
    //     0xa9316c: add             x1, x1, HEAP, lsl #32
    // 0xa93170: LoadField: r0 = r1->field_2b
    //     0xa93170: ldur            w0, [x1, #0x2b]
    // 0xa93174: DecompressPointer r0
    //     0xa93174: add             x0, x0, HEAP, lsl #32
    // 0xa93178: stur            x0, [fp, #-0x30]
    // 0xa9317c: r1 = Instance_Color
    //     0xa9317c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa93180: d0 = 0.850000
    //     0xa93180: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2f878] IMM: double(0.85) from 0x3feb333333333333
    //     0xa93184: ldr             d0, [x17, #0x878]
    // 0xa93188: r0 = withOpacity()
    //     0xa93188: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa9318c: r16 = 10.000000
    //     0xa9318c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xa93190: stp             x0, x16, [SP]
    // 0xa93194: ldur            x1, [fp, #-0x30]
    // 0xa93198: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa93198: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa9319c: ldr             x4, [x4, #0xaa0]
    // 0xa931a0: r0 = copyWith()
    //     0xa931a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa931a4: stur            x0, [fp, #-0x30]
    // 0xa931a8: r0 = Text()
    //     0xa931a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa931ac: mov             x2, x0
    // 0xa931b0: ldur            x0, [fp, #-0x18]
    // 0xa931b4: stur            x2, [fp, #-0x40]
    // 0xa931b8: StoreField: r2->field_b = r0
    //     0xa931b8: stur            w0, [x2, #0xb]
    // 0xa931bc: ldur            x0, [fp, #-0x30]
    // 0xa931c0: StoreField: r2->field_13 = r0
    //     0xa931c0: stur            w0, [x2, #0x13]
    // 0xa931c4: ldur            x0, [fp, #-0x28]
    // 0xa931c8: LoadField: r1 = r0->field_b
    //     0xa931c8: ldur            w1, [x0, #0xb]
    // 0xa931cc: LoadField: r3 = r0->field_f
    //     0xa931cc: ldur            w3, [x0, #0xf]
    // 0xa931d0: DecompressPointer r3
    //     0xa931d0: add             x3, x3, HEAP, lsl #32
    // 0xa931d4: LoadField: r4 = r3->field_b
    //     0xa931d4: ldur            w4, [x3, #0xb]
    // 0xa931d8: r3 = LoadInt32Instr(r1)
    //     0xa931d8: sbfx            x3, x1, #1, #0x1f
    // 0xa931dc: stur            x3, [fp, #-0x38]
    // 0xa931e0: r1 = LoadInt32Instr(r4)
    //     0xa931e0: sbfx            x1, x4, #1, #0x1f
    // 0xa931e4: cmp             x3, x1
    // 0xa931e8: b.ne            #0xa931f4
    // 0xa931ec: mov             x1, x0
    // 0xa931f0: r0 = _growToNextCapacity()
    //     0xa931f0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa931f4: ldur            x2, [fp, #-0x28]
    // 0xa931f8: ldur            x3, [fp, #-0x38]
    // 0xa931fc: add             x0, x3, #1
    // 0xa93200: lsl             x1, x0, #1
    // 0xa93204: StoreField: r2->field_b = r1
    //     0xa93204: stur            w1, [x2, #0xb]
    // 0xa93208: LoadField: r1 = r2->field_f
    //     0xa93208: ldur            w1, [x2, #0xf]
    // 0xa9320c: DecompressPointer r1
    //     0xa9320c: add             x1, x1, HEAP, lsl #32
    // 0xa93210: ldur            x0, [fp, #-0x40]
    // 0xa93214: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa93214: add             x25, x1, x3, lsl #2
    //     0xa93218: add             x25, x25, #0xf
    //     0xa9321c: str             w0, [x25]
    //     0xa93220: tbz             w0, #0, #0xa9323c
    //     0xa93224: ldurb           w16, [x1, #-1]
    //     0xa93228: ldurb           w17, [x0, #-1]
    //     0xa9322c: and             x16, x17, x16, lsr #2
    //     0xa93230: tst             x16, HEAP, lsr #32
    //     0xa93234: b.eq            #0xa9323c
    //     0xa93238: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa9323c: b               #0xa93244
    // 0xa93240: ldur            x2, [fp, #-0x28]
    // 0xa93244: ldur            x0, [fp, #-8]
    // 0xa93248: r0 = Column()
    //     0xa93248: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa9324c: mov             x2, x0
    // 0xa93250: r0 = Instance_Axis
    //     0xa93250: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa93254: stur            x2, [fp, #-0x30]
    // 0xa93258: StoreField: r2->field_f = r0
    //     0xa93258: stur            w0, [x2, #0xf]
    // 0xa9325c: r3 = Instance_MainAxisAlignment
    //     0xa9325c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa93260: ldr             x3, [x3, #0xa08]
    // 0xa93264: StoreField: r2->field_13 = r3
    //     0xa93264: stur            w3, [x2, #0x13]
    // 0xa93268: r4 = Instance_MainAxisSize
    //     0xa93268: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa9326c: ldr             x4, [x4, #0xa10]
    // 0xa93270: ArrayStore: r2[0] = r4  ; List_4
    //     0xa93270: stur            w4, [x2, #0x17]
    // 0xa93274: r5 = Instance_CrossAxisAlignment
    //     0xa93274: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa93278: ldr             x5, [x5, #0x890]
    // 0xa9327c: StoreField: r2->field_1b = r5
    //     0xa9327c: stur            w5, [x2, #0x1b]
    // 0xa93280: r6 = Instance_VerticalDirection
    //     0xa93280: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa93284: ldr             x6, [x6, #0xa20]
    // 0xa93288: StoreField: r2->field_23 = r6
    //     0xa93288: stur            w6, [x2, #0x23]
    // 0xa9328c: r7 = Instance_Clip
    //     0xa9328c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa93290: ldr             x7, [x7, #0x38]
    // 0xa93294: StoreField: r2->field_2b = r7
    //     0xa93294: stur            w7, [x2, #0x2b]
    // 0xa93298: StoreField: r2->field_2f = rZR
    //     0xa93298: stur            xzr, [x2, #0x2f]
    // 0xa9329c: ldur            x1, [fp, #-0x28]
    // 0xa932a0: StoreField: r2->field_b = r1
    //     0xa932a0: stur            w1, [x2, #0xb]
    // 0xa932a4: ldur            x8, [fp, #-8]
    // 0xa932a8: LoadField: r9 = r8->field_f
    //     0xa932a8: ldur            w9, [x8, #0xf]
    // 0xa932ac: DecompressPointer r9
    //     0xa932ac: add             x9, x9, HEAP, lsl #32
    // 0xa932b0: stur            x9, [fp, #-0x28]
    // 0xa932b4: LoadField: r10 = r8->field_13
    //     0xa932b4: ldur            w10, [x8, #0x13]
    // 0xa932b8: DecompressPointer r10
    //     0xa932b8: add             x10, x10, HEAP, lsl #32
    // 0xa932bc: mov             x1, x9
    // 0xa932c0: stur            x10, [fp, #-0x18]
    // 0xa932c4: r0 = controller()
    //     0xa932c4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa932c8: LoadField: r1 = r0->field_7b
    //     0xa932c8: ldur            w1, [x0, #0x7b]
    // 0xa932cc: DecompressPointer r1
    //     0xa932cc: add             x1, x1, HEAP, lsl #32
    // 0xa932d0: r0 = value()
    //     0xa932d0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa932d4: LoadField: r1 = r0->field_b
    //     0xa932d4: ldur            w1, [x0, #0xb]
    // 0xa932d8: DecompressPointer r1
    //     0xa932d8: add             x1, x1, HEAP, lsl #32
    // 0xa932dc: cmp             w1, NULL
    // 0xa932e0: b.ne            #0xa932ec
    // 0xa932e4: r0 = Null
    //     0xa932e4: mov             x0, NULL
    // 0xa932e8: b               #0xa93324
    // 0xa932ec: LoadField: r0 = r1->field_f
    //     0xa932ec: ldur            w0, [x1, #0xf]
    // 0xa932f0: DecompressPointer r0
    //     0xa932f0: add             x0, x0, HEAP, lsl #32
    // 0xa932f4: cmp             w0, NULL
    // 0xa932f8: b.ne            #0xa93304
    // 0xa932fc: r0 = Null
    //     0xa932fc: mov             x0, NULL
    // 0xa93300: b               #0xa93324
    // 0xa93304: LoadField: r1 = r0->field_1b
    //     0xa93304: ldur            w1, [x0, #0x1b]
    // 0xa93308: DecompressPointer r1
    //     0xa93308: add             x1, x1, HEAP, lsl #32
    // 0xa9330c: cmp             w1, NULL
    // 0xa93310: b.ne            #0xa9331c
    // 0xa93314: r0 = Null
    //     0xa93314: mov             x0, NULL
    // 0xa93318: b               #0xa93324
    // 0xa9331c: LoadField: r0 = r1->field_7
    //     0xa9331c: ldur            w0, [x1, #7]
    // 0xa93320: DecompressPointer r0
    //     0xa93320: add             x0, x0, HEAP, lsl #32
    // 0xa93324: cmp             w0, NULL
    // 0xa93328: b.ne            #0xa93334
    // 0xa9332c: r0 = 0
    //     0xa9332c: movz            x0, #0
    // 0xa93330: b               #0xa93344
    // 0xa93334: r1 = LoadInt32Instr(r0)
    //     0xa93334: sbfx            x1, x0, #1, #0x1f
    //     0xa93338: tbz             w0, #0, #0xa93340
    //     0xa9333c: ldur            x1, [x0, #7]
    // 0xa93340: mov             x0, x1
    // 0xa93344: ldur            x2, [fp, #-8]
    // 0xa93348: stur            x0, [fp, #-0x38]
    // 0xa9334c: LoadField: r1 = r2->field_f
    //     0xa9334c: ldur            w1, [x2, #0xf]
    // 0xa93350: DecompressPointer r1
    //     0xa93350: add             x1, x1, HEAP, lsl #32
    // 0xa93354: r0 = controller()
    //     0xa93354: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa93358: LoadField: r1 = r0->field_7b
    //     0xa93358: ldur            w1, [x0, #0x7b]
    // 0xa9335c: DecompressPointer r1
    //     0xa9335c: add             x1, x1, HEAP, lsl #32
    // 0xa93360: r0 = value()
    //     0xa93360: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa93364: LoadField: r1 = r0->field_b
    //     0xa93364: ldur            w1, [x0, #0xb]
    // 0xa93368: DecompressPointer r1
    //     0xa93368: add             x1, x1, HEAP, lsl #32
    // 0xa9336c: cmp             w1, NULL
    // 0xa93370: b.ne            #0xa9337c
    // 0xa93374: r0 = Null
    //     0xa93374: mov             x0, NULL
    // 0xa93378: b               #0xa933b4
    // 0xa9337c: LoadField: r0 = r1->field_f
    //     0xa9337c: ldur            w0, [x1, #0xf]
    // 0xa93380: DecompressPointer r0
    //     0xa93380: add             x0, x0, HEAP, lsl #32
    // 0xa93384: cmp             w0, NULL
    // 0xa93388: b.ne            #0xa93394
    // 0xa9338c: r0 = Null
    //     0xa9338c: mov             x0, NULL
    // 0xa93390: b               #0xa933b4
    // 0xa93394: LoadField: r1 = r0->field_1b
    //     0xa93394: ldur            w1, [x0, #0x1b]
    // 0xa93398: DecompressPointer r1
    //     0xa93398: add             x1, x1, HEAP, lsl #32
    // 0xa9339c: cmp             w1, NULL
    // 0xa933a0: b.ne            #0xa933ac
    // 0xa933a4: r0 = Null
    //     0xa933a4: mov             x0, NULL
    // 0xa933a8: b               #0xa933b4
    // 0xa933ac: LoadField: r0 = r1->field_7
    //     0xa933ac: ldur            w0, [x1, #7]
    // 0xa933b0: DecompressPointer r0
    //     0xa933b0: add             x0, x0, HEAP, lsl #32
    // 0xa933b4: cmp             w0, NULL
    // 0xa933b8: b.ne            #0xa933c4
    // 0xa933bc: r0 = 0
    //     0xa933bc: movz            x0, #0
    // 0xa933c0: b               #0xa933d4
    // 0xa933c4: r1 = LoadInt32Instr(r0)
    //     0xa933c4: sbfx            x1, x0, #1, #0x1f
    //     0xa933c8: tbz             w0, #0, #0xa933d0
    //     0xa933cc: ldur            x1, [x0, #7]
    // 0xa933d0: mov             x0, x1
    // 0xa933d4: ldur            x2, [fp, #-8]
    // 0xa933d8: stur            x0, [fp, #-0x48]
    // 0xa933dc: LoadField: r1 = r2->field_f
    //     0xa933dc: ldur            w1, [x2, #0xf]
    // 0xa933e0: DecompressPointer r1
    //     0xa933e0: add             x1, x1, HEAP, lsl #32
    // 0xa933e4: r0 = controller()
    //     0xa933e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa933e8: LoadField: r1 = r0->field_7b
    //     0xa933e8: ldur            w1, [x0, #0x7b]
    // 0xa933ec: DecompressPointer r1
    //     0xa933ec: add             x1, x1, HEAP, lsl #32
    // 0xa933f0: r0 = value()
    //     0xa933f0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa933f4: LoadField: r1 = r0->field_b
    //     0xa933f4: ldur            w1, [x0, #0xb]
    // 0xa933f8: DecompressPointer r1
    //     0xa933f8: add             x1, x1, HEAP, lsl #32
    // 0xa933fc: cmp             w1, NULL
    // 0xa93400: b.ne            #0xa9340c
    // 0xa93404: r0 = Null
    //     0xa93404: mov             x0, NULL
    // 0xa93408: b               #0xa93444
    // 0xa9340c: LoadField: r0 = r1->field_f
    //     0xa9340c: ldur            w0, [x1, #0xf]
    // 0xa93410: DecompressPointer r0
    //     0xa93410: add             x0, x0, HEAP, lsl #32
    // 0xa93414: cmp             w0, NULL
    // 0xa93418: b.ne            #0xa93424
    // 0xa9341c: r0 = Null
    //     0xa9341c: mov             x0, NULL
    // 0xa93420: b               #0xa93444
    // 0xa93424: LoadField: r1 = r0->field_1b
    //     0xa93424: ldur            w1, [x0, #0x1b]
    // 0xa93428: DecompressPointer r1
    //     0xa93428: add             x1, x1, HEAP, lsl #32
    // 0xa9342c: cmp             w1, NULL
    // 0xa93430: b.ne            #0xa9343c
    // 0xa93434: r0 = Null
    //     0xa93434: mov             x0, NULL
    // 0xa93438: b               #0xa93444
    // 0xa9343c: LoadField: r0 = r1->field_b
    //     0xa9343c: ldur            w0, [x1, #0xb]
    // 0xa93440: DecompressPointer r0
    //     0xa93440: add             x0, x0, HEAP, lsl #32
    // 0xa93444: cmp             w0, NULL
    // 0xa93448: b.ne            #0xa93454
    // 0xa9344c: r1 = 0
    //     0xa9344c: movz            x1, #0
    // 0xa93450: b               #0xa93460
    // 0xa93454: r1 = LoadInt32Instr(r0)
    //     0xa93454: sbfx            x1, x0, #1, #0x1f
    //     0xa93458: tbz             w0, #0, #0xa93460
    //     0xa9345c: ldur            x1, [x0, #7]
    // 0xa93460: ldur            x2, [fp, #-8]
    // 0xa93464: ldur            x0, [fp, #-0x48]
    // 0xa93468: add             x3, x0, x1
    // 0xa9346c: stur            x3, [fp, #-0x50]
    // 0xa93470: LoadField: r1 = r2->field_f
    //     0xa93470: ldur            w1, [x2, #0xf]
    // 0xa93474: DecompressPointer r1
    //     0xa93474: add             x1, x1, HEAP, lsl #32
    // 0xa93478: r0 = controller()
    //     0xa93478: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa9347c: LoadField: r1 = r0->field_7b
    //     0xa9347c: ldur            w1, [x0, #0x7b]
    // 0xa93480: DecompressPointer r1
    //     0xa93480: add             x1, x1, HEAP, lsl #32
    // 0xa93484: r0 = value()
    //     0xa93484: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa93488: LoadField: r1 = r0->field_b
    //     0xa93488: ldur            w1, [x0, #0xb]
    // 0xa9348c: DecompressPointer r1
    //     0xa9348c: add             x1, x1, HEAP, lsl #32
    // 0xa93490: cmp             w1, NULL
    // 0xa93494: b.ne            #0xa934a0
    // 0xa93498: r0 = Null
    //     0xa93498: mov             x0, NULL
    // 0xa9349c: b               #0xa934d8
    // 0xa934a0: LoadField: r0 = r1->field_f
    //     0xa934a0: ldur            w0, [x1, #0xf]
    // 0xa934a4: DecompressPointer r0
    //     0xa934a4: add             x0, x0, HEAP, lsl #32
    // 0xa934a8: cmp             w0, NULL
    // 0xa934ac: b.ne            #0xa934b8
    // 0xa934b0: r0 = Null
    //     0xa934b0: mov             x0, NULL
    // 0xa934b4: b               #0xa934d8
    // 0xa934b8: LoadField: r1 = r0->field_1b
    //     0xa934b8: ldur            w1, [x0, #0x1b]
    // 0xa934bc: DecompressPointer r1
    //     0xa934bc: add             x1, x1, HEAP, lsl #32
    // 0xa934c0: cmp             w1, NULL
    // 0xa934c4: b.ne            #0xa934d0
    // 0xa934c8: r0 = Null
    //     0xa934c8: mov             x0, NULL
    // 0xa934cc: b               #0xa934d8
    // 0xa934d0: LoadField: r0 = r1->field_f
    //     0xa934d0: ldur            w0, [x1, #0xf]
    // 0xa934d4: DecompressPointer r0
    //     0xa934d4: add             x0, x0, HEAP, lsl #32
    // 0xa934d8: cmp             w0, NULL
    // 0xa934dc: b.ne            #0xa934e8
    // 0xa934e0: r1 = 0
    //     0xa934e0: movz            x1, #0
    // 0xa934e4: b               #0xa934f4
    // 0xa934e8: r1 = LoadInt32Instr(r0)
    //     0xa934e8: sbfx            x1, x0, #1, #0x1f
    //     0xa934ec: tbz             w0, #0, #0xa934f4
    //     0xa934f0: ldur            x1, [x0, #7]
    // 0xa934f4: ldur            x2, [fp, #-8]
    // 0xa934f8: ldur            x0, [fp, #-0x50]
    // 0xa934fc: add             x3, x0, x1
    // 0xa93500: stur            x3, [fp, #-0x48]
    // 0xa93504: LoadField: r1 = r2->field_f
    //     0xa93504: ldur            w1, [x2, #0xf]
    // 0xa93508: DecompressPointer r1
    //     0xa93508: add             x1, x1, HEAP, lsl #32
    // 0xa9350c: r0 = controller()
    //     0xa9350c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa93510: LoadField: r1 = r0->field_7b
    //     0xa93510: ldur            w1, [x0, #0x7b]
    // 0xa93514: DecompressPointer r1
    //     0xa93514: add             x1, x1, HEAP, lsl #32
    // 0xa93518: r0 = value()
    //     0xa93518: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa9351c: LoadField: r1 = r0->field_b
    //     0xa9351c: ldur            w1, [x0, #0xb]
    // 0xa93520: DecompressPointer r1
    //     0xa93520: add             x1, x1, HEAP, lsl #32
    // 0xa93524: cmp             w1, NULL
    // 0xa93528: b.ne            #0xa93534
    // 0xa9352c: r0 = Null
    //     0xa9352c: mov             x0, NULL
    // 0xa93530: b               #0xa9356c
    // 0xa93534: LoadField: r0 = r1->field_f
    //     0xa93534: ldur            w0, [x1, #0xf]
    // 0xa93538: DecompressPointer r0
    //     0xa93538: add             x0, x0, HEAP, lsl #32
    // 0xa9353c: cmp             w0, NULL
    // 0xa93540: b.ne            #0xa9354c
    // 0xa93544: r0 = Null
    //     0xa93544: mov             x0, NULL
    // 0xa93548: b               #0xa9356c
    // 0xa9354c: LoadField: r1 = r0->field_1b
    //     0xa9354c: ldur            w1, [x0, #0x1b]
    // 0xa93550: DecompressPointer r1
    //     0xa93550: add             x1, x1, HEAP, lsl #32
    // 0xa93554: cmp             w1, NULL
    // 0xa93558: b.ne            #0xa93564
    // 0xa9355c: r0 = Null
    //     0xa9355c: mov             x0, NULL
    // 0xa93560: b               #0xa9356c
    // 0xa93564: LoadField: r0 = r1->field_13
    //     0xa93564: ldur            w0, [x1, #0x13]
    // 0xa93568: DecompressPointer r0
    //     0xa93568: add             x0, x0, HEAP, lsl #32
    // 0xa9356c: cmp             w0, NULL
    // 0xa93570: b.ne            #0xa9357c
    // 0xa93574: r1 = 0
    //     0xa93574: movz            x1, #0
    // 0xa93578: b               #0xa93588
    // 0xa9357c: r1 = LoadInt32Instr(r0)
    //     0xa9357c: sbfx            x1, x0, #1, #0x1f
    //     0xa93580: tbz             w0, #0, #0xa93588
    //     0xa93584: ldur            x1, [x0, #7]
    // 0xa93588: ldur            x2, [fp, #-8]
    // 0xa9358c: ldur            x0, [fp, #-0x48]
    // 0xa93590: add             x3, x0, x1
    // 0xa93594: stur            x3, [fp, #-0x50]
    // 0xa93598: LoadField: r1 = r2->field_f
    //     0xa93598: ldur            w1, [x2, #0xf]
    // 0xa9359c: DecompressPointer r1
    //     0xa9359c: add             x1, x1, HEAP, lsl #32
    // 0xa935a0: r0 = controller()
    //     0xa935a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa935a4: LoadField: r1 = r0->field_7b
    //     0xa935a4: ldur            w1, [x0, #0x7b]
    // 0xa935a8: DecompressPointer r1
    //     0xa935a8: add             x1, x1, HEAP, lsl #32
    // 0xa935ac: r0 = value()
    //     0xa935ac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa935b0: LoadField: r1 = r0->field_b
    //     0xa935b0: ldur            w1, [x0, #0xb]
    // 0xa935b4: DecompressPointer r1
    //     0xa935b4: add             x1, x1, HEAP, lsl #32
    // 0xa935b8: cmp             w1, NULL
    // 0xa935bc: b.ne            #0xa935c8
    // 0xa935c0: r0 = Null
    //     0xa935c0: mov             x0, NULL
    // 0xa935c4: b               #0xa93600
    // 0xa935c8: LoadField: r0 = r1->field_f
    //     0xa935c8: ldur            w0, [x1, #0xf]
    // 0xa935cc: DecompressPointer r0
    //     0xa935cc: add             x0, x0, HEAP, lsl #32
    // 0xa935d0: cmp             w0, NULL
    // 0xa935d4: b.ne            #0xa935e0
    // 0xa935d8: r0 = Null
    //     0xa935d8: mov             x0, NULL
    // 0xa935dc: b               #0xa93600
    // 0xa935e0: LoadField: r1 = r0->field_1b
    //     0xa935e0: ldur            w1, [x0, #0x1b]
    // 0xa935e4: DecompressPointer r1
    //     0xa935e4: add             x1, x1, HEAP, lsl #32
    // 0xa935e8: cmp             w1, NULL
    // 0xa935ec: b.ne            #0xa935f8
    // 0xa935f0: r0 = Null
    //     0xa935f0: mov             x0, NULL
    // 0xa935f4: b               #0xa93600
    // 0xa935f8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa935f8: ldur            w0, [x1, #0x17]
    // 0xa935fc: DecompressPointer r0
    //     0xa935fc: add             x0, x0, HEAP, lsl #32
    // 0xa93600: cmp             w0, NULL
    // 0xa93604: b.ne            #0xa93610
    // 0xa93608: r3 = 0
    //     0xa93608: movz            x3, #0
    // 0xa9360c: b               #0xa93620
    // 0xa93610: r1 = LoadInt32Instr(r0)
    //     0xa93610: sbfx            x1, x0, #1, #0x1f
    //     0xa93614: tbz             w0, #0, #0xa9361c
    //     0xa93618: ldur            x1, [x0, #7]
    // 0xa9361c: mov             x3, x1
    // 0xa93620: ldur            x2, [fp, #-8]
    // 0xa93624: ldur            x1, [fp, #-0x38]
    // 0xa93628: ldur            x0, [fp, #-0x50]
    // 0xa9362c: add             x4, x0, x3
    // 0xa93630: scvtf           d0, x1
    // 0xa93634: scvtf           d1, x4
    // 0xa93638: fdiv            d2, d0, d1
    // 0xa9363c: stur            d2, [fp, #-0x70]
    // 0xa93640: LoadField: r1 = r2->field_f
    //     0xa93640: ldur            w1, [x2, #0xf]
    // 0xa93644: DecompressPointer r1
    //     0xa93644: add             x1, x1, HEAP, lsl #32
    // 0xa93648: r0 = controller()
    //     0xa93648: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa9364c: LoadField: r1 = r0->field_7b
    //     0xa9364c: ldur            w1, [x0, #0x7b]
    // 0xa93650: DecompressPointer r1
    //     0xa93650: add             x1, x1, HEAP, lsl #32
    // 0xa93654: r0 = value()
    //     0xa93654: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa93658: LoadField: r1 = r0->field_b
    //     0xa93658: ldur            w1, [x0, #0xb]
    // 0xa9365c: DecompressPointer r1
    //     0xa9365c: add             x1, x1, HEAP, lsl #32
    // 0xa93660: cmp             w1, NULL
    // 0xa93664: b.ne            #0xa93670
    // 0xa93668: r0 = Null
    //     0xa93668: mov             x0, NULL
    // 0xa9366c: b               #0xa936a8
    // 0xa93670: LoadField: r0 = r1->field_f
    //     0xa93670: ldur            w0, [x1, #0xf]
    // 0xa93674: DecompressPointer r0
    //     0xa93674: add             x0, x0, HEAP, lsl #32
    // 0xa93678: cmp             w0, NULL
    // 0xa9367c: b.ne            #0xa93688
    // 0xa93680: r0 = Null
    //     0xa93680: mov             x0, NULL
    // 0xa93684: b               #0xa936a8
    // 0xa93688: LoadField: r1 = r0->field_1b
    //     0xa93688: ldur            w1, [x0, #0x1b]
    // 0xa9368c: DecompressPointer r1
    //     0xa9368c: add             x1, x1, HEAP, lsl #32
    // 0xa93690: cmp             w1, NULL
    // 0xa93694: b.ne            #0xa936a0
    // 0xa93698: r0 = Null
    //     0xa93698: mov             x0, NULL
    // 0xa9369c: b               #0xa936a8
    // 0xa936a0: LoadField: r0 = r1->field_7
    //     0xa936a0: ldur            w0, [x1, #7]
    // 0xa936a4: DecompressPointer r0
    //     0xa936a4: add             x0, x0, HEAP, lsl #32
    // 0xa936a8: cmp             w0, NULL
    // 0xa936ac: b.ne            #0xa936b8
    // 0xa936b0: r5 = 0
    //     0xa936b0: movz            x5, #0
    // 0xa936b4: b               #0xa936c8
    // 0xa936b8: r1 = LoadInt32Instr(r0)
    //     0xa936b8: sbfx            x1, x0, #1, #0x1f
    //     0xa936bc: tbz             w0, #0, #0xa936c4
    //     0xa936c0: ldur            x1, [x0, #7]
    // 0xa936c4: mov             x5, x1
    // 0xa936c8: ldur            x0, [fp, #-8]
    // 0xa936cc: ldur            x1, [fp, #-0x28]
    // 0xa936d0: ldur            x2, [fp, #-0x18]
    // 0xa936d4: ldur            d0, [fp, #-0x70]
    // 0xa936d8: r3 = "5"
    //     0xa936d8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f898] "5"
    //     0xa936dc: ldr             x3, [x3, #0x898]
    // 0xa936e0: r0 = chartRow()
    //     0xa936e0: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xa936e4: ldur            x2, [fp, #-8]
    // 0xa936e8: stur            x0, [fp, #-0x40]
    // 0xa936ec: LoadField: r3 = r2->field_f
    //     0xa936ec: ldur            w3, [x2, #0xf]
    // 0xa936f0: DecompressPointer r3
    //     0xa936f0: add             x3, x3, HEAP, lsl #32
    // 0xa936f4: stur            x3, [fp, #-0x28]
    // 0xa936f8: LoadField: r4 = r2->field_13
    //     0xa936f8: ldur            w4, [x2, #0x13]
    // 0xa936fc: DecompressPointer r4
    //     0xa936fc: add             x4, x4, HEAP, lsl #32
    // 0xa93700: mov             x1, x3
    // 0xa93704: stur            x4, [fp, #-0x18]
    // 0xa93708: r0 = controller()
    //     0xa93708: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa9370c: LoadField: r1 = r0->field_7b
    //     0xa9370c: ldur            w1, [x0, #0x7b]
    // 0xa93710: DecompressPointer r1
    //     0xa93710: add             x1, x1, HEAP, lsl #32
    // 0xa93714: r0 = value()
    //     0xa93714: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa93718: LoadField: r1 = r0->field_b
    //     0xa93718: ldur            w1, [x0, #0xb]
    // 0xa9371c: DecompressPointer r1
    //     0xa9371c: add             x1, x1, HEAP, lsl #32
    // 0xa93720: cmp             w1, NULL
    // 0xa93724: b.ne            #0xa93730
    // 0xa93728: r0 = Null
    //     0xa93728: mov             x0, NULL
    // 0xa9372c: b               #0xa93768
    // 0xa93730: LoadField: r0 = r1->field_f
    //     0xa93730: ldur            w0, [x1, #0xf]
    // 0xa93734: DecompressPointer r0
    //     0xa93734: add             x0, x0, HEAP, lsl #32
    // 0xa93738: cmp             w0, NULL
    // 0xa9373c: b.ne            #0xa93748
    // 0xa93740: r0 = Null
    //     0xa93740: mov             x0, NULL
    // 0xa93744: b               #0xa93768
    // 0xa93748: LoadField: r1 = r0->field_1b
    //     0xa93748: ldur            w1, [x0, #0x1b]
    // 0xa9374c: DecompressPointer r1
    //     0xa9374c: add             x1, x1, HEAP, lsl #32
    // 0xa93750: cmp             w1, NULL
    // 0xa93754: b.ne            #0xa93760
    // 0xa93758: r0 = Null
    //     0xa93758: mov             x0, NULL
    // 0xa9375c: b               #0xa93768
    // 0xa93760: LoadField: r0 = r1->field_b
    //     0xa93760: ldur            w0, [x1, #0xb]
    // 0xa93764: DecompressPointer r0
    //     0xa93764: add             x0, x0, HEAP, lsl #32
    // 0xa93768: cmp             w0, NULL
    // 0xa9376c: b.ne            #0xa93778
    // 0xa93770: r0 = 0
    //     0xa93770: movz            x0, #0
    // 0xa93774: b               #0xa93788
    // 0xa93778: r1 = LoadInt32Instr(r0)
    //     0xa93778: sbfx            x1, x0, #1, #0x1f
    //     0xa9377c: tbz             w0, #0, #0xa93784
    //     0xa93780: ldur            x1, [x0, #7]
    // 0xa93784: mov             x0, x1
    // 0xa93788: ldur            x2, [fp, #-8]
    // 0xa9378c: stur            x0, [fp, #-0x38]
    // 0xa93790: LoadField: r1 = r2->field_f
    //     0xa93790: ldur            w1, [x2, #0xf]
    // 0xa93794: DecompressPointer r1
    //     0xa93794: add             x1, x1, HEAP, lsl #32
    // 0xa93798: r0 = controller()
    //     0xa93798: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa9379c: LoadField: r1 = r0->field_7b
    //     0xa9379c: ldur            w1, [x0, #0x7b]
    // 0xa937a0: DecompressPointer r1
    //     0xa937a0: add             x1, x1, HEAP, lsl #32
    // 0xa937a4: r0 = value()
    //     0xa937a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa937a8: LoadField: r1 = r0->field_b
    //     0xa937a8: ldur            w1, [x0, #0xb]
    // 0xa937ac: DecompressPointer r1
    //     0xa937ac: add             x1, x1, HEAP, lsl #32
    // 0xa937b0: cmp             w1, NULL
    // 0xa937b4: b.ne            #0xa937c0
    // 0xa937b8: r0 = Null
    //     0xa937b8: mov             x0, NULL
    // 0xa937bc: b               #0xa937f8
    // 0xa937c0: LoadField: r0 = r1->field_f
    //     0xa937c0: ldur            w0, [x1, #0xf]
    // 0xa937c4: DecompressPointer r0
    //     0xa937c4: add             x0, x0, HEAP, lsl #32
    // 0xa937c8: cmp             w0, NULL
    // 0xa937cc: b.ne            #0xa937d8
    // 0xa937d0: r0 = Null
    //     0xa937d0: mov             x0, NULL
    // 0xa937d4: b               #0xa937f8
    // 0xa937d8: LoadField: r1 = r0->field_1b
    //     0xa937d8: ldur            w1, [x0, #0x1b]
    // 0xa937dc: DecompressPointer r1
    //     0xa937dc: add             x1, x1, HEAP, lsl #32
    // 0xa937e0: cmp             w1, NULL
    // 0xa937e4: b.ne            #0xa937f0
    // 0xa937e8: r0 = Null
    //     0xa937e8: mov             x0, NULL
    // 0xa937ec: b               #0xa937f8
    // 0xa937f0: LoadField: r0 = r1->field_7
    //     0xa937f0: ldur            w0, [x1, #7]
    // 0xa937f4: DecompressPointer r0
    //     0xa937f4: add             x0, x0, HEAP, lsl #32
    // 0xa937f8: cmp             w0, NULL
    // 0xa937fc: b.ne            #0xa93808
    // 0xa93800: r0 = 0
    //     0xa93800: movz            x0, #0
    // 0xa93804: b               #0xa93818
    // 0xa93808: r1 = LoadInt32Instr(r0)
    //     0xa93808: sbfx            x1, x0, #1, #0x1f
    //     0xa9380c: tbz             w0, #0, #0xa93814
    //     0xa93810: ldur            x1, [x0, #7]
    // 0xa93814: mov             x0, x1
    // 0xa93818: ldur            x2, [fp, #-8]
    // 0xa9381c: stur            x0, [fp, #-0x48]
    // 0xa93820: LoadField: r1 = r2->field_f
    //     0xa93820: ldur            w1, [x2, #0xf]
    // 0xa93824: DecompressPointer r1
    //     0xa93824: add             x1, x1, HEAP, lsl #32
    // 0xa93828: r0 = controller()
    //     0xa93828: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa9382c: LoadField: r1 = r0->field_7b
    //     0xa9382c: ldur            w1, [x0, #0x7b]
    // 0xa93830: DecompressPointer r1
    //     0xa93830: add             x1, x1, HEAP, lsl #32
    // 0xa93834: r0 = value()
    //     0xa93834: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa93838: LoadField: r1 = r0->field_b
    //     0xa93838: ldur            w1, [x0, #0xb]
    // 0xa9383c: DecompressPointer r1
    //     0xa9383c: add             x1, x1, HEAP, lsl #32
    // 0xa93840: cmp             w1, NULL
    // 0xa93844: b.ne            #0xa93850
    // 0xa93848: r0 = Null
    //     0xa93848: mov             x0, NULL
    // 0xa9384c: b               #0xa93888
    // 0xa93850: LoadField: r0 = r1->field_f
    //     0xa93850: ldur            w0, [x1, #0xf]
    // 0xa93854: DecompressPointer r0
    //     0xa93854: add             x0, x0, HEAP, lsl #32
    // 0xa93858: cmp             w0, NULL
    // 0xa9385c: b.ne            #0xa93868
    // 0xa93860: r0 = Null
    //     0xa93860: mov             x0, NULL
    // 0xa93864: b               #0xa93888
    // 0xa93868: LoadField: r1 = r0->field_1b
    //     0xa93868: ldur            w1, [x0, #0x1b]
    // 0xa9386c: DecompressPointer r1
    //     0xa9386c: add             x1, x1, HEAP, lsl #32
    // 0xa93870: cmp             w1, NULL
    // 0xa93874: b.ne            #0xa93880
    // 0xa93878: r0 = Null
    //     0xa93878: mov             x0, NULL
    // 0xa9387c: b               #0xa93888
    // 0xa93880: LoadField: r0 = r1->field_b
    //     0xa93880: ldur            w0, [x1, #0xb]
    // 0xa93884: DecompressPointer r0
    //     0xa93884: add             x0, x0, HEAP, lsl #32
    // 0xa93888: cmp             w0, NULL
    // 0xa9388c: b.ne            #0xa93898
    // 0xa93890: r1 = 0
    //     0xa93890: movz            x1, #0
    // 0xa93894: b               #0xa938a4
    // 0xa93898: r1 = LoadInt32Instr(r0)
    //     0xa93898: sbfx            x1, x0, #1, #0x1f
    //     0xa9389c: tbz             w0, #0, #0xa938a4
    //     0xa938a0: ldur            x1, [x0, #7]
    // 0xa938a4: ldur            x2, [fp, #-8]
    // 0xa938a8: ldur            x0, [fp, #-0x48]
    // 0xa938ac: add             x3, x0, x1
    // 0xa938b0: stur            x3, [fp, #-0x50]
    // 0xa938b4: LoadField: r1 = r2->field_f
    //     0xa938b4: ldur            w1, [x2, #0xf]
    // 0xa938b8: DecompressPointer r1
    //     0xa938b8: add             x1, x1, HEAP, lsl #32
    // 0xa938bc: r0 = controller()
    //     0xa938bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa938c0: LoadField: r1 = r0->field_7b
    //     0xa938c0: ldur            w1, [x0, #0x7b]
    // 0xa938c4: DecompressPointer r1
    //     0xa938c4: add             x1, x1, HEAP, lsl #32
    // 0xa938c8: r0 = value()
    //     0xa938c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa938cc: LoadField: r1 = r0->field_b
    //     0xa938cc: ldur            w1, [x0, #0xb]
    // 0xa938d0: DecompressPointer r1
    //     0xa938d0: add             x1, x1, HEAP, lsl #32
    // 0xa938d4: cmp             w1, NULL
    // 0xa938d8: b.ne            #0xa938e4
    // 0xa938dc: r0 = Null
    //     0xa938dc: mov             x0, NULL
    // 0xa938e0: b               #0xa9391c
    // 0xa938e4: LoadField: r0 = r1->field_f
    //     0xa938e4: ldur            w0, [x1, #0xf]
    // 0xa938e8: DecompressPointer r0
    //     0xa938e8: add             x0, x0, HEAP, lsl #32
    // 0xa938ec: cmp             w0, NULL
    // 0xa938f0: b.ne            #0xa938fc
    // 0xa938f4: r0 = Null
    //     0xa938f4: mov             x0, NULL
    // 0xa938f8: b               #0xa9391c
    // 0xa938fc: LoadField: r1 = r0->field_1b
    //     0xa938fc: ldur            w1, [x0, #0x1b]
    // 0xa93900: DecompressPointer r1
    //     0xa93900: add             x1, x1, HEAP, lsl #32
    // 0xa93904: cmp             w1, NULL
    // 0xa93908: b.ne            #0xa93914
    // 0xa9390c: r0 = Null
    //     0xa9390c: mov             x0, NULL
    // 0xa93910: b               #0xa9391c
    // 0xa93914: LoadField: r0 = r1->field_f
    //     0xa93914: ldur            w0, [x1, #0xf]
    // 0xa93918: DecompressPointer r0
    //     0xa93918: add             x0, x0, HEAP, lsl #32
    // 0xa9391c: cmp             w0, NULL
    // 0xa93920: b.ne            #0xa9392c
    // 0xa93924: r1 = 0
    //     0xa93924: movz            x1, #0
    // 0xa93928: b               #0xa93938
    // 0xa9392c: r1 = LoadInt32Instr(r0)
    //     0xa9392c: sbfx            x1, x0, #1, #0x1f
    //     0xa93930: tbz             w0, #0, #0xa93938
    //     0xa93934: ldur            x1, [x0, #7]
    // 0xa93938: ldur            x2, [fp, #-8]
    // 0xa9393c: ldur            x0, [fp, #-0x50]
    // 0xa93940: add             x3, x0, x1
    // 0xa93944: stur            x3, [fp, #-0x48]
    // 0xa93948: LoadField: r1 = r2->field_f
    //     0xa93948: ldur            w1, [x2, #0xf]
    // 0xa9394c: DecompressPointer r1
    //     0xa9394c: add             x1, x1, HEAP, lsl #32
    // 0xa93950: r0 = controller()
    //     0xa93950: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa93954: LoadField: r1 = r0->field_7b
    //     0xa93954: ldur            w1, [x0, #0x7b]
    // 0xa93958: DecompressPointer r1
    //     0xa93958: add             x1, x1, HEAP, lsl #32
    // 0xa9395c: r0 = value()
    //     0xa9395c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa93960: LoadField: r1 = r0->field_b
    //     0xa93960: ldur            w1, [x0, #0xb]
    // 0xa93964: DecompressPointer r1
    //     0xa93964: add             x1, x1, HEAP, lsl #32
    // 0xa93968: cmp             w1, NULL
    // 0xa9396c: b.ne            #0xa93978
    // 0xa93970: r0 = Null
    //     0xa93970: mov             x0, NULL
    // 0xa93974: b               #0xa939b0
    // 0xa93978: LoadField: r0 = r1->field_f
    //     0xa93978: ldur            w0, [x1, #0xf]
    // 0xa9397c: DecompressPointer r0
    //     0xa9397c: add             x0, x0, HEAP, lsl #32
    // 0xa93980: cmp             w0, NULL
    // 0xa93984: b.ne            #0xa93990
    // 0xa93988: r0 = Null
    //     0xa93988: mov             x0, NULL
    // 0xa9398c: b               #0xa939b0
    // 0xa93990: LoadField: r1 = r0->field_1b
    //     0xa93990: ldur            w1, [x0, #0x1b]
    // 0xa93994: DecompressPointer r1
    //     0xa93994: add             x1, x1, HEAP, lsl #32
    // 0xa93998: cmp             w1, NULL
    // 0xa9399c: b.ne            #0xa939a8
    // 0xa939a0: r0 = Null
    //     0xa939a0: mov             x0, NULL
    // 0xa939a4: b               #0xa939b0
    // 0xa939a8: LoadField: r0 = r1->field_13
    //     0xa939a8: ldur            w0, [x1, #0x13]
    // 0xa939ac: DecompressPointer r0
    //     0xa939ac: add             x0, x0, HEAP, lsl #32
    // 0xa939b0: cmp             w0, NULL
    // 0xa939b4: b.ne            #0xa939c0
    // 0xa939b8: r1 = 0
    //     0xa939b8: movz            x1, #0
    // 0xa939bc: b               #0xa939cc
    // 0xa939c0: r1 = LoadInt32Instr(r0)
    //     0xa939c0: sbfx            x1, x0, #1, #0x1f
    //     0xa939c4: tbz             w0, #0, #0xa939cc
    //     0xa939c8: ldur            x1, [x0, #7]
    // 0xa939cc: ldur            x2, [fp, #-8]
    // 0xa939d0: ldur            x0, [fp, #-0x48]
    // 0xa939d4: add             x3, x0, x1
    // 0xa939d8: stur            x3, [fp, #-0x50]
    // 0xa939dc: LoadField: r1 = r2->field_f
    //     0xa939dc: ldur            w1, [x2, #0xf]
    // 0xa939e0: DecompressPointer r1
    //     0xa939e0: add             x1, x1, HEAP, lsl #32
    // 0xa939e4: r0 = controller()
    //     0xa939e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa939e8: LoadField: r1 = r0->field_7b
    //     0xa939e8: ldur            w1, [x0, #0x7b]
    // 0xa939ec: DecompressPointer r1
    //     0xa939ec: add             x1, x1, HEAP, lsl #32
    // 0xa939f0: r0 = value()
    //     0xa939f0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa939f4: LoadField: r1 = r0->field_b
    //     0xa939f4: ldur            w1, [x0, #0xb]
    // 0xa939f8: DecompressPointer r1
    //     0xa939f8: add             x1, x1, HEAP, lsl #32
    // 0xa939fc: cmp             w1, NULL
    // 0xa93a00: b.ne            #0xa93a0c
    // 0xa93a04: r0 = Null
    //     0xa93a04: mov             x0, NULL
    // 0xa93a08: b               #0xa93a44
    // 0xa93a0c: LoadField: r0 = r1->field_f
    //     0xa93a0c: ldur            w0, [x1, #0xf]
    // 0xa93a10: DecompressPointer r0
    //     0xa93a10: add             x0, x0, HEAP, lsl #32
    // 0xa93a14: cmp             w0, NULL
    // 0xa93a18: b.ne            #0xa93a24
    // 0xa93a1c: r0 = Null
    //     0xa93a1c: mov             x0, NULL
    // 0xa93a20: b               #0xa93a44
    // 0xa93a24: LoadField: r1 = r0->field_1b
    //     0xa93a24: ldur            w1, [x0, #0x1b]
    // 0xa93a28: DecompressPointer r1
    //     0xa93a28: add             x1, x1, HEAP, lsl #32
    // 0xa93a2c: cmp             w1, NULL
    // 0xa93a30: b.ne            #0xa93a3c
    // 0xa93a34: r0 = Null
    //     0xa93a34: mov             x0, NULL
    // 0xa93a38: b               #0xa93a44
    // 0xa93a3c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa93a3c: ldur            w0, [x1, #0x17]
    // 0xa93a40: DecompressPointer r0
    //     0xa93a40: add             x0, x0, HEAP, lsl #32
    // 0xa93a44: cmp             w0, NULL
    // 0xa93a48: b.ne            #0xa93a54
    // 0xa93a4c: r3 = 0
    //     0xa93a4c: movz            x3, #0
    // 0xa93a50: b               #0xa93a64
    // 0xa93a54: r1 = LoadInt32Instr(r0)
    //     0xa93a54: sbfx            x1, x0, #1, #0x1f
    //     0xa93a58: tbz             w0, #0, #0xa93a60
    //     0xa93a5c: ldur            x1, [x0, #7]
    // 0xa93a60: mov             x3, x1
    // 0xa93a64: ldur            x2, [fp, #-8]
    // 0xa93a68: ldur            x1, [fp, #-0x38]
    // 0xa93a6c: ldur            x0, [fp, #-0x50]
    // 0xa93a70: add             x4, x0, x3
    // 0xa93a74: scvtf           d0, x1
    // 0xa93a78: scvtf           d1, x4
    // 0xa93a7c: fdiv            d2, d0, d1
    // 0xa93a80: stur            d2, [fp, #-0x70]
    // 0xa93a84: LoadField: r1 = r2->field_f
    //     0xa93a84: ldur            w1, [x2, #0xf]
    // 0xa93a88: DecompressPointer r1
    //     0xa93a88: add             x1, x1, HEAP, lsl #32
    // 0xa93a8c: r0 = controller()
    //     0xa93a8c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa93a90: LoadField: r1 = r0->field_7b
    //     0xa93a90: ldur            w1, [x0, #0x7b]
    // 0xa93a94: DecompressPointer r1
    //     0xa93a94: add             x1, x1, HEAP, lsl #32
    // 0xa93a98: r0 = value()
    //     0xa93a98: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa93a9c: LoadField: r1 = r0->field_b
    //     0xa93a9c: ldur            w1, [x0, #0xb]
    // 0xa93aa0: DecompressPointer r1
    //     0xa93aa0: add             x1, x1, HEAP, lsl #32
    // 0xa93aa4: cmp             w1, NULL
    // 0xa93aa8: b.ne            #0xa93ab4
    // 0xa93aac: r0 = Null
    //     0xa93aac: mov             x0, NULL
    // 0xa93ab0: b               #0xa93aec
    // 0xa93ab4: LoadField: r0 = r1->field_f
    //     0xa93ab4: ldur            w0, [x1, #0xf]
    // 0xa93ab8: DecompressPointer r0
    //     0xa93ab8: add             x0, x0, HEAP, lsl #32
    // 0xa93abc: cmp             w0, NULL
    // 0xa93ac0: b.ne            #0xa93acc
    // 0xa93ac4: r0 = Null
    //     0xa93ac4: mov             x0, NULL
    // 0xa93ac8: b               #0xa93aec
    // 0xa93acc: LoadField: r1 = r0->field_1b
    //     0xa93acc: ldur            w1, [x0, #0x1b]
    // 0xa93ad0: DecompressPointer r1
    //     0xa93ad0: add             x1, x1, HEAP, lsl #32
    // 0xa93ad4: cmp             w1, NULL
    // 0xa93ad8: b.ne            #0xa93ae4
    // 0xa93adc: r0 = Null
    //     0xa93adc: mov             x0, NULL
    // 0xa93ae0: b               #0xa93aec
    // 0xa93ae4: LoadField: r0 = r1->field_b
    //     0xa93ae4: ldur            w0, [x1, #0xb]
    // 0xa93ae8: DecompressPointer r0
    //     0xa93ae8: add             x0, x0, HEAP, lsl #32
    // 0xa93aec: cmp             w0, NULL
    // 0xa93af0: b.ne            #0xa93afc
    // 0xa93af4: r5 = 0
    //     0xa93af4: movz            x5, #0
    // 0xa93af8: b               #0xa93b0c
    // 0xa93afc: r1 = LoadInt32Instr(r0)
    //     0xa93afc: sbfx            x1, x0, #1, #0x1f
    //     0xa93b00: tbz             w0, #0, #0xa93b08
    //     0xa93b04: ldur            x1, [x0, #7]
    // 0xa93b08: mov             x5, x1
    // 0xa93b0c: ldur            x0, [fp, #-8]
    // 0xa93b10: ldur            x1, [fp, #-0x28]
    // 0xa93b14: ldur            x2, [fp, #-0x18]
    // 0xa93b18: ldur            d0, [fp, #-0x70]
    // 0xa93b1c: r3 = "4"
    //     0xa93b1c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8a0] "4"
    //     0xa93b20: ldr             x3, [x3, #0x8a0]
    // 0xa93b24: r0 = chartRow()
    //     0xa93b24: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xa93b28: ldur            x2, [fp, #-8]
    // 0xa93b2c: stur            x0, [fp, #-0x58]
    // 0xa93b30: LoadField: r3 = r2->field_f
    //     0xa93b30: ldur            w3, [x2, #0xf]
    // 0xa93b34: DecompressPointer r3
    //     0xa93b34: add             x3, x3, HEAP, lsl #32
    // 0xa93b38: stur            x3, [fp, #-0x28]
    // 0xa93b3c: LoadField: r4 = r2->field_13
    //     0xa93b3c: ldur            w4, [x2, #0x13]
    // 0xa93b40: DecompressPointer r4
    //     0xa93b40: add             x4, x4, HEAP, lsl #32
    // 0xa93b44: mov             x1, x3
    // 0xa93b48: stur            x4, [fp, #-0x18]
    // 0xa93b4c: r0 = controller()
    //     0xa93b4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa93b50: LoadField: r1 = r0->field_7b
    //     0xa93b50: ldur            w1, [x0, #0x7b]
    // 0xa93b54: DecompressPointer r1
    //     0xa93b54: add             x1, x1, HEAP, lsl #32
    // 0xa93b58: r0 = value()
    //     0xa93b58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa93b5c: LoadField: r1 = r0->field_b
    //     0xa93b5c: ldur            w1, [x0, #0xb]
    // 0xa93b60: DecompressPointer r1
    //     0xa93b60: add             x1, x1, HEAP, lsl #32
    // 0xa93b64: cmp             w1, NULL
    // 0xa93b68: b.ne            #0xa93b74
    // 0xa93b6c: r0 = Null
    //     0xa93b6c: mov             x0, NULL
    // 0xa93b70: b               #0xa93bac
    // 0xa93b74: LoadField: r0 = r1->field_f
    //     0xa93b74: ldur            w0, [x1, #0xf]
    // 0xa93b78: DecompressPointer r0
    //     0xa93b78: add             x0, x0, HEAP, lsl #32
    // 0xa93b7c: cmp             w0, NULL
    // 0xa93b80: b.ne            #0xa93b8c
    // 0xa93b84: r0 = Null
    //     0xa93b84: mov             x0, NULL
    // 0xa93b88: b               #0xa93bac
    // 0xa93b8c: LoadField: r1 = r0->field_1b
    //     0xa93b8c: ldur            w1, [x0, #0x1b]
    // 0xa93b90: DecompressPointer r1
    //     0xa93b90: add             x1, x1, HEAP, lsl #32
    // 0xa93b94: cmp             w1, NULL
    // 0xa93b98: b.ne            #0xa93ba4
    // 0xa93b9c: r0 = Null
    //     0xa93b9c: mov             x0, NULL
    // 0xa93ba0: b               #0xa93bac
    // 0xa93ba4: LoadField: r0 = r1->field_f
    //     0xa93ba4: ldur            w0, [x1, #0xf]
    // 0xa93ba8: DecompressPointer r0
    //     0xa93ba8: add             x0, x0, HEAP, lsl #32
    // 0xa93bac: cmp             w0, NULL
    // 0xa93bb0: b.ne            #0xa93bbc
    // 0xa93bb4: r0 = 0
    //     0xa93bb4: movz            x0, #0
    // 0xa93bb8: b               #0xa93bcc
    // 0xa93bbc: r1 = LoadInt32Instr(r0)
    //     0xa93bbc: sbfx            x1, x0, #1, #0x1f
    //     0xa93bc0: tbz             w0, #0, #0xa93bc8
    //     0xa93bc4: ldur            x1, [x0, #7]
    // 0xa93bc8: mov             x0, x1
    // 0xa93bcc: ldur            x2, [fp, #-8]
    // 0xa93bd0: stur            x0, [fp, #-0x38]
    // 0xa93bd4: LoadField: r1 = r2->field_f
    //     0xa93bd4: ldur            w1, [x2, #0xf]
    // 0xa93bd8: DecompressPointer r1
    //     0xa93bd8: add             x1, x1, HEAP, lsl #32
    // 0xa93bdc: r0 = controller()
    //     0xa93bdc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa93be0: LoadField: r1 = r0->field_7b
    //     0xa93be0: ldur            w1, [x0, #0x7b]
    // 0xa93be4: DecompressPointer r1
    //     0xa93be4: add             x1, x1, HEAP, lsl #32
    // 0xa93be8: r0 = value()
    //     0xa93be8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa93bec: LoadField: r1 = r0->field_b
    //     0xa93bec: ldur            w1, [x0, #0xb]
    // 0xa93bf0: DecompressPointer r1
    //     0xa93bf0: add             x1, x1, HEAP, lsl #32
    // 0xa93bf4: cmp             w1, NULL
    // 0xa93bf8: b.ne            #0xa93c04
    // 0xa93bfc: r0 = Null
    //     0xa93bfc: mov             x0, NULL
    // 0xa93c00: b               #0xa93c3c
    // 0xa93c04: LoadField: r0 = r1->field_f
    //     0xa93c04: ldur            w0, [x1, #0xf]
    // 0xa93c08: DecompressPointer r0
    //     0xa93c08: add             x0, x0, HEAP, lsl #32
    // 0xa93c0c: cmp             w0, NULL
    // 0xa93c10: b.ne            #0xa93c1c
    // 0xa93c14: r0 = Null
    //     0xa93c14: mov             x0, NULL
    // 0xa93c18: b               #0xa93c3c
    // 0xa93c1c: LoadField: r1 = r0->field_1b
    //     0xa93c1c: ldur            w1, [x0, #0x1b]
    // 0xa93c20: DecompressPointer r1
    //     0xa93c20: add             x1, x1, HEAP, lsl #32
    // 0xa93c24: cmp             w1, NULL
    // 0xa93c28: b.ne            #0xa93c34
    // 0xa93c2c: r0 = Null
    //     0xa93c2c: mov             x0, NULL
    // 0xa93c30: b               #0xa93c3c
    // 0xa93c34: LoadField: r0 = r1->field_7
    //     0xa93c34: ldur            w0, [x1, #7]
    // 0xa93c38: DecompressPointer r0
    //     0xa93c38: add             x0, x0, HEAP, lsl #32
    // 0xa93c3c: cmp             w0, NULL
    // 0xa93c40: b.ne            #0xa93c4c
    // 0xa93c44: r0 = 0
    //     0xa93c44: movz            x0, #0
    // 0xa93c48: b               #0xa93c5c
    // 0xa93c4c: r1 = LoadInt32Instr(r0)
    //     0xa93c4c: sbfx            x1, x0, #1, #0x1f
    //     0xa93c50: tbz             w0, #0, #0xa93c58
    //     0xa93c54: ldur            x1, [x0, #7]
    // 0xa93c58: mov             x0, x1
    // 0xa93c5c: ldur            x2, [fp, #-8]
    // 0xa93c60: stur            x0, [fp, #-0x48]
    // 0xa93c64: LoadField: r1 = r2->field_f
    //     0xa93c64: ldur            w1, [x2, #0xf]
    // 0xa93c68: DecompressPointer r1
    //     0xa93c68: add             x1, x1, HEAP, lsl #32
    // 0xa93c6c: r0 = controller()
    //     0xa93c6c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa93c70: LoadField: r1 = r0->field_7b
    //     0xa93c70: ldur            w1, [x0, #0x7b]
    // 0xa93c74: DecompressPointer r1
    //     0xa93c74: add             x1, x1, HEAP, lsl #32
    // 0xa93c78: r0 = value()
    //     0xa93c78: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa93c7c: LoadField: r1 = r0->field_b
    //     0xa93c7c: ldur            w1, [x0, #0xb]
    // 0xa93c80: DecompressPointer r1
    //     0xa93c80: add             x1, x1, HEAP, lsl #32
    // 0xa93c84: cmp             w1, NULL
    // 0xa93c88: b.ne            #0xa93c94
    // 0xa93c8c: r0 = Null
    //     0xa93c8c: mov             x0, NULL
    // 0xa93c90: b               #0xa93ccc
    // 0xa93c94: LoadField: r0 = r1->field_f
    //     0xa93c94: ldur            w0, [x1, #0xf]
    // 0xa93c98: DecompressPointer r0
    //     0xa93c98: add             x0, x0, HEAP, lsl #32
    // 0xa93c9c: cmp             w0, NULL
    // 0xa93ca0: b.ne            #0xa93cac
    // 0xa93ca4: r0 = Null
    //     0xa93ca4: mov             x0, NULL
    // 0xa93ca8: b               #0xa93ccc
    // 0xa93cac: LoadField: r1 = r0->field_1b
    //     0xa93cac: ldur            w1, [x0, #0x1b]
    // 0xa93cb0: DecompressPointer r1
    //     0xa93cb0: add             x1, x1, HEAP, lsl #32
    // 0xa93cb4: cmp             w1, NULL
    // 0xa93cb8: b.ne            #0xa93cc4
    // 0xa93cbc: r0 = Null
    //     0xa93cbc: mov             x0, NULL
    // 0xa93cc0: b               #0xa93ccc
    // 0xa93cc4: LoadField: r0 = r1->field_b
    //     0xa93cc4: ldur            w0, [x1, #0xb]
    // 0xa93cc8: DecompressPointer r0
    //     0xa93cc8: add             x0, x0, HEAP, lsl #32
    // 0xa93ccc: cmp             w0, NULL
    // 0xa93cd0: b.ne            #0xa93cdc
    // 0xa93cd4: r1 = 0
    //     0xa93cd4: movz            x1, #0
    // 0xa93cd8: b               #0xa93ce8
    // 0xa93cdc: r1 = LoadInt32Instr(r0)
    //     0xa93cdc: sbfx            x1, x0, #1, #0x1f
    //     0xa93ce0: tbz             w0, #0, #0xa93ce8
    //     0xa93ce4: ldur            x1, [x0, #7]
    // 0xa93ce8: ldur            x2, [fp, #-8]
    // 0xa93cec: ldur            x0, [fp, #-0x48]
    // 0xa93cf0: add             x3, x0, x1
    // 0xa93cf4: stur            x3, [fp, #-0x50]
    // 0xa93cf8: LoadField: r1 = r2->field_f
    //     0xa93cf8: ldur            w1, [x2, #0xf]
    // 0xa93cfc: DecompressPointer r1
    //     0xa93cfc: add             x1, x1, HEAP, lsl #32
    // 0xa93d00: r0 = controller()
    //     0xa93d00: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa93d04: LoadField: r1 = r0->field_7b
    //     0xa93d04: ldur            w1, [x0, #0x7b]
    // 0xa93d08: DecompressPointer r1
    //     0xa93d08: add             x1, x1, HEAP, lsl #32
    // 0xa93d0c: r0 = value()
    //     0xa93d0c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa93d10: LoadField: r1 = r0->field_b
    //     0xa93d10: ldur            w1, [x0, #0xb]
    // 0xa93d14: DecompressPointer r1
    //     0xa93d14: add             x1, x1, HEAP, lsl #32
    // 0xa93d18: cmp             w1, NULL
    // 0xa93d1c: b.ne            #0xa93d28
    // 0xa93d20: r0 = Null
    //     0xa93d20: mov             x0, NULL
    // 0xa93d24: b               #0xa93d60
    // 0xa93d28: LoadField: r0 = r1->field_f
    //     0xa93d28: ldur            w0, [x1, #0xf]
    // 0xa93d2c: DecompressPointer r0
    //     0xa93d2c: add             x0, x0, HEAP, lsl #32
    // 0xa93d30: cmp             w0, NULL
    // 0xa93d34: b.ne            #0xa93d40
    // 0xa93d38: r0 = Null
    //     0xa93d38: mov             x0, NULL
    // 0xa93d3c: b               #0xa93d60
    // 0xa93d40: LoadField: r1 = r0->field_1b
    //     0xa93d40: ldur            w1, [x0, #0x1b]
    // 0xa93d44: DecompressPointer r1
    //     0xa93d44: add             x1, x1, HEAP, lsl #32
    // 0xa93d48: cmp             w1, NULL
    // 0xa93d4c: b.ne            #0xa93d58
    // 0xa93d50: r0 = Null
    //     0xa93d50: mov             x0, NULL
    // 0xa93d54: b               #0xa93d60
    // 0xa93d58: LoadField: r0 = r1->field_f
    //     0xa93d58: ldur            w0, [x1, #0xf]
    // 0xa93d5c: DecompressPointer r0
    //     0xa93d5c: add             x0, x0, HEAP, lsl #32
    // 0xa93d60: cmp             w0, NULL
    // 0xa93d64: b.ne            #0xa93d70
    // 0xa93d68: r1 = 0
    //     0xa93d68: movz            x1, #0
    // 0xa93d6c: b               #0xa93d7c
    // 0xa93d70: r1 = LoadInt32Instr(r0)
    //     0xa93d70: sbfx            x1, x0, #1, #0x1f
    //     0xa93d74: tbz             w0, #0, #0xa93d7c
    //     0xa93d78: ldur            x1, [x0, #7]
    // 0xa93d7c: ldur            x2, [fp, #-8]
    // 0xa93d80: ldur            x0, [fp, #-0x50]
    // 0xa93d84: add             x3, x0, x1
    // 0xa93d88: stur            x3, [fp, #-0x48]
    // 0xa93d8c: LoadField: r1 = r2->field_f
    //     0xa93d8c: ldur            w1, [x2, #0xf]
    // 0xa93d90: DecompressPointer r1
    //     0xa93d90: add             x1, x1, HEAP, lsl #32
    // 0xa93d94: r0 = controller()
    //     0xa93d94: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa93d98: LoadField: r1 = r0->field_7b
    //     0xa93d98: ldur            w1, [x0, #0x7b]
    // 0xa93d9c: DecompressPointer r1
    //     0xa93d9c: add             x1, x1, HEAP, lsl #32
    // 0xa93da0: r0 = value()
    //     0xa93da0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa93da4: LoadField: r1 = r0->field_b
    //     0xa93da4: ldur            w1, [x0, #0xb]
    // 0xa93da8: DecompressPointer r1
    //     0xa93da8: add             x1, x1, HEAP, lsl #32
    // 0xa93dac: cmp             w1, NULL
    // 0xa93db0: b.ne            #0xa93dbc
    // 0xa93db4: r0 = Null
    //     0xa93db4: mov             x0, NULL
    // 0xa93db8: b               #0xa93df4
    // 0xa93dbc: LoadField: r0 = r1->field_f
    //     0xa93dbc: ldur            w0, [x1, #0xf]
    // 0xa93dc0: DecompressPointer r0
    //     0xa93dc0: add             x0, x0, HEAP, lsl #32
    // 0xa93dc4: cmp             w0, NULL
    // 0xa93dc8: b.ne            #0xa93dd4
    // 0xa93dcc: r0 = Null
    //     0xa93dcc: mov             x0, NULL
    // 0xa93dd0: b               #0xa93df4
    // 0xa93dd4: LoadField: r1 = r0->field_1b
    //     0xa93dd4: ldur            w1, [x0, #0x1b]
    // 0xa93dd8: DecompressPointer r1
    //     0xa93dd8: add             x1, x1, HEAP, lsl #32
    // 0xa93ddc: cmp             w1, NULL
    // 0xa93de0: b.ne            #0xa93dec
    // 0xa93de4: r0 = Null
    //     0xa93de4: mov             x0, NULL
    // 0xa93de8: b               #0xa93df4
    // 0xa93dec: LoadField: r0 = r1->field_13
    //     0xa93dec: ldur            w0, [x1, #0x13]
    // 0xa93df0: DecompressPointer r0
    //     0xa93df0: add             x0, x0, HEAP, lsl #32
    // 0xa93df4: cmp             w0, NULL
    // 0xa93df8: b.ne            #0xa93e04
    // 0xa93dfc: r1 = 0
    //     0xa93dfc: movz            x1, #0
    // 0xa93e00: b               #0xa93e10
    // 0xa93e04: r1 = LoadInt32Instr(r0)
    //     0xa93e04: sbfx            x1, x0, #1, #0x1f
    //     0xa93e08: tbz             w0, #0, #0xa93e10
    //     0xa93e0c: ldur            x1, [x0, #7]
    // 0xa93e10: ldur            x2, [fp, #-8]
    // 0xa93e14: ldur            x0, [fp, #-0x48]
    // 0xa93e18: add             x3, x0, x1
    // 0xa93e1c: stur            x3, [fp, #-0x50]
    // 0xa93e20: LoadField: r1 = r2->field_f
    //     0xa93e20: ldur            w1, [x2, #0xf]
    // 0xa93e24: DecompressPointer r1
    //     0xa93e24: add             x1, x1, HEAP, lsl #32
    // 0xa93e28: r0 = controller()
    //     0xa93e28: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa93e2c: LoadField: r1 = r0->field_7b
    //     0xa93e2c: ldur            w1, [x0, #0x7b]
    // 0xa93e30: DecompressPointer r1
    //     0xa93e30: add             x1, x1, HEAP, lsl #32
    // 0xa93e34: r0 = value()
    //     0xa93e34: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa93e38: LoadField: r1 = r0->field_b
    //     0xa93e38: ldur            w1, [x0, #0xb]
    // 0xa93e3c: DecompressPointer r1
    //     0xa93e3c: add             x1, x1, HEAP, lsl #32
    // 0xa93e40: cmp             w1, NULL
    // 0xa93e44: b.ne            #0xa93e50
    // 0xa93e48: r0 = Null
    //     0xa93e48: mov             x0, NULL
    // 0xa93e4c: b               #0xa93e88
    // 0xa93e50: LoadField: r0 = r1->field_f
    //     0xa93e50: ldur            w0, [x1, #0xf]
    // 0xa93e54: DecompressPointer r0
    //     0xa93e54: add             x0, x0, HEAP, lsl #32
    // 0xa93e58: cmp             w0, NULL
    // 0xa93e5c: b.ne            #0xa93e68
    // 0xa93e60: r0 = Null
    //     0xa93e60: mov             x0, NULL
    // 0xa93e64: b               #0xa93e88
    // 0xa93e68: LoadField: r1 = r0->field_1b
    //     0xa93e68: ldur            w1, [x0, #0x1b]
    // 0xa93e6c: DecompressPointer r1
    //     0xa93e6c: add             x1, x1, HEAP, lsl #32
    // 0xa93e70: cmp             w1, NULL
    // 0xa93e74: b.ne            #0xa93e80
    // 0xa93e78: r0 = Null
    //     0xa93e78: mov             x0, NULL
    // 0xa93e7c: b               #0xa93e88
    // 0xa93e80: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa93e80: ldur            w0, [x1, #0x17]
    // 0xa93e84: DecompressPointer r0
    //     0xa93e84: add             x0, x0, HEAP, lsl #32
    // 0xa93e88: cmp             w0, NULL
    // 0xa93e8c: b.ne            #0xa93e98
    // 0xa93e90: r3 = 0
    //     0xa93e90: movz            x3, #0
    // 0xa93e94: b               #0xa93ea8
    // 0xa93e98: r1 = LoadInt32Instr(r0)
    //     0xa93e98: sbfx            x1, x0, #1, #0x1f
    //     0xa93e9c: tbz             w0, #0, #0xa93ea4
    //     0xa93ea0: ldur            x1, [x0, #7]
    // 0xa93ea4: mov             x3, x1
    // 0xa93ea8: ldur            x2, [fp, #-8]
    // 0xa93eac: ldur            x1, [fp, #-0x38]
    // 0xa93eb0: ldur            x0, [fp, #-0x50]
    // 0xa93eb4: add             x4, x0, x3
    // 0xa93eb8: scvtf           d0, x1
    // 0xa93ebc: scvtf           d1, x4
    // 0xa93ec0: fdiv            d2, d0, d1
    // 0xa93ec4: stur            d2, [fp, #-0x70]
    // 0xa93ec8: LoadField: r1 = r2->field_f
    //     0xa93ec8: ldur            w1, [x2, #0xf]
    // 0xa93ecc: DecompressPointer r1
    //     0xa93ecc: add             x1, x1, HEAP, lsl #32
    // 0xa93ed0: r0 = controller()
    //     0xa93ed0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa93ed4: LoadField: r1 = r0->field_7b
    //     0xa93ed4: ldur            w1, [x0, #0x7b]
    // 0xa93ed8: DecompressPointer r1
    //     0xa93ed8: add             x1, x1, HEAP, lsl #32
    // 0xa93edc: r0 = value()
    //     0xa93edc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa93ee0: LoadField: r1 = r0->field_b
    //     0xa93ee0: ldur            w1, [x0, #0xb]
    // 0xa93ee4: DecompressPointer r1
    //     0xa93ee4: add             x1, x1, HEAP, lsl #32
    // 0xa93ee8: cmp             w1, NULL
    // 0xa93eec: b.ne            #0xa93ef8
    // 0xa93ef0: r0 = Null
    //     0xa93ef0: mov             x0, NULL
    // 0xa93ef4: b               #0xa93f30
    // 0xa93ef8: LoadField: r0 = r1->field_f
    //     0xa93ef8: ldur            w0, [x1, #0xf]
    // 0xa93efc: DecompressPointer r0
    //     0xa93efc: add             x0, x0, HEAP, lsl #32
    // 0xa93f00: cmp             w0, NULL
    // 0xa93f04: b.ne            #0xa93f10
    // 0xa93f08: r0 = Null
    //     0xa93f08: mov             x0, NULL
    // 0xa93f0c: b               #0xa93f30
    // 0xa93f10: LoadField: r1 = r0->field_1b
    //     0xa93f10: ldur            w1, [x0, #0x1b]
    // 0xa93f14: DecompressPointer r1
    //     0xa93f14: add             x1, x1, HEAP, lsl #32
    // 0xa93f18: cmp             w1, NULL
    // 0xa93f1c: b.ne            #0xa93f28
    // 0xa93f20: r0 = Null
    //     0xa93f20: mov             x0, NULL
    // 0xa93f24: b               #0xa93f30
    // 0xa93f28: LoadField: r0 = r1->field_f
    //     0xa93f28: ldur            w0, [x1, #0xf]
    // 0xa93f2c: DecompressPointer r0
    //     0xa93f2c: add             x0, x0, HEAP, lsl #32
    // 0xa93f30: cmp             w0, NULL
    // 0xa93f34: b.ne            #0xa93f40
    // 0xa93f38: r5 = 0
    //     0xa93f38: movz            x5, #0
    // 0xa93f3c: b               #0xa93f50
    // 0xa93f40: r1 = LoadInt32Instr(r0)
    //     0xa93f40: sbfx            x1, x0, #1, #0x1f
    //     0xa93f44: tbz             w0, #0, #0xa93f4c
    //     0xa93f48: ldur            x1, [x0, #7]
    // 0xa93f4c: mov             x5, x1
    // 0xa93f50: ldur            x0, [fp, #-8]
    // 0xa93f54: ldur            x1, [fp, #-0x28]
    // 0xa93f58: ldur            x2, [fp, #-0x18]
    // 0xa93f5c: ldur            d0, [fp, #-0x70]
    // 0xa93f60: r3 = "3"
    //     0xa93f60: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8a8] "3"
    //     0xa93f64: ldr             x3, [x3, #0x8a8]
    // 0xa93f68: r0 = chartRow()
    //     0xa93f68: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xa93f6c: ldur            x2, [fp, #-8]
    // 0xa93f70: stur            x0, [fp, #-0x60]
    // 0xa93f74: LoadField: r3 = r2->field_f
    //     0xa93f74: ldur            w3, [x2, #0xf]
    // 0xa93f78: DecompressPointer r3
    //     0xa93f78: add             x3, x3, HEAP, lsl #32
    // 0xa93f7c: stur            x3, [fp, #-0x28]
    // 0xa93f80: LoadField: r4 = r2->field_13
    //     0xa93f80: ldur            w4, [x2, #0x13]
    // 0xa93f84: DecompressPointer r4
    //     0xa93f84: add             x4, x4, HEAP, lsl #32
    // 0xa93f88: mov             x1, x3
    // 0xa93f8c: stur            x4, [fp, #-0x18]
    // 0xa93f90: r0 = controller()
    //     0xa93f90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa93f94: LoadField: r1 = r0->field_7b
    //     0xa93f94: ldur            w1, [x0, #0x7b]
    // 0xa93f98: DecompressPointer r1
    //     0xa93f98: add             x1, x1, HEAP, lsl #32
    // 0xa93f9c: r0 = value()
    //     0xa93f9c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa93fa0: LoadField: r1 = r0->field_b
    //     0xa93fa0: ldur            w1, [x0, #0xb]
    // 0xa93fa4: DecompressPointer r1
    //     0xa93fa4: add             x1, x1, HEAP, lsl #32
    // 0xa93fa8: cmp             w1, NULL
    // 0xa93fac: b.ne            #0xa93fb8
    // 0xa93fb0: r0 = Null
    //     0xa93fb0: mov             x0, NULL
    // 0xa93fb4: b               #0xa93ff0
    // 0xa93fb8: LoadField: r0 = r1->field_f
    //     0xa93fb8: ldur            w0, [x1, #0xf]
    // 0xa93fbc: DecompressPointer r0
    //     0xa93fbc: add             x0, x0, HEAP, lsl #32
    // 0xa93fc0: cmp             w0, NULL
    // 0xa93fc4: b.ne            #0xa93fd0
    // 0xa93fc8: r0 = Null
    //     0xa93fc8: mov             x0, NULL
    // 0xa93fcc: b               #0xa93ff0
    // 0xa93fd0: LoadField: r1 = r0->field_1b
    //     0xa93fd0: ldur            w1, [x0, #0x1b]
    // 0xa93fd4: DecompressPointer r1
    //     0xa93fd4: add             x1, x1, HEAP, lsl #32
    // 0xa93fd8: cmp             w1, NULL
    // 0xa93fdc: b.ne            #0xa93fe8
    // 0xa93fe0: r0 = Null
    //     0xa93fe0: mov             x0, NULL
    // 0xa93fe4: b               #0xa93ff0
    // 0xa93fe8: LoadField: r0 = r1->field_13
    //     0xa93fe8: ldur            w0, [x1, #0x13]
    // 0xa93fec: DecompressPointer r0
    //     0xa93fec: add             x0, x0, HEAP, lsl #32
    // 0xa93ff0: cmp             w0, NULL
    // 0xa93ff4: b.ne            #0xa94000
    // 0xa93ff8: r0 = 0
    //     0xa93ff8: movz            x0, #0
    // 0xa93ffc: b               #0xa94010
    // 0xa94000: r1 = LoadInt32Instr(r0)
    //     0xa94000: sbfx            x1, x0, #1, #0x1f
    //     0xa94004: tbz             w0, #0, #0xa9400c
    //     0xa94008: ldur            x1, [x0, #7]
    // 0xa9400c: mov             x0, x1
    // 0xa94010: ldur            x2, [fp, #-8]
    // 0xa94014: stur            x0, [fp, #-0x38]
    // 0xa94018: LoadField: r1 = r2->field_f
    //     0xa94018: ldur            w1, [x2, #0xf]
    // 0xa9401c: DecompressPointer r1
    //     0xa9401c: add             x1, x1, HEAP, lsl #32
    // 0xa94020: r0 = controller()
    //     0xa94020: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa94024: LoadField: r1 = r0->field_7b
    //     0xa94024: ldur            w1, [x0, #0x7b]
    // 0xa94028: DecompressPointer r1
    //     0xa94028: add             x1, x1, HEAP, lsl #32
    // 0xa9402c: r0 = value()
    //     0xa9402c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa94030: LoadField: r1 = r0->field_b
    //     0xa94030: ldur            w1, [x0, #0xb]
    // 0xa94034: DecompressPointer r1
    //     0xa94034: add             x1, x1, HEAP, lsl #32
    // 0xa94038: cmp             w1, NULL
    // 0xa9403c: b.ne            #0xa94048
    // 0xa94040: r0 = Null
    //     0xa94040: mov             x0, NULL
    // 0xa94044: b               #0xa94080
    // 0xa94048: LoadField: r0 = r1->field_f
    //     0xa94048: ldur            w0, [x1, #0xf]
    // 0xa9404c: DecompressPointer r0
    //     0xa9404c: add             x0, x0, HEAP, lsl #32
    // 0xa94050: cmp             w0, NULL
    // 0xa94054: b.ne            #0xa94060
    // 0xa94058: r0 = Null
    //     0xa94058: mov             x0, NULL
    // 0xa9405c: b               #0xa94080
    // 0xa94060: LoadField: r1 = r0->field_1b
    //     0xa94060: ldur            w1, [x0, #0x1b]
    // 0xa94064: DecompressPointer r1
    //     0xa94064: add             x1, x1, HEAP, lsl #32
    // 0xa94068: cmp             w1, NULL
    // 0xa9406c: b.ne            #0xa94078
    // 0xa94070: r0 = Null
    //     0xa94070: mov             x0, NULL
    // 0xa94074: b               #0xa94080
    // 0xa94078: LoadField: r0 = r1->field_7
    //     0xa94078: ldur            w0, [x1, #7]
    // 0xa9407c: DecompressPointer r0
    //     0xa9407c: add             x0, x0, HEAP, lsl #32
    // 0xa94080: cmp             w0, NULL
    // 0xa94084: b.ne            #0xa94090
    // 0xa94088: r0 = 0
    //     0xa94088: movz            x0, #0
    // 0xa9408c: b               #0xa940a0
    // 0xa94090: r1 = LoadInt32Instr(r0)
    //     0xa94090: sbfx            x1, x0, #1, #0x1f
    //     0xa94094: tbz             w0, #0, #0xa9409c
    //     0xa94098: ldur            x1, [x0, #7]
    // 0xa9409c: mov             x0, x1
    // 0xa940a0: ldur            x2, [fp, #-8]
    // 0xa940a4: stur            x0, [fp, #-0x48]
    // 0xa940a8: LoadField: r1 = r2->field_f
    //     0xa940a8: ldur            w1, [x2, #0xf]
    // 0xa940ac: DecompressPointer r1
    //     0xa940ac: add             x1, x1, HEAP, lsl #32
    // 0xa940b0: r0 = controller()
    //     0xa940b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa940b4: LoadField: r1 = r0->field_7b
    //     0xa940b4: ldur            w1, [x0, #0x7b]
    // 0xa940b8: DecompressPointer r1
    //     0xa940b8: add             x1, x1, HEAP, lsl #32
    // 0xa940bc: r0 = value()
    //     0xa940bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa940c0: LoadField: r1 = r0->field_b
    //     0xa940c0: ldur            w1, [x0, #0xb]
    // 0xa940c4: DecompressPointer r1
    //     0xa940c4: add             x1, x1, HEAP, lsl #32
    // 0xa940c8: cmp             w1, NULL
    // 0xa940cc: b.ne            #0xa940d8
    // 0xa940d0: r0 = Null
    //     0xa940d0: mov             x0, NULL
    // 0xa940d4: b               #0xa94110
    // 0xa940d8: LoadField: r0 = r1->field_f
    //     0xa940d8: ldur            w0, [x1, #0xf]
    // 0xa940dc: DecompressPointer r0
    //     0xa940dc: add             x0, x0, HEAP, lsl #32
    // 0xa940e0: cmp             w0, NULL
    // 0xa940e4: b.ne            #0xa940f0
    // 0xa940e8: r0 = Null
    //     0xa940e8: mov             x0, NULL
    // 0xa940ec: b               #0xa94110
    // 0xa940f0: LoadField: r1 = r0->field_1b
    //     0xa940f0: ldur            w1, [x0, #0x1b]
    // 0xa940f4: DecompressPointer r1
    //     0xa940f4: add             x1, x1, HEAP, lsl #32
    // 0xa940f8: cmp             w1, NULL
    // 0xa940fc: b.ne            #0xa94108
    // 0xa94100: r0 = Null
    //     0xa94100: mov             x0, NULL
    // 0xa94104: b               #0xa94110
    // 0xa94108: LoadField: r0 = r1->field_b
    //     0xa94108: ldur            w0, [x1, #0xb]
    // 0xa9410c: DecompressPointer r0
    //     0xa9410c: add             x0, x0, HEAP, lsl #32
    // 0xa94110: cmp             w0, NULL
    // 0xa94114: b.ne            #0xa94120
    // 0xa94118: r1 = 0
    //     0xa94118: movz            x1, #0
    // 0xa9411c: b               #0xa9412c
    // 0xa94120: r1 = LoadInt32Instr(r0)
    //     0xa94120: sbfx            x1, x0, #1, #0x1f
    //     0xa94124: tbz             w0, #0, #0xa9412c
    //     0xa94128: ldur            x1, [x0, #7]
    // 0xa9412c: ldur            x2, [fp, #-8]
    // 0xa94130: ldur            x0, [fp, #-0x48]
    // 0xa94134: add             x3, x0, x1
    // 0xa94138: stur            x3, [fp, #-0x50]
    // 0xa9413c: LoadField: r1 = r2->field_f
    //     0xa9413c: ldur            w1, [x2, #0xf]
    // 0xa94140: DecompressPointer r1
    //     0xa94140: add             x1, x1, HEAP, lsl #32
    // 0xa94144: r0 = controller()
    //     0xa94144: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa94148: LoadField: r1 = r0->field_7b
    //     0xa94148: ldur            w1, [x0, #0x7b]
    // 0xa9414c: DecompressPointer r1
    //     0xa9414c: add             x1, x1, HEAP, lsl #32
    // 0xa94150: r0 = value()
    //     0xa94150: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa94154: LoadField: r1 = r0->field_b
    //     0xa94154: ldur            w1, [x0, #0xb]
    // 0xa94158: DecompressPointer r1
    //     0xa94158: add             x1, x1, HEAP, lsl #32
    // 0xa9415c: cmp             w1, NULL
    // 0xa94160: b.ne            #0xa9416c
    // 0xa94164: r0 = Null
    //     0xa94164: mov             x0, NULL
    // 0xa94168: b               #0xa941a4
    // 0xa9416c: LoadField: r0 = r1->field_f
    //     0xa9416c: ldur            w0, [x1, #0xf]
    // 0xa94170: DecompressPointer r0
    //     0xa94170: add             x0, x0, HEAP, lsl #32
    // 0xa94174: cmp             w0, NULL
    // 0xa94178: b.ne            #0xa94184
    // 0xa9417c: r0 = Null
    //     0xa9417c: mov             x0, NULL
    // 0xa94180: b               #0xa941a4
    // 0xa94184: LoadField: r1 = r0->field_1b
    //     0xa94184: ldur            w1, [x0, #0x1b]
    // 0xa94188: DecompressPointer r1
    //     0xa94188: add             x1, x1, HEAP, lsl #32
    // 0xa9418c: cmp             w1, NULL
    // 0xa94190: b.ne            #0xa9419c
    // 0xa94194: r0 = Null
    //     0xa94194: mov             x0, NULL
    // 0xa94198: b               #0xa941a4
    // 0xa9419c: LoadField: r0 = r1->field_f
    //     0xa9419c: ldur            w0, [x1, #0xf]
    // 0xa941a0: DecompressPointer r0
    //     0xa941a0: add             x0, x0, HEAP, lsl #32
    // 0xa941a4: cmp             w0, NULL
    // 0xa941a8: b.ne            #0xa941b4
    // 0xa941ac: r1 = 0
    //     0xa941ac: movz            x1, #0
    // 0xa941b0: b               #0xa941c0
    // 0xa941b4: r1 = LoadInt32Instr(r0)
    //     0xa941b4: sbfx            x1, x0, #1, #0x1f
    //     0xa941b8: tbz             w0, #0, #0xa941c0
    //     0xa941bc: ldur            x1, [x0, #7]
    // 0xa941c0: ldur            x2, [fp, #-8]
    // 0xa941c4: ldur            x0, [fp, #-0x50]
    // 0xa941c8: add             x3, x0, x1
    // 0xa941cc: stur            x3, [fp, #-0x48]
    // 0xa941d0: LoadField: r1 = r2->field_f
    //     0xa941d0: ldur            w1, [x2, #0xf]
    // 0xa941d4: DecompressPointer r1
    //     0xa941d4: add             x1, x1, HEAP, lsl #32
    // 0xa941d8: r0 = controller()
    //     0xa941d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa941dc: LoadField: r1 = r0->field_7b
    //     0xa941dc: ldur            w1, [x0, #0x7b]
    // 0xa941e0: DecompressPointer r1
    //     0xa941e0: add             x1, x1, HEAP, lsl #32
    // 0xa941e4: r0 = value()
    //     0xa941e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa941e8: LoadField: r1 = r0->field_b
    //     0xa941e8: ldur            w1, [x0, #0xb]
    // 0xa941ec: DecompressPointer r1
    //     0xa941ec: add             x1, x1, HEAP, lsl #32
    // 0xa941f0: cmp             w1, NULL
    // 0xa941f4: b.ne            #0xa94200
    // 0xa941f8: r0 = Null
    //     0xa941f8: mov             x0, NULL
    // 0xa941fc: b               #0xa94238
    // 0xa94200: LoadField: r0 = r1->field_f
    //     0xa94200: ldur            w0, [x1, #0xf]
    // 0xa94204: DecompressPointer r0
    //     0xa94204: add             x0, x0, HEAP, lsl #32
    // 0xa94208: cmp             w0, NULL
    // 0xa9420c: b.ne            #0xa94218
    // 0xa94210: r0 = Null
    //     0xa94210: mov             x0, NULL
    // 0xa94214: b               #0xa94238
    // 0xa94218: LoadField: r1 = r0->field_1b
    //     0xa94218: ldur            w1, [x0, #0x1b]
    // 0xa9421c: DecompressPointer r1
    //     0xa9421c: add             x1, x1, HEAP, lsl #32
    // 0xa94220: cmp             w1, NULL
    // 0xa94224: b.ne            #0xa94230
    // 0xa94228: r0 = Null
    //     0xa94228: mov             x0, NULL
    // 0xa9422c: b               #0xa94238
    // 0xa94230: LoadField: r0 = r1->field_13
    //     0xa94230: ldur            w0, [x1, #0x13]
    // 0xa94234: DecompressPointer r0
    //     0xa94234: add             x0, x0, HEAP, lsl #32
    // 0xa94238: cmp             w0, NULL
    // 0xa9423c: b.ne            #0xa94248
    // 0xa94240: r1 = 0
    //     0xa94240: movz            x1, #0
    // 0xa94244: b               #0xa94254
    // 0xa94248: r1 = LoadInt32Instr(r0)
    //     0xa94248: sbfx            x1, x0, #1, #0x1f
    //     0xa9424c: tbz             w0, #0, #0xa94254
    //     0xa94250: ldur            x1, [x0, #7]
    // 0xa94254: ldur            x2, [fp, #-8]
    // 0xa94258: ldur            x0, [fp, #-0x48]
    // 0xa9425c: add             x3, x0, x1
    // 0xa94260: stur            x3, [fp, #-0x50]
    // 0xa94264: LoadField: r1 = r2->field_f
    //     0xa94264: ldur            w1, [x2, #0xf]
    // 0xa94268: DecompressPointer r1
    //     0xa94268: add             x1, x1, HEAP, lsl #32
    // 0xa9426c: r0 = controller()
    //     0xa9426c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa94270: LoadField: r1 = r0->field_7b
    //     0xa94270: ldur            w1, [x0, #0x7b]
    // 0xa94274: DecompressPointer r1
    //     0xa94274: add             x1, x1, HEAP, lsl #32
    // 0xa94278: r0 = value()
    //     0xa94278: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa9427c: LoadField: r1 = r0->field_b
    //     0xa9427c: ldur            w1, [x0, #0xb]
    // 0xa94280: DecompressPointer r1
    //     0xa94280: add             x1, x1, HEAP, lsl #32
    // 0xa94284: cmp             w1, NULL
    // 0xa94288: b.ne            #0xa94294
    // 0xa9428c: r0 = Null
    //     0xa9428c: mov             x0, NULL
    // 0xa94290: b               #0xa942cc
    // 0xa94294: LoadField: r0 = r1->field_f
    //     0xa94294: ldur            w0, [x1, #0xf]
    // 0xa94298: DecompressPointer r0
    //     0xa94298: add             x0, x0, HEAP, lsl #32
    // 0xa9429c: cmp             w0, NULL
    // 0xa942a0: b.ne            #0xa942ac
    // 0xa942a4: r0 = Null
    //     0xa942a4: mov             x0, NULL
    // 0xa942a8: b               #0xa942cc
    // 0xa942ac: LoadField: r1 = r0->field_1b
    //     0xa942ac: ldur            w1, [x0, #0x1b]
    // 0xa942b0: DecompressPointer r1
    //     0xa942b0: add             x1, x1, HEAP, lsl #32
    // 0xa942b4: cmp             w1, NULL
    // 0xa942b8: b.ne            #0xa942c4
    // 0xa942bc: r0 = Null
    //     0xa942bc: mov             x0, NULL
    // 0xa942c0: b               #0xa942cc
    // 0xa942c4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa942c4: ldur            w0, [x1, #0x17]
    // 0xa942c8: DecompressPointer r0
    //     0xa942c8: add             x0, x0, HEAP, lsl #32
    // 0xa942cc: cmp             w0, NULL
    // 0xa942d0: b.ne            #0xa942dc
    // 0xa942d4: r3 = 0
    //     0xa942d4: movz            x3, #0
    // 0xa942d8: b               #0xa942ec
    // 0xa942dc: r1 = LoadInt32Instr(r0)
    //     0xa942dc: sbfx            x1, x0, #1, #0x1f
    //     0xa942e0: tbz             w0, #0, #0xa942e8
    //     0xa942e4: ldur            x1, [x0, #7]
    // 0xa942e8: mov             x3, x1
    // 0xa942ec: ldur            x2, [fp, #-8]
    // 0xa942f0: ldur            x1, [fp, #-0x38]
    // 0xa942f4: ldur            x0, [fp, #-0x50]
    // 0xa942f8: add             x4, x0, x3
    // 0xa942fc: scvtf           d0, x1
    // 0xa94300: scvtf           d1, x4
    // 0xa94304: fdiv            d2, d0, d1
    // 0xa94308: stur            d2, [fp, #-0x70]
    // 0xa9430c: LoadField: r1 = r2->field_f
    //     0xa9430c: ldur            w1, [x2, #0xf]
    // 0xa94310: DecompressPointer r1
    //     0xa94310: add             x1, x1, HEAP, lsl #32
    // 0xa94314: r0 = controller()
    //     0xa94314: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa94318: LoadField: r1 = r0->field_7b
    //     0xa94318: ldur            w1, [x0, #0x7b]
    // 0xa9431c: DecompressPointer r1
    //     0xa9431c: add             x1, x1, HEAP, lsl #32
    // 0xa94320: r0 = value()
    //     0xa94320: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa94324: LoadField: r1 = r0->field_b
    //     0xa94324: ldur            w1, [x0, #0xb]
    // 0xa94328: DecompressPointer r1
    //     0xa94328: add             x1, x1, HEAP, lsl #32
    // 0xa9432c: cmp             w1, NULL
    // 0xa94330: b.ne            #0xa9433c
    // 0xa94334: r0 = Null
    //     0xa94334: mov             x0, NULL
    // 0xa94338: b               #0xa94374
    // 0xa9433c: LoadField: r0 = r1->field_f
    //     0xa9433c: ldur            w0, [x1, #0xf]
    // 0xa94340: DecompressPointer r0
    //     0xa94340: add             x0, x0, HEAP, lsl #32
    // 0xa94344: cmp             w0, NULL
    // 0xa94348: b.ne            #0xa94354
    // 0xa9434c: r0 = Null
    //     0xa9434c: mov             x0, NULL
    // 0xa94350: b               #0xa94374
    // 0xa94354: LoadField: r1 = r0->field_1b
    //     0xa94354: ldur            w1, [x0, #0x1b]
    // 0xa94358: DecompressPointer r1
    //     0xa94358: add             x1, x1, HEAP, lsl #32
    // 0xa9435c: cmp             w1, NULL
    // 0xa94360: b.ne            #0xa9436c
    // 0xa94364: r0 = Null
    //     0xa94364: mov             x0, NULL
    // 0xa94368: b               #0xa94374
    // 0xa9436c: LoadField: r0 = r1->field_13
    //     0xa9436c: ldur            w0, [x1, #0x13]
    // 0xa94370: DecompressPointer r0
    //     0xa94370: add             x0, x0, HEAP, lsl #32
    // 0xa94374: cmp             w0, NULL
    // 0xa94378: b.ne            #0xa94384
    // 0xa9437c: r5 = 0
    //     0xa9437c: movz            x5, #0
    // 0xa94380: b               #0xa94394
    // 0xa94384: r1 = LoadInt32Instr(r0)
    //     0xa94384: sbfx            x1, x0, #1, #0x1f
    //     0xa94388: tbz             w0, #0, #0xa94390
    //     0xa9438c: ldur            x1, [x0, #7]
    // 0xa94390: mov             x5, x1
    // 0xa94394: ldur            x0, [fp, #-8]
    // 0xa94398: ldur            x1, [fp, #-0x28]
    // 0xa9439c: ldur            x2, [fp, #-0x18]
    // 0xa943a0: ldur            d0, [fp, #-0x70]
    // 0xa943a4: r3 = "2"
    //     0xa943a4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8b0] "2"
    //     0xa943a8: ldr             x3, [x3, #0x8b0]
    // 0xa943ac: r0 = chartRow()
    //     0xa943ac: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xa943b0: ldur            x2, [fp, #-8]
    // 0xa943b4: stur            x0, [fp, #-0x68]
    // 0xa943b8: LoadField: r3 = r2->field_f
    //     0xa943b8: ldur            w3, [x2, #0xf]
    // 0xa943bc: DecompressPointer r3
    //     0xa943bc: add             x3, x3, HEAP, lsl #32
    // 0xa943c0: stur            x3, [fp, #-0x28]
    // 0xa943c4: LoadField: r4 = r2->field_13
    //     0xa943c4: ldur            w4, [x2, #0x13]
    // 0xa943c8: DecompressPointer r4
    //     0xa943c8: add             x4, x4, HEAP, lsl #32
    // 0xa943cc: mov             x1, x3
    // 0xa943d0: stur            x4, [fp, #-0x18]
    // 0xa943d4: r0 = controller()
    //     0xa943d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa943d8: LoadField: r1 = r0->field_7b
    //     0xa943d8: ldur            w1, [x0, #0x7b]
    // 0xa943dc: DecompressPointer r1
    //     0xa943dc: add             x1, x1, HEAP, lsl #32
    // 0xa943e0: r0 = value()
    //     0xa943e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa943e4: LoadField: r1 = r0->field_b
    //     0xa943e4: ldur            w1, [x0, #0xb]
    // 0xa943e8: DecompressPointer r1
    //     0xa943e8: add             x1, x1, HEAP, lsl #32
    // 0xa943ec: cmp             w1, NULL
    // 0xa943f0: b.ne            #0xa943fc
    // 0xa943f4: r0 = Null
    //     0xa943f4: mov             x0, NULL
    // 0xa943f8: b               #0xa94434
    // 0xa943fc: LoadField: r0 = r1->field_f
    //     0xa943fc: ldur            w0, [x1, #0xf]
    // 0xa94400: DecompressPointer r0
    //     0xa94400: add             x0, x0, HEAP, lsl #32
    // 0xa94404: cmp             w0, NULL
    // 0xa94408: b.ne            #0xa94414
    // 0xa9440c: r0 = Null
    //     0xa9440c: mov             x0, NULL
    // 0xa94410: b               #0xa94434
    // 0xa94414: LoadField: r1 = r0->field_1b
    //     0xa94414: ldur            w1, [x0, #0x1b]
    // 0xa94418: DecompressPointer r1
    //     0xa94418: add             x1, x1, HEAP, lsl #32
    // 0xa9441c: cmp             w1, NULL
    // 0xa94420: b.ne            #0xa9442c
    // 0xa94424: r0 = Null
    //     0xa94424: mov             x0, NULL
    // 0xa94428: b               #0xa94434
    // 0xa9442c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa9442c: ldur            w0, [x1, #0x17]
    // 0xa94430: DecompressPointer r0
    //     0xa94430: add             x0, x0, HEAP, lsl #32
    // 0xa94434: cmp             w0, NULL
    // 0xa94438: b.ne            #0xa94444
    // 0xa9443c: r0 = 0
    //     0xa9443c: movz            x0, #0
    // 0xa94440: b               #0xa94454
    // 0xa94444: r1 = LoadInt32Instr(r0)
    //     0xa94444: sbfx            x1, x0, #1, #0x1f
    //     0xa94448: tbz             w0, #0, #0xa94450
    //     0xa9444c: ldur            x1, [x0, #7]
    // 0xa94450: mov             x0, x1
    // 0xa94454: ldur            x2, [fp, #-8]
    // 0xa94458: stur            x0, [fp, #-0x38]
    // 0xa9445c: LoadField: r1 = r2->field_f
    //     0xa9445c: ldur            w1, [x2, #0xf]
    // 0xa94460: DecompressPointer r1
    //     0xa94460: add             x1, x1, HEAP, lsl #32
    // 0xa94464: r0 = controller()
    //     0xa94464: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa94468: LoadField: r1 = r0->field_7b
    //     0xa94468: ldur            w1, [x0, #0x7b]
    // 0xa9446c: DecompressPointer r1
    //     0xa9446c: add             x1, x1, HEAP, lsl #32
    // 0xa94470: r0 = value()
    //     0xa94470: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa94474: LoadField: r1 = r0->field_b
    //     0xa94474: ldur            w1, [x0, #0xb]
    // 0xa94478: DecompressPointer r1
    //     0xa94478: add             x1, x1, HEAP, lsl #32
    // 0xa9447c: cmp             w1, NULL
    // 0xa94480: b.ne            #0xa9448c
    // 0xa94484: r0 = Null
    //     0xa94484: mov             x0, NULL
    // 0xa94488: b               #0xa944c4
    // 0xa9448c: LoadField: r0 = r1->field_f
    //     0xa9448c: ldur            w0, [x1, #0xf]
    // 0xa94490: DecompressPointer r0
    //     0xa94490: add             x0, x0, HEAP, lsl #32
    // 0xa94494: cmp             w0, NULL
    // 0xa94498: b.ne            #0xa944a4
    // 0xa9449c: r0 = Null
    //     0xa9449c: mov             x0, NULL
    // 0xa944a0: b               #0xa944c4
    // 0xa944a4: LoadField: r1 = r0->field_1b
    //     0xa944a4: ldur            w1, [x0, #0x1b]
    // 0xa944a8: DecompressPointer r1
    //     0xa944a8: add             x1, x1, HEAP, lsl #32
    // 0xa944ac: cmp             w1, NULL
    // 0xa944b0: b.ne            #0xa944bc
    // 0xa944b4: r0 = Null
    //     0xa944b4: mov             x0, NULL
    // 0xa944b8: b               #0xa944c4
    // 0xa944bc: LoadField: r0 = r1->field_7
    //     0xa944bc: ldur            w0, [x1, #7]
    // 0xa944c0: DecompressPointer r0
    //     0xa944c0: add             x0, x0, HEAP, lsl #32
    // 0xa944c4: cmp             w0, NULL
    // 0xa944c8: b.ne            #0xa944d4
    // 0xa944cc: r0 = 0
    //     0xa944cc: movz            x0, #0
    // 0xa944d0: b               #0xa944e4
    // 0xa944d4: r1 = LoadInt32Instr(r0)
    //     0xa944d4: sbfx            x1, x0, #1, #0x1f
    //     0xa944d8: tbz             w0, #0, #0xa944e0
    //     0xa944dc: ldur            x1, [x0, #7]
    // 0xa944e0: mov             x0, x1
    // 0xa944e4: ldur            x2, [fp, #-8]
    // 0xa944e8: stur            x0, [fp, #-0x48]
    // 0xa944ec: LoadField: r1 = r2->field_f
    //     0xa944ec: ldur            w1, [x2, #0xf]
    // 0xa944f0: DecompressPointer r1
    //     0xa944f0: add             x1, x1, HEAP, lsl #32
    // 0xa944f4: r0 = controller()
    //     0xa944f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa944f8: LoadField: r1 = r0->field_7b
    //     0xa944f8: ldur            w1, [x0, #0x7b]
    // 0xa944fc: DecompressPointer r1
    //     0xa944fc: add             x1, x1, HEAP, lsl #32
    // 0xa94500: r0 = value()
    //     0xa94500: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa94504: LoadField: r1 = r0->field_b
    //     0xa94504: ldur            w1, [x0, #0xb]
    // 0xa94508: DecompressPointer r1
    //     0xa94508: add             x1, x1, HEAP, lsl #32
    // 0xa9450c: cmp             w1, NULL
    // 0xa94510: b.ne            #0xa9451c
    // 0xa94514: r0 = Null
    //     0xa94514: mov             x0, NULL
    // 0xa94518: b               #0xa94554
    // 0xa9451c: LoadField: r0 = r1->field_f
    //     0xa9451c: ldur            w0, [x1, #0xf]
    // 0xa94520: DecompressPointer r0
    //     0xa94520: add             x0, x0, HEAP, lsl #32
    // 0xa94524: cmp             w0, NULL
    // 0xa94528: b.ne            #0xa94534
    // 0xa9452c: r0 = Null
    //     0xa9452c: mov             x0, NULL
    // 0xa94530: b               #0xa94554
    // 0xa94534: LoadField: r1 = r0->field_1b
    //     0xa94534: ldur            w1, [x0, #0x1b]
    // 0xa94538: DecompressPointer r1
    //     0xa94538: add             x1, x1, HEAP, lsl #32
    // 0xa9453c: cmp             w1, NULL
    // 0xa94540: b.ne            #0xa9454c
    // 0xa94544: r0 = Null
    //     0xa94544: mov             x0, NULL
    // 0xa94548: b               #0xa94554
    // 0xa9454c: LoadField: r0 = r1->field_b
    //     0xa9454c: ldur            w0, [x1, #0xb]
    // 0xa94550: DecompressPointer r0
    //     0xa94550: add             x0, x0, HEAP, lsl #32
    // 0xa94554: cmp             w0, NULL
    // 0xa94558: b.ne            #0xa94564
    // 0xa9455c: r1 = 0
    //     0xa9455c: movz            x1, #0
    // 0xa94560: b               #0xa94570
    // 0xa94564: r1 = LoadInt32Instr(r0)
    //     0xa94564: sbfx            x1, x0, #1, #0x1f
    //     0xa94568: tbz             w0, #0, #0xa94570
    //     0xa9456c: ldur            x1, [x0, #7]
    // 0xa94570: ldur            x2, [fp, #-8]
    // 0xa94574: ldur            x0, [fp, #-0x48]
    // 0xa94578: add             x3, x0, x1
    // 0xa9457c: stur            x3, [fp, #-0x50]
    // 0xa94580: LoadField: r1 = r2->field_f
    //     0xa94580: ldur            w1, [x2, #0xf]
    // 0xa94584: DecompressPointer r1
    //     0xa94584: add             x1, x1, HEAP, lsl #32
    // 0xa94588: r0 = controller()
    //     0xa94588: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa9458c: LoadField: r1 = r0->field_7b
    //     0xa9458c: ldur            w1, [x0, #0x7b]
    // 0xa94590: DecompressPointer r1
    //     0xa94590: add             x1, x1, HEAP, lsl #32
    // 0xa94594: r0 = value()
    //     0xa94594: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa94598: LoadField: r1 = r0->field_b
    //     0xa94598: ldur            w1, [x0, #0xb]
    // 0xa9459c: DecompressPointer r1
    //     0xa9459c: add             x1, x1, HEAP, lsl #32
    // 0xa945a0: cmp             w1, NULL
    // 0xa945a4: b.ne            #0xa945b0
    // 0xa945a8: r0 = Null
    //     0xa945a8: mov             x0, NULL
    // 0xa945ac: b               #0xa945e8
    // 0xa945b0: LoadField: r0 = r1->field_f
    //     0xa945b0: ldur            w0, [x1, #0xf]
    // 0xa945b4: DecompressPointer r0
    //     0xa945b4: add             x0, x0, HEAP, lsl #32
    // 0xa945b8: cmp             w0, NULL
    // 0xa945bc: b.ne            #0xa945c8
    // 0xa945c0: r0 = Null
    //     0xa945c0: mov             x0, NULL
    // 0xa945c4: b               #0xa945e8
    // 0xa945c8: LoadField: r1 = r0->field_1b
    //     0xa945c8: ldur            w1, [x0, #0x1b]
    // 0xa945cc: DecompressPointer r1
    //     0xa945cc: add             x1, x1, HEAP, lsl #32
    // 0xa945d0: cmp             w1, NULL
    // 0xa945d4: b.ne            #0xa945e0
    // 0xa945d8: r0 = Null
    //     0xa945d8: mov             x0, NULL
    // 0xa945dc: b               #0xa945e8
    // 0xa945e0: LoadField: r0 = r1->field_f
    //     0xa945e0: ldur            w0, [x1, #0xf]
    // 0xa945e4: DecompressPointer r0
    //     0xa945e4: add             x0, x0, HEAP, lsl #32
    // 0xa945e8: cmp             w0, NULL
    // 0xa945ec: b.ne            #0xa945f8
    // 0xa945f0: r1 = 0
    //     0xa945f0: movz            x1, #0
    // 0xa945f4: b               #0xa94604
    // 0xa945f8: r1 = LoadInt32Instr(r0)
    //     0xa945f8: sbfx            x1, x0, #1, #0x1f
    //     0xa945fc: tbz             w0, #0, #0xa94604
    //     0xa94600: ldur            x1, [x0, #7]
    // 0xa94604: ldur            x2, [fp, #-8]
    // 0xa94608: ldur            x0, [fp, #-0x50]
    // 0xa9460c: add             x3, x0, x1
    // 0xa94610: stur            x3, [fp, #-0x48]
    // 0xa94614: LoadField: r1 = r2->field_f
    //     0xa94614: ldur            w1, [x2, #0xf]
    // 0xa94618: DecompressPointer r1
    //     0xa94618: add             x1, x1, HEAP, lsl #32
    // 0xa9461c: r0 = controller()
    //     0xa9461c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa94620: LoadField: r1 = r0->field_7b
    //     0xa94620: ldur            w1, [x0, #0x7b]
    // 0xa94624: DecompressPointer r1
    //     0xa94624: add             x1, x1, HEAP, lsl #32
    // 0xa94628: r0 = value()
    //     0xa94628: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa9462c: LoadField: r1 = r0->field_b
    //     0xa9462c: ldur            w1, [x0, #0xb]
    // 0xa94630: DecompressPointer r1
    //     0xa94630: add             x1, x1, HEAP, lsl #32
    // 0xa94634: cmp             w1, NULL
    // 0xa94638: b.ne            #0xa94644
    // 0xa9463c: r0 = Null
    //     0xa9463c: mov             x0, NULL
    // 0xa94640: b               #0xa9467c
    // 0xa94644: LoadField: r0 = r1->field_f
    //     0xa94644: ldur            w0, [x1, #0xf]
    // 0xa94648: DecompressPointer r0
    //     0xa94648: add             x0, x0, HEAP, lsl #32
    // 0xa9464c: cmp             w0, NULL
    // 0xa94650: b.ne            #0xa9465c
    // 0xa94654: r0 = Null
    //     0xa94654: mov             x0, NULL
    // 0xa94658: b               #0xa9467c
    // 0xa9465c: LoadField: r1 = r0->field_1b
    //     0xa9465c: ldur            w1, [x0, #0x1b]
    // 0xa94660: DecompressPointer r1
    //     0xa94660: add             x1, x1, HEAP, lsl #32
    // 0xa94664: cmp             w1, NULL
    // 0xa94668: b.ne            #0xa94674
    // 0xa9466c: r0 = Null
    //     0xa9466c: mov             x0, NULL
    // 0xa94670: b               #0xa9467c
    // 0xa94674: LoadField: r0 = r1->field_13
    //     0xa94674: ldur            w0, [x1, #0x13]
    // 0xa94678: DecompressPointer r0
    //     0xa94678: add             x0, x0, HEAP, lsl #32
    // 0xa9467c: cmp             w0, NULL
    // 0xa94680: b.ne            #0xa9468c
    // 0xa94684: r1 = 0
    //     0xa94684: movz            x1, #0
    // 0xa94688: b               #0xa94698
    // 0xa9468c: r1 = LoadInt32Instr(r0)
    //     0xa9468c: sbfx            x1, x0, #1, #0x1f
    //     0xa94690: tbz             w0, #0, #0xa94698
    //     0xa94694: ldur            x1, [x0, #7]
    // 0xa94698: ldur            x2, [fp, #-8]
    // 0xa9469c: ldur            x0, [fp, #-0x48]
    // 0xa946a0: add             x3, x0, x1
    // 0xa946a4: stur            x3, [fp, #-0x50]
    // 0xa946a8: LoadField: r1 = r2->field_f
    //     0xa946a8: ldur            w1, [x2, #0xf]
    // 0xa946ac: DecompressPointer r1
    //     0xa946ac: add             x1, x1, HEAP, lsl #32
    // 0xa946b0: r0 = controller()
    //     0xa946b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa946b4: LoadField: r1 = r0->field_7b
    //     0xa946b4: ldur            w1, [x0, #0x7b]
    // 0xa946b8: DecompressPointer r1
    //     0xa946b8: add             x1, x1, HEAP, lsl #32
    // 0xa946bc: r0 = value()
    //     0xa946bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa946c0: LoadField: r1 = r0->field_b
    //     0xa946c0: ldur            w1, [x0, #0xb]
    // 0xa946c4: DecompressPointer r1
    //     0xa946c4: add             x1, x1, HEAP, lsl #32
    // 0xa946c8: cmp             w1, NULL
    // 0xa946cc: b.ne            #0xa946d8
    // 0xa946d0: r0 = Null
    //     0xa946d0: mov             x0, NULL
    // 0xa946d4: b               #0xa94710
    // 0xa946d8: LoadField: r0 = r1->field_f
    //     0xa946d8: ldur            w0, [x1, #0xf]
    // 0xa946dc: DecompressPointer r0
    //     0xa946dc: add             x0, x0, HEAP, lsl #32
    // 0xa946e0: cmp             w0, NULL
    // 0xa946e4: b.ne            #0xa946f0
    // 0xa946e8: r0 = Null
    //     0xa946e8: mov             x0, NULL
    // 0xa946ec: b               #0xa94710
    // 0xa946f0: LoadField: r1 = r0->field_1b
    //     0xa946f0: ldur            w1, [x0, #0x1b]
    // 0xa946f4: DecompressPointer r1
    //     0xa946f4: add             x1, x1, HEAP, lsl #32
    // 0xa946f8: cmp             w1, NULL
    // 0xa946fc: b.ne            #0xa94708
    // 0xa94700: r0 = Null
    //     0xa94700: mov             x0, NULL
    // 0xa94704: b               #0xa94710
    // 0xa94708: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa94708: ldur            w0, [x1, #0x17]
    // 0xa9470c: DecompressPointer r0
    //     0xa9470c: add             x0, x0, HEAP, lsl #32
    // 0xa94710: cmp             w0, NULL
    // 0xa94714: b.ne            #0xa94720
    // 0xa94718: r3 = 0
    //     0xa94718: movz            x3, #0
    // 0xa9471c: b               #0xa94730
    // 0xa94720: r1 = LoadInt32Instr(r0)
    //     0xa94720: sbfx            x1, x0, #1, #0x1f
    //     0xa94724: tbz             w0, #0, #0xa9472c
    //     0xa94728: ldur            x1, [x0, #7]
    // 0xa9472c: mov             x3, x1
    // 0xa94730: ldur            x2, [fp, #-8]
    // 0xa94734: ldur            x1, [fp, #-0x38]
    // 0xa94738: ldur            x0, [fp, #-0x50]
    // 0xa9473c: add             x4, x0, x3
    // 0xa94740: scvtf           d0, x1
    // 0xa94744: scvtf           d1, x4
    // 0xa94748: fdiv            d2, d0, d1
    // 0xa9474c: stur            d2, [fp, #-0x70]
    // 0xa94750: LoadField: r1 = r2->field_f
    //     0xa94750: ldur            w1, [x2, #0xf]
    // 0xa94754: DecompressPointer r1
    //     0xa94754: add             x1, x1, HEAP, lsl #32
    // 0xa94758: r0 = controller()
    //     0xa94758: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa9475c: LoadField: r1 = r0->field_7b
    //     0xa9475c: ldur            w1, [x0, #0x7b]
    // 0xa94760: DecompressPointer r1
    //     0xa94760: add             x1, x1, HEAP, lsl #32
    // 0xa94764: r0 = value()
    //     0xa94764: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa94768: LoadField: r1 = r0->field_b
    //     0xa94768: ldur            w1, [x0, #0xb]
    // 0xa9476c: DecompressPointer r1
    //     0xa9476c: add             x1, x1, HEAP, lsl #32
    // 0xa94770: cmp             w1, NULL
    // 0xa94774: b.ne            #0xa94780
    // 0xa94778: r0 = Null
    //     0xa94778: mov             x0, NULL
    // 0xa9477c: b               #0xa947b8
    // 0xa94780: LoadField: r0 = r1->field_f
    //     0xa94780: ldur            w0, [x1, #0xf]
    // 0xa94784: DecompressPointer r0
    //     0xa94784: add             x0, x0, HEAP, lsl #32
    // 0xa94788: cmp             w0, NULL
    // 0xa9478c: b.ne            #0xa94798
    // 0xa94790: r0 = Null
    //     0xa94790: mov             x0, NULL
    // 0xa94794: b               #0xa947b8
    // 0xa94798: LoadField: r1 = r0->field_1b
    //     0xa94798: ldur            w1, [x0, #0x1b]
    // 0xa9479c: DecompressPointer r1
    //     0xa9479c: add             x1, x1, HEAP, lsl #32
    // 0xa947a0: cmp             w1, NULL
    // 0xa947a4: b.ne            #0xa947b0
    // 0xa947a8: r0 = Null
    //     0xa947a8: mov             x0, NULL
    // 0xa947ac: b               #0xa947b8
    // 0xa947b0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa947b0: ldur            w0, [x1, #0x17]
    // 0xa947b4: DecompressPointer r0
    //     0xa947b4: add             x0, x0, HEAP, lsl #32
    // 0xa947b8: cmp             w0, NULL
    // 0xa947bc: b.ne            #0xa947c8
    // 0xa947c0: r5 = 0
    //     0xa947c0: movz            x5, #0
    // 0xa947c4: b               #0xa947d8
    // 0xa947c8: r1 = LoadInt32Instr(r0)
    //     0xa947c8: sbfx            x1, x0, #1, #0x1f
    //     0xa947cc: tbz             w0, #0, #0xa947d4
    //     0xa947d0: ldur            x1, [x0, #7]
    // 0xa947d4: mov             x5, x1
    // 0xa947d8: ldur            x0, [fp, #-8]
    // 0xa947dc: ldur            x10, [fp, #-0x10]
    // 0xa947e0: ldur            x9, [fp, #-0x30]
    // 0xa947e4: ldur            x8, [fp, #-0x40]
    // 0xa947e8: ldur            x7, [fp, #-0x58]
    // 0xa947ec: ldur            x6, [fp, #-0x60]
    // 0xa947f0: ldur            x4, [fp, #-0x68]
    // 0xa947f4: ldur            x1, [fp, #-0x28]
    // 0xa947f8: ldur            x2, [fp, #-0x18]
    // 0xa947fc: ldur            d0, [fp, #-0x70]
    // 0xa94800: r3 = "1"
    //     0xa94800: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cba8] "1"
    //     0xa94804: ldr             x3, [x3, #0xba8]
    // 0xa94808: r0 = chartRow()
    //     0xa94808: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xa9480c: r1 = Null
    //     0xa9480c: mov             x1, NULL
    // 0xa94810: r2 = 14
    //     0xa94810: movz            x2, #0xe
    // 0xa94814: stur            x0, [fp, #-0x18]
    // 0xa94818: r0 = AllocateArray()
    //     0xa94818: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa9481c: stur            x0, [fp, #-0x28]
    // 0xa94820: r16 = Instance_SizedBox
    //     0xa94820: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xa94824: ldr             x16, [x16, #0x8b8]
    // 0xa94828: StoreField: r0->field_f = r16
    //     0xa94828: stur            w16, [x0, #0xf]
    // 0xa9482c: ldur            x1, [fp, #-0x40]
    // 0xa94830: StoreField: r0->field_13 = r1
    //     0xa94830: stur            w1, [x0, #0x13]
    // 0xa94834: ldur            x1, [fp, #-0x58]
    // 0xa94838: ArrayStore: r0[0] = r1  ; List_4
    //     0xa94838: stur            w1, [x0, #0x17]
    // 0xa9483c: ldur            x1, [fp, #-0x60]
    // 0xa94840: StoreField: r0->field_1b = r1
    //     0xa94840: stur            w1, [x0, #0x1b]
    // 0xa94844: ldur            x1, [fp, #-0x68]
    // 0xa94848: StoreField: r0->field_1f = r1
    //     0xa94848: stur            w1, [x0, #0x1f]
    // 0xa9484c: ldur            x1, [fp, #-0x18]
    // 0xa94850: StoreField: r0->field_23 = r1
    //     0xa94850: stur            w1, [x0, #0x23]
    // 0xa94854: r16 = Instance_SizedBox
    //     0xa94854: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xa94858: ldr             x16, [x16, #0x8b8]
    // 0xa9485c: StoreField: r0->field_27 = r16
    //     0xa9485c: stur            w16, [x0, #0x27]
    // 0xa94860: r1 = <Widget>
    //     0xa94860: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa94864: r0 = AllocateGrowableArray()
    //     0xa94864: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa94868: mov             x1, x0
    // 0xa9486c: ldur            x0, [fp, #-0x28]
    // 0xa94870: stur            x1, [fp, #-0x18]
    // 0xa94874: StoreField: r1->field_f = r0
    //     0xa94874: stur            w0, [x1, #0xf]
    // 0xa94878: r0 = 14
    //     0xa94878: movz            x0, #0xe
    // 0xa9487c: StoreField: r1->field_b = r0
    //     0xa9487c: stur            w0, [x1, #0xb]
    // 0xa94880: r0 = Column()
    //     0xa94880: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa94884: mov             x3, x0
    // 0xa94888: r0 = Instance_Axis
    //     0xa94888: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa9488c: stur            x3, [fp, #-0x28]
    // 0xa94890: StoreField: r3->field_f = r0
    //     0xa94890: stur            w0, [x3, #0xf]
    // 0xa94894: r4 = Instance_MainAxisAlignment
    //     0xa94894: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa94898: ldr             x4, [x4, #0xa08]
    // 0xa9489c: StoreField: r3->field_13 = r4
    //     0xa9489c: stur            w4, [x3, #0x13]
    // 0xa948a0: r5 = Instance_MainAxisSize
    //     0xa948a0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa948a4: ldr             x5, [x5, #0xa10]
    // 0xa948a8: ArrayStore: r3[0] = r5  ; List_4
    //     0xa948a8: stur            w5, [x3, #0x17]
    // 0xa948ac: r1 = Instance_CrossAxisAlignment
    //     0xa948ac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa948b0: ldr             x1, [x1, #0x890]
    // 0xa948b4: StoreField: r3->field_1b = r1
    //     0xa948b4: stur            w1, [x3, #0x1b]
    // 0xa948b8: r6 = Instance_VerticalDirection
    //     0xa948b8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa948bc: ldr             x6, [x6, #0xa20]
    // 0xa948c0: StoreField: r3->field_23 = r6
    //     0xa948c0: stur            w6, [x3, #0x23]
    // 0xa948c4: r7 = Instance_Clip
    //     0xa948c4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa948c8: ldr             x7, [x7, #0x38]
    // 0xa948cc: StoreField: r3->field_2b = r7
    //     0xa948cc: stur            w7, [x3, #0x2b]
    // 0xa948d0: StoreField: r3->field_2f = rZR
    //     0xa948d0: stur            xzr, [x3, #0x2f]
    // 0xa948d4: ldur            x1, [fp, #-0x18]
    // 0xa948d8: StoreField: r3->field_b = r1
    //     0xa948d8: stur            w1, [x3, #0xb]
    // 0xa948dc: r1 = Null
    //     0xa948dc: mov             x1, NULL
    // 0xa948e0: r2 = 4
    //     0xa948e0: movz            x2, #0x4
    // 0xa948e4: r0 = AllocateArray()
    //     0xa948e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa948e8: mov             x2, x0
    // 0xa948ec: ldur            x0, [fp, #-0x30]
    // 0xa948f0: stur            x2, [fp, #-0x18]
    // 0xa948f4: StoreField: r2->field_f = r0
    //     0xa948f4: stur            w0, [x2, #0xf]
    // 0xa948f8: ldur            x0, [fp, #-0x28]
    // 0xa948fc: StoreField: r2->field_13 = r0
    //     0xa948fc: stur            w0, [x2, #0x13]
    // 0xa94900: r1 = <Widget>
    //     0xa94900: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa94904: r0 = AllocateGrowableArray()
    //     0xa94904: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa94908: mov             x1, x0
    // 0xa9490c: ldur            x0, [fp, #-0x18]
    // 0xa94910: stur            x1, [fp, #-0x28]
    // 0xa94914: StoreField: r1->field_f = r0
    //     0xa94914: stur            w0, [x1, #0xf]
    // 0xa94918: r0 = 4
    //     0xa94918: movz            x0, #0x4
    // 0xa9491c: StoreField: r1->field_b = r0
    //     0xa9491c: stur            w0, [x1, #0xb]
    // 0xa94920: r0 = Row()
    //     0xa94920: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa94924: mov             x1, x0
    // 0xa94928: r0 = Instance_Axis
    //     0xa94928: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa9492c: stur            x1, [fp, #-0x18]
    // 0xa94930: StoreField: r1->field_f = r0
    //     0xa94930: stur            w0, [x1, #0xf]
    // 0xa94934: r2 = Instance_MainAxisAlignment
    //     0xa94934: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xa94938: ldr             x2, [x2, #0xa8]
    // 0xa9493c: StoreField: r1->field_13 = r2
    //     0xa9493c: stur            w2, [x1, #0x13]
    // 0xa94940: r2 = Instance_MainAxisSize
    //     0xa94940: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa94944: ldr             x2, [x2, #0xa10]
    // 0xa94948: ArrayStore: r1[0] = r2  ; List_4
    //     0xa94948: stur            w2, [x1, #0x17]
    // 0xa9494c: r3 = Instance_CrossAxisAlignment
    //     0xa9494c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa94950: ldr             x3, [x3, #0xa18]
    // 0xa94954: StoreField: r1->field_1b = r3
    //     0xa94954: stur            w3, [x1, #0x1b]
    // 0xa94958: r4 = Instance_VerticalDirection
    //     0xa94958: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa9495c: ldr             x4, [x4, #0xa20]
    // 0xa94960: StoreField: r1->field_23 = r4
    //     0xa94960: stur            w4, [x1, #0x23]
    // 0xa94964: r5 = Instance_Clip
    //     0xa94964: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa94968: ldr             x5, [x5, #0x38]
    // 0xa9496c: StoreField: r1->field_2b = r5
    //     0xa9496c: stur            w5, [x1, #0x2b]
    // 0xa94970: StoreField: r1->field_2f = rZR
    //     0xa94970: stur            xzr, [x1, #0x2f]
    // 0xa94974: ldur            x6, [fp, #-0x28]
    // 0xa94978: StoreField: r1->field_b = r6
    //     0xa94978: stur            w6, [x1, #0xb]
    // 0xa9497c: r0 = Container()
    //     0xa9497c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa94980: stur            x0, [fp, #-0x28]
    // 0xa94984: ldur            x16, [fp, #-0x20]
    // 0xa94988: r30 = Instance_EdgeInsets
    //     0xa94988: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xa9498c: ldr             lr, [lr, #0x980]
    // 0xa94990: stp             lr, x16, [SP, #8]
    // 0xa94994: ldur            x16, [fp, #-0x18]
    // 0xa94998: str             x16, [SP]
    // 0xa9499c: mov             x1, x0
    // 0xa949a0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xa949a0: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xa949a4: ldr             x4, [x4, #0xb40]
    // 0xa949a8: r0 = Container()
    //     0xa949a8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa949ac: r0 = Visibility()
    //     0xa949ac: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa949b0: mov             x3, x0
    // 0xa949b4: ldur            x0, [fp, #-0x28]
    // 0xa949b8: stur            x3, [fp, #-0x18]
    // 0xa949bc: StoreField: r3->field_b = r0
    //     0xa949bc: stur            w0, [x3, #0xb]
    // 0xa949c0: r0 = Instance_SizedBox
    //     0xa949c0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa949c4: StoreField: r3->field_f = r0
    //     0xa949c4: stur            w0, [x3, #0xf]
    // 0xa949c8: ldur            x0, [fp, #-0x10]
    // 0xa949cc: StoreField: r3->field_13 = r0
    //     0xa949cc: stur            w0, [x3, #0x13]
    // 0xa949d0: r0 = false
    //     0xa949d0: add             x0, NULL, #0x30  ; false
    // 0xa949d4: ArrayStore: r3[0] = r0  ; List_4
    //     0xa949d4: stur            w0, [x3, #0x17]
    // 0xa949d8: StoreField: r3->field_1b = r0
    //     0xa949d8: stur            w0, [x3, #0x1b]
    // 0xa949dc: StoreField: r3->field_1f = r0
    //     0xa949dc: stur            w0, [x3, #0x1f]
    // 0xa949e0: StoreField: r3->field_23 = r0
    //     0xa949e0: stur            w0, [x3, #0x23]
    // 0xa949e4: StoreField: r3->field_27 = r0
    //     0xa949e4: stur            w0, [x3, #0x27]
    // 0xa949e8: StoreField: r3->field_2b = r0
    //     0xa949e8: stur            w0, [x3, #0x2b]
    // 0xa949ec: r1 = Null
    //     0xa949ec: mov             x1, NULL
    // 0xa949f0: r2 = 2
    //     0xa949f0: movz            x2, #0x2
    // 0xa949f4: r0 = AllocateArray()
    //     0xa949f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa949f8: mov             x2, x0
    // 0xa949fc: ldur            x0, [fp, #-0x18]
    // 0xa94a00: stur            x2, [fp, #-0x10]
    // 0xa94a04: StoreField: r2->field_f = r0
    //     0xa94a04: stur            w0, [x2, #0xf]
    // 0xa94a08: r1 = <Widget>
    //     0xa94a08: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa94a0c: r0 = AllocateGrowableArray()
    //     0xa94a0c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa94a10: mov             x2, x0
    // 0xa94a14: ldur            x0, [fp, #-0x10]
    // 0xa94a18: stur            x2, [fp, #-0x18]
    // 0xa94a1c: StoreField: r2->field_f = r0
    //     0xa94a1c: stur            w0, [x2, #0xf]
    // 0xa94a20: r0 = 2
    //     0xa94a20: movz            x0, #0x2
    // 0xa94a24: StoreField: r2->field_b = r0
    //     0xa94a24: stur            w0, [x2, #0xb]
    // 0xa94a28: ldur            x0, [fp, #-8]
    // 0xa94a2c: LoadField: r1 = r0->field_f
    //     0xa94a2c: ldur            w1, [x0, #0xf]
    // 0xa94a30: DecompressPointer r1
    //     0xa94a30: add             x1, x1, HEAP, lsl #32
    // 0xa94a34: r0 = controller()
    //     0xa94a34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa94a38: LoadField: r1 = r0->field_7f
    //     0xa94a38: ldur            w1, [x0, #0x7f]
    // 0xa94a3c: DecompressPointer r1
    //     0xa94a3c: add             x1, x1, HEAP, lsl #32
    // 0xa94a40: r0 = value()
    //     0xa94a40: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa94a44: LoadField: r3 = r0->field_f
    //     0xa94a44: ldur            w3, [x0, #0xf]
    // 0xa94a48: DecompressPointer r3
    //     0xa94a48: add             x3, x3, HEAP, lsl #32
    // 0xa94a4c: stur            x3, [fp, #-0x10]
    // 0xa94a50: cmp             w3, NULL
    // 0xa94a54: b.ne            #0xa94a60
    // 0xa94a58: r0 = Null
    //     0xa94a58: mov             x0, NULL
    // 0xa94a5c: b               #0xa94ab4
    // 0xa94a60: r1 = Function '<anonymous closure>':.
    //     0xa94a60: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea28] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0xa94a64: ldr             x1, [x1, #0xa28]
    // 0xa94a68: r2 = Null
    //     0xa94a68: mov             x2, NULL
    // 0xa94a6c: r0 = AllocateClosure()
    //     0xa94a6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa94a70: ldur            x16, [fp, #-0x10]
    // 0xa94a74: stp             x16, NULL, [SP, #8]
    // 0xa94a78: str             x0, [SP]
    // 0xa94a7c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa94a7c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa94a80: r0 = expand()
    //     0xa94a80: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0xa94a84: mov             x1, x0
    // 0xa94a88: r0 = iterator()
    //     0xa94a88: bl              #0x7e2acc  ; [dart:_internal] ExpandIterable::iterator
    // 0xa94a8c: r1 = LoadClassIdInstr(r0)
    //     0xa94a8c: ldur            x1, [x0, #-1]
    //     0xa94a90: ubfx            x1, x1, #0xc, #0x14
    // 0xa94a94: mov             x16, x0
    // 0xa94a98: mov             x0, x1
    // 0xa94a9c: mov             x1, x16
    // 0xa94aa0: r0 = GDT[cid_x0 + 0x5ea]()
    //     0xa94aa0: add             lr, x0, #0x5ea
    //     0xa94aa4: ldr             lr, [x21, lr, lsl #3]
    //     0xa94aa8: blr             lr
    // 0xa94aac: eor             x1, x0, #0x10
    // 0xa94ab0: eor             x0, x1, #0x10
    // 0xa94ab4: cmp             w0, NULL
    // 0xa94ab8: b.ne            #0xa94ac4
    // 0xa94abc: ldur            x2, [fp, #-0x18]
    // 0xa94ac0: b               #0xa94e14
    // 0xa94ac4: tbnz            w0, #4, #0xa94e10
    // 0xa94ac8: ldur            x2, [fp, #-8]
    // 0xa94acc: LoadField: r1 = r2->field_13
    //     0xa94acc: ldur            w1, [x2, #0x13]
    // 0xa94ad0: DecompressPointer r1
    //     0xa94ad0: add             x1, x1, HEAP, lsl #32
    // 0xa94ad4: r0 = of()
    //     0xa94ad4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa94ad8: LoadField: r1 = r0->field_87
    //     0xa94ad8: ldur            w1, [x0, #0x87]
    // 0xa94adc: DecompressPointer r1
    //     0xa94adc: add             x1, x1, HEAP, lsl #32
    // 0xa94ae0: LoadField: r0 = r1->field_2f
    //     0xa94ae0: ldur            w0, [x1, #0x2f]
    // 0xa94ae4: DecompressPointer r0
    //     0xa94ae4: add             x0, x0, HEAP, lsl #32
    // 0xa94ae8: cmp             w0, NULL
    // 0xa94aec: b.ne            #0xa94af8
    // 0xa94af0: r0 = Null
    //     0xa94af0: mov             x0, NULL
    // 0xa94af4: b               #0xa94b18
    // 0xa94af8: r16 = Instance_Color
    //     0xa94af8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa94afc: r30 = 14.000000
    //     0xa94afc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa94b00: ldr             lr, [lr, #0x1d8]
    // 0xa94b04: stp             lr, x16, [SP]
    // 0xa94b08: mov             x1, x0
    // 0xa94b0c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa94b0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa94b10: ldr             x4, [x4, #0x9b8]
    // 0xa94b14: r0 = copyWith()
    //     0xa94b14: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa94b18: ldur            x2, [fp, #-8]
    // 0xa94b1c: stur            x0, [fp, #-0x10]
    // 0xa94b20: r0 = Text()
    //     0xa94b20: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa94b24: mov             x2, x0
    // 0xa94b28: r0 = "Real images from customers"
    //     0xa94b28: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f088] "Real images from customers"
    //     0xa94b2c: ldr             x0, [x0, #0x88]
    // 0xa94b30: stur            x2, [fp, #-0x20]
    // 0xa94b34: StoreField: r2->field_b = r0
    //     0xa94b34: stur            w0, [x2, #0xb]
    // 0xa94b38: ldur            x0, [fp, #-0x10]
    // 0xa94b3c: StoreField: r2->field_13 = r0
    //     0xa94b3c: stur            w0, [x2, #0x13]
    // 0xa94b40: ldur            x0, [fp, #-8]
    // 0xa94b44: LoadField: r1 = r0->field_f
    //     0xa94b44: ldur            w1, [x0, #0xf]
    // 0xa94b48: DecompressPointer r1
    //     0xa94b48: add             x1, x1, HEAP, lsl #32
    // 0xa94b4c: r0 = controller()
    //     0xa94b4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa94b50: LoadField: r1 = r0->field_7f
    //     0xa94b50: ldur            w1, [x0, #0x7f]
    // 0xa94b54: DecompressPointer r1
    //     0xa94b54: add             x1, x1, HEAP, lsl #32
    // 0xa94b58: r0 = value()
    //     0xa94b58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa94b5c: LoadField: r3 = r0->field_f
    //     0xa94b5c: ldur            w3, [x0, #0xf]
    // 0xa94b60: DecompressPointer r3
    //     0xa94b60: add             x3, x3, HEAP, lsl #32
    // 0xa94b64: stur            x3, [fp, #-0x10]
    // 0xa94b68: cmp             w3, NULL
    // 0xa94b6c: b.ne            #0xa94b78
    // 0xa94b70: r0 = Null
    //     0xa94b70: mov             x0, NULL
    // 0xa94b74: b               #0xa94ba4
    // 0xa94b78: r1 = Function '<anonymous closure>':.
    //     0xa94b78: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea30] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0xa94b7c: ldr             x1, [x1, #0xa30]
    // 0xa94b80: r2 = Null
    //     0xa94b80: mov             x2, NULL
    // 0xa94b84: r0 = AllocateClosure()
    //     0xa94b84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa94b88: ldur            x16, [fp, #-0x10]
    // 0xa94b8c: stp             x16, NULL, [SP, #8]
    // 0xa94b90: str             x0, [SP]
    // 0xa94b94: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa94b94: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa94b98: r0 = expand()
    //     0xa94b98: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0xa94b9c: str             x0, [SP]
    // 0xa94ba0: r0 = length()
    //     0xa94ba0: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0xa94ba4: cmp             w0, NULL
    // 0xa94ba8: b.ne            #0xa94bb4
    // 0xa94bac: r0 = 0
    //     0xa94bac: movz            x0, #0
    // 0xa94bb0: b               #0xa94bc4
    // 0xa94bb4: r1 = LoadInt32Instr(r0)
    //     0xa94bb4: sbfx            x1, x0, #1, #0x1f
    //     0xa94bb8: tbz             w0, #0, #0xa94bc0
    //     0xa94bbc: ldur            x1, [x0, #7]
    // 0xa94bc0: mov             x0, x1
    // 0xa94bc4: cmp             x0, #5
    // 0xa94bc8: b.le            #0xa94bd4
    // 0xa94bcc: r4 = 5
    //     0xa94bcc: movz            x4, #0x5
    // 0xa94bd0: b               #0xa94c5c
    // 0xa94bd4: ldur            x2, [fp, #-8]
    // 0xa94bd8: LoadField: r1 = r2->field_f
    //     0xa94bd8: ldur            w1, [x2, #0xf]
    // 0xa94bdc: DecompressPointer r1
    //     0xa94bdc: add             x1, x1, HEAP, lsl #32
    // 0xa94be0: r0 = controller()
    //     0xa94be0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa94be4: LoadField: r1 = r0->field_7f
    //     0xa94be4: ldur            w1, [x0, #0x7f]
    // 0xa94be8: DecompressPointer r1
    //     0xa94be8: add             x1, x1, HEAP, lsl #32
    // 0xa94bec: r0 = value()
    //     0xa94bec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa94bf0: LoadField: r3 = r0->field_f
    //     0xa94bf0: ldur            w3, [x0, #0xf]
    // 0xa94bf4: DecompressPointer r3
    //     0xa94bf4: add             x3, x3, HEAP, lsl #32
    // 0xa94bf8: stur            x3, [fp, #-0x10]
    // 0xa94bfc: cmp             w3, NULL
    // 0xa94c00: b.ne            #0xa94c0c
    // 0xa94c04: r0 = Null
    //     0xa94c04: mov             x0, NULL
    // 0xa94c08: b               #0xa94c38
    // 0xa94c0c: r1 = Function '<anonymous closure>':.
    //     0xa94c0c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea38] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0xa94c10: ldr             x1, [x1, #0xa38]
    // 0xa94c14: r2 = Null
    //     0xa94c14: mov             x2, NULL
    // 0xa94c18: r0 = AllocateClosure()
    //     0xa94c18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa94c1c: ldur            x16, [fp, #-0x10]
    // 0xa94c20: stp             x16, NULL, [SP, #8]
    // 0xa94c24: str             x0, [SP]
    // 0xa94c28: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa94c28: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa94c2c: r0 = expand()
    //     0xa94c2c: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0xa94c30: str             x0, [SP]
    // 0xa94c34: r0 = length()
    //     0xa94c34: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0xa94c38: cmp             w0, NULL
    // 0xa94c3c: b.ne            #0xa94c48
    // 0xa94c40: r0 = 0
    //     0xa94c40: movz            x0, #0
    // 0xa94c44: b               #0xa94c58
    // 0xa94c48: r1 = LoadInt32Instr(r0)
    //     0xa94c48: sbfx            x1, x0, #1, #0x1f
    //     0xa94c4c: tbz             w0, #0, #0xa94c54
    //     0xa94c50: ldur            x1, [x0, #7]
    // 0xa94c54: mov             x0, x1
    // 0xa94c58: mov             x4, x0
    // 0xa94c5c: ldur            x0, [fp, #-0x20]
    // 0xa94c60: ldur            x3, [fp, #-0x18]
    // 0xa94c64: ldur            x2, [fp, #-8]
    // 0xa94c68: stur            x4, [fp, #-0x38]
    // 0xa94c6c: r1 = Function '<anonymous closure>':.
    //     0xa94c6c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea40] AnonymousClosure: (0xa97674), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14fc2bc)
    //     0xa94c70: ldr             x1, [x1, #0xa40]
    // 0xa94c74: r0 = AllocateClosure()
    //     0xa94c74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa94c78: r1 = Function '<anonymous closure>':.
    //     0xa94c78: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea48] AnonymousClosure: (0x9ba768), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa94c7c: ldr             x1, [x1, #0xa48]
    // 0xa94c80: r2 = Null
    //     0xa94c80: mov             x2, NULL
    // 0xa94c84: stur            x0, [fp, #-0x10]
    // 0xa94c88: r0 = AllocateClosure()
    //     0xa94c88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa94c8c: stur            x0, [fp, #-0x28]
    // 0xa94c90: r0 = ListView()
    //     0xa94c90: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xa94c94: stur            x0, [fp, #-0x30]
    // 0xa94c98: r16 = true
    //     0xa94c98: add             x16, NULL, #0x20  ; true
    // 0xa94c9c: r30 = Instance_Axis
    //     0xa94c9c: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa94ca0: stp             lr, x16, [SP]
    // 0xa94ca4: mov             x1, x0
    // 0xa94ca8: ldur            x2, [fp, #-0x10]
    // 0xa94cac: ldur            x3, [fp, #-0x38]
    // 0xa94cb0: ldur            x5, [fp, #-0x28]
    // 0xa94cb4: r4 = const [0, 0x6, 0x2, 0x4, scrollDirection, 0x5, shrinkWrap, 0x4, null]
    //     0xa94cb4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f8e8] List(9) [0, 0x6, 0x2, 0x4, "scrollDirection", 0x5, "shrinkWrap", 0x4, Null]
    //     0xa94cb8: ldr             x4, [x4, #0x8e8]
    // 0xa94cbc: r0 = ListView.separated()
    //     0xa94cbc: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xa94cc0: r0 = SizedBox()
    //     0xa94cc0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa94cc4: mov             x3, x0
    // 0xa94cc8: r0 = 60.000000
    //     0xa94cc8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xa94ccc: ldr             x0, [x0, #0x110]
    // 0xa94cd0: stur            x3, [fp, #-0x10]
    // 0xa94cd4: StoreField: r3->field_13 = r0
    //     0xa94cd4: stur            w0, [x3, #0x13]
    // 0xa94cd8: ldur            x0, [fp, #-0x30]
    // 0xa94cdc: StoreField: r3->field_b = r0
    //     0xa94cdc: stur            w0, [x3, #0xb]
    // 0xa94ce0: r1 = Null
    //     0xa94ce0: mov             x1, NULL
    // 0xa94ce4: r2 = 8
    //     0xa94ce4: movz            x2, #0x8
    // 0xa94ce8: r0 = AllocateArray()
    //     0xa94ce8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa94cec: stur            x0, [fp, #-0x28]
    // 0xa94cf0: r16 = Instance_SizedBox
    //     0xa94cf0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xa94cf4: ldr             x16, [x16, #0x9f0]
    // 0xa94cf8: StoreField: r0->field_f = r16
    //     0xa94cf8: stur            w16, [x0, #0xf]
    // 0xa94cfc: ldur            x1, [fp, #-0x20]
    // 0xa94d00: StoreField: r0->field_13 = r1
    //     0xa94d00: stur            w1, [x0, #0x13]
    // 0xa94d04: r16 = Instance_SizedBox
    //     0xa94d04: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xa94d08: ldr             x16, [x16, #0x8f0]
    // 0xa94d0c: ArrayStore: r0[0] = r16  ; List_4
    //     0xa94d0c: stur            w16, [x0, #0x17]
    // 0xa94d10: ldur            x1, [fp, #-0x10]
    // 0xa94d14: StoreField: r0->field_1b = r1
    //     0xa94d14: stur            w1, [x0, #0x1b]
    // 0xa94d18: r1 = <Widget>
    //     0xa94d18: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa94d1c: r0 = AllocateGrowableArray()
    //     0xa94d1c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa94d20: mov             x1, x0
    // 0xa94d24: ldur            x0, [fp, #-0x28]
    // 0xa94d28: stur            x1, [fp, #-0x10]
    // 0xa94d2c: StoreField: r1->field_f = r0
    //     0xa94d2c: stur            w0, [x1, #0xf]
    // 0xa94d30: r0 = 8
    //     0xa94d30: movz            x0, #0x8
    // 0xa94d34: StoreField: r1->field_b = r0
    //     0xa94d34: stur            w0, [x1, #0xb]
    // 0xa94d38: r0 = Column()
    //     0xa94d38: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa94d3c: mov             x2, x0
    // 0xa94d40: r0 = Instance_Axis
    //     0xa94d40: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa94d44: stur            x2, [fp, #-0x20]
    // 0xa94d48: StoreField: r2->field_f = r0
    //     0xa94d48: stur            w0, [x2, #0xf]
    // 0xa94d4c: r3 = Instance_MainAxisAlignment
    //     0xa94d4c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa94d50: ldr             x3, [x3, #0xa08]
    // 0xa94d54: StoreField: r2->field_13 = r3
    //     0xa94d54: stur            w3, [x2, #0x13]
    // 0xa94d58: r4 = Instance_MainAxisSize
    //     0xa94d58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa94d5c: ldr             x4, [x4, #0xa10]
    // 0xa94d60: ArrayStore: r2[0] = r4  ; List_4
    //     0xa94d60: stur            w4, [x2, #0x17]
    // 0xa94d64: r5 = Instance_CrossAxisAlignment
    //     0xa94d64: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa94d68: ldr             x5, [x5, #0xa18]
    // 0xa94d6c: StoreField: r2->field_1b = r5
    //     0xa94d6c: stur            w5, [x2, #0x1b]
    // 0xa94d70: r6 = Instance_VerticalDirection
    //     0xa94d70: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa94d74: ldr             x6, [x6, #0xa20]
    // 0xa94d78: StoreField: r2->field_23 = r6
    //     0xa94d78: stur            w6, [x2, #0x23]
    // 0xa94d7c: r7 = Instance_Clip
    //     0xa94d7c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa94d80: ldr             x7, [x7, #0x38]
    // 0xa94d84: StoreField: r2->field_2b = r7
    //     0xa94d84: stur            w7, [x2, #0x2b]
    // 0xa94d88: StoreField: r2->field_2f = rZR
    //     0xa94d88: stur            xzr, [x2, #0x2f]
    // 0xa94d8c: ldur            x1, [fp, #-0x10]
    // 0xa94d90: StoreField: r2->field_b = r1
    //     0xa94d90: stur            w1, [x2, #0xb]
    // 0xa94d94: ldur            x8, [fp, #-0x18]
    // 0xa94d98: LoadField: r1 = r8->field_b
    //     0xa94d98: ldur            w1, [x8, #0xb]
    // 0xa94d9c: LoadField: r9 = r8->field_f
    //     0xa94d9c: ldur            w9, [x8, #0xf]
    // 0xa94da0: DecompressPointer r9
    //     0xa94da0: add             x9, x9, HEAP, lsl #32
    // 0xa94da4: LoadField: r10 = r9->field_b
    //     0xa94da4: ldur            w10, [x9, #0xb]
    // 0xa94da8: r9 = LoadInt32Instr(r1)
    //     0xa94da8: sbfx            x9, x1, #1, #0x1f
    // 0xa94dac: stur            x9, [fp, #-0x38]
    // 0xa94db0: r1 = LoadInt32Instr(r10)
    //     0xa94db0: sbfx            x1, x10, #1, #0x1f
    // 0xa94db4: cmp             x9, x1
    // 0xa94db8: b.ne            #0xa94dc4
    // 0xa94dbc: mov             x1, x8
    // 0xa94dc0: r0 = _growToNextCapacity()
    //     0xa94dc0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa94dc4: ldur            x2, [fp, #-0x18]
    // 0xa94dc8: ldur            x3, [fp, #-0x38]
    // 0xa94dcc: add             x0, x3, #1
    // 0xa94dd0: lsl             x1, x0, #1
    // 0xa94dd4: StoreField: r2->field_b = r1
    //     0xa94dd4: stur            w1, [x2, #0xb]
    // 0xa94dd8: LoadField: r1 = r2->field_f
    //     0xa94dd8: ldur            w1, [x2, #0xf]
    // 0xa94ddc: DecompressPointer r1
    //     0xa94ddc: add             x1, x1, HEAP, lsl #32
    // 0xa94de0: ldur            x0, [fp, #-0x20]
    // 0xa94de4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa94de4: add             x25, x1, x3, lsl #2
    //     0xa94de8: add             x25, x25, #0xf
    //     0xa94dec: str             w0, [x25]
    //     0xa94df0: tbz             w0, #0, #0xa94e0c
    //     0xa94df4: ldurb           w16, [x1, #-1]
    //     0xa94df8: ldurb           w17, [x0, #-1]
    //     0xa94dfc: and             x16, x17, x16, lsr #2
    //     0xa94e00: tst             x16, HEAP, lsr #32
    //     0xa94e04: b.eq            #0xa94e0c
    //     0xa94e08: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa94e0c: b               #0xa94e14
    // 0xa94e10: ldur            x2, [fp, #-0x18]
    // 0xa94e14: ldur            x0, [fp, #-8]
    // 0xa94e18: LoadField: r1 = r0->field_f
    //     0xa94e18: ldur            w1, [x0, #0xf]
    // 0xa94e1c: DecompressPointer r1
    //     0xa94e1c: add             x1, x1, HEAP, lsl #32
    // 0xa94e20: r0 = controller()
    //     0xa94e20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa94e24: LoadField: r1 = r0->field_77
    //     0xa94e24: ldur            w1, [x0, #0x77]
    // 0xa94e28: DecompressPointer r1
    //     0xa94e28: add             x1, x1, HEAP, lsl #32
    // 0xa94e2c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa94e2c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa94e30: r0 = toList()
    //     0xa94e30: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa94e34: LoadField: r1 = r0->field_b
    //     0xa94e34: ldur            w1, [x0, #0xb]
    // 0xa94e38: cbz             w1, #0xa95258
    // 0xa94e3c: ldur            x2, [fp, #-8]
    // 0xa94e40: r0 = SvgPicture()
    //     0xa94e40: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa94e44: stur            x0, [fp, #-0x10]
    // 0xa94e48: r16 = 20.000000
    //     0xa94e48: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xa94e4c: ldr             x16, [x16, #0xac8]
    // 0xa94e50: r30 = 20.000000
    //     0xa94e50: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xa94e54: ldr             lr, [lr, #0xac8]
    // 0xa94e58: stp             lr, x16, [SP]
    // 0xa94e5c: mov             x1, x0
    // 0xa94e60: r2 = "assets/images/bar_chart.svg"
    //     0xa94e60: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f8f8] "assets/images/bar_chart.svg"
    //     0xa94e64: ldr             x2, [x2, #0x8f8]
    // 0xa94e68: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0xa94e68: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0xa94e6c: ldr             x4, [x4, #0x900]
    // 0xa94e70: r0 = SvgPicture.asset()
    //     0xa94e70: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa94e74: ldur            x2, [fp, #-8]
    // 0xa94e78: LoadField: r1 = r2->field_f
    //     0xa94e78: ldur            w1, [x2, #0xf]
    // 0xa94e7c: DecompressPointer r1
    //     0xa94e7c: add             x1, x1, HEAP, lsl #32
    // 0xa94e80: r0 = controller()
    //     0xa94e80: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa94e84: LoadField: r1 = r0->field_83
    //     0xa94e84: ldur            w1, [x0, #0x83]
    // 0xa94e88: DecompressPointer r1
    //     0xa94e88: add             x1, x1, HEAP, lsl #32
    // 0xa94e8c: r0 = value()
    //     0xa94e8c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa94e90: LoadField: r1 = r0->field_b
    //     0xa94e90: ldur            w1, [x0, #0xb]
    // 0xa94e94: DecompressPointer r1
    //     0xa94e94: add             x1, x1, HEAP, lsl #32
    // 0xa94e98: cmp             w1, NULL
    // 0xa94e9c: b.ne            #0xa94eac
    // 0xa94ea0: r5 = "Most Recent"
    //     0xa94ea0: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f908] "Most Recent"
    //     0xa94ea4: ldr             x5, [x5, #0x908]
    // 0xa94ea8: b               #0xa94eb0
    // 0xa94eac: mov             x5, x1
    // 0xa94eb0: ldur            x2, [fp, #-8]
    // 0xa94eb4: stur            x5, [fp, #-0x20]
    // 0xa94eb8: LoadField: r1 = r2->field_f
    //     0xa94eb8: ldur            w1, [x2, #0xf]
    // 0xa94ebc: DecompressPointer r1
    //     0xa94ebc: add             x1, x1, HEAP, lsl #32
    // 0xa94ec0: r0 = controller()
    //     0xa94ec0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa94ec4: LoadField: r1 = r0->field_7b
    //     0xa94ec4: ldur            w1, [x0, #0x7b]
    // 0xa94ec8: DecompressPointer r1
    //     0xa94ec8: add             x1, x1, HEAP, lsl #32
    // 0xa94ecc: r0 = value()
    //     0xa94ecc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa94ed0: LoadField: r1 = r0->field_b
    //     0xa94ed0: ldur            w1, [x0, #0xb]
    // 0xa94ed4: DecompressPointer r1
    //     0xa94ed4: add             x1, x1, HEAP, lsl #32
    // 0xa94ed8: cmp             w1, NULL
    // 0xa94edc: b.ne            #0xa94ee8
    // 0xa94ee0: r5 = Null
    //     0xa94ee0: mov             x5, NULL
    // 0xa94ee4: b               #0xa94f30
    // 0xa94ee8: LoadField: r0 = r1->field_b
    //     0xa94ee8: ldur            w0, [x1, #0xb]
    // 0xa94eec: DecompressPointer r0
    //     0xa94eec: add             x0, x0, HEAP, lsl #32
    // 0xa94ef0: ldur            x2, [fp, #-8]
    // 0xa94ef4: stur            x0, [fp, #-0x28]
    // 0xa94ef8: r1 = Function '<anonymous closure>':.
    //     0xa94ef8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea50] AnonymousClosure: (0x9ba648), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa94efc: ldr             x1, [x1, #0xa50]
    // 0xa94f00: r0 = AllocateClosure()
    //     0xa94f00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa94f04: r16 = <DropdownMenuItem<String>>
    //     0xa94f04: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f918] TypeArguments: <DropdownMenuItem<String>>
    //     0xa94f08: ldr             x16, [x16, #0x918]
    // 0xa94f0c: ldur            lr, [fp, #-0x28]
    // 0xa94f10: stp             lr, x16, [SP, #8]
    // 0xa94f14: str             x0, [SP]
    // 0xa94f18: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa94f18: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa94f1c: r0 = map()
    //     0xa94f1c: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0xa94f20: mov             x1, x0
    // 0xa94f24: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa94f24: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa94f28: r0 = toList()
    //     0xa94f28: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xa94f2c: mov             x5, x0
    // 0xa94f30: ldur            x0, [fp, #-8]
    // 0xa94f34: ldur            x3, [fp, #-0x10]
    // 0xa94f38: ldur            x4, [fp, #-0x18]
    // 0xa94f3c: mov             x2, x0
    // 0xa94f40: stur            x5, [fp, #-0x28]
    // 0xa94f44: r1 = Function '<anonymous closure>':.
    //     0xa94f44: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea58] AnonymousClosure: (0xa97428), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14fc2bc)
    //     0xa94f48: ldr             x1, [x1, #0xa58]
    // 0xa94f4c: r0 = AllocateClosure()
    //     0xa94f4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa94f50: ldur            x2, [fp, #-8]
    // 0xa94f54: r1 = Function '<anonymous closure>':.
    //     0xa94f54: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea60] AnonymousClosure: (0x9b66a0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa94f58: ldr             x1, [x1, #0xa60]
    // 0xa94f5c: stur            x0, [fp, #-0x30]
    // 0xa94f60: r0 = AllocateClosure()
    //     0xa94f60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa94f64: r1 = <String>
    //     0xa94f64: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xa94f68: stur            x0, [fp, #-0x40]
    // 0xa94f6c: r0 = DropdownButton()
    //     0xa94f6c: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0xa94f70: stur            x0, [fp, #-0x58]
    // 0xa94f74: r16 = Instance_Color
    //     0xa94f74: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa94f78: r30 = Instance_Icon
    //     0xa94f78: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f930] Obj!Icon@d65ef1
    //     0xa94f7c: ldr             lr, [lr, #0x930]
    // 0xa94f80: stp             lr, x16, [SP, #8]
    // 0xa94f84: ldur            x16, [fp, #-0x40]
    // 0xa94f88: str             x16, [SP]
    // 0xa94f8c: mov             x1, x0
    // 0xa94f90: ldur            x2, [fp, #-0x28]
    // 0xa94f94: ldur            x3, [fp, #-0x30]
    // 0xa94f98: ldur            x5, [fp, #-0x20]
    // 0xa94f9c: r4 = const [0, 0x7, 0x3, 0x4, dropdownColor, 0x4, icon, 0x5, onTap, 0x6, null]
    //     0xa94f9c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f938] List(11) [0, 0x7, 0x3, 0x4, "dropdownColor", 0x4, "icon", 0x5, "onTap", 0x6, Null]
    //     0xa94fa0: ldr             x4, [x4, #0x938]
    // 0xa94fa4: r0 = DropdownButton()
    //     0xa94fa4: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0xa94fa8: r0 = DropdownButtonHideUnderline()
    //     0xa94fa8: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0xa94fac: mov             x3, x0
    // 0xa94fb0: ldur            x0, [fp, #-0x58]
    // 0xa94fb4: stur            x3, [fp, #-0x20]
    // 0xa94fb8: StoreField: r3->field_b = r0
    //     0xa94fb8: stur            w0, [x3, #0xb]
    // 0xa94fbc: r1 = Null
    //     0xa94fbc: mov             x1, NULL
    // 0xa94fc0: r2 = 6
    //     0xa94fc0: movz            x2, #0x6
    // 0xa94fc4: r0 = AllocateArray()
    //     0xa94fc4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa94fc8: mov             x2, x0
    // 0xa94fcc: ldur            x0, [fp, #-0x10]
    // 0xa94fd0: stur            x2, [fp, #-0x28]
    // 0xa94fd4: StoreField: r2->field_f = r0
    //     0xa94fd4: stur            w0, [x2, #0xf]
    // 0xa94fd8: r16 = Instance_SizedBox
    //     0xa94fd8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f940] Obj!SizedBox@d67ec1
    //     0xa94fdc: ldr             x16, [x16, #0x940]
    // 0xa94fe0: StoreField: r2->field_13 = r16
    //     0xa94fe0: stur            w16, [x2, #0x13]
    // 0xa94fe4: ldur            x0, [fp, #-0x20]
    // 0xa94fe8: ArrayStore: r2[0] = r0  ; List_4
    //     0xa94fe8: stur            w0, [x2, #0x17]
    // 0xa94fec: r1 = <Widget>
    //     0xa94fec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa94ff0: r0 = AllocateGrowableArray()
    //     0xa94ff0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa94ff4: mov             x1, x0
    // 0xa94ff8: ldur            x0, [fp, #-0x28]
    // 0xa94ffc: stur            x1, [fp, #-0x10]
    // 0xa95000: StoreField: r1->field_f = r0
    //     0xa95000: stur            w0, [x1, #0xf]
    // 0xa95004: r2 = 6
    //     0xa95004: movz            x2, #0x6
    // 0xa95008: StoreField: r1->field_b = r2
    //     0xa95008: stur            w2, [x1, #0xb]
    // 0xa9500c: r0 = Row()
    //     0xa9500c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa95010: mov             x1, x0
    // 0xa95014: r0 = Instance_Axis
    //     0xa95014: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa95018: stur            x1, [fp, #-0x20]
    // 0xa9501c: StoreField: r1->field_f = r0
    //     0xa9501c: stur            w0, [x1, #0xf]
    // 0xa95020: r0 = Instance_MainAxisAlignment
    //     0xa95020: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa95024: ldr             x0, [x0, #0xa08]
    // 0xa95028: StoreField: r1->field_13 = r0
    //     0xa95028: stur            w0, [x1, #0x13]
    // 0xa9502c: r2 = Instance_MainAxisSize
    //     0xa9502c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa95030: ldr             x2, [x2, #0xa10]
    // 0xa95034: ArrayStore: r1[0] = r2  ; List_4
    //     0xa95034: stur            w2, [x1, #0x17]
    // 0xa95038: r3 = Instance_CrossAxisAlignment
    //     0xa95038: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa9503c: ldr             x3, [x3, #0xa18]
    // 0xa95040: StoreField: r1->field_1b = r3
    //     0xa95040: stur            w3, [x1, #0x1b]
    // 0xa95044: r4 = Instance_VerticalDirection
    //     0xa95044: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa95048: ldr             x4, [x4, #0xa20]
    // 0xa9504c: StoreField: r1->field_23 = r4
    //     0xa9504c: stur            w4, [x1, #0x23]
    // 0xa95050: r5 = Instance_Clip
    //     0xa95050: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa95054: ldr             x5, [x5, #0x38]
    // 0xa95058: StoreField: r1->field_2b = r5
    //     0xa95058: stur            w5, [x1, #0x2b]
    // 0xa9505c: StoreField: r1->field_2f = rZR
    //     0xa9505c: stur            xzr, [x1, #0x2f]
    // 0xa95060: ldur            x6, [fp, #-0x10]
    // 0xa95064: StoreField: r1->field_b = r6
    //     0xa95064: stur            w6, [x1, #0xb]
    // 0xa95068: r0 = SizedBox()
    //     0xa95068: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa9506c: mov             x1, x0
    // 0xa95070: r0 = 155.000000
    //     0xa95070: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f948] 155
    //     0xa95074: ldr             x0, [x0, #0x948]
    // 0xa95078: stur            x1, [fp, #-0x10]
    // 0xa9507c: StoreField: r1->field_f = r0
    //     0xa9507c: stur            w0, [x1, #0xf]
    // 0xa95080: ldur            x0, [fp, #-0x20]
    // 0xa95084: StoreField: r1->field_b = r0
    //     0xa95084: stur            w0, [x1, #0xb]
    // 0xa95088: r0 = Align()
    //     0xa95088: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa9508c: mov             x2, x0
    // 0xa95090: r0 = Instance_Alignment
    //     0xa95090: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xa95094: ldr             x0, [x0, #0x950]
    // 0xa95098: stur            x2, [fp, #-0x20]
    // 0xa9509c: StoreField: r2->field_f = r0
    //     0xa9509c: stur            w0, [x2, #0xf]
    // 0xa950a0: ldur            x0, [fp, #-0x10]
    // 0xa950a4: StoreField: r2->field_b = r0
    //     0xa950a4: stur            w0, [x2, #0xb]
    // 0xa950a8: ldur            x0, [fp, #-8]
    // 0xa950ac: LoadField: r1 = r0->field_f
    //     0xa950ac: ldur            w1, [x0, #0xf]
    // 0xa950b0: DecompressPointer r1
    //     0xa950b0: add             x1, x1, HEAP, lsl #32
    // 0xa950b4: r0 = controller()
    //     0xa950b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa950b8: LoadField: r1 = r0->field_77
    //     0xa950b8: ldur            w1, [x0, #0x77]
    // 0xa950bc: DecompressPointer r1
    //     0xa950bc: add             x1, x1, HEAP, lsl #32
    // 0xa950c0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa950c0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa950c4: r0 = toList()
    //     0xa950c4: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa950c8: LoadField: r1 = r0->field_b
    //     0xa950c8: ldur            w1, [x0, #0xb]
    // 0xa950cc: r3 = LoadInt32Instr(r1)
    //     0xa950cc: sbfx            x3, x1, #1, #0x1f
    // 0xa950d0: stur            x3, [fp, #-0x38]
    // 0xa950d4: r1 = Function '<anonymous closure>':.
    //     0xa950d4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea68] AnonymousClosure: (0xa928e0), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14fc2bc)
    //     0xa950d8: ldr             x1, [x1, #0xa68]
    // 0xa950dc: r2 = Null
    //     0xa950dc: mov             x2, NULL
    // 0xa950e0: r0 = AllocateClosure()
    //     0xa950e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa950e4: ldur            x2, [fp, #-8]
    // 0xa950e8: r1 = Function '<anonymous closure>':.
    //     0xa950e8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea70] AnonymousClosure: (0xa95328), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14fc2bc)
    //     0xa950ec: ldr             x1, [x1, #0xa70]
    // 0xa950f0: stur            x0, [fp, #-8]
    // 0xa950f4: r0 = AllocateClosure()
    //     0xa950f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa950f8: stur            x0, [fp, #-0x10]
    // 0xa950fc: r0 = ListView()
    //     0xa950fc: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xa95100: stur            x0, [fp, #-0x28]
    // 0xa95104: r16 = Instance_NeverScrollableScrollPhysics
    //     0xa95104: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xa95108: ldr             x16, [x16, #0x1c8]
    // 0xa9510c: r30 = true
    //     0xa9510c: add             lr, NULL, #0x20  ; true
    // 0xa95110: stp             lr, x16, [SP]
    // 0xa95114: mov             x1, x0
    // 0xa95118: ldur            x2, [fp, #-0x10]
    // 0xa9511c: ldur            x3, [fp, #-0x38]
    // 0xa95120: ldur            x5, [fp, #-8]
    // 0xa95124: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x4, shrinkWrap, 0x5, null]
    //     0xa95124: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f968] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x4, "shrinkWrap", 0x5, Null]
    //     0xa95128: ldr             x4, [x4, #0x968]
    // 0xa9512c: r0 = ListView.separated()
    //     0xa9512c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xa95130: r1 = Null
    //     0xa95130: mov             x1, NULL
    // 0xa95134: r2 = 6
    //     0xa95134: movz            x2, #0x6
    // 0xa95138: r0 = AllocateArray()
    //     0xa95138: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa9513c: mov             x2, x0
    // 0xa95140: ldur            x0, [fp, #-0x20]
    // 0xa95144: stur            x2, [fp, #-8]
    // 0xa95148: StoreField: r2->field_f = r0
    //     0xa95148: stur            w0, [x2, #0xf]
    // 0xa9514c: r16 = Instance_SizedBox
    //     0xa9514c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34a60] Obj!SizedBox@d68041
    //     0xa95150: ldr             x16, [x16, #0xa60]
    // 0xa95154: StoreField: r2->field_13 = r16
    //     0xa95154: stur            w16, [x2, #0x13]
    // 0xa95158: ldur            x0, [fp, #-0x28]
    // 0xa9515c: ArrayStore: r2[0] = r0  ; List_4
    //     0xa9515c: stur            w0, [x2, #0x17]
    // 0xa95160: r1 = <Widget>
    //     0xa95160: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa95164: r0 = AllocateGrowableArray()
    //     0xa95164: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa95168: mov             x1, x0
    // 0xa9516c: ldur            x0, [fp, #-8]
    // 0xa95170: stur            x1, [fp, #-0x10]
    // 0xa95174: StoreField: r1->field_f = r0
    //     0xa95174: stur            w0, [x1, #0xf]
    // 0xa95178: r0 = 6
    //     0xa95178: movz            x0, #0x6
    // 0xa9517c: StoreField: r1->field_b = r0
    //     0xa9517c: stur            w0, [x1, #0xb]
    // 0xa95180: r0 = Column()
    //     0xa95180: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa95184: mov             x2, x0
    // 0xa95188: r0 = Instance_Axis
    //     0xa95188: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa9518c: stur            x2, [fp, #-8]
    // 0xa95190: StoreField: r2->field_f = r0
    //     0xa95190: stur            w0, [x2, #0xf]
    // 0xa95194: r3 = Instance_MainAxisAlignment
    //     0xa95194: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa95198: ldr             x3, [x3, #0xa08]
    // 0xa9519c: StoreField: r2->field_13 = r3
    //     0xa9519c: stur            w3, [x2, #0x13]
    // 0xa951a0: r4 = Instance_MainAxisSize
    //     0xa951a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa951a4: ldr             x4, [x4, #0xa10]
    // 0xa951a8: ArrayStore: r2[0] = r4  ; List_4
    //     0xa951a8: stur            w4, [x2, #0x17]
    // 0xa951ac: r5 = Instance_CrossAxisAlignment
    //     0xa951ac: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa951b0: ldr             x5, [x5, #0xa18]
    // 0xa951b4: StoreField: r2->field_1b = r5
    //     0xa951b4: stur            w5, [x2, #0x1b]
    // 0xa951b8: r6 = Instance_VerticalDirection
    //     0xa951b8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa951bc: ldr             x6, [x6, #0xa20]
    // 0xa951c0: StoreField: r2->field_23 = r6
    //     0xa951c0: stur            w6, [x2, #0x23]
    // 0xa951c4: r7 = Instance_Clip
    //     0xa951c4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa951c8: ldr             x7, [x7, #0x38]
    // 0xa951cc: StoreField: r2->field_2b = r7
    //     0xa951cc: stur            w7, [x2, #0x2b]
    // 0xa951d0: StoreField: r2->field_2f = rZR
    //     0xa951d0: stur            xzr, [x2, #0x2f]
    // 0xa951d4: ldur            x1, [fp, #-0x10]
    // 0xa951d8: StoreField: r2->field_b = r1
    //     0xa951d8: stur            w1, [x2, #0xb]
    // 0xa951dc: ldur            x8, [fp, #-0x18]
    // 0xa951e0: LoadField: r1 = r8->field_b
    //     0xa951e0: ldur            w1, [x8, #0xb]
    // 0xa951e4: LoadField: r9 = r8->field_f
    //     0xa951e4: ldur            w9, [x8, #0xf]
    // 0xa951e8: DecompressPointer r9
    //     0xa951e8: add             x9, x9, HEAP, lsl #32
    // 0xa951ec: LoadField: r10 = r9->field_b
    //     0xa951ec: ldur            w10, [x9, #0xb]
    // 0xa951f0: r9 = LoadInt32Instr(r1)
    //     0xa951f0: sbfx            x9, x1, #1, #0x1f
    // 0xa951f4: stur            x9, [fp, #-0x38]
    // 0xa951f8: r1 = LoadInt32Instr(r10)
    //     0xa951f8: sbfx            x1, x10, #1, #0x1f
    // 0xa951fc: cmp             x9, x1
    // 0xa95200: b.ne            #0xa9520c
    // 0xa95204: mov             x1, x8
    // 0xa95208: r0 = _growToNextCapacity()
    //     0xa95208: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa9520c: ldur            x2, [fp, #-0x18]
    // 0xa95210: ldur            x3, [fp, #-0x38]
    // 0xa95214: add             x0, x3, #1
    // 0xa95218: lsl             x1, x0, #1
    // 0xa9521c: StoreField: r2->field_b = r1
    //     0xa9521c: stur            w1, [x2, #0xb]
    // 0xa95220: LoadField: r1 = r2->field_f
    //     0xa95220: ldur            w1, [x2, #0xf]
    // 0xa95224: DecompressPointer r1
    //     0xa95224: add             x1, x1, HEAP, lsl #32
    // 0xa95228: ldur            x0, [fp, #-8]
    // 0xa9522c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa9522c: add             x25, x1, x3, lsl #2
    //     0xa95230: add             x25, x25, #0xf
    //     0xa95234: str             w0, [x25]
    //     0xa95238: tbz             w0, #0, #0xa95254
    //     0xa9523c: ldurb           w16, [x1, #-1]
    //     0xa95240: ldurb           w17, [x0, #-1]
    //     0xa95244: and             x16, x17, x16, lsr #2
    //     0xa95248: tst             x16, HEAP, lsr #32
    //     0xa9524c: b.eq            #0xa95254
    //     0xa95250: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa95254: b               #0xa9525c
    // 0xa95258: ldur            x2, [fp, #-0x18]
    // 0xa9525c: r0 = Column()
    //     0xa9525c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa95260: mov             x1, x0
    // 0xa95264: r0 = Instance_Axis
    //     0xa95264: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa95268: stur            x1, [fp, #-8]
    // 0xa9526c: StoreField: r1->field_f = r0
    //     0xa9526c: stur            w0, [x1, #0xf]
    // 0xa95270: r2 = Instance_MainAxisAlignment
    //     0xa95270: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa95274: ldr             x2, [x2, #0xa08]
    // 0xa95278: StoreField: r1->field_13 = r2
    //     0xa95278: stur            w2, [x1, #0x13]
    // 0xa9527c: r2 = Instance_MainAxisSize
    //     0xa9527c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa95280: ldr             x2, [x2, #0xa10]
    // 0xa95284: ArrayStore: r1[0] = r2  ; List_4
    //     0xa95284: stur            w2, [x1, #0x17]
    // 0xa95288: r2 = Instance_CrossAxisAlignment
    //     0xa95288: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa9528c: ldr             x2, [x2, #0xa18]
    // 0xa95290: StoreField: r1->field_1b = r2
    //     0xa95290: stur            w2, [x1, #0x1b]
    // 0xa95294: r2 = Instance_VerticalDirection
    //     0xa95294: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa95298: ldr             x2, [x2, #0xa20]
    // 0xa9529c: StoreField: r1->field_23 = r2
    //     0xa9529c: stur            w2, [x1, #0x23]
    // 0xa952a0: r2 = Instance_Clip
    //     0xa952a0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa952a4: ldr             x2, [x2, #0x38]
    // 0xa952a8: StoreField: r1->field_2b = r2
    //     0xa952a8: stur            w2, [x1, #0x2b]
    // 0xa952ac: StoreField: r1->field_2f = rZR
    //     0xa952ac: stur            xzr, [x1, #0x2f]
    // 0xa952b0: ldur            x2, [fp, #-0x18]
    // 0xa952b4: StoreField: r1->field_b = r2
    //     0xa952b4: stur            w2, [x1, #0xb]
    // 0xa952b8: r0 = Padding()
    //     0xa952b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa952bc: mov             x1, x0
    // 0xa952c0: r0 = Instance_EdgeInsets
    //     0xa952c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xa952c4: ldr             x0, [x0, #0x1f0]
    // 0xa952c8: stur            x1, [fp, #-0x10]
    // 0xa952cc: StoreField: r1->field_f = r0
    //     0xa952cc: stur            w0, [x1, #0xf]
    // 0xa952d0: ldur            x0, [fp, #-8]
    // 0xa952d4: StoreField: r1->field_b = r0
    //     0xa952d4: stur            w0, [x1, #0xb]
    // 0xa952d8: r0 = SingleChildScrollView()
    //     0xa952d8: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xa952dc: r1 = Instance_Axis
    //     0xa952dc: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa952e0: StoreField: r0->field_b = r1
    //     0xa952e0: stur            w1, [x0, #0xb]
    // 0xa952e4: r1 = false
    //     0xa952e4: add             x1, NULL, #0x30  ; false
    // 0xa952e8: StoreField: r0->field_f = r1
    //     0xa952e8: stur            w1, [x0, #0xf]
    // 0xa952ec: ldur            x1, [fp, #-0x10]
    // 0xa952f0: StoreField: r0->field_23 = r1
    //     0xa952f0: stur            w1, [x0, #0x23]
    // 0xa952f4: r1 = Instance_DragStartBehavior
    //     0xa952f4: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xa952f8: StoreField: r0->field_27 = r1
    //     0xa952f8: stur            w1, [x0, #0x27]
    // 0xa952fc: r1 = Instance_Clip
    //     0xa952fc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa95300: ldr             x1, [x1, #0x7e0]
    // 0xa95304: StoreField: r0->field_2b = r1
    //     0xa95304: stur            w1, [x0, #0x2b]
    // 0xa95308: r1 = Instance_HitTestBehavior
    //     0xa95308: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xa9530c: ldr             x1, [x1, #0x288]
    // 0xa95310: StoreField: r0->field_2f = r1
    //     0xa95310: stur            w1, [x0, #0x2f]
    // 0xa95314: LeaveFrame
    //     0xa95314: mov             SP, fp
    //     0xa95318: ldp             fp, lr, [SP], #0x10
    // 0xa9531c: ret
    //     0xa9531c: ret             
    // 0xa95320: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa95320: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa95324: b               #0xa92914
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa95328, size: 0x1428
    // 0xa95328: EnterFrame
    //     0xa95328: stp             fp, lr, [SP, #-0x10]!
    //     0xa9532c: mov             fp, SP
    // 0xa95330: AllocStack(0x80)
    //     0xa95330: sub             SP, SP, #0x80
    // 0xa95334: SetupParameters()
    //     0xa95334: ldr             x0, [fp, #0x20]
    //     0xa95338: ldur            w1, [x0, #0x17]
    //     0xa9533c: add             x1, x1, HEAP, lsl #32
    //     0xa95340: stur            x1, [fp, #-8]
    // 0xa95344: CheckStackOverflow
    //     0xa95344: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa95348: cmp             SP, x16
    //     0xa9534c: b.ls            #0xa966f8
    // 0xa95350: r1 = 2
    //     0xa95350: movz            x1, #0x2
    // 0xa95354: r0 = AllocateContext()
    //     0xa95354: bl              #0x16f6108  ; AllocateContextStub
    // 0xa95358: mov             x2, x0
    // 0xa9535c: ldur            x0, [fp, #-8]
    // 0xa95360: stur            x2, [fp, #-0x10]
    // 0xa95364: StoreField: r2->field_b = r0
    //     0xa95364: stur            w0, [x2, #0xb]
    // 0xa95368: ldr             x1, [fp, #0x18]
    // 0xa9536c: StoreField: r2->field_f = r1
    //     0xa9536c: stur            w1, [x2, #0xf]
    // 0xa95370: ldr             x3, [fp, #0x10]
    // 0xa95374: StoreField: r2->field_13 = r3
    //     0xa95374: stur            w3, [x2, #0x13]
    // 0xa95378: r0 = of()
    //     0xa95378: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa9537c: LoadField: r1 = r0->field_5b
    //     0xa9537c: ldur            w1, [x0, #0x5b]
    // 0xa95380: DecompressPointer r1
    //     0xa95380: add             x1, x1, HEAP, lsl #32
    // 0xa95384: r0 = LoadClassIdInstr(r1)
    //     0xa95384: ldur            x0, [x1, #-1]
    //     0xa95388: ubfx            x0, x0, #0xc, #0x14
    // 0xa9538c: d0 = 0.200000
    //     0xa9538c: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xa95390: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa95390: sub             lr, x0, #0xffa
    //     0xa95394: ldr             lr, [x21, lr, lsl #3]
    //     0xa95398: blr             lr
    // 0xa9539c: mov             x2, x0
    // 0xa953a0: r1 = Null
    //     0xa953a0: mov             x1, NULL
    // 0xa953a4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa953a4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa953a8: r0 = Border.all()
    //     0xa953a8: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa953ac: stur            x0, [fp, #-0x18]
    // 0xa953b0: r0 = Radius()
    //     0xa953b0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa953b4: d0 = 15.000000
    //     0xa953b4: fmov            d0, #15.00000000
    // 0xa953b8: stur            x0, [fp, #-0x20]
    // 0xa953bc: StoreField: r0->field_7 = d0
    //     0xa953bc: stur            d0, [x0, #7]
    // 0xa953c0: StoreField: r0->field_f = d0
    //     0xa953c0: stur            d0, [x0, #0xf]
    // 0xa953c4: r0 = BorderRadius()
    //     0xa953c4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa953c8: mov             x1, x0
    // 0xa953cc: ldur            x0, [fp, #-0x20]
    // 0xa953d0: stur            x1, [fp, #-0x28]
    // 0xa953d4: StoreField: r1->field_7 = r0
    //     0xa953d4: stur            w0, [x1, #7]
    // 0xa953d8: StoreField: r1->field_b = r0
    //     0xa953d8: stur            w0, [x1, #0xb]
    // 0xa953dc: StoreField: r1->field_f = r0
    //     0xa953dc: stur            w0, [x1, #0xf]
    // 0xa953e0: StoreField: r1->field_13 = r0
    //     0xa953e0: stur            w0, [x1, #0x13]
    // 0xa953e4: r0 = BoxDecoration()
    //     0xa953e4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa953e8: mov             x2, x0
    // 0xa953ec: ldur            x0, [fp, #-0x18]
    // 0xa953f0: stur            x2, [fp, #-0x20]
    // 0xa953f4: StoreField: r2->field_f = r0
    //     0xa953f4: stur            w0, [x2, #0xf]
    // 0xa953f8: ldur            x0, [fp, #-0x28]
    // 0xa953fc: StoreField: r2->field_13 = r0
    //     0xa953fc: stur            w0, [x2, #0x13]
    // 0xa95400: r0 = Instance_BoxShape
    //     0xa95400: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa95404: ldr             x0, [x0, #0x80]
    // 0xa95408: StoreField: r2->field_23 = r0
    //     0xa95408: stur            w0, [x2, #0x23]
    // 0xa9540c: r1 = Instance_Color
    //     0xa9540c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa95410: d0 = 0.050000
    //     0xa95410: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0xa95414: r0 = withOpacity()
    //     0xa95414: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa95418: stur            x0, [fp, #-0x18]
    // 0xa9541c: r0 = BoxDecoration()
    //     0xa9541c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa95420: mov             x2, x0
    // 0xa95424: ldur            x0, [fp, #-0x18]
    // 0xa95428: stur            x2, [fp, #-0x28]
    // 0xa9542c: StoreField: r2->field_7 = r0
    //     0xa9542c: stur            w0, [x2, #7]
    // 0xa95430: r0 = Instance_BoxShape
    //     0xa95430: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xa95434: ldr             x0, [x0, #0x970]
    // 0xa95438: StoreField: r2->field_23 = r0
    //     0xa95438: stur            w0, [x2, #0x23]
    // 0xa9543c: ldur            x0, [fp, #-8]
    // 0xa95440: LoadField: r1 = r0->field_f
    //     0xa95440: ldur            w1, [x0, #0xf]
    // 0xa95444: DecompressPointer r1
    //     0xa95444: add             x1, x1, HEAP, lsl #32
    // 0xa95448: r0 = controller()
    //     0xa95448: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa9544c: LoadField: r1 = r0->field_77
    //     0xa9544c: ldur            w1, [x0, #0x77]
    // 0xa95450: DecompressPointer r1
    //     0xa95450: add             x1, x1, HEAP, lsl #32
    // 0xa95454: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa95454: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa95458: r0 = toList()
    //     0xa95458: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa9545c: mov             x3, x0
    // 0xa95460: ldur            x2, [fp, #-0x10]
    // 0xa95464: LoadField: r0 = r2->field_13
    //     0xa95464: ldur            w0, [x2, #0x13]
    // 0xa95468: DecompressPointer r0
    //     0xa95468: add             x0, x0, HEAP, lsl #32
    // 0xa9546c: LoadField: r1 = r3->field_b
    //     0xa9546c: ldur            w1, [x3, #0xb]
    // 0xa95470: r4 = LoadInt32Instr(r0)
    //     0xa95470: sbfx            x4, x0, #1, #0x1f
    //     0xa95474: tbz             w0, #0, #0xa9547c
    //     0xa95478: ldur            x4, [x0, #7]
    // 0xa9547c: r0 = LoadInt32Instr(r1)
    //     0xa9547c: sbfx            x0, x1, #1, #0x1f
    // 0xa95480: mov             x1, x4
    // 0xa95484: cmp             x1, x0
    // 0xa95488: b.hs            #0xa96700
    // 0xa9548c: LoadField: r0 = r3->field_f
    //     0xa9548c: ldur            w0, [x3, #0xf]
    // 0xa95490: DecompressPointer r0
    //     0xa95490: add             x0, x0, HEAP, lsl #32
    // 0xa95494: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa95494: add             x16, x0, x4, lsl #2
    //     0xa95498: ldur            w1, [x16, #0xf]
    // 0xa9549c: DecompressPointer r1
    //     0xa9549c: add             x1, x1, HEAP, lsl #32
    // 0xa954a0: cmp             w1, NULL
    // 0xa954a4: b.ne            #0xa954b0
    // 0xa954a8: r0 = Null
    //     0xa954a8: mov             x0, NULL
    // 0xa954ac: b               #0xa954ec
    // 0xa954b0: LoadField: r0 = r1->field_7
    //     0xa954b0: ldur            w0, [x1, #7]
    // 0xa954b4: DecompressPointer r0
    //     0xa954b4: add             x0, x0, HEAP, lsl #32
    // 0xa954b8: cmp             w0, NULL
    // 0xa954bc: b.ne            #0xa954c8
    // 0xa954c0: r0 = Null
    //     0xa954c0: mov             x0, NULL
    // 0xa954c4: b               #0xa954ec
    // 0xa954c8: stp             xzr, x0, [SP]
    // 0xa954cc: r0 = []()
    //     0xa954cc: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xa954d0: r1 = LoadClassIdInstr(r0)
    //     0xa954d0: ldur            x1, [x0, #-1]
    //     0xa954d4: ubfx            x1, x1, #0xc, #0x14
    // 0xa954d8: str             x0, [SP]
    // 0xa954dc: mov             x0, x1
    // 0xa954e0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xa954e0: sub             lr, x0, #1, lsl #12
    //     0xa954e4: ldr             lr, [x21, lr, lsl #3]
    //     0xa954e8: blr             lr
    // 0xa954ec: cmp             w0, NULL
    // 0xa954f0: b.ne            #0xa954fc
    // 0xa954f4: r3 = ""
    //     0xa954f4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa954f8: b               #0xa95500
    // 0xa954fc: mov             x3, x0
    // 0xa95500: ldur            x0, [fp, #-8]
    // 0xa95504: ldur            x2, [fp, #-0x10]
    // 0xa95508: stur            x3, [fp, #-0x18]
    // 0xa9550c: LoadField: r1 = r2->field_f
    //     0xa9550c: ldur            w1, [x2, #0xf]
    // 0xa95510: DecompressPointer r1
    //     0xa95510: add             x1, x1, HEAP, lsl #32
    // 0xa95514: r0 = of()
    //     0xa95514: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa95518: LoadField: r1 = r0->field_87
    //     0xa95518: ldur            w1, [x0, #0x87]
    // 0xa9551c: DecompressPointer r1
    //     0xa9551c: add             x1, x1, HEAP, lsl #32
    // 0xa95520: LoadField: r0 = r1->field_7
    //     0xa95520: ldur            w0, [x1, #7]
    // 0xa95524: DecompressPointer r0
    //     0xa95524: add             x0, x0, HEAP, lsl #32
    // 0xa95528: stur            x0, [fp, #-0x30]
    // 0xa9552c: r1 = Instance_Color
    //     0xa9552c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa95530: d0 = 0.500000
    //     0xa95530: fmov            d0, #0.50000000
    // 0xa95534: r0 = withOpacity()
    //     0xa95534: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa95538: r16 = 16.000000
    //     0xa95538: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa9553c: ldr             x16, [x16, #0x188]
    // 0xa95540: stp             x0, x16, [SP]
    // 0xa95544: ldur            x1, [fp, #-0x30]
    // 0xa95548: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa95548: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa9554c: ldr             x4, [x4, #0xaa0]
    // 0xa95550: r0 = copyWith()
    //     0xa95550: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa95554: stur            x0, [fp, #-0x30]
    // 0xa95558: r0 = Text()
    //     0xa95558: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa9555c: mov             x1, x0
    // 0xa95560: ldur            x0, [fp, #-0x18]
    // 0xa95564: stur            x1, [fp, #-0x38]
    // 0xa95568: StoreField: r1->field_b = r0
    //     0xa95568: stur            w0, [x1, #0xb]
    // 0xa9556c: ldur            x0, [fp, #-0x30]
    // 0xa95570: StoreField: r1->field_13 = r0
    //     0xa95570: stur            w0, [x1, #0x13]
    // 0xa95574: r0 = Center()
    //     0xa95574: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa95578: mov             x1, x0
    // 0xa9557c: r0 = Instance_Alignment
    //     0xa9557c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa95580: ldr             x0, [x0, #0xb10]
    // 0xa95584: stur            x1, [fp, #-0x18]
    // 0xa95588: StoreField: r1->field_f = r0
    //     0xa95588: stur            w0, [x1, #0xf]
    // 0xa9558c: ldur            x0, [fp, #-0x38]
    // 0xa95590: StoreField: r1->field_b = r0
    //     0xa95590: stur            w0, [x1, #0xb]
    // 0xa95594: r0 = Container()
    //     0xa95594: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa95598: stur            x0, [fp, #-0x30]
    // 0xa9559c: r16 = 34.000000
    //     0xa9559c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xa955a0: ldr             x16, [x16, #0x978]
    // 0xa955a4: r30 = 34.000000
    //     0xa955a4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xa955a8: ldr             lr, [lr, #0x978]
    // 0xa955ac: stp             lr, x16, [SP, #0x18]
    // 0xa955b0: r16 = Instance_EdgeInsets
    //     0xa955b0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xa955b4: ldr             x16, [x16, #0x980]
    // 0xa955b8: ldur            lr, [fp, #-0x28]
    // 0xa955bc: stp             lr, x16, [SP, #8]
    // 0xa955c0: ldur            x16, [fp, #-0x18]
    // 0xa955c4: str             x16, [SP]
    // 0xa955c8: mov             x1, x0
    // 0xa955cc: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xa955cc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xa955d0: ldr             x4, [x4, #0x988]
    // 0xa955d4: r0 = Container()
    //     0xa955d4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa955d8: ldur            x0, [fp, #-8]
    // 0xa955dc: LoadField: r1 = r0->field_f
    //     0xa955dc: ldur            w1, [x0, #0xf]
    // 0xa955e0: DecompressPointer r1
    //     0xa955e0: add             x1, x1, HEAP, lsl #32
    // 0xa955e4: r0 = controller()
    //     0xa955e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa955e8: LoadField: r1 = r0->field_77
    //     0xa955e8: ldur            w1, [x0, #0x77]
    // 0xa955ec: DecompressPointer r1
    //     0xa955ec: add             x1, x1, HEAP, lsl #32
    // 0xa955f0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa955f0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa955f4: r0 = toList()
    //     0xa955f4: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa955f8: mov             x3, x0
    // 0xa955fc: ldur            x2, [fp, #-0x10]
    // 0xa95600: LoadField: r0 = r2->field_13
    //     0xa95600: ldur            w0, [x2, #0x13]
    // 0xa95604: DecompressPointer r0
    //     0xa95604: add             x0, x0, HEAP, lsl #32
    // 0xa95608: LoadField: r1 = r3->field_b
    //     0xa95608: ldur            w1, [x3, #0xb]
    // 0xa9560c: r4 = LoadInt32Instr(r0)
    //     0xa9560c: sbfx            x4, x0, #1, #0x1f
    //     0xa95610: tbz             w0, #0, #0xa95618
    //     0xa95614: ldur            x4, [x0, #7]
    // 0xa95618: r0 = LoadInt32Instr(r1)
    //     0xa95618: sbfx            x0, x1, #1, #0x1f
    // 0xa9561c: mov             x1, x4
    // 0xa95620: cmp             x1, x0
    // 0xa95624: b.hs            #0xa96704
    // 0xa95628: LoadField: r0 = r3->field_f
    //     0xa95628: ldur            w0, [x3, #0xf]
    // 0xa9562c: DecompressPointer r0
    //     0xa9562c: add             x0, x0, HEAP, lsl #32
    // 0xa95630: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa95630: add             x16, x0, x4, lsl #2
    //     0xa95634: ldur            w1, [x16, #0xf]
    // 0xa95638: DecompressPointer r1
    //     0xa95638: add             x1, x1, HEAP, lsl #32
    // 0xa9563c: cmp             w1, NULL
    // 0xa95640: b.ne            #0xa9564c
    // 0xa95644: r0 = Null
    //     0xa95644: mov             x0, NULL
    // 0xa95648: b               #0xa95654
    // 0xa9564c: LoadField: r0 = r1->field_7
    //     0xa9564c: ldur            w0, [x1, #7]
    // 0xa95650: DecompressPointer r0
    //     0xa95650: add             x0, x0, HEAP, lsl #32
    // 0xa95654: cmp             w0, NULL
    // 0xa95658: b.ne            #0xa95664
    // 0xa9565c: r3 = ""
    //     0xa9565c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa95660: b               #0xa95668
    // 0xa95664: mov             x3, x0
    // 0xa95668: ldur            x0, [fp, #-8]
    // 0xa9566c: stur            x3, [fp, #-0x18]
    // 0xa95670: LoadField: r1 = r2->field_f
    //     0xa95670: ldur            w1, [x2, #0xf]
    // 0xa95674: DecompressPointer r1
    //     0xa95674: add             x1, x1, HEAP, lsl #32
    // 0xa95678: r0 = of()
    //     0xa95678: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa9567c: LoadField: r1 = r0->field_87
    //     0xa9567c: ldur            w1, [x0, #0x87]
    // 0xa95680: DecompressPointer r1
    //     0xa95680: add             x1, x1, HEAP, lsl #32
    // 0xa95684: LoadField: r0 = r1->field_7
    //     0xa95684: ldur            w0, [x1, #7]
    // 0xa95688: DecompressPointer r0
    //     0xa95688: add             x0, x0, HEAP, lsl #32
    // 0xa9568c: r16 = 14.000000
    //     0xa9568c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa95690: ldr             x16, [x16, #0x1d8]
    // 0xa95694: r30 = Instance_Color
    //     0xa95694: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa95698: stp             lr, x16, [SP]
    // 0xa9569c: mov             x1, x0
    // 0xa956a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa956a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa956a4: ldr             x4, [x4, #0xaa0]
    // 0xa956a8: r0 = copyWith()
    //     0xa956a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa956ac: stur            x0, [fp, #-0x28]
    // 0xa956b0: r0 = Text()
    //     0xa956b0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa956b4: mov             x2, x0
    // 0xa956b8: ldur            x0, [fp, #-0x18]
    // 0xa956bc: stur            x2, [fp, #-0x38]
    // 0xa956c0: StoreField: r2->field_b = r0
    //     0xa956c0: stur            w0, [x2, #0xb]
    // 0xa956c4: ldur            x0, [fp, #-0x28]
    // 0xa956c8: StoreField: r2->field_13 = r0
    //     0xa956c8: stur            w0, [x2, #0x13]
    // 0xa956cc: ldur            x0, [fp, #-8]
    // 0xa956d0: LoadField: r1 = r0->field_f
    //     0xa956d0: ldur            w1, [x0, #0xf]
    // 0xa956d4: DecompressPointer r1
    //     0xa956d4: add             x1, x1, HEAP, lsl #32
    // 0xa956d8: r0 = controller()
    //     0xa956d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa956dc: LoadField: r1 = r0->field_77
    //     0xa956dc: ldur            w1, [x0, #0x77]
    // 0xa956e0: DecompressPointer r1
    //     0xa956e0: add             x1, x1, HEAP, lsl #32
    // 0xa956e4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa956e4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa956e8: r0 = toList()
    //     0xa956e8: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa956ec: mov             x3, x0
    // 0xa956f0: ldur            x2, [fp, #-0x10]
    // 0xa956f4: LoadField: r0 = r2->field_13
    //     0xa956f4: ldur            w0, [x2, #0x13]
    // 0xa956f8: DecompressPointer r0
    //     0xa956f8: add             x0, x0, HEAP, lsl #32
    // 0xa956fc: LoadField: r1 = r3->field_b
    //     0xa956fc: ldur            w1, [x3, #0xb]
    // 0xa95700: r4 = LoadInt32Instr(r0)
    //     0xa95700: sbfx            x4, x0, #1, #0x1f
    //     0xa95704: tbz             w0, #0, #0xa9570c
    //     0xa95708: ldur            x4, [x0, #7]
    // 0xa9570c: r0 = LoadInt32Instr(r1)
    //     0xa9570c: sbfx            x0, x1, #1, #0x1f
    // 0xa95710: mov             x1, x4
    // 0xa95714: cmp             x1, x0
    // 0xa95718: b.hs            #0xa96708
    // 0xa9571c: LoadField: r0 = r3->field_f
    //     0xa9571c: ldur            w0, [x3, #0xf]
    // 0xa95720: DecompressPointer r0
    //     0xa95720: add             x0, x0, HEAP, lsl #32
    // 0xa95724: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa95724: add             x16, x0, x4, lsl #2
    //     0xa95728: ldur            w1, [x16, #0xf]
    // 0xa9572c: DecompressPointer r1
    //     0xa9572c: add             x1, x1, HEAP, lsl #32
    // 0xa95730: cmp             w1, NULL
    // 0xa95734: b.ne            #0xa95740
    // 0xa95738: r0 = Null
    //     0xa95738: mov             x0, NULL
    // 0xa9573c: b               #0xa95748
    // 0xa95740: LoadField: r0 = r1->field_1f
    //     0xa95740: ldur            w0, [x1, #0x1f]
    // 0xa95744: DecompressPointer r0
    //     0xa95744: add             x0, x0, HEAP, lsl #32
    // 0xa95748: cmp             w0, NULL
    // 0xa9574c: b.ne            #0xa95754
    // 0xa95750: r0 = ""
    //     0xa95750: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa95754: stur            x0, [fp, #-0x18]
    // 0xa95758: LoadField: r1 = r2->field_f
    //     0xa95758: ldur            w1, [x2, #0xf]
    // 0xa9575c: DecompressPointer r1
    //     0xa9575c: add             x1, x1, HEAP, lsl #32
    // 0xa95760: r0 = of()
    //     0xa95760: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa95764: LoadField: r1 = r0->field_87
    //     0xa95764: ldur            w1, [x0, #0x87]
    // 0xa95768: DecompressPointer r1
    //     0xa95768: add             x1, x1, HEAP, lsl #32
    // 0xa9576c: LoadField: r0 = r1->field_33
    //     0xa9576c: ldur            w0, [x1, #0x33]
    // 0xa95770: DecompressPointer r0
    //     0xa95770: add             x0, x0, HEAP, lsl #32
    // 0xa95774: stur            x0, [fp, #-0x28]
    // 0xa95778: cmp             w0, NULL
    // 0xa9577c: b.ne            #0xa95788
    // 0xa95780: r5 = Null
    //     0xa95780: mov             x5, NULL
    // 0xa95784: b               #0xa957b0
    // 0xa95788: r1 = Instance_Color
    //     0xa95788: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa9578c: d0 = 0.500000
    //     0xa9578c: fmov            d0, #0.50000000
    // 0xa95790: r0 = withOpacity()
    //     0xa95790: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa95794: r16 = 10.000000
    //     0xa95794: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xa95798: stp             x0, x16, [SP]
    // 0xa9579c: ldur            x1, [fp, #-0x28]
    // 0xa957a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa957a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa957a4: ldr             x4, [x4, #0xaa0]
    // 0xa957a8: r0 = copyWith()
    //     0xa957a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa957ac: mov             x5, x0
    // 0xa957b0: ldur            x3, [fp, #-8]
    // 0xa957b4: ldur            x2, [fp, #-0x10]
    // 0xa957b8: ldur            x4, [fp, #-0x30]
    // 0xa957bc: ldur            x1, [fp, #-0x38]
    // 0xa957c0: ldur            x0, [fp, #-0x18]
    // 0xa957c4: stur            x5, [fp, #-0x28]
    // 0xa957c8: r0 = Text()
    //     0xa957c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa957cc: mov             x1, x0
    // 0xa957d0: ldur            x0, [fp, #-0x18]
    // 0xa957d4: stur            x1, [fp, #-0x40]
    // 0xa957d8: StoreField: r1->field_b = r0
    //     0xa957d8: stur            w0, [x1, #0xb]
    // 0xa957dc: ldur            x0, [fp, #-0x28]
    // 0xa957e0: StoreField: r1->field_13 = r0
    //     0xa957e0: stur            w0, [x1, #0x13]
    // 0xa957e4: r0 = Padding()
    //     0xa957e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa957e8: mov             x3, x0
    // 0xa957ec: r0 = Instance_EdgeInsets
    //     0xa957ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xa957f0: ldr             x0, [x0, #0x990]
    // 0xa957f4: stur            x3, [fp, #-0x18]
    // 0xa957f8: StoreField: r3->field_f = r0
    //     0xa957f8: stur            w0, [x3, #0xf]
    // 0xa957fc: ldur            x0, [fp, #-0x40]
    // 0xa95800: StoreField: r3->field_b = r0
    //     0xa95800: stur            w0, [x3, #0xb]
    // 0xa95804: r1 = Null
    //     0xa95804: mov             x1, NULL
    // 0xa95808: r2 = 4
    //     0xa95808: movz            x2, #0x4
    // 0xa9580c: r0 = AllocateArray()
    //     0xa9580c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa95810: mov             x2, x0
    // 0xa95814: ldur            x0, [fp, #-0x38]
    // 0xa95818: stur            x2, [fp, #-0x28]
    // 0xa9581c: StoreField: r2->field_f = r0
    //     0xa9581c: stur            w0, [x2, #0xf]
    // 0xa95820: ldur            x0, [fp, #-0x18]
    // 0xa95824: StoreField: r2->field_13 = r0
    //     0xa95824: stur            w0, [x2, #0x13]
    // 0xa95828: r1 = <Widget>
    //     0xa95828: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa9582c: r0 = AllocateGrowableArray()
    //     0xa9582c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa95830: mov             x1, x0
    // 0xa95834: ldur            x0, [fp, #-0x28]
    // 0xa95838: stur            x1, [fp, #-0x18]
    // 0xa9583c: StoreField: r1->field_f = r0
    //     0xa9583c: stur            w0, [x1, #0xf]
    // 0xa95840: r2 = 4
    //     0xa95840: movz            x2, #0x4
    // 0xa95844: StoreField: r1->field_b = r2
    //     0xa95844: stur            w2, [x1, #0xb]
    // 0xa95848: r0 = Column()
    //     0xa95848: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa9584c: mov             x3, x0
    // 0xa95850: r0 = Instance_Axis
    //     0xa95850: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa95854: stur            x3, [fp, #-0x28]
    // 0xa95858: StoreField: r3->field_f = r0
    //     0xa95858: stur            w0, [x3, #0xf]
    // 0xa9585c: r4 = Instance_MainAxisAlignment
    //     0xa9585c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa95860: ldr             x4, [x4, #0xa08]
    // 0xa95864: StoreField: r3->field_13 = r4
    //     0xa95864: stur            w4, [x3, #0x13]
    // 0xa95868: r5 = Instance_MainAxisSize
    //     0xa95868: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa9586c: ldr             x5, [x5, #0xa10]
    // 0xa95870: ArrayStore: r3[0] = r5  ; List_4
    //     0xa95870: stur            w5, [x3, #0x17]
    // 0xa95874: r6 = Instance_CrossAxisAlignment
    //     0xa95874: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa95878: ldr             x6, [x6, #0x890]
    // 0xa9587c: StoreField: r3->field_1b = r6
    //     0xa9587c: stur            w6, [x3, #0x1b]
    // 0xa95880: r7 = Instance_VerticalDirection
    //     0xa95880: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa95884: ldr             x7, [x7, #0xa20]
    // 0xa95888: StoreField: r3->field_23 = r7
    //     0xa95888: stur            w7, [x3, #0x23]
    // 0xa9588c: r8 = Instance_Clip
    //     0xa9588c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa95890: ldr             x8, [x8, #0x38]
    // 0xa95894: StoreField: r3->field_2b = r8
    //     0xa95894: stur            w8, [x3, #0x2b]
    // 0xa95898: StoreField: r3->field_2f = rZR
    //     0xa95898: stur            xzr, [x3, #0x2f]
    // 0xa9589c: ldur            x1, [fp, #-0x18]
    // 0xa958a0: StoreField: r3->field_b = r1
    //     0xa958a0: stur            w1, [x3, #0xb]
    // 0xa958a4: r1 = Null
    //     0xa958a4: mov             x1, NULL
    // 0xa958a8: r2 = 6
    //     0xa958a8: movz            x2, #0x6
    // 0xa958ac: r0 = AllocateArray()
    //     0xa958ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa958b0: mov             x2, x0
    // 0xa958b4: ldur            x0, [fp, #-0x30]
    // 0xa958b8: stur            x2, [fp, #-0x18]
    // 0xa958bc: StoreField: r2->field_f = r0
    //     0xa958bc: stur            w0, [x2, #0xf]
    // 0xa958c0: r16 = Instance_SizedBox
    //     0xa958c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xa958c4: ldr             x16, [x16, #0x998]
    // 0xa958c8: StoreField: r2->field_13 = r16
    //     0xa958c8: stur            w16, [x2, #0x13]
    // 0xa958cc: ldur            x0, [fp, #-0x28]
    // 0xa958d0: ArrayStore: r2[0] = r0  ; List_4
    //     0xa958d0: stur            w0, [x2, #0x17]
    // 0xa958d4: r1 = <Widget>
    //     0xa958d4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa958d8: r0 = AllocateGrowableArray()
    //     0xa958d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa958dc: mov             x1, x0
    // 0xa958e0: ldur            x0, [fp, #-0x18]
    // 0xa958e4: stur            x1, [fp, #-0x28]
    // 0xa958e8: StoreField: r1->field_f = r0
    //     0xa958e8: stur            w0, [x1, #0xf]
    // 0xa958ec: r2 = 6
    //     0xa958ec: movz            x2, #0x6
    // 0xa958f0: StoreField: r1->field_b = r2
    //     0xa958f0: stur            w2, [x1, #0xb]
    // 0xa958f4: r0 = Row()
    //     0xa958f4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa958f8: mov             x2, x0
    // 0xa958fc: r0 = Instance_Axis
    //     0xa958fc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa95900: stur            x2, [fp, #-0x18]
    // 0xa95904: StoreField: r2->field_f = r0
    //     0xa95904: stur            w0, [x2, #0xf]
    // 0xa95908: r3 = Instance_MainAxisAlignment
    //     0xa95908: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa9590c: ldr             x3, [x3, #0xa08]
    // 0xa95910: StoreField: r2->field_13 = r3
    //     0xa95910: stur            w3, [x2, #0x13]
    // 0xa95914: r4 = Instance_MainAxisSize
    //     0xa95914: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa95918: ldr             x4, [x4, #0xa10]
    // 0xa9591c: ArrayStore: r2[0] = r4  ; List_4
    //     0xa9591c: stur            w4, [x2, #0x17]
    // 0xa95920: r5 = Instance_CrossAxisAlignment
    //     0xa95920: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa95924: ldr             x5, [x5, #0xa18]
    // 0xa95928: StoreField: r2->field_1b = r5
    //     0xa95928: stur            w5, [x2, #0x1b]
    // 0xa9592c: r6 = Instance_VerticalDirection
    //     0xa9592c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa95930: ldr             x6, [x6, #0xa20]
    // 0xa95934: StoreField: r2->field_23 = r6
    //     0xa95934: stur            w6, [x2, #0x23]
    // 0xa95938: r7 = Instance_Clip
    //     0xa95938: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa9593c: ldr             x7, [x7, #0x38]
    // 0xa95940: StoreField: r2->field_2b = r7
    //     0xa95940: stur            w7, [x2, #0x2b]
    // 0xa95944: StoreField: r2->field_2f = rZR
    //     0xa95944: stur            xzr, [x2, #0x2f]
    // 0xa95948: ldur            x1, [fp, #-0x28]
    // 0xa9594c: StoreField: r2->field_b = r1
    //     0xa9594c: stur            w1, [x2, #0xb]
    // 0xa95950: ldur            x8, [fp, #-8]
    // 0xa95954: LoadField: r1 = r8->field_f
    //     0xa95954: ldur            w1, [x8, #0xf]
    // 0xa95958: DecompressPointer r1
    //     0xa95958: add             x1, x1, HEAP, lsl #32
    // 0xa9595c: r0 = controller()
    //     0xa9595c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa95960: LoadField: r1 = r0->field_77
    //     0xa95960: ldur            w1, [x0, #0x77]
    // 0xa95964: DecompressPointer r1
    //     0xa95964: add             x1, x1, HEAP, lsl #32
    // 0xa95968: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa95968: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa9596c: r0 = toList()
    //     0xa9596c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa95970: mov             x3, x0
    // 0xa95974: ldur            x2, [fp, #-0x10]
    // 0xa95978: LoadField: r0 = r2->field_13
    //     0xa95978: ldur            w0, [x2, #0x13]
    // 0xa9597c: DecompressPointer r0
    //     0xa9597c: add             x0, x0, HEAP, lsl #32
    // 0xa95980: LoadField: r1 = r3->field_b
    //     0xa95980: ldur            w1, [x3, #0xb]
    // 0xa95984: r4 = LoadInt32Instr(r0)
    //     0xa95984: sbfx            x4, x0, #1, #0x1f
    //     0xa95988: tbz             w0, #0, #0xa95990
    //     0xa9598c: ldur            x4, [x0, #7]
    // 0xa95990: r0 = LoadInt32Instr(r1)
    //     0xa95990: sbfx            x0, x1, #1, #0x1f
    // 0xa95994: mov             x1, x4
    // 0xa95998: cmp             x1, x0
    // 0xa9599c: b.hs            #0xa9670c
    // 0xa959a0: LoadField: r0 = r3->field_f
    //     0xa959a0: ldur            w0, [x3, #0xf]
    // 0xa959a4: DecompressPointer r0
    //     0xa959a4: add             x0, x0, HEAP, lsl #32
    // 0xa959a8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa959a8: add             x16, x0, x4, lsl #2
    //     0xa959ac: ldur            w1, [x16, #0xf]
    // 0xa959b0: DecompressPointer r1
    //     0xa959b0: add             x1, x1, HEAP, lsl #32
    // 0xa959b4: cmp             w1, NULL
    // 0xa959b8: b.eq            #0xa959cc
    // 0xa959bc: LoadField: r0 = r1->field_f
    //     0xa959bc: ldur            w0, [x1, #0xf]
    // 0xa959c0: DecompressPointer r0
    //     0xa959c0: add             x0, x0, HEAP, lsl #32
    // 0xa959c4: cmp             w0, #0xa
    // 0xa959c8: b.eq            #0xa95a48
    // 0xa959cc: ldur            x0, [fp, #-8]
    // 0xa959d0: LoadField: r1 = r0->field_f
    //     0xa959d0: ldur            w1, [x0, #0xf]
    // 0xa959d4: DecompressPointer r1
    //     0xa959d4: add             x1, x1, HEAP, lsl #32
    // 0xa959d8: r0 = controller()
    //     0xa959d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa959dc: LoadField: r1 = r0->field_77
    //     0xa959dc: ldur            w1, [x0, #0x77]
    // 0xa959e0: DecompressPointer r1
    //     0xa959e0: add             x1, x1, HEAP, lsl #32
    // 0xa959e4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa959e4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa959e8: r0 = toList()
    //     0xa959e8: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa959ec: mov             x3, x0
    // 0xa959f0: ldur            x2, [fp, #-0x10]
    // 0xa959f4: LoadField: r0 = r2->field_13
    //     0xa959f4: ldur            w0, [x2, #0x13]
    // 0xa959f8: DecompressPointer r0
    //     0xa959f8: add             x0, x0, HEAP, lsl #32
    // 0xa959fc: LoadField: r1 = r3->field_b
    //     0xa959fc: ldur            w1, [x3, #0xb]
    // 0xa95a00: r4 = LoadInt32Instr(r0)
    //     0xa95a00: sbfx            x4, x0, #1, #0x1f
    //     0xa95a04: tbz             w0, #0, #0xa95a0c
    //     0xa95a08: ldur            x4, [x0, #7]
    // 0xa95a0c: r0 = LoadInt32Instr(r1)
    //     0xa95a0c: sbfx            x0, x1, #1, #0x1f
    // 0xa95a10: mov             x1, x4
    // 0xa95a14: cmp             x1, x0
    // 0xa95a18: b.hs            #0xa96710
    // 0xa95a1c: LoadField: r0 = r3->field_f
    //     0xa95a1c: ldur            w0, [x3, #0xf]
    // 0xa95a20: DecompressPointer r0
    //     0xa95a20: add             x0, x0, HEAP, lsl #32
    // 0xa95a24: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa95a24: add             x16, x0, x4, lsl #2
    //     0xa95a28: ldur            w1, [x16, #0xf]
    // 0xa95a2c: DecompressPointer r1
    //     0xa95a2c: add             x1, x1, HEAP, lsl #32
    // 0xa95a30: cmp             w1, NULL
    // 0xa95a34: b.eq            #0xa95a54
    // 0xa95a38: LoadField: r0 = r1->field_f
    //     0xa95a38: ldur            w0, [x1, #0xf]
    // 0xa95a3c: DecompressPointer r0
    //     0xa95a3c: add             x0, x0, HEAP, lsl #32
    // 0xa95a40: cmp             w0, #8
    // 0xa95a44: b.ne            #0xa95a54
    // 0xa95a48: r1 = Instance_Color
    //     0xa95a48: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa95a4c: ldr             x1, [x1, #0x858]
    // 0xa95a50: b               #0xa95b84
    // 0xa95a54: ldur            x0, [fp, #-8]
    // 0xa95a58: LoadField: r1 = r0->field_f
    //     0xa95a58: ldur            w1, [x0, #0xf]
    // 0xa95a5c: DecompressPointer r1
    //     0xa95a5c: add             x1, x1, HEAP, lsl #32
    // 0xa95a60: r0 = controller()
    //     0xa95a60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa95a64: LoadField: r1 = r0->field_77
    //     0xa95a64: ldur            w1, [x0, #0x77]
    // 0xa95a68: DecompressPointer r1
    //     0xa95a68: add             x1, x1, HEAP, lsl #32
    // 0xa95a6c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa95a6c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa95a70: r0 = toList()
    //     0xa95a70: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa95a74: mov             x3, x0
    // 0xa95a78: ldur            x2, [fp, #-0x10]
    // 0xa95a7c: LoadField: r0 = r2->field_13
    //     0xa95a7c: ldur            w0, [x2, #0x13]
    // 0xa95a80: DecompressPointer r0
    //     0xa95a80: add             x0, x0, HEAP, lsl #32
    // 0xa95a84: LoadField: r1 = r3->field_b
    //     0xa95a84: ldur            w1, [x3, #0xb]
    // 0xa95a88: r4 = LoadInt32Instr(r0)
    //     0xa95a88: sbfx            x4, x0, #1, #0x1f
    //     0xa95a8c: tbz             w0, #0, #0xa95a94
    //     0xa95a90: ldur            x4, [x0, #7]
    // 0xa95a94: r0 = LoadInt32Instr(r1)
    //     0xa95a94: sbfx            x0, x1, #1, #0x1f
    // 0xa95a98: mov             x1, x4
    // 0xa95a9c: cmp             x1, x0
    // 0xa95aa0: b.hs            #0xa96714
    // 0xa95aa4: LoadField: r0 = r3->field_f
    //     0xa95aa4: ldur            w0, [x3, #0xf]
    // 0xa95aa8: DecompressPointer r0
    //     0xa95aa8: add             x0, x0, HEAP, lsl #32
    // 0xa95aac: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa95aac: add             x16, x0, x4, lsl #2
    //     0xa95ab0: ldur            w1, [x16, #0xf]
    // 0xa95ab4: DecompressPointer r1
    //     0xa95ab4: add             x1, x1, HEAP, lsl #32
    // 0xa95ab8: cmp             w1, NULL
    // 0xa95abc: b.eq            #0xa95af0
    // 0xa95ac0: LoadField: r0 = r1->field_f
    //     0xa95ac0: ldur            w0, [x1, #0xf]
    // 0xa95ac4: DecompressPointer r0
    //     0xa95ac4: add             x0, x0, HEAP, lsl #32
    // 0xa95ac8: cmp             w0, #6
    // 0xa95acc: b.ne            #0xa95aec
    // 0xa95ad0: r1 = Instance_Color
    //     0xa95ad0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa95ad4: ldr             x1, [x1, #0x858]
    // 0xa95ad8: d0 = 0.700000
    //     0xa95ad8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa95adc: ldr             d0, [x17, #0xf48]
    // 0xa95ae0: r0 = withOpacity()
    //     0xa95ae0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa95ae4: ldur            x2, [fp, #-0x10]
    // 0xa95ae8: b               #0xa95b80
    // 0xa95aec: ldur            x2, [fp, #-0x10]
    // 0xa95af0: ldur            x0, [fp, #-8]
    // 0xa95af4: LoadField: r1 = r0->field_f
    //     0xa95af4: ldur            w1, [x0, #0xf]
    // 0xa95af8: DecompressPointer r1
    //     0xa95af8: add             x1, x1, HEAP, lsl #32
    // 0xa95afc: r0 = controller()
    //     0xa95afc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa95b00: LoadField: r1 = r0->field_77
    //     0xa95b00: ldur            w1, [x0, #0x77]
    // 0xa95b04: DecompressPointer r1
    //     0xa95b04: add             x1, x1, HEAP, lsl #32
    // 0xa95b08: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa95b08: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa95b0c: r0 = toList()
    //     0xa95b0c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa95b10: mov             x3, x0
    // 0xa95b14: ldur            x2, [fp, #-0x10]
    // 0xa95b18: LoadField: r0 = r2->field_13
    //     0xa95b18: ldur            w0, [x2, #0x13]
    // 0xa95b1c: DecompressPointer r0
    //     0xa95b1c: add             x0, x0, HEAP, lsl #32
    // 0xa95b20: LoadField: r1 = r3->field_b
    //     0xa95b20: ldur            w1, [x3, #0xb]
    // 0xa95b24: r4 = LoadInt32Instr(r0)
    //     0xa95b24: sbfx            x4, x0, #1, #0x1f
    //     0xa95b28: tbz             w0, #0, #0xa95b30
    //     0xa95b2c: ldur            x4, [x0, #7]
    // 0xa95b30: r0 = LoadInt32Instr(r1)
    //     0xa95b30: sbfx            x0, x1, #1, #0x1f
    // 0xa95b34: mov             x1, x4
    // 0xa95b38: cmp             x1, x0
    // 0xa95b3c: b.hs            #0xa96718
    // 0xa95b40: LoadField: r0 = r3->field_f
    //     0xa95b40: ldur            w0, [x3, #0xf]
    // 0xa95b44: DecompressPointer r0
    //     0xa95b44: add             x0, x0, HEAP, lsl #32
    // 0xa95b48: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa95b48: add             x16, x0, x4, lsl #2
    //     0xa95b4c: ldur            w1, [x16, #0xf]
    // 0xa95b50: DecompressPointer r1
    //     0xa95b50: add             x1, x1, HEAP, lsl #32
    // 0xa95b54: cmp             w1, NULL
    // 0xa95b58: b.eq            #0xa95b78
    // 0xa95b5c: LoadField: r0 = r1->field_f
    //     0xa95b5c: ldur            w0, [x1, #0xf]
    // 0xa95b60: DecompressPointer r0
    //     0xa95b60: add             x0, x0, HEAP, lsl #32
    // 0xa95b64: cmp             w0, #4
    // 0xa95b68: b.ne            #0xa95b78
    // 0xa95b6c: r0 = Instance_Color
    //     0xa95b6c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xa95b70: ldr             x0, [x0, #0x860]
    // 0xa95b74: b               #0xa95b80
    // 0xa95b78: r0 = Instance_Color
    //     0xa95b78: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xa95b7c: ldr             x0, [x0, #0x50]
    // 0xa95b80: mov             x1, x0
    // 0xa95b84: ldur            x0, [fp, #-8]
    // 0xa95b88: stur            x1, [fp, #-0x28]
    // 0xa95b8c: r0 = ColorFilter()
    //     0xa95b8c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xa95b90: mov             x1, x0
    // 0xa95b94: ldur            x0, [fp, #-0x28]
    // 0xa95b98: stur            x1, [fp, #-0x30]
    // 0xa95b9c: StoreField: r1->field_7 = r0
    //     0xa95b9c: stur            w0, [x1, #7]
    // 0xa95ba0: r0 = Instance_BlendMode
    //     0xa95ba0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa95ba4: ldr             x0, [x0, #0xb30]
    // 0xa95ba8: StoreField: r1->field_b = r0
    //     0xa95ba8: stur            w0, [x1, #0xb]
    // 0xa95bac: r0 = 1
    //     0xa95bac: movz            x0, #0x1
    // 0xa95bb0: StoreField: r1->field_13 = r0
    //     0xa95bb0: stur            x0, [x1, #0x13]
    // 0xa95bb4: r0 = SvgPicture()
    //     0xa95bb4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa95bb8: stur            x0, [fp, #-0x28]
    // 0xa95bbc: ldur            x16, [fp, #-0x30]
    // 0xa95bc0: str             x16, [SP]
    // 0xa95bc4: mov             x1, x0
    // 0xa95bc8: r2 = "assets/images/green_star.svg"
    //     0xa95bc8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xa95bcc: ldr             x2, [x2, #0x9a0]
    // 0xa95bd0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xa95bd0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xa95bd4: ldr             x4, [x4, #0xa38]
    // 0xa95bd8: r0 = SvgPicture.asset()
    //     0xa95bd8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa95bdc: ldur            x0, [fp, #-8]
    // 0xa95be0: LoadField: r1 = r0->field_f
    //     0xa95be0: ldur            w1, [x0, #0xf]
    // 0xa95be4: DecompressPointer r1
    //     0xa95be4: add             x1, x1, HEAP, lsl #32
    // 0xa95be8: r0 = controller()
    //     0xa95be8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa95bec: LoadField: r1 = r0->field_77
    //     0xa95bec: ldur            w1, [x0, #0x77]
    // 0xa95bf0: DecompressPointer r1
    //     0xa95bf0: add             x1, x1, HEAP, lsl #32
    // 0xa95bf4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa95bf4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa95bf8: r0 = toList()
    //     0xa95bf8: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa95bfc: mov             x3, x0
    // 0xa95c00: ldur            x2, [fp, #-0x10]
    // 0xa95c04: LoadField: r0 = r2->field_13
    //     0xa95c04: ldur            w0, [x2, #0x13]
    // 0xa95c08: DecompressPointer r0
    //     0xa95c08: add             x0, x0, HEAP, lsl #32
    // 0xa95c0c: LoadField: r1 = r3->field_b
    //     0xa95c0c: ldur            w1, [x3, #0xb]
    // 0xa95c10: r4 = LoadInt32Instr(r0)
    //     0xa95c10: sbfx            x4, x0, #1, #0x1f
    //     0xa95c14: tbz             w0, #0, #0xa95c1c
    //     0xa95c18: ldur            x4, [x0, #7]
    // 0xa95c1c: r0 = LoadInt32Instr(r1)
    //     0xa95c1c: sbfx            x0, x1, #1, #0x1f
    // 0xa95c20: mov             x1, x4
    // 0xa95c24: cmp             x1, x0
    // 0xa95c28: b.hs            #0xa9671c
    // 0xa95c2c: LoadField: r0 = r3->field_f
    //     0xa95c2c: ldur            w0, [x3, #0xf]
    // 0xa95c30: DecompressPointer r0
    //     0xa95c30: add             x0, x0, HEAP, lsl #32
    // 0xa95c34: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa95c34: add             x16, x0, x4, lsl #2
    //     0xa95c38: ldur            w1, [x16, #0xf]
    // 0xa95c3c: DecompressPointer r1
    //     0xa95c3c: add             x1, x1, HEAP, lsl #32
    // 0xa95c40: cmp             w1, NULL
    // 0xa95c44: b.ne            #0xa95c50
    // 0xa95c48: r0 = Null
    //     0xa95c48: mov             x0, NULL
    // 0xa95c4c: b               #0xa95c84
    // 0xa95c50: LoadField: r0 = r1->field_f
    //     0xa95c50: ldur            w0, [x1, #0xf]
    // 0xa95c54: DecompressPointer r0
    //     0xa95c54: add             x0, x0, HEAP, lsl #32
    // 0xa95c58: r1 = 60
    //     0xa95c58: movz            x1, #0x3c
    // 0xa95c5c: branchIfSmi(r0, 0xa95c68)
    //     0xa95c5c: tbz             w0, #0, #0xa95c68
    // 0xa95c60: r1 = LoadClassIdInstr(r0)
    //     0xa95c60: ldur            x1, [x0, #-1]
    //     0xa95c64: ubfx            x1, x1, #0xc, #0x14
    // 0xa95c68: str             x0, [SP]
    // 0xa95c6c: mov             x0, x1
    // 0xa95c70: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xa95c70: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xa95c74: r0 = GDT[cid_x0 + 0x2700]()
    //     0xa95c74: movz            x17, #0x2700
    //     0xa95c78: add             lr, x0, x17
    //     0xa95c7c: ldr             lr, [x21, lr, lsl #3]
    //     0xa95c80: blr             lr
    // 0xa95c84: cmp             w0, NULL
    // 0xa95c88: b.ne            #0xa95c94
    // 0xa95c8c: r5 = ""
    //     0xa95c8c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa95c90: b               #0xa95c98
    // 0xa95c94: mov             x5, x0
    // 0xa95c98: ldur            x0, [fp, #-8]
    // 0xa95c9c: ldur            x2, [fp, #-0x10]
    // 0xa95ca0: ldur            x4, [fp, #-0x18]
    // 0xa95ca4: ldur            x3, [fp, #-0x28]
    // 0xa95ca8: stur            x5, [fp, #-0x30]
    // 0xa95cac: LoadField: r1 = r2->field_f
    //     0xa95cac: ldur            w1, [x2, #0xf]
    // 0xa95cb0: DecompressPointer r1
    //     0xa95cb0: add             x1, x1, HEAP, lsl #32
    // 0xa95cb4: r0 = of()
    //     0xa95cb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa95cb8: LoadField: r1 = r0->field_87
    //     0xa95cb8: ldur            w1, [x0, #0x87]
    // 0xa95cbc: DecompressPointer r1
    //     0xa95cbc: add             x1, x1, HEAP, lsl #32
    // 0xa95cc0: LoadField: r0 = r1->field_7
    //     0xa95cc0: ldur            w0, [x1, #7]
    // 0xa95cc4: DecompressPointer r0
    //     0xa95cc4: add             x0, x0, HEAP, lsl #32
    // 0xa95cc8: r16 = 12.000000
    //     0xa95cc8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa95ccc: ldr             x16, [x16, #0x9e8]
    // 0xa95cd0: r30 = Instance_Color
    //     0xa95cd0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa95cd4: stp             lr, x16, [SP]
    // 0xa95cd8: mov             x1, x0
    // 0xa95cdc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa95cdc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa95ce0: ldr             x4, [x4, #0xaa0]
    // 0xa95ce4: r0 = copyWith()
    //     0xa95ce4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa95ce8: stur            x0, [fp, #-0x38]
    // 0xa95cec: r0 = Text()
    //     0xa95cec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa95cf0: mov             x3, x0
    // 0xa95cf4: ldur            x0, [fp, #-0x30]
    // 0xa95cf8: stur            x3, [fp, #-0x40]
    // 0xa95cfc: StoreField: r3->field_b = r0
    //     0xa95cfc: stur            w0, [x3, #0xb]
    // 0xa95d00: ldur            x0, [fp, #-0x38]
    // 0xa95d04: StoreField: r3->field_13 = r0
    //     0xa95d04: stur            w0, [x3, #0x13]
    // 0xa95d08: r1 = Null
    //     0xa95d08: mov             x1, NULL
    // 0xa95d0c: r2 = 6
    //     0xa95d0c: movz            x2, #0x6
    // 0xa95d10: r0 = AllocateArray()
    //     0xa95d10: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa95d14: mov             x2, x0
    // 0xa95d18: ldur            x0, [fp, #-0x28]
    // 0xa95d1c: stur            x2, [fp, #-0x30]
    // 0xa95d20: StoreField: r2->field_f = r0
    //     0xa95d20: stur            w0, [x2, #0xf]
    // 0xa95d24: r16 = Instance_SizedBox
    //     0xa95d24: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9a8] Obj!SizedBox@d67ea1
    //     0xa95d28: ldr             x16, [x16, #0x9a8]
    // 0xa95d2c: StoreField: r2->field_13 = r16
    //     0xa95d2c: stur            w16, [x2, #0x13]
    // 0xa95d30: ldur            x0, [fp, #-0x40]
    // 0xa95d34: ArrayStore: r2[0] = r0  ; List_4
    //     0xa95d34: stur            w0, [x2, #0x17]
    // 0xa95d38: r1 = <Widget>
    //     0xa95d38: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa95d3c: r0 = AllocateGrowableArray()
    //     0xa95d3c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa95d40: mov             x1, x0
    // 0xa95d44: ldur            x0, [fp, #-0x30]
    // 0xa95d48: stur            x1, [fp, #-0x28]
    // 0xa95d4c: StoreField: r1->field_f = r0
    //     0xa95d4c: stur            w0, [x1, #0xf]
    // 0xa95d50: r0 = 6
    //     0xa95d50: movz            x0, #0x6
    // 0xa95d54: StoreField: r1->field_b = r0
    //     0xa95d54: stur            w0, [x1, #0xb]
    // 0xa95d58: r0 = Row()
    //     0xa95d58: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa95d5c: mov             x3, x0
    // 0xa95d60: r0 = Instance_Axis
    //     0xa95d60: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa95d64: stur            x3, [fp, #-0x30]
    // 0xa95d68: StoreField: r3->field_f = r0
    //     0xa95d68: stur            w0, [x3, #0xf]
    // 0xa95d6c: r4 = Instance_MainAxisAlignment
    //     0xa95d6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa95d70: ldr             x4, [x4, #0xa08]
    // 0xa95d74: StoreField: r3->field_13 = r4
    //     0xa95d74: stur            w4, [x3, #0x13]
    // 0xa95d78: r5 = Instance_MainAxisSize
    //     0xa95d78: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa95d7c: ldr             x5, [x5, #0xa10]
    // 0xa95d80: ArrayStore: r3[0] = r5  ; List_4
    //     0xa95d80: stur            w5, [x3, #0x17]
    // 0xa95d84: r6 = Instance_CrossAxisAlignment
    //     0xa95d84: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa95d88: ldr             x6, [x6, #0xa18]
    // 0xa95d8c: StoreField: r3->field_1b = r6
    //     0xa95d8c: stur            w6, [x3, #0x1b]
    // 0xa95d90: r7 = Instance_VerticalDirection
    //     0xa95d90: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa95d94: ldr             x7, [x7, #0xa20]
    // 0xa95d98: StoreField: r3->field_23 = r7
    //     0xa95d98: stur            w7, [x3, #0x23]
    // 0xa95d9c: r8 = Instance_Clip
    //     0xa95d9c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa95da0: ldr             x8, [x8, #0x38]
    // 0xa95da4: StoreField: r3->field_2b = r8
    //     0xa95da4: stur            w8, [x3, #0x2b]
    // 0xa95da8: StoreField: r3->field_2f = rZR
    //     0xa95da8: stur            xzr, [x3, #0x2f]
    // 0xa95dac: ldur            x1, [fp, #-0x28]
    // 0xa95db0: StoreField: r3->field_b = r1
    //     0xa95db0: stur            w1, [x3, #0xb]
    // 0xa95db4: r1 = Null
    //     0xa95db4: mov             x1, NULL
    // 0xa95db8: r2 = 4
    //     0xa95db8: movz            x2, #0x4
    // 0xa95dbc: r0 = AllocateArray()
    //     0xa95dbc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa95dc0: mov             x2, x0
    // 0xa95dc4: ldur            x0, [fp, #-0x18]
    // 0xa95dc8: stur            x2, [fp, #-0x28]
    // 0xa95dcc: StoreField: r2->field_f = r0
    //     0xa95dcc: stur            w0, [x2, #0xf]
    // 0xa95dd0: ldur            x0, [fp, #-0x30]
    // 0xa95dd4: StoreField: r2->field_13 = r0
    //     0xa95dd4: stur            w0, [x2, #0x13]
    // 0xa95dd8: r1 = <Widget>
    //     0xa95dd8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa95ddc: r0 = AllocateGrowableArray()
    //     0xa95ddc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa95de0: mov             x1, x0
    // 0xa95de4: ldur            x0, [fp, #-0x28]
    // 0xa95de8: stur            x1, [fp, #-0x18]
    // 0xa95dec: StoreField: r1->field_f = r0
    //     0xa95dec: stur            w0, [x1, #0xf]
    // 0xa95df0: r2 = 4
    //     0xa95df0: movz            x2, #0x4
    // 0xa95df4: StoreField: r1->field_b = r2
    //     0xa95df4: stur            w2, [x1, #0xb]
    // 0xa95df8: r0 = Row()
    //     0xa95df8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa95dfc: mov             x3, x0
    // 0xa95e00: r0 = Instance_Axis
    //     0xa95e00: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa95e04: stur            x3, [fp, #-0x28]
    // 0xa95e08: StoreField: r3->field_f = r0
    //     0xa95e08: stur            w0, [x3, #0xf]
    // 0xa95e0c: r0 = Instance_MainAxisAlignment
    //     0xa95e0c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xa95e10: ldr             x0, [x0, #0xa8]
    // 0xa95e14: StoreField: r3->field_13 = r0
    //     0xa95e14: stur            w0, [x3, #0x13]
    // 0xa95e18: r0 = Instance_MainAxisSize
    //     0xa95e18: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa95e1c: ldr             x0, [x0, #0xa10]
    // 0xa95e20: ArrayStore: r3[0] = r0  ; List_4
    //     0xa95e20: stur            w0, [x3, #0x17]
    // 0xa95e24: r1 = Instance_CrossAxisAlignment
    //     0xa95e24: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa95e28: ldr             x1, [x1, #0xa18]
    // 0xa95e2c: StoreField: r3->field_1b = r1
    //     0xa95e2c: stur            w1, [x3, #0x1b]
    // 0xa95e30: r4 = Instance_VerticalDirection
    //     0xa95e30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa95e34: ldr             x4, [x4, #0xa20]
    // 0xa95e38: StoreField: r3->field_23 = r4
    //     0xa95e38: stur            w4, [x3, #0x23]
    // 0xa95e3c: r5 = Instance_Clip
    //     0xa95e3c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa95e40: ldr             x5, [x5, #0x38]
    // 0xa95e44: StoreField: r3->field_2b = r5
    //     0xa95e44: stur            w5, [x3, #0x2b]
    // 0xa95e48: StoreField: r3->field_2f = rZR
    //     0xa95e48: stur            xzr, [x3, #0x2f]
    // 0xa95e4c: ldur            x1, [fp, #-0x18]
    // 0xa95e50: StoreField: r3->field_b = r1
    //     0xa95e50: stur            w1, [x3, #0xb]
    // 0xa95e54: r1 = Null
    //     0xa95e54: mov             x1, NULL
    // 0xa95e58: r2 = 2
    //     0xa95e58: movz            x2, #0x2
    // 0xa95e5c: r0 = AllocateArray()
    //     0xa95e5c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa95e60: mov             x2, x0
    // 0xa95e64: ldur            x0, [fp, #-0x28]
    // 0xa95e68: stur            x2, [fp, #-0x18]
    // 0xa95e6c: StoreField: r2->field_f = r0
    //     0xa95e6c: stur            w0, [x2, #0xf]
    // 0xa95e70: r1 = <Widget>
    //     0xa95e70: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa95e74: r0 = AllocateGrowableArray()
    //     0xa95e74: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa95e78: mov             x2, x0
    // 0xa95e7c: ldur            x0, [fp, #-0x18]
    // 0xa95e80: stur            x2, [fp, #-0x28]
    // 0xa95e84: StoreField: r2->field_f = r0
    //     0xa95e84: stur            w0, [x2, #0xf]
    // 0xa95e88: r0 = 2
    //     0xa95e88: movz            x0, #0x2
    // 0xa95e8c: StoreField: r2->field_b = r0
    //     0xa95e8c: stur            w0, [x2, #0xb]
    // 0xa95e90: ldur            x0, [fp, #-8]
    // 0xa95e94: LoadField: r1 = r0->field_f
    //     0xa95e94: ldur            w1, [x0, #0xf]
    // 0xa95e98: DecompressPointer r1
    //     0xa95e98: add             x1, x1, HEAP, lsl #32
    // 0xa95e9c: r0 = controller()
    //     0xa95e9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa95ea0: LoadField: r1 = r0->field_77
    //     0xa95ea0: ldur            w1, [x0, #0x77]
    // 0xa95ea4: DecompressPointer r1
    //     0xa95ea4: add             x1, x1, HEAP, lsl #32
    // 0xa95ea8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa95ea8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa95eac: r0 = toList()
    //     0xa95eac: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa95eb0: mov             x3, x0
    // 0xa95eb4: ldur            x2, [fp, #-0x10]
    // 0xa95eb8: LoadField: r0 = r2->field_13
    //     0xa95eb8: ldur            w0, [x2, #0x13]
    // 0xa95ebc: DecompressPointer r0
    //     0xa95ebc: add             x0, x0, HEAP, lsl #32
    // 0xa95ec0: LoadField: r1 = r3->field_b
    //     0xa95ec0: ldur            w1, [x3, #0xb]
    // 0xa95ec4: r4 = LoadInt32Instr(r0)
    //     0xa95ec4: sbfx            x4, x0, #1, #0x1f
    //     0xa95ec8: tbz             w0, #0, #0xa95ed0
    //     0xa95ecc: ldur            x4, [x0, #7]
    // 0xa95ed0: r0 = LoadInt32Instr(r1)
    //     0xa95ed0: sbfx            x0, x1, #1, #0x1f
    // 0xa95ed4: mov             x1, x4
    // 0xa95ed8: cmp             x1, x0
    // 0xa95edc: b.hs            #0xa96720
    // 0xa95ee0: LoadField: r0 = r3->field_f
    //     0xa95ee0: ldur            w0, [x3, #0xf]
    // 0xa95ee4: DecompressPointer r0
    //     0xa95ee4: add             x0, x0, HEAP, lsl #32
    // 0xa95ee8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa95ee8: add             x16, x0, x4, lsl #2
    //     0xa95eec: ldur            w1, [x16, #0xf]
    // 0xa95ef0: DecompressPointer r1
    //     0xa95ef0: add             x1, x1, HEAP, lsl #32
    // 0xa95ef4: cmp             w1, NULL
    // 0xa95ef8: b.ne            #0xa95f04
    // 0xa95efc: r0 = Null
    //     0xa95efc: mov             x0, NULL
    // 0xa95f00: b               #0xa95f30
    // 0xa95f04: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa95f04: ldur            w0, [x1, #0x17]
    // 0xa95f08: DecompressPointer r0
    //     0xa95f08: add             x0, x0, HEAP, lsl #32
    // 0xa95f0c: cmp             w0, NULL
    // 0xa95f10: b.ne            #0xa95f1c
    // 0xa95f14: r0 = Null
    //     0xa95f14: mov             x0, NULL
    // 0xa95f18: b               #0xa95f30
    // 0xa95f1c: LoadField: r1 = r0->field_7
    //     0xa95f1c: ldur            w1, [x0, #7]
    // 0xa95f20: cbnz            w1, #0xa95f2c
    // 0xa95f24: r0 = false
    //     0xa95f24: add             x0, NULL, #0x30  ; false
    // 0xa95f28: b               #0xa95f30
    // 0xa95f2c: r0 = true
    //     0xa95f2c: add             x0, NULL, #0x20  ; true
    // 0xa95f30: cmp             w0, NULL
    // 0xa95f34: b.ne            #0xa95f40
    // 0xa95f38: ldur            x2, [fp, #-0x28]
    // 0xa95f3c: b               #0xa961e0
    // 0xa95f40: tbnz            w0, #4, #0xa961dc
    // 0xa95f44: ldur            x0, [fp, #-8]
    // 0xa95f48: LoadField: r1 = r0->field_f
    //     0xa95f48: ldur            w1, [x0, #0xf]
    // 0xa95f4c: DecompressPointer r1
    //     0xa95f4c: add             x1, x1, HEAP, lsl #32
    // 0xa95f50: r0 = controller()
    //     0xa95f50: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa95f54: LoadField: r1 = r0->field_77
    //     0xa95f54: ldur            w1, [x0, #0x77]
    // 0xa95f58: DecompressPointer r1
    //     0xa95f58: add             x1, x1, HEAP, lsl #32
    // 0xa95f5c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa95f5c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa95f60: r0 = toList()
    //     0xa95f60: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa95f64: mov             x3, x0
    // 0xa95f68: ldur            x2, [fp, #-0x10]
    // 0xa95f6c: LoadField: r0 = r2->field_13
    //     0xa95f6c: ldur            w0, [x2, #0x13]
    // 0xa95f70: DecompressPointer r0
    //     0xa95f70: add             x0, x0, HEAP, lsl #32
    // 0xa95f74: LoadField: r1 = r3->field_b
    //     0xa95f74: ldur            w1, [x3, #0xb]
    // 0xa95f78: r4 = LoadInt32Instr(r0)
    //     0xa95f78: sbfx            x4, x0, #1, #0x1f
    //     0xa95f7c: tbz             w0, #0, #0xa95f84
    //     0xa95f80: ldur            x4, [x0, #7]
    // 0xa95f84: r0 = LoadInt32Instr(r1)
    //     0xa95f84: sbfx            x0, x1, #1, #0x1f
    // 0xa95f88: mov             x1, x4
    // 0xa95f8c: cmp             x1, x0
    // 0xa95f90: b.hs            #0xa96724
    // 0xa95f94: LoadField: r0 = r3->field_f
    //     0xa95f94: ldur            w0, [x3, #0xf]
    // 0xa95f98: DecompressPointer r0
    //     0xa95f98: add             x0, x0, HEAP, lsl #32
    // 0xa95f9c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa95f9c: add             x16, x0, x4, lsl #2
    //     0xa95fa0: ldur            w1, [x16, #0xf]
    // 0xa95fa4: DecompressPointer r1
    //     0xa95fa4: add             x1, x1, HEAP, lsl #32
    // 0xa95fa8: cmp             w1, NULL
    // 0xa95fac: b.ne            #0xa95fb8
    // 0xa95fb0: r0 = Null
    //     0xa95fb0: mov             x0, NULL
    // 0xa95fb4: b               #0xa95fd8
    // 0xa95fb8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa95fb8: ldur            w0, [x1, #0x17]
    // 0xa95fbc: DecompressPointer r0
    //     0xa95fbc: add             x0, x0, HEAP, lsl #32
    // 0xa95fc0: cmp             w0, NULL
    // 0xa95fc4: b.ne            #0xa95fd0
    // 0xa95fc8: r0 = Null
    //     0xa95fc8: mov             x0, NULL
    // 0xa95fcc: b               #0xa95fd8
    // 0xa95fd0: mov             x1, x0
    // 0xa95fd4: r0 = trim()
    //     0xa95fd4: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xa95fd8: cmp             w0, NULL
    // 0xa95fdc: b.ne            #0xa95fe8
    // 0xa95fe0: r3 = ""
    //     0xa95fe0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa95fe4: b               #0xa95fec
    // 0xa95fe8: mov             x3, x0
    // 0xa95fec: ldur            x2, [fp, #-0x10]
    // 0xa95ff0: ldur            x0, [fp, #-0x28]
    // 0xa95ff4: stur            x3, [fp, #-0x18]
    // 0xa95ff8: LoadField: r1 = r2->field_f
    //     0xa95ff8: ldur            w1, [x2, #0xf]
    // 0xa95ffc: DecompressPointer r1
    //     0xa95ffc: add             x1, x1, HEAP, lsl #32
    // 0xa96000: r0 = of()
    //     0xa96000: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa96004: LoadField: r1 = r0->field_87
    //     0xa96004: ldur            w1, [x0, #0x87]
    // 0xa96008: DecompressPointer r1
    //     0xa96008: add             x1, x1, HEAP, lsl #32
    // 0xa9600c: LoadField: r0 = r1->field_2b
    //     0xa9600c: ldur            w0, [x1, #0x2b]
    // 0xa96010: DecompressPointer r0
    //     0xa96010: add             x0, x0, HEAP, lsl #32
    // 0xa96014: LoadField: r1 = r0->field_13
    //     0xa96014: ldur            w1, [x0, #0x13]
    // 0xa96018: DecompressPointer r1
    //     0xa96018: add             x1, x1, HEAP, lsl #32
    // 0xa9601c: r16 = Instance_Color
    //     0xa9601c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa96020: stp             x16, x1, [SP]
    // 0xa96024: r1 = Instance_TextStyle
    //     0xa96024: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9b0] Obj!TextStyle@d62871
    //     0xa96028: ldr             x1, [x1, #0x9b0]
    // 0xa9602c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0xa9602c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0xa96030: ldr             x4, [x4, #0x9b8]
    // 0xa96034: r0 = copyWith()
    //     0xa96034: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa96038: ldur            x2, [fp, #-0x10]
    // 0xa9603c: stur            x0, [fp, #-0x30]
    // 0xa96040: LoadField: r1 = r2->field_f
    //     0xa96040: ldur            w1, [x2, #0xf]
    // 0xa96044: DecompressPointer r1
    //     0xa96044: add             x1, x1, HEAP, lsl #32
    // 0xa96048: r0 = of()
    //     0xa96048: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa9604c: LoadField: r1 = r0->field_87
    //     0xa9604c: ldur            w1, [x0, #0x87]
    // 0xa96050: DecompressPointer r1
    //     0xa96050: add             x1, x1, HEAP, lsl #32
    // 0xa96054: LoadField: r0 = r1->field_7
    //     0xa96054: ldur            w0, [x1, #7]
    // 0xa96058: DecompressPointer r0
    //     0xa96058: add             x0, x0, HEAP, lsl #32
    // 0xa9605c: r16 = Instance_Color
    //     0xa9605c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa96060: r30 = 12.000000
    //     0xa96060: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa96064: ldr             lr, [lr, #0x9e8]
    // 0xa96068: stp             lr, x16, [SP]
    // 0xa9606c: mov             x1, x0
    // 0xa96070: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa96070: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa96074: ldr             x4, [x4, #0x9b8]
    // 0xa96078: r0 = copyWith()
    //     0xa96078: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa9607c: ldur            x2, [fp, #-0x10]
    // 0xa96080: stur            x0, [fp, #-0x38]
    // 0xa96084: LoadField: r1 = r2->field_f
    //     0xa96084: ldur            w1, [x2, #0xf]
    // 0xa96088: DecompressPointer r1
    //     0xa96088: add             x1, x1, HEAP, lsl #32
    // 0xa9608c: r0 = of()
    //     0xa9608c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa96090: LoadField: r1 = r0->field_87
    //     0xa96090: ldur            w1, [x0, #0x87]
    // 0xa96094: DecompressPointer r1
    //     0xa96094: add             x1, x1, HEAP, lsl #32
    // 0xa96098: LoadField: r0 = r1->field_7
    //     0xa96098: ldur            w0, [x1, #7]
    // 0xa9609c: DecompressPointer r0
    //     0xa9609c: add             x0, x0, HEAP, lsl #32
    // 0xa960a0: r16 = Instance_Color
    //     0xa960a0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa960a4: r30 = 12.000000
    //     0xa960a4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa960a8: ldr             lr, [lr, #0x9e8]
    // 0xa960ac: stp             lr, x16, [SP]
    // 0xa960b0: mov             x1, x0
    // 0xa960b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa960b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa960b8: ldr             x4, [x4, #0x9b8]
    // 0xa960bc: r0 = copyWith()
    //     0xa960bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa960c0: stur            x0, [fp, #-0x40]
    // 0xa960c4: r0 = ReadMoreText()
    //     0xa960c4: bl              #0x99f824  ; AllocateReadMoreTextStub -> ReadMoreText (size=0x6c)
    // 0xa960c8: mov             x1, x0
    // 0xa960cc: ldur            x0, [fp, #-0x18]
    // 0xa960d0: stur            x1, [fp, #-0x48]
    // 0xa960d4: StoreField: r1->field_3f = r0
    //     0xa960d4: stur            w0, [x1, #0x3f]
    // 0xa960d8: r0 = " Read Less"
    //     0xa960d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c0] " Read Less"
    //     0xa960dc: ldr             x0, [x0, #0x9c0]
    // 0xa960e0: StoreField: r1->field_43 = r0
    //     0xa960e0: stur            w0, [x1, #0x43]
    // 0xa960e4: r0 = "Read More"
    //     0xa960e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c8] "Read More"
    //     0xa960e8: ldr             x0, [x0, #0x9c8]
    // 0xa960ec: StoreField: r1->field_47 = r0
    //     0xa960ec: stur            w0, [x1, #0x47]
    // 0xa960f0: r0 = 240
    //     0xa960f0: movz            x0, #0xf0
    // 0xa960f4: StoreField: r1->field_f = r0
    //     0xa960f4: stur            x0, [x1, #0xf]
    // 0xa960f8: r0 = 2
    //     0xa960f8: movz            x0, #0x2
    // 0xa960fc: ArrayStore: r1[0] = r0  ; List_8
    //     0xa960fc: stur            x0, [x1, #0x17]
    // 0xa96100: r0 = Instance_TrimMode
    //     0xa96100: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] Obj!TrimMode@d70201
    //     0xa96104: ldr             x0, [x0, #0x9d0]
    // 0xa96108: StoreField: r1->field_1f = r0
    //     0xa96108: stur            w0, [x1, #0x1f]
    // 0xa9610c: ldur            x0, [fp, #-0x30]
    // 0xa96110: StoreField: r1->field_4f = r0
    //     0xa96110: stur            w0, [x1, #0x4f]
    // 0xa96114: r0 = Instance_TextAlign
    //     0xa96114: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xa96118: StoreField: r1->field_53 = r0
    //     0xa96118: stur            w0, [x1, #0x53]
    // 0xa9611c: ldur            x0, [fp, #-0x38]
    // 0xa96120: StoreField: r1->field_23 = r0
    //     0xa96120: stur            w0, [x1, #0x23]
    // 0xa96124: ldur            x0, [fp, #-0x40]
    // 0xa96128: StoreField: r1->field_27 = r0
    //     0xa96128: stur            w0, [x1, #0x27]
    // 0xa9612c: r0 = "… "
    //     0xa9612c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "… "
    //     0xa96130: ldr             x0, [x0, #0x9d8]
    // 0xa96134: StoreField: r1->field_3b = r0
    //     0xa96134: stur            w0, [x1, #0x3b]
    // 0xa96138: r0 = true
    //     0xa96138: add             x0, NULL, #0x20  ; true
    // 0xa9613c: StoreField: r1->field_37 = r0
    //     0xa9613c: stur            w0, [x1, #0x37]
    // 0xa96140: r0 = Padding()
    //     0xa96140: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa96144: mov             x2, x0
    // 0xa96148: r0 = Instance_EdgeInsets
    //     0xa96148: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9e0] Obj!EdgeInsets@d577a1
    //     0xa9614c: ldr             x0, [x0, #0x9e0]
    // 0xa96150: stur            x2, [fp, #-0x18]
    // 0xa96154: StoreField: r2->field_f = r0
    //     0xa96154: stur            w0, [x2, #0xf]
    // 0xa96158: ldur            x0, [fp, #-0x48]
    // 0xa9615c: StoreField: r2->field_b = r0
    //     0xa9615c: stur            w0, [x2, #0xb]
    // 0xa96160: ldur            x0, [fp, #-0x28]
    // 0xa96164: LoadField: r1 = r0->field_b
    //     0xa96164: ldur            w1, [x0, #0xb]
    // 0xa96168: LoadField: r3 = r0->field_f
    //     0xa96168: ldur            w3, [x0, #0xf]
    // 0xa9616c: DecompressPointer r3
    //     0xa9616c: add             x3, x3, HEAP, lsl #32
    // 0xa96170: LoadField: r4 = r3->field_b
    //     0xa96170: ldur            w4, [x3, #0xb]
    // 0xa96174: r3 = LoadInt32Instr(r1)
    //     0xa96174: sbfx            x3, x1, #1, #0x1f
    // 0xa96178: stur            x3, [fp, #-0x50]
    // 0xa9617c: r1 = LoadInt32Instr(r4)
    //     0xa9617c: sbfx            x1, x4, #1, #0x1f
    // 0xa96180: cmp             x3, x1
    // 0xa96184: b.ne            #0xa96190
    // 0xa96188: mov             x1, x0
    // 0xa9618c: r0 = _growToNextCapacity()
    //     0xa9618c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa96190: ldur            x2, [fp, #-0x28]
    // 0xa96194: ldur            x3, [fp, #-0x50]
    // 0xa96198: add             x0, x3, #1
    // 0xa9619c: lsl             x1, x0, #1
    // 0xa961a0: StoreField: r2->field_b = r1
    //     0xa961a0: stur            w1, [x2, #0xb]
    // 0xa961a4: LoadField: r1 = r2->field_f
    //     0xa961a4: ldur            w1, [x2, #0xf]
    // 0xa961a8: DecompressPointer r1
    //     0xa961a8: add             x1, x1, HEAP, lsl #32
    // 0xa961ac: ldur            x0, [fp, #-0x18]
    // 0xa961b0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa961b0: add             x25, x1, x3, lsl #2
    //     0xa961b4: add             x25, x25, #0xf
    //     0xa961b8: str             w0, [x25]
    //     0xa961bc: tbz             w0, #0, #0xa961d8
    //     0xa961c0: ldurb           w16, [x1, #-1]
    //     0xa961c4: ldurb           w17, [x0, #-1]
    //     0xa961c8: and             x16, x17, x16, lsr #2
    //     0xa961cc: tst             x16, HEAP, lsr #32
    //     0xa961d0: b.eq            #0xa961d8
    //     0xa961d4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa961d8: b               #0xa961e0
    // 0xa961dc: ldur            x2, [fp, #-0x28]
    // 0xa961e0: ldur            x3, [fp, #-8]
    // 0xa961e4: ldur            x0, [fp, #-0x10]
    // 0xa961e8: LoadField: r1 = r3->field_f
    //     0xa961e8: ldur            w1, [x3, #0xf]
    // 0xa961ec: DecompressPointer r1
    //     0xa961ec: add             x1, x1, HEAP, lsl #32
    // 0xa961f0: r0 = controller()
    //     0xa961f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa961f4: LoadField: r1 = r0->field_77
    //     0xa961f4: ldur            w1, [x0, #0x77]
    // 0xa961f8: DecompressPointer r1
    //     0xa961f8: add             x1, x1, HEAP, lsl #32
    // 0xa961fc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa961fc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa96200: r0 = toList()
    //     0xa96200: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa96204: mov             x3, x0
    // 0xa96208: ldur            x2, [fp, #-0x10]
    // 0xa9620c: LoadField: r0 = r2->field_13
    //     0xa9620c: ldur            w0, [x2, #0x13]
    // 0xa96210: DecompressPointer r0
    //     0xa96210: add             x0, x0, HEAP, lsl #32
    // 0xa96214: LoadField: r1 = r3->field_b
    //     0xa96214: ldur            w1, [x3, #0xb]
    // 0xa96218: r4 = LoadInt32Instr(r0)
    //     0xa96218: sbfx            x4, x0, #1, #0x1f
    //     0xa9621c: tbz             w0, #0, #0xa96224
    //     0xa96220: ldur            x4, [x0, #7]
    // 0xa96224: r0 = LoadInt32Instr(r1)
    //     0xa96224: sbfx            x0, x1, #1, #0x1f
    // 0xa96228: mov             x1, x4
    // 0xa9622c: cmp             x1, x0
    // 0xa96230: b.hs            #0xa96728
    // 0xa96234: LoadField: r0 = r3->field_f
    //     0xa96234: ldur            w0, [x3, #0xf]
    // 0xa96238: DecompressPointer r0
    //     0xa96238: add             x0, x0, HEAP, lsl #32
    // 0xa9623c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa9623c: add             x16, x0, x4, lsl #2
    //     0xa96240: ldur            w1, [x16, #0xf]
    // 0xa96244: DecompressPointer r1
    //     0xa96244: add             x1, x1, HEAP, lsl #32
    // 0xa96248: cmp             w1, NULL
    // 0xa9624c: b.ne            #0xa96258
    // 0xa96250: r1 = Null
    //     0xa96250: mov             x1, NULL
    // 0xa96254: b               #0xa96264
    // 0xa96258: LoadField: r0 = r1->field_1b
    //     0xa96258: ldur            w0, [x1, #0x1b]
    // 0xa9625c: DecompressPointer r0
    //     0xa9625c: add             x0, x0, HEAP, lsl #32
    // 0xa96260: LoadField: r1 = r0->field_b
    //     0xa96260: ldur            w1, [x0, #0xb]
    // 0xa96264: ldur            x0, [fp, #-8]
    // 0xa96268: cmp             w1, NULL
    // 0xa9626c: b.eq            #0xa9672c
    // 0xa96270: r3 = LoadInt32Instr(r1)
    //     0xa96270: sbfx            x3, x1, #1, #0x1f
    // 0xa96274: cmp             x3, #1
    // 0xa96278: r16 = true
    //     0xa96278: add             x16, NULL, #0x20  ; true
    // 0xa9627c: r17 = false
    //     0xa9627c: add             x17, NULL, #0x30  ; false
    // 0xa96280: csel            x4, x16, x17, ge
    // 0xa96284: stur            x4, [fp, #-0x18]
    // 0xa96288: LoadField: r1 = r0->field_f
    //     0xa96288: ldur            w1, [x0, #0xf]
    // 0xa9628c: DecompressPointer r1
    //     0xa9628c: add             x1, x1, HEAP, lsl #32
    // 0xa96290: r0 = controller()
    //     0xa96290: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa96294: LoadField: r1 = r0->field_77
    //     0xa96294: ldur            w1, [x0, #0x77]
    // 0xa96298: DecompressPointer r1
    //     0xa96298: add             x1, x1, HEAP, lsl #32
    // 0xa9629c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa9629c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa962a0: r0 = toList()
    //     0xa962a0: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa962a4: mov             x3, x0
    // 0xa962a8: ldur            x2, [fp, #-0x10]
    // 0xa962ac: LoadField: r0 = r2->field_13
    //     0xa962ac: ldur            w0, [x2, #0x13]
    // 0xa962b0: DecompressPointer r0
    //     0xa962b0: add             x0, x0, HEAP, lsl #32
    // 0xa962b4: LoadField: r1 = r3->field_b
    //     0xa962b4: ldur            w1, [x3, #0xb]
    // 0xa962b8: r4 = LoadInt32Instr(r0)
    //     0xa962b8: sbfx            x4, x0, #1, #0x1f
    //     0xa962bc: tbz             w0, #0, #0xa962c4
    //     0xa962c0: ldur            x4, [x0, #7]
    // 0xa962c4: r0 = LoadInt32Instr(r1)
    //     0xa962c4: sbfx            x0, x1, #1, #0x1f
    // 0xa962c8: mov             x1, x4
    // 0xa962cc: cmp             x1, x0
    // 0xa962d0: b.hs            #0xa96730
    // 0xa962d4: LoadField: r0 = r3->field_f
    //     0xa962d4: ldur            w0, [x3, #0xf]
    // 0xa962d8: DecompressPointer r0
    //     0xa962d8: add             x0, x0, HEAP, lsl #32
    // 0xa962dc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa962dc: add             x16, x0, x4, lsl #2
    //     0xa962e0: ldur            w1, [x16, #0xf]
    // 0xa962e4: DecompressPointer r1
    //     0xa962e4: add             x1, x1, HEAP, lsl #32
    // 0xa962e8: cmp             w1, NULL
    // 0xa962ec: b.ne            #0xa962f8
    // 0xa962f0: r0 = Null
    //     0xa962f0: mov             x0, NULL
    // 0xa962f4: b               #0xa96314
    // 0xa962f8: LoadField: r0 = r1->field_1b
    //     0xa962f8: ldur            w0, [x1, #0x1b]
    // 0xa962fc: DecompressPointer r0
    //     0xa962fc: add             x0, x0, HEAP, lsl #32
    // 0xa96300: LoadField: r1 = r0->field_b
    //     0xa96300: ldur            w1, [x0, #0xb]
    // 0xa96304: cbz             w1, #0xa96310
    // 0xa96308: r0 = false
    //     0xa96308: add             x0, NULL, #0x30  ; false
    // 0xa9630c: b               #0xa96314
    // 0xa96310: r0 = true
    //     0xa96310: add             x0, NULL, #0x20  ; true
    // 0xa96314: cmp             w0, NULL
    // 0xa96318: b.eq            #0xa96328
    // 0xa9631c: tbnz            w0, #4, #0xa96328
    // 0xa96320: d0 = 0.000000
    //     0xa96320: eor             v0.16b, v0.16b, v0.16b
    // 0xa96324: b               #0xa9632c
    // 0xa96328: d0 = 48.000000
    //     0xa96328: ldr             d0, [PP, #0x6d18]  ; [pp+0x6d18] IMM: double(48) from 0x4048000000000000
    // 0xa9632c: ldur            x0, [fp, #-8]
    // 0xa96330: stur            d0, [fp, #-0x58]
    // 0xa96334: LoadField: r1 = r0->field_f
    //     0xa96334: ldur            w1, [x0, #0xf]
    // 0xa96338: DecompressPointer r1
    //     0xa96338: add             x1, x1, HEAP, lsl #32
    // 0xa9633c: r0 = controller()
    //     0xa9633c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa96340: LoadField: r1 = r0->field_77
    //     0xa96340: ldur            w1, [x0, #0x77]
    // 0xa96344: DecompressPointer r1
    //     0xa96344: add             x1, x1, HEAP, lsl #32
    // 0xa96348: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa96348: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa9634c: r0 = toList()
    //     0xa9634c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa96350: mov             x2, x0
    // 0xa96354: ldur            x3, [fp, #-0x10]
    // 0xa96358: LoadField: r0 = r3->field_13
    //     0xa96358: ldur            w0, [x3, #0x13]
    // 0xa9635c: DecompressPointer r0
    //     0xa9635c: add             x0, x0, HEAP, lsl #32
    // 0xa96360: LoadField: r1 = r2->field_b
    //     0xa96360: ldur            w1, [x2, #0xb]
    // 0xa96364: r4 = LoadInt32Instr(r0)
    //     0xa96364: sbfx            x4, x0, #1, #0x1f
    //     0xa96368: tbz             w0, #0, #0xa96370
    //     0xa9636c: ldur            x4, [x0, #7]
    // 0xa96370: r0 = LoadInt32Instr(r1)
    //     0xa96370: sbfx            x0, x1, #1, #0x1f
    // 0xa96374: mov             x1, x4
    // 0xa96378: cmp             x1, x0
    // 0xa9637c: b.hs            #0xa96734
    // 0xa96380: LoadField: r0 = r2->field_f
    //     0xa96380: ldur            w0, [x2, #0xf]
    // 0xa96384: DecompressPointer r0
    //     0xa96384: add             x0, x0, HEAP, lsl #32
    // 0xa96388: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa96388: add             x16, x0, x4, lsl #2
    //     0xa9638c: ldur            w1, [x16, #0xf]
    // 0xa96390: DecompressPointer r1
    //     0xa96390: add             x1, x1, HEAP, lsl #32
    // 0xa96394: cmp             w1, NULL
    // 0xa96398: b.ne            #0xa963a4
    // 0xa9639c: r0 = Null
    //     0xa9639c: mov             x0, NULL
    // 0xa963a0: b               #0xa963b4
    // 0xa963a4: LoadField: r0 = r1->field_1b
    //     0xa963a4: ldur            w0, [x1, #0x1b]
    // 0xa963a8: DecompressPointer r0
    //     0xa963a8: add             x0, x0, HEAP, lsl #32
    // 0xa963ac: LoadField: r1 = r0->field_b
    //     0xa963ac: ldur            w1, [x0, #0xb]
    // 0xa963b0: mov             x0, x1
    // 0xa963b4: cmp             w0, NULL
    // 0xa963b8: b.ne            #0xa963c4
    // 0xa963bc: r5 = 0
    //     0xa963bc: movz            x5, #0
    // 0xa963c0: b               #0xa963cc
    // 0xa963c4: r1 = LoadInt32Instr(r0)
    //     0xa963c4: sbfx            x1, x0, #1, #0x1f
    // 0xa963c8: mov             x5, x1
    // 0xa963cc: ldur            x0, [fp, #-0x18]
    // 0xa963d0: ldur            d0, [fp, #-0x58]
    // 0xa963d4: ldur            x4, [fp, #-0x28]
    // 0xa963d8: stur            x5, [fp, #-0x50]
    // 0xa963dc: r1 = Function '<anonymous closure>':.
    //     0xa963dc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea78] AnonymousClosure: (0x9b3480), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa963e0: ldr             x1, [x1, #0xa78]
    // 0xa963e4: r2 = Null
    //     0xa963e4: mov             x2, NULL
    // 0xa963e8: r0 = AllocateClosure()
    //     0xa963e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa963ec: ldur            x2, [fp, #-0x10]
    // 0xa963f0: r1 = Function '<anonymous closure>':.
    //     0xa963f0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea80] AnonymousClosure: (0xa96e14), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14fc2bc)
    //     0xa963f4: ldr             x1, [x1, #0xa80]
    // 0xa963f8: stur            x0, [fp, #-8]
    // 0xa963fc: r0 = AllocateClosure()
    //     0xa963fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa96400: stur            x0, [fp, #-0x30]
    // 0xa96404: r0 = ListView()
    //     0xa96404: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xa96408: stur            x0, [fp, #-0x38]
    // 0xa9640c: r16 = true
    //     0xa9640c: add             x16, NULL, #0x20  ; true
    // 0xa96410: r30 = Instance_Axis
    //     0xa96410: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa96414: stp             lr, x16, [SP]
    // 0xa96418: mov             x1, x0
    // 0xa9641c: ldur            x2, [fp, #-0x30]
    // 0xa96420: ldur            x3, [fp, #-0x50]
    // 0xa96424: ldur            x5, [fp, #-8]
    // 0xa96428: r4 = const [0, 0x6, 0x2, 0x4, scrollDirection, 0x5, shrinkWrap, 0x4, null]
    //     0xa96428: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f8e8] List(9) [0, 0x6, 0x2, 0x4, "scrollDirection", 0x5, "shrinkWrap", 0x4, Null]
    //     0xa9642c: ldr             x4, [x4, #0x8e8]
    // 0xa96430: r0 = ListView.separated()
    //     0xa96430: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xa96434: r0 = SizedBox()
    //     0xa96434: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa96438: mov             x1, x0
    // 0xa9643c: r0 = inf
    //     0xa9643c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xa96440: ldr             x0, [x0, #0x9f8]
    // 0xa96444: stur            x1, [fp, #-8]
    // 0xa96448: StoreField: r1->field_f = r0
    //     0xa96448: stur            w0, [x1, #0xf]
    // 0xa9644c: ldur            d0, [fp, #-0x58]
    // 0xa96450: r0 = inline_Allocate_Double()
    //     0xa96450: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xa96454: add             x0, x0, #0x10
    //     0xa96458: cmp             x2, x0
    //     0xa9645c: b.ls            #0xa96738
    //     0xa96460: str             x0, [THR, #0x50]  ; THR::top
    //     0xa96464: sub             x0, x0, #0xf
    //     0xa96468: movz            x2, #0xe15c
    //     0xa9646c: movk            x2, #0x3, lsl #16
    //     0xa96470: stur            x2, [x0, #-1]
    // 0xa96474: StoreField: r0->field_7 = d0
    //     0xa96474: stur            d0, [x0, #7]
    // 0xa96478: StoreField: r1->field_13 = r0
    //     0xa96478: stur            w0, [x1, #0x13]
    // 0xa9647c: ldur            x0, [fp, #-0x38]
    // 0xa96480: StoreField: r1->field_b = r0
    //     0xa96480: stur            w0, [x1, #0xb]
    // 0xa96484: r0 = Padding()
    //     0xa96484: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa96488: mov             x1, x0
    // 0xa9648c: r0 = Instance_EdgeInsets
    //     0xa9648c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xa96490: ldr             x0, [x0, #0xa00]
    // 0xa96494: stur            x1, [fp, #-0x30]
    // 0xa96498: StoreField: r1->field_f = r0
    //     0xa96498: stur            w0, [x1, #0xf]
    // 0xa9649c: ldur            x0, [fp, #-8]
    // 0xa964a0: StoreField: r1->field_b = r0
    //     0xa964a0: stur            w0, [x1, #0xb]
    // 0xa964a4: r0 = Visibility()
    //     0xa964a4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa964a8: mov             x1, x0
    // 0xa964ac: ldur            x0, [fp, #-0x30]
    // 0xa964b0: stur            x1, [fp, #-8]
    // 0xa964b4: StoreField: r1->field_b = r0
    //     0xa964b4: stur            w0, [x1, #0xb]
    // 0xa964b8: r0 = Instance_SizedBox
    //     0xa964b8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa964bc: StoreField: r1->field_f = r0
    //     0xa964bc: stur            w0, [x1, #0xf]
    // 0xa964c0: ldur            x0, [fp, #-0x18]
    // 0xa964c4: StoreField: r1->field_13 = r0
    //     0xa964c4: stur            w0, [x1, #0x13]
    // 0xa964c8: r0 = false
    //     0xa964c8: add             x0, NULL, #0x30  ; false
    // 0xa964cc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa964cc: stur            w0, [x1, #0x17]
    // 0xa964d0: StoreField: r1->field_1b = r0
    //     0xa964d0: stur            w0, [x1, #0x1b]
    // 0xa964d4: StoreField: r1->field_1f = r0
    //     0xa964d4: stur            w0, [x1, #0x1f]
    // 0xa964d8: StoreField: r1->field_23 = r0
    //     0xa964d8: stur            w0, [x1, #0x23]
    // 0xa964dc: StoreField: r1->field_27 = r0
    //     0xa964dc: stur            w0, [x1, #0x27]
    // 0xa964e0: StoreField: r1->field_2b = r0
    //     0xa964e0: stur            w0, [x1, #0x2b]
    // 0xa964e4: r0 = GestureDetector()
    //     0xa964e4: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa964e8: ldur            x2, [fp, #-0x10]
    // 0xa964ec: r1 = Function '<anonymous closure>':.
    //     0xa964ec: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea88] AnonymousClosure: (0x9b3408), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa964f0: ldr             x1, [x1, #0xa88]
    // 0xa964f4: stur            x0, [fp, #-0x18]
    // 0xa964f8: r0 = AllocateClosure()
    //     0xa964f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa964fc: ldur            x2, [fp, #-0x10]
    // 0xa96500: r1 = Function '<anonymous closure>':.
    //     0xa96500: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea90] AnonymousClosure: (0xa96750), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14fc2bc)
    //     0xa96504: ldr             x1, [x1, #0xa90]
    // 0xa96508: stur            x0, [fp, #-0x10]
    // 0xa9650c: r0 = AllocateClosure()
    //     0xa9650c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa96510: ldur            x16, [fp, #-0x10]
    // 0xa96514: stp             x0, x16, [SP, #8]
    // 0xa96518: r16 = Instance_Icon
    //     0xa96518: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa18] Obj!Icon@d65e71
    //     0xa9651c: ldr             x16, [x16, #0xa18]
    // 0xa96520: str             x16, [SP]
    // 0xa96524: ldur            x1, [fp, #-0x18]
    // 0xa96528: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, onTap, 0x2, onTapDown, 0x1, null]
    //     0xa96528: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa20] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "onTap", 0x2, "onTapDown", 0x1, Null]
    //     0xa9652c: ldr             x4, [x4, #0xa20]
    // 0xa96530: r0 = GestureDetector()
    //     0xa96530: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa96534: r0 = Align()
    //     0xa96534: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa96538: mov             x3, x0
    // 0xa9653c: r0 = Instance_Alignment
    //     0xa9653c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0xa96540: ldr             x0, [x0, #0xa28]
    // 0xa96544: stur            x3, [fp, #-0x10]
    // 0xa96548: StoreField: r3->field_f = r0
    //     0xa96548: stur            w0, [x3, #0xf]
    // 0xa9654c: ldur            x1, [fp, #-0x18]
    // 0xa96550: StoreField: r3->field_b = r1
    //     0xa96550: stur            w1, [x3, #0xb]
    // 0xa96554: r1 = Null
    //     0xa96554: mov             x1, NULL
    // 0xa96558: r2 = 4
    //     0xa96558: movz            x2, #0x4
    // 0xa9655c: r0 = AllocateArray()
    //     0xa9655c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa96560: mov             x2, x0
    // 0xa96564: ldur            x0, [fp, #-8]
    // 0xa96568: stur            x2, [fp, #-0x18]
    // 0xa9656c: StoreField: r2->field_f = r0
    //     0xa9656c: stur            w0, [x2, #0xf]
    // 0xa96570: ldur            x0, [fp, #-0x10]
    // 0xa96574: StoreField: r2->field_13 = r0
    //     0xa96574: stur            w0, [x2, #0x13]
    // 0xa96578: r1 = <Widget>
    //     0xa96578: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa9657c: r0 = AllocateGrowableArray()
    //     0xa9657c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa96580: mov             x1, x0
    // 0xa96584: ldur            x0, [fp, #-0x18]
    // 0xa96588: stur            x1, [fp, #-8]
    // 0xa9658c: StoreField: r1->field_f = r0
    //     0xa9658c: stur            w0, [x1, #0xf]
    // 0xa96590: r0 = 4
    //     0xa96590: movz            x0, #0x4
    // 0xa96594: StoreField: r1->field_b = r0
    //     0xa96594: stur            w0, [x1, #0xb]
    // 0xa96598: r0 = Stack()
    //     0xa96598: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa9659c: mov             x2, x0
    // 0xa965a0: r0 = Instance_Alignment
    //     0xa965a0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0xa965a4: ldr             x0, [x0, #0xa28]
    // 0xa965a8: stur            x2, [fp, #-0x10]
    // 0xa965ac: StoreField: r2->field_f = r0
    //     0xa965ac: stur            w0, [x2, #0xf]
    // 0xa965b0: r0 = Instance_StackFit
    //     0xa965b0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa965b4: ldr             x0, [x0, #0xfa8]
    // 0xa965b8: ArrayStore: r2[0] = r0  ; List_4
    //     0xa965b8: stur            w0, [x2, #0x17]
    // 0xa965bc: r0 = Instance_Clip
    //     0xa965bc: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa965c0: ldr             x0, [x0, #0x7e0]
    // 0xa965c4: StoreField: r2->field_1b = r0
    //     0xa965c4: stur            w0, [x2, #0x1b]
    // 0xa965c8: ldur            x0, [fp, #-8]
    // 0xa965cc: StoreField: r2->field_b = r0
    //     0xa965cc: stur            w0, [x2, #0xb]
    // 0xa965d0: ldur            x0, [fp, #-0x28]
    // 0xa965d4: LoadField: r1 = r0->field_b
    //     0xa965d4: ldur            w1, [x0, #0xb]
    // 0xa965d8: LoadField: r3 = r0->field_f
    //     0xa965d8: ldur            w3, [x0, #0xf]
    // 0xa965dc: DecompressPointer r3
    //     0xa965dc: add             x3, x3, HEAP, lsl #32
    // 0xa965e0: LoadField: r4 = r3->field_b
    //     0xa965e0: ldur            w4, [x3, #0xb]
    // 0xa965e4: r3 = LoadInt32Instr(r1)
    //     0xa965e4: sbfx            x3, x1, #1, #0x1f
    // 0xa965e8: stur            x3, [fp, #-0x50]
    // 0xa965ec: r1 = LoadInt32Instr(r4)
    //     0xa965ec: sbfx            x1, x4, #1, #0x1f
    // 0xa965f0: cmp             x3, x1
    // 0xa965f4: b.ne            #0xa96600
    // 0xa965f8: mov             x1, x0
    // 0xa965fc: r0 = _growToNextCapacity()
    //     0xa965fc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa96600: ldur            x2, [fp, #-0x28]
    // 0xa96604: ldur            x3, [fp, #-0x50]
    // 0xa96608: add             x0, x3, #1
    // 0xa9660c: lsl             x1, x0, #1
    // 0xa96610: StoreField: r2->field_b = r1
    //     0xa96610: stur            w1, [x2, #0xb]
    // 0xa96614: LoadField: r1 = r2->field_f
    //     0xa96614: ldur            w1, [x2, #0xf]
    // 0xa96618: DecompressPointer r1
    //     0xa96618: add             x1, x1, HEAP, lsl #32
    // 0xa9661c: ldur            x0, [fp, #-0x10]
    // 0xa96620: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa96620: add             x25, x1, x3, lsl #2
    //     0xa96624: add             x25, x25, #0xf
    //     0xa96628: str             w0, [x25]
    //     0xa9662c: tbz             w0, #0, #0xa96648
    //     0xa96630: ldurb           w16, [x1, #-1]
    //     0xa96634: ldurb           w17, [x0, #-1]
    //     0xa96638: and             x16, x17, x16, lsr #2
    //     0xa9663c: tst             x16, HEAP, lsr #32
    //     0xa96640: b.eq            #0xa96648
    //     0xa96644: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa96648: r0 = Column()
    //     0xa96648: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa9664c: mov             x1, x0
    // 0xa96650: r0 = Instance_Axis
    //     0xa96650: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa96654: stur            x1, [fp, #-8]
    // 0xa96658: StoreField: r1->field_f = r0
    //     0xa96658: stur            w0, [x1, #0xf]
    // 0xa9665c: r0 = Instance_MainAxisAlignment
    //     0xa9665c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa96660: ldr             x0, [x0, #0xa08]
    // 0xa96664: StoreField: r1->field_13 = r0
    //     0xa96664: stur            w0, [x1, #0x13]
    // 0xa96668: r0 = Instance_MainAxisSize
    //     0xa96668: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa9666c: ldr             x0, [x0, #0xa10]
    // 0xa96670: ArrayStore: r1[0] = r0  ; List_4
    //     0xa96670: stur            w0, [x1, #0x17]
    // 0xa96674: r0 = Instance_CrossAxisAlignment
    //     0xa96674: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa96678: ldr             x0, [x0, #0x890]
    // 0xa9667c: StoreField: r1->field_1b = r0
    //     0xa9667c: stur            w0, [x1, #0x1b]
    // 0xa96680: r0 = Instance_VerticalDirection
    //     0xa96680: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa96684: ldr             x0, [x0, #0xa20]
    // 0xa96688: StoreField: r1->field_23 = r0
    //     0xa96688: stur            w0, [x1, #0x23]
    // 0xa9668c: r0 = Instance_Clip
    //     0xa9668c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa96690: ldr             x0, [x0, #0x38]
    // 0xa96694: StoreField: r1->field_2b = r0
    //     0xa96694: stur            w0, [x1, #0x2b]
    // 0xa96698: StoreField: r1->field_2f = rZR
    //     0xa96698: stur            xzr, [x1, #0x2f]
    // 0xa9669c: ldur            x0, [fp, #-0x28]
    // 0xa966a0: StoreField: r1->field_b = r0
    //     0xa966a0: stur            w0, [x1, #0xb]
    // 0xa966a4: r0 = Padding()
    //     0xa966a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa966a8: mov             x1, x0
    // 0xa966ac: r0 = Instance_EdgeInsets
    //     0xa966ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xa966b0: ldr             x0, [x0, #0x1f0]
    // 0xa966b4: stur            x1, [fp, #-0x10]
    // 0xa966b8: StoreField: r1->field_f = r0
    //     0xa966b8: stur            w0, [x1, #0xf]
    // 0xa966bc: ldur            x0, [fp, #-8]
    // 0xa966c0: StoreField: r1->field_b = r0
    //     0xa966c0: stur            w0, [x1, #0xb]
    // 0xa966c4: r0 = Container()
    //     0xa966c4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa966c8: stur            x0, [fp, #-8]
    // 0xa966cc: ldur            x16, [fp, #-0x20]
    // 0xa966d0: ldur            lr, [fp, #-0x10]
    // 0xa966d4: stp             lr, x16, [SP]
    // 0xa966d8: mov             x1, x0
    // 0xa966dc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xa966dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xa966e0: ldr             x4, [x4, #0x88]
    // 0xa966e4: r0 = Container()
    //     0xa966e4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa966e8: ldur            x0, [fp, #-8]
    // 0xa966ec: LeaveFrame
    //     0xa966ec: mov             SP, fp
    //     0xa966f0: ldp             fp, lr, [SP], #0x10
    // 0xa966f4: ret
    //     0xa966f4: ret             
    // 0xa966f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa966f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa966fc: b               #0xa95350
    // 0xa96700: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa96700: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa96704: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa96704: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa96708: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa96708: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa9670c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa9670c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa96710: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa96710: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa96714: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa96714: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa96718: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa96718: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa9671c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa9671c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa96720: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa96720: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa96724: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa96724: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa96728: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa96728: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa9672c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9672c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa96730: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa96730: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa96734: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa96734: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa96738: SaveReg d0
    //     0xa96738: str             q0, [SP, #-0x10]!
    // 0xa9673c: SaveReg r1
    //     0xa9673c: str             x1, [SP, #-8]!
    // 0xa96740: r0 = AllocateDouble()
    //     0xa96740: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa96744: RestoreReg r1
    //     0xa96744: ldr             x1, [SP], #8
    // 0xa96748: RestoreReg d0
    //     0xa96748: ldr             q0, [SP], #0x10
    // 0xa9674c: b               #0xa96474
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa96750, size: 0x178
    // 0xa96750: EnterFrame
    //     0xa96750: stp             fp, lr, [SP, #-0x10]!
    //     0xa96754: mov             fp, SP
    // 0xa96758: AllocStack(0x28)
    //     0xa96758: sub             SP, SP, #0x28
    // 0xa9675c: SetupParameters()
    //     0xa9675c: ldr             x0, [fp, #0x10]
    //     0xa96760: ldur            w2, [x0, #0x17]
    //     0xa96764: add             x2, x2, HEAP, lsl #32
    //     0xa96768: stur            x2, [fp, #-0x10]
    // 0xa9676c: CheckStackOverflow
    //     0xa9676c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa96770: cmp             SP, x16
    //     0xa96774: b.ls            #0xa968b8
    // 0xa96778: LoadField: r0 = r2->field_b
    //     0xa96778: ldur            w0, [x2, #0xb]
    // 0xa9677c: DecompressPointer r0
    //     0xa9677c: add             x0, x0, HEAP, lsl #32
    // 0xa96780: stur            x0, [fp, #-8]
    // 0xa96784: LoadField: r1 = r0->field_f
    //     0xa96784: ldur            w1, [x0, #0xf]
    // 0xa96788: DecompressPointer r1
    //     0xa96788: add             x1, x1, HEAP, lsl #32
    // 0xa9678c: r0 = controller()
    //     0xa9678c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa96790: LoadField: r1 = r0->field_8f
    //     0xa96790: ldur            w1, [x0, #0x8f]
    // 0xa96794: DecompressPointer r1
    //     0xa96794: add             x1, x1, HEAP, lsl #32
    // 0xa96798: cmp             w1, NULL
    // 0xa9679c: b.eq            #0xa968a8
    // 0xa967a0: ldur            x0, [fp, #-0x10]
    // 0xa967a4: ldur            x2, [fp, #-8]
    // 0xa967a8: LoadField: r1 = r2->field_f
    //     0xa967a8: ldur            w1, [x2, #0xf]
    // 0xa967ac: DecompressPointer r1
    //     0xa967ac: add             x1, x1, HEAP, lsl #32
    // 0xa967b0: r0 = controller()
    //     0xa967b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa967b4: mov             x1, x0
    // 0xa967b8: r2 = "flag_dots"
    //     0xa967b8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fa30] "flag_dots"
    //     0xa967bc: ldr             x2, [x2, #0xa30]
    // 0xa967c0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa967c0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa967c4: r0 = ratingReviewClickedEvent()
    //     0xa967c4: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0xa967c8: ldur            x0, [fp, #-8]
    // 0xa967cc: LoadField: r2 = r0->field_f
    //     0xa967cc: ldur            w2, [x0, #0xf]
    // 0xa967d0: DecompressPointer r2
    //     0xa967d0: add             x2, x2, HEAP, lsl #32
    // 0xa967d4: ldur            x3, [fp, #-0x10]
    // 0xa967d8: stur            x2, [fp, #-0x20]
    // 0xa967dc: LoadField: r4 = r3->field_f
    //     0xa967dc: ldur            w4, [x3, #0xf]
    // 0xa967e0: DecompressPointer r4
    //     0xa967e0: add             x4, x4, HEAP, lsl #32
    // 0xa967e4: mov             x1, x2
    // 0xa967e8: stur            x4, [fp, #-0x18]
    // 0xa967ec: r0 = controller()
    //     0xa967ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa967f0: LoadField: r3 = r0->field_8f
    //     0xa967f0: ldur            w3, [x0, #0x8f]
    // 0xa967f4: DecompressPointer r3
    //     0xa967f4: add             x3, x3, HEAP, lsl #32
    // 0xa967f8: stur            x3, [fp, #-0x28]
    // 0xa967fc: cmp             w3, NULL
    // 0xa96800: b.eq            #0xa968c0
    // 0xa96804: ldur            x0, [fp, #-8]
    // 0xa96808: LoadField: r1 = r0->field_f
    //     0xa96808: ldur            w1, [x0, #0xf]
    // 0xa9680c: DecompressPointer r1
    //     0xa9680c: add             x1, x1, HEAP, lsl #32
    // 0xa96810: r0 = controller()
    //     0xa96810: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa96814: LoadField: r1 = r0->field_77
    //     0xa96814: ldur            w1, [x0, #0x77]
    // 0xa96818: DecompressPointer r1
    //     0xa96818: add             x1, x1, HEAP, lsl #32
    // 0xa9681c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa9681c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa96820: r0 = toList()
    //     0xa96820: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa96824: mov             x2, x0
    // 0xa96828: ldur            x0, [fp, #-0x10]
    // 0xa9682c: LoadField: r1 = r0->field_13
    //     0xa9682c: ldur            w1, [x0, #0x13]
    // 0xa96830: DecompressPointer r1
    //     0xa96830: add             x1, x1, HEAP, lsl #32
    // 0xa96834: LoadField: r0 = r2->field_b
    //     0xa96834: ldur            w0, [x2, #0xb]
    // 0xa96838: r3 = LoadInt32Instr(r1)
    //     0xa96838: sbfx            x3, x1, #1, #0x1f
    //     0xa9683c: tbz             w1, #0, #0xa96844
    //     0xa96840: ldur            x3, [x1, #7]
    // 0xa96844: r1 = LoadInt32Instr(r0)
    //     0xa96844: sbfx            x1, x0, #1, #0x1f
    // 0xa96848: mov             x0, x1
    // 0xa9684c: mov             x1, x3
    // 0xa96850: cmp             x1, x0
    // 0xa96854: b.hs            #0xa968c4
    // 0xa96858: LoadField: r0 = r2->field_f
    //     0xa96858: ldur            w0, [x2, #0xf]
    // 0xa9685c: DecompressPointer r0
    //     0xa9685c: add             x0, x0, HEAP, lsl #32
    // 0xa96860: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xa96860: add             x16, x0, x3, lsl #2
    //     0xa96864: ldur            w1, [x16, #0xf]
    // 0xa96868: DecompressPointer r1
    //     0xa96868: add             x1, x1, HEAP, lsl #32
    // 0xa9686c: cmp             w1, NULL
    // 0xa96870: b.ne            #0xa9687c
    // 0xa96874: r0 = Null
    //     0xa96874: mov             x0, NULL
    // 0xa96878: b               #0xa96884
    // 0xa9687c: LoadField: r0 = r1->field_b
    //     0xa9687c: ldur            w0, [x1, #0xb]
    // 0xa96880: DecompressPointer r0
    //     0xa96880: add             x0, x0, HEAP, lsl #32
    // 0xa96884: cmp             w0, NULL
    // 0xa96888: b.ne            #0xa96894
    // 0xa9688c: r5 = ""
    //     0xa9688c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa96890: b               #0xa96898
    // 0xa96894: mov             x5, x0
    // 0xa96898: ldur            x1, [fp, #-0x20]
    // 0xa9689c: ldur            x2, [fp, #-0x18]
    // 0xa968a0: ldur            x3, [fp, #-0x28]
    // 0xa968a4: r0 = showMenuItem()
    //     0xa968a4: bl              #0xa968c8  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::showMenuItem
    // 0xa968a8: r0 = Null
    //     0xa968a8: mov             x0, NULL
    // 0xa968ac: LeaveFrame
    //     0xa968ac: mov             SP, fp
    //     0xa968b0: ldp             fp, lr, [SP], #0x10
    // 0xa968b4: ret
    //     0xa968b4: ret             
    // 0xa968b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa968b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa968bc: b               #0xa96778
    // 0xa968c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa968c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa968c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa968c4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ showMenuItem(/* No info */) {
    // ** addr: 0xa968c8, size: 0x54c
    // 0xa968c8: EnterFrame
    //     0xa968c8: stp             fp, lr, [SP, #-0x10]!
    //     0xa968cc: mov             fp, SP
    // 0xa968d0: AllocStack(0xa0)
    //     0xa968d0: sub             SP, SP, #0xa0
    // 0xa968d4: SetupParameters(ReviewListWidget this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xa968d4: mov             x0, x1
    //     0xa968d8: stur            x1, [fp, #-8]
    //     0xa968dc: mov             x1, x2
    //     0xa968e0: stur            x2, [fp, #-0x10]
    //     0xa968e4: stur            x3, [fp, #-0x18]
    //     0xa968e8: stur            x5, [fp, #-0x20]
    // 0xa968ec: CheckStackOverflow
    //     0xa968ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa968f0: cmp             SP, x16
    //     0xa968f4: b.ls            #0xa96e0c
    // 0xa968f8: r1 = 2
    //     0xa968f8: movz            x1, #0x2
    // 0xa968fc: r0 = AllocateContext()
    //     0xa968fc: bl              #0x16f6108  ; AllocateContextStub
    // 0xa96900: mov             x2, x0
    // 0xa96904: ldur            x0, [fp, #-8]
    // 0xa96908: stur            x2, [fp, #-0x28]
    // 0xa9690c: StoreField: r2->field_f = r0
    //     0xa9690c: stur            w0, [x2, #0xf]
    // 0xa96910: ldur            x1, [fp, #-0x20]
    // 0xa96914: StoreField: r2->field_13 = r1
    //     0xa96914: stur            w1, [x2, #0x13]
    // 0xa96918: mov             x1, x0
    // 0xa9691c: r0 = controller()
    //     0xa9691c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa96920: LoadField: r1 = r0->field_93
    //     0xa96920: ldur            w1, [x0, #0x93]
    // 0xa96924: DecompressPointer r1
    //     0xa96924: add             x1, x1, HEAP, lsl #32
    // 0xa96928: ldur            x0, [fp, #-0x28]
    // 0xa9692c: LoadField: r2 = r0->field_13
    //     0xa9692c: ldur            w2, [x0, #0x13]
    // 0xa96930: DecompressPointer r2
    //     0xa96930: add             x2, x2, HEAP, lsl #32
    // 0xa96934: r0 = []()
    //     0xa96934: bl              #0x1653f0c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::[]
    // 0xa96938: r1 = 60
    //     0xa96938: movz            x1, #0x3c
    // 0xa9693c: branchIfSmi(r0, 0xa96948)
    //     0xa9693c: tbz             w0, #0, #0xa96948
    // 0xa96940: r1 = LoadClassIdInstr(r0)
    //     0xa96940: ldur            x1, [x0, #-1]
    //     0xa96944: ubfx            x1, x1, #0xc, #0x14
    // 0xa96948: r16 = true
    //     0xa96948: add             x16, NULL, #0x20  ; true
    // 0xa9694c: stp             x16, x0, [SP]
    // 0xa96950: mov             x0, x1
    // 0xa96954: mov             lr, x0
    // 0xa96958: ldr             lr, [x21, lr, lsl #3]
    // 0xa9695c: blr             lr
    // 0xa96960: tbnz            w0, #4, #0xa9696c
    // 0xa96964: d0 = 100.000000
    //     0xa96964: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xa96968: b               #0xa96974
    // 0xa9696c: d0 = 120.000000
    //     0xa9696c: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2fa38] IMM: double(120) from 0x405e000000000000
    //     0xa96970: ldr             d0, [x17, #0xa38]
    // 0xa96974: ldur            x0, [fp, #-0x18]
    // 0xa96978: ldur            x2, [fp, #-0x28]
    // 0xa9697c: stur            d0, [fp, #-0x58]
    // 0xa96980: r0 = BoxConstraints()
    //     0xa96980: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xa96984: stur            x0, [fp, #-0x20]
    // 0xa96988: StoreField: r0->field_7 = rZR
    //     0xa96988: stur            xzr, [x0, #7]
    // 0xa9698c: ldur            d0, [fp, #-0x58]
    // 0xa96990: StoreField: r0->field_f = d0
    //     0xa96990: stur            d0, [x0, #0xf]
    // 0xa96994: ArrayStore: r0[0] = rZR  ; List_8
    //     0xa96994: stur            xzr, [x0, #0x17]
    // 0xa96998: d0 = inf
    //     0xa96998: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xa9699c: StoreField: r0->field_1f = d0
    //     0xa9699c: stur            d0, [x0, #0x1f]
    // 0xa969a0: ldur            x1, [fp, #-0x18]
    // 0xa969a4: LoadField: d0 = r1->field_7
    //     0xa969a4: ldur            d0, [x1, #7]
    // 0xa969a8: stur            d0, [fp, #-0x70]
    // 0xa969ac: LoadField: d1 = r1->field_f
    //     0xa969ac: ldur            d1, [x1, #0xf]
    // 0xa969b0: stur            d1, [fp, #-0x68]
    // 0xa969b4: d2 = 50.000000
    //     0xa969b4: ldr             d2, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0xa969b8: fsub            d3, d1, d2
    // 0xa969bc: stur            d3, [fp, #-0x60]
    // 0xa969c0: fadd            d4, d0, d2
    // 0xa969c4: stur            d4, [fp, #-0x58]
    // 0xa969c8: r0 = RelativeRect()
    //     0xa969c8: bl              #0x9abaf4  ; AllocateRelativeRectStub -> RelativeRect (size=0x28)
    // 0xa969cc: ldur            d0, [fp, #-0x70]
    // 0xa969d0: stur            x0, [fp, #-0x18]
    // 0xa969d4: StoreField: r0->field_7 = d0
    //     0xa969d4: stur            d0, [x0, #7]
    // 0xa969d8: ldur            d0, [fp, #-0x60]
    // 0xa969dc: StoreField: r0->field_f = d0
    //     0xa969dc: stur            d0, [x0, #0xf]
    // 0xa969e0: ldur            d0, [fp, #-0x58]
    // 0xa969e4: ArrayStore: r0[0] = d0  ; List_8
    //     0xa969e4: stur            d0, [x0, #0x17]
    // 0xa969e8: ldur            d0, [fp, #-0x68]
    // 0xa969ec: StoreField: r0->field_1f = d0
    //     0xa969ec: stur            d0, [x0, #0x1f]
    // 0xa969f0: ldur            x1, [fp, #-8]
    // 0xa969f4: r0 = controller()
    //     0xa969f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa969f8: LoadField: r1 = r0->field_93
    //     0xa969f8: ldur            w1, [x0, #0x93]
    // 0xa969fc: DecompressPointer r1
    //     0xa969fc: add             x1, x1, HEAP, lsl #32
    // 0xa96a00: ldur            x0, [fp, #-0x28]
    // 0xa96a04: LoadField: r2 = r0->field_13
    //     0xa96a04: ldur            w2, [x0, #0x13]
    // 0xa96a08: DecompressPointer r2
    //     0xa96a08: add             x2, x2, HEAP, lsl #32
    // 0xa96a0c: r0 = []()
    //     0xa96a0c: bl              #0x1653f0c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::[]
    // 0xa96a10: r1 = 60
    //     0xa96a10: movz            x1, #0x3c
    // 0xa96a14: branchIfSmi(r0, 0xa96a20)
    //     0xa96a14: tbz             w0, #0, #0xa96a20
    // 0xa96a18: r1 = LoadClassIdInstr(r0)
    //     0xa96a18: ldur            x1, [x0, #-1]
    //     0xa96a1c: ubfx            x1, x1, #0xc, #0x14
    // 0xa96a20: r16 = true
    //     0xa96a20: add             x16, NULL, #0x20  ; true
    // 0xa96a24: stp             x16, x0, [SP]
    // 0xa96a28: mov             x0, x1
    // 0xa96a2c: mov             lr, x0
    // 0xa96a30: ldr             lr, [x21, lr, lsl #3]
    // 0xa96a34: blr             lr
    // 0xa96a38: tbnz            w0, #4, #0xa96a44
    // 0xa96a3c: r3 = Null
    //     0xa96a3c: mov             x3, NULL
    // 0xa96a40: b               #0xa96a58
    // 0xa96a44: ldur            x2, [fp, #-0x28]
    // 0xa96a48: r1 = Function '<anonymous closure>':.
    //     0xa96a48: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea98] AnonymousClosure: (0x9b3344), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::showMenuItem (0x9b2c74)
    //     0xa96a4c: ldr             x1, [x1, #0xa98]
    // 0xa96a50: r0 = AllocateClosure()
    //     0xa96a50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa96a54: mov             x3, x0
    // 0xa96a58: ldur            x0, [fp, #-0x28]
    // 0xa96a5c: stur            x3, [fp, #-0x30]
    // 0xa96a60: r1 = <Widget>
    //     0xa96a60: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa96a64: r2 = 0
    //     0xa96a64: movz            x2, #0
    // 0xa96a68: r0 = _GrowableList()
    //     0xa96a68: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xa96a6c: ldur            x1, [fp, #-8]
    // 0xa96a70: stur            x0, [fp, #-0x38]
    // 0xa96a74: r0 = controller()
    //     0xa96a74: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa96a78: LoadField: r1 = r0->field_93
    //     0xa96a78: ldur            w1, [x0, #0x93]
    // 0xa96a7c: DecompressPointer r1
    //     0xa96a7c: add             x1, x1, HEAP, lsl #32
    // 0xa96a80: ldur            x0, [fp, #-0x28]
    // 0xa96a84: LoadField: r2 = r0->field_13
    //     0xa96a84: ldur            w2, [x0, #0x13]
    // 0xa96a88: DecompressPointer r2
    //     0xa96a88: add             x2, x2, HEAP, lsl #32
    // 0xa96a8c: r0 = []()
    //     0xa96a8c: bl              #0x1653f0c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::[]
    // 0xa96a90: r1 = 60
    //     0xa96a90: movz            x1, #0x3c
    // 0xa96a94: branchIfSmi(r0, 0xa96aa0)
    //     0xa96a94: tbz             w0, #0, #0xa96aa0
    // 0xa96a98: r1 = LoadClassIdInstr(r0)
    //     0xa96a98: ldur            x1, [x0, #-1]
    //     0xa96a9c: ubfx            x1, x1, #0xc, #0x14
    // 0xa96aa0: r16 = true
    //     0xa96aa0: add             x16, NULL, #0x20  ; true
    // 0xa96aa4: stp             x16, x0, [SP]
    // 0xa96aa8: mov             x0, x1
    // 0xa96aac: mov             lr, x0
    // 0xa96ab0: ldr             lr, [x21, lr, lsl #3]
    // 0xa96ab4: blr             lr
    // 0xa96ab8: tbnz            w0, #4, #0xa96b1c
    // 0xa96abc: ldur            x0, [fp, #-0x38]
    // 0xa96ac0: LoadField: r1 = r0->field_b
    //     0xa96ac0: ldur            w1, [x0, #0xb]
    // 0xa96ac4: LoadField: r2 = r0->field_f
    //     0xa96ac4: ldur            w2, [x0, #0xf]
    // 0xa96ac8: DecompressPointer r2
    //     0xa96ac8: add             x2, x2, HEAP, lsl #32
    // 0xa96acc: LoadField: r3 = r2->field_b
    //     0xa96acc: ldur            w3, [x2, #0xb]
    // 0xa96ad0: r2 = LoadInt32Instr(r1)
    //     0xa96ad0: sbfx            x2, x1, #1, #0x1f
    // 0xa96ad4: stur            x2, [fp, #-0x40]
    // 0xa96ad8: r1 = LoadInt32Instr(r3)
    //     0xa96ad8: sbfx            x1, x3, #1, #0x1f
    // 0xa96adc: cmp             x2, x1
    // 0xa96ae0: b.ne            #0xa96aec
    // 0xa96ae4: mov             x1, x0
    // 0xa96ae8: r0 = _growToNextCapacity()
    //     0xa96ae8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa96aec: ldur            x0, [fp, #-0x38]
    // 0xa96af0: ldur            x1, [fp, #-0x40]
    // 0xa96af4: add             x2, x1, #1
    // 0xa96af8: lsl             x3, x2, #1
    // 0xa96afc: StoreField: r0->field_b = r3
    //     0xa96afc: stur            w3, [x0, #0xb]
    // 0xa96b00: LoadField: r2 = r0->field_f
    //     0xa96b00: ldur            w2, [x0, #0xf]
    // 0xa96b04: DecompressPointer r2
    //     0xa96b04: add             x2, x2, HEAP, lsl #32
    // 0xa96b08: add             x3, x2, x1, lsl #2
    // 0xa96b0c: r16 = Instance_Icon
    //     0xa96b0c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa48] Obj!Icon@d65eb1
    //     0xa96b10: ldr             x16, [x16, #0xa48]
    // 0xa96b14: StoreField: r3->field_f = r16
    //     0xa96b14: stur            w16, [x3, #0xf]
    // 0xa96b18: b               #0xa96b20
    // 0xa96b1c: ldur            x0, [fp, #-0x38]
    // 0xa96b20: LoadField: r1 = r0->field_b
    //     0xa96b20: ldur            w1, [x0, #0xb]
    // 0xa96b24: LoadField: r2 = r0->field_f
    //     0xa96b24: ldur            w2, [x0, #0xf]
    // 0xa96b28: DecompressPointer r2
    //     0xa96b28: add             x2, x2, HEAP, lsl #32
    // 0xa96b2c: LoadField: r3 = r2->field_b
    //     0xa96b2c: ldur            w3, [x2, #0xb]
    // 0xa96b30: r2 = LoadInt32Instr(r1)
    //     0xa96b30: sbfx            x2, x1, #1, #0x1f
    // 0xa96b34: stur            x2, [fp, #-0x40]
    // 0xa96b38: r1 = LoadInt32Instr(r3)
    //     0xa96b38: sbfx            x1, x3, #1, #0x1f
    // 0xa96b3c: cmp             x2, x1
    // 0xa96b40: b.ne            #0xa96b4c
    // 0xa96b44: mov             x1, x0
    // 0xa96b48: r0 = _growToNextCapacity()
    //     0xa96b48: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa96b4c: ldur            x2, [fp, #-0x28]
    // 0xa96b50: ldur            x0, [fp, #-0x38]
    // 0xa96b54: ldur            x1, [fp, #-0x40]
    // 0xa96b58: add             x3, x1, #1
    // 0xa96b5c: lsl             x4, x3, #1
    // 0xa96b60: StoreField: r0->field_b = r4
    //     0xa96b60: stur            w4, [x0, #0xb]
    // 0xa96b64: LoadField: r3 = r0->field_f
    //     0xa96b64: ldur            w3, [x0, #0xf]
    // 0xa96b68: DecompressPointer r3
    //     0xa96b68: add             x3, x3, HEAP, lsl #32
    // 0xa96b6c: add             x4, x3, x1, lsl #2
    // 0xa96b70: r16 = Instance_SizedBox
    //     0xa96b70: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xa96b74: ldr             x16, [x16, #0xa50]
    // 0xa96b78: StoreField: r4->field_f = r16
    //     0xa96b78: stur            w16, [x4, #0xf]
    // 0xa96b7c: ldur            x1, [fp, #-8]
    // 0xa96b80: r0 = controller()
    //     0xa96b80: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa96b84: LoadField: r1 = r0->field_93
    //     0xa96b84: ldur            w1, [x0, #0x93]
    // 0xa96b88: DecompressPointer r1
    //     0xa96b88: add             x1, x1, HEAP, lsl #32
    // 0xa96b8c: ldur            x0, [fp, #-0x28]
    // 0xa96b90: LoadField: r2 = r0->field_13
    //     0xa96b90: ldur            w2, [x0, #0x13]
    // 0xa96b94: DecompressPointer r2
    //     0xa96b94: add             x2, x2, HEAP, lsl #32
    // 0xa96b98: r0 = []()
    //     0xa96b98: bl              #0x1653f0c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::[]
    // 0xa96b9c: r1 = 60
    //     0xa96b9c: movz            x1, #0x3c
    // 0xa96ba0: branchIfSmi(r0, 0xa96bac)
    //     0xa96ba0: tbz             w0, #0, #0xa96bac
    // 0xa96ba4: r1 = LoadClassIdInstr(r0)
    //     0xa96ba4: ldur            x1, [x0, #-1]
    //     0xa96ba8: ubfx            x1, x1, #0xc, #0x14
    // 0xa96bac: r16 = true
    //     0xa96bac: add             x16, NULL, #0x20  ; true
    // 0xa96bb0: stp             x16, x0, [SP]
    // 0xa96bb4: mov             x0, x1
    // 0xa96bb8: mov             lr, x0
    // 0xa96bbc: ldr             lr, [x21, lr, lsl #3]
    // 0xa96bc0: blr             lr
    // 0xa96bc4: tbnz            w0, #4, #0xa96bd4
    // 0xa96bc8: r0 = "Flagged"
    //     0xa96bc8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa58] "Flagged"
    //     0xa96bcc: ldr             x0, [x0, #0xa58]
    // 0xa96bd0: b               #0xa96bdc
    // 0xa96bd4: r0 = "Flag as abusive"
    //     0xa96bd4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa60] "Flag as abusive"
    //     0xa96bd8: ldr             x0, [x0, #0xa60]
    // 0xa96bdc: ldur            x1, [fp, #-0x10]
    // 0xa96be0: stur            x0, [fp, #-8]
    // 0xa96be4: r0 = of()
    //     0xa96be4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa96be8: LoadField: r1 = r0->field_87
    //     0xa96be8: ldur            w1, [x0, #0x87]
    // 0xa96bec: DecompressPointer r1
    //     0xa96bec: add             x1, x1, HEAP, lsl #32
    // 0xa96bf0: LoadField: r0 = r1->field_33
    //     0xa96bf0: ldur            w0, [x1, #0x33]
    // 0xa96bf4: DecompressPointer r0
    //     0xa96bf4: add             x0, x0, HEAP, lsl #32
    // 0xa96bf8: cmp             w0, NULL
    // 0xa96bfc: b.ne            #0xa96c08
    // 0xa96c00: r2 = Null
    //     0xa96c00: mov             x2, NULL
    // 0xa96c04: b               #0xa96c2c
    // 0xa96c08: r16 = 12.000000
    //     0xa96c08: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa96c0c: ldr             x16, [x16, #0x9e8]
    // 0xa96c10: r30 = Instance_Color
    //     0xa96c10: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa96c14: stp             lr, x16, [SP]
    // 0xa96c18: mov             x1, x0
    // 0xa96c1c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa96c1c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa96c20: ldr             x4, [x4, #0xaa0]
    // 0xa96c24: r0 = copyWith()
    //     0xa96c24: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa96c28: mov             x2, x0
    // 0xa96c2c: ldur            x1, [fp, #-0x38]
    // 0xa96c30: ldur            x0, [fp, #-8]
    // 0xa96c34: stur            x2, [fp, #-0x48]
    // 0xa96c38: r0 = Text()
    //     0xa96c38: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa96c3c: mov             x2, x0
    // 0xa96c40: ldur            x0, [fp, #-8]
    // 0xa96c44: stur            x2, [fp, #-0x50]
    // 0xa96c48: StoreField: r2->field_b = r0
    //     0xa96c48: stur            w0, [x2, #0xb]
    // 0xa96c4c: ldur            x0, [fp, #-0x48]
    // 0xa96c50: StoreField: r2->field_13 = r0
    //     0xa96c50: stur            w0, [x2, #0x13]
    // 0xa96c54: ldur            x0, [fp, #-0x38]
    // 0xa96c58: LoadField: r1 = r0->field_b
    //     0xa96c58: ldur            w1, [x0, #0xb]
    // 0xa96c5c: LoadField: r3 = r0->field_f
    //     0xa96c5c: ldur            w3, [x0, #0xf]
    // 0xa96c60: DecompressPointer r3
    //     0xa96c60: add             x3, x3, HEAP, lsl #32
    // 0xa96c64: LoadField: r4 = r3->field_b
    //     0xa96c64: ldur            w4, [x3, #0xb]
    // 0xa96c68: r3 = LoadInt32Instr(r1)
    //     0xa96c68: sbfx            x3, x1, #1, #0x1f
    // 0xa96c6c: stur            x3, [fp, #-0x40]
    // 0xa96c70: r1 = LoadInt32Instr(r4)
    //     0xa96c70: sbfx            x1, x4, #1, #0x1f
    // 0xa96c74: cmp             x3, x1
    // 0xa96c78: b.ne            #0xa96c84
    // 0xa96c7c: mov             x1, x0
    // 0xa96c80: r0 = _growToNextCapacity()
    //     0xa96c80: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa96c84: ldur            x4, [fp, #-0x30]
    // 0xa96c88: ldur            x2, [fp, #-0x38]
    // 0xa96c8c: ldur            x3, [fp, #-0x40]
    // 0xa96c90: add             x0, x3, #1
    // 0xa96c94: lsl             x1, x0, #1
    // 0xa96c98: StoreField: r2->field_b = r1
    //     0xa96c98: stur            w1, [x2, #0xb]
    // 0xa96c9c: LoadField: r1 = r2->field_f
    //     0xa96c9c: ldur            w1, [x2, #0xf]
    // 0xa96ca0: DecompressPointer r1
    //     0xa96ca0: add             x1, x1, HEAP, lsl #32
    // 0xa96ca4: ldur            x0, [fp, #-0x50]
    // 0xa96ca8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa96ca8: add             x25, x1, x3, lsl #2
    //     0xa96cac: add             x25, x25, #0xf
    //     0xa96cb0: str             w0, [x25]
    //     0xa96cb4: tbz             w0, #0, #0xa96cd0
    //     0xa96cb8: ldurb           w16, [x1, #-1]
    //     0xa96cbc: ldurb           w17, [x0, #-1]
    //     0xa96cc0: and             x16, x17, x16, lsr #2
    //     0xa96cc4: tst             x16, HEAP, lsr #32
    //     0xa96cc8: b.eq            #0xa96cd0
    //     0xa96ccc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa96cd0: r0 = Row()
    //     0xa96cd0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa96cd4: mov             x2, x0
    // 0xa96cd8: r0 = Instance_Axis
    //     0xa96cd8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa96cdc: stur            x2, [fp, #-8]
    // 0xa96ce0: StoreField: r2->field_f = r0
    //     0xa96ce0: stur            w0, [x2, #0xf]
    // 0xa96ce4: r0 = Instance_MainAxisAlignment
    //     0xa96ce4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa96ce8: ldr             x0, [x0, #0xa08]
    // 0xa96cec: StoreField: r2->field_13 = r0
    //     0xa96cec: stur            w0, [x2, #0x13]
    // 0xa96cf0: r0 = Instance_MainAxisSize
    //     0xa96cf0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa96cf4: ldr             x0, [x0, #0xa10]
    // 0xa96cf8: ArrayStore: r2[0] = r0  ; List_4
    //     0xa96cf8: stur            w0, [x2, #0x17]
    // 0xa96cfc: r0 = Instance_CrossAxisAlignment
    //     0xa96cfc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa96d00: ldr             x0, [x0, #0xa18]
    // 0xa96d04: StoreField: r2->field_1b = r0
    //     0xa96d04: stur            w0, [x2, #0x1b]
    // 0xa96d08: r0 = Instance_VerticalDirection
    //     0xa96d08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa96d0c: ldr             x0, [x0, #0xa20]
    // 0xa96d10: StoreField: r2->field_23 = r0
    //     0xa96d10: stur            w0, [x2, #0x23]
    // 0xa96d14: r0 = Instance_Clip
    //     0xa96d14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa96d18: ldr             x0, [x0, #0x38]
    // 0xa96d1c: StoreField: r2->field_2b = r0
    //     0xa96d1c: stur            w0, [x2, #0x2b]
    // 0xa96d20: StoreField: r2->field_2f = rZR
    //     0xa96d20: stur            xzr, [x2, #0x2f]
    // 0xa96d24: ldur            x0, [fp, #-0x38]
    // 0xa96d28: StoreField: r2->field_b = r0
    //     0xa96d28: stur            w0, [x2, #0xb]
    // 0xa96d2c: r1 = <String>
    //     0xa96d2c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xa96d30: r0 = PopupMenuItem()
    //     0xa96d30: bl              #0x9abca4  ; AllocatePopupMenuItemStub -> PopupMenuItem<X0> (size=0x38)
    // 0xa96d34: mov             x3, x0
    // 0xa96d38: r0 = "flag"
    //     0xa96d38: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xa96d3c: ldr             x0, [x0, #0xa68]
    // 0xa96d40: stur            x3, [fp, #-0x38]
    // 0xa96d44: StoreField: r3->field_f = r0
    //     0xa96d44: stur            w0, [x3, #0xf]
    // 0xa96d48: ldur            x0, [fp, #-0x30]
    // 0xa96d4c: StoreField: r3->field_13 = r0
    //     0xa96d4c: stur            w0, [x3, #0x13]
    // 0xa96d50: r0 = true
    //     0xa96d50: add             x0, NULL, #0x20  ; true
    // 0xa96d54: ArrayStore: r3[0] = r0  ; List_4
    //     0xa96d54: stur            w0, [x3, #0x17]
    // 0xa96d58: d0 = 25.000000
    //     0xa96d58: fmov            d0, #25.00000000
    // 0xa96d5c: StoreField: r3->field_1b = d0
    //     0xa96d5c: stur            d0, [x3, #0x1b]
    // 0xa96d60: ldur            x0, [fp, #-8]
    // 0xa96d64: StoreField: r3->field_33 = r0
    //     0xa96d64: stur            w0, [x3, #0x33]
    // 0xa96d68: r1 = Null
    //     0xa96d68: mov             x1, NULL
    // 0xa96d6c: r2 = 2
    //     0xa96d6c: movz            x2, #0x2
    // 0xa96d70: r0 = AllocateArray()
    //     0xa96d70: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa96d74: mov             x2, x0
    // 0xa96d78: ldur            x0, [fp, #-0x38]
    // 0xa96d7c: stur            x2, [fp, #-8]
    // 0xa96d80: StoreField: r2->field_f = r0
    //     0xa96d80: stur            w0, [x2, #0xf]
    // 0xa96d84: r1 = <PopupMenuEntry<String>>
    //     0xa96d84: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa70] TypeArguments: <PopupMenuEntry<String>>
    //     0xa96d88: ldr             x1, [x1, #0xa70]
    // 0xa96d8c: r0 = AllocateGrowableArray()
    //     0xa96d8c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa96d90: mov             x1, x0
    // 0xa96d94: ldur            x0, [fp, #-8]
    // 0xa96d98: StoreField: r1->field_f = r0
    //     0xa96d98: stur            w0, [x1, #0xf]
    // 0xa96d9c: r0 = 2
    //     0xa96d9c: movz            x0, #0x2
    // 0xa96da0: StoreField: r1->field_b = r0
    //     0xa96da0: stur            w0, [x1, #0xb]
    // 0xa96da4: r16 = <String>
    //     0xa96da4: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xa96da8: ldur            lr, [fp, #-0x10]
    // 0xa96dac: stp             lr, x16, [SP, #0x20]
    // 0xa96db0: ldur            x16, [fp, #-0x18]
    // 0xa96db4: stp             x16, x1, [SP, #0x10]
    // 0xa96db8: r16 = Instance_Color
    //     0xa96db8: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa96dbc: ldur            lr, [fp, #-0x20]
    // 0xa96dc0: stp             lr, x16, [SP]
    // 0xa96dc4: r4 = const [0x1, 0x5, 0x5, 0x3, color, 0x3, constraints, 0x4, null]
    //     0xa96dc4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa78] List(9) [0x1, 0x5, 0x5, 0x3, "color", 0x3, "constraints", 0x4, Null]
    //     0xa96dc8: ldr             x4, [x4, #0xa78]
    // 0xa96dcc: r0 = showMenu()
    //     0xa96dcc: bl              #0x9ab6c4  ; [package:flutter/src/material/popup_menu.dart] ::showMenu
    // 0xa96dd0: ldur            x2, [fp, #-0x28]
    // 0xa96dd4: r1 = Function '<anonymous closure>':.
    //     0xa96dd4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eaa0] AnonymousClosure: (0x9b31c0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::showMenuItem (0x9b2c74)
    //     0xa96dd8: ldr             x1, [x1, #0xaa0]
    // 0xa96ddc: stur            x0, [fp, #-8]
    // 0xa96de0: r0 = AllocateClosure()
    //     0xa96de0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa96de4: r16 = <Null?>
    //     0xa96de4: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xa96de8: ldur            lr, [fp, #-8]
    // 0xa96dec: stp             lr, x16, [SP, #8]
    // 0xa96df0: str             x0, [SP]
    // 0xa96df4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa96df4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa96df8: r0 = then()
    //     0xa96df8: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xa96dfc: r0 = Null
    //     0xa96dfc: mov             x0, NULL
    // 0xa96e00: LeaveFrame
    //     0xa96e00: mov             SP, fp
    //     0xa96e04: ldp             fp, lr, [SP], #0x10
    // 0xa96e08: ret
    //     0xa96e08: ret             
    // 0xa96e0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa96e0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa96e10: b               #0xa968f8
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa96e14, size: 0x3cc
    // 0xa96e14: EnterFrame
    //     0xa96e14: stp             fp, lr, [SP, #-0x10]!
    //     0xa96e18: mov             fp, SP
    // 0xa96e1c: AllocStack(0x60)
    //     0xa96e1c: sub             SP, SP, #0x60
    // 0xa96e20: SetupParameters()
    //     0xa96e20: ldr             x0, [fp, #0x20]
    //     0xa96e24: ldur            w1, [x0, #0x17]
    //     0xa96e28: add             x1, x1, HEAP, lsl #32
    //     0xa96e2c: stur            x1, [fp, #-8]
    // 0xa96e30: CheckStackOverflow
    //     0xa96e30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa96e34: cmp             SP, x16
    //     0xa96e38: b.ls            #0xa971c8
    // 0xa96e3c: r1 = 3
    //     0xa96e3c: movz            x1, #0x3
    // 0xa96e40: r0 = AllocateContext()
    //     0xa96e40: bl              #0x16f6108  ; AllocateContextStub
    // 0xa96e44: mov             x2, x0
    // 0xa96e48: ldur            x0, [fp, #-8]
    // 0xa96e4c: stur            x2, [fp, #-0x18]
    // 0xa96e50: StoreField: r2->field_b = r0
    //     0xa96e50: stur            w0, [x2, #0xb]
    // 0xa96e54: ldr             x1, [fp, #0x18]
    // 0xa96e58: StoreField: r2->field_f = r1
    //     0xa96e58: stur            w1, [x2, #0xf]
    // 0xa96e5c: ldr             x1, [fp, #0x10]
    // 0xa96e60: StoreField: r2->field_13 = r1
    //     0xa96e60: stur            w1, [x2, #0x13]
    // 0xa96e64: LoadField: r3 = r0->field_b
    //     0xa96e64: ldur            w3, [x0, #0xb]
    // 0xa96e68: DecompressPointer r3
    //     0xa96e68: add             x3, x3, HEAP, lsl #32
    // 0xa96e6c: stur            x3, [fp, #-0x10]
    // 0xa96e70: LoadField: r1 = r3->field_f
    //     0xa96e70: ldur            w1, [x3, #0xf]
    // 0xa96e74: DecompressPointer r1
    //     0xa96e74: add             x1, x1, HEAP, lsl #32
    // 0xa96e78: r0 = controller()
    //     0xa96e78: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa96e7c: LoadField: r1 = r0->field_77
    //     0xa96e7c: ldur            w1, [x0, #0x77]
    // 0xa96e80: DecompressPointer r1
    //     0xa96e80: add             x1, x1, HEAP, lsl #32
    // 0xa96e84: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa96e84: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa96e88: r0 = toList()
    //     0xa96e88: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa96e8c: mov             x3, x0
    // 0xa96e90: ldur            x2, [fp, #-8]
    // 0xa96e94: LoadField: r0 = r2->field_13
    //     0xa96e94: ldur            w0, [x2, #0x13]
    // 0xa96e98: DecompressPointer r0
    //     0xa96e98: add             x0, x0, HEAP, lsl #32
    // 0xa96e9c: LoadField: r1 = r3->field_b
    //     0xa96e9c: ldur            w1, [x3, #0xb]
    // 0xa96ea0: r4 = LoadInt32Instr(r0)
    //     0xa96ea0: sbfx            x4, x0, #1, #0x1f
    //     0xa96ea4: tbz             w0, #0, #0xa96eac
    //     0xa96ea8: ldur            x4, [x0, #7]
    // 0xa96eac: r0 = LoadInt32Instr(r1)
    //     0xa96eac: sbfx            x0, x1, #1, #0x1f
    // 0xa96eb0: mov             x1, x4
    // 0xa96eb4: cmp             x1, x0
    // 0xa96eb8: b.hs            #0xa971d0
    // 0xa96ebc: LoadField: r0 = r3->field_f
    //     0xa96ebc: ldur            w0, [x3, #0xf]
    // 0xa96ec0: DecompressPointer r0
    //     0xa96ec0: add             x0, x0, HEAP, lsl #32
    // 0xa96ec4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa96ec4: add             x16, x0, x4, lsl #2
    //     0xa96ec8: ldur            w1, [x16, #0xf]
    // 0xa96ecc: DecompressPointer r1
    //     0xa96ecc: add             x1, x1, HEAP, lsl #32
    // 0xa96ed0: cmp             w1, NULL
    // 0xa96ed4: b.ne            #0xa96ee4
    // 0xa96ed8: ldur            x3, [fp, #-0x18]
    // 0xa96edc: r4 = Null
    //     0xa96edc: mov             x4, NULL
    // 0xa96ee0: b               #0xa96f38
    // 0xa96ee4: ldur            x3, [fp, #-0x18]
    // 0xa96ee8: LoadField: r4 = r1->field_1b
    //     0xa96ee8: ldur            w4, [x1, #0x1b]
    // 0xa96eec: DecompressPointer r4
    //     0xa96eec: add             x4, x4, HEAP, lsl #32
    // 0xa96ef0: LoadField: r0 = r3->field_13
    //     0xa96ef0: ldur            w0, [x3, #0x13]
    // 0xa96ef4: DecompressPointer r0
    //     0xa96ef4: add             x0, x0, HEAP, lsl #32
    // 0xa96ef8: LoadField: r1 = r4->field_b
    //     0xa96ef8: ldur            w1, [x4, #0xb]
    // 0xa96efc: r5 = LoadInt32Instr(r0)
    //     0xa96efc: sbfx            x5, x0, #1, #0x1f
    //     0xa96f00: tbz             w0, #0, #0xa96f08
    //     0xa96f04: ldur            x5, [x0, #7]
    // 0xa96f08: r0 = LoadInt32Instr(r1)
    //     0xa96f08: sbfx            x0, x1, #1, #0x1f
    // 0xa96f0c: mov             x1, x5
    // 0xa96f10: cmp             x1, x0
    // 0xa96f14: b.hs            #0xa971d4
    // 0xa96f18: LoadField: r0 = r4->field_f
    //     0xa96f18: ldur            w0, [x4, #0xf]
    // 0xa96f1c: DecompressPointer r0
    //     0xa96f1c: add             x0, x0, HEAP, lsl #32
    // 0xa96f20: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa96f20: add             x16, x0, x5, lsl #2
    //     0xa96f24: ldur            w1, [x16, #0xf]
    // 0xa96f28: DecompressPointer r1
    //     0xa96f28: add             x1, x1, HEAP, lsl #32
    // 0xa96f2c: LoadField: r0 = r1->field_13
    //     0xa96f2c: ldur            w0, [x1, #0x13]
    // 0xa96f30: DecompressPointer r0
    //     0xa96f30: add             x0, x0, HEAP, lsl #32
    // 0xa96f34: mov             x4, x0
    // 0xa96f38: ldur            x1, [fp, #-0x10]
    // 0xa96f3c: mov             x0, x4
    // 0xa96f40: stur            x4, [fp, #-0x20]
    // 0xa96f44: ArrayStore: r3[0] = r0  ; List_4
    //     0xa96f44: stur            w0, [x3, #0x17]
    //     0xa96f48: ldurb           w16, [x3, #-1]
    //     0xa96f4c: ldurb           w17, [x0, #-1]
    //     0xa96f50: and             x16, x17, x16, lsr #2
    //     0xa96f54: tst             x16, HEAP, lsr #32
    //     0xa96f58: b.eq            #0xa96f60
    //     0xa96f5c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xa96f60: LoadField: r0 = r1->field_f
    //     0xa96f60: ldur            w0, [x1, #0xf]
    // 0xa96f64: DecompressPointer r0
    //     0xa96f64: add             x0, x0, HEAP, lsl #32
    // 0xa96f68: mov             x1, x0
    // 0xa96f6c: r0 = controller()
    //     0xa96f6c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa96f70: LoadField: r1 = r0->field_77
    //     0xa96f70: ldur            w1, [x0, #0x77]
    // 0xa96f74: DecompressPointer r1
    //     0xa96f74: add             x1, x1, HEAP, lsl #32
    // 0xa96f78: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa96f78: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa96f7c: r0 = toList()
    //     0xa96f7c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa96f80: mov             x2, x0
    // 0xa96f84: ldur            x0, [fp, #-8]
    // 0xa96f88: LoadField: r1 = r0->field_13
    //     0xa96f88: ldur            w1, [x0, #0x13]
    // 0xa96f8c: DecompressPointer r1
    //     0xa96f8c: add             x1, x1, HEAP, lsl #32
    // 0xa96f90: LoadField: r0 = r2->field_b
    //     0xa96f90: ldur            w0, [x2, #0xb]
    // 0xa96f94: r3 = LoadInt32Instr(r1)
    //     0xa96f94: sbfx            x3, x1, #1, #0x1f
    //     0xa96f98: tbz             w1, #0, #0xa96fa0
    //     0xa96f9c: ldur            x3, [x1, #7]
    // 0xa96fa0: r1 = LoadInt32Instr(r0)
    //     0xa96fa0: sbfx            x1, x0, #1, #0x1f
    // 0xa96fa4: mov             x0, x1
    // 0xa96fa8: mov             x1, x3
    // 0xa96fac: cmp             x1, x0
    // 0xa96fb0: b.hs            #0xa971d8
    // 0xa96fb4: LoadField: r0 = r2->field_f
    //     0xa96fb4: ldur            w0, [x2, #0xf]
    // 0xa96fb8: DecompressPointer r0
    //     0xa96fb8: add             x0, x0, HEAP, lsl #32
    // 0xa96fbc: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xa96fbc: add             x16, x0, x3, lsl #2
    //     0xa96fc0: ldur            w1, [x16, #0xf]
    // 0xa96fc4: DecompressPointer r1
    //     0xa96fc4: add             x1, x1, HEAP, lsl #32
    // 0xa96fc8: cmp             w1, NULL
    // 0xa96fcc: b.ne            #0xa96fdc
    // 0xa96fd0: ldur            x2, [fp, #-0x18]
    // 0xa96fd4: r0 = Null
    //     0xa96fd4: mov             x0, NULL
    // 0xa96fd8: b               #0xa9702c
    // 0xa96fdc: ldur            x2, [fp, #-0x18]
    // 0xa96fe0: LoadField: r3 = r1->field_1b
    //     0xa96fe0: ldur            w3, [x1, #0x1b]
    // 0xa96fe4: DecompressPointer r3
    //     0xa96fe4: add             x3, x3, HEAP, lsl #32
    // 0xa96fe8: LoadField: r0 = r2->field_13
    //     0xa96fe8: ldur            w0, [x2, #0x13]
    // 0xa96fec: DecompressPointer r0
    //     0xa96fec: add             x0, x0, HEAP, lsl #32
    // 0xa96ff0: LoadField: r1 = r3->field_b
    //     0xa96ff0: ldur            w1, [x3, #0xb]
    // 0xa96ff4: r4 = LoadInt32Instr(r0)
    //     0xa96ff4: sbfx            x4, x0, #1, #0x1f
    //     0xa96ff8: tbz             w0, #0, #0xa97000
    //     0xa96ffc: ldur            x4, [x0, #7]
    // 0xa97000: r0 = LoadInt32Instr(r1)
    //     0xa97000: sbfx            x0, x1, #1, #0x1f
    // 0xa97004: mov             x1, x4
    // 0xa97008: cmp             x1, x0
    // 0xa9700c: b.hs            #0xa971dc
    // 0xa97010: LoadField: r0 = r3->field_f
    //     0xa97010: ldur            w0, [x3, #0xf]
    // 0xa97014: DecompressPointer r0
    //     0xa97014: add             x0, x0, HEAP, lsl #32
    // 0xa97018: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa97018: add             x16, x0, x4, lsl #2
    //     0xa9701c: ldur            w1, [x16, #0xf]
    // 0xa97020: DecompressPointer r1
    //     0xa97020: add             x1, x1, HEAP, lsl #32
    // 0xa97024: LoadField: r0 = r1->field_f
    //     0xa97024: ldur            w0, [x1, #0xf]
    // 0xa97028: DecompressPointer r0
    //     0xa97028: add             x0, x0, HEAP, lsl #32
    // 0xa9702c: r1 = LoadClassIdInstr(r0)
    //     0xa9702c: ldur            x1, [x0, #-1]
    //     0xa97030: ubfx            x1, x1, #0xc, #0x14
    // 0xa97034: r16 = "image"
    //     0xa97034: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0xa97038: stp             x16, x0, [SP]
    // 0xa9703c: mov             x0, x1
    // 0xa97040: mov             lr, x0
    // 0xa97044: ldr             lr, [x21, lr, lsl #3]
    // 0xa97048: blr             lr
    // 0xa9704c: tbnz            w0, #4, #0xa97138
    // 0xa97050: ldur            x0, [fp, #-0x20]
    // 0xa97054: r0 = Radius()
    //     0xa97054: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa97058: d0 = 12.000000
    //     0xa97058: fmov            d0, #12.00000000
    // 0xa9705c: stur            x0, [fp, #-8]
    // 0xa97060: StoreField: r0->field_7 = d0
    //     0xa97060: stur            d0, [x0, #7]
    // 0xa97064: StoreField: r0->field_f = d0
    //     0xa97064: stur            d0, [x0, #0xf]
    // 0xa97068: r0 = BorderRadius()
    //     0xa97068: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa9706c: mov             x3, x0
    // 0xa97070: ldur            x0, [fp, #-8]
    // 0xa97074: stur            x3, [fp, #-0x10]
    // 0xa97078: StoreField: r3->field_7 = r0
    //     0xa97078: stur            w0, [x3, #7]
    // 0xa9707c: StoreField: r3->field_b = r0
    //     0xa9707c: stur            w0, [x3, #0xb]
    // 0xa97080: StoreField: r3->field_f = r0
    //     0xa97080: stur            w0, [x3, #0xf]
    // 0xa97084: StoreField: r3->field_13 = r0
    //     0xa97084: stur            w0, [x3, #0x13]
    // 0xa97088: ldur            x0, [fp, #-0x20]
    // 0xa9708c: cmp             w0, NULL
    // 0xa97090: b.ne            #0xa97098
    // 0xa97094: r0 = ""
    //     0xa97094: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa97098: stur            x0, [fp, #-8]
    // 0xa9709c: r1 = Function '<anonymous closure>':.
    //     0xa9709c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eaa8] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa970a0: ldr             x1, [x1, #0xaa8]
    // 0xa970a4: r2 = Null
    //     0xa970a4: mov             x2, NULL
    // 0xa970a8: r0 = AllocateClosure()
    //     0xa970a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa970ac: r1 = Function '<anonymous closure>':.
    //     0xa970ac: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eab0] AnonymousClosure: (0x9b17ac), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa970b0: ldr             x1, [x1, #0xab0]
    // 0xa970b4: r2 = Null
    //     0xa970b4: mov             x2, NULL
    // 0xa970b8: stur            x0, [fp, #-0x28]
    // 0xa970bc: r0 = AllocateClosure()
    //     0xa970bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa970c0: stur            x0, [fp, #-0x30]
    // 0xa970c4: r0 = CachedNetworkImage()
    //     0xa970c4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa970c8: stur            x0, [fp, #-0x38]
    // 0xa970cc: r16 = Instance_BoxFit
    //     0xa970cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xa970d0: ldr             x16, [x16, #0x118]
    // 0xa970d4: r30 = 48.000000
    //     0xa970d4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xa970d8: ldr             lr, [lr, #0xad8]
    // 0xa970dc: stp             lr, x16, [SP, #0x18]
    // 0xa970e0: r16 = 48.000000
    //     0xa970e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xa970e4: ldr             x16, [x16, #0xad8]
    // 0xa970e8: ldur            lr, [fp, #-0x28]
    // 0xa970ec: stp             lr, x16, [SP, #8]
    // 0xa970f0: ldur            x16, [fp, #-0x30]
    // 0xa970f4: str             x16, [SP]
    // 0xa970f8: mov             x1, x0
    // 0xa970fc: ldur            x2, [fp, #-8]
    // 0xa97100: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x2, height, 0x4, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xa97100: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fae0] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x2, "height", 0x4, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xa97104: ldr             x4, [x4, #0xae0]
    // 0xa97108: r0 = CachedNetworkImage()
    //     0xa97108: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa9710c: r0 = ClipRRect()
    //     0xa9710c: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xa97110: mov             x1, x0
    // 0xa97114: ldur            x0, [fp, #-0x10]
    // 0xa97118: StoreField: r1->field_f = r0
    //     0xa97118: stur            w0, [x1, #0xf]
    // 0xa9711c: r0 = Instance_Clip
    //     0xa9711c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xa97120: ldr             x0, [x0, #0x138]
    // 0xa97124: ArrayStore: r1[0] = r0  ; List_4
    //     0xa97124: stur            w0, [x1, #0x17]
    // 0xa97128: ldur            x0, [fp, #-0x38]
    // 0xa9712c: StoreField: r1->field_b = r0
    //     0xa9712c: stur            w0, [x1, #0xb]
    // 0xa97130: mov             x0, x1
    // 0xa97134: b               #0xa97184
    // 0xa97138: ldur            x0, [fp, #-0x20]
    // 0xa9713c: cmp             w0, NULL
    // 0xa97140: b.ne            #0xa97148
    // 0xa97144: r0 = ""
    //     0xa97144: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa97148: stur            x0, [fp, #-8]
    // 0xa9714c: r0 = VideoPlayerWidget()
    //     0xa9714c: bl              #0xa971e0  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0xa97150: mov             x1, x0
    // 0xa97154: ldur            x0, [fp, #-8]
    // 0xa97158: stur            x1, [fp, #-0x10]
    // 0xa9715c: StoreField: r1->field_b = r0
    //     0xa9715c: stur            w0, [x1, #0xb]
    // 0xa97160: r0 = SizedBox()
    //     0xa97160: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa97164: mov             x1, x0
    // 0xa97168: r0 = 48.000000
    //     0xa97168: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xa9716c: ldr             x0, [x0, #0xad8]
    // 0xa97170: StoreField: r1->field_f = r0
    //     0xa97170: stur            w0, [x1, #0xf]
    // 0xa97174: StoreField: r1->field_13 = r0
    //     0xa97174: stur            w0, [x1, #0x13]
    // 0xa97178: ldur            x0, [fp, #-0x10]
    // 0xa9717c: StoreField: r1->field_b = r0
    //     0xa9717c: stur            w0, [x1, #0xb]
    // 0xa97180: mov             x0, x1
    // 0xa97184: stur            x0, [fp, #-8]
    // 0xa97188: r0 = GestureDetector()
    //     0xa97188: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa9718c: ldur            x2, [fp, #-0x18]
    // 0xa97190: r1 = Function '<anonymous closure>':.
    //     0xa97190: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eab8] AnonymousClosure: (0xa971ec), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14fc2bc)
    //     0xa97194: ldr             x1, [x1, #0xab8]
    // 0xa97198: stur            x0, [fp, #-0x10]
    // 0xa9719c: r0 = AllocateClosure()
    //     0xa9719c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa971a0: ldur            x16, [fp, #-8]
    // 0xa971a4: stp             x16, x0, [SP]
    // 0xa971a8: ldur            x1, [fp, #-0x10]
    // 0xa971ac: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xa971ac: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xa971b0: ldr             x4, [x4, #0xaf0]
    // 0xa971b4: r0 = GestureDetector()
    //     0xa971b4: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa971b8: ldur            x0, [fp, #-0x10]
    // 0xa971bc: LeaveFrame
    //     0xa971bc: mov             SP, fp
    //     0xa971c0: ldp             fp, lr, [SP], #0x10
    // 0xa971c4: ret
    //     0xa971c4: ret             
    // 0xa971c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa971c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa971cc: b               #0xa96e3c
    // 0xa971d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa971d0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa971d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa971d4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa971d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa971d8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa971dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa971dc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa971ec, size: 0xf4
    // 0xa971ec: EnterFrame
    //     0xa971ec: stp             fp, lr, [SP, #-0x10]!
    //     0xa971f0: mov             fp, SP
    // 0xa971f4: AllocStack(0x28)
    //     0xa971f4: sub             SP, SP, #0x28
    // 0xa971f8: SetupParameters()
    //     0xa971f8: ldr             x0, [fp, #0x10]
    //     0xa971fc: ldur            w2, [x0, #0x17]
    //     0xa97200: add             x2, x2, HEAP, lsl #32
    //     0xa97204: stur            x2, [fp, #-8]
    // 0xa97208: CheckStackOverflow
    //     0xa97208: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9720c: cmp             SP, x16
    //     0xa97210: b.ls            #0xa972d8
    // 0xa97214: LoadField: r0 = r2->field_b
    //     0xa97214: ldur            w0, [x2, #0xb]
    // 0xa97218: DecompressPointer r0
    //     0xa97218: add             x0, x0, HEAP, lsl #32
    // 0xa9721c: LoadField: r1 = r0->field_b
    //     0xa9721c: ldur            w1, [x0, #0xb]
    // 0xa97220: DecompressPointer r1
    //     0xa97220: add             x1, x1, HEAP, lsl #32
    // 0xa97224: LoadField: r0 = r1->field_f
    //     0xa97224: ldur            w0, [x1, #0xf]
    // 0xa97228: DecompressPointer r0
    //     0xa97228: add             x0, x0, HEAP, lsl #32
    // 0xa9722c: mov             x1, x0
    // 0xa97230: r0 = controller()
    //     0xa97230: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa97234: mov             x1, x0
    // 0xa97238: ldur            x0, [fp, #-8]
    // 0xa9723c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xa9723c: ldur            w2, [x0, #0x17]
    // 0xa97240: DecompressPointer r2
    //     0xa97240: add             x2, x2, HEAP, lsl #32
    // 0xa97244: cmp             w2, NULL
    // 0xa97248: b.ne            #0xa97250
    // 0xa9724c: r2 = ""
    //     0xa9724c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa97250: str             x2, [SP]
    // 0xa97254: r2 = "single_media"
    //     0xa97254: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fab0] "single_media"
    //     0xa97258: ldr             x2, [x2, #0xab0]
    // 0xa9725c: r4 = const [0, 0x3, 0x1, 0x2, selectedOption, 0x2, null]
    //     0xa9725c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf8] List(7) [0, 0x3, 0x1, 0x2, "selectedOption", 0x2, Null]
    //     0xa97260: ldr             x4, [x4, #0xaf8]
    // 0xa97264: r0 = ratingReviewClickedEvent()
    //     0xa97264: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0xa97268: ldur            x2, [fp, #-8]
    // 0xa9726c: LoadField: r1 = r2->field_f
    //     0xa9726c: ldur            w1, [x2, #0xf]
    // 0xa97270: DecompressPointer r1
    //     0xa97270: add             x1, x1, HEAP, lsl #32
    // 0xa97274: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa97274: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa97278: r0 = of()
    //     0xa97278: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xa9727c: ldur            x2, [fp, #-8]
    // 0xa97280: r1 = Function '<anonymous closure>':.
    //     0xa97280: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eac0] AnonymousClosure: (0xa972e0), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14fc2bc)
    //     0xa97284: ldr             x1, [x1, #0xac0]
    // 0xa97288: stur            x0, [fp, #-8]
    // 0xa9728c: r0 = AllocateClosure()
    //     0xa9728c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa97290: r1 = Null
    //     0xa97290: mov             x1, NULL
    // 0xa97294: stur            x0, [fp, #-0x10]
    // 0xa97298: r0 = MaterialPageRoute()
    //     0xa97298: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xa9729c: mov             x1, x0
    // 0xa972a0: ldur            x2, [fp, #-0x10]
    // 0xa972a4: stur            x0, [fp, #-0x10]
    // 0xa972a8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa972a8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa972ac: r0 = MaterialPageRoute()
    //     0xa972ac: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xa972b0: ldur            x16, [fp, #-8]
    // 0xa972b4: stp             x16, NULL, [SP, #8]
    // 0xa972b8: ldur            x16, [fp, #-0x10]
    // 0xa972bc: str             x16, [SP]
    // 0xa972c0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa972c0: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa972c4: r0 = push()
    //     0xa972c4: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xa972c8: r0 = Null
    //     0xa972c8: mov             x0, NULL
    // 0xa972cc: LeaveFrame
    //     0xa972cc: mov             SP, fp
    //     0xa972d0: ldp             fp, lr, [SP], #0x10
    // 0xa972d4: ret
    //     0xa972d4: ret             
    // 0xa972d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa972d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa972dc: b               #0xa97214
  }
  [closure] RatingReviewOnTapImage <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xa972e0, size: 0x13c
    // 0xa972e0: EnterFrame
    //     0xa972e0: stp             fp, lr, [SP, #-0x10]!
    //     0xa972e4: mov             fp, SP
    // 0xa972e8: AllocStack(0x28)
    //     0xa972e8: sub             SP, SP, #0x28
    // 0xa972ec: SetupParameters()
    //     0xa972ec: ldr             x0, [fp, #0x18]
    //     0xa972f0: ldur            w2, [x0, #0x17]
    //     0xa972f4: add             x2, x2, HEAP, lsl #32
    //     0xa972f8: stur            x2, [fp, #-0x18]
    // 0xa972fc: CheckStackOverflow
    //     0xa972fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa97300: cmp             SP, x16
    //     0xa97304: b.ls            #0xa97410
    // 0xa97308: LoadField: r0 = r2->field_b
    //     0xa97308: ldur            w0, [x2, #0xb]
    // 0xa9730c: DecompressPointer r0
    //     0xa9730c: add             x0, x0, HEAP, lsl #32
    // 0xa97310: stur            x0, [fp, #-0x10]
    // 0xa97314: LoadField: r3 = r0->field_b
    //     0xa97314: ldur            w3, [x0, #0xb]
    // 0xa97318: DecompressPointer r3
    //     0xa97318: add             x3, x3, HEAP, lsl #32
    // 0xa9731c: stur            x3, [fp, #-8]
    // 0xa97320: LoadField: r1 = r3->field_f
    //     0xa97320: ldur            w1, [x3, #0xf]
    // 0xa97324: DecompressPointer r1
    //     0xa97324: add             x1, x1, HEAP, lsl #32
    // 0xa97328: r0 = controller()
    //     0xa97328: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa9732c: LoadField: r1 = r0->field_77
    //     0xa9732c: ldur            w1, [x0, #0x77]
    // 0xa97330: DecompressPointer r1
    //     0xa97330: add             x1, x1, HEAP, lsl #32
    // 0xa97334: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa97334: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa97338: r0 = toList()
    //     0xa97338: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0xa9733c: mov             x2, x0
    // 0xa97340: ldur            x0, [fp, #-0x10]
    // 0xa97344: LoadField: r1 = r0->field_13
    //     0xa97344: ldur            w1, [x0, #0x13]
    // 0xa97348: DecompressPointer r1
    //     0xa97348: add             x1, x1, HEAP, lsl #32
    // 0xa9734c: LoadField: r0 = r2->field_b
    //     0xa9734c: ldur            w0, [x2, #0xb]
    // 0xa97350: r3 = LoadInt32Instr(r1)
    //     0xa97350: sbfx            x3, x1, #1, #0x1f
    //     0xa97354: tbz             w1, #0, #0xa9735c
    //     0xa97358: ldur            x3, [x1, #7]
    // 0xa9735c: r1 = LoadInt32Instr(r0)
    //     0xa9735c: sbfx            x1, x0, #1, #0x1f
    // 0xa97360: mov             x0, x1
    // 0xa97364: mov             x1, x3
    // 0xa97368: cmp             x1, x0
    // 0xa9736c: b.hs            #0xa97418
    // 0xa97370: LoadField: r0 = r2->field_f
    //     0xa97370: ldur            w0, [x2, #0xf]
    // 0xa97374: DecompressPointer r0
    //     0xa97374: add             x0, x0, HEAP, lsl #32
    // 0xa97378: ArrayLoad: r2 = r0[r3]  ; Unknown_4
    //     0xa97378: add             x16, x0, x3, lsl #2
    //     0xa9737c: ldur            w2, [x16, #0xf]
    // 0xa97380: DecompressPointer r2
    //     0xa97380: add             x2, x2, HEAP, lsl #32
    // 0xa97384: ldur            x0, [fp, #-0x18]
    // 0xa97388: stur            x2, [fp, #-0x20]
    // 0xa9738c: LoadField: r3 = r0->field_13
    //     0xa9738c: ldur            w3, [x0, #0x13]
    // 0xa97390: DecompressPointer r3
    //     0xa97390: add             x3, x3, HEAP, lsl #32
    // 0xa97394: ldur            x1, [fp, #-8]
    // 0xa97398: stur            x3, [fp, #-0x10]
    // 0xa9739c: LoadField: r4 = r1->field_f
    //     0xa9739c: ldur            w4, [x1, #0xf]
    // 0xa973a0: DecompressPointer r4
    //     0xa973a0: add             x4, x4, HEAP, lsl #32
    // 0xa973a4: mov             x1, x4
    // 0xa973a8: r0 = controller()
    //     0xa973a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa973ac: LoadField: r1 = r0->field_97
    //     0xa973ac: ldur            w1, [x0, #0x97]
    // 0xa973b0: DecompressPointer r1
    //     0xa973b0: add             x1, x1, HEAP, lsl #32
    // 0xa973b4: stur            x1, [fp, #-8]
    // 0xa973b8: r0 = RatingReviewOnTapImage()
    //     0xa973b8: bl              #0xa9741c  ; AllocateRatingReviewOnTapImageStub -> RatingReviewOnTapImage (size=0x20)
    // 0xa973bc: mov             x3, x0
    // 0xa973c0: ldur            x0, [fp, #-0x20]
    // 0xa973c4: stur            x3, [fp, #-0x28]
    // 0xa973c8: StoreField: r3->field_b = r0
    //     0xa973c8: stur            w0, [x3, #0xb]
    // 0xa973cc: ldur            x0, [fp, #-0x10]
    // 0xa973d0: r1 = LoadInt32Instr(r0)
    //     0xa973d0: sbfx            x1, x0, #1, #0x1f
    //     0xa973d4: tbz             w0, #0, #0xa973dc
    //     0xa973d8: ldur            x1, [x0, #7]
    // 0xa973dc: StoreField: r3->field_f = r1
    //     0xa973dc: stur            x1, [x3, #0xf]
    // 0xa973e0: ldur            x2, [fp, #-0x18]
    // 0xa973e4: r1 = Function '<anonymous closure>':.
    //     0xa973e4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eac8] AnonymousClosure: (0x9b170c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa973e8: ldr             x1, [x1, #0xac8]
    // 0xa973ec: r0 = AllocateClosure()
    //     0xa973ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa973f0: mov             x1, x0
    // 0xa973f4: ldur            x0, [fp, #-0x28]
    // 0xa973f8: ArrayStore: r0[0] = r1  ; List_4
    //     0xa973f8: stur            w1, [x0, #0x17]
    // 0xa973fc: ldur            x1, [fp, #-8]
    // 0xa97400: StoreField: r0->field_1b = r1
    //     0xa97400: stur            w1, [x0, #0x1b]
    // 0xa97404: LeaveFrame
    //     0xa97404: mov             SP, fp
    //     0xa97408: ldp             fp, lr, [SP], #0x10
    // 0xa9740c: ret
    //     0xa9740c: ret             
    // 0xa97410: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa97410: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa97414: b               #0xa97308
    // 0xa97418: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa97418: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xa97428, size: 0x24c
    // 0xa97428: EnterFrame
    //     0xa97428: stp             fp, lr, [SP, #-0x10]!
    //     0xa9742c: mov             fp, SP
    // 0xa97430: AllocStack(0x20)
    //     0xa97430: sub             SP, SP, #0x20
    // 0xa97434: SetupParameters()
    //     0xa97434: ldr             x0, [fp, #0x18]
    //     0xa97438: ldur            w1, [x0, #0x17]
    //     0xa9743c: add             x1, x1, HEAP, lsl #32
    //     0xa97440: stur            x1, [fp, #-8]
    // 0xa97444: CheckStackOverflow
    //     0xa97444: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa97448: cmp             SP, x16
    //     0xa9744c: b.ls            #0xa9766c
    // 0xa97450: r1 = 1
    //     0xa97450: movz            x1, #0x1
    // 0xa97454: r0 = AllocateContext()
    //     0xa97454: bl              #0x16f6108  ; AllocateContextStub
    // 0xa97458: mov             x2, x0
    // 0xa9745c: ldur            x0, [fp, #-8]
    // 0xa97460: stur            x2, [fp, #-0x10]
    // 0xa97464: StoreField: r2->field_b = r0
    //     0xa97464: stur            w0, [x2, #0xb]
    // 0xa97468: ldr             x1, [fp, #0x10]
    // 0xa9746c: StoreField: r2->field_f = r1
    //     0xa9746c: stur            w1, [x2, #0xf]
    // 0xa97470: LoadField: r1 = r0->field_f
    //     0xa97470: ldur            w1, [x0, #0xf]
    // 0xa97474: DecompressPointer r1
    //     0xa97474: add             x1, x1, HEAP, lsl #32
    // 0xa97478: r0 = controller()
    //     0xa97478: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa9747c: LoadField: r1 = r0->field_7b
    //     0xa9747c: ldur            w1, [x0, #0x7b]
    // 0xa97480: DecompressPointer r1
    //     0xa97480: add             x1, x1, HEAP, lsl #32
    // 0xa97484: r0 = value()
    //     0xa97484: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa97488: LoadField: r1 = r0->field_b
    //     0xa97488: ldur            w1, [x0, #0xb]
    // 0xa9748c: DecompressPointer r1
    //     0xa9748c: add             x1, x1, HEAP, lsl #32
    // 0xa97490: cmp             w1, NULL
    // 0xa97494: b.ne            #0xa974a0
    // 0xa97498: r2 = Null
    //     0xa97498: mov             x2, NULL
    // 0xa9749c: b               #0xa974ec
    // 0xa974a0: LoadField: r0 = r1->field_b
    //     0xa974a0: ldur            w0, [x1, #0xb]
    // 0xa974a4: DecompressPointer r0
    //     0xa974a4: add             x0, x0, HEAP, lsl #32
    // 0xa974a8: ldur            x2, [fp, #-0x10]
    // 0xa974ac: stur            x0, [fp, #-0x18]
    // 0xa974b0: r1 = Function '<anonymous closure>':.
    //     0xa974b0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ead0] AnonymousClosure: (0x9ba5e0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa974b4: ldr             x1, [x1, #0xad0]
    // 0xa974b8: r0 = AllocateClosure()
    //     0xa974b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa974bc: r1 = Function '<anonymous closure>':.
    //     0xa974bc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ead8] AnonymousClosure: (0x9ba5c8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa974c0: ldr             x1, [x1, #0xad8]
    // 0xa974c4: r2 = Null
    //     0xa974c4: mov             x2, NULL
    // 0xa974c8: stur            x0, [fp, #-0x10]
    // 0xa974cc: r0 = AllocateClosure()
    //     0xa974cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa974d0: str             x0, [SP]
    // 0xa974d4: ldur            x1, [fp, #-0x18]
    // 0xa974d8: ldur            x2, [fp, #-0x10]
    // 0xa974dc: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xa974dc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xa974e0: ldr             x4, [x4, #0xb48]
    // 0xa974e4: r0 = firstWhere()
    //     0xa974e4: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0xa974e8: mov             x2, x0
    // 0xa974ec: ldur            x0, [fp, #-8]
    // 0xa974f0: stur            x2, [fp, #-0x10]
    // 0xa974f4: LoadField: r1 = r0->field_f
    //     0xa974f4: ldur            w1, [x0, #0xf]
    // 0xa974f8: DecompressPointer r1
    //     0xa974f8: add             x1, x1, HEAP, lsl #32
    // 0xa974fc: r0 = controller()
    //     0xa974fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa97500: LoadField: r3 = r0->field_83
    //     0xa97500: ldur            w3, [x0, #0x83]
    // 0xa97504: DecompressPointer r3
    //     0xa97504: add             x3, x3, HEAP, lsl #32
    // 0xa97508: ldur            x0, [fp, #-0x10]
    // 0xa9750c: stur            x3, [fp, #-0x18]
    // 0xa97510: r2 = Null
    //     0xa97510: mov             x2, NULL
    // 0xa97514: r1 = Null
    //     0xa97514: mov             x1, NULL
    // 0xa97518: r4 = LoadClassIdInstr(r0)
    //     0xa97518: ldur            x4, [x0, #-1]
    //     0xa9751c: ubfx            x4, x4, #0xc, #0x14
    // 0xa97520: r17 = 5457
    //     0xa97520: movz            x17, #0x1551
    // 0xa97524: cmp             x4, x17
    // 0xa97528: b.eq            #0xa97540
    // 0xa9752c: r8 = Filter
    //     0xa9752c: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2fb58] Type: Filter
    //     0xa97530: ldr             x8, [x8, #0xb58]
    // 0xa97534: r3 = Null
    //     0xa97534: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3eae0] Null
    //     0xa97538: ldr             x3, [x3, #0xae0]
    // 0xa9753c: r0 = Filter()
    //     0xa9753c: bl              #0x9b76bc  ; IsType_Filter_Stub
    // 0xa97540: ldur            x1, [fp, #-0x18]
    // 0xa97544: ldur            x2, [fp, #-0x10]
    // 0xa97548: r0 = value=()
    //     0xa97548: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xa9754c: ldur            x0, [fp, #-8]
    // 0xa97550: LoadField: r1 = r0->field_f
    //     0xa97550: ldur            w1, [x0, #0xf]
    // 0xa97554: DecompressPointer r1
    //     0xa97554: add             x1, x1, HEAP, lsl #32
    // 0xa97558: r0 = controller()
    //     0xa97558: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa9755c: LoadField: r1 = r0->field_6b
    //     0xa9755c: ldur            w1, [x0, #0x6b]
    // 0xa97560: DecompressPointer r1
    //     0xa97560: add             x1, x1, HEAP, lsl #32
    // 0xa97564: r0 = initRefresh()
    //     0xa97564: bl              #0x8aa040  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::initRefresh
    // 0xa97568: ldur            x0, [fp, #-8]
    // 0xa9756c: LoadField: r1 = r0->field_f
    //     0xa9756c: ldur            w1, [x0, #0xf]
    // 0xa97570: DecompressPointer r1
    //     0xa97570: add             x1, x1, HEAP, lsl #32
    // 0xa97574: r0 = controller()
    //     0xa97574: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa97578: LoadField: r1 = r0->field_67
    //     0xa97578: ldur            w1, [x0, #0x67]
    // 0xa9757c: DecompressPointer r1
    //     0xa9757c: add             x1, x1, HEAP, lsl #32
    // 0xa97580: ldur            x0, [fp, #-0x10]
    // 0xa97584: LoadField: r2 = r0->field_7
    //     0xa97584: ldur            w2, [x0, #7]
    // 0xa97588: DecompressPointer r2
    //     0xa97588: add             x2, x2, HEAP, lsl #32
    // 0xa9758c: cmp             w2, NULL
    // 0xa97590: b.ne            #0xa97598
    // 0xa97594: r2 = ""
    //     0xa97594: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa97598: ldur            x3, [fp, #-8]
    // 0xa9759c: r0 = value=()
    //     0xa9759c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xa975a0: ldur            x0, [fp, #-8]
    // 0xa975a4: LoadField: r1 = r0->field_f
    //     0xa975a4: ldur            w1, [x0, #0xf]
    // 0xa975a8: DecompressPointer r1
    //     0xa975a8: add             x1, x1, HEAP, lsl #32
    // 0xa975ac: r0 = controller()
    //     0xa975ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa975b0: mov             x2, x0
    // 0xa975b4: ldur            x0, [fp, #-0x10]
    // 0xa975b8: stur            x2, [fp, #-0x18]
    // 0xa975bc: LoadField: r1 = r0->field_7
    //     0xa975bc: ldur            w1, [x0, #7]
    // 0xa975c0: DecompressPointer r1
    //     0xa975c0: add             x1, x1, HEAP, lsl #32
    // 0xa975c4: cmp             w1, NULL
    // 0xa975c8: b.ne            #0xa975d4
    // 0xa975cc: r3 = ""
    //     0xa975cc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa975d0: b               #0xa975d8
    // 0xa975d4: mov             x3, x1
    // 0xa975d8: ldur            x0, [fp, #-8]
    // 0xa975dc: stur            x3, [fp, #-0x10]
    // 0xa975e0: LoadField: r1 = r0->field_f
    //     0xa975e0: ldur            w1, [x0, #0xf]
    // 0xa975e4: DecompressPointer r1
    //     0xa975e4: add             x1, x1, HEAP, lsl #32
    // 0xa975e8: r0 = controller()
    //     0xa975e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa975ec: LoadField: r2 = r0->field_53
    //     0xa975ec: ldur            w2, [x0, #0x53]
    // 0xa975f0: DecompressPointer r2
    //     0xa975f0: add             x2, x2, HEAP, lsl #32
    // 0xa975f4: ldur            x16, [fp, #-0x10]
    // 0xa975f8: str             x16, [SP]
    // 0xa975fc: ldur            x1, [fp, #-0x18]
    // 0xa97600: r4 = const [0, 0x3, 0x1, 0x2, sortBy, 0x2, null]
    //     0xa97600: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f220] List(7) [0, 0x3, 0x1, 0x2, "sortBy", 0x2, Null]
    //     0xa97604: ldr             x4, [x4, #0x220]
    // 0xa97608: r0 = getViewAllReviews()
    //     0xa97608: bl              #0x9b692c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::getViewAllReviews
    // 0xa9760c: ldur            x0, [fp, #-8]
    // 0xa97610: LoadField: r1 = r0->field_f
    //     0xa97610: ldur            w1, [x0, #0xf]
    // 0xa97614: DecompressPointer r1
    //     0xa97614: add             x1, x1, HEAP, lsl #32
    // 0xa97618: r0 = controller()
    //     0xa97618: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa9761c: mov             x2, x0
    // 0xa97620: ldur            x0, [fp, #-8]
    // 0xa97624: stur            x2, [fp, #-0x10]
    // 0xa97628: LoadField: r1 = r0->field_f
    //     0xa97628: ldur            w1, [x0, #0xf]
    // 0xa9762c: DecompressPointer r1
    //     0xa9762c: add             x1, x1, HEAP, lsl #32
    // 0xa97630: r0 = controller()
    //     0xa97630: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa97634: LoadField: r1 = r0->field_67
    //     0xa97634: ldur            w1, [x0, #0x67]
    // 0xa97638: DecompressPointer r1
    //     0xa97638: add             x1, x1, HEAP, lsl #32
    // 0xa9763c: r0 = value()
    //     0xa9763c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa97640: str             x0, [SP]
    // 0xa97644: ldur            x1, [fp, #-0x10]
    // 0xa97648: r2 = "sort_selected"
    //     0xa97648: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fb50] "sort_selected"
    //     0xa9764c: ldr             x2, [x2, #0xb50]
    // 0xa97650: r4 = const [0, 0x3, 0x1, 0x2, selectedOption, 0x2, null]
    //     0xa97650: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf8] List(7) [0, 0x3, 0x1, 0x2, "selectedOption", 0x2, Null]
    //     0xa97654: ldr             x4, [x4, #0xaf8]
    // 0xa97658: r0 = ratingReviewClickedEvent()
    //     0xa97658: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0xa9765c: r0 = Null
    //     0xa9765c: mov             x0, NULL
    // 0xa97660: LeaveFrame
    //     0xa97660: mov             SP, fp
    //     0xa97664: ldp             fp, lr, [SP], #0x10
    // 0xa97668: ret
    //     0xa97668: ret             
    // 0xa9766c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa9766c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa97670: b               #0xa97450
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa97674, size: 0x78c
    // 0xa97674: EnterFrame
    //     0xa97674: stp             fp, lr, [SP, #-0x10]!
    //     0xa97678: mov             fp, SP
    // 0xa9767c: AllocStack(0x68)
    //     0xa9767c: sub             SP, SP, #0x68
    // 0xa97680: SetupParameters()
    //     0xa97680: ldr             x0, [fp, #0x20]
    //     0xa97684: ldur            w1, [x0, #0x17]
    //     0xa97688: add             x1, x1, HEAP, lsl #32
    //     0xa9768c: stur            x1, [fp, #-8]
    // 0xa97690: CheckStackOverflow
    //     0xa97690: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa97694: cmp             SP, x16
    //     0xa97698: b.ls            #0xa97df4
    // 0xa9769c: r1 = 3
    //     0xa9769c: movz            x1, #0x3
    // 0xa976a0: r0 = AllocateContext()
    //     0xa976a0: bl              #0x16f6108  ; AllocateContextStub
    // 0xa976a4: mov             x2, x0
    // 0xa976a8: ldur            x0, [fp, #-8]
    // 0xa976ac: stur            x2, [fp, #-0x10]
    // 0xa976b0: StoreField: r2->field_b = r0
    //     0xa976b0: stur            w0, [x2, #0xb]
    // 0xa976b4: ldr             x3, [fp, #0x18]
    // 0xa976b8: StoreField: r2->field_f = r3
    //     0xa976b8: stur            w3, [x2, #0xf]
    // 0xa976bc: ldr             x4, [fp, #0x10]
    // 0xa976c0: StoreField: r2->field_13 = r4
    //     0xa976c0: stur            w4, [x2, #0x13]
    // 0xa976c4: LoadField: r1 = r0->field_f
    //     0xa976c4: ldur            w1, [x0, #0xf]
    // 0xa976c8: DecompressPointer r1
    //     0xa976c8: add             x1, x1, HEAP, lsl #32
    // 0xa976cc: r0 = controller()
    //     0xa976cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa976d0: LoadField: r1 = r0->field_7f
    //     0xa976d0: ldur            w1, [x0, #0x7f]
    // 0xa976d4: DecompressPointer r1
    //     0xa976d4: add             x1, x1, HEAP, lsl #32
    // 0xa976d8: r0 = value()
    //     0xa976d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa976dc: LoadField: r3 = r0->field_f
    //     0xa976dc: ldur            w3, [x0, #0xf]
    // 0xa976e0: DecompressPointer r3
    //     0xa976e0: add             x3, x3, HEAP, lsl #32
    // 0xa976e4: stur            x3, [fp, #-0x18]
    // 0xa976e8: cmp             w3, NULL
    // 0xa976ec: b.ne            #0xa976f8
    // 0xa976f0: r2 = Null
    //     0xa976f0: mov             x2, NULL
    // 0xa976f4: b               #0xa9772c
    // 0xa976f8: r1 = Function '<anonymous closure>':.
    //     0xa976f8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0xa976fc: ldr             x1, [x1, #0xaf0]
    // 0xa97700: r2 = Null
    //     0xa97700: mov             x2, NULL
    // 0xa97704: r0 = AllocateClosure()
    //     0xa97704: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa97708: ldur            x16, [fp, #-0x18]
    // 0xa9770c: stp             x16, NULL, [SP, #8]
    // 0xa97710: str             x0, [SP]
    // 0xa97714: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa97714: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa97718: r0 = expand()
    //     0xa97718: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0xa9771c: mov             x1, x0
    // 0xa97720: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa97720: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa97724: r0 = toList()
    //     0xa97724: bl              #0x78a798  ; [dart:core] Iterable::toList
    // 0xa97728: mov             x2, x0
    // 0xa9772c: cmp             w2, NULL
    // 0xa97730: b.ne            #0xa97740
    // 0xa97734: ldr             x3, [fp, #0x10]
    // 0xa97738: r1 = Null
    //     0xa97738: mov             x1, NULL
    // 0xa9773c: b               #0xa9777c
    // 0xa97740: ldr             x3, [fp, #0x10]
    // 0xa97744: LoadField: r0 = r2->field_b
    //     0xa97744: ldur            w0, [x2, #0xb]
    // 0xa97748: r4 = LoadInt32Instr(r3)
    //     0xa97748: sbfx            x4, x3, #1, #0x1f
    //     0xa9774c: tbz             w3, #0, #0xa97754
    //     0xa97750: ldur            x4, [x3, #7]
    // 0xa97754: r1 = LoadInt32Instr(r0)
    //     0xa97754: sbfx            x1, x0, #1, #0x1f
    // 0xa97758: mov             x0, x1
    // 0xa9775c: mov             x1, x4
    // 0xa97760: cmp             x1, x0
    // 0xa97764: b.hs            #0xa97dfc
    // 0xa97768: LoadField: r0 = r2->field_f
    //     0xa97768: ldur            w0, [x2, #0xf]
    // 0xa9776c: DecompressPointer r0
    //     0xa9776c: add             x0, x0, HEAP, lsl #32
    // 0xa97770: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa97770: add             x16, x0, x4, lsl #2
    //     0xa97774: ldur            w1, [x16, #0xf]
    // 0xa97778: DecompressPointer r1
    //     0xa97778: add             x1, x1, HEAP, lsl #32
    // 0xa9777c: ldur            x2, [fp, #-0x10]
    // 0xa97780: mov             x0, x1
    // 0xa97784: stur            x1, [fp, #-0x18]
    // 0xa97788: ArrayStore: r2[0] = r0  ; List_4
    //     0xa97788: stur            w0, [x2, #0x17]
    //     0xa9778c: tbz             w0, #0, #0xa977a8
    //     0xa97790: ldurb           w16, [x2, #-1]
    //     0xa97794: ldurb           w17, [x0, #-1]
    //     0xa97798: and             x16, x17, x16, lsr #2
    //     0xa9779c: tst             x16, HEAP, lsr #32
    //     0xa977a0: b.eq            #0xa977a8
    //     0xa977a4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa977a8: r0 = LoadInt32Instr(r3)
    //     0xa977a8: sbfx            x0, x3, #1, #0x1f
    //     0xa977ac: tbz             w3, #0, #0xa977b4
    //     0xa977b0: ldur            x0, [x3, #7]
    // 0xa977b4: cmp             x0, #4
    // 0xa977b8: b.ge            #0xa97a10
    // 0xa977bc: r0 = Radius()
    //     0xa977bc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa977c0: d0 = 12.000000
    //     0xa977c0: fmov            d0, #12.00000000
    // 0xa977c4: stur            x0, [fp, #-0x20]
    // 0xa977c8: StoreField: r0->field_7 = d0
    //     0xa977c8: stur            d0, [x0, #7]
    // 0xa977cc: StoreField: r0->field_f = d0
    //     0xa977cc: stur            d0, [x0, #0xf]
    // 0xa977d0: r0 = BorderRadius()
    //     0xa977d0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa977d4: mov             x1, x0
    // 0xa977d8: ldur            x0, [fp, #-0x20]
    // 0xa977dc: stur            x1, [fp, #-0x28]
    // 0xa977e0: StoreField: r1->field_7 = r0
    //     0xa977e0: stur            w0, [x1, #7]
    // 0xa977e4: StoreField: r1->field_b = r0
    //     0xa977e4: stur            w0, [x1, #0xb]
    // 0xa977e8: StoreField: r1->field_f = r0
    //     0xa977e8: stur            w0, [x1, #0xf]
    // 0xa977ec: StoreField: r1->field_13 = r0
    //     0xa977ec: stur            w0, [x1, #0x13]
    // 0xa977f0: ldur            x16, [fp, #-0x18]
    // 0xa977f4: str             x16, [SP]
    // 0xa977f8: r4 = 0
    //     0xa977f8: movz            x4, #0
    // 0xa977fc: ldr             x0, [SP]
    // 0xa97800: r16 = UnlinkedCall_0x613b5c
    //     0xa97800: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eaf8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa97804: add             x16, x16, #0xaf8
    // 0xa97808: ldp             x5, lr, [x16]
    // 0xa9780c: blr             lr
    // 0xa97810: r1 = 60
    //     0xa97810: movz            x1, #0x3c
    // 0xa97814: branchIfSmi(r0, 0xa97820)
    //     0xa97814: tbz             w0, #0, #0xa97820
    // 0xa97818: r1 = LoadClassIdInstr(r0)
    //     0xa97818: ldur            x1, [x0, #-1]
    //     0xa9781c: ubfx            x1, x1, #0xc, #0x14
    // 0xa97820: r16 = "image"
    //     0xa97820: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0xa97824: stp             x16, x0, [SP]
    // 0xa97828: mov             x0, x1
    // 0xa9782c: mov             lr, x0
    // 0xa97830: ldr             lr, [x21, lr, lsl #3]
    // 0xa97834: blr             lr
    // 0xa97838: tbnz            w0, #4, #0xa97910
    // 0xa9783c: ldur            x16, [fp, #-0x18]
    // 0xa97840: str             x16, [SP]
    // 0xa97844: r4 = 0
    //     0xa97844: movz            x4, #0
    // 0xa97848: ldr             x0, [SP]
    // 0xa9784c: r16 = UnlinkedCall_0x613b5c
    //     0xa9784c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb08] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa97850: add             x16, x16, #0xb08
    // 0xa97854: ldp             x5, lr, [x16]
    // 0xa97858: blr             lr
    // 0xa9785c: mov             x3, x0
    // 0xa97860: r2 = Null
    //     0xa97860: mov             x2, NULL
    // 0xa97864: r1 = Null
    //     0xa97864: mov             x1, NULL
    // 0xa97868: stur            x3, [fp, #-0x20]
    // 0xa9786c: r4 = 60
    //     0xa9786c: movz            x4, #0x3c
    // 0xa97870: branchIfSmi(r0, 0xa9787c)
    //     0xa97870: tbz             w0, #0, #0xa9787c
    // 0xa97874: r4 = LoadClassIdInstr(r0)
    //     0xa97874: ldur            x4, [x0, #-1]
    //     0xa97878: ubfx            x4, x4, #0xc, #0x14
    // 0xa9787c: sub             x4, x4, #0x5e
    // 0xa97880: cmp             x4, #1
    // 0xa97884: b.ls            #0xa97898
    // 0xa97888: r8 = String
    //     0xa97888: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xa9788c: r3 = Null
    //     0xa9788c: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3eb18] Null
    //     0xa97890: ldr             x3, [x3, #0xb18]
    // 0xa97894: r0 = String()
    //     0xa97894: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xa97898: r1 = Function '<anonymous closure>':.
    //     0xa97898: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb28] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa9789c: ldr             x1, [x1, #0xb28]
    // 0xa978a0: r2 = Null
    //     0xa978a0: mov             x2, NULL
    // 0xa978a4: r0 = AllocateClosure()
    //     0xa978a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa978a8: r1 = Function '<anonymous closure>':.
    //     0xa978a8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb30] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa978ac: ldr             x1, [x1, #0xb30]
    // 0xa978b0: r2 = Null
    //     0xa978b0: mov             x2, NULL
    // 0xa978b4: stur            x0, [fp, #-0x30]
    // 0xa978b8: r0 = AllocateClosure()
    //     0xa978b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa978bc: stur            x0, [fp, #-0x38]
    // 0xa978c0: r0 = CachedNetworkImage()
    //     0xa978c0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa978c4: stur            x0, [fp, #-0x40]
    // 0xa978c8: r16 = 60.000000
    //     0xa978c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xa978cc: ldr             x16, [x16, #0x110]
    // 0xa978d0: r30 = 62.000000
    //     0xa978d0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fbc0] 62
    //     0xa978d4: ldr             lr, [lr, #0xbc0]
    // 0xa978d8: stp             lr, x16, [SP, #0x18]
    // 0xa978dc: r16 = Instance_BoxFit
    //     0xa978dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xa978e0: ldr             x16, [x16, #0x118]
    // 0xa978e4: ldur            lr, [fp, #-0x30]
    // 0xa978e8: stp             lr, x16, [SP, #8]
    // 0xa978ec: ldur            x16, [fp, #-0x38]
    // 0xa978f0: str             x16, [SP]
    // 0xa978f4: mov             x1, x0
    // 0xa978f8: ldur            x2, [fp, #-0x20]
    // 0xa978fc: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xa978fc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xa97900: ldr             x4, [x4, #0xc28]
    // 0xa97904: r0 = CachedNetworkImage()
    //     0xa97904: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa97908: ldur            x1, [fp, #-0x40]
    // 0xa9790c: b               #0xa9797c
    // 0xa97910: ldur            x16, [fp, #-0x18]
    // 0xa97914: str             x16, [SP]
    // 0xa97918: r4 = 0
    //     0xa97918: movz            x4, #0
    // 0xa9791c: ldr             x0, [SP]
    // 0xa97920: r16 = UnlinkedCall_0x613b5c
    //     0xa97920: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb38] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa97924: add             x16, x16, #0xb38
    // 0xa97928: ldp             x5, lr, [x16]
    // 0xa9792c: blr             lr
    // 0xa97930: mov             x3, x0
    // 0xa97934: r2 = Null
    //     0xa97934: mov             x2, NULL
    // 0xa97938: r1 = Null
    //     0xa97938: mov             x1, NULL
    // 0xa9793c: stur            x3, [fp, #-0x20]
    // 0xa97940: r4 = 60
    //     0xa97940: movz            x4, #0x3c
    // 0xa97944: branchIfSmi(r0, 0xa97950)
    //     0xa97944: tbz             w0, #0, #0xa97950
    // 0xa97948: r4 = LoadClassIdInstr(r0)
    //     0xa97948: ldur            x4, [x0, #-1]
    //     0xa9794c: ubfx            x4, x4, #0xc, #0x14
    // 0xa97950: sub             x4, x4, #0x5e
    // 0xa97954: cmp             x4, #1
    // 0xa97958: b.ls            #0xa9796c
    // 0xa9795c: r8 = String
    //     0xa9795c: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xa97960: r3 = Null
    //     0xa97960: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3eb48] Null
    //     0xa97964: ldr             x3, [x3, #0xb48]
    // 0xa97968: r0 = String()
    //     0xa97968: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xa9796c: r0 = VideoPlayerWidget()
    //     0xa9796c: bl              #0xa971e0  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0xa97970: mov             x1, x0
    // 0xa97974: ldur            x0, [fp, #-0x20]
    // 0xa97978: StoreField: r1->field_b = r0
    //     0xa97978: stur            w0, [x1, #0xb]
    // 0xa9797c: ldur            x0, [fp, #-0x28]
    // 0xa97980: stur            x1, [fp, #-0x20]
    // 0xa97984: r0 = InkWell()
    //     0xa97984: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa97988: mov             x3, x0
    // 0xa9798c: ldur            x0, [fp, #-0x20]
    // 0xa97990: stur            x3, [fp, #-0x30]
    // 0xa97994: StoreField: r3->field_b = r0
    //     0xa97994: stur            w0, [x3, #0xb]
    // 0xa97998: ldur            x2, [fp, #-0x10]
    // 0xa9799c: r1 = Function '<anonymous closure>':.
    //     0xa9799c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb58] AnonymousClosure: (0xa97e00), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14fc2bc)
    //     0xa979a0: ldr             x1, [x1, #0xb58]
    // 0xa979a4: r0 = AllocateClosure()
    //     0xa979a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa979a8: mov             x1, x0
    // 0xa979ac: ldur            x0, [fp, #-0x30]
    // 0xa979b0: StoreField: r0->field_f = r1
    //     0xa979b0: stur            w1, [x0, #0xf]
    // 0xa979b4: r1 = true
    //     0xa979b4: add             x1, NULL, #0x20  ; true
    // 0xa979b8: StoreField: r0->field_43 = r1
    //     0xa979b8: stur            w1, [x0, #0x43]
    // 0xa979bc: r2 = Instance_BoxShape
    //     0xa979bc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa979c0: ldr             x2, [x2, #0x80]
    // 0xa979c4: StoreField: r0->field_47 = r2
    //     0xa979c4: stur            w2, [x0, #0x47]
    // 0xa979c8: StoreField: r0->field_6f = r1
    //     0xa979c8: stur            w1, [x0, #0x6f]
    // 0xa979cc: r3 = false
    //     0xa979cc: add             x3, NULL, #0x30  ; false
    // 0xa979d0: StoreField: r0->field_73 = r3
    //     0xa979d0: stur            w3, [x0, #0x73]
    // 0xa979d4: StoreField: r0->field_83 = r1
    //     0xa979d4: stur            w1, [x0, #0x83]
    // 0xa979d8: StoreField: r0->field_7b = r3
    //     0xa979d8: stur            w3, [x0, #0x7b]
    // 0xa979dc: r0 = ClipRRect()
    //     0xa979dc: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xa979e0: mov             x1, x0
    // 0xa979e4: ldur            x0, [fp, #-0x28]
    // 0xa979e8: StoreField: r1->field_f = r0
    //     0xa979e8: stur            w0, [x1, #0xf]
    // 0xa979ec: r0 = Instance_Clip
    //     0xa979ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xa979f0: ldr             x0, [x0, #0x138]
    // 0xa979f4: ArrayStore: r1[0] = r0  ; List_4
    //     0xa979f4: stur            w0, [x1, #0x17]
    // 0xa979f8: ldur            x0, [fp, #-0x30]
    // 0xa979fc: StoreField: r1->field_b = r0
    //     0xa979fc: stur            w0, [x1, #0xb]
    // 0xa97a00: mov             x0, x1
    // 0xa97a04: LeaveFrame
    //     0xa97a04: mov             SP, fp
    //     0xa97a08: ldp             fp, lr, [SP], #0x10
    // 0xa97a0c: ret
    //     0xa97a0c: ret             
    // 0xa97a10: ldur            x4, [fp, #-8]
    // 0xa97a14: r1 = true
    //     0xa97a14: add             x1, NULL, #0x20  ; true
    // 0xa97a18: r3 = false
    //     0xa97a18: add             x3, NULL, #0x30  ; false
    // 0xa97a1c: r2 = Instance_BoxShape
    //     0xa97a1c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa97a20: ldr             x2, [x2, #0x80]
    // 0xa97a24: r0 = Instance_Clip
    //     0xa97a24: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xa97a28: ldr             x0, [x0, #0x138]
    // 0xa97a2c: d0 = 12.000000
    //     0xa97a2c: fmov            d0, #12.00000000
    // 0xa97a30: r0 = Radius()
    //     0xa97a30: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa97a34: d0 = 12.000000
    //     0xa97a34: fmov            d0, #12.00000000
    // 0xa97a38: stur            x0, [fp, #-0x20]
    // 0xa97a3c: StoreField: r0->field_7 = d0
    //     0xa97a3c: stur            d0, [x0, #7]
    // 0xa97a40: StoreField: r0->field_f = d0
    //     0xa97a40: stur            d0, [x0, #0xf]
    // 0xa97a44: r0 = BorderRadius()
    //     0xa97a44: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa97a48: mov             x1, x0
    // 0xa97a4c: ldur            x0, [fp, #-0x20]
    // 0xa97a50: stur            x1, [fp, #-0x28]
    // 0xa97a54: StoreField: r1->field_7 = r0
    //     0xa97a54: stur            w0, [x1, #7]
    // 0xa97a58: StoreField: r1->field_b = r0
    //     0xa97a58: stur            w0, [x1, #0xb]
    // 0xa97a5c: StoreField: r1->field_f = r0
    //     0xa97a5c: stur            w0, [x1, #0xf]
    // 0xa97a60: StoreField: r1->field_13 = r0
    //     0xa97a60: stur            w0, [x1, #0x13]
    // 0xa97a64: ldur            x16, [fp, #-0x18]
    // 0xa97a68: str             x16, [SP]
    // 0xa97a6c: r4 = 0
    //     0xa97a6c: movz            x4, #0
    // 0xa97a70: ldr             x0, [SP]
    // 0xa97a74: r16 = UnlinkedCall_0x613b5c
    //     0xa97a74: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb60] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa97a78: add             x16, x16, #0xb60
    // 0xa97a7c: ldp             x5, lr, [x16]
    // 0xa97a80: blr             lr
    // 0xa97a84: mov             x3, x0
    // 0xa97a88: r2 = Null
    //     0xa97a88: mov             x2, NULL
    // 0xa97a8c: r1 = Null
    //     0xa97a8c: mov             x1, NULL
    // 0xa97a90: stur            x3, [fp, #-0x18]
    // 0xa97a94: r4 = 60
    //     0xa97a94: movz            x4, #0x3c
    // 0xa97a98: branchIfSmi(r0, 0xa97aa4)
    //     0xa97a98: tbz             w0, #0, #0xa97aa4
    // 0xa97a9c: r4 = LoadClassIdInstr(r0)
    //     0xa97a9c: ldur            x4, [x0, #-1]
    //     0xa97aa0: ubfx            x4, x4, #0xc, #0x14
    // 0xa97aa4: sub             x4, x4, #0x5e
    // 0xa97aa8: cmp             x4, #1
    // 0xa97aac: b.ls            #0xa97ac0
    // 0xa97ab0: r8 = String
    //     0xa97ab0: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xa97ab4: r3 = Null
    //     0xa97ab4: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3eb70] Null
    //     0xa97ab8: ldr             x3, [x3, #0xb70]
    // 0xa97abc: r0 = String()
    //     0xa97abc: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xa97ac0: r1 = Function '<anonymous closure>':.
    //     0xa97ac0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb80] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa97ac4: ldr             x1, [x1, #0xb80]
    // 0xa97ac8: r2 = Null
    //     0xa97ac8: mov             x2, NULL
    // 0xa97acc: r0 = AllocateClosure()
    //     0xa97acc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa97ad0: r1 = Function '<anonymous closure>':.
    //     0xa97ad0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb88] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa97ad4: ldr             x1, [x1, #0xb88]
    // 0xa97ad8: r2 = Null
    //     0xa97ad8: mov             x2, NULL
    // 0xa97adc: stur            x0, [fp, #-0x20]
    // 0xa97ae0: r0 = AllocateClosure()
    //     0xa97ae0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa97ae4: stur            x0, [fp, #-0x30]
    // 0xa97ae8: r0 = CachedNetworkImage()
    //     0xa97ae8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa97aec: stur            x0, [fp, #-0x38]
    // 0xa97af0: r16 = 60.000000
    //     0xa97af0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xa97af4: ldr             x16, [x16, #0x110]
    // 0xa97af8: r30 = 62.000000
    //     0xa97af8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fbc0] 62
    //     0xa97afc: ldr             lr, [lr, #0xbc0]
    // 0xa97b00: stp             lr, x16, [SP, #0x18]
    // 0xa97b04: r16 = Instance_BoxFit
    //     0xa97b04: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xa97b08: ldr             x16, [x16, #0x118]
    // 0xa97b0c: ldur            lr, [fp, #-0x20]
    // 0xa97b10: stp             lr, x16, [SP, #8]
    // 0xa97b14: ldur            x16, [fp, #-0x30]
    // 0xa97b18: str             x16, [SP]
    // 0xa97b1c: mov             x1, x0
    // 0xa97b20: ldur            x2, [fp, #-0x18]
    // 0xa97b24: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xa97b24: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xa97b28: ldr             x4, [x4, #0xc28]
    // 0xa97b2c: r0 = CachedNetworkImage()
    //     0xa97b2c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa97b30: r0 = ClipRRect()
    //     0xa97b30: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xa97b34: mov             x1, x0
    // 0xa97b38: ldur            x0, [fp, #-0x28]
    // 0xa97b3c: stur            x1, [fp, #-0x18]
    // 0xa97b40: StoreField: r1->field_f = r0
    //     0xa97b40: stur            w0, [x1, #0xf]
    // 0xa97b44: r0 = Instance_Clip
    //     0xa97b44: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xa97b48: ldr             x0, [x0, #0x138]
    // 0xa97b4c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa97b4c: stur            w0, [x1, #0x17]
    // 0xa97b50: ldur            x0, [fp, #-0x38]
    // 0xa97b54: StoreField: r1->field_b = r0
    //     0xa97b54: stur            w0, [x1, #0xb]
    // 0xa97b58: r0 = Radius()
    //     0xa97b58: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa97b5c: d0 = 12.000000
    //     0xa97b5c: fmov            d0, #12.00000000
    // 0xa97b60: stur            x0, [fp, #-0x20]
    // 0xa97b64: StoreField: r0->field_7 = d0
    //     0xa97b64: stur            d0, [x0, #7]
    // 0xa97b68: StoreField: r0->field_f = d0
    //     0xa97b68: stur            d0, [x0, #0xf]
    // 0xa97b6c: r0 = BorderRadius()
    //     0xa97b6c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa97b70: mov             x2, x0
    // 0xa97b74: ldur            x0, [fp, #-0x20]
    // 0xa97b78: stur            x2, [fp, #-0x28]
    // 0xa97b7c: StoreField: r2->field_7 = r0
    //     0xa97b7c: stur            w0, [x2, #7]
    // 0xa97b80: StoreField: r2->field_b = r0
    //     0xa97b80: stur            w0, [x2, #0xb]
    // 0xa97b84: StoreField: r2->field_f = r0
    //     0xa97b84: stur            w0, [x2, #0xf]
    // 0xa97b88: StoreField: r2->field_13 = r0
    //     0xa97b88: stur            w0, [x2, #0x13]
    // 0xa97b8c: r1 = Instance_Color
    //     0xa97b8c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa97b90: d0 = 0.600000
    //     0xa97b90: ldr             d0, [PP, #0x54f8]  ; [pp+0x54f8] IMM: double(0.6) from 0x3fe3333333333333
    // 0xa97b94: r0 = withOpacity()
    //     0xa97b94: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa97b98: stur            x0, [fp, #-0x20]
    // 0xa97b9c: r0 = BoxDecoration()
    //     0xa97b9c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa97ba0: mov             x3, x0
    // 0xa97ba4: ldur            x0, [fp, #-0x20]
    // 0xa97ba8: stur            x3, [fp, #-0x30]
    // 0xa97bac: StoreField: r3->field_7 = r0
    //     0xa97bac: stur            w0, [x3, #7]
    // 0xa97bb0: ldur            x0, [fp, #-0x28]
    // 0xa97bb4: StoreField: r3->field_13 = r0
    //     0xa97bb4: stur            w0, [x3, #0x13]
    // 0xa97bb8: r0 = Instance_BoxShape
    //     0xa97bb8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa97bbc: ldr             x0, [x0, #0x80]
    // 0xa97bc0: StoreField: r3->field_23 = r0
    //     0xa97bc0: stur            w0, [x3, #0x23]
    // 0xa97bc4: r1 = Null
    //     0xa97bc4: mov             x1, NULL
    // 0xa97bc8: r2 = 4
    //     0xa97bc8: movz            x2, #0x4
    // 0xa97bcc: r0 = AllocateArray()
    //     0xa97bcc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa97bd0: stur            x0, [fp, #-0x20]
    // 0xa97bd4: r16 = "+ "
    //     0xa97bd4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc30] "+ "
    //     0xa97bd8: ldr             x16, [x16, #0xc30]
    // 0xa97bdc: StoreField: r0->field_f = r16
    //     0xa97bdc: stur            w16, [x0, #0xf]
    // 0xa97be0: ldur            x1, [fp, #-8]
    // 0xa97be4: LoadField: r2 = r1->field_f
    //     0xa97be4: ldur            w2, [x1, #0xf]
    // 0xa97be8: DecompressPointer r2
    //     0xa97be8: add             x2, x2, HEAP, lsl #32
    // 0xa97bec: mov             x1, x2
    // 0xa97bf0: r0 = controller()
    //     0xa97bf0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa97bf4: LoadField: r1 = r0->field_7f
    //     0xa97bf4: ldur            w1, [x0, #0x7f]
    // 0xa97bf8: DecompressPointer r1
    //     0xa97bf8: add             x1, x1, HEAP, lsl #32
    // 0xa97bfc: r0 = value()
    //     0xa97bfc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa97c00: LoadField: r1 = r0->field_b
    //     0xa97c00: ldur            w1, [x0, #0xb]
    // 0xa97c04: DecompressPointer r1
    //     0xa97c04: add             x1, x1, HEAP, lsl #32
    // 0xa97c08: cmp             w1, NULL
    // 0xa97c0c: b.ne            #0xa97c18
    // 0xa97c10: r0 = 0
    //     0xa97c10: movz            x0, #0
    // 0xa97c14: b               #0xa97c24
    // 0xa97c18: r0 = LoadInt32Instr(r1)
    //     0xa97c18: sbfx            x0, x1, #1, #0x1f
    //     0xa97c1c: tbz             w1, #0, #0xa97c24
    //     0xa97c20: ldur            x0, [x1, #7]
    // 0xa97c24: ldur            x2, [fp, #-0x18]
    // 0xa97c28: sub             x3, x0, #4
    // 0xa97c2c: r0 = BoxInt64Instr(r3)
    //     0xa97c2c: sbfiz           x0, x3, #1, #0x1f
    //     0xa97c30: cmp             x3, x0, asr #1
    //     0xa97c34: b.eq            #0xa97c40
    //     0xa97c38: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa97c3c: stur            x3, [x0, #7]
    // 0xa97c40: ldur            x1, [fp, #-0x20]
    // 0xa97c44: ArrayStore: r1[1] = r0  ; List_4
    //     0xa97c44: add             x25, x1, #0x13
    //     0xa97c48: str             w0, [x25]
    //     0xa97c4c: tbz             w0, #0, #0xa97c68
    //     0xa97c50: ldurb           w16, [x1, #-1]
    //     0xa97c54: ldurb           w17, [x0, #-1]
    //     0xa97c58: and             x16, x17, x16, lsr #2
    //     0xa97c5c: tst             x16, HEAP, lsr #32
    //     0xa97c60: b.eq            #0xa97c68
    //     0xa97c64: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa97c68: ldur            x16, [fp, #-0x20]
    // 0xa97c6c: str             x16, [SP]
    // 0xa97c70: r0 = _interpolate()
    //     0xa97c70: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa97c74: ldr             x1, [fp, #0x18]
    // 0xa97c78: stur            x0, [fp, #-8]
    // 0xa97c7c: r0 = of()
    //     0xa97c7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa97c80: LoadField: r1 = r0->field_87
    //     0xa97c80: ldur            w1, [x0, #0x87]
    // 0xa97c84: DecompressPointer r1
    //     0xa97c84: add             x1, x1, HEAP, lsl #32
    // 0xa97c88: LoadField: r0 = r1->field_7
    //     0xa97c88: ldur            w0, [x1, #7]
    // 0xa97c8c: DecompressPointer r0
    //     0xa97c8c: add             x0, x0, HEAP, lsl #32
    // 0xa97c90: r16 = 12.000000
    //     0xa97c90: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa97c94: ldr             x16, [x16, #0x9e8]
    // 0xa97c98: r30 = Instance_Color
    //     0xa97c98: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa97c9c: stp             lr, x16, [SP]
    // 0xa97ca0: mov             x1, x0
    // 0xa97ca4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa97ca4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa97ca8: ldr             x4, [x4, #0xaa0]
    // 0xa97cac: r0 = copyWith()
    //     0xa97cac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa97cb0: stur            x0, [fp, #-0x20]
    // 0xa97cb4: r0 = Text()
    //     0xa97cb4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa97cb8: mov             x1, x0
    // 0xa97cbc: ldur            x0, [fp, #-8]
    // 0xa97cc0: stur            x1, [fp, #-0x28]
    // 0xa97cc4: StoreField: r1->field_b = r0
    //     0xa97cc4: stur            w0, [x1, #0xb]
    // 0xa97cc8: ldur            x0, [fp, #-0x20]
    // 0xa97ccc: StoreField: r1->field_13 = r0
    //     0xa97ccc: stur            w0, [x1, #0x13]
    // 0xa97cd0: r0 = Container()
    //     0xa97cd0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa97cd4: stur            x0, [fp, #-8]
    // 0xa97cd8: r16 = 60.000000
    //     0xa97cd8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xa97cdc: ldr             x16, [x16, #0x110]
    // 0xa97ce0: r30 = 62.000000
    //     0xa97ce0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fbc0] 62
    //     0xa97ce4: ldr             lr, [lr, #0xbc0]
    // 0xa97ce8: stp             lr, x16, [SP, #0x18]
    // 0xa97cec: ldur            x16, [fp, #-0x30]
    // 0xa97cf0: r30 = Instance_Alignment
    //     0xa97cf0: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa97cf4: ldr             lr, [lr, #0xb10]
    // 0xa97cf8: stp             lr, x16, [SP, #8]
    // 0xa97cfc: ldur            x16, [fp, #-0x28]
    // 0xa97d00: str             x16, [SP]
    // 0xa97d04: mov             x1, x0
    // 0xa97d08: r4 = const [0, 0x6, 0x5, 0x1, alignment, 0x4, child, 0x5, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xa97d08: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eb90] List(15) [0, 0x6, 0x5, 0x1, "alignment", 0x4, "child", 0x5, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa97d0c: ldr             x4, [x4, #0xb90]
    // 0xa97d10: r0 = Container()
    //     0xa97d10: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa97d14: r1 = Null
    //     0xa97d14: mov             x1, NULL
    // 0xa97d18: r2 = 4
    //     0xa97d18: movz            x2, #0x4
    // 0xa97d1c: r0 = AllocateArray()
    //     0xa97d1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa97d20: mov             x2, x0
    // 0xa97d24: ldur            x0, [fp, #-0x18]
    // 0xa97d28: stur            x2, [fp, #-0x20]
    // 0xa97d2c: StoreField: r2->field_f = r0
    //     0xa97d2c: stur            w0, [x2, #0xf]
    // 0xa97d30: ldur            x0, [fp, #-8]
    // 0xa97d34: StoreField: r2->field_13 = r0
    //     0xa97d34: stur            w0, [x2, #0x13]
    // 0xa97d38: r1 = <Widget>
    //     0xa97d38: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa97d3c: r0 = AllocateGrowableArray()
    //     0xa97d3c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa97d40: mov             x1, x0
    // 0xa97d44: ldur            x0, [fp, #-0x20]
    // 0xa97d48: stur            x1, [fp, #-8]
    // 0xa97d4c: StoreField: r1->field_f = r0
    //     0xa97d4c: stur            w0, [x1, #0xf]
    // 0xa97d50: r0 = 4
    //     0xa97d50: movz            x0, #0x4
    // 0xa97d54: StoreField: r1->field_b = r0
    //     0xa97d54: stur            w0, [x1, #0xb]
    // 0xa97d58: r0 = Stack()
    //     0xa97d58: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa97d5c: mov             x1, x0
    // 0xa97d60: r0 = Instance_Alignment
    //     0xa97d60: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa97d64: ldr             x0, [x0, #0xb10]
    // 0xa97d68: stur            x1, [fp, #-0x18]
    // 0xa97d6c: StoreField: r1->field_f = r0
    //     0xa97d6c: stur            w0, [x1, #0xf]
    // 0xa97d70: r0 = Instance_StackFit
    //     0xa97d70: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa97d74: ldr             x0, [x0, #0xfa8]
    // 0xa97d78: ArrayStore: r1[0] = r0  ; List_4
    //     0xa97d78: stur            w0, [x1, #0x17]
    // 0xa97d7c: r0 = Instance_Clip
    //     0xa97d7c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa97d80: ldr             x0, [x0, #0x7e0]
    // 0xa97d84: StoreField: r1->field_1b = r0
    //     0xa97d84: stur            w0, [x1, #0x1b]
    // 0xa97d88: ldur            x0, [fp, #-8]
    // 0xa97d8c: StoreField: r1->field_b = r0
    //     0xa97d8c: stur            w0, [x1, #0xb]
    // 0xa97d90: r0 = InkWell()
    //     0xa97d90: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa97d94: mov             x3, x0
    // 0xa97d98: ldur            x0, [fp, #-0x18]
    // 0xa97d9c: stur            x3, [fp, #-8]
    // 0xa97da0: StoreField: r3->field_b = r0
    //     0xa97da0: stur            w0, [x3, #0xb]
    // 0xa97da4: ldur            x2, [fp, #-0x10]
    // 0xa97da8: r1 = Function '<anonymous closure>':.
    //     0xa97da8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb98] AnonymousClosure: (0x9bade8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa97dac: ldr             x1, [x1, #0xb98]
    // 0xa97db0: r0 = AllocateClosure()
    //     0xa97db0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa97db4: mov             x1, x0
    // 0xa97db8: ldur            x0, [fp, #-8]
    // 0xa97dbc: StoreField: r0->field_f = r1
    //     0xa97dbc: stur            w1, [x0, #0xf]
    // 0xa97dc0: r1 = true
    //     0xa97dc0: add             x1, NULL, #0x20  ; true
    // 0xa97dc4: StoreField: r0->field_43 = r1
    //     0xa97dc4: stur            w1, [x0, #0x43]
    // 0xa97dc8: r2 = Instance_BoxShape
    //     0xa97dc8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa97dcc: ldr             x2, [x2, #0x80]
    // 0xa97dd0: StoreField: r0->field_47 = r2
    //     0xa97dd0: stur            w2, [x0, #0x47]
    // 0xa97dd4: StoreField: r0->field_6f = r1
    //     0xa97dd4: stur            w1, [x0, #0x6f]
    // 0xa97dd8: r2 = false
    //     0xa97dd8: add             x2, NULL, #0x30  ; false
    // 0xa97ddc: StoreField: r0->field_73 = r2
    //     0xa97ddc: stur            w2, [x0, #0x73]
    // 0xa97de0: StoreField: r0->field_83 = r1
    //     0xa97de0: stur            w1, [x0, #0x83]
    // 0xa97de4: StoreField: r0->field_7b = r2
    //     0xa97de4: stur            w2, [x0, #0x7b]
    // 0xa97de8: LeaveFrame
    //     0xa97de8: mov             SP, fp
    //     0xa97dec: ldp             fp, lr, [SP], #0x10
    // 0xa97df0: ret
    //     0xa97df0: ret             
    // 0xa97df4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa97df4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa97df8: b               #0xa9769c
    // 0xa97dfc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa97dfc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa97e00, size: 0xfc
    // 0xa97e00: EnterFrame
    //     0xa97e00: stp             fp, lr, [SP, #-0x10]!
    //     0xa97e04: mov             fp, SP
    // 0xa97e08: AllocStack(0x28)
    //     0xa97e08: sub             SP, SP, #0x28
    // 0xa97e0c: SetupParameters()
    //     0xa97e0c: ldr             x0, [fp, #0x10]
    //     0xa97e10: ldur            w2, [x0, #0x17]
    //     0xa97e14: add             x2, x2, HEAP, lsl #32
    //     0xa97e18: stur            x2, [fp, #-8]
    // 0xa97e1c: CheckStackOverflow
    //     0xa97e1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa97e20: cmp             SP, x16
    //     0xa97e24: b.ls            #0xa97ef4
    // 0xa97e28: LoadField: r0 = r2->field_b
    //     0xa97e28: ldur            w0, [x2, #0xb]
    // 0xa97e2c: DecompressPointer r0
    //     0xa97e2c: add             x0, x0, HEAP, lsl #32
    // 0xa97e30: LoadField: r1 = r0->field_f
    //     0xa97e30: ldur            w1, [x0, #0xf]
    // 0xa97e34: DecompressPointer r1
    //     0xa97e34: add             x1, x1, HEAP, lsl #32
    // 0xa97e38: r0 = controller()
    //     0xa97e38: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa97e3c: ldur            x2, [fp, #-8]
    // 0xa97e40: stur            x0, [fp, #-0x10]
    // 0xa97e44: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa97e44: ldur            w1, [x2, #0x17]
    // 0xa97e48: DecompressPointer r1
    //     0xa97e48: add             x1, x1, HEAP, lsl #32
    // 0xa97e4c: str             x1, [SP]
    // 0xa97e50: r4 = 0
    //     0xa97e50: movz            x4, #0
    // 0xa97e54: ldr             x0, [SP]
    // 0xa97e58: r16 = UnlinkedCall_0x613b5c
    //     0xa97e58: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eba0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa97e5c: add             x16, x16, #0xba0
    // 0xa97e60: ldp             x5, lr, [x16]
    // 0xa97e64: blr             lr
    // 0xa97e68: str             x0, [SP]
    // 0xa97e6c: ldur            x1, [fp, #-0x10]
    // 0xa97e70: r2 = "single_media"
    //     0xa97e70: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fab0] "single_media"
    //     0xa97e74: ldr             x2, [x2, #0xab0]
    // 0xa97e78: r4 = const [0, 0x3, 0x1, 0x2, selectedOption, 0x2, null]
    //     0xa97e78: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf8] List(7) [0, 0x3, 0x1, 0x2, "selectedOption", 0x2, Null]
    //     0xa97e7c: ldr             x4, [x4, #0xaf8]
    // 0xa97e80: r0 = ratingReviewClickedEvent()
    //     0xa97e80: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0xa97e84: ldur            x2, [fp, #-8]
    // 0xa97e88: LoadField: r1 = r2->field_f
    //     0xa97e88: ldur            w1, [x2, #0xf]
    // 0xa97e8c: DecompressPointer r1
    //     0xa97e8c: add             x1, x1, HEAP, lsl #32
    // 0xa97e90: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa97e90: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa97e94: r0 = of()
    //     0xa97e94: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xa97e98: ldur            x2, [fp, #-8]
    // 0xa97e9c: r1 = Function '<anonymous closure>':.
    //     0xa97e9c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ebb0] AnonymousClosure: (0xa97efc), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14fc2bc)
    //     0xa97ea0: ldr             x1, [x1, #0xbb0]
    // 0xa97ea4: stur            x0, [fp, #-8]
    // 0xa97ea8: r0 = AllocateClosure()
    //     0xa97ea8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa97eac: r1 = Null
    //     0xa97eac: mov             x1, NULL
    // 0xa97eb0: stur            x0, [fp, #-0x10]
    // 0xa97eb4: r0 = MaterialPageRoute()
    //     0xa97eb4: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xa97eb8: mov             x1, x0
    // 0xa97ebc: ldur            x2, [fp, #-0x10]
    // 0xa97ec0: stur            x0, [fp, #-0x10]
    // 0xa97ec4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa97ec4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa97ec8: r0 = MaterialPageRoute()
    //     0xa97ec8: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xa97ecc: ldur            x16, [fp, #-8]
    // 0xa97ed0: stp             x16, NULL, [SP, #8]
    // 0xa97ed4: ldur            x16, [fp, #-0x10]
    // 0xa97ed8: str             x16, [SP]
    // 0xa97edc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa97edc: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa97ee0: r0 = push()
    //     0xa97ee0: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xa97ee4: r0 = Null
    //     0xa97ee4: mov             x0, NULL
    // 0xa97ee8: LeaveFrame
    //     0xa97ee8: mov             SP, fp
    //     0xa97eec: ldp             fp, lr, [SP], #0x10
    // 0xa97ef0: ret
    //     0xa97ef0: ret             
    // 0xa97ef4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa97ef4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa97ef8: b               #0xa97e28
  }
  [closure] RatingReviewAllMediaOnTapImage <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xa97efc, size: 0x190
    // 0xa97efc: EnterFrame
    //     0xa97efc: stp             fp, lr, [SP, #-0x10]!
    //     0xa97f00: mov             fp, SP
    // 0xa97f04: AllocStack(0x38)
    //     0xa97f04: sub             SP, SP, #0x38
    // 0xa97f08: SetupParameters()
    //     0xa97f08: ldr             x0, [fp, #0x18]
    //     0xa97f0c: ldur            w2, [x0, #0x17]
    //     0xa97f10: add             x2, x2, HEAP, lsl #32
    //     0xa97f14: stur            x2, [fp, #-0x10]
    // 0xa97f18: CheckStackOverflow
    //     0xa97f18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa97f1c: cmp             SP, x16
    //     0xa97f20: b.ls            #0xa98084
    // 0xa97f24: LoadField: r0 = r2->field_b
    //     0xa97f24: ldur            w0, [x2, #0xb]
    // 0xa97f28: DecompressPointer r0
    //     0xa97f28: add             x0, x0, HEAP, lsl #32
    // 0xa97f2c: stur            x0, [fp, #-8]
    // 0xa97f30: LoadField: r1 = r0->field_f
    //     0xa97f30: ldur            w1, [x0, #0xf]
    // 0xa97f34: DecompressPointer r1
    //     0xa97f34: add             x1, x1, HEAP, lsl #32
    // 0xa97f38: r0 = controller()
    //     0xa97f38: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa97f3c: LoadField: r1 = r0->field_7f
    //     0xa97f3c: ldur            w1, [x0, #0x7f]
    // 0xa97f40: DecompressPointer r1
    //     0xa97f40: add             x1, x1, HEAP, lsl #32
    // 0xa97f44: r0 = value()
    //     0xa97f44: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa97f48: LoadField: r1 = r0->field_f
    //     0xa97f48: ldur            w1, [x0, #0xf]
    // 0xa97f4c: DecompressPointer r1
    //     0xa97f4c: add             x1, x1, HEAP, lsl #32
    // 0xa97f50: cmp             w1, NULL
    // 0xa97f54: b.ne            #0xa97f6c
    // 0xa97f58: r1 = <ReviewRatingEntity>
    //     0xa97f58: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f150] TypeArguments: <ReviewRatingEntity>
    //     0xa97f5c: ldr             x1, [x1, #0x150]
    // 0xa97f60: r2 = 0
    //     0xa97f60: movz            x2, #0
    // 0xa97f64: r0 = _GrowableList()
    //     0xa97f64: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xa97f68: mov             x1, x0
    // 0xa97f6c: ldur            x2, [fp, #-0x10]
    // 0xa97f70: ldur            x0, [fp, #-8]
    // 0xa97f74: stur            x1, [fp, #-0x20]
    // 0xa97f78: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa97f78: ldur            w3, [x2, #0x17]
    // 0xa97f7c: DecompressPointer r3
    //     0xa97f7c: add             x3, x3, HEAP, lsl #32
    // 0xa97f80: stur            x3, [fp, #-0x18]
    // 0xa97f84: str             x3, [SP]
    // 0xa97f88: r4 = 0
    //     0xa97f88: movz            x4, #0
    // 0xa97f8c: ldr             x0, [SP]
    // 0xa97f90: r16 = UnlinkedCall_0x613b5c
    //     0xa97f90: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ebb8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa97f94: add             x16, x16, #0xbb8
    // 0xa97f98: ldp             x5, lr, [x16]
    // 0xa97f9c: blr             lr
    // 0xa97fa0: stur            x0, [fp, #-0x28]
    // 0xa97fa4: ldur            x16, [fp, #-0x18]
    // 0xa97fa8: str             x16, [SP]
    // 0xa97fac: r4 = 0
    //     0xa97fac: movz            x4, #0
    // 0xa97fb0: ldr             x0, [SP]
    // 0xa97fb4: r16 = UnlinkedCall_0x613b5c
    //     0xa97fb4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ebc8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa97fb8: add             x16, x16, #0xbc8
    // 0xa97fbc: ldp             x5, lr, [x16]
    // 0xa97fc0: blr             lr
    // 0xa97fc4: mov             x3, x0
    // 0xa97fc8: r2 = Null
    //     0xa97fc8: mov             x2, NULL
    // 0xa97fcc: r1 = Null
    //     0xa97fcc: mov             x1, NULL
    // 0xa97fd0: stur            x3, [fp, #-0x18]
    // 0xa97fd4: branchIfSmi(r0, 0xa97ffc)
    //     0xa97fd4: tbz             w0, #0, #0xa97ffc
    // 0xa97fd8: r4 = LoadClassIdInstr(r0)
    //     0xa97fd8: ldur            x4, [x0, #-1]
    //     0xa97fdc: ubfx            x4, x4, #0xc, #0x14
    // 0xa97fe0: sub             x4, x4, #0x3c
    // 0xa97fe4: cmp             x4, #1
    // 0xa97fe8: b.ls            #0xa97ffc
    // 0xa97fec: r8 = int?
    //     0xa97fec: ldr             x8, [PP, #0x38b0]  ; [pp+0x38b0] Type: int?
    // 0xa97ff0: r3 = Null
    //     0xa97ff0: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ebd8] Null
    //     0xa97ff4: ldr             x3, [x3, #0xbd8]
    // 0xa97ff8: r0 = int?()
    //     0xa97ff8: bl              #0x16fc50c  ; IsType_int?_Stub
    // 0xa97ffc: ldur            x0, [fp, #-8]
    // 0xa98000: LoadField: r1 = r0->field_f
    //     0xa98000: ldur            w1, [x0, #0xf]
    // 0xa98004: DecompressPointer r1
    //     0xa98004: add             x1, x1, HEAP, lsl #32
    // 0xa98008: r0 = controller()
    //     0xa98008: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xa9800c: LoadField: r1 = r0->field_97
    //     0xa9800c: ldur            w1, [x0, #0x97]
    // 0xa98010: DecompressPointer r1
    //     0xa98010: add             x1, x1, HEAP, lsl #32
    // 0xa98014: stur            x1, [fp, #-8]
    // 0xa98018: r0 = RatingReviewAllMediaOnTapImage()
    //     0xa98018: bl              #0xa9808c  ; AllocateRatingReviewAllMediaOnTapImageStub -> RatingReviewAllMediaOnTapImage (size=0x28)
    // 0xa9801c: mov             x3, x0
    // 0xa98020: ldur            x0, [fp, #-0x20]
    // 0xa98024: stur            x3, [fp, #-0x30]
    // 0xa98028: StoreField: r3->field_f = r0
    //     0xa98028: stur            w0, [x3, #0xf]
    // 0xa9802c: ldur            x0, [fp, #-0x28]
    // 0xa98030: r1 = LoadInt32Instr(r0)
    //     0xa98030: sbfx            x1, x0, #1, #0x1f
    //     0xa98034: tbz             w0, #0, #0xa9803c
    //     0xa98038: ldur            x1, [x0, #7]
    // 0xa9803c: StoreField: r3->field_13 = r1
    //     0xa9803c: stur            x1, [x3, #0x13]
    // 0xa98040: ldur            x0, [fp, #-0x18]
    // 0xa98044: StoreField: r3->field_1b = r0
    //     0xa98044: stur            w0, [x3, #0x1b]
    // 0xa98048: r0 = "direct_image"
    //     0xa98048: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fc98] "direct_image"
    //     0xa9804c: ldr             x0, [x0, #0xc98]
    // 0xa98050: StoreField: r3->field_b = r0
    //     0xa98050: stur            w0, [x3, #0xb]
    // 0xa98054: ldur            x2, [fp, #-0x10]
    // 0xa98058: r1 = Function '<anonymous closure>':.
    //     0xa98058: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ebe8] AnonymousClosure: (0x9bb314), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa9805c: ldr             x1, [x1, #0xbe8]
    // 0xa98060: r0 = AllocateClosure()
    //     0xa98060: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa98064: mov             x1, x0
    // 0xa98068: ldur            x0, [fp, #-0x30]
    // 0xa9806c: StoreField: r0->field_1f = r1
    //     0xa9806c: stur            w1, [x0, #0x1f]
    // 0xa98070: ldur            x1, [fp, #-8]
    // 0xa98074: StoreField: r0->field_23 = r1
    //     0xa98074: stur            w1, [x0, #0x23]
    // 0xa98078: LeaveFrame
    //     0xa98078: mov             SP, fp
    //     0xa9807c: ldp             fp, lr, [SP], #0x10
    // 0xa98080: ret
    //     0xa98080: ret             
    // 0xa98084: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa98084: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa98088: b               #0xa97f24
  }
  _ body(/* No info */) {
    // ** addr: 0x14fc2bc, size: 0xc0
    // 0x14fc2bc: EnterFrame
    //     0x14fc2bc: stp             fp, lr, [SP, #-0x10]!
    //     0x14fc2c0: mov             fp, SP
    // 0x14fc2c4: AllocStack(0x18)
    //     0x14fc2c4: sub             SP, SP, #0x18
    // 0x14fc2c8: SetupParameters(ReviewListWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14fc2c8: stur            x1, [fp, #-8]
    //     0x14fc2cc: stur            x2, [fp, #-0x10]
    // 0x14fc2d0: CheckStackOverflow
    //     0x14fc2d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fc2d4: cmp             SP, x16
    //     0x14fc2d8: b.ls            #0x14fc374
    // 0x14fc2dc: r1 = 2
    //     0x14fc2dc: movz            x1, #0x2
    // 0x14fc2e0: r0 = AllocateContext()
    //     0x14fc2e0: bl              #0x16f6108  ; AllocateContextStub
    // 0x14fc2e4: mov             x1, x0
    // 0x14fc2e8: ldur            x0, [fp, #-8]
    // 0x14fc2ec: stur            x1, [fp, #-0x18]
    // 0x14fc2f0: StoreField: r1->field_f = r0
    //     0x14fc2f0: stur            w0, [x1, #0xf]
    // 0x14fc2f4: ldur            x0, [fp, #-0x10]
    // 0x14fc2f8: StoreField: r1->field_13 = r0
    //     0x14fc2f8: stur            w0, [x1, #0x13]
    // 0x14fc2fc: r0 = Obx()
    //     0x14fc2fc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14fc300: ldur            x2, [fp, #-0x18]
    // 0x14fc304: r1 = Function '<anonymous closure>':.
    //     0x14fc304: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea10] AnonymousClosure: (0xa928ec), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14fc2bc)
    //     0x14fc308: ldr             x1, [x1, #0xa10]
    // 0x14fc30c: stur            x0, [fp, #-8]
    // 0x14fc310: r0 = AllocateClosure()
    //     0x14fc310: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fc314: mov             x1, x0
    // 0x14fc318: ldur            x0, [fp, #-8]
    // 0x14fc31c: StoreField: r0->field_b = r1
    //     0x14fc31c: stur            w1, [x0, #0xb]
    // 0x14fc320: ldur            x2, [fp, #-0x18]
    // 0x14fc324: r1 = Function '<anonymous closure>':.
    //     0x14fc324: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea18] AnonymousClosure: (0x146fc60), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14fc328: ldr             x1, [x1, #0xa18]
    // 0x14fc32c: r0 = AllocateClosure()
    //     0x14fc32c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fc330: ldur            x2, [fp, #-0x18]
    // 0x14fc334: r1 = Function '<anonymous closure>':.
    //     0x14fc334: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea20] AnonymousClosure: (0x146fba8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14fc338: ldr             x1, [x1, #0xa20]
    // 0x14fc33c: stur            x0, [fp, #-0x10]
    // 0x14fc340: r0 = AllocateClosure()
    //     0x14fc340: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fc344: stur            x0, [fp, #-0x18]
    // 0x14fc348: r0 = PagingView()
    //     0x14fc348: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x14fc34c: mov             x1, x0
    // 0x14fc350: ldur            x2, [fp, #-8]
    // 0x14fc354: ldur            x3, [fp, #-0x18]
    // 0x14fc358: ldur            x5, [fp, #-0x10]
    // 0x14fc35c: stur            x0, [fp, #-8]
    // 0x14fc360: r0 = PagingView()
    //     0x14fc360: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x14fc364: ldur            x0, [fp, #-8]
    // 0x14fc368: LeaveFrame
    //     0x14fc368: mov             SP, fp
    //     0x14fc36c: ldp             fp, lr, [SP], #0x10
    // 0x14fc370: ret
    //     0x14fc370: ret             
    // 0x14fc374: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fc374: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fc378: b               #0x14fc2dc
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e5124, size: 0x38c
    // 0x15e5124: EnterFrame
    //     0x15e5124: stp             fp, lr, [SP, #-0x10]!
    //     0x15e5128: mov             fp, SP
    // 0x15e512c: AllocStack(0x60)
    //     0x15e512c: sub             SP, SP, #0x60
    // 0x15e5130: SetupParameters(ReviewListWidget this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15e5130: mov             x0, x1
    //     0x15e5134: stur            x1, [fp, #-8]
    //     0x15e5138: mov             x1, x2
    //     0x15e513c: stur            x2, [fp, #-0x10]
    // 0x15e5140: CheckStackOverflow
    //     0x15e5140: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e5144: cmp             SP, x16
    //     0x15e5148: b.ls            #0x15e54a8
    // 0x15e514c: r1 = 2
    //     0x15e514c: movz            x1, #0x2
    // 0x15e5150: r0 = AllocateContext()
    //     0x15e5150: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e5154: mov             x2, x0
    // 0x15e5158: ldur            x0, [fp, #-8]
    // 0x15e515c: stur            x2, [fp, #-0x18]
    // 0x15e5160: StoreField: r2->field_f = r0
    //     0x15e5160: stur            w0, [x2, #0xf]
    // 0x15e5164: ldur            x1, [fp, #-0x10]
    // 0x15e5168: StoreField: r2->field_13 = r1
    //     0x15e5168: stur            w1, [x2, #0x13]
    // 0x15e516c: r0 = of()
    //     0x15e516c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e5170: LoadField: r1 = r0->field_5b
    //     0x15e5170: ldur            w1, [x0, #0x5b]
    // 0x15e5174: DecompressPointer r1
    //     0x15e5174: add             x1, x1, HEAP, lsl #32
    // 0x15e5178: stur            x1, [fp, #-0x10]
    // 0x15e517c: r0 = ColorFilter()
    //     0x15e517c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e5180: mov             x1, x0
    // 0x15e5184: ldur            x0, [fp, #-0x10]
    // 0x15e5188: stur            x1, [fp, #-0x20]
    // 0x15e518c: StoreField: r1->field_7 = r0
    //     0x15e518c: stur            w0, [x1, #7]
    // 0x15e5190: r0 = Instance_BlendMode
    //     0x15e5190: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e5194: ldr             x0, [x0, #0xb30]
    // 0x15e5198: StoreField: r1->field_b = r0
    //     0x15e5198: stur            w0, [x1, #0xb]
    // 0x15e519c: r0 = 1
    //     0x15e519c: movz            x0, #0x1
    // 0x15e51a0: StoreField: r1->field_13 = r0
    //     0x15e51a0: stur            x0, [x1, #0x13]
    // 0x15e51a4: r0 = SvgPicture()
    //     0x15e51a4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e51a8: stur            x0, [fp, #-0x10]
    // 0x15e51ac: ldur            x16, [fp, #-0x20]
    // 0x15e51b0: str             x16, [SP]
    // 0x15e51b4: mov             x1, x0
    // 0x15e51b8: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e51b8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e51bc: ldr             x2, [x2, #0xa40]
    // 0x15e51c0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e51c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e51c4: ldr             x4, [x4, #0xa38]
    // 0x15e51c8: r0 = SvgPicture.asset()
    //     0x15e51c8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e51cc: r0 = Align()
    //     0x15e51cc: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e51d0: mov             x1, x0
    // 0x15e51d4: r0 = Instance_Alignment
    //     0x15e51d4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e51d8: ldr             x0, [x0, #0xb10]
    // 0x15e51dc: stur            x1, [fp, #-0x20]
    // 0x15e51e0: StoreField: r1->field_f = r0
    //     0x15e51e0: stur            w0, [x1, #0xf]
    // 0x15e51e4: r0 = 1.500000
    //     0x15e51e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd18] 1.5
    //     0x15e51e8: ldr             x0, [x0, #0xd18]
    // 0x15e51ec: StoreField: r1->field_13 = r0
    //     0x15e51ec: stur            w0, [x1, #0x13]
    // 0x15e51f0: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e51f0: stur            w0, [x1, #0x17]
    // 0x15e51f4: ldur            x0, [fp, #-0x10]
    // 0x15e51f8: StoreField: r1->field_b = r0
    //     0x15e51f8: stur            w0, [x1, #0xb]
    // 0x15e51fc: r0 = InkWell()
    //     0x15e51fc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e5200: mov             x3, x0
    // 0x15e5204: ldur            x0, [fp, #-0x20]
    // 0x15e5208: stur            x3, [fp, #-0x10]
    // 0x15e520c: StoreField: r3->field_b = r0
    //     0x15e520c: stur            w0, [x3, #0xb]
    // 0x15e5210: ldur            x2, [fp, #-0x18]
    // 0x15e5214: r1 = Function '<anonymous closure>':.
    //     0x15e5214: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ebf0] AnonymousClosure: (0x98bce4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0x15e5218: ldr             x1, [x1, #0xbf0]
    // 0x15e521c: r0 = AllocateClosure()
    //     0x15e521c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e5220: ldur            x2, [fp, #-0x10]
    // 0x15e5224: StoreField: r2->field_f = r0
    //     0x15e5224: stur            w0, [x2, #0xf]
    // 0x15e5228: r0 = true
    //     0x15e5228: add             x0, NULL, #0x20  ; true
    // 0x15e522c: StoreField: r2->field_43 = r0
    //     0x15e522c: stur            w0, [x2, #0x43]
    // 0x15e5230: r3 = Instance_BoxShape
    //     0x15e5230: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e5234: ldr             x3, [x3, #0x80]
    // 0x15e5238: StoreField: r2->field_47 = r3
    //     0x15e5238: stur            w3, [x2, #0x47]
    // 0x15e523c: StoreField: r2->field_6f = r0
    //     0x15e523c: stur            w0, [x2, #0x6f]
    // 0x15e5240: r4 = false
    //     0x15e5240: add             x4, NULL, #0x30  ; false
    // 0x15e5244: StoreField: r2->field_73 = r4
    //     0x15e5244: stur            w4, [x2, #0x73]
    // 0x15e5248: StoreField: r2->field_83 = r0
    //     0x15e5248: stur            w0, [x2, #0x83]
    // 0x15e524c: StoreField: r2->field_7b = r4
    //     0x15e524c: stur            w4, [x2, #0x7b]
    // 0x15e5250: ldur            x5, [fp, #-0x18]
    // 0x15e5254: LoadField: r1 = r5->field_13
    //     0x15e5254: ldur            w1, [x5, #0x13]
    // 0x15e5258: DecompressPointer r1
    //     0x15e5258: add             x1, x1, HEAP, lsl #32
    // 0x15e525c: r0 = of()
    //     0x15e525c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e5260: LoadField: r1 = r0->field_87
    //     0x15e5260: ldur            w1, [x0, #0x87]
    // 0x15e5264: DecompressPointer r1
    //     0x15e5264: add             x1, x1, HEAP, lsl #32
    // 0x15e5268: LoadField: r0 = r1->field_2b
    //     0x15e5268: ldur            w0, [x1, #0x2b]
    // 0x15e526c: DecompressPointer r0
    //     0x15e526c: add             x0, x0, HEAP, lsl #32
    // 0x15e5270: stur            x0, [fp, #-0x20]
    // 0x15e5274: r0 = Text()
    //     0x15e5274: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15e5278: mov             x1, x0
    // 0x15e527c: r0 = "Ratings & Reviews"
    //     0x15e527c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd28] "Ratings & Reviews"
    //     0x15e5280: ldr             x0, [x0, #0xd28]
    // 0x15e5284: stur            x1, [fp, #-0x28]
    // 0x15e5288: StoreField: r1->field_b = r0
    //     0x15e5288: stur            w0, [x1, #0xb]
    // 0x15e528c: ldur            x0, [fp, #-0x20]
    // 0x15e5290: StoreField: r1->field_13 = r0
    //     0x15e5290: stur            w0, [x1, #0x13]
    // 0x15e5294: r0 = Container()
    //     0x15e5294: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15e5298: stur            x0, [fp, #-0x20]
    // 0x15e529c: r16 = Instance_Color
    //     0x15e529c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15e52a0: r30 = 1.000000
    //     0x15e52a0: ldr             lr, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e52a4: stp             lr, x16, [SP]
    // 0x15e52a8: mov             x1, x0
    // 0x15e52ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, height, 0x2, null]
    //     0x15e52ac: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd30] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "height", 0x2, Null]
    //     0x15e52b0: ldr             x4, [x4, #0xd30]
    // 0x15e52b4: r0 = Container()
    //     0x15e52b4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15e52b8: r0 = PreferredSize()
    //     0x15e52b8: bl              #0x15d6644  ; AllocatePreferredSizeStub -> PreferredSize (size=0x14)
    // 0x15e52bc: mov             x3, x0
    // 0x15e52c0: r0 = Instance_Size
    //     0x15e52c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eb70] Obj!Size@d6c3e1
    //     0x15e52c4: ldr             x0, [x0, #0xb70]
    // 0x15e52c8: stur            x3, [fp, #-0x30]
    // 0x15e52cc: StoreField: r3->field_f = r0
    //     0x15e52cc: stur            w0, [x3, #0xf]
    // 0x15e52d0: ldur            x0, [fp, #-0x20]
    // 0x15e52d4: StoreField: r3->field_b = r0
    //     0x15e52d4: stur            w0, [x3, #0xb]
    // 0x15e52d8: r1 = <Widget>
    //     0x15e52d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15e52dc: r2 = 0
    //     0x15e52dc: movz            x2, #0
    // 0x15e52e0: r0 = _GrowableList()
    //     0x15e52e0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x15e52e4: ldur            x1, [fp, #-8]
    // 0x15e52e8: stur            x0, [fp, #-8]
    // 0x15e52ec: r0 = controller()
    //     0x15e52ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e52f0: LoadField: r1 = r0->field_7b
    //     0x15e52f0: ldur            w1, [x0, #0x7b]
    // 0x15e52f4: DecompressPointer r1
    //     0x15e52f4: add             x1, x1, HEAP, lsl #32
    // 0x15e52f8: r0 = value()
    //     0x15e52f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e52fc: LoadField: r1 = r0->field_b
    //     0x15e52fc: ldur            w1, [x0, #0xb]
    // 0x15e5300: DecompressPointer r1
    //     0x15e5300: add             x1, x1, HEAP, lsl #32
    // 0x15e5304: cmp             w1, NULL
    // 0x15e5308: b.ne            #0x15e5314
    // 0x15e530c: r0 = Null
    //     0x15e530c: mov             x0, NULL
    // 0x15e5310: b               #0x15e5338
    // 0x15e5314: LoadField: r0 = r1->field_f
    //     0x15e5314: ldur            w0, [x1, #0xf]
    // 0x15e5318: DecompressPointer r0
    //     0x15e5318: add             x0, x0, HEAP, lsl #32
    // 0x15e531c: cmp             w0, NULL
    // 0x15e5320: b.ne            #0x15e532c
    // 0x15e5324: r0 = Null
    //     0x15e5324: mov             x0, NULL
    // 0x15e5328: b               #0x15e5338
    // 0x15e532c: LoadField: r1 = r0->field_b
    //     0x15e532c: ldur            w1, [x0, #0xb]
    // 0x15e5330: DecompressPointer r1
    //     0x15e5330: add             x1, x1, HEAP, lsl #32
    // 0x15e5334: mov             x0, x1
    // 0x15e5338: cmp             w0, NULL
    // 0x15e533c: b.ne            #0x15e5348
    // 0x15e5340: ldur            x2, [fp, #-8]
    // 0x15e5344: b               #0x15e5464
    // 0x15e5348: tbnz            w0, #4, #0x15e5460
    // 0x15e534c: ldur            x1, [fp, #-8]
    // 0x15e5350: r0 = SvgPicture()
    //     0x15e5350: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e5354: mov             x1, x0
    // 0x15e5358: r2 = "assets/images/shopdeck-tag.svg"
    //     0x15e5358: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd38] "assets/images/shopdeck-tag.svg"
    //     0x15e535c: ldr             x2, [x2, #0xd38]
    // 0x15e5360: stur            x0, [fp, #-0x20]
    // 0x15e5364: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x15e5364: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x15e5368: r0 = SvgPicture.asset()
    //     0x15e5368: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e536c: r0 = InkWell()
    //     0x15e536c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e5370: mov             x3, x0
    // 0x15e5374: ldur            x0, [fp, #-0x20]
    // 0x15e5378: stur            x3, [fp, #-0x38]
    // 0x15e537c: StoreField: r3->field_b = r0
    //     0x15e537c: stur            w0, [x3, #0xb]
    // 0x15e5380: ldur            x2, [fp, #-0x18]
    // 0x15e5384: r1 = Function '<anonymous closure>':.
    //     0x15e5384: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ebf8] AnonymousClosure: (0x15e54b0), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15e5124)
    //     0x15e5388: ldr             x1, [x1, #0xbf8]
    // 0x15e538c: r0 = AllocateClosure()
    //     0x15e538c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e5390: mov             x1, x0
    // 0x15e5394: ldur            x0, [fp, #-0x38]
    // 0x15e5398: StoreField: r0->field_f = r1
    //     0x15e5398: stur            w1, [x0, #0xf]
    // 0x15e539c: r1 = true
    //     0x15e539c: add             x1, NULL, #0x20  ; true
    // 0x15e53a0: StoreField: r0->field_43 = r1
    //     0x15e53a0: stur            w1, [x0, #0x43]
    // 0x15e53a4: r2 = Instance_BoxShape
    //     0x15e53a4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e53a8: ldr             x2, [x2, #0x80]
    // 0x15e53ac: StoreField: r0->field_47 = r2
    //     0x15e53ac: stur            w2, [x0, #0x47]
    // 0x15e53b0: StoreField: r0->field_6f = r1
    //     0x15e53b0: stur            w1, [x0, #0x6f]
    // 0x15e53b4: r2 = false
    //     0x15e53b4: add             x2, NULL, #0x30  ; false
    // 0x15e53b8: StoreField: r0->field_73 = r2
    //     0x15e53b8: stur            w2, [x0, #0x73]
    // 0x15e53bc: StoreField: r0->field_83 = r1
    //     0x15e53bc: stur            w1, [x0, #0x83]
    // 0x15e53c0: StoreField: r0->field_7b = r2
    //     0x15e53c0: stur            w2, [x0, #0x7b]
    // 0x15e53c4: r0 = Padding()
    //     0x15e53c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15e53c8: mov             x2, x0
    // 0x15e53cc: r0 = Instance_EdgeInsets
    //     0x15e53cc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0x15e53d0: ldr             x0, [x0, #0xd48]
    // 0x15e53d4: stur            x2, [fp, #-0x18]
    // 0x15e53d8: StoreField: r2->field_f = r0
    //     0x15e53d8: stur            w0, [x2, #0xf]
    // 0x15e53dc: ldur            x0, [fp, #-0x38]
    // 0x15e53e0: StoreField: r2->field_b = r0
    //     0x15e53e0: stur            w0, [x2, #0xb]
    // 0x15e53e4: ldur            x0, [fp, #-8]
    // 0x15e53e8: LoadField: r1 = r0->field_b
    //     0x15e53e8: ldur            w1, [x0, #0xb]
    // 0x15e53ec: LoadField: r3 = r0->field_f
    //     0x15e53ec: ldur            w3, [x0, #0xf]
    // 0x15e53f0: DecompressPointer r3
    //     0x15e53f0: add             x3, x3, HEAP, lsl #32
    // 0x15e53f4: LoadField: r4 = r3->field_b
    //     0x15e53f4: ldur            w4, [x3, #0xb]
    // 0x15e53f8: r3 = LoadInt32Instr(r1)
    //     0x15e53f8: sbfx            x3, x1, #1, #0x1f
    // 0x15e53fc: stur            x3, [fp, #-0x40]
    // 0x15e5400: r1 = LoadInt32Instr(r4)
    //     0x15e5400: sbfx            x1, x4, #1, #0x1f
    // 0x15e5404: cmp             x3, x1
    // 0x15e5408: b.ne            #0x15e5414
    // 0x15e540c: mov             x1, x0
    // 0x15e5410: r0 = _growToNextCapacity()
    //     0x15e5410: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x15e5414: ldur            x2, [fp, #-8]
    // 0x15e5418: ldur            x3, [fp, #-0x40]
    // 0x15e541c: add             x0, x3, #1
    // 0x15e5420: lsl             x1, x0, #1
    // 0x15e5424: StoreField: r2->field_b = r1
    //     0x15e5424: stur            w1, [x2, #0xb]
    // 0x15e5428: LoadField: r1 = r2->field_f
    //     0x15e5428: ldur            w1, [x2, #0xf]
    // 0x15e542c: DecompressPointer r1
    //     0x15e542c: add             x1, x1, HEAP, lsl #32
    // 0x15e5430: ldur            x0, [fp, #-0x18]
    // 0x15e5434: ArrayStore: r1[r3] = r0  ; List_4
    //     0x15e5434: add             x25, x1, x3, lsl #2
    //     0x15e5438: add             x25, x25, #0xf
    //     0x15e543c: str             w0, [x25]
    //     0x15e5440: tbz             w0, #0, #0x15e545c
    //     0x15e5444: ldurb           w16, [x1, #-1]
    //     0x15e5448: ldurb           w17, [x0, #-1]
    //     0x15e544c: and             x16, x17, x16, lsr #2
    //     0x15e5450: tst             x16, HEAP, lsr #32
    //     0x15e5454: b.eq            #0x15e545c
    //     0x15e5458: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15e545c: b               #0x15e5464
    // 0x15e5460: ldur            x2, [fp, #-8]
    // 0x15e5464: r0 = AppBar()
    //     0x15e5464: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e5468: stur            x0, [fp, #-0x18]
    // 0x15e546c: r16 = 0.000000
    //     0x15e546c: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x15e5470: ldur            lr, [fp, #-0x28]
    // 0x15e5474: stp             lr, x16, [SP, #0x10]
    // 0x15e5478: ldur            x16, [fp, #-0x30]
    // 0x15e547c: ldur            lr, [fp, #-8]
    // 0x15e5480: stp             lr, x16, [SP]
    // 0x15e5484: mov             x1, x0
    // 0x15e5488: ldur            x2, [fp, #-0x10]
    // 0x15e548c: r4 = const [0, 0x6, 0x4, 0x2, actions, 0x5, bottom, 0x4, title, 0x3, titleSpacing, 0x2, null]
    //     0x15e548c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd50] List(13) [0, 0x6, 0x4, 0x2, "actions", 0x5, "bottom", 0x4, "title", 0x3, "titleSpacing", 0x2, Null]
    //     0x15e5490: ldr             x4, [x4, #0xd50]
    // 0x15e5494: r0 = AppBar()
    //     0x15e5494: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e5498: ldur            x0, [fp, #-0x18]
    // 0x15e549c: LeaveFrame
    //     0x15e549c: mov             SP, fp
    //     0x15e54a0: ldp             fp, lr, [SP], #0x10
    // 0x15e54a4: ret
    //     0x15e54a4: ret             
    // 0x15e54a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e54a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e54ac: b               #0x15e514c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15e54b0, size: 0xa8
    // 0x15e54b0: EnterFrame
    //     0x15e54b0: stp             fp, lr, [SP, #-0x10]!
    //     0x15e54b4: mov             fp, SP
    // 0x15e54b8: AllocStack(0x38)
    //     0x15e54b8: sub             SP, SP, #0x38
    // 0x15e54bc: SetupParameters()
    //     0x15e54bc: ldr             x0, [fp, #0x10]
    //     0x15e54c0: ldur            w2, [x0, #0x17]
    //     0x15e54c4: add             x2, x2, HEAP, lsl #32
    //     0x15e54c8: stur            x2, [fp, #-8]
    // 0x15e54cc: CheckStackOverflow
    //     0x15e54cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e54d0: cmp             SP, x16
    //     0x15e54d4: b.ls            #0x15e5550
    // 0x15e54d8: LoadField: r1 = r2->field_f
    //     0x15e54d8: ldur            w1, [x2, #0xf]
    // 0x15e54dc: DecompressPointer r1
    //     0x15e54dc: add             x1, x1, HEAP, lsl #32
    // 0x15e54e0: r0 = controller()
    //     0x15e54e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e54e4: mov             x1, x0
    // 0x15e54e8: r2 = "trusted_badge"
    //     0x15e54e8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd58] "trusted_badge"
    //     0x15e54ec: ldr             x2, [x2, #0xd58]
    // 0x15e54f0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x15e54f0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x15e54f4: r0 = ratingReviewClickedEvent()
    //     0x15e54f4: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0x15e54f8: ldur            x0, [fp, #-8]
    // 0x15e54fc: LoadField: r3 = r0->field_13
    //     0x15e54fc: ldur            w3, [x0, #0x13]
    // 0x15e5500: DecompressPointer r3
    //     0x15e5500: add             x3, x3, HEAP, lsl #32
    // 0x15e5504: stur            x3, [fp, #-0x10]
    // 0x15e5508: r1 = Function '<anonymous closure>':.
    //     0x15e5508: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ec00] AnonymousClosure: (0x999958), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0x15e550c: ldr             x1, [x1, #0xc00]
    // 0x15e5510: r2 = Null
    //     0x15e5510: mov             x2, NULL
    // 0x15e5514: r0 = AllocateClosure()
    //     0x15e5514: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e5518: stp             x0, NULL, [SP, #0x18]
    // 0x15e551c: ldur            x16, [fp, #-0x10]
    // 0x15e5520: r30 = Instance_RoundedRectangleBorder
    //     0x15e5520: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec08] Obj!RoundedRectangleBorder@d5abe1
    //     0x15e5524: ldr             lr, [lr, #0xc08]
    // 0x15e5528: stp             lr, x16, [SP, #8]
    // 0x15e552c: r16 = true
    //     0x15e552c: add             x16, NULL, #0x20  ; true
    // 0x15e5530: str             x16, [SP]
    // 0x15e5534: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x15e5534: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x15e5538: ldr             x4, [x4, #0xd70]
    // 0x15e553c: r0 = showModalBottomSheet()
    //     0x15e553c: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x15e5540: r0 = Null
    //     0x15e5540: mov             x0, NULL
    // 0x15e5544: LeaveFrame
    //     0x15e5544: mov             SP, fp
    //     0x15e5548: ldp             fp, lr, [SP], #0x10
    // 0x15e554c: ret
    //     0x15e554c: ret             
    // 0x15e5550: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e5550: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e5554: b               #0x15e54d8
  }
}
