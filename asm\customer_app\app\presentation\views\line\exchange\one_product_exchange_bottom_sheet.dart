// lib: , url: package:customer_app/app/presentation/views/line/exchange/one_product_exchange_bottom_sheet.dart

// class id: 1049517, size: 0x8
class :: {
}

// class id: 3252, size: 0x14, field offset: 0x14
class _OneProductExchangeBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbdc550, size: 0x514
    // 0xbdc550: EnterFrame
    //     0xbdc550: stp             fp, lr, [SP, #-0x10]!
    //     0xbdc554: mov             fp, SP
    // 0xbdc558: AllocStack(0x48)
    //     0xbdc558: sub             SP, SP, #0x48
    // 0xbdc55c: SetupParameters(_OneProductExchangeBottomSheetState this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0xbdc55c: mov             x0, x1
    //     0xbdc560: mov             x1, x2
    //     0xbdc564: stur            x2, [fp, #-8]
    // 0xbdc568: CheckStackOverflow
    //     0xbdc568: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdc56c: cmp             SP, x16
    //     0xbdc570: b.ls            #0xbdca5c
    // 0xbdc574: r1 = 1
    //     0xbdc574: movz            x1, #0x1
    // 0xbdc578: r0 = AllocateContext()
    //     0xbdc578: bl              #0x16f6108  ; AllocateContextStub
    // 0xbdc57c: ldur            x1, [fp, #-8]
    // 0xbdc580: stur            x0, [fp, #-0x10]
    // 0xbdc584: StoreField: r0->field_f = r1
    //     0xbdc584: stur            w1, [x0, #0xf]
    // 0xbdc588: r0 = of()
    //     0xbdc588: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdc58c: LoadField: r1 = r0->field_87
    //     0xbdc58c: ldur            w1, [x0, #0x87]
    // 0xbdc590: DecompressPointer r1
    //     0xbdc590: add             x1, x1, HEAP, lsl #32
    // 0xbdc594: LoadField: r0 = r1->field_7
    //     0xbdc594: ldur            w0, [x1, #7]
    // 0xbdc598: DecompressPointer r0
    //     0xbdc598: add             x0, x0, HEAP, lsl #32
    // 0xbdc59c: r16 = 16.000000
    //     0xbdc59c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbdc5a0: ldr             x16, [x16, #0x188]
    // 0xbdc5a4: r30 = Instance_Color
    //     0xbdc5a4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbdc5a8: stp             lr, x16, [SP]
    // 0xbdc5ac: mov             x1, x0
    // 0xbdc5b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbdc5b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbdc5b4: ldr             x4, [x4, #0xaa0]
    // 0xbdc5b8: r0 = copyWith()
    //     0xbdc5b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbdc5bc: stur            x0, [fp, #-8]
    // 0xbdc5c0: r0 = Text()
    //     0xbdc5c0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbdc5c4: mov             x2, x0
    // 0xbdc5c8: r0 = "Only one product can be exchanged at a time!"
    //     0xbdc5c8: add             x0, PP, #0x53, lsl #12  ; [pp+0x53978] "Only one product can be exchanged at a time!"
    //     0xbdc5cc: ldr             x0, [x0, #0x978]
    // 0xbdc5d0: stur            x2, [fp, #-0x18]
    // 0xbdc5d4: StoreField: r2->field_b = r0
    //     0xbdc5d4: stur            w0, [x2, #0xb]
    // 0xbdc5d8: ldur            x0, [fp, #-8]
    // 0xbdc5dc: StoreField: r2->field_13 = r0
    //     0xbdc5dc: stur            w0, [x2, #0x13]
    // 0xbdc5e0: r1 = <FlexParentData>
    //     0xbdc5e0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbdc5e4: ldr             x1, [x1, #0xe00]
    // 0xbdc5e8: r0 = Expanded()
    //     0xbdc5e8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbdc5ec: mov             x1, x0
    // 0xbdc5f0: r0 = 1
    //     0xbdc5f0: movz            x0, #0x1
    // 0xbdc5f4: stur            x1, [fp, #-8]
    // 0xbdc5f8: StoreField: r1->field_13 = r0
    //     0xbdc5f8: stur            x0, [x1, #0x13]
    // 0xbdc5fc: r0 = Instance_FlexFit
    //     0xbdc5fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbdc600: ldr             x0, [x0, #0xe08]
    // 0xbdc604: StoreField: r1->field_1b = r0
    //     0xbdc604: stur            w0, [x1, #0x1b]
    // 0xbdc608: ldur            x0, [fp, #-0x18]
    // 0xbdc60c: StoreField: r1->field_b = r0
    //     0xbdc60c: stur            w0, [x1, #0xb]
    // 0xbdc610: r0 = SvgPicture()
    //     0xbdc610: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbdc614: mov             x1, x0
    // 0xbdc618: r2 = "assets/images/x.svg"
    //     0xbdc618: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xbdc61c: ldr             x2, [x2, #0x5e8]
    // 0xbdc620: stur            x0, [fp, #-0x18]
    // 0xbdc624: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbdc624: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbdc628: r0 = SvgPicture.asset()
    //     0xbdc628: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbdc62c: r0 = InkWell()
    //     0xbdc62c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbdc630: mov             x3, x0
    // 0xbdc634: ldur            x0, [fp, #-0x18]
    // 0xbdc638: stur            x3, [fp, #-0x20]
    // 0xbdc63c: StoreField: r3->field_b = r0
    //     0xbdc63c: stur            w0, [x3, #0xb]
    // 0xbdc640: ldur            x2, [fp, #-0x10]
    // 0xbdc644: r1 = Function '<anonymous closure>':.
    //     0xbdc644: add             x1, PP, #0x53, lsl #12  ; [pp+0x53980] AnonymousClosure: (0x99db20), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_showFreeGiftDialog (0x99e89c)
    //     0xbdc648: ldr             x1, [x1, #0x980]
    // 0xbdc64c: r0 = AllocateClosure()
    //     0xbdc64c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdc650: mov             x1, x0
    // 0xbdc654: ldur            x0, [fp, #-0x20]
    // 0xbdc658: StoreField: r0->field_f = r1
    //     0xbdc658: stur            w1, [x0, #0xf]
    // 0xbdc65c: r3 = true
    //     0xbdc65c: add             x3, NULL, #0x20  ; true
    // 0xbdc660: StoreField: r0->field_43 = r3
    //     0xbdc660: stur            w3, [x0, #0x43]
    // 0xbdc664: r1 = Instance_BoxShape
    //     0xbdc664: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbdc668: ldr             x1, [x1, #0x80]
    // 0xbdc66c: StoreField: r0->field_47 = r1
    //     0xbdc66c: stur            w1, [x0, #0x47]
    // 0xbdc670: StoreField: r0->field_6f = r3
    //     0xbdc670: stur            w3, [x0, #0x6f]
    // 0xbdc674: r4 = false
    //     0xbdc674: add             x4, NULL, #0x30  ; false
    // 0xbdc678: StoreField: r0->field_73 = r4
    //     0xbdc678: stur            w4, [x0, #0x73]
    // 0xbdc67c: StoreField: r0->field_83 = r3
    //     0xbdc67c: stur            w3, [x0, #0x83]
    // 0xbdc680: StoreField: r0->field_7b = r4
    //     0xbdc680: stur            w4, [x0, #0x7b]
    // 0xbdc684: r1 = Null
    //     0xbdc684: mov             x1, NULL
    // 0xbdc688: r2 = 4
    //     0xbdc688: movz            x2, #0x4
    // 0xbdc68c: r0 = AllocateArray()
    //     0xbdc68c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdc690: mov             x2, x0
    // 0xbdc694: ldur            x0, [fp, #-8]
    // 0xbdc698: stur            x2, [fp, #-0x18]
    // 0xbdc69c: StoreField: r2->field_f = r0
    //     0xbdc69c: stur            w0, [x2, #0xf]
    // 0xbdc6a0: ldur            x0, [fp, #-0x20]
    // 0xbdc6a4: StoreField: r2->field_13 = r0
    //     0xbdc6a4: stur            w0, [x2, #0x13]
    // 0xbdc6a8: r1 = <Widget>
    //     0xbdc6a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdc6ac: r0 = AllocateGrowableArray()
    //     0xbdc6ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbdc6b0: mov             x1, x0
    // 0xbdc6b4: ldur            x0, [fp, #-0x18]
    // 0xbdc6b8: stur            x1, [fp, #-8]
    // 0xbdc6bc: StoreField: r1->field_f = r0
    //     0xbdc6bc: stur            w0, [x1, #0xf]
    // 0xbdc6c0: r0 = 4
    //     0xbdc6c0: movz            x0, #0x4
    // 0xbdc6c4: StoreField: r1->field_b = r0
    //     0xbdc6c4: stur            w0, [x1, #0xb]
    // 0xbdc6c8: r0 = Row()
    //     0xbdc6c8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbdc6cc: mov             x2, x0
    // 0xbdc6d0: r0 = Instance_Axis
    //     0xbdc6d0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbdc6d4: stur            x2, [fp, #-0x18]
    // 0xbdc6d8: StoreField: r2->field_f = r0
    //     0xbdc6d8: stur            w0, [x2, #0xf]
    // 0xbdc6dc: r0 = Instance_MainAxisAlignment
    //     0xbdc6dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbdc6e0: ldr             x0, [x0, #0xa08]
    // 0xbdc6e4: StoreField: r2->field_13 = r0
    //     0xbdc6e4: stur            w0, [x2, #0x13]
    // 0xbdc6e8: r3 = Instance_MainAxisSize
    //     0xbdc6e8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbdc6ec: ldr             x3, [x3, #0xa10]
    // 0xbdc6f0: ArrayStore: r2[0] = r3  ; List_4
    //     0xbdc6f0: stur            w3, [x2, #0x17]
    // 0xbdc6f4: r1 = Instance_CrossAxisAlignment
    //     0xbdc6f4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbdc6f8: ldr             x1, [x1, #0xa18]
    // 0xbdc6fc: StoreField: r2->field_1b = r1
    //     0xbdc6fc: stur            w1, [x2, #0x1b]
    // 0xbdc700: r4 = Instance_VerticalDirection
    //     0xbdc700: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbdc704: ldr             x4, [x4, #0xa20]
    // 0xbdc708: StoreField: r2->field_23 = r4
    //     0xbdc708: stur            w4, [x2, #0x23]
    // 0xbdc70c: r5 = Instance_Clip
    //     0xbdc70c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbdc710: ldr             x5, [x5, #0x38]
    // 0xbdc714: StoreField: r2->field_2b = r5
    //     0xbdc714: stur            w5, [x2, #0x2b]
    // 0xbdc718: StoreField: r2->field_2f = rZR
    //     0xbdc718: stur            xzr, [x2, #0x2f]
    // 0xbdc71c: ldur            x1, [fp, #-8]
    // 0xbdc720: StoreField: r2->field_b = r1
    //     0xbdc720: stur            w1, [x2, #0xb]
    // 0xbdc724: ldur            x6, [fp, #-0x10]
    // 0xbdc728: LoadField: r1 = r6->field_f
    //     0xbdc728: ldur            w1, [x6, #0xf]
    // 0xbdc72c: DecompressPointer r1
    //     0xbdc72c: add             x1, x1, HEAP, lsl #32
    // 0xbdc730: r0 = of()
    //     0xbdc730: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdc734: LoadField: r1 = r0->field_87
    //     0xbdc734: ldur            w1, [x0, #0x87]
    // 0xbdc738: DecompressPointer r1
    //     0xbdc738: add             x1, x1, HEAP, lsl #32
    // 0xbdc73c: LoadField: r0 = r1->field_2b
    //     0xbdc73c: ldur            w0, [x1, #0x2b]
    // 0xbdc740: DecompressPointer r0
    //     0xbdc740: add             x0, x0, HEAP, lsl #32
    // 0xbdc744: stur            x0, [fp, #-8]
    // 0xbdc748: r1 = Instance_Color
    //     0xbdc748: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbdc74c: d0 = 0.700000
    //     0xbdc74c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbdc750: ldr             d0, [x17, #0xf48]
    // 0xbdc754: r0 = withOpacity()
    //     0xbdc754: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbdc758: r16 = 14.000000
    //     0xbdc758: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbdc75c: ldr             x16, [x16, #0x1d8]
    // 0xbdc760: stp             x0, x16, [SP]
    // 0xbdc764: ldur            x1, [fp, #-8]
    // 0xbdc768: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbdc768: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbdc76c: ldr             x4, [x4, #0xaa0]
    // 0xbdc770: r0 = copyWith()
    //     0xbdc770: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbdc774: stur            x0, [fp, #-8]
    // 0xbdc778: r0 = Text()
    //     0xbdc778: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbdc77c: mov             x1, x0
    // 0xbdc780: r0 = "One product is already up for exchange. To exchange this product, please exchange the other item first"
    //     0xbdc780: add             x0, PP, #0x53, lsl #12  ; [pp+0x53988] "One product is already up for exchange. To exchange this product, please exchange the other item first"
    //     0xbdc784: ldr             x0, [x0, #0x988]
    // 0xbdc788: stur            x1, [fp, #-0x20]
    // 0xbdc78c: StoreField: r1->field_b = r0
    //     0xbdc78c: stur            w0, [x1, #0xb]
    // 0xbdc790: ldur            x0, [fp, #-8]
    // 0xbdc794: StoreField: r1->field_13 = r0
    //     0xbdc794: stur            w0, [x1, #0x13]
    // 0xbdc798: r16 = <EdgeInsets>
    //     0xbdc798: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbdc79c: ldr             x16, [x16, #0xda0]
    // 0xbdc7a0: r30 = Instance_EdgeInsets
    //     0xbdc7a0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbdc7a4: ldr             lr, [lr, #0x1f0]
    // 0xbdc7a8: stp             lr, x16, [SP]
    // 0xbdc7ac: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdc7ac: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdc7b0: r0 = all()
    //     0xbdc7b0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdc7b4: mov             x2, x0
    // 0xbdc7b8: ldur            x0, [fp, #-0x10]
    // 0xbdc7bc: stur            x2, [fp, #-8]
    // 0xbdc7c0: LoadField: r1 = r0->field_f
    //     0xbdc7c0: ldur            w1, [x0, #0xf]
    // 0xbdc7c4: DecompressPointer r1
    //     0xbdc7c4: add             x1, x1, HEAP, lsl #32
    // 0xbdc7c8: r0 = of()
    //     0xbdc7c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdc7cc: LoadField: r1 = r0->field_5b
    //     0xbdc7cc: ldur            w1, [x0, #0x5b]
    // 0xbdc7d0: DecompressPointer r1
    //     0xbdc7d0: add             x1, x1, HEAP, lsl #32
    // 0xbdc7d4: r16 = <Color>
    //     0xbdc7d4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbdc7d8: ldr             x16, [x16, #0xf80]
    // 0xbdc7dc: stp             x1, x16, [SP]
    // 0xbdc7e0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdc7e0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdc7e4: r0 = all()
    //     0xbdc7e4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdc7e8: stur            x0, [fp, #-0x28]
    // 0xbdc7ec: r16 = <RoundedRectangleBorder>
    //     0xbdc7ec: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbdc7f0: ldr             x16, [x16, #0xf78]
    // 0xbdc7f4: r30 = Instance_RoundedRectangleBorder
    //     0xbdc7f4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbdc7f8: ldr             lr, [lr, #0xd68]
    // 0xbdc7fc: stp             lr, x16, [SP]
    // 0xbdc800: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdc800: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdc804: r0 = all()
    //     0xbdc804: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdc808: stur            x0, [fp, #-0x30]
    // 0xbdc80c: r0 = ButtonStyle()
    //     0xbdc80c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbdc810: mov             x1, x0
    // 0xbdc814: ldur            x0, [fp, #-0x28]
    // 0xbdc818: stur            x1, [fp, #-0x38]
    // 0xbdc81c: StoreField: r1->field_b = r0
    //     0xbdc81c: stur            w0, [x1, #0xb]
    // 0xbdc820: ldur            x0, [fp, #-8]
    // 0xbdc824: StoreField: r1->field_23 = r0
    //     0xbdc824: stur            w0, [x1, #0x23]
    // 0xbdc828: ldur            x0, [fp, #-0x30]
    // 0xbdc82c: StoreField: r1->field_43 = r0
    //     0xbdc82c: stur            w0, [x1, #0x43]
    // 0xbdc830: r0 = TextButtonThemeData()
    //     0xbdc830: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbdc834: mov             x2, x0
    // 0xbdc838: ldur            x0, [fp, #-0x38]
    // 0xbdc83c: stur            x2, [fp, #-8]
    // 0xbdc840: StoreField: r2->field_7 = r0
    //     0xbdc840: stur            w0, [x2, #7]
    // 0xbdc844: r1 = "Got it!"
    //     0xbdc844: add             x1, PP, #0x53, lsl #12  ; [pp+0x53990] "Got it!"
    //     0xbdc848: ldr             x1, [x1, #0x990]
    // 0xbdc84c: r0 = capitalizeFirstWord()
    //     0xbdc84c: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xbdc850: mov             x2, x0
    // 0xbdc854: ldur            x0, [fp, #-0x10]
    // 0xbdc858: stur            x2, [fp, #-0x28]
    // 0xbdc85c: LoadField: r1 = r0->field_f
    //     0xbdc85c: ldur            w1, [x0, #0xf]
    // 0xbdc860: DecompressPointer r1
    //     0xbdc860: add             x1, x1, HEAP, lsl #32
    // 0xbdc864: r0 = of()
    //     0xbdc864: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdc868: LoadField: r1 = r0->field_87
    //     0xbdc868: ldur            w1, [x0, #0x87]
    // 0xbdc86c: DecompressPointer r1
    //     0xbdc86c: add             x1, x1, HEAP, lsl #32
    // 0xbdc870: LoadField: r0 = r1->field_7
    //     0xbdc870: ldur            w0, [x1, #7]
    // 0xbdc874: DecompressPointer r0
    //     0xbdc874: add             x0, x0, HEAP, lsl #32
    // 0xbdc878: r16 = 16.000000
    //     0xbdc878: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbdc87c: ldr             x16, [x16, #0x188]
    // 0xbdc880: r30 = Instance_Color
    //     0xbdc880: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbdc884: stp             lr, x16, [SP]
    // 0xbdc888: mov             x1, x0
    // 0xbdc88c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbdc88c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbdc890: ldr             x4, [x4, #0xaa0]
    // 0xbdc894: r0 = copyWith()
    //     0xbdc894: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbdc898: stur            x0, [fp, #-0x10]
    // 0xbdc89c: r0 = Text()
    //     0xbdc89c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbdc8a0: mov             x3, x0
    // 0xbdc8a4: ldur            x0, [fp, #-0x28]
    // 0xbdc8a8: stur            x3, [fp, #-0x30]
    // 0xbdc8ac: StoreField: r3->field_b = r0
    //     0xbdc8ac: stur            w0, [x3, #0xb]
    // 0xbdc8b0: ldur            x0, [fp, #-0x10]
    // 0xbdc8b4: StoreField: r3->field_13 = r0
    //     0xbdc8b4: stur            w0, [x3, #0x13]
    // 0xbdc8b8: r1 = Function '<anonymous closure>':.
    //     0xbdc8b8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53998] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbdc8bc: ldr             x1, [x1, #0x998]
    // 0xbdc8c0: r2 = Null
    //     0xbdc8c0: mov             x2, NULL
    // 0xbdc8c4: r0 = AllocateClosure()
    //     0xbdc8c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdc8c8: stur            x0, [fp, #-0x10]
    // 0xbdc8cc: r0 = TextButton()
    //     0xbdc8cc: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbdc8d0: mov             x1, x0
    // 0xbdc8d4: ldur            x0, [fp, #-0x10]
    // 0xbdc8d8: stur            x1, [fp, #-0x28]
    // 0xbdc8dc: StoreField: r1->field_b = r0
    //     0xbdc8dc: stur            w0, [x1, #0xb]
    // 0xbdc8e0: r0 = false
    //     0xbdc8e0: add             x0, NULL, #0x30  ; false
    // 0xbdc8e4: StoreField: r1->field_27 = r0
    //     0xbdc8e4: stur            w0, [x1, #0x27]
    // 0xbdc8e8: r2 = true
    //     0xbdc8e8: add             x2, NULL, #0x20  ; true
    // 0xbdc8ec: StoreField: r1->field_2f = r2
    //     0xbdc8ec: stur            w2, [x1, #0x2f]
    // 0xbdc8f0: ldur            x2, [fp, #-0x30]
    // 0xbdc8f4: StoreField: r1->field_37 = r2
    //     0xbdc8f4: stur            w2, [x1, #0x37]
    // 0xbdc8f8: r0 = TextButtonTheme()
    //     0xbdc8f8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbdc8fc: mov             x1, x0
    // 0xbdc900: ldur            x0, [fp, #-8]
    // 0xbdc904: stur            x1, [fp, #-0x10]
    // 0xbdc908: StoreField: r1->field_f = r0
    //     0xbdc908: stur            w0, [x1, #0xf]
    // 0xbdc90c: ldur            x0, [fp, #-0x28]
    // 0xbdc910: StoreField: r1->field_b = r0
    //     0xbdc910: stur            w0, [x1, #0xb]
    // 0xbdc914: r0 = SizedBox()
    //     0xbdc914: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbdc918: mov             x3, x0
    // 0xbdc91c: r0 = inf
    //     0xbdc91c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbdc920: ldr             x0, [x0, #0x9f8]
    // 0xbdc924: stur            x3, [fp, #-8]
    // 0xbdc928: StoreField: r3->field_f = r0
    //     0xbdc928: stur            w0, [x3, #0xf]
    // 0xbdc92c: ldur            x0, [fp, #-0x10]
    // 0xbdc930: StoreField: r3->field_b = r0
    //     0xbdc930: stur            w0, [x3, #0xb]
    // 0xbdc934: r1 = Null
    //     0xbdc934: mov             x1, NULL
    // 0xbdc938: r2 = 10
    //     0xbdc938: movz            x2, #0xa
    // 0xbdc93c: r0 = AllocateArray()
    //     0xbdc93c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdc940: mov             x2, x0
    // 0xbdc944: ldur            x0, [fp, #-0x18]
    // 0xbdc948: stur            x2, [fp, #-0x10]
    // 0xbdc94c: StoreField: r2->field_f = r0
    //     0xbdc94c: stur            w0, [x2, #0xf]
    // 0xbdc950: r16 = Instance_SizedBox
    //     0xbdc950: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xbdc954: ldr             x16, [x16, #0x578]
    // 0xbdc958: StoreField: r2->field_13 = r16
    //     0xbdc958: stur            w16, [x2, #0x13]
    // 0xbdc95c: ldur            x0, [fp, #-0x20]
    // 0xbdc960: ArrayStore: r2[0] = r0  ; List_4
    //     0xbdc960: stur            w0, [x2, #0x17]
    // 0xbdc964: r16 = Instance_SizedBox
    //     0xbdc964: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xbdc968: ldr             x16, [x16, #0xc70]
    // 0xbdc96c: StoreField: r2->field_1b = r16
    //     0xbdc96c: stur            w16, [x2, #0x1b]
    // 0xbdc970: ldur            x0, [fp, #-8]
    // 0xbdc974: StoreField: r2->field_1f = r0
    //     0xbdc974: stur            w0, [x2, #0x1f]
    // 0xbdc978: r1 = <Widget>
    //     0xbdc978: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdc97c: r0 = AllocateGrowableArray()
    //     0xbdc97c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbdc980: mov             x1, x0
    // 0xbdc984: ldur            x0, [fp, #-0x10]
    // 0xbdc988: stur            x1, [fp, #-8]
    // 0xbdc98c: StoreField: r1->field_f = r0
    //     0xbdc98c: stur            w0, [x1, #0xf]
    // 0xbdc990: r0 = 10
    //     0xbdc990: movz            x0, #0xa
    // 0xbdc994: StoreField: r1->field_b = r0
    //     0xbdc994: stur            w0, [x1, #0xb]
    // 0xbdc998: r0 = Column()
    //     0xbdc998: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbdc99c: mov             x1, x0
    // 0xbdc9a0: r0 = Instance_Axis
    //     0xbdc9a0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbdc9a4: stur            x1, [fp, #-0x10]
    // 0xbdc9a8: StoreField: r1->field_f = r0
    //     0xbdc9a8: stur            w0, [x1, #0xf]
    // 0xbdc9ac: r2 = Instance_MainAxisAlignment
    //     0xbdc9ac: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbdc9b0: ldr             x2, [x2, #0xa08]
    // 0xbdc9b4: StoreField: r1->field_13 = r2
    //     0xbdc9b4: stur            w2, [x1, #0x13]
    // 0xbdc9b8: r2 = Instance_MainAxisSize
    //     0xbdc9b8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbdc9bc: ldr             x2, [x2, #0xa10]
    // 0xbdc9c0: ArrayStore: r1[0] = r2  ; List_4
    //     0xbdc9c0: stur            w2, [x1, #0x17]
    // 0xbdc9c4: r2 = Instance_CrossAxisAlignment
    //     0xbdc9c4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbdc9c8: ldr             x2, [x2, #0x890]
    // 0xbdc9cc: StoreField: r1->field_1b = r2
    //     0xbdc9cc: stur            w2, [x1, #0x1b]
    // 0xbdc9d0: r2 = Instance_VerticalDirection
    //     0xbdc9d0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbdc9d4: ldr             x2, [x2, #0xa20]
    // 0xbdc9d8: StoreField: r1->field_23 = r2
    //     0xbdc9d8: stur            w2, [x1, #0x23]
    // 0xbdc9dc: r2 = Instance_Clip
    //     0xbdc9dc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbdc9e0: ldr             x2, [x2, #0x38]
    // 0xbdc9e4: StoreField: r1->field_2b = r2
    //     0xbdc9e4: stur            w2, [x1, #0x2b]
    // 0xbdc9e8: StoreField: r1->field_2f = rZR
    //     0xbdc9e8: stur            xzr, [x1, #0x2f]
    // 0xbdc9ec: ldur            x2, [fp, #-8]
    // 0xbdc9f0: StoreField: r1->field_b = r2
    //     0xbdc9f0: stur            w2, [x1, #0xb]
    // 0xbdc9f4: r0 = Padding()
    //     0xbdc9f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbdc9f8: mov             x1, x0
    // 0xbdc9fc: r0 = Instance_EdgeInsets
    //     0xbdc9fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbdca00: ldr             x0, [x0, #0x1f0]
    // 0xbdca04: stur            x1, [fp, #-8]
    // 0xbdca08: StoreField: r1->field_f = r0
    //     0xbdca08: stur            w0, [x1, #0xf]
    // 0xbdca0c: ldur            x0, [fp, #-0x10]
    // 0xbdca10: StoreField: r1->field_b = r0
    //     0xbdca10: stur            w0, [x1, #0xb]
    // 0xbdca14: r0 = SingleChildScrollView()
    //     0xbdca14: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xbdca18: r1 = Instance_Axis
    //     0xbdca18: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbdca1c: StoreField: r0->field_b = r1
    //     0xbdca1c: stur            w1, [x0, #0xb]
    // 0xbdca20: r1 = false
    //     0xbdca20: add             x1, NULL, #0x30  ; false
    // 0xbdca24: StoreField: r0->field_f = r1
    //     0xbdca24: stur            w1, [x0, #0xf]
    // 0xbdca28: ldur            x1, [fp, #-8]
    // 0xbdca2c: StoreField: r0->field_23 = r1
    //     0xbdca2c: stur            w1, [x0, #0x23]
    // 0xbdca30: r1 = Instance_DragStartBehavior
    //     0xbdca30: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xbdca34: StoreField: r0->field_27 = r1
    //     0xbdca34: stur            w1, [x0, #0x27]
    // 0xbdca38: r1 = Instance_Clip
    //     0xbdca38: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbdca3c: ldr             x1, [x1, #0x7e0]
    // 0xbdca40: StoreField: r0->field_2b = r1
    //     0xbdca40: stur            w1, [x0, #0x2b]
    // 0xbdca44: r1 = Instance_HitTestBehavior
    //     0xbdca44: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xbdca48: ldr             x1, [x1, #0x288]
    // 0xbdca4c: StoreField: r0->field_2f = r1
    //     0xbdca4c: stur            w1, [x0, #0x2f]
    // 0xbdca50: LeaveFrame
    //     0xbdca50: mov             SP, fp
    //     0xbdca54: ldp             fp, lr, [SP], #0x10
    // 0xbdca58: ret
    //     0xbdca58: ret             
    // 0xbdca5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdca5c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdca60: b               #0xbdc574
  }
}

// class id: 3998, size: 0xc, field offset: 0xc
//   const constructor, 
class OneProductExchangeBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc8095c, size: 0x24
    // 0xc8095c: EnterFrame
    //     0xc8095c: stp             fp, lr, [SP, #-0x10]!
    //     0xc80960: mov             fp, SP
    // 0xc80964: mov             x0, x1
    // 0xc80968: r1 = <OneProductExchangeBottomSheet>
    //     0xc80968: add             x1, PP, #0x48, lsl #12  ; [pp+0x48440] TypeArguments: <OneProductExchangeBottomSheet>
    //     0xc8096c: ldr             x1, [x1, #0x440]
    // 0xc80970: r0 = _OneProductExchangeBottomSheetState()
    //     0xc80970: bl              #0xc80980  ; Allocate_OneProductExchangeBottomSheetStateStub -> _OneProductExchangeBottomSheetState (size=0x14)
    // 0xc80974: LeaveFrame
    //     0xc80974: mov             SP, fp
    //     0xc80978: ldp             fp, lr, [SP], #0x10
    // 0xc8097c: ret
    //     0xc8097c: ret             
  }
}
