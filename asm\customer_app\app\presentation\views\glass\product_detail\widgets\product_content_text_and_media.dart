// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/product_content_text_and_media.dart

// class id: 1049435, size: 0x8
class :: {
}

// class id: 3316, size: 0x14, field offset: 0x14
class _ProductContentTextAndMediaState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb817e8, size: 0x13c
    // 0xb817e8: EnterFrame
    //     0xb817e8: stp             fp, lr, [SP, #-0x10]!
    //     0xb817ec: mov             fp, SP
    // 0xb817f0: AllocStack(0x28)
    //     0xb817f0: sub             SP, SP, #0x28
    // 0xb817f4: SetupParameters(_ProductContentTextAndMediaState this /* r1 => r1, fp-0x8 */)
    //     0xb817f4: stur            x1, [fp, #-8]
    // 0xb817f8: CheckStackOverflow
    //     0xb817f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb817fc: cmp             SP, x16
    //     0xb81800: b.ls            #0xb81918
    // 0xb81804: r1 = 1
    //     0xb81804: movz            x1, #0x1
    // 0xb81808: r0 = AllocateContext()
    //     0xb81808: bl              #0x16f6108  ; AllocateContextStub
    // 0xb8180c: mov             x1, x0
    // 0xb81810: ldur            x0, [fp, #-8]
    // 0xb81814: StoreField: r1->field_f = r0
    //     0xb81814: stur            w0, [x1, #0xf]
    // 0xb81818: LoadField: r2 = r0->field_b
    //     0xb81818: ldur            w2, [x0, #0xb]
    // 0xb8181c: DecompressPointer r2
    //     0xb8181c: add             x2, x2, HEAP, lsl #32
    // 0xb81820: cmp             w2, NULL
    // 0xb81824: b.eq            #0xb81920
    // 0xb81828: LoadField: r0 = r2->field_b
    //     0xb81828: ldur            w0, [x2, #0xb]
    // 0xb8182c: DecompressPointer r0
    //     0xb8182c: add             x0, x0, HEAP, lsl #32
    // 0xb81830: LoadField: r3 = r0->field_b
    //     0xb81830: ldur            w3, [x0, #0xb]
    // 0xb81834: mov             x2, x1
    // 0xb81838: stur            x3, [fp, #-8]
    // 0xb8183c: r1 = Function '<anonymous closure>':.
    //     0xb8183c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55778] AnonymousClosure: (0xb81944), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_content_text_and_media.dart] _ProductContentTextAndMediaState::build (0xb817e8)
    //     0xb81840: ldr             x1, [x1, #0x778]
    // 0xb81844: r0 = AllocateClosure()
    //     0xb81844: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb81848: stur            x0, [fp, #-0x10]
    // 0xb8184c: r0 = ListView()
    //     0xb8184c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb81850: stur            x0, [fp, #-0x18]
    // 0xb81854: r16 = true
    //     0xb81854: add             x16, NULL, #0x20  ; true
    // 0xb81858: r30 = Instance_NeverScrollableScrollPhysics
    //     0xb81858: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb8185c: ldr             lr, [lr, #0x1c8]
    // 0xb81860: stp             lr, x16, [SP]
    // 0xb81864: mov             x1, x0
    // 0xb81868: ldur            x2, [fp, #-0x10]
    // 0xb8186c: ldur            x3, [fp, #-8]
    // 0xb81870: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xb81870: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb81874: ldr             x4, [x4, #8]
    // 0xb81878: r0 = ListView.builder()
    //     0xb81878: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb8187c: r1 = Null
    //     0xb8187c: mov             x1, NULL
    // 0xb81880: r2 = 2
    //     0xb81880: movz            x2, #0x2
    // 0xb81884: r0 = AllocateArray()
    //     0xb81884: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb81888: mov             x2, x0
    // 0xb8188c: ldur            x0, [fp, #-0x18]
    // 0xb81890: stur            x2, [fp, #-8]
    // 0xb81894: StoreField: r2->field_f = r0
    //     0xb81894: stur            w0, [x2, #0xf]
    // 0xb81898: r1 = <Widget>
    //     0xb81898: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8189c: r0 = AllocateGrowableArray()
    //     0xb8189c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb818a0: mov             x1, x0
    // 0xb818a4: ldur            x0, [fp, #-8]
    // 0xb818a8: stur            x1, [fp, #-0x10]
    // 0xb818ac: StoreField: r1->field_f = r0
    //     0xb818ac: stur            w0, [x1, #0xf]
    // 0xb818b0: r0 = 2
    //     0xb818b0: movz            x0, #0x2
    // 0xb818b4: StoreField: r1->field_b = r0
    //     0xb818b4: stur            w0, [x1, #0xb]
    // 0xb818b8: r0 = Column()
    //     0xb818b8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb818bc: r1 = Instance_Axis
    //     0xb818bc: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb818c0: StoreField: r0->field_f = r1
    //     0xb818c0: stur            w1, [x0, #0xf]
    // 0xb818c4: r1 = Instance_MainAxisAlignment
    //     0xb818c4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb818c8: ldr             x1, [x1, #0xa08]
    // 0xb818cc: StoreField: r0->field_13 = r1
    //     0xb818cc: stur            w1, [x0, #0x13]
    // 0xb818d0: r1 = Instance_MainAxisSize
    //     0xb818d0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb818d4: ldr             x1, [x1, #0xa10]
    // 0xb818d8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb818d8: stur            w1, [x0, #0x17]
    // 0xb818dc: r1 = Instance_CrossAxisAlignment
    //     0xb818dc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb818e0: ldr             x1, [x1, #0x890]
    // 0xb818e4: StoreField: r0->field_1b = r1
    //     0xb818e4: stur            w1, [x0, #0x1b]
    // 0xb818e8: r1 = Instance_VerticalDirection
    //     0xb818e8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb818ec: ldr             x1, [x1, #0xa20]
    // 0xb818f0: StoreField: r0->field_23 = r1
    //     0xb818f0: stur            w1, [x0, #0x23]
    // 0xb818f4: r1 = Instance_Clip
    //     0xb818f4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb818f8: ldr             x1, [x1, #0x38]
    // 0xb818fc: StoreField: r0->field_2b = r1
    //     0xb818fc: stur            w1, [x0, #0x2b]
    // 0xb81900: StoreField: r0->field_2f = rZR
    //     0xb81900: stur            xzr, [x0, #0x2f]
    // 0xb81904: ldur            x1, [fp, #-0x10]
    // 0xb81908: StoreField: r0->field_b = r1
    //     0xb81908: stur            w1, [x0, #0xb]
    // 0xb8190c: LeaveFrame
    //     0xb8190c: mov             SP, fp
    //     0xb81910: ldp             fp, lr, [SP], #0x10
    // 0xb81914: ret
    //     0xb81914: ret             
    // 0xb81918: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb81918: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8191c: b               #0xb81804
    // 0xb81920: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb81920: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Card <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb81944, size: 0x1224
    // 0xb81944: EnterFrame
    //     0xb81944: stp             fp, lr, [SP, #-0x10]!
    //     0xb81948: mov             fp, SP
    // 0xb8194c: AllocStack(0x78)
    //     0xb8194c: sub             SP, SP, #0x78
    // 0xb81950: SetupParameters()
    //     0xb81950: ldr             x0, [fp, #0x20]
    //     0xb81954: ldur            w1, [x0, #0x17]
    //     0xb81958: add             x1, x1, HEAP, lsl #32
    //     0xb8195c: stur            x1, [fp, #-8]
    // 0xb81960: CheckStackOverflow
    //     0xb81960: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb81964: cmp             SP, x16
    //     0xb81968: b.ls            #0xb82b08
    // 0xb8196c: r0 = Radius()
    //     0xb8196c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb81970: d0 = 20.000000
    //     0xb81970: fmov            d0, #20.00000000
    // 0xb81974: stur            x0, [fp, #-0x10]
    // 0xb81978: StoreField: r0->field_7 = d0
    //     0xb81978: stur            d0, [x0, #7]
    // 0xb8197c: StoreField: r0->field_f = d0
    //     0xb8197c: stur            d0, [x0, #0xf]
    // 0xb81980: r0 = BorderRadius()
    //     0xb81980: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb81984: mov             x1, x0
    // 0xb81988: ldur            x0, [fp, #-0x10]
    // 0xb8198c: stur            x1, [fp, #-0x18]
    // 0xb81990: StoreField: r1->field_7 = r0
    //     0xb81990: stur            w0, [x1, #7]
    // 0xb81994: StoreField: r1->field_b = r0
    //     0xb81994: stur            w0, [x1, #0xb]
    // 0xb81998: StoreField: r1->field_f = r0
    //     0xb81998: stur            w0, [x1, #0xf]
    // 0xb8199c: StoreField: r1->field_13 = r0
    //     0xb8199c: stur            w0, [x1, #0x13]
    // 0xb819a0: r0 = RoundedRectangleBorder()
    //     0xb819a0: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb819a4: mov             x2, x0
    // 0xb819a8: ldur            x0, [fp, #-0x18]
    // 0xb819ac: stur            x2, [fp, #-0x10]
    // 0xb819b0: StoreField: r2->field_b = r0
    //     0xb819b0: stur            w0, [x2, #0xb]
    // 0xb819b4: r3 = Instance_BorderSide
    //     0xb819b4: add             x3, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb819b8: ldr             x3, [x3, #0xe20]
    // 0xb819bc: StoreField: r2->field_7 = r3
    //     0xb819bc: stur            w3, [x2, #7]
    // 0xb819c0: ldur            x4, [fp, #-8]
    // 0xb819c4: LoadField: r0 = r4->field_f
    //     0xb819c4: ldur            w0, [x4, #0xf]
    // 0xb819c8: DecompressPointer r0
    //     0xb819c8: add             x0, x0, HEAP, lsl #32
    // 0xb819cc: LoadField: r1 = r0->field_b
    //     0xb819cc: ldur            w1, [x0, #0xb]
    // 0xb819d0: DecompressPointer r1
    //     0xb819d0: add             x1, x1, HEAP, lsl #32
    // 0xb819d4: cmp             w1, NULL
    // 0xb819d8: b.eq            #0xb82b10
    // 0xb819dc: LoadField: r5 = r1->field_b
    //     0xb819dc: ldur            w5, [x1, #0xb]
    // 0xb819e0: DecompressPointer r5
    //     0xb819e0: add             x5, x5, HEAP, lsl #32
    // 0xb819e4: LoadField: r0 = r5->field_b
    //     0xb819e4: ldur            w0, [x5, #0xb]
    // 0xb819e8: ldr             x1, [fp, #0x10]
    // 0xb819ec: r6 = LoadInt32Instr(r1)
    //     0xb819ec: sbfx            x6, x1, #1, #0x1f
    //     0xb819f0: tbz             w1, #0, #0xb819f8
    //     0xb819f4: ldur            x6, [x1, #7]
    // 0xb819f8: stur            x6, [fp, #-0x20]
    // 0xb819fc: r1 = LoadInt32Instr(r0)
    //     0xb819fc: sbfx            x1, x0, #1, #0x1f
    // 0xb81a00: mov             x0, x1
    // 0xb81a04: mov             x1, x6
    // 0xb81a08: cmp             x1, x0
    // 0xb81a0c: b.hs            #0xb82b14
    // 0xb81a10: LoadField: r0 = r5->field_f
    //     0xb81a10: ldur            w0, [x5, #0xf]
    // 0xb81a14: DecompressPointer r0
    //     0xb81a14: add             x0, x0, HEAP, lsl #32
    // 0xb81a18: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb81a18: add             x16, x0, x6, lsl #2
    //     0xb81a1c: ldur            w1, [x16, #0xf]
    // 0xb81a20: DecompressPointer r1
    //     0xb81a20: add             x1, x1, HEAP, lsl #32
    // 0xb81a24: LoadField: r0 = r1->field_27
    //     0xb81a24: ldur            w0, [x1, #0x27]
    // 0xb81a28: DecompressPointer r0
    //     0xb81a28: add             x0, x0, HEAP, lsl #32
    // 0xb81a2c: cmp             w0, NULL
    // 0xb81a30: b.ne            #0xb81a3c
    // 0xb81a34: r0 = Null
    //     0xb81a34: mov             x0, NULL
    // 0xb81a38: b               #0xb81a48
    // 0xb81a3c: LoadField: r1 = r0->field_f
    //     0xb81a3c: ldur            w1, [x0, #0xf]
    // 0xb81a40: DecompressPointer r1
    //     0xb81a40: add             x1, x1, HEAP, lsl #32
    // 0xb81a44: mov             x0, x1
    // 0xb81a48: r1 = LoadClassIdInstr(r0)
    //     0xb81a48: ldur            x1, [x0, #-1]
    //     0xb81a4c: ubfx            x1, x1, #0xc, #0x14
    // 0xb81a50: r16 = "left"
    //     0xb81a50: ldr             x16, [PP, #0x6df8]  ; [pp+0x6df8] "left"
    // 0xb81a54: stp             x16, x0, [SP]
    // 0xb81a58: mov             x0, x1
    // 0xb81a5c: mov             lr, x0
    // 0xb81a60: ldr             lr, [x21, lr, lsl #3]
    // 0xb81a64: blr             lr
    // 0xb81a68: tbnz            w0, #4, #0xb82248
    // 0xb81a6c: ldur            x0, [fp, #-8]
    // 0xb81a70: ldur            x1, [fp, #-0x20]
    // 0xb81a74: r0 = Radius()
    //     0xb81a74: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb81a78: d0 = 20.000000
    //     0xb81a78: fmov            d0, #20.00000000
    // 0xb81a7c: stur            x0, [fp, #-0x18]
    // 0xb81a80: StoreField: r0->field_7 = d0
    //     0xb81a80: stur            d0, [x0, #7]
    // 0xb81a84: StoreField: r0->field_f = d0
    //     0xb81a84: stur            d0, [x0, #0xf]
    // 0xb81a88: r0 = BorderRadius()
    //     0xb81a88: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb81a8c: mov             x3, x0
    // 0xb81a90: ldur            x0, [fp, #-0x18]
    // 0xb81a94: stur            x3, [fp, #-0x28]
    // 0xb81a98: StoreField: r3->field_7 = r0
    //     0xb81a98: stur            w0, [x3, #7]
    // 0xb81a9c: StoreField: r3->field_b = r0
    //     0xb81a9c: stur            w0, [x3, #0xb]
    // 0xb81aa0: StoreField: r3->field_f = r0
    //     0xb81aa0: stur            w0, [x3, #0xf]
    // 0xb81aa4: StoreField: r3->field_13 = r0
    //     0xb81aa4: stur            w0, [x3, #0x13]
    // 0xb81aa8: ldur            x4, [fp, #-8]
    // 0xb81aac: LoadField: r0 = r4->field_f
    //     0xb81aac: ldur            w0, [x4, #0xf]
    // 0xb81ab0: DecompressPointer r0
    //     0xb81ab0: add             x0, x0, HEAP, lsl #32
    // 0xb81ab4: LoadField: r1 = r0->field_b
    //     0xb81ab4: ldur            w1, [x0, #0xb]
    // 0xb81ab8: DecompressPointer r1
    //     0xb81ab8: add             x1, x1, HEAP, lsl #32
    // 0xb81abc: cmp             w1, NULL
    // 0xb81ac0: b.eq            #0xb82b18
    // 0xb81ac4: LoadField: r2 = r1->field_b
    //     0xb81ac4: ldur            w2, [x1, #0xb]
    // 0xb81ac8: DecompressPointer r2
    //     0xb81ac8: add             x2, x2, HEAP, lsl #32
    // 0xb81acc: LoadField: r0 = r2->field_b
    //     0xb81acc: ldur            w0, [x2, #0xb]
    // 0xb81ad0: r1 = LoadInt32Instr(r0)
    //     0xb81ad0: sbfx            x1, x0, #1, #0x1f
    // 0xb81ad4: mov             x0, x1
    // 0xb81ad8: ldur            x1, [fp, #-0x20]
    // 0xb81adc: cmp             x1, x0
    // 0xb81ae0: b.hs            #0xb82b1c
    // 0xb81ae4: LoadField: r0 = r2->field_f
    //     0xb81ae4: ldur            w0, [x2, #0xf]
    // 0xb81ae8: DecompressPointer r0
    //     0xb81ae8: add             x0, x0, HEAP, lsl #32
    // 0xb81aec: ldur            x5, [fp, #-0x20]
    // 0xb81af0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb81af0: add             x16, x0, x5, lsl #2
    //     0xb81af4: ldur            w1, [x16, #0xf]
    // 0xb81af8: DecompressPointer r1
    //     0xb81af8: add             x1, x1, HEAP, lsl #32
    // 0xb81afc: LoadField: r0 = r1->field_13
    //     0xb81afc: ldur            w0, [x1, #0x13]
    // 0xb81b00: DecompressPointer r0
    //     0xb81b00: add             x0, x0, HEAP, lsl #32
    // 0xb81b04: cmp             w0, NULL
    // 0xb81b08: b.ne            #0xb81b10
    // 0xb81b0c: r0 = ""
    //     0xb81b0c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb81b10: stur            x0, [fp, #-0x18]
    // 0xb81b14: r1 = Function '<anonymous closure>':.
    //     0xb81b14: add             x1, PP, #0x55, lsl #12  ; [pp+0x55780] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb81b18: ldr             x1, [x1, #0x780]
    // 0xb81b1c: r2 = Null
    //     0xb81b1c: mov             x2, NULL
    // 0xb81b20: r0 = AllocateClosure()
    //     0xb81b20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb81b24: r1 = Function '<anonymous closure>':.
    //     0xb81b24: add             x1, PP, #0x55, lsl #12  ; [pp+0x55788] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb81b28: ldr             x1, [x1, #0x788]
    // 0xb81b2c: r2 = Null
    //     0xb81b2c: mov             x2, NULL
    // 0xb81b30: stur            x0, [fp, #-0x30]
    // 0xb81b34: r0 = AllocateClosure()
    //     0xb81b34: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb81b38: stur            x0, [fp, #-0x38]
    // 0xb81b3c: r0 = CachedNetworkImage()
    //     0xb81b3c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb81b40: stur            x0, [fp, #-0x40]
    // 0xb81b44: ldur            x16, [fp, #-0x30]
    // 0xb81b48: ldur            lr, [fp, #-0x38]
    // 0xb81b4c: stp             lr, x16, [SP, #8]
    // 0xb81b50: r16 = Instance_BoxFit
    //     0xb81b50: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb81b54: ldr             x16, [x16, #0xb18]
    // 0xb81b58: str             x16, [SP]
    // 0xb81b5c: mov             x1, x0
    // 0xb81b60: ldur            x2, [fp, #-0x18]
    // 0xb81b64: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x3, fit, 0x4, progressIndicatorBuilder, 0x2, null]
    //     0xb81b64: add             x4, PP, #0x55, lsl #12  ; [pp+0x55790] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x3, "fit", 0x4, "progressIndicatorBuilder", 0x2, Null]
    //     0xb81b68: ldr             x4, [x4, #0x790]
    // 0xb81b6c: r0 = CachedNetworkImage()
    //     0xb81b6c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb81b70: r0 = ClipRRect()
    //     0xb81b70: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb81b74: mov             x2, x0
    // 0xb81b78: ldur            x0, [fp, #-0x28]
    // 0xb81b7c: stur            x2, [fp, #-0x18]
    // 0xb81b80: StoreField: r2->field_f = r0
    //     0xb81b80: stur            w0, [x2, #0xf]
    // 0xb81b84: r3 = Instance_Clip
    //     0xb81b84: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb81b88: ldr             x3, [x3, #0x138]
    // 0xb81b8c: ArrayStore: r2[0] = r3  ; List_4
    //     0xb81b8c: stur            w3, [x2, #0x17]
    // 0xb81b90: ldur            x0, [fp, #-0x40]
    // 0xb81b94: StoreField: r2->field_b = r0
    //     0xb81b94: stur            w0, [x2, #0xb]
    // 0xb81b98: r1 = <FlexParentData>
    //     0xb81b98: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb81b9c: ldr             x1, [x1, #0xe00]
    // 0xb81ba0: r0 = Expanded()
    //     0xb81ba0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb81ba4: mov             x3, x0
    // 0xb81ba8: r2 = 2
    //     0xb81ba8: movz            x2, #0x2
    // 0xb81bac: stur            x3, [fp, #-0x28]
    // 0xb81bb0: StoreField: r3->field_13 = r2
    //     0xb81bb0: stur            x2, [x3, #0x13]
    // 0xb81bb4: r4 = Instance_FlexFit
    //     0xb81bb4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb81bb8: ldr             x4, [x4, #0xe08]
    // 0xb81bbc: StoreField: r3->field_1b = r4
    //     0xb81bbc: stur            w4, [x3, #0x1b]
    // 0xb81bc0: ldur            x0, [fp, #-0x18]
    // 0xb81bc4: StoreField: r3->field_b = r0
    //     0xb81bc4: stur            w0, [x3, #0xb]
    // 0xb81bc8: ldur            x5, [fp, #-8]
    // 0xb81bcc: LoadField: r0 = r5->field_f
    //     0xb81bcc: ldur            w0, [x5, #0xf]
    // 0xb81bd0: DecompressPointer r0
    //     0xb81bd0: add             x0, x0, HEAP, lsl #32
    // 0xb81bd4: LoadField: r1 = r0->field_b
    //     0xb81bd4: ldur            w1, [x0, #0xb]
    // 0xb81bd8: DecompressPointer r1
    //     0xb81bd8: add             x1, x1, HEAP, lsl #32
    // 0xb81bdc: cmp             w1, NULL
    // 0xb81be0: b.eq            #0xb82b20
    // 0xb81be4: LoadField: r6 = r1->field_b
    //     0xb81be4: ldur            w6, [x1, #0xb]
    // 0xb81be8: DecompressPointer r6
    //     0xb81be8: add             x6, x6, HEAP, lsl #32
    // 0xb81bec: LoadField: r0 = r6->field_b
    //     0xb81bec: ldur            w0, [x6, #0xb]
    // 0xb81bf0: r1 = LoadInt32Instr(r0)
    //     0xb81bf0: sbfx            x1, x0, #1, #0x1f
    // 0xb81bf4: mov             x0, x1
    // 0xb81bf8: ldur            x1, [fp, #-0x20]
    // 0xb81bfc: cmp             x1, x0
    // 0xb81c00: b.hs            #0xb82b24
    // 0xb81c04: LoadField: r0 = r6->field_f
    //     0xb81c04: ldur            w0, [x6, #0xf]
    // 0xb81c08: DecompressPointer r0
    //     0xb81c08: add             x0, x0, HEAP, lsl #32
    // 0xb81c0c: ldur            x6, [fp, #-0x20]
    // 0xb81c10: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb81c10: add             x16, x0, x6, lsl #2
    //     0xb81c14: ldur            w1, [x16, #0xf]
    // 0xb81c18: DecompressPointer r1
    //     0xb81c18: add             x1, x1, HEAP, lsl #32
    // 0xb81c1c: LoadField: r0 = r1->field_7
    //     0xb81c1c: ldur            w0, [x1, #7]
    // 0xb81c20: DecompressPointer r0
    //     0xb81c20: add             x0, x0, HEAP, lsl #32
    // 0xb81c24: cmp             w0, NULL
    // 0xb81c28: b.ne            #0xb81c30
    // 0xb81c2c: r0 = ""
    //     0xb81c2c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb81c30: ldr             x1, [fp, #0x18]
    // 0xb81c34: stur            x0, [fp, #-0x18]
    // 0xb81c38: r0 = of()
    //     0xb81c38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb81c3c: LoadField: r1 = r0->field_87
    //     0xb81c3c: ldur            w1, [x0, #0x87]
    // 0xb81c40: DecompressPointer r1
    //     0xb81c40: add             x1, x1, HEAP, lsl #32
    // 0xb81c44: LoadField: r0 = r1->field_7
    //     0xb81c44: ldur            w0, [x1, #7]
    // 0xb81c48: DecompressPointer r0
    //     0xb81c48: add             x0, x0, HEAP, lsl #32
    // 0xb81c4c: stur            x0, [fp, #-0x30]
    // 0xb81c50: r1 = Instance_Color
    //     0xb81c50: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb81c54: d0 = 0.700000
    //     0xb81c54: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb81c58: ldr             d0, [x17, #0xf48]
    // 0xb81c5c: r0 = withOpacity()
    //     0xb81c5c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb81c60: r16 = 16.000000
    //     0xb81c60: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb81c64: ldr             x16, [x16, #0x188]
    // 0xb81c68: stp             x0, x16, [SP]
    // 0xb81c6c: ldur            x1, [fp, #-0x30]
    // 0xb81c70: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb81c70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb81c74: ldr             x4, [x4, #0xaa0]
    // 0xb81c78: r0 = copyWith()
    //     0xb81c78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb81c7c: stur            x0, [fp, #-0x30]
    // 0xb81c80: r0 = Text()
    //     0xb81c80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb81c84: mov             x1, x0
    // 0xb81c88: ldur            x0, [fp, #-0x18]
    // 0xb81c8c: stur            x1, [fp, #-0x38]
    // 0xb81c90: StoreField: r1->field_b = r0
    //     0xb81c90: stur            w0, [x1, #0xb]
    // 0xb81c94: ldur            x0, [fp, #-0x30]
    // 0xb81c98: StoreField: r1->field_13 = r0
    //     0xb81c98: stur            w0, [x1, #0x13]
    // 0xb81c9c: r0 = Padding()
    //     0xb81c9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb81ca0: mov             x3, x0
    // 0xb81ca4: r2 = Instance_EdgeInsets
    //     0xb81ca4: add             x2, PP, #0x55, lsl #12  ; [pp+0x55798] Obj!EdgeInsets@d593c1
    //     0xb81ca8: ldr             x2, [x2, #0x798]
    // 0xb81cac: stur            x3, [fp, #-0x30]
    // 0xb81cb0: StoreField: r3->field_f = r2
    //     0xb81cb0: stur            w2, [x3, #0xf]
    // 0xb81cb4: ldur            x0, [fp, #-0x38]
    // 0xb81cb8: StoreField: r3->field_b = r0
    //     0xb81cb8: stur            w0, [x3, #0xb]
    // 0xb81cbc: ldur            x2, [fp, #-8]
    // 0xb81cc0: LoadField: r0 = r2->field_f
    //     0xb81cc0: ldur            w0, [x2, #0xf]
    // 0xb81cc4: DecompressPointer r0
    //     0xb81cc4: add             x0, x0, HEAP, lsl #32
    // 0xb81cc8: LoadField: r1 = r0->field_b
    //     0xb81cc8: ldur            w1, [x0, #0xb]
    // 0xb81ccc: DecompressPointer r1
    //     0xb81ccc: add             x1, x1, HEAP, lsl #32
    // 0xb81cd0: cmp             w1, NULL
    // 0xb81cd4: b.eq            #0xb82b28
    // 0xb81cd8: LoadField: r4 = r1->field_b
    //     0xb81cd8: ldur            w4, [x1, #0xb]
    // 0xb81cdc: DecompressPointer r4
    //     0xb81cdc: add             x4, x4, HEAP, lsl #32
    // 0xb81ce0: LoadField: r0 = r4->field_b
    //     0xb81ce0: ldur            w0, [x4, #0xb]
    // 0xb81ce4: r1 = LoadInt32Instr(r0)
    //     0xb81ce4: sbfx            x1, x0, #1, #0x1f
    // 0xb81ce8: mov             x0, x1
    // 0xb81cec: ldur            x1, [fp, #-0x20]
    // 0xb81cf0: cmp             x1, x0
    // 0xb81cf4: b.hs            #0xb82b2c
    // 0xb81cf8: LoadField: r0 = r4->field_f
    //     0xb81cf8: ldur            w0, [x4, #0xf]
    // 0xb81cfc: DecompressPointer r0
    //     0xb81cfc: add             x0, x0, HEAP, lsl #32
    // 0xb81d00: ldur            x4, [fp, #-0x20]
    // 0xb81d04: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb81d04: add             x16, x0, x4, lsl #2
    //     0xb81d08: ldur            w1, [x16, #0xf]
    // 0xb81d0c: DecompressPointer r1
    //     0xb81d0c: add             x1, x1, HEAP, lsl #32
    // 0xb81d10: LoadField: r0 = r1->field_b
    //     0xb81d10: ldur            w0, [x1, #0xb]
    // 0xb81d14: DecompressPointer r0
    //     0xb81d14: add             x0, x0, HEAP, lsl #32
    // 0xb81d18: cmp             w0, NULL
    // 0xb81d1c: b.ne            #0xb81d24
    // 0xb81d20: r0 = ""
    //     0xb81d20: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb81d24: ldr             x1, [fp, #0x18]
    // 0xb81d28: stur            x0, [fp, #-0x18]
    // 0xb81d2c: r0 = of()
    //     0xb81d2c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb81d30: LoadField: r1 = r0->field_87
    //     0xb81d30: ldur            w1, [x0, #0x87]
    // 0xb81d34: DecompressPointer r1
    //     0xb81d34: add             x1, x1, HEAP, lsl #32
    // 0xb81d38: LoadField: r0 = r1->field_2b
    //     0xb81d38: ldur            w0, [x1, #0x2b]
    // 0xb81d3c: DecompressPointer r0
    //     0xb81d3c: add             x0, x0, HEAP, lsl #32
    // 0xb81d40: stur            x0, [fp, #-0x38]
    // 0xb81d44: r1 = Instance_Color
    //     0xb81d44: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb81d48: d0 = 0.400000
    //     0xb81d48: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb81d4c: r0 = withOpacity()
    //     0xb81d4c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb81d50: r16 = 12.000000
    //     0xb81d50: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb81d54: ldr             x16, [x16, #0x9e8]
    // 0xb81d58: stp             x16, x0, [SP]
    // 0xb81d5c: ldur            x1, [fp, #-0x38]
    // 0xb81d60: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb81d60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb81d64: ldr             x4, [x4, #0x9b8]
    // 0xb81d68: r0 = copyWith()
    //     0xb81d68: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb81d6c: stur            x0, [fp, #-0x38]
    // 0xb81d70: r0 = HtmlWidget()
    //     0xb81d70: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xb81d74: mov             x1, x0
    // 0xb81d78: ldur            x0, [fp, #-0x18]
    // 0xb81d7c: stur            x1, [fp, #-0x40]
    // 0xb81d80: StoreField: r1->field_1f = r0
    //     0xb81d80: stur            w0, [x1, #0x1f]
    // 0xb81d84: r4 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xb81d84: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xb81d88: ldr             x4, [x4, #0x1e0]
    // 0xb81d8c: StoreField: r1->field_23 = r4
    //     0xb81d8c: stur            w4, [x1, #0x23]
    // 0xb81d90: r5 = Instance_ColumnMode
    //     0xb81d90: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xb81d94: ldr             x5, [x5, #0x1e8]
    // 0xb81d98: StoreField: r1->field_3b = r5
    //     0xb81d98: stur            w5, [x1, #0x3b]
    // 0xb81d9c: ldur            x0, [fp, #-0x38]
    // 0xb81da0: StoreField: r1->field_3f = r0
    //     0xb81da0: stur            w0, [x1, #0x3f]
    // 0xb81da4: r0 = Container()
    //     0xb81da4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb81da8: stur            x0, [fp, #-0x18]
    // 0xb81dac: r16 = 150.000000
    //     0xb81dac: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb81db0: ldr             x16, [x16, #0x690]
    // 0xb81db4: r30 = Instance_EdgeInsets
    //     0xb81db4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe18] Obj!EdgeInsets@d57ef1
    //     0xb81db8: ldr             lr, [lr, #0xe18]
    // 0xb81dbc: stp             lr, x16, [SP, #8]
    // 0xb81dc0: ldur            x16, [fp, #-0x40]
    // 0xb81dc4: str             x16, [SP]
    // 0xb81dc8: mov             x1, x0
    // 0xb81dcc: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, padding, 0x2, width, 0x1, null]
    //     0xb81dcc: add             x4, PP, #0x44, lsl #12  ; [pp+0x44260] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "padding", 0x2, "width", 0x1, Null]
    //     0xb81dd0: ldr             x4, [x4, #0x260]
    // 0xb81dd4: r0 = Container()
    //     0xb81dd4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb81dd8: ldur            x2, [fp, #-8]
    // 0xb81ddc: LoadField: r0 = r2->field_f
    //     0xb81ddc: ldur            w0, [x2, #0xf]
    // 0xb81de0: DecompressPointer r0
    //     0xb81de0: add             x0, x0, HEAP, lsl #32
    // 0xb81de4: LoadField: r1 = r0->field_b
    //     0xb81de4: ldur            w1, [x0, #0xb]
    // 0xb81de8: DecompressPointer r1
    //     0xb81de8: add             x1, x1, HEAP, lsl #32
    // 0xb81dec: cmp             w1, NULL
    // 0xb81df0: b.eq            #0xb82b30
    // 0xb81df4: LoadField: r3 = r1->field_b
    //     0xb81df4: ldur            w3, [x1, #0xb]
    // 0xb81df8: DecompressPointer r3
    //     0xb81df8: add             x3, x3, HEAP, lsl #32
    // 0xb81dfc: LoadField: r0 = r3->field_b
    //     0xb81dfc: ldur            w0, [x3, #0xb]
    // 0xb81e00: r1 = LoadInt32Instr(r0)
    //     0xb81e00: sbfx            x1, x0, #1, #0x1f
    // 0xb81e04: mov             x0, x1
    // 0xb81e08: ldur            x1, [fp, #-0x20]
    // 0xb81e0c: cmp             x1, x0
    // 0xb81e10: b.hs            #0xb82b34
    // 0xb81e14: LoadField: r0 = r3->field_f
    //     0xb81e14: ldur            w0, [x3, #0xf]
    // 0xb81e18: DecompressPointer r0
    //     0xb81e18: add             x0, x0, HEAP, lsl #32
    // 0xb81e1c: ldur            x3, [fp, #-0x20]
    // 0xb81e20: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb81e20: add             x16, x0, x3, lsl #2
    //     0xb81e24: ldur            w1, [x16, #0xf]
    // 0xb81e28: DecompressPointer r1
    //     0xb81e28: add             x1, x1, HEAP, lsl #32
    // 0xb81e2c: LoadField: r0 = r1->field_f
    //     0xb81e2c: ldur            w0, [x1, #0xf]
    // 0xb81e30: DecompressPointer r0
    //     0xb81e30: add             x0, x0, HEAP, lsl #32
    // 0xb81e34: cmp             w0, NULL
    // 0xb81e38: b.ne            #0xb81e44
    // 0xb81e3c: r0 = Null
    //     0xb81e3c: mov             x0, NULL
    // 0xb81e40: b               #0xb81e58
    // 0xb81e44: LoadField: r1 = r0->field_7
    //     0xb81e44: ldur            w1, [x0, #7]
    // 0xb81e48: cbnz            w1, #0xb81e54
    // 0xb81e4c: r0 = false
    //     0xb81e4c: add             x0, NULL, #0x30  ; false
    // 0xb81e50: b               #0xb81e58
    // 0xb81e54: r0 = true
    //     0xb81e54: add             x0, NULL, #0x20  ; true
    // 0xb81e58: cmp             w0, NULL
    // 0xb81e5c: b.ne            #0xb81e64
    // 0xb81e60: r0 = false
    //     0xb81e60: add             x0, NULL, #0x30  ; false
    // 0xb81e64: ldr             x1, [fp, #0x18]
    // 0xb81e68: stur            x0, [fp, #-0x38]
    // 0xb81e6c: r0 = of()
    //     0xb81e6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb81e70: r17 = 307
    //     0xb81e70: movz            x17, #0x133
    // 0xb81e74: ldr             w1, [x0, x17]
    // 0xb81e78: DecompressPointer r1
    //     0xb81e78: add             x1, x1, HEAP, lsl #32
    // 0xb81e7c: stur            x1, [fp, #-0x40]
    // 0xb81e80: r16 = <EdgeInsets>
    //     0xb81e80: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb81e84: ldr             x16, [x16, #0xda0]
    // 0xb81e88: r30 = Instance_EdgeInsets
    //     0xb81e88: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f528] Obj!EdgeInsets@d593f1
    //     0xb81e8c: ldr             lr, [lr, #0x528]
    // 0xb81e90: stp             lr, x16, [SP]
    // 0xb81e94: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb81e94: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb81e98: r0 = all()
    //     0xb81e98: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb81e9c: stur            x0, [fp, #-0x48]
    // 0xb81ea0: r0 = Radius()
    //     0xb81ea0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb81ea4: d0 = 20.000000
    //     0xb81ea4: fmov            d0, #20.00000000
    // 0xb81ea8: stur            x0, [fp, #-0x50]
    // 0xb81eac: StoreField: r0->field_7 = d0
    //     0xb81eac: stur            d0, [x0, #7]
    // 0xb81eb0: StoreField: r0->field_f = d0
    //     0xb81eb0: stur            d0, [x0, #0xf]
    // 0xb81eb4: r0 = BorderRadius()
    //     0xb81eb4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb81eb8: mov             x1, x0
    // 0xb81ebc: ldur            x0, [fp, #-0x50]
    // 0xb81ec0: stur            x1, [fp, #-0x58]
    // 0xb81ec4: StoreField: r1->field_7 = r0
    //     0xb81ec4: stur            w0, [x1, #7]
    // 0xb81ec8: StoreField: r1->field_b = r0
    //     0xb81ec8: stur            w0, [x1, #0xb]
    // 0xb81ecc: StoreField: r1->field_f = r0
    //     0xb81ecc: stur            w0, [x1, #0xf]
    // 0xb81ed0: StoreField: r1->field_13 = r0
    //     0xb81ed0: stur            w0, [x1, #0x13]
    // 0xb81ed4: r0 = RoundedRectangleBorder()
    //     0xb81ed4: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb81ed8: mov             x1, x0
    // 0xb81edc: ldur            x0, [fp, #-0x58]
    // 0xb81ee0: StoreField: r1->field_b = r0
    //     0xb81ee0: stur            w0, [x1, #0xb]
    // 0xb81ee4: r6 = Instance_BorderSide
    //     0xb81ee4: add             x6, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb81ee8: ldr             x6, [x6, #0xe20]
    // 0xb81eec: StoreField: r1->field_7 = r6
    //     0xb81eec: stur            w6, [x1, #7]
    // 0xb81ef0: r16 = <RoundedRectangleBorder>
    //     0xb81ef0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb81ef4: ldr             x16, [x16, #0xf78]
    // 0xb81ef8: stp             x1, x16, [SP]
    // 0xb81efc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb81efc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb81f00: r0 = all()
    //     0xb81f00: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb81f04: stur            x0, [fp, #-0x50]
    // 0xb81f08: r0 = ButtonStyle()
    //     0xb81f08: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb81f0c: mov             x2, x0
    // 0xb81f10: ldur            x0, [fp, #-0x48]
    // 0xb81f14: stur            x2, [fp, #-0x58]
    // 0xb81f18: StoreField: r2->field_23 = r0
    //     0xb81f18: stur            w0, [x2, #0x23]
    // 0xb81f1c: ldur            x0, [fp, #-0x50]
    // 0xb81f20: StoreField: r2->field_43 = r0
    //     0xb81f20: stur            w0, [x2, #0x43]
    // 0xb81f24: ldur            x7, [fp, #-8]
    // 0xb81f28: LoadField: r0 = r7->field_f
    //     0xb81f28: ldur            w0, [x7, #0xf]
    // 0xb81f2c: DecompressPointer r0
    //     0xb81f2c: add             x0, x0, HEAP, lsl #32
    // 0xb81f30: LoadField: r1 = r0->field_b
    //     0xb81f30: ldur            w1, [x0, #0xb]
    // 0xb81f34: DecompressPointer r1
    //     0xb81f34: add             x1, x1, HEAP, lsl #32
    // 0xb81f38: cmp             w1, NULL
    // 0xb81f3c: b.eq            #0xb82b38
    // 0xb81f40: LoadField: r3 = r1->field_b
    //     0xb81f40: ldur            w3, [x1, #0xb]
    // 0xb81f44: DecompressPointer r3
    //     0xb81f44: add             x3, x3, HEAP, lsl #32
    // 0xb81f48: LoadField: r0 = r3->field_b
    //     0xb81f48: ldur            w0, [x3, #0xb]
    // 0xb81f4c: r1 = LoadInt32Instr(r0)
    //     0xb81f4c: sbfx            x1, x0, #1, #0x1f
    // 0xb81f50: mov             x0, x1
    // 0xb81f54: ldur            x1, [fp, #-0x20]
    // 0xb81f58: cmp             x1, x0
    // 0xb81f5c: b.hs            #0xb82b3c
    // 0xb81f60: LoadField: r0 = r3->field_f
    //     0xb81f60: ldur            w0, [x3, #0xf]
    // 0xb81f64: DecompressPointer r0
    //     0xb81f64: add             x0, x0, HEAP, lsl #32
    // 0xb81f68: ldur            x8, [fp, #-0x20]
    // 0xb81f6c: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xb81f6c: add             x16, x0, x8, lsl #2
    //     0xb81f70: ldur            w1, [x16, #0xf]
    // 0xb81f74: DecompressPointer r1
    //     0xb81f74: add             x1, x1, HEAP, lsl #32
    // 0xb81f78: LoadField: r0 = r1->field_f
    //     0xb81f78: ldur            w0, [x1, #0xf]
    // 0xb81f7c: DecompressPointer r0
    //     0xb81f7c: add             x0, x0, HEAP, lsl #32
    // 0xb81f80: cmp             w0, NULL
    // 0xb81f84: b.ne            #0xb81f8c
    // 0xb81f88: r0 = ""
    //     0xb81f88: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb81f8c: ldr             x1, [fp, #0x18]
    // 0xb81f90: stur            x0, [fp, #-0x48]
    // 0xb81f94: r0 = of()
    //     0xb81f94: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb81f98: LoadField: r1 = r0->field_87
    //     0xb81f98: ldur            w1, [x0, #0x87]
    // 0xb81f9c: DecompressPointer r1
    //     0xb81f9c: add             x1, x1, HEAP, lsl #32
    // 0xb81fa0: LoadField: r0 = r1->field_2f
    //     0xb81fa0: ldur            w0, [x1, #0x2f]
    // 0xb81fa4: DecompressPointer r0
    //     0xb81fa4: add             x0, x0, HEAP, lsl #32
    // 0xb81fa8: cmp             w0, NULL
    // 0xb81fac: b.ne            #0xb81fb8
    // 0xb81fb0: r7 = Null
    //     0xb81fb0: mov             x7, NULL
    // 0xb81fb4: b               #0xb81fdc
    // 0xb81fb8: r16 = Instance_Color
    //     0xb81fb8: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb81fbc: r30 = 14.000000
    //     0xb81fbc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb81fc0: ldr             lr, [lr, #0x1d8]
    // 0xb81fc4: stp             lr, x16, [SP]
    // 0xb81fc8: mov             x1, x0
    // 0xb81fcc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb81fcc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb81fd0: ldr             x4, [x4, #0x9b8]
    // 0xb81fd4: r0 = copyWith()
    //     0xb81fd4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb81fd8: mov             x7, x0
    // 0xb81fdc: ldur            x6, [fp, #-0x28]
    // 0xb81fe0: ldur            x5, [fp, #-0x30]
    // 0xb81fe4: ldur            x4, [fp, #-0x18]
    // 0xb81fe8: ldur            x3, [fp, #-0x38]
    // 0xb81fec: ldur            x2, [fp, #-0x40]
    // 0xb81ff0: ldur            x0, [fp, #-0x58]
    // 0xb81ff4: ldur            x1, [fp, #-0x48]
    // 0xb81ff8: stur            x7, [fp, #-0x50]
    // 0xb81ffc: r0 = Text()
    //     0xb81ffc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb82000: mov             x3, x0
    // 0xb82004: ldur            x0, [fp, #-0x48]
    // 0xb82008: stur            x3, [fp, #-0x60]
    // 0xb8200c: StoreField: r3->field_b = r0
    //     0xb8200c: stur            w0, [x3, #0xb]
    // 0xb82010: ldur            x0, [fp, #-0x50]
    // 0xb82014: StoreField: r3->field_13 = r0
    //     0xb82014: stur            w0, [x3, #0x13]
    // 0xb82018: r1 = Function '<anonymous closure>':.
    //     0xb82018: add             x1, PP, #0x55, lsl #12  ; [pp+0x557a0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb8201c: ldr             x1, [x1, #0x7a0]
    // 0xb82020: r2 = Null
    //     0xb82020: mov             x2, NULL
    // 0xb82024: r0 = AllocateClosure()
    //     0xb82024: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb82028: stur            x0, [fp, #-0x48]
    // 0xb8202c: r0 = TextButton()
    //     0xb8202c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb82030: mov             x1, x0
    // 0xb82034: ldur            x0, [fp, #-0x48]
    // 0xb82038: stur            x1, [fp, #-0x50]
    // 0xb8203c: StoreField: r1->field_b = r0
    //     0xb8203c: stur            w0, [x1, #0xb]
    // 0xb82040: ldur            x0, [fp, #-0x58]
    // 0xb82044: StoreField: r1->field_1b = r0
    //     0xb82044: stur            w0, [x1, #0x1b]
    // 0xb82048: r0 = false
    //     0xb82048: add             x0, NULL, #0x30  ; false
    // 0xb8204c: StoreField: r1->field_27 = r0
    //     0xb8204c: stur            w0, [x1, #0x27]
    // 0xb82050: r2 = true
    //     0xb82050: add             x2, NULL, #0x20  ; true
    // 0xb82054: StoreField: r1->field_2f = r2
    //     0xb82054: stur            w2, [x1, #0x2f]
    // 0xb82058: ldur            x3, [fp, #-0x60]
    // 0xb8205c: StoreField: r1->field_37 = r3
    //     0xb8205c: stur            w3, [x1, #0x37]
    // 0xb82060: r0 = TextButtonTheme()
    //     0xb82060: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb82064: mov             x1, x0
    // 0xb82068: ldur            x0, [fp, #-0x40]
    // 0xb8206c: stur            x1, [fp, #-0x48]
    // 0xb82070: StoreField: r1->field_f = r0
    //     0xb82070: stur            w0, [x1, #0xf]
    // 0xb82074: ldur            x0, [fp, #-0x50]
    // 0xb82078: StoreField: r1->field_b = r0
    //     0xb82078: stur            w0, [x1, #0xb]
    // 0xb8207c: r0 = Padding()
    //     0xb8207c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb82080: r9 = Instance_EdgeInsets
    //     0xb82080: add             x9, PP, #0x55, lsl #12  ; [pp+0x557a8] Obj!EdgeInsets@d59391
    //     0xb82084: ldr             x9, [x9, #0x7a8]
    // 0xb82088: stur            x0, [fp, #-0x40]
    // 0xb8208c: StoreField: r0->field_f = r9
    //     0xb8208c: stur            w9, [x0, #0xf]
    // 0xb82090: ldur            x1, [fp, #-0x48]
    // 0xb82094: StoreField: r0->field_b = r1
    //     0xb82094: stur            w1, [x0, #0xb]
    // 0xb82098: r0 = Visibility()
    //     0xb82098: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb8209c: mov             x3, x0
    // 0xb820a0: ldur            x0, [fp, #-0x40]
    // 0xb820a4: stur            x3, [fp, #-0x48]
    // 0xb820a8: StoreField: r3->field_b = r0
    //     0xb820a8: stur            w0, [x3, #0xb]
    // 0xb820ac: r10 = Instance_SizedBox
    //     0xb820ac: ldr             x10, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb820b0: StoreField: r3->field_f = r10
    //     0xb820b0: stur            w10, [x3, #0xf]
    // 0xb820b4: ldur            x0, [fp, #-0x38]
    // 0xb820b8: StoreField: r3->field_13 = r0
    //     0xb820b8: stur            w0, [x3, #0x13]
    // 0xb820bc: r11 = false
    //     0xb820bc: add             x11, NULL, #0x30  ; false
    // 0xb820c0: ArrayStore: r3[0] = r11  ; List_4
    //     0xb820c0: stur            w11, [x3, #0x17]
    // 0xb820c4: StoreField: r3->field_1b = r11
    //     0xb820c4: stur            w11, [x3, #0x1b]
    // 0xb820c8: StoreField: r3->field_1f = r11
    //     0xb820c8: stur            w11, [x3, #0x1f]
    // 0xb820cc: StoreField: r3->field_23 = r11
    //     0xb820cc: stur            w11, [x3, #0x23]
    // 0xb820d0: StoreField: r3->field_27 = r11
    //     0xb820d0: stur            w11, [x3, #0x27]
    // 0xb820d4: StoreField: r3->field_2b = r11
    //     0xb820d4: stur            w11, [x3, #0x2b]
    // 0xb820d8: r1 = Null
    //     0xb820d8: mov             x1, NULL
    // 0xb820dc: r2 = 6
    //     0xb820dc: movz            x2, #0x6
    // 0xb820e0: r0 = AllocateArray()
    //     0xb820e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb820e4: mov             x2, x0
    // 0xb820e8: ldur            x0, [fp, #-0x30]
    // 0xb820ec: stur            x2, [fp, #-0x38]
    // 0xb820f0: StoreField: r2->field_f = r0
    //     0xb820f0: stur            w0, [x2, #0xf]
    // 0xb820f4: ldur            x0, [fp, #-0x18]
    // 0xb820f8: StoreField: r2->field_13 = r0
    //     0xb820f8: stur            w0, [x2, #0x13]
    // 0xb820fc: ldur            x0, [fp, #-0x48]
    // 0xb82100: ArrayStore: r2[0] = r0  ; List_4
    //     0xb82100: stur            w0, [x2, #0x17]
    // 0xb82104: r1 = <Widget>
    //     0xb82104: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb82108: r0 = AllocateGrowableArray()
    //     0xb82108: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8210c: mov             x1, x0
    // 0xb82110: ldur            x0, [fp, #-0x38]
    // 0xb82114: stur            x1, [fp, #-0x18]
    // 0xb82118: StoreField: r1->field_f = r0
    //     0xb82118: stur            w0, [x1, #0xf]
    // 0xb8211c: r12 = 6
    //     0xb8211c: movz            x12, #0x6
    // 0xb82120: StoreField: r1->field_b = r12
    //     0xb82120: stur            w12, [x1, #0xb]
    // 0xb82124: r0 = Column()
    //     0xb82124: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb82128: r13 = Instance_Axis
    //     0xb82128: ldr             x13, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb8212c: stur            x0, [fp, #-0x30]
    // 0xb82130: StoreField: r0->field_f = r13
    //     0xb82130: stur            w13, [x0, #0xf]
    // 0xb82134: r14 = Instance_MainAxisAlignment
    //     0xb82134: add             x14, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb82138: ldr             x14, [x14, #0xa08]
    // 0xb8213c: StoreField: r0->field_13 = r14
    //     0xb8213c: stur            w14, [x0, #0x13]
    // 0xb82140: r2 = Instance_MainAxisSize
    //     0xb82140: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb82144: ldr             x2, [x2, #0xa10]
    // 0xb82148: ArrayStore: r0[0] = r2  ; List_4
    //     0xb82148: stur            w2, [x0, #0x17]
    // 0xb8214c: r3 = Instance_CrossAxisAlignment
    //     0xb8214c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb82150: ldr             x3, [x3, #0xa18]
    // 0xb82154: StoreField: r0->field_1b = r3
    //     0xb82154: stur            w3, [x0, #0x1b]
    // 0xb82158: r4 = Instance_VerticalDirection
    //     0xb82158: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8215c: ldr             x4, [x4, #0xa20]
    // 0xb82160: StoreField: r0->field_23 = r4
    //     0xb82160: stur            w4, [x0, #0x23]
    // 0xb82164: r5 = Instance_Clip
    //     0xb82164: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb82168: ldr             x5, [x5, #0x38]
    // 0xb8216c: StoreField: r0->field_2b = r5
    //     0xb8216c: stur            w5, [x0, #0x2b]
    // 0xb82170: StoreField: r0->field_2f = rZR
    //     0xb82170: stur            xzr, [x0, #0x2f]
    // 0xb82174: ldur            x1, [fp, #-0x18]
    // 0xb82178: StoreField: r0->field_b = r1
    //     0xb82178: stur            w1, [x0, #0xb]
    // 0xb8217c: r1 = <FlexParentData>
    //     0xb8217c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb82180: ldr             x1, [x1, #0xe00]
    // 0xb82184: r0 = Expanded()
    //     0xb82184: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb82188: r19 = 2
    //     0xb82188: movz            x19, #0x2
    // 0xb8218c: stur            x0, [fp, #-0x18]
    // 0xb82190: StoreField: r0->field_13 = r19
    //     0xb82190: stur            x19, [x0, #0x13]
    // 0xb82194: r20 = Instance_FlexFit
    //     0xb82194: add             x20, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb82198: ldr             x20, [x20, #0xe08]
    // 0xb8219c: StoreField: r0->field_1b = r20
    //     0xb8219c: stur            w20, [x0, #0x1b]
    // 0xb821a0: ldur            x1, [fp, #-0x30]
    // 0xb821a4: StoreField: r0->field_b = r1
    //     0xb821a4: stur            w1, [x0, #0xb]
    // 0xb821a8: r1 = Null
    //     0xb821a8: mov             x1, NULL
    // 0xb821ac: r2 = 4
    //     0xb821ac: movz            x2, #0x4
    // 0xb821b0: r0 = AllocateArray()
    //     0xb821b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb821b4: mov             x2, x0
    // 0xb821b8: ldur            x0, [fp, #-0x28]
    // 0xb821bc: stur            x2, [fp, #-0x30]
    // 0xb821c0: StoreField: r2->field_f = r0
    //     0xb821c0: stur            w0, [x2, #0xf]
    // 0xb821c4: ldur            x0, [fp, #-0x18]
    // 0xb821c8: StoreField: r2->field_13 = r0
    //     0xb821c8: stur            w0, [x2, #0x13]
    // 0xb821cc: r1 = <Widget>
    //     0xb821cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb821d0: r0 = AllocateGrowableArray()
    //     0xb821d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb821d4: mov             x1, x0
    // 0xb821d8: ldur            x0, [fp, #-0x30]
    // 0xb821dc: stur            x1, [fp, #-0x18]
    // 0xb821e0: StoreField: r1->field_f = r0
    //     0xb821e0: stur            w0, [x1, #0xf]
    // 0xb821e4: r23 = 4
    //     0xb821e4: movz            x23, #0x4
    // 0xb821e8: StoreField: r1->field_b = r23
    //     0xb821e8: stur            w23, [x1, #0xb]
    // 0xb821ec: r0 = Row()
    //     0xb821ec: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb821f0: r24 = Instance_Axis
    //     0xb821f0: ldr             x24, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb821f4: StoreField: r0->field_f = r24
    //     0xb821f4: stur            w24, [x0, #0xf]
    // 0xb821f8: r25 = Instance_MainAxisAlignment
    //     0xb821f8: add             x25, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb821fc: ldr             x25, [x25, #0xa8]
    // 0xb82200: StoreField: r0->field_13 = r25
    //     0xb82200: stur            w25, [x0, #0x13]
    // 0xb82204: r1 = Instance_MainAxisSize
    //     0xb82204: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb82208: ldr             x1, [x1, #0xa10]
    // 0xb8220c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb8220c: stur            w1, [x0, #0x17]
    // 0xb82210: r1 = Instance_CrossAxisAlignment
    //     0xb82210: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb82214: ldr             x1, [x1, #0xa18]
    // 0xb82218: StoreField: r0->field_1b = r1
    //     0xb82218: stur            w1, [x0, #0x1b]
    // 0xb8221c: r1 = Instance_VerticalDirection
    //     0xb8221c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb82220: ldr             x1, [x1, #0xa20]
    // 0xb82224: StoreField: r0->field_23 = r1
    //     0xb82224: stur            w1, [x0, #0x23]
    // 0xb82228: r1 = Instance_Clip
    //     0xb82228: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8222c: ldr             x1, [x1, #0x38]
    // 0xb82230: StoreField: r0->field_2b = r1
    //     0xb82230: stur            w1, [x0, #0x2b]
    // 0xb82234: StoreField: r0->field_2f = rZR
    //     0xb82234: stur            xzr, [x0, #0x2f]
    // 0xb82238: ldur            x1, [fp, #-0x18]
    // 0xb8223c: StoreField: r0->field_b = r1
    //     0xb8223c: stur            w1, [x0, #0xb]
    // 0xb82240: mov             x1, x0
    // 0xb82244: b               #0xb82aa8
    // 0xb82248: ldur            x7, [fp, #-8]
    // 0xb8224c: ldur            x8, [fp, #-0x20]
    // 0xb82250: r2 = Instance_EdgeInsets
    //     0xb82250: add             x2, PP, #0x55, lsl #12  ; [pp+0x55798] Obj!EdgeInsets@d593c1
    //     0xb82254: ldr             x2, [x2, #0x798]
    // 0xb82258: r9 = Instance_EdgeInsets
    //     0xb82258: add             x9, PP, #0x55, lsl #12  ; [pp+0x557a8] Obj!EdgeInsets@d59391
    //     0xb8225c: ldr             x9, [x9, #0x7a8]
    // 0xb82260: r23 = 4
    //     0xb82260: movz            x23, #0x4
    // 0xb82264: r25 = Instance_MainAxisAlignment
    //     0xb82264: add             x25, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb82268: ldr             x25, [x25, #0xa8]
    // 0xb8226c: r11 = false
    //     0xb8226c: add             x11, NULL, #0x30  ; false
    // 0xb82270: r3 = Instance_Clip
    //     0xb82270: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb82274: ldr             x3, [x3, #0x138]
    // 0xb82278: r20 = Instance_FlexFit
    //     0xb82278: add             x20, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb8227c: ldr             x20, [x20, #0xe08]
    // 0xb82280: r1 = Instance_MainAxisSize
    //     0xb82280: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb82284: ldr             x1, [x1, #0xa10]
    // 0xb82288: r24 = Instance_Axis
    //     0xb82288: ldr             x24, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb8228c: r10 = Instance_SizedBox
    //     0xb8228c: ldr             x10, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb82290: r12 = 6
    //     0xb82290: movz            x12, #0x6
    // 0xb82294: r14 = Instance_MainAxisAlignment
    //     0xb82294: add             x14, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb82298: ldr             x14, [x14, #0xa08]
    // 0xb8229c: r13 = Instance_Axis
    //     0xb8229c: ldr             x13, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb822a0: r6 = Instance_BorderSide
    //     0xb822a0: add             x6, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb822a4: ldr             x6, [x6, #0xe20]
    // 0xb822a8: r4 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xb822a8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xb822ac: ldr             x4, [x4, #0x1e0]
    // 0xb822b0: r5 = Instance_ColumnMode
    //     0xb822b0: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xb822b4: ldr             x5, [x5, #0x1e8]
    // 0xb822b8: d0 = 20.000000
    //     0xb822b8: fmov            d0, #20.00000000
    // 0xb822bc: r19 = 2
    //     0xb822bc: movz            x19, #0x2
    // 0xb822c0: LoadField: r0 = r7->field_f
    //     0xb822c0: ldur            w0, [x7, #0xf]
    // 0xb822c4: DecompressPointer r0
    //     0xb822c4: add             x0, x0, HEAP, lsl #32
    // 0xb822c8: LoadField: r2 = r0->field_b
    //     0xb822c8: ldur            w2, [x0, #0xb]
    // 0xb822cc: DecompressPointer r2
    //     0xb822cc: add             x2, x2, HEAP, lsl #32
    // 0xb822d0: cmp             w2, NULL
    // 0xb822d4: b.eq            #0xb82b40
    // 0xb822d8: LoadField: r0 = r2->field_b
    //     0xb822d8: ldur            w0, [x2, #0xb]
    // 0xb822dc: DecompressPointer r0
    //     0xb822dc: add             x0, x0, HEAP, lsl #32
    // 0xb822e0: LoadField: r2 = r0->field_b
    //     0xb822e0: ldur            w2, [x0, #0xb]
    // 0xb822e4: r3 = LoadInt32Instr(r2)
    //     0xb822e4: sbfx            x3, x2, #1, #0x1f
    // 0xb822e8: mov             x2, x0
    // 0xb822ec: mov             x0, x3
    // 0xb822f0: mov             x1, x8
    // 0xb822f4: cmp             x1, x0
    // 0xb822f8: b.hs            #0xb82b44
    // 0xb822fc: LoadField: r0 = r2->field_f
    //     0xb822fc: ldur            w0, [x2, #0xf]
    // 0xb82300: DecompressPointer r0
    //     0xb82300: add             x0, x0, HEAP, lsl #32
    // 0xb82304: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xb82304: add             x16, x0, x8, lsl #2
    //     0xb82308: ldur            w1, [x16, #0xf]
    // 0xb8230c: DecompressPointer r1
    //     0xb8230c: add             x1, x1, HEAP, lsl #32
    // 0xb82310: LoadField: r0 = r1->field_7
    //     0xb82310: ldur            w0, [x1, #7]
    // 0xb82314: DecompressPointer r0
    //     0xb82314: add             x0, x0, HEAP, lsl #32
    // 0xb82318: cmp             w0, NULL
    // 0xb8231c: b.ne            #0xb82324
    // 0xb82320: r0 = ""
    //     0xb82320: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb82324: ldr             x1, [fp, #0x18]
    // 0xb82328: stur            x0, [fp, #-0x18]
    // 0xb8232c: r0 = of()
    //     0xb8232c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb82330: LoadField: r1 = r0->field_87
    //     0xb82330: ldur            w1, [x0, #0x87]
    // 0xb82334: DecompressPointer r1
    //     0xb82334: add             x1, x1, HEAP, lsl #32
    // 0xb82338: LoadField: r0 = r1->field_7
    //     0xb82338: ldur            w0, [x1, #7]
    // 0xb8233c: DecompressPointer r0
    //     0xb8233c: add             x0, x0, HEAP, lsl #32
    // 0xb82340: stur            x0, [fp, #-0x28]
    // 0xb82344: r1 = Instance_Color
    //     0xb82344: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb82348: d0 = 0.700000
    //     0xb82348: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb8234c: ldr             d0, [x17, #0xf48]
    // 0xb82350: r0 = withOpacity()
    //     0xb82350: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb82354: r16 = 16.000000
    //     0xb82354: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb82358: ldr             x16, [x16, #0x188]
    // 0xb8235c: stp             x0, x16, [SP]
    // 0xb82360: ldur            x1, [fp, #-0x28]
    // 0xb82364: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb82364: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb82368: ldr             x4, [x4, #0xaa0]
    // 0xb8236c: r0 = copyWith()
    //     0xb8236c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb82370: stur            x0, [fp, #-0x28]
    // 0xb82374: r0 = Text()
    //     0xb82374: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb82378: mov             x1, x0
    // 0xb8237c: ldur            x0, [fp, #-0x18]
    // 0xb82380: stur            x1, [fp, #-0x30]
    // 0xb82384: StoreField: r1->field_b = r0
    //     0xb82384: stur            w0, [x1, #0xb]
    // 0xb82388: ldur            x0, [fp, #-0x28]
    // 0xb8238c: StoreField: r1->field_13 = r0
    //     0xb8238c: stur            w0, [x1, #0x13]
    // 0xb82390: r0 = Padding()
    //     0xb82390: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb82394: mov             x2, x0
    // 0xb82398: r0 = Instance_EdgeInsets
    //     0xb82398: add             x0, PP, #0x55, lsl #12  ; [pp+0x55798] Obj!EdgeInsets@d593c1
    //     0xb8239c: ldr             x0, [x0, #0x798]
    // 0xb823a0: stur            x2, [fp, #-0x28]
    // 0xb823a4: StoreField: r2->field_f = r0
    //     0xb823a4: stur            w0, [x2, #0xf]
    // 0xb823a8: ldur            x0, [fp, #-0x30]
    // 0xb823ac: StoreField: r2->field_b = r0
    //     0xb823ac: stur            w0, [x2, #0xb]
    // 0xb823b0: ldur            x3, [fp, #-8]
    // 0xb823b4: LoadField: r0 = r3->field_f
    //     0xb823b4: ldur            w0, [x3, #0xf]
    // 0xb823b8: DecompressPointer r0
    //     0xb823b8: add             x0, x0, HEAP, lsl #32
    // 0xb823bc: LoadField: r1 = r0->field_b
    //     0xb823bc: ldur            w1, [x0, #0xb]
    // 0xb823c0: DecompressPointer r1
    //     0xb823c0: add             x1, x1, HEAP, lsl #32
    // 0xb823c4: cmp             w1, NULL
    // 0xb823c8: b.eq            #0xb82b48
    // 0xb823cc: LoadField: r4 = r1->field_b
    //     0xb823cc: ldur            w4, [x1, #0xb]
    // 0xb823d0: DecompressPointer r4
    //     0xb823d0: add             x4, x4, HEAP, lsl #32
    // 0xb823d4: LoadField: r0 = r4->field_b
    //     0xb823d4: ldur            w0, [x4, #0xb]
    // 0xb823d8: r1 = LoadInt32Instr(r0)
    //     0xb823d8: sbfx            x1, x0, #1, #0x1f
    // 0xb823dc: mov             x0, x1
    // 0xb823e0: ldur            x1, [fp, #-0x20]
    // 0xb823e4: cmp             x1, x0
    // 0xb823e8: b.hs            #0xb82b4c
    // 0xb823ec: LoadField: r0 = r4->field_f
    //     0xb823ec: ldur            w0, [x4, #0xf]
    // 0xb823f0: DecompressPointer r0
    //     0xb823f0: add             x0, x0, HEAP, lsl #32
    // 0xb823f4: ldur            x4, [fp, #-0x20]
    // 0xb823f8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb823f8: add             x16, x0, x4, lsl #2
    //     0xb823fc: ldur            w1, [x16, #0xf]
    // 0xb82400: DecompressPointer r1
    //     0xb82400: add             x1, x1, HEAP, lsl #32
    // 0xb82404: LoadField: r0 = r1->field_b
    //     0xb82404: ldur            w0, [x1, #0xb]
    // 0xb82408: DecompressPointer r0
    //     0xb82408: add             x0, x0, HEAP, lsl #32
    // 0xb8240c: cmp             w0, NULL
    // 0xb82410: b.ne            #0xb82418
    // 0xb82414: r0 = ""
    //     0xb82414: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb82418: ldr             x1, [fp, #0x18]
    // 0xb8241c: stur            x0, [fp, #-0x18]
    // 0xb82420: r0 = of()
    //     0xb82420: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb82424: LoadField: r1 = r0->field_87
    //     0xb82424: ldur            w1, [x0, #0x87]
    // 0xb82428: DecompressPointer r1
    //     0xb82428: add             x1, x1, HEAP, lsl #32
    // 0xb8242c: LoadField: r0 = r1->field_2b
    //     0xb8242c: ldur            w0, [x1, #0x2b]
    // 0xb82430: DecompressPointer r0
    //     0xb82430: add             x0, x0, HEAP, lsl #32
    // 0xb82434: stur            x0, [fp, #-0x30]
    // 0xb82438: r1 = Instance_Color
    //     0xb82438: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8243c: d0 = 0.400000
    //     0xb8243c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb82440: r0 = withOpacity()
    //     0xb82440: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb82444: r16 = 12.000000
    //     0xb82444: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb82448: ldr             x16, [x16, #0x9e8]
    // 0xb8244c: stp             x16, x0, [SP]
    // 0xb82450: ldur            x1, [fp, #-0x30]
    // 0xb82454: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb82454: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb82458: ldr             x4, [x4, #0x9b8]
    // 0xb8245c: r0 = copyWith()
    //     0xb8245c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb82460: stur            x0, [fp, #-0x30]
    // 0xb82464: r0 = HtmlWidget()
    //     0xb82464: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xb82468: mov             x1, x0
    // 0xb8246c: ldur            x0, [fp, #-0x18]
    // 0xb82470: stur            x1, [fp, #-0x38]
    // 0xb82474: StoreField: r1->field_1f = r0
    //     0xb82474: stur            w0, [x1, #0x1f]
    // 0xb82478: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xb82478: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xb8247c: ldr             x0, [x0, #0x1e0]
    // 0xb82480: StoreField: r1->field_23 = r0
    //     0xb82480: stur            w0, [x1, #0x23]
    // 0xb82484: r0 = Instance_ColumnMode
    //     0xb82484: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xb82488: ldr             x0, [x0, #0x1e8]
    // 0xb8248c: StoreField: r1->field_3b = r0
    //     0xb8248c: stur            w0, [x1, #0x3b]
    // 0xb82490: ldur            x0, [fp, #-0x30]
    // 0xb82494: StoreField: r1->field_3f = r0
    //     0xb82494: stur            w0, [x1, #0x3f]
    // 0xb82498: r0 = Container()
    //     0xb82498: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8249c: stur            x0, [fp, #-0x18]
    // 0xb824a0: r16 = 150.000000
    //     0xb824a0: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb824a4: ldr             x16, [x16, #0x690]
    // 0xb824a8: r30 = Instance_EdgeInsets
    //     0xb824a8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe48] Obj!EdgeInsets@d58f11
    //     0xb824ac: ldr             lr, [lr, #0xe48]
    // 0xb824b0: stp             lr, x16, [SP, #8]
    // 0xb824b4: ldur            x16, [fp, #-0x38]
    // 0xb824b8: str             x16, [SP]
    // 0xb824bc: mov             x1, x0
    // 0xb824c0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, padding, 0x2, width, 0x1, null]
    //     0xb824c0: add             x4, PP, #0x44, lsl #12  ; [pp+0x44260] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "padding", 0x2, "width", 0x1, Null]
    //     0xb824c4: ldr             x4, [x4, #0x260]
    // 0xb824c8: r0 = Container()
    //     0xb824c8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb824cc: ldur            x2, [fp, #-8]
    // 0xb824d0: LoadField: r0 = r2->field_f
    //     0xb824d0: ldur            w0, [x2, #0xf]
    // 0xb824d4: DecompressPointer r0
    //     0xb824d4: add             x0, x0, HEAP, lsl #32
    // 0xb824d8: LoadField: r1 = r0->field_b
    //     0xb824d8: ldur            w1, [x0, #0xb]
    // 0xb824dc: DecompressPointer r1
    //     0xb824dc: add             x1, x1, HEAP, lsl #32
    // 0xb824e0: cmp             w1, NULL
    // 0xb824e4: b.eq            #0xb82b50
    // 0xb824e8: LoadField: r3 = r1->field_b
    //     0xb824e8: ldur            w3, [x1, #0xb]
    // 0xb824ec: DecompressPointer r3
    //     0xb824ec: add             x3, x3, HEAP, lsl #32
    // 0xb824f0: LoadField: r0 = r3->field_b
    //     0xb824f0: ldur            w0, [x3, #0xb]
    // 0xb824f4: r1 = LoadInt32Instr(r0)
    //     0xb824f4: sbfx            x1, x0, #1, #0x1f
    // 0xb824f8: mov             x0, x1
    // 0xb824fc: ldur            x1, [fp, #-0x20]
    // 0xb82500: cmp             x1, x0
    // 0xb82504: b.hs            #0xb82b54
    // 0xb82508: LoadField: r0 = r3->field_f
    //     0xb82508: ldur            w0, [x3, #0xf]
    // 0xb8250c: DecompressPointer r0
    //     0xb8250c: add             x0, x0, HEAP, lsl #32
    // 0xb82510: ldur            x3, [fp, #-0x20]
    // 0xb82514: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb82514: add             x16, x0, x3, lsl #2
    //     0xb82518: ldur            w1, [x16, #0xf]
    // 0xb8251c: DecompressPointer r1
    //     0xb8251c: add             x1, x1, HEAP, lsl #32
    // 0xb82520: LoadField: r0 = r1->field_f
    //     0xb82520: ldur            w0, [x1, #0xf]
    // 0xb82524: DecompressPointer r0
    //     0xb82524: add             x0, x0, HEAP, lsl #32
    // 0xb82528: cmp             w0, NULL
    // 0xb8252c: b.ne            #0xb82538
    // 0xb82530: r0 = Null
    //     0xb82530: mov             x0, NULL
    // 0xb82534: b               #0xb8254c
    // 0xb82538: LoadField: r1 = r0->field_7
    //     0xb82538: ldur            w1, [x0, #7]
    // 0xb8253c: cbnz            w1, #0xb82548
    // 0xb82540: r0 = false
    //     0xb82540: add             x0, NULL, #0x30  ; false
    // 0xb82544: b               #0xb8254c
    // 0xb82548: r0 = true
    //     0xb82548: add             x0, NULL, #0x20  ; true
    // 0xb8254c: cmp             w0, NULL
    // 0xb82550: b.ne            #0xb82558
    // 0xb82554: r0 = false
    //     0xb82554: add             x0, NULL, #0x30  ; false
    // 0xb82558: ldr             x1, [fp, #0x18]
    // 0xb8255c: stur            x0, [fp, #-0x30]
    // 0xb82560: r0 = of()
    //     0xb82560: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb82564: r17 = 307
    //     0xb82564: movz            x17, #0x133
    // 0xb82568: ldr             w1, [x0, x17]
    // 0xb8256c: DecompressPointer r1
    //     0xb8256c: add             x1, x1, HEAP, lsl #32
    // 0xb82570: stur            x1, [fp, #-0x38]
    // 0xb82574: r16 = <EdgeInsets>
    //     0xb82574: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb82578: ldr             x16, [x16, #0xda0]
    // 0xb8257c: r30 = Instance_EdgeInsets
    //     0xb8257c: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f528] Obj!EdgeInsets@d593f1
    //     0xb82580: ldr             lr, [lr, #0x528]
    // 0xb82584: stp             lr, x16, [SP]
    // 0xb82588: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb82588: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb8258c: r0 = all()
    //     0xb8258c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb82590: stur            x0, [fp, #-0x40]
    // 0xb82594: r0 = Radius()
    //     0xb82594: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb82598: d0 = 20.000000
    //     0xb82598: fmov            d0, #20.00000000
    // 0xb8259c: stur            x0, [fp, #-0x48]
    // 0xb825a0: StoreField: r0->field_7 = d0
    //     0xb825a0: stur            d0, [x0, #7]
    // 0xb825a4: StoreField: r0->field_f = d0
    //     0xb825a4: stur            d0, [x0, #0xf]
    // 0xb825a8: r0 = BorderRadius()
    //     0xb825a8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb825ac: mov             x1, x0
    // 0xb825b0: ldur            x0, [fp, #-0x48]
    // 0xb825b4: stur            x1, [fp, #-0x50]
    // 0xb825b8: StoreField: r1->field_7 = r0
    //     0xb825b8: stur            w0, [x1, #7]
    // 0xb825bc: StoreField: r1->field_b = r0
    //     0xb825bc: stur            w0, [x1, #0xb]
    // 0xb825c0: StoreField: r1->field_f = r0
    //     0xb825c0: stur            w0, [x1, #0xf]
    // 0xb825c4: StoreField: r1->field_13 = r0
    //     0xb825c4: stur            w0, [x1, #0x13]
    // 0xb825c8: r0 = RoundedRectangleBorder()
    //     0xb825c8: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb825cc: mov             x1, x0
    // 0xb825d0: ldur            x0, [fp, #-0x50]
    // 0xb825d4: StoreField: r1->field_b = r0
    //     0xb825d4: stur            w0, [x1, #0xb]
    // 0xb825d8: r0 = Instance_BorderSide
    //     0xb825d8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb825dc: ldr             x0, [x0, #0xe20]
    // 0xb825e0: StoreField: r1->field_7 = r0
    //     0xb825e0: stur            w0, [x1, #7]
    // 0xb825e4: r16 = <RoundedRectangleBorder>
    //     0xb825e4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb825e8: ldr             x16, [x16, #0xf78]
    // 0xb825ec: stp             x1, x16, [SP]
    // 0xb825f0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb825f0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb825f4: r0 = all()
    //     0xb825f4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb825f8: stur            x0, [fp, #-0x48]
    // 0xb825fc: r0 = ButtonStyle()
    //     0xb825fc: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb82600: mov             x2, x0
    // 0xb82604: ldur            x0, [fp, #-0x40]
    // 0xb82608: stur            x2, [fp, #-0x50]
    // 0xb8260c: StoreField: r2->field_23 = r0
    //     0xb8260c: stur            w0, [x2, #0x23]
    // 0xb82610: ldur            x0, [fp, #-0x48]
    // 0xb82614: StoreField: r2->field_43 = r0
    //     0xb82614: stur            w0, [x2, #0x43]
    // 0xb82618: ldur            x3, [fp, #-8]
    // 0xb8261c: LoadField: r0 = r3->field_f
    //     0xb8261c: ldur            w0, [x3, #0xf]
    // 0xb82620: DecompressPointer r0
    //     0xb82620: add             x0, x0, HEAP, lsl #32
    // 0xb82624: LoadField: r1 = r0->field_b
    //     0xb82624: ldur            w1, [x0, #0xb]
    // 0xb82628: DecompressPointer r1
    //     0xb82628: add             x1, x1, HEAP, lsl #32
    // 0xb8262c: cmp             w1, NULL
    // 0xb82630: b.eq            #0xb82b58
    // 0xb82634: LoadField: r4 = r1->field_b
    //     0xb82634: ldur            w4, [x1, #0xb]
    // 0xb82638: DecompressPointer r4
    //     0xb82638: add             x4, x4, HEAP, lsl #32
    // 0xb8263c: LoadField: r0 = r4->field_b
    //     0xb8263c: ldur            w0, [x4, #0xb]
    // 0xb82640: r1 = LoadInt32Instr(r0)
    //     0xb82640: sbfx            x1, x0, #1, #0x1f
    // 0xb82644: mov             x0, x1
    // 0xb82648: ldur            x1, [fp, #-0x20]
    // 0xb8264c: cmp             x1, x0
    // 0xb82650: b.hs            #0xb82b5c
    // 0xb82654: LoadField: r0 = r4->field_f
    //     0xb82654: ldur            w0, [x4, #0xf]
    // 0xb82658: DecompressPointer r0
    //     0xb82658: add             x0, x0, HEAP, lsl #32
    // 0xb8265c: ldur            x4, [fp, #-0x20]
    // 0xb82660: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb82660: add             x16, x0, x4, lsl #2
    //     0xb82664: ldur            w1, [x16, #0xf]
    // 0xb82668: DecompressPointer r1
    //     0xb82668: add             x1, x1, HEAP, lsl #32
    // 0xb8266c: LoadField: r0 = r1->field_f
    //     0xb8266c: ldur            w0, [x1, #0xf]
    // 0xb82670: DecompressPointer r0
    //     0xb82670: add             x0, x0, HEAP, lsl #32
    // 0xb82674: cmp             w0, NULL
    // 0xb82678: b.ne            #0xb82680
    // 0xb8267c: r0 = ""
    //     0xb8267c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb82680: ldr             x1, [fp, #0x18]
    // 0xb82684: stur            x0, [fp, #-0x40]
    // 0xb82688: r0 = of()
    //     0xb82688: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8268c: LoadField: r1 = r0->field_87
    //     0xb8268c: ldur            w1, [x0, #0x87]
    // 0xb82690: DecompressPointer r1
    //     0xb82690: add             x1, x1, HEAP, lsl #32
    // 0xb82694: LoadField: r0 = r1->field_2f
    //     0xb82694: ldur            w0, [x1, #0x2f]
    // 0xb82698: DecompressPointer r0
    //     0xb82698: add             x0, x0, HEAP, lsl #32
    // 0xb8269c: cmp             w0, NULL
    // 0xb826a0: b.ne            #0xb826ac
    // 0xb826a4: r8 = Null
    //     0xb826a4: mov             x8, NULL
    // 0xb826a8: b               #0xb826d0
    // 0xb826ac: r16 = Instance_Color
    //     0xb826ac: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb826b0: r30 = 14.000000
    //     0xb826b0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb826b4: ldr             lr, [lr, #0x1d8]
    // 0xb826b8: stp             lr, x16, [SP]
    // 0xb826bc: mov             x1, x0
    // 0xb826c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb826c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb826c4: ldr             x4, [x4, #0x9b8]
    // 0xb826c8: r0 = copyWith()
    //     0xb826c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb826cc: mov             x8, x0
    // 0xb826d0: ldur            x1, [fp, #-8]
    // 0xb826d4: ldur            x7, [fp, #-0x28]
    // 0xb826d8: ldur            x6, [fp, #-0x18]
    // 0xb826dc: ldur            x5, [fp, #-0x30]
    // 0xb826e0: ldur            x4, [fp, #-0x38]
    // 0xb826e4: ldur            x0, [fp, #-0x50]
    // 0xb826e8: ldur            x3, [fp, #-0x40]
    // 0xb826ec: ldur            x2, [fp, #-0x20]
    // 0xb826f0: stur            x8, [fp, #-0x48]
    // 0xb826f4: r0 = Text()
    //     0xb826f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb826f8: mov             x3, x0
    // 0xb826fc: ldur            x0, [fp, #-0x40]
    // 0xb82700: stur            x3, [fp, #-0x58]
    // 0xb82704: StoreField: r3->field_b = r0
    //     0xb82704: stur            w0, [x3, #0xb]
    // 0xb82708: ldur            x0, [fp, #-0x48]
    // 0xb8270c: StoreField: r3->field_13 = r0
    //     0xb8270c: stur            w0, [x3, #0x13]
    // 0xb82710: r1 = Function '<anonymous closure>':.
    //     0xb82710: add             x1, PP, #0x55, lsl #12  ; [pp+0x557b0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb82714: ldr             x1, [x1, #0x7b0]
    // 0xb82718: r2 = Null
    //     0xb82718: mov             x2, NULL
    // 0xb8271c: r0 = AllocateClosure()
    //     0xb8271c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb82720: stur            x0, [fp, #-0x40]
    // 0xb82724: r0 = TextButton()
    //     0xb82724: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb82728: mov             x1, x0
    // 0xb8272c: ldur            x0, [fp, #-0x40]
    // 0xb82730: stur            x1, [fp, #-0x48]
    // 0xb82734: StoreField: r1->field_b = r0
    //     0xb82734: stur            w0, [x1, #0xb]
    // 0xb82738: ldur            x0, [fp, #-0x50]
    // 0xb8273c: StoreField: r1->field_1b = r0
    //     0xb8273c: stur            w0, [x1, #0x1b]
    // 0xb82740: r0 = false
    //     0xb82740: add             x0, NULL, #0x30  ; false
    // 0xb82744: StoreField: r1->field_27 = r0
    //     0xb82744: stur            w0, [x1, #0x27]
    // 0xb82748: r2 = true
    //     0xb82748: add             x2, NULL, #0x20  ; true
    // 0xb8274c: StoreField: r1->field_2f = r2
    //     0xb8274c: stur            w2, [x1, #0x2f]
    // 0xb82750: ldur            x3, [fp, #-0x58]
    // 0xb82754: StoreField: r1->field_37 = r3
    //     0xb82754: stur            w3, [x1, #0x37]
    // 0xb82758: r0 = TextButtonTheme()
    //     0xb82758: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb8275c: mov             x1, x0
    // 0xb82760: ldur            x0, [fp, #-0x38]
    // 0xb82764: stur            x1, [fp, #-0x40]
    // 0xb82768: StoreField: r1->field_f = r0
    //     0xb82768: stur            w0, [x1, #0xf]
    // 0xb8276c: ldur            x0, [fp, #-0x48]
    // 0xb82770: StoreField: r1->field_b = r0
    //     0xb82770: stur            w0, [x1, #0xb]
    // 0xb82774: r0 = Padding()
    //     0xb82774: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb82778: mov             x1, x0
    // 0xb8277c: r0 = Instance_EdgeInsets
    //     0xb8277c: add             x0, PP, #0x55, lsl #12  ; [pp+0x557a8] Obj!EdgeInsets@d59391
    //     0xb82780: ldr             x0, [x0, #0x7a8]
    // 0xb82784: stur            x1, [fp, #-0x38]
    // 0xb82788: StoreField: r1->field_f = r0
    //     0xb82788: stur            w0, [x1, #0xf]
    // 0xb8278c: ldur            x0, [fp, #-0x40]
    // 0xb82790: StoreField: r1->field_b = r0
    //     0xb82790: stur            w0, [x1, #0xb]
    // 0xb82794: r0 = Visibility()
    //     0xb82794: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb82798: mov             x3, x0
    // 0xb8279c: ldur            x0, [fp, #-0x38]
    // 0xb827a0: stur            x3, [fp, #-0x40]
    // 0xb827a4: StoreField: r3->field_b = r0
    //     0xb827a4: stur            w0, [x3, #0xb]
    // 0xb827a8: r0 = Instance_SizedBox
    //     0xb827a8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb827ac: StoreField: r3->field_f = r0
    //     0xb827ac: stur            w0, [x3, #0xf]
    // 0xb827b0: ldur            x0, [fp, #-0x30]
    // 0xb827b4: StoreField: r3->field_13 = r0
    //     0xb827b4: stur            w0, [x3, #0x13]
    // 0xb827b8: r0 = false
    //     0xb827b8: add             x0, NULL, #0x30  ; false
    // 0xb827bc: ArrayStore: r3[0] = r0  ; List_4
    //     0xb827bc: stur            w0, [x3, #0x17]
    // 0xb827c0: StoreField: r3->field_1b = r0
    //     0xb827c0: stur            w0, [x3, #0x1b]
    // 0xb827c4: StoreField: r3->field_1f = r0
    //     0xb827c4: stur            w0, [x3, #0x1f]
    // 0xb827c8: StoreField: r3->field_23 = r0
    //     0xb827c8: stur            w0, [x3, #0x23]
    // 0xb827cc: StoreField: r3->field_27 = r0
    //     0xb827cc: stur            w0, [x3, #0x27]
    // 0xb827d0: StoreField: r3->field_2b = r0
    //     0xb827d0: stur            w0, [x3, #0x2b]
    // 0xb827d4: r1 = Null
    //     0xb827d4: mov             x1, NULL
    // 0xb827d8: r2 = 6
    //     0xb827d8: movz            x2, #0x6
    // 0xb827dc: r0 = AllocateArray()
    //     0xb827dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb827e0: mov             x2, x0
    // 0xb827e4: ldur            x0, [fp, #-0x28]
    // 0xb827e8: stur            x2, [fp, #-0x30]
    // 0xb827ec: StoreField: r2->field_f = r0
    //     0xb827ec: stur            w0, [x2, #0xf]
    // 0xb827f0: ldur            x0, [fp, #-0x18]
    // 0xb827f4: StoreField: r2->field_13 = r0
    //     0xb827f4: stur            w0, [x2, #0x13]
    // 0xb827f8: ldur            x0, [fp, #-0x40]
    // 0xb827fc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb827fc: stur            w0, [x2, #0x17]
    // 0xb82800: r1 = <Widget>
    //     0xb82800: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb82804: r0 = AllocateGrowableArray()
    //     0xb82804: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb82808: mov             x1, x0
    // 0xb8280c: ldur            x0, [fp, #-0x30]
    // 0xb82810: stur            x1, [fp, #-0x18]
    // 0xb82814: StoreField: r1->field_f = r0
    //     0xb82814: stur            w0, [x1, #0xf]
    // 0xb82818: r0 = 6
    //     0xb82818: movz            x0, #0x6
    // 0xb8281c: StoreField: r1->field_b = r0
    //     0xb8281c: stur            w0, [x1, #0xb]
    // 0xb82820: r0 = Column()
    //     0xb82820: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb82824: mov             x2, x0
    // 0xb82828: r0 = Instance_Axis
    //     0xb82828: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb8282c: stur            x2, [fp, #-0x28]
    // 0xb82830: StoreField: r2->field_f = r0
    //     0xb82830: stur            w0, [x2, #0xf]
    // 0xb82834: r0 = Instance_MainAxisAlignment
    //     0xb82834: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb82838: ldr             x0, [x0, #0xa08]
    // 0xb8283c: StoreField: r2->field_13 = r0
    //     0xb8283c: stur            w0, [x2, #0x13]
    // 0xb82840: r0 = Instance_MainAxisSize
    //     0xb82840: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb82844: ldr             x0, [x0, #0xa10]
    // 0xb82848: ArrayStore: r2[0] = r0  ; List_4
    //     0xb82848: stur            w0, [x2, #0x17]
    // 0xb8284c: r1 = Instance_CrossAxisAlignment
    //     0xb8284c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb82850: ldr             x1, [x1, #0x890]
    // 0xb82854: StoreField: r2->field_1b = r1
    //     0xb82854: stur            w1, [x2, #0x1b]
    // 0xb82858: r3 = Instance_VerticalDirection
    //     0xb82858: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8285c: ldr             x3, [x3, #0xa20]
    // 0xb82860: StoreField: r2->field_23 = r3
    //     0xb82860: stur            w3, [x2, #0x23]
    // 0xb82864: r4 = Instance_Clip
    //     0xb82864: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb82868: ldr             x4, [x4, #0x38]
    // 0xb8286c: StoreField: r2->field_2b = r4
    //     0xb8286c: stur            w4, [x2, #0x2b]
    // 0xb82870: StoreField: r2->field_2f = rZR
    //     0xb82870: stur            xzr, [x2, #0x2f]
    // 0xb82874: ldur            x1, [fp, #-0x18]
    // 0xb82878: StoreField: r2->field_b = r1
    //     0xb82878: stur            w1, [x2, #0xb]
    // 0xb8287c: r1 = <FlexParentData>
    //     0xb8287c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb82880: ldr             x1, [x1, #0xe00]
    // 0xb82884: r0 = Expanded()
    //     0xb82884: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb82888: mov             x1, x0
    // 0xb8288c: r0 = 2
    //     0xb8288c: movz            x0, #0x2
    // 0xb82890: stur            x1, [fp, #-0x18]
    // 0xb82894: StoreField: r1->field_13 = r0
    //     0xb82894: stur            x0, [x1, #0x13]
    // 0xb82898: r2 = Instance_FlexFit
    //     0xb82898: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb8289c: ldr             x2, [x2, #0xe08]
    // 0xb828a0: StoreField: r1->field_1b = r2
    //     0xb828a0: stur            w2, [x1, #0x1b]
    // 0xb828a4: ldur            x3, [fp, #-0x28]
    // 0xb828a8: StoreField: r1->field_b = r3
    //     0xb828a8: stur            w3, [x1, #0xb]
    // 0xb828ac: r0 = Radius()
    //     0xb828ac: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb828b0: d0 = 20.000000
    //     0xb828b0: fmov            d0, #20.00000000
    // 0xb828b4: stur            x0, [fp, #-0x28]
    // 0xb828b8: StoreField: r0->field_7 = d0
    //     0xb828b8: stur            d0, [x0, #7]
    // 0xb828bc: StoreField: r0->field_f = d0
    //     0xb828bc: stur            d0, [x0, #0xf]
    // 0xb828c0: r0 = BorderRadius()
    //     0xb828c0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb828c4: mov             x3, x0
    // 0xb828c8: ldur            x0, [fp, #-0x28]
    // 0xb828cc: stur            x3, [fp, #-0x30]
    // 0xb828d0: StoreField: r3->field_7 = r0
    //     0xb828d0: stur            w0, [x3, #7]
    // 0xb828d4: StoreField: r3->field_b = r0
    //     0xb828d4: stur            w0, [x3, #0xb]
    // 0xb828d8: StoreField: r3->field_f = r0
    //     0xb828d8: stur            w0, [x3, #0xf]
    // 0xb828dc: StoreField: r3->field_13 = r0
    //     0xb828dc: stur            w0, [x3, #0x13]
    // 0xb828e0: ldur            x0, [fp, #-8]
    // 0xb828e4: LoadField: r1 = r0->field_f
    //     0xb828e4: ldur            w1, [x0, #0xf]
    // 0xb828e8: DecompressPointer r1
    //     0xb828e8: add             x1, x1, HEAP, lsl #32
    // 0xb828ec: LoadField: r0 = r1->field_b
    //     0xb828ec: ldur            w0, [x1, #0xb]
    // 0xb828f0: DecompressPointer r0
    //     0xb828f0: add             x0, x0, HEAP, lsl #32
    // 0xb828f4: cmp             w0, NULL
    // 0xb828f8: b.eq            #0xb82b60
    // 0xb828fc: LoadField: r2 = r0->field_b
    //     0xb828fc: ldur            w2, [x0, #0xb]
    // 0xb82900: DecompressPointer r2
    //     0xb82900: add             x2, x2, HEAP, lsl #32
    // 0xb82904: LoadField: r0 = r2->field_b
    //     0xb82904: ldur            w0, [x2, #0xb]
    // 0xb82908: r1 = LoadInt32Instr(r0)
    //     0xb82908: sbfx            x1, x0, #1, #0x1f
    // 0xb8290c: mov             x0, x1
    // 0xb82910: ldur            x1, [fp, #-0x20]
    // 0xb82914: cmp             x1, x0
    // 0xb82918: b.hs            #0xb82b64
    // 0xb8291c: LoadField: r0 = r2->field_f
    //     0xb8291c: ldur            w0, [x2, #0xf]
    // 0xb82920: DecompressPointer r0
    //     0xb82920: add             x0, x0, HEAP, lsl #32
    // 0xb82924: ldur            x1, [fp, #-0x20]
    // 0xb82928: ArrayLoad: r2 = r0[r1]  ; Unknown_4
    //     0xb82928: add             x16, x0, x1, lsl #2
    //     0xb8292c: ldur            w2, [x16, #0xf]
    // 0xb82930: DecompressPointer r2
    //     0xb82930: add             x2, x2, HEAP, lsl #32
    // 0xb82934: LoadField: r0 = r2->field_13
    //     0xb82934: ldur            w0, [x2, #0x13]
    // 0xb82938: DecompressPointer r0
    //     0xb82938: add             x0, x0, HEAP, lsl #32
    // 0xb8293c: cmp             w0, NULL
    // 0xb82940: b.ne            #0xb8294c
    // 0xb82944: r4 = ""
    //     0xb82944: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb82948: b               #0xb82950
    // 0xb8294c: mov             x4, x0
    // 0xb82950: ldur            x0, [fp, #-0x18]
    // 0xb82954: stur            x4, [fp, #-8]
    // 0xb82958: r1 = Function '<anonymous closure>':.
    //     0xb82958: add             x1, PP, #0x55, lsl #12  ; [pp+0x557b8] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb8295c: ldr             x1, [x1, #0x7b8]
    // 0xb82960: r2 = Null
    //     0xb82960: mov             x2, NULL
    // 0xb82964: r0 = AllocateClosure()
    //     0xb82964: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb82968: r1 = Function '<anonymous closure>':.
    //     0xb82968: add             x1, PP, #0x55, lsl #12  ; [pp+0x557c0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb8296c: ldr             x1, [x1, #0x7c0]
    // 0xb82970: r2 = Null
    //     0xb82970: mov             x2, NULL
    // 0xb82974: stur            x0, [fp, #-0x28]
    // 0xb82978: r0 = AllocateClosure()
    //     0xb82978: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8297c: stur            x0, [fp, #-0x38]
    // 0xb82980: r0 = CachedNetworkImage()
    //     0xb82980: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb82984: stur            x0, [fp, #-0x40]
    // 0xb82988: ldur            x16, [fp, #-0x28]
    // 0xb8298c: ldur            lr, [fp, #-0x38]
    // 0xb82990: stp             lr, x16, [SP, #8]
    // 0xb82994: r16 = Instance_BoxFit
    //     0xb82994: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb82998: ldr             x16, [x16, #0xb18]
    // 0xb8299c: str             x16, [SP]
    // 0xb829a0: mov             x1, x0
    // 0xb829a4: ldur            x2, [fp, #-8]
    // 0xb829a8: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x3, fit, 0x4, progressIndicatorBuilder, 0x2, null]
    //     0xb829a8: add             x4, PP, #0x55, lsl #12  ; [pp+0x55790] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x3, "fit", 0x4, "progressIndicatorBuilder", 0x2, Null]
    //     0xb829ac: ldr             x4, [x4, #0x790]
    // 0xb829b0: r0 = CachedNetworkImage()
    //     0xb829b0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb829b4: r0 = ClipRRect()
    //     0xb829b4: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb829b8: mov             x2, x0
    // 0xb829bc: ldur            x0, [fp, #-0x30]
    // 0xb829c0: stur            x2, [fp, #-8]
    // 0xb829c4: StoreField: r2->field_f = r0
    //     0xb829c4: stur            w0, [x2, #0xf]
    // 0xb829c8: r0 = Instance_Clip
    //     0xb829c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb829cc: ldr             x0, [x0, #0x138]
    // 0xb829d0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb829d0: stur            w0, [x2, #0x17]
    // 0xb829d4: ldur            x0, [fp, #-0x40]
    // 0xb829d8: StoreField: r2->field_b = r0
    //     0xb829d8: stur            w0, [x2, #0xb]
    // 0xb829dc: r1 = <FlexParentData>
    //     0xb829dc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb829e0: ldr             x1, [x1, #0xe00]
    // 0xb829e4: r0 = Expanded()
    //     0xb829e4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb829e8: mov             x3, x0
    // 0xb829ec: r0 = 2
    //     0xb829ec: movz            x0, #0x2
    // 0xb829f0: stur            x3, [fp, #-0x28]
    // 0xb829f4: StoreField: r3->field_13 = r0
    //     0xb829f4: stur            x0, [x3, #0x13]
    // 0xb829f8: r0 = Instance_FlexFit
    //     0xb829f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb829fc: ldr             x0, [x0, #0xe08]
    // 0xb82a00: StoreField: r3->field_1b = r0
    //     0xb82a00: stur            w0, [x3, #0x1b]
    // 0xb82a04: ldur            x0, [fp, #-8]
    // 0xb82a08: StoreField: r3->field_b = r0
    //     0xb82a08: stur            w0, [x3, #0xb]
    // 0xb82a0c: r1 = Null
    //     0xb82a0c: mov             x1, NULL
    // 0xb82a10: r2 = 4
    //     0xb82a10: movz            x2, #0x4
    // 0xb82a14: r0 = AllocateArray()
    //     0xb82a14: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb82a18: mov             x2, x0
    // 0xb82a1c: ldur            x0, [fp, #-0x18]
    // 0xb82a20: stur            x2, [fp, #-8]
    // 0xb82a24: StoreField: r2->field_f = r0
    //     0xb82a24: stur            w0, [x2, #0xf]
    // 0xb82a28: ldur            x0, [fp, #-0x28]
    // 0xb82a2c: StoreField: r2->field_13 = r0
    //     0xb82a2c: stur            w0, [x2, #0x13]
    // 0xb82a30: r1 = <Widget>
    //     0xb82a30: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb82a34: r0 = AllocateGrowableArray()
    //     0xb82a34: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb82a38: mov             x1, x0
    // 0xb82a3c: ldur            x0, [fp, #-8]
    // 0xb82a40: stur            x1, [fp, #-0x18]
    // 0xb82a44: StoreField: r1->field_f = r0
    //     0xb82a44: stur            w0, [x1, #0xf]
    // 0xb82a48: r0 = 4
    //     0xb82a48: movz            x0, #0x4
    // 0xb82a4c: StoreField: r1->field_b = r0
    //     0xb82a4c: stur            w0, [x1, #0xb]
    // 0xb82a50: r0 = Row()
    //     0xb82a50: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb82a54: mov             x1, x0
    // 0xb82a58: r0 = Instance_Axis
    //     0xb82a58: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb82a5c: StoreField: r1->field_f = r0
    //     0xb82a5c: stur            w0, [x1, #0xf]
    // 0xb82a60: r0 = Instance_MainAxisAlignment
    //     0xb82a60: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb82a64: ldr             x0, [x0, #0xa8]
    // 0xb82a68: StoreField: r1->field_13 = r0
    //     0xb82a68: stur            w0, [x1, #0x13]
    // 0xb82a6c: r0 = Instance_MainAxisSize
    //     0xb82a6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb82a70: ldr             x0, [x0, #0xa10]
    // 0xb82a74: ArrayStore: r1[0] = r0  ; List_4
    //     0xb82a74: stur            w0, [x1, #0x17]
    // 0xb82a78: r0 = Instance_CrossAxisAlignment
    //     0xb82a78: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb82a7c: ldr             x0, [x0, #0xa18]
    // 0xb82a80: StoreField: r1->field_1b = r0
    //     0xb82a80: stur            w0, [x1, #0x1b]
    // 0xb82a84: r0 = Instance_VerticalDirection
    //     0xb82a84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb82a88: ldr             x0, [x0, #0xa20]
    // 0xb82a8c: StoreField: r1->field_23 = r0
    //     0xb82a8c: stur            w0, [x1, #0x23]
    // 0xb82a90: r0 = Instance_Clip
    //     0xb82a90: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb82a94: ldr             x0, [x0, #0x38]
    // 0xb82a98: StoreField: r1->field_2b = r0
    //     0xb82a98: stur            w0, [x1, #0x2b]
    // 0xb82a9c: StoreField: r1->field_2f = rZR
    //     0xb82a9c: stur            xzr, [x1, #0x2f]
    // 0xb82aa0: ldur            x0, [fp, #-0x18]
    // 0xb82aa4: StoreField: r1->field_b = r0
    //     0xb82aa4: stur            w0, [x1, #0xb]
    // 0xb82aa8: ldur            x0, [fp, #-0x10]
    // 0xb82aac: stur            x1, [fp, #-8]
    // 0xb82ab0: r0 = Card()
    //     0xb82ab0: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb82ab4: r1 = 0.000000
    //     0xb82ab4: ldr             x1, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb82ab8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb82ab8: stur            w1, [x0, #0x17]
    // 0xb82abc: ldur            x1, [fp, #-0x10]
    // 0xb82ac0: StoreField: r0->field_1b = r1
    //     0xb82ac0: stur            w1, [x0, #0x1b]
    // 0xb82ac4: r1 = true
    //     0xb82ac4: add             x1, NULL, #0x20  ; true
    // 0xb82ac8: StoreField: r0->field_1f = r1
    //     0xb82ac8: stur            w1, [x0, #0x1f]
    // 0xb82acc: r2 = Instance_EdgeInsets
    //     0xb82acc: add             x2, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb82ad0: ldr             x2, [x2, #0x878]
    // 0xb82ad4: StoreField: r0->field_27 = r2
    //     0xb82ad4: stur            w2, [x0, #0x27]
    // 0xb82ad8: r2 = Instance_Clip
    //     0xb82ad8: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xb82adc: ldr             x2, [x2, #0xb50]
    // 0xb82ae0: StoreField: r0->field_23 = r2
    //     0xb82ae0: stur            w2, [x0, #0x23]
    // 0xb82ae4: ldur            x2, [fp, #-8]
    // 0xb82ae8: StoreField: r0->field_2f = r2
    //     0xb82ae8: stur            w2, [x0, #0x2f]
    // 0xb82aec: StoreField: r0->field_2b = r1
    //     0xb82aec: stur            w1, [x0, #0x2b]
    // 0xb82af0: r1 = Instance__CardVariant
    //     0xb82af0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb82af4: ldr             x1, [x1, #0xa68]
    // 0xb82af8: StoreField: r0->field_33 = r1
    //     0xb82af8: stur            w1, [x0, #0x33]
    // 0xb82afc: LeaveFrame
    //     0xb82afc: mov             SP, fp
    //     0xb82b00: ldp             fp, lr, [SP], #0x10
    // 0xb82b04: ret
    //     0xb82b04: ret             
    // 0xb82b08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb82b08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb82b0c: b               #0xb8196c
    // 0xb82b10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb82b10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb82b14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb82b14: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb82b18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb82b18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb82b1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb82b1c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb82b20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb82b20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb82b24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb82b24: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb82b28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb82b28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb82b2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb82b2c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb82b30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb82b30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb82b34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb82b34: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb82b38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb82b38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb82b3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb82b3c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb82b40: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb82b40: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb82b44: r0 = RangeErrorSharedWithFPURegs()
    //     0xb82b44: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xb82b48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb82b48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb82b4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb82b4c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb82b50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb82b50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb82b54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb82b54: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb82b58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb82b58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb82b5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb82b5c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb82b60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb82b60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb82b64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb82b64: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4058, size: 0x10, field offset: 0xc
//   const constructor, 
class ProductContentTextAndMedia extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f7f0, size: 0x24
    // 0xc7f7f0: EnterFrame
    //     0xc7f7f0: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f7f4: mov             fp, SP
    // 0xc7f7f8: mov             x0, x1
    // 0xc7f7fc: r1 = <ProductContentTextAndMedia>
    //     0xc7f7fc: add             x1, PP, #0x48, lsl #12  ; [pp+0x48790] TypeArguments: <ProductContentTextAndMedia>
    //     0xc7f800: ldr             x1, [x1, #0x790]
    // 0xc7f804: r0 = _ProductContentTextAndMediaState()
    //     0xc7f804: bl              #0xc7f814  ; Allocate_ProductContentTextAndMediaStateStub -> _ProductContentTextAndMediaState (size=0x14)
    // 0xc7f808: LeaveFrame
    //     0xc7f808: mov             SP, fp
    //     0xc7f80c: ldp             fp, lr, [SP], #0x10
    // 0xc7f810: ret
    //     0xc7f810: ret             
  }
}
