// lib: , url: package:customer_app/app/presentation/views/basic/exchange/exchange_checkout_online_payment_method.dart

// class id: 1049147, size: 0x8
class :: {
}

// class id: 4636, size: 0x14, field offset: 0x14
//   const constructor, 
class ExchangeCheckoutOnlinePaymentMethod extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x130f70c, size: 0x64
    // 0x130f70c: EnterFrame
    //     0x130f70c: stp             fp, lr, [SP, #-0x10]!
    //     0x130f710: mov             fp, SP
    // 0x130f714: AllocStack(0x18)
    //     0x130f714: sub             SP, SP, #0x18
    // 0x130f718: SetupParameters(ExchangeCheckoutOnlinePaymentMethod this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x130f718: stur            x1, [fp, #-8]
    //     0x130f71c: stur            x2, [fp, #-0x10]
    // 0x130f720: r1 = 2
    //     0x130f720: movz            x1, #0x2
    // 0x130f724: r0 = AllocateContext()
    //     0x130f724: bl              #0x16f6108  ; AllocateContextStub
    // 0x130f728: mov             x1, x0
    // 0x130f72c: ldur            x0, [fp, #-8]
    // 0x130f730: stur            x1, [fp, #-0x18]
    // 0x130f734: StoreField: r1->field_f = r0
    //     0x130f734: stur            w0, [x1, #0xf]
    // 0x130f738: ldur            x0, [fp, #-0x10]
    // 0x130f73c: StoreField: r1->field_13 = r0
    //     0x130f73c: stur            w0, [x1, #0x13]
    // 0x130f740: r0 = Obx()
    //     0x130f740: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x130f744: ldur            x2, [fp, #-0x18]
    // 0x130f748: r1 = Function '<anonymous closure>':.
    //     0x130f748: add             x1, PP, #0x46, lsl #12  ; [pp+0x46688] AnonymousClosure: (0x130f770), in [package:customer_app/app/presentation/views/basic/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::bottomNavigationBar (0x130f70c)
    //     0x130f74c: ldr             x1, [x1, #0x688]
    // 0x130f750: stur            x0, [fp, #-8]
    // 0x130f754: r0 = AllocateClosure()
    //     0x130f754: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x130f758: mov             x1, x0
    // 0x130f75c: ldur            x0, [fp, #-8]
    // 0x130f760: StoreField: r0->field_b = r1
    //     0x130f760: stur            w1, [x0, #0xb]
    // 0x130f764: LeaveFrame
    //     0x130f764: mov             SP, fp
    //     0x130f768: ldp             fp, lr, [SP], #0x10
    // 0x130f76c: ret
    //     0x130f76c: ret             
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x130f770, size: 0x610
    // 0x130f770: EnterFrame
    //     0x130f770: stp             fp, lr, [SP, #-0x10]!
    //     0x130f774: mov             fp, SP
    // 0x130f778: AllocStack(0x58)
    //     0x130f778: sub             SP, SP, #0x58
    // 0x130f77c: SetupParameters()
    //     0x130f77c: ldr             x0, [fp, #0x10]
    //     0x130f780: ldur            w2, [x0, #0x17]
    //     0x130f784: add             x2, x2, HEAP, lsl #32
    //     0x130f788: stur            x2, [fp, #-8]
    // 0x130f78c: CheckStackOverflow
    //     0x130f78c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x130f790: cmp             SP, x16
    //     0x130f794: b.ls            #0x130fd68
    // 0x130f798: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x130f798: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x130f79c: ldr             x0, [x0, #0x1c80]
    //     0x130f7a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x130f7a4: cmp             w0, w16
    //     0x130f7a8: b.ne            #0x130f7b4
    //     0x130f7ac: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x130f7b0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x130f7b4: r0 = GetNavigation.size()
    //     0x130f7b4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x130f7b8: LoadField: d0 = r0->field_f
    //     0x130f7b8: ldur            d0, [x0, #0xf]
    // 0x130f7bc: d1 = 0.120000
    //     0x130f7bc: ldr             d1, [PP, #0x54a8]  ; [pp+0x54a8] IMM: double(0.12) from 0x3fbeb851eb851eb8
    // 0x130f7c0: fmul            d2, d0, d1
    // 0x130f7c4: stur            d2, [fp, #-0x48]
    // 0x130f7c8: r1 = _ConstMap len:11
    //     0x130f7c8: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c28] Map<int, List<BoxShadow>>(11)
    //     0x130f7cc: ldr             x1, [x1, #0xc28]
    // 0x130f7d0: r2 = 8
    //     0x130f7d0: movz            x2, #0x8
    // 0x130f7d4: r0 = []()
    //     0x130f7d4: bl              #0x16a3e7c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x130f7d8: stur            x0, [fp, #-0x10]
    // 0x130f7dc: r0 = BoxDecoration()
    //     0x130f7dc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x130f7e0: mov             x2, x0
    // 0x130f7e4: r0 = Instance_Color
    //     0x130f7e4: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x130f7e8: stur            x2, [fp, #-0x18]
    // 0x130f7ec: StoreField: r2->field_7 = r0
    //     0x130f7ec: stur            w0, [x2, #7]
    // 0x130f7f0: ldur            x0, [fp, #-0x10]
    // 0x130f7f4: ArrayStore: r2[0] = r0  ; List_4
    //     0x130f7f4: stur            w0, [x2, #0x17]
    // 0x130f7f8: r0 = Instance_BoxShape
    //     0x130f7f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x130f7fc: ldr             x0, [x0, #0x80]
    // 0x130f800: StoreField: r2->field_23 = r0
    //     0x130f800: stur            w0, [x2, #0x23]
    // 0x130f804: ldur            x0, [fp, #-8]
    // 0x130f808: LoadField: r1 = r0->field_f
    //     0x130f808: ldur            w1, [x0, #0xf]
    // 0x130f80c: DecompressPointer r1
    //     0x130f80c: add             x1, x1, HEAP, lsl #32
    // 0x130f810: r0 = controller()
    //     0x130f810: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130f814: LoadField: r1 = r0->field_53
    //     0x130f814: ldur            w1, [x0, #0x53]
    // 0x130f818: DecompressPointer r1
    //     0x130f818: add             x1, x1, HEAP, lsl #32
    // 0x130f81c: r0 = value()
    //     0x130f81c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130f820: LoadField: r1 = r0->field_f
    //     0x130f820: ldur            w1, [x0, #0xf]
    // 0x130f824: DecompressPointer r1
    //     0x130f824: add             x1, x1, HEAP, lsl #32
    // 0x130f828: cmp             w1, NULL
    // 0x130f82c: b.ne            #0x130f838
    // 0x130f830: r0 = ""
    //     0x130f830: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130f834: b               #0x130f83c
    // 0x130f838: mov             x0, x1
    // 0x130f83c: ldur            x2, [fp, #-8]
    // 0x130f840: stur            x0, [fp, #-0x10]
    // 0x130f844: LoadField: r1 = r2->field_13
    //     0x130f844: ldur            w1, [x2, #0x13]
    // 0x130f848: DecompressPointer r1
    //     0x130f848: add             x1, x1, HEAP, lsl #32
    // 0x130f84c: r0 = of()
    //     0x130f84c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130f850: LoadField: r1 = r0->field_87
    //     0x130f850: ldur            w1, [x0, #0x87]
    // 0x130f854: DecompressPointer r1
    //     0x130f854: add             x1, x1, HEAP, lsl #32
    // 0x130f858: LoadField: r0 = r1->field_2b
    //     0x130f858: ldur            w0, [x1, #0x2b]
    // 0x130f85c: DecompressPointer r0
    //     0x130f85c: add             x0, x0, HEAP, lsl #32
    // 0x130f860: stur            x0, [fp, #-0x20]
    // 0x130f864: r1 = Instance_Color
    //     0x130f864: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x130f868: d0 = 0.700000
    //     0x130f868: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x130f86c: ldr             d0, [x17, #0xf48]
    // 0x130f870: r0 = withOpacity()
    //     0x130f870: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x130f874: r16 = 12.000000
    //     0x130f874: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x130f878: ldr             x16, [x16, #0x9e8]
    // 0x130f87c: stp             x0, x16, [SP]
    // 0x130f880: ldur            x1, [fp, #-0x20]
    // 0x130f884: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x130f884: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x130f888: ldr             x4, [x4, #0xaa0]
    // 0x130f88c: r0 = copyWith()
    //     0x130f88c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x130f890: stur            x0, [fp, #-0x20]
    // 0x130f894: r0 = Text()
    //     0x130f894: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x130f898: mov             x2, x0
    // 0x130f89c: ldur            x0, [fp, #-0x10]
    // 0x130f8a0: stur            x2, [fp, #-0x28]
    // 0x130f8a4: StoreField: r2->field_b = r0
    //     0x130f8a4: stur            w0, [x2, #0xb]
    // 0x130f8a8: ldur            x0, [fp, #-0x20]
    // 0x130f8ac: StoreField: r2->field_13 = r0
    //     0x130f8ac: stur            w0, [x2, #0x13]
    // 0x130f8b0: ldur            x0, [fp, #-8]
    // 0x130f8b4: LoadField: r1 = r0->field_f
    //     0x130f8b4: ldur            w1, [x0, #0xf]
    // 0x130f8b8: DecompressPointer r1
    //     0x130f8b8: add             x1, x1, HEAP, lsl #32
    // 0x130f8bc: r0 = controller()
    //     0x130f8bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130f8c0: LoadField: r1 = r0->field_53
    //     0x130f8c0: ldur            w1, [x0, #0x53]
    // 0x130f8c4: DecompressPointer r1
    //     0x130f8c4: add             x1, x1, HEAP, lsl #32
    // 0x130f8c8: r0 = value()
    //     0x130f8c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130f8cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x130f8cc: ldur            w1, [x0, #0x17]
    // 0x130f8d0: DecompressPointer r1
    //     0x130f8d0: add             x1, x1, HEAP, lsl #32
    // 0x130f8d4: cmp             w1, NULL
    // 0x130f8d8: b.ne            #0x130f8e4
    // 0x130f8dc: r3 = ""
    //     0x130f8dc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130f8e0: b               #0x130f8e8
    // 0x130f8e4: mov             x3, x1
    // 0x130f8e8: ldur            x2, [fp, #-8]
    // 0x130f8ec: ldur            d0, [fp, #-0x48]
    // 0x130f8f0: ldur            x0, [fp, #-0x28]
    // 0x130f8f4: stur            x3, [fp, #-0x10]
    // 0x130f8f8: LoadField: r1 = r2->field_13
    //     0x130f8f8: ldur            w1, [x2, #0x13]
    // 0x130f8fc: DecompressPointer r1
    //     0x130f8fc: add             x1, x1, HEAP, lsl #32
    // 0x130f900: r0 = of()
    //     0x130f900: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130f904: LoadField: r1 = r0->field_87
    //     0x130f904: ldur            w1, [x0, #0x87]
    // 0x130f908: DecompressPointer r1
    //     0x130f908: add             x1, x1, HEAP, lsl #32
    // 0x130f90c: LoadField: r0 = r1->field_7
    //     0x130f90c: ldur            w0, [x1, #7]
    // 0x130f910: DecompressPointer r0
    //     0x130f910: add             x0, x0, HEAP, lsl #32
    // 0x130f914: stur            x0, [fp, #-0x20]
    // 0x130f918: r1 = Instance_Color
    //     0x130f918: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x130f91c: d0 = 0.700000
    //     0x130f91c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x130f920: ldr             d0, [x17, #0xf48]
    // 0x130f924: r0 = withOpacity()
    //     0x130f924: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x130f928: r16 = 16.000000
    //     0x130f928: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x130f92c: ldr             x16, [x16, #0x188]
    // 0x130f930: stp             x0, x16, [SP]
    // 0x130f934: ldur            x1, [fp, #-0x20]
    // 0x130f938: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x130f938: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x130f93c: ldr             x4, [x4, #0xaa0]
    // 0x130f940: r0 = copyWith()
    //     0x130f940: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x130f944: stur            x0, [fp, #-0x20]
    // 0x130f948: r0 = Text()
    //     0x130f948: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x130f94c: mov             x1, x0
    // 0x130f950: ldur            x0, [fp, #-0x10]
    // 0x130f954: stur            x1, [fp, #-0x30]
    // 0x130f958: StoreField: r1->field_b = r0
    //     0x130f958: stur            w0, [x1, #0xb]
    // 0x130f95c: ldur            x0, [fp, #-0x20]
    // 0x130f960: StoreField: r1->field_13 = r0
    //     0x130f960: stur            w0, [x1, #0x13]
    // 0x130f964: r0 = Padding()
    //     0x130f964: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x130f968: mov             x3, x0
    // 0x130f96c: r0 = Instance_EdgeInsets
    //     0x130f96c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0x130f970: ldr             x0, [x0, #0x668]
    // 0x130f974: stur            x3, [fp, #-0x10]
    // 0x130f978: StoreField: r3->field_f = r0
    //     0x130f978: stur            w0, [x3, #0xf]
    // 0x130f97c: ldur            x0, [fp, #-0x30]
    // 0x130f980: StoreField: r3->field_b = r0
    //     0x130f980: stur            w0, [x3, #0xb]
    // 0x130f984: r1 = Null
    //     0x130f984: mov             x1, NULL
    // 0x130f988: r2 = 4
    //     0x130f988: movz            x2, #0x4
    // 0x130f98c: r0 = AllocateArray()
    //     0x130f98c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130f990: mov             x2, x0
    // 0x130f994: ldur            x0, [fp, #-0x28]
    // 0x130f998: stur            x2, [fp, #-0x20]
    // 0x130f99c: StoreField: r2->field_f = r0
    //     0x130f99c: stur            w0, [x2, #0xf]
    // 0x130f9a0: ldur            x0, [fp, #-0x10]
    // 0x130f9a4: StoreField: r2->field_13 = r0
    //     0x130f9a4: stur            w0, [x2, #0x13]
    // 0x130f9a8: r1 = <Widget>
    //     0x130f9a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x130f9ac: r0 = AllocateGrowableArray()
    //     0x130f9ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x130f9b0: mov             x1, x0
    // 0x130f9b4: ldur            x0, [fp, #-0x20]
    // 0x130f9b8: stur            x1, [fp, #-0x10]
    // 0x130f9bc: StoreField: r1->field_f = r0
    //     0x130f9bc: stur            w0, [x1, #0xf]
    // 0x130f9c0: r0 = 4
    //     0x130f9c0: movz            x0, #0x4
    // 0x130f9c4: StoreField: r1->field_b = r0
    //     0x130f9c4: stur            w0, [x1, #0xb]
    // 0x130f9c8: r0 = Column()
    //     0x130f9c8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x130f9cc: mov             x1, x0
    // 0x130f9d0: r0 = Instance_Axis
    //     0x130f9d0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x130f9d4: stur            x1, [fp, #-0x20]
    // 0x130f9d8: StoreField: r1->field_f = r0
    //     0x130f9d8: stur            w0, [x1, #0xf]
    // 0x130f9dc: r0 = Instance_MainAxisAlignment
    //     0x130f9dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x130f9e0: ldr             x0, [x0, #0xab0]
    // 0x130f9e4: StoreField: r1->field_13 = r0
    //     0x130f9e4: stur            w0, [x1, #0x13]
    // 0x130f9e8: r2 = Instance_MainAxisSize
    //     0x130f9e8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x130f9ec: ldr             x2, [x2, #0xa10]
    // 0x130f9f0: ArrayStore: r1[0] = r2  ; List_4
    //     0x130f9f0: stur            w2, [x1, #0x17]
    // 0x130f9f4: r3 = Instance_CrossAxisAlignment
    //     0x130f9f4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x130f9f8: ldr             x3, [x3, #0x890]
    // 0x130f9fc: StoreField: r1->field_1b = r3
    //     0x130f9fc: stur            w3, [x1, #0x1b]
    // 0x130fa00: r3 = Instance_VerticalDirection
    //     0x130fa00: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x130fa04: ldr             x3, [x3, #0xa20]
    // 0x130fa08: StoreField: r1->field_23 = r3
    //     0x130fa08: stur            w3, [x1, #0x23]
    // 0x130fa0c: r4 = Instance_Clip
    //     0x130fa0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x130fa10: ldr             x4, [x4, #0x38]
    // 0x130fa14: StoreField: r1->field_2b = r4
    //     0x130fa14: stur            w4, [x1, #0x2b]
    // 0x130fa18: StoreField: r1->field_2f = rZR
    //     0x130fa18: stur            xzr, [x1, #0x2f]
    // 0x130fa1c: ldur            x5, [fp, #-0x10]
    // 0x130fa20: StoreField: r1->field_b = r5
    //     0x130fa20: stur            w5, [x1, #0xb]
    // 0x130fa24: r16 = <EdgeInsets>
    //     0x130fa24: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x130fa28: ldr             x16, [x16, #0xda0]
    // 0x130fa2c: r30 = Instance_EdgeInsets
    //     0x130fa2c: add             lr, PP, #0x38, lsl #12  ; [pp+0x38178] Obj!EdgeInsets@d59a51
    //     0x130fa30: ldr             lr, [lr, #0x178]
    // 0x130fa34: stp             lr, x16, [SP]
    // 0x130fa38: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x130fa38: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x130fa3c: r0 = all()
    //     0x130fa3c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x130fa40: ldur            x2, [fp, #-8]
    // 0x130fa44: stur            x0, [fp, #-0x10]
    // 0x130fa48: LoadField: r1 = r2->field_13
    //     0x130fa48: ldur            w1, [x2, #0x13]
    // 0x130fa4c: DecompressPointer r1
    //     0x130fa4c: add             x1, x1, HEAP, lsl #32
    // 0x130fa50: r0 = of()
    //     0x130fa50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130fa54: LoadField: r1 = r0->field_5b
    //     0x130fa54: ldur            w1, [x0, #0x5b]
    // 0x130fa58: DecompressPointer r1
    //     0x130fa58: add             x1, x1, HEAP, lsl #32
    // 0x130fa5c: r16 = <Color>
    //     0x130fa5c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x130fa60: ldr             x16, [x16, #0xf80]
    // 0x130fa64: stp             x1, x16, [SP]
    // 0x130fa68: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x130fa68: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x130fa6c: r0 = all()
    //     0x130fa6c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x130fa70: stur            x0, [fp, #-0x28]
    // 0x130fa74: r0 = Radius()
    //     0x130fa74: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x130fa78: d0 = 12.000000
    //     0x130fa78: fmov            d0, #12.00000000
    // 0x130fa7c: stur            x0, [fp, #-0x30]
    // 0x130fa80: StoreField: r0->field_7 = d0
    //     0x130fa80: stur            d0, [x0, #7]
    // 0x130fa84: StoreField: r0->field_f = d0
    //     0x130fa84: stur            d0, [x0, #0xf]
    // 0x130fa88: r0 = BorderRadius()
    //     0x130fa88: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x130fa8c: mov             x2, x0
    // 0x130fa90: ldur            x0, [fp, #-0x30]
    // 0x130fa94: stur            x2, [fp, #-0x38]
    // 0x130fa98: StoreField: r2->field_7 = r0
    //     0x130fa98: stur            w0, [x2, #7]
    // 0x130fa9c: StoreField: r2->field_b = r0
    //     0x130fa9c: stur            w0, [x2, #0xb]
    // 0x130faa0: StoreField: r2->field_f = r0
    //     0x130faa0: stur            w0, [x2, #0xf]
    // 0x130faa4: StoreField: r2->field_13 = r0
    //     0x130faa4: stur            w0, [x2, #0x13]
    // 0x130faa8: ldur            x0, [fp, #-8]
    // 0x130faac: LoadField: r1 = r0->field_13
    //     0x130faac: ldur            w1, [x0, #0x13]
    // 0x130fab0: DecompressPointer r1
    //     0x130fab0: add             x1, x1, HEAP, lsl #32
    // 0x130fab4: r0 = of()
    //     0x130fab4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130fab8: LoadField: r1 = r0->field_5b
    //     0x130fab8: ldur            w1, [x0, #0x5b]
    // 0x130fabc: DecompressPointer r1
    //     0x130fabc: add             x1, x1, HEAP, lsl #32
    // 0x130fac0: stur            x1, [fp, #-0x30]
    // 0x130fac4: r0 = BorderSide()
    //     0x130fac4: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x130fac8: mov             x1, x0
    // 0x130facc: ldur            x0, [fp, #-0x30]
    // 0x130fad0: stur            x1, [fp, #-0x40]
    // 0x130fad4: StoreField: r1->field_7 = r0
    //     0x130fad4: stur            w0, [x1, #7]
    // 0x130fad8: d0 = 1.000000
    //     0x130fad8: fmov            d0, #1.00000000
    // 0x130fadc: StoreField: r1->field_b = d0
    //     0x130fadc: stur            d0, [x1, #0xb]
    // 0x130fae0: r0 = Instance_BorderStyle
    //     0x130fae0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x130fae4: ldr             x0, [x0, #0xf68]
    // 0x130fae8: StoreField: r1->field_13 = r0
    //     0x130fae8: stur            w0, [x1, #0x13]
    // 0x130faec: d0 = -1.000000
    //     0x130faec: fmov            d0, #-1.00000000
    // 0x130faf0: ArrayStore: r1[0] = d0  ; List_8
    //     0x130faf0: stur            d0, [x1, #0x17]
    // 0x130faf4: r0 = RoundedRectangleBorder()
    //     0x130faf4: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x130faf8: mov             x1, x0
    // 0x130fafc: ldur            x0, [fp, #-0x38]
    // 0x130fb00: StoreField: r1->field_b = r0
    //     0x130fb00: stur            w0, [x1, #0xb]
    // 0x130fb04: ldur            x0, [fp, #-0x40]
    // 0x130fb08: StoreField: r1->field_7 = r0
    //     0x130fb08: stur            w0, [x1, #7]
    // 0x130fb0c: r16 = <RoundedRectangleBorder>
    //     0x130fb0c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x130fb10: ldr             x16, [x16, #0xf78]
    // 0x130fb14: stp             x1, x16, [SP]
    // 0x130fb18: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x130fb18: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x130fb1c: r0 = all()
    //     0x130fb1c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x130fb20: stur            x0, [fp, #-0x30]
    // 0x130fb24: r0 = ButtonStyle()
    //     0x130fb24: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x130fb28: mov             x1, x0
    // 0x130fb2c: ldur            x0, [fp, #-0x28]
    // 0x130fb30: stur            x1, [fp, #-0x38]
    // 0x130fb34: StoreField: r1->field_b = r0
    //     0x130fb34: stur            w0, [x1, #0xb]
    // 0x130fb38: ldur            x0, [fp, #-0x10]
    // 0x130fb3c: StoreField: r1->field_23 = r0
    //     0x130fb3c: stur            w0, [x1, #0x23]
    // 0x130fb40: ldur            x0, [fp, #-0x30]
    // 0x130fb44: StoreField: r1->field_43 = r0
    //     0x130fb44: stur            w0, [x1, #0x43]
    // 0x130fb48: r0 = TextButtonThemeData()
    //     0x130fb48: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x130fb4c: mov             x2, x0
    // 0x130fb50: ldur            x0, [fp, #-0x38]
    // 0x130fb54: stur            x2, [fp, #-0x10]
    // 0x130fb58: StoreField: r2->field_7 = r0
    //     0x130fb58: stur            w0, [x2, #7]
    // 0x130fb5c: ldur            x0, [fp, #-8]
    // 0x130fb60: LoadField: r1 = r0->field_13
    //     0x130fb60: ldur            w1, [x0, #0x13]
    // 0x130fb64: DecompressPointer r1
    //     0x130fb64: add             x1, x1, HEAP, lsl #32
    // 0x130fb68: r0 = of()
    //     0x130fb68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130fb6c: LoadField: r1 = r0->field_87
    //     0x130fb6c: ldur            w1, [x0, #0x87]
    // 0x130fb70: DecompressPointer r1
    //     0x130fb70: add             x1, x1, HEAP, lsl #32
    // 0x130fb74: LoadField: r0 = r1->field_7
    //     0x130fb74: ldur            w0, [x1, #7]
    // 0x130fb78: DecompressPointer r0
    //     0x130fb78: add             x0, x0, HEAP, lsl #32
    // 0x130fb7c: r16 = Instance_Color
    //     0x130fb7c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x130fb80: r30 = 14.000000
    //     0x130fb80: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x130fb84: ldr             lr, [lr, #0x1d8]
    // 0x130fb88: stp             lr, x16, [SP]
    // 0x130fb8c: mov             x1, x0
    // 0x130fb90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x130fb90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x130fb94: ldr             x4, [x4, #0x9b8]
    // 0x130fb98: r0 = copyWith()
    //     0x130fb98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x130fb9c: stur            x0, [fp, #-0x28]
    // 0x130fba0: r0 = Text()
    //     0x130fba0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x130fba4: mov             x3, x0
    // 0x130fba8: r0 = "CHECKOUT"
    //     0x130fba8: add             x0, PP, #0x39, lsl #12  ; [pp+0x39858] "CHECKOUT"
    //     0x130fbac: ldr             x0, [x0, #0x858]
    // 0x130fbb0: stur            x3, [fp, #-0x30]
    // 0x130fbb4: StoreField: r3->field_b = r0
    //     0x130fbb4: stur            w0, [x3, #0xb]
    // 0x130fbb8: ldur            x0, [fp, #-0x28]
    // 0x130fbbc: StoreField: r3->field_13 = r0
    //     0x130fbbc: stur            w0, [x3, #0x13]
    // 0x130fbc0: ldur            x2, [fp, #-8]
    // 0x130fbc4: r1 = Function '<anonymous closure>':.
    //     0x130fbc4: add             x1, PP, #0x46, lsl #12  ; [pp+0x46690] AnonymousClosure: (0x130fd80), in [package:customer_app/app/presentation/views/glass/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::bottomNavigationBar (0x1360280)
    //     0x130fbc8: ldr             x1, [x1, #0x690]
    // 0x130fbcc: r0 = AllocateClosure()
    //     0x130fbcc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x130fbd0: stur            x0, [fp, #-8]
    // 0x130fbd4: r0 = TextButton()
    //     0x130fbd4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x130fbd8: mov             x1, x0
    // 0x130fbdc: ldur            x0, [fp, #-8]
    // 0x130fbe0: stur            x1, [fp, #-0x28]
    // 0x130fbe4: StoreField: r1->field_b = r0
    //     0x130fbe4: stur            w0, [x1, #0xb]
    // 0x130fbe8: r0 = false
    //     0x130fbe8: add             x0, NULL, #0x30  ; false
    // 0x130fbec: StoreField: r1->field_27 = r0
    //     0x130fbec: stur            w0, [x1, #0x27]
    // 0x130fbf0: r0 = true
    //     0x130fbf0: add             x0, NULL, #0x20  ; true
    // 0x130fbf4: StoreField: r1->field_2f = r0
    //     0x130fbf4: stur            w0, [x1, #0x2f]
    // 0x130fbf8: ldur            x0, [fp, #-0x30]
    // 0x130fbfc: StoreField: r1->field_37 = r0
    //     0x130fbfc: stur            w0, [x1, #0x37]
    // 0x130fc00: r0 = Instance_ValueKey
    //     0x130fc00: add             x0, PP, #0x39, lsl #12  ; [pp+0x39868] Obj!ValueKey<String>@d5b371
    //     0x130fc04: ldr             x0, [x0, #0x868]
    // 0x130fc08: StoreField: r1->field_7 = r0
    //     0x130fc08: stur            w0, [x1, #7]
    // 0x130fc0c: r0 = TextButtonTheme()
    //     0x130fc0c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x130fc10: mov             x3, x0
    // 0x130fc14: ldur            x0, [fp, #-0x10]
    // 0x130fc18: stur            x3, [fp, #-8]
    // 0x130fc1c: StoreField: r3->field_f = r0
    //     0x130fc1c: stur            w0, [x3, #0xf]
    // 0x130fc20: ldur            x0, [fp, #-0x28]
    // 0x130fc24: StoreField: r3->field_b = r0
    //     0x130fc24: stur            w0, [x3, #0xb]
    // 0x130fc28: r1 = Null
    //     0x130fc28: mov             x1, NULL
    // 0x130fc2c: r2 = 6
    //     0x130fc2c: movz            x2, #0x6
    // 0x130fc30: r0 = AllocateArray()
    //     0x130fc30: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130fc34: mov             x2, x0
    // 0x130fc38: ldur            x0, [fp, #-0x20]
    // 0x130fc3c: stur            x2, [fp, #-0x10]
    // 0x130fc40: StoreField: r2->field_f = r0
    //     0x130fc40: stur            w0, [x2, #0xf]
    // 0x130fc44: r16 = Instance_Spacer
    //     0x130fc44: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x130fc48: ldr             x16, [x16, #0xf0]
    // 0x130fc4c: StoreField: r2->field_13 = r16
    //     0x130fc4c: stur            w16, [x2, #0x13]
    // 0x130fc50: ldur            x0, [fp, #-8]
    // 0x130fc54: ArrayStore: r2[0] = r0  ; List_4
    //     0x130fc54: stur            w0, [x2, #0x17]
    // 0x130fc58: r1 = <Widget>
    //     0x130fc58: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x130fc5c: r0 = AllocateGrowableArray()
    //     0x130fc5c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x130fc60: mov             x1, x0
    // 0x130fc64: ldur            x0, [fp, #-0x10]
    // 0x130fc68: stur            x1, [fp, #-8]
    // 0x130fc6c: StoreField: r1->field_f = r0
    //     0x130fc6c: stur            w0, [x1, #0xf]
    // 0x130fc70: r0 = 6
    //     0x130fc70: movz            x0, #0x6
    // 0x130fc74: StoreField: r1->field_b = r0
    //     0x130fc74: stur            w0, [x1, #0xb]
    // 0x130fc78: r0 = Row()
    //     0x130fc78: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x130fc7c: mov             x1, x0
    // 0x130fc80: r0 = Instance_Axis
    //     0x130fc80: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x130fc84: stur            x1, [fp, #-0x10]
    // 0x130fc88: StoreField: r1->field_f = r0
    //     0x130fc88: stur            w0, [x1, #0xf]
    // 0x130fc8c: r0 = Instance_MainAxisAlignment
    //     0x130fc8c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x130fc90: ldr             x0, [x0, #0xab0]
    // 0x130fc94: StoreField: r1->field_13 = r0
    //     0x130fc94: stur            w0, [x1, #0x13]
    // 0x130fc98: r0 = Instance_MainAxisSize
    //     0x130fc98: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x130fc9c: ldr             x0, [x0, #0xa10]
    // 0x130fca0: ArrayStore: r1[0] = r0  ; List_4
    //     0x130fca0: stur            w0, [x1, #0x17]
    // 0x130fca4: r0 = Instance_CrossAxisAlignment
    //     0x130fca4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x130fca8: ldr             x0, [x0, #0xa18]
    // 0x130fcac: StoreField: r1->field_1b = r0
    //     0x130fcac: stur            w0, [x1, #0x1b]
    // 0x130fcb0: r0 = Instance_VerticalDirection
    //     0x130fcb0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x130fcb4: ldr             x0, [x0, #0xa20]
    // 0x130fcb8: StoreField: r1->field_23 = r0
    //     0x130fcb8: stur            w0, [x1, #0x23]
    // 0x130fcbc: r0 = Instance_Clip
    //     0x130fcbc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x130fcc0: ldr             x0, [x0, #0x38]
    // 0x130fcc4: StoreField: r1->field_2b = r0
    //     0x130fcc4: stur            w0, [x1, #0x2b]
    // 0x130fcc8: StoreField: r1->field_2f = rZR
    //     0x130fcc8: stur            xzr, [x1, #0x2f]
    // 0x130fccc: ldur            x0, [fp, #-8]
    // 0x130fcd0: StoreField: r1->field_b = r0
    //     0x130fcd0: stur            w0, [x1, #0xb]
    // 0x130fcd4: r0 = Padding()
    //     0x130fcd4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x130fcd8: mov             x1, x0
    // 0x130fcdc: r0 = Instance_EdgeInsets
    //     0x130fcdc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x130fce0: ldr             x0, [x0, #0x1f0]
    // 0x130fce4: stur            x1, [fp, #-8]
    // 0x130fce8: StoreField: r1->field_f = r0
    //     0x130fce8: stur            w0, [x1, #0xf]
    // 0x130fcec: ldur            x0, [fp, #-0x10]
    // 0x130fcf0: StoreField: r1->field_b = r0
    //     0x130fcf0: stur            w0, [x1, #0xb]
    // 0x130fcf4: r0 = Container()
    //     0x130fcf4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x130fcf8: stur            x0, [fp, #-0x10]
    // 0x130fcfc: ldur            x16, [fp, #-0x18]
    // 0x130fd00: ldur            lr, [fp, #-8]
    // 0x130fd04: stp             lr, x16, [SP]
    // 0x130fd08: mov             x1, x0
    // 0x130fd0c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x130fd0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x130fd10: ldr             x4, [x4, #0x88]
    // 0x130fd14: r0 = Container()
    //     0x130fd14: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x130fd18: ldur            d0, [fp, #-0x48]
    // 0x130fd1c: r0 = inline_Allocate_Double()
    //     0x130fd1c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x130fd20: add             x0, x0, #0x10
    //     0x130fd24: cmp             x1, x0
    //     0x130fd28: b.ls            #0x130fd70
    //     0x130fd2c: str             x0, [THR, #0x50]  ; THR::top
    //     0x130fd30: sub             x0, x0, #0xf
    //     0x130fd34: movz            x1, #0xe15c
    //     0x130fd38: movk            x1, #0x3, lsl #16
    //     0x130fd3c: stur            x1, [x0, #-1]
    // 0x130fd40: StoreField: r0->field_7 = d0
    //     0x130fd40: stur            d0, [x0, #7]
    // 0x130fd44: stur            x0, [fp, #-8]
    // 0x130fd48: r0 = SizedBox()
    //     0x130fd48: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x130fd4c: ldur            x1, [fp, #-8]
    // 0x130fd50: StoreField: r0->field_13 = r1
    //     0x130fd50: stur            w1, [x0, #0x13]
    // 0x130fd54: ldur            x1, [fp, #-0x10]
    // 0x130fd58: StoreField: r0->field_b = r1
    //     0x130fd58: stur            w1, [x0, #0xb]
    // 0x130fd5c: LeaveFrame
    //     0x130fd5c: mov             SP, fp
    //     0x130fd60: ldp             fp, lr, [SP], #0x10
    // 0x130fd64: ret
    //     0x130fd64: ret             
    // 0x130fd68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x130fd68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x130fd6c: b               #0x130f798
    // 0x130fd70: SaveReg d0
    //     0x130fd70: str             q0, [SP, #-0x10]!
    // 0x130fd74: r0 = AllocateDouble()
    //     0x130fd74: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x130fd78: RestoreReg d0
    //     0x130fd78: ldr             q0, [SP], #0x10
    // 0x130fd7c: b               #0x130fd40
  }
  _ body(/* No info */) {
    // ** addr: 0x13bf888, size: 0x64
    // 0x13bf888: EnterFrame
    //     0x13bf888: stp             fp, lr, [SP, #-0x10]!
    //     0x13bf88c: mov             fp, SP
    // 0x13bf890: AllocStack(0x18)
    //     0x13bf890: sub             SP, SP, #0x18
    // 0x13bf894: SetupParameters(ExchangeCheckoutOnlinePaymentMethod this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x13bf894: stur            x1, [fp, #-8]
    //     0x13bf898: stur            x2, [fp, #-0x10]
    // 0x13bf89c: r1 = 2
    //     0x13bf89c: movz            x1, #0x2
    // 0x13bf8a0: r0 = AllocateContext()
    //     0x13bf8a0: bl              #0x16f6108  ; AllocateContextStub
    // 0x13bf8a4: mov             x1, x0
    // 0x13bf8a8: ldur            x0, [fp, #-8]
    // 0x13bf8ac: stur            x1, [fp, #-0x18]
    // 0x13bf8b0: StoreField: r1->field_f = r0
    //     0x13bf8b0: stur            w0, [x1, #0xf]
    // 0x13bf8b4: ldur            x0, [fp, #-0x10]
    // 0x13bf8b8: StoreField: r1->field_13 = r0
    //     0x13bf8b8: stur            w0, [x1, #0x13]
    // 0x13bf8bc: r0 = Obx()
    //     0x13bf8bc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13bf8c0: ldur            x2, [fp, #-0x18]
    // 0x13bf8c4: r1 = Function '<anonymous closure>':.
    //     0x13bf8c4: add             x1, PP, #0x46, lsl #12  ; [pp+0x46698] AnonymousClosure: (0x13bf8ec), in [package:customer_app/app/presentation/views/basic/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::body (0x13bf888)
    //     0x13bf8c8: ldr             x1, [x1, #0x698]
    // 0x13bf8cc: stur            x0, [fp, #-8]
    // 0x13bf8d0: r0 = AllocateClosure()
    //     0x13bf8d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bf8d4: mov             x1, x0
    // 0x13bf8d8: ldur            x0, [fp, #-8]
    // 0x13bf8dc: StoreField: r0->field_b = r1
    //     0x13bf8dc: stur            w1, [x0, #0xb]
    // 0x13bf8e0: LeaveFrame
    //     0x13bf8e0: mov             SP, fp
    //     0x13bf8e4: ldp             fp, lr, [SP], #0x10
    // 0x13bf8e8: ret
    //     0x13bf8e8: ret             
  }
  [closure] ListView <anonymous closure>(dynamic) {
    // ** addr: 0x13bf8ec, size: 0x1c48
    // 0x13bf8ec: EnterFrame
    //     0x13bf8ec: stp             fp, lr, [SP, #-0x10]!
    //     0x13bf8f0: mov             fp, SP
    // 0x13bf8f4: AllocStack(0x88)
    //     0x13bf8f4: sub             SP, SP, #0x88
    // 0x13bf8f8: SetupParameters()
    //     0x13bf8f8: ldr             x0, [fp, #0x10]
    //     0x13bf8fc: ldur            w2, [x0, #0x17]
    //     0x13bf900: add             x2, x2, HEAP, lsl #32
    //     0x13bf904: stur            x2, [fp, #-8]
    // 0x13bf908: CheckStackOverflow
    //     0x13bf908: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bf90c: cmp             SP, x16
    //     0x13bf910: b.ls            #0x13c14e8
    // 0x13bf914: LoadField: r1 = r2->field_13
    //     0x13bf914: ldur            w1, [x2, #0x13]
    // 0x13bf918: DecompressPointer r1
    //     0x13bf918: add             x1, x1, HEAP, lsl #32
    // 0x13bf91c: r0 = of()
    //     0x13bf91c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13bf920: LoadField: r1 = r0->field_87
    //     0x13bf920: ldur            w1, [x0, #0x87]
    // 0x13bf924: DecompressPointer r1
    //     0x13bf924: add             x1, x1, HEAP, lsl #32
    // 0x13bf928: LoadField: r0 = r1->field_7
    //     0x13bf928: ldur            w0, [x1, #7]
    // 0x13bf92c: DecompressPointer r0
    //     0x13bf92c: add             x0, x0, HEAP, lsl #32
    // 0x13bf930: r16 = Instance_Color
    //     0x13bf930: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13bf934: r30 = 14.000000
    //     0x13bf934: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13bf938: ldr             lr, [lr, #0x1d8]
    // 0x13bf93c: stp             lr, x16, [SP]
    // 0x13bf940: mov             x1, x0
    // 0x13bf944: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13bf944: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13bf948: ldr             x4, [x4, #0x9b8]
    // 0x13bf94c: r0 = copyWith()
    //     0x13bf94c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13bf950: stur            x0, [fp, #-0x10]
    // 0x13bf954: r0 = Text()
    //     0x13bf954: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13bf958: mov             x1, x0
    // 0x13bf95c: r0 = "Bag"
    //     0x13bf95c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d60] "Bag"
    //     0x13bf960: ldr             x0, [x0, #0xd60]
    // 0x13bf964: stur            x1, [fp, #-0x18]
    // 0x13bf968: StoreField: r1->field_b = r0
    //     0x13bf968: stur            w0, [x1, #0xb]
    // 0x13bf96c: ldur            x0, [fp, #-0x10]
    // 0x13bf970: StoreField: r1->field_13 = r0
    //     0x13bf970: stur            w0, [x1, #0x13]
    // 0x13bf974: r0 = Radius()
    //     0x13bf974: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x13bf978: d0 = 12.000000
    //     0x13bf978: fmov            d0, #12.00000000
    // 0x13bf97c: stur            x0, [fp, #-0x10]
    // 0x13bf980: StoreField: r0->field_7 = d0
    //     0x13bf980: stur            d0, [x0, #7]
    // 0x13bf984: StoreField: r0->field_f = d0
    //     0x13bf984: stur            d0, [x0, #0xf]
    // 0x13bf988: r0 = BorderRadius()
    //     0x13bf988: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x13bf98c: mov             x2, x0
    // 0x13bf990: ldur            x0, [fp, #-0x10]
    // 0x13bf994: stur            x2, [fp, #-0x20]
    // 0x13bf998: StoreField: r2->field_7 = r0
    //     0x13bf998: stur            w0, [x2, #7]
    // 0x13bf99c: StoreField: r2->field_b = r0
    //     0x13bf99c: stur            w0, [x2, #0xb]
    // 0x13bf9a0: StoreField: r2->field_f = r0
    //     0x13bf9a0: stur            w0, [x2, #0xf]
    // 0x13bf9a4: StoreField: r2->field_13 = r0
    //     0x13bf9a4: stur            w0, [x2, #0x13]
    // 0x13bf9a8: ldur            x0, [fp, #-8]
    // 0x13bf9ac: LoadField: r1 = r0->field_f
    //     0x13bf9ac: ldur            w1, [x0, #0xf]
    // 0x13bf9b0: DecompressPointer r1
    //     0x13bf9b0: add             x1, x1, HEAP, lsl #32
    // 0x13bf9b4: r0 = controller()
    //     0x13bf9b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf9b8: LoadField: r1 = r0->field_53
    //     0x13bf9b8: ldur            w1, [x0, #0x53]
    // 0x13bf9bc: DecompressPointer r1
    //     0x13bf9bc: add             x1, x1, HEAP, lsl #32
    // 0x13bf9c0: r0 = value()
    //     0x13bf9c0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13bf9c4: LoadField: r2 = r0->field_5b
    //     0x13bf9c4: ldur            w2, [x0, #0x5b]
    // 0x13bf9c8: DecompressPointer r2
    //     0x13bf9c8: add             x2, x2, HEAP, lsl #32
    // 0x13bf9cc: cmp             w2, NULL
    // 0x13bf9d0: b.ne            #0x13bf9dc
    // 0x13bf9d4: r0 = Null
    //     0x13bf9d4: mov             x0, NULL
    // 0x13bf9d8: b               #0x13bfa0c
    // 0x13bf9dc: LoadField: r0 = r2->field_b
    //     0x13bf9dc: ldur            w0, [x2, #0xb]
    // 0x13bf9e0: r1 = LoadInt32Instr(r0)
    //     0x13bf9e0: sbfx            x1, x0, #1, #0x1f
    // 0x13bf9e4: mov             x0, x1
    // 0x13bf9e8: r1 = 0
    //     0x13bf9e8: movz            x1, #0
    // 0x13bf9ec: cmp             x1, x0
    // 0x13bf9f0: b.hs            #0x13c14f0
    // 0x13bf9f4: LoadField: r0 = r2->field_f
    //     0x13bf9f4: ldur            w0, [x2, #0xf]
    // 0x13bf9f8: DecompressPointer r0
    //     0x13bf9f8: add             x0, x0, HEAP, lsl #32
    // 0x13bf9fc: LoadField: r1 = r0->field_f
    //     0x13bf9fc: ldur            w1, [x0, #0xf]
    // 0x13bfa00: DecompressPointer r1
    //     0x13bfa00: add             x1, x1, HEAP, lsl #32
    // 0x13bfa04: LoadField: r0 = r1->field_f
    //     0x13bfa04: ldur            w0, [x1, #0xf]
    // 0x13bfa08: DecompressPointer r0
    //     0x13bfa08: add             x0, x0, HEAP, lsl #32
    // 0x13bfa0c: cmp             w0, NULL
    // 0x13bfa10: b.ne            #0x13bfa1c
    // 0x13bfa14: r4 = ""
    //     0x13bfa14: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13bfa18: b               #0x13bfa20
    // 0x13bfa1c: mov             x4, x0
    // 0x13bfa20: ldur            x3, [fp, #-8]
    // 0x13bfa24: ldur            x0, [fp, #-0x20]
    // 0x13bfa28: stur            x4, [fp, #-0x10]
    // 0x13bfa2c: r1 = Function '<anonymous closure>':.
    //     0x13bfa2c: add             x1, PP, #0x46, lsl #12  ; [pp+0x466a0] AnonymousClosure: (0x9d9cb4), in [package:customer_app/app/presentation/views/line/post_order/order_detail/order_detail_view.dart] OrderDetailView::body (0x1506b64)
    //     0x13bfa30: ldr             x1, [x1, #0x6a0]
    // 0x13bfa34: r2 = Null
    //     0x13bfa34: mov             x2, NULL
    // 0x13bfa38: r0 = AllocateClosure()
    //     0x13bfa38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bfa3c: stur            x0, [fp, #-0x28]
    // 0x13bfa40: r0 = CachedNetworkImage()
    //     0x13bfa40: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x13bfa44: stur            x0, [fp, #-0x30]
    // 0x13bfa48: r16 = Instance_BoxFit
    //     0x13bfa48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x13bfa4c: ldr             x16, [x16, #0x118]
    // 0x13bfa50: r30 = 60.000000
    //     0x13bfa50: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x13bfa54: ldr             lr, [lr, #0x110]
    // 0x13bfa58: stp             lr, x16, [SP, #0x10]
    // 0x13bfa5c: r16 = 60.000000
    //     0x13bfa5c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x13bfa60: ldr             x16, [x16, #0x110]
    // 0x13bfa64: ldur            lr, [fp, #-0x28]
    // 0x13bfa68: stp             lr, x16, [SP]
    // 0x13bfa6c: mov             x1, x0
    // 0x13bfa70: ldur            x2, [fp, #-0x10]
    // 0x13bfa74: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x13bfa74: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fbf8] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x13bfa78: ldr             x4, [x4, #0xbf8]
    // 0x13bfa7c: r0 = CachedNetworkImage()
    //     0x13bfa7c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x13bfa80: r0 = ClipRRect()
    //     0x13bfa80: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x13bfa84: mov             x2, x0
    // 0x13bfa88: ldur            x0, [fp, #-0x20]
    // 0x13bfa8c: stur            x2, [fp, #-0x10]
    // 0x13bfa90: StoreField: r2->field_f = r0
    //     0x13bfa90: stur            w0, [x2, #0xf]
    // 0x13bfa94: r0 = Instance_Clip
    //     0x13bfa94: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x13bfa98: ldr             x0, [x0, #0x138]
    // 0x13bfa9c: ArrayStore: r2[0] = r0  ; List_4
    //     0x13bfa9c: stur            w0, [x2, #0x17]
    // 0x13bfaa0: ldur            x1, [fp, #-0x30]
    // 0x13bfaa4: StoreField: r2->field_b = r1
    //     0x13bfaa4: stur            w1, [x2, #0xb]
    // 0x13bfaa8: ldur            x3, [fp, #-8]
    // 0x13bfaac: LoadField: r1 = r3->field_f
    //     0x13bfaac: ldur            w1, [x3, #0xf]
    // 0x13bfab0: DecompressPointer r1
    //     0x13bfab0: add             x1, x1, HEAP, lsl #32
    // 0x13bfab4: r0 = controller()
    //     0x13bfab4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bfab8: LoadField: r1 = r0->field_53
    //     0x13bfab8: ldur            w1, [x0, #0x53]
    // 0x13bfabc: DecompressPointer r1
    //     0x13bfabc: add             x1, x1, HEAP, lsl #32
    // 0x13bfac0: r0 = value()
    //     0x13bfac0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13bfac4: LoadField: r2 = r0->field_5b
    //     0x13bfac4: ldur            w2, [x0, #0x5b]
    // 0x13bfac8: DecompressPointer r2
    //     0x13bfac8: add             x2, x2, HEAP, lsl #32
    // 0x13bfacc: cmp             w2, NULL
    // 0x13bfad0: b.ne            #0x13bfadc
    // 0x13bfad4: r0 = Null
    //     0x13bfad4: mov             x0, NULL
    // 0x13bfad8: b               #0x13bfb0c
    // 0x13bfadc: LoadField: r0 = r2->field_b
    //     0x13bfadc: ldur            w0, [x2, #0xb]
    // 0x13bfae0: r1 = LoadInt32Instr(r0)
    //     0x13bfae0: sbfx            x1, x0, #1, #0x1f
    // 0x13bfae4: mov             x0, x1
    // 0x13bfae8: r1 = 0
    //     0x13bfae8: movz            x1, #0
    // 0x13bfaec: cmp             x1, x0
    // 0x13bfaf0: b.hs            #0x13c14f4
    // 0x13bfaf4: LoadField: r0 = r2->field_f
    //     0x13bfaf4: ldur            w0, [x2, #0xf]
    // 0x13bfaf8: DecompressPointer r0
    //     0x13bfaf8: add             x0, x0, HEAP, lsl #32
    // 0x13bfafc: LoadField: r1 = r0->field_f
    //     0x13bfafc: ldur            w1, [x0, #0xf]
    // 0x13bfb00: DecompressPointer r1
    //     0x13bfb00: add             x1, x1, HEAP, lsl #32
    // 0x13bfb04: LoadField: r0 = r1->field_13
    //     0x13bfb04: ldur            w0, [x1, #0x13]
    // 0x13bfb08: DecompressPointer r0
    //     0x13bfb08: add             x0, x0, HEAP, lsl #32
    // 0x13bfb0c: cmp             w0, NULL
    // 0x13bfb10: b.ne            #0x13bfb18
    // 0x13bfb14: r0 = ""
    //     0x13bfb14: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13bfb18: ldur            x2, [fp, #-8]
    // 0x13bfb1c: stur            x0, [fp, #-0x20]
    // 0x13bfb20: LoadField: r1 = r2->field_13
    //     0x13bfb20: ldur            w1, [x2, #0x13]
    // 0x13bfb24: DecompressPointer r1
    //     0x13bfb24: add             x1, x1, HEAP, lsl #32
    // 0x13bfb28: r0 = of()
    //     0x13bfb28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13bfb2c: LoadField: r1 = r0->field_87
    //     0x13bfb2c: ldur            w1, [x0, #0x87]
    // 0x13bfb30: DecompressPointer r1
    //     0x13bfb30: add             x1, x1, HEAP, lsl #32
    // 0x13bfb34: LoadField: r0 = r1->field_7
    //     0x13bfb34: ldur            w0, [x1, #7]
    // 0x13bfb38: DecompressPointer r0
    //     0x13bfb38: add             x0, x0, HEAP, lsl #32
    // 0x13bfb3c: r16 = Instance_Color
    //     0x13bfb3c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13bfb40: r30 = 12.000000
    //     0x13bfb40: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13bfb44: ldr             lr, [lr, #0x9e8]
    // 0x13bfb48: stp             lr, x16, [SP]
    // 0x13bfb4c: mov             x1, x0
    // 0x13bfb50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13bfb50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13bfb54: ldr             x4, [x4, #0x9b8]
    // 0x13bfb58: r0 = copyWith()
    //     0x13bfb58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13bfb5c: stur            x0, [fp, #-0x28]
    // 0x13bfb60: r0 = Text()
    //     0x13bfb60: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13bfb64: mov             x2, x0
    // 0x13bfb68: ldur            x0, [fp, #-0x20]
    // 0x13bfb6c: stur            x2, [fp, #-0x30]
    // 0x13bfb70: StoreField: r2->field_b = r0
    //     0x13bfb70: stur            w0, [x2, #0xb]
    // 0x13bfb74: ldur            x0, [fp, #-0x28]
    // 0x13bfb78: StoreField: r2->field_13 = r0
    //     0x13bfb78: stur            w0, [x2, #0x13]
    // 0x13bfb7c: r0 = 2
    //     0x13bfb7c: movz            x0, #0x2
    // 0x13bfb80: StoreField: r2->field_37 = r0
    //     0x13bfb80: stur            w0, [x2, #0x37]
    // 0x13bfb84: ldur            x3, [fp, #-8]
    // 0x13bfb88: LoadField: r1 = r3->field_f
    //     0x13bfb88: ldur            w1, [x3, #0xf]
    // 0x13bfb8c: DecompressPointer r1
    //     0x13bfb8c: add             x1, x1, HEAP, lsl #32
    // 0x13bfb90: r0 = controller()
    //     0x13bfb90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bfb94: LoadField: r1 = r0->field_53
    //     0x13bfb94: ldur            w1, [x0, #0x53]
    // 0x13bfb98: DecompressPointer r1
    //     0x13bfb98: add             x1, x1, HEAP, lsl #32
    // 0x13bfb9c: r0 = value()
    //     0x13bfb9c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13bfba0: LoadField: r2 = r0->field_5b
    //     0x13bfba0: ldur            w2, [x0, #0x5b]
    // 0x13bfba4: DecompressPointer r2
    //     0x13bfba4: add             x2, x2, HEAP, lsl #32
    // 0x13bfba8: cmp             w2, NULL
    // 0x13bfbac: b.ne            #0x13bfbb8
    // 0x13bfbb0: r0 = Null
    //     0x13bfbb0: mov             x0, NULL
    // 0x13bfbb4: b               #0x13bfbe8
    // 0x13bfbb8: LoadField: r0 = r2->field_b
    //     0x13bfbb8: ldur            w0, [x2, #0xb]
    // 0x13bfbbc: r1 = LoadInt32Instr(r0)
    //     0x13bfbbc: sbfx            x1, x0, #1, #0x1f
    // 0x13bfbc0: mov             x0, x1
    // 0x13bfbc4: r1 = 0
    //     0x13bfbc4: movz            x1, #0
    // 0x13bfbc8: cmp             x1, x0
    // 0x13bfbcc: b.hs            #0x13c14f8
    // 0x13bfbd0: LoadField: r0 = r2->field_f
    //     0x13bfbd0: ldur            w0, [x2, #0xf]
    // 0x13bfbd4: DecompressPointer r0
    //     0x13bfbd4: add             x0, x0, HEAP, lsl #32
    // 0x13bfbd8: LoadField: r1 = r0->field_f
    //     0x13bfbd8: ldur            w1, [x0, #0xf]
    // 0x13bfbdc: DecompressPointer r1
    //     0x13bfbdc: add             x1, x1, HEAP, lsl #32
    // 0x13bfbe0: LoadField: r0 = r1->field_13
    //     0x13bfbe0: ldur            w0, [x1, #0x13]
    // 0x13bfbe4: DecompressPointer r0
    //     0x13bfbe4: add             x0, x0, HEAP, lsl #32
    // 0x13bfbe8: r1 = LoadClassIdInstr(r0)
    //     0x13bfbe8: ldur            x1, [x0, #-1]
    //     0x13bfbec: ubfx            x1, x1, #0xc, #0x14
    // 0x13bfbf0: r16 = "size"
    //     0x13bfbf0: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0x13bfbf4: ldr             x16, [x16, #0x9c0]
    // 0x13bfbf8: stp             x16, x0, [SP]
    // 0x13bfbfc: mov             x0, x1
    // 0x13bfc00: mov             lr, x0
    // 0x13bfc04: ldr             lr, [x21, lr, lsl #3]
    // 0x13bfc08: blr             lr
    // 0x13bfc0c: tbnz            w0, #4, #0x13bfde8
    // 0x13bfc10: ldur            x0, [fp, #-8]
    // 0x13bfc14: r1 = Null
    //     0x13bfc14: mov             x1, NULL
    // 0x13bfc18: r2 = 8
    //     0x13bfc18: movz            x2, #0x8
    // 0x13bfc1c: r0 = AllocateArray()
    //     0x13bfc1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13bfc20: stur            x0, [fp, #-0x20]
    // 0x13bfc24: r16 = "Size: "
    //     0x13bfc24: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0x13bfc28: ldr             x16, [x16, #0xf00]
    // 0x13bfc2c: StoreField: r0->field_f = r16
    //     0x13bfc2c: stur            w16, [x0, #0xf]
    // 0x13bfc30: ldur            x2, [fp, #-8]
    // 0x13bfc34: LoadField: r1 = r2->field_f
    //     0x13bfc34: ldur            w1, [x2, #0xf]
    // 0x13bfc38: DecompressPointer r1
    //     0x13bfc38: add             x1, x1, HEAP, lsl #32
    // 0x13bfc3c: r0 = controller()
    //     0x13bfc3c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bfc40: LoadField: r1 = r0->field_53
    //     0x13bfc40: ldur            w1, [x0, #0x53]
    // 0x13bfc44: DecompressPointer r1
    //     0x13bfc44: add             x1, x1, HEAP, lsl #32
    // 0x13bfc48: r0 = value()
    //     0x13bfc48: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13bfc4c: LoadField: r2 = r0->field_5b
    //     0x13bfc4c: ldur            w2, [x0, #0x5b]
    // 0x13bfc50: DecompressPointer r2
    //     0x13bfc50: add             x2, x2, HEAP, lsl #32
    // 0x13bfc54: cmp             w2, NULL
    // 0x13bfc58: b.ne            #0x13bfc64
    // 0x13bfc5c: r0 = Null
    //     0x13bfc5c: mov             x0, NULL
    // 0x13bfc60: b               #0x13bfc94
    // 0x13bfc64: LoadField: r0 = r2->field_b
    //     0x13bfc64: ldur            w0, [x2, #0xb]
    // 0x13bfc68: r1 = LoadInt32Instr(r0)
    //     0x13bfc68: sbfx            x1, x0, #1, #0x1f
    // 0x13bfc6c: mov             x0, x1
    // 0x13bfc70: r1 = 0
    //     0x13bfc70: movz            x1, #0
    // 0x13bfc74: cmp             x1, x0
    // 0x13bfc78: b.hs            #0x13c14fc
    // 0x13bfc7c: LoadField: r0 = r2->field_f
    //     0x13bfc7c: ldur            w0, [x2, #0xf]
    // 0x13bfc80: DecompressPointer r0
    //     0x13bfc80: add             x0, x0, HEAP, lsl #32
    // 0x13bfc84: LoadField: r1 = r0->field_f
    //     0x13bfc84: ldur            w1, [x0, #0xf]
    // 0x13bfc88: DecompressPointer r1
    //     0x13bfc88: add             x1, x1, HEAP, lsl #32
    // 0x13bfc8c: LoadField: r0 = r1->field_33
    //     0x13bfc8c: ldur            w0, [x1, #0x33]
    // 0x13bfc90: DecompressPointer r0
    //     0x13bfc90: add             x0, x0, HEAP, lsl #32
    // 0x13bfc94: cmp             w0, NULL
    // 0x13bfc98: b.ne            #0x13bfca0
    // 0x13bfc9c: r0 = ""
    //     0x13bfc9c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13bfca0: ldur            x3, [fp, #-8]
    // 0x13bfca4: ldur            x2, [fp, #-0x20]
    // 0x13bfca8: mov             x1, x2
    // 0x13bfcac: ArrayStore: r1[1] = r0  ; List_4
    //     0x13bfcac: add             x25, x1, #0x13
    //     0x13bfcb0: str             w0, [x25]
    //     0x13bfcb4: tbz             w0, #0, #0x13bfcd0
    //     0x13bfcb8: ldurb           w16, [x1, #-1]
    //     0x13bfcbc: ldurb           w17, [x0, #-1]
    //     0x13bfcc0: and             x16, x17, x16, lsr #2
    //     0x13bfcc4: tst             x16, HEAP, lsr #32
    //     0x13bfcc8: b.eq            #0x13bfcd0
    //     0x13bfccc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13bfcd0: r16 = " / Qty: "
    //     0x13bfcd0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x13bfcd4: ldr             x16, [x16, #0x760]
    // 0x13bfcd8: ArrayStore: r2[0] = r16  ; List_4
    //     0x13bfcd8: stur            w16, [x2, #0x17]
    // 0x13bfcdc: LoadField: r1 = r3->field_f
    //     0x13bfcdc: ldur            w1, [x3, #0xf]
    // 0x13bfce0: DecompressPointer r1
    //     0x13bfce0: add             x1, x1, HEAP, lsl #32
    // 0x13bfce4: r0 = controller()
    //     0x13bfce4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bfce8: LoadField: r1 = r0->field_53
    //     0x13bfce8: ldur            w1, [x0, #0x53]
    // 0x13bfcec: DecompressPointer r1
    //     0x13bfcec: add             x1, x1, HEAP, lsl #32
    // 0x13bfcf0: r0 = value()
    //     0x13bfcf0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13bfcf4: LoadField: r2 = r0->field_5b
    //     0x13bfcf4: ldur            w2, [x0, #0x5b]
    // 0x13bfcf8: DecompressPointer r2
    //     0x13bfcf8: add             x2, x2, HEAP, lsl #32
    // 0x13bfcfc: cmp             w2, NULL
    // 0x13bfd00: b.ne            #0x13bfd0c
    // 0x13bfd04: r0 = Null
    //     0x13bfd04: mov             x0, NULL
    // 0x13bfd08: b               #0x13bfd3c
    // 0x13bfd0c: LoadField: r0 = r2->field_b
    //     0x13bfd0c: ldur            w0, [x2, #0xb]
    // 0x13bfd10: r1 = LoadInt32Instr(r0)
    //     0x13bfd10: sbfx            x1, x0, #1, #0x1f
    // 0x13bfd14: mov             x0, x1
    // 0x13bfd18: r1 = 0
    //     0x13bfd18: movz            x1, #0
    // 0x13bfd1c: cmp             x1, x0
    // 0x13bfd20: b.hs            #0x13c1500
    // 0x13bfd24: LoadField: r0 = r2->field_f
    //     0x13bfd24: ldur            w0, [x2, #0xf]
    // 0x13bfd28: DecompressPointer r0
    //     0x13bfd28: add             x0, x0, HEAP, lsl #32
    // 0x13bfd2c: LoadField: r1 = r0->field_f
    //     0x13bfd2c: ldur            w1, [x0, #0xf]
    // 0x13bfd30: DecompressPointer r1
    //     0x13bfd30: add             x1, x1, HEAP, lsl #32
    // 0x13bfd34: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x13bfd34: ldur            w0, [x1, #0x17]
    // 0x13bfd38: DecompressPointer r0
    //     0x13bfd38: add             x0, x0, HEAP, lsl #32
    // 0x13bfd3c: cmp             w0, NULL
    // 0x13bfd40: b.ne            #0x13bfd48
    // 0x13bfd44: r0 = ""
    //     0x13bfd44: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13bfd48: ldur            x2, [fp, #-8]
    // 0x13bfd4c: ldur            x1, [fp, #-0x20]
    // 0x13bfd50: ArrayStore: r1[3] = r0  ; List_4
    //     0x13bfd50: add             x25, x1, #0x1b
    //     0x13bfd54: str             w0, [x25]
    //     0x13bfd58: tbz             w0, #0, #0x13bfd74
    //     0x13bfd5c: ldurb           w16, [x1, #-1]
    //     0x13bfd60: ldurb           w17, [x0, #-1]
    //     0x13bfd64: and             x16, x17, x16, lsr #2
    //     0x13bfd68: tst             x16, HEAP, lsr #32
    //     0x13bfd6c: b.eq            #0x13bfd74
    //     0x13bfd70: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13bfd74: ldur            x16, [fp, #-0x20]
    // 0x13bfd78: str             x16, [SP]
    // 0x13bfd7c: r0 = _interpolate()
    //     0x13bfd7c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x13bfd80: ldur            x2, [fp, #-8]
    // 0x13bfd84: stur            x0, [fp, #-0x20]
    // 0x13bfd88: LoadField: r1 = r2->field_13
    //     0x13bfd88: ldur            w1, [x2, #0x13]
    // 0x13bfd8c: DecompressPointer r1
    //     0x13bfd8c: add             x1, x1, HEAP, lsl #32
    // 0x13bfd90: r0 = of()
    //     0x13bfd90: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13bfd94: LoadField: r1 = r0->field_87
    //     0x13bfd94: ldur            w1, [x0, #0x87]
    // 0x13bfd98: DecompressPointer r1
    //     0x13bfd98: add             x1, x1, HEAP, lsl #32
    // 0x13bfd9c: LoadField: r0 = r1->field_2b
    //     0x13bfd9c: ldur            w0, [x1, #0x2b]
    // 0x13bfda0: DecompressPointer r0
    //     0x13bfda0: add             x0, x0, HEAP, lsl #32
    // 0x13bfda4: r16 = 12.000000
    //     0x13bfda4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13bfda8: ldr             x16, [x16, #0x9e8]
    // 0x13bfdac: r30 = Instance_Color
    //     0x13bfdac: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13bfdb0: stp             lr, x16, [SP]
    // 0x13bfdb4: mov             x1, x0
    // 0x13bfdb8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13bfdb8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13bfdbc: ldr             x4, [x4, #0xaa0]
    // 0x13bfdc0: r0 = copyWith()
    //     0x13bfdc0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13bfdc4: stur            x0, [fp, #-0x28]
    // 0x13bfdc8: r0 = Text()
    //     0x13bfdc8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13bfdcc: mov             x1, x0
    // 0x13bfdd0: ldur            x0, [fp, #-0x20]
    // 0x13bfdd4: StoreField: r1->field_b = r0
    //     0x13bfdd4: stur            w0, [x1, #0xb]
    // 0x13bfdd8: ldur            x0, [fp, #-0x28]
    // 0x13bfddc: StoreField: r1->field_13 = r0
    //     0x13bfddc: stur            w0, [x1, #0x13]
    // 0x13bfde0: mov             x0, x1
    // 0x13bfde4: b               #0x13bffbc
    // 0x13bfde8: ldur            x0, [fp, #-8]
    // 0x13bfdec: r1 = Null
    //     0x13bfdec: mov             x1, NULL
    // 0x13bfdf0: r2 = 8
    //     0x13bfdf0: movz            x2, #0x8
    // 0x13bfdf4: r0 = AllocateArray()
    //     0x13bfdf4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13bfdf8: stur            x0, [fp, #-0x20]
    // 0x13bfdfc: r16 = "Variant: "
    //     0x13bfdfc: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0x13bfe00: ldr             x16, [x16, #0xf08]
    // 0x13bfe04: StoreField: r0->field_f = r16
    //     0x13bfe04: stur            w16, [x0, #0xf]
    // 0x13bfe08: ldur            x2, [fp, #-8]
    // 0x13bfe0c: LoadField: r1 = r2->field_f
    //     0x13bfe0c: ldur            w1, [x2, #0xf]
    // 0x13bfe10: DecompressPointer r1
    //     0x13bfe10: add             x1, x1, HEAP, lsl #32
    // 0x13bfe14: r0 = controller()
    //     0x13bfe14: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bfe18: LoadField: r1 = r0->field_53
    //     0x13bfe18: ldur            w1, [x0, #0x53]
    // 0x13bfe1c: DecompressPointer r1
    //     0x13bfe1c: add             x1, x1, HEAP, lsl #32
    // 0x13bfe20: r0 = value()
    //     0x13bfe20: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13bfe24: LoadField: r2 = r0->field_5b
    //     0x13bfe24: ldur            w2, [x0, #0x5b]
    // 0x13bfe28: DecompressPointer r2
    //     0x13bfe28: add             x2, x2, HEAP, lsl #32
    // 0x13bfe2c: cmp             w2, NULL
    // 0x13bfe30: b.ne            #0x13bfe3c
    // 0x13bfe34: r0 = Null
    //     0x13bfe34: mov             x0, NULL
    // 0x13bfe38: b               #0x13bfe6c
    // 0x13bfe3c: LoadField: r0 = r2->field_b
    //     0x13bfe3c: ldur            w0, [x2, #0xb]
    // 0x13bfe40: r1 = LoadInt32Instr(r0)
    //     0x13bfe40: sbfx            x1, x0, #1, #0x1f
    // 0x13bfe44: mov             x0, x1
    // 0x13bfe48: r1 = 0
    //     0x13bfe48: movz            x1, #0
    // 0x13bfe4c: cmp             x1, x0
    // 0x13bfe50: b.hs            #0x13c1504
    // 0x13bfe54: LoadField: r0 = r2->field_f
    //     0x13bfe54: ldur            w0, [x2, #0xf]
    // 0x13bfe58: DecompressPointer r0
    //     0x13bfe58: add             x0, x0, HEAP, lsl #32
    // 0x13bfe5c: LoadField: r1 = r0->field_f
    //     0x13bfe5c: ldur            w1, [x0, #0xf]
    // 0x13bfe60: DecompressPointer r1
    //     0x13bfe60: add             x1, x1, HEAP, lsl #32
    // 0x13bfe64: LoadField: r0 = r1->field_33
    //     0x13bfe64: ldur            w0, [x1, #0x33]
    // 0x13bfe68: DecompressPointer r0
    //     0x13bfe68: add             x0, x0, HEAP, lsl #32
    // 0x13bfe6c: cmp             w0, NULL
    // 0x13bfe70: b.ne            #0x13bfe78
    // 0x13bfe74: r0 = ""
    //     0x13bfe74: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13bfe78: ldur            x3, [fp, #-8]
    // 0x13bfe7c: ldur            x2, [fp, #-0x20]
    // 0x13bfe80: mov             x1, x2
    // 0x13bfe84: ArrayStore: r1[1] = r0  ; List_4
    //     0x13bfe84: add             x25, x1, #0x13
    //     0x13bfe88: str             w0, [x25]
    //     0x13bfe8c: tbz             w0, #0, #0x13bfea8
    //     0x13bfe90: ldurb           w16, [x1, #-1]
    //     0x13bfe94: ldurb           w17, [x0, #-1]
    //     0x13bfe98: and             x16, x17, x16, lsr #2
    //     0x13bfe9c: tst             x16, HEAP, lsr #32
    //     0x13bfea0: b.eq            #0x13bfea8
    //     0x13bfea4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13bfea8: r16 = " / Qty: "
    //     0x13bfea8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x13bfeac: ldr             x16, [x16, #0x760]
    // 0x13bfeb0: ArrayStore: r2[0] = r16  ; List_4
    //     0x13bfeb0: stur            w16, [x2, #0x17]
    // 0x13bfeb4: LoadField: r1 = r3->field_f
    //     0x13bfeb4: ldur            w1, [x3, #0xf]
    // 0x13bfeb8: DecompressPointer r1
    //     0x13bfeb8: add             x1, x1, HEAP, lsl #32
    // 0x13bfebc: r0 = controller()
    //     0x13bfebc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bfec0: LoadField: r1 = r0->field_53
    //     0x13bfec0: ldur            w1, [x0, #0x53]
    // 0x13bfec4: DecompressPointer r1
    //     0x13bfec4: add             x1, x1, HEAP, lsl #32
    // 0x13bfec8: r0 = value()
    //     0x13bfec8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13bfecc: LoadField: r2 = r0->field_5b
    //     0x13bfecc: ldur            w2, [x0, #0x5b]
    // 0x13bfed0: DecompressPointer r2
    //     0x13bfed0: add             x2, x2, HEAP, lsl #32
    // 0x13bfed4: cmp             w2, NULL
    // 0x13bfed8: b.ne            #0x13bfee4
    // 0x13bfedc: r0 = Null
    //     0x13bfedc: mov             x0, NULL
    // 0x13bfee0: b               #0x13bff14
    // 0x13bfee4: LoadField: r0 = r2->field_b
    //     0x13bfee4: ldur            w0, [x2, #0xb]
    // 0x13bfee8: r1 = LoadInt32Instr(r0)
    //     0x13bfee8: sbfx            x1, x0, #1, #0x1f
    // 0x13bfeec: mov             x0, x1
    // 0x13bfef0: r1 = 0
    //     0x13bfef0: movz            x1, #0
    // 0x13bfef4: cmp             x1, x0
    // 0x13bfef8: b.hs            #0x13c1508
    // 0x13bfefc: LoadField: r0 = r2->field_f
    //     0x13bfefc: ldur            w0, [x2, #0xf]
    // 0x13bff00: DecompressPointer r0
    //     0x13bff00: add             x0, x0, HEAP, lsl #32
    // 0x13bff04: LoadField: r1 = r0->field_f
    //     0x13bff04: ldur            w1, [x0, #0xf]
    // 0x13bff08: DecompressPointer r1
    //     0x13bff08: add             x1, x1, HEAP, lsl #32
    // 0x13bff0c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x13bff0c: ldur            w0, [x1, #0x17]
    // 0x13bff10: DecompressPointer r0
    //     0x13bff10: add             x0, x0, HEAP, lsl #32
    // 0x13bff14: cmp             w0, NULL
    // 0x13bff18: b.ne            #0x13bff20
    // 0x13bff1c: r0 = ""
    //     0x13bff1c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13bff20: ldur            x2, [fp, #-8]
    // 0x13bff24: ldur            x1, [fp, #-0x20]
    // 0x13bff28: ArrayStore: r1[3] = r0  ; List_4
    //     0x13bff28: add             x25, x1, #0x1b
    //     0x13bff2c: str             w0, [x25]
    //     0x13bff30: tbz             w0, #0, #0x13bff4c
    //     0x13bff34: ldurb           w16, [x1, #-1]
    //     0x13bff38: ldurb           w17, [x0, #-1]
    //     0x13bff3c: and             x16, x17, x16, lsr #2
    //     0x13bff40: tst             x16, HEAP, lsr #32
    //     0x13bff44: b.eq            #0x13bff4c
    //     0x13bff48: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13bff4c: ldur            x16, [fp, #-0x20]
    // 0x13bff50: str             x16, [SP]
    // 0x13bff54: r0 = _interpolate()
    //     0x13bff54: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x13bff58: ldur            x2, [fp, #-8]
    // 0x13bff5c: stur            x0, [fp, #-0x20]
    // 0x13bff60: LoadField: r1 = r2->field_13
    //     0x13bff60: ldur            w1, [x2, #0x13]
    // 0x13bff64: DecompressPointer r1
    //     0x13bff64: add             x1, x1, HEAP, lsl #32
    // 0x13bff68: r0 = of()
    //     0x13bff68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13bff6c: LoadField: r1 = r0->field_87
    //     0x13bff6c: ldur            w1, [x0, #0x87]
    // 0x13bff70: DecompressPointer r1
    //     0x13bff70: add             x1, x1, HEAP, lsl #32
    // 0x13bff74: LoadField: r0 = r1->field_2b
    //     0x13bff74: ldur            w0, [x1, #0x2b]
    // 0x13bff78: DecompressPointer r0
    //     0x13bff78: add             x0, x0, HEAP, lsl #32
    // 0x13bff7c: r16 = 12.000000
    //     0x13bff7c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13bff80: ldr             x16, [x16, #0x9e8]
    // 0x13bff84: r30 = Instance_Color
    //     0x13bff84: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13bff88: stp             lr, x16, [SP]
    // 0x13bff8c: mov             x1, x0
    // 0x13bff90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13bff90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13bff94: ldr             x4, [x4, #0xaa0]
    // 0x13bff98: r0 = copyWith()
    //     0x13bff98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13bff9c: stur            x0, [fp, #-0x28]
    // 0x13bffa0: r0 = Text()
    //     0x13bffa0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13bffa4: mov             x1, x0
    // 0x13bffa8: ldur            x0, [fp, #-0x20]
    // 0x13bffac: StoreField: r1->field_b = r0
    //     0x13bffac: stur            w0, [x1, #0xb]
    // 0x13bffb0: ldur            x0, [fp, #-0x28]
    // 0x13bffb4: StoreField: r1->field_13 = r0
    //     0x13bffb4: stur            w0, [x1, #0x13]
    // 0x13bffb8: mov             x0, x1
    // 0x13bffbc: ldur            x2, [fp, #-8]
    // 0x13bffc0: stur            x0, [fp, #-0x20]
    // 0x13bffc4: r0 = Padding()
    //     0x13bffc4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13bffc8: mov             x2, x0
    // 0x13bffcc: r0 = Instance_EdgeInsets
    //     0x13bffcc: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x13bffd0: ldr             x0, [x0, #0x770]
    // 0x13bffd4: stur            x2, [fp, #-0x28]
    // 0x13bffd8: StoreField: r2->field_f = r0
    //     0x13bffd8: stur            w0, [x2, #0xf]
    // 0x13bffdc: ldur            x1, [fp, #-0x20]
    // 0x13bffe0: StoreField: r2->field_b = r1
    //     0x13bffe0: stur            w1, [x2, #0xb]
    // 0x13bffe4: ldur            x3, [fp, #-8]
    // 0x13bffe8: LoadField: r1 = r3->field_f
    //     0x13bffe8: ldur            w1, [x3, #0xf]
    // 0x13bffec: DecompressPointer r1
    //     0x13bffec: add             x1, x1, HEAP, lsl #32
    // 0x13bfff0: r0 = controller()
    //     0x13bfff0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bfff4: LoadField: r1 = r0->field_53
    //     0x13bfff4: ldur            w1, [x0, #0x53]
    // 0x13bfff8: DecompressPointer r1
    //     0x13bfff8: add             x1, x1, HEAP, lsl #32
    // 0x13bfffc: r0 = value()
    //     0x13bfffc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c0000: LoadField: r2 = r0->field_5b
    //     0x13c0000: ldur            w2, [x0, #0x5b]
    // 0x13c0004: DecompressPointer r2
    //     0x13c0004: add             x2, x2, HEAP, lsl #32
    // 0x13c0008: cmp             w2, NULL
    // 0x13c000c: b.ne            #0x13c0018
    // 0x13c0010: r0 = Null
    //     0x13c0010: mov             x0, NULL
    // 0x13c0014: b               #0x13c0048
    // 0x13c0018: LoadField: r0 = r2->field_b
    //     0x13c0018: ldur            w0, [x2, #0xb]
    // 0x13c001c: r1 = LoadInt32Instr(r0)
    //     0x13c001c: sbfx            x1, x0, #1, #0x1f
    // 0x13c0020: mov             x0, x1
    // 0x13c0024: r1 = 0
    //     0x13c0024: movz            x1, #0
    // 0x13c0028: cmp             x1, x0
    // 0x13c002c: b.hs            #0x13c150c
    // 0x13c0030: LoadField: r0 = r2->field_f
    //     0x13c0030: ldur            w0, [x2, #0xf]
    // 0x13c0034: DecompressPointer r0
    //     0x13c0034: add             x0, x0, HEAP, lsl #32
    // 0x13c0038: LoadField: r1 = r0->field_f
    //     0x13c0038: ldur            w1, [x0, #0xf]
    // 0x13c003c: DecompressPointer r1
    //     0x13c003c: add             x1, x1, HEAP, lsl #32
    // 0x13c0040: LoadField: r0 = r1->field_2f
    //     0x13c0040: ldur            w0, [x1, #0x2f]
    // 0x13c0044: DecompressPointer r0
    //     0x13c0044: add             x0, x0, HEAP, lsl #32
    // 0x13c0048: cmp             w0, NULL
    // 0x13c004c: b.ne            #0x13c0058
    // 0x13c0050: r5 = ""
    //     0x13c0050: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c0054: b               #0x13c005c
    // 0x13c0058: mov             x5, x0
    // 0x13c005c: ldur            x2, [fp, #-8]
    // 0x13c0060: ldur            x4, [fp, #-0x10]
    // 0x13c0064: ldur            x3, [fp, #-0x30]
    // 0x13c0068: ldur            x0, [fp, #-0x28]
    // 0x13c006c: stur            x5, [fp, #-0x20]
    // 0x13c0070: LoadField: r1 = r2->field_13
    //     0x13c0070: ldur            w1, [x2, #0x13]
    // 0x13c0074: DecompressPointer r1
    //     0x13c0074: add             x1, x1, HEAP, lsl #32
    // 0x13c0078: r0 = of()
    //     0x13c0078: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c007c: LoadField: r1 = r0->field_87
    //     0x13c007c: ldur            w1, [x0, #0x87]
    // 0x13c0080: DecompressPointer r1
    //     0x13c0080: add             x1, x1, HEAP, lsl #32
    // 0x13c0084: LoadField: r0 = r1->field_7
    //     0x13c0084: ldur            w0, [x1, #7]
    // 0x13c0088: DecompressPointer r0
    //     0x13c0088: add             x0, x0, HEAP, lsl #32
    // 0x13c008c: r16 = Instance_Color
    //     0x13c008c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13c0090: r30 = 12.000000
    //     0x13c0090: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13c0094: ldr             lr, [lr, #0x9e8]
    // 0x13c0098: stp             lr, x16, [SP]
    // 0x13c009c: mov             x1, x0
    // 0x13c00a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13c00a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13c00a4: ldr             x4, [x4, #0x9b8]
    // 0x13c00a8: r0 = copyWith()
    //     0x13c00a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13c00ac: stur            x0, [fp, #-0x38]
    // 0x13c00b0: r0 = Text()
    //     0x13c00b0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13c00b4: mov             x3, x0
    // 0x13c00b8: ldur            x0, [fp, #-0x20]
    // 0x13c00bc: stur            x3, [fp, #-0x40]
    // 0x13c00c0: StoreField: r3->field_b = r0
    //     0x13c00c0: stur            w0, [x3, #0xb]
    // 0x13c00c4: ldur            x0, [fp, #-0x38]
    // 0x13c00c8: StoreField: r3->field_13 = r0
    //     0x13c00c8: stur            w0, [x3, #0x13]
    // 0x13c00cc: r1 = Null
    //     0x13c00cc: mov             x1, NULL
    // 0x13c00d0: r2 = 8
    //     0x13c00d0: movz            x2, #0x8
    // 0x13c00d4: r0 = AllocateArray()
    //     0x13c00d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c00d8: mov             x2, x0
    // 0x13c00dc: ldur            x0, [fp, #-0x30]
    // 0x13c00e0: stur            x2, [fp, #-0x20]
    // 0x13c00e4: StoreField: r2->field_f = r0
    //     0x13c00e4: stur            w0, [x2, #0xf]
    // 0x13c00e8: ldur            x0, [fp, #-0x28]
    // 0x13c00ec: StoreField: r2->field_13 = r0
    //     0x13c00ec: stur            w0, [x2, #0x13]
    // 0x13c00f0: r16 = Instance_SizedBox
    //     0x13c00f0: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x13c00f4: ldr             x16, [x16, #0xc70]
    // 0x13c00f8: ArrayStore: r2[0] = r16  ; List_4
    //     0x13c00f8: stur            w16, [x2, #0x17]
    // 0x13c00fc: ldur            x0, [fp, #-0x40]
    // 0x13c0100: StoreField: r2->field_1b = r0
    //     0x13c0100: stur            w0, [x2, #0x1b]
    // 0x13c0104: r1 = <Widget>
    //     0x13c0104: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13c0108: r0 = AllocateGrowableArray()
    //     0x13c0108: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13c010c: mov             x1, x0
    // 0x13c0110: ldur            x0, [fp, #-0x20]
    // 0x13c0114: stur            x1, [fp, #-0x28]
    // 0x13c0118: StoreField: r1->field_f = r0
    //     0x13c0118: stur            w0, [x1, #0xf]
    // 0x13c011c: r2 = 8
    //     0x13c011c: movz            x2, #0x8
    // 0x13c0120: StoreField: r1->field_b = r2
    //     0x13c0120: stur            w2, [x1, #0xb]
    // 0x13c0124: r0 = Column()
    //     0x13c0124: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13c0128: mov             x2, x0
    // 0x13c012c: r0 = Instance_Axis
    //     0x13c012c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13c0130: stur            x2, [fp, #-0x20]
    // 0x13c0134: StoreField: r2->field_f = r0
    //     0x13c0134: stur            w0, [x2, #0xf]
    // 0x13c0138: r3 = Instance_MainAxisAlignment
    //     0x13c0138: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13c013c: ldr             x3, [x3, #0xa08]
    // 0x13c0140: StoreField: r2->field_13 = r3
    //     0x13c0140: stur            w3, [x2, #0x13]
    // 0x13c0144: r4 = Instance_MainAxisSize
    //     0x13c0144: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13c0148: ldr             x4, [x4, #0xa10]
    // 0x13c014c: ArrayStore: r2[0] = r4  ; List_4
    //     0x13c014c: stur            w4, [x2, #0x17]
    // 0x13c0150: r5 = Instance_CrossAxisAlignment
    //     0x13c0150: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x13c0154: ldr             x5, [x5, #0x890]
    // 0x13c0158: StoreField: r2->field_1b = r5
    //     0x13c0158: stur            w5, [x2, #0x1b]
    // 0x13c015c: r6 = Instance_VerticalDirection
    //     0x13c015c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13c0160: ldr             x6, [x6, #0xa20]
    // 0x13c0164: StoreField: r2->field_23 = r6
    //     0x13c0164: stur            w6, [x2, #0x23]
    // 0x13c0168: r7 = Instance_Clip
    //     0x13c0168: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13c016c: ldr             x7, [x7, #0x38]
    // 0x13c0170: StoreField: r2->field_2b = r7
    //     0x13c0170: stur            w7, [x2, #0x2b]
    // 0x13c0174: StoreField: r2->field_2f = rZR
    //     0x13c0174: stur            xzr, [x2, #0x2f]
    // 0x13c0178: ldur            x1, [fp, #-0x28]
    // 0x13c017c: StoreField: r2->field_b = r1
    //     0x13c017c: stur            w1, [x2, #0xb]
    // 0x13c0180: r1 = <FlexParentData>
    //     0x13c0180: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x13c0184: ldr             x1, [x1, #0xe00]
    // 0x13c0188: r0 = Expanded()
    //     0x13c0188: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x13c018c: mov             x3, x0
    // 0x13c0190: r0 = 1
    //     0x13c0190: movz            x0, #0x1
    // 0x13c0194: stur            x3, [fp, #-0x28]
    // 0x13c0198: StoreField: r3->field_13 = r0
    //     0x13c0198: stur            x0, [x3, #0x13]
    // 0x13c019c: r4 = Instance_FlexFit
    //     0x13c019c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x13c01a0: ldr             x4, [x4, #0xe08]
    // 0x13c01a4: StoreField: r3->field_1b = r4
    //     0x13c01a4: stur            w4, [x3, #0x1b]
    // 0x13c01a8: ldur            x1, [fp, #-0x20]
    // 0x13c01ac: StoreField: r3->field_b = r1
    //     0x13c01ac: stur            w1, [x3, #0xb]
    // 0x13c01b0: r1 = Null
    //     0x13c01b0: mov             x1, NULL
    // 0x13c01b4: r2 = 6
    //     0x13c01b4: movz            x2, #0x6
    // 0x13c01b8: r0 = AllocateArray()
    //     0x13c01b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c01bc: mov             x2, x0
    // 0x13c01c0: ldur            x0, [fp, #-0x10]
    // 0x13c01c4: stur            x2, [fp, #-0x20]
    // 0x13c01c8: StoreField: r2->field_f = r0
    //     0x13c01c8: stur            w0, [x2, #0xf]
    // 0x13c01cc: r16 = Instance_SizedBox
    //     0x13c01cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x13c01d0: ldr             x16, [x16, #0xb20]
    // 0x13c01d4: StoreField: r2->field_13 = r16
    //     0x13c01d4: stur            w16, [x2, #0x13]
    // 0x13c01d8: ldur            x0, [fp, #-0x28]
    // 0x13c01dc: ArrayStore: r2[0] = r0  ; List_4
    //     0x13c01dc: stur            w0, [x2, #0x17]
    // 0x13c01e0: r1 = <Widget>
    //     0x13c01e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13c01e4: r0 = AllocateGrowableArray()
    //     0x13c01e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13c01e8: mov             x1, x0
    // 0x13c01ec: ldur            x0, [fp, #-0x20]
    // 0x13c01f0: stur            x1, [fp, #-0x10]
    // 0x13c01f4: StoreField: r1->field_f = r0
    //     0x13c01f4: stur            w0, [x1, #0xf]
    // 0x13c01f8: r2 = 6
    //     0x13c01f8: movz            x2, #0x6
    // 0x13c01fc: StoreField: r1->field_b = r2
    //     0x13c01fc: stur            w2, [x1, #0xb]
    // 0x13c0200: r0 = Row()
    //     0x13c0200: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13c0204: mov             x1, x0
    // 0x13c0208: r0 = Instance_Axis
    //     0x13c0208: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13c020c: stur            x1, [fp, #-0x20]
    // 0x13c0210: StoreField: r1->field_f = r0
    //     0x13c0210: stur            w0, [x1, #0xf]
    // 0x13c0214: r2 = Instance_MainAxisAlignment
    //     0x13c0214: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13c0218: ldr             x2, [x2, #0xa08]
    // 0x13c021c: StoreField: r1->field_13 = r2
    //     0x13c021c: stur            w2, [x1, #0x13]
    // 0x13c0220: r3 = Instance_MainAxisSize
    //     0x13c0220: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13c0224: ldr             x3, [x3, #0xa10]
    // 0x13c0228: ArrayStore: r1[0] = r3  ; List_4
    //     0x13c0228: stur            w3, [x1, #0x17]
    // 0x13c022c: r4 = Instance_CrossAxisAlignment
    //     0x13c022c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13c0230: ldr             x4, [x4, #0xa18]
    // 0x13c0234: StoreField: r1->field_1b = r4
    //     0x13c0234: stur            w4, [x1, #0x1b]
    // 0x13c0238: r5 = Instance_VerticalDirection
    //     0x13c0238: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13c023c: ldr             x5, [x5, #0xa20]
    // 0x13c0240: StoreField: r1->field_23 = r5
    //     0x13c0240: stur            w5, [x1, #0x23]
    // 0x13c0244: r6 = Instance_Clip
    //     0x13c0244: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13c0248: ldr             x6, [x6, #0x38]
    // 0x13c024c: StoreField: r1->field_2b = r6
    //     0x13c024c: stur            w6, [x1, #0x2b]
    // 0x13c0250: StoreField: r1->field_2f = rZR
    //     0x13c0250: stur            xzr, [x1, #0x2f]
    // 0x13c0254: ldur            x7, [fp, #-0x10]
    // 0x13c0258: StoreField: r1->field_b = r7
    //     0x13c0258: stur            w7, [x1, #0xb]
    // 0x13c025c: r0 = Padding()
    //     0x13c025c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13c0260: mov             x1, x0
    // 0x13c0264: r0 = Instance_EdgeInsets
    //     0x13c0264: add             x0, PP, #0x34, lsl #12  ; [pp+0x340b8] Obj!EdgeInsets@d57981
    //     0x13c0268: ldr             x0, [x0, #0xb8]
    // 0x13c026c: stur            x1, [fp, #-0x10]
    // 0x13c0270: StoreField: r1->field_f = r0
    //     0x13c0270: stur            w0, [x1, #0xf]
    // 0x13c0274: ldur            x0, [fp, #-0x20]
    // 0x13c0278: StoreField: r1->field_b = r0
    //     0x13c0278: stur            w0, [x1, #0xb]
    // 0x13c027c: r0 = Container()
    //     0x13c027c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13c0280: stur            x0, [fp, #-0x20]
    // 0x13c0284: r16 = Instance_BoxDecoration
    //     0x13c0284: add             x16, PP, #0x42, lsl #12  ; [pp+0x42e50] Obj!BoxDecoration@d64d41
    //     0x13c0288: ldr             x16, [x16, #0xe50]
    // 0x13c028c: ldur            lr, [fp, #-0x10]
    // 0x13c0290: stp             lr, x16, [SP]
    // 0x13c0294: mov             x1, x0
    // 0x13c0298: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x13c0298: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x13c029c: ldr             x4, [x4, #0x88]
    // 0x13c02a0: r0 = Container()
    //     0x13c02a0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13c02a4: r1 = Instance_Color
    //     0x13c02a4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13c02a8: d0 = 0.100000
    //     0x13c02a8: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x13c02ac: r0 = withOpacity()
    //     0x13c02ac: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13c02b0: mov             x2, x0
    // 0x13c02b4: r1 = Null
    //     0x13c02b4: mov             x1, NULL
    // 0x13c02b8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x13c02b8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x13c02bc: r0 = Border.all()
    //     0x13c02bc: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x13c02c0: stur            x0, [fp, #-0x10]
    // 0x13c02c4: r0 = BoxDecoration()
    //     0x13c02c4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x13c02c8: mov             x1, x0
    // 0x13c02cc: r0 = Instance_Color
    //     0x13c02cc: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13c02d0: stur            x1, [fp, #-0x28]
    // 0x13c02d4: StoreField: r1->field_7 = r0
    //     0x13c02d4: stur            w0, [x1, #7]
    // 0x13c02d8: ldur            x0, [fp, #-0x10]
    // 0x13c02dc: StoreField: r1->field_f = r0
    //     0x13c02dc: stur            w0, [x1, #0xf]
    // 0x13c02e0: r0 = Instance_BorderRadius
    //     0x13c02e0: add             x0, PP, #0x43, lsl #12  ; [pp+0x43790] Obj!BorderRadius@d5a2a1
    //     0x13c02e4: ldr             x0, [x0, #0x790]
    // 0x13c02e8: StoreField: r1->field_13 = r0
    //     0x13c02e8: stur            w0, [x1, #0x13]
    // 0x13c02ec: r0 = Instance_BoxShape
    //     0x13c02ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13c02f0: ldr             x0, [x0, #0x80]
    // 0x13c02f4: StoreField: r1->field_23 = r0
    //     0x13c02f4: stur            w0, [x1, #0x23]
    // 0x13c02f8: r0 = Radius()
    //     0x13c02f8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x13c02fc: d0 = 12.000000
    //     0x13c02fc: fmov            d0, #12.00000000
    // 0x13c0300: stur            x0, [fp, #-0x10]
    // 0x13c0304: StoreField: r0->field_7 = d0
    //     0x13c0304: stur            d0, [x0, #7]
    // 0x13c0308: StoreField: r0->field_f = d0
    //     0x13c0308: stur            d0, [x0, #0xf]
    // 0x13c030c: r0 = BorderRadius()
    //     0x13c030c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x13c0310: mov             x2, x0
    // 0x13c0314: ldur            x0, [fp, #-0x10]
    // 0x13c0318: stur            x2, [fp, #-0x30]
    // 0x13c031c: StoreField: r2->field_7 = r0
    //     0x13c031c: stur            w0, [x2, #7]
    // 0x13c0320: StoreField: r2->field_b = r0
    //     0x13c0320: stur            w0, [x2, #0xb]
    // 0x13c0324: StoreField: r2->field_f = r0
    //     0x13c0324: stur            w0, [x2, #0xf]
    // 0x13c0328: StoreField: r2->field_13 = r0
    //     0x13c0328: stur            w0, [x2, #0x13]
    // 0x13c032c: ldur            x0, [fp, #-8]
    // 0x13c0330: LoadField: r1 = r0->field_f
    //     0x13c0330: ldur            w1, [x0, #0xf]
    // 0x13c0334: DecompressPointer r1
    //     0x13c0334: add             x1, x1, HEAP, lsl #32
    // 0x13c0338: r0 = controller()
    //     0x13c0338: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c033c: LoadField: r1 = r0->field_53
    //     0x13c033c: ldur            w1, [x0, #0x53]
    // 0x13c0340: DecompressPointer r1
    //     0x13c0340: add             x1, x1, HEAP, lsl #32
    // 0x13c0344: r0 = value()
    //     0x13c0344: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c0348: LoadField: r2 = r0->field_1b
    //     0x13c0348: ldur            w2, [x0, #0x1b]
    // 0x13c034c: DecompressPointer r2
    //     0x13c034c: add             x2, x2, HEAP, lsl #32
    // 0x13c0350: cmp             w2, NULL
    // 0x13c0354: b.ne            #0x13c0360
    // 0x13c0358: r0 = Null
    //     0x13c0358: mov             x0, NULL
    // 0x13c035c: b               #0x13c0390
    // 0x13c0360: LoadField: r0 = r2->field_b
    //     0x13c0360: ldur            w0, [x2, #0xb]
    // 0x13c0364: r1 = LoadInt32Instr(r0)
    //     0x13c0364: sbfx            x1, x0, #1, #0x1f
    // 0x13c0368: mov             x0, x1
    // 0x13c036c: r1 = 0
    //     0x13c036c: movz            x1, #0
    // 0x13c0370: cmp             x1, x0
    // 0x13c0374: b.hs            #0x13c1510
    // 0x13c0378: LoadField: r0 = r2->field_f
    //     0x13c0378: ldur            w0, [x2, #0xf]
    // 0x13c037c: DecompressPointer r0
    //     0x13c037c: add             x0, x0, HEAP, lsl #32
    // 0x13c0380: LoadField: r1 = r0->field_f
    //     0x13c0380: ldur            w1, [x0, #0xf]
    // 0x13c0384: DecompressPointer r1
    //     0x13c0384: add             x1, x1, HEAP, lsl #32
    // 0x13c0388: LoadField: r0 = r1->field_13
    //     0x13c0388: ldur            w0, [x1, #0x13]
    // 0x13c038c: DecompressPointer r0
    //     0x13c038c: add             x0, x0, HEAP, lsl #32
    // 0x13c0390: cmp             w0, NULL
    // 0x13c0394: b.ne            #0x13c03a0
    // 0x13c0398: r4 = ""
    //     0x13c0398: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c039c: b               #0x13c03a4
    // 0x13c03a0: mov             x4, x0
    // 0x13c03a4: ldur            x3, [fp, #-8]
    // 0x13c03a8: ldur            x0, [fp, #-0x30]
    // 0x13c03ac: stur            x4, [fp, #-0x10]
    // 0x13c03b0: r1 = Function '<anonymous closure>':.
    //     0x13c03b0: add             x1, PP, #0x46, lsl #12  ; [pp+0x466a8] AnonymousClosure: (0x9d9cb4), in [package:customer_app/app/presentation/views/line/post_order/order_detail/order_detail_view.dart] OrderDetailView::body (0x1506b64)
    //     0x13c03b4: ldr             x1, [x1, #0x6a8]
    // 0x13c03b8: r2 = Null
    //     0x13c03b8: mov             x2, NULL
    // 0x13c03bc: r0 = AllocateClosure()
    //     0x13c03bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13c03c0: stur            x0, [fp, #-0x38]
    // 0x13c03c4: r0 = CachedNetworkImage()
    //     0x13c03c4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x13c03c8: stur            x0, [fp, #-0x40]
    // 0x13c03cc: r16 = Instance_BoxFit
    //     0x13c03cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x13c03d0: ldr             x16, [x16, #0x118]
    // 0x13c03d4: r30 = 60.000000
    //     0x13c03d4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x13c03d8: ldr             lr, [lr, #0x110]
    // 0x13c03dc: stp             lr, x16, [SP, #0x10]
    // 0x13c03e0: r16 = 60.000000
    //     0x13c03e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x13c03e4: ldr             x16, [x16, #0x110]
    // 0x13c03e8: ldur            lr, [fp, #-0x38]
    // 0x13c03ec: stp             lr, x16, [SP]
    // 0x13c03f0: mov             x1, x0
    // 0x13c03f4: ldur            x2, [fp, #-0x10]
    // 0x13c03f8: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x13c03f8: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fbf8] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x13c03fc: ldr             x4, [x4, #0xbf8]
    // 0x13c0400: r0 = CachedNetworkImage()
    //     0x13c0400: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x13c0404: r0 = ClipRRect()
    //     0x13c0404: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x13c0408: mov             x2, x0
    // 0x13c040c: ldur            x0, [fp, #-0x30]
    // 0x13c0410: stur            x2, [fp, #-0x10]
    // 0x13c0414: StoreField: r2->field_f = r0
    //     0x13c0414: stur            w0, [x2, #0xf]
    // 0x13c0418: r0 = Instance_Clip
    //     0x13c0418: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x13c041c: ldr             x0, [x0, #0x138]
    // 0x13c0420: ArrayStore: r2[0] = r0  ; List_4
    //     0x13c0420: stur            w0, [x2, #0x17]
    // 0x13c0424: ldur            x0, [fp, #-0x40]
    // 0x13c0428: StoreField: r2->field_b = r0
    //     0x13c0428: stur            w0, [x2, #0xb]
    // 0x13c042c: ldur            x0, [fp, #-8]
    // 0x13c0430: LoadField: r1 = r0->field_f
    //     0x13c0430: ldur            w1, [x0, #0xf]
    // 0x13c0434: DecompressPointer r1
    //     0x13c0434: add             x1, x1, HEAP, lsl #32
    // 0x13c0438: r0 = controller()
    //     0x13c0438: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c043c: LoadField: r1 = r0->field_53
    //     0x13c043c: ldur            w1, [x0, #0x53]
    // 0x13c0440: DecompressPointer r1
    //     0x13c0440: add             x1, x1, HEAP, lsl #32
    // 0x13c0444: r0 = value()
    //     0x13c0444: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c0448: LoadField: r2 = r0->field_1b
    //     0x13c0448: ldur            w2, [x0, #0x1b]
    // 0x13c044c: DecompressPointer r2
    //     0x13c044c: add             x2, x2, HEAP, lsl #32
    // 0x13c0450: cmp             w2, NULL
    // 0x13c0454: b.ne            #0x13c0460
    // 0x13c0458: r0 = Null
    //     0x13c0458: mov             x0, NULL
    // 0x13c045c: b               #0x13c0490
    // 0x13c0460: LoadField: r0 = r2->field_b
    //     0x13c0460: ldur            w0, [x2, #0xb]
    // 0x13c0464: r1 = LoadInt32Instr(r0)
    //     0x13c0464: sbfx            x1, x0, #1, #0x1f
    // 0x13c0468: mov             x0, x1
    // 0x13c046c: r1 = 0
    //     0x13c046c: movz            x1, #0
    // 0x13c0470: cmp             x1, x0
    // 0x13c0474: b.hs            #0x13c1514
    // 0x13c0478: LoadField: r0 = r2->field_f
    //     0x13c0478: ldur            w0, [x2, #0xf]
    // 0x13c047c: DecompressPointer r0
    //     0x13c047c: add             x0, x0, HEAP, lsl #32
    // 0x13c0480: LoadField: r1 = r0->field_f
    //     0x13c0480: ldur            w1, [x0, #0xf]
    // 0x13c0484: DecompressPointer r1
    //     0x13c0484: add             x1, x1, HEAP, lsl #32
    // 0x13c0488: LoadField: r0 = r1->field_f
    //     0x13c0488: ldur            w0, [x1, #0xf]
    // 0x13c048c: DecompressPointer r0
    //     0x13c048c: add             x0, x0, HEAP, lsl #32
    // 0x13c0490: cmp             w0, NULL
    // 0x13c0494: b.ne            #0x13c049c
    // 0x13c0498: r0 = ""
    //     0x13c0498: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c049c: ldur            x2, [fp, #-8]
    // 0x13c04a0: stur            x0, [fp, #-0x30]
    // 0x13c04a4: LoadField: r1 = r2->field_13
    //     0x13c04a4: ldur            w1, [x2, #0x13]
    // 0x13c04a8: DecompressPointer r1
    //     0x13c04a8: add             x1, x1, HEAP, lsl #32
    // 0x13c04ac: r0 = of()
    //     0x13c04ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c04b0: LoadField: r1 = r0->field_87
    //     0x13c04b0: ldur            w1, [x0, #0x87]
    // 0x13c04b4: DecompressPointer r1
    //     0x13c04b4: add             x1, x1, HEAP, lsl #32
    // 0x13c04b8: LoadField: r0 = r1->field_7
    //     0x13c04b8: ldur            w0, [x1, #7]
    // 0x13c04bc: DecompressPointer r0
    //     0x13c04bc: add             x0, x0, HEAP, lsl #32
    // 0x13c04c0: r16 = Instance_Color
    //     0x13c04c0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13c04c4: r30 = 12.000000
    //     0x13c04c4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13c04c8: ldr             lr, [lr, #0x9e8]
    // 0x13c04cc: stp             lr, x16, [SP]
    // 0x13c04d0: mov             x1, x0
    // 0x13c04d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13c04d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13c04d8: ldr             x4, [x4, #0x9b8]
    // 0x13c04dc: r0 = copyWith()
    //     0x13c04dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13c04e0: stur            x0, [fp, #-0x38]
    // 0x13c04e4: r0 = Text()
    //     0x13c04e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13c04e8: mov             x2, x0
    // 0x13c04ec: ldur            x0, [fp, #-0x30]
    // 0x13c04f0: stur            x2, [fp, #-0x40]
    // 0x13c04f4: StoreField: r2->field_b = r0
    //     0x13c04f4: stur            w0, [x2, #0xb]
    // 0x13c04f8: ldur            x0, [fp, #-0x38]
    // 0x13c04fc: StoreField: r2->field_13 = r0
    //     0x13c04fc: stur            w0, [x2, #0x13]
    // 0x13c0500: r0 = 2
    //     0x13c0500: movz            x0, #0x2
    // 0x13c0504: StoreField: r2->field_37 = r0
    //     0x13c0504: stur            w0, [x2, #0x37]
    // 0x13c0508: ldur            x0, [fp, #-8]
    // 0x13c050c: LoadField: r1 = r0->field_f
    //     0x13c050c: ldur            w1, [x0, #0xf]
    // 0x13c0510: DecompressPointer r1
    //     0x13c0510: add             x1, x1, HEAP, lsl #32
    // 0x13c0514: r0 = controller()
    //     0x13c0514: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c0518: LoadField: r1 = r0->field_53
    //     0x13c0518: ldur            w1, [x0, #0x53]
    // 0x13c051c: DecompressPointer r1
    //     0x13c051c: add             x1, x1, HEAP, lsl #32
    // 0x13c0520: r0 = value()
    //     0x13c0520: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c0524: LoadField: r2 = r0->field_1b
    //     0x13c0524: ldur            w2, [x0, #0x1b]
    // 0x13c0528: DecompressPointer r2
    //     0x13c0528: add             x2, x2, HEAP, lsl #32
    // 0x13c052c: cmp             w2, NULL
    // 0x13c0530: b.ne            #0x13c053c
    // 0x13c0534: r0 = Null
    //     0x13c0534: mov             x0, NULL
    // 0x13c0538: b               #0x13c056c
    // 0x13c053c: LoadField: r0 = r2->field_b
    //     0x13c053c: ldur            w0, [x2, #0xb]
    // 0x13c0540: r1 = LoadInt32Instr(r0)
    //     0x13c0540: sbfx            x1, x0, #1, #0x1f
    // 0x13c0544: mov             x0, x1
    // 0x13c0548: r1 = 0
    //     0x13c0548: movz            x1, #0
    // 0x13c054c: cmp             x1, x0
    // 0x13c0550: b.hs            #0x13c1518
    // 0x13c0554: LoadField: r0 = r2->field_f
    //     0x13c0554: ldur            w0, [x2, #0xf]
    // 0x13c0558: DecompressPointer r0
    //     0x13c0558: add             x0, x0, HEAP, lsl #32
    // 0x13c055c: LoadField: r1 = r0->field_f
    //     0x13c055c: ldur            w1, [x0, #0xf]
    // 0x13c0560: DecompressPointer r1
    //     0x13c0560: add             x1, x1, HEAP, lsl #32
    // 0x13c0564: LoadField: r0 = r1->field_13
    //     0x13c0564: ldur            w0, [x1, #0x13]
    // 0x13c0568: DecompressPointer r0
    //     0x13c0568: add             x0, x0, HEAP, lsl #32
    // 0x13c056c: r1 = LoadClassIdInstr(r0)
    //     0x13c056c: ldur            x1, [x0, #-1]
    //     0x13c0570: ubfx            x1, x1, #0xc, #0x14
    // 0x13c0574: r16 = "size"
    //     0x13c0574: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0x13c0578: ldr             x16, [x16, #0x9c0]
    // 0x13c057c: stp             x16, x0, [SP]
    // 0x13c0580: mov             x0, x1
    // 0x13c0584: mov             lr, x0
    // 0x13c0588: ldr             lr, [x21, lr, lsl #3]
    // 0x13c058c: blr             lr
    // 0x13c0590: tbnz            w0, #4, #0x13c076c
    // 0x13c0594: ldur            x0, [fp, #-8]
    // 0x13c0598: r1 = Null
    //     0x13c0598: mov             x1, NULL
    // 0x13c059c: r2 = 8
    //     0x13c059c: movz            x2, #0x8
    // 0x13c05a0: r0 = AllocateArray()
    //     0x13c05a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c05a4: stur            x0, [fp, #-0x30]
    // 0x13c05a8: r16 = "Size: "
    //     0x13c05a8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0x13c05ac: ldr             x16, [x16, #0xf00]
    // 0x13c05b0: StoreField: r0->field_f = r16
    //     0x13c05b0: stur            w16, [x0, #0xf]
    // 0x13c05b4: ldur            x2, [fp, #-8]
    // 0x13c05b8: LoadField: r1 = r2->field_f
    //     0x13c05b8: ldur            w1, [x2, #0xf]
    // 0x13c05bc: DecompressPointer r1
    //     0x13c05bc: add             x1, x1, HEAP, lsl #32
    // 0x13c05c0: r0 = controller()
    //     0x13c05c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c05c4: LoadField: r1 = r0->field_53
    //     0x13c05c4: ldur            w1, [x0, #0x53]
    // 0x13c05c8: DecompressPointer r1
    //     0x13c05c8: add             x1, x1, HEAP, lsl #32
    // 0x13c05cc: r0 = value()
    //     0x13c05cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c05d0: LoadField: r2 = r0->field_1b
    //     0x13c05d0: ldur            w2, [x0, #0x1b]
    // 0x13c05d4: DecompressPointer r2
    //     0x13c05d4: add             x2, x2, HEAP, lsl #32
    // 0x13c05d8: cmp             w2, NULL
    // 0x13c05dc: b.ne            #0x13c05e8
    // 0x13c05e0: r0 = Null
    //     0x13c05e0: mov             x0, NULL
    // 0x13c05e4: b               #0x13c0618
    // 0x13c05e8: LoadField: r0 = r2->field_b
    //     0x13c05e8: ldur            w0, [x2, #0xb]
    // 0x13c05ec: r1 = LoadInt32Instr(r0)
    //     0x13c05ec: sbfx            x1, x0, #1, #0x1f
    // 0x13c05f0: mov             x0, x1
    // 0x13c05f4: r1 = 0
    //     0x13c05f4: movz            x1, #0
    // 0x13c05f8: cmp             x1, x0
    // 0x13c05fc: b.hs            #0x13c151c
    // 0x13c0600: LoadField: r0 = r2->field_f
    //     0x13c0600: ldur            w0, [x2, #0xf]
    // 0x13c0604: DecompressPointer r0
    //     0x13c0604: add             x0, x0, HEAP, lsl #32
    // 0x13c0608: LoadField: r1 = r0->field_f
    //     0x13c0608: ldur            w1, [x0, #0xf]
    // 0x13c060c: DecompressPointer r1
    //     0x13c060c: add             x1, x1, HEAP, lsl #32
    // 0x13c0610: LoadField: r0 = r1->field_33
    //     0x13c0610: ldur            w0, [x1, #0x33]
    // 0x13c0614: DecompressPointer r0
    //     0x13c0614: add             x0, x0, HEAP, lsl #32
    // 0x13c0618: cmp             w0, NULL
    // 0x13c061c: b.ne            #0x13c0624
    // 0x13c0620: r0 = ""
    //     0x13c0620: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c0624: ldur            x3, [fp, #-8]
    // 0x13c0628: ldur            x2, [fp, #-0x30]
    // 0x13c062c: mov             x1, x2
    // 0x13c0630: ArrayStore: r1[1] = r0  ; List_4
    //     0x13c0630: add             x25, x1, #0x13
    //     0x13c0634: str             w0, [x25]
    //     0x13c0638: tbz             w0, #0, #0x13c0654
    //     0x13c063c: ldurb           w16, [x1, #-1]
    //     0x13c0640: ldurb           w17, [x0, #-1]
    //     0x13c0644: and             x16, x17, x16, lsr #2
    //     0x13c0648: tst             x16, HEAP, lsr #32
    //     0x13c064c: b.eq            #0x13c0654
    //     0x13c0650: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13c0654: r16 = " / Qty: "
    //     0x13c0654: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x13c0658: ldr             x16, [x16, #0x760]
    // 0x13c065c: ArrayStore: r2[0] = r16  ; List_4
    //     0x13c065c: stur            w16, [x2, #0x17]
    // 0x13c0660: LoadField: r1 = r3->field_f
    //     0x13c0660: ldur            w1, [x3, #0xf]
    // 0x13c0664: DecompressPointer r1
    //     0x13c0664: add             x1, x1, HEAP, lsl #32
    // 0x13c0668: r0 = controller()
    //     0x13c0668: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c066c: LoadField: r1 = r0->field_53
    //     0x13c066c: ldur            w1, [x0, #0x53]
    // 0x13c0670: DecompressPointer r1
    //     0x13c0670: add             x1, x1, HEAP, lsl #32
    // 0x13c0674: r0 = value()
    //     0x13c0674: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c0678: LoadField: r2 = r0->field_1b
    //     0x13c0678: ldur            w2, [x0, #0x1b]
    // 0x13c067c: DecompressPointer r2
    //     0x13c067c: add             x2, x2, HEAP, lsl #32
    // 0x13c0680: cmp             w2, NULL
    // 0x13c0684: b.ne            #0x13c0690
    // 0x13c0688: r0 = Null
    //     0x13c0688: mov             x0, NULL
    // 0x13c068c: b               #0x13c06c0
    // 0x13c0690: LoadField: r0 = r2->field_b
    //     0x13c0690: ldur            w0, [x2, #0xb]
    // 0x13c0694: r1 = LoadInt32Instr(r0)
    //     0x13c0694: sbfx            x1, x0, #1, #0x1f
    // 0x13c0698: mov             x0, x1
    // 0x13c069c: r1 = 0
    //     0x13c069c: movz            x1, #0
    // 0x13c06a0: cmp             x1, x0
    // 0x13c06a4: b.hs            #0x13c1520
    // 0x13c06a8: LoadField: r0 = r2->field_f
    //     0x13c06a8: ldur            w0, [x2, #0xf]
    // 0x13c06ac: DecompressPointer r0
    //     0x13c06ac: add             x0, x0, HEAP, lsl #32
    // 0x13c06b0: LoadField: r1 = r0->field_f
    //     0x13c06b0: ldur            w1, [x0, #0xf]
    // 0x13c06b4: DecompressPointer r1
    //     0x13c06b4: add             x1, x1, HEAP, lsl #32
    // 0x13c06b8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x13c06b8: ldur            w0, [x1, #0x17]
    // 0x13c06bc: DecompressPointer r0
    //     0x13c06bc: add             x0, x0, HEAP, lsl #32
    // 0x13c06c0: cmp             w0, NULL
    // 0x13c06c4: b.ne            #0x13c06cc
    // 0x13c06c8: r0 = ""
    //     0x13c06c8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c06cc: ldur            x2, [fp, #-8]
    // 0x13c06d0: ldur            x1, [fp, #-0x30]
    // 0x13c06d4: ArrayStore: r1[3] = r0  ; List_4
    //     0x13c06d4: add             x25, x1, #0x1b
    //     0x13c06d8: str             w0, [x25]
    //     0x13c06dc: tbz             w0, #0, #0x13c06f8
    //     0x13c06e0: ldurb           w16, [x1, #-1]
    //     0x13c06e4: ldurb           w17, [x0, #-1]
    //     0x13c06e8: and             x16, x17, x16, lsr #2
    //     0x13c06ec: tst             x16, HEAP, lsr #32
    //     0x13c06f0: b.eq            #0x13c06f8
    //     0x13c06f4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13c06f8: ldur            x16, [fp, #-0x30]
    // 0x13c06fc: str             x16, [SP]
    // 0x13c0700: r0 = _interpolate()
    //     0x13c0700: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x13c0704: ldur            x2, [fp, #-8]
    // 0x13c0708: stur            x0, [fp, #-0x30]
    // 0x13c070c: LoadField: r1 = r2->field_13
    //     0x13c070c: ldur            w1, [x2, #0x13]
    // 0x13c0710: DecompressPointer r1
    //     0x13c0710: add             x1, x1, HEAP, lsl #32
    // 0x13c0714: r0 = of()
    //     0x13c0714: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c0718: LoadField: r1 = r0->field_87
    //     0x13c0718: ldur            w1, [x0, #0x87]
    // 0x13c071c: DecompressPointer r1
    //     0x13c071c: add             x1, x1, HEAP, lsl #32
    // 0x13c0720: LoadField: r0 = r1->field_2b
    //     0x13c0720: ldur            w0, [x1, #0x2b]
    // 0x13c0724: DecompressPointer r0
    //     0x13c0724: add             x0, x0, HEAP, lsl #32
    // 0x13c0728: r16 = 12.000000
    //     0x13c0728: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13c072c: ldr             x16, [x16, #0x9e8]
    // 0x13c0730: r30 = Instance_Color
    //     0x13c0730: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13c0734: stp             lr, x16, [SP]
    // 0x13c0738: mov             x1, x0
    // 0x13c073c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13c073c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13c0740: ldr             x4, [x4, #0xaa0]
    // 0x13c0744: r0 = copyWith()
    //     0x13c0744: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13c0748: stur            x0, [fp, #-0x38]
    // 0x13c074c: r0 = Text()
    //     0x13c074c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13c0750: mov             x1, x0
    // 0x13c0754: ldur            x0, [fp, #-0x30]
    // 0x13c0758: StoreField: r1->field_b = r0
    //     0x13c0758: stur            w0, [x1, #0xb]
    // 0x13c075c: ldur            x0, [fp, #-0x38]
    // 0x13c0760: StoreField: r1->field_13 = r0
    //     0x13c0760: stur            w0, [x1, #0x13]
    // 0x13c0764: mov             x0, x1
    // 0x13c0768: b               #0x13c0940
    // 0x13c076c: ldur            x0, [fp, #-8]
    // 0x13c0770: r1 = Null
    //     0x13c0770: mov             x1, NULL
    // 0x13c0774: r2 = 8
    //     0x13c0774: movz            x2, #0x8
    // 0x13c0778: r0 = AllocateArray()
    //     0x13c0778: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c077c: stur            x0, [fp, #-0x30]
    // 0x13c0780: r16 = "Variant: "
    //     0x13c0780: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0x13c0784: ldr             x16, [x16, #0xf08]
    // 0x13c0788: StoreField: r0->field_f = r16
    //     0x13c0788: stur            w16, [x0, #0xf]
    // 0x13c078c: ldur            x2, [fp, #-8]
    // 0x13c0790: LoadField: r1 = r2->field_f
    //     0x13c0790: ldur            w1, [x2, #0xf]
    // 0x13c0794: DecompressPointer r1
    //     0x13c0794: add             x1, x1, HEAP, lsl #32
    // 0x13c0798: r0 = controller()
    //     0x13c0798: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c079c: LoadField: r1 = r0->field_53
    //     0x13c079c: ldur            w1, [x0, #0x53]
    // 0x13c07a0: DecompressPointer r1
    //     0x13c07a0: add             x1, x1, HEAP, lsl #32
    // 0x13c07a4: r0 = value()
    //     0x13c07a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c07a8: LoadField: r2 = r0->field_1b
    //     0x13c07a8: ldur            w2, [x0, #0x1b]
    // 0x13c07ac: DecompressPointer r2
    //     0x13c07ac: add             x2, x2, HEAP, lsl #32
    // 0x13c07b0: cmp             w2, NULL
    // 0x13c07b4: b.ne            #0x13c07c0
    // 0x13c07b8: r0 = Null
    //     0x13c07b8: mov             x0, NULL
    // 0x13c07bc: b               #0x13c07f0
    // 0x13c07c0: LoadField: r0 = r2->field_b
    //     0x13c07c0: ldur            w0, [x2, #0xb]
    // 0x13c07c4: r1 = LoadInt32Instr(r0)
    //     0x13c07c4: sbfx            x1, x0, #1, #0x1f
    // 0x13c07c8: mov             x0, x1
    // 0x13c07cc: r1 = 0
    //     0x13c07cc: movz            x1, #0
    // 0x13c07d0: cmp             x1, x0
    // 0x13c07d4: b.hs            #0x13c1524
    // 0x13c07d8: LoadField: r0 = r2->field_f
    //     0x13c07d8: ldur            w0, [x2, #0xf]
    // 0x13c07dc: DecompressPointer r0
    //     0x13c07dc: add             x0, x0, HEAP, lsl #32
    // 0x13c07e0: LoadField: r1 = r0->field_f
    //     0x13c07e0: ldur            w1, [x0, #0xf]
    // 0x13c07e4: DecompressPointer r1
    //     0x13c07e4: add             x1, x1, HEAP, lsl #32
    // 0x13c07e8: LoadField: r0 = r1->field_33
    //     0x13c07e8: ldur            w0, [x1, #0x33]
    // 0x13c07ec: DecompressPointer r0
    //     0x13c07ec: add             x0, x0, HEAP, lsl #32
    // 0x13c07f0: cmp             w0, NULL
    // 0x13c07f4: b.ne            #0x13c07fc
    // 0x13c07f8: r0 = ""
    //     0x13c07f8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c07fc: ldur            x3, [fp, #-8]
    // 0x13c0800: ldur            x2, [fp, #-0x30]
    // 0x13c0804: mov             x1, x2
    // 0x13c0808: ArrayStore: r1[1] = r0  ; List_4
    //     0x13c0808: add             x25, x1, #0x13
    //     0x13c080c: str             w0, [x25]
    //     0x13c0810: tbz             w0, #0, #0x13c082c
    //     0x13c0814: ldurb           w16, [x1, #-1]
    //     0x13c0818: ldurb           w17, [x0, #-1]
    //     0x13c081c: and             x16, x17, x16, lsr #2
    //     0x13c0820: tst             x16, HEAP, lsr #32
    //     0x13c0824: b.eq            #0x13c082c
    //     0x13c0828: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13c082c: r16 = " / Qty: "
    //     0x13c082c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x13c0830: ldr             x16, [x16, #0x760]
    // 0x13c0834: ArrayStore: r2[0] = r16  ; List_4
    //     0x13c0834: stur            w16, [x2, #0x17]
    // 0x13c0838: LoadField: r1 = r3->field_f
    //     0x13c0838: ldur            w1, [x3, #0xf]
    // 0x13c083c: DecompressPointer r1
    //     0x13c083c: add             x1, x1, HEAP, lsl #32
    // 0x13c0840: r0 = controller()
    //     0x13c0840: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c0844: LoadField: r1 = r0->field_53
    //     0x13c0844: ldur            w1, [x0, #0x53]
    // 0x13c0848: DecompressPointer r1
    //     0x13c0848: add             x1, x1, HEAP, lsl #32
    // 0x13c084c: r0 = value()
    //     0x13c084c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c0850: LoadField: r2 = r0->field_1b
    //     0x13c0850: ldur            w2, [x0, #0x1b]
    // 0x13c0854: DecompressPointer r2
    //     0x13c0854: add             x2, x2, HEAP, lsl #32
    // 0x13c0858: cmp             w2, NULL
    // 0x13c085c: b.ne            #0x13c0868
    // 0x13c0860: r0 = Null
    //     0x13c0860: mov             x0, NULL
    // 0x13c0864: b               #0x13c0898
    // 0x13c0868: LoadField: r0 = r2->field_b
    //     0x13c0868: ldur            w0, [x2, #0xb]
    // 0x13c086c: r1 = LoadInt32Instr(r0)
    //     0x13c086c: sbfx            x1, x0, #1, #0x1f
    // 0x13c0870: mov             x0, x1
    // 0x13c0874: r1 = 0
    //     0x13c0874: movz            x1, #0
    // 0x13c0878: cmp             x1, x0
    // 0x13c087c: b.hs            #0x13c1528
    // 0x13c0880: LoadField: r0 = r2->field_f
    //     0x13c0880: ldur            w0, [x2, #0xf]
    // 0x13c0884: DecompressPointer r0
    //     0x13c0884: add             x0, x0, HEAP, lsl #32
    // 0x13c0888: LoadField: r1 = r0->field_f
    //     0x13c0888: ldur            w1, [x0, #0xf]
    // 0x13c088c: DecompressPointer r1
    //     0x13c088c: add             x1, x1, HEAP, lsl #32
    // 0x13c0890: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x13c0890: ldur            w0, [x1, #0x17]
    // 0x13c0894: DecompressPointer r0
    //     0x13c0894: add             x0, x0, HEAP, lsl #32
    // 0x13c0898: cmp             w0, NULL
    // 0x13c089c: b.ne            #0x13c08a4
    // 0x13c08a0: r0 = ""
    //     0x13c08a0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c08a4: ldur            x2, [fp, #-8]
    // 0x13c08a8: ldur            x1, [fp, #-0x30]
    // 0x13c08ac: ArrayStore: r1[3] = r0  ; List_4
    //     0x13c08ac: add             x25, x1, #0x1b
    //     0x13c08b0: str             w0, [x25]
    //     0x13c08b4: tbz             w0, #0, #0x13c08d0
    //     0x13c08b8: ldurb           w16, [x1, #-1]
    //     0x13c08bc: ldurb           w17, [x0, #-1]
    //     0x13c08c0: and             x16, x17, x16, lsr #2
    //     0x13c08c4: tst             x16, HEAP, lsr #32
    //     0x13c08c8: b.eq            #0x13c08d0
    //     0x13c08cc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13c08d0: ldur            x16, [fp, #-0x30]
    // 0x13c08d4: str             x16, [SP]
    // 0x13c08d8: r0 = _interpolate()
    //     0x13c08d8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x13c08dc: ldur            x2, [fp, #-8]
    // 0x13c08e0: stur            x0, [fp, #-0x30]
    // 0x13c08e4: LoadField: r1 = r2->field_13
    //     0x13c08e4: ldur            w1, [x2, #0x13]
    // 0x13c08e8: DecompressPointer r1
    //     0x13c08e8: add             x1, x1, HEAP, lsl #32
    // 0x13c08ec: r0 = of()
    //     0x13c08ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c08f0: LoadField: r1 = r0->field_87
    //     0x13c08f0: ldur            w1, [x0, #0x87]
    // 0x13c08f4: DecompressPointer r1
    //     0x13c08f4: add             x1, x1, HEAP, lsl #32
    // 0x13c08f8: LoadField: r0 = r1->field_2b
    //     0x13c08f8: ldur            w0, [x1, #0x2b]
    // 0x13c08fc: DecompressPointer r0
    //     0x13c08fc: add             x0, x0, HEAP, lsl #32
    // 0x13c0900: r16 = 12.000000
    //     0x13c0900: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13c0904: ldr             x16, [x16, #0x9e8]
    // 0x13c0908: r30 = Instance_Color
    //     0x13c0908: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13c090c: stp             lr, x16, [SP]
    // 0x13c0910: mov             x1, x0
    // 0x13c0914: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13c0914: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13c0918: ldr             x4, [x4, #0xaa0]
    // 0x13c091c: r0 = copyWith()
    //     0x13c091c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13c0920: stur            x0, [fp, #-0x38]
    // 0x13c0924: r0 = Text()
    //     0x13c0924: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13c0928: mov             x1, x0
    // 0x13c092c: ldur            x0, [fp, #-0x30]
    // 0x13c0930: StoreField: r1->field_b = r0
    //     0x13c0930: stur            w0, [x1, #0xb]
    // 0x13c0934: ldur            x0, [fp, #-0x38]
    // 0x13c0938: StoreField: r1->field_13 = r0
    //     0x13c0938: stur            w0, [x1, #0x13]
    // 0x13c093c: mov             x0, x1
    // 0x13c0940: ldur            x2, [fp, #-8]
    // 0x13c0944: stur            x0, [fp, #-0x30]
    // 0x13c0948: r0 = Padding()
    //     0x13c0948: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13c094c: mov             x2, x0
    // 0x13c0950: r0 = Instance_EdgeInsets
    //     0x13c0950: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x13c0954: ldr             x0, [x0, #0x770]
    // 0x13c0958: stur            x2, [fp, #-0x38]
    // 0x13c095c: StoreField: r2->field_f = r0
    //     0x13c095c: stur            w0, [x2, #0xf]
    // 0x13c0960: ldur            x0, [fp, #-0x30]
    // 0x13c0964: StoreField: r2->field_b = r0
    //     0x13c0964: stur            w0, [x2, #0xb]
    // 0x13c0968: ldur            x0, [fp, #-8]
    // 0x13c096c: LoadField: r1 = r0->field_f
    //     0x13c096c: ldur            w1, [x0, #0xf]
    // 0x13c0970: DecompressPointer r1
    //     0x13c0970: add             x1, x1, HEAP, lsl #32
    // 0x13c0974: r0 = controller()
    //     0x13c0974: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c0978: LoadField: r1 = r0->field_53
    //     0x13c0978: ldur            w1, [x0, #0x53]
    // 0x13c097c: DecompressPointer r1
    //     0x13c097c: add             x1, x1, HEAP, lsl #32
    // 0x13c0980: r0 = value()
    //     0x13c0980: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c0984: LoadField: r2 = r0->field_1b
    //     0x13c0984: ldur            w2, [x0, #0x1b]
    // 0x13c0988: DecompressPointer r2
    //     0x13c0988: add             x2, x2, HEAP, lsl #32
    // 0x13c098c: cmp             w2, NULL
    // 0x13c0990: b.ne            #0x13c099c
    // 0x13c0994: r0 = Null
    //     0x13c0994: mov             x0, NULL
    // 0x13c0998: b               #0x13c09cc
    // 0x13c099c: LoadField: r0 = r2->field_b
    //     0x13c099c: ldur            w0, [x2, #0xb]
    // 0x13c09a0: r1 = LoadInt32Instr(r0)
    //     0x13c09a0: sbfx            x1, x0, #1, #0x1f
    // 0x13c09a4: mov             x0, x1
    // 0x13c09a8: r1 = 0
    //     0x13c09a8: movz            x1, #0
    // 0x13c09ac: cmp             x1, x0
    // 0x13c09b0: b.hs            #0x13c152c
    // 0x13c09b4: LoadField: r0 = r2->field_f
    //     0x13c09b4: ldur            w0, [x2, #0xf]
    // 0x13c09b8: DecompressPointer r0
    //     0x13c09b8: add             x0, x0, HEAP, lsl #32
    // 0x13c09bc: LoadField: r1 = r0->field_f
    //     0x13c09bc: ldur            w1, [x0, #0xf]
    // 0x13c09c0: DecompressPointer r1
    //     0x13c09c0: add             x1, x1, HEAP, lsl #32
    // 0x13c09c4: LoadField: r0 = r1->field_2f
    //     0x13c09c4: ldur            w0, [x1, #0x2f]
    // 0x13c09c8: DecompressPointer r0
    //     0x13c09c8: add             x0, x0, HEAP, lsl #32
    // 0x13c09cc: cmp             w0, NULL
    // 0x13c09d0: b.ne            #0x13c09d8
    // 0x13c09d4: r0 = ""
    //     0x13c09d4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c09d8: ldur            x2, [fp, #-8]
    // 0x13c09dc: stur            x0, [fp, #-0x30]
    // 0x13c09e0: LoadField: r1 = r2->field_13
    //     0x13c09e0: ldur            w1, [x2, #0x13]
    // 0x13c09e4: DecompressPointer r1
    //     0x13c09e4: add             x1, x1, HEAP, lsl #32
    // 0x13c09e8: r0 = of()
    //     0x13c09e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c09ec: LoadField: r1 = r0->field_87
    //     0x13c09ec: ldur            w1, [x0, #0x87]
    // 0x13c09f0: DecompressPointer r1
    //     0x13c09f0: add             x1, x1, HEAP, lsl #32
    // 0x13c09f4: LoadField: r0 = r1->field_7
    //     0x13c09f4: ldur            w0, [x1, #7]
    // 0x13c09f8: DecompressPointer r0
    //     0x13c09f8: add             x0, x0, HEAP, lsl #32
    // 0x13c09fc: r16 = Instance_Color
    //     0x13c09fc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13c0a00: r30 = 12.000000
    //     0x13c0a00: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13c0a04: ldr             lr, [lr, #0x9e8]
    // 0x13c0a08: stp             lr, x16, [SP]
    // 0x13c0a0c: mov             x1, x0
    // 0x13c0a10: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13c0a10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13c0a14: ldr             x4, [x4, #0x9b8]
    // 0x13c0a18: r0 = copyWith()
    //     0x13c0a18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13c0a1c: stur            x0, [fp, #-0x48]
    // 0x13c0a20: r0 = Text()
    //     0x13c0a20: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13c0a24: mov             x2, x0
    // 0x13c0a28: ldur            x0, [fp, #-0x30]
    // 0x13c0a2c: stur            x2, [fp, #-0x50]
    // 0x13c0a30: StoreField: r2->field_b = r0
    //     0x13c0a30: stur            w0, [x2, #0xb]
    // 0x13c0a34: ldur            x0, [fp, #-0x48]
    // 0x13c0a38: StoreField: r2->field_13 = r0
    //     0x13c0a38: stur            w0, [x2, #0x13]
    // 0x13c0a3c: ldur            x0, [fp, #-8]
    // 0x13c0a40: LoadField: r1 = r0->field_f
    //     0x13c0a40: ldur            w1, [x0, #0xf]
    // 0x13c0a44: DecompressPointer r1
    //     0x13c0a44: add             x1, x1, HEAP, lsl #32
    // 0x13c0a48: r0 = controller()
    //     0x13c0a48: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c0a4c: LoadField: r1 = r0->field_73
    //     0x13c0a4c: ldur            w1, [x0, #0x73]
    // 0x13c0a50: DecompressPointer r1
    //     0x13c0a50: add             x1, x1, HEAP, lsl #32
    // 0x13c0a54: r0 = value()
    //     0x13c0a54: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c0a58: LoadField: r1 = r0->field_13
    //     0x13c0a58: ldur            w1, [x0, #0x13]
    // 0x13c0a5c: DecompressPointer r1
    //     0x13c0a5c: add             x1, x1, HEAP, lsl #32
    // 0x13c0a60: cmp             w1, NULL
    // 0x13c0a64: b.ne            #0x13c0a70
    // 0x13c0a68: r0 = Null
    //     0x13c0a68: mov             x0, NULL
    // 0x13c0a6c: b               #0x13c0a88
    // 0x13c0a70: LoadField: r0 = r1->field_b
    //     0x13c0a70: ldur            w0, [x1, #0xb]
    // 0x13c0a74: cbnz            w0, #0x13c0a80
    // 0x13c0a78: r1 = false
    //     0x13c0a78: add             x1, NULL, #0x30  ; false
    // 0x13c0a7c: b               #0x13c0a84
    // 0x13c0a80: r1 = true
    //     0x13c0a80: add             x1, NULL, #0x20  ; true
    // 0x13c0a84: mov             x0, x1
    // 0x13c0a88: cmp             w0, NULL
    // 0x13c0a8c: b.eq            #0x13c0a9c
    // 0x13c0a90: tbnz            w0, #4, #0x13c0a9c
    // 0x13c0a94: r8 = true
    //     0x13c0a94: add             x8, NULL, #0x20  ; true
    // 0x13c0a98: b               #0x13c0b54
    // 0x13c0a9c: ldur            x2, [fp, #-8]
    // 0x13c0aa0: LoadField: r1 = r2->field_f
    //     0x13c0aa0: ldur            w1, [x2, #0xf]
    // 0x13c0aa4: DecompressPointer r1
    //     0x13c0aa4: add             x1, x1, HEAP, lsl #32
    // 0x13c0aa8: r0 = controller()
    //     0x13c0aa8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c0aac: LoadField: r1 = r0->field_6f
    //     0x13c0aac: ldur            w1, [x0, #0x6f]
    // 0x13c0ab0: DecompressPointer r1
    //     0x13c0ab0: add             x1, x1, HEAP, lsl #32
    // 0x13c0ab4: r0 = value()
    //     0x13c0ab4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c0ab8: LoadField: r1 = r0->field_b
    //     0x13c0ab8: ldur            w1, [x0, #0xb]
    // 0x13c0abc: DecompressPointer r1
    //     0x13c0abc: add             x1, x1, HEAP, lsl #32
    // 0x13c0ac0: cmp             w1, NULL
    // 0x13c0ac4: b.ne            #0x13c0ad0
    // 0x13c0ac8: r0 = Null
    //     0x13c0ac8: mov             x0, NULL
    // 0x13c0acc: b               #0x13c0b44
    // 0x13c0ad0: LoadField: r0 = r1->field_f
    //     0x13c0ad0: ldur            w0, [x1, #0xf]
    // 0x13c0ad4: DecompressPointer r0
    //     0x13c0ad4: add             x0, x0, HEAP, lsl #32
    // 0x13c0ad8: cmp             w0, NULL
    // 0x13c0adc: b.ne            #0x13c0ae8
    // 0x13c0ae0: r0 = Null
    //     0x13c0ae0: mov             x0, NULL
    // 0x13c0ae4: b               #0x13c0b44
    // 0x13c0ae8: LoadField: r2 = r0->field_b
    //     0x13c0ae8: ldur            w2, [x0, #0xb]
    // 0x13c0aec: DecompressPointer r2
    //     0x13c0aec: add             x2, x2, HEAP, lsl #32
    // 0x13c0af0: LoadField: r0 = r2->field_b
    //     0x13c0af0: ldur            w0, [x2, #0xb]
    // 0x13c0af4: r1 = LoadInt32Instr(r0)
    //     0x13c0af4: sbfx            x1, x0, #1, #0x1f
    // 0x13c0af8: mov             x0, x1
    // 0x13c0afc: r1 = 0
    //     0x13c0afc: movz            x1, #0
    // 0x13c0b00: cmp             x1, x0
    // 0x13c0b04: b.hs            #0x13c1530
    // 0x13c0b08: LoadField: r0 = r2->field_f
    //     0x13c0b08: ldur            w0, [x2, #0xf]
    // 0x13c0b0c: DecompressPointer r0
    //     0x13c0b0c: add             x0, x0, HEAP, lsl #32
    // 0x13c0b10: LoadField: r1 = r0->field_f
    //     0x13c0b10: ldur            w1, [x0, #0xf]
    // 0x13c0b14: DecompressPointer r1
    //     0x13c0b14: add             x1, x1, HEAP, lsl #32
    // 0x13c0b18: LoadField: r0 = r1->field_43
    //     0x13c0b18: ldur            w0, [x1, #0x43]
    // 0x13c0b1c: DecompressPointer r0
    //     0x13c0b1c: add             x0, x0, HEAP, lsl #32
    // 0x13c0b20: cmp             w0, NULL
    // 0x13c0b24: b.ne            #0x13c0b30
    // 0x13c0b28: r0 = Null
    //     0x13c0b28: mov             x0, NULL
    // 0x13c0b2c: b               #0x13c0b44
    // 0x13c0b30: LoadField: r1 = r0->field_b
    //     0x13c0b30: ldur            w1, [x0, #0xb]
    // 0x13c0b34: cbnz            w1, #0x13c0b40
    // 0x13c0b38: r0 = false
    //     0x13c0b38: add             x0, NULL, #0x30  ; false
    // 0x13c0b3c: b               #0x13c0b44
    // 0x13c0b40: r0 = true
    //     0x13c0b40: add             x0, NULL, #0x20  ; true
    // 0x13c0b44: cmp             w0, NULL
    // 0x13c0b48: b.ne            #0x13c0b50
    // 0x13c0b4c: r0 = false
    //     0x13c0b4c: add             x0, NULL, #0x30  ; false
    // 0x13c0b50: mov             x8, x0
    // 0x13c0b54: ldur            x2, [fp, #-8]
    // 0x13c0b58: ldur            x7, [fp, #-0x18]
    // 0x13c0b5c: ldur            x6, [fp, #-0x20]
    // 0x13c0b60: ldur            x5, [fp, #-0x10]
    // 0x13c0b64: ldur            x4, [fp, #-0x40]
    // 0x13c0b68: ldur            x3, [fp, #-0x38]
    // 0x13c0b6c: ldur            x0, [fp, #-0x50]
    // 0x13c0b70: stur            x8, [fp, #-0x30]
    // 0x13c0b74: LoadField: r1 = r2->field_13
    //     0x13c0b74: ldur            w1, [x2, #0x13]
    // 0x13c0b78: DecompressPointer r1
    //     0x13c0b78: add             x1, x1, HEAP, lsl #32
    // 0x13c0b7c: r0 = of()
    //     0x13c0b7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c0b80: LoadField: r1 = r0->field_5b
    //     0x13c0b80: ldur            w1, [x0, #0x5b]
    // 0x13c0b84: DecompressPointer r1
    //     0x13c0b84: add             x1, x1, HEAP, lsl #32
    // 0x13c0b88: r0 = LoadClassIdInstr(r1)
    //     0x13c0b88: ldur            x0, [x1, #-1]
    //     0x13c0b8c: ubfx            x0, x0, #0xc, #0x14
    // 0x13c0b90: d0 = 0.100000
    //     0x13c0b90: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x13c0b94: r0 = GDT[cid_x0 + -0xffa]()
    //     0x13c0b94: sub             lr, x0, #0xffa
    //     0x13c0b98: ldr             lr, [x21, lr, lsl #3]
    //     0x13c0b9c: blr             lr
    // 0x13c0ba0: mov             x2, x0
    // 0x13c0ba4: r1 = Null
    //     0x13c0ba4: mov             x1, NULL
    // 0x13c0ba8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x13c0ba8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x13c0bac: r0 = Border.all()
    //     0x13c0bac: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x13c0bb0: stur            x0, [fp, #-0x48]
    // 0x13c0bb4: r0 = Radius()
    //     0x13c0bb4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x13c0bb8: d0 = 12.000000
    //     0x13c0bb8: fmov            d0, #12.00000000
    // 0x13c0bbc: stur            x0, [fp, #-0x58]
    // 0x13c0bc0: StoreField: r0->field_7 = d0
    //     0x13c0bc0: stur            d0, [x0, #7]
    // 0x13c0bc4: StoreField: r0->field_f = d0
    //     0x13c0bc4: stur            d0, [x0, #0xf]
    // 0x13c0bc8: r0 = BorderRadius()
    //     0x13c0bc8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x13c0bcc: mov             x1, x0
    // 0x13c0bd0: ldur            x0, [fp, #-0x58]
    // 0x13c0bd4: stur            x1, [fp, #-0x60]
    // 0x13c0bd8: StoreField: r1->field_7 = r0
    //     0x13c0bd8: stur            w0, [x1, #7]
    // 0x13c0bdc: StoreField: r1->field_b = r0
    //     0x13c0bdc: stur            w0, [x1, #0xb]
    // 0x13c0be0: StoreField: r1->field_f = r0
    //     0x13c0be0: stur            w0, [x1, #0xf]
    // 0x13c0be4: StoreField: r1->field_13 = r0
    //     0x13c0be4: stur            w0, [x1, #0x13]
    // 0x13c0be8: r0 = BoxDecoration()
    //     0x13c0be8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x13c0bec: mov             x2, x0
    // 0x13c0bf0: ldur            x0, [fp, #-0x48]
    // 0x13c0bf4: stur            x2, [fp, #-0x58]
    // 0x13c0bf8: StoreField: r2->field_f = r0
    //     0x13c0bf8: stur            w0, [x2, #0xf]
    // 0x13c0bfc: ldur            x0, [fp, #-0x60]
    // 0x13c0c00: StoreField: r2->field_13 = r0
    //     0x13c0c00: stur            w0, [x2, #0x13]
    // 0x13c0c04: r0 = Instance_BoxShape
    //     0x13c0c04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13c0c08: ldr             x0, [x0, #0x80]
    // 0x13c0c0c: StoreField: r2->field_23 = r0
    //     0x13c0c0c: stur            w0, [x2, #0x23]
    // 0x13c0c10: ldur            x0, [fp, #-8]
    // 0x13c0c14: LoadField: r1 = r0->field_13
    //     0x13c0c14: ldur            w1, [x0, #0x13]
    // 0x13c0c18: DecompressPointer r1
    //     0x13c0c18: add             x1, x1, HEAP, lsl #32
    // 0x13c0c1c: r0 = of()
    //     0x13c0c1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c0c20: LoadField: r1 = r0->field_87
    //     0x13c0c20: ldur            w1, [x0, #0x87]
    // 0x13c0c24: DecompressPointer r1
    //     0x13c0c24: add             x1, x1, HEAP, lsl #32
    // 0x13c0c28: LoadField: r0 = r1->field_7
    //     0x13c0c28: ldur            w0, [x1, #7]
    // 0x13c0c2c: DecompressPointer r0
    //     0x13c0c2c: add             x0, x0, HEAP, lsl #32
    // 0x13c0c30: stur            x0, [fp, #-0x48]
    // 0x13c0c34: r1 = Instance_Color
    //     0x13c0c34: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13c0c38: d0 = 0.700000
    //     0x13c0c38: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13c0c3c: ldr             d0, [x17, #0xf48]
    // 0x13c0c40: r0 = withOpacity()
    //     0x13c0c40: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13c0c44: r16 = 12.000000
    //     0x13c0c44: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13c0c48: ldr             x16, [x16, #0x9e8]
    // 0x13c0c4c: stp             x0, x16, [SP]
    // 0x13c0c50: ldur            x1, [fp, #-0x48]
    // 0x13c0c54: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13c0c54: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13c0c58: ldr             x4, [x4, #0xaa0]
    // 0x13c0c5c: r0 = copyWith()
    //     0x13c0c5c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13c0c60: stur            x0, [fp, #-0x48]
    // 0x13c0c64: r0 = Text()
    //     0x13c0c64: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13c0c68: mov             x1, x0
    // 0x13c0c6c: r0 = "Customised"
    //     0x13c0c6c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d88] "Customised"
    //     0x13c0c70: ldr             x0, [x0, #0xd88]
    // 0x13c0c74: stur            x1, [fp, #-0x60]
    // 0x13c0c78: StoreField: r1->field_b = r0
    //     0x13c0c78: stur            w0, [x1, #0xb]
    // 0x13c0c7c: ldur            x0, [fp, #-0x48]
    // 0x13c0c80: StoreField: r1->field_13 = r0
    //     0x13c0c80: stur            w0, [x1, #0x13]
    // 0x13c0c84: r0 = Center()
    //     0x13c0c84: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x13c0c88: mov             x1, x0
    // 0x13c0c8c: r0 = Instance_Alignment
    //     0x13c0c8c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x13c0c90: ldr             x0, [x0, #0xb10]
    // 0x13c0c94: stur            x1, [fp, #-0x48]
    // 0x13c0c98: StoreField: r1->field_f = r0
    //     0x13c0c98: stur            w0, [x1, #0xf]
    // 0x13c0c9c: ldur            x2, [fp, #-0x60]
    // 0x13c0ca0: StoreField: r1->field_b = r2
    //     0x13c0ca0: stur            w2, [x1, #0xb]
    // 0x13c0ca4: r0 = Container()
    //     0x13c0ca4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13c0ca8: stur            x0, [fp, #-0x60]
    // 0x13c0cac: r16 = Instance_EdgeInsets
    //     0x13c0cac: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d90] Obj!EdgeInsets@d59631
    //     0x13c0cb0: ldr             x16, [x16, #0xd90]
    // 0x13c0cb4: ldur            lr, [fp, #-0x58]
    // 0x13c0cb8: stp             lr, x16, [SP, #8]
    // 0x13c0cbc: ldur            x16, [fp, #-0x48]
    // 0x13c0cc0: str             x16, [SP]
    // 0x13c0cc4: mov             x1, x0
    // 0x13c0cc8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0x13c0cc8: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0x13c0ccc: ldr             x4, [x4, #0x610]
    // 0x13c0cd0: r0 = Container()
    //     0x13c0cd0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13c0cd4: r0 = Visibility()
    //     0x13c0cd4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x13c0cd8: mov             x3, x0
    // 0x13c0cdc: ldur            x0, [fp, #-0x60]
    // 0x13c0ce0: stur            x3, [fp, #-0x48]
    // 0x13c0ce4: StoreField: r3->field_b = r0
    //     0x13c0ce4: stur            w0, [x3, #0xb]
    // 0x13c0ce8: r0 = Instance_SizedBox
    //     0x13c0ce8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x13c0cec: StoreField: r3->field_f = r0
    //     0x13c0cec: stur            w0, [x3, #0xf]
    // 0x13c0cf0: ldur            x0, [fp, #-0x30]
    // 0x13c0cf4: StoreField: r3->field_13 = r0
    //     0x13c0cf4: stur            w0, [x3, #0x13]
    // 0x13c0cf8: r0 = false
    //     0x13c0cf8: add             x0, NULL, #0x30  ; false
    // 0x13c0cfc: ArrayStore: r3[0] = r0  ; List_4
    //     0x13c0cfc: stur            w0, [x3, #0x17]
    // 0x13c0d00: StoreField: r3->field_1b = r0
    //     0x13c0d00: stur            w0, [x3, #0x1b]
    // 0x13c0d04: StoreField: r3->field_1f = r0
    //     0x13c0d04: stur            w0, [x3, #0x1f]
    // 0x13c0d08: StoreField: r3->field_23 = r0
    //     0x13c0d08: stur            w0, [x3, #0x23]
    // 0x13c0d0c: StoreField: r3->field_27 = r0
    //     0x13c0d0c: stur            w0, [x3, #0x27]
    // 0x13c0d10: StoreField: r3->field_2b = r0
    //     0x13c0d10: stur            w0, [x3, #0x2b]
    // 0x13c0d14: r1 = Null
    //     0x13c0d14: mov             x1, NULL
    // 0x13c0d18: r2 = 6
    //     0x13c0d18: movz            x2, #0x6
    // 0x13c0d1c: r0 = AllocateArray()
    //     0x13c0d1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c0d20: mov             x2, x0
    // 0x13c0d24: ldur            x0, [fp, #-0x50]
    // 0x13c0d28: stur            x2, [fp, #-0x30]
    // 0x13c0d2c: StoreField: r2->field_f = r0
    //     0x13c0d2c: stur            w0, [x2, #0xf]
    // 0x13c0d30: r16 = Instance_SizedBox
    //     0x13c0d30: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0x13c0d34: ldr             x16, [x16, #0x998]
    // 0x13c0d38: StoreField: r2->field_13 = r16
    //     0x13c0d38: stur            w16, [x2, #0x13]
    // 0x13c0d3c: ldur            x0, [fp, #-0x48]
    // 0x13c0d40: ArrayStore: r2[0] = r0  ; List_4
    //     0x13c0d40: stur            w0, [x2, #0x17]
    // 0x13c0d44: r1 = <Widget>
    //     0x13c0d44: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13c0d48: r0 = AllocateGrowableArray()
    //     0x13c0d48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13c0d4c: mov             x1, x0
    // 0x13c0d50: ldur            x0, [fp, #-0x30]
    // 0x13c0d54: stur            x1, [fp, #-0x48]
    // 0x13c0d58: StoreField: r1->field_f = r0
    //     0x13c0d58: stur            w0, [x1, #0xf]
    // 0x13c0d5c: r2 = 6
    //     0x13c0d5c: movz            x2, #0x6
    // 0x13c0d60: StoreField: r1->field_b = r2
    //     0x13c0d60: stur            w2, [x1, #0xb]
    // 0x13c0d64: r0 = Row()
    //     0x13c0d64: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13c0d68: mov             x3, x0
    // 0x13c0d6c: r0 = Instance_Axis
    //     0x13c0d6c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13c0d70: stur            x3, [fp, #-0x30]
    // 0x13c0d74: StoreField: r3->field_f = r0
    //     0x13c0d74: stur            w0, [x3, #0xf]
    // 0x13c0d78: r4 = Instance_MainAxisAlignment
    //     0x13c0d78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13c0d7c: ldr             x4, [x4, #0xa08]
    // 0x13c0d80: StoreField: r3->field_13 = r4
    //     0x13c0d80: stur            w4, [x3, #0x13]
    // 0x13c0d84: r5 = Instance_MainAxisSize
    //     0x13c0d84: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13c0d88: ldr             x5, [x5, #0xa10]
    // 0x13c0d8c: ArrayStore: r3[0] = r5  ; List_4
    //     0x13c0d8c: stur            w5, [x3, #0x17]
    // 0x13c0d90: r6 = Instance_CrossAxisAlignment
    //     0x13c0d90: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13c0d94: ldr             x6, [x6, #0xa18]
    // 0x13c0d98: StoreField: r3->field_1b = r6
    //     0x13c0d98: stur            w6, [x3, #0x1b]
    // 0x13c0d9c: r7 = Instance_VerticalDirection
    //     0x13c0d9c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13c0da0: ldr             x7, [x7, #0xa20]
    // 0x13c0da4: StoreField: r3->field_23 = r7
    //     0x13c0da4: stur            w7, [x3, #0x23]
    // 0x13c0da8: r8 = Instance_Clip
    //     0x13c0da8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13c0dac: ldr             x8, [x8, #0x38]
    // 0x13c0db0: StoreField: r3->field_2b = r8
    //     0x13c0db0: stur            w8, [x3, #0x2b]
    // 0x13c0db4: StoreField: r3->field_2f = rZR
    //     0x13c0db4: stur            xzr, [x3, #0x2f]
    // 0x13c0db8: ldur            x1, [fp, #-0x48]
    // 0x13c0dbc: StoreField: r3->field_b = r1
    //     0x13c0dbc: stur            w1, [x3, #0xb]
    // 0x13c0dc0: r1 = Null
    //     0x13c0dc0: mov             x1, NULL
    // 0x13c0dc4: r2 = 8
    //     0x13c0dc4: movz            x2, #0x8
    // 0x13c0dc8: r0 = AllocateArray()
    //     0x13c0dc8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c0dcc: mov             x2, x0
    // 0x13c0dd0: ldur            x0, [fp, #-0x40]
    // 0x13c0dd4: stur            x2, [fp, #-0x48]
    // 0x13c0dd8: StoreField: r2->field_f = r0
    //     0x13c0dd8: stur            w0, [x2, #0xf]
    // 0x13c0ddc: ldur            x0, [fp, #-0x38]
    // 0x13c0de0: StoreField: r2->field_13 = r0
    //     0x13c0de0: stur            w0, [x2, #0x13]
    // 0x13c0de4: r16 = Instance_SizedBox
    //     0x13c0de4: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x13c0de8: ldr             x16, [x16, #0xc70]
    // 0x13c0dec: ArrayStore: r2[0] = r16  ; List_4
    //     0x13c0dec: stur            w16, [x2, #0x17]
    // 0x13c0df0: ldur            x0, [fp, #-0x30]
    // 0x13c0df4: StoreField: r2->field_1b = r0
    //     0x13c0df4: stur            w0, [x2, #0x1b]
    // 0x13c0df8: r1 = <Widget>
    //     0x13c0df8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13c0dfc: r0 = AllocateGrowableArray()
    //     0x13c0dfc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13c0e00: mov             x1, x0
    // 0x13c0e04: ldur            x0, [fp, #-0x48]
    // 0x13c0e08: stur            x1, [fp, #-0x30]
    // 0x13c0e0c: StoreField: r1->field_f = r0
    //     0x13c0e0c: stur            w0, [x1, #0xf]
    // 0x13c0e10: r2 = 8
    //     0x13c0e10: movz            x2, #0x8
    // 0x13c0e14: StoreField: r1->field_b = r2
    //     0x13c0e14: stur            w2, [x1, #0xb]
    // 0x13c0e18: r0 = Column()
    //     0x13c0e18: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13c0e1c: mov             x2, x0
    // 0x13c0e20: r0 = Instance_Axis
    //     0x13c0e20: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13c0e24: stur            x2, [fp, #-0x38]
    // 0x13c0e28: StoreField: r2->field_f = r0
    //     0x13c0e28: stur            w0, [x2, #0xf]
    // 0x13c0e2c: r3 = Instance_MainAxisAlignment
    //     0x13c0e2c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13c0e30: ldr             x3, [x3, #0xa08]
    // 0x13c0e34: StoreField: r2->field_13 = r3
    //     0x13c0e34: stur            w3, [x2, #0x13]
    // 0x13c0e38: r4 = Instance_MainAxisSize
    //     0x13c0e38: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13c0e3c: ldr             x4, [x4, #0xa10]
    // 0x13c0e40: ArrayStore: r2[0] = r4  ; List_4
    //     0x13c0e40: stur            w4, [x2, #0x17]
    // 0x13c0e44: r1 = Instance_CrossAxisAlignment
    //     0x13c0e44: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x13c0e48: ldr             x1, [x1, #0x890]
    // 0x13c0e4c: StoreField: r2->field_1b = r1
    //     0x13c0e4c: stur            w1, [x2, #0x1b]
    // 0x13c0e50: r5 = Instance_VerticalDirection
    //     0x13c0e50: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13c0e54: ldr             x5, [x5, #0xa20]
    // 0x13c0e58: StoreField: r2->field_23 = r5
    //     0x13c0e58: stur            w5, [x2, #0x23]
    // 0x13c0e5c: r6 = Instance_Clip
    //     0x13c0e5c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13c0e60: ldr             x6, [x6, #0x38]
    // 0x13c0e64: StoreField: r2->field_2b = r6
    //     0x13c0e64: stur            w6, [x2, #0x2b]
    // 0x13c0e68: StoreField: r2->field_2f = rZR
    //     0x13c0e68: stur            xzr, [x2, #0x2f]
    // 0x13c0e6c: ldur            x1, [fp, #-0x30]
    // 0x13c0e70: StoreField: r2->field_b = r1
    //     0x13c0e70: stur            w1, [x2, #0xb]
    // 0x13c0e74: r1 = <FlexParentData>
    //     0x13c0e74: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x13c0e78: ldr             x1, [x1, #0xe00]
    // 0x13c0e7c: r0 = Expanded()
    //     0x13c0e7c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x13c0e80: mov             x3, x0
    // 0x13c0e84: r0 = 1
    //     0x13c0e84: movz            x0, #0x1
    // 0x13c0e88: stur            x3, [fp, #-0x30]
    // 0x13c0e8c: StoreField: r3->field_13 = r0
    //     0x13c0e8c: stur            x0, [x3, #0x13]
    // 0x13c0e90: r0 = Instance_FlexFit
    //     0x13c0e90: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x13c0e94: ldr             x0, [x0, #0xe08]
    // 0x13c0e98: StoreField: r3->field_1b = r0
    //     0x13c0e98: stur            w0, [x3, #0x1b]
    // 0x13c0e9c: ldur            x0, [fp, #-0x38]
    // 0x13c0ea0: StoreField: r3->field_b = r0
    //     0x13c0ea0: stur            w0, [x3, #0xb]
    // 0x13c0ea4: r1 = Null
    //     0x13c0ea4: mov             x1, NULL
    // 0x13c0ea8: r2 = 6
    //     0x13c0ea8: movz            x2, #0x6
    // 0x13c0eac: r0 = AllocateArray()
    //     0x13c0eac: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c0eb0: mov             x2, x0
    // 0x13c0eb4: ldur            x0, [fp, #-0x10]
    // 0x13c0eb8: stur            x2, [fp, #-0x38]
    // 0x13c0ebc: StoreField: r2->field_f = r0
    //     0x13c0ebc: stur            w0, [x2, #0xf]
    // 0x13c0ec0: r16 = Instance_SizedBox
    //     0x13c0ec0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x13c0ec4: ldr             x16, [x16, #0xb20]
    // 0x13c0ec8: StoreField: r2->field_13 = r16
    //     0x13c0ec8: stur            w16, [x2, #0x13]
    // 0x13c0ecc: ldur            x0, [fp, #-0x30]
    // 0x13c0ed0: ArrayStore: r2[0] = r0  ; List_4
    //     0x13c0ed0: stur            w0, [x2, #0x17]
    // 0x13c0ed4: r1 = <Widget>
    //     0x13c0ed4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13c0ed8: r0 = AllocateGrowableArray()
    //     0x13c0ed8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13c0edc: mov             x1, x0
    // 0x13c0ee0: ldur            x0, [fp, #-0x38]
    // 0x13c0ee4: stur            x1, [fp, #-0x10]
    // 0x13c0ee8: StoreField: r1->field_f = r0
    //     0x13c0ee8: stur            w0, [x1, #0xf]
    // 0x13c0eec: r0 = 6
    //     0x13c0eec: movz            x0, #0x6
    // 0x13c0ef0: StoreField: r1->field_b = r0
    //     0x13c0ef0: stur            w0, [x1, #0xb]
    // 0x13c0ef4: r0 = Row()
    //     0x13c0ef4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13c0ef8: mov             x1, x0
    // 0x13c0efc: r0 = Instance_Axis
    //     0x13c0efc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13c0f00: stur            x1, [fp, #-0x30]
    // 0x13c0f04: StoreField: r1->field_f = r0
    //     0x13c0f04: stur            w0, [x1, #0xf]
    // 0x13c0f08: r2 = Instance_MainAxisAlignment
    //     0x13c0f08: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13c0f0c: ldr             x2, [x2, #0xa08]
    // 0x13c0f10: StoreField: r1->field_13 = r2
    //     0x13c0f10: stur            w2, [x1, #0x13]
    // 0x13c0f14: r3 = Instance_MainAxisSize
    //     0x13c0f14: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13c0f18: ldr             x3, [x3, #0xa10]
    // 0x13c0f1c: ArrayStore: r1[0] = r3  ; List_4
    //     0x13c0f1c: stur            w3, [x1, #0x17]
    // 0x13c0f20: r4 = Instance_CrossAxisAlignment
    //     0x13c0f20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13c0f24: ldr             x4, [x4, #0xa18]
    // 0x13c0f28: StoreField: r1->field_1b = r4
    //     0x13c0f28: stur            w4, [x1, #0x1b]
    // 0x13c0f2c: r5 = Instance_VerticalDirection
    //     0x13c0f2c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13c0f30: ldr             x5, [x5, #0xa20]
    // 0x13c0f34: StoreField: r1->field_23 = r5
    //     0x13c0f34: stur            w5, [x1, #0x23]
    // 0x13c0f38: r6 = Instance_Clip
    //     0x13c0f38: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13c0f3c: ldr             x6, [x6, #0x38]
    // 0x13c0f40: StoreField: r1->field_2b = r6
    //     0x13c0f40: stur            w6, [x1, #0x2b]
    // 0x13c0f44: StoreField: r1->field_2f = rZR
    //     0x13c0f44: stur            xzr, [x1, #0x2f]
    // 0x13c0f48: ldur            x7, [fp, #-0x10]
    // 0x13c0f4c: StoreField: r1->field_b = r7
    //     0x13c0f4c: stur            w7, [x1, #0xb]
    // 0x13c0f50: r0 = Padding()
    //     0x13c0f50: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13c0f54: mov             x1, x0
    // 0x13c0f58: r0 = Instance_EdgeInsets
    //     0x13c0f58: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fc30] Obj!EdgeInsets@d57891
    //     0x13c0f5c: ldr             x0, [x0, #0xc30]
    // 0x13c0f60: stur            x1, [fp, #-0x10]
    // 0x13c0f64: StoreField: r1->field_f = r0
    //     0x13c0f64: stur            w0, [x1, #0xf]
    // 0x13c0f68: ldur            x0, [fp, #-0x30]
    // 0x13c0f6c: StoreField: r1->field_b = r0
    //     0x13c0f6c: stur            w0, [x1, #0xb]
    // 0x13c0f70: r0 = Container()
    //     0x13c0f70: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13c0f74: stur            x0, [fp, #-0x30]
    // 0x13c0f78: ldur            x16, [fp, #-0x28]
    // 0x13c0f7c: ldur            lr, [fp, #-0x10]
    // 0x13c0f80: stp             lr, x16, [SP]
    // 0x13c0f84: mov             x1, x0
    // 0x13c0f88: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x13c0f88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x13c0f8c: ldr             x4, [x4, #0x88]
    // 0x13c0f90: r0 = Container()
    //     0x13c0f90: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13c0f94: r1 = Null
    //     0x13c0f94: mov             x1, NULL
    // 0x13c0f98: r2 = 4
    //     0x13c0f98: movz            x2, #0x4
    // 0x13c0f9c: r0 = AllocateArray()
    //     0x13c0f9c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c0fa0: mov             x2, x0
    // 0x13c0fa4: ldur            x0, [fp, #-0x20]
    // 0x13c0fa8: stur            x2, [fp, #-0x10]
    // 0x13c0fac: StoreField: r2->field_f = r0
    //     0x13c0fac: stur            w0, [x2, #0xf]
    // 0x13c0fb0: ldur            x0, [fp, #-0x30]
    // 0x13c0fb4: StoreField: r2->field_13 = r0
    //     0x13c0fb4: stur            w0, [x2, #0x13]
    // 0x13c0fb8: r1 = <Widget>
    //     0x13c0fb8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13c0fbc: r0 = AllocateGrowableArray()
    //     0x13c0fbc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13c0fc0: mov             x1, x0
    // 0x13c0fc4: ldur            x0, [fp, #-0x10]
    // 0x13c0fc8: stur            x1, [fp, #-0x20]
    // 0x13c0fcc: StoreField: r1->field_f = r0
    //     0x13c0fcc: stur            w0, [x1, #0xf]
    // 0x13c0fd0: r2 = 4
    //     0x13c0fd0: movz            x2, #0x4
    // 0x13c0fd4: StoreField: r1->field_b = r2
    //     0x13c0fd4: stur            w2, [x1, #0xb]
    // 0x13c0fd8: r0 = Column()
    //     0x13c0fd8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13c0fdc: mov             x1, x0
    // 0x13c0fe0: r0 = Instance_Axis
    //     0x13c0fe0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13c0fe4: stur            x1, [fp, #-0x10]
    // 0x13c0fe8: StoreField: r1->field_f = r0
    //     0x13c0fe8: stur            w0, [x1, #0xf]
    // 0x13c0fec: r0 = Instance_MainAxisAlignment
    //     0x13c0fec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13c0ff0: ldr             x0, [x0, #0xa08]
    // 0x13c0ff4: StoreField: r1->field_13 = r0
    //     0x13c0ff4: stur            w0, [x1, #0x13]
    // 0x13c0ff8: r0 = Instance_MainAxisSize
    //     0x13c0ff8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13c0ffc: ldr             x0, [x0, #0xa10]
    // 0x13c1000: ArrayStore: r1[0] = r0  ; List_4
    //     0x13c1000: stur            w0, [x1, #0x17]
    // 0x13c1004: r2 = Instance_CrossAxisAlignment
    //     0x13c1004: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13c1008: ldr             x2, [x2, #0xa18]
    // 0x13c100c: StoreField: r1->field_1b = r2
    //     0x13c100c: stur            w2, [x1, #0x1b]
    // 0x13c1010: r3 = Instance_VerticalDirection
    //     0x13c1010: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13c1014: ldr             x3, [x3, #0xa20]
    // 0x13c1018: StoreField: r1->field_23 = r3
    //     0x13c1018: stur            w3, [x1, #0x23]
    // 0x13c101c: r4 = Instance_Clip
    //     0x13c101c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13c1020: ldr             x4, [x4, #0x38]
    // 0x13c1024: StoreField: r1->field_2b = r4
    //     0x13c1024: stur            w4, [x1, #0x2b]
    // 0x13c1028: StoreField: r1->field_2f = rZR
    //     0x13c1028: stur            xzr, [x1, #0x2f]
    // 0x13c102c: ldur            x5, [fp, #-0x20]
    // 0x13c1030: StoreField: r1->field_b = r5
    //     0x13c1030: stur            w5, [x1, #0xb]
    // 0x13c1034: r0 = SvgPicture()
    //     0x13c1034: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x13c1038: stur            x0, [fp, #-0x20]
    // 0x13c103c: r16 = Instance_BoxFit
    //     0x13c103c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x13c1040: ldr             x16, [x16, #0xb18]
    // 0x13c1044: str             x16, [SP]
    // 0x13c1048: mov             x1, x0
    // 0x13c104c: r2 = "assets/images/product_between_icon.svg"
    //     0x13c104c: add             x2, PP, #0x35, lsl #12  ; [pp+0x35f28] "assets/images/product_between_icon.svg"
    //     0x13c1050: ldr             x2, [x2, #0xf28]
    // 0x13c1054: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0x13c1054: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0x13c1058: ldr             x4, [x4, #0xb0]
    // 0x13c105c: r0 = SvgPicture.asset()
    //     0x13c105c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x13c1060: r1 = <StackParentData>
    //     0x13c1060: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x13c1064: ldr             x1, [x1, #0x8e0]
    // 0x13c1068: r0 = Positioned()
    //     0x13c1068: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x13c106c: mov             x3, x0
    // 0x13c1070: r0 = 0.000000
    //     0x13c1070: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x13c1074: stur            x3, [fp, #-0x28]
    // 0x13c1078: StoreField: r3->field_13 = r0
    //     0x13c1078: stur            w0, [x3, #0x13]
    // 0x13c107c: ArrayStore: r3[0] = r0  ; List_4
    //     0x13c107c: stur            w0, [x3, #0x17]
    // 0x13c1080: StoreField: r3->field_1b = r0
    //     0x13c1080: stur            w0, [x3, #0x1b]
    // 0x13c1084: r0 = 7.000000
    //     0x13c1084: add             x0, PP, #0x43, lsl #12  ; [pp+0x438b0] 7
    //     0x13c1088: ldr             x0, [x0, #0x8b0]
    // 0x13c108c: StoreField: r3->field_1f = r0
    //     0x13c108c: stur            w0, [x3, #0x1f]
    // 0x13c1090: ldur            x0, [fp, #-0x20]
    // 0x13c1094: StoreField: r3->field_b = r0
    //     0x13c1094: stur            w0, [x3, #0xb]
    // 0x13c1098: r1 = Null
    //     0x13c1098: mov             x1, NULL
    // 0x13c109c: r2 = 4
    //     0x13c109c: movz            x2, #0x4
    // 0x13c10a0: r0 = AllocateArray()
    //     0x13c10a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c10a4: mov             x2, x0
    // 0x13c10a8: ldur            x0, [fp, #-0x10]
    // 0x13c10ac: stur            x2, [fp, #-0x20]
    // 0x13c10b0: StoreField: r2->field_f = r0
    //     0x13c10b0: stur            w0, [x2, #0xf]
    // 0x13c10b4: ldur            x0, [fp, #-0x28]
    // 0x13c10b8: StoreField: r2->field_13 = r0
    //     0x13c10b8: stur            w0, [x2, #0x13]
    // 0x13c10bc: r1 = <Widget>
    //     0x13c10bc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13c10c0: r0 = AllocateGrowableArray()
    //     0x13c10c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13c10c4: mov             x1, x0
    // 0x13c10c8: ldur            x0, [fp, #-0x20]
    // 0x13c10cc: stur            x1, [fp, #-0x10]
    // 0x13c10d0: StoreField: r1->field_f = r0
    //     0x13c10d0: stur            w0, [x1, #0xf]
    // 0x13c10d4: r2 = 4
    //     0x13c10d4: movz            x2, #0x4
    // 0x13c10d8: StoreField: r1->field_b = r2
    //     0x13c10d8: stur            w2, [x1, #0xb]
    // 0x13c10dc: r0 = Stack()
    //     0x13c10dc: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x13c10e0: mov             x1, x0
    // 0x13c10e4: r0 = Instance_Alignment
    //     0x13c10e4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x13c10e8: ldr             x0, [x0, #0xb10]
    // 0x13c10ec: stur            x1, [fp, #-0x20]
    // 0x13c10f0: StoreField: r1->field_f = r0
    //     0x13c10f0: stur            w0, [x1, #0xf]
    // 0x13c10f4: r0 = Instance_StackFit
    //     0x13c10f4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x13c10f8: ldr             x0, [x0, #0xfa8]
    // 0x13c10fc: ArrayStore: r1[0] = r0  ; List_4
    //     0x13c10fc: stur            w0, [x1, #0x17]
    // 0x13c1100: r0 = Instance_Clip
    //     0x13c1100: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x13c1104: ldr             x0, [x0, #0x7e0]
    // 0x13c1108: StoreField: r1->field_1b = r0
    //     0x13c1108: stur            w0, [x1, #0x1b]
    // 0x13c110c: ldur            x0, [fp, #-0x10]
    // 0x13c1110: StoreField: r1->field_b = r0
    //     0x13c1110: stur            w0, [x1, #0xb]
    // 0x13c1114: r0 = Container()
    //     0x13c1114: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13c1118: mov             x1, x0
    // 0x13c111c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13c111c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13c1120: r0 = Container()
    //     0x13c1120: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13c1124: r0 = Accordion()
    //     0x13c1124: bl              #0x9f39a8  ; AllocateAccordionStub -> Accordion (size=0x3c)
    // 0x13c1128: mov             x1, x0
    // 0x13c112c: ldur            x0, [fp, #-0x18]
    // 0x13c1130: stur            x1, [fp, #-0x10]
    // 0x13c1134: StoreField: r1->field_b = r0
    //     0x13c1134: stur            w0, [x1, #0xb]
    // 0x13c1138: ldur            x0, [fp, #-0x20]
    // 0x13c113c: StoreField: r1->field_13 = r0
    //     0x13c113c: stur            w0, [x1, #0x13]
    // 0x13c1140: r0 = false
    //     0x13c1140: add             x0, NULL, #0x30  ; false
    // 0x13c1144: ArrayStore: r1[0] = r0  ; List_4
    //     0x13c1144: stur            w0, [x1, #0x17]
    // 0x13c1148: r0 = 25.000000
    //     0x13c1148: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0x13c114c: ldr             x0, [x0, #0x98]
    // 0x13c1150: StoreField: r1->field_1b = r0
    //     0x13c1150: stur            w0, [x1, #0x1b]
    // 0x13c1154: r0 = true
    //     0x13c1154: add             x0, NULL, #0x20  ; true
    // 0x13c1158: StoreField: r1->field_1f = r0
    //     0x13c1158: stur            w0, [x1, #0x1f]
    // 0x13c115c: r0 = Padding()
    //     0x13c115c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13c1160: mov             x2, x0
    // 0x13c1164: r0 = Instance_EdgeInsets
    //     0x13c1164: add             x0, PP, #0x46, lsl #12  ; [pp+0x466b0] Obj!EdgeInsets@d59c01
    //     0x13c1168: ldr             x0, [x0, #0x6b0]
    // 0x13c116c: stur            x2, [fp, #-0x18]
    // 0x13c1170: StoreField: r2->field_f = r0
    //     0x13c1170: stur            w0, [x2, #0xf]
    // 0x13c1174: ldur            x0, [fp, #-0x10]
    // 0x13c1178: StoreField: r2->field_b = r0
    //     0x13c1178: stur            w0, [x2, #0xb]
    // 0x13c117c: ldur            x0, [fp, #-8]
    // 0x13c1180: LoadField: r1 = r0->field_f
    //     0x13c1180: ldur            w1, [x0, #0xf]
    // 0x13c1184: DecompressPointer r1
    //     0x13c1184: add             x1, x1, HEAP, lsl #32
    // 0x13c1188: r0 = controller()
    //     0x13c1188: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c118c: LoadField: r1 = r0->field_53
    //     0x13c118c: ldur            w1, [x0, #0x53]
    // 0x13c1190: DecompressPointer r1
    //     0x13c1190: add             x1, x1, HEAP, lsl #32
    // 0x13c1194: r0 = value()
    //     0x13c1194: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c1198: LoadField: r1 = r0->field_2f
    //     0x13c1198: ldur            w1, [x0, #0x2f]
    // 0x13c119c: DecompressPointer r1
    //     0x13c119c: add             x1, x1, HEAP, lsl #32
    // 0x13c11a0: cmp             w1, NULL
    // 0x13c11a4: b.ne            #0x13c11b0
    // 0x13c11a8: r0 = ""
    //     0x13c11a8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c11ac: b               #0x13c11b4
    // 0x13c11b0: mov             x0, x1
    // 0x13c11b4: ldur            x2, [fp, #-8]
    // 0x13c11b8: stur            x0, [fp, #-0x10]
    // 0x13c11bc: LoadField: r1 = r2->field_13
    //     0x13c11bc: ldur            w1, [x2, #0x13]
    // 0x13c11c0: DecompressPointer r1
    //     0x13c11c0: add             x1, x1, HEAP, lsl #32
    // 0x13c11c4: r0 = of()
    //     0x13c11c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c11c8: LoadField: r1 = r0->field_87
    //     0x13c11c8: ldur            w1, [x0, #0x87]
    // 0x13c11cc: DecompressPointer r1
    //     0x13c11cc: add             x1, x1, HEAP, lsl #32
    // 0x13c11d0: LoadField: r0 = r1->field_7
    //     0x13c11d0: ldur            w0, [x1, #7]
    // 0x13c11d4: DecompressPointer r0
    //     0x13c11d4: add             x0, x0, HEAP, lsl #32
    // 0x13c11d8: stur            x0, [fp, #-0x20]
    // 0x13c11dc: r1 = Instance_Color
    //     0x13c11dc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13c11e0: d0 = 0.700000
    //     0x13c11e0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13c11e4: ldr             d0, [x17, #0xf48]
    // 0x13c11e8: r0 = withOpacity()
    //     0x13c11e8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13c11ec: r16 = 21.000000
    //     0x13c11ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x13c11f0: ldr             x16, [x16, #0x9b0]
    // 0x13c11f4: stp             x16, x0, [SP]
    // 0x13c11f8: ldur            x1, [fp, #-0x20]
    // 0x13c11fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13c11fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13c1200: ldr             x4, [x4, #0x9b8]
    // 0x13c1204: r0 = copyWith()
    //     0x13c1204: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13c1208: stur            x0, [fp, #-0x20]
    // 0x13c120c: r0 = Text()
    //     0x13c120c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13c1210: mov             x1, x0
    // 0x13c1214: ldur            x0, [fp, #-0x10]
    // 0x13c1218: stur            x1, [fp, #-0x28]
    // 0x13c121c: StoreField: r1->field_b = r0
    //     0x13c121c: stur            w0, [x1, #0xb]
    // 0x13c1220: ldur            x0, [fp, #-0x20]
    // 0x13c1224: StoreField: r1->field_13 = r0
    //     0x13c1224: stur            w0, [x1, #0x13]
    // 0x13c1228: r0 = SvgPicture()
    //     0x13c1228: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x13c122c: stur            x0, [fp, #-0x10]
    // 0x13c1230: r16 = "return order"
    //     0x13c1230: add             x16, PP, #0x38, lsl #12  ; [pp+0x38c78] "return order"
    //     0x13c1234: ldr             x16, [x16, #0xc78]
    // 0x13c1238: r30 = Instance_BoxFit
    //     0x13c1238: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x13c123c: ldr             lr, [lr, #0xb18]
    // 0x13c1240: stp             lr, x16, [SP]
    // 0x13c1244: mov             x1, x0
    // 0x13c1248: r2 = "assets/images/secure_icon.svg"
    //     0x13c1248: add             x2, PP, #0x38, lsl #12  ; [pp+0x38c80] "assets/images/secure_icon.svg"
    //     0x13c124c: ldr             x2, [x2, #0xc80]
    // 0x13c1250: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0x13c1250: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0x13c1254: ldr             x4, [x4, #0xb28]
    // 0x13c1258: r0 = SvgPicture.asset()
    //     0x13c1258: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x13c125c: r1 = Null
    //     0x13c125c: mov             x1, NULL
    // 0x13c1260: r2 = 4
    //     0x13c1260: movz            x2, #0x4
    // 0x13c1264: r0 = AllocateArray()
    //     0x13c1264: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c1268: mov             x2, x0
    // 0x13c126c: ldur            x0, [fp, #-0x28]
    // 0x13c1270: stur            x2, [fp, #-0x20]
    // 0x13c1274: StoreField: r2->field_f = r0
    //     0x13c1274: stur            w0, [x2, #0xf]
    // 0x13c1278: ldur            x0, [fp, #-0x10]
    // 0x13c127c: StoreField: r2->field_13 = r0
    //     0x13c127c: stur            w0, [x2, #0x13]
    // 0x13c1280: r1 = <Widget>
    //     0x13c1280: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13c1284: r0 = AllocateGrowableArray()
    //     0x13c1284: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13c1288: mov             x1, x0
    // 0x13c128c: ldur            x0, [fp, #-0x20]
    // 0x13c1290: stur            x1, [fp, #-0x10]
    // 0x13c1294: StoreField: r1->field_f = r0
    //     0x13c1294: stur            w0, [x1, #0xf]
    // 0x13c1298: r0 = 4
    //     0x13c1298: movz            x0, #0x4
    // 0x13c129c: StoreField: r1->field_b = r0
    //     0x13c129c: stur            w0, [x1, #0xb]
    // 0x13c12a0: r0 = Row()
    //     0x13c12a0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13c12a4: mov             x1, x0
    // 0x13c12a8: r0 = Instance_Axis
    //     0x13c12a8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13c12ac: stur            x1, [fp, #-0x20]
    // 0x13c12b0: StoreField: r1->field_f = r0
    //     0x13c12b0: stur            w0, [x1, #0xf]
    // 0x13c12b4: r0 = Instance_MainAxisAlignment
    //     0x13c12b4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x13c12b8: ldr             x0, [x0, #0xa8]
    // 0x13c12bc: StoreField: r1->field_13 = r0
    //     0x13c12bc: stur            w0, [x1, #0x13]
    // 0x13c12c0: r0 = Instance_MainAxisSize
    //     0x13c12c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13c12c4: ldr             x0, [x0, #0xa10]
    // 0x13c12c8: ArrayStore: r1[0] = r0  ; List_4
    //     0x13c12c8: stur            w0, [x1, #0x17]
    // 0x13c12cc: r0 = Instance_CrossAxisAlignment
    //     0x13c12cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13c12d0: ldr             x0, [x0, #0xa18]
    // 0x13c12d4: StoreField: r1->field_1b = r0
    //     0x13c12d4: stur            w0, [x1, #0x1b]
    // 0x13c12d8: r0 = Instance_VerticalDirection
    //     0x13c12d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13c12dc: ldr             x0, [x0, #0xa20]
    // 0x13c12e0: StoreField: r1->field_23 = r0
    //     0x13c12e0: stur            w0, [x1, #0x23]
    // 0x13c12e4: r0 = Instance_Clip
    //     0x13c12e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13c12e8: ldr             x0, [x0, #0x38]
    // 0x13c12ec: StoreField: r1->field_2b = r0
    //     0x13c12ec: stur            w0, [x1, #0x2b]
    // 0x13c12f0: StoreField: r1->field_2f = rZR
    //     0x13c12f0: stur            xzr, [x1, #0x2f]
    // 0x13c12f4: ldur            x0, [fp, #-0x10]
    // 0x13c12f8: StoreField: r1->field_b = r0
    //     0x13c12f8: stur            w0, [x1, #0xb]
    // 0x13c12fc: r0 = Padding()
    //     0x13c12fc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13c1300: mov             x2, x0
    // 0x13c1304: r0 = Instance_EdgeInsets
    //     0x13c1304: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0x13c1308: ldr             x0, [x0, #0x240]
    // 0x13c130c: stur            x2, [fp, #-0x10]
    // 0x13c1310: StoreField: r2->field_f = r0
    //     0x13c1310: stur            w0, [x2, #0xf]
    // 0x13c1314: ldur            x0, [fp, #-0x20]
    // 0x13c1318: StoreField: r2->field_b = r0
    //     0x13c1318: stur            w0, [x2, #0xb]
    // 0x13c131c: ldur            x0, [fp, #-8]
    // 0x13c1320: LoadField: r1 = r0->field_f
    //     0x13c1320: ldur            w1, [x0, #0xf]
    // 0x13c1324: DecompressPointer r1
    //     0x13c1324: add             x1, x1, HEAP, lsl #32
    // 0x13c1328: r0 = controller()
    //     0x13c1328: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c132c: LoadField: r1 = r0->field_53
    //     0x13c132c: ldur            w1, [x0, #0x53]
    // 0x13c1330: DecompressPointer r1
    //     0x13c1330: add             x1, x1, HEAP, lsl #32
    // 0x13c1334: r0 = value()
    //     0x13c1334: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c1338: LoadField: r1 = r0->field_1f
    //     0x13c1338: ldur            w1, [x0, #0x1f]
    // 0x13c133c: DecompressPointer r1
    //     0x13c133c: add             x1, x1, HEAP, lsl #32
    // 0x13c1340: cmp             w1, NULL
    // 0x13c1344: b.ne            #0x13c135c
    // 0x13c1348: r1 = <PaymentOptions>
    //     0x13c1348: add             x1, PP, #0x22, lsl #12  ; [pp+0x225a8] TypeArguments: <PaymentOptions>
    //     0x13c134c: ldr             x1, [x1, #0x5a8]
    // 0x13c1350: r2 = 0
    //     0x13c1350: movz            x2, #0
    // 0x13c1354: r0 = AllocateArray()
    //     0x13c1354: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c1358: b               #0x13c1360
    // 0x13c135c: mov             x0, x1
    // 0x13c1360: ldur            x2, [fp, #-8]
    // 0x13c1364: ldur            x3, [fp, #-0x18]
    // 0x13c1368: ldur            x1, [fp, #-0x10]
    // 0x13c136c: r4 = LoadClassIdInstr(r0)
    //     0x13c136c: ldur            x4, [x0, #-1]
    //     0x13c1370: ubfx            x4, x4, #0xc, #0x14
    // 0x13c1374: str             x0, [SP]
    // 0x13c1378: mov             x0, x4
    // 0x13c137c: r0 = GDT[cid_x0 + 0xc898]()
    //     0x13c137c: movz            x17, #0xc898
    //     0x13c1380: add             lr, x0, x17
    //     0x13c1384: ldr             lr, [x21, lr, lsl #3]
    //     0x13c1388: blr             lr
    // 0x13c138c: r3 = LoadInt32Instr(r0)
    //     0x13c138c: sbfx            x3, x0, #1, #0x1f
    // 0x13c1390: ldur            x2, [fp, #-8]
    // 0x13c1394: stur            x3, [fp, #-0x68]
    // 0x13c1398: r1 = Function '<anonymous closure>':.
    //     0x13c1398: add             x1, PP, #0x46, lsl #12  ; [pp+0x466b8] AnonymousClosure: (0x13c1534), in [package:customer_app/app/presentation/views/basic/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::body (0x13bf888)
    //     0x13c139c: ldr             x1, [x1, #0x6b8]
    // 0x13c13a0: r0 = AllocateClosure()
    //     0x13c13a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13c13a4: r1 = Function '<anonymous closure>':.
    //     0x13c13a4: add             x1, PP, #0x46, lsl #12  ; [pp+0x466c0] AnonymousClosure: (0xa928e0), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14fc2bc)
    //     0x13c13a8: ldr             x1, [x1, #0x6c0]
    // 0x13c13ac: r2 = Null
    //     0x13c13ac: mov             x2, NULL
    // 0x13c13b0: stur            x0, [fp, #-0x20]
    // 0x13c13b4: r0 = AllocateClosure()
    //     0x13c13b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13c13b8: stur            x0, [fp, #-0x28]
    // 0x13c13bc: r0 = ListView()
    //     0x13c13bc: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x13c13c0: stur            x0, [fp, #-0x30]
    // 0x13c13c4: r16 = true
    //     0x13c13c4: add             x16, NULL, #0x20  ; true
    // 0x13c13c8: r30 = false
    //     0x13c13c8: add             lr, NULL, #0x30  ; false
    // 0x13c13cc: stp             lr, x16, [SP]
    // 0x13c13d0: mov             x1, x0
    // 0x13c13d4: ldur            x2, [fp, #-0x20]
    // 0x13c13d8: ldur            x3, [fp, #-0x68]
    // 0x13c13dc: ldur            x5, [fp, #-0x28]
    // 0x13c13e0: r4 = const [0, 0x6, 0x2, 0x4, primary, 0x5, shrinkWrap, 0x4, null]
    //     0x13c13e0: add             x4, PP, #0x38, lsl #12  ; [pp+0x38c98] List(9) [0, 0x6, 0x2, 0x4, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0x13c13e4: ldr             x4, [x4, #0xc98]
    // 0x13c13e8: r0 = ListView.separated()
    //     0x13c13e8: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x13c13ec: ldur            x0, [fp, #-8]
    // 0x13c13f0: LoadField: r1 = r0->field_f
    //     0x13c13f0: ldur            w1, [x0, #0xf]
    // 0x13c13f4: DecompressPointer r1
    //     0x13c13f4: add             x1, x1, HEAP, lsl #32
    // 0x13c13f8: r0 = controller()
    //     0x13c13f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c13fc: LoadField: r1 = r0->field_7b
    //     0x13c13fc: ldur            w1, [x0, #0x7b]
    // 0x13c1400: DecompressPointer r1
    //     0x13c1400: add             x1, x1, HEAP, lsl #32
    // 0x13c1404: r0 = value()
    //     0x13c1404: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c1408: stur            x0, [fp, #-8]
    // 0x13c140c: r0 = PaymentTrustMarkers()
    //     0x13c140c: bl              #0xbc7fe4  ; AllocatePaymentTrustMarkersStub -> PaymentTrustMarkers (size=0x14)
    // 0x13c1410: mov             x1, x0
    // 0x13c1414: r0 = true
    //     0x13c1414: add             x0, NULL, #0x20  ; true
    // 0x13c1418: stur            x1, [fp, #-0x20]
    // 0x13c141c: StoreField: r1->field_b = r0
    //     0x13c141c: stur            w0, [x1, #0xb]
    // 0x13c1420: ldur            x0, [fp, #-8]
    // 0x13c1424: StoreField: r1->field_f = r0
    //     0x13c1424: stur            w0, [x1, #0xf]
    // 0x13c1428: r0 = Padding()
    //     0x13c1428: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13c142c: mov             x3, x0
    // 0x13c1430: r0 = Instance_EdgeInsets
    //     0x13c1430: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0x13c1434: ldr             x0, [x0, #0x100]
    // 0x13c1438: stur            x3, [fp, #-8]
    // 0x13c143c: StoreField: r3->field_f = r0
    //     0x13c143c: stur            w0, [x3, #0xf]
    // 0x13c1440: ldur            x0, [fp, #-0x20]
    // 0x13c1444: StoreField: r3->field_b = r0
    //     0x13c1444: stur            w0, [x3, #0xb]
    // 0x13c1448: r1 = Null
    //     0x13c1448: mov             x1, NULL
    // 0x13c144c: r2 = 8
    //     0x13c144c: movz            x2, #0x8
    // 0x13c1450: r0 = AllocateArray()
    //     0x13c1450: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c1454: mov             x2, x0
    // 0x13c1458: ldur            x0, [fp, #-0x18]
    // 0x13c145c: stur            x2, [fp, #-0x20]
    // 0x13c1460: StoreField: r2->field_f = r0
    //     0x13c1460: stur            w0, [x2, #0xf]
    // 0x13c1464: ldur            x0, [fp, #-0x10]
    // 0x13c1468: StoreField: r2->field_13 = r0
    //     0x13c1468: stur            w0, [x2, #0x13]
    // 0x13c146c: ldur            x0, [fp, #-0x30]
    // 0x13c1470: ArrayStore: r2[0] = r0  ; List_4
    //     0x13c1470: stur            w0, [x2, #0x17]
    // 0x13c1474: ldur            x0, [fp, #-8]
    // 0x13c1478: StoreField: r2->field_1b = r0
    //     0x13c1478: stur            w0, [x2, #0x1b]
    // 0x13c147c: r1 = <Widget>
    //     0x13c147c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13c1480: r0 = AllocateGrowableArray()
    //     0x13c1480: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13c1484: mov             x1, x0
    // 0x13c1488: ldur            x0, [fp, #-0x20]
    // 0x13c148c: stur            x1, [fp, #-8]
    // 0x13c1490: StoreField: r1->field_f = r0
    //     0x13c1490: stur            w0, [x1, #0xf]
    // 0x13c1494: r0 = 8
    //     0x13c1494: movz            x0, #0x8
    // 0x13c1498: StoreField: r1->field_b = r0
    //     0x13c1498: stur            w0, [x1, #0xb]
    // 0x13c149c: r0 = ListView()
    //     0x13c149c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x13c14a0: stur            x0, [fp, #-0x10]
    // 0x13c14a4: r16 = Instance_EdgeInsets
    //     0x13c14a4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x13c14a8: ldr             x16, [x16, #0x668]
    // 0x13c14ac: r30 = true
    //     0x13c14ac: add             lr, NULL, #0x20  ; true
    // 0x13c14b0: stp             lr, x16, [SP, #0x10]
    // 0x13c14b4: r16 = false
    //     0x13c14b4: add             x16, NULL, #0x30  ; false
    // 0x13c14b8: r30 = Instance_RangeMaintainingScrollPhysics
    //     0x13c14b8: add             lr, PP, #0x3a, lsl #12  ; [pp+0x3afa0] Obj!RangeMaintainingScrollPhysics@d55901
    //     0x13c14bc: ldr             lr, [lr, #0xfa0]
    // 0x13c14c0: stp             lr, x16, [SP]
    // 0x13c14c4: mov             x1, x0
    // 0x13c14c8: ldur            x2, [fp, #-8]
    // 0x13c14cc: r4 = const [0, 0x6, 0x4, 0x2, padding, 0x2, physics, 0x5, primary, 0x4, shrinkWrap, 0x3, null]
    //     0x13c14cc: add             x4, PP, #0x40, lsl #12  ; [pp+0x409e8] List(13) [0, 0x6, 0x4, 0x2, "padding", 0x2, "physics", 0x5, "primary", 0x4, "shrinkWrap", 0x3, Null]
    //     0x13c14d0: ldr             x4, [x4, #0x9e8]
    // 0x13c14d4: r0 = ListView()
    //     0x13c14d4: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0x13c14d8: ldur            x0, [fp, #-0x10]
    // 0x13c14dc: LeaveFrame
    //     0x13c14dc: mov             SP, fp
    //     0x13c14e0: ldp             fp, lr, [SP], #0x10
    // 0x13c14e4: ret
    //     0x13c14e4: ret             
    // 0x13c14e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13c14e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13c14ec: b               #0x13bf914
    // 0x13c14f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c14f0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c14f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c14f4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c14f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c14f8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c14fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c14fc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c1500: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c1500: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c1504: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c1504: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c1508: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c1508: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c150c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c150c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c1510: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c1510: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c1514: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c1514: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c1518: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c1518: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c151c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c151c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c1520: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c1520: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c1524: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c1524: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c1528: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c1528: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c152c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c152c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13c1530: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c1530: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x13c1534, size: 0xd4
    // 0x13c1534: EnterFrame
    //     0x13c1534: stp             fp, lr, [SP, #-0x10]!
    //     0x13c1538: mov             fp, SP
    // 0x13c153c: AllocStack(0x8)
    //     0x13c153c: sub             SP, SP, #8
    // 0x13c1540: SetupParameters()
    //     0x13c1540: ldr             x0, [fp, #0x20]
    //     0x13c1544: ldur            w1, [x0, #0x17]
    //     0x13c1548: add             x1, x1, HEAP, lsl #32
    // 0x13c154c: CheckStackOverflow
    //     0x13c154c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13c1550: cmp             SP, x16
    //     0x13c1554: b.ls            #0x13c15fc
    // 0x13c1558: LoadField: r0 = r1->field_f
    //     0x13c1558: ldur            w0, [x1, #0xf]
    // 0x13c155c: DecompressPointer r0
    //     0x13c155c: add             x0, x0, HEAP, lsl #32
    // 0x13c1560: mov             x1, x0
    // 0x13c1564: stur            x0, [fp, #-8]
    // 0x13c1568: r0 = controller()
    //     0x13c1568: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c156c: LoadField: r1 = r0->field_53
    //     0x13c156c: ldur            w1, [x0, #0x53]
    // 0x13c1570: DecompressPointer r1
    //     0x13c1570: add             x1, x1, HEAP, lsl #32
    // 0x13c1574: r0 = value()
    //     0x13c1574: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c1578: LoadField: r2 = r0->field_1f
    //     0x13c1578: ldur            w2, [x0, #0x1f]
    // 0x13c157c: DecompressPointer r2
    //     0x13c157c: add             x2, x2, HEAP, lsl #32
    // 0x13c1580: cmp             w2, NULL
    // 0x13c1584: b.ne            #0x13c1594
    // 0x13c1588: ldr             x3, [fp, #0x10]
    // 0x13c158c: r2 = Null
    //     0x13c158c: mov             x2, NULL
    // 0x13c1590: b               #0x13c15d4
    // 0x13c1594: ldr             x3, [fp, #0x10]
    // 0x13c1598: LoadField: r0 = r2->field_b
    //     0x13c1598: ldur            w0, [x2, #0xb]
    // 0x13c159c: r4 = LoadInt32Instr(r3)
    //     0x13c159c: sbfx            x4, x3, #1, #0x1f
    //     0x13c15a0: tbz             w3, #0, #0x13c15a8
    //     0x13c15a4: ldur            x4, [x3, #7]
    // 0x13c15a8: r1 = LoadInt32Instr(r0)
    //     0x13c15a8: sbfx            x1, x0, #1, #0x1f
    // 0x13c15ac: mov             x0, x1
    // 0x13c15b0: mov             x1, x4
    // 0x13c15b4: cmp             x1, x0
    // 0x13c15b8: b.hs            #0x13c1604
    // 0x13c15bc: LoadField: r0 = r2->field_f
    //     0x13c15bc: ldur            w0, [x2, #0xf]
    // 0x13c15c0: DecompressPointer r0
    //     0x13c15c0: add             x0, x0, HEAP, lsl #32
    // 0x13c15c4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x13c15c4: add             x16, x0, x4, lsl #2
    //     0x13c15c8: ldur            w1, [x16, #0xf]
    // 0x13c15cc: DecompressPointer r1
    //     0x13c15cc: add             x1, x1, HEAP, lsl #32
    // 0x13c15d0: mov             x2, x1
    // 0x13c15d4: r0 = LoadInt32Instr(r3)
    //     0x13c15d4: sbfx            x0, x3, #1, #0x1f
    //     0x13c15d8: tbz             w3, #0, #0x13c15e0
    //     0x13c15dc: ldur            x0, [x3, #7]
    // 0x13c15e0: ldur            x1, [fp, #-8]
    // 0x13c15e4: mov             x3, x0
    // 0x13c15e8: ldr             x5, [fp, #0x18]
    // 0x13c15ec: r0 = basicThemePaymentMethodCard()
    //     0x13c15ec: bl              #0x13c1608  ; [package:customer_app/app/presentation/views/basic/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::basicThemePaymentMethodCard
    // 0x13c15f0: LeaveFrame
    //     0x13c15f0: mov             SP, fp
    //     0x13c15f4: ldp             fp, lr, [SP], #0x10
    // 0x13c15f8: ret
    //     0x13c15f8: ret             
    // 0x13c15fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13c15fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13c1600: b               #0x13c1558
    // 0x13c1604: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13c1604: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ basicThemePaymentMethodCard(/* No info */) {
    // ** addr: 0x13c1608, size: 0x90
    // 0x13c1608: EnterFrame
    //     0x13c1608: stp             fp, lr, [SP, #-0x10]!
    //     0x13c160c: mov             fp, SP
    // 0x13c1610: AllocStack(0x28)
    //     0x13c1610: sub             SP, SP, #0x28
    // 0x13c1614: SetupParameters(ExchangeCheckoutOnlinePaymentMethod this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x13c1614: stur            x1, [fp, #-8]
    //     0x13c1618: stur            x2, [fp, #-0x10]
    //     0x13c161c: stur            x3, [fp, #-0x18]
    //     0x13c1620: stur            x5, [fp, #-0x20]
    // 0x13c1624: r1 = 4
    //     0x13c1624: movz            x1, #0x4
    // 0x13c1628: r0 = AllocateContext()
    //     0x13c1628: bl              #0x16f6108  ; AllocateContextStub
    // 0x13c162c: mov             x2, x0
    // 0x13c1630: ldur            x0, [fp, #-8]
    // 0x13c1634: stur            x2, [fp, #-0x28]
    // 0x13c1638: StoreField: r2->field_f = r0
    //     0x13c1638: stur            w0, [x2, #0xf]
    // 0x13c163c: ldur            x0, [fp, #-0x10]
    // 0x13c1640: StoreField: r2->field_13 = r0
    //     0x13c1640: stur            w0, [x2, #0x13]
    // 0x13c1644: ldur            x3, [fp, #-0x18]
    // 0x13c1648: r0 = BoxInt64Instr(r3)
    //     0x13c1648: sbfiz           x0, x3, #1, #0x1f
    //     0x13c164c: cmp             x3, x0, asr #1
    //     0x13c1650: b.eq            #0x13c165c
    //     0x13c1654: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x13c1658: stur            x3, [x0, #7]
    // 0x13c165c: ArrayStore: r2[0] = r0  ; List_4
    //     0x13c165c: stur            w0, [x2, #0x17]
    // 0x13c1660: ldur            x0, [fp, #-0x20]
    // 0x13c1664: StoreField: r2->field_1b = r0
    //     0x13c1664: stur            w0, [x2, #0x1b]
    // 0x13c1668: r0 = Obx()
    //     0x13c1668: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13c166c: ldur            x2, [fp, #-0x28]
    // 0x13c1670: r1 = Function '<anonymous closure>':.
    //     0x13c1670: add             x1, PP, #0x46, lsl #12  ; [pp+0x466c8] AnonymousClosure: (0x13c1698), in [package:customer_app/app/presentation/views/basic/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::basicThemePaymentMethodCard (0x13c1608)
    //     0x13c1674: ldr             x1, [x1, #0x6c8]
    // 0x13c1678: stur            x0, [fp, #-8]
    // 0x13c167c: r0 = AllocateClosure()
    //     0x13c167c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13c1680: mov             x1, x0
    // 0x13c1684: ldur            x0, [fp, #-8]
    // 0x13c1688: StoreField: r0->field_b = r1
    //     0x13c1688: stur            w1, [x0, #0xb]
    // 0x13c168c: LeaveFrame
    //     0x13c168c: mov             SP, fp
    //     0x13c1690: ldp             fp, lr, [SP], #0x10
    // 0x13c1694: ret
    //     0x13c1694: ret             
  }
  [closure] InkWell <anonymous closure>(dynamic) {
    // ** addr: 0x13c1698, size: 0x48c
    // 0x13c1698: EnterFrame
    //     0x13c1698: stp             fp, lr, [SP, #-0x10]!
    //     0x13c169c: mov             fp, SP
    // 0x13c16a0: AllocStack(0x48)
    //     0x13c16a0: sub             SP, SP, #0x48
    // 0x13c16a4: SetupParameters()
    //     0x13c16a4: ldr             x0, [fp, #0x10]
    //     0x13c16a8: ldur            w2, [x0, #0x17]
    //     0x13c16ac: add             x2, x2, HEAP, lsl #32
    //     0x13c16b0: stur            x2, [fp, #-8]
    // 0x13c16b4: CheckStackOverflow
    //     0x13c16b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13c16b8: cmp             SP, x16
    //     0x13c16bc: b.ls            #0x13c1b1c
    // 0x13c16c0: r0 = Radius()
    //     0x13c16c0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x13c16c4: d0 = 12.000000
    //     0x13c16c4: fmov            d0, #12.00000000
    // 0x13c16c8: stur            x0, [fp, #-0x10]
    // 0x13c16cc: StoreField: r0->field_7 = d0
    //     0x13c16cc: stur            d0, [x0, #7]
    // 0x13c16d0: StoreField: r0->field_f = d0
    //     0x13c16d0: stur            d0, [x0, #0xf]
    // 0x13c16d4: r0 = BorderRadius()
    //     0x13c16d4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x13c16d8: mov             x2, x0
    // 0x13c16dc: ldur            x0, [fp, #-0x10]
    // 0x13c16e0: stur            x2, [fp, #-0x18]
    // 0x13c16e4: StoreField: r2->field_7 = r0
    //     0x13c16e4: stur            w0, [x2, #7]
    // 0x13c16e8: StoreField: r2->field_b = r0
    //     0x13c16e8: stur            w0, [x2, #0xb]
    // 0x13c16ec: StoreField: r2->field_f = r0
    //     0x13c16ec: stur            w0, [x2, #0xf]
    // 0x13c16f0: StoreField: r2->field_13 = r0
    //     0x13c16f0: stur            w0, [x2, #0x13]
    // 0x13c16f4: ldur            x0, [fp, #-8]
    // 0x13c16f8: LoadField: r1 = r0->field_1b
    //     0x13c16f8: ldur            w1, [x0, #0x1b]
    // 0x13c16fc: DecompressPointer r1
    //     0x13c16fc: add             x1, x1, HEAP, lsl #32
    // 0x13c1700: r0 = of()
    //     0x13c1700: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c1704: LoadField: r1 = r0->field_5b
    //     0x13c1704: ldur            w1, [x0, #0x5b]
    // 0x13c1708: DecompressPointer r1
    //     0x13c1708: add             x1, x1, HEAP, lsl #32
    // 0x13c170c: r0 = LoadClassIdInstr(r1)
    //     0x13c170c: ldur            x0, [x1, #-1]
    //     0x13c1710: ubfx            x0, x0, #0xc, #0x14
    // 0x13c1714: d0 = 0.100000
    //     0x13c1714: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x13c1718: r0 = GDT[cid_x0 + -0xffa]()
    //     0x13c1718: sub             lr, x0, #0xffa
    //     0x13c171c: ldr             lr, [x21, lr, lsl #3]
    //     0x13c1720: blr             lr
    // 0x13c1724: mov             x2, x0
    // 0x13c1728: r1 = Null
    //     0x13c1728: mov             x1, NULL
    // 0x13c172c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x13c172c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x13c1730: r0 = Border.all()
    //     0x13c1730: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x13c1734: stur            x0, [fp, #-0x10]
    // 0x13c1738: r0 = BoxDecoration()
    //     0x13c1738: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x13c173c: mov             x2, x0
    // 0x13c1740: ldur            x0, [fp, #-0x10]
    // 0x13c1744: stur            x2, [fp, #-0x20]
    // 0x13c1748: StoreField: r2->field_f = r0
    //     0x13c1748: stur            w0, [x2, #0xf]
    // 0x13c174c: ldur            x0, [fp, #-0x18]
    // 0x13c1750: StoreField: r2->field_13 = r0
    //     0x13c1750: stur            w0, [x2, #0x13]
    // 0x13c1754: r0 = Instance_BoxShape
    //     0x13c1754: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13c1758: ldr             x0, [x0, #0x80]
    // 0x13c175c: StoreField: r2->field_23 = r0
    //     0x13c175c: stur            w0, [x2, #0x23]
    // 0x13c1760: ldur            x3, [fp, #-8]
    // 0x13c1764: LoadField: r1 = r3->field_f
    //     0x13c1764: ldur            w1, [x3, #0xf]
    // 0x13c1768: DecompressPointer r1
    //     0x13c1768: add             x1, x1, HEAP, lsl #32
    // 0x13c176c: r0 = controller()
    //     0x13c176c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c1770: LoadField: r1 = r0->field_77
    //     0x13c1770: ldur            w1, [x0, #0x77]
    // 0x13c1774: DecompressPointer r1
    //     0x13c1774: add             x1, x1, HEAP, lsl #32
    // 0x13c1778: r0 = value()
    //     0x13c1778: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c177c: ldur            x2, [fp, #-8]
    // 0x13c1780: LoadField: r1 = r2->field_13
    //     0x13c1780: ldur            w1, [x2, #0x13]
    // 0x13c1784: DecompressPointer r1
    //     0x13c1784: add             x1, x1, HEAP, lsl #32
    // 0x13c1788: cmp             w1, NULL
    // 0x13c178c: b.ne            #0x13c1798
    // 0x13c1790: r1 = Null
    //     0x13c1790: mov             x1, NULL
    // 0x13c1794: b               #0x13c17a4
    // 0x13c1798: LoadField: r3 = r1->field_f
    //     0x13c1798: ldur            w3, [x1, #0xf]
    // 0x13c179c: DecompressPointer r3
    //     0x13c179c: add             x3, x3, HEAP, lsl #32
    // 0x13c17a0: mov             x1, x3
    // 0x13c17a4: r3 = 60
    //     0x13c17a4: movz            x3, #0x3c
    // 0x13c17a8: branchIfSmi(r0, 0x13c17b4)
    //     0x13c17a8: tbz             w0, #0, #0x13c17b4
    // 0x13c17ac: r3 = LoadClassIdInstr(r0)
    //     0x13c17ac: ldur            x3, [x0, #-1]
    //     0x13c17b0: ubfx            x3, x3, #0xc, #0x14
    // 0x13c17b4: stp             x1, x0, [SP]
    // 0x13c17b8: mov             x0, x3
    // 0x13c17bc: mov             lr, x0
    // 0x13c17c0: ldr             lr, [x21, lr, lsl #3]
    // 0x13c17c4: blr             lr
    // 0x13c17c8: tbnz            w0, #4, #0x13c1820
    // 0x13c17cc: ldur            x2, [fp, #-8]
    // 0x13c17d0: LoadField: r1 = r2->field_f
    //     0x13c17d0: ldur            w1, [x2, #0xf]
    // 0x13c17d4: DecompressPointer r1
    //     0x13c17d4: add             x1, x1, HEAP, lsl #32
    // 0x13c17d8: r0 = controller()
    //     0x13c17d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c17dc: LoadField: r1 = r0->field_97
    //     0x13c17dc: ldur            w1, [x0, #0x97]
    // 0x13c17e0: DecompressPointer r1
    //     0x13c17e0: add             x1, x1, HEAP, lsl #32
    // 0x13c17e4: r0 = value()
    //     0x13c17e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c17e8: ldur            x2, [fp, #-8]
    // 0x13c17ec: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x13c17ec: ldur            w1, [x2, #0x17]
    // 0x13c17f0: DecompressPointer r1
    //     0x13c17f0: add             x1, x1, HEAP, lsl #32
    // 0x13c17f4: r3 = LoadInt32Instr(r0)
    //     0x13c17f4: sbfx            x3, x0, #1, #0x1f
    //     0x13c17f8: tbz             w0, #0, #0x13c1800
    //     0x13c17fc: ldur            x3, [x0, #7]
    // 0x13c1800: r0 = LoadInt32Instr(r1)
    //     0x13c1800: sbfx            x0, x1, #1, #0x1f
    //     0x13c1804: tbz             w1, #0, #0x13c180c
    //     0x13c1808: ldur            x0, [x1, #7]
    // 0x13c180c: cmp             x3, x0
    // 0x13c1810: b.ne            #0x13c1824
    // 0x13c1814: r0 = Instance_IconData
    //     0x13c1814: add             x0, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0x13c1818: ldr             x0, [x0, #0x30]
    // 0x13c181c: b               #0x13c182c
    // 0x13c1820: ldur            x2, [fp, #-8]
    // 0x13c1824: r0 = Instance_IconData
    //     0x13c1824: add             x0, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0x13c1828: ldr             x0, [x0, #0x38]
    // 0x13c182c: stur            x0, [fp, #-0x10]
    // 0x13c1830: LoadField: r1 = r2->field_1b
    //     0x13c1830: ldur            w1, [x2, #0x1b]
    // 0x13c1834: DecompressPointer r1
    //     0x13c1834: add             x1, x1, HEAP, lsl #32
    // 0x13c1838: r0 = of()
    //     0x13c1838: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c183c: LoadField: r1 = r0->field_5b
    //     0x13c183c: ldur            w1, [x0, #0x5b]
    // 0x13c1840: DecompressPointer r1
    //     0x13c1840: add             x1, x1, HEAP, lsl #32
    // 0x13c1844: stur            x1, [fp, #-0x18]
    // 0x13c1848: r0 = Icon()
    //     0x13c1848: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x13c184c: mov             x1, x0
    // 0x13c1850: ldur            x0, [fp, #-0x10]
    // 0x13c1854: stur            x1, [fp, #-0x28]
    // 0x13c1858: StoreField: r1->field_b = r0
    //     0x13c1858: stur            w0, [x1, #0xb]
    // 0x13c185c: ldur            x0, [fp, #-0x18]
    // 0x13c1860: StoreField: r1->field_23 = r0
    //     0x13c1860: stur            w0, [x1, #0x23]
    // 0x13c1864: ldur            x2, [fp, #-8]
    // 0x13c1868: LoadField: r0 = r2->field_13
    //     0x13c1868: ldur            w0, [x2, #0x13]
    // 0x13c186c: DecompressPointer r0
    //     0x13c186c: add             x0, x0, HEAP, lsl #32
    // 0x13c1870: cmp             w0, NULL
    // 0x13c1874: b.ne            #0x13c1880
    // 0x13c1878: r0 = Null
    //     0x13c1878: mov             x0, NULL
    // 0x13c187c: b               #0x13c188c
    // 0x13c1880: LoadField: r3 = r0->field_7
    //     0x13c1880: ldur            w3, [x0, #7]
    // 0x13c1884: DecompressPointer r3
    //     0x13c1884: add             x3, x3, HEAP, lsl #32
    // 0x13c1888: mov             x0, x3
    // 0x13c188c: cmp             w0, NULL
    // 0x13c1890: b.ne            #0x13c1898
    // 0x13c1894: r0 = ""
    //     0x13c1894: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c1898: stur            x0, [fp, #-0x10]
    // 0x13c189c: r0 = CachedNetworkImage()
    //     0x13c189c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x13c18a0: stur            x0, [fp, #-0x18]
    // 0x13c18a4: r16 = 48.000000
    //     0x13c18a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x13c18a8: ldr             x16, [x16, #0xad8]
    // 0x13c18ac: r30 = 48.000000
    //     0x13c18ac: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x13c18b0: ldr             lr, [lr, #0xad8]
    // 0x13c18b4: stp             lr, x16, [SP]
    // 0x13c18b8: mov             x1, x0
    // 0x13c18bc: ldur            x2, [fp, #-0x10]
    // 0x13c18c0: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0x13c18c0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0x13c18c4: ldr             x4, [x4, #0x900]
    // 0x13c18c8: r0 = CachedNetworkImage()
    //     0x13c18c8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x13c18cc: r0 = Padding()
    //     0x13c18cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13c18d0: mov             x2, x0
    // 0x13c18d4: r0 = Instance_EdgeInsets
    //     0x13c18d4: add             x0, PP, #0x40, lsl #12  ; [pp+0x409f8] Obj!EdgeInsets@d59b41
    //     0x13c18d8: ldr             x0, [x0, #0x9f8]
    // 0x13c18dc: stur            x2, [fp, #-0x30]
    // 0x13c18e0: StoreField: r2->field_f = r0
    //     0x13c18e0: stur            w0, [x2, #0xf]
    // 0x13c18e4: ldur            x1, [fp, #-0x18]
    // 0x13c18e8: StoreField: r2->field_b = r1
    //     0x13c18e8: stur            w1, [x2, #0xb]
    // 0x13c18ec: ldur            x3, [fp, #-8]
    // 0x13c18f0: LoadField: r1 = r3->field_13
    //     0x13c18f0: ldur            w1, [x3, #0x13]
    // 0x13c18f4: DecompressPointer r1
    //     0x13c18f4: add             x1, x1, HEAP, lsl #32
    // 0x13c18f8: cmp             w1, NULL
    // 0x13c18fc: b.ne            #0x13c1908
    // 0x13c1900: r1 = Null
    //     0x13c1900: mov             x1, NULL
    // 0x13c1904: b               #0x13c1914
    // 0x13c1908: LoadField: r4 = r1->field_b
    //     0x13c1908: ldur            w4, [x1, #0xb]
    // 0x13c190c: DecompressPointer r4
    //     0x13c190c: add             x4, x4, HEAP, lsl #32
    // 0x13c1910: mov             x1, x4
    // 0x13c1914: cmp             w1, NULL
    // 0x13c1918: b.ne            #0x13c1924
    // 0x13c191c: r5 = ""
    //     0x13c191c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c1920: b               #0x13c1928
    // 0x13c1924: mov             x5, x1
    // 0x13c1928: ldur            x4, [fp, #-0x28]
    // 0x13c192c: stur            x5, [fp, #-0x10]
    // 0x13c1930: LoadField: r1 = r3->field_1b
    //     0x13c1930: ldur            w1, [x3, #0x1b]
    // 0x13c1934: DecompressPointer r1
    //     0x13c1934: add             x1, x1, HEAP, lsl #32
    // 0x13c1938: r0 = of()
    //     0x13c1938: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c193c: LoadField: r1 = r0->field_87
    //     0x13c193c: ldur            w1, [x0, #0x87]
    // 0x13c1940: DecompressPointer r1
    //     0x13c1940: add             x1, x1, HEAP, lsl #32
    // 0x13c1944: LoadField: r0 = r1->field_2b
    //     0x13c1944: ldur            w0, [x1, #0x2b]
    // 0x13c1948: DecompressPointer r0
    //     0x13c1948: add             x0, x0, HEAP, lsl #32
    // 0x13c194c: r16 = 14.000000
    //     0x13c194c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13c1950: ldr             x16, [x16, #0x1d8]
    // 0x13c1954: r30 = Instance_Color
    //     0x13c1954: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13c1958: stp             lr, x16, [SP]
    // 0x13c195c: mov             x1, x0
    // 0x13c1960: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13c1960: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13c1964: ldr             x4, [x4, #0xaa0]
    // 0x13c1968: r0 = copyWith()
    //     0x13c1968: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13c196c: stur            x0, [fp, #-0x18]
    // 0x13c1970: r0 = Text()
    //     0x13c1970: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13c1974: mov             x1, x0
    // 0x13c1978: ldur            x0, [fp, #-0x10]
    // 0x13c197c: stur            x1, [fp, #-0x38]
    // 0x13c1980: StoreField: r1->field_b = r0
    //     0x13c1980: stur            w0, [x1, #0xb]
    // 0x13c1984: ldur            x0, [fp, #-0x18]
    // 0x13c1988: StoreField: r1->field_13 = r0
    //     0x13c1988: stur            w0, [x1, #0x13]
    // 0x13c198c: r0 = Padding()
    //     0x13c198c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13c1990: mov             x3, x0
    // 0x13c1994: r0 = Instance_EdgeInsets
    //     0x13c1994: add             x0, PP, #0x40, lsl #12  ; [pp+0x409f8] Obj!EdgeInsets@d59b41
    //     0x13c1998: ldr             x0, [x0, #0x9f8]
    // 0x13c199c: stur            x3, [fp, #-0x10]
    // 0x13c19a0: StoreField: r3->field_f = r0
    //     0x13c19a0: stur            w0, [x3, #0xf]
    // 0x13c19a4: ldur            x0, [fp, #-0x38]
    // 0x13c19a8: StoreField: r3->field_b = r0
    //     0x13c19a8: stur            w0, [x3, #0xb]
    // 0x13c19ac: r1 = Null
    //     0x13c19ac: mov             x1, NULL
    // 0x13c19b0: r2 = 6
    //     0x13c19b0: movz            x2, #0x6
    // 0x13c19b4: r0 = AllocateArray()
    //     0x13c19b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c19b8: mov             x2, x0
    // 0x13c19bc: ldur            x0, [fp, #-0x28]
    // 0x13c19c0: stur            x2, [fp, #-0x18]
    // 0x13c19c4: StoreField: r2->field_f = r0
    //     0x13c19c4: stur            w0, [x2, #0xf]
    // 0x13c19c8: ldur            x0, [fp, #-0x30]
    // 0x13c19cc: StoreField: r2->field_13 = r0
    //     0x13c19cc: stur            w0, [x2, #0x13]
    // 0x13c19d0: ldur            x0, [fp, #-0x10]
    // 0x13c19d4: ArrayStore: r2[0] = r0  ; List_4
    //     0x13c19d4: stur            w0, [x2, #0x17]
    // 0x13c19d8: r1 = <Widget>
    //     0x13c19d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13c19dc: r0 = AllocateGrowableArray()
    //     0x13c19dc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13c19e0: mov             x1, x0
    // 0x13c19e4: ldur            x0, [fp, #-0x18]
    // 0x13c19e8: stur            x1, [fp, #-0x10]
    // 0x13c19ec: StoreField: r1->field_f = r0
    //     0x13c19ec: stur            w0, [x1, #0xf]
    // 0x13c19f0: r0 = 6
    //     0x13c19f0: movz            x0, #0x6
    // 0x13c19f4: StoreField: r1->field_b = r0
    //     0x13c19f4: stur            w0, [x1, #0xb]
    // 0x13c19f8: r0 = Row()
    //     0x13c19f8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13c19fc: mov             x1, x0
    // 0x13c1a00: r0 = Instance_Axis
    //     0x13c1a00: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13c1a04: stur            x1, [fp, #-0x18]
    // 0x13c1a08: StoreField: r1->field_f = r0
    //     0x13c1a08: stur            w0, [x1, #0xf]
    // 0x13c1a0c: r0 = Instance_MainAxisAlignment
    //     0x13c1a0c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13c1a10: ldr             x0, [x0, #0xa08]
    // 0x13c1a14: StoreField: r1->field_13 = r0
    //     0x13c1a14: stur            w0, [x1, #0x13]
    // 0x13c1a18: r0 = Instance_MainAxisSize
    //     0x13c1a18: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13c1a1c: ldr             x0, [x0, #0xa10]
    // 0x13c1a20: ArrayStore: r1[0] = r0  ; List_4
    //     0x13c1a20: stur            w0, [x1, #0x17]
    // 0x13c1a24: r0 = Instance_CrossAxisAlignment
    //     0x13c1a24: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13c1a28: ldr             x0, [x0, #0xa18]
    // 0x13c1a2c: StoreField: r1->field_1b = r0
    //     0x13c1a2c: stur            w0, [x1, #0x1b]
    // 0x13c1a30: r0 = Instance_VerticalDirection
    //     0x13c1a30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13c1a34: ldr             x0, [x0, #0xa20]
    // 0x13c1a38: StoreField: r1->field_23 = r0
    //     0x13c1a38: stur            w0, [x1, #0x23]
    // 0x13c1a3c: r0 = Instance_Clip
    //     0x13c1a3c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13c1a40: ldr             x0, [x0, #0x38]
    // 0x13c1a44: StoreField: r1->field_2b = r0
    //     0x13c1a44: stur            w0, [x1, #0x2b]
    // 0x13c1a48: StoreField: r1->field_2f = rZR
    //     0x13c1a48: stur            xzr, [x1, #0x2f]
    // 0x13c1a4c: ldur            x0, [fp, #-0x10]
    // 0x13c1a50: StoreField: r1->field_b = r0
    //     0x13c1a50: stur            w0, [x1, #0xb]
    // 0x13c1a54: r0 = Padding()
    //     0x13c1a54: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13c1a58: mov             x1, x0
    // 0x13c1a5c: r0 = Instance_EdgeInsets
    //     0x13c1a5c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x13c1a60: ldr             x0, [x0, #0x1f0]
    // 0x13c1a64: stur            x1, [fp, #-0x10]
    // 0x13c1a68: StoreField: r1->field_f = r0
    //     0x13c1a68: stur            w0, [x1, #0xf]
    // 0x13c1a6c: ldur            x0, [fp, #-0x18]
    // 0x13c1a70: StoreField: r1->field_b = r0
    //     0x13c1a70: stur            w0, [x1, #0xb]
    // 0x13c1a74: r0 = Container()
    //     0x13c1a74: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13c1a78: stur            x0, [fp, #-0x18]
    // 0x13c1a7c: ldur            x16, [fp, #-0x20]
    // 0x13c1a80: ldur            lr, [fp, #-0x10]
    // 0x13c1a84: stp             lr, x16, [SP]
    // 0x13c1a88: mov             x1, x0
    // 0x13c1a8c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x13c1a8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x13c1a90: ldr             x4, [x4, #0x88]
    // 0x13c1a94: r0 = Container()
    //     0x13c1a94: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13c1a98: r0 = Center()
    //     0x13c1a98: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x13c1a9c: mov             x1, x0
    // 0x13c1aa0: r0 = Instance_Alignment
    //     0x13c1aa0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x13c1aa4: ldr             x0, [x0, #0xb10]
    // 0x13c1aa8: stur            x1, [fp, #-0x10]
    // 0x13c1aac: StoreField: r1->field_f = r0
    //     0x13c1aac: stur            w0, [x1, #0xf]
    // 0x13c1ab0: ldur            x0, [fp, #-0x18]
    // 0x13c1ab4: StoreField: r1->field_b = r0
    //     0x13c1ab4: stur            w0, [x1, #0xb]
    // 0x13c1ab8: r0 = InkWell()
    //     0x13c1ab8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x13c1abc: mov             x3, x0
    // 0x13c1ac0: ldur            x0, [fp, #-0x10]
    // 0x13c1ac4: stur            x3, [fp, #-0x18]
    // 0x13c1ac8: StoreField: r3->field_b = r0
    //     0x13c1ac8: stur            w0, [x3, #0xb]
    // 0x13c1acc: ldur            x2, [fp, #-8]
    // 0x13c1ad0: r1 = Function '<anonymous closure>':.
    //     0x13c1ad0: add             x1, PP, #0x46, lsl #12  ; [pp+0x466d0] AnonymousClosure: (0x13c1b6c), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::lineThemePaymentMethodCard (0x13c1fc4)
    //     0x13c1ad4: ldr             x1, [x1, #0x6d0]
    // 0x13c1ad8: r0 = AllocateClosure()
    //     0x13c1ad8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13c1adc: mov             x1, x0
    // 0x13c1ae0: ldur            x0, [fp, #-0x18]
    // 0x13c1ae4: StoreField: r0->field_f = r1
    //     0x13c1ae4: stur            w1, [x0, #0xf]
    // 0x13c1ae8: r1 = true
    //     0x13c1ae8: add             x1, NULL, #0x20  ; true
    // 0x13c1aec: StoreField: r0->field_43 = r1
    //     0x13c1aec: stur            w1, [x0, #0x43]
    // 0x13c1af0: r2 = Instance_BoxShape
    //     0x13c1af0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13c1af4: ldr             x2, [x2, #0x80]
    // 0x13c1af8: StoreField: r0->field_47 = r2
    //     0x13c1af8: stur            w2, [x0, #0x47]
    // 0x13c1afc: StoreField: r0->field_6f = r1
    //     0x13c1afc: stur            w1, [x0, #0x6f]
    // 0x13c1b00: r2 = false
    //     0x13c1b00: add             x2, NULL, #0x30  ; false
    // 0x13c1b04: StoreField: r0->field_73 = r2
    //     0x13c1b04: stur            w2, [x0, #0x73]
    // 0x13c1b08: StoreField: r0->field_83 = r1
    //     0x13c1b08: stur            w1, [x0, #0x83]
    // 0x13c1b0c: StoreField: r0->field_7b = r2
    //     0x13c1b0c: stur            w2, [x0, #0x7b]
    // 0x13c1b10: LeaveFrame
    //     0x13c1b10: mov             SP, fp
    //     0x13c1b14: ldp             fp, lr, [SP], #0x10
    // 0x13c1b18: ret
    //     0x13c1b18: ret             
    // 0x13c1b1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13c1b1c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13c1b20: b               #0x13c16c0
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15cf4c4, size: 0x18c
    // 0x15cf4c4: EnterFrame
    //     0x15cf4c4: stp             fp, lr, [SP, #-0x10]!
    //     0x15cf4c8: mov             fp, SP
    // 0x15cf4cc: AllocStack(0x28)
    //     0x15cf4cc: sub             SP, SP, #0x28
    // 0x15cf4d0: SetupParameters(ExchangeCheckoutOnlinePaymentMethod this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15cf4d0: mov             x0, x1
    //     0x15cf4d4: stur            x1, [fp, #-8]
    //     0x15cf4d8: mov             x1, x2
    //     0x15cf4dc: stur            x2, [fp, #-0x10]
    // 0x15cf4e0: CheckStackOverflow
    //     0x15cf4e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15cf4e4: cmp             SP, x16
    //     0x15cf4e8: b.ls            #0x15cf648
    // 0x15cf4ec: r1 = 2
    //     0x15cf4ec: movz            x1, #0x2
    // 0x15cf4f0: r0 = AllocateContext()
    //     0x15cf4f0: bl              #0x16f6108  ; AllocateContextStub
    // 0x15cf4f4: mov             x1, x0
    // 0x15cf4f8: ldur            x0, [fp, #-8]
    // 0x15cf4fc: stur            x1, [fp, #-0x18]
    // 0x15cf500: StoreField: r1->field_f = r0
    //     0x15cf500: stur            w0, [x1, #0xf]
    // 0x15cf504: ldur            x0, [fp, #-0x10]
    // 0x15cf508: StoreField: r1->field_13 = r0
    //     0x15cf508: stur            w0, [x1, #0x13]
    // 0x15cf50c: r0 = Obx()
    //     0x15cf50c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15cf510: ldur            x2, [fp, #-0x18]
    // 0x15cf514: r1 = Function '<anonymous closure>':.
    //     0x15cf514: add             x1, PP, #0x46, lsl #12  ; [pp+0x466d8] AnonymousClosure: (0x15cf880), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::appBar (0x15ea93c)
    //     0x15cf518: ldr             x1, [x1, #0x6d8]
    // 0x15cf51c: stur            x0, [fp, #-8]
    // 0x15cf520: r0 = AllocateClosure()
    //     0x15cf520: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15cf524: mov             x1, x0
    // 0x15cf528: ldur            x0, [fp, #-8]
    // 0x15cf52c: StoreField: r0->field_b = r1
    //     0x15cf52c: stur            w1, [x0, #0xb]
    // 0x15cf530: ldur            x1, [fp, #-0x10]
    // 0x15cf534: r0 = of()
    //     0x15cf534: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15cf538: LoadField: r1 = r0->field_5b
    //     0x15cf538: ldur            w1, [x0, #0x5b]
    // 0x15cf53c: DecompressPointer r1
    //     0x15cf53c: add             x1, x1, HEAP, lsl #32
    // 0x15cf540: stur            x1, [fp, #-0x10]
    // 0x15cf544: r0 = ColorFilter()
    //     0x15cf544: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15cf548: mov             x1, x0
    // 0x15cf54c: ldur            x0, [fp, #-0x10]
    // 0x15cf550: stur            x1, [fp, #-0x20]
    // 0x15cf554: StoreField: r1->field_7 = r0
    //     0x15cf554: stur            w0, [x1, #7]
    // 0x15cf558: r0 = Instance_BlendMode
    //     0x15cf558: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15cf55c: ldr             x0, [x0, #0xb30]
    // 0x15cf560: StoreField: r1->field_b = r0
    //     0x15cf560: stur            w0, [x1, #0xb]
    // 0x15cf564: r0 = 1
    //     0x15cf564: movz            x0, #0x1
    // 0x15cf568: StoreField: r1->field_13 = r0
    //     0x15cf568: stur            x0, [x1, #0x13]
    // 0x15cf56c: r0 = SvgPicture()
    //     0x15cf56c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15cf570: stur            x0, [fp, #-0x10]
    // 0x15cf574: ldur            x16, [fp, #-0x20]
    // 0x15cf578: str             x16, [SP]
    // 0x15cf57c: mov             x1, x0
    // 0x15cf580: r2 = "assets/images/appbar_arrow.svg"
    //     0x15cf580: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15cf584: ldr             x2, [x2, #0xa40]
    // 0x15cf588: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15cf588: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15cf58c: ldr             x4, [x4, #0xa38]
    // 0x15cf590: r0 = SvgPicture.asset()
    //     0x15cf590: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15cf594: r0 = Align()
    //     0x15cf594: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15cf598: mov             x1, x0
    // 0x15cf59c: r0 = Instance_Alignment
    //     0x15cf59c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15cf5a0: ldr             x0, [x0, #0xb10]
    // 0x15cf5a4: stur            x1, [fp, #-0x20]
    // 0x15cf5a8: StoreField: r1->field_f = r0
    //     0x15cf5a8: stur            w0, [x1, #0xf]
    // 0x15cf5ac: r0 = 1.000000
    //     0x15cf5ac: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15cf5b0: StoreField: r1->field_13 = r0
    //     0x15cf5b0: stur            w0, [x1, #0x13]
    // 0x15cf5b4: ArrayStore: r1[0] = r0  ; List_4
    //     0x15cf5b4: stur            w0, [x1, #0x17]
    // 0x15cf5b8: ldur            x0, [fp, #-0x10]
    // 0x15cf5bc: StoreField: r1->field_b = r0
    //     0x15cf5bc: stur            w0, [x1, #0xb]
    // 0x15cf5c0: r0 = InkWell()
    //     0x15cf5c0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15cf5c4: mov             x3, x0
    // 0x15cf5c8: ldur            x0, [fp, #-0x20]
    // 0x15cf5cc: stur            x3, [fp, #-0x10]
    // 0x15cf5d0: StoreField: r3->field_b = r0
    //     0x15cf5d0: stur            w0, [x3, #0xb]
    // 0x15cf5d4: ldur            x2, [fp, #-0x18]
    // 0x15cf5d8: r1 = Function '<anonymous closure>':.
    //     0x15cf5d8: add             x1, PP, #0x46, lsl #12  ; [pp+0x466e0] AnonymousClosure: (0x15cf650), in [package:customer_app/app/presentation/views/basic/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::appBar (0x15cf4c4)
    //     0x15cf5dc: ldr             x1, [x1, #0x6e0]
    // 0x15cf5e0: r0 = AllocateClosure()
    //     0x15cf5e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15cf5e4: ldur            x2, [fp, #-0x10]
    // 0x15cf5e8: StoreField: r2->field_f = r0
    //     0x15cf5e8: stur            w0, [x2, #0xf]
    // 0x15cf5ec: r0 = true
    //     0x15cf5ec: add             x0, NULL, #0x20  ; true
    // 0x15cf5f0: StoreField: r2->field_43 = r0
    //     0x15cf5f0: stur            w0, [x2, #0x43]
    // 0x15cf5f4: r1 = Instance_BoxShape
    //     0x15cf5f4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15cf5f8: ldr             x1, [x1, #0x80]
    // 0x15cf5fc: StoreField: r2->field_47 = r1
    //     0x15cf5fc: stur            w1, [x2, #0x47]
    // 0x15cf600: StoreField: r2->field_6f = r0
    //     0x15cf600: stur            w0, [x2, #0x6f]
    // 0x15cf604: r1 = false
    //     0x15cf604: add             x1, NULL, #0x30  ; false
    // 0x15cf608: StoreField: r2->field_73 = r1
    //     0x15cf608: stur            w1, [x2, #0x73]
    // 0x15cf60c: StoreField: r2->field_83 = r0
    //     0x15cf60c: stur            w0, [x2, #0x83]
    // 0x15cf610: StoreField: r2->field_7b = r1
    //     0x15cf610: stur            w1, [x2, #0x7b]
    // 0x15cf614: r0 = AppBar()
    //     0x15cf614: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15cf618: stur            x0, [fp, #-0x18]
    // 0x15cf61c: ldur            x16, [fp, #-8]
    // 0x15cf620: str             x16, [SP]
    // 0x15cf624: mov             x1, x0
    // 0x15cf628: ldur            x2, [fp, #-0x10]
    // 0x15cf62c: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15cf62c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15cf630: ldr             x4, [x4, #0xf00]
    // 0x15cf634: r0 = AppBar()
    //     0x15cf634: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15cf638: ldur            x0, [fp, #-0x18]
    // 0x15cf63c: LeaveFrame
    //     0x15cf63c: mov             SP, fp
    //     0x15cf640: ldp             fp, lr, [SP], #0x10
    // 0x15cf644: ret
    //     0x15cf644: ret             
    // 0x15cf648: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15cf648: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15cf64c: b               #0x15cf4ec
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15cf650, size: 0x48
    // 0x15cf650: EnterFrame
    //     0x15cf650: stp             fp, lr, [SP, #-0x10]!
    //     0x15cf654: mov             fp, SP
    // 0x15cf658: ldr             x0, [fp, #0x10]
    // 0x15cf65c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x15cf65c: ldur            w1, [x0, #0x17]
    // 0x15cf660: DecompressPointer r1
    //     0x15cf660: add             x1, x1, HEAP, lsl #32
    // 0x15cf664: CheckStackOverflow
    //     0x15cf664: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15cf668: cmp             SP, x16
    //     0x15cf66c: b.ls            #0x15cf690
    // 0x15cf670: LoadField: r0 = r1->field_f
    //     0x15cf670: ldur            w0, [x1, #0xf]
    // 0x15cf674: DecompressPointer r0
    //     0x15cf674: add             x0, x0, HEAP, lsl #32
    // 0x15cf678: mov             x1, x0
    // 0x15cf67c: r0 = onBackPress()
    //     0x15cf67c: bl              #0x15cf698  ; [package:customer_app/app/presentation/views/basic/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::onBackPress
    // 0x15cf680: r0 = Null
    //     0x15cf680: mov             x0, NULL
    // 0x15cf684: LeaveFrame
    //     0x15cf684: mov             SP, fp
    //     0x15cf688: ldp             fp, lr, [SP], #0x10
    // 0x15cf68c: ret
    //     0x15cf68c: ret             
    // 0x15cf690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15cf690: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15cf694: b               #0x15cf670
  }
  _ onBackPress(/* No info */) {
    // ** addr: 0x15cf698, size: 0xbc
    // 0x15cf698: EnterFrame
    //     0x15cf698: stp             fp, lr, [SP, #-0x10]!
    //     0x15cf69c: mov             fp, SP
    // 0x15cf6a0: AllocStack(0x8)
    //     0x15cf6a0: sub             SP, SP, #8
    // 0x15cf6a4: CheckStackOverflow
    //     0x15cf6a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15cf6a8: cmp             SP, x16
    //     0x15cf6ac: b.ls            #0x15cf74c
    // 0x15cf6b0: r0 = controller()
    //     0x15cf6b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cf6b4: LoadField: r1 = r0->field_77
    //     0x15cf6b4: ldur            w1, [x0, #0x77]
    // 0x15cf6b8: DecompressPointer r1
    //     0x15cf6b8: add             x1, x1, HEAP, lsl #32
    // 0x15cf6bc: r2 = ""
    //     0x15cf6bc: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15cf6c0: r0 = value=()
    //     0x15cf6c0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15cf6c4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15cf6c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15cf6c8: ldr             x0, [x0, #0x1c80]
    //     0x15cf6cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15cf6d0: cmp             w0, w16
    //     0x15cf6d4: b.ne            #0x15cf6e0
    //     0x15cf6d8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15cf6dc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15cf6e0: r1 = Function '<anonymous closure>':.
    //     0x15cf6e0: add             x1, PP, #0x46, lsl #12  ; [pp+0x466e8] AnonymousClosure: (0x15cf754), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::onBackPress (0x15cf7c4)
    //     0x15cf6e4: ldr             x1, [x1, #0x6e8]
    // 0x15cf6e8: r2 = Null
    //     0x15cf6e8: mov             x2, NULL
    // 0x15cf6ec: r0 = AllocateClosure()
    //     0x15cf6ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15cf6f0: mov             x1, x0
    // 0x15cf6f4: r0 = GetNavigation.until()
    //     0x15cf6f4: bl              #0x12f9dc4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.until
    // 0x15cf6f8: r1 = <bool>
    //     0x15cf6f8: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x15cf6fc: r0 = _Future()
    //     0x15cf6fc: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x15cf700: stur            x0, [fp, #-8]
    // 0x15cf704: StoreField: r0->field_b = rZR
    //     0x15cf704: stur            xzr, [x0, #0xb]
    // 0x15cf708: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x15cf708: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15cf70c: ldr             x0, [x0, #0x778]
    //     0x15cf710: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15cf714: cmp             w0, w16
    //     0x15cf718: b.ne            #0x15cf724
    //     0x15cf71c: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x15cf720: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x15cf724: mov             x1, x0
    // 0x15cf728: ldur            x0, [fp, #-8]
    // 0x15cf72c: StoreField: r0->field_13 = r1
    //     0x15cf72c: stur            w1, [x0, #0x13]
    // 0x15cf730: mov             x1, x0
    // 0x15cf734: r2 = true
    //     0x15cf734: add             x2, NULL, #0x20  ; true
    // 0x15cf738: r0 = _asyncComplete()
    //     0x15cf738: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x15cf73c: ldur            x0, [fp, #-8]
    // 0x15cf740: LeaveFrame
    //     0x15cf740: mov             SP, fp
    //     0x15cf744: ldp             fp, lr, [SP], #0x10
    // 0x15cf748: ret
    //     0x15cf748: ret             
    // 0x15cf74c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15cf74c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15cf750: b               #0x15cf6b0
  }
}
