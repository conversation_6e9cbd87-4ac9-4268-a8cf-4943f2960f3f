// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_detail_grid_item_view.dart

// class id: 1049314, size: 0x8
class :: {
}

// class id: 3404, size: 0x14, field offset: 0x14
class _ProductGridItemState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb090fc, size: 0xf8
    // 0xb090fc: EnterFrame
    //     0xb090fc: stp             fp, lr, [SP, #-0x10]!
    //     0xb09100: mov             fp, SP
    // 0xb09104: AllocStack(0x28)
    //     0xb09104: sub             SP, SP, #0x28
    // 0xb09108: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */)
    //     0xb09108: stur            x1, [fp, #-8]
    // 0xb0910c: CheckStackOverflow
    //     0xb0910c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb09110: cmp             SP, x16
    //     0xb09114: b.ls            #0xb091e8
    // 0xb09118: r1 = 1
    //     0xb09118: movz            x1, #0x1
    // 0xb0911c: r0 = AllocateContext()
    //     0xb0911c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb09120: mov             x2, x0
    // 0xb09124: ldur            x0, [fp, #-8]
    // 0xb09128: stur            x2, [fp, #-0x10]
    // 0xb0912c: StoreField: r2->field_f = r0
    //     0xb0912c: stur            w0, [x2, #0xf]
    // 0xb09130: mov             x1, x0
    // 0xb09134: r0 = _calculateAspectRatio()
    //     0xb09134: bl              #0xb09218  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_calculateAspectRatio
    // 0xb09138: stur            d0, [fp, #-0x20]
    // 0xb0913c: r0 = SliverGridDelegateWithFixedCrossAxisCount()
    //     0xb0913c: bl              #0xa4d630  ; AllocateSliverGridDelegateWithFixedCrossAxisCountStub -> SliverGridDelegateWithFixedCrossAxisCount (size=0x2c)
    // 0xb09140: mov             x1, x0
    // 0xb09144: r0 = 2
    //     0xb09144: movz            x0, #0x2
    // 0xb09148: stur            x1, [fp, #-0x18]
    // 0xb0914c: StoreField: r1->field_7 = r0
    //     0xb0914c: stur            x0, [x1, #7]
    // 0xb09150: d0 = 12.000000
    //     0xb09150: fmov            d0, #12.00000000
    // 0xb09154: StoreField: r1->field_f = d0
    //     0xb09154: stur            d0, [x1, #0xf]
    // 0xb09158: d0 = 4.000000
    //     0xb09158: fmov            d0, #4.00000000
    // 0xb0915c: ArrayStore: r1[0] = d0  ; List_8
    //     0xb0915c: stur            d0, [x1, #0x17]
    // 0xb09160: ldur            d0, [fp, #-0x20]
    // 0xb09164: StoreField: r1->field_1f = d0
    //     0xb09164: stur            d0, [x1, #0x1f]
    // 0xb09168: ldur            x0, [fp, #-8]
    // 0xb0916c: LoadField: r2 = r0->field_b
    //     0xb0916c: ldur            w2, [x0, #0xb]
    // 0xb09170: DecompressPointer r2
    //     0xb09170: add             x2, x2, HEAP, lsl #32
    // 0xb09174: cmp             w2, NULL
    // 0xb09178: b.eq            #0xb091f0
    // 0xb0917c: LoadField: r0 = r2->field_b
    //     0xb0917c: ldur            w0, [x2, #0xb]
    // 0xb09180: DecompressPointer r0
    //     0xb09180: add             x0, x0, HEAP, lsl #32
    // 0xb09184: r2 = LoadClassIdInstr(r0)
    //     0xb09184: ldur            x2, [x0, #-1]
    //     0xb09188: ubfx            x2, x2, #0xc, #0x14
    // 0xb0918c: str             x0, [SP]
    // 0xb09190: mov             x0, x2
    // 0xb09194: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb09194: movz            x17, #0xc898
    //     0xb09198: add             lr, x0, x17
    //     0xb0919c: ldr             lr, [x21, lr, lsl #3]
    //     0xb091a0: blr             lr
    // 0xb091a4: ldur            x2, [fp, #-0x10]
    // 0xb091a8: r1 = Function '<anonymous closure>':.
    //     0xb091a8: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e98] AnonymousClosure: (0xb092a8), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::build (0xb090fc)
    //     0xb091ac: ldr             x1, [x1, #0xe98]
    // 0xb091b0: stur            x0, [fp, #-8]
    // 0xb091b4: r0 = AllocateClosure()
    //     0xb091b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb091b8: stur            x0, [fp, #-0x10]
    // 0xb091bc: r0 = GridView()
    //     0xb091bc: bl              #0x9010e0  ; AllocateGridViewStub -> GridView (size=0x60)
    // 0xb091c0: mov             x1, x0
    // 0xb091c4: ldur            x2, [fp, #-0x18]
    // 0xb091c8: ldur            x3, [fp, #-0x10]
    // 0xb091cc: ldur            x5, [fp, #-8]
    // 0xb091d0: stur            x0, [fp, #-8]
    // 0xb091d4: r0 = GridView.builder()
    //     0xb091d4: bl              #0x900fc8  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.builder
    // 0xb091d8: ldur            x0, [fp, #-8]
    // 0xb091dc: LeaveFrame
    //     0xb091dc: mov             SP, fp
    //     0xb091e0: ldp             fp, lr, [SP], #0x10
    // 0xb091e4: ret
    //     0xb091e4: ret             
    // 0xb091e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb091e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb091ec: b               #0xb09118
    // 0xb091f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb091f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _calculateAspectRatio(/* No info */) {
    // ** addr: 0xb09218, size: 0x90
    // 0xb09218: LoadField: r0 = r1->field_b
    //     0xb09218: ldur            w0, [x1, #0xb]
    // 0xb0921c: DecompressPointer r0
    //     0xb0921c: add             x0, x0, HEAP, lsl #32
    // 0xb09220: cmp             w0, NULL
    // 0xb09224: b.eq            #0xb0929c
    // 0xb09228: LoadField: r1 = r0->field_f
    //     0xb09228: ldur            w1, [x0, #0xf]
    // 0xb0922c: DecompressPointer r1
    //     0xb0922c: add             x1, x1, HEAP, lsl #32
    // 0xb09230: LoadField: r0 = r1->field_1f
    //     0xb09230: ldur            w0, [x1, #0x1f]
    // 0xb09234: DecompressPointer r0
    //     0xb09234: add             x0, x0, HEAP, lsl #32
    // 0xb09238: cmp             w0, NULL
    // 0xb0923c: b.ne            #0xb09248
    // 0xb09240: r0 = Null
    //     0xb09240: mov             x0, NULL
    // 0xb09244: b               #0xb09254
    // 0xb09248: LoadField: r2 = r0->field_7
    //     0xb09248: ldur            w2, [x0, #7]
    // 0xb0924c: DecompressPointer r2
    //     0xb0924c: add             x2, x2, HEAP, lsl #32
    // 0xb09250: mov             x0, x2
    // 0xb09254: cmp             w0, NULL
    // 0xb09258: b.eq            #0xb09290
    // 0xb0925c: tbnz            w0, #4, #0xb09290
    // 0xb09260: LoadField: r0 = r1->field_3f
    //     0xb09260: ldur            w0, [x1, #0x3f]
    // 0xb09264: DecompressPointer r0
    //     0xb09264: add             x0, x0, HEAP, lsl #32
    // 0xb09268: cmp             w0, NULL
    // 0xb0926c: b.ne            #0xb09278
    // 0xb09270: r0 = Null
    //     0xb09270: mov             x0, NULL
    // 0xb09274: b               #0xb09284
    // 0xb09278: LoadField: r1 = r0->field_23
    //     0xb09278: ldur            w1, [x0, #0x23]
    // 0xb0927c: DecompressPointer r1
    //     0xb0927c: add             x1, x1, HEAP, lsl #32
    // 0xb09280: mov             x0, x1
    // 0xb09284: cmp             w0, NULL
    // 0xb09288: b.eq            #0xb09290
    // 0xb0928c: tbz             w0, #4, #0xb09290
    // 0xb09290: d0 = 0.540541
    //     0xb09290: add             x17, PP, #0x5a, lsl #12  ; [pp+0x5aa80] IMM: double(0.5405405405405405) from 0x3fe14c1bacf914c1
    //     0xb09294: ldr             d0, [x17, #0xa80]
    // 0xb09298: ret
    //     0xb09298: ret             
    // 0xb0929c: EnterFrame
    //     0xb0929c: stp             fp, lr, [SP, #-0x10]!
    //     0xb092a0: mov             fp, SP
    // 0xb092a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb092a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] SingleChildRenderObjectWidget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb092a8, size: 0x874
    // 0xb092a8: EnterFrame
    //     0xb092a8: stp             fp, lr, [SP, #-0x10]!
    //     0xb092ac: mov             fp, SP
    // 0xb092b0: AllocStack(0x80)
    //     0xb092b0: sub             SP, SP, #0x80
    // 0xb092b4: SetupParameters()
    //     0xb092b4: ldr             x0, [fp, #0x20]
    //     0xb092b8: ldur            w1, [x0, #0x17]
    //     0xb092bc: add             x1, x1, HEAP, lsl #32
    //     0xb092c0: stur            x1, [fp, #-8]
    // 0xb092c4: CheckStackOverflow
    //     0xb092c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb092c8: cmp             SP, x16
    //     0xb092cc: b.ls            #0xb09b10
    // 0xb092d0: r1 = 1
    //     0xb092d0: movz            x1, #0x1
    // 0xb092d4: r0 = AllocateContext()
    //     0xb092d4: bl              #0x16f6108  ; AllocateContextStub
    // 0xb092d8: mov             x2, x0
    // 0xb092dc: ldur            x1, [fp, #-8]
    // 0xb092e0: stur            x2, [fp, #-0x10]
    // 0xb092e4: StoreField: r2->field_b = r1
    //     0xb092e4: stur            w1, [x2, #0xb]
    // 0xb092e8: LoadField: r0 = r1->field_f
    //     0xb092e8: ldur            w0, [x1, #0xf]
    // 0xb092ec: DecompressPointer r0
    //     0xb092ec: add             x0, x0, HEAP, lsl #32
    // 0xb092f0: LoadField: r3 = r0->field_b
    //     0xb092f0: ldur            w3, [x0, #0xb]
    // 0xb092f4: DecompressPointer r3
    //     0xb092f4: add             x3, x3, HEAP, lsl #32
    // 0xb092f8: cmp             w3, NULL
    // 0xb092fc: b.eq            #0xb09b18
    // 0xb09300: LoadField: r0 = r3->field_b
    //     0xb09300: ldur            w0, [x3, #0xb]
    // 0xb09304: DecompressPointer r0
    //     0xb09304: add             x0, x0, HEAP, lsl #32
    // 0xb09308: r3 = LoadClassIdInstr(r0)
    //     0xb09308: ldur            x3, [x0, #-1]
    //     0xb0930c: ubfx            x3, x3, #0xc, #0x14
    // 0xb09310: ldr             x16, [fp, #0x10]
    // 0xb09314: stp             x16, x0, [SP]
    // 0xb09318: mov             x0, x3
    // 0xb0931c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb0931c: sub             lr, x0, #0xb7
    //     0xb09320: ldr             lr, [x21, lr, lsl #3]
    //     0xb09324: blr             lr
    // 0xb09328: mov             x4, x0
    // 0xb0932c: ldur            x3, [fp, #-0x10]
    // 0xb09330: stur            x4, [fp, #-0x18]
    // 0xb09334: StoreField: r3->field_f = r0
    //     0xb09334: stur            w0, [x3, #0xf]
    //     0xb09338: ldurb           w16, [x3, #-1]
    //     0xb0933c: ldurb           w17, [x0, #-1]
    //     0xb09340: and             x16, x17, x16, lsr #2
    //     0xb09344: tst             x16, HEAP, lsr #32
    //     0xb09348: b.eq            #0xb09350
    //     0xb0934c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xb09350: cmp             w4, NULL
    // 0xb09354: b.ne            #0xb09368
    // 0xb09358: r0 = Instance_SizedBox
    //     0xb09358: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb0935c: LeaveFrame
    //     0xb0935c: mov             SP, fp
    //     0xb09360: ldp             fp, lr, [SP], #0x10
    // 0xb09364: ret
    //     0xb09364: ret             
    // 0xb09368: ldr             x0, [fp, #0x10]
    // 0xb0936c: r1 = Null
    //     0xb0936c: mov             x1, NULL
    // 0xb09370: r2 = 8
    //     0xb09370: movz            x2, #0x8
    // 0xb09374: r0 = AllocateArray()
    //     0xb09374: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb09378: r16 = "product_"
    //     0xb09378: add             x16, PP, #0x61, lsl #12  ; [pp+0x61b88] "product_"
    //     0xb0937c: ldr             x16, [x16, #0xb88]
    // 0xb09380: StoreField: r0->field_f = r16
    //     0xb09380: stur            w16, [x0, #0xf]
    // 0xb09384: ldur            x1, [fp, #-0x18]
    // 0xb09388: LoadField: r2 = r1->field_47
    //     0xb09388: ldur            w2, [x1, #0x47]
    // 0xb0938c: DecompressPointer r2
    //     0xb0938c: add             x2, x2, HEAP, lsl #32
    // 0xb09390: StoreField: r0->field_13 = r2
    //     0xb09390: stur            w2, [x0, #0x13]
    // 0xb09394: r16 = "_"
    //     0xb09394: ldr             x16, [PP, #0x45a8]  ; [pp+0x45a8] "_"
    // 0xb09398: ArrayStore: r0[0] = r16  ; List_4
    //     0xb09398: stur            w16, [x0, #0x17]
    // 0xb0939c: ldr             x2, [fp, #0x10]
    // 0xb093a0: StoreField: r0->field_1b = r2
    //     0xb093a0: stur            w2, [x0, #0x1b]
    // 0xb093a4: str             x0, [SP]
    // 0xb093a8: r0 = _interpolate()
    //     0xb093a8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb093ac: r1 = <String>
    //     0xb093ac: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb093b0: stur            x0, [fp, #-0x20]
    // 0xb093b4: r0 = ValueKey()
    //     0xb093b4: bl              #0x68b554  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xb093b8: mov             x1, x0
    // 0xb093bc: ldur            x0, [fp, #-0x20]
    // 0xb093c0: stur            x1, [fp, #-0x28]
    // 0xb093c4: StoreField: r1->field_b = r0
    //     0xb093c4: stur            w0, [x1, #0xb]
    // 0xb093c8: r0 = Radius()
    //     0xb093c8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb093cc: d0 = 12.000000
    //     0xb093cc: fmov            d0, #12.00000000
    // 0xb093d0: stur            x0, [fp, #-0x20]
    // 0xb093d4: StoreField: r0->field_7 = d0
    //     0xb093d4: stur            d0, [x0, #7]
    // 0xb093d8: StoreField: r0->field_f = d0
    //     0xb093d8: stur            d0, [x0, #0xf]
    // 0xb093dc: r0 = BorderRadius()
    //     0xb093dc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb093e0: mov             x1, x0
    // 0xb093e4: ldur            x0, [fp, #-0x20]
    // 0xb093e8: stur            x1, [fp, #-0x30]
    // 0xb093ec: StoreField: r1->field_7 = r0
    //     0xb093ec: stur            w0, [x1, #7]
    // 0xb093f0: StoreField: r1->field_b = r0
    //     0xb093f0: stur            w0, [x1, #0xb]
    // 0xb093f4: StoreField: r1->field_f = r0
    //     0xb093f4: stur            w0, [x1, #0xf]
    // 0xb093f8: StoreField: r1->field_13 = r0
    //     0xb093f8: stur            w0, [x1, #0x13]
    // 0xb093fc: r0 = RoundedRectangleBorder()
    //     0xb093fc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb09400: mov             x3, x0
    // 0xb09404: ldur            x0, [fp, #-0x30]
    // 0xb09408: stur            x3, [fp, #-0x38]
    // 0xb0940c: StoreField: r3->field_b = r0
    //     0xb0940c: stur            w0, [x3, #0xb]
    // 0xb09410: r0 = Instance_BorderSide
    //     0xb09410: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb09414: ldr             x0, [x0, #0xe20]
    // 0xb09418: StoreField: r3->field_7 = r0
    //     0xb09418: stur            w0, [x3, #7]
    // 0xb0941c: ldur            x0, [fp, #-0x18]
    // 0xb09420: LoadField: r1 = r0->field_e7
    //     0xb09420: ldur            w1, [x0, #0xe7]
    // 0xb09424: DecompressPointer r1
    //     0xb09424: add             x1, x1, HEAP, lsl #32
    // 0xb09428: cmp             w1, NULL
    // 0xb0942c: b.ne            #0xb09438
    // 0xb09430: r1 = Null
    //     0xb09430: mov             x1, NULL
    // 0xb09434: b               #0xb09440
    // 0xb09438: LoadField: r2 = r1->field_b
    //     0xb09438: ldur            w2, [x1, #0xb]
    // 0xb0943c: mov             x1, x2
    // 0xb09440: cmp             w1, NULL
    // 0xb09444: b.ne            #0xb09450
    // 0xb09448: r1 = 0
    //     0xb09448: movz            x1, #0
    // 0xb0944c: b               #0xb09458
    // 0xb09450: r2 = LoadInt32Instr(r1)
    //     0xb09450: sbfx            x2, x1, #1, #0x1f
    // 0xb09454: mov             x1, x2
    // 0xb09458: lsl             x4, x1, #1
    // 0xb0945c: stur            x4, [fp, #-0x20]
    // 0xb09460: r1 = Function '<anonymous closure>':.
    //     0xb09460: add             x1, PP, #0x61, lsl #12  ; [pp+0x61ea0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb09464: ldr             x1, [x1, #0xea0]
    // 0xb09468: r2 = Null
    //     0xb09468: mov             x2, NULL
    // 0xb0946c: r0 = AllocateClosure()
    //     0xb0946c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb09470: ldur            x2, [fp, #-0x10]
    // 0xb09474: r1 = Function '<anonymous closure>':.
    //     0xb09474: add             x1, PP, #0x61, lsl #12  ; [pp+0x61ea8] AnonymousClosure: (0xb0abd0), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::build (0xb090fc)
    //     0xb09478: ldr             x1, [x1, #0xea8]
    // 0xb0947c: stur            x0, [fp, #-0x30]
    // 0xb09480: r0 = AllocateClosure()
    //     0xb09480: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb09484: stur            x0, [fp, #-0x40]
    // 0xb09488: r0 = PageView()
    //     0xb09488: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb0948c: mov             x1, x0
    // 0xb09490: ldur            x2, [fp, #-0x40]
    // 0xb09494: ldur            x3, [fp, #-0x20]
    // 0xb09498: ldur            x5, [fp, #-0x30]
    // 0xb0949c: stur            x0, [fp, #-0x20]
    // 0xb094a0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xb094a0: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xb094a4: r0 = PageView.builder()
    //     0xb094a4: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb094a8: r0 = AspectRatio()
    //     0xb094a8: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb094ac: d0 = 1.000000
    //     0xb094ac: fmov            d0, #1.00000000
    // 0xb094b0: stur            x0, [fp, #-0x30]
    // 0xb094b4: StoreField: r0->field_f = d0
    //     0xb094b4: stur            d0, [x0, #0xf]
    // 0xb094b8: ldur            x1, [fp, #-0x20]
    // 0xb094bc: StoreField: r0->field_b = r1
    //     0xb094bc: stur            w1, [x0, #0xb]
    // 0xb094c0: r0 = Card()
    //     0xb094c0: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb094c4: mov             x2, x0
    // 0xb094c8: r0 = 0.000000
    //     0xb094c8: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb094cc: stur            x2, [fp, #-0x20]
    // 0xb094d0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb094d0: stur            w0, [x2, #0x17]
    // 0xb094d4: ldur            x0, [fp, #-0x38]
    // 0xb094d8: StoreField: r2->field_1b = r0
    //     0xb094d8: stur            w0, [x2, #0x1b]
    // 0xb094dc: r0 = true
    //     0xb094dc: add             x0, NULL, #0x20  ; true
    // 0xb094e0: StoreField: r2->field_1f = r0
    //     0xb094e0: stur            w0, [x2, #0x1f]
    // 0xb094e4: ldur            x1, [fp, #-0x30]
    // 0xb094e8: StoreField: r2->field_2f = r1
    //     0xb094e8: stur            w1, [x2, #0x2f]
    // 0xb094ec: StoreField: r2->field_2b = r0
    //     0xb094ec: stur            w0, [x2, #0x2b]
    // 0xb094f0: r0 = Instance__CardVariant
    //     0xb094f0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb094f4: ldr             x0, [x0, #0xa68]
    // 0xb094f8: StoreField: r2->field_33 = r0
    //     0xb094f8: stur            w0, [x2, #0x33]
    // 0xb094fc: ldur            x0, [fp, #-0x18]
    // 0xb09500: LoadField: r1 = r0->field_53
    //     0xb09500: ldur            w1, [x0, #0x53]
    // 0xb09504: DecompressPointer r1
    //     0xb09504: add             x1, x1, HEAP, lsl #32
    // 0xb09508: cmp             w1, NULL
    // 0xb0950c: b.ne            #0xb09518
    // 0xb09510: r1 = Null
    //     0xb09510: mov             x1, NULL
    // 0xb09514: b               #0xb09524
    // 0xb09518: LoadField: r3 = r1->field_b
    //     0xb09518: ldur            w3, [x1, #0xb]
    // 0xb0951c: DecompressPointer r3
    //     0xb0951c: add             x3, x3, HEAP, lsl #32
    // 0xb09520: mov             x1, x3
    // 0xb09524: cmp             w1, NULL
    // 0xb09528: b.ne            #0xb09530
    // 0xb0952c: r1 = ""
    //     0xb0952c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb09530: ldr             x3, [fp, #0x10]
    // 0xb09534: ldur            x4, [fp, #-8]
    // 0xb09538: r0 = capitalizeFirstWord()
    //     0xb09538: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb0953c: ldr             x1, [fp, #0x18]
    // 0xb09540: stur            x0, [fp, #-0x30]
    // 0xb09544: r0 = of()
    //     0xb09544: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb09548: LoadField: r1 = r0->field_87
    //     0xb09548: ldur            w1, [x0, #0x87]
    // 0xb0954c: DecompressPointer r1
    //     0xb0954c: add             x1, x1, HEAP, lsl #32
    // 0xb09550: LoadField: r0 = r1->field_2b
    //     0xb09550: ldur            w0, [x1, #0x2b]
    // 0xb09554: DecompressPointer r0
    //     0xb09554: add             x0, x0, HEAP, lsl #32
    // 0xb09558: r16 = 14.000000
    //     0xb09558: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb0955c: ldr             x16, [x16, #0x1d8]
    // 0xb09560: r30 = Instance_Color
    //     0xb09560: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb09564: stp             lr, x16, [SP]
    // 0xb09568: mov             x1, x0
    // 0xb0956c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0956c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb09570: ldr             x4, [x4, #0xaa0]
    // 0xb09574: r0 = copyWith()
    //     0xb09574: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb09578: stur            x0, [fp, #-0x38]
    // 0xb0957c: r0 = Text()
    //     0xb0957c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb09580: mov             x1, x0
    // 0xb09584: ldur            x0, [fp, #-0x30]
    // 0xb09588: stur            x1, [fp, #-0x40]
    // 0xb0958c: StoreField: r1->field_b = r0
    //     0xb0958c: stur            w0, [x1, #0xb]
    // 0xb09590: ldur            x0, [fp, #-0x38]
    // 0xb09594: StoreField: r1->field_13 = r0
    //     0xb09594: stur            w0, [x1, #0x13]
    // 0xb09598: r0 = Instance_TextOverflow
    //     0xb09598: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb0959c: ldr             x0, [x0, #0xe10]
    // 0xb095a0: StoreField: r1->field_2b = r0
    //     0xb095a0: stur            w0, [x1, #0x2b]
    // 0xb095a4: r0 = 2
    //     0xb095a4: movz            x0, #0x2
    // 0xb095a8: StoreField: r1->field_37 = r0
    //     0xb095a8: stur            w0, [x1, #0x37]
    // 0xb095ac: r0 = Padding()
    //     0xb095ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb095b0: mov             x3, x0
    // 0xb095b4: r0 = Instance_EdgeInsets
    //     0xb095b4: add             x0, PP, #0x55, lsl #12  ; [pp+0x55930] Obj!EdgeInsets@d586a1
    //     0xb095b8: ldr             x0, [x0, #0x930]
    // 0xb095bc: stur            x3, [fp, #-0x30]
    // 0xb095c0: StoreField: r3->field_f = r0
    //     0xb095c0: stur            w0, [x3, #0xf]
    // 0xb095c4: ldur            x0, [fp, #-0x40]
    // 0xb095c8: StoreField: r3->field_b = r0
    //     0xb095c8: stur            w0, [x3, #0xb]
    // 0xb095cc: ldur            x0, [fp, #-8]
    // 0xb095d0: LoadField: r1 = r0->field_f
    //     0xb095d0: ldur            w1, [x0, #0xf]
    // 0xb095d4: DecompressPointer r1
    //     0xb095d4: add             x1, x1, HEAP, lsl #32
    // 0xb095d8: ldr             x2, [fp, #0x10]
    // 0xb095dc: r4 = LoadInt32Instr(r2)
    //     0xb095dc: sbfx            x4, x2, #1, #0x1f
    //     0xb095e0: tbz             w2, #0, #0xb095e8
    //     0xb095e4: ldur            x4, [x2, #7]
    // 0xb095e8: mov             x2, x4
    // 0xb095ec: stur            x4, [fp, #-0x48]
    // 0xb095f0: r0 = _buildRatingSection()
    //     0xb095f0: bl              #0xb0a1e8  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildRatingSection
    // 0xb095f4: r1 = Null
    //     0xb095f4: mov             x1, NULL
    // 0xb095f8: r2 = 6
    //     0xb095f8: movz            x2, #0x6
    // 0xb095fc: stur            x0, [fp, #-0x38]
    // 0xb09600: r0 = AllocateArray()
    //     0xb09600: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb09604: r16 = " "
    //     0xb09604: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb09608: StoreField: r0->field_f = r16
    //     0xb09608: stur            w16, [x0, #0xf]
    // 0xb0960c: ldur            x1, [fp, #-0x18]
    // 0xb09610: LoadField: r2 = r1->field_f3
    //     0xb09610: ldur            w2, [x1, #0xf3]
    // 0xb09614: DecompressPointer r2
    //     0xb09614: add             x2, x2, HEAP, lsl #32
    // 0xb09618: StoreField: r0->field_13 = r2
    //     0xb09618: stur            w2, [x0, #0x13]
    // 0xb0961c: r16 = " "
    //     0xb0961c: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb09620: ArrayStore: r0[0] = r16  ; List_4
    //     0xb09620: stur            w16, [x0, #0x17]
    // 0xb09624: str             x0, [SP]
    // 0xb09628: r0 = _interpolate()
    //     0xb09628: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb0962c: ldr             x1, [fp, #0x18]
    // 0xb09630: stur            x0, [fp, #-0x40]
    // 0xb09634: r0 = of()
    //     0xb09634: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb09638: LoadField: r1 = r0->field_87
    //     0xb09638: ldur            w1, [x0, #0x87]
    // 0xb0963c: DecompressPointer r1
    //     0xb0963c: add             x1, x1, HEAP, lsl #32
    // 0xb09640: LoadField: r0 = r1->field_27
    //     0xb09640: ldur            w0, [x1, #0x27]
    // 0xb09644: DecompressPointer r0
    //     0xb09644: add             x0, x0, HEAP, lsl #32
    // 0xb09648: r16 = 21.000000
    //     0xb09648: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xb0964c: ldr             x16, [x16, #0x9b0]
    // 0xb09650: r30 = Instance_Color
    //     0xb09650: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb09654: stp             lr, x16, [SP]
    // 0xb09658: mov             x1, x0
    // 0xb0965c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0965c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb09660: ldr             x4, [x4, #0xaa0]
    // 0xb09664: r0 = copyWith()
    //     0xb09664: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb09668: stur            x0, [fp, #-0x50]
    // 0xb0966c: r0 = Text()
    //     0xb0966c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb09670: mov             x1, x0
    // 0xb09674: ldur            x0, [fp, #-0x40]
    // 0xb09678: stur            x1, [fp, #-0x58]
    // 0xb0967c: StoreField: r1->field_b = r0
    //     0xb0967c: stur            w0, [x1, #0xb]
    // 0xb09680: ldur            x0, [fp, #-0x50]
    // 0xb09684: StoreField: r1->field_13 = r0
    //     0xb09684: stur            w0, [x1, #0x13]
    // 0xb09688: r0 = WidgetSpan()
    //     0xb09688: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xb0968c: mov             x3, x0
    // 0xb09690: ldur            x0, [fp, #-0x58]
    // 0xb09694: stur            x3, [fp, #-0x50]
    // 0xb09698: StoreField: r3->field_13 = r0
    //     0xb09698: stur            w0, [x3, #0x13]
    // 0xb0969c: r0 = Instance_PlaceholderAlignment
    //     0xb0969c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] Obj!PlaceholderAlignment@d76401
    //     0xb096a0: ldr             x0, [x0, #0xa0]
    // 0xb096a4: StoreField: r3->field_b = r0
    //     0xb096a4: stur            w0, [x3, #0xb]
    // 0xb096a8: ldur            x0, [fp, #-0x18]
    // 0xb096ac: LoadField: r4 = r0->field_fb
    //     0xb096ac: ldur            w4, [x0, #0xfb]
    // 0xb096b0: DecompressPointer r4
    //     0xb096b0: add             x4, x4, HEAP, lsl #32
    // 0xb096b4: stur            x4, [fp, #-0x40]
    // 0xb096b8: r1 = Null
    //     0xb096b8: mov             x1, NULL
    // 0xb096bc: r2 = 4
    //     0xb096bc: movz            x2, #0x4
    // 0xb096c0: r0 = AllocateArray()
    //     0xb096c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb096c4: mov             x1, x0
    // 0xb096c8: ldur            x0, [fp, #-0x40]
    // 0xb096cc: StoreField: r1->field_f = r0
    //     0xb096cc: stur            w0, [x1, #0xf]
    // 0xb096d0: r16 = " "
    //     0xb096d0: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb096d4: StoreField: r1->field_13 = r16
    //     0xb096d4: stur            w16, [x1, #0x13]
    // 0xb096d8: str             x1, [SP]
    // 0xb096dc: r0 = _interpolate()
    //     0xb096dc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb096e0: ldr             x1, [fp, #0x18]
    // 0xb096e4: stur            x0, [fp, #-0x40]
    // 0xb096e8: r0 = of()
    //     0xb096e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb096ec: LoadField: r1 = r0->field_87
    //     0xb096ec: ldur            w1, [x0, #0x87]
    // 0xb096f0: DecompressPointer r1
    //     0xb096f0: add             x1, x1, HEAP, lsl #32
    // 0xb096f4: LoadField: r0 = r1->field_2b
    //     0xb096f4: ldur            w0, [x1, #0x2b]
    // 0xb096f8: DecompressPointer r0
    //     0xb096f8: add             x0, x0, HEAP, lsl #32
    // 0xb096fc: stur            x0, [fp, #-0x58]
    // 0xb09700: r1 = Instance_Color
    //     0xb09700: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb09704: d0 = 0.400000
    //     0xb09704: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb09708: r0 = withOpacity()
    //     0xb09708: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb0970c: r16 = Instance_TextDecoration
    //     0xb0970c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb09710: ldr             x16, [x16, #0xe30]
    // 0xb09714: r30 = 10.000000
    //     0xb09714: ldr             lr, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb09718: stp             lr, x16, [SP, #8]
    // 0xb0971c: str             x0, [SP]
    // 0xb09720: ldur            x1, [fp, #-0x58]
    // 0xb09724: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, decoration, 0x1, fontSize, 0x2, null]
    //     0xb09724: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb60] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "decoration", 0x1, "fontSize", 0x2, Null]
    //     0xb09728: ldr             x4, [x4, #0xb60]
    // 0xb0972c: r0 = copyWith()
    //     0xb0972c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb09730: stur            x0, [fp, #-0x58]
    // 0xb09734: r0 = TextSpan()
    //     0xb09734: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb09738: mov             x3, x0
    // 0xb0973c: ldur            x0, [fp, #-0x40]
    // 0xb09740: stur            x3, [fp, #-0x60]
    // 0xb09744: StoreField: r3->field_b = r0
    //     0xb09744: stur            w0, [x3, #0xb]
    // 0xb09748: r0 = Instance__DeferringMouseCursor
    //     0xb09748: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb0974c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb0974c: stur            w0, [x3, #0x17]
    // 0xb09750: ldur            x1, [fp, #-0x58]
    // 0xb09754: StoreField: r3->field_7 = r1
    //     0xb09754: stur            w1, [x3, #7]
    // 0xb09758: r1 = Null
    //     0xb09758: mov             x1, NULL
    // 0xb0975c: r2 = 4
    //     0xb0975c: movz            x2, #0x4
    // 0xb09760: r0 = AllocateArray()
    //     0xb09760: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb09764: mov             x2, x0
    // 0xb09768: ldur            x0, [fp, #-0x50]
    // 0xb0976c: stur            x2, [fp, #-0x40]
    // 0xb09770: StoreField: r2->field_f = r0
    //     0xb09770: stur            w0, [x2, #0xf]
    // 0xb09774: ldur            x0, [fp, #-0x60]
    // 0xb09778: StoreField: r2->field_13 = r0
    //     0xb09778: stur            w0, [x2, #0x13]
    // 0xb0977c: r1 = <InlineSpan>
    //     0xb0977c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb09780: ldr             x1, [x1, #0xe40]
    // 0xb09784: r0 = AllocateGrowableArray()
    //     0xb09784: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb09788: mov             x1, x0
    // 0xb0978c: ldur            x0, [fp, #-0x40]
    // 0xb09790: stur            x1, [fp, #-0x50]
    // 0xb09794: StoreField: r1->field_f = r0
    //     0xb09794: stur            w0, [x1, #0xf]
    // 0xb09798: r0 = 4
    //     0xb09798: movz            x0, #0x4
    // 0xb0979c: StoreField: r1->field_b = r0
    //     0xb0979c: stur            w0, [x1, #0xb]
    // 0xb097a0: ldur            x2, [fp, #-0x18]
    // 0xb097a4: LoadField: r0 = r2->field_5f
    //     0xb097a4: ldur            w0, [x2, #0x5f]
    // 0xb097a8: DecompressPointer r0
    //     0xb097a8: add             x0, x0, HEAP, lsl #32
    // 0xb097ac: cmp             w0, NULL
    // 0xb097b0: b.ne            #0xb097b8
    // 0xb097b4: r0 = 0.000000
    //     0xb097b4: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb097b8: r3 = 60
    //     0xb097b8: movz            x3, #0x3c
    // 0xb097bc: branchIfSmi(r0, 0xb097c8)
    //     0xb097bc: tbz             w0, #0, #0xb097c8
    // 0xb097c0: r3 = LoadClassIdInstr(r0)
    //     0xb097c0: ldur            x3, [x0, #-1]
    //     0xb097c4: ubfx            x3, x3, #0xc, #0x14
    // 0xb097c8: r16 = 0.000000
    //     0xb097c8: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb097cc: stp             x16, x0, [SP]
    // 0xb097d0: mov             x0, x3
    // 0xb097d4: mov             lr, x0
    // 0xb097d8: ldr             lr, [x21, lr, lsl #3]
    // 0xb097dc: blr             lr
    // 0xb097e0: tbz             w0, #4, #0xb09968
    // 0xb097e4: ldur            x0, [fp, #-0x18]
    // 0xb097e8: r1 = Null
    //     0xb097e8: mov             x1, NULL
    // 0xb097ec: r2 = 6
    //     0xb097ec: movz            x2, #0x6
    // 0xb097f0: r0 = AllocateArray()
    //     0xb097f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb097f4: stur            x0, [fp, #-0x40]
    // 0xb097f8: r16 = "| "
    //     0xb097f8: add             x16, PP, #0x55, lsl #12  ; [pp+0x55938] "| "
    //     0xb097fc: ldr             x16, [x16, #0x938]
    // 0xb09800: StoreField: r0->field_f = r16
    //     0xb09800: stur            w16, [x0, #0xf]
    // 0xb09804: ldur            x1, [fp, #-0x18]
    // 0xb09808: LoadField: r2 = r1->field_5f
    //     0xb09808: ldur            w2, [x1, #0x5f]
    // 0xb0980c: DecompressPointer r2
    //     0xb0980c: add             x2, x2, HEAP, lsl #32
    // 0xb09810: cmp             w2, NULL
    // 0xb09814: b.ne            #0xb09824
    // 0xb09818: mov             x2, x0
    // 0xb0981c: r0 = Null
    //     0xb0981c: mov             x0, NULL
    // 0xb09820: b               #0xb09844
    // 0xb09824: stp             xzr, x2, [SP]
    // 0xb09828: r4 = 0
    //     0xb09828: movz            x4, #0
    // 0xb0982c: ldr             x0, [SP, #8]
    // 0xb09830: r16 = UnlinkedCall_0x613b5c
    //     0xb09830: add             x16, PP, #0x61, lsl #12  ; [pp+0x61eb0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb09834: add             x16, x16, #0xeb0
    // 0xb09838: ldp             x5, lr, [x16]
    // 0xb0983c: blr             lr
    // 0xb09840: ldur            x2, [fp, #-0x40]
    // 0xb09844: ldur            x3, [fp, #-0x50]
    // 0xb09848: mov             x1, x2
    // 0xb0984c: ArrayStore: r1[1] = r0  ; List_4
    //     0xb0984c: add             x25, x1, #0x13
    //     0xb09850: str             w0, [x25]
    //     0xb09854: tbz             w0, #0, #0xb09870
    //     0xb09858: ldurb           w16, [x1, #-1]
    //     0xb0985c: ldurb           w17, [x0, #-1]
    //     0xb09860: and             x16, x17, x16, lsr #2
    //     0xb09864: tst             x16, HEAP, lsr #32
    //     0xb09868: b.eq            #0xb09870
    //     0xb0986c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb09870: r16 = "% OFF"
    //     0xb09870: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d98] "% OFF"
    //     0xb09874: ldr             x16, [x16, #0xd98]
    // 0xb09878: ArrayStore: r2[0] = r16  ; List_4
    //     0xb09878: stur            w16, [x2, #0x17]
    // 0xb0987c: str             x2, [SP]
    // 0xb09880: r0 = _interpolate()
    //     0xb09880: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb09884: ldr             x1, [fp, #0x18]
    // 0xb09888: stur            x0, [fp, #-0x18]
    // 0xb0988c: r0 = of()
    //     0xb0988c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb09890: LoadField: r1 = r0->field_87
    //     0xb09890: ldur            w1, [x0, #0x87]
    // 0xb09894: DecompressPointer r1
    //     0xb09894: add             x1, x1, HEAP, lsl #32
    // 0xb09898: LoadField: r0 = r1->field_2b
    //     0xb09898: ldur            w0, [x1, #0x2b]
    // 0xb0989c: DecompressPointer r0
    //     0xb0989c: add             x0, x0, HEAP, lsl #32
    // 0xb098a0: r16 = Instance_Color
    //     0xb098a0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb098a4: ldr             x16, [x16, #0x858]
    // 0xb098a8: r30 = 12.000000
    //     0xb098a8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb098ac: ldr             lr, [lr, #0x9e8]
    // 0xb098b0: stp             lr, x16, [SP]
    // 0xb098b4: mov             x1, x0
    // 0xb098b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb098b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb098bc: ldr             x4, [x4, #0x9b8]
    // 0xb098c0: r0 = copyWith()
    //     0xb098c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb098c4: stur            x0, [fp, #-0x40]
    // 0xb098c8: r0 = TextSpan()
    //     0xb098c8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb098cc: mov             x2, x0
    // 0xb098d0: ldur            x0, [fp, #-0x18]
    // 0xb098d4: stur            x2, [fp, #-0x58]
    // 0xb098d8: StoreField: r2->field_b = r0
    //     0xb098d8: stur            w0, [x2, #0xb]
    // 0xb098dc: r0 = Instance__DeferringMouseCursor
    //     0xb098dc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb098e0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb098e0: stur            w0, [x2, #0x17]
    // 0xb098e4: ldur            x1, [fp, #-0x40]
    // 0xb098e8: StoreField: r2->field_7 = r1
    //     0xb098e8: stur            w1, [x2, #7]
    // 0xb098ec: ldur            x3, [fp, #-0x50]
    // 0xb098f0: LoadField: r1 = r3->field_b
    //     0xb098f0: ldur            w1, [x3, #0xb]
    // 0xb098f4: LoadField: r4 = r3->field_f
    //     0xb098f4: ldur            w4, [x3, #0xf]
    // 0xb098f8: DecompressPointer r4
    //     0xb098f8: add             x4, x4, HEAP, lsl #32
    // 0xb098fc: LoadField: r5 = r4->field_b
    //     0xb098fc: ldur            w5, [x4, #0xb]
    // 0xb09900: r4 = LoadInt32Instr(r1)
    //     0xb09900: sbfx            x4, x1, #1, #0x1f
    // 0xb09904: stur            x4, [fp, #-0x68]
    // 0xb09908: r1 = LoadInt32Instr(r5)
    //     0xb09908: sbfx            x1, x5, #1, #0x1f
    // 0xb0990c: cmp             x4, x1
    // 0xb09910: b.ne            #0xb0991c
    // 0xb09914: mov             x1, x3
    // 0xb09918: r0 = _growToNextCapacity()
    //     0xb09918: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0991c: ldur            x2, [fp, #-0x50]
    // 0xb09920: ldur            x3, [fp, #-0x68]
    // 0xb09924: add             x0, x3, #1
    // 0xb09928: lsl             x1, x0, #1
    // 0xb0992c: StoreField: r2->field_b = r1
    //     0xb0992c: stur            w1, [x2, #0xb]
    // 0xb09930: LoadField: r1 = r2->field_f
    //     0xb09930: ldur            w1, [x2, #0xf]
    // 0xb09934: DecompressPointer r1
    //     0xb09934: add             x1, x1, HEAP, lsl #32
    // 0xb09938: ldur            x0, [fp, #-0x58]
    // 0xb0993c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0993c: add             x25, x1, x3, lsl #2
    //     0xb09940: add             x25, x25, #0xf
    //     0xb09944: str             w0, [x25]
    //     0xb09948: tbz             w0, #0, #0xb09964
    //     0xb0994c: ldurb           w16, [x1, #-1]
    //     0xb09950: ldurb           w17, [x0, #-1]
    //     0xb09954: and             x16, x17, x16, lsr #2
    //     0xb09958: tst             x16, HEAP, lsr #32
    //     0xb0995c: b.eq            #0xb09964
    //     0xb09960: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb09964: b               #0xb0996c
    // 0xb09968: ldur            x2, [fp, #-0x50]
    // 0xb0996c: ldur            x3, [fp, #-8]
    // 0xb09970: ldur            x5, [fp, #-0x28]
    // 0xb09974: ldur            x4, [fp, #-0x20]
    // 0xb09978: ldur            x1, [fp, #-0x30]
    // 0xb0997c: ldur            x0, [fp, #-0x38]
    // 0xb09980: r0 = TextSpan()
    //     0xb09980: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb09984: mov             x1, x0
    // 0xb09988: ldur            x0, [fp, #-0x50]
    // 0xb0998c: stur            x1, [fp, #-0x18]
    // 0xb09990: StoreField: r1->field_f = r0
    //     0xb09990: stur            w0, [x1, #0xf]
    // 0xb09994: r0 = Instance__DeferringMouseCursor
    //     0xb09994: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb09998: ArrayStore: r1[0] = r0  ; List_4
    //     0xb09998: stur            w0, [x1, #0x17]
    // 0xb0999c: r0 = RichText()
    //     0xb0999c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb099a0: mov             x1, x0
    // 0xb099a4: ldur            x2, [fp, #-0x18]
    // 0xb099a8: stur            x0, [fp, #-0x18]
    // 0xb099ac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb099ac: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb099b0: r0 = RichText()
    //     0xb099b0: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb099b4: r0 = Padding()
    //     0xb099b4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb099b8: mov             x3, x0
    // 0xb099bc: r0 = Instance_EdgeInsets
    //     0xb099bc: add             x0, PP, #0x55, lsl #12  ; [pp+0x55c98] Obj!EdgeInsets@d59061
    //     0xb099c0: ldr             x0, [x0, #0xc98]
    // 0xb099c4: stur            x3, [fp, #-0x40]
    // 0xb099c8: StoreField: r3->field_f = r0
    //     0xb099c8: stur            w0, [x3, #0xf]
    // 0xb099cc: ldur            x0, [fp, #-0x18]
    // 0xb099d0: StoreField: r3->field_b = r0
    //     0xb099d0: stur            w0, [x3, #0xb]
    // 0xb099d4: ldur            x0, [fp, #-8]
    // 0xb099d8: LoadField: r1 = r0->field_f
    //     0xb099d8: ldur            w1, [x0, #0xf]
    // 0xb099dc: DecompressPointer r1
    //     0xb099dc: add             x1, x1, HEAP, lsl #32
    // 0xb099e0: ldur            x2, [fp, #-0x48]
    // 0xb099e4: r0 = _buildAddToBagSection()
    //     0xb099e4: bl              #0xb09b1c  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildAddToBagSection
    // 0xb099e8: r1 = <FlexParentData>
    //     0xb099e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb099ec: ldr             x1, [x1, #0xe00]
    // 0xb099f0: stur            x0, [fp, #-8]
    // 0xb099f4: r0 = Expanded()
    //     0xb099f4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb099f8: mov             x3, x0
    // 0xb099fc: r0 = 1
    //     0xb099fc: movz            x0, #0x1
    // 0xb09a00: stur            x3, [fp, #-0x18]
    // 0xb09a04: StoreField: r3->field_13 = r0
    //     0xb09a04: stur            x0, [x3, #0x13]
    // 0xb09a08: r0 = Instance_FlexFit
    //     0xb09a08: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb09a0c: ldr             x0, [x0, #0xe08]
    // 0xb09a10: StoreField: r3->field_1b = r0
    //     0xb09a10: stur            w0, [x3, #0x1b]
    // 0xb09a14: ldur            x0, [fp, #-8]
    // 0xb09a18: StoreField: r3->field_b = r0
    //     0xb09a18: stur            w0, [x3, #0xb]
    // 0xb09a1c: r1 = Null
    //     0xb09a1c: mov             x1, NULL
    // 0xb09a20: r2 = 10
    //     0xb09a20: movz            x2, #0xa
    // 0xb09a24: r0 = AllocateArray()
    //     0xb09a24: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb09a28: mov             x2, x0
    // 0xb09a2c: ldur            x0, [fp, #-0x20]
    // 0xb09a30: stur            x2, [fp, #-8]
    // 0xb09a34: StoreField: r2->field_f = r0
    //     0xb09a34: stur            w0, [x2, #0xf]
    // 0xb09a38: ldur            x0, [fp, #-0x30]
    // 0xb09a3c: StoreField: r2->field_13 = r0
    //     0xb09a3c: stur            w0, [x2, #0x13]
    // 0xb09a40: ldur            x0, [fp, #-0x38]
    // 0xb09a44: ArrayStore: r2[0] = r0  ; List_4
    //     0xb09a44: stur            w0, [x2, #0x17]
    // 0xb09a48: ldur            x0, [fp, #-0x40]
    // 0xb09a4c: StoreField: r2->field_1b = r0
    //     0xb09a4c: stur            w0, [x2, #0x1b]
    // 0xb09a50: ldur            x0, [fp, #-0x18]
    // 0xb09a54: StoreField: r2->field_1f = r0
    //     0xb09a54: stur            w0, [x2, #0x1f]
    // 0xb09a58: r1 = <Widget>
    //     0xb09a58: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb09a5c: r0 = AllocateGrowableArray()
    //     0xb09a5c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb09a60: mov             x1, x0
    // 0xb09a64: ldur            x0, [fp, #-8]
    // 0xb09a68: stur            x1, [fp, #-0x18]
    // 0xb09a6c: StoreField: r1->field_f = r0
    //     0xb09a6c: stur            w0, [x1, #0xf]
    // 0xb09a70: r0 = 10
    //     0xb09a70: movz            x0, #0xa
    // 0xb09a74: StoreField: r1->field_b = r0
    //     0xb09a74: stur            w0, [x1, #0xb]
    // 0xb09a78: r0 = Column()
    //     0xb09a78: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb09a7c: mov             x3, x0
    // 0xb09a80: r0 = Instance_Axis
    //     0xb09a80: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb09a84: stur            x3, [fp, #-8]
    // 0xb09a88: StoreField: r3->field_f = r0
    //     0xb09a88: stur            w0, [x3, #0xf]
    // 0xb09a8c: r0 = Instance_MainAxisAlignment
    //     0xb09a8c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb09a90: ldr             x0, [x0, #0xa08]
    // 0xb09a94: StoreField: r3->field_13 = r0
    //     0xb09a94: stur            w0, [x3, #0x13]
    // 0xb09a98: r0 = Instance_MainAxisSize
    //     0xb09a98: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb09a9c: ldr             x0, [x0, #0xdd0]
    // 0xb09aa0: ArrayStore: r3[0] = r0  ; List_4
    //     0xb09aa0: stur            w0, [x3, #0x17]
    // 0xb09aa4: r0 = Instance_CrossAxisAlignment
    //     0xb09aa4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb09aa8: ldr             x0, [x0, #0x890]
    // 0xb09aac: StoreField: r3->field_1b = r0
    //     0xb09aac: stur            w0, [x3, #0x1b]
    // 0xb09ab0: r0 = Instance_VerticalDirection
    //     0xb09ab0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb09ab4: ldr             x0, [x0, #0xa20]
    // 0xb09ab8: StoreField: r3->field_23 = r0
    //     0xb09ab8: stur            w0, [x3, #0x23]
    // 0xb09abc: r0 = Instance_Clip
    //     0xb09abc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb09ac0: ldr             x0, [x0, #0x38]
    // 0xb09ac4: StoreField: r3->field_2b = r0
    //     0xb09ac4: stur            w0, [x3, #0x2b]
    // 0xb09ac8: StoreField: r3->field_2f = rZR
    //     0xb09ac8: stur            xzr, [x3, #0x2f]
    // 0xb09acc: ldur            x0, [fp, #-0x18]
    // 0xb09ad0: StoreField: r3->field_b = r0
    //     0xb09ad0: stur            w0, [x3, #0xb]
    // 0xb09ad4: ldur            x2, [fp, #-0x10]
    // 0xb09ad8: r1 = Function '<anonymous closure>':.
    //     0xb09ad8: add             x1, PP, #0x61, lsl #12  ; [pp+0x61ec0] AnonymousClosure: (0xb0aaac), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::build (0xb090fc)
    //     0xb09adc: ldr             x1, [x1, #0xec0]
    // 0xb09ae0: r0 = AllocateClosure()
    //     0xb09ae0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb09ae4: stur            x0, [fp, #-0x10]
    // 0xb09ae8: r0 = VisibilityDetector()
    //     0xb09ae8: bl              #0xa4f4ac  ; AllocateVisibilityDetectorStub -> VisibilityDetector (size=0x14)
    // 0xb09aec: ldur            x1, [fp, #-0x10]
    // 0xb09af0: StoreField: r0->field_f = r1
    //     0xb09af0: stur            w1, [x0, #0xf]
    // 0xb09af4: ldur            x1, [fp, #-8]
    // 0xb09af8: StoreField: r0->field_b = r1
    //     0xb09af8: stur            w1, [x0, #0xb]
    // 0xb09afc: ldur            x1, [fp, #-0x28]
    // 0xb09b00: StoreField: r0->field_7 = r1
    //     0xb09b00: stur            w1, [x0, #7]
    // 0xb09b04: LeaveFrame
    //     0xb09b04: mov             SP, fp
    //     0xb09b08: ldp             fp, lr, [SP], #0x10
    // 0xb09b0c: ret
    //     0xb09b0c: ret             
    // 0xb09b10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb09b10: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb09b14: b               #0xb092d0
    // 0xb09b18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb09b18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildAddToBagSection(/* No info */) {
    // ** addr: 0xb09b1c, size: 0x5d4
    // 0xb09b1c: EnterFrame
    //     0xb09b1c: stp             fp, lr, [SP, #-0x10]!
    //     0xb09b20: mov             fp, SP
    // 0xb09b24: AllocStack(0x68)
    //     0xb09b24: sub             SP, SP, #0x68
    // 0xb09b28: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb09b28: stur            x1, [fp, #-8]
    //     0xb09b2c: stur            x2, [fp, #-0x10]
    // 0xb09b30: CheckStackOverflow
    //     0xb09b30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb09b34: cmp             SP, x16
    //     0xb09b38: b.ls            #0xb0a0cc
    // 0xb09b3c: r1 = 2
    //     0xb09b3c: movz            x1, #0x2
    // 0xb09b40: r0 = AllocateContext()
    //     0xb09b40: bl              #0x16f6108  ; AllocateContextStub
    // 0xb09b44: mov             x3, x0
    // 0xb09b48: ldur            x2, [fp, #-8]
    // 0xb09b4c: stur            x3, [fp, #-0x18]
    // 0xb09b50: StoreField: r3->field_f = r2
    //     0xb09b50: stur            w2, [x3, #0xf]
    // 0xb09b54: LoadField: r0 = r2->field_b
    //     0xb09b54: ldur            w0, [x2, #0xb]
    // 0xb09b58: DecompressPointer r0
    //     0xb09b58: add             x0, x0, HEAP, lsl #32
    // 0xb09b5c: cmp             w0, NULL
    // 0xb09b60: b.eq            #0xb0a0d4
    // 0xb09b64: LoadField: r4 = r0->field_b
    //     0xb09b64: ldur            w4, [x0, #0xb]
    // 0xb09b68: DecompressPointer r4
    //     0xb09b68: add             x4, x4, HEAP, lsl #32
    // 0xb09b6c: ldur            x5, [fp, #-0x10]
    // 0xb09b70: r0 = BoxInt64Instr(r5)
    //     0xb09b70: sbfiz           x0, x5, #1, #0x1f
    //     0xb09b74: cmp             x5, x0, asr #1
    //     0xb09b78: b.eq            #0xb09b84
    //     0xb09b7c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb09b80: stur            x5, [x0, #7]
    // 0xb09b84: r1 = LoadClassIdInstr(r4)
    //     0xb09b84: ldur            x1, [x4, #-1]
    //     0xb09b88: ubfx            x1, x1, #0xc, #0x14
    // 0xb09b8c: stp             x0, x4, [SP]
    // 0xb09b90: mov             x0, x1
    // 0xb09b94: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb09b94: sub             lr, x0, #0xb7
    //     0xb09b98: ldr             lr, [x21, lr, lsl #3]
    //     0xb09b9c: blr             lr
    // 0xb09ba0: mov             x1, x0
    // 0xb09ba4: ldur            x2, [fp, #-0x18]
    // 0xb09ba8: stur            x1, [fp, #-0x28]
    // 0xb09bac: StoreField: r2->field_13 = r0
    //     0xb09bac: stur            w0, [x2, #0x13]
    //     0xb09bb0: ldurb           w16, [x2, #-1]
    //     0xb09bb4: ldurb           w17, [x0, #-1]
    //     0xb09bb8: and             x16, x17, x16, lsr #2
    //     0xb09bbc: tst             x16, HEAP, lsr #32
    //     0xb09bc0: b.eq            #0xb09bc8
    //     0xb09bc4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb09bc8: cmp             w1, NULL
    // 0xb09bcc: b.ne            #0xb09be0
    // 0xb09bd0: r0 = Instance_SizedBox
    //     0xb09bd0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb09bd4: LeaveFrame
    //     0xb09bd4: mov             SP, fp
    //     0xb09bd8: ldp             fp, lr, [SP], #0x10
    // 0xb09bdc: ret
    //     0xb09bdc: ret             
    // 0xb09be0: ldur            x0, [fp, #-8]
    // 0xb09be4: LoadField: r3 = r0->field_b
    //     0xb09be4: ldur            w3, [x0, #0xb]
    // 0xb09be8: DecompressPointer r3
    //     0xb09be8: add             x3, x3, HEAP, lsl #32
    // 0xb09bec: cmp             w3, NULL
    // 0xb09bf0: b.eq            #0xb0a0d8
    // 0xb09bf4: LoadField: r4 = r3->field_f
    //     0xb09bf4: ldur            w4, [x3, #0xf]
    // 0xb09bf8: DecompressPointer r4
    //     0xb09bf8: add             x4, x4, HEAP, lsl #32
    // 0xb09bfc: LoadField: r3 = r4->field_1f
    //     0xb09bfc: ldur            w3, [x4, #0x1f]
    // 0xb09c00: DecompressPointer r3
    //     0xb09c00: add             x3, x3, HEAP, lsl #32
    // 0xb09c04: cmp             w3, NULL
    // 0xb09c08: b.ne            #0xb09c14
    // 0xb09c0c: r3 = Null
    //     0xb09c0c: mov             x3, NULL
    // 0xb09c10: b               #0xb09c20
    // 0xb09c14: LoadField: r5 = r3->field_7
    //     0xb09c14: ldur            w5, [x3, #7]
    // 0xb09c18: DecompressPointer r5
    //     0xb09c18: add             x5, x5, HEAP, lsl #32
    // 0xb09c1c: mov             x3, x5
    // 0xb09c20: cmp             w3, NULL
    // 0xb09c24: b.ne            #0xb09c2c
    // 0xb09c28: r3 = false
    //     0xb09c28: add             x3, NULL, #0x30  ; false
    // 0xb09c2c: stur            x3, [fp, #-0x20]
    // 0xb09c30: LoadField: r5 = r4->field_3f
    //     0xb09c30: ldur            w5, [x4, #0x3f]
    // 0xb09c34: DecompressPointer r5
    //     0xb09c34: add             x5, x5, HEAP, lsl #32
    // 0xb09c38: cmp             w5, NULL
    // 0xb09c3c: b.ne            #0xb09c48
    // 0xb09c40: r4 = Null
    //     0xb09c40: mov             x4, NULL
    // 0xb09c44: b               #0xb09c50
    // 0xb09c48: LoadField: r4 = r5->field_23
    //     0xb09c48: ldur            w4, [x5, #0x23]
    // 0xb09c4c: DecompressPointer r4
    //     0xb09c4c: add             x4, x4, HEAP, lsl #32
    // 0xb09c50: cmp             w4, NULL
    // 0xb09c54: b.eq            #0xb0a068
    // 0xb09c58: tbnz            w4, #4, #0xb0a068
    // 0xb09c5c: r16 = <Size?>
    //     0xb09c5c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27768] TypeArguments: <Size?>
    //     0xb09c60: ldr             x16, [x16, #0x768]
    // 0xb09c64: r30 = Instance_Size
    //     0xb09c64: add             lr, PP, #0x55, lsl #12  ; [pp+0x55950] Obj!Size@d6c241
    //     0xb09c68: ldr             lr, [lr, #0x950]
    // 0xb09c6c: stp             lr, x16, [SP]
    // 0xb09c70: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb09c70: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb09c74: r0 = all()
    //     0xb09c74: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb09c78: mov             x1, x0
    // 0xb09c7c: ldur            x0, [fp, #-0x28]
    // 0xb09c80: stur            x1, [fp, #-0x30]
    // 0xb09c84: r17 = 275
    //     0xb09c84: movz            x17, #0x113
    // 0xb09c88: ldr             w2, [x0, x17]
    // 0xb09c8c: DecompressPointer r2
    //     0xb09c8c: add             x2, x2, HEAP, lsl #32
    // 0xb09c90: cmp             w2, NULL
    // 0xb09c94: b.eq            #0xb09c9c
    // 0xb09c98: tbnz            w2, #4, #0xb09cc0
    // 0xb09c9c: r16 = <Color>
    //     0xb09c9c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb09ca0: ldr             x16, [x16, #0xf80]
    // 0xb09ca4: r30 = Instance_MaterialColor
    //     0xb09ca4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xb09ca8: ldr             lr, [lr, #0xdc0]
    // 0xb09cac: stp             lr, x16, [SP]
    // 0xb09cb0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb09cb0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb09cb4: r0 = all()
    //     0xb09cb4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb09cb8: mov             x2, x0
    // 0xb09cbc: b               #0xb09cdc
    // 0xb09cc0: r16 = <Color>
    //     0xb09cc0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb09cc4: ldr             x16, [x16, #0xf80]
    // 0xb09cc8: r30 = Instance_Color
    //     0xb09cc8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb09ccc: stp             lr, x16, [SP]
    // 0xb09cd0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb09cd0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb09cd4: r0 = all()
    //     0xb09cd4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb09cd8: mov             x2, x0
    // 0xb09cdc: ldur            x0, [fp, #-0x28]
    // 0xb09ce0: stur            x2, [fp, #-0x38]
    // 0xb09ce4: r17 = 275
    //     0xb09ce4: movz            x17, #0x113
    // 0xb09ce8: ldr             w1, [x0, x17]
    // 0xb09cec: DecompressPointer r1
    //     0xb09cec: add             x1, x1, HEAP, lsl #32
    // 0xb09cf0: cmp             w1, NULL
    // 0xb09cf4: b.eq            #0xb09cfc
    // 0xb09cf8: tbnz            w1, #4, #0xb09d28
    // 0xb09cfc: r1 = Instance_MaterialColor
    //     0xb09cfc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xb09d00: ldr             x1, [x1, #0xdc0]
    // 0xb09d04: d0 = 0.200000
    //     0xb09d04: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb09d08: r0 = withOpacity()
    //     0xb09d08: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb09d0c: r16 = <Color>
    //     0xb09d0c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb09d10: ldr             x16, [x16, #0xf80]
    // 0xb09d14: stp             x0, x16, [SP]
    // 0xb09d18: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb09d18: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb09d1c: r0 = all()
    //     0xb09d1c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb09d20: mov             x2, x0
    // 0xb09d24: b               #0xb09d44
    // 0xb09d28: r16 = <Color>
    //     0xb09d28: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb09d2c: ldr             x16, [x16, #0xf80]
    // 0xb09d30: r30 = Instance_Color
    //     0xb09d30: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb09d34: stp             lr, x16, [SP]
    // 0xb09d38: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb09d38: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb09d3c: r0 = all()
    //     0xb09d3c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb09d40: mov             x2, x0
    // 0xb09d44: ldur            x0, [fp, #-0x28]
    // 0xb09d48: stur            x2, [fp, #-0x40]
    // 0xb09d4c: r17 = 275
    //     0xb09d4c: movz            x17, #0x113
    // 0xb09d50: ldr             w1, [x0, x17]
    // 0xb09d54: DecompressPointer r1
    //     0xb09d54: add             x1, x1, HEAP, lsl #32
    // 0xb09d58: cmp             w1, NULL
    // 0xb09d5c: b.eq            #0xb09d64
    // 0xb09d60: tbnz            w1, #4, #0xb09dbc
    // 0xb09d64: ldur            x3, [fp, #-8]
    // 0xb09d68: LoadField: r1 = r3->field_f
    //     0xb09d68: ldur            w1, [x3, #0xf]
    // 0xb09d6c: DecompressPointer r1
    //     0xb09d6c: add             x1, x1, HEAP, lsl #32
    // 0xb09d70: cmp             w1, NULL
    // 0xb09d74: b.eq            #0xb0a0dc
    // 0xb09d78: r0 = of()
    //     0xb09d78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb09d7c: LoadField: r1 = r0->field_5b
    //     0xb09d7c: ldur            w1, [x0, #0x5b]
    // 0xb09d80: DecompressPointer r1
    //     0xb09d80: add             x1, x1, HEAP, lsl #32
    // 0xb09d84: stur            x1, [fp, #-0x48]
    // 0xb09d88: r0 = BorderSide()
    //     0xb09d88: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb09d8c: mov             x1, x0
    // 0xb09d90: ldur            x0, [fp, #-0x48]
    // 0xb09d94: StoreField: r1->field_7 = r0
    //     0xb09d94: stur            w0, [x1, #7]
    // 0xb09d98: d0 = 1.000000
    //     0xb09d98: fmov            d0, #1.00000000
    // 0xb09d9c: StoreField: r1->field_b = d0
    //     0xb09d9c: stur            d0, [x1, #0xb]
    // 0xb09da0: r0 = Instance_BorderStyle
    //     0xb09da0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb09da4: ldr             x0, [x0, #0xf68]
    // 0xb09da8: StoreField: r1->field_13 = r0
    //     0xb09da8: stur            w0, [x1, #0x13]
    // 0xb09dac: d0 = -1.000000
    //     0xb09dac: fmov            d0, #-1.00000000
    // 0xb09db0: ArrayStore: r1[0] = d0  ; List_8
    //     0xb09db0: stur            d0, [x1, #0x17]
    // 0xb09db4: mov             x4, x1
    // 0xb09db8: b               #0xb09dc4
    // 0xb09dbc: r4 = Instance_BorderSide
    //     0xb09dbc: add             x4, PP, #0x55, lsl #12  ; [pp+0x55958] Obj!BorderSide@d62f31
    //     0xb09dc0: ldr             x4, [x4, #0x958]
    // 0xb09dc4: ldur            x0, [fp, #-0x28]
    // 0xb09dc8: ldur            x3, [fp, #-0x30]
    // 0xb09dcc: ldur            x2, [fp, #-0x38]
    // 0xb09dd0: ldur            x1, [fp, #-0x40]
    // 0xb09dd4: stur            x4, [fp, #-0x48]
    // 0xb09dd8: r0 = RoundedRectangleBorder()
    //     0xb09dd8: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb09ddc: mov             x1, x0
    // 0xb09de0: r0 = Instance_BorderRadius
    //     0xb09de0: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb09de4: ldr             x0, [x0, #0x460]
    // 0xb09de8: StoreField: r1->field_b = r0
    //     0xb09de8: stur            w0, [x1, #0xb]
    // 0xb09dec: ldur            x0, [fp, #-0x48]
    // 0xb09df0: StoreField: r1->field_7 = r0
    //     0xb09df0: stur            w0, [x1, #7]
    // 0xb09df4: r16 = <RoundedRectangleBorder>
    //     0xb09df4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb09df8: ldr             x16, [x16, #0xf78]
    // 0xb09dfc: stp             x1, x16, [SP]
    // 0xb09e00: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb09e00: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb09e04: r0 = all()
    //     0xb09e04: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb09e08: stur            x0, [fp, #-0x48]
    // 0xb09e0c: r0 = ButtonStyle()
    //     0xb09e0c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb09e10: mov             x1, x0
    // 0xb09e14: ldur            x0, [fp, #-0x40]
    // 0xb09e18: stur            x1, [fp, #-0x50]
    // 0xb09e1c: StoreField: r1->field_b = r0
    //     0xb09e1c: stur            w0, [x1, #0xb]
    // 0xb09e20: ldur            x0, [fp, #-0x38]
    // 0xb09e24: StoreField: r1->field_f = r0
    //     0xb09e24: stur            w0, [x1, #0xf]
    // 0xb09e28: ldur            x0, [fp, #-0x30]
    // 0xb09e2c: StoreField: r1->field_27 = r0
    //     0xb09e2c: stur            w0, [x1, #0x27]
    // 0xb09e30: ldur            x0, [fp, #-0x48]
    // 0xb09e34: StoreField: r1->field_43 = r0
    //     0xb09e34: stur            w0, [x1, #0x43]
    // 0xb09e38: r0 = TextButtonThemeData()
    //     0xb09e38: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb09e3c: mov             x2, x0
    // 0xb09e40: ldur            x0, [fp, #-0x50]
    // 0xb09e44: stur            x2, [fp, #-0x30]
    // 0xb09e48: StoreField: r2->field_7 = r0
    //     0xb09e48: stur            w0, [x2, #7]
    // 0xb09e4c: ldur            x0, [fp, #-0x28]
    // 0xb09e50: r17 = 275
    //     0xb09e50: movz            x17, #0x113
    // 0xb09e54: ldr             w1, [x0, x17]
    // 0xb09e58: DecompressPointer r1
    //     0xb09e58: add             x1, x1, HEAP, lsl #32
    // 0xb09e5c: cmp             w1, NULL
    // 0xb09e60: b.eq            #0xb09e68
    // 0xb09e64: tbnz            w1, #4, #0xb09f18
    // 0xb09e68: LoadField: r1 = r0->field_f
    //     0xb09e68: ldur            w1, [x0, #0xf]
    // 0xb09e6c: DecompressPointer r1
    //     0xb09e6c: add             x1, x1, HEAP, lsl #32
    // 0xb09e70: cmp             w1, NULL
    // 0xb09e74: b.ne            #0xb09e7c
    // 0xb09e78: r1 = ""
    //     0xb09e78: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb09e7c: ldur            x0, [fp, #-8]
    // 0xb09e80: r0 = capitalizeFirstWord()
    //     0xb09e80: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb09e84: mov             x2, x0
    // 0xb09e88: ldur            x0, [fp, #-8]
    // 0xb09e8c: stur            x2, [fp, #-0x38]
    // 0xb09e90: LoadField: r1 = r0->field_f
    //     0xb09e90: ldur            w1, [x0, #0xf]
    // 0xb09e94: DecompressPointer r1
    //     0xb09e94: add             x1, x1, HEAP, lsl #32
    // 0xb09e98: cmp             w1, NULL
    // 0xb09e9c: b.eq            #0xb0a0e0
    // 0xb09ea0: r0 = of()
    //     0xb09ea0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb09ea4: LoadField: r1 = r0->field_87
    //     0xb09ea4: ldur            w1, [x0, #0x87]
    // 0xb09ea8: DecompressPointer r1
    //     0xb09ea8: add             x1, x1, HEAP, lsl #32
    // 0xb09eac: LoadField: r0 = r1->field_7
    //     0xb09eac: ldur            w0, [x1, #7]
    // 0xb09eb0: DecompressPointer r0
    //     0xb09eb0: add             x0, x0, HEAP, lsl #32
    // 0xb09eb4: ldur            x2, [fp, #-8]
    // 0xb09eb8: stur            x0, [fp, #-0x40]
    // 0xb09ebc: LoadField: r1 = r2->field_f
    //     0xb09ebc: ldur            w1, [x2, #0xf]
    // 0xb09ec0: DecompressPointer r1
    //     0xb09ec0: add             x1, x1, HEAP, lsl #32
    // 0xb09ec4: cmp             w1, NULL
    // 0xb09ec8: b.eq            #0xb0a0e4
    // 0xb09ecc: r0 = of()
    //     0xb09ecc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb09ed0: LoadField: r1 = r0->field_5b
    //     0xb09ed0: ldur            w1, [x0, #0x5b]
    // 0xb09ed4: DecompressPointer r1
    //     0xb09ed4: add             x1, x1, HEAP, lsl #32
    // 0xb09ed8: r16 = 16.000000
    //     0xb09ed8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb09edc: ldr             x16, [x16, #0x188]
    // 0xb09ee0: stp             x1, x16, [SP]
    // 0xb09ee4: ldur            x1, [fp, #-0x40]
    // 0xb09ee8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb09ee8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb09eec: ldr             x4, [x4, #0xaa0]
    // 0xb09ef0: r0 = copyWith()
    //     0xb09ef0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb09ef4: stur            x0, [fp, #-0x40]
    // 0xb09ef8: r0 = Text()
    //     0xb09ef8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb09efc: mov             x1, x0
    // 0xb09f00: ldur            x0, [fp, #-0x38]
    // 0xb09f04: StoreField: r1->field_b = r0
    //     0xb09f04: stur            w0, [x1, #0xb]
    // 0xb09f08: ldur            x0, [fp, #-0x40]
    // 0xb09f0c: StoreField: r1->field_13 = r0
    //     0xb09f0c: stur            w0, [x1, #0x13]
    // 0xb09f10: mov             x3, x1
    // 0xb09f14: b               #0xb09fc8
    // 0xb09f18: ldur            x2, [fp, #-8]
    // 0xb09f1c: LoadField: r1 = r0->field_f
    //     0xb09f1c: ldur            w1, [x0, #0xf]
    // 0xb09f20: DecompressPointer r1
    //     0xb09f20: add             x1, x1, HEAP, lsl #32
    // 0xb09f24: cmp             w1, NULL
    // 0xb09f28: b.ne            #0xb09f30
    // 0xb09f2c: r1 = ""
    //     0xb09f2c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb09f30: r0 = capitalizeFirstWord()
    //     0xb09f30: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb09f34: mov             x2, x0
    // 0xb09f38: ldur            x0, [fp, #-8]
    // 0xb09f3c: stur            x2, [fp, #-0x28]
    // 0xb09f40: LoadField: r1 = r0->field_f
    //     0xb09f40: ldur            w1, [x0, #0xf]
    // 0xb09f44: DecompressPointer r1
    //     0xb09f44: add             x1, x1, HEAP, lsl #32
    // 0xb09f48: cmp             w1, NULL
    // 0xb09f4c: b.eq            #0xb0a0e8
    // 0xb09f50: r0 = of()
    //     0xb09f50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb09f54: LoadField: r1 = r0->field_87
    //     0xb09f54: ldur            w1, [x0, #0x87]
    // 0xb09f58: DecompressPointer r1
    //     0xb09f58: add             x1, x1, HEAP, lsl #32
    // 0xb09f5c: LoadField: r0 = r1->field_7
    //     0xb09f5c: ldur            w0, [x1, #7]
    // 0xb09f60: DecompressPointer r0
    //     0xb09f60: add             x0, x0, HEAP, lsl #32
    // 0xb09f64: ldur            x1, [fp, #-8]
    // 0xb09f68: stur            x0, [fp, #-0x38]
    // 0xb09f6c: LoadField: r2 = r1->field_f
    //     0xb09f6c: ldur            w2, [x1, #0xf]
    // 0xb09f70: DecompressPointer r2
    //     0xb09f70: add             x2, x2, HEAP, lsl #32
    // 0xb09f74: cmp             w2, NULL
    // 0xb09f78: b.eq            #0xb0a0ec
    // 0xb09f7c: mov             x1, x2
    // 0xb09f80: r0 = of()
    //     0xb09f80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb09f84: LoadField: r1 = r0->field_5b
    //     0xb09f84: ldur            w1, [x0, #0x5b]
    // 0xb09f88: DecompressPointer r1
    //     0xb09f88: add             x1, x1, HEAP, lsl #32
    // 0xb09f8c: r16 = 16.000000
    //     0xb09f8c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb09f90: ldr             x16, [x16, #0x188]
    // 0xb09f94: stp             x1, x16, [SP]
    // 0xb09f98: ldur            x1, [fp, #-0x38]
    // 0xb09f9c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb09f9c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb09fa0: ldr             x4, [x4, #0xaa0]
    // 0xb09fa4: r0 = copyWith()
    //     0xb09fa4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb09fa8: stur            x0, [fp, #-8]
    // 0xb09fac: r0 = Text()
    //     0xb09fac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb09fb0: mov             x1, x0
    // 0xb09fb4: ldur            x0, [fp, #-0x28]
    // 0xb09fb8: StoreField: r1->field_b = r0
    //     0xb09fb8: stur            w0, [x1, #0xb]
    // 0xb09fbc: ldur            x0, [fp, #-8]
    // 0xb09fc0: StoreField: r1->field_13 = r0
    //     0xb09fc0: stur            w0, [x1, #0x13]
    // 0xb09fc4: mov             x3, x1
    // 0xb09fc8: ldur            x0, [fp, #-0x30]
    // 0xb09fcc: ldur            x2, [fp, #-0x18]
    // 0xb09fd0: stur            x3, [fp, #-8]
    // 0xb09fd4: r1 = Function '<anonymous closure>':.
    //     0xb09fd4: add             x1, PP, #0x61, lsl #12  ; [pp+0x61f10] AnonymousClosure: (0xb0a0f0), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildAddToBagSection (0xb09b1c)
    //     0xb09fd8: ldr             x1, [x1, #0xf10]
    // 0xb09fdc: r0 = AllocateClosure()
    //     0xb09fdc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb09fe0: stur            x0, [fp, #-0x18]
    // 0xb09fe4: r0 = TextButton()
    //     0xb09fe4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb09fe8: mov             x1, x0
    // 0xb09fec: ldur            x0, [fp, #-0x18]
    // 0xb09ff0: stur            x1, [fp, #-0x28]
    // 0xb09ff4: StoreField: r1->field_b = r0
    //     0xb09ff4: stur            w0, [x1, #0xb]
    // 0xb09ff8: r0 = false
    //     0xb09ff8: add             x0, NULL, #0x30  ; false
    // 0xb09ffc: StoreField: r1->field_27 = r0
    //     0xb09ffc: stur            w0, [x1, #0x27]
    // 0xb0a000: r2 = true
    //     0xb0a000: add             x2, NULL, #0x20  ; true
    // 0xb0a004: StoreField: r1->field_2f = r2
    //     0xb0a004: stur            w2, [x1, #0x2f]
    // 0xb0a008: ldur            x2, [fp, #-8]
    // 0xb0a00c: StoreField: r1->field_37 = r2
    //     0xb0a00c: stur            w2, [x1, #0x37]
    // 0xb0a010: r0 = TextButtonTheme()
    //     0xb0a010: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb0a014: mov             x1, x0
    // 0xb0a018: ldur            x0, [fp, #-0x30]
    // 0xb0a01c: stur            x1, [fp, #-8]
    // 0xb0a020: StoreField: r1->field_f = r0
    //     0xb0a020: stur            w0, [x1, #0xf]
    // 0xb0a024: ldur            x0, [fp, #-0x28]
    // 0xb0a028: StoreField: r1->field_b = r0
    //     0xb0a028: stur            w0, [x1, #0xb]
    // 0xb0a02c: r0 = Container()
    //     0xb0a02c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb0a030: stur            x0, [fp, #-0x18]
    // 0xb0a034: r16 = Instance_EdgeInsets
    //     0xb0a034: add             x16, PP, #0x55, lsl #12  ; [pp+0x55968] Obj!EdgeInsets@d58581
    //     0xb0a038: ldr             x16, [x16, #0x968]
    // 0xb0a03c: r30 = Instance_Alignment
    //     0xb0a03c: add             lr, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xb0a040: ldr             lr, [lr, #0xcb0]
    // 0xb0a044: stp             lr, x16, [SP, #8]
    // 0xb0a048: ldur            x16, [fp, #-8]
    // 0xb0a04c: str             x16, [SP]
    // 0xb0a050: mov             x1, x0
    // 0xb0a054: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x2, child, 0x3, margin, 0x1, null]
    //     0xb0a054: add             x4, PP, #0x61, lsl #12  ; [pp+0x61f18] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x2, "child", 0x3, "margin", 0x1, Null]
    //     0xb0a058: ldr             x4, [x4, #0xf18]
    // 0xb0a05c: r0 = Container()
    //     0xb0a05c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb0a060: ldur            x1, [fp, #-0x18]
    // 0xb0a064: b               #0xb0a080
    // 0xb0a068: r0 = Container()
    //     0xb0a068: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb0a06c: mov             x1, x0
    // 0xb0a070: stur            x0, [fp, #-8]
    // 0xb0a074: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0a074: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0a078: r0 = Container()
    //     0xb0a078: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb0a07c: ldur            x1, [fp, #-8]
    // 0xb0a080: ldur            x0, [fp, #-0x20]
    // 0xb0a084: stur            x1, [fp, #-8]
    // 0xb0a088: r0 = Visibility()
    //     0xb0a088: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb0a08c: ldur            x1, [fp, #-8]
    // 0xb0a090: StoreField: r0->field_b = r1
    //     0xb0a090: stur            w1, [x0, #0xb]
    // 0xb0a094: r1 = Instance_SizedBox
    //     0xb0a094: ldr             x1, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb0a098: StoreField: r0->field_f = r1
    //     0xb0a098: stur            w1, [x0, #0xf]
    // 0xb0a09c: ldur            x1, [fp, #-0x20]
    // 0xb0a0a0: StoreField: r0->field_13 = r1
    //     0xb0a0a0: stur            w1, [x0, #0x13]
    // 0xb0a0a4: r1 = false
    //     0xb0a0a4: add             x1, NULL, #0x30  ; false
    // 0xb0a0a8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb0a0a8: stur            w1, [x0, #0x17]
    // 0xb0a0ac: StoreField: r0->field_1b = r1
    //     0xb0a0ac: stur            w1, [x0, #0x1b]
    // 0xb0a0b0: StoreField: r0->field_1f = r1
    //     0xb0a0b0: stur            w1, [x0, #0x1f]
    // 0xb0a0b4: StoreField: r0->field_23 = r1
    //     0xb0a0b4: stur            w1, [x0, #0x23]
    // 0xb0a0b8: StoreField: r0->field_27 = r1
    //     0xb0a0b8: stur            w1, [x0, #0x27]
    // 0xb0a0bc: StoreField: r0->field_2b = r1
    //     0xb0a0bc: stur            w1, [x0, #0x2b]
    // 0xb0a0c0: LeaveFrame
    //     0xb0a0c0: mov             SP, fp
    //     0xb0a0c4: ldp             fp, lr, [SP], #0x10
    // 0xb0a0c8: ret
    //     0xb0a0c8: ret             
    // 0xb0a0cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0a0cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0a0d0: b               #0xb09b3c
    // 0xb0a0d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0a0d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0a0d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0a0d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0a0dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0a0dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0a0e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0a0e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0a0e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0a0e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0a0e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0a0e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0a0ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0a0ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb0a0f0, size: 0xf8
    // 0xb0a0f0: EnterFrame
    //     0xb0a0f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb0a0f4: mov             fp, SP
    // 0xb0a0f8: AllocStack(0x20)
    //     0xb0a0f8: sub             SP, SP, #0x20
    // 0xb0a0fc: SetupParameters()
    //     0xb0a0fc: ldr             x0, [fp, #0x10]
    //     0xb0a100: ldur            w1, [x0, #0x17]
    //     0xb0a104: add             x1, x1, HEAP, lsl #32
    //     0xb0a108: stur            x1, [fp, #-0x10]
    // 0xb0a10c: CheckStackOverflow
    //     0xb0a10c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0a110: cmp             SP, x16
    //     0xb0a114: b.ls            #0xb0a1d4
    // 0xb0a118: LoadField: r0 = r1->field_13
    //     0xb0a118: ldur            w0, [x1, #0x13]
    // 0xb0a11c: DecompressPointer r0
    //     0xb0a11c: add             x0, x0, HEAP, lsl #32
    // 0xb0a120: stur            x0, [fp, #-8]
    // 0xb0a124: cmp             w0, NULL
    // 0xb0a128: b.eq            #0xb0a1dc
    // 0xb0a12c: r17 = 275
    //     0xb0a12c: movz            x17, #0x113
    // 0xb0a130: ldr             w2, [x0, x17]
    // 0xb0a134: DecompressPointer r2
    //     0xb0a134: add             x2, x2, HEAP, lsl #32
    // 0xb0a138: cmp             w2, NULL
    // 0xb0a13c: b.eq            #0xb0a144
    // 0xb0a140: tbz             w2, #4, #0xb0a1c4
    // 0xb0a144: LoadField: r2 = r1->field_f
    //     0xb0a144: ldur            w2, [x1, #0xf]
    // 0xb0a148: DecompressPointer r2
    //     0xb0a148: add             x2, x2, HEAP, lsl #32
    // 0xb0a14c: LoadField: r3 = r2->field_b
    //     0xb0a14c: ldur            w3, [x2, #0xb]
    // 0xb0a150: DecompressPointer r3
    //     0xb0a150: add             x3, x3, HEAP, lsl #32
    // 0xb0a154: cmp             w3, NULL
    // 0xb0a158: b.eq            #0xb0a1e0
    // 0xb0a15c: LoadField: r2 = r3->field_2b
    //     0xb0a15c: ldur            w2, [x3, #0x2b]
    // 0xb0a160: DecompressPointer r2
    //     0xb0a160: add             x2, x2, HEAP, lsl #32
    // 0xb0a164: r16 = "add_to_bag"
    //     0xb0a164: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xb0a168: ldr             x16, [x16, #0xa38]
    // 0xb0a16c: stp             x16, x2, [SP]
    // 0xb0a170: r4 = 0
    //     0xb0a170: movz            x4, #0
    // 0xb0a174: ldr             x0, [SP, #8]
    // 0xb0a178: r16 = UnlinkedCall_0x613b5c
    //     0xb0a178: add             x16, PP, #0x61, lsl #12  ; [pp+0x61f20] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb0a17c: add             x16, x16, #0xf20
    // 0xb0a180: ldp             x5, lr, [x16]
    // 0xb0a184: blr             lr
    // 0xb0a188: ldur            x0, [fp, #-0x10]
    // 0xb0a18c: LoadField: r1 = r0->field_f
    //     0xb0a18c: ldur            w1, [x0, #0xf]
    // 0xb0a190: DecompressPointer r1
    //     0xb0a190: add             x1, x1, HEAP, lsl #32
    // 0xb0a194: LoadField: r0 = r1->field_b
    //     0xb0a194: ldur            w0, [x1, #0xb]
    // 0xb0a198: DecompressPointer r0
    //     0xb0a198: add             x0, x0, HEAP, lsl #32
    // 0xb0a19c: cmp             w0, NULL
    // 0xb0a1a0: b.eq            #0xb0a1e4
    // 0xb0a1a4: LoadField: r1 = r0->field_37
    //     0xb0a1a4: ldur            w1, [x0, #0x37]
    // 0xb0a1a8: DecompressPointer r1
    //     0xb0a1a8: add             x1, x1, HEAP, lsl #32
    // 0xb0a1ac: ldur            x16, [fp, #-8]
    // 0xb0a1b0: stp             x16, x1, [SP]
    // 0xb0a1b4: mov             x0, x1
    // 0xb0a1b8: ClosureCall
    //     0xb0a1b8: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb0a1bc: ldur            x2, [x0, #0x1f]
    //     0xb0a1c0: blr             x2
    // 0xb0a1c4: r0 = Null
    //     0xb0a1c4: mov             x0, NULL
    // 0xb0a1c8: LeaveFrame
    //     0xb0a1c8: mov             SP, fp
    //     0xb0a1cc: ldp             fp, lr, [SP], #0x10
    // 0xb0a1d0: ret
    //     0xb0a1d0: ret             
    // 0xb0a1d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0a1d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0a1d8: b               #0xb0a118
    // 0xb0a1dc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb0a1dc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb0a1e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0a1e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0a1e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0a1e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildRatingSection(/* No info */) {
    // ** addr: 0xb0a1e8, size: 0x8c4
    // 0xb0a1e8: EnterFrame
    //     0xb0a1e8: stp             fp, lr, [SP, #-0x10]!
    //     0xb0a1ec: mov             fp, SP
    // 0xb0a1f0: AllocStack(0x50)
    //     0xb0a1f0: sub             SP, SP, #0x50
    // 0xb0a1f4: SetupParameters(_ProductGridItemState this /* r1 => r3, fp-0x8 */)
    //     0xb0a1f4: mov             x3, x1
    //     0xb0a1f8: stur            x1, [fp, #-8]
    // 0xb0a1fc: CheckStackOverflow
    //     0xb0a1fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0a200: cmp             SP, x16
    //     0xb0a204: b.ls            #0xb0aa60
    // 0xb0a208: LoadField: r0 = r3->field_b
    //     0xb0a208: ldur            w0, [x3, #0xb]
    // 0xb0a20c: DecompressPointer r0
    //     0xb0a20c: add             x0, x0, HEAP, lsl #32
    // 0xb0a210: cmp             w0, NULL
    // 0xb0a214: b.eq            #0xb0aa68
    // 0xb0a218: LoadField: r4 = r0->field_b
    //     0xb0a218: ldur            w4, [x0, #0xb]
    // 0xb0a21c: DecompressPointer r4
    //     0xb0a21c: add             x4, x4, HEAP, lsl #32
    // 0xb0a220: r0 = BoxInt64Instr(r2)
    //     0xb0a220: sbfiz           x0, x2, #1, #0x1f
    //     0xb0a224: cmp             x2, x0, asr #1
    //     0xb0a228: b.eq            #0xb0a234
    //     0xb0a22c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb0a230: stur            x2, [x0, #7]
    // 0xb0a234: r1 = LoadClassIdInstr(r4)
    //     0xb0a234: ldur            x1, [x4, #-1]
    //     0xb0a238: ubfx            x1, x1, #0xc, #0x14
    // 0xb0a23c: stp             x0, x4, [SP]
    // 0xb0a240: mov             x0, x1
    // 0xb0a244: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb0a244: sub             lr, x0, #0xb7
    //     0xb0a248: ldr             lr, [x21, lr, lsl #3]
    //     0xb0a24c: blr             lr
    // 0xb0a250: mov             x1, x0
    // 0xb0a254: stur            x1, [fp, #-0x10]
    // 0xb0a258: cmp             w1, NULL
    // 0xb0a25c: b.ne            #0xb0a270
    // 0xb0a260: r0 = Instance_SizedBox
    //     0xb0a260: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb0a264: LeaveFrame
    //     0xb0a264: mov             SP, fp
    //     0xb0a268: ldp             fp, lr, [SP], #0x10
    // 0xb0a26c: ret
    //     0xb0a26c: ret             
    // 0xb0a270: LoadField: r0 = r1->field_e3
    //     0xb0a270: ldur            w0, [x1, #0xe3]
    // 0xb0a274: DecompressPointer r0
    //     0xb0a274: add             x0, x0, HEAP, lsl #32
    // 0xb0a278: cmp             w0, NULL
    // 0xb0a27c: b.ne            #0xb0a288
    // 0xb0a280: r0 = Null
    //     0xb0a280: mov             x0, NULL
    // 0xb0a284: b               #0xb0a294
    // 0xb0a288: LoadField: r2 = r0->field_7
    //     0xb0a288: ldur            w2, [x0, #7]
    // 0xb0a28c: DecompressPointer r2
    //     0xb0a28c: add             x2, x2, HEAP, lsl #32
    // 0xb0a290: mov             x0, x2
    // 0xb0a294: r2 = LoadClassIdInstr(r0)
    //     0xb0a294: ldur            x2, [x0, #-1]
    //     0xb0a298: ubfx            x2, x2, #0xc, #0x14
    // 0xb0a29c: r16 = 0.000000
    //     0xb0a29c: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb0a2a0: stp             x16, x0, [SP]
    // 0xb0a2a4: mov             x0, x2
    // 0xb0a2a8: mov             lr, x0
    // 0xb0a2ac: ldr             lr, [x21, lr, lsl #3]
    // 0xb0a2b0: blr             lr
    // 0xb0a2b4: eor             x1, x0, #0x10
    // 0xb0a2b8: tbz             w1, #4, #0xb0a2cc
    // 0xb0a2bc: r0 = Instance_SizedBox
    //     0xb0a2bc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb0a2c0: LeaveFrame
    //     0xb0a2c0: mov             SP, fp
    //     0xb0a2c4: ldp             fp, lr, [SP], #0x10
    // 0xb0a2c8: ret
    //     0xb0a2c8: ret             
    // 0xb0a2cc: ldur            x1, [fp, #-0x10]
    // 0xb0a2d0: LoadField: r0 = r1->field_e3
    //     0xb0a2d0: ldur            w0, [x1, #0xe3]
    // 0xb0a2d4: DecompressPointer r0
    //     0xb0a2d4: add             x0, x0, HEAP, lsl #32
    // 0xb0a2d8: cmp             w0, NULL
    // 0xb0a2dc: b.ne            #0xb0a2e8
    // 0xb0a2e0: r2 = Null
    //     0xb0a2e0: mov             x2, NULL
    // 0xb0a2e4: b               #0xb0a2f0
    // 0xb0a2e8: LoadField: r2 = r0->field_7
    //     0xb0a2e8: ldur            w2, [x0, #7]
    // 0xb0a2ec: DecompressPointer r2
    //     0xb0a2ec: add             x2, x2, HEAP, lsl #32
    // 0xb0a2f0: cmp             w2, NULL
    // 0xb0a2f4: r16 = true
    //     0xb0a2f4: add             x16, NULL, #0x20  ; true
    // 0xb0a2f8: r17 = false
    //     0xb0a2f8: add             x17, NULL, #0x30  ; false
    // 0xb0a2fc: csel            x3, x16, x17, ne
    // 0xb0a300: stur            x3, [fp, #-0x18]
    // 0xb0a304: cmp             w0, NULL
    // 0xb0a308: b.ne            #0xb0a314
    // 0xb0a30c: r0 = Null
    //     0xb0a30c: mov             x0, NULL
    // 0xb0a310: b               #0xb0a320
    // 0xb0a314: LoadField: r2 = r0->field_f
    //     0xb0a314: ldur            w2, [x0, #0xf]
    // 0xb0a318: DecompressPointer r2
    //     0xb0a318: add             x2, x2, HEAP, lsl #32
    // 0xb0a31c: mov             x0, x2
    // 0xb0a320: r2 = LoadClassIdInstr(r0)
    //     0xb0a320: ldur            x2, [x0, #-1]
    //     0xb0a324: ubfx            x2, x2, #0xc, #0x14
    // 0xb0a328: r16 = "product_rating"
    //     0xb0a328: add             x16, PP, #0x23, lsl #12  ; [pp+0x23f20] "product_rating"
    //     0xb0a32c: ldr             x16, [x16, #0xf20]
    // 0xb0a330: stp             x16, x0, [SP]
    // 0xb0a334: mov             x0, x2
    // 0xb0a338: mov             lr, x0
    // 0xb0a33c: ldr             lr, [x21, lr, lsl #3]
    // 0xb0a340: blr             lr
    // 0xb0a344: tbnz            w0, #4, #0xb0a774
    // 0xb0a348: ldur            x0, [fp, #-0x10]
    // 0xb0a34c: LoadField: r1 = r0->field_e3
    //     0xb0a34c: ldur            w1, [x0, #0xe3]
    // 0xb0a350: DecompressPointer r1
    //     0xb0a350: add             x1, x1, HEAP, lsl #32
    // 0xb0a354: cmp             w1, NULL
    // 0xb0a358: b.ne            #0xb0a364
    // 0xb0a35c: r2 = Null
    //     0xb0a35c: mov             x2, NULL
    // 0xb0a360: b               #0xb0a36c
    // 0xb0a364: LoadField: r2 = r1->field_7
    //     0xb0a364: ldur            w2, [x1, #7]
    // 0xb0a368: DecompressPointer r2
    //     0xb0a368: add             x2, x2, HEAP, lsl #32
    // 0xb0a36c: cmp             w2, NULL
    // 0xb0a370: b.ne            #0xb0a37c
    // 0xb0a374: d1 = 0.000000
    //     0xb0a374: eor             v1.16b, v1.16b, v1.16b
    // 0xb0a378: b               #0xb0a384
    // 0xb0a37c: LoadField: d0 = r2->field_7
    //     0xb0a37c: ldur            d0, [x2, #7]
    // 0xb0a380: mov             v1.16b, v0.16b
    // 0xb0a384: d0 = 4.000000
    //     0xb0a384: fmov            d0, #4.00000000
    // 0xb0a388: fcmp            d1, d0
    // 0xb0a38c: b.lt            #0xb0a39c
    // 0xb0a390: r1 = Instance_Color
    //     0xb0a390: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb0a394: ldr             x1, [x1, #0x858]
    // 0xb0a398: b               #0xb0a448
    // 0xb0a39c: cmp             w1, NULL
    // 0xb0a3a0: b.ne            #0xb0a3ac
    // 0xb0a3a4: r2 = Null
    //     0xb0a3a4: mov             x2, NULL
    // 0xb0a3a8: b               #0xb0a3b4
    // 0xb0a3ac: LoadField: r2 = r1->field_7
    //     0xb0a3ac: ldur            w2, [x1, #7]
    // 0xb0a3b0: DecompressPointer r2
    //     0xb0a3b0: add             x2, x2, HEAP, lsl #32
    // 0xb0a3b4: cmp             w2, NULL
    // 0xb0a3b8: b.ne            #0xb0a3c4
    // 0xb0a3bc: d1 = 0.000000
    //     0xb0a3bc: eor             v1.16b, v1.16b, v1.16b
    // 0xb0a3c0: b               #0xb0a3cc
    // 0xb0a3c4: LoadField: d0 = r2->field_7
    //     0xb0a3c4: ldur            d0, [x2, #7]
    // 0xb0a3c8: mov             v1.16b, v0.16b
    // 0xb0a3cc: d0 = 3.500000
    //     0xb0a3cc: fmov            d0, #3.50000000
    // 0xb0a3d0: fcmp            d1, d0
    // 0xb0a3d4: b.lt            #0xb0a3f0
    // 0xb0a3d8: r1 = Instance_Color
    //     0xb0a3d8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb0a3dc: ldr             x1, [x1, #0x858]
    // 0xb0a3e0: d0 = 0.700000
    //     0xb0a3e0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb0a3e4: ldr             d0, [x17, #0xf48]
    // 0xb0a3e8: r0 = withOpacity()
    //     0xb0a3e8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb0a3ec: b               #0xb0a440
    // 0xb0a3f0: cmp             w1, NULL
    // 0xb0a3f4: b.ne            #0xb0a400
    // 0xb0a3f8: r0 = Null
    //     0xb0a3f8: mov             x0, NULL
    // 0xb0a3fc: b               #0xb0a408
    // 0xb0a400: LoadField: r0 = r1->field_7
    //     0xb0a400: ldur            w0, [x1, #7]
    // 0xb0a404: DecompressPointer r0
    //     0xb0a404: add             x0, x0, HEAP, lsl #32
    // 0xb0a408: cmp             w0, NULL
    // 0xb0a40c: b.ne            #0xb0a418
    // 0xb0a410: d1 = 0.000000
    //     0xb0a410: eor             v1.16b, v1.16b, v1.16b
    // 0xb0a414: b               #0xb0a420
    // 0xb0a418: LoadField: d0 = r0->field_7
    //     0xb0a418: ldur            d0, [x0, #7]
    // 0xb0a41c: mov             v1.16b, v0.16b
    // 0xb0a420: d0 = 2.000000
    //     0xb0a420: fmov            d0, #2.00000000
    // 0xb0a424: fcmp            d1, d0
    // 0xb0a428: b.lt            #0xb0a438
    // 0xb0a42c: r0 = Instance_Color
    //     0xb0a42c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb0a430: ldr             x0, [x0, #0x860]
    // 0xb0a434: b               #0xb0a440
    // 0xb0a438: r0 = Instance_Color
    //     0xb0a438: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb0a43c: ldr             x0, [x0, #0x50]
    // 0xb0a440: mov             x1, x0
    // 0xb0a444: ldur            x0, [fp, #-0x10]
    // 0xb0a448: stur            x1, [fp, #-0x20]
    // 0xb0a44c: r0 = ColorFilter()
    //     0xb0a44c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb0a450: mov             x1, x0
    // 0xb0a454: ldur            x0, [fp, #-0x20]
    // 0xb0a458: stur            x1, [fp, #-0x28]
    // 0xb0a45c: StoreField: r1->field_7 = r0
    //     0xb0a45c: stur            w0, [x1, #7]
    // 0xb0a460: r0 = Instance_BlendMode
    //     0xb0a460: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb0a464: ldr             x0, [x0, #0xb30]
    // 0xb0a468: StoreField: r1->field_b = r0
    //     0xb0a468: stur            w0, [x1, #0xb]
    // 0xb0a46c: r2 = 1
    //     0xb0a46c: movz            x2, #0x1
    // 0xb0a470: StoreField: r1->field_13 = r2
    //     0xb0a470: stur            x2, [x1, #0x13]
    // 0xb0a474: r0 = SvgPicture()
    //     0xb0a474: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb0a478: stur            x0, [fp, #-0x20]
    // 0xb0a47c: ldur            x16, [fp, #-0x28]
    // 0xb0a480: str             x16, [SP]
    // 0xb0a484: mov             x1, x0
    // 0xb0a488: r2 = "assets/images/green_star.svg"
    //     0xb0a488: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb0a48c: ldr             x2, [x2, #0x9a0]
    // 0xb0a490: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb0a490: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb0a494: ldr             x4, [x4, #0xa38]
    // 0xb0a498: r0 = SvgPicture.asset()
    //     0xb0a498: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb0a49c: ldur            x0, [fp, #-0x10]
    // 0xb0a4a0: LoadField: r1 = r0->field_e3
    //     0xb0a4a0: ldur            w1, [x0, #0xe3]
    // 0xb0a4a4: DecompressPointer r1
    //     0xb0a4a4: add             x1, x1, HEAP, lsl #32
    // 0xb0a4a8: cmp             w1, NULL
    // 0xb0a4ac: b.ne            #0xb0a4b8
    // 0xb0a4b0: r0 = Null
    //     0xb0a4b0: mov             x0, NULL
    // 0xb0a4b4: b               #0xb0a4dc
    // 0xb0a4b8: LoadField: r2 = r1->field_7
    //     0xb0a4b8: ldur            w2, [x1, #7]
    // 0xb0a4bc: DecompressPointer r2
    //     0xb0a4bc: add             x2, x2, HEAP, lsl #32
    // 0xb0a4c0: cmp             w2, NULL
    // 0xb0a4c4: b.ne            #0xb0a4d0
    // 0xb0a4c8: r0 = Null
    //     0xb0a4c8: mov             x0, NULL
    // 0xb0a4cc: b               #0xb0a4dc
    // 0xb0a4d0: mov             x1, x2
    // 0xb0a4d4: r2 = 1
    //     0xb0a4d4: movz            x2, #0x1
    // 0xb0a4d8: r0 = toStringAsFixed()
    //     0xb0a4d8: bl              #0x7c1ef8  ; [dart:core] _Double::toStringAsFixed
    // 0xb0a4dc: cmp             w0, NULL
    // 0xb0a4e0: b.ne            #0xb0a4ec
    // 0xb0a4e4: r4 = ""
    //     0xb0a4e4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0a4e8: b               #0xb0a4f0
    // 0xb0a4ec: mov             x4, x0
    // 0xb0a4f0: ldur            x3, [fp, #-8]
    // 0xb0a4f4: ldur            x0, [fp, #-0x10]
    // 0xb0a4f8: ldur            x2, [fp, #-0x20]
    // 0xb0a4fc: stur            x4, [fp, #-0x28]
    // 0xb0a500: LoadField: r1 = r3->field_f
    //     0xb0a500: ldur            w1, [x3, #0xf]
    // 0xb0a504: DecompressPointer r1
    //     0xb0a504: add             x1, x1, HEAP, lsl #32
    // 0xb0a508: cmp             w1, NULL
    // 0xb0a50c: b.eq            #0xb0aa6c
    // 0xb0a510: r0 = of()
    //     0xb0a510: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0a514: LoadField: r1 = r0->field_87
    //     0xb0a514: ldur            w1, [x0, #0x87]
    // 0xb0a518: DecompressPointer r1
    //     0xb0a518: add             x1, x1, HEAP, lsl #32
    // 0xb0a51c: LoadField: r0 = r1->field_7
    //     0xb0a51c: ldur            w0, [x1, #7]
    // 0xb0a520: DecompressPointer r0
    //     0xb0a520: add             x0, x0, HEAP, lsl #32
    // 0xb0a524: r16 = 12.000000
    //     0xb0a524: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb0a528: ldr             x16, [x16, #0x9e8]
    // 0xb0a52c: r30 = Instance_Color
    //     0xb0a52c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb0a530: stp             lr, x16, [SP]
    // 0xb0a534: mov             x1, x0
    // 0xb0a538: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0a538: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0a53c: ldr             x4, [x4, #0xaa0]
    // 0xb0a540: r0 = copyWith()
    //     0xb0a540: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0a544: stur            x0, [fp, #-0x30]
    // 0xb0a548: r0 = Text()
    //     0xb0a548: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0a54c: mov             x3, x0
    // 0xb0a550: ldur            x0, [fp, #-0x28]
    // 0xb0a554: stur            x3, [fp, #-0x38]
    // 0xb0a558: StoreField: r3->field_b = r0
    //     0xb0a558: stur            w0, [x3, #0xb]
    // 0xb0a55c: ldur            x0, [fp, #-0x30]
    // 0xb0a560: StoreField: r3->field_13 = r0
    //     0xb0a560: stur            w0, [x3, #0x13]
    // 0xb0a564: r1 = Null
    //     0xb0a564: mov             x1, NULL
    // 0xb0a568: r2 = 4
    //     0xb0a568: movz            x2, #0x4
    // 0xb0a56c: r0 = AllocateArray()
    //     0xb0a56c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0a570: mov             x2, x0
    // 0xb0a574: ldur            x0, [fp, #-0x20]
    // 0xb0a578: stur            x2, [fp, #-0x28]
    // 0xb0a57c: StoreField: r2->field_f = r0
    //     0xb0a57c: stur            w0, [x2, #0xf]
    // 0xb0a580: ldur            x0, [fp, #-0x38]
    // 0xb0a584: StoreField: r2->field_13 = r0
    //     0xb0a584: stur            w0, [x2, #0x13]
    // 0xb0a588: r1 = <Widget>
    //     0xb0a588: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0a58c: r0 = AllocateGrowableArray()
    //     0xb0a58c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0a590: mov             x3, x0
    // 0xb0a594: ldur            x0, [fp, #-0x28]
    // 0xb0a598: stur            x3, [fp, #-0x30]
    // 0xb0a59c: StoreField: r3->field_f = r0
    //     0xb0a59c: stur            w0, [x3, #0xf]
    // 0xb0a5a0: r0 = 4
    //     0xb0a5a0: movz            x0, #0x4
    // 0xb0a5a4: StoreField: r3->field_b = r0
    //     0xb0a5a4: stur            w0, [x3, #0xb]
    // 0xb0a5a8: ldur            x2, [fp, #-0x10]
    // 0xb0a5ac: LoadField: r0 = r2->field_e3
    //     0xb0a5ac: ldur            w0, [x2, #0xe3]
    // 0xb0a5b0: DecompressPointer r0
    //     0xb0a5b0: add             x0, x0, HEAP, lsl #32
    // 0xb0a5b4: cmp             w0, NULL
    // 0xb0a5b8: b.ne            #0xb0a5c4
    // 0xb0a5bc: mov             x2, x3
    // 0xb0a5c0: b               #0xb0a718
    // 0xb0a5c4: LoadField: r4 = r0->field_b
    //     0xb0a5c4: ldur            w4, [x0, #0xb]
    // 0xb0a5c8: DecompressPointer r4
    //     0xb0a5c8: add             x4, x4, HEAP, lsl #32
    // 0xb0a5cc: stur            x4, [fp, #-0x20]
    // 0xb0a5d0: cmp             w4, NULL
    // 0xb0a5d4: b.eq            #0xb0a714
    // 0xb0a5d8: ldur            x0, [fp, #-8]
    // 0xb0a5dc: r1 = Null
    //     0xb0a5dc: mov             x1, NULL
    // 0xb0a5e0: r2 = 6
    //     0xb0a5e0: movz            x2, #0x6
    // 0xb0a5e4: r0 = AllocateArray()
    //     0xb0a5e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0a5e8: r16 = " | ("
    //     0xb0a5e8: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d70] " | ("
    //     0xb0a5ec: ldr             x16, [x16, #0xd70]
    // 0xb0a5f0: StoreField: r0->field_f = r16
    //     0xb0a5f0: stur            w16, [x0, #0xf]
    // 0xb0a5f4: ldur            x1, [fp, #-0x20]
    // 0xb0a5f8: LoadField: d0 = r1->field_7
    //     0xb0a5f8: ldur            d0, [x1, #7]
    // 0xb0a5fc: fcmp            d0, d0
    // 0xb0a600: b.vs            #0xb0aa70
    // 0xb0a604: fcvtzs          x1, d0
    // 0xb0a608: asr             x16, x1, #0x1e
    // 0xb0a60c: cmp             x16, x1, asr #63
    // 0xb0a610: b.ne            #0xb0aa70
    // 0xb0a614: lsl             x1, x1, #1
    // 0xb0a618: StoreField: r0->field_13 = r1
    //     0xb0a618: stur            w1, [x0, #0x13]
    // 0xb0a61c: r16 = ")"
    //     0xb0a61c: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xb0a620: ArrayStore: r0[0] = r16  ; List_4
    //     0xb0a620: stur            w16, [x0, #0x17]
    // 0xb0a624: str             x0, [SP]
    // 0xb0a628: r0 = _interpolate()
    //     0xb0a628: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb0a62c: ldur            x3, [fp, #-8]
    // 0xb0a630: stur            x0, [fp, #-0x20]
    // 0xb0a634: LoadField: r1 = r3->field_f
    //     0xb0a634: ldur            w1, [x3, #0xf]
    // 0xb0a638: DecompressPointer r1
    //     0xb0a638: add             x1, x1, HEAP, lsl #32
    // 0xb0a63c: cmp             w1, NULL
    // 0xb0a640: b.eq            #0xb0aa98
    // 0xb0a644: r0 = of()
    //     0xb0a644: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0a648: LoadField: r1 = r0->field_87
    //     0xb0a648: ldur            w1, [x0, #0x87]
    // 0xb0a64c: DecompressPointer r1
    //     0xb0a64c: add             x1, x1, HEAP, lsl #32
    // 0xb0a650: LoadField: r0 = r1->field_2b
    //     0xb0a650: ldur            w0, [x1, #0x2b]
    // 0xb0a654: DecompressPointer r0
    //     0xb0a654: add             x0, x0, HEAP, lsl #32
    // 0xb0a658: r16 = 12.000000
    //     0xb0a658: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb0a65c: ldr             x16, [x16, #0x9e8]
    // 0xb0a660: r30 = Instance_Color
    //     0xb0a660: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb0a664: stp             lr, x16, [SP]
    // 0xb0a668: mov             x1, x0
    // 0xb0a66c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0a66c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0a670: ldr             x4, [x4, #0xaa0]
    // 0xb0a674: r0 = copyWith()
    //     0xb0a674: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0a678: stur            x0, [fp, #-0x28]
    // 0xb0a67c: r0 = Text()
    //     0xb0a67c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0a680: mov             x2, x0
    // 0xb0a684: ldur            x0, [fp, #-0x20]
    // 0xb0a688: stur            x2, [fp, #-0x38]
    // 0xb0a68c: StoreField: r2->field_b = r0
    //     0xb0a68c: stur            w0, [x2, #0xb]
    // 0xb0a690: ldur            x0, [fp, #-0x28]
    // 0xb0a694: StoreField: r2->field_13 = r0
    //     0xb0a694: stur            w0, [x2, #0x13]
    // 0xb0a698: ldur            x0, [fp, #-0x30]
    // 0xb0a69c: LoadField: r1 = r0->field_b
    //     0xb0a69c: ldur            w1, [x0, #0xb]
    // 0xb0a6a0: LoadField: r3 = r0->field_f
    //     0xb0a6a0: ldur            w3, [x0, #0xf]
    // 0xb0a6a4: DecompressPointer r3
    //     0xb0a6a4: add             x3, x3, HEAP, lsl #32
    // 0xb0a6a8: LoadField: r4 = r3->field_b
    //     0xb0a6a8: ldur            w4, [x3, #0xb]
    // 0xb0a6ac: r3 = LoadInt32Instr(r1)
    //     0xb0a6ac: sbfx            x3, x1, #1, #0x1f
    // 0xb0a6b0: stur            x3, [fp, #-0x40]
    // 0xb0a6b4: r1 = LoadInt32Instr(r4)
    //     0xb0a6b4: sbfx            x1, x4, #1, #0x1f
    // 0xb0a6b8: cmp             x3, x1
    // 0xb0a6bc: b.ne            #0xb0a6c8
    // 0xb0a6c0: mov             x1, x0
    // 0xb0a6c4: r0 = _growToNextCapacity()
    //     0xb0a6c4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0a6c8: ldur            x2, [fp, #-0x30]
    // 0xb0a6cc: ldur            x3, [fp, #-0x40]
    // 0xb0a6d0: add             x0, x3, #1
    // 0xb0a6d4: lsl             x1, x0, #1
    // 0xb0a6d8: StoreField: r2->field_b = r1
    //     0xb0a6d8: stur            w1, [x2, #0xb]
    // 0xb0a6dc: LoadField: r1 = r2->field_f
    //     0xb0a6dc: ldur            w1, [x2, #0xf]
    // 0xb0a6e0: DecompressPointer r1
    //     0xb0a6e0: add             x1, x1, HEAP, lsl #32
    // 0xb0a6e4: ldur            x0, [fp, #-0x38]
    // 0xb0a6e8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0a6e8: add             x25, x1, x3, lsl #2
    //     0xb0a6ec: add             x25, x25, #0xf
    //     0xb0a6f0: str             w0, [x25]
    //     0xb0a6f4: tbz             w0, #0, #0xb0a710
    //     0xb0a6f8: ldurb           w16, [x1, #-1]
    //     0xb0a6fc: ldurb           w17, [x0, #-1]
    //     0xb0a700: and             x16, x17, x16, lsr #2
    //     0xb0a704: tst             x16, HEAP, lsr #32
    //     0xb0a708: b.eq            #0xb0a710
    //     0xb0a70c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0a710: b               #0xb0a718
    // 0xb0a714: mov             x2, x3
    // 0xb0a718: r0 = Row()
    //     0xb0a718: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb0a71c: r4 = Instance_Axis
    //     0xb0a71c: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb0a720: StoreField: r0->field_f = r4
    //     0xb0a720: stur            w4, [x0, #0xf]
    // 0xb0a724: r5 = Instance_MainAxisAlignment
    //     0xb0a724: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb0a728: ldr             x5, [x5, #0xa08]
    // 0xb0a72c: StoreField: r0->field_13 = r5
    //     0xb0a72c: stur            w5, [x0, #0x13]
    // 0xb0a730: r6 = Instance_MainAxisSize
    //     0xb0a730: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb0a734: ldr             x6, [x6, #0xa10]
    // 0xb0a738: ArrayStore: r0[0] = r6  ; List_4
    //     0xb0a738: stur            w6, [x0, #0x17]
    // 0xb0a73c: r7 = Instance_CrossAxisAlignment
    //     0xb0a73c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb0a740: ldr             x7, [x7, #0xa18]
    // 0xb0a744: StoreField: r0->field_1b = r7
    //     0xb0a744: stur            w7, [x0, #0x1b]
    // 0xb0a748: r8 = Instance_VerticalDirection
    //     0xb0a748: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb0a74c: ldr             x8, [x8, #0xa20]
    // 0xb0a750: StoreField: r0->field_23 = r8
    //     0xb0a750: stur            w8, [x0, #0x23]
    // 0xb0a754: r9 = Instance_Clip
    //     0xb0a754: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb0a758: ldr             x9, [x9, #0x38]
    // 0xb0a75c: StoreField: r0->field_2b = r9
    //     0xb0a75c: stur            w9, [x0, #0x2b]
    // 0xb0a760: StoreField: r0->field_2f = rZR
    //     0xb0a760: stur            xzr, [x0, #0x2f]
    // 0xb0a764: ldur            x1, [fp, #-0x30]
    // 0xb0a768: StoreField: r0->field_b = r1
    //     0xb0a768: stur            w1, [x0, #0xb]
    // 0xb0a76c: mov             x1, x0
    // 0xb0a770: b               #0xb0aa14
    // 0xb0a774: ldur            x3, [fp, #-8]
    // 0xb0a778: ldur            x2, [fp, #-0x10]
    // 0xb0a77c: r7 = Instance_CrossAxisAlignment
    //     0xb0a77c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb0a780: ldr             x7, [x7, #0xa18]
    // 0xb0a784: r5 = Instance_MainAxisAlignment
    //     0xb0a784: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb0a788: ldr             x5, [x5, #0xa08]
    // 0xb0a78c: r6 = Instance_MainAxisSize
    //     0xb0a78c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb0a790: ldr             x6, [x6, #0xa10]
    // 0xb0a794: r4 = Instance_Axis
    //     0xb0a794: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb0a798: r8 = Instance_VerticalDirection
    //     0xb0a798: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb0a79c: ldr             x8, [x8, #0xa20]
    // 0xb0a7a0: r0 = Instance_BlendMode
    //     0xb0a7a0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb0a7a4: ldr             x0, [x0, #0xb30]
    // 0xb0a7a8: r9 = Instance_Clip
    //     0xb0a7a8: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb0a7ac: ldr             x9, [x9, #0x38]
    // 0xb0a7b0: LoadField: r1 = r3->field_f
    //     0xb0a7b0: ldur            w1, [x3, #0xf]
    // 0xb0a7b4: DecompressPointer r1
    //     0xb0a7b4: add             x1, x1, HEAP, lsl #32
    // 0xb0a7b8: cmp             w1, NULL
    // 0xb0a7bc: b.eq            #0xb0aa9c
    // 0xb0a7c0: r0 = of()
    //     0xb0a7c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0a7c4: LoadField: r1 = r0->field_5b
    //     0xb0a7c4: ldur            w1, [x0, #0x5b]
    // 0xb0a7c8: DecompressPointer r1
    //     0xb0a7c8: add             x1, x1, HEAP, lsl #32
    // 0xb0a7cc: stur            x1, [fp, #-0x20]
    // 0xb0a7d0: r0 = ColorFilter()
    //     0xb0a7d0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb0a7d4: mov             x1, x0
    // 0xb0a7d8: ldur            x0, [fp, #-0x20]
    // 0xb0a7dc: stur            x1, [fp, #-0x28]
    // 0xb0a7e0: StoreField: r1->field_7 = r0
    //     0xb0a7e0: stur            w0, [x1, #7]
    // 0xb0a7e4: r0 = Instance_BlendMode
    //     0xb0a7e4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb0a7e8: ldr             x0, [x0, #0xb30]
    // 0xb0a7ec: StoreField: r1->field_b = r0
    //     0xb0a7ec: stur            w0, [x1, #0xb]
    // 0xb0a7f0: r2 = 1
    //     0xb0a7f0: movz            x2, #0x1
    // 0xb0a7f4: StoreField: r1->field_13 = r2
    //     0xb0a7f4: stur            x2, [x1, #0x13]
    // 0xb0a7f8: r0 = SvgPicture()
    //     0xb0a7f8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb0a7fc: stur            x0, [fp, #-0x20]
    // 0xb0a800: ldur            x16, [fp, #-0x28]
    // 0xb0a804: str             x16, [SP]
    // 0xb0a808: mov             x1, x0
    // 0xb0a80c: r2 = "assets/images/green_star.svg"
    //     0xb0a80c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb0a810: ldr             x2, [x2, #0x9a0]
    // 0xb0a814: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb0a814: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb0a818: ldr             x4, [x4, #0xa38]
    // 0xb0a81c: r0 = SvgPicture.asset()
    //     0xb0a81c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb0a820: ldur            x0, [fp, #-0x10]
    // 0xb0a824: LoadField: r1 = r0->field_e3
    //     0xb0a824: ldur            w1, [x0, #0xe3]
    // 0xb0a828: DecompressPointer r1
    //     0xb0a828: add             x1, x1, HEAP, lsl #32
    // 0xb0a82c: cmp             w1, NULL
    // 0xb0a830: b.ne            #0xb0a83c
    // 0xb0a834: r0 = Null
    //     0xb0a834: mov             x0, NULL
    // 0xb0a838: b               #0xb0a860
    // 0xb0a83c: LoadField: r0 = r1->field_7
    //     0xb0a83c: ldur            w0, [x1, #7]
    // 0xb0a840: DecompressPointer r0
    //     0xb0a840: add             x0, x0, HEAP, lsl #32
    // 0xb0a844: cmp             w0, NULL
    // 0xb0a848: b.ne            #0xb0a854
    // 0xb0a84c: r0 = Null
    //     0xb0a84c: mov             x0, NULL
    // 0xb0a850: b               #0xb0a860
    // 0xb0a854: mov             x1, x0
    // 0xb0a858: r2 = 1
    //     0xb0a858: movz            x2, #0x1
    // 0xb0a85c: r0 = toStringAsFixed()
    //     0xb0a85c: bl              #0x7c1ef8  ; [dart:core] _Double::toStringAsFixed
    // 0xb0a860: cmp             w0, NULL
    // 0xb0a864: b.ne            #0xb0a870
    // 0xb0a868: r3 = ""
    //     0xb0a868: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0a86c: b               #0xb0a874
    // 0xb0a870: mov             x3, x0
    // 0xb0a874: ldur            x2, [fp, #-8]
    // 0xb0a878: ldur            x0, [fp, #-0x20]
    // 0xb0a87c: stur            x3, [fp, #-0x10]
    // 0xb0a880: LoadField: r1 = r2->field_f
    //     0xb0a880: ldur            w1, [x2, #0xf]
    // 0xb0a884: DecompressPointer r1
    //     0xb0a884: add             x1, x1, HEAP, lsl #32
    // 0xb0a888: cmp             w1, NULL
    // 0xb0a88c: b.eq            #0xb0aaa0
    // 0xb0a890: r0 = of()
    //     0xb0a890: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0a894: LoadField: r1 = r0->field_87
    //     0xb0a894: ldur            w1, [x0, #0x87]
    // 0xb0a898: DecompressPointer r1
    //     0xb0a898: add             x1, x1, HEAP, lsl #32
    // 0xb0a89c: LoadField: r0 = r1->field_7
    //     0xb0a89c: ldur            w0, [x1, #7]
    // 0xb0a8a0: DecompressPointer r0
    //     0xb0a8a0: add             x0, x0, HEAP, lsl #32
    // 0xb0a8a4: r16 = 12.000000
    //     0xb0a8a4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb0a8a8: ldr             x16, [x16, #0x9e8]
    // 0xb0a8ac: r30 = Instance_Color
    //     0xb0a8ac: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb0a8b0: stp             lr, x16, [SP]
    // 0xb0a8b4: mov             x1, x0
    // 0xb0a8b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0a8b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0a8bc: ldr             x4, [x4, #0xaa0]
    // 0xb0a8c0: r0 = copyWith()
    //     0xb0a8c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0a8c4: stur            x0, [fp, #-0x28]
    // 0xb0a8c8: r0 = Text()
    //     0xb0a8c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0a8cc: mov             x2, x0
    // 0xb0a8d0: ldur            x0, [fp, #-0x10]
    // 0xb0a8d4: stur            x2, [fp, #-0x30]
    // 0xb0a8d8: StoreField: r2->field_b = r0
    //     0xb0a8d8: stur            w0, [x2, #0xb]
    // 0xb0a8dc: ldur            x0, [fp, #-0x28]
    // 0xb0a8e0: StoreField: r2->field_13 = r0
    //     0xb0a8e0: stur            w0, [x2, #0x13]
    // 0xb0a8e4: ldur            x0, [fp, #-8]
    // 0xb0a8e8: LoadField: r1 = r0->field_f
    //     0xb0a8e8: ldur            w1, [x0, #0xf]
    // 0xb0a8ec: DecompressPointer r1
    //     0xb0a8ec: add             x1, x1, HEAP, lsl #32
    // 0xb0a8f0: cmp             w1, NULL
    // 0xb0a8f4: b.eq            #0xb0aaa4
    // 0xb0a8f8: r0 = of()
    //     0xb0a8f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0a8fc: LoadField: r1 = r0->field_87
    //     0xb0a8fc: ldur            w1, [x0, #0x87]
    // 0xb0a900: DecompressPointer r1
    //     0xb0a900: add             x1, x1, HEAP, lsl #32
    // 0xb0a904: LoadField: r0 = r1->field_2b
    //     0xb0a904: ldur            w0, [x1, #0x2b]
    // 0xb0a908: DecompressPointer r0
    //     0xb0a908: add             x0, x0, HEAP, lsl #32
    // 0xb0a90c: ldur            x1, [fp, #-8]
    // 0xb0a910: stur            x0, [fp, #-0x10]
    // 0xb0a914: LoadField: r2 = r1->field_f
    //     0xb0a914: ldur            w2, [x1, #0xf]
    // 0xb0a918: DecompressPointer r2
    //     0xb0a918: add             x2, x2, HEAP, lsl #32
    // 0xb0a91c: cmp             w2, NULL
    // 0xb0a920: b.eq            #0xb0aaa8
    // 0xb0a924: mov             x1, x2
    // 0xb0a928: r0 = of()
    //     0xb0a928: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0a92c: LoadField: r1 = r0->field_5b
    //     0xb0a92c: ldur            w1, [x0, #0x5b]
    // 0xb0a930: DecompressPointer r1
    //     0xb0a930: add             x1, x1, HEAP, lsl #32
    // 0xb0a934: r16 = 10.000000
    //     0xb0a934: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb0a938: stp             x1, x16, [SP]
    // 0xb0a93c: ldur            x1, [fp, #-0x10]
    // 0xb0a940: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0a940: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0a944: ldr             x4, [x4, #0xaa0]
    // 0xb0a948: r0 = copyWith()
    //     0xb0a948: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0a94c: stur            x0, [fp, #-8]
    // 0xb0a950: r0 = Text()
    //     0xb0a950: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0a954: mov             x3, x0
    // 0xb0a958: r0 = " Brand Rating"
    //     0xb0a958: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d78] " Brand Rating"
    //     0xb0a95c: ldr             x0, [x0, #0xd78]
    // 0xb0a960: stur            x3, [fp, #-0x10]
    // 0xb0a964: StoreField: r3->field_b = r0
    //     0xb0a964: stur            w0, [x3, #0xb]
    // 0xb0a968: ldur            x0, [fp, #-8]
    // 0xb0a96c: StoreField: r3->field_13 = r0
    //     0xb0a96c: stur            w0, [x3, #0x13]
    // 0xb0a970: r1 = Null
    //     0xb0a970: mov             x1, NULL
    // 0xb0a974: r2 = 6
    //     0xb0a974: movz            x2, #0x6
    // 0xb0a978: r0 = AllocateArray()
    //     0xb0a978: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0a97c: mov             x2, x0
    // 0xb0a980: ldur            x0, [fp, #-0x20]
    // 0xb0a984: stur            x2, [fp, #-8]
    // 0xb0a988: StoreField: r2->field_f = r0
    //     0xb0a988: stur            w0, [x2, #0xf]
    // 0xb0a98c: ldur            x0, [fp, #-0x30]
    // 0xb0a990: StoreField: r2->field_13 = r0
    //     0xb0a990: stur            w0, [x2, #0x13]
    // 0xb0a994: ldur            x0, [fp, #-0x10]
    // 0xb0a998: ArrayStore: r2[0] = r0  ; List_4
    //     0xb0a998: stur            w0, [x2, #0x17]
    // 0xb0a99c: r1 = <Widget>
    //     0xb0a99c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0a9a0: r0 = AllocateGrowableArray()
    //     0xb0a9a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0a9a4: mov             x1, x0
    // 0xb0a9a8: ldur            x0, [fp, #-8]
    // 0xb0a9ac: stur            x1, [fp, #-0x10]
    // 0xb0a9b0: StoreField: r1->field_f = r0
    //     0xb0a9b0: stur            w0, [x1, #0xf]
    // 0xb0a9b4: r0 = 6
    //     0xb0a9b4: movz            x0, #0x6
    // 0xb0a9b8: StoreField: r1->field_b = r0
    //     0xb0a9b8: stur            w0, [x1, #0xb]
    // 0xb0a9bc: r0 = Row()
    //     0xb0a9bc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb0a9c0: mov             x1, x0
    // 0xb0a9c4: r0 = Instance_Axis
    //     0xb0a9c4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb0a9c8: StoreField: r1->field_f = r0
    //     0xb0a9c8: stur            w0, [x1, #0xf]
    // 0xb0a9cc: r0 = Instance_MainAxisAlignment
    //     0xb0a9cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb0a9d0: ldr             x0, [x0, #0xa08]
    // 0xb0a9d4: StoreField: r1->field_13 = r0
    //     0xb0a9d4: stur            w0, [x1, #0x13]
    // 0xb0a9d8: r0 = Instance_MainAxisSize
    //     0xb0a9d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb0a9dc: ldr             x0, [x0, #0xa10]
    // 0xb0a9e0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0a9e0: stur            w0, [x1, #0x17]
    // 0xb0a9e4: r0 = Instance_CrossAxisAlignment
    //     0xb0a9e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb0a9e8: ldr             x0, [x0, #0xa18]
    // 0xb0a9ec: StoreField: r1->field_1b = r0
    //     0xb0a9ec: stur            w0, [x1, #0x1b]
    // 0xb0a9f0: r0 = Instance_VerticalDirection
    //     0xb0a9f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb0a9f4: ldr             x0, [x0, #0xa20]
    // 0xb0a9f8: StoreField: r1->field_23 = r0
    //     0xb0a9f8: stur            w0, [x1, #0x23]
    // 0xb0a9fc: r0 = Instance_Clip
    //     0xb0a9fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb0aa00: ldr             x0, [x0, #0x38]
    // 0xb0aa04: StoreField: r1->field_2b = r0
    //     0xb0aa04: stur            w0, [x1, #0x2b]
    // 0xb0aa08: StoreField: r1->field_2f = rZR
    //     0xb0aa08: stur            xzr, [x1, #0x2f]
    // 0xb0aa0c: ldur            x0, [fp, #-0x10]
    // 0xb0aa10: StoreField: r1->field_b = r0
    //     0xb0aa10: stur            w0, [x1, #0xb]
    // 0xb0aa14: ldur            x0, [fp, #-0x18]
    // 0xb0aa18: stur            x1, [fp, #-8]
    // 0xb0aa1c: r0 = Visibility()
    //     0xb0aa1c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb0aa20: ldur            x1, [fp, #-8]
    // 0xb0aa24: StoreField: r0->field_b = r1
    //     0xb0aa24: stur            w1, [x0, #0xb]
    // 0xb0aa28: r1 = Instance_SizedBox
    //     0xb0aa28: ldr             x1, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb0aa2c: StoreField: r0->field_f = r1
    //     0xb0aa2c: stur            w1, [x0, #0xf]
    // 0xb0aa30: ldur            x1, [fp, #-0x18]
    // 0xb0aa34: StoreField: r0->field_13 = r1
    //     0xb0aa34: stur            w1, [x0, #0x13]
    // 0xb0aa38: r1 = false
    //     0xb0aa38: add             x1, NULL, #0x30  ; false
    // 0xb0aa3c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb0aa3c: stur            w1, [x0, #0x17]
    // 0xb0aa40: StoreField: r0->field_1b = r1
    //     0xb0aa40: stur            w1, [x0, #0x1b]
    // 0xb0aa44: StoreField: r0->field_1f = r1
    //     0xb0aa44: stur            w1, [x0, #0x1f]
    // 0xb0aa48: StoreField: r0->field_23 = r1
    //     0xb0aa48: stur            w1, [x0, #0x23]
    // 0xb0aa4c: StoreField: r0->field_27 = r1
    //     0xb0aa4c: stur            w1, [x0, #0x27]
    // 0xb0aa50: StoreField: r0->field_2b = r1
    //     0xb0aa50: stur            w1, [x0, #0x2b]
    // 0xb0aa54: LeaveFrame
    //     0xb0aa54: mov             SP, fp
    //     0xb0aa58: ldp             fp, lr, [SP], #0x10
    // 0xb0aa5c: ret
    //     0xb0aa5c: ret             
    // 0xb0aa60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0aa60: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0aa64: b               #0xb0a208
    // 0xb0aa68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0aa68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0aa6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0aa6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0aa70: SaveReg d0
    //     0xb0aa70: str             q0, [SP, #-0x10]!
    // 0xb0aa74: SaveReg r0
    //     0xb0aa74: str             x0, [SP, #-8]!
    // 0xb0aa78: r0 = 74
    //     0xb0aa78: movz            x0, #0x4a
    // 0xb0aa7c: r30 = DoubleToIntegerStub
    //     0xb0aa7c: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xb0aa80: LoadField: r30 = r30->field_7
    //     0xb0aa80: ldur            lr, [lr, #7]
    // 0xb0aa84: blr             lr
    // 0xb0aa88: mov             x1, x0
    // 0xb0aa8c: RestoreReg r0
    //     0xb0aa8c: ldr             x0, [SP], #8
    // 0xb0aa90: RestoreReg d0
    //     0xb0aa90: ldr             q0, [SP], #0x10
    // 0xb0aa94: b               #0xb0a618
    // 0xb0aa98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0aa98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0aa9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0aa9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0aaa0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0aaa0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0aaa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0aaa4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0aaa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0aaa8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, VisibilityInfo) {
    // ** addr: 0xb0aaac, size: 0x124
    // 0xb0aaac: EnterFrame
    //     0xb0aaac: stp             fp, lr, [SP, #-0x10]!
    //     0xb0aab0: mov             fp, SP
    // 0xb0aab4: AllocStack(0x38)
    //     0xb0aab4: sub             SP, SP, #0x38
    // 0xb0aab8: SetupParameters()
    //     0xb0aab8: ldr             x0, [fp, #0x18]
    //     0xb0aabc: ldur            w2, [x0, #0x17]
    //     0xb0aac0: add             x2, x2, HEAP, lsl #32
    //     0xb0aac4: stur            x2, [fp, #-8]
    // 0xb0aac8: CheckStackOverflow
    //     0xb0aac8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0aacc: cmp             SP, x16
    //     0xb0aad0: b.ls            #0xb0abc0
    // 0xb0aad4: ldr             x1, [fp, #0x10]
    // 0xb0aad8: r0 = visibleFraction()
    //     0xb0aad8: bl              #0xa4fa10  ; [package:visibility_detector/src/visibility_detector.dart] VisibilityInfo::visibleFraction
    // 0xb0aadc: mov             v1.16b, v0.16b
    // 0xb0aae0: d0 = 0.500000
    //     0xb0aae0: fmov            d0, #0.50000000
    // 0xb0aae4: fcmp            d1, d0
    // 0xb0aae8: b.le            #0xb0abb0
    // 0xb0aaec: ldur            x0, [fp, #-8]
    // 0xb0aaf0: LoadField: r1 = r0->field_b
    //     0xb0aaf0: ldur            w1, [x0, #0xb]
    // 0xb0aaf4: DecompressPointer r1
    //     0xb0aaf4: add             x1, x1, HEAP, lsl #32
    // 0xb0aaf8: LoadField: r2 = r1->field_f
    //     0xb0aaf8: ldur            w2, [x1, #0xf]
    // 0xb0aafc: DecompressPointer r2
    //     0xb0aafc: add             x2, x2, HEAP, lsl #32
    // 0xb0ab00: LoadField: r1 = r2->field_b
    //     0xb0ab00: ldur            w1, [x2, #0xb]
    // 0xb0ab04: DecompressPointer r1
    //     0xb0ab04: add             x1, x1, HEAP, lsl #32
    // 0xb0ab08: cmp             w1, NULL
    // 0xb0ab0c: b.eq            #0xb0abc8
    // 0xb0ab10: LoadField: r2 = r0->field_f
    //     0xb0ab10: ldur            w2, [x0, #0xf]
    // 0xb0ab14: DecompressPointer r2
    //     0xb0ab14: add             x2, x2, HEAP, lsl #32
    // 0xb0ab18: cmp             w2, NULL
    // 0xb0ab1c: b.eq            #0xb0abcc
    // 0xb0ab20: LoadField: r0 = r2->field_eb
    //     0xb0ab20: ldur            w0, [x2, #0xeb]
    // 0xb0ab24: DecompressPointer r0
    //     0xb0ab24: add             x0, x0, HEAP, lsl #32
    // 0xb0ab28: cmp             w0, NULL
    // 0xb0ab2c: b.ne            #0xb0ab38
    // 0xb0ab30: r3 = Null
    //     0xb0ab30: mov             x3, NULL
    // 0xb0ab34: b               #0xb0ab40
    // 0xb0ab38: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb0ab38: ldur            w3, [x0, #0x17]
    // 0xb0ab3c: DecompressPointer r3
    //     0xb0ab3c: add             x3, x3, HEAP, lsl #32
    // 0xb0ab40: cmp             w0, NULL
    // 0xb0ab44: b.ne            #0xb0ab50
    // 0xb0ab48: r4 = Null
    //     0xb0ab48: mov             x4, NULL
    // 0xb0ab4c: b               #0xb0ab58
    // 0xb0ab50: LoadField: r4 = r0->field_23
    //     0xb0ab50: ldur            w4, [x0, #0x23]
    // 0xb0ab54: DecompressPointer r4
    //     0xb0ab54: add             x4, x4, HEAP, lsl #32
    // 0xb0ab58: LoadField: r5 = r2->field_47
    //     0xb0ab58: ldur            w5, [x2, #0x47]
    // 0xb0ab5c: DecompressPointer r5
    //     0xb0ab5c: add             x5, x5, HEAP, lsl #32
    // 0xb0ab60: cmp             w0, NULL
    // 0xb0ab64: b.ne            #0xb0ab70
    // 0xb0ab68: r0 = Null
    //     0xb0ab68: mov             x0, NULL
    // 0xb0ab6c: b               #0xb0ab7c
    // 0xb0ab70: LoadField: r6 = r0->field_13
    //     0xb0ab70: ldur            w6, [x0, #0x13]
    // 0xb0ab74: DecompressPointer r6
    //     0xb0ab74: add             x6, x6, HEAP, lsl #32
    // 0xb0ab78: mov             x0, x6
    // 0xb0ab7c: LoadField: r6 = r2->field_e3
    //     0xb0ab7c: ldur            w6, [x2, #0xe3]
    // 0xb0ab80: DecompressPointer r6
    //     0xb0ab80: add             x6, x6, HEAP, lsl #32
    // 0xb0ab84: LoadField: r2 = r1->field_33
    //     0xb0ab84: ldur            w2, [x1, #0x33]
    // 0xb0ab88: DecompressPointer r2
    //     0xb0ab88: add             x2, x2, HEAP, lsl #32
    // 0xb0ab8c: stp             x3, x2, [SP, #0x20]
    // 0xb0ab90: stp             x5, x4, [SP, #0x10]
    // 0xb0ab94: stp             x6, x0, [SP]
    // 0xb0ab98: r4 = 0
    //     0xb0ab98: movz            x4, #0
    // 0xb0ab9c: ldr             x0, [SP, #0x28]
    // 0xb0aba0: r16 = UnlinkedCall_0x613b5c
    //     0xb0aba0: add             x16, PP, #0x61, lsl #12  ; [pp+0x61ec8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb0aba4: add             x16, x16, #0xec8
    // 0xb0aba8: ldp             x5, lr, [x16]
    // 0xb0abac: blr             lr
    // 0xb0abb0: r0 = Null
    //     0xb0abb0: mov             x0, NULL
    // 0xb0abb4: LeaveFrame
    //     0xb0abb4: mov             SP, fp
    //     0xb0abb8: ldp             fp, lr, [SP], #0x10
    // 0xb0abbc: ret
    //     0xb0abbc: ret             
    // 0xb0abc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0abc0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0abc4: b               #0xb0aad4
    // 0xb0abc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0abc8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0abcc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb0abcc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] Stack <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb0abd0, size: 0x218
    // 0xb0abd0: EnterFrame
    //     0xb0abd0: stp             fp, lr, [SP, #-0x10]!
    //     0xb0abd4: mov             fp, SP
    // 0xb0abd8: AllocStack(0x20)
    //     0xb0abd8: sub             SP, SP, #0x20
    // 0xb0abdc: SetupParameters()
    //     0xb0abdc: ldr             x0, [fp, #0x20]
    //     0xb0abe0: ldur            w1, [x0, #0x17]
    //     0xb0abe4: add             x1, x1, HEAP, lsl #32
    // 0xb0abe8: CheckStackOverflow
    //     0xb0abe8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0abec: cmp             SP, x16
    //     0xb0abf0: b.ls            #0xb0add8
    // 0xb0abf4: LoadField: r0 = r1->field_b
    //     0xb0abf4: ldur            w0, [x1, #0xb]
    // 0xb0abf8: DecompressPointer r0
    //     0xb0abf8: add             x0, x0, HEAP, lsl #32
    // 0xb0abfc: LoadField: r2 = r0->field_f
    //     0xb0abfc: ldur            w2, [x0, #0xf]
    // 0xb0ac00: DecompressPointer r2
    //     0xb0ac00: add             x2, x2, HEAP, lsl #32
    // 0xb0ac04: LoadField: r4 = r1->field_f
    //     0xb0ac04: ldur            w4, [x1, #0xf]
    // 0xb0ac08: DecompressPointer r4
    //     0xb0ac08: add             x4, x4, HEAP, lsl #32
    // 0xb0ac0c: stur            x4, [fp, #-8]
    // 0xb0ac10: cmp             w4, NULL
    // 0xb0ac14: b.eq            #0xb0ade0
    // 0xb0ac18: LoadField: r3 = r4->field_e7
    //     0xb0ac18: ldur            w3, [x4, #0xe7]
    // 0xb0ac1c: DecompressPointer r3
    //     0xb0ac1c: add             x3, x3, HEAP, lsl #32
    // 0xb0ac20: cmp             w3, NULL
    // 0xb0ac24: b.ne            #0xb0ac30
    // 0xb0ac28: r0 = Null
    //     0xb0ac28: mov             x0, NULL
    // 0xb0ac2c: b               #0xb0ac6c
    // 0xb0ac30: ldr             x0, [fp, #0x10]
    // 0xb0ac34: LoadField: r1 = r3->field_b
    //     0xb0ac34: ldur            w1, [x3, #0xb]
    // 0xb0ac38: r5 = LoadInt32Instr(r0)
    //     0xb0ac38: sbfx            x5, x0, #1, #0x1f
    //     0xb0ac3c: tbz             w0, #0, #0xb0ac44
    //     0xb0ac40: ldur            x5, [x0, #7]
    // 0xb0ac44: r0 = LoadInt32Instr(r1)
    //     0xb0ac44: sbfx            x0, x1, #1, #0x1f
    // 0xb0ac48: mov             x1, x5
    // 0xb0ac4c: cmp             x1, x0
    // 0xb0ac50: b.hs            #0xb0ade4
    // 0xb0ac54: LoadField: r0 = r3->field_f
    //     0xb0ac54: ldur            w0, [x3, #0xf]
    // 0xb0ac58: DecompressPointer r0
    //     0xb0ac58: add             x0, x0, HEAP, lsl #32
    // 0xb0ac5c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb0ac5c: add             x16, x0, x5, lsl #2
    //     0xb0ac60: ldur            w1, [x16, #0xf]
    // 0xb0ac64: DecompressPointer r1
    //     0xb0ac64: add             x1, x1, HEAP, lsl #32
    // 0xb0ac68: mov             x0, x1
    // 0xb0ac6c: mov             x1, x2
    // 0xb0ac70: mov             x2, x0
    // 0xb0ac74: mov             x3, x4
    // 0xb0ac78: r0 = getCosmeticThemeSlider()
    //     0xb0ac78: bl              #0xb0ade8  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::getCosmeticThemeSlider
    // 0xb0ac7c: r1 = Null
    //     0xb0ac7c: mov             x1, NULL
    // 0xb0ac80: r2 = 2
    //     0xb0ac80: movz            x2, #0x2
    // 0xb0ac84: stur            x0, [fp, #-0x10]
    // 0xb0ac88: r0 = AllocateArray()
    //     0xb0ac88: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0ac8c: mov             x2, x0
    // 0xb0ac90: ldur            x0, [fp, #-0x10]
    // 0xb0ac94: stur            x2, [fp, #-0x18]
    // 0xb0ac98: StoreField: r2->field_f = r0
    //     0xb0ac98: stur            w0, [x2, #0xf]
    // 0xb0ac9c: r1 = <Widget>
    //     0xb0ac9c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0aca0: r0 = AllocateGrowableArray()
    //     0xb0aca0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0aca4: mov             x1, x0
    // 0xb0aca8: ldur            x0, [fp, #-0x18]
    // 0xb0acac: stur            x1, [fp, #-0x10]
    // 0xb0acb0: StoreField: r1->field_f = r0
    //     0xb0acb0: stur            w0, [x1, #0xf]
    // 0xb0acb4: r0 = 2
    //     0xb0acb4: movz            x0, #0x2
    // 0xb0acb8: StoreField: r1->field_b = r0
    //     0xb0acb8: stur            w0, [x1, #0xb]
    // 0xb0acbc: ldur            x0, [fp, #-8]
    // 0xb0acc0: r17 = 315
    //     0xb0acc0: movz            x17, #0x13b
    // 0xb0acc4: ldr             w2, [x0, x17]
    // 0xb0acc8: DecompressPointer r2
    //     0xb0acc8: add             x2, x2, HEAP, lsl #32
    // 0xb0accc: cmp             w2, NULL
    // 0xb0acd0: b.ne            #0xb0acdc
    // 0xb0acd4: mov             x2, x1
    // 0xb0acd8: b               #0xb0ad9c
    // 0xb0acdc: tbnz            w2, #4, #0xb0ad98
    // 0xb0ace0: r0 = SvgPicture()
    //     0xb0ace0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb0ace4: mov             x1, x0
    // 0xb0ace8: r2 = "assets/images/free-gift-icon.svg"
    //     0xb0ace8: add             x2, PP, #0x52, lsl #12  ; [pp+0x52d40] "assets/images/free-gift-icon.svg"
    //     0xb0acec: ldr             x2, [x2, #0xd40]
    // 0xb0acf0: stur            x0, [fp, #-8]
    // 0xb0acf4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb0acf4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb0acf8: r0 = SvgPicture.asset()
    //     0xb0acf8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb0acfc: r0 = Padding()
    //     0xb0acfc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0ad00: mov             x2, x0
    // 0xb0ad04: r0 = Instance_EdgeInsets
    //     0xb0ad04: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d48] Obj!EdgeInsets@d584c1
    //     0xb0ad08: ldr             x0, [x0, #0xd48]
    // 0xb0ad0c: stur            x2, [fp, #-0x18]
    // 0xb0ad10: StoreField: r2->field_f = r0
    //     0xb0ad10: stur            w0, [x2, #0xf]
    // 0xb0ad14: ldur            x0, [fp, #-8]
    // 0xb0ad18: StoreField: r2->field_b = r0
    //     0xb0ad18: stur            w0, [x2, #0xb]
    // 0xb0ad1c: ldur            x0, [fp, #-0x10]
    // 0xb0ad20: LoadField: r1 = r0->field_b
    //     0xb0ad20: ldur            w1, [x0, #0xb]
    // 0xb0ad24: LoadField: r3 = r0->field_f
    //     0xb0ad24: ldur            w3, [x0, #0xf]
    // 0xb0ad28: DecompressPointer r3
    //     0xb0ad28: add             x3, x3, HEAP, lsl #32
    // 0xb0ad2c: LoadField: r4 = r3->field_b
    //     0xb0ad2c: ldur            w4, [x3, #0xb]
    // 0xb0ad30: r3 = LoadInt32Instr(r1)
    //     0xb0ad30: sbfx            x3, x1, #1, #0x1f
    // 0xb0ad34: stur            x3, [fp, #-0x20]
    // 0xb0ad38: r1 = LoadInt32Instr(r4)
    //     0xb0ad38: sbfx            x1, x4, #1, #0x1f
    // 0xb0ad3c: cmp             x3, x1
    // 0xb0ad40: b.ne            #0xb0ad4c
    // 0xb0ad44: mov             x1, x0
    // 0xb0ad48: r0 = _growToNextCapacity()
    //     0xb0ad48: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0ad4c: ldur            x2, [fp, #-0x10]
    // 0xb0ad50: ldur            x3, [fp, #-0x20]
    // 0xb0ad54: add             x0, x3, #1
    // 0xb0ad58: lsl             x1, x0, #1
    // 0xb0ad5c: StoreField: r2->field_b = r1
    //     0xb0ad5c: stur            w1, [x2, #0xb]
    // 0xb0ad60: LoadField: r1 = r2->field_f
    //     0xb0ad60: ldur            w1, [x2, #0xf]
    // 0xb0ad64: DecompressPointer r1
    //     0xb0ad64: add             x1, x1, HEAP, lsl #32
    // 0xb0ad68: ldur            x0, [fp, #-0x18]
    // 0xb0ad6c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0ad6c: add             x25, x1, x3, lsl #2
    //     0xb0ad70: add             x25, x25, #0xf
    //     0xb0ad74: str             w0, [x25]
    //     0xb0ad78: tbz             w0, #0, #0xb0ad94
    //     0xb0ad7c: ldurb           w16, [x1, #-1]
    //     0xb0ad80: ldurb           w17, [x0, #-1]
    //     0xb0ad84: and             x16, x17, x16, lsr #2
    //     0xb0ad88: tst             x16, HEAP, lsr #32
    //     0xb0ad8c: b.eq            #0xb0ad94
    //     0xb0ad90: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0ad94: b               #0xb0ad9c
    // 0xb0ad98: mov             x2, x1
    // 0xb0ad9c: r0 = Stack()
    //     0xb0ad9c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb0ada0: r1 = Instance_Alignment
    //     0xb0ada0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xb0ada4: ldr             x1, [x1, #0x950]
    // 0xb0ada8: StoreField: r0->field_f = r1
    //     0xb0ada8: stur            w1, [x0, #0xf]
    // 0xb0adac: r1 = Instance_StackFit
    //     0xb0adac: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb0adb0: ldr             x1, [x1, #0xfa8]
    // 0xb0adb4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb0adb4: stur            w1, [x0, #0x17]
    // 0xb0adb8: r1 = Instance_Clip
    //     0xb0adb8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb0adbc: ldr             x1, [x1, #0x7e0]
    // 0xb0adc0: StoreField: r0->field_1b = r1
    //     0xb0adc0: stur            w1, [x0, #0x1b]
    // 0xb0adc4: ldur            x1, [fp, #-0x10]
    // 0xb0adc8: StoreField: r0->field_b = r1
    //     0xb0adc8: stur            w1, [x0, #0xb]
    // 0xb0adcc: LeaveFrame
    //     0xb0adcc: mov             SP, fp
    //     0xb0add0: ldp             fp, lr, [SP], #0x10
    // 0xb0add4: ret
    //     0xb0add4: ret             
    // 0xb0add8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0add8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0addc: b               #0xb0abf4
    // 0xb0ade0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb0ade0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb0ade4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0ade4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ getCosmeticThemeSlider(/* No info */) {
    // ** addr: 0xb0ade8, size: 0x528
    // 0xb0ade8: EnterFrame
    //     0xb0ade8: stp             fp, lr, [SP, #-0x10]!
    //     0xb0adec: mov             fp, SP
    // 0xb0adf0: AllocStack(0x60)
    //     0xb0adf0: sub             SP, SP, #0x60
    // 0xb0adf4: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0xb0adf4: mov             x0, x2
    //     0xb0adf8: stur            x2, [fp, #-0x10]
    //     0xb0adfc: mov             x2, x3
    //     0xb0ae00: stur            x1, [fp, #-8]
    //     0xb0ae04: stur            x3, [fp, #-0x18]
    // 0xb0ae08: CheckStackOverflow
    //     0xb0ae08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0ae0c: cmp             SP, x16
    //     0xb0ae10: b.ls            #0xb0b300
    // 0xb0ae14: r1 = 2
    //     0xb0ae14: movz            x1, #0x2
    // 0xb0ae18: r0 = AllocateContext()
    //     0xb0ae18: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0ae1c: ldur            x1, [fp, #-8]
    // 0xb0ae20: stur            x0, [fp, #-0x20]
    // 0xb0ae24: StoreField: r0->field_f = r1
    //     0xb0ae24: stur            w1, [x0, #0xf]
    // 0xb0ae28: ldur            x2, [fp, #-0x18]
    // 0xb0ae2c: StoreField: r0->field_13 = r2
    //     0xb0ae2c: stur            w2, [x0, #0x13]
    // 0xb0ae30: r0 = Radius()
    //     0xb0ae30: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb0ae34: d0 = 10.000000
    //     0xb0ae34: fmov            d0, #10.00000000
    // 0xb0ae38: stur            x0, [fp, #-0x28]
    // 0xb0ae3c: StoreField: r0->field_7 = d0
    //     0xb0ae3c: stur            d0, [x0, #7]
    // 0xb0ae40: StoreField: r0->field_f = d0
    //     0xb0ae40: stur            d0, [x0, #0xf]
    // 0xb0ae44: r0 = BorderRadius()
    //     0xb0ae44: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb0ae48: mov             x1, x0
    // 0xb0ae4c: ldur            x0, [fp, #-0x28]
    // 0xb0ae50: stur            x1, [fp, #-0x30]
    // 0xb0ae54: StoreField: r1->field_7 = r0
    //     0xb0ae54: stur            w0, [x1, #7]
    // 0xb0ae58: StoreField: r1->field_b = r0
    //     0xb0ae58: stur            w0, [x1, #0xb]
    // 0xb0ae5c: StoreField: r1->field_f = r0
    //     0xb0ae5c: stur            w0, [x1, #0xf]
    // 0xb0ae60: StoreField: r1->field_13 = r0
    //     0xb0ae60: stur            w0, [x1, #0x13]
    // 0xb0ae64: r0 = RoundedRectangleBorder()
    //     0xb0ae64: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb0ae68: mov             x3, x0
    // 0xb0ae6c: ldur            x0, [fp, #-0x30]
    // 0xb0ae70: stur            x3, [fp, #-0x28]
    // 0xb0ae74: StoreField: r3->field_b = r0
    //     0xb0ae74: stur            w0, [x3, #0xb]
    // 0xb0ae78: r0 = Instance_BorderSide
    //     0xb0ae78: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb0ae7c: ldr             x0, [x0, #0xe20]
    // 0xb0ae80: StoreField: r3->field_7 = r0
    //     0xb0ae80: stur            w0, [x3, #7]
    // 0xb0ae84: ldur            x0, [fp, #-0x10]
    // 0xb0ae88: cmp             w0, NULL
    // 0xb0ae8c: b.ne            #0xb0ae98
    // 0xb0ae90: r0 = Null
    //     0xb0ae90: mov             x0, NULL
    // 0xb0ae94: b               #0xb0aea4
    // 0xb0ae98: LoadField: r1 = r0->field_b
    //     0xb0ae98: ldur            w1, [x0, #0xb]
    // 0xb0ae9c: DecompressPointer r1
    //     0xb0ae9c: add             x1, x1, HEAP, lsl #32
    // 0xb0aea0: mov             x0, x1
    // 0xb0aea4: cmp             w0, NULL
    // 0xb0aea8: b.ne            #0xb0aeb4
    // 0xb0aeac: r4 = ""
    //     0xb0aeac: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0aeb0: b               #0xb0aeb8
    // 0xb0aeb4: mov             x4, x0
    // 0xb0aeb8: ldur            x0, [fp, #-0x18]
    // 0xb0aebc: stur            x4, [fp, #-0x10]
    // 0xb0aec0: r1 = Function '<anonymous closure>':.
    //     0xb0aec0: add             x1, PP, #0x61, lsl #12  ; [pp+0x61ed8] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb0aec4: ldr             x1, [x1, #0xed8]
    // 0xb0aec8: r2 = Null
    //     0xb0aec8: mov             x2, NULL
    // 0xb0aecc: r0 = AllocateClosure()
    //     0xb0aecc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0aed0: r1 = Function '<anonymous closure>':.
    //     0xb0aed0: add             x1, PP, #0x61, lsl #12  ; [pp+0x61ee0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb0aed4: ldr             x1, [x1, #0xee0]
    // 0xb0aed8: r2 = Null
    //     0xb0aed8: mov             x2, NULL
    // 0xb0aedc: stur            x0, [fp, #-0x30]
    // 0xb0aee0: r0 = AllocateClosure()
    //     0xb0aee0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0aee4: stur            x0, [fp, #-0x38]
    // 0xb0aee8: r0 = CachedNetworkImage()
    //     0xb0aee8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb0aeec: stur            x0, [fp, #-0x40]
    // 0xb0aef0: r16 = Instance_BoxFit
    //     0xb0aef0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb0aef4: ldr             x16, [x16, #0x118]
    // 0xb0aef8: ldur            lr, [fp, #-0x30]
    // 0xb0aefc: stp             lr, x16, [SP, #8]
    // 0xb0af00: ldur            x16, [fp, #-0x38]
    // 0xb0af04: str             x16, [SP]
    // 0xb0af08: mov             x1, x0
    // 0xb0af0c: ldur            x2, [fp, #-0x10]
    // 0xb0af10: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xb0af10: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xb0af14: ldr             x4, [x4, #0x638]
    // 0xb0af18: r0 = CachedNetworkImage()
    //     0xb0af18: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb0af1c: r0 = Card()
    //     0xb0af1c: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb0af20: mov             x1, x0
    // 0xb0af24: r0 = Instance_Color
    //     0xb0af24: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb0af28: ldr             x0, [x0, #0xf88]
    // 0xb0af2c: stur            x1, [fp, #-0x10]
    // 0xb0af30: StoreField: r1->field_b = r0
    //     0xb0af30: stur            w0, [x1, #0xb]
    // 0xb0af34: r0 = 0.000000
    //     0xb0af34: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb0af38: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0af38: stur            w0, [x1, #0x17]
    // 0xb0af3c: ldur            x0, [fp, #-0x28]
    // 0xb0af40: StoreField: r1->field_1b = r0
    //     0xb0af40: stur            w0, [x1, #0x1b]
    // 0xb0af44: r0 = true
    //     0xb0af44: add             x0, NULL, #0x20  ; true
    // 0xb0af48: StoreField: r1->field_1f = r0
    //     0xb0af48: stur            w0, [x1, #0x1f]
    // 0xb0af4c: r2 = Instance_EdgeInsets
    //     0xb0af4c: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb0af50: StoreField: r1->field_27 = r2
    //     0xb0af50: stur            w2, [x1, #0x27]
    // 0xb0af54: r2 = Instance_Clip
    //     0xb0af54: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xb0af58: ldr             x2, [x2, #0xb50]
    // 0xb0af5c: StoreField: r1->field_23 = r2
    //     0xb0af5c: stur            w2, [x1, #0x23]
    // 0xb0af60: ldur            x2, [fp, #-0x40]
    // 0xb0af64: StoreField: r1->field_2f = r2
    //     0xb0af64: stur            w2, [x1, #0x2f]
    // 0xb0af68: StoreField: r1->field_2b = r0
    //     0xb0af68: stur            w0, [x1, #0x2b]
    // 0xb0af6c: r2 = Instance__CardVariant
    //     0xb0af6c: add             x2, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb0af70: ldr             x2, [x2, #0xa68]
    // 0xb0af74: StoreField: r1->field_33 = r2
    //     0xb0af74: stur            w2, [x1, #0x33]
    // 0xb0af78: r0 = Center()
    //     0xb0af78: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb0af7c: mov             x3, x0
    // 0xb0af80: r0 = Instance_Alignment
    //     0xb0af80: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb0af84: ldr             x0, [x0, #0xb10]
    // 0xb0af88: stur            x3, [fp, #-0x28]
    // 0xb0af8c: StoreField: r3->field_f = r0
    //     0xb0af8c: stur            w0, [x3, #0xf]
    // 0xb0af90: ldur            x0, [fp, #-0x10]
    // 0xb0af94: StoreField: r3->field_b = r0
    //     0xb0af94: stur            w0, [x3, #0xb]
    // 0xb0af98: r1 = Null
    //     0xb0af98: mov             x1, NULL
    // 0xb0af9c: r2 = 2
    //     0xb0af9c: movz            x2, #0x2
    // 0xb0afa0: r0 = AllocateArray()
    //     0xb0afa0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0afa4: mov             x2, x0
    // 0xb0afa8: ldur            x0, [fp, #-0x28]
    // 0xb0afac: stur            x2, [fp, #-0x10]
    // 0xb0afb0: StoreField: r2->field_f = r0
    //     0xb0afb0: stur            w0, [x2, #0xf]
    // 0xb0afb4: r1 = <Widget>
    //     0xb0afb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0afb8: r0 = AllocateGrowableArray()
    //     0xb0afb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0afbc: mov             x3, x0
    // 0xb0afc0: ldur            x0, [fp, #-0x10]
    // 0xb0afc4: stur            x3, [fp, #-0x28]
    // 0xb0afc8: StoreField: r3->field_f = r0
    //     0xb0afc8: stur            w0, [x3, #0xf]
    // 0xb0afcc: r0 = 2
    //     0xb0afcc: movz            x0, #0x2
    // 0xb0afd0: StoreField: r3->field_b = r0
    //     0xb0afd0: stur            w0, [x3, #0xb]
    // 0xb0afd4: ldur            x0, [fp, #-0x18]
    // 0xb0afd8: cmp             w0, NULL
    // 0xb0afdc: b.ne            #0xb0afe8
    // 0xb0afe0: r1 = Null
    //     0xb0afe0: mov             x1, NULL
    // 0xb0afe4: b               #0xb0b018
    // 0xb0afe8: r17 = 295
    //     0xb0afe8: movz            x17, #0x127
    // 0xb0afec: ldr             w1, [x0, x17]
    // 0xb0aff0: DecompressPointer r1
    //     0xb0aff0: add             x1, x1, HEAP, lsl #32
    // 0xb0aff4: cmp             w1, NULL
    // 0xb0aff8: b.ne            #0xb0b004
    // 0xb0affc: r1 = Null
    //     0xb0affc: mov             x1, NULL
    // 0xb0b000: b               #0xb0b018
    // 0xb0b004: LoadField: r2 = r1->field_7
    //     0xb0b004: ldur            w2, [x1, #7]
    // 0xb0b008: cbnz            w2, #0xb0b014
    // 0xb0b00c: r1 = false
    //     0xb0b00c: add             x1, NULL, #0x30  ; false
    // 0xb0b010: b               #0xb0b018
    // 0xb0b014: r1 = true
    //     0xb0b014: add             x1, NULL, #0x20  ; true
    // 0xb0b018: cmp             w1, NULL
    // 0xb0b01c: b.eq            #0xb0b0b8
    // 0xb0b020: tbnz            w1, #4, #0xb0b0b8
    // 0xb0b024: cmp             w0, NULL
    // 0xb0b028: b.eq            #0xb0b308
    // 0xb0b02c: ldur            x1, [fp, #-8]
    // 0xb0b030: mov             x2, x0
    // 0xb0b034: r0 = _buildDiscountBadge()
    //     0xb0b034: bl              #0xb0b75c  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildDiscountBadge
    // 0xb0b038: mov             x2, x0
    // 0xb0b03c: ldur            x0, [fp, #-0x28]
    // 0xb0b040: stur            x2, [fp, #-0x10]
    // 0xb0b044: LoadField: r1 = r0->field_b
    //     0xb0b044: ldur            w1, [x0, #0xb]
    // 0xb0b048: LoadField: r3 = r0->field_f
    //     0xb0b048: ldur            w3, [x0, #0xf]
    // 0xb0b04c: DecompressPointer r3
    //     0xb0b04c: add             x3, x3, HEAP, lsl #32
    // 0xb0b050: LoadField: r4 = r3->field_b
    //     0xb0b050: ldur            w4, [x3, #0xb]
    // 0xb0b054: r3 = LoadInt32Instr(r1)
    //     0xb0b054: sbfx            x3, x1, #1, #0x1f
    // 0xb0b058: stur            x3, [fp, #-0x48]
    // 0xb0b05c: r1 = LoadInt32Instr(r4)
    //     0xb0b05c: sbfx            x1, x4, #1, #0x1f
    // 0xb0b060: cmp             x3, x1
    // 0xb0b064: b.ne            #0xb0b070
    // 0xb0b068: mov             x1, x0
    // 0xb0b06c: r0 = _growToNextCapacity()
    //     0xb0b06c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0b070: ldur            x3, [fp, #-0x28]
    // 0xb0b074: ldur            x2, [fp, #-0x48]
    // 0xb0b078: add             x0, x2, #1
    // 0xb0b07c: lsl             x1, x0, #1
    // 0xb0b080: StoreField: r3->field_b = r1
    //     0xb0b080: stur            w1, [x3, #0xb]
    // 0xb0b084: LoadField: r1 = r3->field_f
    //     0xb0b084: ldur            w1, [x3, #0xf]
    // 0xb0b088: DecompressPointer r1
    //     0xb0b088: add             x1, x1, HEAP, lsl #32
    // 0xb0b08c: ldur            x0, [fp, #-0x10]
    // 0xb0b090: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb0b090: add             x25, x1, x2, lsl #2
    //     0xb0b094: add             x25, x25, #0xf
    //     0xb0b098: str             w0, [x25]
    //     0xb0b09c: tbz             w0, #0, #0xb0b0b8
    //     0xb0b0a0: ldurb           w16, [x1, #-1]
    //     0xb0b0a4: ldurb           w17, [x0, #-1]
    //     0xb0b0a8: and             x16, x17, x16, lsr #2
    //     0xb0b0ac: tst             x16, HEAP, lsr #32
    //     0xb0b0b0: b.eq            #0xb0b0b8
    //     0xb0b0b4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0b0b8: ldur            x0, [fp, #-0x18]
    // 0xb0b0bc: cmp             w0, NULL
    // 0xb0b0c0: b.ne            #0xb0b0cc
    // 0xb0b0c4: r1 = Null
    //     0xb0b0c4: mov             x1, NULL
    // 0xb0b0c8: b               #0xb0b0fc
    // 0xb0b0cc: r17 = 311
    //     0xb0b0cc: movz            x17, #0x137
    // 0xb0b0d0: ldr             w1, [x0, x17]
    // 0xb0b0d4: DecompressPointer r1
    //     0xb0b0d4: add             x1, x1, HEAP, lsl #32
    // 0xb0b0d8: cmp             w1, NULL
    // 0xb0b0dc: b.ne            #0xb0b0e8
    // 0xb0b0e0: r1 = Null
    //     0xb0b0e0: mov             x1, NULL
    // 0xb0b0e4: b               #0xb0b0fc
    // 0xb0b0e8: LoadField: r2 = r1->field_7
    //     0xb0b0e8: ldur            w2, [x1, #7]
    // 0xb0b0ec: cbnz            w2, #0xb0b0f8
    // 0xb0b0f0: r1 = false
    //     0xb0b0f0: add             x1, NULL, #0x30  ; false
    // 0xb0b0f4: b               #0xb0b0fc
    // 0xb0b0f8: r1 = true
    //     0xb0b0f8: add             x1, NULL, #0x20  ; true
    // 0xb0b0fc: cmp             w1, NULL
    // 0xb0b100: b.ne            #0xb0b10c
    // 0xb0b104: mov             x2, x3
    // 0xb0b108: b               #0xb0b1ac
    // 0xb0b10c: tbnz            w1, #4, #0xb0b1a8
    // 0xb0b110: cmp             w0, NULL
    // 0xb0b114: b.eq            #0xb0b30c
    // 0xb0b118: ldur            x1, [fp, #-8]
    // 0xb0b11c: mov             x2, x0
    // 0xb0b120: r0 = _buildStockAlert()
    //     0xb0b120: bl              #0xb0b4f4  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildStockAlert
    // 0xb0b124: mov             x2, x0
    // 0xb0b128: ldur            x0, [fp, #-0x28]
    // 0xb0b12c: stur            x2, [fp, #-0x10]
    // 0xb0b130: LoadField: r1 = r0->field_b
    //     0xb0b130: ldur            w1, [x0, #0xb]
    // 0xb0b134: LoadField: r3 = r0->field_f
    //     0xb0b134: ldur            w3, [x0, #0xf]
    // 0xb0b138: DecompressPointer r3
    //     0xb0b138: add             x3, x3, HEAP, lsl #32
    // 0xb0b13c: LoadField: r4 = r3->field_b
    //     0xb0b13c: ldur            w4, [x3, #0xb]
    // 0xb0b140: r3 = LoadInt32Instr(r1)
    //     0xb0b140: sbfx            x3, x1, #1, #0x1f
    // 0xb0b144: stur            x3, [fp, #-0x48]
    // 0xb0b148: r1 = LoadInt32Instr(r4)
    //     0xb0b148: sbfx            x1, x4, #1, #0x1f
    // 0xb0b14c: cmp             x3, x1
    // 0xb0b150: b.ne            #0xb0b15c
    // 0xb0b154: mov             x1, x0
    // 0xb0b158: r0 = _growToNextCapacity()
    //     0xb0b158: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0b15c: ldur            x2, [fp, #-0x28]
    // 0xb0b160: ldur            x3, [fp, #-0x48]
    // 0xb0b164: add             x0, x3, #1
    // 0xb0b168: lsl             x1, x0, #1
    // 0xb0b16c: StoreField: r2->field_b = r1
    //     0xb0b16c: stur            w1, [x2, #0xb]
    // 0xb0b170: LoadField: r1 = r2->field_f
    //     0xb0b170: ldur            w1, [x2, #0xf]
    // 0xb0b174: DecompressPointer r1
    //     0xb0b174: add             x1, x1, HEAP, lsl #32
    // 0xb0b178: ldur            x0, [fp, #-0x10]
    // 0xb0b17c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0b17c: add             x25, x1, x3, lsl #2
    //     0xb0b180: add             x25, x25, #0xf
    //     0xb0b184: str             w0, [x25]
    //     0xb0b188: tbz             w0, #0, #0xb0b1a4
    //     0xb0b18c: ldurb           w16, [x1, #-1]
    //     0xb0b190: ldurb           w17, [x0, #-1]
    //     0xb0b194: and             x16, x17, x16, lsr #2
    //     0xb0b198: tst             x16, HEAP, lsr #32
    //     0xb0b19c: b.eq            #0xb0b1a4
    //     0xb0b1a0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0b1a4: b               #0xb0b1ac
    // 0xb0b1a8: mov             x2, x3
    // 0xb0b1ac: ldur            x0, [fp, #-0x18]
    // 0xb0b1b0: cmp             w0, NULL
    // 0xb0b1b4: b.ne            #0xb0b1c0
    // 0xb0b1b8: r0 = Null
    //     0xb0b1b8: mov             x0, NULL
    // 0xb0b1bc: b               #0xb0b1d0
    // 0xb0b1c0: r17 = 263
    //     0xb0b1c0: movz            x17, #0x107
    // 0xb0b1c4: ldr             w1, [x0, x17]
    // 0xb0b1c8: DecompressPointer r1
    //     0xb0b1c8: add             x1, x1, HEAP, lsl #32
    // 0xb0b1cc: mov             x0, x1
    // 0xb0b1d0: cmp             w0, NULL
    // 0xb0b1d4: b.eq            #0xb0b264
    // 0xb0b1d8: tbnz            w0, #4, #0xb0b264
    // 0xb0b1dc: ldur            x1, [fp, #-8]
    // 0xb0b1e0: r0 = _buildCustomizationBadge()
    //     0xb0b1e0: bl              #0xb0b310  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildCustomizationBadge
    // 0xb0b1e4: mov             x2, x0
    // 0xb0b1e8: ldur            x0, [fp, #-0x28]
    // 0xb0b1ec: stur            x2, [fp, #-8]
    // 0xb0b1f0: LoadField: r1 = r0->field_b
    //     0xb0b1f0: ldur            w1, [x0, #0xb]
    // 0xb0b1f4: LoadField: r3 = r0->field_f
    //     0xb0b1f4: ldur            w3, [x0, #0xf]
    // 0xb0b1f8: DecompressPointer r3
    //     0xb0b1f8: add             x3, x3, HEAP, lsl #32
    // 0xb0b1fc: LoadField: r4 = r3->field_b
    //     0xb0b1fc: ldur            w4, [x3, #0xb]
    // 0xb0b200: r3 = LoadInt32Instr(r1)
    //     0xb0b200: sbfx            x3, x1, #1, #0x1f
    // 0xb0b204: stur            x3, [fp, #-0x48]
    // 0xb0b208: r1 = LoadInt32Instr(r4)
    //     0xb0b208: sbfx            x1, x4, #1, #0x1f
    // 0xb0b20c: cmp             x3, x1
    // 0xb0b210: b.ne            #0xb0b21c
    // 0xb0b214: mov             x1, x0
    // 0xb0b218: r0 = _growToNextCapacity()
    //     0xb0b218: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0b21c: ldur            x2, [fp, #-0x28]
    // 0xb0b220: ldur            x3, [fp, #-0x48]
    // 0xb0b224: add             x0, x3, #1
    // 0xb0b228: lsl             x1, x0, #1
    // 0xb0b22c: StoreField: r2->field_b = r1
    //     0xb0b22c: stur            w1, [x2, #0xb]
    // 0xb0b230: LoadField: r1 = r2->field_f
    //     0xb0b230: ldur            w1, [x2, #0xf]
    // 0xb0b234: DecompressPointer r1
    //     0xb0b234: add             x1, x1, HEAP, lsl #32
    // 0xb0b238: ldur            x0, [fp, #-8]
    // 0xb0b23c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0b23c: add             x25, x1, x3, lsl #2
    //     0xb0b240: add             x25, x25, #0xf
    //     0xb0b244: str             w0, [x25]
    //     0xb0b248: tbz             w0, #0, #0xb0b264
    //     0xb0b24c: ldurb           w16, [x1, #-1]
    //     0xb0b250: ldurb           w17, [x0, #-1]
    //     0xb0b254: and             x16, x17, x16, lsr #2
    //     0xb0b258: tst             x16, HEAP, lsr #32
    //     0xb0b25c: b.eq            #0xb0b264
    //     0xb0b260: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0b264: r0 = Stack()
    //     0xb0b264: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb0b268: mov             x1, x0
    // 0xb0b26c: r0 = Instance_Alignment
    //     0xb0b26c: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xb0b270: ldr             x0, [x0, #0x5b8]
    // 0xb0b274: stur            x1, [fp, #-8]
    // 0xb0b278: StoreField: r1->field_f = r0
    //     0xb0b278: stur            w0, [x1, #0xf]
    // 0xb0b27c: r0 = Instance_StackFit
    //     0xb0b27c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb0b280: ldr             x0, [x0, #0xfa8]
    // 0xb0b284: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0b284: stur            w0, [x1, #0x17]
    // 0xb0b288: r0 = Instance_Clip
    //     0xb0b288: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb0b28c: ldr             x0, [x0, #0x7e0]
    // 0xb0b290: StoreField: r1->field_1b = r0
    //     0xb0b290: stur            w0, [x1, #0x1b]
    // 0xb0b294: ldur            x0, [fp, #-0x28]
    // 0xb0b298: StoreField: r1->field_b = r0
    //     0xb0b298: stur            w0, [x1, #0xb]
    // 0xb0b29c: r0 = InkWell()
    //     0xb0b29c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb0b2a0: mov             x3, x0
    // 0xb0b2a4: ldur            x0, [fp, #-8]
    // 0xb0b2a8: stur            x3, [fp, #-0x10]
    // 0xb0b2ac: StoreField: r3->field_b = r0
    //     0xb0b2ac: stur            w0, [x3, #0xb]
    // 0xb0b2b0: ldur            x2, [fp, #-0x20]
    // 0xb0b2b4: r1 = Function '<anonymous closure>':.
    //     0xb0b2b4: add             x1, PP, #0x61, lsl #12  ; [pp+0x61ee8] AnonymousClosure: (0xb0bfac), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::getCosmeticThemeSlider (0xb0ade8)
    //     0xb0b2b8: ldr             x1, [x1, #0xee8]
    // 0xb0b2bc: r0 = AllocateClosure()
    //     0xb0b2bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0b2c0: mov             x1, x0
    // 0xb0b2c4: ldur            x0, [fp, #-0x10]
    // 0xb0b2c8: StoreField: r0->field_f = r1
    //     0xb0b2c8: stur            w1, [x0, #0xf]
    // 0xb0b2cc: r1 = true
    //     0xb0b2cc: add             x1, NULL, #0x20  ; true
    // 0xb0b2d0: StoreField: r0->field_43 = r1
    //     0xb0b2d0: stur            w1, [x0, #0x43]
    // 0xb0b2d4: r2 = Instance_BoxShape
    //     0xb0b2d4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb0b2d8: ldr             x2, [x2, #0x80]
    // 0xb0b2dc: StoreField: r0->field_47 = r2
    //     0xb0b2dc: stur            w2, [x0, #0x47]
    // 0xb0b2e0: StoreField: r0->field_6f = r1
    //     0xb0b2e0: stur            w1, [x0, #0x6f]
    // 0xb0b2e4: r2 = false
    //     0xb0b2e4: add             x2, NULL, #0x30  ; false
    // 0xb0b2e8: StoreField: r0->field_73 = r2
    //     0xb0b2e8: stur            w2, [x0, #0x73]
    // 0xb0b2ec: StoreField: r0->field_83 = r1
    //     0xb0b2ec: stur            w1, [x0, #0x83]
    // 0xb0b2f0: StoreField: r0->field_7b = r2
    //     0xb0b2f0: stur            w2, [x0, #0x7b]
    // 0xb0b2f4: LeaveFrame
    //     0xb0b2f4: mov             SP, fp
    //     0xb0b2f8: ldp             fp, lr, [SP], #0x10
    // 0xb0b2fc: ret
    //     0xb0b2fc: ret             
    // 0xb0b300: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0b300: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0b304: b               #0xb0ae14
    // 0xb0b308: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0b308: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0b30c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0b30c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildCustomizationBadge(/* No info */) {
    // ** addr: 0xb0b310, size: 0x1e4
    // 0xb0b310: EnterFrame
    //     0xb0b310: stp             fp, lr, [SP, #-0x10]!
    //     0xb0b314: mov             fp, SP
    // 0xb0b318: AllocStack(0x38)
    //     0xb0b318: sub             SP, SP, #0x38
    // 0xb0b31c: SetupParameters(_ProductGridItemState this /* r1 => r0, fp-0x8 */)
    //     0xb0b31c: mov             x0, x1
    //     0xb0b320: stur            x1, [fp, #-8]
    // 0xb0b324: CheckStackOverflow
    //     0xb0b324: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0b328: cmp             SP, x16
    //     0xb0b32c: b.ls            #0xb0b4e4
    // 0xb0b330: LoadField: r1 = r0->field_f
    //     0xb0b330: ldur            w1, [x0, #0xf]
    // 0xb0b334: DecompressPointer r1
    //     0xb0b334: add             x1, x1, HEAP, lsl #32
    // 0xb0b338: cmp             w1, NULL
    // 0xb0b33c: b.eq            #0xb0b4ec
    // 0xb0b340: r0 = of()
    //     0xb0b340: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0b344: r17 = 307
    //     0xb0b344: movz            x17, #0x133
    // 0xb0b348: ldr             w1, [x0, x17]
    // 0xb0b34c: DecompressPointer r1
    //     0xb0b34c: add             x1, x1, HEAP, lsl #32
    // 0xb0b350: stur            x1, [fp, #-0x10]
    // 0xb0b354: r0 = Radius()
    //     0xb0b354: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb0b358: d0 = 5.000000
    //     0xb0b358: fmov            d0, #5.00000000
    // 0xb0b35c: stur            x0, [fp, #-0x18]
    // 0xb0b360: StoreField: r0->field_7 = d0
    //     0xb0b360: stur            d0, [x0, #7]
    // 0xb0b364: StoreField: r0->field_f = d0
    //     0xb0b364: stur            d0, [x0, #0xf]
    // 0xb0b368: r0 = BorderRadius()
    //     0xb0b368: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb0b36c: mov             x1, x0
    // 0xb0b370: ldur            x0, [fp, #-0x18]
    // 0xb0b374: stur            x1, [fp, #-0x20]
    // 0xb0b378: StoreField: r1->field_7 = r0
    //     0xb0b378: stur            w0, [x1, #7]
    // 0xb0b37c: StoreField: r1->field_b = r0
    //     0xb0b37c: stur            w0, [x1, #0xb]
    // 0xb0b380: StoreField: r1->field_f = r0
    //     0xb0b380: stur            w0, [x1, #0xf]
    // 0xb0b384: StoreField: r1->field_13 = r0
    //     0xb0b384: stur            w0, [x1, #0x13]
    // 0xb0b388: r0 = RoundedRectangleBorder()
    //     0xb0b388: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb0b38c: mov             x1, x0
    // 0xb0b390: ldur            x0, [fp, #-0x20]
    // 0xb0b394: StoreField: r1->field_b = r0
    //     0xb0b394: stur            w0, [x1, #0xb]
    // 0xb0b398: r0 = Instance_BorderSide
    //     0xb0b398: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb0b39c: ldr             x0, [x0, #0xe20]
    // 0xb0b3a0: StoreField: r1->field_7 = r0
    //     0xb0b3a0: stur            w0, [x1, #7]
    // 0xb0b3a4: r16 = <RoundedRectangleBorder>
    //     0xb0b3a4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb0b3a8: ldr             x16, [x16, #0xf78]
    // 0xb0b3ac: stp             x1, x16, [SP]
    // 0xb0b3b0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb0b3b0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb0b3b4: r0 = all()
    //     0xb0b3b4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb0b3b8: stur            x0, [fp, #-0x18]
    // 0xb0b3bc: r0 = ButtonStyle()
    //     0xb0b3bc: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb0b3c0: mov             x2, x0
    // 0xb0b3c4: ldur            x0, [fp, #-0x18]
    // 0xb0b3c8: stur            x2, [fp, #-0x20]
    // 0xb0b3cc: StoreField: r2->field_43 = r0
    //     0xb0b3cc: stur            w0, [x2, #0x43]
    // 0xb0b3d0: ldur            x0, [fp, #-8]
    // 0xb0b3d4: LoadField: r1 = r0->field_f
    //     0xb0b3d4: ldur            w1, [x0, #0xf]
    // 0xb0b3d8: DecompressPointer r1
    //     0xb0b3d8: add             x1, x1, HEAP, lsl #32
    // 0xb0b3dc: cmp             w1, NULL
    // 0xb0b3e0: b.eq            #0xb0b4f0
    // 0xb0b3e4: r0 = of()
    //     0xb0b3e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0b3e8: LoadField: r1 = r0->field_87
    //     0xb0b3e8: ldur            w1, [x0, #0x87]
    // 0xb0b3ec: DecompressPointer r1
    //     0xb0b3ec: add             x1, x1, HEAP, lsl #32
    // 0xb0b3f0: LoadField: r0 = r1->field_2b
    //     0xb0b3f0: ldur            w0, [x1, #0x2b]
    // 0xb0b3f4: DecompressPointer r0
    //     0xb0b3f4: add             x0, x0, HEAP, lsl #32
    // 0xb0b3f8: r16 = 12.000000
    //     0xb0b3f8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb0b3fc: ldr             x16, [x16, #0x9e8]
    // 0xb0b400: r30 = Instance_Color
    //     0xb0b400: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb0b404: stp             lr, x16, [SP]
    // 0xb0b408: mov             x1, x0
    // 0xb0b40c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0b40c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0b410: ldr             x4, [x4, #0xaa0]
    // 0xb0b414: r0 = copyWith()
    //     0xb0b414: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0b418: stur            x0, [fp, #-8]
    // 0xb0b41c: r0 = Text()
    //     0xb0b41c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0b420: mov             x3, x0
    // 0xb0b424: r0 = "Customisable"
    //     0xb0b424: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xb0b428: ldr             x0, [x0, #0x970]
    // 0xb0b42c: stur            x3, [fp, #-0x18]
    // 0xb0b430: StoreField: r3->field_b = r0
    //     0xb0b430: stur            w0, [x3, #0xb]
    // 0xb0b434: ldur            x0, [fp, #-8]
    // 0xb0b438: StoreField: r3->field_13 = r0
    //     0xb0b438: stur            w0, [x3, #0x13]
    // 0xb0b43c: r1 = Function '<anonymous closure>':.
    //     0xb0b43c: add             x1, PP, #0x61, lsl #12  ; [pp+0x61f00] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb0b440: ldr             x1, [x1, #0xf00]
    // 0xb0b444: r2 = Null
    //     0xb0b444: mov             x2, NULL
    // 0xb0b448: r0 = AllocateClosure()
    //     0xb0b448: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0b44c: stur            x0, [fp, #-8]
    // 0xb0b450: r0 = TextButton()
    //     0xb0b450: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb0b454: mov             x1, x0
    // 0xb0b458: ldur            x0, [fp, #-8]
    // 0xb0b45c: stur            x1, [fp, #-0x28]
    // 0xb0b460: StoreField: r1->field_b = r0
    //     0xb0b460: stur            w0, [x1, #0xb]
    // 0xb0b464: ldur            x0, [fp, #-0x20]
    // 0xb0b468: StoreField: r1->field_1b = r0
    //     0xb0b468: stur            w0, [x1, #0x1b]
    // 0xb0b46c: r0 = false
    //     0xb0b46c: add             x0, NULL, #0x30  ; false
    // 0xb0b470: StoreField: r1->field_27 = r0
    //     0xb0b470: stur            w0, [x1, #0x27]
    // 0xb0b474: r0 = true
    //     0xb0b474: add             x0, NULL, #0x20  ; true
    // 0xb0b478: StoreField: r1->field_2f = r0
    //     0xb0b478: stur            w0, [x1, #0x2f]
    // 0xb0b47c: ldur            x0, [fp, #-0x18]
    // 0xb0b480: StoreField: r1->field_37 = r0
    //     0xb0b480: stur            w0, [x1, #0x37]
    // 0xb0b484: r0 = TextButtonTheme()
    //     0xb0b484: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb0b488: mov             x1, x0
    // 0xb0b48c: ldur            x0, [fp, #-0x10]
    // 0xb0b490: stur            x1, [fp, #-8]
    // 0xb0b494: StoreField: r1->field_f = r0
    //     0xb0b494: stur            w0, [x1, #0xf]
    // 0xb0b498: ldur            x0, [fp, #-0x28]
    // 0xb0b49c: StoreField: r1->field_b = r0
    //     0xb0b49c: stur            w0, [x1, #0xb]
    // 0xb0b4a0: r0 = SizedBox()
    //     0xb0b4a0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb0b4a4: mov             x1, x0
    // 0xb0b4a8: r0 = 30.000000
    //     0xb0b4a8: add             x0, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xb0b4ac: ldr             x0, [x0, #0x768]
    // 0xb0b4b0: stur            x1, [fp, #-0x10]
    // 0xb0b4b4: StoreField: r1->field_13 = r0
    //     0xb0b4b4: stur            w0, [x1, #0x13]
    // 0xb0b4b8: ldur            x0, [fp, #-8]
    // 0xb0b4bc: StoreField: r1->field_b = r0
    //     0xb0b4bc: stur            w0, [x1, #0xb]
    // 0xb0b4c0: r0 = Padding()
    //     0xb0b4c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0b4c4: r1 = Instance_EdgeInsets
    //     0xb0b4c4: add             x1, PP, #0x52, lsl #12  ; [pp+0x52e68] Obj!EdgeInsets@d58461
    //     0xb0b4c8: ldr             x1, [x1, #0xe68]
    // 0xb0b4cc: StoreField: r0->field_f = r1
    //     0xb0b4cc: stur            w1, [x0, #0xf]
    // 0xb0b4d0: ldur            x1, [fp, #-0x10]
    // 0xb0b4d4: StoreField: r0->field_b = r1
    //     0xb0b4d4: stur            w1, [x0, #0xb]
    // 0xb0b4d8: LeaveFrame
    //     0xb0b4d8: mov             SP, fp
    //     0xb0b4dc: ldp             fp, lr, [SP], #0x10
    // 0xb0b4e0: ret
    //     0xb0b4e0: ret             
    // 0xb0b4e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0b4e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0b4e8: b               #0xb0b330
    // 0xb0b4ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0b4ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0b4f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0b4f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildStockAlert(/* No info */) {
    // ** addr: 0xb0b4f4, size: 0x268
    // 0xb0b4f4: EnterFrame
    //     0xb0b4f4: stp             fp, lr, [SP, #-0x10]!
    //     0xb0b4f8: mov             fp, SP
    // 0xb0b4fc: AllocStack(0x50)
    //     0xb0b4fc: sub             SP, SP, #0x50
    // 0xb0b500: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb0b500: stur            x1, [fp, #-8]
    //     0xb0b504: stur            x2, [fp, #-0x10]
    // 0xb0b508: CheckStackOverflow
    //     0xb0b508: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0b50c: cmp             SP, x16
    //     0xb0b510: b.ls            #0xb0b750
    // 0xb0b514: r17 = 263
    //     0xb0b514: movz            x17, #0x107
    // 0xb0b518: ldr             w0, [x2, x17]
    // 0xb0b51c: DecompressPointer r0
    //     0xb0b51c: add             x0, x0, HEAP, lsl #32
    // 0xb0b520: cmp             w0, NULL
    // 0xb0b524: b.eq            #0xb0b538
    // 0xb0b528: tbnz            w0, #4, #0xb0b538
    // 0xb0b52c: d0 = 38.000000
    //     0xb0b52c: add             x17, PP, #0x50, lsl #12  ; [pp+0x50d10] IMM: double(38) from 0x4043000000000000
    //     0xb0b530: ldr             d0, [x17, #0xd10]
    // 0xb0b534: b               #0xb0b53c
    // 0xb0b538: d0 = 4.000000
    //     0xb0b538: fmov            d0, #4.00000000
    // 0xb0b53c: stur            d0, [fp, #-0x40]
    // 0xb0b540: r0 = EdgeInsets()
    //     0xb0b540: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xb0b544: d0 = 8.000000
    //     0xb0b544: fmov            d0, #8.00000000
    // 0xb0b548: stur            x0, [fp, #-0x18]
    // 0xb0b54c: StoreField: r0->field_7 = d0
    //     0xb0b54c: stur            d0, [x0, #7]
    // 0xb0b550: StoreField: r0->field_f = rZR
    //     0xb0b550: stur            xzr, [x0, #0xf]
    // 0xb0b554: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb0b554: stur            xzr, [x0, #0x17]
    // 0xb0b558: ldur            d0, [fp, #-0x40]
    // 0xb0b55c: StoreField: r0->field_1f = d0
    //     0xb0b55c: stur            d0, [x0, #0x1f]
    // 0xb0b560: r16 = <EdgeInsets>
    //     0xb0b560: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb0b564: ldr             x16, [x16, #0xda0]
    // 0xb0b568: r30 = Instance_EdgeInsets
    //     0xb0b568: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb0b56c: ldr             lr, [lr, #0x668]
    // 0xb0b570: stp             lr, x16, [SP]
    // 0xb0b574: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb0b574: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb0b578: r0 = all()
    //     0xb0b578: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb0b57c: stur            x0, [fp, #-0x20]
    // 0xb0b580: r16 = <Color>
    //     0xb0b580: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb0b584: ldr             x16, [x16, #0xf80]
    // 0xb0b588: r30 = Instance_Color
    //     0xb0b588: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb0b58c: stp             lr, x16, [SP]
    // 0xb0b590: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb0b590: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb0b594: r0 = all()
    //     0xb0b594: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb0b598: stur            x0, [fp, #-0x28]
    // 0xb0b59c: r0 = Radius()
    //     0xb0b59c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb0b5a0: d0 = 5.000000
    //     0xb0b5a0: fmov            d0, #5.00000000
    // 0xb0b5a4: stur            x0, [fp, #-0x30]
    // 0xb0b5a8: StoreField: r0->field_7 = d0
    //     0xb0b5a8: stur            d0, [x0, #7]
    // 0xb0b5ac: StoreField: r0->field_f = d0
    //     0xb0b5ac: stur            d0, [x0, #0xf]
    // 0xb0b5b0: r0 = BorderRadius()
    //     0xb0b5b0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb0b5b4: mov             x1, x0
    // 0xb0b5b8: ldur            x0, [fp, #-0x30]
    // 0xb0b5bc: stur            x1, [fp, #-0x38]
    // 0xb0b5c0: StoreField: r1->field_7 = r0
    //     0xb0b5c0: stur            w0, [x1, #7]
    // 0xb0b5c4: StoreField: r1->field_b = r0
    //     0xb0b5c4: stur            w0, [x1, #0xb]
    // 0xb0b5c8: StoreField: r1->field_f = r0
    //     0xb0b5c8: stur            w0, [x1, #0xf]
    // 0xb0b5cc: StoreField: r1->field_13 = r0
    //     0xb0b5cc: stur            w0, [x1, #0x13]
    // 0xb0b5d0: r0 = RoundedRectangleBorder()
    //     0xb0b5d0: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb0b5d4: mov             x1, x0
    // 0xb0b5d8: ldur            x0, [fp, #-0x38]
    // 0xb0b5dc: StoreField: r1->field_b = r0
    //     0xb0b5dc: stur            w0, [x1, #0xb]
    // 0xb0b5e0: r0 = Instance_BorderSide
    //     0xb0b5e0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb0b5e4: ldr             x0, [x0, #0xe20]
    // 0xb0b5e8: StoreField: r1->field_7 = r0
    //     0xb0b5e8: stur            w0, [x1, #7]
    // 0xb0b5ec: r16 = <RoundedRectangleBorder>
    //     0xb0b5ec: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb0b5f0: ldr             x16, [x16, #0xf78]
    // 0xb0b5f4: stp             x1, x16, [SP]
    // 0xb0b5f8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb0b5f8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb0b5fc: r0 = all()
    //     0xb0b5fc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb0b600: stur            x0, [fp, #-0x30]
    // 0xb0b604: r0 = ButtonStyle()
    //     0xb0b604: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb0b608: mov             x1, x0
    // 0xb0b60c: ldur            x0, [fp, #-0x28]
    // 0xb0b610: stur            x1, [fp, #-0x38]
    // 0xb0b614: StoreField: r1->field_b = r0
    //     0xb0b614: stur            w0, [x1, #0xb]
    // 0xb0b618: ldur            x0, [fp, #-0x20]
    // 0xb0b61c: StoreField: r1->field_23 = r0
    //     0xb0b61c: stur            w0, [x1, #0x23]
    // 0xb0b620: ldur            x0, [fp, #-0x30]
    // 0xb0b624: StoreField: r1->field_43 = r0
    //     0xb0b624: stur            w0, [x1, #0x43]
    // 0xb0b628: r0 = TextButtonThemeData()
    //     0xb0b628: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb0b62c: mov             x2, x0
    // 0xb0b630: ldur            x0, [fp, #-0x38]
    // 0xb0b634: stur            x2, [fp, #-0x20]
    // 0xb0b638: StoreField: r2->field_7 = r0
    //     0xb0b638: stur            w0, [x2, #7]
    // 0xb0b63c: ldur            x0, [fp, #-0x10]
    // 0xb0b640: r17 = 311
    //     0xb0b640: movz            x17, #0x137
    // 0xb0b644: ldr             w1, [x0, x17]
    // 0xb0b648: DecompressPointer r1
    //     0xb0b648: add             x1, x1, HEAP, lsl #32
    // 0xb0b64c: cmp             w1, NULL
    // 0xb0b650: b.ne            #0xb0b65c
    // 0xb0b654: r3 = ""
    //     0xb0b654: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0b658: b               #0xb0b660
    // 0xb0b65c: mov             x3, x1
    // 0xb0b660: ldur            x1, [fp, #-8]
    // 0xb0b664: ldur            x0, [fp, #-0x18]
    // 0xb0b668: stur            x3, [fp, #-0x10]
    // 0xb0b66c: LoadField: r4 = r1->field_f
    //     0xb0b66c: ldur            w4, [x1, #0xf]
    // 0xb0b670: DecompressPointer r4
    //     0xb0b670: add             x4, x4, HEAP, lsl #32
    // 0xb0b674: cmp             w4, NULL
    // 0xb0b678: b.eq            #0xb0b758
    // 0xb0b67c: mov             x1, x4
    // 0xb0b680: r0 = of()
    //     0xb0b680: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0b684: LoadField: r1 = r0->field_87
    //     0xb0b684: ldur            w1, [x0, #0x87]
    // 0xb0b688: DecompressPointer r1
    //     0xb0b688: add             x1, x1, HEAP, lsl #32
    // 0xb0b68c: LoadField: r0 = r1->field_2b
    //     0xb0b68c: ldur            w0, [x1, #0x2b]
    // 0xb0b690: DecompressPointer r0
    //     0xb0b690: add             x0, x0, HEAP, lsl #32
    // 0xb0b694: r16 = 12.000000
    //     0xb0b694: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb0b698: ldr             x16, [x16, #0x9e8]
    // 0xb0b69c: r30 = Instance_Color
    //     0xb0b69c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb0b6a0: stp             lr, x16, [SP]
    // 0xb0b6a4: mov             x1, x0
    // 0xb0b6a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0b6a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0b6ac: ldr             x4, [x4, #0xaa0]
    // 0xb0b6b0: r0 = copyWith()
    //     0xb0b6b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0b6b4: stur            x0, [fp, #-8]
    // 0xb0b6b8: r0 = Text()
    //     0xb0b6b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0b6bc: mov             x3, x0
    // 0xb0b6c0: ldur            x0, [fp, #-0x10]
    // 0xb0b6c4: stur            x3, [fp, #-0x28]
    // 0xb0b6c8: StoreField: r3->field_b = r0
    //     0xb0b6c8: stur            w0, [x3, #0xb]
    // 0xb0b6cc: ldur            x0, [fp, #-8]
    // 0xb0b6d0: StoreField: r3->field_13 = r0
    //     0xb0b6d0: stur            w0, [x3, #0x13]
    // 0xb0b6d4: r1 = Function '<anonymous closure>':.
    //     0xb0b6d4: add             x1, PP, #0x61, lsl #12  ; [pp+0x61f08] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb0b6d8: ldr             x1, [x1, #0xf08]
    // 0xb0b6dc: r2 = Null
    //     0xb0b6dc: mov             x2, NULL
    // 0xb0b6e0: r0 = AllocateClosure()
    //     0xb0b6e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0b6e4: stur            x0, [fp, #-8]
    // 0xb0b6e8: r0 = TextButton()
    //     0xb0b6e8: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb0b6ec: mov             x1, x0
    // 0xb0b6f0: ldur            x0, [fp, #-8]
    // 0xb0b6f4: stur            x1, [fp, #-0x10]
    // 0xb0b6f8: StoreField: r1->field_b = r0
    //     0xb0b6f8: stur            w0, [x1, #0xb]
    // 0xb0b6fc: r0 = false
    //     0xb0b6fc: add             x0, NULL, #0x30  ; false
    // 0xb0b700: StoreField: r1->field_27 = r0
    //     0xb0b700: stur            w0, [x1, #0x27]
    // 0xb0b704: r0 = true
    //     0xb0b704: add             x0, NULL, #0x20  ; true
    // 0xb0b708: StoreField: r1->field_2f = r0
    //     0xb0b708: stur            w0, [x1, #0x2f]
    // 0xb0b70c: ldur            x0, [fp, #-0x28]
    // 0xb0b710: StoreField: r1->field_37 = r0
    //     0xb0b710: stur            w0, [x1, #0x37]
    // 0xb0b714: r0 = TextButtonTheme()
    //     0xb0b714: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb0b718: mov             x1, x0
    // 0xb0b71c: ldur            x0, [fp, #-0x20]
    // 0xb0b720: stur            x1, [fp, #-8]
    // 0xb0b724: StoreField: r1->field_f = r0
    //     0xb0b724: stur            w0, [x1, #0xf]
    // 0xb0b728: ldur            x0, [fp, #-0x10]
    // 0xb0b72c: StoreField: r1->field_b = r0
    //     0xb0b72c: stur            w0, [x1, #0xb]
    // 0xb0b730: r0 = Padding()
    //     0xb0b730: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0b734: ldur            x1, [fp, #-0x18]
    // 0xb0b738: StoreField: r0->field_f = r1
    //     0xb0b738: stur            w1, [x0, #0xf]
    // 0xb0b73c: ldur            x1, [fp, #-8]
    // 0xb0b740: StoreField: r0->field_b = r1
    //     0xb0b740: stur            w1, [x0, #0xb]
    // 0xb0b744: LeaveFrame
    //     0xb0b744: mov             SP, fp
    //     0xb0b748: ldp             fp, lr, [SP], #0x10
    // 0xb0b74c: ret
    //     0xb0b74c: ret             
    // 0xb0b750: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0b750: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0b754: b               #0xb0b514
    // 0xb0b758: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0b758: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildDiscountBadge(/* No info */) {
    // ** addr: 0xb0b75c, size: 0x850
    // 0xb0b75c: EnterFrame
    //     0xb0b75c: stp             fp, lr, [SP, #-0x10]!
    //     0xb0b760: mov             fp, SP
    // 0xb0b764: AllocStack(0x70)
    //     0xb0b764: sub             SP, SP, #0x70
    // 0xb0b768: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb0b768: stur            x1, [fp, #-8]
    //     0xb0b76c: stur            x2, [fp, #-0x10]
    // 0xb0b770: CheckStackOverflow
    //     0xb0b770: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0b774: cmp             SP, x16
    //     0xb0b778: b.ls            #0xb0bf78
    // 0xb0b77c: r17 = 271
    //     0xb0b77c: movz            x17, #0x10f
    // 0xb0b780: ldr             w0, [x2, x17]
    // 0xb0b784: DecompressPointer r0
    //     0xb0b784: add             x0, x0, HEAP, lsl #32
    // 0xb0b788: cmp             w0, NULL
    // 0xb0b78c: b.ne            #0xb0b7b0
    // 0xb0b790: mov             x4, x1
    // 0xb0b794: mov             x3, x2
    // 0xb0b798: r5 = Instance_Alignment
    //     0xb0b798: add             x5, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb0b79c: ldr             x5, [x5, #0xfa0]
    // 0xb0b7a0: r2 = 4
    //     0xb0b7a0: movz            x2, #0x4
    // 0xb0b7a4: r0 = Instance_BoxShape
    //     0xb0b7a4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb0b7a8: ldr             x0, [x0, #0x80]
    // 0xb0b7ac: b               #0xb0bc60
    // 0xb0b7b0: tbnz            w0, #4, #0xb0bc44
    // 0xb0b7b4: r0 = Radius()
    //     0xb0b7b4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb0b7b8: d0 = 8.000000
    //     0xb0b7b8: fmov            d0, #8.00000000
    // 0xb0b7bc: stur            x0, [fp, #-0x18]
    // 0xb0b7c0: StoreField: r0->field_7 = d0
    //     0xb0b7c0: stur            d0, [x0, #7]
    // 0xb0b7c4: StoreField: r0->field_f = d0
    //     0xb0b7c4: stur            d0, [x0, #0xf]
    // 0xb0b7c8: r0 = BorderRadius()
    //     0xb0b7c8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb0b7cc: mov             x1, x0
    // 0xb0b7d0: ldur            x0, [fp, #-0x18]
    // 0xb0b7d4: stur            x1, [fp, #-0x38]
    // 0xb0b7d8: StoreField: r1->field_7 = r0
    //     0xb0b7d8: stur            w0, [x1, #7]
    // 0xb0b7dc: StoreField: r1->field_b = r0
    //     0xb0b7dc: stur            w0, [x1, #0xb]
    // 0xb0b7e0: StoreField: r1->field_f = r0
    //     0xb0b7e0: stur            w0, [x1, #0xf]
    // 0xb0b7e4: StoreField: r1->field_13 = r0
    //     0xb0b7e4: stur            w0, [x1, #0x13]
    // 0xb0b7e8: ldur            x0, [fp, #-8]
    // 0xb0b7ec: LoadField: r2 = r0->field_b
    //     0xb0b7ec: ldur            w2, [x0, #0xb]
    // 0xb0b7f0: DecompressPointer r2
    //     0xb0b7f0: add             x2, x2, HEAP, lsl #32
    // 0xb0b7f4: cmp             w2, NULL
    // 0xb0b7f8: b.eq            #0xb0bf80
    // 0xb0b7fc: LoadField: r3 = r2->field_27
    //     0xb0b7fc: ldur            w3, [x2, #0x27]
    // 0xb0b800: DecompressPointer r3
    //     0xb0b800: add             x3, x3, HEAP, lsl #32
    // 0xb0b804: LoadField: r2 = r3->field_13
    //     0xb0b804: ldur            w2, [x3, #0x13]
    // 0xb0b808: DecompressPointer r2
    //     0xb0b808: add             x2, x2, HEAP, lsl #32
    // 0xb0b80c: stur            x2, [fp, #-0x18]
    // 0xb0b810: cmp             w2, NULL
    // 0xb0b814: b.ne            #0xb0b820
    // 0xb0b818: r3 = Null
    //     0xb0b818: mov             x3, NULL
    // 0xb0b81c: b               #0xb0b828
    // 0xb0b820: LoadField: r3 = r2->field_7
    //     0xb0b820: ldur            w3, [x2, #7]
    // 0xb0b824: DecompressPointer r3
    //     0xb0b824: add             x3, x3, HEAP, lsl #32
    // 0xb0b828: cmp             w3, NULL
    // 0xb0b82c: b.ne            #0xb0b838
    // 0xb0b830: r3 = 0
    //     0xb0b830: movz            x3, #0
    // 0xb0b834: b               #0xb0b848
    // 0xb0b838: r4 = LoadInt32Instr(r3)
    //     0xb0b838: sbfx            x4, x3, #1, #0x1f
    //     0xb0b83c: tbz             w3, #0, #0xb0b844
    //     0xb0b840: ldur            x4, [x3, #7]
    // 0xb0b844: mov             x3, x4
    // 0xb0b848: stur            x3, [fp, #-0x30]
    // 0xb0b84c: cmp             w2, NULL
    // 0xb0b850: b.ne            #0xb0b85c
    // 0xb0b854: r4 = Null
    //     0xb0b854: mov             x4, NULL
    // 0xb0b858: b               #0xb0b864
    // 0xb0b85c: LoadField: r4 = r2->field_b
    //     0xb0b85c: ldur            w4, [x2, #0xb]
    // 0xb0b860: DecompressPointer r4
    //     0xb0b860: add             x4, x4, HEAP, lsl #32
    // 0xb0b864: cmp             w4, NULL
    // 0xb0b868: b.ne            #0xb0b874
    // 0xb0b86c: r4 = 0
    //     0xb0b86c: movz            x4, #0
    // 0xb0b870: b               #0xb0b884
    // 0xb0b874: r5 = LoadInt32Instr(r4)
    //     0xb0b874: sbfx            x5, x4, #1, #0x1f
    //     0xb0b878: tbz             w4, #0, #0xb0b880
    //     0xb0b87c: ldur            x5, [x4, #7]
    // 0xb0b880: mov             x4, x5
    // 0xb0b884: stur            x4, [fp, #-0x28]
    // 0xb0b888: cmp             w2, NULL
    // 0xb0b88c: b.ne            #0xb0b898
    // 0xb0b890: r5 = Null
    //     0xb0b890: mov             x5, NULL
    // 0xb0b894: b               #0xb0b8a0
    // 0xb0b898: LoadField: r5 = r2->field_f
    //     0xb0b898: ldur            w5, [x2, #0xf]
    // 0xb0b89c: DecompressPointer r5
    //     0xb0b89c: add             x5, x5, HEAP, lsl #32
    // 0xb0b8a0: cmp             w5, NULL
    // 0xb0b8a4: b.ne            #0xb0b8b0
    // 0xb0b8a8: r5 = 0
    //     0xb0b8a8: movz            x5, #0
    // 0xb0b8ac: b               #0xb0b8c0
    // 0xb0b8b0: r6 = LoadInt32Instr(r5)
    //     0xb0b8b0: sbfx            x6, x5, #1, #0x1f
    //     0xb0b8b4: tbz             w5, #0, #0xb0b8bc
    //     0xb0b8b8: ldur            x6, [x5, #7]
    // 0xb0b8bc: mov             x5, x6
    // 0xb0b8c0: stur            x5, [fp, #-0x20]
    // 0xb0b8c4: r0 = Color()
    //     0xb0b8c4: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb0b8c8: mov             x1, x0
    // 0xb0b8cc: r0 = Instance_ColorSpace
    //     0xb0b8cc: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb0b8d0: stur            x1, [fp, #-0x40]
    // 0xb0b8d4: StoreField: r1->field_27 = r0
    //     0xb0b8d4: stur            w0, [x1, #0x27]
    // 0xb0b8d8: d0 = 1.000000
    //     0xb0b8d8: fmov            d0, #1.00000000
    // 0xb0b8dc: StoreField: r1->field_7 = d0
    //     0xb0b8dc: stur            d0, [x1, #7]
    // 0xb0b8e0: ldur            x2, [fp, #-0x30]
    // 0xb0b8e4: ubfx            x2, x2, #0, #0x20
    // 0xb0b8e8: and             w3, w2, #0xff
    // 0xb0b8ec: ubfx            x3, x3, #0, #0x20
    // 0xb0b8f0: scvtf           d0, x3
    // 0xb0b8f4: d1 = 255.000000
    //     0xb0b8f4: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb0b8f8: fdiv            d2, d0, d1
    // 0xb0b8fc: StoreField: r1->field_f = d2
    //     0xb0b8fc: stur            d2, [x1, #0xf]
    // 0xb0b900: ldur            x2, [fp, #-0x28]
    // 0xb0b904: ubfx            x2, x2, #0, #0x20
    // 0xb0b908: and             w3, w2, #0xff
    // 0xb0b90c: ubfx            x3, x3, #0, #0x20
    // 0xb0b910: scvtf           d0, x3
    // 0xb0b914: fdiv            d2, d0, d1
    // 0xb0b918: ArrayStore: r1[0] = d2  ; List_8
    //     0xb0b918: stur            d2, [x1, #0x17]
    // 0xb0b91c: ldur            x2, [fp, #-0x20]
    // 0xb0b920: ubfx            x2, x2, #0, #0x20
    // 0xb0b924: and             w3, w2, #0xff
    // 0xb0b928: ubfx            x3, x3, #0, #0x20
    // 0xb0b92c: scvtf           d0, x3
    // 0xb0b930: fdiv            d2, d0, d1
    // 0xb0b934: StoreField: r1->field_1f = d2
    //     0xb0b934: stur            d2, [x1, #0x1f]
    // 0xb0b938: ldur            x2, [fp, #-0x18]
    // 0xb0b93c: cmp             w2, NULL
    // 0xb0b940: b.ne            #0xb0b94c
    // 0xb0b944: r3 = Null
    //     0xb0b944: mov             x3, NULL
    // 0xb0b948: b               #0xb0b954
    // 0xb0b94c: LoadField: r3 = r2->field_7
    //     0xb0b94c: ldur            w3, [x2, #7]
    // 0xb0b950: DecompressPointer r3
    //     0xb0b950: add             x3, x3, HEAP, lsl #32
    // 0xb0b954: cmp             w3, NULL
    // 0xb0b958: b.ne            #0xb0b964
    // 0xb0b95c: r3 = 0
    //     0xb0b95c: movz            x3, #0
    // 0xb0b960: b               #0xb0b974
    // 0xb0b964: r4 = LoadInt32Instr(r3)
    //     0xb0b964: sbfx            x4, x3, #1, #0x1f
    //     0xb0b968: tbz             w3, #0, #0xb0b970
    //     0xb0b96c: ldur            x4, [x3, #7]
    // 0xb0b970: mov             x3, x4
    // 0xb0b974: stur            x3, [fp, #-0x30]
    // 0xb0b978: cmp             w2, NULL
    // 0xb0b97c: b.ne            #0xb0b988
    // 0xb0b980: r4 = Null
    //     0xb0b980: mov             x4, NULL
    // 0xb0b984: b               #0xb0b990
    // 0xb0b988: LoadField: r4 = r2->field_b
    //     0xb0b988: ldur            w4, [x2, #0xb]
    // 0xb0b98c: DecompressPointer r4
    //     0xb0b98c: add             x4, x4, HEAP, lsl #32
    // 0xb0b990: cmp             w4, NULL
    // 0xb0b994: b.ne            #0xb0b9a0
    // 0xb0b998: r4 = 0
    //     0xb0b998: movz            x4, #0
    // 0xb0b99c: b               #0xb0b9b0
    // 0xb0b9a0: r5 = LoadInt32Instr(r4)
    //     0xb0b9a0: sbfx            x5, x4, #1, #0x1f
    //     0xb0b9a4: tbz             w4, #0, #0xb0b9ac
    //     0xb0b9a8: ldur            x5, [x4, #7]
    // 0xb0b9ac: mov             x4, x5
    // 0xb0b9b0: stur            x4, [fp, #-0x28]
    // 0xb0b9b4: cmp             w2, NULL
    // 0xb0b9b8: b.ne            #0xb0b9c4
    // 0xb0b9bc: r2 = Null
    //     0xb0b9bc: mov             x2, NULL
    // 0xb0b9c0: b               #0xb0b9d0
    // 0xb0b9c4: LoadField: r5 = r2->field_f
    //     0xb0b9c4: ldur            w5, [x2, #0xf]
    // 0xb0b9c8: DecompressPointer r5
    //     0xb0b9c8: add             x5, x5, HEAP, lsl #32
    // 0xb0b9cc: mov             x2, x5
    // 0xb0b9d0: cmp             w2, NULL
    // 0xb0b9d4: b.ne            #0xb0b9e0
    // 0xb0b9d8: r6 = 0
    //     0xb0b9d8: movz            x6, #0
    // 0xb0b9dc: b               #0xb0b9f0
    // 0xb0b9e0: r5 = LoadInt32Instr(r2)
    //     0xb0b9e0: sbfx            x5, x2, #1, #0x1f
    //     0xb0b9e4: tbz             w2, #0, #0xb0b9ec
    //     0xb0b9e8: ldur            x5, [x2, #7]
    // 0xb0b9ec: mov             x6, x5
    // 0xb0b9f0: ldur            x5, [fp, #-0x10]
    // 0xb0b9f4: ldur            x2, [fp, #-0x38]
    // 0xb0b9f8: stur            x6, [fp, #-0x20]
    // 0xb0b9fc: r0 = Color()
    //     0xb0b9fc: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb0ba00: mov             x3, x0
    // 0xb0ba04: r0 = Instance_ColorSpace
    //     0xb0ba04: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb0ba08: stur            x3, [fp, #-0x18]
    // 0xb0ba0c: StoreField: r3->field_27 = r0
    //     0xb0ba0c: stur            w0, [x3, #0x27]
    // 0xb0ba10: d0 = 0.700000
    //     0xb0ba10: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb0ba14: ldr             d0, [x17, #0xf48]
    // 0xb0ba18: StoreField: r3->field_7 = d0
    //     0xb0ba18: stur            d0, [x3, #7]
    // 0xb0ba1c: ldur            x0, [fp, #-0x30]
    // 0xb0ba20: ubfx            x0, x0, #0, #0x20
    // 0xb0ba24: and             w1, w0, #0xff
    // 0xb0ba28: ubfx            x1, x1, #0, #0x20
    // 0xb0ba2c: scvtf           d0, x1
    // 0xb0ba30: d1 = 255.000000
    //     0xb0ba30: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb0ba34: fdiv            d2, d0, d1
    // 0xb0ba38: StoreField: r3->field_f = d2
    //     0xb0ba38: stur            d2, [x3, #0xf]
    // 0xb0ba3c: ldur            x0, [fp, #-0x28]
    // 0xb0ba40: ubfx            x0, x0, #0, #0x20
    // 0xb0ba44: and             w1, w0, #0xff
    // 0xb0ba48: ubfx            x1, x1, #0, #0x20
    // 0xb0ba4c: scvtf           d0, x1
    // 0xb0ba50: fdiv            d2, d0, d1
    // 0xb0ba54: ArrayStore: r3[0] = d2  ; List_8
    //     0xb0ba54: stur            d2, [x3, #0x17]
    // 0xb0ba58: ldur            x0, [fp, #-0x20]
    // 0xb0ba5c: ubfx            x0, x0, #0, #0x20
    // 0xb0ba60: and             w1, w0, #0xff
    // 0xb0ba64: ubfx            x1, x1, #0, #0x20
    // 0xb0ba68: scvtf           d0, x1
    // 0xb0ba6c: fdiv            d2, d0, d1
    // 0xb0ba70: StoreField: r3->field_1f = d2
    //     0xb0ba70: stur            d2, [x3, #0x1f]
    // 0xb0ba74: r1 = Null
    //     0xb0ba74: mov             x1, NULL
    // 0xb0ba78: r2 = 4
    //     0xb0ba78: movz            x2, #0x4
    // 0xb0ba7c: r0 = AllocateArray()
    //     0xb0ba7c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0ba80: mov             x2, x0
    // 0xb0ba84: ldur            x0, [fp, #-0x40]
    // 0xb0ba88: stur            x2, [fp, #-0x48]
    // 0xb0ba8c: StoreField: r2->field_f = r0
    //     0xb0ba8c: stur            w0, [x2, #0xf]
    // 0xb0ba90: ldur            x0, [fp, #-0x18]
    // 0xb0ba94: StoreField: r2->field_13 = r0
    //     0xb0ba94: stur            w0, [x2, #0x13]
    // 0xb0ba98: r1 = <Color>
    //     0xb0ba98: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb0ba9c: ldr             x1, [x1, #0xf80]
    // 0xb0baa0: r0 = AllocateGrowableArray()
    //     0xb0baa0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0baa4: mov             x1, x0
    // 0xb0baa8: ldur            x0, [fp, #-0x48]
    // 0xb0baac: stur            x1, [fp, #-0x18]
    // 0xb0bab0: StoreField: r1->field_f = r0
    //     0xb0bab0: stur            w0, [x1, #0xf]
    // 0xb0bab4: r2 = 4
    //     0xb0bab4: movz            x2, #0x4
    // 0xb0bab8: StoreField: r1->field_b = r2
    //     0xb0bab8: stur            w2, [x1, #0xb]
    // 0xb0babc: r0 = LinearGradient()
    //     0xb0babc: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xb0bac0: mov             x1, x0
    // 0xb0bac4: r0 = Instance_Alignment
    //     0xb0bac4: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb0bac8: ldr             x0, [x0, #0xce0]
    // 0xb0bacc: stur            x1, [fp, #-0x40]
    // 0xb0bad0: StoreField: r1->field_13 = r0
    //     0xb0bad0: stur            w0, [x1, #0x13]
    // 0xb0bad4: r0 = Instance_Alignment
    //     0xb0bad4: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xb0bad8: ldr             x0, [x0, #0xce8]
    // 0xb0badc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0badc: stur            w0, [x1, #0x17]
    // 0xb0bae0: r0 = Instance_TileMode
    //     0xb0bae0: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xb0bae4: ldr             x0, [x0, #0xcf0]
    // 0xb0bae8: StoreField: r1->field_1b = r0
    //     0xb0bae8: stur            w0, [x1, #0x1b]
    // 0xb0baec: ldur            x0, [fp, #-0x18]
    // 0xb0baf0: StoreField: r1->field_7 = r0
    //     0xb0baf0: stur            w0, [x1, #7]
    // 0xb0baf4: r0 = BoxDecoration()
    //     0xb0baf4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb0baf8: mov             x2, x0
    // 0xb0bafc: ldur            x0, [fp, #-0x38]
    // 0xb0bb00: stur            x2, [fp, #-0x48]
    // 0xb0bb04: StoreField: r2->field_13 = r0
    //     0xb0bb04: stur            w0, [x2, #0x13]
    // 0xb0bb08: ldur            x0, [fp, #-0x40]
    // 0xb0bb0c: StoreField: r2->field_1b = r0
    //     0xb0bb0c: stur            w0, [x2, #0x1b]
    // 0xb0bb10: r0 = Instance_BoxShape
    //     0xb0bb10: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb0bb14: ldr             x0, [x0, #0x80]
    // 0xb0bb18: StoreField: r2->field_23 = r0
    //     0xb0bb18: stur            w0, [x2, #0x23]
    // 0xb0bb1c: ldur            x3, [fp, #-0x10]
    // 0xb0bb20: r17 = 295
    //     0xb0bb20: movz            x17, #0x127
    // 0xb0bb24: ldr             w0, [x3, x17]
    // 0xb0bb28: DecompressPointer r0
    //     0xb0bb28: add             x0, x0, HEAP, lsl #32
    // 0xb0bb2c: cmp             w0, NULL
    // 0xb0bb30: b.ne            #0xb0bb38
    // 0xb0bb34: r0 = ""
    //     0xb0bb34: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0bb38: ldur            x4, [fp, #-8]
    // 0xb0bb3c: stur            x0, [fp, #-0x18]
    // 0xb0bb40: LoadField: r1 = r4->field_f
    //     0xb0bb40: ldur            w1, [x4, #0xf]
    // 0xb0bb44: DecompressPointer r1
    //     0xb0bb44: add             x1, x1, HEAP, lsl #32
    // 0xb0bb48: cmp             w1, NULL
    // 0xb0bb4c: b.eq            #0xb0bf84
    // 0xb0bb50: r0 = of()
    //     0xb0bb50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0bb54: LoadField: r1 = r0->field_87
    //     0xb0bb54: ldur            w1, [x0, #0x87]
    // 0xb0bb58: DecompressPointer r1
    //     0xb0bb58: add             x1, x1, HEAP, lsl #32
    // 0xb0bb5c: LoadField: r0 = r1->field_7
    //     0xb0bb5c: ldur            w0, [x1, #7]
    // 0xb0bb60: DecompressPointer r0
    //     0xb0bb60: add             x0, x0, HEAP, lsl #32
    // 0xb0bb64: r16 = 12.000000
    //     0xb0bb64: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb0bb68: ldr             x16, [x16, #0x9e8]
    // 0xb0bb6c: r30 = Instance_Color
    //     0xb0bb6c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb0bb70: stp             lr, x16, [SP]
    // 0xb0bb74: mov             x1, x0
    // 0xb0bb78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0bb78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0bb7c: ldr             x4, [x4, #0xaa0]
    // 0xb0bb80: r0 = copyWith()
    //     0xb0bb80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0bb84: stur            x0, [fp, #-0x38]
    // 0xb0bb88: r0 = Text()
    //     0xb0bb88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0bb8c: mov             x1, x0
    // 0xb0bb90: ldur            x0, [fp, #-0x18]
    // 0xb0bb94: stur            x1, [fp, #-0x40]
    // 0xb0bb98: StoreField: r1->field_b = r0
    //     0xb0bb98: stur            w0, [x1, #0xb]
    // 0xb0bb9c: ldur            x0, [fp, #-0x38]
    // 0xb0bba0: StoreField: r1->field_13 = r0
    //     0xb0bba0: stur            w0, [x1, #0x13]
    // 0xb0bba4: r0 = Instance_TextAlign
    //     0xb0bba4: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb0bba8: StoreField: r1->field_1b = r0
    //     0xb0bba8: stur            w0, [x1, #0x1b]
    // 0xb0bbac: r0 = Padding()
    //     0xb0bbac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0bbb0: mov             x1, x0
    // 0xb0bbb4: r0 = Instance_EdgeInsets
    //     0xb0bbb4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27850] Obj!EdgeInsets@d57a71
    //     0xb0bbb8: ldr             x0, [x0, #0x850]
    // 0xb0bbbc: stur            x1, [fp, #-0x18]
    // 0xb0bbc0: StoreField: r1->field_f = r0
    //     0xb0bbc0: stur            w0, [x1, #0xf]
    // 0xb0bbc4: ldur            x0, [fp, #-0x40]
    // 0xb0bbc8: StoreField: r1->field_b = r0
    //     0xb0bbc8: stur            w0, [x1, #0xb]
    // 0xb0bbcc: r0 = Container()
    //     0xb0bbcc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb0bbd0: stur            x0, [fp, #-0x38]
    // 0xb0bbd4: r16 = 20.000000
    //     0xb0bbd4: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb0bbd8: ldr             x16, [x16, #0xac8]
    // 0xb0bbdc: r30 = 120.000000
    //     0xb0bbdc: add             lr, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0xb0bbe0: ldr             lr, [lr, #0x3a0]
    // 0xb0bbe4: stp             lr, x16, [SP, #0x10]
    // 0xb0bbe8: ldur            x16, [fp, #-0x48]
    // 0xb0bbec: ldur            lr, [fp, #-0x18]
    // 0xb0bbf0: stp             lr, x16, [SP]
    // 0xb0bbf4: mov             x1, x0
    // 0xb0bbf8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb0bbf8: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb0bbfc: ldr             x4, [x4, #0x8c0]
    // 0xb0bc00: r0 = Container()
    //     0xb0bc00: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb0bc04: r0 = Align()
    //     0xb0bc04: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb0bc08: r5 = Instance_Alignment
    //     0xb0bc08: add             x5, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb0bc0c: ldr             x5, [x5, #0xfa0]
    // 0xb0bc10: stur            x0, [fp, #-0x18]
    // 0xb0bc14: StoreField: r0->field_f = r5
    //     0xb0bc14: stur            w5, [x0, #0xf]
    // 0xb0bc18: ldur            x1, [fp, #-0x38]
    // 0xb0bc1c: StoreField: r0->field_b = r1
    //     0xb0bc1c: stur            w1, [x0, #0xb]
    // 0xb0bc20: r0 = Padding()
    //     0xb0bc20: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0bc24: mov             x1, x0
    // 0xb0bc28: r0 = Instance_EdgeInsets
    //     0xb0bc28: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xb0bc2c: ldr             x0, [x0, #0xe50]
    // 0xb0bc30: StoreField: r1->field_f = r0
    //     0xb0bc30: stur            w0, [x1, #0xf]
    // 0xb0bc34: ldur            x0, [fp, #-0x18]
    // 0xb0bc38: StoreField: r1->field_b = r0
    //     0xb0bc38: stur            w0, [x1, #0xb]
    // 0xb0bc3c: mov             x0, x1
    // 0xb0bc40: b               #0xb0bf6c
    // 0xb0bc44: mov             x4, x1
    // 0xb0bc48: mov             x3, x2
    // 0xb0bc4c: r5 = Instance_Alignment
    //     0xb0bc4c: add             x5, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb0bc50: ldr             x5, [x5, #0xfa0]
    // 0xb0bc54: r2 = 4
    //     0xb0bc54: movz            x2, #0x4
    // 0xb0bc58: r0 = Instance_BoxShape
    //     0xb0bc58: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb0bc5c: ldr             x0, [x0, #0x80]
    // 0xb0bc60: LoadField: r1 = r4->field_f
    //     0xb0bc60: ldur            w1, [x4, #0xf]
    // 0xb0bc64: DecompressPointer r1
    //     0xb0bc64: add             x1, x1, HEAP, lsl #32
    // 0xb0bc68: cmp             w1, NULL
    // 0xb0bc6c: b.eq            #0xb0bf88
    // 0xb0bc70: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0bc70: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0bc74: r0 = _of()
    //     0xb0bc74: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb0bc78: LoadField: r1 = r0->field_7
    //     0xb0bc78: ldur            w1, [x0, #7]
    // 0xb0bc7c: DecompressPointer r1
    //     0xb0bc7c: add             x1, x1, HEAP, lsl #32
    // 0xb0bc80: LoadField: d0 = r1->field_7
    //     0xb0bc80: ldur            d0, [x1, #7]
    // 0xb0bc84: d1 = 0.370000
    //     0xb0bc84: add             x17, PP, #0x52, lsl #12  ; [pp+0x52e40] IMM: double(0.37) from 0x3fd7ae147ae147ae
    //     0xb0bc88: ldr             d1, [x17, #0xe40]
    // 0xb0bc8c: fmul            d2, d0, d1
    // 0xb0bc90: stur            d2, [fp, #-0x50]
    // 0xb0bc94: r1 = Instance_Color
    //     0xb0bc94: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb0bc98: d0 = 0.900000
    //     0xb0bc98: ldr             d0, [PP, #0x5a78]  ; [pp+0x5a78] IMM: double(0.9) from 0x3feccccccccccccd
    // 0xb0bc9c: r0 = withOpacity()
    //     0xb0bc9c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb0bca0: stur            x0, [fp, #-0x18]
    // 0xb0bca4: r0 = Radius()
    //     0xb0bca4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb0bca8: d0 = 4.000000
    //     0xb0bca8: fmov            d0, #4.00000000
    // 0xb0bcac: stur            x0, [fp, #-0x38]
    // 0xb0bcb0: StoreField: r0->field_7 = d0
    //     0xb0bcb0: stur            d0, [x0, #7]
    // 0xb0bcb4: StoreField: r0->field_f = d0
    //     0xb0bcb4: stur            d0, [x0, #0xf]
    // 0xb0bcb8: r0 = BorderRadius()
    //     0xb0bcb8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb0bcbc: mov             x1, x0
    // 0xb0bcc0: ldur            x0, [fp, #-0x38]
    // 0xb0bcc4: stur            x1, [fp, #-0x40]
    // 0xb0bcc8: StoreField: r1->field_7 = r0
    //     0xb0bcc8: stur            w0, [x1, #7]
    // 0xb0bccc: StoreField: r1->field_b = r0
    //     0xb0bccc: stur            w0, [x1, #0xb]
    // 0xb0bcd0: StoreField: r1->field_f = r0
    //     0xb0bcd0: stur            w0, [x1, #0xf]
    // 0xb0bcd4: StoreField: r1->field_13 = r0
    //     0xb0bcd4: stur            w0, [x1, #0x13]
    // 0xb0bcd8: r0 = BoxDecoration()
    //     0xb0bcd8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb0bcdc: mov             x2, x0
    // 0xb0bce0: ldur            x0, [fp, #-0x18]
    // 0xb0bce4: stur            x2, [fp, #-0x38]
    // 0xb0bce8: StoreField: r2->field_7 = r0
    //     0xb0bce8: stur            w0, [x2, #7]
    // 0xb0bcec: ldur            x0, [fp, #-0x40]
    // 0xb0bcf0: StoreField: r2->field_13 = r0
    //     0xb0bcf0: stur            w0, [x2, #0x13]
    // 0xb0bcf4: r0 = Instance_BoxShape
    //     0xb0bcf4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb0bcf8: ldr             x0, [x0, #0x80]
    // 0xb0bcfc: StoreField: r2->field_23 = r0
    //     0xb0bcfc: stur            w0, [x2, #0x23]
    // 0xb0bd00: ldur            x0, [fp, #-8]
    // 0xb0bd04: LoadField: r1 = r0->field_f
    //     0xb0bd04: ldur            w1, [x0, #0xf]
    // 0xb0bd08: DecompressPointer r1
    //     0xb0bd08: add             x1, x1, HEAP, lsl #32
    // 0xb0bd0c: cmp             w1, NULL
    // 0xb0bd10: b.eq            #0xb0bf8c
    // 0xb0bd14: r0 = of()
    //     0xb0bd14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0bd18: LoadField: r1 = r0->field_5b
    //     0xb0bd18: ldur            w1, [x0, #0x5b]
    // 0xb0bd1c: DecompressPointer r1
    //     0xb0bd1c: add             x1, x1, HEAP, lsl #32
    // 0xb0bd20: stur            x1, [fp, #-0x18]
    // 0xb0bd24: r0 = ColorFilter()
    //     0xb0bd24: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb0bd28: mov             x1, x0
    // 0xb0bd2c: ldur            x0, [fp, #-0x18]
    // 0xb0bd30: stur            x1, [fp, #-0x40]
    // 0xb0bd34: StoreField: r1->field_7 = r0
    //     0xb0bd34: stur            w0, [x1, #7]
    // 0xb0bd38: r0 = Instance_BlendMode
    //     0xb0bd38: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb0bd3c: ldr             x0, [x0, #0xb30]
    // 0xb0bd40: StoreField: r1->field_b = r0
    //     0xb0bd40: stur            w0, [x1, #0xb]
    // 0xb0bd44: r0 = 1
    //     0xb0bd44: movz            x0, #0x1
    // 0xb0bd48: StoreField: r1->field_13 = r0
    //     0xb0bd48: stur            x0, [x1, #0x13]
    // 0xb0bd4c: r0 = SvgPicture()
    //     0xb0bd4c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb0bd50: stur            x0, [fp, #-0x18]
    // 0xb0bd54: ldur            x16, [fp, #-0x40]
    // 0xb0bd58: str             x16, [SP]
    // 0xb0bd5c: mov             x1, x0
    // 0xb0bd60: r2 = "assets/images/bumper_coupon.svg"
    //     0xb0bd60: add             x2, PP, #0x52, lsl #12  ; [pp+0x52e48] "assets/images/bumper_coupon.svg"
    //     0xb0bd64: ldr             x2, [x2, #0xe48]
    // 0xb0bd68: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb0bd68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb0bd6c: ldr             x4, [x4, #0xa38]
    // 0xb0bd70: r0 = SvgPicture.asset()
    //     0xb0bd70: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb0bd74: ldur            x0, [fp, #-0x10]
    // 0xb0bd78: r17 = 295
    //     0xb0bd78: movz            x17, #0x127
    // 0xb0bd7c: ldr             w1, [x0, x17]
    // 0xb0bd80: DecompressPointer r1
    //     0xb0bd80: add             x1, x1, HEAP, lsl #32
    // 0xb0bd84: cmp             w1, NULL
    // 0xb0bd88: b.ne            #0xb0bd94
    // 0xb0bd8c: r2 = ""
    //     0xb0bd8c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0bd90: b               #0xb0bd98
    // 0xb0bd94: mov             x2, x1
    // 0xb0bd98: ldur            x1, [fp, #-8]
    // 0xb0bd9c: ldur            d0, [fp, #-0x50]
    // 0xb0bda0: ldur            x0, [fp, #-0x18]
    // 0xb0bda4: stur            x2, [fp, #-0x10]
    // 0xb0bda8: LoadField: r3 = r1->field_f
    //     0xb0bda8: ldur            w3, [x1, #0xf]
    // 0xb0bdac: DecompressPointer r3
    //     0xb0bdac: add             x3, x3, HEAP, lsl #32
    // 0xb0bdb0: cmp             w3, NULL
    // 0xb0bdb4: b.eq            #0xb0bf90
    // 0xb0bdb8: mov             x1, x3
    // 0xb0bdbc: r0 = of()
    //     0xb0bdbc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0bdc0: LoadField: r1 = r0->field_87
    //     0xb0bdc0: ldur            w1, [x0, #0x87]
    // 0xb0bdc4: DecompressPointer r1
    //     0xb0bdc4: add             x1, x1, HEAP, lsl #32
    // 0xb0bdc8: LoadField: r0 = r1->field_2b
    //     0xb0bdc8: ldur            w0, [x1, #0x2b]
    // 0xb0bdcc: DecompressPointer r0
    //     0xb0bdcc: add             x0, x0, HEAP, lsl #32
    // 0xb0bdd0: r16 = 12.000000
    //     0xb0bdd0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb0bdd4: ldr             x16, [x16, #0x9e8]
    // 0xb0bdd8: r30 = Instance_Color
    //     0xb0bdd8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb0bddc: stp             lr, x16, [SP]
    // 0xb0bde0: mov             x1, x0
    // 0xb0bde4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0bde4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0bde8: ldr             x4, [x4, #0xaa0]
    // 0xb0bdec: r0 = copyWith()
    //     0xb0bdec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0bdf0: stur            x0, [fp, #-8]
    // 0xb0bdf4: r0 = Text()
    //     0xb0bdf4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0bdf8: mov             x1, x0
    // 0xb0bdfc: ldur            x0, [fp, #-0x10]
    // 0xb0be00: stur            x1, [fp, #-0x40]
    // 0xb0be04: StoreField: r1->field_b = r0
    //     0xb0be04: stur            w0, [x1, #0xb]
    // 0xb0be08: ldur            x0, [fp, #-8]
    // 0xb0be0c: StoreField: r1->field_13 = r0
    //     0xb0be0c: stur            w0, [x1, #0x13]
    // 0xb0be10: r0 = Padding()
    //     0xb0be10: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0be14: mov             x3, x0
    // 0xb0be18: r0 = Instance_EdgeInsets
    //     0xb0be18: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xb0be1c: ldr             x0, [x0, #0xc40]
    // 0xb0be20: stur            x3, [fp, #-8]
    // 0xb0be24: StoreField: r3->field_f = r0
    //     0xb0be24: stur            w0, [x3, #0xf]
    // 0xb0be28: ldur            x0, [fp, #-0x40]
    // 0xb0be2c: StoreField: r3->field_b = r0
    //     0xb0be2c: stur            w0, [x3, #0xb]
    // 0xb0be30: r1 = Null
    //     0xb0be30: mov             x1, NULL
    // 0xb0be34: r2 = 4
    //     0xb0be34: movz            x2, #0x4
    // 0xb0be38: r0 = AllocateArray()
    //     0xb0be38: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0be3c: mov             x2, x0
    // 0xb0be40: ldur            x0, [fp, #-0x18]
    // 0xb0be44: stur            x2, [fp, #-0x10]
    // 0xb0be48: StoreField: r2->field_f = r0
    //     0xb0be48: stur            w0, [x2, #0xf]
    // 0xb0be4c: ldur            x0, [fp, #-8]
    // 0xb0be50: StoreField: r2->field_13 = r0
    //     0xb0be50: stur            w0, [x2, #0x13]
    // 0xb0be54: r1 = <Widget>
    //     0xb0be54: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0be58: r0 = AllocateGrowableArray()
    //     0xb0be58: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0be5c: mov             x1, x0
    // 0xb0be60: ldur            x0, [fp, #-0x10]
    // 0xb0be64: stur            x1, [fp, #-8]
    // 0xb0be68: StoreField: r1->field_f = r0
    //     0xb0be68: stur            w0, [x1, #0xf]
    // 0xb0be6c: r0 = 4
    //     0xb0be6c: movz            x0, #0x4
    // 0xb0be70: StoreField: r1->field_b = r0
    //     0xb0be70: stur            w0, [x1, #0xb]
    // 0xb0be74: r0 = Row()
    //     0xb0be74: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb0be78: mov             x1, x0
    // 0xb0be7c: r0 = Instance_Axis
    //     0xb0be7c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb0be80: stur            x1, [fp, #-0x10]
    // 0xb0be84: StoreField: r1->field_f = r0
    //     0xb0be84: stur            w0, [x1, #0xf]
    // 0xb0be88: r0 = Instance_MainAxisAlignment
    //     0xb0be88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb0be8c: ldr             x0, [x0, #0xa08]
    // 0xb0be90: StoreField: r1->field_13 = r0
    //     0xb0be90: stur            w0, [x1, #0x13]
    // 0xb0be94: r0 = Instance_MainAxisSize
    //     0xb0be94: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb0be98: ldr             x0, [x0, #0xa10]
    // 0xb0be9c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0be9c: stur            w0, [x1, #0x17]
    // 0xb0bea0: r0 = Instance_CrossAxisAlignment
    //     0xb0bea0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb0bea4: ldr             x0, [x0, #0xa18]
    // 0xb0bea8: StoreField: r1->field_1b = r0
    //     0xb0bea8: stur            w0, [x1, #0x1b]
    // 0xb0beac: r0 = Instance_VerticalDirection
    //     0xb0beac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb0beb0: ldr             x0, [x0, #0xa20]
    // 0xb0beb4: StoreField: r1->field_23 = r0
    //     0xb0beb4: stur            w0, [x1, #0x23]
    // 0xb0beb8: r0 = Instance_Clip
    //     0xb0beb8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb0bebc: ldr             x0, [x0, #0x38]
    // 0xb0bec0: StoreField: r1->field_2b = r0
    //     0xb0bec0: stur            w0, [x1, #0x2b]
    // 0xb0bec4: StoreField: r1->field_2f = rZR
    //     0xb0bec4: stur            xzr, [x1, #0x2f]
    // 0xb0bec8: ldur            x0, [fp, #-8]
    // 0xb0becc: StoreField: r1->field_b = r0
    //     0xb0becc: stur            w0, [x1, #0xb]
    // 0xb0bed0: ldur            d0, [fp, #-0x50]
    // 0xb0bed4: r0 = inline_Allocate_Double()
    //     0xb0bed4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb0bed8: add             x0, x0, #0x10
    //     0xb0bedc: cmp             x2, x0
    //     0xb0bee0: b.ls            #0xb0bf94
    //     0xb0bee4: str             x0, [THR, #0x50]  ; THR::top
    //     0xb0bee8: sub             x0, x0, #0xf
    //     0xb0beec: movz            x2, #0xe15c
    //     0xb0bef0: movk            x2, #0x3, lsl #16
    //     0xb0bef4: stur            x2, [x0, #-1]
    // 0xb0bef8: StoreField: r0->field_7 = d0
    //     0xb0bef8: stur            d0, [x0, #7]
    // 0xb0befc: stur            x0, [fp, #-8]
    // 0xb0bf00: r0 = Container()
    //     0xb0bf00: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb0bf04: stur            x0, [fp, #-0x18]
    // 0xb0bf08: r16 = 20.000000
    //     0xb0bf08: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb0bf0c: ldr             x16, [x16, #0xac8]
    // 0xb0bf10: ldur            lr, [fp, #-8]
    // 0xb0bf14: stp             lr, x16, [SP, #0x10]
    // 0xb0bf18: ldur            x16, [fp, #-0x38]
    // 0xb0bf1c: ldur            lr, [fp, #-0x10]
    // 0xb0bf20: stp             lr, x16, [SP]
    // 0xb0bf24: mov             x1, x0
    // 0xb0bf28: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb0bf28: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb0bf2c: ldr             x4, [x4, #0x8c0]
    // 0xb0bf30: r0 = Container()
    //     0xb0bf30: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb0bf34: r0 = Padding()
    //     0xb0bf34: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0bf38: mov             x1, x0
    // 0xb0bf3c: r0 = Instance_EdgeInsets
    //     0xb0bf3c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xb0bf40: ldr             x0, [x0, #0xf70]
    // 0xb0bf44: stur            x1, [fp, #-8]
    // 0xb0bf48: StoreField: r1->field_f = r0
    //     0xb0bf48: stur            w0, [x1, #0xf]
    // 0xb0bf4c: ldur            x0, [fp, #-0x18]
    // 0xb0bf50: StoreField: r1->field_b = r0
    //     0xb0bf50: stur            w0, [x1, #0xb]
    // 0xb0bf54: r0 = Align()
    //     0xb0bf54: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb0bf58: r1 = Instance_Alignment
    //     0xb0bf58: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb0bf5c: ldr             x1, [x1, #0xfa0]
    // 0xb0bf60: StoreField: r0->field_f = r1
    //     0xb0bf60: stur            w1, [x0, #0xf]
    // 0xb0bf64: ldur            x1, [fp, #-8]
    // 0xb0bf68: StoreField: r0->field_b = r1
    //     0xb0bf68: stur            w1, [x0, #0xb]
    // 0xb0bf6c: LeaveFrame
    //     0xb0bf6c: mov             SP, fp
    //     0xb0bf70: ldp             fp, lr, [SP], #0x10
    // 0xb0bf74: ret
    //     0xb0bf74: ret             
    // 0xb0bf78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0bf78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0bf7c: b               #0xb0b77c
    // 0xb0bf80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0bf80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0bf84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0bf84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0bf88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0bf88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0bf8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0bf8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0bf90: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb0bf90: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb0bf94: SaveReg d0
    //     0xb0bf94: str             q0, [SP, #-0x10]!
    // 0xb0bf98: SaveReg r1
    //     0xb0bf98: str             x1, [SP, #-8]!
    // 0xb0bf9c: r0 = AllocateDouble()
    //     0xb0bf9c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb0bfa0: RestoreReg r1
    //     0xb0bfa0: ldr             x1, [SP], #8
    // 0xb0bfa4: RestoreReg d0
    //     0xb0bfa4: ldr             q0, [SP], #0x10
    // 0xb0bfa8: b               #0xb0bef8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb0bfac, size: 0x130
    // 0xb0bfac: EnterFrame
    //     0xb0bfac: stp             fp, lr, [SP, #-0x10]!
    //     0xb0bfb0: mov             fp, SP
    // 0xb0bfb4: AllocStack(0x40)
    //     0xb0bfb4: sub             SP, SP, #0x40
    // 0xb0bfb8: SetupParameters()
    //     0xb0bfb8: ldr             x0, [fp, #0x10]
    //     0xb0bfbc: ldur            w1, [x0, #0x17]
    //     0xb0bfc0: add             x1, x1, HEAP, lsl #32
    // 0xb0bfc4: CheckStackOverflow
    //     0xb0bfc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0bfc8: cmp             SP, x16
    //     0xb0bfcc: b.ls            #0xb0c0d0
    // 0xb0bfd0: LoadField: r0 = r1->field_f
    //     0xb0bfd0: ldur            w0, [x1, #0xf]
    // 0xb0bfd4: DecompressPointer r0
    //     0xb0bfd4: add             x0, x0, HEAP, lsl #32
    // 0xb0bfd8: LoadField: r2 = r0->field_b
    //     0xb0bfd8: ldur            w2, [x0, #0xb]
    // 0xb0bfdc: DecompressPointer r2
    //     0xb0bfdc: add             x2, x2, HEAP, lsl #32
    // 0xb0bfe0: cmp             w2, NULL
    // 0xb0bfe4: b.eq            #0xb0c0d8
    // 0xb0bfe8: LoadField: r0 = r2->field_1b
    //     0xb0bfe8: ldur            w0, [x2, #0x1b]
    // 0xb0bfec: DecompressPointer r0
    //     0xb0bfec: add             x0, x0, HEAP, lsl #32
    // 0xb0bff0: cmp             w0, NULL
    // 0xb0bff4: b.ne            #0xb0bffc
    // 0xb0bff8: r0 = ""
    //     0xb0bff8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0bffc: LoadField: r3 = r2->field_13
    //     0xb0bffc: ldur            w3, [x2, #0x13]
    // 0xb0c000: DecompressPointer r3
    //     0xb0c000: add             x3, x3, HEAP, lsl #32
    // 0xb0c004: cmp             w3, NULL
    // 0xb0c008: b.ne            #0xb0c010
    // 0xb0c00c: r3 = ""
    //     0xb0c00c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0c010: LoadField: r4 = r2->field_1f
    //     0xb0c010: ldur            w4, [x2, #0x1f]
    // 0xb0c014: DecompressPointer r4
    //     0xb0c014: add             x4, x4, HEAP, lsl #32
    // 0xb0c018: cmp             w4, NULL
    // 0xb0c01c: b.ne            #0xb0c024
    // 0xb0c020: r4 = ""
    //     0xb0c020: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0c024: ArrayLoad: r5 = r2[0]  ; List_4
    //     0xb0c024: ldur            w5, [x2, #0x17]
    // 0xb0c028: DecompressPointer r5
    //     0xb0c028: add             x5, x5, HEAP, lsl #32
    // 0xb0c02c: cmp             w5, NULL
    // 0xb0c030: b.ne            #0xb0c038
    // 0xb0c034: r5 = ""
    //     0xb0c034: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0c038: LoadField: r6 = r1->field_13
    //     0xb0c038: ldur            w6, [x1, #0x13]
    // 0xb0c03c: DecompressPointer r6
    //     0xb0c03c: add             x6, x6, HEAP, lsl #32
    // 0xb0c040: cmp             w6, NULL
    // 0xb0c044: b.ne            #0xb0c050
    // 0xb0c048: r1 = Null
    //     0xb0c048: mov             x1, NULL
    // 0xb0c04c: b               #0xb0c058
    // 0xb0c050: LoadField: r1 = r6->field_47
    //     0xb0c050: ldur            w1, [x6, #0x47]
    // 0xb0c054: DecompressPointer r1
    //     0xb0c054: add             x1, x1, HEAP, lsl #32
    // 0xb0c058: cmp             w6, NULL
    // 0xb0c05c: b.ne            #0xb0c068
    // 0xb0c060: r6 = Null
    //     0xb0c060: mov             x6, NULL
    // 0xb0c064: b               #0xb0c088
    // 0xb0c068: LoadField: r7 = r6->field_eb
    //     0xb0c068: ldur            w7, [x6, #0xeb]
    // 0xb0c06c: DecompressPointer r7
    //     0xb0c06c: add             x7, x7, HEAP, lsl #32
    // 0xb0c070: cmp             w7, NULL
    // 0xb0c074: b.ne            #0xb0c080
    // 0xb0c078: r6 = Null
    //     0xb0c078: mov             x6, NULL
    // 0xb0c07c: b               #0xb0c088
    // 0xb0c080: LoadField: r6 = r7->field_23
    //     0xb0c080: ldur            w6, [x7, #0x23]
    // 0xb0c084: DecompressPointer r6
    //     0xb0c084: add             x6, x6, HEAP, lsl #32
    // 0xb0c088: LoadField: r7 = r2->field_2f
    //     0xb0c088: ldur            w7, [x2, #0x2f]
    // 0xb0c08c: DecompressPointer r7
    //     0xb0c08c: add             x7, x7, HEAP, lsl #32
    // 0xb0c090: stp             x0, x7, [SP, #0x30]
    // 0xb0c094: r16 = "product_page"
    //     0xb0c094: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xb0c098: ldr             x16, [x16, #0x480]
    // 0xb0c09c: stp             x16, x3, [SP, #0x20]
    // 0xb0c0a0: stp             x5, x4, [SP, #0x10]
    // 0xb0c0a4: stp             x6, x1, [SP]
    // 0xb0c0a8: r4 = 0
    //     0xb0c0a8: movz            x4, #0
    // 0xb0c0ac: ldr             x0, [SP, #0x38]
    // 0xb0c0b0: r16 = UnlinkedCall_0x613b5c
    //     0xb0c0b0: add             x16, PP, #0x61, lsl #12  ; [pp+0x61ef0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb0c0b4: add             x16, x16, #0xef0
    // 0xb0c0b8: ldp             x5, lr, [x16]
    // 0xb0c0bc: blr             lr
    // 0xb0c0c0: r0 = Null
    //     0xb0c0c0: mov             x0, NULL
    // 0xb0c0c4: LeaveFrame
    //     0xb0c0c4: mov             SP, fp
    //     0xb0c0c8: ldp             fp, lr, [SP], #0x10
    // 0xb0c0cc: ret
    //     0xb0c0cc: ret             
    // 0xb0c0d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0c0d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0c0d4: b               #0xb0bfd0
    // 0xb0c0d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0c0d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4141, size: 0x3c, field offset: 0xc
//   const constructor, 
class _ProductGridItem extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7dfb0, size: 0x24
    // 0xc7dfb0: EnterFrame
    //     0xc7dfb0: stp             fp, lr, [SP, #-0x10]!
    //     0xc7dfb4: mov             fp, SP
    // 0xc7dfb8: mov             x0, x1
    // 0xc7dfbc: r1 = <_ProductGridItem>
    //     0xc7dfbc: add             x1, PP, #0x57, lsl #12  ; [pp+0x57d48] TypeArguments: <_ProductGridItem>
    //     0xc7dfc0: ldr             x1, [x1, #0xd48]
    // 0xc7dfc4: r0 = _ProductGridItemState()
    //     0xc7dfc4: bl              #0xc7dfd4  ; Allocate_ProductGridItemStateStub -> _ProductGridItemState (size=0x14)
    // 0xc7dfc8: LeaveFrame
    //     0xc7dfc8: mov             SP, fp
    //     0xc7dfcc: ldp             fp, lr, [SP], #0x10
    // 0xc7dfd0: ret
    //     0xc7dfd0: ret             
  }
}

// class id: 4497, size: 0x4c, field offset: 0xc
//   const constructor, 
class ProductDetailGridItemView extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0x128f690, size: 0xf64
    // 0x128f690: EnterFrame
    //     0x128f690: stp             fp, lr, [SP, #-0x10]!
    //     0x128f694: mov             fp, SP
    // 0x128f698: AllocStack(0xb0)
    //     0x128f698: sub             SP, SP, #0xb0
    // 0x128f69c: SetupParameters(ProductDetailGridItemView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x128f69c: mov             x0, x1
    //     0x128f6a0: stur            x1, [fp, #-8]
    //     0x128f6a4: mov             x1, x2
    //     0x128f6a8: stur            x2, [fp, #-0x10]
    // 0x128f6ac: CheckStackOverflow
    //     0x128f6ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128f6b0: cmp             SP, x16
    //     0x128f6b4: b.ls            #0x12905ec
    // 0x128f6b8: r1 = 1
    //     0x128f6b8: movz            x1, #0x1
    // 0x128f6bc: r0 = AllocateContext()
    //     0x128f6bc: bl              #0x16f6108  ; AllocateContextStub
    // 0x128f6c0: mov             x3, x0
    // 0x128f6c4: ldur            x0, [fp, #-8]
    // 0x128f6c8: stur            x3, [fp, #-0x28]
    // 0x128f6cc: StoreField: r3->field_f = r0
    //     0x128f6cc: stur            w0, [x3, #0xf]
    // 0x128f6d0: LoadField: r4 = r0->field_1f
    //     0x128f6d0: ldur            w4, [x0, #0x1f]
    // 0x128f6d4: DecompressPointer r4
    //     0x128f6d4: add             x4, x4, HEAP, lsl #32
    // 0x128f6d8: stur            x4, [fp, #-0x20]
    // 0x128f6dc: LoadField: r1 = r4->field_7
    //     0x128f6dc: ldur            w1, [x4, #7]
    // 0x128f6e0: DecompressPointer r1
    //     0x128f6e0: add             x1, x1, HEAP, lsl #32
    // 0x128f6e4: cmp             w1, NULL
    // 0x128f6e8: b.ne            #0x128f6f4
    // 0x128f6ec: r1 = Instance_TitleAlignment
    //     0x128f6ec: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x128f6f0: ldr             x1, [x1, #0x518]
    // 0x128f6f4: r16 = Instance_TitleAlignment
    //     0x128f6f4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0x128f6f8: ldr             x16, [x16, #0x520]
    // 0x128f6fc: cmp             w1, w16
    // 0x128f700: b.ne            #0x128f710
    // 0x128f704: r5 = Instance_CrossAxisAlignment
    //     0x128f704: add             x5, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0x128f708: ldr             x5, [x5, #0xc68]
    // 0x128f70c: b               #0x128f734
    // 0x128f710: r16 = Instance_TitleAlignment
    //     0x128f710: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x128f714: ldr             x16, [x16, #0x518]
    // 0x128f718: cmp             w1, w16
    // 0x128f71c: b.ne            #0x128f72c
    // 0x128f720: r5 = Instance_CrossAxisAlignment
    //     0x128f720: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x128f724: ldr             x5, [x5, #0x890]
    // 0x128f728: b               #0x128f734
    // 0x128f72c: r5 = Instance_CrossAxisAlignment
    //     0x128f72c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x128f730: ldr             x5, [x5, #0xa18]
    // 0x128f734: stur            x5, [fp, #-0x18]
    // 0x128f738: r1 = <Widget>
    //     0x128f738: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128f73c: r2 = 0
    //     0x128f73c: movz            x2, #0
    // 0x128f740: r0 = _GrowableList()
    //     0x128f740: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x128f744: mov             x2, x0
    // 0x128f748: ldur            x1, [fp, #-8]
    // 0x128f74c: stur            x2, [fp, #-0x38]
    // 0x128f750: LoadField: r3 = r1->field_f
    //     0x128f750: ldur            w3, [x1, #0xf]
    // 0x128f754: DecompressPointer r3
    //     0x128f754: add             x3, x3, HEAP, lsl #32
    // 0x128f758: stur            x3, [fp, #-0x30]
    // 0x128f75c: cmp             w3, NULL
    // 0x128f760: b.ne            #0x128f76c
    // 0x128f764: r0 = Null
    //     0x128f764: mov             x0, NULL
    // 0x128f768: b               #0x128f784
    // 0x128f76c: LoadField: r0 = r3->field_7
    //     0x128f76c: ldur            w0, [x3, #7]
    // 0x128f770: cbnz            w0, #0x128f77c
    // 0x128f774: r4 = false
    //     0x128f774: add             x4, NULL, #0x30  ; false
    // 0x128f778: b               #0x128f780
    // 0x128f77c: r4 = true
    //     0x128f77c: add             x4, NULL, #0x20  ; true
    // 0x128f780: mov             x0, x4
    // 0x128f784: cmp             w0, NULL
    // 0x128f788: b.eq            #0x128f928
    // 0x128f78c: tbnz            w0, #4, #0x128f928
    // 0x128f790: cmp             w3, NULL
    // 0x128f794: b.ne            #0x128f7a0
    // 0x128f798: r0 = Null
    //     0x128f798: mov             x0, NULL
    // 0x128f79c: b               #0x128f7b8
    // 0x128f7a0: r0 = LoadClassIdInstr(r3)
    //     0x128f7a0: ldur            x0, [x3, #-1]
    //     0x128f7a4: ubfx            x0, x0, #0xc, #0x14
    // 0x128f7a8: str             x3, [SP]
    // 0x128f7ac: r0 = GDT[cid_x0 + -0x1000]()
    //     0x128f7ac: sub             lr, x0, #1, lsl #12
    //     0x128f7b0: ldr             lr, [x21, lr, lsl #3]
    //     0x128f7b4: blr             lr
    // 0x128f7b8: cmp             w0, NULL
    // 0x128f7bc: b.ne            #0x128f7c8
    // 0x128f7c0: r2 = ""
    //     0x128f7c0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x128f7c4: b               #0x128f7cc
    // 0x128f7c8: mov             x2, x0
    // 0x128f7cc: ldur            x0, [fp, #-0x20]
    // 0x128f7d0: stur            x2, [fp, #-0x40]
    // 0x128f7d4: LoadField: r1 = r0->field_7
    //     0x128f7d4: ldur            w1, [x0, #7]
    // 0x128f7d8: DecompressPointer r1
    //     0x128f7d8: add             x1, x1, HEAP, lsl #32
    // 0x128f7dc: cmp             w1, NULL
    // 0x128f7e0: b.ne            #0x128f7f0
    // 0x128f7e4: r0 = Instance_TitleAlignment
    //     0x128f7e4: add             x0, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x128f7e8: ldr             x0, [x0, #0x518]
    // 0x128f7ec: b               #0x128f7f4
    // 0x128f7f0: mov             x0, x1
    // 0x128f7f4: r16 = Instance_TitleAlignment
    //     0x128f7f4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0x128f7f8: ldr             x16, [x16, #0x520]
    // 0x128f7fc: cmp             w0, w16
    // 0x128f800: b.ne            #0x128f80c
    // 0x128f804: r3 = Instance_TextAlign
    //     0x128f804: ldr             x3, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0x128f808: b               #0x128f828
    // 0x128f80c: r16 = Instance_TitleAlignment
    //     0x128f80c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x128f810: ldr             x16, [x16, #0x518]
    // 0x128f814: cmp             w0, w16
    // 0x128f818: b.ne            #0x128f824
    // 0x128f81c: r3 = Instance_TextAlign
    //     0x128f81c: ldr             x3, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0x128f820: b               #0x128f828
    // 0x128f824: r3 = Instance_TextAlign
    //     0x128f824: ldr             x3, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0x128f828: ldur            x0, [fp, #-0x38]
    // 0x128f82c: ldur            x1, [fp, #-0x10]
    // 0x128f830: stur            x3, [fp, #-0x20]
    // 0x128f834: r0 = of()
    //     0x128f834: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x128f838: LoadField: r1 = r0->field_87
    //     0x128f838: ldur            w1, [x0, #0x87]
    // 0x128f83c: DecompressPointer r1
    //     0x128f83c: add             x1, x1, HEAP, lsl #32
    // 0x128f840: LoadField: r0 = r1->field_7
    //     0x128f840: ldur            w0, [x1, #7]
    // 0x128f844: DecompressPointer r0
    //     0x128f844: add             x0, x0, HEAP, lsl #32
    // 0x128f848: r16 = 32.000000
    //     0x128f848: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0x128f84c: ldr             x16, [x16, #0x848]
    // 0x128f850: r30 = Instance_Color
    //     0x128f850: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x128f854: stp             lr, x16, [SP]
    // 0x128f858: mov             x1, x0
    // 0x128f85c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x128f85c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x128f860: ldr             x4, [x4, #0xaa0]
    // 0x128f864: r0 = copyWith()
    //     0x128f864: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x128f868: stur            x0, [fp, #-0x48]
    // 0x128f86c: r0 = Text()
    //     0x128f86c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x128f870: mov             x1, x0
    // 0x128f874: ldur            x0, [fp, #-0x40]
    // 0x128f878: stur            x1, [fp, #-0x50]
    // 0x128f87c: StoreField: r1->field_b = r0
    //     0x128f87c: stur            w0, [x1, #0xb]
    // 0x128f880: ldur            x0, [fp, #-0x48]
    // 0x128f884: StoreField: r1->field_13 = r0
    //     0x128f884: stur            w0, [x1, #0x13]
    // 0x128f888: ldur            x0, [fp, #-0x20]
    // 0x128f88c: StoreField: r1->field_1b = r0
    //     0x128f88c: stur            w0, [x1, #0x1b]
    // 0x128f890: r0 = Padding()
    //     0x128f890: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128f894: mov             x2, x0
    // 0x128f898: r0 = Instance_EdgeInsets
    //     0x128f898: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f4c0] Obj!EdgeInsets@d579e1
    //     0x128f89c: ldr             x0, [x0, #0x4c0]
    // 0x128f8a0: stur            x2, [fp, #-0x20]
    // 0x128f8a4: StoreField: r2->field_f = r0
    //     0x128f8a4: stur            w0, [x2, #0xf]
    // 0x128f8a8: ldur            x0, [fp, #-0x50]
    // 0x128f8ac: StoreField: r2->field_b = r0
    //     0x128f8ac: stur            w0, [x2, #0xb]
    // 0x128f8b0: ldur            x0, [fp, #-0x38]
    // 0x128f8b4: LoadField: r1 = r0->field_b
    //     0x128f8b4: ldur            w1, [x0, #0xb]
    // 0x128f8b8: LoadField: r3 = r0->field_f
    //     0x128f8b8: ldur            w3, [x0, #0xf]
    // 0x128f8bc: DecompressPointer r3
    //     0x128f8bc: add             x3, x3, HEAP, lsl #32
    // 0x128f8c0: LoadField: r4 = r3->field_b
    //     0x128f8c0: ldur            w4, [x3, #0xb]
    // 0x128f8c4: r3 = LoadInt32Instr(r1)
    //     0x128f8c4: sbfx            x3, x1, #1, #0x1f
    // 0x128f8c8: stur            x3, [fp, #-0x58]
    // 0x128f8cc: r1 = LoadInt32Instr(r4)
    //     0x128f8cc: sbfx            x1, x4, #1, #0x1f
    // 0x128f8d0: cmp             x3, x1
    // 0x128f8d4: b.ne            #0x128f8e0
    // 0x128f8d8: mov             x1, x0
    // 0x128f8dc: r0 = _growToNextCapacity()
    //     0x128f8dc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x128f8e0: ldur            x2, [fp, #-0x38]
    // 0x128f8e4: ldur            x3, [fp, #-0x58]
    // 0x128f8e8: add             x0, x3, #1
    // 0x128f8ec: lsl             x1, x0, #1
    // 0x128f8f0: StoreField: r2->field_b = r1
    //     0x128f8f0: stur            w1, [x2, #0xb]
    // 0x128f8f4: LoadField: r1 = r2->field_f
    //     0x128f8f4: ldur            w1, [x2, #0xf]
    // 0x128f8f8: DecompressPointer r1
    //     0x128f8f8: add             x1, x1, HEAP, lsl #32
    // 0x128f8fc: ldur            x0, [fp, #-0x20]
    // 0x128f900: ArrayStore: r1[r3] = r0  ; List_4
    //     0x128f900: add             x25, x1, x3, lsl #2
    //     0x128f904: add             x25, x25, #0xf
    //     0x128f908: str             w0, [x25]
    //     0x128f90c: tbz             w0, #0, #0x128f928
    //     0x128f910: ldurb           w16, [x1, #-1]
    //     0x128f914: ldurb           w17, [x0, #-1]
    //     0x128f918: and             x16, x17, x16, lsr #2
    //     0x128f91c: tst             x16, HEAP, lsr #32
    //     0x128f920: b.eq            #0x128f928
    //     0x128f924: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x128f928: ldur            x0, [fp, #-8]
    // 0x128f92c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x128f92c: ldur            w3, [x0, #0x17]
    // 0x128f930: DecompressPointer r3
    //     0x128f930: add             x3, x3, HEAP, lsl #32
    // 0x128f934: stur            x3, [fp, #-0x40]
    // 0x128f938: cmp             w3, NULL
    // 0x128f93c: b.ne            #0x128f948
    // 0x128f940: r1 = Null
    //     0x128f940: mov             x1, NULL
    // 0x128f944: b               #0x128f974
    // 0x128f948: LoadField: r1 = r3->field_7
    //     0x128f948: ldur            w1, [x3, #7]
    // 0x128f94c: DecompressPointer r1
    //     0x128f94c: add             x1, x1, HEAP, lsl #32
    // 0x128f950: cmp             w1, NULL
    // 0x128f954: b.ne            #0x128f960
    // 0x128f958: r1 = Null
    //     0x128f958: mov             x1, NULL
    // 0x128f95c: b               #0x128f974
    // 0x128f960: LoadField: r4 = r1->field_7
    //     0x128f960: ldur            w4, [x1, #7]
    // 0x128f964: cbnz            w4, #0x128f970
    // 0x128f968: r1 = false
    //     0x128f968: add             x1, NULL, #0x30  ; false
    // 0x128f96c: b               #0x128f974
    // 0x128f970: r1 = true
    //     0x128f970: add             x1, NULL, #0x20  ; true
    // 0x128f974: cmp             w1, NULL
    // 0x128f978: b.ne            #0x128f984
    // 0x128f97c: r4 = false
    //     0x128f97c: add             x4, NULL, #0x30  ; false
    // 0x128f980: b               #0x128f988
    // 0x128f984: mov             x4, x1
    // 0x128f988: ldur            x1, [fp, #-0x10]
    // 0x128f98c: stur            x4, [fp, #-0x20]
    // 0x128f990: r0 = of()
    //     0x128f990: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x128f994: LoadField: r1 = r0->field_5b
    //     0x128f994: ldur            w1, [x0, #0x5b]
    // 0x128f998: DecompressPointer r1
    //     0x128f998: add             x1, x1, HEAP, lsl #32
    // 0x128f99c: stur            x1, [fp, #-0x48]
    // 0x128f9a0: r0 = BoxDecoration()
    //     0x128f9a0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x128f9a4: mov             x2, x0
    // 0x128f9a8: ldur            x0, [fp, #-0x48]
    // 0x128f9ac: stur            x2, [fp, #-0x50]
    // 0x128f9b0: StoreField: r2->field_7 = r0
    //     0x128f9b0: stur            w0, [x2, #7]
    // 0x128f9b4: r0 = Instance_BorderRadius
    //     0x128f9b4: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0x128f9b8: ldr             x0, [x0, #0x460]
    // 0x128f9bc: StoreField: r2->field_13 = r0
    //     0x128f9bc: stur            w0, [x2, #0x13]
    // 0x128f9c0: r0 = Instance_BoxShape
    //     0x128f9c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x128f9c4: ldr             x0, [x0, #0x80]
    // 0x128f9c8: StoreField: r2->field_23 = r0
    //     0x128f9c8: stur            w0, [x2, #0x23]
    // 0x128f9cc: ldur            x1, [fp, #-0x40]
    // 0x128f9d0: cmp             w1, NULL
    // 0x128f9d4: b.ne            #0x128f9e0
    // 0x128f9d8: r1 = Null
    //     0x128f9d8: mov             x1, NULL
    // 0x128f9dc: b               #0x128f9ec
    // 0x128f9e0: LoadField: r3 = r1->field_7
    //     0x128f9e0: ldur            w3, [x1, #7]
    // 0x128f9e4: DecompressPointer r3
    //     0x128f9e4: add             x3, x3, HEAP, lsl #32
    // 0x128f9e8: mov             x1, x3
    // 0x128f9ec: cmp             w1, NULL
    // 0x128f9f0: b.ne            #0x128f9fc
    // 0x128f9f4: r5 = ""
    //     0x128f9f4: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x128f9f8: b               #0x128fa00
    // 0x128f9fc: mov             x5, x1
    // 0x128fa00: ldur            x3, [fp, #-0x38]
    // 0x128fa04: ldur            x4, [fp, #-0x20]
    // 0x128fa08: ldur            x1, [fp, #-0x10]
    // 0x128fa0c: stur            x5, [fp, #-0x40]
    // 0x128fa10: r0 = of()
    //     0x128fa10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x128fa14: LoadField: r1 = r0->field_87
    //     0x128fa14: ldur            w1, [x0, #0x87]
    // 0x128fa18: DecompressPointer r1
    //     0x128fa18: add             x1, x1, HEAP, lsl #32
    // 0x128fa1c: LoadField: r0 = r1->field_2b
    //     0x128fa1c: ldur            w0, [x1, #0x2b]
    // 0x128fa20: DecompressPointer r0
    //     0x128fa20: add             x0, x0, HEAP, lsl #32
    // 0x128fa24: r16 = 16.000000
    //     0x128fa24: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x128fa28: ldr             x16, [x16, #0x188]
    // 0x128fa2c: r30 = Instance_Color
    //     0x128fa2c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x128fa30: stp             lr, x16, [SP]
    // 0x128fa34: mov             x1, x0
    // 0x128fa38: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x128fa38: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x128fa3c: ldr             x4, [x4, #0xaa0]
    // 0x128fa40: r0 = copyWith()
    //     0x128fa40: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x128fa44: stur            x0, [fp, #-0x48]
    // 0x128fa48: r0 = Text()
    //     0x128fa48: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x128fa4c: mov             x1, x0
    // 0x128fa50: ldur            x0, [fp, #-0x40]
    // 0x128fa54: stur            x1, [fp, #-0x60]
    // 0x128fa58: StoreField: r1->field_b = r0
    //     0x128fa58: stur            w0, [x1, #0xb]
    // 0x128fa5c: ldur            x0, [fp, #-0x48]
    // 0x128fa60: StoreField: r1->field_13 = r0
    //     0x128fa60: stur            w0, [x1, #0x13]
    // 0x128fa64: r0 = Center()
    //     0x128fa64: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x128fa68: mov             x1, x0
    // 0x128fa6c: r0 = Instance_Alignment
    //     0x128fa6c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x128fa70: ldr             x0, [x0, #0xb10]
    // 0x128fa74: stur            x1, [fp, #-0x40]
    // 0x128fa78: StoreField: r1->field_f = r0
    //     0x128fa78: stur            w0, [x1, #0xf]
    // 0x128fa7c: ldur            x0, [fp, #-0x60]
    // 0x128fa80: StoreField: r1->field_b = r0
    //     0x128fa80: stur            w0, [x1, #0xb]
    // 0x128fa84: r0 = Container()
    //     0x128fa84: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x128fa88: stur            x0, [fp, #-0x48]
    // 0x128fa8c: r16 = 40.000000
    //     0x128fa8c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0x128fa90: ldr             x16, [x16, #8]
    // 0x128fa94: r30 = 110.000000
    //     0x128fa94: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0x128fa98: ldr             lr, [lr, #0x770]
    // 0x128fa9c: stp             lr, x16, [SP, #0x10]
    // 0x128faa0: ldur            x16, [fp, #-0x50]
    // 0x128faa4: ldur            lr, [fp, #-0x40]
    // 0x128faa8: stp             lr, x16, [SP]
    // 0x128faac: mov             x1, x0
    // 0x128fab0: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0x128fab0: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x128fab4: ldr             x4, [x4, #0x8c0]
    // 0x128fab8: r0 = Container()
    //     0x128fab8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x128fabc: r0 = InkWell()
    //     0x128fabc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x128fac0: mov             x3, x0
    // 0x128fac4: ldur            x0, [fp, #-0x48]
    // 0x128fac8: stur            x3, [fp, #-0x40]
    // 0x128facc: StoreField: r3->field_b = r0
    //     0x128facc: stur            w0, [x3, #0xb]
    // 0x128fad0: ldur            x2, [fp, #-0x28]
    // 0x128fad4: r1 = Function '<anonymous closure>':.
    //     0x128fad4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48b18] AnonymousClosure: (0x1290600), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_detail_grid_item_view.dart] ProductDetailGridItemView::build (0x128f690)
    //     0x128fad8: ldr             x1, [x1, #0xb18]
    // 0x128fadc: r0 = AllocateClosure()
    //     0x128fadc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128fae0: mov             x1, x0
    // 0x128fae4: ldur            x0, [fp, #-0x40]
    // 0x128fae8: StoreField: r0->field_f = r1
    //     0x128fae8: stur            w1, [x0, #0xf]
    // 0x128faec: r1 = true
    //     0x128faec: add             x1, NULL, #0x20  ; true
    // 0x128faf0: StoreField: r0->field_43 = r1
    //     0x128faf0: stur            w1, [x0, #0x43]
    // 0x128faf4: r2 = Instance_BoxShape
    //     0x128faf4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x128faf8: ldr             x2, [x2, #0x80]
    // 0x128fafc: StoreField: r0->field_47 = r2
    //     0x128fafc: stur            w2, [x0, #0x47]
    // 0x128fb00: StoreField: r0->field_6f = r1
    //     0x128fb00: stur            w1, [x0, #0x6f]
    // 0x128fb04: r3 = false
    //     0x128fb04: add             x3, NULL, #0x30  ; false
    // 0x128fb08: StoreField: r0->field_73 = r3
    //     0x128fb08: stur            w3, [x0, #0x73]
    // 0x128fb0c: StoreField: r0->field_83 = r1
    //     0x128fb0c: stur            w1, [x0, #0x83]
    // 0x128fb10: StoreField: r0->field_7b = r3
    //     0x128fb10: stur            w3, [x0, #0x7b]
    // 0x128fb14: r0 = Visibility()
    //     0x128fb14: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x128fb18: mov             x2, x0
    // 0x128fb1c: ldur            x0, [fp, #-0x40]
    // 0x128fb20: stur            x2, [fp, #-0x28]
    // 0x128fb24: StoreField: r2->field_b = r0
    //     0x128fb24: stur            w0, [x2, #0xb]
    // 0x128fb28: r0 = Instance_SizedBox
    //     0x128fb28: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x128fb2c: StoreField: r2->field_f = r0
    //     0x128fb2c: stur            w0, [x2, #0xf]
    // 0x128fb30: ldur            x1, [fp, #-0x20]
    // 0x128fb34: StoreField: r2->field_13 = r1
    //     0x128fb34: stur            w1, [x2, #0x13]
    // 0x128fb38: r3 = false
    //     0x128fb38: add             x3, NULL, #0x30  ; false
    // 0x128fb3c: ArrayStore: r2[0] = r3  ; List_4
    //     0x128fb3c: stur            w3, [x2, #0x17]
    // 0x128fb40: StoreField: r2->field_1b = r3
    //     0x128fb40: stur            w3, [x2, #0x1b]
    // 0x128fb44: StoreField: r2->field_1f = r3
    //     0x128fb44: stur            w3, [x2, #0x1f]
    // 0x128fb48: StoreField: r2->field_23 = r3
    //     0x128fb48: stur            w3, [x2, #0x23]
    // 0x128fb4c: StoreField: r2->field_27 = r3
    //     0x128fb4c: stur            w3, [x2, #0x27]
    // 0x128fb50: StoreField: r2->field_2b = r3
    //     0x128fb50: stur            w3, [x2, #0x2b]
    // 0x128fb54: ldur            x4, [fp, #-0x38]
    // 0x128fb58: LoadField: r1 = r4->field_b
    //     0x128fb58: ldur            w1, [x4, #0xb]
    // 0x128fb5c: LoadField: r5 = r4->field_f
    //     0x128fb5c: ldur            w5, [x4, #0xf]
    // 0x128fb60: DecompressPointer r5
    //     0x128fb60: add             x5, x5, HEAP, lsl #32
    // 0x128fb64: LoadField: r6 = r5->field_b
    //     0x128fb64: ldur            w6, [x5, #0xb]
    // 0x128fb68: r5 = LoadInt32Instr(r1)
    //     0x128fb68: sbfx            x5, x1, #1, #0x1f
    // 0x128fb6c: stur            x5, [fp, #-0x58]
    // 0x128fb70: r1 = LoadInt32Instr(r6)
    //     0x128fb70: sbfx            x1, x6, #1, #0x1f
    // 0x128fb74: cmp             x5, x1
    // 0x128fb78: b.ne            #0x128fb84
    // 0x128fb7c: mov             x1, x4
    // 0x128fb80: r0 = _growToNextCapacity()
    //     0x128fb80: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x128fb84: ldur            x2, [fp, #-0x38]
    // 0x128fb88: ldur            x3, [fp, #-0x58]
    // 0x128fb8c: add             x4, x3, #1
    // 0x128fb90: stur            x4, [fp, #-0x68]
    // 0x128fb94: lsl             x0, x4, #1
    // 0x128fb98: StoreField: r2->field_b = r0
    //     0x128fb98: stur            w0, [x2, #0xb]
    // 0x128fb9c: LoadField: r5 = r2->field_f
    //     0x128fb9c: ldur            w5, [x2, #0xf]
    // 0x128fba0: DecompressPointer r5
    //     0x128fba0: add             x5, x5, HEAP, lsl #32
    // 0x128fba4: mov             x1, x5
    // 0x128fba8: ldur            x0, [fp, #-0x28]
    // 0x128fbac: ArrayStore: r1[r3] = r0  ; List_4
    //     0x128fbac: add             x25, x1, x3, lsl #2
    //     0x128fbb0: add             x25, x25, #0xf
    //     0x128fbb4: str             w0, [x25]
    //     0x128fbb8: tbz             w0, #0, #0x128fbd4
    //     0x128fbbc: ldurb           w16, [x1, #-1]
    //     0x128fbc0: ldurb           w17, [x0, #-1]
    //     0x128fbc4: and             x16, x17, x16, lsr #2
    //     0x128fbc8: tst             x16, HEAP, lsr #32
    //     0x128fbcc: b.eq            #0x128fbd4
    //     0x128fbd0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x128fbd4: LoadField: r0 = r5->field_b
    //     0x128fbd4: ldur            w0, [x5, #0xb]
    // 0x128fbd8: r1 = LoadInt32Instr(r0)
    //     0x128fbd8: sbfx            x1, x0, #1, #0x1f
    // 0x128fbdc: cmp             x4, x1
    // 0x128fbe0: b.ne            #0x128fbec
    // 0x128fbe4: mov             x1, x2
    // 0x128fbe8: r0 = _growToNextCapacity()
    //     0x128fbe8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x128fbec: ldur            x2, [fp, #-8]
    // 0x128fbf0: ldur            x1, [fp, #-0x38]
    // 0x128fbf4: ldur            x0, [fp, #-0x68]
    // 0x128fbf8: add             x3, x0, #1
    // 0x128fbfc: lsl             x4, x3, #1
    // 0x128fc00: StoreField: r1->field_b = r4
    //     0x128fc00: stur            w4, [x1, #0xb]
    // 0x128fc04: LoadField: r3 = r1->field_f
    //     0x128fc04: ldur            w3, [x1, #0xf]
    // 0x128fc08: DecompressPointer r3
    //     0x128fc08: add             x3, x3, HEAP, lsl #32
    // 0x128fc0c: add             x4, x3, x0, lsl #2
    // 0x128fc10: r16 = Instance_SizedBox
    //     0x128fc10: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x128fc14: ldr             x16, [x16, #0x8f0]
    // 0x128fc18: StoreField: r4->field_f = r16
    //     0x128fc18: stur            w16, [x4, #0xf]
    // 0x128fc1c: LoadField: r0 = r2->field_33
    //     0x128fc1c: ldur            w0, [x2, #0x33]
    // 0x128fc20: DecompressPointer r0
    //     0x128fc20: add             x0, x0, HEAP, lsl #32
    // 0x128fc24: stur            x0, [fp, #-0x40]
    // 0x128fc28: LoadField: r3 = r0->field_f
    //     0x128fc28: ldur            w3, [x0, #0xf]
    // 0x128fc2c: DecompressPointer r3
    //     0x128fc2c: add             x3, x3, HEAP, lsl #32
    // 0x128fc30: cmp             w3, NULL
    // 0x128fc34: r16 = true
    //     0x128fc34: add             x16, NULL, #0x20  ; true
    // 0x128fc38: r17 = false
    //     0x128fc38: add             x17, NULL, #0x30  ; false
    // 0x128fc3c: csel            x4, x16, x17, ne
    // 0x128fc40: stur            x4, [fp, #-0x28]
    // 0x128fc44: LoadField: r3 = r0->field_13
    //     0x128fc44: ldur            w3, [x0, #0x13]
    // 0x128fc48: DecompressPointer r3
    //     0x128fc48: add             x3, x3, HEAP, lsl #32
    // 0x128fc4c: stur            x3, [fp, #-0x20]
    // 0x128fc50: cmp             w3, NULL
    // 0x128fc54: b.ne            #0x128fc60
    // 0x128fc58: r5 = Null
    //     0x128fc58: mov             x5, NULL
    // 0x128fc5c: b               #0x128fc68
    // 0x128fc60: LoadField: r5 = r3->field_7
    //     0x128fc60: ldur            w5, [x3, #7]
    // 0x128fc64: DecompressPointer r5
    //     0x128fc64: add             x5, x5, HEAP, lsl #32
    // 0x128fc68: cmp             w5, NULL
    // 0x128fc6c: b.ne            #0x128fc78
    // 0x128fc70: r5 = 0
    //     0x128fc70: movz            x5, #0
    // 0x128fc74: b               #0x128fc88
    // 0x128fc78: r6 = LoadInt32Instr(r5)
    //     0x128fc78: sbfx            x6, x5, #1, #0x1f
    //     0x128fc7c: tbz             w5, #0, #0x128fc84
    //     0x128fc80: ldur            x6, [x5, #7]
    // 0x128fc84: mov             x5, x6
    // 0x128fc88: stur            x5, [fp, #-0x70]
    // 0x128fc8c: cmp             w3, NULL
    // 0x128fc90: b.ne            #0x128fc9c
    // 0x128fc94: r6 = Null
    //     0x128fc94: mov             x6, NULL
    // 0x128fc98: b               #0x128fca4
    // 0x128fc9c: LoadField: r6 = r3->field_b
    //     0x128fc9c: ldur            w6, [x3, #0xb]
    // 0x128fca0: DecompressPointer r6
    //     0x128fca0: add             x6, x6, HEAP, lsl #32
    // 0x128fca4: cmp             w6, NULL
    // 0x128fca8: b.ne            #0x128fcb4
    // 0x128fcac: r6 = 0
    //     0x128fcac: movz            x6, #0
    // 0x128fcb0: b               #0x128fcc4
    // 0x128fcb4: r7 = LoadInt32Instr(r6)
    //     0x128fcb4: sbfx            x7, x6, #1, #0x1f
    //     0x128fcb8: tbz             w6, #0, #0x128fcc0
    //     0x128fcbc: ldur            x7, [x6, #7]
    // 0x128fcc0: mov             x6, x7
    // 0x128fcc4: stur            x6, [fp, #-0x68]
    // 0x128fcc8: cmp             w3, NULL
    // 0x128fccc: b.ne            #0x128fcd8
    // 0x128fcd0: r7 = Null
    //     0x128fcd0: mov             x7, NULL
    // 0x128fcd4: b               #0x128fce0
    // 0x128fcd8: LoadField: r7 = r3->field_f
    //     0x128fcd8: ldur            w7, [x3, #0xf]
    // 0x128fcdc: DecompressPointer r7
    //     0x128fcdc: add             x7, x7, HEAP, lsl #32
    // 0x128fce0: cmp             w7, NULL
    // 0x128fce4: b.ne            #0x128fcf0
    // 0x128fce8: r7 = 0
    //     0x128fce8: movz            x7, #0
    // 0x128fcec: b               #0x128fd00
    // 0x128fcf0: r8 = LoadInt32Instr(r7)
    //     0x128fcf0: sbfx            x8, x7, #1, #0x1f
    //     0x128fcf4: tbz             w7, #0, #0x128fcfc
    //     0x128fcf8: ldur            x8, [x7, #7]
    // 0x128fcfc: mov             x7, x8
    // 0x128fd00: stur            x7, [fp, #-0x58]
    // 0x128fd04: r0 = Color()
    //     0x128fd04: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x128fd08: mov             x1, x0
    // 0x128fd0c: r0 = Instance_ColorSpace
    //     0x128fd0c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x128fd10: stur            x1, [fp, #-0x48]
    // 0x128fd14: StoreField: r1->field_27 = r0
    //     0x128fd14: stur            w0, [x1, #0x27]
    // 0x128fd18: d0 = 1.000000
    //     0x128fd18: fmov            d0, #1.00000000
    // 0x128fd1c: StoreField: r1->field_7 = d0
    //     0x128fd1c: stur            d0, [x1, #7]
    // 0x128fd20: ldur            x2, [fp, #-0x70]
    // 0x128fd24: ubfx            x2, x2, #0, #0x20
    // 0x128fd28: and             w3, w2, #0xff
    // 0x128fd2c: ubfx            x3, x3, #0, #0x20
    // 0x128fd30: scvtf           d0, x3
    // 0x128fd34: d1 = 255.000000
    //     0x128fd34: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x128fd38: fdiv            d2, d0, d1
    // 0x128fd3c: StoreField: r1->field_f = d2
    //     0x128fd3c: stur            d2, [x1, #0xf]
    // 0x128fd40: ldur            x2, [fp, #-0x68]
    // 0x128fd44: ubfx            x2, x2, #0, #0x20
    // 0x128fd48: and             w3, w2, #0xff
    // 0x128fd4c: ubfx            x3, x3, #0, #0x20
    // 0x128fd50: scvtf           d0, x3
    // 0x128fd54: fdiv            d2, d0, d1
    // 0x128fd58: ArrayStore: r1[0] = d2  ; List_8
    //     0x128fd58: stur            d2, [x1, #0x17]
    // 0x128fd5c: ldur            x2, [fp, #-0x58]
    // 0x128fd60: ubfx            x2, x2, #0, #0x20
    // 0x128fd64: and             w3, w2, #0xff
    // 0x128fd68: ubfx            x3, x3, #0, #0x20
    // 0x128fd6c: scvtf           d0, x3
    // 0x128fd70: fdiv            d2, d0, d1
    // 0x128fd74: StoreField: r1->field_1f = d2
    //     0x128fd74: stur            d2, [x1, #0x1f]
    // 0x128fd78: ldur            x2, [fp, #-0x20]
    // 0x128fd7c: cmp             w2, NULL
    // 0x128fd80: b.ne            #0x128fd8c
    // 0x128fd84: r3 = Null
    //     0x128fd84: mov             x3, NULL
    // 0x128fd88: b               #0x128fd94
    // 0x128fd8c: LoadField: r3 = r2->field_7
    //     0x128fd8c: ldur            w3, [x2, #7]
    // 0x128fd90: DecompressPointer r3
    //     0x128fd90: add             x3, x3, HEAP, lsl #32
    // 0x128fd94: cmp             w3, NULL
    // 0x128fd98: b.ne            #0x128fda4
    // 0x128fd9c: r3 = 0
    //     0x128fd9c: movz            x3, #0
    // 0x128fda0: b               #0x128fdb4
    // 0x128fda4: r4 = LoadInt32Instr(r3)
    //     0x128fda4: sbfx            x4, x3, #1, #0x1f
    //     0x128fda8: tbz             w3, #0, #0x128fdb0
    //     0x128fdac: ldur            x4, [x3, #7]
    // 0x128fdb0: mov             x3, x4
    // 0x128fdb4: stur            x3, [fp, #-0x70]
    // 0x128fdb8: cmp             w2, NULL
    // 0x128fdbc: b.ne            #0x128fdc8
    // 0x128fdc0: r4 = Null
    //     0x128fdc0: mov             x4, NULL
    // 0x128fdc4: b               #0x128fdd0
    // 0x128fdc8: LoadField: r4 = r2->field_b
    //     0x128fdc8: ldur            w4, [x2, #0xb]
    // 0x128fdcc: DecompressPointer r4
    //     0x128fdcc: add             x4, x4, HEAP, lsl #32
    // 0x128fdd0: cmp             w4, NULL
    // 0x128fdd4: b.ne            #0x128fde0
    // 0x128fdd8: r4 = 0
    //     0x128fdd8: movz            x4, #0
    // 0x128fddc: b               #0x128fdf0
    // 0x128fde0: r5 = LoadInt32Instr(r4)
    //     0x128fde0: sbfx            x5, x4, #1, #0x1f
    //     0x128fde4: tbz             w4, #0, #0x128fdec
    //     0x128fde8: ldur            x5, [x4, #7]
    // 0x128fdec: mov             x4, x5
    // 0x128fdf0: stur            x4, [fp, #-0x68]
    // 0x128fdf4: cmp             w2, NULL
    // 0x128fdf8: b.ne            #0x128fe04
    // 0x128fdfc: r2 = Null
    //     0x128fdfc: mov             x2, NULL
    // 0x128fe00: b               #0x128fe10
    // 0x128fe04: LoadField: r5 = r2->field_f
    //     0x128fe04: ldur            w5, [x2, #0xf]
    // 0x128fe08: DecompressPointer r5
    //     0x128fe08: add             x5, x5, HEAP, lsl #32
    // 0x128fe0c: mov             x2, x5
    // 0x128fe10: cmp             w2, NULL
    // 0x128fe14: b.ne            #0x128fe20
    // 0x128fe18: r7 = 0
    //     0x128fe18: movz            x7, #0
    // 0x128fe1c: b               #0x128fe30
    // 0x128fe20: r5 = LoadInt32Instr(r2)
    //     0x128fe20: sbfx            x5, x2, #1, #0x1f
    //     0x128fe24: tbz             w2, #0, #0x128fe2c
    //     0x128fe28: ldur            x5, [x2, #7]
    // 0x128fe2c: mov             x7, x5
    // 0x128fe30: ldur            x2, [fp, #-0x38]
    // 0x128fe34: ldur            x5, [fp, #-0x40]
    // 0x128fe38: ldur            x6, [fp, #-0x28]
    // 0x128fe3c: stur            x7, [fp, #-0x58]
    // 0x128fe40: r0 = Color()
    //     0x128fe40: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x128fe44: mov             x3, x0
    // 0x128fe48: r0 = Instance_ColorSpace
    //     0x128fe48: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x128fe4c: stur            x3, [fp, #-0x20]
    // 0x128fe50: StoreField: r3->field_27 = r0
    //     0x128fe50: stur            w0, [x3, #0x27]
    // 0x128fe54: d0 = 0.700000
    //     0x128fe54: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x128fe58: ldr             d0, [x17, #0xf48]
    // 0x128fe5c: StoreField: r3->field_7 = d0
    //     0x128fe5c: stur            d0, [x3, #7]
    // 0x128fe60: ldur            x0, [fp, #-0x70]
    // 0x128fe64: ubfx            x0, x0, #0, #0x20
    // 0x128fe68: and             w1, w0, #0xff
    // 0x128fe6c: ubfx            x1, x1, #0, #0x20
    // 0x128fe70: scvtf           d0, x1
    // 0x128fe74: d1 = 255.000000
    //     0x128fe74: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x128fe78: fdiv            d2, d0, d1
    // 0x128fe7c: StoreField: r3->field_f = d2
    //     0x128fe7c: stur            d2, [x3, #0xf]
    // 0x128fe80: ldur            x0, [fp, #-0x68]
    // 0x128fe84: ubfx            x0, x0, #0, #0x20
    // 0x128fe88: and             w1, w0, #0xff
    // 0x128fe8c: ubfx            x1, x1, #0, #0x20
    // 0x128fe90: scvtf           d0, x1
    // 0x128fe94: fdiv            d2, d0, d1
    // 0x128fe98: ArrayStore: r3[0] = d2  ; List_8
    //     0x128fe98: stur            d2, [x3, #0x17]
    // 0x128fe9c: ldur            x0, [fp, #-0x58]
    // 0x128fea0: ubfx            x0, x0, #0, #0x20
    // 0x128fea4: and             w1, w0, #0xff
    // 0x128fea8: ubfx            x1, x1, #0, #0x20
    // 0x128feac: scvtf           d0, x1
    // 0x128feb0: fdiv            d2, d0, d1
    // 0x128feb4: StoreField: r3->field_1f = d2
    //     0x128feb4: stur            d2, [x3, #0x1f]
    // 0x128feb8: r1 = Null
    //     0x128feb8: mov             x1, NULL
    // 0x128febc: r2 = 4
    //     0x128febc: movz            x2, #0x4
    // 0x128fec0: r0 = AllocateArray()
    //     0x128fec0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128fec4: mov             x2, x0
    // 0x128fec8: ldur            x0, [fp, #-0x48]
    // 0x128fecc: stur            x2, [fp, #-0x50]
    // 0x128fed0: StoreField: r2->field_f = r0
    //     0x128fed0: stur            w0, [x2, #0xf]
    // 0x128fed4: ldur            x0, [fp, #-0x20]
    // 0x128fed8: StoreField: r2->field_13 = r0
    //     0x128fed8: stur            w0, [x2, #0x13]
    // 0x128fedc: r1 = <Color>
    //     0x128fedc: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x128fee0: ldr             x1, [x1, #0xf80]
    // 0x128fee4: r0 = AllocateGrowableArray()
    //     0x128fee4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128fee8: mov             x1, x0
    // 0x128feec: ldur            x0, [fp, #-0x50]
    // 0x128fef0: stur            x1, [fp, #-0x20]
    // 0x128fef4: StoreField: r1->field_f = r0
    //     0x128fef4: stur            w0, [x1, #0xf]
    // 0x128fef8: r2 = 4
    //     0x128fef8: movz            x2, #0x4
    // 0x128fefc: StoreField: r1->field_b = r2
    //     0x128fefc: stur            w2, [x1, #0xb]
    // 0x128ff00: r0 = LinearGradient()
    //     0x128ff00: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0x128ff04: mov             x1, x0
    // 0x128ff08: r0 = Instance_Alignment
    //     0x128ff08: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0x128ff0c: ldr             x0, [x0, #0xce0]
    // 0x128ff10: stur            x1, [fp, #-0x48]
    // 0x128ff14: StoreField: r1->field_13 = r0
    //     0x128ff14: stur            w0, [x1, #0x13]
    // 0x128ff18: r0 = Instance_Alignment
    //     0x128ff18: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0x128ff1c: ldr             x0, [x0, #0xce8]
    // 0x128ff20: ArrayStore: r1[0] = r0  ; List_4
    //     0x128ff20: stur            w0, [x1, #0x17]
    // 0x128ff24: r0 = Instance_TileMode
    //     0x128ff24: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0x128ff28: ldr             x0, [x0, #0xcf0]
    // 0x128ff2c: StoreField: r1->field_1b = r0
    //     0x128ff2c: stur            w0, [x1, #0x1b]
    // 0x128ff30: ldur            x0, [fp, #-0x20]
    // 0x128ff34: StoreField: r1->field_7 = r0
    //     0x128ff34: stur            w0, [x1, #7]
    // 0x128ff38: r0 = BoxDecoration()
    //     0x128ff38: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x128ff3c: mov             x2, x0
    // 0x128ff40: r0 = Instance_BorderRadius
    //     0x128ff40: add             x0, PP, #0x48, lsl #12  ; [pp+0x48b20] Obj!BorderRadius@d5a2e1
    //     0x128ff44: ldr             x0, [x0, #0xb20]
    // 0x128ff48: stur            x2, [fp, #-0x20]
    // 0x128ff4c: StoreField: r2->field_13 = r0
    //     0x128ff4c: stur            w0, [x2, #0x13]
    // 0x128ff50: ldur            x0, [fp, #-0x48]
    // 0x128ff54: StoreField: r2->field_1b = r0
    //     0x128ff54: stur            w0, [x2, #0x1b]
    // 0x128ff58: r0 = Instance_BoxShape
    //     0x128ff58: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x128ff5c: ldr             x0, [x0, #0x80]
    // 0x128ff60: StoreField: r2->field_23 = r0
    //     0x128ff60: stur            w0, [x2, #0x23]
    // 0x128ff64: ldur            x1, [fp, #-0x10]
    // 0x128ff68: r0 = of()
    //     0x128ff68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x128ff6c: LoadField: r1 = r0->field_87
    //     0x128ff6c: ldur            w1, [x0, #0x87]
    // 0x128ff70: DecompressPointer r1
    //     0x128ff70: add             x1, x1, HEAP, lsl #32
    // 0x128ff74: LoadField: r0 = r1->field_7
    //     0x128ff74: ldur            w0, [x1, #7]
    // 0x128ff78: DecompressPointer r0
    //     0x128ff78: add             x0, x0, HEAP, lsl #32
    // 0x128ff7c: r16 = 16.000000
    //     0x128ff7c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x128ff80: ldr             x16, [x16, #0x188]
    // 0x128ff84: r30 = Instance_Color
    //     0x128ff84: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x128ff88: stp             lr, x16, [SP]
    // 0x128ff8c: mov             x1, x0
    // 0x128ff90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x128ff90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x128ff94: ldr             x4, [x4, #0xaa0]
    // 0x128ff98: r0 = copyWith()
    //     0x128ff98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x128ff9c: stur            x0, [fp, #-0x48]
    // 0x128ffa0: r0 = TextSpan()
    //     0x128ffa0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x128ffa4: mov             x2, x0
    // 0x128ffa8: r0 = "BUMPER OFFER\n"
    //     0x128ffa8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48338] "BUMPER OFFER\n"
    //     0x128ffac: ldr             x0, [x0, #0x338]
    // 0x128ffb0: stur            x2, [fp, #-0x50]
    // 0x128ffb4: StoreField: r2->field_b = r0
    //     0x128ffb4: stur            w0, [x2, #0xb]
    // 0x128ffb8: r0 = Instance__DeferringMouseCursor
    //     0x128ffb8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x128ffbc: ArrayStore: r2[0] = r0  ; List_4
    //     0x128ffbc: stur            w0, [x2, #0x17]
    // 0x128ffc0: ldur            x1, [fp, #-0x48]
    // 0x128ffc4: StoreField: r2->field_7 = r1
    //     0x128ffc4: stur            w1, [x2, #7]
    // 0x128ffc8: ldur            x1, [fp, #-0x10]
    // 0x128ffcc: r0 = of()
    //     0x128ffcc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x128ffd0: LoadField: r1 = r0->field_87
    //     0x128ffd0: ldur            w1, [x0, #0x87]
    // 0x128ffd4: DecompressPointer r1
    //     0x128ffd4: add             x1, x1, HEAP, lsl #32
    // 0x128ffd8: LoadField: r0 = r1->field_2b
    //     0x128ffd8: ldur            w0, [x1, #0x2b]
    // 0x128ffdc: DecompressPointer r0
    //     0x128ffdc: add             x0, x0, HEAP, lsl #32
    // 0x128ffe0: r16 = 12.000000
    //     0x128ffe0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x128ffe4: ldr             x16, [x16, #0x9e8]
    // 0x128ffe8: r30 = Instance_Color
    //     0x128ffe8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x128ffec: stp             lr, x16, [SP]
    // 0x128fff0: mov             x1, x0
    // 0x128fff4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x128fff4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x128fff8: ldr             x4, [x4, #0xaa0]
    // 0x128fffc: r0 = copyWith()
    //     0x128fffc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1290000: stur            x0, [fp, #-0x48]
    // 0x1290004: r0 = TextSpan()
    //     0x1290004: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1290008: mov             x3, x0
    // 0x129000c: r0 = "Unlocked from your last order"
    //     0x129000c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48340] "Unlocked from your last order"
    //     0x1290010: ldr             x0, [x0, #0x340]
    // 0x1290014: stur            x3, [fp, #-0x60]
    // 0x1290018: StoreField: r3->field_b = r0
    //     0x1290018: stur            w0, [x3, #0xb]
    // 0x129001c: r0 = Instance__DeferringMouseCursor
    //     0x129001c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1290020: ArrayStore: r3[0] = r0  ; List_4
    //     0x1290020: stur            w0, [x3, #0x17]
    // 0x1290024: ldur            x1, [fp, #-0x48]
    // 0x1290028: StoreField: r3->field_7 = r1
    //     0x1290028: stur            w1, [x3, #7]
    // 0x129002c: r1 = Null
    //     0x129002c: mov             x1, NULL
    // 0x1290030: r2 = 4
    //     0x1290030: movz            x2, #0x4
    // 0x1290034: r0 = AllocateArray()
    //     0x1290034: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1290038: mov             x2, x0
    // 0x129003c: ldur            x0, [fp, #-0x50]
    // 0x1290040: stur            x2, [fp, #-0x48]
    // 0x1290044: StoreField: r2->field_f = r0
    //     0x1290044: stur            w0, [x2, #0xf]
    // 0x1290048: ldur            x0, [fp, #-0x60]
    // 0x129004c: StoreField: r2->field_13 = r0
    //     0x129004c: stur            w0, [x2, #0x13]
    // 0x1290050: r1 = <InlineSpan>
    //     0x1290050: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x1290054: ldr             x1, [x1, #0xe40]
    // 0x1290058: r0 = AllocateGrowableArray()
    //     0x1290058: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x129005c: mov             x1, x0
    // 0x1290060: ldur            x0, [fp, #-0x48]
    // 0x1290064: stur            x1, [fp, #-0x50]
    // 0x1290068: StoreField: r1->field_f = r0
    //     0x1290068: stur            w0, [x1, #0xf]
    // 0x129006c: r2 = 4
    //     0x129006c: movz            x2, #0x4
    // 0x1290070: StoreField: r1->field_b = r2
    //     0x1290070: stur            w2, [x1, #0xb]
    // 0x1290074: r0 = TextSpan()
    //     0x1290074: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1290078: mov             x1, x0
    // 0x129007c: ldur            x0, [fp, #-0x50]
    // 0x1290080: stur            x1, [fp, #-0x48]
    // 0x1290084: StoreField: r1->field_f = r0
    //     0x1290084: stur            w0, [x1, #0xf]
    // 0x1290088: r0 = Instance__DeferringMouseCursor
    //     0x1290088: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x129008c: ArrayStore: r1[0] = r0  ; List_4
    //     0x129008c: stur            w0, [x1, #0x17]
    // 0x1290090: r0 = RichText()
    //     0x1290090: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x1290094: mov             x1, x0
    // 0x1290098: ldur            x2, [fp, #-0x48]
    // 0x129009c: stur            x0, [fp, #-0x48]
    // 0x12900a0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x12900a0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x12900a4: r0 = RichText()
    //     0x12900a4: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x12900a8: ldur            x0, [fp, #-0x40]
    // 0x12900ac: LoadField: r3 = r0->field_7
    //     0x12900ac: ldur            w3, [x0, #7]
    // 0x12900b0: DecompressPointer r3
    //     0x12900b0: add             x3, x3, HEAP, lsl #32
    // 0x12900b4: stur            x3, [fp, #-0x50]
    // 0x12900b8: r1 = Null
    //     0x12900b8: mov             x1, NULL
    // 0x12900bc: r2 = 4
    //     0x12900bc: movz            x2, #0x4
    // 0x12900c0: r0 = AllocateArray()
    //     0x12900c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12900c4: mov             x1, x0
    // 0x12900c8: ldur            x0, [fp, #-0x50]
    // 0x12900cc: StoreField: r1->field_f = r0
    //     0x12900cc: stur            w0, [x1, #0xf]
    // 0x12900d0: r16 = "\n"
    //     0x12900d0: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0x12900d4: StoreField: r1->field_13 = r16
    //     0x12900d4: stur            w16, [x1, #0x13]
    // 0x12900d8: str             x1, [SP]
    // 0x12900dc: r0 = _interpolate()
    //     0x12900dc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x12900e0: ldur            x1, [fp, #-0x10]
    // 0x12900e4: stur            x0, [fp, #-0x50]
    // 0x12900e8: r0 = of()
    //     0x12900e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12900ec: LoadField: r1 = r0->field_87
    //     0x12900ec: ldur            w1, [x0, #0x87]
    // 0x12900f0: DecompressPointer r1
    //     0x12900f0: add             x1, x1, HEAP, lsl #32
    // 0x12900f4: LoadField: r0 = r1->field_7
    //     0x12900f4: ldur            w0, [x1, #7]
    // 0x12900f8: DecompressPointer r0
    //     0x12900f8: add             x0, x0, HEAP, lsl #32
    // 0x12900fc: r16 = 32.000000
    //     0x12900fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0x1290100: ldr             x16, [x16, #0x848]
    // 0x1290104: r30 = Instance_Color
    //     0x1290104: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1290108: stp             lr, x16, [SP]
    // 0x129010c: mov             x1, x0
    // 0x1290110: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1290110: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1290114: ldr             x4, [x4, #0xaa0]
    // 0x1290118: r0 = copyWith()
    //     0x1290118: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x129011c: ldur            x1, [fp, #-0x10]
    // 0x1290120: stur            x0, [fp, #-0x10]
    // 0x1290124: r0 = of()
    //     0x1290124: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1290128: LoadField: r1 = r0->field_87
    //     0x1290128: ldur            w1, [x0, #0x87]
    // 0x129012c: DecompressPointer r1
    //     0x129012c: add             x1, x1, HEAP, lsl #32
    // 0x1290130: LoadField: r0 = r1->field_2b
    //     0x1290130: ldur            w0, [x1, #0x2b]
    // 0x1290134: DecompressPointer r0
    //     0x1290134: add             x0, x0, HEAP, lsl #32
    // 0x1290138: r16 = Instance_Color
    //     0x1290138: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x129013c: r30 = 16.000000
    //     0x129013c: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1290140: ldr             lr, [lr, #0x188]
    // 0x1290144: stp             lr, x16, [SP]
    // 0x1290148: mov             x1, x0
    // 0x129014c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x129014c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1290150: ldr             x4, [x4, #0x9b8]
    // 0x1290154: r0 = copyWith()
    //     0x1290154: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1290158: stur            x0, [fp, #-0x60]
    // 0x129015c: r0 = TextSpan()
    //     0x129015c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1290160: mov             x3, x0
    // 0x1290164: r0 = "OFF"
    //     0x1290164: add             x0, PP, #0x48, lsl #12  ; [pp+0x48348] "OFF"
    //     0x1290168: ldr             x0, [x0, #0x348]
    // 0x129016c: stur            x3, [fp, #-0x78]
    // 0x1290170: StoreField: r3->field_b = r0
    //     0x1290170: stur            w0, [x3, #0xb]
    // 0x1290174: r0 = Instance__DeferringMouseCursor
    //     0x1290174: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1290178: ArrayStore: r3[0] = r0  ; List_4
    //     0x1290178: stur            w0, [x3, #0x17]
    // 0x129017c: ldur            x1, [fp, #-0x60]
    // 0x1290180: StoreField: r3->field_7 = r1
    //     0x1290180: stur            w1, [x3, #7]
    // 0x1290184: r1 = Null
    //     0x1290184: mov             x1, NULL
    // 0x1290188: r2 = 2
    //     0x1290188: movz            x2, #0x2
    // 0x129018c: r0 = AllocateArray()
    //     0x129018c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1290190: mov             x2, x0
    // 0x1290194: ldur            x0, [fp, #-0x78]
    // 0x1290198: stur            x2, [fp, #-0x60]
    // 0x129019c: StoreField: r2->field_f = r0
    //     0x129019c: stur            w0, [x2, #0xf]
    // 0x12901a0: r1 = <InlineSpan>
    //     0x12901a0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x12901a4: ldr             x1, [x1, #0xe40]
    // 0x12901a8: r0 = AllocateGrowableArray()
    //     0x12901a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12901ac: mov             x1, x0
    // 0x12901b0: ldur            x0, [fp, #-0x60]
    // 0x12901b4: stur            x1, [fp, #-0x78]
    // 0x12901b8: StoreField: r1->field_f = r0
    //     0x12901b8: stur            w0, [x1, #0xf]
    // 0x12901bc: r0 = 2
    //     0x12901bc: movz            x0, #0x2
    // 0x12901c0: StoreField: r1->field_b = r0
    //     0x12901c0: stur            w0, [x1, #0xb]
    // 0x12901c4: r0 = TextSpan()
    //     0x12901c4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x12901c8: mov             x1, x0
    // 0x12901cc: ldur            x0, [fp, #-0x50]
    // 0x12901d0: stur            x1, [fp, #-0x60]
    // 0x12901d4: StoreField: r1->field_b = r0
    //     0x12901d4: stur            w0, [x1, #0xb]
    // 0x12901d8: ldur            x0, [fp, #-0x78]
    // 0x12901dc: StoreField: r1->field_f = r0
    //     0x12901dc: stur            w0, [x1, #0xf]
    // 0x12901e0: r0 = Instance__DeferringMouseCursor
    //     0x12901e0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x12901e4: ArrayStore: r1[0] = r0  ; List_4
    //     0x12901e4: stur            w0, [x1, #0x17]
    // 0x12901e8: ldur            x0, [fp, #-0x10]
    // 0x12901ec: StoreField: r1->field_7 = r0
    //     0x12901ec: stur            w0, [x1, #7]
    // 0x12901f0: r0 = RichText()
    //     0x12901f0: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x12901f4: stur            x0, [fp, #-0x10]
    // 0x12901f8: r16 = Instance_TextAlign
    //     0x12901f8: ldr             x16, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0x12901fc: str             x16, [SP]
    // 0x1290200: mov             x1, x0
    // 0x1290204: ldur            x2, [fp, #-0x60]
    // 0x1290208: r4 = const [0, 0x3, 0x1, 0x2, textAlign, 0x2, null]
    //     0x1290208: add             x4, PP, #0x48, lsl #12  ; [pp+0x48350] List(7) [0, 0x3, 0x1, 0x2, "textAlign", 0x2, Null]
    //     0x129020c: ldr             x4, [x4, #0x350]
    // 0x1290210: r0 = RichText()
    //     0x1290210: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x1290214: r1 = Null
    //     0x1290214: mov             x1, NULL
    // 0x1290218: r2 = 6
    //     0x1290218: movz            x2, #0x6
    // 0x129021c: r0 = AllocateArray()
    //     0x129021c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1290220: mov             x2, x0
    // 0x1290224: ldur            x0, [fp, #-0x48]
    // 0x1290228: stur            x2, [fp, #-0x50]
    // 0x129022c: StoreField: r2->field_f = r0
    //     0x129022c: stur            w0, [x2, #0xf]
    // 0x1290230: r16 = Instance_VerticalDivider
    //     0x1290230: add             x16, PP, #0x48, lsl #12  ; [pp+0x48760] Obj!VerticalDivider@d66b51
    //     0x1290234: ldr             x16, [x16, #0x760]
    // 0x1290238: StoreField: r2->field_13 = r16
    //     0x1290238: stur            w16, [x2, #0x13]
    // 0x129023c: ldur            x0, [fp, #-0x10]
    // 0x1290240: ArrayStore: r2[0] = r0  ; List_4
    //     0x1290240: stur            w0, [x2, #0x17]
    // 0x1290244: r1 = <Widget>
    //     0x1290244: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1290248: r0 = AllocateGrowableArray()
    //     0x1290248: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x129024c: mov             x1, x0
    // 0x1290250: ldur            x0, [fp, #-0x50]
    // 0x1290254: stur            x1, [fp, #-0x10]
    // 0x1290258: StoreField: r1->field_f = r0
    //     0x1290258: stur            w0, [x1, #0xf]
    // 0x129025c: r0 = 6
    //     0x129025c: movz            x0, #0x6
    // 0x1290260: StoreField: r1->field_b = r0
    //     0x1290260: stur            w0, [x1, #0xb]
    // 0x1290264: r0 = Row()
    //     0x1290264: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1290268: mov             x1, x0
    // 0x129026c: r0 = Instance_Axis
    //     0x129026c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1290270: stur            x1, [fp, #-0x48]
    // 0x1290274: StoreField: r1->field_f = r0
    //     0x1290274: stur            w0, [x1, #0xf]
    // 0x1290278: r0 = Instance_MainAxisAlignment
    //     0x1290278: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x129027c: ldr             x0, [x0, #0xa8]
    // 0x1290280: StoreField: r1->field_13 = r0
    //     0x1290280: stur            w0, [x1, #0x13]
    // 0x1290284: r0 = Instance_MainAxisSize
    //     0x1290284: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1290288: ldr             x0, [x0, #0xa10]
    // 0x129028c: ArrayStore: r1[0] = r0  ; List_4
    //     0x129028c: stur            w0, [x1, #0x17]
    // 0x1290290: r2 = Instance_CrossAxisAlignment
    //     0x1290290: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1290294: ldr             x2, [x2, #0xa18]
    // 0x1290298: StoreField: r1->field_1b = r2
    //     0x1290298: stur            w2, [x1, #0x1b]
    // 0x129029c: r2 = Instance_VerticalDirection
    //     0x129029c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x12902a0: ldr             x2, [x2, #0xa20]
    // 0x12902a4: StoreField: r1->field_23 = r2
    //     0x12902a4: stur            w2, [x1, #0x23]
    // 0x12902a8: r3 = Instance_Clip
    //     0x12902a8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12902ac: ldr             x3, [x3, #0x38]
    // 0x12902b0: StoreField: r1->field_2b = r3
    //     0x12902b0: stur            w3, [x1, #0x2b]
    // 0x12902b4: StoreField: r1->field_2f = rZR
    //     0x12902b4: stur            xzr, [x1, #0x2f]
    // 0x12902b8: ldur            x4, [fp, #-0x10]
    // 0x12902bc: StoreField: r1->field_b = r4
    //     0x12902bc: stur            w4, [x1, #0xb]
    // 0x12902c0: r0 = Padding()
    //     0x12902c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x12902c4: mov             x1, x0
    // 0x12902c8: r0 = Instance_EdgeInsets
    //     0x12902c8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48358] Obj!EdgeInsets@d57411
    //     0x12902cc: ldr             x0, [x0, #0x358]
    // 0x12902d0: stur            x1, [fp, #-0x10]
    // 0x12902d4: StoreField: r1->field_f = r0
    //     0x12902d4: stur            w0, [x1, #0xf]
    // 0x12902d8: ldur            x0, [fp, #-0x48]
    // 0x12902dc: StoreField: r1->field_b = r0
    //     0x12902dc: stur            w0, [x1, #0xb]
    // 0x12902e0: r0 = IntrinsicHeight()
    //     0x12902e0: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0x12902e4: mov             x1, x0
    // 0x12902e8: ldur            x0, [fp, #-0x10]
    // 0x12902ec: stur            x1, [fp, #-0x48]
    // 0x12902f0: StoreField: r1->field_b = r0
    //     0x12902f0: stur            w0, [x1, #0xb]
    // 0x12902f4: r0 = Container()
    //     0x12902f4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x12902f8: stur            x0, [fp, #-0x10]
    // 0x12902fc: r16 = 100.000000
    //     0x12902fc: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0x1290300: ldur            lr, [fp, #-0x20]
    // 0x1290304: stp             lr, x16, [SP, #8]
    // 0x1290308: ldur            x16, [fp, #-0x48]
    // 0x129030c: str             x16, [SP]
    // 0x1290310: mov             x1, x0
    // 0x1290314: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0x1290314: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0x1290318: ldr             x4, [x4, #0xc78]
    // 0x129031c: r0 = Container()
    //     0x129031c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1290320: r1 = <Path>
    //     0x1290320: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0x1290324: ldr             x1, [x1, #0xd30]
    // 0x1290328: r0 = MovieTicketClipper()
    //     0x1290328: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0x129032c: stur            x0, [fp, #-0x20]
    // 0x1290330: r0 = ClipPath()
    //     0x1290330: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0x1290334: mov             x1, x0
    // 0x1290338: ldur            x0, [fp, #-0x20]
    // 0x129033c: stur            x1, [fp, #-0x48]
    // 0x1290340: StoreField: r1->field_f = r0
    //     0x1290340: stur            w0, [x1, #0xf]
    // 0x1290344: r0 = Instance_Clip
    //     0x1290344: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x1290348: ldr             x0, [x0, #0x138]
    // 0x129034c: StoreField: r1->field_13 = r0
    //     0x129034c: stur            w0, [x1, #0x13]
    // 0x1290350: ldur            x0, [fp, #-0x10]
    // 0x1290354: StoreField: r1->field_b = r0
    //     0x1290354: stur            w0, [x1, #0xb]
    // 0x1290358: r0 = Visibility()
    //     0x1290358: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x129035c: mov             x2, x0
    // 0x1290360: ldur            x0, [fp, #-0x48]
    // 0x1290364: stur            x2, [fp, #-0x10]
    // 0x1290368: StoreField: r2->field_b = r0
    //     0x1290368: stur            w0, [x2, #0xb]
    // 0x129036c: r0 = Instance_SizedBox
    //     0x129036c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1290370: StoreField: r2->field_f = r0
    //     0x1290370: stur            w0, [x2, #0xf]
    // 0x1290374: ldur            x0, [fp, #-0x28]
    // 0x1290378: StoreField: r2->field_13 = r0
    //     0x1290378: stur            w0, [x2, #0x13]
    // 0x129037c: r0 = false
    //     0x129037c: add             x0, NULL, #0x30  ; false
    // 0x1290380: ArrayStore: r2[0] = r0  ; List_4
    //     0x1290380: stur            w0, [x2, #0x17]
    // 0x1290384: StoreField: r2->field_1b = r0
    //     0x1290384: stur            w0, [x2, #0x1b]
    // 0x1290388: StoreField: r2->field_1f = r0
    //     0x1290388: stur            w0, [x2, #0x1f]
    // 0x129038c: StoreField: r2->field_23 = r0
    //     0x129038c: stur            w0, [x2, #0x23]
    // 0x1290390: StoreField: r2->field_27 = r0
    //     0x1290390: stur            w0, [x2, #0x27]
    // 0x1290394: StoreField: r2->field_2b = r0
    //     0x1290394: stur            w0, [x2, #0x2b]
    // 0x1290398: ldur            x0, [fp, #-0x38]
    // 0x129039c: LoadField: r1 = r0->field_b
    //     0x129039c: ldur            w1, [x0, #0xb]
    // 0x12903a0: LoadField: r3 = r0->field_f
    //     0x12903a0: ldur            w3, [x0, #0xf]
    // 0x12903a4: DecompressPointer r3
    //     0x12903a4: add             x3, x3, HEAP, lsl #32
    // 0x12903a8: LoadField: r4 = r3->field_b
    //     0x12903a8: ldur            w4, [x3, #0xb]
    // 0x12903ac: r3 = LoadInt32Instr(r1)
    //     0x12903ac: sbfx            x3, x1, #1, #0x1f
    // 0x12903b0: stur            x3, [fp, #-0x58]
    // 0x12903b4: r1 = LoadInt32Instr(r4)
    //     0x12903b4: sbfx            x1, x4, #1, #0x1f
    // 0x12903b8: cmp             x3, x1
    // 0x12903bc: b.ne            #0x12903c8
    // 0x12903c0: mov             x1, x0
    // 0x12903c4: r0 = _growToNextCapacity()
    //     0x12903c4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x12903c8: ldur            x5, [fp, #-8]
    // 0x12903cc: ldur            x2, [fp, #-0x38]
    // 0x12903d0: ldur            x6, [fp, #-0x30]
    // 0x12903d4: ldur            x4, [fp, #-0x40]
    // 0x12903d8: ldur            x3, [fp, #-0x58]
    // 0x12903dc: add             x7, x3, #1
    // 0x12903e0: stur            x7, [fp, #-0x68]
    // 0x12903e4: lsl             x0, x7, #1
    // 0x12903e8: StoreField: r2->field_b = r0
    //     0x12903e8: stur            w0, [x2, #0xb]
    // 0x12903ec: LoadField: r8 = r2->field_f
    //     0x12903ec: ldur            w8, [x2, #0xf]
    // 0x12903f0: DecompressPointer r8
    //     0x12903f0: add             x8, x8, HEAP, lsl #32
    // 0x12903f4: mov             x1, x8
    // 0x12903f8: ldur            x0, [fp, #-0x10]
    // 0x12903fc: stur            x8, [fp, #-0x90]
    // 0x1290400: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1290400: add             x25, x1, x3, lsl #2
    //     0x1290404: add             x25, x25, #0xf
    //     0x1290408: str             w0, [x25]
    //     0x129040c: tbz             w0, #0, #0x1290428
    //     0x1290410: ldurb           w16, [x1, #-1]
    //     0x1290414: ldurb           w17, [x0, #-1]
    //     0x1290418: and             x16, x17, x16, lsr #2
    //     0x129041c: tst             x16, HEAP, lsr #32
    //     0x1290420: b.eq            #0x1290428
    //     0x1290424: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1290428: LoadField: r0 = r5->field_b
    //     0x1290428: ldur            w0, [x5, #0xb]
    // 0x129042c: DecompressPointer r0
    //     0x129042c: add             x0, x0, HEAP, lsl #32
    // 0x1290430: stur            x0, [fp, #-0x88]
    // 0x1290434: LoadField: r1 = r5->field_1b
    //     0x1290434: ldur            w1, [x5, #0x1b]
    // 0x1290438: DecompressPointer r1
    //     0x1290438: add             x1, x1, HEAP, lsl #32
    // 0x129043c: stur            x1, [fp, #-0x80]
    // 0x1290440: LoadField: r3 = r5->field_27
    //     0x1290440: ldur            w3, [x5, #0x27]
    // 0x1290444: DecompressPointer r3
    //     0x1290444: add             x3, x3, HEAP, lsl #32
    // 0x1290448: stur            x3, [fp, #-0x78]
    // 0x129044c: LoadField: r9 = r5->field_23
    //     0x129044c: ldur            w9, [x5, #0x23]
    // 0x1290450: DecompressPointer r9
    //     0x1290450: add             x9, x9, HEAP, lsl #32
    // 0x1290454: stur            x9, [fp, #-0x60]
    // 0x1290458: LoadField: r10 = r5->field_2b
    //     0x1290458: ldur            w10, [x5, #0x2b]
    // 0x129045c: DecompressPointer r10
    //     0x129045c: add             x10, x10, HEAP, lsl #32
    // 0x1290460: stur            x10, [fp, #-0x50]
    // 0x1290464: LoadField: r11 = r5->field_3b
    //     0x1290464: ldur            w11, [x5, #0x3b]
    // 0x1290468: DecompressPointer r11
    //     0x1290468: add             x11, x11, HEAP, lsl #32
    // 0x129046c: stur            x11, [fp, #-0x48]
    // 0x1290470: LoadField: r12 = r5->field_43
    //     0x1290470: ldur            w12, [x5, #0x43]
    // 0x1290474: DecompressPointer r12
    //     0x1290474: add             x12, x12, HEAP, lsl #32
    // 0x1290478: stur            x12, [fp, #-0x28]
    // 0x129047c: LoadField: r13 = r5->field_37
    //     0x129047c: ldur            w13, [x5, #0x37]
    // 0x1290480: DecompressPointer r13
    //     0x1290480: add             x13, x13, HEAP, lsl #32
    // 0x1290484: stur            x13, [fp, #-0x20]
    // 0x1290488: LoadField: r14 = r5->field_47
    //     0x1290488: ldur            w14, [x5, #0x47]
    // 0x129048c: DecompressPointer r14
    //     0x129048c: add             x14, x14, HEAP, lsl #32
    // 0x1290490: stur            x14, [fp, #-0x10]
    // 0x1290494: r0 = _ProductGridItem()
    //     0x1290494: bl              #0x12905f4  ; Allocate_ProductGridItemStub -> _ProductGridItem (size=0x3c)
    // 0x1290498: mov             x2, x0
    // 0x129049c: ldur            x0, [fp, #-0x88]
    // 0x12904a0: stur            x2, [fp, #-8]
    // 0x12904a4: StoreField: r2->field_b = r0
    //     0x12904a4: stur            w0, [x2, #0xb]
    // 0x12904a8: ldur            x0, [fp, #-0x80]
    // 0x12904ac: StoreField: r2->field_f = r0
    //     0x12904ac: stur            w0, [x2, #0xf]
    // 0x12904b0: ldur            x0, [fp, #-0x30]
    // 0x12904b4: ArrayStore: r2[0] = r0  ; List_4
    //     0x12904b4: stur            w0, [x2, #0x17]
    // 0x12904b8: ldur            x0, [fp, #-0x78]
    // 0x12904bc: StoreField: r2->field_1b = r0
    //     0x12904bc: stur            w0, [x2, #0x1b]
    // 0x12904c0: ldur            x0, [fp, #-0x60]
    // 0x12904c4: StoreField: r2->field_13 = r0
    //     0x12904c4: stur            w0, [x2, #0x13]
    // 0x12904c8: ldur            x0, [fp, #-0x50]
    // 0x12904cc: StoreField: r2->field_1f = r0
    //     0x12904cc: stur            w0, [x2, #0x1f]
    // 0x12904d0: r0 = "product_page"
    //     0x12904d0: add             x0, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x12904d4: ldr             x0, [x0, #0x480]
    // 0x12904d8: StoreField: r2->field_23 = r0
    //     0x12904d8: stur            w0, [x2, #0x23]
    // 0x12904dc: ldur            x0, [fp, #-0x40]
    // 0x12904e0: StoreField: r2->field_27 = r0
    //     0x12904e0: stur            w0, [x2, #0x27]
    // 0x12904e4: ldur            x0, [fp, #-0x48]
    // 0x12904e8: StoreField: r2->field_2b = r0
    //     0x12904e8: stur            w0, [x2, #0x2b]
    // 0x12904ec: ldur            x0, [fp, #-0x28]
    // 0x12904f0: StoreField: r2->field_2f = r0
    //     0x12904f0: stur            w0, [x2, #0x2f]
    // 0x12904f4: ldur            x0, [fp, #-0x20]
    // 0x12904f8: StoreField: r2->field_33 = r0
    //     0x12904f8: stur            w0, [x2, #0x33]
    // 0x12904fc: ldur            x0, [fp, #-0x10]
    // 0x1290500: StoreField: r2->field_37 = r0
    //     0x1290500: stur            w0, [x2, #0x37]
    // 0x1290504: ldur            x0, [fp, #-0x90]
    // 0x1290508: LoadField: r1 = r0->field_b
    //     0x1290508: ldur            w1, [x0, #0xb]
    // 0x129050c: r0 = LoadInt32Instr(r1)
    //     0x129050c: sbfx            x0, x1, #1, #0x1f
    // 0x1290510: ldur            x3, [fp, #-0x68]
    // 0x1290514: cmp             x3, x0
    // 0x1290518: b.ne            #0x1290524
    // 0x129051c: ldur            x1, [fp, #-0x38]
    // 0x1290520: r0 = _growToNextCapacity()
    //     0x1290520: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1290524: ldur            x3, [fp, #-0x38]
    // 0x1290528: ldur            x2, [fp, #-0x68]
    // 0x129052c: ldur            x4, [fp, #-0x18]
    // 0x1290530: add             x0, x2, #1
    // 0x1290534: lsl             x1, x0, #1
    // 0x1290538: StoreField: r3->field_b = r1
    //     0x1290538: stur            w1, [x3, #0xb]
    // 0x129053c: LoadField: r1 = r3->field_f
    //     0x129053c: ldur            w1, [x3, #0xf]
    // 0x1290540: DecompressPointer r1
    //     0x1290540: add             x1, x1, HEAP, lsl #32
    // 0x1290544: ldur            x0, [fp, #-8]
    // 0x1290548: ArrayStore: r1[r2] = r0  ; List_4
    //     0x1290548: add             x25, x1, x2, lsl #2
    //     0x129054c: add             x25, x25, #0xf
    //     0x1290550: str             w0, [x25]
    //     0x1290554: tbz             w0, #0, #0x1290570
    //     0x1290558: ldurb           w16, [x1, #-1]
    //     0x129055c: ldurb           w17, [x0, #-1]
    //     0x1290560: and             x16, x17, x16, lsr #2
    //     0x1290564: tst             x16, HEAP, lsr #32
    //     0x1290568: b.eq            #0x1290570
    //     0x129056c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1290570: r0 = Column()
    //     0x1290570: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1290574: mov             x1, x0
    // 0x1290578: r0 = Instance_Axis
    //     0x1290578: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x129057c: stur            x1, [fp, #-8]
    // 0x1290580: StoreField: r1->field_f = r0
    //     0x1290580: stur            w0, [x1, #0xf]
    // 0x1290584: r0 = Instance_MainAxisAlignment
    //     0x1290584: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x1290588: ldr             x0, [x0, #0xab0]
    // 0x129058c: StoreField: r1->field_13 = r0
    //     0x129058c: stur            w0, [x1, #0x13]
    // 0x1290590: r0 = Instance_MainAxisSize
    //     0x1290590: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1290594: ldr             x0, [x0, #0xa10]
    // 0x1290598: ArrayStore: r1[0] = r0  ; List_4
    //     0x1290598: stur            w0, [x1, #0x17]
    // 0x129059c: ldur            x0, [fp, #-0x18]
    // 0x12905a0: StoreField: r1->field_1b = r0
    //     0x12905a0: stur            w0, [x1, #0x1b]
    // 0x12905a4: r0 = Instance_VerticalDirection
    //     0x12905a4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x12905a8: ldr             x0, [x0, #0xa20]
    // 0x12905ac: StoreField: r1->field_23 = r0
    //     0x12905ac: stur            w0, [x1, #0x23]
    // 0x12905b0: r0 = Instance_Clip
    //     0x12905b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12905b4: ldr             x0, [x0, #0x38]
    // 0x12905b8: StoreField: r1->field_2b = r0
    //     0x12905b8: stur            w0, [x1, #0x2b]
    // 0x12905bc: StoreField: r1->field_2f = rZR
    //     0x12905bc: stur            xzr, [x1, #0x2f]
    // 0x12905c0: ldur            x0, [fp, #-0x38]
    // 0x12905c4: StoreField: r1->field_b = r0
    //     0x12905c4: stur            w0, [x1, #0xb]
    // 0x12905c8: r0 = Padding()
    //     0x12905c8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x12905cc: r1 = Instance_EdgeInsets
    //     0x12905cc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3edd8] Obj!EdgeInsets@d58f41
    //     0x12905d0: ldr             x1, [x1, #0xdd8]
    // 0x12905d4: StoreField: r0->field_f = r1
    //     0x12905d4: stur            w1, [x0, #0xf]
    // 0x12905d8: ldur            x1, [fp, #-8]
    // 0x12905dc: StoreField: r0->field_b = r1
    //     0x12905dc: stur            w1, [x0, #0xb]
    // 0x12905e0: LeaveFrame
    //     0x12905e0: mov             SP, fp
    //     0x12905e4: ldp             fp, lr, [SP], #0x10
    // 0x12905e8: ret
    //     0x12905e8: ret             
    // 0x12905ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12905ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12905f0: b               #0x128f6b8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1290600, size: 0xc0
    // 0x1290600: EnterFrame
    //     0x1290600: stp             fp, lr, [SP, #-0x10]!
    //     0x1290604: mov             fp, SP
    // 0x1290608: AllocStack(0x38)
    //     0x1290608: sub             SP, SP, #0x38
    // 0x129060c: SetupParameters()
    //     0x129060c: ldr             x0, [fp, #0x10]
    //     0x1290610: ldur            w1, [x0, #0x17]
    //     0x1290614: add             x1, x1, HEAP, lsl #32
    // 0x1290618: CheckStackOverflow
    //     0x1290618: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x129061c: cmp             SP, x16
    //     0x1290620: b.ls            #0x12906b8
    // 0x1290624: LoadField: r0 = r1->field_f
    //     0x1290624: ldur            w0, [x1, #0xf]
    // 0x1290628: DecompressPointer r0
    //     0x1290628: add             x0, x0, HEAP, lsl #32
    // 0x129062c: LoadField: r1 = r0->field_27
    //     0x129062c: ldur            w1, [x0, #0x27]
    // 0x1290630: DecompressPointer r1
    //     0x1290630: add             x1, x1, HEAP, lsl #32
    // 0x1290634: LoadField: r2 = r0->field_23
    //     0x1290634: ldur            w2, [x0, #0x23]
    // 0x1290638: DecompressPointer r2
    //     0x1290638: add             x2, x2, HEAP, lsl #32
    // 0x129063c: LoadField: r3 = r0->field_2b
    //     0x129063c: ldur            w3, [x0, #0x2b]
    // 0x1290640: DecompressPointer r3
    //     0x1290640: add             x3, x3, HEAP, lsl #32
    // 0x1290644: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x1290644: ldur            w4, [x0, #0x17]
    // 0x1290648: DecompressPointer r4
    //     0x1290648: add             x4, x4, HEAP, lsl #32
    // 0x129064c: cmp             w4, NULL
    // 0x1290650: b.ne            #0x129065c
    // 0x1290654: r4 = Null
    //     0x1290654: mov             x4, NULL
    // 0x1290658: b               #0x1290668
    // 0x129065c: LoadField: r5 = r4->field_b
    //     0x129065c: ldur            w5, [x4, #0xb]
    // 0x1290660: DecompressPointer r5
    //     0x1290660: add             x5, x5, HEAP, lsl #32
    // 0x1290664: mov             x4, x5
    // 0x1290668: LoadField: r5 = r0->field_f
    //     0x1290668: ldur            w5, [x0, #0xf]
    // 0x129066c: DecompressPointer r5
    //     0x129066c: add             x5, x5, HEAP, lsl #32
    // 0x1290670: LoadField: r6 = r0->field_3f
    //     0x1290670: ldur            w6, [x0, #0x3f]
    // 0x1290674: DecompressPointer r6
    //     0x1290674: add             x6, x6, HEAP, lsl #32
    // 0x1290678: stp             x1, x6, [SP, #0x28]
    // 0x129067c: r16 = "product_page"
    //     0x129067c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x1290680: ldr             x16, [x16, #0x480]
    // 0x1290684: stp             x16, x2, [SP, #0x18]
    // 0x1290688: stp             x4, x3, [SP, #8]
    // 0x129068c: str             x5, [SP]
    // 0x1290690: r4 = 0
    //     0x1290690: movz            x4, #0
    // 0x1290694: ldr             x0, [SP, #0x30]
    // 0x1290698: r16 = UnlinkedCall_0x613b5c
    //     0x1290698: add             x16, PP, #0x48, lsl #12  ; [pp+0x48b28] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x129069c: add             x16, x16, #0xb28
    // 0x12906a0: ldp             x5, lr, [x16]
    // 0x12906a4: blr             lr
    // 0x12906a8: r0 = Null
    //     0x12906a8: mov             x0, NULL
    // 0x12906ac: LeaveFrame
    //     0x12906ac: mov             SP, fp
    //     0x12906b0: ldp             fp, lr, [SP], #0x10
    // 0x12906b4: ret
    //     0x12906b4: ret             
    // 0x12906b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12906b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12906bc: b               #0x1290624
  }
}
