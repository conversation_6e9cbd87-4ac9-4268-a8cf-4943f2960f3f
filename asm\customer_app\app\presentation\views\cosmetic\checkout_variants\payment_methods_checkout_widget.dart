// lib: , url: package:customer_app/app/presentation/views/cosmetic/checkout_variants/payment_methods_checkout_widget.dart

// class id: 1049236, size: 0x8
class :: {
}

// class id: 4607, size: 0x14, field offset: 0x14
//   const constructor, 
class PaymentMethodsCheckoutWidget extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x134cc78, size: 0x64
    // 0x134cc78: EnterFrame
    //     0x134cc78: stp             fp, lr, [SP, #-0x10]!
    //     0x134cc7c: mov             fp, SP
    // 0x134cc80: AllocStack(0x18)
    //     0x134cc80: sub             SP, SP, #0x18
    // 0x134cc84: SetupParameters(PaymentMethodsCheckoutWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x134cc84: stur            x1, [fp, #-8]
    //     0x134cc88: stur            x2, [fp, #-0x10]
    // 0x134cc8c: r1 = 2
    //     0x134cc8c: movz            x1, #0x2
    // 0x134cc90: r0 = AllocateContext()
    //     0x134cc90: bl              #0x16f6108  ; AllocateContextStub
    // 0x134cc94: mov             x1, x0
    // 0x134cc98: ldur            x0, [fp, #-8]
    // 0x134cc9c: stur            x1, [fp, #-0x18]
    // 0x134cca0: StoreField: r1->field_f = r0
    //     0x134cca0: stur            w0, [x1, #0xf]
    // 0x134cca4: ldur            x0, [fp, #-0x10]
    // 0x134cca8: StoreField: r1->field_13 = r0
    //     0x134cca8: stur            w0, [x1, #0x13]
    // 0x134ccac: r0 = Obx()
    //     0x134ccac: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x134ccb0: ldur            x2, [fp, #-0x18]
    // 0x134ccb4: r1 = Function '<anonymous closure>':.
    //     0x134ccb4: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c28] AnonymousClosure: (0x134ccdc), in [package:customer_app/app/presentation/views/cosmetic/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::bottomNavigationBar (0x134cc78)
    //     0x134ccb8: ldr             x1, [x1, #0xc28]
    // 0x134ccbc: stur            x0, [fp, #-8]
    // 0x134ccc0: r0 = AllocateClosure()
    //     0x134ccc0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x134ccc4: mov             x1, x0
    // 0x134ccc8: ldur            x0, [fp, #-8]
    // 0x134cccc: StoreField: r0->field_b = r1
    //     0x134cccc: stur            w1, [x0, #0xb]
    // 0x134ccd0: LeaveFrame
    //     0x134ccd0: mov             SP, fp
    //     0x134ccd4: ldp             fp, lr, [SP], #0x10
    // 0x134ccd8: ret
    //     0x134ccd8: ret             
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x134ccdc, size: 0x644
    // 0x134ccdc: EnterFrame
    //     0x134ccdc: stp             fp, lr, [SP, #-0x10]!
    //     0x134cce0: mov             fp, SP
    // 0x134cce4: AllocStack(0x50)
    //     0x134cce4: sub             SP, SP, #0x50
    // 0x134cce8: SetupParameters()
    //     0x134cce8: ldr             x0, [fp, #0x10]
    //     0x134ccec: ldur            w2, [x0, #0x17]
    //     0x134ccf0: add             x2, x2, HEAP, lsl #32
    //     0x134ccf4: stur            x2, [fp, #-8]
    // 0x134ccf8: CheckStackOverflow
    //     0x134ccf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x134ccfc: cmp             SP, x16
    //     0x134cd00: b.ls            #0x134d308
    // 0x134cd04: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x134cd04: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x134cd08: ldr             x0, [x0, #0x1c80]
    //     0x134cd0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x134cd10: cmp             w0, w16
    //     0x134cd14: b.ne            #0x134cd20
    //     0x134cd18: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x134cd1c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x134cd20: r0 = GetNavigation.size()
    //     0x134cd20: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x134cd24: LoadField: d0 = r0->field_f
    //     0x134cd24: ldur            d0, [x0, #0xf]
    // 0x134cd28: d1 = 0.120000
    //     0x134cd28: ldr             d1, [PP, #0x54a8]  ; [pp+0x54a8] IMM: double(0.12) from 0x3fbeb851eb851eb8
    // 0x134cd2c: fmul            d2, d0, d1
    // 0x134cd30: stur            d2, [fp, #-0x40]
    // 0x134cd34: r1 = _ConstMap len:11
    //     0x134cd34: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c28] Map<int, List<BoxShadow>>(11)
    //     0x134cd38: ldr             x1, [x1, #0xc28]
    // 0x134cd3c: r2 = 8
    //     0x134cd3c: movz            x2, #0x8
    // 0x134cd40: r0 = []()
    //     0x134cd40: bl              #0x16a3e7c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x134cd44: stur            x0, [fp, #-0x10]
    // 0x134cd48: r0 = BoxDecoration()
    //     0x134cd48: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x134cd4c: mov             x2, x0
    // 0x134cd50: r0 = Instance_Color
    //     0x134cd50: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x134cd54: stur            x2, [fp, #-0x18]
    // 0x134cd58: StoreField: r2->field_7 = r0
    //     0x134cd58: stur            w0, [x2, #7]
    // 0x134cd5c: ldur            x0, [fp, #-0x10]
    // 0x134cd60: ArrayStore: r2[0] = r0  ; List_4
    //     0x134cd60: stur            w0, [x2, #0x17]
    // 0x134cd64: r0 = Instance_BoxShape
    //     0x134cd64: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x134cd68: ldr             x0, [x0, #0x80]
    // 0x134cd6c: StoreField: r2->field_23 = r0
    //     0x134cd6c: stur            w0, [x2, #0x23]
    // 0x134cd70: ldur            x0, [fp, #-8]
    // 0x134cd74: LoadField: r1 = r0->field_f
    //     0x134cd74: ldur            w1, [x0, #0xf]
    // 0x134cd78: DecompressPointer r1
    //     0x134cd78: add             x1, x1, HEAP, lsl #32
    // 0x134cd7c: r0 = controller()
    //     0x134cd7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x134cd80: LoadField: r1 = r0->field_87
    //     0x134cd80: ldur            w1, [x0, #0x87]
    // 0x134cd84: DecompressPointer r1
    //     0x134cd84: add             x1, x1, HEAP, lsl #32
    // 0x134cd88: r0 = value()
    //     0x134cd88: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x134cd8c: LoadField: r1 = r0->field_f
    //     0x134cd8c: ldur            w1, [x0, #0xf]
    // 0x134cd90: DecompressPointer r1
    //     0x134cd90: add             x1, x1, HEAP, lsl #32
    // 0x134cd94: cmp             w1, NULL
    // 0x134cd98: b.ne            #0x134cda4
    // 0x134cd9c: r0 = ""
    //     0x134cd9c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x134cda0: b               #0x134cda8
    // 0x134cda4: mov             x0, x1
    // 0x134cda8: ldur            x2, [fp, #-8]
    // 0x134cdac: stur            x0, [fp, #-0x10]
    // 0x134cdb0: LoadField: r1 = r2->field_13
    //     0x134cdb0: ldur            w1, [x2, #0x13]
    // 0x134cdb4: DecompressPointer r1
    //     0x134cdb4: add             x1, x1, HEAP, lsl #32
    // 0x134cdb8: r0 = of()
    //     0x134cdb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x134cdbc: LoadField: r1 = r0->field_87
    //     0x134cdbc: ldur            w1, [x0, #0x87]
    // 0x134cdc0: DecompressPointer r1
    //     0x134cdc0: add             x1, x1, HEAP, lsl #32
    // 0x134cdc4: LoadField: r0 = r1->field_2b
    //     0x134cdc4: ldur            w0, [x1, #0x2b]
    // 0x134cdc8: DecompressPointer r0
    //     0x134cdc8: add             x0, x0, HEAP, lsl #32
    // 0x134cdcc: ldur            x2, [fp, #-8]
    // 0x134cdd0: stur            x0, [fp, #-0x20]
    // 0x134cdd4: LoadField: r1 = r2->field_13
    //     0x134cdd4: ldur            w1, [x2, #0x13]
    // 0x134cdd8: DecompressPointer r1
    //     0x134cdd8: add             x1, x1, HEAP, lsl #32
    // 0x134cddc: r0 = of()
    //     0x134cddc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x134cde0: LoadField: r1 = r0->field_5b
    //     0x134cde0: ldur            w1, [x0, #0x5b]
    // 0x134cde4: DecompressPointer r1
    //     0x134cde4: add             x1, x1, HEAP, lsl #32
    // 0x134cde8: r0 = LoadClassIdInstr(r1)
    //     0x134cde8: ldur            x0, [x1, #-1]
    //     0x134cdec: ubfx            x0, x0, #0xc, #0x14
    // 0x134cdf0: d0 = 0.700000
    //     0x134cdf0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x134cdf4: ldr             d0, [x17, #0xf48]
    // 0x134cdf8: r0 = GDT[cid_x0 + -0xffa]()
    //     0x134cdf8: sub             lr, x0, #0xffa
    //     0x134cdfc: ldr             lr, [x21, lr, lsl #3]
    //     0x134ce00: blr             lr
    // 0x134ce04: r16 = 12.000000
    //     0x134ce04: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x134ce08: ldr             x16, [x16, #0x9e8]
    // 0x134ce0c: stp             x0, x16, [SP]
    // 0x134ce10: ldur            x1, [fp, #-0x20]
    // 0x134ce14: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x134ce14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x134ce18: ldr             x4, [x4, #0xaa0]
    // 0x134ce1c: r0 = copyWith()
    //     0x134ce1c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x134ce20: stur            x0, [fp, #-0x20]
    // 0x134ce24: r0 = Text()
    //     0x134ce24: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x134ce28: mov             x2, x0
    // 0x134ce2c: ldur            x0, [fp, #-0x10]
    // 0x134ce30: stur            x2, [fp, #-0x28]
    // 0x134ce34: StoreField: r2->field_b = r0
    //     0x134ce34: stur            w0, [x2, #0xb]
    // 0x134ce38: ldur            x0, [fp, #-0x20]
    // 0x134ce3c: StoreField: r2->field_13 = r0
    //     0x134ce3c: stur            w0, [x2, #0x13]
    // 0x134ce40: ldur            x0, [fp, #-8]
    // 0x134ce44: LoadField: r1 = r0->field_f
    //     0x134ce44: ldur            w1, [x0, #0xf]
    // 0x134ce48: DecompressPointer r1
    //     0x134ce48: add             x1, x1, HEAP, lsl #32
    // 0x134ce4c: r0 = controller()
    //     0x134ce4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x134ce50: LoadField: r1 = r0->field_87
    //     0x134ce50: ldur            w1, [x0, #0x87]
    // 0x134ce54: DecompressPointer r1
    //     0x134ce54: add             x1, x1, HEAP, lsl #32
    // 0x134ce58: r0 = value()
    //     0x134ce58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x134ce5c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x134ce5c: ldur            w1, [x0, #0x17]
    // 0x134ce60: DecompressPointer r1
    //     0x134ce60: add             x1, x1, HEAP, lsl #32
    // 0x134ce64: cmp             w1, NULL
    // 0x134ce68: b.ne            #0x134ce74
    // 0x134ce6c: r3 = ""
    //     0x134ce6c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x134ce70: b               #0x134ce78
    // 0x134ce74: mov             x3, x1
    // 0x134ce78: ldur            x2, [fp, #-8]
    // 0x134ce7c: ldur            d0, [fp, #-0x40]
    // 0x134ce80: ldur            x0, [fp, #-0x28]
    // 0x134ce84: stur            x3, [fp, #-0x10]
    // 0x134ce88: LoadField: r1 = r2->field_13
    //     0x134ce88: ldur            w1, [x2, #0x13]
    // 0x134ce8c: DecompressPointer r1
    //     0x134ce8c: add             x1, x1, HEAP, lsl #32
    // 0x134ce90: r0 = of()
    //     0x134ce90: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x134ce94: LoadField: r1 = r0->field_87
    //     0x134ce94: ldur            w1, [x0, #0x87]
    // 0x134ce98: DecompressPointer r1
    //     0x134ce98: add             x1, x1, HEAP, lsl #32
    // 0x134ce9c: LoadField: r0 = r1->field_7
    //     0x134ce9c: ldur            w0, [x1, #7]
    // 0x134cea0: DecompressPointer r0
    //     0x134cea0: add             x0, x0, HEAP, lsl #32
    // 0x134cea4: ldur            x2, [fp, #-8]
    // 0x134cea8: stur            x0, [fp, #-0x20]
    // 0x134ceac: LoadField: r1 = r2->field_13
    //     0x134ceac: ldur            w1, [x2, #0x13]
    // 0x134ceb0: DecompressPointer r1
    //     0x134ceb0: add             x1, x1, HEAP, lsl #32
    // 0x134ceb4: r0 = of()
    //     0x134ceb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x134ceb8: LoadField: r1 = r0->field_5b
    //     0x134ceb8: ldur            w1, [x0, #0x5b]
    // 0x134cebc: DecompressPointer r1
    //     0x134cebc: add             x1, x1, HEAP, lsl #32
    // 0x134cec0: r16 = 16.000000
    //     0x134cec0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x134cec4: ldr             x16, [x16, #0x188]
    // 0x134cec8: stp             x1, x16, [SP]
    // 0x134cecc: ldur            x1, [fp, #-0x20]
    // 0x134ced0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x134ced0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x134ced4: ldr             x4, [x4, #0xaa0]
    // 0x134ced8: r0 = copyWith()
    //     0x134ced8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x134cedc: stur            x0, [fp, #-0x20]
    // 0x134cee0: r0 = Text()
    //     0x134cee0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x134cee4: mov             x1, x0
    // 0x134cee8: ldur            x0, [fp, #-0x10]
    // 0x134ceec: stur            x1, [fp, #-0x30]
    // 0x134cef0: StoreField: r1->field_b = r0
    //     0x134cef0: stur            w0, [x1, #0xb]
    // 0x134cef4: ldur            x0, [fp, #-0x20]
    // 0x134cef8: StoreField: r1->field_13 = r0
    //     0x134cef8: stur            w0, [x1, #0x13]
    // 0x134cefc: r0 = Padding()
    //     0x134cefc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x134cf00: mov             x3, x0
    // 0x134cf04: r0 = Instance_EdgeInsets
    //     0x134cf04: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0x134cf08: ldr             x0, [x0, #0x668]
    // 0x134cf0c: stur            x3, [fp, #-0x10]
    // 0x134cf10: StoreField: r3->field_f = r0
    //     0x134cf10: stur            w0, [x3, #0xf]
    // 0x134cf14: ldur            x0, [fp, #-0x30]
    // 0x134cf18: StoreField: r3->field_b = r0
    //     0x134cf18: stur            w0, [x3, #0xb]
    // 0x134cf1c: r1 = Null
    //     0x134cf1c: mov             x1, NULL
    // 0x134cf20: r2 = 2
    //     0x134cf20: movz            x2, #0x2
    // 0x134cf24: r0 = AllocateArray()
    //     0x134cf24: bl              #0x16f7198  ; AllocateArrayStub
    // 0x134cf28: mov             x2, x0
    // 0x134cf2c: ldur            x0, [fp, #-0x10]
    // 0x134cf30: stur            x2, [fp, #-0x20]
    // 0x134cf34: StoreField: r2->field_f = r0
    //     0x134cf34: stur            w0, [x2, #0xf]
    // 0x134cf38: r1 = <Widget>
    //     0x134cf38: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x134cf3c: r0 = AllocateGrowableArray()
    //     0x134cf3c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x134cf40: mov             x1, x0
    // 0x134cf44: ldur            x0, [fp, #-0x20]
    // 0x134cf48: stur            x1, [fp, #-0x10]
    // 0x134cf4c: StoreField: r1->field_f = r0
    //     0x134cf4c: stur            w0, [x1, #0xf]
    // 0x134cf50: r0 = 2
    //     0x134cf50: movz            x0, #0x2
    // 0x134cf54: StoreField: r1->field_b = r0
    //     0x134cf54: stur            w0, [x1, #0xb]
    // 0x134cf58: r0 = Row()
    //     0x134cf58: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x134cf5c: mov             x3, x0
    // 0x134cf60: r0 = Instance_Axis
    //     0x134cf60: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x134cf64: stur            x3, [fp, #-0x20]
    // 0x134cf68: StoreField: r3->field_f = r0
    //     0x134cf68: stur            w0, [x3, #0xf]
    // 0x134cf6c: r4 = Instance_MainAxisAlignment
    //     0x134cf6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x134cf70: ldr             x4, [x4, #0xa08]
    // 0x134cf74: StoreField: r3->field_13 = r4
    //     0x134cf74: stur            w4, [x3, #0x13]
    // 0x134cf78: r5 = Instance_MainAxisSize
    //     0x134cf78: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x134cf7c: ldr             x5, [x5, #0xa10]
    // 0x134cf80: ArrayStore: r3[0] = r5  ; List_4
    //     0x134cf80: stur            w5, [x3, #0x17]
    // 0x134cf84: r6 = Instance_CrossAxisAlignment
    //     0x134cf84: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x134cf88: ldr             x6, [x6, #0xa18]
    // 0x134cf8c: StoreField: r3->field_1b = r6
    //     0x134cf8c: stur            w6, [x3, #0x1b]
    // 0x134cf90: r7 = Instance_VerticalDirection
    //     0x134cf90: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x134cf94: ldr             x7, [x7, #0xa20]
    // 0x134cf98: StoreField: r3->field_23 = r7
    //     0x134cf98: stur            w7, [x3, #0x23]
    // 0x134cf9c: r8 = Instance_Clip
    //     0x134cf9c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x134cfa0: ldr             x8, [x8, #0x38]
    // 0x134cfa4: StoreField: r3->field_2b = r8
    //     0x134cfa4: stur            w8, [x3, #0x2b]
    // 0x134cfa8: StoreField: r3->field_2f = rZR
    //     0x134cfa8: stur            xzr, [x3, #0x2f]
    // 0x134cfac: ldur            x1, [fp, #-0x10]
    // 0x134cfb0: StoreField: r3->field_b = r1
    //     0x134cfb0: stur            w1, [x3, #0xb]
    // 0x134cfb4: r1 = Null
    //     0x134cfb4: mov             x1, NULL
    // 0x134cfb8: r2 = 4
    //     0x134cfb8: movz            x2, #0x4
    // 0x134cfbc: r0 = AllocateArray()
    //     0x134cfbc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x134cfc0: mov             x2, x0
    // 0x134cfc4: ldur            x0, [fp, #-0x28]
    // 0x134cfc8: stur            x2, [fp, #-0x10]
    // 0x134cfcc: StoreField: r2->field_f = r0
    //     0x134cfcc: stur            w0, [x2, #0xf]
    // 0x134cfd0: ldur            x0, [fp, #-0x20]
    // 0x134cfd4: StoreField: r2->field_13 = r0
    //     0x134cfd4: stur            w0, [x2, #0x13]
    // 0x134cfd8: r1 = <Widget>
    //     0x134cfd8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x134cfdc: r0 = AllocateGrowableArray()
    //     0x134cfdc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x134cfe0: mov             x1, x0
    // 0x134cfe4: ldur            x0, [fp, #-0x10]
    // 0x134cfe8: stur            x1, [fp, #-0x20]
    // 0x134cfec: StoreField: r1->field_f = r0
    //     0x134cfec: stur            w0, [x1, #0xf]
    // 0x134cff0: r0 = 4
    //     0x134cff0: movz            x0, #0x4
    // 0x134cff4: StoreField: r1->field_b = r0
    //     0x134cff4: stur            w0, [x1, #0xb]
    // 0x134cff8: r0 = Column()
    //     0x134cff8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x134cffc: mov             x1, x0
    // 0x134d000: r0 = Instance_Axis
    //     0x134d000: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x134d004: stur            x1, [fp, #-0x10]
    // 0x134d008: StoreField: r1->field_f = r0
    //     0x134d008: stur            w0, [x1, #0xf]
    // 0x134d00c: r0 = Instance_MainAxisAlignment
    //     0x134d00c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x134d010: ldr             x0, [x0, #0xa08]
    // 0x134d014: StoreField: r1->field_13 = r0
    //     0x134d014: stur            w0, [x1, #0x13]
    // 0x134d018: r2 = Instance_MainAxisSize
    //     0x134d018: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x134d01c: ldr             x2, [x2, #0xa10]
    // 0x134d020: ArrayStore: r1[0] = r2  ; List_4
    //     0x134d020: stur            w2, [x1, #0x17]
    // 0x134d024: r3 = Instance_CrossAxisAlignment
    //     0x134d024: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x134d028: ldr             x3, [x3, #0x890]
    // 0x134d02c: StoreField: r1->field_1b = r3
    //     0x134d02c: stur            w3, [x1, #0x1b]
    // 0x134d030: r3 = Instance_VerticalDirection
    //     0x134d030: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x134d034: ldr             x3, [x3, #0xa20]
    // 0x134d038: StoreField: r1->field_23 = r3
    //     0x134d038: stur            w3, [x1, #0x23]
    // 0x134d03c: r4 = Instance_Clip
    //     0x134d03c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x134d040: ldr             x4, [x4, #0x38]
    // 0x134d044: StoreField: r1->field_2b = r4
    //     0x134d044: stur            w4, [x1, #0x2b]
    // 0x134d048: StoreField: r1->field_2f = rZR
    //     0x134d048: stur            xzr, [x1, #0x2f]
    // 0x134d04c: ldur            x5, [fp, #-0x20]
    // 0x134d050: StoreField: r1->field_b = r5
    //     0x134d050: stur            w5, [x1, #0xb]
    // 0x134d054: r16 = <EdgeInsets>
    //     0x134d054: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x134d058: ldr             x16, [x16, #0xda0]
    // 0x134d05c: r30 = Instance_EdgeInsets
    //     0x134d05c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x134d060: ldr             lr, [lr, #0x1f0]
    // 0x134d064: stp             lr, x16, [SP]
    // 0x134d068: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x134d068: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x134d06c: r0 = all()
    //     0x134d06c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x134d070: ldur            x2, [fp, #-8]
    // 0x134d074: stur            x0, [fp, #-0x20]
    // 0x134d078: LoadField: r1 = r2->field_13
    //     0x134d078: ldur            w1, [x2, #0x13]
    // 0x134d07c: DecompressPointer r1
    //     0x134d07c: add             x1, x1, HEAP, lsl #32
    // 0x134d080: r0 = of()
    //     0x134d080: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x134d084: LoadField: r1 = r0->field_5b
    //     0x134d084: ldur            w1, [x0, #0x5b]
    // 0x134d088: DecompressPointer r1
    //     0x134d088: add             x1, x1, HEAP, lsl #32
    // 0x134d08c: r16 = <Color>
    //     0x134d08c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x134d090: ldr             x16, [x16, #0xf80]
    // 0x134d094: stp             x1, x16, [SP]
    // 0x134d098: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x134d098: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x134d09c: r0 = all()
    //     0x134d09c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x134d0a0: stur            x0, [fp, #-0x28]
    // 0x134d0a4: r16 = <RoundedRectangleBorder>
    //     0x134d0a4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x134d0a8: ldr             x16, [x16, #0xf78]
    // 0x134d0ac: r30 = Instance_RoundedRectangleBorder
    //     0x134d0ac: add             lr, PP, #0x40, lsl #12  ; [pp+0x409a8] Obj!RoundedRectangleBorder@d5acb1
    //     0x134d0b0: ldr             lr, [lr, #0x9a8]
    // 0x134d0b4: stp             lr, x16, [SP]
    // 0x134d0b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x134d0b8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x134d0bc: r0 = all()
    //     0x134d0bc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x134d0c0: stur            x0, [fp, #-0x30]
    // 0x134d0c4: r0 = ButtonStyle()
    //     0x134d0c4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x134d0c8: mov             x1, x0
    // 0x134d0cc: ldur            x0, [fp, #-0x28]
    // 0x134d0d0: stur            x1, [fp, #-0x38]
    // 0x134d0d4: StoreField: r1->field_b = r0
    //     0x134d0d4: stur            w0, [x1, #0xb]
    // 0x134d0d8: ldur            x0, [fp, #-0x20]
    // 0x134d0dc: StoreField: r1->field_23 = r0
    //     0x134d0dc: stur            w0, [x1, #0x23]
    // 0x134d0e0: ldur            x0, [fp, #-0x30]
    // 0x134d0e4: StoreField: r1->field_43 = r0
    //     0x134d0e4: stur            w0, [x1, #0x43]
    // 0x134d0e8: r0 = TextButtonThemeData()
    //     0x134d0e8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x134d0ec: mov             x2, x0
    // 0x134d0f0: ldur            x0, [fp, #-0x38]
    // 0x134d0f4: stur            x2, [fp, #-0x20]
    // 0x134d0f8: StoreField: r2->field_7 = r0
    //     0x134d0f8: stur            w0, [x2, #7]
    // 0x134d0fc: ldur            x0, [fp, #-8]
    // 0x134d100: LoadField: r1 = r0->field_13
    //     0x134d100: ldur            w1, [x0, #0x13]
    // 0x134d104: DecompressPointer r1
    //     0x134d104: add             x1, x1, HEAP, lsl #32
    // 0x134d108: r0 = of()
    //     0x134d108: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x134d10c: LoadField: r1 = r0->field_87
    //     0x134d10c: ldur            w1, [x0, #0x87]
    // 0x134d110: DecompressPointer r1
    //     0x134d110: add             x1, x1, HEAP, lsl #32
    // 0x134d114: LoadField: r0 = r1->field_7
    //     0x134d114: ldur            w0, [x1, #7]
    // 0x134d118: DecompressPointer r0
    //     0x134d118: add             x0, x0, HEAP, lsl #32
    // 0x134d11c: r16 = Instance_Color
    //     0x134d11c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x134d120: r30 = 14.000000
    //     0x134d120: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x134d124: ldr             lr, [lr, #0x1d8]
    // 0x134d128: stp             lr, x16, [SP]
    // 0x134d12c: mov             x1, x0
    // 0x134d130: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x134d130: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x134d134: ldr             x4, [x4, #0x9b8]
    // 0x134d138: r0 = copyWith()
    //     0x134d138: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x134d13c: stur            x0, [fp, #-0x28]
    // 0x134d140: r0 = Text()
    //     0x134d140: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x134d144: mov             x3, x0
    // 0x134d148: r0 = "Place Order"
    //     0x134d148: add             x0, PP, #0x40, lsl #12  ; [pp+0x40d90] "Place Order"
    //     0x134d14c: ldr             x0, [x0, #0xd90]
    // 0x134d150: stur            x3, [fp, #-0x30]
    // 0x134d154: StoreField: r3->field_b = r0
    //     0x134d154: stur            w0, [x3, #0xb]
    // 0x134d158: ldur            x0, [fp, #-0x28]
    // 0x134d15c: StoreField: r3->field_13 = r0
    //     0x134d15c: stur            w0, [x3, #0x13]
    // 0x134d160: ldur            x2, [fp, #-8]
    // 0x134d164: r1 = Function '<anonymous closure>':.
    //     0x134d164: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c30] AnonymousClosure: (0x130534c), in [package:customer_app/app/presentation/views/glass/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::bottomNavigationBar (0x13601b8)
    //     0x134d168: ldr             x1, [x1, #0xc30]
    // 0x134d16c: r0 = AllocateClosure()
    //     0x134d16c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x134d170: stur            x0, [fp, #-8]
    // 0x134d174: r0 = TextButton()
    //     0x134d174: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x134d178: mov             x1, x0
    // 0x134d17c: ldur            x0, [fp, #-8]
    // 0x134d180: stur            x1, [fp, #-0x28]
    // 0x134d184: StoreField: r1->field_b = r0
    //     0x134d184: stur            w0, [x1, #0xb]
    // 0x134d188: r0 = false
    //     0x134d188: add             x0, NULL, #0x30  ; false
    // 0x134d18c: StoreField: r1->field_27 = r0
    //     0x134d18c: stur            w0, [x1, #0x27]
    // 0x134d190: r0 = true
    //     0x134d190: add             x0, NULL, #0x20  ; true
    // 0x134d194: StoreField: r1->field_2f = r0
    //     0x134d194: stur            w0, [x1, #0x2f]
    // 0x134d198: ldur            x0, [fp, #-0x30]
    // 0x134d19c: StoreField: r1->field_37 = r0
    //     0x134d19c: stur            w0, [x1, #0x37]
    // 0x134d1a0: r0 = Instance_ValueKey
    //     0x134d1a0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34680] Obj!ValueKey<String>@d5b361
    //     0x134d1a4: ldr             x0, [x0, #0x680]
    // 0x134d1a8: StoreField: r1->field_7 = r0
    //     0x134d1a8: stur            w0, [x1, #7]
    // 0x134d1ac: r0 = TextButtonTheme()
    //     0x134d1ac: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x134d1b0: mov             x3, x0
    // 0x134d1b4: ldur            x0, [fp, #-0x20]
    // 0x134d1b8: stur            x3, [fp, #-8]
    // 0x134d1bc: StoreField: r3->field_f = r0
    //     0x134d1bc: stur            w0, [x3, #0xf]
    // 0x134d1c0: ldur            x0, [fp, #-0x28]
    // 0x134d1c4: StoreField: r3->field_b = r0
    //     0x134d1c4: stur            w0, [x3, #0xb]
    // 0x134d1c8: r1 = Null
    //     0x134d1c8: mov             x1, NULL
    // 0x134d1cc: r2 = 6
    //     0x134d1cc: movz            x2, #0x6
    // 0x134d1d0: r0 = AllocateArray()
    //     0x134d1d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x134d1d4: mov             x2, x0
    // 0x134d1d8: ldur            x0, [fp, #-0x10]
    // 0x134d1dc: stur            x2, [fp, #-0x20]
    // 0x134d1e0: StoreField: r2->field_f = r0
    //     0x134d1e0: stur            w0, [x2, #0xf]
    // 0x134d1e4: r16 = Instance_Spacer
    //     0x134d1e4: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x134d1e8: ldr             x16, [x16, #0xf0]
    // 0x134d1ec: StoreField: r2->field_13 = r16
    //     0x134d1ec: stur            w16, [x2, #0x13]
    // 0x134d1f0: ldur            x0, [fp, #-8]
    // 0x134d1f4: ArrayStore: r2[0] = r0  ; List_4
    //     0x134d1f4: stur            w0, [x2, #0x17]
    // 0x134d1f8: r1 = <Widget>
    //     0x134d1f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x134d1fc: r0 = AllocateGrowableArray()
    //     0x134d1fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x134d200: mov             x1, x0
    // 0x134d204: ldur            x0, [fp, #-0x20]
    // 0x134d208: stur            x1, [fp, #-8]
    // 0x134d20c: StoreField: r1->field_f = r0
    //     0x134d20c: stur            w0, [x1, #0xf]
    // 0x134d210: r0 = 6
    //     0x134d210: movz            x0, #0x6
    // 0x134d214: StoreField: r1->field_b = r0
    //     0x134d214: stur            w0, [x1, #0xb]
    // 0x134d218: r0 = Row()
    //     0x134d218: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x134d21c: mov             x1, x0
    // 0x134d220: r0 = Instance_Axis
    //     0x134d220: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x134d224: stur            x1, [fp, #-0x10]
    // 0x134d228: StoreField: r1->field_f = r0
    //     0x134d228: stur            w0, [x1, #0xf]
    // 0x134d22c: r0 = Instance_MainAxisAlignment
    //     0x134d22c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x134d230: ldr             x0, [x0, #0xa08]
    // 0x134d234: StoreField: r1->field_13 = r0
    //     0x134d234: stur            w0, [x1, #0x13]
    // 0x134d238: r0 = Instance_MainAxisSize
    //     0x134d238: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x134d23c: ldr             x0, [x0, #0xa10]
    // 0x134d240: ArrayStore: r1[0] = r0  ; List_4
    //     0x134d240: stur            w0, [x1, #0x17]
    // 0x134d244: r0 = Instance_CrossAxisAlignment
    //     0x134d244: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x134d248: ldr             x0, [x0, #0xa18]
    // 0x134d24c: StoreField: r1->field_1b = r0
    //     0x134d24c: stur            w0, [x1, #0x1b]
    // 0x134d250: r0 = Instance_VerticalDirection
    //     0x134d250: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x134d254: ldr             x0, [x0, #0xa20]
    // 0x134d258: StoreField: r1->field_23 = r0
    //     0x134d258: stur            w0, [x1, #0x23]
    // 0x134d25c: r0 = Instance_Clip
    //     0x134d25c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x134d260: ldr             x0, [x0, #0x38]
    // 0x134d264: StoreField: r1->field_2b = r0
    //     0x134d264: stur            w0, [x1, #0x2b]
    // 0x134d268: StoreField: r1->field_2f = rZR
    //     0x134d268: stur            xzr, [x1, #0x2f]
    // 0x134d26c: ldur            x0, [fp, #-8]
    // 0x134d270: StoreField: r1->field_b = r0
    //     0x134d270: stur            w0, [x1, #0xb]
    // 0x134d274: r0 = Padding()
    //     0x134d274: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x134d278: mov             x1, x0
    // 0x134d27c: r0 = Instance_EdgeInsets
    //     0x134d27c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x134d280: ldr             x0, [x0, #0x1f0]
    // 0x134d284: stur            x1, [fp, #-8]
    // 0x134d288: StoreField: r1->field_f = r0
    //     0x134d288: stur            w0, [x1, #0xf]
    // 0x134d28c: ldur            x0, [fp, #-0x10]
    // 0x134d290: StoreField: r1->field_b = r0
    //     0x134d290: stur            w0, [x1, #0xb]
    // 0x134d294: r0 = Container()
    //     0x134d294: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x134d298: stur            x0, [fp, #-0x10]
    // 0x134d29c: ldur            x16, [fp, #-0x18]
    // 0x134d2a0: ldur            lr, [fp, #-8]
    // 0x134d2a4: stp             lr, x16, [SP]
    // 0x134d2a8: mov             x1, x0
    // 0x134d2ac: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x134d2ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x134d2b0: ldr             x4, [x4, #0x88]
    // 0x134d2b4: r0 = Container()
    //     0x134d2b4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x134d2b8: ldur            d0, [fp, #-0x40]
    // 0x134d2bc: r0 = inline_Allocate_Double()
    //     0x134d2bc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x134d2c0: add             x0, x0, #0x10
    //     0x134d2c4: cmp             x1, x0
    //     0x134d2c8: b.ls            #0x134d310
    //     0x134d2cc: str             x0, [THR, #0x50]  ; THR::top
    //     0x134d2d0: sub             x0, x0, #0xf
    //     0x134d2d4: movz            x1, #0xe15c
    //     0x134d2d8: movk            x1, #0x3, lsl #16
    //     0x134d2dc: stur            x1, [x0, #-1]
    // 0x134d2e0: StoreField: r0->field_7 = d0
    //     0x134d2e0: stur            d0, [x0, #7]
    // 0x134d2e4: stur            x0, [fp, #-8]
    // 0x134d2e8: r0 = SizedBox()
    //     0x134d2e8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x134d2ec: ldur            x1, [fp, #-8]
    // 0x134d2f0: StoreField: r0->field_13 = r1
    //     0x134d2f0: stur            w1, [x0, #0x13]
    // 0x134d2f4: ldur            x1, [fp, #-0x10]
    // 0x134d2f8: StoreField: r0->field_b = r1
    //     0x134d2f8: stur            w1, [x0, #0xb]
    // 0x134d2fc: LeaveFrame
    //     0x134d2fc: mov             SP, fp
    //     0x134d300: ldp             fp, lr, [SP], #0x10
    // 0x134d304: ret
    //     0x134d304: ret             
    // 0x134d308: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x134d308: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x134d30c: b               #0x134cd04
    // 0x134d310: SaveReg d0
    //     0x134d310: str             q0, [SP, #-0x10]!
    // 0x134d314: r0 = AllocateDouble()
    //     0x134d314: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x134d318: RestoreReg d0
    //     0x134d318: ldr             q0, [SP], #0x10
    // 0x134d31c: b               #0x134d2e0
  }
  _ body(/* No info */) {
    // ** addr: 0x148b85c, size: 0x64
    // 0x148b85c: EnterFrame
    //     0x148b85c: stp             fp, lr, [SP, #-0x10]!
    //     0x148b860: mov             fp, SP
    // 0x148b864: AllocStack(0x18)
    //     0x148b864: sub             SP, SP, #0x18
    // 0x148b868: SetupParameters(PaymentMethodsCheckoutWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x148b868: stur            x1, [fp, #-8]
    //     0x148b86c: stur            x2, [fp, #-0x10]
    // 0x148b870: r1 = 2
    //     0x148b870: movz            x1, #0x2
    // 0x148b874: r0 = AllocateContext()
    //     0x148b874: bl              #0x16f6108  ; AllocateContextStub
    // 0x148b878: mov             x1, x0
    // 0x148b87c: ldur            x0, [fp, #-8]
    // 0x148b880: stur            x1, [fp, #-0x18]
    // 0x148b884: StoreField: r1->field_f = r0
    //     0x148b884: stur            w0, [x1, #0xf]
    // 0x148b888: ldur            x0, [fp, #-0x10]
    // 0x148b88c: StoreField: r1->field_13 = r0
    //     0x148b88c: stur            w0, [x1, #0x13]
    // 0x148b890: r0 = Obx()
    //     0x148b890: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x148b894: ldur            x2, [fp, #-0x18]
    // 0x148b898: r1 = Function '<anonymous closure>':.
    //     0x148b898: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c38] AnonymousClosure: (0x148b8c0), in [package:customer_app/app/presentation/views/cosmetic/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x148b85c)
    //     0x148b89c: ldr             x1, [x1, #0xc38]
    // 0x148b8a0: stur            x0, [fp, #-8]
    // 0x148b8a4: r0 = AllocateClosure()
    //     0x148b8a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148b8a8: mov             x1, x0
    // 0x148b8ac: ldur            x0, [fp, #-8]
    // 0x148b8b0: StoreField: r0->field_b = r1
    //     0x148b8b0: stur            w1, [x0, #0xb]
    // 0x148b8b4: LeaveFrame
    //     0x148b8b4: mov             SP, fp
    //     0x148b8b8: ldp             fp, lr, [SP], #0x10
    // 0x148b8bc: ret
    //     0x148b8bc: ret             
  }
  [closure] WillPopScope <anonymous closure>(dynamic) {
    // ** addr: 0x148b8c0, size: 0x1118
    // 0x148b8c0: EnterFrame
    //     0x148b8c0: stp             fp, lr, [SP, #-0x10]!
    //     0x148b8c4: mov             fp, SP
    // 0x148b8c8: AllocStack(0xa8)
    //     0x148b8c8: sub             SP, SP, #0xa8
    // 0x148b8cc: SetupParameters()
    //     0x148b8cc: ldr             x0, [fp, #0x10]
    //     0x148b8d0: ldur            w2, [x0, #0x17]
    //     0x148b8d4: add             x2, x2, HEAP, lsl #32
    //     0x148b8d8: stur            x2, [fp, #-0x10]
    // 0x148b8dc: CheckStackOverflow
    //     0x148b8dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148b8e0: cmp             SP, x16
    //     0x148b8e4: b.ls            #0x148c9d0
    // 0x148b8e8: LoadField: r1 = r2->field_f
    //     0x148b8e8: ldur            w1, [x2, #0xf]
    // 0x148b8ec: DecompressPointer r1
    //     0x148b8ec: add             x1, x1, HEAP, lsl #32
    // 0x148b8f0: stur            x1, [fp, #-8]
    // 0x148b8f4: r0 = Obx()
    //     0x148b8f4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x148b8f8: ldur            x2, [fp, #-0x10]
    // 0x148b8fc: r1 = Function '<anonymous closure>':.
    //     0x148b8fc: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c40] AnonymousClosure: (0x148fa54), in [package:customer_app/app/presentation/views/cosmetic/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x148b85c)
    //     0x148b900: ldr             x1, [x1, #0xc40]
    // 0x148b904: stur            x0, [fp, #-0x18]
    // 0x148b908: r0 = AllocateClosure()
    //     0x148b908: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148b90c: mov             x1, x0
    // 0x148b910: ldur            x0, [fp, #-0x18]
    // 0x148b914: StoreField: r0->field_b = r1
    //     0x148b914: stur            w1, [x0, #0xb]
    // 0x148b918: r0 = Radius()
    //     0x148b918: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x148b91c: d0 = 12.000000
    //     0x148b91c: fmov            d0, #12.00000000
    // 0x148b920: stur            x0, [fp, #-0x20]
    // 0x148b924: StoreField: r0->field_7 = d0
    //     0x148b924: stur            d0, [x0, #7]
    // 0x148b928: StoreField: r0->field_f = d0
    //     0x148b928: stur            d0, [x0, #0xf]
    // 0x148b92c: r0 = BorderRadius()
    //     0x148b92c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x148b930: mov             x1, x0
    // 0x148b934: ldur            x0, [fp, #-0x20]
    // 0x148b938: stur            x1, [fp, #-0x28]
    // 0x148b93c: StoreField: r1->field_7 = r0
    //     0x148b93c: stur            w0, [x1, #7]
    // 0x148b940: StoreField: r1->field_b = r0
    //     0x148b940: stur            w0, [x1, #0xb]
    // 0x148b944: StoreField: r1->field_f = r0
    //     0x148b944: stur            w0, [x1, #0xf]
    // 0x148b948: StoreField: r1->field_13 = r0
    //     0x148b948: stur            w0, [x1, #0x13]
    // 0x148b94c: r0 = BoxDecoration()
    //     0x148b94c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x148b950: mov             x3, x0
    // 0x148b954: r0 = Instance_Color
    //     0x148b954: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x148b958: stur            x3, [fp, #-0x20]
    // 0x148b95c: StoreField: r3->field_7 = r0
    //     0x148b95c: stur            w0, [x3, #7]
    // 0x148b960: ldur            x1, [fp, #-0x28]
    // 0x148b964: StoreField: r3->field_13 = r1
    //     0x148b964: stur            w1, [x3, #0x13]
    // 0x148b968: r4 = Instance_BoxShape
    //     0x148b968: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x148b96c: ldr             x4, [x4, #0x80]
    // 0x148b970: StoreField: r3->field_23 = r4
    //     0x148b970: stur            w4, [x3, #0x23]
    // 0x148b974: r1 = Null
    //     0x148b974: mov             x1, NULL
    // 0x148b978: r2 = 4
    //     0x148b978: movz            x2, #0x4
    // 0x148b97c: r0 = AllocateArray()
    //     0x148b97c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148b980: stur            x0, [fp, #-0x28]
    // 0x148b984: r16 = "Bag "
    //     0x148b984: add             x16, PP, #0x34, lsl #12  ; [pp+0x346a8] "Bag "
    //     0x148b988: ldr             x16, [x16, #0x6a8]
    // 0x148b98c: StoreField: r0->field_f = r16
    //     0x148b98c: stur            w16, [x0, #0xf]
    // 0x148b990: ldur            x1, [fp, #-8]
    // 0x148b994: r0 = controller()
    //     0x148b994: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148b998: LoadField: r1 = r0->field_73
    //     0x148b998: ldur            w1, [x0, #0x73]
    // 0x148b99c: DecompressPointer r1
    //     0x148b99c: add             x1, x1, HEAP, lsl #32
    // 0x148b9a0: r0 = value()
    //     0x148b9a0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148b9a4: LoadField: r1 = r0->field_b
    //     0x148b9a4: ldur            w1, [x0, #0xb]
    // 0x148b9a8: DecompressPointer r1
    //     0x148b9a8: add             x1, x1, HEAP, lsl #32
    // 0x148b9ac: cmp             w1, NULL
    // 0x148b9b0: b.ne            #0x148b9bc
    // 0x148b9b4: r0 = Null
    //     0x148b9b4: mov             x0, NULL
    // 0x148b9b8: b               #0x148b9e0
    // 0x148b9bc: LoadField: r0 = r1->field_f
    //     0x148b9bc: ldur            w0, [x1, #0xf]
    // 0x148b9c0: DecompressPointer r0
    //     0x148b9c0: add             x0, x0, HEAP, lsl #32
    // 0x148b9c4: cmp             w0, NULL
    // 0x148b9c8: b.ne            #0x148b9d4
    // 0x148b9cc: r0 = Null
    //     0x148b9cc: mov             x0, NULL
    // 0x148b9d0: b               #0x148b9e0
    // 0x148b9d4: LoadField: r1 = r0->field_7
    //     0x148b9d4: ldur            w1, [x0, #7]
    // 0x148b9d8: DecompressPointer r1
    //     0x148b9d8: add             x1, x1, HEAP, lsl #32
    // 0x148b9dc: mov             x0, x1
    // 0x148b9e0: cmp             w0, NULL
    // 0x148b9e4: b.ne            #0x148b9ec
    // 0x148b9e8: r0 = ""
    //     0x148b9e8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148b9ec: ldur            x2, [fp, #-0x10]
    // 0x148b9f0: ldur            x1, [fp, #-0x28]
    // 0x148b9f4: ArrayStore: r1[1] = r0  ; List_4
    //     0x148b9f4: add             x25, x1, #0x13
    //     0x148b9f8: str             w0, [x25]
    //     0x148b9fc: tbz             w0, #0, #0x148ba18
    //     0x148ba00: ldurb           w16, [x1, #-1]
    //     0x148ba04: ldurb           w17, [x0, #-1]
    //     0x148ba08: and             x16, x17, x16, lsr #2
    //     0x148ba0c: tst             x16, HEAP, lsr #32
    //     0x148ba10: b.eq            #0x148ba18
    //     0x148ba14: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x148ba18: ldur            x16, [fp, #-0x28]
    // 0x148ba1c: str             x16, [SP]
    // 0x148ba20: r0 = _interpolate()
    //     0x148ba20: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x148ba24: ldur            x2, [fp, #-0x10]
    // 0x148ba28: stur            x0, [fp, #-0x28]
    // 0x148ba2c: LoadField: r1 = r2->field_13
    //     0x148ba2c: ldur            w1, [x2, #0x13]
    // 0x148ba30: DecompressPointer r1
    //     0x148ba30: add             x1, x1, HEAP, lsl #32
    // 0x148ba34: r0 = of()
    //     0x148ba34: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148ba38: LoadField: r1 = r0->field_87
    //     0x148ba38: ldur            w1, [x0, #0x87]
    // 0x148ba3c: DecompressPointer r1
    //     0x148ba3c: add             x1, x1, HEAP, lsl #32
    // 0x148ba40: LoadField: r0 = r1->field_7
    //     0x148ba40: ldur            w0, [x1, #7]
    // 0x148ba44: DecompressPointer r0
    //     0x148ba44: add             x0, x0, HEAP, lsl #32
    // 0x148ba48: r16 = Instance_Color
    //     0x148ba48: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148ba4c: r30 = 14.000000
    //     0x148ba4c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x148ba50: ldr             lr, [lr, #0x1d8]
    // 0x148ba54: stp             lr, x16, [SP]
    // 0x148ba58: mov             x1, x0
    // 0x148ba5c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x148ba5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x148ba60: ldr             x4, [x4, #0x9b8]
    // 0x148ba64: r0 = copyWith()
    //     0x148ba64: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148ba68: stur            x0, [fp, #-0x30]
    // 0x148ba6c: r0 = Text()
    //     0x148ba6c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148ba70: mov             x3, x0
    // 0x148ba74: ldur            x0, [fp, #-0x28]
    // 0x148ba78: stur            x3, [fp, #-0x38]
    // 0x148ba7c: StoreField: r3->field_b = r0
    //     0x148ba7c: stur            w0, [x3, #0xb]
    // 0x148ba80: ldur            x0, [fp, #-0x30]
    // 0x148ba84: StoreField: r3->field_13 = r0
    //     0x148ba84: stur            w0, [x3, #0x13]
    // 0x148ba88: r1 = <Widget>
    //     0x148ba88: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148ba8c: r2 = 0
    //     0x148ba8c: movz            x2, #0
    // 0x148ba90: r0 = _GrowableList()
    //     0x148ba90: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x148ba94: ldur            x2, [fp, #-0x10]
    // 0x148ba98: stur            x0, [fp, #-0x28]
    // 0x148ba9c: LoadField: r1 = r2->field_f
    //     0x148ba9c: ldur            w1, [x2, #0xf]
    // 0x148baa0: DecompressPointer r1
    //     0x148baa0: add             x1, x1, HEAP, lsl #32
    // 0x148baa4: r0 = controller()
    //     0x148baa4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148baa8: LoadField: r1 = r0->field_73
    //     0x148baa8: ldur            w1, [x0, #0x73]
    // 0x148baac: DecompressPointer r1
    //     0x148baac: add             x1, x1, HEAP, lsl #32
    // 0x148bab0: r0 = value()
    //     0x148bab0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148bab4: LoadField: r1 = r0->field_b
    //     0x148bab4: ldur            w1, [x0, #0xb]
    // 0x148bab8: DecompressPointer r1
    //     0x148bab8: add             x1, x1, HEAP, lsl #32
    // 0x148babc: cmp             w1, NULL
    // 0x148bac0: b.ne            #0x148bacc
    // 0x148bac4: ldur            x2, [fp, #-0x28]
    // 0x148bac8: b               #0x148c344
    // 0x148bacc: LoadField: r0 = r1->field_43
    //     0x148bacc: ldur            w0, [x1, #0x43]
    // 0x148bad0: DecompressPointer r0
    //     0x148bad0: add             x0, x0, HEAP, lsl #32
    // 0x148bad4: cmp             w0, NULL
    // 0x148bad8: b.eq            #0x148c340
    // 0x148badc: ldur            x2, [fp, #-0x10]
    // 0x148bae0: LoadField: r1 = r2->field_f
    //     0x148bae0: ldur            w1, [x2, #0xf]
    // 0x148bae4: DecompressPointer r1
    //     0x148bae4: add             x1, x1, HEAP, lsl #32
    // 0x148bae8: r0 = controller()
    //     0x148bae8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148baec: LoadField: r1 = r0->field_73
    //     0x148baec: ldur            w1, [x0, #0x73]
    // 0x148baf0: DecompressPointer r1
    //     0x148baf0: add             x1, x1, HEAP, lsl #32
    // 0x148baf4: r0 = value()
    //     0x148baf4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148baf8: LoadField: r1 = r0->field_b
    //     0x148baf8: ldur            w1, [x0, #0xb]
    // 0x148bafc: DecompressPointer r1
    //     0x148bafc: add             x1, x1, HEAP, lsl #32
    // 0x148bb00: cmp             w1, NULL
    // 0x148bb04: b.ne            #0x148bb10
    // 0x148bb08: r0 = Null
    //     0x148bb08: mov             x0, NULL
    // 0x148bb0c: b               #0x148bb34
    // 0x148bb10: LoadField: r0 = r1->field_43
    //     0x148bb10: ldur            w0, [x1, #0x43]
    // 0x148bb14: DecompressPointer r0
    //     0x148bb14: add             x0, x0, HEAP, lsl #32
    // 0x148bb18: cmp             w0, NULL
    // 0x148bb1c: b.ne            #0x148bb28
    // 0x148bb20: r0 = Null
    //     0x148bb20: mov             x0, NULL
    // 0x148bb24: b               #0x148bb34
    // 0x148bb28: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x148bb28: ldur            w1, [x0, #0x17]
    // 0x148bb2c: DecompressPointer r1
    //     0x148bb2c: add             x1, x1, HEAP, lsl #32
    // 0x148bb30: mov             x0, x1
    // 0x148bb34: cmp             w0, NULL
    // 0x148bb38: b.ne            #0x148bb40
    // 0x148bb3c: r0 = false
    //     0x148bb3c: add             x0, NULL, #0x30  ; false
    // 0x148bb40: ldur            x2, [fp, #-0x10]
    // 0x148bb44: stur            x0, [fp, #-0x30]
    // 0x148bb48: r0 = Radius()
    //     0x148bb48: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x148bb4c: d0 = 12.000000
    //     0x148bb4c: fmov            d0, #12.00000000
    // 0x148bb50: stur            x0, [fp, #-0x40]
    // 0x148bb54: StoreField: r0->field_7 = d0
    //     0x148bb54: stur            d0, [x0, #7]
    // 0x148bb58: StoreField: r0->field_f = d0
    //     0x148bb58: stur            d0, [x0, #0xf]
    // 0x148bb5c: r0 = BorderRadius()
    //     0x148bb5c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x148bb60: mov             x2, x0
    // 0x148bb64: ldur            x0, [fp, #-0x40]
    // 0x148bb68: stur            x2, [fp, #-0x48]
    // 0x148bb6c: StoreField: r2->field_7 = r0
    //     0x148bb6c: stur            w0, [x2, #7]
    // 0x148bb70: StoreField: r2->field_b = r0
    //     0x148bb70: stur            w0, [x2, #0xb]
    // 0x148bb74: StoreField: r2->field_f = r0
    //     0x148bb74: stur            w0, [x2, #0xf]
    // 0x148bb78: StoreField: r2->field_13 = r0
    //     0x148bb78: stur            w0, [x2, #0x13]
    // 0x148bb7c: r1 = Instance_Color
    //     0x148bb7c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148bb80: d0 = 0.070000
    //     0x148bb80: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0x148bb84: ldr             d0, [x17, #0x5f8]
    // 0x148bb88: r0 = withOpacity()
    //     0x148bb88: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x148bb8c: r16 = 1.000000
    //     0x148bb8c: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x148bb90: str             x16, [SP]
    // 0x148bb94: mov             x2, x0
    // 0x148bb98: r1 = Null
    //     0x148bb98: mov             x1, NULL
    // 0x148bb9c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x148bb9c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x148bba0: ldr             x4, [x4, #0x108]
    // 0x148bba4: r0 = Border.all()
    //     0x148bba4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x148bba8: stur            x0, [fp, #-0x40]
    // 0x148bbac: r0 = BoxDecoration()
    //     0x148bbac: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x148bbb0: mov             x2, x0
    // 0x148bbb4: ldur            x0, [fp, #-0x40]
    // 0x148bbb8: stur            x2, [fp, #-0x50]
    // 0x148bbbc: StoreField: r2->field_f = r0
    //     0x148bbbc: stur            w0, [x2, #0xf]
    // 0x148bbc0: ldur            x0, [fp, #-0x48]
    // 0x148bbc4: StoreField: r2->field_13 = r0
    //     0x148bbc4: stur            w0, [x2, #0x13]
    // 0x148bbc8: r0 = Instance_LinearGradient
    //     0x148bbc8: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0x148bbcc: ldr             x0, [x0, #0x660]
    // 0x148bbd0: StoreField: r2->field_1b = r0
    //     0x148bbd0: stur            w0, [x2, #0x1b]
    // 0x148bbd4: r0 = Instance_BoxShape
    //     0x148bbd4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x148bbd8: ldr             x0, [x0, #0x80]
    // 0x148bbdc: StoreField: r2->field_23 = r0
    //     0x148bbdc: stur            w0, [x2, #0x23]
    // 0x148bbe0: ldur            x0, [fp, #-0x10]
    // 0x148bbe4: LoadField: r1 = r0->field_13
    //     0x148bbe4: ldur            w1, [x0, #0x13]
    // 0x148bbe8: DecompressPointer r1
    //     0x148bbe8: add             x1, x1, HEAP, lsl #32
    // 0x148bbec: r0 = of()
    //     0x148bbec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148bbf0: LoadField: r1 = r0->field_87
    //     0x148bbf0: ldur            w1, [x0, #0x87]
    // 0x148bbf4: DecompressPointer r1
    //     0x148bbf4: add             x1, x1, HEAP, lsl #32
    // 0x148bbf8: LoadField: r0 = r1->field_7
    //     0x148bbf8: ldur            w0, [x1, #7]
    // 0x148bbfc: DecompressPointer r0
    //     0x148bbfc: add             x0, x0, HEAP, lsl #32
    // 0x148bc00: r16 = 12.000000
    //     0x148bc00: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x148bc04: ldr             x16, [x16, #0x9e8]
    // 0x148bc08: r30 = Instance_Color
    //     0x148bc08: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x148bc0c: stp             lr, x16, [SP]
    // 0x148bc10: mov             x1, x0
    // 0x148bc14: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x148bc14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x148bc18: ldr             x4, [x4, #0xaa0]
    // 0x148bc1c: r0 = copyWith()
    //     0x148bc1c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148bc20: stur            x0, [fp, #-0x40]
    // 0x148bc24: r0 = Text()
    //     0x148bc24: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148bc28: mov             x1, x0
    // 0x148bc2c: r0 = "Free"
    //     0x148bc2c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0x148bc30: ldr             x0, [x0, #0x668]
    // 0x148bc34: stur            x1, [fp, #-0x48]
    // 0x148bc38: StoreField: r1->field_b = r0
    //     0x148bc38: stur            w0, [x1, #0xb]
    // 0x148bc3c: ldur            x2, [fp, #-0x40]
    // 0x148bc40: StoreField: r1->field_13 = r2
    //     0x148bc40: stur            w2, [x1, #0x13]
    // 0x148bc44: r0 = Center()
    //     0x148bc44: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x148bc48: mov             x1, x0
    // 0x148bc4c: r0 = Instance_Alignment
    //     0x148bc4c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x148bc50: ldr             x0, [x0, #0xb10]
    // 0x148bc54: stur            x1, [fp, #-0x40]
    // 0x148bc58: StoreField: r1->field_f = r0
    //     0x148bc58: stur            w0, [x1, #0xf]
    // 0x148bc5c: ldur            x0, [fp, #-0x48]
    // 0x148bc60: StoreField: r1->field_b = r0
    //     0x148bc60: stur            w0, [x1, #0xb]
    // 0x148bc64: r0 = RotatedBox()
    //     0x148bc64: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0x148bc68: mov             x1, x0
    // 0x148bc6c: r0 = -1
    //     0x148bc6c: movn            x0, #0
    // 0x148bc70: stur            x1, [fp, #-0x48]
    // 0x148bc74: StoreField: r1->field_f = r0
    //     0x148bc74: stur            x0, [x1, #0xf]
    // 0x148bc78: ldur            x0, [fp, #-0x40]
    // 0x148bc7c: StoreField: r1->field_b = r0
    //     0x148bc7c: stur            w0, [x1, #0xb]
    // 0x148bc80: r0 = Container()
    //     0x148bc80: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x148bc84: stur            x0, [fp, #-0x40]
    // 0x148bc88: r16 = 24.000000
    //     0x148bc88: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x148bc8c: ldr             x16, [x16, #0xba8]
    // 0x148bc90: r30 = 56.000000
    //     0x148bc90: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x148bc94: ldr             lr, [lr, #0xb78]
    // 0x148bc98: stp             lr, x16, [SP, #0x10]
    // 0x148bc9c: r16 = Instance_BoxDecoration
    //     0x148bc9c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40db0] Obj!BoxDecoration@d648c1
    //     0x148bca0: ldr             x16, [x16, #0xdb0]
    // 0x148bca4: ldur            lr, [fp, #-0x48]
    // 0x148bca8: stp             lr, x16, [SP]
    // 0x148bcac: mov             x1, x0
    // 0x148bcb0: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x148bcb0: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x148bcb4: ldr             x4, [x4, #0x870]
    // 0x148bcb8: r0 = Container()
    //     0x148bcb8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x148bcbc: ldur            x2, [fp, #-0x10]
    // 0x148bcc0: LoadField: r1 = r2->field_f
    //     0x148bcc0: ldur            w1, [x2, #0xf]
    // 0x148bcc4: DecompressPointer r1
    //     0x148bcc4: add             x1, x1, HEAP, lsl #32
    // 0x148bcc8: r0 = controller()
    //     0x148bcc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148bccc: LoadField: r1 = r0->field_73
    //     0x148bccc: ldur            w1, [x0, #0x73]
    // 0x148bcd0: DecompressPointer r1
    //     0x148bcd0: add             x1, x1, HEAP, lsl #32
    // 0x148bcd4: r0 = value()
    //     0x148bcd4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148bcd8: LoadField: r1 = r0->field_b
    //     0x148bcd8: ldur            w1, [x0, #0xb]
    // 0x148bcdc: DecompressPointer r1
    //     0x148bcdc: add             x1, x1, HEAP, lsl #32
    // 0x148bce0: cmp             w1, NULL
    // 0x148bce4: b.ne            #0x148bcf0
    // 0x148bce8: r0 = Null
    //     0x148bce8: mov             x0, NULL
    // 0x148bcec: b               #0x148bd14
    // 0x148bcf0: LoadField: r0 = r1->field_43
    //     0x148bcf0: ldur            w0, [x1, #0x43]
    // 0x148bcf4: DecompressPointer r0
    //     0x148bcf4: add             x0, x0, HEAP, lsl #32
    // 0x148bcf8: cmp             w0, NULL
    // 0x148bcfc: b.ne            #0x148bd08
    // 0x148bd00: r0 = Null
    //     0x148bd00: mov             x0, NULL
    // 0x148bd04: b               #0x148bd14
    // 0x148bd08: LoadField: r1 = r0->field_7
    //     0x148bd08: ldur            w1, [x0, #7]
    // 0x148bd0c: DecompressPointer r1
    //     0x148bd0c: add             x1, x1, HEAP, lsl #32
    // 0x148bd10: mov             x0, x1
    // 0x148bd14: cmp             w0, NULL
    // 0x148bd18: b.ne            #0x148bd24
    // 0x148bd1c: r3 = ""
    //     0x148bd1c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148bd20: b               #0x148bd28
    // 0x148bd24: mov             x3, x0
    // 0x148bd28: ldur            x0, [fp, #-0x10]
    // 0x148bd2c: stur            x3, [fp, #-0x48]
    // 0x148bd30: r1 = Function '<anonymous closure>':.
    //     0x148bd30: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c48] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x148bd34: ldr             x1, [x1, #0xc48]
    // 0x148bd38: r2 = Null
    //     0x148bd38: mov             x2, NULL
    // 0x148bd3c: r0 = AllocateClosure()
    //     0x148bd3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148bd40: r1 = Function '<anonymous closure>':.
    //     0x148bd40: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c50] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x148bd44: ldr             x1, [x1, #0xc50]
    // 0x148bd48: r2 = Null
    //     0x148bd48: mov             x2, NULL
    // 0x148bd4c: stur            x0, [fp, #-0x58]
    // 0x148bd50: r0 = AllocateClosure()
    //     0x148bd50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148bd54: stur            x0, [fp, #-0x60]
    // 0x148bd58: r0 = CachedNetworkImage()
    //     0x148bd58: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x148bd5c: stur            x0, [fp, #-0x68]
    // 0x148bd60: r16 = 56.000000
    //     0x148bd60: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x148bd64: ldr             x16, [x16, #0xb78]
    // 0x148bd68: r30 = 56.000000
    //     0x148bd68: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x148bd6c: ldr             lr, [lr, #0xb78]
    // 0x148bd70: stp             lr, x16, [SP, #0x18]
    // 0x148bd74: r16 = Instance_BoxFit
    //     0x148bd74: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x148bd78: ldr             x16, [x16, #0x118]
    // 0x148bd7c: ldur            lr, [fp, #-0x58]
    // 0x148bd80: stp             lr, x16, [SP, #8]
    // 0x148bd84: ldur            x16, [fp, #-0x60]
    // 0x148bd88: str             x16, [SP]
    // 0x148bd8c: mov             x1, x0
    // 0x148bd90: ldur            x2, [fp, #-0x48]
    // 0x148bd94: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x3, progressIndicatorBuilder, 0x5, width, 0x2, null]
    //     0x148bd94: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c710] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x3, "progressIndicatorBuilder", 0x5, "width", 0x2, Null]
    //     0x148bd98: ldr             x4, [x4, #0x710]
    // 0x148bd9c: r0 = CachedNetworkImage()
    //     0x148bd9c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x148bda0: ldur            x2, [fp, #-0x10]
    // 0x148bda4: LoadField: r1 = r2->field_f
    //     0x148bda4: ldur            w1, [x2, #0xf]
    // 0x148bda8: DecompressPointer r1
    //     0x148bda8: add             x1, x1, HEAP, lsl #32
    // 0x148bdac: r0 = controller()
    //     0x148bdac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148bdb0: LoadField: r1 = r0->field_73
    //     0x148bdb0: ldur            w1, [x0, #0x73]
    // 0x148bdb4: DecompressPointer r1
    //     0x148bdb4: add             x1, x1, HEAP, lsl #32
    // 0x148bdb8: r0 = value()
    //     0x148bdb8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148bdbc: LoadField: r1 = r0->field_b
    //     0x148bdbc: ldur            w1, [x0, #0xb]
    // 0x148bdc0: DecompressPointer r1
    //     0x148bdc0: add             x1, x1, HEAP, lsl #32
    // 0x148bdc4: cmp             w1, NULL
    // 0x148bdc8: b.ne            #0x148bdd4
    // 0x148bdcc: r0 = Null
    //     0x148bdcc: mov             x0, NULL
    // 0x148bdd0: b               #0x148bdf8
    // 0x148bdd4: LoadField: r0 = r1->field_43
    //     0x148bdd4: ldur            w0, [x1, #0x43]
    // 0x148bdd8: DecompressPointer r0
    //     0x148bdd8: add             x0, x0, HEAP, lsl #32
    // 0x148bddc: cmp             w0, NULL
    // 0x148bde0: b.ne            #0x148bdec
    // 0x148bde4: r0 = Null
    //     0x148bde4: mov             x0, NULL
    // 0x148bde8: b               #0x148bdf8
    // 0x148bdec: LoadField: r1 = r0->field_b
    //     0x148bdec: ldur            w1, [x0, #0xb]
    // 0x148bdf0: DecompressPointer r1
    //     0x148bdf0: add             x1, x1, HEAP, lsl #32
    // 0x148bdf4: mov             x0, x1
    // 0x148bdf8: cmp             w0, NULL
    // 0x148bdfc: b.ne            #0x148be04
    // 0x148be00: r0 = ""
    //     0x148be00: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148be04: ldur            x2, [fp, #-0x10]
    // 0x148be08: stur            x0, [fp, #-0x48]
    // 0x148be0c: LoadField: r1 = r2->field_13
    //     0x148be0c: ldur            w1, [x2, #0x13]
    // 0x148be10: DecompressPointer r1
    //     0x148be10: add             x1, x1, HEAP, lsl #32
    // 0x148be14: r0 = of()
    //     0x148be14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148be18: LoadField: r1 = r0->field_87
    //     0x148be18: ldur            w1, [x0, #0x87]
    // 0x148be1c: DecompressPointer r1
    //     0x148be1c: add             x1, x1, HEAP, lsl #32
    // 0x148be20: LoadField: r0 = r1->field_7
    //     0x148be20: ldur            w0, [x1, #7]
    // 0x148be24: DecompressPointer r0
    //     0x148be24: add             x0, x0, HEAP, lsl #32
    // 0x148be28: r16 = 12.000000
    //     0x148be28: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x148be2c: ldr             x16, [x16, #0x9e8]
    // 0x148be30: r30 = Instance_Color
    //     0x148be30: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148be34: stp             lr, x16, [SP]
    // 0x148be38: mov             x1, x0
    // 0x148be3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x148be3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x148be40: ldr             x4, [x4, #0xaa0]
    // 0x148be44: r0 = copyWith()
    //     0x148be44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148be48: stur            x0, [fp, #-0x58]
    // 0x148be4c: r0 = Text()
    //     0x148be4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148be50: mov             x1, x0
    // 0x148be54: ldur            x0, [fp, #-0x48]
    // 0x148be58: stur            x1, [fp, #-0x60]
    // 0x148be5c: StoreField: r1->field_b = r0
    //     0x148be5c: stur            w0, [x1, #0xb]
    // 0x148be60: ldur            x0, [fp, #-0x58]
    // 0x148be64: StoreField: r1->field_13 = r0
    //     0x148be64: stur            w0, [x1, #0x13]
    // 0x148be68: r0 = Instance_TextOverflow
    //     0x148be68: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x148be6c: ldr             x0, [x0, #0xe10]
    // 0x148be70: StoreField: r1->field_2b = r0
    //     0x148be70: stur            w0, [x1, #0x2b]
    // 0x148be74: r0 = 2
    //     0x148be74: movz            x0, #0x2
    // 0x148be78: StoreField: r1->field_37 = r0
    //     0x148be78: stur            w0, [x1, #0x37]
    // 0x148be7c: r0 = SizedBox()
    //     0x148be7c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x148be80: mov             x2, x0
    // 0x148be84: r0 = 150.000000
    //     0x148be84: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0x148be88: ldr             x0, [x0, #0x690]
    // 0x148be8c: stur            x2, [fp, #-0x48]
    // 0x148be90: StoreField: r2->field_f = r0
    //     0x148be90: stur            w0, [x2, #0xf]
    // 0x148be94: ldur            x0, [fp, #-0x60]
    // 0x148be98: StoreField: r2->field_b = r0
    //     0x148be98: stur            w0, [x2, #0xb]
    // 0x148be9c: ldur            x0, [fp, #-0x10]
    // 0x148bea0: LoadField: r1 = r0->field_13
    //     0x148bea0: ldur            w1, [x0, #0x13]
    // 0x148bea4: DecompressPointer r1
    //     0x148bea4: add             x1, x1, HEAP, lsl #32
    // 0x148bea8: r0 = of()
    //     0x148bea8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148beac: LoadField: r1 = r0->field_87
    //     0x148beac: ldur            w1, [x0, #0x87]
    // 0x148beb0: DecompressPointer r1
    //     0x148beb0: add             x1, x1, HEAP, lsl #32
    // 0x148beb4: LoadField: r0 = r1->field_2b
    //     0x148beb4: ldur            w0, [x1, #0x2b]
    // 0x148beb8: DecompressPointer r0
    //     0x148beb8: add             x0, x0, HEAP, lsl #32
    // 0x148bebc: r16 = 12.000000
    //     0x148bebc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x148bec0: ldr             x16, [x16, #0x9e8]
    // 0x148bec4: r30 = Instance_Color
    //     0x148bec4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x148bec8: ldr             lr, [lr, #0x858]
    // 0x148becc: stp             lr, x16, [SP]
    // 0x148bed0: mov             x1, x0
    // 0x148bed4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x148bed4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x148bed8: ldr             x4, [x4, #0xaa0]
    // 0x148bedc: r0 = copyWith()
    //     0x148bedc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148bee0: stur            x0, [fp, #-0x58]
    // 0x148bee4: r0 = Text()
    //     0x148bee4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148bee8: mov             x2, x0
    // 0x148beec: r0 = "Free"
    //     0x148beec: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0x148bef0: ldr             x0, [x0, #0x668]
    // 0x148bef4: stur            x2, [fp, #-0x60]
    // 0x148bef8: StoreField: r2->field_b = r0
    //     0x148bef8: stur            w0, [x2, #0xb]
    // 0x148befc: ldur            x0, [fp, #-0x58]
    // 0x148bf00: StoreField: r2->field_13 = r0
    //     0x148bf00: stur            w0, [x2, #0x13]
    // 0x148bf04: ldur            x0, [fp, #-0x10]
    // 0x148bf08: LoadField: r1 = r0->field_f
    //     0x148bf08: ldur            w1, [x0, #0xf]
    // 0x148bf0c: DecompressPointer r1
    //     0x148bf0c: add             x1, x1, HEAP, lsl #32
    // 0x148bf10: r0 = controller()
    //     0x148bf10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148bf14: LoadField: r1 = r0->field_73
    //     0x148bf14: ldur            w1, [x0, #0x73]
    // 0x148bf18: DecompressPointer r1
    //     0x148bf18: add             x1, x1, HEAP, lsl #32
    // 0x148bf1c: r0 = value()
    //     0x148bf1c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148bf20: LoadField: r1 = r0->field_b
    //     0x148bf20: ldur            w1, [x0, #0xb]
    // 0x148bf24: DecompressPointer r1
    //     0x148bf24: add             x1, x1, HEAP, lsl #32
    // 0x148bf28: cmp             w1, NULL
    // 0x148bf2c: b.ne            #0x148bf38
    // 0x148bf30: r0 = Null
    //     0x148bf30: mov             x0, NULL
    // 0x148bf34: b               #0x148bf5c
    // 0x148bf38: LoadField: r0 = r1->field_43
    //     0x148bf38: ldur            w0, [x1, #0x43]
    // 0x148bf3c: DecompressPointer r0
    //     0x148bf3c: add             x0, x0, HEAP, lsl #32
    // 0x148bf40: cmp             w0, NULL
    // 0x148bf44: b.ne            #0x148bf50
    // 0x148bf48: r0 = Null
    //     0x148bf48: mov             x0, NULL
    // 0x148bf4c: b               #0x148bf5c
    // 0x148bf50: LoadField: r1 = r0->field_13
    //     0x148bf50: ldur            w1, [x0, #0x13]
    // 0x148bf54: DecompressPointer r1
    //     0x148bf54: add             x1, x1, HEAP, lsl #32
    // 0x148bf58: mov             x0, x1
    // 0x148bf5c: cmp             w0, NULL
    // 0x148bf60: b.ne            #0x148bf6c
    // 0x148bf64: r8 = ""
    //     0x148bf64: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148bf68: b               #0x148bf70
    // 0x148bf6c: mov             x8, x0
    // 0x148bf70: ldur            x2, [fp, #-0x10]
    // 0x148bf74: ldur            x7, [fp, #-0x28]
    // 0x148bf78: ldur            x6, [fp, #-0x30]
    // 0x148bf7c: ldur            x5, [fp, #-0x40]
    // 0x148bf80: ldur            x4, [fp, #-0x68]
    // 0x148bf84: ldur            x3, [fp, #-0x48]
    // 0x148bf88: ldur            x0, [fp, #-0x60]
    // 0x148bf8c: stur            x8, [fp, #-0x58]
    // 0x148bf90: LoadField: r1 = r2->field_13
    //     0x148bf90: ldur            w1, [x2, #0x13]
    // 0x148bf94: DecompressPointer r1
    //     0x148bf94: add             x1, x1, HEAP, lsl #32
    // 0x148bf98: r0 = of()
    //     0x148bf98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148bf9c: LoadField: r1 = r0->field_87
    //     0x148bf9c: ldur            w1, [x0, #0x87]
    // 0x148bfa0: DecompressPointer r1
    //     0x148bfa0: add             x1, x1, HEAP, lsl #32
    // 0x148bfa4: LoadField: r0 = r1->field_2b
    //     0x148bfa4: ldur            w0, [x1, #0x2b]
    // 0x148bfa8: DecompressPointer r0
    //     0x148bfa8: add             x0, x0, HEAP, lsl #32
    // 0x148bfac: r16 = 12.000000
    //     0x148bfac: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x148bfb0: ldr             x16, [x16, #0x9e8]
    // 0x148bfb4: r30 = Instance_TextDecoration
    //     0x148bfb4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0x148bfb8: ldr             lr, [lr, #0xe30]
    // 0x148bfbc: stp             lr, x16, [SP]
    // 0x148bfc0: mov             x1, x0
    // 0x148bfc4: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0x148bfc4: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0x148bfc8: ldr             x4, [x4, #0x698]
    // 0x148bfcc: r0 = copyWith()
    //     0x148bfcc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148bfd0: stur            x0, [fp, #-0x70]
    // 0x148bfd4: r0 = Text()
    //     0x148bfd4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148bfd8: mov             x3, x0
    // 0x148bfdc: ldur            x0, [fp, #-0x58]
    // 0x148bfe0: stur            x3, [fp, #-0x78]
    // 0x148bfe4: StoreField: r3->field_b = r0
    //     0x148bfe4: stur            w0, [x3, #0xb]
    // 0x148bfe8: ldur            x0, [fp, #-0x70]
    // 0x148bfec: StoreField: r3->field_13 = r0
    //     0x148bfec: stur            w0, [x3, #0x13]
    // 0x148bff0: r1 = Null
    //     0x148bff0: mov             x1, NULL
    // 0x148bff4: r2 = 6
    //     0x148bff4: movz            x2, #0x6
    // 0x148bff8: r0 = AllocateArray()
    //     0x148bff8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148bffc: mov             x2, x0
    // 0x148c000: ldur            x0, [fp, #-0x60]
    // 0x148c004: stur            x2, [fp, #-0x58]
    // 0x148c008: StoreField: r2->field_f = r0
    //     0x148c008: stur            w0, [x2, #0xf]
    // 0x148c00c: r16 = Instance_SizedBox
    //     0x148c00c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0x148c010: ldr             x16, [x16, #0xa50]
    // 0x148c014: StoreField: r2->field_13 = r16
    //     0x148c014: stur            w16, [x2, #0x13]
    // 0x148c018: ldur            x0, [fp, #-0x78]
    // 0x148c01c: ArrayStore: r2[0] = r0  ; List_4
    //     0x148c01c: stur            w0, [x2, #0x17]
    // 0x148c020: r1 = <Widget>
    //     0x148c020: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148c024: r0 = AllocateGrowableArray()
    //     0x148c024: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148c028: mov             x1, x0
    // 0x148c02c: ldur            x0, [fp, #-0x58]
    // 0x148c030: stur            x1, [fp, #-0x60]
    // 0x148c034: StoreField: r1->field_f = r0
    //     0x148c034: stur            w0, [x1, #0xf]
    // 0x148c038: r2 = 6
    //     0x148c038: movz            x2, #0x6
    // 0x148c03c: StoreField: r1->field_b = r2
    //     0x148c03c: stur            w2, [x1, #0xb]
    // 0x148c040: r0 = Row()
    //     0x148c040: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x148c044: mov             x3, x0
    // 0x148c048: r0 = Instance_Axis
    //     0x148c048: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x148c04c: stur            x3, [fp, #-0x58]
    // 0x148c050: StoreField: r3->field_f = r0
    //     0x148c050: stur            w0, [x3, #0xf]
    // 0x148c054: r4 = Instance_MainAxisAlignment
    //     0x148c054: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x148c058: ldr             x4, [x4, #0xa08]
    // 0x148c05c: StoreField: r3->field_13 = r4
    //     0x148c05c: stur            w4, [x3, #0x13]
    // 0x148c060: r5 = Instance_MainAxisSize
    //     0x148c060: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148c064: ldr             x5, [x5, #0xa10]
    // 0x148c068: ArrayStore: r3[0] = r5  ; List_4
    //     0x148c068: stur            w5, [x3, #0x17]
    // 0x148c06c: r6 = Instance_CrossAxisAlignment
    //     0x148c06c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x148c070: ldr             x6, [x6, #0xa18]
    // 0x148c074: StoreField: r3->field_1b = r6
    //     0x148c074: stur            w6, [x3, #0x1b]
    // 0x148c078: r7 = Instance_VerticalDirection
    //     0x148c078: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148c07c: ldr             x7, [x7, #0xa20]
    // 0x148c080: StoreField: r3->field_23 = r7
    //     0x148c080: stur            w7, [x3, #0x23]
    // 0x148c084: r8 = Instance_Clip
    //     0x148c084: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148c088: ldr             x8, [x8, #0x38]
    // 0x148c08c: StoreField: r3->field_2b = r8
    //     0x148c08c: stur            w8, [x3, #0x2b]
    // 0x148c090: StoreField: r3->field_2f = rZR
    //     0x148c090: stur            xzr, [x3, #0x2f]
    // 0x148c094: ldur            x1, [fp, #-0x60]
    // 0x148c098: StoreField: r3->field_b = r1
    //     0x148c098: stur            w1, [x3, #0xb]
    // 0x148c09c: r1 = Null
    //     0x148c09c: mov             x1, NULL
    // 0x148c0a0: r2 = 6
    //     0x148c0a0: movz            x2, #0x6
    // 0x148c0a4: r0 = AllocateArray()
    //     0x148c0a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148c0a8: mov             x2, x0
    // 0x148c0ac: ldur            x0, [fp, #-0x48]
    // 0x148c0b0: stur            x2, [fp, #-0x60]
    // 0x148c0b4: StoreField: r2->field_f = r0
    //     0x148c0b4: stur            w0, [x2, #0xf]
    // 0x148c0b8: r16 = Instance_SizedBox
    //     0x148c0b8: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x148c0bc: ldr             x16, [x16, #0xc70]
    // 0x148c0c0: StoreField: r2->field_13 = r16
    //     0x148c0c0: stur            w16, [x2, #0x13]
    // 0x148c0c4: ldur            x0, [fp, #-0x58]
    // 0x148c0c8: ArrayStore: r2[0] = r0  ; List_4
    //     0x148c0c8: stur            w0, [x2, #0x17]
    // 0x148c0cc: r1 = <Widget>
    //     0x148c0cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148c0d0: r0 = AllocateGrowableArray()
    //     0x148c0d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148c0d4: mov             x1, x0
    // 0x148c0d8: ldur            x0, [fp, #-0x60]
    // 0x148c0dc: stur            x1, [fp, #-0x48]
    // 0x148c0e0: StoreField: r1->field_f = r0
    //     0x148c0e0: stur            w0, [x1, #0xf]
    // 0x148c0e4: r2 = 6
    //     0x148c0e4: movz            x2, #0x6
    // 0x148c0e8: StoreField: r1->field_b = r2
    //     0x148c0e8: stur            w2, [x1, #0xb]
    // 0x148c0ec: r0 = Column()
    //     0x148c0ec: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x148c0f0: mov             x1, x0
    // 0x148c0f4: r0 = Instance_Axis
    //     0x148c0f4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x148c0f8: stur            x1, [fp, #-0x58]
    // 0x148c0fc: StoreField: r1->field_f = r0
    //     0x148c0fc: stur            w0, [x1, #0xf]
    // 0x148c100: r2 = Instance_MainAxisAlignment
    //     0x148c100: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x148c104: ldr             x2, [x2, #0xa08]
    // 0x148c108: StoreField: r1->field_13 = r2
    //     0x148c108: stur            w2, [x1, #0x13]
    // 0x148c10c: r3 = Instance_MainAxisSize
    //     0x148c10c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148c110: ldr             x3, [x3, #0xa10]
    // 0x148c114: ArrayStore: r1[0] = r3  ; List_4
    //     0x148c114: stur            w3, [x1, #0x17]
    // 0x148c118: r4 = Instance_CrossAxisAlignment
    //     0x148c118: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x148c11c: ldr             x4, [x4, #0x890]
    // 0x148c120: StoreField: r1->field_1b = r4
    //     0x148c120: stur            w4, [x1, #0x1b]
    // 0x148c124: r4 = Instance_VerticalDirection
    //     0x148c124: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148c128: ldr             x4, [x4, #0xa20]
    // 0x148c12c: StoreField: r1->field_23 = r4
    //     0x148c12c: stur            w4, [x1, #0x23]
    // 0x148c130: r5 = Instance_Clip
    //     0x148c130: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148c134: ldr             x5, [x5, #0x38]
    // 0x148c138: StoreField: r1->field_2b = r5
    //     0x148c138: stur            w5, [x1, #0x2b]
    // 0x148c13c: StoreField: r1->field_2f = rZR
    //     0x148c13c: stur            xzr, [x1, #0x2f]
    // 0x148c140: ldur            x6, [fp, #-0x48]
    // 0x148c144: StoreField: r1->field_b = r6
    //     0x148c144: stur            w6, [x1, #0xb]
    // 0x148c148: r0 = Padding()
    //     0x148c148: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148c14c: mov             x2, x0
    // 0x148c150: r0 = Instance_EdgeInsets
    //     0x148c150: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x148c154: ldr             x0, [x0, #0xa78]
    // 0x148c158: stur            x2, [fp, #-0x48]
    // 0x148c15c: StoreField: r2->field_f = r0
    //     0x148c15c: stur            w0, [x2, #0xf]
    // 0x148c160: ldur            x0, [fp, #-0x58]
    // 0x148c164: StoreField: r2->field_b = r0
    //     0x148c164: stur            w0, [x2, #0xb]
    // 0x148c168: r1 = <FlexParentData>
    //     0x148c168: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x148c16c: ldr             x1, [x1, #0xe00]
    // 0x148c170: r0 = Expanded()
    //     0x148c170: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x148c174: mov             x3, x0
    // 0x148c178: r0 = 1
    //     0x148c178: movz            x0, #0x1
    // 0x148c17c: stur            x3, [fp, #-0x58]
    // 0x148c180: StoreField: r3->field_13 = r0
    //     0x148c180: stur            x0, [x3, #0x13]
    // 0x148c184: r0 = Instance_FlexFit
    //     0x148c184: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x148c188: ldr             x0, [x0, #0xe08]
    // 0x148c18c: StoreField: r3->field_1b = r0
    //     0x148c18c: stur            w0, [x3, #0x1b]
    // 0x148c190: ldur            x0, [fp, #-0x48]
    // 0x148c194: StoreField: r3->field_b = r0
    //     0x148c194: stur            w0, [x3, #0xb]
    // 0x148c198: r1 = Null
    //     0x148c198: mov             x1, NULL
    // 0x148c19c: r2 = 6
    //     0x148c19c: movz            x2, #0x6
    // 0x148c1a0: r0 = AllocateArray()
    //     0x148c1a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148c1a4: mov             x2, x0
    // 0x148c1a8: ldur            x0, [fp, #-0x40]
    // 0x148c1ac: stur            x2, [fp, #-0x48]
    // 0x148c1b0: StoreField: r2->field_f = r0
    //     0x148c1b0: stur            w0, [x2, #0xf]
    // 0x148c1b4: ldur            x0, [fp, #-0x68]
    // 0x148c1b8: StoreField: r2->field_13 = r0
    //     0x148c1b8: stur            w0, [x2, #0x13]
    // 0x148c1bc: ldur            x0, [fp, #-0x58]
    // 0x148c1c0: ArrayStore: r2[0] = r0  ; List_4
    //     0x148c1c0: stur            w0, [x2, #0x17]
    // 0x148c1c4: r1 = <Widget>
    //     0x148c1c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148c1c8: r0 = AllocateGrowableArray()
    //     0x148c1c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148c1cc: mov             x1, x0
    // 0x148c1d0: ldur            x0, [fp, #-0x48]
    // 0x148c1d4: stur            x1, [fp, #-0x40]
    // 0x148c1d8: StoreField: r1->field_f = r0
    //     0x148c1d8: stur            w0, [x1, #0xf]
    // 0x148c1dc: r0 = 6
    //     0x148c1dc: movz            x0, #0x6
    // 0x148c1e0: StoreField: r1->field_b = r0
    //     0x148c1e0: stur            w0, [x1, #0xb]
    // 0x148c1e4: r0 = Row()
    //     0x148c1e4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x148c1e8: mov             x1, x0
    // 0x148c1ec: r0 = Instance_Axis
    //     0x148c1ec: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x148c1f0: stur            x1, [fp, #-0x48]
    // 0x148c1f4: StoreField: r1->field_f = r0
    //     0x148c1f4: stur            w0, [x1, #0xf]
    // 0x148c1f8: r2 = Instance_MainAxisAlignment
    //     0x148c1f8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x148c1fc: ldr             x2, [x2, #0xa08]
    // 0x148c200: StoreField: r1->field_13 = r2
    //     0x148c200: stur            w2, [x1, #0x13]
    // 0x148c204: r3 = Instance_MainAxisSize
    //     0x148c204: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148c208: ldr             x3, [x3, #0xa10]
    // 0x148c20c: ArrayStore: r1[0] = r3  ; List_4
    //     0x148c20c: stur            w3, [x1, #0x17]
    // 0x148c210: r4 = Instance_CrossAxisAlignment
    //     0x148c210: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x148c214: ldr             x4, [x4, #0xa18]
    // 0x148c218: StoreField: r1->field_1b = r4
    //     0x148c218: stur            w4, [x1, #0x1b]
    // 0x148c21c: r5 = Instance_VerticalDirection
    //     0x148c21c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148c220: ldr             x5, [x5, #0xa20]
    // 0x148c224: StoreField: r1->field_23 = r5
    //     0x148c224: stur            w5, [x1, #0x23]
    // 0x148c228: r6 = Instance_Clip
    //     0x148c228: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148c22c: ldr             x6, [x6, #0x38]
    // 0x148c230: StoreField: r1->field_2b = r6
    //     0x148c230: stur            w6, [x1, #0x2b]
    // 0x148c234: StoreField: r1->field_2f = rZR
    //     0x148c234: stur            xzr, [x1, #0x2f]
    // 0x148c238: ldur            x7, [fp, #-0x40]
    // 0x148c23c: StoreField: r1->field_b = r7
    //     0x148c23c: stur            w7, [x1, #0xb]
    // 0x148c240: r0 = Container()
    //     0x148c240: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x148c244: stur            x0, [fp, #-0x40]
    // 0x148c248: ldur            x16, [fp, #-0x50]
    // 0x148c24c: ldur            lr, [fp, #-0x48]
    // 0x148c250: stp             lr, x16, [SP]
    // 0x148c254: mov             x1, x0
    // 0x148c258: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x148c258: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x148c25c: ldr             x4, [x4, #0x88]
    // 0x148c260: r0 = Container()
    //     0x148c260: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x148c264: r0 = Padding()
    //     0x148c264: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148c268: mov             x1, x0
    // 0x148c26c: r0 = Instance_EdgeInsets
    //     0x148c26c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0x148c270: ldr             x0, [x0, #0x778]
    // 0x148c274: stur            x1, [fp, #-0x48]
    // 0x148c278: StoreField: r1->field_f = r0
    //     0x148c278: stur            w0, [x1, #0xf]
    // 0x148c27c: ldur            x0, [fp, #-0x40]
    // 0x148c280: StoreField: r1->field_b = r0
    //     0x148c280: stur            w0, [x1, #0xb]
    // 0x148c284: r0 = Visibility()
    //     0x148c284: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x148c288: mov             x2, x0
    // 0x148c28c: ldur            x0, [fp, #-0x48]
    // 0x148c290: stur            x2, [fp, #-0x40]
    // 0x148c294: StoreField: r2->field_b = r0
    //     0x148c294: stur            w0, [x2, #0xb]
    // 0x148c298: r0 = Instance_SizedBox
    //     0x148c298: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x148c29c: StoreField: r2->field_f = r0
    //     0x148c29c: stur            w0, [x2, #0xf]
    // 0x148c2a0: ldur            x0, [fp, #-0x30]
    // 0x148c2a4: StoreField: r2->field_13 = r0
    //     0x148c2a4: stur            w0, [x2, #0x13]
    // 0x148c2a8: r0 = false
    //     0x148c2a8: add             x0, NULL, #0x30  ; false
    // 0x148c2ac: ArrayStore: r2[0] = r0  ; List_4
    //     0x148c2ac: stur            w0, [x2, #0x17]
    // 0x148c2b0: StoreField: r2->field_1b = r0
    //     0x148c2b0: stur            w0, [x2, #0x1b]
    // 0x148c2b4: StoreField: r2->field_1f = r0
    //     0x148c2b4: stur            w0, [x2, #0x1f]
    // 0x148c2b8: StoreField: r2->field_23 = r0
    //     0x148c2b8: stur            w0, [x2, #0x23]
    // 0x148c2bc: StoreField: r2->field_27 = r0
    //     0x148c2bc: stur            w0, [x2, #0x27]
    // 0x148c2c0: StoreField: r2->field_2b = r0
    //     0x148c2c0: stur            w0, [x2, #0x2b]
    // 0x148c2c4: ldur            x3, [fp, #-0x28]
    // 0x148c2c8: LoadField: r1 = r3->field_b
    //     0x148c2c8: ldur            w1, [x3, #0xb]
    // 0x148c2cc: LoadField: r4 = r3->field_f
    //     0x148c2cc: ldur            w4, [x3, #0xf]
    // 0x148c2d0: DecompressPointer r4
    //     0x148c2d0: add             x4, x4, HEAP, lsl #32
    // 0x148c2d4: LoadField: r5 = r4->field_b
    //     0x148c2d4: ldur            w5, [x4, #0xb]
    // 0x148c2d8: r4 = LoadInt32Instr(r1)
    //     0x148c2d8: sbfx            x4, x1, #1, #0x1f
    // 0x148c2dc: stur            x4, [fp, #-0x80]
    // 0x148c2e0: r1 = LoadInt32Instr(r5)
    //     0x148c2e0: sbfx            x1, x5, #1, #0x1f
    // 0x148c2e4: cmp             x4, x1
    // 0x148c2e8: b.ne            #0x148c2f4
    // 0x148c2ec: mov             x1, x3
    // 0x148c2f0: r0 = _growToNextCapacity()
    //     0x148c2f0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x148c2f4: ldur            x2, [fp, #-0x28]
    // 0x148c2f8: ldur            x3, [fp, #-0x80]
    // 0x148c2fc: add             x0, x3, #1
    // 0x148c300: lsl             x1, x0, #1
    // 0x148c304: StoreField: r2->field_b = r1
    //     0x148c304: stur            w1, [x2, #0xb]
    // 0x148c308: LoadField: r1 = r2->field_f
    //     0x148c308: ldur            w1, [x2, #0xf]
    // 0x148c30c: DecompressPointer r1
    //     0x148c30c: add             x1, x1, HEAP, lsl #32
    // 0x148c310: ldur            x0, [fp, #-0x40]
    // 0x148c314: ArrayStore: r1[r3] = r0  ; List_4
    //     0x148c314: add             x25, x1, x3, lsl #2
    //     0x148c318: add             x25, x25, #0xf
    //     0x148c31c: str             w0, [x25]
    //     0x148c320: tbz             w0, #0, #0x148c33c
    //     0x148c324: ldurb           w16, [x1, #-1]
    //     0x148c328: ldurb           w17, [x0, #-1]
    //     0x148c32c: and             x16, x17, x16, lsr #2
    //     0x148c330: tst             x16, HEAP, lsr #32
    //     0x148c334: b.eq            #0x148c33c
    //     0x148c338: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x148c33c: b               #0x148c344
    // 0x148c340: ldur            x2, [fp, #-0x28]
    // 0x148c344: ldur            x0, [fp, #-0x10]
    // 0x148c348: LoadField: r1 = r0->field_f
    //     0x148c348: ldur            w1, [x0, #0xf]
    // 0x148c34c: DecompressPointer r1
    //     0x148c34c: add             x1, x1, HEAP, lsl #32
    // 0x148c350: r0 = controller()
    //     0x148c350: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148c354: LoadField: r1 = r0->field_73
    //     0x148c354: ldur            w1, [x0, #0x73]
    // 0x148c358: DecompressPointer r1
    //     0x148c358: add             x1, x1, HEAP, lsl #32
    // 0x148c35c: r0 = value()
    //     0x148c35c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148c360: LoadField: r1 = r0->field_b
    //     0x148c360: ldur            w1, [x0, #0xb]
    // 0x148c364: DecompressPointer r1
    //     0x148c364: add             x1, x1, HEAP, lsl #32
    // 0x148c368: cmp             w1, NULL
    // 0x148c36c: b.ne            #0x148c378
    // 0x148c370: r0 = Null
    //     0x148c370: mov             x0, NULL
    // 0x148c374: b               #0x148c39c
    // 0x148c378: LoadField: r0 = r1->field_f
    //     0x148c378: ldur            w0, [x1, #0xf]
    // 0x148c37c: DecompressPointer r0
    //     0x148c37c: add             x0, x0, HEAP, lsl #32
    // 0x148c380: cmp             w0, NULL
    // 0x148c384: b.ne            #0x148c390
    // 0x148c388: r0 = Null
    //     0x148c388: mov             x0, NULL
    // 0x148c38c: b               #0x148c39c
    // 0x148c390: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x148c390: ldur            w1, [x0, #0x17]
    // 0x148c394: DecompressPointer r1
    //     0x148c394: add             x1, x1, HEAP, lsl #32
    // 0x148c398: mov             x0, x1
    // 0x148c39c: cmp             w0, NULL
    // 0x148c3a0: b.ne            #0x148c3b4
    // 0x148c3a4: r1 = <BEntities>
    //     0x148c3a4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23130] TypeArguments: <BEntities>
    //     0x148c3a8: ldr             x1, [x1, #0x130]
    // 0x148c3ac: r2 = 0
    //     0x148c3ac: movz            x2, #0
    // 0x148c3b0: r0 = AllocateArray()
    //     0x148c3b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148c3b4: ldur            x1, [fp, #-0x28]
    // 0x148c3b8: r2 = LoadClassIdInstr(r0)
    //     0x148c3b8: ldur            x2, [x0, #-1]
    //     0x148c3bc: ubfx            x2, x2, #0xc, #0x14
    // 0x148c3c0: str             x0, [SP]
    // 0x148c3c4: mov             x0, x2
    // 0x148c3c8: r0 = GDT[cid_x0 + 0xc898]()
    //     0x148c3c8: movz            x17, #0xc898
    //     0x148c3cc: add             lr, x0, x17
    //     0x148c3d0: ldr             lr, [x21, lr, lsl #3]
    //     0x148c3d4: blr             lr
    // 0x148c3d8: ldur            x2, [fp, #-0x10]
    // 0x148c3dc: r1 = Function '<anonymous closure>':.
    //     0x148c3dc: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c58] AnonymousClosure: (0x148f098), in [package:customer_app/app/presentation/views/cosmetic/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x148b85c)
    //     0x148c3e0: ldr             x1, [x1, #0xc58]
    // 0x148c3e4: stur            x0, [fp, #-0x30]
    // 0x148c3e8: r0 = AllocateClosure()
    //     0x148c3e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148c3ec: stur            x0, [fp, #-0x40]
    // 0x148c3f0: r0 = ListView()
    //     0x148c3f0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x148c3f4: stur            x0, [fp, #-0x48]
    // 0x148c3f8: r16 = true
    //     0x148c3f8: add             x16, NULL, #0x20  ; true
    // 0x148c3fc: r30 = false
    //     0x148c3fc: add             lr, NULL, #0x30  ; false
    // 0x148c400: stp             lr, x16, [SP, #8]
    // 0x148c404: r16 = Instance_NeverScrollableScrollPhysics
    //     0x148c404: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x148c408: ldr             x16, [x16, #0x1c8]
    // 0x148c40c: str             x16, [SP]
    // 0x148c410: mov             x1, x0
    // 0x148c414: ldur            x2, [fp, #-0x40]
    // 0x148c418: ldur            x3, [fp, #-0x30]
    // 0x148c41c: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x5, primary, 0x4, shrinkWrap, 0x3, null]
    //     0x148c41c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33fd8] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x5, "primary", 0x4, "shrinkWrap", 0x3, Null]
    //     0x148c420: ldr             x4, [x4, #0xfd8]
    // 0x148c424: r0 = ListView.builder()
    //     0x148c424: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x148c428: ldur            x0, [fp, #-0x28]
    // 0x148c42c: LoadField: r1 = r0->field_b
    //     0x148c42c: ldur            w1, [x0, #0xb]
    // 0x148c430: LoadField: r2 = r0->field_f
    //     0x148c430: ldur            w2, [x0, #0xf]
    // 0x148c434: DecompressPointer r2
    //     0x148c434: add             x2, x2, HEAP, lsl #32
    // 0x148c438: LoadField: r3 = r2->field_b
    //     0x148c438: ldur            w3, [x2, #0xb]
    // 0x148c43c: r2 = LoadInt32Instr(r1)
    //     0x148c43c: sbfx            x2, x1, #1, #0x1f
    // 0x148c440: stur            x2, [fp, #-0x80]
    // 0x148c444: r1 = LoadInt32Instr(r3)
    //     0x148c444: sbfx            x1, x3, #1, #0x1f
    // 0x148c448: cmp             x2, x1
    // 0x148c44c: b.ne            #0x148c458
    // 0x148c450: mov             x1, x0
    // 0x148c454: r0 = _growToNextCapacity()
    //     0x148c454: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x148c458: ldur            x4, [fp, #-0x10]
    // 0x148c45c: ldur            x5, [fp, #-0x38]
    // 0x148c460: ldur            x2, [fp, #-0x28]
    // 0x148c464: ldur            x3, [fp, #-0x80]
    // 0x148c468: add             x0, x3, #1
    // 0x148c46c: lsl             x1, x0, #1
    // 0x148c470: StoreField: r2->field_b = r1
    //     0x148c470: stur            w1, [x2, #0xb]
    // 0x148c474: LoadField: r1 = r2->field_f
    //     0x148c474: ldur            w1, [x2, #0xf]
    // 0x148c478: DecompressPointer r1
    //     0x148c478: add             x1, x1, HEAP, lsl #32
    // 0x148c47c: ldur            x0, [fp, #-0x48]
    // 0x148c480: ArrayStore: r1[r3] = r0  ; List_4
    //     0x148c480: add             x25, x1, x3, lsl #2
    //     0x148c484: add             x25, x25, #0xf
    //     0x148c488: str             w0, [x25]
    //     0x148c48c: tbz             w0, #0, #0x148c4a8
    //     0x148c490: ldurb           w16, [x1, #-1]
    //     0x148c494: ldurb           w17, [x0, #-1]
    //     0x148c498: and             x16, x17, x16, lsr #2
    //     0x148c49c: tst             x16, HEAP, lsr #32
    //     0x148c4a0: b.eq            #0x148c4a8
    //     0x148c4a4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x148c4a8: r0 = Column()
    //     0x148c4a8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x148c4ac: mov             x1, x0
    // 0x148c4b0: r0 = Instance_Axis
    //     0x148c4b0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x148c4b4: stur            x1, [fp, #-0x30]
    // 0x148c4b8: StoreField: r1->field_f = r0
    //     0x148c4b8: stur            w0, [x1, #0xf]
    // 0x148c4bc: r0 = Instance_MainAxisAlignment
    //     0x148c4bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x148c4c0: ldr             x0, [x0, #0xa08]
    // 0x148c4c4: StoreField: r1->field_13 = r0
    //     0x148c4c4: stur            w0, [x1, #0x13]
    // 0x148c4c8: r0 = Instance_MainAxisSize
    //     0x148c4c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148c4cc: ldr             x0, [x0, #0xa10]
    // 0x148c4d0: ArrayStore: r1[0] = r0  ; List_4
    //     0x148c4d0: stur            w0, [x1, #0x17]
    // 0x148c4d4: r2 = Instance_CrossAxisAlignment
    //     0x148c4d4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x148c4d8: ldr             x2, [x2, #0xa18]
    // 0x148c4dc: StoreField: r1->field_1b = r2
    //     0x148c4dc: stur            w2, [x1, #0x1b]
    // 0x148c4e0: r3 = Instance_VerticalDirection
    //     0x148c4e0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148c4e4: ldr             x3, [x3, #0xa20]
    // 0x148c4e8: StoreField: r1->field_23 = r3
    //     0x148c4e8: stur            w3, [x1, #0x23]
    // 0x148c4ec: r4 = Instance_Clip
    //     0x148c4ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148c4f0: ldr             x4, [x4, #0x38]
    // 0x148c4f4: StoreField: r1->field_2b = r4
    //     0x148c4f4: stur            w4, [x1, #0x2b]
    // 0x148c4f8: StoreField: r1->field_2f = rZR
    //     0x148c4f8: stur            xzr, [x1, #0x2f]
    // 0x148c4fc: ldur            x5, [fp, #-0x28]
    // 0x148c500: StoreField: r1->field_b = r5
    //     0x148c500: stur            w5, [x1, #0xb]
    // 0x148c504: r0 = Accordion()
    //     0x148c504: bl              #0xa06e8c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0x148c508: mov             x1, x0
    // 0x148c50c: ldur            x0, [fp, #-0x38]
    // 0x148c510: stur            x1, [fp, #-0x28]
    // 0x148c514: StoreField: r1->field_b = r0
    //     0x148c514: stur            w0, [x1, #0xb]
    // 0x148c518: ldur            x0, [fp, #-0x30]
    // 0x148c51c: StoreField: r1->field_13 = r0
    //     0x148c51c: stur            w0, [x1, #0x13]
    // 0x148c520: r0 = false
    //     0x148c520: add             x0, NULL, #0x30  ; false
    // 0x148c524: ArrayStore: r1[0] = r0  ; List_4
    //     0x148c524: stur            w0, [x1, #0x17]
    // 0x148c528: d0 = 25.000000
    //     0x148c528: fmov            d0, #25.00000000
    // 0x148c52c: StoreField: r1->field_1b = d0
    //     0x148c52c: stur            d0, [x1, #0x1b]
    // 0x148c530: r0 = true
    //     0x148c530: add             x0, NULL, #0x20  ; true
    // 0x148c534: StoreField: r1->field_23 = r0
    //     0x148c534: stur            w0, [x1, #0x23]
    // 0x148c538: r0 = Padding()
    //     0x148c538: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148c53c: mov             x1, x0
    // 0x148c540: r0 = Instance_EdgeInsets
    //     0x148c540: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x148c544: ldr             x0, [x0, #0x668]
    // 0x148c548: stur            x1, [fp, #-0x30]
    // 0x148c54c: StoreField: r1->field_f = r0
    //     0x148c54c: stur            w0, [x1, #0xf]
    // 0x148c550: ldur            x2, [fp, #-0x28]
    // 0x148c554: StoreField: r1->field_b = r2
    //     0x148c554: stur            w2, [x1, #0xb]
    // 0x148c558: r0 = Container()
    //     0x148c558: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x148c55c: stur            x0, [fp, #-0x28]
    // 0x148c560: ldur            x16, [fp, #-0x20]
    // 0x148c564: ldur            lr, [fp, #-0x30]
    // 0x148c568: stp             lr, x16, [SP]
    // 0x148c56c: mov             x1, x0
    // 0x148c570: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x148c570: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x148c574: ldr             x4, [x4, #0x88]
    // 0x148c578: r0 = Container()
    //     0x148c578: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x148c57c: r0 = Padding()
    //     0x148c57c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148c580: mov             x2, x0
    // 0x148c584: r0 = Instance_EdgeInsets
    //     0x148c584: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f30] Obj!EdgeInsets@d57b31
    //     0x148c588: ldr             x0, [x0, #0xf30]
    // 0x148c58c: stur            x2, [fp, #-0x20]
    // 0x148c590: StoreField: r2->field_f = r0
    //     0x148c590: stur            w0, [x2, #0xf]
    // 0x148c594: ldur            x0, [fp, #-0x28]
    // 0x148c598: StoreField: r2->field_b = r0
    //     0x148c598: stur            w0, [x2, #0xb]
    // 0x148c59c: r1 = Instance_Color
    //     0x148c59c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148c5a0: d0 = 0.100000
    //     0x148c5a0: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x148c5a4: r0 = withOpacity()
    //     0x148c5a4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x148c5a8: stur            x0, [fp, #-0x28]
    // 0x148c5ac: r0 = Divider()
    //     0x148c5ac: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x148c5b0: mov             x2, x0
    // 0x148c5b4: ldur            x0, [fp, #-0x28]
    // 0x148c5b8: stur            x2, [fp, #-0x30]
    // 0x148c5bc: StoreField: r2->field_1f = r0
    //     0x148c5bc: stur            w0, [x2, #0x1f]
    // 0x148c5c0: ldur            x0, [fp, #-0x10]
    // 0x148c5c4: LoadField: r1 = r0->field_f
    //     0x148c5c4: ldur            w1, [x0, #0xf]
    // 0x148c5c8: DecompressPointer r1
    //     0x148c5c8: add             x1, x1, HEAP, lsl #32
    // 0x148c5cc: r0 = controller()
    //     0x148c5cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148c5d0: LoadField: r1 = r0->field_87
    //     0x148c5d0: ldur            w1, [x0, #0x87]
    // 0x148c5d4: DecompressPointer r1
    //     0x148c5d4: add             x1, x1, HEAP, lsl #32
    // 0x148c5d8: r0 = value()
    //     0x148c5d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148c5dc: LoadField: r1 = r0->field_2f
    //     0x148c5dc: ldur            w1, [x0, #0x2f]
    // 0x148c5e0: DecompressPointer r1
    //     0x148c5e0: add             x1, x1, HEAP, lsl #32
    // 0x148c5e4: cmp             w1, NULL
    // 0x148c5e8: b.ne            #0x148c5f4
    // 0x148c5ec: r0 = ""
    //     0x148c5ec: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148c5f0: b               #0x148c5f8
    // 0x148c5f4: mov             x0, x1
    // 0x148c5f8: ldur            x2, [fp, #-0x10]
    // 0x148c5fc: stur            x0, [fp, #-0x28]
    // 0x148c600: LoadField: r1 = r2->field_13
    //     0x148c600: ldur            w1, [x2, #0x13]
    // 0x148c604: DecompressPointer r1
    //     0x148c604: add             x1, x1, HEAP, lsl #32
    // 0x148c608: r0 = of()
    //     0x148c608: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148c60c: LoadField: r1 = r0->field_87
    //     0x148c60c: ldur            w1, [x0, #0x87]
    // 0x148c610: DecompressPointer r1
    //     0x148c610: add             x1, x1, HEAP, lsl #32
    // 0x148c614: LoadField: r0 = r1->field_7
    //     0x148c614: ldur            w0, [x1, #7]
    // 0x148c618: DecompressPointer r0
    //     0x148c618: add             x0, x0, HEAP, lsl #32
    // 0x148c61c: r16 = Instance_Color
    //     0x148c61c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148c620: r30 = 16.000000
    //     0x148c620: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x148c624: ldr             lr, [lr, #0x188]
    // 0x148c628: stp             lr, x16, [SP]
    // 0x148c62c: mov             x1, x0
    // 0x148c630: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x148c630: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x148c634: ldr             x4, [x4, #0x9b8]
    // 0x148c638: r0 = copyWith()
    //     0x148c638: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148c63c: stur            x0, [fp, #-0x38]
    // 0x148c640: r0 = Text()
    //     0x148c640: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148c644: mov             x1, x0
    // 0x148c648: ldur            x0, [fp, #-0x28]
    // 0x148c64c: stur            x1, [fp, #-0x40]
    // 0x148c650: StoreField: r1->field_b = r0
    //     0x148c650: stur            w0, [x1, #0xb]
    // 0x148c654: ldur            x0, [fp, #-0x38]
    // 0x148c658: StoreField: r1->field_13 = r0
    //     0x148c658: stur            w0, [x1, #0x13]
    // 0x148c65c: r0 = SvgPicture()
    //     0x148c65c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x148c660: stur            x0, [fp, #-0x28]
    // 0x148c664: r16 = "return order"
    //     0x148c664: add             x16, PP, #0x38, lsl #12  ; [pp+0x38c78] "return order"
    //     0x148c668: ldr             x16, [x16, #0xc78]
    // 0x148c66c: r30 = Instance_BoxFit
    //     0x148c66c: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x148c670: ldr             lr, [lr, #0xb18]
    // 0x148c674: stp             lr, x16, [SP]
    // 0x148c678: mov             x1, x0
    // 0x148c67c: r2 = "assets/images/secure_icon.svg"
    //     0x148c67c: add             x2, PP, #0x38, lsl #12  ; [pp+0x38c80] "assets/images/secure_icon.svg"
    //     0x148c680: ldr             x2, [x2, #0xc80]
    // 0x148c684: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0x148c684: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0x148c688: ldr             x4, [x4, #0xb28]
    // 0x148c68c: r0 = SvgPicture.asset()
    //     0x148c68c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x148c690: r1 = Null
    //     0x148c690: mov             x1, NULL
    // 0x148c694: r2 = 4
    //     0x148c694: movz            x2, #0x4
    // 0x148c698: r0 = AllocateArray()
    //     0x148c698: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148c69c: mov             x2, x0
    // 0x148c6a0: ldur            x0, [fp, #-0x40]
    // 0x148c6a4: stur            x2, [fp, #-0x38]
    // 0x148c6a8: StoreField: r2->field_f = r0
    //     0x148c6a8: stur            w0, [x2, #0xf]
    // 0x148c6ac: ldur            x0, [fp, #-0x28]
    // 0x148c6b0: StoreField: r2->field_13 = r0
    //     0x148c6b0: stur            w0, [x2, #0x13]
    // 0x148c6b4: r1 = <Widget>
    //     0x148c6b4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148c6b8: r0 = AllocateGrowableArray()
    //     0x148c6b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148c6bc: mov             x1, x0
    // 0x148c6c0: ldur            x0, [fp, #-0x38]
    // 0x148c6c4: stur            x1, [fp, #-0x28]
    // 0x148c6c8: StoreField: r1->field_f = r0
    //     0x148c6c8: stur            w0, [x1, #0xf]
    // 0x148c6cc: r0 = 4
    //     0x148c6cc: movz            x0, #0x4
    // 0x148c6d0: StoreField: r1->field_b = r0
    //     0x148c6d0: stur            w0, [x1, #0xb]
    // 0x148c6d4: r0 = Row()
    //     0x148c6d4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x148c6d8: mov             x1, x0
    // 0x148c6dc: r0 = Instance_Axis
    //     0x148c6dc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x148c6e0: stur            x1, [fp, #-0x38]
    // 0x148c6e4: StoreField: r1->field_f = r0
    //     0x148c6e4: stur            w0, [x1, #0xf]
    // 0x148c6e8: r0 = Instance_MainAxisAlignment
    //     0x148c6e8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x148c6ec: ldr             x0, [x0, #0xa8]
    // 0x148c6f0: StoreField: r1->field_13 = r0
    //     0x148c6f0: stur            w0, [x1, #0x13]
    // 0x148c6f4: r0 = Instance_MainAxisSize
    //     0x148c6f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148c6f8: ldr             x0, [x0, #0xa10]
    // 0x148c6fc: ArrayStore: r1[0] = r0  ; List_4
    //     0x148c6fc: stur            w0, [x1, #0x17]
    // 0x148c700: r0 = Instance_CrossAxisAlignment
    //     0x148c700: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x148c704: ldr             x0, [x0, #0xa18]
    // 0x148c708: StoreField: r1->field_1b = r0
    //     0x148c708: stur            w0, [x1, #0x1b]
    // 0x148c70c: r0 = Instance_VerticalDirection
    //     0x148c70c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148c710: ldr             x0, [x0, #0xa20]
    // 0x148c714: StoreField: r1->field_23 = r0
    //     0x148c714: stur            w0, [x1, #0x23]
    // 0x148c718: r0 = Instance_Clip
    //     0x148c718: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148c71c: ldr             x0, [x0, #0x38]
    // 0x148c720: StoreField: r1->field_2b = r0
    //     0x148c720: stur            w0, [x1, #0x2b]
    // 0x148c724: StoreField: r1->field_2f = rZR
    //     0x148c724: stur            xzr, [x1, #0x2f]
    // 0x148c728: ldur            x0, [fp, #-0x28]
    // 0x148c72c: StoreField: r1->field_b = r0
    //     0x148c72c: stur            w0, [x1, #0xb]
    // 0x148c730: r0 = Padding()
    //     0x148c730: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148c734: mov             x2, x0
    // 0x148c738: r0 = Instance_EdgeInsets
    //     0x148c738: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0x148c73c: ldr             x0, [x0, #0xd0]
    // 0x148c740: stur            x2, [fp, #-0x28]
    // 0x148c744: StoreField: r2->field_f = r0
    //     0x148c744: stur            w0, [x2, #0xf]
    // 0x148c748: ldur            x0, [fp, #-0x38]
    // 0x148c74c: StoreField: r2->field_b = r0
    //     0x148c74c: stur            w0, [x2, #0xb]
    // 0x148c750: ldur            x0, [fp, #-0x10]
    // 0x148c754: LoadField: r1 = r0->field_f
    //     0x148c754: ldur            w1, [x0, #0xf]
    // 0x148c758: DecompressPointer r1
    //     0x148c758: add             x1, x1, HEAP, lsl #32
    // 0x148c75c: r0 = controller()
    //     0x148c75c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148c760: LoadField: r1 = r0->field_87
    //     0x148c760: ldur            w1, [x0, #0x87]
    // 0x148c764: DecompressPointer r1
    //     0x148c764: add             x1, x1, HEAP, lsl #32
    // 0x148c768: r0 = value()
    //     0x148c768: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148c76c: LoadField: r1 = r0->field_1f
    //     0x148c76c: ldur            w1, [x0, #0x1f]
    // 0x148c770: DecompressPointer r1
    //     0x148c770: add             x1, x1, HEAP, lsl #32
    // 0x148c774: cmp             w1, NULL
    // 0x148c778: b.ne            #0x148c790
    // 0x148c77c: r1 = <PaymentOptions>
    //     0x148c77c: add             x1, PP, #0x22, lsl #12  ; [pp+0x225a8] TypeArguments: <PaymentOptions>
    //     0x148c780: ldr             x1, [x1, #0x5a8]
    // 0x148c784: r2 = 0
    //     0x148c784: movz            x2, #0
    // 0x148c788: r0 = AllocateArray()
    //     0x148c788: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148c78c: b               #0x148c794
    // 0x148c790: mov             x0, x1
    // 0x148c794: ldur            x2, [fp, #-0x10]
    // 0x148c798: ldur            x5, [fp, #-0x18]
    // 0x148c79c: ldur            x4, [fp, #-0x20]
    // 0x148c7a0: ldur            x3, [fp, #-0x30]
    // 0x148c7a4: ldur            x1, [fp, #-0x28]
    // 0x148c7a8: r6 = LoadClassIdInstr(r0)
    //     0x148c7a8: ldur            x6, [x0, #-1]
    //     0x148c7ac: ubfx            x6, x6, #0xc, #0x14
    // 0x148c7b0: str             x0, [SP]
    // 0x148c7b4: mov             x0, x6
    // 0x148c7b8: r0 = GDT[cid_x0 + 0xc898]()
    //     0x148c7b8: movz            x17, #0xc898
    //     0x148c7bc: add             lr, x0, x17
    //     0x148c7c0: ldr             lr, [x21, lr, lsl #3]
    //     0x148c7c4: blr             lr
    // 0x148c7c8: r3 = LoadInt32Instr(r0)
    //     0x148c7c8: sbfx            x3, x0, #1, #0x1f
    // 0x148c7cc: ldur            x2, [fp, #-0x10]
    // 0x148c7d0: stur            x3, [fp, #-0x80]
    // 0x148c7d4: r1 = Function '<anonymous closure>':.
    //     0x148c7d4: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c60] AnonymousClosure: (0x148eb70), in [package:customer_app/app/presentation/views/cosmetic/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x148b85c)
    //     0x148c7d8: ldr             x1, [x1, #0xc60]
    // 0x148c7dc: r0 = AllocateClosure()
    //     0x148c7dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148c7e0: r1 = Function '<anonymous closure>':.
    //     0x148c7e0: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c68] AnonymousClosure: (0x148ca10), in [package:customer_app/app/presentation/views/glass/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x14d817c)
    //     0x148c7e4: ldr             x1, [x1, #0xc68]
    // 0x148c7e8: r2 = Null
    //     0x148c7e8: mov             x2, NULL
    // 0x148c7ec: stur            x0, [fp, #-0x38]
    // 0x148c7f0: r0 = AllocateClosure()
    //     0x148c7f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148c7f4: stur            x0, [fp, #-0x40]
    // 0x148c7f8: r0 = ListView()
    //     0x148c7f8: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x148c7fc: stur            x0, [fp, #-0x48]
    // 0x148c800: r16 = true
    //     0x148c800: add             x16, NULL, #0x20  ; true
    // 0x148c804: r30 = false
    //     0x148c804: add             lr, NULL, #0x30  ; false
    // 0x148c808: stp             lr, x16, [SP]
    // 0x148c80c: mov             x1, x0
    // 0x148c810: ldur            x2, [fp, #-0x38]
    // 0x148c814: ldur            x3, [fp, #-0x80]
    // 0x148c818: ldur            x5, [fp, #-0x40]
    // 0x148c81c: r4 = const [0, 0x6, 0x2, 0x4, primary, 0x5, shrinkWrap, 0x4, null]
    //     0x148c81c: add             x4, PP, #0x38, lsl #12  ; [pp+0x38c98] List(9) [0, 0x6, 0x2, 0x4, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0x148c820: ldr             x4, [x4, #0xc98]
    // 0x148c824: r0 = ListView.separated()
    //     0x148c824: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x148c828: r0 = Card()
    //     0x148c828: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0x148c82c: mov             x1, x0
    // 0x148c830: r0 = Instance_Color
    //     0x148c830: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x148c834: stur            x1, [fp, #-0x38]
    // 0x148c838: StoreField: r1->field_b = r0
    //     0x148c838: stur            w0, [x1, #0xb]
    // 0x148c83c: r0 = 0.000000
    //     0x148c83c: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x148c840: ArrayStore: r1[0] = r0  ; List_4
    //     0x148c840: stur            w0, [x1, #0x17]
    // 0x148c844: r0 = Instance_RoundedRectangleBorder
    //     0x148c844: add             x0, PP, #0x43, lsl #12  ; [pp+0x438d0] Obj!RoundedRectangleBorder@d5ac11
    //     0x148c848: ldr             x0, [x0, #0x8d0]
    // 0x148c84c: StoreField: r1->field_1b = r0
    //     0x148c84c: stur            w0, [x1, #0x1b]
    // 0x148c850: r0 = true
    //     0x148c850: add             x0, NULL, #0x20  ; true
    // 0x148c854: StoreField: r1->field_1f = r0
    //     0x148c854: stur            w0, [x1, #0x1f]
    // 0x148c858: r2 = Instance_EdgeInsets
    //     0x148c858: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x148c85c: StoreField: r1->field_27 = r2
    //     0x148c85c: stur            w2, [x1, #0x27]
    // 0x148c860: ldur            x2, [fp, #-0x48]
    // 0x148c864: StoreField: r1->field_2f = r2
    //     0x148c864: stur            w2, [x1, #0x2f]
    // 0x148c868: StoreField: r1->field_2b = r0
    //     0x148c868: stur            w0, [x1, #0x2b]
    // 0x148c86c: r2 = Instance__CardVariant
    //     0x148c86c: add             x2, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0x148c870: ldr             x2, [x2, #0xa68]
    // 0x148c874: StoreField: r1->field_33 = r2
    //     0x148c874: stur            w2, [x1, #0x33]
    // 0x148c878: r0 = Padding()
    //     0x148c878: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148c87c: mov             x2, x0
    // 0x148c880: r0 = Instance_EdgeInsets
    //     0x148c880: add             x0, PP, #0x40, lsl #12  ; [pp+0x40de8] Obj!EdgeInsets@d57351
    //     0x148c884: ldr             x0, [x0, #0xde8]
    // 0x148c888: stur            x2, [fp, #-0x40]
    // 0x148c88c: StoreField: r2->field_f = r0
    //     0x148c88c: stur            w0, [x2, #0xf]
    // 0x148c890: ldur            x0, [fp, #-0x38]
    // 0x148c894: StoreField: r2->field_b = r0
    //     0x148c894: stur            w0, [x2, #0xb]
    // 0x148c898: ldur            x0, [fp, #-0x10]
    // 0x148c89c: LoadField: r1 = r0->field_f
    //     0x148c89c: ldur            w1, [x0, #0xf]
    // 0x148c8a0: DecompressPointer r1
    //     0x148c8a0: add             x1, x1, HEAP, lsl #32
    // 0x148c8a4: r0 = controller()
    //     0x148c8a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148c8a8: LoadField: r1 = r0->field_57
    //     0x148c8a8: ldur            w1, [x0, #0x57]
    // 0x148c8ac: DecompressPointer r1
    //     0x148c8ac: add             x1, x1, HEAP, lsl #32
    // 0x148c8b0: r0 = value()
    //     0x148c8b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148c8b4: stur            x0, [fp, #-0x10]
    // 0x148c8b8: r0 = PaymentTrustMarkers()
    //     0x148c8b8: bl              #0xbc7fe4  ; AllocatePaymentTrustMarkersStub -> PaymentTrustMarkers (size=0x14)
    // 0x148c8bc: mov             x1, x0
    // 0x148c8c0: r0 = true
    //     0x148c8c0: add             x0, NULL, #0x20  ; true
    // 0x148c8c4: stur            x1, [fp, #-0x38]
    // 0x148c8c8: StoreField: r1->field_b = r0
    //     0x148c8c8: stur            w0, [x1, #0xb]
    // 0x148c8cc: ldur            x0, [fp, #-0x10]
    // 0x148c8d0: StoreField: r1->field_f = r0
    //     0x148c8d0: stur            w0, [x1, #0xf]
    // 0x148c8d4: r0 = Padding()
    //     0x148c8d4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148c8d8: mov             x3, x0
    // 0x148c8dc: r0 = Instance_EdgeInsets
    //     0x148c8dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x148c8e0: ldr             x0, [x0, #0x668]
    // 0x148c8e4: stur            x3, [fp, #-0x10]
    // 0x148c8e8: StoreField: r3->field_f = r0
    //     0x148c8e8: stur            w0, [x3, #0xf]
    // 0x148c8ec: ldur            x0, [fp, #-0x38]
    // 0x148c8f0: StoreField: r3->field_b = r0
    //     0x148c8f0: stur            w0, [x3, #0xb]
    // 0x148c8f4: r1 = Null
    //     0x148c8f4: mov             x1, NULL
    // 0x148c8f8: r2 = 12
    //     0x148c8f8: movz            x2, #0xc
    // 0x148c8fc: r0 = AllocateArray()
    //     0x148c8fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148c900: mov             x2, x0
    // 0x148c904: ldur            x0, [fp, #-0x18]
    // 0x148c908: stur            x2, [fp, #-0x38]
    // 0x148c90c: StoreField: r2->field_f = r0
    //     0x148c90c: stur            w0, [x2, #0xf]
    // 0x148c910: ldur            x0, [fp, #-0x20]
    // 0x148c914: StoreField: r2->field_13 = r0
    //     0x148c914: stur            w0, [x2, #0x13]
    // 0x148c918: ldur            x0, [fp, #-0x30]
    // 0x148c91c: ArrayStore: r2[0] = r0  ; List_4
    //     0x148c91c: stur            w0, [x2, #0x17]
    // 0x148c920: ldur            x0, [fp, #-0x28]
    // 0x148c924: StoreField: r2->field_1b = r0
    //     0x148c924: stur            w0, [x2, #0x1b]
    // 0x148c928: ldur            x0, [fp, #-0x40]
    // 0x148c92c: StoreField: r2->field_1f = r0
    //     0x148c92c: stur            w0, [x2, #0x1f]
    // 0x148c930: ldur            x0, [fp, #-0x10]
    // 0x148c934: StoreField: r2->field_23 = r0
    //     0x148c934: stur            w0, [x2, #0x23]
    // 0x148c938: r1 = <Widget>
    //     0x148c938: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148c93c: r0 = AllocateGrowableArray()
    //     0x148c93c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148c940: mov             x1, x0
    // 0x148c944: ldur            x0, [fp, #-0x38]
    // 0x148c948: stur            x1, [fp, #-0x10]
    // 0x148c94c: StoreField: r1->field_f = r0
    //     0x148c94c: stur            w0, [x1, #0xf]
    // 0x148c950: r0 = 12
    //     0x148c950: movz            x0, #0xc
    // 0x148c954: StoreField: r1->field_b = r0
    //     0x148c954: stur            w0, [x1, #0xb]
    // 0x148c958: r0 = ListView()
    //     0x148c958: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x148c95c: stur            x0, [fp, #-0x18]
    // 0x148c960: r16 = Instance_EdgeInsets
    //     0x148c960: add             x16, PP, #0x40, lsl #12  ; [pp+0x409e0] Obj!EdgeInsets@d58bb1
    //     0x148c964: ldr             x16, [x16, #0x9e0]
    // 0x148c968: r30 = true
    //     0x148c968: add             lr, NULL, #0x20  ; true
    // 0x148c96c: stp             lr, x16, [SP, #0x10]
    // 0x148c970: r16 = false
    //     0x148c970: add             x16, NULL, #0x30  ; false
    // 0x148c974: r30 = Instance_RangeMaintainingScrollPhysics
    //     0x148c974: add             lr, PP, #0x3a, lsl #12  ; [pp+0x3afa0] Obj!RangeMaintainingScrollPhysics@d55901
    //     0x148c978: ldr             lr, [lr, #0xfa0]
    // 0x148c97c: stp             lr, x16, [SP]
    // 0x148c980: mov             x1, x0
    // 0x148c984: ldur            x2, [fp, #-0x10]
    // 0x148c988: r4 = const [0, 0x6, 0x4, 0x2, padding, 0x2, physics, 0x5, primary, 0x4, shrinkWrap, 0x3, null]
    //     0x148c988: add             x4, PP, #0x40, lsl #12  ; [pp+0x409e8] List(13) [0, 0x6, 0x4, 0x2, "padding", 0x2, "physics", 0x5, "primary", 0x4, "shrinkWrap", 0x3, Null]
    //     0x148c98c: ldr             x4, [x4, #0x9e8]
    // 0x148c990: r0 = ListView()
    //     0x148c990: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0x148c994: r0 = WillPopScope()
    //     0x148c994: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x148c998: mov             x3, x0
    // 0x148c99c: ldur            x0, [fp, #-0x18]
    // 0x148c9a0: stur            x3, [fp, #-0x10]
    // 0x148c9a4: StoreField: r3->field_b = r0
    //     0x148c9a4: stur            w0, [x3, #0xb]
    // 0x148c9a8: ldur            x2, [fp, #-8]
    // 0x148c9ac: r1 = Function 'onBackPress':.
    //     0x148c9ac: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c70] AnonymousClosure: (0x148c9d8), in [package:customer_app/app/presentation/views/basic/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::onBackPress (0x13ac748)
    //     0x148c9b0: ldr             x1, [x1, #0xc70]
    // 0x148c9b4: r0 = AllocateClosure()
    //     0x148c9b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148c9b8: mov             x1, x0
    // 0x148c9bc: ldur            x0, [fp, #-0x10]
    // 0x148c9c0: StoreField: r0->field_f = r1
    //     0x148c9c0: stur            w1, [x0, #0xf]
    // 0x148c9c4: LeaveFrame
    //     0x148c9c4: mov             SP, fp
    //     0x148c9c8: ldp             fp, lr, [SP], #0x10
    // 0x148c9cc: ret
    //     0x148c9cc: ret             
    // 0x148c9d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148c9d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148c9d4: b               #0x148b8e8
  }
  [closure] Future<bool> onBackPress(dynamic) {
    // ** addr: 0x148c9d8, size: 0x38
    // 0x148c9d8: EnterFrame
    //     0x148c9d8: stp             fp, lr, [SP, #-0x10]!
    //     0x148c9dc: mov             fp, SP
    // 0x148c9e0: ldr             x0, [fp, #0x10]
    // 0x148c9e4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x148c9e4: ldur            w1, [x0, #0x17]
    // 0x148c9e8: DecompressPointer r1
    //     0x148c9e8: add             x1, x1, HEAP, lsl #32
    // 0x148c9ec: CheckStackOverflow
    //     0x148c9ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148c9f0: cmp             SP, x16
    //     0x148c9f4: b.ls            #0x148ca08
    // 0x148c9f8: r0 = onBackPress()
    //     0x148c9f8: bl              #0x13ac748  ; [package:customer_app/app/presentation/views/basic/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::onBackPress
    // 0x148c9fc: LeaveFrame
    //     0x148c9fc: mov             SP, fp
    //     0x148ca00: ldp             fp, lr, [SP], #0x10
    // 0x148ca04: ret
    //     0x148ca04: ret             
    // 0x148ca08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148ca08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148ca0c: b               #0x148c9f8
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x148eb70, size: 0xd4
    // 0x148eb70: EnterFrame
    //     0x148eb70: stp             fp, lr, [SP, #-0x10]!
    //     0x148eb74: mov             fp, SP
    // 0x148eb78: AllocStack(0x8)
    //     0x148eb78: sub             SP, SP, #8
    // 0x148eb7c: SetupParameters()
    //     0x148eb7c: ldr             x0, [fp, #0x20]
    //     0x148eb80: ldur            w1, [x0, #0x17]
    //     0x148eb84: add             x1, x1, HEAP, lsl #32
    // 0x148eb88: CheckStackOverflow
    //     0x148eb88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148eb8c: cmp             SP, x16
    //     0x148eb90: b.ls            #0x148ec38
    // 0x148eb94: LoadField: r0 = r1->field_f
    //     0x148eb94: ldur            w0, [x1, #0xf]
    // 0x148eb98: DecompressPointer r0
    //     0x148eb98: add             x0, x0, HEAP, lsl #32
    // 0x148eb9c: mov             x1, x0
    // 0x148eba0: stur            x0, [fp, #-8]
    // 0x148eba4: r0 = controller()
    //     0x148eba4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148eba8: LoadField: r1 = r0->field_87
    //     0x148eba8: ldur            w1, [x0, #0x87]
    // 0x148ebac: DecompressPointer r1
    //     0x148ebac: add             x1, x1, HEAP, lsl #32
    // 0x148ebb0: r0 = value()
    //     0x148ebb0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148ebb4: LoadField: r2 = r0->field_1f
    //     0x148ebb4: ldur            w2, [x0, #0x1f]
    // 0x148ebb8: DecompressPointer r2
    //     0x148ebb8: add             x2, x2, HEAP, lsl #32
    // 0x148ebbc: cmp             w2, NULL
    // 0x148ebc0: b.ne            #0x148ebd0
    // 0x148ebc4: ldr             x3, [fp, #0x10]
    // 0x148ebc8: r2 = Null
    //     0x148ebc8: mov             x2, NULL
    // 0x148ebcc: b               #0x148ec10
    // 0x148ebd0: ldr             x3, [fp, #0x10]
    // 0x148ebd4: LoadField: r0 = r2->field_b
    //     0x148ebd4: ldur            w0, [x2, #0xb]
    // 0x148ebd8: r4 = LoadInt32Instr(r3)
    //     0x148ebd8: sbfx            x4, x3, #1, #0x1f
    //     0x148ebdc: tbz             w3, #0, #0x148ebe4
    //     0x148ebe0: ldur            x4, [x3, #7]
    // 0x148ebe4: r1 = LoadInt32Instr(r0)
    //     0x148ebe4: sbfx            x1, x0, #1, #0x1f
    // 0x148ebe8: mov             x0, x1
    // 0x148ebec: mov             x1, x4
    // 0x148ebf0: cmp             x1, x0
    // 0x148ebf4: b.hs            #0x148ec40
    // 0x148ebf8: LoadField: r0 = r2->field_f
    //     0x148ebf8: ldur            w0, [x2, #0xf]
    // 0x148ebfc: DecompressPointer r0
    //     0x148ebfc: add             x0, x0, HEAP, lsl #32
    // 0x148ec00: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x148ec00: add             x16, x0, x4, lsl #2
    //     0x148ec04: ldur            w1, [x16, #0xf]
    // 0x148ec08: DecompressPointer r1
    //     0x148ec08: add             x1, x1, HEAP, lsl #32
    // 0x148ec0c: mov             x2, x1
    // 0x148ec10: r0 = LoadInt32Instr(r3)
    //     0x148ec10: sbfx            x0, x3, #1, #0x1f
    //     0x148ec14: tbz             w3, #0, #0x148ec1c
    //     0x148ec18: ldur            x0, [x3, #7]
    // 0x148ec1c: ldur            x1, [fp, #-8]
    // 0x148ec20: mov             x3, x0
    // 0x148ec24: ldr             x5, [fp, #0x18]
    // 0x148ec28: r0 = cosmeticThemePaymentMethodCard()
    //     0x148ec28: bl              #0x148ec44  ; [package:customer_app/app/presentation/views/cosmetic/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::cosmeticThemePaymentMethodCard
    // 0x148ec2c: LeaveFrame
    //     0x148ec2c: mov             SP, fp
    //     0x148ec30: ldp             fp, lr, [SP], #0x10
    // 0x148ec34: ret
    //     0x148ec34: ret             
    // 0x148ec38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148ec38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148ec3c: b               #0x148eb94
    // 0x148ec40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x148ec40: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ cosmeticThemePaymentMethodCard(/* No info */) {
    // ** addr: 0x148ec44, size: 0x90
    // 0x148ec44: EnterFrame
    //     0x148ec44: stp             fp, lr, [SP, #-0x10]!
    //     0x148ec48: mov             fp, SP
    // 0x148ec4c: AllocStack(0x28)
    //     0x148ec4c: sub             SP, SP, #0x28
    // 0x148ec50: SetupParameters(PaymentMethodsCheckoutWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x148ec50: stur            x1, [fp, #-8]
    //     0x148ec54: stur            x2, [fp, #-0x10]
    //     0x148ec58: stur            x3, [fp, #-0x18]
    //     0x148ec5c: stur            x5, [fp, #-0x20]
    // 0x148ec60: r1 = 4
    //     0x148ec60: movz            x1, #0x4
    // 0x148ec64: r0 = AllocateContext()
    //     0x148ec64: bl              #0x16f6108  ; AllocateContextStub
    // 0x148ec68: mov             x2, x0
    // 0x148ec6c: ldur            x0, [fp, #-8]
    // 0x148ec70: stur            x2, [fp, #-0x28]
    // 0x148ec74: StoreField: r2->field_f = r0
    //     0x148ec74: stur            w0, [x2, #0xf]
    // 0x148ec78: ldur            x0, [fp, #-0x10]
    // 0x148ec7c: StoreField: r2->field_13 = r0
    //     0x148ec7c: stur            w0, [x2, #0x13]
    // 0x148ec80: ldur            x3, [fp, #-0x18]
    // 0x148ec84: r0 = BoxInt64Instr(r3)
    //     0x148ec84: sbfiz           x0, x3, #1, #0x1f
    //     0x148ec88: cmp             x3, x0, asr #1
    //     0x148ec8c: b.eq            #0x148ec98
    //     0x148ec90: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x148ec94: stur            x3, [x0, #7]
    // 0x148ec98: ArrayStore: r2[0] = r0  ; List_4
    //     0x148ec98: stur            w0, [x2, #0x17]
    // 0x148ec9c: ldur            x0, [fp, #-0x20]
    // 0x148eca0: StoreField: r2->field_1b = r0
    //     0x148eca0: stur            w0, [x2, #0x1b]
    // 0x148eca4: r0 = Obx()
    //     0x148eca4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x148eca8: ldur            x2, [fp, #-0x28]
    // 0x148ecac: r1 = Function '<anonymous closure>':.
    //     0x148ecac: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c78] AnonymousClosure: (0x148ecd4), in [package:customer_app/app/presentation/views/cosmetic/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::cosmeticThemePaymentMethodCard (0x148ec44)
    //     0x148ecb0: ldr             x1, [x1, #0xc78]
    // 0x148ecb4: stur            x0, [fp, #-8]
    // 0x148ecb8: r0 = AllocateClosure()
    //     0x148ecb8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148ecbc: mov             x1, x0
    // 0x148ecc0: ldur            x0, [fp, #-8]
    // 0x148ecc4: StoreField: r0->field_b = r1
    //     0x148ecc4: stur            w1, [x0, #0xb]
    // 0x148ecc8: LeaveFrame
    //     0x148ecc8: mov             SP, fp
    //     0x148eccc: ldp             fp, lr, [SP], #0x10
    // 0x148ecd0: ret
    //     0x148ecd0: ret             
  }
  [closure] InkWell <anonymous closure>(dynamic) {
    // ** addr: 0x148ecd4, size: 0x3c4
    // 0x148ecd4: EnterFrame
    //     0x148ecd4: stp             fp, lr, [SP, #-0x10]!
    //     0x148ecd8: mov             fp, SP
    // 0x148ecdc: AllocStack(0x40)
    //     0x148ecdc: sub             SP, SP, #0x40
    // 0x148ece0: SetupParameters()
    //     0x148ece0: ldr             x0, [fp, #0x10]
    //     0x148ece4: ldur            w2, [x0, #0x17]
    //     0x148ece8: add             x2, x2, HEAP, lsl #32
    //     0x148ecec: stur            x2, [fp, #-8]
    // 0x148ecf0: CheckStackOverflow
    //     0x148ecf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148ecf4: cmp             SP, x16
    //     0x148ecf8: b.ls            #0x148f090
    // 0x148ecfc: LoadField: r1 = r2->field_f
    //     0x148ecfc: ldur            w1, [x2, #0xf]
    // 0x148ed00: DecompressPointer r1
    //     0x148ed00: add             x1, x1, HEAP, lsl #32
    // 0x148ed04: r0 = controller()
    //     0x148ed04: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148ed08: LoadField: r1 = r0->field_7f
    //     0x148ed08: ldur            w1, [x0, #0x7f]
    // 0x148ed0c: DecompressPointer r1
    //     0x148ed0c: add             x1, x1, HEAP, lsl #32
    // 0x148ed10: r0 = value()
    //     0x148ed10: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148ed14: ldur            x2, [fp, #-8]
    // 0x148ed18: LoadField: r1 = r2->field_13
    //     0x148ed18: ldur            w1, [x2, #0x13]
    // 0x148ed1c: DecompressPointer r1
    //     0x148ed1c: add             x1, x1, HEAP, lsl #32
    // 0x148ed20: cmp             w1, NULL
    // 0x148ed24: b.ne            #0x148ed30
    // 0x148ed28: r1 = Null
    //     0x148ed28: mov             x1, NULL
    // 0x148ed2c: b               #0x148ed3c
    // 0x148ed30: LoadField: r3 = r1->field_f
    //     0x148ed30: ldur            w3, [x1, #0xf]
    // 0x148ed34: DecompressPointer r3
    //     0x148ed34: add             x3, x3, HEAP, lsl #32
    // 0x148ed38: mov             x1, x3
    // 0x148ed3c: r3 = 60
    //     0x148ed3c: movz            x3, #0x3c
    // 0x148ed40: branchIfSmi(r0, 0x148ed4c)
    //     0x148ed40: tbz             w0, #0, #0x148ed4c
    // 0x148ed44: r3 = LoadClassIdInstr(r0)
    //     0x148ed44: ldur            x3, [x0, #-1]
    //     0x148ed48: ubfx            x3, x3, #0xc, #0x14
    // 0x148ed4c: stp             x1, x0, [SP]
    // 0x148ed50: mov             x0, x3
    // 0x148ed54: mov             lr, x0
    // 0x148ed58: ldr             lr, [x21, lr, lsl #3]
    // 0x148ed5c: blr             lr
    // 0x148ed60: tbnz            w0, #4, #0x148edb8
    // 0x148ed64: ldur            x2, [fp, #-8]
    // 0x148ed68: LoadField: r1 = r2->field_f
    //     0x148ed68: ldur            w1, [x2, #0xf]
    // 0x148ed6c: DecompressPointer r1
    //     0x148ed6c: add             x1, x1, HEAP, lsl #32
    // 0x148ed70: r0 = controller()
    //     0x148ed70: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148ed74: LoadField: r1 = r0->field_a3
    //     0x148ed74: ldur            w1, [x0, #0xa3]
    // 0x148ed78: DecompressPointer r1
    //     0x148ed78: add             x1, x1, HEAP, lsl #32
    // 0x148ed7c: r0 = value()
    //     0x148ed7c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148ed80: ldur            x2, [fp, #-8]
    // 0x148ed84: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x148ed84: ldur            w1, [x2, #0x17]
    // 0x148ed88: DecompressPointer r1
    //     0x148ed88: add             x1, x1, HEAP, lsl #32
    // 0x148ed8c: r3 = LoadInt32Instr(r0)
    //     0x148ed8c: sbfx            x3, x0, #1, #0x1f
    //     0x148ed90: tbz             w0, #0, #0x148ed98
    //     0x148ed94: ldur            x3, [x0, #7]
    // 0x148ed98: r0 = LoadInt32Instr(r1)
    //     0x148ed98: sbfx            x0, x1, #1, #0x1f
    //     0x148ed9c: tbz             w1, #0, #0x148eda4
    //     0x148eda0: ldur            x0, [x1, #7]
    // 0x148eda4: cmp             x3, x0
    // 0x148eda8: b.ne            #0x148edbc
    // 0x148edac: r0 = Instance_IconData
    //     0x148edac: add             x0, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0x148edb0: ldr             x0, [x0, #0x30]
    // 0x148edb4: b               #0x148edc4
    // 0x148edb8: ldur            x2, [fp, #-8]
    // 0x148edbc: r0 = Instance_IconData
    //     0x148edbc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0x148edc0: ldr             x0, [x0, #0x38]
    // 0x148edc4: stur            x0, [fp, #-0x10]
    // 0x148edc8: LoadField: r1 = r2->field_1b
    //     0x148edc8: ldur            w1, [x2, #0x1b]
    // 0x148edcc: DecompressPointer r1
    //     0x148edcc: add             x1, x1, HEAP, lsl #32
    // 0x148edd0: r0 = of()
    //     0x148edd0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148edd4: LoadField: r1 = r0->field_5b
    //     0x148edd4: ldur            w1, [x0, #0x5b]
    // 0x148edd8: DecompressPointer r1
    //     0x148edd8: add             x1, x1, HEAP, lsl #32
    // 0x148eddc: stur            x1, [fp, #-0x18]
    // 0x148ede0: r0 = Icon()
    //     0x148ede0: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x148ede4: mov             x1, x0
    // 0x148ede8: ldur            x0, [fp, #-0x10]
    // 0x148edec: stur            x1, [fp, #-0x20]
    // 0x148edf0: StoreField: r1->field_b = r0
    //     0x148edf0: stur            w0, [x1, #0xb]
    // 0x148edf4: ldur            x0, [fp, #-0x18]
    // 0x148edf8: StoreField: r1->field_23 = r0
    //     0x148edf8: stur            w0, [x1, #0x23]
    // 0x148edfc: ldur            x2, [fp, #-8]
    // 0x148ee00: LoadField: r0 = r2->field_13
    //     0x148ee00: ldur            w0, [x2, #0x13]
    // 0x148ee04: DecompressPointer r0
    //     0x148ee04: add             x0, x0, HEAP, lsl #32
    // 0x148ee08: cmp             w0, NULL
    // 0x148ee0c: b.ne            #0x148ee18
    // 0x148ee10: r0 = Null
    //     0x148ee10: mov             x0, NULL
    // 0x148ee14: b               #0x148ee24
    // 0x148ee18: LoadField: r3 = r0->field_7
    //     0x148ee18: ldur            w3, [x0, #7]
    // 0x148ee1c: DecompressPointer r3
    //     0x148ee1c: add             x3, x3, HEAP, lsl #32
    // 0x148ee20: mov             x0, x3
    // 0x148ee24: cmp             w0, NULL
    // 0x148ee28: b.ne            #0x148ee30
    // 0x148ee2c: r0 = ""
    //     0x148ee2c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148ee30: stur            x0, [fp, #-0x10]
    // 0x148ee34: r0 = CachedNetworkImage()
    //     0x148ee34: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x148ee38: stur            x0, [fp, #-0x18]
    // 0x148ee3c: r16 = 48.000000
    //     0x148ee3c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x148ee40: ldr             x16, [x16, #0xad8]
    // 0x148ee44: r30 = 48.000000
    //     0x148ee44: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x148ee48: ldr             lr, [lr, #0xad8]
    // 0x148ee4c: stp             lr, x16, [SP]
    // 0x148ee50: mov             x1, x0
    // 0x148ee54: ldur            x2, [fp, #-0x10]
    // 0x148ee58: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0x148ee58: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0x148ee5c: ldr             x4, [x4, #0x900]
    // 0x148ee60: r0 = CachedNetworkImage()
    //     0x148ee60: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x148ee64: r0 = Padding()
    //     0x148ee64: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148ee68: mov             x2, x0
    // 0x148ee6c: r0 = Instance_EdgeInsets
    //     0x148ee6c: add             x0, PP, #0x40, lsl #12  ; [pp+0x409f8] Obj!EdgeInsets@d59b41
    //     0x148ee70: ldr             x0, [x0, #0x9f8]
    // 0x148ee74: stur            x2, [fp, #-0x28]
    // 0x148ee78: StoreField: r2->field_f = r0
    //     0x148ee78: stur            w0, [x2, #0xf]
    // 0x148ee7c: ldur            x1, [fp, #-0x18]
    // 0x148ee80: StoreField: r2->field_b = r1
    //     0x148ee80: stur            w1, [x2, #0xb]
    // 0x148ee84: ldur            x3, [fp, #-8]
    // 0x148ee88: LoadField: r1 = r3->field_13
    //     0x148ee88: ldur            w1, [x3, #0x13]
    // 0x148ee8c: DecompressPointer r1
    //     0x148ee8c: add             x1, x1, HEAP, lsl #32
    // 0x148ee90: cmp             w1, NULL
    // 0x148ee94: b.ne            #0x148eea0
    // 0x148ee98: r1 = Null
    //     0x148ee98: mov             x1, NULL
    // 0x148ee9c: b               #0x148eeac
    // 0x148eea0: LoadField: r4 = r1->field_b
    //     0x148eea0: ldur            w4, [x1, #0xb]
    // 0x148eea4: DecompressPointer r4
    //     0x148eea4: add             x4, x4, HEAP, lsl #32
    // 0x148eea8: mov             x1, x4
    // 0x148eeac: cmp             w1, NULL
    // 0x148eeb0: b.ne            #0x148eebc
    // 0x148eeb4: r5 = ""
    //     0x148eeb4: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148eeb8: b               #0x148eec0
    // 0x148eebc: mov             x5, x1
    // 0x148eec0: ldur            x4, [fp, #-0x20]
    // 0x148eec4: stur            x5, [fp, #-0x10]
    // 0x148eec8: LoadField: r1 = r3->field_1b
    //     0x148eec8: ldur            w1, [x3, #0x1b]
    // 0x148eecc: DecompressPointer r1
    //     0x148eecc: add             x1, x1, HEAP, lsl #32
    // 0x148eed0: r0 = of()
    //     0x148eed0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148eed4: LoadField: r1 = r0->field_87
    //     0x148eed4: ldur            w1, [x0, #0x87]
    // 0x148eed8: DecompressPointer r1
    //     0x148eed8: add             x1, x1, HEAP, lsl #32
    // 0x148eedc: LoadField: r0 = r1->field_2b
    //     0x148eedc: ldur            w0, [x1, #0x2b]
    // 0x148eee0: DecompressPointer r0
    //     0x148eee0: add             x0, x0, HEAP, lsl #32
    // 0x148eee4: r16 = 14.000000
    //     0x148eee4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x148eee8: ldr             x16, [x16, #0x1d8]
    // 0x148eeec: r30 = Instance_Color
    //     0x148eeec: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148eef0: stp             lr, x16, [SP]
    // 0x148eef4: mov             x1, x0
    // 0x148eef8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x148eef8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x148eefc: ldr             x4, [x4, #0xaa0]
    // 0x148ef00: r0 = copyWith()
    //     0x148ef00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148ef04: stur            x0, [fp, #-0x18]
    // 0x148ef08: r0 = Text()
    //     0x148ef08: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148ef0c: mov             x1, x0
    // 0x148ef10: ldur            x0, [fp, #-0x10]
    // 0x148ef14: stur            x1, [fp, #-0x30]
    // 0x148ef18: StoreField: r1->field_b = r0
    //     0x148ef18: stur            w0, [x1, #0xb]
    // 0x148ef1c: ldur            x0, [fp, #-0x18]
    // 0x148ef20: StoreField: r1->field_13 = r0
    //     0x148ef20: stur            w0, [x1, #0x13]
    // 0x148ef24: r0 = Padding()
    //     0x148ef24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148ef28: mov             x3, x0
    // 0x148ef2c: r0 = Instance_EdgeInsets
    //     0x148ef2c: add             x0, PP, #0x40, lsl #12  ; [pp+0x409f8] Obj!EdgeInsets@d59b41
    //     0x148ef30: ldr             x0, [x0, #0x9f8]
    // 0x148ef34: stur            x3, [fp, #-0x10]
    // 0x148ef38: StoreField: r3->field_f = r0
    //     0x148ef38: stur            w0, [x3, #0xf]
    // 0x148ef3c: ldur            x0, [fp, #-0x30]
    // 0x148ef40: StoreField: r3->field_b = r0
    //     0x148ef40: stur            w0, [x3, #0xb]
    // 0x148ef44: r1 = Null
    //     0x148ef44: mov             x1, NULL
    // 0x148ef48: r2 = 6
    //     0x148ef48: movz            x2, #0x6
    // 0x148ef4c: r0 = AllocateArray()
    //     0x148ef4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148ef50: mov             x2, x0
    // 0x148ef54: ldur            x0, [fp, #-0x20]
    // 0x148ef58: stur            x2, [fp, #-0x18]
    // 0x148ef5c: StoreField: r2->field_f = r0
    //     0x148ef5c: stur            w0, [x2, #0xf]
    // 0x148ef60: ldur            x0, [fp, #-0x28]
    // 0x148ef64: StoreField: r2->field_13 = r0
    //     0x148ef64: stur            w0, [x2, #0x13]
    // 0x148ef68: ldur            x0, [fp, #-0x10]
    // 0x148ef6c: ArrayStore: r2[0] = r0  ; List_4
    //     0x148ef6c: stur            w0, [x2, #0x17]
    // 0x148ef70: r1 = <Widget>
    //     0x148ef70: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148ef74: r0 = AllocateGrowableArray()
    //     0x148ef74: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148ef78: mov             x1, x0
    // 0x148ef7c: ldur            x0, [fp, #-0x18]
    // 0x148ef80: stur            x1, [fp, #-0x10]
    // 0x148ef84: StoreField: r1->field_f = r0
    //     0x148ef84: stur            w0, [x1, #0xf]
    // 0x148ef88: r0 = 6
    //     0x148ef88: movz            x0, #0x6
    // 0x148ef8c: StoreField: r1->field_b = r0
    //     0x148ef8c: stur            w0, [x1, #0xb]
    // 0x148ef90: r0 = Row()
    //     0x148ef90: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x148ef94: mov             x1, x0
    // 0x148ef98: r0 = Instance_Axis
    //     0x148ef98: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x148ef9c: stur            x1, [fp, #-0x18]
    // 0x148efa0: StoreField: r1->field_f = r0
    //     0x148efa0: stur            w0, [x1, #0xf]
    // 0x148efa4: r0 = Instance_MainAxisAlignment
    //     0x148efa4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x148efa8: ldr             x0, [x0, #0xa08]
    // 0x148efac: StoreField: r1->field_13 = r0
    //     0x148efac: stur            w0, [x1, #0x13]
    // 0x148efb0: r0 = Instance_MainAxisSize
    //     0x148efb0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148efb4: ldr             x0, [x0, #0xa10]
    // 0x148efb8: ArrayStore: r1[0] = r0  ; List_4
    //     0x148efb8: stur            w0, [x1, #0x17]
    // 0x148efbc: r0 = Instance_CrossAxisAlignment
    //     0x148efbc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x148efc0: ldr             x0, [x0, #0xa18]
    // 0x148efc4: StoreField: r1->field_1b = r0
    //     0x148efc4: stur            w0, [x1, #0x1b]
    // 0x148efc8: r0 = Instance_VerticalDirection
    //     0x148efc8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148efcc: ldr             x0, [x0, #0xa20]
    // 0x148efd0: StoreField: r1->field_23 = r0
    //     0x148efd0: stur            w0, [x1, #0x23]
    // 0x148efd4: r0 = Instance_Clip
    //     0x148efd4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148efd8: ldr             x0, [x0, #0x38]
    // 0x148efdc: StoreField: r1->field_2b = r0
    //     0x148efdc: stur            w0, [x1, #0x2b]
    // 0x148efe0: StoreField: r1->field_2f = rZR
    //     0x148efe0: stur            xzr, [x1, #0x2f]
    // 0x148efe4: ldur            x0, [fp, #-0x10]
    // 0x148efe8: StoreField: r1->field_b = r0
    //     0x148efe8: stur            w0, [x1, #0xb]
    // 0x148efec: r0 = Center()
    //     0x148efec: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x148eff0: mov             x1, x0
    // 0x148eff4: r0 = Instance_Alignment
    //     0x148eff4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x148eff8: ldr             x0, [x0, #0xb10]
    // 0x148effc: stur            x1, [fp, #-0x10]
    // 0x148f000: StoreField: r1->field_f = r0
    //     0x148f000: stur            w0, [x1, #0xf]
    // 0x148f004: ldur            x0, [fp, #-0x18]
    // 0x148f008: StoreField: r1->field_b = r0
    //     0x148f008: stur            w0, [x1, #0xb]
    // 0x148f00c: r0 = Padding()
    //     0x148f00c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148f010: mov             x1, x0
    // 0x148f014: r0 = Instance_EdgeInsets
    //     0x148f014: add             x0, PP, #0x40, lsl #12  ; [pp+0x40800] Obj!EdgeInsets@d59b11
    //     0x148f018: ldr             x0, [x0, #0x800]
    // 0x148f01c: stur            x1, [fp, #-0x18]
    // 0x148f020: StoreField: r1->field_f = r0
    //     0x148f020: stur            w0, [x1, #0xf]
    // 0x148f024: ldur            x0, [fp, #-0x10]
    // 0x148f028: StoreField: r1->field_b = r0
    //     0x148f028: stur            w0, [x1, #0xb]
    // 0x148f02c: r0 = InkWell()
    //     0x148f02c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x148f030: mov             x3, x0
    // 0x148f034: ldur            x0, [fp, #-0x18]
    // 0x148f038: stur            x3, [fp, #-0x10]
    // 0x148f03c: StoreField: r3->field_b = r0
    //     0x148f03c: stur            w0, [x3, #0xb]
    // 0x148f040: ldur            x2, [fp, #-8]
    // 0x148f044: r1 = Function '<anonymous closure>':.
    //     0x148f044: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c80] AnonymousClosure: (0x13ace4c), in [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::lineThemePaymentMethodCard (0x13ad294)
    //     0x148f048: ldr             x1, [x1, #0xc80]
    // 0x148f04c: r0 = AllocateClosure()
    //     0x148f04c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148f050: mov             x1, x0
    // 0x148f054: ldur            x0, [fp, #-0x10]
    // 0x148f058: StoreField: r0->field_f = r1
    //     0x148f058: stur            w1, [x0, #0xf]
    // 0x148f05c: r1 = true
    //     0x148f05c: add             x1, NULL, #0x20  ; true
    // 0x148f060: StoreField: r0->field_43 = r1
    //     0x148f060: stur            w1, [x0, #0x43]
    // 0x148f064: r2 = Instance_BoxShape
    //     0x148f064: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x148f068: ldr             x2, [x2, #0x80]
    // 0x148f06c: StoreField: r0->field_47 = r2
    //     0x148f06c: stur            w2, [x0, #0x47]
    // 0x148f070: StoreField: r0->field_6f = r1
    //     0x148f070: stur            w1, [x0, #0x6f]
    // 0x148f074: r2 = false
    //     0x148f074: add             x2, NULL, #0x30  ; false
    // 0x148f078: StoreField: r0->field_73 = r2
    //     0x148f078: stur            w2, [x0, #0x73]
    // 0x148f07c: StoreField: r0->field_83 = r1
    //     0x148f07c: stur            w1, [x0, #0x83]
    // 0x148f080: StoreField: r0->field_7b = r2
    //     0x148f080: stur            w2, [x0, #0x7b]
    // 0x148f084: LeaveFrame
    //     0x148f084: mov             SP, fp
    //     0x148f088: ldp             fp, lr, [SP], #0x10
    // 0x148f08c: ret
    //     0x148f08c: ret             
    // 0x148f090: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148f090: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148f094: b               #0x148ecfc
  }
  [closure] Obx <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x148f098, size: 0x74
    // 0x148f098: EnterFrame
    //     0x148f098: stp             fp, lr, [SP, #-0x10]!
    //     0x148f09c: mov             fp, SP
    // 0x148f0a0: AllocStack(0x10)
    //     0x148f0a0: sub             SP, SP, #0x10
    // 0x148f0a4: SetupParameters()
    //     0x148f0a4: ldr             x0, [fp, #0x20]
    //     0x148f0a8: ldur            w1, [x0, #0x17]
    //     0x148f0ac: add             x1, x1, HEAP, lsl #32
    //     0x148f0b0: stur            x1, [fp, #-8]
    // 0x148f0b4: r1 = 2
    //     0x148f0b4: movz            x1, #0x2
    // 0x148f0b8: r0 = AllocateContext()
    //     0x148f0b8: bl              #0x16f6108  ; AllocateContextStub
    // 0x148f0bc: mov             x1, x0
    // 0x148f0c0: ldur            x0, [fp, #-8]
    // 0x148f0c4: stur            x1, [fp, #-0x10]
    // 0x148f0c8: StoreField: r1->field_b = r0
    //     0x148f0c8: stur            w0, [x1, #0xb]
    // 0x148f0cc: ldr             x0, [fp, #0x18]
    // 0x148f0d0: StoreField: r1->field_f = r0
    //     0x148f0d0: stur            w0, [x1, #0xf]
    // 0x148f0d4: ldr             x0, [fp, #0x10]
    // 0x148f0d8: StoreField: r1->field_13 = r0
    //     0x148f0d8: stur            w0, [x1, #0x13]
    // 0x148f0dc: r0 = Obx()
    //     0x148f0dc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x148f0e0: ldur            x2, [fp, #-0x10]
    // 0x148f0e4: r1 = Function '<anonymous closure>':.
    //     0x148f0e4: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c88] AnonymousClosure: (0x148f10c), in [package:customer_app/app/presentation/views/cosmetic/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x148b85c)
    //     0x148f0e8: ldr             x1, [x1, #0xc88]
    // 0x148f0ec: stur            x0, [fp, #-8]
    // 0x148f0f0: r0 = AllocateClosure()
    //     0x148f0f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148f0f4: mov             x1, x0
    // 0x148f0f8: ldur            x0, [fp, #-8]
    // 0x148f0fc: StoreField: r0->field_b = r1
    //     0x148f0fc: stur            w1, [x0, #0xb]
    // 0x148f100: LeaveFrame
    //     0x148f100: mov             SP, fp
    //     0x148f104: ldp             fp, lr, [SP], #0x10
    // 0x148f108: ret
    //     0x148f108: ret             
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x148f10c, size: 0x134
    // 0x148f10c: EnterFrame
    //     0x148f10c: stp             fp, lr, [SP, #-0x10]!
    //     0x148f110: mov             fp, SP
    // 0x148f114: AllocStack(0x10)
    //     0x148f114: sub             SP, SP, #0x10
    // 0x148f118: SetupParameters()
    //     0x148f118: ldr             x0, [fp, #0x10]
    //     0x148f11c: ldur            w2, [x0, #0x17]
    //     0x148f120: add             x2, x2, HEAP, lsl #32
    //     0x148f124: stur            x2, [fp, #-0x10]
    // 0x148f128: CheckStackOverflow
    //     0x148f128: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148f12c: cmp             SP, x16
    //     0x148f130: b.ls            #0x148f234
    // 0x148f134: LoadField: r0 = r2->field_b
    //     0x148f134: ldur            w0, [x2, #0xb]
    // 0x148f138: DecompressPointer r0
    //     0x148f138: add             x0, x0, HEAP, lsl #32
    // 0x148f13c: stur            x0, [fp, #-8]
    // 0x148f140: LoadField: r1 = r0->field_f
    //     0x148f140: ldur            w1, [x0, #0xf]
    // 0x148f144: DecompressPointer r1
    //     0x148f144: add             x1, x1, HEAP, lsl #32
    // 0x148f148: r0 = controller()
    //     0x148f148: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148f14c: LoadField: r1 = r0->field_73
    //     0x148f14c: ldur            w1, [x0, #0x73]
    // 0x148f150: DecompressPointer r1
    //     0x148f150: add             x1, x1, HEAP, lsl #32
    // 0x148f154: r0 = value()
    //     0x148f154: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148f158: LoadField: r1 = r0->field_b
    //     0x148f158: ldur            w1, [x0, #0xb]
    // 0x148f15c: DecompressPointer r1
    //     0x148f15c: add             x1, x1, HEAP, lsl #32
    // 0x148f160: cmp             w1, NULL
    // 0x148f164: b.ne            #0x148f174
    // 0x148f168: ldur            x3, [fp, #-0x10]
    // 0x148f16c: r0 = Null
    //     0x148f16c: mov             x0, NULL
    // 0x148f170: b               #0x148f1f0
    // 0x148f174: LoadField: r0 = r1->field_f
    //     0x148f174: ldur            w0, [x1, #0xf]
    // 0x148f178: DecompressPointer r0
    //     0x148f178: add             x0, x0, HEAP, lsl #32
    // 0x148f17c: cmp             w0, NULL
    // 0x148f180: b.ne            #0x148f190
    // 0x148f184: ldur            x3, [fp, #-0x10]
    // 0x148f188: r0 = Null
    //     0x148f188: mov             x0, NULL
    // 0x148f18c: b               #0x148f1f0
    // 0x148f190: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x148f190: ldur            w2, [x0, #0x17]
    // 0x148f194: DecompressPointer r2
    //     0x148f194: add             x2, x2, HEAP, lsl #32
    // 0x148f198: cmp             w2, NULL
    // 0x148f19c: b.ne            #0x148f1ac
    // 0x148f1a0: ldur            x3, [fp, #-0x10]
    // 0x148f1a4: r0 = Null
    //     0x148f1a4: mov             x0, NULL
    // 0x148f1a8: b               #0x148f1f0
    // 0x148f1ac: ldur            x3, [fp, #-0x10]
    // 0x148f1b0: LoadField: r0 = r3->field_13
    //     0x148f1b0: ldur            w0, [x3, #0x13]
    // 0x148f1b4: DecompressPointer r0
    //     0x148f1b4: add             x0, x0, HEAP, lsl #32
    // 0x148f1b8: LoadField: r1 = r2->field_b
    //     0x148f1b8: ldur            w1, [x2, #0xb]
    // 0x148f1bc: r4 = LoadInt32Instr(r0)
    //     0x148f1bc: sbfx            x4, x0, #1, #0x1f
    //     0x148f1c0: tbz             w0, #0, #0x148f1c8
    //     0x148f1c4: ldur            x4, [x0, #7]
    // 0x148f1c8: r0 = LoadInt32Instr(r1)
    //     0x148f1c8: sbfx            x0, x1, #1, #0x1f
    // 0x148f1cc: mov             x1, x4
    // 0x148f1d0: cmp             x1, x0
    // 0x148f1d4: b.hs            #0x148f23c
    // 0x148f1d8: LoadField: r0 = r2->field_f
    //     0x148f1d8: ldur            w0, [x2, #0xf]
    // 0x148f1dc: DecompressPointer r0
    //     0x148f1dc: add             x0, x0, HEAP, lsl #32
    // 0x148f1e0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x148f1e0: add             x16, x0, x4, lsl #2
    //     0x148f1e4: ldur            w1, [x16, #0xf]
    // 0x148f1e8: DecompressPointer r1
    //     0x148f1e8: add             x1, x1, HEAP, lsl #32
    // 0x148f1ec: mov             x0, x1
    // 0x148f1f0: cmp             w0, NULL
    // 0x148f1f4: b.ne            #0x148f204
    // 0x148f1f8: r0 = BEntities()
    //     0x148f1f8: bl              #0x9fc7d0  ; AllocateBEntitiesStub -> BEntities (size=0x5c)
    // 0x148f1fc: mov             x2, x0
    // 0x148f200: b               #0x148f208
    // 0x148f204: mov             x2, x0
    // 0x148f208: ldur            x0, [fp, #-0x10]
    // 0x148f20c: ldur            x1, [fp, #-8]
    // 0x148f210: LoadField: r3 = r0->field_f
    //     0x148f210: ldur            w3, [x0, #0xf]
    // 0x148f214: DecompressPointer r3
    //     0x148f214: add             x3, x3, HEAP, lsl #32
    // 0x148f218: LoadField: r0 = r1->field_f
    //     0x148f218: ldur            w0, [x1, #0xf]
    // 0x148f21c: DecompressPointer r0
    //     0x148f21c: add             x0, x0, HEAP, lsl #32
    // 0x148f220: mov             x1, x0
    // 0x148f224: r0 = cosmeticThemeBagItem()
    //     0x148f224: bl              #0x148f240  ; [package:customer_app/app/presentation/views/cosmetic/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::cosmeticThemeBagItem
    // 0x148f228: LeaveFrame
    //     0x148f228: mov             SP, fp
    //     0x148f22c: ldp             fp, lr, [SP], #0x10
    // 0x148f230: ret
    //     0x148f230: ret             
    // 0x148f234: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148f234: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148f238: b               #0x148f134
    // 0x148f23c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x148f23c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ cosmeticThemeBagItem(/* No info */) {
    // ** addr: 0x148f240, size: 0x814
    // 0x148f240: EnterFrame
    //     0x148f240: stp             fp, lr, [SP, #-0x10]!
    //     0x148f244: mov             fp, SP
    // 0x148f248: AllocStack(0x68)
    //     0x148f248: sub             SP, SP, #0x68
    // 0x148f24c: SetupParameters(PaymentMethodsCheckoutWidget this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0x148f24c: mov             x0, x1
    //     0x148f250: stur            x1, [fp, #-8]
    //     0x148f254: mov             x1, x3
    //     0x148f258: stur            x2, [fp, #-0x10]
    //     0x148f25c: stur            x3, [fp, #-0x18]
    // 0x148f260: CheckStackOverflow
    //     0x148f260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148f264: cmp             SP, x16
    //     0x148f268: b.ls            #0x148fa4c
    // 0x148f26c: r0 = Radius()
    //     0x148f26c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x148f270: d0 = 15.000000
    //     0x148f270: fmov            d0, #15.00000000
    // 0x148f274: stur            x0, [fp, #-0x20]
    // 0x148f278: StoreField: r0->field_7 = d0
    //     0x148f278: stur            d0, [x0, #7]
    // 0x148f27c: StoreField: r0->field_f = d0
    //     0x148f27c: stur            d0, [x0, #0xf]
    // 0x148f280: r0 = BorderRadius()
    //     0x148f280: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x148f284: mov             x3, x0
    // 0x148f288: ldur            x0, [fp, #-0x20]
    // 0x148f28c: stur            x3, [fp, #-0x28]
    // 0x148f290: StoreField: r3->field_7 = r0
    //     0x148f290: stur            w0, [x3, #7]
    // 0x148f294: StoreField: r3->field_b = r0
    //     0x148f294: stur            w0, [x3, #0xb]
    // 0x148f298: StoreField: r3->field_f = r0
    //     0x148f298: stur            w0, [x3, #0xf]
    // 0x148f29c: StoreField: r3->field_13 = r0
    //     0x148f29c: stur            w0, [x3, #0x13]
    // 0x148f2a0: ldur            x0, [fp, #-0x10]
    // 0x148f2a4: LoadField: r1 = r0->field_13
    //     0x148f2a4: ldur            w1, [x0, #0x13]
    // 0x148f2a8: DecompressPointer r1
    //     0x148f2a8: add             x1, x1, HEAP, lsl #32
    // 0x148f2ac: cmp             w1, NULL
    // 0x148f2b0: b.ne            #0x148f2bc
    // 0x148f2b4: r4 = ""
    //     0x148f2b4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148f2b8: b               #0x148f2c0
    // 0x148f2bc: mov             x4, x1
    // 0x148f2c0: stur            x4, [fp, #-0x20]
    // 0x148f2c4: r1 = Function '<anonymous closure>':.
    //     0x148f2c4: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c90] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x148f2c8: ldr             x1, [x1, #0xc90]
    // 0x148f2cc: r2 = Null
    //     0x148f2cc: mov             x2, NULL
    // 0x148f2d0: r0 = AllocateClosure()
    //     0x148f2d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148f2d4: r1 = Function '<anonymous closure>':.
    //     0x148f2d4: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c98] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x148f2d8: ldr             x1, [x1, #0xc98]
    // 0x148f2dc: r2 = Null
    //     0x148f2dc: mov             x2, NULL
    // 0x148f2e0: stur            x0, [fp, #-0x30]
    // 0x148f2e4: r0 = AllocateClosure()
    //     0x148f2e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148f2e8: stur            x0, [fp, #-0x38]
    // 0x148f2ec: r0 = CachedNetworkImage()
    //     0x148f2ec: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x148f2f0: stur            x0, [fp, #-0x40]
    // 0x148f2f4: r16 = 56.000000
    //     0x148f2f4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x148f2f8: ldr             x16, [x16, #0xb78]
    // 0x148f2fc: r30 = 56.000000
    //     0x148f2fc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x148f300: ldr             lr, [lr, #0xb78]
    // 0x148f304: stp             lr, x16, [SP, #0x18]
    // 0x148f308: r16 = Instance_BoxFit
    //     0x148f308: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x148f30c: ldr             x16, [x16, #0x118]
    // 0x148f310: ldur            lr, [fp, #-0x30]
    // 0x148f314: stp             lr, x16, [SP, #8]
    // 0x148f318: ldur            x16, [fp, #-0x38]
    // 0x148f31c: str             x16, [SP]
    // 0x148f320: mov             x1, x0
    // 0x148f324: ldur            x2, [fp, #-0x20]
    // 0x148f328: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x3, progressIndicatorBuilder, 0x5, width, 0x2, null]
    //     0x148f328: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c710] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x3, "progressIndicatorBuilder", 0x5, "width", 0x2, Null]
    //     0x148f32c: ldr             x4, [x4, #0x710]
    // 0x148f330: r0 = CachedNetworkImage()
    //     0x148f330: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x148f334: r0 = ClipRRect()
    //     0x148f334: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x148f338: mov             x2, x0
    // 0x148f33c: ldur            x0, [fp, #-0x28]
    // 0x148f340: stur            x2, [fp, #-0x30]
    // 0x148f344: StoreField: r2->field_f = r0
    //     0x148f344: stur            w0, [x2, #0xf]
    // 0x148f348: r0 = Instance_Clip
    //     0x148f348: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x148f34c: ldr             x0, [x0, #0x138]
    // 0x148f350: ArrayStore: r2[0] = r0  ; List_4
    //     0x148f350: stur            w0, [x2, #0x17]
    // 0x148f354: ldur            x0, [fp, #-0x40]
    // 0x148f358: StoreField: r2->field_b = r0
    //     0x148f358: stur            w0, [x2, #0xb]
    // 0x148f35c: ldur            x0, [fp, #-0x10]
    // 0x148f360: LoadField: r1 = r0->field_f
    //     0x148f360: ldur            w1, [x0, #0xf]
    // 0x148f364: DecompressPointer r1
    //     0x148f364: add             x1, x1, HEAP, lsl #32
    // 0x148f368: cmp             w1, NULL
    // 0x148f36c: b.ne            #0x148f378
    // 0x148f370: r3 = ""
    //     0x148f370: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148f374: b               #0x148f37c
    // 0x148f378: mov             x3, x1
    // 0x148f37c: ldur            x1, [fp, #-0x18]
    // 0x148f380: stur            x3, [fp, #-0x20]
    // 0x148f384: r0 = of()
    //     0x148f384: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148f388: LoadField: r1 = r0->field_87
    //     0x148f388: ldur            w1, [x0, #0x87]
    // 0x148f38c: DecompressPointer r1
    //     0x148f38c: add             x1, x1, HEAP, lsl #32
    // 0x148f390: LoadField: r0 = r1->field_2b
    //     0x148f390: ldur            w0, [x1, #0x2b]
    // 0x148f394: DecompressPointer r0
    //     0x148f394: add             x0, x0, HEAP, lsl #32
    // 0x148f398: stur            x0, [fp, #-0x28]
    // 0x148f39c: r1 = Instance_Color
    //     0x148f39c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148f3a0: d0 = 0.700000
    //     0x148f3a0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x148f3a4: ldr             d0, [x17, #0xf48]
    // 0x148f3a8: r0 = withOpacity()
    //     0x148f3a8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x148f3ac: r16 = 12.000000
    //     0x148f3ac: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x148f3b0: ldr             x16, [x16, #0x9e8]
    // 0x148f3b4: stp             x0, x16, [SP]
    // 0x148f3b8: ldur            x1, [fp, #-0x28]
    // 0x148f3bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x148f3bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x148f3c0: ldr             x4, [x4, #0xaa0]
    // 0x148f3c4: r0 = copyWith()
    //     0x148f3c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148f3c8: stur            x0, [fp, #-0x28]
    // 0x148f3cc: r0 = Text()
    //     0x148f3cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148f3d0: mov             x3, x0
    // 0x148f3d4: ldur            x0, [fp, #-0x20]
    // 0x148f3d8: stur            x3, [fp, #-0x38]
    // 0x148f3dc: StoreField: r3->field_b = r0
    //     0x148f3dc: stur            w0, [x3, #0xb]
    // 0x148f3e0: ldur            x0, [fp, #-0x28]
    // 0x148f3e4: StoreField: r3->field_13 = r0
    //     0x148f3e4: stur            w0, [x3, #0x13]
    // 0x148f3e8: r0 = 4
    //     0x148f3e8: movz            x0, #0x4
    // 0x148f3ec: StoreField: r3->field_37 = r0
    //     0x148f3ec: stur            w0, [x3, #0x37]
    // 0x148f3f0: r1 = Null
    //     0x148f3f0: mov             x1, NULL
    // 0x148f3f4: r2 = 8
    //     0x148f3f4: movz            x2, #0x8
    // 0x148f3f8: r0 = AllocateArray()
    //     0x148f3f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148f3fc: mov             x1, x0
    // 0x148f400: stur            x1, [fp, #-0x20]
    // 0x148f404: r16 = "Size: "
    //     0x148f404: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0x148f408: ldr             x16, [x16, #0xf00]
    // 0x148f40c: StoreField: r1->field_f = r16
    //     0x148f40c: stur            w16, [x1, #0xf]
    // 0x148f410: ldur            x2, [fp, #-0x10]
    // 0x148f414: LoadField: r0 = r2->field_2f
    //     0x148f414: ldur            w0, [x2, #0x2f]
    // 0x148f418: DecompressPointer r0
    //     0x148f418: add             x0, x0, HEAP, lsl #32
    // 0x148f41c: cmp             w0, NULL
    // 0x148f420: b.ne            #0x148f428
    // 0x148f424: r0 = ""
    //     0x148f424: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148f428: StoreField: r1->field_13 = r0
    //     0x148f428: stur            w0, [x1, #0x13]
    // 0x148f42c: r16 = " / Qty: "
    //     0x148f42c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x148f430: ldr             x16, [x16, #0x760]
    // 0x148f434: ArrayStore: r1[0] = r16  ; List_4
    //     0x148f434: stur            w16, [x1, #0x17]
    // 0x148f438: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x148f438: ldur            w0, [x2, #0x17]
    // 0x148f43c: DecompressPointer r0
    //     0x148f43c: add             x0, x0, HEAP, lsl #32
    // 0x148f440: r3 = 60
    //     0x148f440: movz            x3, #0x3c
    // 0x148f444: branchIfSmi(r0, 0x148f450)
    //     0x148f444: tbz             w0, #0, #0x148f450
    // 0x148f448: r3 = LoadClassIdInstr(r0)
    //     0x148f448: ldur            x3, [x0, #-1]
    //     0x148f44c: ubfx            x3, x3, #0xc, #0x14
    // 0x148f450: str             x0, [SP]
    // 0x148f454: mov             x0, x3
    // 0x148f458: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x148f458: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x148f45c: r0 = GDT[cid_x0 + 0x2700]()
    //     0x148f45c: movz            x17, #0x2700
    //     0x148f460: add             lr, x0, x17
    //     0x148f464: ldr             lr, [x21, lr, lsl #3]
    //     0x148f468: blr             lr
    // 0x148f46c: mov             x1, x0
    // 0x148f470: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x148f470: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x148f474: r0 = tryParse()
    //     0x148f474: bl              #0x62d820  ; [dart:core] int::tryParse
    // 0x148f478: cmp             w0, NULL
    // 0x148f47c: b.ne            #0x148f488
    // 0x148f480: r3 = 0
    //     0x148f480: movz            x3, #0
    // 0x148f484: b               #0x148f498
    // 0x148f488: r1 = LoadInt32Instr(r0)
    //     0x148f488: sbfx            x1, x0, #1, #0x1f
    //     0x148f48c: tbz             w0, #0, #0x148f494
    //     0x148f490: ldur            x1, [x0, #7]
    // 0x148f494: mov             x3, x1
    // 0x148f498: ldur            x2, [fp, #-0x10]
    // 0x148f49c: r0 = BoxInt64Instr(r3)
    //     0x148f49c: sbfiz           x0, x3, #1, #0x1f
    //     0x148f4a0: cmp             x3, x0, asr #1
    //     0x148f4a4: b.eq            #0x148f4b0
    //     0x148f4a8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x148f4ac: stur            x3, [x0, #7]
    // 0x148f4b0: ldur            x1, [fp, #-0x20]
    // 0x148f4b4: ArrayStore: r1[3] = r0  ; List_4
    //     0x148f4b4: add             x25, x1, #0x1b
    //     0x148f4b8: str             w0, [x25]
    //     0x148f4bc: tbz             w0, #0, #0x148f4d8
    //     0x148f4c0: ldurb           w16, [x1, #-1]
    //     0x148f4c4: ldurb           w17, [x0, #-1]
    //     0x148f4c8: and             x16, x17, x16, lsr #2
    //     0x148f4cc: tst             x16, HEAP, lsr #32
    //     0x148f4d0: b.eq            #0x148f4d8
    //     0x148f4d4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x148f4d8: ldur            x16, [fp, #-0x20]
    // 0x148f4dc: str             x16, [SP]
    // 0x148f4e0: r0 = _interpolate()
    //     0x148f4e0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x148f4e4: ldur            x1, [fp, #-0x18]
    // 0x148f4e8: stur            x0, [fp, #-0x20]
    // 0x148f4ec: r0 = of()
    //     0x148f4ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148f4f0: LoadField: r1 = r0->field_87
    //     0x148f4f0: ldur            w1, [x0, #0x87]
    // 0x148f4f4: DecompressPointer r1
    //     0x148f4f4: add             x1, x1, HEAP, lsl #32
    // 0x148f4f8: LoadField: r0 = r1->field_7
    //     0x148f4f8: ldur            w0, [x1, #7]
    // 0x148f4fc: DecompressPointer r0
    //     0x148f4fc: add             x0, x0, HEAP, lsl #32
    // 0x148f500: stur            x0, [fp, #-0x28]
    // 0x148f504: r1 = Instance_Color
    //     0x148f504: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148f508: d0 = 0.700000
    //     0x148f508: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x148f50c: ldr             d0, [x17, #0xf48]
    // 0x148f510: r0 = withOpacity()
    //     0x148f510: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x148f514: r16 = 12.000000
    //     0x148f514: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x148f518: ldr             x16, [x16, #0x9e8]
    // 0x148f51c: stp             x0, x16, [SP]
    // 0x148f520: ldur            x1, [fp, #-0x28]
    // 0x148f524: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x148f524: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x148f528: ldr             x4, [x4, #0xaa0]
    // 0x148f52c: r0 = copyWith()
    //     0x148f52c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148f530: stur            x0, [fp, #-0x28]
    // 0x148f534: r0 = Text()
    //     0x148f534: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148f538: mov             x2, x0
    // 0x148f53c: ldur            x0, [fp, #-0x20]
    // 0x148f540: stur            x2, [fp, #-0x40]
    // 0x148f544: StoreField: r2->field_b = r0
    //     0x148f544: stur            w0, [x2, #0xb]
    // 0x148f548: ldur            x0, [fp, #-0x28]
    // 0x148f54c: StoreField: r2->field_13 = r0
    //     0x148f54c: stur            w0, [x2, #0x13]
    // 0x148f550: ldur            x0, [fp, #-0x10]
    // 0x148f554: LoadField: r1 = r0->field_2b
    //     0x148f554: ldur            w1, [x0, #0x2b]
    // 0x148f558: DecompressPointer r1
    //     0x148f558: add             x1, x1, HEAP, lsl #32
    // 0x148f55c: cmp             w1, NULL
    // 0x148f560: b.ne            #0x148f570
    // 0x148f564: r5 = "₹0"
    //     0x148f564: add             x5, PP, #0x34, lsl #12  ; [pp+0x34a10] "₹0"
    //     0x148f568: ldr             x5, [x5, #0xa10]
    // 0x148f56c: b               #0x148f574
    // 0x148f570: mov             x5, x1
    // 0x148f574: ldur            x4, [fp, #-0x30]
    // 0x148f578: ldur            x3, [fp, #-0x38]
    // 0x148f57c: ldur            x1, [fp, #-0x18]
    // 0x148f580: stur            x5, [fp, #-0x20]
    // 0x148f584: r0 = of()
    //     0x148f584: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148f588: LoadField: r1 = r0->field_87
    //     0x148f588: ldur            w1, [x0, #0x87]
    // 0x148f58c: DecompressPointer r1
    //     0x148f58c: add             x1, x1, HEAP, lsl #32
    // 0x148f590: LoadField: r0 = r1->field_2b
    //     0x148f590: ldur            w0, [x1, #0x2b]
    // 0x148f594: DecompressPointer r0
    //     0x148f594: add             x0, x0, HEAP, lsl #32
    // 0x148f598: r16 = 12.000000
    //     0x148f598: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x148f59c: ldr             x16, [x16, #0x9e8]
    // 0x148f5a0: r30 = Instance_Color
    //     0x148f5a0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148f5a4: stp             lr, x16, [SP]
    // 0x148f5a8: mov             x1, x0
    // 0x148f5ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x148f5ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x148f5b0: ldr             x4, [x4, #0xaa0]
    // 0x148f5b4: r0 = copyWith()
    //     0x148f5b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148f5b8: stur            x0, [fp, #-0x18]
    // 0x148f5bc: r0 = Text()
    //     0x148f5bc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148f5c0: mov             x3, x0
    // 0x148f5c4: ldur            x0, [fp, #-0x20]
    // 0x148f5c8: stur            x3, [fp, #-0x28]
    // 0x148f5cc: StoreField: r3->field_b = r0
    //     0x148f5cc: stur            w0, [x3, #0xb]
    // 0x148f5d0: ldur            x0, [fp, #-0x18]
    // 0x148f5d4: StoreField: r3->field_13 = r0
    //     0x148f5d4: stur            w0, [x3, #0x13]
    // 0x148f5d8: r1 = Null
    //     0x148f5d8: mov             x1, NULL
    // 0x148f5dc: r2 = 10
    //     0x148f5dc: movz            x2, #0xa
    // 0x148f5e0: r0 = AllocateArray()
    //     0x148f5e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148f5e4: mov             x2, x0
    // 0x148f5e8: ldur            x0, [fp, #-0x38]
    // 0x148f5ec: stur            x2, [fp, #-0x18]
    // 0x148f5f0: StoreField: r2->field_f = r0
    //     0x148f5f0: stur            w0, [x2, #0xf]
    // 0x148f5f4: r16 = Instance_SizedBox
    //     0x148f5f4: add             x16, PP, #0x34, lsl #12  ; [pp+0x34a60] Obj!SizedBox@d68041
    //     0x148f5f8: ldr             x16, [x16, #0xa60]
    // 0x148f5fc: StoreField: r2->field_13 = r16
    //     0x148f5fc: stur            w16, [x2, #0x13]
    // 0x148f600: ldur            x0, [fp, #-0x40]
    // 0x148f604: ArrayStore: r2[0] = r0  ; List_4
    //     0x148f604: stur            w0, [x2, #0x17]
    // 0x148f608: r16 = Instance_SizedBox
    //     0x148f608: add             x16, PP, #0x34, lsl #12  ; [pp+0x34a60] Obj!SizedBox@d68041
    //     0x148f60c: ldr             x16, [x16, #0xa60]
    // 0x148f610: StoreField: r2->field_1b = r16
    //     0x148f610: stur            w16, [x2, #0x1b]
    // 0x148f614: ldur            x0, [fp, #-0x28]
    // 0x148f618: StoreField: r2->field_1f = r0
    //     0x148f618: stur            w0, [x2, #0x1f]
    // 0x148f61c: r1 = <Widget>
    //     0x148f61c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148f620: r0 = AllocateGrowableArray()
    //     0x148f620: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148f624: mov             x1, x0
    // 0x148f628: ldur            x0, [fp, #-0x18]
    // 0x148f62c: stur            x1, [fp, #-0x20]
    // 0x148f630: StoreField: r1->field_f = r0
    //     0x148f630: stur            w0, [x1, #0xf]
    // 0x148f634: r0 = 10
    //     0x148f634: movz            x0, #0xa
    // 0x148f638: StoreField: r1->field_b = r0
    //     0x148f638: stur            w0, [x1, #0xb]
    // 0x148f63c: r0 = Column()
    //     0x148f63c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x148f640: mov             x1, x0
    // 0x148f644: r0 = Instance_Axis
    //     0x148f644: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x148f648: stur            x1, [fp, #-0x18]
    // 0x148f64c: StoreField: r1->field_f = r0
    //     0x148f64c: stur            w0, [x1, #0xf]
    // 0x148f650: r2 = Instance_MainAxisAlignment
    //     0x148f650: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x148f654: ldr             x2, [x2, #0xa8]
    // 0x148f658: StoreField: r1->field_13 = r2
    //     0x148f658: stur            w2, [x1, #0x13]
    // 0x148f65c: r2 = Instance_MainAxisSize
    //     0x148f65c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148f660: ldr             x2, [x2, #0xa10]
    // 0x148f664: ArrayStore: r1[0] = r2  ; List_4
    //     0x148f664: stur            w2, [x1, #0x17]
    // 0x148f668: r3 = Instance_CrossAxisAlignment
    //     0x148f668: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x148f66c: ldr             x3, [x3, #0x890]
    // 0x148f670: StoreField: r1->field_1b = r3
    //     0x148f670: stur            w3, [x1, #0x1b]
    // 0x148f674: r4 = Instance_VerticalDirection
    //     0x148f674: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148f678: ldr             x4, [x4, #0xa20]
    // 0x148f67c: StoreField: r1->field_23 = r4
    //     0x148f67c: stur            w4, [x1, #0x23]
    // 0x148f680: r5 = Instance_Clip
    //     0x148f680: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148f684: ldr             x5, [x5, #0x38]
    // 0x148f688: StoreField: r1->field_2b = r5
    //     0x148f688: stur            w5, [x1, #0x2b]
    // 0x148f68c: StoreField: r1->field_2f = rZR
    //     0x148f68c: stur            xzr, [x1, #0x2f]
    // 0x148f690: ldur            x6, [fp, #-0x20]
    // 0x148f694: StoreField: r1->field_b = r6
    //     0x148f694: stur            w6, [x1, #0xb]
    // 0x148f698: r0 = Padding()
    //     0x148f698: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148f69c: mov             x2, x0
    // 0x148f6a0: r0 = Instance_EdgeInsets
    //     0x148f6a0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0x148f6a4: ldr             x0, [x0, #0xf30]
    // 0x148f6a8: stur            x2, [fp, #-0x20]
    // 0x148f6ac: StoreField: r2->field_f = r0
    //     0x148f6ac: stur            w0, [x2, #0xf]
    // 0x148f6b0: ldur            x0, [fp, #-0x18]
    // 0x148f6b4: StoreField: r2->field_b = r0
    //     0x148f6b4: stur            w0, [x2, #0xb]
    // 0x148f6b8: r1 = <FlexParentData>
    //     0x148f6b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x148f6bc: ldr             x1, [x1, #0xe00]
    // 0x148f6c0: r0 = Expanded()
    //     0x148f6c0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x148f6c4: mov             x3, x0
    // 0x148f6c8: r0 = 1
    //     0x148f6c8: movz            x0, #0x1
    // 0x148f6cc: stur            x3, [fp, #-0x18]
    // 0x148f6d0: StoreField: r3->field_13 = r0
    //     0x148f6d0: stur            x0, [x3, #0x13]
    // 0x148f6d4: r0 = Instance_FlexFit
    //     0x148f6d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x148f6d8: ldr             x0, [x0, #0xe08]
    // 0x148f6dc: StoreField: r3->field_1b = r0
    //     0x148f6dc: stur            w0, [x3, #0x1b]
    // 0x148f6e0: ldur            x0, [fp, #-0x20]
    // 0x148f6e4: StoreField: r3->field_b = r0
    //     0x148f6e4: stur            w0, [x3, #0xb]
    // 0x148f6e8: r1 = Null
    //     0x148f6e8: mov             x1, NULL
    // 0x148f6ec: r2 = 4
    //     0x148f6ec: movz            x2, #0x4
    // 0x148f6f0: r0 = AllocateArray()
    //     0x148f6f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148f6f4: mov             x2, x0
    // 0x148f6f8: ldur            x0, [fp, #-0x30]
    // 0x148f6fc: stur            x2, [fp, #-0x20]
    // 0x148f700: StoreField: r2->field_f = r0
    //     0x148f700: stur            w0, [x2, #0xf]
    // 0x148f704: ldur            x0, [fp, #-0x18]
    // 0x148f708: StoreField: r2->field_13 = r0
    //     0x148f708: stur            w0, [x2, #0x13]
    // 0x148f70c: r1 = <Widget>
    //     0x148f70c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148f710: r0 = AllocateGrowableArray()
    //     0x148f710: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148f714: mov             x1, x0
    // 0x148f718: ldur            x0, [fp, #-0x20]
    // 0x148f71c: stur            x1, [fp, #-0x18]
    // 0x148f720: StoreField: r1->field_f = r0
    //     0x148f720: stur            w0, [x1, #0xf]
    // 0x148f724: r2 = 4
    //     0x148f724: movz            x2, #0x4
    // 0x148f728: StoreField: r1->field_b = r2
    //     0x148f728: stur            w2, [x1, #0xb]
    // 0x148f72c: r0 = Row()
    //     0x148f72c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x148f730: mov             x2, x0
    // 0x148f734: r0 = Instance_Axis
    //     0x148f734: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x148f738: stur            x2, [fp, #-0x20]
    // 0x148f73c: StoreField: r2->field_f = r0
    //     0x148f73c: stur            w0, [x2, #0xf]
    // 0x148f740: r0 = Instance_MainAxisAlignment
    //     0x148f740: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x148f744: ldr             x0, [x0, #0xa08]
    // 0x148f748: StoreField: r2->field_13 = r0
    //     0x148f748: stur            w0, [x2, #0x13]
    // 0x148f74c: r3 = Instance_MainAxisSize
    //     0x148f74c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148f750: ldr             x3, [x3, #0xa10]
    // 0x148f754: ArrayStore: r2[0] = r3  ; List_4
    //     0x148f754: stur            w3, [x2, #0x17]
    // 0x148f758: r1 = Instance_CrossAxisAlignment
    //     0x148f758: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x148f75c: ldr             x1, [x1, #0x890]
    // 0x148f760: StoreField: r2->field_1b = r1
    //     0x148f760: stur            w1, [x2, #0x1b]
    // 0x148f764: r4 = Instance_VerticalDirection
    //     0x148f764: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148f768: ldr             x4, [x4, #0xa20]
    // 0x148f76c: StoreField: r2->field_23 = r4
    //     0x148f76c: stur            w4, [x2, #0x23]
    // 0x148f770: r5 = Instance_Clip
    //     0x148f770: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148f774: ldr             x5, [x5, #0x38]
    // 0x148f778: StoreField: r2->field_2b = r5
    //     0x148f778: stur            w5, [x2, #0x2b]
    // 0x148f77c: StoreField: r2->field_2f = rZR
    //     0x148f77c: stur            xzr, [x2, #0x2f]
    // 0x148f780: ldur            x1, [fp, #-0x18]
    // 0x148f784: StoreField: r2->field_b = r1
    //     0x148f784: stur            w1, [x2, #0xb]
    // 0x148f788: ldur            x6, [fp, #-0x10]
    // 0x148f78c: LoadField: r1 = r6->field_3f
    //     0x148f78c: ldur            w1, [x6, #0x3f]
    // 0x148f790: DecompressPointer r1
    //     0x148f790: add             x1, x1, HEAP, lsl #32
    // 0x148f794: cmp             w1, NULL
    // 0x148f798: b.ne            #0x148f7a4
    // 0x148f79c: r1 = Null
    //     0x148f79c: mov             x1, NULL
    // 0x148f7a0: b               #0x148f7b8
    // 0x148f7a4: LoadField: r7 = r1->field_b
    //     0x148f7a4: ldur            w7, [x1, #0xb]
    // 0x148f7a8: cbnz            w7, #0x148f7b4
    // 0x148f7ac: r1 = false
    //     0x148f7ac: add             x1, NULL, #0x30  ; false
    // 0x148f7b0: b               #0x148f7b8
    // 0x148f7b4: r1 = true
    //     0x148f7b4: add             x1, NULL, #0x20  ; true
    // 0x148f7b8: cmp             w1, NULL
    // 0x148f7bc: b.eq            #0x148f7d0
    // 0x148f7c0: tbnz            w1, #4, #0x148f7d0
    // 0x148f7c4: mov             x0, x6
    // 0x148f7c8: r2 = true
    //     0x148f7c8: add             x2, NULL, #0x20  ; true
    // 0x148f7cc: b               #0x148f828
    // 0x148f7d0: ldur            x1, [fp, #-8]
    // 0x148f7d4: r0 = controller()
    //     0x148f7d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148f7d8: LoadField: r1 = r0->field_6f
    //     0x148f7d8: ldur            w1, [x0, #0x6f]
    // 0x148f7dc: DecompressPointer r1
    //     0x148f7dc: add             x1, x1, HEAP, lsl #32
    // 0x148f7e0: r0 = value()
    //     0x148f7e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148f7e4: LoadField: r1 = r0->field_13
    //     0x148f7e4: ldur            w1, [x0, #0x13]
    // 0x148f7e8: DecompressPointer r1
    //     0x148f7e8: add             x1, x1, HEAP, lsl #32
    // 0x148f7ec: cmp             w1, NULL
    // 0x148f7f0: b.ne            #0x148f7fc
    // 0x148f7f4: r0 = Null
    //     0x148f7f4: mov             x0, NULL
    // 0x148f7f8: b               #0x148f814
    // 0x148f7fc: LoadField: r0 = r1->field_b
    //     0x148f7fc: ldur            w0, [x1, #0xb]
    // 0x148f800: cbnz            w0, #0x148f80c
    // 0x148f804: r1 = false
    //     0x148f804: add             x1, NULL, #0x30  ; false
    // 0x148f808: b               #0x148f810
    // 0x148f80c: r1 = true
    //     0x148f80c: add             x1, NULL, #0x20  ; true
    // 0x148f810: mov             x0, x1
    // 0x148f814: cmp             w0, NULL
    // 0x148f818: b.ne            #0x148f820
    // 0x148f81c: r0 = false
    //     0x148f81c: add             x0, NULL, #0x30  ; false
    // 0x148f820: mov             x2, x0
    // 0x148f824: ldur            x0, [fp, #-0x10]
    // 0x148f828: stur            x2, [fp, #-0x28]
    // 0x148f82c: LoadField: r3 = r0->field_3f
    //     0x148f82c: ldur            w3, [x0, #0x3f]
    // 0x148f830: DecompressPointer r3
    //     0x148f830: add             x3, x3, HEAP, lsl #32
    // 0x148f834: ldur            x1, [fp, #-8]
    // 0x148f838: stur            x3, [fp, #-0x18]
    // 0x148f83c: r0 = controller()
    //     0x148f83c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148f840: LoadField: r1 = r0->field_6f
    //     0x148f840: ldur            w1, [x0, #0x6f]
    // 0x148f844: DecompressPointer r1
    //     0x148f844: add             x1, x1, HEAP, lsl #32
    // 0x148f848: r0 = value()
    //     0x148f848: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148f84c: mov             x2, x0
    // 0x148f850: ldur            x0, [fp, #-0x10]
    // 0x148f854: stur            x2, [fp, #-0x30]
    // 0x148f858: LoadField: r1 = r0->field_43
    //     0x148f858: ldur            w1, [x0, #0x43]
    // 0x148f85c: DecompressPointer r1
    //     0x148f85c: add             x1, x1, HEAP, lsl #32
    // 0x148f860: cbz             w1, #0x148f878
    // 0x148f864: LoadField: r1 = r0->field_47
    //     0x148f864: ldur            w1, [x0, #0x47]
    // 0x148f868: DecompressPointer r1
    //     0x148f868: add             x1, x1, HEAP, lsl #32
    // 0x148f86c: mov             x4, x1
    // 0x148f870: mov             x0, x2
    // 0x148f874: b               #0x148f914
    // 0x148f878: ldur            x1, [fp, #-8]
    // 0x148f87c: r0 = controller()
    //     0x148f87c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148f880: LoadField: r1 = r0->field_bf
    //     0x148f880: ldur            w1, [x0, #0xbf]
    // 0x148f884: DecompressPointer r1
    //     0x148f884: add             x1, x1, HEAP, lsl #32
    // 0x148f888: cmp             w1, NULL
    // 0x148f88c: b.eq            #0x148f8fc
    // 0x148f890: ldur            x1, [fp, #-8]
    // 0x148f894: r0 = controller()
    //     0x148f894: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148f898: LoadField: r1 = r0->field_bf
    //     0x148f898: ldur            w1, [x0, #0xbf]
    // 0x148f89c: DecompressPointer r1
    //     0x148f89c: add             x1, x1, HEAP, lsl #32
    // 0x148f8a0: cmp             w1, NULL
    // 0x148f8a4: b.ne            #0x148f8b0
    // 0x148f8a8: r0 = Null
    //     0x148f8a8: mov             x0, NULL
    // 0x148f8ac: b               #0x148f8c8
    // 0x148f8b0: LoadField: r0 = r1->field_7
    //     0x148f8b0: ldur            w0, [x1, #7]
    // 0x148f8b4: cbnz            w0, #0x148f8c0
    // 0x148f8b8: r1 = false
    //     0x148f8b8: add             x1, NULL, #0x30  ; false
    // 0x148f8bc: b               #0x148f8c4
    // 0x148f8c0: r1 = true
    //     0x148f8c0: add             x1, NULL, #0x20  ; true
    // 0x148f8c4: mov             x0, x1
    // 0x148f8c8: cmp             w0, NULL
    // 0x148f8cc: b.eq            #0x148f8fc
    // 0x148f8d0: tbnz            w0, #4, #0x148f8fc
    // 0x148f8d4: ldur            x1, [fp, #-8]
    // 0x148f8d8: r0 = controller()
    //     0x148f8d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148f8dc: LoadField: r1 = r0->field_bf
    //     0x148f8dc: ldur            w1, [x0, #0xbf]
    // 0x148f8e0: DecompressPointer r1
    //     0x148f8e0: add             x1, x1, HEAP, lsl #32
    // 0x148f8e4: cmp             w1, NULL
    // 0x148f8e8: b.ne            #0x148f8f4
    // 0x148f8ec: r0 = ""
    //     0x148f8ec: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148f8f0: b               #0x148f90c
    // 0x148f8f4: mov             x0, x1
    // 0x148f8f8: b               #0x148f90c
    // 0x148f8fc: ldur            x0, [fp, #-0x10]
    // 0x148f900: LoadField: r1 = r0->field_47
    //     0x148f900: ldur            w1, [x0, #0x47]
    // 0x148f904: DecompressPointer r1
    //     0x148f904: add             x1, x1, HEAP, lsl #32
    // 0x148f908: mov             x0, x1
    // 0x148f90c: mov             x4, x0
    // 0x148f910: ldur            x0, [fp, #-0x30]
    // 0x148f914: ldur            x3, [fp, #-0x20]
    // 0x148f918: ldur            x1, [fp, #-0x28]
    // 0x148f91c: ldur            x2, [fp, #-0x18]
    // 0x148f920: stur            x4, [fp, #-8]
    // 0x148f924: r0 = CustomisedStrip()
    //     0x148f924: bl              #0xa078f0  ; AllocateCustomisedStripStub -> CustomisedStrip (size=0x18)
    // 0x148f928: mov             x1, x0
    // 0x148f92c: ldur            x0, [fp, #-0x18]
    // 0x148f930: stur            x1, [fp, #-0x10]
    // 0x148f934: StoreField: r1->field_b = r0
    //     0x148f934: stur            w0, [x1, #0xb]
    // 0x148f938: ldur            x0, [fp, #-0x30]
    // 0x148f93c: StoreField: r1->field_f = r0
    //     0x148f93c: stur            w0, [x1, #0xf]
    // 0x148f940: ldur            x0, [fp, #-8]
    // 0x148f944: StoreField: r1->field_13 = r0
    //     0x148f944: stur            w0, [x1, #0x13]
    // 0x148f948: r0 = Visibility()
    //     0x148f948: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x148f94c: mov             x3, x0
    // 0x148f950: ldur            x0, [fp, #-0x10]
    // 0x148f954: stur            x3, [fp, #-8]
    // 0x148f958: StoreField: r3->field_b = r0
    //     0x148f958: stur            w0, [x3, #0xb]
    // 0x148f95c: r0 = Instance_SizedBox
    //     0x148f95c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x148f960: StoreField: r3->field_f = r0
    //     0x148f960: stur            w0, [x3, #0xf]
    // 0x148f964: ldur            x0, [fp, #-0x28]
    // 0x148f968: StoreField: r3->field_13 = r0
    //     0x148f968: stur            w0, [x3, #0x13]
    // 0x148f96c: r0 = false
    //     0x148f96c: add             x0, NULL, #0x30  ; false
    // 0x148f970: ArrayStore: r3[0] = r0  ; List_4
    //     0x148f970: stur            w0, [x3, #0x17]
    // 0x148f974: StoreField: r3->field_1b = r0
    //     0x148f974: stur            w0, [x3, #0x1b]
    // 0x148f978: StoreField: r3->field_1f = r0
    //     0x148f978: stur            w0, [x3, #0x1f]
    // 0x148f97c: StoreField: r3->field_23 = r0
    //     0x148f97c: stur            w0, [x3, #0x23]
    // 0x148f980: StoreField: r3->field_27 = r0
    //     0x148f980: stur            w0, [x3, #0x27]
    // 0x148f984: StoreField: r3->field_2b = r0
    //     0x148f984: stur            w0, [x3, #0x2b]
    // 0x148f988: r1 = Null
    //     0x148f988: mov             x1, NULL
    // 0x148f98c: r2 = 4
    //     0x148f98c: movz            x2, #0x4
    // 0x148f990: r0 = AllocateArray()
    //     0x148f990: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148f994: mov             x2, x0
    // 0x148f998: ldur            x0, [fp, #-0x20]
    // 0x148f99c: stur            x2, [fp, #-0x10]
    // 0x148f9a0: StoreField: r2->field_f = r0
    //     0x148f9a0: stur            w0, [x2, #0xf]
    // 0x148f9a4: ldur            x0, [fp, #-8]
    // 0x148f9a8: StoreField: r2->field_13 = r0
    //     0x148f9a8: stur            w0, [x2, #0x13]
    // 0x148f9ac: r1 = <Widget>
    //     0x148f9ac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148f9b0: r0 = AllocateGrowableArray()
    //     0x148f9b0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148f9b4: mov             x1, x0
    // 0x148f9b8: ldur            x0, [fp, #-0x10]
    // 0x148f9bc: stur            x1, [fp, #-8]
    // 0x148f9c0: StoreField: r1->field_f = r0
    //     0x148f9c0: stur            w0, [x1, #0xf]
    // 0x148f9c4: r0 = 4
    //     0x148f9c4: movz            x0, #0x4
    // 0x148f9c8: StoreField: r1->field_b = r0
    //     0x148f9c8: stur            w0, [x1, #0xb]
    // 0x148f9cc: r0 = Column()
    //     0x148f9cc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x148f9d0: mov             x1, x0
    // 0x148f9d4: r0 = Instance_Axis
    //     0x148f9d4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x148f9d8: stur            x1, [fp, #-0x10]
    // 0x148f9dc: StoreField: r1->field_f = r0
    //     0x148f9dc: stur            w0, [x1, #0xf]
    // 0x148f9e0: r0 = Instance_MainAxisAlignment
    //     0x148f9e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x148f9e4: ldr             x0, [x0, #0xa08]
    // 0x148f9e8: StoreField: r1->field_13 = r0
    //     0x148f9e8: stur            w0, [x1, #0x13]
    // 0x148f9ec: r0 = Instance_MainAxisSize
    //     0x148f9ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148f9f0: ldr             x0, [x0, #0xa10]
    // 0x148f9f4: ArrayStore: r1[0] = r0  ; List_4
    //     0x148f9f4: stur            w0, [x1, #0x17]
    // 0x148f9f8: r0 = Instance_CrossAxisAlignment
    //     0x148f9f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x148f9fc: ldr             x0, [x0, #0xa18]
    // 0x148fa00: StoreField: r1->field_1b = r0
    //     0x148fa00: stur            w0, [x1, #0x1b]
    // 0x148fa04: r0 = Instance_VerticalDirection
    //     0x148fa04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148fa08: ldr             x0, [x0, #0xa20]
    // 0x148fa0c: StoreField: r1->field_23 = r0
    //     0x148fa0c: stur            w0, [x1, #0x23]
    // 0x148fa10: r0 = Instance_Clip
    //     0x148fa10: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148fa14: ldr             x0, [x0, #0x38]
    // 0x148fa18: StoreField: r1->field_2b = r0
    //     0x148fa18: stur            w0, [x1, #0x2b]
    // 0x148fa1c: StoreField: r1->field_2f = rZR
    //     0x148fa1c: stur            xzr, [x1, #0x2f]
    // 0x148fa20: ldur            x0, [fp, #-8]
    // 0x148fa24: StoreField: r1->field_b = r0
    //     0x148fa24: stur            w0, [x1, #0xb]
    // 0x148fa28: r0 = Padding()
    //     0x148fa28: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148fa2c: r1 = Instance_EdgeInsets
    //     0x148fa2c: add             x1, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x148fa30: ldr             x1, [x1, #0x868]
    // 0x148fa34: StoreField: r0->field_f = r1
    //     0x148fa34: stur            w1, [x0, #0xf]
    // 0x148fa38: ldur            x1, [fp, #-0x10]
    // 0x148fa3c: StoreField: r0->field_b = r1
    //     0x148fa3c: stur            w1, [x0, #0xb]
    // 0x148fa40: LeaveFrame
    //     0x148fa40: mov             SP, fp
    //     0x148fa44: ldp             fp, lr, [SP], #0x10
    // 0x148fa48: ret
    //     0x148fa48: ret             
    // 0x148fa4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148fa4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148fa50: b               #0x148f26c
  }
  [closure] CheckoutBreadCrumb <anonymous closure>(dynamic) {
    // ** addr: 0x148fa54, size: 0x68
    // 0x148fa54: EnterFrame
    //     0x148fa54: stp             fp, lr, [SP, #-0x10]!
    //     0x148fa58: mov             fp, SP
    // 0x148fa5c: AllocStack(0x8)
    //     0x148fa5c: sub             SP, SP, #8
    // 0x148fa60: SetupParameters()
    //     0x148fa60: ldr             x0, [fp, #0x10]
    //     0x148fa64: ldur            w1, [x0, #0x17]
    //     0x148fa68: add             x1, x1, HEAP, lsl #32
    // 0x148fa6c: CheckStackOverflow
    //     0x148fa6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148fa70: cmp             SP, x16
    //     0x148fa74: b.ls            #0x148fab4
    // 0x148fa78: LoadField: r0 = r1->field_f
    //     0x148fa78: ldur            w0, [x1, #0xf]
    // 0x148fa7c: DecompressPointer r0
    //     0x148fa7c: add             x0, x0, HEAP, lsl #32
    // 0x148fa80: mov             x1, x0
    // 0x148fa84: r0 = controller()
    //     0x148fa84: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148fa88: LoadField: r1 = r0->field_ab
    //     0x148fa88: ldur            w1, [x0, #0xab]
    // 0x148fa8c: DecompressPointer r1
    //     0x148fa8c: add             x1, x1, HEAP, lsl #32
    // 0x148fa90: r0 = value()
    //     0x148fa90: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148fa94: stur            x0, [fp, #-8]
    // 0x148fa98: r0 = CheckoutBreadCrumb()
    //     0x148fa98: bl              #0x1484fc8  ; AllocateCheckoutBreadCrumbStub -> CheckoutBreadCrumb (size=0x18)
    // 0x148fa9c: ldur            x1, [fp, #-8]
    // 0x148faa0: StoreField: r0->field_b = r1
    //     0x148faa0: stur            w1, [x0, #0xb]
    // 0x148faa4: StoreField: r0->field_f = rZR
    //     0x148faa4: stur            xzr, [x0, #0xf]
    // 0x148faa8: LeaveFrame
    //     0x148faa8: mov             SP, fp
    //     0x148faac: ldp             fp, lr, [SP], #0x10
    // 0x148fab0: ret
    //     0x148fab0: ret             
    // 0x148fab4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148fab4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148fab8: b               #0x148fa78
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15d9ee8, size: 0x250
    // 0x15d9ee8: EnterFrame
    //     0x15d9ee8: stp             fp, lr, [SP, #-0x10]!
    //     0x15d9eec: mov             fp, SP
    // 0x15d9ef0: AllocStack(0x28)
    //     0x15d9ef0: sub             SP, SP, #0x28
    // 0x15d9ef4: SetupParameters(PaymentMethodsCheckoutWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15d9ef4: stur            x1, [fp, #-8]
    //     0x15d9ef8: stur            x2, [fp, #-0x10]
    // 0x15d9efc: CheckStackOverflow
    //     0x15d9efc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d9f00: cmp             SP, x16
    //     0x15d9f04: b.ls            #0x15da130
    // 0x15d9f08: r1 = 2
    //     0x15d9f08: movz            x1, #0x2
    // 0x15d9f0c: r0 = AllocateContext()
    //     0x15d9f0c: bl              #0x16f6108  ; AllocateContextStub
    // 0x15d9f10: ldur            x1, [fp, #-8]
    // 0x15d9f14: stur            x0, [fp, #-0x18]
    // 0x15d9f18: StoreField: r0->field_f = r1
    //     0x15d9f18: stur            w1, [x0, #0xf]
    // 0x15d9f1c: ldur            x2, [fp, #-0x10]
    // 0x15d9f20: StoreField: r0->field_13 = r2
    //     0x15d9f20: stur            w2, [x0, #0x13]
    // 0x15d9f24: r0 = Obx()
    //     0x15d9f24: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15d9f28: ldur            x2, [fp, #-0x18]
    // 0x15d9f2c: r1 = Function '<anonymous closure>':.
    //     0x15d9f2c: add             x1, PP, #0x43, lsl #12  ; [pp+0x43ca0] AnonymousClosure: (0x15ce458), in [package:customer_app/app/presentation/views/glass/post_order/order_failure/order_failed_widget.dart] OrderFailedWidget::appBar (0x15e3c40)
    //     0x15d9f30: ldr             x1, [x1, #0xca0]
    // 0x15d9f34: stur            x0, [fp, #-0x10]
    // 0x15d9f38: r0 = AllocateClosure()
    //     0x15d9f38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d9f3c: mov             x1, x0
    // 0x15d9f40: ldur            x0, [fp, #-0x10]
    // 0x15d9f44: StoreField: r0->field_b = r1
    //     0x15d9f44: stur            w1, [x0, #0xb]
    // 0x15d9f48: ldur            x1, [fp, #-8]
    // 0x15d9f4c: r0 = controller()
    //     0x15d9f4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d9f50: LoadField: r1 = r0->field_5b
    //     0x15d9f50: ldur            w1, [x0, #0x5b]
    // 0x15d9f54: DecompressPointer r1
    //     0x15d9f54: add             x1, x1, HEAP, lsl #32
    // 0x15d9f58: r0 = value()
    //     0x15d9f58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d9f5c: tbnz            w0, #4, #0x15d9ff4
    // 0x15d9f60: ldur            x2, [fp, #-0x18]
    // 0x15d9f64: LoadField: r1 = r2->field_13
    //     0x15d9f64: ldur            w1, [x2, #0x13]
    // 0x15d9f68: DecompressPointer r1
    //     0x15d9f68: add             x1, x1, HEAP, lsl #32
    // 0x15d9f6c: r0 = of()
    //     0x15d9f6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d9f70: LoadField: r1 = r0->field_5b
    //     0x15d9f70: ldur            w1, [x0, #0x5b]
    // 0x15d9f74: DecompressPointer r1
    //     0x15d9f74: add             x1, x1, HEAP, lsl #32
    // 0x15d9f78: stur            x1, [fp, #-8]
    // 0x15d9f7c: r0 = ColorFilter()
    //     0x15d9f7c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d9f80: mov             x1, x0
    // 0x15d9f84: ldur            x0, [fp, #-8]
    // 0x15d9f88: stur            x1, [fp, #-0x20]
    // 0x15d9f8c: StoreField: r1->field_7 = r0
    //     0x15d9f8c: stur            w0, [x1, #7]
    // 0x15d9f90: r0 = Instance_BlendMode
    //     0x15d9f90: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d9f94: ldr             x0, [x0, #0xb30]
    // 0x15d9f98: StoreField: r1->field_b = r0
    //     0x15d9f98: stur            w0, [x1, #0xb]
    // 0x15d9f9c: r2 = 1
    //     0x15d9f9c: movz            x2, #0x1
    // 0x15d9fa0: StoreField: r1->field_13 = r2
    //     0x15d9fa0: stur            x2, [x1, #0x13]
    // 0x15d9fa4: r0 = SvgPicture()
    //     0x15d9fa4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d9fa8: stur            x0, [fp, #-8]
    // 0x15d9fac: ldur            x16, [fp, #-0x20]
    // 0x15d9fb0: str             x16, [SP]
    // 0x15d9fb4: mov             x1, x0
    // 0x15d9fb8: r2 = "assets/images/search.svg"
    //     0x15d9fb8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15d9fbc: ldr             x2, [x2, #0xa30]
    // 0x15d9fc0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15d9fc0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15d9fc4: ldr             x4, [x4, #0xa38]
    // 0x15d9fc8: r0 = SvgPicture.asset()
    //     0x15d9fc8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d9fcc: r0 = Align()
    //     0x15d9fcc: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15d9fd0: r3 = Instance_Alignment
    //     0x15d9fd0: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d9fd4: ldr             x3, [x3, #0xb10]
    // 0x15d9fd8: StoreField: r0->field_f = r3
    //     0x15d9fd8: stur            w3, [x0, #0xf]
    // 0x15d9fdc: r4 = 1.000000
    //     0x15d9fdc: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d9fe0: StoreField: r0->field_13 = r4
    //     0x15d9fe0: stur            w4, [x0, #0x13]
    // 0x15d9fe4: ArrayStore: r0[0] = r4  ; List_4
    //     0x15d9fe4: stur            w4, [x0, #0x17]
    // 0x15d9fe8: ldur            x1, [fp, #-8]
    // 0x15d9fec: StoreField: r0->field_b = r1
    //     0x15d9fec: stur            w1, [x0, #0xb]
    // 0x15d9ff0: b               #0x15da0a4
    // 0x15d9ff4: ldur            x5, [fp, #-0x18]
    // 0x15d9ff8: r4 = 1.000000
    //     0x15d9ff8: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d9ffc: r0 = Instance_BlendMode
    //     0x15d9ffc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15da000: ldr             x0, [x0, #0xb30]
    // 0x15da004: r3 = Instance_Alignment
    //     0x15da004: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15da008: ldr             x3, [x3, #0xb10]
    // 0x15da00c: r2 = 1
    //     0x15da00c: movz            x2, #0x1
    // 0x15da010: LoadField: r1 = r5->field_13
    //     0x15da010: ldur            w1, [x5, #0x13]
    // 0x15da014: DecompressPointer r1
    //     0x15da014: add             x1, x1, HEAP, lsl #32
    // 0x15da018: r0 = of()
    //     0x15da018: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15da01c: LoadField: r1 = r0->field_5b
    //     0x15da01c: ldur            w1, [x0, #0x5b]
    // 0x15da020: DecompressPointer r1
    //     0x15da020: add             x1, x1, HEAP, lsl #32
    // 0x15da024: stur            x1, [fp, #-8]
    // 0x15da028: r0 = ColorFilter()
    //     0x15da028: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15da02c: mov             x1, x0
    // 0x15da030: ldur            x0, [fp, #-8]
    // 0x15da034: stur            x1, [fp, #-0x20]
    // 0x15da038: StoreField: r1->field_7 = r0
    //     0x15da038: stur            w0, [x1, #7]
    // 0x15da03c: r0 = Instance_BlendMode
    //     0x15da03c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15da040: ldr             x0, [x0, #0xb30]
    // 0x15da044: StoreField: r1->field_b = r0
    //     0x15da044: stur            w0, [x1, #0xb]
    // 0x15da048: r0 = 1
    //     0x15da048: movz            x0, #0x1
    // 0x15da04c: StoreField: r1->field_13 = r0
    //     0x15da04c: stur            x0, [x1, #0x13]
    // 0x15da050: r0 = SvgPicture()
    //     0x15da050: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15da054: stur            x0, [fp, #-8]
    // 0x15da058: ldur            x16, [fp, #-0x20]
    // 0x15da05c: str             x16, [SP]
    // 0x15da060: mov             x1, x0
    // 0x15da064: r2 = "assets/images/appbar_arrow.svg"
    //     0x15da064: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15da068: ldr             x2, [x2, #0xa40]
    // 0x15da06c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15da06c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15da070: ldr             x4, [x4, #0xa38]
    // 0x15da074: r0 = SvgPicture.asset()
    //     0x15da074: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15da078: r0 = Align()
    //     0x15da078: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15da07c: mov             x1, x0
    // 0x15da080: r0 = Instance_Alignment
    //     0x15da080: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15da084: ldr             x0, [x0, #0xb10]
    // 0x15da088: StoreField: r1->field_f = r0
    //     0x15da088: stur            w0, [x1, #0xf]
    // 0x15da08c: r0 = 1.000000
    //     0x15da08c: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15da090: StoreField: r1->field_13 = r0
    //     0x15da090: stur            w0, [x1, #0x13]
    // 0x15da094: ArrayStore: r1[0] = r0  ; List_4
    //     0x15da094: stur            w0, [x1, #0x17]
    // 0x15da098: ldur            x0, [fp, #-8]
    // 0x15da09c: StoreField: r1->field_b = r0
    //     0x15da09c: stur            w0, [x1, #0xb]
    // 0x15da0a0: mov             x0, x1
    // 0x15da0a4: stur            x0, [fp, #-8]
    // 0x15da0a8: r0 = InkWell()
    //     0x15da0a8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15da0ac: mov             x3, x0
    // 0x15da0b0: ldur            x0, [fp, #-8]
    // 0x15da0b4: stur            x3, [fp, #-0x20]
    // 0x15da0b8: StoreField: r3->field_b = r0
    //     0x15da0b8: stur            w0, [x3, #0xb]
    // 0x15da0bc: ldur            x2, [fp, #-0x18]
    // 0x15da0c0: r1 = Function '<anonymous closure>':.
    //     0x15da0c0: add             x1, PP, #0x43, lsl #12  ; [pp+0x43ca8] AnonymousClosure: (0x15ce36c), in [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::appBar (0x15e90ec)
    //     0x15da0c4: ldr             x1, [x1, #0xca8]
    // 0x15da0c8: r0 = AllocateClosure()
    //     0x15da0c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15da0cc: ldur            x2, [fp, #-0x20]
    // 0x15da0d0: StoreField: r2->field_f = r0
    //     0x15da0d0: stur            w0, [x2, #0xf]
    // 0x15da0d4: r0 = true
    //     0x15da0d4: add             x0, NULL, #0x20  ; true
    // 0x15da0d8: StoreField: r2->field_43 = r0
    //     0x15da0d8: stur            w0, [x2, #0x43]
    // 0x15da0dc: r1 = Instance_BoxShape
    //     0x15da0dc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15da0e0: ldr             x1, [x1, #0x80]
    // 0x15da0e4: StoreField: r2->field_47 = r1
    //     0x15da0e4: stur            w1, [x2, #0x47]
    // 0x15da0e8: StoreField: r2->field_6f = r0
    //     0x15da0e8: stur            w0, [x2, #0x6f]
    // 0x15da0ec: r1 = false
    //     0x15da0ec: add             x1, NULL, #0x30  ; false
    // 0x15da0f0: StoreField: r2->field_73 = r1
    //     0x15da0f0: stur            w1, [x2, #0x73]
    // 0x15da0f4: StoreField: r2->field_83 = r0
    //     0x15da0f4: stur            w0, [x2, #0x83]
    // 0x15da0f8: StoreField: r2->field_7b = r1
    //     0x15da0f8: stur            w1, [x2, #0x7b]
    // 0x15da0fc: r0 = AppBar()
    //     0x15da0fc: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15da100: stur            x0, [fp, #-8]
    // 0x15da104: ldur            x16, [fp, #-0x10]
    // 0x15da108: str             x16, [SP]
    // 0x15da10c: mov             x1, x0
    // 0x15da110: ldur            x2, [fp, #-0x20]
    // 0x15da114: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15da114: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15da118: ldr             x4, [x4, #0xf00]
    // 0x15da11c: r0 = AppBar()
    //     0x15da11c: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15da120: ldur            x0, [fp, #-8]
    // 0x15da124: LeaveFrame
    //     0x15da124: mov             SP, fp
    //     0x15da128: ldp             fp, lr, [SP], #0x10
    // 0x15da12c: ret
    //     0x15da12c: ret             
    // 0x15da130: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15da130: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15da134: b               #0x15d9f08
  }
}
