// lib: , url: package:customer_app/app/presentation/views/glass/bag/bag_multi_select.dart

// class id: 1049342, size: 0x8
class :: {
}

// class id: 3382, size: 0x18, field offset: 0x14
class _BagMultiSelectState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x93da60, size: 0x3a4
    // 0x93da60: EnterFrame
    //     0x93da60: stp             fp, lr, [SP, #-0x10]!
    //     0x93da64: mov             fp, SP
    // 0x93da68: AllocStack(0x40)
    //     0x93da68: sub             SP, SP, #0x40
    // 0x93da6c: SetupParameters(_BagMultiSelectState this /* r1 => r1, fp-0x8 */)
    //     0x93da6c: stur            x1, [fp, #-8]
    // 0x93da70: CheckStackOverflow
    //     0x93da70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93da74: cmp             SP, x16
    //     0x93da78: b.ls            #0x93dde4
    // 0x93da7c: LoadField: r0 = r1->field_b
    //     0x93da7c: ldur            w0, [x1, #0xb]
    // 0x93da80: DecompressPointer r0
    //     0x93da80: add             x0, x0, HEAP, lsl #32
    // 0x93da84: cmp             w0, NULL
    // 0x93da88: b.eq            #0x93ddec
    // 0x93da8c: LoadField: r2 = r0->field_b
    //     0x93da8c: ldur            w2, [x0, #0xb]
    // 0x93da90: DecompressPointer r2
    //     0x93da90: add             x2, x2, HEAP, lsl #32
    // 0x93da94: cmp             w2, NULL
    // 0x93da98: b.ne            #0x93daa4
    // 0x93da9c: r0 = Null
    //     0x93da9c: mov             x0, NULL
    // 0x93daa0: b               #0x93daac
    // 0x93daa4: LoadField: r0 = r2->field_23
    //     0x93daa4: ldur            w0, [x2, #0x23]
    // 0x93daa8: DecompressPointer r0
    //     0x93daa8: add             x0, x0, HEAP, lsl #32
    // 0x93daac: cmp             w0, NULL
    // 0x93dab0: b.ne            #0x93daf8
    // 0x93dab4: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x93dab4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93dab8: ldr             x0, [x0]
    //     0x93dabc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93dac0: cmp             w0, w16
    //     0x93dac4: b.ne            #0x93dad0
    //     0x93dac8: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x93dacc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x93dad0: r1 = <CustomerResponse>
    //     0x93dad0: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a8] TypeArguments: <CustomerResponse>
    //     0x93dad4: ldr             x1, [x1, #0x5a8]
    // 0x93dad8: stur            x0, [fp, #-0x10]
    // 0x93dadc: r0 = AllocateGrowableArray()
    //     0x93dadc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x93dae0: mov             x1, x0
    // 0x93dae4: ldur            x0, [fp, #-0x10]
    // 0x93dae8: StoreField: r1->field_f = r0
    //     0x93dae8: stur            w0, [x1, #0xf]
    // 0x93daec: StoreField: r1->field_b = rZR
    //     0x93daec: stur            wzr, [x1, #0xb]
    // 0x93daf0: mov             x3, x1
    // 0x93daf4: b               #0x93dafc
    // 0x93daf8: mov             x3, x0
    // 0x93dafc: stur            x3, [fp, #-0x30]
    // 0x93db00: LoadField: r4 = r3->field_7
    //     0x93db00: ldur            w4, [x3, #7]
    // 0x93db04: DecompressPointer r4
    //     0x93db04: add             x4, x4, HEAP, lsl #32
    // 0x93db08: stur            x4, [fp, #-0x28]
    // 0x93db0c: LoadField: r0 = r3->field_b
    //     0x93db0c: ldur            w0, [x3, #0xb]
    // 0x93db10: r5 = LoadInt32Instr(r0)
    //     0x93db10: sbfx            x5, x0, #1, #0x1f
    // 0x93db14: stur            x5, [fp, #-0x20]
    // 0x93db18: r0 = 0
    //     0x93db18: movz            x0, #0
    // 0x93db1c: ldur            x6, [fp, #-8]
    // 0x93db20: CheckStackOverflow
    //     0x93db20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93db24: cmp             SP, x16
    //     0x93db28: b.ls            #0x93ddf0
    // 0x93db2c: LoadField: r1 = r3->field_b
    //     0x93db2c: ldur            w1, [x3, #0xb]
    // 0x93db30: r2 = LoadInt32Instr(r1)
    //     0x93db30: sbfx            x2, x1, #1, #0x1f
    // 0x93db34: cmp             x5, x2
    // 0x93db38: b.ne            #0x93ddc4
    // 0x93db3c: cmp             x0, x2
    // 0x93db40: b.ge            #0x93dc50
    // 0x93db44: LoadField: r1 = r3->field_f
    //     0x93db44: ldur            w1, [x3, #0xf]
    // 0x93db48: DecompressPointer r1
    //     0x93db48: add             x1, x1, HEAP, lsl #32
    // 0x93db4c: ArrayLoad: r7 = r1[r0]  ; Unknown_4
    //     0x93db4c: add             x16, x1, x0, lsl #2
    //     0x93db50: ldur            w7, [x16, #0xf]
    // 0x93db54: DecompressPointer r7
    //     0x93db54: add             x7, x7, HEAP, lsl #32
    // 0x93db58: stur            x7, [fp, #-0x10]
    // 0x93db5c: add             x8, x0, #1
    // 0x93db60: stur            x8, [fp, #-0x18]
    // 0x93db64: cmp             w7, NULL
    // 0x93db68: b.ne            #0x93db9c
    // 0x93db6c: mov             x0, x7
    // 0x93db70: mov             x2, x4
    // 0x93db74: r1 = Null
    //     0x93db74: mov             x1, NULL
    // 0x93db78: cmp             w2, NULL
    // 0x93db7c: b.eq            #0x93db9c
    // 0x93db80: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93db80: ldur            w4, [x2, #0x17]
    // 0x93db84: DecompressPointer r4
    //     0x93db84: add             x4, x4, HEAP, lsl #32
    // 0x93db88: r8 = X0
    //     0x93db88: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x93db8c: LoadField: r9 = r4->field_7
    //     0x93db8c: ldur            x9, [x4, #7]
    // 0x93db90: r3 = Null
    //     0x93db90: add             x3, PP, #0x6a, lsl #12  ; [pp+0x6ab70] Null
    //     0x93db94: ldr             x3, [x3, #0xb70]
    // 0x93db98: blr             x9
    // 0x93db9c: ldur            x0, [fp, #-8]
    // 0x93dba0: ldur            x1, [fp, #-0x10]
    // 0x93dba4: LoadField: r2 = r0->field_13
    //     0x93dba4: ldur            w2, [x0, #0x13]
    // 0x93dba8: DecompressPointer r2
    //     0x93dba8: add             x2, x2, HEAP, lsl #32
    // 0x93dbac: stur            x2, [fp, #-0x40]
    // 0x93dbb0: LoadField: r3 = r1->field_b
    //     0x93dbb0: ldur            w3, [x1, #0xb]
    // 0x93dbb4: DecompressPointer r3
    //     0x93dbb4: add             x3, x3, HEAP, lsl #32
    // 0x93dbb8: cmp             w3, NULL
    // 0x93dbbc: b.ne            #0x93dbc4
    // 0x93dbc0: r3 = ""
    //     0x93dbc0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93dbc4: stur            x3, [fp, #-0x10]
    // 0x93dbc8: LoadField: r1 = r2->field_b
    //     0x93dbc8: ldur            w1, [x2, #0xb]
    // 0x93dbcc: LoadField: r4 = r2->field_f
    //     0x93dbcc: ldur            w4, [x2, #0xf]
    // 0x93dbd0: DecompressPointer r4
    //     0x93dbd0: add             x4, x4, HEAP, lsl #32
    // 0x93dbd4: LoadField: r5 = r4->field_b
    //     0x93dbd4: ldur            w5, [x4, #0xb]
    // 0x93dbd8: r4 = LoadInt32Instr(r1)
    //     0x93dbd8: sbfx            x4, x1, #1, #0x1f
    // 0x93dbdc: stur            x4, [fp, #-0x38]
    // 0x93dbe0: r1 = LoadInt32Instr(r5)
    //     0x93dbe0: sbfx            x1, x5, #1, #0x1f
    // 0x93dbe4: cmp             x4, x1
    // 0x93dbe8: b.ne            #0x93dbf4
    // 0x93dbec: mov             x1, x2
    // 0x93dbf0: r0 = _growToNextCapacity()
    //     0x93dbf0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93dbf4: ldur            x0, [fp, #-0x40]
    // 0x93dbf8: ldur            x2, [fp, #-0x38]
    // 0x93dbfc: add             x1, x2, #1
    // 0x93dc00: lsl             x3, x1, #1
    // 0x93dc04: StoreField: r0->field_b = r3
    //     0x93dc04: stur            w3, [x0, #0xb]
    // 0x93dc08: LoadField: r1 = r0->field_f
    //     0x93dc08: ldur            w1, [x0, #0xf]
    // 0x93dc0c: DecompressPointer r1
    //     0x93dc0c: add             x1, x1, HEAP, lsl #32
    // 0x93dc10: ldur            x0, [fp, #-0x10]
    // 0x93dc14: ArrayStore: r1[r2] = r0  ; List_4
    //     0x93dc14: add             x25, x1, x2, lsl #2
    //     0x93dc18: add             x25, x25, #0xf
    //     0x93dc1c: str             w0, [x25]
    //     0x93dc20: tbz             w0, #0, #0x93dc3c
    //     0x93dc24: ldurb           w16, [x1, #-1]
    //     0x93dc28: ldurb           w17, [x0, #-1]
    //     0x93dc2c: and             x16, x17, x16, lsr #2
    //     0x93dc30: tst             x16, HEAP, lsr #32
    //     0x93dc34: b.eq            #0x93dc3c
    //     0x93dc38: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93dc3c: ldur            x0, [fp, #-0x18]
    // 0x93dc40: ldur            x3, [fp, #-0x30]
    // 0x93dc44: ldur            x4, [fp, #-0x28]
    // 0x93dc48: ldur            x5, [fp, #-0x20]
    // 0x93dc4c: b               #0x93db1c
    // 0x93dc50: mov             x0, x6
    // 0x93dc54: LoadField: r1 = r0->field_b
    //     0x93dc54: ldur            w1, [x0, #0xb]
    // 0x93dc58: DecompressPointer r1
    //     0x93dc58: add             x1, x1, HEAP, lsl #32
    // 0x93dc5c: cmp             w1, NULL
    // 0x93dc60: b.eq            #0x93ddf8
    // 0x93dc64: LoadField: r2 = r1->field_f
    //     0x93dc64: ldur            w2, [x1, #0xf]
    // 0x93dc68: DecompressPointer r2
    //     0x93dc68: add             x2, x2, HEAP, lsl #32
    // 0x93dc6c: cmp             w2, NULL
    // 0x93dc70: b.ne            #0x93dc7c
    // 0x93dc74: r1 = Null
    //     0x93dc74: mov             x1, NULL
    // 0x93dc78: b               #0x93dc84
    // 0x93dc7c: LoadField: r1 = r2->field_23
    //     0x93dc7c: ldur            w1, [x2, #0x23]
    // 0x93dc80: DecompressPointer r1
    //     0x93dc80: add             x1, x1, HEAP, lsl #32
    // 0x93dc84: cmp             w1, NULL
    // 0x93dc88: b.ne            #0x93dca0
    // 0x93dc8c: r1 = <CustomerResponse>
    //     0x93dc8c: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a8] TypeArguments: <CustomerResponse>
    //     0x93dc90: ldr             x1, [x1, #0x5a8]
    // 0x93dc94: r2 = 0
    //     0x93dc94: movz            x2, #0
    // 0x93dc98: r0 = AllocateArray()
    //     0x93dc98: bl              #0x16f7198  ; AllocateArrayStub
    // 0x93dc9c: mov             x1, x0
    // 0x93dca0: r0 = LoadClassIdInstr(r1)
    //     0x93dca0: ldur            x0, [x1, #-1]
    //     0x93dca4: ubfx            x0, x0, #0xc, #0x14
    // 0x93dca8: r0 = GDT[cid_x0 + 0xc907]()
    //     0x93dca8: movz            x17, #0xc907
    //     0x93dcac: add             lr, x0, x17
    //     0x93dcb0: ldr             lr, [x21, lr, lsl #3]
    //     0x93dcb4: blr             lr
    // 0x93dcb8: mov             x2, x0
    // 0x93dcbc: stur            x2, [fp, #-0x10]
    // 0x93dcc0: ldur            x3, [fp, #-8]
    // 0x93dcc4: CheckStackOverflow
    //     0x93dcc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93dcc8: cmp             SP, x16
    //     0x93dccc: b.ls            #0x93ddfc
    // 0x93dcd0: r0 = LoadClassIdInstr(r2)
    //     0x93dcd0: ldur            x0, [x2, #-1]
    //     0x93dcd4: ubfx            x0, x0, #0xc, #0x14
    // 0x93dcd8: mov             x1, x2
    // 0x93dcdc: r0 = GDT[cid_x0 + 0x5ea]()
    //     0x93dcdc: add             lr, x0, #0x5ea
    //     0x93dce0: ldr             lr, [x21, lr, lsl #3]
    //     0x93dce4: blr             lr
    // 0x93dce8: tbnz            w0, #4, #0x93ddb4
    // 0x93dcec: ldur            x3, [fp, #-8]
    // 0x93dcf0: ldur            x2, [fp, #-0x10]
    // 0x93dcf4: r0 = LoadClassIdInstr(r2)
    //     0x93dcf4: ldur            x0, [x2, #-1]
    //     0x93dcf8: ubfx            x0, x0, #0xc, #0x14
    // 0x93dcfc: mov             x1, x2
    // 0x93dd00: r0 = GDT[cid_x0 + 0x655]()
    //     0x93dd00: add             lr, x0, #0x655
    //     0x93dd04: ldr             lr, [x21, lr, lsl #3]
    //     0x93dd08: blr             lr
    // 0x93dd0c: mov             x1, x0
    // 0x93dd10: ldur            x0, [fp, #-8]
    // 0x93dd14: LoadField: r2 = r0->field_13
    //     0x93dd14: ldur            w2, [x0, #0x13]
    // 0x93dd18: DecompressPointer r2
    //     0x93dd18: add             x2, x2, HEAP, lsl #32
    // 0x93dd1c: stur            x2, [fp, #-0x40]
    // 0x93dd20: LoadField: r3 = r1->field_b
    //     0x93dd20: ldur            w3, [x1, #0xb]
    // 0x93dd24: DecompressPointer r3
    //     0x93dd24: add             x3, x3, HEAP, lsl #32
    // 0x93dd28: cmp             w3, NULL
    // 0x93dd2c: b.ne            #0x93dd34
    // 0x93dd30: r3 = ""
    //     0x93dd30: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93dd34: stur            x3, [fp, #-0x28]
    // 0x93dd38: LoadField: r1 = r2->field_b
    //     0x93dd38: ldur            w1, [x2, #0xb]
    // 0x93dd3c: LoadField: r4 = r2->field_f
    //     0x93dd3c: ldur            w4, [x2, #0xf]
    // 0x93dd40: DecompressPointer r4
    //     0x93dd40: add             x4, x4, HEAP, lsl #32
    // 0x93dd44: LoadField: r5 = r4->field_b
    //     0x93dd44: ldur            w5, [x4, #0xb]
    // 0x93dd48: r4 = LoadInt32Instr(r1)
    //     0x93dd48: sbfx            x4, x1, #1, #0x1f
    // 0x93dd4c: stur            x4, [fp, #-0x18]
    // 0x93dd50: r1 = LoadInt32Instr(r5)
    //     0x93dd50: sbfx            x1, x5, #1, #0x1f
    // 0x93dd54: cmp             x4, x1
    // 0x93dd58: b.ne            #0x93dd64
    // 0x93dd5c: mov             x1, x2
    // 0x93dd60: r0 = _growToNextCapacity()
    //     0x93dd60: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93dd64: ldur            x0, [fp, #-0x40]
    // 0x93dd68: ldur            x2, [fp, #-0x18]
    // 0x93dd6c: add             x1, x2, #1
    // 0x93dd70: lsl             x3, x1, #1
    // 0x93dd74: StoreField: r0->field_b = r3
    //     0x93dd74: stur            w3, [x0, #0xb]
    // 0x93dd78: LoadField: r1 = r0->field_f
    //     0x93dd78: ldur            w1, [x0, #0xf]
    // 0x93dd7c: DecompressPointer r1
    //     0x93dd7c: add             x1, x1, HEAP, lsl #32
    // 0x93dd80: ldur            x0, [fp, #-0x28]
    // 0x93dd84: ArrayStore: r1[r2] = r0  ; List_4
    //     0x93dd84: add             x25, x1, x2, lsl #2
    //     0x93dd88: add             x25, x25, #0xf
    //     0x93dd8c: str             w0, [x25]
    //     0x93dd90: tbz             w0, #0, #0x93ddac
    //     0x93dd94: ldurb           w16, [x1, #-1]
    //     0x93dd98: ldurb           w17, [x0, #-1]
    //     0x93dd9c: and             x16, x17, x16, lsr #2
    //     0x93dda0: tst             x16, HEAP, lsr #32
    //     0x93dda4: b.eq            #0x93ddac
    //     0x93dda8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93ddac: ldur            x2, [fp, #-0x10]
    // 0x93ddb0: b               #0x93dcc0
    // 0x93ddb4: r0 = Null
    //     0x93ddb4: mov             x0, NULL
    // 0x93ddb8: LeaveFrame
    //     0x93ddb8: mov             SP, fp
    //     0x93ddbc: ldp             fp, lr, [SP], #0x10
    // 0x93ddc0: ret
    //     0x93ddc0: ret             
    // 0x93ddc4: mov             x0, x3
    // 0x93ddc8: r0 = ConcurrentModificationError()
    //     0x93ddc8: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x93ddcc: mov             x1, x0
    // 0x93ddd0: ldur            x0, [fp, #-0x30]
    // 0x93ddd4: StoreField: r1->field_b = r0
    //     0x93ddd4: stur            w0, [x1, #0xb]
    // 0x93ddd8: mov             x0, x1
    // 0x93dddc: r0 = Throw()
    //     0x93dddc: bl              #0x16f5420  ; ThrowStub
    // 0x93dde0: brk             #0
    // 0x93dde4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93dde4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93dde8: b               #0x93da7c
    // 0x93ddec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93ddec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93ddf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93ddf0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93ddf4: b               #0x93db2c
    // 0x93ddf8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93ddf8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93ddfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93ddfc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93de00: b               #0x93dcd0
  }
  _ build(/* No info */) {
    // ** addr: 0xb2ee3c, size: 0x8e4
    // 0xb2ee3c: EnterFrame
    //     0xb2ee3c: stp             fp, lr, [SP, #-0x10]!
    //     0xb2ee40: mov             fp, SP
    // 0xb2ee44: AllocStack(0x50)
    //     0xb2ee44: sub             SP, SP, #0x50
    // 0xb2ee48: SetupParameters(_BagMultiSelectState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb2ee48: mov             x0, x1
    //     0xb2ee4c: stur            x1, [fp, #-8]
    //     0xb2ee50: mov             x1, x2
    //     0xb2ee54: stur            x2, [fp, #-0x10]
    // 0xb2ee58: CheckStackOverflow
    //     0xb2ee58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2ee5c: cmp             SP, x16
    //     0xb2ee60: b.ls            #0xb2f704
    // 0xb2ee64: LoadField: r2 = r0->field_b
    //     0xb2ee64: ldur            w2, [x0, #0xb]
    // 0xb2ee68: DecompressPointer r2
    //     0xb2ee68: add             x2, x2, HEAP, lsl #32
    // 0xb2ee6c: cmp             w2, NULL
    // 0xb2ee70: b.eq            #0xb2f70c
    // 0xb2ee74: LoadField: r3 = r2->field_b
    //     0xb2ee74: ldur            w3, [x2, #0xb]
    // 0xb2ee78: DecompressPointer r3
    //     0xb2ee78: add             x3, x3, HEAP, lsl #32
    // 0xb2ee7c: cmp             w3, NULL
    // 0xb2ee80: b.ne            #0xb2ee8c
    // 0xb2ee84: r2 = Null
    //     0xb2ee84: mov             x2, NULL
    // 0xb2ee88: b               #0xb2eeb8
    // 0xb2ee8c: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xb2ee8c: ldur            w2, [x3, #0x17]
    // 0xb2ee90: DecompressPointer r2
    //     0xb2ee90: add             x2, x2, HEAP, lsl #32
    // 0xb2ee94: cmp             w2, NULL
    // 0xb2ee98: b.ne            #0xb2eea4
    // 0xb2ee9c: r2 = Null
    //     0xb2ee9c: mov             x2, NULL
    // 0xb2eea0: b               #0xb2eeb8
    // 0xb2eea4: LoadField: r3 = r2->field_7
    //     0xb2eea4: ldur            w3, [x2, #7]
    // 0xb2eea8: cbnz            w3, #0xb2eeb4
    // 0xb2eeac: r2 = false
    //     0xb2eeac: add             x2, NULL, #0x30  ; false
    // 0xb2eeb0: b               #0xb2eeb8
    // 0xb2eeb4: r2 = true
    //     0xb2eeb4: add             x2, NULL, #0x20  ; true
    // 0xb2eeb8: cmp             w2, NULL
    // 0xb2eebc: b.ne            #0xb2ef08
    // 0xb2eec0: mov             x3, x0
    // 0xb2eec4: r2 = 4
    //     0xb2eec4: movz            x2, #0x4
    // 0xb2eec8: r4 = 6
    //     0xb2eec8: movz            x4, #0x6
    // 0xb2eecc: r7 = Instance_CrossAxisAlignment
    //     0xb2eecc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2eed0: ldr             x7, [x7, #0xa18]
    // 0xb2eed4: r5 = Instance_MainAxisAlignment
    //     0xb2eed4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2eed8: ldr             x5, [x5, #0xa08]
    // 0xb2eedc: r6 = Instance_MainAxisSize
    //     0xb2eedc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2eee0: ldr             x6, [x6, #0xa10]
    // 0xb2eee4: r1 = Instance_Axis
    //     0xb2eee4: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2eee8: r8 = Instance_VerticalDirection
    //     0xb2eee8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2eeec: ldr             x8, [x8, #0xa20]
    // 0xb2eef0: r0 = Instance__DeferringMouseCursor
    //     0xb2eef0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2eef4: r9 = Instance_Clip
    //     0xb2eef4: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2eef8: ldr             x9, [x9, #0x38]
    // 0xb2eefc: d1 = 0.500000
    //     0xb2eefc: fmov            d1, #0.50000000
    // 0xb2ef00: d0 = inf
    //     0xb2ef00: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb2ef04: b               #0xb2f328
    // 0xb2ef08: tbnz            w2, #4, #0xb2f2e4
    // 0xb2ef0c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb2ef0c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb2ef10: ldr             x0, [x0, #0x1c80]
    //     0xb2ef14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb2ef18: cmp             w0, w16
    //     0xb2ef1c: b.ne            #0xb2ef28
    //     0xb2ef20: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb2ef24: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb2ef28: r0 = GetNavigation.size()
    //     0xb2ef28: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb2ef2c: LoadField: d0 = r0->field_7
    //     0xb2ef2c: ldur            d0, [x0, #7]
    // 0xb2ef30: d1 = 0.500000
    //     0xb2ef30: fmov            d1, #0.50000000
    // 0xb2ef34: fmul            d2, d0, d1
    // 0xb2ef38: stur            d2, [fp, #-0x40]
    // 0xb2ef3c: r0 = BoxConstraints()
    //     0xb2ef3c: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb2ef40: stur            x0, [fp, #-0x20]
    // 0xb2ef44: StoreField: r0->field_7 = rZR
    //     0xb2ef44: stur            xzr, [x0, #7]
    // 0xb2ef48: ldur            d0, [fp, #-0x40]
    // 0xb2ef4c: StoreField: r0->field_f = d0
    //     0xb2ef4c: stur            d0, [x0, #0xf]
    // 0xb2ef50: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb2ef50: stur            xzr, [x0, #0x17]
    // 0xb2ef54: d0 = inf
    //     0xb2ef54: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb2ef58: StoreField: r0->field_1f = d0
    //     0xb2ef58: stur            d0, [x0, #0x1f]
    // 0xb2ef5c: ldur            x2, [fp, #-8]
    // 0xb2ef60: LoadField: r1 = r2->field_b
    //     0xb2ef60: ldur            w1, [x2, #0xb]
    // 0xb2ef64: DecompressPointer r1
    //     0xb2ef64: add             x1, x1, HEAP, lsl #32
    // 0xb2ef68: cmp             w1, NULL
    // 0xb2ef6c: b.eq            #0xb2f710
    // 0xb2ef70: LoadField: r3 = r1->field_b
    //     0xb2ef70: ldur            w3, [x1, #0xb]
    // 0xb2ef74: DecompressPointer r3
    //     0xb2ef74: add             x3, x3, HEAP, lsl #32
    // 0xb2ef78: cmp             w3, NULL
    // 0xb2ef7c: b.ne            #0xb2ef88
    // 0xb2ef80: r1 = Null
    //     0xb2ef80: mov             x1, NULL
    // 0xb2ef84: b               #0xb2ef90
    // 0xb2ef88: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb2ef88: ldur            w1, [x3, #0x17]
    // 0xb2ef8c: DecompressPointer r1
    //     0xb2ef8c: add             x1, x1, HEAP, lsl #32
    // 0xb2ef90: cmp             w1, NULL
    // 0xb2ef94: b.ne            #0xb2efa0
    // 0xb2ef98: r3 = ""
    //     0xb2ef98: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2ef9c: b               #0xb2efa4
    // 0xb2efa0: mov             x3, x1
    // 0xb2efa4: ldur            x1, [fp, #-0x10]
    // 0xb2efa8: stur            x3, [fp, #-0x18]
    // 0xb2efac: r0 = of()
    //     0xb2efac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2efb0: LoadField: r1 = r0->field_87
    //     0xb2efb0: ldur            w1, [x0, #0x87]
    // 0xb2efb4: DecompressPointer r1
    //     0xb2efb4: add             x1, x1, HEAP, lsl #32
    // 0xb2efb8: LoadField: r0 = r1->field_7
    //     0xb2efb8: ldur            w0, [x1, #7]
    // 0xb2efbc: DecompressPointer r0
    //     0xb2efbc: add             x0, x0, HEAP, lsl #32
    // 0xb2efc0: r16 = 14.000000
    //     0xb2efc0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2efc4: ldr             x16, [x16, #0x1d8]
    // 0xb2efc8: r30 = Instance_Color
    //     0xb2efc8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2efcc: stp             lr, x16, [SP]
    // 0xb2efd0: mov             x1, x0
    // 0xb2efd4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2efd4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2efd8: ldr             x4, [x4, #0xaa0]
    // 0xb2efdc: r0 = copyWith()
    //     0xb2efdc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2efe0: stur            x0, [fp, #-0x28]
    // 0xb2efe4: r0 = TextSpan()
    //     0xb2efe4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2efe8: mov             x3, x0
    // 0xb2efec: ldur            x0, [fp, #-0x18]
    // 0xb2eff0: stur            x3, [fp, #-0x30]
    // 0xb2eff4: StoreField: r3->field_b = r0
    //     0xb2eff4: stur            w0, [x3, #0xb]
    // 0xb2eff8: r0 = Instance__DeferringMouseCursor
    //     0xb2eff8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2effc: ArrayStore: r3[0] = r0  ; List_4
    //     0xb2effc: stur            w0, [x3, #0x17]
    // 0xb2f000: ldur            x1, [fp, #-0x28]
    // 0xb2f004: StoreField: r3->field_7 = r1
    //     0xb2f004: stur            w1, [x3, #7]
    // 0xb2f008: r1 = Null
    //     0xb2f008: mov             x1, NULL
    // 0xb2f00c: r2 = 4
    //     0xb2f00c: movz            x2, #0x4
    // 0xb2f010: r0 = AllocateArray()
    //     0xb2f010: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2f014: stur            x0, [fp, #-0x18]
    // 0xb2f018: r16 = " : "
    //     0xb2f018: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xb2f01c: ldr             x16, [x16, #0x680]
    // 0xb2f020: StoreField: r0->field_f = r16
    //     0xb2f020: stur            w16, [x0, #0xf]
    // 0xb2f024: ldur            x2, [fp, #-8]
    // 0xb2f028: LoadField: r1 = r2->field_13
    //     0xb2f028: ldur            w1, [x2, #0x13]
    // 0xb2f02c: DecompressPointer r1
    //     0xb2f02c: add             x1, x1, HEAP, lsl #32
    // 0xb2f030: r16 = ", "
    //     0xb2f030: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0xb2f034: str             x16, [SP]
    // 0xb2f038: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb2f038: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb2f03c: r0 = join()
    //     0xb2f03c: bl              #0x7d6d4c  ; [dart:core] _GrowableList::join
    // 0xb2f040: ldur            x1, [fp, #-0x18]
    // 0xb2f044: ArrayStore: r1[1] = r0  ; List_4
    //     0xb2f044: add             x25, x1, #0x13
    //     0xb2f048: str             w0, [x25]
    //     0xb2f04c: tbz             w0, #0, #0xb2f068
    //     0xb2f050: ldurb           w16, [x1, #-1]
    //     0xb2f054: ldurb           w17, [x0, #-1]
    //     0xb2f058: and             x16, x17, x16, lsr #2
    //     0xb2f05c: tst             x16, HEAP, lsr #32
    //     0xb2f060: b.eq            #0xb2f068
    //     0xb2f064: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb2f068: ldur            x16, [fp, #-0x18]
    // 0xb2f06c: str             x16, [SP]
    // 0xb2f070: r0 = _interpolate()
    //     0xb2f070: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb2f074: ldur            x1, [fp, #-0x10]
    // 0xb2f078: stur            x0, [fp, #-0x18]
    // 0xb2f07c: r0 = of()
    //     0xb2f07c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2f080: LoadField: r1 = r0->field_87
    //     0xb2f080: ldur            w1, [x0, #0x87]
    // 0xb2f084: DecompressPointer r1
    //     0xb2f084: add             x1, x1, HEAP, lsl #32
    // 0xb2f088: LoadField: r0 = r1->field_2b
    //     0xb2f088: ldur            w0, [x1, #0x2b]
    // 0xb2f08c: DecompressPointer r0
    //     0xb2f08c: add             x0, x0, HEAP, lsl #32
    // 0xb2f090: r16 = 14.000000
    //     0xb2f090: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2f094: ldr             x16, [x16, #0x1d8]
    // 0xb2f098: r30 = Instance_Color
    //     0xb2f098: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2f09c: stp             lr, x16, [SP]
    // 0xb2f0a0: mov             x1, x0
    // 0xb2f0a4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2f0a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2f0a8: ldr             x4, [x4, #0xaa0]
    // 0xb2f0ac: r0 = copyWith()
    //     0xb2f0ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2f0b0: stur            x0, [fp, #-0x28]
    // 0xb2f0b4: r0 = TextSpan()
    //     0xb2f0b4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2f0b8: mov             x3, x0
    // 0xb2f0bc: ldur            x0, [fp, #-0x18]
    // 0xb2f0c0: stur            x3, [fp, #-0x38]
    // 0xb2f0c4: StoreField: r3->field_b = r0
    //     0xb2f0c4: stur            w0, [x3, #0xb]
    // 0xb2f0c8: r0 = Instance__DeferringMouseCursor
    //     0xb2f0c8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2f0cc: ArrayStore: r3[0] = r0  ; List_4
    //     0xb2f0cc: stur            w0, [x3, #0x17]
    // 0xb2f0d0: ldur            x1, [fp, #-0x28]
    // 0xb2f0d4: StoreField: r3->field_7 = r1
    //     0xb2f0d4: stur            w1, [x3, #7]
    // 0xb2f0d8: r1 = Null
    //     0xb2f0d8: mov             x1, NULL
    // 0xb2f0dc: r2 = 4
    //     0xb2f0dc: movz            x2, #0x4
    // 0xb2f0e0: r0 = AllocateArray()
    //     0xb2f0e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2f0e4: mov             x2, x0
    // 0xb2f0e8: ldur            x0, [fp, #-0x30]
    // 0xb2f0ec: stur            x2, [fp, #-0x18]
    // 0xb2f0f0: StoreField: r2->field_f = r0
    //     0xb2f0f0: stur            w0, [x2, #0xf]
    // 0xb2f0f4: ldur            x0, [fp, #-0x38]
    // 0xb2f0f8: StoreField: r2->field_13 = r0
    //     0xb2f0f8: stur            w0, [x2, #0x13]
    // 0xb2f0fc: r1 = <InlineSpan>
    //     0xb2f0fc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb2f100: ldr             x1, [x1, #0xe40]
    // 0xb2f104: r0 = AllocateGrowableArray()
    //     0xb2f104: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2f108: mov             x1, x0
    // 0xb2f10c: ldur            x0, [fp, #-0x18]
    // 0xb2f110: stur            x1, [fp, #-0x28]
    // 0xb2f114: StoreField: r1->field_f = r0
    //     0xb2f114: stur            w0, [x1, #0xf]
    // 0xb2f118: r2 = 4
    //     0xb2f118: movz            x2, #0x4
    // 0xb2f11c: StoreField: r1->field_b = r2
    //     0xb2f11c: stur            w2, [x1, #0xb]
    // 0xb2f120: r0 = TextSpan()
    //     0xb2f120: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2f124: mov             x1, x0
    // 0xb2f128: ldur            x0, [fp, #-0x28]
    // 0xb2f12c: stur            x1, [fp, #-0x18]
    // 0xb2f130: StoreField: r1->field_f = r0
    //     0xb2f130: stur            w0, [x1, #0xf]
    // 0xb2f134: r0 = Instance__DeferringMouseCursor
    //     0xb2f134: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2f138: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2f138: stur            w0, [x1, #0x17]
    // 0xb2f13c: r0 = RichText()
    //     0xb2f13c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb2f140: mov             x1, x0
    // 0xb2f144: ldur            x2, [fp, #-0x18]
    // 0xb2f148: stur            x0, [fp, #-0x18]
    // 0xb2f14c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb2f14c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb2f150: r0 = RichText()
    //     0xb2f150: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb2f154: r0 = ConstrainedBox()
    //     0xb2f154: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xb2f158: mov             x1, x0
    // 0xb2f15c: ldur            x0, [fp, #-0x20]
    // 0xb2f160: stur            x1, [fp, #-0x28]
    // 0xb2f164: StoreField: r1->field_f = r0
    //     0xb2f164: stur            w0, [x1, #0xf]
    // 0xb2f168: ldur            x0, [fp, #-0x18]
    // 0xb2f16c: StoreField: r1->field_b = r0
    //     0xb2f16c: stur            w0, [x1, #0xb]
    // 0xb2f170: ldur            x3, [fp, #-8]
    // 0xb2f174: LoadField: r0 = r3->field_b
    //     0xb2f174: ldur            w0, [x3, #0xb]
    // 0xb2f178: DecompressPointer r0
    //     0xb2f178: add             x0, x0, HEAP, lsl #32
    // 0xb2f17c: cmp             w0, NULL
    // 0xb2f180: b.eq            #0xb2f714
    // 0xb2f184: LoadField: r2 = r0->field_b
    //     0xb2f184: ldur            w2, [x0, #0xb]
    // 0xb2f188: DecompressPointer r2
    //     0xb2f188: add             x2, x2, HEAP, lsl #32
    // 0xb2f18c: cmp             w2, NULL
    // 0xb2f190: b.ne            #0xb2f19c
    // 0xb2f194: r0 = Null
    //     0xb2f194: mov             x0, NULL
    // 0xb2f198: b               #0xb2f1c8
    // 0xb2f19c: LoadField: r0 = r2->field_2b
    //     0xb2f19c: ldur            w0, [x2, #0x2b]
    // 0xb2f1a0: DecompressPointer r0
    //     0xb2f1a0: add             x0, x0, HEAP, lsl #32
    // 0xb2f1a4: r2 = LoadClassIdInstr(r0)
    //     0xb2f1a4: ldur            x2, [x0, #-1]
    //     0xb2f1a8: ubfx            x2, x2, #0xc, #0x14
    // 0xb2f1ac: str             x0, [SP]
    // 0xb2f1b0: mov             x0, x2
    // 0xb2f1b4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb2f1b4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb2f1b8: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb2f1b8: movz            x17, #0x2700
    //     0xb2f1bc: add             lr, x0, x17
    //     0xb2f1c0: ldr             lr, [x21, lr, lsl #3]
    //     0xb2f1c4: blr             lr
    // 0xb2f1c8: cmp             w0, NULL
    // 0xb2f1cc: b.ne            #0xb2f1d8
    // 0xb2f1d0: r2 = " "
    //     0xb2f1d0: ldr             x2, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb2f1d4: b               #0xb2f1dc
    // 0xb2f1d8: mov             x2, x0
    // 0xb2f1dc: ldur            x0, [fp, #-0x28]
    // 0xb2f1e0: ldur            x1, [fp, #-0x10]
    // 0xb2f1e4: stur            x2, [fp, #-0x18]
    // 0xb2f1e8: r0 = of()
    //     0xb2f1e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2f1ec: LoadField: r1 = r0->field_87
    //     0xb2f1ec: ldur            w1, [x0, #0x87]
    // 0xb2f1f0: DecompressPointer r1
    //     0xb2f1f0: add             x1, x1, HEAP, lsl #32
    // 0xb2f1f4: LoadField: r0 = r1->field_2b
    //     0xb2f1f4: ldur            w0, [x1, #0x2b]
    // 0xb2f1f8: DecompressPointer r0
    //     0xb2f1f8: add             x0, x0, HEAP, lsl #32
    // 0xb2f1fc: r16 = 14.000000
    //     0xb2f1fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2f200: ldr             x16, [x16, #0x1d8]
    // 0xb2f204: r30 = Instance_Color
    //     0xb2f204: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2f208: stp             lr, x16, [SP]
    // 0xb2f20c: mov             x1, x0
    // 0xb2f210: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2f210: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2f214: ldr             x4, [x4, #0xaa0]
    // 0xb2f218: r0 = copyWith()
    //     0xb2f218: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2f21c: stur            x0, [fp, #-0x20]
    // 0xb2f220: r0 = Text()
    //     0xb2f220: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2f224: mov             x3, x0
    // 0xb2f228: ldur            x0, [fp, #-0x18]
    // 0xb2f22c: stur            x3, [fp, #-0x30]
    // 0xb2f230: StoreField: r3->field_b = r0
    //     0xb2f230: stur            w0, [x3, #0xb]
    // 0xb2f234: ldur            x0, [fp, #-0x20]
    // 0xb2f238: StoreField: r3->field_13 = r0
    //     0xb2f238: stur            w0, [x3, #0x13]
    // 0xb2f23c: r1 = Null
    //     0xb2f23c: mov             x1, NULL
    // 0xb2f240: r2 = 6
    //     0xb2f240: movz            x2, #0x6
    // 0xb2f244: r0 = AllocateArray()
    //     0xb2f244: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2f248: mov             x2, x0
    // 0xb2f24c: ldur            x0, [fp, #-0x28]
    // 0xb2f250: stur            x2, [fp, #-0x18]
    // 0xb2f254: StoreField: r2->field_f = r0
    //     0xb2f254: stur            w0, [x2, #0xf]
    // 0xb2f258: r16 = Instance_Spacer
    //     0xb2f258: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb2f25c: ldr             x16, [x16, #0xf0]
    // 0xb2f260: StoreField: r2->field_13 = r16
    //     0xb2f260: stur            w16, [x2, #0x13]
    // 0xb2f264: ldur            x0, [fp, #-0x30]
    // 0xb2f268: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2f268: stur            w0, [x2, #0x17]
    // 0xb2f26c: r1 = <Widget>
    //     0xb2f26c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2f270: r0 = AllocateGrowableArray()
    //     0xb2f270: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2f274: mov             x1, x0
    // 0xb2f278: ldur            x0, [fp, #-0x18]
    // 0xb2f27c: stur            x1, [fp, #-0x20]
    // 0xb2f280: StoreField: r1->field_f = r0
    //     0xb2f280: stur            w0, [x1, #0xf]
    // 0xb2f284: r4 = 6
    //     0xb2f284: movz            x4, #0x6
    // 0xb2f288: StoreField: r1->field_b = r4
    //     0xb2f288: stur            w4, [x1, #0xb]
    // 0xb2f28c: r0 = Row()
    //     0xb2f28c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2f290: r1 = Instance_Axis
    //     0xb2f290: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2f294: StoreField: r0->field_f = r1
    //     0xb2f294: stur            w1, [x0, #0xf]
    // 0xb2f298: r5 = Instance_MainAxisAlignment
    //     0xb2f298: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2f29c: ldr             x5, [x5, #0xa08]
    // 0xb2f2a0: StoreField: r0->field_13 = r5
    //     0xb2f2a0: stur            w5, [x0, #0x13]
    // 0xb2f2a4: r6 = Instance_MainAxisSize
    //     0xb2f2a4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2f2a8: ldr             x6, [x6, #0xa10]
    // 0xb2f2ac: ArrayStore: r0[0] = r6  ; List_4
    //     0xb2f2ac: stur            w6, [x0, #0x17]
    // 0xb2f2b0: r7 = Instance_CrossAxisAlignment
    //     0xb2f2b0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2f2b4: ldr             x7, [x7, #0xa18]
    // 0xb2f2b8: StoreField: r0->field_1b = r7
    //     0xb2f2b8: stur            w7, [x0, #0x1b]
    // 0xb2f2bc: r8 = Instance_VerticalDirection
    //     0xb2f2bc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2f2c0: ldr             x8, [x8, #0xa20]
    // 0xb2f2c4: StoreField: r0->field_23 = r8
    //     0xb2f2c4: stur            w8, [x0, #0x23]
    // 0xb2f2c8: r9 = Instance_Clip
    //     0xb2f2c8: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2f2cc: ldr             x9, [x9, #0x38]
    // 0xb2f2d0: StoreField: r0->field_2b = r9
    //     0xb2f2d0: stur            w9, [x0, #0x2b]
    // 0xb2f2d4: StoreField: r0->field_2f = rZR
    //     0xb2f2d4: stur            xzr, [x0, #0x2f]
    // 0xb2f2d8: ldur            x1, [fp, #-0x20]
    // 0xb2f2dc: StoreField: r0->field_b = r1
    //     0xb2f2dc: stur            w1, [x0, #0xb]
    // 0xb2f2e0: b               #0xb2f6f8
    // 0xb2f2e4: mov             x3, x0
    // 0xb2f2e8: r2 = 4
    //     0xb2f2e8: movz            x2, #0x4
    // 0xb2f2ec: r4 = 6
    //     0xb2f2ec: movz            x4, #0x6
    // 0xb2f2f0: r7 = Instance_CrossAxisAlignment
    //     0xb2f2f0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2f2f4: ldr             x7, [x7, #0xa18]
    // 0xb2f2f8: r5 = Instance_MainAxisAlignment
    //     0xb2f2f8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2f2fc: ldr             x5, [x5, #0xa08]
    // 0xb2f300: r6 = Instance_MainAxisSize
    //     0xb2f300: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2f304: ldr             x6, [x6, #0xa10]
    // 0xb2f308: r1 = Instance_Axis
    //     0xb2f308: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2f30c: r8 = Instance_VerticalDirection
    //     0xb2f30c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2f310: ldr             x8, [x8, #0xa20]
    // 0xb2f314: r0 = Instance__DeferringMouseCursor
    //     0xb2f314: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2f318: r9 = Instance_Clip
    //     0xb2f318: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2f31c: ldr             x9, [x9, #0x38]
    // 0xb2f320: d1 = 0.500000
    //     0xb2f320: fmov            d1, #0.50000000
    // 0xb2f324: d0 = inf
    //     0xb2f324: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb2f328: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb2f328: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb2f32c: ldr             x0, [x0, #0x1c80]
    //     0xb2f330: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb2f334: cmp             w0, w16
    //     0xb2f338: b.ne            #0xb2f344
    //     0xb2f33c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb2f340: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb2f344: r0 = GetNavigation.size()
    //     0xb2f344: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb2f348: LoadField: d0 = r0->field_7
    //     0xb2f348: ldur            d0, [x0, #7]
    // 0xb2f34c: d1 = 0.500000
    //     0xb2f34c: fmov            d1, #0.50000000
    // 0xb2f350: fmul            d2, d0, d1
    // 0xb2f354: stur            d2, [fp, #-0x40]
    // 0xb2f358: r0 = BoxConstraints()
    //     0xb2f358: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb2f35c: stur            x0, [fp, #-0x20]
    // 0xb2f360: StoreField: r0->field_7 = rZR
    //     0xb2f360: stur            xzr, [x0, #7]
    // 0xb2f364: ldur            d0, [fp, #-0x40]
    // 0xb2f368: StoreField: r0->field_f = d0
    //     0xb2f368: stur            d0, [x0, #0xf]
    // 0xb2f36c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb2f36c: stur            xzr, [x0, #0x17]
    // 0xb2f370: d0 = inf
    //     0xb2f370: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb2f374: StoreField: r0->field_1f = d0
    //     0xb2f374: stur            d0, [x0, #0x1f]
    // 0xb2f378: ldur            x2, [fp, #-8]
    // 0xb2f37c: LoadField: r1 = r2->field_b
    //     0xb2f37c: ldur            w1, [x2, #0xb]
    // 0xb2f380: DecompressPointer r1
    //     0xb2f380: add             x1, x1, HEAP, lsl #32
    // 0xb2f384: cmp             w1, NULL
    // 0xb2f388: b.eq            #0xb2f718
    // 0xb2f38c: LoadField: r3 = r1->field_f
    //     0xb2f38c: ldur            w3, [x1, #0xf]
    // 0xb2f390: DecompressPointer r3
    //     0xb2f390: add             x3, x3, HEAP, lsl #32
    // 0xb2f394: cmp             w3, NULL
    // 0xb2f398: b.ne            #0xb2f3a4
    // 0xb2f39c: r1 = Null
    //     0xb2f39c: mov             x1, NULL
    // 0xb2f3a0: b               #0xb2f3ac
    // 0xb2f3a4: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb2f3a4: ldur            w1, [x3, #0x17]
    // 0xb2f3a8: DecompressPointer r1
    //     0xb2f3a8: add             x1, x1, HEAP, lsl #32
    // 0xb2f3ac: cmp             w1, NULL
    // 0xb2f3b0: b.ne            #0xb2f3bc
    // 0xb2f3b4: r3 = ""
    //     0xb2f3b4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2f3b8: b               #0xb2f3c0
    // 0xb2f3bc: mov             x3, x1
    // 0xb2f3c0: ldur            x1, [fp, #-0x10]
    // 0xb2f3c4: stur            x3, [fp, #-0x18]
    // 0xb2f3c8: r0 = of()
    //     0xb2f3c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2f3cc: LoadField: r1 = r0->field_87
    //     0xb2f3cc: ldur            w1, [x0, #0x87]
    // 0xb2f3d0: DecompressPointer r1
    //     0xb2f3d0: add             x1, x1, HEAP, lsl #32
    // 0xb2f3d4: LoadField: r0 = r1->field_7
    //     0xb2f3d4: ldur            w0, [x1, #7]
    // 0xb2f3d8: DecompressPointer r0
    //     0xb2f3d8: add             x0, x0, HEAP, lsl #32
    // 0xb2f3dc: r16 = 16.000000
    //     0xb2f3dc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb2f3e0: ldr             x16, [x16, #0x188]
    // 0xb2f3e4: r30 = Instance_Color
    //     0xb2f3e4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2f3e8: stp             lr, x16, [SP]
    // 0xb2f3ec: mov             x1, x0
    // 0xb2f3f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2f3f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2f3f4: ldr             x4, [x4, #0xaa0]
    // 0xb2f3f8: r0 = copyWith()
    //     0xb2f3f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2f3fc: stur            x0, [fp, #-0x28]
    // 0xb2f400: r0 = TextSpan()
    //     0xb2f400: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2f404: mov             x3, x0
    // 0xb2f408: ldur            x0, [fp, #-0x18]
    // 0xb2f40c: stur            x3, [fp, #-0x30]
    // 0xb2f410: StoreField: r3->field_b = r0
    //     0xb2f410: stur            w0, [x3, #0xb]
    // 0xb2f414: r0 = Instance__DeferringMouseCursor
    //     0xb2f414: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2f418: ArrayStore: r3[0] = r0  ; List_4
    //     0xb2f418: stur            w0, [x3, #0x17]
    // 0xb2f41c: ldur            x1, [fp, #-0x28]
    // 0xb2f420: StoreField: r3->field_7 = r1
    //     0xb2f420: stur            w1, [x3, #7]
    // 0xb2f424: r1 = Null
    //     0xb2f424: mov             x1, NULL
    // 0xb2f428: r2 = 4
    //     0xb2f428: movz            x2, #0x4
    // 0xb2f42c: r0 = AllocateArray()
    //     0xb2f42c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2f430: stur            x0, [fp, #-0x18]
    // 0xb2f434: r16 = " : "
    //     0xb2f434: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xb2f438: ldr             x16, [x16, #0x680]
    // 0xb2f43c: StoreField: r0->field_f = r16
    //     0xb2f43c: stur            w16, [x0, #0xf]
    // 0xb2f440: ldur            x2, [fp, #-8]
    // 0xb2f444: LoadField: r1 = r2->field_13
    //     0xb2f444: ldur            w1, [x2, #0x13]
    // 0xb2f448: DecompressPointer r1
    //     0xb2f448: add             x1, x1, HEAP, lsl #32
    // 0xb2f44c: r16 = ", "
    //     0xb2f44c: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0xb2f450: str             x16, [SP]
    // 0xb2f454: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb2f454: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb2f458: r0 = join()
    //     0xb2f458: bl              #0x7d6d4c  ; [dart:core] _GrowableList::join
    // 0xb2f45c: ldur            x1, [fp, #-0x18]
    // 0xb2f460: ArrayStore: r1[1] = r0  ; List_4
    //     0xb2f460: add             x25, x1, #0x13
    //     0xb2f464: str             w0, [x25]
    //     0xb2f468: tbz             w0, #0, #0xb2f484
    //     0xb2f46c: ldurb           w16, [x1, #-1]
    //     0xb2f470: ldurb           w17, [x0, #-1]
    //     0xb2f474: and             x16, x17, x16, lsr #2
    //     0xb2f478: tst             x16, HEAP, lsr #32
    //     0xb2f47c: b.eq            #0xb2f484
    //     0xb2f480: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb2f484: ldur            x16, [fp, #-0x18]
    // 0xb2f488: str             x16, [SP]
    // 0xb2f48c: r0 = _interpolate()
    //     0xb2f48c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb2f490: ldur            x1, [fp, #-0x10]
    // 0xb2f494: stur            x0, [fp, #-0x18]
    // 0xb2f498: r0 = of()
    //     0xb2f498: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2f49c: LoadField: r1 = r0->field_87
    //     0xb2f49c: ldur            w1, [x0, #0x87]
    // 0xb2f4a0: DecompressPointer r1
    //     0xb2f4a0: add             x1, x1, HEAP, lsl #32
    // 0xb2f4a4: LoadField: r0 = r1->field_2b
    //     0xb2f4a4: ldur            w0, [x1, #0x2b]
    // 0xb2f4a8: DecompressPointer r0
    //     0xb2f4a8: add             x0, x0, HEAP, lsl #32
    // 0xb2f4ac: r16 = 14.000000
    //     0xb2f4ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2f4b0: ldr             x16, [x16, #0x1d8]
    // 0xb2f4b4: r30 = Instance_Color
    //     0xb2f4b4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2f4b8: stp             lr, x16, [SP]
    // 0xb2f4bc: mov             x1, x0
    // 0xb2f4c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2f4c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2f4c4: ldr             x4, [x4, #0xaa0]
    // 0xb2f4c8: r0 = copyWith()
    //     0xb2f4c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2f4cc: stur            x0, [fp, #-0x28]
    // 0xb2f4d0: r0 = TextSpan()
    //     0xb2f4d0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2f4d4: mov             x3, x0
    // 0xb2f4d8: ldur            x0, [fp, #-0x18]
    // 0xb2f4dc: stur            x3, [fp, #-0x38]
    // 0xb2f4e0: StoreField: r3->field_b = r0
    //     0xb2f4e0: stur            w0, [x3, #0xb]
    // 0xb2f4e4: r0 = Instance__DeferringMouseCursor
    //     0xb2f4e4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2f4e8: ArrayStore: r3[0] = r0  ; List_4
    //     0xb2f4e8: stur            w0, [x3, #0x17]
    // 0xb2f4ec: ldur            x1, [fp, #-0x28]
    // 0xb2f4f0: StoreField: r3->field_7 = r1
    //     0xb2f4f0: stur            w1, [x3, #7]
    // 0xb2f4f4: r1 = Null
    //     0xb2f4f4: mov             x1, NULL
    // 0xb2f4f8: r2 = 4
    //     0xb2f4f8: movz            x2, #0x4
    // 0xb2f4fc: r0 = AllocateArray()
    //     0xb2f4fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2f500: mov             x2, x0
    // 0xb2f504: ldur            x0, [fp, #-0x30]
    // 0xb2f508: stur            x2, [fp, #-0x18]
    // 0xb2f50c: StoreField: r2->field_f = r0
    //     0xb2f50c: stur            w0, [x2, #0xf]
    // 0xb2f510: ldur            x0, [fp, #-0x38]
    // 0xb2f514: StoreField: r2->field_13 = r0
    //     0xb2f514: stur            w0, [x2, #0x13]
    // 0xb2f518: r1 = <InlineSpan>
    //     0xb2f518: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb2f51c: ldr             x1, [x1, #0xe40]
    // 0xb2f520: r0 = AllocateGrowableArray()
    //     0xb2f520: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2f524: mov             x1, x0
    // 0xb2f528: ldur            x0, [fp, #-0x18]
    // 0xb2f52c: stur            x1, [fp, #-0x28]
    // 0xb2f530: StoreField: r1->field_f = r0
    //     0xb2f530: stur            w0, [x1, #0xf]
    // 0xb2f534: r0 = 4
    //     0xb2f534: movz            x0, #0x4
    // 0xb2f538: StoreField: r1->field_b = r0
    //     0xb2f538: stur            w0, [x1, #0xb]
    // 0xb2f53c: r0 = TextSpan()
    //     0xb2f53c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2f540: mov             x1, x0
    // 0xb2f544: ldur            x0, [fp, #-0x28]
    // 0xb2f548: stur            x1, [fp, #-0x18]
    // 0xb2f54c: StoreField: r1->field_f = r0
    //     0xb2f54c: stur            w0, [x1, #0xf]
    // 0xb2f550: r0 = Instance__DeferringMouseCursor
    //     0xb2f550: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2f554: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2f554: stur            w0, [x1, #0x17]
    // 0xb2f558: r0 = RichText()
    //     0xb2f558: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb2f55c: mov             x1, x0
    // 0xb2f560: ldur            x2, [fp, #-0x18]
    // 0xb2f564: stur            x0, [fp, #-0x18]
    // 0xb2f568: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb2f568: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb2f56c: r0 = RichText()
    //     0xb2f56c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb2f570: r0 = ConstrainedBox()
    //     0xb2f570: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xb2f574: mov             x1, x0
    // 0xb2f578: ldur            x0, [fp, #-0x20]
    // 0xb2f57c: stur            x1, [fp, #-0x28]
    // 0xb2f580: StoreField: r1->field_f = r0
    //     0xb2f580: stur            w0, [x1, #0xf]
    // 0xb2f584: ldur            x0, [fp, #-0x18]
    // 0xb2f588: StoreField: r1->field_b = r0
    //     0xb2f588: stur            w0, [x1, #0xb]
    // 0xb2f58c: ldur            x0, [fp, #-8]
    // 0xb2f590: LoadField: r2 = r0->field_b
    //     0xb2f590: ldur            w2, [x0, #0xb]
    // 0xb2f594: DecompressPointer r2
    //     0xb2f594: add             x2, x2, HEAP, lsl #32
    // 0xb2f598: cmp             w2, NULL
    // 0xb2f59c: b.eq            #0xb2f71c
    // 0xb2f5a0: LoadField: r0 = r2->field_f
    //     0xb2f5a0: ldur            w0, [x2, #0xf]
    // 0xb2f5a4: DecompressPointer r0
    //     0xb2f5a4: add             x0, x0, HEAP, lsl #32
    // 0xb2f5a8: cmp             w0, NULL
    // 0xb2f5ac: b.ne            #0xb2f5b8
    // 0xb2f5b0: r0 = Null
    //     0xb2f5b0: mov             x0, NULL
    // 0xb2f5b4: b               #0xb2f5e0
    // 0xb2f5b8: LoadField: r2 = r0->field_2b
    //     0xb2f5b8: ldur            w2, [x0, #0x2b]
    // 0xb2f5bc: DecompressPointer r2
    //     0xb2f5bc: add             x2, x2, HEAP, lsl #32
    // 0xb2f5c0: r0 = LoadClassIdInstr(r2)
    //     0xb2f5c0: ldur            x0, [x2, #-1]
    //     0xb2f5c4: ubfx            x0, x0, #0xc, #0x14
    // 0xb2f5c8: str             x2, [SP]
    // 0xb2f5cc: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb2f5cc: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb2f5d0: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb2f5d0: movz            x17, #0x2700
    //     0xb2f5d4: add             lr, x0, x17
    //     0xb2f5d8: ldr             lr, [x21, lr, lsl #3]
    //     0xb2f5dc: blr             lr
    // 0xb2f5e0: cmp             w0, NULL
    // 0xb2f5e4: b.ne            #0xb2f5f0
    // 0xb2f5e8: r2 = " "
    //     0xb2f5e8: ldr             x2, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb2f5ec: b               #0xb2f5f4
    // 0xb2f5f0: mov             x2, x0
    // 0xb2f5f4: ldur            x0, [fp, #-0x28]
    // 0xb2f5f8: ldur            x1, [fp, #-0x10]
    // 0xb2f5fc: stur            x2, [fp, #-8]
    // 0xb2f600: r0 = of()
    //     0xb2f600: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2f604: LoadField: r1 = r0->field_87
    //     0xb2f604: ldur            w1, [x0, #0x87]
    // 0xb2f608: DecompressPointer r1
    //     0xb2f608: add             x1, x1, HEAP, lsl #32
    // 0xb2f60c: LoadField: r0 = r1->field_2b
    //     0xb2f60c: ldur            w0, [x1, #0x2b]
    // 0xb2f610: DecompressPointer r0
    //     0xb2f610: add             x0, x0, HEAP, lsl #32
    // 0xb2f614: r16 = 14.000000
    //     0xb2f614: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2f618: ldr             x16, [x16, #0x1d8]
    // 0xb2f61c: r30 = Instance_Color
    //     0xb2f61c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2f620: stp             lr, x16, [SP]
    // 0xb2f624: mov             x1, x0
    // 0xb2f628: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2f628: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2f62c: ldr             x4, [x4, #0xaa0]
    // 0xb2f630: r0 = copyWith()
    //     0xb2f630: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2f634: stur            x0, [fp, #-0x10]
    // 0xb2f638: r0 = Text()
    //     0xb2f638: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2f63c: mov             x3, x0
    // 0xb2f640: ldur            x0, [fp, #-8]
    // 0xb2f644: stur            x3, [fp, #-0x18]
    // 0xb2f648: StoreField: r3->field_b = r0
    //     0xb2f648: stur            w0, [x3, #0xb]
    // 0xb2f64c: ldur            x0, [fp, #-0x10]
    // 0xb2f650: StoreField: r3->field_13 = r0
    //     0xb2f650: stur            w0, [x3, #0x13]
    // 0xb2f654: r1 = Null
    //     0xb2f654: mov             x1, NULL
    // 0xb2f658: r2 = 6
    //     0xb2f658: movz            x2, #0x6
    // 0xb2f65c: r0 = AllocateArray()
    //     0xb2f65c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2f660: mov             x2, x0
    // 0xb2f664: ldur            x0, [fp, #-0x28]
    // 0xb2f668: stur            x2, [fp, #-8]
    // 0xb2f66c: StoreField: r2->field_f = r0
    //     0xb2f66c: stur            w0, [x2, #0xf]
    // 0xb2f670: r16 = Instance_Spacer
    //     0xb2f670: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb2f674: ldr             x16, [x16, #0xf0]
    // 0xb2f678: StoreField: r2->field_13 = r16
    //     0xb2f678: stur            w16, [x2, #0x13]
    // 0xb2f67c: ldur            x0, [fp, #-0x18]
    // 0xb2f680: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2f680: stur            w0, [x2, #0x17]
    // 0xb2f684: r1 = <Widget>
    //     0xb2f684: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2f688: r0 = AllocateGrowableArray()
    //     0xb2f688: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2f68c: mov             x1, x0
    // 0xb2f690: ldur            x0, [fp, #-8]
    // 0xb2f694: stur            x1, [fp, #-0x10]
    // 0xb2f698: StoreField: r1->field_f = r0
    //     0xb2f698: stur            w0, [x1, #0xf]
    // 0xb2f69c: r0 = 6
    //     0xb2f69c: movz            x0, #0x6
    // 0xb2f6a0: StoreField: r1->field_b = r0
    //     0xb2f6a0: stur            w0, [x1, #0xb]
    // 0xb2f6a4: r0 = Row()
    //     0xb2f6a4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2f6a8: r1 = Instance_Axis
    //     0xb2f6a8: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2f6ac: StoreField: r0->field_f = r1
    //     0xb2f6ac: stur            w1, [x0, #0xf]
    // 0xb2f6b0: r1 = Instance_MainAxisAlignment
    //     0xb2f6b0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2f6b4: ldr             x1, [x1, #0xa08]
    // 0xb2f6b8: StoreField: r0->field_13 = r1
    //     0xb2f6b8: stur            w1, [x0, #0x13]
    // 0xb2f6bc: r1 = Instance_MainAxisSize
    //     0xb2f6bc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2f6c0: ldr             x1, [x1, #0xa10]
    // 0xb2f6c4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb2f6c4: stur            w1, [x0, #0x17]
    // 0xb2f6c8: r1 = Instance_CrossAxisAlignment
    //     0xb2f6c8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2f6cc: ldr             x1, [x1, #0xa18]
    // 0xb2f6d0: StoreField: r0->field_1b = r1
    //     0xb2f6d0: stur            w1, [x0, #0x1b]
    // 0xb2f6d4: r1 = Instance_VerticalDirection
    //     0xb2f6d4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2f6d8: ldr             x1, [x1, #0xa20]
    // 0xb2f6dc: StoreField: r0->field_23 = r1
    //     0xb2f6dc: stur            w1, [x0, #0x23]
    // 0xb2f6e0: r1 = Instance_Clip
    //     0xb2f6e0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2f6e4: ldr             x1, [x1, #0x38]
    // 0xb2f6e8: StoreField: r0->field_2b = r1
    //     0xb2f6e8: stur            w1, [x0, #0x2b]
    // 0xb2f6ec: StoreField: r0->field_2f = rZR
    //     0xb2f6ec: stur            xzr, [x0, #0x2f]
    // 0xb2f6f0: ldur            x1, [fp, #-0x10]
    // 0xb2f6f4: StoreField: r0->field_b = r1
    //     0xb2f6f4: stur            w1, [x0, #0xb]
    // 0xb2f6f8: LeaveFrame
    //     0xb2f6f8: mov             SP, fp
    //     0xb2f6fc: ldp             fp, lr, [SP], #0x10
    // 0xb2f700: ret
    //     0xb2f700: ret             
    // 0xb2f704: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2f704: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2f708: b               #0xb2ee64
    // 0xb2f70c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2f70c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2f710: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2f710: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2f714: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2f714: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2f718: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2f718: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2f71c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2f71c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4120, size: 0x14, field offset: 0xc
//   const constructor, 
class BagMultiSelect extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e618, size: 0x7c
    // 0xc7e618: EnterFrame
    //     0xc7e618: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e61c: mov             fp, SP
    // 0xc7e620: AllocStack(0x10)
    //     0xc7e620: sub             SP, SP, #0x10
    // 0xc7e624: CheckStackOverflow
    //     0xc7e624: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7e628: cmp             SP, x16
    //     0xc7e62c: b.ls            #0xc7e68c
    // 0xc7e630: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xc7e630: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc7e634: ldr             x0, [x0]
    //     0xc7e638: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc7e63c: cmp             w0, w16
    //     0xc7e640: b.ne            #0xc7e64c
    //     0xc7e644: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xc7e648: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc7e64c: r1 = <String>
    //     0xc7e64c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xc7e650: stur            x0, [fp, #-8]
    // 0xc7e654: r0 = AllocateGrowableArray()
    //     0xc7e654: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc7e658: mov             x2, x0
    // 0xc7e65c: ldur            x0, [fp, #-8]
    // 0xc7e660: stur            x2, [fp, #-0x10]
    // 0xc7e664: StoreField: r2->field_f = r0
    //     0xc7e664: stur            w0, [x2, #0xf]
    // 0xc7e668: StoreField: r2->field_b = rZR
    //     0xc7e668: stur            wzr, [x2, #0xb]
    // 0xc7e66c: r1 = <BagMultiSelect>
    //     0xc7e66c: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e58] TypeArguments: <BagMultiSelect>
    //     0xc7e670: ldr             x1, [x1, #0xe58]
    // 0xc7e674: r0 = _BagMultiSelectState()
    //     0xc7e674: bl              #0xc7e694  ; Allocate_BagMultiSelectStateStub -> _BagMultiSelectState (size=0x18)
    // 0xc7e678: ldur            x1, [fp, #-0x10]
    // 0xc7e67c: StoreField: r0->field_13 = r1
    //     0xc7e67c: stur            w1, [x0, #0x13]
    // 0xc7e680: LeaveFrame
    //     0xc7e680: mov             SP, fp
    //     0xc7e684: ldp             fp, lr, [SP], #0x10
    // 0xc7e688: ret
    //     0xc7e688: ret             
    // 0xc7e68c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e68c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e690: b               #0xc7e630
  }
}
