// lib: , url: package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart

// class id: 1049584, size: 0x8
class :: {
}

// class id: 4516, size: 0x14, field offset: 0x14
//   const constructor, 
class TestimonialsView extends BaseView<dynamic> {

  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x1479fd8, size: 0x74
    // 0x1479fd8: EnterFrame
    //     0x1479fd8: stp             fp, lr, [SP, #-0x10]!
    //     0x1479fdc: mov             fp, SP
    // 0x1479fe0: AllocStack(0x8)
    //     0x1479fe0: sub             SP, SP, #8
    // 0x1479fe4: CheckStackOverflow
    //     0x1479fe4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1479fe8: cmp             SP, x16
    //     0x1479fec: b.ls            #0x147a044
    // 0x1479ff0: r1 = <bool>
    //     0x1479ff0: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x1479ff4: r0 = _Future()
    //     0x1479ff4: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x1479ff8: stur            x0, [fp, #-8]
    // 0x1479ffc: StoreField: r0->field_b = rZR
    //     0x1479ffc: stur            xzr, [x0, #0xb]
    // 0x147a000: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x147a000: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x147a004: ldr             x0, [x0, #0x778]
    //     0x147a008: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x147a00c: cmp             w0, w16
    //     0x147a010: b.ne            #0x147a01c
    //     0x147a014: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x147a018: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x147a01c: mov             x1, x0
    // 0x147a020: ldur            x0, [fp, #-8]
    // 0x147a024: StoreField: r0->field_13 = r1
    //     0x147a024: stur            w1, [x0, #0x13]
    // 0x147a028: mov             x1, x0
    // 0x147a02c: r2 = true
    //     0x147a02c: add             x2, NULL, #0x20  ; true
    // 0x147a030: r0 = _asyncComplete()
    //     0x147a030: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x147a034: ldur            x0, [fp, #-8]
    // 0x147a038: LeaveFrame
    //     0x147a038: mov             SP, fp
    //     0x147a03c: ldp             fp, lr, [SP], #0x10
    // 0x147a040: ret
    //     0x147a040: ret             
    // 0x147a044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147a044: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147a048: b               #0x1479ff0
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x147a04c, size: 0x50
    // 0x147a04c: EnterFrame
    //     0x147a04c: stp             fp, lr, [SP, #-0x10]!
    //     0x147a050: mov             fp, SP
    // 0x147a054: ldr             x0, [fp, #0x10]
    // 0x147a058: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x147a058: ldur            w1, [x0, #0x17]
    // 0x147a05c: DecompressPointer r1
    //     0x147a05c: add             x1, x1, HEAP, lsl #32
    // 0x147a060: CheckStackOverflow
    //     0x147a060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147a064: cmp             SP, x16
    //     0x147a068: b.ls            #0x147a094
    // 0x147a06c: LoadField: r0 = r1->field_f
    //     0x147a06c: ldur            w0, [x1, #0xf]
    // 0x147a070: DecompressPointer r0
    //     0x147a070: add             x0, x0, HEAP, lsl #32
    // 0x147a074: mov             x1, x0
    // 0x147a078: r0 = controller()
    //     0x147a078: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147a07c: mov             x1, x0
    // 0x147a080: r0 = getTestimonials()
    //     0x147a080: bl              #0x147a09c  ; [package:customer_app/app/presentation/controllers/testimonials/testimonials_controller.dart] TestimonialsController::getTestimonials
    // 0x147a084: r0 = Null
    //     0x147a084: mov             x0, NULL
    // 0x147a088: LeaveFrame
    //     0x147a088: mov             SP, fp
    //     0x147a08c: ldp             fp, lr, [SP], #0x10
    // 0x147a090: ret
    //     0x147a090: ret             
    // 0x147a094: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147a094: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147a098: b               #0x147a06c
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x147bdfc, size: 0x64
    // 0x147bdfc: EnterFrame
    //     0x147bdfc: stp             fp, lr, [SP, #-0x10]!
    //     0x147be00: mov             fp, SP
    // 0x147be04: AllocStack(0x10)
    //     0x147be04: sub             SP, SP, #0x10
    // 0x147be08: SetupParameters(TestimonialsView this /* r1 */)
    //     0x147be08: stur            NULL, [fp, #-8]
    //     0x147be0c: movz            x0, #0
    //     0x147be10: add             x1, fp, w0, sxtw #2
    //     0x147be14: ldr             x1, [x1, #0x10]
    //     0x147be18: ldur            w2, [x1, #0x17]
    //     0x147be1c: add             x2, x2, HEAP, lsl #32
    //     0x147be20: stur            x2, [fp, #-0x10]
    // 0x147be24: CheckStackOverflow
    //     0x147be24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147be28: cmp             SP, x16
    //     0x147be2c: b.ls            #0x147be58
    // 0x147be30: InitAsync() -> Future<void?>
    //     0x147be30: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x147be34: bl              #0x6326e0  ; InitAsyncStub
    // 0x147be38: ldur            x0, [fp, #-0x10]
    // 0x147be3c: LoadField: r1 = r0->field_f
    //     0x147be3c: ldur            w1, [x0, #0xf]
    // 0x147be40: DecompressPointer r1
    //     0x147be40: add             x1, x1, HEAP, lsl #32
    // 0x147be44: r0 = controller()
    //     0x147be44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147be48: mov             x1, x0
    // 0x147be4c: r0 = onRefreshPage()
    //     0x147be4c: bl              #0x147be60  ; [package:customer_app/app/presentation/controllers/testimonials/testimonials_controller.dart] TestimonialsController::onRefreshPage
    // 0x147be50: r0 = Null
    //     0x147be50: mov             x0, NULL
    // 0x147be54: r0 = ReturnAsyncNotFuture()
    //     0x147be54: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x147be58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147be58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147be5c: b               #0x147be30
  }
  _ body(/* No info */) {
    // ** addr: 0x15096cc, size: 0x10c
    // 0x15096cc: EnterFrame
    //     0x15096cc: stp             fp, lr, [SP, #-0x10]!
    //     0x15096d0: mov             fp, SP
    // 0x15096d4: AllocStack(0x18)
    //     0x15096d4: sub             SP, SP, #0x18
    // 0x15096d8: SetupParameters(TestimonialsView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15096d8: stur            x1, [fp, #-8]
    //     0x15096dc: stur            x2, [fp, #-0x10]
    // 0x15096e0: CheckStackOverflow
    //     0x15096e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15096e4: cmp             SP, x16
    //     0x15096e8: b.ls            #0x15097d0
    // 0x15096ec: r1 = 2
    //     0x15096ec: movz            x1, #0x2
    // 0x15096f0: r0 = AllocateContext()
    //     0x15096f0: bl              #0x16f6108  ; AllocateContextStub
    // 0x15096f4: mov             x1, x0
    // 0x15096f8: ldur            x0, [fp, #-8]
    // 0x15096fc: stur            x1, [fp, #-0x18]
    // 0x1509700: StoreField: r1->field_f = r0
    //     0x1509700: stur            w0, [x1, #0xf]
    // 0x1509704: ldur            x0, [fp, #-0x10]
    // 0x1509708: StoreField: r1->field_13 = r0
    //     0x1509708: stur            w0, [x1, #0x13]
    // 0x150970c: r0 = Obx()
    //     0x150970c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1509710: ldur            x2, [fp, #-0x18]
    // 0x1509714: r1 = Function '<anonymous closure>':.
    //     0x1509714: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e660] AnonymousClosure: (0x15097d8), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::body (0x15096cc)
    //     0x1509718: ldr             x1, [x1, #0x660]
    // 0x150971c: stur            x0, [fp, #-8]
    // 0x1509720: r0 = AllocateClosure()
    //     0x1509720: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1509724: mov             x1, x0
    // 0x1509728: ldur            x0, [fp, #-8]
    // 0x150972c: StoreField: r0->field_b = r1
    //     0x150972c: stur            w1, [x0, #0xb]
    // 0x1509730: r0 = Padding()
    //     0x1509730: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1509734: mov             x3, x0
    // 0x1509738: r0 = Instance_EdgeInsets
    //     0x1509738: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x150973c: ldr             x0, [x0, #0x668]
    // 0x1509740: stur            x3, [fp, #-0x10]
    // 0x1509744: StoreField: r3->field_f = r0
    //     0x1509744: stur            w0, [x3, #0xf]
    // 0x1509748: ldur            x0, [fp, #-8]
    // 0x150974c: StoreField: r3->field_b = r0
    //     0x150974c: stur            w0, [x3, #0xb]
    // 0x1509750: ldur            x2, [fp, #-0x18]
    // 0x1509754: r1 = Function '<anonymous closure>':.
    //     0x1509754: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e670] AnonymousClosure: (0x147bdfc), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::body (0x15096cc)
    //     0x1509758: ldr             x1, [x1, #0x670]
    // 0x150975c: r0 = AllocateClosure()
    //     0x150975c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1509760: ldur            x2, [fp, #-0x18]
    // 0x1509764: r1 = Function '<anonymous closure>':.
    //     0x1509764: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e678] AnonymousClosure: (0x147a04c), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::body (0x15096cc)
    //     0x1509768: ldr             x1, [x1, #0x678]
    // 0x150976c: stur            x0, [fp, #-8]
    // 0x1509770: r0 = AllocateClosure()
    //     0x1509770: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1509774: stur            x0, [fp, #-0x18]
    // 0x1509778: r0 = PagingView()
    //     0x1509778: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x150977c: mov             x1, x0
    // 0x1509780: ldur            x2, [fp, #-0x10]
    // 0x1509784: ldur            x3, [fp, #-0x18]
    // 0x1509788: ldur            x5, [fp, #-8]
    // 0x150978c: stur            x0, [fp, #-8]
    // 0x1509790: r0 = PagingView()
    //     0x1509790: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x1509794: r0 = WillPopScope()
    //     0x1509794: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x1509798: mov             x3, x0
    // 0x150979c: ldur            x0, [fp, #-8]
    // 0x15097a0: stur            x3, [fp, #-0x10]
    // 0x15097a4: StoreField: r3->field_b = r0
    //     0x15097a4: stur            w0, [x3, #0xb]
    // 0x15097a8: r1 = Function '<anonymous closure>':.
    //     0x15097a8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e680] AnonymousClosure: (0x1479fd8), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::body (0x15096cc)
    //     0x15097ac: ldr             x1, [x1, #0x680]
    // 0x15097b0: r2 = Null
    //     0x15097b0: mov             x2, NULL
    // 0x15097b4: r0 = AllocateClosure()
    //     0x15097b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15097b8: mov             x1, x0
    // 0x15097bc: ldur            x0, [fp, #-0x10]
    // 0x15097c0: StoreField: r0->field_f = r1
    //     0x15097c0: stur            w1, [x0, #0xf]
    // 0x15097c4: LeaveFrame
    //     0x15097c4: mov             SP, fp
    //     0x15097c8: ldp             fp, lr, [SP], #0x10
    // 0x15097cc: ret
    //     0x15097cc: ret             
    // 0x15097d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15097d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15097d4: b               #0x15096ec
  }
  [closure] RenderObjectWidget <anonymous closure>(dynamic) {
    // ** addr: 0x15097d8, size: 0x5d4
    // 0x15097d8: EnterFrame
    //     0x15097d8: stp             fp, lr, [SP, #-0x10]!
    //     0x15097dc: mov             fp, SP
    // 0x15097e0: AllocStack(0x50)
    //     0x15097e0: sub             SP, SP, #0x50
    // 0x15097e4: SetupParameters()
    //     0x15097e4: ldr             x0, [fp, #0x10]
    //     0x15097e8: ldur            w2, [x0, #0x17]
    //     0x15097ec: add             x2, x2, HEAP, lsl #32
    //     0x15097f0: stur            x2, [fp, #-8]
    // 0x15097f4: CheckStackOverflow
    //     0x15097f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15097f8: cmp             SP, x16
    //     0x15097fc: b.ls            #0x1509da4
    // 0x1509800: LoadField: r1 = r2->field_f
    //     0x1509800: ldur            w1, [x2, #0xf]
    // 0x1509804: DecompressPointer r1
    //     0x1509804: add             x1, x1, HEAP, lsl #32
    // 0x1509808: r0 = controller()
    //     0x1509808: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x150980c: LoadField: r1 = r0->field_53
    //     0x150980c: ldur            w1, [x0, #0x53]
    // 0x1509810: DecompressPointer r1
    //     0x1509810: add             x1, x1, HEAP, lsl #32
    // 0x1509814: r0 = value()
    //     0x1509814: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1509818: LoadField: r1 = r0->field_b
    //     0x1509818: ldur            w1, [x0, #0xb]
    // 0x150981c: DecompressPointer r1
    //     0x150981c: add             x1, x1, HEAP, lsl #32
    // 0x1509820: cmp             w1, NULL
    // 0x1509824: b.ne            #0x1509834
    // 0x1509828: r0 = Instance_Center
    //     0x1509828: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9a8] Obj!Center@d68361
    //     0x150982c: ldr             x0, [x0, #0x9a8]
    // 0x1509830: b               #0x1509d98
    // 0x1509834: ldur            x0, [fp, #-8]
    // 0x1509838: LoadField: r1 = r0->field_13
    //     0x1509838: ldur            w1, [x0, #0x13]
    // 0x150983c: DecompressPointer r1
    //     0x150983c: add             x1, x1, HEAP, lsl #32
    // 0x1509840: r0 = of()
    //     0x1509840: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1509844: LoadField: r1 = r0->field_87
    //     0x1509844: ldur            w1, [x0, #0x87]
    // 0x1509848: DecompressPointer r1
    //     0x1509848: add             x1, x1, HEAP, lsl #32
    // 0x150984c: LoadField: r0 = r1->field_7
    //     0x150984c: ldur            w0, [x1, #7]
    // 0x1509850: DecompressPointer r0
    //     0x1509850: add             x0, x0, HEAP, lsl #32
    // 0x1509854: r16 = Instance_Color
    //     0x1509854: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1509858: r30 = 21.000000
    //     0x1509858: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x150985c: ldr             lr, [lr, #0x9b0]
    // 0x1509860: stp             lr, x16, [SP]
    // 0x1509864: mov             x1, x0
    // 0x1509868: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1509868: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x150986c: ldr             x4, [x4, #0x9b8]
    // 0x1509870: r0 = copyWith()
    //     0x1509870: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1509874: stur            x0, [fp, #-0x10]
    // 0x1509878: r0 = Text()
    //     0x1509878: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x150987c: mov             x2, x0
    // 0x1509880: r0 = "Customers Love Us"
    //     0x1509880: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9c0] "Customers Love Us"
    //     0x1509884: ldr             x0, [x0, #0x9c0]
    // 0x1509888: stur            x2, [fp, #-0x18]
    // 0x150988c: StoreField: r2->field_b = r0
    //     0x150988c: stur            w0, [x2, #0xb]
    // 0x1509890: ldur            x0, [fp, #-0x10]
    // 0x1509894: StoreField: r2->field_13 = r0
    //     0x1509894: stur            w0, [x2, #0x13]
    // 0x1509898: ldur            x0, [fp, #-8]
    // 0x150989c: LoadField: r1 = r0->field_13
    //     0x150989c: ldur            w1, [x0, #0x13]
    // 0x15098a0: DecompressPointer r1
    //     0x15098a0: add             x1, x1, HEAP, lsl #32
    // 0x15098a4: r0 = of()
    //     0x15098a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15098a8: LoadField: r1 = r0->field_87
    //     0x15098a8: ldur            w1, [x0, #0x87]
    // 0x15098ac: DecompressPointer r1
    //     0x15098ac: add             x1, x1, HEAP, lsl #32
    // 0x15098b0: LoadField: r0 = r1->field_7
    //     0x15098b0: ldur            w0, [x1, #7]
    // 0x15098b4: DecompressPointer r0
    //     0x15098b4: add             x0, x0, HEAP, lsl #32
    // 0x15098b8: ldur            x2, [fp, #-8]
    // 0x15098bc: stur            x0, [fp, #-0x10]
    // 0x15098c0: LoadField: r1 = r2->field_13
    //     0x15098c0: ldur            w1, [x2, #0x13]
    // 0x15098c4: DecompressPointer r1
    //     0x15098c4: add             x1, x1, HEAP, lsl #32
    // 0x15098c8: r0 = of()
    //     0x15098c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15098cc: LoadField: r1 = r0->field_5b
    //     0x15098cc: ldur            w1, [x0, #0x5b]
    // 0x15098d0: DecompressPointer r1
    //     0x15098d0: add             x1, x1, HEAP, lsl #32
    // 0x15098d4: r16 = 21.000000
    //     0x15098d4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x15098d8: ldr             x16, [x16, #0x9b0]
    // 0x15098dc: stp             x16, x1, [SP]
    // 0x15098e0: ldur            x1, [fp, #-0x10]
    // 0x15098e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x15098e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x15098e8: ldr             x4, [x4, #0x9b8]
    // 0x15098ec: r0 = copyWith()
    //     0x15098ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15098f0: stur            x0, [fp, #-0x10]
    // 0x15098f4: r0 = Text()
    //     0x15098f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15098f8: mov             x2, x0
    // 0x15098fc: r0 = "—"
    //     0x15098fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9c8] "—"
    //     0x1509900: ldr             x0, [x0, #0x9c8]
    // 0x1509904: stur            x2, [fp, #-0x20]
    // 0x1509908: StoreField: r2->field_b = r0
    //     0x1509908: stur            w0, [x2, #0xb]
    // 0x150990c: ldur            x0, [fp, #-0x10]
    // 0x1509910: StoreField: r2->field_13 = r0
    //     0x1509910: stur            w0, [x2, #0x13]
    // 0x1509914: r1 = Instance_Color
    //     0x1509914: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1509918: d0 = 0.100000
    //     0x1509918: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x150991c: r0 = withOpacity()
    //     0x150991c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1509920: stur            x0, [fp, #-0x10]
    // 0x1509924: r0 = Divider()
    //     0x1509924: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x1509928: mov             x3, x0
    // 0x150992c: ldur            x0, [fp, #-0x10]
    // 0x1509930: stur            x3, [fp, #-0x28]
    // 0x1509934: StoreField: r3->field_1f = r0
    //     0x1509934: stur            w0, [x3, #0x1f]
    // 0x1509938: r1 = Null
    //     0x1509938: mov             x1, NULL
    // 0x150993c: r2 = 10
    //     0x150993c: movz            x2, #0xa
    // 0x1509940: r0 = AllocateArray()
    //     0x1509940: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1509944: stur            x0, [fp, #-0x10]
    // 0x1509948: r16 = "Showing "
    //     0x1509948: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9d0] "Showing "
    //     0x150994c: ldr             x16, [x16, #0x9d0]
    // 0x1509950: StoreField: r0->field_f = r16
    //     0x1509950: stur            w16, [x0, #0xf]
    // 0x1509954: ldur            x2, [fp, #-8]
    // 0x1509958: LoadField: r1 = r2->field_f
    //     0x1509958: ldur            w1, [x2, #0xf]
    // 0x150995c: DecompressPointer r1
    //     0x150995c: add             x1, x1, HEAP, lsl #32
    // 0x1509960: r0 = controller()
    //     0x1509960: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1509964: LoadField: r1 = r0->field_57
    //     0x1509964: ldur            w1, [x0, #0x57]
    // 0x1509968: DecompressPointer r1
    //     0x1509968: add             x1, x1, HEAP, lsl #32
    // 0x150996c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x150996c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1509970: r0 = toList()
    //     0x1509970: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x1509974: LoadField: r1 = r0->field_b
    //     0x1509974: ldur            w1, [x0, #0xb]
    // 0x1509978: ldur            x0, [fp, #-0x10]
    // 0x150997c: StoreField: r0->field_13 = r1
    //     0x150997c: stur            w1, [x0, #0x13]
    // 0x1509980: r16 = " out of "
    //     0x1509980: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9d8] " out of "
    //     0x1509984: ldr             x16, [x16, #0x9d8]
    // 0x1509988: ArrayStore: r0[0] = r16  ; List_4
    //     0x1509988: stur            w16, [x0, #0x17]
    // 0x150998c: ldur            x2, [fp, #-8]
    // 0x1509990: LoadField: r1 = r2->field_f
    //     0x1509990: ldur            w1, [x2, #0xf]
    // 0x1509994: DecompressPointer r1
    //     0x1509994: add             x1, x1, HEAP, lsl #32
    // 0x1509998: r0 = controller()
    //     0x1509998: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x150999c: mov             x1, x0
    // 0x15099a0: r0 = headerConfigData()
    //     0x15099a0: bl              #0x8a3724  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_address_controller.dart] CheckoutRequestAddressController::headerConfigData
    // 0x15099a4: LoadField: r1 = r0->field_b
    //     0x15099a4: ldur            w1, [x0, #0xb]
    // 0x15099a8: DecompressPointer r1
    //     0x15099a8: add             x1, x1, HEAP, lsl #32
    // 0x15099ac: cmp             w1, NULL
    // 0x15099b0: b.ne            #0x15099bc
    // 0x15099b4: r0 = Null
    //     0x15099b4: mov             x0, NULL
    // 0x15099b8: b               #0x15099e0
    // 0x15099bc: LoadField: r0 = r1->field_b
    //     0x15099bc: ldur            w0, [x1, #0xb]
    // 0x15099c0: DecompressPointer r0
    //     0x15099c0: add             x0, x0, HEAP, lsl #32
    // 0x15099c4: cmp             w0, NULL
    // 0x15099c8: b.ne            #0x15099d4
    // 0x15099cc: r0 = Null
    //     0x15099cc: mov             x0, NULL
    // 0x15099d0: b               #0x15099e0
    // 0x15099d4: LoadField: r1 = r0->field_f
    //     0x15099d4: ldur            w1, [x0, #0xf]
    // 0x15099d8: DecompressPointer r1
    //     0x15099d8: add             x1, x1, HEAP, lsl #32
    // 0x15099dc: mov             x0, x1
    // 0x15099e0: ldur            x3, [fp, #-8]
    // 0x15099e4: ldur            x6, [fp, #-0x18]
    // 0x15099e8: ldur            x5, [fp, #-0x20]
    // 0x15099ec: ldur            x4, [fp, #-0x28]
    // 0x15099f0: ldur            x2, [fp, #-0x10]
    // 0x15099f4: mov             x1, x2
    // 0x15099f8: ArrayStore: r1[3] = r0  ; List_4
    //     0x15099f8: add             x25, x1, #0x1b
    //     0x15099fc: str             w0, [x25]
    //     0x1509a00: tbz             w0, #0, #0x1509a1c
    //     0x1509a04: ldurb           w16, [x1, #-1]
    //     0x1509a08: ldurb           w17, [x0, #-1]
    //     0x1509a0c: and             x16, x17, x16, lsr #2
    //     0x1509a10: tst             x16, HEAP, lsr #32
    //     0x1509a14: b.eq            #0x1509a1c
    //     0x1509a18: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1509a1c: r16 = " testimonials"
    //     0x1509a1c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e0] " testimonials"
    //     0x1509a20: ldr             x16, [x16, #0x9e0]
    // 0x1509a24: StoreField: r2->field_1f = r16
    //     0x1509a24: stur            w16, [x2, #0x1f]
    // 0x1509a28: str             x2, [SP]
    // 0x1509a2c: r0 = _interpolate()
    //     0x1509a2c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1509a30: mov             x2, x0
    // 0x1509a34: ldur            x0, [fp, #-8]
    // 0x1509a38: stur            x2, [fp, #-0x10]
    // 0x1509a3c: LoadField: r1 = r0->field_13
    //     0x1509a3c: ldur            w1, [x0, #0x13]
    // 0x1509a40: DecompressPointer r1
    //     0x1509a40: add             x1, x1, HEAP, lsl #32
    // 0x1509a44: r0 = of()
    //     0x1509a44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1509a48: LoadField: r1 = r0->field_87
    //     0x1509a48: ldur            w1, [x0, #0x87]
    // 0x1509a4c: DecompressPointer r1
    //     0x1509a4c: add             x1, x1, HEAP, lsl #32
    // 0x1509a50: LoadField: r0 = r1->field_2b
    //     0x1509a50: ldur            w0, [x1, #0x2b]
    // 0x1509a54: DecompressPointer r0
    //     0x1509a54: add             x0, x0, HEAP, lsl #32
    // 0x1509a58: stur            x0, [fp, #-0x30]
    // 0x1509a5c: r1 = Instance_Color
    //     0x1509a5c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1509a60: d0 = 0.700000
    //     0x1509a60: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1509a64: ldr             d0, [x17, #0xf48]
    // 0x1509a68: r0 = withOpacity()
    //     0x1509a68: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1509a6c: r16 = 12.000000
    //     0x1509a6c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1509a70: ldr             x16, [x16, #0x9e8]
    // 0x1509a74: stp             x16, x0, [SP]
    // 0x1509a78: ldur            x1, [fp, #-0x30]
    // 0x1509a7c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1509a7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1509a80: ldr             x4, [x4, #0x9b8]
    // 0x1509a84: r0 = copyWith()
    //     0x1509a84: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1509a88: stur            x0, [fp, #-0x30]
    // 0x1509a8c: r0 = Text()
    //     0x1509a8c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1509a90: mov             x3, x0
    // 0x1509a94: ldur            x0, [fp, #-0x10]
    // 0x1509a98: stur            x3, [fp, #-0x38]
    // 0x1509a9c: StoreField: r3->field_b = r0
    //     0x1509a9c: stur            w0, [x3, #0xb]
    // 0x1509aa0: ldur            x0, [fp, #-0x30]
    // 0x1509aa4: StoreField: r3->field_13 = r0
    //     0x1509aa4: stur            w0, [x3, #0x13]
    // 0x1509aa8: r1 = Null
    //     0x1509aa8: mov             x1, NULL
    // 0x1509aac: r2 = 10
    //     0x1509aac: movz            x2, #0xa
    // 0x1509ab0: r0 = AllocateArray()
    //     0x1509ab0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1509ab4: mov             x2, x0
    // 0x1509ab8: ldur            x0, [fp, #-0x18]
    // 0x1509abc: stur            x2, [fp, #-0x10]
    // 0x1509ac0: StoreField: r2->field_f = r0
    //     0x1509ac0: stur            w0, [x2, #0xf]
    // 0x1509ac4: ldur            x0, [fp, #-0x20]
    // 0x1509ac8: StoreField: r2->field_13 = r0
    //     0x1509ac8: stur            w0, [x2, #0x13]
    // 0x1509acc: ldur            x0, [fp, #-0x28]
    // 0x1509ad0: ArrayStore: r2[0] = r0  ; List_4
    //     0x1509ad0: stur            w0, [x2, #0x17]
    // 0x1509ad4: ldur            x0, [fp, #-0x38]
    // 0x1509ad8: StoreField: r2->field_1b = r0
    //     0x1509ad8: stur            w0, [x2, #0x1b]
    // 0x1509adc: r16 = Instance_SizedBox
    //     0x1509adc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x1509ae0: ldr             x16, [x16, #0x9f0]
    // 0x1509ae4: StoreField: r2->field_1f = r16
    //     0x1509ae4: stur            w16, [x2, #0x1f]
    // 0x1509ae8: r1 = <Widget>
    //     0x1509ae8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1509aec: r0 = AllocateGrowableArray()
    //     0x1509aec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1509af0: mov             x2, x0
    // 0x1509af4: ldur            x0, [fp, #-0x10]
    // 0x1509af8: stur            x2, [fp, #-0x18]
    // 0x1509afc: StoreField: r2->field_f = r0
    //     0x1509afc: stur            w0, [x2, #0xf]
    // 0x1509b00: r0 = 10
    //     0x1509b00: movz            x0, #0xa
    // 0x1509b04: StoreField: r2->field_b = r0
    //     0x1509b04: stur            w0, [x2, #0xb]
    // 0x1509b08: ldur            x0, [fp, #-8]
    // 0x1509b0c: LoadField: r1 = r0->field_f
    //     0x1509b0c: ldur            w1, [x0, #0xf]
    // 0x1509b10: DecompressPointer r1
    //     0x1509b10: add             x1, x1, HEAP, lsl #32
    // 0x1509b14: r0 = controller()
    //     0x1509b14: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1509b18: LoadField: r1 = r0->field_67
    //     0x1509b18: ldur            w1, [x0, #0x67]
    // 0x1509b1c: DecompressPointer r1
    //     0x1509b1c: add             x1, x1, HEAP, lsl #32
    // 0x1509b20: r0 = LoadClassIdInstr(r1)
    //     0x1509b20: ldur            x0, [x1, #-1]
    //     0x1509b24: ubfx            x0, x0, #0xc, #0x14
    // 0x1509b28: r16 = "testimonial"
    //     0x1509b28: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f8] "testimonial"
    //     0x1509b2c: ldr             x16, [x16, #0x9f8]
    // 0x1509b30: stp             x16, x1, [SP]
    // 0x1509b34: mov             lr, x0
    // 0x1509b38: ldr             lr, [x21, lr, lsl #3]
    // 0x1509b3c: blr             lr
    // 0x1509b40: tbnz            w0, #4, #0x1509c3c
    // 0x1509b44: ldur            x2, [fp, #-8]
    // 0x1509b48: ldur            x0, [fp, #-0x18]
    // 0x1509b4c: LoadField: r1 = r2->field_f
    //     0x1509b4c: ldur            w1, [x2, #0xf]
    // 0x1509b50: DecompressPointer r1
    //     0x1509b50: add             x1, x1, HEAP, lsl #32
    // 0x1509b54: r0 = controller()
    //     0x1509b54: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1509b58: LoadField: r1 = r0->field_57
    //     0x1509b58: ldur            w1, [x0, #0x57]
    // 0x1509b5c: DecompressPointer r1
    //     0x1509b5c: add             x1, x1, HEAP, lsl #32
    // 0x1509b60: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1509b60: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1509b64: r0 = toList()
    //     0x1509b64: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x1509b68: r1 = Instance_Color
    //     0x1509b68: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1509b6c: d0 = 0.100000
    //     0x1509b6c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x1509b70: stur            x0, [fp, #-0x10]
    // 0x1509b74: r0 = withOpacity()
    //     0x1509b74: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1509b78: mov             x2, x0
    // 0x1509b7c: r1 = Null
    //     0x1509b7c: mov             x1, NULL
    // 0x1509b80: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x1509b80: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x1509b84: r0 = Border.all()
    //     0x1509b84: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x1509b88: stur            x0, [fp, #-0x20]
    // 0x1509b8c: r0 = TestimonialViewAll()
    //     0x1509b8c: bl              #0x147c4c8  ; AllocateTestimonialViewAllStub -> TestimonialViewAll (size=0x1c)
    // 0x1509b90: mov             x2, x0
    // 0x1509b94: ldur            x0, [fp, #-0x10]
    // 0x1509b98: stur            x2, [fp, #-0x28]
    // 0x1509b9c: StoreField: r2->field_b = r0
    //     0x1509b9c: stur            w0, [x2, #0xb]
    // 0x1509ba0: r0 = Instance_BorderRadius
    //     0x1509ba0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x1509ba4: ldr             x0, [x0, #0xf70]
    // 0x1509ba8: StoreField: r2->field_f = r0
    //     0x1509ba8: stur            w0, [x2, #0xf]
    // 0x1509bac: ldur            x0, [fp, #-0x20]
    // 0x1509bb0: StoreField: r2->field_13 = r0
    //     0x1509bb0: stur            w0, [x2, #0x13]
    // 0x1509bb4: r0 = Instance_Color
    //     0x1509bb4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x1509bb8: ldr             x0, [x0, #0xf88]
    // 0x1509bbc: ArrayStore: r2[0] = r0  ; List_4
    //     0x1509bbc: stur            w0, [x2, #0x17]
    // 0x1509bc0: ldur            x0, [fp, #-0x18]
    // 0x1509bc4: LoadField: r1 = r0->field_b
    //     0x1509bc4: ldur            w1, [x0, #0xb]
    // 0x1509bc8: LoadField: r3 = r0->field_f
    //     0x1509bc8: ldur            w3, [x0, #0xf]
    // 0x1509bcc: DecompressPointer r3
    //     0x1509bcc: add             x3, x3, HEAP, lsl #32
    // 0x1509bd0: LoadField: r4 = r3->field_b
    //     0x1509bd0: ldur            w4, [x3, #0xb]
    // 0x1509bd4: r3 = LoadInt32Instr(r1)
    //     0x1509bd4: sbfx            x3, x1, #1, #0x1f
    // 0x1509bd8: stur            x3, [fp, #-0x40]
    // 0x1509bdc: r1 = LoadInt32Instr(r4)
    //     0x1509bdc: sbfx            x1, x4, #1, #0x1f
    // 0x1509be0: cmp             x3, x1
    // 0x1509be4: b.ne            #0x1509bf0
    // 0x1509be8: mov             x1, x0
    // 0x1509bec: r0 = _growToNextCapacity()
    //     0x1509bec: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1509bf0: ldur            x2, [fp, #-0x18]
    // 0x1509bf4: ldur            x3, [fp, #-0x40]
    // 0x1509bf8: add             x0, x3, #1
    // 0x1509bfc: lsl             x1, x0, #1
    // 0x1509c00: StoreField: r2->field_b = r1
    //     0x1509c00: stur            w1, [x2, #0xb]
    // 0x1509c04: LoadField: r1 = r2->field_f
    //     0x1509c04: ldur            w1, [x2, #0xf]
    // 0x1509c08: DecompressPointer r1
    //     0x1509c08: add             x1, x1, HEAP, lsl #32
    // 0x1509c0c: ldur            x0, [fp, #-0x28]
    // 0x1509c10: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1509c10: add             x25, x1, x3, lsl #2
    //     0x1509c14: add             x25, x25, #0xf
    //     0x1509c18: str             w0, [x25]
    //     0x1509c1c: tbz             w0, #0, #0x1509c38
    //     0x1509c20: ldurb           w16, [x1, #-1]
    //     0x1509c24: ldurb           w17, [x0, #-1]
    //     0x1509c28: and             x16, x17, x16, lsr #2
    //     0x1509c2c: tst             x16, HEAP, lsr #32
    //     0x1509c30: b.eq            #0x1509c38
    //     0x1509c34: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1509c38: b               #0x1509c40
    // 0x1509c3c: ldur            x2, [fp, #-0x18]
    // 0x1509c40: ldur            x0, [fp, #-8]
    // 0x1509c44: LoadField: r1 = r0->field_f
    //     0x1509c44: ldur            w1, [x0, #0xf]
    // 0x1509c48: DecompressPointer r1
    //     0x1509c48: add             x1, x1, HEAP, lsl #32
    // 0x1509c4c: r0 = controller()
    //     0x1509c4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1509c50: LoadField: r1 = r0->field_67
    //     0x1509c50: ldur            w1, [x0, #0x67]
    // 0x1509c54: DecompressPointer r1
    //     0x1509c54: add             x1, x1, HEAP, lsl #32
    // 0x1509c58: r0 = LoadClassIdInstr(r1)
    //     0x1509c58: ldur            x0, [x1, #-1]
    //     0x1509c5c: ubfx            x0, x0, #0xc, #0x14
    // 0x1509c60: r16 = "product_testimonial"
    //     0x1509c60: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea00] "product_testimonial"
    //     0x1509c64: ldr             x16, [x16, #0xa00]
    // 0x1509c68: stp             x16, x1, [SP]
    // 0x1509c6c: mov             lr, x0
    // 0x1509c70: ldr             lr, [x21, lr, lsl #3]
    // 0x1509c74: blr             lr
    // 0x1509c78: tbnz            w0, #4, #0x1509d40
    // 0x1509c7c: ldur            x1, [fp, #-8]
    // 0x1509c80: ldur            x0, [fp, #-0x18]
    // 0x1509c84: LoadField: r2 = r1->field_f
    //     0x1509c84: ldur            w2, [x1, #0xf]
    // 0x1509c88: DecompressPointer r2
    //     0x1509c88: add             x2, x2, HEAP, lsl #32
    // 0x1509c8c: mov             x1, x2
    // 0x1509c90: r0 = controller()
    //     0x1509c90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1509c94: mov             x1, x0
    // 0x1509c98: r0 = widgetsList()
    //     0x1509c98: bl              #0x9be300  ; [package:customer_app/app/presentation/controllers/post_order/order_success_controller.dart] OrderSuccessController::widgetsList
    // 0x1509c9c: stur            x0, [fp, #-8]
    // 0x1509ca0: r0 = ProductTestimonialViewAll()
    //     0x1509ca0: bl              #0x147c4bc  ; AllocateProductTestimonialViewAllStub -> ProductTestimonialViewAll (size=0x1c)
    // 0x1509ca4: mov             x2, x0
    // 0x1509ca8: ldur            x0, [fp, #-8]
    // 0x1509cac: stur            x2, [fp, #-0x10]
    // 0x1509cb0: StoreField: r2->field_b = r0
    //     0x1509cb0: stur            w0, [x2, #0xb]
    // 0x1509cb4: StoreField: r2->field_f = rZR
    //     0x1509cb4: stur            xzr, [x2, #0xf]
    // 0x1509cb8: r0 = Instance_Color
    //     0x1509cb8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x1509cbc: ldr             x0, [x0, #0x90]
    // 0x1509cc0: ArrayStore: r2[0] = r0  ; List_4
    //     0x1509cc0: stur            w0, [x2, #0x17]
    // 0x1509cc4: ldur            x0, [fp, #-0x18]
    // 0x1509cc8: LoadField: r1 = r0->field_b
    //     0x1509cc8: ldur            w1, [x0, #0xb]
    // 0x1509ccc: LoadField: r3 = r0->field_f
    //     0x1509ccc: ldur            w3, [x0, #0xf]
    // 0x1509cd0: DecompressPointer r3
    //     0x1509cd0: add             x3, x3, HEAP, lsl #32
    // 0x1509cd4: LoadField: r4 = r3->field_b
    //     0x1509cd4: ldur            w4, [x3, #0xb]
    // 0x1509cd8: r3 = LoadInt32Instr(r1)
    //     0x1509cd8: sbfx            x3, x1, #1, #0x1f
    // 0x1509cdc: stur            x3, [fp, #-0x40]
    // 0x1509ce0: r1 = LoadInt32Instr(r4)
    //     0x1509ce0: sbfx            x1, x4, #1, #0x1f
    // 0x1509ce4: cmp             x3, x1
    // 0x1509ce8: b.ne            #0x1509cf4
    // 0x1509cec: mov             x1, x0
    // 0x1509cf0: r0 = _growToNextCapacity()
    //     0x1509cf0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1509cf4: ldur            x2, [fp, #-0x18]
    // 0x1509cf8: ldur            x3, [fp, #-0x40]
    // 0x1509cfc: add             x0, x3, #1
    // 0x1509d00: lsl             x1, x0, #1
    // 0x1509d04: StoreField: r2->field_b = r1
    //     0x1509d04: stur            w1, [x2, #0xb]
    // 0x1509d08: LoadField: r1 = r2->field_f
    //     0x1509d08: ldur            w1, [x2, #0xf]
    // 0x1509d0c: DecompressPointer r1
    //     0x1509d0c: add             x1, x1, HEAP, lsl #32
    // 0x1509d10: ldur            x0, [fp, #-0x10]
    // 0x1509d14: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1509d14: add             x25, x1, x3, lsl #2
    //     0x1509d18: add             x25, x25, #0xf
    //     0x1509d1c: str             w0, [x25]
    //     0x1509d20: tbz             w0, #0, #0x1509d3c
    //     0x1509d24: ldurb           w16, [x1, #-1]
    //     0x1509d28: ldurb           w17, [x0, #-1]
    //     0x1509d2c: and             x16, x17, x16, lsr #2
    //     0x1509d30: tst             x16, HEAP, lsr #32
    //     0x1509d34: b.eq            #0x1509d3c
    //     0x1509d38: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1509d3c: b               #0x1509d44
    // 0x1509d40: ldur            x2, [fp, #-0x18]
    // 0x1509d44: r0 = Column()
    //     0x1509d44: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1509d48: r1 = Instance_Axis
    //     0x1509d48: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1509d4c: StoreField: r0->field_f = r1
    //     0x1509d4c: stur            w1, [x0, #0xf]
    // 0x1509d50: r1 = Instance_MainAxisAlignment
    //     0x1509d50: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1509d54: ldr             x1, [x1, #0xa08]
    // 0x1509d58: StoreField: r0->field_13 = r1
    //     0x1509d58: stur            w1, [x0, #0x13]
    // 0x1509d5c: r1 = Instance_MainAxisSize
    //     0x1509d5c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1509d60: ldr             x1, [x1, #0xa10]
    // 0x1509d64: ArrayStore: r0[0] = r1  ; List_4
    //     0x1509d64: stur            w1, [x0, #0x17]
    // 0x1509d68: r1 = Instance_CrossAxisAlignment
    //     0x1509d68: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1509d6c: ldr             x1, [x1, #0xa18]
    // 0x1509d70: StoreField: r0->field_1b = r1
    //     0x1509d70: stur            w1, [x0, #0x1b]
    // 0x1509d74: r1 = Instance_VerticalDirection
    //     0x1509d74: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1509d78: ldr             x1, [x1, #0xa20]
    // 0x1509d7c: StoreField: r0->field_23 = r1
    //     0x1509d7c: stur            w1, [x0, #0x23]
    // 0x1509d80: r1 = Instance_Clip
    //     0x1509d80: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1509d84: ldr             x1, [x1, #0x38]
    // 0x1509d88: StoreField: r0->field_2b = r1
    //     0x1509d88: stur            w1, [x0, #0x2b]
    // 0x1509d8c: StoreField: r0->field_2f = rZR
    //     0x1509d8c: stur            xzr, [x0, #0x2f]
    // 0x1509d90: ldur            x1, [fp, #-0x18]
    // 0x1509d94: StoreField: r0->field_b = r1
    //     0x1509d94: stur            w1, [x0, #0xb]
    // 0x1509d98: LeaveFrame
    //     0x1509d98: mov             SP, fp
    //     0x1509d9c: ldp             fp, lr, [SP], #0x10
    // 0x1509da0: ret
    //     0x1509da0: ret             
    // 0x1509da4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1509da4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1509da8: b               #0x1509800
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15d7f78, size: 0x9c
    // 0x15d7f78: EnterFrame
    //     0x15d7f78: stp             fp, lr, [SP, #-0x10]!
    //     0x15d7f7c: mov             fp, SP
    // 0x15d7f80: AllocStack(0x18)
    //     0x15d7f80: sub             SP, SP, #0x18
    // 0x15d7f84: CheckStackOverflow
    //     0x15d7f84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d7f88: cmp             SP, x16
    //     0x15d7f8c: b.ls            #0x15d800c
    // 0x15d7f90: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15d7f90: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15d7f94: ldr             x0, [x0, #0x1c80]
    //     0x15d7f98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15d7f9c: cmp             w0, w16
    //     0x15d7fa0: b.ne            #0x15d7fac
    //     0x15d7fa4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15d7fa8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15d7fac: r1 = Null
    //     0x15d7fac: mov             x1, NULL
    // 0x15d7fb0: r2 = 4
    //     0x15d7fb0: movz            x2, #0x4
    // 0x15d7fb4: r0 = AllocateArray()
    //     0x15d7fb4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15d7fb8: r16 = "previousScreenSource"
    //     0x15d7fb8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x15d7fbc: ldr             x16, [x16, #0x448]
    // 0x15d7fc0: StoreField: r0->field_f = r16
    //     0x15d7fc0: stur            w16, [x0, #0xf]
    // 0x15d7fc4: r16 = "testimonial_page"
    //     0x15d7fc4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea80] "testimonial_page"
    //     0x15d7fc8: ldr             x16, [x16, #0xa80]
    // 0x15d7fcc: StoreField: r0->field_13 = r16
    //     0x15d7fcc: stur            w16, [x0, #0x13]
    // 0x15d7fd0: r16 = <String, String>
    //     0x15d7fd0: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x15d7fd4: ldr             x16, [x16, #0x788]
    // 0x15d7fd8: stp             x0, x16, [SP]
    // 0x15d7fdc: r0 = Map._fromLiteral()
    //     0x15d7fdc: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x15d7fe0: r16 = "/bag"
    //     0x15d7fe0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x15d7fe4: ldr             x16, [x16, #0x468]
    // 0x15d7fe8: stp             x16, NULL, [SP, #8]
    // 0x15d7fec: str             x0, [SP]
    // 0x15d7ff0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x15d7ff0: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x15d7ff4: ldr             x4, [x4, #0x438]
    // 0x15d7ff8: r0 = GetNavigation.toNamed()
    //     0x15d7ff8: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15d7ffc: r0 = Null
    //     0x15d7ffc: mov             x0, NULL
    // 0x15d8000: LeaveFrame
    //     0x15d8000: mov             SP, fp
    //     0x15d8004: ldp             fp, lr, [SP], #0x10
    // 0x15d8008: ret
    //     0x15d8008: ret             
    // 0x15d800c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d800c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d8010: b               #0x15d7f90
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15d8014, size: 0x2fc
    // 0x15d8014: EnterFrame
    //     0x15d8014: stp             fp, lr, [SP, #-0x10]!
    //     0x15d8018: mov             fp, SP
    // 0x15d801c: AllocStack(0x58)
    //     0x15d801c: sub             SP, SP, #0x58
    // 0x15d8020: SetupParameters()
    //     0x15d8020: ldr             x0, [fp, #0x10]
    //     0x15d8024: ldur            w2, [x0, #0x17]
    //     0x15d8028: add             x2, x2, HEAP, lsl #32
    //     0x15d802c: stur            x2, [fp, #-8]
    // 0x15d8030: CheckStackOverflow
    //     0x15d8030: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d8034: cmp             SP, x16
    //     0x15d8038: b.ls            #0x15d8308
    // 0x15d803c: LoadField: r1 = r2->field_f
    //     0x15d803c: ldur            w1, [x2, #0xf]
    // 0x15d8040: DecompressPointer r1
    //     0x15d8040: add             x1, x1, HEAP, lsl #32
    // 0x15d8044: r0 = controller()
    //     0x15d8044: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d8048: mov             x1, x0
    // 0x15d804c: r0 = appConfigResponse()
    //     0x15d804c: bl              #0x933060  ; [package:customer_app/app/config_controller.dart] ConfigController::appConfigResponse
    // 0x15d8050: LoadField: r1 = r0->field_1f
    //     0x15d8050: ldur            w1, [x0, #0x1f]
    // 0x15d8054: DecompressPointer r1
    //     0x15d8054: add             x1, x1, HEAP, lsl #32
    // 0x15d8058: cmp             w1, NULL
    // 0x15d805c: b.ne            #0x15d8068
    // 0x15d8060: r0 = Null
    //     0x15d8060: mov             x0, NULL
    // 0x15d8064: b               #0x15d8070
    // 0x15d8068: LoadField: r0 = r1->field_7
    //     0x15d8068: ldur            w0, [x1, #7]
    // 0x15d806c: DecompressPointer r0
    //     0x15d806c: add             x0, x0, HEAP, lsl #32
    // 0x15d8070: cmp             w0, NULL
    // 0x15d8074: b.ne            #0x15d8080
    // 0x15d8078: r0 = false
    //     0x15d8078: add             x0, NULL, #0x30  ; false
    // 0x15d807c: b               #0x15d8270
    // 0x15d8080: tbnz            w0, #4, #0x15d826c
    // 0x15d8084: ldur            x0, [fp, #-8]
    // 0x15d8088: LoadField: r1 = r0->field_f
    //     0x15d8088: ldur            w1, [x0, #0xf]
    // 0x15d808c: DecompressPointer r1
    //     0x15d808c: add             x1, x1, HEAP, lsl #32
    // 0x15d8090: r0 = controller()
    //     0x15d8090: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d8094: mov             x1, x0
    // 0x15d8098: r0 = activeExchangeResponse()
    //     0x15d8098: bl              #0x91ea4c  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::activeExchangeResponse
    // 0x15d809c: mov             x2, x0
    // 0x15d80a0: ldur            x0, [fp, #-8]
    // 0x15d80a4: stur            x2, [fp, #-0x10]
    // 0x15d80a8: LoadField: r1 = r0->field_13
    //     0x15d80a8: ldur            w1, [x0, #0x13]
    // 0x15d80ac: DecompressPointer r1
    //     0x15d80ac: add             x1, x1, HEAP, lsl #32
    // 0x15d80b0: r0 = of()
    //     0x15d80b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d80b4: LoadField: r2 = r0->field_5b
    //     0x15d80b4: ldur            w2, [x0, #0x5b]
    // 0x15d80b8: DecompressPointer r2
    //     0x15d80b8: add             x2, x2, HEAP, lsl #32
    // 0x15d80bc: ldur            x0, [fp, #-8]
    // 0x15d80c0: stur            x2, [fp, #-0x18]
    // 0x15d80c4: LoadField: r1 = r0->field_f
    //     0x15d80c4: ldur            w1, [x0, #0xf]
    // 0x15d80c8: DecompressPointer r1
    //     0x15d80c8: add             x1, x1, HEAP, lsl #32
    // 0x15d80cc: r0 = controller()
    //     0x15d80cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d80d0: LoadField: r1 = r0->field_73
    //     0x15d80d0: ldur            w1, [x0, #0x73]
    // 0x15d80d4: DecompressPointer r1
    //     0x15d80d4: add             x1, x1, HEAP, lsl #32
    // 0x15d80d8: r0 = value()
    //     0x15d80d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d80dc: cmp             w0, NULL
    // 0x15d80e0: r16 = true
    //     0x15d80e0: add             x16, NULL, #0x20  ; true
    // 0x15d80e4: r17 = false
    //     0x15d80e4: add             x17, NULL, #0x30  ; false
    // 0x15d80e8: csel            x2, x16, x17, ne
    // 0x15d80ec: ldur            x0, [fp, #-8]
    // 0x15d80f0: stur            x2, [fp, #-0x20]
    // 0x15d80f4: LoadField: r1 = r0->field_f
    //     0x15d80f4: ldur            w1, [x0, #0xf]
    // 0x15d80f8: DecompressPointer r1
    //     0x15d80f8: add             x1, x1, HEAP, lsl #32
    // 0x15d80fc: r0 = controller()
    //     0x15d80fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d8100: LoadField: r1 = r0->field_73
    //     0x15d8100: ldur            w1, [x0, #0x73]
    // 0x15d8104: DecompressPointer r1
    //     0x15d8104: add             x1, x1, HEAP, lsl #32
    // 0x15d8108: r0 = value()
    //     0x15d8108: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d810c: str             x0, [SP]
    // 0x15d8110: r0 = _interpolateSingle()
    //     0x15d8110: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15d8114: mov             x2, x0
    // 0x15d8118: ldur            x0, [fp, #-8]
    // 0x15d811c: stur            x2, [fp, #-0x28]
    // 0x15d8120: LoadField: r1 = r0->field_13
    //     0x15d8120: ldur            w1, [x0, #0x13]
    // 0x15d8124: DecompressPointer r1
    //     0x15d8124: add             x1, x1, HEAP, lsl #32
    // 0x15d8128: r0 = of()
    //     0x15d8128: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d812c: LoadField: r1 = r0->field_87
    //     0x15d812c: ldur            w1, [x0, #0x87]
    // 0x15d8130: DecompressPointer r1
    //     0x15d8130: add             x1, x1, HEAP, lsl #32
    // 0x15d8134: LoadField: r0 = r1->field_27
    //     0x15d8134: ldur            w0, [x1, #0x27]
    // 0x15d8138: DecompressPointer r0
    //     0x15d8138: add             x0, x0, HEAP, lsl #32
    // 0x15d813c: r16 = Instance_Color
    //     0x15d813c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15d8140: str             x16, [SP]
    // 0x15d8144: mov             x1, x0
    // 0x15d8148: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15d8148: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15d814c: ldr             x4, [x4, #0xf40]
    // 0x15d8150: r0 = copyWith()
    //     0x15d8150: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15d8154: stur            x0, [fp, #-0x30]
    // 0x15d8158: r0 = Text()
    //     0x15d8158: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15d815c: mov             x2, x0
    // 0x15d8160: ldur            x0, [fp, #-0x28]
    // 0x15d8164: stur            x2, [fp, #-0x38]
    // 0x15d8168: StoreField: r2->field_b = r0
    //     0x15d8168: stur            w0, [x2, #0xb]
    // 0x15d816c: ldur            x0, [fp, #-0x30]
    // 0x15d8170: StoreField: r2->field_13 = r0
    //     0x15d8170: stur            w0, [x2, #0x13]
    // 0x15d8174: ldur            x0, [fp, #-8]
    // 0x15d8178: LoadField: r1 = r0->field_13
    //     0x15d8178: ldur            w1, [x0, #0x13]
    // 0x15d817c: DecompressPointer r1
    //     0x15d817c: add             x1, x1, HEAP, lsl #32
    // 0x15d8180: r0 = of()
    //     0x15d8180: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d8184: LoadField: r1 = r0->field_5b
    //     0x15d8184: ldur            w1, [x0, #0x5b]
    // 0x15d8188: DecompressPointer r1
    //     0x15d8188: add             x1, x1, HEAP, lsl #32
    // 0x15d818c: stur            x1, [fp, #-8]
    // 0x15d8190: r0 = ColorFilter()
    //     0x15d8190: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d8194: mov             x1, x0
    // 0x15d8198: ldur            x0, [fp, #-8]
    // 0x15d819c: stur            x1, [fp, #-0x28]
    // 0x15d81a0: StoreField: r1->field_7 = r0
    //     0x15d81a0: stur            w0, [x1, #7]
    // 0x15d81a4: r0 = Instance_BlendMode
    //     0x15d81a4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d81a8: ldr             x0, [x0, #0xb30]
    // 0x15d81ac: StoreField: r1->field_b = r0
    //     0x15d81ac: stur            w0, [x1, #0xb]
    // 0x15d81b0: r0 = 1
    //     0x15d81b0: movz            x0, #0x1
    // 0x15d81b4: StoreField: r1->field_13 = r0
    //     0x15d81b4: stur            x0, [x1, #0x13]
    // 0x15d81b8: r0 = SvgPicture()
    //     0x15d81b8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d81bc: stur            x0, [fp, #-8]
    // 0x15d81c0: r16 = Instance_BoxFit
    //     0x15d81c0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15d81c4: ldr             x16, [x16, #0xb18]
    // 0x15d81c8: r30 = 24.000000
    //     0x15d81c8: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15d81cc: ldr             lr, [lr, #0xba8]
    // 0x15d81d0: stp             lr, x16, [SP, #0x10]
    // 0x15d81d4: r16 = 24.000000
    //     0x15d81d4: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15d81d8: ldr             x16, [x16, #0xba8]
    // 0x15d81dc: ldur            lr, [fp, #-0x28]
    // 0x15d81e0: stp             lr, x16, [SP]
    // 0x15d81e4: mov             x1, x0
    // 0x15d81e8: r2 = "assets/images/shopping_bag.svg"
    //     0x15d81e8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15d81ec: ldr             x2, [x2, #0xa60]
    // 0x15d81f0: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15d81f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15d81f4: ldr             x4, [x4, #0xa68]
    // 0x15d81f8: r0 = SvgPicture.asset()
    //     0x15d81f8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d81fc: r0 = Badge()
    //     0x15d81fc: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15d8200: mov             x1, x0
    // 0x15d8204: ldur            x0, [fp, #-0x18]
    // 0x15d8208: stur            x1, [fp, #-0x28]
    // 0x15d820c: StoreField: r1->field_b = r0
    //     0x15d820c: stur            w0, [x1, #0xb]
    // 0x15d8210: ldur            x0, [fp, #-0x38]
    // 0x15d8214: StoreField: r1->field_27 = r0
    //     0x15d8214: stur            w0, [x1, #0x27]
    // 0x15d8218: ldur            x0, [fp, #-0x20]
    // 0x15d821c: StoreField: r1->field_2b = r0
    //     0x15d821c: stur            w0, [x1, #0x2b]
    // 0x15d8220: ldur            x0, [fp, #-8]
    // 0x15d8224: StoreField: r1->field_2f = r0
    //     0x15d8224: stur            w0, [x1, #0x2f]
    // 0x15d8228: r0 = Visibility()
    //     0x15d8228: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15d822c: mov             x1, x0
    // 0x15d8230: ldur            x0, [fp, #-0x28]
    // 0x15d8234: StoreField: r1->field_b = r0
    //     0x15d8234: stur            w0, [x1, #0xb]
    // 0x15d8238: r0 = Instance_SizedBox
    //     0x15d8238: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15d823c: StoreField: r1->field_f = r0
    //     0x15d823c: stur            w0, [x1, #0xf]
    // 0x15d8240: ldur            x0, [fp, #-0x10]
    // 0x15d8244: StoreField: r1->field_13 = r0
    //     0x15d8244: stur            w0, [x1, #0x13]
    // 0x15d8248: r0 = false
    //     0x15d8248: add             x0, NULL, #0x30  ; false
    // 0x15d824c: ArrayStore: r1[0] = r0  ; List_4
    //     0x15d824c: stur            w0, [x1, #0x17]
    // 0x15d8250: StoreField: r1->field_1b = r0
    //     0x15d8250: stur            w0, [x1, #0x1b]
    // 0x15d8254: StoreField: r1->field_1f = r0
    //     0x15d8254: stur            w0, [x1, #0x1f]
    // 0x15d8258: StoreField: r1->field_23 = r0
    //     0x15d8258: stur            w0, [x1, #0x23]
    // 0x15d825c: StoreField: r1->field_27 = r0
    //     0x15d825c: stur            w0, [x1, #0x27]
    // 0x15d8260: StoreField: r1->field_2b = r0
    //     0x15d8260: stur            w0, [x1, #0x2b]
    // 0x15d8264: mov             x0, x1
    // 0x15d8268: b               #0x15d8288
    // 0x15d826c: r0 = false
    //     0x15d826c: add             x0, NULL, #0x30  ; false
    // 0x15d8270: r0 = Container()
    //     0x15d8270: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15d8274: mov             x1, x0
    // 0x15d8278: stur            x0, [fp, #-8]
    // 0x15d827c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15d827c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15d8280: r0 = Container()
    //     0x15d8280: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15d8284: ldur            x0, [fp, #-8]
    // 0x15d8288: stur            x0, [fp, #-8]
    // 0x15d828c: r0 = InkWell()
    //     0x15d828c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15d8290: mov             x3, x0
    // 0x15d8294: ldur            x0, [fp, #-8]
    // 0x15d8298: stur            x3, [fp, #-0x10]
    // 0x15d829c: StoreField: r3->field_b = r0
    //     0x15d829c: stur            w0, [x3, #0xb]
    // 0x15d82a0: r1 = Function '<anonymous closure>':.
    //     0x15d82a0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea70] AnonymousClosure: (0x15d7f78), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::appBar (0x15eea18)
    //     0x15d82a4: ldr             x1, [x1, #0xa70]
    // 0x15d82a8: r2 = Null
    //     0x15d82a8: mov             x2, NULL
    // 0x15d82ac: r0 = AllocateClosure()
    //     0x15d82ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d82b0: mov             x1, x0
    // 0x15d82b4: ldur            x0, [fp, #-0x10]
    // 0x15d82b8: StoreField: r0->field_f = r1
    //     0x15d82b8: stur            w1, [x0, #0xf]
    // 0x15d82bc: r1 = true
    //     0x15d82bc: add             x1, NULL, #0x20  ; true
    // 0x15d82c0: StoreField: r0->field_43 = r1
    //     0x15d82c0: stur            w1, [x0, #0x43]
    // 0x15d82c4: r2 = Instance_BoxShape
    //     0x15d82c4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15d82c8: ldr             x2, [x2, #0x80]
    // 0x15d82cc: StoreField: r0->field_47 = r2
    //     0x15d82cc: stur            w2, [x0, #0x47]
    // 0x15d82d0: StoreField: r0->field_6f = r1
    //     0x15d82d0: stur            w1, [x0, #0x6f]
    // 0x15d82d4: r2 = false
    //     0x15d82d4: add             x2, NULL, #0x30  ; false
    // 0x15d82d8: StoreField: r0->field_73 = r2
    //     0x15d82d8: stur            w2, [x0, #0x73]
    // 0x15d82dc: StoreField: r0->field_83 = r1
    //     0x15d82dc: stur            w1, [x0, #0x83]
    // 0x15d82e0: StoreField: r0->field_7b = r2
    //     0x15d82e0: stur            w2, [x0, #0x7b]
    // 0x15d82e4: r0 = Padding()
    //     0x15d82e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15d82e8: r1 = Instance_EdgeInsets
    //     0x15d82e8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea78] Obj!EdgeInsets@d5a0e1
    //     0x15d82ec: ldr             x1, [x1, #0xa78]
    // 0x15d82f0: StoreField: r0->field_f = r1
    //     0x15d82f0: stur            w1, [x0, #0xf]
    // 0x15d82f4: ldur            x1, [fp, #-0x10]
    // 0x15d82f8: StoreField: r0->field_b = r1
    //     0x15d82f8: stur            w1, [x0, #0xb]
    // 0x15d82fc: LeaveFrame
    //     0x15d82fc: mov             SP, fp
    //     0x15d8300: ldp             fp, lr, [SP], #0x10
    // 0x15d8304: ret
    //     0x15d8304: ret             
    // 0x15d8308: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d8308: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d830c: b               #0x15d803c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15d8310, size: 0xb8
    // 0x15d8310: EnterFrame
    //     0x15d8310: stp             fp, lr, [SP, #-0x10]!
    //     0x15d8314: mov             fp, SP
    // 0x15d8318: AllocStack(0x10)
    //     0x15d8318: sub             SP, SP, #0x10
    // 0x15d831c: SetupParameters()
    //     0x15d831c: ldr             x0, [fp, #0x10]
    //     0x15d8320: ldur            w1, [x0, #0x17]
    //     0x15d8324: add             x1, x1, HEAP, lsl #32
    // 0x15d8328: CheckStackOverflow
    //     0x15d8328: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d832c: cmp             SP, x16
    //     0x15d8330: b.ls            #0x15d83c0
    // 0x15d8334: LoadField: r0 = r1->field_f
    //     0x15d8334: ldur            w0, [x1, #0xf]
    // 0x15d8338: DecompressPointer r0
    //     0x15d8338: add             x0, x0, HEAP, lsl #32
    // 0x15d833c: mov             x1, x0
    // 0x15d8340: r0 = controller()
    //     0x15d8340: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d8344: LoadField: r1 = r0->field_6b
    //     0x15d8344: ldur            w1, [x0, #0x6b]
    // 0x15d8348: DecompressPointer r1
    //     0x15d8348: add             x1, x1, HEAP, lsl #32
    // 0x15d834c: r0 = value()
    //     0x15d834c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d8350: tbnz            w0, #4, #0x15d8388
    // 0x15d8354: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15d8354: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15d8358: ldr             x0, [x0, #0x1c80]
    //     0x15d835c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15d8360: cmp             w0, w16
    //     0x15d8364: b.ne            #0x15d8370
    //     0x15d8368: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15d836c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15d8370: r16 = "/search"
    //     0x15d8370: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15d8374: ldr             x16, [x16, #0x838]
    // 0x15d8378: stp             x16, NULL, [SP]
    // 0x15d837c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15d837c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15d8380: r0 = GetNavigation.toNamed()
    //     0x15d8380: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15d8384: b               #0x15d83b0
    // 0x15d8388: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15d8388: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15d838c: ldr             x0, [x0, #0x1c80]
    //     0x15d8390: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15d8394: cmp             w0, w16
    //     0x15d8398: b.ne            #0x15d83a4
    //     0x15d839c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15d83a0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15d83a4: str             NULL, [SP]
    // 0x15d83a8: r4 = const [0x1, 0, 0, 0, null]
    //     0x15d83a8: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x15d83ac: r0 = GetNavigation.back()
    //     0x15d83ac: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x15d83b0: r0 = Null
    //     0x15d83b0: mov             x0, NULL
    // 0x15d83b4: LeaveFrame
    //     0x15d83b4: mov             SP, fp
    //     0x15d83b8: ldp             fp, lr, [SP], #0x10
    // 0x15d83bc: ret
    //     0x15d83bc: ret             
    // 0x15d83c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d83c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d83c4: b               #0x15d8334
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15eea18, size: 0x2b0
    // 0x15eea18: EnterFrame
    //     0x15eea18: stp             fp, lr, [SP, #-0x10]!
    //     0x15eea1c: mov             fp, SP
    // 0x15eea20: AllocStack(0x30)
    //     0x15eea20: sub             SP, SP, #0x30
    // 0x15eea24: SetupParameters(TestimonialsView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15eea24: stur            x1, [fp, #-8]
    //     0x15eea28: stur            x2, [fp, #-0x10]
    // 0x15eea2c: CheckStackOverflow
    //     0x15eea2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15eea30: cmp             SP, x16
    //     0x15eea34: b.ls            #0x15eecc0
    // 0x15eea38: r1 = 2
    //     0x15eea38: movz            x1, #0x2
    // 0x15eea3c: r0 = AllocateContext()
    //     0x15eea3c: bl              #0x16f6108  ; AllocateContextStub
    // 0x15eea40: ldur            x1, [fp, #-8]
    // 0x15eea44: stur            x0, [fp, #-0x18]
    // 0x15eea48: StoreField: r0->field_f = r1
    //     0x15eea48: stur            w1, [x0, #0xf]
    // 0x15eea4c: ldur            x2, [fp, #-0x10]
    // 0x15eea50: StoreField: r0->field_13 = r2
    //     0x15eea50: stur            w2, [x0, #0x13]
    // 0x15eea54: r0 = Obx()
    //     0x15eea54: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15eea58: ldur            x2, [fp, #-0x18]
    // 0x15eea5c: r1 = Function '<anonymous closure>':.
    //     0x15eea5c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea28] AnonymousClosure: (0x15eecc8), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::appBar (0x15eea18)
    //     0x15eea60: ldr             x1, [x1, #0xa28]
    // 0x15eea64: stur            x0, [fp, #-0x10]
    // 0x15eea68: r0 = AllocateClosure()
    //     0x15eea68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15eea6c: mov             x1, x0
    // 0x15eea70: ldur            x0, [fp, #-0x10]
    // 0x15eea74: StoreField: r0->field_b = r1
    //     0x15eea74: stur            w1, [x0, #0xb]
    // 0x15eea78: ldur            x1, [fp, #-8]
    // 0x15eea7c: r0 = controller()
    //     0x15eea7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15eea80: mov             x1, x0
    // 0x15eea84: r0 = appliedCoupon()
    //     0x15eea84: bl              #0x913a0c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_payment_method_controller.dart] CheckoutPaymentMethodController::appliedCoupon
    // 0x15eea88: tbnz            w0, #4, #0x15eeb20
    // 0x15eea8c: ldur            x2, [fp, #-0x18]
    // 0x15eea90: LoadField: r1 = r2->field_13
    //     0x15eea90: ldur            w1, [x2, #0x13]
    // 0x15eea94: DecompressPointer r1
    //     0x15eea94: add             x1, x1, HEAP, lsl #32
    // 0x15eea98: r0 = of()
    //     0x15eea98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15eea9c: LoadField: r1 = r0->field_5b
    //     0x15eea9c: ldur            w1, [x0, #0x5b]
    // 0x15eeaa0: DecompressPointer r1
    //     0x15eeaa0: add             x1, x1, HEAP, lsl #32
    // 0x15eeaa4: stur            x1, [fp, #-8]
    // 0x15eeaa8: r0 = ColorFilter()
    //     0x15eeaa8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15eeaac: mov             x1, x0
    // 0x15eeab0: ldur            x0, [fp, #-8]
    // 0x15eeab4: stur            x1, [fp, #-0x20]
    // 0x15eeab8: StoreField: r1->field_7 = r0
    //     0x15eeab8: stur            w0, [x1, #7]
    // 0x15eeabc: r0 = Instance_BlendMode
    //     0x15eeabc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15eeac0: ldr             x0, [x0, #0xb30]
    // 0x15eeac4: StoreField: r1->field_b = r0
    //     0x15eeac4: stur            w0, [x1, #0xb]
    // 0x15eeac8: r2 = 1
    //     0x15eeac8: movz            x2, #0x1
    // 0x15eeacc: StoreField: r1->field_13 = r2
    //     0x15eeacc: stur            x2, [x1, #0x13]
    // 0x15eead0: r0 = SvgPicture()
    //     0x15eead0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15eead4: stur            x0, [fp, #-8]
    // 0x15eead8: ldur            x16, [fp, #-0x20]
    // 0x15eeadc: str             x16, [SP]
    // 0x15eeae0: mov             x1, x0
    // 0x15eeae4: r2 = "assets/images/search.svg"
    //     0x15eeae4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15eeae8: ldr             x2, [x2, #0xa30]
    // 0x15eeaec: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15eeaec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15eeaf0: ldr             x4, [x4, #0xa38]
    // 0x15eeaf4: r0 = SvgPicture.asset()
    //     0x15eeaf4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15eeaf8: r0 = Align()
    //     0x15eeaf8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15eeafc: r3 = Instance_Alignment
    //     0x15eeafc: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15eeb00: ldr             x3, [x3, #0xb10]
    // 0x15eeb04: StoreField: r0->field_f = r3
    //     0x15eeb04: stur            w3, [x0, #0xf]
    // 0x15eeb08: r4 = 1.000000
    //     0x15eeb08: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15eeb0c: StoreField: r0->field_13 = r4
    //     0x15eeb0c: stur            w4, [x0, #0x13]
    // 0x15eeb10: ArrayStore: r0[0] = r4  ; List_4
    //     0x15eeb10: stur            w4, [x0, #0x17]
    // 0x15eeb14: ldur            x1, [fp, #-8]
    // 0x15eeb18: StoreField: r0->field_b = r1
    //     0x15eeb18: stur            w1, [x0, #0xb]
    // 0x15eeb1c: b               #0x15eebd0
    // 0x15eeb20: ldur            x5, [fp, #-0x18]
    // 0x15eeb24: r4 = 1.000000
    //     0x15eeb24: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15eeb28: r0 = Instance_BlendMode
    //     0x15eeb28: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15eeb2c: ldr             x0, [x0, #0xb30]
    // 0x15eeb30: r3 = Instance_Alignment
    //     0x15eeb30: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15eeb34: ldr             x3, [x3, #0xb10]
    // 0x15eeb38: r2 = 1
    //     0x15eeb38: movz            x2, #0x1
    // 0x15eeb3c: LoadField: r1 = r5->field_13
    //     0x15eeb3c: ldur            w1, [x5, #0x13]
    // 0x15eeb40: DecompressPointer r1
    //     0x15eeb40: add             x1, x1, HEAP, lsl #32
    // 0x15eeb44: r0 = of()
    //     0x15eeb44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15eeb48: LoadField: r1 = r0->field_5b
    //     0x15eeb48: ldur            w1, [x0, #0x5b]
    // 0x15eeb4c: DecompressPointer r1
    //     0x15eeb4c: add             x1, x1, HEAP, lsl #32
    // 0x15eeb50: stur            x1, [fp, #-8]
    // 0x15eeb54: r0 = ColorFilter()
    //     0x15eeb54: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15eeb58: mov             x1, x0
    // 0x15eeb5c: ldur            x0, [fp, #-8]
    // 0x15eeb60: stur            x1, [fp, #-0x20]
    // 0x15eeb64: StoreField: r1->field_7 = r0
    //     0x15eeb64: stur            w0, [x1, #7]
    // 0x15eeb68: r0 = Instance_BlendMode
    //     0x15eeb68: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15eeb6c: ldr             x0, [x0, #0xb30]
    // 0x15eeb70: StoreField: r1->field_b = r0
    //     0x15eeb70: stur            w0, [x1, #0xb]
    // 0x15eeb74: r0 = 1
    //     0x15eeb74: movz            x0, #0x1
    // 0x15eeb78: StoreField: r1->field_13 = r0
    //     0x15eeb78: stur            x0, [x1, #0x13]
    // 0x15eeb7c: r0 = SvgPicture()
    //     0x15eeb7c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15eeb80: stur            x0, [fp, #-8]
    // 0x15eeb84: ldur            x16, [fp, #-0x20]
    // 0x15eeb88: str             x16, [SP]
    // 0x15eeb8c: mov             x1, x0
    // 0x15eeb90: r2 = "assets/images/appbar_arrow.svg"
    //     0x15eeb90: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15eeb94: ldr             x2, [x2, #0xa40]
    // 0x15eeb98: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15eeb98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15eeb9c: ldr             x4, [x4, #0xa38]
    // 0x15eeba0: r0 = SvgPicture.asset()
    //     0x15eeba0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15eeba4: r0 = Align()
    //     0x15eeba4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15eeba8: mov             x1, x0
    // 0x15eebac: r0 = Instance_Alignment
    //     0x15eebac: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15eebb0: ldr             x0, [x0, #0xb10]
    // 0x15eebb4: StoreField: r1->field_f = r0
    //     0x15eebb4: stur            w0, [x1, #0xf]
    // 0x15eebb8: r0 = 1.000000
    //     0x15eebb8: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15eebbc: StoreField: r1->field_13 = r0
    //     0x15eebbc: stur            w0, [x1, #0x13]
    // 0x15eebc0: ArrayStore: r1[0] = r0  ; List_4
    //     0x15eebc0: stur            w0, [x1, #0x17]
    // 0x15eebc4: ldur            x0, [fp, #-8]
    // 0x15eebc8: StoreField: r1->field_b = r0
    //     0x15eebc8: stur            w0, [x1, #0xb]
    // 0x15eebcc: mov             x0, x1
    // 0x15eebd0: stur            x0, [fp, #-8]
    // 0x15eebd4: r0 = InkWell()
    //     0x15eebd4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15eebd8: mov             x3, x0
    // 0x15eebdc: ldur            x0, [fp, #-8]
    // 0x15eebe0: stur            x3, [fp, #-0x20]
    // 0x15eebe4: StoreField: r3->field_b = r0
    //     0x15eebe4: stur            w0, [x3, #0xb]
    // 0x15eebe8: ldur            x2, [fp, #-0x18]
    // 0x15eebec: r1 = Function '<anonymous closure>':.
    //     0x15eebec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea48] AnonymousClosure: (0x15d8310), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::appBar (0x15eea18)
    //     0x15eebf0: ldr             x1, [x1, #0xa48]
    // 0x15eebf4: r0 = AllocateClosure()
    //     0x15eebf4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15eebf8: ldur            x2, [fp, #-0x20]
    // 0x15eebfc: StoreField: r2->field_f = r0
    //     0x15eebfc: stur            w0, [x2, #0xf]
    // 0x15eec00: r0 = true
    //     0x15eec00: add             x0, NULL, #0x20  ; true
    // 0x15eec04: StoreField: r2->field_43 = r0
    //     0x15eec04: stur            w0, [x2, #0x43]
    // 0x15eec08: r1 = Instance_BoxShape
    //     0x15eec08: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15eec0c: ldr             x1, [x1, #0x80]
    // 0x15eec10: StoreField: r2->field_47 = r1
    //     0x15eec10: stur            w1, [x2, #0x47]
    // 0x15eec14: StoreField: r2->field_6f = r0
    //     0x15eec14: stur            w0, [x2, #0x6f]
    // 0x15eec18: r1 = false
    //     0x15eec18: add             x1, NULL, #0x30  ; false
    // 0x15eec1c: StoreField: r2->field_73 = r1
    //     0x15eec1c: stur            w1, [x2, #0x73]
    // 0x15eec20: StoreField: r2->field_83 = r0
    //     0x15eec20: stur            w0, [x2, #0x83]
    // 0x15eec24: StoreField: r2->field_7b = r1
    //     0x15eec24: stur            w1, [x2, #0x7b]
    // 0x15eec28: r0 = Obx()
    //     0x15eec28: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15eec2c: ldur            x2, [fp, #-0x18]
    // 0x15eec30: r1 = Function '<anonymous closure>':.
    //     0x15eec30: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea50] AnonymousClosure: (0x15d8014), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::appBar (0x15eea18)
    //     0x15eec34: ldr             x1, [x1, #0xa50]
    // 0x15eec38: stur            x0, [fp, #-8]
    // 0x15eec3c: r0 = AllocateClosure()
    //     0x15eec3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15eec40: mov             x1, x0
    // 0x15eec44: ldur            x0, [fp, #-8]
    // 0x15eec48: StoreField: r0->field_b = r1
    //     0x15eec48: stur            w1, [x0, #0xb]
    // 0x15eec4c: r1 = Null
    //     0x15eec4c: mov             x1, NULL
    // 0x15eec50: r2 = 2
    //     0x15eec50: movz            x2, #0x2
    // 0x15eec54: r0 = AllocateArray()
    //     0x15eec54: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15eec58: mov             x2, x0
    // 0x15eec5c: ldur            x0, [fp, #-8]
    // 0x15eec60: stur            x2, [fp, #-0x18]
    // 0x15eec64: StoreField: r2->field_f = r0
    //     0x15eec64: stur            w0, [x2, #0xf]
    // 0x15eec68: r1 = <Widget>
    //     0x15eec68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15eec6c: r0 = AllocateGrowableArray()
    //     0x15eec6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15eec70: mov             x1, x0
    // 0x15eec74: ldur            x0, [fp, #-0x18]
    // 0x15eec78: stur            x1, [fp, #-8]
    // 0x15eec7c: StoreField: r1->field_f = r0
    //     0x15eec7c: stur            w0, [x1, #0xf]
    // 0x15eec80: r0 = 2
    //     0x15eec80: movz            x0, #0x2
    // 0x15eec84: StoreField: r1->field_b = r0
    //     0x15eec84: stur            w0, [x1, #0xb]
    // 0x15eec88: r0 = AppBar()
    //     0x15eec88: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15eec8c: stur            x0, [fp, #-0x18]
    // 0x15eec90: ldur            x16, [fp, #-0x10]
    // 0x15eec94: ldur            lr, [fp, #-8]
    // 0x15eec98: stp             lr, x16, [SP]
    // 0x15eec9c: mov             x1, x0
    // 0x15eeca0: ldur            x2, [fp, #-0x20]
    // 0x15eeca4: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15eeca4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15eeca8: ldr             x4, [x4, #0xa58]
    // 0x15eecac: r0 = AppBar()
    //     0x15eecac: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15eecb0: ldur            x0, [fp, #-0x18]
    // 0x15eecb4: LeaveFrame
    //     0x15eecb4: mov             SP, fp
    //     0x15eecb8: ldp             fp, lr, [SP], #0x10
    // 0x15eecbc: ret
    //     0x15eecbc: ret             
    // 0x15eecc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15eecc0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15eecc4: b               #0x15eea38
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0x15eecc8, size: 0x434
    // 0x15eecc8: EnterFrame
    //     0x15eecc8: stp             fp, lr, [SP, #-0x10]!
    //     0x15eeccc: mov             fp, SP
    // 0x15eecd0: AllocStack(0x48)
    //     0x15eecd0: sub             SP, SP, #0x48
    // 0x15eecd4: SetupParameters()
    //     0x15eecd4: ldr             x0, [fp, #0x10]
    //     0x15eecd8: ldur            w2, [x0, #0x17]
    //     0x15eecdc: add             x2, x2, HEAP, lsl #32
    //     0x15eece0: stur            x2, [fp, #-8]
    // 0x15eece4: CheckStackOverflow
    //     0x15eece4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15eece8: cmp             SP, x16
    //     0x15eecec: b.ls            #0x15ef0f4
    // 0x15eecf0: LoadField: r1 = r2->field_f
    //     0x15eecf0: ldur            w1, [x2, #0xf]
    // 0x15eecf4: DecompressPointer r1
    //     0x15eecf4: add             x1, x1, HEAP, lsl #32
    // 0x15eecf8: r0 = controller()
    //     0x15eecf8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15eecfc: LoadField: r1 = r0->field_5b
    //     0x15eecfc: ldur            w1, [x0, #0x5b]
    // 0x15eed00: DecompressPointer r1
    //     0x15eed00: add             x1, x1, HEAP, lsl #32
    // 0x15eed04: r0 = value()
    //     0x15eed04: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15eed08: LoadField: r1 = r0->field_3f
    //     0x15eed08: ldur            w1, [x0, #0x3f]
    // 0x15eed0c: DecompressPointer r1
    //     0x15eed0c: add             x1, x1, HEAP, lsl #32
    // 0x15eed10: cmp             w1, NULL
    // 0x15eed14: b.ne            #0x15eed20
    // 0x15eed18: r0 = Null
    //     0x15eed18: mov             x0, NULL
    // 0x15eed1c: b               #0x15eed28
    // 0x15eed20: LoadField: r0 = r1->field_f
    //     0x15eed20: ldur            w0, [x1, #0xf]
    // 0x15eed24: DecompressPointer r0
    //     0x15eed24: add             x0, x0, HEAP, lsl #32
    // 0x15eed28: r1 = LoadClassIdInstr(r0)
    //     0x15eed28: ldur            x1, [x0, #-1]
    //     0x15eed2c: ubfx            x1, x1, #0xc, #0x14
    // 0x15eed30: r16 = "image_text"
    //     0x15eed30: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15eed34: ldr             x16, [x16, #0xa88]
    // 0x15eed38: stp             x16, x0, [SP]
    // 0x15eed3c: mov             x0, x1
    // 0x15eed40: mov             lr, x0
    // 0x15eed44: ldr             lr, [x21, lr, lsl #3]
    // 0x15eed48: blr             lr
    // 0x15eed4c: tbnz            w0, #4, #0x15eed58
    // 0x15eed50: r1 = true
    //     0x15eed50: add             x1, NULL, #0x20  ; true
    // 0x15eed54: b               #0x15eedb8
    // 0x15eed58: ldur            x0, [fp, #-8]
    // 0x15eed5c: LoadField: r1 = r0->field_f
    //     0x15eed5c: ldur            w1, [x0, #0xf]
    // 0x15eed60: DecompressPointer r1
    //     0x15eed60: add             x1, x1, HEAP, lsl #32
    // 0x15eed64: r0 = controller()
    //     0x15eed64: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15eed68: LoadField: r1 = r0->field_5b
    //     0x15eed68: ldur            w1, [x0, #0x5b]
    // 0x15eed6c: DecompressPointer r1
    //     0x15eed6c: add             x1, x1, HEAP, lsl #32
    // 0x15eed70: r0 = value()
    //     0x15eed70: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15eed74: LoadField: r1 = r0->field_3f
    //     0x15eed74: ldur            w1, [x0, #0x3f]
    // 0x15eed78: DecompressPointer r1
    //     0x15eed78: add             x1, x1, HEAP, lsl #32
    // 0x15eed7c: cmp             w1, NULL
    // 0x15eed80: b.ne            #0x15eed8c
    // 0x15eed84: r0 = Null
    //     0x15eed84: mov             x0, NULL
    // 0x15eed88: b               #0x15eed94
    // 0x15eed8c: LoadField: r0 = r1->field_f
    //     0x15eed8c: ldur            w0, [x1, #0xf]
    // 0x15eed90: DecompressPointer r0
    //     0x15eed90: add             x0, x0, HEAP, lsl #32
    // 0x15eed94: r1 = LoadClassIdInstr(r0)
    //     0x15eed94: ldur            x1, [x0, #-1]
    //     0x15eed98: ubfx            x1, x1, #0xc, #0x14
    // 0x15eed9c: r16 = "image"
    //     0x15eed9c: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x15eeda0: stp             x16, x0, [SP]
    // 0x15eeda4: mov             x0, x1
    // 0x15eeda8: mov             lr, x0
    // 0x15eedac: ldr             lr, [x21, lr, lsl #3]
    // 0x15eedb0: blr             lr
    // 0x15eedb4: mov             x1, x0
    // 0x15eedb8: ldur            x0, [fp, #-8]
    // 0x15eedbc: stur            x1, [fp, #-0x10]
    // 0x15eedc0: r0 = ImageHeaders.forImages()
    //     0x15eedc0: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x15eedc4: mov             x2, x0
    // 0x15eedc8: ldur            x0, [fp, #-8]
    // 0x15eedcc: stur            x2, [fp, #-0x18]
    // 0x15eedd0: LoadField: r1 = r0->field_f
    //     0x15eedd0: ldur            w1, [x0, #0xf]
    // 0x15eedd4: DecompressPointer r1
    //     0x15eedd4: add             x1, x1, HEAP, lsl #32
    // 0x15eedd8: r0 = controller()
    //     0x15eedd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15eeddc: LoadField: r1 = r0->field_5b
    //     0x15eeddc: ldur            w1, [x0, #0x5b]
    // 0x15eede0: DecompressPointer r1
    //     0x15eede0: add             x1, x1, HEAP, lsl #32
    // 0x15eede4: r0 = value()
    //     0x15eede4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15eede8: LoadField: r1 = r0->field_27
    //     0x15eede8: ldur            w1, [x0, #0x27]
    // 0x15eedec: DecompressPointer r1
    //     0x15eedec: add             x1, x1, HEAP, lsl #32
    // 0x15eedf0: cmp             w1, NULL
    // 0x15eedf4: b.ne            #0x15eee00
    // 0x15eedf8: r2 = ""
    //     0x15eedf8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15eedfc: b               #0x15eee04
    // 0x15eee00: mov             x2, x1
    // 0x15eee04: ldur            x0, [fp, #-8]
    // 0x15eee08: ldur            x1, [fp, #-0x10]
    // 0x15eee0c: stur            x2, [fp, #-0x20]
    // 0x15eee10: r0 = CachedNetworkImage()
    //     0x15eee10: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15eee14: stur            x0, [fp, #-0x28]
    // 0x15eee18: ldur            x16, [fp, #-0x18]
    // 0x15eee1c: r30 = Instance_BoxFit
    //     0x15eee1c: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15eee20: ldr             lr, [lr, #0xb18]
    // 0x15eee24: stp             lr, x16, [SP, #0x10]
    // 0x15eee28: r16 = 50.000000
    //     0x15eee28: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15eee2c: ldr             x16, [x16, #0xa90]
    // 0x15eee30: r30 = 50.000000
    //     0x15eee30: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15eee34: ldr             lr, [lr, #0xa90]
    // 0x15eee38: stp             lr, x16, [SP]
    // 0x15eee3c: mov             x1, x0
    // 0x15eee40: ldur            x2, [fp, #-0x20]
    // 0x15eee44: r4 = const [0, 0x6, 0x4, 0x2, fit, 0x3, height, 0x4, httpHeaders, 0x2, width, 0x5, null]
    //     0x15eee44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea98] List(13) [0, 0x6, 0x4, 0x2, "fit", 0x3, "height", 0x4, "httpHeaders", 0x2, "width", 0x5, Null]
    //     0x15eee48: ldr             x4, [x4, #0xa98]
    // 0x15eee4c: r0 = CachedNetworkImage()
    //     0x15eee4c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15eee50: r0 = Visibility()
    //     0x15eee50: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15eee54: mov             x2, x0
    // 0x15eee58: ldur            x0, [fp, #-0x28]
    // 0x15eee5c: stur            x2, [fp, #-0x18]
    // 0x15eee60: StoreField: r2->field_b = r0
    //     0x15eee60: stur            w0, [x2, #0xb]
    // 0x15eee64: r0 = Instance_SizedBox
    //     0x15eee64: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15eee68: StoreField: r2->field_f = r0
    //     0x15eee68: stur            w0, [x2, #0xf]
    // 0x15eee6c: ldur            x1, [fp, #-0x10]
    // 0x15eee70: StoreField: r2->field_13 = r1
    //     0x15eee70: stur            w1, [x2, #0x13]
    // 0x15eee74: r3 = false
    //     0x15eee74: add             x3, NULL, #0x30  ; false
    // 0x15eee78: ArrayStore: r2[0] = r3  ; List_4
    //     0x15eee78: stur            w3, [x2, #0x17]
    // 0x15eee7c: StoreField: r2->field_1b = r3
    //     0x15eee7c: stur            w3, [x2, #0x1b]
    // 0x15eee80: StoreField: r2->field_1f = r3
    //     0x15eee80: stur            w3, [x2, #0x1f]
    // 0x15eee84: StoreField: r2->field_23 = r3
    //     0x15eee84: stur            w3, [x2, #0x23]
    // 0x15eee88: StoreField: r2->field_27 = r3
    //     0x15eee88: stur            w3, [x2, #0x27]
    // 0x15eee8c: StoreField: r2->field_2b = r3
    //     0x15eee8c: stur            w3, [x2, #0x2b]
    // 0x15eee90: ldur            x4, [fp, #-8]
    // 0x15eee94: LoadField: r1 = r4->field_f
    //     0x15eee94: ldur            w1, [x4, #0xf]
    // 0x15eee98: DecompressPointer r1
    //     0x15eee98: add             x1, x1, HEAP, lsl #32
    // 0x15eee9c: r0 = controller()
    //     0x15eee9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15eeea0: LoadField: r1 = r0->field_5b
    //     0x15eeea0: ldur            w1, [x0, #0x5b]
    // 0x15eeea4: DecompressPointer r1
    //     0x15eeea4: add             x1, x1, HEAP, lsl #32
    // 0x15eeea8: r0 = value()
    //     0x15eeea8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15eeeac: LoadField: r1 = r0->field_3f
    //     0x15eeeac: ldur            w1, [x0, #0x3f]
    // 0x15eeeb0: DecompressPointer r1
    //     0x15eeeb0: add             x1, x1, HEAP, lsl #32
    // 0x15eeeb4: cmp             w1, NULL
    // 0x15eeeb8: b.ne            #0x15eeec4
    // 0x15eeebc: r0 = Null
    //     0x15eeebc: mov             x0, NULL
    // 0x15eeec0: b               #0x15eeecc
    // 0x15eeec4: LoadField: r0 = r1->field_f
    //     0x15eeec4: ldur            w0, [x1, #0xf]
    // 0x15eeec8: DecompressPointer r0
    //     0x15eeec8: add             x0, x0, HEAP, lsl #32
    // 0x15eeecc: r1 = LoadClassIdInstr(r0)
    //     0x15eeecc: ldur            x1, [x0, #-1]
    //     0x15eeed0: ubfx            x1, x1, #0xc, #0x14
    // 0x15eeed4: r16 = "image_text"
    //     0x15eeed4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15eeed8: ldr             x16, [x16, #0xa88]
    // 0x15eeedc: stp             x16, x0, [SP]
    // 0x15eeee0: mov             x0, x1
    // 0x15eeee4: mov             lr, x0
    // 0x15eeee8: ldr             lr, [x21, lr, lsl #3]
    // 0x15eeeec: blr             lr
    // 0x15eeef0: tbnz            w0, #4, #0x15eeefc
    // 0x15eeef4: r2 = true
    //     0x15eeef4: add             x2, NULL, #0x20  ; true
    // 0x15eeef8: b               #0x15eef5c
    // 0x15eeefc: ldur            x0, [fp, #-8]
    // 0x15eef00: LoadField: r1 = r0->field_f
    //     0x15eef00: ldur            w1, [x0, #0xf]
    // 0x15eef04: DecompressPointer r1
    //     0x15eef04: add             x1, x1, HEAP, lsl #32
    // 0x15eef08: r0 = controller()
    //     0x15eef08: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15eef0c: LoadField: r1 = r0->field_5b
    //     0x15eef0c: ldur            w1, [x0, #0x5b]
    // 0x15eef10: DecompressPointer r1
    //     0x15eef10: add             x1, x1, HEAP, lsl #32
    // 0x15eef14: r0 = value()
    //     0x15eef14: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15eef18: LoadField: r1 = r0->field_3f
    //     0x15eef18: ldur            w1, [x0, #0x3f]
    // 0x15eef1c: DecompressPointer r1
    //     0x15eef1c: add             x1, x1, HEAP, lsl #32
    // 0x15eef20: cmp             w1, NULL
    // 0x15eef24: b.ne            #0x15eef30
    // 0x15eef28: r0 = Null
    //     0x15eef28: mov             x0, NULL
    // 0x15eef2c: b               #0x15eef38
    // 0x15eef30: LoadField: r0 = r1->field_f
    //     0x15eef30: ldur            w0, [x1, #0xf]
    // 0x15eef34: DecompressPointer r0
    //     0x15eef34: add             x0, x0, HEAP, lsl #32
    // 0x15eef38: r1 = LoadClassIdInstr(r0)
    //     0x15eef38: ldur            x1, [x0, #-1]
    //     0x15eef3c: ubfx            x1, x1, #0xc, #0x14
    // 0x15eef40: r16 = "text"
    //     0x15eef40: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15eef44: stp             x16, x0, [SP]
    // 0x15eef48: mov             x0, x1
    // 0x15eef4c: mov             lr, x0
    // 0x15eef50: ldr             lr, [x21, lr, lsl #3]
    // 0x15eef54: blr             lr
    // 0x15eef58: mov             x2, x0
    // 0x15eef5c: ldur            x0, [fp, #-8]
    // 0x15eef60: stur            x2, [fp, #-0x10]
    // 0x15eef64: LoadField: r1 = r0->field_f
    //     0x15eef64: ldur            w1, [x0, #0xf]
    // 0x15eef68: DecompressPointer r1
    //     0x15eef68: add             x1, x1, HEAP, lsl #32
    // 0x15eef6c: r0 = controller()
    //     0x15eef6c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15eef70: LoadField: r1 = r0->field_5b
    //     0x15eef70: ldur            w1, [x0, #0x5b]
    // 0x15eef74: DecompressPointer r1
    //     0x15eef74: add             x1, x1, HEAP, lsl #32
    // 0x15eef78: r0 = value()
    //     0x15eef78: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15eef7c: LoadField: r1 = r0->field_2b
    //     0x15eef7c: ldur            w1, [x0, #0x2b]
    // 0x15eef80: DecompressPointer r1
    //     0x15eef80: add             x1, x1, HEAP, lsl #32
    // 0x15eef84: cmp             w1, NULL
    // 0x15eef88: b.ne            #0x15eef94
    // 0x15eef8c: r4 = ""
    //     0x15eef8c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15eef90: b               #0x15eef98
    // 0x15eef94: mov             x4, x1
    // 0x15eef98: ldur            x0, [fp, #-8]
    // 0x15eef9c: ldur            x3, [fp, #-0x18]
    // 0x15eefa0: ldur            x2, [fp, #-0x10]
    // 0x15eefa4: stur            x4, [fp, #-0x20]
    // 0x15eefa8: LoadField: r1 = r0->field_13
    //     0x15eefa8: ldur            w1, [x0, #0x13]
    // 0x15eefac: DecompressPointer r1
    //     0x15eefac: add             x1, x1, HEAP, lsl #32
    // 0x15eefb0: r0 = of()
    //     0x15eefb0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15eefb4: LoadField: r1 = r0->field_87
    //     0x15eefb4: ldur            w1, [x0, #0x87]
    // 0x15eefb8: DecompressPointer r1
    //     0x15eefb8: add             x1, x1, HEAP, lsl #32
    // 0x15eefbc: LoadField: r0 = r1->field_2b
    //     0x15eefbc: ldur            w0, [x1, #0x2b]
    // 0x15eefc0: DecompressPointer r0
    //     0x15eefc0: add             x0, x0, HEAP, lsl #32
    // 0x15eefc4: r16 = 16.000000
    //     0x15eefc4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15eefc8: ldr             x16, [x16, #0x188]
    // 0x15eefcc: r30 = Instance_Color
    //     0x15eefcc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15eefd0: stp             lr, x16, [SP]
    // 0x15eefd4: mov             x1, x0
    // 0x15eefd8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15eefd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15eefdc: ldr             x4, [x4, #0xaa0]
    // 0x15eefe0: r0 = copyWith()
    //     0x15eefe0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15eefe4: stur            x0, [fp, #-8]
    // 0x15eefe8: r0 = Text()
    //     0x15eefe8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15eefec: mov             x1, x0
    // 0x15eeff0: ldur            x0, [fp, #-0x20]
    // 0x15eeff4: stur            x1, [fp, #-0x28]
    // 0x15eeff8: StoreField: r1->field_b = r0
    //     0x15eeff8: stur            w0, [x1, #0xb]
    // 0x15eeffc: ldur            x0, [fp, #-8]
    // 0x15ef000: StoreField: r1->field_13 = r0
    //     0x15ef000: stur            w0, [x1, #0x13]
    // 0x15ef004: r0 = Visibility()
    //     0x15ef004: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15ef008: mov             x3, x0
    // 0x15ef00c: ldur            x0, [fp, #-0x28]
    // 0x15ef010: stur            x3, [fp, #-8]
    // 0x15ef014: StoreField: r3->field_b = r0
    //     0x15ef014: stur            w0, [x3, #0xb]
    // 0x15ef018: r0 = Instance_SizedBox
    //     0x15ef018: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15ef01c: StoreField: r3->field_f = r0
    //     0x15ef01c: stur            w0, [x3, #0xf]
    // 0x15ef020: ldur            x0, [fp, #-0x10]
    // 0x15ef024: StoreField: r3->field_13 = r0
    //     0x15ef024: stur            w0, [x3, #0x13]
    // 0x15ef028: r0 = false
    //     0x15ef028: add             x0, NULL, #0x30  ; false
    // 0x15ef02c: ArrayStore: r3[0] = r0  ; List_4
    //     0x15ef02c: stur            w0, [x3, #0x17]
    // 0x15ef030: StoreField: r3->field_1b = r0
    //     0x15ef030: stur            w0, [x3, #0x1b]
    // 0x15ef034: StoreField: r3->field_1f = r0
    //     0x15ef034: stur            w0, [x3, #0x1f]
    // 0x15ef038: StoreField: r3->field_23 = r0
    //     0x15ef038: stur            w0, [x3, #0x23]
    // 0x15ef03c: StoreField: r3->field_27 = r0
    //     0x15ef03c: stur            w0, [x3, #0x27]
    // 0x15ef040: StoreField: r3->field_2b = r0
    //     0x15ef040: stur            w0, [x3, #0x2b]
    // 0x15ef044: r1 = Null
    //     0x15ef044: mov             x1, NULL
    // 0x15ef048: r2 = 6
    //     0x15ef048: movz            x2, #0x6
    // 0x15ef04c: r0 = AllocateArray()
    //     0x15ef04c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15ef050: mov             x2, x0
    // 0x15ef054: ldur            x0, [fp, #-0x18]
    // 0x15ef058: stur            x2, [fp, #-0x10]
    // 0x15ef05c: StoreField: r2->field_f = r0
    //     0x15ef05c: stur            w0, [x2, #0xf]
    // 0x15ef060: r16 = Instance_SizedBox
    //     0x15ef060: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x15ef064: ldr             x16, [x16, #0xaa8]
    // 0x15ef068: StoreField: r2->field_13 = r16
    //     0x15ef068: stur            w16, [x2, #0x13]
    // 0x15ef06c: ldur            x0, [fp, #-8]
    // 0x15ef070: ArrayStore: r2[0] = r0  ; List_4
    //     0x15ef070: stur            w0, [x2, #0x17]
    // 0x15ef074: r1 = <Widget>
    //     0x15ef074: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15ef078: r0 = AllocateGrowableArray()
    //     0x15ef078: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15ef07c: mov             x1, x0
    // 0x15ef080: ldur            x0, [fp, #-0x10]
    // 0x15ef084: stur            x1, [fp, #-8]
    // 0x15ef088: StoreField: r1->field_f = r0
    //     0x15ef088: stur            w0, [x1, #0xf]
    // 0x15ef08c: r0 = 6
    //     0x15ef08c: movz            x0, #0x6
    // 0x15ef090: StoreField: r1->field_b = r0
    //     0x15ef090: stur            w0, [x1, #0xb]
    // 0x15ef094: r0 = Row()
    //     0x15ef094: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15ef098: r1 = Instance_Axis
    //     0x15ef098: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15ef09c: StoreField: r0->field_f = r1
    //     0x15ef09c: stur            w1, [x0, #0xf]
    // 0x15ef0a0: r1 = Instance_MainAxisAlignment
    //     0x15ef0a0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x15ef0a4: ldr             x1, [x1, #0xab0]
    // 0x15ef0a8: StoreField: r0->field_13 = r1
    //     0x15ef0a8: stur            w1, [x0, #0x13]
    // 0x15ef0ac: r1 = Instance_MainAxisSize
    //     0x15ef0ac: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15ef0b0: ldr             x1, [x1, #0xa10]
    // 0x15ef0b4: ArrayStore: r0[0] = r1  ; List_4
    //     0x15ef0b4: stur            w1, [x0, #0x17]
    // 0x15ef0b8: r1 = Instance_CrossAxisAlignment
    //     0x15ef0b8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15ef0bc: ldr             x1, [x1, #0xa18]
    // 0x15ef0c0: StoreField: r0->field_1b = r1
    //     0x15ef0c0: stur            w1, [x0, #0x1b]
    // 0x15ef0c4: r1 = Instance_VerticalDirection
    //     0x15ef0c4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15ef0c8: ldr             x1, [x1, #0xa20]
    // 0x15ef0cc: StoreField: r0->field_23 = r1
    //     0x15ef0cc: stur            w1, [x0, #0x23]
    // 0x15ef0d0: r1 = Instance_Clip
    //     0x15ef0d0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15ef0d4: ldr             x1, [x1, #0x38]
    // 0x15ef0d8: StoreField: r0->field_2b = r1
    //     0x15ef0d8: stur            w1, [x0, #0x2b]
    // 0x15ef0dc: StoreField: r0->field_2f = rZR
    //     0x15ef0dc: stur            xzr, [x0, #0x2f]
    // 0x15ef0e0: ldur            x1, [fp, #-8]
    // 0x15ef0e4: StoreField: r0->field_b = r1
    //     0x15ef0e4: stur            w1, [x0, #0xb]
    // 0x15ef0e8: LeaveFrame
    //     0x15ef0e8: mov             SP, fp
    //     0x15ef0ec: ldp             fp, lr, [SP], #0x10
    // 0x15ef0f0: ret
    //     0x15ef0f0: ret             
    // 0x15ef0f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15ef0f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15ef0f8: b               #0x15eecf0
  }
}
