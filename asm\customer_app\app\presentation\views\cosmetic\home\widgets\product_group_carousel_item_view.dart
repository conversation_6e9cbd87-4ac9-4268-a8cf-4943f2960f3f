// lib: , url: package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart

// class id: 1049282, size: 0x8
class :: {
}

// class id: 3423, size: 0x24, field offset: 0x14
class _ProductGroupCarouselItemViewState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14
  late PageController _imagePageController; // offset: 0x18

  _ build(/* No info */) {
    // ** addr: 0xaeae6c, size: 0x530
    // 0xaeae6c: EnterFrame
    //     0xaeae6c: stp             fp, lr, [SP, #-0x10]!
    //     0xaeae70: mov             fp, SP
    // 0xaeae74: AllocStack(0x88)
    //     0xaeae74: sub             SP, SP, #0x88
    // 0xaeae78: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xaeae78: mov             x0, x1
    //     0xaeae7c: stur            x1, [fp, #-8]
    //     0xaeae80: mov             x1, x2
    //     0xaeae84: stur            x2, [fp, #-0x10]
    // 0xaeae88: CheckStackOverflow
    //     0xaeae88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeae8c: cmp             SP, x16
    //     0xaeae90: b.ls            #0xaeb35c
    // 0xaeae94: r1 = 1
    //     0xaeae94: movz            x1, #0x1
    // 0xaeae98: r0 = AllocateContext()
    //     0xaeae98: bl              #0x16f6108  ; AllocateContextStub
    // 0xaeae9c: mov             x2, x0
    // 0xaeaea0: ldur            x0, [fp, #-8]
    // 0xaeaea4: stur            x2, [fp, #-0x20]
    // 0xaeaea8: StoreField: r2->field_f = r0
    //     0xaeaea8: stur            w0, [x2, #0xf]
    // 0xaeaeac: LoadField: r1 = r0->field_b
    //     0xaeaeac: ldur            w1, [x0, #0xb]
    // 0xaeaeb0: DecompressPointer r1
    //     0xaeaeb0: add             x1, x1, HEAP, lsl #32
    // 0xaeaeb4: cmp             w1, NULL
    // 0xaeaeb8: b.eq            #0xaeb364
    // 0xaeaebc: LoadField: r3 = r1->field_b
    //     0xaeaebc: ldur            w3, [x1, #0xb]
    // 0xaeaec0: DecompressPointer r3
    //     0xaeaec0: add             x3, x3, HEAP, lsl #32
    // 0xaeaec4: cmp             w3, NULL
    // 0xaeaec8: b.ne            #0xaeaed4
    // 0xaeaecc: r3 = Null
    //     0xaeaecc: mov             x3, NULL
    // 0xaeaed0: b               #0xaeaee8
    // 0xaeaed4: LoadField: r4 = r3->field_b
    //     0xaeaed4: ldur            w4, [x3, #0xb]
    // 0xaeaed8: cbz             w4, #0xaeaee4
    // 0xaeaedc: r3 = false
    //     0xaeaedc: add             x3, NULL, #0x30  ; false
    // 0xaeaee0: b               #0xaeaee8
    // 0xaeaee4: r3 = true
    //     0xaeaee4: add             x3, NULL, #0x20  ; true
    // 0xaeaee8: cmp             w3, NULL
    // 0xaeaeec: b.eq            #0xaeaef4
    // 0xaeaef0: tbnz            w3, #4, #0xaeaf04
    // 0xaeaef4: r0 = Instance_SizedBox
    //     0xaeaef4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xaeaef8: LeaveFrame
    //     0xaeaef8: mov             SP, fp
    //     0xaeaefc: ldp             fp, lr, [SP], #0x10
    // 0xaeaf00: ret
    //     0xaeaf00: ret             
    // 0xaeaf04: LoadField: r3 = r1->field_1b
    //     0xaeaf04: ldur            w3, [x1, #0x1b]
    // 0xaeaf08: DecompressPointer r3
    //     0xaeaf08: add             x3, x3, HEAP, lsl #32
    // 0xaeaf0c: LoadField: r1 = r3->field_7
    //     0xaeaf0c: ldur            w1, [x3, #7]
    // 0xaeaf10: DecompressPointer r1
    //     0xaeaf10: add             x1, x1, HEAP, lsl #32
    // 0xaeaf14: cmp             w1, NULL
    // 0xaeaf18: b.ne            #0xaeaf24
    // 0xaeaf1c: r1 = Instance_TitleAlignment
    //     0xaeaf1c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xaeaf20: ldr             x1, [x1, #0x518]
    // 0xaeaf24: r16 = Instance_TitleAlignment
    //     0xaeaf24: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xaeaf28: ldr             x16, [x16, #0x520]
    // 0xaeaf2c: cmp             w1, w16
    // 0xaeaf30: b.ne            #0xaeaf40
    // 0xaeaf34: r3 = Instance_CrossAxisAlignment
    //     0xaeaf34: add             x3, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xaeaf38: ldr             x3, [x3, #0xc68]
    // 0xaeaf3c: b               #0xaeaf64
    // 0xaeaf40: r16 = Instance_TitleAlignment
    //     0xaeaf40: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xaeaf44: ldr             x16, [x16, #0x518]
    // 0xaeaf48: cmp             w1, w16
    // 0xaeaf4c: b.ne            #0xaeaf5c
    // 0xaeaf50: r3 = Instance_CrossAxisAlignment
    //     0xaeaf50: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaeaf54: ldr             x3, [x3, #0x890]
    // 0xaeaf58: b               #0xaeaf64
    // 0xaeaf5c: r3 = Instance_CrossAxisAlignment
    //     0xaeaf5c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaeaf60: ldr             x3, [x3, #0xa18]
    // 0xaeaf64: mov             x1, x0
    // 0xaeaf68: stur            x3, [fp, #-0x18]
    // 0xaeaf6c: r0 = _buildHeader()
    //     0xaeaf6c: bl              #0xaebbc0  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildHeader
    // 0xaeaf70: ldur            x1, [fp, #-8]
    // 0xaeaf74: stur            x0, [fp, #-0x28]
    // 0xaeaf78: r0 = _buildBumperCoupon()
    //     0xaeaf78: bl              #0xaeb3c0  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildBumperCoupon
    // 0xaeaf7c: ldur            x1, [fp, #-0x10]
    // 0xaeaf80: stur            x0, [fp, #-0x30]
    // 0xaeaf84: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaeaf84: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaeaf88: r0 = _of()
    //     0xaeaf88: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xaeaf8c: LoadField: r1 = r0->field_7
    //     0xaeaf8c: ldur            w1, [x0, #7]
    // 0xaeaf90: DecompressPointer r1
    //     0xaeaf90: add             x1, x1, HEAP, lsl #32
    // 0xaeaf94: LoadField: d0 = r1->field_f
    //     0xaeaf94: ldur            d0, [x1, #0xf]
    // 0xaeaf98: d1 = 0.375000
    //     0xaeaf98: fmov            d1, #0.37500000
    // 0xaeaf9c: fmul            d2, d0, d1
    // 0xaeafa0: ldur            x1, [fp, #-0x10]
    // 0xaeafa4: stur            d2, [fp, #-0x68]
    // 0xaeafa8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaeafa8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaeafac: r0 = _of()
    //     0xaeafac: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xaeafb0: LoadField: r1 = r0->field_7
    //     0xaeafb0: ldur            w1, [x0, #7]
    // 0xaeafb4: DecompressPointer r1
    //     0xaeafb4: add             x1, x1, HEAP, lsl #32
    // 0xaeafb8: LoadField: d0 = r1->field_7
    //     0xaeafb8: ldur            d0, [x1, #7]
    // 0xaeafbc: ldur            x1, [fp, #-8]
    // 0xaeafc0: stur            d0, [fp, #-0x70]
    // 0xaeafc4: r0 = totalPages()
    //     0xaeafc4: bl              #0xa55010  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::totalPages
    // 0xaeafc8: mov             x2, x0
    // 0xaeafcc: ldur            x3, [fp, #-8]
    // 0xaeafd0: LoadField: r4 = r3->field_13
    //     0xaeafd0: ldur            w4, [x3, #0x13]
    // 0xaeafd4: DecompressPointer r4
    //     0xaeafd4: add             x4, x4, HEAP, lsl #32
    // 0xaeafd8: r16 = Sentinel
    //     0xaeafd8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaeafdc: cmp             w4, w16
    // 0xaeafe0: b.eq            #0xaeb368
    // 0xaeafe4: stur            x4, [fp, #-0x40]
    // 0xaeafe8: r0 = BoxInt64Instr(r2)
    //     0xaeafe8: sbfiz           x0, x2, #1, #0x1f
    //     0xaeafec: cmp             x2, x0, asr #1
    //     0xaeaff0: b.eq            #0xaeaffc
    //     0xaeaff4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaeaff8: stur            x2, [x0, #7]
    // 0xaeaffc: ldur            x2, [fp, #-0x20]
    // 0xaeb000: r1 = Function '<anonymous closure>':.
    //     0xaeb000: add             x1, PP, #0x58, lsl #12  ; [pp+0x58178] AnonymousClosure: (0xaef134), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::build (0xaeae6c)
    //     0xaeb004: ldr             x1, [x1, #0x178]
    // 0xaeb008: stur            x0, [fp, #-0x38]
    // 0xaeb00c: r0 = AllocateClosure()
    //     0xaeb00c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaeb010: ldur            x2, [fp, #-0x20]
    // 0xaeb014: r1 = Function '<anonymous closure>':.
    //     0xaeb014: add             x1, PP, #0x58, lsl #12  ; [pp+0x58180] AnonymousClosure: (0xaec394), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::build (0xaeae6c)
    //     0xaeb018: ldr             x1, [x1, #0x180]
    // 0xaeb01c: stur            x0, [fp, #-0x20]
    // 0xaeb020: r0 = AllocateClosure()
    //     0xaeb020: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaeb024: stur            x0, [fp, #-0x48]
    // 0xaeb028: r0 = PageView()
    //     0xaeb028: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xaeb02c: stur            x0, [fp, #-0x50]
    // 0xaeb030: r16 = Instance_BouncingScrollPhysics
    //     0xaeb030: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xaeb034: ldr             x16, [x16, #0x890]
    // 0xaeb038: r30 = false
    //     0xaeb038: add             lr, NULL, #0x30  ; false
    // 0xaeb03c: stp             lr, x16, [SP, #8]
    // 0xaeb040: ldur            x16, [fp, #-0x40]
    // 0xaeb044: str             x16, [SP]
    // 0xaeb048: mov             x1, x0
    // 0xaeb04c: ldur            x2, [fp, #-0x48]
    // 0xaeb050: ldur            x3, [fp, #-0x38]
    // 0xaeb054: ldur            x5, [fp, #-0x20]
    // 0xaeb058: r4 = const [0, 0x7, 0x3, 0x4, controller, 0x6, padEnds, 0x5, physics, 0x4, null]
    //     0xaeb058: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d20] List(11) [0, 0x7, 0x3, 0x4, "controller", 0x6, "padEnds", 0x5, "physics", 0x4, Null]
    //     0xaeb05c: ldr             x4, [x4, #0xd20]
    // 0xaeb060: r0 = PageView.builder()
    //     0xaeb060: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xaeb064: ldur            d0, [fp, #-0x70]
    // 0xaeb068: r0 = inline_Allocate_Double()
    //     0xaeb068: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xaeb06c: add             x0, x0, #0x10
    //     0xaeb070: cmp             x1, x0
    //     0xaeb074: b.ls            #0xaeb374
    //     0xaeb078: str             x0, [THR, #0x50]  ; THR::top
    //     0xaeb07c: sub             x0, x0, #0xf
    //     0xaeb080: movz            x1, #0xe15c
    //     0xaeb084: movk            x1, #0x3, lsl #16
    //     0xaeb088: stur            x1, [x0, #-1]
    // 0xaeb08c: StoreField: r0->field_7 = d0
    //     0xaeb08c: stur            d0, [x0, #7]
    // 0xaeb090: stur            x0, [fp, #-0x20]
    // 0xaeb094: r0 = SizedBox()
    //     0xaeb094: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaeb098: mov             x3, x0
    // 0xaeb09c: ldur            x0, [fp, #-0x20]
    // 0xaeb0a0: stur            x3, [fp, #-0x38]
    // 0xaeb0a4: StoreField: r3->field_f = r0
    //     0xaeb0a4: stur            w0, [x3, #0xf]
    // 0xaeb0a8: ldur            d0, [fp, #-0x68]
    // 0xaeb0ac: r0 = inline_Allocate_Double()
    //     0xaeb0ac: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xaeb0b0: add             x0, x0, #0x10
    //     0xaeb0b4: cmp             x1, x0
    //     0xaeb0b8: b.ls            #0xaeb384
    //     0xaeb0bc: str             x0, [THR, #0x50]  ; THR::top
    //     0xaeb0c0: sub             x0, x0, #0xf
    //     0xaeb0c4: movz            x1, #0xe15c
    //     0xaeb0c8: movk            x1, #0x3, lsl #16
    //     0xaeb0cc: stur            x1, [x0, #-1]
    // 0xaeb0d0: StoreField: r0->field_7 = d0
    //     0xaeb0d0: stur            d0, [x0, #7]
    // 0xaeb0d4: StoreField: r3->field_13 = r0
    //     0xaeb0d4: stur            w0, [x3, #0x13]
    // 0xaeb0d8: ldur            x0, [fp, #-0x50]
    // 0xaeb0dc: StoreField: r3->field_b = r0
    //     0xaeb0dc: stur            w0, [x3, #0xb]
    // 0xaeb0e0: r1 = Null
    //     0xaeb0e0: mov             x1, NULL
    // 0xaeb0e4: r2 = 6
    //     0xaeb0e4: movz            x2, #0x6
    // 0xaeb0e8: r0 = AllocateArray()
    //     0xaeb0e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaeb0ec: mov             x2, x0
    // 0xaeb0f0: ldur            x0, [fp, #-0x28]
    // 0xaeb0f4: stur            x2, [fp, #-0x20]
    // 0xaeb0f8: StoreField: r2->field_f = r0
    //     0xaeb0f8: stur            w0, [x2, #0xf]
    // 0xaeb0fc: ldur            x0, [fp, #-0x30]
    // 0xaeb100: StoreField: r2->field_13 = r0
    //     0xaeb100: stur            w0, [x2, #0x13]
    // 0xaeb104: ldur            x0, [fp, #-0x38]
    // 0xaeb108: ArrayStore: r2[0] = r0  ; List_4
    //     0xaeb108: stur            w0, [x2, #0x17]
    // 0xaeb10c: r1 = <Widget>
    //     0xaeb10c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaeb110: r0 = AllocateGrowableArray()
    //     0xaeb110: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaeb114: mov             x2, x0
    // 0xaeb118: ldur            x0, [fp, #-0x20]
    // 0xaeb11c: stur            x2, [fp, #-0x28]
    // 0xaeb120: StoreField: r2->field_f = r0
    //     0xaeb120: stur            w0, [x2, #0xf]
    // 0xaeb124: r0 = 6
    //     0xaeb124: movz            x0, #0x6
    // 0xaeb128: StoreField: r2->field_b = r0
    //     0xaeb128: stur            w0, [x2, #0xb]
    // 0xaeb12c: ldur            x1, [fp, #-8]
    // 0xaeb130: r0 = totalPages()
    //     0xaeb130: bl              #0xa55010  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::totalPages
    // 0xaeb134: cmp             x0, #1
    // 0xaeb138: b.le            #0xaeb2d8
    // 0xaeb13c: ldur            x2, [fp, #-8]
    // 0xaeb140: ldur            x0, [fp, #-0x28]
    // 0xaeb144: mov             x1, x2
    // 0xaeb148: r0 = totalPages()
    //     0xaeb148: bl              #0xa55010  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::totalPages
    // 0xaeb14c: mov             x2, x0
    // 0xaeb150: ldur            x0, [fp, #-8]
    // 0xaeb154: stur            x2, [fp, #-0x60]
    // 0xaeb158: LoadField: r3 = r0->field_1b
    //     0xaeb158: ldur            x3, [x0, #0x1b]
    // 0xaeb15c: ldur            x1, [fp, #-0x10]
    // 0xaeb160: stur            x3, [fp, #-0x58]
    // 0xaeb164: r0 = of()
    //     0xaeb164: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaeb168: LoadField: r1 = r0->field_5b
    //     0xaeb168: ldur            w1, [x0, #0x5b]
    // 0xaeb16c: DecompressPointer r1
    //     0xaeb16c: add             x1, x1, HEAP, lsl #32
    // 0xaeb170: stur            x1, [fp, #-8]
    // 0xaeb174: r0 = CarouselIndicator()
    //     0xaeb174: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xaeb178: mov             x3, x0
    // 0xaeb17c: ldur            x0, [fp, #-0x60]
    // 0xaeb180: stur            x3, [fp, #-0x10]
    // 0xaeb184: StoreField: r3->field_b = r0
    //     0xaeb184: stur            x0, [x3, #0xb]
    // 0xaeb188: ldur            x0, [fp, #-0x58]
    // 0xaeb18c: StoreField: r3->field_13 = r0
    //     0xaeb18c: stur            x0, [x3, #0x13]
    // 0xaeb190: ldur            x0, [fp, #-8]
    // 0xaeb194: StoreField: r3->field_1b = r0
    //     0xaeb194: stur            w0, [x3, #0x1b]
    // 0xaeb198: r0 = Instance_Color
    //     0xaeb198: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xaeb19c: ldr             x0, [x0, #0x90]
    // 0xaeb1a0: StoreField: r3->field_1f = r0
    //     0xaeb1a0: stur            w0, [x3, #0x1f]
    // 0xaeb1a4: r1 = Null
    //     0xaeb1a4: mov             x1, NULL
    // 0xaeb1a8: r2 = 2
    //     0xaeb1a8: movz            x2, #0x2
    // 0xaeb1ac: r0 = AllocateArray()
    //     0xaeb1ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaeb1b0: mov             x2, x0
    // 0xaeb1b4: ldur            x0, [fp, #-0x10]
    // 0xaeb1b8: stur            x2, [fp, #-8]
    // 0xaeb1bc: StoreField: r2->field_f = r0
    //     0xaeb1bc: stur            w0, [x2, #0xf]
    // 0xaeb1c0: r1 = <Widget>
    //     0xaeb1c0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaeb1c4: r0 = AllocateGrowableArray()
    //     0xaeb1c4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaeb1c8: mov             x1, x0
    // 0xaeb1cc: ldur            x0, [fp, #-8]
    // 0xaeb1d0: stur            x1, [fp, #-0x10]
    // 0xaeb1d4: StoreField: r1->field_f = r0
    //     0xaeb1d4: stur            w0, [x1, #0xf]
    // 0xaeb1d8: r0 = 2
    //     0xaeb1d8: movz            x0, #0x2
    // 0xaeb1dc: StoreField: r1->field_b = r0
    //     0xaeb1dc: stur            w0, [x1, #0xb]
    // 0xaeb1e0: r0 = Row()
    //     0xaeb1e0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaeb1e4: mov             x1, x0
    // 0xaeb1e8: r0 = Instance_Axis
    //     0xaeb1e8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaeb1ec: stur            x1, [fp, #-8]
    // 0xaeb1f0: StoreField: r1->field_f = r0
    //     0xaeb1f0: stur            w0, [x1, #0xf]
    // 0xaeb1f4: r0 = Instance_MainAxisAlignment
    //     0xaeb1f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xaeb1f8: ldr             x0, [x0, #0xab0]
    // 0xaeb1fc: StoreField: r1->field_13 = r0
    //     0xaeb1fc: stur            w0, [x1, #0x13]
    // 0xaeb200: r0 = Instance_MainAxisSize
    //     0xaeb200: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaeb204: ldr             x0, [x0, #0xa10]
    // 0xaeb208: ArrayStore: r1[0] = r0  ; List_4
    //     0xaeb208: stur            w0, [x1, #0x17]
    // 0xaeb20c: r2 = Instance_CrossAxisAlignment
    //     0xaeb20c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaeb210: ldr             x2, [x2, #0xa18]
    // 0xaeb214: StoreField: r1->field_1b = r2
    //     0xaeb214: stur            w2, [x1, #0x1b]
    // 0xaeb218: r2 = Instance_VerticalDirection
    //     0xaeb218: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaeb21c: ldr             x2, [x2, #0xa20]
    // 0xaeb220: StoreField: r1->field_23 = r2
    //     0xaeb220: stur            w2, [x1, #0x23]
    // 0xaeb224: r3 = Instance_Clip
    //     0xaeb224: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaeb228: ldr             x3, [x3, #0x38]
    // 0xaeb22c: StoreField: r1->field_2b = r3
    //     0xaeb22c: stur            w3, [x1, #0x2b]
    // 0xaeb230: StoreField: r1->field_2f = rZR
    //     0xaeb230: stur            xzr, [x1, #0x2f]
    // 0xaeb234: ldur            x4, [fp, #-0x10]
    // 0xaeb238: StoreField: r1->field_b = r4
    //     0xaeb238: stur            w4, [x1, #0xb]
    // 0xaeb23c: r0 = Padding()
    //     0xaeb23c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaeb240: mov             x2, x0
    // 0xaeb244: r0 = Instance_EdgeInsets
    //     0xaeb244: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xaeb248: ldr             x0, [x0, #0xa00]
    // 0xaeb24c: stur            x2, [fp, #-0x10]
    // 0xaeb250: StoreField: r2->field_f = r0
    //     0xaeb250: stur            w0, [x2, #0xf]
    // 0xaeb254: ldur            x0, [fp, #-8]
    // 0xaeb258: StoreField: r2->field_b = r0
    //     0xaeb258: stur            w0, [x2, #0xb]
    // 0xaeb25c: ldur            x0, [fp, #-0x28]
    // 0xaeb260: LoadField: r1 = r0->field_b
    //     0xaeb260: ldur            w1, [x0, #0xb]
    // 0xaeb264: LoadField: r3 = r0->field_f
    //     0xaeb264: ldur            w3, [x0, #0xf]
    // 0xaeb268: DecompressPointer r3
    //     0xaeb268: add             x3, x3, HEAP, lsl #32
    // 0xaeb26c: LoadField: r4 = r3->field_b
    //     0xaeb26c: ldur            w4, [x3, #0xb]
    // 0xaeb270: r3 = LoadInt32Instr(r1)
    //     0xaeb270: sbfx            x3, x1, #1, #0x1f
    // 0xaeb274: stur            x3, [fp, #-0x58]
    // 0xaeb278: r1 = LoadInt32Instr(r4)
    //     0xaeb278: sbfx            x1, x4, #1, #0x1f
    // 0xaeb27c: cmp             x3, x1
    // 0xaeb280: b.ne            #0xaeb28c
    // 0xaeb284: mov             x1, x0
    // 0xaeb288: r0 = _growToNextCapacity()
    //     0xaeb288: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaeb28c: ldur            x2, [fp, #-0x28]
    // 0xaeb290: ldur            x3, [fp, #-0x58]
    // 0xaeb294: add             x0, x3, #1
    // 0xaeb298: lsl             x1, x0, #1
    // 0xaeb29c: StoreField: r2->field_b = r1
    //     0xaeb29c: stur            w1, [x2, #0xb]
    // 0xaeb2a0: LoadField: r1 = r2->field_f
    //     0xaeb2a0: ldur            w1, [x2, #0xf]
    // 0xaeb2a4: DecompressPointer r1
    //     0xaeb2a4: add             x1, x1, HEAP, lsl #32
    // 0xaeb2a8: ldur            x0, [fp, #-0x10]
    // 0xaeb2ac: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaeb2ac: add             x25, x1, x3, lsl #2
    //     0xaeb2b0: add             x25, x25, #0xf
    //     0xaeb2b4: str             w0, [x25]
    //     0xaeb2b8: tbz             w0, #0, #0xaeb2d4
    //     0xaeb2bc: ldurb           w16, [x1, #-1]
    //     0xaeb2c0: ldurb           w17, [x0, #-1]
    //     0xaeb2c4: and             x16, x17, x16, lsr #2
    //     0xaeb2c8: tst             x16, HEAP, lsr #32
    //     0xaeb2cc: b.eq            #0xaeb2d4
    //     0xaeb2d0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaeb2d4: b               #0xaeb2dc
    // 0xaeb2d8: ldur            x2, [fp, #-0x28]
    // 0xaeb2dc: ldur            x0, [fp, #-0x18]
    // 0xaeb2e0: r0 = Column()
    //     0xaeb2e0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaeb2e4: mov             x1, x0
    // 0xaeb2e8: r0 = Instance_Axis
    //     0xaeb2e8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaeb2ec: stur            x1, [fp, #-8]
    // 0xaeb2f0: StoreField: r1->field_f = r0
    //     0xaeb2f0: stur            w0, [x1, #0xf]
    // 0xaeb2f4: r0 = Instance_MainAxisAlignment
    //     0xaeb2f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaeb2f8: ldr             x0, [x0, #0xa08]
    // 0xaeb2fc: StoreField: r1->field_13 = r0
    //     0xaeb2fc: stur            w0, [x1, #0x13]
    // 0xaeb300: r0 = Instance_MainAxisSize
    //     0xaeb300: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaeb304: ldr             x0, [x0, #0xa10]
    // 0xaeb308: ArrayStore: r1[0] = r0  ; List_4
    //     0xaeb308: stur            w0, [x1, #0x17]
    // 0xaeb30c: ldur            x0, [fp, #-0x18]
    // 0xaeb310: StoreField: r1->field_1b = r0
    //     0xaeb310: stur            w0, [x1, #0x1b]
    // 0xaeb314: r0 = Instance_VerticalDirection
    //     0xaeb314: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaeb318: ldr             x0, [x0, #0xa20]
    // 0xaeb31c: StoreField: r1->field_23 = r0
    //     0xaeb31c: stur            w0, [x1, #0x23]
    // 0xaeb320: r0 = Instance_Clip
    //     0xaeb320: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaeb324: ldr             x0, [x0, #0x38]
    // 0xaeb328: StoreField: r1->field_2b = r0
    //     0xaeb328: stur            w0, [x1, #0x2b]
    // 0xaeb32c: StoreField: r1->field_2f = rZR
    //     0xaeb32c: stur            xzr, [x1, #0x2f]
    // 0xaeb330: ldur            x0, [fp, #-0x28]
    // 0xaeb334: StoreField: r1->field_b = r0
    //     0xaeb334: stur            w0, [x1, #0xb]
    // 0xaeb338: r0 = Padding()
    //     0xaeb338: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaeb33c: r1 = Instance_EdgeInsets
    //     0xaeb33c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3edd8] Obj!EdgeInsets@d58f41
    //     0xaeb340: ldr             x1, [x1, #0xdd8]
    // 0xaeb344: StoreField: r0->field_f = r1
    //     0xaeb344: stur            w1, [x0, #0xf]
    // 0xaeb348: ldur            x1, [fp, #-8]
    // 0xaeb34c: StoreField: r0->field_b = r1
    //     0xaeb34c: stur            w1, [x0, #0xb]
    // 0xaeb350: LeaveFrame
    //     0xaeb350: mov             SP, fp
    //     0xaeb354: ldp             fp, lr, [SP], #0x10
    // 0xaeb358: ret
    //     0xaeb358: ret             
    // 0xaeb35c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeb35c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeb360: b               #0xaeae94
    // 0xaeb364: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaeb364: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaeb368: r9 = _pageController
    //     0xaeb368: add             x9, PP, #0x58, lsl #12  ; [pp+0x58188] Field <_ProductGroupCarouselItemViewState@1467114462._pageController@1467114462>: late (offset: 0x14)
    //     0xaeb36c: ldr             x9, [x9, #0x188]
    // 0xaeb370: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaeb370: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaeb374: SaveReg d0
    //     0xaeb374: str             q0, [SP, #-0x10]!
    // 0xaeb378: r0 = AllocateDouble()
    //     0xaeb378: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaeb37c: RestoreReg d0
    //     0xaeb37c: ldr             q0, [SP], #0x10
    // 0xaeb380: b               #0xaeb08c
    // 0xaeb384: SaveReg d0
    //     0xaeb384: str             q0, [SP, #-0x10]!
    // 0xaeb388: SaveReg r3
    //     0xaeb388: str             x3, [SP, #-8]!
    // 0xaeb38c: r0 = AllocateDouble()
    //     0xaeb38c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaeb390: RestoreReg r3
    //     0xaeb390: ldr             x3, [SP], #8
    // 0xaeb394: RestoreReg d0
    //     0xaeb394: ldr             q0, [SP], #0x10
    // 0xaeb398: b               #0xaeb0d0
  }
  _ _buildBumperCoupon(/* No info */) {
    // ** addr: 0xaeb3c0, size: 0x800
    // 0xaeb3c0: EnterFrame
    //     0xaeb3c0: stp             fp, lr, [SP, #-0x10]!
    //     0xaeb3c4: mov             fp, SP
    // 0xaeb3c8: AllocStack(0x60)
    //     0xaeb3c8: sub             SP, SP, #0x60
    // 0xaeb3cc: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x28 */)
    //     0xaeb3cc: stur            x1, [fp, #-0x28]
    // 0xaeb3d0: CheckStackOverflow
    //     0xaeb3d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeb3d4: cmp             SP, x16
    //     0xaeb3d8: b.ls            #0xaebba0
    // 0xaeb3dc: LoadField: r0 = r1->field_b
    //     0xaeb3dc: ldur            w0, [x1, #0xb]
    // 0xaeb3e0: DecompressPointer r0
    //     0xaeb3e0: add             x0, x0, HEAP, lsl #32
    // 0xaeb3e4: cmp             w0, NULL
    // 0xaeb3e8: b.eq            #0xaebba8
    // 0xaeb3ec: LoadField: r2 = r0->field_33
    //     0xaeb3ec: ldur            w2, [x0, #0x33]
    // 0xaeb3f0: DecompressPointer r2
    //     0xaeb3f0: add             x2, x2, HEAP, lsl #32
    // 0xaeb3f4: cmp             w2, NULL
    // 0xaeb3f8: b.eq            #0xaeb40c
    // 0xaeb3fc: LoadField: r0 = r2->field_f
    //     0xaeb3fc: ldur            w0, [x2, #0xf]
    // 0xaeb400: DecompressPointer r0
    //     0xaeb400: add             x0, x0, HEAP, lsl #32
    // 0xaeb404: cmp             w0, NULL
    // 0xaeb408: b.ne            #0xaeb41c
    // 0xaeb40c: r0 = Instance_SizedBox
    //     0xaeb40c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xaeb410: LeaveFrame
    //     0xaeb410: mov             SP, fp
    //     0xaeb414: ldp             fp, lr, [SP], #0x10
    // 0xaeb418: ret
    //     0xaeb418: ret             
    // 0xaeb41c: LoadField: r0 = r2->field_13
    //     0xaeb41c: ldur            w0, [x2, #0x13]
    // 0xaeb420: DecompressPointer r0
    //     0xaeb420: add             x0, x0, HEAP, lsl #32
    // 0xaeb424: stur            x0, [fp, #-0x20]
    // 0xaeb428: cmp             w0, NULL
    // 0xaeb42c: b.ne            #0xaeb438
    // 0xaeb430: r2 = Null
    //     0xaeb430: mov             x2, NULL
    // 0xaeb434: b               #0xaeb440
    // 0xaeb438: LoadField: r2 = r0->field_7
    //     0xaeb438: ldur            w2, [x0, #7]
    // 0xaeb43c: DecompressPointer r2
    //     0xaeb43c: add             x2, x2, HEAP, lsl #32
    // 0xaeb440: cmp             w2, NULL
    // 0xaeb444: b.ne            #0xaeb450
    // 0xaeb448: r2 = 0
    //     0xaeb448: movz            x2, #0
    // 0xaeb44c: b               #0xaeb460
    // 0xaeb450: r3 = LoadInt32Instr(r2)
    //     0xaeb450: sbfx            x3, x2, #1, #0x1f
    //     0xaeb454: tbz             w2, #0, #0xaeb45c
    //     0xaeb458: ldur            x3, [x2, #7]
    // 0xaeb45c: mov             x2, x3
    // 0xaeb460: stur            x2, [fp, #-0x18]
    // 0xaeb464: cmp             w0, NULL
    // 0xaeb468: b.ne            #0xaeb474
    // 0xaeb46c: r3 = Null
    //     0xaeb46c: mov             x3, NULL
    // 0xaeb470: b               #0xaeb47c
    // 0xaeb474: LoadField: r3 = r0->field_b
    //     0xaeb474: ldur            w3, [x0, #0xb]
    // 0xaeb478: DecompressPointer r3
    //     0xaeb478: add             x3, x3, HEAP, lsl #32
    // 0xaeb47c: cmp             w3, NULL
    // 0xaeb480: b.ne            #0xaeb48c
    // 0xaeb484: r3 = 0
    //     0xaeb484: movz            x3, #0
    // 0xaeb488: b               #0xaeb49c
    // 0xaeb48c: r4 = LoadInt32Instr(r3)
    //     0xaeb48c: sbfx            x4, x3, #1, #0x1f
    //     0xaeb490: tbz             w3, #0, #0xaeb498
    //     0xaeb494: ldur            x4, [x3, #7]
    // 0xaeb498: mov             x3, x4
    // 0xaeb49c: stur            x3, [fp, #-0x10]
    // 0xaeb4a0: cmp             w0, NULL
    // 0xaeb4a4: b.ne            #0xaeb4b0
    // 0xaeb4a8: r4 = Null
    //     0xaeb4a8: mov             x4, NULL
    // 0xaeb4ac: b               #0xaeb4b8
    // 0xaeb4b0: LoadField: r4 = r0->field_f
    //     0xaeb4b0: ldur            w4, [x0, #0xf]
    // 0xaeb4b4: DecompressPointer r4
    //     0xaeb4b4: add             x4, x4, HEAP, lsl #32
    // 0xaeb4b8: cmp             w4, NULL
    // 0xaeb4bc: b.ne            #0xaeb4c8
    // 0xaeb4c0: r4 = 0
    //     0xaeb4c0: movz            x4, #0
    // 0xaeb4c4: b               #0xaeb4d8
    // 0xaeb4c8: r5 = LoadInt32Instr(r4)
    //     0xaeb4c8: sbfx            x5, x4, #1, #0x1f
    //     0xaeb4cc: tbz             w4, #0, #0xaeb4d4
    //     0xaeb4d0: ldur            x5, [x4, #7]
    // 0xaeb4d4: mov             x4, x5
    // 0xaeb4d8: stur            x4, [fp, #-8]
    // 0xaeb4dc: r0 = Color()
    //     0xaeb4dc: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xaeb4e0: mov             x1, x0
    // 0xaeb4e4: r0 = Instance_ColorSpace
    //     0xaeb4e4: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xaeb4e8: stur            x1, [fp, #-0x30]
    // 0xaeb4ec: StoreField: r1->field_27 = r0
    //     0xaeb4ec: stur            w0, [x1, #0x27]
    // 0xaeb4f0: d0 = 1.000000
    //     0xaeb4f0: fmov            d0, #1.00000000
    // 0xaeb4f4: StoreField: r1->field_7 = d0
    //     0xaeb4f4: stur            d0, [x1, #7]
    // 0xaeb4f8: ldur            x2, [fp, #-0x18]
    // 0xaeb4fc: ubfx            x2, x2, #0, #0x20
    // 0xaeb500: and             w3, w2, #0xff
    // 0xaeb504: ubfx            x3, x3, #0, #0x20
    // 0xaeb508: scvtf           d0, x3
    // 0xaeb50c: d1 = 255.000000
    //     0xaeb50c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xaeb510: fdiv            d2, d0, d1
    // 0xaeb514: StoreField: r1->field_f = d2
    //     0xaeb514: stur            d2, [x1, #0xf]
    // 0xaeb518: ldur            x2, [fp, #-0x10]
    // 0xaeb51c: ubfx            x2, x2, #0, #0x20
    // 0xaeb520: and             w3, w2, #0xff
    // 0xaeb524: ubfx            x3, x3, #0, #0x20
    // 0xaeb528: scvtf           d0, x3
    // 0xaeb52c: fdiv            d2, d0, d1
    // 0xaeb530: ArrayStore: r1[0] = d2  ; List_8
    //     0xaeb530: stur            d2, [x1, #0x17]
    // 0xaeb534: ldur            x2, [fp, #-8]
    // 0xaeb538: ubfx            x2, x2, #0, #0x20
    // 0xaeb53c: and             w3, w2, #0xff
    // 0xaeb540: ubfx            x3, x3, #0, #0x20
    // 0xaeb544: scvtf           d0, x3
    // 0xaeb548: fdiv            d2, d0, d1
    // 0xaeb54c: StoreField: r1->field_1f = d2
    //     0xaeb54c: stur            d2, [x1, #0x1f]
    // 0xaeb550: ldur            x2, [fp, #-0x20]
    // 0xaeb554: cmp             w2, NULL
    // 0xaeb558: b.ne            #0xaeb564
    // 0xaeb55c: r3 = Null
    //     0xaeb55c: mov             x3, NULL
    // 0xaeb560: b               #0xaeb56c
    // 0xaeb564: LoadField: r3 = r2->field_7
    //     0xaeb564: ldur            w3, [x2, #7]
    // 0xaeb568: DecompressPointer r3
    //     0xaeb568: add             x3, x3, HEAP, lsl #32
    // 0xaeb56c: cmp             w3, NULL
    // 0xaeb570: b.ne            #0xaeb57c
    // 0xaeb574: r3 = 0
    //     0xaeb574: movz            x3, #0
    // 0xaeb578: b               #0xaeb58c
    // 0xaeb57c: r4 = LoadInt32Instr(r3)
    //     0xaeb57c: sbfx            x4, x3, #1, #0x1f
    //     0xaeb580: tbz             w3, #0, #0xaeb588
    //     0xaeb584: ldur            x4, [x3, #7]
    // 0xaeb588: mov             x3, x4
    // 0xaeb58c: stur            x3, [fp, #-0x18]
    // 0xaeb590: cmp             w2, NULL
    // 0xaeb594: b.ne            #0xaeb5a0
    // 0xaeb598: r4 = Null
    //     0xaeb598: mov             x4, NULL
    // 0xaeb59c: b               #0xaeb5a8
    // 0xaeb5a0: LoadField: r4 = r2->field_b
    //     0xaeb5a0: ldur            w4, [x2, #0xb]
    // 0xaeb5a4: DecompressPointer r4
    //     0xaeb5a4: add             x4, x4, HEAP, lsl #32
    // 0xaeb5a8: cmp             w4, NULL
    // 0xaeb5ac: b.ne            #0xaeb5b8
    // 0xaeb5b0: r4 = 0
    //     0xaeb5b0: movz            x4, #0
    // 0xaeb5b4: b               #0xaeb5c8
    // 0xaeb5b8: r5 = LoadInt32Instr(r4)
    //     0xaeb5b8: sbfx            x5, x4, #1, #0x1f
    //     0xaeb5bc: tbz             w4, #0, #0xaeb5c4
    //     0xaeb5c0: ldur            x5, [x4, #7]
    // 0xaeb5c4: mov             x4, x5
    // 0xaeb5c8: stur            x4, [fp, #-0x10]
    // 0xaeb5cc: cmp             w2, NULL
    // 0xaeb5d0: b.ne            #0xaeb5dc
    // 0xaeb5d4: r2 = Null
    //     0xaeb5d4: mov             x2, NULL
    // 0xaeb5d8: b               #0xaeb5e8
    // 0xaeb5dc: LoadField: r5 = r2->field_f
    //     0xaeb5dc: ldur            w5, [x2, #0xf]
    // 0xaeb5e0: DecompressPointer r5
    //     0xaeb5e0: add             x5, x5, HEAP, lsl #32
    // 0xaeb5e4: mov             x2, x5
    // 0xaeb5e8: cmp             w2, NULL
    // 0xaeb5ec: b.ne            #0xaeb5f8
    // 0xaeb5f0: r5 = 0
    //     0xaeb5f0: movz            x5, #0
    // 0xaeb5f4: b               #0xaeb604
    // 0xaeb5f8: r5 = LoadInt32Instr(r2)
    //     0xaeb5f8: sbfx            x5, x2, #1, #0x1f
    //     0xaeb5fc: tbz             w2, #0, #0xaeb604
    //     0xaeb600: ldur            x5, [x2, #7]
    // 0xaeb604: ldur            x2, [fp, #-0x28]
    // 0xaeb608: stur            x5, [fp, #-8]
    // 0xaeb60c: r0 = Color()
    //     0xaeb60c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xaeb610: mov             x3, x0
    // 0xaeb614: r0 = Instance_ColorSpace
    //     0xaeb614: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xaeb618: stur            x3, [fp, #-0x20]
    // 0xaeb61c: StoreField: r3->field_27 = r0
    //     0xaeb61c: stur            w0, [x3, #0x27]
    // 0xaeb620: d0 = 0.700000
    //     0xaeb620: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xaeb624: ldr             d0, [x17, #0xf48]
    // 0xaeb628: StoreField: r3->field_7 = d0
    //     0xaeb628: stur            d0, [x3, #7]
    // 0xaeb62c: ldur            x0, [fp, #-0x18]
    // 0xaeb630: ubfx            x0, x0, #0, #0x20
    // 0xaeb634: and             w1, w0, #0xff
    // 0xaeb638: ubfx            x1, x1, #0, #0x20
    // 0xaeb63c: scvtf           d0, x1
    // 0xaeb640: d1 = 255.000000
    //     0xaeb640: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xaeb644: fdiv            d2, d0, d1
    // 0xaeb648: StoreField: r3->field_f = d2
    //     0xaeb648: stur            d2, [x3, #0xf]
    // 0xaeb64c: ldur            x0, [fp, #-0x10]
    // 0xaeb650: ubfx            x0, x0, #0, #0x20
    // 0xaeb654: and             w1, w0, #0xff
    // 0xaeb658: ubfx            x1, x1, #0, #0x20
    // 0xaeb65c: scvtf           d0, x1
    // 0xaeb660: fdiv            d2, d0, d1
    // 0xaeb664: ArrayStore: r3[0] = d2  ; List_8
    //     0xaeb664: stur            d2, [x3, #0x17]
    // 0xaeb668: ldur            x0, [fp, #-8]
    // 0xaeb66c: ubfx            x0, x0, #0, #0x20
    // 0xaeb670: and             w1, w0, #0xff
    // 0xaeb674: ubfx            x1, x1, #0, #0x20
    // 0xaeb678: scvtf           d0, x1
    // 0xaeb67c: fdiv            d2, d0, d1
    // 0xaeb680: StoreField: r3->field_1f = d2
    //     0xaeb680: stur            d2, [x3, #0x1f]
    // 0xaeb684: r1 = Null
    //     0xaeb684: mov             x1, NULL
    // 0xaeb688: r2 = 4
    //     0xaeb688: movz            x2, #0x4
    // 0xaeb68c: r0 = AllocateArray()
    //     0xaeb68c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaeb690: mov             x2, x0
    // 0xaeb694: ldur            x0, [fp, #-0x30]
    // 0xaeb698: stur            x2, [fp, #-0x38]
    // 0xaeb69c: StoreField: r2->field_f = r0
    //     0xaeb69c: stur            w0, [x2, #0xf]
    // 0xaeb6a0: ldur            x0, [fp, #-0x20]
    // 0xaeb6a4: StoreField: r2->field_13 = r0
    //     0xaeb6a4: stur            w0, [x2, #0x13]
    // 0xaeb6a8: r1 = <Color>
    //     0xaeb6a8: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xaeb6ac: ldr             x1, [x1, #0xf80]
    // 0xaeb6b0: r0 = AllocateGrowableArray()
    //     0xaeb6b0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaeb6b4: mov             x1, x0
    // 0xaeb6b8: ldur            x0, [fp, #-0x38]
    // 0xaeb6bc: stur            x1, [fp, #-0x20]
    // 0xaeb6c0: StoreField: r1->field_f = r0
    //     0xaeb6c0: stur            w0, [x1, #0xf]
    // 0xaeb6c4: r2 = 4
    //     0xaeb6c4: movz            x2, #0x4
    // 0xaeb6c8: StoreField: r1->field_b = r2
    //     0xaeb6c8: stur            w2, [x1, #0xb]
    // 0xaeb6cc: r0 = LinearGradient()
    //     0xaeb6cc: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xaeb6d0: mov             x1, x0
    // 0xaeb6d4: r0 = Instance_Alignment
    //     0xaeb6d4: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xaeb6d8: ldr             x0, [x0, #0xce0]
    // 0xaeb6dc: stur            x1, [fp, #-0x30]
    // 0xaeb6e0: StoreField: r1->field_13 = r0
    //     0xaeb6e0: stur            w0, [x1, #0x13]
    // 0xaeb6e4: r0 = Instance_Alignment
    //     0xaeb6e4: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xaeb6e8: ldr             x0, [x0, #0xce8]
    // 0xaeb6ec: ArrayStore: r1[0] = r0  ; List_4
    //     0xaeb6ec: stur            w0, [x1, #0x17]
    // 0xaeb6f0: r0 = Instance_TileMode
    //     0xaeb6f0: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xaeb6f4: ldr             x0, [x0, #0xcf0]
    // 0xaeb6f8: StoreField: r1->field_1b = r0
    //     0xaeb6f8: stur            w0, [x1, #0x1b]
    // 0xaeb6fc: ldur            x0, [fp, #-0x20]
    // 0xaeb700: StoreField: r1->field_7 = r0
    //     0xaeb700: stur            w0, [x1, #7]
    // 0xaeb704: r0 = BoxDecoration()
    //     0xaeb704: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaeb708: mov             x2, x0
    // 0xaeb70c: r0 = Instance_BorderRadius
    //     0xaeb70c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48b20] Obj!BorderRadius@d5a2e1
    //     0xaeb710: ldr             x0, [x0, #0xb20]
    // 0xaeb714: stur            x2, [fp, #-0x20]
    // 0xaeb718: StoreField: r2->field_13 = r0
    //     0xaeb718: stur            w0, [x2, #0x13]
    // 0xaeb71c: ldur            x0, [fp, #-0x30]
    // 0xaeb720: StoreField: r2->field_1b = r0
    //     0xaeb720: stur            w0, [x2, #0x1b]
    // 0xaeb724: r0 = Instance_BoxShape
    //     0xaeb724: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaeb728: ldr             x0, [x0, #0x80]
    // 0xaeb72c: StoreField: r2->field_23 = r0
    //     0xaeb72c: stur            w0, [x2, #0x23]
    // 0xaeb730: ldur            x0, [fp, #-0x28]
    // 0xaeb734: LoadField: r1 = r0->field_f
    //     0xaeb734: ldur            w1, [x0, #0xf]
    // 0xaeb738: DecompressPointer r1
    //     0xaeb738: add             x1, x1, HEAP, lsl #32
    // 0xaeb73c: cmp             w1, NULL
    // 0xaeb740: b.eq            #0xaebbac
    // 0xaeb744: r0 = of()
    //     0xaeb744: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaeb748: LoadField: r1 = r0->field_87
    //     0xaeb748: ldur            w1, [x0, #0x87]
    // 0xaeb74c: DecompressPointer r1
    //     0xaeb74c: add             x1, x1, HEAP, lsl #32
    // 0xaeb750: LoadField: r0 = r1->field_7
    //     0xaeb750: ldur            w0, [x1, #7]
    // 0xaeb754: DecompressPointer r0
    //     0xaeb754: add             x0, x0, HEAP, lsl #32
    // 0xaeb758: r16 = 16.000000
    //     0xaeb758: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaeb75c: ldr             x16, [x16, #0x188]
    // 0xaeb760: r30 = Instance_Color
    //     0xaeb760: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaeb764: stp             lr, x16, [SP]
    // 0xaeb768: mov             x1, x0
    // 0xaeb76c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaeb76c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaeb770: ldr             x4, [x4, #0xaa0]
    // 0xaeb774: r0 = copyWith()
    //     0xaeb774: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaeb778: stur            x0, [fp, #-0x30]
    // 0xaeb77c: r0 = TextSpan()
    //     0xaeb77c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaeb780: mov             x2, x0
    // 0xaeb784: r0 = "BUMPER OFFER\n"
    //     0xaeb784: add             x0, PP, #0x48, lsl #12  ; [pp+0x48338] "BUMPER OFFER\n"
    //     0xaeb788: ldr             x0, [x0, #0x338]
    // 0xaeb78c: stur            x2, [fp, #-0x38]
    // 0xaeb790: StoreField: r2->field_b = r0
    //     0xaeb790: stur            w0, [x2, #0xb]
    // 0xaeb794: r0 = Instance__DeferringMouseCursor
    //     0xaeb794: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaeb798: ArrayStore: r2[0] = r0  ; List_4
    //     0xaeb798: stur            w0, [x2, #0x17]
    // 0xaeb79c: ldur            x1, [fp, #-0x30]
    // 0xaeb7a0: StoreField: r2->field_7 = r1
    //     0xaeb7a0: stur            w1, [x2, #7]
    // 0xaeb7a4: ldur            x3, [fp, #-0x28]
    // 0xaeb7a8: LoadField: r1 = r3->field_f
    //     0xaeb7a8: ldur            w1, [x3, #0xf]
    // 0xaeb7ac: DecompressPointer r1
    //     0xaeb7ac: add             x1, x1, HEAP, lsl #32
    // 0xaeb7b0: cmp             w1, NULL
    // 0xaeb7b4: b.eq            #0xaebbb0
    // 0xaeb7b8: r0 = of()
    //     0xaeb7b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaeb7bc: LoadField: r1 = r0->field_87
    //     0xaeb7bc: ldur            w1, [x0, #0x87]
    // 0xaeb7c0: DecompressPointer r1
    //     0xaeb7c0: add             x1, x1, HEAP, lsl #32
    // 0xaeb7c4: LoadField: r0 = r1->field_2b
    //     0xaeb7c4: ldur            w0, [x1, #0x2b]
    // 0xaeb7c8: DecompressPointer r0
    //     0xaeb7c8: add             x0, x0, HEAP, lsl #32
    // 0xaeb7cc: r16 = 12.000000
    //     0xaeb7cc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaeb7d0: ldr             x16, [x16, #0x9e8]
    // 0xaeb7d4: r30 = Instance_Color
    //     0xaeb7d4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaeb7d8: stp             lr, x16, [SP]
    // 0xaeb7dc: mov             x1, x0
    // 0xaeb7e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaeb7e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaeb7e4: ldr             x4, [x4, #0xaa0]
    // 0xaeb7e8: r0 = copyWith()
    //     0xaeb7e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaeb7ec: stur            x0, [fp, #-0x30]
    // 0xaeb7f0: r0 = TextSpan()
    //     0xaeb7f0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaeb7f4: mov             x3, x0
    // 0xaeb7f8: r0 = "Unlocked from your last order"
    //     0xaeb7f8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48340] "Unlocked from your last order"
    //     0xaeb7fc: ldr             x0, [x0, #0x340]
    // 0xaeb800: stur            x3, [fp, #-0x40]
    // 0xaeb804: StoreField: r3->field_b = r0
    //     0xaeb804: stur            w0, [x3, #0xb]
    // 0xaeb808: r0 = Instance__DeferringMouseCursor
    //     0xaeb808: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaeb80c: ArrayStore: r3[0] = r0  ; List_4
    //     0xaeb80c: stur            w0, [x3, #0x17]
    // 0xaeb810: ldur            x1, [fp, #-0x30]
    // 0xaeb814: StoreField: r3->field_7 = r1
    //     0xaeb814: stur            w1, [x3, #7]
    // 0xaeb818: r1 = Null
    //     0xaeb818: mov             x1, NULL
    // 0xaeb81c: r2 = 4
    //     0xaeb81c: movz            x2, #0x4
    // 0xaeb820: r0 = AllocateArray()
    //     0xaeb820: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaeb824: mov             x2, x0
    // 0xaeb828: ldur            x0, [fp, #-0x38]
    // 0xaeb82c: stur            x2, [fp, #-0x30]
    // 0xaeb830: StoreField: r2->field_f = r0
    //     0xaeb830: stur            w0, [x2, #0xf]
    // 0xaeb834: ldur            x0, [fp, #-0x40]
    // 0xaeb838: StoreField: r2->field_13 = r0
    //     0xaeb838: stur            w0, [x2, #0x13]
    // 0xaeb83c: r1 = <InlineSpan>
    //     0xaeb83c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xaeb840: ldr             x1, [x1, #0xe40]
    // 0xaeb844: r0 = AllocateGrowableArray()
    //     0xaeb844: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaeb848: mov             x1, x0
    // 0xaeb84c: ldur            x0, [fp, #-0x30]
    // 0xaeb850: stur            x1, [fp, #-0x38]
    // 0xaeb854: StoreField: r1->field_f = r0
    //     0xaeb854: stur            w0, [x1, #0xf]
    // 0xaeb858: r2 = 4
    //     0xaeb858: movz            x2, #0x4
    // 0xaeb85c: StoreField: r1->field_b = r2
    //     0xaeb85c: stur            w2, [x1, #0xb]
    // 0xaeb860: r0 = TextSpan()
    //     0xaeb860: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaeb864: mov             x1, x0
    // 0xaeb868: ldur            x0, [fp, #-0x38]
    // 0xaeb86c: stur            x1, [fp, #-0x30]
    // 0xaeb870: StoreField: r1->field_f = r0
    //     0xaeb870: stur            w0, [x1, #0xf]
    // 0xaeb874: r0 = Instance__DeferringMouseCursor
    //     0xaeb874: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaeb878: ArrayStore: r1[0] = r0  ; List_4
    //     0xaeb878: stur            w0, [x1, #0x17]
    // 0xaeb87c: r0 = RichText()
    //     0xaeb87c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xaeb880: mov             x1, x0
    // 0xaeb884: ldur            x2, [fp, #-0x30]
    // 0xaeb888: stur            x0, [fp, #-0x30]
    // 0xaeb88c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaeb88c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaeb890: r0 = RichText()
    //     0xaeb890: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xaeb894: ldur            x0, [fp, #-0x28]
    // 0xaeb898: LoadField: r1 = r0->field_b
    //     0xaeb898: ldur            w1, [x0, #0xb]
    // 0xaeb89c: DecompressPointer r1
    //     0xaeb89c: add             x1, x1, HEAP, lsl #32
    // 0xaeb8a0: cmp             w1, NULL
    // 0xaeb8a4: b.eq            #0xaebbb4
    // 0xaeb8a8: LoadField: r2 = r1->field_33
    //     0xaeb8a8: ldur            w2, [x1, #0x33]
    // 0xaeb8ac: DecompressPointer r2
    //     0xaeb8ac: add             x2, x2, HEAP, lsl #32
    // 0xaeb8b0: cmp             w2, NULL
    // 0xaeb8b4: b.ne            #0xaeb8c0
    // 0xaeb8b8: r4 = Null
    //     0xaeb8b8: mov             x4, NULL
    // 0xaeb8bc: b               #0xaeb8cc
    // 0xaeb8c0: LoadField: r1 = r2->field_7
    //     0xaeb8c0: ldur            w1, [x2, #7]
    // 0xaeb8c4: DecompressPointer r1
    //     0xaeb8c4: add             x1, x1, HEAP, lsl #32
    // 0xaeb8c8: mov             x4, x1
    // 0xaeb8cc: ldur            x3, [fp, #-0x30]
    // 0xaeb8d0: stur            x4, [fp, #-0x38]
    // 0xaeb8d4: r1 = Null
    //     0xaeb8d4: mov             x1, NULL
    // 0xaeb8d8: r2 = 4
    //     0xaeb8d8: movz            x2, #0x4
    // 0xaeb8dc: r0 = AllocateArray()
    //     0xaeb8dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaeb8e0: mov             x1, x0
    // 0xaeb8e4: ldur            x0, [fp, #-0x38]
    // 0xaeb8e8: StoreField: r1->field_f = r0
    //     0xaeb8e8: stur            w0, [x1, #0xf]
    // 0xaeb8ec: r16 = "\n"
    //     0xaeb8ec: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0xaeb8f0: StoreField: r1->field_13 = r16
    //     0xaeb8f0: stur            w16, [x1, #0x13]
    // 0xaeb8f4: str             x1, [SP]
    // 0xaeb8f8: r0 = _interpolate()
    //     0xaeb8f8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xaeb8fc: mov             x2, x0
    // 0xaeb900: ldur            x0, [fp, #-0x28]
    // 0xaeb904: stur            x2, [fp, #-0x38]
    // 0xaeb908: LoadField: r1 = r0->field_f
    //     0xaeb908: ldur            w1, [x0, #0xf]
    // 0xaeb90c: DecompressPointer r1
    //     0xaeb90c: add             x1, x1, HEAP, lsl #32
    // 0xaeb910: cmp             w1, NULL
    // 0xaeb914: b.eq            #0xaebbb8
    // 0xaeb918: r0 = of()
    //     0xaeb918: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaeb91c: LoadField: r1 = r0->field_87
    //     0xaeb91c: ldur            w1, [x0, #0x87]
    // 0xaeb920: DecompressPointer r1
    //     0xaeb920: add             x1, x1, HEAP, lsl #32
    // 0xaeb924: LoadField: r0 = r1->field_23
    //     0xaeb924: ldur            w0, [x1, #0x23]
    // 0xaeb928: DecompressPointer r0
    //     0xaeb928: add             x0, x0, HEAP, lsl #32
    // 0xaeb92c: r16 = 32.000000
    //     0xaeb92c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xaeb930: ldr             x16, [x16, #0x848]
    // 0xaeb934: r30 = Instance_Color
    //     0xaeb934: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaeb938: stp             lr, x16, [SP]
    // 0xaeb93c: mov             x1, x0
    // 0xaeb940: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaeb940: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaeb944: ldr             x4, [x4, #0xaa0]
    // 0xaeb948: r0 = copyWith()
    //     0xaeb948: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaeb94c: mov             x2, x0
    // 0xaeb950: ldur            x0, [fp, #-0x28]
    // 0xaeb954: stur            x2, [fp, #-0x40]
    // 0xaeb958: LoadField: r1 = r0->field_f
    //     0xaeb958: ldur            w1, [x0, #0xf]
    // 0xaeb95c: DecompressPointer r1
    //     0xaeb95c: add             x1, x1, HEAP, lsl #32
    // 0xaeb960: cmp             w1, NULL
    // 0xaeb964: b.eq            #0xaebbbc
    // 0xaeb968: r0 = of()
    //     0xaeb968: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaeb96c: LoadField: r1 = r0->field_87
    //     0xaeb96c: ldur            w1, [x0, #0x87]
    // 0xaeb970: DecompressPointer r1
    //     0xaeb970: add             x1, x1, HEAP, lsl #32
    // 0xaeb974: LoadField: r0 = r1->field_2b
    //     0xaeb974: ldur            w0, [x1, #0x2b]
    // 0xaeb978: DecompressPointer r0
    //     0xaeb978: add             x0, x0, HEAP, lsl #32
    // 0xaeb97c: r16 = Instance_Color
    //     0xaeb97c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaeb980: r30 = 16.000000
    //     0xaeb980: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaeb984: ldr             lr, [lr, #0x188]
    // 0xaeb988: stp             lr, x16, [SP]
    // 0xaeb98c: mov             x1, x0
    // 0xaeb990: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xaeb990: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xaeb994: ldr             x4, [x4, #0x9b8]
    // 0xaeb998: r0 = copyWith()
    //     0xaeb998: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaeb99c: stur            x0, [fp, #-0x28]
    // 0xaeb9a0: r0 = TextSpan()
    //     0xaeb9a0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaeb9a4: mov             x3, x0
    // 0xaeb9a8: r0 = "OFF"
    //     0xaeb9a8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48348] "OFF"
    //     0xaeb9ac: ldr             x0, [x0, #0x348]
    // 0xaeb9b0: stur            x3, [fp, #-0x48]
    // 0xaeb9b4: StoreField: r3->field_b = r0
    //     0xaeb9b4: stur            w0, [x3, #0xb]
    // 0xaeb9b8: r0 = Instance__DeferringMouseCursor
    //     0xaeb9b8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaeb9bc: ArrayStore: r3[0] = r0  ; List_4
    //     0xaeb9bc: stur            w0, [x3, #0x17]
    // 0xaeb9c0: ldur            x1, [fp, #-0x28]
    // 0xaeb9c4: StoreField: r3->field_7 = r1
    //     0xaeb9c4: stur            w1, [x3, #7]
    // 0xaeb9c8: r1 = Null
    //     0xaeb9c8: mov             x1, NULL
    // 0xaeb9cc: r2 = 2
    //     0xaeb9cc: movz            x2, #0x2
    // 0xaeb9d0: r0 = AllocateArray()
    //     0xaeb9d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaeb9d4: mov             x2, x0
    // 0xaeb9d8: ldur            x0, [fp, #-0x48]
    // 0xaeb9dc: stur            x2, [fp, #-0x28]
    // 0xaeb9e0: StoreField: r2->field_f = r0
    //     0xaeb9e0: stur            w0, [x2, #0xf]
    // 0xaeb9e4: r1 = <InlineSpan>
    //     0xaeb9e4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xaeb9e8: ldr             x1, [x1, #0xe40]
    // 0xaeb9ec: r0 = AllocateGrowableArray()
    //     0xaeb9ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaeb9f0: mov             x1, x0
    // 0xaeb9f4: ldur            x0, [fp, #-0x28]
    // 0xaeb9f8: stur            x1, [fp, #-0x48]
    // 0xaeb9fc: StoreField: r1->field_f = r0
    //     0xaeb9fc: stur            w0, [x1, #0xf]
    // 0xaeba00: r0 = 2
    //     0xaeba00: movz            x0, #0x2
    // 0xaeba04: StoreField: r1->field_b = r0
    //     0xaeba04: stur            w0, [x1, #0xb]
    // 0xaeba08: r0 = TextSpan()
    //     0xaeba08: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaeba0c: mov             x1, x0
    // 0xaeba10: ldur            x0, [fp, #-0x38]
    // 0xaeba14: stur            x1, [fp, #-0x28]
    // 0xaeba18: StoreField: r1->field_b = r0
    //     0xaeba18: stur            w0, [x1, #0xb]
    // 0xaeba1c: ldur            x0, [fp, #-0x48]
    // 0xaeba20: StoreField: r1->field_f = r0
    //     0xaeba20: stur            w0, [x1, #0xf]
    // 0xaeba24: r0 = Instance__DeferringMouseCursor
    //     0xaeba24: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaeba28: ArrayStore: r1[0] = r0  ; List_4
    //     0xaeba28: stur            w0, [x1, #0x17]
    // 0xaeba2c: ldur            x0, [fp, #-0x40]
    // 0xaeba30: StoreField: r1->field_7 = r0
    //     0xaeba30: stur            w0, [x1, #7]
    // 0xaeba34: r0 = RichText()
    //     0xaeba34: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xaeba38: stur            x0, [fp, #-0x38]
    // 0xaeba3c: r16 = Instance_TextAlign
    //     0xaeba3c: ldr             x16, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xaeba40: str             x16, [SP]
    // 0xaeba44: mov             x1, x0
    // 0xaeba48: ldur            x2, [fp, #-0x28]
    // 0xaeba4c: r4 = const [0, 0x3, 0x1, 0x2, textAlign, 0x2, null]
    //     0xaeba4c: add             x4, PP, #0x48, lsl #12  ; [pp+0x48350] List(7) [0, 0x3, 0x1, 0x2, "textAlign", 0x2, Null]
    //     0xaeba50: ldr             x4, [x4, #0x350]
    // 0xaeba54: r0 = RichText()
    //     0xaeba54: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xaeba58: r1 = Null
    //     0xaeba58: mov             x1, NULL
    // 0xaeba5c: r2 = 6
    //     0xaeba5c: movz            x2, #0x6
    // 0xaeba60: r0 = AllocateArray()
    //     0xaeba60: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaeba64: mov             x2, x0
    // 0xaeba68: ldur            x0, [fp, #-0x30]
    // 0xaeba6c: stur            x2, [fp, #-0x28]
    // 0xaeba70: StoreField: r2->field_f = r0
    //     0xaeba70: stur            w0, [x2, #0xf]
    // 0xaeba74: r16 = Instance_VerticalDivider
    //     0xaeba74: add             x16, PP, #0x48, lsl #12  ; [pp+0x48760] Obj!VerticalDivider@d66b51
    //     0xaeba78: ldr             x16, [x16, #0x760]
    // 0xaeba7c: StoreField: r2->field_13 = r16
    //     0xaeba7c: stur            w16, [x2, #0x13]
    // 0xaeba80: ldur            x0, [fp, #-0x38]
    // 0xaeba84: ArrayStore: r2[0] = r0  ; List_4
    //     0xaeba84: stur            w0, [x2, #0x17]
    // 0xaeba88: r1 = <Widget>
    //     0xaeba88: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaeba8c: r0 = AllocateGrowableArray()
    //     0xaeba8c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaeba90: mov             x1, x0
    // 0xaeba94: ldur            x0, [fp, #-0x28]
    // 0xaeba98: stur            x1, [fp, #-0x30]
    // 0xaeba9c: StoreField: r1->field_f = r0
    //     0xaeba9c: stur            w0, [x1, #0xf]
    // 0xaebaa0: r0 = 6
    //     0xaebaa0: movz            x0, #0x6
    // 0xaebaa4: StoreField: r1->field_b = r0
    //     0xaebaa4: stur            w0, [x1, #0xb]
    // 0xaebaa8: r0 = Row()
    //     0xaebaa8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaebaac: mov             x1, x0
    // 0xaebab0: r0 = Instance_Axis
    //     0xaebab0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaebab4: stur            x1, [fp, #-0x28]
    // 0xaebab8: StoreField: r1->field_f = r0
    //     0xaebab8: stur            w0, [x1, #0xf]
    // 0xaebabc: r0 = Instance_MainAxisAlignment
    //     0xaebabc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xaebac0: ldr             x0, [x0, #0xa8]
    // 0xaebac4: StoreField: r1->field_13 = r0
    //     0xaebac4: stur            w0, [x1, #0x13]
    // 0xaebac8: r0 = Instance_MainAxisSize
    //     0xaebac8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaebacc: ldr             x0, [x0, #0xa10]
    // 0xaebad0: ArrayStore: r1[0] = r0  ; List_4
    //     0xaebad0: stur            w0, [x1, #0x17]
    // 0xaebad4: r0 = Instance_CrossAxisAlignment
    //     0xaebad4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaebad8: ldr             x0, [x0, #0xa18]
    // 0xaebadc: StoreField: r1->field_1b = r0
    //     0xaebadc: stur            w0, [x1, #0x1b]
    // 0xaebae0: r0 = Instance_VerticalDirection
    //     0xaebae0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaebae4: ldr             x0, [x0, #0xa20]
    // 0xaebae8: StoreField: r1->field_23 = r0
    //     0xaebae8: stur            w0, [x1, #0x23]
    // 0xaebaec: r0 = Instance_Clip
    //     0xaebaec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaebaf0: ldr             x0, [x0, #0x38]
    // 0xaebaf4: StoreField: r1->field_2b = r0
    //     0xaebaf4: stur            w0, [x1, #0x2b]
    // 0xaebaf8: StoreField: r1->field_2f = rZR
    //     0xaebaf8: stur            xzr, [x1, #0x2f]
    // 0xaebafc: ldur            x0, [fp, #-0x30]
    // 0xaebb00: StoreField: r1->field_b = r0
    //     0xaebb00: stur            w0, [x1, #0xb]
    // 0xaebb04: r0 = Padding()
    //     0xaebb04: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaebb08: mov             x1, x0
    // 0xaebb0c: r0 = Instance_EdgeInsets
    //     0xaebb0c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48358] Obj!EdgeInsets@d57411
    //     0xaebb10: ldr             x0, [x0, #0x358]
    // 0xaebb14: stur            x1, [fp, #-0x30]
    // 0xaebb18: StoreField: r1->field_f = r0
    //     0xaebb18: stur            w0, [x1, #0xf]
    // 0xaebb1c: ldur            x0, [fp, #-0x28]
    // 0xaebb20: StoreField: r1->field_b = r0
    //     0xaebb20: stur            w0, [x1, #0xb]
    // 0xaebb24: r0 = IntrinsicHeight()
    //     0xaebb24: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xaebb28: mov             x1, x0
    // 0xaebb2c: ldur            x0, [fp, #-0x30]
    // 0xaebb30: stur            x1, [fp, #-0x28]
    // 0xaebb34: StoreField: r1->field_b = r0
    //     0xaebb34: stur            w0, [x1, #0xb]
    // 0xaebb38: r0 = Container()
    //     0xaebb38: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaebb3c: stur            x0, [fp, #-0x30]
    // 0xaebb40: r16 = 100.000000
    //     0xaebb40: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xaebb44: ldur            lr, [fp, #-0x20]
    // 0xaebb48: stp             lr, x16, [SP, #8]
    // 0xaebb4c: ldur            x16, [fp, #-0x28]
    // 0xaebb50: str             x16, [SP]
    // 0xaebb54: mov             x1, x0
    // 0xaebb58: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xaebb58: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xaebb5c: ldr             x4, [x4, #0xc78]
    // 0xaebb60: r0 = Container()
    //     0xaebb60: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaebb64: r1 = <Path>
    //     0xaebb64: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xaebb68: ldr             x1, [x1, #0xd30]
    // 0xaebb6c: r0 = MovieTicketClipper()
    //     0xaebb6c: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0xaebb70: stur            x0, [fp, #-0x20]
    // 0xaebb74: r0 = ClipPath()
    //     0xaebb74: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xaebb78: ldur            x1, [fp, #-0x20]
    // 0xaebb7c: StoreField: r0->field_f = r1
    //     0xaebb7c: stur            w1, [x0, #0xf]
    // 0xaebb80: r1 = Instance_Clip
    //     0xaebb80: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xaebb84: ldr             x1, [x1, #0x138]
    // 0xaebb88: StoreField: r0->field_13 = r1
    //     0xaebb88: stur            w1, [x0, #0x13]
    // 0xaebb8c: ldur            x1, [fp, #-0x30]
    // 0xaebb90: StoreField: r0->field_b = r1
    //     0xaebb90: stur            w1, [x0, #0xb]
    // 0xaebb94: LeaveFrame
    //     0xaebb94: mov             SP, fp
    //     0xaebb98: ldp             fp, lr, [SP], #0x10
    // 0xaebb9c: ret
    //     0xaebb9c: ret             
    // 0xaebba0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaebba0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaebba4: b               #0xaeb3dc
    // 0xaebba8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaebba8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaebbac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaebbac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaebbb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaebbb0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaebbb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaebbb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaebbb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaebbb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaebbbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaebbbc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildHeader(/* No info */) {
    // ** addr: 0xaebbc0, size: 0x6a8
    // 0xaebbc0: EnterFrame
    //     0xaebbc0: stp             fp, lr, [SP, #-0x10]!
    //     0xaebbc4: mov             fp, SP
    // 0xaebbc8: AllocStack(0x68)
    //     0xaebbc8: sub             SP, SP, #0x68
    // 0xaebbcc: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */)
    //     0xaebbcc: stur            x1, [fp, #-8]
    // 0xaebbd0: CheckStackOverflow
    //     0xaebbd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaebbd4: cmp             SP, x16
    //     0xaebbd8: b.ls            #0xaec23c
    // 0xaebbdc: r1 = 1
    //     0xaebbdc: movz            x1, #0x1
    // 0xaebbe0: r0 = AllocateContext()
    //     0xaebbe0: bl              #0x16f6108  ; AllocateContextStub
    // 0xaebbe4: mov             x3, x0
    // 0xaebbe8: ldur            x0, [fp, #-8]
    // 0xaebbec: stur            x3, [fp, #-0x18]
    // 0xaebbf0: StoreField: r3->field_f = r0
    //     0xaebbf0: stur            w0, [x3, #0xf]
    // 0xaebbf4: LoadField: r1 = r0->field_b
    //     0xaebbf4: ldur            w1, [x0, #0xb]
    // 0xaebbf8: DecompressPointer r1
    //     0xaebbf8: add             x1, x1, HEAP, lsl #32
    // 0xaebbfc: cmp             w1, NULL
    // 0xaebc00: b.eq            #0xaec244
    // 0xaebc04: LoadField: r2 = r1->field_1b
    //     0xaebc04: ldur            w2, [x1, #0x1b]
    // 0xaebc08: DecompressPointer r2
    //     0xaebc08: add             x2, x2, HEAP, lsl #32
    // 0xaebc0c: LoadField: r1 = r2->field_7
    //     0xaebc0c: ldur            w1, [x2, #7]
    // 0xaebc10: DecompressPointer r1
    //     0xaebc10: add             x1, x1, HEAP, lsl #32
    // 0xaebc14: cmp             w1, NULL
    // 0xaebc18: b.ne            #0xaebc24
    // 0xaebc1c: r1 = Instance_TitleAlignment
    //     0xaebc1c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xaebc20: ldr             x1, [x1, #0x518]
    // 0xaebc24: r16 = Instance_TitleAlignment
    //     0xaebc24: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xaebc28: ldr             x16, [x16, #0x520]
    // 0xaebc2c: cmp             w1, w16
    // 0xaebc30: b.ne            #0xaebc40
    // 0xaebc34: r4 = Instance_CrossAxisAlignment
    //     0xaebc34: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xaebc38: ldr             x4, [x4, #0xc68]
    // 0xaebc3c: b               #0xaebc64
    // 0xaebc40: r16 = Instance_TitleAlignment
    //     0xaebc40: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xaebc44: ldr             x16, [x16, #0x518]
    // 0xaebc48: cmp             w1, w16
    // 0xaebc4c: b.ne            #0xaebc5c
    // 0xaebc50: r4 = Instance_CrossAxisAlignment
    //     0xaebc50: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaebc54: ldr             x4, [x4, #0x890]
    // 0xaebc58: b               #0xaebc64
    // 0xaebc5c: r4 = Instance_CrossAxisAlignment
    //     0xaebc5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaebc60: ldr             x4, [x4, #0xa18]
    // 0xaebc64: stur            x4, [fp, #-0x10]
    // 0xaebc68: r1 = <Widget>
    //     0xaebc68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaebc6c: r2 = 0
    //     0xaebc6c: movz            x2, #0
    // 0xaebc70: r0 = _GrowableList()
    //     0xaebc70: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xaebc74: mov             x2, x0
    // 0xaebc78: ldur            x1, [fp, #-8]
    // 0xaebc7c: stur            x2, [fp, #-0x20]
    // 0xaebc80: LoadField: r0 = r1->field_b
    //     0xaebc80: ldur            w0, [x1, #0xb]
    // 0xaebc84: DecompressPointer r0
    //     0xaebc84: add             x0, x0, HEAP, lsl #32
    // 0xaebc88: cmp             w0, NULL
    // 0xaebc8c: b.eq            #0xaec248
    // 0xaebc90: LoadField: r3 = r0->field_f
    //     0xaebc90: ldur            w3, [x0, #0xf]
    // 0xaebc94: DecompressPointer r3
    //     0xaebc94: add             x3, x3, HEAP, lsl #32
    // 0xaebc98: cmp             w3, NULL
    // 0xaebc9c: b.ne            #0xaebca8
    // 0xaebca0: r0 = Null
    //     0xaebca0: mov             x0, NULL
    // 0xaebca4: b               #0xaebcc0
    // 0xaebca8: LoadField: r0 = r3->field_7
    //     0xaebca8: ldur            w0, [x3, #7]
    // 0xaebcac: cbnz            w0, #0xaebcb8
    // 0xaebcb0: r4 = false
    //     0xaebcb0: add             x4, NULL, #0x30  ; false
    // 0xaebcb4: b               #0xaebcbc
    // 0xaebcb8: r4 = true
    //     0xaebcb8: add             x4, NULL, #0x20  ; true
    // 0xaebcbc: mov             x0, x4
    // 0xaebcc0: cmp             w0, NULL
    // 0xaebcc4: b.eq            #0xaebe80
    // 0xaebcc8: tbnz            w0, #4, #0xaebe80
    // 0xaebccc: cmp             w3, NULL
    // 0xaebcd0: b.ne            #0xaebcdc
    // 0xaebcd4: r0 = Null
    //     0xaebcd4: mov             x0, NULL
    // 0xaebcd8: b               #0xaebcf4
    // 0xaebcdc: r0 = LoadClassIdInstr(r3)
    //     0xaebcdc: ldur            x0, [x3, #-1]
    //     0xaebce0: ubfx            x0, x0, #0xc, #0x14
    // 0xaebce4: str             x3, [SP]
    // 0xaebce8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xaebce8: sub             lr, x0, #1, lsl #12
    //     0xaebcec: ldr             lr, [x21, lr, lsl #3]
    //     0xaebcf0: blr             lr
    // 0xaebcf4: cmp             w0, NULL
    // 0xaebcf8: b.ne            #0xaebd04
    // 0xaebcfc: r2 = ""
    //     0xaebcfc: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaebd00: b               #0xaebd08
    // 0xaebd04: mov             x2, x0
    // 0xaebd08: ldur            x0, [fp, #-8]
    // 0xaebd0c: stur            x2, [fp, #-0x30]
    // 0xaebd10: LoadField: r1 = r0->field_b
    //     0xaebd10: ldur            w1, [x0, #0xb]
    // 0xaebd14: DecompressPointer r1
    //     0xaebd14: add             x1, x1, HEAP, lsl #32
    // 0xaebd18: cmp             w1, NULL
    // 0xaebd1c: b.eq            #0xaec24c
    // 0xaebd20: LoadField: r3 = r1->field_1b
    //     0xaebd20: ldur            w3, [x1, #0x1b]
    // 0xaebd24: DecompressPointer r3
    //     0xaebd24: add             x3, x3, HEAP, lsl #32
    // 0xaebd28: LoadField: r1 = r3->field_7
    //     0xaebd28: ldur            w1, [x3, #7]
    // 0xaebd2c: DecompressPointer r1
    //     0xaebd2c: add             x1, x1, HEAP, lsl #32
    // 0xaebd30: cmp             w1, NULL
    // 0xaebd34: b.ne            #0xaebd40
    // 0xaebd38: r1 = Instance_TitleAlignment
    //     0xaebd38: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xaebd3c: ldr             x1, [x1, #0x518]
    // 0xaebd40: r16 = Instance_TitleAlignment
    //     0xaebd40: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xaebd44: ldr             x16, [x16, #0x520]
    // 0xaebd48: cmp             w1, w16
    // 0xaebd4c: b.ne            #0xaebd58
    // 0xaebd50: r4 = Instance_TextAlign
    //     0xaebd50: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xaebd54: b               #0xaebd74
    // 0xaebd58: r16 = Instance_TitleAlignment
    //     0xaebd58: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xaebd5c: ldr             x16, [x16, #0x518]
    // 0xaebd60: cmp             w1, w16
    // 0xaebd64: b.ne            #0xaebd70
    // 0xaebd68: r4 = Instance_TextAlign
    //     0xaebd68: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xaebd6c: b               #0xaebd74
    // 0xaebd70: r4 = Instance_TextAlign
    //     0xaebd70: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xaebd74: ldur            x3, [fp, #-0x20]
    // 0xaebd78: stur            x4, [fp, #-0x28]
    // 0xaebd7c: LoadField: r1 = r0->field_f
    //     0xaebd7c: ldur            w1, [x0, #0xf]
    // 0xaebd80: DecompressPointer r1
    //     0xaebd80: add             x1, x1, HEAP, lsl #32
    // 0xaebd84: cmp             w1, NULL
    // 0xaebd88: b.eq            #0xaec250
    // 0xaebd8c: r0 = of()
    //     0xaebd8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaebd90: LoadField: r1 = r0->field_87
    //     0xaebd90: ldur            w1, [x0, #0x87]
    // 0xaebd94: DecompressPointer r1
    //     0xaebd94: add             x1, x1, HEAP, lsl #32
    // 0xaebd98: LoadField: r0 = r1->field_23
    //     0xaebd98: ldur            w0, [x1, #0x23]
    // 0xaebd9c: DecompressPointer r0
    //     0xaebd9c: add             x0, x0, HEAP, lsl #32
    // 0xaebda0: r16 = 32.000000
    //     0xaebda0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xaebda4: ldr             x16, [x16, #0x848]
    // 0xaebda8: r30 = Instance_Color
    //     0xaebda8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaebdac: stp             lr, x16, [SP]
    // 0xaebdb0: mov             x1, x0
    // 0xaebdb4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaebdb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaebdb8: ldr             x4, [x4, #0xaa0]
    // 0xaebdbc: r0 = copyWith()
    //     0xaebdbc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaebdc0: stur            x0, [fp, #-0x38]
    // 0xaebdc4: r0 = Text()
    //     0xaebdc4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaebdc8: mov             x1, x0
    // 0xaebdcc: ldur            x0, [fp, #-0x30]
    // 0xaebdd0: stur            x1, [fp, #-0x40]
    // 0xaebdd4: StoreField: r1->field_b = r0
    //     0xaebdd4: stur            w0, [x1, #0xb]
    // 0xaebdd8: ldur            x0, [fp, #-0x38]
    // 0xaebddc: StoreField: r1->field_13 = r0
    //     0xaebddc: stur            w0, [x1, #0x13]
    // 0xaebde0: ldur            x0, [fp, #-0x28]
    // 0xaebde4: StoreField: r1->field_1b = r0
    //     0xaebde4: stur            w0, [x1, #0x1b]
    // 0xaebde8: r0 = Padding()
    //     0xaebde8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaebdec: mov             x2, x0
    // 0xaebdf0: r0 = Instance_EdgeInsets
    //     0xaebdf0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xaebdf4: ldr             x0, [x0, #0x100]
    // 0xaebdf8: stur            x2, [fp, #-0x28]
    // 0xaebdfc: StoreField: r2->field_f = r0
    //     0xaebdfc: stur            w0, [x2, #0xf]
    // 0xaebe00: ldur            x0, [fp, #-0x40]
    // 0xaebe04: StoreField: r2->field_b = r0
    //     0xaebe04: stur            w0, [x2, #0xb]
    // 0xaebe08: ldur            x0, [fp, #-0x20]
    // 0xaebe0c: LoadField: r1 = r0->field_b
    //     0xaebe0c: ldur            w1, [x0, #0xb]
    // 0xaebe10: LoadField: r3 = r0->field_f
    //     0xaebe10: ldur            w3, [x0, #0xf]
    // 0xaebe14: DecompressPointer r3
    //     0xaebe14: add             x3, x3, HEAP, lsl #32
    // 0xaebe18: LoadField: r4 = r3->field_b
    //     0xaebe18: ldur            w4, [x3, #0xb]
    // 0xaebe1c: r3 = LoadInt32Instr(r1)
    //     0xaebe1c: sbfx            x3, x1, #1, #0x1f
    // 0xaebe20: stur            x3, [fp, #-0x48]
    // 0xaebe24: r1 = LoadInt32Instr(r4)
    //     0xaebe24: sbfx            x1, x4, #1, #0x1f
    // 0xaebe28: cmp             x3, x1
    // 0xaebe2c: b.ne            #0xaebe38
    // 0xaebe30: mov             x1, x0
    // 0xaebe34: r0 = _growToNextCapacity()
    //     0xaebe34: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaebe38: ldur            x2, [fp, #-0x20]
    // 0xaebe3c: ldur            x3, [fp, #-0x48]
    // 0xaebe40: add             x0, x3, #1
    // 0xaebe44: lsl             x1, x0, #1
    // 0xaebe48: StoreField: r2->field_b = r1
    //     0xaebe48: stur            w1, [x2, #0xb]
    // 0xaebe4c: LoadField: r1 = r2->field_f
    //     0xaebe4c: ldur            w1, [x2, #0xf]
    // 0xaebe50: DecompressPointer r1
    //     0xaebe50: add             x1, x1, HEAP, lsl #32
    // 0xaebe54: ldur            x0, [fp, #-0x28]
    // 0xaebe58: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaebe58: add             x25, x1, x3, lsl #2
    //     0xaebe5c: add             x25, x25, #0xf
    //     0xaebe60: str             w0, [x25]
    //     0xaebe64: tbz             w0, #0, #0xaebe80
    //     0xaebe68: ldurb           w16, [x1, #-1]
    //     0xaebe6c: ldurb           w17, [x0, #-1]
    //     0xaebe70: and             x16, x17, x16, lsr #2
    //     0xaebe74: tst             x16, HEAP, lsr #32
    //     0xaebe78: b.eq            #0xaebe80
    //     0xaebe7c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaebe80: LoadField: r0 = r2->field_b
    //     0xaebe80: ldur            w0, [x2, #0xb]
    // 0xaebe84: LoadField: r1 = r2->field_f
    //     0xaebe84: ldur            w1, [x2, #0xf]
    // 0xaebe88: DecompressPointer r1
    //     0xaebe88: add             x1, x1, HEAP, lsl #32
    // 0xaebe8c: LoadField: r3 = r1->field_b
    //     0xaebe8c: ldur            w3, [x1, #0xb]
    // 0xaebe90: r4 = LoadInt32Instr(r0)
    //     0xaebe90: sbfx            x4, x0, #1, #0x1f
    // 0xaebe94: stur            x4, [fp, #-0x48]
    // 0xaebe98: r0 = LoadInt32Instr(r3)
    //     0xaebe98: sbfx            x0, x3, #1, #0x1f
    // 0xaebe9c: cmp             x4, x0
    // 0xaebea0: b.ne            #0xaebeac
    // 0xaebea4: mov             x1, x2
    // 0xaebea8: r0 = _growToNextCapacity()
    //     0xaebea8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaebeac: ldur            x2, [fp, #-8]
    // 0xaebeb0: ldur            x0, [fp, #-0x20]
    // 0xaebeb4: ldur            x1, [fp, #-0x48]
    // 0xaebeb8: add             x3, x1, #1
    // 0xaebebc: lsl             x4, x3, #1
    // 0xaebec0: StoreField: r0->field_b = r4
    //     0xaebec0: stur            w4, [x0, #0xb]
    // 0xaebec4: LoadField: r4 = r0->field_f
    //     0xaebec4: ldur            w4, [x0, #0xf]
    // 0xaebec8: DecompressPointer r4
    //     0xaebec8: add             x4, x4, HEAP, lsl #32
    // 0xaebecc: add             x5, x4, x1, lsl #2
    // 0xaebed0: r16 = Instance_SizedBox
    //     0xaebed0: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xaebed4: ldr             x16, [x16, #0xc70]
    // 0xaebed8: StoreField: r5->field_f = r16
    //     0xaebed8: stur            w16, [x5, #0xf]
    // 0xaebedc: LoadField: r1 = r2->field_b
    //     0xaebedc: ldur            w1, [x2, #0xb]
    // 0xaebee0: DecompressPointer r1
    //     0xaebee0: add             x1, x1, HEAP, lsl #32
    // 0xaebee4: cmp             w1, NULL
    // 0xaebee8: b.eq            #0xaec254
    // 0xaebeec: LoadField: r5 = r1->field_13
    //     0xaebeec: ldur            w5, [x1, #0x13]
    // 0xaebef0: DecompressPointer r5
    //     0xaebef0: add             x5, x5, HEAP, lsl #32
    // 0xaebef4: cmp             w5, NULL
    // 0xaebef8: b.ne            #0xaebf04
    // 0xaebefc: r1 = Null
    //     0xaebefc: mov             x1, NULL
    // 0xaebf00: b               #0xaebf30
    // 0xaebf04: LoadField: r1 = r5->field_7
    //     0xaebf04: ldur            w1, [x5, #7]
    // 0xaebf08: DecompressPointer r1
    //     0xaebf08: add             x1, x1, HEAP, lsl #32
    // 0xaebf0c: cmp             w1, NULL
    // 0xaebf10: b.ne            #0xaebf1c
    // 0xaebf14: r1 = Null
    //     0xaebf14: mov             x1, NULL
    // 0xaebf18: b               #0xaebf30
    // 0xaebf1c: LoadField: r5 = r1->field_7
    //     0xaebf1c: ldur            w5, [x1, #7]
    // 0xaebf20: cbnz            w5, #0xaebf2c
    // 0xaebf24: r1 = false
    //     0xaebf24: add             x1, NULL, #0x30  ; false
    // 0xaebf28: b               #0xaebf30
    // 0xaebf2c: r1 = true
    //     0xaebf2c: add             x1, NULL, #0x20  ; true
    // 0xaebf30: cmp             w1, NULL
    // 0xaebf34: b.ne            #0xaebf40
    // 0xaebf38: mov             x2, x0
    // 0xaebf3c: b               #0xaec184
    // 0xaebf40: tbnz            w1, #4, #0xaec180
    // 0xaebf44: LoadField: r1 = r2->field_f
    //     0xaebf44: ldur            w1, [x2, #0xf]
    // 0xaebf48: DecompressPointer r1
    //     0xaebf48: add             x1, x1, HEAP, lsl #32
    // 0xaebf4c: cmp             w1, NULL
    // 0xaebf50: b.eq            #0xaec258
    // 0xaebf54: r0 = of()
    //     0xaebf54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaebf58: LoadField: r1 = r0->field_5b
    //     0xaebf58: ldur            w1, [x0, #0x5b]
    // 0xaebf5c: DecompressPointer r1
    //     0xaebf5c: add             x1, x1, HEAP, lsl #32
    // 0xaebf60: stur            x1, [fp, #-0x28]
    // 0xaebf64: r0 = BoxDecoration()
    //     0xaebf64: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaebf68: mov             x2, x0
    // 0xaebf6c: ldur            x0, [fp, #-0x28]
    // 0xaebf70: stur            x2, [fp, #-0x30]
    // 0xaebf74: StoreField: r2->field_7 = r0
    //     0xaebf74: stur            w0, [x2, #7]
    // 0xaebf78: r0 = Instance_BorderRadius
    //     0xaebf78: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xaebf7c: ldr             x0, [x0, #0x460]
    // 0xaebf80: StoreField: r2->field_13 = r0
    //     0xaebf80: stur            w0, [x2, #0x13]
    // 0xaebf84: r0 = Instance_BoxShape
    //     0xaebf84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaebf88: ldr             x0, [x0, #0x80]
    // 0xaebf8c: StoreField: r2->field_23 = r0
    //     0xaebf8c: stur            w0, [x2, #0x23]
    // 0xaebf90: ldur            x1, [fp, #-8]
    // 0xaebf94: LoadField: r3 = r1->field_b
    //     0xaebf94: ldur            w3, [x1, #0xb]
    // 0xaebf98: DecompressPointer r3
    //     0xaebf98: add             x3, x3, HEAP, lsl #32
    // 0xaebf9c: cmp             w3, NULL
    // 0xaebfa0: b.eq            #0xaec25c
    // 0xaebfa4: LoadField: r4 = r3->field_13
    //     0xaebfa4: ldur            w4, [x3, #0x13]
    // 0xaebfa8: DecompressPointer r4
    //     0xaebfa8: add             x4, x4, HEAP, lsl #32
    // 0xaebfac: cmp             w4, NULL
    // 0xaebfb0: b.ne            #0xaebfbc
    // 0xaebfb4: r3 = Null
    //     0xaebfb4: mov             x3, NULL
    // 0xaebfb8: b               #0xaebfc4
    // 0xaebfbc: LoadField: r3 = r4->field_7
    //     0xaebfbc: ldur            w3, [x4, #7]
    // 0xaebfc0: DecompressPointer r3
    //     0xaebfc0: add             x3, x3, HEAP, lsl #32
    // 0xaebfc4: cmp             w3, NULL
    // 0xaebfc8: b.ne            #0xaebfd4
    // 0xaebfcc: r4 = ""
    //     0xaebfcc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaebfd0: b               #0xaebfd8
    // 0xaebfd4: mov             x4, x3
    // 0xaebfd8: ldur            x3, [fp, #-0x20]
    // 0xaebfdc: stur            x4, [fp, #-0x28]
    // 0xaebfe0: LoadField: r5 = r1->field_f
    //     0xaebfe0: ldur            w5, [x1, #0xf]
    // 0xaebfe4: DecompressPointer r5
    //     0xaebfe4: add             x5, x5, HEAP, lsl #32
    // 0xaebfe8: cmp             w5, NULL
    // 0xaebfec: b.eq            #0xaec260
    // 0xaebff0: mov             x1, x5
    // 0xaebff4: r0 = of()
    //     0xaebff4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaebff8: LoadField: r1 = r0->field_87
    //     0xaebff8: ldur            w1, [x0, #0x87]
    // 0xaebffc: DecompressPointer r1
    //     0xaebffc: add             x1, x1, HEAP, lsl #32
    // 0xaec000: LoadField: r0 = r1->field_2b
    //     0xaec000: ldur            w0, [x1, #0x2b]
    // 0xaec004: DecompressPointer r0
    //     0xaec004: add             x0, x0, HEAP, lsl #32
    // 0xaec008: r16 = 16.000000
    //     0xaec008: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaec00c: ldr             x16, [x16, #0x188]
    // 0xaec010: r30 = Instance_Color
    //     0xaec010: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaec014: stp             lr, x16, [SP]
    // 0xaec018: mov             x1, x0
    // 0xaec01c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaec01c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaec020: ldr             x4, [x4, #0xaa0]
    // 0xaec024: r0 = copyWith()
    //     0xaec024: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaec028: stur            x0, [fp, #-8]
    // 0xaec02c: r0 = Text()
    //     0xaec02c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaec030: mov             x1, x0
    // 0xaec034: ldur            x0, [fp, #-0x28]
    // 0xaec038: stur            x1, [fp, #-0x38]
    // 0xaec03c: StoreField: r1->field_b = r0
    //     0xaec03c: stur            w0, [x1, #0xb]
    // 0xaec040: ldur            x0, [fp, #-8]
    // 0xaec044: StoreField: r1->field_13 = r0
    //     0xaec044: stur            w0, [x1, #0x13]
    // 0xaec048: r0 = Center()
    //     0xaec048: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaec04c: mov             x1, x0
    // 0xaec050: r0 = Instance_Alignment
    //     0xaec050: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xaec054: ldr             x0, [x0, #0xb10]
    // 0xaec058: stur            x1, [fp, #-8]
    // 0xaec05c: StoreField: r1->field_f = r0
    //     0xaec05c: stur            w0, [x1, #0xf]
    // 0xaec060: ldur            x0, [fp, #-0x38]
    // 0xaec064: StoreField: r1->field_b = r0
    //     0xaec064: stur            w0, [x1, #0xb]
    // 0xaec068: r0 = Container()
    //     0xaec068: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaec06c: stur            x0, [fp, #-0x28]
    // 0xaec070: r16 = 40.000000
    //     0xaec070: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xaec074: ldr             x16, [x16, #8]
    // 0xaec078: r30 = 110.000000
    //     0xaec078: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xaec07c: ldr             lr, [lr, #0x770]
    // 0xaec080: stp             lr, x16, [SP, #0x10]
    // 0xaec084: ldur            x16, [fp, #-0x30]
    // 0xaec088: ldur            lr, [fp, #-8]
    // 0xaec08c: stp             lr, x16, [SP]
    // 0xaec090: mov             x1, x0
    // 0xaec094: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xaec094: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xaec098: ldr             x4, [x4, #0x8c0]
    // 0xaec09c: r0 = Container()
    //     0xaec09c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaec0a0: r0 = InkWell()
    //     0xaec0a0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xaec0a4: mov             x3, x0
    // 0xaec0a8: ldur            x0, [fp, #-0x28]
    // 0xaec0ac: stur            x3, [fp, #-8]
    // 0xaec0b0: StoreField: r3->field_b = r0
    //     0xaec0b0: stur            w0, [x3, #0xb]
    // 0xaec0b4: ldur            x2, [fp, #-0x18]
    // 0xaec0b8: r1 = Function '<anonymous closure>':.
    //     0xaec0b8: add             x1, PP, #0x58, lsl #12  ; [pp+0x58238] AnonymousClosure: (0xaec268), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildHeader (0xaebbc0)
    //     0xaec0bc: ldr             x1, [x1, #0x238]
    // 0xaec0c0: r0 = AllocateClosure()
    //     0xaec0c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaec0c4: mov             x1, x0
    // 0xaec0c8: ldur            x0, [fp, #-8]
    // 0xaec0cc: StoreField: r0->field_f = r1
    //     0xaec0cc: stur            w1, [x0, #0xf]
    // 0xaec0d0: r1 = true
    //     0xaec0d0: add             x1, NULL, #0x20  ; true
    // 0xaec0d4: StoreField: r0->field_43 = r1
    //     0xaec0d4: stur            w1, [x0, #0x43]
    // 0xaec0d8: r2 = Instance_BoxShape
    //     0xaec0d8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaec0dc: ldr             x2, [x2, #0x80]
    // 0xaec0e0: StoreField: r0->field_47 = r2
    //     0xaec0e0: stur            w2, [x0, #0x47]
    // 0xaec0e4: StoreField: r0->field_6f = r1
    //     0xaec0e4: stur            w1, [x0, #0x6f]
    // 0xaec0e8: r2 = false
    //     0xaec0e8: add             x2, NULL, #0x30  ; false
    // 0xaec0ec: StoreField: r0->field_73 = r2
    //     0xaec0ec: stur            w2, [x0, #0x73]
    // 0xaec0f0: StoreField: r0->field_83 = r1
    //     0xaec0f0: stur            w1, [x0, #0x83]
    // 0xaec0f4: StoreField: r0->field_7b = r2
    //     0xaec0f4: stur            w2, [x0, #0x7b]
    // 0xaec0f8: ldur            x2, [fp, #-0x20]
    // 0xaec0fc: LoadField: r1 = r2->field_b
    //     0xaec0fc: ldur            w1, [x2, #0xb]
    // 0xaec100: LoadField: r3 = r2->field_f
    //     0xaec100: ldur            w3, [x2, #0xf]
    // 0xaec104: DecompressPointer r3
    //     0xaec104: add             x3, x3, HEAP, lsl #32
    // 0xaec108: LoadField: r4 = r3->field_b
    //     0xaec108: ldur            w4, [x3, #0xb]
    // 0xaec10c: r3 = LoadInt32Instr(r1)
    //     0xaec10c: sbfx            x3, x1, #1, #0x1f
    // 0xaec110: stur            x3, [fp, #-0x48]
    // 0xaec114: r1 = LoadInt32Instr(r4)
    //     0xaec114: sbfx            x1, x4, #1, #0x1f
    // 0xaec118: cmp             x3, x1
    // 0xaec11c: b.ne            #0xaec128
    // 0xaec120: mov             x1, x2
    // 0xaec124: r0 = _growToNextCapacity()
    //     0xaec124: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaec128: ldur            x2, [fp, #-0x20]
    // 0xaec12c: ldur            x3, [fp, #-0x48]
    // 0xaec130: add             x4, x3, #1
    // 0xaec134: lsl             x0, x4, #1
    // 0xaec138: StoreField: r2->field_b = r0
    //     0xaec138: stur            w0, [x2, #0xb]
    // 0xaec13c: LoadField: r5 = r2->field_f
    //     0xaec13c: ldur            w5, [x2, #0xf]
    // 0xaec140: DecompressPointer r5
    //     0xaec140: add             x5, x5, HEAP, lsl #32
    // 0xaec144: mov             x1, x5
    // 0xaec148: ldur            x0, [fp, #-8]
    // 0xaec14c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaec14c: add             x25, x1, x3, lsl #2
    //     0xaec150: add             x25, x25, #0xf
    //     0xaec154: str             w0, [x25]
    //     0xaec158: tbz             w0, #0, #0xaec174
    //     0xaec15c: ldurb           w16, [x1, #-1]
    //     0xaec160: ldurb           w17, [x0, #-1]
    //     0xaec164: and             x16, x17, x16, lsr #2
    //     0xaec168: tst             x16, HEAP, lsr #32
    //     0xaec16c: b.eq            #0xaec174
    //     0xaec170: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaec174: mov             x3, x4
    // 0xaec178: mov             x0, x5
    // 0xaec17c: b               #0xaec188
    // 0xaec180: mov             x2, x0
    // 0xaec184: mov             x0, x4
    // 0xaec188: stur            x3, [fp, #-0x48]
    // 0xaec18c: LoadField: r1 = r0->field_b
    //     0xaec18c: ldur            w1, [x0, #0xb]
    // 0xaec190: r0 = LoadInt32Instr(r1)
    //     0xaec190: sbfx            x0, x1, #1, #0x1f
    // 0xaec194: cmp             x3, x0
    // 0xaec198: b.ne            #0xaec1a4
    // 0xaec19c: mov             x1, x2
    // 0xaec1a0: r0 = _growToNextCapacity()
    //     0xaec1a0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaec1a4: ldur            x2, [fp, #-0x20]
    // 0xaec1a8: ldur            x4, [fp, #-0x10]
    // 0xaec1ac: ldur            x3, [fp, #-0x48]
    // 0xaec1b0: add             x0, x3, #1
    // 0xaec1b4: lsl             x1, x0, #1
    // 0xaec1b8: StoreField: r2->field_b = r1
    //     0xaec1b8: stur            w1, [x2, #0xb]
    // 0xaec1bc: mov             x1, x3
    // 0xaec1c0: cmp             x1, x0
    // 0xaec1c4: b.hs            #0xaec264
    // 0xaec1c8: LoadField: r0 = r2->field_f
    //     0xaec1c8: ldur            w0, [x2, #0xf]
    // 0xaec1cc: DecompressPointer r0
    //     0xaec1cc: add             x0, x0, HEAP, lsl #32
    // 0xaec1d0: add             x1, x0, x3, lsl #2
    // 0xaec1d4: r16 = Instance_SizedBox
    //     0xaec1d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xaec1d8: ldr             x16, [x16, #0x8f0]
    // 0xaec1dc: StoreField: r1->field_f = r16
    //     0xaec1dc: stur            w16, [x1, #0xf]
    // 0xaec1e0: r0 = Column()
    //     0xaec1e0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaec1e4: r1 = Instance_Axis
    //     0xaec1e4: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaec1e8: StoreField: r0->field_f = r1
    //     0xaec1e8: stur            w1, [x0, #0xf]
    // 0xaec1ec: r1 = Instance_MainAxisAlignment
    //     0xaec1ec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaec1f0: ldr             x1, [x1, #0xa08]
    // 0xaec1f4: StoreField: r0->field_13 = r1
    //     0xaec1f4: stur            w1, [x0, #0x13]
    // 0xaec1f8: r1 = Instance_MainAxisSize
    //     0xaec1f8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaec1fc: ldr             x1, [x1, #0xa10]
    // 0xaec200: ArrayStore: r0[0] = r1  ; List_4
    //     0xaec200: stur            w1, [x0, #0x17]
    // 0xaec204: ldur            x1, [fp, #-0x10]
    // 0xaec208: StoreField: r0->field_1b = r1
    //     0xaec208: stur            w1, [x0, #0x1b]
    // 0xaec20c: r1 = Instance_VerticalDirection
    //     0xaec20c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaec210: ldr             x1, [x1, #0xa20]
    // 0xaec214: StoreField: r0->field_23 = r1
    //     0xaec214: stur            w1, [x0, #0x23]
    // 0xaec218: r1 = Instance_Clip
    //     0xaec218: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaec21c: ldr             x1, [x1, #0x38]
    // 0xaec220: StoreField: r0->field_2b = r1
    //     0xaec220: stur            w1, [x0, #0x2b]
    // 0xaec224: StoreField: r0->field_2f = rZR
    //     0xaec224: stur            xzr, [x0, #0x2f]
    // 0xaec228: ldur            x1, [fp, #-0x20]
    // 0xaec22c: StoreField: r0->field_b = r1
    //     0xaec22c: stur            w1, [x0, #0xb]
    // 0xaec230: LeaveFrame
    //     0xaec230: mov             SP, fp
    //     0xaec234: ldp             fp, lr, [SP], #0x10
    // 0xaec238: ret
    //     0xaec238: ret             
    // 0xaec23c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec23c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec240: b               #0xaebbdc
    // 0xaec244: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaec244: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaec248: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaec248: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaec24c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaec24c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaec250: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaec250: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaec254: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaec254: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaec258: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaec258: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaec25c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaec25c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaec260: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaec260: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaec264: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaec264: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaec268, size: 0x48
    // 0xaec268: EnterFrame
    //     0xaec268: stp             fp, lr, [SP, #-0x10]!
    //     0xaec26c: mov             fp, SP
    // 0xaec270: ldr             x0, [fp, #0x10]
    // 0xaec274: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaec274: ldur            w1, [x0, #0x17]
    // 0xaec278: DecompressPointer r1
    //     0xaec278: add             x1, x1, HEAP, lsl #32
    // 0xaec27c: CheckStackOverflow
    //     0xaec27c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec280: cmp             SP, x16
    //     0xaec284: b.ls            #0xaec2a8
    // 0xaec288: LoadField: r0 = r1->field_f
    //     0xaec288: ldur            w0, [x1, #0xf]
    // 0xaec28c: DecompressPointer r0
    //     0xaec28c: add             x0, x0, HEAP, lsl #32
    // 0xaec290: mov             x1, x0
    // 0xaec294: r0 = _onViewAllTap()
    //     0xaec294: bl              #0xaec2b0  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_onViewAllTap
    // 0xaec298: r0 = Null
    //     0xaec298: mov             x0, NULL
    // 0xaec29c: LeaveFrame
    //     0xaec29c: mov             SP, fp
    //     0xaec2a0: ldp             fp, lr, [SP], #0x10
    // 0xaec2a4: ret
    //     0xaec2a4: ret             
    // 0xaec2a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec2a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec2ac: b               #0xaec288
  }
  _ _onViewAllTap(/* No info */) {
    // ** addr: 0xaec2b0, size: 0xe4
    // 0xaec2b0: EnterFrame
    //     0xaec2b0: stp             fp, lr, [SP, #-0x10]!
    //     0xaec2b4: mov             fp, SP
    // 0xaec2b8: AllocStack(0x30)
    //     0xaec2b8: sub             SP, SP, #0x30
    // 0xaec2bc: CheckStackOverflow
    //     0xaec2bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec2c0: cmp             SP, x16
    //     0xaec2c4: b.ls            #0xaec388
    // 0xaec2c8: LoadField: r0 = r1->field_b
    //     0xaec2c8: ldur            w0, [x1, #0xb]
    // 0xaec2cc: DecompressPointer r0
    //     0xaec2cc: add             x0, x0, HEAP, lsl #32
    // 0xaec2d0: cmp             w0, NULL
    // 0xaec2d4: b.eq            #0xaec390
    // 0xaec2d8: LoadField: r1 = r0->field_27
    //     0xaec2d8: ldur            w1, [x0, #0x27]
    // 0xaec2dc: DecompressPointer r1
    //     0xaec2dc: add             x1, x1, HEAP, lsl #32
    // 0xaec2e0: cmp             w1, NULL
    // 0xaec2e4: b.ne            #0xaec2ec
    // 0xaec2e8: r1 = ""
    //     0xaec2e8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaec2ec: LoadField: r2 = r0->field_23
    //     0xaec2ec: ldur            w2, [x0, #0x23]
    // 0xaec2f0: DecompressPointer r2
    //     0xaec2f0: add             x2, x2, HEAP, lsl #32
    // 0xaec2f4: cmp             w2, NULL
    // 0xaec2f8: b.ne            #0xaec300
    // 0xaec2fc: r2 = ""
    //     0xaec2fc: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaec300: LoadField: r3 = r0->field_2f
    //     0xaec300: ldur            w3, [x0, #0x2f]
    // 0xaec304: DecompressPointer r3
    //     0xaec304: add             x3, x3, HEAP, lsl #32
    // 0xaec308: cmp             w3, NULL
    // 0xaec30c: b.ne            #0xaec314
    // 0xaec310: r3 = ""
    //     0xaec310: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaec314: LoadField: r4 = r0->field_2b
    //     0xaec314: ldur            w4, [x0, #0x2b]
    // 0xaec318: DecompressPointer r4
    //     0xaec318: add             x4, x4, HEAP, lsl #32
    // 0xaec31c: cmp             w4, NULL
    // 0xaec320: b.ne            #0xaec328
    // 0xaec324: r4 = ""
    //     0xaec324: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaec328: LoadField: r5 = r0->field_13
    //     0xaec328: ldur            w5, [x0, #0x13]
    // 0xaec32c: DecompressPointer r5
    //     0xaec32c: add             x5, x5, HEAP, lsl #32
    // 0xaec330: cmp             w5, NULL
    // 0xaec334: b.ne            #0xaec340
    // 0xaec338: r5 = Null
    //     0xaec338: mov             x5, NULL
    // 0xaec33c: b               #0xaec34c
    // 0xaec340: LoadField: r6 = r5->field_b
    //     0xaec340: ldur            w6, [x5, #0xb]
    // 0xaec344: DecompressPointer r6
    //     0xaec344: add             x6, x6, HEAP, lsl #32
    // 0xaec348: mov             x5, x6
    // 0xaec34c: LoadField: r6 = r0->field_3f
    //     0xaec34c: ldur            w6, [x0, #0x3f]
    // 0xaec350: DecompressPointer r6
    //     0xaec350: add             x6, x6, HEAP, lsl #32
    // 0xaec354: stp             x1, x6, [SP, #0x20]
    // 0xaec358: stp             x3, x2, [SP, #0x10]
    // 0xaec35c: stp             x5, x4, [SP]
    // 0xaec360: r4 = 0
    //     0xaec360: movz            x4, #0
    // 0xaec364: ldr             x0, [SP, #0x28]
    // 0xaec368: r16 = UnlinkedCall_0x613b5c
    //     0xaec368: add             x16, PP, #0x58, lsl #12  ; [pp+0x58240] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaec36c: add             x16, x16, #0x240
    // 0xaec370: ldp             x5, lr, [x16]
    // 0xaec374: blr             lr
    // 0xaec378: r0 = Null
    //     0xaec378: mov             x0, NULL
    // 0xaec37c: LeaveFrame
    //     0xaec37c: mov             SP, fp
    //     0xaec380: ldp             fp, lr, [SP], #0x10
    // 0xaec384: ret
    //     0xaec384: ret             
    // 0xaec388: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec388: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec38c: b               #0xaec2c8
    // 0xaec390: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaec390: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaec394, size: 0x2fc
    // 0xaec394: EnterFrame
    //     0xaec394: stp             fp, lr, [SP, #-0x10]!
    //     0xaec398: mov             fp, SP
    // 0xaec39c: AllocStack(0x50)
    //     0xaec39c: sub             SP, SP, #0x50
    // 0xaec3a0: SetupParameters()
    //     0xaec3a0: ldr             x0, [fp, #0x20]
    //     0xaec3a4: ldur            w1, [x0, #0x17]
    //     0xaec3a8: add             x1, x1, HEAP, lsl #32
    //     0xaec3ac: stur            x1, [fp, #-8]
    // 0xaec3b0: CheckStackOverflow
    //     0xaec3b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec3b4: cmp             SP, x16
    //     0xaec3b8: b.ls            #0xaec66c
    // 0xaec3bc: r1 = 3
    //     0xaec3bc: movz            x1, #0x3
    // 0xaec3c0: r0 = AllocateContext()
    //     0xaec3c0: bl              #0x16f6108  ; AllocateContextStub
    // 0xaec3c4: mov             x5, x0
    // 0xaec3c8: ldur            x4, [fp, #-8]
    // 0xaec3cc: stur            x5, [fp, #-0x18]
    // 0xaec3d0: StoreField: r5->field_b = r4
    //     0xaec3d0: stur            w4, [x5, #0xb]
    // 0xaec3d4: ldr             x0, [fp, #0x18]
    // 0xaec3d8: StoreField: r5->field_f = r0
    //     0xaec3d8: stur            w0, [x5, #0xf]
    // 0xaec3dc: ldr             x0, [fp, #0x10]
    // 0xaec3e0: r1 = LoadInt32Instr(r0)
    //     0xaec3e0: sbfx            x1, x0, #1, #0x1f
    //     0xaec3e4: tbz             w0, #0, #0xaec3ec
    //     0xaec3e8: ldur            x1, [x0, #7]
    // 0xaec3ec: lsl             x6, x1, #1
    // 0xaec3f0: stur            x6, [fp, #-0x10]
    // 0xaec3f4: r0 = BoxInt64Instr(r6)
    //     0xaec3f4: sbfiz           x0, x6, #1, #0x1f
    //     0xaec3f8: cmp             x6, x0, asr #1
    //     0xaec3fc: b.eq            #0xaec408
    //     0xaec400: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaec404: stur            x6, [x0, #7]
    // 0xaec408: StoreField: r5->field_13 = r0
    //     0xaec408: stur            w0, [x5, #0x13]
    // 0xaec40c: add             x2, x6, #2
    // 0xaec410: LoadField: r0 = r4->field_f
    //     0xaec410: ldur            w0, [x4, #0xf]
    // 0xaec414: DecompressPointer r0
    //     0xaec414: add             x0, x0, HEAP, lsl #32
    // 0xaec418: LoadField: r1 = r0->field_b
    //     0xaec418: ldur            w1, [x0, #0xb]
    // 0xaec41c: DecompressPointer r1
    //     0xaec41c: add             x1, x1, HEAP, lsl #32
    // 0xaec420: cmp             w1, NULL
    // 0xaec424: b.eq            #0xaec674
    // 0xaec428: LoadField: r0 = r1->field_b
    //     0xaec428: ldur            w0, [x1, #0xb]
    // 0xaec42c: DecompressPointer r0
    //     0xaec42c: add             x0, x0, HEAP, lsl #32
    // 0xaec430: cmp             w0, NULL
    // 0xaec434: b.eq            #0xaec678
    // 0xaec438: LoadField: r3 = r0->field_b
    //     0xaec438: ldur            w3, [x0, #0xb]
    // 0xaec43c: r0 = BoxInt64Instr(r2)
    //     0xaec43c: sbfiz           x0, x2, #1, #0x1f
    //     0xaec440: cmp             x2, x0, asr #1
    //     0xaec444: b.eq            #0xaec450
    //     0xaec448: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaec44c: stur            x2, [x0, #7]
    // 0xaec450: mov             x1, x0
    // 0xaec454: r2 = 0
    //     0xaec454: movz            x2, #0
    // 0xaec458: r0 = clamp()
    //     0xaec458: bl              #0x6b3958  ; [dart:core] _IntegerImplementation::clamp
    // 0xaec45c: mov             x1, x0
    // 0xaec460: ldur            x0, [fp, #-8]
    // 0xaec464: LoadField: r2 = r0->field_f
    //     0xaec464: ldur            w2, [x0, #0xf]
    // 0xaec468: DecompressPointer r2
    //     0xaec468: add             x2, x2, HEAP, lsl #32
    // 0xaec46c: LoadField: r3 = r2->field_b
    //     0xaec46c: ldur            w3, [x2, #0xb]
    // 0xaec470: DecompressPointer r3
    //     0xaec470: add             x3, x3, HEAP, lsl #32
    // 0xaec474: cmp             w3, NULL
    // 0xaec478: b.eq            #0xaec67c
    // 0xaec47c: LoadField: r2 = r3->field_b
    //     0xaec47c: ldur            w2, [x3, #0xb]
    // 0xaec480: DecompressPointer r2
    //     0xaec480: add             x2, x2, HEAP, lsl #32
    // 0xaec484: cmp             w2, NULL
    // 0xaec488: b.eq            #0xaec680
    // 0xaec48c: str             x1, [SP]
    // 0xaec490: mov             x1, x2
    // 0xaec494: ldur            x2, [fp, #-0x10]
    // 0xaec498: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xaec498: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xaec49c: r0 = sublist()
    //     0xaec49c: bl              #0x71da80  ; [dart:core] _GrowableList::sublist
    // 0xaec4a0: mov             x1, x0
    // 0xaec4a4: ldur            x2, [fp, #-0x18]
    // 0xaec4a8: stur            x1, [fp, #-0x20]
    // 0xaec4ac: ArrayStore: r2[0] = r0  ; List_4
    //     0xaec4ac: stur            w0, [x2, #0x17]
    //     0xaec4b0: ldurb           w16, [x2, #-1]
    //     0xaec4b4: ldurb           w17, [x0, #-1]
    //     0xaec4b8: and             x16, x17, x16, lsr #2
    //     0xaec4bc: tst             x16, HEAP, lsr #32
    //     0xaec4c0: b.eq            #0xaec4c8
    //     0xaec4c4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xaec4c8: r3 = 0
    //     0xaec4c8: movz            x3, #0
    // 0xaec4cc: ldur            x0, [fp, #-8]
    // 0xaec4d0: stur            x3, [fp, #-0x10]
    // 0xaec4d4: CheckStackOverflow
    //     0xaec4d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec4d8: cmp             SP, x16
    //     0xaec4dc: b.ls            #0xaec684
    // 0xaec4e0: LoadField: r4 = r1->field_b
    //     0xaec4e0: ldur            w4, [x1, #0xb]
    // 0xaec4e4: r5 = LoadInt32Instr(r4)
    //     0xaec4e4: sbfx            x5, x4, #1, #0x1f
    // 0xaec4e8: cmp             x3, x5
    // 0xaec4ec: b.ge            #0xaec59c
    // 0xaec4f0: LoadField: r4 = r1->field_f
    //     0xaec4f0: ldur            w4, [x1, #0xf]
    // 0xaec4f4: DecompressPointer r4
    //     0xaec4f4: add             x4, x4, HEAP, lsl #32
    // 0xaec4f8: ArrayLoad: r5 = r4[r3]  ; Unknown_4
    //     0xaec4f8: add             x16, x4, x3, lsl #2
    //     0xaec4fc: ldur            w5, [x16, #0xf]
    // 0xaec500: DecompressPointer r5
    //     0xaec500: add             x5, x5, HEAP, lsl #32
    // 0xaec504: LoadField: r4 = r0->field_f
    //     0xaec504: ldur            w4, [x0, #0xf]
    // 0xaec508: DecompressPointer r4
    //     0xaec508: add             x4, x4, HEAP, lsl #32
    // 0xaec50c: LoadField: r6 = r4->field_b
    //     0xaec50c: ldur            w6, [x4, #0xb]
    // 0xaec510: DecompressPointer r6
    //     0xaec510: add             x6, x6, HEAP, lsl #32
    // 0xaec514: cmp             w6, NULL
    // 0xaec518: b.eq            #0xaec68c
    // 0xaec51c: LoadField: r4 = r5->field_53
    //     0xaec51c: ldur            w4, [x5, #0x53]
    // 0xaec520: DecompressPointer r4
    //     0xaec520: add             x4, x4, HEAP, lsl #32
    // 0xaec524: LoadField: r7 = r5->field_3b
    //     0xaec524: ldur            w7, [x5, #0x3b]
    // 0xaec528: DecompressPointer r7
    //     0xaec528: add             x7, x7, HEAP, lsl #32
    // 0xaec52c: cmp             w7, NULL
    // 0xaec530: b.ne            #0xaec53c
    // 0xaec534: r7 = Null
    //     0xaec534: mov             x7, NULL
    // 0xaec538: b               #0xaec548
    // 0xaec53c: LoadField: r8 = r7->field_b
    //     0xaec53c: ldur            w8, [x7, #0xb]
    // 0xaec540: DecompressPointer r8
    //     0xaec540: add             x8, x8, HEAP, lsl #32
    // 0xaec544: mov             x7, x8
    // 0xaec548: LoadField: r8 = r5->field_2b
    //     0xaec548: ldur            w8, [x5, #0x2b]
    // 0xaec54c: DecompressPointer r8
    //     0xaec54c: add             x8, x8, HEAP, lsl #32
    // 0xaec550: LoadField: r9 = r5->field_57
    //     0xaec550: ldur            w9, [x5, #0x57]
    // 0xaec554: DecompressPointer r9
    //     0xaec554: add             x9, x9, HEAP, lsl #32
    // 0xaec558: LoadField: r10 = r5->field_7b
    //     0xaec558: ldur            w10, [x5, #0x7b]
    // 0xaec55c: DecompressPointer r10
    //     0xaec55c: add             x10, x10, HEAP, lsl #32
    // 0xaec560: LoadField: r5 = r6->field_3b
    //     0xaec560: ldur            w5, [x6, #0x3b]
    // 0xaec564: DecompressPointer r5
    //     0xaec564: add             x5, x5, HEAP, lsl #32
    // 0xaec568: stp             x4, x5, [SP, #0x20]
    // 0xaec56c: stp             x8, x7, [SP, #0x10]
    // 0xaec570: stp             x10, x9, [SP]
    // 0xaec574: r4 = 0
    //     0xaec574: movz            x4, #0
    // 0xaec578: ldr             x0, [SP, #0x28]
    // 0xaec57c: r5 = UnlinkedCall_0x613b5c
    //     0xaec57c: add             x16, PP, #0x58, lsl #12  ; [pp+0x58190] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaec580: ldp             x5, lr, [x16, #0x190]
    // 0xaec584: blr             lr
    // 0xaec588: ldur            x0, [fp, #-0x10]
    // 0xaec58c: add             x3, x0, #1
    // 0xaec590: ldur            x2, [fp, #-0x18]
    // 0xaec594: ldur            x1, [fp, #-0x20]
    // 0xaec598: b               #0xaec4cc
    // 0xaec59c: ldur            x1, [fp, #-0x20]
    // 0xaec5a0: r0 = asMap()
    //     0xaec5a0: bl              #0x7143ec  ; [dart:collection] ListBase::asMap
    // 0xaec5a4: mov             x1, x0
    // 0xaec5a8: r0 = entries()
    //     0xaec5a8: bl              #0x1641968  ; [dart:collection] MapBase::entries
    // 0xaec5ac: ldur            x2, [fp, #-0x18]
    // 0xaec5b0: r1 = Function '<anonymous closure>':.
    //     0xaec5b0: add             x1, PP, #0x58, lsl #12  ; [pp+0x581a0] AnonymousClosure: (0xaec690), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::build (0xaeae6c)
    //     0xaec5b4: ldr             x1, [x1, #0x1a0]
    // 0xaec5b8: stur            x0, [fp, #-8]
    // 0xaec5bc: r0 = AllocateClosure()
    //     0xaec5bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaec5c0: r16 = <SizedBox>
    //     0xaec5c0: add             x16, PP, #0x53, lsl #12  ; [pp+0x536b0] TypeArguments: <SizedBox>
    //     0xaec5c4: ldr             x16, [x16, #0x6b0]
    // 0xaec5c8: ldur            lr, [fp, #-8]
    // 0xaec5cc: stp             lr, x16, [SP, #8]
    // 0xaec5d0: str             x0, [SP]
    // 0xaec5d4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaec5d4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaec5d8: r0 = map()
    //     0xaec5d8: bl              #0x78898c  ; [dart:_internal] ListIterable::map
    // 0xaec5dc: mov             x1, x0
    // 0xaec5e0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaec5e0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaec5e4: r0 = toList()
    //     0xaec5e4: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xaec5e8: stur            x0, [fp, #-8]
    // 0xaec5ec: r0 = Row()
    //     0xaec5ec: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaec5f0: mov             x1, x0
    // 0xaec5f4: r0 = Instance_Axis
    //     0xaec5f4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaec5f8: stur            x1, [fp, #-0x18]
    // 0xaec5fc: StoreField: r1->field_f = r0
    //     0xaec5fc: stur            w0, [x1, #0xf]
    // 0xaec600: r0 = Instance_MainAxisAlignment
    //     0xaec600: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaec604: ldr             x0, [x0, #0xa08]
    // 0xaec608: StoreField: r1->field_13 = r0
    //     0xaec608: stur            w0, [x1, #0x13]
    // 0xaec60c: r0 = Instance_MainAxisSize
    //     0xaec60c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaec610: ldr             x0, [x0, #0xa10]
    // 0xaec614: ArrayStore: r1[0] = r0  ; List_4
    //     0xaec614: stur            w0, [x1, #0x17]
    // 0xaec618: r0 = Instance_CrossAxisAlignment
    //     0xaec618: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaec61c: ldr             x0, [x0, #0xa18]
    // 0xaec620: StoreField: r1->field_1b = r0
    //     0xaec620: stur            w0, [x1, #0x1b]
    // 0xaec624: r0 = Instance_VerticalDirection
    //     0xaec624: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaec628: ldr             x0, [x0, #0xa20]
    // 0xaec62c: StoreField: r1->field_23 = r0
    //     0xaec62c: stur            w0, [x1, #0x23]
    // 0xaec630: r0 = Instance_Clip
    //     0xaec630: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaec634: ldr             x0, [x0, #0x38]
    // 0xaec638: StoreField: r1->field_2b = r0
    //     0xaec638: stur            w0, [x1, #0x2b]
    // 0xaec63c: StoreField: r1->field_2f = rZR
    //     0xaec63c: stur            xzr, [x1, #0x2f]
    // 0xaec640: ldur            x0, [fp, #-8]
    // 0xaec644: StoreField: r1->field_b = r0
    //     0xaec644: stur            w0, [x1, #0xb]
    // 0xaec648: r0 = Padding()
    //     0xaec648: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaec64c: r1 = Instance_EdgeInsets
    //     0xaec64c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52018] Obj!EdgeInsets@d586d1
    //     0xaec650: ldr             x1, [x1, #0x18]
    // 0xaec654: StoreField: r0->field_f = r1
    //     0xaec654: stur            w1, [x0, #0xf]
    // 0xaec658: ldur            x1, [fp, #-0x18]
    // 0xaec65c: StoreField: r0->field_b = r1
    //     0xaec65c: stur            w1, [x0, #0xb]
    // 0xaec660: LeaveFrame
    //     0xaec660: mov             SP, fp
    //     0xaec664: ldp             fp, lr, [SP], #0x10
    // 0xaec668: ret
    //     0xaec668: ret             
    // 0xaec66c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec66c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec670: b               #0xaec3bc
    // 0xaec674: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaec674: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaec678: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaec678: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaec67c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaec67c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaec680: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaec680: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaec684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec684: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec688: b               #0xaec4e0
    // 0xaec68c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaec68c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] SizedBox <anonymous closure>(dynamic, MapEntry<int, Entity>) {
    // ** addr: 0xaec690, size: 0x330
    // 0xaec690: EnterFrame
    //     0xaec690: stp             fp, lr, [SP, #-0x10]!
    //     0xaec694: mov             fp, SP
    // 0xaec698: AllocStack(0x40)
    //     0xaec698: sub             SP, SP, #0x40
    // 0xaec69c: SetupParameters()
    //     0xaec69c: ldr             x0, [fp, #0x18]
    //     0xaec6a0: ldur            w2, [x0, #0x17]
    //     0xaec6a4: add             x2, x2, HEAP, lsl #32
    //     0xaec6a8: stur            x2, [fp, #-0x18]
    // 0xaec6ac: CheckStackOverflow
    //     0xaec6ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec6b0: cmp             SP, x16
    //     0xaec6b4: b.ls            #0xaec998
    // 0xaec6b8: ldr             x0, [fp, #0x10]
    // 0xaec6bc: LoadField: r3 = r0->field_b
    //     0xaec6bc: ldur            w3, [x0, #0xb]
    // 0xaec6c0: DecompressPointer r3
    //     0xaec6c0: add             x3, x3, HEAP, lsl #32
    // 0xaec6c4: stur            x3, [fp, #-0x10]
    // 0xaec6c8: LoadField: r4 = r0->field_f
    //     0xaec6c8: ldur            w4, [x0, #0xf]
    // 0xaec6cc: DecompressPointer r4
    //     0xaec6cc: add             x4, x4, HEAP, lsl #32
    // 0xaec6d0: stur            x4, [fp, #-8]
    // 0xaec6d4: LoadField: r1 = r2->field_f
    //     0xaec6d4: ldur            w1, [x2, #0xf]
    // 0xaec6d8: DecompressPointer r1
    //     0xaec6d8: add             x1, x1, HEAP, lsl #32
    // 0xaec6dc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaec6dc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaec6e0: r0 = _of()
    //     0xaec6e0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xaec6e4: LoadField: r1 = r0->field_7
    //     0xaec6e4: ldur            w1, [x0, #7]
    // 0xaec6e8: DecompressPointer r1
    //     0xaec6e8: add             x1, x1, HEAP, lsl #32
    // 0xaec6ec: LoadField: d0 = r1->field_7
    //     0xaec6ec: ldur            d0, [x1, #7]
    // 0xaec6f0: d1 = 0.450000
    //     0xaec6f0: add             x17, PP, #0x52, lsl #12  ; [pp+0x52d08] IMM: double(0.45) from 0x3fdccccccccccccd
    //     0xaec6f4: ldr             d1, [x17, #0xd08]
    // 0xaec6f8: fmul            d2, d0, d1
    // 0xaec6fc: ldur            x0, [fp, #-0x10]
    // 0xaec700: stur            d2, [fp, #-0x40]
    // 0xaec704: cbnz            w0, #0xaec710
    // 0xaec708: d0 = 0.000000
    //     0xaec708: eor             v0.16b, v0.16b, v0.16b
    // 0xaec70c: b               #0xaec714
    // 0xaec710: d0 = 2.000000
    //     0xaec710: fmov            d0, #2.00000000
    // 0xaec714: ldur            x1, [fp, #-0x18]
    // 0xaec718: stur            d0, [fp, #-0x38]
    // 0xaec71c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xaec71c: ldur            w2, [x1, #0x17]
    // 0xaec720: DecompressPointer r2
    //     0xaec720: add             x2, x2, HEAP, lsl #32
    // 0xaec724: LoadField: r3 = r2->field_b
    //     0xaec724: ldur            w3, [x2, #0xb]
    // 0xaec728: r2 = LoadInt32Instr(r3)
    //     0xaec728: sbfx            x2, x3, #1, #0x1f
    // 0xaec72c: sub             x3, x2, #1
    // 0xaec730: lsl             x2, x3, #1
    // 0xaec734: cmp             w0, w2
    // 0xaec738: b.ne            #0xaec744
    // 0xaec73c: d1 = 0.000000
    //     0xaec73c: eor             v1.16b, v1.16b, v1.16b
    // 0xaec740: b               #0xaec748
    // 0xaec744: d1 = 2.000000
    //     0xaec744: fmov            d1, #2.00000000
    // 0xaec748: ldur            x2, [fp, #-8]
    // 0xaec74c: stur            d1, [fp, #-0x30]
    // 0xaec750: r0 = EdgeInsets()
    //     0xaec750: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xaec754: ldur            d0, [fp, #-0x38]
    // 0xaec758: stur            x0, [fp, #-0x20]
    // 0xaec75c: StoreField: r0->field_7 = d0
    //     0xaec75c: stur            d0, [x0, #7]
    // 0xaec760: StoreField: r0->field_f = rZR
    //     0xaec760: stur            xzr, [x0, #0xf]
    // 0xaec764: ldur            d0, [fp, #-0x30]
    // 0xaec768: ArrayStore: r0[0] = d0  ; List_8
    //     0xaec768: stur            d0, [x0, #0x17]
    // 0xaec76c: StoreField: r0->field_1f = rZR
    //     0xaec76c: stur            xzr, [x0, #0x1f]
    // 0xaec770: ldur            x1, [fp, #-0x18]
    // 0xaec774: LoadField: r2 = r1->field_b
    //     0xaec774: ldur            w2, [x1, #0xb]
    // 0xaec778: DecompressPointer r2
    //     0xaec778: add             x2, x2, HEAP, lsl #32
    // 0xaec77c: LoadField: r3 = r2->field_f
    //     0xaec77c: ldur            w3, [x2, #0xf]
    // 0xaec780: DecompressPointer r3
    //     0xaec780: add             x3, x3, HEAP, lsl #32
    // 0xaec784: LoadField: r2 = r1->field_13
    //     0xaec784: ldur            w2, [x1, #0x13]
    // 0xaec788: DecompressPointer r2
    //     0xaec788: add             x2, x2, HEAP, lsl #32
    // 0xaec78c: ldur            x1, [fp, #-0x10]
    // 0xaec790: cmp             w1, NULL
    // 0xaec794: b.eq            #0xaec9a0
    // 0xaec798: r4 = LoadInt32Instr(r2)
    //     0xaec798: sbfx            x4, x2, #1, #0x1f
    //     0xaec79c: tbz             w2, #0, #0xaec7a4
    //     0xaec7a0: ldur            x4, [x2, #7]
    // 0xaec7a4: r2 = LoadInt32Instr(r1)
    //     0xaec7a4: sbfx            x2, x1, #1, #0x1f
    //     0xaec7a8: tbz             w1, #0, #0xaec7b0
    //     0xaec7ac: ldur            x2, [x1, #7]
    // 0xaec7b0: add             x1, x4, x2
    // 0xaec7b4: mov             x16, x1
    // 0xaec7b8: mov             x1, x3
    // 0xaec7bc: mov             x3, x16
    // 0xaec7c0: ldur            x2, [fp, #-8]
    // 0xaec7c4: r0 = cosmeticThemeSlider()
    //     0xaec7c4: bl              #0xaec9c0  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::cosmeticThemeSlider
    // 0xaec7c8: r1 = Null
    //     0xaec7c8: mov             x1, NULL
    // 0xaec7cc: r2 = 2
    //     0xaec7cc: movz            x2, #0x2
    // 0xaec7d0: stur            x0, [fp, #-0x10]
    // 0xaec7d4: r0 = AllocateArray()
    //     0xaec7d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaec7d8: mov             x2, x0
    // 0xaec7dc: ldur            x0, [fp, #-0x10]
    // 0xaec7e0: stur            x2, [fp, #-0x18]
    // 0xaec7e4: StoreField: r2->field_f = r0
    //     0xaec7e4: stur            w0, [x2, #0xf]
    // 0xaec7e8: r1 = <Widget>
    //     0xaec7e8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaec7ec: r0 = AllocateGrowableArray()
    //     0xaec7ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaec7f0: mov             x1, x0
    // 0xaec7f4: ldur            x0, [fp, #-0x18]
    // 0xaec7f8: stur            x1, [fp, #-0x10]
    // 0xaec7fc: StoreField: r1->field_f = r0
    //     0xaec7fc: stur            w0, [x1, #0xf]
    // 0xaec800: r0 = 2
    //     0xaec800: movz            x0, #0x2
    // 0xaec804: StoreField: r1->field_b = r0
    //     0xaec804: stur            w0, [x1, #0xb]
    // 0xaec808: ldur            x0, [fp, #-8]
    // 0xaec80c: cmp             w0, NULL
    // 0xaec810: b.eq            #0xaec9a4
    // 0xaec814: LoadField: r2 = r0->field_bb
    //     0xaec814: ldur            w2, [x0, #0xbb]
    // 0xaec818: DecompressPointer r2
    //     0xaec818: add             x2, x2, HEAP, lsl #32
    // 0xaec81c: cmp             w2, NULL
    // 0xaec820: b.ne            #0xaec82c
    // 0xaec824: mov             x2, x1
    // 0xaec828: b               #0xaec8ec
    // 0xaec82c: tbnz            w2, #4, #0xaec8e8
    // 0xaec830: r0 = SvgPicture()
    //     0xaec830: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xaec834: mov             x1, x0
    // 0xaec838: r2 = "assets/images/free-gift-icon.svg"
    //     0xaec838: add             x2, PP, #0x52, lsl #12  ; [pp+0x52d40] "assets/images/free-gift-icon.svg"
    //     0xaec83c: ldr             x2, [x2, #0xd40]
    // 0xaec840: stur            x0, [fp, #-8]
    // 0xaec844: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaec844: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaec848: r0 = SvgPicture.asset()
    //     0xaec848: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xaec84c: r0 = Padding()
    //     0xaec84c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaec850: mov             x2, x0
    // 0xaec854: r0 = Instance_EdgeInsets
    //     0xaec854: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d48] Obj!EdgeInsets@d584c1
    //     0xaec858: ldr             x0, [x0, #0xd48]
    // 0xaec85c: stur            x2, [fp, #-0x18]
    // 0xaec860: StoreField: r2->field_f = r0
    //     0xaec860: stur            w0, [x2, #0xf]
    // 0xaec864: ldur            x0, [fp, #-8]
    // 0xaec868: StoreField: r2->field_b = r0
    //     0xaec868: stur            w0, [x2, #0xb]
    // 0xaec86c: ldur            x0, [fp, #-0x10]
    // 0xaec870: LoadField: r1 = r0->field_b
    //     0xaec870: ldur            w1, [x0, #0xb]
    // 0xaec874: LoadField: r3 = r0->field_f
    //     0xaec874: ldur            w3, [x0, #0xf]
    // 0xaec878: DecompressPointer r3
    //     0xaec878: add             x3, x3, HEAP, lsl #32
    // 0xaec87c: LoadField: r4 = r3->field_b
    //     0xaec87c: ldur            w4, [x3, #0xb]
    // 0xaec880: r3 = LoadInt32Instr(r1)
    //     0xaec880: sbfx            x3, x1, #1, #0x1f
    // 0xaec884: stur            x3, [fp, #-0x28]
    // 0xaec888: r1 = LoadInt32Instr(r4)
    //     0xaec888: sbfx            x1, x4, #1, #0x1f
    // 0xaec88c: cmp             x3, x1
    // 0xaec890: b.ne            #0xaec89c
    // 0xaec894: mov             x1, x0
    // 0xaec898: r0 = _growToNextCapacity()
    //     0xaec898: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaec89c: ldur            x2, [fp, #-0x10]
    // 0xaec8a0: ldur            x3, [fp, #-0x28]
    // 0xaec8a4: add             x0, x3, #1
    // 0xaec8a8: lsl             x1, x0, #1
    // 0xaec8ac: StoreField: r2->field_b = r1
    //     0xaec8ac: stur            w1, [x2, #0xb]
    // 0xaec8b0: LoadField: r1 = r2->field_f
    //     0xaec8b0: ldur            w1, [x2, #0xf]
    // 0xaec8b4: DecompressPointer r1
    //     0xaec8b4: add             x1, x1, HEAP, lsl #32
    // 0xaec8b8: ldur            x0, [fp, #-0x18]
    // 0xaec8bc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaec8bc: add             x25, x1, x3, lsl #2
    //     0xaec8c0: add             x25, x25, #0xf
    //     0xaec8c4: str             w0, [x25]
    //     0xaec8c8: tbz             w0, #0, #0xaec8e4
    //     0xaec8cc: ldurb           w16, [x1, #-1]
    //     0xaec8d0: ldurb           w17, [x0, #-1]
    //     0xaec8d4: and             x16, x17, x16, lsr #2
    //     0xaec8d8: tst             x16, HEAP, lsr #32
    //     0xaec8dc: b.eq            #0xaec8e4
    //     0xaec8e0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaec8e4: b               #0xaec8ec
    // 0xaec8e8: mov             x2, x1
    // 0xaec8ec: ldur            d0, [fp, #-0x40]
    // 0xaec8f0: ldur            x0, [fp, #-0x20]
    // 0xaec8f4: r0 = Stack()
    //     0xaec8f4: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xaec8f8: mov             x1, x0
    // 0xaec8fc: r0 = Instance_Alignment
    //     0xaec8fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xaec900: ldr             x0, [x0, #0x950]
    // 0xaec904: stur            x1, [fp, #-8]
    // 0xaec908: StoreField: r1->field_f = r0
    //     0xaec908: stur            w0, [x1, #0xf]
    // 0xaec90c: r0 = Instance_StackFit
    //     0xaec90c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xaec910: ldr             x0, [x0, #0xfa8]
    // 0xaec914: ArrayStore: r1[0] = r0  ; List_4
    //     0xaec914: stur            w0, [x1, #0x17]
    // 0xaec918: r0 = Instance_Clip
    //     0xaec918: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xaec91c: ldr             x0, [x0, #0x7e0]
    // 0xaec920: StoreField: r1->field_1b = r0
    //     0xaec920: stur            w0, [x1, #0x1b]
    // 0xaec924: ldur            x0, [fp, #-0x10]
    // 0xaec928: StoreField: r1->field_b = r0
    //     0xaec928: stur            w0, [x1, #0xb]
    // 0xaec92c: r0 = Padding()
    //     0xaec92c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaec930: mov             x1, x0
    // 0xaec934: ldur            x0, [fp, #-0x20]
    // 0xaec938: stur            x1, [fp, #-0x10]
    // 0xaec93c: StoreField: r1->field_f = r0
    //     0xaec93c: stur            w0, [x1, #0xf]
    // 0xaec940: ldur            x0, [fp, #-8]
    // 0xaec944: StoreField: r1->field_b = r0
    //     0xaec944: stur            w0, [x1, #0xb]
    // 0xaec948: ldur            d0, [fp, #-0x40]
    // 0xaec94c: r0 = inline_Allocate_Double()
    //     0xaec94c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xaec950: add             x0, x0, #0x10
    //     0xaec954: cmp             x2, x0
    //     0xaec958: b.ls            #0xaec9a8
    //     0xaec95c: str             x0, [THR, #0x50]  ; THR::top
    //     0xaec960: sub             x0, x0, #0xf
    //     0xaec964: movz            x2, #0xe15c
    //     0xaec968: movk            x2, #0x3, lsl #16
    //     0xaec96c: stur            x2, [x0, #-1]
    // 0xaec970: StoreField: r0->field_7 = d0
    //     0xaec970: stur            d0, [x0, #7]
    // 0xaec974: stur            x0, [fp, #-8]
    // 0xaec978: r0 = SizedBox()
    //     0xaec978: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaec97c: ldur            x1, [fp, #-8]
    // 0xaec980: StoreField: r0->field_f = r1
    //     0xaec980: stur            w1, [x0, #0xf]
    // 0xaec984: ldur            x1, [fp, #-0x10]
    // 0xaec988: StoreField: r0->field_b = r1
    //     0xaec988: stur            w1, [x0, #0xb]
    // 0xaec98c: LeaveFrame
    //     0xaec98c: mov             SP, fp
    //     0xaec990: ldp             fp, lr, [SP], #0x10
    // 0xaec994: ret
    //     0xaec994: ret             
    // 0xaec998: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec998: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec99c: b               #0xaec6b8
    // 0xaec9a0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaec9a0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xaec9a4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaec9a4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xaec9a8: SaveReg d0
    //     0xaec9a8: str             q0, [SP, #-0x10]!
    // 0xaec9ac: SaveReg r1
    //     0xaec9ac: str             x1, [SP, #-8]!
    // 0xaec9b0: r0 = AllocateDouble()
    //     0xaec9b0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaec9b4: RestoreReg r1
    //     0xaec9b4: ldr             x1, [SP], #8
    // 0xaec9b8: RestoreReg d0
    //     0xaec9b8: ldr             q0, [SP], #0x10
    // 0xaec9bc: b               #0xaec970
  }
  _ cosmeticThemeSlider(/* No info */) {
    // ** addr: 0xaec9c0, size: 0x814
    // 0xaec9c0: EnterFrame
    //     0xaec9c0: stp             fp, lr, [SP, #-0x10]!
    //     0xaec9c4: mov             fp, SP
    // 0xaec9c8: AllocStack(0x70)
    //     0xaec9c8: sub             SP, SP, #0x70
    // 0xaec9cc: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xaec9cc: stur            x1, [fp, #-8]
    //     0xaec9d0: stur            x2, [fp, #-0x10]
    //     0xaec9d4: stur            x3, [fp, #-0x18]
    // 0xaec9d8: CheckStackOverflow
    //     0xaec9d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec9dc: cmp             SP, x16
    //     0xaec9e0: b.ls            #0xaed1b0
    // 0xaec9e4: r1 = 2
    //     0xaec9e4: movz            x1, #0x2
    // 0xaec9e8: r0 = AllocateContext()
    //     0xaec9e8: bl              #0x16f6108  ; AllocateContextStub
    // 0xaec9ec: ldur            x1, [fp, #-8]
    // 0xaec9f0: stur            x0, [fp, #-0x20]
    // 0xaec9f4: StoreField: r0->field_f = r1
    //     0xaec9f4: stur            w1, [x0, #0xf]
    // 0xaec9f8: ldur            x2, [fp, #-0x10]
    // 0xaec9fc: StoreField: r0->field_13 = r2
    //     0xaec9fc: stur            w2, [x0, #0x13]
    // 0xaeca00: r0 = Radius()
    //     0xaeca00: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaeca04: d0 = 12.000000
    //     0xaeca04: fmov            d0, #12.00000000
    // 0xaeca08: stur            x0, [fp, #-0x28]
    // 0xaeca0c: StoreField: r0->field_7 = d0
    //     0xaeca0c: stur            d0, [x0, #7]
    // 0xaeca10: StoreField: r0->field_f = d0
    //     0xaeca10: stur            d0, [x0, #0xf]
    // 0xaeca14: r0 = BorderRadius()
    //     0xaeca14: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaeca18: mov             x1, x0
    // 0xaeca1c: ldur            x0, [fp, #-0x28]
    // 0xaeca20: stur            x1, [fp, #-0x30]
    // 0xaeca24: StoreField: r1->field_7 = r0
    //     0xaeca24: stur            w0, [x1, #7]
    // 0xaeca28: StoreField: r1->field_b = r0
    //     0xaeca28: stur            w0, [x1, #0xb]
    // 0xaeca2c: StoreField: r1->field_f = r0
    //     0xaeca2c: stur            w0, [x1, #0xf]
    // 0xaeca30: StoreField: r1->field_13 = r0
    //     0xaeca30: stur            w0, [x1, #0x13]
    // 0xaeca34: r0 = RoundedRectangleBorder()
    //     0xaeca34: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xaeca38: mov             x3, x0
    // 0xaeca3c: ldur            x0, [fp, #-0x30]
    // 0xaeca40: stur            x3, [fp, #-0x38]
    // 0xaeca44: StoreField: r3->field_b = r0
    //     0xaeca44: stur            w0, [x3, #0xb]
    // 0xaeca48: r0 = Instance_BorderSide
    //     0xaeca48: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xaeca4c: ldr             x0, [x0, #0xe20]
    // 0xaeca50: StoreField: r3->field_7 = r0
    //     0xaeca50: stur            w0, [x3, #7]
    // 0xaeca54: ldur            x0, [fp, #-0x10]
    // 0xaeca58: LoadField: r1 = r0->field_37
    //     0xaeca58: ldur            w1, [x0, #0x37]
    // 0xaeca5c: DecompressPointer r1
    //     0xaeca5c: add             x1, x1, HEAP, lsl #32
    // 0xaeca60: cmp             w1, NULL
    // 0xaeca64: b.ne            #0xaeca70
    // 0xaeca68: r0 = Null
    //     0xaeca68: mov             x0, NULL
    // 0xaeca6c: b               #0xaeca74
    // 0xaeca70: LoadField: r0 = r1->field_b
    //     0xaeca70: ldur            w0, [x1, #0xb]
    // 0xaeca74: cmp             w0, NULL
    // 0xaeca78: b.ne            #0xaeca84
    // 0xaeca7c: r1 = 0
    //     0xaeca7c: movz            x1, #0
    // 0xaeca80: b               #0xaeca88
    // 0xaeca84: r1 = LoadInt32Instr(r0)
    //     0xaeca84: sbfx            x1, x0, #1, #0x1f
    // 0xaeca88: ldur            x0, [fp, #-8]
    // 0xaeca8c: ldur            x4, [fp, #-0x20]
    // 0xaeca90: ArrayLoad: r5 = r0[0]  ; List_4
    //     0xaeca90: ldur            w5, [x0, #0x17]
    // 0xaeca94: DecompressPointer r5
    //     0xaeca94: add             x5, x5, HEAP, lsl #32
    // 0xaeca98: r16 = Sentinel
    //     0xaeca98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaeca9c: cmp             w5, w16
    // 0xaecaa0: b.eq            #0xaed1b8
    // 0xaecaa4: stur            x5, [fp, #-0x28]
    // 0xaecaa8: lsl             x6, x1, #1
    // 0xaecaac: mov             x2, x4
    // 0xaecab0: stur            x6, [fp, #-0x10]
    // 0xaecab4: r1 = Function '<anonymous closure>':.
    //     0xaecab4: add             x1, PP, #0x58, lsl #12  ; [pp+0x581a8] AnonymousClosure: (0xaef0b0), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::cosmeticThemeSlider (0xaec9c0)
    //     0xaecab8: ldr             x1, [x1, #0x1a8]
    // 0xaecabc: r0 = AllocateClosure()
    //     0xaecabc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaecac0: ldur            x2, [fp, #-0x20]
    // 0xaecac4: r1 = Function '<anonymous closure>':.
    //     0xaecac4: add             x1, PP, #0x58, lsl #12  ; [pp+0x581b0] AnonymousClosure: (0xaedec0), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::cosmeticThemeSlider (0xaec9c0)
    //     0xaecac8: ldr             x1, [x1, #0x1b0]
    // 0xaecacc: stur            x0, [fp, #-0x30]
    // 0xaecad0: r0 = AllocateClosure()
    //     0xaecad0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaecad4: stur            x0, [fp, #-0x40]
    // 0xaecad8: r0 = PageView()
    //     0xaecad8: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xaecadc: stur            x0, [fp, #-0x48]
    // 0xaecae0: ldur            x16, [fp, #-0x28]
    // 0xaecae4: str             x16, [SP]
    // 0xaecae8: mov             x1, x0
    // 0xaecaec: ldur            x2, [fp, #-0x40]
    // 0xaecaf0: ldur            x3, [fp, #-0x10]
    // 0xaecaf4: ldur            x5, [fp, #-0x30]
    // 0xaecaf8: r4 = const [0, 0x5, 0x1, 0x4, controller, 0x4, null]
    //     0xaecaf8: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d60] List(7) [0, 0x5, 0x1, 0x4, "controller", 0x4, Null]
    //     0xaecafc: ldr             x4, [x4, #0xd60]
    // 0xaecb00: r0 = PageView.builder()
    //     0xaecb00: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xaecb04: r0 = AspectRatio()
    //     0xaecb04: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xaecb08: d0 = 1.000000
    //     0xaecb08: fmov            d0, #1.00000000
    // 0xaecb0c: stur            x0, [fp, #-0x10]
    // 0xaecb10: StoreField: r0->field_f = d0
    //     0xaecb10: stur            d0, [x0, #0xf]
    // 0xaecb14: ldur            x1, [fp, #-0x48]
    // 0xaecb18: StoreField: r0->field_b = r1
    //     0xaecb18: stur            w1, [x0, #0xb]
    // 0xaecb1c: r0 = Card()
    //     0xaecb1c: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xaecb20: mov             x2, x0
    // 0xaecb24: r0 = 0.000000
    //     0xaecb24: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xaecb28: stur            x2, [fp, #-0x28]
    // 0xaecb2c: ArrayStore: r2[0] = r0  ; List_4
    //     0xaecb2c: stur            w0, [x2, #0x17]
    // 0xaecb30: ldur            x0, [fp, #-0x38]
    // 0xaecb34: StoreField: r2->field_1b = r0
    //     0xaecb34: stur            w0, [x2, #0x1b]
    // 0xaecb38: r0 = true
    //     0xaecb38: add             x0, NULL, #0x20  ; true
    // 0xaecb3c: StoreField: r2->field_1f = r0
    //     0xaecb3c: stur            w0, [x2, #0x1f]
    // 0xaecb40: ldur            x1, [fp, #-0x10]
    // 0xaecb44: StoreField: r2->field_2f = r1
    //     0xaecb44: stur            w1, [x2, #0x2f]
    // 0xaecb48: StoreField: r2->field_2b = r0
    //     0xaecb48: stur            w0, [x2, #0x2b]
    // 0xaecb4c: r1 = Instance__CardVariant
    //     0xaecb4c: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xaecb50: ldr             x1, [x1, #0xa68]
    // 0xaecb54: StoreField: r2->field_33 = r1
    //     0xaecb54: stur            w1, [x2, #0x33]
    // 0xaecb58: ldur            x3, [fp, #-0x20]
    // 0xaecb5c: LoadField: r1 = r3->field_13
    //     0xaecb5c: ldur            w1, [x3, #0x13]
    // 0xaecb60: DecompressPointer r1
    //     0xaecb60: add             x1, x1, HEAP, lsl #32
    // 0xaecb64: LoadField: r4 = r1->field_33
    //     0xaecb64: ldur            w4, [x1, #0x33]
    // 0xaecb68: DecompressPointer r4
    //     0xaecb68: add             x4, x4, HEAP, lsl #32
    // 0xaecb6c: cmp             w4, NULL
    // 0xaecb70: b.ne            #0xaecb7c
    // 0xaecb74: r1 = Null
    //     0xaecb74: mov             x1, NULL
    // 0xaecb78: b               #0xaecb84
    // 0xaecb7c: LoadField: r1 = r4->field_7
    //     0xaecb7c: ldur            w1, [x4, #7]
    // 0xaecb80: DecompressPointer r1
    //     0xaecb80: add             x1, x1, HEAP, lsl #32
    // 0xaecb84: cmp             w1, NULL
    // 0xaecb88: b.ne            #0xaecb90
    // 0xaecb8c: r1 = ""
    //     0xaecb8c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaecb90: ldur            x4, [fp, #-8]
    // 0xaecb94: r0 = capitalizeFirstWord()
    //     0xaecb94: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xaecb98: mov             x2, x0
    // 0xaecb9c: ldur            x0, [fp, #-8]
    // 0xaecba0: stur            x2, [fp, #-0x10]
    // 0xaecba4: LoadField: r1 = r0->field_f
    //     0xaecba4: ldur            w1, [x0, #0xf]
    // 0xaecba8: DecompressPointer r1
    //     0xaecba8: add             x1, x1, HEAP, lsl #32
    // 0xaecbac: cmp             w1, NULL
    // 0xaecbb0: b.eq            #0xaed1c4
    // 0xaecbb4: r0 = of()
    //     0xaecbb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaecbb8: LoadField: r1 = r0->field_87
    //     0xaecbb8: ldur            w1, [x0, #0x87]
    // 0xaecbbc: DecompressPointer r1
    //     0xaecbbc: add             x1, x1, HEAP, lsl #32
    // 0xaecbc0: LoadField: r0 = r1->field_2b
    //     0xaecbc0: ldur            w0, [x1, #0x2b]
    // 0xaecbc4: DecompressPointer r0
    //     0xaecbc4: add             x0, x0, HEAP, lsl #32
    // 0xaecbc8: r16 = 14.000000
    //     0xaecbc8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xaecbcc: ldr             x16, [x16, #0x1d8]
    // 0xaecbd0: r30 = Instance_Color
    //     0xaecbd0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaecbd4: stp             lr, x16, [SP]
    // 0xaecbd8: mov             x1, x0
    // 0xaecbdc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaecbdc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaecbe0: ldr             x4, [x4, #0xaa0]
    // 0xaecbe4: r0 = copyWith()
    //     0xaecbe4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaecbe8: stur            x0, [fp, #-0x30]
    // 0xaecbec: r0 = Text()
    //     0xaecbec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaecbf0: mov             x1, x0
    // 0xaecbf4: ldur            x0, [fp, #-0x10]
    // 0xaecbf8: stur            x1, [fp, #-0x38]
    // 0xaecbfc: StoreField: r1->field_b = r0
    //     0xaecbfc: stur            w0, [x1, #0xb]
    // 0xaecc00: ldur            x0, [fp, #-0x30]
    // 0xaecc04: StoreField: r1->field_13 = r0
    //     0xaecc04: stur            w0, [x1, #0x13]
    // 0xaecc08: r0 = Instance_TextOverflow
    //     0xaecc08: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xaecc0c: ldr             x0, [x0, #0xe10]
    // 0xaecc10: StoreField: r1->field_2b = r0
    //     0xaecc10: stur            w0, [x1, #0x2b]
    // 0xaecc14: r0 = 2
    //     0xaecc14: movz            x0, #0x2
    // 0xaecc18: StoreField: r1->field_37 = r0
    //     0xaecc18: stur            w0, [x1, #0x37]
    // 0xaecc1c: r0 = Padding()
    //     0xaecc1c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaecc20: mov             x3, x0
    // 0xaecc24: r0 = Instance_EdgeInsets
    //     0xaecc24: add             x0, PP, #0x34, lsl #12  ; [pp+0x34590] Obj!EdgeInsets@d57951
    //     0xaecc28: ldr             x0, [x0, #0x590]
    // 0xaecc2c: stur            x3, [fp, #-0x10]
    // 0xaecc30: StoreField: r3->field_f = r0
    //     0xaecc30: stur            w0, [x3, #0xf]
    // 0xaecc34: ldur            x1, [fp, #-0x38]
    // 0xaecc38: StoreField: r3->field_b = r1
    //     0xaecc38: stur            w1, [x3, #0xb]
    // 0xaecc3c: ldur            x4, [fp, #-0x20]
    // 0xaecc40: LoadField: r2 = r4->field_13
    //     0xaecc40: ldur            w2, [x4, #0x13]
    // 0xaecc44: DecompressPointer r2
    //     0xaecc44: add             x2, x2, HEAP, lsl #32
    // 0xaecc48: ldur            x1, [fp, #-8]
    // 0xaecc4c: r0 = _buildRatingWidget()
    //     0xaecc4c: bl              #0xaed790  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildRatingWidget
    // 0xaecc50: mov             x3, x0
    // 0xaecc54: ldur            x0, [fp, #-0x20]
    // 0xaecc58: stur            x3, [fp, #-0x38]
    // 0xaecc5c: LoadField: r1 = r0->field_13
    //     0xaecc5c: ldur            w1, [x0, #0x13]
    // 0xaecc60: DecompressPointer r1
    //     0xaecc60: add             x1, x1, HEAP, lsl #32
    // 0xaecc64: LoadField: r4 = r1->field_43
    //     0xaecc64: ldur            w4, [x1, #0x43]
    // 0xaecc68: DecompressPointer r4
    //     0xaecc68: add             x4, x4, HEAP, lsl #32
    // 0xaecc6c: stur            x4, [fp, #-0x30]
    // 0xaecc70: r1 = Null
    //     0xaecc70: mov             x1, NULL
    // 0xaecc74: r2 = 4
    //     0xaecc74: movz            x2, #0x4
    // 0xaecc78: r0 = AllocateArray()
    //     0xaecc78: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaecc7c: mov             x1, x0
    // 0xaecc80: ldur            x0, [fp, #-0x30]
    // 0xaecc84: StoreField: r1->field_f = r0
    //     0xaecc84: stur            w0, [x1, #0xf]
    // 0xaecc88: r16 = " "
    //     0xaecc88: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xaecc8c: StoreField: r1->field_13 = r16
    //     0xaecc8c: stur            w16, [x1, #0x13]
    // 0xaecc90: str             x1, [SP]
    // 0xaecc94: r0 = _interpolate()
    //     0xaecc94: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xaecc98: mov             x2, x0
    // 0xaecc9c: ldur            x0, [fp, #-8]
    // 0xaecca0: stur            x2, [fp, #-0x30]
    // 0xaecca4: LoadField: r1 = r0->field_f
    //     0xaecca4: ldur            w1, [x0, #0xf]
    // 0xaecca8: DecompressPointer r1
    //     0xaecca8: add             x1, x1, HEAP, lsl #32
    // 0xaeccac: cmp             w1, NULL
    // 0xaeccb0: b.eq            #0xaed1c8
    // 0xaeccb4: r0 = of()
    //     0xaeccb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaeccb8: LoadField: r1 = r0->field_87
    //     0xaeccb8: ldur            w1, [x0, #0x87]
    // 0xaeccbc: DecompressPointer r1
    //     0xaeccbc: add             x1, x1, HEAP, lsl #32
    // 0xaeccc0: LoadField: r0 = r1->field_27
    //     0xaeccc0: ldur            w0, [x1, #0x27]
    // 0xaeccc4: DecompressPointer r0
    //     0xaeccc4: add             x0, x0, HEAP, lsl #32
    // 0xaeccc8: r16 = 15.000000
    //     0xaeccc8: add             x16, PP, #0x41, lsl #12  ; [pp+0x41480] 15
    //     0xaecccc: ldr             x16, [x16, #0x480]
    // 0xaeccd0: r30 = Instance_Color
    //     0xaeccd0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaeccd4: stp             lr, x16, [SP]
    // 0xaeccd8: mov             x1, x0
    // 0xaeccdc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaeccdc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaecce0: ldr             x4, [x4, #0xaa0]
    // 0xaecce4: r0 = copyWith()
    //     0xaecce4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaecce8: stur            x0, [fp, #-0x40]
    // 0xaeccec: r0 = Text()
    //     0xaeccec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaeccf0: mov             x1, x0
    // 0xaeccf4: ldur            x0, [fp, #-0x30]
    // 0xaeccf8: stur            x1, [fp, #-0x48]
    // 0xaeccfc: StoreField: r1->field_b = r0
    //     0xaeccfc: stur            w0, [x1, #0xb]
    // 0xaecd00: ldur            x0, [fp, #-0x40]
    // 0xaecd04: StoreField: r1->field_13 = r0
    //     0xaecd04: stur            w0, [x1, #0x13]
    // 0xaecd08: r0 = WidgetSpan()
    //     0xaecd08: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xaecd0c: mov             x1, x0
    // 0xaecd10: ldur            x0, [fp, #-0x48]
    // 0xaecd14: stur            x1, [fp, #-0x30]
    // 0xaecd18: StoreField: r1->field_13 = r0
    //     0xaecd18: stur            w0, [x1, #0x13]
    // 0xaecd1c: r0 = Instance_PlaceholderAlignment
    //     0xaecd1c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] Obj!PlaceholderAlignment@d76401
    //     0xaecd20: ldr             x0, [x0, #0xa0]
    // 0xaecd24: StoreField: r1->field_b = r0
    //     0xaecd24: stur            w0, [x1, #0xb]
    // 0xaecd28: ldur            x2, [fp, #-0x20]
    // 0xaecd2c: LoadField: r0 = r2->field_13
    //     0xaecd2c: ldur            w0, [x2, #0x13]
    // 0xaecd30: DecompressPointer r0
    //     0xaecd30: add             x0, x0, HEAP, lsl #32
    // 0xaecd34: LoadField: r3 = r0->field_4b
    //     0xaecd34: ldur            w3, [x0, #0x4b]
    // 0xaecd38: DecompressPointer r3
    //     0xaecd38: add             x3, x3, HEAP, lsl #32
    // 0xaecd3c: str             x3, [SP]
    // 0xaecd40: r0 = _interpolateSingle()
    //     0xaecd40: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xaecd44: mov             x2, x0
    // 0xaecd48: ldur            x0, [fp, #-8]
    // 0xaecd4c: stur            x2, [fp, #-0x40]
    // 0xaecd50: LoadField: r1 = r0->field_f
    //     0xaecd50: ldur            w1, [x0, #0xf]
    // 0xaecd54: DecompressPointer r1
    //     0xaecd54: add             x1, x1, HEAP, lsl #32
    // 0xaecd58: cmp             w1, NULL
    // 0xaecd5c: b.eq            #0xaed1cc
    // 0xaecd60: r0 = of()
    //     0xaecd60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaecd64: LoadField: r1 = r0->field_87
    //     0xaecd64: ldur            w1, [x0, #0x87]
    // 0xaecd68: DecompressPointer r1
    //     0xaecd68: add             x1, x1, HEAP, lsl #32
    // 0xaecd6c: LoadField: r0 = r1->field_2b
    //     0xaecd6c: ldur            w0, [x1, #0x2b]
    // 0xaecd70: DecompressPointer r0
    //     0xaecd70: add             x0, x0, HEAP, lsl #32
    // 0xaecd74: stur            x0, [fp, #-0x48]
    // 0xaecd78: r1 = Instance_Color
    //     0xaecd78: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaecd7c: d0 = 0.400000
    //     0xaecd7c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xaecd80: r0 = withOpacity()
    //     0xaecd80: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaecd84: r16 = Instance_TextDecoration
    //     0xaecd84: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xaecd88: ldr             x16, [x16, #0xe30]
    // 0xaecd8c: r30 = 10.000000
    //     0xaecd8c: ldr             lr, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xaecd90: stp             lr, x16, [SP, #8]
    // 0xaecd94: str             x0, [SP]
    // 0xaecd98: ldur            x1, [fp, #-0x48]
    // 0xaecd9c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, decoration, 0x1, fontSize, 0x2, null]
    //     0xaecd9c: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb60] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "decoration", 0x1, "fontSize", 0x2, Null]
    //     0xaecda0: ldr             x4, [x4, #0xb60]
    // 0xaecda4: r0 = copyWith()
    //     0xaecda4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaecda8: stur            x0, [fp, #-0x48]
    // 0xaecdac: r0 = TextSpan()
    //     0xaecdac: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaecdb0: mov             x3, x0
    // 0xaecdb4: ldur            x0, [fp, #-0x40]
    // 0xaecdb8: stur            x3, [fp, #-0x50]
    // 0xaecdbc: StoreField: r3->field_b = r0
    //     0xaecdbc: stur            w0, [x3, #0xb]
    // 0xaecdc0: r0 = Instance__DeferringMouseCursor
    //     0xaecdc0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaecdc4: ArrayStore: r3[0] = r0  ; List_4
    //     0xaecdc4: stur            w0, [x3, #0x17]
    // 0xaecdc8: ldur            x1, [fp, #-0x48]
    // 0xaecdcc: StoreField: r3->field_7 = r1
    //     0xaecdcc: stur            w1, [x3, #7]
    // 0xaecdd0: r1 = Null
    //     0xaecdd0: mov             x1, NULL
    // 0xaecdd4: r2 = 4
    //     0xaecdd4: movz            x2, #0x4
    // 0xaecdd8: r0 = AllocateArray()
    //     0xaecdd8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaecddc: mov             x2, x0
    // 0xaecde0: ldur            x0, [fp, #-0x30]
    // 0xaecde4: stur            x2, [fp, #-0x40]
    // 0xaecde8: StoreField: r2->field_f = r0
    //     0xaecde8: stur            w0, [x2, #0xf]
    // 0xaecdec: ldur            x0, [fp, #-0x50]
    // 0xaecdf0: StoreField: r2->field_13 = r0
    //     0xaecdf0: stur            w0, [x2, #0x13]
    // 0xaecdf4: r1 = <InlineSpan>
    //     0xaecdf4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xaecdf8: ldr             x1, [x1, #0xe40]
    // 0xaecdfc: r0 = AllocateGrowableArray()
    //     0xaecdfc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaece00: mov             x1, x0
    // 0xaece04: ldur            x0, [fp, #-0x40]
    // 0xaece08: stur            x1, [fp, #-0x30]
    // 0xaece0c: StoreField: r1->field_f = r0
    //     0xaece0c: stur            w0, [x1, #0xf]
    // 0xaece10: r0 = 4
    //     0xaece10: movz            x0, #0x4
    // 0xaece14: StoreField: r1->field_b = r0
    //     0xaece14: stur            w0, [x1, #0xb]
    // 0xaece18: ldur            x2, [fp, #-0x20]
    // 0xaece1c: LoadField: r0 = r2->field_13
    //     0xaece1c: ldur            w0, [x2, #0x13]
    // 0xaece20: DecompressPointer r0
    //     0xaece20: add             x0, x0, HEAP, lsl #32
    // 0xaece24: LoadField: r3 = r0->field_63
    //     0xaece24: ldur            w3, [x0, #0x63]
    // 0xaece28: DecompressPointer r3
    //     0xaece28: add             x3, x3, HEAP, lsl #32
    // 0xaece2c: r0 = 60
    //     0xaece2c: movz            x0, #0x3c
    // 0xaece30: branchIfSmi(r3, 0xaece3c)
    //     0xaece30: tbz             w3, #0, #0xaece3c
    // 0xaece34: r0 = LoadClassIdInstr(r3)
    //     0xaece34: ldur            x0, [x3, #-1]
    //     0xaece38: ubfx            x0, x0, #0xc, #0x14
    // 0xaece3c: r16 = 0.000000
    //     0xaece3c: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xaece40: stp             x16, x3, [SP]
    // 0xaece44: mov             lr, x0
    // 0xaece48: ldr             lr, [x21, lr, lsl #3]
    // 0xaece4c: blr             lr
    // 0xaece50: tbz             w0, #4, #0xaecfe0
    // 0xaece54: ldur            x4, [fp, #-8]
    // 0xaece58: ldur            x3, [fp, #-0x20]
    // 0xaece5c: ldur            x0, [fp, #-0x30]
    // 0xaece60: r1 = Null
    //     0xaece60: mov             x1, NULL
    // 0xaece64: r2 = 6
    //     0xaece64: movz            x2, #0x6
    // 0xaece68: r0 = AllocateArray()
    //     0xaece68: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaece6c: stur            x0, [fp, #-0x40]
    // 0xaece70: r16 = " | "
    //     0xaece70: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d80] " | "
    //     0xaece74: ldr             x16, [x16, #0xd80]
    // 0xaece78: StoreField: r0->field_f = r16
    //     0xaece78: stur            w16, [x0, #0xf]
    // 0xaece7c: ldur            x2, [fp, #-0x20]
    // 0xaece80: LoadField: r1 = r2->field_13
    //     0xaece80: ldur            w1, [x2, #0x13]
    // 0xaece84: DecompressPointer r1
    //     0xaece84: add             x1, x1, HEAP, lsl #32
    // 0xaece88: LoadField: r3 = r1->field_63
    //     0xaece88: ldur            w3, [x1, #0x63]
    // 0xaece8c: DecompressPointer r3
    //     0xaece8c: add             x3, x3, HEAP, lsl #32
    // 0xaece90: stp             xzr, x3, [SP]
    // 0xaece94: r4 = 0
    //     0xaece94: movz            x4, #0
    // 0xaece98: ldr             x0, [SP, #8]
    // 0xaece9c: r5 = UnlinkedCall_0x613b5c
    //     0xaece9c: add             x16, PP, #0x58, lsl #12  ; [pp+0x581b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaecea0: ldp             x5, lr, [x16, #0x1b8]
    // 0xaecea4: blr             lr
    // 0xaecea8: ldur            x1, [fp, #-0x40]
    // 0xaeceac: ArrayStore: r1[1] = r0  ; List_4
    //     0xaeceac: add             x25, x1, #0x13
    //     0xaeceb0: str             w0, [x25]
    //     0xaeceb4: tbz             w0, #0, #0xaeced0
    //     0xaeceb8: ldurb           w16, [x1, #-1]
    //     0xaecebc: ldurb           w17, [x0, #-1]
    //     0xaecec0: and             x16, x17, x16, lsr #2
    //     0xaecec4: tst             x16, HEAP, lsr #32
    //     0xaecec8: b.eq            #0xaeced0
    //     0xaececc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaeced0: ldur            x0, [fp, #-0x40]
    // 0xaeced4: r16 = "% OFF"
    //     0xaeced4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d98] "% OFF"
    //     0xaeced8: ldr             x16, [x16, #0xd98]
    // 0xaecedc: ArrayStore: r0[0] = r16  ; List_4
    //     0xaecedc: stur            w16, [x0, #0x17]
    // 0xaecee0: str             x0, [SP]
    // 0xaecee4: r0 = _interpolate()
    //     0xaecee4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xaecee8: mov             x2, x0
    // 0xaeceec: ldur            x0, [fp, #-8]
    // 0xaecef0: stur            x2, [fp, #-0x40]
    // 0xaecef4: LoadField: r1 = r0->field_f
    //     0xaecef4: ldur            w1, [x0, #0xf]
    // 0xaecef8: DecompressPointer r1
    //     0xaecef8: add             x1, x1, HEAP, lsl #32
    // 0xaecefc: cmp             w1, NULL
    // 0xaecf00: b.eq            #0xaed1d0
    // 0xaecf04: r0 = of()
    //     0xaecf04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaecf08: LoadField: r1 = r0->field_87
    //     0xaecf08: ldur            w1, [x0, #0x87]
    // 0xaecf0c: DecompressPointer r1
    //     0xaecf0c: add             x1, x1, HEAP, lsl #32
    // 0xaecf10: LoadField: r0 = r1->field_2b
    //     0xaecf10: ldur            w0, [x1, #0x2b]
    // 0xaecf14: DecompressPointer r0
    //     0xaecf14: add             x0, x0, HEAP, lsl #32
    // 0xaecf18: r16 = Instance_Color
    //     0xaecf18: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xaecf1c: ldr             x16, [x16, #0x858]
    // 0xaecf20: r30 = 12.000000
    //     0xaecf20: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaecf24: ldr             lr, [lr, #0x9e8]
    // 0xaecf28: stp             lr, x16, [SP]
    // 0xaecf2c: mov             x1, x0
    // 0xaecf30: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xaecf30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xaecf34: ldr             x4, [x4, #0x9b8]
    // 0xaecf38: r0 = copyWith()
    //     0xaecf38: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaecf3c: stur            x0, [fp, #-0x48]
    // 0xaecf40: r0 = TextSpan()
    //     0xaecf40: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaecf44: mov             x2, x0
    // 0xaecf48: ldur            x0, [fp, #-0x40]
    // 0xaecf4c: stur            x2, [fp, #-0x50]
    // 0xaecf50: StoreField: r2->field_b = r0
    //     0xaecf50: stur            w0, [x2, #0xb]
    // 0xaecf54: r0 = Instance__DeferringMouseCursor
    //     0xaecf54: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaecf58: ArrayStore: r2[0] = r0  ; List_4
    //     0xaecf58: stur            w0, [x2, #0x17]
    // 0xaecf5c: ldur            x1, [fp, #-0x48]
    // 0xaecf60: StoreField: r2->field_7 = r1
    //     0xaecf60: stur            w1, [x2, #7]
    // 0xaecf64: ldur            x3, [fp, #-0x30]
    // 0xaecf68: LoadField: r1 = r3->field_b
    //     0xaecf68: ldur            w1, [x3, #0xb]
    // 0xaecf6c: LoadField: r4 = r3->field_f
    //     0xaecf6c: ldur            w4, [x3, #0xf]
    // 0xaecf70: DecompressPointer r4
    //     0xaecf70: add             x4, x4, HEAP, lsl #32
    // 0xaecf74: LoadField: r5 = r4->field_b
    //     0xaecf74: ldur            w5, [x4, #0xb]
    // 0xaecf78: r4 = LoadInt32Instr(r1)
    //     0xaecf78: sbfx            x4, x1, #1, #0x1f
    // 0xaecf7c: stur            x4, [fp, #-0x58]
    // 0xaecf80: r1 = LoadInt32Instr(r5)
    //     0xaecf80: sbfx            x1, x5, #1, #0x1f
    // 0xaecf84: cmp             x4, x1
    // 0xaecf88: b.ne            #0xaecf94
    // 0xaecf8c: mov             x1, x3
    // 0xaecf90: r0 = _growToNextCapacity()
    //     0xaecf90: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaecf94: ldur            x2, [fp, #-0x30]
    // 0xaecf98: ldur            x3, [fp, #-0x58]
    // 0xaecf9c: add             x0, x3, #1
    // 0xaecfa0: lsl             x1, x0, #1
    // 0xaecfa4: StoreField: r2->field_b = r1
    //     0xaecfa4: stur            w1, [x2, #0xb]
    // 0xaecfa8: LoadField: r1 = r2->field_f
    //     0xaecfa8: ldur            w1, [x2, #0xf]
    // 0xaecfac: DecompressPointer r1
    //     0xaecfac: add             x1, x1, HEAP, lsl #32
    // 0xaecfb0: ldur            x0, [fp, #-0x50]
    // 0xaecfb4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaecfb4: add             x25, x1, x3, lsl #2
    //     0xaecfb8: add             x25, x25, #0xf
    //     0xaecfbc: str             w0, [x25]
    //     0xaecfc0: tbz             w0, #0, #0xaecfdc
    //     0xaecfc4: ldurb           w16, [x1, #-1]
    //     0xaecfc8: ldurb           w17, [x0, #-1]
    //     0xaecfcc: and             x16, x17, x16, lsr #2
    //     0xaecfd0: tst             x16, HEAP, lsr #32
    //     0xaecfd4: b.eq            #0xaecfdc
    //     0xaecfd8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaecfdc: b               #0xaecfe4
    // 0xaecfe0: ldur            x2, [fp, #-0x30]
    // 0xaecfe4: ldur            x0, [fp, #-0x20]
    // 0xaecfe8: ldur            x4, [fp, #-0x28]
    // 0xaecfec: ldur            x3, [fp, #-0x10]
    // 0xaecff0: ldur            x1, [fp, #-0x38]
    // 0xaecff4: r0 = TextSpan()
    //     0xaecff4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaecff8: mov             x1, x0
    // 0xaecffc: ldur            x0, [fp, #-0x30]
    // 0xaed000: stur            x1, [fp, #-0x40]
    // 0xaed004: StoreField: r1->field_f = r0
    //     0xaed004: stur            w0, [x1, #0xf]
    // 0xaed008: r0 = Instance__DeferringMouseCursor
    //     0xaed008: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaed00c: ArrayStore: r1[0] = r0  ; List_4
    //     0xaed00c: stur            w0, [x1, #0x17]
    // 0xaed010: r0 = RichText()
    //     0xaed010: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xaed014: mov             x1, x0
    // 0xaed018: ldur            x2, [fp, #-0x40]
    // 0xaed01c: stur            x0, [fp, #-0x30]
    // 0xaed020: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaed020: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaed024: r0 = RichText()
    //     0xaed024: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xaed028: r0 = Padding()
    //     0xaed028: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaed02c: mov             x4, x0
    // 0xaed030: r0 = Instance_EdgeInsets
    //     0xaed030: add             x0, PP, #0x34, lsl #12  ; [pp+0x34590] Obj!EdgeInsets@d57951
    //     0xaed034: ldr             x0, [x0, #0x590]
    // 0xaed038: stur            x4, [fp, #-0x40]
    // 0xaed03c: StoreField: r4->field_f = r0
    //     0xaed03c: stur            w0, [x4, #0xf]
    // 0xaed040: ldur            x0, [fp, #-0x30]
    // 0xaed044: StoreField: r4->field_b = r0
    //     0xaed044: stur            w0, [x4, #0xb]
    // 0xaed048: ldur            x0, [fp, #-0x20]
    // 0xaed04c: LoadField: r2 = r0->field_13
    //     0xaed04c: ldur            w2, [x0, #0x13]
    // 0xaed050: DecompressPointer r2
    //     0xaed050: add             x2, x2, HEAP, lsl #32
    // 0xaed054: ldur            x1, [fp, #-8]
    // 0xaed058: ldur            x3, [fp, #-0x18]
    // 0xaed05c: r0 = _buildAddToBagSection()
    //     0xaed05c: bl              #0xaed1d4  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildAddToBagSection
    // 0xaed060: r1 = <FlexParentData>
    //     0xaed060: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xaed064: ldr             x1, [x1, #0xe00]
    // 0xaed068: stur            x0, [fp, #-8]
    // 0xaed06c: r0 = Expanded()
    //     0xaed06c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaed070: mov             x3, x0
    // 0xaed074: r0 = 1
    //     0xaed074: movz            x0, #0x1
    // 0xaed078: stur            x3, [fp, #-0x30]
    // 0xaed07c: StoreField: r3->field_13 = r0
    //     0xaed07c: stur            x0, [x3, #0x13]
    // 0xaed080: r0 = Instance_FlexFit
    //     0xaed080: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xaed084: ldr             x0, [x0, #0xe08]
    // 0xaed088: StoreField: r3->field_1b = r0
    //     0xaed088: stur            w0, [x3, #0x1b]
    // 0xaed08c: ldur            x0, [fp, #-8]
    // 0xaed090: StoreField: r3->field_b = r0
    //     0xaed090: stur            w0, [x3, #0xb]
    // 0xaed094: r1 = Null
    //     0xaed094: mov             x1, NULL
    // 0xaed098: r2 = 10
    //     0xaed098: movz            x2, #0xa
    // 0xaed09c: r0 = AllocateArray()
    //     0xaed09c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaed0a0: mov             x2, x0
    // 0xaed0a4: ldur            x0, [fp, #-0x28]
    // 0xaed0a8: stur            x2, [fp, #-8]
    // 0xaed0ac: StoreField: r2->field_f = r0
    //     0xaed0ac: stur            w0, [x2, #0xf]
    // 0xaed0b0: ldur            x0, [fp, #-0x10]
    // 0xaed0b4: StoreField: r2->field_13 = r0
    //     0xaed0b4: stur            w0, [x2, #0x13]
    // 0xaed0b8: ldur            x0, [fp, #-0x38]
    // 0xaed0bc: ArrayStore: r2[0] = r0  ; List_4
    //     0xaed0bc: stur            w0, [x2, #0x17]
    // 0xaed0c0: ldur            x0, [fp, #-0x40]
    // 0xaed0c4: StoreField: r2->field_1b = r0
    //     0xaed0c4: stur            w0, [x2, #0x1b]
    // 0xaed0c8: ldur            x0, [fp, #-0x30]
    // 0xaed0cc: StoreField: r2->field_1f = r0
    //     0xaed0cc: stur            w0, [x2, #0x1f]
    // 0xaed0d0: r1 = <Widget>
    //     0xaed0d0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaed0d4: r0 = AllocateGrowableArray()
    //     0xaed0d4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaed0d8: mov             x1, x0
    // 0xaed0dc: ldur            x0, [fp, #-8]
    // 0xaed0e0: stur            x1, [fp, #-0x10]
    // 0xaed0e4: StoreField: r1->field_f = r0
    //     0xaed0e4: stur            w0, [x1, #0xf]
    // 0xaed0e8: r0 = 10
    //     0xaed0e8: movz            x0, #0xa
    // 0xaed0ec: StoreField: r1->field_b = r0
    //     0xaed0ec: stur            w0, [x1, #0xb]
    // 0xaed0f0: r0 = Column()
    //     0xaed0f0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaed0f4: mov             x1, x0
    // 0xaed0f8: r0 = Instance_Axis
    //     0xaed0f8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaed0fc: stur            x1, [fp, #-8]
    // 0xaed100: StoreField: r1->field_f = r0
    //     0xaed100: stur            w0, [x1, #0xf]
    // 0xaed104: r0 = Instance_MainAxisAlignment
    //     0xaed104: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaed108: ldr             x0, [x0, #0xa08]
    // 0xaed10c: StoreField: r1->field_13 = r0
    //     0xaed10c: stur            w0, [x1, #0x13]
    // 0xaed110: r0 = Instance_MainAxisSize
    //     0xaed110: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xaed114: ldr             x0, [x0, #0xdd0]
    // 0xaed118: ArrayStore: r1[0] = r0  ; List_4
    //     0xaed118: stur            w0, [x1, #0x17]
    // 0xaed11c: r0 = Instance_CrossAxisAlignment
    //     0xaed11c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaed120: ldr             x0, [x0, #0x890]
    // 0xaed124: StoreField: r1->field_1b = r0
    //     0xaed124: stur            w0, [x1, #0x1b]
    // 0xaed128: r0 = Instance_VerticalDirection
    //     0xaed128: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaed12c: ldr             x0, [x0, #0xa20]
    // 0xaed130: StoreField: r1->field_23 = r0
    //     0xaed130: stur            w0, [x1, #0x23]
    // 0xaed134: r0 = Instance_Clip
    //     0xaed134: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaed138: ldr             x0, [x0, #0x38]
    // 0xaed13c: StoreField: r1->field_2b = r0
    //     0xaed13c: stur            w0, [x1, #0x2b]
    // 0xaed140: StoreField: r1->field_2f = rZR
    //     0xaed140: stur            xzr, [x1, #0x2f]
    // 0xaed144: ldur            x0, [fp, #-0x10]
    // 0xaed148: StoreField: r1->field_b = r0
    //     0xaed148: stur            w0, [x1, #0xb]
    // 0xaed14c: r0 = InkWell()
    //     0xaed14c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xaed150: mov             x3, x0
    // 0xaed154: ldur            x0, [fp, #-8]
    // 0xaed158: stur            x3, [fp, #-0x10]
    // 0xaed15c: StoreField: r3->field_b = r0
    //     0xaed15c: stur            w0, [x3, #0xb]
    // 0xaed160: ldur            x2, [fp, #-0x20]
    // 0xaed164: r1 = Function '<anonymous closure>':.
    //     0xaed164: add             x1, PP, #0x58, lsl #12  ; [pp+0x581c8] AnonymousClosure: (0xaedd5c), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::cosmeticThemeSlider (0xaec9c0)
    //     0xaed168: ldr             x1, [x1, #0x1c8]
    // 0xaed16c: r0 = AllocateClosure()
    //     0xaed16c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaed170: mov             x1, x0
    // 0xaed174: ldur            x0, [fp, #-0x10]
    // 0xaed178: StoreField: r0->field_f = r1
    //     0xaed178: stur            w1, [x0, #0xf]
    // 0xaed17c: r1 = true
    //     0xaed17c: add             x1, NULL, #0x20  ; true
    // 0xaed180: StoreField: r0->field_43 = r1
    //     0xaed180: stur            w1, [x0, #0x43]
    // 0xaed184: r2 = Instance_BoxShape
    //     0xaed184: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaed188: ldr             x2, [x2, #0x80]
    // 0xaed18c: StoreField: r0->field_47 = r2
    //     0xaed18c: stur            w2, [x0, #0x47]
    // 0xaed190: StoreField: r0->field_6f = r1
    //     0xaed190: stur            w1, [x0, #0x6f]
    // 0xaed194: r2 = false
    //     0xaed194: add             x2, NULL, #0x30  ; false
    // 0xaed198: StoreField: r0->field_73 = r2
    //     0xaed198: stur            w2, [x0, #0x73]
    // 0xaed19c: StoreField: r0->field_83 = r1
    //     0xaed19c: stur            w1, [x0, #0x83]
    // 0xaed1a0: StoreField: r0->field_7b = r2
    //     0xaed1a0: stur            w2, [x0, #0x7b]
    // 0xaed1a4: LeaveFrame
    //     0xaed1a4: mov             SP, fp
    //     0xaed1a8: ldp             fp, lr, [SP], #0x10
    // 0xaed1ac: ret
    //     0xaed1ac: ret             
    // 0xaed1b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaed1b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaed1b4: b               #0xaec9e4
    // 0xaed1b8: r9 = _imagePageController
    //     0xaed1b8: add             x9, PP, #0x58, lsl #12  ; [pp+0x581d0] Field <_ProductGroupCarouselItemViewState@1467114462._imagePageController@1467114462>: late (offset: 0x18)
    //     0xaed1bc: ldr             x9, [x9, #0x1d0]
    // 0xaed1c0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaed1c0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaed1c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaed1c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaed1c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaed1c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaed1cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaed1cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaed1d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaed1d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildAddToBagSection(/* No info */) {
    // ** addr: 0xaed1d4, size: 0x3d8
    // 0xaed1d4: EnterFrame
    //     0xaed1d4: stp             fp, lr, [SP, #-0x10]!
    //     0xaed1d8: mov             fp, SP
    // 0xaed1dc: AllocStack(0x58)
    //     0xaed1dc: sub             SP, SP, #0x58
    // 0xaed1e0: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xaed1e0: stur            x1, [fp, #-8]
    //     0xaed1e4: stur            x2, [fp, #-0x10]
    //     0xaed1e8: stur            x3, [fp, #-0x18]
    // 0xaed1ec: CheckStackOverflow
    //     0xaed1ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaed1f0: cmp             SP, x16
    //     0xaed1f4: b.ls            #0xaed59c
    // 0xaed1f8: r1 = 3
    //     0xaed1f8: movz            x1, #0x3
    // 0xaed1fc: r0 = AllocateContext()
    //     0xaed1fc: bl              #0x16f6108  ; AllocateContextStub
    // 0xaed200: mov             x3, x0
    // 0xaed204: ldur            x2, [fp, #-8]
    // 0xaed208: stur            x3, [fp, #-0x20]
    // 0xaed20c: StoreField: r3->field_f = r2
    //     0xaed20c: stur            w2, [x3, #0xf]
    // 0xaed210: ldur            x0, [fp, #-0x10]
    // 0xaed214: StoreField: r3->field_13 = r0
    //     0xaed214: stur            w0, [x3, #0x13]
    // 0xaed218: ldur            x4, [fp, #-0x18]
    // 0xaed21c: r0 = BoxInt64Instr(r4)
    //     0xaed21c: sbfiz           x0, x4, #1, #0x1f
    //     0xaed220: cmp             x4, x0, asr #1
    //     0xaed224: b.eq            #0xaed230
    //     0xaed228: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaed22c: stur            x4, [x0, #7]
    // 0xaed230: ArrayStore: r3[0] = r0  ; List_4
    //     0xaed230: stur            w0, [x3, #0x17]
    // 0xaed234: LoadField: r0 = r2->field_b
    //     0xaed234: ldur            w0, [x2, #0xb]
    // 0xaed238: DecompressPointer r0
    //     0xaed238: add             x0, x0, HEAP, lsl #32
    // 0xaed23c: cmp             w0, NULL
    // 0xaed240: b.eq            #0xaed5a4
    // 0xaed244: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaed244: ldur            w1, [x0, #0x17]
    // 0xaed248: DecompressPointer r1
    //     0xaed248: add             x1, x1, HEAP, lsl #32
    // 0xaed24c: LoadField: r0 = r1->field_1f
    //     0xaed24c: ldur            w0, [x1, #0x1f]
    // 0xaed250: DecompressPointer r0
    //     0xaed250: add             x0, x0, HEAP, lsl #32
    // 0xaed254: cmp             w0, NULL
    // 0xaed258: b.ne            #0xaed264
    // 0xaed25c: r0 = Null
    //     0xaed25c: mov             x0, NULL
    // 0xaed260: b               #0xaed270
    // 0xaed264: LoadField: r4 = r0->field_7
    //     0xaed264: ldur            w4, [x0, #7]
    // 0xaed268: DecompressPointer r4
    //     0xaed268: add             x4, x4, HEAP, lsl #32
    // 0xaed26c: mov             x0, x4
    // 0xaed270: cmp             w0, NULL
    // 0xaed274: b.eq            #0xaed27c
    // 0xaed278: tbz             w0, #4, #0xaed28c
    // 0xaed27c: r0 = Instance_SizedBox
    //     0xaed27c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xaed280: LeaveFrame
    //     0xaed280: mov             SP, fp
    //     0xaed284: ldp             fp, lr, [SP], #0x10
    // 0xaed288: ret
    //     0xaed288: ret             
    // 0xaed28c: LoadField: r0 = r1->field_3f
    //     0xaed28c: ldur            w0, [x1, #0x3f]
    // 0xaed290: DecompressPointer r0
    //     0xaed290: add             x0, x0, HEAP, lsl #32
    // 0xaed294: cmp             w0, NULL
    // 0xaed298: b.ne            #0xaed2a4
    // 0xaed29c: r0 = Null
    //     0xaed29c: mov             x0, NULL
    // 0xaed2a0: b               #0xaed2b0
    // 0xaed2a4: LoadField: r1 = r0->field_23
    //     0xaed2a4: ldur            w1, [x0, #0x23]
    // 0xaed2a8: DecompressPointer r1
    //     0xaed2a8: add             x1, x1, HEAP, lsl #32
    // 0xaed2ac: mov             x0, x1
    // 0xaed2b0: cmp             w0, NULL
    // 0xaed2b4: b.eq            #0xaed550
    // 0xaed2b8: tbnz            w0, #4, #0xaed550
    // 0xaed2bc: r16 = <Size?>
    //     0xaed2bc: add             x16, PP, #0x27, lsl #12  ; [pp+0x27768] TypeArguments: <Size?>
    //     0xaed2c0: ldr             x16, [x16, #0x768]
    // 0xaed2c4: r30 = Instance_Size
    //     0xaed2c4: add             lr, PP, #0x55, lsl #12  ; [pp+0x55950] Obj!Size@d6c241
    //     0xaed2c8: ldr             lr, [lr, #0x950]
    // 0xaed2cc: stp             lr, x16, [SP]
    // 0xaed2d0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaed2d0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaed2d4: r0 = all()
    //     0xaed2d4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaed2d8: ldur            x2, [fp, #-0x20]
    // 0xaed2dc: stur            x0, [fp, #-0x10]
    // 0xaed2e0: LoadField: r1 = r2->field_13
    //     0xaed2e0: ldur            w1, [x2, #0x13]
    // 0xaed2e4: DecompressPointer r1
    //     0xaed2e4: add             x1, x1, HEAP, lsl #32
    // 0xaed2e8: LoadField: r3 = r1->field_4f
    //     0xaed2e8: ldur            w3, [x1, #0x4f]
    // 0xaed2ec: DecompressPointer r3
    //     0xaed2ec: add             x3, x3, HEAP, lsl #32
    // 0xaed2f0: cmp             w3, NULL
    // 0xaed2f4: b.eq            #0xaed2fc
    // 0xaed2f8: tbnz            w3, #4, #0xaed31c
    // 0xaed2fc: r16 = <Color>
    //     0xaed2fc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xaed300: ldr             x16, [x16, #0xf80]
    // 0xaed304: r30 = Instance_MaterialColor
    //     0xaed304: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xaed308: ldr             lr, [lr, #0xdc0]
    // 0xaed30c: stp             lr, x16, [SP]
    // 0xaed310: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaed310: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaed314: r0 = all()
    //     0xaed314: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaed318: b               #0xaed334
    // 0xaed31c: r16 = <Color>
    //     0xaed31c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xaed320: ldr             x16, [x16, #0xf80]
    // 0xaed324: r30 = Instance_Color
    //     0xaed324: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaed328: stp             lr, x16, [SP]
    // 0xaed32c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaed32c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaed330: r0 = all()
    //     0xaed330: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaed334: ldur            x2, [fp, #-0x20]
    // 0xaed338: stur            x0, [fp, #-0x28]
    // 0xaed33c: LoadField: r1 = r2->field_13
    //     0xaed33c: ldur            w1, [x2, #0x13]
    // 0xaed340: DecompressPointer r1
    //     0xaed340: add             x1, x1, HEAP, lsl #32
    // 0xaed344: LoadField: r3 = r1->field_4f
    //     0xaed344: ldur            w3, [x1, #0x4f]
    // 0xaed348: DecompressPointer r3
    //     0xaed348: add             x3, x3, HEAP, lsl #32
    // 0xaed34c: cmp             w3, NULL
    // 0xaed350: b.eq            #0xaed358
    // 0xaed354: tbnz            w3, #4, #0xaed380
    // 0xaed358: r1 = Instance_MaterialColor
    //     0xaed358: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xaed35c: ldr             x1, [x1, #0xdc0]
    // 0xaed360: d0 = 0.200000
    //     0xaed360: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xaed364: r0 = withOpacity()
    //     0xaed364: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaed368: r16 = <Color>
    //     0xaed368: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xaed36c: ldr             x16, [x16, #0xf80]
    // 0xaed370: stp             x0, x16, [SP]
    // 0xaed374: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaed374: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaed378: r0 = all()
    //     0xaed378: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaed37c: b               #0xaed398
    // 0xaed380: r16 = <Color>
    //     0xaed380: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xaed384: ldr             x16, [x16, #0xf80]
    // 0xaed388: r30 = Instance_Color
    //     0xaed388: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaed38c: stp             lr, x16, [SP]
    // 0xaed390: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaed390: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaed394: r0 = all()
    //     0xaed394: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaed398: ldur            x2, [fp, #-0x20]
    // 0xaed39c: stur            x0, [fp, #-0x38]
    // 0xaed3a0: LoadField: r1 = r2->field_13
    //     0xaed3a0: ldur            w1, [x2, #0x13]
    // 0xaed3a4: DecompressPointer r1
    //     0xaed3a4: add             x1, x1, HEAP, lsl #32
    // 0xaed3a8: LoadField: r3 = r1->field_4f
    //     0xaed3a8: ldur            w3, [x1, #0x4f]
    // 0xaed3ac: DecompressPointer r3
    //     0xaed3ac: add             x3, x3, HEAP, lsl #32
    // 0xaed3b0: cmp             w3, NULL
    // 0xaed3b4: b.eq            #0xaed3bc
    // 0xaed3b8: tbnz            w3, #4, #0xaed3c8
    // 0xaed3bc: r4 = Instance_BorderSide
    //     0xaed3bc: add             x4, PP, #0x36, lsl #12  ; [pp+0x36118] Obj!BorderSide@d62ef1
    //     0xaed3c0: ldr             x4, [x4, #0x118]
    // 0xaed3c4: b               #0xaed3d0
    // 0xaed3c8: r4 = Instance_BorderSide
    //     0xaed3c8: add             x4, PP, #0x55, lsl #12  ; [pp+0x55958] Obj!BorderSide@d62f31
    //     0xaed3cc: ldr             x4, [x4, #0x958]
    // 0xaed3d0: ldur            x3, [fp, #-0x10]
    // 0xaed3d4: ldur            x1, [fp, #-0x28]
    // 0xaed3d8: stur            x4, [fp, #-0x30]
    // 0xaed3dc: r0 = RoundedRectangleBorder()
    //     0xaed3dc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xaed3e0: mov             x1, x0
    // 0xaed3e4: r0 = Instance_BorderRadius
    //     0xaed3e4: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xaed3e8: ldr             x0, [x0, #0x460]
    // 0xaed3ec: StoreField: r1->field_b = r0
    //     0xaed3ec: stur            w0, [x1, #0xb]
    // 0xaed3f0: ldur            x0, [fp, #-0x30]
    // 0xaed3f4: StoreField: r1->field_7 = r0
    //     0xaed3f4: stur            w0, [x1, #7]
    // 0xaed3f8: r16 = <RoundedRectangleBorder>
    //     0xaed3f8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xaed3fc: ldr             x16, [x16, #0xf78]
    // 0xaed400: stp             x1, x16, [SP]
    // 0xaed404: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaed404: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaed408: r0 = all()
    //     0xaed408: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaed40c: stur            x0, [fp, #-0x30]
    // 0xaed410: r0 = ButtonStyle()
    //     0xaed410: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xaed414: mov             x1, x0
    // 0xaed418: ldur            x0, [fp, #-0x38]
    // 0xaed41c: stur            x1, [fp, #-0x40]
    // 0xaed420: StoreField: r1->field_b = r0
    //     0xaed420: stur            w0, [x1, #0xb]
    // 0xaed424: ldur            x0, [fp, #-0x28]
    // 0xaed428: StoreField: r1->field_f = r0
    //     0xaed428: stur            w0, [x1, #0xf]
    // 0xaed42c: ldur            x0, [fp, #-0x10]
    // 0xaed430: StoreField: r1->field_27 = r0
    //     0xaed430: stur            w0, [x1, #0x27]
    // 0xaed434: ldur            x0, [fp, #-0x30]
    // 0xaed438: StoreField: r1->field_43 = r0
    //     0xaed438: stur            w0, [x1, #0x43]
    // 0xaed43c: r0 = TextButtonThemeData()
    //     0xaed43c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xaed440: mov             x2, x0
    // 0xaed444: ldur            x0, [fp, #-0x40]
    // 0xaed448: stur            x2, [fp, #-0x10]
    // 0xaed44c: StoreField: r2->field_7 = r0
    //     0xaed44c: stur            w0, [x2, #7]
    // 0xaed450: ldur            x0, [fp, #-0x20]
    // 0xaed454: LoadField: r1 = r0->field_13
    //     0xaed454: ldur            w1, [x0, #0x13]
    // 0xaed458: DecompressPointer r1
    //     0xaed458: add             x1, x1, HEAP, lsl #32
    // 0xaed45c: LoadField: r3 = r1->field_f
    //     0xaed45c: ldur            w3, [x1, #0xf]
    // 0xaed460: DecompressPointer r3
    //     0xaed460: add             x3, x3, HEAP, lsl #32
    // 0xaed464: cmp             w3, NULL
    // 0xaed468: b.ne            #0xaed474
    // 0xaed46c: r1 = ""
    //     0xaed46c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaed470: b               #0xaed478
    // 0xaed474: mov             x1, x3
    // 0xaed478: ldur            x3, [fp, #-8]
    // 0xaed47c: r0 = capitalizeFirstWord()
    //     0xaed47c: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xaed480: mov             x2, x0
    // 0xaed484: ldur            x0, [fp, #-8]
    // 0xaed488: stur            x2, [fp, #-0x28]
    // 0xaed48c: LoadField: r1 = r0->field_f
    //     0xaed48c: ldur            w1, [x0, #0xf]
    // 0xaed490: DecompressPointer r1
    //     0xaed490: add             x1, x1, HEAP, lsl #32
    // 0xaed494: cmp             w1, NULL
    // 0xaed498: b.eq            #0xaed5a8
    // 0xaed49c: r0 = of()
    //     0xaed49c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaed4a0: LoadField: r1 = r0->field_87
    //     0xaed4a0: ldur            w1, [x0, #0x87]
    // 0xaed4a4: DecompressPointer r1
    //     0xaed4a4: add             x1, x1, HEAP, lsl #32
    // 0xaed4a8: LoadField: r0 = r1->field_7
    //     0xaed4a8: ldur            w0, [x1, #7]
    // 0xaed4ac: DecompressPointer r0
    //     0xaed4ac: add             x0, x0, HEAP, lsl #32
    // 0xaed4b0: r16 = 16.000000
    //     0xaed4b0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaed4b4: ldr             x16, [x16, #0x188]
    // 0xaed4b8: r30 = Instance_Color
    //     0xaed4b8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaed4bc: stp             lr, x16, [SP]
    // 0xaed4c0: mov             x1, x0
    // 0xaed4c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaed4c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaed4c8: ldr             x4, [x4, #0xaa0]
    // 0xaed4cc: r0 = copyWith()
    //     0xaed4cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaed4d0: stur            x0, [fp, #-8]
    // 0xaed4d4: r0 = Text()
    //     0xaed4d4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaed4d8: mov             x3, x0
    // 0xaed4dc: ldur            x0, [fp, #-0x28]
    // 0xaed4e0: stur            x3, [fp, #-0x30]
    // 0xaed4e4: StoreField: r3->field_b = r0
    //     0xaed4e4: stur            w0, [x3, #0xb]
    // 0xaed4e8: ldur            x0, [fp, #-8]
    // 0xaed4ec: StoreField: r3->field_13 = r0
    //     0xaed4ec: stur            w0, [x3, #0x13]
    // 0xaed4f0: ldur            x2, [fp, #-0x20]
    // 0xaed4f4: r1 = Function '<anonymous closure>':.
    //     0xaed4f4: add             x1, PP, #0x58, lsl #12  ; [pp+0x58218] AnonymousClosure: (0xaed5ac), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildAddToBagSection (0xaed1d4)
    //     0xaed4f8: ldr             x1, [x1, #0x218]
    // 0xaed4fc: r0 = AllocateClosure()
    //     0xaed4fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaed500: stur            x0, [fp, #-8]
    // 0xaed504: r0 = TextButton()
    //     0xaed504: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xaed508: mov             x1, x0
    // 0xaed50c: ldur            x0, [fp, #-8]
    // 0xaed510: stur            x1, [fp, #-0x20]
    // 0xaed514: StoreField: r1->field_b = r0
    //     0xaed514: stur            w0, [x1, #0xb]
    // 0xaed518: r0 = false
    //     0xaed518: add             x0, NULL, #0x30  ; false
    // 0xaed51c: StoreField: r1->field_27 = r0
    //     0xaed51c: stur            w0, [x1, #0x27]
    // 0xaed520: r0 = true
    //     0xaed520: add             x0, NULL, #0x20  ; true
    // 0xaed524: StoreField: r1->field_2f = r0
    //     0xaed524: stur            w0, [x1, #0x2f]
    // 0xaed528: ldur            x0, [fp, #-0x30]
    // 0xaed52c: StoreField: r1->field_37 = r0
    //     0xaed52c: stur            w0, [x1, #0x37]
    // 0xaed530: r0 = TextButtonTheme()
    //     0xaed530: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xaed534: mov             x1, x0
    // 0xaed538: ldur            x0, [fp, #-0x10]
    // 0xaed53c: StoreField: r1->field_f = r0
    //     0xaed53c: stur            w0, [x1, #0xf]
    // 0xaed540: ldur            x0, [fp, #-0x20]
    // 0xaed544: StoreField: r1->field_b = r0
    //     0xaed544: stur            w0, [x1, #0xb]
    // 0xaed548: mov             x0, x1
    // 0xaed54c: b               #0xaed554
    // 0xaed550: r0 = Instance_SizedBox
    //     0xaed550: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xaed554: stur            x0, [fp, #-8]
    // 0xaed558: r0 = Container()
    //     0xaed558: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaed55c: stur            x0, [fp, #-0x10]
    // 0xaed560: r16 = Instance_Alignment
    //     0xaed560: add             x16, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xaed564: ldr             x16, [x16, #0xcb0]
    // 0xaed568: r30 = Instance_EdgeInsets
    //     0xaed568: add             lr, PP, #0x55, lsl #12  ; [pp+0x55968] Obj!EdgeInsets@d58581
    //     0xaed56c: ldr             lr, [lr, #0x968]
    // 0xaed570: stp             lr, x16, [SP, #8]
    // 0xaed574: ldur            x16, [fp, #-8]
    // 0xaed578: str             x16, [SP]
    // 0xaed57c: mov             x1, x0
    // 0xaed580: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x1, child, 0x3, margin, 0x2, null]
    //     0xaed580: add             x4, PP, #0x55, lsl #12  ; [pp+0x55970] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x1, "child", 0x3, "margin", 0x2, Null]
    //     0xaed584: ldr             x4, [x4, #0x970]
    // 0xaed588: r0 = Container()
    //     0xaed588: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaed58c: ldur            x0, [fp, #-0x10]
    // 0xaed590: LeaveFrame
    //     0xaed590: mov             SP, fp
    //     0xaed594: ldp             fp, lr, [SP], #0x10
    // 0xaed598: ret
    //     0xaed598: ret             
    // 0xaed59c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaed59c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaed5a0: b               #0xaed1f8
    // 0xaed5a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaed5a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaed5a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaed5a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaed5ac, size: 0x68
    // 0xaed5ac: EnterFrame
    //     0xaed5ac: stp             fp, lr, [SP, #-0x10]!
    //     0xaed5b0: mov             fp, SP
    // 0xaed5b4: ldr             x0, [fp, #0x10]
    // 0xaed5b8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaed5b8: ldur            w1, [x0, #0x17]
    // 0xaed5bc: DecompressPointer r1
    //     0xaed5bc: add             x1, x1, HEAP, lsl #32
    // 0xaed5c0: CheckStackOverflow
    //     0xaed5c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaed5c4: cmp             SP, x16
    //     0xaed5c8: b.ls            #0xaed60c
    // 0xaed5cc: LoadField: r0 = r1->field_f
    //     0xaed5cc: ldur            w0, [x1, #0xf]
    // 0xaed5d0: DecompressPointer r0
    //     0xaed5d0: add             x0, x0, HEAP, lsl #32
    // 0xaed5d4: LoadField: r2 = r1->field_13
    //     0xaed5d4: ldur            w2, [x1, #0x13]
    // 0xaed5d8: DecompressPointer r2
    //     0xaed5d8: add             x2, x2, HEAP, lsl #32
    // 0xaed5dc: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xaed5dc: ldur            w3, [x1, #0x17]
    // 0xaed5e0: DecompressPointer r3
    //     0xaed5e0: add             x3, x3, HEAP, lsl #32
    // 0xaed5e4: r1 = LoadInt32Instr(r3)
    //     0xaed5e4: sbfx            x1, x3, #1, #0x1f
    //     0xaed5e8: tbz             w3, #0, #0xaed5f0
    //     0xaed5ec: ldur            x1, [x3, #7]
    // 0xaed5f0: mov             x3, x1
    // 0xaed5f4: mov             x1, x0
    // 0xaed5f8: r0 = _onAddToBagPressed()
    //     0xaed5f8: bl              #0xaed614  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_onAddToBagPressed
    // 0xaed5fc: r0 = Null
    //     0xaed5fc: mov             x0, NULL
    // 0xaed600: LeaveFrame
    //     0xaed600: mov             SP, fp
    //     0xaed604: ldp             fp, lr, [SP], #0x10
    // 0xaed608: ret
    //     0xaed608: ret             
    // 0xaed60c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaed60c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaed610: b               #0xaed5cc
  }
  _ _onAddToBagPressed(/* No info */) {
    // ** addr: 0xaed614, size: 0x17c
    // 0xaed614: EnterFrame
    //     0xaed614: stp             fp, lr, [SP, #-0x10]!
    //     0xaed618: mov             fp, SP
    // 0xaed61c: AllocStack(0x38)
    //     0xaed61c: sub             SP, SP, #0x38
    // 0xaed620: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xaed620: mov             x0, x1
    //     0xaed624: stur            x1, [fp, #-8]
    //     0xaed628: mov             x1, x3
    //     0xaed62c: stur            x2, [fp, #-0x10]
    //     0xaed630: stur            x3, [fp, #-0x18]
    // 0xaed634: CheckStackOverflow
    //     0xaed634: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaed638: cmp             SP, x16
    //     0xaed63c: b.ls            #0xaed77c
    // 0xaed640: LoadField: r3 = r2->field_4f
    //     0xaed640: ldur            w3, [x2, #0x4f]
    // 0xaed644: DecompressPointer r3
    //     0xaed644: add             x3, x3, HEAP, lsl #32
    // 0xaed648: cmp             w3, NULL
    // 0xaed64c: b.eq            #0xaed654
    // 0xaed650: tbz             w3, #4, #0xaed76c
    // 0xaed654: LoadField: r3 = r0->field_b
    //     0xaed654: ldur            w3, [x0, #0xb]
    // 0xaed658: DecompressPointer r3
    //     0xaed658: add             x3, x3, HEAP, lsl #32
    // 0xaed65c: cmp             w3, NULL
    // 0xaed660: b.eq            #0xaed784
    // 0xaed664: LoadField: r4 = r2->field_2b
    //     0xaed664: ldur            w4, [x2, #0x2b]
    // 0xaed668: DecompressPointer r4
    //     0xaed668: add             x4, x4, HEAP, lsl #32
    // 0xaed66c: LoadField: r5 = r2->field_3b
    //     0xaed66c: ldur            w5, [x2, #0x3b]
    // 0xaed670: DecompressPointer r5
    //     0xaed670: add             x5, x5, HEAP, lsl #32
    // 0xaed674: cmp             w5, NULL
    // 0xaed678: b.ne            #0xaed684
    // 0xaed67c: r6 = Null
    //     0xaed67c: mov             x6, NULL
    // 0xaed680: b               #0xaed68c
    // 0xaed684: LoadField: r6 = r5->field_7
    //     0xaed684: ldur            w6, [x5, #7]
    // 0xaed688: DecompressPointer r6
    //     0xaed688: add             x6, x6, HEAP, lsl #32
    // 0xaed68c: cmp             w5, NULL
    // 0xaed690: b.ne            #0xaed69c
    // 0xaed694: r5 = Null
    //     0xaed694: mov             x5, NULL
    // 0xaed698: b               #0xaed6a8
    // 0xaed69c: LoadField: r7 = r5->field_b
    //     0xaed69c: ldur            w7, [x5, #0xb]
    // 0xaed6a0: DecompressPointer r7
    //     0xaed6a0: add             x7, x7, HEAP, lsl #32
    // 0xaed6a4: mov             x5, x7
    // 0xaed6a8: LoadField: r7 = r3->field_37
    //     0xaed6a8: ldur            w7, [x3, #0x37]
    // 0xaed6ac: DecompressPointer r7
    //     0xaed6ac: add             x7, x7, HEAP, lsl #32
    // 0xaed6b0: stp             x4, x7, [SP, #0x10]
    // 0xaed6b4: stp             x5, x6, [SP]
    // 0xaed6b8: r4 = 0
    //     0xaed6b8: movz            x4, #0
    // 0xaed6bc: ldr             x0, [SP, #0x18]
    // 0xaed6c0: r16 = UnlinkedCall_0x613b5c
    //     0xaed6c0: add             x16, PP, #0x58, lsl #12  ; [pp+0x58220] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaed6c4: add             x16, x16, #0x220
    // 0xaed6c8: ldp             x5, lr, [x16]
    // 0xaed6cc: blr             lr
    // 0xaed6d0: ldur            x0, [fp, #-8]
    // 0xaed6d4: LoadField: r2 = r0->field_b
    //     0xaed6d4: ldur            w2, [x0, #0xb]
    // 0xaed6d8: DecompressPointer r2
    //     0xaed6d8: add             x2, x2, HEAP, lsl #32
    // 0xaed6dc: cmp             w2, NULL
    // 0xaed6e0: b.eq            #0xaed788
    // 0xaed6e4: LoadField: r3 = r2->field_1f
    //     0xaed6e4: ldur            w3, [x2, #0x1f]
    // 0xaed6e8: DecompressPointer r3
    //     0xaed6e8: add             x3, x3, HEAP, lsl #32
    // 0xaed6ec: LoadField: r4 = r2->field_b
    //     0xaed6ec: ldur            w4, [x2, #0xb]
    // 0xaed6f0: DecompressPointer r4
    //     0xaed6f0: add             x4, x4, HEAP, lsl #32
    // 0xaed6f4: cmp             w4, NULL
    // 0xaed6f8: b.ne            #0xaed704
    // 0xaed6fc: r0 = Null
    //     0xaed6fc: mov             x0, NULL
    // 0xaed700: b               #0xaed73c
    // 0xaed704: ldur            x5, [fp, #-0x18]
    // 0xaed708: LoadField: r0 = r4->field_b
    //     0xaed708: ldur            w0, [x4, #0xb]
    // 0xaed70c: r1 = LoadInt32Instr(r0)
    //     0xaed70c: sbfx            x1, x0, #1, #0x1f
    // 0xaed710: mov             x0, x1
    // 0xaed714: mov             x1, x5
    // 0xaed718: cmp             x1, x0
    // 0xaed71c: b.hs            #0xaed78c
    // 0xaed720: LoadField: r0 = r4->field_f
    //     0xaed720: ldur            w0, [x4, #0xf]
    // 0xaed724: DecompressPointer r0
    //     0xaed724: add             x0, x0, HEAP, lsl #32
    // 0xaed728: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xaed728: add             x16, x0, x5, lsl #2
    //     0xaed72c: ldur            w1, [x16, #0xf]
    // 0xaed730: DecompressPointer r1
    //     0xaed730: add             x1, x1, HEAP, lsl #32
    // 0xaed734: LoadField: r0 = r1->field_b3
    //     0xaed734: ldur            w0, [x1, #0xb3]
    // 0xaed738: DecompressPointer r0
    //     0xaed738: add             x0, x0, HEAP, lsl #32
    // 0xaed73c: cmp             w0, NULL
    // 0xaed740: b.ne            #0xaed748
    // 0xaed744: r0 = ""
    //     0xaed744: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaed748: LoadField: r1 = r2->field_47
    //     0xaed748: ldur            w1, [x2, #0x47]
    // 0xaed74c: DecompressPointer r1
    //     0xaed74c: add             x1, x1, HEAP, lsl #32
    // 0xaed750: ldur            x16, [fp, #-0x10]
    // 0xaed754: stp             x16, x1, [SP, #0x10]
    // 0xaed758: stp             x0, x3, [SP]
    // 0xaed75c: mov             x0, x1
    // 0xaed760: ClosureCall
    //     0xaed760: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xaed764: ldur            x2, [x0, #0x1f]
    //     0xaed768: blr             x2
    // 0xaed76c: r0 = Null
    //     0xaed76c: mov             x0, NULL
    // 0xaed770: LeaveFrame
    //     0xaed770: mov             SP, fp
    //     0xaed774: ldp             fp, lr, [SP], #0x10
    // 0xaed778: ret
    //     0xaed778: ret             
    // 0xaed77c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaed77c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaed780: b               #0xaed640
    // 0xaed784: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaed784: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaed788: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaed788: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaed78c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaed78c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildRatingWidget(/* No info */) {
    // ** addr: 0xaed790, size: 0x5cc
    // 0xaed790: EnterFrame
    //     0xaed790: stp             fp, lr, [SP, #-0x10]!
    //     0xaed794: mov             fp, SP
    // 0xaed798: AllocStack(0x50)
    //     0xaed798: sub             SP, SP, #0x50
    // 0xaed79c: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xaed79c: stur            x1, [fp, #-8]
    //     0xaed7a0: stur            x2, [fp, #-0x10]
    // 0xaed7a4: CheckStackOverflow
    //     0xaed7a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaed7a8: cmp             SP, x16
    //     0xaed7ac: b.ls            #0xaedd18
    // 0xaed7b0: LoadField: r0 = r2->field_7b
    //     0xaed7b0: ldur            w0, [x2, #0x7b]
    // 0xaed7b4: DecompressPointer r0
    //     0xaed7b4: add             x0, x0, HEAP, lsl #32
    // 0xaed7b8: cmp             w0, NULL
    // 0xaed7bc: b.ne            #0xaed7c8
    // 0xaed7c0: r0 = Null
    //     0xaed7c0: mov             x0, NULL
    // 0xaed7c4: b               #0xaed7d4
    // 0xaed7c8: LoadField: r3 = r0->field_7
    //     0xaed7c8: ldur            w3, [x0, #7]
    // 0xaed7cc: DecompressPointer r3
    //     0xaed7cc: add             x3, x3, HEAP, lsl #32
    // 0xaed7d0: mov             x0, x3
    // 0xaed7d4: r3 = LoadClassIdInstr(r0)
    //     0xaed7d4: ldur            x3, [x0, #-1]
    //     0xaed7d8: ubfx            x3, x3, #0xc, #0x14
    // 0xaed7dc: r16 = 0.000000
    //     0xaed7dc: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xaed7e0: stp             x16, x0, [SP]
    // 0xaed7e4: mov             x0, x3
    // 0xaed7e8: mov             lr, x0
    // 0xaed7ec: ldr             lr, [x21, lr, lsl #3]
    // 0xaed7f0: blr             lr
    // 0xaed7f4: tbnz            w0, #4, #0xaed808
    // 0xaed7f8: r0 = Instance_SizedBox
    //     0xaed7f8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xaed7fc: LeaveFrame
    //     0xaed7fc: mov             SP, fp
    //     0xaed800: ldp             fp, lr, [SP], #0x10
    // 0xaed804: ret
    //     0xaed804: ret             
    // 0xaed808: ldur            x1, [fp, #-0x10]
    // 0xaed80c: LoadField: r0 = r1->field_7b
    //     0xaed80c: ldur            w0, [x1, #0x7b]
    // 0xaed810: DecompressPointer r0
    //     0xaed810: add             x0, x0, HEAP, lsl #32
    // 0xaed814: cmp             w0, NULL
    // 0xaed818: b.ne            #0xaed824
    // 0xaed81c: r0 = Null
    //     0xaed81c: mov             x0, NULL
    // 0xaed820: b               #0xaed830
    // 0xaed824: LoadField: r2 = r0->field_f
    //     0xaed824: ldur            w2, [x0, #0xf]
    // 0xaed828: DecompressPointer r2
    //     0xaed828: add             x2, x2, HEAP, lsl #32
    // 0xaed82c: mov             x0, x2
    // 0xaed830: r2 = LoadClassIdInstr(r0)
    //     0xaed830: ldur            x2, [x0, #-1]
    //     0xaed834: ubfx            x2, x2, #0xc, #0x14
    // 0xaed838: r16 = "product_rating"
    //     0xaed838: add             x16, PP, #0x23, lsl #12  ; [pp+0x23f20] "product_rating"
    //     0xaed83c: ldr             x16, [x16, #0xf20]
    // 0xaed840: stp             x16, x0, [SP]
    // 0xaed844: mov             x0, x2
    // 0xaed848: mov             lr, x0
    // 0xaed84c: ldr             lr, [x21, lr, lsl #3]
    // 0xaed850: blr             lr
    // 0xaed854: stur            x0, [fp, #-0x18]
    // 0xaed858: tbnz            w0, #4, #0xaed8a8
    // 0xaed85c: ldur            x2, [fp, #-0x10]
    // 0xaed860: LoadField: r1 = r2->field_7b
    //     0xaed860: ldur            w1, [x2, #0x7b]
    // 0xaed864: DecompressPointer r1
    //     0xaed864: add             x1, x1, HEAP, lsl #32
    // 0xaed868: cmp             w1, NULL
    // 0xaed86c: b.ne            #0xaed878
    // 0xaed870: r1 = Null
    //     0xaed870: mov             x1, NULL
    // 0xaed874: b               #0xaed884
    // 0xaed878: LoadField: r3 = r1->field_7
    //     0xaed878: ldur            w3, [x1, #7]
    // 0xaed87c: DecompressPointer r3
    //     0xaed87c: add             x3, x3, HEAP, lsl #32
    // 0xaed880: mov             x1, x3
    // 0xaed884: cmp             w1, NULL
    // 0xaed888: b.ne            #0xaed894
    // 0xaed88c: d0 = 0.000000
    //     0xaed88c: eor             v0.16b, v0.16b, v0.16b
    // 0xaed890: b               #0xaed898
    // 0xaed894: LoadField: d0 = r1->field_7
    //     0xaed894: ldur            d0, [x1, #7]
    // 0xaed898: ldur            x1, [fp, #-8]
    // 0xaed89c: r0 = _getRatingColor()
    //     0xaed89c: bl              #0xa582f0  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_getRatingColor
    // 0xaed8a0: mov             x1, x0
    // 0xaed8a4: b               #0xaed8c8
    // 0xaed8a8: ldur            x0, [fp, #-8]
    // 0xaed8ac: LoadField: r1 = r0->field_f
    //     0xaed8ac: ldur            w1, [x0, #0xf]
    // 0xaed8b0: DecompressPointer r1
    //     0xaed8b0: add             x1, x1, HEAP, lsl #32
    // 0xaed8b4: cmp             w1, NULL
    // 0xaed8b8: b.eq            #0xaedd20
    // 0xaed8bc: r0 = of()
    //     0xaed8bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaed8c0: LoadField: r1 = r0->field_5b
    //     0xaed8c0: ldur            w1, [x0, #0x5b]
    // 0xaed8c4: DecompressPointer r1
    //     0xaed8c4: add             x1, x1, HEAP, lsl #32
    // 0xaed8c8: ldur            x0, [fp, #-0x10]
    // 0xaed8cc: stur            x1, [fp, #-0x20]
    // 0xaed8d0: r0 = ColorFilter()
    //     0xaed8d0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xaed8d4: mov             x1, x0
    // 0xaed8d8: ldur            x0, [fp, #-0x20]
    // 0xaed8dc: stur            x1, [fp, #-0x28]
    // 0xaed8e0: StoreField: r1->field_7 = r0
    //     0xaed8e0: stur            w0, [x1, #7]
    // 0xaed8e4: r0 = Instance_BlendMode
    //     0xaed8e4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xaed8e8: ldr             x0, [x0, #0xb30]
    // 0xaed8ec: StoreField: r1->field_b = r0
    //     0xaed8ec: stur            w0, [x1, #0xb]
    // 0xaed8f0: r0 = 1
    //     0xaed8f0: movz            x0, #0x1
    // 0xaed8f4: StoreField: r1->field_13 = r0
    //     0xaed8f4: stur            x0, [x1, #0x13]
    // 0xaed8f8: r0 = SvgPicture()
    //     0xaed8f8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xaed8fc: stur            x0, [fp, #-0x20]
    // 0xaed900: ldur            x16, [fp, #-0x28]
    // 0xaed904: str             x16, [SP]
    // 0xaed908: mov             x1, x0
    // 0xaed90c: r2 = "assets/images/green_star.svg"
    //     0xaed90c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xaed910: ldr             x2, [x2, #0x9a0]
    // 0xaed914: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xaed914: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xaed918: ldr             x4, [x4, #0xa38]
    // 0xaed91c: r0 = SvgPicture.asset()
    //     0xaed91c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xaed920: ldur            x0, [fp, #-0x10]
    // 0xaed924: LoadField: r1 = r0->field_7b
    //     0xaed924: ldur            w1, [x0, #0x7b]
    // 0xaed928: DecompressPointer r1
    //     0xaed928: add             x1, x1, HEAP, lsl #32
    // 0xaed92c: cmp             w1, NULL
    // 0xaed930: b.ne            #0xaed93c
    // 0xaed934: r0 = Null
    //     0xaed934: mov             x0, NULL
    // 0xaed938: b               #0xaed95c
    // 0xaed93c: LoadField: r2 = r1->field_7
    //     0xaed93c: ldur            w2, [x1, #7]
    // 0xaed940: DecompressPointer r2
    //     0xaed940: add             x2, x2, HEAP, lsl #32
    // 0xaed944: cmp             w2, NULL
    // 0xaed948: b.ne            #0xaed954
    // 0xaed94c: r0 = Null
    //     0xaed94c: mov             x0, NULL
    // 0xaed950: b               #0xaed95c
    // 0xaed954: str             x2, [SP]
    // 0xaed958: r0 = toString()
    //     0xaed958: bl              #0x1583704  ; [dart:core] _Double::toString
    // 0xaed95c: cmp             w0, NULL
    // 0xaed960: b.ne            #0xaed96c
    // 0xaed964: r4 = ""
    //     0xaed964: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaed968: b               #0xaed970
    // 0xaed96c: mov             x4, x0
    // 0xaed970: ldur            x2, [fp, #-8]
    // 0xaed974: ldur            x3, [fp, #-0x18]
    // 0xaed978: ldur            x0, [fp, #-0x20]
    // 0xaed97c: stur            x4, [fp, #-0x28]
    // 0xaed980: LoadField: r1 = r2->field_f
    //     0xaed980: ldur            w1, [x2, #0xf]
    // 0xaed984: DecompressPointer r1
    //     0xaed984: add             x1, x1, HEAP, lsl #32
    // 0xaed988: cmp             w1, NULL
    // 0xaed98c: b.eq            #0xaedd24
    // 0xaed990: r0 = of()
    //     0xaed990: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaed994: LoadField: r1 = r0->field_87
    //     0xaed994: ldur            w1, [x0, #0x87]
    // 0xaed998: DecompressPointer r1
    //     0xaed998: add             x1, x1, HEAP, lsl #32
    // 0xaed99c: LoadField: r0 = r1->field_7
    //     0xaed99c: ldur            w0, [x1, #7]
    // 0xaed9a0: DecompressPointer r0
    //     0xaed9a0: add             x0, x0, HEAP, lsl #32
    // 0xaed9a4: r16 = 12.000000
    //     0xaed9a4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaed9a8: ldr             x16, [x16, #0x9e8]
    // 0xaed9ac: r30 = Instance_Color
    //     0xaed9ac: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaed9b0: stp             lr, x16, [SP]
    // 0xaed9b4: mov             x1, x0
    // 0xaed9b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaed9b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaed9bc: ldr             x4, [x4, #0xaa0]
    // 0xaed9c0: r0 = copyWith()
    //     0xaed9c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaed9c4: stur            x0, [fp, #-0x30]
    // 0xaed9c8: r0 = Text()
    //     0xaed9c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaed9cc: mov             x3, x0
    // 0xaed9d0: ldur            x0, [fp, #-0x28]
    // 0xaed9d4: stur            x3, [fp, #-0x38]
    // 0xaed9d8: StoreField: r3->field_b = r0
    //     0xaed9d8: stur            w0, [x3, #0xb]
    // 0xaed9dc: ldur            x0, [fp, #-0x30]
    // 0xaed9e0: StoreField: r3->field_13 = r0
    //     0xaed9e0: stur            w0, [x3, #0x13]
    // 0xaed9e4: r1 = Null
    //     0xaed9e4: mov             x1, NULL
    // 0xaed9e8: r2 = 4
    //     0xaed9e8: movz            x2, #0x4
    // 0xaed9ec: r0 = AllocateArray()
    //     0xaed9ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaed9f0: mov             x2, x0
    // 0xaed9f4: ldur            x0, [fp, #-0x20]
    // 0xaed9f8: stur            x2, [fp, #-0x28]
    // 0xaed9fc: StoreField: r2->field_f = r0
    //     0xaed9fc: stur            w0, [x2, #0xf]
    // 0xaeda00: ldur            x0, [fp, #-0x38]
    // 0xaeda04: StoreField: r2->field_13 = r0
    //     0xaeda04: stur            w0, [x2, #0x13]
    // 0xaeda08: r1 = <Widget>
    //     0xaeda08: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaeda0c: r0 = AllocateGrowableArray()
    //     0xaeda0c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaeda10: mov             x3, x0
    // 0xaeda14: ldur            x0, [fp, #-0x28]
    // 0xaeda18: stur            x3, [fp, #-0x20]
    // 0xaeda1c: StoreField: r3->field_f = r0
    //     0xaeda1c: stur            w0, [x3, #0xf]
    // 0xaeda20: r0 = 4
    //     0xaeda20: movz            x0, #0x4
    // 0xaeda24: StoreField: r3->field_b = r0
    //     0xaeda24: stur            w0, [x3, #0xb]
    // 0xaeda28: ldur            x0, [fp, #-0x18]
    // 0xaeda2c: tbnz            w0, #4, #0xaedba8
    // 0xaeda30: ldur            x1, [fp, #-0x10]
    // 0xaeda34: LoadField: r2 = r1->field_7b
    //     0xaeda34: ldur            w2, [x1, #0x7b]
    // 0xaeda38: DecompressPointer r2
    //     0xaeda38: add             x2, x2, HEAP, lsl #32
    // 0xaeda3c: cmp             w2, NULL
    // 0xaeda40: b.ne            #0xaeda4c
    // 0xaeda44: ldur            x2, [fp, #-8]
    // 0xaeda48: b               #0xaedbac
    // 0xaeda4c: LoadField: r4 = r2->field_b
    //     0xaeda4c: ldur            w4, [x2, #0xb]
    // 0xaeda50: DecompressPointer r4
    //     0xaeda50: add             x4, x4, HEAP, lsl #32
    // 0xaeda54: stur            x4, [fp, #-0x10]
    // 0xaeda58: cmp             w4, NULL
    // 0xaeda5c: b.eq            #0xaedba0
    // 0xaeda60: ldur            x0, [fp, #-8]
    // 0xaeda64: r1 = Null
    //     0xaeda64: mov             x1, NULL
    // 0xaeda68: r2 = 6
    //     0xaeda68: movz            x2, #0x6
    // 0xaeda6c: r0 = AllocateArray()
    //     0xaeda6c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaeda70: r16 = " | ("
    //     0xaeda70: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d70] " | ("
    //     0xaeda74: ldr             x16, [x16, #0xd70]
    // 0xaeda78: StoreField: r0->field_f = r16
    //     0xaeda78: stur            w16, [x0, #0xf]
    // 0xaeda7c: ldur            x1, [fp, #-0x10]
    // 0xaeda80: LoadField: d0 = r1->field_7
    //     0xaeda80: ldur            d0, [x1, #7]
    // 0xaeda84: fcmp            d0, d0
    // 0xaeda88: b.vs            #0xaedd28
    // 0xaeda8c: fcvtzs          x1, d0
    // 0xaeda90: asr             x16, x1, #0x1e
    // 0xaeda94: cmp             x16, x1, asr #63
    // 0xaeda98: b.ne            #0xaedd28
    // 0xaeda9c: lsl             x1, x1, #1
    // 0xaedaa0: StoreField: r0->field_13 = r1
    //     0xaedaa0: stur            w1, [x0, #0x13]
    // 0xaedaa4: r16 = ")"
    //     0xaedaa4: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xaedaa8: ArrayStore: r0[0] = r16  ; List_4
    //     0xaedaa8: stur            w16, [x0, #0x17]
    // 0xaedaac: str             x0, [SP]
    // 0xaedab0: r0 = _interpolate()
    //     0xaedab0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xaedab4: ldur            x2, [fp, #-8]
    // 0xaedab8: stur            x0, [fp, #-0x10]
    // 0xaedabc: LoadField: r1 = r2->field_f
    //     0xaedabc: ldur            w1, [x2, #0xf]
    // 0xaedac0: DecompressPointer r1
    //     0xaedac0: add             x1, x1, HEAP, lsl #32
    // 0xaedac4: cmp             w1, NULL
    // 0xaedac8: b.eq            #0xaedd50
    // 0xaedacc: r0 = of()
    //     0xaedacc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaedad0: LoadField: r1 = r0->field_87
    //     0xaedad0: ldur            w1, [x0, #0x87]
    // 0xaedad4: DecompressPointer r1
    //     0xaedad4: add             x1, x1, HEAP, lsl #32
    // 0xaedad8: LoadField: r0 = r1->field_2b
    //     0xaedad8: ldur            w0, [x1, #0x2b]
    // 0xaedadc: DecompressPointer r0
    //     0xaedadc: add             x0, x0, HEAP, lsl #32
    // 0xaedae0: r16 = 12.000000
    //     0xaedae0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaedae4: ldr             x16, [x16, #0x9e8]
    // 0xaedae8: r30 = Instance_Color
    //     0xaedae8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaedaec: stp             lr, x16, [SP]
    // 0xaedaf0: mov             x1, x0
    // 0xaedaf4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaedaf4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaedaf8: ldr             x4, [x4, #0xaa0]
    // 0xaedafc: r0 = copyWith()
    //     0xaedafc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaedb00: stur            x0, [fp, #-0x28]
    // 0xaedb04: r0 = Text()
    //     0xaedb04: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaedb08: mov             x2, x0
    // 0xaedb0c: ldur            x0, [fp, #-0x10]
    // 0xaedb10: stur            x2, [fp, #-0x30]
    // 0xaedb14: StoreField: r2->field_b = r0
    //     0xaedb14: stur            w0, [x2, #0xb]
    // 0xaedb18: ldur            x0, [fp, #-0x28]
    // 0xaedb1c: StoreField: r2->field_13 = r0
    //     0xaedb1c: stur            w0, [x2, #0x13]
    // 0xaedb20: ldur            x0, [fp, #-0x20]
    // 0xaedb24: LoadField: r1 = r0->field_b
    //     0xaedb24: ldur            w1, [x0, #0xb]
    // 0xaedb28: LoadField: r3 = r0->field_f
    //     0xaedb28: ldur            w3, [x0, #0xf]
    // 0xaedb2c: DecompressPointer r3
    //     0xaedb2c: add             x3, x3, HEAP, lsl #32
    // 0xaedb30: LoadField: r4 = r3->field_b
    //     0xaedb30: ldur            w4, [x3, #0xb]
    // 0xaedb34: r3 = LoadInt32Instr(r1)
    //     0xaedb34: sbfx            x3, x1, #1, #0x1f
    // 0xaedb38: stur            x3, [fp, #-0x40]
    // 0xaedb3c: r1 = LoadInt32Instr(r4)
    //     0xaedb3c: sbfx            x1, x4, #1, #0x1f
    // 0xaedb40: cmp             x3, x1
    // 0xaedb44: b.ne            #0xaedb50
    // 0xaedb48: mov             x1, x0
    // 0xaedb4c: r0 = _growToNextCapacity()
    //     0xaedb4c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaedb50: ldur            x3, [fp, #-0x20]
    // 0xaedb54: ldur            x2, [fp, #-0x40]
    // 0xaedb58: add             x0, x2, #1
    // 0xaedb5c: lsl             x1, x0, #1
    // 0xaedb60: StoreField: r3->field_b = r1
    //     0xaedb60: stur            w1, [x3, #0xb]
    // 0xaedb64: LoadField: r1 = r3->field_f
    //     0xaedb64: ldur            w1, [x3, #0xf]
    // 0xaedb68: DecompressPointer r1
    //     0xaedb68: add             x1, x1, HEAP, lsl #32
    // 0xaedb6c: ldur            x0, [fp, #-0x30]
    // 0xaedb70: ArrayStore: r1[r2] = r0  ; List_4
    //     0xaedb70: add             x25, x1, x2, lsl #2
    //     0xaedb74: add             x25, x25, #0xf
    //     0xaedb78: str             w0, [x25]
    //     0xaedb7c: tbz             w0, #0, #0xaedb98
    //     0xaedb80: ldurb           w16, [x1, #-1]
    //     0xaedb84: ldurb           w17, [x0, #-1]
    //     0xaedb88: and             x16, x17, x16, lsr #2
    //     0xaedb8c: tst             x16, HEAP, lsr #32
    //     0xaedb90: b.eq            #0xaedb98
    //     0xaedb94: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaedb98: mov             x2, x3
    // 0xaedb9c: b               #0xaedcb8
    // 0xaedba0: ldur            x2, [fp, #-8]
    // 0xaedba4: b               #0xaedbac
    // 0xaedba8: ldur            x2, [fp, #-8]
    // 0xaedbac: tbz             w0, #4, #0xaedcb4
    // 0xaedbb0: LoadField: r1 = r2->field_f
    //     0xaedbb0: ldur            w1, [x2, #0xf]
    // 0xaedbb4: DecompressPointer r1
    //     0xaedbb4: add             x1, x1, HEAP, lsl #32
    // 0xaedbb8: cmp             w1, NULL
    // 0xaedbbc: b.eq            #0xaedd54
    // 0xaedbc0: r0 = of()
    //     0xaedbc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaedbc4: LoadField: r1 = r0->field_87
    //     0xaedbc4: ldur            w1, [x0, #0x87]
    // 0xaedbc8: DecompressPointer r1
    //     0xaedbc8: add             x1, x1, HEAP, lsl #32
    // 0xaedbcc: LoadField: r0 = r1->field_2b
    //     0xaedbcc: ldur            w0, [x1, #0x2b]
    // 0xaedbd0: DecompressPointer r0
    //     0xaedbd0: add             x0, x0, HEAP, lsl #32
    // 0xaedbd4: ldur            x1, [fp, #-8]
    // 0xaedbd8: stur            x0, [fp, #-0x10]
    // 0xaedbdc: LoadField: r2 = r1->field_f
    //     0xaedbdc: ldur            w2, [x1, #0xf]
    // 0xaedbe0: DecompressPointer r2
    //     0xaedbe0: add             x2, x2, HEAP, lsl #32
    // 0xaedbe4: cmp             w2, NULL
    // 0xaedbe8: b.eq            #0xaedd58
    // 0xaedbec: mov             x1, x2
    // 0xaedbf0: r0 = of()
    //     0xaedbf0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaedbf4: LoadField: r1 = r0->field_5b
    //     0xaedbf4: ldur            w1, [x0, #0x5b]
    // 0xaedbf8: DecompressPointer r1
    //     0xaedbf8: add             x1, x1, HEAP, lsl #32
    // 0xaedbfc: r16 = 10.000000
    //     0xaedbfc: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xaedc00: stp             x1, x16, [SP]
    // 0xaedc04: ldur            x1, [fp, #-0x10]
    // 0xaedc08: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaedc08: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaedc0c: ldr             x4, [x4, #0xaa0]
    // 0xaedc10: r0 = copyWith()
    //     0xaedc10: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaedc14: stur            x0, [fp, #-8]
    // 0xaedc18: r0 = Text()
    //     0xaedc18: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaedc1c: mov             x2, x0
    // 0xaedc20: r0 = " Brand Rating"
    //     0xaedc20: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d78] " Brand Rating"
    //     0xaedc24: ldr             x0, [x0, #0xd78]
    // 0xaedc28: stur            x2, [fp, #-0x10]
    // 0xaedc2c: StoreField: r2->field_b = r0
    //     0xaedc2c: stur            w0, [x2, #0xb]
    // 0xaedc30: ldur            x0, [fp, #-8]
    // 0xaedc34: StoreField: r2->field_13 = r0
    //     0xaedc34: stur            w0, [x2, #0x13]
    // 0xaedc38: ldur            x0, [fp, #-0x20]
    // 0xaedc3c: LoadField: r1 = r0->field_b
    //     0xaedc3c: ldur            w1, [x0, #0xb]
    // 0xaedc40: LoadField: r3 = r0->field_f
    //     0xaedc40: ldur            w3, [x0, #0xf]
    // 0xaedc44: DecompressPointer r3
    //     0xaedc44: add             x3, x3, HEAP, lsl #32
    // 0xaedc48: LoadField: r4 = r3->field_b
    //     0xaedc48: ldur            w4, [x3, #0xb]
    // 0xaedc4c: r3 = LoadInt32Instr(r1)
    //     0xaedc4c: sbfx            x3, x1, #1, #0x1f
    // 0xaedc50: stur            x3, [fp, #-0x40]
    // 0xaedc54: r1 = LoadInt32Instr(r4)
    //     0xaedc54: sbfx            x1, x4, #1, #0x1f
    // 0xaedc58: cmp             x3, x1
    // 0xaedc5c: b.ne            #0xaedc68
    // 0xaedc60: mov             x1, x0
    // 0xaedc64: r0 = _growToNextCapacity()
    //     0xaedc64: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaedc68: ldur            x2, [fp, #-0x20]
    // 0xaedc6c: ldur            x3, [fp, #-0x40]
    // 0xaedc70: add             x0, x3, #1
    // 0xaedc74: lsl             x1, x0, #1
    // 0xaedc78: StoreField: r2->field_b = r1
    //     0xaedc78: stur            w1, [x2, #0xb]
    // 0xaedc7c: LoadField: r1 = r2->field_f
    //     0xaedc7c: ldur            w1, [x2, #0xf]
    // 0xaedc80: DecompressPointer r1
    //     0xaedc80: add             x1, x1, HEAP, lsl #32
    // 0xaedc84: ldur            x0, [fp, #-0x10]
    // 0xaedc88: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaedc88: add             x25, x1, x3, lsl #2
    //     0xaedc8c: add             x25, x25, #0xf
    //     0xaedc90: str             w0, [x25]
    //     0xaedc94: tbz             w0, #0, #0xaedcb0
    //     0xaedc98: ldurb           w16, [x1, #-1]
    //     0xaedc9c: ldurb           w17, [x0, #-1]
    //     0xaedca0: and             x16, x17, x16, lsr #2
    //     0xaedca4: tst             x16, HEAP, lsr #32
    //     0xaedca8: b.eq            #0xaedcb0
    //     0xaedcac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaedcb0: b               #0xaedcb8
    // 0xaedcb4: mov             x2, x3
    // 0xaedcb8: r0 = Row()
    //     0xaedcb8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaedcbc: r1 = Instance_Axis
    //     0xaedcbc: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaedcc0: StoreField: r0->field_f = r1
    //     0xaedcc0: stur            w1, [x0, #0xf]
    // 0xaedcc4: r1 = Instance_MainAxisAlignment
    //     0xaedcc4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaedcc8: ldr             x1, [x1, #0xa08]
    // 0xaedccc: StoreField: r0->field_13 = r1
    //     0xaedccc: stur            w1, [x0, #0x13]
    // 0xaedcd0: r1 = Instance_MainAxisSize
    //     0xaedcd0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaedcd4: ldr             x1, [x1, #0xa10]
    // 0xaedcd8: ArrayStore: r0[0] = r1  ; List_4
    //     0xaedcd8: stur            w1, [x0, #0x17]
    // 0xaedcdc: r1 = Instance_CrossAxisAlignment
    //     0xaedcdc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaedce0: ldr             x1, [x1, #0xa18]
    // 0xaedce4: StoreField: r0->field_1b = r1
    //     0xaedce4: stur            w1, [x0, #0x1b]
    // 0xaedce8: r1 = Instance_VerticalDirection
    //     0xaedce8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaedcec: ldr             x1, [x1, #0xa20]
    // 0xaedcf0: StoreField: r0->field_23 = r1
    //     0xaedcf0: stur            w1, [x0, #0x23]
    // 0xaedcf4: r1 = Instance_Clip
    //     0xaedcf4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaedcf8: ldr             x1, [x1, #0x38]
    // 0xaedcfc: StoreField: r0->field_2b = r1
    //     0xaedcfc: stur            w1, [x0, #0x2b]
    // 0xaedd00: StoreField: r0->field_2f = rZR
    //     0xaedd00: stur            xzr, [x0, #0x2f]
    // 0xaedd04: ldur            x1, [fp, #-0x20]
    // 0xaedd08: StoreField: r0->field_b = r1
    //     0xaedd08: stur            w1, [x0, #0xb]
    // 0xaedd0c: LeaveFrame
    //     0xaedd0c: mov             SP, fp
    //     0xaedd10: ldp             fp, lr, [SP], #0x10
    // 0xaedd14: ret
    //     0xaedd14: ret             
    // 0xaedd18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaedd18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaedd1c: b               #0xaed7b0
    // 0xaedd20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaedd20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaedd24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaedd24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaedd28: SaveReg d0
    //     0xaedd28: str             q0, [SP, #-0x10]!
    // 0xaedd2c: SaveReg r0
    //     0xaedd2c: str             x0, [SP, #-8]!
    // 0xaedd30: r0 = 74
    //     0xaedd30: movz            x0, #0x4a
    // 0xaedd34: r30 = DoubleToIntegerStub
    //     0xaedd34: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xaedd38: LoadField: r30 = r30->field_7
    //     0xaedd38: ldur            lr, [lr, #7]
    // 0xaedd3c: blr             lr
    // 0xaedd40: mov             x1, x0
    // 0xaedd44: RestoreReg r0
    //     0xaedd44: ldr             x0, [SP], #8
    // 0xaedd48: RestoreReg d0
    //     0xaedd48: ldr             q0, [SP], #0x10
    // 0xaedd4c: b               #0xaedaa0
    // 0xaedd50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaedd50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaedd54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaedd54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaedd58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaedd58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaedd5c, size: 0x50
    // 0xaedd5c: EnterFrame
    //     0xaedd5c: stp             fp, lr, [SP, #-0x10]!
    //     0xaedd60: mov             fp, SP
    // 0xaedd64: ldr             x0, [fp, #0x10]
    // 0xaedd68: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaedd68: ldur            w1, [x0, #0x17]
    // 0xaedd6c: DecompressPointer r1
    //     0xaedd6c: add             x1, x1, HEAP, lsl #32
    // 0xaedd70: CheckStackOverflow
    //     0xaedd70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaedd74: cmp             SP, x16
    //     0xaedd78: b.ls            #0xaedda4
    // 0xaedd7c: LoadField: r0 = r1->field_f
    //     0xaedd7c: ldur            w0, [x1, #0xf]
    // 0xaedd80: DecompressPointer r0
    //     0xaedd80: add             x0, x0, HEAP, lsl #32
    // 0xaedd84: LoadField: r2 = r1->field_13
    //     0xaedd84: ldur            w2, [x1, #0x13]
    // 0xaedd88: DecompressPointer r2
    //     0xaedd88: add             x2, x2, HEAP, lsl #32
    // 0xaedd8c: mov             x1, x0
    // 0xaedd90: r0 = _onProductCardTap()
    //     0xaedd90: bl              #0xaeddac  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_onProductCardTap
    // 0xaedd94: r0 = Null
    //     0xaedd94: mov             x0, NULL
    // 0xaedd98: LeaveFrame
    //     0xaedd98: mov             SP, fp
    //     0xaedd9c: ldp             fp, lr, [SP], #0x10
    // 0xaedda0: ret
    //     0xaedda0: ret             
    // 0xaedda4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaedda4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaedda8: b               #0xaedd7c
  }
  _ _onProductCardTap(/* No info */) {
    // ** addr: 0xaeddac, size: 0x114
    // 0xaeddac: EnterFrame
    //     0xaeddac: stp             fp, lr, [SP, #-0x10]!
    //     0xaeddb0: mov             fp, SP
    // 0xaeddb4: AllocStack(0x48)
    //     0xaeddb4: sub             SP, SP, #0x48
    // 0xaeddb8: CheckStackOverflow
    //     0xaeddb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeddbc: cmp             SP, x16
    //     0xaeddc0: b.ls            #0xaedeb4
    // 0xaeddc4: LoadField: r0 = r1->field_b
    //     0xaeddc4: ldur            w0, [x1, #0xb]
    // 0xaeddc8: DecompressPointer r0
    //     0xaeddc8: add             x0, x0, HEAP, lsl #32
    // 0xaeddcc: cmp             w0, NULL
    // 0xaeddd0: b.eq            #0xaedebc
    // 0xaeddd4: LoadField: r1 = r0->field_27
    //     0xaeddd4: ldur            w1, [x0, #0x27]
    // 0xaeddd8: DecompressPointer r1
    //     0xaeddd8: add             x1, x1, HEAP, lsl #32
    // 0xaedddc: cmp             w1, NULL
    // 0xaedde0: b.ne            #0xaedde8
    // 0xaedde4: r1 = ""
    //     0xaedde4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaedde8: LoadField: r3 = r0->field_23
    //     0xaedde8: ldur            w3, [x0, #0x23]
    // 0xaeddec: DecompressPointer r3
    //     0xaeddec: add             x3, x3, HEAP, lsl #32
    // 0xaeddf0: cmp             w3, NULL
    // 0xaeddf4: b.ne            #0xaeddfc
    // 0xaeddf8: r3 = ""
    //     0xaeddf8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaeddfc: LoadField: r4 = r0->field_2f
    //     0xaeddfc: ldur            w4, [x0, #0x2f]
    // 0xaede00: DecompressPointer r4
    //     0xaede00: add             x4, x4, HEAP, lsl #32
    // 0xaede04: LoadField: r5 = r0->field_1f
    //     0xaede04: ldur            w5, [x0, #0x1f]
    // 0xaede08: DecompressPointer r5
    //     0xaede08: add             x5, x5, HEAP, lsl #32
    // 0xaede0c: LoadField: r6 = r0->field_2b
    //     0xaede0c: ldur            w6, [x0, #0x2b]
    // 0xaede10: DecompressPointer r6
    //     0xaede10: add             x6, x6, HEAP, lsl #32
    // 0xaede14: cmp             w6, NULL
    // 0xaede18: b.ne            #0xaede20
    // 0xaede1c: r6 = ""
    //     0xaede1c: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaede20: LoadField: r7 = r0->field_f
    //     0xaede20: ldur            w7, [x0, #0xf]
    // 0xaede24: DecompressPointer r7
    //     0xaede24: add             x7, x7, HEAP, lsl #32
    // 0xaede28: cmp             w7, NULL
    // 0xaede2c: b.ne            #0xaede34
    // 0xaede30: r7 = ""
    //     0xaede30: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaede34: LoadField: r8 = r2->field_2b
    //     0xaede34: ldur            w8, [x2, #0x2b]
    // 0xaede38: DecompressPointer r8
    //     0xaede38: add             x8, x8, HEAP, lsl #32
    // 0xaede3c: cmp             w8, NULL
    // 0xaede40: b.ne            #0xaede48
    // 0xaede44: r8 = ""
    //     0xaede44: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaede48: LoadField: r9 = r2->field_3b
    //     0xaede48: ldur            w9, [x2, #0x3b]
    // 0xaede4c: DecompressPointer r9
    //     0xaede4c: add             x9, x9, HEAP, lsl #32
    // 0xaede50: cmp             w9, NULL
    // 0xaede54: b.ne            #0xaede60
    // 0xaede58: r2 = Null
    //     0xaede58: mov             x2, NULL
    // 0xaede5c: b               #0xaede68
    // 0xaede60: LoadField: r2 = r9->field_b
    //     0xaede60: ldur            w2, [x9, #0xb]
    // 0xaede64: DecompressPointer r2
    //     0xaede64: add             x2, x2, HEAP, lsl #32
    // 0xaede68: cmp             w2, NULL
    // 0xaede6c: b.ne            #0xaede74
    // 0xaede70: r2 = ""
    //     0xaede70: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaede74: LoadField: r9 = r0->field_43
    //     0xaede74: ldur            w9, [x0, #0x43]
    // 0xaede78: DecompressPointer r9
    //     0xaede78: add             x9, x9, HEAP, lsl #32
    // 0xaede7c: stp             x1, x9, [SP, #0x38]
    // 0xaede80: stp             x4, x3, [SP, #0x28]
    // 0xaede84: stp             x6, x5, [SP, #0x18]
    // 0xaede88: stp             x8, x7, [SP, #8]
    // 0xaede8c: str             x2, [SP]
    // 0xaede90: r4 = 0
    //     0xaede90: movz            x4, #0
    // 0xaede94: ldr             x0, [SP, #0x40]
    // 0xaede98: r5 = UnlinkedCall_0x613b5c
    //     0xaede98: add             x16, PP, #0x58, lsl #12  ; [pp+0x581d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaede9c: ldp             x5, lr, [x16, #0x1d8]
    // 0xaedea0: blr             lr
    // 0xaedea4: r0 = Null
    //     0xaedea4: mov             x0, NULL
    // 0xaedea8: LeaveFrame
    //     0xaedea8: mov             SP, fp
    //     0xaedeac: ldp             fp, lr, [SP], #0x10
    // 0xaedeb0: ret
    //     0xaedeb0: ret             
    // 0xaedeb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaedeb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaedeb8: b               #0xaeddc4
    // 0xaedebc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaedebc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaedec0, size: 0x64
    // 0xaedec0: EnterFrame
    //     0xaedec0: stp             fp, lr, [SP, #-0x10]!
    //     0xaedec4: mov             fp, SP
    // 0xaedec8: ldr             x0, [fp, #0x20]
    // 0xaedecc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaedecc: ldur            w1, [x0, #0x17]
    // 0xaeded0: DecompressPointer r1
    //     0xaeded0: add             x1, x1, HEAP, lsl #32
    // 0xaeded4: CheckStackOverflow
    //     0xaeded4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeded8: cmp             SP, x16
    //     0xaededc: b.ls            #0xaedf1c
    // 0xaedee0: LoadField: r0 = r1->field_f
    //     0xaedee0: ldur            w0, [x1, #0xf]
    // 0xaedee4: DecompressPointer r0
    //     0xaedee4: add             x0, x0, HEAP, lsl #32
    // 0xaedee8: LoadField: r3 = r1->field_13
    //     0xaedee8: ldur            w3, [x1, #0x13]
    // 0xaedeec: DecompressPointer r3
    //     0xaedeec: add             x3, x3, HEAP, lsl #32
    // 0xaedef0: LoadField: r2 = r3->field_37
    //     0xaedef0: ldur            w2, [x3, #0x37]
    // 0xaedef4: DecompressPointer r2
    //     0xaedef4: add             x2, x2, HEAP, lsl #32
    // 0xaedef8: ldr             x1, [fp, #0x10]
    // 0xaedefc: r5 = LoadInt32Instr(r1)
    //     0xaedefc: sbfx            x5, x1, #1, #0x1f
    //     0xaedf00: tbz             w1, #0, #0xaedf08
    //     0xaedf04: ldur            x5, [x1, #7]
    // 0xaedf08: mov             x1, x0
    // 0xaedf0c: r0 = cosmeticThemeImageSlider()
    //     0xaedf0c: bl              #0xaedf24  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::cosmeticThemeImageSlider
    // 0xaedf10: LeaveFrame
    //     0xaedf10: mov             SP, fp
    //     0xaedf14: ldp             fp, lr, [SP], #0x10
    // 0xaedf18: ret
    //     0xaedf18: ret             
    // 0xaedf1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaedf1c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaedf20: b               #0xaedee0
  }
  _ cosmeticThemeImageSlider(/* No info */) {
    // ** addr: 0xaedf24, size: 0x50c
    // 0xaedf24: EnterFrame
    //     0xaedf24: stp             fp, lr, [SP, #-0x10]!
    //     0xaedf28: mov             fp, SP
    // 0xaedf2c: AllocStack(0x60)
    //     0xaedf2c: sub             SP, SP, #0x60
    // 0xaedf30: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */)
    //     0xaedf30: mov             x0, x2
    //     0xaedf34: stur            x2, [fp, #-0x10]
    //     0xaedf38: mov             x2, x3
    //     0xaedf3c: stur            x3, [fp, #-0x18]
    //     0xaedf40: mov             x3, x1
    //     0xaedf44: stur            x1, [fp, #-8]
    //     0xaedf48: mov             x1, x5
    //     0xaedf4c: stur            x5, [fp, #-0x20]
    // 0xaedf50: CheckStackOverflow
    //     0xaedf50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaedf54: cmp             SP, x16
    //     0xaedf58: b.ls            #0xaee424
    // 0xaedf5c: r1 = 2
    //     0xaedf5c: movz            x1, #0x2
    // 0xaedf60: r0 = AllocateContext()
    //     0xaedf60: bl              #0x16f6108  ; AllocateContextStub
    // 0xaedf64: ldur            x1, [fp, #-8]
    // 0xaedf68: stur            x0, [fp, #-0x28]
    // 0xaedf6c: StoreField: r0->field_f = r1
    //     0xaedf6c: stur            w1, [x0, #0xf]
    // 0xaedf70: ldur            x2, [fp, #-0x18]
    // 0xaedf74: StoreField: r0->field_13 = r2
    //     0xaedf74: stur            w2, [x0, #0x13]
    // 0xaedf78: r0 = Radius()
    //     0xaedf78: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaedf7c: d0 = 10.000000
    //     0xaedf7c: fmov            d0, #10.00000000
    // 0xaedf80: stur            x0, [fp, #-0x30]
    // 0xaedf84: StoreField: r0->field_7 = d0
    //     0xaedf84: stur            d0, [x0, #7]
    // 0xaedf88: StoreField: r0->field_f = d0
    //     0xaedf88: stur            d0, [x0, #0xf]
    // 0xaedf8c: r0 = BorderRadius()
    //     0xaedf8c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaedf90: mov             x1, x0
    // 0xaedf94: ldur            x0, [fp, #-0x30]
    // 0xaedf98: stur            x1, [fp, #-0x38]
    // 0xaedf9c: StoreField: r1->field_7 = r0
    //     0xaedf9c: stur            w0, [x1, #7]
    // 0xaedfa0: StoreField: r1->field_b = r0
    //     0xaedfa0: stur            w0, [x1, #0xb]
    // 0xaedfa4: StoreField: r1->field_f = r0
    //     0xaedfa4: stur            w0, [x1, #0xf]
    // 0xaedfa8: StoreField: r1->field_13 = r0
    //     0xaedfa8: stur            w0, [x1, #0x13]
    // 0xaedfac: r0 = RoundedRectangleBorder()
    //     0xaedfac: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xaedfb0: mov             x3, x0
    // 0xaedfb4: ldur            x0, [fp, #-0x38]
    // 0xaedfb8: stur            x3, [fp, #-0x30]
    // 0xaedfbc: StoreField: r3->field_b = r0
    //     0xaedfbc: stur            w0, [x3, #0xb]
    // 0xaedfc0: r0 = Instance_BorderSide
    //     0xaedfc0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xaedfc4: ldr             x0, [x0, #0xe20]
    // 0xaedfc8: StoreField: r3->field_7 = r0
    //     0xaedfc8: stur            w0, [x3, #7]
    // 0xaedfcc: ldur            x2, [fp, #-0x10]
    // 0xaedfd0: cmp             w2, NULL
    // 0xaedfd4: b.ne            #0xaedfe0
    // 0xaedfd8: r0 = Null
    //     0xaedfd8: mov             x0, NULL
    // 0xaedfdc: b               #0xaee018
    // 0xaedfe0: ldur            x4, [fp, #-0x20]
    // 0xaedfe4: LoadField: r0 = r2->field_b
    //     0xaedfe4: ldur            w0, [x2, #0xb]
    // 0xaedfe8: r1 = LoadInt32Instr(r0)
    //     0xaedfe8: sbfx            x1, x0, #1, #0x1f
    // 0xaedfec: mov             x0, x1
    // 0xaedff0: mov             x1, x4
    // 0xaedff4: cmp             x1, x0
    // 0xaedff8: b.hs            #0xaee42c
    // 0xaedffc: LoadField: r0 = r2->field_f
    //     0xaedffc: ldur            w0, [x2, #0xf]
    // 0xaee000: DecompressPointer r0
    //     0xaee000: add             x0, x0, HEAP, lsl #32
    // 0xaee004: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xaee004: add             x16, x0, x4, lsl #2
    //     0xaee008: ldur            w1, [x16, #0xf]
    // 0xaee00c: DecompressPointer r1
    //     0xaee00c: add             x1, x1, HEAP, lsl #32
    // 0xaee010: LoadField: r0 = r1->field_b
    //     0xaee010: ldur            w0, [x1, #0xb]
    // 0xaee014: DecompressPointer r0
    //     0xaee014: add             x0, x0, HEAP, lsl #32
    // 0xaee018: cmp             w0, NULL
    // 0xaee01c: b.ne            #0xaee028
    // 0xaee020: r4 = ""
    //     0xaee020: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaee024: b               #0xaee02c
    // 0xaee028: mov             x4, x0
    // 0xaee02c: ldur            x0, [fp, #-0x18]
    // 0xaee030: stur            x4, [fp, #-0x10]
    // 0xaee034: r1 = Function '<anonymous closure>':.
    //     0xaee034: add             x1, PP, #0x58, lsl #12  ; [pp+0x581e8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xaee038: ldr             x1, [x1, #0x1e8]
    // 0xaee03c: r2 = Null
    //     0xaee03c: mov             x2, NULL
    // 0xaee040: r0 = AllocateClosure()
    //     0xaee040: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaee044: r1 = Function '<anonymous closure>':.
    //     0xaee044: add             x1, PP, #0x58, lsl #12  ; [pp+0x581f0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xaee048: ldr             x1, [x1, #0x1f0]
    // 0xaee04c: r2 = Null
    //     0xaee04c: mov             x2, NULL
    // 0xaee050: stur            x0, [fp, #-0x38]
    // 0xaee054: r0 = AllocateClosure()
    //     0xaee054: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaee058: stur            x0, [fp, #-0x40]
    // 0xaee05c: r0 = CachedNetworkImage()
    //     0xaee05c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xaee060: stur            x0, [fp, #-0x48]
    // 0xaee064: r16 = Instance_BoxFit
    //     0xaee064: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xaee068: ldr             x16, [x16, #0xb18]
    // 0xaee06c: ldur            lr, [fp, #-0x38]
    // 0xaee070: stp             lr, x16, [SP, #8]
    // 0xaee074: ldur            x16, [fp, #-0x40]
    // 0xaee078: str             x16, [SP]
    // 0xaee07c: mov             x1, x0
    // 0xaee080: ldur            x2, [fp, #-0x10]
    // 0xaee084: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xaee084: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xaee088: ldr             x4, [x4, #0x638]
    // 0xaee08c: r0 = CachedNetworkImage()
    //     0xaee08c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xaee090: r0 = Card()
    //     0xaee090: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xaee094: mov             x1, x0
    // 0xaee098: r0 = Instance_Color
    //     0xaee098: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xaee09c: ldr             x0, [x0, #0xf88]
    // 0xaee0a0: stur            x1, [fp, #-0x10]
    // 0xaee0a4: StoreField: r1->field_b = r0
    //     0xaee0a4: stur            w0, [x1, #0xb]
    // 0xaee0a8: r0 = 0.000000
    //     0xaee0a8: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xaee0ac: ArrayStore: r1[0] = r0  ; List_4
    //     0xaee0ac: stur            w0, [x1, #0x17]
    // 0xaee0b0: ldur            x0, [fp, #-0x30]
    // 0xaee0b4: StoreField: r1->field_1b = r0
    //     0xaee0b4: stur            w0, [x1, #0x1b]
    // 0xaee0b8: r0 = true
    //     0xaee0b8: add             x0, NULL, #0x20  ; true
    // 0xaee0bc: StoreField: r1->field_1f = r0
    //     0xaee0bc: stur            w0, [x1, #0x1f]
    // 0xaee0c0: r2 = Instance_EdgeInsets
    //     0xaee0c0: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xaee0c4: StoreField: r1->field_27 = r2
    //     0xaee0c4: stur            w2, [x1, #0x27]
    // 0xaee0c8: r2 = Instance_Clip
    //     0xaee0c8: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xaee0cc: ldr             x2, [x2, #0xb50]
    // 0xaee0d0: StoreField: r1->field_23 = r2
    //     0xaee0d0: stur            w2, [x1, #0x23]
    // 0xaee0d4: ldur            x2, [fp, #-0x48]
    // 0xaee0d8: StoreField: r1->field_2f = r2
    //     0xaee0d8: stur            w2, [x1, #0x2f]
    // 0xaee0dc: StoreField: r1->field_2b = r0
    //     0xaee0dc: stur            w0, [x1, #0x2b]
    // 0xaee0e0: r2 = Instance__CardVariant
    //     0xaee0e0: add             x2, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xaee0e4: ldr             x2, [x2, #0xa68]
    // 0xaee0e8: StoreField: r1->field_33 = r2
    //     0xaee0e8: stur            w2, [x1, #0x33]
    // 0xaee0ec: r0 = Center()
    //     0xaee0ec: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaee0f0: mov             x3, x0
    // 0xaee0f4: r0 = Instance_Alignment
    //     0xaee0f4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xaee0f8: ldr             x0, [x0, #0xb10]
    // 0xaee0fc: stur            x3, [fp, #-0x30]
    // 0xaee100: StoreField: r3->field_f = r0
    //     0xaee100: stur            w0, [x3, #0xf]
    // 0xaee104: ldur            x0, [fp, #-0x10]
    // 0xaee108: StoreField: r3->field_b = r0
    //     0xaee108: stur            w0, [x3, #0xb]
    // 0xaee10c: r1 = Null
    //     0xaee10c: mov             x1, NULL
    // 0xaee110: r2 = 2
    //     0xaee110: movz            x2, #0x2
    // 0xaee114: r0 = AllocateArray()
    //     0xaee114: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaee118: mov             x2, x0
    // 0xaee11c: ldur            x0, [fp, #-0x30]
    // 0xaee120: stur            x2, [fp, #-0x10]
    // 0xaee124: StoreField: r2->field_f = r0
    //     0xaee124: stur            w0, [x2, #0xf]
    // 0xaee128: r1 = <Widget>
    //     0xaee128: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaee12c: r0 = AllocateGrowableArray()
    //     0xaee12c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaee130: mov             x3, x0
    // 0xaee134: ldur            x0, [fp, #-0x10]
    // 0xaee138: stur            x3, [fp, #-0x30]
    // 0xaee13c: StoreField: r3->field_f = r0
    //     0xaee13c: stur            w0, [x3, #0xf]
    // 0xaee140: r0 = 2
    //     0xaee140: movz            x0, #0x2
    // 0xaee144: StoreField: r3->field_b = r0
    //     0xaee144: stur            w0, [x3, #0xb]
    // 0xaee148: ldur            x0, [fp, #-0x18]
    // 0xaee14c: LoadField: r1 = r0->field_7f
    //     0xaee14c: ldur            w1, [x0, #0x7f]
    // 0xaee150: DecompressPointer r1
    //     0xaee150: add             x1, x1, HEAP, lsl #32
    // 0xaee154: cmp             w1, NULL
    // 0xaee158: b.ne            #0xaee164
    // 0xaee15c: r1 = Null
    //     0xaee15c: mov             x1, NULL
    // 0xaee160: b               #0xaee178
    // 0xaee164: LoadField: r2 = r1->field_7
    //     0xaee164: ldur            w2, [x1, #7]
    // 0xaee168: cbnz            w2, #0xaee174
    // 0xaee16c: r1 = false
    //     0xaee16c: add             x1, NULL, #0x30  ; false
    // 0xaee170: b               #0xaee178
    // 0xaee174: r1 = true
    //     0xaee174: add             x1, NULL, #0x20  ; true
    // 0xaee178: cmp             w1, NULL
    // 0xaee17c: b.eq            #0xaee210
    // 0xaee180: tbnz            w1, #4, #0xaee210
    // 0xaee184: ldur            x1, [fp, #-8]
    // 0xaee188: mov             x2, x0
    // 0xaee18c: r0 = _buildDiscountBadge()
    //     0xaee18c: bl              #0xaee864  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildDiscountBadge
    // 0xaee190: mov             x2, x0
    // 0xaee194: ldur            x0, [fp, #-0x30]
    // 0xaee198: stur            x2, [fp, #-0x10]
    // 0xaee19c: LoadField: r1 = r0->field_b
    //     0xaee19c: ldur            w1, [x0, #0xb]
    // 0xaee1a0: LoadField: r3 = r0->field_f
    //     0xaee1a0: ldur            w3, [x0, #0xf]
    // 0xaee1a4: DecompressPointer r3
    //     0xaee1a4: add             x3, x3, HEAP, lsl #32
    // 0xaee1a8: LoadField: r4 = r3->field_b
    //     0xaee1a8: ldur            w4, [x3, #0xb]
    // 0xaee1ac: r3 = LoadInt32Instr(r1)
    //     0xaee1ac: sbfx            x3, x1, #1, #0x1f
    // 0xaee1b0: stur            x3, [fp, #-0x20]
    // 0xaee1b4: r1 = LoadInt32Instr(r4)
    //     0xaee1b4: sbfx            x1, x4, #1, #0x1f
    // 0xaee1b8: cmp             x3, x1
    // 0xaee1bc: b.ne            #0xaee1c8
    // 0xaee1c0: mov             x1, x0
    // 0xaee1c4: r0 = _growToNextCapacity()
    //     0xaee1c4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaee1c8: ldur            x3, [fp, #-0x30]
    // 0xaee1cc: ldur            x2, [fp, #-0x20]
    // 0xaee1d0: add             x0, x2, #1
    // 0xaee1d4: lsl             x1, x0, #1
    // 0xaee1d8: StoreField: r3->field_b = r1
    //     0xaee1d8: stur            w1, [x3, #0xb]
    // 0xaee1dc: LoadField: r1 = r3->field_f
    //     0xaee1dc: ldur            w1, [x3, #0xf]
    // 0xaee1e0: DecompressPointer r1
    //     0xaee1e0: add             x1, x1, HEAP, lsl #32
    // 0xaee1e4: ldur            x0, [fp, #-0x10]
    // 0xaee1e8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xaee1e8: add             x25, x1, x2, lsl #2
    //     0xaee1ec: add             x25, x25, #0xf
    //     0xaee1f0: str             w0, [x25]
    //     0xaee1f4: tbz             w0, #0, #0xaee210
    //     0xaee1f8: ldurb           w16, [x1, #-1]
    //     0xaee1fc: ldurb           w17, [x0, #-1]
    //     0xaee200: and             x16, x17, x16, lsr #2
    //     0xaee204: tst             x16, HEAP, lsr #32
    //     0xaee208: b.eq            #0xaee210
    //     0xaee20c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaee210: ldur            x0, [fp, #-0x18]
    // 0xaee214: LoadField: r1 = r0->field_b7
    //     0xaee214: ldur            w1, [x0, #0xb7]
    // 0xaee218: DecompressPointer r1
    //     0xaee218: add             x1, x1, HEAP, lsl #32
    // 0xaee21c: cmp             w1, NULL
    // 0xaee220: b.ne            #0xaee22c
    // 0xaee224: r1 = Null
    //     0xaee224: mov             x1, NULL
    // 0xaee228: b               #0xaee240
    // 0xaee22c: LoadField: r2 = r1->field_7
    //     0xaee22c: ldur            w2, [x1, #7]
    // 0xaee230: cbnz            w2, #0xaee23c
    // 0xaee234: r1 = false
    //     0xaee234: add             x1, NULL, #0x30  ; false
    // 0xaee238: b               #0xaee240
    // 0xaee23c: r1 = true
    //     0xaee23c: add             x1, NULL, #0x20  ; true
    // 0xaee240: cmp             w1, NULL
    // 0xaee244: b.ne            #0xaee250
    // 0xaee248: mov             x2, x3
    // 0xaee24c: b               #0xaee2e8
    // 0xaee250: tbnz            w1, #4, #0xaee2e4
    // 0xaee254: ldur            x1, [fp, #-8]
    // 0xaee258: mov             x2, x0
    // 0xaee25c: r0 = _buildStockAlert()
    //     0xaee25c: bl              #0xaee614  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildStockAlert
    // 0xaee260: mov             x2, x0
    // 0xaee264: ldur            x0, [fp, #-0x30]
    // 0xaee268: stur            x2, [fp, #-0x10]
    // 0xaee26c: LoadField: r1 = r0->field_b
    //     0xaee26c: ldur            w1, [x0, #0xb]
    // 0xaee270: LoadField: r3 = r0->field_f
    //     0xaee270: ldur            w3, [x0, #0xf]
    // 0xaee274: DecompressPointer r3
    //     0xaee274: add             x3, x3, HEAP, lsl #32
    // 0xaee278: LoadField: r4 = r3->field_b
    //     0xaee278: ldur            w4, [x3, #0xb]
    // 0xaee27c: r3 = LoadInt32Instr(r1)
    //     0xaee27c: sbfx            x3, x1, #1, #0x1f
    // 0xaee280: stur            x3, [fp, #-0x20]
    // 0xaee284: r1 = LoadInt32Instr(r4)
    //     0xaee284: sbfx            x1, x4, #1, #0x1f
    // 0xaee288: cmp             x3, x1
    // 0xaee28c: b.ne            #0xaee298
    // 0xaee290: mov             x1, x0
    // 0xaee294: r0 = _growToNextCapacity()
    //     0xaee294: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaee298: ldur            x2, [fp, #-0x30]
    // 0xaee29c: ldur            x3, [fp, #-0x20]
    // 0xaee2a0: add             x0, x3, #1
    // 0xaee2a4: lsl             x1, x0, #1
    // 0xaee2a8: StoreField: r2->field_b = r1
    //     0xaee2a8: stur            w1, [x2, #0xb]
    // 0xaee2ac: LoadField: r1 = r2->field_f
    //     0xaee2ac: ldur            w1, [x2, #0xf]
    // 0xaee2b0: DecompressPointer r1
    //     0xaee2b0: add             x1, x1, HEAP, lsl #32
    // 0xaee2b4: ldur            x0, [fp, #-0x10]
    // 0xaee2b8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaee2b8: add             x25, x1, x3, lsl #2
    //     0xaee2bc: add             x25, x25, #0xf
    //     0xaee2c0: str             w0, [x25]
    //     0xaee2c4: tbz             w0, #0, #0xaee2e0
    //     0xaee2c8: ldurb           w16, [x1, #-1]
    //     0xaee2cc: ldurb           w17, [x0, #-1]
    //     0xaee2d0: and             x16, x17, x16, lsr #2
    //     0xaee2d4: tst             x16, HEAP, lsr #32
    //     0xaee2d8: b.eq            #0xaee2e0
    //     0xaee2dc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaee2e0: b               #0xaee2e8
    // 0xaee2e4: mov             x2, x3
    // 0xaee2e8: ldur            x0, [fp, #-0x18]
    // 0xaee2ec: LoadField: r1 = r0->field_8b
    //     0xaee2ec: ldur            w1, [x0, #0x8b]
    // 0xaee2f0: DecompressPointer r1
    //     0xaee2f0: add             x1, x1, HEAP, lsl #32
    // 0xaee2f4: cmp             w1, NULL
    // 0xaee2f8: b.eq            #0xaee388
    // 0xaee2fc: tbnz            w1, #4, #0xaee388
    // 0xaee300: ldur            x1, [fp, #-8]
    // 0xaee304: r0 = _buildCustomizationBadge()
    //     0xaee304: bl              #0xaee430  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildCustomizationBadge
    // 0xaee308: mov             x2, x0
    // 0xaee30c: ldur            x0, [fp, #-0x30]
    // 0xaee310: stur            x2, [fp, #-8]
    // 0xaee314: LoadField: r1 = r0->field_b
    //     0xaee314: ldur            w1, [x0, #0xb]
    // 0xaee318: LoadField: r3 = r0->field_f
    //     0xaee318: ldur            w3, [x0, #0xf]
    // 0xaee31c: DecompressPointer r3
    //     0xaee31c: add             x3, x3, HEAP, lsl #32
    // 0xaee320: LoadField: r4 = r3->field_b
    //     0xaee320: ldur            w4, [x3, #0xb]
    // 0xaee324: r3 = LoadInt32Instr(r1)
    //     0xaee324: sbfx            x3, x1, #1, #0x1f
    // 0xaee328: stur            x3, [fp, #-0x20]
    // 0xaee32c: r1 = LoadInt32Instr(r4)
    //     0xaee32c: sbfx            x1, x4, #1, #0x1f
    // 0xaee330: cmp             x3, x1
    // 0xaee334: b.ne            #0xaee340
    // 0xaee338: mov             x1, x0
    // 0xaee33c: r0 = _growToNextCapacity()
    //     0xaee33c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaee340: ldur            x2, [fp, #-0x30]
    // 0xaee344: ldur            x3, [fp, #-0x20]
    // 0xaee348: add             x0, x3, #1
    // 0xaee34c: lsl             x1, x0, #1
    // 0xaee350: StoreField: r2->field_b = r1
    //     0xaee350: stur            w1, [x2, #0xb]
    // 0xaee354: LoadField: r1 = r2->field_f
    //     0xaee354: ldur            w1, [x2, #0xf]
    // 0xaee358: DecompressPointer r1
    //     0xaee358: add             x1, x1, HEAP, lsl #32
    // 0xaee35c: ldur            x0, [fp, #-8]
    // 0xaee360: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaee360: add             x25, x1, x3, lsl #2
    //     0xaee364: add             x25, x25, #0xf
    //     0xaee368: str             w0, [x25]
    //     0xaee36c: tbz             w0, #0, #0xaee388
    //     0xaee370: ldurb           w16, [x1, #-1]
    //     0xaee374: ldurb           w17, [x0, #-1]
    //     0xaee378: and             x16, x17, x16, lsr #2
    //     0xaee37c: tst             x16, HEAP, lsr #32
    //     0xaee380: b.eq            #0xaee388
    //     0xaee384: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaee388: r0 = Stack()
    //     0xaee388: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xaee38c: mov             x1, x0
    // 0xaee390: r0 = Instance_Alignment
    //     0xaee390: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xaee394: ldr             x0, [x0, #0x5b8]
    // 0xaee398: stur            x1, [fp, #-8]
    // 0xaee39c: StoreField: r1->field_f = r0
    //     0xaee39c: stur            w0, [x1, #0xf]
    // 0xaee3a0: r0 = Instance_StackFit
    //     0xaee3a0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xaee3a4: ldr             x0, [x0, #0xfa8]
    // 0xaee3a8: ArrayStore: r1[0] = r0  ; List_4
    //     0xaee3a8: stur            w0, [x1, #0x17]
    // 0xaee3ac: r0 = Instance_Clip
    //     0xaee3ac: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xaee3b0: ldr             x0, [x0, #0x7e0]
    // 0xaee3b4: StoreField: r1->field_1b = r0
    //     0xaee3b4: stur            w0, [x1, #0x1b]
    // 0xaee3b8: ldur            x0, [fp, #-0x30]
    // 0xaee3bc: StoreField: r1->field_b = r0
    //     0xaee3bc: stur            w0, [x1, #0xb]
    // 0xaee3c0: r0 = InkWell()
    //     0xaee3c0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xaee3c4: mov             x3, x0
    // 0xaee3c8: ldur            x0, [fp, #-8]
    // 0xaee3cc: stur            x3, [fp, #-0x10]
    // 0xaee3d0: StoreField: r3->field_b = r0
    //     0xaee3d0: stur            w0, [x3, #0xb]
    // 0xaee3d4: ldur            x2, [fp, #-0x28]
    // 0xaee3d8: r1 = Function '<anonymous closure>':.
    //     0xaee3d8: add             x1, PP, #0x58, lsl #12  ; [pp+0x581f8] AnonymousClosure: (0xaedd5c), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::cosmeticThemeSlider (0xaec9c0)
    //     0xaee3dc: ldr             x1, [x1, #0x1f8]
    // 0xaee3e0: r0 = AllocateClosure()
    //     0xaee3e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaee3e4: mov             x1, x0
    // 0xaee3e8: ldur            x0, [fp, #-0x10]
    // 0xaee3ec: StoreField: r0->field_f = r1
    //     0xaee3ec: stur            w1, [x0, #0xf]
    // 0xaee3f0: r1 = true
    //     0xaee3f0: add             x1, NULL, #0x20  ; true
    // 0xaee3f4: StoreField: r0->field_43 = r1
    //     0xaee3f4: stur            w1, [x0, #0x43]
    // 0xaee3f8: r2 = Instance_BoxShape
    //     0xaee3f8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaee3fc: ldr             x2, [x2, #0x80]
    // 0xaee400: StoreField: r0->field_47 = r2
    //     0xaee400: stur            w2, [x0, #0x47]
    // 0xaee404: StoreField: r0->field_6f = r1
    //     0xaee404: stur            w1, [x0, #0x6f]
    // 0xaee408: r2 = false
    //     0xaee408: add             x2, NULL, #0x30  ; false
    // 0xaee40c: StoreField: r0->field_73 = r2
    //     0xaee40c: stur            w2, [x0, #0x73]
    // 0xaee410: StoreField: r0->field_83 = r1
    //     0xaee410: stur            w1, [x0, #0x83]
    // 0xaee414: StoreField: r0->field_7b = r2
    //     0xaee414: stur            w2, [x0, #0x7b]
    // 0xaee418: LeaveFrame
    //     0xaee418: mov             SP, fp
    //     0xaee41c: ldp             fp, lr, [SP], #0x10
    // 0xaee420: ret
    //     0xaee420: ret             
    // 0xaee424: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaee424: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaee428: b               #0xaedf5c
    // 0xaee42c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaee42c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildCustomizationBadge(/* No info */) {
    // ** addr: 0xaee430, size: 0x1e4
    // 0xaee430: EnterFrame
    //     0xaee430: stp             fp, lr, [SP, #-0x10]!
    //     0xaee434: mov             fp, SP
    // 0xaee438: AllocStack(0x38)
    //     0xaee438: sub             SP, SP, #0x38
    // 0xaee43c: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xaee43c: mov             x0, x1
    //     0xaee440: stur            x1, [fp, #-8]
    // 0xaee444: CheckStackOverflow
    //     0xaee444: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaee448: cmp             SP, x16
    //     0xaee44c: b.ls            #0xaee604
    // 0xaee450: LoadField: r1 = r0->field_f
    //     0xaee450: ldur            w1, [x0, #0xf]
    // 0xaee454: DecompressPointer r1
    //     0xaee454: add             x1, x1, HEAP, lsl #32
    // 0xaee458: cmp             w1, NULL
    // 0xaee45c: b.eq            #0xaee60c
    // 0xaee460: r0 = of()
    //     0xaee460: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaee464: r17 = 307
    //     0xaee464: movz            x17, #0x133
    // 0xaee468: ldr             w1, [x0, x17]
    // 0xaee46c: DecompressPointer r1
    //     0xaee46c: add             x1, x1, HEAP, lsl #32
    // 0xaee470: stur            x1, [fp, #-0x10]
    // 0xaee474: r0 = Radius()
    //     0xaee474: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaee478: d0 = 5.000000
    //     0xaee478: fmov            d0, #5.00000000
    // 0xaee47c: stur            x0, [fp, #-0x18]
    // 0xaee480: StoreField: r0->field_7 = d0
    //     0xaee480: stur            d0, [x0, #7]
    // 0xaee484: StoreField: r0->field_f = d0
    //     0xaee484: stur            d0, [x0, #0xf]
    // 0xaee488: r0 = BorderRadius()
    //     0xaee488: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaee48c: mov             x1, x0
    // 0xaee490: ldur            x0, [fp, #-0x18]
    // 0xaee494: stur            x1, [fp, #-0x20]
    // 0xaee498: StoreField: r1->field_7 = r0
    //     0xaee498: stur            w0, [x1, #7]
    // 0xaee49c: StoreField: r1->field_b = r0
    //     0xaee49c: stur            w0, [x1, #0xb]
    // 0xaee4a0: StoreField: r1->field_f = r0
    //     0xaee4a0: stur            w0, [x1, #0xf]
    // 0xaee4a4: StoreField: r1->field_13 = r0
    //     0xaee4a4: stur            w0, [x1, #0x13]
    // 0xaee4a8: r0 = RoundedRectangleBorder()
    //     0xaee4a8: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xaee4ac: mov             x1, x0
    // 0xaee4b0: ldur            x0, [fp, #-0x20]
    // 0xaee4b4: StoreField: r1->field_b = r0
    //     0xaee4b4: stur            w0, [x1, #0xb]
    // 0xaee4b8: r0 = Instance_BorderSide
    //     0xaee4b8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xaee4bc: ldr             x0, [x0, #0xe20]
    // 0xaee4c0: StoreField: r1->field_7 = r0
    //     0xaee4c0: stur            w0, [x1, #7]
    // 0xaee4c4: r16 = <RoundedRectangleBorder>
    //     0xaee4c4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xaee4c8: ldr             x16, [x16, #0xf78]
    // 0xaee4cc: stp             x1, x16, [SP]
    // 0xaee4d0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaee4d0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaee4d4: r0 = all()
    //     0xaee4d4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaee4d8: stur            x0, [fp, #-0x18]
    // 0xaee4dc: r0 = ButtonStyle()
    //     0xaee4dc: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xaee4e0: mov             x2, x0
    // 0xaee4e4: ldur            x0, [fp, #-0x18]
    // 0xaee4e8: stur            x2, [fp, #-0x20]
    // 0xaee4ec: StoreField: r2->field_43 = r0
    //     0xaee4ec: stur            w0, [x2, #0x43]
    // 0xaee4f0: ldur            x0, [fp, #-8]
    // 0xaee4f4: LoadField: r1 = r0->field_f
    //     0xaee4f4: ldur            w1, [x0, #0xf]
    // 0xaee4f8: DecompressPointer r1
    //     0xaee4f8: add             x1, x1, HEAP, lsl #32
    // 0xaee4fc: cmp             w1, NULL
    // 0xaee500: b.eq            #0xaee610
    // 0xaee504: r0 = of()
    //     0xaee504: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaee508: LoadField: r1 = r0->field_87
    //     0xaee508: ldur            w1, [x0, #0x87]
    // 0xaee50c: DecompressPointer r1
    //     0xaee50c: add             x1, x1, HEAP, lsl #32
    // 0xaee510: LoadField: r0 = r1->field_2b
    //     0xaee510: ldur            w0, [x1, #0x2b]
    // 0xaee514: DecompressPointer r0
    //     0xaee514: add             x0, x0, HEAP, lsl #32
    // 0xaee518: r16 = 12.000000
    //     0xaee518: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaee51c: ldr             x16, [x16, #0x9e8]
    // 0xaee520: r30 = Instance_Color
    //     0xaee520: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaee524: stp             lr, x16, [SP]
    // 0xaee528: mov             x1, x0
    // 0xaee52c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaee52c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaee530: ldr             x4, [x4, #0xaa0]
    // 0xaee534: r0 = copyWith()
    //     0xaee534: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaee538: stur            x0, [fp, #-8]
    // 0xaee53c: r0 = Text()
    //     0xaee53c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaee540: mov             x3, x0
    // 0xaee544: r0 = "Customisable"
    //     0xaee544: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xaee548: ldr             x0, [x0, #0x970]
    // 0xaee54c: stur            x3, [fp, #-0x18]
    // 0xaee550: StoreField: r3->field_b = r0
    //     0xaee550: stur            w0, [x3, #0xb]
    // 0xaee554: ldur            x0, [fp, #-8]
    // 0xaee558: StoreField: r3->field_13 = r0
    //     0xaee558: stur            w0, [x3, #0x13]
    // 0xaee55c: r1 = Function '<anonymous closure>':.
    //     0xaee55c: add             x1, PP, #0x58, lsl #12  ; [pp+0x58200] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xaee560: ldr             x1, [x1, #0x200]
    // 0xaee564: r2 = Null
    //     0xaee564: mov             x2, NULL
    // 0xaee568: r0 = AllocateClosure()
    //     0xaee568: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaee56c: stur            x0, [fp, #-8]
    // 0xaee570: r0 = TextButton()
    //     0xaee570: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xaee574: mov             x1, x0
    // 0xaee578: ldur            x0, [fp, #-8]
    // 0xaee57c: stur            x1, [fp, #-0x28]
    // 0xaee580: StoreField: r1->field_b = r0
    //     0xaee580: stur            w0, [x1, #0xb]
    // 0xaee584: ldur            x0, [fp, #-0x20]
    // 0xaee588: StoreField: r1->field_1b = r0
    //     0xaee588: stur            w0, [x1, #0x1b]
    // 0xaee58c: r0 = false
    //     0xaee58c: add             x0, NULL, #0x30  ; false
    // 0xaee590: StoreField: r1->field_27 = r0
    //     0xaee590: stur            w0, [x1, #0x27]
    // 0xaee594: r0 = true
    //     0xaee594: add             x0, NULL, #0x20  ; true
    // 0xaee598: StoreField: r1->field_2f = r0
    //     0xaee598: stur            w0, [x1, #0x2f]
    // 0xaee59c: ldur            x0, [fp, #-0x18]
    // 0xaee5a0: StoreField: r1->field_37 = r0
    //     0xaee5a0: stur            w0, [x1, #0x37]
    // 0xaee5a4: r0 = TextButtonTheme()
    //     0xaee5a4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xaee5a8: mov             x1, x0
    // 0xaee5ac: ldur            x0, [fp, #-0x10]
    // 0xaee5b0: stur            x1, [fp, #-8]
    // 0xaee5b4: StoreField: r1->field_f = r0
    //     0xaee5b4: stur            w0, [x1, #0xf]
    // 0xaee5b8: ldur            x0, [fp, #-0x28]
    // 0xaee5bc: StoreField: r1->field_b = r0
    //     0xaee5bc: stur            w0, [x1, #0xb]
    // 0xaee5c0: r0 = SizedBox()
    //     0xaee5c0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaee5c4: mov             x1, x0
    // 0xaee5c8: r0 = 30.000000
    //     0xaee5c8: add             x0, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xaee5cc: ldr             x0, [x0, #0x768]
    // 0xaee5d0: stur            x1, [fp, #-0x10]
    // 0xaee5d4: StoreField: r1->field_13 = r0
    //     0xaee5d4: stur            w0, [x1, #0x13]
    // 0xaee5d8: ldur            x0, [fp, #-8]
    // 0xaee5dc: StoreField: r1->field_b = r0
    //     0xaee5dc: stur            w0, [x1, #0xb]
    // 0xaee5e0: r0 = Padding()
    //     0xaee5e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaee5e4: r1 = Instance_EdgeInsets
    //     0xaee5e4: add             x1, PP, #0x52, lsl #12  ; [pp+0x52e68] Obj!EdgeInsets@d58461
    //     0xaee5e8: ldr             x1, [x1, #0xe68]
    // 0xaee5ec: StoreField: r0->field_f = r1
    //     0xaee5ec: stur            w1, [x0, #0xf]
    // 0xaee5f0: ldur            x1, [fp, #-0x10]
    // 0xaee5f4: StoreField: r0->field_b = r1
    //     0xaee5f4: stur            w1, [x0, #0xb]
    // 0xaee5f8: LeaveFrame
    //     0xaee5f8: mov             SP, fp
    //     0xaee5fc: ldp             fp, lr, [SP], #0x10
    // 0xaee600: ret
    //     0xaee600: ret             
    // 0xaee604: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaee604: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaee608: b               #0xaee450
    // 0xaee60c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaee60c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaee610: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaee610: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildStockAlert(/* No info */) {
    // ** addr: 0xaee614, size: 0x250
    // 0xaee614: EnterFrame
    //     0xaee614: stp             fp, lr, [SP, #-0x10]!
    //     0xaee618: mov             fp, SP
    // 0xaee61c: AllocStack(0x50)
    //     0xaee61c: sub             SP, SP, #0x50
    // 0xaee620: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xaee620: stur            x1, [fp, #-8]
    //     0xaee624: stur            x2, [fp, #-0x10]
    // 0xaee628: CheckStackOverflow
    //     0xaee628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaee62c: cmp             SP, x16
    //     0xaee630: b.ls            #0xaee858
    // 0xaee634: LoadField: r0 = r2->field_8b
    //     0xaee634: ldur            w0, [x2, #0x8b]
    // 0xaee638: DecompressPointer r0
    //     0xaee638: add             x0, x0, HEAP, lsl #32
    // 0xaee63c: cmp             w0, NULL
    // 0xaee640: b.eq            #0xaee654
    // 0xaee644: tbnz            w0, #4, #0xaee654
    // 0xaee648: d0 = 38.000000
    //     0xaee648: add             x17, PP, #0x50, lsl #12  ; [pp+0x50d10] IMM: double(38) from 0x4043000000000000
    //     0xaee64c: ldr             d0, [x17, #0xd10]
    // 0xaee650: b               #0xaee658
    // 0xaee654: d0 = 4.000000
    //     0xaee654: fmov            d0, #4.00000000
    // 0xaee658: stur            d0, [fp, #-0x40]
    // 0xaee65c: r0 = EdgeInsets()
    //     0xaee65c: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xaee660: d0 = 8.000000
    //     0xaee660: fmov            d0, #8.00000000
    // 0xaee664: stur            x0, [fp, #-0x18]
    // 0xaee668: StoreField: r0->field_7 = d0
    //     0xaee668: stur            d0, [x0, #7]
    // 0xaee66c: StoreField: r0->field_f = rZR
    //     0xaee66c: stur            xzr, [x0, #0xf]
    // 0xaee670: ArrayStore: r0[0] = rZR  ; List_8
    //     0xaee670: stur            xzr, [x0, #0x17]
    // 0xaee674: ldur            d0, [fp, #-0x40]
    // 0xaee678: StoreField: r0->field_1f = d0
    //     0xaee678: stur            d0, [x0, #0x1f]
    // 0xaee67c: r16 = <EdgeInsets>
    //     0xaee67c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xaee680: ldr             x16, [x16, #0xda0]
    // 0xaee684: r30 = Instance_EdgeInsets
    //     0xaee684: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xaee688: ldr             lr, [lr, #0x668]
    // 0xaee68c: stp             lr, x16, [SP]
    // 0xaee690: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaee690: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaee694: r0 = all()
    //     0xaee694: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaee698: stur            x0, [fp, #-0x20]
    // 0xaee69c: r16 = <Color>
    //     0xaee69c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xaee6a0: ldr             x16, [x16, #0xf80]
    // 0xaee6a4: r30 = Instance_Color
    //     0xaee6a4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaee6a8: stp             lr, x16, [SP]
    // 0xaee6ac: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaee6ac: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaee6b0: r0 = all()
    //     0xaee6b0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaee6b4: stur            x0, [fp, #-0x28]
    // 0xaee6b8: r0 = Radius()
    //     0xaee6b8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaee6bc: d0 = 5.000000
    //     0xaee6bc: fmov            d0, #5.00000000
    // 0xaee6c0: stur            x0, [fp, #-0x30]
    // 0xaee6c4: StoreField: r0->field_7 = d0
    //     0xaee6c4: stur            d0, [x0, #7]
    // 0xaee6c8: StoreField: r0->field_f = d0
    //     0xaee6c8: stur            d0, [x0, #0xf]
    // 0xaee6cc: r0 = BorderRadius()
    //     0xaee6cc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaee6d0: mov             x1, x0
    // 0xaee6d4: ldur            x0, [fp, #-0x30]
    // 0xaee6d8: stur            x1, [fp, #-0x38]
    // 0xaee6dc: StoreField: r1->field_7 = r0
    //     0xaee6dc: stur            w0, [x1, #7]
    // 0xaee6e0: StoreField: r1->field_b = r0
    //     0xaee6e0: stur            w0, [x1, #0xb]
    // 0xaee6e4: StoreField: r1->field_f = r0
    //     0xaee6e4: stur            w0, [x1, #0xf]
    // 0xaee6e8: StoreField: r1->field_13 = r0
    //     0xaee6e8: stur            w0, [x1, #0x13]
    // 0xaee6ec: r0 = RoundedRectangleBorder()
    //     0xaee6ec: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xaee6f0: mov             x1, x0
    // 0xaee6f4: ldur            x0, [fp, #-0x38]
    // 0xaee6f8: StoreField: r1->field_b = r0
    //     0xaee6f8: stur            w0, [x1, #0xb]
    // 0xaee6fc: r0 = Instance_BorderSide
    //     0xaee6fc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xaee700: ldr             x0, [x0, #0xe20]
    // 0xaee704: StoreField: r1->field_7 = r0
    //     0xaee704: stur            w0, [x1, #7]
    // 0xaee708: r16 = <RoundedRectangleBorder>
    //     0xaee708: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xaee70c: ldr             x16, [x16, #0xf78]
    // 0xaee710: stp             x1, x16, [SP]
    // 0xaee714: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaee714: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaee718: r0 = all()
    //     0xaee718: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaee71c: stur            x0, [fp, #-0x30]
    // 0xaee720: r0 = ButtonStyle()
    //     0xaee720: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xaee724: mov             x1, x0
    // 0xaee728: ldur            x0, [fp, #-0x28]
    // 0xaee72c: stur            x1, [fp, #-0x38]
    // 0xaee730: StoreField: r1->field_b = r0
    //     0xaee730: stur            w0, [x1, #0xb]
    // 0xaee734: ldur            x0, [fp, #-0x20]
    // 0xaee738: StoreField: r1->field_23 = r0
    //     0xaee738: stur            w0, [x1, #0x23]
    // 0xaee73c: ldur            x0, [fp, #-0x30]
    // 0xaee740: StoreField: r1->field_43 = r0
    //     0xaee740: stur            w0, [x1, #0x43]
    // 0xaee744: r0 = TextButtonThemeData()
    //     0xaee744: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xaee748: mov             x1, x0
    // 0xaee74c: ldur            x0, [fp, #-0x38]
    // 0xaee750: stur            x1, [fp, #-0x20]
    // 0xaee754: StoreField: r1->field_7 = r0
    //     0xaee754: stur            w0, [x1, #7]
    // 0xaee758: ldur            x0, [fp, #-0x10]
    // 0xaee75c: LoadField: r2 = r0->field_b7
    //     0xaee75c: ldur            w2, [x0, #0xb7]
    // 0xaee760: DecompressPointer r2
    //     0xaee760: add             x2, x2, HEAP, lsl #32
    // 0xaee764: str             x2, [SP]
    // 0xaee768: r0 = _interpolateSingle()
    //     0xaee768: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xaee76c: mov             x2, x0
    // 0xaee770: ldur            x0, [fp, #-8]
    // 0xaee774: stur            x2, [fp, #-0x10]
    // 0xaee778: LoadField: r1 = r0->field_f
    //     0xaee778: ldur            w1, [x0, #0xf]
    // 0xaee77c: DecompressPointer r1
    //     0xaee77c: add             x1, x1, HEAP, lsl #32
    // 0xaee780: cmp             w1, NULL
    // 0xaee784: b.eq            #0xaee860
    // 0xaee788: r0 = of()
    //     0xaee788: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaee78c: LoadField: r1 = r0->field_87
    //     0xaee78c: ldur            w1, [x0, #0x87]
    // 0xaee790: DecompressPointer r1
    //     0xaee790: add             x1, x1, HEAP, lsl #32
    // 0xaee794: LoadField: r0 = r1->field_2b
    //     0xaee794: ldur            w0, [x1, #0x2b]
    // 0xaee798: DecompressPointer r0
    //     0xaee798: add             x0, x0, HEAP, lsl #32
    // 0xaee79c: r16 = 12.000000
    //     0xaee79c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaee7a0: ldr             x16, [x16, #0x9e8]
    // 0xaee7a4: r30 = Instance_Color
    //     0xaee7a4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaee7a8: stp             lr, x16, [SP]
    // 0xaee7ac: mov             x1, x0
    // 0xaee7b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaee7b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaee7b4: ldr             x4, [x4, #0xaa0]
    // 0xaee7b8: r0 = copyWith()
    //     0xaee7b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaee7bc: stur            x0, [fp, #-8]
    // 0xaee7c0: r0 = Text()
    //     0xaee7c0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaee7c4: mov             x3, x0
    // 0xaee7c8: ldur            x0, [fp, #-0x10]
    // 0xaee7cc: stur            x3, [fp, #-0x28]
    // 0xaee7d0: StoreField: r3->field_b = r0
    //     0xaee7d0: stur            w0, [x3, #0xb]
    // 0xaee7d4: ldur            x0, [fp, #-8]
    // 0xaee7d8: StoreField: r3->field_13 = r0
    //     0xaee7d8: stur            w0, [x3, #0x13]
    // 0xaee7dc: r1 = Function '<anonymous closure>':.
    //     0xaee7dc: add             x1, PP, #0x58, lsl #12  ; [pp+0x58208] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xaee7e0: ldr             x1, [x1, #0x208]
    // 0xaee7e4: r2 = Null
    //     0xaee7e4: mov             x2, NULL
    // 0xaee7e8: r0 = AllocateClosure()
    //     0xaee7e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaee7ec: stur            x0, [fp, #-8]
    // 0xaee7f0: r0 = TextButton()
    //     0xaee7f0: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xaee7f4: mov             x1, x0
    // 0xaee7f8: ldur            x0, [fp, #-8]
    // 0xaee7fc: stur            x1, [fp, #-0x10]
    // 0xaee800: StoreField: r1->field_b = r0
    //     0xaee800: stur            w0, [x1, #0xb]
    // 0xaee804: r0 = false
    //     0xaee804: add             x0, NULL, #0x30  ; false
    // 0xaee808: StoreField: r1->field_27 = r0
    //     0xaee808: stur            w0, [x1, #0x27]
    // 0xaee80c: r0 = true
    //     0xaee80c: add             x0, NULL, #0x20  ; true
    // 0xaee810: StoreField: r1->field_2f = r0
    //     0xaee810: stur            w0, [x1, #0x2f]
    // 0xaee814: ldur            x0, [fp, #-0x28]
    // 0xaee818: StoreField: r1->field_37 = r0
    //     0xaee818: stur            w0, [x1, #0x37]
    // 0xaee81c: r0 = TextButtonTheme()
    //     0xaee81c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xaee820: mov             x1, x0
    // 0xaee824: ldur            x0, [fp, #-0x20]
    // 0xaee828: stur            x1, [fp, #-8]
    // 0xaee82c: StoreField: r1->field_f = r0
    //     0xaee82c: stur            w0, [x1, #0xf]
    // 0xaee830: ldur            x0, [fp, #-0x10]
    // 0xaee834: StoreField: r1->field_b = r0
    //     0xaee834: stur            w0, [x1, #0xb]
    // 0xaee838: r0 = Padding()
    //     0xaee838: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaee83c: ldur            x1, [fp, #-0x18]
    // 0xaee840: StoreField: r0->field_f = r1
    //     0xaee840: stur            w1, [x0, #0xf]
    // 0xaee844: ldur            x1, [fp, #-8]
    // 0xaee848: StoreField: r0->field_b = r1
    //     0xaee848: stur            w1, [x0, #0xb]
    // 0xaee84c: LeaveFrame
    //     0xaee84c: mov             SP, fp
    //     0xaee850: ldp             fp, lr, [SP], #0x10
    // 0xaee854: ret
    //     0xaee854: ret             
    // 0xaee858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaee858: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaee85c: b               #0xaee634
    // 0xaee860: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaee860: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildDiscountBadge(/* No info */) {
    // ** addr: 0xaee864, size: 0x84c
    // 0xaee864: EnterFrame
    //     0xaee864: stp             fp, lr, [SP, #-0x10]!
    //     0xaee868: mov             fp, SP
    // 0xaee86c: AllocStack(0x70)
    //     0xaee86c: sub             SP, SP, #0x70
    // 0xaee870: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xaee870: stur            x1, [fp, #-0x10]
    //     0xaee874: stur            x2, [fp, #-0x18]
    // 0xaee878: CheckStackOverflow
    //     0xaee878: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaee87c: cmp             SP, x16
    //     0xaee880: b.ls            #0xaef07c
    // 0xaee884: LoadField: r0 = r1->field_b
    //     0xaee884: ldur            w0, [x1, #0xb]
    // 0xaee888: DecompressPointer r0
    //     0xaee888: add             x0, x0, HEAP, lsl #32
    // 0xaee88c: cmp             w0, NULL
    // 0xaee890: b.eq            #0xaef084
    // 0xaee894: LoadField: r3 = r0->field_33
    //     0xaee894: ldur            w3, [x0, #0x33]
    // 0xaee898: DecompressPointer r3
    //     0xaee898: add             x3, x3, HEAP, lsl #32
    // 0xaee89c: stur            x3, [fp, #-8]
    // 0xaee8a0: cmp             w3, NULL
    // 0xaee8a4: b.ne            #0xaee8c8
    // 0xaee8a8: mov             x4, x1
    // 0xaee8ac: mov             x3, x2
    // 0xaee8b0: r5 = Instance_Alignment
    //     0xaee8b0: add             x5, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xaee8b4: ldr             x5, [x5, #0xfa0]
    // 0xaee8b8: r2 = 4
    //     0xaee8b8: movz            x2, #0x4
    // 0xaee8bc: r0 = Instance_BoxShape
    //     0xaee8bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaee8c0: ldr             x0, [x0, #0x80]
    // 0xaee8c4: b               #0xaeed68
    // 0xaee8c8: LoadField: r0 = r3->field_f
    //     0xaee8c8: ldur            w0, [x3, #0xf]
    // 0xaee8cc: DecompressPointer r0
    //     0xaee8cc: add             x0, x0, HEAP, lsl #32
    // 0xaee8d0: cmp             w0, NULL
    // 0xaee8d4: b.eq            #0xaeed4c
    // 0xaee8d8: r0 = Radius()
    //     0xaee8d8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaee8dc: d0 = 8.000000
    //     0xaee8dc: fmov            d0, #8.00000000
    // 0xaee8e0: stur            x0, [fp, #-0x20]
    // 0xaee8e4: StoreField: r0->field_7 = d0
    //     0xaee8e4: stur            d0, [x0, #7]
    // 0xaee8e8: StoreField: r0->field_f = d0
    //     0xaee8e8: stur            d0, [x0, #0xf]
    // 0xaee8ec: r0 = BorderRadius()
    //     0xaee8ec: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaee8f0: mov             x1, x0
    // 0xaee8f4: ldur            x0, [fp, #-0x20]
    // 0xaee8f8: stur            x1, [fp, #-0x40]
    // 0xaee8fc: StoreField: r1->field_7 = r0
    //     0xaee8fc: stur            w0, [x1, #7]
    // 0xaee900: StoreField: r1->field_b = r0
    //     0xaee900: stur            w0, [x1, #0xb]
    // 0xaee904: StoreField: r1->field_f = r0
    //     0xaee904: stur            w0, [x1, #0xf]
    // 0xaee908: StoreField: r1->field_13 = r0
    //     0xaee908: stur            w0, [x1, #0x13]
    // 0xaee90c: ldur            x0, [fp, #-8]
    // 0xaee910: LoadField: r2 = r0->field_13
    //     0xaee910: ldur            w2, [x0, #0x13]
    // 0xaee914: DecompressPointer r2
    //     0xaee914: add             x2, x2, HEAP, lsl #32
    // 0xaee918: stur            x2, [fp, #-0x20]
    // 0xaee91c: cmp             w2, NULL
    // 0xaee920: b.ne            #0xaee92c
    // 0xaee924: r0 = Null
    //     0xaee924: mov             x0, NULL
    // 0xaee928: b               #0xaee934
    // 0xaee92c: LoadField: r0 = r2->field_7
    //     0xaee92c: ldur            w0, [x2, #7]
    // 0xaee930: DecompressPointer r0
    //     0xaee930: add             x0, x0, HEAP, lsl #32
    // 0xaee934: cmp             w0, NULL
    // 0xaee938: b.ne            #0xaee944
    // 0xaee93c: r0 = 0
    //     0xaee93c: movz            x0, #0
    // 0xaee940: b               #0xaee954
    // 0xaee944: r3 = LoadInt32Instr(r0)
    //     0xaee944: sbfx            x3, x0, #1, #0x1f
    //     0xaee948: tbz             w0, #0, #0xaee950
    //     0xaee94c: ldur            x3, [x0, #7]
    // 0xaee950: mov             x0, x3
    // 0xaee954: stur            x0, [fp, #-0x38]
    // 0xaee958: cmp             w2, NULL
    // 0xaee95c: b.ne            #0xaee968
    // 0xaee960: r3 = Null
    //     0xaee960: mov             x3, NULL
    // 0xaee964: b               #0xaee970
    // 0xaee968: LoadField: r3 = r2->field_b
    //     0xaee968: ldur            w3, [x2, #0xb]
    // 0xaee96c: DecompressPointer r3
    //     0xaee96c: add             x3, x3, HEAP, lsl #32
    // 0xaee970: cmp             w3, NULL
    // 0xaee974: b.ne            #0xaee980
    // 0xaee978: r3 = 0
    //     0xaee978: movz            x3, #0
    // 0xaee97c: b               #0xaee990
    // 0xaee980: r4 = LoadInt32Instr(r3)
    //     0xaee980: sbfx            x4, x3, #1, #0x1f
    //     0xaee984: tbz             w3, #0, #0xaee98c
    //     0xaee988: ldur            x4, [x3, #7]
    // 0xaee98c: mov             x3, x4
    // 0xaee990: stur            x3, [fp, #-0x30]
    // 0xaee994: cmp             w2, NULL
    // 0xaee998: b.ne            #0xaee9a4
    // 0xaee99c: r4 = Null
    //     0xaee99c: mov             x4, NULL
    // 0xaee9a0: b               #0xaee9ac
    // 0xaee9a4: LoadField: r4 = r2->field_f
    //     0xaee9a4: ldur            w4, [x2, #0xf]
    // 0xaee9a8: DecompressPointer r4
    //     0xaee9a8: add             x4, x4, HEAP, lsl #32
    // 0xaee9ac: cmp             w4, NULL
    // 0xaee9b0: b.ne            #0xaee9bc
    // 0xaee9b4: r4 = 0
    //     0xaee9b4: movz            x4, #0
    // 0xaee9b8: b               #0xaee9cc
    // 0xaee9bc: r5 = LoadInt32Instr(r4)
    //     0xaee9bc: sbfx            x5, x4, #1, #0x1f
    //     0xaee9c0: tbz             w4, #0, #0xaee9c8
    //     0xaee9c4: ldur            x5, [x4, #7]
    // 0xaee9c8: mov             x4, x5
    // 0xaee9cc: stur            x4, [fp, #-0x28]
    // 0xaee9d0: r0 = Color()
    //     0xaee9d0: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xaee9d4: mov             x1, x0
    // 0xaee9d8: r0 = Instance_ColorSpace
    //     0xaee9d8: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xaee9dc: stur            x1, [fp, #-8]
    // 0xaee9e0: StoreField: r1->field_27 = r0
    //     0xaee9e0: stur            w0, [x1, #0x27]
    // 0xaee9e4: d0 = 1.000000
    //     0xaee9e4: fmov            d0, #1.00000000
    // 0xaee9e8: StoreField: r1->field_7 = d0
    //     0xaee9e8: stur            d0, [x1, #7]
    // 0xaee9ec: ldur            x2, [fp, #-0x38]
    // 0xaee9f0: ubfx            x2, x2, #0, #0x20
    // 0xaee9f4: and             w3, w2, #0xff
    // 0xaee9f8: ubfx            x3, x3, #0, #0x20
    // 0xaee9fc: scvtf           d0, x3
    // 0xaeea00: d1 = 255.000000
    //     0xaeea00: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xaeea04: fdiv            d2, d0, d1
    // 0xaeea08: StoreField: r1->field_f = d2
    //     0xaeea08: stur            d2, [x1, #0xf]
    // 0xaeea0c: ldur            x2, [fp, #-0x30]
    // 0xaeea10: ubfx            x2, x2, #0, #0x20
    // 0xaeea14: and             w3, w2, #0xff
    // 0xaeea18: ubfx            x3, x3, #0, #0x20
    // 0xaeea1c: scvtf           d0, x3
    // 0xaeea20: fdiv            d2, d0, d1
    // 0xaeea24: ArrayStore: r1[0] = d2  ; List_8
    //     0xaeea24: stur            d2, [x1, #0x17]
    // 0xaeea28: ldur            x2, [fp, #-0x28]
    // 0xaeea2c: ubfx            x2, x2, #0, #0x20
    // 0xaeea30: and             w3, w2, #0xff
    // 0xaeea34: ubfx            x3, x3, #0, #0x20
    // 0xaeea38: scvtf           d0, x3
    // 0xaeea3c: fdiv            d2, d0, d1
    // 0xaeea40: StoreField: r1->field_1f = d2
    //     0xaeea40: stur            d2, [x1, #0x1f]
    // 0xaeea44: ldur            x2, [fp, #-0x20]
    // 0xaeea48: cmp             w2, NULL
    // 0xaeea4c: b.ne            #0xaeea58
    // 0xaeea50: r3 = Null
    //     0xaeea50: mov             x3, NULL
    // 0xaeea54: b               #0xaeea60
    // 0xaeea58: LoadField: r3 = r2->field_7
    //     0xaeea58: ldur            w3, [x2, #7]
    // 0xaeea5c: DecompressPointer r3
    //     0xaeea5c: add             x3, x3, HEAP, lsl #32
    // 0xaeea60: cmp             w3, NULL
    // 0xaeea64: b.ne            #0xaeea70
    // 0xaeea68: r3 = 0
    //     0xaeea68: movz            x3, #0
    // 0xaeea6c: b               #0xaeea80
    // 0xaeea70: r4 = LoadInt32Instr(r3)
    //     0xaeea70: sbfx            x4, x3, #1, #0x1f
    //     0xaeea74: tbz             w3, #0, #0xaeea7c
    //     0xaeea78: ldur            x4, [x3, #7]
    // 0xaeea7c: mov             x3, x4
    // 0xaeea80: stur            x3, [fp, #-0x38]
    // 0xaeea84: cmp             w2, NULL
    // 0xaeea88: b.ne            #0xaeea94
    // 0xaeea8c: r4 = Null
    //     0xaeea8c: mov             x4, NULL
    // 0xaeea90: b               #0xaeea9c
    // 0xaeea94: LoadField: r4 = r2->field_b
    //     0xaeea94: ldur            w4, [x2, #0xb]
    // 0xaeea98: DecompressPointer r4
    //     0xaeea98: add             x4, x4, HEAP, lsl #32
    // 0xaeea9c: cmp             w4, NULL
    // 0xaeeaa0: b.ne            #0xaeeaac
    // 0xaeeaa4: r4 = 0
    //     0xaeeaa4: movz            x4, #0
    // 0xaeeaa8: b               #0xaeeabc
    // 0xaeeaac: r5 = LoadInt32Instr(r4)
    //     0xaeeaac: sbfx            x5, x4, #1, #0x1f
    //     0xaeeab0: tbz             w4, #0, #0xaeeab8
    //     0xaeeab4: ldur            x5, [x4, #7]
    // 0xaeeab8: mov             x4, x5
    // 0xaeeabc: stur            x4, [fp, #-0x30]
    // 0xaeeac0: cmp             w2, NULL
    // 0xaeeac4: b.ne            #0xaeead0
    // 0xaeeac8: r2 = Null
    //     0xaeeac8: mov             x2, NULL
    // 0xaeeacc: b               #0xaeeadc
    // 0xaeead0: LoadField: r5 = r2->field_f
    //     0xaeead0: ldur            w5, [x2, #0xf]
    // 0xaeead4: DecompressPointer r5
    //     0xaeead4: add             x5, x5, HEAP, lsl #32
    // 0xaeead8: mov             x2, x5
    // 0xaeeadc: cmp             w2, NULL
    // 0xaeeae0: b.ne            #0xaeeaec
    // 0xaeeae4: r6 = 0
    //     0xaeeae4: movz            x6, #0
    // 0xaeeae8: b               #0xaeeafc
    // 0xaeeaec: r5 = LoadInt32Instr(r2)
    //     0xaeeaec: sbfx            x5, x2, #1, #0x1f
    //     0xaeeaf0: tbz             w2, #0, #0xaeeaf8
    //     0xaeeaf4: ldur            x5, [x2, #7]
    // 0xaeeaf8: mov             x6, x5
    // 0xaeeafc: ldur            x5, [fp, #-0x18]
    // 0xaeeb00: ldur            x2, [fp, #-0x40]
    // 0xaeeb04: stur            x6, [fp, #-0x28]
    // 0xaeeb08: r0 = Color()
    //     0xaeeb08: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xaeeb0c: mov             x3, x0
    // 0xaeeb10: r0 = Instance_ColorSpace
    //     0xaeeb10: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xaeeb14: stur            x3, [fp, #-0x20]
    // 0xaeeb18: StoreField: r3->field_27 = r0
    //     0xaeeb18: stur            w0, [x3, #0x27]
    // 0xaeeb1c: d0 = 0.700000
    //     0xaeeb1c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xaeeb20: ldr             d0, [x17, #0xf48]
    // 0xaeeb24: StoreField: r3->field_7 = d0
    //     0xaeeb24: stur            d0, [x3, #7]
    // 0xaeeb28: ldur            x0, [fp, #-0x38]
    // 0xaeeb2c: ubfx            x0, x0, #0, #0x20
    // 0xaeeb30: and             w1, w0, #0xff
    // 0xaeeb34: ubfx            x1, x1, #0, #0x20
    // 0xaeeb38: scvtf           d0, x1
    // 0xaeeb3c: d1 = 255.000000
    //     0xaeeb3c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xaeeb40: fdiv            d2, d0, d1
    // 0xaeeb44: StoreField: r3->field_f = d2
    //     0xaeeb44: stur            d2, [x3, #0xf]
    // 0xaeeb48: ldur            x0, [fp, #-0x30]
    // 0xaeeb4c: ubfx            x0, x0, #0, #0x20
    // 0xaeeb50: and             w1, w0, #0xff
    // 0xaeeb54: ubfx            x1, x1, #0, #0x20
    // 0xaeeb58: scvtf           d0, x1
    // 0xaeeb5c: fdiv            d2, d0, d1
    // 0xaeeb60: ArrayStore: r3[0] = d2  ; List_8
    //     0xaeeb60: stur            d2, [x3, #0x17]
    // 0xaeeb64: ldur            x0, [fp, #-0x28]
    // 0xaeeb68: ubfx            x0, x0, #0, #0x20
    // 0xaeeb6c: and             w1, w0, #0xff
    // 0xaeeb70: ubfx            x1, x1, #0, #0x20
    // 0xaeeb74: scvtf           d0, x1
    // 0xaeeb78: fdiv            d2, d0, d1
    // 0xaeeb7c: StoreField: r3->field_1f = d2
    //     0xaeeb7c: stur            d2, [x3, #0x1f]
    // 0xaeeb80: r1 = Null
    //     0xaeeb80: mov             x1, NULL
    // 0xaeeb84: r2 = 4
    //     0xaeeb84: movz            x2, #0x4
    // 0xaeeb88: r0 = AllocateArray()
    //     0xaeeb88: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaeeb8c: mov             x2, x0
    // 0xaeeb90: ldur            x0, [fp, #-8]
    // 0xaeeb94: stur            x2, [fp, #-0x48]
    // 0xaeeb98: StoreField: r2->field_f = r0
    //     0xaeeb98: stur            w0, [x2, #0xf]
    // 0xaeeb9c: ldur            x0, [fp, #-0x20]
    // 0xaeeba0: StoreField: r2->field_13 = r0
    //     0xaeeba0: stur            w0, [x2, #0x13]
    // 0xaeeba4: r1 = <Color>
    //     0xaeeba4: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xaeeba8: ldr             x1, [x1, #0xf80]
    // 0xaeebac: r0 = AllocateGrowableArray()
    //     0xaeebac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaeebb0: mov             x1, x0
    // 0xaeebb4: ldur            x0, [fp, #-0x48]
    // 0xaeebb8: stur            x1, [fp, #-8]
    // 0xaeebbc: StoreField: r1->field_f = r0
    //     0xaeebbc: stur            w0, [x1, #0xf]
    // 0xaeebc0: r2 = 4
    //     0xaeebc0: movz            x2, #0x4
    // 0xaeebc4: StoreField: r1->field_b = r2
    //     0xaeebc4: stur            w2, [x1, #0xb]
    // 0xaeebc8: r0 = LinearGradient()
    //     0xaeebc8: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xaeebcc: mov             x1, x0
    // 0xaeebd0: r0 = Instance_Alignment
    //     0xaeebd0: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xaeebd4: ldr             x0, [x0, #0xce0]
    // 0xaeebd8: stur            x1, [fp, #-0x20]
    // 0xaeebdc: StoreField: r1->field_13 = r0
    //     0xaeebdc: stur            w0, [x1, #0x13]
    // 0xaeebe0: r0 = Instance_Alignment
    //     0xaeebe0: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xaeebe4: ldr             x0, [x0, #0xce8]
    // 0xaeebe8: ArrayStore: r1[0] = r0  ; List_4
    //     0xaeebe8: stur            w0, [x1, #0x17]
    // 0xaeebec: r0 = Instance_TileMode
    //     0xaeebec: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xaeebf0: ldr             x0, [x0, #0xcf0]
    // 0xaeebf4: StoreField: r1->field_1b = r0
    //     0xaeebf4: stur            w0, [x1, #0x1b]
    // 0xaeebf8: ldur            x0, [fp, #-8]
    // 0xaeebfc: StoreField: r1->field_7 = r0
    //     0xaeebfc: stur            w0, [x1, #7]
    // 0xaeec00: r0 = BoxDecoration()
    //     0xaeec00: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaeec04: mov             x2, x0
    // 0xaeec08: ldur            x0, [fp, #-0x40]
    // 0xaeec0c: stur            x2, [fp, #-0x48]
    // 0xaeec10: StoreField: r2->field_13 = r0
    //     0xaeec10: stur            w0, [x2, #0x13]
    // 0xaeec14: ldur            x0, [fp, #-0x20]
    // 0xaeec18: StoreField: r2->field_1b = r0
    //     0xaeec18: stur            w0, [x2, #0x1b]
    // 0xaeec1c: r0 = Instance_BoxShape
    //     0xaeec1c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaeec20: ldr             x0, [x0, #0x80]
    // 0xaeec24: StoreField: r2->field_23 = r0
    //     0xaeec24: stur            w0, [x2, #0x23]
    // 0xaeec28: ldur            x3, [fp, #-0x18]
    // 0xaeec2c: LoadField: r0 = r3->field_7f
    //     0xaeec2c: ldur            w0, [x3, #0x7f]
    // 0xaeec30: DecompressPointer r0
    //     0xaeec30: add             x0, x0, HEAP, lsl #32
    // 0xaeec34: cmp             w0, NULL
    // 0xaeec38: b.ne            #0xaeec40
    // 0xaeec3c: r0 = ""
    //     0xaeec3c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaeec40: ldur            x4, [fp, #-0x10]
    // 0xaeec44: stur            x0, [fp, #-8]
    // 0xaeec48: LoadField: r1 = r4->field_f
    //     0xaeec48: ldur            w1, [x4, #0xf]
    // 0xaeec4c: DecompressPointer r1
    //     0xaeec4c: add             x1, x1, HEAP, lsl #32
    // 0xaeec50: cmp             w1, NULL
    // 0xaeec54: b.eq            #0xaef088
    // 0xaeec58: r0 = of()
    //     0xaeec58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaeec5c: LoadField: r1 = r0->field_87
    //     0xaeec5c: ldur            w1, [x0, #0x87]
    // 0xaeec60: DecompressPointer r1
    //     0xaeec60: add             x1, x1, HEAP, lsl #32
    // 0xaeec64: LoadField: r0 = r1->field_7
    //     0xaeec64: ldur            w0, [x1, #7]
    // 0xaeec68: DecompressPointer r0
    //     0xaeec68: add             x0, x0, HEAP, lsl #32
    // 0xaeec6c: r16 = 12.000000
    //     0xaeec6c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaeec70: ldr             x16, [x16, #0x9e8]
    // 0xaeec74: r30 = Instance_Color
    //     0xaeec74: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaeec78: stp             lr, x16, [SP]
    // 0xaeec7c: mov             x1, x0
    // 0xaeec80: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaeec80: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaeec84: ldr             x4, [x4, #0xaa0]
    // 0xaeec88: r0 = copyWith()
    //     0xaeec88: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaeec8c: stur            x0, [fp, #-0x20]
    // 0xaeec90: r0 = Text()
    //     0xaeec90: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaeec94: mov             x1, x0
    // 0xaeec98: ldur            x0, [fp, #-8]
    // 0xaeec9c: stur            x1, [fp, #-0x40]
    // 0xaeeca0: StoreField: r1->field_b = r0
    //     0xaeeca0: stur            w0, [x1, #0xb]
    // 0xaeeca4: ldur            x0, [fp, #-0x20]
    // 0xaeeca8: StoreField: r1->field_13 = r0
    //     0xaeeca8: stur            w0, [x1, #0x13]
    // 0xaeecac: r0 = Instance_TextAlign
    //     0xaeecac: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xaeecb0: StoreField: r1->field_1b = r0
    //     0xaeecb0: stur            w0, [x1, #0x1b]
    // 0xaeecb4: r0 = Padding()
    //     0xaeecb4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaeecb8: mov             x1, x0
    // 0xaeecbc: r0 = Instance_EdgeInsets
    //     0xaeecbc: add             x0, PP, #0x27, lsl #12  ; [pp+0x27850] Obj!EdgeInsets@d57a71
    //     0xaeecc0: ldr             x0, [x0, #0x850]
    // 0xaeecc4: stur            x1, [fp, #-8]
    // 0xaeecc8: StoreField: r1->field_f = r0
    //     0xaeecc8: stur            w0, [x1, #0xf]
    // 0xaeeccc: ldur            x0, [fp, #-0x40]
    // 0xaeecd0: StoreField: r1->field_b = r0
    //     0xaeecd0: stur            w0, [x1, #0xb]
    // 0xaeecd4: r0 = Container()
    //     0xaeecd4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaeecd8: stur            x0, [fp, #-0x20]
    // 0xaeecdc: r16 = 20.000000
    //     0xaeecdc: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xaeece0: ldr             x16, [x16, #0xac8]
    // 0xaeece4: r30 = 120.000000
    //     0xaeece4: add             lr, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0xaeece8: ldr             lr, [lr, #0x3a0]
    // 0xaeecec: stp             lr, x16, [SP, #0x10]
    // 0xaeecf0: ldur            x16, [fp, #-0x48]
    // 0xaeecf4: ldur            lr, [fp, #-8]
    // 0xaeecf8: stp             lr, x16, [SP]
    // 0xaeecfc: mov             x1, x0
    // 0xaeed00: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xaeed00: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xaeed04: ldr             x4, [x4, #0x8c0]
    // 0xaeed08: r0 = Container()
    //     0xaeed08: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaeed0c: r0 = Align()
    //     0xaeed0c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xaeed10: r5 = Instance_Alignment
    //     0xaeed10: add             x5, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xaeed14: ldr             x5, [x5, #0xfa0]
    // 0xaeed18: stur            x0, [fp, #-8]
    // 0xaeed1c: StoreField: r0->field_f = r5
    //     0xaeed1c: stur            w5, [x0, #0xf]
    // 0xaeed20: ldur            x1, [fp, #-0x20]
    // 0xaeed24: StoreField: r0->field_b = r1
    //     0xaeed24: stur            w1, [x0, #0xb]
    // 0xaeed28: r0 = Padding()
    //     0xaeed28: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaeed2c: mov             x1, x0
    // 0xaeed30: r0 = Instance_EdgeInsets
    //     0xaeed30: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xaeed34: ldr             x0, [x0, #0xe50]
    // 0xaeed38: StoreField: r1->field_f = r0
    //     0xaeed38: stur            w0, [x1, #0xf]
    // 0xaeed3c: ldur            x0, [fp, #-8]
    // 0xaeed40: StoreField: r1->field_b = r0
    //     0xaeed40: stur            w0, [x1, #0xb]
    // 0xaeed44: mov             x0, x1
    // 0xaeed48: b               #0xaef070
    // 0xaeed4c: mov             x4, x1
    // 0xaeed50: mov             x3, x2
    // 0xaeed54: r5 = Instance_Alignment
    //     0xaeed54: add             x5, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xaeed58: ldr             x5, [x5, #0xfa0]
    // 0xaeed5c: r2 = 4
    //     0xaeed5c: movz            x2, #0x4
    // 0xaeed60: r0 = Instance_BoxShape
    //     0xaeed60: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaeed64: ldr             x0, [x0, #0x80]
    // 0xaeed68: LoadField: r1 = r4->field_f
    //     0xaeed68: ldur            w1, [x4, #0xf]
    // 0xaeed6c: DecompressPointer r1
    //     0xaeed6c: add             x1, x1, HEAP, lsl #32
    // 0xaeed70: cmp             w1, NULL
    // 0xaeed74: b.eq            #0xaef08c
    // 0xaeed78: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaeed78: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaeed7c: r0 = _of()
    //     0xaeed7c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xaeed80: LoadField: r1 = r0->field_7
    //     0xaeed80: ldur            w1, [x0, #7]
    // 0xaeed84: DecompressPointer r1
    //     0xaeed84: add             x1, x1, HEAP, lsl #32
    // 0xaeed88: LoadField: d0 = r1->field_7
    //     0xaeed88: ldur            d0, [x1, #7]
    // 0xaeed8c: d1 = 0.370000
    //     0xaeed8c: add             x17, PP, #0x52, lsl #12  ; [pp+0x52e40] IMM: double(0.37) from 0x3fd7ae147ae147ae
    //     0xaeed90: ldr             d1, [x17, #0xe40]
    // 0xaeed94: fmul            d2, d0, d1
    // 0xaeed98: stur            d2, [fp, #-0x50]
    // 0xaeed9c: r1 = Instance_Color
    //     0xaeed9c: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaeeda0: d0 = 0.900000
    //     0xaeeda0: ldr             d0, [PP, #0x5a78]  ; [pp+0x5a78] IMM: double(0.9) from 0x3feccccccccccccd
    // 0xaeeda4: r0 = withOpacity()
    //     0xaeeda4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaeeda8: stur            x0, [fp, #-8]
    // 0xaeedac: r0 = Radius()
    //     0xaeedac: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaeedb0: d0 = 4.000000
    //     0xaeedb0: fmov            d0, #4.00000000
    // 0xaeedb4: stur            x0, [fp, #-0x20]
    // 0xaeedb8: StoreField: r0->field_7 = d0
    //     0xaeedb8: stur            d0, [x0, #7]
    // 0xaeedbc: StoreField: r0->field_f = d0
    //     0xaeedbc: stur            d0, [x0, #0xf]
    // 0xaeedc0: r0 = BorderRadius()
    //     0xaeedc0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaeedc4: mov             x1, x0
    // 0xaeedc8: ldur            x0, [fp, #-0x20]
    // 0xaeedcc: stur            x1, [fp, #-0x40]
    // 0xaeedd0: StoreField: r1->field_7 = r0
    //     0xaeedd0: stur            w0, [x1, #7]
    // 0xaeedd4: StoreField: r1->field_b = r0
    //     0xaeedd4: stur            w0, [x1, #0xb]
    // 0xaeedd8: StoreField: r1->field_f = r0
    //     0xaeedd8: stur            w0, [x1, #0xf]
    // 0xaeeddc: StoreField: r1->field_13 = r0
    //     0xaeeddc: stur            w0, [x1, #0x13]
    // 0xaeede0: r0 = BoxDecoration()
    //     0xaeede0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaeede4: mov             x2, x0
    // 0xaeede8: ldur            x0, [fp, #-8]
    // 0xaeedec: stur            x2, [fp, #-0x20]
    // 0xaeedf0: StoreField: r2->field_7 = r0
    //     0xaeedf0: stur            w0, [x2, #7]
    // 0xaeedf4: ldur            x0, [fp, #-0x40]
    // 0xaeedf8: StoreField: r2->field_13 = r0
    //     0xaeedf8: stur            w0, [x2, #0x13]
    // 0xaeedfc: r0 = Instance_BoxShape
    //     0xaeedfc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaeee00: ldr             x0, [x0, #0x80]
    // 0xaeee04: StoreField: r2->field_23 = r0
    //     0xaeee04: stur            w0, [x2, #0x23]
    // 0xaeee08: ldur            x0, [fp, #-0x10]
    // 0xaeee0c: LoadField: r1 = r0->field_f
    //     0xaeee0c: ldur            w1, [x0, #0xf]
    // 0xaeee10: DecompressPointer r1
    //     0xaeee10: add             x1, x1, HEAP, lsl #32
    // 0xaeee14: cmp             w1, NULL
    // 0xaeee18: b.eq            #0xaef090
    // 0xaeee1c: r0 = of()
    //     0xaeee1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaeee20: LoadField: r1 = r0->field_5b
    //     0xaeee20: ldur            w1, [x0, #0x5b]
    // 0xaeee24: DecompressPointer r1
    //     0xaeee24: add             x1, x1, HEAP, lsl #32
    // 0xaeee28: stur            x1, [fp, #-8]
    // 0xaeee2c: r0 = ColorFilter()
    //     0xaeee2c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xaeee30: mov             x1, x0
    // 0xaeee34: ldur            x0, [fp, #-8]
    // 0xaeee38: stur            x1, [fp, #-0x40]
    // 0xaeee3c: StoreField: r1->field_7 = r0
    //     0xaeee3c: stur            w0, [x1, #7]
    // 0xaeee40: r0 = Instance_BlendMode
    //     0xaeee40: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xaeee44: ldr             x0, [x0, #0xb30]
    // 0xaeee48: StoreField: r1->field_b = r0
    //     0xaeee48: stur            w0, [x1, #0xb]
    // 0xaeee4c: r0 = 1
    //     0xaeee4c: movz            x0, #0x1
    // 0xaeee50: StoreField: r1->field_13 = r0
    //     0xaeee50: stur            x0, [x1, #0x13]
    // 0xaeee54: r0 = SvgPicture()
    //     0xaeee54: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xaeee58: stur            x0, [fp, #-8]
    // 0xaeee5c: ldur            x16, [fp, #-0x40]
    // 0xaeee60: str             x16, [SP]
    // 0xaeee64: mov             x1, x0
    // 0xaeee68: r2 = "assets/images/bumper_coupon.svg"
    //     0xaeee68: add             x2, PP, #0x52, lsl #12  ; [pp+0x52e48] "assets/images/bumper_coupon.svg"
    //     0xaeee6c: ldr             x2, [x2, #0xe48]
    // 0xaeee70: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xaeee70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xaeee74: ldr             x4, [x4, #0xa38]
    // 0xaeee78: r0 = SvgPicture.asset()
    //     0xaeee78: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xaeee7c: ldur            x0, [fp, #-0x18]
    // 0xaeee80: LoadField: r1 = r0->field_7f
    //     0xaeee80: ldur            w1, [x0, #0x7f]
    // 0xaeee84: DecompressPointer r1
    //     0xaeee84: add             x1, x1, HEAP, lsl #32
    // 0xaeee88: cmp             w1, NULL
    // 0xaeee8c: b.ne            #0xaeee98
    // 0xaeee90: r2 = ""
    //     0xaeee90: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaeee94: b               #0xaeee9c
    // 0xaeee98: mov             x2, x1
    // 0xaeee9c: ldur            x1, [fp, #-0x10]
    // 0xaeeea0: ldur            d0, [fp, #-0x50]
    // 0xaeeea4: ldur            x0, [fp, #-8]
    // 0xaeeea8: stur            x2, [fp, #-0x18]
    // 0xaeeeac: LoadField: r3 = r1->field_f
    //     0xaeeeac: ldur            w3, [x1, #0xf]
    // 0xaeeeb0: DecompressPointer r3
    //     0xaeeeb0: add             x3, x3, HEAP, lsl #32
    // 0xaeeeb4: cmp             w3, NULL
    // 0xaeeeb8: b.eq            #0xaef094
    // 0xaeeebc: mov             x1, x3
    // 0xaeeec0: r0 = of()
    //     0xaeeec0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaeeec4: LoadField: r1 = r0->field_87
    //     0xaeeec4: ldur            w1, [x0, #0x87]
    // 0xaeeec8: DecompressPointer r1
    //     0xaeeec8: add             x1, x1, HEAP, lsl #32
    // 0xaeeecc: LoadField: r0 = r1->field_2b
    //     0xaeeecc: ldur            w0, [x1, #0x2b]
    // 0xaeeed0: DecompressPointer r0
    //     0xaeeed0: add             x0, x0, HEAP, lsl #32
    // 0xaeeed4: r16 = 12.000000
    //     0xaeeed4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaeeed8: ldr             x16, [x16, #0x9e8]
    // 0xaeeedc: r30 = Instance_Color
    //     0xaeeedc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaeeee0: stp             lr, x16, [SP]
    // 0xaeeee4: mov             x1, x0
    // 0xaeeee8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaeeee8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaeeeec: ldr             x4, [x4, #0xaa0]
    // 0xaeeef0: r0 = copyWith()
    //     0xaeeef0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaeeef4: stur            x0, [fp, #-0x10]
    // 0xaeeef8: r0 = Text()
    //     0xaeeef8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaeeefc: mov             x1, x0
    // 0xaeef00: ldur            x0, [fp, #-0x18]
    // 0xaeef04: stur            x1, [fp, #-0x40]
    // 0xaeef08: StoreField: r1->field_b = r0
    //     0xaeef08: stur            w0, [x1, #0xb]
    // 0xaeef0c: ldur            x0, [fp, #-0x10]
    // 0xaeef10: StoreField: r1->field_13 = r0
    //     0xaeef10: stur            w0, [x1, #0x13]
    // 0xaeef14: r0 = Padding()
    //     0xaeef14: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaeef18: mov             x3, x0
    // 0xaeef1c: r0 = Instance_EdgeInsets
    //     0xaeef1c: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xaeef20: ldr             x0, [x0, #0xc40]
    // 0xaeef24: stur            x3, [fp, #-0x10]
    // 0xaeef28: StoreField: r3->field_f = r0
    //     0xaeef28: stur            w0, [x3, #0xf]
    // 0xaeef2c: ldur            x0, [fp, #-0x40]
    // 0xaeef30: StoreField: r3->field_b = r0
    //     0xaeef30: stur            w0, [x3, #0xb]
    // 0xaeef34: r1 = Null
    //     0xaeef34: mov             x1, NULL
    // 0xaeef38: r2 = 4
    //     0xaeef38: movz            x2, #0x4
    // 0xaeef3c: r0 = AllocateArray()
    //     0xaeef3c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaeef40: mov             x2, x0
    // 0xaeef44: ldur            x0, [fp, #-8]
    // 0xaeef48: stur            x2, [fp, #-0x18]
    // 0xaeef4c: StoreField: r2->field_f = r0
    //     0xaeef4c: stur            w0, [x2, #0xf]
    // 0xaeef50: ldur            x0, [fp, #-0x10]
    // 0xaeef54: StoreField: r2->field_13 = r0
    //     0xaeef54: stur            w0, [x2, #0x13]
    // 0xaeef58: r1 = <Widget>
    //     0xaeef58: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaeef5c: r0 = AllocateGrowableArray()
    //     0xaeef5c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaeef60: mov             x1, x0
    // 0xaeef64: ldur            x0, [fp, #-0x18]
    // 0xaeef68: stur            x1, [fp, #-8]
    // 0xaeef6c: StoreField: r1->field_f = r0
    //     0xaeef6c: stur            w0, [x1, #0xf]
    // 0xaeef70: r0 = 4
    //     0xaeef70: movz            x0, #0x4
    // 0xaeef74: StoreField: r1->field_b = r0
    //     0xaeef74: stur            w0, [x1, #0xb]
    // 0xaeef78: r0 = Row()
    //     0xaeef78: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaeef7c: mov             x1, x0
    // 0xaeef80: r0 = Instance_Axis
    //     0xaeef80: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaeef84: stur            x1, [fp, #-0x10]
    // 0xaeef88: StoreField: r1->field_f = r0
    //     0xaeef88: stur            w0, [x1, #0xf]
    // 0xaeef8c: r0 = Instance_MainAxisAlignment
    //     0xaeef8c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaeef90: ldr             x0, [x0, #0xa08]
    // 0xaeef94: StoreField: r1->field_13 = r0
    //     0xaeef94: stur            w0, [x1, #0x13]
    // 0xaeef98: r0 = Instance_MainAxisSize
    //     0xaeef98: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaeef9c: ldr             x0, [x0, #0xa10]
    // 0xaeefa0: ArrayStore: r1[0] = r0  ; List_4
    //     0xaeefa0: stur            w0, [x1, #0x17]
    // 0xaeefa4: r0 = Instance_CrossAxisAlignment
    //     0xaeefa4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaeefa8: ldr             x0, [x0, #0xa18]
    // 0xaeefac: StoreField: r1->field_1b = r0
    //     0xaeefac: stur            w0, [x1, #0x1b]
    // 0xaeefb0: r0 = Instance_VerticalDirection
    //     0xaeefb0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaeefb4: ldr             x0, [x0, #0xa20]
    // 0xaeefb8: StoreField: r1->field_23 = r0
    //     0xaeefb8: stur            w0, [x1, #0x23]
    // 0xaeefbc: r0 = Instance_Clip
    //     0xaeefbc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaeefc0: ldr             x0, [x0, #0x38]
    // 0xaeefc4: StoreField: r1->field_2b = r0
    //     0xaeefc4: stur            w0, [x1, #0x2b]
    // 0xaeefc8: StoreField: r1->field_2f = rZR
    //     0xaeefc8: stur            xzr, [x1, #0x2f]
    // 0xaeefcc: ldur            x0, [fp, #-8]
    // 0xaeefd0: StoreField: r1->field_b = r0
    //     0xaeefd0: stur            w0, [x1, #0xb]
    // 0xaeefd4: ldur            d0, [fp, #-0x50]
    // 0xaeefd8: r0 = inline_Allocate_Double()
    //     0xaeefd8: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xaeefdc: add             x0, x0, #0x10
    //     0xaeefe0: cmp             x2, x0
    //     0xaeefe4: b.ls            #0xaef098
    //     0xaeefe8: str             x0, [THR, #0x50]  ; THR::top
    //     0xaeefec: sub             x0, x0, #0xf
    //     0xaeeff0: movz            x2, #0xe15c
    //     0xaeeff4: movk            x2, #0x3, lsl #16
    //     0xaeeff8: stur            x2, [x0, #-1]
    // 0xaeeffc: StoreField: r0->field_7 = d0
    //     0xaeeffc: stur            d0, [x0, #7]
    // 0xaef000: stur            x0, [fp, #-8]
    // 0xaef004: r0 = Container()
    //     0xaef004: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaef008: stur            x0, [fp, #-0x18]
    // 0xaef00c: r16 = 20.000000
    //     0xaef00c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xaef010: ldr             x16, [x16, #0xac8]
    // 0xaef014: ldur            lr, [fp, #-8]
    // 0xaef018: stp             lr, x16, [SP, #0x10]
    // 0xaef01c: ldur            x16, [fp, #-0x20]
    // 0xaef020: ldur            lr, [fp, #-0x10]
    // 0xaef024: stp             lr, x16, [SP]
    // 0xaef028: mov             x1, x0
    // 0xaef02c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xaef02c: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xaef030: ldr             x4, [x4, #0x8c0]
    // 0xaef034: r0 = Container()
    //     0xaef034: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaef038: r0 = Padding()
    //     0xaef038: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaef03c: mov             x1, x0
    // 0xaef040: r0 = Instance_EdgeInsets
    //     0xaef040: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xaef044: ldr             x0, [x0, #0xf70]
    // 0xaef048: stur            x1, [fp, #-8]
    // 0xaef04c: StoreField: r1->field_f = r0
    //     0xaef04c: stur            w0, [x1, #0xf]
    // 0xaef050: ldur            x0, [fp, #-0x18]
    // 0xaef054: StoreField: r1->field_b = r0
    //     0xaef054: stur            w0, [x1, #0xb]
    // 0xaef058: r0 = Align()
    //     0xaef058: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xaef05c: r1 = Instance_Alignment
    //     0xaef05c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xaef060: ldr             x1, [x1, #0xfa0]
    // 0xaef064: StoreField: r0->field_f = r1
    //     0xaef064: stur            w1, [x0, #0xf]
    // 0xaef068: ldur            x1, [fp, #-8]
    // 0xaef06c: StoreField: r0->field_b = r1
    //     0xaef06c: stur            w1, [x0, #0xb]
    // 0xaef070: LeaveFrame
    //     0xaef070: mov             SP, fp
    //     0xaef074: ldp             fp, lr, [SP], #0x10
    // 0xaef078: ret
    //     0xaef078: ret             
    // 0xaef07c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaef07c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaef080: b               #0xaee884
    // 0xaef084: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaef084: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaef088: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaef088: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaef08c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaef08c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaef090: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaef090: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaef094: r0 = NullCastErrorSharedWithFPURegs()
    //     0xaef094: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xaef098: SaveReg d0
    //     0xaef098: str             q0, [SP, #-0x10]!
    // 0xaef09c: SaveReg r1
    //     0xaef09c: str             x1, [SP, #-8]!
    // 0xaef0a0: r0 = AllocateDouble()
    //     0xaef0a0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaef0a4: RestoreReg r1
    //     0xaef0a4: ldr             x1, [SP], #8
    // 0xaef0a8: RestoreReg d0
    //     0xaef0a8: ldr             q0, [SP], #0x10
    // 0xaef0ac: b               #0xaeeffc
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xaef0b0, size: 0x84
    // 0xaef0b0: EnterFrame
    //     0xaef0b0: stp             fp, lr, [SP, #-0x10]!
    //     0xaef0b4: mov             fp, SP
    // 0xaef0b8: AllocStack(0x10)
    //     0xaef0b8: sub             SP, SP, #0x10
    // 0xaef0bc: SetupParameters()
    //     0xaef0bc: ldr             x0, [fp, #0x18]
    //     0xaef0c0: ldur            w1, [x0, #0x17]
    //     0xaef0c4: add             x1, x1, HEAP, lsl #32
    //     0xaef0c8: stur            x1, [fp, #-8]
    // 0xaef0cc: CheckStackOverflow
    //     0xaef0cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaef0d0: cmp             SP, x16
    //     0xaef0d4: b.ls            #0xaef12c
    // 0xaef0d8: r1 = 1
    //     0xaef0d8: movz            x1, #0x1
    // 0xaef0dc: r0 = AllocateContext()
    //     0xaef0dc: bl              #0x16f6108  ; AllocateContextStub
    // 0xaef0e0: mov             x1, x0
    // 0xaef0e4: ldur            x0, [fp, #-8]
    // 0xaef0e8: StoreField: r1->field_b = r0
    //     0xaef0e8: stur            w0, [x1, #0xb]
    // 0xaef0ec: ldr             x2, [fp, #0x10]
    // 0xaef0f0: StoreField: r1->field_f = r2
    //     0xaef0f0: stur            w2, [x1, #0xf]
    // 0xaef0f4: LoadField: r3 = r0->field_f
    //     0xaef0f4: ldur            w3, [x0, #0xf]
    // 0xaef0f8: DecompressPointer r3
    //     0xaef0f8: add             x3, x3, HEAP, lsl #32
    // 0xaef0fc: mov             x2, x1
    // 0xaef100: stur            x3, [fp, #-0x10]
    // 0xaef104: r1 = Function '<anonymous closure>':.
    //     0xaef104: add             x1, PP, #0x58, lsl #12  ; [pp+0x58210] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xaef108: ldr             x1, [x1, #0x210]
    // 0xaef10c: r0 = AllocateClosure()
    //     0xaef10c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaef110: ldur            x1, [fp, #-0x10]
    // 0xaef114: mov             x2, x0
    // 0xaef118: r0 = setState()
    //     0xaef118: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaef11c: r0 = Null
    //     0xaef11c: mov             x0, NULL
    // 0xaef120: LeaveFrame
    //     0xaef120: mov             SP, fp
    //     0xaef124: ldp             fp, lr, [SP], #0x10
    // 0xaef128: ret
    //     0xaef128: ret             
    // 0xaef12c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaef12c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaef130: b               #0xaef0d8
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xaef134, size: 0x84
    // 0xaef134: EnterFrame
    //     0xaef134: stp             fp, lr, [SP, #-0x10]!
    //     0xaef138: mov             fp, SP
    // 0xaef13c: AllocStack(0x10)
    //     0xaef13c: sub             SP, SP, #0x10
    // 0xaef140: SetupParameters()
    //     0xaef140: ldr             x0, [fp, #0x18]
    //     0xaef144: ldur            w1, [x0, #0x17]
    //     0xaef148: add             x1, x1, HEAP, lsl #32
    //     0xaef14c: stur            x1, [fp, #-8]
    // 0xaef150: CheckStackOverflow
    //     0xaef150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaef154: cmp             SP, x16
    //     0xaef158: b.ls            #0xaef1b0
    // 0xaef15c: r1 = 1
    //     0xaef15c: movz            x1, #0x1
    // 0xaef160: r0 = AllocateContext()
    //     0xaef160: bl              #0x16f6108  ; AllocateContextStub
    // 0xaef164: mov             x1, x0
    // 0xaef168: ldur            x0, [fp, #-8]
    // 0xaef16c: StoreField: r1->field_b = r0
    //     0xaef16c: stur            w0, [x1, #0xb]
    // 0xaef170: ldr             x2, [fp, #0x10]
    // 0xaef174: StoreField: r1->field_f = r2
    //     0xaef174: stur            w2, [x1, #0xf]
    // 0xaef178: LoadField: r3 = r0->field_f
    //     0xaef178: ldur            w3, [x0, #0xf]
    // 0xaef17c: DecompressPointer r3
    //     0xaef17c: add             x3, x3, HEAP, lsl #32
    // 0xaef180: mov             x2, x1
    // 0xaef184: stur            x3, [fp, #-0x10]
    // 0xaef188: r1 = Function '<anonymous closure>':.
    //     0xaef188: add             x1, PP, #0x58, lsl #12  ; [pp+0x58230] AnonymousClosure: (0xa59828), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildMediaCarousel (0xa598e8)
    //     0xaef18c: ldr             x1, [x1, #0x230]
    // 0xaef190: r0 = AllocateClosure()
    //     0xaef190: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaef194: ldur            x1, [fp, #-0x10]
    // 0xaef198: mov             x2, x0
    // 0xaef19c: r0 = setState()
    //     0xaef19c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaef1a0: r0 = Null
    //     0xaef1a0: mov             x0, NULL
    // 0xaef1a4: LeaveFrame
    //     0xaef1a4: mov             SP, fp
    //     0xaef1a8: ldp             fp, lr, [SP], #0x10
    // 0xaef1ac: ret
    //     0xaef1ac: ret             
    // 0xaef1b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaef1b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaef1b4: b               #0xaef15c
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc873f0, size: 0x84
    // 0xc873f0: EnterFrame
    //     0xc873f0: stp             fp, lr, [SP, #-0x10]!
    //     0xc873f4: mov             fp, SP
    // 0xc873f8: AllocStack(0x8)
    //     0xc873f8: sub             SP, SP, #8
    // 0xc873fc: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xc873fc: mov             x0, x1
    //     0xc87400: stur            x1, [fp, #-8]
    // 0xc87404: CheckStackOverflow
    //     0xc87404: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87408: cmp             SP, x16
    //     0xc8740c: b.ls            #0xc87454
    // 0xc87410: LoadField: r1 = r0->field_13
    //     0xc87410: ldur            w1, [x0, #0x13]
    // 0xc87414: DecompressPointer r1
    //     0xc87414: add             x1, x1, HEAP, lsl #32
    // 0xc87418: r16 = Sentinel
    //     0xc87418: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc8741c: cmp             w1, w16
    // 0xc87420: b.eq            #0xc8745c
    // 0xc87424: r0 = dispose()
    //     0xc87424: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc87428: ldur            x0, [fp, #-8]
    // 0xc8742c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc8742c: ldur            w1, [x0, #0x17]
    // 0xc87430: DecompressPointer r1
    //     0xc87430: add             x1, x1, HEAP, lsl #32
    // 0xc87434: r16 = Sentinel
    //     0xc87434: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87438: cmp             w1, w16
    // 0xc8743c: b.eq            #0xc87468
    // 0xc87440: r0 = dispose()
    //     0xc87440: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc87444: r0 = Null
    //     0xc87444: mov             x0, NULL
    // 0xc87448: LeaveFrame
    //     0xc87448: mov             SP, fp
    //     0xc8744c: ldp             fp, lr, [SP], #0x10
    // 0xc87450: ret
    //     0xc87450: ret             
    // 0xc87454: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87454: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87458: b               #0xc87410
    // 0xc8745c: r9 = _pageController
    //     0xc8745c: add             x9, PP, #0x58, lsl #12  ; [pp+0x58188] Field <_ProductGroupCarouselItemViewState@1467114462._pageController@1467114462>: late (offset: 0x14)
    //     0xc87460: ldr             x9, [x9, #0x188]
    // 0xc87464: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87464: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc87468: r9 = _imagePageController
    //     0xc87468: add             x9, PP, #0x58, lsl #12  ; [pp+0x581d0] Field <_ProductGroupCarouselItemViewState@1467114462._imagePageController@1467114462>: late (offset: 0x18)
    //     0xc8746c: ldr             x9, [x9, #0x1d0]
    // 0xc87470: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87470: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4159, size: 0x4c, field offset: 0xc
//   const constructor, 
class ProductGroupCarouselItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7daf4, size: 0x34
    // 0xc7daf4: EnterFrame
    //     0xc7daf4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7daf8: mov             fp, SP
    // 0xc7dafc: mov             x0, x1
    // 0xc7db00: r1 = <ProductGroupCarouselItemView>
    //     0xc7db00: add             x1, PP, #0x48, lsl #12  ; [pp+0x48bd0] TypeArguments: <ProductGroupCarouselItemView>
    //     0xc7db04: ldr             x1, [x1, #0xbd0]
    // 0xc7db08: r0 = _ProductGroupCarouselItemViewState()
    //     0xc7db08: bl              #0xc7db28  ; Allocate_ProductGroupCarouselItemViewStateStub -> _ProductGroupCarouselItemViewState (size=0x24)
    // 0xc7db0c: r1 = Sentinel
    //     0xc7db0c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7db10: StoreField: r0->field_13 = r1
    //     0xc7db10: stur            w1, [x0, #0x13]
    // 0xc7db14: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7db14: stur            w1, [x0, #0x17]
    // 0xc7db18: StoreField: r0->field_1b = rZR
    //     0xc7db18: stur            xzr, [x0, #0x1b]
    // 0xc7db1c: LeaveFrame
    //     0xc7db1c: mov             SP, fp
    //     0xc7db20: ldp             fp, lr, [SP], #0x10
    // 0xc7db24: ret
    //     0xc7db24: ret             
  }
}
