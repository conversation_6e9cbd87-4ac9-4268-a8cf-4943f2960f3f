// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/view_size_chat.dart

// class id: 1049575, size: 0x8
class :: {
}

// class id: 3210, size: 0x14, field offset: 0x14
class _ViewSizeChartState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xc11b78, size: 0x3ec
    // 0xc11b78: EnterFrame
    //     0xc11b78: stp             fp, lr, [SP, #-0x10]!
    //     0xc11b7c: mov             fp, SP
    // 0xc11b80: AllocStack(0x38)
    //     0xc11b80: sub             SP, SP, #0x38
    // 0xc11b84: SetupParameters(_ViewSizeChartState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc11b84: mov             x0, x2
    //     0xc11b88: stur            x2, [fp, #-0x10]
    //     0xc11b8c: mov             x2, x1
    //     0xc11b90: stur            x1, [fp, #-8]
    // 0xc11b94: CheckStackOverflow
    //     0xc11b94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc11b98: cmp             SP, x16
    //     0xc11b9c: b.ls            #0xc11f58
    // 0xc11ba0: mov             x1, x0
    // 0xc11ba4: r0 = of()
    //     0xc11ba4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc11ba8: LoadField: r1 = r0->field_87
    //     0xc11ba8: ldur            w1, [x0, #0x87]
    // 0xc11bac: DecompressPointer r1
    //     0xc11bac: add             x1, x1, HEAP, lsl #32
    // 0xc11bb0: LoadField: r0 = r1->field_27
    //     0xc11bb0: ldur            w0, [x1, #0x27]
    // 0xc11bb4: DecompressPointer r0
    //     0xc11bb4: add             x0, x0, HEAP, lsl #32
    // 0xc11bb8: r16 = Instance_FontWeight
    //     0xc11bb8: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xc11bbc: ldr             x16, [x16, #0x20]
    // 0xc11bc0: r30 = 22.000000
    //     0xc11bc0: add             lr, PP, #0x51, lsl #12  ; [pp+0x510a0] 22
    //     0xc11bc4: ldr             lr, [lr, #0xa0]
    // 0xc11bc8: stp             lr, x16, [SP]
    // 0xc11bcc: mov             x1, x0
    // 0xc11bd0: r4 = const [0, 0x3, 0x2, 0x1, fontSize, 0x2, fontWeight, 0x1, null]
    //     0xc11bd0: add             x4, PP, #0x53, lsl #12  ; [pp+0x53dc8] List(9) [0, 0x3, 0x2, 0x1, "fontSize", 0x2, "fontWeight", 0x1, Null]
    //     0xc11bd4: ldr             x4, [x4, #0xdc8]
    // 0xc11bd8: r0 = copyWith()
    //     0xc11bd8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc11bdc: stur            x0, [fp, #-0x18]
    // 0xc11be0: r0 = Text()
    //     0xc11be0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc11be4: mov             x2, x0
    // 0xc11be8: r0 = "SIZE GUIDE"
    //     0xc11be8: add             x0, PP, #0x53, lsl #12  ; [pp+0x53dd0] "SIZE GUIDE"
    //     0xc11bec: ldr             x0, [x0, #0xdd0]
    // 0xc11bf0: stur            x2, [fp, #-0x20]
    // 0xc11bf4: StoreField: r2->field_b = r0
    //     0xc11bf4: stur            w0, [x2, #0xb]
    // 0xc11bf8: ldur            x0, [fp, #-0x18]
    // 0xc11bfc: StoreField: r2->field_13 = r0
    //     0xc11bfc: stur            w0, [x2, #0x13]
    // 0xc11c00: ldur            x1, [fp, #-0x10]
    // 0xc11c04: r0 = of()
    //     0xc11c04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc11c08: LoadField: r1 = r0->field_5b
    //     0xc11c08: ldur            w1, [x0, #0x5b]
    // 0xc11c0c: DecompressPointer r1
    //     0xc11c0c: add             x1, x1, HEAP, lsl #32
    // 0xc11c10: stur            x1, [fp, #-0x18]
    // 0xc11c14: r0 = Icon()
    //     0xc11c14: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xc11c18: mov             x1, x0
    // 0xc11c1c: r0 = Instance_IconData
    //     0xc11c1c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53dd8] Obj!IconData@d55661
    //     0xc11c20: ldr             x0, [x0, #0xdd8]
    // 0xc11c24: stur            x1, [fp, #-0x28]
    // 0xc11c28: StoreField: r1->field_b = r0
    //     0xc11c28: stur            w0, [x1, #0xb]
    // 0xc11c2c: ldur            x0, [fp, #-0x18]
    // 0xc11c30: StoreField: r1->field_23 = r0
    //     0xc11c30: stur            w0, [x1, #0x23]
    // 0xc11c34: r0 = InkWell()
    //     0xc11c34: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc11c38: mov             x3, x0
    // 0xc11c3c: ldur            x0, [fp, #-0x28]
    // 0xc11c40: stur            x3, [fp, #-0x18]
    // 0xc11c44: StoreField: r3->field_b = r0
    //     0xc11c44: stur            w0, [x3, #0xb]
    // 0xc11c48: r1 = Function '<anonymous closure>':.
    //     0xc11c48: add             x1, PP, #0x53, lsl #12  ; [pp+0x53de0] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc11c4c: ldr             x1, [x1, #0xde0]
    // 0xc11c50: r2 = Null
    //     0xc11c50: mov             x2, NULL
    // 0xc11c54: r0 = AllocateClosure()
    //     0xc11c54: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc11c58: mov             x1, x0
    // 0xc11c5c: ldur            x0, [fp, #-0x18]
    // 0xc11c60: StoreField: r0->field_f = r1
    //     0xc11c60: stur            w1, [x0, #0xf]
    // 0xc11c64: r3 = true
    //     0xc11c64: add             x3, NULL, #0x20  ; true
    // 0xc11c68: StoreField: r0->field_43 = r3
    //     0xc11c68: stur            w3, [x0, #0x43]
    // 0xc11c6c: r1 = Instance_BoxShape
    //     0xc11c6c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc11c70: ldr             x1, [x1, #0x80]
    // 0xc11c74: StoreField: r0->field_47 = r1
    //     0xc11c74: stur            w1, [x0, #0x47]
    // 0xc11c78: StoreField: r0->field_6f = r3
    //     0xc11c78: stur            w3, [x0, #0x6f]
    // 0xc11c7c: r4 = false
    //     0xc11c7c: add             x4, NULL, #0x30  ; false
    // 0xc11c80: StoreField: r0->field_73 = r4
    //     0xc11c80: stur            w4, [x0, #0x73]
    // 0xc11c84: StoreField: r0->field_83 = r3
    //     0xc11c84: stur            w3, [x0, #0x83]
    // 0xc11c88: StoreField: r0->field_7b = r4
    //     0xc11c88: stur            w4, [x0, #0x7b]
    // 0xc11c8c: r1 = Null
    //     0xc11c8c: mov             x1, NULL
    // 0xc11c90: r2 = 4
    //     0xc11c90: movz            x2, #0x4
    // 0xc11c94: r0 = AllocateArray()
    //     0xc11c94: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc11c98: mov             x2, x0
    // 0xc11c9c: ldur            x0, [fp, #-0x20]
    // 0xc11ca0: stur            x2, [fp, #-0x28]
    // 0xc11ca4: StoreField: r2->field_f = r0
    //     0xc11ca4: stur            w0, [x2, #0xf]
    // 0xc11ca8: ldur            x0, [fp, #-0x18]
    // 0xc11cac: StoreField: r2->field_13 = r0
    //     0xc11cac: stur            w0, [x2, #0x13]
    // 0xc11cb0: r1 = <Widget>
    //     0xc11cb0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc11cb4: r0 = AllocateGrowableArray()
    //     0xc11cb4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc11cb8: mov             x1, x0
    // 0xc11cbc: ldur            x0, [fp, #-0x28]
    // 0xc11cc0: stur            x1, [fp, #-0x18]
    // 0xc11cc4: StoreField: r1->field_f = r0
    //     0xc11cc4: stur            w0, [x1, #0xf]
    // 0xc11cc8: r0 = 4
    //     0xc11cc8: movz            x0, #0x4
    // 0xc11ccc: StoreField: r1->field_b = r0
    //     0xc11ccc: stur            w0, [x1, #0xb]
    // 0xc11cd0: r0 = Row()
    //     0xc11cd0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc11cd4: mov             x1, x0
    // 0xc11cd8: r0 = Instance_Axis
    //     0xc11cd8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc11cdc: stur            x1, [fp, #-0x20]
    // 0xc11ce0: StoreField: r1->field_f = r0
    //     0xc11ce0: stur            w0, [x1, #0xf]
    // 0xc11ce4: r0 = Instance_MainAxisAlignment
    //     0xc11ce4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xc11ce8: ldr             x0, [x0, #0xa8]
    // 0xc11cec: StoreField: r1->field_13 = r0
    //     0xc11cec: stur            w0, [x1, #0x13]
    // 0xc11cf0: r0 = Instance_MainAxisSize
    //     0xc11cf0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc11cf4: ldr             x0, [x0, #0xa10]
    // 0xc11cf8: ArrayStore: r1[0] = r0  ; List_4
    //     0xc11cf8: stur            w0, [x1, #0x17]
    // 0xc11cfc: r0 = Instance_CrossAxisAlignment
    //     0xc11cfc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc11d00: ldr             x0, [x0, #0xa18]
    // 0xc11d04: StoreField: r1->field_1b = r0
    //     0xc11d04: stur            w0, [x1, #0x1b]
    // 0xc11d08: r2 = Instance_VerticalDirection
    //     0xc11d08: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc11d0c: ldr             x2, [x2, #0xa20]
    // 0xc11d10: StoreField: r1->field_23 = r2
    //     0xc11d10: stur            w2, [x1, #0x23]
    // 0xc11d14: r3 = Instance_Clip
    //     0xc11d14: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc11d18: ldr             x3, [x3, #0x38]
    // 0xc11d1c: StoreField: r1->field_2b = r3
    //     0xc11d1c: stur            w3, [x1, #0x2b]
    // 0xc11d20: StoreField: r1->field_2f = rZR
    //     0xc11d20: stur            xzr, [x1, #0x2f]
    // 0xc11d24: ldur            x4, [fp, #-0x18]
    // 0xc11d28: StoreField: r1->field_b = r4
    //     0xc11d28: stur            w4, [x1, #0xb]
    // 0xc11d2c: r0 = Padding()
    //     0xc11d2c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc11d30: mov             x2, x0
    // 0xc11d34: r0 = Instance_EdgeInsets
    //     0xc11d34: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xc11d38: ldr             x0, [x0, #0x980]
    // 0xc11d3c: stur            x2, [fp, #-0x18]
    // 0xc11d40: StoreField: r2->field_f = r0
    //     0xc11d40: stur            w0, [x2, #0xf]
    // 0xc11d44: ldur            x1, [fp, #-0x20]
    // 0xc11d48: StoreField: r2->field_b = r1
    //     0xc11d48: stur            w1, [x2, #0xb]
    // 0xc11d4c: ldur            x1, [fp, #-0x10]
    // 0xc11d50: r0 = of()
    //     0xc11d50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc11d54: LoadField: r1 = r0->field_87
    //     0xc11d54: ldur            w1, [x0, #0x87]
    // 0xc11d58: DecompressPointer r1
    //     0xc11d58: add             x1, x1, HEAP, lsl #32
    // 0xc11d5c: LoadField: r0 = r1->field_27
    //     0xc11d5c: ldur            w0, [x1, #0x27]
    // 0xc11d60: DecompressPointer r0
    //     0xc11d60: add             x0, x0, HEAP, lsl #32
    // 0xc11d64: r16 = Instance_FontWeight
    //     0xc11d64: add             x16, PP, #0x13, lsl #12  ; [pp+0x13010] Obj!FontWeight@d68d61
    //     0xc11d68: ldr             x16, [x16, #0x10]
    // 0xc11d6c: r30 = 16.000000
    //     0xc11d6c: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc11d70: ldr             lr, [lr, #0x188]
    // 0xc11d74: stp             lr, x16, [SP]
    // 0xc11d78: mov             x1, x0
    // 0xc11d7c: r4 = const [0, 0x3, 0x2, 0x1, fontSize, 0x2, fontWeight, 0x1, null]
    //     0xc11d7c: add             x4, PP, #0x53, lsl #12  ; [pp+0x53dc8] List(9) [0, 0x3, 0x2, 0x1, "fontSize", 0x2, "fontWeight", 0x1, Null]
    //     0xc11d80: ldr             x4, [x4, #0xdc8]
    // 0xc11d84: r0 = copyWith()
    //     0xc11d84: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc11d88: stur            x0, [fp, #-0x10]
    // 0xc11d8c: r0 = Text()
    //     0xc11d8c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc11d90: mov             x1, x0
    // 0xc11d94: r0 = "We have provided the body measurements to help you decide which size to buy."
    //     0xc11d94: add             x0, PP, #0x53, lsl #12  ; [pp+0x53de8] "We have provided the body measurements to help you decide which size to buy."
    //     0xc11d98: ldr             x0, [x0, #0xde8]
    // 0xc11d9c: stur            x1, [fp, #-0x20]
    // 0xc11da0: StoreField: r1->field_b = r0
    //     0xc11da0: stur            w0, [x1, #0xb]
    // 0xc11da4: ldur            x0, [fp, #-0x10]
    // 0xc11da8: StoreField: r1->field_13 = r0
    //     0xc11da8: stur            w0, [x1, #0x13]
    // 0xc11dac: r0 = Padding()
    //     0xc11dac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc11db0: mov             x1, x0
    // 0xc11db4: r0 = Instance_EdgeInsets
    //     0xc11db4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xc11db8: ldr             x0, [x0, #0x980]
    // 0xc11dbc: stur            x1, [fp, #-0x10]
    // 0xc11dc0: StoreField: r1->field_f = r0
    //     0xc11dc0: stur            w0, [x1, #0xf]
    // 0xc11dc4: ldur            x0, [fp, #-0x20]
    // 0xc11dc8: StoreField: r1->field_b = r0
    //     0xc11dc8: stur            w0, [x1, #0xb]
    // 0xc11dcc: r0 = ImageHeaders.forImages()
    //     0xc11dcc: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xc11dd0: mov             x1, x0
    // 0xc11dd4: ldur            x0, [fp, #-8]
    // 0xc11dd8: stur            x1, [fp, #-0x20]
    // 0xc11ddc: LoadField: r2 = r0->field_b
    //     0xc11ddc: ldur            w2, [x0, #0xb]
    // 0xc11de0: DecompressPointer r2
    //     0xc11de0: add             x2, x2, HEAP, lsl #32
    // 0xc11de4: cmp             w2, NULL
    // 0xc11de8: b.eq            #0xc11f60
    // 0xc11dec: LoadField: r0 = r2->field_b
    //     0xc11dec: ldur            w0, [x2, #0xb]
    // 0xc11df0: DecompressPointer r0
    //     0xc11df0: add             x0, x0, HEAP, lsl #32
    // 0xc11df4: cmp             w0, NULL
    // 0xc11df8: b.ne            #0xc11e04
    // 0xc11dfc: r3 = ""
    //     0xc11dfc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc11e00: b               #0xc11e08
    // 0xc11e04: mov             x3, x0
    // 0xc11e08: ldur            x2, [fp, #-0x18]
    // 0xc11e0c: ldur            x0, [fp, #-0x10]
    // 0xc11e10: stur            x3, [fp, #-8]
    // 0xc11e14: r0 = CachedNetworkImage()
    //     0xc11e14: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xc11e18: stur            x0, [fp, #-0x28]
    // 0xc11e1c: ldur            x16, [fp, #-0x20]
    // 0xc11e20: r30 = Instance_BoxFit
    //     0xc11e20: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xc11e24: ldr             lr, [lr, #0xb18]
    // 0xc11e28: stp             lr, x16, [SP]
    // 0xc11e2c: mov             x1, x0
    // 0xc11e30: ldur            x2, [fp, #-8]
    // 0xc11e34: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, httpHeaders, 0x2, null]
    //     0xc11e34: add             x4, PP, #0x53, lsl #12  ; [pp+0x53df0] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "httpHeaders", 0x2, Null]
    //     0xc11e38: ldr             x4, [x4, #0xdf0]
    // 0xc11e3c: r0 = CachedNetworkImage()
    //     0xc11e3c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xc11e40: r1 = Null
    //     0xc11e40: mov             x1, NULL
    // 0xc11e44: r2 = 8
    //     0xc11e44: movz            x2, #0x8
    // 0xc11e48: r0 = AllocateArray()
    //     0xc11e48: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc11e4c: mov             x2, x0
    // 0xc11e50: ldur            x0, [fp, #-0x18]
    // 0xc11e54: stur            x2, [fp, #-8]
    // 0xc11e58: StoreField: r2->field_f = r0
    //     0xc11e58: stur            w0, [x2, #0xf]
    // 0xc11e5c: ldur            x0, [fp, #-0x10]
    // 0xc11e60: StoreField: r2->field_13 = r0
    //     0xc11e60: stur            w0, [x2, #0x13]
    // 0xc11e64: r16 = Instance_SizedBox
    //     0xc11e64: add             x16, PP, #0x53, lsl #12  ; [pp+0x53df8] Obj!SizedBox@d68061
    //     0xc11e68: ldr             x16, [x16, #0xdf8]
    // 0xc11e6c: ArrayStore: r2[0] = r16  ; List_4
    //     0xc11e6c: stur            w16, [x2, #0x17]
    // 0xc11e70: ldur            x0, [fp, #-0x28]
    // 0xc11e74: StoreField: r2->field_1b = r0
    //     0xc11e74: stur            w0, [x2, #0x1b]
    // 0xc11e78: r1 = <Widget>
    //     0xc11e78: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc11e7c: r0 = AllocateGrowableArray()
    //     0xc11e7c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc11e80: mov             x1, x0
    // 0xc11e84: ldur            x0, [fp, #-8]
    // 0xc11e88: stur            x1, [fp, #-0x10]
    // 0xc11e8c: StoreField: r1->field_f = r0
    //     0xc11e8c: stur            w0, [x1, #0xf]
    // 0xc11e90: r0 = 8
    //     0xc11e90: movz            x0, #0x8
    // 0xc11e94: StoreField: r1->field_b = r0
    //     0xc11e94: stur            w0, [x1, #0xb]
    // 0xc11e98: r0 = Column()
    //     0xc11e98: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc11e9c: mov             x1, x0
    // 0xc11ea0: r0 = Instance_Axis
    //     0xc11ea0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc11ea4: stur            x1, [fp, #-8]
    // 0xc11ea8: StoreField: r1->field_f = r0
    //     0xc11ea8: stur            w0, [x1, #0xf]
    // 0xc11eac: r0 = Instance_MainAxisAlignment
    //     0xc11eac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc11eb0: ldr             x0, [x0, #0xa08]
    // 0xc11eb4: StoreField: r1->field_13 = r0
    //     0xc11eb4: stur            w0, [x1, #0x13]
    // 0xc11eb8: r0 = Instance_MainAxisSize
    //     0xc11eb8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xc11ebc: ldr             x0, [x0, #0xdd0]
    // 0xc11ec0: ArrayStore: r1[0] = r0  ; List_4
    //     0xc11ec0: stur            w0, [x1, #0x17]
    // 0xc11ec4: r0 = Instance_CrossAxisAlignment
    //     0xc11ec4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc11ec8: ldr             x0, [x0, #0xa18]
    // 0xc11ecc: StoreField: r1->field_1b = r0
    //     0xc11ecc: stur            w0, [x1, #0x1b]
    // 0xc11ed0: r0 = Instance_VerticalDirection
    //     0xc11ed0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc11ed4: ldr             x0, [x0, #0xa20]
    // 0xc11ed8: StoreField: r1->field_23 = r0
    //     0xc11ed8: stur            w0, [x1, #0x23]
    // 0xc11edc: r0 = Instance_Clip
    //     0xc11edc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc11ee0: ldr             x0, [x0, #0x38]
    // 0xc11ee4: StoreField: r1->field_2b = r0
    //     0xc11ee4: stur            w0, [x1, #0x2b]
    // 0xc11ee8: StoreField: r1->field_2f = rZR
    //     0xc11ee8: stur            xzr, [x1, #0x2f]
    // 0xc11eec: ldur            x0, [fp, #-0x10]
    // 0xc11ef0: StoreField: r1->field_b = r0
    //     0xc11ef0: stur            w0, [x1, #0xb]
    // 0xc11ef4: r0 = Scaffold()
    //     0xc11ef4: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xc11ef8: mov             x1, x0
    // 0xc11efc: ldur            x0, [fp, #-8]
    // 0xc11f00: stur            x1, [fp, #-0x10]
    // 0xc11f04: ArrayStore: r1[0] = r0  ; List_4
    //     0xc11f04: stur            w0, [x1, #0x17]
    // 0xc11f08: r0 = true
    //     0xc11f08: add             x0, NULL, #0x20  ; true
    // 0xc11f0c: StoreField: r1->field_43 = r0
    //     0xc11f0c: stur            w0, [x1, #0x43]
    // 0xc11f10: r2 = false
    //     0xc11f10: add             x2, NULL, #0x30  ; false
    // 0xc11f14: StoreField: r1->field_b = r2
    //     0xc11f14: stur            w2, [x1, #0xb]
    // 0xc11f18: StoreField: r1->field_f = r2
    //     0xc11f18: stur            w2, [x1, #0xf]
    // 0xc11f1c: r0 = SafeArea()
    //     0xc11f1c: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xc11f20: r1 = true
    //     0xc11f20: add             x1, NULL, #0x20  ; true
    // 0xc11f24: StoreField: r0->field_b = r1
    //     0xc11f24: stur            w1, [x0, #0xb]
    // 0xc11f28: StoreField: r0->field_f = r1
    //     0xc11f28: stur            w1, [x0, #0xf]
    // 0xc11f2c: StoreField: r0->field_13 = r1
    //     0xc11f2c: stur            w1, [x0, #0x13]
    // 0xc11f30: ArrayStore: r0[0] = r1  ; List_4
    //     0xc11f30: stur            w1, [x0, #0x17]
    // 0xc11f34: r1 = Instance_EdgeInsets
    //     0xc11f34: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xc11f38: StoreField: r0->field_1b = r1
    //     0xc11f38: stur            w1, [x0, #0x1b]
    // 0xc11f3c: r1 = false
    //     0xc11f3c: add             x1, NULL, #0x30  ; false
    // 0xc11f40: StoreField: r0->field_1f = r1
    //     0xc11f40: stur            w1, [x0, #0x1f]
    // 0xc11f44: ldur            x1, [fp, #-0x10]
    // 0xc11f48: StoreField: r0->field_23 = r1
    //     0xc11f48: stur            w1, [x0, #0x23]
    // 0xc11f4c: LeaveFrame
    //     0xc11f4c: mov             SP, fp
    //     0xc11f50: ldp             fp, lr, [SP], #0x10
    // 0xc11f54: ret
    //     0xc11f54: ret             
    // 0xc11f58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc11f58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc11f5c: b               #0xc11ba0
    // 0xc11f60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc11f60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3958, size: 0x10, field offset: 0xc
//   const constructor, 
class ViewSizeChart extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc81450, size: 0x24
    // 0xc81450: EnterFrame
    //     0xc81450: stp             fp, lr, [SP, #-0x10]!
    //     0xc81454: mov             fp, SP
    // 0xc81458: mov             x0, x1
    // 0xc8145c: r1 = <ViewSizeChart>
    //     0xc8145c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48600] TypeArguments: <ViewSizeChart>
    //     0xc81460: ldr             x1, [x1, #0x600]
    // 0xc81464: r0 = _ViewSizeChartState()
    //     0xc81464: bl              #0xc81474  ; Allocate_ViewSizeChartStateStub -> _ViewSizeChartState (size=0x14)
    // 0xc81468: LeaveFrame
    //     0xc81468: mov             SP, fp
    //     0xc8146c: ldp             fp, lr, [SP], #0x10
    // 0xc81470: ret
    //     0xc81470: ret             
  }
}
