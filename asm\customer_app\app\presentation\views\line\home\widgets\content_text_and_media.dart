// lib: , url: package:customer_app/app/presentation/views/line/home/<USER>/content_text_and_media.dart

// class id: 1049523, size: 0x8
class :: {
}

// class id: 3248, size: 0x14, field offset: 0x14
class _ContentTextAndMediaState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbe00f0, size: 0x170
    // 0xbe00f0: EnterFrame
    //     0xbe00f0: stp             fp, lr, [SP, #-0x10]!
    //     0xbe00f4: mov             fp, SP
    // 0xbe00f8: AllocStack(0x28)
    //     0xbe00f8: sub             SP, SP, #0x28
    // 0xbe00fc: SetupParameters(_ContentTextAndMediaState this /* r1 => r1, fp-0x8 */)
    //     0xbe00fc: stur            x1, [fp, #-8]
    // 0xbe0100: CheckStackOverflow
    //     0xbe0100: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe0104: cmp             SP, x16
    //     0xbe0108: b.ls            #0xbe0254
    // 0xbe010c: r1 = 1
    //     0xbe010c: movz            x1, #0x1
    // 0xbe0110: r0 = AllocateContext()
    //     0xbe0110: bl              #0x16f6108  ; AllocateContextStub
    // 0xbe0114: mov             x1, x0
    // 0xbe0118: ldur            x0, [fp, #-8]
    // 0xbe011c: StoreField: r1->field_f = r0
    //     0xbe011c: stur            w0, [x1, #0xf]
    // 0xbe0120: LoadField: r2 = r0->field_b
    //     0xbe0120: ldur            w2, [x0, #0xb]
    // 0xbe0124: DecompressPointer r2
    //     0xbe0124: add             x2, x2, HEAP, lsl #32
    // 0xbe0128: cmp             w2, NULL
    // 0xbe012c: b.eq            #0xbe025c
    // 0xbe0130: LoadField: r0 = r2->field_b
    //     0xbe0130: ldur            w0, [x2, #0xb]
    // 0xbe0134: DecompressPointer r0
    //     0xbe0134: add             x0, x0, HEAP, lsl #32
    // 0xbe0138: cmp             w0, NULL
    // 0xbe013c: b.ne            #0xbe0148
    // 0xbe0140: r3 = Null
    //     0xbe0140: mov             x3, NULL
    // 0xbe0144: b               #0xbe0150
    // 0xbe0148: LoadField: r2 = r0->field_b
    //     0xbe0148: ldur            w2, [x0, #0xb]
    // 0xbe014c: mov             x3, x2
    // 0xbe0150: mov             x2, x1
    // 0xbe0154: stur            x3, [fp, #-8]
    // 0xbe0158: r1 = Function '<anonymous closure>':.
    //     0xbe0158: add             x1, PP, #0x53, lsl #12  ; [pp+0x53768] AnonymousClosure: (0xbe0260), in [package:customer_app/app/presentation/views/line/home/<USER>/content_text_and_media.dart] _ContentTextAndMediaState::build (0xbe00f0)
    //     0xbe015c: ldr             x1, [x1, #0x768]
    // 0xbe0160: r0 = AllocateClosure()
    //     0xbe0160: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe0164: stur            x0, [fp, #-0x10]
    // 0xbe0168: r0 = ListView()
    //     0xbe0168: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbe016c: stur            x0, [fp, #-0x18]
    // 0xbe0170: r16 = true
    //     0xbe0170: add             x16, NULL, #0x20  ; true
    // 0xbe0174: r30 = Instance_NeverScrollableScrollPhysics
    //     0xbe0174: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbe0178: ldr             lr, [lr, #0x1c8]
    // 0xbe017c: stp             lr, x16, [SP]
    // 0xbe0180: mov             x1, x0
    // 0xbe0184: ldur            x2, [fp, #-0x10]
    // 0xbe0188: ldur            x3, [fp, #-8]
    // 0xbe018c: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xbe018c: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xbe0190: ldr             x4, [x4, #8]
    // 0xbe0194: r0 = ListView.builder()
    //     0xbe0194: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbe0198: r1 = Null
    //     0xbe0198: mov             x1, NULL
    // 0xbe019c: r2 = 2
    //     0xbe019c: movz            x2, #0x2
    // 0xbe01a0: r0 = AllocateArray()
    //     0xbe01a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe01a4: mov             x2, x0
    // 0xbe01a8: ldur            x0, [fp, #-0x18]
    // 0xbe01ac: stur            x2, [fp, #-8]
    // 0xbe01b0: StoreField: r2->field_f = r0
    //     0xbe01b0: stur            w0, [x2, #0xf]
    // 0xbe01b4: r1 = <Widget>
    //     0xbe01b4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe01b8: r0 = AllocateGrowableArray()
    //     0xbe01b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe01bc: mov             x1, x0
    // 0xbe01c0: ldur            x0, [fp, #-8]
    // 0xbe01c4: stur            x1, [fp, #-0x10]
    // 0xbe01c8: StoreField: r1->field_f = r0
    //     0xbe01c8: stur            w0, [x1, #0xf]
    // 0xbe01cc: r0 = 2
    //     0xbe01cc: movz            x0, #0x2
    // 0xbe01d0: StoreField: r1->field_b = r0
    //     0xbe01d0: stur            w0, [x1, #0xb]
    // 0xbe01d4: r0 = Column()
    //     0xbe01d4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbe01d8: mov             x1, x0
    // 0xbe01dc: r0 = Instance_Axis
    //     0xbe01dc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbe01e0: stur            x1, [fp, #-8]
    // 0xbe01e4: StoreField: r1->field_f = r0
    //     0xbe01e4: stur            w0, [x1, #0xf]
    // 0xbe01e8: r0 = Instance_MainAxisAlignment
    //     0xbe01e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbe01ec: ldr             x0, [x0, #0xa08]
    // 0xbe01f0: StoreField: r1->field_13 = r0
    //     0xbe01f0: stur            w0, [x1, #0x13]
    // 0xbe01f4: r0 = Instance_MainAxisSize
    //     0xbe01f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbe01f8: ldr             x0, [x0, #0xa10]
    // 0xbe01fc: ArrayStore: r1[0] = r0  ; List_4
    //     0xbe01fc: stur            w0, [x1, #0x17]
    // 0xbe0200: r0 = Instance_CrossAxisAlignment
    //     0xbe0200: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbe0204: ldr             x0, [x0, #0x890]
    // 0xbe0208: StoreField: r1->field_1b = r0
    //     0xbe0208: stur            w0, [x1, #0x1b]
    // 0xbe020c: r0 = Instance_VerticalDirection
    //     0xbe020c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe0210: ldr             x0, [x0, #0xa20]
    // 0xbe0214: StoreField: r1->field_23 = r0
    //     0xbe0214: stur            w0, [x1, #0x23]
    // 0xbe0218: r0 = Instance_Clip
    //     0xbe0218: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe021c: ldr             x0, [x0, #0x38]
    // 0xbe0220: StoreField: r1->field_2b = r0
    //     0xbe0220: stur            w0, [x1, #0x2b]
    // 0xbe0224: StoreField: r1->field_2f = rZR
    //     0xbe0224: stur            xzr, [x1, #0x2f]
    // 0xbe0228: ldur            x0, [fp, #-0x10]
    // 0xbe022c: StoreField: r1->field_b = r0
    //     0xbe022c: stur            w0, [x1, #0xb]
    // 0xbe0230: r0 = Padding()
    //     0xbe0230: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe0234: r1 = Instance_EdgeInsets
    //     0xbe0234: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xbe0238: ldr             x1, [x1, #0x110]
    // 0xbe023c: StoreField: r0->field_f = r1
    //     0xbe023c: stur            w1, [x0, #0xf]
    // 0xbe0240: ldur            x1, [fp, #-8]
    // 0xbe0244: StoreField: r0->field_b = r1
    //     0xbe0244: stur            w1, [x0, #0xb]
    // 0xbe0248: LeaveFrame
    //     0xbe0248: mov             SP, fp
    //     0xbe024c: ldp             fp, lr, [SP], #0x10
    // 0xbe0250: ret
    //     0xbe0250: ret             
    // 0xbe0254: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe0254: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe0258: b               #0xbe010c
    // 0xbe025c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe025c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Column <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbe0260, size: 0x1414
    // 0xbe0260: EnterFrame
    //     0xbe0260: stp             fp, lr, [SP, #-0x10]!
    //     0xbe0264: mov             fp, SP
    // 0xbe0268: AllocStack(0x68)
    //     0xbe0268: sub             SP, SP, #0x68
    // 0xbe026c: SetupParameters()
    //     0xbe026c: ldr             x0, [fp, #0x20]
    //     0xbe0270: ldur            w2, [x0, #0x17]
    //     0xbe0274: add             x2, x2, HEAP, lsl #32
    //     0xbe0278: stur            x2, [fp, #-8]
    // 0xbe027c: CheckStackOverflow
    //     0xbe027c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe0280: cmp             SP, x16
    //     0xbe0284: b.ls            #0xbe160c
    // 0xbe0288: LoadField: r0 = r2->field_f
    //     0xbe0288: ldur            w0, [x2, #0xf]
    // 0xbe028c: DecompressPointer r0
    //     0xbe028c: add             x0, x0, HEAP, lsl #32
    // 0xbe0290: LoadField: r1 = r0->field_b
    //     0xbe0290: ldur            w1, [x0, #0xb]
    // 0xbe0294: DecompressPointer r1
    //     0xbe0294: add             x1, x1, HEAP, lsl #32
    // 0xbe0298: cmp             w1, NULL
    // 0xbe029c: b.eq            #0xbe1614
    // 0xbe02a0: LoadField: r3 = r1->field_b
    //     0xbe02a0: ldur            w3, [x1, #0xb]
    // 0xbe02a4: DecompressPointer r3
    //     0xbe02a4: add             x3, x3, HEAP, lsl #32
    // 0xbe02a8: cmp             w3, NULL
    // 0xbe02ac: b.ne            #0xbe02bc
    // 0xbe02b0: ldr             x4, [fp, #0x10]
    // 0xbe02b4: r0 = Null
    //     0xbe02b4: mov             x0, NULL
    // 0xbe02b8: b               #0xbe031c
    // 0xbe02bc: ldr             x4, [fp, #0x10]
    // 0xbe02c0: LoadField: r0 = r3->field_b
    //     0xbe02c0: ldur            w0, [x3, #0xb]
    // 0xbe02c4: r5 = LoadInt32Instr(r4)
    //     0xbe02c4: sbfx            x5, x4, #1, #0x1f
    //     0xbe02c8: tbz             w4, #0, #0xbe02d0
    //     0xbe02cc: ldur            x5, [x4, #7]
    // 0xbe02d0: r1 = LoadInt32Instr(r0)
    //     0xbe02d0: sbfx            x1, x0, #1, #0x1f
    // 0xbe02d4: mov             x0, x1
    // 0xbe02d8: mov             x1, x5
    // 0xbe02dc: cmp             x1, x0
    // 0xbe02e0: b.hs            #0xbe1618
    // 0xbe02e4: LoadField: r0 = r3->field_f
    //     0xbe02e4: ldur            w0, [x3, #0xf]
    // 0xbe02e8: DecompressPointer r0
    //     0xbe02e8: add             x0, x0, HEAP, lsl #32
    // 0xbe02ec: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbe02ec: add             x16, x0, x5, lsl #2
    //     0xbe02f0: ldur            w1, [x16, #0xf]
    // 0xbe02f4: DecompressPointer r1
    //     0xbe02f4: add             x1, x1, HEAP, lsl #32
    // 0xbe02f8: LoadField: r0 = r1->field_27
    //     0xbe02f8: ldur            w0, [x1, #0x27]
    // 0xbe02fc: DecompressPointer r0
    //     0xbe02fc: add             x0, x0, HEAP, lsl #32
    // 0xbe0300: cmp             w0, NULL
    // 0xbe0304: b.ne            #0xbe0310
    // 0xbe0308: r0 = Null
    //     0xbe0308: mov             x0, NULL
    // 0xbe030c: b               #0xbe031c
    // 0xbe0310: LoadField: r1 = r0->field_f
    //     0xbe0310: ldur            w1, [x0, #0xf]
    // 0xbe0314: DecompressPointer r1
    //     0xbe0314: add             x1, x1, HEAP, lsl #32
    // 0xbe0318: mov             x0, x1
    // 0xbe031c: r1 = LoadClassIdInstr(r0)
    //     0xbe031c: ldur            x1, [x0, #-1]
    //     0xbe0320: ubfx            x1, x1, #0xc, #0x14
    // 0xbe0324: r16 = "LEFT"
    //     0xbe0324: add             x16, PP, #0x53, lsl #12  ; [pp+0x53770] "LEFT"
    //     0xbe0328: ldr             x16, [x16, #0x770]
    // 0xbe032c: stp             x16, x0, [SP]
    // 0xbe0330: mov             x0, x1
    // 0xbe0334: mov             lr, x0
    // 0xbe0338: ldr             lr, [x21, lr, lsl #3]
    // 0xbe033c: blr             lr
    // 0xbe0340: tbnz            w0, #4, #0xbe0c28
    // 0xbe0344: ldur            x2, [fp, #-8]
    // 0xbe0348: LoadField: r0 = r2->field_f
    //     0xbe0348: ldur            w0, [x2, #0xf]
    // 0xbe034c: DecompressPointer r0
    //     0xbe034c: add             x0, x0, HEAP, lsl #32
    // 0xbe0350: LoadField: r1 = r0->field_b
    //     0xbe0350: ldur            w1, [x0, #0xb]
    // 0xbe0354: DecompressPointer r1
    //     0xbe0354: add             x1, x1, HEAP, lsl #32
    // 0xbe0358: cmp             w1, NULL
    // 0xbe035c: b.eq            #0xbe161c
    // 0xbe0360: LoadField: r3 = r1->field_b
    //     0xbe0360: ldur            w3, [x1, #0xb]
    // 0xbe0364: DecompressPointer r3
    //     0xbe0364: add             x3, x3, HEAP, lsl #32
    // 0xbe0368: cmp             w3, NULL
    // 0xbe036c: b.ne            #0xbe037c
    // 0xbe0370: ldr             x4, [fp, #0x10]
    // 0xbe0374: r0 = Null
    //     0xbe0374: mov             x0, NULL
    // 0xbe0378: b               #0xbe03c0
    // 0xbe037c: ldr             x4, [fp, #0x10]
    // 0xbe0380: LoadField: r0 = r3->field_b
    //     0xbe0380: ldur            w0, [x3, #0xb]
    // 0xbe0384: r5 = LoadInt32Instr(r4)
    //     0xbe0384: sbfx            x5, x4, #1, #0x1f
    //     0xbe0388: tbz             w4, #0, #0xbe0390
    //     0xbe038c: ldur            x5, [x4, #7]
    // 0xbe0390: r1 = LoadInt32Instr(r0)
    //     0xbe0390: sbfx            x1, x0, #1, #0x1f
    // 0xbe0394: mov             x0, x1
    // 0xbe0398: mov             x1, x5
    // 0xbe039c: cmp             x1, x0
    // 0xbe03a0: b.hs            #0xbe1620
    // 0xbe03a4: LoadField: r0 = r3->field_f
    //     0xbe03a4: ldur            w0, [x3, #0xf]
    // 0xbe03a8: DecompressPointer r0
    //     0xbe03a8: add             x0, x0, HEAP, lsl #32
    // 0xbe03ac: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbe03ac: add             x16, x0, x5, lsl #2
    //     0xbe03b0: ldur            w1, [x16, #0xf]
    // 0xbe03b4: DecompressPointer r1
    //     0xbe03b4: add             x1, x1, HEAP, lsl #32
    // 0xbe03b8: LoadField: r0 = r1->field_13
    //     0xbe03b8: ldur            w0, [x1, #0x13]
    // 0xbe03bc: DecompressPointer r0
    //     0xbe03bc: add             x0, x0, HEAP, lsl #32
    // 0xbe03c0: cmp             w0, NULL
    // 0xbe03c4: b.ne            #0xbe03cc
    // 0xbe03c8: r0 = ""
    //     0xbe03c8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe03cc: stur            x0, [fp, #-0x10]
    // 0xbe03d0: r0 = ImageHeaders.forImages()
    //     0xbe03d0: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbe03d4: r1 = Function '<anonymous closure>':.
    //     0xbe03d4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53778] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbe03d8: ldr             x1, [x1, #0x778]
    // 0xbe03dc: r2 = Null
    //     0xbe03dc: mov             x2, NULL
    // 0xbe03e0: stur            x0, [fp, #-0x18]
    // 0xbe03e4: r0 = AllocateClosure()
    //     0xbe03e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe03e8: r1 = Function '<anonymous closure>':.
    //     0xbe03e8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53780] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbe03ec: ldr             x1, [x1, #0x780]
    // 0xbe03f0: r2 = Null
    //     0xbe03f0: mov             x2, NULL
    // 0xbe03f4: stur            x0, [fp, #-0x20]
    // 0xbe03f8: r0 = AllocateClosure()
    //     0xbe03f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe03fc: stur            x0, [fp, #-0x28]
    // 0xbe0400: r0 = CachedNetworkImage()
    //     0xbe0400: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbe0404: stur            x0, [fp, #-0x30]
    // 0xbe0408: ldur            x16, [fp, #-0x18]
    // 0xbe040c: ldur            lr, [fp, #-0x20]
    // 0xbe0410: stp             lr, x16, [SP, #0x10]
    // 0xbe0414: ldur            x16, [fp, #-0x28]
    // 0xbe0418: r30 = Instance_BoxFit
    //     0xbe0418: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbe041c: ldr             lr, [lr, #0xb18]
    // 0xbe0420: stp             lr, x16, [SP]
    // 0xbe0424: mov             x1, x0
    // 0xbe0428: ldur            x2, [fp, #-0x10]
    // 0xbe042c: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x4, fit, 0x5, httpHeaders, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xbe042c: add             x4, PP, #0x52, lsl #12  ; [pp+0x522b0] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x4, "fit", 0x5, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xbe0430: ldr             x4, [x4, #0x2b0]
    // 0xbe0434: r0 = CachedNetworkImage()
    //     0xbe0434: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbe0438: ldur            x2, [fp, #-8]
    // 0xbe043c: LoadField: r0 = r2->field_f
    //     0xbe043c: ldur            w0, [x2, #0xf]
    // 0xbe0440: DecompressPointer r0
    //     0xbe0440: add             x0, x0, HEAP, lsl #32
    // 0xbe0444: LoadField: r1 = r0->field_b
    //     0xbe0444: ldur            w1, [x0, #0xb]
    // 0xbe0448: DecompressPointer r1
    //     0xbe0448: add             x1, x1, HEAP, lsl #32
    // 0xbe044c: cmp             w1, NULL
    // 0xbe0450: b.eq            #0xbe1624
    // 0xbe0454: LoadField: r3 = r1->field_b
    //     0xbe0454: ldur            w3, [x1, #0xb]
    // 0xbe0458: DecompressPointer r3
    //     0xbe0458: add             x3, x3, HEAP, lsl #32
    // 0xbe045c: cmp             w3, NULL
    // 0xbe0460: b.ne            #0xbe0470
    // 0xbe0464: ldr             x4, [fp, #0x10]
    // 0xbe0468: r0 = Null
    //     0xbe0468: mov             x0, NULL
    // 0xbe046c: b               #0xbe04b4
    // 0xbe0470: ldr             x4, [fp, #0x10]
    // 0xbe0474: LoadField: r0 = r3->field_b
    //     0xbe0474: ldur            w0, [x3, #0xb]
    // 0xbe0478: r5 = LoadInt32Instr(r4)
    //     0xbe0478: sbfx            x5, x4, #1, #0x1f
    //     0xbe047c: tbz             w4, #0, #0xbe0484
    //     0xbe0480: ldur            x5, [x4, #7]
    // 0xbe0484: r1 = LoadInt32Instr(r0)
    //     0xbe0484: sbfx            x1, x0, #1, #0x1f
    // 0xbe0488: mov             x0, x1
    // 0xbe048c: mov             x1, x5
    // 0xbe0490: cmp             x1, x0
    // 0xbe0494: b.hs            #0xbe1628
    // 0xbe0498: LoadField: r0 = r3->field_f
    //     0xbe0498: ldur            w0, [x3, #0xf]
    // 0xbe049c: DecompressPointer r0
    //     0xbe049c: add             x0, x0, HEAP, lsl #32
    // 0xbe04a0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbe04a0: add             x16, x0, x5, lsl #2
    //     0xbe04a4: ldur            w1, [x16, #0xf]
    // 0xbe04a8: DecompressPointer r1
    //     0xbe04a8: add             x1, x1, HEAP, lsl #32
    // 0xbe04ac: LoadField: r0 = r1->field_7
    //     0xbe04ac: ldur            w0, [x1, #7]
    // 0xbe04b0: DecompressPointer r0
    //     0xbe04b0: add             x0, x0, HEAP, lsl #32
    // 0xbe04b4: cmp             w0, NULL
    // 0xbe04b8: b.ne            #0xbe04c4
    // 0xbe04bc: r3 = ""
    //     0xbe04bc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe04c0: b               #0xbe04c8
    // 0xbe04c4: mov             x3, x0
    // 0xbe04c8: ldur            x0, [fp, #-0x30]
    // 0xbe04cc: ldr             x1, [fp, #0x18]
    // 0xbe04d0: stur            x3, [fp, #-0x10]
    // 0xbe04d4: r0 = of()
    //     0xbe04d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe04d8: LoadField: r1 = r0->field_87
    //     0xbe04d8: ldur            w1, [x0, #0x87]
    // 0xbe04dc: DecompressPointer r1
    //     0xbe04dc: add             x1, x1, HEAP, lsl #32
    // 0xbe04e0: LoadField: r0 = r1->field_7
    //     0xbe04e0: ldur            w0, [x1, #7]
    // 0xbe04e4: DecompressPointer r0
    //     0xbe04e4: add             x0, x0, HEAP, lsl #32
    // 0xbe04e8: stur            x0, [fp, #-0x18]
    // 0xbe04ec: r1 = Instance_Color
    //     0xbe04ec: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe04f0: d0 = 0.700000
    //     0xbe04f0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbe04f4: ldr             d0, [x17, #0xf48]
    // 0xbe04f8: r0 = withOpacity()
    //     0xbe04f8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbe04fc: r16 = 16.000000
    //     0xbe04fc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbe0500: ldr             x16, [x16, #0x188]
    // 0xbe0504: stp             x0, x16, [SP]
    // 0xbe0508: ldur            x1, [fp, #-0x18]
    // 0xbe050c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe050c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe0510: ldr             x4, [x4, #0xaa0]
    // 0xbe0514: r0 = copyWith()
    //     0xbe0514: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe0518: stur            x0, [fp, #-0x18]
    // 0xbe051c: r0 = Text()
    //     0xbe051c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe0520: mov             x1, x0
    // 0xbe0524: ldur            x0, [fp, #-0x10]
    // 0xbe0528: stur            x1, [fp, #-0x20]
    // 0xbe052c: StoreField: r1->field_b = r0
    //     0xbe052c: stur            w0, [x1, #0xb]
    // 0xbe0530: ldur            x0, [fp, #-0x18]
    // 0xbe0534: StoreField: r1->field_13 = r0
    //     0xbe0534: stur            w0, [x1, #0x13]
    // 0xbe0538: r0 = Padding()
    //     0xbe0538: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe053c: mov             x2, x0
    // 0xbe0540: r0 = Instance_EdgeInsets
    //     0xbe0540: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b30] Obj!EdgeInsets@d588e1
    //     0xbe0544: ldr             x0, [x0, #0xb30]
    // 0xbe0548: stur            x2, [fp, #-0x10]
    // 0xbe054c: StoreField: r2->field_f = r0
    //     0xbe054c: stur            w0, [x2, #0xf]
    // 0xbe0550: ldur            x1, [fp, #-0x20]
    // 0xbe0554: StoreField: r2->field_b = r1
    //     0xbe0554: stur            w1, [x2, #0xb]
    // 0xbe0558: ldr             x1, [fp, #0x18]
    // 0xbe055c: r0 = of()
    //     0xbe055c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe0560: LoadField: r1 = r0->field_87
    //     0xbe0560: ldur            w1, [x0, #0x87]
    // 0xbe0564: DecompressPointer r1
    //     0xbe0564: add             x1, x1, HEAP, lsl #32
    // 0xbe0568: LoadField: r0 = r1->field_7
    //     0xbe0568: ldur            w0, [x1, #7]
    // 0xbe056c: DecompressPointer r0
    //     0xbe056c: add             x0, x0, HEAP, lsl #32
    // 0xbe0570: r16 = Instance_TextDecoration
    //     0xbe0570: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xbe0574: ldr             x16, [x16, #0xe30]
    // 0xbe0578: r30 = 4.000000
    //     0xbe0578: add             lr, PP, #0x27, lsl #12  ; [pp+0x27838] 4
    //     0xbe057c: ldr             lr, [lr, #0x838]
    // 0xbe0580: stp             lr, x16, [SP, #0x18]
    // 0xbe0584: r16 = Instance_Color
    //     0xbe0584: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe0588: r30 = 16.000000
    //     0xbe0588: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbe058c: ldr             lr, [lr, #0x188]
    // 0xbe0590: stp             lr, x16, [SP, #8]
    // 0xbe0594: r16 = Instance_Color
    //     0xbe0594: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe0598: str             x16, [SP]
    // 0xbe059c: mov             x1, x0
    // 0xbe05a0: r4 = const [0, 0x6, 0x5, 0x1, color, 0x5, decoration, 0x1, decorationColor, 0x3, decorationThickness, 0x2, fontSize, 0x4, null]
    //     0xbe05a0: add             x4, PP, #0x52, lsl #12  ; [pp+0x52b38] List(15) [0, 0x6, 0x5, 0x1, "color", 0x5, "decoration", 0x1, "decorationColor", 0x3, "decorationThickness", 0x2, "fontSize", 0x4, Null]
    //     0xbe05a4: ldr             x4, [x4, #0xb38]
    // 0xbe05a8: r0 = copyWith()
    //     0xbe05a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe05ac: stur            x0, [fp, #-0x18]
    // 0xbe05b0: r0 = Text()
    //     0xbe05b0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe05b4: r2 = "------"
    //     0xbe05b4: add             x2, PP, #0x52, lsl #12  ; [pp+0x52b40] "------"
    //     0xbe05b8: ldr             x2, [x2, #0xb40]
    // 0xbe05bc: stur            x0, [fp, #-0x20]
    // 0xbe05c0: StoreField: r0->field_b = r2
    //     0xbe05c0: stur            w2, [x0, #0xb]
    // 0xbe05c4: ldur            x1, [fp, #-0x18]
    // 0xbe05c8: StoreField: r0->field_13 = r1
    //     0xbe05c8: stur            w1, [x0, #0x13]
    // 0xbe05cc: r1 = Null
    //     0xbe05cc: mov             x1, NULL
    // 0xbe05d0: r2 = 6
    //     0xbe05d0: movz            x2, #0x6
    // 0xbe05d4: r0 = AllocateArray()
    //     0xbe05d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe05d8: mov             x2, x0
    // 0xbe05dc: ldur            x0, [fp, #-0x30]
    // 0xbe05e0: stur            x2, [fp, #-0x18]
    // 0xbe05e4: StoreField: r2->field_f = r0
    //     0xbe05e4: stur            w0, [x2, #0xf]
    // 0xbe05e8: ldur            x0, [fp, #-0x10]
    // 0xbe05ec: StoreField: r2->field_13 = r0
    //     0xbe05ec: stur            w0, [x2, #0x13]
    // 0xbe05f0: ldur            x0, [fp, #-0x20]
    // 0xbe05f4: ArrayStore: r2[0] = r0  ; List_4
    //     0xbe05f4: stur            w0, [x2, #0x17]
    // 0xbe05f8: r1 = <Widget>
    //     0xbe05f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe05fc: r0 = AllocateGrowableArray()
    //     0xbe05fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe0600: mov             x2, x0
    // 0xbe0604: ldur            x0, [fp, #-0x18]
    // 0xbe0608: stur            x2, [fp, #-0x20]
    // 0xbe060c: StoreField: r2->field_f = r0
    //     0xbe060c: stur            w0, [x2, #0xf]
    // 0xbe0610: r0 = 6
    //     0xbe0610: movz            x0, #0x6
    // 0xbe0614: StoreField: r2->field_b = r0
    //     0xbe0614: stur            w0, [x2, #0xb]
    // 0xbe0618: ldur            x3, [fp, #-8]
    // 0xbe061c: LoadField: r0 = r3->field_f
    //     0xbe061c: ldur            w0, [x3, #0xf]
    // 0xbe0620: DecompressPointer r0
    //     0xbe0620: add             x0, x0, HEAP, lsl #32
    // 0xbe0624: LoadField: r1 = r0->field_b
    //     0xbe0624: ldur            w1, [x0, #0xb]
    // 0xbe0628: DecompressPointer r1
    //     0xbe0628: add             x1, x1, HEAP, lsl #32
    // 0xbe062c: cmp             w1, NULL
    // 0xbe0630: b.eq            #0xbe162c
    // 0xbe0634: LoadField: r4 = r1->field_b
    //     0xbe0634: ldur            w4, [x1, #0xb]
    // 0xbe0638: DecompressPointer r4
    //     0xbe0638: add             x4, x4, HEAP, lsl #32
    // 0xbe063c: cmp             w4, NULL
    // 0xbe0640: b.ne            #0xbe0650
    // 0xbe0644: ldr             x5, [fp, #0x10]
    // 0xbe0648: r0 = Null
    //     0xbe0648: mov             x0, NULL
    // 0xbe064c: b               #0xbe06b8
    // 0xbe0650: ldr             x5, [fp, #0x10]
    // 0xbe0654: LoadField: r0 = r4->field_b
    //     0xbe0654: ldur            w0, [x4, #0xb]
    // 0xbe0658: r6 = LoadInt32Instr(r5)
    //     0xbe0658: sbfx            x6, x5, #1, #0x1f
    //     0xbe065c: tbz             w5, #0, #0xbe0664
    //     0xbe0660: ldur            x6, [x5, #7]
    // 0xbe0664: r1 = LoadInt32Instr(r0)
    //     0xbe0664: sbfx            x1, x0, #1, #0x1f
    // 0xbe0668: mov             x0, x1
    // 0xbe066c: mov             x1, x6
    // 0xbe0670: cmp             x1, x0
    // 0xbe0674: b.hs            #0xbe1630
    // 0xbe0678: LoadField: r0 = r4->field_f
    //     0xbe0678: ldur            w0, [x4, #0xf]
    // 0xbe067c: DecompressPointer r0
    //     0xbe067c: add             x0, x0, HEAP, lsl #32
    // 0xbe0680: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbe0680: add             x16, x0, x6, lsl #2
    //     0xbe0684: ldur            w1, [x16, #0xf]
    // 0xbe0688: DecompressPointer r1
    //     0xbe0688: add             x1, x1, HEAP, lsl #32
    // 0xbe068c: LoadField: r0 = r1->field_b
    //     0xbe068c: ldur            w0, [x1, #0xb]
    // 0xbe0690: DecompressPointer r0
    //     0xbe0690: add             x0, x0, HEAP, lsl #32
    // 0xbe0694: cmp             w0, NULL
    // 0xbe0698: b.ne            #0xbe06a4
    // 0xbe069c: r0 = Null
    //     0xbe069c: mov             x0, NULL
    // 0xbe06a0: b               #0xbe06b8
    // 0xbe06a4: LoadField: r1 = r0->field_7
    //     0xbe06a4: ldur            w1, [x0, #7]
    // 0xbe06a8: cbnz            w1, #0xbe06b4
    // 0xbe06ac: r0 = false
    //     0xbe06ac: add             x0, NULL, #0x30  ; false
    // 0xbe06b0: b               #0xbe06b8
    // 0xbe06b4: r0 = true
    //     0xbe06b4: add             x0, NULL, #0x20  ; true
    // 0xbe06b8: cmp             w0, NULL
    // 0xbe06bc: b.eq            #0xbe0828
    // 0xbe06c0: tbnz            w0, #4, #0xbe0828
    // 0xbe06c4: cmp             w4, NULL
    // 0xbe06c8: b.ne            #0xbe06d4
    // 0xbe06cc: r0 = Null
    //     0xbe06cc: mov             x0, NULL
    // 0xbe06d0: b               #0xbe0714
    // 0xbe06d4: LoadField: r0 = r4->field_b
    //     0xbe06d4: ldur            w0, [x4, #0xb]
    // 0xbe06d8: r6 = LoadInt32Instr(r5)
    //     0xbe06d8: sbfx            x6, x5, #1, #0x1f
    //     0xbe06dc: tbz             w5, #0, #0xbe06e4
    //     0xbe06e0: ldur            x6, [x5, #7]
    // 0xbe06e4: r1 = LoadInt32Instr(r0)
    //     0xbe06e4: sbfx            x1, x0, #1, #0x1f
    // 0xbe06e8: mov             x0, x1
    // 0xbe06ec: mov             x1, x6
    // 0xbe06f0: cmp             x1, x0
    // 0xbe06f4: b.hs            #0xbe1634
    // 0xbe06f8: LoadField: r0 = r4->field_f
    //     0xbe06f8: ldur            w0, [x4, #0xf]
    // 0xbe06fc: DecompressPointer r0
    //     0xbe06fc: add             x0, x0, HEAP, lsl #32
    // 0xbe0700: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbe0700: add             x16, x0, x6, lsl #2
    //     0xbe0704: ldur            w1, [x16, #0xf]
    // 0xbe0708: DecompressPointer r1
    //     0xbe0708: add             x1, x1, HEAP, lsl #32
    // 0xbe070c: LoadField: r0 = r1->field_b
    //     0xbe070c: ldur            w0, [x1, #0xb]
    // 0xbe0710: DecompressPointer r0
    //     0xbe0710: add             x0, x0, HEAP, lsl #32
    // 0xbe0714: cmp             w0, NULL
    // 0xbe0718: b.ne            #0xbe0720
    // 0xbe071c: r0 = ""
    //     0xbe071c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe0720: ldr             x1, [fp, #0x18]
    // 0xbe0724: stur            x0, [fp, #-0x10]
    // 0xbe0728: r0 = of()
    //     0xbe0728: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe072c: LoadField: r1 = r0->field_87
    //     0xbe072c: ldur            w1, [x0, #0x87]
    // 0xbe0730: DecompressPointer r1
    //     0xbe0730: add             x1, x1, HEAP, lsl #32
    // 0xbe0734: LoadField: r0 = r1->field_2b
    //     0xbe0734: ldur            w0, [x1, #0x2b]
    // 0xbe0738: DecompressPointer r0
    //     0xbe0738: add             x0, x0, HEAP, lsl #32
    // 0xbe073c: r16 = 14.000000
    //     0xbe073c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbe0740: ldr             x16, [x16, #0x1d8]
    // 0xbe0744: r30 = Instance_Color
    //     0xbe0744: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe0748: stp             lr, x16, [SP]
    // 0xbe074c: mov             x1, x0
    // 0xbe0750: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe0750: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe0754: ldr             x4, [x4, #0xaa0]
    // 0xbe0758: r0 = copyWith()
    //     0xbe0758: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe075c: stur            x0, [fp, #-0x18]
    // 0xbe0760: r0 = HtmlWidget()
    //     0xbe0760: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xbe0764: mov             x1, x0
    // 0xbe0768: ldur            x0, [fp, #-0x10]
    // 0xbe076c: stur            x1, [fp, #-0x28]
    // 0xbe0770: StoreField: r1->field_1f = r0
    //     0xbe0770: stur            w0, [x1, #0x1f]
    // 0xbe0774: r3 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xbe0774: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xbe0778: ldr             x3, [x3, #0x1e0]
    // 0xbe077c: StoreField: r1->field_23 = r3
    //     0xbe077c: stur            w3, [x1, #0x23]
    // 0xbe0780: r4 = Instance_ColumnMode
    //     0xbe0780: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xbe0784: ldr             x4, [x4, #0x1e8]
    // 0xbe0788: StoreField: r1->field_3b = r4
    //     0xbe0788: stur            w4, [x1, #0x3b]
    // 0xbe078c: ldur            x0, [fp, #-0x18]
    // 0xbe0790: StoreField: r1->field_3f = r0
    //     0xbe0790: stur            w0, [x1, #0x3f]
    // 0xbe0794: r0 = Padding()
    //     0xbe0794: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe0798: r5 = Instance_EdgeInsets
    //     0xbe0798: add             x5, PP, #0x52, lsl #12  ; [pp+0x52b30] Obj!EdgeInsets@d588e1
    //     0xbe079c: ldr             x5, [x5, #0xb30]
    // 0xbe07a0: stur            x0, [fp, #-0x10]
    // 0xbe07a4: StoreField: r0->field_f = r5
    //     0xbe07a4: stur            w5, [x0, #0xf]
    // 0xbe07a8: ldur            x1, [fp, #-0x28]
    // 0xbe07ac: StoreField: r0->field_b = r1
    //     0xbe07ac: stur            w1, [x0, #0xb]
    // 0xbe07b0: ldur            x2, [fp, #-0x20]
    // 0xbe07b4: LoadField: r1 = r2->field_b
    //     0xbe07b4: ldur            w1, [x2, #0xb]
    // 0xbe07b8: LoadField: r3 = r2->field_f
    //     0xbe07b8: ldur            w3, [x2, #0xf]
    // 0xbe07bc: DecompressPointer r3
    //     0xbe07bc: add             x3, x3, HEAP, lsl #32
    // 0xbe07c0: LoadField: r4 = r3->field_b
    //     0xbe07c0: ldur            w4, [x3, #0xb]
    // 0xbe07c4: r3 = LoadInt32Instr(r1)
    //     0xbe07c4: sbfx            x3, x1, #1, #0x1f
    // 0xbe07c8: stur            x3, [fp, #-0x38]
    // 0xbe07cc: r1 = LoadInt32Instr(r4)
    //     0xbe07cc: sbfx            x1, x4, #1, #0x1f
    // 0xbe07d0: cmp             x3, x1
    // 0xbe07d4: b.ne            #0xbe07e0
    // 0xbe07d8: mov             x1, x2
    // 0xbe07dc: r0 = _growToNextCapacity()
    //     0xbe07dc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbe07e0: ldur            x2, [fp, #-0x20]
    // 0xbe07e4: ldur            x3, [fp, #-0x38]
    // 0xbe07e8: add             x0, x3, #1
    // 0xbe07ec: lsl             x1, x0, #1
    // 0xbe07f0: StoreField: r2->field_b = r1
    //     0xbe07f0: stur            w1, [x2, #0xb]
    // 0xbe07f4: LoadField: r1 = r2->field_f
    //     0xbe07f4: ldur            w1, [x2, #0xf]
    // 0xbe07f8: DecompressPointer r1
    //     0xbe07f8: add             x1, x1, HEAP, lsl #32
    // 0xbe07fc: ldur            x0, [fp, #-0x10]
    // 0xbe0800: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbe0800: add             x25, x1, x3, lsl #2
    //     0xbe0804: add             x25, x25, #0xf
    //     0xbe0808: str             w0, [x25]
    //     0xbe080c: tbz             w0, #0, #0xbe0828
    //     0xbe0810: ldurb           w16, [x1, #-1]
    //     0xbe0814: ldurb           w17, [x0, #-1]
    //     0xbe0818: and             x16, x17, x16, lsr #2
    //     0xbe081c: tst             x16, HEAP, lsr #32
    //     0xbe0820: b.eq            #0xbe0828
    //     0xbe0824: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe0828: ldur            x3, [fp, #-8]
    // 0xbe082c: LoadField: r0 = r3->field_f
    //     0xbe082c: ldur            w0, [x3, #0xf]
    // 0xbe0830: DecompressPointer r0
    //     0xbe0830: add             x0, x0, HEAP, lsl #32
    // 0xbe0834: LoadField: r1 = r0->field_b
    //     0xbe0834: ldur            w1, [x0, #0xb]
    // 0xbe0838: DecompressPointer r1
    //     0xbe0838: add             x1, x1, HEAP, lsl #32
    // 0xbe083c: cmp             w1, NULL
    // 0xbe0840: b.eq            #0xbe1638
    // 0xbe0844: LoadField: r4 = r1->field_b
    //     0xbe0844: ldur            w4, [x1, #0xb]
    // 0xbe0848: DecompressPointer r4
    //     0xbe0848: add             x4, x4, HEAP, lsl #32
    // 0xbe084c: cmp             w4, NULL
    // 0xbe0850: b.ne            #0xbe0860
    // 0xbe0854: ldr             x5, [fp, #0x10]
    // 0xbe0858: r0 = Null
    //     0xbe0858: mov             x0, NULL
    // 0xbe085c: b               #0xbe08c8
    // 0xbe0860: ldr             x5, [fp, #0x10]
    // 0xbe0864: LoadField: r0 = r4->field_b
    //     0xbe0864: ldur            w0, [x4, #0xb]
    // 0xbe0868: r6 = LoadInt32Instr(r5)
    //     0xbe0868: sbfx            x6, x5, #1, #0x1f
    //     0xbe086c: tbz             w5, #0, #0xbe0874
    //     0xbe0870: ldur            x6, [x5, #7]
    // 0xbe0874: r1 = LoadInt32Instr(r0)
    //     0xbe0874: sbfx            x1, x0, #1, #0x1f
    // 0xbe0878: mov             x0, x1
    // 0xbe087c: mov             x1, x6
    // 0xbe0880: cmp             x1, x0
    // 0xbe0884: b.hs            #0xbe163c
    // 0xbe0888: LoadField: r0 = r4->field_f
    //     0xbe0888: ldur            w0, [x4, #0xf]
    // 0xbe088c: DecompressPointer r0
    //     0xbe088c: add             x0, x0, HEAP, lsl #32
    // 0xbe0890: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbe0890: add             x16, x0, x6, lsl #2
    //     0xbe0894: ldur            w1, [x16, #0xf]
    // 0xbe0898: DecompressPointer r1
    //     0xbe0898: add             x1, x1, HEAP, lsl #32
    // 0xbe089c: LoadField: r0 = r1->field_f
    //     0xbe089c: ldur            w0, [x1, #0xf]
    // 0xbe08a0: DecompressPointer r0
    //     0xbe08a0: add             x0, x0, HEAP, lsl #32
    // 0xbe08a4: cmp             w0, NULL
    // 0xbe08a8: b.ne            #0xbe08b4
    // 0xbe08ac: r0 = Null
    //     0xbe08ac: mov             x0, NULL
    // 0xbe08b0: b               #0xbe08c8
    // 0xbe08b4: LoadField: r1 = r0->field_7
    //     0xbe08b4: ldur            w1, [x0, #7]
    // 0xbe08b8: cbnz            w1, #0xbe08c4
    // 0xbe08bc: r0 = false
    //     0xbe08bc: add             x0, NULL, #0x30  ; false
    // 0xbe08c0: b               #0xbe08c8
    // 0xbe08c4: r0 = true
    //     0xbe08c4: add             x0, NULL, #0x20  ; true
    // 0xbe08c8: cmp             w0, NULL
    // 0xbe08cc: b.ne            #0xbe08d4
    // 0xbe08d0: r0 = false
    //     0xbe08d0: add             x0, NULL, #0x30  ; false
    // 0xbe08d4: stur            x0, [fp, #-0x10]
    // 0xbe08d8: r16 = <EdgeInsets>
    //     0xbe08d8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbe08dc: ldr             x16, [x16, #0xda0]
    // 0xbe08e0: r30 = Instance_EdgeInsets
    //     0xbe08e0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbe08e4: ldr             lr, [lr, #0x1f0]
    // 0xbe08e8: stp             lr, x16, [SP]
    // 0xbe08ec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe08ec: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe08f0: r0 = all()
    //     0xbe08f0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe08f4: r1 = Instance_Color
    //     0xbe08f4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe08f8: d0 = 0.400000
    //     0xbe08f8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbe08fc: stur            x0, [fp, #-0x18]
    // 0xbe0900: r0 = withOpacity()
    //     0xbe0900: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbe0904: stur            x0, [fp, #-0x28]
    // 0xbe0908: r0 = BorderSide()
    //     0xbe0908: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbe090c: mov             x1, x0
    // 0xbe0910: ldur            x0, [fp, #-0x28]
    // 0xbe0914: stur            x1, [fp, #-0x30]
    // 0xbe0918: StoreField: r1->field_7 = r0
    //     0xbe0918: stur            w0, [x1, #7]
    // 0xbe091c: d0 = 1.000000
    //     0xbe091c: fmov            d0, #1.00000000
    // 0xbe0920: StoreField: r1->field_b = d0
    //     0xbe0920: stur            d0, [x1, #0xb]
    // 0xbe0924: r6 = Instance_BorderStyle
    //     0xbe0924: add             x6, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbe0928: ldr             x6, [x6, #0xf68]
    // 0xbe092c: StoreField: r1->field_13 = r6
    //     0xbe092c: stur            w6, [x1, #0x13]
    // 0xbe0930: d1 = -1.000000
    //     0xbe0930: fmov            d1, #-1.00000000
    // 0xbe0934: ArrayStore: r1[0] = d1  ; List_8
    //     0xbe0934: stur            d1, [x1, #0x17]
    // 0xbe0938: r0 = RoundedRectangleBorder()
    //     0xbe0938: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xbe093c: r7 = Instance_BorderRadius
    //     0xbe093c: add             x7, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbe0940: ldr             x7, [x7, #0xf70]
    // 0xbe0944: StoreField: r0->field_b = r7
    //     0xbe0944: stur            w7, [x0, #0xb]
    // 0xbe0948: ldur            x1, [fp, #-0x30]
    // 0xbe094c: StoreField: r0->field_7 = r1
    //     0xbe094c: stur            w1, [x0, #7]
    // 0xbe0950: r16 = <RoundedRectangleBorder>
    //     0xbe0950: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbe0954: ldr             x16, [x16, #0xf78]
    // 0xbe0958: stp             x0, x16, [SP]
    // 0xbe095c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe095c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe0960: r0 = all()
    //     0xbe0960: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe0964: stur            x0, [fp, #-0x28]
    // 0xbe0968: r0 = ButtonStyle()
    //     0xbe0968: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbe096c: mov             x1, x0
    // 0xbe0970: ldur            x0, [fp, #-0x18]
    // 0xbe0974: stur            x1, [fp, #-0x30]
    // 0xbe0978: StoreField: r1->field_23 = r0
    //     0xbe0978: stur            w0, [x1, #0x23]
    // 0xbe097c: ldur            x0, [fp, #-0x28]
    // 0xbe0980: StoreField: r1->field_43 = r0
    //     0xbe0980: stur            w0, [x1, #0x43]
    // 0xbe0984: r0 = TextButtonThemeData()
    //     0xbe0984: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbe0988: mov             x2, x0
    // 0xbe098c: ldur            x0, [fp, #-0x30]
    // 0xbe0990: stur            x2, [fp, #-0x18]
    // 0xbe0994: StoreField: r2->field_7 = r0
    //     0xbe0994: stur            w0, [x2, #7]
    // 0xbe0998: ldur            x8, [fp, #-8]
    // 0xbe099c: LoadField: r0 = r8->field_f
    //     0xbe099c: ldur            w0, [x8, #0xf]
    // 0xbe09a0: DecompressPointer r0
    //     0xbe09a0: add             x0, x0, HEAP, lsl #32
    // 0xbe09a4: LoadField: r1 = r0->field_b
    //     0xbe09a4: ldur            w1, [x0, #0xb]
    // 0xbe09a8: DecompressPointer r1
    //     0xbe09a8: add             x1, x1, HEAP, lsl #32
    // 0xbe09ac: cmp             w1, NULL
    // 0xbe09b0: b.eq            #0xbe1640
    // 0xbe09b4: LoadField: r3 = r1->field_b
    //     0xbe09b4: ldur            w3, [x1, #0xb]
    // 0xbe09b8: DecompressPointer r3
    //     0xbe09b8: add             x3, x3, HEAP, lsl #32
    // 0xbe09bc: cmp             w3, NULL
    // 0xbe09c0: b.ne            #0xbe09cc
    // 0xbe09c4: r0 = Null
    //     0xbe09c4: mov             x0, NULL
    // 0xbe09c8: b               #0xbe0a10
    // 0xbe09cc: ldr             x9, [fp, #0x10]
    // 0xbe09d0: LoadField: r0 = r3->field_b
    //     0xbe09d0: ldur            w0, [x3, #0xb]
    // 0xbe09d4: r4 = LoadInt32Instr(r9)
    //     0xbe09d4: sbfx            x4, x9, #1, #0x1f
    //     0xbe09d8: tbz             w9, #0, #0xbe09e0
    //     0xbe09dc: ldur            x4, [x9, #7]
    // 0xbe09e0: r1 = LoadInt32Instr(r0)
    //     0xbe09e0: sbfx            x1, x0, #1, #0x1f
    // 0xbe09e4: mov             x0, x1
    // 0xbe09e8: mov             x1, x4
    // 0xbe09ec: cmp             x1, x0
    // 0xbe09f0: b.hs            #0xbe1644
    // 0xbe09f4: LoadField: r0 = r3->field_f
    //     0xbe09f4: ldur            w0, [x3, #0xf]
    // 0xbe09f8: DecompressPointer r0
    //     0xbe09f8: add             x0, x0, HEAP, lsl #32
    // 0xbe09fc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbe09fc: add             x16, x0, x4, lsl #2
    //     0xbe0a00: ldur            w1, [x16, #0xf]
    // 0xbe0a04: DecompressPointer r1
    //     0xbe0a04: add             x1, x1, HEAP, lsl #32
    // 0xbe0a08: LoadField: r0 = r1->field_f
    //     0xbe0a08: ldur            w0, [x1, #0xf]
    // 0xbe0a0c: DecompressPointer r0
    //     0xbe0a0c: add             x0, x0, HEAP, lsl #32
    // 0xbe0a10: cmp             w0, NULL
    // 0xbe0a14: b.ne            #0xbe0a1c
    // 0xbe0a18: r0 = ""
    //     0xbe0a18: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe0a1c: ldur            x3, [fp, #-0x10]
    // 0xbe0a20: ldur            x1, [fp, #-0x20]
    // 0xbe0a24: r4 = LoadClassIdInstr(r0)
    //     0xbe0a24: ldur            x4, [x0, #-1]
    //     0xbe0a28: ubfx            x4, x4, #0xc, #0x14
    // 0xbe0a2c: str             x0, [SP]
    // 0xbe0a30: mov             x0, x4
    // 0xbe0a34: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbe0a34: sub             lr, x0, #1, lsl #12
    //     0xbe0a38: ldr             lr, [x21, lr, lsl #3]
    //     0xbe0a3c: blr             lr
    // 0xbe0a40: ldr             x1, [fp, #0x18]
    // 0xbe0a44: stur            x0, [fp, #-0x28]
    // 0xbe0a48: r0 = of()
    //     0xbe0a48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe0a4c: LoadField: r1 = r0->field_87
    //     0xbe0a4c: ldur            w1, [x0, #0x87]
    // 0xbe0a50: DecompressPointer r1
    //     0xbe0a50: add             x1, x1, HEAP, lsl #32
    // 0xbe0a54: LoadField: r0 = r1->field_7
    //     0xbe0a54: ldur            w0, [x1, #7]
    // 0xbe0a58: DecompressPointer r0
    //     0xbe0a58: add             x0, x0, HEAP, lsl #32
    // 0xbe0a5c: r16 = Instance_Color
    //     0xbe0a5c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe0a60: r30 = 12.000000
    //     0xbe0a60: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe0a64: ldr             lr, [lr, #0x9e8]
    // 0xbe0a68: stp             lr, x16, [SP]
    // 0xbe0a6c: mov             x1, x0
    // 0xbe0a70: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbe0a70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbe0a74: ldr             x4, [x4, #0x9b8]
    // 0xbe0a78: r0 = copyWith()
    //     0xbe0a78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe0a7c: stur            x0, [fp, #-0x30]
    // 0xbe0a80: r0 = Text()
    //     0xbe0a80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe0a84: mov             x3, x0
    // 0xbe0a88: ldur            x0, [fp, #-0x28]
    // 0xbe0a8c: stur            x3, [fp, #-0x40]
    // 0xbe0a90: StoreField: r3->field_b = r0
    //     0xbe0a90: stur            w0, [x3, #0xb]
    // 0xbe0a94: ldur            x0, [fp, #-0x30]
    // 0xbe0a98: StoreField: r3->field_13 = r0
    //     0xbe0a98: stur            w0, [x3, #0x13]
    // 0xbe0a9c: r1 = Function '<anonymous closure>':.
    //     0xbe0a9c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53788] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xbe0aa0: ldr             x1, [x1, #0x788]
    // 0xbe0aa4: r2 = Null
    //     0xbe0aa4: mov             x2, NULL
    // 0xbe0aa8: r0 = AllocateClosure()
    //     0xbe0aa8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe0aac: stur            x0, [fp, #-0x28]
    // 0xbe0ab0: r0 = TextButton()
    //     0xbe0ab0: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbe0ab4: mov             x1, x0
    // 0xbe0ab8: ldur            x0, [fp, #-0x28]
    // 0xbe0abc: stur            x1, [fp, #-0x30]
    // 0xbe0ac0: StoreField: r1->field_b = r0
    //     0xbe0ac0: stur            w0, [x1, #0xb]
    // 0xbe0ac4: r0 = false
    //     0xbe0ac4: add             x0, NULL, #0x30  ; false
    // 0xbe0ac8: StoreField: r1->field_27 = r0
    //     0xbe0ac8: stur            w0, [x1, #0x27]
    // 0xbe0acc: r10 = true
    //     0xbe0acc: add             x10, NULL, #0x20  ; true
    // 0xbe0ad0: StoreField: r1->field_2f = r10
    //     0xbe0ad0: stur            w10, [x1, #0x2f]
    // 0xbe0ad4: ldur            x2, [fp, #-0x40]
    // 0xbe0ad8: StoreField: r1->field_37 = r2
    //     0xbe0ad8: stur            w2, [x1, #0x37]
    // 0xbe0adc: r0 = TextButtonTheme()
    //     0xbe0adc: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbe0ae0: mov             x1, x0
    // 0xbe0ae4: ldur            x0, [fp, #-0x18]
    // 0xbe0ae8: stur            x1, [fp, #-0x28]
    // 0xbe0aec: StoreField: r1->field_f = r0
    //     0xbe0aec: stur            w0, [x1, #0xf]
    // 0xbe0af0: ldur            x0, [fp, #-0x30]
    // 0xbe0af4: StoreField: r1->field_b = r0
    //     0xbe0af4: stur            w0, [x1, #0xb]
    // 0xbe0af8: r0 = Padding()
    //     0xbe0af8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe0afc: mov             x1, x0
    // 0xbe0b00: r0 = Instance_EdgeInsets
    //     0xbe0b00: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fce8] Obj!EdgeInsets@d58131
    //     0xbe0b04: ldr             x0, [x0, #0xce8]
    // 0xbe0b08: stur            x1, [fp, #-0x18]
    // 0xbe0b0c: StoreField: r1->field_f = r0
    //     0xbe0b0c: stur            w0, [x1, #0xf]
    // 0xbe0b10: ldur            x0, [fp, #-0x28]
    // 0xbe0b14: StoreField: r1->field_b = r0
    //     0xbe0b14: stur            w0, [x1, #0xb]
    // 0xbe0b18: r0 = Visibility()
    //     0xbe0b18: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbe0b1c: mov             x2, x0
    // 0xbe0b20: ldur            x0, [fp, #-0x18]
    // 0xbe0b24: stur            x2, [fp, #-0x28]
    // 0xbe0b28: StoreField: r2->field_b = r0
    //     0xbe0b28: stur            w0, [x2, #0xb]
    // 0xbe0b2c: r11 = Instance_SizedBox
    //     0xbe0b2c: ldr             x11, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbe0b30: StoreField: r2->field_f = r11
    //     0xbe0b30: stur            w11, [x2, #0xf]
    // 0xbe0b34: ldur            x0, [fp, #-0x10]
    // 0xbe0b38: StoreField: r2->field_13 = r0
    //     0xbe0b38: stur            w0, [x2, #0x13]
    // 0xbe0b3c: r12 = false
    //     0xbe0b3c: add             x12, NULL, #0x30  ; false
    // 0xbe0b40: ArrayStore: r2[0] = r12  ; List_4
    //     0xbe0b40: stur            w12, [x2, #0x17]
    // 0xbe0b44: StoreField: r2->field_1b = r12
    //     0xbe0b44: stur            w12, [x2, #0x1b]
    // 0xbe0b48: StoreField: r2->field_1f = r12
    //     0xbe0b48: stur            w12, [x2, #0x1f]
    // 0xbe0b4c: StoreField: r2->field_23 = r12
    //     0xbe0b4c: stur            w12, [x2, #0x23]
    // 0xbe0b50: StoreField: r2->field_27 = r12
    //     0xbe0b50: stur            w12, [x2, #0x27]
    // 0xbe0b54: StoreField: r2->field_2b = r12
    //     0xbe0b54: stur            w12, [x2, #0x2b]
    // 0xbe0b58: ldur            x0, [fp, #-0x20]
    // 0xbe0b5c: LoadField: r1 = r0->field_b
    //     0xbe0b5c: ldur            w1, [x0, #0xb]
    // 0xbe0b60: LoadField: r3 = r0->field_f
    //     0xbe0b60: ldur            w3, [x0, #0xf]
    // 0xbe0b64: DecompressPointer r3
    //     0xbe0b64: add             x3, x3, HEAP, lsl #32
    // 0xbe0b68: LoadField: r4 = r3->field_b
    //     0xbe0b68: ldur            w4, [x3, #0xb]
    // 0xbe0b6c: r3 = LoadInt32Instr(r1)
    //     0xbe0b6c: sbfx            x3, x1, #1, #0x1f
    // 0xbe0b70: stur            x3, [fp, #-0x38]
    // 0xbe0b74: r1 = LoadInt32Instr(r4)
    //     0xbe0b74: sbfx            x1, x4, #1, #0x1f
    // 0xbe0b78: cmp             x3, x1
    // 0xbe0b7c: b.ne            #0xbe0b88
    // 0xbe0b80: mov             x1, x0
    // 0xbe0b84: r0 = _growToNextCapacity()
    //     0xbe0b84: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbe0b88: ldur            x2, [fp, #-0x20]
    // 0xbe0b8c: ldur            x3, [fp, #-0x38]
    // 0xbe0b90: add             x0, x3, #1
    // 0xbe0b94: lsl             x1, x0, #1
    // 0xbe0b98: StoreField: r2->field_b = r1
    //     0xbe0b98: stur            w1, [x2, #0xb]
    // 0xbe0b9c: LoadField: r1 = r2->field_f
    //     0xbe0b9c: ldur            w1, [x2, #0xf]
    // 0xbe0ba0: DecompressPointer r1
    //     0xbe0ba0: add             x1, x1, HEAP, lsl #32
    // 0xbe0ba4: ldur            x0, [fp, #-0x28]
    // 0xbe0ba8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbe0ba8: add             x25, x1, x3, lsl #2
    //     0xbe0bac: add             x25, x25, #0xf
    //     0xbe0bb0: str             w0, [x25]
    //     0xbe0bb4: tbz             w0, #0, #0xbe0bd0
    //     0xbe0bb8: ldurb           w16, [x1, #-1]
    //     0xbe0bbc: ldurb           w17, [x0, #-1]
    //     0xbe0bc0: and             x16, x17, x16, lsr #2
    //     0xbe0bc4: tst             x16, HEAP, lsr #32
    //     0xbe0bc8: b.eq            #0xbe0bd0
    //     0xbe0bcc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe0bd0: r0 = Column()
    //     0xbe0bd0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbe0bd4: r13 = Instance_Axis
    //     0xbe0bd4: ldr             x13, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbe0bd8: StoreField: r0->field_f = r13
    //     0xbe0bd8: stur            w13, [x0, #0xf]
    // 0xbe0bdc: r14 = Instance_MainAxisAlignment
    //     0xbe0bdc: add             x14, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbe0be0: ldr             x14, [x14, #0xa08]
    // 0xbe0be4: StoreField: r0->field_13 = r14
    //     0xbe0be4: stur            w14, [x0, #0x13]
    // 0xbe0be8: r19 = Instance_MainAxisSize
    //     0xbe0be8: add             x19, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbe0bec: ldr             x19, [x19, #0xa10]
    // 0xbe0bf0: ArrayStore: r0[0] = r19  ; List_4
    //     0xbe0bf0: stur            w19, [x0, #0x17]
    // 0xbe0bf4: r20 = Instance_CrossAxisAlignment
    //     0xbe0bf4: add             x20, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbe0bf8: ldr             x20, [x20, #0x890]
    // 0xbe0bfc: StoreField: r0->field_1b = r20
    //     0xbe0bfc: stur            w20, [x0, #0x1b]
    // 0xbe0c00: r23 = Instance_VerticalDirection
    //     0xbe0c00: add             x23, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe0c04: ldr             x23, [x23, #0xa20]
    // 0xbe0c08: StoreField: r0->field_23 = r23
    //     0xbe0c08: stur            w23, [x0, #0x23]
    // 0xbe0c0c: r24 = Instance_Clip
    //     0xbe0c0c: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe0c10: ldr             x24, [x24, #0x38]
    // 0xbe0c14: StoreField: r0->field_2b = r24
    //     0xbe0c14: stur            w24, [x0, #0x2b]
    // 0xbe0c18: StoreField: r0->field_2f = rZR
    //     0xbe0c18: stur            xzr, [x0, #0x2f]
    // 0xbe0c1c: ldur            x1, [fp, #-0x20]
    // 0xbe0c20: StoreField: r0->field_b = r1
    //     0xbe0c20: stur            w1, [x0, #0xb]
    // 0xbe0c24: b               #0xbe1600
    // 0xbe0c28: ldr             x9, [fp, #0x10]
    // 0xbe0c2c: ldur            x8, [fp, #-8]
    // 0xbe0c30: r10 = true
    //     0xbe0c30: add             x10, NULL, #0x20  ; true
    // 0xbe0c34: r5 = Instance_EdgeInsets
    //     0xbe0c34: add             x5, PP, #0x52, lsl #12  ; [pp+0x52b30] Obj!EdgeInsets@d588e1
    //     0xbe0c38: ldr             x5, [x5, #0xb30]
    // 0xbe0c3c: r2 = "------"
    //     0xbe0c3c: add             x2, PP, #0x52, lsl #12  ; [pp+0x52b40] "------"
    //     0xbe0c40: ldr             x2, [x2, #0xb40]
    // 0xbe0c44: r7 = Instance_BorderRadius
    //     0xbe0c44: add             x7, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbe0c48: ldr             x7, [x7, #0xf70]
    // 0xbe0c4c: r20 = Instance_CrossAxisAlignment
    //     0xbe0c4c: add             x20, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbe0c50: ldr             x20, [x20, #0x890]
    // 0xbe0c54: r12 = false
    //     0xbe0c54: add             x12, NULL, #0x30  ; false
    // 0xbe0c58: r14 = Instance_MainAxisAlignment
    //     0xbe0c58: add             x14, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbe0c5c: ldr             x14, [x14, #0xa08]
    // 0xbe0c60: r19 = Instance_MainAxisSize
    //     0xbe0c60: add             x19, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbe0c64: ldr             x19, [x19, #0xa10]
    // 0xbe0c68: r23 = Instance_VerticalDirection
    //     0xbe0c68: add             x23, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe0c6c: ldr             x23, [x23, #0xa20]
    // 0xbe0c70: r13 = Instance_Axis
    //     0xbe0c70: ldr             x13, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbe0c74: r11 = Instance_SizedBox
    //     0xbe0c74: ldr             x11, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbe0c78: r6 = Instance_BorderStyle
    //     0xbe0c78: add             x6, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbe0c7c: ldr             x6, [x6, #0xf68]
    // 0xbe0c80: r24 = Instance_Clip
    //     0xbe0c80: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe0c84: ldr             x24, [x24, #0x38]
    // 0xbe0c88: r3 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xbe0c88: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xbe0c8c: ldr             x3, [x3, #0x1e0]
    // 0xbe0c90: r4 = Instance_ColumnMode
    //     0xbe0c90: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xbe0c94: ldr             x4, [x4, #0x1e8]
    // 0xbe0c98: d0 = 1.000000
    //     0xbe0c98: fmov            d0, #1.00000000
    // 0xbe0c9c: d1 = -1.000000
    //     0xbe0c9c: fmov            d1, #-1.00000000
    // 0xbe0ca0: LoadField: r0 = r8->field_f
    //     0xbe0ca0: ldur            w0, [x8, #0xf]
    // 0xbe0ca4: DecompressPointer r0
    //     0xbe0ca4: add             x0, x0, HEAP, lsl #32
    // 0xbe0ca8: LoadField: r1 = r0->field_b
    //     0xbe0ca8: ldur            w1, [x0, #0xb]
    // 0xbe0cac: DecompressPointer r1
    //     0xbe0cac: add             x1, x1, HEAP, lsl #32
    // 0xbe0cb0: cmp             w1, NULL
    // 0xbe0cb4: b.eq            #0xbe1648
    // 0xbe0cb8: LoadField: r25 = r1->field_b
    //     0xbe0cb8: ldur            w25, [x1, #0xb]
    // 0xbe0cbc: DecompressPointer r25
    //     0xbe0cbc: add             x25, x25, HEAP, lsl #32
    // 0xbe0cc0: cmp             w25, NULL
    // 0xbe0cc4: b.ne            #0xbe0cd0
    // 0xbe0cc8: r0 = Null
    //     0xbe0cc8: mov             x0, NULL
    // 0xbe0ccc: b               #0xbe0d10
    // 0xbe0cd0: LoadField: r0 = r25->field_b
    //     0xbe0cd0: ldur            w0, [x25, #0xb]
    // 0xbe0cd4: r1 = LoadInt32Instr(r9)
    //     0xbe0cd4: sbfx            x1, x9, #1, #0x1f
    //     0xbe0cd8: tbz             w9, #0, #0xbe0ce0
    //     0xbe0cdc: ldur            x1, [x9, #7]
    // 0xbe0ce0: r2 = LoadInt32Instr(r0)
    //     0xbe0ce0: sbfx            x2, x0, #1, #0x1f
    // 0xbe0ce4: mov             x0, x2
    // 0xbe0ce8: mov             x2, x1
    // 0xbe0cec: cmp             x1, x0
    // 0xbe0cf0: b.hs            #0xbe164c
    // 0xbe0cf4: LoadField: r0 = r25->field_f
    //     0xbe0cf4: ldur            w0, [x25, #0xf]
    // 0xbe0cf8: DecompressPointer r0
    //     0xbe0cf8: add             x0, x0, HEAP, lsl #32
    // 0xbe0cfc: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xbe0cfc: add             x16, x0, x2, lsl #2
    //     0xbe0d00: ldur            w1, [x16, #0xf]
    // 0xbe0d04: DecompressPointer r1
    //     0xbe0d04: add             x1, x1, HEAP, lsl #32
    // 0xbe0d08: LoadField: r0 = r1->field_7
    //     0xbe0d08: ldur            w0, [x1, #7]
    // 0xbe0d0c: DecompressPointer r0
    //     0xbe0d0c: add             x0, x0, HEAP, lsl #32
    // 0xbe0d10: cmp             w0, NULL
    // 0xbe0d14: b.ne            #0xbe0d1c
    // 0xbe0d18: r0 = ""
    //     0xbe0d18: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe0d1c: ldr             x1, [fp, #0x18]
    // 0xbe0d20: stur            x0, [fp, #-0x10]
    // 0xbe0d24: r0 = of()
    //     0xbe0d24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe0d28: LoadField: r1 = r0->field_87
    //     0xbe0d28: ldur            w1, [x0, #0x87]
    // 0xbe0d2c: DecompressPointer r1
    //     0xbe0d2c: add             x1, x1, HEAP, lsl #32
    // 0xbe0d30: LoadField: r0 = r1->field_7
    //     0xbe0d30: ldur            w0, [x1, #7]
    // 0xbe0d34: DecompressPointer r0
    //     0xbe0d34: add             x0, x0, HEAP, lsl #32
    // 0xbe0d38: stur            x0, [fp, #-0x18]
    // 0xbe0d3c: r1 = Instance_Color
    //     0xbe0d3c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe0d40: d0 = 0.700000
    //     0xbe0d40: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbe0d44: ldr             d0, [x17, #0xf48]
    // 0xbe0d48: r0 = withOpacity()
    //     0xbe0d48: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbe0d4c: r16 = 16.000000
    //     0xbe0d4c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbe0d50: ldr             x16, [x16, #0x188]
    // 0xbe0d54: stp             x0, x16, [SP]
    // 0xbe0d58: ldur            x1, [fp, #-0x18]
    // 0xbe0d5c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe0d5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe0d60: ldr             x4, [x4, #0xaa0]
    // 0xbe0d64: r0 = copyWith()
    //     0xbe0d64: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe0d68: stur            x0, [fp, #-0x18]
    // 0xbe0d6c: r0 = Text()
    //     0xbe0d6c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe0d70: mov             x1, x0
    // 0xbe0d74: ldur            x0, [fp, #-0x10]
    // 0xbe0d78: stur            x1, [fp, #-0x20]
    // 0xbe0d7c: StoreField: r1->field_b = r0
    //     0xbe0d7c: stur            w0, [x1, #0xb]
    // 0xbe0d80: ldur            x0, [fp, #-0x18]
    // 0xbe0d84: StoreField: r1->field_13 = r0
    //     0xbe0d84: stur            w0, [x1, #0x13]
    // 0xbe0d88: r0 = Padding()
    //     0xbe0d88: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe0d8c: mov             x2, x0
    // 0xbe0d90: r0 = Instance_EdgeInsets
    //     0xbe0d90: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b30] Obj!EdgeInsets@d588e1
    //     0xbe0d94: ldr             x0, [x0, #0xb30]
    // 0xbe0d98: stur            x2, [fp, #-0x10]
    // 0xbe0d9c: StoreField: r2->field_f = r0
    //     0xbe0d9c: stur            w0, [x2, #0xf]
    // 0xbe0da0: ldur            x1, [fp, #-0x20]
    // 0xbe0da4: StoreField: r2->field_b = r1
    //     0xbe0da4: stur            w1, [x2, #0xb]
    // 0xbe0da8: ldr             x1, [fp, #0x18]
    // 0xbe0dac: r0 = of()
    //     0xbe0dac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe0db0: LoadField: r1 = r0->field_87
    //     0xbe0db0: ldur            w1, [x0, #0x87]
    // 0xbe0db4: DecompressPointer r1
    //     0xbe0db4: add             x1, x1, HEAP, lsl #32
    // 0xbe0db8: LoadField: r0 = r1->field_7
    //     0xbe0db8: ldur            w0, [x1, #7]
    // 0xbe0dbc: DecompressPointer r0
    //     0xbe0dbc: add             x0, x0, HEAP, lsl #32
    // 0xbe0dc0: r16 = Instance_TextDecoration
    //     0xbe0dc0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xbe0dc4: ldr             x16, [x16, #0xe30]
    // 0xbe0dc8: r30 = 4.000000
    //     0xbe0dc8: add             lr, PP, #0x27, lsl #12  ; [pp+0x27838] 4
    //     0xbe0dcc: ldr             lr, [lr, #0x838]
    // 0xbe0dd0: stp             lr, x16, [SP, #0x18]
    // 0xbe0dd4: r16 = Instance_Color
    //     0xbe0dd4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe0dd8: r30 = 16.000000
    //     0xbe0dd8: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbe0ddc: ldr             lr, [lr, #0x188]
    // 0xbe0de0: stp             lr, x16, [SP, #8]
    // 0xbe0de4: r16 = Instance_Color
    //     0xbe0de4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe0de8: str             x16, [SP]
    // 0xbe0dec: mov             x1, x0
    // 0xbe0df0: r4 = const [0, 0x6, 0x5, 0x1, color, 0x5, decoration, 0x1, decorationColor, 0x3, decorationThickness, 0x2, fontSize, 0x4, null]
    //     0xbe0df0: add             x4, PP, #0x52, lsl #12  ; [pp+0x52b38] List(15) [0, 0x6, 0x5, 0x1, "color", 0x5, "decoration", 0x1, "decorationColor", 0x3, "decorationThickness", 0x2, "fontSize", 0x4, Null]
    //     0xbe0df4: ldr             x4, [x4, #0xb38]
    // 0xbe0df8: r0 = copyWith()
    //     0xbe0df8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe0dfc: stur            x0, [fp, #-0x18]
    // 0xbe0e00: r0 = Text()
    //     0xbe0e00: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe0e04: mov             x3, x0
    // 0xbe0e08: r0 = "------"
    //     0xbe0e08: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b40] "------"
    //     0xbe0e0c: ldr             x0, [x0, #0xb40]
    // 0xbe0e10: stur            x3, [fp, #-0x20]
    // 0xbe0e14: StoreField: r3->field_b = r0
    //     0xbe0e14: stur            w0, [x3, #0xb]
    // 0xbe0e18: ldur            x0, [fp, #-0x18]
    // 0xbe0e1c: StoreField: r3->field_13 = r0
    //     0xbe0e1c: stur            w0, [x3, #0x13]
    // 0xbe0e20: r1 = Null
    //     0xbe0e20: mov             x1, NULL
    // 0xbe0e24: r2 = 4
    //     0xbe0e24: movz            x2, #0x4
    // 0xbe0e28: r0 = AllocateArray()
    //     0xbe0e28: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe0e2c: mov             x2, x0
    // 0xbe0e30: ldur            x0, [fp, #-0x10]
    // 0xbe0e34: stur            x2, [fp, #-0x18]
    // 0xbe0e38: StoreField: r2->field_f = r0
    //     0xbe0e38: stur            w0, [x2, #0xf]
    // 0xbe0e3c: ldur            x0, [fp, #-0x20]
    // 0xbe0e40: StoreField: r2->field_13 = r0
    //     0xbe0e40: stur            w0, [x2, #0x13]
    // 0xbe0e44: r1 = <Widget>
    //     0xbe0e44: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe0e48: r0 = AllocateGrowableArray()
    //     0xbe0e48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe0e4c: mov             x2, x0
    // 0xbe0e50: ldur            x0, [fp, #-0x18]
    // 0xbe0e54: stur            x2, [fp, #-0x20]
    // 0xbe0e58: StoreField: r2->field_f = r0
    //     0xbe0e58: stur            w0, [x2, #0xf]
    // 0xbe0e5c: r0 = 4
    //     0xbe0e5c: movz            x0, #0x4
    // 0xbe0e60: StoreField: r2->field_b = r0
    //     0xbe0e60: stur            w0, [x2, #0xb]
    // 0xbe0e64: ldur            x3, [fp, #-8]
    // 0xbe0e68: LoadField: r0 = r3->field_f
    //     0xbe0e68: ldur            w0, [x3, #0xf]
    // 0xbe0e6c: DecompressPointer r0
    //     0xbe0e6c: add             x0, x0, HEAP, lsl #32
    // 0xbe0e70: LoadField: r1 = r0->field_b
    //     0xbe0e70: ldur            w1, [x0, #0xb]
    // 0xbe0e74: DecompressPointer r1
    //     0xbe0e74: add             x1, x1, HEAP, lsl #32
    // 0xbe0e78: cmp             w1, NULL
    // 0xbe0e7c: b.eq            #0xbe1650
    // 0xbe0e80: LoadField: r4 = r1->field_b
    //     0xbe0e80: ldur            w4, [x1, #0xb]
    // 0xbe0e84: DecompressPointer r4
    //     0xbe0e84: add             x4, x4, HEAP, lsl #32
    // 0xbe0e88: cmp             w4, NULL
    // 0xbe0e8c: b.ne            #0xbe0e9c
    // 0xbe0e90: ldr             x5, [fp, #0x10]
    // 0xbe0e94: r0 = Null
    //     0xbe0e94: mov             x0, NULL
    // 0xbe0e98: b               #0xbe0f04
    // 0xbe0e9c: ldr             x5, [fp, #0x10]
    // 0xbe0ea0: LoadField: r0 = r4->field_b
    //     0xbe0ea0: ldur            w0, [x4, #0xb]
    // 0xbe0ea4: r6 = LoadInt32Instr(r5)
    //     0xbe0ea4: sbfx            x6, x5, #1, #0x1f
    //     0xbe0ea8: tbz             w5, #0, #0xbe0eb0
    //     0xbe0eac: ldur            x6, [x5, #7]
    // 0xbe0eb0: r1 = LoadInt32Instr(r0)
    //     0xbe0eb0: sbfx            x1, x0, #1, #0x1f
    // 0xbe0eb4: mov             x0, x1
    // 0xbe0eb8: mov             x1, x6
    // 0xbe0ebc: cmp             x1, x0
    // 0xbe0ec0: b.hs            #0xbe1654
    // 0xbe0ec4: LoadField: r0 = r4->field_f
    //     0xbe0ec4: ldur            w0, [x4, #0xf]
    // 0xbe0ec8: DecompressPointer r0
    //     0xbe0ec8: add             x0, x0, HEAP, lsl #32
    // 0xbe0ecc: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbe0ecc: add             x16, x0, x6, lsl #2
    //     0xbe0ed0: ldur            w1, [x16, #0xf]
    // 0xbe0ed4: DecompressPointer r1
    //     0xbe0ed4: add             x1, x1, HEAP, lsl #32
    // 0xbe0ed8: LoadField: r0 = r1->field_b
    //     0xbe0ed8: ldur            w0, [x1, #0xb]
    // 0xbe0edc: DecompressPointer r0
    //     0xbe0edc: add             x0, x0, HEAP, lsl #32
    // 0xbe0ee0: cmp             w0, NULL
    // 0xbe0ee4: b.ne            #0xbe0ef0
    // 0xbe0ee8: r0 = Null
    //     0xbe0ee8: mov             x0, NULL
    // 0xbe0eec: b               #0xbe0f04
    // 0xbe0ef0: LoadField: r1 = r0->field_7
    //     0xbe0ef0: ldur            w1, [x0, #7]
    // 0xbe0ef4: cbnz            w1, #0xbe0f00
    // 0xbe0ef8: r0 = false
    //     0xbe0ef8: add             x0, NULL, #0x30  ; false
    // 0xbe0efc: b               #0xbe0f04
    // 0xbe0f00: r0 = true
    //     0xbe0f00: add             x0, NULL, #0x20  ; true
    // 0xbe0f04: cmp             w0, NULL
    // 0xbe0f08: b.eq            #0xbe1078
    // 0xbe0f0c: tbnz            w0, #4, #0xbe1078
    // 0xbe0f10: cmp             w4, NULL
    // 0xbe0f14: b.ne            #0xbe0f20
    // 0xbe0f18: r0 = Null
    //     0xbe0f18: mov             x0, NULL
    // 0xbe0f1c: b               #0xbe0f60
    // 0xbe0f20: LoadField: r0 = r4->field_b
    //     0xbe0f20: ldur            w0, [x4, #0xb]
    // 0xbe0f24: r6 = LoadInt32Instr(r5)
    //     0xbe0f24: sbfx            x6, x5, #1, #0x1f
    //     0xbe0f28: tbz             w5, #0, #0xbe0f30
    //     0xbe0f2c: ldur            x6, [x5, #7]
    // 0xbe0f30: r1 = LoadInt32Instr(r0)
    //     0xbe0f30: sbfx            x1, x0, #1, #0x1f
    // 0xbe0f34: mov             x0, x1
    // 0xbe0f38: mov             x1, x6
    // 0xbe0f3c: cmp             x1, x0
    // 0xbe0f40: b.hs            #0xbe1658
    // 0xbe0f44: LoadField: r0 = r4->field_f
    //     0xbe0f44: ldur            w0, [x4, #0xf]
    // 0xbe0f48: DecompressPointer r0
    //     0xbe0f48: add             x0, x0, HEAP, lsl #32
    // 0xbe0f4c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbe0f4c: add             x16, x0, x6, lsl #2
    //     0xbe0f50: ldur            w1, [x16, #0xf]
    // 0xbe0f54: DecompressPointer r1
    //     0xbe0f54: add             x1, x1, HEAP, lsl #32
    // 0xbe0f58: LoadField: r0 = r1->field_b
    //     0xbe0f58: ldur            w0, [x1, #0xb]
    // 0xbe0f5c: DecompressPointer r0
    //     0xbe0f5c: add             x0, x0, HEAP, lsl #32
    // 0xbe0f60: cmp             w0, NULL
    // 0xbe0f64: b.ne            #0xbe0f6c
    // 0xbe0f68: r0 = ""
    //     0xbe0f68: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe0f6c: ldr             x1, [fp, #0x18]
    // 0xbe0f70: stur            x0, [fp, #-0x10]
    // 0xbe0f74: r0 = of()
    //     0xbe0f74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe0f78: LoadField: r1 = r0->field_87
    //     0xbe0f78: ldur            w1, [x0, #0x87]
    // 0xbe0f7c: DecompressPointer r1
    //     0xbe0f7c: add             x1, x1, HEAP, lsl #32
    // 0xbe0f80: LoadField: r0 = r1->field_2b
    //     0xbe0f80: ldur            w0, [x1, #0x2b]
    // 0xbe0f84: DecompressPointer r0
    //     0xbe0f84: add             x0, x0, HEAP, lsl #32
    // 0xbe0f88: r16 = 14.000000
    //     0xbe0f88: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbe0f8c: ldr             x16, [x16, #0x1d8]
    // 0xbe0f90: r30 = Instance_Color
    //     0xbe0f90: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe0f94: stp             lr, x16, [SP]
    // 0xbe0f98: mov             x1, x0
    // 0xbe0f9c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe0f9c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe0fa0: ldr             x4, [x4, #0xaa0]
    // 0xbe0fa4: r0 = copyWith()
    //     0xbe0fa4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe0fa8: stur            x0, [fp, #-0x18]
    // 0xbe0fac: r0 = HtmlWidget()
    //     0xbe0fac: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xbe0fb0: mov             x1, x0
    // 0xbe0fb4: ldur            x0, [fp, #-0x10]
    // 0xbe0fb8: stur            x1, [fp, #-0x28]
    // 0xbe0fbc: StoreField: r1->field_1f = r0
    //     0xbe0fbc: stur            w0, [x1, #0x1f]
    // 0xbe0fc0: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xbe0fc0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xbe0fc4: ldr             x0, [x0, #0x1e0]
    // 0xbe0fc8: StoreField: r1->field_23 = r0
    //     0xbe0fc8: stur            w0, [x1, #0x23]
    // 0xbe0fcc: r0 = Instance_ColumnMode
    //     0xbe0fcc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xbe0fd0: ldr             x0, [x0, #0x1e8]
    // 0xbe0fd4: StoreField: r1->field_3b = r0
    //     0xbe0fd4: stur            w0, [x1, #0x3b]
    // 0xbe0fd8: ldur            x0, [fp, #-0x18]
    // 0xbe0fdc: StoreField: r1->field_3f = r0
    //     0xbe0fdc: stur            w0, [x1, #0x3f]
    // 0xbe0fe0: r0 = Padding()
    //     0xbe0fe0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe0fe4: mov             x2, x0
    // 0xbe0fe8: r0 = Instance_EdgeInsets
    //     0xbe0fe8: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b30] Obj!EdgeInsets@d588e1
    //     0xbe0fec: ldr             x0, [x0, #0xb30]
    // 0xbe0ff0: stur            x2, [fp, #-0x10]
    // 0xbe0ff4: StoreField: r2->field_f = r0
    //     0xbe0ff4: stur            w0, [x2, #0xf]
    // 0xbe0ff8: ldur            x1, [fp, #-0x28]
    // 0xbe0ffc: StoreField: r2->field_b = r1
    //     0xbe0ffc: stur            w1, [x2, #0xb]
    // 0xbe1000: ldur            x3, [fp, #-0x20]
    // 0xbe1004: LoadField: r1 = r3->field_b
    //     0xbe1004: ldur            w1, [x3, #0xb]
    // 0xbe1008: LoadField: r4 = r3->field_f
    //     0xbe1008: ldur            w4, [x3, #0xf]
    // 0xbe100c: DecompressPointer r4
    //     0xbe100c: add             x4, x4, HEAP, lsl #32
    // 0xbe1010: LoadField: r5 = r4->field_b
    //     0xbe1010: ldur            w5, [x4, #0xb]
    // 0xbe1014: r4 = LoadInt32Instr(r1)
    //     0xbe1014: sbfx            x4, x1, #1, #0x1f
    // 0xbe1018: stur            x4, [fp, #-0x38]
    // 0xbe101c: r1 = LoadInt32Instr(r5)
    //     0xbe101c: sbfx            x1, x5, #1, #0x1f
    // 0xbe1020: cmp             x4, x1
    // 0xbe1024: b.ne            #0xbe1030
    // 0xbe1028: mov             x1, x3
    // 0xbe102c: r0 = _growToNextCapacity()
    //     0xbe102c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbe1030: ldur            x2, [fp, #-0x20]
    // 0xbe1034: ldur            x3, [fp, #-0x38]
    // 0xbe1038: add             x0, x3, #1
    // 0xbe103c: lsl             x1, x0, #1
    // 0xbe1040: StoreField: r2->field_b = r1
    //     0xbe1040: stur            w1, [x2, #0xb]
    // 0xbe1044: LoadField: r1 = r2->field_f
    //     0xbe1044: ldur            w1, [x2, #0xf]
    // 0xbe1048: DecompressPointer r1
    //     0xbe1048: add             x1, x1, HEAP, lsl #32
    // 0xbe104c: ldur            x0, [fp, #-0x10]
    // 0xbe1050: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbe1050: add             x25, x1, x3, lsl #2
    //     0xbe1054: add             x25, x25, #0xf
    //     0xbe1058: str             w0, [x25]
    //     0xbe105c: tbz             w0, #0, #0xbe1078
    //     0xbe1060: ldurb           w16, [x1, #-1]
    //     0xbe1064: ldurb           w17, [x0, #-1]
    //     0xbe1068: and             x16, x17, x16, lsr #2
    //     0xbe106c: tst             x16, HEAP, lsr #32
    //     0xbe1070: b.eq            #0xbe1078
    //     0xbe1074: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe1078: ldur            x3, [fp, #-8]
    // 0xbe107c: LoadField: r0 = r3->field_f
    //     0xbe107c: ldur            w0, [x3, #0xf]
    // 0xbe1080: DecompressPointer r0
    //     0xbe1080: add             x0, x0, HEAP, lsl #32
    // 0xbe1084: LoadField: r1 = r0->field_b
    //     0xbe1084: ldur            w1, [x0, #0xb]
    // 0xbe1088: DecompressPointer r1
    //     0xbe1088: add             x1, x1, HEAP, lsl #32
    // 0xbe108c: cmp             w1, NULL
    // 0xbe1090: b.eq            #0xbe165c
    // 0xbe1094: LoadField: r4 = r1->field_b
    //     0xbe1094: ldur            w4, [x1, #0xb]
    // 0xbe1098: DecompressPointer r4
    //     0xbe1098: add             x4, x4, HEAP, lsl #32
    // 0xbe109c: cmp             w4, NULL
    // 0xbe10a0: b.ne            #0xbe10b0
    // 0xbe10a4: ldr             x5, [fp, #0x10]
    // 0xbe10a8: r0 = Null
    //     0xbe10a8: mov             x0, NULL
    // 0xbe10ac: b               #0xbe1118
    // 0xbe10b0: ldr             x5, [fp, #0x10]
    // 0xbe10b4: LoadField: r0 = r4->field_b
    //     0xbe10b4: ldur            w0, [x4, #0xb]
    // 0xbe10b8: r6 = LoadInt32Instr(r5)
    //     0xbe10b8: sbfx            x6, x5, #1, #0x1f
    //     0xbe10bc: tbz             w5, #0, #0xbe10c4
    //     0xbe10c0: ldur            x6, [x5, #7]
    // 0xbe10c4: r1 = LoadInt32Instr(r0)
    //     0xbe10c4: sbfx            x1, x0, #1, #0x1f
    // 0xbe10c8: mov             x0, x1
    // 0xbe10cc: mov             x1, x6
    // 0xbe10d0: cmp             x1, x0
    // 0xbe10d4: b.hs            #0xbe1660
    // 0xbe10d8: LoadField: r0 = r4->field_f
    //     0xbe10d8: ldur            w0, [x4, #0xf]
    // 0xbe10dc: DecompressPointer r0
    //     0xbe10dc: add             x0, x0, HEAP, lsl #32
    // 0xbe10e0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbe10e0: add             x16, x0, x6, lsl #2
    //     0xbe10e4: ldur            w1, [x16, #0xf]
    // 0xbe10e8: DecompressPointer r1
    //     0xbe10e8: add             x1, x1, HEAP, lsl #32
    // 0xbe10ec: LoadField: r0 = r1->field_f
    //     0xbe10ec: ldur            w0, [x1, #0xf]
    // 0xbe10f0: DecompressPointer r0
    //     0xbe10f0: add             x0, x0, HEAP, lsl #32
    // 0xbe10f4: cmp             w0, NULL
    // 0xbe10f8: b.ne            #0xbe1104
    // 0xbe10fc: r0 = Null
    //     0xbe10fc: mov             x0, NULL
    // 0xbe1100: b               #0xbe1118
    // 0xbe1104: LoadField: r1 = r0->field_7
    //     0xbe1104: ldur            w1, [x0, #7]
    // 0xbe1108: cbnz            w1, #0xbe1114
    // 0xbe110c: r0 = false
    //     0xbe110c: add             x0, NULL, #0x30  ; false
    // 0xbe1110: b               #0xbe1118
    // 0xbe1114: r0 = true
    //     0xbe1114: add             x0, NULL, #0x20  ; true
    // 0xbe1118: cmp             w0, NULL
    // 0xbe111c: b.ne            #0xbe1124
    // 0xbe1120: r0 = false
    //     0xbe1120: add             x0, NULL, #0x30  ; false
    // 0xbe1124: stur            x0, [fp, #-0x10]
    // 0xbe1128: r16 = <EdgeInsets>
    //     0xbe1128: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbe112c: ldr             x16, [x16, #0xda0]
    // 0xbe1130: r30 = Instance_EdgeInsets
    //     0xbe1130: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbe1134: ldr             lr, [lr, #0x1f0]
    // 0xbe1138: stp             lr, x16, [SP]
    // 0xbe113c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe113c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe1140: r0 = all()
    //     0xbe1140: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe1144: r1 = Instance_Color
    //     0xbe1144: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe1148: d0 = 0.400000
    //     0xbe1148: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbe114c: stur            x0, [fp, #-0x18]
    // 0xbe1150: r0 = withOpacity()
    //     0xbe1150: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbe1154: stur            x0, [fp, #-0x28]
    // 0xbe1158: r0 = BorderSide()
    //     0xbe1158: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbe115c: mov             x1, x0
    // 0xbe1160: ldur            x0, [fp, #-0x28]
    // 0xbe1164: stur            x1, [fp, #-0x30]
    // 0xbe1168: StoreField: r1->field_7 = r0
    //     0xbe1168: stur            w0, [x1, #7]
    // 0xbe116c: d0 = 1.000000
    //     0xbe116c: fmov            d0, #1.00000000
    // 0xbe1170: StoreField: r1->field_b = d0
    //     0xbe1170: stur            d0, [x1, #0xb]
    // 0xbe1174: r0 = Instance_BorderStyle
    //     0xbe1174: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbe1178: ldr             x0, [x0, #0xf68]
    // 0xbe117c: StoreField: r1->field_13 = r0
    //     0xbe117c: stur            w0, [x1, #0x13]
    // 0xbe1180: d0 = -1.000000
    //     0xbe1180: fmov            d0, #-1.00000000
    // 0xbe1184: ArrayStore: r1[0] = d0  ; List_8
    //     0xbe1184: stur            d0, [x1, #0x17]
    // 0xbe1188: r0 = RoundedRectangleBorder()
    //     0xbe1188: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xbe118c: mov             x1, x0
    // 0xbe1190: r0 = Instance_BorderRadius
    //     0xbe1190: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbe1194: ldr             x0, [x0, #0xf70]
    // 0xbe1198: StoreField: r1->field_b = r0
    //     0xbe1198: stur            w0, [x1, #0xb]
    // 0xbe119c: ldur            x0, [fp, #-0x30]
    // 0xbe11a0: StoreField: r1->field_7 = r0
    //     0xbe11a0: stur            w0, [x1, #7]
    // 0xbe11a4: r16 = <RoundedRectangleBorder>
    //     0xbe11a4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbe11a8: ldr             x16, [x16, #0xf78]
    // 0xbe11ac: stp             x1, x16, [SP]
    // 0xbe11b0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe11b0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe11b4: r0 = all()
    //     0xbe11b4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe11b8: stur            x0, [fp, #-0x28]
    // 0xbe11bc: r0 = ButtonStyle()
    //     0xbe11bc: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbe11c0: mov             x1, x0
    // 0xbe11c4: ldur            x0, [fp, #-0x18]
    // 0xbe11c8: stur            x1, [fp, #-0x30]
    // 0xbe11cc: StoreField: r1->field_23 = r0
    //     0xbe11cc: stur            w0, [x1, #0x23]
    // 0xbe11d0: ldur            x0, [fp, #-0x28]
    // 0xbe11d4: StoreField: r1->field_43 = r0
    //     0xbe11d4: stur            w0, [x1, #0x43]
    // 0xbe11d8: r0 = TextButtonThemeData()
    //     0xbe11d8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbe11dc: mov             x2, x0
    // 0xbe11e0: ldur            x0, [fp, #-0x30]
    // 0xbe11e4: stur            x2, [fp, #-0x18]
    // 0xbe11e8: StoreField: r2->field_7 = r0
    //     0xbe11e8: stur            w0, [x2, #7]
    // 0xbe11ec: ldur            x3, [fp, #-8]
    // 0xbe11f0: LoadField: r0 = r3->field_f
    //     0xbe11f0: ldur            w0, [x3, #0xf]
    // 0xbe11f4: DecompressPointer r0
    //     0xbe11f4: add             x0, x0, HEAP, lsl #32
    // 0xbe11f8: LoadField: r1 = r0->field_b
    //     0xbe11f8: ldur            w1, [x0, #0xb]
    // 0xbe11fc: DecompressPointer r1
    //     0xbe11fc: add             x1, x1, HEAP, lsl #32
    // 0xbe1200: cmp             w1, NULL
    // 0xbe1204: b.eq            #0xbe1664
    // 0xbe1208: LoadField: r4 = r1->field_b
    //     0xbe1208: ldur            w4, [x1, #0xb]
    // 0xbe120c: DecompressPointer r4
    //     0xbe120c: add             x4, x4, HEAP, lsl #32
    // 0xbe1210: cmp             w4, NULL
    // 0xbe1214: b.ne            #0xbe1224
    // 0xbe1218: ldr             x5, [fp, #0x10]
    // 0xbe121c: r0 = Null
    //     0xbe121c: mov             x0, NULL
    // 0xbe1220: b               #0xbe1268
    // 0xbe1224: ldr             x5, [fp, #0x10]
    // 0xbe1228: LoadField: r0 = r4->field_b
    //     0xbe1228: ldur            w0, [x4, #0xb]
    // 0xbe122c: r6 = LoadInt32Instr(r5)
    //     0xbe122c: sbfx            x6, x5, #1, #0x1f
    //     0xbe1230: tbz             w5, #0, #0xbe1238
    //     0xbe1234: ldur            x6, [x5, #7]
    // 0xbe1238: r1 = LoadInt32Instr(r0)
    //     0xbe1238: sbfx            x1, x0, #1, #0x1f
    // 0xbe123c: mov             x0, x1
    // 0xbe1240: mov             x1, x6
    // 0xbe1244: cmp             x1, x0
    // 0xbe1248: b.hs            #0xbe1668
    // 0xbe124c: LoadField: r0 = r4->field_f
    //     0xbe124c: ldur            w0, [x4, #0xf]
    // 0xbe1250: DecompressPointer r0
    //     0xbe1250: add             x0, x0, HEAP, lsl #32
    // 0xbe1254: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbe1254: add             x16, x0, x6, lsl #2
    //     0xbe1258: ldur            w1, [x16, #0xf]
    // 0xbe125c: DecompressPointer r1
    //     0xbe125c: add             x1, x1, HEAP, lsl #32
    // 0xbe1260: LoadField: r0 = r1->field_f
    //     0xbe1260: ldur            w0, [x1, #0xf]
    // 0xbe1264: DecompressPointer r0
    //     0xbe1264: add             x0, x0, HEAP, lsl #32
    // 0xbe1268: cmp             w0, NULL
    // 0xbe126c: b.ne            #0xbe1274
    // 0xbe1270: r0 = ""
    //     0xbe1270: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe1274: ldur            x4, [fp, #-0x10]
    // 0xbe1278: ldur            x1, [fp, #-0x20]
    // 0xbe127c: r6 = LoadClassIdInstr(r0)
    //     0xbe127c: ldur            x6, [x0, #-1]
    //     0xbe1280: ubfx            x6, x6, #0xc, #0x14
    // 0xbe1284: str             x0, [SP]
    // 0xbe1288: mov             x0, x6
    // 0xbe128c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbe128c: sub             lr, x0, #1, lsl #12
    //     0xbe1290: ldr             lr, [x21, lr, lsl #3]
    //     0xbe1294: blr             lr
    // 0xbe1298: ldr             x1, [fp, #0x18]
    // 0xbe129c: stur            x0, [fp, #-0x28]
    // 0xbe12a0: r0 = of()
    //     0xbe12a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe12a4: LoadField: r1 = r0->field_87
    //     0xbe12a4: ldur            w1, [x0, #0x87]
    // 0xbe12a8: DecompressPointer r1
    //     0xbe12a8: add             x1, x1, HEAP, lsl #32
    // 0xbe12ac: LoadField: r0 = r1->field_7
    //     0xbe12ac: ldur            w0, [x1, #7]
    // 0xbe12b0: DecompressPointer r0
    //     0xbe12b0: add             x0, x0, HEAP, lsl #32
    // 0xbe12b4: r16 = Instance_Color
    //     0xbe12b4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe12b8: r30 = 12.000000
    //     0xbe12b8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe12bc: ldr             lr, [lr, #0x9e8]
    // 0xbe12c0: stp             lr, x16, [SP]
    // 0xbe12c4: mov             x1, x0
    // 0xbe12c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbe12c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbe12cc: ldr             x4, [x4, #0x9b8]
    // 0xbe12d0: r0 = copyWith()
    //     0xbe12d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe12d4: stur            x0, [fp, #-0x30]
    // 0xbe12d8: r0 = Text()
    //     0xbe12d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe12dc: mov             x3, x0
    // 0xbe12e0: ldur            x0, [fp, #-0x28]
    // 0xbe12e4: stur            x3, [fp, #-0x40]
    // 0xbe12e8: StoreField: r3->field_b = r0
    //     0xbe12e8: stur            w0, [x3, #0xb]
    // 0xbe12ec: ldur            x0, [fp, #-0x30]
    // 0xbe12f0: StoreField: r3->field_13 = r0
    //     0xbe12f0: stur            w0, [x3, #0x13]
    // 0xbe12f4: r1 = Function '<anonymous closure>':.
    //     0xbe12f4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53790] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xbe12f8: ldr             x1, [x1, #0x790]
    // 0xbe12fc: r2 = Null
    //     0xbe12fc: mov             x2, NULL
    // 0xbe1300: r0 = AllocateClosure()
    //     0xbe1300: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe1304: stur            x0, [fp, #-0x28]
    // 0xbe1308: r0 = TextButton()
    //     0xbe1308: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbe130c: mov             x1, x0
    // 0xbe1310: ldur            x0, [fp, #-0x28]
    // 0xbe1314: stur            x1, [fp, #-0x30]
    // 0xbe1318: StoreField: r1->field_b = r0
    //     0xbe1318: stur            w0, [x1, #0xb]
    // 0xbe131c: r0 = false
    //     0xbe131c: add             x0, NULL, #0x30  ; false
    // 0xbe1320: StoreField: r1->field_27 = r0
    //     0xbe1320: stur            w0, [x1, #0x27]
    // 0xbe1324: r2 = true
    //     0xbe1324: add             x2, NULL, #0x20  ; true
    // 0xbe1328: StoreField: r1->field_2f = r2
    //     0xbe1328: stur            w2, [x1, #0x2f]
    // 0xbe132c: ldur            x2, [fp, #-0x40]
    // 0xbe1330: StoreField: r1->field_37 = r2
    //     0xbe1330: stur            w2, [x1, #0x37]
    // 0xbe1334: r0 = TextButtonTheme()
    //     0xbe1334: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbe1338: mov             x1, x0
    // 0xbe133c: ldur            x0, [fp, #-0x18]
    // 0xbe1340: stur            x1, [fp, #-0x28]
    // 0xbe1344: StoreField: r1->field_f = r0
    //     0xbe1344: stur            w0, [x1, #0xf]
    // 0xbe1348: ldur            x0, [fp, #-0x30]
    // 0xbe134c: StoreField: r1->field_b = r0
    //     0xbe134c: stur            w0, [x1, #0xb]
    // 0xbe1350: r0 = Padding()
    //     0xbe1350: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe1354: mov             x1, x0
    // 0xbe1358: r0 = Instance_EdgeInsets
    //     0xbe1358: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b30] Obj!EdgeInsets@d588e1
    //     0xbe135c: ldr             x0, [x0, #0xb30]
    // 0xbe1360: stur            x1, [fp, #-0x18]
    // 0xbe1364: StoreField: r1->field_f = r0
    //     0xbe1364: stur            w0, [x1, #0xf]
    // 0xbe1368: ldur            x2, [fp, #-0x28]
    // 0xbe136c: StoreField: r1->field_b = r2
    //     0xbe136c: stur            w2, [x1, #0xb]
    // 0xbe1370: r0 = Visibility()
    //     0xbe1370: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbe1374: mov             x2, x0
    // 0xbe1378: ldur            x0, [fp, #-0x18]
    // 0xbe137c: stur            x2, [fp, #-0x28]
    // 0xbe1380: StoreField: r2->field_b = r0
    //     0xbe1380: stur            w0, [x2, #0xb]
    // 0xbe1384: r0 = Instance_SizedBox
    //     0xbe1384: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbe1388: StoreField: r2->field_f = r0
    //     0xbe1388: stur            w0, [x2, #0xf]
    // 0xbe138c: ldur            x0, [fp, #-0x10]
    // 0xbe1390: StoreField: r2->field_13 = r0
    //     0xbe1390: stur            w0, [x2, #0x13]
    // 0xbe1394: r0 = false
    //     0xbe1394: add             x0, NULL, #0x30  ; false
    // 0xbe1398: ArrayStore: r2[0] = r0  ; List_4
    //     0xbe1398: stur            w0, [x2, #0x17]
    // 0xbe139c: StoreField: r2->field_1b = r0
    //     0xbe139c: stur            w0, [x2, #0x1b]
    // 0xbe13a0: StoreField: r2->field_1f = r0
    //     0xbe13a0: stur            w0, [x2, #0x1f]
    // 0xbe13a4: StoreField: r2->field_23 = r0
    //     0xbe13a4: stur            w0, [x2, #0x23]
    // 0xbe13a8: StoreField: r2->field_27 = r0
    //     0xbe13a8: stur            w0, [x2, #0x27]
    // 0xbe13ac: StoreField: r2->field_2b = r0
    //     0xbe13ac: stur            w0, [x2, #0x2b]
    // 0xbe13b0: ldur            x0, [fp, #-0x20]
    // 0xbe13b4: LoadField: r1 = r0->field_b
    //     0xbe13b4: ldur            w1, [x0, #0xb]
    // 0xbe13b8: LoadField: r3 = r0->field_f
    //     0xbe13b8: ldur            w3, [x0, #0xf]
    // 0xbe13bc: DecompressPointer r3
    //     0xbe13bc: add             x3, x3, HEAP, lsl #32
    // 0xbe13c0: LoadField: r4 = r3->field_b
    //     0xbe13c0: ldur            w4, [x3, #0xb]
    // 0xbe13c4: r3 = LoadInt32Instr(r1)
    //     0xbe13c4: sbfx            x3, x1, #1, #0x1f
    // 0xbe13c8: stur            x3, [fp, #-0x38]
    // 0xbe13cc: r1 = LoadInt32Instr(r4)
    //     0xbe13cc: sbfx            x1, x4, #1, #0x1f
    // 0xbe13d0: cmp             x3, x1
    // 0xbe13d4: b.ne            #0xbe13e0
    // 0xbe13d8: mov             x1, x0
    // 0xbe13dc: r0 = _growToNextCapacity()
    //     0xbe13dc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbe13e0: ldur            x4, [fp, #-8]
    // 0xbe13e4: ldur            x2, [fp, #-0x20]
    // 0xbe13e8: ldur            x3, [fp, #-0x38]
    // 0xbe13ec: add             x0, x3, #1
    // 0xbe13f0: lsl             x1, x0, #1
    // 0xbe13f4: StoreField: r2->field_b = r1
    //     0xbe13f4: stur            w1, [x2, #0xb]
    // 0xbe13f8: LoadField: r1 = r2->field_f
    //     0xbe13f8: ldur            w1, [x2, #0xf]
    // 0xbe13fc: DecompressPointer r1
    //     0xbe13fc: add             x1, x1, HEAP, lsl #32
    // 0xbe1400: ldur            x0, [fp, #-0x28]
    // 0xbe1404: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbe1404: add             x25, x1, x3, lsl #2
    //     0xbe1408: add             x25, x25, #0xf
    //     0xbe140c: str             w0, [x25]
    //     0xbe1410: tbz             w0, #0, #0xbe142c
    //     0xbe1414: ldurb           w16, [x1, #-1]
    //     0xbe1418: ldurb           w17, [x0, #-1]
    //     0xbe141c: and             x16, x17, x16, lsr #2
    //     0xbe1420: tst             x16, HEAP, lsr #32
    //     0xbe1424: b.eq            #0xbe142c
    //     0xbe1428: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe142c: LoadField: r0 = r4->field_f
    //     0xbe142c: ldur            w0, [x4, #0xf]
    // 0xbe1430: DecompressPointer r0
    //     0xbe1430: add             x0, x0, HEAP, lsl #32
    // 0xbe1434: LoadField: r1 = r0->field_b
    //     0xbe1434: ldur            w1, [x0, #0xb]
    // 0xbe1438: DecompressPointer r1
    //     0xbe1438: add             x1, x1, HEAP, lsl #32
    // 0xbe143c: cmp             w1, NULL
    // 0xbe1440: b.eq            #0xbe166c
    // 0xbe1444: LoadField: r3 = r1->field_b
    //     0xbe1444: ldur            w3, [x1, #0xb]
    // 0xbe1448: DecompressPointer r3
    //     0xbe1448: add             x3, x3, HEAP, lsl #32
    // 0xbe144c: cmp             w3, NULL
    // 0xbe1450: b.ne            #0xbe145c
    // 0xbe1454: r0 = Null
    //     0xbe1454: mov             x0, NULL
    // 0xbe1458: b               #0xbe149c
    // 0xbe145c: ldr             x0, [fp, #0x10]
    // 0xbe1460: LoadField: r1 = r3->field_b
    //     0xbe1460: ldur            w1, [x3, #0xb]
    // 0xbe1464: r4 = LoadInt32Instr(r0)
    //     0xbe1464: sbfx            x4, x0, #1, #0x1f
    //     0xbe1468: tbz             w0, #0, #0xbe1470
    //     0xbe146c: ldur            x4, [x0, #7]
    // 0xbe1470: r0 = LoadInt32Instr(r1)
    //     0xbe1470: sbfx            x0, x1, #1, #0x1f
    // 0xbe1474: mov             x1, x4
    // 0xbe1478: cmp             x1, x0
    // 0xbe147c: b.hs            #0xbe1670
    // 0xbe1480: LoadField: r0 = r3->field_f
    //     0xbe1480: ldur            w0, [x3, #0xf]
    // 0xbe1484: DecompressPointer r0
    //     0xbe1484: add             x0, x0, HEAP, lsl #32
    // 0xbe1488: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbe1488: add             x16, x0, x4, lsl #2
    //     0xbe148c: ldur            w1, [x16, #0xf]
    // 0xbe1490: DecompressPointer r1
    //     0xbe1490: add             x1, x1, HEAP, lsl #32
    // 0xbe1494: LoadField: r0 = r1->field_13
    //     0xbe1494: ldur            w0, [x1, #0x13]
    // 0xbe1498: DecompressPointer r0
    //     0xbe1498: add             x0, x0, HEAP, lsl #32
    // 0xbe149c: cmp             w0, NULL
    // 0xbe14a0: b.ne            #0xbe14a8
    // 0xbe14a4: r0 = ""
    //     0xbe14a4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe14a8: stur            x0, [fp, #-8]
    // 0xbe14ac: r0 = ImageHeaders.forImages()
    //     0xbe14ac: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbe14b0: r1 = Function '<anonymous closure>':.
    //     0xbe14b0: add             x1, PP, #0x53, lsl #12  ; [pp+0x53798] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbe14b4: ldr             x1, [x1, #0x798]
    // 0xbe14b8: r2 = Null
    //     0xbe14b8: mov             x2, NULL
    // 0xbe14bc: stur            x0, [fp, #-0x10]
    // 0xbe14c0: r0 = AllocateClosure()
    //     0xbe14c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe14c4: r1 = Function '<anonymous closure>':.
    //     0xbe14c4: add             x1, PP, #0x53, lsl #12  ; [pp+0x537a0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbe14c8: ldr             x1, [x1, #0x7a0]
    // 0xbe14cc: r2 = Null
    //     0xbe14cc: mov             x2, NULL
    // 0xbe14d0: stur            x0, [fp, #-0x18]
    // 0xbe14d4: r0 = AllocateClosure()
    //     0xbe14d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe14d8: stur            x0, [fp, #-0x28]
    // 0xbe14dc: r0 = CachedNetworkImage()
    //     0xbe14dc: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbe14e0: stur            x0, [fp, #-0x30]
    // 0xbe14e4: ldur            x16, [fp, #-0x10]
    // 0xbe14e8: ldur            lr, [fp, #-0x18]
    // 0xbe14ec: stp             lr, x16, [SP, #0x10]
    // 0xbe14f0: ldur            x16, [fp, #-0x28]
    // 0xbe14f4: r30 = Instance_BoxFit
    //     0xbe14f4: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbe14f8: ldr             lr, [lr, #0xb18]
    // 0xbe14fc: stp             lr, x16, [SP]
    // 0xbe1500: mov             x1, x0
    // 0xbe1504: ldur            x2, [fp, #-8]
    // 0xbe1508: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x4, fit, 0x5, httpHeaders, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xbe1508: add             x4, PP, #0x52, lsl #12  ; [pp+0x522b0] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x4, "fit", 0x5, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xbe150c: ldr             x4, [x4, #0x2b0]
    // 0xbe1510: r0 = CachedNetworkImage()
    //     0xbe1510: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbe1514: r0 = Padding()
    //     0xbe1514: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe1518: mov             x2, x0
    // 0xbe151c: r0 = Instance_EdgeInsets
    //     0xbe151c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b30] Obj!EdgeInsets@d588e1
    //     0xbe1520: ldr             x0, [x0, #0xb30]
    // 0xbe1524: stur            x2, [fp, #-8]
    // 0xbe1528: StoreField: r2->field_f = r0
    //     0xbe1528: stur            w0, [x2, #0xf]
    // 0xbe152c: ldur            x0, [fp, #-0x30]
    // 0xbe1530: StoreField: r2->field_b = r0
    //     0xbe1530: stur            w0, [x2, #0xb]
    // 0xbe1534: ldur            x0, [fp, #-0x20]
    // 0xbe1538: LoadField: r1 = r0->field_b
    //     0xbe1538: ldur            w1, [x0, #0xb]
    // 0xbe153c: LoadField: r3 = r0->field_f
    //     0xbe153c: ldur            w3, [x0, #0xf]
    // 0xbe1540: DecompressPointer r3
    //     0xbe1540: add             x3, x3, HEAP, lsl #32
    // 0xbe1544: LoadField: r4 = r3->field_b
    //     0xbe1544: ldur            w4, [x3, #0xb]
    // 0xbe1548: r3 = LoadInt32Instr(r1)
    //     0xbe1548: sbfx            x3, x1, #1, #0x1f
    // 0xbe154c: stur            x3, [fp, #-0x38]
    // 0xbe1550: r1 = LoadInt32Instr(r4)
    //     0xbe1550: sbfx            x1, x4, #1, #0x1f
    // 0xbe1554: cmp             x3, x1
    // 0xbe1558: b.ne            #0xbe1564
    // 0xbe155c: mov             x1, x0
    // 0xbe1560: r0 = _growToNextCapacity()
    //     0xbe1560: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbe1564: ldur            x2, [fp, #-0x20]
    // 0xbe1568: ldur            x3, [fp, #-0x38]
    // 0xbe156c: add             x0, x3, #1
    // 0xbe1570: lsl             x1, x0, #1
    // 0xbe1574: StoreField: r2->field_b = r1
    //     0xbe1574: stur            w1, [x2, #0xb]
    // 0xbe1578: LoadField: r1 = r2->field_f
    //     0xbe1578: ldur            w1, [x2, #0xf]
    // 0xbe157c: DecompressPointer r1
    //     0xbe157c: add             x1, x1, HEAP, lsl #32
    // 0xbe1580: ldur            x0, [fp, #-8]
    // 0xbe1584: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbe1584: add             x25, x1, x3, lsl #2
    //     0xbe1588: add             x25, x25, #0xf
    //     0xbe158c: str             w0, [x25]
    //     0xbe1590: tbz             w0, #0, #0xbe15ac
    //     0xbe1594: ldurb           w16, [x1, #-1]
    //     0xbe1598: ldurb           w17, [x0, #-1]
    //     0xbe159c: and             x16, x17, x16, lsr #2
    //     0xbe15a0: tst             x16, HEAP, lsr #32
    //     0xbe15a4: b.eq            #0xbe15ac
    //     0xbe15a8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe15ac: r0 = Column()
    //     0xbe15ac: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbe15b0: r1 = Instance_Axis
    //     0xbe15b0: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbe15b4: StoreField: r0->field_f = r1
    //     0xbe15b4: stur            w1, [x0, #0xf]
    // 0xbe15b8: r1 = Instance_MainAxisAlignment
    //     0xbe15b8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbe15bc: ldr             x1, [x1, #0xa08]
    // 0xbe15c0: StoreField: r0->field_13 = r1
    //     0xbe15c0: stur            w1, [x0, #0x13]
    // 0xbe15c4: r1 = Instance_MainAxisSize
    //     0xbe15c4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbe15c8: ldr             x1, [x1, #0xa10]
    // 0xbe15cc: ArrayStore: r0[0] = r1  ; List_4
    //     0xbe15cc: stur            w1, [x0, #0x17]
    // 0xbe15d0: r1 = Instance_CrossAxisAlignment
    //     0xbe15d0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbe15d4: ldr             x1, [x1, #0x890]
    // 0xbe15d8: StoreField: r0->field_1b = r1
    //     0xbe15d8: stur            w1, [x0, #0x1b]
    // 0xbe15dc: r1 = Instance_VerticalDirection
    //     0xbe15dc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe15e0: ldr             x1, [x1, #0xa20]
    // 0xbe15e4: StoreField: r0->field_23 = r1
    //     0xbe15e4: stur            w1, [x0, #0x23]
    // 0xbe15e8: r1 = Instance_Clip
    //     0xbe15e8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe15ec: ldr             x1, [x1, #0x38]
    // 0xbe15f0: StoreField: r0->field_2b = r1
    //     0xbe15f0: stur            w1, [x0, #0x2b]
    // 0xbe15f4: StoreField: r0->field_2f = rZR
    //     0xbe15f4: stur            xzr, [x0, #0x2f]
    // 0xbe15f8: ldur            x1, [fp, #-0x20]
    // 0xbe15fc: StoreField: r0->field_b = r1
    //     0xbe15fc: stur            w1, [x0, #0xb]
    // 0xbe1600: LeaveFrame
    //     0xbe1600: mov             SP, fp
    //     0xbe1604: ldp             fp, lr, [SP], #0x10
    // 0xbe1608: ret
    //     0xbe1608: ret             
    // 0xbe160c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe160c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe1610: b               #0xbe0288
    // 0xbe1614: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe1614: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe1618: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbe1618: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbe161c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe161c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe1620: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbe1620: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbe1624: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe1624: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe1628: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbe1628: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbe162c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe162c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe1630: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbe1630: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbe1634: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbe1634: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbe1638: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe1638: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe163c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbe163c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbe1640: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe1640: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe1644: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbe1644: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbe1648: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbe1648: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbe164c: r0 = RangeErrorSharedWithFPURegs()
    //     0xbe164c: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xbe1650: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe1650: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe1654: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbe1654: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbe1658: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbe1658: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbe165c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe165c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe1660: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbe1660: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbe1664: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe1664: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe1668: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbe1668: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbe166c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe166c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe1670: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbe1670: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 3994, size: 0x10, field offset: 0xc
//   const constructor, 
class ContentTextAndMedia extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80a40, size: 0x24
    // 0xc80a40: EnterFrame
    //     0xc80a40: stp             fp, lr, [SP, #-0x10]!
    //     0xc80a44: mov             fp, SP
    // 0xc80a48: mov             x0, x1
    // 0xc80a4c: r1 = <ContentTextAndMedia>
    //     0xc80a4c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48418] TypeArguments: <ContentTextAndMedia>
    //     0xc80a50: ldr             x1, [x1, #0x418]
    // 0xc80a54: r0 = _ContentTextAndMediaState()
    //     0xc80a54: bl              #0xc80a64  ; Allocate_ContentTextAndMediaStateStub -> _ContentTextAndMediaState (size=0x14)
    // 0xc80a58: LeaveFrame
    //     0xc80a58: mov             SP, fp
    //     0xc80a5c: ldp             fp, lr, [SP], #0x10
    // 0xc80a60: ret
    //     0xc80a60: ret             
  }
}
