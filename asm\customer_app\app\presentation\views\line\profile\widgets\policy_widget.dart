// lib: , url: package:customer_app/app/presentation/views/line/profile/widgets/policy_widget.dart

// class id: 1049578, size: 0x8
class :: {
}

// class id: 4519, size: 0x14, field offset: 0x14
//   const constructor, 
class PolicyWidget extends BaseView<dynamic> {

  [closure] SingleChildScrollView <anonymous closure>(dynamic) {
    // ** addr: 0x147899c, size: 0x160
    // 0x147899c: EnterFrame
    //     0x147899c: stp             fp, lr, [SP, #-0x10]!
    //     0x14789a0: mov             fp, SP
    // 0x14789a4: AllocStack(0x28)
    //     0x14789a4: sub             SP, SP, #0x28
    // 0x14789a8: SetupParameters()
    //     0x14789a8: ldr             x0, [fp, #0x10]
    //     0x14789ac: ldur            w2, [x0, #0x17]
    //     0x14789b0: add             x2, x2, HEAP, lsl #32
    //     0x14789b4: stur            x2, [fp, #-8]
    // 0x14789b8: CheckStackOverflow
    //     0x14789b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14789bc: cmp             SP, x16
    //     0x14789c0: b.ls            #0x1478af4
    // 0x14789c4: LoadField: r1 = r2->field_f
    //     0x14789c4: ldur            w1, [x2, #0xf]
    // 0x14789c8: DecompressPointer r1
    //     0x14789c8: add             x1, x1, HEAP, lsl #32
    // 0x14789cc: r0 = controller()
    //     0x14789cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14789d0: LoadField: r1 = r0->field_5f
    //     0x14789d0: ldur            w1, [x0, #0x5f]
    // 0x14789d4: DecompressPointer r1
    //     0x14789d4: add             x1, x1, HEAP, lsl #32
    // 0x14789d8: r0 = value()
    //     0x14789d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14789dc: LoadField: r1 = r0->field_b
    //     0x14789dc: ldur            w1, [x0, #0xb]
    // 0x14789e0: DecompressPointer r1
    //     0x14789e0: add             x1, x1, HEAP, lsl #32
    // 0x14789e4: cmp             w1, NULL
    // 0x14789e8: b.ne            #0x14789f4
    // 0x14789ec: r0 = Null
    //     0x14789ec: mov             x0, NULL
    // 0x14789f0: b               #0x14789fc
    // 0x14789f4: LoadField: r0 = r1->field_7
    //     0x14789f4: ldur            w0, [x1, #7]
    // 0x14789f8: DecompressPointer r0
    //     0x14789f8: add             x0, x0, HEAP, lsl #32
    // 0x14789fc: cmp             w0, NULL
    // 0x1478a00: b.ne            #0x1478a0c
    // 0x1478a04: r2 = ""
    //     0x1478a04: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1478a08: b               #0x1478a10
    // 0x1478a0c: mov             x2, x0
    // 0x1478a10: ldur            x0, [fp, #-8]
    // 0x1478a14: stur            x2, [fp, #-0x10]
    // 0x1478a18: LoadField: r1 = r0->field_13
    //     0x1478a18: ldur            w1, [x0, #0x13]
    // 0x1478a1c: DecompressPointer r1
    //     0x1478a1c: add             x1, x1, HEAP, lsl #32
    // 0x1478a20: r0 = of()
    //     0x1478a20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1478a24: LoadField: r1 = r0->field_87
    //     0x1478a24: ldur            w1, [x0, #0x87]
    // 0x1478a28: DecompressPointer r1
    //     0x1478a28: add             x1, x1, HEAP, lsl #32
    // 0x1478a2c: LoadField: r0 = r1->field_2b
    //     0x1478a2c: ldur            w0, [x1, #0x2b]
    // 0x1478a30: DecompressPointer r0
    //     0x1478a30: add             x0, x0, HEAP, lsl #32
    // 0x1478a34: r16 = 14.000000
    //     0x1478a34: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1478a38: ldr             x16, [x16, #0x1d8]
    // 0x1478a3c: r30 = Instance_Color
    //     0x1478a3c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1478a40: stp             lr, x16, [SP]
    // 0x1478a44: mov             x1, x0
    // 0x1478a48: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1478a48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1478a4c: ldr             x4, [x4, #0xaa0]
    // 0x1478a50: r0 = copyWith()
    //     0x1478a50: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1478a54: stur            x0, [fp, #-8]
    // 0x1478a58: r0 = HtmlWidget()
    //     0x1478a58: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0x1478a5c: mov             x1, x0
    // 0x1478a60: ldur            x0, [fp, #-0x10]
    // 0x1478a64: stur            x1, [fp, #-0x18]
    // 0x1478a68: StoreField: r1->field_1f = r0
    //     0x1478a68: stur            w0, [x1, #0x1f]
    // 0x1478a6c: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0x1478a6c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0x1478a70: ldr             x0, [x0, #0x1e0]
    // 0x1478a74: StoreField: r1->field_23 = r0
    //     0x1478a74: stur            w0, [x1, #0x23]
    // 0x1478a78: r0 = Instance_ColumnMode
    //     0x1478a78: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0x1478a7c: ldr             x0, [x0, #0x1e8]
    // 0x1478a80: StoreField: r1->field_3b = r0
    //     0x1478a80: stur            w0, [x1, #0x3b]
    // 0x1478a84: ldur            x0, [fp, #-8]
    // 0x1478a88: StoreField: r1->field_3f = r0
    //     0x1478a88: stur            w0, [x1, #0x3f]
    // 0x1478a8c: r0 = Padding()
    //     0x1478a8c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1478a90: mov             x1, x0
    // 0x1478a94: r0 = Instance_EdgeInsets
    //     0x1478a94: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1478a98: ldr             x0, [x0, #0x1f0]
    // 0x1478a9c: stur            x1, [fp, #-8]
    // 0x1478aa0: StoreField: r1->field_f = r0
    //     0x1478aa0: stur            w0, [x1, #0xf]
    // 0x1478aa4: ldur            x0, [fp, #-0x18]
    // 0x1478aa8: StoreField: r1->field_b = r0
    //     0x1478aa8: stur            w0, [x1, #0xb]
    // 0x1478aac: r0 = SingleChildScrollView()
    //     0x1478aac: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x1478ab0: r1 = Instance_Axis
    //     0x1478ab0: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1478ab4: StoreField: r0->field_b = r1
    //     0x1478ab4: stur            w1, [x0, #0xb]
    // 0x1478ab8: r1 = false
    //     0x1478ab8: add             x1, NULL, #0x30  ; false
    // 0x1478abc: StoreField: r0->field_f = r1
    //     0x1478abc: stur            w1, [x0, #0xf]
    // 0x1478ac0: ldur            x1, [fp, #-8]
    // 0x1478ac4: StoreField: r0->field_23 = r1
    //     0x1478ac4: stur            w1, [x0, #0x23]
    // 0x1478ac8: r1 = Instance_DragStartBehavior
    //     0x1478ac8: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x1478acc: StoreField: r0->field_27 = r1
    //     0x1478acc: stur            w1, [x0, #0x27]
    // 0x1478ad0: r1 = Instance_Clip
    //     0x1478ad0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x1478ad4: ldr             x1, [x1, #0x7e0]
    // 0x1478ad8: StoreField: r0->field_2b = r1
    //     0x1478ad8: stur            w1, [x0, #0x2b]
    // 0x1478adc: r1 = Instance_HitTestBehavior
    //     0x1478adc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x1478ae0: ldr             x1, [x1, #0x288]
    // 0x1478ae4: StoreField: r0->field_2f = r1
    //     0x1478ae4: stur            w1, [x0, #0x2f]
    // 0x1478ae8: LeaveFrame
    //     0x1478ae8: mov             SP, fp
    //     0x1478aec: ldp             fp, lr, [SP], #0x10
    // 0x1478af0: ret
    //     0x1478af0: ret             
    // 0x1478af4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1478af4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1478af8: b               #0x14789c4
  }
  _ body(/* No info */) {
    // ** addr: 0x15094e8, size: 0x64
    // 0x15094e8: EnterFrame
    //     0x15094e8: stp             fp, lr, [SP, #-0x10]!
    //     0x15094ec: mov             fp, SP
    // 0x15094f0: AllocStack(0x18)
    //     0x15094f0: sub             SP, SP, #0x18
    // 0x15094f4: SetupParameters(PolicyWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15094f4: stur            x1, [fp, #-8]
    //     0x15094f8: stur            x2, [fp, #-0x10]
    // 0x15094fc: r1 = 2
    //     0x15094fc: movz            x1, #0x2
    // 0x1509500: r0 = AllocateContext()
    //     0x1509500: bl              #0x16f6108  ; AllocateContextStub
    // 0x1509504: mov             x1, x0
    // 0x1509508: ldur            x0, [fp, #-8]
    // 0x150950c: stur            x1, [fp, #-0x18]
    // 0x1509510: StoreField: r1->field_f = r0
    //     0x1509510: stur            w0, [x1, #0xf]
    // 0x1509514: ldur            x0, [fp, #-0x10]
    // 0x1509518: StoreField: r1->field_13 = r0
    //     0x1509518: stur            w0, [x1, #0x13]
    // 0x150951c: r0 = Obx()
    //     0x150951c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1509520: ldur            x2, [fp, #-0x18]
    // 0x1509524: r1 = Function '<anonymous closure>':.
    //     0x1509524: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1d0] AnonymousClosure: (0x147899c), in [package:customer_app/app/presentation/views/line/profile/widgets/policy_widget.dart] PolicyWidget::body (0x15094e8)
    //     0x1509528: ldr             x1, [x1, #0x1d0]
    // 0x150952c: stur            x0, [fp, #-8]
    // 0x1509530: r0 = AllocateClosure()
    //     0x1509530: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1509534: mov             x1, x0
    // 0x1509538: ldur            x0, [fp, #-8]
    // 0x150953c: StoreField: r0->field_b = r1
    //     0x150953c: stur            w1, [x0, #0xb]
    // 0x1509540: LeaveFrame
    //     0x1509540: mov             SP, fp
    //     0x1509544: ldp             fp, lr, [SP], #0x10
    // 0x1509548: ret
    //     0x1509548: ret             
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15ee174, size: 0x130
    // 0x15ee174: EnterFrame
    //     0x15ee174: stp             fp, lr, [SP, #-0x10]!
    //     0x15ee178: mov             fp, SP
    // 0x15ee17c: AllocStack(0x18)
    //     0x15ee17c: sub             SP, SP, #0x18
    // 0x15ee180: SetupParameters(PolicyWidget this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0x15ee180: mov             x0, x1
    //     0x15ee184: mov             x1, x2
    // 0x15ee188: CheckStackOverflow
    //     0x15ee188: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15ee18c: cmp             SP, x16
    //     0x15ee190: b.ls            #0x15ee29c
    // 0x15ee194: r0 = of()
    //     0x15ee194: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15ee198: LoadField: r1 = r0->field_5b
    //     0x15ee198: ldur            w1, [x0, #0x5b]
    // 0x15ee19c: DecompressPointer r1
    //     0x15ee19c: add             x1, x1, HEAP, lsl #32
    // 0x15ee1a0: stur            x1, [fp, #-8]
    // 0x15ee1a4: r0 = ColorFilter()
    //     0x15ee1a4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15ee1a8: mov             x1, x0
    // 0x15ee1ac: ldur            x0, [fp, #-8]
    // 0x15ee1b0: stur            x1, [fp, #-0x10]
    // 0x15ee1b4: StoreField: r1->field_7 = r0
    //     0x15ee1b4: stur            w0, [x1, #7]
    // 0x15ee1b8: r0 = Instance_BlendMode
    //     0x15ee1b8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15ee1bc: ldr             x0, [x0, #0xb30]
    // 0x15ee1c0: StoreField: r1->field_b = r0
    //     0x15ee1c0: stur            w0, [x1, #0xb]
    // 0x15ee1c4: r0 = 1
    //     0x15ee1c4: movz            x0, #0x1
    // 0x15ee1c8: StoreField: r1->field_13 = r0
    //     0x15ee1c8: stur            x0, [x1, #0x13]
    // 0x15ee1cc: r0 = SvgPicture()
    //     0x15ee1cc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15ee1d0: stur            x0, [fp, #-8]
    // 0x15ee1d4: ldur            x16, [fp, #-0x10]
    // 0x15ee1d8: str             x16, [SP]
    // 0x15ee1dc: mov             x1, x0
    // 0x15ee1e0: r2 = "assets/images/appbar_arrow.svg"
    //     0x15ee1e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15ee1e4: ldr             x2, [x2, #0xa40]
    // 0x15ee1e8: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15ee1e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15ee1ec: ldr             x4, [x4, #0xa38]
    // 0x15ee1f0: r0 = SvgPicture.asset()
    //     0x15ee1f0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15ee1f4: r0 = Align()
    //     0x15ee1f4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15ee1f8: mov             x1, x0
    // 0x15ee1fc: r0 = Instance_Alignment
    //     0x15ee1fc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15ee200: ldr             x0, [x0, #0xb10]
    // 0x15ee204: stur            x1, [fp, #-0x10]
    // 0x15ee208: StoreField: r1->field_f = r0
    //     0x15ee208: stur            w0, [x1, #0xf]
    // 0x15ee20c: r0 = 1.000000
    //     0x15ee20c: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15ee210: StoreField: r1->field_13 = r0
    //     0x15ee210: stur            w0, [x1, #0x13]
    // 0x15ee214: ArrayStore: r1[0] = r0  ; List_4
    //     0x15ee214: stur            w0, [x1, #0x17]
    // 0x15ee218: ldur            x0, [fp, #-8]
    // 0x15ee21c: StoreField: r1->field_b = r0
    //     0x15ee21c: stur            w0, [x1, #0xb]
    // 0x15ee220: r0 = InkWell()
    //     0x15ee220: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15ee224: mov             x3, x0
    // 0x15ee228: ldur            x0, [fp, #-0x10]
    // 0x15ee22c: stur            x3, [fp, #-8]
    // 0x15ee230: StoreField: r3->field_b = r0
    //     0x15ee230: stur            w0, [x3, #0xb]
    // 0x15ee234: r1 = Function '<anonymous closure>':.
    //     0x15ee234: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f200] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x15ee238: ldr             x1, [x1, #0x200]
    // 0x15ee23c: r2 = Null
    //     0x15ee23c: mov             x2, NULL
    // 0x15ee240: r0 = AllocateClosure()
    //     0x15ee240: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15ee244: ldur            x2, [fp, #-8]
    // 0x15ee248: StoreField: r2->field_f = r0
    //     0x15ee248: stur            w0, [x2, #0xf]
    // 0x15ee24c: r0 = true
    //     0x15ee24c: add             x0, NULL, #0x20  ; true
    // 0x15ee250: StoreField: r2->field_43 = r0
    //     0x15ee250: stur            w0, [x2, #0x43]
    // 0x15ee254: r1 = Instance_BoxShape
    //     0x15ee254: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15ee258: ldr             x1, [x1, #0x80]
    // 0x15ee25c: StoreField: r2->field_47 = r1
    //     0x15ee25c: stur            w1, [x2, #0x47]
    // 0x15ee260: StoreField: r2->field_6f = r0
    //     0x15ee260: stur            w0, [x2, #0x6f]
    // 0x15ee264: r1 = false
    //     0x15ee264: add             x1, NULL, #0x30  ; false
    // 0x15ee268: StoreField: r2->field_73 = r1
    //     0x15ee268: stur            w1, [x2, #0x73]
    // 0x15ee26c: StoreField: r2->field_83 = r0
    //     0x15ee26c: stur            w0, [x2, #0x83]
    // 0x15ee270: StoreField: r2->field_7b = r1
    //     0x15ee270: stur            w1, [x2, #0x7b]
    // 0x15ee274: r0 = AppBar()
    //     0x15ee274: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15ee278: mov             x1, x0
    // 0x15ee27c: ldur            x2, [fp, #-8]
    // 0x15ee280: stur            x0, [fp, #-8]
    // 0x15ee284: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x15ee284: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x15ee288: r0 = AppBar()
    //     0x15ee288: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15ee28c: ldur            x0, [fp, #-8]
    // 0x15ee290: LeaveFrame
    //     0x15ee290: mov             SP, fp
    //     0x15ee294: ldp             fp, lr, [SP], #0x10
    // 0x15ee298: ret
    //     0x15ee298: ret             
    // 0x15ee29c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15ee29c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15ee2a0: b               #0x15ee194
  }
}
